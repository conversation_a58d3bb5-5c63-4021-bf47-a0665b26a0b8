35a8325c5eb664abbe959347dea71aa2
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", { value: true });
exports.Report = void 0;
const typeorm_1 = require("typeorm");
const report_execution_entity_1 = require("./report-execution.entity");
const report_schedule_entity_1 = require("./report-schedule.entity");
/**
 * Report entity
 * Represents report templates and configurations for generating various types of reports
 */
let Report = class Report {
    /**
     * Check if report is active
     */
    get isActive() {
        return this.status === 'active';
    }
    /**
     * Check if report is a template
     */
    get isReportTemplate() {
        return this.isTemplate;
    }
    /**
     * Check if report has been executed recently
     */
    get hasRecentExecution() {
        if (!this.lastExecutedAt)
            return false;
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        return this.lastExecutedAt > oneDayAgo;
    }
    /**
     * Get estimated execution time
     */
    get estimatedExecutionTime() {
        return this.avgExecutionTime || this.metadata?.estimatedExecutionTime || 30000; // 30 seconds default
    }
    /**
     * Update execution statistics
     */
    updateExecutionStats(executionTime, sizeBytes, status) {
        this.executionCount += 1;
        this.lastExecutedAt = new Date();
        this.lastExecutionStatus = status;
        this.lastSizeBytes = sizeBytes;
        // Update average execution time
        if (this.avgExecutionTime) {
            this.avgExecutionTime = Math.round((this.avgExecutionTime * (this.executionCount - 1) + executionTime) / this.executionCount);
        }
        else {
            this.avgExecutionTime = executionTime;
        }
    }
    /**
     * Validate report configuration
     */
    validateConfiguration() {
        const errors = [];
        // Check required fields
        if (!this.configuration.dataSources || this.configuration.dataSources.length === 0) {
            errors.push('At least one data source must be specified');
        }
        // Validate time range
        if (this.configuration.timeRange) {
            const { type, value, startDate, endDate } = this.configuration.timeRange;
            if (type === 'relative' && !value) {
                errors.push('Relative time range value is required');
            }
            if (type === 'absolute' && (!startDate || !endDate)) {
                errors.push('Absolute time range requires both start and end dates');
            }
        }
        // Validate visualizations
        if (this.configuration.visualizations) {
            this.configuration.visualizations.forEach((viz, index) => {
                if (!viz.type || !viz.title || !viz.dataSource) {
                    errors.push(`Visualization ${index + 1} is missing required fields`);
                }
            });
        }
        return {
            isValid: errors.length === 0,
            errors,
        };
    }
    /**
     * Clone report as new template or instance
     */
    clone(name, isTemplate = false) {
        return {
            name,
            description: this.description,
            type: this.type,
            category: this.category,
            status: 'draft',
            isTemplate,
            templateId: isTemplate ? undefined : this.id,
            configuration: JSON.parse(JSON.stringify(this.configuration)),
            layout: this.layout ? JSON.parse(JSON.stringify(this.layout)) : undefined,
            accessControl: this.accessControl ? JSON.parse(JSON.stringify(this.accessControl)) : undefined,
            performanceSettings: this.performanceSettings ? JSON.parse(JSON.stringify(this.performanceSettings)) : undefined,
            metadata: {
                ...this.metadata,
                version: '1.0.0',
                author: undefined, // Will be set by creator
            },
        };
    }
    /**
     * Get data sources used by this report
     */
    getDataSources() {
        return this.configuration.dataSources || [];
    }
    /**
     * Check if report uses specific data source
     */
    usesDataSource(dataSource) {
        return this.getDataSources().includes(dataSource);
    }
    /**
     * Get export formats supported by this report
     */
    getSupportedExportFormats() {
        return this.configuration.exportFormats || ['pdf', 'excel', 'csv'];
    }
    /**
     * Update report version
     */
    updateVersion(changes) {
        if (!this.metadata) {
            this.metadata = {};
        }
        const currentVersion = this.metadata.version || '1.0.0';
        const versionParts = currentVersion.split('.').map(Number);
        versionParts[2] += 1; // Increment patch version
        const newVersion = versionParts.join('.');
        this.metadata.version = newVersion;
        if (!this.metadata.changelog) {
            this.metadata.changelog = [];
        }
        this.metadata.changelog.push({
            version: newVersion,
            date: new Date().toISOString(),
            changes,
        });
    }
};
exports.Report = Report;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Report.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], Report.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Report.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: [
            'vulnerability_summary',
            'risk_assessment',
            'compliance_report',
            'executive_dashboard',
            'threat_intelligence',
            'asset_inventory',
            'scan_results',
            'remediation_tracking',
            'trend_analysis',
            'performance_metrics',
            'audit_report',
            'custom_query',
        ],
    }),
    __metadata("design:type", String)
], Report.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['security', 'compliance', 'operational', 'executive', 'technical'],
        default: 'security',
    }),
    __metadata("design:type", String)
], Report.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['draft', 'active', 'archived', 'deprecated'],
        default: 'draft',
    }),
    __metadata("design:type", String)
], Report.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_template', default: false }),
    __metadata("design:type", Boolean)
], Report.prototype, "isTemplate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'template_id', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], Report.prototype, "templateId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    __metadata("design:type", Object)
], Report.prototype, "configuration", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Report.prototype, "layout", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'access_control', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Report.prototype, "accessControl", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'performance_settings', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Report.prototype, "performanceSettings", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Report.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_executed_at', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], Report.prototype, "lastExecutedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_execution_status', nullable: true }),
    __metadata("design:type", String)
], Report.prototype, "lastExecutionStatus", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'execution_count', type: 'integer', default: 0 }),
    __metadata("design:type", Number)
], Report.prototype, "executionCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'avg_execution_time', type: 'integer', nullable: true }),
    __metadata("design:type", Number)
], Report.prototype, "avgExecutionTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_size_bytes', type: 'bigint', nullable: true }),
    __metadata("design:type", Number)
], Report.prototype, "lastSizeBytes", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', type: 'uuid' }),
    __metadata("design:type", String)
], Report.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], Report.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], Report.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], Report.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => report_execution_entity_1.ReportExecution, execution => execution.report),
    __metadata("design:type", Array)
], Report.prototype, "executions", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => report_schedule_entity_1.ReportSchedule, schedule => schedule.report),
    __metadata("design:type", Array)
], Report.prototype, "schedules", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => Report, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'template_id' }),
    __metadata("design:type", Report)
], Report.prototype, "template", void 0);
exports.Report = Report = __decorate([
    (0, typeorm_1.Entity)('reports'),
    (0, typeorm_1.Index)(['type']),
    (0, typeorm_1.Index)(['category']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['createdBy']),
    (0, typeorm_1.Index)(['isTemplate'])
], Report);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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