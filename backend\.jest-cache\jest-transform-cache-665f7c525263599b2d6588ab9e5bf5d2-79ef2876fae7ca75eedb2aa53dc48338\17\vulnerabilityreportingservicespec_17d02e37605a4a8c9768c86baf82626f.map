{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\__tests__\\vulnerability-reporting.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,6CAAqD;AAErD,wFAAmF;AACnF,wFAA8E;AAC9E,8GAAmG;AACnG,kGAAuF;AACvF,yFAAqF;AAErF,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;IAC7C,IAAI,OAAsC,CAAC;IAC3C,IAAI,uBAA+D,CAAC;IACpE,IAAI,oBAAsE,CAAC;IAC3E,IAAI,cAA0D,CAAC;IAC/D,IAAI,aAAyC,CAAC;IAE9C,MAAM,iBAAiB,GAAG;QACxB,EAAE,EAAE,UAAU;QACd,UAAU,EAAE,eAAe;QAC3B,KAAK,EAAE,oBAAoB;QAC3B,QAAQ,EAAE,MAAM;QAChB,SAAS,EAAE,GAAG;QACd,aAAa,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QACrC,WAAW,EAAE,IAAI;QACjB,UAAU,EAAE,KAAK;QACjB,cAAc,EAAE,IAAI;KACrB,CAAC;IAEF,MAAM,cAAc,GAAG;QACrB,EAAE,EAAE,gBAAgB;QACpB,eAAe,EAAE,UAAU;QAC3B,MAAM,EAAE,WAAW;QACnB,gBAAgB,EAAE,MAAM;QACxB,iBAAiB,EAAE,GAAG;QACtB,UAAU,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QAClC,UAAU,EAAE,UAAU;KACvB,CAAC;IAEF,MAAM,QAAQ,GAAG;QACf,EAAE,EAAE,UAAU;QACd,IAAI,EAAE,WAAW;QACjB,QAAQ,EAAE,SAAS;QACnB,MAAM,EAAE,WAAW;QACnB,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QACjC,WAAW,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;QAC7C,OAAO,EAAE;YACP,oBAAoB,EAAE,EAAE;YACxB,aAAa,EAAE,CAAC;YAChB,SAAS,EAAE,CAAC;YACZ,WAAW,EAAE,CAAC;YACd,QAAQ,EAAE,CAAC;SACZ;KACF,CAAC;IAEF,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,cAAc,GAAG;YACrB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;YACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;YAClB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;YAChB,kBAAkB,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;gBACjC,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBAClC,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACrC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACpC,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBAC7C,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACjC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACpC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACnC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACnC,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACtC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBAChC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBAChC,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;gBACrB,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;gBACpB,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;gBAClB,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE;aAC3B,CAAC,CAAC;SACJ,CAAC;QAEF,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,+DAA6B;gBAC7B;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,oCAAa,CAAC;oBAC1C,QAAQ,EAAE,cAAc;iBACzB;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,yDAAuB,CAAC;oBACpD,QAAQ,EAAE,cAAc;iBACzB;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,6CAAiB,CAAC;oBAC9C,QAAQ,EAAE,cAAc;iBACzB;gBACD;oBACE,OAAO,EAAE,8BAAa;oBACtB,QAAQ,EAAE;wBACR,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;wBAChB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;wBACd,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;wBACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;qBACjB;iBACF;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAAgC,+DAA6B,CAAC,CAAC;QACnF,uBAAuB,GAAG,MAAM,CAAC,GAAG,CAAC,IAAA,4BAAkB,EAAC,oCAAa,CAAC,CAAC,CAAC;QACxE,oBAAoB,GAAG,MAAM,CAAC,GAAG,CAAC,IAAA,4BAAkB,EAAC,yDAAuB,CAAC,CAAC,CAAC;QAC/E,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC,IAAA,4BAAkB,EAAC,6CAAiB,CAAC,CAAC,CAAC;QACnE,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,8BAAa,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mBAAmB,EAAE,GAAG,EAAE;QAC3B,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,kBAAkB,GAAG;gBACzB,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,EAAE;gBACpC,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE;gBACjC,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE;gBACnC,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE;aACjC,CAAC;YAEF,MAAM,aAAa,GAAG;gBACpB,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE;gBACnC,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE;aACpC,CAAC;YAEF,uBAAuB,CAAC,kBAAkB,EAAE,CAAC,UAAU;iBACpD,qBAAqB,CAAC,kBAAkB,CAAC;iBACzC,qBAAqB,CAAC,aAAa,CAAC,CAAC;YAExC,uBAAuB,CAAC,KAAK;iBAC1B,qBAAqB,CAAC,CAAC,CAAC,CAAC,cAAc;iBACvC,qBAAqB,CAAC,CAAC,CAAC,CAAC,gBAAgB;iBACzC,qBAAqB,CAAC,EAAE,CAAC,CAAC,UAAU;iBACpC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YAEtC,oBAAoB,CAAC,KAAK;iBACvB,qBAAqB,CAAC,EAAE,CAAC,CAAC,wBAAwB;iBAClD,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,sBAAsB;YAEnD,cAAc,CAAC,KAAK,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc;YAE9D,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,wBAAwB,EAAE,CAAC;YAExD,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YAE7C,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,SAAS,GAAG;gBAChB,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACjC,OAAO,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;aAChC,CAAC;YAEF,uBAAuB,CAAC,kBAAkB,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YAC9E,uBAAuB,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YACnD,oBAAoB,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAChD,cAAc,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAE1C,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC;YAEjE,MAAM,YAAY,GAAG,uBAAuB,CAAC,kBAAkB,EAAE,CAAC;YAClE,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAChD,kCAAkC,EAClC,EAAE,SAAS,EAAE,SAAS,CAAC,SAAS,EAAE,CACnC,CAAC;YACF,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAChD,gCAAgC,EAChC,EAAE,OAAO,EAAE,SAAS,CAAC,OAAO,EAAE,CAC/B,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,MAAM,aAAa,GAAG;gBACpB,kBAAkB,EAAE,IAAI;gBACxB,YAAY,EAAE,IAAI;gBAClB,cAAc,EAAE,CAAC,UAAU,EAAE,MAAM,CAAC;gBACpC,MAAM,EAAE,MAAe;aACxB,CAAC;YAEF,uBAAuB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC;YAEpE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC;YAEnE,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEtD,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC;gBACxD,KAAK,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAC7B,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC7B,CAAC;gBACF,SAAS,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC,aAAa,CAAC,CAAC;gBAClD,KAAK,EAAE,EAAE,aAAa,EAAE,MAAM,EAAE;aACjC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,aAAa,GAAG;gBACpB,cAAc,EAAE,CAAC,UAAU,CAAC;gBAC5B,eAAe,EAAE,IAAI;gBACrB,WAAW,EAAE,KAAK;gBAClB,SAAS,EAAE;oBACT,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;oBACjC,OAAO,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;iBAChC;aACF,CAAC;YAEF,uBAAuB,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YAEnD,MAAM,OAAO,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC;YAEpD,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC;gBACxD,KAAK,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAC7B,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC5B,WAAW,EAAE,IAAI;oBACjB,cAAc,EAAE,KAAK;oBACrB,aAAa,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAClC,CAAC;gBACF,SAAS,EAAE,EAAE;gBACb,KAAK,EAAE,EAAE,aAAa,EAAE,MAAM,EAAE;aACjC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;YACpE,MAAM,iBAAiB,GAAG;gBACxB,SAAS,EAAE,MAAe;gBAC1B,eAAe,EAAE,IAAI;gBACrB,kBAAkB,EAAE,IAAI;aACzB,CAAC;YAEF,uBAAuB,CAAC,IAAI,CAAC,iBAAiB,CAAC;gBAC7C;oBACE,GAAG,iBAAiB;oBACpB,oBAAoB,EAAE;wBACpB,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;qBACvB;iBACF;aACF,CAAC,CAAC;YAEH,oBAAoB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC;YAE9D,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,CAAC;YAEzE,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,iBAAiB,GAAG;gBACxB,SAAS,EAAE,UAAmB;aAC/B,CAAC;YAEF,uBAAuB,CAAC,IAAI,CAAC,iBAAiB,CAAC;gBAC7C;oBACE,GAAG,iBAAiB;oBACpB,oBAAoB,EAAE;wBACpB,QAAQ,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;qBACnC;iBACF;aACF,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,CAAC;YAEzE,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,YAAY,GAAG;gBACnB,MAAM,EAAE,SAAkB;gBAC1B,OAAO,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,aAAa,CAAC;gBAC7C,SAAS,EAAE;oBACT,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;oBACjC,OAAO,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;iBAChC;aACF,CAAC;YAEF,MAAM,aAAa,GAAG;gBACpB,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE;gBAC7D,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE;aAC9D,CAAC;YAEF,uBAAuB,CAAC,kBAAkB,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAEzF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;YAEjE,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,YAAY,GAAG;gBACnB,MAAM,EAAE,QAAiB;gBACzB,OAAO,EAAE,CAAC,OAAO,CAAC;aACnB,CAAC;YAEF,MAAM,aAAa,GAAG;gBACpB,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE;gBACnC,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE;gBACnC,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE;aACpC,CAAC;YAEF,uBAAuB,CAAC,kBAAkB,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAEzF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;YAEjE,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,WAAW,GAAG;gBAClB,kBAAkB,EAAE,IAAI;gBACxB,sBAAsB,EAAE,IAAI;gBAC5B,SAAS,EAAE,CAAC,SAAS,EAAE,iBAAiB,CAAC;aAC1C,CAAC;YAEF,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;YAElD,MAAM,aAAa,GAAG;gBACpB,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,EAAE,WAAW,EAAE,KAAK,EAAE;gBACvD,EAAE,QAAQ,EAAE,iBAAiB,EAAE,KAAK,EAAE,GAAG,EAAE,WAAW,EAAE,KAAK,EAAE;aAChE,CAAC;YAEF,cAAc,CAAC,kBAAkB,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAEhF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;YAE7D,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,UAAU,GAAG;gBACjB,KAAK,EAAE,aAAa;gBACpB,IAAI,EAAE,CAAC,iBAAiB,CAAC;gBACzB,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YAE9D,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACnC,MAAM,CAAC,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,UAAU,GAAG;gBACjB,KAAK,EAAE,aAAa;gBACpB,IAAI,EAAE,CAAC,iBAAiB,CAAC;gBACzB,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,YAAY,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YAE7D,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;YAC9D,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,UAAU,GAAG;gBACjB,KAAK,EAAE,aAAa;gBACpB,IAAI,EAAE,EAAE;gBACR,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC;YAEF,MAAM,MAAM,CACV,OAAO,CAAC,YAAY,CAAC,UAAU,EAAE,aAAoB,CAAC,CACvD,CAAC,OAAO,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,eAAe,GAAG;gBACtB,UAAU,EAAE,mBAA4B;gBACxC,QAAQ,EAAE,WAAW,EAAE,qBAAqB;gBAC5C,UAAU,EAAE,CAAC,mBAAmB,CAAC;gBACjC,MAAM,EAAE,KAAc;gBACtB,OAAO,EAAE;oBACP,kBAAkB,EAAE,IAAI;iBACzB;aACF,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;YAE7D,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\__tests__\\vulnerability-reporting.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { getRepositoryToken } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\nimport { VulnerabilityReportingService } from '../vulnerability-reporting.service';\r\nimport { Vulnerability } from '../../../domain/entities/vulnerability.entity';\r\nimport { VulnerabilityAssessment } from '../../../domain/entities/vulnerability-assessment.entity';\r\nimport { VulnerabilityScan } from '../../../domain/entities/vulnerability-scan.entity';\r\nimport { LoggerService } from '../../../../../infrastructure/logging/logger.service';\r\n\r\ndescribe('VulnerabilityReportingService', () => {\r\n  let service: VulnerabilityReportingService;\r\n  let vulnerabilityRepository: jest.Mocked<Repository<Vulnerability>>;\r\n  let assessmentRepository: jest.Mocked<Repository<VulnerabilityAssessment>>;\r\n  let scanRepository: jest.Mocked<Repository<VulnerabilityScan>>;\r\n  let loggerService: jest.Mocked<LoggerService>;\r\n\r\n  const mockVulnerability = {\r\n    id: 'vuln-123',\r\n    identifier: 'CVE-2023-1234',\r\n    title: 'Test Vulnerability',\r\n    severity: 'high',\r\n    cvssScore: 7.5,\r\n    publishedDate: new Date('2023-01-01'),\r\n    exploitable: true,\r\n    hasExploit: false,\r\n    patchAvailable: true,\r\n  };\r\n\r\n  const mockAssessment = {\r\n    id: 'assessment-123',\r\n    vulnerabilityId: 'vuln-123',\r\n    status: 'completed',\r\n    assessedSeverity: 'high',\r\n    assessedCvssScore: 7.5,\r\n    assessedAt: new Date('2023-01-02'),\r\n    assessedBy: 'user-123',\r\n  };\r\n\r\n  const mockScan = {\r\n    id: 'scan-123',\r\n    name: 'Test Scan',\r\n    scanType: 'network',\r\n    status: 'completed',\r\n    startedAt: new Date('2023-01-01'),\r\n    completedAt: new Date('2023-01-01T02:00:00Z'),\r\n    summary: {\r\n      totalVulnerabilities: 10,\r\n      criticalCount: 2,\r\n      highCount: 3,\r\n      mediumCount: 3,\r\n      lowCount: 2,\r\n    },\r\n  };\r\n\r\n  beforeEach(async () => {\r\n    const mockRepository = {\r\n      find: jest.fn(),\r\n      findOne: jest.fn(),\r\n      count: jest.fn(),\r\n      createQueryBuilder: jest.fn(() => ({\r\n        select: jest.fn().mockReturnThis(),\r\n        addSelect: jest.fn().mockReturnThis(),\r\n        leftJoin: jest.fn().mockReturnThis(),\r\n        leftJoinAndSelect: jest.fn().mockReturnThis(),\r\n        where: jest.fn().mockReturnThis(),\r\n        andWhere: jest.fn().mockReturnThis(),\r\n        groupBy: jest.fn().mockReturnThis(),\r\n        orderBy: jest.fn().mockReturnThis(),\r\n        addOrderBy: jest.fn().mockReturnThis(),\r\n        skip: jest.fn().mockReturnThis(),\r\n        take: jest.fn().mockReturnThis(),\r\n        getRawMany: jest.fn(),\r\n        getRawOne: jest.fn(),\r\n        getMany: jest.fn(),\r\n        getManyAndCount: jest.fn(),\r\n      })),\r\n    };\r\n\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        VulnerabilityReportingService,\r\n        {\r\n          provide: getRepositoryToken(Vulnerability),\r\n          useValue: mockRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(VulnerabilityAssessment),\r\n          useValue: mockRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(VulnerabilityScan),\r\n          useValue: mockRepository,\r\n        },\r\n        {\r\n          provide: LoggerService,\r\n          useValue: {\r\n            debug: jest.fn(),\r\n            log: jest.fn(),\r\n            warn: jest.fn(),\r\n            error: jest.fn(),\r\n          },\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    service = module.get<VulnerabilityReportingService>(VulnerabilityReportingService);\r\n    vulnerabilityRepository = module.get(getRepositoryToken(Vulnerability));\r\n    assessmentRepository = module.get(getRepositoryToken(VulnerabilityAssessment));\r\n    scanRepository = module.get(getRepositoryToken(VulnerabilityScan));\r\n    loggerService = module.get(LoggerService);\r\n  });\r\n\r\n  it('should be defined', () => {\r\n    expect(service).toBeDefined();\r\n  });\r\n\r\n  describe('generateExecutiveSummary', () => {\r\n    it('should generate executive summary report', async () => {\r\n      const mockSeverityCounts = [\r\n        { severity: 'critical', count: '5' },\r\n        { severity: 'high', count: '10' },\r\n        { severity: 'medium', count: '15' },\r\n        { severity: 'low', count: '20' },\r\n      ];\r\n\r\n      const mockTrendData = [\r\n        { date: '2023-01-01', count: '45' },\r\n        { date: '2023-01-02', count: '50' },\r\n      ];\r\n\r\n      vulnerabilityRepository.createQueryBuilder().getRawMany\r\n        .mockResolvedValueOnce(mockSeverityCounts)\r\n        .mockResolvedValueOnce(mockTrendData);\r\n\r\n      vulnerabilityRepository.count\r\n        .mockResolvedValueOnce(3) // exploitable\r\n        .mockResolvedValueOnce(2) // with exploits\r\n        .mockResolvedValueOnce(25) // patched\r\n        .mockResolvedValueOnce(8); // recent\r\n\r\n      assessmentRepository.count\r\n        .mockResolvedValueOnce(40) // completed assessments\r\n        .mockResolvedValueOnce(5); // pending assessments\r\n\r\n      scanRepository.count.mockResolvedValueOnce(12); // total scans\r\n\r\n      const result = await service.generateExecutiveSummary();\r\n\r\n      expect(result).toHaveProperty('summary');\r\n      expect(result).toHaveProperty('breakdown');\r\n      expect(result).toHaveProperty('trends');\r\n      expect(result).toHaveProperty('assessmentMetrics');\r\n      expect(result).toHaveProperty('scanMetrics');\r\n      expect(result).toHaveProperty('generatedAt');\r\n\r\n      expect(result.summary.total).toBe(50);\r\n      expect(result.summary.critical).toBe(5);\r\n      expect(result.summary.exploitable).toBe(3);\r\n      expect(result.assessmentMetrics.completed).toBe(40);\r\n    });\r\n\r\n    it('should handle date range filters', async () => {\r\n      const dateRange = {\r\n        startDate: new Date('2023-01-01'),\r\n        endDate: new Date('2023-01-31'),\r\n      };\r\n\r\n      vulnerabilityRepository.createQueryBuilder().getRawMany.mockResolvedValue([]);\r\n      vulnerabilityRepository.count.mockResolvedValue(0);\r\n      assessmentRepository.count.mockResolvedValue(0);\r\n      scanRepository.count.mockResolvedValue(0);\r\n\r\n      const result = await service.generateExecutiveSummary(dateRange);\r\n\r\n      const queryBuilder = vulnerabilityRepository.createQueryBuilder();\r\n      expect(queryBuilder.andWhere).toHaveBeenCalledWith(\r\n        'vuln.publishedDate >= :startDate',\r\n        { startDate: dateRange.startDate }\r\n      );\r\n      expect(queryBuilder.andWhere).toHaveBeenCalledWith(\r\n        'vuln.publishedDate <= :endDate',\r\n        { endDate: dateRange.endDate }\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('generateDetailedReport', () => {\r\n    it('should generate detailed vulnerability report', async () => {\r\n      const reportOptions = {\r\n        includeAssessments: true,\r\n        includeScans: true,\r\n        severityFilter: ['critical', 'high'],\r\n        format: 'json' as const,\r\n      };\r\n\r\n      vulnerabilityRepository.find.mockResolvedValue([mockVulnerability]);\r\n\r\n      const result = await service.generateDetailedReport(reportOptions);\r\n\r\n      expect(result).toHaveProperty('vulnerabilities');\r\n      expect(result).toHaveProperty('metadata');\r\n      expect(result.vulnerabilities).toHaveLength(1);\r\n      expect(result.metadata.format).toBe('json');\r\n      expect(result.metadata.includeAssessments).toBe(true);\r\n\r\n      expect(vulnerabilityRepository.find).toHaveBeenCalledWith({\r\n        where: expect.objectContaining({\r\n          severity: expect.any(Object),\r\n        }),\r\n        relations: expect.arrayContaining(['assessments']),\r\n        order: { publishedDate: 'DESC' },\r\n      });\r\n    });\r\n\r\n    it('should apply filters correctly', async () => {\r\n      const reportOptions = {\r\n        severityFilter: ['critical'],\r\n        exploitableOnly: true,\r\n        patchedOnly: false,\r\n        dateRange: {\r\n          startDate: new Date('2023-01-01'),\r\n          endDate: new Date('2023-01-31'),\r\n        },\r\n      };\r\n\r\n      vulnerabilityRepository.find.mockResolvedValue([]);\r\n\r\n      await service.generateDetailedReport(reportOptions);\r\n\r\n      expect(vulnerabilityRepository.find).toHaveBeenCalledWith({\r\n        where: expect.objectContaining({\r\n          severity: expect.any(Object),\r\n          exploitable: true,\r\n          patchAvailable: false,\r\n          publishedDate: expect.any(Object),\r\n        }),\r\n        relations: [],\r\n        order: { publishedDate: 'DESC' },\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('generateComplianceReport', () => {\r\n    it('should generate compliance report for NIST framework', async () => {\r\n      const complianceOptions = {\r\n        framework: 'nist' as const,\r\n        includeEvidence: true,\r\n        includeRemediation: true,\r\n      };\r\n\r\n      vulnerabilityRepository.find.mockResolvedValue([\r\n        {\r\n          ...mockVulnerability,\r\n          complianceFrameworks: {\r\n            nist: ['SC-7', 'SI-2'],\r\n          },\r\n        },\r\n      ]);\r\n\r\n      assessmentRepository.find.mockResolvedValue([mockAssessment]);\r\n\r\n      const result = await service.generateComplianceReport(complianceOptions);\r\n\r\n      expect(result).toHaveProperty('framework');\r\n      expect(result).toHaveProperty('controls');\r\n      expect(result).toHaveProperty('summary');\r\n      expect(result).toHaveProperty('vulnerabilities');\r\n      expect(result.framework).toBe('nist');\r\n      expect(result.controls).toContain('SC-7');\r\n      expect(result.controls).toContain('SI-2');\r\n    });\r\n\r\n    it('should handle multiple compliance frameworks', async () => {\r\n      const complianceOptions = {\r\n        framework: 'iso27001' as const,\r\n      };\r\n\r\n      vulnerabilityRepository.find.mockResolvedValue([\r\n        {\r\n          ...mockVulnerability,\r\n          complianceFrameworks: {\r\n            iso27001: ['A.12.6.1', 'A.14.2.3'],\r\n          },\r\n        },\r\n      ]);\r\n\r\n      const result = await service.generateComplianceReport(complianceOptions);\r\n\r\n      expect(result.framework).toBe('iso27001');\r\n      expect(result.controls).toContain('A.12.6.1');\r\n      expect(result.controls).toContain('A.14.2.3');\r\n    });\r\n  });\r\n\r\n  describe('generateTrendAnalysis', () => {\r\n    it('should generate trend analysis report', async () => {\r\n      const trendOptions = {\r\n        period: 'monthly' as const,\r\n        metrics: ['total', 'severity', 'exploitable'],\r\n        dateRange: {\r\n          startDate: new Date('2023-01-01'),\r\n          endDate: new Date('2023-12-31'),\r\n        },\r\n      };\r\n\r\n      const mockTrendData = [\r\n        { period: '2023-01', total: '45', critical: '5', high: '10' },\r\n        { period: '2023-02', total: '50', critical: '6', high: '12' },\r\n      ];\r\n\r\n      vulnerabilityRepository.createQueryBuilder().getRawMany.mockResolvedValue(mockTrendData);\r\n\r\n      const result = await service.generateTrendAnalysis(trendOptions);\r\n\r\n      expect(result).toHaveProperty('period');\r\n      expect(result).toHaveProperty('metrics');\r\n      expect(result).toHaveProperty('data');\r\n      expect(result).toHaveProperty('analysis');\r\n      expect(result.period).toBe('monthly');\r\n      expect(result.data).toHaveLength(2);\r\n    });\r\n\r\n    it('should calculate trend analysis correctly', async () => {\r\n      const trendOptions = {\r\n        period: 'weekly' as const,\r\n        metrics: ['total'],\r\n      };\r\n\r\n      const mockTrendData = [\r\n        { period: '2023-W01', total: '40' },\r\n        { period: '2023-W02', total: '45' },\r\n        { period: '2023-W03', total: '50' },\r\n      ];\r\n\r\n      vulnerabilityRepository.createQueryBuilder().getRawMany.mockResolvedValue(mockTrendData);\r\n\r\n      const result = await service.generateTrendAnalysis(trendOptions);\r\n\r\n      expect(result.analysis).toHaveProperty('trend');\r\n      expect(result.analysis).toHaveProperty('growthRate');\r\n      expect(result.analysis.trend).toBe('increasing');\r\n      expect(result.analysis.growthRate).toBeGreaterThan(0);\r\n    });\r\n  });\r\n\r\n  describe('generateScanReport', () => {\r\n    it('should generate scan performance report', async () => {\r\n      const scanOptions = {\r\n        includeScanDetails: true,\r\n        includeVulnerabilities: true,\r\n        scanTypes: ['network', 'web_application'],\r\n      };\r\n\r\n      scanRepository.find.mockResolvedValue([mockScan]);\r\n\r\n      const mockScanStats = [\r\n        { scanType: 'network', count: '5', avgDuration: '120' },\r\n        { scanType: 'web_application', count: '3', avgDuration: '180' },\r\n      ];\r\n\r\n      scanRepository.createQueryBuilder().getRawMany.mockResolvedValue(mockScanStats);\r\n\r\n      const result = await service.generateScanReport(scanOptions);\r\n\r\n      expect(result).toHaveProperty('scans');\r\n      expect(result).toHaveProperty('statistics');\r\n      expect(result).toHaveProperty('performance');\r\n      expect(result.scans).toHaveLength(1);\r\n      expect(result.statistics).toHaveLength(2);\r\n    });\r\n  });\r\n\r\n  describe('exportReport', () => {\r\n    it('should export report in JSON format', async () => {\r\n      const reportData = {\r\n        title: 'Test Report',\r\n        data: [mockVulnerability],\r\n        generatedAt: new Date(),\r\n      };\r\n\r\n      const result = await service.exportReport(reportData, 'json');\r\n\r\n      expect(result).toHaveProperty('format');\r\n      expect(result).toHaveProperty('content');\r\n      expect(result).toHaveProperty('filename');\r\n      expect(result.format).toBe('json');\r\n      expect(typeof result.content).toBe('string');\r\n      expect(result.filename).toMatch(/\\.json$/);\r\n    });\r\n\r\n    it('should export report in CSV format', async () => {\r\n      const reportData = {\r\n        title: 'Test Report',\r\n        data: [mockVulnerability],\r\n        generatedAt: new Date(),\r\n      };\r\n\r\n      const result = await service.exportReport(reportData, 'csv');\r\n\r\n      expect(result.format).toBe('csv');\r\n      expect(result.content).toContain('identifier,title,severity');\r\n      expect(result.filename).toMatch(/\\.csv$/);\r\n    });\r\n\r\n    it('should handle unsupported export formats', async () => {\r\n      const reportData = {\r\n        title: 'Test Report',\r\n        data: [],\r\n        generatedAt: new Date(),\r\n      };\r\n\r\n      await expect(\r\n        service.exportReport(reportData, 'unsupported' as any)\r\n      ).rejects.toThrow('Unsupported export format');\r\n    });\r\n  });\r\n\r\n  describe('scheduleReport', () => {\r\n    it('should schedule a recurring report', async () => {\r\n      const scheduleOptions = {\r\n        reportType: 'executive_summary' as const,\r\n        schedule: '0 8 * * 1', // Weekly Monday 8 AM\r\n        recipients: ['<EMAIL>'],\r\n        format: 'pdf' as const,\r\n        options: {\r\n          includeAssessments: true,\r\n        },\r\n      };\r\n\r\n      const result = await service.scheduleReport(scheduleOptions);\r\n\r\n      expect(result).toHaveProperty('scheduleId');\r\n      expect(result).toHaveProperty('nextRun');\r\n      expect(result).toHaveProperty('status');\r\n      expect(result.status).toBe('scheduled');\r\n    });\r\n  });\r\n});\r\n"], "version": 3}