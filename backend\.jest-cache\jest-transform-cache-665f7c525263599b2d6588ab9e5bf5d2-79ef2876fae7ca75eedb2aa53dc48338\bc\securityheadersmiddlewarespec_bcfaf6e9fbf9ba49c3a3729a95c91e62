c03941721a11bfcd7354f6ee3737a60d
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const security_headers_middleware_1 = require("../security-headers.middleware");
const csp_config_1 = require("../csp.config");
const hsts_config_1 = require("../hsts.config");
describe('SecurityHeadersMiddleware', () => {
    let middleware;
    let configService;
    let cspConfig;
    let hstsConfig;
    let mockRequest;
    let mockResponse;
    let nextFunction;
    beforeEach(async () => {
        const mockConfigService = {
            get: jest.fn(),
        };
        const mockCspConfig = {
            generateCSPHeader: jest.fn(),
        };
        const mockHstsConfig = {
            generateHSTSHeader: jest.fn(),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                security_headers_middleware_1.SecurityHeadersMiddleware,
                {
                    provide: config_1.ConfigService,
                    useValue: mockConfigService,
                },
                {
                    provide: csp_config_1.CspConfig,
                    useValue: mockCspConfig,
                },
                {
                    provide: hsts_config_1.HstsConfig,
                    useValue: mockHstsConfig,
                },
            ],
        }).compile();
        middleware = module.get(security_headers_middleware_1.SecurityHeadersMiddleware);
        configService = module.get(config_1.ConfigService);
        cspConfig = module.get(csp_config_1.CspConfig);
        hstsConfig = module.get(hsts_config_1.HstsConfig);
        // Setup mocks
        mockRequest = {
            path: '/api/v1/test',
            headers: {},
            secure: false,
        };
        mockResponse = {
            setHeader: jest.fn(),
            removeHeader: jest.fn(),
        };
        nextFunction = jest.fn();
        // Default mock implementations
        cspConfig.generateCSPHeader.mockReturnValue("default-src 'self'");
        hstsConfig.generateHSTSHeader.mockReturnValue('max-age=31536000');
        configService.get.mockImplementation((key, defaultValue) => {
            if (key === 'app.version')
                return '1.0.0';
            return defaultValue;
        });
    });
    describe('use', () => {
        it('should set all required security headers', () => {
            middleware.use(mockRequest, mockResponse, nextFunction);
            expect(mockResponse.setHeader).toHaveBeenCalledWith('Content-Security-Policy', "default-src 'self'");
            expect(mockResponse.setHeader).toHaveBeenCalledWith('X-Frame-Options', 'DENY');
            expect(mockResponse.setHeader).toHaveBeenCalledWith('X-Content-Type-Options', 'nosniff');
            expect(mockResponse.setHeader).toHaveBeenCalledWith('X-XSS-Protection', '1; mode=block');
            expect(mockResponse.setHeader).toHaveBeenCalledWith('Referrer-Policy', 'strict-origin-when-cross-origin');
            expect(mockResponse.setHeader).toHaveBeenCalledWith('X-DNS-Prefetch-Control', 'off');
            expect(mockResponse.setHeader).toHaveBeenCalledWith('X-Download-Options', 'noopen');
            expect(mockResponse.setHeader).toHaveBeenCalledWith('X-Permitted-Cross-Domain-Policies', 'none');
            expect(nextFunction).toHaveBeenCalled();
        });
        it('should set HSTS header for HTTPS requests', () => {
            mockRequest.secure = true;
            middleware.use(mockRequest, mockResponse, nextFunction);
            expect(mockResponse.setHeader).toHaveBeenCalledWith('Strict-Transport-Security', 'max-age=31536000');
        });
        it('should set HSTS header for requests with x-forwarded-proto https', () => {
            mockRequest.headers = { 'x-forwarded-proto': 'https' };
            middleware.use(mockRequest, mockResponse, nextFunction);
            expect(mockResponse.setHeader).toHaveBeenCalledWith('Strict-Transport-Security', 'max-age=31536000');
        });
        it('should not set HSTS header for HTTP requests', () => {
            mockRequest.secure = false;
            mockRequest.headers = {};
            middleware.use(mockRequest, mockResponse, nextFunction);
            expect(mockResponse.setHeader).not.toHaveBeenCalledWith('Strict-Transport-Security', expect.any(String));
        });
        it('should set cache control headers for sensitive endpoints', () => {
            mockRequest.path = '/auth/login';
            middleware.use(mockRequest, mockResponse, nextFunction);
            expect(mockResponse.setHeader).toHaveBeenCalledWith('Cache-Control', 'no-store, no-cache, must-revalidate, private');
            expect(mockResponse.setHeader).toHaveBeenCalledWith('Pragma', 'no-cache');
            expect(mockResponse.setHeader).toHaveBeenCalledWith('Expires', '0');
        });
        it('should remove server identification headers', () => {
            middleware.use(mockRequest, mockResponse, nextFunction);
            expect(mockResponse.removeHeader).toHaveBeenCalledWith('X-Powered-By');
            expect(mockResponse.removeHeader).toHaveBeenCalledWith('Server');
        });
        it('should set API version header', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'app.version')
                    return '2.1.0';
                return defaultValue;
            });
            middleware.use(mockRequest, mockResponse, nextFunction);
            expect(mockResponse.setHeader).toHaveBeenCalledWith('X-API-Version', '2.1.0');
        });
        it('should use correlation ID from request headers', () => {
            mockRequest.headers = { 'x-correlation-id': 'test-correlation-id' };
            middleware.use(mockRequest, mockResponse, nextFunction);
            expect(mockResponse.setHeader).toHaveBeenCalledWith('X-Request-ID', 'test-correlation-id');
        });
        it('should generate request ID when correlation ID is not present', () => {
            middleware.use(mockRequest, mockResponse, nextFunction);
            expect(mockResponse.setHeader).toHaveBeenCalledWith('X-Request-ID', expect.stringMatching(/^req_\d+_[a-z0-9]+$/));
        });
        it('should set comprehensive Permissions Policy', () => {
            middleware.use(mockRequest, mockResponse, nextFunction);
            const permissionsPolicyCall = mockResponse.setHeader.mock.calls
                .find(call => call[0] === 'Permissions-Policy');
            expect(permissionsPolicyCall).toBeDefined();
            expect(permissionsPolicyCall[1]).toContain('accelerometer=()');
            expect(permissionsPolicyCall[1]).toContain('camera=()');
            expect(permissionsPolicyCall[1]).toContain('microphone=()');
        });
    });
    describe('sensitive endpoint detection', () => {
        const sensitiveEndpoints = [
            '/auth/login',
            '/auth/logout',
            '/admin/users',
            '/api/v1/auth/token',
            '/api/v2/auth/refresh',
            '/health/detailed',
            '/metrics/sensitive',
        ];
        sensitiveEndpoints.forEach(endpoint => {
            it(`should detect ${endpoint} as sensitive`, () => {
                mockRequest.path = endpoint;
                middleware.use(mockRequest, mockResponse, nextFunction);
                expect(mockResponse.setHeader).toHaveBeenCalledWith('Cache-Control', 'no-store, no-cache, must-revalidate, private');
            });
        });
        const nonSensitiveEndpoints = [
            '/api/v1/health',
            '/api/v1/vulnerabilities',
            '/api/v2/threats',
            '/metrics',
            '/docs',
        ];
        nonSensitiveEndpoints.forEach(endpoint => {
            it(`should not detect ${endpoint} as sensitive`, () => {
                mockRequest.path = endpoint;
                middleware.use(mockRequest, mockResponse, nextFunction);
                expect(mockResponse.setHeader).not.toHaveBeenCalledWith('Cache-Control', 'no-store, no-cache, must-revalidate, private');
            });
        });
    });
    describe('error handling', () => {
        it('should handle CSP config errors gracefully', () => {
            cspConfig.generateCSPHeader.mockImplementation(() => {
                throw new Error('CSP configuration error');
            });
            expect(() => {
                middleware.use(mockRequest, mockResponse, nextFunction);
            }).not.toThrow();
            expect(nextFunction).toHaveBeenCalled();
        });
        it('should handle HSTS config errors gracefully', () => {
            mockRequest.secure = true;
            hstsConfig.generateHSTSHeader.mockImplementation(() => {
                throw new Error('HSTS configuration error');
            });
            expect(() => {
                middleware.use(mockRequest, mockResponse, nextFunction);
            }).not.toThrow();
            expect(nextFunction).toHaveBeenCalled();
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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