105d45c9b2779af127e2cb07842f875f
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const app_service_1 = require("./app.service");
/**
 * Root application controller
 * Provides basic application information and status endpoints
 */
let AppController = class AppController {
    constructor(appService) {
        this.appService = appService;
    }
    /**
     * Root endpoint - returns application information
     */
    getApplicationInfo() {
        return this.appService.getApplicationInfo();
    }
    /**
     * Status endpoint - returns application status
     */
    getStatus() {
        return this.appService.getStatus();
    }
    /**
     * Version endpoint - returns application version information
     */
    getVersion() {
        return this.appService.getVersion();
    }
};
exports.AppController = AppController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get application information',
        description: 'Returns basic information about the Sentinel platform'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Application information retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                name: { type: 'string', example: 'Sentinel Vulnerability Assessment Platform' },
                version: { type: 'string', example: '1.0.0' },
                description: { type: 'string' },
                environment: { type: 'string', example: 'development' },
                timestamp: { type: 'string', format: 'date-time' },
                uptime: { type: 'number', description: 'Application uptime in seconds' }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AppController.prototype, "getApplicationInfo", null);
__decorate([
    (0, common_1.Get)('status'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get application status',
        description: 'Returns current application status and basic metrics'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Application status retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                status: { type: 'string', example: 'healthy' },
                timestamp: { type: 'string', format: 'date-time' },
                uptime: { type: 'number' },
                memory: {
                    type: 'object',
                    properties: {
                        used: { type: 'number' },
                        total: { type: 'number' },
                        percentage: { type: 'number' }
                    }
                },
                version: { type: 'string' },
                environment: { type: 'string' }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AppController.prototype, "getStatus", null);
__decorate([
    (0, common_1.Get)('version'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get version information',
        description: 'Returns detailed version and build information'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Version information retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                version: { type: 'string', example: '1.0.0' },
                buildDate: { type: 'string', format: 'date-time' },
                gitCommit: { type: 'string' },
                nodeVersion: { type: 'string' },
                platform: { type: 'string' }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AppController.prototype, "getVersion", null);
exports.AppController = AppController = __decorate([
    (0, swagger_1.ApiTags)('Application'),
    (0, common_1.Controller)(),
    __metadata("design:paramtypes", [typeof (_a = typeof app_service_1.AppService !== "undefined" && app_service_1.AppService) === "function" ? _a : Object])
], AppController);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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