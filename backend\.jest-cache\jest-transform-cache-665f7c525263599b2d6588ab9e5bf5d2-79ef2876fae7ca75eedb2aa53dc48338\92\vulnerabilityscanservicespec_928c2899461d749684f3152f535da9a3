c49b870088a92a2187e297ad6b47195f
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const vulnerability_scan_service_1 = require("../vulnerability-scan.service");
const vulnerability_scan_entity_1 = require("../../../domain/entities/vulnerability-scan.entity");
const asset_entity_1 = require("../../../../asset-management/domain/entities/asset.entity");
const logger_service_1 = require("../../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../../infrastructure/logging/audit/audit.service");
const vulnerability_scanner_service_1 = require("../../../infrastructure/services/vulnerability-scanner.service");
describe('VulnerabilityScanService', () => {
    let service;
    let scanRepository;
    let assetRepository;
    let loggerService;
    let auditService;
    let scannerService;
    const mockScan = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        name: 'Test Scan',
        description: 'Test scan description',
        scanType: 'network',
        scannerType: 'nessus',
        status: 'pending',
        targets: [
            {
                type: 'network',
                value: '***********/24',
            },
        ],
        scanConfig: {
            intensity: 'normal',
            useCredentials: false,
        },
        priority: 'medium',
        initiatedBy: 'user-123',
        getSummary: jest.fn(),
        exportForReporting: jest.fn(),
        start: jest.fn(),
        complete: jest.fn(),
        fail: jest.fn(),
        cancel: jest.fn(),
        updateProgress: jest.fn(),
    };
    const mockAsset = {
        id: 'asset-123',
        name: 'Test Asset',
        type: 'server',
        ipAddress: '*************',
    };
    beforeEach(async () => {
        const mockRepository = {
            create: jest.fn(),
            save: jest.fn(),
            find: jest.fn(),
            findOne: jest.fn(),
            findOneBy: jest.fn(),
            count: jest.fn(),
            remove: jest.fn(),
            createQueryBuilder: jest.fn(() => ({
                leftJoinAndSelect: jest.fn().mockReturnThis(),
                where: jest.fn().mockReturnThis(),
                andWhere: jest.fn().mockReturnThis(),
                orderBy: jest.fn().mockReturnThis(),
                addOrderBy: jest.fn().mockReturnThis(),
                skip: jest.fn().mockReturnThis(),
                take: jest.fn().mockReturnThis(),
                getManyAndCount: jest.fn(),
                getMany: jest.fn(),
            })),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                vulnerability_scan_service_1.VulnerabilityScanService,
                {
                    provide: (0, typeorm_1.getRepositoryToken)(vulnerability_scan_entity_1.VulnerabilityScan),
                    useValue: mockRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(asset_entity_1.Asset),
                    useValue: mockRepository,
                },
                {
                    provide: logger_service_1.LoggerService,
                    useValue: {
                        debug: jest.fn(),
                        log: jest.fn(),
                        warn: jest.fn(),
                        error: jest.fn(),
                    },
                },
                {
                    provide: audit_service_1.AuditService,
                    useValue: {
                        logUserAction: jest.fn(),
                    },
                },
                {
                    provide: vulnerability_scanner_service_1.VulnerabilityScannerService,
                    useValue: {
                        startNessusScan: jest.fn(),
                        startOpenVASScan: jest.fn(),
                        startZAPScan: jest.fn(),
                        getNessusScanStatus: jest.fn(),
                        getNessusScanResults: jest.fn(),
                        cancelScan: jest.fn(),
                    },
                },
            ],
        }).compile();
        service = module.get(vulnerability_scan_service_1.VulnerabilityScanService);
        scanRepository = module.get((0, typeorm_1.getRepositoryToken)(vulnerability_scan_entity_1.VulnerabilityScan));
        assetRepository = module.get((0, typeorm_1.getRepositoryToken)(asset_entity_1.Asset));
        loggerService = module.get(logger_service_1.LoggerService);
        auditService = module.get(audit_service_1.AuditService);
        scannerService = module.get(vulnerability_scanner_service_1.VulnerabilityScannerService);
    });
    it('should be defined', () => {
        expect(service).toBeDefined();
    });
    describe('createScan', () => {
        it('should create a new scan', async () => {
            const scanData = {
                name: 'New Scan',
                description: 'New scan description',
                scanType: 'network',
                scannerType: 'nessus',
                targets: [
                    {
                        type: 'network',
                        value: '***********/24',
                    },
                ],
                scanConfig: {
                    intensity: 'normal',
                    useCredentials: false,
                },
            };
            scanRepository.create.mockReturnValue(mockScan);
            scanRepository.save.mockResolvedValue(mockScan);
            const result = await service.createScan(scanData, 'user-123');
            expect(scanRepository.create).toHaveBeenCalledWith(expect.objectContaining({
                ...scanData,
                status: 'pending',
                initiatedBy: 'user-123',
            }));
            expect(scanRepository.save).toHaveBeenCalledWith(mockScan);
            expect(auditService.logUserAction).toHaveBeenCalledWith('user-123', 'create', 'vulnerability_scan', mockScan.id, expect.any(Object));
        });
        it('should resolve asset IDs when provided', async () => {
            const scanData = {
                name: 'Asset Scan',
                scanType: 'network',
                scannerType: 'nessus',
                targets: [],
                scanConfig: {},
                assetIds: ['asset-123'],
            };
            assetRepository.find.mockResolvedValue([mockAsset]);
            scanRepository.create.mockReturnValue(mockScan);
            scanRepository.save.mockResolvedValue(mockScan);
            await service.createScan(scanData, 'user-123');
            expect(assetRepository.find).toHaveBeenCalledWith({
                where: { id: expect.any(Object) },
            });
        });
    });
    describe('getScanDetails', () => {
        it('should return scan details', async () => {
            scanRepository.findOne.mockResolvedValue(mockScan);
            const result = await service.getScanDetails('123e4567-e89b-12d3-a456-426614174000');
            expect(result).toEqual(mockScan);
            expect(scanRepository.findOne).toHaveBeenCalledWith({
                where: { id: '123e4567-e89b-12d3-a456-426614174000' },
                relations: ['scannedAssets'],
            });
        });
        it('should throw NotFoundException when scan not found', async () => {
            scanRepository.findOne.mockResolvedValue(null);
            await expect(service.getScanDetails('non-existent-id')).rejects.toThrow('Scan not found');
        });
    });
    describe('searchScans', () => {
        it('should search scans with filters', async () => {
            const mockQueryBuilder = scanRepository.createQueryBuilder();
            mockQueryBuilder.getManyAndCount.mockResolvedValue([[mockScan], 1]);
            const searchCriteria = {
                page: 1,
                limit: 10,
                scanTypes: ['network'],
                statuses: ['pending', 'running'],
                scannerTypes: ['nessus'],
            };
            const result = await service.searchScans(searchCriteria);
            expect(result).toHaveProperty('scans');
            expect(result).toHaveProperty('total');
            expect(result).toHaveProperty('page');
            expect(result).toHaveProperty('totalPages');
            expect(result.scans).toHaveLength(1);
            expect(result.total).toBe(1);
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('scan.scanType IN (:...scanTypes)', { scanTypes: ['network'] });
        });
        it('should handle date range filters', async () => {
            const mockQueryBuilder = scanRepository.createQueryBuilder();
            mockQueryBuilder.getManyAndCount.mockResolvedValue([[], 0]);
            const searchCriteria = {
                page: 1,
                limit: 10,
                createdAfter: new Date('2023-01-01'),
                createdBefore: new Date('2023-12-31'),
            };
            await service.searchScans(searchCriteria);
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('scan.createdAt >= :createdAfter', { createdAfter: searchCriteria.createdAfter });
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('scan.createdAt <= :createdBefore', { createdBefore: searchCriteria.createdBefore });
        });
    });
    describe('startScan', () => {
        it('should start a Nessus scan', async () => {
            const scannerResult = {
                scannerType: 'nessus',
                externalScanId: 'nessus-123',
                status: 'running',
                startedAt: new Date(),
            };
            scanRepository.findOne.mockResolvedValue(mockScan);
            scannerService.startNessusScan.mockResolvedValue(scannerResult);
            scanRepository.save.mockResolvedValue({
                ...mockScan,
                status: 'running',
                externalScanId: 'nessus-123',
            });
            const result = await service.startScan('123e4567-e89b-12d3-a456-426614174000', 'user-123');
            expect(scannerService.startNessusScan).toHaveBeenCalledWith(expect.objectContaining({
                name: mockScan.name,
                targets: mockScan.targets,
            }));
            expect(mockScan.start).toHaveBeenCalledWith('nessus-123');
            expect(auditService.logUserAction).toHaveBeenCalledWith('user-123', 'start', 'vulnerability_scan', mockScan.id, expect.any(Object));
        });
        it('should throw error when scan not found', async () => {
            scanRepository.findOne.mockResolvedValue(null);
            await expect(service.startScan('non-existent-id', 'user-123')).rejects.toThrow('Scan not found');
        });
        it('should throw error when scan cannot be started', async () => {
            const runningMockScan = { ...mockScan, status: 'running' };
            scanRepository.findOne.mockResolvedValue(runningMockScan);
            await expect(service.startScan('123e4567-e89b-12d3-a456-426614174000', 'user-123')).rejects.toThrow('Scan cannot be started');
        });
    });
    describe('cancelScan', () => {
        it('should cancel a running scan', async () => {
            const runningMockScan = {
                ...mockScan,
                status: 'running',
                externalScanId: 'nessus-123',
            };
            scanRepository.findOne.mockResolvedValue(runningMockScan);
            scannerService.cancelScan.mockResolvedValue(undefined);
            scanRepository.save.mockResolvedValue({
                ...runningMockScan,
                status: 'cancelled',
            });
            const result = await service.cancelScan('123e4567-e89b-12d3-a456-426614174000', 'User requested cancellation', 'user-123');
            expect(scannerService.cancelScan).toHaveBeenCalledWith('nessus', 'nessus-123');
            expect(runningMockScan.cancel).toHaveBeenCalledWith('User requested cancellation');
            expect(auditService.logUserAction).toHaveBeenCalledWith('user-123', 'cancel', 'vulnerability_scan', runningMockScan.id, expect.any(Object));
        });
        it('should throw error when scan cannot be cancelled', async () => {
            const completedMockScan = { ...mockScan, status: 'completed' };
            scanRepository.findOne.mockResolvedValue(completedMockScan);
            await expect(service.cancelScan('123e4567-e89b-12d3-a456-426614174000', 'reason', 'user-123')).rejects.toThrow('Scan cannot be cancelled');
        });
    });
    describe('updateScanProgress', () => {
        it('should update scan progress', async () => {
            const runningMockScan = { ...mockScan, status: 'running' };
            const progressData = {
                overallProgress: 50,
                currentPhase: 'port_scan',
                vulnerabilitiesFound: 5,
            };
            scanRepository.findOne.mockResolvedValue(runningMockScan);
            scanRepository.save.mockResolvedValue(runningMockScan);
            const result = await service.updateScanProgress('123e4567-e89b-12d3-a456-426614174000', progressData);
            expect(runningMockScan.updateProgress).toHaveBeenCalledWith(progressData);
            expect(scanRepository.save).toHaveBeenCalledWith(runningMockScan);
        });
        it('should throw error when scan not found', async () => {
            scanRepository.findOne.mockResolvedValue(null);
            await expect(service.updateScanProgress('non-existent-id', {})).rejects.toThrow('Scan not found');
        });
    });
    describe('completeScan', () => {
        it('should complete a scan with results', async () => {
            const runningMockScan = { ...mockScan, status: 'running' };
            const results = {
                summary: {
                    totalVulnerabilities: 10,
                    criticalCount: 2,
                    highCount: 3,
                    mediumCount: 3,
                    lowCount: 2,
                },
                vulnerabilities: [],
            };
            scanRepository.findOne.mockResolvedValue(runningMockScan);
            scanRepository.save.mockResolvedValue({
                ...runningMockScan,
                status: 'completed',
            });
            const result = await service.completeScan('123e4567-e89b-12d3-a456-426614174000', results, 'user-123');
            expect(runningMockScan.complete).toHaveBeenCalledWith(results);
            expect(auditService.logUserAction).toHaveBeenCalledWith('user-123', 'complete', 'vulnerability_scan', runningMockScan.id, expect.any(Object));
        });
    });
    describe('deleteScan', () => {
        it('should delete a scan that is not running', async () => {
            const completedMockScan = { ...mockScan, status: 'completed' };
            scanRepository.findOne.mockResolvedValue(completedMockScan);
            scanRepository.remove.mockResolvedValue(completedMockScan);
            await service.deleteScan('123e4567-e89b-12d3-a456-426614174000', 'user-123');
            expect(scanRepository.remove).toHaveBeenCalledWith(completedMockScan);
            expect(auditService.logUserAction).toHaveBeenCalledWith('user-123', 'delete', 'vulnerability_scan', completedMockScan.id, expect.any(Object));
        });
        it('should throw error when trying to delete a running scan', async () => {
            const runningMockScan = { ...mockScan, status: 'running' };
            scanRepository.findOne.mockResolvedValue(runningMockScan);
            await expect(service.deleteScan('123e4567-e89b-12d3-a456-426614174000', 'user-123')).rejects.toThrow('Cannot delete running scan');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************