cb8e8775af44eb2742995205036cefef
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const event_emitter_1 = require("@nestjs/event-emitter");
const bull_1 = require("@nestjs/bull");
const typeorm_2 = require("@nestjs/typeorm");
const notification_workflow_service_1 = require("../../services/notification-workflow.service");
const notification_analytics_service_1 = require("../../services/notification-analytics.service");
const notification_queue_management_service_1 = require("../../services/notification-queue-management.service");
const notification_template_management_service_1 = require("../../services/notification-template-management.service");
const notification_workflow_entity_1 = require("../../entities/notification-workflow.entity");
const workflow_execution_entity_1 = require("../../entities/workflow-execution.entity");
const notification_analytics_event_entity_1 = require("../../entities/notification-analytics-event.entity");
const notification_template_entity_1 = require("../../entities/notification-template.entity");
/**
 * Cross-Module Integration Tests
 *
 * Comprehensive integration tests validating data flow and interactions between modules:
 * - Asset Management ↔ Threat Intelligence data flow validation
 * - Reporting Module ↔ User Management authentication integration
 * - Notification workflows ↔ Asset discovery event processing
 * - Analytics data consistency across module boundaries
 * - Event-driven architecture validation with real-time processing
 * - Performance testing for cross-module data synchronization
 */
describe('Cross-Module Integration Tests', () => {
    let app;
    let workflowService;
    let analyticsService;
    let queueService;
    let templateService;
    let workflowRepository;
    let executionRepository;
    let analyticsRepository;
    let templateRepository;
    const testUser = {
        id: 'test-user-123',
        email: '<EMAIL>',
        role: 'admin',
        team: 'security',
        permissions: ['asset.read', 'threat.read', 'notification.send'],
    };
    beforeAll(async () => {
        const moduleFixture = await testing_1.Test.createTestingModule({
            imports: [
                config_1.ConfigModule.forRoot({
                    isGlobal: true,
                    envFilePath: '.env.test',
                }),
                typeorm_1.TypeOrmModule.forRoot({
                    type: 'postgres',
                    host: process.env.TEST_DB_HOST || 'localhost',
                    port: parseInt(process.env.TEST_DB_PORT) || 5433,
                    username: process.env.TEST_DB_USERNAME || 'test',
                    password: process.env.TEST_DB_PASSWORD || 'test',
                    database: process.env.TEST_DB_NAME || 'sentinel_test',
                    entities: [
                        notification_workflow_entity_1.NotificationWorkflow,
                        workflow_execution_entity_1.WorkflowExecution,
                        notification_analytics_event_entity_1.NotificationAnalyticsEvent,
                        notification_template_entity_1.NotificationTemplate,
                    ],
                    synchronize: true,
                    dropSchema: true,
                }),
                typeorm_1.TypeOrmModule.forFeature([
                    notification_workflow_entity_1.NotificationWorkflow,
                    workflow_execution_entity_1.WorkflowExecution,
                    notification_analytics_event_entity_1.NotificationAnalyticsEvent,
                    notification_template_entity_1.NotificationTemplate,
                ]),
                event_emitter_1.EventEmitterModule.forRoot(),
                bull_1.BullModule.forRoot({
                    redis: {
                        host: process.env.TEST_REDIS_HOST || 'localhost',
                        port: parseInt(process.env.TEST_REDIS_PORT) || 6380,
                        db: 1,
                    },
                }),
                bull_1.BullModule.registerQueue({ name: 'notification-high' }, { name: 'notification-medium' }, { name: 'notification-low' }),
            ],
            providers: [
                notification_workflow_service_1.NotificationWorkflowService,
                notification_analytics_service_1.NotificationAnalyticsService,
                notification_queue_management_service_1.NotificationQueueManagementService,
                notification_template_management_service_1.NotificationTemplateManagementService,
                // Mock external module services
                {
                    provide: 'AssetManagementService',
                    useValue: {
                        getAssetById: jest.fn().mockResolvedValue({
                            id: 'asset-123',
                            name: 'Web Server 01',
                            type: 'server',
                            criticality: 'high',
                            owner: 'infrastructure-team',
                            environment: 'production',
                            metadata: {
                                ip: '**********',
                                os: 'Ubuntu 20.04',
                                services: ['nginx', 'nodejs'],
                            },
                        }),
                        getAssetsByQuery: jest.fn().mockResolvedValue([]),
                        updateAssetStatus: jest.fn().mockResolvedValue(true),
                    },
                },
                {
                    provide: 'ThreatIntelligenceService',
                    useValue: {
                        getThreatById: jest.fn().mockResolvedValue({
                            id: 'threat-456',
                            name: 'CVE-2024-1234',
                            severity: 'critical',
                            type: 'vulnerability',
                            description: 'Remote code execution vulnerability',
                            indicators: ['malicious-ip', 'suspicious-domain'],
                            affectedAssets: ['asset-123'],
                        }),
                        getThreatsForAsset: jest.fn().mockResolvedValue([]),
                        correlateThreats: jest.fn().mockResolvedValue([]),
                    },
                },
                {
                    provide: 'UserManagementService',
                    useValue: {
                        getUserById: jest.fn().mockResolvedValue(testUser),
                        validateUserPermissions: jest.fn().mockResolvedValue(true),
                        getUsersByRole: jest.fn().mockResolvedValue([testUser]),
                    },
                },
            ],
        }).compile();
        app = moduleFixture.createNestApplication();
        await app.init();
        // Get service instances
        workflowService = moduleFixture.get(notification_workflow_service_1.NotificationWorkflowService);
        analyticsService = moduleFixture.get(notification_analytics_service_1.NotificationAnalyticsService);
        queueService = moduleFixture.get(notification_queue_management_service_1.NotificationQueueManagementService);
        templateService = moduleFixture.get(notification_template_management_service_1.NotificationTemplateManagementService);
        // Get repository instances
        workflowRepository = moduleFixture.get((0, typeorm_2.getRepositoryToken)(notification_workflow_entity_1.NotificationWorkflow));
        executionRepository = moduleFixture.get((0, typeorm_2.getRepositoryToken)(workflow_execution_entity_1.WorkflowExecution));
        analyticsRepository = moduleFixture.get((0, typeorm_2.getRepositoryToken)(notification_analytics_event_entity_1.NotificationAnalyticsEvent));
        templateRepository = moduleFixture.get((0, typeorm_2.getRepositoryToken)(notification_template_entity_1.NotificationTemplate));
    });
    afterAll(async () => {
        await app.close();
    });
    beforeEach(async () => {
        // Clean up database before each test
        await analyticsRepository.delete({});
        await executionRepository.delete({});
        await workflowRepository.delete({});
        await templateRepository.delete({});
    });
    describe('Asset Management ↔ Threat Intelligence Integration', () => {
        it('should trigger notification workflow when new threat affects asset', async () => {
            // Create workflow for threat notifications
            const threatWorkflow = await workflowService.createWorkflow({
                name: 'Threat Detection Workflow',
                description: 'Notify when threats affect critical assets',
                definition: {
                    startStep: 'assess_threat',
                    steps: [
                        {
                            id: 'assess_threat',
                            type: 'condition',
                            config: {
                                condition: {
                                    field: 'input.threat.severity',
                                    operator: 'in',
                                    value: ['high', 'critical'],
                                },
                            },
                            nextSteps: [
                                {
                                    stepId: 'notify_security_team',
                                    condition: {
                                        field: 'stepResult.result',
                                        operator: 'eq',
                                        value: true,
                                    },
                                },
                            ],
                        },
                        {
                            id: 'notify_security_team',
                            type: 'notification',
                            config: {
                                channel: 'email',
                                message: 'THREAT ALERT: {{input.threat.name}} affects {{input.asset.name}}',
                                recipients: ['<EMAIL>'],
                                priority: 'critical',
                            },
                        },
                    ],
                    triggers: [
                        {
                            type: 'event',
                            eventType: 'threat.asset_affected',
                            conditions: [
                                {
                                    field: 'asset.criticality',
                                    operator: 'eq',
                                    value: 'high',
                                },
                            ],
                        },
                    ],
                },
                status: 'active',
                category: 'incident_response',
            }, testUser);
            // Simulate threat affecting asset event
            const threatEvent = {
                type: 'threat.asset_affected',
                data: {
                    threat: {
                        id: 'threat-456',
                        name: 'CVE-2024-1234',
                        severity: 'critical',
                        type: 'vulnerability',
                    },
                    asset: {
                        id: 'asset-123',
                        name: 'Web Server 01',
                        criticality: 'high',
                        environment: 'production',
                    },
                    correlation: {
                        confidence: 0.95,
                        indicators: ['malicious-ip', 'suspicious-domain'],
                    },
                },
                timestamp: new Date(),
            };
            // Execute workflow with threat data
            const execution = await workflowService.executeWorkflow(threatWorkflow.id, {
                input: {
                    threat: threatEvent.data.threat,
                    asset: threatEvent.data.asset,
                    correlation: threatEvent.data.correlation,
                    user: testUser,
                },
                context: {
                    eventType: 'threat.asset_affected',
                    environment: 'production',
                    escalationLevel: 1,
                },
            }, testUser);
            // Wait for execution
            await new Promise(resolve => setTimeout(resolve, 3000));
            // Verify execution completed successfully
            const completedExecution = await executionRepository.findOne({
                where: { id: execution.executionId },
                relations: ['contexts'],
            });
            expect(completedExecution.status).toBe('completed');
            expect(completedExecution.stepsCompleted).toBe(2); // condition + notification
            // Verify analytics were recorded
            const analyticsEvents = await analyticsRepository.find({
                where: { notificationId: execution.executionId },
            });
            expect(analyticsEvents.length).toBeGreaterThan(0);
            const event = analyticsEvents[0];
            expect(event.metadata.threatId).toBe('threat-456');
            expect(event.metadata.assetId).toBe('asset-123');
        });
        it('should correlate multiple threats affecting same asset', async () => {
            // Create correlation workflow
            const correlationWorkflow = await workflowService.createWorkflow({
                name: 'Threat Correlation Workflow',
                description: 'Correlate multiple threats for comprehensive analysis',
                definition: {
                    startStep: 'correlate_threats',
                    steps: [
                        {
                            id: 'correlate_threats',
                            type: 'http_request',
                            config: {
                                url: 'http://threat-intelligence-service/correlate',
                                method: 'POST',
                                body: {
                                    assetId: '{{input.asset.id}}',
                                    timeWindow: '24h',
                                },
                            },
                            nextStep: 'assess_risk',
                        },
                        {
                            id: 'assess_risk',
                            type: 'condition',
                            config: {
                                condition: {
                                    field: 'stepResult.data.riskScore',
                                    operator: 'gt',
                                    value: 80,
                                },
                            },
                            nextSteps: [
                                {
                                    stepId: 'escalate_incident',
                                    condition: {
                                        field: 'stepResult.result',
                                        operator: 'eq',
                                        value: true,
                                    },
                                },
                            ],
                        },
                        {
                            id: 'escalate_incident',
                            type: 'notification',
                            config: {
                                channel: 'slack',
                                message: 'HIGH RISK: Multiple threats detected for {{input.asset.name}}',
                                recipients: ['#security-incidents'],
                                priority: 'critical',
                            },
                        },
                    ],
                },
                status: 'active',
            }, testUser);
            // Execute with multiple threat data
            const execution = await workflowService.executeWorkflow(correlationWorkflow.id, {
                input: {
                    asset: {
                        id: 'asset-123',
                        name: 'Web Server 01',
                        criticality: 'high',
                    },
                    threats: [
                        { id: 'threat-1', severity: 'high' },
                        { id: 'threat-2', severity: 'critical' },
                        { id: 'threat-3', severity: 'medium' },
                    ],
                    user: testUser,
                },
            }, testUser);
            await new Promise(resolve => setTimeout(resolve, 3000));
            const completedExecution = await executionRepository.findOne({
                where: { id: execution.executionId },
            });
            expect(completedExecution.status).toBe('completed');
        });
    });
    describe('User Management Authentication Integration', () => {
        it('should validate user permissions for notification workflows', async () => {
            // Create workflow requiring specific permissions
            const restrictedWorkflow = await workflowService.createWorkflow({
                name: 'Restricted Security Workflow',
                description: 'Requires security permissions',
                definition: {
                    startStep: 'check_permissions',
                    steps: [
                        {
                            id: 'check_permissions',
                            type: 'condition',
                            config: {
                                condition: {
                                    field: 'input.user.permissions',
                                    operator: 'contains',
                                    value: 'security.admin',
                                },
                            },
                            nextSteps: [
                                {
                                    stepId: 'send_security_alert',
                                    condition: {
                                        field: 'stepResult.result',
                                        operator: 'eq',
                                        value: true,
                                    },
                                },
                            ],
                        },
                        {
                            id: 'send_security_alert',
                            type: 'notification',
                            config: {
                                channel: 'email',
                                message: 'Security alert authorized by {{input.user.email}}',
                                recipients: ['<EMAIL>'],
                            },
                        },
                    ],
                },
                status: 'active',
                configuration: {
                    permissions: {
                        execute: ['security.admin', 'admin'],
                        modify: ['admin'],
                        view: ['security.user', 'security.admin', 'admin'],
                    },
                },
            }, testUser);
            // Test with authorized user
            const authorizedUser = {
                ...testUser,
                permissions: ['security.admin', 'notification.send'],
            };
            const authorizedExecution = await workflowService.executeWorkflow(restrictedWorkflow.id, {
                input: {
                    user: authorizedUser,
                    alert: { severity: 'high', message: 'Security incident detected' },
                },
            }, authorizedUser);
            await new Promise(resolve => setTimeout(resolve, 2000));
            const authorizedResult = await executionRepository.findOne({
                where: { id: authorizedExecution.executionId },
            });
            expect(authorizedResult.status).toBe('completed');
            // Test with unauthorized user
            const unauthorizedUser = {
                ...testUser,
                permissions: ['notification.send'], // Missing security.admin
            };
            try {
                await workflowService.executeWorkflow(restrictedWorkflow.id, {
                    input: {
                        user: unauthorizedUser,
                        alert: { severity: 'high', message: 'Security incident detected' },
                    },
                }, unauthorizedUser);
            }
            catch (error) {
                expect(error.message).toContain('permission');
            }
        });
        it('should track user actions in analytics', async () => {
            const workflow = await workflowService.createWorkflow({
                name: 'User Action Tracking Workflow',
                definition: {
                    startStep: 'log_action',
                    steps: [
                        {
                            id: 'log_action',
                            type: 'notification',
                            config: {
                                channel: 'email',
                                message: 'Action performed by {{input.user.email}}',
                                recipients: ['<EMAIL>'],
                            },
                        },
                    ],
                },
                status: 'active',
            }, testUser);
            const execution = await workflowService.executeWorkflow(workflow.id, {
                input: {
                    user: testUser,
                    action: 'security_alert_triggered',
                    metadata: {
                        source: 'automated_scan',
                        severity: 'medium',
                    },
                },
            }, testUser);
            await new Promise(resolve => setTimeout(resolve, 2000));
            // Verify analytics include user information
            const analyticsEvents = await analyticsRepository.find({
                where: { notificationId: execution.executionId },
            });
            expect(analyticsEvents.length).toBeGreaterThan(0);
            const event = analyticsEvents[0];
            expect(event.metadata.userId).toBe(testUser.id);
            expect(event.metadata.userEmail).toBe(testUser.email);
            expect(event.metadata.action).toBe('security_alert_triggered');
        });
    });
    describe('Event-Driven Architecture Validation', () => {
        it('should process asset discovery events in real-time', async () => {
            // Create asset discovery workflow
            const discoveryWorkflow = await workflowService.createWorkflow({
                name: 'Asset Discovery Workflow',
                description: 'Process newly discovered assets',
                definition: {
                    startStep: 'validate_asset',
                    steps: [
                        {
                            id: 'validate_asset',
                            type: 'condition',
                            config: {
                                condition: {
                                    field: 'input.asset.type',
                                    operator: 'in',
                                    value: ['server', 'database', 'application'],
                                },
                            },
                            nextStep: 'categorize_asset',
                        },
                        {
                            id: 'categorize_asset',
                            type: 'variable',
                            config: {
                                variables: {
                                    category: '{{input.asset.type}}',
                                    riskLevel: 'medium',
                                },
                            },
                            nextStep: 'notify_teams',
                        },
                        {
                            id: 'notify_teams',
                            type: 'notification',
                            config: {
                                channel: 'slack',
                                message: 'New {{variables.category}} discovered: {{input.asset.name}}',
                                recipients: ['#asset-management'],
                            },
                        },
                    ],
                    triggers: [
                        {
                            type: 'event',
                            eventType: 'asset.discovered',
                        },
                    ],
                },
                status: 'active',
            }, testUser);
            // Simulate asset discovery event
            const assetDiscoveryEvent = {
                type: 'asset.discovered',
                data: {
                    asset: {
                        id: 'asset-new-789',
                        name: 'Database Server 02',
                        type: 'database',
                        ip: '**********',
                        discoveredAt: new Date(),
                    },
                    scanner: 'nmap',
                    confidence: 0.98,
                },
            };
            const execution = await workflowService.executeWorkflow(discoveryWorkflow.id, {
                input: {
                    asset: assetDiscoveryEvent.data.asset,
                    scanner: assetDiscoveryEvent.data.scanner,
                    user: testUser,
                },
                context: {
                    eventType: 'asset.discovered',
                    realTime: true,
                },
            }, testUser);
            await new Promise(resolve => setTimeout(resolve, 3000));
            const completedExecution = await executionRepository.findOne({
                where: { id: execution.executionId },
                relations: ['contexts'],
            });
            expect(completedExecution.status).toBe('completed');
            expect(completedExecution.stepsCompleted).toBe(3);
            // Verify real-time processing
            const processingTime = completedExecution.duration;
            expect(processingTime).toBeLessThan(5000); // Should process quickly
        });
        it('should handle event ordering and deduplication', async () => {
            const eventWorkflow = await workflowService.createWorkflow({
                name: 'Event Deduplication Workflow',
                definition: {
                    startStep: 'check_duplicate',
                    steps: [
                        {
                            id: 'check_duplicate',
                            type: 'condition',
                            config: {
                                condition: {
                                    field: 'context.isDuplicate',
                                    operator: 'neq',
                                    value: true,
                                },
                            },
                            nextSteps: [
                                {
                                    stepId: 'process_event',
                                    condition: {
                                        field: 'stepResult.result',
                                        operator: 'eq',
                                        value: true,
                                    },
                                },
                            ],
                        },
                        {
                            id: 'process_event',
                            type: 'notification',
                            config: {
                                channel: 'email',
                                message: 'Processing unique event: {{input.event.id}}',
                                recipients: ['<EMAIL>'],
                            },
                        },
                    ],
                },
                status: 'active',
            }, testUser);
            // Send duplicate events
            const eventData = {
                event: { id: 'event-123', type: 'security_alert' },
                user: testUser,
            };
            const execution1 = await workflowService.executeWorkflow(eventWorkflow.id, {
                input: eventData,
                context: { isDuplicate: false },
            }, testUser);
            const execution2 = await workflowService.executeWorkflow(eventWorkflow.id, {
                input: eventData,
                context: { isDuplicate: true }, // Duplicate
            }, testUser);
            await new Promise(resolve => setTimeout(resolve, 3000));
            const result1 = await executionRepository.findOne({
                where: { id: execution1.executionId },
            });
            const result2 = await executionRepository.findOne({
                where: { id: execution2.executionId },
            });
            // First execution should complete, second should skip processing
            expect(result1.status).toBe('completed');
            expect(result1.stepsCompleted).toBe(2);
            expect(result2.status).toBe('completed');
            expect(result2.stepsCompleted).toBe(1); // Only check_duplicate step
        });
    });
    describe('Performance and Data Consistency', () => {
        it('should maintain data consistency across modules', async () => {
            // Create workflow that updates multiple modules
            const consistencyWorkflow = await workflowService.createWorkflow({
                name: 'Data Consistency Workflow',
                definition: {
                    startStep: 'update_asset',
                    steps: [
                        {
                            id: 'update_asset',
                            type: 'http_request',
                            config: {
                                url: 'http://asset-service/assets/{{input.asset.id}}',
                                method: 'PATCH',
                                body: {
                                    status: 'compromised',
                                    lastIncident: '{{input.incident.id}}',
                                },
                            },
                            nextStep: 'update_threat_intel',
                        },
                        {
                            id: 'update_threat_intel',
                            type: 'http_request',
                            config: {
                                url: 'http://threat-intel-service/threats/{{input.threat.id}}',
                                method: 'PATCH',
                                body: {
                                    affectedAssets: ['{{input.asset.id}}'],
                                    status: 'active',
                                },
                            },
                            nextStep: 'notify_incident',
                        },
                        {
                            id: 'notify_incident',
                            type: 'notification',
                            config: {
                                channel: 'email',
                                message: 'Incident {{input.incident.id}} updated across all systems',
                                recipients: ['<EMAIL>'],
                            },
                        },
                    ],
                },
                status: 'active',
            }, testUser);
            const execution = await workflowService.executeWorkflow(consistencyWorkflow.id, {
                input: {
                    asset: { id: 'asset-123' },
                    threat: { id: 'threat-456' },
                    incident: { id: 'incident-789' },
                    user: testUser,
                },
            }, testUser);
            await new Promise(resolve => setTimeout(resolve, 4000));
            const completedExecution = await executionRepository.findOne({
                where: { id: execution.executionId },
                relations: ['contexts'],
            });
            expect(completedExecution.status).toBe('completed');
            expect(completedExecution.stepsCompleted).toBe(3);
            // Verify all steps completed successfully (indicating consistency)
            completedExecution.contexts.forEach(context => {
                expect(context.status).toBe('completed');
                expect(context.error).toBeNull();
            });
        });
        it('should handle high-volume cross-module events', async () => {
            const highVolumeWorkflow = await workflowService.createWorkflow({
                name: 'High Volume Processing Workflow',
                definition: {
                    startStep: 'batch_process',
                    steps: [
                        {
                            id: 'batch_process',
                            type: 'notification',
                            config: {
                                channel: 'webhook',
                                message: 'Processing batch {{input.batch.id}}',
                                recipients: ['http://analytics-service/batch-processed'],
                            },
                        },
                    ],
                },
                status: 'active',
            }, testUser);
            // Process multiple events concurrently
            const batchSize = 50;
            const executions = [];
            for (let i = 0; i < batchSize; i++) {
                const execution = workflowService.executeWorkflow(highVolumeWorkflow.id, {
                    input: {
                        batch: { id: `batch-${i}`, size: 100 },
                        user: testUser,
                    },
                }, testUser);
                executions.push(execution);
            }
            const results = await Promise.all(executions);
            expect(results).toHaveLength(batchSize);
            // Wait for all executions to complete
            await new Promise(resolve => setTimeout(resolve, 10000));
            // Verify all executions completed successfully
            const completedExecutions = await executionRepository.find({
                where: { workflowId: highVolumeWorkflow.id },
            });
            expect(completedExecutions).toHaveLength(batchSize);
            completedExecutions.forEach(execution => {
                expect(execution.status).toBe('completed');
            });
            // Verify analytics were recorded for all executions
            const analyticsEvents = await analyticsRepository.find({
                where: { workflowId: highVolumeWorkflow.id },
            });
            expect(analyticsEvents.length).toBeGreaterThanOrEqual(batchSize);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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