{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\enriched-event-enrichment-failed.domain-event.ts", "mappings": ";;;AAAA,6DAA4E;AA4B5E;;;;;;;;;;GAUG;AACH,MAAa,wCAAyC,SAAQ,+BAAuD;IACnH,YACE,WAA2B,EAC3B,SAAiD,EACjD,OAOC;QAED,KAAK,CAAC,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,IAAI,EAAE,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,IAAI,EAAE,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,IAAI,EAAE,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,OAAO,KAAK,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,yBAAyB;QACvB,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,aAAa,KAAK,SAAS;YAChC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC5C,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC;YAC/C,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,IAAI,CAAC,aAAa,KAAK,YAAY;YACnC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC;YAC/C,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,aAAa,KAAK,YAAY;YACnC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC;YAC/C,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC;YAC1C,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,aAAa,KAAK,gBAAgB;YACvC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC;YACzC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC;YACjD,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,OAAO,IAAI,CAAC,aAAa,KAAK,eAAe;YACtC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC3C,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,IAAI,CAAC,aAAa,KAAK,aAAa;YACpC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC3C,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC;YAC1C,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC;YACzC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE,CAAC;YAChE,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,EAAE,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC;YACrD,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAC;YACzD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,OAAO,CAAC,CAAC,CAAC,WAAW;QACvB,CAAC;QAED,8CAA8C;QAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,CAAC,WAAW;QACnC,MAAM,gBAAgB,GAAG,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;QAEnE,wCAAwC;QACxC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC;QAEpC,uCAAuC;QACvC,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC;YAC5B,OAAO,gBAAgB,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,+BAA+B;QACvE,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;YAC1B,OAAO,gBAAgB,GAAG,MAAM,CAAC;QACnC,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAC;YACzD,OAAO,SAAS,GAAG,MAAM,CAAC,CAAC,8BAA8B;QAC3D,CAAC;QAED,OAAO,gBAAgB,GAAG,MAAM,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,aAAa;QACX,yBAAyB;QACzB,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAC;YAC7B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,qBAAqB;QACrB,IAAI,IAAI,CAAC,cAAc,EAAE,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC;YACrD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,4CAA4C;QAC5C,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,gBAAgB;QAOd,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACzC,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QACjC,MAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEnD,IAAI,iBAAiB,GAAG,mBAAmB,CAAC;QAE5C,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,iBAAiB,GAAG,8BAA8B,CAAC;QACrD,CAAC;aAAM,IAAI,CAAC,WAAW,EAAE,CAAC;YACxB,iBAAiB,GAAG,kCAAkC,CAAC;QACzD,CAAC;aAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC;YACnC,iBAAiB,GAAG,6BAA6B,CAAC;QACpD,CAAC;aAAM,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;YACjC,iBAAiB,GAAG,sCAAsC,CAAC;QAC7D,CAAC;QAED,OAAO;YACL,QAAQ;YACR,WAAW;YACX,QAAQ;YACR,iBAAiB;YACjB,iBAAiB;SAClB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,eAAe;QAqBb,OAAO;YACL,eAAe,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;YAC5C,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE;YACpD,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;YAC7C,kBAAkB,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM;YAC7C,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE;YACrC,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE;YACzB,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE;YAC3C,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE;YACrC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE;YAC3C,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EAAE;YACzC,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE;YACnD,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE;YACtC,qBAAqB,EAAE,IAAI,CAAC,wBAAwB,EAAE;YACtD,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE;YACnC,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE;SACvC,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,GAAG,KAAK,CAAC,MAAM,EAAE;YACjB,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE;SACrC,CAAC;IACJ,CAAC;CACF;AApVD,4FAoVC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\enriched-event-enrichment-failed.domain-event.ts"], "sourcesContent": ["import { BaseDomainEvent, UniqueEntityId } from '../../../../shared-kernel';\r\n\r\n/**\r\n * Enriched Event Enrichment Failed Domain Event Data\r\n */\r\nexport interface EnrichedEventEnrichmentFailedEventData {\r\n  /** Normalized event ID that was being enriched */\r\n  normalizedEventId: UniqueEntityId;\r\n  /** Error message describing the failure */\r\n  error: string;\r\n  /** Current enrichment attempt number */\r\n  attempt: number;\r\n  /** Whether maximum attempts have been exceeded */\r\n  maxAttemptsExceeded: boolean;\r\n  /** Timestamp of the failure */\r\n  timestamp?: Date;\r\n  /** Error code for categorization */\r\n  errorCode?: string;\r\n  /** Error category (e.g., 'network', 'timeout', 'validation') */\r\n  errorCategory?: string;\r\n  /** Failed enrichment sources */\r\n  failedSources?: string[];\r\n  /** Partial results that were obtained before failure */\r\n  partialResults?: Record<string, any>;\r\n  /** Additional error context */\r\n  errorContext?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Enriched Event Enrichment Failed Domain Event\r\n * \r\n * Raised when enrichment processing fails for a security event.\r\n * This event triggers various downstream processes including:\r\n * - Error handling and recovery workflows\r\n * - Retry logic and backoff strategies\r\n * - Alert generation for operational issues\r\n * - Metrics collection for failure analysis\r\n * - Fallback processing mechanisms\r\n */\r\nexport class EnrichedEventEnrichmentFailedDomainEvent extends BaseDomainEvent<EnrichedEventEnrichmentFailedEventData> {\r\n  constructor(\r\n    aggregateId: UniqueEntityId,\r\n    eventData: EnrichedEventEnrichmentFailedEventData,\r\n    options?: {\r\n      eventId?: UniqueEntityId;\r\n      occurredOn?: Date;\r\n      eventVersion?: number;\r\n      correlationId?: string;\r\n      causationId?: string;\r\n      metadata?: Record<string, any>;\r\n    }\r\n  ) {\r\n    super(aggregateId, eventData, options);\r\n  }\r\n\r\n  /**\r\n   * Get the normalized event ID\r\n   */\r\n  get normalizedEventId(): UniqueEntityId {\r\n    return this.eventData.normalizedEventId;\r\n  }\r\n\r\n  /**\r\n   * Get the error message\r\n   */\r\n  get error(): string {\r\n    return this.eventData.error;\r\n  }\r\n\r\n  /**\r\n   * Get the current attempt number\r\n   */\r\n  get attempt(): number {\r\n    return this.eventData.attempt;\r\n  }\r\n\r\n  /**\r\n   * Check if maximum attempts have been exceeded\r\n   */\r\n  get maxAttemptsExceeded(): boolean {\r\n    return this.eventData.maxAttemptsExceeded;\r\n  }\r\n\r\n  /**\r\n   * Get the timestamp of the failure\r\n   */\r\n  get timestamp(): Date {\r\n    return this.eventData.timestamp || this.occurredOn;\r\n  }\r\n\r\n  /**\r\n   * Get the error code\r\n   */\r\n  get errorCode(): string | undefined {\r\n    return this.eventData.errorCode;\r\n  }\r\n\r\n  /**\r\n   * Get the error category\r\n   */\r\n  get errorCategory(): string | undefined {\r\n    return this.eventData.errorCategory;\r\n  }\r\n\r\n  /**\r\n   * Get the failed enrichment sources\r\n   */\r\n  get failedSources(): string[] {\r\n    return this.eventData.failedSources || [];\r\n  }\r\n\r\n  /**\r\n   * Get partial results that were obtained\r\n   */\r\n  get partialResults(): Record<string, any> {\r\n    return this.eventData.partialResults || {};\r\n  }\r\n\r\n  /**\r\n   * Get additional error context\r\n   */\r\n  get errorContext(): Record<string, any> {\r\n    return this.eventData.errorContext || {};\r\n  }\r\n\r\n  /**\r\n   * Check if this is the first attempt\r\n   */\r\n  isFirstAttempt(): boolean {\r\n    return this.attempt === 1;\r\n  }\r\n\r\n  /**\r\n   * Check if retry is possible\r\n   */\r\n  canRetry(): boolean {\r\n    return !this.maxAttemptsExceeded;\r\n  }\r\n\r\n  /**\r\n   * Check if partial results are available\r\n   */\r\n  hasPartialResults(): boolean {\r\n    return Object.keys(this.partialResults).length > 0;\r\n  }\r\n\r\n  /**\r\n   * Check if multiple sources failed\r\n   */\r\n  hasMultipleSourceFailures(): boolean {\r\n    return this.failedSources.length > 1;\r\n  }\r\n\r\n  /**\r\n   * Check if this is a network-related error\r\n   */\r\n  isNetworkError(): boolean {\r\n    return this.errorCategory === 'network' || \r\n           this.error.toLowerCase().includes('network') ||\r\n           this.error.toLowerCase().includes('connection') ||\r\n           this.error.toLowerCase().includes('timeout');\r\n  }\r\n\r\n  /**\r\n   * Check if this is a validation error\r\n   */\r\n  isValidationError(): boolean {\r\n    return this.errorCategory === 'validation' ||\r\n           this.error.toLowerCase().includes('validation') ||\r\n           this.error.toLowerCase().includes('invalid');\r\n  }\r\n\r\n  /**\r\n   * Check if this is a rate limiting error\r\n   */\r\n  isRateLimitError(): boolean {\r\n    return this.errorCategory === 'rate_limit' ||\r\n           this.error.toLowerCase().includes('rate limit') ||\r\n           this.error.toLowerCase().includes('quota') ||\r\n           this.error.toLowerCase().includes('throttle');\r\n  }\r\n\r\n  /**\r\n   * Check if this is an authentication error\r\n   */\r\n  isAuthenticationError(): boolean {\r\n    return this.errorCategory === 'authentication' ||\r\n           this.error.toLowerCase().includes('auth') ||\r\n           this.error.toLowerCase().includes('unauthorized') ||\r\n           this.error.toLowerCase().includes('forbidden');\r\n  }\r\n\r\n  /**\r\n   * Check if this is a configuration error\r\n   */\r\n  isConfigurationError(): boolean {\r\n    return this.errorCategory === 'configuration' ||\r\n           this.error.toLowerCase().includes('config') ||\r\n           this.error.toLowerCase().includes('setting');\r\n  }\r\n\r\n  /**\r\n   * Check if this is a data format error\r\n   */\r\n  isDataFormatError(): boolean {\r\n    return this.errorCategory === 'data_format' ||\r\n           this.error.toLowerCase().includes('format') ||\r\n           this.error.toLowerCase().includes('parse') ||\r\n           this.error.toLowerCase().includes('json') ||\r\n           this.error.toLowerCase().includes('xml');\r\n  }\r\n\r\n  /**\r\n   * Get error severity level\r\n   */\r\n  getErrorSeverity(): 'low' | 'medium' | 'high' | 'critical' {\r\n    if (this.maxAttemptsExceeded) {\r\n      return 'critical';\r\n    }\r\n    \r\n    if (this.isAuthenticationError() || this.isConfigurationError()) {\r\n      return 'high';\r\n    }\r\n    \r\n    if (this.isNetworkError() || this.isRateLimitError()) {\r\n      return 'medium';\r\n    }\r\n    \r\n    if (this.isValidationError() || this.isDataFormatError()) {\r\n      return 'low';\r\n    }\r\n    \r\n    return 'medium';\r\n  }\r\n\r\n  /**\r\n   * Get recommended retry delay in milliseconds\r\n   */\r\n  getRecommendedRetryDelay(): number {\r\n    if (this.maxAttemptsExceeded) {\r\n      return 0; // No retry\r\n    }\r\n    \r\n    // Exponential backoff based on attempt number\r\n    const baseDelay = 1000; // 1 second\r\n    const exponentialDelay = baseDelay * Math.pow(2, this.attempt - 1);\r\n    \r\n    // Add jitter to prevent thundering herd\r\n    const jitter = Math.random() * 1000;\r\n    \r\n    // Different delays based on error type\r\n    if (this.isRateLimitError()) {\r\n      return exponentialDelay * 2 + jitter; // Longer delay for rate limits\r\n    }\r\n    \r\n    if (this.isNetworkError()) {\r\n      return exponentialDelay + jitter;\r\n    }\r\n    \r\n    if (this.isValidationError() || this.isDataFormatError()) {\r\n      return baseDelay + jitter; // Short delay for data issues\r\n    }\r\n    \r\n    return exponentialDelay + jitter;\r\n  }\r\n\r\n  /**\r\n   * Check if error is recoverable\r\n   */\r\n  isRecoverable(): boolean {\r\n    // Non-recoverable errors\r\n    if (this.isAuthenticationError() || \r\n        this.isConfigurationError() || \r\n        this.isValidationError() ||\r\n        this.isDataFormatError()) {\r\n      return false;\r\n    }\r\n    \r\n    // Recoverable errors\r\n    if (this.isNetworkError() || this.isRateLimitError()) {\r\n      return true;\r\n    }\r\n    \r\n    // Default to recoverable for unknown errors\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Get failure impact assessment\r\n   */\r\n  getFailureImpact(): {\r\n    severity: string;\r\n    recoverable: boolean;\r\n    canRetry: boolean;\r\n    hasPartialResults: boolean;\r\n    recommendedAction: string;\r\n  } {\r\n    const severity = this.getErrorSeverity();\r\n    const recoverable = this.isRecoverable();\r\n    const canRetry = this.canRetry();\r\n    const hasPartialResults = this.hasPartialResults();\r\n    \r\n    let recommendedAction = 'Monitor and retry';\r\n    \r\n    if (this.maxAttemptsExceeded) {\r\n      recommendedAction = 'Manual intervention required';\r\n    } else if (!recoverable) {\r\n      recommendedAction = 'Fix configuration or data format';\r\n    } else if (this.isRateLimitError()) {\r\n      recommendedAction = 'Wait and retry with backoff';\r\n    } else if (this.isNetworkError()) {\r\n      recommendedAction = 'Check network connectivity and retry';\r\n    }\r\n    \r\n    return {\r\n      severity,\r\n      recoverable,\r\n      canRetry,\r\n      hasPartialResults,\r\n      recommendedAction,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get event summary for handlers\r\n   */\r\n  getEventSummary(): {\r\n    enrichedEventId: string;\r\n    normalizedEventId: string;\r\n    error: string;\r\n    errorCode?: string;\r\n    errorCategory?: string;\r\n    attempt: number;\r\n    maxAttemptsExceeded: boolean;\r\n    failedSourcesCount: number;\r\n    isFirstAttempt: boolean;\r\n    canRetry: boolean;\r\n    hasPartialResults: boolean;\r\n    isNetworkError: boolean;\r\n    isValidationError: boolean;\r\n    isRateLimitError: boolean;\r\n    isAuthenticationError: boolean;\r\n    errorSeverity: string;\r\n    recommendedRetryDelay: number;\r\n    isRecoverable: boolean;\r\n    failureImpact: ReturnType<typeof this.getFailureImpact>;\r\n  } {\r\n    return {\r\n      enrichedEventId: this.aggregateId.toString(),\r\n      normalizedEventId: this.normalizedEventId.toString(),\r\n      error: this.error,\r\n      errorCode: this.errorCode,\r\n      errorCategory: this.errorCategory,\r\n      attempt: this.attempt,\r\n      maxAttemptsExceeded: this.maxAttemptsExceeded,\r\n      failedSourcesCount: this.failedSources.length,\r\n      isFirstAttempt: this.isFirstAttempt(),\r\n      canRetry: this.canRetry(),\r\n      hasPartialResults: this.hasPartialResults(),\r\n      isNetworkError: this.isNetworkError(),\r\n      isValidationError: this.isValidationError(),\r\n      isRateLimitError: this.isRateLimitError(),\r\n      isAuthenticationError: this.isAuthenticationError(),\r\n      errorSeverity: this.getErrorSeverity(),\r\n      recommendedRetryDelay: this.getRecommendedRetryDelay(),\r\n      isRecoverable: this.isRecoverable(),\r\n      failureImpact: this.getFailureImpact(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      ...super.toJSON(),\r\n      eventSummary: this.getEventSummary(),\r\n    };\r\n  }\r\n}"], "version": 3}