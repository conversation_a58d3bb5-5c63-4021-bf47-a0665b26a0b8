03fe6603effa0ffdd91a219e441fdca9
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var TokenService_1;
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const password_service_1 = require("./password.service");
const jwt_config_1 = require("../../config/jwt.config");
const crypto = __importStar(require("crypto"));
// Import entities (these will be created later)
const refresh_token_entity_1 = require("../../../modules/user-management/domain/entities/refresh-token.entity");
/**
 * Token service for generating and managing JWT and refresh tokens
 * Handles token creation, validation, and refresh token lifecycle
 */
let TokenService = TokenService_1 = class TokenService {
    constructor(jwtService, configService, passwordService, refreshTokenRepository) {
        this.jwtService = jwtService;
        this.configService = configService;
        this.passwordService = passwordService;
        this.refreshTokenRepository = refreshTokenRepository;
        this.logger = new common_1.Logger(TokenService_1.name);
    }
    /**
     * Generate JWT access token
     * @param payload Token payload
     * @returns Promise<string> JWT token
     */
    async generateAccessToken(payload) {
        try {
            this.logger.debug('Generating access token', {
                userId: payload.sub,
                email: payload.email,
                roleCount: payload.roles?.length || 0,
                permissionCount: payload.permissions?.length || 0,
            });
            const jwtConfig = this.configService.get('jwt');
            const tokenPayload = {
                ...payload,
                iat: Math.floor(Date.now() / 1000),
                type: 'access',
            };
            const signOptions = jwt_config_1.JwtConfigHelper.getSignOptions(jwtConfig);
            const secretOrKey = jwt_config_1.JwtConfigHelper.getSecretOrKey(jwtConfig, true);
            const token = this.jwtService.sign(tokenPayload, {
                ...signOptions,
                secret: secretOrKey,
            });
            this.logger.debug('Access token generated successfully', {
                userId: payload.sub,
                tokenLength: token.length,
                algorithm: jwtConfig.algorithm,
            });
            return token;
        }
        catch (error) {
            this.logger.error('Error generating access token', {
                userId: payload.sub,
                error: error.message,
            });
            throw new Error('Failed to generate access token');
        }
    }
    /**
     * Generate refresh token
     * @param userId User ID
     * @returns Promise<string> Refresh token
     */
    async generateRefreshToken(userId) {
        try {
            this.logger.debug('Generating refresh token', { userId });
            const jwtConfig = this.configService.get('jwt');
            // Generate random token
            const tokenValue = crypto.randomBytes(32).toString('hex');
            // Hash the token for storage
            const tokenHash = await this.passwordService.hashPassword(tokenValue);
            // Calculate expiration date
            const expiresAt = new Date();
            const expirationTime = this.parseExpirationTime(jwtConfig.refreshExpiresIn);
            expiresAt.setTime(expiresAt.getTime() + expirationTime * 1000);
            // Save refresh token to database
            const refreshToken = this.refreshTokenRepository.create({
                userId,
                tokenHash,
                expiresAt,
                isRevoked: false,
            });
            await this.refreshTokenRepository.save(refreshToken);
            this.logger.debug('Refresh token generated successfully', {
                userId,
                tokenId: refreshToken.id,
                expiresAt,
            });
            return tokenValue;
        }
        catch (error) {
            this.logger.error('Error generating refresh token', {
                userId,
                error: error.message,
            });
            throw new Error('Failed to generate refresh token');
        }
    }
    /**
     * Verify JWT token
     * @param token JWT token
     * @returns Promise<any> Decoded token payload
     */
    async verifyAccessToken(token) {
        try {
            this.logger.debug('Verifying access token', {
                tokenLength: token.length,
            });
            const jwtConfig = this.configService.get('jwt');
            const verifyOptions = jwt_config_1.JwtConfigHelper.getVerifyOptions(jwtConfig);
            const secretOrKey = jwt_config_1.JwtConfigHelper.getSecretOrKey(jwtConfig, false);
            const payload = this.jwtService.verify(token, {
                ...verifyOptions,
                secret: secretOrKey,
            });
            // Validate token type
            if (payload.type !== 'access') {
                throw new Error('Invalid token type');
            }
            this.logger.debug('Access token verified successfully', {
                userId: payload.sub,
                email: payload.email,
                exp: payload.exp,
                algorithm: jwtConfig.algorithm,
            });
            return payload;
        }
        catch (error) {
            this.logger.error('Error verifying access token', {
                error: error.message,
                tokenLength: token.length,
            });
            throw new Error('Invalid or expired token');
        }
    }
    /**
     * Verify refresh token
     * @param token Refresh token
     * @returns Promise<RefreshToken | null> Refresh token entity or null
     */
    async verifyRefreshToken(token) {
        try {
            this.logger.debug('Verifying refresh token', {
                tokenLength: token.length,
            });
            const tokenHash = await this.passwordService.hashPassword(token);
            const refreshToken = await this.refreshTokenRepository.findOne({
                where: { tokenHash },
                relations: ['user'],
            });
            if (!refreshToken) {
                this.logger.warn('Refresh token not found');
                return null;
            }
            if (refreshToken.isRevoked) {
                this.logger.warn('Refresh token is revoked', {
                    tokenId: refreshToken.id,
                });
                return null;
            }
            if (refreshToken.expiresAt < new Date()) {
                this.logger.warn('Refresh token has expired', {
                    tokenId: refreshToken.id,
                    expiresAt: refreshToken.expiresAt,
                });
                return null;
            }
            this.logger.debug('Refresh token verified successfully', {
                tokenId: refreshToken.id,
                userId: refreshToken.userId,
            });
            return refreshToken;
        }
        catch (error) {
            this.logger.error('Error verifying refresh token', {
                error: error.message,
                tokenLength: token.length,
            });
            return null;
        }
    }
    /**
     * Revoke refresh token
     * @param tokenId Token ID
     * @returns Promise<boolean>
     */
    async revokeRefreshToken(tokenId) {
        try {
            this.logger.debug('Revoking refresh token', { tokenId });
            const result = await this.refreshTokenRepository.update({ id: tokenId }, { isRevoked: true });
            const success = result.affected > 0;
            this.logger.debug('Refresh token revocation result', {
                tokenId,
                success,
            });
            return success;
        }
        catch (error) {
            this.logger.error('Error revoking refresh token', {
                tokenId,
                error: error.message,
            });
            return false;
        }
    }
    /**
     * Revoke all refresh tokens for a user
     * @param userId User ID
     * @returns Promise<number> Number of tokens revoked
     */
    async revokeAllUserTokens(userId) {
        try {
            this.logger.debug('Revoking all user tokens', { userId });
            const result = await this.refreshTokenRepository.update({ userId, isRevoked: false }, { isRevoked: true });
            const revokedCount = result.affected || 0;
            this.logger.log('All user tokens revoked', {
                userId,
                revokedCount,
            });
            return revokedCount;
        }
        catch (error) {
            this.logger.error('Error revoking all user tokens', {
                userId,
                error: error.message,
            });
            return 0;
        }
    }
    /**
     * Clean up expired refresh tokens
     * @returns Promise<number> Number of tokens cleaned up
     */
    async cleanupExpiredTokens() {
        try {
            this.logger.debug('Cleaning up expired refresh tokens');
            const result = await this.refreshTokenRepository.delete({
                expiresAt: { $lt: new Date() },
            });
            const deletedCount = result.affected || 0;
            this.logger.log('Expired tokens cleaned up', {
                deletedCount,
            });
            return deletedCount;
        }
        catch (error) {
            this.logger.error('Error cleaning up expired tokens', {
                error: error.message,
            });
            return 0;
        }
    }
    /**
     * Get token statistics for a user
     * @param userId User ID
     * @returns Promise<Object> Token statistics
     */
    async getUserTokenStats(userId) {
        try {
            const tokens = await this.refreshTokenRepository.find({
                where: { userId },
            });
            const now = new Date();
            const stats = {
                total: tokens.length,
                active: 0,
                expired: 0,
                revoked: 0,
            };
            tokens.forEach(token => {
                if (token.isRevoked) {
                    stats.revoked++;
                }
                else if (token.expiresAt < now) {
                    stats.expired++;
                }
                else {
                    stats.active++;
                }
            });
            this.logger.debug('User token statistics', {
                userId,
                ...stats,
            });
            return stats;
        }
        catch (error) {
            this.logger.error('Error getting user token statistics', {
                userId,
                error: error.message,
            });
            return { total: 0, active: 0, expired: 0, revoked: 0 };
        }
    }
    /**
     * Parse expiration time string to seconds
     * @param expiresIn Expiration time string (e.g., '1h', '30m')
     * @returns number Expiration time in seconds
     */
    parseExpirationTime(expiresIn) {
        const match = expiresIn.match(/^(\d+)([smhd])$/);
        if (!match)
            return 3600; // Default to 1 hour
        const value = parseInt(match[1]);
        const unit = match[2];
        switch (unit) {
            case 's': return value;
            case 'm': return value * 60;
            case 'h': return value * 3600;
            case 'd': return value * 86400;
            default: return 3600;
        }
    }
};
exports.TokenService = TokenService;
exports.TokenService = TokenService = TokenService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(3, (0, typeorm_1.InjectRepository)(refresh_token_entity_1.RefreshToken)),
    __metadata("design:paramtypes", [typeof (_a = typeof jwt_1.JwtService !== "undefined" && jwt_1.JwtService) === "function" ? _a : Object, typeof (_b = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _b : Object, typeof (_c = typeof password_service_1.PasswordService !== "undefined" && password_service_1.PasswordService) === "function" ? _c : Object, typeof (_d = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _d : Object])
], TokenService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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