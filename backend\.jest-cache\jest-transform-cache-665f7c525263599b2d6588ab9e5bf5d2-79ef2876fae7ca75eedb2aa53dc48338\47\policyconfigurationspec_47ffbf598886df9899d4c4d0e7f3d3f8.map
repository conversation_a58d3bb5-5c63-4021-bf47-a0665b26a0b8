{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\policies\\__tests__\\policy-configuration.spec.ts", "mappings": ";;AAAA,kEAMiC;AACjC,8GAA6F;AAC7F,0GAAyF;AACzF,uGAAmG;AAEnG,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;IACnC,IAAI,QAAkB,CAAC;IACvB,IAAI,MAAc,CAAC;IACnB,IAAI,iBAA+B,CAAC;IAEpC,UAAU,CAAC,GAAG,EAAE;QACd,QAAQ,GAAG,iCAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QACzC,MAAM,GAAG,6BAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAEnC,iBAAiB,GAAG;YAClB,EAAE,EAAE,YAAY;YAChB,IAAI,EAAE,uBAAuB;YAC7B,WAAW,EAAE,8CAA8C;YAC3D,QAAQ,EAAE,mCAAY,CAAC,cAAc;YACrC,QAAQ,EAAE,kFAAkF;YAC5F,UAAU,EAAE;gBACV;oBACE,IAAI,EAAE,aAAa;oBACnB,IAAI,EAAE,wCAAiB,CAAC,MAAM;oBAC9B,WAAW,EAAE,mCAAmC;oBAChD,QAAQ,EAAE,IAAI;oBACd,YAAY,EAAE,CAAC;iBAChB;aACF;YACD,eAAe,EAAE,EAAE;YACnB,eAAe,EAAE,MAAM;YACvB,IAAI,EAAE,CAAC,gBAAgB,EAAE,aAAa,CAAC;SACxC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,MAAM,GAAG,0CAAmB,CAAC,MAAM,CAAC;gBACxC,QAAQ;gBACR,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7B,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,MAAM,GAAG,0CAAmB,CAAC,MAAM,CAAC;gBACxC,QAAQ;gBACR,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;YACjC,MAAM,CAAC,QAAQ,CAAC,wBAAwB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,QAAQ,CAAC,iCAAiC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9D,MAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC7C,MAAM,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC9C,MAAM,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC,WAAW,EAAE,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,MAAM,GAAG,0CAAmB,CAAC,MAAM,CAAC;gBACxC,QAAQ;gBACR,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,mCAAY,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9F,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,mCAAY,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,IAAI,MAA2B,CAAC;QAEhC,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,0CAAmB,CAAC,MAAM,CAAC;gBAClC,QAAQ;gBACR,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wBAAwB,EAAE,GAAG,EAAE;YAChC,MAAM,OAAO,GAAG;gBACd,wBAAwB,EAAE,IAAI;gBAC9B,iBAAiB,EAAE,GAAG;aACvB,CAAC;YAEF,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAEvC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,wBAAwB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,cAAc,GAAG;gBACrB,iBAAiB,EAAE,CAAC,CAAC;aACtB,CAAC;YAEF,MAAM,CAAC,GAAG,EAAE;gBACV,MAAM,CAAC,cAAc,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,qBAAqB,GAAG,MAAM,CAAC,QAAQ,CAAC,kBAAkB,CAAC;YAEjE,MAAM,CAAC,cAAc,CAAC,EAAE,wBAAwB,EAAE,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC;YAElE,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACvE,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,wBAAwB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,IAAI,MAA2B,CAAC;QAEhC,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,0CAAmB,CAAC,MAAM,CAAC;gBAClC,QAAQ;gBACR,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,YAAY,GAAG,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YACjD,MAAM,CAAC,eAAe,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;YAElD,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;YAC5D,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,YAAY,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QAC9E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,CAAC,eAAe,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;YAElD,MAAM,CAAC,GAAG,EAAE;gBACV,MAAM,CAAC,eAAe,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,CAAC,eAAe,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;YAElD,MAAM,CAAC,kBAAkB,CAAC,YAAY,EAAE,EAAE,IAAI,EAAE,kBAAkB,EAAE,EAAE,MAAM,CAAC,CAAC;YAE9E,MAAM,eAAe,GAAG,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,YAAY,CAAC,CAAC;YAC9E,MAAM,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,CAAC,GAAG,EAAE;gBACV,MAAM,CAAC,kBAAkB,CAAC,cAAc,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC,CAAC;YACzE,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,CAAC,eAAe,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;YAClD,MAAM,YAAY,GAAG,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YAEjD,MAAM,CAAC,kBAAkB,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YAEhD,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;YAC5D,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,YAAY,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC;QAChF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,CAAC,GAAG,EAAE;gBACV,MAAM,CAAC,kBAAkB,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,IAAI,MAA2B,CAAC;QAEhC,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,0CAAmB,CAAC,MAAM,CAAC;gBAClC,QAAQ;gBACR,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,EAAE,GAAG,iBAAiB,EAAE,QAAQ,EAAE,mCAAY,CAAC,cAAc,EAAE,CAAC;YACrF,MAAM,eAAe,GAAG;gBACtB,GAAG,iBAAiB;gBACpB,EAAE,EAAE,YAAY;gBAChB,QAAQ,EAAE,mCAAY,CAAC,gBAAgB;aACxC,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YAC7C,MAAM,CAAC,eAAe,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,aAAa,GAAG,MAAM,CAAC,0BAA0B,CAAC,mCAAY,CAAC,cAAc,CAAC,CAAC;YACrF,MAAM,gBAAgB,GAAG,MAAM,CAAC,0BAA0B,CAAC,mCAAY,CAAC,gBAAgB,CAAC,CAAC;YAE1F,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAChD,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACnD,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,mCAAY,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxF,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,mCAAY,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,eAAe,GAAG,MAAM,CAAC,0BAA0B,CAAC,mCAAY,CAAC,MAAM,CAAC,CAAC;YAC/E,MAAM,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;QAC9C,IAAI,MAA2B,CAAC;QAEhC,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,0CAAmB,CAAC,MAAM,CAAC;gBAClC,QAAQ;gBACR,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;YACH,MAAM,CAAC,eAAe,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,UAAU,GAAG,EAAE,WAAW,EAAE,EAAE,EAAE,CAAC;YACvC,MAAM,aAAa,GAAG,MAAM,CAAC,wBAAwB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;YAEhF,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,CAAC,aAAa,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC;gBACxC,KAAK,EAAE,oBAAoB;gBAC3B,QAAQ,EAAE,cAAc;gBACxB,KAAK,EAAE,EAAE;aACV,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,CAAC,GAAG,EAAE;gBACV,MAAM,CAAC,wBAAwB,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,CAAC,GAAG,EAAE;gBACV,MAAM,CAAC,wBAAwB,CAAC,cAAc,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC;YACtE,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,CAAC,GAAG,EAAE;gBACV,MAAM,CAAC,wBAAwB,CAAC,YAAY,EAAE,EAAE,WAAW,EAAE,SAAS,EAAE,CAAC,CAAC;YAC5E,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,kBAAkB,GAAiB;gBACvC,GAAG,iBAAiB;gBACpB,EAAE,EAAE,aAAa;gBACjB,QAAQ,EAAE,oKAAoK;gBAC9K,UAAU,EAAE;oBACV;wBACE,IAAI,EAAE,aAAa;wBACnB,IAAI,EAAE,wCAAiB,CAAC,MAAM;wBAC9B,WAAW,EAAE,cAAc;wBAC3B,QAAQ,EAAE,IAAI;qBACf;oBACD;wBACE,IAAI,EAAE,UAAU;wBAChB,IAAI,EAAE,wCAAiB,CAAC,UAAU;wBAClC,WAAW,EAAE,WAAW;wBACxB,QAAQ,EAAE,IAAI;qBACf;iBACF;aACF,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;YAEnD,MAAM,UAAU,GAAG,EAAE,WAAW,EAAE,CAAC,EAAE,QAAQ,EAAE,aAAa,EAAE,CAAC;YAC/D,MAAM,aAAa,GAAG,MAAM,CAAC,wBAAwB,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;YAEjF,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,IAAI,MAA2B,CAAC;QAEhC,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,0CAAmB,CAAC,MAAM,CAAC;gBAClC,QAAQ;gBACR,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,cAAc,GAAiB;gBACnC,GAAG,iBAAiB;gBACpB,EAAE,EAAE,iBAAiB;gBACrB,UAAU,EAAE,CAAC;wBACX,IAAI,EAAE,aAAa;wBACnB,IAAI,EAAE,wCAAiB,CAAC,MAAM;wBAC9B,WAAW,EAAE,kBAAkB;wBAC/B,QAAQ,EAAE,IAAI;qBACf,CAAC;aACH,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;YAE/C,MAAM,CAAC,GAAG,EAAE;gBACV,MAAM,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC,CAAC;YAC3E,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,UAAU,GAAiB;gBAC/B,GAAG,iBAAiB;gBACpB,EAAE,EAAE,aAAa;gBACjB,UAAU,EAAE,CAAC;wBACX,IAAI,EAAE,SAAS;wBACf,IAAI,EAAE,wCAAiB,CAAC,UAAU;wBAClC,WAAW,EAAE,cAAc;wBAC3B,QAAQ,EAAE,IAAI;qBACf,CAAC;aACH,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YAE3C,MAAM,CAAC,GAAG,EAAE;gBACV,MAAM,CAAC,wBAAwB,CAAC,aAAa,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC,CAAC;YAC5E,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;YAEhC,uBAAuB;YACvB,MAAM,CAAC,GAAG,EAAE;gBACV,MAAM,CAAC,wBAAwB,CAAC,aAAa,EAAE,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC,CAAC;YAC7E,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,aAAa,GAAiB;gBAClC,GAAG,iBAAiB;gBACpB,EAAE,EAAE,gBAAgB;gBACpB,UAAU,EAAE,CAAC;wBACX,IAAI,EAAE,YAAY;wBAClB,IAAI,EAAE,wCAAiB,CAAC,KAAK;wBAC7B,WAAW,EAAE,iBAAiB;wBAC9B,QAAQ,EAAE,IAAI;qBACf,CAAC;aACH,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;YAE9C,MAAM,CAAC,GAAG,EAAE;gBACV,MAAM,CAAC,wBAAwB,CAAC,gBAAgB,EAAE,EAAE,UAAU,EAAE,eAAe,EAAE,CAAC,CAAC;YACrF,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;YAEhC,0BAA0B;YAC1B,MAAM,CAAC,GAAG,EAAE;gBACV,MAAM,CAAC,wBAAwB,CAAC,gBAAgB,EAAE,EAAE,UAAU,EAAE,kBAAkB,EAAE,CAAC,CAAC;YACxF,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,YAAY,GAAiB;gBACjC,GAAG,iBAAiB;gBACpB,EAAE,EAAE,eAAe;gBACnB,UAAU,EAAE,CAAC;wBACX,IAAI,EAAE,WAAW;wBACjB,IAAI,EAAE,wCAAiB,CAAC,IAAI;wBAC5B,WAAW,EAAE,gBAAgB;wBAC7B,QAAQ,EAAE,IAAI;wBACd,OAAO,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;qBAC3C,CAAC;aACH,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YAE7C,MAAM,CAAC,GAAG,EAAE;gBACV,MAAM,CAAC,wBAAwB,CAAC,eAAe,EAAE,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAC,CAAC;YACpF,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;YAEhC,2BAA2B;YAC3B,MAAM,CAAC,GAAG,EAAE;gBACV,MAAM,CAAC,wBAAwB,CAAC,eAAe,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,CAAC;YAC7E,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,IAAI,MAA2B,CAAC;QAEhC,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,0CAAmB,CAAC,MAAM,CAAC;gBAClC,QAAQ;gBACR,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,eAAe,GAAG;gBACtB,qDAAqD;gBACrD,uGAAuG;gBACvG,sGAAsG;gBACtG,6DAA6D;aAC9D,CAAC;YAEF,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBAClC,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,iBAAiB,GAAG;gBACxB,cAAc;gBACd,IAAI;gBACJ,kBAAkB;gBAClB,uBAAuB;gBACvB,kBAAkB;gBAClB,sDAAsD;gBACtD,wBAAwB;aACzB,CAAC;YAEF,iBAAiB,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBACpC,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,IAAI,MAA2B,CAAC;QAEhC,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,0CAAmB,CAAC,MAAM,CAAC;gBAClC,QAAQ;gBACR,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,eAAe,GAAG,EAAE,GAAG,iBAAiB,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YAEzD,MAAM,CAAC,GAAG,EAAE;gBACV,MAAM,CAAC,eAAe,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,wBAAwB,GAAG;gBAC/B,GAAG,iBAAiB;gBACpB,UAAU,EAAE,CAAC;wBACX,IAAI,EAAE,EAAE;wBACR,IAAI,EAAE,wCAAiB,CAAC,MAAM;wBAC9B,WAAW,EAAE,eAAe;wBAC5B,QAAQ,EAAE,IAAI;qBACf,CAAC;aACH,CAAC;YAEF,MAAM,CAAC,GAAG,EAAE;gBACV,MAAM,CAAC,eAAe,CAAC,wBAAwB,EAAE,MAAM,CAAC,CAAC;YAC3D,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,4BAA4B,GAAG;gBACnC,GAAG,iBAAiB;gBACpB,QAAQ,EAAE,cAAc;aACzB,CAAC;YAEF,MAAM,CAAC,GAAG,EAAE;gBACV,MAAM,CAAC,eAAe,CAAC,4BAA4B,EAAE,MAAM,CAAC,CAAC;YAC/D,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;QACvB,IAAI,MAA2B,CAAC;QAEhC,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,0CAAmB,CAAC,MAAM,CAAC;gBAClC,QAAQ;gBACR,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;YAEpC,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9B,MAAM,CAAC,OAAO,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACjD,MAAM,CAAC,OAAO,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAClD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\policies\\__tests__\\policy-configuration.spec.ts"], "sourcesContent": ["import { \r\n  PolicyConfiguration, \r\n  RuleTemplate, \r\n  RuleCategory, \r\n  RuleParameterType,\r\n  PolicyConfigurationSettings\r\n} from '../policy-configuration';\r\nimport { TenantId } from '../../../../../shared-kernel/value-objects/tenant-id.value-object';\r\nimport { UserId } from '../../../../../shared-kernel/value-objects/user-id.value-object';\r\nimport { ValidationException } from '../../../../../shared-kernel/exceptions/validation.exception';\r\n\r\ndescribe('PolicyConfiguration', () => {\r\n  let tenantId: TenantId;\r\n  let userId: UserId;\r\n  let validRuleTemplate: RuleTemplate;\r\n\r\n  beforeEach(() => {\r\n    tenantId = TenantId.create('tenant-123');\r\n    userId = UserId.create('user-123');\r\n    \r\n    validRuleTemplate = {\r\n      id: 'template-1',\r\n      name: 'Failed Login Template',\r\n      description: 'Template for detecting failed login attempts',\r\n      category: RuleCategory.AUTHENTICATION,\r\n      template: '{\"field\":\"eventData.attempts\",\"operator\":\"greater_than\",\"value\":{{maxAttempts}}}',\r\n      parameters: [\r\n        {\r\n          name: 'maxAttempts',\r\n          type: RuleParameterType.NUMBER,\r\n          description: 'Maximum number of failed attempts',\r\n          required: true,\r\n          defaultValue: 5\r\n        }\r\n      ],\r\n      defaultPriority: 80,\r\n      defaultSeverity: 'HIGH',\r\n      tags: ['authentication', 'brute-force']\r\n    };\r\n  });\r\n\r\n  describe('creation', () => {\r\n    it('should create a policy configuration with defaults', () => {\r\n      const config = PolicyConfiguration.create({\r\n        tenantId,\r\n        createdBy: userId\r\n      });\r\n\r\n      expect(config).toBeDefined();\r\n      expect(config.tenantId).toBe(tenantId);\r\n      expect(config.createdBy).toBe(userId);\r\n      expect(config.settings).toBeDefined();\r\n      expect(config.ruleTemplates).toBeDefined();\r\n      expect(config.version).toBe('1.0.0');\r\n    });\r\n\r\n    it('should have default settings configured', () => {\r\n      const config = PolicyConfiguration.create({\r\n        tenantId,\r\n        createdBy: userId\r\n      });\r\n\r\n      const settings = config.settings;\r\n      expect(settings.enableAutomaticExecution).toBe(false);\r\n      expect(settings.requireApprovalForCriticalActions).toBe(true);\r\n      expect(settings.maxRulesPerPolicy).toBe(100);\r\n      expect(settings.auditRetentionDays).toBe(365);\r\n      expect(settings.notificationSettings).toBeDefined();\r\n    });\r\n\r\n    it('should include default rule templates', () => {\r\n      const config = PolicyConfiguration.create({\r\n        tenantId,\r\n        createdBy: userId\r\n      });\r\n\r\n      expect(config.ruleTemplates.length).toBeGreaterThan(0);\r\n      expect(config.ruleTemplates.some(t => t.category === RuleCategory.AUTHENTICATION)).toBe(true);\r\n      expect(config.ruleTemplates.some(t => t.category === RuleCategory.NETWORK_SECURITY)).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('settings management', () => {\r\n    let config: PolicyConfiguration;\r\n\r\n    beforeEach(() => {\r\n      config = PolicyConfiguration.create({\r\n        tenantId,\r\n        createdBy: userId\r\n      });\r\n    });\r\n\r\n    it('should update settings', () => {\r\n      const updates = {\r\n        enableAutomaticExecution: true,\r\n        maxRulesPerPolicy: 200\r\n      };\r\n\r\n      config.updateSettings(updates, userId);\r\n\r\n      expect(config.settings.enableAutomaticExecution).toBe(true);\r\n      expect(config.settings.maxRulesPerPolicy).toBe(200);\r\n      expect(config.lastModifiedBy).toBe(userId);\r\n    });\r\n\r\n    it('should validate settings updates', () => {\r\n      const invalidUpdates = {\r\n        maxRulesPerPolicy: -1\r\n      };\r\n\r\n      expect(() => {\r\n        config.updateSettings(invalidUpdates, userId);\r\n      }).toThrow(ValidationException);\r\n    });\r\n\r\n    it('should preserve existing settings when updating', () => {\r\n      const originalRetentionDays = config.settings.auditRetentionDays;\r\n      \r\n      config.updateSettings({ enableAutomaticExecution: true }, userId);\r\n\r\n      expect(config.settings.auditRetentionDays).toBe(originalRetentionDays);\r\n      expect(config.settings.enableAutomaticExecution).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('rule template management', () => {\r\n    let config: PolicyConfiguration;\r\n\r\n    beforeEach(() => {\r\n      config = PolicyConfiguration.create({\r\n        tenantId,\r\n        createdBy: userId\r\n      });\r\n    });\r\n\r\n    it('should add a new rule template', () => {\r\n      const initialCount = config.ruleTemplates.length;\r\n      config.addRuleTemplate(validRuleTemplate, userId);\r\n\r\n      expect(config.ruleTemplates).toHaveLength(initialCount + 1);\r\n      expect(config.ruleTemplates.find(t => t.id === 'template-1')).toBeDefined();\r\n    });\r\n\r\n    it('should throw error when adding duplicate template ID', () => {\r\n      config.addRuleTemplate(validRuleTemplate, userId);\r\n\r\n      expect(() => {\r\n        config.addRuleTemplate(validRuleTemplate, userId);\r\n      }).toThrow(ValidationException);\r\n    });\r\n\r\n    it('should update an existing rule template', () => {\r\n      config.addRuleTemplate(validRuleTemplate, userId);\r\n      \r\n      config.updateRuleTemplate('template-1', { name: 'Updated Template' }, userId);\r\n\r\n      const updatedTemplate = config.ruleTemplates.find(t => t.id === 'template-1');\r\n      expect(updatedTemplate?.name).toBe('Updated Template');\r\n    });\r\n\r\n    it('should throw error when updating non-existent template', () => {\r\n      expect(() => {\r\n        config.updateRuleTemplate('non-existent', { name: 'Updated' }, userId);\r\n      }).toThrow(ValidationException);\r\n    });\r\n\r\n    it('should remove a rule template', () => {\r\n      config.addRuleTemplate(validRuleTemplate, userId);\r\n      const initialCount = config.ruleTemplates.length;\r\n\r\n      config.removeRuleTemplate('template-1', userId);\r\n\r\n      expect(config.ruleTemplates).toHaveLength(initialCount - 1);\r\n      expect(config.ruleTemplates.find(t => t.id === 'template-1')).toBeUndefined();\r\n    });\r\n\r\n    it('should throw error when removing non-existent template', () => {\r\n      expect(() => {\r\n        config.removeRuleTemplate('non-existent', userId);\r\n      }).toThrow(ValidationException);\r\n    });\r\n  });\r\n\r\n  describe('rule template filtering', () => {\r\n    let config: PolicyConfiguration;\r\n\r\n    beforeEach(() => {\r\n      config = PolicyConfiguration.create({\r\n        tenantId,\r\n        createdBy: userId\r\n      });\r\n      \r\n      const authTemplate = { ...validRuleTemplate, category: RuleCategory.AUTHENTICATION };\r\n      const networkTemplate = { \r\n        ...validRuleTemplate, \r\n        id: 'template-2', \r\n        category: RuleCategory.NETWORK_SECURITY \r\n      };\r\n      \r\n      config.addRuleTemplate(authTemplate, userId);\r\n      config.addRuleTemplate(networkTemplate, userId);\r\n    });\r\n\r\n    it('should filter templates by category', () => {\r\n      const authTemplates = config.getRuleTemplatesByCategory(RuleCategory.AUTHENTICATION);\r\n      const networkTemplates = config.getRuleTemplatesByCategory(RuleCategory.NETWORK_SECURITY);\r\n\r\n      expect(authTemplates.length).toBeGreaterThan(0);\r\n      expect(networkTemplates.length).toBeGreaterThan(0);\r\n      expect(authTemplates.every(t => t.category === RuleCategory.AUTHENTICATION)).toBe(true);\r\n      expect(networkTemplates.every(t => t.category === RuleCategory.NETWORK_SECURITY)).toBe(true);\r\n    });\r\n\r\n    it('should return empty array for category with no templates', () => {\r\n      const customTemplates = config.getRuleTemplatesByCategory(RuleCategory.CUSTOM);\r\n      expect(customTemplates).toHaveLength(0);\r\n    });\r\n  });\r\n\r\n  describe('rule generation from templates', () => {\r\n    let config: PolicyConfiguration;\r\n\r\n    beforeEach(() => {\r\n      config = PolicyConfiguration.create({\r\n        tenantId,\r\n        createdBy: userId\r\n      });\r\n      config.addRuleTemplate(validRuleTemplate, userId);\r\n    });\r\n\r\n    it('should generate rule from template with valid parameters', () => {\r\n      const parameters = { maxAttempts: 10 };\r\n      const ruleCondition = config.generateRuleFromTemplate('template-1', parameters);\r\n\r\n      expect(ruleCondition).toBeDefined();\r\n      expect(ruleCondition).toContain('10');\r\n      expect(JSON.parse(ruleCondition)).toEqual({\r\n        field: 'eventData.attempts',\r\n        operator: 'greater_than',\r\n        value: 10\r\n      });\r\n    });\r\n\r\n    it('should throw error for missing required parameters', () => {\r\n      expect(() => {\r\n        config.generateRuleFromTemplate('template-1', {});\r\n      }).toThrow(ValidationException);\r\n    });\r\n\r\n    it('should throw error for non-existent template', () => {\r\n      expect(() => {\r\n        config.generateRuleFromTemplate('non-existent', { maxAttempts: 5 });\r\n      }).toThrow(ValidationException);\r\n    });\r\n\r\n    it('should validate parameter types', () => {\r\n      expect(() => {\r\n        config.generateRuleFromTemplate('template-1', { maxAttempts: 'invalid' });\r\n      }).toThrow(ValidationException);\r\n    });\r\n\r\n    it('should handle multiple parameters', () => {\r\n      const multiParamTemplate: RuleTemplate = {\r\n        ...validRuleTemplate,\r\n        id: 'multi-param',\r\n        template: '{\"and\":[{\"field\":\"eventData.attempts\",\"operator\":\"greater_than\",\"value\":{{maxAttempts}}},{\"field\":\"eventData.sourceIp\",\"operator\":\"equals\",\"value\":{{sourceIp}}}]}',\r\n        parameters: [\r\n          {\r\n            name: 'maxAttempts',\r\n            type: RuleParameterType.NUMBER,\r\n            description: 'Max attempts',\r\n            required: true\r\n          },\r\n          {\r\n            name: 'sourceIp',\r\n            type: RuleParameterType.IP_ADDRESS,\r\n            description: 'Source IP',\r\n            required: true\r\n          }\r\n        ]\r\n      };\r\n\r\n      config.addRuleTemplate(multiParamTemplate, userId);\r\n\r\n      const parameters = { maxAttempts: 5, sourceIp: '***********' };\r\n      const ruleCondition = config.generateRuleFromTemplate('multi-param', parameters);\r\n\r\n      const parsed = JSON.parse(ruleCondition);\r\n      expect(parsed.and).toHaveLength(2);\r\n      expect(parsed.and[0].value).toBe(5);\r\n      expect(parsed.and[1].value).toBe('***********');\r\n    });\r\n  });\r\n\r\n  describe('parameter validation', () => {\r\n    let config: PolicyConfiguration;\r\n\r\n    beforeEach(() => {\r\n      config = PolicyConfiguration.create({\r\n        tenantId,\r\n        createdBy: userId\r\n      });\r\n    });\r\n\r\n    it('should validate string parameters', () => {\r\n      const stringTemplate: RuleTemplate = {\r\n        ...validRuleTemplate,\r\n        id: 'string-template',\r\n        parameters: [{\r\n          name: 'stringParam',\r\n          type: RuleParameterType.STRING,\r\n          description: 'String parameter',\r\n          required: true\r\n        }]\r\n      };\r\n\r\n      config.addRuleTemplate(stringTemplate, userId);\r\n\r\n      expect(() => {\r\n        config.generateRuleFromTemplate('string-template', { stringParam: 123 });\r\n      }).toThrow(ValidationException);\r\n    });\r\n\r\n    it('should validate IP address parameters', () => {\r\n      const ipTemplate: RuleTemplate = {\r\n        ...validRuleTemplate,\r\n        id: 'ip-template',\r\n        parameters: [{\r\n          name: 'ipParam',\r\n          type: RuleParameterType.IP_ADDRESS,\r\n          description: 'IP parameter',\r\n          required: true\r\n        }]\r\n      };\r\n\r\n      config.addRuleTemplate(ipTemplate, userId);\r\n\r\n      expect(() => {\r\n        config.generateRuleFromTemplate('ip-template', { ipParam: 'invalid-ip' });\r\n      }).toThrow(ValidationException);\r\n\r\n      // Valid IP should work\r\n      expect(() => {\r\n        config.generateRuleFromTemplate('ip-template', { ipParam: '***********' });\r\n      }).not.toThrow();\r\n    });\r\n\r\n    it('should validate email parameters', () => {\r\n      const emailTemplate: RuleTemplate = {\r\n        ...validRuleTemplate,\r\n        id: 'email-template',\r\n        parameters: [{\r\n          name: 'emailParam',\r\n          type: RuleParameterType.EMAIL,\r\n          description: 'Email parameter',\r\n          required: true\r\n        }]\r\n      };\r\n\r\n      config.addRuleTemplate(emailTemplate, userId);\r\n\r\n      expect(() => {\r\n        config.generateRuleFromTemplate('email-template', { emailParam: 'invalid-email' });\r\n      }).toThrow(ValidationException);\r\n\r\n      // Valid email should work\r\n      expect(() => {\r\n        config.generateRuleFromTemplate('email-template', { emailParam: '<EMAIL>' });\r\n      }).not.toThrow();\r\n    });\r\n\r\n    it('should validate enum parameters', () => {\r\n      const enumTemplate: RuleTemplate = {\r\n        ...validRuleTemplate,\r\n        id: 'enum-template',\r\n        parameters: [{\r\n          name: 'enumParam',\r\n          type: RuleParameterType.ENUM,\r\n          description: 'Enum parameter',\r\n          required: true,\r\n          options: ['option1', 'option2', 'option3']\r\n        }]\r\n      };\r\n\r\n      config.addRuleTemplate(enumTemplate, userId);\r\n\r\n      expect(() => {\r\n        config.generateRuleFromTemplate('enum-template', { enumParam: 'invalid-option' });\r\n      }).toThrow(ValidationException);\r\n\r\n      // Valid option should work\r\n      expect(() => {\r\n        config.generateRuleFromTemplate('enum-template', { enumParam: 'option1' });\r\n      }).not.toThrow();\r\n    });\r\n  });\r\n\r\n  describe('rule condition validation', () => {\r\n    let config: PolicyConfiguration;\r\n\r\n    beforeEach(() => {\r\n      config = PolicyConfiguration.create({\r\n        tenantId,\r\n        createdBy: userId\r\n      });\r\n    });\r\n\r\n    it('should validate valid rule conditions', () => {\r\n      const validConditions = [\r\n        '{\"field\":\"test\",\"operator\":\"equals\",\"value\":\"test\"}',\r\n        '{\"and\":[{\"field\":\"a\",\"operator\":\"equals\",\"value\":\"1\"},{\"field\":\"b\",\"operator\":\"equals\",\"value\":\"2\"}]}',\r\n        '{\"or\":[{\"field\":\"a\",\"operator\":\"equals\",\"value\":\"1\"},{\"field\":\"b\",\"operator\":\"equals\",\"value\":\"2\"}]}',\r\n        '{\"not\":{\"field\":\"test\",\"operator\":\"equals\",\"value\":\"test\"}}'\r\n      ];\r\n\r\n      validConditions.forEach(condition => {\r\n        expect(config.validateRuleCondition(condition)).toBe(true);\r\n      });\r\n    });\r\n\r\n    it('should reject invalid rule conditions', () => {\r\n      const invalidConditions = [\r\n        'invalid json',\r\n        '{}',\r\n        '{\"field\":\"test\"}',\r\n        '{\"operator\":\"equals\"}',\r\n        '{\"value\":\"test\"}',\r\n        '{\"field\":\"test\",\"operator\":\"invalid\",\"value\":\"test\"}',\r\n        '{\"and\":\"not an array\"}'\r\n      ];\r\n\r\n      invalidConditions.forEach(condition => {\r\n        expect(config.validateRuleCondition(condition)).toBe(false);\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('template validation', () => {\r\n    let config: PolicyConfiguration;\r\n\r\n    beforeEach(() => {\r\n      config = PolicyConfiguration.create({\r\n        tenantId,\r\n        createdBy: userId\r\n      });\r\n    });\r\n\r\n    it('should validate template properties', () => {\r\n      const invalidTemplate = { ...validRuleTemplate, id: '' };\r\n\r\n      expect(() => {\r\n        config.addRuleTemplate(invalidTemplate, userId);\r\n      }).toThrow(ValidationException);\r\n    });\r\n\r\n    it('should validate template parameters', () => {\r\n      const templateWithInvalidParam = {\r\n        ...validRuleTemplate,\r\n        parameters: [{\r\n          name: '',\r\n          type: RuleParameterType.STRING,\r\n          description: 'Invalid param',\r\n          required: true\r\n        }]\r\n      };\r\n\r\n      expect(() => {\r\n        config.addRuleTemplate(templateWithInvalidParam, userId);\r\n      }).toThrow(ValidationException);\r\n    });\r\n\r\n    it('should validate template condition JSON', () => {\r\n      const templateWithInvalidCondition = {\r\n        ...validRuleTemplate,\r\n        template: 'invalid json'\r\n      };\r\n\r\n      expect(() => {\r\n        config.addRuleTemplate(templateWithInvalidCondition, userId);\r\n      }).toThrow(ValidationException);\r\n    });\r\n  });\r\n\r\n  describe('metrics', () => {\r\n    let config: PolicyConfiguration;\r\n\r\n    beforeEach(() => {\r\n      config = PolicyConfiguration.create({\r\n        tenantId,\r\n        createdBy: userId\r\n      });\r\n    });\r\n\r\n    it('should return policy metrics', () => {\r\n      const metrics = config.getMetrics();\r\n\r\n      expect(metrics).toBeDefined();\r\n      expect(typeof metrics.totalPolicies).toBe('number');\r\n      expect(typeof metrics.activePolicies).toBe('number');\r\n      expect(typeof metrics.totalRules).toBe('number');\r\n      expect(typeof metrics.activeRules).toBe('number');\r\n      expect(Array.isArray(metrics.topViolatedRules)).toBe(true);\r\n    });\r\n  });\r\n});"], "version": 3}