edb1c8598a6b170975d256aed7436671
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const common_1 = require("@nestjs/common");
const threat_intelligence_service_1 = require("../threat-intelligence.service");
const threat_intelligence_entity_1 = require("../../../domain/entities/threat-intelligence.entity");
const indicator_of_compromise_entity_1 = require("../../../domain/entities/indicator-of-compromise.entity");
const threat_actor_entity_1 = require("../../../domain/entities/threat-actor.entity");
const threat_campaign_entity_1 = require("../../../domain/entities/threat-campaign.entity");
const logger_service_1 = require("../../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../../infrastructure/logging/audit/audit.service");
const notification_service_1 = require("../../../../../infrastructure/notification/notification.service");
describe('ThreatIntelligenceService', () => {
    let service;
    let threatRepository;
    let iocRepository;
    let actorRepository;
    let campaignRepository;
    let loggerService;
    let auditService;
    let notificationService;
    let configService;
    const mockThreatIntelligence = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        title: 'Test Threat Intelligence',
        description: 'Test description',
        threatType: threat_intelligence_entity_1.ThreatType.APT,
        severity: threat_intelligence_entity_1.ThreatSeverity.HIGH,
        confidence: threat_intelligence_entity_1.ThreatConfidence.HIGH,
        status: threat_intelligence_entity_1.ThreatStatus.ACTIVE,
        firstSeen: new Date('2023-01-01'),
        dataSource: {
            name: 'Test Source',
            type: 'commercial',
            reliability: 'A',
            url: 'https://test.com',
            lastUpdated: new Date(),
            confidence: threat_intelligence_entity_1.ThreatConfidence.HIGH,
        },
        observationCount: 0,
        isIoc: false,
        isAttributed: false,
        calculateRiskScore: jest.fn().mockReturnValue(7.5),
        recordObservation: jest.fn(),
    };
    beforeEach(async () => {
        const mockRepository = {
            create: jest.fn(),
            save: jest.fn(),
            findOne: jest.fn(),
            find: jest.fn(),
            remove: jest.fn(),
            count: jest.fn(),
            createQueryBuilder: jest.fn(),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                threat_intelligence_service_1.ThreatIntelligenceService,
                {
                    provide: (0, typeorm_1.getRepositoryToken)(threat_intelligence_entity_1.ThreatIntelligence),
                    useValue: mockRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(indicator_of_compromise_entity_1.IndicatorOfCompromise),
                    useValue: mockRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(threat_actor_entity_1.ThreatActor),
                    useValue: mockRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(threat_campaign_entity_1.ThreatCampaign),
                    useValue: mockRepository,
                },
                {
                    provide: logger_service_1.LoggerService,
                    useValue: {
                        error: jest.fn(),
                        warn: jest.fn(),
                        log: jest.fn(),
                        debug: jest.fn(),
                    },
                },
                {
                    provide: audit_service_1.AuditService,
                    useValue: {
                        logUserAction: jest.fn(),
                        logSecurityEvent: jest.fn(),
                    },
                },
                {
                    provide: notification_service_1.NotificationService,
                    useValue: {
                        sendCriticalThreatAlert: jest.fn(),
                        sendUserNotification: jest.fn(),
                        sendRoleNotification: jest.fn(),
                    },
                },
                {
                    provide: config_1.ConfigService,
                    useValue: {
                        get: jest.fn(),
                    },
                },
            ],
        }).compile();
        service = module.get(threat_intelligence_service_1.ThreatIntelligenceService);
        threatRepository = module.get((0, typeorm_1.getRepositoryToken)(threat_intelligence_entity_1.ThreatIntelligence));
        iocRepository = module.get((0, typeorm_1.getRepositoryToken)(indicator_of_compromise_entity_1.IndicatorOfCompromise));
        actorRepository = module.get((0, typeorm_1.getRepositoryToken)(threat_actor_entity_1.ThreatActor));
        campaignRepository = module.get((0, typeorm_1.getRepositoryToken)(threat_campaign_entity_1.ThreatCampaign));
        loggerService = module.get(logger_service_1.LoggerService);
        auditService = module.get(audit_service_1.AuditService);
        notificationService = module.get(notification_service_1.NotificationService);
        configService = module.get(config_1.ConfigService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('createThreatIntelligence', () => {
        it('should create threat intelligence successfully', async () => {
            const createData = {
                title: 'New Threat',
                description: 'New threat description',
                threatType: threat_intelligence_entity_1.ThreatType.MALWARE,
                severity: threat_intelligence_entity_1.ThreatSeverity.MEDIUM,
                firstSeen: new Date(),
                dataSource: mockThreatIntelligence.dataSource,
            };
            threatRepository.findOne.mockResolvedValue(null);
            threatRepository.create.mockReturnValue(mockThreatIntelligence);
            threatRepository.save.mockResolvedValue(mockThreatIntelligence);
            const result = await service.createThreatIntelligence(createData, 'user123');
            expect(threatRepository.findOne).toHaveBeenCalledWith({
                where: {
                    title: createData.title,
                    dataSource: createData.dataSource,
                },
            });
            expect(threatRepository.create).toHaveBeenCalledWith(expect.objectContaining({
                ...createData,
                status: threat_intelligence_entity_1.ThreatStatus.ACTIVE,
                confidence: threat_intelligence_entity_1.ThreatConfidence.MEDIUM,
                severity: threat_intelligence_entity_1.ThreatSeverity.MEDIUM,
                observationCount: 0,
                isIoc: false,
                isAttributed: false,
            }));
            expect(threatRepository.save).toHaveBeenCalled();
            expect(auditService.logUserAction).toHaveBeenCalledWith('user123', 'create', 'threat_intelligence', mockThreatIntelligence.id, expect.any(Object));
            expect(result).toEqual(mockThreatIntelligence);
        });
        it('should throw ConflictException for duplicate threat intelligence', async () => {
            const createData = {
                title: 'Existing Threat',
                description: 'Description',
                threatType: threat_intelligence_entity_1.ThreatType.MALWARE,
                firstSeen: new Date(),
                dataSource: mockThreatIntelligence.dataSource,
            };
            threatRepository.findOne.mockResolvedValue(mockThreatIntelligence);
            await expect(service.createThreatIntelligence(createData, 'user123'))
                .rejects.toThrow(common_1.ConflictException);
            expect(threatRepository.create).not.toHaveBeenCalled();
            expect(threatRepository.save).not.toHaveBeenCalled();
        });
        it('should send critical threat alert for critical severity', async () => {
            const createData = {
                title: 'Critical Threat',
                description: 'Critical threat description',
                threatType: threat_intelligence_entity_1.ThreatType.APT,
                severity: threat_intelligence_entity_1.ThreatSeverity.CRITICAL,
                firstSeen: new Date(),
                dataSource: mockThreatIntelligence.dataSource,
            };
            const criticalThreat = {
                ...mockThreatIntelligence,
                severity: threat_intelligence_entity_1.ThreatSeverity.CRITICAL,
            };
            threatRepository.findOne.mockResolvedValue(null);
            threatRepository.create.mockReturnValue(criticalThreat);
            threatRepository.save.mockResolvedValue(criticalThreat);
            await service.createThreatIntelligence(createData, 'user123');
            expect(notificationService.sendCriticalThreatAlert).toHaveBeenCalledWith({
                threatId: criticalThreat.id,
                title: criticalThreat.title,
                threatType: criticalThreat.threatType,
                severity: criticalThreat.severity,
                dataSource: criticalThreat.dataSource.name,
            });
        });
    });
    describe('getThreatIntelligenceById', () => {
        it('should return threat intelligence by ID', async () => {
            threatRepository.findOne.mockResolvedValue(mockThreatIntelligence);
            const result = await service.getThreatIntelligenceById('123e4567-e89b-12d3-a456-426614174000');
            expect(threatRepository.findOne).toHaveBeenCalledWith({
                where: { id: '123e4567-e89b-12d3-a456-426614174000' },
                relations: ['indicators', 'threatActor', 'threatCampaign'],
            });
            expect(result).toEqual(mockThreatIntelligence);
        });
        it('should throw NotFoundException when threat intelligence not found', async () => {
            threatRepository.findOne.mockResolvedValue(null);
            await expect(service.getThreatIntelligenceById('nonexistent'))
                .rejects.toThrow(common_1.NotFoundException);
        });
    });
    describe('searchThreatIntelligence', () => {
        it('should search threat intelligence with criteria', async () => {
            const mockQueryBuilder = {
                leftJoinAndSelect: jest.fn().mockReturnThis(),
                andWhere: jest.fn().mockReturnThis(),
                orderBy: jest.fn().mockReturnThis(),
                addOrderBy: jest.fn().mockReturnThis(),
                skip: jest.fn().mockReturnThis(),
                take: jest.fn().mockReturnThis(),
                getManyAndCount: jest.fn().mockResolvedValue([[mockThreatIntelligence], 1]),
            };
            threatRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
            const criteria = {
                page: 1,
                limit: 10,
                threatTypes: [threat_intelligence_entity_1.ThreatType.APT],
                severities: [threat_intelligence_entity_1.ThreatSeverity.HIGH],
                searchText: 'test',
            };
            const result = await service.searchThreatIntelligence(criteria);
            expect(threatRepository.createQueryBuilder).toHaveBeenCalledWith('threat');
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('threat.threatType IN (:...threatTypes)', { threatTypes: [threat_intelligence_entity_1.ThreatType.APT] });
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('threat.severity IN (:...severities)', { severities: [threat_intelligence_entity_1.ThreatSeverity.HIGH] });
            expect(result).toEqual({
                threats: [mockThreatIntelligence],
                total: 1,
                page: 1,
                totalPages: 1,
            });
        });
        it('should handle empty search results', async () => {
            const mockQueryBuilder = {
                leftJoinAndSelect: jest.fn().mockReturnThis(),
                andWhere: jest.fn().mockReturnThis(),
                orderBy: jest.fn().mockReturnThis(),
                addOrderBy: jest.fn().mockReturnThis(),
                skip: jest.fn().mockReturnThis(),
                take: jest.fn().mockReturnThis(),
                getManyAndCount: jest.fn().mockResolvedValue([[], 0]),
            };
            threatRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
            const result = await service.searchThreatIntelligence({ page: 1, limit: 10 });
            expect(result).toEqual({
                threats: [],
                total: 0,
                page: 1,
                totalPages: 0,
            });
        });
    });
    describe('updateThreatIntelligence', () => {
        it('should update threat intelligence successfully', async () => {
            const updateData = {
                title: 'Updated Title',
                severity: threat_intelligence_entity_1.ThreatSeverity.CRITICAL,
            };
            const existingThreat = { ...mockThreatIntelligence };
            const updatedThreat = { ...existingThreat, ...updateData };
            threatRepository.findOne.mockResolvedValue(existingThreat);
            threatRepository.save.mockResolvedValue(updatedThreat);
            const result = await service.updateThreatIntelligence('123e4567-e89b-12d3-a456-426614174000', updateData, 'user123');
            expect(threatRepository.save).toHaveBeenCalledWith(expect.objectContaining(updateData));
            expect(auditService.logUserAction).toHaveBeenCalledWith('user123', 'update', 'threat_intelligence', '123e4567-e89b-12d3-a456-426614174000', expect.objectContaining({
                title: existingThreat.title,
                changes: expect.any(Object),
            }));
            expect(result).toEqual(updatedThreat);
        });
        it('should recalculate risk score when relevant fields change', async () => {
            const updateData = {
                severity: threat_intelligence_entity_1.ThreatSeverity.CRITICAL,
                confidence: threat_intelligence_entity_1.ThreatConfidence.HIGH,
            };
            const existingThreat = {
                ...mockThreatIntelligence,
                calculateRiskScore: jest.fn().mockReturnValue(9.0),
            };
            threatRepository.findOne.mockResolvedValue(existingThreat);
            threatRepository.save.mockResolvedValue(existingThreat);
            await service.updateThreatIntelligence('123e4567-e89b-12d3-a456-426614174000', updateData, 'user123');
            expect(existingThreat.calculateRiskScore).toHaveBeenCalled();
        });
    });
    describe('deleteThreatIntelligence', () => {
        it('should delete threat intelligence successfully', async () => {
            threatRepository.findOne.mockResolvedValue(mockThreatIntelligence);
            threatRepository.remove.mockResolvedValue(mockThreatIntelligence);
            await service.deleteThreatIntelligence('123e4567-e89b-12d3-a456-426614174000', 'user123');
            expect(threatRepository.remove).toHaveBeenCalledWith(mockThreatIntelligence);
            expect(auditService.logUserAction).toHaveBeenCalledWith('user123', 'delete', 'threat_intelligence', '123e4567-e89b-12d3-a456-426614174000', expect.objectContaining({
                title: mockThreatIntelligence.title,
                threatType: mockThreatIntelligence.threatType,
            }));
        });
    });
    describe('recordObservation', () => {
        it('should record observation and update threat intelligence', async () => {
            const threat = {
                ...mockThreatIntelligence,
                recordObservation: jest.fn(),
                calculateRiskScore: jest.fn().mockReturnValue(8.0),
            };
            threatRepository.findOne.mockResolvedValue(threat);
            threatRepository.save.mockResolvedValue(threat);
            const context = { source: 'test', timestamp: new Date() };
            const result = await service.recordObservation('123e4567-e89b-12d3-a456-426614174000', context);
            expect(threat.recordObservation).toHaveBeenCalled();
            expect(threat.calculateRiskScore).toHaveBeenCalled();
            expect(threatRepository.save).toHaveBeenCalledWith(threat);
            expect(result).toEqual(threat);
        });
    });
    describe('getDashboardData', () => {
        it('should return dashboard data', async () => {
            threatRepository.count
                .mockResolvedValueOnce(100) // total
                .mockResolvedValueOnce(80) // active
                .mockResolvedValueOnce(15) // critical
                .mockResolvedValueOnce(5); // recent
            // Mock private methods by spying on the service
            const getThreatTypeDistributionSpy = jest.spyOn(service, 'getThreatTypeDistribution')
                .mockResolvedValue([{ type: 'apt', count: 25 }]);
            const getTopThreatActorsSpy = jest.spyOn(service, 'getTopThreatActors')
                .mockResolvedValue([{ name: 'APT29', threatCount: 10 }]);
            const result = await service.getDashboardData();
            expect(result).toEqual({
                summary: {
                    total: 100,
                    active: 80,
                    critical: 15,
                    recent: 5,
                },
                distribution: {
                    threatTypes: [{ type: 'apt', count: 25 }],
                    topActors: [{ name: 'APT29', threatCount: 10 }],
                },
                timestamp: expect.any(Date),
            });
            getThreatTypeDistributionSpy.mockRestore();
            getTopThreatActorsSpy.mockRestore();
        });
    });
    describe('error handling', () => {
        it('should handle database errors gracefully', async () => {
            const createData = {
                title: 'Test Threat',
                description: 'Description',
                threatType: threat_intelligence_entity_1.ThreatType.MALWARE,
                firstSeen: new Date(),
                dataSource: mockThreatIntelligence.dataSource,
            };
            threatRepository.findOne.mockResolvedValue(null);
            threatRepository.create.mockReturnValue(mockThreatIntelligence);
            threatRepository.save.mockRejectedValue(new Error('Database error'));
            await expect(service.createThreatIntelligence(createData, 'user123'))
                .rejects.toThrow('Database error');
            expect(loggerService.error).toHaveBeenCalledWith('Failed to create threat intelligence', expect.objectContaining({
                error: 'Database error',
                userId: 'user123',
            }));
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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