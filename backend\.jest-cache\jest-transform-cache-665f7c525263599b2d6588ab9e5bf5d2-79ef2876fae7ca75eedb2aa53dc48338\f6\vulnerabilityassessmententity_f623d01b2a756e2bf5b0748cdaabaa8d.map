{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\domain\\entities\\vulnerability-assessment.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCASiB;AACjB,iEAAuD;AACvD,2EAAgE;AAChE,yFAA+E;AAE/E;;;GAGG;AASI,IAAM,uBAAuB,GAA7B,MAAM,uBAAuB;IA2NlC;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,MAAM,KAAK,aAAa,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,MAAM,KAAK,UAAU,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QACzD,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,eAAe,IAAI,EAAE,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAe;QACpB,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC;QACzB,IAAI,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5B,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAG,MAAM,CAAC;QACzC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAkB,EAAE,QAAiB;QAC1C,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC;QACjC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,aAAqB;QACvC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,0BAA0B,GAAG,aAAa,CAAC;QAChD,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,aAAqB;QAC9B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,2BAA2B,GAAG,aAAa,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;SAC5B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO;YACL,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,wBAAwB,EAAE,IAAI,CAAC,wBAAwB;YACvD,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACrC,CAAC;IACJ,CAAC;CACF,CAAA;AA/XY,0DAAuB;AAElC;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;mDACpB;AAMX;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;gEAC3B;AAMxB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;wDAC1C;AAMjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uDAC1C;AAUhB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,aAAa,CAAC;QAC3D,OAAO,EAAE,QAAQ;KAClB,CAAC;;+DACqE;AAUvE;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,SAAS,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,CAAC;QACzD,OAAO,EAAE,SAAS;KACnB,CAAC;;uDAC2D;AAU7D;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC;QACnD,QAAQ,EAAE,IAAI;KACf,CAAC;;iEACiE;AAMnE;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,qBAAqB,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kEACtE;AAM3B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,qBAAqB,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kEACtE;AAU3B;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC;QACzC,QAAQ,EAAE,IAAI;KACf,CAAC;;iEACsD;AAMxD;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;yDACvB;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,qBAAqB,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mEAC1C;AAM5B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;+DAC1C;AAMxB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;gEAC1C;AAMzB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,2BAA2B,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;yEAC1C;AAMlC;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,uBAAuB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qEACzC;AAMhC;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,uBAAuB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qEACzC;AAMhC;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;gEAC3C;AAMxB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;gEAC7B;AAMzB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,8BAA8B,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;2EAC3C;AAMpC;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;+DAC7B;AAMxB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,+BAA+B,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4EAC3C;AAMrC;IADC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4DACN;AAMrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;0DACrB;AAMrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,qBAAqB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAClD,MAAM,oBAAN,MAAM;mEAAc;AAMzC;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAC/B,MAAM,oBAAN,MAAM;yDAAc;AAM/B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;2DAC3B;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC;kDACtD,IAAI,oBAAJ,IAAI;2DAAC;AAMjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;2DAC1C;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACrE,IAAI,oBAAJ,IAAI;2DAAC;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;+DAC1C;AAGxB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;0DAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;0DAAC;AAKhB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,oCAAa,EAAE,aAAa,CAAC,EAAE,CAAC,aAAa,CAAC,WAAW,CAAC;IAC1E,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,CAAC;kDAC1B,oCAAa,oBAAb,oCAAa;8DAAC;AAI7B;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,oBAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC1C,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;kDACzB,oBAAK,oBAAL,oBAAK;sDAAC;AAId;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,6CAAiB,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAChF,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;kDACzB,6CAAiB,oBAAjB,6CAAiB;qDAAC;kCAzNd,uBAAuB;IARnC,IAAA,gBAAM,EAAC,2BAA2B,CAAC;IACnC,IAAA,eAAK,EAAC,CAAC,iBAAiB,CAAC,CAAC;IAC1B,IAAA,eAAK,EAAC,CAAC,SAAS,CAAC,CAAC;IAClB,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,YAAY,CAAC,CAAC;IACrB,IAAA,eAAK,EAAC,CAAC,YAAY,CAAC,CAAC;IACrB,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,kBAAkB,CAAC,CAAC;GACf,uBAAuB,CA+XnC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\domain\\entities\\vulnerability-assessment.entity.ts"], "sourcesContent": ["import {\r\n  <PERSON><PERSON>ty,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  ManyToOne,\r\n  JoinColumn,\r\n  Index,\r\n} from 'typeorm';\r\nimport { Vulnerability } from './vulnerability.entity';\r\nimport { VulnerabilityScan } from './vulnerability-scan.entity';\r\nimport { Asset } from '../../../asset-management/domain/entities/asset.entity';\r\n\r\n/**\r\n * Vulnerability Assessment entity\r\n * Represents an assessment or analysis of a vulnerability\r\n */\r\n@Entity('vulnerability_assessments')\r\n@Index(['vulnerabilityId'])\r\n@Index(['assetId'])\r\n@Index(['scanId'])\r\n@Index(['assessedBy'])\r\n@Index(['assessedAt'])\r\n@Index(['status'])\r\n@Index(['assessedSeverity'])\r\nexport class VulnerabilityAssessment {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * Associated vulnerability\r\n   */\r\n  @Column({ name: 'vulnerability_id', type: 'uuid' })\r\n  vulnerabilityId: string;\r\n\r\n  /**\r\n   * Associated asset (if assessment is asset-specific)\r\n   */\r\n  @Column({ name: 'asset_id', type: 'uuid', nullable: true })\r\n  assetId?: string;\r\n\r\n  /**\r\n   * Associated scan (if assessment came from a scan)\r\n   */\r\n  @Column({ name: 'scan_id', type: 'uuid', nullable: true })\r\n  scanId?: string;\r\n\r\n  /**\r\n   * Assessment type\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['manual', 'automated', 'ai_assisted', 'peer_review'],\r\n    default: 'manual',\r\n  })\r\n  assessmentType: 'manual' | 'automated' | 'ai_assisted' | 'peer_review';\r\n\r\n  /**\r\n   * Assessment status\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['pending', 'in_progress', 'completed', 'rejected'],\r\n    default: 'pending',\r\n  })\r\n  status: 'pending' | 'in_progress' | 'completed' | 'rejected';\r\n\r\n  /**\r\n   * Assessed severity (may differ from original)\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['critical', 'high', 'medium', 'low', 'info'],\r\n    nullable: true,\r\n  })\r\n  assessedSeverity?: 'critical' | 'high' | 'medium' | 'low' | 'info';\r\n\r\n  /**\r\n   * Assessed CVSS score\r\n   */\r\n  @Column({ name: 'assessed_cvss_score', type: 'decimal', precision: 3, scale: 1, nullable: true })\r\n  assessedCvssScore?: number;\r\n\r\n  /**\r\n   * Assessed risk score\r\n   */\r\n  @Column({ name: 'assessed_risk_score', type: 'decimal', precision: 5, scale: 2, nullable: true })\r\n  assessedRiskScore?: number;\r\n\r\n  /**\r\n   * Assessed priority\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['urgent', 'high', 'medium', 'low'],\r\n    nullable: true,\r\n  })\r\n  assessedPriority?: 'urgent' | 'high' | 'medium' | 'low';\r\n\r\n  /**\r\n   * Assessment findings and notes\r\n   */\r\n  @Column({ type: 'text', nullable: true })\r\n  findings?: string;\r\n\r\n  /**\r\n   * Recommended actions\r\n   */\r\n  @Column({ name: 'recommended_actions', type: 'text', nullable: true })\r\n  recommendedActions?: string;\r\n\r\n  /**\r\n   * Business impact assessment\r\n   */\r\n  @Column({ name: 'business_impact', type: 'text', nullable: true })\r\n  businessImpact?: string;\r\n\r\n  /**\r\n   * Technical impact assessment\r\n   */\r\n  @Column({ name: 'technical_impact', type: 'text', nullable: true })\r\n  technicalImpact?: string;\r\n\r\n  /**\r\n   * Exploitability assessment\r\n   */\r\n  @Column({ name: 'exploitability_assessment', type: 'text', nullable: true })\r\n  exploitabilityAssessment?: string;\r\n\r\n  /**\r\n   * Mitigation strategies\r\n   */\r\n  @Column({ name: 'mitigation_strategies', type: 'jsonb', nullable: true })\r\n  mitigationStrategies?: string[];\r\n\r\n  /**\r\n   * Compensating controls\r\n   */\r\n  @Column({ name: 'compensating_controls', type: 'jsonb', nullable: true })\r\n  compensatingControls?: string[];\r\n\r\n  /**\r\n   * Assessment confidence level (0-100)\r\n   */\r\n  @Column({ name: 'confidence_level', type: 'integer', default: 50 })\r\n  confidenceLevel: number;\r\n\r\n  /**\r\n   * Whether false positive\r\n   */\r\n  @Column({ name: 'is_false_positive', default: false })\r\n  isFalsePositive: boolean;\r\n\r\n  /**\r\n   * False positive justification\r\n   */\r\n  @Column({ name: 'false_positive_justification', type: 'text', nullable: true })\r\n  falsePositiveJustification?: string;\r\n\r\n  /**\r\n   * Whether accepted risk\r\n   */\r\n  @Column({ name: 'is_accepted_risk', default: false })\r\n  isAcceptedRisk: boolean;\r\n\r\n  /**\r\n   * Risk acceptance justification\r\n   */\r\n  @Column({ name: 'risk_acceptance_justification', type: 'text', nullable: true })\r\n  riskAcceptanceJustification?: string;\r\n\r\n  /**\r\n   * Assessment methodology used\r\n   */\r\n  @Column({ nullable: true })\r\n  methodology?: string;\r\n\r\n  /**\r\n   * Tools used for assessment\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  toolsUsed?: string[];\r\n\r\n  /**\r\n   * Assessment criteria\r\n   */\r\n  @Column({ name: 'assessment_criteria', type: 'jsonb', nullable: true })\r\n  assessmentCriteria?: Record<string, any>;\r\n\r\n  /**\r\n   * Additional metadata\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  metadata?: Record<string, any>;\r\n\r\n  /**\r\n   * User who performed the assessment\r\n   */\r\n  @Column({ name: 'assessed_by', type: 'uuid' })\r\n  assessedBy: string;\r\n\r\n  /**\r\n   * When the assessment was performed\r\n   */\r\n  @Column({ name: 'assessed_at', type: 'timestamp with time zone' })\r\n  assessedAt: Date;\r\n\r\n  /**\r\n   * User who reviewed the assessment\r\n   */\r\n  @Column({ name: 'reviewed_by', type: 'uuid', nullable: true })\r\n  reviewedBy?: string;\r\n\r\n  /**\r\n   * When the assessment was reviewed\r\n   */\r\n  @Column({ name: 'reviewed_at', type: 'timestamp with time zone', nullable: true })\r\n  reviewedAt?: Date;\r\n\r\n  /**\r\n   * Review comments\r\n   */\r\n  @Column({ name: 'review_comments', type: 'text', nullable: true })\r\n  reviewComments?: string;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  // Relationships\r\n  @ManyToOne(() => Vulnerability, vulnerability => vulnerability.assessments)\r\n  @JoinColumn({ name: 'vulnerability_id' })\r\n  vulnerability: Vulnerability;\r\n\r\n  @ManyToOne(() => Asset, { nullable: true })\r\n  @JoinColumn({ name: 'asset_id' })\r\n  asset?: Asset;\r\n\r\n  @ManyToOne(() => VulnerabilityScan, scan => scan.assessments, { nullable: true })\r\n  @JoinColumn({ name: 'scan_id' })\r\n  scan?: VulnerabilityScan;\r\n\r\n  /**\r\n   * Check if assessment is completed\r\n   */\r\n  get isCompleted(): boolean {\r\n    return this.status === 'completed';\r\n  }\r\n\r\n  /**\r\n   * Check if assessment is pending\r\n   */\r\n  get isPending(): boolean {\r\n    return this.status === 'pending';\r\n  }\r\n\r\n  /**\r\n   * Check if assessment is in progress\r\n   */\r\n  get isInProgress(): boolean {\r\n    return this.status === 'in_progress';\r\n  }\r\n\r\n  /**\r\n   * Check if assessment was rejected\r\n   */\r\n  get isRejected(): boolean {\r\n    return this.status === 'rejected';\r\n  }\r\n\r\n  /**\r\n   * Check if assessment has been reviewed\r\n   */\r\n  get isReviewed(): boolean {\r\n    return this.reviewedBy !== null && this.reviewedAt !== null;\r\n  }\r\n\r\n  /**\r\n   * Get assessment age in days\r\n   */\r\n  get ageInDays(): number {\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - this.assessedAt.getTime();\r\n    return Math.floor(diffMs / (1000 * 60 * 60 * 24));\r\n  }\r\n\r\n  /**\r\n   * Check if assessment is high confidence\r\n   */\r\n  get isHighConfidence(): boolean {\r\n    return this.confidenceLevel >= 80;\r\n  }\r\n\r\n  /**\r\n   * Check if assessment changed the original severity\r\n   */\r\n  get severityChanged(): boolean {\r\n    return this.assessedSeverity !== null;\r\n  }\r\n\r\n  /**\r\n   * Start assessment\r\n   */\r\n  start(): void {\r\n    this.status = 'in_progress';\r\n  }\r\n\r\n  /**\r\n   * Complete assessment\r\n   */\r\n  complete(): void {\r\n    this.status = 'completed';\r\n  }\r\n\r\n  /**\r\n   * Reject assessment\r\n   */\r\n  reject(reason?: string): void {\r\n    this.status = 'rejected';\r\n    if (reason && this.metadata) {\r\n      this.metadata.rejectionReason = reason;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Review assessment\r\n   */\r\n  review(reviewedBy: string, comments?: string): void {\r\n    this.reviewedBy = reviewedBy;\r\n    this.reviewedAt = new Date();\r\n    if (comments) {\r\n      this.reviewComments = comments;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Mark as false positive\r\n   */\r\n  markAsFalsePositive(justification: string): void {\r\n    this.isFalsePositive = true;\r\n    this.falsePositiveJustification = justification;\r\n    this.assessedSeverity = 'info';\r\n  }\r\n\r\n  /**\r\n   * Accept risk\r\n   */\r\n  acceptRisk(justification: string): void {\r\n    this.isAcceptedRisk = true;\r\n    this.riskAcceptanceJustification = justification;\r\n  }\r\n\r\n  /**\r\n   * Get assessment summary\r\n   */\r\n  getSummary(): any {\r\n    return {\r\n      id: this.id,\r\n      vulnerabilityId: this.vulnerabilityId,\r\n      assetId: this.assetId,\r\n      scanId: this.scanId,\r\n      assessmentType: this.assessmentType,\r\n      status: this.status,\r\n      assessedSeverity: this.assessedSeverity,\r\n      assessedCvssScore: this.assessedCvssScore,\r\n      assessedRiskScore: this.assessedRiskScore,\r\n      assessedPriority: this.assessedPriority,\r\n      confidenceLevel: this.confidenceLevel,\r\n      isFalsePositive: this.isFalsePositive,\r\n      isAcceptedRisk: this.isAcceptedRisk,\r\n      isCompleted: this.isCompleted,\r\n      isPending: this.isPending,\r\n      isInProgress: this.isInProgress,\r\n      isRejected: this.isRejected,\r\n      isReviewed: this.isReviewed,\r\n      isHighConfidence: this.isHighConfidence,\r\n      severityChanged: this.severityChanged,\r\n      ageInDays: this.ageInDays,\r\n      assessedBy: this.assessedBy,\r\n      assessedAt: this.assessedAt,\r\n      reviewedBy: this.reviewedBy,\r\n      reviewedAt: this.reviewedAt,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Export assessment for reporting\r\n   */\r\n  exportForReporting(): any {\r\n    return {\r\n      assessment: this.getSummary(),\r\n      findings: this.findings,\r\n      recommendedActions: this.recommendedActions,\r\n      businessImpact: this.businessImpact,\r\n      technicalImpact: this.technicalImpact,\r\n      exploitabilityAssessment: this.exploitabilityAssessment,\r\n      mitigationStrategies: this.mitigationStrategies,\r\n      compensatingControls: this.compensatingControls,\r\n      methodology: this.methodology,\r\n      toolsUsed: this.toolsUsed,\r\n      assessmentCriteria: this.assessmentCriteria,\r\n      reviewComments: this.reviewComments,\r\n      metadata: this.metadata,\r\n      exportedAt: new Date().toISOString(),\r\n    };\r\n  }\r\n}\r\n"], "version": 3}