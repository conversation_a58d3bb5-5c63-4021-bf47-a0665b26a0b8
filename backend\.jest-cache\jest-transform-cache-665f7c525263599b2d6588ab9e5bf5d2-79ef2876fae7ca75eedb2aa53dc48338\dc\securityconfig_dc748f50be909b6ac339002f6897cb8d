07622bb66e7f4af5747425a8ae83aa0e
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.securityConfig = void 0;
const config_1 = require("@nestjs/config");
// Utility functions for cleaner code
const parseEnvBoolean = (value, defaultValue = false) => value ? value === 'true' : defaultValue;
const parseEnvInt = (value, defaultValue) => value ? parseInt(value, 10) || defaultValue : defaultValue;
const parseEnvArray = (value, defaultArray) => value ? value.split(',').map(item => item.trim()) : defaultArray;
const isProduction = () => process.env['NODE_ENV'] === 'production';
// Default values as constants
const DEFAULT_CORS_ORIGINS = ['http://localhost:3001'];
const DEFAULT_CORS_METHODS = ['GET', 'HEAD', 'PUT', 'PATCH', 'POST', 'DELETE', 'OPTIONS'];
const DEFAULT_CORS_HEADERS = ['Content-Type', 'Authorization', 'X-Requested-With', 'X-Correlation-ID'];
const DEFAULT_ALLOWED_MIME_TYPES = ['application/json', 'text/csv', 'application/xml'];
const DEFAULT_CSP_DIRECTIVES = {
    defaultSrc: ["'self'"],
    styleSrc: ["'self'", "'unsafe-inline'"],
    scriptSrc: ["'self'"],
    imgSrc: ["'self'", "data:", "https:"],
    connectSrc: ["'self'"],
    fontSrc: ["'self'"],
    objectSrc: ["'none'"],
    mediaSrc: ["'self'"],
    frameSrc: ["'none'"],
};
const DEFAULT_PERMISSIONS_POLICY = {
    camera: ['none'],
    microphone: ['none'],
    geolocation: ['none'],
    payment: ['none'],
    usb: ['none'],
};
/**
 * Security configuration for headers, CORS, and protection mechanisms
 * Provides comprehensive security settings for the application
 */
exports.securityConfig = (0, config_1.registerAs)('security', () => ({
    cors: {
        enabled: parseEnvBoolean(process.env['CORS_ENABLED'], true),
        origin: parseEnvArray(process.env['CORS_ORIGIN'], DEFAULT_CORS_ORIGINS),
        methods: parseEnvArray(process.env['CORS_METHODS'], DEFAULT_CORS_METHODS),
        allowedHeaders: parseEnvArray(process.env['CORS_ALLOWED_HEADERS'], DEFAULT_CORS_HEADERS),
        credentials: parseEnvBoolean(process.env['CORS_CREDENTIALS']),
        maxAge: parseEnvInt(process.env['CORS_MAX_AGE'], 86400), // 24 hours
        preflightContinue: false,
        optionsSuccessStatus: 204,
    },
    headers: {
        csp: {
            enabled: parseEnvBoolean(process.env['SECURITY_CSP_ENABLED']),
            directives: DEFAULT_CSP_DIRECTIVES,
            reportOnly: parseEnvBoolean(process.env['SECURITY_CSP_REPORT_ONLY']),
            reportUri: process.env['SECURITY_CSP_REPORT_URI'],
        },
        hsts: {
            enabled: parseEnvBoolean(process.env['SECURITY_HSTS_ENABLED']),
            maxAge: parseEnvInt(process.env['SECURITY_HSTS_MAX_AGE'], 31536000), // 1 year
            includeSubDomains: parseEnvBoolean(process.env['SECURITY_HSTS_INCLUDE_SUBDOMAINS']),
            preload: parseEnvBoolean(process.env['SECURITY_HSTS_PRELOAD']),
        },
        frameOptions: process.env['SECURITY_FRAME_OPTIONS'] || 'DENY',
        contentTypeOptions: parseEnvBoolean(process.env['SECURITY_CONTENT_TYPE_OPTIONS'], true),
        xssFilter: parseEnvBoolean(process.env['SECURITY_XSS_FILTER'], true),
        referrerPolicy: process.env['SECURITY_REFERRER_POLICY'] || 'strict-origin-when-cross-origin',
        permissionsPolicy: DEFAULT_PERMISSIONS_POLICY,
    },
    rateLimit: {
        enabled: parseEnvBoolean(process.env['RATE_LIMIT_ENABLED'], true),
        windowMs: parseEnvInt(process.env['RATE_LIMIT_TTL'], 60) * 1000,
        max: parseEnvInt(process.env['RATE_LIMIT_LIMIT'], 100),
        message: process.env['RATE_LIMIT_MESSAGE'] || 'Too many requests from this IP',
        standardHeaders: true,
        legacyHeaders: false,
        skipSuccessfulRequests: parseEnvBoolean(process.env['RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS']),
        skipFailedRequests: false,
        keyGenerator: (req) => req.ip,
    },
    apiRateLimit: {
        enabled: parseEnvBoolean(process.env['API_RATE_LIMIT_ENABLED'], true),
        windowMs: parseEnvInt(process.env['API_RATE_LIMIT_WINDOW'], 60000),
        max: parseEnvInt(process.env['API_RATE_LIMIT_MAX'], 1000),
        message: 'API rate limit exceeded',
        standardHeaders: true,
        legacyHeaders: false,
    },
    validation: {
        whitelist: parseEnvBoolean(process.env['VALIDATION_WHITELIST']),
        forbidNonWhitelisted: parseEnvBoolean(process.env['VALIDATION_FORBID_NON_WHITELISTED']),
        skipMissingProperties: parseEnvBoolean(process.env['VALIDATION_SKIP_MISSING_PROPERTIES']),
        transform: parseEnvBoolean(process.env['VALIDATION_TRANSFORM']),
        disableErrorMessages: isProduction(),
        maxLength: parseEnvInt(process.env['VALIDATION_MAX_LENGTH'], 1000000), // 1MB
    },
    upload: {
        maxFileSize: parseEnvInt(process.env['UPLOAD_MAX_FILE_SIZE'], 10485760), // 10MB
        allowedMimeTypes: parseEnvArray(process.env['UPLOAD_ALLOWED_TYPES'], DEFAULT_ALLOWED_MIME_TYPES),
        destination: process.env['UPLOAD_DESTINATION'] || './uploads',
        tempDestination: process.env['UPLOAD_TEMP_DESTINATION'] || './temp',
        virusScanEnabled: parseEnvBoolean(process.env['UPLOAD_VIRUS_SCAN_ENABLED']),
        quarantineEnabled: parseEnvBoolean(process.env['UPLOAD_QUARANTINE_ENABLED']),
    },
    csrf: {
        enabled: parseEnvBoolean(process.env['CSRF_ENABLED']),
        cookieName: process.env['CSRF_COOKIE_NAME'] || '_csrf',
        headerName: process.env['CSRF_HEADER_NAME'] || 'x-csrf-token',
        ignoreMethods: ['GET', 'HEAD', 'OPTIONS'],
        secure: isProduction(),
        sameSite: 'strict',
    },
    ipFilter: {
        enabled: parseEnvBoolean(process.env['IP_FILTER_ENABLED']),
        whitelist: parseEnvArray(process.env['IP_WHITELIST'], []),
        blacklist: parseEnvArray(process.env['IP_BLACKLIST'], []),
        trustProxy: parseEnvBoolean(process.env['TRUST_PROXY']),
    },
    limits: {
        json: process.env['LIMIT_JSON'] || '1mb',
        urlencoded: process.env['LIMIT_URLENCODED'] || '1mb',
        text: process.env['LIMIT_TEXT'] || '1mb',
        raw: process.env['LIMIT_RAW'] || '1mb',
    },
    monitoring: {
        enabled: parseEnvBoolean(process.env['SECURITY_MONITORING_ENABLED']),
        logSecurityEvents: parseEnvBoolean(process.env['SECURITY_LOG_EVENTS'], true),
        alertOnSuspiciousActivity: parseEnvBoolean(process.env['SECURITY_ALERT_SUSPICIOUS']),
        blockSuspiciousIPs: parseEnvBoolean(process.env['SECURITY_BLOCK_SUSPICIOUS_IPS']),
        suspiciousActivityThreshold: parseEnvInt(process.env['SECURITY_SUSPICIOUS_THRESHOLD'], 10),
    },
    apiKey: {
        enabled: parseEnvBoolean(process.env['API_KEY_SECURITY_ENABLED']),
        headerName: process.env['API_KEY_HEADER'] || 'X-API-Key',
        queryParam: process.env['API_KEY_QUERY_PARAM'] || 'apikey',
        allowQueryParam: parseEnvBoolean(process.env['API_KEY_ALLOW_QUERY']),
        rateLimitPerKey: parseEnvInt(process.env['API_KEY_RATE_LIMIT'], 1000),
        logUsage: parseEnvBoolean(process.env['API_KEY_LOG_USAGE']),
    },
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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