{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting-analytics\\domain\\entities\\report-execution.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCASiB;AACjB,mDAAyC;AAEzC;;;GAGG;AAOI,IAAM,eAAe,GAArB,MAAM,eAAe;IAuN1B;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,IAAI,iBAAiB;QACnB,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAO,KAAK,CAAC;QAEnC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC;QACnD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC;QACzC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC;QAEvC,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;YACd,OAAO,GAAG,KAAK,KAAK,OAAO,GAAG,EAAE,KAAK,OAAO,GAAG,EAAE,GAAG,CAAC;QACvD,CAAC;aAAM,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;YACvB,OAAO,GAAG,OAAO,KAAK,OAAO,GAAG,EAAE,GAAG,CAAC;QACxC,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,OAAO,GAAG,CAAC;QACvB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,IAAI,mBAAmB;QACrB,IAAI,CAAC,IAAI,CAAC,eAAe;YAAE,OAAO,KAAK,CAAC;QAExC,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC3C,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACtC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;QAEvD,OAAO,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;IACjE,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;QACxB,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC5B,IAAI,CAAC,QAAQ,GAAG;YACd,UAAU,EAAE,CAAC;YACb,WAAW,EAAE,cAAc;YAC3B,UAAU,EAAE,CAAC;YACb,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACrC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,UAAkB,EAAE,WAAmB,EAAE,UAAmB;QACzE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,IAAI,CAAC,QAAQ,GAAG;gBACd,UAAU,EAAE,CAAC;gBACb,WAAW,EAAE,UAAU;gBACvB,UAAU,EAAE,UAAU,IAAI,CAAC;gBAC3B,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACrC,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC;QAClE,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,WAAW,CAAC;QACxC,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,UAAU,CAAC;QACxC,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAEpD,0BAA0B;QAC1B,IAAI,IAAI,CAAC,SAAS,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;YACrC,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YACtD,MAAM,SAAS,GAAG,CAAC,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,CAAC,CAAC;YAC9D,IAAI,CAAC,QAAQ,CAAC,sBAAsB,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,UAAmB,EAAE,YAAqB,EAAE,UAAgB;QACnE,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;QAC1B,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QAC9B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAE7B,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAC1E,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,GAAG,CAAC;YAC/B,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,WAAW,CAAC;YACxC,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QACtD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,IAAI,CAAC,KAAqB,EAAE,IAAa;QACvC,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;QACvB,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QAE9B,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAC1E,CAAC;QAED,IAAI,CAAC,YAAY,GAAG;YAClB,OAAO,EAAE,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO;YAC1D,IAAI;YACJ,KAAK,EAAE,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;YAC1D,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAe;QACpB,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;QAC1B,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QAE9B,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAC1E,CAAC;QAED,IAAI,CAAC,YAAY,GAAG;YAClB,OAAO,EAAE,MAAM,IAAI,6BAA6B;YAChD,IAAI,EAAE,WAAW;YACjB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;QACxB,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QAE9B,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAC1E,CAAC;QAED,IAAI,CAAC,YAAY,GAAG;YAClB,OAAO,EAAE,qBAAqB;YAC9B,IAAI,EAAE,SAAS;YACf,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,MAAc,EAAE,KAAa;QAC5C,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,IAAI,CAAC,cAAc,GAAG;gBACpB,gBAAgB,EAAE,CAAC;gBACnB,YAAY,EAAE,CAAC;gBACf,gBAAgB,EAAE,CAAC;aACpB,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,IAAkC,EAAE,OAAe,EAAE,QAAmC;QACtG,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,IAAI,CAAC,cAAc,GAAG;gBACpB,gBAAgB,EAAE,CAAC;gBACnB,YAAY,EAAE,CAAC;gBACf,gBAAgB,EAAE,CAAC;aACpB,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;YAChC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,EAAE,CAAC;QAClC,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC;YAC9B,IAAI;YACJ,OAAO;YACP,QAAQ;SACT,CAAC,CAAC;IACL,CAAC;CACF,CAAA;AA9aY,0CAAe;AAE1B;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;2CACpB;AAUX;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC;QAC3E,OAAO,EAAE,SAAS;KACnB,CAAC;;+CAC+E;AAWjF;IANC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,cAAc;QACpB,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,KAAK,EAAE,SAAS,CAAC;QAC/C,OAAO,EAAE,QAAQ;KAClB,CAAC;;oDACsD;AAMxD;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDAWxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACrE,IAAI,oBAAJ,IAAI;kDAAC;AAMjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACrE,IAAI,oBAAJ,IAAI;oDAAC;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDAC7C;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;yDAC7C;AAM1B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;wDAC7C;AAMzB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDAC5B;AAMtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDAC5B;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDAwC7D;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;iDAUxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDAO/D;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;iDAOxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uDAWjE;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;mDAC3B;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDAC5B;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,qBAAqB,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;0DAC7B;AAG3B;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;kDAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;kDAAC;AAKhB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,sBAAM,EAAE,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC;IACpD,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;kDAC1B,sBAAM,oBAAN,sBAAM;+CAAC;AAGf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;iDAC3B;0BArNN,eAAe;IAN3B,IAAA,gBAAM,EAAC,mBAAmB,CAAC;IAC3B,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,YAAY,CAAC,CAAC;IACrB,IAAA,eAAK,EAAC,CAAC,WAAW,CAAC,CAAC;IACpB,IAAA,eAAK,EAAC,CAAC,aAAa,CAAC,CAAC;GACV,eAAe,CA8a3B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting-analytics\\domain\\entities\\report-execution.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  ManyToOne,\r\n  JoinColumn,\r\n} from 'typeorm';\r\nimport { Report } from './report.entity';\r\n\r\n/**\r\n * Report Execution entity\r\n * Represents individual executions of reports with their results and metadata\r\n */\r\n@Entity('report_executions')\r\n@Index(['reportId'])\r\n@Index(['status'])\r\n@Index(['executedBy'])\r\n@Index(['startedAt'])\r\n@Index(['completedAt'])\r\nexport class ReportExecution {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * Execution status\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['pending', 'running', 'completed', 'failed', 'cancelled', 'timeout'],\r\n    default: 'pending',\r\n  })\r\n  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled' | 'timeout';\r\n\r\n  /**\r\n   * Execution trigger type\r\n   */\r\n  @Column({\r\n    name: 'trigger_type',\r\n    type: 'enum',\r\n    enum: ['manual', 'scheduled', 'api', 'webhook'],\r\n    default: 'manual',\r\n  })\r\n  triggerType: 'manual' | 'scheduled' | 'api' | 'webhook';\r\n\r\n  /**\r\n   * Execution parameters (overrides from report configuration)\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  parameters?: {\r\n    timeRange?: {\r\n      type: 'relative' | 'absolute';\r\n      value?: string;\r\n      startDate?: string;\r\n      endDate?: string;\r\n    };\r\n    filters?: Record<string, any>;\r\n    exportFormat?: string;\r\n    customParameters?: Record<string, any>;\r\n  };\r\n\r\n  /**\r\n   * When execution started\r\n   */\r\n  @Column({ name: 'started_at', type: 'timestamp with time zone', nullable: true })\r\n  startedAt?: Date;\r\n\r\n  /**\r\n   * When execution completed\r\n   */\r\n  @Column({ name: 'completed_at', type: 'timestamp with time zone', nullable: true })\r\n  completedAt?: Date;\r\n\r\n  /**\r\n   * Execution duration in milliseconds\r\n   */\r\n  @Column({ name: 'duration_ms', type: 'integer', nullable: true })\r\n  durationMs?: number;\r\n\r\n  /**\r\n   * Number of records processed\r\n   */\r\n  @Column({ name: 'records_processed', type: 'integer', nullable: true })\r\n  recordsProcessed?: number;\r\n\r\n  /**\r\n   * Size of generated report in bytes\r\n   */\r\n  @Column({ name: 'output_size_bytes', type: 'bigint', nullable: true })\r\n  outputSizeBytes?: number;\r\n\r\n  /**\r\n   * Output format of the generated report\r\n   */\r\n  @Column({ name: 'output_format', nullable: true })\r\n  outputFormat?: string;\r\n\r\n  /**\r\n   * File path or URL to the generated report\r\n   */\r\n  @Column({ name: 'output_path', nullable: true })\r\n  outputPath?: string;\r\n\r\n  /**\r\n   * Report data (for small reports or previews)\r\n   */\r\n  @Column({ name: 'report_data', type: 'jsonb', nullable: true })\r\n  reportData?: {\r\n    summary?: {\r\n      totalRecords: number;\r\n      dataPoints: number;\r\n      generatedAt: string;\r\n      timeRange: string;\r\n    };\r\n    sections?: Array<{\r\n      id: string;\r\n      title: string;\r\n      type: string;\r\n      data: any;\r\n      metadata?: Record<string, any>;\r\n    }>;\r\n    charts?: Array<{\r\n      id: string;\r\n      title: string;\r\n      type: string;\r\n      data: any;\r\n      configuration: Record<string, any>;\r\n    }>;\r\n    tables?: Array<{\r\n      id: string;\r\n      title: string;\r\n      headers: string[];\r\n      rows: any[][];\r\n      metadata?: Record<string, any>;\r\n    }>;\r\n    metrics?: Array<{\r\n      id: string;\r\n      title: string;\r\n      value: number | string;\r\n      unit?: string;\r\n      trend?: {\r\n        direction: 'up' | 'down' | 'stable';\r\n        percentage: number;\r\n        period: string;\r\n      };\r\n    }>;\r\n  };\r\n\r\n  /**\r\n   * Execution metadata\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  metadata?: {\r\n    queryExecutionTime?: number;\r\n    dataProcessingTime?: number;\r\n    renderingTime?: number;\r\n    cacheHit?: boolean;\r\n    dataSourcesUsed?: string[];\r\n    filtersApplied?: Record<string, any>;\r\n    optimizations?: string[];\r\n    warnings?: string[];\r\n  };\r\n\r\n  /**\r\n   * Error information if execution failed\r\n   */\r\n  @Column({ name: 'error_details', type: 'jsonb', nullable: true })\r\n  errorDetails?: {\r\n    message: string;\r\n    code?: string;\r\n    stack?: string;\r\n    context?: Record<string, any>;\r\n    timestamp: string;\r\n  };\r\n\r\n  /**\r\n   * Progress information for long-running executions\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  progress?: {\r\n    percentage: number;\r\n    currentStep: string;\r\n    totalSteps: number;\r\n    estimatedTimeRemaining?: number;\r\n    lastUpdate: string;\r\n  };\r\n\r\n  /**\r\n   * Quality metrics for the execution\r\n   */\r\n  @Column({ name: 'quality_metrics', type: 'jsonb', nullable: true })\r\n  qualityMetrics?: {\r\n    dataCompleteness: number; // 0-1\r\n    dataAccuracy: number; // 0-1\r\n    performanceScore: number; // 0-1\r\n    userSatisfaction?: number; // 0-1\r\n    issues?: Array<{\r\n      type: 'warning' | 'error' | 'info';\r\n      message: string;\r\n      severity: 'low' | 'medium' | 'high';\r\n    }>;\r\n  };\r\n\r\n  /**\r\n   * User who executed the report\r\n   */\r\n  @Column({ name: 'executed_by', type: 'uuid' })\r\n  executedBy: string;\r\n\r\n  /**\r\n   * IP address of the executor\r\n   */\r\n  @Column({ name: 'executor_ip', nullable: true })\r\n  executorIp?: string;\r\n\r\n  /**\r\n   * User agent of the executor\r\n   */\r\n  @Column({ name: 'executor_user_agent', nullable: true })\r\n  executorUserAgent?: string;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  // Relationships\r\n  @ManyToOne(() => Report, report => report.executions)\r\n  @JoinColumn({ name: 'report_id' })\r\n  report: Report;\r\n\r\n  @Column({ name: 'report_id', type: 'uuid' })\r\n  reportId: string;\r\n\r\n  /**\r\n   * Check if execution is running\r\n   */\r\n  get isRunning(): boolean {\r\n    return ['pending', 'running'].includes(this.status);\r\n  }\r\n\r\n  /**\r\n   * Check if execution is completed\r\n   */\r\n  get isCompleted(): boolean {\r\n    return this.status === 'completed';\r\n  }\r\n\r\n  /**\r\n   * Check if execution failed\r\n   */\r\n  get isFailed(): boolean {\r\n    return ['failed', 'cancelled', 'timeout'].includes(this.status);\r\n  }\r\n\r\n  /**\r\n   * Get execution duration in seconds\r\n   */\r\n  get durationSeconds(): number | null {\r\n    return this.durationMs ? Math.round(this.durationMs / 1000) : null;\r\n  }\r\n\r\n  /**\r\n   * Get human-readable duration\r\n   */\r\n  get durationFormatted(): string {\r\n    if (!this.durationMs) return 'N/A';\r\n    \r\n    const seconds = Math.floor(this.durationMs / 1000);\r\n    const minutes = Math.floor(seconds / 60);\r\n    const hours = Math.floor(minutes / 60);\r\n    \r\n    if (hours > 0) {\r\n      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;\r\n    } else if (minutes > 0) {\r\n      return `${minutes}m ${seconds % 60}s`;\r\n    } else {\r\n      return `${seconds}s`;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get output size in human-readable format\r\n   */\r\n  get outputSizeFormatted(): string {\r\n    if (!this.outputSizeBytes) return 'N/A';\r\n    \r\n    const bytes = Number(this.outputSizeBytes);\r\n    const sizes = ['B', 'KB', 'MB', 'GB'];\r\n    const i = Math.floor(Math.log(bytes) / Math.log(1024));\r\n    \r\n    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;\r\n  }\r\n\r\n  /**\r\n   * Start execution\r\n   */\r\n  start(): void {\r\n    this.status = 'running';\r\n    this.startedAt = new Date();\r\n    this.progress = {\r\n      percentage: 0,\r\n      currentStep: 'Initializing',\r\n      totalSteps: 1,\r\n      lastUpdate: new Date().toISOString(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Update execution progress\r\n   */\r\n  updateProgress(percentage: number, currentStep: string, totalSteps?: number): void {\r\n    if (!this.progress) {\r\n      this.progress = {\r\n        percentage: 0,\r\n        currentStep: 'Starting',\r\n        totalSteps: totalSteps || 1,\r\n        lastUpdate: new Date().toISOString(),\r\n      };\r\n    }\r\n\r\n    this.progress.percentage = Math.min(100, Math.max(0, percentage));\r\n    this.progress.currentStep = currentStep;\r\n    if (totalSteps) {\r\n      this.progress.totalSteps = totalSteps;\r\n    }\r\n    this.progress.lastUpdate = new Date().toISOString();\r\n\r\n    // Estimate time remaining\r\n    if (this.startedAt && percentage > 0) {\r\n      const elapsed = Date.now() - this.startedAt.getTime();\r\n      const estimated = (elapsed / percentage) * (100 - percentage);\r\n      this.progress.estimatedTimeRemaining = Math.round(estimated);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Complete execution successfully\r\n   */\r\n  complete(outputPath?: string, outputFormat?: string, reportData?: any): void {\r\n    this.status = 'completed';\r\n    this.completedAt = new Date();\r\n    this.outputPath = outputPath;\r\n    this.outputFormat = outputFormat;\r\n    this.reportData = reportData;\r\n\r\n    if (this.startedAt) {\r\n      this.durationMs = this.completedAt.getTime() - this.startedAt.getTime();\r\n    }\r\n\r\n    if (this.progress) {\r\n      this.progress.percentage = 100;\r\n      this.progress.currentStep = 'Completed';\r\n      this.progress.lastUpdate = new Date().toISOString();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Fail execution with error details\r\n   */\r\n  fail(error: Error | string, code?: string): void {\r\n    this.status = 'failed';\r\n    this.completedAt = new Date();\r\n\r\n    if (this.startedAt) {\r\n      this.durationMs = this.completedAt.getTime() - this.startedAt.getTime();\r\n    }\r\n\r\n    this.errorDetails = {\r\n      message: typeof error === 'string' ? error : error.message,\r\n      code,\r\n      stack: typeof error === 'object' ? error.stack : undefined,\r\n      timestamp: new Date().toISOString(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Cancel execution\r\n   */\r\n  cancel(reason?: string): void {\r\n    this.status = 'cancelled';\r\n    this.completedAt = new Date();\r\n\r\n    if (this.startedAt) {\r\n      this.durationMs = this.completedAt.getTime() - this.startedAt.getTime();\r\n    }\r\n\r\n    this.errorDetails = {\r\n      message: reason || 'Execution cancelled by user',\r\n      code: 'CANCELLED',\r\n      timestamp: new Date().toISOString(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Mark execution as timed out\r\n   */\r\n  timeout(): void {\r\n    this.status = 'timeout';\r\n    this.completedAt = new Date();\r\n\r\n    if (this.startedAt) {\r\n      this.durationMs = this.completedAt.getTime() - this.startedAt.getTime();\r\n    }\r\n\r\n    this.errorDetails = {\r\n      message: 'Execution timed out',\r\n      code: 'TIMEOUT',\r\n      timestamp: new Date().toISOString(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Add quality metric\r\n   */\r\n  addQualityMetric(metric: string, value: number): void {\r\n    if (!this.qualityMetrics) {\r\n      this.qualityMetrics = {\r\n        dataCompleteness: 1,\r\n        dataAccuracy: 1,\r\n        performanceScore: 1,\r\n      };\r\n    }\r\n\r\n    this.qualityMetrics[metric] = value;\r\n  }\r\n\r\n  /**\r\n   * Add quality issue\r\n   */\r\n  addQualityIssue(type: 'warning' | 'error' | 'info', message: string, severity: 'low' | 'medium' | 'high'): void {\r\n    if (!this.qualityMetrics) {\r\n      this.qualityMetrics = {\r\n        dataCompleteness: 1,\r\n        dataAccuracy: 1,\r\n        performanceScore: 1,\r\n      };\r\n    }\r\n\r\n    if (!this.qualityMetrics.issues) {\r\n      this.qualityMetrics.issues = [];\r\n    }\r\n\r\n    this.qualityMetrics.issues.push({\r\n      type,\r\n      message,\r\n      severity,\r\n    });\r\n  }\r\n}\r\n"], "version": 3}