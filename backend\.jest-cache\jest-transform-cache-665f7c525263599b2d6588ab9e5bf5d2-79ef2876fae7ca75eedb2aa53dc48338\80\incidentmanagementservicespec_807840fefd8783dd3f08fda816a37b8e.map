{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\incident-response\\application\\services\\incident-management.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,6CAAqD;AAErD,+EAA0E;AAC1E,2EAAiE;AACjE,qFAA0E;AAC1E,6FAAkF;AAClF,6FAAkF;AAClF,qFAA0E;AAC1E,sFAAkF;AAClF,0FAAsF;AACtF,iEAA6D;AAC7D,qFAAgF;AAEhF,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;IACzC,IAAI,OAAkC,CAAC;IACvC,IAAI,kBAAwC,CAAC;IAC7C,IAAI,cAAwC,CAAC;IAC7C,IAAI,kBAAgD,CAAC;IACrD,IAAI,kBAAgD,CAAC;IACrD,IAAI,sBAAgD,CAAC;IACrD,IAAI,aAA4B,CAAC;IACjC,IAAI,YAA0B,CAAC;IAC/B,IAAI,mBAAwC,CAAC;IAC7C,IAAI,4BAA0D,CAAC;IAE/D,MAAM,sBAAsB,GAAG;QAC7B,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC7B,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;KAClB,CAAC;IAEF,MAAM,kBAAkB,GAAG;QACzB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;KAChB,CAAC;IAEF,MAAM,sBAAsB,GAAG;QAC7B,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;KAChB,CAAC;IAEF,MAAM,sBAAsB,GAAG;QAC7B,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;KAChB,CAAC;IAEF,MAAM,0BAA0B,GAAG;QACjC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;KACnB,CAAC;IAEF,MAAM,iBAAiB,GAAG;QACxB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;QACd,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;KACjB,CAAC;IAEF,MAAM,gBAAgB,GAAG;QACvB,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;KACzB,CAAC;IAEF,MAAM,uBAAuB,GAAG;QAC9B,wBAAwB,EAAE,IAAI,CAAC,EAAE,EAAE;KACpC,CAAC;IAEF,MAAM,gCAAgC,GAAG;QACvC,mBAAmB,EAAE,IAAI,CAAC,EAAE,EAAE;KAC/B,CAAC;IAEF,MAAM,gBAAgB,GAAG;QACvB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;QACjC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;QACpC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;QACnC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;QAChC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;QAChC,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE;QAC1B,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;QACnB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;QAClC,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;QACrC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;QACnC,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;QACrB,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;KACrB,CAAC;IAEF,MAAM,gBAAgB,GAAG;QACvB,KAAK,EAAE,eAAe;QACtB,WAAW,EAAE,2BAA2B;QACxC,QAAQ,EAAE,MAAe;QACzB,QAAQ,EAAE,UAAU;QACpB,MAAM,EAAE,QAAQ;QAChB,UAAU,EAAE,IAAI,IAAI,EAAE;QACtB,IAAI,EAAE,CAAC,MAAM,CAAC;QACd,eAAe,EAAE,UAAmB;KACrC,CAAC;IAEF,MAAM,YAAY,GAAsB;QACtC,EAAE,EAAE,cAAc;QAClB,KAAK,EAAE,eAAe;QACtB,WAAW,EAAE,2BAA2B;QACxC,MAAM,EAAE,KAAK;QACb,QAAQ,EAAE,MAAM;QAChB,QAAQ,EAAE,MAAM;QAChB,QAAQ,EAAE,UAAU;QACpB,MAAM,EAAE,QAAQ;QAChB,UAAU,EAAE,IAAI,IAAI,EAAE;QACtB,IAAI,EAAE,CAAC,MAAM,CAAC;QACd,eAAe,EAAE,UAAU;QAC3B,SAAS,EAAE,UAAU;QACrB,MAAM,EAAE,IAAI;QACZ,UAAU,EAAE,IAAI;QAChB,SAAS,EAAE,KAAK;QAChB,UAAU,EAAE,CAAC;QACb,kBAAkB,EAAE,CAAC;QACrB,QAAQ,EAAE,CAAC;QACX,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,GAAG,CAAC;QAClD,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;QACtB,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;QACnB,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;QACxB,KAAK,EAAE,EAAE;QACT,QAAQ,EAAE,EAAE;QACZ,cAAc,EAAE,EAAE;QAClB,QAAQ,EAAE,EAAE;KACb,CAAC;IAEF,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,uDAAyB;gBACzB;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,0BAAQ,CAAC;oBACrC,QAAQ,EAAE,sBAAsB;iBACjC;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,mCAAY,CAAC;oBACzC,QAAQ,EAAE,kBAAkB;iBAC7B;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,2CAAgB,CAAC;oBAC7C,QAAQ,EAAE,sBAAsB;iBACjC;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,2CAAgB,CAAC;oBAC7C,QAAQ,EAAE,sBAAsB;iBACjC;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,mCAAY,CAAC;oBACzC,QAAQ,EAAE,0BAA0B;iBACrC;gBACD;oBACE,OAAO,EAAE,8BAAa;oBACtB,QAAQ,EAAE,iBAAiB;iBAC5B;gBACD;oBACE,OAAO,EAAE,4BAAY;oBACrB,QAAQ,EAAE,gBAAgB;iBAC3B;gBACD;oBACE,OAAO,EAAE,0CAAmB;oBAC5B,QAAQ,EAAE,uBAAuB;iBAClC;gBACD;oBACE,OAAO,EAAE,6DAA4B;oBACrC,QAAQ,EAAE,gCAAgC;iBAC3C;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAA4B,uDAAyB,CAAC,CAAC;QAC3E,kBAAkB,GAAG,MAAM,CAAC,GAAG,CAAuB,IAAA,4BAAkB,EAAC,0BAAQ,CAAC,CAAC,CAAC;QACpF,cAAc,GAAG,MAAM,CAAC,GAAG,CAA2B,IAAA,4BAAkB,EAAC,mCAAY,CAAC,CAAC,CAAC;QACxF,kBAAkB,GAAG,MAAM,CAAC,GAAG,CAA+B,IAAA,4BAAkB,EAAC,2CAAgB,CAAC,CAAC,CAAC;QACpG,kBAAkB,GAAG,MAAM,CAAC,GAAG,CAA+B,IAAA,4BAAkB,EAAC,2CAAgB,CAAC,CAAC,CAAC;QACpG,sBAAsB,GAAG,MAAM,CAAC,GAAG,CAA2B,IAAA,4BAAkB,EAAC,mCAAY,CAAC,CAAC,CAAC;QAChG,aAAa,GAAG,MAAM,CAAC,GAAG,CAAgB,8BAAa,CAAC,CAAC;QACzD,YAAY,GAAG,MAAM,CAAC,GAAG,CAAe,4BAAY,CAAC,CAAC;QACtD,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAsB,0CAAmB,CAAC,CAAC;QAC3E,4BAA4B,GAAG,MAAM,CAAC,GAAG,CAA+B,6DAA4B,CAAC,CAAC;QAEtG,2BAA2B;QAC3B,sBAAsB,CAAC,kBAAkB,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;IAC9E,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,MAAM,GAAG,UAAU,CAAC;YAC1B,MAAM,aAAa,GAAG,EAAE,GAAG,YAAY,EAAE,EAAE,EAAE,cAAc,EAAE,CAAC;YAE9D,sBAAsB,CAAC,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;YAC5D,sBAAsB,CAAC,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAC7D,sBAAsB,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAClD,sBAAsB,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YAClD,0BAA0B,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YAEtD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;YAEtE,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC;gBACzD,GAAG,gBAAgB;gBACnB,QAAQ,EAAE,MAAM,EAAE,2BAA2B;gBAC7C,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;YACH,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;YACvE,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACzD,MAAM,CAAC,uBAAuB,CAAC,wBAAwB,CAAC,CAAC,oBAAoB,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;YACxG,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,MAAM,EACN,QAAQ,EACR,UAAU,EACV,aAAa,CAAC,EAAE,EAChB,MAAM,CAAC,gBAAgB,CAAC;gBACtB,KAAK,EAAE,gBAAgB,CAAC,KAAK;gBAC7B,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;gBACnC,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;aACpC,CAAC,CACH,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,MAAM,GAAG,UAAU,CAAC;YAC1B,MAAM,aAAa,GAAG,EAAE,GAAG,YAAY,EAAE,EAAE,EAAE,cAAc,EAAE,CAAC;YAC9D,MAAM,YAAY,GAAG;gBACnB,EAAE,EAAE,UAAU;gBACd,IAAI,EAAE,wBAAwB;gBAC9B,sBAAsB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;aACxD,CAAC;YAEF,sBAAsB,CAAC,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;YAC5D,sBAAsB,CAAC,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAC7D,sBAAsB,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAClD,sBAAsB,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YAClD,0BAA0B,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;YAElE,MAAM,OAAO,CAAC,cAAc,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;YAEvD,MAAM,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;YAC/E,MAAM,CAAC,gCAAgC,CAAC,mBAAmB,CAAC,CAAC,oBAAoB,CAC/E,aAAa,CAAC,EAAE,EAChB,YAAY,CAAC,EAAE,CAChB,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,UAAU,GAAG,cAAc,CAAC;YAClC,sBAAsB,CAAC,OAAO,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAE/D,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAElD,MAAM,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBAC1D,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE;gBACzB,SAAS,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,gBAAgB,EAAE,UAAU,EAAE,cAAc,CAAC;aAC/E,CAAC,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,UAAU,GAAG,cAAc,CAAC;YAClC,sBAAsB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAEvD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAElD,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,MAAM,OAAO,GAAG;gBACd,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,EAAE;gBACT,MAAM,EAAE,CAAC,KAAK,EAAE,eAAe,CAAC;gBAChC,QAAQ,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC;gBAC9B,MAAM,EAAE,UAAU;aACnB,CAAC;YAEF,MAAM,SAAS,GAAG,CAAC,YAAY,CAAC,CAAC;YACjC,MAAM,KAAK,GAAG,CAAC,CAAC;YAEhB,gBAAgB,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;YAEvE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAE/C,MAAM,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;YACnF,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,iCAAiC,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YACtH,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,qCAAqC,EAAE,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC9H,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CACpD,sEAAsE,EACtE,EAAE,MAAM,EAAE,YAAY,EAAE,CACzB,CAAC;YACF,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC,oBAAoB,EAAE,MAAM,CAAC,CAAC;YACpF,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;YACtD,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;YAEvD,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,SAAS;gBACT,KAAK;gBACL,IAAI,EAAE,CAAC;gBACP,UAAU,EAAE,CAAC;aACd,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,UAAU,GAAG,cAAc,CAAC;YAClC,MAAM,MAAM,GAAG,UAAU,CAAC;YAC1B,MAAM,QAAQ,GAAG,EAAE,GAAG,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;YAEpD,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,QAAoB,CAAC,CAAC;YACxE,sBAAsB,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACxD,sBAAsB,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAClD,sBAAsB,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YAElD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YAErE,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;YAC1D,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YACnE,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACzD,MAAM,CAAC,uBAAuB,CAAC,wBAAwB,CAAC,CAAC,oBAAoB,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;YACxG,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,MAAM,EACN,aAAa,EACb,UAAU,EACV,UAAU,EACV,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,UAAU,GAAG,cAAc,CAAC;YAClC,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAExD,MAAM,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;iBAC1D,OAAO,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACrE,MAAM,UAAU,GAAG,cAAc,CAAC;YAClC,MAAM,MAAM,GAAG,UAAU,CAAC;YAC1B,MAAM,QAAQ,GAAG,EAAE,GAAG,YAAY,EAAE,MAAM,EAAE,eAAe,EAAE,CAAC;YAE9D,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,QAAoB,CAAC,CAAC;YAExE,MAAM,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;iBAC1D,OAAO,CAAC,OAAO,CAAC,wCAAwC,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,UAAU,GAAG,cAAc,CAAC;YAClC,MAAM,MAAM,GAAG,UAAU,CAAC;YAC1B,MAAM,MAAM,GAAG,+BAA+B,CAAC;YAC/C,MAAM,QAAQ,GAAG,EAAE,GAAG,YAAY,EAAE,CAAC;YAErC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,QAAoB,CAAC,CAAC;YACxE,sBAAsB,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACxD,sBAAsB,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAClD,sBAAsB,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YAElD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,gBAAgB,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;YAE1E,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAC/D,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YACnE,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACzD,MAAM,CAAC,uBAAuB,CAAC,wBAAwB,CAAC,CAAC,oBAAoB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;YACrG,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,MAAM,EACN,UAAU,EACV,UAAU,EACV,UAAU,EACV,MAAM,CAAC,gBAAgB,CAAC,EAAE,MAAM,EAAE,CAAC,CACpC,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,MAAM,UAAU,GAAG,cAAc,CAAC;YAClC,MAAM,MAAM,GAAG,UAAU,CAAC;YAC1B,MAAM,UAAU,GAAG,4CAA4C,CAAC;YAChE,MAAM,QAAQ,GAAG,EAAE,GAAG,YAAY,EAAE,CAAC;YAErC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,QAAoB,CAAC,CAAC;YACxE,sBAAsB,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACxD,sBAAsB,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAClD,sBAAsB,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YAElD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;YAE7E,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YAClE,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YACnE,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACzD,MAAM,CAAC,uBAAuB,CAAC,wBAAwB,CAAC,CAAC,oBAAoB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YACpG,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,MAAM,EACN,SAAS,EACT,UAAU,EACV,UAAU,EACV,MAAM,CAAC,gBAAgB,CAAC,EAAE,UAAU,EAAE,CAAC,CACxC,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,UAAU,GAAG,cAAc,CAAC;YAClC,MAAM,MAAM,GAAG,UAAU,CAAC;YAC1B,MAAM,IAAI,GAAG,SAAS,CAAC;YACvB,MAAM,WAAW,GAAG,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC;YACtD,MAAM,OAAO,GAAG,UAAU,CAAC;YAC3B,MAAM,QAAQ,GAAG,EAAE,GAAG,YAAY,EAAE,CAAC;YAErC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,QAAoB,CAAC,CAAC;YACxE,sBAAsB,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACxD,sBAAsB,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAClD,sBAAsB,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YAElD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,aAAa,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;YAE3F,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;YAC/E,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YACnE,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACzD,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,OAAO,EACP,iBAAiB,EACjB,UAAU,EACV,UAAU,EACV,MAAM,CAAC,gBAAgB,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CACvD,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,SAAS,GAAG;gBAChB,cAAc,EAAE,GAAG;gBACnB,aAAa,EAAE,EAAE;gBACjB,iBAAiB,EAAE,CAAC;gBACpB,gBAAgB,EAAE,CAAC;gBACnB,QAAQ,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,aAAa,EAAE,EAAE,EAAE;gBACxC,UAAU,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE;gBACrC,WAAW,EAAE;oBACX,qBAAqB,EAAE,GAAG;oBAC1B,aAAa,EAAE,IAAI;iBACpB;aACF,CAAC;YAEF,sBAAsB,CAAC,KAAK,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC,iBAAiB;YAC1E,sBAAsB,CAAC,KAAK,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC,CAAE,gBAAgB;YACzE,sBAAsB,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAG,oBAAoB;YAE7E,sBAAsB;YACtB,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,0BAA0B,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAC5E,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,sBAAsB,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YACzF,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,wBAAwB,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YAC7F,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,0BAA0B,CAAC,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;YAC9E,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,kBAAkB,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAEvE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,aAAa,EAAE,CAAC;YAE7C,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC;gBAC3B,cAAc,EAAE,GAAG;gBACnB,aAAa,EAAE,EAAE;gBACjB,iBAAiB,EAAE,CAAC;gBACpB,gBAAgB,EAAE,CAAC;gBACnB,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,UAAU,EAAE,SAAS,CAAC,UAAU;gBAChC,WAAW,EAAE,SAAS,CAAC,WAAW;aACnC,CAAC,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\incident-response\\application\\services\\incident-management.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { getRepositoryToken } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\nimport { IncidentManagementService } from './incident-management.service';\r\nimport { Incident } from '../../domain/entities/incident.entity';\r\nimport { IncidentTask } from '../../domain/entities/incident-task.entity';\r\nimport { IncidentEvidence } from '../../domain/entities/incident-evidence.entity';\r\nimport { IncidentTimeline } from '../../domain/entities/incident-timeline.entity';\r\nimport { ResponsePlan } from '../../domain/entities/response-plan.entity';\r\nimport { LoggerService } from '../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../infrastructure/logging/audit/audit.service';\r\nimport { NotificationService } from './notification.service';\r\nimport { ResponseOrchestrationService } from './response-orchestration.service';\r\n\r\ndescribe('IncidentManagementService', () => {\r\n  let service: IncidentManagementService;\r\n  let incidentRepository: Repository<Incident>;\r\n  let taskRepository: Repository<IncidentTask>;\r\n  let evidenceRepository: Repository<IncidentEvidence>;\r\n  let timelineRepository: Repository<IncidentTimeline>;\r\n  let responsePlanRepository: Repository<ResponsePlan>;\r\n  let loggerService: LoggerService;\r\n  let auditService: AuditService;\r\n  let notificationService: NotificationService;\r\n  let responseOrchestrationService: ResponseOrchestrationService;\r\n\r\n  const mockIncidentRepository = {\r\n    create: jest.fn(),\r\n    save: jest.fn(),\r\n    findOne: jest.fn(),\r\n    find: jest.fn(),\r\n    createQueryBuilder: jest.fn(),\r\n    count: jest.fn(),\r\n    remove: jest.fn(),\r\n  };\r\n\r\n  const mockTaskRepository = {\r\n    create: jest.fn(),\r\n    save: jest.fn(),\r\n    findOne: jest.fn(),\r\n    find: jest.fn(),\r\n  };\r\n\r\n  const mockEvidenceRepository = {\r\n    create: jest.fn(),\r\n    save: jest.fn(),\r\n    findOne: jest.fn(),\r\n    find: jest.fn(),\r\n  };\r\n\r\n  const mockTimelineRepository = {\r\n    create: jest.fn(),\r\n    save: jest.fn(),\r\n    findOne: jest.fn(),\r\n    find: jest.fn(),\r\n  };\r\n\r\n  const mockResponsePlanRepository = {\r\n    find: jest.fn(),\r\n    findOne: jest.fn(),\r\n  };\r\n\r\n  const mockLoggerService = {\r\n    debug: jest.fn(),\r\n    log: jest.fn(),\r\n    warn: jest.fn(),\r\n    error: jest.fn(),\r\n  };\r\n\r\n  const mockAuditService = {\r\n    logUserAction: jest.fn(),\r\n  };\r\n\r\n  const mockNotificationService = {\r\n    sendIncidentNotification: jest.fn(),\r\n  };\r\n\r\n  const mockResponseOrchestrationService = {\r\n    executeResponsePlan: jest.fn(),\r\n  };\r\n\r\n  const mockQueryBuilder = {\r\n    where: jest.fn().mockReturnThis(),\r\n    andWhere: jest.fn().mockReturnThis(),\r\n    orderBy: jest.fn().mockReturnThis(),\r\n    skip: jest.fn().mockReturnThis(),\r\n    take: jest.fn().mockReturnThis(),\r\n    getManyAndCount: jest.fn(),\r\n    getCount: jest.fn(),\r\n    select: jest.fn().mockReturnThis(),\r\n    addSelect: jest.fn().mockReturnThis(),\r\n    groupBy: jest.fn().mockReturnThis(),\r\n    getRawMany: jest.fn(),\r\n    getRawOne: jest.fn(),\r\n  };\r\n\r\n  const mockIncidentData = {\r\n    title: 'Test Incident',\r\n    description: 'Test incident description',\r\n    severity: 'high' as const,\r\n    category: 'phishing',\r\n    source: 'manual',\r\n    detectedAt: new Date(),\r\n    tags: ['test'],\r\n    confidentiality: 'internal' as const,\r\n  };\r\n\r\n  const mockIncident: Partial<Incident> = {\r\n    id: 'incident-123',\r\n    title: 'Test Incident',\r\n    description: 'Test incident description',\r\n    status: 'new',\r\n    severity: 'high',\r\n    priority: 'high',\r\n    category: 'phishing',\r\n    source: 'manual',\r\n    detectedAt: new Date(),\r\n    tags: ['test'],\r\n    confidentiality: 'internal',\r\n    createdBy: 'user-123',\r\n    isOpen: true,\r\n    isCritical: true,\r\n    isOverdue: false,\r\n    ageInHours: 2,\r\n    affectedAssetCount: 1,\r\n    iocCount: 2,\r\n    calculateRiskScore: jest.fn().mockReturnValue(8.5),\r\n    acknowledge: jest.fn(),\r\n    escalate: jest.fn(),\r\n    resolve: jest.fn(),\r\n    close: jest.fn(),\r\n    addTeamMember: jest.fn(),\r\n    tasks: [],\r\n    evidence: [],\r\n    communications: [],\r\n    timeline: [],\r\n  };\r\n\r\n  beforeEach(async () => {\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        IncidentManagementService,\r\n        {\r\n          provide: getRepositoryToken(Incident),\r\n          useValue: mockIncidentRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(IncidentTask),\r\n          useValue: mockTaskRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(IncidentEvidence),\r\n          useValue: mockEvidenceRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(IncidentTimeline),\r\n          useValue: mockTimelineRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(ResponsePlan),\r\n          useValue: mockResponsePlanRepository,\r\n        },\r\n        {\r\n          provide: LoggerService,\r\n          useValue: mockLoggerService,\r\n        },\r\n        {\r\n          provide: AuditService,\r\n          useValue: mockAuditService,\r\n        },\r\n        {\r\n          provide: NotificationService,\r\n          useValue: mockNotificationService,\r\n        },\r\n        {\r\n          provide: ResponseOrchestrationService,\r\n          useValue: mockResponseOrchestrationService,\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    service = module.get<IncidentManagementService>(IncidentManagementService);\r\n    incidentRepository = module.get<Repository<Incident>>(getRepositoryToken(Incident));\r\n    taskRepository = module.get<Repository<IncidentTask>>(getRepositoryToken(IncidentTask));\r\n    evidenceRepository = module.get<Repository<IncidentEvidence>>(getRepositoryToken(IncidentEvidence));\r\n    timelineRepository = module.get<Repository<IncidentTimeline>>(getRepositoryToken(IncidentTimeline));\r\n    responsePlanRepository = module.get<Repository<ResponsePlan>>(getRepositoryToken(ResponsePlan));\r\n    loggerService = module.get<LoggerService>(LoggerService);\r\n    auditService = module.get<AuditService>(AuditService);\r\n    notificationService = module.get<NotificationService>(NotificationService);\r\n    responseOrchestrationService = module.get<ResponseOrchestrationService>(ResponseOrchestrationService);\r\n\r\n    // Setup query builder mock\r\n    mockIncidentRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);\r\n  });\r\n\r\n  afterEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('createIncident', () => {\r\n    it('should create incident successfully', async () => {\r\n      const userId = 'user-123';\r\n      const savedIncident = { ...mockIncident, id: 'incident-123' };\r\n\r\n      mockIncidentRepository.create.mockReturnValue(mockIncident);\r\n      mockIncidentRepository.save.mockResolvedValue(savedIncident);\r\n      mockTimelineRepository.create.mockReturnValue({});\r\n      mockTimelineRepository.save.mockResolvedValue({});\r\n      mockResponsePlanRepository.find.mockResolvedValue([]);\r\n\r\n      const result = await service.createIncident(mockIncidentData, userId);\r\n\r\n      expect(mockIncidentRepository.create).toHaveBeenCalledWith({\r\n        ...mockIncidentData,\r\n        priority: 'high', // calculated from severity\r\n        createdBy: userId,\r\n      });\r\n      expect(mockIncidentRepository.save).toHaveBeenCalledWith(mockIncident);\r\n      expect(mockTimelineRepository.create).toHaveBeenCalled();\r\n      expect(mockNotificationService.sendIncidentNotification).toHaveBeenCalledWith(savedIncident, 'created');\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        userId,\r\n        'create',\r\n        'incident',\r\n        savedIncident.id,\r\n        expect.objectContaining({\r\n          title: mockIncidentData.title,\r\n          severity: mockIncidentData.severity,\r\n          category: mockIncidentData.category,\r\n        }),\r\n      );\r\n      expect(result).toEqual(savedIncident);\r\n    });\r\n\r\n    it('should apply response plan if applicable', async () => {\r\n      const userId = 'user-123';\r\n      const savedIncident = { ...mockIncident, id: 'incident-123' };\r\n      const responsePlan = {\r\n        id: 'plan-123',\r\n        name: 'Phishing Response Plan',\r\n        isApplicableToIncident: jest.fn().mockReturnValue(true),\r\n      };\r\n\r\n      mockIncidentRepository.create.mockReturnValue(mockIncident);\r\n      mockIncidentRepository.save.mockResolvedValue(savedIncident);\r\n      mockTimelineRepository.create.mockReturnValue({});\r\n      mockTimelineRepository.save.mockResolvedValue({});\r\n      mockResponsePlanRepository.find.mockResolvedValue([responsePlan]);\r\n\r\n      await service.createIncident(mockIncidentData, userId);\r\n\r\n      expect(responsePlan.isApplicableToIncident).toHaveBeenCalledWith(mockIncident);\r\n      expect(mockResponseOrchestrationService.executeResponsePlan).toHaveBeenCalledWith(\r\n        savedIncident.id,\r\n        responsePlan.id,\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('findById', () => {\r\n    it('should return incident when found', async () => {\r\n      const incidentId = 'incident-123';\r\n      mockIncidentRepository.findOne.mockResolvedValue(mockIncident);\r\n\r\n      const result = await service.findById(incidentId);\r\n\r\n      expect(mockIncidentRepository.findOne).toHaveBeenCalledWith({\r\n        where: { id: incidentId },\r\n        relations: ['tasks', 'evidence', 'communications', 'timeline', 'responsePlan'],\r\n      });\r\n      expect(result).toEqual(mockIncident);\r\n    });\r\n\r\n    it('should return null when incident not found', async () => {\r\n      const incidentId = 'non-existent';\r\n      mockIncidentRepository.findOne.mockResolvedValue(null);\r\n\r\n      const result = await service.findById(incidentId);\r\n\r\n      expect(result).toBeNull();\r\n    });\r\n  });\r\n\r\n  describe('findMany', () => {\r\n    it('should return paginated incidents with filters', async () => {\r\n      const options = {\r\n        page: 1,\r\n        limit: 10,\r\n        status: ['new', 'investigating'],\r\n        severity: ['high', 'critical'],\r\n        search: 'phishing',\r\n      };\r\n\r\n      const incidents = [mockIncident];\r\n      const total = 1;\r\n\r\n      mockQueryBuilder.getManyAndCount.mockResolvedValue([incidents, total]);\r\n\r\n      const result = await service.findMany(options);\r\n\r\n      expect(mockIncidentRepository.createQueryBuilder).toHaveBeenCalledWith('incident');\r\n      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('incident.status IN (:...status)', { status: options.status });\r\n      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('incident.severity IN (:...severity)', { severity: options.severity });\r\n      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(\r\n        '(incident.title ILIKE :search OR incident.description ILIKE :search)',\r\n        { search: '%phishing%' },\r\n      );\r\n      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith('incident.createdAt', 'DESC');\r\n      expect(mockQueryBuilder.skip).toHaveBeenCalledWith(0);\r\n      expect(mockQueryBuilder.take).toHaveBeenCalledWith(10);\r\n\r\n      expect(result).toEqual({\r\n        incidents,\r\n        total,\r\n        page: 1,\r\n        totalPages: 1,\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('acknowledgeIncident', () => {\r\n    it('should acknowledge incident successfully', async () => {\r\n      const incidentId = 'incident-123';\r\n      const userId = 'user-123';\r\n      const incident = { ...mockIncident, status: 'new' };\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(incident as Incident);\r\n      mockIncidentRepository.save.mockResolvedValue(incident);\r\n      mockTimelineRepository.create.mockReturnValue({});\r\n      mockTimelineRepository.save.mockResolvedValue({});\r\n\r\n      const result = await service.acknowledgeIncident(incidentId, userId);\r\n\r\n      expect(incident.acknowledge).toHaveBeenCalledWith(userId);\r\n      expect(mockIncidentRepository.save).toHaveBeenCalledWith(incident);\r\n      expect(mockTimelineRepository.create).toHaveBeenCalled();\r\n      expect(mockNotificationService.sendIncidentNotification).toHaveBeenCalledWith(incident, 'acknowledged');\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        userId,\r\n        'acknowledge',\r\n        'incident',\r\n        incidentId,\r\n        expect.any(Object),\r\n      );\r\n      expect(result).toEqual(incident);\r\n    });\r\n\r\n    it('should throw error when incident not found', async () => {\r\n      const incidentId = 'non-existent';\r\n      const userId = 'user-123';\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(null);\r\n\r\n      await expect(service.acknowledgeIncident(incidentId, userId))\r\n        .rejects.toThrow('Incident not found');\r\n    });\r\n\r\n    it('should throw error when incident already acknowledged', async () => {\r\n      const incidentId = 'incident-123';\r\n      const userId = 'user-123';\r\n      const incident = { ...mockIncident, status: 'investigating' };\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(incident as Incident);\r\n\r\n      await expect(service.acknowledgeIncident(incidentId, userId))\r\n        .rejects.toThrow('Incident has already been acknowledged');\r\n    });\r\n  });\r\n\r\n  describe('escalateIncident', () => {\r\n    it('should escalate incident successfully', async () => {\r\n      const incidentId = 'incident-123';\r\n      const userId = 'user-123';\r\n      const reason = 'Critical data breach detected';\r\n      const incident = { ...mockIncident };\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(incident as Incident);\r\n      mockIncidentRepository.save.mockResolvedValue(incident);\r\n      mockTimelineRepository.create.mockReturnValue({});\r\n      mockTimelineRepository.save.mockResolvedValue({});\r\n\r\n      const result = await service.escalateIncident(incidentId, reason, userId);\r\n\r\n      expect(incident.escalate).toHaveBeenCalledWith(reason, userId);\r\n      expect(mockIncidentRepository.save).toHaveBeenCalledWith(incident);\r\n      expect(mockTimelineRepository.create).toHaveBeenCalled();\r\n      expect(mockNotificationService.sendIncidentNotification).toHaveBeenCalledWith(incident, 'escalated');\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        userId,\r\n        'escalate',\r\n        'incident',\r\n        incidentId,\r\n        expect.objectContaining({ reason }),\r\n      );\r\n      expect(result).toEqual(incident);\r\n    });\r\n  });\r\n\r\n  describe('resolveIncident', () => {\r\n    it('should resolve incident successfully', async () => {\r\n      const incidentId = 'incident-123';\r\n      const userId = 'user-123';\r\n      const resolution = 'Phishing emails blocked, credentials reset';\r\n      const incident = { ...mockIncident };\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(incident as Incident);\r\n      mockIncidentRepository.save.mockResolvedValue(incident);\r\n      mockTimelineRepository.create.mockReturnValue({});\r\n      mockTimelineRepository.save.mockResolvedValue({});\r\n\r\n      const result = await service.resolveIncident(incidentId, resolution, userId);\r\n\r\n      expect(incident.resolve).toHaveBeenCalledWith(userId, resolution);\r\n      expect(mockIncidentRepository.save).toHaveBeenCalledWith(incident);\r\n      expect(mockTimelineRepository.create).toHaveBeenCalled();\r\n      expect(mockNotificationService.sendIncidentNotification).toHaveBeenCalledWith(incident, 'resolved');\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        userId,\r\n        'resolve',\r\n        'incident',\r\n        incidentId,\r\n        expect.objectContaining({ resolution }),\r\n      );\r\n      expect(result).toEqual(incident);\r\n    });\r\n  });\r\n\r\n  describe('addTeamMember', () => {\r\n    it('should add team member successfully', async () => {\r\n      const incidentId = 'incident-123';\r\n      const userId = 'user-456';\r\n      const role = 'analyst';\r\n      const permissions = ['view_evidence', 'update_tasks'];\r\n      const addedBy = 'user-123';\r\n      const incident = { ...mockIncident };\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(incident as Incident);\r\n      mockIncidentRepository.save.mockResolvedValue(incident);\r\n      mockTimelineRepository.create.mockReturnValue({});\r\n      mockTimelineRepository.save.mockResolvedValue({});\r\n\r\n      const result = await service.addTeamMember(incidentId, userId, role, permissions, addedBy);\r\n\r\n      expect(incident.addTeamMember).toHaveBeenCalledWith(userId, role, permissions);\r\n      expect(mockIncidentRepository.save).toHaveBeenCalledWith(incident);\r\n      expect(mockTimelineRepository.create).toHaveBeenCalled();\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        addedBy,\r\n        'add_team_member',\r\n        'incident',\r\n        incidentId,\r\n        expect.objectContaining({ userId, role, permissions }),\r\n      );\r\n      expect(result).toEqual(incident);\r\n    });\r\n  });\r\n\r\n  describe('getStatistics', () => {\r\n    it('should return incident statistics', async () => {\r\n      const mockStats = {\r\n        totalIncidents: 100,\r\n        openIncidents: 25,\r\n        criticalIncidents: 5,\r\n        overdueIncidents: 3,\r\n        byStatus: { new: 10, investigating: 15 },\r\n        bySeverity: { high: 20, critical: 5 },\r\n        performance: {\r\n          averageResolutionTime: 240,\r\n          slaCompliance: 85.5,\r\n        },\r\n      };\r\n\r\n      mockIncidentRepository.count.mockResolvedValueOnce(100); // totalIncidents\r\n      mockIncidentRepository.count.mockResolvedValueOnce(25);  // openIncidents\r\n      mockIncidentRepository.count.mockResolvedValueOnce(5);   // criticalIncidents\r\n\r\n      // Mock helper methods\r\n      jest.spyOn(service as any, 'getOverdueIncidentsCount').mockResolvedValue(3);\r\n      jest.spyOn(service as any, 'getIncidentsByStatus').mockResolvedValue(mockStats.byStatus);\r\n      jest.spyOn(service as any, 'getIncidentsBySeverity').mockResolvedValue(mockStats.bySeverity);\r\n      jest.spyOn(service as any, 'getAverageResolutionTime').mockResolvedValue(240);\r\n      jest.spyOn(service as any, 'getSlaCompliance').mockResolvedValue(85.5);\r\n\r\n      const result = await service.getStatistics();\r\n\r\n      expect(result).toMatchObject({\r\n        totalIncidents: 100,\r\n        openIncidents: 25,\r\n        criticalIncidents: 5,\r\n        overdueIncidents: 3,\r\n        byStatus: mockStats.byStatus,\r\n        bySeverity: mockStats.bySeverity,\r\n        performance: mockStats.performance,\r\n      });\r\n      expect(result.timestamp).toBeDefined();\r\n    });\r\n  });\r\n});\r\n"], "version": 3}