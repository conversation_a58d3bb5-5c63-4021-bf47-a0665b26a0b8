0412fc23425bc99b2eb9be1458dcf238
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AuthService_1;
var _a, _b, _c, _d, _e, _f, _g;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const password_service_1 = require("./services/password.service");
const token_service_1 = require("./services/token.service");
const rbac_service_1 = require("./rbac/rbac.service");
// Import entities (these will be created later)
const user_entity_1 = require("../../modules/user-management/domain/entities/user.entity");
const role_entity_1 = require("../../modules/user-management/domain/entities/role.entity");
const refresh_token_entity_1 = require("../../modules/user-management/domain/entities/refresh-token.entity");
/**
 * Authentication service that handles user authentication and authorization
 * Provides methods for login, logout, token management, and user validation
 */
let AuthService = AuthService_1 = class AuthService {
    constructor(userRepository, roleRepository, refreshTokenRepository, passwordService, tokenService, rbacService, configService) {
        this.userRepository = userRepository;
        this.roleRepository = roleRepository;
        this.refreshTokenRepository = refreshTokenRepository;
        this.passwordService = passwordService;
        this.tokenService = tokenService;
        this.rbacService = rbacService;
        this.configService = configService;
        this.logger = new common_1.Logger(AuthService_1.name);
    }
    /**
     * Validate user credentials for local authentication
     * @param email User email
     * @param password Plain text password
     * @returns Promise<User | null> User object if valid, null otherwise
     */
    async validateUser(email, password) {
        try {
            this.logger.debug('Validating user credentials', { email });
            const user = await this.userRepository.findOne({
                where: { email: email.toLowerCase() },
                relations: ['roles'],
            });
            if (!user) {
                this.logger.warn('User not found', { email });
                return null;
            }
            if (!user.isActive) {
                this.logger.warn('User account is inactive', { email, userId: user.id });
                throw new common_1.UnauthorizedException('Account is inactive');
            }
            if (user.lockedUntil && user.lockedUntil > new Date()) {
                this.logger.warn('User account is locked', {
                    email,
                    userId: user.id,
                    lockedUntil: user.lockedUntil
                });
                throw new common_1.UnauthorizedException('Account is temporarily locked');
            }
            const isPasswordValid = await this.passwordService.validatePassword(password, user.passwordHash);
            if (!isPasswordValid) {
                await this.handleFailedLogin(user);
                this.logger.warn('Invalid password attempt', { email, userId: user.id });
                return null;
            }
            // Reset failed login attempts on successful validation
            if (user.failedLoginAttempts > 0) {
                await this.resetFailedLoginAttempts(user);
            }
            this.logger.log('User credentials validated successfully', {
                email,
                userId: user.id
            });
            return user;
        }
        catch (error) {
            this.logger.error('Error validating user credentials', {
                email,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Authenticate user and generate tokens
     * @param user User object
     * @param ipAddress Client IP address
     * @param userAgent Client user agent
     * @returns Promise<AuthResult> Authentication result with tokens
     */
    async login(user, ipAddress, userAgent) {
        try {
            this.logger.debug('Generating authentication tokens', {
                userId: user.id,
                email: user.email
            });
            // Get user permissions
            const permissions = await this.rbacService.getUserPermissions(user.id);
            // Generate access token
            const payload = {
                sub: user.id,
                email: user.email,
                roles: user.roles?.map(role => role.name) || [],
                permissions,
            };
            const accessToken = await this.tokenService.generateAccessToken(payload);
            const refreshToken = await this.tokenService.generateRefreshToken(user.id);
            // Update last login
            await this.updateLastLogin(user, ipAddress, userAgent);
            // Clean user object for response
            const { passwordHash, ...userResponse } = user;
            const authConfig = this.configService.get('auth');
            const expiresIn = this.parseExpirationTime(authConfig.jwt.expiresIn);
            this.logger.log('User authenticated successfully', {
                userId: user.id,
                email: user.email,
                ipAddress,
            });
            return {
                accessToken,
                refreshToken,
                user: userResponse,
                expiresIn,
            };
        }
        catch (error) {
            this.logger.error('Error during user authentication', {
                userId: user.id,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Refresh access token using refresh token
     * @param refreshToken Refresh token string
     * @returns Promise<AuthResult> New authentication tokens
     */
    async refreshTokens(refreshToken) {
        try {
            this.logger.debug('Refreshing authentication tokens');
            const tokenRecord = await this.refreshTokenRepository.findOne({
                where: { tokenHash: await this.passwordService.hashPassword(refreshToken) },
                relations: ['user', 'user.roles'],
            });
            if (!tokenRecord || tokenRecord.isRevoked || tokenRecord.expiresAt < new Date()) {
                this.logger.warn('Invalid or expired refresh token');
                throw new common_1.UnauthorizedException('Invalid refresh token');
            }
            const user = tokenRecord.user;
            if (!user.isActive) {
                this.logger.warn('User account is inactive', { userId: user.id });
                throw new common_1.UnauthorizedException('Account is inactive');
            }
            // Revoke old refresh token
            await this.revokeRefreshToken(tokenRecord.id);
            // Generate new tokens
            const permissions = await this.rbacService.getUserPermissions(user.id);
            const payload = {
                sub: user.id,
                email: user.email,
                roles: user.roles?.map(role => role.name) || [],
                permissions,
            };
            const newAccessToken = await this.tokenService.generateAccessToken(payload);
            const newRefreshToken = await this.tokenService.generateRefreshToken(user.id);
            const authConfig = this.configService.get('auth');
            const expiresIn = this.parseExpirationTime(authConfig.jwt.expiresIn);
            this.logger.log('Tokens refreshed successfully', { userId: user.id });
            return {
                accessToken: newAccessToken,
                refreshToken: newRefreshToken,
                expiresIn,
            };
        }
        catch (error) {
            this.logger.error('Error refreshing tokens', {
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Logout user and revoke refresh token
     * @param refreshToken Refresh token to revoke
     * @returns Promise<void>
     */
    async logout(refreshToken) {
        try {
            this.logger.debug('Logging out user');
            const tokenRecord = await this.refreshTokenRepository.findOne({
                where: { tokenHash: await this.passwordService.hashPassword(refreshToken) },
            });
            if (tokenRecord) {
                await this.revokeRefreshToken(tokenRecord.id);
                this.logger.log('User logged out successfully', {
                    tokenId: tokenRecord.id
                });
            }
        }
        catch (error) {
            this.logger.error('Error during logout', {
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Validate JWT payload and return user
     * @param payload JWT payload
     * @returns Promise<User> User object
     */
    async validateJwtPayload(payload) {
        try {
            const user = await this.userRepository.findOne({
                where: { id: payload.sub },
                relations: ['roles'],
            });
            if (!user || !user.isActive) {
                throw new common_1.UnauthorizedException('User not found or inactive');
            }
            return user;
        }
        catch (error) {
            this.logger.error('Error validating JWT payload', {
                payload,
                error: error.message,
            });
            throw new common_1.UnauthorizedException('Invalid token');
        }
    }
    /**
     * Handle failed login attempt
     * @param user User object
     * @returns Promise<void>
     */
    async handleFailedLogin(user) {
        const authConfig = this.configService.get('auth');
        const maxAttempts = authConfig.security.maxLoginAttempts;
        const lockoutDuration = authConfig.security.lockoutDuration;
        user.failedLoginAttempts += 1;
        if (user.failedLoginAttempts >= maxAttempts) {
            user.lockedUntil = new Date(Date.now() + lockoutDuration);
            this.logger.warn('User account locked due to failed login attempts', {
                userId: user.id,
                attempts: user.failedLoginAttempts,
                lockedUntil: user.lockedUntil,
            });
        }
        await this.userRepository.save(user);
    }
    /**
     * Reset failed login attempts
     * @param user User object
     * @returns Promise<void>
     */
    async resetFailedLoginAttempts(user) {
        user.failedLoginAttempts = 0;
        user.lockedUntil = null;
        await this.userRepository.save(user);
    }
    /**
     * Update user's last login timestamp
     * @param user User object
     * @param ipAddress Client IP address
     * @param userAgent Client user agent
     * @returns Promise<void>
     */
    async updateLastLogin(user, ipAddress, userAgent) {
        user.lastLoginAt = new Date();
        await this.userRepository.save(user);
        // Log login event for audit
        this.logger.log('User login recorded', {
            userId: user.id,
            email: user.email,
            ipAddress,
            userAgent,
            timestamp: user.lastLoginAt,
        });
    }
    /**
     * Revoke refresh token
     * @param tokenId Token ID to revoke
     * @returns Promise<void>
     */
    async revokeRefreshToken(tokenId) {
        await this.refreshTokenRepository.update(tokenId, { isRevoked: true });
    }
    /**
     * Parse expiration time string to seconds
     * @param expiresIn Expiration time string (e.g., '1h', '30m')
     * @returns number Expiration time in seconds
     */
    parseExpirationTime(expiresIn) {
        const match = expiresIn.match(/^(\d+)([smhd])$/);
        if (!match)
            return 3600; // Default to 1 hour
        const value = parseInt(match[1]);
        const unit = match[2];
        switch (unit) {
            case 's': return value;
            case 'm': return value * 60;
            case 'h': return value * 3600;
            case 'd': return value * 86400;
            default: return 3600;
        }
    }
    /**
     * Find or create OAuth user
     * @param oauthData OAuth user data
     * @returns User entity
     */
    async findOrCreateOAuthUser(oauthData) {
        try {
            // Find existing user by email
            let user = await this.userRepository.findOne({
                where: { email: oauthData.email.toLowerCase() },
                relations: ['roles'],
            });
            if (user) {
                // Update OAuth provider data
                user.metadata = {
                    ...user.metadata,
                    oauth: {
                        ...user.metadata?.oauth,
                        [oauthData.provider]: {
                            providerId: oauthData.providerId,
                            lastLogin: new Date(),
                            providerData: oauthData.providerData,
                        },
                    },
                };
                // Update avatar if provided and user doesn't have one
                if (oauthData.avatarUrl && !user.avatarUrl) {
                    user.avatarUrl = oauthData.avatarUrl;
                }
                // Mark email as verified for OAuth users
                if (!user.emailVerified) {
                    user.emailVerified = true;
                    user.status = 'active';
                }
                await this.userRepository.save(user);
                return user;
            }
            // Create new user for OAuth
            const defaultRole = await this.getDefaultRole();
            user = this.userRepository.create({
                email: oauthData.email.toLowerCase(),
                firstName: oauthData.firstName,
                lastName: oauthData.lastName,
                avatarUrl: oauthData.avatarUrl,
                emailVerified: true,
                status: 'active',
                passwordHash: '', // OAuth users don't have passwords
                roles: defaultRole ? [defaultRole] : [],
                metadata: {
                    oauth: {
                        [oauthData.provider]: {
                            providerId: oauthData.providerId,
                            createdAt: new Date(),
                            providerData: oauthData.providerData,
                        },
                    },
                },
            });
            return await this.userRepository.save(user);
        }
        catch (error) {
            this.logger.error('Error finding or creating OAuth user', {
                email: oauthData.email,
                provider: oauthData.provider,
                error: error.message,
            });
            return null;
        }
    }
    /**
     * Find or create SAML user
     * @param samlData SAML user data
     * @returns User entity
     */
    async findOrCreateSamlUser(samlData) {
        try {
            // Find existing user by email
            let user = await this.userRepository.findOne({
                where: { email: samlData.email.toLowerCase() },
                relations: ['roles'],
            });
            if (user) {
                // Update SAML provider data
                user.metadata = {
                    ...user.metadata,
                    saml: {
                        ...user.metadata?.saml,
                        providerId: samlData.providerId,
                        lastLogin: new Date(),
                        providerData: samlData.providerData,
                    },
                };
                // Mark email as verified for SAML users
                if (!user.emailVerified) {
                    user.emailVerified = true;
                    user.status = 'active';
                }
                await this.userRepository.save(user);
                return user;
            }
            // Create new user for SAML
            const defaultRole = await this.getDefaultRole();
            user = this.userRepository.create({
                email: samlData.email.toLowerCase(),
                firstName: samlData.firstName,
                lastName: samlData.lastName,
                emailVerified: true,
                status: 'active',
                passwordHash: '', // SAML users don't have passwords
                roles: defaultRole ? [defaultRole] : [],
                metadata: {
                    saml: {
                        providerId: samlData.providerId,
                        createdAt: new Date(),
                        providerData: samlData.providerData,
                    },
                },
            });
            return await this.userRepository.save(user);
        }
        catch (error) {
            this.logger.error('Error finding or creating SAML user', {
                email: samlData.email,
                provider: samlData.provider,
                error: error.message,
            });
            return null;
        }
    }
    /**
     * Find user by SAML nameID
     * @param nameId SAML nameID
     * @returns User entity or null
     */
    async findUserBySamlNameId(nameId) {
        try {
            const users = await this.userRepository
                .createQueryBuilder('user')
                .where("user.metadata->>'saml'->>'providerId' = :nameId", { nameId })
                .getMany();
            return users.length > 0 ? users[0] : null;
        }
        catch (error) {
            this.logger.error('Error finding user by SAML nameID', {
                nameId,
                error: error.message,
            });
            return null;
        }
    }
    /**
     * Revoke all user tokens
     * @param userId User ID
     * @param reason Revocation reason
     * @returns Number of tokens revoked
     */
    async revokeAllUserTokens(userId, reason) {
        try {
            const result = await this.refreshTokenRepository.update({ userId, isRevoked: false }, {
                isRevoked: true,
                revokedAt: new Date(),
                revokedReason: reason || 'manual_revocation',
            });
            return result.affected || 0;
        }
        catch (error) {
            this.logger.error('Error revoking all user tokens', {
                userId,
                error: error.message,
            });
            return 0;
        }
    }
    /**
     * Get default role for new users
     * @returns Default role or null
     */
    async getDefaultRole() {
        try {
            return await this.roleRepository.findOne({
                where: { isDefault: true, isActive: true },
            });
        }
        catch (error) {
            this.logger.error('Error getting default role', {
                error: error.message,
            });
            return null;
        }
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = AuthService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(role_entity_1.Role)),
    __param(2, (0, typeorm_1.InjectRepository)(refresh_token_entity_1.RefreshToken)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _c : Object, typeof (_d = typeof password_service_1.PasswordService !== "undefined" && password_service_1.PasswordService) === "function" ? _d : Object, typeof (_e = typeof token_service_1.TokenService !== "undefined" && token_service_1.TokenService) === "function" ? _e : Object, typeof (_f = typeof rbac_service_1.RbacService !== "undefined" && rbac_service_1.RbacService) === "function" ? _f : Object, typeof (_g = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _g : Object])
], AuthService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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