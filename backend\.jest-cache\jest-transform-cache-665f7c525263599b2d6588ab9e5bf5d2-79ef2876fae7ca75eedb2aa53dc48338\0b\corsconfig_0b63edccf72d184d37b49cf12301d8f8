6ddfe2581d169fbe885e1ca3c65bdeb7
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CorsConfig = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
/**
 * CORS configuration service for secure cross-origin resource sharing
 * Implements environment-specific CORS policies
 */
let CorsConfig = class CorsConfig {
    constructor(configService) {
        this.configService = configService;
    }
    /**
     * Generate CORS configuration based on environment
     */
    getCorsOptions() {
        const environment = this.configService.get('NODE_ENV', 'development');
        switch (environment) {
            case 'production':
                return this.getProductionCorsOptions();
            case 'staging':
                return this.getStagingCorsOptions();
            case 'test':
                return this.getTestCorsOptions();
            default:
                return this.getDevelopmentCorsOptions();
        }
    }
    /**
     * Production CORS configuration - Most restrictive
     */
    getProductionCorsOptions() {
        const allowedOrigins = this.configService.get('cors.allowedOrigins', []);
        return {
            origin: (origin, callback) => {
                // Allow requests with no origin (mobile apps, Postman, etc.)
                if (!origin) {
                    return callback(null, true);
                }
                // Check against whitelist
                if (allowedOrigins.includes(origin)) {
                    return callback(null, true);
                }
                // Log unauthorized origin attempts
                console.warn(`CORS: Blocked request from unauthorized origin: ${origin}`);
                return callback(new Error('Not allowed by CORS'), false);
            },
            methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
            allowedHeaders: [
                'Origin',
                'X-Requested-With',
                'Content-Type',
                'Accept',
                'Authorization',
                'X-API-Key',
                'X-Correlation-ID',
                'X-Request-ID',
                'Cache-Control',
            ],
            exposedHeaders: [
                'X-Total-Count',
                'X-Page-Count',
                'X-Current-Page',
                'X-Per-Page',
                'X-Rate-Limit-Limit',
                'X-Rate-Limit-Remaining',
                'X-Rate-Limit-Reset',
                'X-API-Version',
                'X-Request-ID',
            ],
            credentials: true,
            maxAge: 86400, // 24 hours
            preflightContinue: false,
            optionsSuccessStatus: 204,
        };
    }
    /**
     * Staging CORS configuration - Moderate restrictions
     */
    getStagingCorsOptions() {
        const allowedOrigins = this.configService.get('cors.allowedOrigins', [
            'https://staging.sentinel.com',
            'https://staging-admin.sentinel.com',
        ]);
        return {
            origin: allowedOrigins,
            methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS', 'HEAD'],
            allowedHeaders: [
                'Origin',
                'X-Requested-With',
                'Content-Type',
                'Accept',
                'Authorization',
                'X-API-Key',
                'X-Correlation-ID',
                'X-Request-ID',
                'Cache-Control',
                'X-Debug-Mode',
            ],
            exposedHeaders: [
                'X-Total-Count',
                'X-Page-Count',
                'X-Current-Page',
                'X-Per-Page',
                'X-Rate-Limit-Limit',
                'X-Rate-Limit-Remaining',
                'X-Rate-Limit-Reset',
                'X-API-Version',
                'X-Request-ID',
                'X-Debug-Info',
            ],
            credentials: true,
            maxAge: 3600, // 1 hour
            preflightContinue: false,
            optionsSuccessStatus: 204,
        };
    }
    /**
     * Development CORS configuration - Permissive for development
     */
    getDevelopmentCorsOptions() {
        return {
            origin: true, // Allow all origins in development
            methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS', 'HEAD'],
            allowedHeaders: [
                'Origin',
                'X-Requested-With',
                'Content-Type',
                'Accept',
                'Authorization',
                'X-API-Key',
                'X-Correlation-ID',
                'X-Request-ID',
                'Cache-Control',
                'X-Debug-Mode',
                'X-Test-Mode',
            ],
            exposedHeaders: [
                'X-Total-Count',
                'X-Page-Count',
                'X-Current-Page',
                'X-Per-Page',
                'X-Rate-Limit-Limit',
                'X-Rate-Limit-Remaining',
                'X-Rate-Limit-Reset',
                'X-API-Version',
                'X-Request-ID',
                'X-Debug-Info',
                'X-Performance-Metrics',
            ],
            credentials: true,
            maxAge: 300, // 5 minutes
            preflightContinue: false,
            optionsSuccessStatus: 200,
        };
    }
    /**
     * Test CORS configuration - Minimal restrictions for testing
     */
    getTestCorsOptions() {
        return {
            origin: true,
            methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS', 'HEAD'],
            allowedHeaders: '*',
            exposedHeaders: '*',
            credentials: true,
            maxAge: 0, // No caching in tests
            preflightContinue: false,
            optionsSuccessStatus: 200,
        };
    }
    /**
     * Validate CORS configuration
     */
    validateCorsConfig() {
        try {
            const corsOptions = this.getCorsOptions();
            const environment = this.configService.get('NODE_ENV', 'development');
            // Validate required properties exist
            if (!corsOptions.methods || !corsOptions.allowedHeaders) {
                throw new Error('CORS configuration missing required properties');
            }
            // Validate production security
            if (environment === 'production') {
                if (corsOptions.origin === true) {
                    throw new Error('Production CORS cannot allow all origins');
                }
                if (!corsOptions.credentials) {
                    console.warn('CORS: Credentials not enabled in production');
                }
            }
            return true;
        }
        catch (error) {
            console.error('CORS configuration validation failed:', error.message);
            return false;
        }
    }
    /**
     * Get CORS configuration summary for logging
     */
    getCorsConfigSummary() {
        const corsOptions = this.getCorsOptions();
        const environment = this.configService.get('NODE_ENV', 'development');
        return {
            environment,
            allowsCredentials: corsOptions.credentials,
            maxAge: corsOptions.maxAge,
            methodsCount: Array.isArray(corsOptions.methods) ? corsOptions.methods.length : 'all',
            headersCount: Array.isArray(corsOptions.allowedHeaders) ? corsOptions.allowedHeaders.length : 'all',
            originPolicy: typeof corsOptions.origin === 'boolean' ?
                (corsOptions.origin ? 'allow-all' : 'deny-all') : 'whitelist',
        };
    }
};
exports.CorsConfig = CorsConfig;
exports.CorsConfig = CorsConfig = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], CorsConfig);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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