{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\event-status-changed.domain-event.ts", "mappings": ";;;AAAA,6DAA4E;AAC5E,kEAAyD;AAkBzD;;;;;;;;;;GAUG;AACH,MAAa,6BAA8B,SAAQ,+BAA4C;IAC7F,YACE,WAA2B,EAC3B,SAAsC,EACtC,OAOC;QAED,KAAK,CAAC,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,IAAI,CAAC,SAAS,KAAK,+BAAW,CAAC,QAAQ,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,IAAI,CAAC,SAAS,KAAK,+BAAW,CAAC,QAAQ;YACvC,CAAC,+BAAW,CAAC,MAAM,EAAE,+BAAW,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAClF,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,IAAI,CAAC,SAAS,KAAK,+BAAW,CAAC,MAAM,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,OAAO,IAAI,CAAC,SAAS,KAAK,+BAAW,CAAC,cAAc,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,SAAS,KAAK,+BAAW,CAAC,OAAO,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,YAAY;QACV,MAAM,qBAAqB,GAAG;YAC5B,EAAE,IAAI,EAAE,+BAAW,CAAC,MAAM,EAAE,EAAE,EAAE,+BAAW,CAAC,aAAa,EAAE;SAC5D,CAAC;QAEF,OAAO,qBAAqB,CAAC,IAAI,CAC/B,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,KAAK,IAAI,CAAC,SAAS,IAAI,UAAU,CAAC,EAAE,KAAK,IAAI,CAAC,SAAS,CACrF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,MAAM,uBAAuB,GAAG;YAC9B,EAAE,IAAI,EAAE,+BAAW,CAAC,aAAa,EAAE,EAAE,EAAE,+BAAW,CAAC,MAAM,EAAE;YAC3D,EAAE,IAAI,EAAE,+BAAW,CAAC,aAAa,EAAE,EAAE,EAAE,+BAAW,CAAC,SAAS,EAAE;YAC9D,EAAE,IAAI,EAAE,+BAAW,CAAC,MAAM,EAAE,EAAE,EAAE,+BAAW,CAAC,SAAS,EAAE;SACxD,CAAC;QAEF,OAAO,uBAAuB,CAAC,IAAI,CACjC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,KAAK,IAAI,CAAC,SAAS,IAAI,UAAU,CAAC,EAAE,KAAK,IAAI,CAAC,SAAS,CACrF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,IAAI,IAAI,CAAC,WAAW,EAAE;YAAE,OAAO,YAAY,CAAC;QAC5C,IAAI,IAAI,CAAC,WAAW,EAAE;YAAE,OAAO,WAAW,CAAC;QAC3C,IAAI,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,sBAAsB,EAAE,IAAI,IAAI,CAAC,UAAU,EAAE;YAAE,OAAO,SAAS,CAAC;QAC7F,IAAI,IAAI,CAAC,YAAY,EAAE;YAAE,OAAO,YAAY,CAAC;QAC7C,IAAI,IAAI,CAAC,cAAc,EAAE;YAAE,OAAO,eAAe,CAAC;QAClD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,uBAAuB;QACrB,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAE1C,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,YAAY;gBACf,OAAO,MAAM,CAAC;YAChB,KAAK,YAAY;gBACf,OAAO,QAAQ,CAAC;YAClB,KAAK,WAAW;gBACd,OAAO,MAAM,CAAC;YAChB,KAAK,SAAS;gBACZ,OAAO,KAAK,CAAC;YACf;gBACE,OAAO,QAAQ,CAAC;QACpB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,iEAAiE;QACjE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,gBAAgB;QAad,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;YACpC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,IAAI,CAAC,iBAAiB,EAAE;YAClC,QAAQ,EAAE,IAAI,CAAC,uBAAuB,EAAE;YACxC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB,EAAE;YACjD,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE;YAC/B,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE;YAC/B,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE;YAC3B,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;SACxC,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,GAAG,KAAK,CAAC,MAAM,EAAE;YACjB,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE;SACvC,CAAC;IACJ,CAAC;CACF;AAnMD,sEAmMC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\event-status-changed.domain-event.ts"], "sourcesContent": ["import { BaseDomainEvent, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { EventStatus } from '../enums/event-status.enum';\r\n\r\n/**\r\n * Event Status Changed Domain Event Data\r\n */\r\nexport interface EventStatusChangedEventData {\r\n  /** Previous status of the event */\r\n  oldStatus: EventStatus;\r\n  /** New status of the event */\r\n  newStatus: EventStatus;\r\n  /** Who changed the status */\r\n  changedBy?: string;\r\n  /** Notes about the status change */\r\n  notes?: string;\r\n  /** When the status was changed */\r\n  timestamp: Date;\r\n}\r\n\r\n/**\r\n * Event Status Changed Domain Event\r\n * \r\n * Raised when a security event's status changes.\r\n * This event triggers various downstream processes including:\r\n * - Status change notifications\r\n * - Workflow transitions\r\n * - Metrics updates\r\n * - Audit logging\r\n * - SLA tracking\r\n */\r\nexport class EventStatusChangedDomainEvent extends BaseDomainEvent<EventStatusChangedEventData> {\r\n  constructor(\r\n    aggregateId: UniqueEntityId,\r\n    eventData: EventStatusChangedEventData,\r\n    options?: {\r\n      eventId?: UniqueEntityId;\r\n      occurredOn?: Date;\r\n      eventVersion?: number;\r\n      correlationId?: string;\r\n      causationId?: string;\r\n      metadata?: Record<string, any>;\r\n    }\r\n  ) {\r\n    super(aggregateId, eventData, options);\r\n  }\r\n\r\n  /**\r\n   * Get the previous status\r\n   */\r\n  get oldStatus(): EventStatus {\r\n    return this.eventData.oldStatus;\r\n  }\r\n\r\n  /**\r\n   * Get the new status\r\n   */\r\n  get newStatus(): EventStatus {\r\n    return this.eventData.newStatus;\r\n  }\r\n\r\n  /**\r\n   * Get who changed the status\r\n   */\r\n  get changedBy(): string | undefined {\r\n    return this.eventData.changedBy;\r\n  }\r\n\r\n  /**\r\n   * Get notes about the change\r\n   */\r\n  get notes(): string | undefined {\r\n    return this.eventData.notes;\r\n  }\r\n\r\n  /**\r\n   * Get when the status was changed\r\n   */\r\n  get timestamp(): Date {\r\n    return this.eventData.timestamp;\r\n  }\r\n\r\n  /**\r\n   * Check if event was resolved\r\n   */\r\n  wasResolved(): boolean {\r\n    return this.newStatus === EventStatus.RESOLVED;\r\n  }\r\n\r\n  /**\r\n   * Check if event was reopened\r\n   */\r\n  wasReopened(): boolean {\r\n    return this.oldStatus === EventStatus.RESOLVED && \r\n           [EventStatus.ACTIVE, EventStatus.INVESTIGATING].includes(this.newStatus);\r\n  }\r\n\r\n  /**\r\n   * Check if event was closed\r\n   */\r\n  wasClosed(): boolean {\r\n    return this.newStatus === EventStatus.CLOSED;\r\n  }\r\n\r\n  /**\r\n   * Check if event was marked as false positive\r\n   */\r\n  wasMarkedFalsePositive(): boolean {\r\n    return this.newStatus === EventStatus.FALSE_POSITIVE;\r\n  }\r\n\r\n  /**\r\n   * Check if event was ignored\r\n   */\r\n  wasIgnored(): boolean {\r\n    return this.newStatus === EventStatus.IGNORED;\r\n  }\r\n\r\n  /**\r\n   * Check if status change indicates escalation\r\n   */\r\n  isEscalation(): boolean {\r\n    const escalationTransitions = [\r\n      { from: EventStatus.ACTIVE, to: EventStatus.INVESTIGATING },\r\n    ];\r\n\r\n    return escalationTransitions.some(\r\n      transition => transition.from === this.oldStatus && transition.to === this.newStatus\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Check if status change indicates de-escalation\r\n   */\r\n  isDeEscalation(): boolean {\r\n    const deEscalationTransitions = [\r\n      { from: EventStatus.INVESTIGATING, to: EventStatus.ACTIVE },\r\n      { from: EventStatus.INVESTIGATING, to: EventStatus.MITIGATED },\r\n      { from: EventStatus.ACTIVE, to: EventStatus.MITIGATED },\r\n    ];\r\n\r\n    return deEscalationTransitions.some(\r\n      transition => transition.from === this.oldStatus && transition.to === this.newStatus\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get status change category\r\n   */\r\n  getChangeCategory(): 'resolution' | 'escalation' | 'de-escalation' | 'reopening' | 'closure' | 'other' {\r\n    if (this.wasResolved()) return 'resolution';\r\n    if (this.wasReopened()) return 'reopening';\r\n    if (this.wasClosed() || this.wasMarkedFalsePositive() || this.wasIgnored()) return 'closure';\r\n    if (this.isEscalation()) return 'escalation';\r\n    if (this.isDeEscalation()) return 'de-escalation';\r\n    return 'other';\r\n  }\r\n\r\n  /**\r\n   * Get priority level for notifications\r\n   */\r\n  getNotificationPriority(): 'low' | 'medium' | 'high' | 'critical' {\r\n    const category = this.getChangeCategory();\r\n    \r\n    switch (category) {\r\n      case 'escalation':\r\n        return 'high';\r\n      case 'resolution':\r\n        return 'medium';\r\n      case 'reopening':\r\n        return 'high';\r\n      case 'closure':\r\n        return 'low';\r\n      default:\r\n        return 'medium';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if change requires notification\r\n   */\r\n  requiresNotification(): boolean {\r\n    // All status changes typically require some form of notification\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Get change summary for handlers\r\n   */\r\n  getChangeSummary(): {\r\n    eventId: string;\r\n    oldStatus: EventStatus;\r\n    newStatus: EventStatus;\r\n    changedBy?: string;\r\n    category: string;\r\n    priority: string;\r\n    requiresNotification: boolean;\r\n    wasResolved: boolean;\r\n    wasReopened: boolean;\r\n    wasClosed: boolean;\r\n    timestamp: string;\r\n  } {\r\n    return {\r\n      eventId: this.aggregateId.toString(),\r\n      oldStatus: this.oldStatus,\r\n      newStatus: this.newStatus,\r\n      changedBy: this.changedBy,\r\n      category: this.getChangeCategory(),\r\n      priority: this.getNotificationPriority(),\r\n      requiresNotification: this.requiresNotification(),\r\n      wasResolved: this.wasResolved(),\r\n      wasReopened: this.wasReopened(),\r\n      wasClosed: this.wasClosed(),\r\n      timestamp: this.timestamp.toISOString(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      ...super.toJSON(),\r\n      changeSummary: this.getChangeSummary(),\r\n    };\r\n  }\r\n}"], "version": 3}