{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\common\\base-api.controller.ts", "mappings": ";;;;;;;;;;;;AAAA,2CAMwB;AASxB;;;GAGG;AAEI,IAAe,iBAAiB,GAAhC,MAAe,iBAAiB;IAGrC,YAAY,cAAsB;QAChC,IAAI,CAAC,MAAM,GAAG,IAAI,eAAM,CAAC,cAAc,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACO,qBAAqB,CAC7B,IAAO,EACP,OAAgB,EAChB,QAAc;QAEd,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI;YACJ,OAAO,EAAE,OAAO,IAAI,kCAAkC;YACtD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,QAAQ,EAAE,QAAQ,IAAI,EAAE;SACzB,CAAC;IACJ,CAAC;IAED;;OAEG;IACO,mBAAmB,CAC3B,KAAqB,EACrB,IAAa,EACb,OAAa;QAEb,MAAM,YAAY,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC;QACvE,MAAM,SAAS,GAAG,IAAI,IAAI,gBAAgB,CAAC;QAE3C,OAAO;YACL,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,YAAY;gBACrB,OAAO,EAAE,OAAO,IAAI,EAAE;gBACtB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACO,uBAAuB,CAC/B,IAAS,EACT,UAA0B,EAC1B,OAAgB;QAEhB,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI;YACJ,OAAO,EAAE,OAAO,IAAI,6BAA6B;YACjD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,UAAU;YACV,QAAQ,EAAE;gBACR,UAAU,EAAE,UAAU,CAAC,KAAK;gBAC5B,YAAY,EAAE,UAAU,CAAC,KAAK;gBAC9B,WAAW,EAAE,UAAU,CAAC,IAAI;gBAC5B,UAAU,EAAE,UAAU,CAAC,UAAU;aAClC;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACO,uBAAuB,CAAC,GAAmB;QACnD,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,IAAc,CAAC,IAAI,CAAC,CAAC,CAAC;QAClE,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,KAAe,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QACpF,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAElC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACO,oBAAoB,CAAC,GAAmB;QAChD,MAAM,MAAM,GAAI,GAAG,CAAC,KAAK,CAAC,MAAiB,IAAI,WAAW,CAAC;QAC3D,MAAM,SAAS,GAAI,GAAG,CAAC,KAAK,CAAC,SAA4B,IAAI,MAAM,CAAC;QAEpE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;IAC/B,CAAC;IAED;;OAEG;IACO,mBAAmB,CAAC,GAAmB;QAC/C,MAAM,OAAO,GAAwB,EAAE,CAAC;QAExC,mCAAmC;QACnC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACnC,IAAI,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9B,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;gBAC7C,OAAO,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACtC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACO,UAAU,CAClB,GAAyB,EACzB,MAAc,EACd,cAAoB;QAEpB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAgB,MAAM,EAAE,EAAE;YACxC,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,GAAG,EAAE,GAAG,CAAC,WAAW;YACpB,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC;YACpC,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;YACpB,aAAa,EAAE,GAAG,CAAC,OAAO,CAAC,kBAAkB,CAAC;YAC9C,GAAG,cAAc;SAClB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACO,WAAW,CACnB,GAAyB,EACzB,MAAc,EACd,OAAgB,EAChB,cAAuB,EACvB,cAAoB;QAEpB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,MAAM,EAAE,EAAE;YACzC,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,GAAG,EAAE,GAAG,CAAC,WAAW;YACpB,OAAO;YACP,cAAc,EAAE,cAAc,CAAC,CAAC,CAAC,GAAG,cAAc,IAAI,CAAC,CAAC,CAAC,SAAS;YAClE,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;YACpB,aAAa,EAAE,GAAG,CAAC,OAAO,CAAC,kBAAkB,CAAC;YAC9C,GAAG,cAAc;SAClB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACO,WAAW,CACnB,KAAY,EACZ,GAAyB,EACzB,MAAc;QAEd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,MAAM,EAAE,EAAE;YACxC,KAAK,EAAE,KAAK,CAAC,OAAO;YACpB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,GAAG,EAAE,GAAG,CAAC,WAAW;YACpB,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;YACpB,aAAa,EAAE,GAAG,CAAC,OAAO,CAAC,kBAAkB,CAAC;SAC/C,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACO,sBAAsB,CAC9B,MAA2B,EAC3B,cAAwB;QAExB,MAAM,aAAa,GAAG,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAClD,MAAM,CAAC,KAAK,CAAC,KAAK,SAAS,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,CAC9E,CAAC;QAEF,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,gCAAgC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAED;;OAEG;IACO,aAAa,CAAI,KAAQ;QACjC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,uBAAuB;YACvB,OAAO,KAAK,CAAC,OAAO,CAAC,qDAAqD,EAAE,EAAE,CAAM,CAAC;QACvF,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAM,CAAC;QAC1D,CAAC;QAED,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YAChD,MAAM,SAAS,GAAG,EAAO,CAAC;YAC1B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBAC/B,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;YACH,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACO,gBAAgB,CACxB,GAAyB,EACzB,mBAA6B;QAE7B,MAAM,eAAe,GAAG,GAAG,CAAC,IAAI,EAAE,WAAW,IAAI,EAAE,CAAC;QACpD,OAAO,mBAAmB,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,CAC5C,eAAe,CAAC,QAAQ,CAAC,UAAU,CAAC,CACrC,CAAC;IACJ,CAAC;IAED;;OAEG;IACO,cAAc,CAAC,GAAyB;QAChD,OAAO;YACL,EAAE,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;YAChB,KAAK,EAAE,GAAG,CAAC,IAAI,EAAE,KAAK;YACtB,KAAK,EAAE,GAAG,CAAC,IAAI,EAAE,KAAK,IAAI,EAAE;YAC5B,WAAW,EAAE,GAAG,CAAC,IAAI,EAAE,WAAW,IAAI,EAAE;YACxC,cAAc,EAAE,GAAG,CAAC,IAAI,EAAE,cAAc;SACzC,CAAC;IACJ,CAAC;IAED;;OAEG;IACO,cAAc,CACtB,GAAyB,EACzB,MAAc,EACd,QAAgB,EAChB,OAA8B,EAC9B,OAAa;QAEb,OAAO;YACL,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;YACpB,MAAM;YACN,QAAQ;YACR,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,GAAG,CAAC,EAAE;YACjB,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC;YACpC,aAAa,EAAE,GAAG,CAAC,OAAO,CAAC,kBAAkB,CAAW;YACxD,OAAO,EAAE,OAAO,IAAI,EAAE;SACvB,CAAC;IACJ,CAAC;CACF,CAAA;AA/PqB,8CAAiB;4BAAjB,iBAAiB;IADtC,IAAA,mBAAU,GAAE;;GACS,iBAAiB,CA+PtC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\common\\base-api.controller.ts"], "sourcesContent": ["import {\r\n  Controller,\r\n  HttpStatus,\r\n  Logger,\r\n  Request,\r\n  Response,\r\n} from '@nestjs/common';\r\nimport { ApiResponse, ApiTags } from '@nestjs/swagger';\r\nimport { Request as ExpressRequest, Response as ExpressResponse } from 'express';\r\n\r\n// Custom request interface with user context\r\ninterface AuthenticatedRequest extends ExpressRequest {\r\n  user?: UserContext;\r\n}\r\n\r\n/**\r\n * Base API Controller\r\n * Provides shared functionality for all API controllers\r\n */\r\n@Controller()\r\nexport abstract class BaseApiController {\r\n  protected readonly logger: Logger;\r\n\r\n  constructor(controllerName: string) {\r\n    this.logger = new Logger(controllerName);\r\n  }\r\n\r\n  /**\r\n   * Create standardized success response\r\n   */\r\n  protected createSuccessResponse<T>(\r\n    data: T,\r\n    message?: string,\r\n    metadata?: any,\r\n  ): ApiSuccessResponse<T> {\r\n    return {\r\n      success: true,\r\n      data,\r\n      message: message || 'Operation completed successfully',\r\n      timestamp: new Date().toISOString(),\r\n      metadata: metadata || {},\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create standardized error response\r\n   */\r\n  protected createErrorResponse(\r\n    error: string | Error,\r\n    code?: string,\r\n    details?: any,\r\n  ): ApiErrorResponse {\r\n    const errorMessage = typeof error === 'string' ? error : error.message;\r\n    const errorCode = code || 'INTERNAL_ERROR';\r\n\r\n    return {\r\n      success: false,\r\n      error: {\r\n        code: errorCode,\r\n        message: errorMessage,\r\n        details: details || {},\r\n        timestamp: new Date().toISOString(),\r\n      },\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create paginated response\r\n   */\r\n  protected createPaginatedResponse<T>(\r\n    data: T[],\r\n    pagination: PaginationInfo,\r\n    message?: string,\r\n  ): ApiPaginatedResponse<T> {\r\n    return {\r\n      success: true,\r\n      data,\r\n      message: message || 'Data retrieved successfully',\r\n      timestamp: new Date().toISOString(),\r\n      pagination,\r\n      metadata: {\r\n        totalItems: pagination.total,\r\n        itemsPerPage: pagination.limit,\r\n        currentPage: pagination.page,\r\n        totalPages: pagination.totalPages,\r\n      },\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Extract pagination parameters from request\r\n   */\r\n  protected extractPaginationParams(req: ExpressRequest): PaginationParams {\r\n    const page = Math.max(1, parseInt(req.query.page as string) || 1);\r\n    const limit = Math.min(100, Math.max(1, parseInt(req.query.limit as string) || 10));\r\n    const offset = (page - 1) * limit;\r\n\r\n    return { page, limit, offset };\r\n  }\r\n\r\n  /**\r\n   * Extract sorting parameters from request\r\n   */\r\n  protected extractSortingParams(req: ExpressRequest): SortingParams {\r\n    const sortBy = (req.query.sortBy as string) || 'createdAt';\r\n    const sortOrder = (req.query.sortOrder as 'asc' | 'desc') || 'desc';\r\n\r\n    return { sortBy, sortOrder };\r\n  }\r\n\r\n  /**\r\n   * Extract filtering parameters from request\r\n   */\r\n  protected extractFilterParams(req: ExpressRequest): FilterParams {\r\n    const filters: Record<string, any> = {};\r\n    \r\n    // Extract common filter parameters\r\n    Object.keys(req.query).forEach(key => {\r\n      if (key.startsWith('filter_')) {\r\n        const filterKey = key.replace('filter_', '');\r\n        filters[filterKey] = req.query[key];\r\n      }\r\n    });\r\n\r\n    return filters;\r\n  }\r\n\r\n  /**\r\n   * Log API request\r\n   */\r\n  protected logRequest(\r\n    req: AuthenticatedRequest,\r\n    action: string,\r\n    additionalData?: any,\r\n  ): void {\r\n    this.logger.log(`API Request: ${action}`, {\r\n      method: req.method,\r\n      url: req.originalUrl,\r\n      userAgent: req.headers['user-agent'],\r\n      ip: req.ip,\r\n      userId: req.user?.id,\r\n      correlationId: req.headers['x-correlation-id'],\r\n      ...additionalData,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Log API response\r\n   */\r\n  protected logResponse(\r\n    req: AuthenticatedRequest,\r\n    action: string,\r\n    success: boolean,\r\n    processingTime?: number,\r\n    additionalData?: any,\r\n  ): void {\r\n    this.logger.log(`API Response: ${action}`, {\r\n      method: req.method,\r\n      url: req.originalUrl,\r\n      success,\r\n      processingTime: processingTime ? `${processingTime}ms` : undefined,\r\n      userId: req.user?.id,\r\n      correlationId: req.headers['x-correlation-id'],\r\n      ...additionalData,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Handle API errors consistently\r\n   */\r\n  protected handleError(\r\n    error: Error,\r\n    req: AuthenticatedRequest,\r\n    action: string,\r\n  ): ApiErrorResponse {\r\n    this.logger.error(`API Error: ${action}`, {\r\n      error: error.message,\r\n      stack: error.stack,\r\n      method: req.method,\r\n      url: req.originalUrl,\r\n      userId: req.user?.id,\r\n      correlationId: req.headers['x-correlation-id'],\r\n    });\r\n\r\n    return this.createErrorResponse(error);\r\n  }\r\n\r\n  /**\r\n   * Validate required parameters\r\n   */\r\n  protected validateRequiredParams(\r\n    params: Record<string, any>,\r\n    requiredFields: string[],\r\n  ): void {\r\n    const missingFields = requiredFields.filter(field => \r\n      params[field] === undefined || params[field] === null || params[field] === ''\r\n    );\r\n\r\n    if (missingFields.length > 0) {\r\n      throw new Error(`Missing required parameters: ${missingFields.join(', ')}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Sanitize input data\r\n   */\r\n  protected sanitizeInput<T>(input: T): T {\r\n    if (typeof input === 'string') {\r\n      // Basic XSS protection\r\n      return input.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '') as T;\r\n    }\r\n\r\n    if (Array.isArray(input)) {\r\n      return input.map(item => this.sanitizeInput(item)) as T;\r\n    }\r\n\r\n    if (typeof input === 'object' && input !== null) {\r\n      const sanitized = {} as T;\r\n      Object.keys(input).forEach(key => {\r\n        sanitized[key] = this.sanitizeInput(input[key]);\r\n      });\r\n      return sanitized;\r\n    }\r\n\r\n    return input;\r\n  }\r\n\r\n  /**\r\n   * Check if user has required permissions\r\n   */\r\n  protected checkPermissions(\r\n    req: AuthenticatedRequest,\r\n    requiredPermissions: string[],\r\n  ): boolean {\r\n    const userPermissions = req.user?.permissions || [];\r\n    return requiredPermissions.every(permission => \r\n      userPermissions.includes(permission)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get user context from request\r\n   */\r\n  protected getUserContext(req: AuthenticatedRequest): UserContext {\r\n    return {\r\n      id: req.user?.id,\r\n      email: req.user?.email,\r\n      roles: req.user?.roles || [],\r\n      permissions: req.user?.permissions || [],\r\n      organizationId: req.user?.organizationId,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create audit log entry\r\n   */\r\n  protected createAuditLog(\r\n    req: AuthenticatedRequest,\r\n    action: string,\r\n    resource: string,\r\n    outcome: 'success' | 'failure',\r\n    details?: any,\r\n  ): AuditLogEntry {\r\n    return {\r\n      userId: req.user?.id,\r\n      action,\r\n      resource,\r\n      outcome,\r\n      timestamp: new Date().toISOString(),\r\n      ipAddress: req.ip,\r\n      userAgent: req.headers['user-agent'],\r\n      correlationId: req.headers['x-correlation-id'] as string,\r\n      details: details || {},\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Response interfaces\r\n */\r\nexport interface ApiSuccessResponse<T> {\r\n  success: true;\r\n  data: T;\r\n  message: string;\r\n  timestamp: string;\r\n  metadata: any;\r\n}\r\n\r\nexport interface ApiErrorResponse {\r\n  success: false;\r\n  error: {\r\n    code: string;\r\n    message: string;\r\n    details: any;\r\n    timestamp: string;\r\n  };\r\n}\r\n\r\nexport interface ApiPaginatedResponse<T> {\r\n  success: true;\r\n  data: T[];\r\n  message: string;\r\n  timestamp: string;\r\n  pagination: PaginationInfo;\r\n  metadata: any;\r\n}\r\n\r\nexport interface PaginationInfo {\r\n  page: number;\r\n  limit: number;\r\n  total: number;\r\n  totalPages: number;\r\n  hasNext: boolean;\r\n  hasPrev: boolean;\r\n  offset: number;\r\n}\r\n\r\nexport interface PaginationParams {\r\n  page: number;\r\n  limit: number;\r\n  offset: number;\r\n}\r\n\r\nexport interface SortingParams {\r\n  sortBy: string;\r\n  sortOrder: 'asc' | 'desc';\r\n}\r\n\r\nexport interface FilterParams {\r\n  [key: string]: any;\r\n}\r\n\r\nexport interface UserContext {\r\n  id?: string;\r\n  email?: string;\r\n  roles: string[];\r\n  permissions: string[];\r\n  organizationId?: string;\r\n}\r\n\r\nexport interface AuditLogEntry {\r\n  userId?: string;\r\n  action: string;\r\n  resource: string;\r\n  outcome: 'success' | 'failure';\r\n  timestamp: string;\r\n  ipAddress: string;\r\n  userAgent: string;\r\n  correlationId: string;\r\n  details: any;\r\n}"], "version": 3}