b6c9f93a87990e327d8f9ce19e42d268
"use strict";
/**
 * Sort Types
 *
 * Common types and interfaces for sorting data across the application.
 * Provides consistent sorting patterns and utilities.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SortUtils = exports.SortDirection = void 0;
/**
 * Sort Direction
 * Supported sort directions
 */
var SortDirection;
(function (SortDirection) {
    SortDirection["ASC"] = "asc";
    SortDirection["DESC"] = "desc";
})(SortDirection || (exports.SortDirection = SortDirection = {}));
/**
 * Sort Utilities
 */
class SortUtils {
    /**
     * Validate sort parameters
     */
    static validateSort(sortParams, config = SortUtils.DEFAULT_CONFIG) {
        const errors = [];
        const warnings = [];
        // Check maximum sort fields
        if (sortParams.sort.length > config.maxSortFields) {
            errors.push(`Number of sort fields (${sortParams.sort.length}) exceeds maximum allowed (${config.maxSortFields})`);
        }
        // Validate each sort field
        sortParams.sort.forEach((sortField, index) => {
            // Check if field is sortable
            if (config.sortableFields.length > 0 && !config.sortableFields.includes(sortField.field)) {
                errors.push(`Field '${sortField.field}' is not sortable`);
            }
            // Check custom sort functions
            if (sortField.customSort && !config.allowCustomSort) {
                errors.push(`Custom sort functions are not allowed for field '${sortField.field}'`);
            }
            // Check case sensitivity
            if (sortField.caseSensitive !== undefined && !config.allowCaseSensitive) {
                warnings.push(`Case sensitivity setting ignored for field '${sortField.field}'`);
            }
            // Check for duplicate fields
            const duplicateIndex = sortParams.sort.findIndex((other, otherIndex) => otherIndex !== index && other.field === sortField.field);
            if (duplicateIndex !== -1) {
                errors.push(`Duplicate sort field '${sortField.field}' found`);
            }
        });
        return {
            isValid: errors.length === 0,
            errors,
            warnings,
        };
    }
    /**
     * Create a simple ascending sort
     */
    static asc(field, priority) {
        return {
            field,
            direction: SortDirection.ASC,
            priority,
        };
    }
    /**
     * Create a simple descending sort
     */
    static desc(field, priority) {
        return {
            field,
            direction: SortDirection.DESC,
            priority,
        };
    }
    /**
     * Create sort parameters from multiple fields
     */
    static create(...sortFields) {
        // Sort by priority if specified
        const sorted = [...sortFields].sort((a, b) => {
            const aPriority = a.priority ?? Number.MAX_SAFE_INTEGER;
            const bPriority = b.priority ?? Number.MAX_SAFE_INTEGER;
            return aPriority - bPriority;
        });
        return {
            sort: sorted,
            allowMultipleFields: true,
        };
    }
    /**
     * Parse sort from query parameters
     */
    static fromQuery(query, config = SortUtils.DEFAULT_CONFIG) {
        const sortFields = [];
        if (query.sort) {
            const sortString = Array.isArray(query.sort) ? query.sort.join(',') : query.sort;
            const sortParts = sortString.split(',');
            sortParts.forEach((part, index) => {
                const trimmed = part.trim();
                if (!trimmed)
                    return;
                let field;
                let direction = config.defaultDirection;
                if (trimmed.startsWith('-')) {
                    field = trimmed.substring(1);
                    direction = SortDirection.DESC;
                }
                else if (trimmed.startsWith('+')) {
                    field = trimmed.substring(1);
                    direction = SortDirection.ASC;
                }
                else {
                    field = trimmed;
                }
                sortFields.push({
                    field,
                    direction,
                    priority: index,
                });
            });
        }
        return {
            sort: sortFields,
            allowMultipleFields: true,
        };
    }
    /**
     * Convert sort parameters to query string
     */
    static toQueryString(sortParams) {
        if (sortParams.sort.length === 0) {
            return '';
        }
        const sortString = sortParams.sort
            .map(field => {
            const prefix = field.direction === SortDirection.DESC ? '-' : '';
            return `${prefix}${field.field}`;
        })
            .join(',');
        return `sort=${encodeURIComponent(sortString)}`;
    }
    /**
     * Apply sort to an array of objects
     */
    static applySort(items, sortParams) {
        if (sortParams.sort.length === 0) {
            return items;
        }
        return [...items].sort((a, b) => {
            for (const sortField of sortParams.sort) {
                let result;
                if (sortField.customSort) {
                    result = sortField.customSort(a, b);
                }
                else {
                    const aValue = SortUtils.getNestedValue(a, sortField.field);
                    const bValue = SortUtils.getNestedValue(b, sortField.field);
                    result = SortUtils.compareValues(aValue, bValue, sortField.caseSensitive);
                }
                if (sortField.direction === SortDirection.DESC) {
                    result = -result;
                }
                if (result !== 0) {
                    return result;
                }
            }
            return 0;
        });
    }
    /**
     * Get nested property value from object
     */
    static getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : undefined;
        }, obj);
    }
    /**
     * Compare two values for sorting
     */
    static compareValues(a, b, caseSensitive = true) {
        // Handle null/undefined values
        if (a == null && b == null)
            return 0;
        if (a == null)
            return -1;
        if (b == null)
            return 1;
        // Handle string comparison
        if (typeof a === 'string' && typeof b === 'string') {
            if (!caseSensitive) {
                a = a.toLowerCase();
                b = b.toLowerCase();
            }
            return a.localeCompare(b);
        }
        // Handle number comparison
        if (typeof a === 'number' && typeof b === 'number') {
            return a - b;
        }
        // Handle date comparison
        if (a instanceof Date && b instanceof Date) {
            return a.getTime() - b.getTime();
        }
        // Handle boolean comparison
        if (typeof a === 'boolean' && typeof b === 'boolean') {
            return a === b ? 0 : a ? 1 : -1;
        }
        // Convert to string for comparison
        const aStr = String(a);
        const bStr = String(b);
        if (!caseSensitive) {
            return aStr.toLowerCase().localeCompare(bStr.toLowerCase());
        }
        return aStr.localeCompare(bStr);
    }
    /**
     * Reverse sort direction
     */
    static reverseDirection(direction) {
        return direction === SortDirection.ASC ? SortDirection.DESC : SortDirection.ASC;
    }
    /**
     * Check if sort parameters are empty
     */
    static isEmpty(sortParams) {
        return sortParams.sort.length === 0;
    }
    /**
     * Get all fields referenced in sort parameters
     */
    static getReferencedFields(sortParams) {
        return sortParams.sort.map(field => field.field);
    }
    /**
     * Merge multiple sort parameters
     */
    static merge(...sortParams) {
        const allFields = [];
        const seenFields = new Set();
        sortParams.forEach(params => {
            params.sort.forEach(field => {
                if (!seenFields.has(field.field)) {
                    allFields.push(field);
                    seenFields.add(field.field);
                }
            });
        });
        return {
            sort: allFields,
            allowMultipleFields: true,
        };
    }
    /**
     * Create SQL ORDER BY clause from sort parameters
     */
    static toSqlOrderBy(sortParams, tableAlias) {
        if (sortParams.sort.length === 0) {
            return '';
        }
        const orderClauses = sortParams.sort.map(field => {
            const fieldName = tableAlias ? `${tableAlias}.${field.field}` : field.field;
            return `${fieldName} ${field.direction.toUpperCase()}`;
        });
        return `ORDER BY ${orderClauses.join(', ')}`;
    }
    /**
     * Create MongoDB sort object from sort parameters
     */
    static toMongoSort(sortParams) {
        const mongoSort = {};
        sortParams.sort.forEach(field => {
            mongoSort[field.field] = field.direction === SortDirection.ASC ? 1 : -1;
        });
        return mongoSort;
    }
}
exports.SortUtils = SortUtils;
/**
 * Default sort configuration
 */
SortUtils.DEFAULT_CONFIG = {
    maxSortFields: 5,
    sortableFields: [],
    defaultDirection: SortDirection.ASC,
    allowCaseSensitive: true,
    allowCustomSort: false,
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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