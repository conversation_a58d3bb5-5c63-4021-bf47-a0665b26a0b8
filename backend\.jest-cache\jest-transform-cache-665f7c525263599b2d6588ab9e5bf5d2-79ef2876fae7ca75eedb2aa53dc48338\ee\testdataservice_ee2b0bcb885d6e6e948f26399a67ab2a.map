{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\testing\\fixtures\\test-data.service.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,6CAAmD;AACnD,qCAAiD;AACjD,2CAAwC;AACxC,2GAAgG;AAChG,2HAA+G;AAC/G,gGAAqF;AACrF,6FAAkF;AAClF,mGAAwF;AACxF,qGAAyF;AACzF,mFAAwE;AACxE,2FAAgF;AAEhF;;;;;;;;;;GAUG;AAEI,IAAM,eAAe,uBAArB,MAAM,eAAe;IAI1B,YAEE,mBAAmE,EAEnE,iBAAwE,EAExE,kBAAiE,EAEjE,wBAAqE,EAErE,2BAA2E,EAE3E,qBAAqE,EAErE,mBAA2D,EAE3D,uBAAmE,EAClD,UAAsB;QAftB,wBAAmB,GAAnB,mBAAmB,CAA+B;QAElD,sBAAiB,GAAjB,iBAAiB,CAAsC;QAEvD,uBAAkB,GAAlB,kBAAkB,CAA8B;QAEhD,6BAAwB,GAAxB,wBAAwB,CAA4B;QAEpD,gCAA2B,GAA3B,2BAA2B,CAA+B;QAE1D,0BAAqB,GAArB,qBAAqB,CAA+B;QAEpD,wBAAmB,GAAnB,mBAAmB,CAAuB;QAE1C,4BAAuB,GAAvB,uBAAuB,CAA2B;QAClD,eAAU,GAAV,UAAU,CAAY;QApBxB,WAAM,GAAG,IAAI,eAAM,CAAC,iBAAe,CAAC,IAAI,CAAC,CAAC;QAC1C,kBAAa,GAAG,IAAI,GAAG,EAAe,CAAC;IAoBrD,CAAC;IAEJ;;OAEG;IACH,KAAK,CAAC,yBAAyB,CAAC,QAAgB,EAAE;QAChD,MAAM,SAAS,GAAuB,EAAE,CAAC;QAEzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;gBAC9C,IAAI,EAAE,aAAK,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,WAAW;gBACxC,WAAW,EAAE,aAAK,CAAC,KAAK,CAAC,SAAS,EAAE;gBACpC,QAAQ,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;gBAC7F,OAAO,EAAE,aAAK,CAAC,MAAM,CAAC,MAAM,EAAE;gBAC9B,QAAQ,EAAE,aAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC;gBACrC,UAAU,EAAE,IAAI,CAAC,0BAA0B,EAAE;gBAC7C,QAAQ,EAAE;oBACR,MAAM,EAAE,aAAK,CAAC,MAAM,CAAC,QAAQ,EAAE;oBAC/B,IAAI,EAAE,aAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;oBACxG,iBAAiB,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC;oBACjE,UAAU,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;iBAClE;gBACD,SAAS,EAAE,WAAW;gBACtB,SAAS,EAAE,WAAW;aACvB,CAAC,CAAC;YAEH,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3B,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,0BAA0B,CAC9B,SAA6B,EAC7B,QAAgB,EAAE;QAElB,MAAM,UAAU,GAAwB,EAAE,CAAC;QAC3C,MAAM,kBAAkB,GAAG,CAAC,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;QAEtF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/B,MAAM,QAAQ,GAAG,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YACvD,MAAM,MAAM,GAAG,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;YAC9D,MAAM,SAAS,GAAG,aAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;YAClD,MAAM,QAAQ,GAAG,MAAM,KAAK,WAAW,IAAI,MAAM,KAAK,QAAQ;gBAC5D,CAAC,CAAC,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC;gBAC9C,CAAC,CAAC,IAAI,CAAC;YACT,MAAM,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAE/E,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;gBAChD,UAAU,EAAE,QAAQ,CAAC,EAAE;gBACvB,aAAa,EAAE,GAAG,QAAQ,CAAC,IAAI,MAAM,aAAK,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE,EAAE;gBACxE,MAAM,EAAE,MAAa;gBACrB,QAAQ,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;gBAC3E,SAAS,EAAE,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI;gBAClD,WAAW;gBACX,QAAQ;gBACR,SAAS,EAAE,IAAI,CAAC,0BAA0B,EAAE;gBAC5C,UAAU,EAAE,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,2BAA2B,EAAE,CAAC,CAAC,CAAC,IAAI;gBAC9E,WAAW,EAAE,IAAI,CAAC,4BAA4B,EAAE;gBAChD,cAAc,EAAE,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC;gBACxE,kBAAkB,EAAE,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,UAAU,CAAC;gBACxE,YAAY,EAAE,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC,IAAI;gBACtE,WAAW,EAAE,aAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC,IAAI;gBAC5E,eAAe,EAAE,IAAI,CAAC,uBAAuB,EAAE;gBAC/C,WAAW,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,YAAY,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;gBAC9E,aAAa,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;gBAClF,WAAW,EAAE,IAAI,CAAC,mBAAmB,EAAE;aACxC,CAAC,CAAC;YAEH,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC7B,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,yBAAyB,CAC7B,UAA+B,EAC/B,uBAA+B,CAAC;QAEhC,MAAM,QAAQ,GAA+B,EAAE,CAAC;QAEhD,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,oBAAoB,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC9C,MAAM,WAAW,GAAG,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC;oBAC7C,QAAQ,EAAE,MAAM,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,MAAM;iBACjE,CAAC,CAAC;gBAEH,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;oBAC5C,WAAW,EAAE,SAAS,CAAC,EAAE;oBACzB,WAAW,EAAE,WAAkB;oBAC/B,UAAU,EAAE,GAAG,WAAW,IAAI,CAAC,EAAE;oBACjC,SAAS,EAAE,IAAI,CAAC,wBAAwB,EAAE;oBAC1C,SAAS,EAAE,IAAI,CAAC,wBAAwB,CAAC,WAAW,CAAC;oBACrD,eAAe,EAAE,IAAI,CAAC,uBAAuB,EAAE;oBAC/C,QAAQ,EAAE,IAAI,CAAC,uBAAuB,EAAE;oBACxC,QAAQ,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,EAAE,aAAa;oBACtE,aAAa,EAAE,IAAI,CAAC,qBAAqB,EAAE;iBAC5C,CAAC,CAAC;gBAEH,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAAC,QAAgB,GAAG;QAC/C,MAAM,SAAS,GAAqB,EAAE,CAAC;QACvC,MAAM,WAAW,GAAG;YAClB,2BAA2B;YAC3B,6BAA6B;YAC7B,wBAAwB;YACxB,kBAAkB;YAClB,qBAAqB;YACrB,yBAAyB;YACzB,wBAAwB;YACxB,YAAY;SACb,CAAC;QAEF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/B,MAAM,UAAU,GAAG,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YAC3D,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAEpD,MAAM,QAAQ,GAAG,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC;gBACpD,UAAU;gBACV,QAAQ;gBACR,KAAK,EAAE,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC;gBAC3C,MAAM,EAAE,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC;gBAC7C,SAAS,EAAE,aAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;gBACzC,eAAe,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;gBAClF,iBAAiB,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;gBAC9E,QAAQ,EAAE;oBACR,MAAM,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,iBAAiB,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;oBAC/E,OAAO,EAAE,OAAO;oBAChB,WAAW,EAAE,MAAM;iBACpB;aACF,CAAC,CAAC;YAEH,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3B,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,8BAA8B,CAAC,QAAgB,GAAG;QACtD,MAAM,OAAO,GAAwB,EAAE,CAAC;QACxC,MAAM,UAAU,GAAG,CAAC,iBAAiB,EAAE,UAAU,EAAE,kBAAkB,EAAE,QAAQ,CAAC,CAAC;QACjF,MAAM,UAAU,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;QAE3D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/B,MAAM,SAAS,GAAG,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YACzD,MAAM,SAAS,GAAG,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YAEzD,MAAM,MAAM,GAAG,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC;gBACrD,UAAU,EAAE,GAAG,SAAS,IAAI,SAAS,WAAW;gBAChD,SAAS;gBACT,SAAS;gBACT,KAAK,EAAE,aAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;gBAClE,IAAI,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,cAAc,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;gBAC/E,MAAM,EAAE,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC;gBACjD,QAAQ,EAAE,IAAI,CAAC,2BAA2B,EAAE;gBAC5C,SAAS,EAAE,aAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;gBACzC,MAAM,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;gBACrE,YAAY,EAAE,aAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;aACtE,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvB,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,0BAA0B,CAAC,QAAgB,EAAE;QACjD,MAAM,OAAO,GAAwB,EAAE,CAAC;QACxC,MAAM,UAAU,GAAG;YACjB,uBAAuB;YACvB,oBAAoB;YACpB,qBAAqB;YACrB,YAAY;YACZ,cAAc;YACd,wBAAwB;SACzB,CAAC;QAEF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/B,MAAM,SAAS,GAAG,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YACzD,MAAM,MAAM,GAAG,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC;YAEhF,MAAM,MAAM,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC;gBAC/C,SAAS;gBACT,MAAM,EAAE,MAAa;gBACrB,YAAY,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;gBACrD,OAAO,EAAE,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC;gBAChD,OAAO,EAAE,IAAI,CAAC,0BAA0B,CAAC,SAAS,EAAE,MAAM,CAAC;gBAC3D,SAAS,EAAE,aAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;gBACzC,QAAQ,EAAE,aAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC;gBACrC,IAAI,EAAE,aAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,gBAAgB,EAAE,aAAa,EAAE,UAAU,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;gBACpG,QAAQ,EAAE;oBACR,OAAO,EAAE,OAAO;oBAChB,WAAW,EAAE,MAAM;oBACnB,MAAM,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;iBAC5E;aACF,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvB,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,QAAgB,EAAE;QACzC,MAAM,KAAK,GAAgB,EAAE,CAAC;QAE9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/B,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;gBAC3C,IAAI,EAAE,aAAK,CAAC,OAAO,CAAC,UAAU,EAAE;gBAChC,WAAW,EAAE,aAAK,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACnC,UAAU,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC;oBACrC,6BAA6B;oBAC7B,YAAY;oBACZ,kBAAkB;oBAClB,yBAAyB;iBAC1B,CAAC;gBACF,SAAS,EAAE,IAAI,CAAC,sBAAsB,EAAE;gBACxC,QAAQ,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,MAAM,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;gBACrE,OAAO,EAAE,aAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC;gBACpC,UAAU,EAAE,IAAI,CAAC,wBAAwB,EAAE;gBAC3C,QAAQ,EAAE,IAAI,CAAC,sBAAsB,EAAE;gBACvC,WAAW,EAAE,IAAI,CAAC,yBAAyB,EAAE;gBAC7C,QAAQ,EAAE,IAAI,CAAC,yBAAyB,EAAE;gBAC1C,SAAS,EAAE,YAAY;gBACvB,SAAS,EAAE,YAAY;aACxB,CAAC,CAAC;YAEH,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnB,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CAC1B,UAAuB,EACvB,QAAgB,EAAE;QAElB,MAAM,SAAS,GAAoB,EAAE,CAAC;QAEtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/B,MAAM,IAAI,GAAG,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YACpD,MAAM,MAAM,GAAG,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,QAAQ,EAAE,cAAc,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC,CAAC;YAChG,MAAM,SAAS,GAAG,aAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;YACjD,MAAM,OAAO,GAAG,MAAM,KAAK,UAAU;gBACnC,CAAC,CAAC,aAAK,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC;gBACzD,CAAC,CAAC,IAAI,CAAC;YAET,MAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC;gBACnD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,MAAM,EAAE,MAAa;gBACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,SAAS;gBACT,OAAO;gBACP,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI;gBAClE,YAAY,EAAE,aAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;gBACvE,WAAW,EAAE,aAAK,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACnC,eAAe,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;gBACrD,cAAc,EAAE,MAAM,KAAK,cAAc;gBACzC,cAAc,EAAE,MAAM,KAAK,cAAc,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI;gBAC9D,cAAc,EAAE,MAAM,KAAK,cAAc,CAAC,CAAC,CAAC,aAAK,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,IAAI;gBACtE,UAAU,EAAE,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI;gBACtD,UAAU,EAAE,OAAO;gBACnB,WAAW,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;gBAC9E,gBAAgB,EAAE,aAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,UAAU,EAAE,YAAY,EAAE,WAAW,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;gBAC1G,QAAQ,EAAE,IAAI,CAAC,wBAAwB,EAAE;aAC1C,CAAC,CAAC;YAEH,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3B,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,OAQlB;QAUC,MAAM,EACJ,SAAS,EAAE,aAAa,GAAG,EAAE,EAC7B,UAAU,EAAE,cAAc,GAAG,EAAE,EAC/B,QAAQ,EAAE,YAAY,GAAG,CAAC,EAC1B,OAAO,EAAE,WAAW,GAAG,GAAG,EAC1B,YAAY,EAAE,gBAAgB,GAAG,EAAE,EACnC,UAAU,EAAE,cAAc,GAAG,EAAE,EAC/B,SAAS,EAAE,aAAa,GAAG,EAAE,GAC9B,GAAG,OAAO,IAAI,EAAE,CAAC;QAElB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;QAEjD,oCAAoC;QACpC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,aAAa,CAAC,CAAC;QACtE,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QACpF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;QAChF,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;QACxE,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,8BAA8B,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;QACtF,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,gBAAgB,CAAC,CAAC;QAC7E,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;QACjE,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;QAE/E,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;QAE5D,OAAO;YACL,SAAS;YACT,UAAU;YACV,QAAQ;YACR,eAAe;YACf,kBAAkB;YAClB,YAAY;YACZ,UAAU;YACV,SAAS;SACV,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa;QACjB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QAEzC,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;QACxD,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;QAC5B,MAAM,WAAW,CAAC,gBAAgB,EAAE,CAAC;QAErC,IAAI,CAAC;YACH,oCAAoC;YACpC,MAAM,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,qCAAa,EAAE,EAAE,CAAC,CAAC;YACpD,MAAM,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,6BAAS,EAAE,EAAE,CAAC,CAAC;YAChD,MAAM,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,8CAAiB,EAAE,EAAE,CAAC,CAAC;YACxD,MAAM,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,6CAAiB,EAAE,EAAE,CAAC,CAAC;YACxD,MAAM,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,uCAAc,EAAE,EAAE,CAAC,CAAC;YACrD,MAAM,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,4DAAwB,EAAE,EAAE,CAAC,CAAC;YAC/D,MAAM,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,6CAAiB,EAAE,EAAE,CAAC,CAAC;YACxD,MAAM,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,2CAAgB,EAAE,EAAE,CAAC,CAAC;YAEvD,MAAM,WAAW,CAAC,iBAAiB,EAAE,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,WAAW,CAAC,mBAAmB,EAAE,CAAC;YACxC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC5D,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;QAC9B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,WAAmB;QAChC,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;YACxC,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAC7C,CAAC;QAED,MAAM,QAAQ,GAAG;YACf,sBAAsB,EAAE;gBACtB,IAAI,EAAE,sBAAsB;gBAC5B,WAAW,EAAE,+BAA+B;gBAC5C,UAAU,EAAE;oBACV,KAAK,EAAE;wBACL,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,aAAa,EAAE,EAAE;wBACzE,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,EAAE,EAAE;qBAC/E;iBACF;aACF;YACD,uBAAuB,EAAE;gBACvB,IAAI,EAAE,uBAAuB;gBAC7B,WAAW,EAAE,wDAAwD;gBACrE,UAAU,EAAE;oBACV,KAAK,EAAE;wBACL;4BACE,EAAE,EAAE,WAAW;4BACf,IAAI,EAAE,UAAU;4BAChB,MAAM,EAAE;gCACN,KAAK,EAAE;oCACL,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE;oCACrE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE;iCACtE;6BACF;yBACF;wBACD;4BACE,EAAE,EAAE,cAAc;4BAClB,IAAI,EAAE,aAAa;4BACnB,MAAM,EAAE;gCACN,SAAS,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE;gCACvF,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,CAAC;gCACrF,WAAW,EAAE,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,CAAC;6BACvF;yBACF;qBACF;iBACF;aACF;YACD,QAAQ,EAAE;gBACR,EAAE,EAAE,cAAc;gBAClB,KAAK,EAAE,kBAAkB;gBACzB,IAAI,EAAE,MAAM;gBACZ,WAAW,EAAE,CAAC,eAAe,EAAE,kBAAkB,CAAC;aACnD;YACD,SAAS,EAAE;gBACT,EAAE,EAAE,eAAe;gBACnB,KAAK,EAAE,mBAAmB;gBAC1B,IAAI,EAAE,OAAO;gBACb,WAAW,EAAE,CAAC,GAAG,CAAC;aACnB;SACF,CAAC;QAEF,MAAM,OAAO,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;QACtC,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;QAC/C,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,0BAA0B;QAChC,MAAM,SAAS,GAAG,CAAC,cAAc,EAAE,YAAY,EAAE,qBAAqB,EAAE,kBAAkB,CAAC,CAAC;QAC5F,MAAM,SAAS,GAAG,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;QACvD,MAAM,KAAK,GAAG,EAAE,CAAC;QAEjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;YACnC,MAAM,QAAQ,GAAG,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YACvD,KAAK,CAAC,IAAI,CAAC;gBACT,EAAE,EAAE,QAAQ,CAAC,GAAG,CAAC,EAAE;gBACnB,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC;gBACzC,aAAa,EAAE,aAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC,CAAC,CAAC,SAAS;aACnF,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,KAAK;YACL,MAAM,EAAE;gBACN,QAAQ,EAAE,CAAC,MAAM,CAAC;gBAClB,QAAQ,EAAE,CAAC,SAAS,CAAC;gBACrB,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE;aAC7C;YACD,OAAO,EAAE;gBACP,MAAM,EAAE,QAAQ;gBAChB,MAAM,EAAE,QAAQ;aACjB;SACF,CAAC;IACJ,CAAC;IAEO,kBAAkB,CAAC,QAAgB;QACzC,MAAM,OAAO,GAAG;YACd,YAAY,EAAE;gBACZ,IAAI,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;gBAC3D,UAAU,EAAE,CAAC,aAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;gBACpC,OAAO,EAAE,aAAK,CAAC,KAAK,CAAC,QAAQ,EAAE;aAChC;YACD,UAAU,EAAE;gBACV,KAAK,EAAE;oBACL,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE;oBACnC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE;iBACxD;aACF;YACD,mBAAmB,EAAE;gBACnB,cAAc,EAAE;oBACd,IAAI,EAAE,KAAK;oBACX,QAAQ,EAAE,wCAAwC;iBACnD;aACF;YACD,gBAAgB,EAAE;gBAChB,OAAO,EAAE,aAAK,CAAC,OAAO,CAAC,IAAI,EAAE;gBAC7B,QAAQ,EAAE,aAAK,CAAC,QAAQ,CAAC,GAAG,EAAE;gBAC9B,MAAM,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;gBAC1D,OAAO,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;aACrD;SACF,CAAC;QAEF,OAAO,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;IACjC,CAAC;IAEO,0BAA0B;QAChC,OAAO;YACL,IAAI,EAAE;gBACJ,UAAU,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;gBACtE,MAAM,EAAE,aAAK,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;gBACzD,UAAU,EAAE,CAAC,aAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,aAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;gBAC5D,aAAa,EAAE,aAAK,CAAC,QAAQ,CAAC,OAAO,EAAE;gBACvC,MAAM,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;aAC5D;YACD,OAAO,EAAE;gBACP,QAAQ,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAC/D,OAAO,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC;gBACvD,cAAc,EAAE,aAAK,CAAC,QAAQ,CAAC,OAAO,EAAE;aACzC;SACF,CAAC;IACJ,CAAC;IAEO,2BAA2B;QACjC,OAAO;YACL,MAAM,EAAE;gBACN,QAAQ,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;gBAC7B,WAAW,EAAE,aAAK,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE;gBAC9C,QAAQ,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC;gBACxD,WAAW,EAAE,aAAK,CAAC,QAAQ,CAAC,GAAG,EAAE;aAClC;YACD,MAAM,EAAE,WAAW;YACnB,OAAO,EAAE;gBACP,cAAc,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC;gBAC5D,gBAAgB,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC;gBAC7D,iBAAiB,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC;aACzD;SACF,CAAC;IACJ,CAAC;IAEO,4BAA4B;QAClC,OAAO;YACL,WAAW,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,aAAa,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;YACjF,aAAa,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;YAClC,eAAe,EAAE;gBACf,QAAQ,EAAE,aAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE;gBACnC,MAAM,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;gBACxE,UAAU,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;aACnF;YACD,UAAU,EAAE;gBACV,OAAO,EAAE,aAAK,CAAC,MAAM,CAAC,MAAM,EAAE;gBAC9B,WAAW,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;gBACvD,YAAY,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;aAClC;SACF,CAAC;IACJ,CAAC;IAEO,sBAAsB,CAAC,UAAe,EAAE,MAAc;QAC5D,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,IAAI,EAAE,CAAC;QACrC,MAAM,UAAU,GAAQ,EAAE,CAAC;QAC3B,MAAM,cAAc,GAAa,EAAE,CAAC;QACpC,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,MAAM,YAAY,GAAa,EAAE,CAAC;QAElC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAC5B,IAAI,UAAkB,CAAC;YAEvB,IAAI,MAAM,KAAK,WAAW,EAAE,CAAC;gBAC3B,UAAU,GAAG,WAAW,CAAC;gBACzB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC/B,CAAC;iBAAM,IAAI,MAAM,KAAK,QAAQ,IAAI,KAAK,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3D,UAAU,GAAG,WAAW,CAAC;gBACzB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC/B,CAAC;iBAAM,IAAI,MAAM,KAAK,QAAQ,IAAI,KAAK,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC7D,UAAU,GAAG,QAAQ,CAAC;gBACtB,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC5B,CAAC;iBAAM,IAAI,MAAM,KAAK,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC7D,UAAU,GAAG,WAAW,CAAC;gBACzB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC/B,CAAC;iBAAM,IAAI,MAAM,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC9E,UAAU,GAAG,SAAS,CAAC;YACzB,CAAC;iBAAM,CAAC;gBACN,UAAU,GAAG,SAAS,CAAC;gBACvB,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC7B,CAAC;YAED,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG;gBACpB,MAAM,EAAE,UAAU;gBAClB,SAAS,EAAE,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,aAAK,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,SAAS;gBACnF,WAAW,EAAE,UAAU,KAAK,WAAW,IAAI,UAAU,KAAK,QAAQ;oBAChE,CAAC,CAAC,aAAK,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,SAAS;gBACjD,QAAQ,EAAE,UAAU,KAAK,WAAW,IAAI,UAAU,KAAK,QAAQ;oBAC7D,CAAC,CAAC,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS;gBAC1D,QAAQ,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;gBAC9C,MAAM,EAAE,UAAU,KAAK,WAAW,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,SAAS;gBAChF,SAAS,EAAE,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,aAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,SAAS;aACxE,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,WAAW,EAAE,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS;YACvF,cAAc;YACd,WAAW;YACX,YAAY,EAAE,EAAE;YAChB,YAAY;YACZ,UAAU;SACX,CAAC;IACJ,CAAC;CACF,CAAA;AAnoBY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,mBAAU,GAAE;IAMR,WAAA,IAAA,0BAAgB,EAAC,6CAAiB,CAAC,CAAA;IAEnC,WAAA,IAAA,0BAAgB,EAAC,4DAAwB,CAAC,CAAA;IAE1C,WAAA,IAAA,0BAAgB,EAAC,2CAAgB,CAAC,CAAA;IAElC,WAAA,IAAA,0BAAgB,EAAC,uCAAc,CAAC,CAAA;IAEhC,WAAA,IAAA,0BAAgB,EAAC,6CAAiB,CAAC,CAAA;IAEnC,WAAA,IAAA,0BAAgB,EAAC,8CAAiB,CAAC,CAAA;IAEnC,WAAA,IAAA,0BAAgB,EAAC,6BAAS,CAAC,CAAA;IAE3B,WAAA,IAAA,0BAAgB,EAAC,qCAAa,CAAC,CAAA;yDAbM,oBAAU,oBAAV,oBAAU,oDAEZ,oBAAU,oBAAV,oBAAU,oDAET,oBAAU,oBAAV,oBAAU,oDAEJ,oBAAU,oBAAV,oBAAU,oDAEP,oBAAU,oBAAV,oBAAU,oDAEhB,oBAAU,oBAAV,oBAAU,oDAEZ,oBAAU,oBAAV,oBAAU,oDAEN,oBAAU,oBAAV,oBAAU,oDACvB,oBAAU,oBAAV,oBAAU;GArB9B,eAAe,CAmoB3B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\testing\\fixtures\\test-data.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository, DataSource } from 'typeorm';\r\nimport { faker } from '@faker-js/faker';\r\nimport { WorkflowExecution } from '../../workflow-execution/entities/workflow-execution.entity';\r\nimport { WorkflowExecutionContext } from '../../workflow-execution/entities/workflow-execution-context.entity';\r\nimport { WorkflowTemplate } from '../../templates/entities/workflow-template.entity';\r\nimport { MetricSnapshot } from '../../monitoring/entities/metric-snapshot.entity';\r\nimport { PerformanceMetric } from '../../monitoring/entities/performance-metric.entity';\r\nimport { HealthCheckResult } from '../../monitoring/entities/health-check-result.entity';\r\nimport { AlertRule } from '../../monitoring/entities/alert-rule.entity';\r\nimport { AlertIncident } from '../../monitoring/entities/alert-incident.entity';\r\n\r\n/**\r\n * Test Data Service\r\n *\r\n * Comprehensive test data generation and management providing:\r\n * - Realistic test data generation using Faker.js\r\n * - Database seeding and cleanup utilities\r\n * - Test fixture management and versioning\r\n * - Performance test data generation\r\n * - Cross-module test data consistency\r\n * - Test data validation and integrity checks\r\n */\r\n@Injectable()\r\nexport class TestDataService {\r\n  private readonly logger = new Logger(TestDataService.name);\r\n  private readonly testDataCache = new Map<string, any>();\r\n\r\n  constructor(\r\n    @InjectRepository(WorkflowExecution)\r\n    private readonly executionRepository: Repository<WorkflowExecution>,\r\n    @InjectRepository(WorkflowExecutionContext)\r\n    private readonly contextRepository: Repository<WorkflowExecutionContext>,\r\n    @InjectRepository(WorkflowTemplate)\r\n    private readonly templateRepository: Repository<WorkflowTemplate>,\r\n    @InjectRepository(MetricSnapshot)\r\n    private readonly metricSnapshotRepository: Repository<MetricSnapshot>,\r\n    @InjectRepository(PerformanceMetric)\r\n    private readonly performanceMetricRepository: Repository<PerformanceMetric>,\r\n    @InjectRepository(HealthCheckResult)\r\n    private readonly healthCheckRepository: Repository<HealthCheckResult>,\r\n    @InjectRepository(AlertRule)\r\n    private readonly alertRuleRepository: Repository<AlertRule>,\r\n    @InjectRepository(AlertIncident)\r\n    private readonly alertIncidentRepository: Repository<AlertIncident>,\r\n    private readonly dataSource: DataSource,\r\n  ) {}\r\n\r\n  /**\r\n   * Generate test workflow templates\r\n   */\r\n  async generateWorkflowTemplates(count: number = 10): Promise<WorkflowTemplate[]> {\r\n    const templates: WorkflowTemplate[] = [];\r\n\r\n    for (let i = 0; i < count; i++) {\r\n      const template = this.templateRepository.create({\r\n        name: faker.company.name() + ' Workflow',\r\n        description: faker.lorem.paragraph(),\r\n        category: faker.helpers.arrayElement(['reporting', 'monitoring', 'automation', 'compliance']),\r\n        version: faker.system.semver(),\r\n        isActive: faker.datatype.boolean(0.8),\r\n        definition: this.generateWorkflowDefinition(),\r\n        metadata: {\r\n          author: faker.person.fullName(),\r\n          tags: faker.helpers.arrayElements(['test', 'automation', 'reporting', 'monitoring'], { min: 1, max: 3 }),\r\n          estimatedDuration: faker.number.int({ min: 60000, max: 3600000 }),\r\n          complexity: faker.helpers.arrayElement(['low', 'medium', 'high']),\r\n        },\r\n        createdBy: 'test-user',\r\n        updatedBy: 'test-user',\r\n      });\r\n\r\n      templates.push(template);\r\n    }\r\n\r\n    return await this.templateRepository.save(templates);\r\n  }\r\n\r\n  /**\r\n   * Generate test workflow executions\r\n   */\r\n  async generateWorkflowExecutions(\r\n    templates: WorkflowTemplate[],\r\n    count: number = 50\r\n  ): Promise<WorkflowExecution[]> {\r\n    const executions: WorkflowExecution[] = [];\r\n    const statusDistribution = ['completed', 'failed', 'running', 'pending', 'cancelled'];\r\n\r\n    for (let i = 0; i < count; i++) {\r\n      const template = faker.helpers.arrayElement(templates);\r\n      const status = faker.helpers.arrayElement(statusDistribution);\r\n      const startedAt = faker.date.recent({ days: 30 });\r\n      const duration = status === 'completed' || status === 'failed'\r\n        ? faker.number.int({ min: 1000, max: 300000 })\r\n        : null;\r\n      const completedAt = duration ? new Date(startedAt.getTime() + duration) : null;\r\n\r\n      const execution = this.executionRepository.create({\r\n        templateId: template.id,\r\n        executionName: `${template.name} - ${faker.date.recent().toISOString()}`,\r\n        status: status as any,\r\n        priority: faker.helpers.arrayElement(['low', 'medium', 'high', 'critical']),\r\n        startedAt: status !== 'pending' ? startedAt : null,\r\n        completedAt,\r\n        duration,\r\n        inputData: this.generateExecutionInputData(),\r\n        outputData: status === 'completed' ? this.generateExecutionOutputData() : null,\r\n        contextData: this.generateExecutionContextData(),\r\n        executionState: this.generateExecutionState(template.definition, status),\r\n        performanceMetrics: this.generatePerformanceMetrics(template.definition),\r\n        errorDetails: status === 'failed' ? this.generateErrorDetails() : null,\r\n        retryConfig: faker.datatype.boolean(0.3) ? this.generateRetryConfig() : null,\r\n        executionConfig: this.generateExecutionConfig(),\r\n        triggeredBy: faker.helpers.arrayElement(['test-admin', 'test-user', 'system']),\r\n        triggerSource: faker.helpers.arrayElement(['manual', 'scheduled', 'event', 'api']),\r\n        triggerData: this.generateTriggerData(),\r\n      });\r\n\r\n      executions.push(execution);\r\n    }\r\n\r\n    return await this.executionRepository.save(executions);\r\n  }\r\n\r\n  /**\r\n   * Generate test execution contexts\r\n   */\r\n  async generateExecutionContexts(\r\n    executions: WorkflowExecution[],\r\n    contextsPerExecution: number = 3\r\n  ): Promise<WorkflowExecutionContext[]> {\r\n    const contexts: WorkflowExecutionContext[] = [];\r\n\r\n    for (const execution of executions) {\r\n      for (let i = 0; i < contextsPerExecution; i++) {\r\n        const contextType = faker.helpers.arrayElement([\r\n          'global', 'step', 'parallel_group', 'conditional_branch', 'loop'\r\n        ]);\r\n\r\n        const context = this.contextRepository.create({\r\n          executionId: execution.id,\r\n          contextType: contextType as any,\r\n          contextKey: `${contextType}_${i}`,\r\n          variables: this.generateContextVariables(),\r\n          stateData: this.generateContextStateData(contextType),\r\n          environmentData: this.generateEnvironmentData(),\r\n          metadata: this.generateContextMetadata(),\r\n          dataSize: faker.number.int({ min: 1024, max: 1048576 }), // 1KB to 1MB\r\n          accessPattern: this.generateAccessPattern(),\r\n        });\r\n\r\n        contexts.push(context);\r\n      }\r\n    }\r\n\r\n    return await this.contextRepository.save(contexts);\r\n  }\r\n\r\n  /**\r\n   * Generate test metric snapshots\r\n   */\r\n  async generateMetricSnapshots(count: number = 100): Promise<MetricSnapshot[]> {\r\n    const snapshots: MetricSnapshot[] = [];\r\n    const metricNames = [\r\n      'workflow_executions_total',\r\n      'workflow_execution_duration',\r\n      'workflow_step_duration',\r\n      'system_cpu_usage',\r\n      'system_memory_usage',\r\n      'database_query_duration',\r\n      'external_service_calls',\r\n      'error_rate',\r\n    ];\r\n\r\n    for (let i = 0; i < count; i++) {\r\n      const metricName = faker.helpers.arrayElement(metricNames);\r\n      const category = this.getMetricCategory(metricName);\r\n\r\n      const snapshot = this.metricSnapshotRepository.create({\r\n        metricName,\r\n        category,\r\n        value: this.generateMetricValue(metricName),\r\n        labels: this.generateMetricLabels(metricName),\r\n        timestamp: faker.date.recent({ days: 7 }),\r\n        aggregationType: faker.helpers.arrayElement(['sum', 'avg', 'max', 'min', 'count']),\r\n        aggregationWindow: faker.helpers.arrayElement(['1m', '5m', '15m', '1h', '1d']),\r\n        metadata: {\r\n          source: faker.helpers.arrayElement(['workflow_engine', 'monitoring', 'system']),\r\n          version: '1.0.0',\r\n          environment: 'test',\r\n        },\r\n      });\r\n\r\n      snapshots.push(snapshot);\r\n    }\r\n\r\n    return await this.metricSnapshotRepository.save(snapshots);\r\n  }\r\n\r\n  /**\r\n   * Generate test performance metrics\r\n   */\r\n  async generatePerformanceMetricsData(count: number = 200): Promise<PerformanceMetric[]> {\r\n    const metrics: PerformanceMetric[] = [];\r\n    const components = ['workflow_engine', 'database', 'external_service', 'system'];\r\n    const operations = ['execute', 'query', 'call', 'process'];\r\n\r\n    for (let i = 0; i < count; i++) {\r\n      const component = faker.helpers.arrayElement(components);\r\n      const operation = faker.helpers.arrayElement(operations);\r\n\r\n      const metric = this.performanceMetricRepository.create({\r\n        metricName: `${component}_${operation}_duration`,\r\n        component,\r\n        operation,\r\n        value: faker.number.float({ min: 10, max: 5000, precision: 0.01 }),\r\n        unit: faker.helpers.arrayElement(['milliseconds', 'seconds', 'bytes', 'count']),\r\n        labels: this.generatePerformanceLabels(component),\r\n        metadata: this.generatePerformanceMetadata(),\r\n        timestamp: faker.date.recent({ days: 1 }),\r\n        status: faker.helpers.arrayElement(['normal', 'warning', 'critical']),\r\n        anomalyScore: faker.number.float({ min: 0, max: 1, precision: 0.01 }),\r\n      });\r\n\r\n      metrics.push(metric);\r\n    }\r\n\r\n    return await this.performanceMetricRepository.save(metrics);\r\n  }\r\n\r\n  /**\r\n   * Generate test health check results\r\n   */\r\n  async generateHealthCheckResults(count: number = 50): Promise<HealthCheckResult[]> {\r\n    const results: HealthCheckResult[] = [];\r\n    const checkNames = [\r\n      'database_connectivity',\r\n      'redis_connectivity',\r\n      'external_api_health',\r\n      'disk_space',\r\n      'memory_usage',\r\n      'workflow_engine_health',\r\n    ];\r\n\r\n    for (let i = 0; i < count; i++) {\r\n      const checkName = faker.helpers.arrayElement(checkNames);\r\n      const status = faker.helpers.arrayElement(['healthy', 'degraded', 'unhealthy']);\r\n\r\n      const result = this.healthCheckRepository.create({\r\n        checkName,\r\n        status: status as any,\r\n        responseTime: faker.number.int({ min: 1, max: 5000 }),\r\n        message: this.generateHealthCheckMessage(status),\r\n        details: this.generateHealthCheckDetails(checkName, status),\r\n        timestamp: faker.date.recent({ days: 1 }),\r\n        critical: faker.datatype.boolean(0.3),\r\n        tags: faker.helpers.arrayElements(['infrastructure', 'application', 'external'], { min: 1, max: 2 }),\r\n        metadata: {\r\n          version: '1.0.0',\r\n          environment: 'test',\r\n          region: faker.helpers.arrayElement(['us-east-1', 'us-west-2', 'eu-west-1']),\r\n        },\r\n      });\r\n\r\n      results.push(result);\r\n    }\r\n\r\n    return await this.healthCheckRepository.save(results);\r\n  }\r\n\r\n  /**\r\n   * Generate test alert rules\r\n   */\r\n  async generateAlertRules(count: number = 20): Promise<AlertRule[]> {\r\n    const rules: AlertRule[] = [];\r\n\r\n    for (let i = 0; i < count; i++) {\r\n      const rule = this.alertRuleRepository.create({\r\n        name: faker.company.buzzPhrase(),\r\n        description: faker.lorem.sentence(),\r\n        metricName: faker.helpers.arrayElement([\r\n          'workflow_execution_duration',\r\n          'error_rate',\r\n          'system_cpu_usage',\r\n          'database_query_duration',\r\n        ]),\r\n        condition: this.generateAlertCondition(),\r\n        severity: faker.helpers.arrayElement(['info', 'warning', 'critical']),\r\n        enabled: faker.datatype.boolean(0.8),\r\n        escalation: this.generateEscalationConfig(),\r\n        recovery: this.generateRecoveryConfig(),\r\n        suppression: this.generateSuppressionConfig(),\r\n        metadata: this.generateAlertRuleMetadata(),\r\n        createdBy: 'test-admin',\r\n        updatedBy: 'test-admin',\r\n      });\r\n\r\n      rules.push(rule);\r\n    }\r\n\r\n    return await this.alertRuleRepository.save(rules);\r\n  }\r\n\r\n  /**\r\n   * Generate test alert incidents\r\n   */\r\n  async generateAlertIncidents(\r\n    alertRules: AlertRule[],\r\n    count: number = 30\r\n  ): Promise<AlertIncident[]> {\r\n    const incidents: AlertIncident[] = [];\r\n\r\n    for (let i = 0; i < count; i++) {\r\n      const rule = faker.helpers.arrayElement(alertRules);\r\n      const status = faker.helpers.arrayElement(['active', 'acknowledged', 'resolved', 'suppressed']);\r\n      const startTime = faker.date.recent({ days: 7 });\r\n      const endTime = status === 'resolved'\r\n        ? faker.date.between({ from: startTime, to: new Date() })\r\n        : null;\r\n\r\n      const incident = this.alertIncidentRepository.create({\r\n        ruleId: rule.id,\r\n        status: status as any,\r\n        severity: rule.severity,\r\n        startTime,\r\n        endTime,\r\n        duration: endTime ? endTime.getTime() - startTime.getTime() : null,\r\n        triggerValue: faker.number.float({ min: 0, max: 100, precision: 0.01 }),\r\n        description: faker.lorem.sentence(),\r\n        escalationLevel: faker.number.int({ min: 0, max: 3 }),\r\n        isAcknowledged: status === 'acknowledged',\r\n        acknowledgedBy: status === 'acknowledged' ? 'test-user' : null,\r\n        acknowledgedAt: status === 'acknowledged' ? faker.date.recent() : null,\r\n        resolvedBy: status === 'resolved' ? 'test-user' : null,\r\n        resolvedAt: endTime,\r\n        impactLevel: faker.helpers.arrayElement(['low', 'medium', 'high', 'critical']),\r\n        affectedServices: faker.helpers.arrayElements(['workflow', 'monitoring', 'reporting'], { min: 1, max: 2 }),\r\n        metadata: this.generateIncidentMetadata(),\r\n      });\r\n\r\n      incidents.push(incident);\r\n    }\r\n\r\n    return await this.alertIncidentRepository.save(incidents);\r\n  }\r\n\r\n  /**\r\n   * Seed complete test dataset\r\n   */\r\n  async seedTestData(options?: {\r\n    templates?: number;\r\n    executions?: number;\r\n    contexts?: number;\r\n    metrics?: number;\r\n    healthChecks?: number;\r\n    alertRules?: number;\r\n    incidents?: number;\r\n  }): Promise<{\r\n    templates: WorkflowTemplate[];\r\n    executions: WorkflowExecution[];\r\n    contexts: WorkflowExecutionContext[];\r\n    metricSnapshots: MetricSnapshot[];\r\n    performanceMetrics: PerformanceMetric[];\r\n    healthChecks: HealthCheckResult[];\r\n    alertRules: AlertRule[];\r\n    incidents: AlertIncident[];\r\n  }> {\r\n    const {\r\n      templates: templateCount = 10,\r\n      executions: executionCount = 50,\r\n      contexts: contextCount = 3,\r\n      metrics: metricCount = 100,\r\n      healthChecks: healthCheckCount = 50,\r\n      alertRules: alertRuleCount = 20,\r\n      incidents: incidentCount = 30,\r\n    } = options || {};\r\n\r\n    this.logger.log('Starting test data seeding...');\r\n\r\n    // Generate data in dependency order\r\n    const templates = await this.generateWorkflowTemplates(templateCount);\r\n    const executions = await this.generateWorkflowExecutions(templates, executionCount);\r\n    const contexts = await this.generateExecutionContexts(executions, contextCount);\r\n    const metricSnapshots = await this.generateMetricSnapshots(metricCount);\r\n    const performanceMetrics = await this.generatePerformanceMetricsData(metricCount * 2);\r\n    const healthChecks = await this.generateHealthCheckResults(healthCheckCount);\r\n    const alertRules = await this.generateAlertRules(alertRuleCount);\r\n    const incidents = await this.generateAlertIncidents(alertRules, incidentCount);\r\n\r\n    this.logger.log('Test data seeding completed successfully');\r\n\r\n    return {\r\n      templates,\r\n      executions,\r\n      contexts,\r\n      metricSnapshots,\r\n      performanceMetrics,\r\n      healthChecks,\r\n      alertRules,\r\n      incidents,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Clean all test data\r\n   */\r\n  async cleanTestData(): Promise<void> {\r\n    this.logger.log('Cleaning test data...');\r\n\r\n    const queryRunner = this.dataSource.createQueryRunner();\r\n    await queryRunner.connect();\r\n    await queryRunner.startTransaction();\r\n\r\n    try {\r\n      // Clean in reverse dependency order\r\n      await queryRunner.manager.delete(AlertIncident, {});\r\n      await queryRunner.manager.delete(AlertRule, {});\r\n      await queryRunner.manager.delete(HealthCheckResult, {});\r\n      await queryRunner.manager.delete(PerformanceMetric, {});\r\n      await queryRunner.manager.delete(MetricSnapshot, {});\r\n      await queryRunner.manager.delete(WorkflowExecutionContext, {});\r\n      await queryRunner.manager.delete(WorkflowExecution, {});\r\n      await queryRunner.manager.delete(WorkflowTemplate, {});\r\n\r\n      await queryRunner.commitTransaction();\r\n      this.logger.log('Test data cleaned successfully');\r\n    } catch (error) {\r\n      await queryRunner.rollbackTransaction();\r\n      this.logger.error('Failed to clean test data', error.stack);\r\n      throw error;\r\n    } finally {\r\n      await queryRunner.release();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get test fixture by name\r\n   */\r\n  getTestFixture(fixtureName: string): any {\r\n    if (this.testDataCache.has(fixtureName)) {\r\n      return this.testDataCache.get(fixtureName);\r\n    }\r\n\r\n    const fixtures = {\r\n      simpleWorkflowTemplate: {\r\n        name: 'Simple Test Workflow',\r\n        description: 'A simple workflow for testing',\r\n        definition: {\r\n          steps: [\r\n            { id: 'step1', type: 'notification', config: { message: 'Hello World' } },\r\n            { id: 'step2', type: 'validation', config: { rules: [{ type: 'required' }] } },\r\n          ],\r\n        },\r\n      },\r\n      complexWorkflowTemplate: {\r\n        name: 'Complex Test Workflow',\r\n        description: 'A complex workflow with parallel and conditional steps',\r\n        definition: {\r\n          steps: [\r\n            {\r\n              id: 'parallel1',\r\n              type: 'parallel',\r\n              config: {\r\n                steps: [\r\n                  { id: 'p1', type: 'notification', config: { message: 'Parallel 1' } },\r\n                  { id: 'p2', type: 'notification', config: { message: 'Parallel 2' } },\r\n                ],\r\n              },\r\n            },\r\n            {\r\n              id: 'conditional1',\r\n              type: 'conditional',\r\n              config: {\r\n                condition: { type: 'comparison', left: 'status', operator: 'equals', right: 'success' },\r\n                trueBranch: [{ id: 'success', type: 'notification', config: { message: 'Success' } }],\r\n                falseBranch: [{ id: 'failure', type: 'notification', config: { message: 'Failure' } }],\r\n              },\r\n            },\r\n          ],\r\n        },\r\n      },\r\n      testUser: {\r\n        id: 'test-user-id',\r\n        email: '<EMAIL>',\r\n        role: 'user',\r\n        permissions: ['workflow.view', 'workflow.execute'],\r\n      },\r\n      testAdmin: {\r\n        id: 'test-admin-id',\r\n        email: '<EMAIL>',\r\n        role: 'admin',\r\n        permissions: ['*'],\r\n      },\r\n    };\r\n\r\n    const fixture = fixtures[fixtureName];\r\n    if (fixture) {\r\n      this.testDataCache.set(fixtureName, fixture);\r\n    }\r\n\r\n    return fixture;\r\n  }\r\n\r\n  /**\r\n   * Private helper methods for data generation\r\n   */\r\n  private generateWorkflowDefinition(): any {\r\n    const stepTypes = ['notification', 'validation', 'data_transformation', 'external_service'];\r\n    const stepCount = faker.number.int({ min: 2, max: 8 });\r\n    const steps = [];\r\n\r\n    for (let i = 0; i < stepCount; i++) {\r\n      const stepType = faker.helpers.arrayElement(stepTypes);\r\n      steps.push({\r\n        id: `step_${i + 1}`,\r\n        type: stepType,\r\n        config: this.generateStepConfig(stepType),\r\n        outputMapping: faker.datatype.boolean(0.5) ? { result: 'output.data' } : undefined,\r\n      });\r\n    }\r\n\r\n    return {\r\n      steps,\r\n      inputs: {\r\n        required: ['data'],\r\n        optional: ['options'],\r\n        types: { data: 'object', options: 'object' },\r\n      },\r\n      outputs: {\r\n        result: 'object',\r\n        status: 'string',\r\n      },\r\n    };\r\n  }\r\n\r\n  private generateStepConfig(stepType: string): any {\r\n    const configs = {\r\n      notification: {\r\n        type: faker.helpers.arrayElement(['email', 'slack', 'sms']),\r\n        recipients: [faker.internet.email()],\r\n        message: faker.lorem.sentence(),\r\n      },\r\n      validation: {\r\n        rules: [\r\n          { type: 'required', field: 'data' },\r\n          { type: 'type', field: 'data', expectedType: 'object' },\r\n        ],\r\n      },\r\n      data_transformation: {\r\n        transformation: {\r\n          type: 'map',\r\n          function: 'item => ({ ...item, processed: true })',\r\n        },\r\n      },\r\n      external_service: {\r\n        service: faker.company.name(),\r\n        endpoint: faker.internet.url(),\r\n        method: faker.helpers.arrayElement(['GET', 'POST', 'PUT']),\r\n        timeout: faker.number.int({ min: 5000, max: 30000 }),\r\n      },\r\n    };\r\n\r\n    return configs[stepType] || {};\r\n  }\r\n\r\n  private generateExecutionInputData(): any {\r\n    return {\r\n      data: {\r\n        reportType: faker.helpers.arrayElement(['monthly', 'weekly', 'daily']),\r\n        period: faker.date.recent().toISOString().substring(0, 7),\r\n        recipients: [faker.internet.email(), faker.internet.email()],\r\n        includeCharts: faker.datatype.boolean(),\r\n        format: faker.helpers.arrayElement(['pdf', 'excel', 'csv']),\r\n      },\r\n      options: {\r\n        priority: faker.helpers.arrayElement(['low', 'medium', 'high']),\r\n        timeout: faker.number.int({ min: 60000, max: 3600000 }),\r\n        retryOnFailure: faker.datatype.boolean(),\r\n      },\r\n    };\r\n  }\r\n\r\n  private generateExecutionOutputData(): any {\r\n    return {\r\n      result: {\r\n        reportId: faker.string.uuid(),\r\n        generatedAt: faker.date.recent().toISOString(),\r\n        fileSize: faker.number.int({ min: 1024, max: 10485760 }),\r\n        downloadUrl: faker.internet.url(),\r\n      },\r\n      status: 'completed',\r\n      metrics: {\r\n        processingTime: faker.number.int({ min: 1000, max: 300000 }),\r\n        recordsProcessed: faker.number.int({ min: 100, max: 100000 }),\r\n        errorsEncountered: faker.number.int({ min: 0, max: 10 }),\r\n      },\r\n    };\r\n  }\r\n\r\n  private generateExecutionContextData(): any {\r\n    return {\r\n      environment: faker.helpers.arrayElement(['development', 'staging', 'production']),\r\n      correlationId: faker.string.uuid(),\r\n      userPreferences: {\r\n        timezone: faker.location.timeZone(),\r\n        locale: faker.helpers.arrayElement(['en-US', 'en-GB', 'fr-FR', 'de-DE']),\r\n        dateFormat: faker.helpers.arrayElement(['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD']),\r\n      },\r\n      systemInfo: {\r\n        version: faker.system.semver(),\r\n        buildNumber: faker.number.int({ min: 1000, max: 9999 }),\r\n        deploymentId: faker.string.uuid(),\r\n      },\r\n    };\r\n  }\r\n\r\n  private generateExecutionState(definition: any, status: string): any {\r\n    const steps = definition.steps || [];\r\n    const stepStates: any = {};\r\n    const completedSteps: string[] = [];\r\n    const failedSteps: string[] = [];\r\n    const pendingSteps: string[] = [];\r\n\r\n    steps.forEach((step, index) => {\r\n      let stepStatus: string;\r\n\r\n      if (status === 'completed') {\r\n        stepStatus = 'completed';\r\n        completedSteps.push(step.id);\r\n      } else if (status === 'failed' && index < steps.length - 1) {\r\n        stepStatus = 'completed';\r\n        completedSteps.push(step.id);\r\n      } else if (status === 'failed' && index === steps.length - 1) {\r\n        stepStatus = 'failed';\r\n        failedSteps.push(step.id);\r\n      } else if (status === 'running' && index <= steps.length / 2) {\r\n        stepStatus = 'completed';\r\n        completedSteps.push(step.id);\r\n      } else if (status === 'running' && index === Math.floor(steps.length / 2) + 1) {\r\n        stepStatus = 'running';\r\n      } else {\r\n        stepStatus = 'pending';\r\n        pendingSteps.push(step.id);\r\n      }\r\n\r\n      stepStates[step.id] = {\r\n        status: stepStatus,\r\n        startedAt: stepStatus !== 'pending' ? faker.date.recent().toISOString() : undefined,\r\n        completedAt: stepStatus === 'completed' || stepStatus === 'failed'\r\n          ? faker.date.recent().toISOString() : undefined,\r\n        duration: stepStatus === 'completed' || stepStatus === 'failed'\r\n          ? faker.number.int({ min: 100, max: 10000 }) : undefined,\r\n        attempts: faker.number.int({ min: 1, max: 3 }),\r\n        output: stepStatus === 'completed' ? { success: true, data: 'test' } : undefined,\r\n        lastError: stepStatus === 'failed' ? faker.lorem.sentence() : undefined,\r\n      };\r\n    });\r\n\r\n    return {\r\n      currentStep: status === 'running' ? steps[Math.floor(steps.length / 2)]?.id : undefined,\r\n      completedSteps,\r\n      failedSteps,\r\n      skippedSteps: [],\r\n      pendingSteps,\r\n      stepStates,\r\n    };\r\n  }\r\n}"], "version": 3}