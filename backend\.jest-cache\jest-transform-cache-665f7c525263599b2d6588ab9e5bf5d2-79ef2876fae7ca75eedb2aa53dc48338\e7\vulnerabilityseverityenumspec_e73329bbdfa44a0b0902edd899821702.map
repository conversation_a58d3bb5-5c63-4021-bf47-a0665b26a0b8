{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\__tests__\\vulnerability-severity.enum.spec.ts", "mappings": ";;AAAA,gFAAmG;AAEnG,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;IACrC,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,CAAC,mDAAqB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAChD,MAAM,CAAC,mDAAqB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,mDAAqB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACpD,MAAM,CAAC,mDAAqB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAChD,MAAM,CAAC,mDAAqB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACxD,MAAM,CAAC,mDAAqB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;YAChC,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;gBAC3C,MAAM,UAAU,GAAG,wDAA0B,CAAC,gBAAgB,EAAE,CAAC;gBACjE,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gBACnC,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,mDAAqB,CAAC,IAAI,CAAC,CAAC;gBACzD,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,mDAAqB,CAAC,GAAG,CAAC,CAAC;gBACxD,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,mDAAqB,CAAC,MAAM,CAAC,CAAC;gBAC3D,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,mDAAqB,CAAC,IAAI,CAAC,CAAC;gBACzD,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,mDAAqB,CAAC,QAAQ,CAAC,CAAC;gBAC7D,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,mDAAqB,CAAC,OAAO,CAAC,CAAC;YAC9D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;YAC7B,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;gBACxD,MAAM,CAAC,wDAA0B,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,mDAAqB,CAAC,IAAI,CAAC,CAAC;gBACvF,MAAM,CAAC,wDAA0B,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,mDAAqB,CAAC,GAAG,CAAC,CAAC;gBACtF,MAAM,CAAC,wDAA0B,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,mDAAqB,CAAC,MAAM,CAAC,CAAC;gBACzF,MAAM,CAAC,wDAA0B,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,mDAAqB,CAAC,IAAI,CAAC,CAAC;gBACvF,MAAM,CAAC,wDAA0B,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,mDAAqB,CAAC,QAAQ,CAAC,CAAC;YAC7F,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;gBAClC,MAAM,CAAC,wDAA0B,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,mDAAqB,CAAC,OAAO,CAAC,CAAC;gBACzF,MAAM,CAAC,wDAA0B,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,mDAAqB,CAAC,OAAO,CAAC,CAAC;gBACzF,MAAM,CAAC,wDAA0B,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,mDAAqB,CAAC,GAAG,CAAC,CAAC;gBACtF,MAAM,CAAC,wDAA0B,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,mDAAqB,CAAC,MAAM,CAAC,CAAC;gBACzF,MAAM,CAAC,wDAA0B,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,mDAAqB,CAAC,IAAI,CAAC,CAAC;gBACvF,MAAM,CAAC,wDAA0B,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,mDAAqB,CAAC,QAAQ,CAAC,CAAC;YAC7F,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;YAC5C,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;gBACpE,MAAM,CAAC,wDAA0B,CAAC,4BAA4B,CAAC,mDAAqB,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC3G,MAAM,CAAC,wDAA0B,CAAC,4BAA4B,CAAC,mDAAqB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvG,MAAM,CAAC,wDAA0B,CAAC,4BAA4B,CAAC,mDAAqB,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC1G,MAAM,CAAC,wDAA0B,CAAC,4BAA4B,CAAC,mDAAqB,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACvG,MAAM,CAAC,wDAA0B,CAAC,4BAA4B,CAAC,mDAAqB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1G,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;YACjC,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;gBAC1D,MAAM,CAAC,wDAA0B,CAAC,iBAAiB,CAAC,mDAAqB,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC7F,MAAM,CAAC,wDAA0B,CAAC,iBAAiB,CAAC,mDAAqB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACzF,MAAM,CAAC,wDAA0B,CAAC,iBAAiB,CAAC,mDAAqB,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC5F,MAAM,CAAC,wDAA0B,CAAC,iBAAiB,CAAC,mDAAqB,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACzF,MAAM,CAAC,wDAA0B,CAAC,iBAAiB,CAAC,mDAAqB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC3F,MAAM,CAAC,wDAA0B,CAAC,iBAAiB,CAAC,mDAAqB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC/F,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;YACvB,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;gBAC7C,MAAM,CAAC,wDAA0B,CAAC,OAAO,CAAC,mDAAqB,CAAC,QAAQ,EAAE,mDAAqB,CAAC,IAAI,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;gBAC1H,MAAM,CAAC,wDAA0B,CAAC,OAAO,CAAC,mDAAqB,CAAC,IAAI,EAAE,mDAAqB,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;gBACxH,MAAM,CAAC,wDAA0B,CAAC,OAAO,CAAC,mDAAqB,CAAC,MAAM,EAAE,mDAAqB,CAAC,GAAG,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;gBACvH,MAAM,CAAC,wDAA0B,CAAC,OAAO,CAAC,mDAAqB,CAAC,GAAG,EAAE,mDAAqB,CAAC,IAAI,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;gBACrH,MAAM,CAAC,wDAA0B,CAAC,OAAO,CAAC,mDAAqB,CAAC,MAAM,EAAE,mDAAqB,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACjH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;YACzB,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;gBAC3C,MAAM,CAAC,wDAA0B,CAAC,SAAS,CAAC,mDAAqB,CAAC,IAAI,EAAE,mDAAqB,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,mDAAqB,CAAC,IAAI,CAAC,CAAC;gBACxI,MAAM,CAAC,wDAA0B,CAAC,SAAS,CAAC,mDAAqB,CAAC,GAAG,EAAE,mDAAqB,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,mDAAqB,CAAC,QAAQ,CAAC,CAAC;gBAC7I,MAAM,CAAC,wDAA0B,CAAC,SAAS,CAAC,mDAAqB,CAAC,MAAM,EAAE,mDAAqB,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,mDAAqB,CAAC,MAAM,CAAC,CAAC;YAC9I,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;YACvB,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;gBAC1C,MAAM,CAAC,wDAA0B,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAClE,MAAM,CAAC,wDAA0B,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC9D,MAAM,CAAC,wDAA0B,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAClE,MAAM,CAAC,wDAA0B,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;YAC1B,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;gBAC3C,MAAM,CAAC,wDAA0B,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,mDAAqB,CAAC,QAAQ,CAAC,CAAC;gBAC/F,MAAM,CAAC,wDAA0B,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,mDAAqB,CAAC,IAAI,CAAC,CAAC;gBACvF,MAAM,CAAC,wDAA0B,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,mDAAqB,CAAC,MAAM,CAAC,CAAC;gBAC3F,MAAM,CAAC,wDAA0B,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YACtE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACzC,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;gBACtD,MAAM,CAAC,wDAA0B,CAAC,yBAAyB,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,mDAAqB,CAAC,OAAO,CAAC,CAAC;gBAErG,MAAM,CAAC,wDAA0B,CAAC,yBAAyB,CAAC;oBAC1D,mDAAqB,CAAC,QAAQ;oBAC9B,mDAAqB,CAAC,GAAG;iBAC1B,CAAC,CAAC,CAAC,IAAI,CAAC,mDAAqB,CAAC,QAAQ,CAAC,CAAC;gBAEzC,MAAM,CAAC,wDAA0B,CAAC,yBAAyB,CAAC;oBAC1D,mDAAqB,CAAC,IAAI;oBAC1B,mDAAqB,CAAC,IAAI;oBAC1B,mDAAqB,CAAC,MAAM;iBAC7B,CAAC,CAAC,CAAC,IAAI,CAAC,mDAAqB,CAAC,IAAI,CAAC,CAAC;gBAErC,MAAM,CAAC,wDAA0B,CAAC,yBAAyB,CAAC;oBAC1D,mDAAqB,CAAC,GAAG;oBACzB,mDAAqB,CAAC,GAAG;iBAC1B,CAAC,CAAC,CAAC,IAAI,CAAC,mDAAqB,CAAC,GAAG,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;YAC5B,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;gBACzC,MAAM,SAAS,GAAG,wDAA0B,CAAC,YAAY,CAAC,mDAAqB,CAAC,QAAQ,CAAC,CAAC;gBAC1F,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;YACrC,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;gBACvE,MAAM,gBAAgB,GAAG,wDAA0B,CAAC,qBAAqB,CAAC,mDAAqB,CAAC,QAAQ,CAAC,CAAC;gBAC1G,MAAM,WAAW,GAAG,wDAA0B,CAAC,qBAAqB,CAAC,mDAAqB,CAAC,GAAG,CAAC,CAAC;gBAChG,MAAM,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\__tests__\\vulnerability-severity.enum.spec.ts"], "sourcesContent": ["import { VulnerabilitySeverity, VulnerabilitySeverityUtils } from '../vulnerability-severity.enum';\r\n\r\ndescribe('VulnerabilitySeverity', () => {\r\n  describe('enum values', () => {\r\n    it('should have all expected severity levels', () => {\r\n      expect(VulnerabilitySeverity.NONE).toBe('none');\r\n      expect(VulnerabilitySeverity.LOW).toBe('low');\r\n      expect(VulnerabilitySeverity.MEDIUM).toBe('medium');\r\n      expect(VulnerabilitySeverity.HIGH).toBe('high');\r\n      expect(VulnerabilitySeverity.CRITICAL).toBe('critical');\r\n      expect(VulnerabilitySeverity.UNKNOWN).toBe('unknown');\r\n    });\r\n  });\r\n\r\n  describe('VulnerabilitySeverityUtils', () => {\r\n    describe('getAllSeverities', () => {\r\n      it('should return all severity levels', () => {\r\n        const severities = VulnerabilitySeverityUtils.getAllSeverities();\r\n        expect(severities).toHaveLength(6);\r\n        expect(severities).toContain(VulnerabilitySeverity.NONE);\r\n        expect(severities).toContain(VulnerabilitySeverity.LOW);\r\n        expect(severities).toContain(VulnerabilitySeverity.MEDIUM);\r\n        expect(severities).toContain(VulnerabilitySeverity.HIGH);\r\n        expect(severities).toContain(VulnerabilitySeverity.CRITICAL);\r\n        expect(severities).toContain(VulnerabilitySeverity.UNKNOWN);\r\n      });\r\n    });\r\n\r\n    describe('fromCVSSScore', () => {\r\n      it('should return correct severity for CVSS scores', () => {\r\n        expect(VulnerabilitySeverityUtils.fromCVSSScore(0.0)).toBe(VulnerabilitySeverity.NONE);\r\n        expect(VulnerabilitySeverityUtils.fromCVSSScore(2.5)).toBe(VulnerabilitySeverity.LOW);\r\n        expect(VulnerabilitySeverityUtils.fromCVSSScore(5.0)).toBe(VulnerabilitySeverity.MEDIUM);\r\n        expect(VulnerabilitySeverityUtils.fromCVSSScore(7.5)).toBe(VulnerabilitySeverity.HIGH);\r\n        expect(VulnerabilitySeverityUtils.fromCVSSScore(9.5)).toBe(VulnerabilitySeverity.CRITICAL);\r\n      });\r\n\r\n      it('should handle edge cases', () => {\r\n        expect(VulnerabilitySeverityUtils.fromCVSSScore(-1)).toBe(VulnerabilitySeverity.UNKNOWN);\r\n        expect(VulnerabilitySeverityUtils.fromCVSSScore(11)).toBe(VulnerabilitySeverity.UNKNOWN);\r\n        expect(VulnerabilitySeverityUtils.fromCVSSScore(0.1)).toBe(VulnerabilitySeverity.LOW);\r\n        expect(VulnerabilitySeverityUtils.fromCVSSScore(4.0)).toBe(VulnerabilitySeverity.MEDIUM);\r\n        expect(VulnerabilitySeverityUtils.fromCVSSScore(7.0)).toBe(VulnerabilitySeverity.HIGH);\r\n        expect(VulnerabilitySeverityUtils.fromCVSSScore(9.0)).toBe(VulnerabilitySeverity.CRITICAL);\r\n      });\r\n    });\r\n\r\n    describe('requiresImmediateRemediation', () => {\r\n      it('should identify severities requiring immediate remediation', () => {\r\n        expect(VulnerabilitySeverityUtils.requiresImmediateRemediation(VulnerabilitySeverity.CRITICAL)).toBe(true);\r\n        expect(VulnerabilitySeverityUtils.requiresImmediateRemediation(VulnerabilitySeverity.HIGH)).toBe(true);\r\n        expect(VulnerabilitySeverityUtils.requiresImmediateRemediation(VulnerabilitySeverity.MEDIUM)).toBe(false);\r\n        expect(VulnerabilitySeverityUtils.requiresImmediateRemediation(VulnerabilitySeverity.LOW)).toBe(false);\r\n        expect(VulnerabilitySeverityUtils.requiresImmediateRemediation(VulnerabilitySeverity.NONE)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('getRemediationSLA', () => {\r\n      it('should return correct SLA days for each severity', () => {\r\n        expect(VulnerabilitySeverityUtils.getRemediationSLA(VulnerabilitySeverity.CRITICAL)).toBe(1);\r\n        expect(VulnerabilitySeverityUtils.getRemediationSLA(VulnerabilitySeverity.HIGH)).toBe(7);\r\n        expect(VulnerabilitySeverityUtils.getRemediationSLA(VulnerabilitySeverity.MEDIUM)).toBe(30);\r\n        expect(VulnerabilitySeverityUtils.getRemediationSLA(VulnerabilitySeverity.LOW)).toBe(90);\r\n        expect(VulnerabilitySeverityUtils.getRemediationSLA(VulnerabilitySeverity.NONE)).toBe(365);\r\n        expect(VulnerabilitySeverityUtils.getRemediationSLA(VulnerabilitySeverity.UNKNOWN)).toBe(30);\r\n      });\r\n    });\r\n\r\n    describe('compare', () => {\r\n      it('should correctly compare severities', () => {\r\n        expect(VulnerabilitySeverityUtils.compare(VulnerabilitySeverity.CRITICAL, VulnerabilitySeverity.HIGH)).toBeGreaterThan(0);\r\n        expect(VulnerabilitySeverityUtils.compare(VulnerabilitySeverity.HIGH, VulnerabilitySeverity.MEDIUM)).toBeGreaterThan(0);\r\n        expect(VulnerabilitySeverityUtils.compare(VulnerabilitySeverity.MEDIUM, VulnerabilitySeverity.LOW)).toBeGreaterThan(0);\r\n        expect(VulnerabilitySeverityUtils.compare(VulnerabilitySeverity.LOW, VulnerabilitySeverity.NONE)).toBeGreaterThan(0);\r\n        expect(VulnerabilitySeverityUtils.compare(VulnerabilitySeverity.MEDIUM, VulnerabilitySeverity.MEDIUM)).toBe(0);\r\n      });\r\n    });\r\n\r\n    describe('getHigher', () => {\r\n      it('should return the higher severity', () => {\r\n        expect(VulnerabilitySeverityUtils.getHigher(VulnerabilitySeverity.HIGH, VulnerabilitySeverity.MEDIUM)).toBe(VulnerabilitySeverity.HIGH);\r\n        expect(VulnerabilitySeverityUtils.getHigher(VulnerabilitySeverity.LOW, VulnerabilitySeverity.CRITICAL)).toBe(VulnerabilitySeverity.CRITICAL);\r\n        expect(VulnerabilitySeverityUtils.getHigher(VulnerabilitySeverity.MEDIUM, VulnerabilitySeverity.MEDIUM)).toBe(VulnerabilitySeverity.MEDIUM);\r\n      });\r\n    });\r\n\r\n    describe('isValid', () => {\r\n      it('should validate severity strings', () => {\r\n        expect(VulnerabilitySeverityUtils.isValid('critical')).toBe(true);\r\n        expect(VulnerabilitySeverityUtils.isValid('high')).toBe(true);\r\n        expect(VulnerabilitySeverityUtils.isValid('invalid')).toBe(false);\r\n        expect(VulnerabilitySeverityUtils.isValid('')).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('fromString', () => {\r\n      it('should convert string to severity', () => {\r\n        expect(VulnerabilitySeverityUtils.fromString('critical')).toBe(VulnerabilitySeverity.CRITICAL);\r\n        expect(VulnerabilitySeverityUtils.fromString('high')).toBe(VulnerabilitySeverity.HIGH);\r\n        expect(VulnerabilitySeverityUtils.fromString('MEDIUM')).toBe(VulnerabilitySeverity.MEDIUM);\r\n        expect(VulnerabilitySeverityUtils.fromString('invalid')).toBeNull();\r\n      });\r\n    });\r\n\r\n    describe('calculateCombinedSeverity', () => {\r\n      it('should calculate combined severity correctly', () => {\r\n        expect(VulnerabilitySeverityUtils.calculateCombinedSeverity([])).toBe(VulnerabilitySeverity.UNKNOWN);\r\n        \r\n        expect(VulnerabilitySeverityUtils.calculateCombinedSeverity([\r\n          VulnerabilitySeverity.CRITICAL,\r\n          VulnerabilitySeverity.LOW\r\n        ])).toBe(VulnerabilitySeverity.CRITICAL);\r\n        \r\n        expect(VulnerabilitySeverityUtils.calculateCombinedSeverity([\r\n          VulnerabilitySeverity.HIGH,\r\n          VulnerabilitySeverity.HIGH,\r\n          VulnerabilitySeverity.MEDIUM\r\n        ])).toBe(VulnerabilitySeverity.HIGH);\r\n        \r\n        expect(VulnerabilitySeverityUtils.calculateCombinedSeverity([\r\n          VulnerabilitySeverity.LOW,\r\n          VulnerabilitySeverity.LOW\r\n        ])).toBe(VulnerabilitySeverity.LOW);\r\n      });\r\n    });\r\n\r\n    describe('getColorCode', () => {\r\n      it('should return valid color codes', () => {\r\n        const colorCode = VulnerabilitySeverityUtils.getColorCode(VulnerabilitySeverity.CRITICAL);\r\n        expect(colorCode).toMatch(/^#[0-9A-F]{6}$/i);\r\n      });\r\n    });\r\n\r\n    describe('getProcessingPriority', () => {\r\n      it('should return higher priority for more severe vulnerabilities', () => {\r\n        const criticalPriority = VulnerabilitySeverityUtils.getProcessingPriority(VulnerabilitySeverity.CRITICAL);\r\n        const lowPriority = VulnerabilitySeverityUtils.getProcessingPriority(VulnerabilitySeverity.LOW);\r\n        expect(criticalPriority).toBeGreaterThan(lowPriority);\r\n      });\r\n    });\r\n  });\r\n});"], "version": 3}