b63c0ba5de27ad731d90094177b9afe1
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIModelPerformanceUpdatedEvent = void 0;
const base_domain_event_1 = require("../../../../shared-kernel/domain/base-domain-event");
/**
 * AI Model Performance Updated Domain Event
 *
 * Published when an AI model's performance metrics are updated.
 * This event can trigger various downstream processes such as
 * model ranking updates, performance alerts, and optimization workflows.
 */
class AIModelPerformanceUpdatedEvent extends base_domain_event_1.BaseDomainEvent {
    constructor(modelId, performance, eventId, occurredOn) {
        super(eventId, occurredOn);
        this.modelId = modelId;
        this.performance = performance;
    }
    getEventName() {
        return 'AIModelPerformanceUpdated';
    }
    getEventVersion() {
        return '1.0';
    }
    getEventData() {
        return {
            modelId: this.modelId.toString(),
            performance: {
                totalRequests: this.performance.totalRequests,
                successfulRequests: this.performance.successfulRequests,
                failedRequests: this.performance.failedRequests,
                averageLatency: this.performance.averageLatency,
                p95Latency: this.performance.p95Latency,
                p99Latency: this.performance.p99Latency,
                accuracy: this.performance.accuracy,
                precision: this.performance.precision,
                recall: this.performance.recall,
                f1Score: this.performance.f1Score,
                throughput: this.performance.throughput,
                lastUpdated: this.performance.lastUpdated.toISOString(),
            },
        };
    }
}
exports.AIModelPerformanceUpdatedEvent = AIModelPerformanceUpdatedEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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