{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\domain\\base-entity.spec.ts", "mappings": ";;AAAA,0DAAsD;AACtD,qGAAmF;AASnF,MAAM,UAAW,SAAQ,wBAA2B;IAClD,YAAY,KAAsB,EAAE,EAAmB;QACrD,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IACnB,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IACzB,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;IAC1B,CAAC;IAED,IAAI,GAAG;QACL,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;IACxB,CAAC;IAED,qCAAqC;IACrC,UAAU,CAAC,IAAY;QACpB,IAAI,CAAC,KAAa,CAAC,IAAI,GAAG,IAAI,CAAC;IAClC,CAAC;CACF;AAED,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;IAC1B,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,KAAK,GAAoB;gBAC7B,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,kBAAkB;gBACzB,GAAG,EAAE,EAAE;aACR,CAAC;YAEF,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;YAErC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,KAAK,GAAoB;gBAC7B,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,kBAAkB;aAC1B,CAAC;YACF,MAAM,EAAE,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YAErC,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YAEzC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,KAAK,GAAoB;gBAC7B,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,kBAAkB;aAC1B,CAAC;YAEF,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;YAErC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,8CAAc,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,KAAK,GAAoB;gBAC7B,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,kBAAkB;gBACzB,mCAAmC;aACpC,CAAC;YAEF,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;YAErC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,KAAK,GAAoB;gBAC7B,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,kBAAkB;aAC1B,CAAC;YAEF,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;YACtC,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;YAEtC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,KAAK,GAAoB;gBAC7B,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,kBAAkB;aAC1B,CAAC;YAEF,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;YACrC,MAAM,UAAU,GAAG,MAAM,CAAC,EAAE,CAAC;YAE7B,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAE9B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC;gBAC5B,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,kBAAkB;aAC1B,CAAC,CAAC;YAEH,MAAM,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;YAErB,8CAA8C;YAC9C,MAAM,CAAC,GAAG,EAAE;gBACT,MAAc,CAAC,EAAE,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YACjD,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;QACf,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC;gBAC5B,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,kBAAkB;aAC1B,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,EAAE,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,MAAM,GAAoB;gBAC9B,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,kBAAkB;aAC1B,CAAC;YACF,MAAM,MAAM,GAAoB;gBAC9B,IAAI,EAAE,UAAU,EAAE,kBAAkB;gBACpC,KAAK,EAAE,kBAAkB;aAC1B,CAAC;YAEF,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;YAC3C,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;YAE3C,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,KAAK,GAAoB;gBAC7B,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,kBAAkB;aAC1B,CAAC;YAEF,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;YACtC,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;YAEtC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC;gBAC5B,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,kBAAkB;aAC1B,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC;gBAC5B,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,kBAAkB;aAC1B,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,KAAK,GAAoB;gBAC7B,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,kBAAkB;gBACzB,GAAG,EAAE,EAAE;aACR,CAAC;YAEF,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;YACrC,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAE7B,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;YACxD,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;YACzD,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,KAAK,GAAoB;gBAC7B,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,kBAAkB;gBACzB,mBAAmB;aACpB,CAAC;YAEF,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;YACrC,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAE7B,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;YACzD,4DAA4D;YAC5D,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,KAAK,GAAoB;gBAC7B,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,kBAAkB;gBACzB,GAAG,EAAE,EAAE;aACR,CAAC;YAEF,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;YACrC,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAE7B,mCAAmC;YACnC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,KAAK,GAAoB;gBAC7B,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,kBAAkB;gBACzB,GAAG,EAAE,EAAE;aACR,CAAC;YAEF,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;YAErC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,KAAK,GAAoB;gBAC7B,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,kBAAkB;gBACzB,GAAG,EAAE,EAAE;aACR,CAAC;YAEF,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;YAErC,uDAAuD;YACvD,MAAM,CAAE,MAAc,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,KAAK,GAAoB;gBAC7B,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,kBAAkB;aAC1B,CAAC;YAEF,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;YAErC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAErC,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAE9B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,uEAAuE;YACvE,MAAM,UAAU,GAAoB;gBAClC,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,kBAAkB;gBACzB,GAAG,EAAE,EAAE;aACR,CAAC;YAEF,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC;gBAC5B,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,kBAAkB;aAC1B,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,8CAAc,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAM3B,MAAM,cAAe,SAAQ,UAAU;YACrC,YAAY,KAA0B,EAAE,EAAmB;gBACzD,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YACnB,CAAC;YAED,IAAI,UAAU;gBACZ,OAAQ,IAAI,CAAC,KAA6B,CAAC,UAAU,CAAC;YACxD,CAAC;SACF;QAED,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,KAAK,GAAwB;gBACjC,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,kBAAkB;gBACzB,GAAG,EAAE,EAAE;gBACP,UAAU,EAAE,aAAa;aAC1B,CAAC;YAEF,MAAM,MAAM,GAAG,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC;YAEzC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,wBAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,KAAK,GAAwB;gBACjC,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,kBAAkB;gBACzB,UAAU,EAAE,aAAa;aAC1B,CAAC;YAEF,MAAM,MAAM,GAAG,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC;YAEzC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,8CAAc,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,KAAK,GAAoB;gBAC7B,IAAI,EAAE,EAAE;gBACR,KAAK,EAAE,EAAE;aACV,CAAC;YAEF,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;YAErC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC7B,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wBAAwB,EAAE,GAAG,EAAE;YAChC,MAAM,KAAK,GAAoB;gBAC7B,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,kBAAkB;gBACzB,GAAG,EAAE,CAAC;aACP,CAAC;YAEF,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;YAErC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,KAAK,GAAoB;gBAC7B,IAAI,EAAE,eAAe;gBACrB,KAAK,EAAE,sBAAsB;gBAC7B,GAAG,EAAE,CAAC,CAAC;aACR,CAAC;YAEF,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;YAErC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,KAAK,GAAoB;gBAC7B,IAAI,EAAE,wBAAwB;gBAC9B,KAAK,EAAE,wBAAwB;aAChC,CAAC;YAEF,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;YAErC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\domain\\base-entity.spec.ts"], "sourcesContent": ["import { BaseEntity } from '../../domain/base-entity';\r\nimport { UniqueEntityId } from '../../value-objects/unique-entity-id.value-object';\r\n\r\n// Test implementation of BaseEntity\r\ninterface TestEntityProps {\r\n  name: string;\r\n  email: string;\r\n  age?: number;\r\n}\r\n\r\nclass TestEntity extends BaseEntity<TestEntityProps> {\r\n  constructor(props: TestEntityProps, id?: UniqueEntityId) {\r\n    super(props, id);\r\n  }\r\n\r\n  get name(): string {\r\n    return this.props.name;\r\n  }\r\n\r\n  get email(): string {\r\n    return this.props.email;\r\n  }\r\n\r\n  get age(): number | undefined {\r\n    return this.props.age;\r\n  }\r\n\r\n  // Method to update props for testing\r\n  updateName(name: string): void {\r\n    (this.props as any).name = name;\r\n  }\r\n}\r\n\r\ndescribe('BaseEntity', () => {\r\n  describe('construction', () => {\r\n    it('should create entity with provided props', () => {\r\n      const props: TestEntityProps = {\r\n        name: '<PERSON>',\r\n        email: '<EMAIL>',\r\n        age: 30\r\n      };\r\n      \r\n      const entity = new TestEntity(props);\r\n      \r\n      expect(entity.name).toBe('<PERSON>');\r\n      expect(entity.email).toBe('<EMAIL>');\r\n      expect(entity.age).toBe(30);\r\n    });\r\n\r\n    it('should create entity with provided ID', () => {\r\n      const props: TestEntityProps = {\r\n        name: 'John Doe',\r\n        email: '<EMAIL>'\r\n      };\r\n      const id = UniqueEntityId.generate();\r\n      \r\n      const entity = new TestEntity(props, id);\r\n      \r\n      expect(entity.id.equals(id)).toBe(true);\r\n    });\r\n\r\n    it('should generate ID if not provided', () => {\r\n      const props: TestEntityProps = {\r\n        name: 'John Doe',\r\n        email: '<EMAIL>'\r\n      };\r\n      \r\n      const entity = new TestEntity(props);\r\n      \r\n      expect(entity.id).toBeInstanceOf(UniqueEntityId);\r\n      expect(entity.id.value).toBeDefined();\r\n    });\r\n\r\n    it('should handle optional properties', () => {\r\n      const props: TestEntityProps = {\r\n        name: 'John Doe',\r\n        email: '<EMAIL>'\r\n        // age is optional and not provided\r\n      };\r\n      \r\n      const entity = new TestEntity(props);\r\n      \r\n      expect(entity.name).toBe('John Doe');\r\n      expect(entity.email).toBe('<EMAIL>');\r\n      expect(entity.age).toBeUndefined();\r\n    });\r\n  });\r\n\r\n  describe('identity', () => {\r\n    it('should have unique identity', () => {\r\n      const props: TestEntityProps = {\r\n        name: 'John Doe',\r\n        email: '<EMAIL>'\r\n      };\r\n      \r\n      const entity1 = new TestEntity(props);\r\n      const entity2 = new TestEntity(props);\r\n      \r\n      expect(entity1.id.equals(entity2.id)).toBe(false);\r\n    });\r\n\r\n    it('should maintain identity across property changes', () => {\r\n      const props: TestEntityProps = {\r\n        name: 'John Doe',\r\n        email: '<EMAIL>'\r\n      };\r\n      \r\n      const entity = new TestEntity(props);\r\n      const originalId = entity.id;\r\n      \r\n      entity.updateName('Jane Doe');\r\n      \r\n      expect(entity.id.equals(originalId)).toBe(true);\r\n    });\r\n\r\n    it('should expose ID as readonly', () => {\r\n      const entity = new TestEntity({\r\n        name: 'John Doe',\r\n        email: '<EMAIL>'\r\n      });\r\n      \r\n      const id = entity.id;\r\n      \r\n      // Attempt to modify ID should not be possible\r\n      expect(() => {\r\n        (entity as any).id = UniqueEntityId.generate();\r\n      }).toThrow();\r\n    });\r\n  });\r\n\r\n  describe('equality comparison', () => {\r\n    it('should be equal to itself', () => {\r\n      const entity = new TestEntity({\r\n        name: 'John Doe',\r\n        email: '<EMAIL>'\r\n      });\r\n      \r\n      expect(entity.equals(entity)).toBe(true);\r\n    });\r\n\r\n    it('should be equal to entity with same ID', () => {\r\n      const id = UniqueEntityId.generate();\r\n      const props1: TestEntityProps = {\r\n        name: 'John Doe',\r\n        email: '<EMAIL>'\r\n      };\r\n      const props2: TestEntityProps = {\r\n        name: 'Jane Doe', // Different props\r\n        email: '<EMAIL>'\r\n      };\r\n      \r\n      const entity1 = new TestEntity(props1, id);\r\n      const entity2 = new TestEntity(props2, id);\r\n      \r\n      expect(entity1.equals(entity2)).toBe(true);\r\n    });\r\n\r\n    it('should not be equal to entity with different ID', () => {\r\n      const props: TestEntityProps = {\r\n        name: 'John Doe',\r\n        email: '<EMAIL>'\r\n      };\r\n      \r\n      const entity1 = new TestEntity(props);\r\n      const entity2 = new TestEntity(props);\r\n      \r\n      expect(entity1.equals(entity2)).toBe(false);\r\n    });\r\n\r\n    it('should not be equal to null', () => {\r\n      const entity = new TestEntity({\r\n        name: 'John Doe',\r\n        email: '<EMAIL>'\r\n      });\r\n      \r\n      expect(entity.equals(null)).toBe(false);\r\n    });\r\n\r\n    it('should not be equal to undefined', () => {\r\n      const entity = new TestEntity({\r\n        name: 'John Doe',\r\n        email: '<EMAIL>'\r\n      });\r\n      \r\n      expect(entity.equals(undefined)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('JSON serialization', () => {\r\n    it('should serialize to JSON with ID and props', () => {\r\n      const props: TestEntityProps = {\r\n        name: 'John Doe',\r\n        email: '<EMAIL>',\r\n        age: 30\r\n      };\r\n      \r\n      const entity = new TestEntity(props);\r\n      const json = entity.toJSON();\r\n      \r\n      expect(json).toHaveProperty('id', entity.id.toString());\r\n      expect(json).toHaveProperty('name', 'John Doe');\r\n      expect(json).toHaveProperty('email', '<EMAIL>');\r\n      expect(json).toHaveProperty('age', 30);\r\n    });\r\n\r\n    it('should handle undefined optional properties in JSON', () => {\r\n      const props: TestEntityProps = {\r\n        name: 'John Doe',\r\n        email: '<EMAIL>'\r\n        // age is undefined\r\n      };\r\n      \r\n      const entity = new TestEntity(props);\r\n      const json = entity.toJSON();\r\n      \r\n      expect(json).toHaveProperty('id');\r\n      expect(json).toHaveProperty('name', 'John Doe');\r\n      expect(json).toHaveProperty('email', '<EMAIL>');\r\n      // age property should not be present in JSON when undefined\r\n      expect(json.age).toBeUndefined();\r\n    });\r\n\r\n    it('should include all props in JSON', () => {\r\n      const props: TestEntityProps = {\r\n        name: 'John Doe',\r\n        email: '<EMAIL>',\r\n        age: 30\r\n      };\r\n      \r\n      const entity = new TestEntity(props);\r\n      const json = entity.toJSON();\r\n      \r\n      // Should include ID plus all props\r\n      expect(Object.keys(json)).toEqual(['id', 'name', 'email', 'age']);\r\n    });\r\n  });\r\n\r\n  describe('props access', () => {\r\n    it('should provide access to props through getters', () => {\r\n      const props: TestEntityProps = {\r\n        name: 'John Doe',\r\n        email: '<EMAIL>',\r\n        age: 30\r\n      };\r\n      \r\n      const entity = new TestEntity(props);\r\n      \r\n      expect(entity.name).toBe(props.name);\r\n      expect(entity.email).toBe(props.email);\r\n      expect(entity.age).toBe(props.age);\r\n    });\r\n\r\n    it('should maintain props reference', () => {\r\n      const props: TestEntityProps = {\r\n        name: 'John Doe',\r\n        email: '<EMAIL>',\r\n        age: 30\r\n      };\r\n      \r\n      const entity = new TestEntity(props);\r\n      \r\n      // Props should be the same reference (for performance)\r\n      expect((entity as any).props).toBe(props);\r\n    });\r\n\r\n    it('should allow props modification through entity methods', () => {\r\n      const props: TestEntityProps = {\r\n        name: 'John Doe',\r\n        email: '<EMAIL>'\r\n      };\r\n      \r\n      const entity = new TestEntity(props);\r\n      \r\n      expect(entity.name).toBe('John Doe');\r\n      \r\n      entity.updateName('Jane Doe');\r\n      \r\n      expect(entity.name).toBe('Jane Doe');\r\n    });\r\n  });\r\n\r\n  describe('type safety', () => {\r\n    it('should enforce props type at compile time', () => {\r\n      // This test ensures TypeScript compilation succeeds with correct types\r\n      const validProps: TestEntityProps = {\r\n        name: 'John Doe',\r\n        email: '<EMAIL>',\r\n        age: 30\r\n      };\r\n      \r\n      const entity = new TestEntity(validProps);\r\n      expect(entity).toBeInstanceOf(TestEntity);\r\n    });\r\n\r\n    it('should maintain type safety for ID', () => {\r\n      const entity = new TestEntity({\r\n        name: 'John Doe',\r\n        email: '<EMAIL>'\r\n      });\r\n      \r\n      expect(entity.id).toBeInstanceOf(UniqueEntityId);\r\n    });\r\n  });\r\n\r\n  describe('inheritance', () => {\r\n    // Extended entity for testing inheritance\r\n    interface ExtendedEntityProps extends TestEntityProps {\r\n      department: string;\r\n    }\r\n\r\n    class ExtendedEntity extends TestEntity {\r\n      constructor(props: ExtendedEntityProps, id?: UniqueEntityId) {\r\n        super(props, id);\r\n      }\r\n\r\n      get department(): string {\r\n        return (this.props as ExtendedEntityProps).department;\r\n      }\r\n    }\r\n\r\n    it('should support inheritance', () => {\r\n      const props: ExtendedEntityProps = {\r\n        name: 'John Doe',\r\n        email: '<EMAIL>',\r\n        age: 30,\r\n        department: 'Engineering'\r\n      };\r\n      \r\n      const entity = new ExtendedEntity(props);\r\n      \r\n      expect(entity).toBeInstanceOf(ExtendedEntity);\r\n      expect(entity).toBeInstanceOf(TestEntity);\r\n      expect(entity).toBeInstanceOf(BaseEntity);\r\n      expect(entity.department).toBe('Engineering');\r\n    });\r\n\r\n    it('should maintain base functionality in derived classes', () => {\r\n      const props: ExtendedEntityProps = {\r\n        name: 'John Doe',\r\n        email: '<EMAIL>',\r\n        department: 'Engineering'\r\n      };\r\n      \r\n      const entity = new ExtendedEntity(props);\r\n      \r\n      expect(entity.id).toBeInstanceOf(UniqueEntityId);\r\n      expect(entity.name).toBe('John Doe');\r\n      expect(entity.email).toBe('<EMAIL>');\r\n      expect(entity.equals(entity)).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('edge cases', () => {\r\n    it('should handle empty string properties', () => {\r\n      const props: TestEntityProps = {\r\n        name: '',\r\n        email: ''\r\n      };\r\n      \r\n      const entity = new TestEntity(props);\r\n      \r\n      expect(entity.name).toBe('');\r\n      expect(entity.email).toBe('');\r\n    });\r\n\r\n    it('should handle zero age', () => {\r\n      const props: TestEntityProps = {\r\n        name: 'Baby Doe',\r\n        email: '<EMAIL>',\r\n        age: 0\r\n      };\r\n      \r\n      const entity = new TestEntity(props);\r\n      \r\n      expect(entity.age).toBe(0);\r\n    });\r\n\r\n    it('should handle negative age', () => {\r\n      const props: TestEntityProps = {\r\n        name: 'Time Traveler',\r\n        email: '<EMAIL>',\r\n        age: -1\r\n      };\r\n      \r\n      const entity = new TestEntity(props);\r\n      \r\n      expect(entity.age).toBe(-1);\r\n    });\r\n\r\n    it('should handle special characters in properties', () => {\r\n      const props: TestEntityProps = {\r\n        name: 'José María Aznar-López',\r\n        email: 'josé.marí*************'\r\n      };\r\n      \r\n      const entity = new TestEntity(props);\r\n      \r\n      expect(entity.name).toBe('José María Aznar-López');\r\n      expect(entity.email).toBe('josé.marí*************');\r\n    });\r\n  });\r\n});"], "version": 3}