2bec35e7b8bdfd59cb9e610b1801d61b
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const base_value_object_1 = require("../../value-objects/base-value-object");
// Test implementation of BaseValueObject
class TestValueObject extends base_value_object_1.BaseValueObject {
    constructor(value) {
        super(value);
    }
    validate() {
        super.validate();
        if (this._value.length < 3) {
            throw new Error('Value must be at least 3 characters long');
        }
    }
}
// Complex value object for testing equality
class ComplexValueObject extends base_value_object_1.BaseValueObject {
    constructor(value) {
        super(value);
    }
    validate() {
        super.validate();
        if (!this._value.name || this._value.age < 0) {
            throw new Error('Invalid complex value');
        }
    }
    getEqualityComponents() {
        return [this._value.name, this._value.age];
    }
}
// Nested value object for testing
class NestedValueObject extends base_value_object_1.BaseValueObject {
    constructor(simple, complex) {
        super({ simple, complex });
    }
    getEqualityComponents() {
        return [this._value.simple, this._value.complex];
    }
}
describe('BaseValueObject', () => {
    describe('construction and validation', () => {
        it('should create a valid value object', () => {
            const vo = new TestValueObject('test');
            expect(vo.value).toBe('test');
        });
        it('should throw error for null value', () => {
            expect(() => new TestValueObject(null)).toThrow('TestValueObject cannot be null or undefined');
        });
        it('should throw error for undefined value', () => {
            expect(() => new TestValueObject(undefined)).toThrow('TestValueObject cannot be null or undefined');
        });
        it('should throw error for invalid value', () => {
            expect(() => new TestValueObject('ab')).toThrow('Value must be at least 3 characters long');
        });
        it('should be immutable', () => {
            const vo = new TestValueObject('test');
            expect(Object.isFrozen(vo)).toBe(true);
        });
    });
    describe('equality comparison', () => {
        it('should be equal to itself', () => {
            const vo = new TestValueObject('test');
            expect(vo.equals(vo)).toBe(true);
        });
        it('should be equal to another value object with same value', () => {
            const vo1 = new TestValueObject('test');
            const vo2 = new TestValueObject('test');
            expect(vo1.equals(vo2)).toBe(true);
        });
        it('should not be equal to value object with different value', () => {
            const vo1 = new TestValueObject('test1');
            const vo2 = new TestValueObject('test2');
            expect(vo1.equals(vo2)).toBe(false);
        });
        it('should not be equal to null or undefined', () => {
            const vo = new TestValueObject('test');
            expect(vo.equals(null)).toBe(false);
            expect(vo.equals(undefined)).toBe(false);
        });
        it('should not be equal to different type', () => {
            const vo1 = new TestValueObject('test');
            const vo2 = new ComplexValueObject({ name: 'test', age: 25 });
            expect(vo1.equals(vo2)).toBe(false);
        });
        it('should handle complex object equality', () => {
            const vo1 = new ComplexValueObject({ name: 'John', age: 25 });
            const vo2 = new ComplexValueObject({ name: 'John', age: 25 });
            const vo3 = new ComplexValueObject({ name: 'Jane', age: 25 });
            expect(vo1.equals(vo2)).toBe(true);
            expect(vo1.equals(vo3)).toBe(false);
        });
        it('should handle nested value object equality', () => {
            const simple1 = new TestValueObject('test');
            const complex1 = new ComplexValueObject({ name: 'John', age: 25 });
            const nested1 = new NestedValueObject(simple1, complex1);
            const simple2 = new TestValueObject('test');
            const complex2 = new ComplexValueObject({ name: 'John', age: 25 });
            const nested2 = new NestedValueObject(simple2, complex2);
            const simple3 = new TestValueObject('different');
            const nested3 = new NestedValueObject(simple3, complex2);
            expect(nested1.equals(nested2)).toBe(true);
            expect(nested1.equals(nested3)).toBe(false);
        });
    });
    describe('string representation', () => {
        it('should convert simple value to string', () => {
            const vo = new TestValueObject('test');
            expect(vo.toString()).toBe('test');
        });
        it('should convert complex value to JSON string', () => {
            const vo = new ComplexValueObject({ name: 'John', age: 25 });
            expect(vo.toString()).toBe('{"name":"John","age":25}');
        });
    });
    describe('JSON serialization', () => {
        it('should serialize simple value', () => {
            const vo = new TestValueObject('test');
            expect(vo.toJSON()).toBe('test');
        });
        it('should serialize complex value', () => {
            const vo = new ComplexValueObject({ name: 'John', age: 25 });
            expect(vo.toJSON()).toEqual({ name: 'John', age: 25 });
        });
        it('should serialize nested value objects', () => {
            const simple = new TestValueObject('test');
            const complex = new ComplexValueObject({ name: 'John', age: 25 });
            const nested = new NestedValueObject(simple, complex);
            const json = nested.toJSON();
            expect(json).toEqual({
                simple: 'test',
                complex: { name: 'John', age: 25 }
            });
        });
    });
    describe('hash code', () => {
        it('should generate consistent hash codes', () => {
            const vo1 = new TestValueObject('test');
            const vo2 = new TestValueObject('test');
            expect(vo1.getHashCode()).toBe(vo2.getHashCode());
        });
        it('should generate different hash codes for different values', () => {
            const vo1 = new TestValueObject('test1');
            const vo2 = new TestValueObject('test2');
            expect(vo1.getHashCode()).not.toBe(vo2.getHashCode());
        });
        it('should handle complex objects', () => {
            const vo1 = new ComplexValueObject({ name: 'John', age: 25 });
            const vo2 = new ComplexValueObject({ name: 'John', age: 25 });
            const vo3 = new ComplexValueObject({ name: 'Jane', age: 25 });
            expect(vo1.getHashCode()).toBe(vo2.getHashCode());
            expect(vo1.getHashCode()).not.toBe(vo3.getHashCode());
        });
    });
    describe('cloning', () => {
        it('should create a clone with same value', () => {
            const vo = new TestValueObject('test');
            const clone = vo.clone();
            expect(clone).not.toBe(vo);
            expect(clone.equals(vo)).toBe(true);
            expect(clone.value).toBe(vo.value);
        });
        it('should create independent clones', () => {
            const vo = new TestValueObject('test');
            const clone = vo.clone();
            expect(clone).toBeInstanceOf(TestValueObject);
            expect(clone.equals(vo)).toBe(true);
        });
    });
    describe('validation utilities', () => {
        it('should return true for valid value object', () => {
            const vo = new TestValueObject('test');
            expect(vo.isValid()).toBe(true);
        });
        it('should return null for valid value object validation error', () => {
            const vo = new TestValueObject('test');
            expect(vo.getValidationError()).toBeNull();
        });
        it('should handle validation errors gracefully', () => {
            // Create a value object that bypasses constructor validation
            const vo = Object.create(TestValueObject.prototype);
            vo._value = 'ab'; // Invalid value
            expect(vo.isValid()).toBe(false);
            expect(vo.getValidationError()).toBe('Value must be at least 3 characters long');
        });
    });
    describe('array equality', () => {
        class ArrayValueObject extends base_value_object_1.BaseValueObject {
            getEqualityComponents() {
                return [this._value];
            }
        }
        it('should handle array equality correctly', () => {
            const vo1 = new ArrayValueObject(['a', 'b', 'c']);
            const vo2 = new ArrayValueObject(['a', 'b', 'c']);
            const vo3 = new ArrayValueObject(['a', 'b', 'd']);
            expect(vo1.equals(vo2)).toBe(true);
            expect(vo1.equals(vo3)).toBe(false);
        });
        it('should handle empty arrays', () => {
            const vo1 = new ArrayValueObject([]);
            const vo2 = new ArrayValueObject([]);
            expect(vo1.equals(vo2)).toBe(true);
        });
        it('should handle arrays of different lengths', () => {
            const vo1 = new ArrayValueObject(['a', 'b']);
            const vo2 = new ArrayValueObject(['a', 'b', 'c']);
            expect(vo1.equals(vo2)).toBe(false);
        });
    });
    describe('edge cases', () => {
        it('should handle empty string values', () => {
            class EmptyStringValueObject extends base_value_object_1.BaseValueObject {
                validate() {
                    if (this._value === null || this._value === undefined) {
                        throw new Error('Value cannot be null or undefined');
                    }
                }
            }
            const vo = new EmptyStringValueObject('');
            expect(vo.value).toBe('');
            expect(vo.toString()).toBe('');
        });
        it('should handle numeric values', () => {
            class NumericValueObject extends base_value_object_1.BaseValueObject {
                validate() {
                    super.validate();
                    if (typeof this._value !== 'number' || isNaN(this._value)) {
                        throw new Error('Value must be a valid number');
                    }
                }
            }
            const vo = new NumericValueObject(42);
            expect(vo.value).toBe(42);
            expect(vo.toString()).toBe('42');
        });
        it('should handle boolean values', () => {
            class BooleanValueObject extends base_value_object_1.BaseValueObject {
                validate() {
                    super.validate();
                    if (typeof this._value !== 'boolean') {
                        throw new Error('Value must be a boolean');
                    }
                }
            }
            const vo = new BooleanValueObject(true);
            expect(vo.value).toBe(true);
            expect(vo.toString()).toBe('true');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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