{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\auth\\strategies\\oauth-google.strategy.ts", "mappings": ";;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,+CAAoD;AACpD,qEAAmE;AACnE,2CAA+C;AAC/C,kDAA8C;AAE9C;;;GAGG;AAEI,IAAM,mBAAmB,2BAAzB,MAAM,mBAAoB,SAAQ,IAAA,2BAAgB,EAAC,kCAAQ,EAAE,QAAQ,CAAC;IAG3E,YACmB,aAA4B,EAC5B,WAAwB;QAEzC,MAAM,UAAU,GAAG,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAE7C,KAAK,CAAC;YACJ,QAAQ,EAAE,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ;YAC1C,YAAY,EAAE,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,YAAY;YAClD,WAAW,EAAE,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW;YAChD,KAAK,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC;YAC3B,iBAAiB,EAAE,IAAI;SACxB,CAAC,CAAC;QAXc,kBAAa,GAAb,aAAa,CAAe;QAC5B,gBAAW,GAAX,WAAW,CAAa;QAJ1B,WAAM,GAAG,IAAI,eAAM,CAAC,qBAAmB,CAAC,IAAI,CAAC,CAAC;IAe/D,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,QAAQ,CACZ,OAAY,EACZ,WAAmB,EACnB,YAAoB,EACpB,OAAY,EACZ,IAAoB;QAEpB,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;gBAChD,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK;gBACjC,SAAS,EAAE,OAAO,CAAC,EAAE;aACtB,CAAC,CAAC;YAEH,+CAA+C;YAC/C,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;YACzC,MAAM,SAAS,GAAG,OAAO,CAAC,IAAI,EAAE,SAAS,IAAI,EAAE,CAAC;YAChD,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,EAAE,UAAU,IAAI,EAAE,CAAC;YAChD,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;YAE7C,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oCAAoC,EAAE;oBACrD,SAAS,EAAE,OAAO,CAAC,EAAE;iBACtB,CAAC,CAAC;gBACH,OAAO,IAAI,CAAC,IAAI,KAAK,CAAC,8BAA8B,CAAC,EAAE,IAAI,CAAC,CAAC;YAC/D,CAAC;YAED,sBAAsB;YACtB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC;gBACxD,KAAK;gBACL,SAAS;gBACT,QAAQ;gBACR,SAAS;gBACT,QAAQ,EAAE,QAAQ;gBAClB,UAAU,EAAE,OAAO,CAAC,EAAE;gBACtB,YAAY,EAAE;oBACZ,WAAW;oBACX,YAAY;oBACZ,OAAO,EAAE;wBACP,EAAE,EAAE,OAAO,CAAC,EAAE;wBACd,WAAW,EAAE,OAAO,CAAC,WAAW;wBAChC,MAAM,EAAE,OAAO,CAAC,MAAM;wBACtB,MAAM,EAAE,OAAO,CAAC,MAAM;qBACvB;iBACF;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE;oBACtD,KAAK;oBACL,QAAQ,EAAE,QAAQ;iBACnB,CAAC,CAAC;gBACH,OAAO,IAAI,CAAC,IAAI,KAAK,CAAC,6BAA6B,CAAC,EAAE,IAAI,CAAC,CAAC;YAC9D,CAAC;YAED,kCAAkC;YAClC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACnB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE;oBAC1C,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,aAAa,EAAE,IAAI,CAAC,aAAa;iBAClC,CAAC,CAAC;gBACH,OAAO,IAAI,CAAC,IAAI,KAAK,CAAC,uBAAuB,CAAC,EAAE,IAAI,CAAC,CAAC;YACxD,CAAC;YAED,oCAAoC;YACpC,MAAM,gBAAgB,GAAG;gBACvB,GAAG,IAAI;gBACP,YAAY,EAAE;oBACZ,QAAQ,EAAE,QAAQ;oBAClB,UAAU,EAAE,OAAO,CAAC,EAAE;oBACtB,SAAS,EAAE,OAAO,CAAC,EAAE;oBACrB,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;oBACpC,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,MAAM,EAAE,OAAO;iBAChB;aACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wCAAwC,EAAE;gBACxD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,SAAS,EAAE,OAAO,CAAC,EAAE;aACtB,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;QAEtC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE;gBACrD,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,SAAS,EAAE,OAAO,EAAE,EAAE;gBACtB,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK;gBAClC,SAAS,EAAE,OAAO,CAAC,EAAE;aACtB,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;CACF,CAAA;AA3HY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;yDAKuB,sBAAa,oBAAb,sBAAa,oDACf,0BAAW,oBAAX,0BAAW;GALhC,mBAAmB,CA2H/B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\auth\\strategies\\oauth-google.strategy.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { PassportStrategy } from '@nestjs/passport';\r\nimport { Strategy, VerifyCallback } from 'passport-google-oauth20';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { AuthService } from '../auth.service';\r\n\r\n/**\r\n * Google OAuth 2.0 authentication strategy\r\n * Handles Google OAuth authentication flow\r\n */\r\n@Injectable()\r\nexport class GoogleOAuthStrategy extends PassportStrategy(Strategy, 'google') {\r\n  private readonly logger = new Logger(GoogleOAuthStrategy.name);\r\n\r\n  constructor(\r\n    private readonly configService: ConfigService,\r\n    private readonly authService: AuthService,\r\n  ) {\r\n    const authConfig = configService.get('auth');\r\n    \r\n    super({\r\n      clientID: authConfig.oauth.google.clientId,\r\n      clientSecret: authConfig.oauth.google.clientSecret,\r\n      callbackURL: authConfig.oauth.google.callbackUrl,\r\n      scope: ['email', 'profile'],\r\n      passReqToCallback: true,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Validate Google OAuth user\r\n   * @param request Express request object\r\n   * @param accessToken OAuth access token\r\n   * @param refreshToken OAuth refresh token\r\n   * @param profile Google user profile\r\n   * @param done Passport callback\r\n   */\r\n  async validate(\r\n    request: any,\r\n    accessToken: string,\r\n    refreshToken: string,\r\n    profile: any,\r\n    done: VerifyCallback,\r\n  ): Promise<void> {\r\n    try {\r\n      this.logger.debug('Validating Google OAuth user', {\r\n        profileId: profile.id,\r\n        email: profile.emails?.[0]?.value,\r\n        ipAddress: request.ip,\r\n      });\r\n\r\n      // Extract user information from Google profile\r\n      const email = profile.emails?.[0]?.value;\r\n      const firstName = profile.name?.givenName || '';\r\n      const lastName = profile.name?.familyName || '';\r\n      const avatarUrl = profile.photos?.[0]?.value;\r\n\r\n      if (!email) {\r\n        this.logger.warn('Google OAuth profile missing email', {\r\n          profileId: profile.id,\r\n        });\r\n        return done(new Error('Email not provided by Google'), null);\r\n      }\r\n\r\n      // Find or create user\r\n      const user = await this.authService.findOrCreateOAuthUser({\r\n        email,\r\n        firstName,\r\n        lastName,\r\n        avatarUrl,\r\n        provider: 'google',\r\n        providerId: profile.id,\r\n        providerData: {\r\n          accessToken,\r\n          refreshToken,\r\n          profile: {\r\n            id: profile.id,\r\n            displayName: profile.displayName,\r\n            emails: profile.emails,\r\n            photos: profile.photos,\r\n          },\r\n        },\r\n      });\r\n\r\n      if (!user) {\r\n        this.logger.warn('Failed to create or find OAuth user', {\r\n          email,\r\n          provider: 'google',\r\n        });\r\n        return done(new Error('Failed to authenticate user'), null);\r\n      }\r\n\r\n      // Check if user account is active\r\n      if (!user.canLogin) {\r\n        this.logger.warn('OAuth user cannot login', {\r\n          userId: user.id,\r\n          status: user.status,\r\n          emailVerified: user.emailVerified,\r\n        });\r\n        return done(new Error('Account is not active'), null);\r\n      }\r\n\r\n      // Add OAuth metadata to user object\r\n      const userWithMetadata = {\r\n        ...user,\r\n        authMetadata: {\r\n          provider: 'google',\r\n          providerId: profile.id,\r\n          ipAddress: request.ip,\r\n          userAgent: request.get('User-Agent'),\r\n          timestamp: new Date(),\r\n          method: 'oauth',\r\n        },\r\n      };\r\n\r\n      this.logger.log('Google OAuth authentication successful', {\r\n        userId: user.id,\r\n        email: user.email,\r\n        ipAddress: request.ip,\r\n      });\r\n\r\n      return done(null, userWithMetadata);\r\n\r\n    } catch (error) {\r\n      this.logger.error('Google OAuth authentication error', {\r\n        error: error.message,\r\n        profileId: profile?.id,\r\n        email: profile?.emails?.[0]?.value,\r\n        ipAddress: request.ip,\r\n      });\r\n\r\n      return done(error, null);\r\n    }\r\n  }\r\n}\r\n"], "version": 3}