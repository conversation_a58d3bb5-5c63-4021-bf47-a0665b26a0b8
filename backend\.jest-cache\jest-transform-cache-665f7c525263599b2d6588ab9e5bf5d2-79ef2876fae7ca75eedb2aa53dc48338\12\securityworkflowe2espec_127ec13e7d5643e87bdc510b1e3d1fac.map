{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\__tests__\\e2e\\security-workflow.e2e-spec.ts", "mappings": ";;;;;AAAA,6CAAsD;AAEtD,2CAA8C;AAC9C,0DAAgC;AAChC,iDAA6C;AAE7C,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;IACvC,IAAI,GAAqB,CAAC;IAC1B,IAAI,SAAiB,CAAC;IACtB,IAAI,MAAc,CAAC;IAEnB,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,aAAa,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAClE,OAAO,EAAE;gBACP,qBAAY,CAAC,OAAO,CAAC;oBACnB,QAAQ,EAAE,IAAI;oBACd,WAAW,EAAE,WAAW;iBACzB,CAAC;gBACF,sBAAS;aACV;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,GAAG,GAAG,aAAa,CAAC,qBAAqB,EAAE,CAAC;QAC5C,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC;QAEjB,+CAA+C;QAC/C,MAAM,YAAY,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;aACpD,IAAI,CAAC,uBAAuB,CAAC;aAC7B,IAAI,CAAC;YACJ,KAAK,EAAE,2BAA2B;YAClC,QAAQ,EAAE,oBAAoB;YAC9B,IAAI,EAAE,oBAAoB;SAC3B,CAAC,CAAC;QAEL,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;QACtD,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAC1C,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mCAAmC,EAAE,GAAG,EAAE;QACjD,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,cAAc,GAAG;gBACrB,IAAI,EAAE,yCAAyC;gBAC/C,WAAW,EAAE,6CAA6C;gBAC1D,QAAQ,EAAE,MAAM;aACjB,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC,cAAc,CAAC;iBACpB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,0BAA0B;YAC1B,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACnD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YAC1D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC3D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,oBAAoB,GAAG;gBAC3B,yBAAyB;gBACzB,cAAc;gBACd,UAAU;gBACV,gCAAgC;aACjC,CAAC;YAEF,KAAK,MAAM,SAAS,IAAI,oBAAoB,EAAE,CAAC;gBAC7C,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,oBAAoB,CAAC;qBACzB,KAAK,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;qBAC5B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;qBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,gDAAgD;gBAChD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;YACxD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,GAAG,CAAC,cAAc,EAAE,kBAAkB,CAAC;iBACvC,IAAI,CAAC,mCAAmC,CAAC,CAAC,iBAAiB;iBAC3D,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,YAAY,GAAG;gBACnB,IAAI,EAAE,YAAY;gBAClB,WAAW,EAAE,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,yBAAyB;gBACzD,QAAQ,EAAE,MAAM;gBAChB,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;oBAC5C,GAAG,EAAE,MAAM,CAAC,EAAE;oBACd,KAAK,EAAE,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC;iBACxB,CAAC,CAAC;aACJ,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC,YAAY,CAAC,CAAC;YAEtB,mEAAmE;YACnE,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,6CAA6C;YAC7C,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,qBAAqB,CAAC;iBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,EAAE;gBACzD,QAAQ,EAAE,eAAe;gBACzB,WAAW,EAAE,mBAAmB;aACjC,CAAC,CAAC;YAEL,qCAAqC;YACrC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,aAAa,GAAG;gBACpB,QAAQ;gBACR,UAAU;gBACV,QAAQ;gBACR,QAAQ;gBACR,UAAU,EAAE,uCAAuC;gBACnD,aAAa,EAAE,0CAA0C;aAC1D,CAAC;YAEF,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE,CAAC;gBACzC,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,IAAI,CAAC,uBAAuB,CAAC;qBAC7B,IAAI,CAAC;oBACJ,KAAK,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,cAAc;oBACvC,QAAQ,EAAE,YAAY;oBACtB,IAAI,EAAE,oBAAoB;iBAC3B,CAAC;qBACD,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YACtD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACrE,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CACpD,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACzB,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC;gBACJ,KAAK,EAAE,2BAA2B;gBAClC,QAAQ,EAAE,eAAe;aAC1B,CAAC,CACL,CAAC;YAEF,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CACjC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CACtB,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;iBACtC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,QAAQ,EAAE,MAAM,IAAI,GAAG,EAAE,CAAC,CAAC,CAC3D,CACF,CAAC;YAEF,uCAAuC;YACvC,MAAM,gBAAgB,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC;YACxE,MAAM,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,uBAAuB;YACvB,MAAM,aAAa,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACrD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC;gBACJ,KAAK,EAAE,2BAA2B;gBAClC,QAAQ,EAAE,oBAAoB;aAC/B,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,QAAQ,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;YAE5D,kBAAkB;YAClB,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,8BAA8B,CAAC;iBACnC,GAAG,CAAC,eAAe,EAAE,UAAU,QAAQ,EAAE,CAAC;iBAC1C,IAAI,CAAC;gBACJ,eAAe,EAAE,oBAAoB;gBACrC,WAAW,EAAE,uBAAuB;aACrC,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,8BAA8B;YAC9B,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,QAAQ,EAAE,CAAC;iBAC1C,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,yBAAyB;YACzB,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC;gBACJ,KAAK,EAAE,2BAA2B;gBAClC,QAAQ,EAAE,uBAAuB;aAClC,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,6BAA6B;YAC7B,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC;gBACJ,KAAK,EAAE,2BAA2B;gBAClC,QAAQ,EAAE,uBAAuB;aAClC,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,6CAA6C;YAC7C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,IAAI,CACpD,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CACtC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,iDAAiD;YACjD,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,qBAAqB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,MAAM,CAAC,6BAA6B,CAAC;iBACrC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,oDAAoD;YACpD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC;gBACJ,IAAI,EAAE,cAAc;gBACpB,KAAK,EAAE,CAAC,OAAO,CAAC,EAAE,oCAAoC;aACvD,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,8BAA8B;YAC9B,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,qCAAqC;YACrC,MAAM,cAAc,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACtD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC;gBACJ,IAAI,EAAE,WAAW;gBACjB,WAAW,EAAE,oBAAoB;gBACjC,QAAQ,EAAE,MAAM;aACjB,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,MAAM,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAE3C,sBAAsB;YACtB,MAAM,iBAAiB,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACzD,IAAI,CAAC,uBAAuB,CAAC;iBAC7B,IAAI,CAAC;gBACJ,KAAK,EAAE,wBAAwB;gBAC/B,QAAQ,EAAE,mBAAmB;gBAC7B,IAAI,EAAE,YAAY;aACnB,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,cAAc,GAAG,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;YAEtE,mDAAmD;YACnD,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,sBAAsB,MAAM,EAAE,CAAC;iBACnC,GAAG,CAAC,eAAe,EAAE,UAAU,cAAc,EAAE,CAAC;iBAChD,IAAI,CAAC;gBACJ,IAAI,EAAE,wBAAwB;aAC/B,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,uCAAuC;YACvC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAC/D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAEnE,wCAAwC;YACxC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAC1D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,mDAAmD;YACnD,uDAAuD;YACvD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,gBAAgB,CAAC;iBACrB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,uDAAuD;YACvD,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,MAAM,OAAO,GAAG;gBACd,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,sBAAsB;gBAC7B,KAAK,EAAE,iBAAiB;gBACxB,GAAG,EAAE,aAAa,EAAE,+BAA+B;aACpD,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC,OAAO,CAAC,CAAC;YAEjB,sCAAsC;YACtC,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;gBAC5B,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAC5D,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,gBAAgB,CAAC;iBACrB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,oCAAoC;YACpC,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;YAEjC,qEAAqE;YACrE,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAE9B,uCAAuC;YACvC,mDAAmD;YACnD,6DAA6D;YAC7D,6DAA6D;YAC7D,8DAA8D;YAC9D,4DAA4D;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;YAC3C,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,OAAO,CAAC,gBAAgB,CAAC;iBACzB,GAAG,CAAC,QAAQ,EAAE,4BAA4B,CAAC;iBAC3C,GAAG,CAAC,+BAA+B,EAAE,KAAK,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,gBAAgB,CAAC;iBACrB,GAAG,CAAC,QAAQ,EAAE,8BAA8B,CAAC,CAAC;YAEjD,2DAA2D;YAC3D,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,mBAAmB;YACnB,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,8BAA8B,CAAC;iBACnC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,qCAAqC;YACrC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAClD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAClC,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC,GAAG,CAAC,qBAAqB,CAAC;gBACvD,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC;gBACxD,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC,GAAG,CAAC,qBAAqB,CAAC;gBACvD,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC,MAAM,CAAC,qBAAqB,CAAC;aAC3D,CAAC,CAAC;YAEH,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAC3B,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;gBACnD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;gBAChD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,6CAA6C;YAC7C,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,gBAAgB,CAAC;iBACrB,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,qBAAqB;YAEtC,mCAAmC;YACnC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;YAC1C,uBAAuB;YACvB,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC;gBACJ,KAAK,EAAE,2BAA2B;gBAClC,QAAQ,EAAE,eAAe;aAC1B,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,iEAAiE;YACjE,uDAAuD;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,mCAAmC;YACnC,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,qBAAqB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,iEAAiE;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,sBAAsB;YACtB,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC;gBACJ,KAAK,EAAE,2BAA2B;gBAClC,QAAQ,EAAE,uBAAuB;aAClC,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,2EAA2E;QAC7E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,yDAAyD;YACzD,MAAM,MAAM,GAAG,EAAE,CAAC;YAElB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3B,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,IAAI,CAAC,oBAAoB,CAAC;qBAC1B,IAAI,CAAC;oBACJ,KAAK,EAAE,2BAA2B;oBAClC,QAAQ,EAAE,uBAAuB;iBAClC,CAAC;qBACD,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YACrD,CAAC;YAED,8BAA8B;YAC9B,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC;YACrC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,4CAA4C;YAC5C,MAAM,aAAa,GAAG;gBACpB,IAAI,EAAE,gBAAgB;gBACtB,WAAW,EAAE,qCAAqC;gBAClD,QAAQ,EAAE,WAAW;gBACrB,cAAc,EAAE,iBAAiB;aAClC,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,2BAA2B,CAAC;iBACjC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC,aAAa,CAAC;iBACnB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,wDAAwD;YACxD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,yBAAyB;YACzB,MAAM,aAAa,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACrD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC;gBACJ,KAAK,EAAE,2BAA2B;gBAClC,QAAQ,EAAE,uBAAuB;aAClC,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,YAAY,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;YAEhE,SAAS;YACT,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,IAAI,CAAC,qBAAqB,CAAC;iBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,YAAY,EAAE,CAAC;iBAC9C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,4BAA4B;YAC5B,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,YAAY,EAAE,CAAC;iBAC9C,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,2BAA2B;YAC3B,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACjC,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBACzB,IAAI,CAAC,oBAAoB,CAAC;qBAC1B,IAAI,CAAC;oBACJ,KAAK,EAAE,2BAA2B;oBAClC,QAAQ,EAAE,uBAAuB;iBAClC,CAAC;gBACJ,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBACzB,IAAI,CAAC,oBAAoB,CAAC;qBAC1B,IAAI,CAAC;oBACJ,KAAK,EAAE,2BAA2B;oBAClC,QAAQ,EAAE,uBAAuB;iBAClC,CAAC;aACL,CAAC,CAAC;YAEH,gCAAgC;YAChC,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;gBAC/B,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAEjC,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAC/B,GAAG,CAAC,sBAAsB,CAAC;qBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;qBACtE,MAAM,CAAC,GAAG,CAAC,CAAC;YACjB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,gEAAgE;YAChE,8DAA8D;YAE9D,MAAM,aAAa,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACrD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC;gBACJ,KAAK,EAAE,2BAA2B;gBAClC,QAAQ,EAAE,uBAAuB;aAClC,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;YAEzD,kCAAkC;YAClC,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;iBACvC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,6DAA6D;QAC/D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,+BAA+B;YAC/B,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,gBAAgB,CAAC,CAAC,kCAAkC;iBACxD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,iBAAiB,GAAG;gBACxB,qBAAqB;gBACrB,UAAU,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC9B,4BAA4B;gBAC5B,kCAAkC;gBAClC,aAAa;gBACb,uBAAuB;aACxB,CAAC;YAEF,KAAK,MAAM,GAAG,IAAI,iBAAiB,EAAE,CAAC;gBACpC,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,GAAG,CAAC;qBACR,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC,CAAC;gBAE/C,4CAA4C;gBAC5C,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACrD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,oBAAoB,CAAC;iBACzB,KAAK,CAAC,6BAA6B,CAAC,CAAC,4BAA4B;iBACjE,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,+CAA+C;YAC/C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,qBAAqB;QACxF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\__tests__\\e2e\\security-workflow.e2e-spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { INestApplication } from '@nestjs/common';\r\nimport { ConfigModule } from '@nestjs/config';\r\nimport request from 'supertest';\r\nimport { AppModule } from '../../app.module';\r\n\r\ndescribe('Security Workflow (e2e)', () => {\r\n  let app: INestApplication;\r\n  let authToken: string;\r\n  let userId: string;\r\n\r\n  beforeAll(async () => {\r\n    const moduleFixture: TestingModule = await Test.createTestingModule({\r\n      imports: [\r\n        ConfigModule.forRoot({\r\n          isGlobal: true,\r\n          envFilePath: '.env.test',\r\n        }),\r\n        AppModule,\r\n      ],\r\n    }).compile();\r\n\r\n    app = moduleFixture.createNestApplication();\r\n    await app.init();\r\n\r\n    // Setup authentication for protected endpoints\r\n    const authResponse = await request(app.getHttpServer())\r\n      .post('/api/v1/auth/register')\r\n      .send({\r\n        email: '<EMAIL>',\r\n        password: 'SecurePassword123!',\r\n        name: 'Security Test User',\r\n      });\r\n\r\n    authToken = authResponse.body.data.tokens.accessToken;\r\n    userId = authResponse.body.data.user.id;\r\n  });\r\n\r\n  afterAll(async () => {\r\n    await app.close();\r\n  });\r\n\r\n  describe('Input Validation and Sanitization', () => {\r\n    it('should sanitize XSS attempts in input', async () => {\r\n      const maliciousInput = {\r\n        name: '<script>alert(\"xss\")</script>Clean Name',\r\n        description: '<img src=\"x\" onerror=\"alert(1)\">Description',\r\n        category: 'test',\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/test/items')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send(maliciousInput)\r\n        .expect(201);\r\n\r\n      // XSS should be sanitized\r\n      expect(response.body.data.name).toBe('Clean Name');\r\n      expect(response.body.data.name).not.toContain('<script>');\r\n      expect(response.body.data.description).toBe('Description');\r\n      expect(response.body.data.description).not.toContain('<img');\r\n    });\r\n\r\n    it('should prevent SQL injection attempts', async () => {\r\n      const sqlInjectionAttempts = [\r\n        \"'; DROP TABLE users; --\",\r\n        \"1' OR '1'='1\",\r\n        \"admin'--\",\r\n        \"' UNION SELECT * FROM users --\",\r\n      ];\r\n\r\n      for (const injection of sqlInjectionAttempts) {\r\n        const response = await request(app.getHttpServer())\r\n          .get('/api/v1/test/items')\r\n          .query({ search: injection })\r\n          .set('Authorization', `Bearer ${authToken}`)\r\n          .expect(200);\r\n\r\n        // Should return normal results, not execute SQL\r\n        expect(response.body.success).toBe(true);\r\n        expect(response.body.data).toEqual(expect.any(Array));\r\n      }\r\n    });\r\n\r\n    it('should validate and reject malformed JSON', async () => {\r\n      await request(app.getHttpServer())\r\n        .post('/api/v1/test/items')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .set('Content-Type', 'application/json')\r\n        .send('{\"name\": \"test\", \"invalid\": json}') // Malformed JSON\r\n        .expect(400);\r\n    });\r\n\r\n    it('should enforce maximum request size limits', async () => {\r\n      const largePayload = {\r\n        name: 'Large Item',\r\n        description: 'A'.repeat(10000), // Very large description\r\n        category: 'test',\r\n        data: Array.from({ length: 1000 }, (_, i) => ({\r\n          key: `key${i}`,\r\n          value: 'x'.repeat(1000),\r\n        })),\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/test/items')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send(largePayload);\r\n\r\n      // Should either accept (if within limits) or reject (if too large)\r\n      expect([201, 413]).toContain(response.status);\r\n    });\r\n\r\n    it('should validate file upload security', async () => {\r\n      // Test file upload with malicious file types\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/test/upload')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .attach('file', Buffer.from('<?php echo \"malicious\"; ?>'), {\r\n          filename: 'malicious.php',\r\n          contentType: 'application/x-php',\r\n        });\r\n\r\n      // Should reject dangerous file types\r\n      expect([400, 415]).toContain(response.status);\r\n    });\r\n  });\r\n\r\n  describe('Authentication Security', () => {\r\n    it('should enforce strong password requirements', async () => {\r\n      const weakPasswords = [\r\n        '123456',\r\n        'password',\r\n        'qwerty',\r\n        'abc123',\r\n        'Password', // Missing special character and number\r\n        'password123', // Missing uppercase and special character\r\n      ];\r\n\r\n      for (const weakPassword of weakPasswords) {\r\n        const response = await request(app.getHttpServer())\r\n          .post('/api/v1/auth/register')\r\n          .send({\r\n            email: `weak-${Date.now()}@example.com`,\r\n            password: weakPassword,\r\n            name: 'Weak Password User',\r\n          })\r\n          .expect(400);\r\n\r\n        expect(response.body.message).toContain('password');\r\n      }\r\n    });\r\n\r\n    it('should prevent brute force attacks with rate limiting', async () => {\r\n      const loginAttempts = Array.from({ length: 10 }, () =>\r\n        request(app.getHttpServer())\r\n          .post('/api/v1/auth/login')\r\n          .send({\r\n            email: '<EMAIL>',\r\n            password: 'WrongPassword',\r\n          })\r\n      );\r\n\r\n      const responses = await Promise.all(\r\n        loginAttempts.map(req => \r\n          req.then(res => ({ status: res.status }))\r\n            .catch(err => ({ status: err.response?.status || 500 }))\r\n        )\r\n      );\r\n\r\n      // Some requests should be rate limited\r\n      const rateLimitedCount = responses.filter(r => r.status === 429).length;\r\n      expect(rateLimitedCount).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should invalidate tokens on password change', async () => {\r\n      // Login to get a token\r\n      const loginResponse = await request(app.getHttpServer())\r\n        .post('/api/v1/auth/login')\r\n        .send({\r\n          email: '<EMAIL>',\r\n          password: 'SecurePassword123!',\r\n        })\r\n        .expect(200);\r\n\r\n      const oldToken = loginResponse.body.data.tokens.accessToken;\r\n\r\n      // Change password\r\n      await request(app.getHttpServer())\r\n        .put('/api/v1/auth/change-password')\r\n        .set('Authorization', `Bearer ${oldToken}`)\r\n        .send({\r\n          currentPassword: 'SecurePassword123!',\r\n          newPassword: 'NewSecurePassword456!',\r\n        })\r\n        .expect(200);\r\n\r\n      // Old token should be invalid\r\n      await request(app.getHttpServer())\r\n        .get('/api/v1/auth/profile')\r\n        .set('Authorization', `Bearer ${oldToken}`)\r\n        .expect(401);\r\n    });\r\n\r\n    it('should prevent session fixation attacks', async () => {\r\n      // Login with one session\r\n      const session1 = await request(app.getHttpServer())\r\n        .post('/api/v1/auth/login')\r\n        .send({\r\n          email: '<EMAIL>',\r\n          password: 'NewSecurePassword456!',\r\n        })\r\n        .expect(200);\r\n\r\n      // Login with another session\r\n      const session2 = await request(app.getHttpServer())\r\n        .post('/api/v1/auth/login')\r\n        .send({\r\n          email: '<EMAIL>',\r\n          password: 'NewSecurePassword456!',\r\n        })\r\n        .expect(200);\r\n\r\n      // Both sessions should have different tokens\r\n      expect(session1.body.data.tokens.accessToken).not.toBe(\r\n        session2.body.data.tokens.accessToken\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('Authorization Security', () => {\r\n    it('should enforce role-based access control', async () => {\r\n      // Regular user should not access admin endpoints\r\n      await request(app.getHttpServer())\r\n        .get('/api/v1/admin/users')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .expect(403);\r\n\r\n      await request(app.getHttpServer())\r\n        .delete('/api/v1/admin/users/some-id')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .expect(403);\r\n    });\r\n\r\n    it('should prevent privilege escalation', async () => {\r\n      // User should not be able to modify their own roles\r\n      const response = await request(app.getHttpServer())\r\n        .put('/api/v1/auth/profile')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send({\r\n          name: 'Updated Name',\r\n          roles: ['admin'], // Attempting to escalate privileges\r\n        })\r\n        .expect(200);\r\n\r\n      // Roles should not be updated\r\n      expect(response.body.data.user.roles).toEqual(['user']);\r\n    });\r\n\r\n    it('should validate resource ownership', async () => {\r\n      // Create an item as the current user\r\n      const createResponse = await request(app.getHttpServer())\r\n        .post('/api/v1/test/items')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send({\r\n          name: 'User Item',\r\n          description: 'Item owned by user',\r\n          category: 'test',\r\n        })\r\n        .expect(201);\r\n\r\n      const itemId = createResponse.body.data.id;\r\n\r\n      // Create another user\r\n      const otherUserResponse = await request(app.getHttpServer())\r\n        .post('/api/v1/auth/register')\r\n        .send({\r\n          email: '<EMAIL>',\r\n          password: 'OtherPassword123!',\r\n          name: 'Other User',\r\n        })\r\n        .expect(201);\r\n\r\n      const otherUserToken = otherUserResponse.body.data.tokens.accessToken;\r\n\r\n      // Other user should not be able to modify the item\r\n      await request(app.getHttpServer())\r\n        .put(`/api/v1/test/items/${itemId}`)\r\n        .set('Authorization', `Bearer ${otherUserToken}`)\r\n        .send({\r\n          name: 'Modified by other user',\r\n        })\r\n        .expect(403);\r\n    });\r\n  });\r\n\r\n  describe('Data Protection', () => {\r\n    it('should not expose sensitive data in responses', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/auth/profile')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .expect(200);\r\n\r\n      // Password should never be in response\r\n      expect(response.body.data.user).not.toHaveProperty('password');\r\n      expect(response.body.data.user).not.toHaveProperty('passwordHash');\r\n      \r\n      // Internal fields should not be exposed\r\n      expect(response.body.data.user).not.toHaveProperty('__v');\r\n      expect(response.body.data.user).not.toHaveProperty('_id');\r\n    });\r\n\r\n    it('should encrypt sensitive data in transit', async () => {\r\n      // All API responses should use HTTPS in production\r\n      // This test verifies that security headers are present\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/health')\r\n        .expect(200);\r\n\r\n      // In a real implementation, check for security headers\r\n      expect(response.headers).toBeDefined();\r\n    });\r\n\r\n    it('should handle PII data appropriately', async () => {\r\n      const piiData = {\r\n        name: 'John Doe',\r\n        email: '<EMAIL>',\r\n        phone: '******-123-4567',\r\n        ssn: '***********', // Should be rejected or masked\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .put('/api/v1/auth/profile')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send(piiData);\r\n\r\n      // Should either reject SSN or mask it\r\n      if (response.status === 200) {\r\n        expect(response.body.data.user).not.toHaveProperty('ssn');\r\n      } else {\r\n        expect(response.status).toBe(400);\r\n      }\r\n    });\r\n  });\r\n\r\n  describe('Security Headers', () => {\r\n    it('should include security headers in all responses', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/health')\r\n        .expect(200);\r\n\r\n      // Check for common security headers\r\n      const headers = response.headers;\r\n      \r\n      // These would be set by security middleware in a real implementation\r\n      expect(headers).toBeDefined();\r\n      \r\n      // In a real test, you would check for:\r\n      // expect(headers['x-frame-options']).toBe('DENY');\r\n      // expect(headers['x-content-type-options']).toBe('nosniff');\r\n      // expect(headers['x-xss-protection']).toBe('1; mode=block');\r\n      // expect(headers['strict-transport-security']).toBeDefined();\r\n      // expect(headers['content-security-policy']).toBeDefined();\r\n    });\r\n\r\n    it('should handle CORS properly', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .options('/api/v1/health')\r\n        .set('Origin', 'https://trusted-domain.com')\r\n        .set('Access-Control-Request-Method', 'GET')\r\n        .expect(200);\r\n\r\n      expect(response.headers['access-control-allow-origin']).toBeDefined();\r\n    });\r\n\r\n    it('should reject requests from untrusted origins', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/health')\r\n        .set('Origin', 'https://malicious-domain.com');\r\n\r\n      // Should either block or allow based on CORS configuration\r\n      expect([200, 403]).toContain(response.status);\r\n    });\r\n  });\r\n\r\n  describe('Error Handling Security', () => {\r\n    it('should not expose stack traces in production', async () => {\r\n      // Trigger an error\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/nonexistent-endpoint')\r\n        .expect(404);\r\n\r\n      // Should not expose internal details\r\n      expect(response.body).not.toHaveProperty('stack');\r\n      expect(response.body.message).not.toContain('at ');\r\n      expect(response.body.message).not.toContain('node_modules');\r\n    });\r\n\r\n    it('should provide consistent error responses', async () => {\r\n      const responses = await Promise.all([\r\n        request(app.getHttpServer()).get('/api/v1/nonexistent'),\r\n        request(app.getHttpServer()).post('/api/v1/nonexistent'),\r\n        request(app.getHttpServer()).put('/api/v1/nonexistent'),\r\n        request(app.getHttpServer()).delete('/api/v1/nonexistent'),\r\n      ]);\r\n\r\n      responses.forEach(response => {\r\n        expect(response.status).toBe(404);\r\n        expect(response.body).toHaveProperty('statusCode');\r\n        expect(response.body).toHaveProperty('message');\r\n        expect(response.body).toHaveProperty('timestamp');\r\n      });\r\n    });\r\n\r\n    it('should handle timeout errors gracefully', async () => {\r\n      // This would require mocking slow operations\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/health')\r\n        .timeout(100); // Very short timeout\r\n\r\n      // Should handle timeout gracefully\r\n      expect([200, 408, 503]).toContain(response.status);\r\n    });\r\n  });\r\n\r\n  describe('Audit and Logging Security', () => {\r\n    it('should log security events', async () => {\r\n      // Failed login attempt\r\n      await request(app.getHttpServer())\r\n        .post('/api/v1/auth/login')\r\n        .send({\r\n          email: '<EMAIL>',\r\n          password: 'WrongPassword',\r\n        })\r\n        .expect(401);\r\n\r\n      // In a real implementation, you would verify that this is logged\r\n      // This test serves as documentation of the requirement\r\n    });\r\n\r\n    it('should log privilege escalation attempts', async () => {\r\n      // Attempt to access admin endpoint\r\n      await request(app.getHttpServer())\r\n        .get('/api/v1/admin/users')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .expect(403);\r\n\r\n      // In a real implementation, you would verify that this is logged\r\n    });\r\n\r\n    it('should not log sensitive data', async () => {\r\n      // Login with password\r\n      await request(app.getHttpServer())\r\n        .post('/api/v1/auth/login')\r\n        .send({\r\n          email: '<EMAIL>',\r\n          password: 'NewSecurePassword456!',\r\n        })\r\n        .expect(200);\r\n\r\n      // In a real implementation, you would verify that passwords are not logged\r\n    });\r\n  });\r\n\r\n  describe('Cryptographic Security', () => {\r\n    it('should use secure random values', async () => {\r\n      // Generate multiple tokens and verify they are different\r\n      const tokens = [];\r\n      \r\n      for (let i = 0; i < 5; i++) {\r\n        const response = await request(app.getHttpServer())\r\n          .post('/api/v1/auth/login')\r\n          .send({\r\n            email: '<EMAIL>',\r\n            password: 'NewSecurePassword456!',\r\n          })\r\n          .expect(200);\r\n        \r\n        tokens.push(response.body.data.tokens.accessToken);\r\n      }\r\n\r\n      // All tokens should be unique\r\n      const uniqueTokens = new Set(tokens);\r\n      expect(uniqueTokens.size).toBe(tokens.length);\r\n    });\r\n\r\n    it('should handle encryption/decryption securely', async () => {\r\n      // Test encrypted data storage and retrieval\r\n      const sensitiveData = {\r\n        name: 'Sensitive Item',\r\n        description: 'This contains sensitive information',\r\n        category: 'sensitive',\r\n        encryptedField: 'secret-data-123',\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/test/secure-items')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send(sensitiveData)\r\n        .expect(201);\r\n\r\n      // Encrypted fields should not be returned in plain text\r\n      expect(response.body.data.encryptedField).not.toBe(sensitiveData.encryptedField);\r\n    });\r\n  });\r\n\r\n  describe('Session Security', () => {\r\n    it('should invalidate sessions on logout', async () => {\r\n      // Login to get a session\r\n      const loginResponse = await request(app.getHttpServer())\r\n        .post('/api/v1/auth/login')\r\n        .send({\r\n          email: '<EMAIL>',\r\n          password: 'NewSecurePassword456!',\r\n        })\r\n        .expect(200);\r\n\r\n      const sessionToken = loginResponse.body.data.tokens.accessToken;\r\n\r\n      // Logout\r\n      await request(app.getHttpServer())\r\n        .post('/api/v1/auth/logout')\r\n        .set('Authorization', `Bearer ${sessionToken}`)\r\n        .expect(200);\r\n\r\n      // Session should be invalid\r\n      await request(app.getHttpServer())\r\n        .get('/api/v1/auth/profile')\r\n        .set('Authorization', `Bearer ${sessionToken}`)\r\n        .expect(401);\r\n    });\r\n\r\n    it('should handle concurrent sessions securely', async () => {\r\n      // Create multiple sessions\r\n      const sessions = await Promise.all([\r\n        request(app.getHttpServer())\r\n          .post('/api/v1/auth/login')\r\n          .send({\r\n            email: '<EMAIL>',\r\n            password: 'NewSecurePassword456!',\r\n          }),\r\n        request(app.getHttpServer())\r\n          .post('/api/v1/auth/login')\r\n          .send({\r\n            email: '<EMAIL>',\r\n            password: 'NewSecurePassword456!',\r\n          }),\r\n      ]);\r\n\r\n      // Both sessions should be valid\r\n      for (const session of sessions) {\r\n        expect(session.status).toBe(200);\r\n        \r\n        await request(app.getHttpServer())\r\n          .get('/api/v1/auth/profile')\r\n          .set('Authorization', `Bearer ${session.body.data.tokens.accessToken}`)\r\n          .expect(200);\r\n      }\r\n    });\r\n\r\n    it('should enforce session timeout', async () => {\r\n      // This would require mocking time or waiting for actual timeout\r\n      // In a real implementation, you would test session expiration\r\n      \r\n      const loginResponse = await request(app.getHttpServer())\r\n        .post('/api/v1/auth/login')\r\n        .send({\r\n          email: '<EMAIL>',\r\n          password: 'NewSecurePassword456!',\r\n        })\r\n        .expect(200);\r\n\r\n      const token = loginResponse.body.data.tokens.accessToken;\r\n      \r\n      // Verify token is currently valid\r\n      await request(app.getHttpServer())\r\n        .get('/api/v1/auth/profile')\r\n        .set('Authorization', `Bearer ${token}`)\r\n        .expect(200);\r\n\r\n      // In a real test, you would wait for expiration or mock time\r\n    });\r\n  });\r\n\r\n  describe('API Security', () => {\r\n    it('should validate API version security', async () => {\r\n      // Test deprecated API versions\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v0/health') // Hypothetical deprecated version\r\n        .expect(404);\r\n\r\n      expect(response.body.message).toContain('not found');\r\n    });\r\n\r\n    it('should handle malformed requests securely', async () => {\r\n      const malformedRequests = [\r\n        // Extremely long URL\r\n        '/api/v1/' + 'a'.repeat(10000),\r\n        // Special characters in URL\r\n        '/api/v1/test/../../../etc/passwd',\r\n        // Null bytes\r\n        '/api/v1/test\\x00items',\r\n      ];\r\n\r\n      for (const url of malformedRequests) {\r\n        const response = await request(app.getHttpServer())\r\n          .get(url)\r\n          .set('Authorization', `Bearer ${authToken}`);\r\n\r\n        // Should handle gracefully without crashing\r\n        expect([400, 404, 500]).toContain(response.status);\r\n      }\r\n    });\r\n\r\n    it('should prevent parameter pollution', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/test/items')\r\n        .query('limit=10&limit=1000&limit=5') // Multiple limit parameters\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .expect(200);\r\n\r\n      // Should handle parameter pollution gracefully\r\n      expect(response.body.pagination.limit).toBeLessThanOrEqual(100); // Assuming max limit\r\n    });\r\n  });\r\n});"], "version": 3}