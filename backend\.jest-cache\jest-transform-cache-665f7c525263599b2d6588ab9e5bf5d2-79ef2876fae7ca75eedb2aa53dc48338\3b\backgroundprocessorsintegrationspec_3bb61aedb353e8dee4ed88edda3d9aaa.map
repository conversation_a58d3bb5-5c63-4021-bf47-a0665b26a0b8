{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\testing\\integration\\background-processors.integration.spec.ts", "mappings": ";;AAEA,yDAAsD;AACtD,uCAA6C;AAE7C,6DAAwD;AACxD,2GAAsG;AACtG,0EAAsE;AACtE,8GAAwG;AACxG,sHAAiH;AACjH,gHAA2G;AAC3G,oHAA+G;AAC/G,qGAAgG;AAChG,oGAAyF;AACzF,0GAA8F;AAC9F,2EAAiE;AAEjE;;;;;;;;;;;;GAYG;AACH,QAAQ,CAAC,yCAAyC,EAAE,GAAG,EAAE;IACvD,IAAI,GAAqB,CAAC;IAC1B,IAAI,aAA4B,CAAC;IACjC,IAAI,eAAuC,CAAC;IAC5C,IAAI,iBAA6C,CAAC;IAClD,IAAI,uBAAgD,CAAC;IACrD,IAAI,yBAAoD,CAAC;IACzD,IAAI,cAAwC,CAAC;IAC7C,IAAI,YAA2B,CAAC;IAChC,IAAI,aAAoB,CAAC;IACzB,IAAI,UAAiB,CAAC;IACtB,IAAI,UAAiB,CAAC;IACtB,IAAI,QAAuB,CAAC;IAE5B,gBAAgB;IAChB,MAAM,YAAY,GAAG,CAAC,mCAAY,EAAE,wCAAc,EAAE,sBAAM,CAAC,CAAC;IAE5D,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,4BAA4B;QAC5B,QAAQ,GAAG,IAAI,CAAC,KAAM,SAAQ,+BAAa;SAAG,CAAC,EAAE,CAAC;QAElD,0CAA0C;QAC1C,MAAM,QAAQ,CAAC,oBAAoB,CACjC;YACE,OAAO,EAAE;gBACP,yDAA0B;gBAC1B,oCAAgB;aACjB;YACD,SAAS,EAAE;gBACT,uBAAuB;gBACvB;oBACE,OAAO,EAAE,IAAA,oBAAa,EAAC,oBAAoB,CAAC;oBAC5C,QAAQ,EAAE,eAAe,CAAC,oBAAoB,CAAC;iBAChD;gBACD;oBACE,OAAO,EAAE,IAAA,oBAAa,EAAC,iBAAiB,CAAC;oBACzC,QAAQ,EAAE,eAAe,CAAC,iBAAiB,CAAC;iBAC7C;gBACD;oBACE,OAAO,EAAE,IAAA,oBAAa,EAAC,mBAAmB,CAAC;oBAC3C,QAAQ,EAAE,eAAe,CAAC,mBAAmB,CAAC;iBAC/C;aACF;SACF,EACD,YAAY,CACb,CAAC;QAEF,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC;QACnB,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC;QAEvC,wBAAwB;QACxB,eAAe,GAAG,QAAQ,CAAC,UAAU,CAAC,kDAAsB,CAAC,CAAC;QAC9D,iBAAiB,GAAG,QAAQ,CAAC,UAAU,CAAC,yDAA0B,CAAC,CAAC;QACpE,uBAAuB,GAAG,QAAQ,CAAC,UAAU,CAAC,mDAAuB,CAAC,CAAC;QACvE,yBAAyB,GAAG,QAAQ,CAAC,UAAU,CAAC,uDAAyB,CAAC,CAAC;QAC3E,cAAc,GAAG,QAAQ,CAAC,UAAU,CAAC,qDAAwB,CAAC,CAAC;QAC/D,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC,6BAAa,CAAC,CAAC;QAClD,aAAa,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAA,oBAAa,EAAC,oBAAoB,CAAC,CAAC,CAAC;QACzE,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAA,oBAAa,EAAC,iBAAiB,CAAC,CAAC,CAAC;QACnE,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAA,oBAAa,EAAC,mBAAmB,CAAC,CAAC,CAAC;IACvE,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,QAAQ,CAAC,sBAAsB,EAAE,CAAC;IAC1C,CAAC,CAAC,CAAC;IAEH,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,QAAQ,CAAC,aAAa,EAAE,CAAC;QAC/B,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,QAAQ,CAAC,eAAe,EAAE,CAAC;IACnC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,UAAU;YACV,MAAM,eAAe,GAAG;gBACtB,UAAU,EAAE,eAAe;gBAC3B,UAAU,EAAE,cAAc;gBAC1B,SAAS,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE;aAC9B,CAAC;YAEF,MAAM,YAAY,GAAG;gBACnB,MAAM,EAAE,UAAU;gBAClB,aAAa,EAAE,CAAC,gBAAgB,CAAC;gBACjC,iBAAiB,EAAE;oBACjB,QAAQ,EAAE,MAAe;oBACzB,SAAS,EAAE,CAAC,KAAK,CAAC;oBAClB,UAAU,EAAE,CAAC,QAAQ,CAAC;oBACtB,SAAS,EAAE,IAAI;oBACf,cAAc,EAAE,EAAE;oBAClB,2BAA2B,EAAE,IAAI;oBACjC,sBAAsB,EAAE,IAAI;oBAC5B,iBAAiB,EAAE,IAAI;iBACxB;gBACD,OAAO,EAAE;oBACP,eAAe,EAAE,EAAE;oBACnB,eAAe,EAAE,EAAE;oBACnB,UAAU,EAAE,EAAE;iBACf;aACF,CAAC;YAEF,MAAM;YACN,MAAM,WAAW,GAAG,MAAM,eAAe,CAAC,MAAM,CAC9C,oBAAoB,EACpB,kBAAkB,EAClB,eAAe,EACf,EAAE,QAAQ,EAAE,EAAE,EAAE,CACjB,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,eAAe,CAAC,MAAM,CAC3C,iBAAiB,EACjB,kBAAkB,EAClB,YAAY,EACZ,EAAE,QAAQ,EAAE,CAAC,EAAE,CAChB,CAAC;YAEF,SAAS;YACT,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACzD,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACrD,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAEtC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACnD,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAClD,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAElC,uCAAuC;YACvC,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAC5C,kBAAkB,EAClB,MAAM,CAAC,gBAAgB,CAAC;gBACtB,cAAc,EAAE,WAAW,CAAC,EAAE;gBAC9B,GAAG,eAAe;aACnB,CAAC,EACF,MAAM,CAAC,gBAAgB,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CAC1C,CAAC;YAEF,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,oBAAoB,CACzC,kBAAkB,EAClB,MAAM,CAAC,gBAAgB,CAAC;gBACtB,cAAc,EAAE,QAAQ,CAAC,EAAE;gBAC3B,GAAG,YAAY;aAChB,CAAC,EACF,MAAM,CAAC,gBAAgB,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CACzC,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,UAAU;YACV,MAAM,SAAS,GAAG;gBAChB,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC;gBACnC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC;gBACrB,SAAS,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC;gBAClD,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC;gBACrB,OAAO,EAAE,EAAE;aACZ,CAAC;YAEF,qBAAqB;YACrB,MAAM,CAAC,MAAM,CAAC,EAAE,aAAa,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACtE,KAAK,CAAC,UAAwB,CAAC,iBAAiB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBACpE,KAAK,CAAC,SAAuB,CAAC,iBAAiB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;gBAClE,KAAK,CAAC,YAA0B,CAAC,iBAAiB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;gBACxE,KAAK,CAAC,SAAuB,CAAC,iBAAiB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;gBAClE,KAAK,CAAC,UAAwB,CAAC,iBAAiB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACvE,CAAC,CAAC,CAAC;YAEH,MAAM;YACN,MAAM,aAAa,GAAG,MAAM,eAAe,CAAC,qBAAqB,EAAE,CAAC;YAEpE,SAAS;YACT,MAAM,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEtC,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAC5B,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;oBAC3B,OAAO,EAAE,CAAC;oBACV,MAAM,EAAE,CAAC;oBACT,SAAS,EAAE,CAAC;oBACZ,MAAM,EAAE,CAAC;oBACT,OAAO,EAAE,CAAC;oBACV,KAAK,EAAE,CAAC;iBACT,CAAC,CAAC;gBACH,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;gBACnC,MAAM,CAAC,CAAC,oBAAoB,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;qBACnE,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,eAAe,CAAC,UAAU,CAAC,oBAAoB,CAAC,CAAC;YACtE,MAAM,OAAO,GAAG,MAAM,eAAe,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;YAExE,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1B,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3B,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE,CAAC;YAC/C,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,gBAAgB,EAAE,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,UAAU;YACV,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,sBAAsB,CAAC;gBACzD,OAAO,EAAE,oBAAoB;gBAC7B,SAAS,EAAE,oBAAoB;gBAC/B,MAAM,EAAE,QAAQ;gBAChB,SAAS,EAAE;oBACT,OAAO,EAAE,CAAC;oBACV,WAAW,EAAE,CAAC;oBACd,eAAe,EAAE,aAAa;oBAC9B,YAAY,EAAE,IAAI;oBAClB,YAAY,EAAE,EAAE;iBACjB;gBACD,KAAK,EAAE;oBACL,OAAO,EAAE,mBAAmB;oBAC5B,SAAS,EAAE,IAAI;oBACf,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC;aACF,CAAC,CAAC;YAEH,MAAM;YACN,MAAM,OAAO,GAAG,MAAM,eAAe,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YAEhE,SAAS;YACT,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE3B,mCAAmC;YACnC,MAAM,UAAU,GAAG,MAAM,QAAQ,CAAC,aAAa,CAAC,mCAAY,CAAC;iBAC1D,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAE/C,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC1C,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;YAEpC,iCAAiC;YACjC,MAAM,QAAQ,CAAC,kBAAkB,CAAC,aAAa,EAAE;gBAC/C,cAAc,EAAE,YAAY,CAAC,EAAE;gBAC/B,OAAO,EAAE,CAAC;aACX,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,UAAU;YACV,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,sBAAsB,CAAC;gBACzD,OAAO,EAAE,oBAAoB;gBAC7B,SAAS,EAAE,oBAAoB;gBAC/B,MAAM,EAAE,QAAQ;gBAChB,SAAS,EAAE;oBACT,OAAO,EAAE,CAAC;oBACV,WAAW,EAAE,CAAC;oBACd,eAAe,EAAE,aAAa;oBAC9B,YAAY,EAAE,IAAI;oBAClB,YAAY,EAAE,EAAE;iBACjB;gBACD,KAAK,EAAE;oBACL,OAAO,EAAE,sBAAsB;oBAC/B,SAAS,EAAE,IAAI;oBACf,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC;aACF,CAAC,CAAC;YAEH,MAAM;YACN,MAAM,OAAO,GAAG,MAAM,eAAe,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YAEhE,SAAS;YACT,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE5B,uCAAuC;YACvC,MAAM,UAAU,GAAG,MAAM,QAAQ,CAAC,aAAa,CAAC,mCAAY,CAAC;iBAC1D,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAE/C,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,UAAU;YACV,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,sBAAsB,CAAC;gBACzD,OAAO,EAAE,oBAAoB;gBAC7B,SAAS,EAAE,oBAAoB;gBAC/B,MAAM,EAAE,QAAQ;gBAChB,SAAS,EAAE;oBACT,OAAO,EAAE,CAAC;oBACV,WAAW,EAAE,CAAC;oBACd,eAAe,EAAE,aAAa;oBAC9B,YAAY,EAAE,IAAI;oBAClB,YAAY,EAAE,EAAE;iBACjB;gBACD,KAAK,EAAE;oBACL,OAAO,EAAE,kBAAkB;oBAC3B,SAAS,EAAE,KAAK;oBAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC;aACF,CAAC,CAAC;YAEH,MAAM;YACN,MAAM,OAAO,GAAG,MAAM,eAAe,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YAEhE,SAAS;YACT,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,UAAU;YACV,MAAM,QAAQ,GAAG,GAAG,CAAC;YACrB,MAAM,OAAO,GAAG;gBACd,UAAU,EAAE,WAAW;gBACvB,UAAU,EAAE,eAAe;gBAC3B,SAAS,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;aAC5B,CAAC;YAEF,MAAM;YACN,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,MAAM,QAAQ,CAAC,oBAAoB,CAAC,KAAK,IAAI,EAAE;gBACrF,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACzD,eAAe,CAAC,MAAM,CACpB,oBAAoB,EACpB,kBAAkB,EAClB,EAAE,GAAG,OAAO,EAAE,UAAU,EAAE,aAAa,CAAC,EAAE,EAAE,EAC5C,EAAE,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,EAAE,CAC7C,CACF,CAAC;gBACF,OAAO,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YACpC,MAAM,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,mCAAmC;YAC7E,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE/D,OAAO,CAAC,GAAG,CAAC,yBAAyB,QAAQ,SAAS,EAAE;gBACtD,SAAS,EAAE,GAAG,aAAa,IAAI;gBAC/B,iBAAiB,EAAE,GAAG,aAAa,GAAG,QAAQ,IAAI;gBAClD,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,GAAG,aAAa,CAAC,GAAG,IAAI,CAAC;aAC7D,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACrE,UAAU;YACV,MAAM,aAAa,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;YAC5C,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,sBAAsB,CAAC;gBACzD,OAAO,EAAE,oBAAoB;gBAC7B,SAAS,EAAE,oBAAoB;gBAC/B,MAAM,EAAE,SAAS;aAClB,CAAC,CAAC;YAEH,MAAM;YACN,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,MAAM,QAAQ,CAAC,oBAAoB,CAAC,KAAK,IAAI,EAAE;gBAC/E,OAAO,MAAM,iBAAiB,CAAC,eAAe,CAAC;oBAC7C,EAAE,EAAE,mBAAmB;oBACvB,IAAI,EAAE,kBAAkB;oBACxB,IAAI,EAAE;wBACJ,cAAc,EAAE,YAAY,CAAC,EAAE;wBAC/B,UAAU,EAAE,eAAe;wBAC3B,UAAU,EAAE,mBAAmB;wBAC/B,SAAS,EAAE,EAAE;wBACb,gBAAgB,EAAE,EAAE,MAAM,EAAE,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE;wBAClD,aAAa,EAAE,EAAE;qBAClB;oBACD,IAAI,EAAE,EAAE;oBACR,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;oBACrB,YAAY,EAAE,CAAC;oBACf,KAAK,EAAE,CAAC;oBACR,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;iBACb,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;YAE1C,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7B,MAAM,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAE1C,MAAM,cAAc,GAAG,WAAW,CAAC,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;YACrE,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,0BAA0B;YAEjF,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE;gBACxC,aAAa,EAAE,GAAG,aAAa,IAAI;gBACnC,cAAc,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI;gBAC/D,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI;gBACpE,SAAS,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI;aACjE,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,UAAU;YACV,MAAM,aAAa,GAAG;gBACpB,QAAQ,CAAC,YAAY,CAAC,WAAW,CAAC;gBAClC,QAAQ,CAAC,YAAY,CAAC,8BAA8B,CAAC;aACtD,CAAC;YAEF,MAAM,OAAO,GAAG;gBACd,UAAU,EAAE,YAAY;gBACxB,UAAU,EAAE,gBAAgB;gBAC5B,SAAS,EAAE,EAAE;aACd,CAAC;YAEF,MAAM;YACN,MAAM,YAAY,GAAG,MAAM,eAAe,CAAC,MAAM,CAC/C,oBAAoB,EACpB,kBAAkB,EAClB,OAAO,CACR,CAAC;YAEF,MAAM,iBAAiB,CAAC,eAAe,CAAC;gBACtC,EAAE,EAAE,gBAAgB;gBACpB,IAAI,EAAE,kBAAkB;gBACxB,IAAI,EAAE;oBACJ,cAAc,EAAE,YAAY,CAAC,EAAE;oBAC/B,GAAG,OAAO;oBACV,gBAAgB,EAAE,EAAE,MAAM,EAAE,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE;oBAClD,aAAa,EAAE,EAAE;iBAClB;gBACD,IAAI,EAAE,EAAE;gBACR,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,YAAY,EAAE,CAAC;gBACf,KAAK,EAAE,CAAC;gBACR,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;aACb,CAAC,CAAC;YAEV,SAAS;YACT,MAAM,CAAC,aAAa,EAAE,sBAAsB,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YAEjF,MAAM,CAAC,aAAa,CAAC,CAAC,aAAa,CAAC;gBAClC,cAAc,EAAE,YAAY,CAAC,EAAE;gBAC/B,SAAS,EAAE,oBAAoB;gBAC/B,OAAO,EAAE,kBAAkB;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,sBAAsB,CAAC,CAAC,aAAa,CAAC;gBAC3C,UAAU,EAAE,YAAY;gBACxB,UAAU,EAAE,gBAAgB;gBAC5B,cAAc,EAAE,YAAY,CAAC,EAAE;aAChC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,UAAU;YACV,MAAM,uBAAuB,GAAG,QAAQ,CAAC,YAAY,CAAC,6BAA6B,CAAC,CAAC;YAErF,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,sBAAsB,CAAC;gBACzD,OAAO,EAAE,mBAAmB;gBAC5B,SAAS,EAAE,mBAAmB;gBAC9B,MAAM,EAAE,SAAS;aAClB,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG;gBACtB,aAAa,EAAE,sBAAsB;gBACrC,MAAM,EAAE;oBACN,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE;oBACjE,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;iBAClE;gBACD,gBAAgB,EAAE;oBAChB,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,0BAA0B,EAAE,OAAO,EAAE,eAAe,EAAE;iBAC5E;gBACD,UAAU,EAAE;oBACV,YAAY,EAAE,MAAM;oBACpB,iBAAiB,EAAE,KAAK;oBACxB,kBAAkB,EAAE,IAAI;iBACzB;gBACD,cAAc,EAAE;oBACd,sBAAsB,EAAE,IAAI;oBAC5B,qBAAqB,EAAE,IAAI;oBAC3B,wBAAwB,EAAE,KAAK;oBAC/B,wBAAwB,EAAE,KAAK;oBAC/B,oBAAoB,EAAE,GAAG;oBACzB,cAAc,EAAE,GAAG;iBACpB;aACF,CAAC;YAEF,MAAM;YACN,MAAM,yBAAyB,CAAC,eAAe,CAAC;gBAC9C,EAAE,EAAE,qBAAqB;gBACzB,IAAI,EAAE,kBAAkB;gBACxB,IAAI,EAAE;oBACJ,cAAc,EAAE,YAAY,CAAC,EAAE;oBAC/B,GAAG,eAAe;iBACnB;gBACD,IAAI,EAAE,EAAE;gBACR,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,YAAY,EAAE,CAAC;gBACf,KAAK,EAAE,CAAC;gBACR,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;aACb,CAAC,CAAC;YAEV,SAAS;YACT,MAAM,gBAAgB,GAAG,MAAM,uBAAuB,CAAC;YACvD,MAAM,CAAC,gBAAgB,CAAC,CAAC,aAAa,CAAC;gBACrC,aAAa,EAAE,sBAAsB;gBACrC,cAAc,EAAE,YAAY,CAAC,EAAE;gBAC/B,eAAe,EAAE,CAAC;aACnB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,UAAU;YACV,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;YACnE,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,sBAAsB,CAAC;gBACzD,OAAO,EAAE,iBAAiB;gBAC1B,SAAS,EAAE,iBAAiB;gBAC5B,MAAM,EAAE,SAAS;aAClB,CAAC,CAAC;YAEH,MAAM;YACN,MAAM,uBAAuB,CAAC,eAAe,CAAC;gBAC5C,EAAE,EAAE,kBAAkB;gBACtB,IAAI,EAAE,kBAAkB;gBACxB,IAAI,EAAE;oBACJ,cAAc,EAAE,YAAY,CAAC,EAAE;oBAC/B,MAAM,EAAE,cAAc;oBACtB,aAAa,EAAE,CAAC,gBAAgB,CAAC;oBACjC,iBAAiB,EAAE;wBACjB,QAAQ,EAAE,OAAO;wBACjB,SAAS,EAAE,CAAC,KAAK,CAAC;wBAClB,UAAU,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC;wBACzB,SAAS,EAAE,IAAI;wBACf,cAAc,EAAE,CAAC;wBACjB,2BAA2B,EAAE,KAAK;wBAClC,sBAAsB,EAAE,IAAI;wBAC5B,iBAAiB,EAAE,KAAK;qBACzB;oBACD,OAAO,EAAE;wBACP,eAAe,EAAE,EAAE;wBACnB,eAAe,EAAE,EAAE;wBACnB,UAAU,EAAE,EAAE;qBACf;iBACF;gBACD,IAAI,EAAE,EAAE;gBACR,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,YAAY,EAAE,CAAC;gBACf,KAAK,EAAE,CAAC;gBACR,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;aACb,CAAC,CAAC;YAEV,SAAS;YACT,MAAM,CAAC,eAAe,CAAC,CAAC,oBAAoB,CAC1C,MAAM,CAAC,gBAAgB,CAAC;gBACtB,IAAI,EAAE,uBAAuB;gBAC7B,KAAK,EAAE,CAAC;gBACR,QAAQ,EAAE,UAAU;gBACpB,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAC9B,OAAO,EAAE,cAAc;oBACvB,MAAM,EAAE,SAAS;iBAClB,CAAC;aACH,CAAC,CACH,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,CAAC,oBAAoB,CAC1C,MAAM,CAAC,gBAAgB,CAAC;gBACtB,IAAI,EAAE,0BAA0B;gBAChC,QAAQ,EAAE,aAAa;gBACvB,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE,IAAI;aACX,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,uCAAuC;IACvC,SAAS,eAAe,CAAC,IAAY;QACnC,OAAO;YACL,IAAI;YACJ,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,EAAE,EAAE,GAAG,IAAI,QAAQ,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC;YACrE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;YACjB,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAC3C,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAC1C,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAC7C,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAC1C,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAC3C,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;YAC7C,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;YAC9C,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;SAC9C,CAAC;IACJ,CAAC;AACH,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\testing\\integration\\background-processors.integration.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { INestApplication } from '@nestjs/common';\r\nimport { EventEmitter2 } from '@nestjs/event-emitter';\r\nimport { getQueueToken } from '@nestjs/bull';\r\nimport { Queue } from 'bull';\r\nimport { BaseTestClass } from '../base/base-test.class';\r\nimport { BackgroundProcessorsModule } from '../../background-processors/background-processors.module';\r\nimport { MonitoringModule } from '../../monitoring/monitoring.module';\r\nimport { JobQueueManagerService } from '../../background-processors/services/job-queue-manager.service';\r\nimport { WorkflowExecutionProcessor } from '../../background-processors/processors/workflow-execution.processor';\r\nimport { AssetDiscoveryProcessor } from '../../background-processors/processors/asset-discovery.processor';\r\nimport { EventCorrelationProcessor } from '../../background-processors/processors/event-correlation.processor';\r\nimport { MetricsCollectionService } from '../../monitoring/services/metrics-collection.service';\r\nimport { JobExecution } from '../../background-processors/entities/job-execution.entity';\r\nimport { JobQueueConfig } from '../../background-processors/entities/job-queue-config.entity';\r\nimport { Metric } from '../../monitoring/entities/metric.entity';\r\n\r\n/**\r\n * Background Processors Integration Tests\r\n * \r\n * Comprehensive integration tests for background processors providing:\r\n * - Job queue validation with Bull Redis integration testing\r\n * - Retry mechanism testing with exponential backoff validation\r\n * - Performance benchmarking with resource utilization monitoring\r\n * - Cross-processor communication and event handling validation\r\n * - Database transaction testing with job execution persistence\r\n * - Monitoring integration with metrics collection verification\r\n * - Error handling and circuit breaker functionality testing\r\n * - Queue health monitoring and alerting system validation\r\n */\r\ndescribe('Background Processors Integration Tests', () => {\r\n  let app: INestApplication;\r\n  let testingModule: TestingModule;\r\n  let jobQueueManager: JobQueueManagerService;\r\n  let workflowProcessor: WorkflowExecutionProcessor;\r\n  let assetDiscoveryProcessor: AssetDiscoveryProcessor;\r\n  let eventCorrelationProcessor: EventCorrelationProcessor;\r\n  let metricsService: MetricsCollectionService;\r\n  let eventEmitter: EventEmitter2;\r\n  let workflowQueue: Queue;\r\n  let assetQueue: Queue;\r\n  let eventQueue: Queue;\r\n  let baseTest: BaseTestClass;\r\n\r\n  // Test entities\r\n  const testEntities = [JobExecution, JobQueueConfig, Metric];\r\n\r\n  beforeAll(async () => {\r\n    // Create base test instance\r\n    baseTest = new (class extends BaseTestClass {})();\r\n\r\n    // Setup test environment with mock queues\r\n    await baseTest.setupTestEnvironment(\r\n      {\r\n        imports: [\r\n          BackgroundProcessorsModule,\r\n          MonitoringModule,\r\n        ],\r\n        providers: [\r\n          // Mock queue providers\r\n          {\r\n            provide: getQueueToken('workflow-execution'),\r\n            useValue: createMockQueue('workflow-execution'),\r\n          },\r\n          {\r\n            provide: getQueueToken('asset-discovery'),\r\n            useValue: createMockQueue('asset-discovery'),\r\n          },\r\n          {\r\n            provide: getQueueToken('event-correlation'),\r\n            useValue: createMockQueue('event-correlation'),\r\n          },\r\n        ],\r\n      },\r\n      testEntities\r\n    );\r\n\r\n    app = baseTest.app;\r\n    testingModule = baseTest.testingModule;\r\n\r\n    // Get service instances\r\n    jobQueueManager = baseTest.getService(JobQueueManagerService);\r\n    workflowProcessor = baseTest.getService(WorkflowExecutionProcessor);\r\n    assetDiscoveryProcessor = baseTest.getService(AssetDiscoveryProcessor);\r\n    eventCorrelationProcessor = baseTest.getService(EventCorrelationProcessor);\r\n    metricsService = baseTest.getService(MetricsCollectionService);\r\n    eventEmitter = baseTest.getService(EventEmitter2);\r\n    workflowQueue = baseTest.getService(getQueueToken('workflow-execution'));\r\n    assetQueue = baseTest.getService(getQueueToken('asset-discovery'));\r\n    eventQueue = baseTest.getService(getQueueToken('event-correlation'));\r\n  });\r\n\r\n  afterAll(async () => {\r\n    await baseTest.cleanupTestEnvironment();\r\n  });\r\n\r\n  beforeEach(async () => {\r\n    await baseTest.setupTestData();\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  afterEach(async () => {\r\n    await baseTest.cleanupTestData();\r\n  });\r\n\r\n  describe('Job Queue Management', () => {\r\n    it('should add jobs to different queues successfully', async () => {\r\n      // Arrange\r\n      const workflowJobData = {\r\n        workflowId: 'test-workflow',\r\n        templateId: 'template-123',\r\n        inputData: { param: 'value' },\r\n      };\r\n\r\n      const assetJobData = {\r\n        scanId: 'scan-123',\r\n        networkRanges: ['192.168.1.0/24'],\r\n        scanConfiguration: {\r\n          scanType: 'full' as const,\r\n          protocols: ['tcp'],\r\n          portRanges: ['1-1000'],\r\n          timeoutMs: 5000,\r\n          maxConcurrency: 10,\r\n          enableVulnerabilityScanning: true,\r\n          enableServiceDetection: true,\r\n          enableOSDetection: true,\r\n        },\r\n        filters: {\r\n          includePatterns: [],\r\n          excludePatterns: [],\r\n          assetTypes: [],\r\n        },\r\n      };\r\n\r\n      // Act\r\n      const workflowJob = await jobQueueManager.addJob(\r\n        'workflow-execution',\r\n        'execute-workflow',\r\n        workflowJobData,\r\n        { priority: 10 }\r\n      );\r\n\r\n      const assetJob = await jobQueueManager.addJob(\r\n        'asset-discovery',\r\n        'discover-network',\r\n        assetJobData,\r\n        { priority: 8 }\r\n      );\r\n\r\n      // Assert\r\n      expect(workflowJob.queueName).toBe('workflow-execution');\r\n      expect(workflowJob.jobType).toBe('execute-workflow');\r\n      expect(workflowJob.priority).toBe(10);\r\n\r\n      expect(assetJob.queueName).toBe('asset-discovery');\r\n      expect(assetJob.jobType).toBe('discover-network');\r\n      expect(assetJob.priority).toBe(8);\r\n\r\n      // Verify queue add methods were called\r\n      expect(workflowQueue.add).toHaveBeenCalledWith(\r\n        'execute-workflow',\r\n        expect.objectContaining({\r\n          jobExecutionId: workflowJob.id,\r\n          ...workflowJobData,\r\n        }),\r\n        expect.objectContaining({ priority: 10 })\r\n      );\r\n\r\n      expect(assetQueue.add).toHaveBeenCalledWith(\r\n        'discover-network',\r\n        expect.objectContaining({\r\n          jobExecutionId: assetJob.id,\r\n          ...assetJobData,\r\n        }),\r\n        expect.objectContaining({ priority: 8 })\r\n      );\r\n    });\r\n\r\n    it('should get queue statistics for all queues', async () => {\r\n      // Arrange\r\n      const mockStats = {\r\n        waiting: [{ id: '1' }, { id: '2' }],\r\n        active: [{ id: '3' }],\r\n        completed: [{ id: '4' }, { id: '5' }, { id: '6' }],\r\n        failed: [{ id: '7' }],\r\n        delayed: [],\r\n      };\r\n\r\n      // Mock queue methods\r\n      Object.values({ workflowQueue, assetQueue, eventQueue }).forEach(queue => {\r\n        (queue.getWaiting as jest.Mock).mockResolvedValue(mockStats.waiting);\r\n        (queue.getActive as jest.Mock).mockResolvedValue(mockStats.active);\r\n        (queue.getCompleted as jest.Mock).mockResolvedValue(mockStats.completed);\r\n        (queue.getFailed as jest.Mock).mockResolvedValue(mockStats.failed);\r\n        (queue.getDelayed as jest.Mock).mockResolvedValue(mockStats.delayed);\r\n      });\r\n\r\n      // Act\r\n      const allStatistics = await jobQueueManager.getAllQueueStatistics();\r\n\r\n      // Assert\r\n      expect(allStatistics).toHaveLength(3);\r\n      \r\n      allStatistics.forEach(stats => {\r\n        expect(stats.counts).toEqual({\r\n          waiting: 2,\r\n          active: 1,\r\n          completed: 3,\r\n          failed: 1,\r\n          delayed: 0,\r\n          total: 7,\r\n        });\r\n        expect(stats.health).toBeDefined();\r\n        expect(['workflow-execution', 'asset-discovery', 'event-correlation'])\r\n          .toContain(stats.queueName);\r\n      });\r\n    });\r\n\r\n    it('should pause and resume queues', async () => {\r\n      // Act\r\n      const paused = await jobQueueManager.pauseQueue('workflow-execution');\r\n      const resumed = await jobQueueManager.resumeQueue('workflow-execution');\r\n\r\n      // Assert\r\n      expect(paused).toBe(true);\r\n      expect(resumed).toBe(true);\r\n      expect(workflowQueue.pause).toHaveBeenCalled();\r\n      expect(workflowQueue.resume).toHaveBeenCalled();\r\n    });\r\n  });\r\n\r\n  describe('Retry Mechanisms', () => {\r\n    it('should retry failed jobs with exponential backoff', async () => {\r\n      // Arrange\r\n      const jobExecution = await baseTest.createTestJobExecution({\r\n        jobType: 'workflow_execution',\r\n        queueName: 'workflow-execution',\r\n        status: 'failed',\r\n        retryInfo: {\r\n          attempt: 1,\r\n          maxAttempts: 3,\r\n          backoffStrategy: 'exponential',\r\n          backoffDelay: 1000,\r\n          retryHistory: [],\r\n        },\r\n        error: {\r\n          message: 'Temporary failure',\r\n          retryable: true,\r\n          timestamp: new Date().toISOString(),\r\n        },\r\n      });\r\n\r\n      // Act\r\n      const retried = await jobQueueManager.retryJob(jobExecution.id);\r\n\r\n      // Assert\r\n      expect(retried).toBe(true);\r\n\r\n      // Verify job execution was updated\r\n      const updatedJob = await baseTest.getRepository(JobExecution)\r\n        .findOne({ where: { id: jobExecution.id } });\r\n      \r\n      expect(updatedJob.retryInfo.attempt).toBe(2);\r\n      expect(updatedJob.status).toBe('pending');\r\n      expect(updatedJob.error).toBeNull();\r\n\r\n      // Verify retry event was emitted\r\n      await baseTest.assertEventEmitted('job.retried', {\r\n        jobExecutionId: jobExecution.id,\r\n        attempt: 2,\r\n      });\r\n    });\r\n\r\n    it('should not retry jobs that exceed max attempts', async () => {\r\n      // Arrange\r\n      const jobExecution = await baseTest.createTestJobExecution({\r\n        jobType: 'workflow_execution',\r\n        queueName: 'workflow-execution',\r\n        status: 'failed',\r\n        retryInfo: {\r\n          attempt: 3,\r\n          maxAttempts: 3,\r\n          backoffStrategy: 'exponential',\r\n          backoffDelay: 1000,\r\n          retryHistory: [],\r\n        },\r\n        error: {\r\n          message: 'Max retries exceeded',\r\n          retryable: true,\r\n          timestamp: new Date().toISOString(),\r\n        },\r\n      });\r\n\r\n      // Act\r\n      const retried = await jobQueueManager.retryJob(jobExecution.id);\r\n\r\n      // Assert\r\n      expect(retried).toBe(false);\r\n\r\n      // Verify job execution was not updated\r\n      const updatedJob = await baseTest.getRepository(JobExecution)\r\n        .findOne({ where: { id: jobExecution.id } });\r\n      \r\n      expect(updatedJob.retryInfo.attempt).toBe(3);\r\n      expect(updatedJob.status).toBe('failed');\r\n    });\r\n\r\n    it('should not retry non-retryable errors', async () => {\r\n      // Arrange\r\n      const jobExecution = await baseTest.createTestJobExecution({\r\n        jobType: 'workflow_execution',\r\n        queueName: 'workflow-execution',\r\n        status: 'failed',\r\n        retryInfo: {\r\n          attempt: 1,\r\n          maxAttempts: 3,\r\n          backoffStrategy: 'exponential',\r\n          backoffDelay: 1000,\r\n          retryHistory: [],\r\n        },\r\n        error: {\r\n          message: 'Validation error',\r\n          retryable: false,\r\n          timestamp: new Date().toISOString(),\r\n        },\r\n      });\r\n\r\n      // Act\r\n      const retried = await jobQueueManager.retryJob(jobExecution.id);\r\n\r\n      // Assert\r\n      expect(retried).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('Performance Testing', () => {\r\n    it('should handle high job throughput', async () => {\r\n      // Arrange\r\n      const jobCount = 100;\r\n      const jobData = {\r\n        workflowId: 'perf-test',\r\n        templateId: 'template-perf',\r\n        inputData: { test: 'data' },\r\n      };\r\n\r\n      // Act\r\n      const { result: jobs, executionTime } = await baseTest.measureExecutionTime(async () => {\r\n        const promises = Array.from({ length: jobCount }, (_, i) =>\r\n          jobQueueManager.addJob(\r\n            'workflow-execution',\r\n            'execute-workflow',\r\n            { ...jobData, workflowId: `perf-test-${i}` },\r\n            { priority: Math.floor(Math.random() * 10) }\r\n          )\r\n        );\r\n        return await Promise.all(promises);\r\n      });\r\n\r\n      // Assert\r\n      expect(jobs).toHaveLength(jobCount);\r\n      expect(executionTime).toBeLessThan(5000); // Should complete within 5 seconds\r\n      expect(jobs.every(job => job.status === 'pending')).toBe(true);\r\n\r\n      console.log(`High Throughput Test (${jobCount} jobs):`, {\r\n        totalTime: `${executionTime}ms`,\r\n        averageTimePerJob: `${executionTime / jobCount}ms`,\r\n        jobsPerSecond: Math.round((jobCount / executionTime) * 1000),\r\n      });\r\n    });\r\n\r\n    it('should monitor resource utilization during processing', async () => {\r\n      // Arrange\r\n      const initialMemory = process.memoryUsage();\r\n      const jobExecution = await baseTest.createTestJobExecution({\r\n        jobType: 'workflow_execution',\r\n        queueName: 'workflow-execution',\r\n        status: 'pending',\r\n      });\r\n\r\n      // Act\r\n      const { result, executionTime } = await baseTest.measureExecutionTime(async () => {\r\n        return await workflowProcessor.executeWorkflow({\r\n          id: 'resource-test-job',\r\n          name: 'execute-workflow',\r\n          data: {\r\n            jobExecutionId: jobExecution.id,\r\n            workflowId: 'resource-test',\r\n            templateId: 'template-resource',\r\n            inputData: {},\r\n            executionContext: { userId: baseTest.testUser.id },\r\n            configuration: {},\r\n          },\r\n          opts: {},\r\n          timestamp: Date.now(),\r\n          attemptsMade: 0,\r\n          delay: 0,\r\n          progress: jest.fn(),\r\n        } as any);\r\n      });\r\n\r\n      const finalMemory = process.memoryUsage();\r\n\r\n      // Assert\r\n      expect(result).toBeDefined();\r\n      expect(executionTime).toBeLessThan(10000);\r\n\r\n      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;\r\n      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024); // Less than 50MB increase\r\n\r\n      console.log('Resource Utilization Test:', {\r\n        executionTime: `${executionTime}ms`,\r\n        memoryIncrease: `${Math.round(memoryIncrease / 1024 / 1024)}MB`,\r\n        initialHeap: `${Math.round(initialMemory.heapUsed / 1024 / 1024)}MB`,\r\n        finalHeap: `${Math.round(finalMemory.heapUsed / 1024 / 1024)}MB`,\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Event Handling', () => {\r\n    it('should emit and handle job lifecycle events', async () => {\r\n      // Arrange\r\n      const eventPromises = [\r\n        baseTest.waitForEvent('job.added'),\r\n        baseTest.waitForEvent('workflow.execution.completed'),\r\n      ];\r\n\r\n      const jobData = {\r\n        workflowId: 'event-test',\r\n        templateId: 'template-event',\r\n        inputData: {},\r\n      };\r\n\r\n      // Act\r\n      const jobExecution = await jobQueueManager.addJob(\r\n        'workflow-execution',\r\n        'execute-workflow',\r\n        jobData\r\n      );\r\n\r\n      await workflowProcessor.executeWorkflow({\r\n        id: 'event-test-job',\r\n        name: 'execute-workflow',\r\n        data: {\r\n          jobExecutionId: jobExecution.id,\r\n          ...jobData,\r\n          executionContext: { userId: baseTest.testUser.id },\r\n          configuration: {},\r\n        },\r\n        opts: {},\r\n        timestamp: Date.now(),\r\n        attemptsMade: 0,\r\n        delay: 0,\r\n        progress: jest.fn(),\r\n      } as any);\r\n\r\n      // Assert\r\n      const [jobAddedEvent, workflowCompletedEvent] = await Promise.all(eventPromises);\r\n\r\n      expect(jobAddedEvent).toMatchObject({\r\n        jobExecutionId: jobExecution.id,\r\n        queueName: 'workflow-execution',\r\n        jobType: 'execute-workflow',\r\n      });\r\n\r\n      expect(workflowCompletedEvent).toMatchObject({\r\n        workflowId: 'event-test',\r\n        templateId: 'template-event',\r\n        jobExecutionId: jobExecution.id,\r\n      });\r\n    });\r\n\r\n    it('should handle cross-processor event communication', async () => {\r\n      // Arrange\r\n      const correlationEventPromise = baseTest.waitForEvent('event.correlation.completed');\r\n      \r\n      const jobExecution = await baseTest.createTestJobExecution({\r\n        jobType: 'event_correlation',\r\n        queueName: 'event-correlation',\r\n        status: 'pending',\r\n      });\r\n\r\n      const correlationData = {\r\n        correlationId: 'cross-processor-test',\r\n        events: [\r\n          { id: '1', type: 'login', timestamp: new Date(), source: 'auth' },\r\n          { id: '2', type: 'access', timestamp: new Date(), source: 'api' },\r\n        ],\r\n        correlationRules: [\r\n          { id: 'rule1', name: 'Login followed by access', pattern: 'login->access' },\r\n        ],\r\n        timeWindow: {\r\n          windowSizeMs: 300000,\r\n          slidingIntervalMs: 60000,\r\n          maxEventsPerWindow: 1000,\r\n        },\r\n        analysisConfig: {\r\n          enableAnomalyDetection: true,\r\n          enablePatternMatching: true,\r\n          enableThreatIntelligence: false,\r\n          enableBehavioralAnalysis: false,\r\n          correlationThreshold: 0.8,\r\n          alertThreshold: 0.9,\r\n        },\r\n      };\r\n\r\n      // Act\r\n      await eventCorrelationProcessor.correlateEvents({\r\n        id: 'cross-processor-job',\r\n        name: 'correlate-events',\r\n        data: {\r\n          jobExecutionId: jobExecution.id,\r\n          ...correlationData,\r\n        },\r\n        opts: {},\r\n        timestamp: Date.now(),\r\n        attemptsMade: 0,\r\n        delay: 0,\r\n        progress: jest.fn(),\r\n      } as any);\r\n\r\n      // Assert\r\n      const correlationEvent = await correlationEventPromise;\r\n      expect(correlationEvent).toMatchObject({\r\n        correlationId: 'cross-processor-test',\r\n        jobExecutionId: jobExecution.id,\r\n        eventsProcessed: 2,\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Monitoring Integration', () => {\r\n    it('should record processor metrics', async () => {\r\n      // Arrange\r\n      const recordMetricSpy = jest.spyOn(metricsService, 'recordMetric');\r\n      const jobExecution = await baseTest.createTestJobExecution({\r\n        jobType: 'asset_discovery',\r\n        queueName: 'asset-discovery',\r\n        status: 'pending',\r\n      });\r\n\r\n      // Act\r\n      await assetDiscoveryProcessor.discoverNetwork({\r\n        id: 'metrics-test-job',\r\n        name: 'discover-network',\r\n        data: {\r\n          jobExecutionId: jobExecution.id,\r\n          scanId: 'metrics-scan',\r\n          networkRanges: ['192.168.1.0/24'],\r\n          scanConfiguration: {\r\n            scanType: 'quick',\r\n            protocols: ['tcp'],\r\n            portRanges: ['80', '443'],\r\n            timeoutMs: 5000,\r\n            maxConcurrency: 5,\r\n            enableVulnerabilityScanning: false,\r\n            enableServiceDetection: true,\r\n            enableOSDetection: false,\r\n          },\r\n          filters: {\r\n            includePatterns: [],\r\n            excludePatterns: [],\r\n            assetTypes: [],\r\n          },\r\n        },\r\n        opts: {},\r\n        timestamp: Date.now(),\r\n        attemptsMade: 0,\r\n        delay: 0,\r\n        progress: jest.fn(),\r\n      } as any);\r\n\r\n      // Assert\r\n      expect(recordMetricSpy).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          name: 'asset_discovery_total',\r\n          value: 1,\r\n          category: 'business',\r\n          type: 'counter',\r\n          labels: expect.objectContaining({\r\n            scan_id: 'metrics-scan',\r\n            status: 'success',\r\n          }),\r\n        })\r\n      );\r\n\r\n      expect(recordMetricSpy).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          name: 'asset_discovery_duration',\r\n          category: 'performance',\r\n          type: 'histogram',\r\n          unit: 'ms',\r\n        })\r\n      );\r\n    });\r\n  });\r\n\r\n  // Helper function to create mock queue\r\n  function createMockQueue(name: string): Partial<Queue> {\r\n    return {\r\n      name,\r\n      add: jest.fn().mockResolvedValue({ id: `${name}-job-${Date.now()}` }),\r\n      getJob: jest.fn(),\r\n      getWaiting: jest.fn().mockResolvedValue([]),\r\n      getActive: jest.fn().mockResolvedValue([]),\r\n      getCompleted: jest.fn().mockResolvedValue([]),\r\n      getFailed: jest.fn().mockResolvedValue([]),\r\n      getDelayed: jest.fn().mockResolvedValue([]),\r\n      pause: jest.fn().mockResolvedValue(undefined),\r\n      resume: jest.fn().mockResolvedValue(undefined),\r\n      close: jest.fn().mockResolvedValue(undefined),\r\n    };\r\n  }\r\n});\r\n"], "version": 3}