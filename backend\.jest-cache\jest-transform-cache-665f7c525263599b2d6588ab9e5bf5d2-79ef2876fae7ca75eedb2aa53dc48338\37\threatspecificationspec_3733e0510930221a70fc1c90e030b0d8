52ff48b3e439f11c286c8033a9f322e2
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const threat_specification_1 = require("../threat.specification");
const threat_entity_1 = require("../../entities/threat/threat.entity");
const threat_severity_enum_1 = require("../../enums/threat-severity.enum");
const ioc_value_object_1 = require("../../value-objects/threat-indicators/ioc.value-object");
describe('ThreatSpecification', () => {
    let threat;
    let criticalThreat;
    let lowSeverityThreat;
    let resolvedThreat;
    beforeEach(() => {
        // Create test threats
        threat = threat_entity_1.Threat.create('Test Threat', 'Test threat description', threat_severity_enum_1.ThreatSeverity.HIGH, 'malware', 'trojan', 85, {
            tags: ['malware', 'high-priority'],
            affectedAssets: ['server-001', 'workstation-042'],
            indicators: [ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '*************', 'high', 'confirmed')],
            attribution: {
                actor: 'APT29',
                confidence: 90,
                aliases: ['Cozy Bear'],
                motivation: ['espionage'],
                capabilities: ['advanced_evasion'],
                campaigns: ['Operation Ghost'],
            },
            malwareFamily: {
                name: 'Emotet',
                confidence: 85,
                capabilities: ['credential_theft'],
                protocols: ['HTTP'],
                persistence: ['registry'],
            },
        });
        criticalThreat = threat_entity_1.Threat.create('Critical Threat', 'Critical threat requiring immediate attention', threat_severity_enum_1.ThreatSeverity.CRITICAL, 'apt', 'advanced-persistent-threat', 95, {
            tags: ['critical', 'apt', 'nation-state'],
            affectedAssets: ['dc-01', 'file-server', 'web-server'],
            indicators: [
                ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '********', 'high', 'confirmed'),
                ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.DOMAIN, 'malicious.example.com', 'high', 'confirmed'),
            ],
        });
        lowSeverityThreat = threat_entity_1.Threat.create('Low Severity Threat', 'Low severity threat', threat_severity_enum_1.ThreatSeverity.LOW, 'suspicious-activity', 'anomaly', 40, {
            tags: ['low-priority', 'anomaly'],
            affectedAssets: ['workstation-001'],
        });
        resolvedThreat = threat_entity_1.Threat.create('Resolved Threat', 'Previously active threat that has been resolved', threat_severity_enum_1.ThreatSeverity.MEDIUM, 'malware', 'virus', 75);
        resolvedThreat.updateMitigationStatus('recovered');
    });
    describe('HighSeverityThreatSpecification', () => {
        it('should identify high severity threats', () => {
            const spec = new threat_specification_1.HighSeverityThreatSpecification();
            expect(spec.isSatisfiedBy(threat)).toBe(true); // HIGH severity
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // CRITICAL severity
            expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // LOW severity
            expect(spec.getDescription()).toBe('Threat has high or critical severity');
        });
    });
    describe('CriticalThreatSpecification', () => {
        it('should identify only critical threats', () => {
            const spec = new threat_specification_1.CriticalThreatSpecification();
            expect(spec.isSatisfiedBy(threat)).toBe(false); // HIGH severity
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // CRITICAL severity
            expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // LOW severity
            expect(spec.getDescription()).toBe('Threat has critical severity');
        });
    });
    describe('ActiveThreatSpecification', () => {
        it('should identify active threats', () => {
            const spec = new threat_specification_1.ActiveThreatSpecification();
            expect(spec.isSatisfiedBy(threat)).toBe(true);
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(true);
            expect(spec.isSatisfiedBy(resolvedThreat)).toBe(false);
            expect(spec.getDescription()).toBe('Threat is active (not resolved)');
        });
    });
    describe('ResolvedThreatSpecification', () => {
        it('should identify resolved threats', () => {
            const spec = new threat_specification_1.ResolvedThreatSpecification();
            expect(spec.isSatisfiedBy(threat)).toBe(false);
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(false);
            expect(spec.isSatisfiedBy(resolvedThreat)).toBe(true);
            expect(spec.getDescription()).toBe('Threat has been resolved');
        });
    });
    describe('HighRiskThreatSpecification', () => {
        it('should identify high risk threats with default threshold', () => {
            const spec = new threat_specification_1.HighRiskThreatSpecification();
            expect(spec.isSatisfiedBy(threat)).toBe(true); // Should have high risk score
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // Should have very high risk score
            expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // Should have low risk score
            expect(spec.getDescription()).toBe('Threat has high risk score (>= 70)');
        });
        it('should use custom risk threshold', () => {
            const spec = new threat_specification_1.HighRiskThreatSpecification(90);
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // Very high risk
            expect(spec.isSatisfiedBy(threat)).toBe(false); // High but not >= 90
            expect(spec.getDescription()).toBe('Threat has high risk score (>= 90)');
        });
    });
    describe('HighConfidenceThreatSpecification', () => {
        it('should identify high confidence threats with default threshold', () => {
            const spec = new threat_specification_1.HighConfidenceThreatSpecification();
            expect(spec.isSatisfiedBy(threat)).toBe(true); // 85% confidence
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // 95% confidence
            expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // 40% confidence
            expect(spec.getDescription()).toBe('Threat has high confidence (>= 80)');
        });
        it('should use custom confidence threshold', () => {
            const spec = new threat_specification_1.HighConfidenceThreatSpecification(90);
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // 95% confidence
            expect(spec.isSatisfiedBy(threat)).toBe(false); // 85% confidence
            expect(spec.getDescription()).toBe('Threat has high confidence (>= 90)');
        });
    });
    describe('RecentThreatSpecification', () => {
        it('should identify recent threats', () => {
            const spec = new threat_specification_1.RecentThreatSpecification(24); // Within 24 hours
            // All test threats should be recent (just created)
            expect(spec.isSatisfiedBy(threat)).toBe(true);
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(true);
            expect(spec.getDescription()).toBe('Threat was detected within 24 hours');
        });
        it('should use default time window', () => {
            const spec = new threat_specification_1.RecentThreatSpecification();
            expect(spec.isSatisfiedBy(threat)).toBe(true);
            expect(spec.getDescription()).toBe('Threat was detected within 24 hours');
        });
    });
    describe('StaleThreatSpecification', () => {
        it('should identify stale threats', () => {
            const spec = new threat_specification_1.StaleThreatSpecification(1); // Older than 1 hour
            // Test threats are just created, so they shouldn't be stale
            expect(spec.isSatisfiedBy(threat)).toBe(false);
            expect(spec.getDescription()).toBe('Threat is older than 1 hours');
        });
    });
    describe('ThreatSeveritySpecification', () => {
        it('should match threats with specified severities', () => {
            const spec = new threat_specification_1.ThreatSeveritySpecification([threat_severity_enum_1.ThreatSeverity.HIGH, threat_severity_enum_1.ThreatSeverity.CRITICAL]);
            expect(spec.isSatisfiedBy(threat)).toBe(true); // HIGH
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // CRITICAL
            expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // LOW
            expect(spec.getDescription()).toBe('Threat severity is one of: HIGH, CRITICAL');
        });
    });
    describe('ThreatCategorySpecification', () => {
        it('should match threats with specified categories', () => {
            const spec = new threat_specification_1.ThreatCategorySpecification(['malware', 'apt']);
            expect(spec.isSatisfiedBy(threat)).toBe(true); // malware
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // apt
            expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // suspicious-activity
            expect(spec.getDescription()).toBe('Threat category is one of: malware, apt');
        });
    });
    describe('ThreatTypeSpecification', () => {
        it('should match threats with specified types', () => {
            const spec = new threat_specification_1.ThreatTypeSpecification(['trojan', 'advanced-persistent-threat']);
            expect(spec.isSatisfiedBy(threat)).toBe(true); // trojan
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // advanced-persistent-threat
            expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // anomaly
            expect(spec.getDescription()).toBe('Threat type is one of: trojan, advanced-persistent-threat');
        });
    });
    describe('ThreatTagSpecification', () => {
        it('should match threats with any of the specified tags', () => {
            const spec = new threat_specification_1.ThreatTagSpecification(['malware', 'critical']);
            expect(spec.isSatisfiedBy(threat)).toBe(true); // has 'malware' tag
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // has 'critical' tag
            expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // has neither tag
            expect(spec.getDescription()).toBe('Threat has any of these tags: malware, critical');
        });
        it('should match threats with all specified tags when requireAll is true', () => {
            const spec = new threat_specification_1.ThreatTagSpecification(['malware', 'high-priority'], true);
            expect(spec.isSatisfiedBy(threat)).toBe(true); // has both tags
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(false); // doesn't have 'malware' tag
            expect(spec.getDescription()).toBe('Threat has all of these tags: malware, high-priority');
        });
    });
    describe('ThreatRiskScoreRangeSpecification', () => {
        it('should match threats within risk score range', () => {
            const spec = new threat_specification_1.ThreatRiskScoreRangeSpecification(60, 80);
            // This depends on the actual risk scores calculated by the threats
            // We'll test the logic rather than specific values
            expect(spec.getDescription()).toBe('Threat risk score is between 60 and 80');
        });
        it('should match threats with minimum risk score', () => {
            const spec = new threat_specification_1.ThreatRiskScoreRangeSpecification(50);
            expect(spec.getDescription()).toBe('Threat risk score is at least 50');
        });
        it('should match threats with maximum risk score', () => {
            const spec = new threat_specification_1.ThreatRiskScoreRangeSpecification(undefined, 90);
            expect(spec.getDescription()).toBe('Threat risk score is at most 90');
        });
    });
    describe('ThreatConfidenceRangeSpecification', () => {
        it('should match threats within confidence range', () => {
            const spec = new threat_specification_1.ThreatConfidenceRangeSpecification(80, 90);
            expect(spec.isSatisfiedBy(threat)).toBe(true); // 85% confidence
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(false); // 95% confidence (above range)
            expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // 40% confidence (below range)
            expect(spec.getDescription()).toBe('Threat confidence is between 80 and 90');
        });
    });
    describe('ThreatAgeRangeSpecification', () => {
        it('should match threats within age range', () => {
            const spec = new threat_specification_1.ThreatAgeRangeSpecification(0, 1); // 0-1 hours old
            // All test threats are just created, so they should match
            expect(spec.isSatisfiedBy(threat)).toBe(true);
            expect(spec.getDescription()).toBe('Threat age is between 0 and 1 hours');
        });
    });
    describe('ThreatAttributionSpecification', () => {
        it('should match threats with attribution', () => {
            const spec = new threat_specification_1.ThreatAttributionSpecification();
            expect(spec.isSatisfiedBy(threat)).toBe(true); // has attribution
            expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // no attribution
            expect(spec.getDescription()).toBe('Threat has attribution information');
        });
        it('should match threats with specific actor', () => {
            const spec = new threat_specification_1.ThreatAttributionSpecification('APT29');
            expect(spec.isSatisfiedBy(threat)).toBe(true); // attributed to APT29
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(false); // no attribution
            expect(spec.getDescription()).toBe('Threat is attributed to actor "APT29"');
        });
        it('should match threats with specific campaign', () => {
            const spec = new threat_specification_1.ThreatAttributionSpecification(undefined, 'Operation Ghost');
            expect(spec.isSatisfiedBy(threat)).toBe(true); // part of Operation Ghost
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(false); // no attribution
            expect(spec.getDescription()).toBe('Threat is part of campaign "Operation Ghost"');
        });
        it('should match threats without attribution when hasAttribution is false', () => {
            const spec = new threat_specification_1.ThreatAttributionSpecification(undefined, undefined, false);
            expect(spec.isSatisfiedBy(threat)).toBe(false); // has attribution
            expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(true); // no attribution
            expect(spec.getDescription()).toBe('Threat has no attribution information');
        });
    });
    describe('ThreatMalwareFamilySpecification', () => {
        it('should match threats with malware family', () => {
            const spec = new threat_specification_1.ThreatMalwareFamilySpecification();
            expect(spec.isSatisfiedBy(threat)).toBe(true); // has malware family
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(false); // no malware family
            expect(spec.getDescription()).toBe('Threat has malware family information');
        });
        it('should match threats with specific malware family', () => {
            const spec = new threat_specification_1.ThreatMalwareFamilySpecification('Emotet');
            expect(spec.isSatisfiedBy(threat)).toBe(true); // Emotet family
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(false); // no malware family
            expect(spec.getDescription()).toBe('Threat is associated with malware family "Emotet"');
        });
        it('should match threats without malware family when hasMalwareFamily is false', () => {
            const spec = new threat_specification_1.ThreatMalwareFamilySpecification(undefined, false);
            expect(spec.isSatisfiedBy(threat)).toBe(false); // has malware family
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // no malware family
            expect(spec.getDescription()).toBe('Threat has no malware family information');
        });
    });
    describe('RequiresImmediateAttentionSpecification', () => {
        it('should identify threats requiring immediate attention', () => {
            const spec = new threat_specification_1.RequiresImmediateAttentionSpecification();
            expect(spec.isSatisfiedBy(threat)).toBe(true); // HIGH severity with high confidence
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // CRITICAL severity
            expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // LOW severity with low confidence
            expect(spec.getDescription()).toBe('Threat requires immediate attention (critical severity or high severity with high confidence)');
        });
    });
    describe('AffectedAssetsSpecification', () => {
        it('should match threats affecting specific assets', () => {
            const spec = new threat_specification_1.AffectedAssetsSpecification(['server-001']);
            expect(spec.isSatisfiedBy(threat)).toBe(true); // affects server-001
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(false); // doesn't affect server-001
            expect(spec.getDescription()).toBe('Threat affects assets: server-001');
        });
        it('should match threats with asset count in range', () => {
            const spec = new threat_specification_1.AffectedAssetsSpecification(undefined, 2, 5);
            expect(spec.isSatisfiedBy(threat)).toBe(true); // affects 2 assets
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // affects 3 assets
            expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // affects 1 asset
            expect(spec.getDescription()).toBe('Threat affects 2-5 assets');
        });
        it('should match threats with minimum asset count', () => {
            const spec = new threat_specification_1.AffectedAssetsSpecification(undefined, 2);
            expect(spec.isSatisfiedBy(threat)).toBe(true); // affects 2 assets
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // affects 3 assets
            expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // affects 1 asset
            expect(spec.getDescription()).toBe('Threat affects at least 2 assets');
        });
    });
    describe('IndicatorsCountSpecification', () => {
        it('should match threats with indicator count in range', () => {
            const spec = new threat_specification_1.IndicatorsCountSpecification(1, 2);
            expect(spec.isSatisfiedBy(threat)).toBe(true); // has 1 indicator
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // has 2 indicators
            expect(spec.getDescription()).toBe('Threat has 1-2 indicators');
        });
        it('should match threats with minimum indicator count', () => {
            const spec = new threat_specification_1.IndicatorsCountSpecification(2);
            expect(spec.isSatisfiedBy(threat)).toBe(false); // has 1 indicator
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // has 2 indicators
            expect(spec.getDescription()).toBe('Threat has at least 2 indicators');
        });
    });
    describe('ThreatSpecificationBuilder', () => {
        it('should build simple specification', () => {
            const spec = threat_specification_1.ThreatSpecificationBuilder.create()
                .highSeverity()
                .build();
            expect(spec.isSatisfiedBy(threat)).toBe(true);
            expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false);
        });
        it('should build complex AND specification', () => {
            const spec = threat_specification_1.ThreatSpecificationBuilder.create()
                .highSeverity()
                .active()
                .highConfidence(80)
                .withCategories('malware', 'apt')
                .build();
            expect(spec.isSatisfiedBy(threat)).toBe(true); // meets all criteria
            expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // doesn't meet severity
            expect(spec.isSatisfiedBy(resolvedThreat)).toBe(false); // not active
        });
        it('should build complex OR specification', () => {
            const spec = threat_specification_1.ThreatSpecificationBuilder.create()
                .critical()
                .requiresImmediateAttention()
                .buildWithOr();
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // is critical
            expect(spec.isSatisfiedBy(threat)).toBe(true); // requires immediate attention
            expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // meets neither
        });
        it('should build specification with tags', () => {
            const spec = threat_specification_1.ThreatSpecificationBuilder.create()
                .withTags(['malware', 'critical'])
                .build();
            expect(spec.isSatisfiedBy(threat)).toBe(true); // has malware tag
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // has critical tag
            expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // has neither tag
        });
        it('should build specification with risk and confidence ranges', () => {
            const spec = threat_specification_1.ThreatSpecificationBuilder.create()
                .riskScoreRange(50, 90)
                .confidenceRange(70, 95)
                .build();
            expect(spec.isSatisfiedBy(threat)).toBe(true); // should meet both criteria
            expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // low confidence
        });
        it('should build specification with attribution and malware family', () => {
            const spec = threat_specification_1.ThreatSpecificationBuilder.create()
                .withAttribution('APT29')
                .withMalwareFamily('Emotet')
                .build();
            expect(spec.isSatisfiedBy(threat)).toBe(true); // has both
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(false); // has neither
        });
        it('should build specification with asset and indicator constraints', () => {
            const spec = threat_specification_1.ThreatSpecificationBuilder.create()
                .affectingAssets(undefined, 2) // at least 2 assets
                .withIndicatorCount(1) // at least 1 indicator
                .build();
            expect(spec.isSatisfiedBy(threat)).toBe(true); // 2 assets, 1 indicator
            expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // 3 assets, 2 indicators
            expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // 1 asset
        });
        it('should throw error when building empty specification', () => {
            expect(() => {
                threat_specification_1.ThreatSpecificationBuilder.create().build();
            }).toThrow('At least one specification must be added');
        });
        it('should return single specification when only one is added', () => {
            const spec = threat_specification_1.ThreatSpecificationBuilder.create()
                .critical()
                .build();
            expect(spec).toBeInstanceOf(threat_specification_1.CriticalThreatSpecification);
        });
    });
    describe('specification combination', () => {
        it('should combine specifications with AND logic', () => {
            const highSeveritySpec = new threat_specification_1.HighSeverityThreatSpecification();
            const activeSpec = new threat_specification_1.ActiveThreatSpecification();
            const combinedSpec = highSeveritySpec.and(activeSpec);
            expect(combinedSpec.isSatisfiedBy(threat)).toBe(true); // high severity and active
            expect(combinedSpec.isSatisfiedBy(resolvedThreat)).toBe(false); // not active
            expect(combinedSpec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // not high severity
        });
        it('should combine specifications with OR logic', () => {
            const criticalSpec = new threat_specification_1.CriticalThreatSpecification();
            const resolvedSpec = new threat_specification_1.ResolvedThreatSpecification();
            const combinedSpec = criticalSpec.or(resolvedSpec);
            expect(combinedSpec.isSatisfiedBy(criticalThreat)).toBe(true); // is critical
            expect(combinedSpec.isSatisfiedBy(resolvedThreat)).toBe(true); // is resolved
            expect(combinedSpec.isSatisfiedBy(threat)).toBe(false); // neither critical nor resolved
        });
        it('should negate specifications', () => {
            const activeSpec = new threat_specification_1.ActiveThreatSpecification();
            const notActiveSpec = activeSpec.not();
            expect(notActiveSpec.isSatisfiedBy(threat)).toBe(false); // is active
            expect(notActiveSpec.isSatisfiedBy(resolvedThreat)).toBe(true); // not active
        });
    });
    describe('specification descriptions', () => {
        it('should provide meaningful descriptions for all specifications', () => {
            const specs = [
                new threat_specification_1.HighSeverityThreatSpecification(),
                new threat_specification_1.CriticalThreatSpecification(),
                new threat_specification_1.ActiveThreatSpecification(),
                new threat_specification_1.ResolvedThreatSpecification(),
                new threat_specification_1.HighRiskThreatSpecification(),
                new threat_specification_1.HighConfidenceThreatSpecification(),
                new threat_specification_1.RecentThreatSpecification(),
                new threat_specification_1.StaleThreatSpecification(),
                new threat_specification_1.ThreatSeveritySpecification([threat_severity_enum_1.ThreatSeverity.HIGH]),
                new threat_specification_1.ThreatCategorySpecification(['malware']),
                new threat_specification_1.ThreatTypeSpecification(['trojan']),
                new threat_specification_1.ThreatTagSpecification(['test']),
                new threat_specification_1.ThreatRiskScoreRangeSpecification(50, 90),
                new threat_specification_1.ThreatConfidenceRangeSpecification(70, 95),
                new threat_specification_1.ThreatAgeRangeSpecification(0, 24),
                new threat_specification_1.ThreatAttributionSpecification(),
                new threat_specification_1.ThreatMalwareFamilySpecification(),
                new threat_specification_1.RequiresImmediateAttentionSpecification(),
                new threat_specification_1.AffectedAssetsSpecification(),
                new threat_specification_1.IndicatorsCountSpecification(),
            ];
            specs.forEach(spec => {
                expect(spec.getDescription()).toBeTruthy();
                expect(typeof spec.getDescription()).toBe('string');
                expect(spec.getDescription().length).toBeGreaterThan(0);
            });
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************