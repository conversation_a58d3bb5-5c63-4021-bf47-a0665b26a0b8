86eaecc00ad390678f2843c395541b03
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const asset_management_service_1 = require("./asset-management.service");
const asset_entity_1 = require("../../domain/entities/asset.entity");
const vulnerability_entity_1 = require("../../domain/entities/vulnerability.entity");
const logger_service_1 = require("../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../infrastructure/logging/audit/audit.service");
describe('AssetManagementService', () => {
    let service;
    let assetRepository;
    let vulnerabilityRepository;
    let loggerService;
    let auditService;
    const mockAssetRepository = {
        create: jest.fn(),
        save: jest.fn(),
        findOne: jest.fn(),
        find: jest.fn(),
        remove: jest.fn(),
        count: jest.fn(),
        createQueryBuilder: jest.fn(),
    };
    const mockVulnerabilityRepository = {
        createQueryBuilder: jest.fn(),
    };
    const mockLoggerService = {
        debug: jest.fn(),
        log: jest.fn(),
        warn: jest.fn(),
        error: jest.fn(),
    };
    const mockAuditService = {
        logUserAction: jest.fn(),
    };
    const mockAsset = {
        id: 'asset-123',
        name: 'test-server',
        type: 'server',
        status: 'active',
        criticality: 'medium',
        environment: 'production',
        ipAddress: '*************',
        hostname: 'test-server.company.com',
        createdBy: 'user-123',
        calculateRiskScore: jest.fn(),
        updateLastSeen: jest.fn(),
        addSoftware: jest.fn(),
        addService: jest.fn(),
        addTag: jest.fn(),
        removeTag: jest.fn(),
        hasTag: jest.fn(),
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [
                asset_management_service_1.AssetManagementService,
                {
                    provide: (0, typeorm_1.getRepositoryToken)(asset_entity_1.Asset),
                    useValue: mockAssetRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(vulnerability_entity_1.Vulnerability),
                    useValue: mockVulnerabilityRepository,
                },
                {
                    provide: logger_service_1.LoggerService,
                    useValue: mockLoggerService,
                },
                {
                    provide: audit_service_1.AuditService,
                    useValue: mockAuditService,
                },
            ],
        }).compile();
        service = module.get(asset_management_service_1.AssetManagementService);
        assetRepository = module.get((0, typeorm_1.getRepositoryToken)(asset_entity_1.Asset));
        vulnerabilityRepository = module.get((0, typeorm_1.getRepositoryToken)(vulnerability_entity_1.Vulnerability));
        loggerService = module.get(logger_service_1.LoggerService);
        auditService = module.get(audit_service_1.AuditService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('createAsset', () => {
        it('should create asset successfully', async () => {
            const assetData = {
                name: 'test-server',
                type: 'server',
                description: 'Test server',
                ipAddress: '*************',
                hostname: 'test-server.company.com',
                criticality: 'medium',
                environment: 'production',
            };
            const userId = 'user-123';
            mockAssetRepository.findOne.mockResolvedValue(null); // No duplicate
            mockAssetRepository.create.mockReturnValue(mockAsset);
            mockAssetRepository.save.mockResolvedValue(mockAsset);
            const result = await service.createAsset(assetData, userId);
            expect(mockAssetRepository.findOne).toHaveBeenCalledWith({
                where: { ipAddress: assetData.ipAddress },
            });
            expect(mockAssetRepository.create).toHaveBeenCalledWith(expect.objectContaining({
                ...assetData,
                status: 'active',
                firstDiscovered: expect.any(Date),
                lastSeen: expect.any(Date),
                createdBy: userId,
                discoveryMethod: 'manual',
            }));
            expect(mockAsset.calculateRiskScore).toHaveBeenCalled();
            expect(mockAssetRepository.save).toHaveBeenCalledWith(mockAsset);
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(userId, 'create', 'asset', mockAsset.id, expect.objectContaining({
                name: assetData.name,
                type: assetData.type,
                ipAddress: assetData.ipAddress,
            }));
            expect(result).toEqual(mockAsset);
        });
        it('should throw error when duplicate IP address exists', async () => {
            const assetData = {
                name: 'test-server',
                type: 'server',
                ipAddress: '*************',
            };
            const userId = 'user-123';
            mockAssetRepository.findOne.mockResolvedValue(mockAsset); // Duplicate found
            await expect(service.createAsset(assetData, userId))
                .rejects.toThrow('Asset with IP address ************* already exists');
        });
    });
    describe('findById', () => {
        it('should return asset when found', async () => {
            const assetId = 'asset-123';
            mockAssetRepository.findOne.mockResolvedValue(mockAsset);
            const result = await service.findById(assetId);
            expect(mockAssetRepository.findOne).toHaveBeenCalledWith({
                where: { id: assetId },
                relations: ['vulnerabilities', 'scanResults'],
            });
            expect(result).toEqual(mockAsset);
        });
        it('should return null when asset not found', async () => {
            const assetId = 'non-existent';
            mockAssetRepository.findOne.mockResolvedValue(null);
            const result = await service.findById(assetId);
            expect(result).toBeNull();
        });
    });
    describe('findMany', () => {
        it('should return paginated assets with filters', async () => {
            const options = {
                page: 1,
                limit: 20,
                type: ['server'],
                status: ['active'],
                criticality: ['high'],
                search: 'test',
            };
            const mockQueryBuilder = {
                andWhere: jest.fn().mockReturnThis(),
                orderBy: jest.fn().mockReturnThis(),
                skip: jest.fn().mockReturnThis(),
                take: jest.fn().mockReturnThis(),
                getManyAndCount: jest.fn().mockResolvedValue([[mockAsset], 1]),
            };
            mockAssetRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
            const result = await service.findMany(options);
            expect(mockAssetRepository.createQueryBuilder).toHaveBeenCalledWith('asset');
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('asset.type IN (:...type)', { type: options.type });
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('asset.status IN (:...status)', { status: options.status });
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('asset.criticality IN (:...criticality)', { criticality: options.criticality });
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('(asset.name ILIKE :search OR asset.description ILIKE :search OR asset.hostname ILIKE :search)', { search: '%test%' });
            expect(result).toEqual({
                assets: [mockAsset],
                total: 1,
                page: 1,
                totalPages: 1,
            });
        });
    });
    describe('updateAsset', () => {
        it('should update asset successfully', async () => {
            const assetId = 'asset-123';
            const updateData = {
                name: 'updated-server',
                criticality: 'high',
            };
            const userId = 'user-123';
            jest.spyOn(service, 'findById').mockResolvedValue(mockAsset);
            mockAssetRepository.save.mockResolvedValue({ ...mockAsset, ...updateData });
            const result = await service.updateAsset(assetId, updateData, userId);
            expect(service.findById).toHaveBeenCalledWith(assetId);
            expect(Object.assign).toHaveBeenCalledWith(mockAsset, updateData, { updatedBy: userId });
            expect(mockAsset.calculateRiskScore).toHaveBeenCalled();
            expect(mockAssetRepository.save).toHaveBeenCalledWith(mockAsset);
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(userId, 'update', 'asset', assetId, expect.objectContaining({
                changes: updateData,
            }));
        });
        it('should throw error when asset not found', async () => {
            const assetId = 'non-existent';
            const updateData = { name: 'updated-server' };
            const userId = 'user-123';
            jest.spyOn(service, 'findById').mockResolvedValue(null);
            await expect(service.updateAsset(assetId, updateData, userId))
                .rejects.toThrow('Asset not found');
        });
    });
    describe('deleteAsset', () => {
        it('should delete asset successfully', async () => {
            const assetId = 'asset-123';
            const userId = 'user-123';
            jest.spyOn(service, 'findById').mockResolvedValue(mockAsset);
            mockAssetRepository.remove.mockResolvedValue(mockAsset);
            await service.deleteAsset(assetId, userId);
            expect(service.findById).toHaveBeenCalledWith(assetId);
            expect(mockAssetRepository.remove).toHaveBeenCalledWith(mockAsset);
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(userId, 'delete', 'asset', assetId, expect.objectContaining({
                name: mockAsset.name,
                type: mockAsset.type,
                ipAddress: mockAsset.ipAddress,
            }));
        });
        it('should throw error when asset not found', async () => {
            const assetId = 'non-existent';
            const userId = 'user-123';
            jest.spyOn(service, 'findById').mockResolvedValue(null);
            await expect(service.deleteAsset(assetId, userId))
                .rejects.toThrow('Asset not found');
        });
    });
    describe('discoverAssets', () => {
        it('should discover and create new assets', async () => {
            const discoveryData = [
                {
                    ipAddress: '*************',
                    hostname: 'new-server.company.com',
                    macAddress: '00:1B:44:11:3A:B8',
                    operatingSystem: { name: 'Ubuntu', version: '20.04' },
                    services: [{ name: 'ssh', port: 22, protocol: 'tcp', state: 'open' }],
                    discoveryMethod: 'network_scan',
                },
            ];
            const userId = 'user-123';
            mockAssetRepository.findOne.mockResolvedValue(null); // No existing asset
            mockAssetRepository.create.mockReturnValue(mockAsset);
            mockAssetRepository.save.mockResolvedValue(mockAsset);
            const result = await service.discoverAssets(discoveryData, userId);
            expect(mockAssetRepository.findOne).toHaveBeenCalledWith({
                where: { ipAddress: discoveryData[0].ipAddress },
            });
            expect(mockAssetRepository.create).toHaveBeenCalledWith(expect.objectContaining({
                name: discoveryData[0].hostname,
                type: 'server', // Determined from services
                ipAddress: discoveryData[0].ipAddress,
                hostname: discoveryData[0].hostname,
                macAddress: discoveryData[0].macAddress,
                operatingSystem: discoveryData[0].operatingSystem,
                services: discoveryData[0].services,
                discoveryMethod: discoveryData[0].discoveryMethod,
                createdBy: userId,
            }));
            expect(result).toEqual([mockAsset]);
        });
        it('should update existing assets during discovery', async () => {
            const discoveryData = [
                {
                    ipAddress: '*************',
                    hostname: 'updated-server.company.com',
                    discoveryMethod: 'network_scan',
                },
            ];
            const userId = 'user-123';
            mockAssetRepository.findOne.mockResolvedValue(mockAsset); // Existing asset
            mockAssetRepository.save.mockResolvedValue(mockAsset);
            const result = await service.discoverAssets(discoveryData, userId);
            expect(mockAsset.updateLastSeen).toHaveBeenCalled();
            expect(mockAssetRepository.save).toHaveBeenCalledWith(mockAsset);
            expect(result).toEqual([mockAsset]);
        });
    });
    describe('updateVulnerabilityCounts', () => {
        it('should update vulnerability counts and risk score', async () => {
            const assetId = 'asset-123';
            const mockVulnCounts = [
                { severity: 'critical', count: '2' },
                { severity: 'high', count: '5' },
                { severity: 'medium', count: '10' },
            ];
            jest.spyOn(service, 'findById').mockResolvedValue(mockAsset);
            const mockQueryBuilder = {
                select: jest.fn().mockReturnThis(),
                addSelect: jest.fn().mockReturnThis(),
                where: jest.fn().mockReturnThis(),
                andWhere: jest.fn().mockReturnThis(),
                groupBy: jest.fn().mockReturnThis(),
                getRawMany: jest.fn().mockResolvedValue(mockVulnCounts),
            };
            mockVulnerabilityRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
            mockAssetRepository.save.mockResolvedValue(mockAsset);
            const result = await service.updateVulnerabilityCounts(assetId);
            expect(service.findById).toHaveBeenCalledWith(assetId);
            expect(mockVulnerabilityRepository.createQueryBuilder).toHaveBeenCalledWith('vuln');
            expect(mockAsset.updateVulnerabilityCounts).toHaveBeenCalledWith({
                critical: 2,
                high: 5,
                medium: 10,
                low: 0,
                info: 0,
                total: 17,
            });
            expect(mockAssetRepository.save).toHaveBeenCalledWith(mockAsset);
            expect(result).toEqual(mockAsset);
        });
    });
    describe('getAssetsDueForScanning', () => {
        it('should return assets due for scanning', async () => {
            const mockQueryBuilder = {
                where: jest.fn().mockReturnThis(),
                andWhere: jest.fn().mockReturnThis(),
                orderBy: jest.fn().mockReturnThis(),
                addOrderBy: jest.fn().mockReturnThis(),
                getMany: jest.fn().mockResolvedValue([mockAsset]),
            };
            mockAssetRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
            const result = await service.getAssetsDueForScanning();
            expect(mockAssetRepository.createQueryBuilder).toHaveBeenCalledWith('asset');
            expect(mockQueryBuilder.where).toHaveBeenCalledWith('asset.isScannable = :scannable', { scannable: true });
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('asset.status = :status', { status: 'active' });
            expect(result).toEqual([mockAsset]);
        });
    });
    describe('getStatistics', () => {
        it('should return asset statistics', async () => {
            mockAssetRepository.count
                .mockResolvedValueOnce(100) // total
                .mockResolvedValueOnce(85) // active
                .mockResolvedValueOnce(15); // critical
            jest.spyOn(service, 'getAssetCountsByType').mockResolvedValue({
                server: 50,
                workstation: 30,
                network_device: 20,
            });
            jest.spyOn(service, 'getAssetCountsByEnvironment').mockResolvedValue({
                production: 60,
                staging: 25,
                development: 15,
            });
            jest.spyOn(service, 'getAssetsWithVulnerabilities').mockResolvedValue(45);
            const result = await service.getStatistics();
            expect(result).toEqual({
                total: 100,
                active: 85,
                critical: 15,
                byType: {
                    server: 50,
                    workstation: 30,
                    network_device: 20,
                },
                byEnvironment: {
                    production: 60,
                    staging: 25,
                    development: 15,
                },
                withVulnerabilities: 45,
                timestamp: expect.any(String),
            });
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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