25241014acef5e186dfb95a9a2400676
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const vulnerability_service_1 = require("../vulnerability.service");
const vulnerability_entity_1 = require("../../../domain/entities/vulnerability.entity");
const vulnerability_assessment_entity_1 = require("../../../domain/entities/vulnerability-assessment.entity");
const vulnerability_exception_entity_1 = require("../../../domain/entities/vulnerability-exception.entity");
const asset_entity_1 = require("../../../../asset-management/domain/entities/asset.entity");
const logger_service_1 = require("../../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../../infrastructure/logging/audit/audit.service");
const notification_service_1 = require("../../../../../infrastructure/notification/notification.service");
describe('VulnerabilityService', () => {
    let service;
    let vulnerabilityRepository;
    let assessmentRepository;
    let exceptionRepository;
    let assetRepository;
    let loggerService;
    let auditService;
    let notificationService;
    const mockVulnerability = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        identifier: 'CVE-2023-1234',
        title: 'Test Vulnerability',
        description: 'Test vulnerability description',
        severity: 'high',
        cvssScore: 7.5,
        publishedDate: new Date('2023-01-01'),
        lastModifiedDate: new Date('2023-01-02'),
        exploitable: false,
        hasExploit: false,
        inTheWild: false,
        patchAvailable: true,
        affectedProducts: [
            {
                vendor: 'Test Vendor',
                product: 'Test Product',
                version: '1.0.0',
            },
        ],
        dataSource: {
            name: 'Test Source',
            type: 'internal',
            confidence: 'high',
            lastUpdated: '2023-01-01T00:00:00Z',
        },
        getSummary: jest.fn(),
        exportForReporting: jest.fn(),
        calculateRiskScore: jest.fn().mockReturnValue(7.5),
        updateExploitStatus: jest.fn(),
        updatePatchStatus: jest.fn(),
        addTag: jest.fn(),
        removeTag: jest.fn(),
    };
    beforeEach(async () => {
        const mockRepository = {
            create: jest.fn(),
            save: jest.fn(),
            find: jest.fn(),
            findOne: jest.fn(),
            findOneBy: jest.fn(),
            count: jest.fn(),
            remove: jest.fn(),
            createQueryBuilder: jest.fn(() => ({
                leftJoinAndSelect: jest.fn().mockReturnThis(),
                where: jest.fn().mockReturnThis(),
                andWhere: jest.fn().mockReturnThis(),
                orderBy: jest.fn().mockReturnThis(),
                addOrderBy: jest.fn().mockReturnThis(),
                skip: jest.fn().mockReturnThis(),
                take: jest.fn().mockReturnThis(),
                getManyAndCount: jest.fn(),
                getMany: jest.fn(),
                select: jest.fn().mockReturnThis(),
                addSelect: jest.fn().mockReturnThis(),
                groupBy: jest.fn().mockReturnThis(),
                getRawMany: jest.fn(),
            })),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                vulnerability_service_1.VulnerabilityService,
                {
                    provide: (0, typeorm_1.getRepositoryToken)(vulnerability_entity_1.Vulnerability),
                    useValue: mockRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(vulnerability_assessment_entity_1.VulnerabilityAssessment),
                    useValue: mockRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(vulnerability_exception_entity_1.VulnerabilityException),
                    useValue: mockRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(asset_entity_1.Asset),
                    useValue: mockRepository,
                },
                {
                    provide: logger_service_1.LoggerService,
                    useValue: {
                        debug: jest.fn(),
                        log: jest.fn(),
                        warn: jest.fn(),
                        error: jest.fn(),
                    },
                },
                {
                    provide: audit_service_1.AuditService,
                    useValue: {
                        logUserAction: jest.fn(),
                    },
                },
                {
                    provide: notification_service_1.NotificationService,
                    useValue: {
                        sendNewCriticalVulnerabilitiesAlert: jest.fn(),
                    },
                },
            ],
        }).compile();
        service = module.get(vulnerability_service_1.VulnerabilityService);
        vulnerabilityRepository = module.get((0, typeorm_1.getRepositoryToken)(vulnerability_entity_1.Vulnerability));
        assessmentRepository = module.get((0, typeorm_1.getRepositoryToken)(vulnerability_assessment_entity_1.VulnerabilityAssessment));
        exceptionRepository = module.get((0, typeorm_1.getRepositoryToken)(vulnerability_exception_entity_1.VulnerabilityException));
        assetRepository = module.get((0, typeorm_1.getRepositoryToken)(asset_entity_1.Asset));
        loggerService = module.get(logger_service_1.LoggerService);
        auditService = module.get(audit_service_1.AuditService);
        notificationService = module.get(notification_service_1.NotificationService);
    });
    it('should be defined', () => {
        expect(service).toBeDefined();
    });
    describe('getVulnerabilityDashboard', () => {
        it('should return vulnerability dashboard data', async () => {
            const mockSeverityCounts = [
                { severity: 'critical', count: '5' },
                { severity: 'high', count: '10' },
                { severity: 'medium', count: '15' },
                { severity: 'low', count: '20' },
            ];
            vulnerabilityRepository.createQueryBuilder().getRawMany.mockResolvedValue(mockSeverityCounts);
            vulnerabilityRepository.count
                .mockResolvedValueOnce(3) // exploitable
                .mockResolvedValueOnce(2) // with exploits
                .mockResolvedValueOnce(1) // in the wild
                .mockResolvedValueOnce(8) // recent
                .mockResolvedValueOnce(25); // patched
            vulnerabilityRepository.find.mockResolvedValue([mockVulnerability]);
            const result = await service.getVulnerabilityDashboard();
            expect(result).toHaveProperty('summary');
            expect(result).toHaveProperty('breakdown');
            expect(result).toHaveProperty('timestamp');
            expect(result.summary.total).toBe(50);
            expect(result.summary.critical).toBe(5);
            expect(result.summary.exploitable).toBe(3);
        });
    });
    describe('searchVulnerabilities', () => {
        it('should search vulnerabilities with filters', async () => {
            const mockQueryBuilder = vulnerabilityRepository.createQueryBuilder();
            mockQueryBuilder.getManyAndCount.mockResolvedValue([[mockVulnerability], 1]);
            const searchCriteria = {
                page: 1,
                limit: 10,
                severities: ['high', 'critical'],
                exploitable: true,
                searchText: 'test',
            };
            const result = await service.searchVulnerabilities(searchCriteria);
            expect(result).toHaveProperty('vulnerabilities');
            expect(result).toHaveProperty('total');
            expect(result).toHaveProperty('page');
            expect(result).toHaveProperty('totalPages');
            expect(result.vulnerabilities).toHaveLength(1);
            expect(result.total).toBe(1);
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('vuln.severity IN (:...severities)', { severities: ['high', 'critical'] });
        });
        it('should handle pagination correctly', async () => {
            const mockQueryBuilder = vulnerabilityRepository.createQueryBuilder();
            mockQueryBuilder.getManyAndCount.mockResolvedValue([[], 0]);
            const searchCriteria = {
                page: 2,
                limit: 25,
            };
            await service.searchVulnerabilities(searchCriteria);
            expect(mockQueryBuilder.skip).toHaveBeenCalledWith(25);
            expect(mockQueryBuilder.take).toHaveBeenCalledWith(25);
        });
    });
    describe('getVulnerabilityDetails', () => {
        it('should return vulnerability details', async () => {
            vulnerabilityRepository.findOne.mockResolvedValue(mockVulnerability);
            const result = await service.getVulnerabilityDetails('123e4567-e89b-12d3-a456-426614174000');
            expect(result).toEqual(mockVulnerability);
            expect(vulnerabilityRepository.findOne).toHaveBeenCalledWith({
                where: { id: '123e4567-e89b-12d3-a456-426614174000' },
                relations: [
                    'assessments',
                    'exceptions',
                    'affectedAssets',
                    'affectedAssets.group',
                ],
            });
        });
        it('should throw NotFoundException when vulnerability not found', async () => {
            vulnerabilityRepository.findOne.mockResolvedValue(null);
            await expect(service.getVulnerabilityDetails('non-existent-id')).rejects.toThrow('Vulnerability not found');
        });
    });
    describe('createVulnerability', () => {
        it('should create a new vulnerability', async () => {
            const vulnerabilityData = {
                identifier: 'CVE-2023-5678',
                title: 'New Vulnerability',
                description: 'New vulnerability description',
                severity: 'medium',
                affectedProducts: [
                    {
                        vendor: 'New Vendor',
                        product: 'New Product',
                        version: '2.0.0',
                    },
                ],
                dataSource: {
                    name: 'Manual Entry',
                    type: 'internal',
                    confidence: 'high',
                    lastUpdated: '2023-01-01T00:00:00Z',
                },
            };
            vulnerabilityRepository.findOne.mockResolvedValue(null); // No existing vulnerability
            vulnerabilityRepository.create.mockReturnValue(mockVulnerability);
            vulnerabilityRepository.save.mockResolvedValue(mockVulnerability);
            const result = await service.createVulnerability(vulnerabilityData, 'user-123');
            expect(vulnerabilityRepository.create).toHaveBeenCalledWith(expect.objectContaining({
                ...vulnerabilityData,
                publishedDate: expect.any(Date),
                lastModifiedDate: expect.any(Date),
            }));
            expect(vulnerabilityRepository.save).toHaveBeenCalledWith(mockVulnerability);
            expect(auditService.logUserAction).toHaveBeenCalledWith('user-123', 'create', 'vulnerability', mockVulnerability.id, expect.any(Object));
        });
        it('should throw error when vulnerability already exists', async () => {
            const vulnerabilityData = {
                identifier: 'CVE-2023-1234',
                title: 'Existing Vulnerability',
                description: 'Description',
                severity: 'high',
                affectedProducts: [],
                dataSource: {
                    name: 'Test',
                    type: 'internal',
                    confidence: 'high',
                    lastUpdated: '2023-01-01T00:00:00Z',
                },
            };
            vulnerabilityRepository.findOne.mockResolvedValue(mockVulnerability);
            await expect(service.createVulnerability(vulnerabilityData, 'user-123')).rejects.toThrow('Vulnerability with this identifier already exists');
        });
    });
    describe('updateVulnerability', () => {
        it('should update an existing vulnerability', async () => {
            const updates = {
                severity: 'critical',
                cvssScore: 9.0,
            };
            vulnerabilityRepository.findOne.mockResolvedValue(mockVulnerability);
            vulnerabilityRepository.save.mockResolvedValue({
                ...mockVulnerability,
                ...updates,
            });
            const result = await service.updateVulnerability('123e4567-e89b-12d3-a456-426614174000', updates, 'user-123');
            expect(vulnerabilityRepository.save).toHaveBeenCalled();
            expect(auditService.logUserAction).toHaveBeenCalledWith('user-123', 'update', 'vulnerability', '123e4567-e89b-12d3-a456-426614174000', expect.objectContaining({
                identifier: mockVulnerability.identifier,
                changes: expect.any(Object),
            }));
        });
        it('should throw NotFoundException when vulnerability not found', async () => {
            vulnerabilityRepository.findOne.mockResolvedValue(null);
            await expect(service.updateVulnerability('non-existent-id', {}, 'user-123')).rejects.toThrow('Vulnerability not found');
        });
    });
    describe('deleteVulnerability', () => {
        it('should delete an existing vulnerability', async () => {
            vulnerabilityRepository.findOne.mockResolvedValue(mockVulnerability);
            vulnerabilityRepository.remove.mockResolvedValue(mockVulnerability);
            await service.deleteVulnerability('123e4567-e89b-12d3-a456-426614174000', 'user-123');
            expect(vulnerabilityRepository.remove).toHaveBeenCalledWith(mockVulnerability);
            expect(auditService.logUserAction).toHaveBeenCalledWith('user-123', 'delete', 'vulnerability', '123e4567-e89b-12d3-a456-426614174000', expect.objectContaining({
                identifier: mockVulnerability.identifier,
                severity: mockVulnerability.severity,
                title: mockVulnerability.title,
            }));
        });
        it('should throw NotFoundException when vulnerability not found', async () => {
            vulnerabilityRepository.findOne.mockResolvedValue(null);
            await expect(service.deleteVulnerability('non-existent-id', 'user-123')).rejects.toThrow('Vulnerability not found');
        });
    });
    describe('monitorVulnerabilities', () => {
        it('should monitor vulnerabilities and send alerts for new critical ones', async () => {
            const mockCriticalVulns = [
                { ...mockVulnerability, severity: 'critical' },
            ];
            vulnerabilityRepository.find.mockResolvedValue(mockCriticalVulns);
            await service.monitorVulnerabilities();
            expect(notificationService.sendNewCriticalVulnerabilitiesAlert).toHaveBeenCalledWith(mockCriticalVulns);
        });
        it('should handle monitoring errors gracefully', async () => {
            vulnerabilityRepository.find.mockRejectedValue(new Error('Database error'));
            await expect(service.monitorVulnerabilities()).resolves.not.toThrow();
            expect(loggerService.error).toHaveBeenCalledWith('Failed to complete vulnerability monitoring', expect.objectContaining({
                error: 'Database error',
            }));
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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