{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\vulnerability.specification.ts", "mappings": ";;;AAAA,6DAA8D;AAC9D,yFAAoG;AACpG,wEAA+D;AAE/D,0EAAiE;AAEjE;;;;;GAKG;AACH,MAAsB,0BAA2B,SAAQ,iCAAgC;IACvF;;OAEG;IACO,kBAAkB,CAAC,aAA4B,EAAE,UAA4B;QACrF,OAAO,UAAU,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACO,gBAAgB,CAAC,aAA4B,EAAE,QAA+B;QACtF,OAAO,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACO,kBAAkB,CAAC,aAA4B,EAAE,UAAoB;QAC7E,OAAO,UAAU,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACO,cAAc,CAAC,aAA4B,EAAE,KAAe;QACpE,OAAO,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACO,SAAS,CAAC,aAA4B,EAAE,IAAc;QAC9D,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACO,UAAU,CAAC,aAA4B,EAAE,IAAc;QAC/D,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACO,gBAAgB,CAAC,aAA4B,EAAE,UAAmB,EAAE,UAAmB;QAC/F,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,aAAa,CAAC,SAAS,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;QAC1E,MAAM,OAAO,GAAG,KAAK,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QAE9C,IAAI,UAAU,KAAK,SAAS,IAAI,OAAO,GAAG,UAAU,EAAE,CAAC;YACrD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,UAAU,KAAK,SAAS,IAAI,OAAO,GAAG,UAAU,EAAE,CAAC;YACrD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACO,uBAAuB,CAAC,aAA4B,EAAE,aAA+B,EAAE,aAA+B;QAC9H,MAAM,gBAAgB,GAAG;YACvB,CAAC,uCAAe,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7B,CAAC,uCAAe,CAAC,GAAG,CAAC,EAAE,CAAC;YACxB,CAAC,uCAAe,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3B,CAAC,uCAAe,CAAC,IAAI,CAAC,EAAE,CAAC;YACzB,CAAC,uCAAe,CAAC,SAAS,CAAC,EAAE,CAAC;YAC9B,CAAC,uCAAe,CAAC,SAAS,CAAC,EAAE,CAAC;SAC/B,CAAC;QAEF,MAAM,YAAY,GAAG,gBAAgB,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QAEhE,IAAI,aAAa,KAAK,SAAS,IAAI,YAAY,GAAG,gBAAgB,CAAC,aAAa,CAAC,EAAE,CAAC;YAClF,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,aAAa,KAAK,SAAS,IAAI,YAAY,GAAG,gBAAgB,CAAC,aAAa,CAAC,EAAE,CAAC;YAClF,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACO,sBAAsB,CAAC,aAA4B,EAAE,QAAiB,EAAE,QAAiB;QACjG,MAAM,SAAS,GAAG,aAAa,CAAC,cAAc,CAAC,SAAS,CAAC;QAEzD,IAAI,QAAQ,KAAK,SAAS,IAAI,SAAS,GAAG,QAAQ,EAAE,CAAC;YACnD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,QAAQ,KAAK,SAAS,IAAI,SAAS,GAAG,QAAQ,EAAE,CAAC;YACnD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACO,sBAAsB,CAAC,aAA4B,EAAE,QAAiB,EAAE,QAAiB;QACjG,IAAI,aAAa,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1C,OAAO,QAAQ,KAAK,SAAS,IAAI,QAAQ,IAAI,CAAC,CAAC;QACjD,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;QAEpF,IAAI,QAAQ,KAAK,SAAS,IAAI,OAAO,GAAG,QAAQ,EAAE,CAAC;YACjD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,QAAQ,KAAK,SAAS,IAAI,OAAO,GAAG,QAAQ,EAAE,CAAC;YACjD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AA5HD,gEA4HC;AAED;;;;GAIG;AACH,MAAa,kCAAmC,SAAQ,0BAA0B;IAChF,aAAa,CAAC,aAA4B;QACxC,OAAO,aAAa,CAAC,QAAQ,KAAK,qCAAc,CAAC,QAAQ,CAAC;IAC5D,CAAC;IAED,cAAc;QACZ,OAAO,qCAAqC,CAAC;IAC/C,CAAC;CACF;AARD,gFAQC;AAED;;;;GAIG;AACH,MAAa,sCAAuC,SAAQ,0BAA0B;IACpF,aAAa,CAAC,aAA4B;QACxC,OAAO,aAAa,CAAC,QAAQ,KAAK,qCAAc,CAAC,IAAI;YAC9C,aAAa,CAAC,QAAQ,KAAK,qCAAc,CAAC,QAAQ,CAAC;IAC5D,CAAC;IAED,cAAc;QACZ,OAAO,6CAA6C,CAAC;IACvD,CAAC;CACF;AATD,wFASC;AAED;;;;GAIG;AACH,MAAa,gCAAiC,SAAQ,0BAA0B;IAC9E,aAAa,CAAC,aAA4B;QACxC,OAAO,CAAC;YACN,0CAAmB,CAAC,UAAU;YAC9B,0CAAmB,CAAC,QAAQ;YAC5B,0CAAmB,CAAC,MAAM;YAC1B,0CAAmB,CAAC,cAAc;SACnC,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;IAED,cAAc;QACZ,OAAO,qEAAqE,CAAC;IAC/E,CAAC;CACF;AAbD,4EAaC;AAED;;;;GAIG;AACH,MAAa,oCAAqC,SAAQ,0BAA0B;IAClF,aAAa,CAAC,aAA4B;QACxC,OAAO;YACL,0CAAmB,CAAC,UAAU;YAC9B,0CAAmB,CAAC,QAAQ;YAC5B,0CAAmB,CAAC,MAAM;SAC3B,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;IAED,cAAc;QACZ,OAAO,mCAAmC,CAAC;IAC7C,CAAC;CACF;AAZD,oFAYC;AAED;;;;GAIG;AACH,MAAa,kCAAmC,SAAQ,0BAA0B;IAChF,YAA6B,eAAuB,EAAE;QACpD,KAAK,EAAE,CAAC;QADmB,iBAAY,GAAZ,YAAY,CAAa;IAEtD,CAAC;IAED,aAAa,CAAC,aAA4B;QACxC,OAAO,aAAa,CAAC,cAAc,CAAC,SAAS,IAAI,IAAI,CAAC,YAAY,CAAC;IACrE,CAAC;IAED,cAAc;QACZ,OAAO,yCAAyC,IAAI,CAAC,YAAY,GAAG,CAAC;IACvE,CAAC;CACF;AAZD,gFAYC;AAED;;;;GAIG;AACH,MAAa,wCAAyC,SAAQ,0BAA0B;IACtF,aAAa,CAAC,aAA4B;QACxC,OAAO;YACL,uCAAe,CAAC,IAAI;YACpB,uCAAe,CAAC,SAAS;YACzB,uCAAe,CAAC,SAAS;SAC1B,CAAC,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;IACvC,CAAC;IAED,cAAc;QACZ,OAAO,yCAAyC,CAAC;IACnD,CAAC;CACF;AAZD,4FAYC;AAED;;;;GAIG;AACH,MAAa,gCAAiC,SAAQ,0BAA0B;IAC9E,YAA6B,aAAqB,CAAC;QACjD,KAAK,EAAE,CAAC;QADmB,eAAU,GAAV,UAAU,CAAY;IAEnD,CAAC;IAED,aAAa,CAAC,aAA4B;QACxC,OAAO,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IAC1E,CAAC;IAED,cAAc;QACZ,OAAO,uCAAuC,IAAI,CAAC,UAAU,OAAO,CAAC;IACvE,CAAC;CACF;AAZD,4EAYC;AAED;;;;GAIG;AACH,MAAa,+BAAgC,SAAQ,0BAA0B;IAC7E,YAA6B,gBAAwB,EAAE;QACrD,KAAK,EAAE,CAAC;QADmB,kBAAa,GAAb,aAAa,CAAa;IAEvD,CAAC;IAED,aAAa,CAAC,aAA4B;QACxC,OAAO,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;IAC7E,CAAC;IAED,cAAc;QACZ,OAAO,+BAA+B,IAAI,CAAC,aAAa,OAAO,CAAC;IAClE,CAAC;CACF;AAZD,0EAYC;AAED;;;;GAIG;AACH,MAAa,6BAA8B,SAAQ,0BAA0B;IAC3E,YAA6B,KAAc;QACzC,KAAK,EAAE,CAAC;QADmB,UAAK,GAAL,KAAK,CAAS;IAE3C,CAAC;IAED,aAAa,CAAC,aAA4B;QACxC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;YACzB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,OAAO,aAAa,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,CAAC;QAC5C,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,cAAc;QACZ,OAAO,IAAI,CAAC,KAAK;YACf,CAAC,CAAC,6BAA6B,IAAI,CAAC,KAAK,EAAE;YAC3C,CAAC,CAAC,oCAAoC,CAAC;IAC3C,CAAC;CACF;AAtBD,sEAsBC;AAED;;;;GAIG;AACH,MAAa,iCAAkC,SAAQ,0BAA0B;IAC/E,aAAa,CAAC,aAA4B;QACxC,OAAO,CAAC,aAAa,CAAC,KAAK,IAAI,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IACzE,CAAC;IAED,cAAc;QACZ,OAAO,iDAAiD,CAAC;IAC3D,CAAC;CACF;AARD,8EAQC;AAED;;;;GAIG;AACH,MAAa,2CAA4C,SAAQ,0BAA0B;IACzF,aAAa,CAAC,aAA4B;QACxC,OAAO,aAAa,CAAC,YAAY,EAAE,MAAM,KAAK,qBAAqB;YAC5D,aAAa,CAAC,YAAY,EAAE,MAAM,KAAK,YAAY,CAAC;IAC7D,CAAC;IAED,cAAc;QACZ,OAAO,mDAAmD,CAAC;IAC7D,CAAC;CACF;AATD,kGASC;AAED;;;;GAIG;AACH,MAAa,2CAA4C,SAAQ,0BAA0B;IACzF,aAAa,CAAC,aAA4B;QACxC,OAAO,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAC/C,KAAK,CAAC,QAAQ,KAAK,UAAU,IAAI,KAAK,CAAC,QAAQ,KAAK,OAAO,CAC5D,CAAC;IACJ,CAAC;IAED,cAAc;QACZ,OAAO,iDAAiD,CAAC;IAC3D,CAAC;CACF;AAVD,kGAUC;AAED;;;;GAIG;AACH,MAAa,uCAAwC,SAAQ,0BAA0B;IACrF,aAAa,CAAC,aAA4B;QACxC,OAAO,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAC/C,KAAK,CAAC,WAAW,KAAK,UAAU,CACjC,CAAC;IACJ,CAAC;IAED,cAAc;QACZ,OAAO,uCAAuC,CAAC;IACjD,CAAC;CACF;AAVD,0FAUC;AAED;;;;GAIG;AACH,MAAa,kCAAmC,SAAQ,0BAA0B;IAChF,YAA6B,WAAmB,GAAG;QACjD,KAAK,EAAE,CAAC;QADmB,aAAQ,GAAR,QAAQ,CAAc;IAEnD,CAAC;IAED,aAAa,CAAC,aAA4B;QACxC,OAAO,IAAI,CAAC,sBAAsB,CAAC,aAAa,EAAE,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;IAC9E,CAAC;IAED,cAAc;QACZ,OAAO,mCAAmC,IAAI,CAAC,QAAQ,EAAE,CAAC;IAC5D,CAAC;CACF;AAZD,gFAYC;AAED;;;;GAIG;AACH,MAAa,+BAAgC,SAAQ,0BAA0B;IAC7E,aAAa,CAAC,aAA4B;QACxC,OAAO,aAAa,CAAC,oBAAoB,EAAE,CAAC;IAC9C,CAAC;IAED,cAAc;QACZ,OAAO,sCAAsC,CAAC;IAChD,CAAC;CACF;AARD,0EAQC;AAED;;;;GAIG;AACH,MAAa,uCAAwC,SAAQ,0BAA0B;IACrF,aAAa,CAAC,aAA4B;QACxC,OAAO,aAAa,CAAC,0BAA0B,EAAE,CAAC;IACpD,CAAC;IAED,cAAc;QACZ,OAAO,4CAA4C,CAAC;IACtD,CAAC;CACF;AARD,0FAQC;AAED;;;;GAIG;AACH,MAAa,kCAAmC,SAAQ,0BAA0B;IAChF,YAA6B,UAA4B;QACvD,KAAK,EAAE,CAAC;QADmB,eAAU,GAAV,UAAU,CAAkB;IAEzD,CAAC;IAED,aAAa,CAAC,aAA4B;QACxC,OAAO,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IACjE,CAAC;IAED,cAAc;QACZ,OAAO,qCAAqC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAC3E,CAAC;CACF;AAZD,gFAYC;AAED;;;;GAIG;AACH,MAAa,gCAAiC,SAAQ,0BAA0B;IAC9E,YAA6B,QAA+B;QAC1D,KAAK,EAAE,CAAC;QADmB,aAAQ,GAAR,QAAQ,CAAuB;IAE5D,CAAC;IAED,aAAa,CAAC,aAA4B;QACxC,OAAO,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC7D,CAAC;IAED,cAAc;QACZ,OAAO,mCAAmC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IACvE,CAAC;CACF;AAZD,4EAYC;AAED;;;;GAIG;AACH,MAAa,kCAAmC,SAAQ,0BAA0B;IAChF,YAA6B,UAAoB;QAC/C,KAAK,EAAE,CAAC;QADmB,eAAU,GAAV,UAAU,CAAU;IAEjD,CAAC;IAED,aAAa,CAAC,aAA4B;QACxC,OAAO,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IACjE,CAAC;IAED,cAAc;QACZ,OAAO,qCAAqC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAC3E,CAAC;CACF;AAZD,gFAYC;AAED;;;;GAIG;AACH,MAAa,8BAA+B,SAAQ,0BAA0B;IAC5E,YAA6B,KAAe;QAC1C,KAAK,EAAE,CAAC;QADmB,UAAK,GAAL,KAAK,CAAU;IAE5C,CAAC;IAED,aAAa,CAAC,aAA4B;QACxC,OAAO,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IACxD,CAAC;IAED,cAAc;QACZ,OAAO,iCAAiC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAClE,CAAC;CACF;AAZD,wEAYC;AAED;;;;GAIG;AACH,MAAa,6BAA8B,SAAQ,0BAA0B;IAC3E,YACmB,IAAc,EACd,aAAsB,KAAK;QAE5C,KAAK,EAAE,CAAC;QAHS,SAAI,GAAJ,IAAI,CAAU;QACd,eAAU,GAAV,UAAU,CAAiB;IAG9C,CAAC;IAED,aAAa,CAAC,aAA4B;QACxC,OAAO,IAAI,CAAC,UAAU;YACpB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC;YAC3C,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/C,CAAC;IAED,cAAc;QACZ,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;QACjD,OAAO,qBAAqB,QAAQ,mBAAmB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAChF,CAAC;CACF;AAlBD,sEAkBC;AAED;;;;GAIG;AACH,MAAa,wCAAyC,SAAQ,0BAA0B;IACtF,YACmB,QAAiB,EACjB,QAAiB;QAElC,KAAK,EAAE,CAAC;QAHS,aAAQ,GAAR,QAAQ,CAAS;QACjB,aAAQ,GAAR,QAAQ,CAAS;IAGpC,CAAC;IAED,aAAa,CAAC,aAA4B;QACxC,OAAO,IAAI,CAAC,sBAAsB,CAAC,aAAa,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAClF,CAAC;IAED,cAAc;QACZ,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC/D,OAAO,uCAAuC,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;QACrF,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,wCAAwC,IAAI,CAAC,QAAQ,EAAE,CAAC;QACjE,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,uCAAuC,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChE,CAAC;QACD,OAAO,kCAAkC,CAAC;IAC5C,CAAC;CACF;AAtBD,4FAsBC;AAED;;;;GAIG;AACH,MAAa,wCAAyC,SAAQ,0BAA0B;IACtF,YACmB,QAAiB,EACjB,QAAiB;QAElC,KAAK,EAAE,CAAC;QAHS,aAAQ,GAAR,QAAQ,CAAS;QACjB,aAAQ,GAAR,QAAQ,CAAS;IAGpC,CAAC;IAED,aAAa,CAAC,aAA4B;QACxC,OAAO,IAAI,CAAC,sBAAsB,CAAC,aAAa,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAClF,CAAC;IAED,cAAc;QACZ,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC/D,OAAO,uCAAuC,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;QACrF,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,wCAAwC,IAAI,CAAC,QAAQ,EAAE,CAAC;QACjE,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,uCAAuC,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChE,CAAC;QACD,OAAO,kCAAkC,CAAC;IAC5C,CAAC;CACF;AAtBD,4FAsBC;AAED;;;;GAIG;AACH,MAAa,kCAAmC,SAAQ,0BAA0B;IAChF,YACmB,UAAmB,EACnB,UAAmB;QAEpC,KAAK,EAAE,CAAC;QAHS,eAAU,GAAV,UAAU,CAAS;QACnB,eAAU,GAAV,UAAU,CAAS;IAGtC,CAAC;IAED,aAAa,CAAC,aAA4B;QACxC,OAAO,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IAChF,CAAC;IAED,cAAc;QACZ,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YACnE,OAAO,gCAAgC,IAAI,CAAC,UAAU,QAAQ,IAAI,CAAC,UAAU,OAAO,CAAC;QACvF,CAAC;aAAM,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YACzC,OAAO,6BAA6B,IAAI,CAAC,UAAU,WAAW,CAAC;QACjE,CAAC;aAAM,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YACzC,OAAO,4BAA4B,IAAI,CAAC,UAAU,WAAW,CAAC;QAChE,CAAC;QACD,OAAO,2BAA2B,CAAC;IACrC,CAAC;CACF;AAtBD,gFAsBC;AAED;;;;GAIG;AACH,MAAa,+BAAgC,SAAQ,0BAA0B;IAC7E,YACmB,QAAiB,EACjB,QAAiB;QAElC,KAAK,EAAE,CAAC;QAHS,aAAQ,GAAR,QAAQ,CAAS;QACjB,aAAQ,GAAR,QAAQ,CAAS;IAGpC,CAAC;IAED,aAAa,CAAC,aAA4B;QACxC,MAAM,UAAU,GAAG,aAAa,CAAC,cAAc,CAAC,MAAM,CAAC;QAEvD,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,UAAU,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC9D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,UAAU,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC9D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,cAAc;QACZ,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC/D,OAAO,yBAAyB,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,SAAS,CAAC;QAC1E,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,kCAAkC,IAAI,CAAC,QAAQ,SAAS,CAAC;QAClE,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,iCAAiC,IAAI,CAAC,QAAQ,SAAS,CAAC;QACjE,CAAC;QACD,OAAO,4CAA4C,CAAC;IACtD,CAAC;CACF;AAhCD,0EAgCC;AAED;;;;GAIG;AACH,MAAa,4BAA6B,SAAQ,0BAA0B;IAC1E,YAA6B,OAAiB;QAC5C,KAAK,EAAE,CAAC;QADmB,YAAO,GAAP,OAAO,CAAU;IAE9C,CAAC;IAED,aAAa,CAAC,aAA4B;QACxC,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAC/D,CAAC;IAED,cAAc;QACZ,OAAO,2CAA2C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAC9E,CAAC;CACF;AAZD,oEAYC;AAED;;;;GAIG;AACH,MAAa,iCAAiC;IAA9C;QACU,mBAAc,GAAiC,EAAE,CAAC;IAkQ5D,CAAC;IAhQC;;OAEG;IACH,QAAQ;QACN,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,kCAAkC,EAAE,CAAC,CAAC;QACnE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,YAAY;QACV,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,sCAAsC,EAAE,CAAC,CAAC;QACvE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,gCAAgC,EAAE,CAAC,CAAC;QACjE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,UAAU;QACR,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,oCAAoC,EAAE,CAAC,CAAC;QACrE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,eAAuB,EAAE;QAChC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,kCAAkC,CAAC,YAAY,CAAC,CAAC,CAAC;QAC/E,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,wCAAwC,EAAE,CAAC,CAAC;QACzE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAqB,CAAC;QAC3B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,gCAAgC,CAAC,UAAU,CAAC,CAAC,CAAC;QAC3E,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAwB,EAAE;QAC9B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,+BAA+B,CAAC,aAAa,CAAC,CAAC,CAAC;QAC7E,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,KAAc;QACpB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,6BAA6B,CAAC,KAAK,CAAC,CAAC,CAAC;QACnE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,iCAAiC,EAAE,CAAC,CAAC;QAClE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,2CAA2C,EAAE,CAAC,CAAC;QAC5E,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,2CAA2C,EAAE,CAAC,CAAC;QAC5E,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,uCAAuC,EAAE,CAAC,CAAC;QACxE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,WAAmB,GAAG;QAC7B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,kCAAkC,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC3E,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,+BAA+B,EAAE,CAAC,CAAC;QAChE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,0BAA0B;QACxB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,uCAAuC,EAAE,CAAC,CAAC;QACxE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,GAAG,UAA4B;QAC5C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,kCAAkC,CAAC,UAAU,CAAC,CAAC,CAAC;QAC7E,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,GAAG,QAA+B;QAC7C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,gCAAgC,CAAC,QAAQ,CAAC,CAAC,CAAC;QACzE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,GAAG,UAAoB;QACpC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,kCAAkC,CAAC,UAAU,CAAC,CAAC,CAAC;QAC7E,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,GAAG,KAAe;QAC1B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,8BAA8B,CAAC,KAAK,CAAC,CAAC,CAAC;QACpE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,IAAc,EAAE,aAAsB,KAAK;QAClD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,6BAA6B,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;QAC9E,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,QAAiB,EAAE,QAAiB;QACjD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,wCAAwC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC3F,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,QAAiB,EAAE,QAAiB;QACjD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,wCAAwC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC3F,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,OAAgB,EAAE,OAAgB;QACzC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,kCAAkC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;QACnF,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,QAAiB,EAAE,QAAiB;QACrD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,+BAA+B,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;QAClF,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,GAAG,OAAiB;QAC/B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,4BAA4B,CAAC,OAAO,CAAC,CAAC,CAAC;QACpE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC;QAED,4CAA4C;QAC5C,IAAI,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpD,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAA+B,CAAC;QAChF,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,WAAW;QACT,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC;QAED,2CAA2C;QAC3C,IAAI,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpD,QAAQ,GAAG,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAA+B,CAAC;QAC/E,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM;QACX,OAAO,IAAI,iCAAiC,EAAE,CAAC;IACjD,CAAC;CACF;AAnQD,8EAmQC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\vulnerability.specification.ts"], "sourcesContent": ["import { BaseSpecification } from '../../../../shared-kernel';\r\nimport { Vulnerability, VulnerabilityStatus } from '../entities/vulnerability/vulnerability.entity';\r\nimport { ThreatSeverity } from '../enums/threat-severity.enum';\r\nimport { VulnerabilitySeverity } from '../enums/vulnerability-severity.enum';\r\nimport { ConfidenceLevel } from '../enums/confidence-level.enum';\r\n\r\n/**\r\n * Vulnerability Specification Base Class\r\n * \r\n * Base class for all vulnerability-related specifications.\r\n * Provides common functionality for vulnerability filtering and validation.\r\n */\r\nexport abstract class VulnerabilitySpecification extends BaseSpecification<Vulnerability> {\r\n  /**\r\n   * Helper method to check if vulnerability matches any of the provided severities\r\n   */\r\n  protected matchesAnySeverity(vulnerability: Vulnerability, severities: ThreatSeverity[]): boolean {\r\n    return severities.includes(vulnerability.severity);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if vulnerability matches any of the provided statuses\r\n   */\r\n  protected matchesAnyStatus(vulnerability: Vulnerability, statuses: VulnerabilityStatus[]): boolean {\r\n    return statuses.includes(vulnerability.status);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if vulnerability matches any of the provided categories\r\n   */\r\n  protected matchesAnyCategory(vulnerability: Vulnerability, categories: string[]): boolean {\r\n    return categories.includes(vulnerability.category);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if vulnerability matches any of the provided types\r\n   */\r\n  protected matchesAnyType(vulnerability: Vulnerability, types: string[]): boolean {\r\n    return types.includes(vulnerability.type);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if vulnerability has any of the provided tags\r\n   */\r\n  protected hasAnyTag(vulnerability: Vulnerability, tags: string[]): boolean {\r\n    return vulnerability.tags.some(tag => tags.includes(tag));\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if vulnerability has all of the provided tags\r\n   */\r\n  protected hasAllTags(vulnerability: Vulnerability, tags: string[]): boolean {\r\n    return tags.every(tag => vulnerability.tags.includes(tag));\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if vulnerability age is within range\r\n   */\r\n  protected isAgeWithinRange(vulnerability: Vulnerability, minAgeDays?: number, maxAgeDays?: number): boolean {\r\n    const ageMs = Date.now() - vulnerability.discovery.discoveredAt.getTime();\r\n    const ageDays = ageMs / (1000 * 60 * 60 * 24);\r\n    \r\n    if (minAgeDays !== undefined && ageDays < minAgeDays) {\r\n      return false;\r\n    }\r\n    \r\n    if (maxAgeDays !== undefined && ageDays > maxAgeDays) {\r\n      return false;\r\n    }\r\n    \r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if confidence is within range\r\n   */\r\n  protected isConfidenceWithinRange(vulnerability: Vulnerability, minConfidence?: ConfidenceLevel, maxConfidence?: ConfidenceLevel): boolean {\r\n    const confidenceValues = {\r\n      [ConfidenceLevel.VERY_LOW]: 1,\r\n      [ConfidenceLevel.LOW]: 2,\r\n      [ConfidenceLevel.MEDIUM]: 3,\r\n      [ConfidenceLevel.HIGH]: 4,\r\n      [ConfidenceLevel.VERY_HIGH]: 5,\r\n      [ConfidenceLevel.CONFIRMED]: 6,\r\n    };\r\n\r\n    const currentValue = confidenceValues[vulnerability.confidence];\r\n    \r\n    if (minConfidence !== undefined && currentValue < confidenceValues[minConfidence]) {\r\n      return false;\r\n    }\r\n    \r\n    if (maxConfidence !== undefined && currentValue > confidenceValues[maxConfidence]) {\r\n      return false;\r\n    }\r\n    \r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if risk score is within range\r\n   */\r\n  protected isRiskScoreWithinRange(vulnerability: Vulnerability, minScore?: number, maxScore?: number): boolean {\r\n    const riskScore = vulnerability.riskAssessment.riskScore;\r\n    \r\n    if (minScore !== undefined && riskScore < minScore) {\r\n      return false;\r\n    }\r\n    \r\n    if (maxScore !== undefined && riskScore > maxScore) {\r\n      return false;\r\n    }\r\n    \r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if CVSS score is within range\r\n   */\r\n  protected isCVSSScoreWithinRange(vulnerability: Vulnerability, minScore?: number, maxScore?: number): boolean {\r\n    if (vulnerability.cvssScores.length === 0) {\r\n      return minScore === undefined || minScore <= 0;\r\n    }\r\n\r\n    const maxCVSS = Math.max(...vulnerability.cvssScores.map(score => score.baseScore));\r\n    \r\n    if (minScore !== undefined && maxCVSS < minScore) {\r\n      return false;\r\n    }\r\n    \r\n    if (maxScore !== undefined && maxCVSS > maxScore) {\r\n      return false;\r\n    }\r\n    \r\n    return true;\r\n  }\r\n}\r\n\r\n/**\r\n * Critical Vulnerability Specification\r\n * \r\n * Specification for vulnerabilities with critical severity.\r\n */\r\nexport class CriticalVulnerabilitySpecification extends VulnerabilitySpecification {\r\n  isSatisfiedBy(vulnerability: Vulnerability): boolean {\r\n    return vulnerability.severity === ThreatSeverity.CRITICAL;\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Vulnerability has critical severity';\r\n  }\r\n}\r\n\r\n/**\r\n * High Severity Vulnerability Specification\r\n * \r\n * Specification for vulnerabilities with high or critical severity.\r\n */\r\nexport class HighSeverityVulnerabilitySpecification extends VulnerabilitySpecification {\r\n  isSatisfiedBy(vulnerability: Vulnerability): boolean {\r\n    return vulnerability.severity === ThreatSeverity.HIGH || \r\n           vulnerability.severity === ThreatSeverity.CRITICAL;\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Vulnerability has high or critical severity';\r\n  }\r\n}\r\n\r\n/**\r\n * Active Vulnerability Specification\r\n * \r\n * Specification for vulnerabilities that are not yet remediated.\r\n */\r\nexport class ActiveVulnerabilitySpecification extends VulnerabilitySpecification {\r\n  isSatisfiedBy(vulnerability: Vulnerability): boolean {\r\n    return ![\r\n      VulnerabilityStatus.REMEDIATED,\r\n      VulnerabilityStatus.VERIFIED,\r\n      VulnerabilityStatus.CLOSED,\r\n      VulnerabilityStatus.FALSE_POSITIVE,\r\n    ].includes(vulnerability.status);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Vulnerability is active (not remediated, closed, or false positive)';\r\n  }\r\n}\r\n\r\n/**\r\n * Remediated Vulnerability Specification\r\n * \r\n * Specification for vulnerabilities that have been remediated.\r\n */\r\nexport class RemediatedVulnerabilitySpecification extends VulnerabilitySpecification {\r\n  isSatisfiedBy(vulnerability: Vulnerability): boolean {\r\n    return [\r\n      VulnerabilityStatus.REMEDIATED,\r\n      VulnerabilityStatus.VERIFIED,\r\n      VulnerabilityStatus.CLOSED,\r\n    ].includes(vulnerability.status);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Vulnerability has been remediated';\r\n  }\r\n}\r\n\r\n/**\r\n * High Risk Vulnerability Specification\r\n * \r\n * Specification for vulnerabilities with high risk scores.\r\n */\r\nexport class HighRiskVulnerabilitySpecification extends VulnerabilitySpecification {\r\n  constructor(private readonly minRiskScore: number = 70) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(vulnerability: Vulnerability): boolean {\r\n    return vulnerability.riskAssessment.riskScore >= this.minRiskScore;\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Vulnerability has high risk score (>= ${this.minRiskScore})`;\r\n  }\r\n}\r\n\r\n/**\r\n * High Confidence Vulnerability Specification\r\n * \r\n * Specification for vulnerabilities with high confidence levels.\r\n */\r\nexport class HighConfidenceVulnerabilitySpecification extends VulnerabilitySpecification {\r\n  isSatisfiedBy(vulnerability: Vulnerability): boolean {\r\n    return [\r\n      ConfidenceLevel.HIGH,\r\n      ConfidenceLevel.VERY_HIGH,\r\n      ConfidenceLevel.CONFIRMED,\r\n    ].includes(vulnerability.confidence);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Vulnerability has high confidence level';\r\n  }\r\n}\r\n\r\n/**\r\n * Recent Vulnerability Specification\r\n * \r\n * Specification for vulnerabilities discovered recently.\r\n */\r\nexport class RecentVulnerabilitySpecification extends VulnerabilitySpecification {\r\n  constructor(private readonly withinDays: number = 7) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(vulnerability: Vulnerability): boolean {\r\n    return this.isAgeWithinRange(vulnerability, undefined, this.withinDays);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Vulnerability was discovered within ${this.withinDays} days`;\r\n  }\r\n}\r\n\r\n/**\r\n * Stale Vulnerability Specification\r\n * \r\n * Specification for vulnerabilities that are considered stale.\r\n */\r\nexport class StaleVulnerabilitySpecification extends VulnerabilitySpecification {\r\n  constructor(private readonly olderThanDays: number = 90) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(vulnerability: Vulnerability): boolean {\r\n    return this.isAgeWithinRange(vulnerability, this.olderThanDays, undefined);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Vulnerability is older than ${this.olderThanDays} days`;\r\n  }\r\n}\r\n\r\n/**\r\n * CVE Vulnerability Specification\r\n * \r\n * Specification for vulnerabilities with CVE identifiers.\r\n */\r\nexport class CVEVulnerabilitySpecification extends VulnerabilitySpecification {\r\n  constructor(private readonly cveId?: string) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(vulnerability: Vulnerability): boolean {\r\n    if (!vulnerability.cveId) {\r\n      return false;\r\n    }\r\n\r\n    if (this.cveId) {\r\n      return vulnerability.cveId === this.cveId;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  getDescription(): string {\r\n    return this.cveId \r\n      ? `Vulnerability has CVE ID: ${this.cveId}`\r\n      : 'Vulnerability has a CVE identifier';\r\n  }\r\n}\r\n\r\n/**\r\n * Zero Day Vulnerability Specification\r\n * \r\n * Specification for zero-day vulnerabilities (no CVE).\r\n */\r\nexport class ZeroDayVulnerabilitySpecification extends VulnerabilitySpecification {\r\n  isSatisfiedBy(vulnerability: Vulnerability): boolean {\r\n    return !vulnerability.cveId || vulnerability.tags.includes('zero-day');\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Vulnerability is a zero-day (no CVE identifier)';\r\n  }\r\n}\r\n\r\n/**\r\n * Actively Exploited Vulnerability Specification\r\n * \r\n * Specification for vulnerabilities that are actively exploited.\r\n */\r\nexport class ActivelyExploitedVulnerabilitySpecification extends VulnerabilitySpecification {\r\n  isSatisfiedBy(vulnerability: Vulnerability): boolean {\r\n    return vulnerability.exploitation?.status === 'active_exploitation' ||\r\n           vulnerability.exploitation?.status === 'weaponized';\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Vulnerability is actively exploited or weaponized';\r\n  }\r\n}\r\n\r\n/**\r\n * Externally Exposed Vulnerability Specification\r\n * \r\n * Specification for vulnerabilities affecting externally exposed assets.\r\n */\r\nexport class ExternallyExposedVulnerabilitySpecification extends VulnerabilitySpecification {\r\n  isSatisfiedBy(vulnerability: Vulnerability): boolean {\r\n    return vulnerability.affectedAssets.some(asset => \r\n      asset.exposure === 'external' || asset.exposure === 'cloud'\r\n    );\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Vulnerability affects externally exposed assets';\r\n  }\r\n}\r\n\r\n/**\r\n * Critical Asset Vulnerability Specification\r\n * \r\n * Specification for vulnerabilities affecting critical assets.\r\n */\r\nexport class CriticalAssetVulnerabilitySpecification extends VulnerabilitySpecification {\r\n  isSatisfiedBy(vulnerability: Vulnerability): boolean {\r\n    return vulnerability.affectedAssets.some(asset => \r\n      asset.criticality === 'critical'\r\n    );\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Vulnerability affects critical assets';\r\n  }\r\n}\r\n\r\n/**\r\n * High CVSS Score Vulnerability Specification\r\n * \r\n * Specification for vulnerabilities with high CVSS scores.\r\n */\r\nexport class HighCVSSVulnerabilitySpecification extends VulnerabilitySpecification {\r\n  constructor(private readonly minScore: number = 7.0) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(vulnerability: Vulnerability): boolean {\r\n    return this.isCVSSScoreWithinRange(vulnerability, this.minScore, undefined);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Vulnerability has CVSS score >= ${this.minScore}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Overdue Remediation Specification\r\n * \r\n * Specification for vulnerabilities with overdue remediation.\r\n */\r\nexport class OverdueRemediationSpecification extends VulnerabilitySpecification {\r\n  isSatisfiedBy(vulnerability: Vulnerability): boolean {\r\n    return vulnerability.isRemediationOverdue();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Vulnerability remediation is overdue';\r\n  }\r\n}\r\n\r\n/**\r\n * Requires Immediate Attention Specification\r\n * \r\n * Specification for vulnerabilities requiring immediate attention.\r\n */\r\nexport class RequiresImmediateAttentionSpecification extends VulnerabilitySpecification {\r\n  isSatisfiedBy(vulnerability: Vulnerability): boolean {\r\n    return vulnerability.requiresImmediateAttention();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Vulnerability requires immediate attention';\r\n  }\r\n}\r\n\r\n/**\r\n * Vulnerability Severity Specification\r\n * \r\n * Specification for vulnerabilities of specific severities.\r\n */\r\nexport class VulnerabilitySeveritySpecification extends VulnerabilitySpecification {\r\n  constructor(private readonly severities: ThreatSeverity[]) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(vulnerability: Vulnerability): boolean {\r\n    return this.matchesAnySeverity(vulnerability, this.severities);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Vulnerability severity is one of: ${this.severities.join(', ')}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Vulnerability Status Specification\r\n * \r\n * Specification for vulnerabilities with specific statuses.\r\n */\r\nexport class VulnerabilityStatusSpecification extends VulnerabilitySpecification {\r\n  constructor(private readonly statuses: VulnerabilityStatus[]) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(vulnerability: Vulnerability): boolean {\r\n    return this.matchesAnyStatus(vulnerability, this.statuses);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Vulnerability status is one of: ${this.statuses.join(', ')}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Vulnerability Category Specification\r\n * \r\n * Specification for vulnerabilities of specific categories.\r\n */\r\nexport class VulnerabilityCategorySpecification extends VulnerabilitySpecification {\r\n  constructor(private readonly categories: string[]) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(vulnerability: Vulnerability): boolean {\r\n    return this.matchesAnyCategory(vulnerability, this.categories);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Vulnerability category is one of: ${this.categories.join(', ')}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Vulnerability Type Specification\r\n * \r\n * Specification for vulnerabilities of specific types.\r\n */\r\nexport class VulnerabilityTypeSpecification extends VulnerabilitySpecification {\r\n  constructor(private readonly types: string[]) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(vulnerability: Vulnerability): boolean {\r\n    return this.matchesAnyType(vulnerability, this.types);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Vulnerability type is one of: ${this.types.join(', ')}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Vulnerability Tag Specification\r\n * \r\n * Specification for vulnerabilities with specific tags.\r\n */\r\nexport class VulnerabilityTagSpecification extends VulnerabilitySpecification {\r\n  constructor(\r\n    private readonly tags: string[],\r\n    private readonly requireAll: boolean = false\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(vulnerability: Vulnerability): boolean {\r\n    return this.requireAll \r\n      ? this.hasAllTags(vulnerability, this.tags)\r\n      : this.hasAnyTag(vulnerability, this.tags);\r\n  }\r\n\r\n  getDescription(): string {\r\n    const operator = this.requireAll ? 'all' : 'any';\r\n    return `Vulnerability has ${operator} of these tags: ${this.tags.join(', ')}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Vulnerability Risk Score Range Specification\r\n * \r\n * Specification for vulnerabilities within a specific risk score range.\r\n */\r\nexport class VulnerabilityRiskScoreRangeSpecification extends VulnerabilitySpecification {\r\n  constructor(\r\n    private readonly minScore?: number,\r\n    private readonly maxScore?: number\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(vulnerability: Vulnerability): boolean {\r\n    return this.isRiskScoreWithinRange(vulnerability, this.minScore, this.maxScore);\r\n  }\r\n\r\n  getDescription(): string {\r\n    if (this.minScore !== undefined && this.maxScore !== undefined) {\r\n      return `Vulnerability risk score is between ${this.minScore} and ${this.maxScore}`;\r\n    } else if (this.minScore !== undefined) {\r\n      return `Vulnerability risk score is at least ${this.minScore}`;\r\n    } else if (this.maxScore !== undefined) {\r\n      return `Vulnerability risk score is at most ${this.maxScore}`;\r\n    }\r\n    return 'Vulnerability has any risk score';\r\n  }\r\n}\r\n\r\n/**\r\n * Vulnerability CVSS Score Range Specification\r\n * \r\n * Specification for vulnerabilities within a specific CVSS score range.\r\n */\r\nexport class VulnerabilityCVSSScoreRangeSpecification extends VulnerabilitySpecification {\r\n  constructor(\r\n    private readonly minScore?: number,\r\n    private readonly maxScore?: number\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(vulnerability: Vulnerability): boolean {\r\n    return this.isCVSSScoreWithinRange(vulnerability, this.minScore, this.maxScore);\r\n  }\r\n\r\n  getDescription(): string {\r\n    if (this.minScore !== undefined && this.maxScore !== undefined) {\r\n      return `Vulnerability CVSS score is between ${this.minScore} and ${this.maxScore}`;\r\n    } else if (this.minScore !== undefined) {\r\n      return `Vulnerability CVSS score is at least ${this.minScore}`;\r\n    } else if (this.maxScore !== undefined) {\r\n      return `Vulnerability CVSS score is at most ${this.maxScore}`;\r\n    }\r\n    return 'Vulnerability has any CVSS score';\r\n  }\r\n}\r\n\r\n/**\r\n * Vulnerability Age Range Specification\r\n * \r\n * Specification for vulnerabilities within a specific age range.\r\n */\r\nexport class VulnerabilityAgeRangeSpecification extends VulnerabilitySpecification {\r\n  constructor(\r\n    private readonly minAgeDays?: number,\r\n    private readonly maxAgeDays?: number\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(vulnerability: Vulnerability): boolean {\r\n    return this.isAgeWithinRange(vulnerability, this.minAgeDays, this.maxAgeDays);\r\n  }\r\n\r\n  getDescription(): string {\r\n    if (this.minAgeDays !== undefined && this.maxAgeDays !== undefined) {\r\n      return `Vulnerability age is between ${this.minAgeDays} and ${this.maxAgeDays} days`;\r\n    } else if (this.minAgeDays !== undefined) {\r\n      return `Vulnerability is at least ${this.minAgeDays} days old`;\r\n    } else if (this.maxAgeDays !== undefined) {\r\n      return `Vulnerability is at most ${this.maxAgeDays} days old`;\r\n    }\r\n    return 'Vulnerability has any age';\r\n  }\r\n}\r\n\r\n/**\r\n * Affected Asset Count Specification\r\n * \r\n * Specification for vulnerabilities affecting a specific number of assets.\r\n */\r\nexport class AffectedAssetCountSpecification extends VulnerabilitySpecification {\r\n  constructor(\r\n    private readonly minCount?: number,\r\n    private readonly maxCount?: number\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(vulnerability: Vulnerability): boolean {\r\n    const assetCount = vulnerability.affectedAssets.length;\r\n    \r\n    if (this.minCount !== undefined && assetCount < this.minCount) {\r\n      return false;\r\n    }\r\n    \r\n    if (this.maxCount !== undefined && assetCount > this.maxCount) {\r\n      return false;\r\n    }\r\n    \r\n    return true;\r\n  }\r\n\r\n  getDescription(): string {\r\n    if (this.minCount !== undefined && this.maxCount !== undefined) {\r\n      return `Vulnerability affects ${this.minCount}-${this.maxCount} assets`;\r\n    } else if (this.minCount !== undefined) {\r\n      return `Vulnerability affects at least ${this.minCount} assets`;\r\n    } else if (this.maxCount !== undefined) {\r\n      return `Vulnerability affects at most ${this.maxCount} assets`;\r\n    }\r\n    return 'Vulnerability affects any number of assets';\r\n  }\r\n}\r\n\r\n/**\r\n * Discovery Method Specification\r\n * \r\n * Specification for vulnerabilities discovered by specific methods.\r\n */\r\nexport class DiscoveryMethodSpecification extends VulnerabilitySpecification {\r\n  constructor(private readonly methods: string[]) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(vulnerability: Vulnerability): boolean {\r\n    return this.methods.includes(vulnerability.discovery.method);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Vulnerability was discovered by one of: ${this.methods.join(', ')}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Composite Vulnerability Specification Builder\r\n * \r\n * Builder for creating complex vulnerability specifications using fluent interface.\r\n */\r\nexport class VulnerabilitySpecificationBuilder {\r\n  private specifications: VulnerabilitySpecification[] = [];\r\n\r\n  /**\r\n   * Add critical severity filter\r\n   */\r\n  critical(): VulnerabilitySpecificationBuilder {\r\n    this.specifications.push(new CriticalVulnerabilitySpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add high severity filter\r\n   */\r\n  highSeverity(): VulnerabilitySpecificationBuilder {\r\n    this.specifications.push(new HighSeverityVulnerabilitySpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add active status filter\r\n   */\r\n  active(): VulnerabilitySpecificationBuilder {\r\n    this.specifications.push(new ActiveVulnerabilitySpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add remediated status filter\r\n   */\r\n  remediated(): VulnerabilitySpecificationBuilder {\r\n    this.specifications.push(new RemediatedVulnerabilitySpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add high risk filter\r\n   */\r\n  highRisk(minRiskScore: number = 70): VulnerabilitySpecificationBuilder {\r\n    this.specifications.push(new HighRiskVulnerabilitySpecification(minRiskScore));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add high confidence filter\r\n   */\r\n  highConfidence(): VulnerabilitySpecificationBuilder {\r\n    this.specifications.push(new HighConfidenceVulnerabilitySpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add recent vulnerabilities filter\r\n   */\r\n  recent(withinDays: number = 7): VulnerabilitySpecificationBuilder {\r\n    this.specifications.push(new RecentVulnerabilitySpecification(withinDays));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add stale vulnerabilities filter\r\n   */\r\n  stale(olderThanDays: number = 90): VulnerabilitySpecificationBuilder {\r\n    this.specifications.push(new StaleVulnerabilitySpecification(olderThanDays));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add CVE filter\r\n   */\r\n  withCVE(cveId?: string): VulnerabilitySpecificationBuilder {\r\n    this.specifications.push(new CVEVulnerabilitySpecification(cveId));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add zero-day filter\r\n   */\r\n  zeroDay(): VulnerabilitySpecificationBuilder {\r\n    this.specifications.push(new ZeroDayVulnerabilitySpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add actively exploited filter\r\n   */\r\n  activelyExploited(): VulnerabilitySpecificationBuilder {\r\n    this.specifications.push(new ActivelyExploitedVulnerabilitySpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add externally exposed filter\r\n   */\r\n  externallyExposed(): VulnerabilitySpecificationBuilder {\r\n    this.specifications.push(new ExternallyExposedVulnerabilitySpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add critical asset filter\r\n   */\r\n  affectsCriticalAssets(): VulnerabilitySpecificationBuilder {\r\n    this.specifications.push(new CriticalAssetVulnerabilitySpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add high CVSS score filter\r\n   */\r\n  highCVSS(minScore: number = 7.0): VulnerabilitySpecificationBuilder {\r\n    this.specifications.push(new HighCVSSVulnerabilitySpecification(minScore));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add overdue remediation filter\r\n   */\r\n  overdueRemediation(): VulnerabilitySpecificationBuilder {\r\n    this.specifications.push(new OverdueRemediationSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add requires immediate attention filter\r\n   */\r\n  requiresImmediateAttention(): VulnerabilitySpecificationBuilder {\r\n    this.specifications.push(new RequiresImmediateAttentionSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add severity filter\r\n   */\r\n  withSeverities(...severities: ThreatSeverity[]): VulnerabilitySpecificationBuilder {\r\n    this.specifications.push(new VulnerabilitySeveritySpecification(severities));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add status filter\r\n   */\r\n  withStatuses(...statuses: VulnerabilityStatus[]): VulnerabilitySpecificationBuilder {\r\n    this.specifications.push(new VulnerabilityStatusSpecification(statuses));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add category filter\r\n   */\r\n  withCategories(...categories: string[]): VulnerabilitySpecificationBuilder {\r\n    this.specifications.push(new VulnerabilityCategorySpecification(categories));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add type filter\r\n   */\r\n  withTypes(...types: string[]): VulnerabilitySpecificationBuilder {\r\n    this.specifications.push(new VulnerabilityTypeSpecification(types));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add tag filter\r\n   */\r\n  withTags(tags: string[], requireAll: boolean = false): VulnerabilitySpecificationBuilder {\r\n    this.specifications.push(new VulnerabilityTagSpecification(tags, requireAll));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add risk score range filter\r\n   */\r\n  riskScoreRange(minScore?: number, maxScore?: number): VulnerabilitySpecificationBuilder {\r\n    this.specifications.push(new VulnerabilityRiskScoreRangeSpecification(minScore, maxScore));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add CVSS score range filter\r\n   */\r\n  cvssScoreRange(minScore?: number, maxScore?: number): VulnerabilitySpecificationBuilder {\r\n    this.specifications.push(new VulnerabilityCVSSScoreRangeSpecification(minScore, maxScore));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add age range filter\r\n   */\r\n  ageRange(minDays?: number, maxDays?: number): VulnerabilitySpecificationBuilder {\r\n    this.specifications.push(new VulnerabilityAgeRangeSpecification(minDays, maxDays));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add affected asset count filter\r\n   */\r\n  affectedAssetCount(minCount?: number, maxCount?: number): VulnerabilitySpecificationBuilder {\r\n    this.specifications.push(new AffectedAssetCountSpecification(minCount, maxCount));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add discovery method filter\r\n   */\r\n  discoveredBy(...methods: string[]): VulnerabilitySpecificationBuilder {\r\n    this.specifications.push(new DiscoveryMethodSpecification(methods));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Build the final specification using AND logic\r\n   */\r\n  build(): VulnerabilitySpecification {\r\n    if (this.specifications.length === 0) {\r\n      throw new Error('At least one specification must be added');\r\n    }\r\n\r\n    if (this.specifications.length === 1) {\r\n      return this.specifications[0];\r\n    }\r\n\r\n    // Combine all specifications with AND logic\r\n    let combined = this.specifications[0];\r\n    for (let i = 1; i < this.specifications.length; i++) {\r\n      combined = combined.and(this.specifications[i]) as VulnerabilitySpecification;\r\n    }\r\n\r\n    return combined;\r\n  }\r\n\r\n  /**\r\n   * Build the final specification using OR logic\r\n   */\r\n  buildWithOr(): VulnerabilitySpecification {\r\n    if (this.specifications.length === 0) {\r\n      throw new Error('At least one specification must be added');\r\n    }\r\n\r\n    if (this.specifications.length === 1) {\r\n      return this.specifications[0];\r\n    }\r\n\r\n    // Combine all specifications with OR logic\r\n    let combined = this.specifications[0];\r\n    for (let i = 1; i < this.specifications.length; i++) {\r\n      combined = combined.or(this.specifications[i]) as VulnerabilitySpecification;\r\n    }\r\n\r\n    return combined;\r\n  }\r\n\r\n  /**\r\n   * Create a new builder instance\r\n   */\r\n  static create(): VulnerabilitySpecificationBuilder {\r\n    return new VulnerabilitySpecificationBuilder();\r\n  }\r\n}"], "version": 3}