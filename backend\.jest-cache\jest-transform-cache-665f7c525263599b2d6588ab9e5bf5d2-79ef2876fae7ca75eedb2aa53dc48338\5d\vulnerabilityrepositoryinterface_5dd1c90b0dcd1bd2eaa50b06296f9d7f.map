{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\repositories\\vulnerability.repository.interface.ts", "mappings": "", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\repositories\\vulnerability.repository.interface.ts"], "sourcesContent": ["import { Vulnerability, VulnerabilityStatus } from '../../entities/vulnerability/vulnerability.entity';\r\nimport { ThreatSeverity } from '../../enums/threat-severity.enum';\r\nimport { ConfidenceLevel } from '../../enums/confidence-level.enum';\r\nimport { BaseRepository } from '../../../../shared-kernel/domain/base-repository.interface';\r\n\r\n/**\r\n * Vulnerability Search Criteria\r\n */\r\nexport interface VulnerabilitySearchCriteria {\r\n  /** CVE identifiers */\r\n  cveIds?: string[];\r\n  /** Vulnerability statuses */\r\n  statuses?: VulnerabilityStatus[];\r\n  /** Severity levels */\r\n  severities?: ThreatSeverity[];\r\n  /** Confidence levels */\r\n  confidenceLevels?: ConfidenceLevel[];\r\n  /** Categories */\r\n  categories?: string[];\r\n  /** Types */\r\n  types?: string[];\r\n  /** Risk score range */\r\n  riskScoreRange?: {\r\n    min: number;\r\n    max: number;\r\n  };\r\n  /** CVSS score range */\r\n  cvssScoreRange?: {\r\n    min: number;\r\n    max: number;\r\n  };\r\n  /** Affected asset IDs */\r\n  affectedAssetIds?: string[];\r\n  /** Asset criticality levels */\r\n  assetCriticalities?: ('low' | 'medium' | 'high' | 'critical')[];\r\n  /** Network exposure types */\r\n  exposureTypes?: ('internal' | 'dmz' | 'external' | 'cloud')[];\r\n  /** Discovery date range */\r\n  discoveryDateRange?: {\r\n    from: Date;\r\n    to: Date;\r\n  };\r\n  /** Remediation due date range */\r\n  remediationDueDateRange?: {\r\n    from: Date;\r\n    to: Date;\r\n  };\r\n  /** Assigned analyst */\r\n  assignedTo?: string;\r\n  /** Tags */\r\n  tags?: string[];\r\n  /** Text search in title/description */\r\n  searchText?: string;\r\n  /** Exploitation status */\r\n  exploitationStatus?: ('not_exploited' | 'proof_of_concept' | 'active_exploitation' | 'weaponized')[];\r\n  /** Has public exploits */\r\n  hasPublicExploits?: boolean;\r\n  /** Compliance requirements */\r\n  complianceRequirements?: string[];\r\n  /** Remediation overdue */\r\n  isRemediationOverdue?: boolean;\r\n  /** Requires immediate attention */\r\n  requiresImmediateAttention?: boolean;\r\n  /** Pagination */\r\n  pagination?: {\r\n    page: number;\r\n    limit: number;\r\n    sortBy?: string;\r\n    sortOrder?: 'asc' | 'desc';\r\n  };\r\n}\r\n\r\n/**\r\n * Vulnerability Statistics\r\n */\r\nexport interface VulnerabilityStatistics {\r\n  /** Total vulnerabilities */\r\n  total: number;\r\n  /** Status distribution */\r\n  statusDistribution: Record<VulnerabilityStatus, number>;\r\n  /** Severity distribution */\r\n  severityDistribution: Record<ThreatSeverity, number>;\r\n  /** Risk level distribution */\r\n  riskLevelDistribution: Record<'low' | 'medium' | 'high' | 'critical', number>;\r\n  /** Average risk score */\r\n  averageRiskScore: number;\r\n  /** Average CVSS score */\r\n  averageCVSSScore: number;\r\n  /** Remediation metrics */\r\n  remediationMetrics: {\r\n    averageRemediationTime: number;\r\n    slaComplianceRate: number;\r\n    overdueCount: number;\r\n    completedCount: number;\r\n  };\r\n  /** Exploitation metrics */\r\n  exploitationMetrics: {\r\n    activelyExploited: number;\r\n    publicExploitsAvailable: number;\r\n    weaponizedExploits: number;\r\n    zeroDay: number;\r\n  };\r\n  /** Asset impact */\r\n  assetImpact: {\r\n    criticalAssetsAffected: number;\r\n    externallyExposed: number;\r\n    totalAssetsAffected: number;\r\n  };\r\n  /** Trends */\r\n  trends: {\r\n    daily: Array<{ date: string; count: number; avgRiskScore: number }>;\r\n    weekly: Array<{ week: string; count: number; avgRiskScore: number }>;\r\n    monthly: Array<{ month: string; count: number; avgRiskScore: number }>;\r\n  };\r\n}\r\n\r\n/**\r\n * Vulnerability Aggregation\r\n */\r\nexport interface VulnerabilityAggregation {\r\n  /** Group by field */\r\n  groupBy: 'severity' | 'status' | 'category' | 'type' | 'assetCriticality' | 'exposure' | 'assignedTo';\r\n  /** Aggregation results */\r\n  results: Array<{\r\n    key: string;\r\n    count: number;\r\n    averageRiskScore: number;\r\n    highestRiskScore: number;\r\n    criticalCount: number;\r\n    overdueCount: number;\r\n  }>;\r\n}\r\n\r\n/**\r\n * Vulnerability Repository Interface\r\n * \r\n * Defines the contract for vulnerability data persistence and retrieval.\r\n * Supports complex querying, aggregation, and analytics for vulnerability management.\r\n * \r\n * Key responsibilities:\r\n * - Vulnerability CRUD operations\r\n * - Complex search and filtering\r\n * - Risk-based prioritization\r\n * - Remediation tracking\r\n * - Compliance reporting\r\n * - Performance analytics\r\n */\r\nexport interface VulnerabilityRepository extends BaseRepository<Vulnerability> {\r\n  /**\r\n   * Find vulnerabilities by search criteria\r\n   */\r\n  findByCriteria(criteria: VulnerabilitySearchCriteria): Promise<{\r\n    vulnerabilities: Vulnerability[];\r\n    total: number;\r\n    page: number;\r\n    limit: number;\r\n  }>;\r\n\r\n  /**\r\n   * Find vulnerability by CVE ID\r\n   */\r\n  findByCveId(cveId: string): Promise<Vulnerability | null>;\r\n\r\n  /**\r\n   * Find vulnerabilities by affected asset\r\n   */\r\n  findByAffectedAsset(assetId: string): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find vulnerabilities by status\r\n   */\r\n  findByStatus(status: VulnerabilityStatus): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find vulnerabilities by severity\r\n   */\r\n  findBySeverity(severity: ThreatSeverity): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find critical vulnerabilities requiring immediate attention\r\n   */\r\n  findCriticalVulnerabilities(): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find vulnerabilities with active exploitation\r\n   */\r\n  findActivelyExploited(): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find vulnerabilities with public exploits\r\n   */\r\n  findWithPublicExploits(): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find overdue vulnerabilities\r\n   */\r\n  findOverdueVulnerabilities(): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find vulnerabilities by risk score range\r\n   */\r\n  findByRiskScoreRange(minScore: number, maxScore: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find vulnerabilities by CVSS score range\r\n   */\r\n  findByCVSSScoreRange(minScore: number, maxScore: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find vulnerabilities affecting critical assets\r\n   */\r\n  findAffectingCriticalAssets(): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find externally exposed vulnerabilities\r\n   */\r\n  findExternallyExposed(): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find vulnerabilities by assigned analyst\r\n   */\r\n  findByAssignedAnalyst(analystId: string): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find vulnerabilities by tags\r\n   */\r\n  findByTags(tags: string[]): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find vulnerabilities discovered in date range\r\n   */\r\n  findByDiscoveryDateRange(from: Date, to: Date): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find vulnerabilities with remediation due in date range\r\n   */\r\n  findByRemediationDueDate(from: Date, to: Date): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find vulnerabilities by compliance requirements\r\n   */\r\n  findByComplianceRequirements(requirements: string[]): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Get vulnerability statistics\r\n   */\r\n  getStatistics(dateRange?: { from: Date; to: Date }): Promise<VulnerabilityStatistics>;\r\n\r\n  /**\r\n   * Get vulnerability aggregations\r\n   */\r\n  getAggregations(\r\n    groupBy: 'severity' | 'status' | 'category' | 'type' | 'assetCriticality' | 'exposure' | 'assignedTo',\r\n    criteria?: Partial<VulnerabilitySearchCriteria>\r\n  ): Promise<VulnerabilityAggregation>;\r\n\r\n  /**\r\n   * Get top vulnerabilities by risk score\r\n   */\r\n  getTopVulnerabilitiesByRisk(limit: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Get vulnerability trends\r\n   */\r\n  getTrends(\r\n    period: 'daily' | 'weekly' | 'monthly',\r\n    dateRange: { from: Date; to: Date }\r\n  ): Promise<Array<{\r\n    date: string;\r\n    discovered: number;\r\n    remediated: number;\r\n    overdue: number;\r\n    averageRiskScore: number;\r\n  }>>;\r\n\r\n  /**\r\n   * Get remediation performance metrics\r\n   */\r\n  getRemediationMetrics(dateRange?: { from: Date; to: Date }): Promise<{\r\n    averageRemediationTime: number;\r\n    slaComplianceRate: number;\r\n    remediationsByPriority: Record<string, number>;\r\n    remediationsByAnalyst: Record<string, number>;\r\n    overdueByPriority: Record<string, number>;\r\n  }>;\r\n\r\n  /**\r\n   * Get asset vulnerability summary\r\n   */\r\n  getAssetVulnerabilitySummary(assetId: string): Promise<{\r\n    totalVulnerabilities: number;\r\n    criticalVulnerabilities: number;\r\n    highRiskVulnerabilities: number;\r\n    activelyExploited: number;\r\n    overdue: number;\r\n    averageRiskScore: number;\r\n    remediationProgress: number;\r\n  }>;\r\n\r\n  /**\r\n   * Get vulnerability exposure analysis\r\n   */\r\n  getExposureAnalysis(): Promise<{\r\n    externallyExposed: number;\r\n    internalOnly: number;\r\n    cloudExposed: number;\r\n    dmzExposed: number;\r\n    criticalAssetsExposed: number;\r\n    riskByExposure: Record<string, number>;\r\n  }>;\r\n\r\n  /**\r\n   * Get compliance vulnerability report\r\n   */\r\n  getComplianceReport(regulation: string): Promise<{\r\n    totalVulnerabilities: number;\r\n    criticalVulnerabilities: number;\r\n    overdueVulnerabilities: number;\r\n    reportingRequired: number;\r\n    potentialFines: number;\r\n    complianceRate: number;\r\n    remediationTimeline: Array<{\r\n      priority: string;\r\n      count: number;\r\n      averageAge: number;\r\n      slaTarget: number;\r\n    }>;\r\n  }>;\r\n\r\n  /**\r\n   * Bulk update vulnerability status\r\n   */\r\n  bulkUpdateStatus(\r\n    vulnerabilityIds: string[],\r\n    status: VulnerabilityStatus,\r\n    reason: string,\r\n    updatedBy: string\r\n  ): Promise<number>;\r\n\r\n  /**\r\n   * Bulk assign vulnerabilities\r\n   */\r\n  bulkAssign(\r\n    vulnerabilityIds: string[],\r\n    assignedTo: string,\r\n    assignedBy: string\r\n  ): Promise<number>;\r\n\r\n  /**\r\n   * Bulk add tags to vulnerabilities\r\n   */\r\n  bulkAddTags(\r\n    vulnerabilityIds: string[],\r\n    tags: string[],\r\n    addedBy: string\r\n  ): Promise<number>;\r\n\r\n  /**\r\n   * Search vulnerabilities with full-text search\r\n   */\r\n  searchFullText(\r\n    query: string,\r\n    filters?: Partial<VulnerabilitySearchCriteria>\r\n  ): Promise<{\r\n    vulnerabilities: Vulnerability[];\r\n    total: number;\r\n    highlights: Record<string, string[]>;\r\n  }>;\r\n\r\n  /**\r\n   * Get similar vulnerabilities\r\n   */\r\n  findSimilarVulnerabilities(\r\n    vulnerability: Vulnerability,\r\n    similarity: 'cvss' | 'category' | 'assets' | 'indicators'\r\n  ): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Get vulnerability dependencies\r\n   */\r\n  getVulnerabilityDependencies(vulnerabilityId: string): Promise<{\r\n    dependsOn: Vulnerability[];\r\n    dependents: Vulnerability[];\r\n    relatedThreats: string[];\r\n    relatedIncidents: string[];\r\n  }>;\r\n\r\n  /**\r\n   * Archive old vulnerabilities\r\n   */\r\n  archiveOldVulnerabilities(\r\n    olderThan: Date,\r\n    statuses: VulnerabilityStatus[]\r\n  ): Promise<number>;\r\n\r\n  /**\r\n   * Get vulnerability health metrics\r\n   */\r\n  getHealthMetrics(): Promise<{\r\n    discoveryRate: number;\r\n    remediationRate: number;\r\n    backlogSize: number;\r\n    averageAge: number;\r\n    slaBreaches: number;\r\n    criticalBacklog: number;\r\n    analystWorkload: Record<string, number>;\r\n  }>;\r\n\r\n  /**\r\n   * Export vulnerabilities for reporting\r\n   */\r\n  exportVulnerabilities(\r\n    criteria: VulnerabilitySearchCriteria,\r\n    format: 'csv' | 'json' | 'pdf'\r\n  ): Promise<{\r\n    data: Buffer | string;\r\n    filename: string;\r\n    contentType: string;\r\n  }>;\r\n\r\n  /**\r\n   * Get vulnerability forecast\r\n   */\r\n  getForecast(\r\n    period: 'weekly' | 'monthly' | 'quarterly'\r\n  ): Promise<{\r\n    expectedDiscoveries: number;\r\n    expectedRemediations: number;\r\n    riskTrend: 'increasing' | 'stable' | 'decreasing';\r\n    recommendations: string[];\r\n  }>;\r\n\r\n  /**\r\n   * Validate vulnerability data integrity\r\n   */\r\n  validateDataIntegrity(): Promise<{\r\n    isValid: boolean;\r\n    issues: Array<{\r\n      type: string;\r\n      description: string;\r\n      affectedRecords: number;\r\n      severity: 'low' | 'medium' | 'high' | 'critical';\r\n    }>;\r\n  }>;\r\n}\r\n"], "version": 3}