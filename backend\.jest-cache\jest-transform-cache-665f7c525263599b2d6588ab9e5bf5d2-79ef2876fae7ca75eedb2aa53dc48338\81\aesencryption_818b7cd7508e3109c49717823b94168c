b8fb369a9b3d5b1ce8c5e1ac33f0ade5
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AESEncryption = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const crypto = __importStar(require("crypto"));
let AESEncryption = class AESEncryption {
    constructor(configService) {
        this.configService = configService;
        this.algorithm = 'aes-256-gcm';
        this.keyLength = 32;
        this.ivLength = 16;
    }
    /**
     * Encrypt data using AES-256-GCM
     */
    async encrypt(data, key) {
        const encryptionKey = this.getEncryptionKey(key);
        const iv = crypto.randomBytes(this.ivLength);
        const cipher = crypto.createCipher(this.algorithm, encryptionKey);
        cipher.setAAD(Buffer.from('sentinel-aes', 'utf8'));
        let encrypted = cipher.update(data, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        const tag = cipher.getAuthTag();
        return {
            encryptedData: encrypted,
            iv: iv.toString('hex'),
            tag: tag.toString('hex'),
            algorithm: this.algorithm,
        };
    }
    /**
     * Decrypt data using AES-256-GCM
     */
    async decrypt(options) {
        const encryptionKey = this.getEncryptionKey(options.key);
        const ivBuffer = Buffer.from(options.iv, 'hex');
        const tagBuffer = Buffer.from(options.tag, 'hex');
        const decipher = crypto.createDecipher(this.algorithm, encryptionKey);
        decipher.setAAD(Buffer.from('sentinel-aes', 'utf8'));
        decipher.setAuthTag(tagBuffer);
        let decrypted = decipher.update(options.encryptedData, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        return decrypted;
    }
    /**
     * Generate a new AES key
     */
    generateKey() {
        return crypto.randomBytes(this.keyLength).toString('hex');
    }
    /**
     * Derive key from password using PBKDF2
     */
    deriveKeyFromPassword(password, salt, iterations = 100000) {
        return crypto.pbkdf2Sync(password, salt, iterations, this.keyLength, 'sha256').toString('hex');
    }
    /**
     * Generate salt for key derivation
     */
    generateSalt() {
        return crypto.randomBytes(16).toString('hex');
    }
    getEncryptionKey(key) {
        const encryptionKey = key || this.configService.get('AES_ENCRYPTION_KEY');
        if (!encryptionKey) {
            throw new Error('AES encryption key not found in configuration');
        }
        return encryptionKey;
    }
    /**
     * Validate encryption key format
     */
    validateKey(key) {
        try {
            const keyBuffer = Buffer.from(key, 'hex');
            return keyBuffer.length === this.keyLength;
        }
        catch {
            return false;
        }
    }
};
exports.AESEncryption = AESEncryption;
exports.AESEncryption = AESEncryption = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], AESEncryption);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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