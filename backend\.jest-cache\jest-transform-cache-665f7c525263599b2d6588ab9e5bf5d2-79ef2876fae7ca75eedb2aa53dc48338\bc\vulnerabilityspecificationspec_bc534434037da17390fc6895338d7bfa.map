{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\__tests__\\vulnerability.specification.spec.ts", "mappings": ";;AAAA,gFA6BwC;AACxC,4FAAmJ;AACnJ,2EAAkE;AAClE,6EAAoE;AACpE,2GAA0F;AAE1F,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;IAC1C,IAAI,kBAAwC,CAAC;IAC7C,IAAI,aAAqC,CAAC;IAC1C,IAAI,qBAAoC,CAAC;IACzC,IAAI,iBAAgC,CAAC;IACrC,IAAI,mBAAkC,CAAC;IACvC,IAAI,gBAA+B,CAAC;IAEpC,UAAU,CAAC,GAAG,EAAE;QACd,kBAAkB,GAAG,CAAC;gBACpB,OAAO,EAAE,SAAS;gBAClB,SAAS,EAAE,eAAe;gBAC1B,SAAS,EAAE,QAAQ;gBACnB,WAAW,EAAE,UAAU;gBACvB,kBAAkB,EAAE,CAAC;wBACnB,IAAI,EAAE,oBAAoB;wBAC1B,OAAO,EAAE,QAAQ;wBACjB,IAAI,EAAE,UAAU;qBACjB,CAAC;gBACF,QAAQ,EAAE,UAAU;gBACpB,cAAc,EAAE,CAAC,yBAAyB,CAAC;aAC5C,CAAC,CAAC;QAEH,6DAA6D;QAC7D,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAC9B,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY;QAE1D,aAAa,GAAG;YACd,MAAM,EAAE,gBAAgB;YACxB,MAAM,EAAE,gBAAgB;YACxB,YAAY,EAAE,UAAU;YACxB,YAAY,EAAE,eAAe;YAC7B,OAAO,EAAE,8CAA8C;SACxD,CAAC;QAEF,wDAAwD;QACxD,qBAAqB,GAAG,oCAAa,CAAC,MAAM,CAC1C,4BAA4B,EAC5B,qCAAqC,EACrC,qCAAc,CAAC,QAAQ,EACvB,uBAAuB,EACvB,iBAAiB,EACjB,kBAAkB,EAClB,aAAa,EACb;YACE,KAAK,EAAE,eAAe;YACtB,UAAU,EAAE,CAAC,mCAAS,CAAC,UAAU,CAAC,GAAG,EAAE,8CAA8C,CAAC,CAAC;YACvF,UAAU,EAAE,uCAAe,CAAC,IAAI;YAChC,IAAI,EAAE,CAAC,UAAU,EAAE,KAAK,EAAE,UAAU,CAAC;SACtC,CACF,CAAC;QAEF,iBAAiB,GAAG,oCAAa,CAAC,MAAM,CACtC,6BAA6B,EAC7B,2CAA2C,EAC3C,qCAAc,CAAC,IAAI,EACnB,WAAW,EACX,eAAe,EACf,CAAC;gBACC,GAAG,kBAAkB,CAAC,CAAC,CAAC;gBACxB,WAAW,EAAE,MAAM;gBACnB,QAAQ,EAAE,UAAU;aACrB,CAAC,EACF,aAAa,EACb;YACE,KAAK,EAAE,eAAe;YACtB,UAAU,EAAE,CAAC,mCAAS,CAAC,UAAU,CAAC,GAAG,EAAE,8CAA8C,CAAC,CAAC;YACvF,UAAU,EAAE,uCAAe,CAAC,IAAI;YAChC,IAAI,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC;SAC5B,CACF,CAAC;QAEF,mBAAmB,GAAG,oCAAa,CAAC,MAAM,CACxC,0BAA0B,EAC1B,oCAAoC,EACpC,qCAAc,CAAC,MAAM,EACrB,WAAW,EACX,KAAK,EACL,CAAC;gBACC,GAAG,kBAAkB,CAAC,CAAC,CAAC;gBACxB,WAAW,EAAE,QAAQ;gBACrB,QAAQ,EAAE,UAAU;aACrB,CAAC,EACF,aAAa,EACb;YACE,UAAU,EAAE,CAAC,mCAAS,CAAC,UAAU,CAAC,GAAG,EAAE,8CAA8C,CAAC,CAAC;YACvF,UAAU,EAAE,uCAAe,CAAC,MAAM;YAClC,IAAI,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC;SACxB,CACF,CAAC;QAEF,gBAAgB,GAAG,oCAAa,CAAC,MAAM,CACrC,4BAA4B,EAC5B,sCAAsC,EACtC,qCAAc,CAAC,GAAG,EAClB,wBAAwB,EACxB,WAAW,EACX,CAAC;gBACC,GAAG,kBAAkB,CAAC,CAAC,CAAC;gBACxB,WAAW,EAAE,KAAK;gBAClB,QAAQ,EAAE,UAAU;aACrB,CAAC,EACF,aAAa,EACb;YACE,UAAU,EAAE,CAAC,mCAAS,CAAC,UAAU,CAAC,GAAG,EAAE,8CAA8C,CAAC,CAAC;YACvF,UAAU,EAAE,uCAAe,CAAC,GAAG;YAC/B,IAAI,EAAE,CAAC,KAAK,EAAE,iBAAiB,CAAC;SACjC,CACF,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE;QAClD,IAAI,IAAwC,CAAC;QAE7C,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,IAAI,gEAAkC,EAAE,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wCAAwC,EAAE,GAAG,EAAE;QACtD,IAAI,IAA4C,CAAC;QAEjD,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,IAAI,oEAAsC,EAAE,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAChD,IAAI,IAAsC,CAAC;QAE3C,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,IAAI,8DAAgC,EAAE,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE7D,8BAA8B;YAC9B,qBAAqB,CAAC,YAAY,CAAC,0CAAmB,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;YACrF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE9D,kCAAkC;YAClC,qBAAqB,CAAC,YAAY,CAAC,0CAAmB,CAAC,cAAc,EAAE,0BAA0B,CAAC,CAAC;YACnG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,qEAAqE,CAAC,CAAC;QAC5G,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sCAAsC,EAAE,GAAG,EAAE;QACpD,IAAI,IAA0C,CAAC;QAE/C,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,IAAI,kEAAoC,EAAE,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE9D,qBAAqB,CAAC,YAAY,CAAC,0CAAmB,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;YACrF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE7D,qBAAqB,CAAC,YAAY,CAAC,0CAAmB,CAAC,QAAQ,EAAE,mBAAmB,CAAC,CAAC;YACtF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE7D,qBAAqB,CAAC,YAAY,CAAC,0CAAmB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YACzE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE;QAClD,IAAI,IAAwC,CAAC;QAE7C,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,IAAI,gEAAkC,CAAC,EAAE,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,uCAAuC;YACrG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,UAAU,GAAG,IAAI,gEAAkC,CAAC,EAAE,CAAC,CAAC;YAC9D,MAAM,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0CAA0C,EAAE,GAAG,EAAE;QACxD,IAAI,IAA8C,CAAC;QAEnD,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,IAAI,sEAAwC,EAAE,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,kBAAkB;YAChF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,kBAAkB;YAC5E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,oBAAoB;YACjF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAiB;QAC7E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAChD,IAAI,IAAsC,CAAC;QAE3C,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,IAAI,8DAAgC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,0CAA0C;YAC1C,MAAM,mBAAmB,GAAG,oCAAa,CAAC,MAAM,CAC9C,sBAAsB,EACtB,mCAAmC,EACnC,qCAAc,CAAC,MAAM,EACrB,SAAS,EACT,SAAS,EACT,kBAAkB,EAClB;gBACE,GAAG,aAAa;gBAChB,YAAY,EAAE,IAAI,IAAI,EAAE,EAAE,QAAQ;aACnC,CACF,CAAC;YAEF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,0BAA0B;QAC1F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC/C,IAAI,IAAqC,CAAC;QAE1C,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,IAAI,6DAA+B,CAAC,EAAE,CAAC,CAAC,CAAC,qBAAqB;QACvE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,8BAA8B;YAC9B,MAAM,gBAAgB,GAAG,oCAAa,CAAC,MAAM,CAC3C,mBAAmB,EACnB,mBAAmB,EACnB,qCAAc,CAAC,MAAM,EACrB,SAAS,EACT,SAAS,EACT,kBAAkB,EAClB;gBACE,GAAG,aAAa;gBAChB,YAAY,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC,EAAE,WAAW;aAC5D,CACF,CAAC;YAEF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,IAAI,GAAG,IAAI,2DAA6B,EAAE,CAAC;YAEjD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,oBAAoB;YAClF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY;QAC3E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,IAAI,GAAG,IAAI,2DAA6B,CAAC,eAAe,CAAC,CAAC;YAEhE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,gBAAgB;QAC7E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mCAAmC,EAAE,GAAG,EAAE;QACjD,IAAI,IAAuC,CAAC;QAE5C,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,IAAI,+DAAiC,EAAE,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY;YACxE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,aAAa;YAE5E,yCAAyC;YACzC,MAAM,WAAW,GAAG,oCAAa,CAAC,MAAM,CACtC,UAAU,EACV,wBAAwB,EACxB,qCAAc,CAAC,QAAQ,EACvB,UAAU,EACV,SAAS,EACT,kBAAkB,EAClB,aAAa,EACb;gBACE,KAAK,EAAE,eAAe;gBACtB,IAAI,EAAE,CAAC,UAAU,CAAC;aACnB,CACF,CAAC;YAEF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAmB;QACzE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6CAA6C,EAAE,GAAG,EAAE;QAC3D,IAAI,IAAiD,CAAC;QAEtD,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,IAAI,yEAA2C,EAAE,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,gDAAgD;YAChD,MAAM,aAAa,GAAG,oCAAa,CAAC,MAAM,CACxC,yBAAyB,EACzB,kCAAkC,EAClC,qCAAc,CAAC,QAAQ,EACvB,cAAc,EACd,QAAQ,EACR,kBAAkB,EAClB,aAAa,EACb;gBACE,YAAY,EAAE;oBACZ,MAAM,EAAE,qBAAqB;oBAC7B,UAAU,EAAE,KAAK;oBACjB,iBAAiB,EAAE,EAAE;oBACrB,aAAa,EAAE,EAAE;oBACjB,aAAa,EAAE,EAAE;iBAClB;aACF,CACF,CAAC;YAEF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6CAA6C,EAAE,GAAG,EAAE;QAC3D,IAAI,IAAiD,CAAC;QAEtD,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,IAAI,yEAA2C,EAAE,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,oBAAoB;YAClF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,oBAAoB;QACjF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yCAAyC,EAAE,GAAG,EAAE;QACvD,IAAI,IAA6C,CAAC;QAElD,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,IAAI,qEAAuC,EAAE,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAiB;YAC/E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,yBAAyB;QACtF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE;QAClD,IAAI,IAAwC,CAAC;QAE7C,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,IAAI,gEAAkC,CAAC,GAAG,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW;YACzE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW;YACrE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW;YACxE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW;QACvE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC/C,IAAI,IAAqC,CAAC;QAE1C,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,IAAI,6DAA+B,EAAE,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,8DAA8D;YAC9D,MAAM,eAAe,GAAG,oCAAa,CAAC,MAAM,CAC1C,4BAA4B,EAC5B,4BAA4B,EAC5B,qCAAc,CAAC,QAAQ,EACvB,UAAU,EACV,KAAK,EACL,kBAAkB,EAClB;gBACE,GAAG,aAAa;gBAChB,YAAY,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC,EAAE,WAAW;aAC5D,CACF,CAAC;YAEF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,+DAA+D;QAChI,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yCAAyC,EAAE,GAAG,EAAE;QACvD,IAAI,IAA6C,CAAC;QAElD,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,IAAI,qEAAuC,EAAE,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE;QAClD,IAAI,IAAwC,CAAC;QAE7C,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,IAAI,gEAAkC,CAAC,CAAC,qCAAc,CAAC,QAAQ,EAAE,qCAAc,CAAC,IAAI,CAAC,CAAC,CAAC;QAChG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAChD,IAAI,IAAsC,CAAC;QAE3C,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,IAAI,8DAAgC,CAAC,CAAC,0CAAmB,CAAC,UAAU,EAAE,0CAAmB,CAAC,SAAS,CAAC,CAAC,CAAC;QAC/G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,oBAAoB;YAElF,qBAAqB,CAAC,YAAY,CAAC,0CAAmB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;YAC/E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE7D,qBAAqB,CAAC,YAAY,CAAC,0CAAmB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAC5E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE;QAClD,IAAI,IAAwC,CAAC;QAE7C,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,IAAI,gEAAkC,CAAC,CAAC,WAAW,EAAE,uBAAuB,CAAC,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,wBAAwB;YACtF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY;YACtE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,yBAAyB;QACrF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;QAC9C,IAAI,IAAoC,CAAC;QAEzC,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,IAAI,4DAA8B,CAAC,CAAC,iBAAiB,EAAE,eAAe,CAAC,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,kBAAkB;YAChF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB;YAC1E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM;QACrE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;YACxE,MAAM,IAAI,GAAG,IAAI,2DAA6B,CAAC,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC;YAE5E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,qBAAqB;YACnF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAiB;YAC3E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,kBAAkB;QACjF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,IAAI,GAAG,IAAI,2DAA6B,CAAC,CAAC,UAAU,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC;YAE1E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB;YAC9E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,yBAAyB;QACtF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0CAA0C,EAAE,GAAG,EAAE;QACxD,IAAI,IAA8C,CAAC;QAEnD,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,IAAI,sEAAwC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,kBAAkB;YAChF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAiB;QAC7E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,WAAW,GAAG,IAAI,sEAAwC,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;YAChF,MAAM,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,WAAW,GAAG,IAAI,sEAAwC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YAChF,MAAM,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QACtF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0CAA0C,EAAE,GAAG,EAAE;QACxD,IAAI,IAA8C,CAAC;QAEnD,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,IAAI,sEAAwC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW;YACzE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW;YACrE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW;QAC1E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE;QAClD,IAAI,IAAwC,CAAC;QAE7C,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,IAAI,gEAAkC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,gBAAgB;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,gFAAgF;YAChF,MAAM,YAAY,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACtD,MAAM,QAAQ,GAAG,oCAAa,CAAC,MAAM,CACnC,oBAAoB,EACpB,oCAAoC,EACpC,qCAAc,CAAC,MAAM,EACrB,SAAS,EACT,MAAM,EACN,kBAAkB,EAClB;gBACE,GAAG,aAAa;gBAChB,YAAY,EAAE,YAAY;aAC3B,CACF,CAAC;YAEF,oEAAoE;YACpE,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAClD,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;YAE5D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,oCAAoC;YAErF,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,WAAW,EAAE,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC/C,IAAI,IAAqC,CAAC;QAE1C,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,IAAI,6DAA+B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU;YAExE,4CAA4C;YAC5C,MAAM,cAAc,GAAG,oCAAa,CAAC,MAAM,CACzC,2BAA2B,EAC3B,yBAAyB,EACzB,qCAAc,CAAC,MAAM,EACrB,SAAS,EACT,OAAO,EACP,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,kBAAkB,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,EACzE,aAAa,CACd,CAAC;YAEF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW;QACpE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,IAAI,IAAkC,CAAC;QAEvC,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,IAAI,0DAA4B,CAAC,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iEAAiE,EAAE,GAAG,EAAE;YACzE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAiB;YAE/E,MAAM,UAAU,GAAG,oCAAa,CAAC,MAAM,CACrC,kBAAkB,EAClB,mCAAmC,EACnC,qCAAc,CAAC,MAAM,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,EAClB;gBACE,GAAG,aAAa;gBAChB,MAAM,EAAE,gBAAgB;aACzB,CACF,CAAC;YAEF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAElD,MAAM,eAAe,GAAG,oCAAa,CAAC,MAAM,CAC1C,wBAAwB,EACxB,oCAAoC,EACpC,qCAAc,CAAC,IAAI,EACnB,SAAS,EACT,OAAO,EACP,kBAAkB,EAClB;gBACE,GAAG,aAAa;gBAChB,MAAM,EAAE,qBAAqB;aAC9B,CACF,CAAC;YAEF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mCAAmC,EAAE,GAAG,EAAE;QACjD,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,IAAI,GAAG,+DAAiC,CAAC,MAAM,EAAE;iBACpD,QAAQ,EAAE;iBACV,MAAM,EAAE;iBACR,iBAAiB,EAAE;iBACnB,KAAK,EAAE,CAAC;YAEX,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,eAAe;YAC1E,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,IAAI,GAAG,+DAAiC,CAAC,MAAM,EAAE;iBACpD,QAAQ,EAAE;iBACV,YAAY,EAAE;iBACd,WAAW,EAAE,CAAC;YAEjB,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5D,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,IAAI,GAAG,+DAAiC,CAAC,MAAM,EAAE;iBACpD,QAAQ,EAAE;iBACV,KAAK,EAAE,CAAC;YAEX,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,gEAAkC,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,CAAC,GAAG,EAAE;gBACV,+DAAiC,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC;YACrD,CAAC,CAAC,CAAC,OAAO,CAAC,0CAA0C,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,OAAO,GAAG,+DAAiC,CAAC,MAAM,EAAE;iBACvD,QAAQ,EAAE;iBACV,YAAY,EAAE;iBACd,MAAM,EAAE;iBACR,UAAU,EAAE;iBACZ,QAAQ,CAAC,EAAE,CAAC;iBACZ,cAAc,EAAE;iBAChB,MAAM,CAAC,CAAC,CAAC;iBACT,KAAK,CAAC,EAAE,CAAC;iBACT,OAAO,CAAC,eAAe,CAAC;iBACxB,OAAO,EAAE;iBACT,iBAAiB,EAAE;iBACnB,iBAAiB,EAAE;iBACnB,qBAAqB,EAAE;iBACvB,QAAQ,CAAC,GAAG,CAAC;iBACb,kBAAkB,EAAE;iBACpB,0BAA0B,EAAE;iBAC5B,cAAc,CAAC,qCAAc,CAAC,QAAQ,EAAE,qCAAc,CAAC,IAAI,CAAC;iBAC5D,YAAY,CAAC,0CAAmB,CAAC,UAAU,CAAC;iBAC5C,cAAc,CAAC,WAAW,CAAC;iBAC3B,SAAS,CAAC,eAAe,CAAC;iBAC1B,QAAQ,CAAC,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC;iBAC7B,cAAc,CAAC,EAAE,EAAE,GAAG,CAAC;iBACvB,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC;iBACzB,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC;iBACf,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC;iBACxB,YAAY,CAAC,gBAAgB,CAAC,CAAC;YAElC,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,+DAAiC,CAAC,CAAC;YAElE,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;YAC7B,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;QAC7B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,YAAY,GAAG,IAAI,gEAAkC,EAAE,CAAC;YAC9D,MAAM,UAAU,GAAG,IAAI,8DAAgC,EAAE,CAAC;YAC1D,MAAM,YAAY,GAAG,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAElD,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAErE,qBAAqB,CAAC,YAAY,CAAC,0CAAmB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAC5E,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,YAAY,GAAG,IAAI,gEAAkC,EAAE,CAAC;YAC9D,MAAM,QAAQ,GAAG,IAAI,oEAAsC,EAAE,CAAC;YAC9D,MAAM,YAAY,GAAG,YAAY,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;YAE/C,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrE,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjE,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,YAAY,GAAG,IAAI,gEAAkC,EAAE,CAAC;YAC9D,MAAM,eAAe,GAAG,YAAY,CAAC,GAAG,EAAE,CAAC;YAE3C,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzE,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\__tests__\\vulnerability.specification.spec.ts"], "sourcesContent": ["import {\r\n  VulnerabilitySpecification,\r\n  CriticalVulnerabilitySpecification,\r\n  HighSeverityVulnerabilitySpecification,\r\n  ActiveVulnerabilitySpecification,\r\n  RemediatedVulnerabilitySpecification,\r\n  HighRiskVulnerabilitySpecification,\r\n  HighConfidenceVulnerabilitySpecification,\r\n  RecentVulnerabilitySpecification,\r\n  StaleVulnerabilitySpecification,\r\n  CVEVulnerabilitySpecification,\r\n  ZeroDayVulnerabilitySpecification,\r\n  ActivelyExploitedVulnerabilitySpecification,\r\n  ExternallyExposedVulnerabilitySpecification,\r\n  CriticalAssetVulnerabilitySpecification,\r\n  HighCVSSVulnerabilitySpecification,\r\n  OverdueRemediationSpecification,\r\n  RequiresImmediateAttentionSpecification,\r\n  VulnerabilitySeveritySpecification,\r\n  VulnerabilityStatusSpecification,\r\n  VulnerabilityCategorySpecification,\r\n  VulnerabilityTypeSpecification,\r\n  VulnerabilityTagSpecification,\r\n  VulnerabilityRiskScoreRangeSpecification,\r\n  VulnerabilityCVSSScoreRangeSpecification,\r\n  VulnerabilityAgeRangeSpecification,\r\n  AffectedAssetCountSpecification,\r\n  DiscoveryMethodSpecification,\r\n  VulnerabilitySpecificationBuilder,\r\n} from '../vulnerability.specification';\r\nimport { Vulnerability, VulnerabilityAsset, VulnerabilityDiscovery, VulnerabilityStatus } from '../../entities/vulnerability/vulnerability.entity';\r\nimport { ThreatSeverity } from '../../enums/threat-severity.enum';\r\nimport { ConfidenceLevel } from '../../enums/confidence-level.enum';\r\nimport { CVSSScore } from '../../value-objects/threat-indicators/cvss-score.value-object';\r\n\r\ndescribe('VulnerabilitySpecification', () => {\r\n  let mockAffectedAssets: VulnerabilityAsset[];\r\n  let mockDiscovery: VulnerabilityDiscovery;\r\n  let criticalVulnerability: Vulnerability;\r\n  let highVulnerability: Vulnerability;\r\n  let mediumVulnerability: Vulnerability;\r\n  let lowVulnerability: Vulnerability;\r\n\r\n  beforeEach(() => {\r\n    mockAffectedAssets = [{\r\n      assetId: 'asset-1',\r\n      assetName: 'Web Server 01',\r\n      assetType: 'server',\r\n      criticality: 'critical',\r\n      affectedComponents: [{\r\n        name: 'Apache HTTP Server',\r\n        version: '2.4.41',\r\n        type: 'software',\r\n      }],\r\n      exposure: 'external',\r\n      businessImpact: ['customer_facing_service'],\r\n    }];\r\n\r\n    // Use a recent date to avoid issues with stale/overdue tests\r\n    const recentDate = new Date();\r\n    recentDate.setDate(recentDate.getDate() - 1); // 1 day ago\r\n\r\n    mockDiscovery = {\r\n      method: 'automated_scan',\r\n      source: 'Nessus Scanner',\r\n      discoveredAt: recentDate,\r\n      discoveredBy: 'Security Team',\r\n      details: 'Discovered during routine vulnerability scan',\r\n    };\r\n\r\n    // Create test vulnerabilities with different severities\r\n    criticalVulnerability = Vulnerability.create(\r\n      'Critical RCE Vulnerability',\r\n      'Remote code execution vulnerability',\r\n      ThreatSeverity.CRITICAL,\r\n      'remote_code_execution',\r\n      'buffer_overflow',\r\n      mockAffectedAssets,\r\n      mockDiscovery,\r\n      {\r\n        cveId: 'CVE-2024-1234',\r\n        cvssScores: [CVSSScore.createV3_1(9.8, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H')],\r\n        confidence: ConfidenceLevel.HIGH,\r\n        tags: ['critical', 'rce', 'external'],\r\n      }\r\n    );\r\n\r\n    highVulnerability = Vulnerability.create(\r\n      'High Severity SQL Injection',\r\n      'SQL injection vulnerability in login form',\r\n      ThreatSeverity.HIGH,\r\n      'injection',\r\n      'sql_injection',\r\n      [{\r\n        ...mockAffectedAssets[0],\r\n        criticality: 'high',\r\n        exposure: 'internal',\r\n      }],\r\n      mockDiscovery,\r\n      {\r\n        cveId: 'CVE-2024-5678',\r\n        cvssScores: [CVSSScore.createV3_1(8.1, 'CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:N')],\r\n        confidence: ConfidenceLevel.HIGH,\r\n        tags: ['high', 'injection'],\r\n      }\r\n    );\r\n\r\n    mediumVulnerability = Vulnerability.create(\r\n      'Medium XSS Vulnerability',\r\n      'Cross-site scripting vulnerability',\r\n      ThreatSeverity.MEDIUM,\r\n      'injection',\r\n      'xss',\r\n      [{\r\n        ...mockAffectedAssets[0],\r\n        criticality: 'medium',\r\n        exposure: 'internal',\r\n      }],\r\n      mockDiscovery,\r\n      {\r\n        cvssScores: [CVSSScore.createV3_1(6.1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N')],\r\n        confidence: ConfidenceLevel.MEDIUM,\r\n        tags: ['medium', 'xss'],\r\n      }\r\n    );\r\n\r\n    lowVulnerability = Vulnerability.create(\r\n      'Low Information Disclosure',\r\n      'Information disclosure vulnerability',\r\n      ThreatSeverity.LOW,\r\n      'information_disclosure',\r\n      'info_leak',\r\n      [{\r\n        ...mockAffectedAssets[0],\r\n        criticality: 'low',\r\n        exposure: 'internal',\r\n      }],\r\n      mockDiscovery,\r\n      {\r\n        cvssScores: [CVSSScore.createV3_1(3.7, 'CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:L/I:N/A:N')],\r\n        confidence: ConfidenceLevel.LOW,\r\n        tags: ['low', 'info-disclosure'],\r\n      }\r\n    );\r\n  });\r\n\r\n  describe('CriticalVulnerabilitySpecification', () => {\r\n    let spec: CriticalVulnerabilitySpecification;\r\n\r\n    beforeEach(() => {\r\n      spec = new CriticalVulnerabilitySpecification();\r\n    });\r\n\r\n    it('should identify critical vulnerabilities', () => {\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true);\r\n      expect(spec.isSatisfiedBy(highVulnerability)).toBe(false);\r\n      expect(spec.isSatisfiedBy(mediumVulnerability)).toBe(false);\r\n      expect(spec.isSatisfiedBy(lowVulnerability)).toBe(false);\r\n    });\r\n\r\n    it('should provide correct description', () => {\r\n      expect(spec.getDescription()).toBe('Vulnerability has critical severity');\r\n    });\r\n  });\r\n\r\n  describe('HighSeverityVulnerabilitySpecification', () => {\r\n    let spec: HighSeverityVulnerabilitySpecification;\r\n\r\n    beforeEach(() => {\r\n      spec = new HighSeverityVulnerabilitySpecification();\r\n    });\r\n\r\n    it('should identify high and critical vulnerabilities', () => {\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true);\r\n      expect(spec.isSatisfiedBy(highVulnerability)).toBe(true);\r\n      expect(spec.isSatisfiedBy(mediumVulnerability)).toBe(false);\r\n      expect(spec.isSatisfiedBy(lowVulnerability)).toBe(false);\r\n    });\r\n\r\n    it('should provide correct description', () => {\r\n      expect(spec.getDescription()).toBe('Vulnerability has high or critical severity');\r\n    });\r\n  });\r\n\r\n  describe('ActiveVulnerabilitySpecification', () => {\r\n    let spec: ActiveVulnerabilitySpecification;\r\n\r\n    beforeEach(() => {\r\n      spec = new ActiveVulnerabilitySpecification();\r\n    });\r\n\r\n    it('should identify active vulnerabilities', () => {\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true);\r\n      \r\n      // Change status to remediated\r\n      criticalVulnerability.changeStatus(VulnerabilityStatus.REMEDIATED, 'Fixed by patch');\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(false);\r\n      \r\n      // Change status to false positive\r\n      criticalVulnerability.changeStatus(VulnerabilityStatus.FALSE_POSITIVE, 'Not a real vulnerability');\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(false);\r\n    });\r\n\r\n    it('should provide correct description', () => {\r\n      expect(spec.getDescription()).toBe('Vulnerability is active (not remediated, closed, or false positive)');\r\n    });\r\n  });\r\n\r\n  describe('RemediatedVulnerabilitySpecification', () => {\r\n    let spec: RemediatedVulnerabilitySpecification;\r\n\r\n    beforeEach(() => {\r\n      spec = new RemediatedVulnerabilitySpecification();\r\n    });\r\n\r\n    it('should identify remediated vulnerabilities', () => {\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(false);\r\n      \r\n      criticalVulnerability.changeStatus(VulnerabilityStatus.REMEDIATED, 'Fixed by patch');\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true);\r\n      \r\n      criticalVulnerability.changeStatus(VulnerabilityStatus.VERIFIED, 'Verified as fixed');\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true);\r\n      \r\n      criticalVulnerability.changeStatus(VulnerabilityStatus.CLOSED, 'Closed');\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('HighRiskVulnerabilitySpecification', () => {\r\n    let spec: HighRiskVulnerabilitySpecification;\r\n\r\n    beforeEach(() => {\r\n      spec = new HighRiskVulnerabilitySpecification(80);\r\n    });\r\n\r\n    it('should identify high risk vulnerabilities', () => {\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // Critical should have high risk score\r\n      expect(spec.isSatisfiedBy(lowVulnerability)).toBe(false);\r\n    });\r\n\r\n    it('should use custom risk threshold', () => {\r\n      const customSpec = new HighRiskVulnerabilitySpecification(50);\r\n      expect(customSpec.getDescription()).toBe('Vulnerability has high risk score (>= 50)');\r\n    });\r\n  });\r\n\r\n  describe('HighConfidenceVulnerabilitySpecification', () => {\r\n    let spec: HighConfidenceVulnerabilitySpecification;\r\n\r\n    beforeEach(() => {\r\n      spec = new HighConfidenceVulnerabilitySpecification();\r\n    });\r\n\r\n    it('should identify high confidence vulnerabilities', () => {\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // HIGH confidence\r\n      expect(spec.isSatisfiedBy(highVulnerability)).toBe(true); // HIGH confidence\r\n      expect(spec.isSatisfiedBy(mediumVulnerability)).toBe(false); // MEDIUM confidence\r\n      expect(spec.isSatisfiedBy(lowVulnerability)).toBe(false); // LOW confidence\r\n    });\r\n  });\r\n\r\n  describe('RecentVulnerabilitySpecification', () => {\r\n    let spec: RecentVulnerabilitySpecification;\r\n\r\n    beforeEach(() => {\r\n      spec = new RecentVulnerabilitySpecification(7); // Within 7 days\r\n    });\r\n\r\n    it('should identify recent vulnerabilities', () => {\r\n      // Create a vulnerability discovered today\r\n      const recentVulnerability = Vulnerability.create(\r\n        'Recent Vulnerability',\r\n        'Recently discovered vulnerability',\r\n        ThreatSeverity.MEDIUM,\r\n        'general',\r\n        'unknown',\r\n        mockAffectedAssets,\r\n        {\r\n          ...mockDiscovery,\r\n          discoveredAt: new Date(), // Today\r\n        }\r\n      );\r\n\r\n      expect(spec.isSatisfiedBy(recentVulnerability)).toBe(true);\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // Also recent (1 day ago)\r\n    });\r\n\r\n    it('should provide correct description', () => {\r\n      expect(spec.getDescription()).toBe('Vulnerability was discovered within 7 days');\r\n    });\r\n  });\r\n\r\n  describe('StaleVulnerabilitySpecification', () => {\r\n    let spec: StaleVulnerabilitySpecification;\r\n\r\n    beforeEach(() => {\r\n      spec = new StaleVulnerabilitySpecification(30); // Older than 30 days\r\n    });\r\n\r\n    it('should identify stale vulnerabilities', () => {\r\n      // Create an old vulnerability\r\n      const oldVulnerability = Vulnerability.create(\r\n        'Old Vulnerability',\r\n        'Old vulnerability',\r\n        ThreatSeverity.MEDIUM,\r\n        'general',\r\n        'unknown',\r\n        mockAffectedAssets,\r\n        {\r\n          ...mockDiscovery,\r\n          discoveredAt: new Date('2023-01-01T00:00:00Z'), // Very old\r\n        }\r\n      );\r\n\r\n      expect(spec.isSatisfiedBy(oldVulnerability)).toBe(true);\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('CVEVulnerabilitySpecification', () => {\r\n    it('should identify vulnerabilities with CVE IDs', () => {\r\n      const spec = new CVEVulnerabilitySpecification();\r\n      \r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // Has CVE-2024-1234\r\n      expect(spec.isSatisfiedBy(mediumVulnerability)).toBe(false); // No CVE ID\r\n    });\r\n\r\n    it('should identify specific CVE', () => {\r\n      const spec = new CVEVulnerabilitySpecification('CVE-2024-1234');\r\n      \r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true);\r\n      expect(spec.isSatisfiedBy(highVulnerability)).toBe(false); // Different CVE\r\n    });\r\n  });\r\n\r\n  describe('ZeroDayVulnerabilitySpecification', () => {\r\n    let spec: ZeroDayVulnerabilitySpecification;\r\n\r\n    beforeEach(() => {\r\n      spec = new ZeroDayVulnerabilitySpecification();\r\n    });\r\n\r\n    it('should identify zero-day vulnerabilities', () => {\r\n      expect(spec.isSatisfiedBy(mediumVulnerability)).toBe(true); // No CVE ID\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(false); // Has CVE ID\r\n      \r\n      // Create vulnerability with zero-day tag\r\n      const zeroDayVuln = Vulnerability.create(\r\n        'Zero Day',\r\n        'Zero day vulnerability',\r\n        ThreatSeverity.CRITICAL,\r\n        'zero-day',\r\n        'unknown',\r\n        mockAffectedAssets,\r\n        mockDiscovery,\r\n        {\r\n          cveId: 'CVE-2024-9999',\r\n          tags: ['zero-day'],\r\n        }\r\n      );\r\n      \r\n      expect(spec.isSatisfiedBy(zeroDayVuln)).toBe(true); // Has zero-day tag\r\n    });\r\n  });\r\n\r\n  describe('ActivelyExploitedVulnerabilitySpecification', () => {\r\n    let spec: ActivelyExploitedVulnerabilitySpecification;\r\n\r\n    beforeEach(() => {\r\n      spec = new ActivelyExploitedVulnerabilitySpecification();\r\n    });\r\n\r\n    it('should identify actively exploited vulnerabilities', () => {\r\n      // Create vulnerability with active exploitation\r\n      const exploitedVuln = Vulnerability.create(\r\n        'Exploited Vulnerability',\r\n        'Actively exploited vulnerability',\r\n        ThreatSeverity.CRITICAL,\r\n        'exploitation',\r\n        'active',\r\n        mockAffectedAssets,\r\n        mockDiscovery,\r\n        {\r\n          exploitation: {\r\n            status: 'active_exploitation',\r\n            difficulty: 'low',\r\n            availableExploits: [],\r\n            attackVectors: [],\r\n            prerequisites: [],\r\n          },\r\n        }\r\n      );\r\n\r\n      expect(spec.isSatisfiedBy(exploitedVuln)).toBe(true);\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('ExternallyExposedVulnerabilitySpecification', () => {\r\n    let spec: ExternallyExposedVulnerabilitySpecification;\r\n\r\n    beforeEach(() => {\r\n      spec = new ExternallyExposedVulnerabilitySpecification();\r\n    });\r\n\r\n    it('should identify externally exposed vulnerabilities', () => {\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // External exposure\r\n      expect(spec.isSatisfiedBy(highVulnerability)).toBe(false); // Internal exposure\r\n    });\r\n  });\r\n\r\n  describe('CriticalAssetVulnerabilitySpecification', () => {\r\n    let spec: CriticalAssetVulnerabilitySpecification;\r\n\r\n    beforeEach(() => {\r\n      spec = new CriticalAssetVulnerabilitySpecification();\r\n    });\r\n\r\n    it('should identify vulnerabilities affecting critical assets', () => {\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // Critical asset\r\n      expect(spec.isSatisfiedBy(highVulnerability)).toBe(false); // High criticality asset\r\n    });\r\n  });\r\n\r\n  describe('HighCVSSVulnerabilitySpecification', () => {\r\n    let spec: HighCVSSVulnerabilitySpecification;\r\n\r\n    beforeEach(() => {\r\n      spec = new HighCVSSVulnerabilitySpecification(7.0);\r\n    });\r\n\r\n    it('should identify vulnerabilities with high CVSS scores', () => {\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // CVSS 9.8\r\n      expect(spec.isSatisfiedBy(highVulnerability)).toBe(true); // CVSS 8.1\r\n      expect(spec.isSatisfiedBy(mediumVulnerability)).toBe(false); // CVSS 6.1\r\n      expect(spec.isSatisfiedBy(lowVulnerability)).toBe(false); // CVSS 3.7\r\n    });\r\n  });\r\n\r\n  describe('OverdueRemediationSpecification', () => {\r\n    let spec: OverdueRemediationSpecification;\r\n\r\n    beforeEach(() => {\r\n      spec = new OverdueRemediationSpecification();\r\n    });\r\n\r\n    it('should identify overdue vulnerabilities', () => {\r\n      // Create an old critical vulnerability that should be overdue\r\n      const oldCriticalVuln = Vulnerability.create(\r\n        'Old Critical Vulnerability',\r\n        'Old critical vulnerability',\r\n        ThreatSeverity.CRITICAL,\r\n        'critical',\r\n        'old',\r\n        mockAffectedAssets,\r\n        {\r\n          ...mockDiscovery,\r\n          discoveredAt: new Date('2023-01-01T00:00:00Z'), // Very old\r\n        }\r\n      );\r\n\r\n      expect(spec.isSatisfiedBy(oldCriticalVuln)).toBe(true);\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(false); // Recent critical vulnerability (1 day old) is not yet overdue\r\n    });\r\n  });\r\n\r\n  describe('RequiresImmediateAttentionSpecification', () => {\r\n    let spec: RequiresImmediateAttentionSpecification;\r\n\r\n    beforeEach(() => {\r\n      spec = new RequiresImmediateAttentionSpecification();\r\n    });\r\n\r\n    it('should identify vulnerabilities requiring immediate attention', () => {\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true);\r\n      expect(spec.isSatisfiedBy(lowVulnerability)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('VulnerabilitySeveritySpecification', () => {\r\n    let spec: VulnerabilitySeveritySpecification;\r\n\r\n    beforeEach(() => {\r\n      spec = new VulnerabilitySeveritySpecification([ThreatSeverity.CRITICAL, ThreatSeverity.HIGH]);\r\n    });\r\n\r\n    it('should identify vulnerabilities with specified severities', () => {\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true);\r\n      expect(spec.isSatisfiedBy(highVulnerability)).toBe(true);\r\n      expect(spec.isSatisfiedBy(mediumVulnerability)).toBe(false);\r\n      expect(spec.isSatisfiedBy(lowVulnerability)).toBe(false);\r\n    });\r\n\r\n    it('should provide correct description', () => {\r\n      expect(spec.getDescription()).toBe('Vulnerability severity is one of: critical, high');\r\n    });\r\n  });\r\n\r\n  describe('VulnerabilityStatusSpecification', () => {\r\n    let spec: VulnerabilityStatusSpecification;\r\n\r\n    beforeEach(() => {\r\n      spec = new VulnerabilityStatusSpecification([VulnerabilityStatus.DISCOVERED, VulnerabilityStatus.CONFIRMED]);\r\n    });\r\n\r\n    it('should identify vulnerabilities with specified statuses', () => {\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // DISCOVERED status\r\n      \r\n      criticalVulnerability.changeStatus(VulnerabilityStatus.CONFIRMED, 'Confirmed');\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true);\r\n      \r\n      criticalVulnerability.changeStatus(VulnerabilityStatus.REMEDIATED, 'Fixed');\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('VulnerabilityCategorySpecification', () => {\r\n    let spec: VulnerabilityCategorySpecification;\r\n\r\n    beforeEach(() => {\r\n      spec = new VulnerabilityCategorySpecification(['injection', 'remote_code_execution']);\r\n    });\r\n\r\n    it('should identify vulnerabilities with specified categories', () => {\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // remote_code_execution\r\n      expect(spec.isSatisfiedBy(highVulnerability)).toBe(true); // injection\r\n      expect(spec.isSatisfiedBy(lowVulnerability)).toBe(false); // information_disclosure\r\n    });\r\n  });\r\n\r\n  describe('VulnerabilityTypeSpecification', () => {\r\n    let spec: VulnerabilityTypeSpecification;\r\n\r\n    beforeEach(() => {\r\n      spec = new VulnerabilityTypeSpecification(['buffer_overflow', 'sql_injection']);\r\n    });\r\n\r\n    it('should identify vulnerabilities with specified types', () => {\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // buffer_overflow\r\n      expect(spec.isSatisfiedBy(highVulnerability)).toBe(true); // sql_injection\r\n      expect(spec.isSatisfiedBy(mediumVulnerability)).toBe(false); // xss\r\n    });\r\n  });\r\n\r\n  describe('VulnerabilityTagSpecification', () => {\r\n    it('should identify vulnerabilities with any of the specified tags', () => {\r\n      const spec = new VulnerabilityTagSpecification(['critical', 'high'], false);\r\n      \r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // Has 'critical' tag\r\n      expect(spec.isSatisfiedBy(highVulnerability)).toBe(true); // Has 'high' tag\r\n      expect(spec.isSatisfiedBy(mediumVulnerability)).toBe(false); // Has neither tag\r\n    });\r\n\r\n    it('should identify vulnerabilities with all specified tags', () => {\r\n      const spec = new VulnerabilityTagSpecification(['critical', 'rce'], true);\r\n      \r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // Has both tags\r\n      expect(spec.isSatisfiedBy(highVulnerability)).toBe(false); // Missing 'critical' tag\r\n    });\r\n  });\r\n\r\n  describe('VulnerabilityRiskScoreRangeSpecification', () => {\r\n    let spec: VulnerabilityRiskScoreRangeSpecification;\r\n\r\n    beforeEach(() => {\r\n      spec = new VulnerabilityRiskScoreRangeSpecification(70, 100);\r\n    });\r\n\r\n    it('should identify vulnerabilities within risk score range', () => {\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // High risk score\r\n      expect(spec.isSatisfiedBy(lowVulnerability)).toBe(false); // Low risk score\r\n    });\r\n\r\n    it('should handle min-only range', () => {\r\n      const minOnlySpec = new VulnerabilityRiskScoreRangeSpecification(80, undefined);\r\n      expect(minOnlySpec.getDescription()).toBe('Vulnerability risk score is at least 80');\r\n    });\r\n\r\n    it('should handle max-only range', () => {\r\n      const maxOnlySpec = new VulnerabilityRiskScoreRangeSpecification(undefined, 50);\r\n      expect(maxOnlySpec.getDescription()).toBe('Vulnerability risk score is at most 50');\r\n    });\r\n  });\r\n\r\n  describe('VulnerabilityCVSSScoreRangeSpecification', () => {\r\n    let spec: VulnerabilityCVSSScoreRangeSpecification;\r\n\r\n    beforeEach(() => {\r\n      spec = new VulnerabilityCVSSScoreRangeSpecification(7.0, 10.0);\r\n    });\r\n\r\n    it('should identify vulnerabilities within CVSS score range', () => {\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // CVSS 9.8\r\n      expect(spec.isSatisfiedBy(highVulnerability)).toBe(true); // CVSS 8.1\r\n      expect(spec.isSatisfiedBy(mediumVulnerability)).toBe(false); // CVSS 6.1\r\n    });\r\n  });\r\n\r\n  describe('VulnerabilityAgeRangeSpecification', () => {\r\n    let spec: VulnerabilityAgeRangeSpecification;\r\n\r\n    beforeEach(() => {\r\n      spec = new VulnerabilityAgeRangeSpecification(1, 30); // 1-30 days old\r\n    });\r\n\r\n    it('should identify vulnerabilities within age range', () => {\r\n      // Create a vulnerability with a specific discovery date for predictable testing\r\n      const specificDate = new Date('2024-01-15T00:00:00Z');\r\n      const testVuln = Vulnerability.create(\r\n        'Test Vulnerability',\r\n        'Test vulnerability for age testing',\r\n        ThreatSeverity.MEDIUM,\r\n        'general',\r\n        'test',\r\n        mockAffectedAssets,\r\n        {\r\n          ...mockDiscovery,\r\n          discoveredAt: specificDate,\r\n        }\r\n      );\r\n\r\n      // Mock current date to make calculations predictable (5 days later)\r\n      const mockDate = new Date('2024-01-20T00:00:00Z');\r\n      jest.spyOn(Date, 'now').mockReturnValue(mockDate.getTime());\r\n\r\n      expect(spec.isSatisfiedBy(testVuln)).toBe(true); // 5 days old, within 1-30 day range\r\n      \r\n      Date.now = jest.fn().mockRestore();\r\n    });\r\n  });\r\n\r\n  describe('AffectedAssetCountSpecification', () => {\r\n    let spec: AffectedAssetCountSpecification;\r\n\r\n    beforeEach(() => {\r\n      spec = new AffectedAssetCountSpecification(1, 5);\r\n    });\r\n\r\n    it('should identify vulnerabilities with specified asset count', () => {\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // 1 asset\r\n      \r\n      // Create vulnerability with multiple assets\r\n      const multiAssetVuln = Vulnerability.create(\r\n        'Multi Asset Vulnerability',\r\n        'Affects multiple assets',\r\n        ThreatSeverity.MEDIUM,\r\n        'general',\r\n        'multi',\r\n        [mockAffectedAssets[0], { ...mockAffectedAssets[0], assetId: 'asset-2' }],\r\n        mockDiscovery\r\n      );\r\n      \r\n      expect(spec.isSatisfiedBy(multiAssetVuln)).toBe(true); // 2 assets\r\n    });\r\n  });\r\n\r\n  describe('DiscoveryMethodSpecification', () => {\r\n    let spec: DiscoveryMethodSpecification;\r\n\r\n    beforeEach(() => {\r\n      spec = new DiscoveryMethodSpecification(['automated_scan', 'manual_testing']);\r\n    });\r\n\r\n    it('should identify vulnerabilities discovered by specified methods', () => {\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // automated_scan\r\n      \r\n      const manualVuln = Vulnerability.create(\r\n        'Manual Discovery',\r\n        'Manually discovered vulnerability',\r\n        ThreatSeverity.MEDIUM,\r\n        'general',\r\n        'manual',\r\n        mockAffectedAssets,\r\n        {\r\n          ...mockDiscovery,\r\n          method: 'manual_testing',\r\n        }\r\n      );\r\n      \r\n      expect(spec.isSatisfiedBy(manualVuln)).toBe(true);\r\n      \r\n      const threatIntelVuln = Vulnerability.create(\r\n        'Threat Intel Discovery',\r\n        'Discovered via threat intelligence',\r\n        ThreatSeverity.HIGH,\r\n        'general',\r\n        'intel',\r\n        mockAffectedAssets,\r\n        {\r\n          ...mockDiscovery,\r\n          method: 'threat_intelligence',\r\n        }\r\n      );\r\n      \r\n      expect(spec.isSatisfiedBy(threatIntelVuln)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('VulnerabilitySpecificationBuilder', () => {\r\n    it('should build complex specifications with AND logic', () => {\r\n      const spec = VulnerabilitySpecificationBuilder.create()\r\n        .critical()\r\n        .active()\r\n        .externallyExposed()\r\n        .build();\r\n\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true);\r\n      expect(spec.isSatisfiedBy(highVulnerability)).toBe(false); // Not critical\r\n      expect(spec.getDescription()).toContain('AND');\r\n    });\r\n\r\n    it('should build complex specifications with OR logic', () => {\r\n      const spec = VulnerabilitySpecificationBuilder.create()\r\n        .critical()\r\n        .highSeverity()\r\n        .buildWithOr();\r\n\r\n      expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true);\r\n      expect(spec.isSatisfiedBy(highVulnerability)).toBe(true);\r\n      expect(spec.isSatisfiedBy(mediumVulnerability)).toBe(false);\r\n      expect(spec.getDescription()).toContain('OR');\r\n    });\r\n\r\n    it('should handle single specification', () => {\r\n      const spec = VulnerabilitySpecificationBuilder.create()\r\n        .critical()\r\n        .build();\r\n\r\n      expect(spec).toBeInstanceOf(CriticalVulnerabilitySpecification);\r\n    });\r\n\r\n    it('should throw error for empty builder', () => {\r\n      expect(() => {\r\n        VulnerabilitySpecificationBuilder.create().build();\r\n      }).toThrow('At least one specification must be added');\r\n    });\r\n\r\n    it('should support all builder methods', () => {\r\n      const builder = VulnerabilitySpecificationBuilder.create()\r\n        .critical()\r\n        .highSeverity()\r\n        .active()\r\n        .remediated()\r\n        .highRisk(80)\r\n        .highConfidence()\r\n        .recent(7)\r\n        .stale(90)\r\n        .withCVE('CVE-2024-1234')\r\n        .zeroDay()\r\n        .activelyExploited()\r\n        .externallyExposed()\r\n        .affectsCriticalAssets()\r\n        .highCVSS(7.0)\r\n        .overdueRemediation()\r\n        .requiresImmediateAttention()\r\n        .withSeverities(ThreatSeverity.CRITICAL, ThreatSeverity.HIGH)\r\n        .withStatuses(VulnerabilityStatus.DISCOVERED)\r\n        .withCategories('injection')\r\n        .withTypes('sql_injection')\r\n        .withTags(['critical'], false)\r\n        .riskScoreRange(70, 100)\r\n        .cvssScoreRange(7.0, 10.0)\r\n        .ageRange(1, 30)\r\n        .affectedAssetCount(1, 5)\r\n        .discoveredBy('automated_scan');\r\n\r\n      expect(builder).toBeInstanceOf(VulnerabilitySpecificationBuilder);\r\n      \r\n      const spec = builder.build();\r\n      expect(spec).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('specification combinations', () => {\r\n    it('should combine specifications with AND logic', () => {\r\n      const criticalSpec = new CriticalVulnerabilitySpecification();\r\n      const activeSpec = new ActiveVulnerabilitySpecification();\r\n      const combinedSpec = criticalSpec.and(activeSpec);\r\n\r\n      expect(combinedSpec.isSatisfiedBy(criticalVulnerability)).toBe(true);\r\n      \r\n      criticalVulnerability.changeStatus(VulnerabilityStatus.REMEDIATED, 'Fixed');\r\n      expect(combinedSpec.isSatisfiedBy(criticalVulnerability)).toBe(false);\r\n    });\r\n\r\n    it('should combine specifications with OR logic', () => {\r\n      const criticalSpec = new CriticalVulnerabilitySpecification();\r\n      const highSpec = new HighSeverityVulnerabilitySpecification();\r\n      const combinedSpec = criticalSpec.or(highSpec);\r\n\r\n      expect(combinedSpec.isSatisfiedBy(criticalVulnerability)).toBe(true);\r\n      expect(combinedSpec.isSatisfiedBy(highVulnerability)).toBe(true);\r\n      expect(combinedSpec.isSatisfiedBy(mediumVulnerability)).toBe(false);\r\n    });\r\n\r\n    it('should negate specifications', () => {\r\n      const criticalSpec = new CriticalVulnerabilitySpecification();\r\n      const notCriticalSpec = criticalSpec.not();\r\n\r\n      expect(notCriticalSpec.isSatisfiedBy(criticalVulnerability)).toBe(false);\r\n      expect(notCriticalSpec.isSatisfiedBy(highVulnerability)).toBe(true);\r\n    });\r\n  });\r\n});"], "version": 3}