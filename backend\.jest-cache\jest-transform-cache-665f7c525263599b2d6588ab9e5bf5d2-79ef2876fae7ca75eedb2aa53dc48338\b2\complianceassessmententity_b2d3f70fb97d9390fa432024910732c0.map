{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\compliance-audit\\domain\\entities\\compliance-assessment.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCAUiB;AACjB,+EAAoE;AACpE,uEAA4D;AAE5D;;;GAGG;AAOI,IAAM,oBAAoB,GAA1B,MAAM,oBAAoB;IAuT/B;;OAEG;IACH,IAAI,SAAS;QACX,IAAI,CAAC,IAAI,CAAC,kBAAkB;YAAE,OAAO,KAAK,CAAC;QAC3C,OAAO,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,OAAO,EAAE,aAAa,KAAK,WAAW,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QAC7D,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,IAAI,oBAAoB;QACtB,OAAO,IAAI,CAAC,OAAO,EAAE,oBAAoB,IAAI,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,IAAI,qBAAqB;QACvB,OAAO,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC;IACpF,CAAC;IAED;;OAEG;IACH,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAc;QAClB,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC;QAC5B,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QAExB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,IAAI,CAAC,WAAW,GAAG;gBACjB,QAAQ,EAAE,UAAU;gBACpB,WAAW,EAAE,iBAAiB;aAC/B,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,MAAc,EAAE,OAAY;QACnC,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;QAC1B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QAExB,iEAAiE;QACjE,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAI,CAAC,MAAc,EAAE,MAAc;QACjC,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QAExB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACrB,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,aAAa,GAAG,MAAM,CAAC;IACvC,CAAC;IAED;;OAEG;IACK,2BAA2B;QACjC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,CAAC,UAAU,EAAE,mBAAmB;YAAE,OAAO;QAE3E,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,UAAU,CAAC,mBAAmB,CAAC;QAC9E,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAE/C,QAAQ,SAAS,EAAE,CAAC;YAClB,KAAK,SAAS;gBACZ,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;gBAC3C,MAAM;YACR,KAAK,WAAW;gBACd,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;gBAC3C,MAAM;YACR,KAAK,eAAe;gBAClB,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;gBAC3C,MAAM;YACR,KAAK,UAAU;gBACb,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC;gBACjD,MAAM;QACV,CAAC;QAED,IAAI,CAAC,kBAAkB,GAAG,QAAQ,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,OAAY;QACrB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QACpB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YAC3B,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,EAAE,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;YACzB,EAAE,EAAE,WAAW,IAAI,CAAC,GAAG,EAAE,EAAE;YAC3B,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,QAAgB,EAAE,SAAiB,EAAE,MAAW;QAClE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,CAAC,OAAO,GAAG,EAAE,aAAa,EAAE,EAAE,EAAE,CAAC;QACvC,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;YAChC,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,EAAE,CAAC;QAClC,CAAC;QAED,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;QAC3E,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,GAAG;gBACP,QAAQ;gBACR,UAAU,EAAE,EAAE;gBACd,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,eAAe;gBACvB,cAAc,EAAE,EAAE;aACnB,CAAC;YACF,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1C,CAAC;QAED,MAAM,aAAa,GAAG,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,KAAK,SAAS,CAAC,CAAC;QACtF,IAAI,aAAa,IAAI,CAAC,EAAE,CAAC;YACvB,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC;QAChG,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,GAAG,MAAM,EAAE,CAAC,CAAC;QACvD,CAAC;QAED,2BAA2B;QAC3B,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,QAAgB;QAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;QAC/E,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,MAAM,iBAAiB,GAAG,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,MAAM,CAAC;QAC7F,MAAM,aAAa,GAAG,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,gBAAgB,CAAC,CAAC,MAAM,CAAC;QAE9F,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;YACtB,MAAM,CAAC,KAAK,GAAG,CAAC,iBAAiB,GAAG,aAAa,CAAC,GAAG,GAAG,CAAC;YAEzD,IAAI,MAAM,CAAC,KAAK,IAAI,EAAE,EAAE,CAAC;gBACvB,MAAM,CAAC,MAAM,GAAG,WAAW,CAAC;YAC9B,CAAC;iBAAM,IAAI,MAAM,CAAC,KAAK,IAAI,EAAE,EAAE,CAAC;gBAC9B,MAAM,CAAC,MAAM,GAAG,qBAAqB,CAAC;YACxC,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,MAAM,GAAG,eAAe,CAAC;YAClC,CAAC;QACH,CAAC;QAED,4BAA4B;QAC5B,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,uBAAuB;QAC7B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa;YAAE,OAAO;QAEzC,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC7F,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC;QAEtD,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;YACpB,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,UAAU,GAAG,WAAW,CAAC;YACrD,IAAI,CAAC,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;YAE9D,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;gBACpC,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,WAAW,CAAC;YAC3C,CAAC;iBAAM,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;gBAC3C,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,qBAAqB,CAAC;YACrD,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,eAAe,CAAC;YAC/C,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,gBAAgB,EAAE,IAAI,CAAC,qBAAqB;YAC5C,YAAY,EAAE,IAAI,CAAC,iBAAiB;YACpC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO;YACL,UAAU,EAAE,IAAI,CAAC,eAAe,EAAE;YAClC,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACrC,CAAC;IACJ,CAAC;CACF,CAAA;AAljBY,oDAAoB;AAE/B;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;gDACpB;AAMX;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;kDACX;AAMb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;yDACL;AASpB;IAJC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,eAAe,EAAE,uBAAuB,CAAC;KACxG,CAAC;;4DACkH;AAUpH;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,SAAS,EAAE,aAAa,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,CAAC;QACpE,OAAO,EAAE,SAAS;KACnB,CAAC;;oDACuE;AAMzE;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC;kDACtD,IAAI,oBAAJ,IAAI;4DAAC;AAMrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,sBAAsB,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACtE,IAAI,oBAAJ,IAAI;gEAAC;AAM1B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;;mDA+CxB;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDAwExC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;yDA+CxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sDA2CxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;kDACtC;AAMf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;uDAC3B;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uDAC1C;AAGnB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;uDAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;uDAAC;AAKhB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,iDAAmB,EAAE,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC;IACxE,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC;kDAC1B,iDAAmB,oBAAnB,iDAAmB;uDAAC;AAG/B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;yDAC3B;AAGpB;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,yCAAe,EAAE,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC;;wDACtC;+BArTnB,oBAAoB;IANhC,IAAA,gBAAM,EAAC,wBAAwB,CAAC;IAChC,IAAA,eAAK,EAAC,CAAC,aAAa,CAAC,CAAC;IACtB,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,gBAAgB,CAAC,CAAC;IACzB,IAAA,eAAK,EAAC,CAAC,gBAAgB,CAAC,CAAC;IACzB,IAAA,eAAK,EAAC,CAAC,oBAAoB,CAAC,CAAC;GACjB,oBAAoB,CAkjBhC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\compliance-audit\\domain\\entities\\compliance-assessment.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  ManyToOne,\r\n  JoinColumn,\r\n  OneToMany,\r\n} from 'typeorm';\r\nimport { ComplianceFramework } from './compliance-framework.entity';\r\nimport { PolicyViolation } from './policy-violation.entity';\r\n\r\n/**\r\n * Compliance Assessment entity\r\n * Represents assessments of compliance against specific frameworks\r\n */\r\n@Entity('compliance_assessments')\r\n@Index(['frameworkId'])\r\n@Index(['status'])\r\n@Index(['assessmentType'])\r\n@Index(['assessmentDate'])\r\n@Index(['nextAssessmentDate'])\r\nexport class ComplianceAssessment {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * Assessment name\r\n   */\r\n  @Column({ length: 255 })\r\n  name: string;\r\n\r\n  /**\r\n   * Assessment description\r\n   */\r\n  @Column({ type: 'text' })\r\n  description: string;\r\n\r\n  /**\r\n   * Assessment type\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['self_assessment', 'internal_audit', 'external_audit', 'certification', 'continuous_monitoring'],\r\n  })\r\n  assessmentType: 'self_assessment' | 'internal_audit' | 'external_audit' | 'certification' | 'continuous_monitoring';\r\n\r\n  /**\r\n   * Assessment status\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['planned', 'in_progress', 'completed', 'failed', 'cancelled'],\r\n    default: 'planned',\r\n  })\r\n  status: 'planned' | 'in_progress' | 'completed' | 'failed' | 'cancelled';\r\n\r\n  /**\r\n   * Assessment date\r\n   */\r\n  @Column({ name: 'assessment_date', type: 'timestamp with time zone' })\r\n  assessmentDate: Date;\r\n\r\n  /**\r\n   * Next assessment date\r\n   */\r\n  @Column({ name: 'next_assessment_date', type: 'timestamp with time zone', nullable: true })\r\n  nextAssessmentDate?: Date;\r\n\r\n  /**\r\n   * Assessment scope and configuration\r\n   */\r\n  @Column({ type: 'jsonb' })\r\n  scope: {\r\n    // Scope definition\r\n    description: string;\r\n    includedSystems?: string[];\r\n    excludedSystems?: string[];\r\n    includedProcesses?: string[];\r\n    excludedProcesses?: string[];\r\n    \r\n    // Assessment criteria\r\n    assessmentCriteria?: {\r\n      controlsToAssess?: string[];\r\n      domainsToAssess?: string[];\r\n      maturityLevelRequired?: number;\r\n      passingScore?: number;\r\n      evidenceRequirements?: string[];\r\n    };\r\n    \r\n    // Time boundaries\r\n    assessmentPeriod?: {\r\n      startDate: string;\r\n      endDate: string;\r\n      lookbackPeriod?: number; // days\r\n    };\r\n    \r\n    // Organizational scope\r\n    businessUnits?: string[];\r\n    locations?: string[];\r\n    departments?: string[];\r\n    \r\n    // Technical scope\r\n    applications?: string[];\r\n    infrastructure?: string[];\r\n    dataTypes?: string[];\r\n    \r\n    // Exclusions and limitations\r\n    exclusions?: Array<{\r\n      type: string;\r\n      description: string;\r\n      justification: string;\r\n    }>;\r\n    \r\n    limitations?: Array<{\r\n      type: string;\r\n      description: string;\r\n      impact: string;\r\n    }>;\r\n  };\r\n\r\n  /**\r\n   * Assessment results and findings\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  results?: {\r\n    // Overall results\r\n    overallScore?: number;\r\n    overallStatus?: 'compliant' | 'non_compliant' | 'partially_compliant';\r\n    compliancePercentage?: number;\r\n    \r\n    // Domain results\r\n    domainResults?: Array<{\r\n      domainId: string;\r\n      domainName: string;\r\n      score: number;\r\n      status: 'compliant' | 'non_compliant' | 'partially_compliant';\r\n      controlResults: Array<{\r\n        controlId: string;\r\n        controlName: string;\r\n        status: 'compliant' | 'non_compliant' | 'not_applicable' | 'not_tested';\r\n        score?: number;\r\n        maturityLevel?: number;\r\n        findings?: string[];\r\n        evidence?: string[];\r\n        recommendations?: string[];\r\n        riskLevel?: 'low' | 'medium' | 'high' | 'critical';\r\n      }>;\r\n    }>;\r\n    \r\n    // Key findings\r\n    findings?: Array<{\r\n      id: string;\r\n      type: 'gap' | 'weakness' | 'strength' | 'opportunity';\r\n      severity: 'low' | 'medium' | 'high' | 'critical';\r\n      title: string;\r\n      description: string;\r\n      affectedControls: string[];\r\n      riskRating: number;\r\n      businessImpact: string;\r\n      recommendations: string[];\r\n      priority: 'low' | 'medium' | 'high' | 'urgent';\r\n      estimatedEffort?: string;\r\n      targetDate?: string;\r\n    }>;\r\n    \r\n    // Risk assessment\r\n    riskAssessment?: {\r\n      overallRiskLevel: 'low' | 'medium' | 'high' | 'critical';\r\n      riskFactors: Array<{\r\n        factor: string;\r\n        impact: 'low' | 'medium' | 'high';\r\n        likelihood: 'low' | 'medium' | 'high';\r\n        riskLevel: 'low' | 'medium' | 'high' | 'critical';\r\n      }>;\r\n      mitigationStrategies?: string[];\r\n    };\r\n    \r\n    // Metrics and KPIs\r\n    metrics?: {\r\n      totalControlsAssessed: number;\r\n      compliantControls: number;\r\n      nonCompliantControls: number;\r\n      notApplicableControls: number;\r\n      notTestedControls: number;\r\n      averageMaturityLevel?: number;\r\n      improvementFromLastAssessment?: number;\r\n    };\r\n    \r\n    // Evidence summary\r\n    evidenceSummary?: {\r\n      totalEvidenceItems: number;\r\n      evidenceTypes: Record<string, number>;\r\n      evidenceQuality: 'poor' | 'fair' | 'good' | 'excellent';\r\n      evidenceGaps?: string[];\r\n    };\r\n  };\r\n\r\n  /**\r\n   * Assessment methodology and execution details\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  methodology?: {\r\n    // Assessment approach\r\n    approach: string;\r\n    methodology: string;\r\n    standards?: string[];\r\n    \r\n    // Assessment team\r\n    assessors?: Array<{\r\n      name: string;\r\n      role: string;\r\n      qualifications?: string[];\r\n      responsibilities?: string[];\r\n    }>;\r\n    \r\n    // Assessment activities\r\n    activities?: Array<{\r\n      activity: string;\r\n      description: string;\r\n      startDate: string;\r\n      endDate: string;\r\n      participants: string[];\r\n      outcomes?: string[];\r\n    }>;\r\n    \r\n    // Tools and techniques\r\n    tools?: Array<{\r\n      name: string;\r\n      type: string;\r\n      purpose: string;\r\n      version?: string;\r\n    }>;\r\n    \r\n    // Sampling methodology\r\n    sampling?: {\r\n      approach: string;\r\n      sampleSize: number;\r\n      selectionCriteria: string;\r\n      confidence: number;\r\n    };\r\n    \r\n    // Quality assurance\r\n    qualityAssurance?: {\r\n      reviewProcess: string;\r\n      reviewers: string[];\r\n      qualityChecks: string[];\r\n    };\r\n  };\r\n\r\n  /**\r\n   * Assessment metadata\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  metadata?: {\r\n    // Assessment context\r\n    businessContext?: string;\r\n    regulatoryContext?: string;\r\n    organizationalChanges?: string[];\r\n    \r\n    // Previous assessments\r\n    previousAssessmentId?: string;\r\n    comparisonWithPrevious?: {\r\n      improvementAreas: string[];\r\n      regressionAreas: string[];\r\n      overallTrend: 'improving' | 'stable' | 'declining';\r\n    };\r\n    \r\n    // External factors\r\n    externalFactors?: Array<{\r\n      factor: string;\r\n      impact: string;\r\n      mitigation?: string;\r\n    }>;\r\n    \r\n    // Lessons learned\r\n    lessonsLearned?: string[];\r\n    bestPractices?: string[];\r\n    \r\n    // Follow-up actions\r\n    followUpActions?: Array<{\r\n      action: string;\r\n      owner: string;\r\n      dueDate: string;\r\n      priority: 'low' | 'medium' | 'high' | 'urgent';\r\n      status: 'pending' | 'in_progress' | 'completed';\r\n    }>;\r\n    \r\n    // Certification information\r\n    certification?: {\r\n      certificationBody?: string;\r\n      certificateNumber?: string;\r\n      issueDate?: string;\r\n      expiryDate?: string;\r\n      scope?: string;\r\n    };\r\n  };\r\n\r\n  /**\r\n   * Assessment tags\r\n   */\r\n  @Column({ type: 'text', array: true, default: '{}' })\r\n  tags: string[];\r\n\r\n  /**\r\n   * User who created the assessment\r\n   */\r\n  @Column({ name: 'created_by', type: 'uuid' })\r\n  createdBy: string;\r\n\r\n  /**\r\n   * User who last updated the assessment\r\n   */\r\n  @Column({ name: 'updated_by', type: 'uuid', nullable: true })\r\n  updatedBy?: string;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  // Relationships\r\n  @ManyToOne(() => ComplianceFramework, framework => framework.assessments)\r\n  @JoinColumn({ name: 'framework_id' })\r\n  framework: ComplianceFramework;\r\n\r\n  @Column({ name: 'framework_id', type: 'uuid' })\r\n  frameworkId: string;\r\n\r\n  @OneToMany(() => PolicyViolation, violation => violation.assessment)\r\n  violations: PolicyViolation[];\r\n\r\n  /**\r\n   * Check if assessment is overdue\r\n   */\r\n  get isOverdue(): boolean {\r\n    if (!this.nextAssessmentDate) return false;\r\n    return new Date() > this.nextAssessmentDate;\r\n  }\r\n\r\n  /**\r\n   * Check if assessment is compliant\r\n   */\r\n  get isCompliant(): boolean {\r\n    return this.results?.overallStatus === 'compliant';\r\n  }\r\n\r\n  /**\r\n   * Get assessment age in days\r\n   */\r\n  get ageInDays(): number {\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - this.assessmentDate.getTime();\r\n    return Math.floor(diffMs / (1000 * 60 * 60 * 24));\r\n  }\r\n\r\n  /**\r\n   * Get compliance percentage\r\n   */\r\n  get compliancePercentage(): number {\r\n    return this.results?.compliancePercentage || 0;\r\n  }\r\n\r\n  /**\r\n   * Get critical findings count\r\n   */\r\n  get criticalFindingsCount(): number {\r\n    return this.results?.findings?.filter(f => f.severity === 'critical').length || 0;\r\n  }\r\n\r\n  /**\r\n   * Get high findings count\r\n   */\r\n  get highFindingsCount(): number {\r\n    return this.results?.findings?.filter(f => f.severity === 'high').length || 0;\r\n  }\r\n\r\n  /**\r\n   * Start assessment\r\n   */\r\n  start(userId: string): void {\r\n    this.status = 'in_progress';\r\n    this.updatedBy = userId;\r\n    \r\n    if (!this.methodology) {\r\n      this.methodology = {\r\n        approach: 'standard',\r\n        methodology: 'control_testing',\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Complete assessment\r\n   */\r\n  complete(userId: string, results: any): void {\r\n    this.status = 'completed';\r\n    this.results = results;\r\n    this.updatedBy = userId;\r\n    \r\n    // Calculate next assessment date based on framework requirements\r\n    this.calculateNextAssessmentDate();\r\n  }\r\n\r\n  /**\r\n   * Fail assessment\r\n   */\r\n  fail(userId: string, reason: string): void {\r\n    this.status = 'failed';\r\n    this.updatedBy = userId;\r\n    \r\n    if (!this.metadata) {\r\n      this.metadata = {};\r\n    }\r\n    \r\n    this.metadata.failureReason = reason;\r\n  }\r\n\r\n  /**\r\n   * Calculate next assessment date\r\n   */\r\n  private calculateNextAssessmentDate(): void {\r\n    if (!this.framework?.configuration.assessment?.assessmentFrequency) return;\r\n    \r\n    const frequency = this.framework.configuration.assessment.assessmentFrequency;\r\n    const nextDate = new Date(this.assessmentDate);\r\n    \r\n    switch (frequency) {\r\n      case 'monthly':\r\n        nextDate.setMonth(nextDate.getMonth() + 1);\r\n        break;\r\n      case 'quarterly':\r\n        nextDate.setMonth(nextDate.getMonth() + 3);\r\n        break;\r\n      case 'semi_annually':\r\n        nextDate.setMonth(nextDate.getMonth() + 6);\r\n        break;\r\n      case 'annually':\r\n        nextDate.setFullYear(nextDate.getFullYear() + 1);\r\n        break;\r\n    }\r\n    \r\n    this.nextAssessmentDate = nextDate;\r\n  }\r\n\r\n  /**\r\n   * Add finding\r\n   */\r\n  addFinding(finding: any): void {\r\n    if (!this.results) {\r\n      this.results = {};\r\n    }\r\n    \r\n    if (!this.results.findings) {\r\n      this.results.findings = [];\r\n    }\r\n    \r\n    this.results.findings.push({\r\n      id: `finding-${Date.now()}`,\r\n      ...finding,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Update control result\r\n   */\r\n  updateControlResult(domainId: string, controlId: string, result: any): void {\r\n    if (!this.results) {\r\n      this.results = { domainResults: [] };\r\n    }\r\n    \r\n    if (!this.results.domainResults) {\r\n      this.results.domainResults = [];\r\n    }\r\n    \r\n    let domain = this.results.domainResults.find(d => d.domainId === domainId);\r\n    if (!domain) {\r\n      domain = {\r\n        domainId,\r\n        domainName: '',\r\n        score: 0,\r\n        status: 'non_compliant',\r\n        controlResults: [],\r\n      };\r\n      this.results.domainResults.push(domain);\r\n    }\r\n    \r\n    const existingIndex = domain.controlResults.findIndex(c => c.controlId === controlId);\r\n    if (existingIndex >= 0) {\r\n      domain.controlResults[existingIndex] = { ...domain.controlResults[existingIndex], ...result };\r\n    } else {\r\n      domain.controlResults.push({ controlId, ...result });\r\n    }\r\n    \r\n    // Recalculate domain score\r\n    this.recalculateDomainScore(domainId);\r\n  }\r\n\r\n  /**\r\n   * Recalculate domain score\r\n   */\r\n  private recalculateDomainScore(domainId: string): void {\r\n    const domain = this.results?.domainResults?.find(d => d.domainId === domainId);\r\n    if (!domain) return;\r\n    \r\n    const compliantControls = domain.controlResults.filter(c => c.status === 'compliant').length;\r\n    const totalControls = domain.controlResults.filter(c => c.status !== 'not_applicable').length;\r\n    \r\n    if (totalControls > 0) {\r\n      domain.score = (compliantControls / totalControls) * 100;\r\n      \r\n      if (domain.score >= 95) {\r\n        domain.status = 'compliant';\r\n      } else if (domain.score >= 70) {\r\n        domain.status = 'partially_compliant';\r\n      } else {\r\n        domain.status = 'non_compliant';\r\n      }\r\n    }\r\n    \r\n    // Recalculate overall score\r\n    this.recalculateOverallScore();\r\n  }\r\n\r\n  /**\r\n   * Recalculate overall assessment score\r\n   */\r\n  private recalculateOverallScore(): void {\r\n    if (!this.results?.domainResults) return;\r\n    \r\n    const totalScore = this.results.domainResults.reduce((sum, domain) => sum + domain.score, 0);\r\n    const domainCount = this.results.domainResults.length;\r\n    \r\n    if (domainCount > 0) {\r\n      this.results.overallScore = totalScore / domainCount;\r\n      this.results.compliancePercentage = this.results.overallScore;\r\n      \r\n      if (this.results.overallScore >= 95) {\r\n        this.results.overallStatus = 'compliant';\r\n      } else if (this.results.overallScore >= 70) {\r\n        this.results.overallStatus = 'partially_compliant';\r\n      } else {\r\n        this.results.overallStatus = 'non_compliant';\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Generate assessment summary\r\n   */\r\n  generateSummary(): any {\r\n    return {\r\n      id: this.id,\r\n      name: this.name,\r\n      frameworkId: this.frameworkId,\r\n      assessmentType: this.assessmentType,\r\n      status: this.status,\r\n      assessmentDate: this.assessmentDate,\r\n      nextAssessmentDate: this.nextAssessmentDate,\r\n      isOverdue: this.isOverdue,\r\n      isCompliant: this.isCompliant,\r\n      compliancePercentage: this.compliancePercentage,\r\n      criticalFindings: this.criticalFindingsCount,\r\n      highFindings: this.highFindingsCount,\r\n      ageInDays: this.ageInDays,\r\n      createdAt: this.createdAt,\r\n      updatedAt: this.updatedAt,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Export assessment for reporting\r\n   */\r\n  exportForReporting(): any {\r\n    return {\r\n      assessment: this.generateSummary(),\r\n      scope: this.scope,\r\n      results: this.results,\r\n      methodology: this.methodology,\r\n      metadata: this.metadata,\r\n      exportedAt: new Date().toISOString(),\r\n    };\r\n  }\r\n}\r\n"], "version": 3}