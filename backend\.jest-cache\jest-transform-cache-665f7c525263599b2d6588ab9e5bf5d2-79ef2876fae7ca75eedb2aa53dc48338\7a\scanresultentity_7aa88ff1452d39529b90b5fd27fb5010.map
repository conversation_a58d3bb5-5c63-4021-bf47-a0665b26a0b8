{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\domain\\entities\\scan-result.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCASiB;AACjB,iDAAuC;AACvC,iEAAuD;AACvD,2EAAgE;AAEhE;;;GAGG;AAQI,IAAM,UAAU,GAAhB,MAAM,UAAU;IAuPrB;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,QAAQ,KAAK,UAAU,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,IAAI,cAAc;QAChB,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,CAAC,OAAO,EAAE,gBAAgB,EAAE,eAAe,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC5D,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,IAAI,sBAAsB;QACxB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;QAC3D,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;QAC/B,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,MAAM,cAAc,GAAG;YACrB,IAAI,EAAE,GAAG;YACT,GAAG,EAAE,GAAG;YACR,MAAM,EAAE,GAAG;YACX,IAAI,EAAE,GAAG;YACT,QAAQ,EAAE,GAAG;SACd,CAAC;QAEF,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEhE,wCAAwC;QACxC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,SAAS,IAAI,GAAG,CAAC;QACnB,CAAC;QAED,0BAA0B;QAC1B,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC;QACvC,CAAC;QAED,4CAA4C;QAC5C,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACjC,SAAS,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QACzC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,qBAAqB;IAC9E,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,IAAI,CAAC,MAAM,GAAG,gBAAgB,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,WAAW;QACT,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,CAAC,MAAM,GAAG,eAAe,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,eAAuB;QACzC,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC;IAC3B,CAAC;CACF,CAAA;AA1WY,gCAAU;AAErB;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;sCACpB;AAMX;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;yCACV;AAMd;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;+CACL;AASpB;IAJC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC;KACpD,CAAC;;4CACwD;AAU1D;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,gBAAgB,EAAE,eAAe,CAAC;QACrE,OAAO,EAAE,KAAK;KACf,CAAC;;0CACwE;AAM1E;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;6CACrE;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;6CAClE;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4CAC5B;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;8CAC5B;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;gDAC5B;AAMtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;wCAC9B;AAMd;IADC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4CACT;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;+CAC5B;AAMrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kDAC5B;AAMxB;IADC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uCACd;AAMb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;8CAC5B;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDAKlE;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4CACvB;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4CACvB;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;2CACzC;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;iDACzC;AAMzB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;iDAC5B;AAMvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;yCAC5B;AAMf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;oDAC5B;AAM1B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;+CAC5B;AAMrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,wBAAwB,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACtE,IAAI,oBAAJ,IAAI;wDAAC;AAM5B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,uBAAuB,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACtE,IAAI,oBAAJ,IAAI;uDAAC;AAM3B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC;kDACtD,IAAI,oBAAJ,IAAI;iDAAC;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC;kDACtD,IAAI,oBAAJ,IAAI;gDAAC;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;kDAC1C;AAMvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;8CACxB;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,2BAA2B,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;2DACtE;AAMjC;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;6CAC1C;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAC/B,MAAM,oBAAN,MAAM;4CAAc;AAM/B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC;kDACtD,IAAI,oBAAJ,IAAI;8CAAC;AAGjB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;6CAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;6CAAC;AAKhB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,6CAAiB,CAAC;IAClC,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;kDAC1B,6CAAiB,oBAAjB,6CAAiB;wCAAC;AAGxB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;0CAC3B;AAIf;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,oBAAK,CAAC;IACtB,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;kDAC1B,oBAAK,oBAAL,oBAAK;yCAAC;AAGb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;2CAC3B;AAIhB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,oCAAa,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAClD,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,CAAC;kDACzB,oCAAa,oBAAb,oCAAa;iDAAC;AAG9B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDAC1C;qBArPd,UAAU;IAPtB,IAAA,gBAAM,EAAC,cAAc,CAAC;IACtB,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,SAAS,CAAC,CAAC;IAClB,IAAA,eAAK,EAAC,CAAC,iBAAiB,CAAC,CAAC;IAC1B,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,YAAY,CAAC,CAAC;GACT,UAAU,CA0WtB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\domain\\entities\\scan-result.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  ManyToOne,\r\n  JoinColumn,\r\n} from 'typeorm';\r\nimport { Asset } from './asset.entity';\r\nimport { Vulnerability } from './vulnerability.entity';\r\nimport { VulnerabilityScan } from './vulnerability-scan.entity';\r\n\r\n/**\r\n * Scan Result entity\r\n * Represents individual findings from vulnerability scans\r\n */\r\n@Entity('scan_results')\r\n@Index(['scanId'])\r\n@Index(['assetId'])\r\n@Index(['vulnerabilityId'])\r\n@Index(['severity'])\r\n@Index(['status'])\r\n@Index(['detectedAt'])\r\nexport class ScanResult {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * Finding title/name\r\n   */\r\n  @Column({ length: 500 })\r\n  title: string;\r\n\r\n  /**\r\n   * Finding description\r\n   */\r\n  @Column({ type: 'text' })\r\n  description: string;\r\n\r\n  /**\r\n   * Severity level\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['info', 'low', 'medium', 'high', 'critical'],\r\n  })\r\n  severity: 'info' | 'low' | 'medium' | 'high' | 'critical';\r\n\r\n  /**\r\n   * Finding status\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['new', 'existing', 'fixed', 'false_positive', 'risk_accepted'],\r\n    default: 'new',\r\n  })\r\n  status: 'new' | 'existing' | 'fixed' | 'false_positive' | 'risk_accepted';\r\n\r\n  /**\r\n   * CVSS score if applicable\r\n   */\r\n  @Column({ name: 'cvss_score', type: 'decimal', precision: 3, scale: 1, nullable: true })\r\n  cvssScore?: number;\r\n\r\n  /**\r\n   * Risk score\r\n   */\r\n  @Column({ name: 'risk_score', type: 'decimal', precision: 3, scale: 1, default: 0 })\r\n  riskScore: number;\r\n\r\n  /**\r\n   * Plugin/rule ID that detected this finding\r\n   */\r\n  @Column({ name: 'plugin_id', nullable: true })\r\n  pluginId?: string;\r\n\r\n  /**\r\n   * Plugin/rule name\r\n   */\r\n  @Column({ name: 'plugin_name', nullable: true })\r\n  pluginName?: string;\r\n\r\n  /**\r\n   * Plugin family/category\r\n   */\r\n  @Column({ name: 'plugin_family', nullable: true })\r\n  pluginFamily?: string;\r\n\r\n  /**\r\n   * Port number (for network findings)\r\n   */\r\n  @Column({ type: 'integer', nullable: true })\r\n  port?: number;\r\n\r\n  /**\r\n   * Protocol (TCP/UDP)\r\n   */\r\n  @Column({ nullable: true })\r\n  protocol?: string;\r\n\r\n  /**\r\n   * Service name\r\n   */\r\n  @Column({ name: 'service_name', nullable: true })\r\n  serviceName?: string;\r\n\r\n  /**\r\n   * Service version\r\n   */\r\n  @Column({ name: 'service_version', nullable: true })\r\n  serviceVersion?: string;\r\n\r\n  /**\r\n   * URL (for web application findings)\r\n   */\r\n  @Column({ nullable: true })\r\n  url?: string;\r\n\r\n  /**\r\n   * HTTP method (for web application findings)\r\n   */\r\n  @Column({ name: 'http_method', nullable: true })\r\n  httpMethod?: string;\r\n\r\n  /**\r\n   * Request/response data\r\n   */\r\n  @Column({ name: 'request_response', type: 'jsonb', nullable: true })\r\n  requestResponse?: {\r\n    request?: string;\r\n    response?: string;\r\n    headers?: Record<string, string>;\r\n  };\r\n\r\n  /**\r\n   * Evidence/proof of the finding\r\n   */\r\n  @Column({ type: 'text', nullable: true })\r\n  evidence?: string;\r\n\r\n  /**\r\n   * Solution/remediation recommendation\r\n   */\r\n  @Column({ type: 'text', nullable: true })\r\n  solution?: string;\r\n\r\n  /**\r\n   * See also references\r\n   */\r\n  @Column({ name: 'see_also', type: 'jsonb', nullable: true })\r\n  seeAlso?: string[];\r\n\r\n  /**\r\n   * CVE references\r\n   */\r\n  @Column({ name: 'cve_references', type: 'jsonb', nullable: true })\r\n  cveReferences?: string[];\r\n\r\n  /**\r\n   * OWASP category\r\n   */\r\n  @Column({ name: 'owasp_category', nullable: true })\r\n  owaspCategory?: string;\r\n\r\n  /**\r\n   * CWE ID\r\n   */\r\n  @Column({ name: 'cwe_id', nullable: true })\r\n  cweId?: string;\r\n\r\n  /**\r\n   * Exploit availability\r\n   */\r\n  @Column({ name: 'exploit_available', default: false })\r\n  exploitAvailable: boolean;\r\n\r\n  /**\r\n   * Exploit ease\r\n   */\r\n  @Column({ name: 'exploit_ease', nullable: true })\r\n  exploitEase?: string;\r\n\r\n  /**\r\n   * Patch publication date\r\n   */\r\n  @Column({ name: 'patch_publication_date', type: 'timestamp with time zone', nullable: true })\r\n  patchPublicationDate?: Date;\r\n\r\n  /**\r\n   * Vulnerability publication date\r\n   */\r\n  @Column({ name: 'vuln_publication_date', type: 'timestamp with time zone', nullable: true })\r\n  vulnPublicationDate?: Date;\r\n\r\n  /**\r\n   * When this finding was first detected\r\n   */\r\n  @Column({ name: 'first_detected', type: 'timestamp with time zone' })\r\n  firstDetected: Date;\r\n\r\n  /**\r\n   * When this finding was last detected\r\n   */\r\n  @Column({ name: 'last_detected', type: 'timestamp with time zone' })\r\n  lastDetected: Date;\r\n\r\n  /**\r\n   * Detection count\r\n   */\r\n  @Column({ name: 'detection_count', type: 'integer', default: 1 })\r\n  detectionCount: number;\r\n\r\n  /**\r\n   * Scanner confidence level\r\n   */\r\n  @Column({ type: 'integer', nullable: true })\r\n  confidence?: number;\r\n\r\n  /**\r\n   * False positive likelihood\r\n   */\r\n  @Column({ name: 'false_positive_likelihood', type: 'decimal', precision: 3, scale: 2, nullable: true })\r\n  falsePositiveLikelihood?: number;\r\n\r\n  /**\r\n   * Raw scanner output for this finding\r\n   */\r\n  @Column({ name: 'raw_output', type: 'text', nullable: true })\r\n  rawOutput?: string;\r\n\r\n  /**\r\n   * Additional metadata\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  metadata?: Record<string, any>;\r\n\r\n  /**\r\n   * When this finding was detected in the scan\r\n   */\r\n  @Column({ name: 'detected_at', type: 'timestamp with time zone' })\r\n  detectedAt: Date;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  // Relationships\r\n  @ManyToOne(() => VulnerabilityScan)\r\n  @JoinColumn({ name: 'scan_id' })\r\n  scan: VulnerabilityScan;\r\n\r\n  @Column({ name: 'scan_id', type: 'uuid' })\r\n  scanId: string;\r\n\r\n  @ManyToOne(() => Asset)\r\n  @JoinColumn({ name: 'asset_id' })\r\n  asset: Asset;\r\n\r\n  @Column({ name: 'asset_id', type: 'uuid' })\r\n  assetId: string;\r\n\r\n  @ManyToOne(() => Vulnerability, { nullable: true })\r\n  @JoinColumn({ name: 'vulnerability_id' })\r\n  vulnerability?: Vulnerability;\r\n\r\n  @Column({ name: 'vulnerability_id', type: 'uuid', nullable: true })\r\n  vulnerabilityId?: string;\r\n\r\n  /**\r\n   * Check if finding is critical\r\n   */\r\n  get isCritical(): boolean {\r\n    return this.severity === 'critical';\r\n  }\r\n\r\n  /**\r\n   * Check if finding is high severity\r\n   */\r\n  get isHighSeverity(): boolean {\r\n    return ['critical', 'high'].includes(this.severity);\r\n  }\r\n\r\n  /**\r\n   * Check if finding is new\r\n   */\r\n  get isNew(): boolean {\r\n    return this.status === 'new';\r\n  }\r\n\r\n  /**\r\n   * Check if finding is resolved\r\n   */\r\n  get isResolved(): boolean {\r\n    return ['fixed', 'false_positive', 'risk_accepted'].includes(this.status);\r\n  }\r\n\r\n  /**\r\n   * Get finding age in days\r\n   */\r\n  get ageInDays(): number {\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - this.firstDetected.getTime();\r\n    return Math.floor(diffMs / (1000 * 60 * 60 * 24));\r\n  }\r\n\r\n  /**\r\n   * Get days since last detection\r\n   */\r\n  get daysSinceLastDetection(): number {\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - this.lastDetected.getTime();\r\n    return Math.floor(diffMs / (1000 * 60 * 60 * 24));\r\n  }\r\n\r\n  /**\r\n   * Update last detected timestamp\r\n   */\r\n  updateLastDetected(): void {\r\n    this.lastDetected = new Date();\r\n    this.detectionCount += 1;\r\n  }\r\n\r\n  /**\r\n   * Calculate risk score based on severity, exploitability, and other factors\r\n   */\r\n  calculateRiskScore(): void {\r\n    const severityScores = {\r\n      info: 0.1,\r\n      low: 2.5,\r\n      medium: 5.0,\r\n      high: 7.5,\r\n      critical: 9.5,\r\n    };\r\n\r\n    let baseScore = this.cvssScore || severityScores[this.severity];\r\n\r\n    // Apply exploit availability multiplier\r\n    if (this.exploitAvailable) {\r\n      baseScore *= 1.3;\r\n    }\r\n\r\n    // Apply confidence factor\r\n    if (this.confidence) {\r\n      baseScore *= (this.confidence / 100);\r\n    }\r\n\r\n    // Apply false positive likelihood reduction\r\n    if (this.falsePositiveLikelihood) {\r\n      baseScore *= (1 - this.falsePositiveLikelihood);\r\n    }\r\n\r\n    this.riskScore = Math.min(baseScore, 10);\r\n    this.riskScore = Math.round(this.riskScore * 10) / 10; // Round to 1 decimal\r\n  }\r\n\r\n  /**\r\n   * Mark finding as false positive\r\n   */\r\n  markAsFalsePositive(): void {\r\n    this.status = 'false_positive';\r\n  }\r\n\r\n  /**\r\n   * Mark finding as fixed\r\n   */\r\n  markAsFixed(): void {\r\n    this.status = 'fixed';\r\n  }\r\n\r\n  /**\r\n   * Mark finding as risk accepted\r\n   */\r\n  markAsRiskAccepted(): void {\r\n    this.status = 'risk_accepted';\r\n  }\r\n\r\n  /**\r\n   * Link to vulnerability\r\n   */\r\n  linkToVulnerability(vulnerabilityId: string): void {\r\n    this.vulnerabilityId = vulnerabilityId;\r\n    this.status = 'existing';\r\n  }\r\n}\r\n"], "version": 3}