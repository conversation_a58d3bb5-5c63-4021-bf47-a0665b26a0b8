{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\app.module.ts", "mappings": ";;;;;;;;;AAAA,2CAAwE;AACxE,uCAAsE;AACtE,iDAAoE;AACpE,2CAA6D;AAE7D,yBAAyB;AACzB,yEAAwF;AACxF,+EAA2E;AAC3E,mEAA+D;AAC/D,4EAAwE;AAExE,+CAA+C;AAC/C,sFAAiF;AACjF,mFAA+E;AAC/E,yGAAoG;AACpG,6FAAwF;AAExF,cAAc;AACd,iDAA6C;AAE7C,kBAAkB;AAClB,wHAAmH;AACnH,sDAAkD;AAElD,yBAAyB;AACzB,qDAAiD;AACjD,+CAA2C;AAC3C,2DAAsE;AACtE,6DAAyE;AAEzE;;;GAGG;AA8DI,IAAM,SAAS,GAAf,MAAM,SAAS;IACpB;;OAEG;IACH,SAAS,CAAC,QAA4B;QACpC,QAAQ;aACL,KAAK,CAAC,mDAAuB,CAAC;aAC9B,SAAS,CAAC,GAAG,CAAC,CAAC;IACpB,CAAC;CACF,CAAA;AATY,8BAAS;oBAAT,SAAS;IA7DrB,IAAA,eAAM,EAAC;QACN,OAAO,EAAE;YACP,uCAAuC;YACvC,4BAAe;YAEf,gBAAgB;YAChB,2BAAe,CAAC,YAAY,CAAC;gBAC3B,OAAO,EAAE,CAAC,qBAAY,CAAC;gBACvB,MAAM,EAAE,CAAC,sBAAa,CAAC;gBACvB,UAAU,EAAE,CAAC,aAA4B,EAAE,EAAE;oBAC3C,MAAM,cAAc,GAAG,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;oBACrD,OAAO;wBACL;4BACE,GAAG,EAAE,cAAc,EAAE,SAAS,EAAE,QAAQ,IAAI,KAAK;4BACjD,KAAK,EAAE,cAAc,EAAE,SAAS,EAAE,GAAG,IAAI,GAAG;yBAC7C;qBACF,CAAC;gBACJ,CAAC;aACF,CAAC;YAEF,yBAAyB;YACzB,gCAAc;YACd,wBAAU;YACV,8BAAa;YAEb,cAAc;YACd,sBAAS;YAET,kBAAkB;YAClB,+DAA6B;YAC7B,oBAAQ;SACT;QACD,WAAW,EAAE,CAAC,8BAAa,EAAE,oCAAgB,EAAE,sCAAiB,CAAC;QACjE,SAAS,EAAE;YACT,wBAAU;YACV,iCAAa;YACb,mCAAc;YAEd,0BAA0B;YAC1B;gBACE,OAAO,EAAE,iBAAU;gBACnB,QAAQ,EAAE,+CAAqB;aAChC;YAED,sBAAsB;YACtB;gBACE,OAAO,EAAE,sBAAe;gBACxB,QAAQ,EAAE,wCAAkB;aAC7B;YACD;gBACE,OAAO,EAAE,sBAAe;gBACxB,QAAQ,EAAE,6DAA4B;aACvC;YAED,6BAA6B;YAC7B;gBACE,OAAO,EAAE,gBAAS;gBAClB,QAAQ,EAAE,0BAAc;aACzB;SACF;KACF,CAAC;GACW,SAAS,CASrB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\app.module.ts"], "sourcesContent": ["import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';\r\nimport { APP_FILTER, APP_INTERCEPTOR, APP_GUARD } from '@nestjs/core';\r\nimport { ThrottlerModule, ThrottlerGuard } from '@nestjs/throttler';\r\nimport { ConfigModule, ConfigService } from '@nestjs/config';\r\n\r\n// Infrastructure modules\r\nimport { ConfigModule as AppConfigModule } from './infrastructure/config/config.module';\r\nimport { DatabaseModule } from './infrastructure/database/database.module';\r\nimport { AuthModule } from './infrastructure/auth/auth.module';\r\nimport { LoggingModule } from './infrastructure/logging/logging.module';\r\n\r\n// Common filters, interceptors, and middleware\r\nimport { GlobalExceptionFilter } from './common/filters/global-exception.filter';\r\nimport { LoggingInterceptor } from './common/interceptors/logging.interceptor';\r\nimport { ResponseTransformInterceptor } from './common/interceptors/response-transform.interceptor';\r\nimport { CorrelationIdMiddleware } from './common/middleware/correlation-id.middleware';\r\n\r\n// API modules\r\nimport { ApiModule } from './api/api.module';\r\n\r\n// Feature modules\r\nimport { VulnerabilityManagementModule } from './modules/vulnerability-management/vulnerability-management.module';\r\nimport { AiModule } from './modules/ai/ai.module';\r\n\r\n// Application components\r\nimport { AppController } from './app.controller';\r\nimport { AppService } from './app.service';\r\nimport { HealthController, HealthService } from './health.controller';\r\nimport { MetricsController, MetricsService } from './metrics.controller';\r\n\r\n/**\r\n * Main application module for the Sentinel vulnerability assessment platform\r\n * Configures all infrastructure modules and global providers\r\n */\r\n@Module({\r\n  imports: [\r\n    // Configuration module (must be first)\r\n    AppConfigModule,\r\n\r\n    // Rate limiting\r\n    ThrottlerModule.forRootAsync({\r\n      imports: [ConfigModule],\r\n      inject: [ConfigService],\r\n      useFactory: (configService: ConfigService) => {\r\n        const securityConfig = configService.get('security');\r\n        return [\r\n          {\r\n            ttl: securityConfig?.rateLimit?.windowMs || 60000,\r\n            limit: securityConfig?.rateLimit?.max || 100,\r\n          },\r\n        ];\r\n      },\r\n    }),\r\n\r\n    // Infrastructure modules\r\n    DatabaseModule,\r\n    AuthModule,\r\n    LoggingModule,\r\n\r\n    // API modules\r\n    ApiModule,\r\n\r\n    // Feature modules\r\n    VulnerabilityManagementModule,\r\n    AiModule,\r\n  ],\r\n  controllers: [AppController, HealthController, MetricsController],\r\n  providers: [\r\n    AppService,\r\n    HealthService,\r\n    MetricsService,\r\n\r\n    // Global exception filter\r\n    {\r\n      provide: APP_FILTER,\r\n      useClass: GlobalExceptionFilter,\r\n    },\r\n\r\n    // Global interceptors\r\n    {\r\n      provide: APP_INTERCEPTOR,\r\n      useClass: LoggingInterceptor,\r\n    },\r\n    {\r\n      provide: APP_INTERCEPTOR,\r\n      useClass: ResponseTransformInterceptor,\r\n    },\r\n\r\n    // Global rate limiting guard\r\n    {\r\n      provide: APP_GUARD,\r\n      useClass: ThrottlerGuard,\r\n    },\r\n  ],\r\n})\r\nexport class AppModule implements NestModule {\r\n  /**\r\n   * Configure middleware for the application\r\n   */\r\n  configure(consumer: MiddlewareConsumer): void {\r\n    consumer\r\n      .apply(CorrelationIdMiddleware)\r\n      .forRoutes('*');\r\n  }\r\n}\r\n"], "version": 3}