{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\__tests__\\e2e\\error-scenarios.e2e-spec.ts", "mappings": ";;;;;AAAA,6CAAsD;AAEtD,2CAA8C;AAC9C,0DAAgC;AAChC,iDAA6C;AAE7C,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;IACrC,IAAI,GAAqB,CAAC;IAC1B,IAAI,SAAiB,CAAC;IAEtB,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,aAAa,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAClE,OAAO,EAAE;gBACP,qBAAY,CAAC,OAAO,CAAC;oBACnB,QAAQ,EAAE,IAAI;oBACd,WAAW,EAAE,WAAW;iBACzB,CAAC;gBACF,sBAAS;aACV;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,GAAG,GAAG,aAAa,CAAC,qBAAqB,EAAE,CAAC;QAC5C,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC;QAEjB,+CAA+C;QAC/C,MAAM,YAAY,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;aACpD,IAAI,CAAC,uBAAuB,CAAC;aAC7B,IAAI,CAAC;YACJ,KAAK,EAAE,wBAAwB;YAC/B,QAAQ,EAAE,oBAAoB;YAC9B,IAAI,EAAE,iBAAiB;SACxB,CAAC,CAAC;QAEL,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;IACxD,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC;gBACJ,0BAA0B;gBAC1B,WAAW,EAAE,oBAAoB;aAClC,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,UAAU,EAAE,GAAG;gBACf,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC3B,KAAK,EAAE,aAAa;gBACpB,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,IAAI,EAAE,oBAAoB;gBAC1B,MAAM,EAAE,MAAM;gBACd,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC9B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,UAAU,EAAE,GAAG;gBACf,OAAO,EAAE,cAAc;gBACvB,KAAK,EAAE,cAAc;gBACrB,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,IAAI,EAAE,sBAAsB;gBAC5B,MAAM,EAAE,KAAK;gBACb,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC9B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,qBAAqB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,UAAU,EAAE,GAAG;gBACf,OAAO,EAAE,oBAAoB;gBAC7B,KAAK,EAAE,WAAW;gBAClB,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,IAAI,EAAE,qBAAqB;gBAC3B,MAAM,EAAE,KAAK;gBACb,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC9B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,oCAAoC,CAAC;iBACzC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,UAAU,EAAE,GAAG;gBACf,OAAO,EAAE,oBAAoB;gBAC7B,KAAK,EAAE,WAAW;gBAClB,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,IAAI,EAAE,oCAAoC;gBAC1C,MAAM,EAAE,KAAK;gBACb,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC9B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,KAAK,CAAC,gBAAgB,CAAC,CAAC,mDAAmD;iBAC3E,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,qBAAqB;YACrB,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,IAAI,CAAC,uBAAuB,CAAC;iBAC7B,IAAI,CAAC;gBACJ,KAAK,EAAE,2BAA2B;gBAClC,QAAQ,EAAE,oBAAoB;gBAC9B,IAAI,EAAE,oBAAoB;aAC3B,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,yBAAyB;YACzB,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,uBAAuB,CAAC;iBAC7B,IAAI,CAAC;gBACJ,KAAK,EAAE,2BAA2B;gBAClC,QAAQ,EAAE,qBAAqB;gBAC/B,IAAI,EAAE,cAAc;aACrB,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,UAAU,EAAE,GAAG;gBACf,OAAO,EAAE,sBAAsB;gBAC/B,KAAK,EAAE,UAAU;gBACjB,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,IAAI,EAAE,uBAAuB;gBAC7B,MAAM,EAAE,MAAM;gBACd,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC9B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,YAAY,GAAG;gBACnB,IAAI,EAAE,YAAY;gBAClB,WAAW,EAAE,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,yBAAyB;gBAC1D,QAAQ,EAAE,MAAM;gBAChB,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;oBAC7C,GAAG,EAAE,MAAM,CAAC,EAAE;oBACd,KAAK,EAAE,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC;iBACxB,CAAC,CAAC;aACJ,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC,YAAY,CAAC,CAAC;YAEtB,sDAAsD;YACtD,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;gBAC5B,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACxD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,GAAG,CAAC,cAAc,EAAE,iBAAiB,CAAC;iBACtC,IAAI,CAAC,oCAAoC,CAAC;iBAC1C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC;gBACJ,IAAI,EAAE,YAAY;gBAClB,WAAW,EAAE,mBAAmB;gBAChC,QAAQ,EAAE,sCAAsC;aACjD,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,wDAAwD;YACxD,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAC/C,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACzB,GAAG,CAAC,gBAAgB,CAAC,CACzB,CAAC;YAEF,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CACjC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CACjB,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;iBAC5E,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBACb,MAAM,EAAE,GAAG,CAAC,QAAQ,EAAE,MAAM,IAAI,GAAG;gBACnC,OAAO,EAAE,GAAG,CAAC,QAAQ,EAAE,OAAO,IAAI,EAAE;gBACpC,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,IAAI,IAAI,EAAE;aAC/B,CAAC,CAAC,CACN,CACF,CAAC;YAEF,uCAAuC;YACvC,MAAM,oBAAoB,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,GAAG,CAAC,CAAC;YAErE,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpC,MAAM,mBAAmB,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;gBACpD,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACtD,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBACjE,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;gBACvE,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC3E,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;YACnE,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC;gBACJ,WAAW,EAAE,oBAAoB;gBACjC,QAAQ,EAAE,MAAM;aACjB,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAChD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CACnC,MAAM,CAAC,eAAe,CAAC;gBACrB,MAAM,CAAC,gBAAgB,CAAC;oBACtB,KAAK,EAAE,MAAM;oBACb,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC5B,CAAC;aACH,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC;gBACJ,IAAI,EAAE,GAAG,EAAE,mBAAmB;gBAC9B,WAAW,EAAE,IAAI,EAAE,mBAAmB;gBACtC,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,mBAAmB;aAC3C,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CACnC,MAAM,CAAC,eAAe,CAAC;gBACrB,MAAM,CAAC,gBAAgB,CAAC;oBACtB,KAAK,EAAE,MAAM;oBACb,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;iBAC3C,CAAC;gBACF,MAAM,CAAC,gBAAgB,CAAC;oBACtB,KAAK,EAAE,aAAa;oBACpB,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;iBAC3C,CAAC;gBACF,MAAM,CAAC,gBAAgB,CAAC;oBACtB,KAAK,EAAE,UAAU;oBACjB,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;iBAC3C,CAAC;aACH,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC;gBACJ,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,WAAW;gBAClC,WAAW,EAAE,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,WAAW;gBAC1C,QAAQ,EAAE,EAAE,EAAE,YAAY;aAC3B,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CACnC,MAAM,CAAC,eAAe,CAAC;gBACrB,MAAM,CAAC,gBAAgB,CAAC;oBACtB,KAAK,EAAE,MAAM;oBACb,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;iBAC3C,CAAC;gBACF,MAAM,CAAC,gBAAgB,CAAC;oBACtB,KAAK,EAAE,aAAa;oBACpB,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;iBAC3C,CAAC;gBACF,MAAM,CAAC,gBAAgB,CAAC;oBACtB,KAAK,EAAE,UAAU;oBACjB,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC;iBAC1C,CAAC;aACH,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,8BAA8B,CAAC;iBACpC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC;gBACJ,SAAS,EAAE,wBAAwB;gBACnC,QAAQ,EAAE,kBAAkB,EAAE,wCAAwC;gBACtE,SAAS,EAAE,iBAAiB,EAAE,aAAa;gBAC3C,KAAK,EAAE,oBAAoB,EAAE,iCAAiC;aAC/D,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CACnC,MAAM,CAAC,eAAe,CAAC;gBACrB,MAAM,CAAC,gBAAgB,CAAC;oBACtB,KAAK,EAAE,UAAU;oBACjB,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC;iBAC7C,CAAC;gBACF,MAAM,CAAC,gBAAgB,CAAC;oBACtB,KAAK,EAAE,WAAW;oBAClB,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC;iBAC/C,CAAC;gBACF,MAAM,CAAC,gBAAgB,CAAC;oBACtB,KAAK,EAAE,OAAO;oBACd,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC;iBACxC,CAAC;aACH,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,4BAA4B,CAAC;iBAClC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC;gBACJ,IAAI,EAAE,cAAc;gBACpB,QAAQ,EAAE;oBACR,IAAI,EAAE,iBAAiB,EAAE,kBAAkB;oBAC3C,QAAQ,EAAE,kBAAkB,EAAE,mBAAmB;oBACjD,QAAQ,EAAE;wBACR,OAAO,EAAE,aAAa,EAAE,oBAAoB;wBAC5C,SAAS,EAAE,YAAY,EAAE,mBAAmB;qBAC7C;iBACF;aACF,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CACnC,MAAM,CAAC,eAAe,CAAC;gBACrB,MAAM,CAAC,gBAAgB,CAAC;oBACtB,KAAK,EAAE,eAAe;oBACtB,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC;iBAC1C,CAAC;gBACF,MAAM,CAAC,gBAAgB,CAAC;oBACtB,KAAK,EAAE,mBAAmB;oBAC1B,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;iBAC3C,CAAC;gBACF,MAAM,CAAC,gBAAgB,CAAC;oBACtB,KAAK,EAAE,2BAA2B;oBAClC,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC;iBAC5C,CAAC;gBACF,MAAM,CAAC,gBAAgB,CAAC;oBACtB,KAAK,EAAE,6BAA6B;oBACpC,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;iBAC3C,CAAC;aACH,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,+CAA+C;YAC/C,gEAAgE;YAEhE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,oBAAoB,CAAC;iBACzB,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC,CAAC;YAE/C,oDAAoD;YACpD,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;gBAC5B,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;oBAC5B,UAAU,EAAE,GAAG;oBACf,OAAO,EAAE,iCAAiC;oBAC1C,KAAK,EAAE,qBAAqB;oBAC5B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC7B,IAAI,EAAE,oBAAoB;oBAC1B,MAAM,EAAE,KAAK;oBACb,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC9B,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,wCAAwC;YACxC,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,IAAI,CAAC,2BAA2B,CAAC;iBACjC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC;gBACJ,UAAU,EAAE,YAAY;gBACxB,IAAI,EAAE,aAAa;aACpB,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,mDAAmD;YACnD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,2BAA2B,CAAC;iBACjC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC;gBACJ,UAAU,EAAE,YAAY;gBACxB,IAAI,EAAE,qBAAqB;aAC5B,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,sDAAsD;YACtD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,yBAAyB,CAAC;iBAC9B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,OAAO,CAAC,IAAI,CAAC,CAAC;YAEjB,mCAAmC;YACnC,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;gBAC5B,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACtD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,2CAA2C,CAAC;iBAChD,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC,CAAC;YAE/C,qDAAqD;YACrD,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;gBAC5B,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;oBAC5B,UAAU,EAAE,GAAG;oBACf,OAAO,EAAE,8BAA8B;oBACvC,KAAK,EAAE,qBAAqB;oBAC5B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC7B,IAAI,EAAE,2CAA2C;oBACjD,MAAM,EAAE,KAAK;oBACb,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC9B,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,gDAAgD,CAAC;iBACrD,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,OAAO,CAAC,IAAI,CAAC,CAAC;YAEjB,sCAAsC;YACtC,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;gBAC5B,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACtD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,oCAAoC,CAAC;iBACzC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC,CAAC;YAE/C,+CAA+C;YAC/C,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAEnD,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;gBAC5B,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAClD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,MAAM,YAAY,GAAG,+IAA+I,CAAC;YAErK,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,YAAY,EAAE,CAAC;iBAC9C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,eAAe,GAAG;gBACtB,iBAAiB;gBACjB,gBAAgB,EAAE,oBAAoB;gBACtC,gCAAgC,EAAE,iBAAiB;gBACnD,EAAE,EAAE,cAAc;aACnB,CAAC;YAEF,KAAK,MAAM,KAAK,IAAI,eAAe,EAAE,CAAC;gBACpC,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,sBAAsB,CAAC;qBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;qBACvC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,uBAAuB;YACvB,MAAM,aAAa,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACrD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC;gBACJ,KAAK,EAAE,wBAAwB;gBAC/B,QAAQ,EAAE,oBAAoB;aAC/B,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;YAEzD,6BAA6B;YAC7B,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,IAAI,CAAC,qBAAqB,CAAC;iBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;iBACvC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,+BAA+B;YAC/B,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;iBACvC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,qBAAqB,CAAC;iBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,EAAE;gBACzD,QAAQ,EAAE,eAAe;gBACzB,WAAW,EAAE,mBAAmB;aACjC,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,YAAY;YAE9D,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,qBAAqB,CAAC;iBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE;gBACzB,QAAQ,EAAE,gBAAgB;gBAC1B,WAAW,EAAE,YAAY;aAC1B,CAAC,CAAC;YAEL,sDAAsD;YACtD,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;gBAC5B,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,wBAAwB;YAEjG,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,qBAAqB,CAAC;iBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,MAAM,EAAE,aAAa,EAAE;gBAC7B,QAAQ,EAAE,eAAe;gBACzB,WAAW,EAAE,YAAY;aAC1B,CAAC,CAAC;YAEL,2CAA2C;YAC3C,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,uEAAuE;YACvE,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAC/C,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACzB,IAAI,CAAC,4BAA4B,CAAC;iBAClC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC;gBACJ,IAAI,EAAE,cAAc;gBACpB,SAAS,EAAE,CAAC;aACb,CAAC,CACL,CAAC;YAEF,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CACjC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CACjB,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;iBACtD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBACb,MAAM,EAAE,GAAG,CAAC,QAAQ,EAAE,MAAM,IAAI,GAAG;gBACnC,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,IAAI,IAAI,EAAE;aAC/B,CAAC,CAAC,CACN,CACF,CAAC;YAEF,wDAAwD;YACxD,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAC3B,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAC1D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,6EAA6E;YAC7E,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,kCAAkC,CAAC;iBACxC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC;gBACJ,UAAU,EAAE;oBACV,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;oBAC5C,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE;oBAChE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,YAAY,EAAE;iBACrC;aACF,CAAC,CAAC;YAEL,qCAAqC;YACrC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,yDAAyD;YACzD,MAAM,sBAAsB,GAAG;gBAC7B,IAAI,EAAE,aAAa;gBACnB,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;oBAC9C,EAAE,EAAE,CAAC;oBACL,KAAK,EAAE,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC;oBACvB,MAAM,EAAE;wBACN,IAAI,EAAE;4BACJ,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC;yBACzD;qBACF;iBACF,CAAC,CAAC;aACJ,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,+BAA+B,CAAC;iBACrC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAEhC,6CAA6C;YAC7C,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,4BAA4B,CAAC;iBAClC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC;gBACJ,UAAU,EAAE,OAAO;gBACnB,UAAU,EAAE,MAAM;aACnB,CAAC;iBACD,OAAO,CAAC,KAAK,CAAC,CAAC;YAElB,gEAAgE;YAChE,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,8DAA8D,EAAE,KAAK,IAAI,EAAE;YAC5E,MAAM,cAAc,GAAG;gBACrB,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,qBAAqB,EAAE,cAAc,EAAE,GAAG,EAAE;gBACnE,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,oBAAoB,EAAE,IAAI,EAAE,EAAE,EAAE,cAAc,EAAE,GAAG,EAAE;gBAC7E,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,sBAAsB,EAAE,cAAc,EAAE,GAAG,EAAE;gBACpE,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,qBAAqB,EAAE,cAAc,EAAE,GAAG,EAAE;aACpE,CAAC;YAEF,KAAK,MAAM,QAAQ,IAAI,cAAc,EAAE,CAAC;gBACtC,IAAI,GAAG,GAAG,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAEvE,IAAI,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;oBAC9E,2CAA2C;gBAC7C,CAAC;qBAAM,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;oBACzB,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC5E,CAAC;qBAAM,CAAC;oBACN,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC,CAAC;gBACxD,CAAC;gBAED,MAAM,QAAQ,GAAG,MAAM,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;gBAE3D,uDAAuD;gBACvD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;oBAC5B,UAAU,EAAE,QAAQ,CAAC,cAAc;oBACnC,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC3B,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACzB,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC7B,IAAI,EAAE,QAAQ,CAAC,IAAI;oBACnB,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,WAAW,EAAE;oBACrC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC9B,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,aAAa,GAAG,sBAAsB,CAAC;YAE7C,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,qBAAqB,CAAC;iBAC1B,GAAG,CAAC,kBAAkB,EAAE,aAAa,CAAC;iBACtC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9C,6DAA6D;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,6BAA6B,CAAC;iBAClC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC,CAAC;YAE/C,sEAAsE;YACtE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YACxD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YACzD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YACtD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YAC5D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\__tests__\\e2e\\error-scenarios.e2e-spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { INestApplication } from '@nestjs/common';\r\nimport { ConfigModule } from '@nestjs/config';\r\nimport request from 'supertest';\r\nimport { AppModule } from '../../app.module';\r\n\r\ndescribe('Error Scenarios (e2e)', () => {\r\n  let app: INestApplication;\r\n  let authToken: string;\r\n\r\n  beforeAll(async () => {\r\n    const moduleFixture: TestingModule = await Test.createTestingModule({\r\n      imports: [\r\n        ConfigModule.forRoot({\r\n          isGlobal: true,\r\n          envFilePath: '.env.test',\r\n        }),\r\n        AppModule,\r\n      ],\r\n    }).compile();\r\n\r\n    app = moduleFixture.createNestApplication();\r\n    await app.init();\r\n\r\n    // Setup authentication for protected endpoints\r\n    const authResponse = await request(app.getHttpServer())\r\n      .post('/api/v1/auth/register')\r\n      .send({\r\n        email: '<EMAIL>',\r\n        password: 'SecurePassword123!',\r\n        name: 'Error Test User',\r\n      });\r\n\r\n    authToken = authResponse.body.data.tokens.accessToken;\r\n  });\r\n\r\n  afterAll(async () => {\r\n    await app.close();\r\n  });\r\n\r\n  describe('HTTP Error Codes', () => {\r\n    it('should return 400 for bad request', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/test/items')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send({\r\n          // Missing required fields\r\n          description: 'Missing name field',\r\n        })\r\n        .expect(400);\r\n\r\n      expect(response.body).toEqual({\r\n        statusCode: 400,\r\n        message: expect.any(String),\r\n        error: 'Bad Request',\r\n        timestamp: expect.any(String),\r\n        path: '/api/v1/test/items',\r\n        method: 'POST',\r\n        requestId: expect.any(String),\r\n      });\r\n    });\r\n\r\n    it('should return 401 for unauthorized access', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/auth/profile')\r\n        .expect(401);\r\n\r\n      expect(response.body).toEqual({\r\n        statusCode: 401,\r\n        message: 'Unauthorized',\r\n        error: 'Unauthorized',\r\n        timestamp: expect.any(String),\r\n        path: '/api/v1/auth/profile',\r\n        method: 'GET',\r\n        requestId: expect.any(String),\r\n      });\r\n    });\r\n\r\n    it('should return 403 for forbidden access', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/admin/users')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .expect(403);\r\n\r\n      expect(response.body).toEqual({\r\n        statusCode: 403,\r\n        message: 'Forbidden resource',\r\n        error: 'Forbidden',\r\n        timestamp: expect.any(String),\r\n        path: '/api/v1/admin/users',\r\n        method: 'GET',\r\n        requestId: expect.any(String),\r\n      });\r\n    });\r\n\r\n    it('should return 404 for not found resources', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/test/items/non-existent-id')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .expect(404);\r\n\r\n      expect(response.body).toEqual({\r\n        statusCode: 404,\r\n        message: 'Resource not found',\r\n        error: 'Not Found',\r\n        timestamp: expect.any(String),\r\n        path: '/api/v1/test/items/non-existent-id',\r\n        method: 'GET',\r\n        requestId: expect.any(String),\r\n      });\r\n    });\r\n\r\n    it('should return 405 for method not allowed', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .patch('/api/v1/health') // Assuming PATCH is not allowed on health endpoint\r\n        .expect(405);\r\n\r\n      expect(response.body.statusCode).toBe(405);\r\n      expect(response.body.error).toBe('Method Not Allowed');\r\n    });\r\n\r\n    it('should return 409 for conflict errors', async () => {\r\n      // First registration\r\n      await request(app.getHttpServer())\r\n        .post('/api/v1/auth/register')\r\n        .send({\r\n          email: '<EMAIL>',\r\n          password: 'SecurePassword123!',\r\n          name: 'Conflict Test User',\r\n        })\r\n        .expect(201);\r\n\r\n      // Duplicate registration\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/auth/register')\r\n        .send({\r\n          email: '<EMAIL>',\r\n          password: 'AnotherPassword123!',\r\n          name: 'Another User',\r\n        })\r\n        .expect(409);\r\n\r\n      expect(response.body).toEqual({\r\n        statusCode: 409,\r\n        message: 'Email already exists',\r\n        error: 'Conflict',\r\n        timestamp: expect.any(String),\r\n        path: '/api/v1/auth/register',\r\n        method: 'POST',\r\n        requestId: expect.any(String),\r\n      });\r\n    });\r\n\r\n    it('should return 413 for payload too large', async () => {\r\n      const largePayload = {\r\n        name: 'Large Item',\r\n        description: 'A'.repeat(100000), // Very large description\r\n        category: 'test',\r\n        data: Array.from({ length: 10000 }, (_, i) => ({\r\n          key: `key${i}`,\r\n          value: 'x'.repeat(1000),\r\n        })),\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/test/items')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send(largePayload);\r\n\r\n      // Should either accept or reject based on size limits\r\n      if (response.status === 413) {\r\n        expect(response.body.statusCode).toBe(413);\r\n        expect(response.body.error).toBe('Payload Too Large');\r\n      }\r\n    });\r\n\r\n    it('should return 415 for unsupported media type', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/test/items')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .set('Content-Type', 'application/xml')\r\n        .send('<item><name>XML Item</name></item>')\r\n        .expect(415);\r\n\r\n      expect(response.body.statusCode).toBe(415);\r\n      expect(response.body.error).toBe('Unsupported Media Type');\r\n    });\r\n\r\n    it('should return 422 for unprocessable entity', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/test/items')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send({\r\n          name: 'Valid Name',\r\n          description: 'Valid Description',\r\n          category: 'invalid-category-that-does-not-exist',\r\n        })\r\n        .expect(422);\r\n\r\n      expect(response.body.statusCode).toBe(422);\r\n      expect(response.body.error).toBe('Unprocessable Entity');\r\n    });\r\n\r\n    it('should return 429 for rate limiting', async () => {\r\n      // Make multiple rapid requests to trigger rate limiting\r\n      const requests = Array.from({ length: 50 }, () =>\r\n        request(app.getHttpServer())\r\n          .get('/api/v1/health')\r\n      );\r\n\r\n      const responses = await Promise.all(\r\n        requests.map(req => \r\n          req.then(res => ({ status: res.status, headers: res.headers, body: res.body }))\r\n            .catch(err => ({ \r\n              status: err.response?.status || 500, \r\n              headers: err.response?.headers || {},\r\n              body: err.response?.body || {}\r\n            }))\r\n        )\r\n      );\r\n\r\n      // Some requests should be rate limited\r\n      const rateLimitedResponses = responses.filter(r => r.status === 429);\r\n      \r\n      if (rateLimitedResponses.length > 0) {\r\n        const rateLimitedResponse = rateLimitedResponses[0];\r\n        expect(rateLimitedResponse.body.statusCode).toBe(429);\r\n        expect(rateLimitedResponse.body.error).toBe('Too Many Requests');\r\n        expect(rateLimitedResponse.headers['x-ratelimit-limit']).toBeDefined();\r\n        expect(rateLimitedResponse.headers['x-ratelimit-remaining']).toBeDefined();\r\n        expect(rateLimitedResponse.headers['retry-after']).toBeDefined();\r\n      }\r\n    });\r\n  });\r\n\r\n  describe('Validation Errors', () => {\r\n    it('should handle missing required fields', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/test/items')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send({\r\n          description: 'Missing name field',\r\n          category: 'test',\r\n        })\r\n        .expect(400);\r\n\r\n      expect(response.body.message).toContain('name');\r\n      expect(response.body.details).toEqual(\r\n        expect.arrayContaining([\r\n          expect.objectContaining({\r\n            field: 'name',\r\n            message: expect.any(String),\r\n          }),\r\n        ])\r\n      );\r\n    });\r\n\r\n    it('should handle invalid field types', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/test/items')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send({\r\n          name: 123, // Should be string\r\n          description: true, // Should be string\r\n          category: ['invalid'], // Should be string\r\n        })\r\n        .expect(400);\r\n\r\n      expect(response.body.details).toEqual(\r\n        expect.arrayContaining([\r\n          expect.objectContaining({\r\n            field: 'name',\r\n            message: expect.stringContaining('string'),\r\n          }),\r\n          expect.objectContaining({\r\n            field: 'description',\r\n            message: expect.stringContaining('string'),\r\n          }),\r\n          expect.objectContaining({\r\n            field: 'category',\r\n            message: expect.stringContaining('string'),\r\n          }),\r\n        ])\r\n      );\r\n    });\r\n\r\n    it('should handle field length validation', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/test/items')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send({\r\n          name: 'a'.repeat(256), // Too long\r\n          description: 'b'.repeat(1001), // Too long\r\n          category: '', // Too short\r\n        })\r\n        .expect(400);\r\n\r\n      expect(response.body.details).toEqual(\r\n        expect.arrayContaining([\r\n          expect.objectContaining({\r\n            field: 'name',\r\n            message: expect.stringContaining('length'),\r\n          }),\r\n          expect.objectContaining({\r\n            field: 'description',\r\n            message: expect.stringContaining('length'),\r\n          }),\r\n          expect.objectContaining({\r\n            field: 'category',\r\n            message: expect.stringContaining('empty'),\r\n          }),\r\n        ])\r\n      );\r\n    });\r\n\r\n    it('should handle custom validation rules', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/test/security-events')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send({\r\n          eventType: 'vulnerability_detected',\r\n          severity: 'invalid-severity', // Should be low, medium, high, critical\r\n          ipAddress: '999.999.999.999', // Invalid IP\r\n          cveId: 'INVALID-CVE-FORMAT', // Should be CVE-YYYY-NNNN format\r\n        })\r\n        .expect(400);\r\n\r\n      expect(response.body.details).toEqual(\r\n        expect.arrayContaining([\r\n          expect.objectContaining({\r\n            field: 'severity',\r\n            message: expect.stringContaining('severity'),\r\n          }),\r\n          expect.objectContaining({\r\n            field: 'ipAddress',\r\n            message: expect.stringContaining('IP address'),\r\n          }),\r\n          expect.objectContaining({\r\n            field: 'cveId',\r\n            message: expect.stringContaining('CVE'),\r\n          }),\r\n        ])\r\n      );\r\n    });\r\n\r\n    it('should handle nested object validation', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/test/complex-items')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send({\r\n          name: 'Complex Item',\r\n          metadata: {\r\n            tags: 'should-be-array', // Should be array\r\n            priority: 'invalid-priority', // Should be number\r\n            settings: {\r\n              enabled: 'not-boolean', // Should be boolean\r\n              threshold: 'not-number', // Should be number\r\n            },\r\n          },\r\n        })\r\n        .expect(400);\r\n\r\n      expect(response.body.details).toEqual(\r\n        expect.arrayContaining([\r\n          expect.objectContaining({\r\n            field: 'metadata.tags',\r\n            message: expect.stringContaining('array'),\r\n          }),\r\n          expect.objectContaining({\r\n            field: 'metadata.priority',\r\n            message: expect.stringContaining('number'),\r\n          }),\r\n          expect.objectContaining({\r\n            field: 'metadata.settings.enabled',\r\n            message: expect.stringContaining('boolean'),\r\n          }),\r\n          expect.objectContaining({\r\n            field: 'metadata.settings.threshold',\r\n            message: expect.stringContaining('number'),\r\n          }),\r\n        ])\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('Database Errors', () => {\r\n    it('should handle database connection errors gracefully', async () => {\r\n      // This would require mocking database failures\r\n      // In a real test, you might temporarily disconnect the database\r\n      \r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/test/items')\r\n        .set('Authorization', `Bearer ${authToken}`);\r\n\r\n      // Should either succeed or return appropriate error\r\n      if (response.status === 503) {\r\n        expect(response.body).toEqual({\r\n          statusCode: 503,\r\n          message: 'Service temporarily unavailable',\r\n          error: 'Service Unavailable',\r\n          timestamp: expect.any(String),\r\n          path: '/api/v1/test/items',\r\n          method: 'GET',\r\n          requestId: expect.any(String),\r\n        });\r\n      } else {\r\n        expect(response.status).toBe(200);\r\n      }\r\n    });\r\n\r\n    it('should handle database constraint violations', async () => {\r\n      // Create an item with unique constraint\r\n      await request(app.getHttpServer())\r\n        .post('/api/v1/test/unique-items')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send({\r\n          uniqueCode: 'UNIQUE-001',\r\n          name: 'Unique Item',\r\n        })\r\n        .expect(201);\r\n\r\n      // Try to create another item with same unique code\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/test/unique-items')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send({\r\n          uniqueCode: 'UNIQUE-001',\r\n          name: 'Another Unique Item',\r\n        })\r\n        .expect(409);\r\n\r\n      expect(response.body.message).toContain('already exists');\r\n    });\r\n\r\n    it('should handle database timeout errors', async () => {\r\n      // This would require mocking slow database operations\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/test/slow-query')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .timeout(1000);\r\n\r\n      // Should handle timeout gracefully\r\n      if (response.status === 408) {\r\n        expect(response.body.statusCode).toBe(408);\r\n        expect(response.body.error).toBe('Request Timeout');\r\n      }\r\n    });\r\n  });\r\n\r\n  describe('External Service Errors', () => {\r\n    it('should handle external API failures', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/threat-intelligence/external-data')\r\n        .set('Authorization', `Bearer ${authToken}`);\r\n\r\n      // Should handle external service failures gracefully\r\n      if (response.status === 503) {\r\n        expect(response.body).toEqual({\r\n          statusCode: 503,\r\n          message: 'External service unavailable',\r\n          error: 'Service Unavailable',\r\n          timestamp: expect.any(String),\r\n          path: '/api/v1/threat-intelligence/external-data',\r\n          method: 'GET',\r\n          requestId: expect.any(String),\r\n        });\r\n      }\r\n    });\r\n\r\n    it('should handle external API timeout', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/vulnerability-management/scan-external')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .timeout(5000);\r\n\r\n      // Should handle external API timeouts\r\n      if (response.status === 504) {\r\n        expect(response.body.statusCode).toBe(504);\r\n        expect(response.body.error).toBe('Gateway Timeout');\r\n      }\r\n    });\r\n\r\n    it('should handle malformed external API responses', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/analytics/external-metrics')\r\n        .set('Authorization', `Bearer ${authToken}`);\r\n\r\n      // Should handle malformed responses gracefully\r\n      expect([200, 502, 503]).toContain(response.status);\r\n      \r\n      if (response.status === 502) {\r\n        expect(response.body.error).toBe('Bad Gateway');\r\n      }\r\n    });\r\n  });\r\n\r\n  describe('Authentication Errors', () => {\r\n    it('should handle expired tokens', async () => {\r\n      const expiredToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.invalid';\r\n      \r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/auth/profile')\r\n        .set('Authorization', `Bearer ${expiredToken}`)\r\n        .expect(401);\r\n\r\n      expect(response.body.message).toContain('expired');\r\n    });\r\n\r\n    it('should handle malformed tokens', async () => {\r\n      const malformedTokens = [\r\n        'not-a-jwt-token',\r\n        'header.payload', // Missing signature\r\n        'header.payload.signature.extra', // Too many parts\r\n        '', // Empty token\r\n      ];\r\n\r\n      for (const token of malformedTokens) {\r\n        const response = await request(app.getHttpServer())\r\n          .get('/api/v1/auth/profile')\r\n          .set('Authorization', `Bearer ${token}`)\r\n          .expect(401);\r\n\r\n        expect(response.body.message).toContain('Invalid token');\r\n      }\r\n    });\r\n\r\n    it('should handle revoked tokens', async () => {\r\n      // Login to get a token\r\n      const loginResponse = await request(app.getHttpServer())\r\n        .post('/api/v1/auth/login')\r\n        .send({\r\n          email: '<EMAIL>',\r\n          password: 'SecurePassword123!',\r\n        })\r\n        .expect(200);\r\n\r\n      const token = loginResponse.body.data.tokens.accessToken;\r\n\r\n      // Logout to revoke the token\r\n      await request(app.getHttpServer())\r\n        .post('/api/v1/auth/logout')\r\n        .set('Authorization', `Bearer ${token}`)\r\n        .expect(200);\r\n\r\n      // Try to use the revoked token\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/auth/profile')\r\n        .set('Authorization', `Bearer ${token}`)\r\n        .expect(401);\r\n\r\n      expect(response.body.message).toContain('Invalid token');\r\n    });\r\n  });\r\n\r\n  describe('File Upload Errors', () => {\r\n    it('should handle unsupported file types', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/test/upload')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .attach('file', Buffer.from('<?php echo \"malicious\"; ?>'), {\r\n          filename: 'malicious.php',\r\n          contentType: 'application/x-php',\r\n        })\r\n        .expect(415);\r\n\r\n      expect(response.body.message).toContain('file type not supported');\r\n    });\r\n\r\n    it('should handle file size limits', async () => {\r\n      const largeFile = Buffer.alloc(10 * 1024 * 1024); // 10MB file\r\n      \r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/test/upload')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .attach('file', largeFile, {\r\n          filename: 'large-file.txt',\r\n          contentType: 'text/plain',\r\n        });\r\n\r\n      // Should either accept or reject based on size limits\r\n      if (response.status === 413) {\r\n        expect(response.body.message).toContain('file too large');\r\n      }\r\n    });\r\n\r\n    it('should handle corrupted files', async () => {\r\n      const corruptedFile = Buffer.from([0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10]); // Corrupted JPEG header\r\n      \r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/test/upload')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .attach('file', corruptedFile, {\r\n          filename: 'corrupted.jpg',\r\n          contentType: 'image/jpeg',\r\n        });\r\n\r\n      // Should handle corrupted files gracefully\r\n      expect([400, 422]).toContain(response.status);\r\n    });\r\n  });\r\n\r\n  describe('Concurrent Request Errors', () => {\r\n    it('should handle race conditions gracefully', async () => {\r\n      // Create multiple concurrent requests that might cause race conditions\r\n      const requests = Array.from({ length: 10 }, () =>\r\n        request(app.getHttpServer())\r\n          .post('/api/v1/test/counter-items')\r\n          .set('Authorization', `Bearer ${authToken}`)\r\n          .send({\r\n            name: 'Counter Item',\r\n            increment: 1,\r\n          })\r\n      );\r\n\r\n      const responses = await Promise.all(\r\n        requests.map(req => \r\n          req.then(res => ({ status: res.status, body: res.body }))\r\n            .catch(err => ({ \r\n              status: err.response?.status || 500, \r\n              body: err.response?.body || {}\r\n            }))\r\n        )\r\n      );\r\n\r\n      // All requests should either succeed or fail gracefully\r\n      responses.forEach(response => {\r\n        expect([200, 201, 409, 500]).toContain(response.status);\r\n      });\r\n    });\r\n\r\n    it('should handle deadlock situations', async () => {\r\n      // This would require specific database operations that could cause deadlocks\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/test/complex-transaction')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send({\r\n          operations: [\r\n            { type: 'create', data: { name: 'Item 1' } },\r\n            { type: 'update', id: 'existing-id', data: { name: 'Updated' } },\r\n            { type: 'delete', id: 'another-id' },\r\n          ],\r\n        });\r\n\r\n      // Should handle deadlocks gracefully\r\n      expect([200, 409, 500]).toContain(response.status);\r\n    });\r\n  });\r\n\r\n  describe('Memory and Resource Errors', () => {\r\n    it('should handle memory exhaustion gracefully', async () => {\r\n      // Try to create a request that might cause memory issues\r\n      const memoryIntensivePayload = {\r\n        name: 'Memory Test',\r\n        data: Array.from({ length: 100000 }, (_, i) => ({\r\n          id: i,\r\n          value: 'x'.repeat(1000),\r\n          nested: {\r\n            deep: {\r\n              data: Array.from({ length: 100 }, (_, j) => `item-${j}`),\r\n            },\r\n          },\r\n        })),\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/test/memory-intensive')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send(memoryIntensivePayload);\r\n\r\n      // Should either process or reject gracefully\r\n      expect([201, 413, 500]).toContain(response.status);\r\n    });\r\n\r\n    it('should handle CPU intensive operations', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/test/cpu-intensive')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send({\r\n          iterations: 1000000,\r\n          complexity: 'high',\r\n        })\r\n        .timeout(10000);\r\n\r\n      // Should handle CPU intensive operations within reasonable time\r\n      expect([200, 408, 503]).toContain(response.status);\r\n    });\r\n  });\r\n\r\n  describe('Error Response Consistency', () => {\r\n    it('should maintain consistent error format across all endpoints', async () => {\r\n      const errorEndpoints = [\r\n        { method: 'get', path: '/api/v1/nonexistent', expectedStatus: 404 },\r\n        { method: 'post', path: '/api/v1/test/items', body: {}, expectedStatus: 400 },\r\n        { method: 'get', path: '/api/v1/auth/profile', expectedStatus: 401 },\r\n        { method: 'get', path: '/api/v1/admin/users', expectedStatus: 403 },\r\n      ];\r\n\r\n      for (const endpoint of errorEndpoints) {\r\n        let req = request(app.getHttpServer())[endpoint.method](endpoint.path);\r\n        \r\n        if (endpoint.path.includes('admin') || endpoint.path.includes('auth/profile')) {\r\n          // Don't set auth for these to test 401/403\r\n        } else if (endpoint.body) {\r\n          req = req.set('Authorization', `Bearer ${authToken}`).send(endpoint.body);\r\n        } else {\r\n          req = req.set('Authorization', `Bearer ${authToken}`);\r\n        }\r\n\r\n        const response = await req.expect(endpoint.expectedStatus);\r\n\r\n        // All error responses should have consistent structure\r\n        expect(response.body).toEqual({\r\n          statusCode: endpoint.expectedStatus,\r\n          message: expect.any(String),\r\n          error: expect.any(String),\r\n          timestamp: expect.any(String),\r\n          path: endpoint.path,\r\n          method: endpoint.method.toUpperCase(),\r\n          requestId: expect.any(String),\r\n        });\r\n      }\r\n    });\r\n\r\n    it('should include correlation IDs in error responses', async () => {\r\n      const correlationId = 'test-correlation-123';\r\n      \r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/nonexistent')\r\n        .set('X-Correlation-ID', correlationId)\r\n        .expect(404);\r\n\r\n      expect(response.body.requestId).toBeDefined();\r\n      // In a real implementation, correlation ID would be included\r\n    });\r\n\r\n    it('should not expose sensitive information in errors', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/test/database-error')\r\n        .set('Authorization', `Bearer ${authToken}`);\r\n\r\n      // Should not expose database connection strings, internal paths, etc.\r\n      expect(response.body.message).not.toContain('password');\r\n      expect(response.body.message).not.toContain('localhost');\r\n      expect(response.body.message).not.toContain('/home/');\r\n      expect(response.body.message).not.toContain('node_modules');\r\n      expect(response.body).not.toHaveProperty('stack');\r\n    });\r\n  });\r\n});"], "version": 3}