18ee56abd5b1f969f72e2803f45be376
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const is_ip_address_validator_1 = require("../is-ip-address.validator");
class TestIpDto {
}
__decorate([
    (0, is_ip_address_validator_1.IsIpAddress)(),
    __metadata("design:type", String)
], TestIpDto.prototype, "ipAddress", void 0);
__decorate([
    (0, is_ip_address_validator_1.IsIPv4Address)(),
    __metadata("design:type", String)
], TestIpDto.prototype, "ipv4Address", void 0);
__decorate([
    (0, is_ip_address_validator_1.IsIPv6Address)(),
    __metadata("design:type", String)
], TestIpDto.prototype, "ipv6Address", void 0);
__decorate([
    (0, is_ip_address_validator_1.IsPublicIpAddress)(),
    __metadata("design:type", String)
], TestIpDto.prototype, "publicIpAddress", void 0);
describe('IP Address Validator', () => {
    let constraint;
    beforeEach(() => {
        constraint = new is_ip_address_validator_1.IsIpAddressConstraint();
    });
    describe('IsIpAddressConstraint', () => {
        describe('validate', () => {
            it('should validate IPv4 addresses', () => {
                const validIPv4 = [
                    '***********',
                    '********',
                    '**********',
                    '*******',
                    '127.0.0.1',
                    '***************',
                ];
                validIPv4.forEach(ip => {
                    expect(constraint.validate(ip, { constraints: [{}] })).toBe(true);
                });
            });
            it('should validate reserved IPv4 addresses when allowed', () => {
                const reservedIPv4 = ['0.0.0.0'];
                const options = { allowReserved: true };
                reservedIPv4.forEach(ip => {
                    expect(constraint.validate(ip, { constraints: [options] })).toBe(true);
                });
            });
            it('should validate IPv6 addresses', () => {
                const validIPv6 = [
                    '::1',
                    'fe80::1',
                    '2001:4860:4860::8888', // Google DNS
                    'fc00::1', // Unique Local Address
                ];
                validIPv6.forEach(ip => {
                    expect(constraint.validate(ip, { constraints: [{}] })).toBe(true);
                });
            });
            it('should validate reserved IPv6 addresses when allowed', () => {
                const reservedIPv6 = [
                    '::',
                    '2001:db8::1',
                    '2001:0db8:85a3:0000:0000:8a2e:0370:7334',
                    '2001:db8:85a3::8a2e:370:7334',
                ];
                const options = { allowReserved: true };
                reservedIPv6.forEach(ip => {
                    expect(constraint.validate(ip, { constraints: [options] })).toBe(true);
                });
            });
            it('should reject invalid IP addresses', () => {
                const invalidIPs = [
                    '256.1.1.1', // Invalid IPv4
                    '192.168.1', // Incomplete IPv4
                    '***********.1', // Too many octets
                    'invalid', // Not an IP
                    '', // Empty string
                    null, // Null value
                    undefined, // Undefined value
                    123, // Number instead of string
                    'gggg::1', // Invalid IPv6
                ];
                invalidIPs.forEach(ip => {
                    expect(constraint.validate(ip, { constraints: [{}] })).toBe(false);
                });
            });
            it('should respect allowIPv4 option', () => {
                const options = { allowIPv4: false, allowIPv6: true };
                expect(constraint.validate('***********', { constraints: [options] })).toBe(false);
                expect(constraint.validate('::1', { constraints: [options] })).toBe(true);
            });
            it('should respect allowIPv6 option', () => {
                const options = { allowIPv4: true, allowIPv6: false };
                expect(constraint.validate('***********', { constraints: [options] })).toBe(true);
                expect(constraint.validate('::1', { constraints: [options] })).toBe(false);
            });
            it('should respect allowPrivate option', () => {
                const options = { allowPrivate: false };
                expect(constraint.validate('***********', { constraints: [options] })).toBe(false);
                expect(constraint.validate('*******', { constraints: [options] })).toBe(true);
            });
            it('should respect allowLoopback option', () => {
                const options = { allowLoopback: false };
                expect(constraint.validate('127.0.0.1', { constraints: [options] })).toBe(false);
                expect(constraint.validate('::1', { constraints: [options] })).toBe(false);
                expect(constraint.validate('*******', { constraints: [options] })).toBe(true);
            });
        });
    });
    describe('IpAddressUtils', () => {
        describe('getVersion', () => {
            it('should return correct IP version', () => {
                expect(is_ip_address_validator_1.IpAddressUtils.getVersion('***********')).toBe(4);
                expect(is_ip_address_validator_1.IpAddressUtils.getVersion('::1')).toBe(6);
                expect(is_ip_address_validator_1.IpAddressUtils.getVersion('invalid')).toBe(0);
            });
        });
        describe('isValid', () => {
            it('should validate IP addresses correctly', () => {
                expect(is_ip_address_validator_1.IpAddressUtils.isValid('***********')).toBe(true);
                expect(is_ip_address_validator_1.IpAddressUtils.isValid('::1')).toBe(true);
                expect(is_ip_address_validator_1.IpAddressUtils.isValid('invalid')).toBe(false);
            });
            it('should respect validation options', () => {
                expect(is_ip_address_validator_1.IpAddressUtils.isValid('***********', { allowPrivate: false })).toBe(false);
                expect(is_ip_address_validator_1.IpAddressUtils.isValid('*******', { allowPrivate: false })).toBe(true);
            });
        });
        describe('normalize', () => {
            it('should normalize IPv4 addresses', () => {
                expect(is_ip_address_validator_1.IpAddressUtils.normalize('***********')).toBe('***********');
            });
            it('should normalize IPv6 addresses', () => {
                const normalized = is_ip_address_validator_1.IpAddressUtils.normalize('2001:4860:4860::8888');
                expect(normalized).toBeTruthy();
                expect(normalized?.includes(':')).toBe(true);
            });
            it('should return null for invalid addresses', () => {
                expect(is_ip_address_validator_1.IpAddressUtils.normalize('invalid')).toBeNull();
            });
        });
        describe('isInCidrRange', () => {
            it('should check IPv4 CIDR ranges correctly', () => {
                expect(is_ip_address_validator_1.IpAddressUtils.isInCidrRange('***********00', '***********/24')).toBe(true);
                expect(is_ip_address_validator_1.IpAddressUtils.isInCidrRange('*************', '***********/24')).toBe(false);
                expect(is_ip_address_validator_1.IpAddressUtils.isInCidrRange('********', '10.0.0.0/8')).toBe(true);
            });
            it('should handle different IP versions', () => {
                expect(is_ip_address_validator_1.IpAddressUtils.isInCidrRange('***********', '::1/128')).toBe(false);
            });
        });
    });
    describe('DTO Validation', () => {
        it('should validate DTO with valid IP addresses', async () => {
            const dto = (0, class_transformer_1.plainToClass)(TestIpDto, {
                ipAddress: '***********',
                ipv4Address: '********',
                ipv6Address: '::1',
                publicIpAddress: '*******',
            });
            const errors = await (0, class_validator_1.validate)(dto);
            expect(errors).toHaveLength(0);
        });
        it('should reject DTO with invalid IP addresses', async () => {
            const dto = (0, class_transformer_1.plainToClass)(TestIpDto, {
                ipAddress: 'invalid-ip',
                ipv4Address: '::1', // IPv6 in IPv4 field
                ipv6Address: '***********', // IPv4 in IPv6 field
                publicIpAddress: '***********', // Private IP in public field
            });
            const errors = await (0, class_validator_1.validate)(dto);
            expect(errors.length).toBeGreaterThan(0);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************