7b683976c69724f794a0a681ac0ddc09
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const event_timestamp_value_object_1 = require("../event-timestamp.value-object");
describe('EventTimestamp Value Object', () => {
    describe('creation', () => {
        it('should create a valid event timestamp with current time', () => {
            // Act
            const timestamp = event_timestamp_value_object_1.EventTimestamp.create();
            // Assert
            expect(timestamp).toBeDefined();
            expect(timestamp.occurredAt).toBeInstanceOf(Date);
            expect(timestamp.receivedAt).toBeInstanceOf(Date);
            expect(timestamp.precision).toBe('millisecond');
        });
        it('should create a valid event timestamp from a specific date', () => {
            // Arrange
            const specificDate = new Date('2024-01-15T10:30:00.000Z');
            // Act
            const timestamp = event_timestamp_value_object_1.EventTimestamp.fromDate(specificDate);
            // Assert
            expect(timestamp.occurredAt).toEqual(specificDate);
            expect(timestamp.receivedAt).toBeInstanceOf(Date);
            expect(timestamp.originalTimestamp).toBeUndefined();
        });
        it('should create a valid event timestamp from ISO string', () => {
            // Arrange
            const isoString = '2024-01-15T10:30:00.000Z';
            // Act
            const timestamp = event_timestamp_value_object_1.EventTimestamp.fromISOString(isoString);
            // Assert
            expect(timestamp.occurredAt).toEqual(new Date(isoString));
            expect(timestamp.originalTimestamp).toBe(isoString);
        });
        it('should create a valid event timestamp from Unix timestamp', () => {
            // Arrange
            const unixTimestamp = 1705316200; // 2024-01-15T10:30:00.000Z
            // Act
            const timestamp = event_timestamp_value_object_1.EventTimestamp.fromUnixTimestamp(unixTimestamp);
            // Assert
            expect(timestamp.occurredAt).toEqual(new Date(unixTimestamp * 1000));
            expect(timestamp.originalTimestamp).toBe(unixTimestamp.toString());
        });
        it('should create a valid event timestamp from original string', () => {
            // Arrange
            const originalString = '2024-01-15T10:30:00.000Z';
            // Act
            const timestamp = event_timestamp_value_object_1.EventTimestamp.fromOriginalString(originalString);
            // Assert
            expect(timestamp.occurredAt).toEqual(new Date(originalString));
            expect(timestamp.originalTimestamp).toBe(originalString);
            expect(timestamp.receivedAt).toBeInstanceOf(Date);
        });
    });
    describe('validation', () => {
        it('should throw error when occurredAt is not provided', () => {
            // Act & Assert
            expect(() => {
                new event_timestamp_value_object_1.EventTimestamp({});
            }).toThrow('Event timestamp must have an occurredAt date');
        });
        it('should throw error when occurredAt is not a Date object', () => {
            // Act & Assert
            expect(() => {
                new event_timestamp_value_object_1.EventTimestamp({ occurredAt: 'invalid' });
            }).toThrow('occurredAt must be a valid Date object');
        });
        it('should throw error when occurredAt is an invalid date', () => {
            // Act & Assert
            expect(() => {
                new event_timestamp_value_object_1.EventTimestamp({ occurredAt: new Date('invalid') });
            }).toThrow('occurredAt must be a valid date');
        });
        it('should throw error when receivedAt is invalid', () => {
            // Act & Assert
            expect(() => {
                new event_timestamp_value_object_1.EventTimestamp({
                    occurredAt: new Date(),
                    receivedAt: new Date('invalid'),
                });
            }).toThrow('receivedAt must be a valid Date object');
        });
        it('should throw error when processedAt is invalid', () => {
            // Act & Assert
            expect(() => {
                new event_timestamp_value_object_1.EventTimestamp({
                    occurredAt: new Date(),
                    processedAt: new Date('invalid'),
                });
            }).toThrow('processedAt must be a valid Date object');
        });
        it('should throw error when timezone offset is out of range', () => {
            // Act & Assert
            expect(() => {
                new event_timestamp_value_object_1.EventTimestamp({
                    occurredAt: new Date(),
                    timezoneOffset: 800, // Invalid: > 720
                });
            }).toThrow('Timezone offset must be an integer between -720 and 720 minutes');
            expect(() => {
                new event_timestamp_value_object_1.EventTimestamp({
                    occurredAt: new Date(),
                    timezoneOffset: -800, // Invalid: < -720
                });
            }).toThrow('Timezone offset must be an integer between -720 and 720 minutes');
        });
        it('should throw error when timestamp is too far in the future', () => {
            // Arrange
            const futureDate = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes in future
            // Act & Assert
            expect(() => {
                new event_timestamp_value_object_1.EventTimestamp({ occurredAt: futureDate });
            }).toThrow('Event timestamp cannot be more than 5 minutes in the future');
        });
        it('should throw error when timestamp is too far in the past', () => {
            // Arrange
            const pastDate = new Date(Date.now() - 2 * 365 * 24 * 60 * 60 * 1000); // 2 years ago
            // Act & Assert
            expect(() => {
                new event_timestamp_value_object_1.EventTimestamp({ occurredAt: pastDate });
            }).toThrow('Event timestamp cannot be more than 1 year in the past');
        });
        it('should throw error when occurredAt is significantly after receivedAt', () => {
            // Arrange
            const receivedAt = new Date();
            const occurredAt = new Date(receivedAt.getTime() + 2 * 60 * 1000); // 2 minutes after
            // Act & Assert
            expect(() => {
                new event_timestamp_value_object_1.EventTimestamp({ occurredAt, receivedAt });
            }).toThrow('Event occurredAt cannot be significantly after receivedAt');
        });
        it('should throw error when receivedAt is after processedAt', () => {
            // Arrange
            const processedAt = new Date();
            const receivedAt = new Date(processedAt.getTime() + 60 * 1000); // 1 minute after
            // Act & Assert
            expect(() => {
                new event_timestamp_value_object_1.EventTimestamp({
                    occurredAt: new Date(processedAt.getTime() - 60 * 1000),
                    receivedAt,
                    processedAt,
                });
            }).toThrow('Event receivedAt cannot be after processedAt');
        });
    });
    describe('age calculations', () => {
        it('should calculate event age correctly', () => {
            // Arrange
            const pastDate = new Date(Date.now() - 5000); // 5 seconds ago
            const timestamp = event_timestamp_value_object_1.EventTimestamp.fromDate(pastDate);
            // Act
            const age = timestamp.getAge();
            // Assert
            expect(age).toBeGreaterThanOrEqual(4900); // Allow some tolerance
            expect(age).toBeLessThanOrEqual(5100);
        });
        it('should calculate age in seconds correctly', () => {
            // Arrange
            const pastDate = new Date(Date.now() - 5000); // 5 seconds ago
            const timestamp = event_timestamp_value_object_1.EventTimestamp.fromDate(pastDate);
            // Act
            const ageInSeconds = timestamp.getAgeInSeconds();
            // Assert
            expect(ageInSeconds).toBeGreaterThanOrEqual(4);
            expect(ageInSeconds).toBeLessThanOrEqual(6);
        });
        it('should calculate age in minutes correctly', () => {
            // Arrange
            const pastDate = new Date(Date.now() - 3 * 60 * 1000); // 3 minutes ago
            const timestamp = event_timestamp_value_object_1.EventTimestamp.fromDate(pastDate);
            // Act
            const ageInMinutes = timestamp.getAgeInMinutes();
            // Assert
            expect(ageInMinutes).toBeGreaterThanOrEqual(2);
            expect(ageInMinutes).toBeLessThanOrEqual(4);
        });
        it('should calculate age in hours correctly', () => {
            // Arrange
            const pastDate = new Date(Date.now() - 2 * 60 * 60 * 1000); // 2 hours ago
            const timestamp = event_timestamp_value_object_1.EventTimestamp.fromDate(pastDate);
            // Act
            const ageInHours = timestamp.getAgeInHours();
            // Assert
            expect(ageInHours).toBeGreaterThanOrEqual(1);
            expect(ageInHours).toBeLessThanOrEqual(3);
        });
    });
    describe('freshness checks', () => {
        it('should identify recent events correctly', () => {
            // Arrange
            const recentDate = new Date(Date.now() - 2 * 60 * 1000); // 2 minutes ago
            const timestamp = event_timestamp_value_object_1.EventTimestamp.fromDate(recentDate);
            // Act & Assert
            expect(timestamp.isRecent()).toBe(true);
            expect(timestamp.isRecent(60000)).toBe(false); // Within 1 minute
        });
        it('should identify stale events correctly', () => {
            // Arrange
            const staleDate = new Date(Date.now() - 25 * 60 * 60 * 1000); // 25 hours ago
            const timestamp = event_timestamp_value_object_1.EventTimestamp.fromDate(staleDate);
            // Act & Assert
            expect(timestamp.isStale()).toBe(true);
            expect(timestamp.isStale(48 * 60 * 60 * 1000)).toBe(false); // Within 48 hours
        });
        it('should identify future events correctly', () => {
            // Arrange
            const futureDate = new Date(Date.now() + 2 * 60 * 1000); // 2 minutes in future (within tolerance)
            const timestamp = event_timestamp_value_object_1.EventTimestamp.fromDate(futureDate);
            // Act & Assert
            expect(timestamp.isInFuture()).toBe(true);
        });
    });
    describe('delay calculations', () => {
        it('should calculate processing delay correctly', () => {
            // Arrange
            const occurredAt = new Date();
            const receivedAt = new Date(occurredAt.getTime() + 1000); // 1 second later
            const timestamp = new event_timestamp_value_object_1.EventTimestamp({ occurredAt, receivedAt });
            // Act
            const delay = timestamp.getProcessingDelay();
            // Assert
            expect(delay).toBe(1000);
        });
        it('should return null for processing delay when receivedAt is not set', () => {
            // Arrange
            const timestamp = event_timestamp_value_object_1.EventTimestamp.fromDate(new Date());
            // Act
            const delay = timestamp.getProcessingDelay();
            // Assert
            expect(delay).toBeNull();
        });
        it('should calculate ingestion delay correctly', () => {
            // Arrange
            const occurredAt = new Date();
            const receivedAt = new Date(occurredAt.getTime() + 1000);
            const processedAt = new Date(receivedAt.getTime() + 500);
            const timestamp = new event_timestamp_value_object_1.EventTimestamp({ occurredAt, receivedAt, processedAt });
            // Act
            const delay = timestamp.getIngestionDelay();
            // Assert
            expect(delay).toBe(500);
        });
        it('should calculate total delay correctly', () => {
            // Arrange
            const occurredAt = new Date();
            const processedAt = new Date(occurredAt.getTime() + 1500);
            const timestamp = new event_timestamp_value_object_1.EventTimestamp({ occurredAt, processedAt });
            // Act
            const delay = timestamp.getTotalDelay();
            // Assert
            expect(delay).toBe(1500);
        });
    });
    describe('clock skew detection', () => {
        it('should detect clock skew correctly', () => {
            // Arrange
            const occurredAt = new Date();
            const receivedAt = new Date(occurredAt.getTime() - 2 * 60 * 1000); // 2 minutes before
            const timestamp = new event_timestamp_value_object_1.EventTimestamp({ occurredAt, receivedAt });
            // Act & Assert
            expect(timestamp.hasClockSkew()).toBe(true);
            expect(timestamp.hasClockSkew(3 * 60 * 1000)).toBe(false); // 3 minute tolerance
        });
        it('should not detect clock skew for normal delays', () => {
            // Arrange
            const occurredAt = new Date();
            const receivedAt = new Date(occurredAt.getTime() + 30 * 1000); // 30 seconds later
            const timestamp = new event_timestamp_value_object_1.EventTimestamp({ occurredAt, receivedAt });
            // Act & Assert
            expect(timestamp.hasClockSkew()).toBe(false);
        });
    });
    describe('timezone handling', () => {
        it('should convert to UTC correctly', () => {
            // Arrange
            const occurredAt = new Date('2024-01-15T10:30:00.000Z');
            const timezoneOffset = -300; // EST (UTC-5)
            const timestamp = new event_timestamp_value_object_1.EventTimestamp({ occurredAt, timezoneOffset });
            // Act
            const utc = timestamp.toUTC();
            // Assert
            expect(utc.getTime()).toBe(occurredAt.getTime() + (timezoneOffset * 60000));
        });
        it('should convert to specific timezone correctly', () => {
            // Arrange
            const occurredAt = new Date('2024-01-15T10:30:00.000Z');
            const timestamp = new event_timestamp_value_object_1.EventTimestamp({ occurredAt });
            // Act
            const pst = timestamp.toTimezone(480); // PST (UTC-8)
            // Assert
            expect(pst.getTime()).toBe(occurredAt.getTime() - (480 * 60000));
        });
    });
    describe('immutability and updates', () => {
        it('should create new instance when marking as received', () => {
            // Arrange
            const original = event_timestamp_value_object_1.EventTimestamp.fromDate(new Date());
            // Act
            const updated = original.markAsReceived();
            // Assert
            expect(updated).not.toBe(original);
            expect(updated.receivedAt).toBeInstanceOf(Date);
            expect(updated.occurredAt).toEqual(original.occurredAt);
        });
        it('should create new instance when marking as processed', () => {
            // Arrange
            const original = event_timestamp_value_object_1.EventTimestamp.fromDate(new Date());
            // Act
            const updated = original.markAsProcessed();
            // Assert
            expect(updated).not.toBe(original);
            expect(updated.processedAt).toBeInstanceOf(Date);
            expect(updated.occurredAt).toEqual(original.occurredAt);
        });
        it('should create new instance with processing timestamps', () => {
            // Arrange
            const original = event_timestamp_value_object_1.EventTimestamp.fromDate(new Date());
            const receivedAt = new Date();
            const processedAt = new Date();
            // Act
            const updated = original.withProcessingTimestamps(receivedAt, processedAt);
            // Assert
            expect(updated).not.toBe(original);
            expect(updated.receivedAt).toEqual(receivedAt);
            expect(updated.processedAt).toEqual(processedAt);
        });
    });
    describe('serialization', () => {
        it('should convert to ISO string correctly', () => {
            // Arrange
            const date = new Date('2024-01-15T10:30:00.000Z');
            const timestamp = event_timestamp_value_object_1.EventTimestamp.fromDate(date);
            // Act
            const isoString = timestamp.toISOString();
            // Assert
            expect(isoString).toBe('2024-01-15T10:30:00.000Z');
        });
        it('should convert to Unix timestamp correctly', () => {
            // Arrange
            const date = new Date('2024-01-15T10:30:00.000Z');
            const timestamp = event_timestamp_value_object_1.EventTimestamp.fromDate(date);
            // Act
            const unixTimestamp = timestamp.toUnixTimestamp();
            // Assert
            expect(unixTimestamp).toBe(Math.floor(date.getTime() / 1000));
        });
        it('should convert to JSON correctly', () => {
            // Arrange
            const occurredAt = new Date('2024-01-15T10:30:00.000Z');
            const receivedAt = new Date('2024-01-15T10:30:01.000Z');
            const timestamp = new event_timestamp_value_object_1.EventTimestamp({
                occurredAt,
                receivedAt,
                timezoneOffset: -300,
                originalTimestamp: '2024-01-15T10:30:00.000Z',
                precision: 'millisecond',
            });
            // Act
            const json = timestamp.toJSON();
            // Assert
            expect(json.occurredAt).toBe('2024-01-15T10:30:00.000Z');
            expect(json.receivedAt).toBe('2024-01-15T10:30:01.000Z');
            expect(json.timezoneOffset).toBe(-300);
            expect(json.originalTimestamp).toBe('2024-01-15T10:30:00.000Z');
            expect(json.precision).toBe('millisecond');
            expect(json.summary).toBeDefined();
        });
        it('should create from JSON correctly', () => {
            // Arrange
            const json = {
                occurredAt: '2024-01-15T10:30:00.000Z',
                receivedAt: '2024-01-15T10:30:01.000Z',
                timezoneOffset: -300,
                originalTimestamp: '2024-01-15T10:30:00.000Z',
                precision: 'millisecond',
            };
            // Act
            const timestamp = event_timestamp_value_object_1.EventTimestamp.fromJSON(json);
            // Assert
            expect(timestamp.occurredAt).toEqual(new Date('2024-01-15T10:30:00.000Z'));
            expect(timestamp.receivedAt).toEqual(new Date('2024-01-15T10:30:01.000Z'));
            expect(timestamp.timezoneOffset).toBe(-300);
            expect(timestamp.originalTimestamp).toBe('2024-01-15T10:30:00.000Z');
            expect(timestamp.precision).toBe('millisecond');
        });
    });
    describe('equality and comparison', () => {
        it('should compare timestamps for equality correctly', () => {
            // Arrange
            const date = new Date('2024-01-15T10:30:00.000Z');
            const timestamp1 = event_timestamp_value_object_1.EventTimestamp.fromDate(date);
            const timestamp2 = event_timestamp_value_object_1.EventTimestamp.fromDate(date);
            const timestamp3 = event_timestamp_value_object_1.EventTimestamp.fromDate(new Date('2024-01-15T10:31:00.000Z'));
            // Act & Assert
            expect(timestamp1.equals(timestamp2)).toBe(true);
            expect(timestamp1.equals(timestamp3)).toBe(false);
            expect(timestamp1.equals(undefined)).toBe(false);
            expect(timestamp1.equals(timestamp1)).toBe(true);
        });
        it('should convert to string correctly', () => {
            // Arrange
            const date = new Date('2024-01-15T10:30:00.000Z');
            const timestamp = event_timestamp_value_object_1.EventTimestamp.fromDate(date);
            // Act
            const str = timestamp.toString();
            // Assert
            expect(str).toBe('2024-01-15T10:30:00.000Z');
        });
    });
    describe('validation utility', () => {
        it('should validate timestamp without creating instance', () => {
            // Arrange
            const validDate = new Date();
            const invalidDate = new Date('invalid');
            // Act & Assert
            expect(event_timestamp_value_object_1.EventTimestamp.isValid(validDate)).toBe(true);
            expect(event_timestamp_value_object_1.EventTimestamp.isValid(invalidDate)).toBe(false);
        });
    });
    describe('timing summary', () => {
        it('should provide comprehensive timing summary', () => {
            // Arrange
            const occurredAt = new Date('2024-01-15T10:30:00.000Z');
            const receivedAt = new Date('2024-01-15T10:30:01.000Z');
            const processedAt = new Date('2024-01-15T10:30:02.000Z');
            const timestamp = new event_timestamp_value_object_1.EventTimestamp({ occurredAt, receivedAt, processedAt });
            // Act
            const summary = timestamp.getTimingSummary();
            // Assert
            expect(summary.occurredAt).toBe('2024-01-15T10:30:00.000Z');
            expect(summary.receivedAt).toBe('2024-01-15T10:30:01.000Z');
            expect(summary.processedAt).toBe('2024-01-15T10:30:02.000Z');
            expect(summary.processingDelay).toBe(1000);
            expect(summary.ingestionDelay).toBe(1000);
            expect(summary.totalDelay).toBe(2000);
            expect(summary.hasClockSkew).toBe(false);
            expect(typeof summary.age).toBe('number');
            expect(typeof summary.isRecent).toBe('boolean');
            expect(typeof summary.isStale).toBe('boolean');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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