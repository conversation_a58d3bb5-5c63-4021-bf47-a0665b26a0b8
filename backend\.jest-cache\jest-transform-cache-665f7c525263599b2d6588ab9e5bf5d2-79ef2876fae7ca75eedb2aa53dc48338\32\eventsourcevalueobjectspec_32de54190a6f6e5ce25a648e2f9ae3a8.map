{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\event-metadata\\__tests__\\event-source.value-object.spec.ts", "mappings": ";;AAAA,4EAA2D;AAC3D,kFAAwE;AAExE,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;IACxC,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM;YACN,MAAM,MAAM,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAEtE,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,wCAAe,CAAC,QAAQ,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,wBAAwB;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM;YACN,MAAM,MAAM,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,GAAG,EAAE,eAAe,EAAE;gBACtE,IAAI,EAAE,oBAAoB;gBAC1B,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,aAAa;gBACrB,QAAQ,EAAE,iBAAiB;gBAC3B,QAAQ,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE;aACjC,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,wCAAe,CAAC,GAAG,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM;YACN,MAAM,MAAM,GAAG,uCAAW,CAAC,YAAY,CAAC,wCAAe,CAAC,UAAU,EAAE,mBAAmB,CAAC,CAAC;YAEzF,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,wCAAe,CAAC,UAAU,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM;YACN,MAAM,MAAM,GAAG,uCAAW,CAAC,aAAa,CAAC,wCAAe,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;YAElF,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,wCAAe,CAAC,QAAQ,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM;YACN,MAAM,MAAM,GAAG,uCAAW,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAEjD,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,wCAAe,CAAC,MAAM,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM;YACN,MAAM,MAAM,GAAG,uCAAW,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;YAEzD,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,wCAAe,CAAC,OAAO,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,eAAe;YACf,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,uCAAW,CAAC,EAAE,IAAI,EAAE,IAAW,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC;YAC7D,CAAC,CAAC,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,eAAe;YACf,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,uCAAW,CAAC,EAAE,IAAI,EAAE,cAAqB,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC;YACvE,CAAC,CAAC,CAAC,OAAO,CAAC,yCAAyC,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,eAAe;YACf,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,uCAAW,CAAC,EAAE,IAAI,EAAE,wCAAe,CAAC,QAAQ,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,CAAC;YACtE,CAAC,CAAC,CAAC,OAAO,CAAC,+CAA+C,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;YACxE,eAAe;YACf,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,uCAAW,CAAC,EAAE,IAAI,EAAE,wCAAe,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC,CAAC;YAC7E,CAAC,CAAC,CAAC,OAAO,CAAC,mGAAmG,CAAC,CAAC;QAClH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,eAAe;YACf,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,uCAAW,CAAC,EAAE,IAAI,EAAE,wCAAe,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,CAAC;YAC5E,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,uCAAW,CAAC,EAAE,IAAI,EAAE,wCAAe,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,CAAC;YAC5E,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,uCAAW,CAAC,EAAE,IAAI,EAAE,wCAAe,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,CAAC;YAC5E,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,uCAAW,CAAC,EAAE,IAAI,EAAE,wCAAe,CAAC,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,CAAC,CAAC;YAC3E,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,eAAe;YACf,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,uCAAW,CAAC;oBACd,IAAI,EAAE,wCAAe,CAAC,QAAQ;oBAC9B,UAAU,EAAE,QAAQ;oBACpB,OAAO,EAAE,iBAAiB;iBAC3B,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,yDAAyD,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,eAAe;YACf,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,uCAAW,CAAC;oBACd,IAAI,EAAE,wCAAe,CAAC,QAAQ;oBAC9B,UAAU,EAAE,QAAQ;oBACpB,OAAO,EAAE,OAAO;iBACjB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,uCAAW,CAAC;oBACd,IAAI,EAAE,wCAAe,CAAC,QAAQ;oBAC9B,UAAU,EAAE,QAAQ;oBACpB,OAAO,EAAE,YAAY;iBACtB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,uCAAW,CAAC;oBACd,IAAI,EAAE,wCAAe,CAAC,QAAQ;oBAC9B,UAAU,EAAE,QAAQ;oBACpB,OAAO,EAAE,KAAK;iBACf,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,eAAe;YACf,MAAM,CAAC,GAAG,EAAE;gBACV,IAAI,uCAAW,CAAC;oBACd,IAAI,EAAE,wCAAe,CAAC,QAAQ;oBAC9B,UAAU,EAAE,QAAQ;oBACpB,QAAQ,EAAE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,iBAAiB;iBAC7C,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,sDAAsD,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,UAAU;YACV,MAAM,QAAQ,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACxE,MAAM,MAAM,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YACtE,MAAM,aAAa,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;YAEvF,eAAe;YACf,MAAM,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5C,MAAM,CAAC,aAAa,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,UAAU;YACV,MAAM,GAAG,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAC/D,MAAM,SAAS,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YAC1E,MAAM,EAAE,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,CAAC;YAElF,eAAe;YACf,MAAM,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,UAAU;YACV,MAAM,GAAG,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAC/D,MAAM,QAAQ,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACxE,MAAM,SAAS,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YAE5E,eAAe;YACf,MAAM,CAAC,GAAG,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,SAAS,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,UAAU;YACV,MAAM,GAAG,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC;YACtE,MAAM,KAAK,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;YAC1E,MAAM,GAAG,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,GAAG,EAAE,qBAAqB,CAAC,CAAC;YAE3E,eAAe;YACf,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,UAAU;YACV,MAAM,EAAE,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;YAC3E,MAAM,GAAG,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAC/D,MAAM,GAAG,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAE/D,eAAe;YACf,MAAM,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,UAAU;YACV,MAAM,IAAI,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;YACpE,MAAM,IAAI,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;YACrE,MAAM,WAAW,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,qBAAqB,EAAE,YAAY,CAAC,CAAC;YAE5F,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,UAAU;YACV,MAAM,YAAY,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,aAAa,EAAE,iBAAiB,CAAC,CAAC;YAC1F,MAAM,UAAU,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;YACjF,MAAM,UAAU,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;YAE/E,eAAe;YACf,MAAM,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,UAAU;YACV,MAAM,IAAI,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;YACpE,MAAM,GAAG,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC;YACvE,MAAM,EAAE,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;YAC3E,MAAM,OAAO,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;YAE3E,eAAe;YACf,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnC,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,UAAU;YACV,MAAM,IAAI,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;YACpE,MAAM,GAAG,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,GAAG,EAAE,iBAAiB,EAAE,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC,CAAC;YAClG,MAAM,OAAO,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;YAC3E,MAAM,GAAG,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;YAEzE,MAAM;YACN,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7C,MAAM,QAAQ,GAAG,GAAG,CAAC,mBAAmB,EAAE,CAAC;YAC3C,MAAM,YAAY,GAAG,OAAO,CAAC,mBAAmB,EAAE,CAAC;YACnD,MAAM,QAAQ,GAAG,GAAG,CAAC,mBAAmB,EAAE,CAAC;YAE3C,SAAS;YACT,MAAM,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YACtC,MAAM,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YACrC,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YACtC,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YAClC,MAAM,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC,4CAA4C;QAC3F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,UAAU;YACV,MAAM,GAAG,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAC/D,MAAM,QAAQ,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACxE,MAAM,GAAG,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;YACzE,MAAM,OAAO,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;YAE3E,MAAM;YACN,MAAM,WAAW,GAAG,GAAG,CAAC,qBAAqB,EAAE,CAAC;YAChD,MAAM,gBAAgB,GAAG,QAAQ,CAAC,qBAAqB,EAAE,CAAC;YAC1D,MAAM,WAAW,GAAG,GAAG,CAAC,qBAAqB,EAAE,CAAC;YAChD,MAAM,eAAe,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;YAExD,SAAS;YACT,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,mBAAmB;YACjD,MAAM,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACjC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC5B,MAAM,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB;QACrD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,UAAU;YACV,MAAM,QAAQ,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACxE,MAAM,SAAS,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YAC5E,MAAM,GAAG,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAC/D,MAAM,MAAM,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;YAEzE,eAAe;YACf,MAAM,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,SAAS,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,GAAG,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,UAAU;YACV,MAAM,QAAQ,GAAG,EAAE,IAAI,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;YAC7D,MAAM,MAAM,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YAEpF,eAAe;YACf,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,UAAU;YACV,MAAM,QAAQ,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACxE,MAAM,WAAW,GAAG,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;YAE3C,MAAM;YACN,MAAM,OAAO,GAAG,QAAQ,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YAEnD,SAAS;YACT,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,UAAU;YACV,MAAM,QAAQ,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAExE,MAAM;YACN,MAAM,OAAO,GAAG,QAAQ,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;YAEzD,SAAS;YACT,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACjD,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,UAAU;YACV,MAAM,QAAQ,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAExE,MAAM;YACN,MAAM,OAAO,GAAG,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAE9C,SAAS;YACT,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACtC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,UAAU;YACV,MAAM,QAAQ,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACxE,MAAM,GAAG,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAC/D,MAAM,MAAM,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;YAEzE,eAAe;YACf,MAAM,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YACxD,MAAM,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;YAC7D,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,UAAU;YACV,MAAM,MAAM,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,GAAG,EAAE,SAAS,EAAE;gBAChE,IAAI,EAAE,aAAa;gBACnB,MAAM,EAAE,aAAa;aACtB,CAAC,CAAC;YAEH,MAAM;YACN,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;YAEpC,SAAS;YACT,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,wCAAe,CAAC,GAAG,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAClC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,UAAU;YACV,MAAM,MAAM,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,QAAQ,EAAE,QAAQ,EAAE;gBACpE,IAAI,EAAE,eAAe;gBACrB,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,WAAW;gBACnB,QAAQ,EAAE,iBAAiB;gBAC3B,QAAQ,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE;aACjC,CAAC,CAAC;YAEH,MAAM;YACN,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAE7B,SAAS;YACT,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,wCAAe,CAAC,QAAQ,CAAC,CAAC;YACjD,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACtC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,UAAU;YACV,MAAM,IAAI,GAAG;gBACX,IAAI,EAAE,wCAAe,CAAC,QAAQ;gBAC9B,UAAU,EAAE,QAAQ;gBACpB,IAAI,EAAE,eAAe;gBACrB,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,WAAW;gBACnB,QAAQ,EAAE,iBAAiB;gBAC3B,QAAQ,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE;aACjC,CAAC;YAEF,MAAM;YACN,MAAM,MAAM,GAAG,uCAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAE1C,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,wCAAe,CAAC,QAAQ,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,UAAU;YACV,MAAM,OAAO,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACvE,MAAM,OAAO,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACvE,MAAM,OAAO,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACvE,MAAM,OAAO,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YAElE,eAAe;YACf,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,UAAU;YACV,MAAM,MAAM,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAEtE,MAAM;YACN,MAAM,GAAG,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAE9B,SAAS;YACT,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,eAAe;YACf,MAAM,CAAC,uCAAW,CAAC,OAAO,CAAC,wCAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3E,MAAM,CAAC,uCAAW,CAAC,OAAO,CAAC,wCAAe,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtE,MAAM,CAAC,uCAAW,CAAC,OAAO,CAAC,wCAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,UAAU;YACV,MAAM,MAAM,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAEtE,eAAe;YACf,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACpC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,UAAU;YACV,MAAM,MAAM,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAEtE,eAAe;YACf,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,2BAA2B;YAC/D,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,UAAU;YACV,MAAM,MAAM,GAAG,uCAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;YAEtE,eAAe;YACf,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,oBAAoB;YACnE,MAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,0BAA0B;YAC1E,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\event-metadata\\__tests__\\event-source.value-object.spec.ts"], "sourcesContent": ["import { EventSource } from '../event-source.value-object';\r\nimport { EventSourceType } from '../../../enums/event-source-type.enum';\r\n\r\ndescribe('EventSource Value Object', () => {\r\n  describe('creation', () => {\r\n    it('should create a valid event source with type and identifier', () => {\r\n      // Act\r\n      const source = EventSource.create(EventSourceType.FIREWALL, 'fw-001');\r\n\r\n      // Assert\r\n      expect(source).toBeDefined();\r\n      expect(source.type).toBe(EventSourceType.FIREWALL);\r\n      expect(source.identifier).toBe('fw-001');\r\n      expect(source.name).toBe('fw-001'); // Default to identifier\r\n    });\r\n\r\n    it('should create a valid event source with optional properties', () => {\r\n      // Act\r\n      const source = EventSource.create(EventSourceType.EDR, 'edr-server-01', {\r\n        name: 'Primary EDR Server',\r\n        version: '1.2.3',\r\n        vendor: 'CrowdStrike',\r\n        location: 'datacenter-east',\r\n        metadata: { zone: 'production' },\r\n      });\r\n\r\n      // Assert\r\n      expect(source.type).toBe(EventSourceType.EDR);\r\n      expect(source.identifier).toBe('edr-server-01');\r\n      expect(source.name).toBe('Primary EDR Server');\r\n      expect(source.version).toBe('1.2.3');\r\n      expect(source.vendor).toBe('CrowdStrike');\r\n      expect(source.location).toBe('datacenter-east');\r\n      expect(source.getMetadata('zone')).toBe('production');\r\n    });\r\n\r\n    it('should create event source from hostname', () => {\r\n      // Act\r\n      const source = EventSource.fromHostname(EventSourceType.WEB_SERVER, 'web01.company.com');\r\n\r\n      // Assert\r\n      expect(source.type).toBe(EventSourceType.WEB_SERVER);\r\n      expect(source.identifier).toBe('web01.company.com');\r\n      expect(source.name).toBe('web01.company.com');\r\n    });\r\n\r\n    it('should create event source from IP address', () => {\r\n      // Act\r\n      const source = EventSource.fromIPAddress(EventSourceType.FIREWALL, '***********');\r\n\r\n      // Assert\r\n      expect(source.type).toBe(EventSourceType.FIREWALL);\r\n      expect(source.identifier).toBe('***********');\r\n      expect(source.name).toBe('firewall-***********');\r\n    });\r\n\r\n    it('should create manual event source', () => {\r\n      // Act\r\n      const source = EventSource.manual('analyst-001');\r\n\r\n      // Assert\r\n      expect(source.type).toBe(EventSourceType.MANUAL);\r\n      expect(source.identifier).toBe('analyst-001');\r\n      expect(source.name).toBe('Manual Event Creation');\r\n    });\r\n\r\n    it('should create unknown event source', () => {\r\n      // Act\r\n      const source = EventSource.unknown('unknown-device-123');\r\n\r\n      // Assert\r\n      expect(source.type).toBe(EventSourceType.UNKNOWN);\r\n      expect(source.identifier).toBe('unknown-device-123');\r\n      expect(source.name).toBe('Unknown Source');\r\n    });\r\n  });\r\n\r\n  describe('validation', () => {\r\n    it('should throw error when type is not provided', () => {\r\n      // Act & Assert\r\n      expect(() => {\r\n        new EventSource({ type: null as any, identifier: 'test' });\r\n      }).toThrow('Event source must have a type');\r\n    });\r\n\r\n    it('should throw error when type is invalid', () => {\r\n      // Act & Assert\r\n      expect(() => {\r\n        new EventSource({ type: 'invalid_type' as any, identifier: 'test' });\r\n      }).toThrow('Invalid event source type: invalid_type');\r\n    });\r\n\r\n    it('should throw error when identifier is not provided', () => {\r\n      // Act & Assert\r\n      expect(() => {\r\n        new EventSource({ type: EventSourceType.FIREWALL, identifier: '' });\r\n      }).toThrow('Event source must have a non-empty identifier');\r\n    });\r\n\r\n    it('should throw error when identifier contains invalid characters', () => {\r\n      // Act & Assert\r\n      expect(() => {\r\n        new EventSource({ type: EventSourceType.FIREWALL, identifier: 'fw@001!' });\r\n      }).toThrow('Event source identifier must contain only alphanumeric characters, dots, hyphens, and underscores');\r\n    });\r\n\r\n    it('should accept valid identifier formats', () => {\r\n      // Act & Assert\r\n      expect(() => {\r\n        new EventSource({ type: EventSourceType.FIREWALL, identifier: 'fw-001' });\r\n      }).not.toThrow();\r\n\r\n      expect(() => {\r\n        new EventSource({ type: EventSourceType.FIREWALL, identifier: 'fw_001' });\r\n      }).not.toThrow();\r\n\r\n      expect(() => {\r\n        new EventSource({ type: EventSourceType.FIREWALL, identifier: 'fw.001' });\r\n      }).not.toThrow();\r\n\r\n      expect(() => {\r\n        new EventSource({ type: EventSourceType.FIREWALL, identifier: 'fw001' });\r\n      }).not.toThrow();\r\n    });\r\n\r\n    it('should throw error when version format is invalid', () => {\r\n      // Act & Assert\r\n      expect(() => {\r\n        new EventSource({\r\n          type: EventSourceType.FIREWALL,\r\n          identifier: 'fw-001',\r\n          version: 'invalid-version',\r\n        });\r\n      }).toThrow('Event source version must be in semantic version format');\r\n    });\r\n\r\n    it('should accept valid version formats', () => {\r\n      // Act & Assert\r\n      expect(() => {\r\n        new EventSource({\r\n          type: EventSourceType.FIREWALL,\r\n          identifier: 'fw-001',\r\n          version: '1.0.0',\r\n        });\r\n      }).not.toThrow();\r\n\r\n      expect(() => {\r\n        new EventSource({\r\n          type: EventSourceType.FIREWALL,\r\n          identifier: 'fw-001',\r\n          version: '2.1.3-beta',\r\n        });\r\n      }).not.toThrow();\r\n\r\n      expect(() => {\r\n        new EventSource({\r\n          type: EventSourceType.FIREWALL,\r\n          identifier: 'fw-001',\r\n          version: '1.0',\r\n        });\r\n      }).not.toThrow();\r\n    });\r\n\r\n    it('should throw error when location is too long', () => {\r\n      // Act & Assert\r\n      expect(() => {\r\n        new EventSource({\r\n          type: EventSourceType.FIREWALL,\r\n          identifier: 'fw-001',\r\n          location: 'a'.repeat(101), // 101 characters\r\n        });\r\n      }).toThrow('Event source location must be 100 characters or less');\r\n    });\r\n  });\r\n\r\n  describe('categorization', () => {\r\n    it('should identify network sources correctly', () => {\r\n      // Arrange\r\n      const firewall = EventSource.create(EventSourceType.FIREWALL, 'fw-001');\r\n      const idsIps = EventSource.create(EventSourceType.IDS_IPS, 'ids-001');\r\n      const networkDevice = EventSource.create(EventSourceType.NETWORK_DEVICE, 'switch-001');\r\n\r\n      // Act & Assert\r\n      expect(firewall.isNetworkSource()).toBe(true);\r\n      expect(idsIps.isNetworkSource()).toBe(true);\r\n      expect(networkDevice.isNetworkSource()).toBe(true);\r\n      expect(firewall.getCategory()).toBe('Network');\r\n    });\r\n\r\n    it('should identify endpoint sources correctly', () => {\r\n      // Arrange\r\n      const edr = EventSource.create(EventSourceType.EDR, 'edr-001');\r\n      const antivirus = EventSource.create(EventSourceType.ANTIVIRUS, 'av-001');\r\n      const os = EventSource.create(EventSourceType.OPERATING_SYSTEM, 'win-server-001');\r\n\r\n      // Act & Assert\r\n      expect(edr.isEndpointSource()).toBe(true);\r\n      expect(antivirus.isEndpointSource()).toBe(true);\r\n      expect(os.isEndpointSource()).toBe(true);\r\n      expect(edr.getCategory()).toBe('Endpoint');\r\n    });\r\n\r\n    it('should identify application sources correctly', () => {\r\n      // Arrange\r\n      const waf = EventSource.create(EventSourceType.WAF, 'waf-001');\r\n      const database = EventSource.create(EventSourceType.DATABASE, 'db-001');\r\n      const webServer = EventSource.create(EventSourceType.WEB_SERVER, 'web-001');\r\n\r\n      // Act & Assert\r\n      expect(waf.isApplicationSource()).toBe(true);\r\n      expect(database.isApplicationSource()).toBe(true);\r\n      expect(webServer.isApplicationSource()).toBe(true);\r\n      expect(waf.getCategory()).toBe('Application');\r\n    });\r\n\r\n    it('should identify cloud sources correctly', () => {\r\n      // Arrange\r\n      const aws = EventSource.create(EventSourceType.AWS, 'aws-cloudtrail');\r\n      const azure = EventSource.create(EventSourceType.AZURE, 'azure-sentinel');\r\n      const gcp = EventSource.create(EventSourceType.GCP, 'gcp-security-center');\r\n\r\n      // Act & Assert\r\n      expect(aws.isCloudSource()).toBe(true);\r\n      expect(azure.isCloudSource()).toBe(true);\r\n      expect(gcp.isCloudSource()).toBe(true);\r\n      expect(aws.getCategory()).toBe('Cloud');\r\n    });\r\n\r\n    it('should identify identity sources correctly', () => {\r\n      // Arrange\r\n      const ad = EventSource.create(EventSourceType.DIRECTORY_SERVICE, 'ad-001');\r\n      const sso = EventSource.create(EventSourceType.SSO, 'sso-001');\r\n      const mfa = EventSource.create(EventSourceType.MFA, 'mfa-001');\r\n\r\n      // Act & Assert\r\n      expect(ad.isIdentitySource()).toBe(true);\r\n      expect(sso.isIdentitySource()).toBe(true);\r\n      expect(mfa.isIdentitySource()).toBe(true);\r\n      expect(ad.getCategory()).toBe('Identity');\r\n    });\r\n\r\n    it('should identify security tools correctly', () => {\r\n      // Arrange\r\n      const siem = EventSource.create(EventSourceType.SIEM, 'splunk-001');\r\n      const soar = EventSource.create(EventSourceType.SOAR, 'phantom-001');\r\n      const vulnScanner = EventSource.create(EventSourceType.VULNERABILITY_SCANNER, 'nessus-001');\r\n\r\n      // Act & Assert\r\n      expect(siem.isSecurityTool()).toBe(true);\r\n      expect(soar.isSecurityTool()).toBe(true);\r\n      expect(vulnScanner.isSecurityTool()).toBe(true);\r\n      expect(siem.getCategory()).toBe('Security Tool');\r\n    });\r\n\r\n    it('should identify external sources correctly', () => {\r\n      // Arrange\r\n      const externalFeed = EventSource.create(EventSourceType.EXTERNAL_FEED, 'threat-feed-001');\r\n      const thirdParty = EventSource.create(EventSourceType.THIRD_PARTY, 'vendor-001');\r\n      const government = EventSource.create(EventSourceType.GOVERNMENT, 'cisa-feed');\r\n\r\n      // Act & Assert\r\n      expect(externalFeed.isExternalSource()).toBe(true);\r\n      expect(thirdParty.isExternalSource()).toBe(true);\r\n      expect(government.isExternalSource()).toBe(true);\r\n      expect(externalFeed.getCategory()).toBe('External');\r\n    });\r\n  });\r\n\r\n  describe('trust and reliability', () => {\r\n    it('should identify trusted sources correctly', () => {\r\n      // Arrange\r\n      const siem = EventSource.create(EventSourceType.SIEM, 'splunk-001');\r\n      const edr = EventSource.create(EventSourceType.EDR, 'crowdstrike-001');\r\n      const ad = EventSource.create(EventSourceType.DIRECTORY_SERVICE, 'ad-001');\r\n      const unknown = EventSource.create(EventSourceType.UNKNOWN, 'unknown-001');\r\n\r\n      // Act & Assert\r\n      expect(siem.isTrusted()).toBe(true);\r\n      expect(edr.isTrusted()).toBe(true);\r\n      expect(ad.isTrusted()).toBe(true);\r\n      expect(unknown.isTrusted()).toBe(false);\r\n    });\r\n\r\n    it('should calculate reliability scores correctly', () => {\r\n      // Arrange\r\n      const siem = EventSource.create(EventSourceType.SIEM, 'splunk-001');\r\n      const edr = EventSource.create(EventSourceType.EDR, 'crowdstrike-001', { vendor: 'CrowdStrike' });\r\n      const unknown = EventSource.create(EventSourceType.UNKNOWN, 'unknown-001');\r\n      const iot = EventSource.create(EventSourceType.IOT_DEVICE, 'sensor-001');\r\n\r\n      // Act\r\n      const siemScore = siem.getReliabilityScore();\r\n      const edrScore = edr.getReliabilityScore();\r\n      const unknownScore = unknown.getReliabilityScore();\r\n      const iotScore = iot.getReliabilityScore();\r\n\r\n      // Assert\r\n      expect(siemScore).toBeGreaterThan(90);\r\n      expect(edrScore).toBeGreaterThan(90);\r\n      expect(unknownScore).toBeLessThan(40);\r\n      expect(iotScore).toBeLessThan(60);\r\n      expect(edrScore).toBeGreaterThan(siemScore); // EDR with known vendor should score higher\r\n    });\r\n\r\n    it('should calculate processing priority correctly', () => {\r\n      // Arrange\r\n      const edr = EventSource.create(EventSourceType.EDR, 'edr-001');\r\n      const firewall = EventSource.create(EventSourceType.FIREWALL, 'fw-001');\r\n      const iot = EventSource.create(EventSourceType.IOT_DEVICE, 'sensor-001');\r\n      const unknown = EventSource.create(EventSourceType.UNKNOWN, 'unknown-001');\r\n\r\n      // Act\r\n      const edrPriority = edr.getProcessingPriority();\r\n      const firewallPriority = firewall.getProcessingPriority();\r\n      const iotPriority = iot.getProcessingPriority();\r\n      const unknownPriority = unknown.getProcessingPriority();\r\n\r\n      // Assert\r\n      expect(edrPriority).toBe(10); // Highest priority\r\n      expect(firewallPriority).toBe(7);\r\n      expect(iotPriority).toBe(4);\r\n      expect(unknownPriority).toBe(2); // Lowest priority\r\n    });\r\n  });\r\n\r\n  describe('volume characteristics', () => {\r\n    it('should identify high-volume sources correctly', () => {\r\n      // Arrange\r\n      const firewall = EventSource.create(EventSourceType.FIREWALL, 'fw-001');\r\n      const webServer = EventSource.create(EventSourceType.WEB_SERVER, 'web-001');\r\n      const edr = EventSource.create(EventSourceType.EDR, 'edr-001');\r\n      const manual = EventSource.create(EventSourceType.MANUAL, 'analyst-001');\r\n\r\n      // Act & Assert\r\n      expect(firewall.isHighVolumeSource()).toBe(true);\r\n      expect(webServer.isHighVolumeSource()).toBe(true);\r\n      expect(edr.isHighVolumeSource()).toBe(false);\r\n      expect(manual.isHighVolumeSource()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('metadata handling', () => {\r\n    it('should handle metadata correctly', () => {\r\n      // Arrange\r\n      const metadata = { zone: 'production', criticality: 'high' };\r\n      const source = EventSource.create(EventSourceType.FIREWALL, 'fw-001', { metadata });\r\n\r\n      // Act & Assert\r\n      expect(source.hasMetadata('zone')).toBe(true);\r\n      expect(source.hasMetadata('nonexistent')).toBe(false);\r\n      expect(source.getMetadata('zone')).toBe('production');\r\n      expect(source.getMetadata('criticality')).toBe('high');\r\n      expect(source.getMetadata('nonexistent')).toBeUndefined();\r\n    });\r\n\r\n    it('should create new instance with updated metadata', () => {\r\n      // Arrange\r\n      const original = EventSource.create(EventSourceType.FIREWALL, 'fw-001');\r\n      const newMetadata = { zone: 'production' };\r\n\r\n      // Act\r\n      const updated = original.withMetadata(newMetadata);\r\n\r\n      // Assert\r\n      expect(updated).not.toBe(original);\r\n      expect(updated.hasMetadata('zone')).toBe(true);\r\n      expect(original.hasMetadata('zone')).toBe(false);\r\n    });\r\n\r\n    it('should create new instance with updated location', () => {\r\n      // Arrange\r\n      const original = EventSource.create(EventSourceType.FIREWALL, 'fw-001');\r\n\r\n      // Act\r\n      const updated = original.withLocation('datacenter-west');\r\n\r\n      // Assert\r\n      expect(updated).not.toBe(original);\r\n      expect(updated.location).toBe('datacenter-west');\r\n      expect(original.location).toBeUndefined();\r\n    });\r\n\r\n    it('should create new instance with updated version', () => {\r\n      // Arrange\r\n      const original = EventSource.create(EventSourceType.FIREWALL, 'fw-001');\r\n\r\n      // Act\r\n      const updated = original.withVersion('2.1.0');\r\n\r\n      // Assert\r\n      expect(updated).not.toBe(original);\r\n      expect(updated.version).toBe('2.1.0');\r\n      expect(original.version).toBeUndefined();\r\n    });\r\n  });\r\n\r\n  describe('descriptions and summaries', () => {\r\n    it('should provide correct descriptions', () => {\r\n      // Arrange\r\n      const firewall = EventSource.create(EventSourceType.FIREWALL, 'fw-001');\r\n      const edr = EventSource.create(EventSourceType.EDR, 'edr-001');\r\n      const manual = EventSource.create(EventSourceType.MANUAL, 'analyst-001');\r\n\r\n      // Act & Assert\r\n      expect(firewall.getDescription()).toContain('firewall');\r\n      expect(edr.getDescription()).toContain('Endpoint Detection');\r\n      expect(manual.getDescription()).toContain('Manual');\r\n    });\r\n\r\n    it('should provide comprehensive summary', () => {\r\n      // Arrange\r\n      const source = EventSource.create(EventSourceType.EDR, 'edr-001', {\r\n        name: 'Primary EDR',\r\n        vendor: 'CrowdStrike',\r\n      });\r\n\r\n      // Act\r\n      const summary = source.getSummary();\r\n\r\n      // Assert\r\n      expect(summary.type).toBe(EventSourceType.EDR);\r\n      expect(summary.identifier).toBe('edr-001');\r\n      expect(summary.name).toBe('Primary EDR');\r\n      expect(summary.category).toBe('Endpoint');\r\n      expect(summary.reliability).toBeGreaterThan(90);\r\n      expect(summary.priority).toBe(10);\r\n      expect(summary.isTrusted).toBe(true);\r\n      expect(summary.isHighVolume).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('serialization', () => {\r\n    it('should convert to JSON correctly', () => {\r\n      // Arrange\r\n      const source = EventSource.create(EventSourceType.FIREWALL, 'fw-001', {\r\n        name: 'Main Firewall',\r\n        version: '1.2.3',\r\n        vendor: 'Palo Alto',\r\n        location: 'datacenter-east',\r\n        metadata: { zone: 'production' },\r\n      });\r\n\r\n      // Act\r\n      const json = source.toJSON();\r\n\r\n      // Assert\r\n      expect(json.type).toBe(EventSourceType.FIREWALL);\r\n      expect(json.identifier).toBe('fw-001');\r\n      expect(json.name).toBe('Main Firewall');\r\n      expect(json.version).toBe('1.2.3');\r\n      expect(json.vendor).toBe('Palo Alto');\r\n      expect(json.location).toBe('datacenter-east');\r\n      expect(json.metadata).toEqual({ zone: 'production' });\r\n      expect(json.summary).toBeDefined();\r\n      expect(json.description).toBeDefined();\r\n    });\r\n\r\n    it('should create from JSON correctly', () => {\r\n      // Arrange\r\n      const json = {\r\n        type: EventSourceType.FIREWALL,\r\n        identifier: 'fw-001',\r\n        name: 'Main Firewall',\r\n        version: '1.2.3',\r\n        vendor: 'Palo Alto',\r\n        location: 'datacenter-east',\r\n        metadata: { zone: 'production' },\r\n      };\r\n\r\n      // Act\r\n      const source = EventSource.fromJSON(json);\r\n\r\n      // Assert\r\n      expect(source.type).toBe(EventSourceType.FIREWALL);\r\n      expect(source.identifier).toBe('fw-001');\r\n      expect(source.name).toBe('Main Firewall');\r\n      expect(source.version).toBe('1.2.3');\r\n      expect(source.vendor).toBe('Palo Alto');\r\n      expect(source.location).toBe('datacenter-east');\r\n      expect(source.getMetadata('zone')).toBe('production');\r\n    });\r\n  });\r\n\r\n  describe('equality and comparison', () => {\r\n    it('should compare sources for equality correctly', () => {\r\n      // Arrange\r\n      const source1 = EventSource.create(EventSourceType.FIREWALL, 'fw-001');\r\n      const source2 = EventSource.create(EventSourceType.FIREWALL, 'fw-001');\r\n      const source3 = EventSource.create(EventSourceType.FIREWALL, 'fw-002');\r\n      const source4 = EventSource.create(EventSourceType.EDR, 'fw-001');\r\n\r\n      // Act & Assert\r\n      expect(source1.equals(source2)).toBe(true);\r\n      expect(source1.equals(source3)).toBe(false);\r\n      expect(source1.equals(source4)).toBe(false);\r\n      expect(source1.equals(undefined)).toBe(false);\r\n      expect(source1.equals(source1)).toBe(true);\r\n    });\r\n\r\n    it('should convert to string correctly', () => {\r\n      // Arrange\r\n      const source = EventSource.create(EventSourceType.FIREWALL, 'fw-001');\r\n\r\n      // Act\r\n      const str = source.toString();\r\n\r\n      // Assert\r\n      expect(str).toBe('firewall:fw-001');\r\n    });\r\n  });\r\n\r\n  describe('validation utility', () => {\r\n    it('should validate source without creating instance', () => {\r\n      // Act & Assert\r\n      expect(EventSource.isValid(EventSourceType.FIREWALL, 'fw-001')).toBe(true);\r\n      expect(EventSource.isValid(EventSourceType.FIREWALL, '')).toBe(false);\r\n      expect(EventSource.isValid(EventSourceType.FIREWALL, 'fw@001')).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('edge cases', () => {\r\n    it('should handle empty metadata gracefully', () => {\r\n      // Arrange\r\n      const source = EventSource.create(EventSourceType.FIREWALL, 'fw-001');\r\n\r\n      // Act & Assert\r\n      expect(source.metadata).toEqual({});\r\n      expect(source.hasMetadata('anything')).toBe(false);\r\n      expect(source.getMetadata('anything')).toBeUndefined();\r\n    });\r\n\r\n    it('should handle missing optional properties gracefully', () => {\r\n      // Arrange\r\n      const source = EventSource.create(EventSourceType.FIREWALL, 'fw-001');\r\n\r\n      // Act & Assert\r\n      expect(source.name).toBe('fw-001'); // Falls back to identifier\r\n      expect(source.version).toBeUndefined();\r\n      expect(source.vendor).toBeUndefined();\r\n      expect(source.location).toBeUndefined();\r\n    });\r\n\r\n    it('should handle unknown source types in utility methods', () => {\r\n      // Arrange\r\n      const source = EventSource.create(EventSourceType.OTHER, 'other-001');\r\n\r\n      // Act & Assert\r\n      expect(source.getCategory()).toBe('Other');\r\n      expect(source.getReliabilityScore()).toBe(40); // Default for OTHER\r\n      expect(source.getProcessingPriority()).toBe(5); // Default medium priority\r\n      expect(source.isTrusted()).toBe(false);\r\n      expect(source.isHighVolumeSource()).toBe(false);\r\n    });\r\n  });\r\n});"], "version": 3}