183d9324b32016091346fef65945faa5
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const security_orchestrator_service_1 = require("../../application/services/security-orchestrator.service");
const threat_assessment_service_1 = require("../../application/services/threat-assessment.service");
const event_factory_1 = require("../../domain/factories/event.factory");
const event_repository_1 = require("../../domain/repositories/event.repository");
const threat_repository_1 = require("../../domain/repositories/threat.repository");
const vulnerability_repository_1 = require("../../domain/repositories/vulnerability.repository");
const response_action_repository_1 = require("../../domain/repositories/response-action.repository");
const event_processor_interface_1 = require("../../domain/interfaces/services/event-processor.interface");
const threat_detector_interface_1 = require("../../domain/interfaces/services/threat-detector.interface");
const vulnerability_scanner_interface_1 = require("../../domain/interfaces/services/vulnerability-scanner.interface");
const response_executor_interface_1 = require("../../domain/interfaces/services/response-executor.interface");
const event_metadata_value_object_1 = require("../../domain/value-objects/event-metadata/event-metadata.value-object");
const event_timestamp_value_object_1 = require("../../domain/value-objects/event-metadata/event-timestamp.value-object");
const event_source_value_object_1 = require("../../domain/value-objects/event-metadata/event-source.value-object");
const event_type_enum_1 = require("../../domain/enums/event-type.enum");
const event_severity_enum_1 = require("../../domain/enums/event-severity.enum");
const event_status_enum_1 = require("../../domain/enums/event-status.enum");
const event_source_type_enum_1 = require("../../domain/enums/event-source-type.enum");
const threat_severity_enum_1 = require("../../domain/enums/threat-severity.enum");
const vulnerability_severity_enum_1 = require("../../domain/enums/vulnerability-severity.enum");
const action_type_enum_1 = require("../../domain/enums/action-type.enum");
const action_status_enum_1 = require("../../domain/enums/action-status.enum");
const unique_entity_id_value_object_1 = require("../../../../shared-kernel/value-objects/unique-entity-id.value-object");
const correlation_id_value_object_1 = require("../../../../shared-kernel/value-objects/correlation-id.value-object");
const tenant_id_value_object_1 = require("../../../../shared-kernel/value-objects/tenant-id.value-object");
const user_id_value_object_1 = require("../../../../shared-kernel/value-objects/user-id.value-object");
const service_unavailable_exception_1 = require("../../../../shared-kernel/exceptions/service-unavailable.exception");
/**
 * Mock implementations for application service testing
 */
class MockEventRepository {
    constructor() {
        this.events = new Map();
        this.shouldFail = false;
    }
    setShouldFail(fail) {
        this.shouldFail = fail;
    }
    async save(event) {
        if (this.shouldFail) {
            throw new service_unavailable_exception_1.ServiceUnavailableException('Database unavailable');
        }
        this.events.set(event.id.toString(), event);
    }
    async findById(id) {
        if (this.shouldFail) {
            throw new service_unavailable_exception_1.ServiceUnavailableException('Database unavailable');
        }
        return this.events.get(id.toString()) || null;
    }
    async findAll() {
        return Array.from(this.events.values());
    }
    async delete(id) {
        this.events.delete(id.toString());
    }
    async findByTimeRange(start, end) {
        return Array.from(this.events.values()).filter(event => {
            const eventTime = event.timestamp.toDate();
            return eventTime >= start && eventTime <= end;
        });
    }
    async findBySource(source) {
        return Array.from(this.events.values()).filter(event => event.source.equals(source));
    }
    async findBySeverity(severity) {
        return Array.from(this.events.values()).filter(event => event.severity === severity);
    }
    async findEventsForCorrelation(timeWindowMs, eventTypes, minSeverity) {
        const cutoffTime = new Date(Date.now() - timeWindowMs);
        return Array.from(this.events.values()).filter(event => {
            const eventTime = event.timestamp.toDate();
            return eventTime >= cutoffTime;
        });
    }
    clear() {
        this.events.clear();
    }
}
class MockThreatRepository {
    constructor() {
        this.threats = new Map();
    }
    async save(threat) {
        this.threats.set(threat.id.toString(), threat);
    }
    async findById(id) {
        return this.threats.get(id.toString()) || null;
    }
    async findAll() {
        return Array.from(this.threats.values());
    }
    async delete(id) {
        this.threats.delete(id.toString());
    }
    async findBySeverity(severity) {
        return Array.from(this.threats.values()).filter(threat => threat.severity === severity);
    }
    async findBySourceIp(sourceIp) {
        return Array.from(this.threats.values()).filter(threat => threat.sourceIp && threat.sourceIp.equals(sourceIp));
    }
    clear() {
        this.threats.clear();
    }
}
class MockVulnerabilityRepository {
    constructor() {
        this.vulnerabilities = new Map();
    }
    async save(vulnerability) {
        this.vulnerabilities.set(vulnerability.id.toString(), vulnerability);
    }
    async findById(id) {
        return this.vulnerabilities.get(id.toString()) || null;
    }
    async findAll() {
        return Array.from(this.vulnerabilities.values());
    }
    async delete(id) {
        this.vulnerabilities.delete(id.toString());
    }
    async findBySeverity(severity) {
        return Array.from(this.vulnerabilities.values()).filter(vuln => vuln.severity === severity);
    }
    async findByPort(port) {
        return Array.from(this.vulnerabilities.values()).filter(vuln => vuln.affectedPort && vuln.affectedPort.equals(port));
    }
    clear() {
        this.vulnerabilities.clear();
    }
}
class MockResponseActionRepository {
    constructor() {
        this.actions = new Map();
    }
    async save(action) {
        this.actions.set(action.id.toString(), action);
    }
    async findById(id) {
        return this.actions.get(id.toString()) || null;
    }
    async findAll() {
        return Array.from(this.actions.values());
    }
    async delete(id) {
        this.actions.delete(id.toString());
    }
    async findByStatus(status) {
        return Array.from(this.actions.values()).filter(action => action.status === status);
    }
    async findByType(type) {
        return Array.from(this.actions.values()).filter(action => action.type === type);
    }
    clear() {
        this.actions.clear();
    }
}
class MockEventProcessor {
    constructor() {
        this.shouldFail = false;
        this.processingDelay = 0;
    }
    setShouldFail(fail) {
        this.shouldFail = fail;
    }
    setProcessingDelay(delay) {
        this.processingDelay = delay;
    }
    async processEvent(event, context) {
        if (this.processingDelay > 0) {
            await new Promise(resolve => setTimeout(resolve, this.processingDelay));
        }
        if (this.shouldFail) {
            throw new service_unavailable_exception_1.ServiceUnavailableException('Event processing service unavailable');
        }
        return {
            success: true,
            eventId: event.id.toString(),
            processingSteps: ['normalize', 'enrich', 'correlate'],
            duration: this.processingDelay || 100,
            normalizedEvent: null,
            enrichedEvent: null,
            correlatedEvents: [],
            errors: [],
        };
    }
    async processEvents(events, context) {
        const results = await Promise.all(events.map(event => this.processEvent(event, context)));
        return {
            success: true,
            totalEvents: events.length,
            processedEvents: results.length,
            results,
        };
    }
}
class MockThreatDetector {
    constructor() {
        this.shouldFail = false;
        this.detectionDelay = 0;
    }
    setShouldFail(fail) {
        this.shouldFail = fail;
    }
    setDetectionDelay(delay) {
        this.detectionDelay = delay;
    }
    async analyzeEvent(event, context) {
        if (this.detectionDelay > 0) {
            await new Promise(resolve => setTimeout(resolve, this.detectionDelay));
        }
        if (this.shouldFail) {
            throw new service_unavailable_exception_1.ServiceUnavailableException('Threat detection service unavailable');
        }
        const threats = event.severity === event_severity_enum_1.EventSeverity.HIGH || event.severity === event_severity_enum_1.EventSeverity.CRITICAL
            ? [{
                    id: unique_entity_id_value_object_1.UniqueEntityId.create().toString(),
                    severity: threat_severity_enum_1.ThreatSeverity.HIGH,
                    confidence: 85,
                    signature: 'detected-threat-001',
                    indicators: ['suspicious-pattern']
                }]
            : [];
        return {
            eventId: event.id.toString(),
            threats,
            confidence: threats.length > 0 ? 85 : 10,
            analysisTime: this.detectionDelay || 50,
            indicators: threats.flatMap(t => t.indicators),
            recommendations: threats.length > 0 ? ['Investigate immediately', 'Block source IP'] : [],
        };
    }
    async analyzeEvents(events, context) {
        const analyses = await Promise.all(events.map(event => this.analyzeEvent(event, context)));
        return {
            totalEvents: events.length,
            analyses,
            aggregatedThreats: analyses.flatMap(a => a.threats),
        };
    }
}
class MockVulnerabilityScanner {
    constructor() {
        this.shouldFail = false;
    }
    setShouldFail(fail) {
        this.shouldFail = fail;
    }
    async scanEvent(event, context) {
        if (this.shouldFail) {
            throw new service_unavailable_exception_1.ServiceUnavailableException('Vulnerability scanner unavailable');
        }
        const vulnerabilities = event.type === event_type_enum_1.EventType.NETWORK_INTRUSION
            ? [{
                    id: unique_entity_id_value_object_1.UniqueEntityId.create().toString(),
                    severity: vulnerability_severity_enum_1.VulnerabilitySeverity.MEDIUM,
                    cveId: 'CVE-2023-1234',
                    score: 6.5,
                    description: 'Network vulnerability detected'
                }]
            : [];
        return {
            target: { id: event.id.toString(), type: 'event' },
            vulnerabilities,
            scanDuration: 75,
            scanType: 'QUICK',
            findings: vulnerabilities.length,
        };
    }
}
class MockResponseExecutor {
    constructor() {
        this.shouldFail = false;
        this.executionDelay = 0;
    }
    setShouldFail(fail) {
        this.shouldFail = fail;
    }
    setExecutionDelay(delay) {
        this.executionDelay = delay;
    }
    async executeResponse(event, context) {
        if (this.executionDelay > 0) {
            await new Promise(resolve => setTimeout(resolve, this.executionDelay));
        }
        if (this.shouldFail) {
            throw new service_unavailable_exception_1.ServiceUnavailableException('Response executor unavailable');
        }
        const actions = context.threats?.length > 0 || context.vulnerabilities?.length > 0
            ? [{
                    type: action_type_enum_1.ActionType.BLOCK_IP,
                    status: action_status_enum_1.ActionStatus.COMPLETED,
                    parameters: { ipAddress: '*************', duration: 3600 }
                }]
            : [];
        return {
            responseId: unique_entity_id_value_object_1.UniqueEntityId.create().toString(),
            event: event.id.toString(),
            actionsExecuted: actions.length,
            actions,
            success: true,
            duration: this.executionDelay || 200,
        };
    }
}
describe('Application Services Integration Tests', () => {
    let module;
    let orchestratorService;
    let threatAssessmentService;
    let eventRepository;
    let threatRepository;
    let vulnerabilityRepository;
    let responseActionRepository;
    let eventProcessor;
    let threatDetector;
    let vulnerabilityScanner;
    let responseExecutor;
    beforeEach(async () => {
        // Create mock instances
        eventRepository = new MockEventRepository();
        threatRepository = new MockThreatRepository();
        vulnerabilityRepository = new MockVulnerabilityRepository();
        responseActionRepository = new MockResponseActionRepository();
        eventProcessor = new MockEventProcessor();
        threatDetector = new MockThreatDetector();
        vulnerabilityScanner = new MockVulnerabilityScanner();
        responseExecutor = new MockResponseExecutor();
        module = await testing_1.Test.createTestingModule({
            providers: [
                security_orchestrator_service_1.SecurityOrchestratorService,
                threat_assessment_service_1.ThreatAssessmentService,
                { provide: event_repository_1.EventRepository, useValue: eventRepository },
                { provide: threat_repository_1.ThreatRepository, useValue: threatRepository },
                { provide: vulnerability_repository_1.VulnerabilityRepository, useValue: vulnerabilityRepository },
                { provide: response_action_repository_1.ResponseActionRepository, useValue: responseActionRepository },
                { provide: event_processor_interface_1.EventProcessor, useValue: eventProcessor },
                { provide: threat_detector_interface_1.ThreatDetector, useValue: threatDetector },
                { provide: vulnerability_scanner_interface_1.VulnerabilityScanner, useValue: vulnerabilityScanner },
                { provide: response_executor_interface_1.ResponseExecutor, useValue: responseExecutor },
            ],
        }).compile();
        orchestratorService = module.get(security_orchestrator_service_1.SecurityOrchestratorService);
        threatAssessmentService = module.get(threat_assessment_service_1.ThreatAssessmentService);
    });
    afterEach(async () => {
        eventRepository.clear();
        threatRepository.clear();
        vulnerabilityRepository.clear();
        responseActionRepository.clear();
        await module.close();
    });
    describe('Service Coordination and Transaction Boundaries', () => {
        it('should coordinate multiple services in security workflow', async () => {
            // Arrange
            const tenantId = tenant_id_value_object_1.TenantId.create();
            const userId = user_id_value_object_1.UserId.create();
            const correlationId = correlation_id_value_object_1.CorrelationId.create();
            const events = [
                event_factory_1.EventFactory.create({
                    metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'firewall', version: '1.0' }),
                    timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                    source: event_source_value_object_1.EventSource.create({
                        type: event_source_type_enum_1.EventSourceType.FIREWALL,
                        identifier: 'fw-001',
                        name: 'Firewall',
                    }),
                    type: event_type_enum_1.EventType.NETWORK_INTRUSION,
                    severity: event_severity_enum_1.EventSeverity.HIGH,
                    status: event_status_enum_1.EventStatus.RECEIVED,
                    payload: { sourceIp: '*************' },
                    tenantId,
                    userId,
                }),
                event_factory_1.EventFactory.create({
                    metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'ids', version: '1.0' }),
                    timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                    source: event_source_value_object_1.EventSource.create({
                        type: event_source_type_enum_1.EventSourceType.IDS,
                        identifier: 'ids-001',
                        name: 'IDS',
                    }),
                    type: event_type_enum_1.EventType.MALWARE_DETECTED,
                    severity: event_severity_enum_1.EventSeverity.CRITICAL,
                    status: event_status_enum_1.EventStatus.RECEIVED,
                    payload: { malwareType: 'trojan' },
                    tenantId,
                    userId,
                }),
            ];
            // Act
            const result = await orchestratorService.orchestrateSecurityWorkflow(events, {
                tenantId: tenantId.value,
                userId: userId.value,
                correlationId: correlationId.value,
                priority: 'HIGH',
                autoResponse: true,
                enableThreatHunting: true,
                enableVulnerabilityScanning: true,
                workflowConfig: {
                    enableEventProcessing: true,
                    enableThreatDetection: true,
                    enableVulnerabilityScanning: true,
                    enableResponseExecution: true,
                    enableCorrelation: true,
                    enableEnrichment: true,
                    batchSize: 5,
                    maxConcurrentOperations: 3,
                    retryAttempts: 2,
                    timeoutMs: 60000,
                },
            });
            // Assert
            expect(result.isSuccess()).toBe(true);
            const orchestrationResult = result.getValue();
            expect(orchestrationResult.success).toBe(true);
            expect(orchestrationResult.eventsProcessed).toBe(2);
            expect(orchestrationResult.processingResults).toHaveLength(2);
            expect(orchestrationResult.threatAnalyses).toHaveLength(2);
            expect(orchestrationResult.vulnerabilityScans).toHaveLength(2);
            expect(orchestrationResult.responseResults).toHaveLength(2);
            // Verify service coordination
            expect(orchestrationResult.threatsDetected).toBeGreaterThan(0);
            expect(orchestrationResult.actionsExecuted).toBeGreaterThan(0);
            expect(orchestrationResult.duration).toBeGreaterThan(0);
            expect(orchestrationResult.errors).toHaveLength(0);
        });
        it('should handle transaction boundaries correctly with rollback on failure', async () => {
            // Arrange
            const event = event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'test', version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: 'app-001',
                    name: 'Test App',
                }),
                type: event_type_enum_1.EventType.SECURITY_ALERT,
                severity: event_severity_enum_1.EventSeverity.HIGH,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { alertType: 'suspicious-activity' },
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            // Configure repository to fail after processing starts
            eventRepository.setShouldFail(true);
            // Act
            const result = await orchestratorService.processSecurityEvents([event], {
                priority: 'HIGH',
                timeoutMs: 30000,
            });
            // Assert
            expect(result.isSuccess()).toBe(true); // Service handles errors gracefully
            const processingResult = result.getValue();
            expect(processingResult.success).toBe(false); // But marks operation as failed
            expect(processingResult.errors.length).toBeGreaterThan(0);
            expect(processingResult.errors[0].errorCode).toBe('ORCHESTRATION_FAILED');
        });
        it('should handle concurrent service operations', async () => {
            // Arrange
            const events = Array.from({ length: 10 }, (_, i) => event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: `source-${i}`, version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: `app-${i}`,
                    name: `App ${i}`,
                }),
                type: event_type_enum_1.EventType.AUTHENTICATION_FAILURE,
                severity: i % 2 === 0 ? event_severity_enum_1.EventSeverity.HIGH : event_severity_enum_1.EventSeverity.MEDIUM,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { attempt: i },
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            }));
            // Act
            const result = await orchestratorService.orchestrateSecurityWorkflow(events, {
                priority: 'MEDIUM',
                workflowConfig: {
                    enableEventProcessing: true,
                    enableThreatDetection: true,
                    enableVulnerabilityScanning: true,
                    enableResponseExecution: true,
                    enableCorrelation: true,
                    enableEnrichment: true,
                    batchSize: 3,
                    maxConcurrentOperations: 5,
                    retryAttempts: 2,
                    timeoutMs: 60000,
                },
            });
            // Assert
            expect(result.isSuccess()).toBe(true);
            const orchestrationResult = result.getValue();
            expect(orchestrationResult.success).toBe(true);
            expect(orchestrationResult.eventsProcessed).toBe(10);
            expect(orchestrationResult.processingResults).toHaveLength(10);
            // All events should be processed successfully
            orchestrationResult.processingResults.forEach(pr => {
                expect(pr.success).toBe(true);
            });
            // High severity events should generate threats
            const highSeverityCount = events.filter(e => e.severity === event_severity_enum_1.EventSeverity.HIGH).length;
            expect(orchestrationResult.threatsDetected).toBe(highSeverityCount);
        });
        it('should handle service timeouts gracefully', async () => {
            // Arrange
            const event = event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'test', version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: 'app-001',
                    name: 'Test App',
                }),
                type: event_type_enum_1.EventType.SECURITY_ALERT,
                severity: event_severity_enum_1.EventSeverity.HIGH,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { alertType: 'test' },
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            // Configure services with delays
            eventProcessor.setProcessingDelay(100);
            threatDetector.setDetectionDelay(100);
            responseExecutor.setExecutionDelay(100);
            // Act - Process with very short timeout
            const result = await orchestratorService.orchestrateSecurityWorkflow([event], {
                priority: 'HIGH',
                timeoutMs: 50, // Very short timeout
            });
            // Assert
            expect(result.isSuccess()).toBe(true);
            const orchestrationResult = result.getValue();
            // Should complete successfully despite timeout (graceful handling)
            expect(orchestrationResult.eventsProcessed).toBe(1);
            expect(orchestrationResult.duration).toBeGreaterThan(0);
        });
    });
    describe('Error Handling and Recovery', () => {
        it('should handle individual service failures gracefully', async () => {
            // Arrange
            const event = event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'test', version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: 'app-001',
                    name: 'Test App',
                }),
                type: event_type_enum_1.EventType.NETWORK_INTRUSION,
                severity: event_severity_enum_1.EventSeverity.HIGH,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { sourceIp: '*************' },
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            // Configure threat detector to fail
            threatDetector.setShouldFail(true);
            // Act
            const result = await orchestratorService.orchestrateSecurityWorkflow([event], {
                priority: 'HIGH',
                autoResponse: true,
            });
            // Assert
            expect(result.isSuccess()).toBe(true);
            const orchestrationResult = result.getValue();
            // Should continue processing despite threat detector failure
            expect(orchestrationResult.eventsProcessed).toBe(1);
            expect(orchestrationResult.processingResults).toHaveLength(1);
            expect(orchestrationResult.processingResults[0].success).toBe(true);
            // Should have errors from threat detection
            expect(orchestrationResult.errors.length).toBeGreaterThan(0);
            const threatDetectionError = orchestrationResult.errors.find(e => e.stage === 'THREAT_DETECTION' || e.message.includes('Threat detection'));
            expect(threatDetectionError).toBeDefined();
        });
        it('should handle cascading service failures', async () => {
            // Arrange
            const events = [
                event_factory_1.EventFactory.create({
                    metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'firewall', version: '1.0' }),
                    timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                    source: event_source_value_object_1.EventSource.create({
                        type: event_source_type_enum_1.EventSourceType.FIREWALL,
                        identifier: 'fw-001',
                        name: 'Firewall',
                    }),
                    type: event_type_enum_1.EventType.NETWORK_INTRUSION,
                    severity: event_severity_enum_1.EventSeverity.CRITICAL,
                    status: event_status_enum_1.EventStatus.RECEIVED,
                    payload: { sourceIp: '*************' },
                    tenantId: tenant_id_value_object_1.TenantId.create(),
                    userId: user_id_value_object_1.UserId.create(),
                }),
            ];
            // Configure multiple services to fail
            eventProcessor.setShouldFail(true);
            threatDetector.setShouldFail(true);
            vulnerabilityScanner.setShouldFail(true);
            // Act
            const result = await orchestratorService.orchestrateSecurityWorkflow(events, {
                priority: 'CRITICAL',
                autoResponse: false,
            });
            // Assert
            expect(result.isSuccess()).toBe(true);
            const orchestrationResult = result.getValue();
            // Should fail gracefully with multiple errors
            expect(orchestrationResult.success).toBe(false);
            expect(orchestrationResult.errors.length).toBeGreaterThan(0);
            // Should have attempted to process the event
            expect(orchestrationResult.eventsProcessed).toBe(0); // No events processed due to failures
        });
        it('should implement retry logic for transient failures', async () => {
            // Arrange
            const event = event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'test', version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: 'app-001',
                    name: 'Test App',
                }),
                type: event_type_enum_1.EventType.AUTHENTICATION_FAILURE,
                severity: event_severity_enum_1.EventSeverity.MEDIUM,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { username: 'testuser' },
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            let callCount = 0;
            const originalProcessEvent = eventProcessor.processEvent.bind(eventProcessor);
            eventProcessor.processEvent = jest.fn().mockImplementation(async (event, context) => {
                callCount++;
                if (callCount <= 2) {
                    throw new service_unavailable_exception_1.ServiceUnavailableException('Transient failure');
                }
                return originalProcessEvent(event, context);
            });
            // Act
            const result = await orchestratorService.processSecurityEvents([event], {
                priority: 'MEDIUM',
                workflowConfig: {
                    enableEventProcessing: true,
                    enableThreatDetection: false,
                    enableVulnerabilityScanning: false,
                    enableResponseExecution: false,
                    enableCorrelation: false,
                    enableEnrichment: false,
                    batchSize: 1,
                    maxConcurrentOperations: 1,
                    retryAttempts: 3,
                    timeoutMs: 60000,
                },
            });
            // Assert
            expect(result.isSuccess()).toBe(true);
            const processingResult = result.getValue();
            // Should succeed after retries
            expect(processingResult.success).toBe(true);
            expect(processingResult.eventsProcessed).toBe(1);
            expect(callCount).toBe(3); // Initial call + 2 retries
        });
    });
    describe('Performance and Scalability', () => {
        it('should handle high-volume event processing efficiently', async () => {
            // Arrange
            const eventCount = 100;
            const events = Array.from({ length: eventCount }, (_, i) => event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: `source-${i}`, version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: `app-${i}`,
                    name: `App ${i}`,
                }),
                type: event_type_enum_1.EventType.AUTHENTICATION_FAILURE,
                severity: event_severity_enum_1.EventSeverity.MEDIUM,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { attempt: i },
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            }));
            const startTime = Date.now();
            // Act
            const result = await orchestratorService.orchestrateSecurityWorkflow(events, {
                priority: 'MEDIUM',
                workflowConfig: {
                    enableEventProcessing: true,
                    enableThreatDetection: true,
                    enableVulnerabilityScanning: false,
                    enableResponseExecution: false,
                    enableCorrelation: true,
                    enableEnrichment: true,
                    batchSize: 10,
                    maxConcurrentOperations: 5,
                    retryAttempts: 1,
                    timeoutMs: 120000,
                },
            });
            const endTime = Date.now();
            const totalTime = endTime - startTime;
            // Assert
            expect(result.isSuccess()).toBe(true);
            const orchestrationResult = result.getValue();
            expect(orchestrationResult.success).toBe(true);
            expect(orchestrationResult.eventsProcessed).toBe(eventCount);
            expect(orchestrationResult.processingResults).toHaveLength(eventCount);
            // Performance assertions
            expect(totalTime).toBeLessThan(10000); // Should complete within 10 seconds
            expect(orchestrationResult.duration).toBeGreaterThan(0);
            // Verify throughput
            const throughput = eventCount / (totalTime / 1000); // events per second
            expect(throughput).toBeGreaterThan(10); // At least 10 events per second
        });
        it('should optimize batch processing for better performance', async () => {
            // Arrange
            const events = Array.from({ length: 50 }, (_, i) => event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: `batch-${i}`, version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: `batch-app-${i}`,
                    name: `Batch App ${i}`,
                }),
                type: event_type_enum_1.EventType.SECURITY_ALERT,
                severity: event_severity_enum_1.EventSeverity.LOW,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { batchId: Math.floor(i / 10) },
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            }));
            // Test different batch sizes
            const batchSizes = [5, 10, 25];
            const results = [];
            for (const batchSize of batchSizes) {
                const startTime = Date.now();
                const result = await orchestratorService.orchestrateSecurityWorkflow(events, {
                    priority: 'LOW',
                    workflowConfig: {
                        enableEventProcessing: true,
                        enableThreatDetection: false,
                        enableVulnerabilityScanning: false,
                        enableResponseExecution: false,
                        enableCorrelation: false,
                        enableEnrichment: false,
                        batchSize,
                        maxConcurrentOperations: 3,
                        retryAttempts: 1,
                        timeoutMs: 60000,
                    },
                });
                const endTime = Date.now();
                const duration = endTime - startTime;
                results.push({
                    batchSize,
                    duration,
                    success: result.isSuccess() && result.getValue().success,
                    eventsProcessed: result.isSuccess() ? result.getValue().eventsProcessed : 0,
                });
            }
            // Assert
            results.forEach(result => {
                expect(result.success).toBe(true);
                expect(result.eventsProcessed).toBe(50);
            });
            // Verify that larger batch sizes are generally more efficient
            const sortedByBatchSize = results.sort((a, b) => a.batchSize - b.batchSize);
            expect(sortedByBatchSize[0].batchSize).toBe(5);
            expect(sortedByBatchSize[2].batchSize).toBe(25);
        });
    });
    describe('Cross-Cutting Concerns', () => {
        it('should maintain audit trail throughout service operations', async () => {
            // Arrange
            const tenantId = tenant_id_value_object_1.TenantId.create();
            const userId = user_id_value_object_1.UserId.create();
            const correlationId = correlation_id_value_object_1.CorrelationId.create();
            const event = event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'audit-test', version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: 'audit-app',
                    name: 'Audit Test App',
                }),
                type: event_type_enum_1.EventType.SECURITY_ALERT,
                severity: event_severity_enum_1.EventSeverity.HIGH,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { alertType: 'audit-test' },
                tenantId,
                userId,
            });
            // Act
            const result = await orchestratorService.orchestrateSecurityWorkflow([event], {
                tenantId: tenantId.value,
                userId: userId.value,
                correlationId: correlationId.value,
                priority: 'HIGH',
                autoResponse: true,
            });
            // Assert
            expect(result.isSuccess()).toBe(true);
            const orchestrationResult = result.getValue();
            // Verify audit information is maintained
            expect(orchestrationResult.orchestrationId).toBeDefined();
            expect(orchestrationResult.startTime).toBeInstanceOf(Date);
            expect(orchestrationResult.endTime).toBeInstanceOf(Date);
            expect(orchestrationResult.duration).toBeGreaterThan(0);
            // Verify metrics are collected
            expect(orchestrationResult.metrics).toBeDefined();
            expect(orchestrationResult.metrics.totalProcessingTime).toBeGreaterThan(0);
            expect(orchestrationResult.metrics.throughput).toBeGreaterThan(0);
        });
        it('should handle security policies and compliance requirements', async () => {
            // Arrange
            const event = event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'compliance-test', version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: 'compliance-app',
                    name: 'Compliance Test App',
                }),
                type: event_type_enum_1.EventType.DATA_BREACH,
                severity: event_severity_enum_1.EventSeverity.CRITICAL,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: {
                    dataType: 'PII',
                    recordCount: 1000,
                    affectedUsers: ['user1', 'user2']
                },
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            // Act
            const result = await orchestratorService.orchestrateSecurityWorkflow([event], {
                priority: 'CRITICAL',
                autoResponse: true,
                customRules: [
                    {
                        id: 'data-breach-rule',
                        name: 'Data Breach Response',
                        description: 'Immediate response for data breaches',
                        condition: {
                            eventTypes: [event_type_enum_1.EventType.DATA_BREACH],
                            severityLevels: [event_severity_enum_1.EventSeverity.CRITICAL],
                        },
                        action: 'AUTO_RESPOND',
                        priority: 1,
                        enabled: true,
                    },
                ],
            });
            // Assert
            expect(result.isSuccess()).toBe(true);
            const orchestrationResult = result.getValue();
            expect(orchestrationResult.success).toBe(true);
            expect(orchestrationResult.eventsProcessed).toBe(1);
            // Should have triggered automated response due to critical data breach
            expect(orchestrationResult.actionsExecuted).toBeGreaterThan(0);
            expect(orchestrationResult.responseResults).toHaveLength(1);
            // Should have recommendations for compliance
            expect(orchestrationResult.recommendations).toBeDefined();
            expect(orchestrationResult.recommendations.length).toBeGreaterThan(0);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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