{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\tests\\integration\\database-integration.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,6CAAoE;AACpE,qCAAiD;AACjD,2CAA8C;AAC9C,sFAA2E;AAC3E,wFAAmF;AACnF,+EAA2E;AAC3E,qFAAiF;AAEjF;;;;;;;;;GASG;AACH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;IAC1C,IAAI,MAAqB,CAAC;IAC1B,IAAI,UAAsB,CAAC;IAC3B,IAAI,UAAwC,CAAC;IAC7C,IAAI,OAAgC,CAAC;IACrC,IAAI,YAAuC,CAAC;IAC5C,IAAI,cAA2C,CAAC;IAEhD,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,gBAAgB,GAAG;YACvB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;YACd,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;YACd,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;YACd,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;SACtB,CAAC;QAEF,MAAM,kBAAkB,GAAG;YACzB,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;YACxB,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE;YAC1B,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;SACvB,CAAC;QAEF,MAAM,GAAG,MAAM,cAAI,CAAC,mBAAmB,CAAC;YACtC,OAAO,EAAE;gBACP,qBAAY,CAAC,OAAO,CAAC;oBACnB,QAAQ,EAAE,IAAI;oBACd,WAAW,EAAE,WAAW;iBACzB,CAAC;gBACF,uBAAa,CAAC,OAAO,CAAC;oBACpB,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,WAAW;oBAC7C,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,MAAM,CAAC;oBAClD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,MAAM;oBAChD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,MAAM;oBAChD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,eAAe;oBACrD,QAAQ,EAAE,CAAC,2CAAgB,CAAC;oBAC5B,WAAW,EAAE,IAAI;oBACjB,UAAU,EAAE,IAAI;oBAChB,OAAO,EAAE,KAAK;iBACf,CAAC;gBACF,uBAAa,CAAC,UAAU,CAAC,CAAC,2CAAgB,CAAC,CAAC;aAC7C;YACD,SAAS,EAAE;gBACT,mDAAuB;gBACvB;oBACE,OAAO,EAAE,4BAAY;oBACrB,QAAQ,EAAE,gBAAgB;iBAC3B;gBACD;oBACE,OAAO,EAAE,gCAAc;oBACvB,QAAQ,EAAE,kBAAkB;iBAC7B;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,UAAU,GAAG,MAAM,CAAC,GAAG,CAAa,oBAAU,CAAC,CAAC;QAChD,UAAU,GAAG,MAAM,CAAC,GAAG,CAA+B,IAAA,4BAAkB,EAAC,2CAAgB,CAAC,CAAC,CAAC;QAC5F,OAAO,GAAG,MAAM,CAAC,GAAG,CAA0B,mDAAuB,CAAC,CAAC;QACvE,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,4BAAY,CAAC,CAAC;QACxC,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC,gCAAc,CAAC,CAAC;IAC9C,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,UAAU,CAAC,OAAO,EAAE,CAAC;QAC3B,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,qCAAqC;QACrC,MAAM,UAAU,CAAC,KAAK,EAAE,CAAC;QACzB,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,UAAU,GAAG;gBACjB,IAAI,EAAE,yBAAyB;gBAC/B,WAAW,EAAE,qCAAqC;gBAClD,QAAQ,EAAE,YAAY;gBACtB,IAAI,EAAE,SAAS;gBACf,UAAU,EAAE,YAAY;gBACxB,KAAK,EAAE,wDAAwD;gBAC/D,UAAU,EAAE;oBACV;wBACE,IAAI,EAAE,WAAW;wBACjB,IAAI,EAAE,MAAM;wBACZ,QAAQ,EAAE,IAAI;wBACd,YAAY,EAAE,IAAI;qBACnB;iBACF;gBACD,aAAa,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC;gBAC/B,QAAQ,EAAE;oBACR,OAAO,EAAE,IAAI;oBACb,cAAc,EAAE,WAAW;oBAC3B,QAAQ,EAAE,KAAK;iBAChB;aACF,CAAC;YAEF,MAAM,aAAa,GAAG,MAAM,OAAO,CAAC,sBAAsB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;YAEpF,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;YACvC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAClD,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE1C,qBAAqB;YACrB,MAAM,eAAe,GAAG,MAAM,UAAU,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,aAAa,CAAC,EAAE,EAAE,CAAC,CAAC;YAC7E,MAAM,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;YACtC,MAAM,CAAC,eAAgB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,UAAU,GAAG;gBACjB,IAAI,EAAE,2BAA2B;gBACjC,WAAW,EAAE,0CAA0C;gBACvD,QAAQ,EAAE,OAAO;gBACjB,IAAI,EAAE,OAAO;gBACb,UAAU,EAAE,OAAO;gBACnB,KAAK,EAAE,mGAAmG;gBAC1G,UAAU,EAAE;oBACV;wBACE,IAAI,EAAE,WAAW;wBACjB,IAAI,EAAE,QAAQ;wBACd,QAAQ,EAAE,IAAI;wBACd,OAAO,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC;wBAC9C,YAAY,EAAE,QAAQ;qBACvB;oBACD;wBACE,IAAI,EAAE,WAAW;wBACjB,IAAI,EAAE,MAAM;wBACZ,QAAQ,EAAE,IAAI;wBACd,YAAY,EAAE,IAAI;qBACnB;oBACD;wBACE,IAAI,EAAE,SAAS;wBACf,IAAI,EAAE,MAAM;wBACZ,QAAQ,EAAE,IAAI;wBACd,YAAY,EAAE,IAAI;qBACnB;iBACF;gBACD,aAAa,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;aAC9B,CAAC;YAEF,MAAM,aAAa,GAAG,MAAM,OAAO,CAAC,sBAAsB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;YAEpF,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACjD,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC;YAE3F,4CAA4C;YAC5C,MAAM,eAAe,GAAG,MAAM,UAAU,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,aAAa,CAAC,EAAE,EAAE,CAAC,CAAC;YAC7E,MAAM,CAAC,eAAgB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC;QAChG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,MAAM,UAAU,GAAG;gBACjB,IAAI,EAAE,yBAAyB;gBAC/B,WAAW,EAAE,qCAAqC;gBAClD,QAAQ,EAAE,YAAY;gBACtB,IAAI,EAAE,SAAS;gBACf,UAAU,EAAE,YAAY;gBACxB,KAAK,EAAE,+BAA+B;gBACtC,UAAU,EAAE,EAAE;gBACd,aAAa,EAAE,CAAC,KAAK,CAAC;aACvB,CAAC;YAEF,wBAAwB;YACxB,MAAM,aAAa,GAAG,MAAM,OAAO,CAAC,sBAAsB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;YACpF,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;YAEpC,4CAA4C;YAC5C,MAAM,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;iBAClE,OAAO,CAAC,OAAO,CAAC,iDAAiD,CAAC,CAAC;YAEtE,sCAAsC;YACtC,MAAM,cAAc,GAAG,MAAM,UAAU,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,aAAa,CAAC,EAAE,EAAE,CAAC,CAAC;YAC5E,MAAM,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAC;YAErC,gCAAgC;YAChC,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,IAAI,EAAE,CAAC;YAC3C,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,UAAU,GAAG;gBACjB,IAAI,EAAE,wBAAwB;gBAC9B,WAAW,EAAE,oCAAoC;gBACjD,QAAQ,EAAE,YAAY;gBACtB,IAAI,EAAE,SAAS;gBACf,UAAU,EAAE,YAAY;gBACxB,KAAK,EAAE,+BAA+B;gBACtC,UAAU,EAAE,EAAE;gBACd,aAAa,EAAE,CAAC,KAAK,CAAC;aACvB,CAAC;YAEF,MAAM,aAAa,GAAG,MAAM,OAAO,CAAC,sBAAsB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;YAEpF,8BAA8B;YAC9B,MAAM,cAAc,GAAG,OAAO,CAAC,sBAAsB,CACnD,aAAa,CAAC,EAAE,EAChB,EAAE,WAAW,EAAE,mBAAmB,EAAE,EACpC,QAAQ,CACT,CAAC;YAEF,MAAM,cAAc,GAAG,OAAO,CAAC,sBAAsB,CACnD,aAAa,CAAC,EAAE,EAChB,EAAE,WAAW,EAAE,mBAAmB,EAAE,EACpC,QAAQ,CACT,CAAC;YAEF,8CAA8C;YAC9C,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC,CAAC;YAE/E,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9B,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAE9B,qBAAqB;YACrB,MAAM,WAAW,GAAG,MAAM,UAAU,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,aAAa,CAAC,EAAE,EAAE,CAAC,CAAC;YACzE,MAAM,CAAC,WAAY,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,UAAU,GAAG;gBACjB,IAAI,EAAE,4BAA4B;gBAClC,WAAW,EAAE,uCAAuC;gBACpD,QAAQ,EAAE,YAAY;gBACtB,IAAI,EAAE,SAAS;gBACf,UAAU,EAAE,YAAY;gBACxB,KAAK,EAAE,+BAA+B;gBACtC,UAAU,EAAE,EAAE;gBACd,aAAa,EAAE,CAAC,KAAK,CAAC;aACvB,CAAC;YAEF,MAAM,aAAa,GAAG,MAAM,OAAO,CAAC,sBAAsB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;YAEpF,qCAAqC;YACrC,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9C,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9C,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAClD,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAClD,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE1C,+BAA+B;YAC/B,MAAM,iBAAiB,GAAG,aAAa,CAAC,SAAS,CAAC;YAClD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,cAAc;YAErE,MAAM,aAAa,GAAG,MAAM,OAAO,CAAC,sBAAsB,CACxD,aAAa,CAAC,EAAE,EAChB,EAAE,WAAW,EAAE,qBAAqB,EAAE,EACtC,cAAc,CACf,CAAC;YAEF,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,eAAe,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC,CAAC;YACvF,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACrD,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,0BAA0B;QAC/E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,WAAW,GAAG;gBAClB,IAAI,EAAE,oBAAoB;gBAC1B,WAAW,EAAE,cAAc;gBAC3B,QAAQ,EAAE,YAAY;gBACtB,IAAI,EAAE,SAAS;gBACf,UAAU,EAAE,YAAY;gBACxB,KAAK,EAAE,+BAA+B;gBACtC,UAAU,EAAE,EAAE;gBACd,aAAa,EAAE,CAAC,KAAK,CAAC;aACvB,CAAC;YAEF,MAAM,WAAW,GAAG;gBAClB,GAAG,WAAW;gBACd,WAAW,EAAE,8BAA8B;aAC5C,CAAC;YAEF,MAAM,OAAO,CAAC,sBAAsB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YAE/D,MAAM,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;iBACnE,OAAO,CAAC,OAAO,CAAC,iDAAiD,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,UAAU,CAAC,KAAK,IAAI,EAAE;YACpB,mBAAmB;YACnB,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;gBACpD,IAAI,EAAE,2BAA2B,CAAC,EAAE;gBACpC,WAAW,EAAE,eAAe,CAAC,0BAA0B;gBACvD,QAAQ,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO;gBAC9C,IAAI,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO;gBACvC,UAAU,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO;gBAChD,KAAK,EAAE,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,OAAO;gBACnE,UAAU,EAAE,EAAE;gBACd,aAAa,EAAE,CAAC,KAAK,CAAC;gBACtB,QAAQ,EAAE,IAAI;gBACd,SAAS,EAAE,WAAW;gBACtB,SAAS,EAAE,WAAW;gBACtB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC,CAAC;YAEJ,MAAM,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,oBAAoB,CAAC;gBAChD,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,EAAE;gBACT,QAAQ,EAAE,YAAY;aACvB,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAEzC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,kCAAkC;QAC1E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,oBAAoB,CAAC;gBAChD,MAAM,EAAE,kBAAkB;gBAC1B,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,EAAE;aACV,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAEzC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC3D,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,kCAAkC;YAExE,gDAAgD;YAChD,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACxC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;YAClE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,oBAAoB,CAAC;gBAChD,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,GAAG;aACX,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAEzC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC3D,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,mCAAmC;QAC3E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,MAAM,UAAU,GAAG;gBACjB,IAAI,EAAE,kBAAkB;gBACxB,WAAW,EAAE,6BAA6B;gBAC1C,QAAQ,EAAE,YAAY;gBACtB,IAAI,EAAE,SAAS;gBACf,UAAU,EAAE,YAAY;gBACxB,KAAK,EAAE,+BAA+B;gBACtC,UAAU,EAAE,EAAE;gBACd,aAAa,EAAE,CAAC,KAAK,CAAC;aACvB,CAAC;YAEF,MAAM,aAAa,GAAG,MAAM,OAAO,CAAC,sBAAsB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;YAEpF,oBAAoB;YACpB,MAAM,OAAO,CAAC,sBAAsB,CAAC,aAAa,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC;YAEtE,6CAA6C;YAC7C,MAAM,aAAa,GAAG,MAAM,OAAO,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;YAC7D,MAAM,CAAC,aAAa,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC;YAE7F,4DAA4D;YAC5D,MAAM,aAAa,GAAG,MAAM,UAAU,CAAC,OAAO,CAAC;gBAC7C,KAAK,EAAE,EAAE,EAAE,EAAE,aAAa,CAAC,EAAE,EAAE;gBAC/B,WAAW,EAAE,IAAI;aAClB,CAAC,CAAC;YACH,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,CAAC,aAAc,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,CAAC,aAAc,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,UAAU,GAAG;gBACjB,IAAI,EAAE,sBAAsB;gBAC5B,WAAW,EAAE,iCAAiC;gBAC9C,QAAQ,EAAE,YAAY;gBACtB,IAAI,EAAE,SAAS;gBACf,UAAU,EAAE,YAAY;gBACxB,KAAK,EAAE,+BAA+B;gBACtC,UAAU,EAAE,EAAE;gBACd,aAAa,EAAE,CAAC,KAAK,CAAC;aACvB,CAAC;YAEF,MAAM,aAAa,GAAG,MAAM,OAAO,CAAC,sBAAsB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;YACpF,MAAM,OAAO,CAAC,sBAAsB,CAAC,aAAa,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC;YAEtE,8CAA8C;YAC9C,MAAM,eAAe,GAAG,MAAM,OAAO,CAAC,uBAAuB,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;YAChF,MAAM,CAAC,eAAe,CAAC,CAAC,QAAQ,EAAE,CAAC;YAEnC,8BAA8B;YAC9B,MAAM,UAAU,GAAG,MAAM,OAAO,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;YAC1D,MAAM,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC;QAC5F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,UAAU,GAAG;gBACjB,IAAI,EAAE,wBAAwB;gBAC9B,WAAW,EAAE,mCAAmC;gBAChD,QAAQ,EAAE,YAAY;gBACtB,IAAI,EAAE,SAAS;gBACf,UAAU,EAAE,YAAY;gBACxB,KAAK,EAAE,+BAA+B;gBACtC,UAAU,EAAE,EAAE;gBACd,aAAa,EAAE,CAAC,KAAK,CAAC;aACvB,CAAC;YAEF,MAAM,aAAa,GAAG,MAAM,OAAO,CAAC,sBAAsB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;YAEpF,sCAAsC;YACtC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,CAAC;YAE7E,cAAc;YACd,IAAI,CAAC,aAAa,EAAE,CAAC;YAErB,gDAAgD;YAChD,YAAY,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,OAAO,CAAC,uBAAuB,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;YAExD,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAAC,qBAAqB,aAAa,CAAC,EAAE,EAAE,CAAC,CAAC;YACvF,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAC3C,qBAAqB,aAAa,CAAC,EAAE,EAAE,EACvC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAClB,IAAI,CACL,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,MAAM,UAAU,GAAG;gBACjB,IAAI,EAAE,0BAA0B;gBAChC,WAAW,EAAE,qCAAqC;gBAClD,QAAQ,EAAE,YAAY;gBACtB,IAAI,EAAE,SAAS;gBACf,UAAU,EAAE,YAAY;gBACxB,KAAK,EAAE,+BAA+B;gBACtC,UAAU,EAAE,EAAE;gBACd,aAAa,EAAE,CAAC,KAAK,CAAC;aACvB,CAAC;YAEF,MAAM,OAAO,CAAC,sBAAsB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;YAE9D,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,oBAAoB,CAAC,2BAA2B,EAAE;gBACrF,QAAQ,EAAE,YAAY;gBACtB,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\tests\\integration\\database-integration.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { TypeOrmModule, getRepositoryToken } from '@nestjs/typeorm';\r\nimport { Repository, DataSource } from 'typeorm';\r\nimport { ConfigModule } from '@nestjs/config';\r\nimport { ReportDefinition } from '../../entities/report-definition.entity';\r\nimport { ReportDefinitionService } from '../../services/report-definition.service';\r\nimport { CacheService } from '../../../infrastructure/cache/cache.service';\r\nimport { MetricsService } from '../../../infrastructure/metrics/metrics.service';\r\n\r\n/**\r\n * Database Integration Tests\r\n * \r\n * Tests database operations including:\r\n * - Entity persistence and retrieval\r\n * - Transaction management and rollback\r\n * - Data consistency and integrity\r\n * - Relationship handling and cascading\r\n * - Query performance and optimization\r\n */\r\ndescribe('Database Integration Tests', () => {\r\n  let module: TestingModule;\r\n  let dataSource: DataSource;\r\n  let repository: Repository<ReportDefinition>;\r\n  let service: ReportDefinitionService;\r\n  let cacheService: jest.Mocked<CacheService>;\r\n  let metricsService: jest.Mocked<MetricsService>;\r\n\r\n  beforeAll(async () => {\r\n    const mockCacheService = {\r\n      get: jest.fn(),\r\n      set: jest.fn(),\r\n      del: jest.fn(),\r\n      delPattern: jest.fn(),\r\n    };\r\n\r\n    const mockMetricsService = {\r\n      recordCounter: jest.fn(),\r\n      recordHistogram: jest.fn(),\r\n      recordGauge: jest.fn(),\r\n    };\r\n\r\n    module = await Test.createTestingModule({\r\n      imports: [\r\n        ConfigModule.forRoot({\r\n          isGlobal: true,\r\n          envFilePath: '.env.test',\r\n        }),\r\n        TypeOrmModule.forRoot({\r\n          type: 'postgres',\r\n          host: process.env.TEST_DB_HOST || 'localhost',\r\n          port: parseInt(process.env.TEST_DB_PORT || '5433'),\r\n          username: process.env.TEST_DB_USERNAME || 'test',\r\n          password: process.env.TEST_DB_PASSWORD || 'test',\r\n          database: process.env.TEST_DB_NAME || 'sentinel_test',\r\n          entities: [ReportDefinition],\r\n          synchronize: true,\r\n          dropSchema: true,\r\n          logging: false,\r\n        }),\r\n        TypeOrmModule.forFeature([ReportDefinition]),\r\n      ],\r\n      providers: [\r\n        ReportDefinitionService,\r\n        {\r\n          provide: CacheService,\r\n          useValue: mockCacheService,\r\n        },\r\n        {\r\n          provide: MetricsService,\r\n          useValue: mockMetricsService,\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    dataSource = module.get<DataSource>(DataSource);\r\n    repository = module.get<Repository<ReportDefinition>>(getRepositoryToken(ReportDefinition));\r\n    service = module.get<ReportDefinitionService>(ReportDefinitionService);\r\n    cacheService = module.get(CacheService);\r\n    metricsService = module.get(MetricsService);\r\n  });\r\n\r\n  afterAll(async () => {\r\n    await dataSource.destroy();\r\n    await module.close();\r\n  });\r\n\r\n  beforeEach(async () => {\r\n    // Clean up database before each test\r\n    await repository.clear();\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('Entity Persistence', () => {\r\n    it('should create and persist report definition', async () => {\r\n      const reportData = {\r\n        name: 'Integration Test Report',\r\n        description: 'Test report for integration testing',\r\n        category: 'compliance',\r\n        type: 'tabular',\r\n        dataSource: 'compliance',\r\n        query: 'SELECT * FROM compliance_data WHERE date >= :startDate',\r\n        parameters: [\r\n          {\r\n            name: 'startDate',\r\n            type: 'date',\r\n            required: true,\r\n            defaultValue: null,\r\n          },\r\n        ],\r\n        outputFormats: ['pdf', 'excel'],\r\n        schedule: {\r\n          enabled: true,\r\n          cronExpression: '0 9 * * 1',\r\n          timezone: 'UTC',\r\n        },\r\n      };\r\n\r\n      const createdReport = await service.createReportDefinition(reportData, 'test-user');\r\n\r\n      expect(createdReport).toBeDefined();\r\n      expect(createdReport.id).toBeDefined();\r\n      expect(createdReport.name).toBe(reportData.name);\r\n      expect(createdReport.createdBy).toBe('test-user');\r\n      expect(createdReport.isActive).toBe(true);\r\n\r\n      // Verify persistence\r\n      const retrievedReport = await repository.findOneBy({ id: createdReport.id });\r\n      expect(retrievedReport).toBeDefined();\r\n      expect(retrievedReport!.name).toBe(reportData.name);\r\n    });\r\n\r\n    it('should handle complex parameter structures', async () => {\r\n      const reportData = {\r\n        name: 'Complex Parameters Report',\r\n        description: 'Report with complex parameter structures',\r\n        category: 'audit',\r\n        type: 'chart',\r\n        dataSource: 'audit',\r\n        query: 'SELECT * FROM audit_events WHERE risk_level = :riskLevel AND date BETWEEN :startDate AND :endDate',\r\n        parameters: [\r\n          {\r\n            name: 'riskLevel',\r\n            type: 'select',\r\n            required: true,\r\n            options: ['low', 'medium', 'high', 'critical'],\r\n            defaultValue: 'medium',\r\n          },\r\n          {\r\n            name: 'startDate',\r\n            type: 'date',\r\n            required: true,\r\n            defaultValue: null,\r\n          },\r\n          {\r\n            name: 'endDate',\r\n            type: 'date',\r\n            required: true,\r\n            defaultValue: null,\r\n          },\r\n        ],\r\n        outputFormats: ['pdf', 'csv'],\r\n      };\r\n\r\n      const createdReport = await service.createReportDefinition(reportData, 'test-user');\r\n\r\n      expect(createdReport.parameters).toHaveLength(3);\r\n      expect(createdReport.parameters[0].options).toEqual(['low', 'medium', 'high', 'critical']);\r\n\r\n      // Verify JSON serialization/deserialization\r\n      const retrievedReport = await repository.findOneBy({ id: createdReport.id });\r\n      expect(retrievedReport!.parameters[0].options).toEqual(['low', 'medium', 'high', 'critical']);\r\n    });\r\n  });\r\n\r\n  describe('Transaction Management', () => {\r\n    it('should rollback transaction on error', async () => {\r\n      const reportData = {\r\n        name: 'Transaction Test Report',\r\n        description: 'Test report for transaction testing',\r\n        category: 'compliance',\r\n        type: 'tabular',\r\n        dataSource: 'compliance',\r\n        query: 'SELECT * FROM compliance_data',\r\n        parameters: [],\r\n        outputFormats: ['pdf'],\r\n      };\r\n\r\n      // Create initial report\r\n      const createdReport = await service.createReportDefinition(reportData, 'test-user');\r\n      expect(createdReport).toBeDefined();\r\n\r\n      // Attempt to create duplicate (should fail)\r\n      await expect(service.createReportDefinition(reportData, 'test-user'))\r\n        .rejects.toThrow('Report definition with this name already exists');\r\n\r\n      // Verify original report still exists\r\n      const existingReport = await repository.findOneBy({ id: createdReport.id });\r\n      expect(existingReport).toBeDefined();\r\n\r\n      // Verify only one report exists\r\n      const allReports = await repository.find();\r\n      expect(allReports).toHaveLength(1);\r\n    });\r\n\r\n    it('should handle concurrent updates correctly', async () => {\r\n      const reportData = {\r\n        name: 'Concurrent Update Test',\r\n        description: 'Test report for concurrent updates',\r\n        category: 'compliance',\r\n        type: 'tabular',\r\n        dataSource: 'compliance',\r\n        query: 'SELECT * FROM compliance_data',\r\n        parameters: [],\r\n        outputFormats: ['pdf'],\r\n      };\r\n\r\n      const createdReport = await service.createReportDefinition(reportData, 'test-user');\r\n\r\n      // Simulate concurrent updates\r\n      const update1Promise = service.updateReportDefinition(\r\n        createdReport.id,\r\n        { description: 'Updated by user 1' },\r\n        'user-1'\r\n      );\r\n\r\n      const update2Promise = service.updateReportDefinition(\r\n        createdReport.id,\r\n        { description: 'Updated by user 2' },\r\n        'user-2'\r\n      );\r\n\r\n      // Both updates should succeed (last one wins)\r\n      const [result1, result2] = await Promise.all([update1Promise, update2Promise]);\r\n\r\n      expect(result1).toBeDefined();\r\n      expect(result2).toBeDefined();\r\n\r\n      // Verify final state\r\n      const finalReport = await repository.findOneBy({ id: createdReport.id });\r\n      expect(finalReport!.description).toMatch(/Updated by user [12]/);\r\n    });\r\n  });\r\n\r\n  describe('Data Consistency', () => {\r\n    it('should maintain referential integrity', async () => {\r\n      const reportData = {\r\n        name: 'Referential Integrity Test',\r\n        description: 'Test report for referential integrity',\r\n        category: 'compliance',\r\n        type: 'tabular',\r\n        dataSource: 'compliance',\r\n        query: 'SELECT * FROM compliance_data',\r\n        parameters: [],\r\n        outputFormats: ['pdf'],\r\n      };\r\n\r\n      const createdReport = await service.createReportDefinition(reportData, 'test-user');\r\n\r\n      // Verify all required fields are set\r\n      expect(createdReport.createdAt).toBeDefined();\r\n      expect(createdReport.updatedAt).toBeDefined();\r\n      expect(createdReport.createdBy).toBe('test-user');\r\n      expect(createdReport.updatedBy).toBe('test-user');\r\n      expect(createdReport.isActive).toBe(true);\r\n\r\n      // Update and verify timestamps\r\n      const originalUpdatedAt = createdReport.updatedAt;\r\n      await new Promise(resolve => setTimeout(resolve, 10)); // Small delay\r\n\r\n      const updatedReport = await service.updateReportDefinition(\r\n        createdReport.id,\r\n        { description: 'Updated description' },\r\n        'another-user'\r\n      );\r\n\r\n      expect(updatedReport.updatedAt.getTime()).toBeGreaterThan(originalUpdatedAt.getTime());\r\n      expect(updatedReport.updatedBy).toBe('another-user');\r\n      expect(updatedReport.createdBy).toBe('test-user'); // Should remain unchanged\r\n    });\r\n\r\n    it('should enforce unique constraints', async () => {\r\n      const reportData1 = {\r\n        name: 'Unique Test Report',\r\n        description: 'First report',\r\n        category: 'compliance',\r\n        type: 'tabular',\r\n        dataSource: 'compliance',\r\n        query: 'SELECT * FROM compliance_data',\r\n        parameters: [],\r\n        outputFormats: ['pdf'],\r\n      };\r\n\r\n      const reportData2 = {\r\n        ...reportData1,\r\n        description: 'Second report with same name',\r\n      };\r\n\r\n      await service.createReportDefinition(reportData1, 'test-user');\r\n\r\n      await expect(service.createReportDefinition(reportData2, 'test-user'))\r\n        .rejects.toThrow('Report definition with this name already exists');\r\n    });\r\n  });\r\n\r\n  describe('Query Performance', () => {\r\n    beforeEach(async () => {\r\n      // Create test data\r\n      const reports = Array.from({ length: 50 }, (_, i) => ({\r\n        name: `Performance Test Report ${i}`,\r\n        description: `Test report ${i} for performance testing`,\r\n        category: i % 2 === 0 ? 'compliance' : 'audit',\r\n        type: i % 3 === 0 ? 'tabular' : 'chart',\r\n        dataSource: i % 2 === 0 ? 'compliance' : 'audit',\r\n        query: `SELECT * FROM ${i % 2 === 0 ? 'compliance' : 'audit'}_data`,\r\n        parameters: [],\r\n        outputFormats: ['pdf'],\r\n        isActive: true,\r\n        createdBy: 'test-user',\r\n        updatedBy: 'test-user',\r\n        createdAt: new Date(),\r\n        updatedAt: new Date(),\r\n      }));\r\n\r\n      await repository.save(reports);\r\n    });\r\n\r\n    it('should perform efficient pagination queries', async () => {\r\n      const startTime = Date.now();\r\n\r\n      const result = await service.getReportDefinitions({\r\n        page: 1,\r\n        limit: 10,\r\n        category: 'compliance',\r\n      });\r\n\r\n      const queryTime = Date.now() - startTime;\r\n\r\n      expect(result.reportDefinitions).toHaveLength(10);\r\n      expect(result.total).toBeGreaterThan(0);\r\n      expect(queryTime).toBeLessThan(1000); // Should complete within 1 second\r\n    });\r\n\r\n    it('should perform efficient search queries', async () => {\r\n      const startTime = Date.now();\r\n\r\n      const result = await service.getReportDefinitions({\r\n        search: 'Performance Test',\r\n        page: 1,\r\n        limit: 20,\r\n      });\r\n\r\n      const queryTime = Date.now() - startTime;\r\n\r\n      expect(result.reportDefinitions.length).toBeGreaterThan(0);\r\n      expect(queryTime).toBeLessThan(1000); // Should complete within 1 second\r\n\r\n      // Verify search results contain the search term\r\n      result.reportDefinitions.forEach(report => {\r\n        expect(report.name.toLowerCase()).toContain('performance test');\r\n      });\r\n    });\r\n\r\n    it('should handle large result sets efficiently', async () => {\r\n      const startTime = Date.now();\r\n\r\n      const result = await service.getReportDefinitions({\r\n        page: 1,\r\n        limit: 100,\r\n      });\r\n\r\n      const queryTime = Date.now() - startTime;\r\n\r\n      expect(result.reportDefinitions.length).toBeGreaterThan(0);\r\n      expect(queryTime).toBeLessThan(2000); // Should complete within 2 seconds\r\n    });\r\n  });\r\n\r\n  describe('Soft Delete Functionality', () => {\r\n    it('should soft delete report definition', async () => {\r\n      const reportData = {\r\n        name: 'Soft Delete Test',\r\n        description: 'Test report for soft delete',\r\n        category: 'compliance',\r\n        type: 'tabular',\r\n        dataSource: 'compliance',\r\n        query: 'SELECT * FROM compliance_data',\r\n        parameters: [],\r\n        outputFormats: ['pdf'],\r\n      };\r\n\r\n      const createdReport = await service.createReportDefinition(reportData, 'test-user');\r\n\r\n      // Delete the report\r\n      await service.deleteReportDefinition(createdReport.id, 'delete-user');\r\n\r\n      // Verify it's not returned in active queries\r\n      const activeReports = await service.getReportDefinitions({});\r\n      expect(activeReports.reportDefinitions.find(r => r.id === createdReport.id)).toBeUndefined();\r\n\r\n      // Verify it still exists in database but marked as inactive\r\n      const deletedReport = await repository.findOne({\r\n        where: { id: createdReport.id },\r\n        withDeleted: true,\r\n      });\r\n      expect(deletedReport).toBeDefined();\r\n      expect(deletedReport!.isActive).toBe(false);\r\n      expect(deletedReport!.updatedBy).toBe('delete-user');\r\n    });\r\n\r\n    it('should not return soft deleted reports in queries', async () => {\r\n      const reportData = {\r\n        name: 'Query Exclusion Test',\r\n        description: 'Test report for query exclusion',\r\n        category: 'compliance',\r\n        type: 'tabular',\r\n        dataSource: 'compliance',\r\n        query: 'SELECT * FROM compliance_data',\r\n        parameters: [],\r\n        outputFormats: ['pdf'],\r\n      };\r\n\r\n      const createdReport = await service.createReportDefinition(reportData, 'test-user');\r\n      await service.deleteReportDefinition(createdReport.id, 'delete-user');\r\n\r\n      // Verify getReportDefinitionById returns null\r\n      const retrievedReport = await service.getReportDefinitionById(createdReport.id);\r\n      expect(retrievedReport).toBeNull();\r\n\r\n      // Verify it's not in the list\r\n      const allReports = await service.getReportDefinitions({});\r\n      expect(allReports.reportDefinitions.find(r => r.id === createdReport.id)).toBeUndefined();\r\n    });\r\n  });\r\n\r\n  describe('Cache Integration', () => {\r\n    it('should integrate with cache service', async () => {\r\n      const reportData = {\r\n        name: 'Cache Integration Test',\r\n        description: 'Test report for cache integration',\r\n        category: 'compliance',\r\n        type: 'tabular',\r\n        dataSource: 'compliance',\r\n        query: 'SELECT * FROM compliance_data',\r\n        parameters: [],\r\n        outputFormats: ['pdf'],\r\n      };\r\n\r\n      const createdReport = await service.createReportDefinition(reportData, 'test-user');\r\n\r\n      // Verify cache operations were called\r\n      expect(cacheService.delPattern).toHaveBeenCalledWith('report_definitions_*');\r\n\r\n      // Reset mocks\r\n      jest.clearAllMocks();\r\n\r\n      // Get report by ID (should attempt cache first)\r\n      cacheService.get.mockResolvedValue(null);\r\n      await service.getReportDefinitionById(createdReport.id);\r\n\r\n      expect(cacheService.get).toHaveBeenCalledWith(`report_definition_${createdReport.id}`);\r\n      expect(cacheService.set).toHaveBeenCalledWith(\r\n        `report_definition_${createdReport.id}`,\r\n        expect.any(Object),\r\n        3600\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('Metrics Integration', () => {\r\n    it('should record metrics for database operations', async () => {\r\n      const reportData = {\r\n        name: 'Metrics Integration Test',\r\n        description: 'Test report for metrics integration',\r\n        category: 'compliance',\r\n        type: 'tabular',\r\n        dataSource: 'compliance',\r\n        query: 'SELECT * FROM compliance_data',\r\n        parameters: [],\r\n        outputFormats: ['pdf'],\r\n      };\r\n\r\n      await service.createReportDefinition(reportData, 'test-user');\r\n\r\n      expect(metricsService.recordCounter).toHaveBeenCalledWith('report_definition_created', {\r\n        category: 'compliance',\r\n        type: 'tabular',\r\n      });\r\n    });\r\n  });\r\n});\r\n"], "version": 3}