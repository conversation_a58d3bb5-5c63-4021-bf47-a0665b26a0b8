442c701f71d88adf914a43d630f74cd5
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseApiController = void 0;
const common_1 = require("@nestjs/common");
/**
 * Base API Controller
 * Provides shared functionality for all API controllers
 */
let BaseApiController = class BaseApiController {
    constructor(controllerName) {
        this.logger = new common_1.Logger(controllerName);
    }
    /**
     * Create standardized success response
     */
    createSuccessResponse(data, message, metadata) {
        return {
            success: true,
            data,
            message: message || 'Operation completed successfully',
            timestamp: new Date().toISOString(),
            metadata: metadata || {},
        };
    }
    /**
     * Create standardized error response
     */
    createErrorResponse(error, code, details) {
        const errorMessage = typeof error === 'string' ? error : error.message;
        const errorCode = code || 'INTERNAL_ERROR';
        return {
            success: false,
            error: {
                code: errorCode,
                message: errorMessage,
                details: details || {},
                timestamp: new Date().toISOString(),
            },
        };
    }
    /**
     * Create paginated response
     */
    createPaginatedResponse(data, pagination, message) {
        return {
            success: true,
            data,
            message: message || 'Data retrieved successfully',
            timestamp: new Date().toISOString(),
            pagination,
            metadata: {
                totalItems: pagination.total,
                itemsPerPage: pagination.limit,
                currentPage: pagination.page,
                totalPages: pagination.totalPages,
            },
        };
    }
    /**
     * Extract pagination parameters from request
     */
    extractPaginationParams(req) {
        const page = Math.max(1, parseInt(req.query.page) || 1);
        const limit = Math.min(100, Math.max(1, parseInt(req.query.limit) || 10));
        const offset = (page - 1) * limit;
        return { page, limit, offset };
    }
    /**
     * Extract sorting parameters from request
     */
    extractSortingParams(req) {
        const sortBy = req.query.sortBy || 'createdAt';
        const sortOrder = req.query.sortOrder || 'desc';
        return { sortBy, sortOrder };
    }
    /**
     * Extract filtering parameters from request
     */
    extractFilterParams(req) {
        const filters = {};
        // Extract common filter parameters
        Object.keys(req.query).forEach(key => {
            if (key.startsWith('filter_')) {
                const filterKey = key.replace('filter_', '');
                filters[filterKey] = req.query[key];
            }
        });
        return filters;
    }
    /**
     * Log API request
     */
    logRequest(req, action, additionalData) {
        this.logger.log(`API Request: ${action}`, {
            method: req.method,
            url: req.originalUrl,
            userAgent: req.headers['user-agent'],
            ip: req.ip,
            userId: req.user?.id,
            correlationId: req.headers['x-correlation-id'],
            ...additionalData,
        });
    }
    /**
     * Log API response
     */
    logResponse(req, action, success, processingTime, additionalData) {
        this.logger.log(`API Response: ${action}`, {
            method: req.method,
            url: req.originalUrl,
            success,
            processingTime: processingTime ? `${processingTime}ms` : undefined,
            userId: req.user?.id,
            correlationId: req.headers['x-correlation-id'],
            ...additionalData,
        });
    }
    /**
     * Handle API errors consistently
     */
    handleError(error, req, action) {
        this.logger.error(`API Error: ${action}`, {
            error: error.message,
            stack: error.stack,
            method: req.method,
            url: req.originalUrl,
            userId: req.user?.id,
            correlationId: req.headers['x-correlation-id'],
        });
        return this.createErrorResponse(error);
    }
    /**
     * Validate required parameters
     */
    validateRequiredParams(params, requiredFields) {
        const missingFields = requiredFields.filter(field => params[field] === undefined || params[field] === null || params[field] === '');
        if (missingFields.length > 0) {
            throw new Error(`Missing required parameters: ${missingFields.join(', ')}`);
        }
    }
    /**
     * Sanitize input data
     */
    sanitizeInput(input) {
        if (typeof input === 'string') {
            // Basic XSS protection
            return input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
        }
        if (Array.isArray(input)) {
            return input.map(item => this.sanitizeInput(item));
        }
        if (typeof input === 'object' && input !== null) {
            const sanitized = {};
            Object.keys(input).forEach(key => {
                sanitized[key] = this.sanitizeInput(input[key]);
            });
            return sanitized;
        }
        return input;
    }
    /**
     * Check if user has required permissions
     */
    checkPermissions(req, requiredPermissions) {
        const userPermissions = req.user?.permissions || [];
        return requiredPermissions.every(permission => userPermissions.includes(permission));
    }
    /**
     * Get user context from request
     */
    getUserContext(req) {
        return {
            id: req.user?.id,
            email: req.user?.email,
            roles: req.user?.roles || [],
            permissions: req.user?.permissions || [],
            organizationId: req.user?.organizationId,
        };
    }
    /**
     * Create audit log entry
     */
    createAuditLog(req, action, resource, outcome, details) {
        return {
            userId: req.user?.id,
            action,
            resource,
            outcome,
            timestamp: new Date().toISOString(),
            ipAddress: req.ip,
            userAgent: req.headers['user-agent'],
            correlationId: req.headers['x-correlation-id'],
            details: details || {},
        };
    }
};
exports.BaseApiController = BaseApiController;
exports.BaseApiController = BaseApiController = __decorate([
    (0, common_1.Controller)(),
    __metadata("design:paramtypes", [String])
], BaseApiController);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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