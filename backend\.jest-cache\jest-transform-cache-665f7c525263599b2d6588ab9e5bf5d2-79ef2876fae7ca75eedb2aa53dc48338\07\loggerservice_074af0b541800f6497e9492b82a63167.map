{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\logging\\logger.service.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,2CAAgF;AAChF,+CAAuD;AACvD,2CAAwC;AACxC,qCAAiC;AACjC,qEAAgE;AAEhE;;;GAGG;AAEI,IAAM,aAAa,qBAAnB,MAAM,aAAa;IACxB,YAEmB,MAAc,EACd,oBAA0C;QAD1C,WAAM,GAAN,MAAM,CAAQ;QACd,yBAAoB,GAApB,oBAAoB,CAAsB;IAC1D,CAAC;IAEJ;;;;OAIG;IACH,GAAG,CAAC,OAAe,EAAE,OAAyB;QAC5C,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC9B,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,OAAe,EAAE,KAAc,EAAE,OAAyB;QAC9D,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAEpD,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QACxB,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACH,IAAI,CAAC,OAAe,EAAE,OAAyB;QAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACpD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC5B,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,OAAe,EAAE,OAAyB;QAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACpD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACH,OAAO,CAAC,OAAe,EAAE,OAAyB;QAChD,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACpD,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;IAED;;;;OAIG;IACH,IAAI,CAAC,OAAe,EAAE,OAAyB;QAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACpD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC5B,CAAC;IAED;;;;;;;OAOG;IACH,cAAc,CACZ,MAAc,EACd,GAAW,EACX,UAAkB,EAClB,QAAgB,EAChB,QAAiB;QAEjB,MAAM,OAAO,GAAG;YACd,OAAO,EAAE,GAAG,MAAM,IAAI,GAAG,IAAI,UAAU,MAAM,QAAQ,IAAI;YACzD,IAAI,EAAE,cAAc;YACpB,MAAM;YACN,GAAG;YACH,UAAU;YACV,QAAQ;YACR,GAAG,QAAQ;SACZ,CAAC;QAEF,IAAI,UAAU,IAAI,GAAG,EAAE,CAAC;YACtB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC7B,CAAC;aAAM,IAAI,UAAU,IAAI,GAAG,EAAE,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,oBAAoB,CAClB,SAAiB,EACjB,KAAa,EACb,QAAgB,EAChB,QAAiB;QAEjB,MAAM,OAAO,GAAG;YACd,OAAO,EAAE,YAAY,SAAS,OAAO,KAAK,MAAM,QAAQ,IAAI;YAC5D,IAAI,EAAE,oBAAoB;YAC1B,SAAS;YACT,KAAK;YACL,QAAQ;YACR,GAAG,QAAQ;SACZ,CAAC;QAEF,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;YACpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;IAED;;;;;;;OAOG;IACH,sBAAsB,CACpB,OAAe,EACf,SAAiB,EACjB,QAAgB,EAChB,OAAgB,EAChB,QAAiB;QAEjB,MAAM,OAAO,GAAG;YACd,OAAO,EAAE,4BAA4B,OAAO,IAAI,SAAS,MAAM,QAAQ,QAAQ,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE;YAC/G,IAAI,EAAE,uBAAuB;YAC7B,OAAO;YACP,SAAS;YACT,QAAQ;YACR,OAAO;YACP,GAAG,QAAQ;SACZ,CAAC;QAEF,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC7B,CAAC;aAAM,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;YAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,oBAAoB,CAClB,MAAc,EACd,KAAa,EACb,IAAY,EACZ,QAAiB;QAEjB,MAAM,OAAO,GAAG;YACd,OAAO,EAAE,uBAAuB,MAAM,MAAM,KAAK,GAAG,IAAI,EAAE;YAC1D,IAAI,EAAE,oBAAoB;YAC1B,MAAM;YACN,KAAK;YACL,IAAI;YACJ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,GAAG,QAAQ;SACZ,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC5B,CAAC;IAED;;;;;;OAMG;IACH,gBAAgB,CACd,KAAa,EACb,MAAc,EACd,QAAgB,EAChB,QAAiB;QAEjB,MAAM,OAAO,GAAG;YACd,OAAO,EAAE,mBAAmB,KAAK,OAAO,MAAM,IAAI,QAAQ,EAAE;YAC5D,IAAI,EAAE,gBAAgB;YACtB,KAAK;YACL,MAAM;YACN,QAAQ;YACR,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,GAAG,QAAQ;SACZ,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC5B,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,OAAe;QACnB,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC/C,MAAM,YAAY,GAAG,IAAI,eAAa,CAAC,WAAkB,CAAC,CAAC;QAC3D,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;;;;OAKG;IACK,YAAY,CAAC,OAAe,EAAE,OAAyB;QAC7D,MAAM,OAAO,GAAQ;YACnB,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAEF,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;gBAChC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;YAC5B,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAClC,CAAC;QACH,CAAC;QAED,mDAAmD;QACnD,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9C,IAAI,aAAa,EAAE,CAAC;YAClB,OAAO,CAAC,aAAa,GAAG,aAAa,CAAC;QACxC,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;OAGG;IACK,gBAAgB;QACtB,OAAO,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,CAAC;IACtD,CAAC;CACF,CAAA;AA5QY,sCAAa;wBAAb,aAAa;IADzB,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,eAAM,EAAC,sCAAuB,CAAC,CAAA;yDACP,gBAAM,oBAAN,gBAAM,oDACQ,6CAAoB,oBAApB,6CAAoB;GAJlD,aAAa,CA4QzB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\logging\\logger.service.ts"], "sourcesContent": ["import { Injectable, LoggerService as NestLoggerService } from '@nestjs/common';\r\nimport { WINSTON_MODULE_PROVIDER } from 'nest-winston';\r\nimport { Inject } from '@nestjs/common';\r\nimport { Logger } from 'winston';\r\nimport { CorrelationIdService } from './correlation-id.service';\r\n\r\n/**\r\n * Enhanced logger service that wraps Winston logger\r\n * Provides structured logging with correlation ID support\r\n */\r\n@Injectable()\r\nexport class LoggerService implements NestLoggerService {\r\n  constructor(\r\n    @Inject(WINSTON_MODULE_PROVIDER)\r\n    private readonly logger: Logger,\r\n    private readonly correlationIdService: CorrelationIdService,\r\n  ) {}\r\n\r\n  /**\r\n   * Log a message at info level\r\n   * @param message Log message\r\n   * @param context Optional context or metadata\r\n   */\r\n  log(message: string, context?: string | object): void {\r\n    this.info(message, context);\r\n  }\r\n\r\n  /**\r\n   * Log a message at error level\r\n   * @param message Error message\r\n   * @param trace Optional stack trace\r\n   * @param context Optional context or metadata\r\n   */\r\n  error(message: string, trace?: string, context?: string | object): void {\r\n    const logData = this.buildLogData(message, context);\r\n    \r\n    if (trace) {\r\n      logData.stack = trace;\r\n    }\r\n\r\n    this.logger.error(logData);\r\n  }\r\n\r\n  /**\r\n   * Log a message at warn level\r\n   * @param message Warning message\r\n   * @param context Optional context or metadata\r\n   */\r\n  warn(message: string, context?: string | object): void {\r\n    const logData = this.buildLogData(message, context);\r\n    this.logger.warn(logData);\r\n  }\r\n\r\n  /**\r\n   * Log a message at debug level\r\n   * @param message Debug message\r\n   * @param context Optional context or metadata\r\n   */\r\n  debug(message: string, context?: string | object): void {\r\n    const logData = this.buildLogData(message, context);\r\n    this.logger.debug(logData);\r\n  }\r\n\r\n  /**\r\n   * Log a message at verbose level\r\n   * @param message Verbose message\r\n   * @param context Optional context or metadata\r\n   */\r\n  verbose(message: string, context?: string | object): void {\r\n    const logData = this.buildLogData(message, context);\r\n    this.logger.verbose(logData);\r\n  }\r\n\r\n  /**\r\n   * Log a message at info level\r\n   * @param message Info message\r\n   * @param context Optional context or metadata\r\n   */\r\n  info(message: string, context?: string | object): void {\r\n    const logData = this.buildLogData(message, context);\r\n    this.logger.info(logData);\r\n  }\r\n\r\n  /**\r\n   * Log HTTP request information\r\n   * @param method HTTP method\r\n   * @param url Request URL\r\n   * @param statusCode Response status code\r\n   * @param duration Request duration in milliseconds\r\n   * @param metadata Additional metadata\r\n   */\r\n  logHttpRequest(\r\n    method: string,\r\n    url: string,\r\n    statusCode: number,\r\n    duration: number,\r\n    metadata?: object,\r\n  ): void {\r\n    const logData = {\r\n      message: `${method} ${url} ${statusCode} - ${duration}ms`,\r\n      type: 'HTTP_REQUEST',\r\n      method,\r\n      url,\r\n      statusCode,\r\n      duration,\r\n      ...metadata,\r\n    };\r\n\r\n    if (statusCode >= 500) {\r\n      this.logger.error(logData);\r\n    } else if (statusCode >= 400) {\r\n      this.logger.warn(logData);\r\n    } else {\r\n      this.logger.info(logData);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Log database operation\r\n   * @param operation Database operation type\r\n   * @param table Table name\r\n   * @param duration Operation duration in milliseconds\r\n   * @param metadata Additional metadata\r\n   */\r\n  logDatabaseOperation(\r\n    operation: string,\r\n    table: string,\r\n    duration: number,\r\n    metadata?: object,\r\n  ): void {\r\n    const logData = {\r\n      message: `Database ${operation} on ${table} - ${duration}ms`,\r\n      type: 'DATABASE_OPERATION',\r\n      operation,\r\n      table,\r\n      duration,\r\n      ...metadata,\r\n    };\r\n\r\n    if (duration > 1000) {\r\n      this.logger.warn(logData);\r\n    } else {\r\n      this.logger.debug(logData);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Log external service call\r\n   * @param service Service name\r\n   * @param operation Operation performed\r\n   * @param duration Call duration in milliseconds\r\n   * @param success Whether the call was successful\r\n   * @param metadata Additional metadata\r\n   */\r\n  logExternalServiceCall(\r\n    service: string,\r\n    operation: string,\r\n    duration: number,\r\n    success: boolean,\r\n    metadata?: object,\r\n  ): void {\r\n    const logData = {\r\n      message: `External service call to ${service}.${operation} - ${duration}ms - ${success ? 'SUCCESS' : 'FAILED'}`,\r\n      type: 'EXTERNAL_SERVICE_CALL',\r\n      service,\r\n      operation,\r\n      duration,\r\n      success,\r\n      ...metadata,\r\n    };\r\n\r\n    if (!success) {\r\n      this.logger.error(logData);\r\n    } else if (duration > 5000) {\r\n      this.logger.warn(logData);\r\n    } else {\r\n      this.logger.info(logData);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Log performance metrics\r\n   * @param metric Metric name\r\n   * @param value Metric value\r\n   * @param unit Metric unit\r\n   * @param metadata Additional metadata\r\n   */\r\n  logPerformanceMetric(\r\n    metric: string,\r\n    value: number,\r\n    unit: string,\r\n    metadata?: object,\r\n  ): void {\r\n    const logData = {\r\n      message: `Performance metric: ${metric} = ${value}${unit}`,\r\n      type: 'PERFORMANCE_METRIC',\r\n      metric,\r\n      value,\r\n      unit,\r\n      timestamp: new Date().toISOString(),\r\n      ...metadata,\r\n    };\r\n\r\n    this.logger.info(logData);\r\n  }\r\n\r\n  /**\r\n   * Log business event\r\n   * @param event Event name\r\n   * @param entity Entity type\r\n   * @param entityId Entity ID\r\n   * @param metadata Additional metadata\r\n   */\r\n  logBusinessEvent(\r\n    event: string,\r\n    entity: string,\r\n    entityId: string,\r\n    metadata?: object,\r\n  ): void {\r\n    const logData = {\r\n      message: `Business event: ${event} on ${entity}:${entityId}`,\r\n      type: 'BUSINESS_EVENT',\r\n      event,\r\n      entity,\r\n      entityId,\r\n      timestamp: new Date().toISOString(),\r\n      ...metadata,\r\n    };\r\n\r\n    this.logger.info(logData);\r\n  }\r\n\r\n  /**\r\n   * Create a child logger with additional context\r\n   * @param context Context to add to all log messages\r\n   * @returns Child logger instance\r\n   */\r\n  child(context: object): LoggerService {\r\n    const childLogger = this.logger.child(context);\r\n    const childService = new LoggerService(childLogger as any);\r\n    return childService;\r\n  }\r\n\r\n  /**\r\n   * Build log data object from message and context\r\n   * @param message Log message\r\n   * @param context Context or metadata\r\n   * @returns Log data object\r\n   */\r\n  private buildLogData(message: string, context?: string | object): any {\r\n    const logData: any = {\r\n      message,\r\n      timestamp: new Date().toISOString(),\r\n    };\r\n\r\n    if (context) {\r\n      if (typeof context === 'string') {\r\n        logData.context = context;\r\n      } else {\r\n        Object.assign(logData, context);\r\n      }\r\n    }\r\n\r\n    // Add correlation ID if available in async context\r\n    const correlationId = this.getCorrelationId();\r\n    if (correlationId) {\r\n      logData.correlationId = correlationId;\r\n    }\r\n\r\n    return logData;\r\n  }\r\n\r\n  /**\r\n   * Get correlation ID from async context\r\n   * @returns Correlation ID or undefined\r\n   */\r\n  private getCorrelationId(): string | undefined {\r\n    return this.correlationIdService.getCorrelationId();\r\n  }\r\n}\r\n"], "version": 3}