31043e8155e8c570e68bbbe03ad766f5
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g;
Object.defineProperty(exports, "__esModule", { value: true });
exports.VulnerabilityScan = void 0;
const typeorm_1 = require("typeorm");
const vulnerability_assessment_entity_1 = require("./vulnerability-assessment.entity");
const asset_entity_1 = require("../../../asset-management/domain/entities/asset.entity");
/**
 * Vulnerability Scan entity
 * Represents a vulnerability scan operation and its results
 */
let VulnerabilityScan = class VulnerabilityScan {
    /**
     * Check if scan is running
     */
    get isRunning() {
        return this.status === 'running';
    }
    /**
     * Check if scan is completed
     */
    get isCompleted() {
        return this.status === 'completed';
    }
    /**
     * Check if scan failed
     */
    get isFailed() {
        return this.status === 'failed';
    }
    /**
     * Get scan duration in seconds
     */
    get durationSeconds() {
        if (!this.startedAt || !this.completedAt)
            return null;
        return Math.floor((this.completedAt.getTime() - this.startedAt.getTime()) / 1000);
    }
    /**
     * Get scan duration in human readable format
     */
    get durationFormatted() {
        const duration = this.durationSeconds;
        if (duration === null)
            return null;
        const hours = Math.floor(duration / 3600);
        const minutes = Math.floor((duration % 3600) / 60);
        const seconds = duration % 60;
        if (hours > 0) {
            return `${hours}h ${minutes}m ${seconds}s`;
        }
        else if (minutes > 0) {
            return `${minutes}m ${seconds}s`;
        }
        else {
            return `${seconds}s`;
        }
    }
    /**
     * Get total vulnerability count
     */
    get totalVulnerabilities() {
        return this.scanResults?.summary.totalVulnerabilities || 0;
    }
    /**
     * Get critical vulnerability count
     */
    get criticalVulnerabilities() {
        return this.scanResults?.summary.criticalCount || 0;
    }
    /**
     * Get high vulnerability count
     */
    get highVulnerabilities() {
        return this.scanResults?.summary.highCount || 0;
    }
    /**
     * Check if scan has critical vulnerabilities
     */
    get hasCriticalVulnerabilities() {
        return this.criticalVulnerabilities > 0;
    }
    /**
     * Get scan risk score
     */
    get riskScore() {
        if (!this.scanResults)
            return 0;
        const { criticalCount, highCount, mediumCount, lowCount } = this.scanResults.summary;
        return (criticalCount * 10) + (highCount * 7) + (mediumCount * 4) + (lowCount * 1);
    }
    /**
     * Start scan
     */
    start() {
        this.status = 'running';
        this.startedAt = new Date();
    }
    /**
     * Complete scan
     */
    complete(results) {
        this.status = 'completed';
        this.completedAt = new Date();
        this.scanResults = results;
    }
    /**
     * Fail scan
     */
    fail(error) {
        this.status = 'failed';
        this.completedAt = new Date();
        if (!this.errors) {
            this.errors = [];
        }
        this.errors.push({
            type: 'error',
            code: 'SCAN_FAILED',
            message: error,
            timestamp: new Date().toISOString(),
        });
    }
    /**
     * Cancel scan
     */
    cancel(reason) {
        this.status = 'cancelled';
        this.completedAt = new Date();
        if (reason) {
            if (!this.errors) {
                this.errors = [];
            }
            this.errors.push({
                type: 'info',
                code: 'SCAN_CANCELLED',
                message: reason,
                timestamp: new Date().toISOString(),
            });
        }
    }
    /**
     * Pause scan
     */
    pause(reason) {
        this.status = 'paused';
        if (reason) {
            if (!this.errors) {
                this.errors = [];
            }
            this.errors.push({
                type: 'info',
                code: 'SCAN_PAUSED',
                message: reason,
                timestamp: new Date().toISOString(),
            });
        }
    }
    /**
     * Resume scan
     */
    resume() {
        this.status = 'running';
    }
    /**
     * Update progress
     */
    updateProgress(progress) {
        this.progressInfo = progress;
    }
    /**
     * Add error
     */
    addError(type, code, message, target) {
        if (!this.errors) {
            this.errors = [];
        }
        this.errors.push({
            type,
            code,
            message,
            target,
            timestamp: new Date().toISOString(),
        });
    }
    /**
     * Add tag to scan
     */
    addTag(tag) {
        if (!this.tags.includes(tag)) {
            this.tags.push(tag);
        }
    }
    /**
     * Remove tag from scan
     */
    removeTag(tag) {
        this.tags = this.tags.filter(t => t !== tag);
    }
    /**
     * Get scan summary
     */
    getSummary() {
        return {
            id: this.id,
            name: this.name,
            scanType: this.scanType,
            scannerType: this.scannerType,
            status: this.status,
            isRunning: this.isRunning,
            isCompleted: this.isCompleted,
            isFailed: this.isFailed,
            hasCriticalVulnerabilities: this.hasCriticalVulnerabilities,
            totalVulnerabilities: this.totalVulnerabilities,
            criticalVulnerabilities: this.criticalVulnerabilities,
            highVulnerabilities: this.highVulnerabilities,
            riskScore: this.riskScore,
            durationSeconds: this.durationSeconds,
            durationFormatted: this.durationFormatted,
            startedAt: this.startedAt,
            completedAt: this.completedAt,
            scheduledAt: this.scheduledAt,
            tags: this.tags,
        };
    }
    /**
     * Export scan for reporting
     */
    exportForReporting() {
        return {
            scan: this.getSummary(),
            description: this.description,
            scanConfig: this.scanConfig,
            scanResults: this.scanResults,
            performanceMetrics: this.performanceMetrics,
            qualityAssurance: this.qualityAssurance,
            comparison: this.comparison,
            errors: this.errors,
            customAttributes: this.customAttributes,
            exportedAt: new Date().toISOString(),
        };
    }
};
exports.VulnerabilityScan = VulnerabilityScan;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], VulnerabilityScan.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], VulnerabilityScan.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], VulnerabilityScan.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'scan_type',
        type: 'enum',
        enum: ['network', 'web_application', 'database', 'infrastructure', 'container', 'cloud', 'mobile', 'api', 'compliance'],
    }),
    __metadata("design:type", String)
], VulnerabilityScan.prototype, "scanType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'scanner_type',
        type: 'enum',
        enum: ['nessus', 'openvas', 'qualys', 'rapid7', 'burp_suite', 'owasp_zap', 'nmap', 'custom', 'other'],
    }),
    __metadata("design:type", String)
], VulnerabilityScan.prototype, "scannerType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['pending', 'running', 'completed', 'failed', 'cancelled', 'paused'],
        default: 'pending',
    }),
    __metadata("design:type", String)
], VulnerabilityScan.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'scanner_version', nullable: true }),
    __metadata("design:type", String)
], VulnerabilityScan.prototype, "scannerVersion", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'scan_config', type: 'jsonb' }),
    __metadata("design:type", Object)
], VulnerabilityScan.prototype, "scanConfig", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'scan_results', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], VulnerabilityScan.prototype, "scanResults", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'performance_metrics', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], VulnerabilityScan.prototype, "performanceMetrics", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'progress_info', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], VulnerabilityScan.prototype, "progressInfo", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_a = typeof Array !== "undefined" && Array) === "function" ? _a : Object)
], VulnerabilityScan.prototype, "errors", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'quality_assurance', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], VulnerabilityScan.prototype, "qualityAssurance", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], VulnerabilityScan.prototype, "comparison", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, default: '{}' }),
    __metadata("design:type", Array)
], VulnerabilityScan.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'started_at', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], VulnerabilityScan.prototype, "startedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'completed_at', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], VulnerabilityScan.prototype, "completedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'scheduled_at', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], VulnerabilityScan.prototype, "scheduledAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'initiated_by', type: 'uuid' }),
    __metadata("design:type", String)
], VulnerabilityScan.prototype, "initiatedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'reviewed_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], VulnerabilityScan.prototype, "reviewedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'custom_attributes', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_e = typeof Record !== "undefined" && Record) === "function" ? _e : Object)
], VulnerabilityScan.prototype, "customAttributes", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_f = typeof Date !== "undefined" && Date) === "function" ? _f : Object)
], VulnerabilityScan.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_g = typeof Date !== "undefined" && Date) === "function" ? _g : Object)
], VulnerabilityScan.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => vulnerability_assessment_entity_1.VulnerabilityAssessment, assessment => assessment.scan),
    __metadata("design:type", Array)
], VulnerabilityScan.prototype, "assessments", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => asset_entity_1.Asset, asset => asset.vulnerabilityScans),
    (0, typeorm_1.JoinTable)({
        name: 'scan_assets',
        joinColumn: { name: 'scan_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'asset_id', referencedColumnName: 'id' },
    }),
    __metadata("design:type", Array)
], VulnerabilityScan.prototype, "scannedAssets", void 0);
exports.VulnerabilityScan = VulnerabilityScan = __decorate([
    (0, typeorm_1.Entity)('vulnerability_scans'),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['scanType']),
    (0, typeorm_1.Index)(['startedAt']),
    (0, typeorm_1.Index)(['completedAt']),
    (0, typeorm_1.Index)(['severity']),
    (0, typeorm_1.Index)(['scannerType'])
], VulnerabilityScan);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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