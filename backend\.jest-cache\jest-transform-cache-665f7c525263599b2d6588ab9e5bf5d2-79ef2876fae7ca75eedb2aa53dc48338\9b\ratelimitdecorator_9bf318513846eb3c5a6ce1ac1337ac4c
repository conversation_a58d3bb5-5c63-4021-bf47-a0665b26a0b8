5b1261060e8395ca6558d5886d4105bb
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenBucketRateLimit = exports.SlidingWindowRateLimit = exports.BurstRateLimit = exports.AdvancedRateLimit = exports.ApiKeyRateLimit = exports.UserRateLimit = exports.IpRateLimit = exports.RoleBasedRateLimit = exports.RealtimeRateLimit = exports.AnalyticsRateLimit = exports.SearchRateLimit = exports.ExportRateLimit = exports.FileUploadRateLimit = exports.PasswordResetRateLimit = exports.AuthRateLimit = exports.LenientRateLimit = exports.ModerateRateLimit = exports.StrictRateLimit = exports.VeryStrictRateLimit = exports.RateLimitPresets = exports.RateLimit = exports.RATE_LIMIT_KEY = void 0;
const common_1 = require("@nestjs/common");
/**
 * Rate limit metadata key
 */
exports.RATE_LIMIT_KEY = 'rate_limit';
/**
 * Rate Limit Decorator
 * Applies rate limiting to controller methods or entire controllers
 */
const RateLimit = (config) => (0, common_1.SetMetadata)(exports.RATE_LIMIT_KEY, config);
exports.RateLimit = RateLimit;
/**
 * Predefined rate limit configurations
 */
exports.RateLimitPresets = {
    /**
     * Very strict rate limiting - 10 requests per minute
     */
    VERY_STRICT: {
        windowMs: 60 * 1000, // 1 minute
        max: 10,
        message: 'Too many requests, please try again later.',
        headers: true,
        standardHeaders: true,
    },
    /**
     * Strict rate limiting - 30 requests per minute
     */
    STRICT: {
        windowMs: 60 * 1000, // 1 minute
        max: 30,
        message: 'Too many requests, please try again later.',
        headers: true,
        standardHeaders: true,
    },
    /**
     * Moderate rate limiting - 100 requests per minute
     */
    MODERATE: {
        windowMs: 60 * 1000, // 1 minute
        max: 100,
        message: 'Too many requests, please try again later.',
        headers: true,
        standardHeaders: true,
    },
    /**
     * Lenient rate limiting - 300 requests per minute
     */
    LENIENT: {
        windowMs: 60 * 1000, // 1 minute
        max: 300,
        message: 'Too many requests, please try again later.',
        headers: true,
        standardHeaders: true,
    },
    /**
     * Authentication endpoints - 5 attempts per 15 minutes
     */
    AUTH: {
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 5,
        message: 'Too many authentication attempts, please try again later.',
        headers: true,
        standardHeaders: true,
        skipSuccessfulRequests: true,
    },
    /**
     * Password reset - 3 attempts per hour
     */
    PASSWORD_RESET: {
        windowMs: 60 * 60 * 1000, // 1 hour
        max: 3,
        message: 'Too many password reset attempts, please try again later.',
        headers: true,
        standardHeaders: true,
    },
    /**
     * File upload - 10 uploads per hour
     */
    FILE_UPLOAD: {
        windowMs: 60 * 60 * 1000, // 1 hour
        max: 10,
        message: 'Too many file uploads, please try again later.',
        headers: true,
        standardHeaders: true,
    },
    /**
     * Export operations - 5 exports per hour
     */
    EXPORT: {
        windowMs: 60 * 60 * 1000, // 1 hour
        max: 5,
        message: 'Too many export requests, please try again later.',
        headers: true,
        standardHeaders: true,
    },
    /**
     * Search operations - 200 searches per minute
     */
    SEARCH: {
        windowMs: 60 * 1000, // 1 minute
        max: 200,
        message: 'Too many search requests, please try again later.',
        headers: true,
        standardHeaders: true,
    },
    /**
     * Analytics operations - 50 requests per minute
     */
    ANALYTICS: {
        windowMs: 60 * 1000, // 1 minute
        max: 50,
        message: 'Too many analytics requests, please try again later.',
        headers: true,
        standardHeaders: true,
    },
    /**
     * Real-time operations - 1000 requests per minute
     */
    REALTIME: {
        windowMs: 60 * 1000, // 1 minute
        max: 1000,
        message: 'Too many real-time requests, please try again later.',
        headers: true,
        standardHeaders: true,
    },
};
/**
 * Convenience decorators for common rate limiting scenarios
 */
const VeryStrictRateLimit = () => (0, exports.RateLimit)(exports.RateLimitPresets.VERY_STRICT);
exports.VeryStrictRateLimit = VeryStrictRateLimit;
const StrictRateLimit = () => (0, exports.RateLimit)(exports.RateLimitPresets.STRICT);
exports.StrictRateLimit = StrictRateLimit;
const ModerateRateLimit = () => (0, exports.RateLimit)(exports.RateLimitPresets.MODERATE);
exports.ModerateRateLimit = ModerateRateLimit;
const LenientRateLimit = () => (0, exports.RateLimit)(exports.RateLimitPresets.LENIENT);
exports.LenientRateLimit = LenientRateLimit;
const AuthRateLimit = () => (0, exports.RateLimit)(exports.RateLimitPresets.AUTH);
exports.AuthRateLimit = AuthRateLimit;
const PasswordResetRateLimit = () => (0, exports.RateLimit)(exports.RateLimitPresets.PASSWORD_RESET);
exports.PasswordResetRateLimit = PasswordResetRateLimit;
const FileUploadRateLimit = () => (0, exports.RateLimit)(exports.RateLimitPresets.FILE_UPLOAD);
exports.FileUploadRateLimit = FileUploadRateLimit;
const ExportRateLimit = () => (0, exports.RateLimit)(exports.RateLimitPresets.EXPORT);
exports.ExportRateLimit = ExportRateLimit;
const SearchRateLimit = () => (0, exports.RateLimit)(exports.RateLimitPresets.SEARCH);
exports.SearchRateLimit = SearchRateLimit;
const AnalyticsRateLimit = () => (0, exports.RateLimit)(exports.RateLimitPresets.ANALYTICS);
exports.AnalyticsRateLimit = AnalyticsRateLimit;
const RealtimeRateLimit = () => (0, exports.RateLimit)(exports.RateLimitPresets.REALTIME);
exports.RealtimeRateLimit = RealtimeRateLimit;
/**
 * Dynamic rate limit decorator based on user role
 */
const RoleBasedRateLimit = (roleConfigs) => (0, common_1.SetMetadata)(exports.RATE_LIMIT_KEY, { type: 'role-based', configs: roleConfigs });
exports.RoleBasedRateLimit = RoleBasedRateLimit;
/**
 * IP-based rate limiting
 */
const IpRateLimit = (config) => (0, common_1.SetMetadata)(exports.RATE_LIMIT_KEY, { ...config, keyGenerator: 'ip' });
exports.IpRateLimit = IpRateLimit;
/**
 * User-based rate limiting
 */
const UserRateLimit = (config) => (0, common_1.SetMetadata)(exports.RATE_LIMIT_KEY, { ...config, keyGenerator: 'user' });
exports.UserRateLimit = UserRateLimit;
/**
 * API key-based rate limiting
 */
const ApiKeyRateLimit = (config) => (0, common_1.SetMetadata)(exports.RATE_LIMIT_KEY, { ...config, keyGenerator: 'apiKey' });
exports.ApiKeyRateLimit = ApiKeyRateLimit;
/**
 * Custom rate limit decorator with advanced options
 */
const AdvancedRateLimit = (options) => (0, common_1.SetMetadata)(exports.RATE_LIMIT_KEY, options);
exports.AdvancedRateLimit = AdvancedRateLimit;
/**
 * Burst rate limiting - allows short bursts but limits sustained usage
 */
const BurstRateLimit = (options) => (0, common_1.SetMetadata)(exports.RATE_LIMIT_KEY, { type: 'burst', ...options });
exports.BurstRateLimit = BurstRateLimit;
/**
 * Sliding window rate limiting
 */
const SlidingWindowRateLimit = (options) => (0, common_1.SetMetadata)(exports.RATE_LIMIT_KEY, { type: 'sliding-window', ...options });
exports.SlidingWindowRateLimit = SlidingWindowRateLimit;
/**
 * Token bucket rate limiting
 */
const TokenBucketRateLimit = (options) => (0, common_1.SetMetadata)(exports.RATE_LIMIT_KEY, { type: 'token-bucket', ...options });
exports.TokenBucketRateLimit = TokenBucketRateLimit;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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