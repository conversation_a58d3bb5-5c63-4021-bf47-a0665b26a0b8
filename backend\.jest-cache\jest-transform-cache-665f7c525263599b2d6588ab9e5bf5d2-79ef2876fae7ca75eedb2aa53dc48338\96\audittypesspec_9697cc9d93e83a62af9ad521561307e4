4574a3776f67c5f08725636fb42fffaa
"use strict";
/**
 * Audit Types Tests
 */
Object.defineProperty(exports, "__esModule", { value: true });
const audit_types_1 = require("../../types/audit.types");
describe('AuditUtils', () => {
    describe('createEntry', () => {
        it('should create a basic audit entry', () => {
            const entry = audit_types_1.AuditUtils.createEntry(audit_types_1.AuditAction.CREATE, audit_types_1.AuditResult.SUCCESS, 'User created successfully');
            expect(entry.id).toBeDefined();
            expect(entry.timestamp).toBeInstanceOf(Date);
            expect(entry.action).toBe(audit_types_1.AuditAction.CREATE);
            expect(entry.result).toBe(audit_types_1.AuditResult.SUCCESS);
            expect(entry.description).toBe('User created successfully');
            expect(entry.system.application).toBe('sentinel');
        });
        it('should create an audit entry with custom options', () => {
            const entry = audit_types_1.AuditUtils.createEntry(audit_types_1.AuditAction.DELETE, audit_types_1.AuditResult.FAILURE, 'Failed to delete user', {
                severity: audit_types_1.AuditSeverity.CRITICAL,
                category: audit_types_1.AuditCategory.DATA_MODIFICATION,
                user: {
                    userId: 'user123',
                    username: 'testuser',
                },
                resource: {
                    resourceType: 'User',
                    resourceId: '456',
                },
            });
            expect(entry.severity).toBe(audit_types_1.AuditSeverity.CRITICAL);
            expect(entry.category).toBe(audit_types_1.AuditCategory.DATA_MODIFICATION);
            expect(entry.user?.userId).toBe('user123');
            expect(entry.resource?.resourceType).toBe('User');
        });
    });
    describe('inferSeverity', () => {
        it('should infer high severity for critical actions', () => {
            const severity = audit_types_1.AuditUtils.inferSeverity(audit_types_1.AuditAction.DELETE, audit_types_1.AuditResult.SUCCESS);
            expect(severity).toBe(audit_types_1.AuditSeverity.HIGH);
        });
        it('should infer critical severity for failed critical actions', () => {
            const severity = audit_types_1.AuditUtils.inferSeverity(audit_types_1.AuditAction.PURGE, audit_types_1.AuditResult.FAILURE);
            expect(severity).toBe(audit_types_1.AuditSeverity.CRITICAL);
        });
        it('should infer medium severity for moderate actions', () => {
            const severity = audit_types_1.AuditUtils.inferSeverity(audit_types_1.AuditAction.UPDATE, audit_types_1.AuditResult.SUCCESS);
            expect(severity).toBe(audit_types_1.AuditSeverity.MEDIUM);
        });
        it('should infer low severity for read actions', () => {
            const severity = audit_types_1.AuditUtils.inferSeverity(audit_types_1.AuditAction.READ, audit_types_1.AuditResult.SUCCESS);
            expect(severity).toBe(audit_types_1.AuditSeverity.LOW);
        });
    });
    describe('inferCategory', () => {
        it('should infer authentication category for login/logout', () => {
            expect(audit_types_1.AuditUtils.inferCategory(audit_types_1.AuditAction.LOGIN)).toBe(audit_types_1.AuditCategory.AUTHENTICATION);
            expect(audit_types_1.AuditUtils.inferCategory(audit_types_1.AuditAction.LOGOUT)).toBe(audit_types_1.AuditCategory.AUTHENTICATION);
        });
        it('should infer authorization category for access', () => {
            expect(audit_types_1.AuditUtils.inferCategory(audit_types_1.AuditAction.ACCESS)).toBe(audit_types_1.AuditCategory.AUTHORIZATION);
        });
        it('should infer data access category for read/export', () => {
            expect(audit_types_1.AuditUtils.inferCategory(audit_types_1.AuditAction.READ)).toBe(audit_types_1.AuditCategory.DATA_ACCESS);
            expect(audit_types_1.AuditUtils.inferCategory(audit_types_1.AuditAction.EXPORT)).toBe(audit_types_1.AuditCategory.DATA_ACCESS);
        });
        it('should infer data modification category for CRUD operations', () => {
            expect(audit_types_1.AuditUtils.inferCategory(audit_types_1.AuditAction.CREATE)).toBe(audit_types_1.AuditCategory.DATA_MODIFICATION);
            expect(audit_types_1.AuditUtils.inferCategory(audit_types_1.AuditAction.UPDATE)).toBe(audit_types_1.AuditCategory.DATA_MODIFICATION);
            expect(audit_types_1.AuditUtils.inferCategory(audit_types_1.AuditAction.DELETE)).toBe(audit_types_1.AuditCategory.DATA_MODIFICATION);
        });
        it('should infer system configuration category for config actions', () => {
            expect(audit_types_1.AuditUtils.inferCategory(audit_types_1.AuditAction.CONFIGURE)).toBe(audit_types_1.AuditCategory.SYSTEM_CONFIGURATION);
            expect(audit_types_1.AuditUtils.inferCategory(audit_types_1.AuditAction.BACKUP)).toBe(audit_types_1.AuditCategory.SYSTEM_CONFIGURATION);
        });
    });
    describe('calculateRiskScore', () => {
        it('should calculate risk score based on severity', () => {
            const entry = {
                severity: audit_types_1.AuditSeverity.CRITICAL,
                action: audit_types_1.AuditAction.READ,
                result: audit_types_1.AuditResult.SUCCESS,
            };
            const score = audit_types_1.AuditUtils.calculateRiskScore(entry);
            expect(score).toBeGreaterThan(40); // Base critical severity score
        });
        it('should increase risk score for high-risk actions', () => {
            const entry = {
                severity: audit_types_1.AuditSeverity.MEDIUM,
                action: audit_types_1.AuditAction.DELETE,
                result: audit_types_1.AuditResult.SUCCESS,
            };
            const score = audit_types_1.AuditUtils.calculateRiskScore(entry);
            expect(score).toBeGreaterThan(20); // Base medium + high-risk action
        });
        it('should increase risk score for failures', () => {
            const entry = {
                severity: audit_types_1.AuditSeverity.LOW,
                action: audit_types_1.AuditAction.READ,
                result: audit_types_1.AuditResult.FAILURE,
            };
            const score = audit_types_1.AuditUtils.calculateRiskScore(entry);
            expect(score).toBeGreaterThan(10); // Base low + failure penalty
        });
        it('should increase risk score for admin users', () => {
            const entry = {
                severity: audit_types_1.AuditSeverity.LOW,
                action: audit_types_1.AuditAction.READ,
                result: audit_types_1.AuditResult.SUCCESS,
                user: {
                    userId: 'admin',
                    roles: ['admin'],
                },
            };
            const score = audit_types_1.AuditUtils.calculateRiskScore(entry);
            expect(score).toBeGreaterThan(10); // Base low + admin penalty
        });
        it('should increase risk score for off-hours access', () => {
            const offHoursDate = new Date();
            offHoursDate.setHours(2); // 2 AM
            const entry = {
                severity: audit_types_1.AuditSeverity.LOW,
                action: audit_types_1.AuditAction.READ,
                result: audit_types_1.AuditResult.SUCCESS,
                timestamp: offHoursDate,
            };
            const score = audit_types_1.AuditUtils.calculateRiskScore(entry);
            expect(score).toBeGreaterThan(10); // Base low + off-hours penalty
        });
        it('should cap risk score at 100', () => {
            const entry = {
                severity: audit_types_1.AuditSeverity.CRITICAL,
                action: audit_types_1.AuditAction.DELETE,
                result: audit_types_1.AuditResult.FAILURE,
                user: {
                    userId: 'admin',
                    roles: ['admin'],
                    ipAddress: '***********',
                    location: { country: 'US' },
                },
                timestamp: new Date(new Date().setHours(2)),
            };
            const score = audit_types_1.AuditUtils.calculateRiskScore(entry);
            expect(score).toBeLessThanOrEqual(100);
        });
    });
    describe('sanitizeEntry', () => {
        it('should sanitize sensitive data when includeSensitiveData is false', () => {
            const entry = {
                id: 'audit-1',
                timestamp: new Date(),
                action: audit_types_1.AuditAction.LOGIN,
                result: audit_types_1.AuditResult.SUCCESS,
                severity: audit_types_1.AuditSeverity.LOW,
                category: audit_types_1.AuditCategory.AUTHENTICATION,
                description: 'User login',
                system: { application: 'test' },
                user: {
                    userId: 'user123',
                    email: '<EMAIL>',
                    ipAddress: '*************',
                },
                changes: {
                    before: { password: 'oldpass' },
                    after: { password: 'newpass' },
                },
                metadata: {
                    token: 'secret-token',
                    publicInfo: 'safe-data',
                },
            };
            const config = {
                ...audit_types_1.AuditUtils.DEFAULT_CONFIG,
                includeSensitiveData: false,
                includeDataChanges: true,
            };
            const sanitized = audit_types_1.AuditUtils.sanitizeEntry(entry, config);
            expect(sanitized.user?.email).toMatch(/us\*\*\*@example\.com/);
            expect(sanitized.user?.ipAddress).toMatch(/192\.168\.\*\*\*\.\*\*\*/);
            expect(sanitized.changes?.before?.password).toBe('***REDACTED***');
            expect(sanitized.changes?.after?.password).toBe('***REDACTED***');
            expect(sanitized.metadata?.token).toBe('***REDACTED***');
            expect(sanitized.metadata?.publicInfo).toBe('safe-data');
        });
        it('should remove data changes when includeDataChanges is false', () => {
            const entry = {
                id: 'audit-1',
                timestamp: new Date(),
                action: audit_types_1.AuditAction.UPDATE,
                result: audit_types_1.AuditResult.SUCCESS,
                severity: audit_types_1.AuditSeverity.MEDIUM,
                category: audit_types_1.AuditCategory.DATA_MODIFICATION,
                description: 'User updated',
                system: { application: 'test' },
                changes: {
                    before: { name: 'Old Name' },
                    after: { name: 'New Name' },
                },
            };
            const config = {
                ...audit_types_1.AuditUtils.DEFAULT_CONFIG,
                includeDataChanges: false,
            };
            const sanitized = audit_types_1.AuditUtils.sanitizeEntry(entry, config);
            expect(sanitized.changes).toBeUndefined();
        });
    });
    describe('shouldAudit', () => {
        it('should return false when auditing is disabled', () => {
            const entry = {
                action: audit_types_1.AuditAction.READ,
            };
            const config = {
                ...audit_types_1.AuditUtils.DEFAULT_CONFIG,
                enabled: false,
            };
            const result = audit_types_1.AuditUtils.shouldAudit(entry, config);
            expect(result).toBe(false);
        });
        it('should return false for non-audited actions', () => {
            const entry = {
                action: audit_types_1.AuditAction.READ,
            };
            const config = {
                ...audit_types_1.AuditUtils.DEFAULT_CONFIG,
                auditedActions: [audit_types_1.AuditAction.CREATE, audit_types_1.AuditAction.UPDATE],
            };
            const result = audit_types_1.AuditUtils.shouldAudit(entry, config);
            expect(result).toBe(false);
        });
        it('should return false for non-audited categories', () => {
            const entry = {
                action: audit_types_1.AuditAction.READ,
                category: audit_types_1.AuditCategory.DATA_ACCESS,
            };
            const config = {
                ...audit_types_1.AuditUtils.DEFAULT_CONFIG,
                auditedCategories: [audit_types_1.AuditCategory.AUTHENTICATION],
            };
            const result = audit_types_1.AuditUtils.shouldAudit(entry, config);
            expect(result).toBe(false);
        });
        it('should return false for severity below minimum', () => {
            const entry = {
                action: audit_types_1.AuditAction.READ,
                severity: audit_types_1.AuditSeverity.LOW,
            };
            const config = {
                ...audit_types_1.AuditUtils.DEFAULT_CONFIG,
                minimumSeverity: audit_types_1.AuditSeverity.MEDIUM,
            };
            const result = audit_types_1.AuditUtils.shouldAudit(entry, config);
            expect(result).toBe(false);
        });
        it('should return true for valid audit entry', () => {
            const entry = {
                action: audit_types_1.AuditAction.CREATE,
                category: audit_types_1.AuditCategory.DATA_MODIFICATION,
                severity: audit_types_1.AuditSeverity.MEDIUM,
            };
            const result = audit_types_1.AuditUtils.shouldAudit(entry, audit_types_1.AuditUtils.DEFAULT_CONFIG);
            expect(result).toBe(true);
        });
    });
    describe('formatEntry', () => {
        it('should format audit entry for display', () => {
            const entry = {
                id: 'audit-1',
                timestamp: new Date('2023-01-01T12:00:00Z'),
                action: audit_types_1.AuditAction.CREATE,
                result: audit_types_1.AuditResult.SUCCESS,
                severity: audit_types_1.AuditSeverity.MEDIUM,
                category: audit_types_1.AuditCategory.DATA_MODIFICATION,
                description: 'User created',
                system: { application: 'test' },
                user: {
                    userId: 'user123',
                    username: 'testuser',
                },
                resource: {
                    resourceType: 'User',
                    resourceId: '456',
                },
            };
            const formatted = audit_types_1.AuditUtils.formatEntry(entry);
            expect(formatted).toContain('2023-01-01T12:00:00.000Z');
            expect(formatted).toContain('testuser');
            expect(formatted).toContain('create');
            expect(formatted).toContain('User:456');
            expect(formatted).toContain('success');
            expect(formatted).toContain('medium');
        });
        it('should handle missing user information', () => {
            const entry = {
                id: 'audit-1',
                timestamp: new Date('2023-01-01T12:00:00Z'),
                action: audit_types_1.AuditAction.CREATE,
                result: audit_types_1.AuditResult.SUCCESS,
                severity: audit_types_1.AuditSeverity.MEDIUM,
                category: audit_types_1.AuditCategory.DATA_MODIFICATION,
                description: 'System action',
                system: { application: 'test' },
            };
            const formatted = audit_types_1.AuditUtils.formatEntry(entry);
            expect(formatted).toContain('system');
            expect(formatted).toContain('N/A');
        });
    });
    describe('createQuery', () => {
        it('should create query from parameters', () => {
            const params = {
                startDate: new Date('2023-01-01'),
                endDate: new Date('2023-12-31'),
                userId: 'user123',
                actions: [audit_types_1.AuditAction.CREATE, audit_types_1.AuditAction.UPDATE],
                results: [audit_types_1.AuditResult.SUCCESS],
                severities: [audit_types_1.AuditSeverity.HIGH],
                categories: [audit_types_1.AuditCategory.DATA_MODIFICATION],
                resourceTypes: ['User'],
                resourceIds: ['456'],
                search: 'test search',
                minRiskScore: 50,
                maxRiskScore: 90,
                complianceTags: ['SOX', 'GDPR'],
                tenantId: 'tenant123',
                applications: ['app1'],
                environments: ['prod'],
            };
            const query = audit_types_1.AuditUtils.createQuery(params);
            expect(query.timestamp.$gte).toEqual(params.startDate);
            expect(query.timestamp.$lte).toEqual(params.endDate);
            expect(query['user.userId']).toBe(params.userId);
            expect(query.action.$in).toEqual(params.actions);
            expect(query.result.$in).toEqual(params.results);
            expect(query.severity.$in).toEqual(params.severities);
            expect(query.category.$in).toEqual(params.categories);
            expect(query['resource.resourceType'].$in).toEqual(params.resourceTypes);
            expect(query['resource.resourceId'].$in).toEqual(params.resourceIds);
            expect(query.$or).toBeDefined();
            expect(query.riskScore.$gte).toBe(params.minRiskScore);
            expect(query.riskScore.$lte).toBe(params.maxRiskScore);
            expect(query.complianceTags.$in).toEqual(params.complianceTags);
            expect(query['system.tenantId']).toBe(params.tenantId);
            expect(query['system.application'].$in).toEqual(params.applications);
            expect(query['system.environment'].$in).toEqual(params.environments);
        });
        it('should create empty query for empty parameters', () => {
            const params = {};
            const query = audit_types_1.AuditUtils.createQuery(params);
            expect(Object.keys(query)).toHaveLength(0);
        });
        it('should handle single date parameter', () => {
            const params = {
                startDate: new Date('2023-01-01'),
            };
            const query = audit_types_1.AuditUtils.createQuery(params);
            expect(query.timestamp.$gte).toEqual(params.startDate);
            expect(query.timestamp.$lte).toBeUndefined();
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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