{"name": "sentinel-backend", "version": "1.0.0", "description": "Sentinel vulnerability assessment platform backend - Part 1 foundational infrastructure", "author": "Sentinel Development Team", "private": true, "license": "MIT", "main": "dist/main.js", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typecheck": "tsc --noEmit", "prebuild": "<PERSON><PERSON><PERSON> dist", "postbuild": "cp -r src/assets dist/ || true", "migration:generate": "typeorm migration:generate", "migration:run": "typeorm migration:run", "migration:revert": "typeorm migration:revert", "seed": "ts-node -r tsconfig-paths/register src/infrastructure/database/seeds/run-seeds.ts", "seed:dev": "ts-node -r tsconfig-paths/register src/infrastructure/database/seeds/run-seeds.ts development", "seed:test": "ts-node -r tsconfig-paths/register src/infrastructure/database/seeds/run-seeds.ts test", "seed:prod": "ts-node -r tsconfig-paths/register src/infrastructure/database/seeds/run-seeds.ts production", "seed:force": "ts-node -r tsconfig-paths/register src/infrastructure/database/seeds/run-seeds.ts --force", "seed:status": "ts-node -r tsconfig-paths/register src/infrastructure/database/seeds/run-seeds.ts --status", "seed:cleanup": "ts-node -r tsconfig-paths/register src/infrastructure/database/seeds/run-seeds.ts --cleanup", "docker:build": "docker build -t sentinel-backend .", "docker:build:dev": "docker build -f Dockerfile.dev -t sentinel-backend:dev .", "docker:build:prod": "docker build -f Dockerfile.prod -t sentinel-backend:prod .", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "health": "curl -f http://localhost:3000/health || exit 1", "clean": "rimraf dist node_modules coverage .nyc_output"}, "dependencies": {"@azure/keyvault-secrets": "^4.7.0", "@nestjs/cache-manager": "^2.0.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^2.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.0.0", "@nestjs/swagger": "^7.0.0", "@nestjs/terminus": "^10.0.0", "@nestjs/throttler": "^5.0.0", "@nestjs/typeorm": "^10.0.0", "@nestjs/websockets": "^10.4.19", "@types/chokidar": "^2.1.7", "@types/cookie-parser": "^1.4.9", "aws-sdk": "^2.1400.0", "axios": "^1.4.0", "bcrypt": "^5.1.0", "bull": "^4.16.5", "cache-manager": "^5.2.0", "cache-manager-redis-store": "^3.0.0", "chokidar": "^3.6.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.7.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.0", "express-rate-limit": "^6.8.0", "helmet": "^7.0.0", "joi": "^17.9.0", "jsonwebtoken": "^9.0.0", "lodash": "^4.17.21", "moment": "^2.29.4", "node-vault": "^0.10.2", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "passport-oauth2": "^1.7.0", "pg": "^8.11.0", "redis": "^4.6.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "swagger-ui-express": "^5.0.0", "typeorm": "^0.3.17", "uuid": "^9.0.0", "winston": "^3.10.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^5.0.0", "@types/compression": "^1.7.2", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/jsonwebtoken": "^9.0.2", "@types/lodash": "^4.14.195", "@types/node": "^20.3.1", "@types/passport-jwt": "^3.0.8", "@types/passport-local": "^1.0.35", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^8.0.3", "jest": "^29.5.0", "lint-staged": "^13.2.3", "prettier": "^3.0.0", "rimraf": "^5.0.1", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3", "typescript-eslint": "^8.37.0"}, "lint-staged": {"*.{ts,js}": ["eslint --fix", "prettier --write"]}, "keywords": ["<PERSON><PERSON><PERSON>", "typescript", "vulnerability-assessment", "security", "backend", "api", "sentinel"]}