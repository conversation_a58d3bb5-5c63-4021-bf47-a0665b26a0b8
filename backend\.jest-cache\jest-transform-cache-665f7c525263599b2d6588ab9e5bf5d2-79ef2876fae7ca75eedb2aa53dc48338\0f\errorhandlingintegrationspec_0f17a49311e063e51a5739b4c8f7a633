1bff537936e512e5b7947af9ee03e369
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const security_orchestrator_service_1 = require("../../application/services/security-orchestrator.service");
const event_factory_1 = require("../../domain/factories/event.factory");
const threat_factory_1 = require("../../domain/factories/threat.factory");
const vulnerability_factory_1 = require("../../domain/factories/vulnerability.factory");
const event_repository_1 = require("../../domain/repositories/event.repository");
const threat_repository_1 = require("../../domain/repositories/threat.repository");
const vulnerability_repository_1 = require("../../domain/repositories/vulnerability.repository");
const response_action_repository_1 = require("../../domain/repositories/response-action.repository");
const event_processor_interface_1 = require("../../domain/interfaces/services/event-processor.interface");
const threat_detector_interface_1 = require("../../domain/interfaces/services/threat-detector.interface");
const vulnerability_scanner_interface_1 = require("../../domain/interfaces/services/vulnerability-scanner.interface");
const response_executor_interface_1 = require("../../domain/interfaces/services/response-executor.interface");
const event_metadata_value_object_1 = require("../../domain/value-objects/event-metadata/event-metadata.value-object");
const event_timestamp_value_object_1 = require("../../domain/value-objects/event-metadata/event-timestamp.value-object");
const event_source_value_object_1 = require("../../domain/value-objects/event-metadata/event-source.value-object");
const ip_address_value_object_1 = require("../../domain/value-objects/network/ip-address.value-object");
const port_value_object_1 = require("../../domain/value-objects/network/port.value-object");
const cvss_score_value_object_1 = require("../../domain/value-objects/threat-indicators/cvss-score.value-object");
const event_type_enum_1 = require("../../domain/enums/event-type.enum");
const event_severity_enum_1 = require("../../domain/enums/event-severity.enum");
const event_status_enum_1 = require("../../domain/enums/event-status.enum");
const event_source_type_enum_1 = require("../../domain/enums/event-source-type.enum");
const threat_severity_enum_1 = require("../../domain/enums/threat-severity.enum");
const vulnerability_severity_enum_1 = require("../../domain/enums/vulnerability-severity.enum");
const unique_entity_id_value_object_1 = require("../../../../shared-kernel/value-objects/unique-entity-id.value-object");
const tenant_id_value_object_1 = require("../../../../shared-kernel/value-objects/tenant-id.value-object");
const user_id_value_object_1 = require("../../../../shared-kernel/value-objects/user-id.value-object");
const validation_exception_1 = require("../../../../shared-kernel/exceptions/validation.exception");
const not_found_exception_1 = require("../../../../shared-kernel/exceptions/not-found.exception");
const unauthorized_exception_1 = require("../../../../shared-kernel/exceptions/unauthorized.exception");
const forbidden_exception_1 = require("../../../../shared-kernel/exceptions/forbidden.exception");
const conflict_exception_1 = require("../../../../shared-kernel/exceptions/conflict.exception");
const rate_limit_exception_1 = require("../../../../shared-kernel/exceptions/rate-limit.exception");
const service_unavailable_exception_1 = require("../../../../shared-kernel/exceptions/service-unavailable.exception");
/**
 * Error-prone mock implementations for testing error handling
 */
class ErrorProneEventRepository {
    constructor() {
        this.events = new Map();
        this.errorScenario = null;
        this.errorCount = 0;
        this.maxErrors = 1;
    }
    setErrorScenario(scenario, maxErrors = 1) {
        this.errorScenario = scenario;
        this.errorCount = 0;
        this.maxErrors = maxErrors;
    }
    clearErrorScenario() {
        this.errorScenario = null;
        this.errorCount = 0;
    }
    throwErrorIfConfigured(operation) {
        if (this.errorScenario === operation && this.errorCount < this.maxErrors) {
            this.errorCount++;
            switch (operation) {
                case 'save':
                    throw new conflict_exception_1.ConflictException('Event already exists');
                case 'findById':
                    throw new not_found_exception_1.NotFoundException('Event not found');
                case 'delete':
                    throw new forbidden_exception_1.ForbiddenException('Insufficient permissions to delete event');
                case 'findAll':
                    throw new service_unavailable_exception_1.ServiceUnavailableException('Database connection failed');
                case 'timeout':
                    throw new Error('Operation timed out');
                default:
                    throw new Error(`Simulated error in ${operation}`);
            }
        }
    }
    async save(event) {
        this.throwErrorIfConfigured('save');
        this.events.set(event.id.toString(), event);
    }
    async findById(id) {
        this.throwErrorIfConfigured('findById');
        return this.events.get(id.toString()) || null;
    }
    async findAll() {
        this.throwErrorIfConfigured('findAll');
        return Array.from(this.events.values());
    }
    async delete(id) {
        this.throwErrorIfConfigured('delete');
        this.events.delete(id.toString());
    }
    async findByTimeRange(start, end) {
        this.throwErrorIfConfigured('findByTimeRange');
        return Array.from(this.events.values()).filter(event => {
            const eventTime = event.timestamp.toDate();
            return eventTime >= start && eventTime <= end;
        });
    }
    async findBySource(source) {
        this.throwErrorIfConfigured('findBySource');
        return Array.from(this.events.values()).filter(event => event.source.equals(source));
    }
    async findBySeverity(severity) {
        this.throwErrorIfConfigured('findBySeverity');
        return Array.from(this.events.values()).filter(event => event.severity === severity);
    }
    async findEventsForCorrelation(timeWindowMs, eventTypes, minSeverity) {
        this.throwErrorIfConfigured('findEventsForCorrelation');
        const cutoffTime = new Date(Date.now() - timeWindowMs);
        return Array.from(this.events.values()).filter(event => {
            const eventTime = event.timestamp.toDate();
            return eventTime >= cutoffTime;
        });
    }
    clear() {
        this.events.clear();
    }
}
class ErrorProneEventProcessor {
    constructor() {
        this.errorScenario = null;
        this.errorCount = 0;
        this.maxErrors = 1;
    }
    setErrorScenario(scenario, maxErrors = 1) {
        this.errorScenario = scenario;
        this.errorCount = 0;
        this.maxErrors = maxErrors;
    }
    clearErrorScenario() {
        this.errorScenario = null;
        this.errorCount = 0;
    }
    throwErrorIfConfigured(operation) {
        if (this.errorScenario === operation && this.errorCount < this.maxErrors) {
            this.errorCount++;
            switch (operation) {
                case 'processEvent':
                    throw new validation_exception_1.ValidationException('Invalid event data', 'payload');
                case 'timeout':
                    throw new Error('Processing timeout');
                case 'rateLimit':
                    throw new rate_limit_exception_1.RateLimitException('Processing rate limit exceeded');
                case 'unauthorized':
                    throw new unauthorized_exception_1.UnauthorizedException('Insufficient permissions for event processing');
                default:
                    throw new service_unavailable_exception_1.ServiceUnavailableException('Event processing service unavailable');
            }
        }
    }
    async processEvent(event, context) {
        this.throwErrorIfConfigured('processEvent');
        return {
            success: true,
            eventId: event.id.toString(),
            processingSteps: ['normalize', 'enrich', 'correlate'],
            duration: 100,
            normalizedEvent: null,
            enrichedEvent: null,
            correlatedEvents: [],
            errors: [],
        };
    }
    async processEvents(events, context) {
        this.throwErrorIfConfigured('processEvents');
        const results = await Promise.all(events.map(event => this.processEvent(event, context)));
        return {
            success: true,
            totalEvents: events.length,
            processedEvents: results.length,
            results,
        };
    }
}
class ErrorProneThreatDetector {
    constructor() {
        this.errorScenario = null;
        this.errorCount = 0;
        this.maxErrors = 1;
    }
    setErrorScenario(scenario, maxErrors = 1) {
        this.errorScenario = scenario;
        this.errorCount = 0;
        this.maxErrors = maxErrors;
    }
    clearErrorScenario() {
        this.errorScenario = null;
        this.errorCount = 0;
    }
    throwErrorIfConfigured(operation) {
        if (this.errorScenario === operation && this.errorCount < this.maxErrors) {
            this.errorCount++;
            switch (operation) {
                case 'analyzeEvent':
                    throw new service_unavailable_exception_1.ServiceUnavailableException('Threat intelligence service unavailable');
                case 'timeout':
                    throw new Error('Threat analysis timeout');
                case 'rateLimit':
                    throw new rate_limit_exception_1.RateLimitException('Threat analysis rate limit exceeded');
                default:
                    throw new Error(`Threat detection error: ${operation}`);
            }
        }
    }
    async analyzeEvent(event, context) {
        this.throwErrorIfConfigured('analyzeEvent');
        const threats = event.severity === event_severity_enum_1.EventSeverity.HIGH || event.severity === event_severity_enum_1.EventSeverity.CRITICAL
            ? [{
                    id: unique_entity_id_value_object_1.UniqueEntityId.create().toString(),
                    severity: threat_severity_enum_1.ThreatSeverity.HIGH,
                    confidence: 85,
                    signature: 'detected-threat-001'
                }]
            : [];
        return {
            eventId: event.id.toString(),
            threats,
            confidence: threats.length > 0 ? 85 : 10,
            analysisTime: 50,
            indicators: [],
            recommendations: threats.length > 0 ? ['Investigate immediately'] : [],
        };
    }
    async analyzeEvents(events, context) {
        this.throwErrorIfConfigured('analyzeEvents');
        const analyses = await Promise.all(events.map(event => this.analyzeEvent(event, context)));
        return {
            totalEvents: events.length,
            analyses,
            aggregatedThreats: analyses.flatMap(a => a.threats),
        };
    }
}
describe('Error Handling Integration Tests', () => {
    let module;
    let orchestratorService;
    let eventRepository;
    let eventProcessor;
    let threatDetector;
    beforeEach(async () => {
        eventRepository = new ErrorProneEventRepository();
        eventProcessor = new ErrorProneEventProcessor();
        threatDetector = new ErrorProneThreatDetector();
        module = await testing_1.Test.createTestingModule({
            providers: [
                security_orchestrator_service_1.SecurityOrchestratorService,
                { provide: event_repository_1.EventRepository, useValue: eventRepository },
                { provide: threat_repository_1.ThreatRepository, useValue: {} },
                { provide: vulnerability_repository_1.VulnerabilityRepository, useValue: {} },
                { provide: response_action_repository_1.ResponseActionRepository, useValue: {} },
                { provide: event_processor_interface_1.EventProcessor, useValue: eventProcessor },
                { provide: threat_detector_interface_1.ThreatDetector, useValue: threatDetector },
                { provide: vulnerability_scanner_interface_1.VulnerabilityScanner, useValue: {} },
                { provide: response_executor_interface_1.ResponseExecutor, useValue: {} },
            ],
        }).compile();
        orchestratorService = module.get(security_orchestrator_service_1.SecurityOrchestratorService);
    });
    afterEach(async () => {
        eventRepository.clear();
        eventRepository.clearErrorScenario();
        eventProcessor.clearErrorScenario();
        threatDetector.clearErrorScenario();
        await module.close();
    });
    describe('Domain Validation Errors', () => {
        it('should handle validation errors in entity creation', () => {
            // Arrange - Create invalid event data
            const invalidEventData = {
                metadata: null, // Invalid - required field
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: 'app-001',
                    name: 'Test App',
                }),
                type: event_type_enum_1.EventType.AUTHENTICATION_FAILURE,
                severity: event_severity_enum_1.EventSeverity.MEDIUM,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: {},
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            };
            // Act & Assert
            expect(() => {
                event_factory_1.EventFactory.create(invalidEventData);
            }).toThrow(validation_exception_1.ValidationException);
        });
        it('should handle validation errors in value object creation', () => {
            // Test invalid IP address
            expect(() => {
                ip_address_value_object_1.IpAddress.create('invalid-ip');
            }).toThrow(validation_exception_1.ValidationException);
            // Test invalid port number
            expect(() => {
                port_value_object_1.Port.create(70000); // Port out of range
            }).toThrow(validation_exception_1.ValidationException);
            // Test invalid CVSS score
            expect(() => {
                cvss_score_value_object_1.CvssScore.create(15.0); // Score out of range
            }).toThrow(validation_exception_1.ValidationException);
        });
        it('should handle validation errors in factory methods', () => {
            // Test threat factory with invalid data
            expect(() => {
                threat_factory_1.ThreatFactory.create({
                    signature: '', // Empty signature
                    score: cvss_score_value_object_1.CvssScore.create(7.5),
                    severity: threat_severity_enum_1.ThreatSeverity.HIGH,
                    confidence: 150, // Invalid confidence > 100
                    sourceIp: ip_address_value_object_1.IpAddress.create('*************'),
                    indicators: [],
                    mitreTechniques: [],
                    tenantId: tenant_id_value_object_1.TenantId.create(),
                    userId: user_id_value_object_1.UserId.create(),
                });
            }).toThrow(validation_exception_1.ValidationException);
            // Test vulnerability factory with invalid CVE ID
            expect(() => {
                vulnerability_factory_1.VulnerabilityFactory.create({
                    cveId: 'INVALID-CVE', // Invalid CVE format
                    score: cvss_score_value_object_1.CvssScore.create(8.0),
                    severity: vulnerability_severity_enum_1.VulnerabilitySeverity.HIGH,
                    affectedPort: port_value_object_1.Port.create(80),
                    description: 'Test vulnerability',
                    tenantId: tenant_id_value_object_1.TenantId.create(),
                    userId: user_id_value_object_1.UserId.create(),
                });
            }).toThrow(validation_exception_1.ValidationException);
        });
    });
    describe('Repository Error Handling', () => {
        it('should handle repository save conflicts', async () => {
            // Arrange
            const event = event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'test', version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: 'app-001',
                    name: 'Test App',
                }),
                type: event_type_enum_1.EventType.AUTHENTICATION_SUCCESS,
                severity: event_severity_enum_1.EventSeverity.LOW,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { username: 'testuser' },
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            eventRepository.setErrorScenario('save');
            // Act
            const result = await orchestratorService.orchestrateSecurityWorkflow([event], {
                priority: 'LOW',
            });
            // Assert
            expect(result.isSuccess()).toBe(true);
            const orchestrationResult = result.getValue();
            expect(orchestrationResult.success).toBe(false);
            expect(orchestrationResult.errors.length).toBeGreaterThan(0);
            expect(orchestrationResult.errors[0].errorCode).toBe('ORCHESTRATION_FAILED');
        });
        it('should handle repository not found errors', async () => {
            // Arrange
            const nonExistentId = unique_entity_id_value_object_1.UniqueEntityId.create();
            eventRepository.setErrorScenario('findById');
            // Act & Assert
            const event = await eventRepository.findById(nonExistentId);
            expect(event).toBeNull(); // Should handle gracefully, not throw
        });
        it('should handle repository permission errors', async () => {
            // Arrange
            const event = event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'test', version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: 'app-001',
                    name: 'Test App',
                }),
                type: event_type_enum_1.EventType.SECURITY_ALERT,
                severity: event_severity_enum_1.EventSeverity.MEDIUM,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { alertType: 'test' },
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            await eventRepository.save(event);
            eventRepository.setErrorScenario('delete');
            // Act & Assert
            await expect(eventRepository.delete(event.id)).rejects.toThrow(forbidden_exception_1.ForbiddenException);
        });
        it('should handle repository service unavailable errors', async () => {
            // Arrange
            eventRepository.setErrorScenario('findAll');
            // Act & Assert
            await expect(eventRepository.findAll()).rejects.toThrow(service_unavailable_exception_1.ServiceUnavailableException);
        });
    });
    describe('Service Layer Error Handling', () => {
        it('should handle event processing validation errors', async () => {
            // Arrange
            const event = event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'test', version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: 'app-001',
                    name: 'Test App',
                }),
                type: event_type_enum_1.EventType.AUTHENTICATION_FAILURE,
                severity: event_severity_enum_1.EventSeverity.MEDIUM,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { username: 'testuser' },
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            eventProcessor.setErrorScenario('processEvent');
            // Act
            const result = await orchestratorService.processSecurityEvents([event], {
                priority: 'MEDIUM',
            });
            // Assert
            expect(result.isSuccess()).toBe(true);
            const processingResult = result.getValue();
            expect(processingResult.success).toBe(false);
            expect(processingResult.errors.length).toBeGreaterThan(0);
        });
        it('should handle threat detection service errors', async () => {
            // Arrange
            const event = event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'test', version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: 'app-001',
                    name: 'Test App',
                }),
                type: event_type_enum_1.EventType.NETWORK_INTRUSION,
                severity: event_severity_enum_1.EventSeverity.HIGH,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { sourceIp: '*************' },
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            threatDetector.setErrorScenario('analyzeEvent');
            // Act
            const result = await orchestratorService.orchestrateSecurityWorkflow([event], {
                priority: 'HIGH',
                enableThreatHunting: true,
            });
            // Assert
            expect(result.isSuccess()).toBe(true);
            const orchestrationResult = result.getValue();
            // Should continue processing despite threat detection failure
            expect(orchestrationResult.eventsProcessed).toBe(1);
            expect(orchestrationResult.errors.length).toBeGreaterThan(0);
        });
        it('should handle rate limiting errors', async () => {
            // Arrange
            const events = Array.from({ length: 5 }, (_, i) => event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: `source-${i}`, version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: `app-${i}`,
                    name: `App ${i}`,
                }),
                type: event_type_enum_1.EventType.AUTHENTICATION_FAILURE,
                severity: event_severity_enum_1.EventSeverity.MEDIUM,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { attempt: i },
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            }));
            eventProcessor.setErrorScenario('rateLimit', 3); // First 3 calls will fail
            // Act
            const result = await orchestratorService.orchestrateSecurityWorkflow(events, {
                priority: 'MEDIUM',
                workflowConfig: {
                    enableEventProcessing: true,
                    enableThreatDetection: false,
                    enableVulnerabilityScanning: false,
                    enableResponseExecution: false,
                    enableCorrelation: false,
                    enableEnrichment: false,
                    batchSize: 1,
                    maxConcurrentOperations: 1,
                    retryAttempts: 1,
                    timeoutMs: 60000,
                },
            });
            // Assert
            expect(result.isSuccess()).toBe(true);
            const orchestrationResult = result.getValue();
            // Should have processed some events successfully after rate limit errors
            expect(orchestrationResult.eventsProcessed).toBeGreaterThan(0);
            expect(orchestrationResult.errors.length).toBeGreaterThan(0);
        });
        it('should handle unauthorized access errors', async () => {
            // Arrange
            const event = event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'restricted', version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: 'restricted-app',
                    name: 'Restricted App',
                }),
                type: event_type_enum_1.EventType.SECURITY_ALERT,
                severity: event_severity_enum_1.EventSeverity.HIGH,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { alertType: 'restricted' },
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            eventProcessor.setErrorScenario('unauthorized');
            // Act
            const result = await orchestratorService.processSecurityEvents([event], {
                priority: 'HIGH',
            });
            // Assert
            expect(result.isSuccess()).toBe(true);
            const processingResult = result.getValue();
            expect(processingResult.success).toBe(false);
            expect(processingResult.errors.length).toBeGreaterThan(0);
        });
    });
    describe('Error Recovery and Resilience', () => {
        it('should implement circuit breaker pattern for failing services', async () => {
            // Arrange
            const events = Array.from({ length: 10 }, (_, i) => event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: `cb-test-${i}`, version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: `cb-app-${i}`,
                    name: `Circuit Breaker App ${i}`,
                }),
                type: event_type_enum_1.EventType.AUTHENTICATION_FAILURE,
                severity: event_severity_enum_1.EventSeverity.MEDIUM,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { attempt: i },
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            }));
            // Configure threat detector to fail consistently
            threatDetector.setErrorScenario('analyzeEvent', 10);
            // Act
            const result = await orchestratorService.orchestrateSecurityWorkflow(events, {
                priority: 'MEDIUM',
                workflowConfig: {
                    enableEventProcessing: true,
                    enableThreatDetection: true,
                    enableVulnerabilityScanning: false,
                    enableResponseExecution: false,
                    enableCorrelation: false,
                    enableEnrichment: false,
                    batchSize: 2,
                    maxConcurrentOperations: 3,
                    retryAttempts: 1,
                    timeoutMs: 60000,
                },
            });
            // Assert
            expect(result.isSuccess()).toBe(true);
            const orchestrationResult = result.getValue();
            // Should continue processing events despite threat detection failures
            expect(orchestrationResult.eventsProcessed).toBe(10);
            expect(orchestrationResult.processingResults).toHaveLength(10);
            // Should have errors from threat detection
            expect(orchestrationResult.errors.length).toBeGreaterThan(0);
            // But overall processing should succeed
            orchestrationResult.processingResults.forEach(pr => {
                expect(pr.success).toBe(true);
            });
        });
        it('should handle partial failures in batch processing', async () => {
            // Arrange
            const events = Array.from({ length: 6 }, (_, i) => event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: `batch-${i}`, version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: `batch-app-${i}`,
                    name: `Batch App ${i}`,
                }),
                type: event_type_enum_1.EventType.SECURITY_ALERT,
                severity: event_severity_enum_1.EventSeverity.LOW,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { batchIndex: i },
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            }));
            // Configure processor to fail on first 2 events
            eventProcessor.setErrorScenario('processEvent', 2);
            // Act
            const result = await orchestratorService.orchestrateSecurityWorkflow(events, {
                priority: 'LOW',
                workflowConfig: {
                    enableEventProcessing: true,
                    enableThreatDetection: false,
                    enableVulnerabilityScanning: false,
                    enableResponseExecution: false,
                    enableCorrelation: false,
                    enableEnrichment: false,
                    batchSize: 3,
                    maxConcurrentOperations: 2,
                    retryAttempts: 1,
                    timeoutMs: 60000,
                },
            });
            // Assert
            expect(result.isSuccess()).toBe(true);
            const orchestrationResult = result.getValue();
            // Should have processed some events successfully
            expect(orchestrationResult.eventsProcessed).toBeGreaterThan(0);
            expect(orchestrationResult.eventsProcessed).toBeLessThan(6); // Some failed
            // Should have both successful and failed processing results
            expect(orchestrationResult.processingResults.length).toBeGreaterThan(0);
            expect(orchestrationResult.errors.length).toBeGreaterThan(0);
        });
        it('should implement exponential backoff for retries', async () => {
            // Arrange
            const event = event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'retry-test', version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: 'retry-app',
                    name: 'Retry Test App',
                }),
                type: event_type_enum_1.EventType.AUTHENTICATION_FAILURE,
                severity: event_severity_enum_1.EventSeverity.MEDIUM,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { username: 'retryuser' },
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            let callCount = 0;
            const callTimes = [];
            const originalProcessEvent = eventProcessor.processEvent.bind(eventProcessor);
            eventProcessor.processEvent = jest.fn().mockImplementation(async (event, context) => {
                callTimes.push(Date.now());
                callCount++;
                if (callCount <= 2) {
                    throw new service_unavailable_exception_1.ServiceUnavailableException('Transient failure');
                }
                return originalProcessEvent(event, context);
            });
            // Act
            const startTime = Date.now();
            const result = await orchestratorService.processSecurityEvents([event], {
                priority: 'MEDIUM',
                workflowConfig: {
                    enableEventProcessing: true,
                    enableThreatDetection: false,
                    enableVulnerabilityScanning: false,
                    enableResponseExecution: false,
                    enableCorrelation: false,
                    enableEnrichment: false,
                    batchSize: 1,
                    maxConcurrentOperations: 1,
                    retryAttempts: 3,
                    timeoutMs: 60000,
                },
            });
            // Assert
            expect(result.isSuccess()).toBe(true);
            const processingResult = result.getValue();
            expect(processingResult.success).toBe(true);
            expect(processingResult.eventsProcessed).toBe(1);
            expect(callCount).toBe(3); // Initial call + 2 retries
            // Verify exponential backoff (approximate timing)
            if (callTimes.length >= 3) {
                const firstRetryDelay = callTimes[1] - callTimes[0];
                const secondRetryDelay = callTimes[2] - callTimes[1];
                expect(secondRetryDelay).toBeGreaterThan(firstRetryDelay);
            }
        });
    });
    describe('Error Propagation and Context', () => {
        it('should maintain error context through service layers', async () => {
            // Arrange
            const tenantId = tenant_id_value_object_1.TenantId.create();
            const userId = user_id_value_object_1.UserId.create();
            const event = event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'context-test', version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: 'context-app',
                    name: 'Context Test App',
                }),
                type: event_type_enum_1.EventType.SECURITY_ALERT,
                severity: event_severity_enum_1.EventSeverity.HIGH,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { alertType: 'context-test' },
                tenantId,
                userId,
            });
            eventProcessor.setErrorScenario('processEvent');
            // Act
            const result = await orchestratorService.orchestrateSecurityWorkflow([event], {
                tenantId: tenantId.value,
                userId: userId.value,
                priority: 'HIGH',
            });
            // Assert
            expect(result.isSuccess()).toBe(true);
            const orchestrationResult = result.getValue();
            expect(orchestrationResult.success).toBe(false);
            expect(orchestrationResult.errors.length).toBeGreaterThan(0);
            const error = orchestrationResult.errors[0];
            expect(error.eventId).toBeDefined();
            expect(error.timestamp).toBeInstanceOf(Date);
            expect(error.stage).toBeDefined();
            expect(error.errorCode).toBeDefined();
            expect(error.message).toBeDefined();
        });
        it('should handle cascading errors gracefully', async () => {
            // Arrange
            const event = event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'cascade-test', version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: 'cascade-app',
                    name: 'Cascade Test App',
                }),
                type: event_type_enum_1.EventType.NETWORK_INTRUSION,
                severity: event_severity_enum_1.EventSeverity.CRITICAL,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { sourceIp: '*************' },
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            // Configure multiple services to fail
            eventProcessor.setErrorScenario('processEvent');
            threatDetector.setErrorScenario('analyzeEvent');
            // Act
            const result = await orchestratorService.orchestrateSecurityWorkflow([event], {
                priority: 'CRITICAL',
                autoResponse: false,
            });
            // Assert
            expect(result.isSuccess()).toBe(true);
            const orchestrationResult = result.getValue();
            expect(orchestrationResult.success).toBe(false);
            expect(orchestrationResult.errors.length).toBeGreaterThan(0);
            // Should have errors from multiple stages
            const errorStages = orchestrationResult.errors.map(e => e.stage);
            expect(errorStages.length).toBeGreaterThan(0);
        });
        it('should provide detailed error information for debugging', async () => {
            // Arrange
            const event = event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'debug-test', version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: 'debug-app',
                    name: 'Debug Test App',
                }),
                type: event_type_enum_1.EventType.AUTHENTICATION_FAILURE,
                severity: event_severity_enum_1.EventSeverity.MEDIUM,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { username: 'debuguser' },
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            eventRepository.setErrorScenario('save');
            // Act
            const result = await orchestratorService.orchestrateSecurityWorkflow([event], {
                priority: 'MEDIUM',
            });
            // Assert
            expect(result.isSuccess()).toBe(true);
            const orchestrationResult = result.getValue();
            expect(orchestrationResult.success).toBe(false);
            expect(orchestrationResult.errors.length).toBeGreaterThan(0);
            const error = orchestrationResult.errors[0];
            expect(error.errorCode).toBe('ORCHESTRATION_FAILED');
            expect(error.message).toContain('Event already exists');
            expect(error.recoverable).toBe(false);
            expect(error.timestamp).toBeInstanceOf(Date);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************