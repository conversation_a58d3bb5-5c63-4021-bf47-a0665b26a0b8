34f49b99c0ba4a1afdec11a0de7a036c
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThreatSeverityUtils = exports.ThreatSeverity = void 0;
/**
 * Threat Severity Enum
 *
 * Represents the severity level of identified threats based on potential impact,
 * likelihood, and risk assessment. Used for threat prioritization, response planning,
 * and resource allocation in security operations.
 *
 * Severity Levels (ascending order):
 * UNKNOWN -> LOW -> MEDIUM -> HIGH -> CRITICAL
 */
var ThreatSeverity;
(function (ThreatSeverity) {
    /**
     * Unknown threat severity
     * - Insufficient information for assessment
     * - Requires further analysis
     * - Default state for new threats
     */
    ThreatSeverity["UNKNOWN"] = "unknown";
    /**
     * Low severity threats
     * - Minimal potential impact
     * - Low likelihood of exploitation
     * - Routine monitoring sufficient
     * - No immediate action required
     */
    ThreatSeverity["LOW"] = "low";
    /**
     * Medium severity threats
     * - Moderate potential impact
     * - Possible exploitation scenarios
     * - Enhanced monitoring recommended
     * - Mitigation planning advised
     */
    ThreatSeverity["MEDIUM"] = "medium";
    /**
     * High severity threats
     * - Significant potential impact
     * - High likelihood of exploitation
     * - Active monitoring required
     * - Immediate mitigation needed
     */
    ThreatSeverity["HIGH"] = "high";
    /**
     * Critical severity threats
     * - Severe potential impact
     * - Imminent or active exploitation
     * - Continuous monitoring required
     * - Emergency response activated
     */
    ThreatSeverity["CRITICAL"] = "critical";
})(ThreatSeverity || (exports.ThreatSeverity = ThreatSeverity = {}));
/**
 * Threat Severity Utilities
 */
class ThreatSeverityUtils {
    /**
     * Get all threat severity levels
     */
    static getAllSeverities() {
        return Object.values(ThreatSeverity);
    }
    /**
     * Get severity levels requiring immediate action
     */
    static getImmediateActionSeverities() {
        return [ThreatSeverity.HIGH, ThreatSeverity.CRITICAL];
    }
    /**
     * Get severity levels requiring enhanced monitoring
     */
    static getEnhancedMonitoringSeverities() {
        return [ThreatSeverity.MEDIUM, ThreatSeverity.HIGH, ThreatSeverity.CRITICAL];
    }
    /**
     * Get severity levels for routine processing
     */
    static getRoutineSeverities() {
        return [ThreatSeverity.LOW, ThreatSeverity.UNKNOWN];
    }
    /**
     * Check if severity requires immediate action
     */
    static requiresImmediateAction(severity) {
        return ThreatSeverityUtils.getImmediateActionSeverities().includes(severity);
    }
    /**
     * Check if severity is critical
     */
    static isCritical(severity) {
        return severity === ThreatSeverity.CRITICAL;
    }
    /**
     * Check if severity is high or critical
     */
    static isHighOrCritical(severity) {
        return severity === ThreatSeverity.HIGH || severity === ThreatSeverity.CRITICAL;
    }
    /**
     * Check if severity requires enhanced monitoring
     */
    static requiresEnhancedMonitoring(severity) {
        return ThreatSeverityUtils.getEnhancedMonitoringSeverities().includes(severity);
    }
    /**
     * Get numeric value for severity (for comparison and scoring)
     */
    static getNumericValue(severity) {
        const values = {
            [ThreatSeverity.UNKNOWN]: 0,
            [ThreatSeverity.LOW]: 1,
            [ThreatSeverity.MEDIUM]: 2,
            [ThreatSeverity.HIGH]: 3,
            [ThreatSeverity.CRITICAL]: 4,
        };
        return values[severity];
    }
    /**
     * Get severity score (0-100) for risk calculations
     */
    static getScore(severity) {
        const scores = {
            [ThreatSeverity.UNKNOWN]: 0,
            [ThreatSeverity.LOW]: 25,
            [ThreatSeverity.MEDIUM]: 50,
            [ThreatSeverity.HIGH]: 75,
            [ThreatSeverity.CRITICAL]: 100,
        };
        return scores[severity];
    }
    /**
     * Compare two threat severities
     */
    static compare(severity1, severity2) {
        return ThreatSeverityUtils.getNumericValue(severity1) - ThreatSeverityUtils.getNumericValue(severity2);
    }
    /**
     * Get the higher of two severities
     */
    static getHigher(severity1, severity2) {
        return ThreatSeverityUtils.compare(severity1, severity2) >= 0 ? severity1 : severity2;
    }
    /**
     * Get the lower of two severities
     */
    static getLower(severity1, severity2) {
        return ThreatSeverityUtils.compare(severity1, severity2) <= 0 ? severity1 : severity2;
    }
    /**
     * Get response time SLA in minutes based on threat severity
     */
    static getResponseTimeSLA(severity) {
        const slaMinutes = {
            [ThreatSeverity.CRITICAL]: 5, // 5 minutes
            [ThreatSeverity.HIGH]: 30, // 30 minutes
            [ThreatSeverity.MEDIUM]: 120, // 2 hours
            [ThreatSeverity.LOW]: 480, // 8 hours
            [ThreatSeverity.UNKNOWN]: 1440, // 24 hours
        };
        return slaMinutes[severity];
    }
    /**
     * Get containment time SLA in hours based on threat severity
     */
    static getContainmentTimeSLA(severity) {
        const slaHours = {
            [ThreatSeverity.CRITICAL]: 1, // 1 hour
            [ThreatSeverity.HIGH]: 4, // 4 hours
            [ThreatSeverity.MEDIUM]: 24, // 24 hours
            [ThreatSeverity.LOW]: 72, // 3 days
            [ThreatSeverity.UNKNOWN]: 168, // 7 days
        };
        return slaHours[severity];
    }
    /**
     * Get eradication time SLA in hours based on threat severity
     */
    static getEradicationTimeSLA(severity) {
        const slaHours = {
            [ThreatSeverity.CRITICAL]: 4, // 4 hours
            [ThreatSeverity.HIGH]: 24, // 24 hours
            [ThreatSeverity.MEDIUM]: 72, // 3 days
            [ThreatSeverity.LOW]: 168, // 7 days
            [ThreatSeverity.UNKNOWN]: 336, // 14 days
        };
        return slaHours[severity];
    }
    /**
     * Get escalation time in minutes based on threat severity
     */
    static getEscalationTime(severity) {
        const escalationMinutes = {
            [ThreatSeverity.CRITICAL]: 15, // 15 minutes
            [ThreatSeverity.HIGH]: 60, // 1 hour
            [ThreatSeverity.MEDIUM]: 240, // 4 hours
            [ThreatSeverity.LOW]: 960, // 16 hours
            [ThreatSeverity.UNKNOWN]: 2880, // 48 hours
        };
        return escalationMinutes[severity];
    }
    /**
     * Get required response team based on threat severity
     */
    static getResponseTeam(severity) {
        const teams = {
            [ThreatSeverity.CRITICAL]: ['incident_commander', 'senior_analyst', 'threat_hunter', 'forensics', 'legal', 'communications'],
            [ThreatSeverity.HIGH]: ['senior_analyst', 'threat_hunter', 'forensics'],
            [ThreatSeverity.MEDIUM]: ['analyst', 'threat_hunter'],
            [ThreatSeverity.LOW]: ['analyst'],
            [ThreatSeverity.UNKNOWN]: ['analyst'],
        };
        return teams[severity];
    }
    /**
     * Get notification channels based on threat severity
     */
    static getNotificationChannels(severity) {
        const channels = {
            [ThreatSeverity.CRITICAL]: ['email', 'sms', 'slack', 'webhook', 'pager', 'phone'],
            [ThreatSeverity.HIGH]: ['email', 'sms', 'slack', 'webhook'],
            [ThreatSeverity.MEDIUM]: ['email', 'slack', 'webhook'],
            [ThreatSeverity.LOW]: ['email', 'webhook'],
            [ThreatSeverity.UNKNOWN]: ['webhook'],
        };
        return channels[severity];
    }
    /**
     * Get monitoring frequency based on threat severity
     */
    static getMonitoringFrequency(severity) {
        const frequencies = {
            [ThreatSeverity.CRITICAL]: { interval: 1, duration: 72 }, // Every minute for 3 days
            [ThreatSeverity.HIGH]: { interval: 5, duration: 48 }, // Every 5 minutes for 2 days
            [ThreatSeverity.MEDIUM]: { interval: 15, duration: 24 }, // Every 15 minutes for 1 day
            [ThreatSeverity.LOW]: { interval: 60, duration: 8 }, // Every hour for 8 hours
            [ThreatSeverity.UNKNOWN]: { interval: 240, duration: 4 }, // Every 4 hours for 4 hours
        };
        return frequencies[severity];
    }
    /**
     * Get color code for UI display
     */
    static getColorCode(severity) {
        const colors = {
            [ThreatSeverity.CRITICAL]: '#DC2626', // Red
            [ThreatSeverity.HIGH]: '#EA580C', // Orange
            [ThreatSeverity.MEDIUM]: '#D97706', // Amber
            [ThreatSeverity.LOW]: '#16A34A', // Green
            [ThreatSeverity.UNKNOWN]: '#6B7280', // Gray
        };
        return colors[severity];
    }
    /**
     * Get icon name for UI display
     */
    static getIconName(severity) {
        const icons = {
            [ThreatSeverity.CRITICAL]: 'shield-alert',
            [ThreatSeverity.HIGH]: 'shield-exclamation',
            [ThreatSeverity.MEDIUM]: 'shield-check',
            [ThreatSeverity.LOW]: 'shield',
            [ThreatSeverity.UNKNOWN]: 'shield-question',
        };
        return icons[severity];
    }
    /**
     * Get human-readable description
     */
    static getDescription(severity) {
        const descriptions = {
            [ThreatSeverity.CRITICAL]: 'Critical threat requiring immediate emergency response',
            [ThreatSeverity.HIGH]: 'High-severity threat requiring urgent containment and mitigation',
            [ThreatSeverity.MEDIUM]: 'Medium-severity threat requiring enhanced monitoring and planned response',
            [ThreatSeverity.LOW]: 'Low-severity threat requiring routine monitoring and documentation',
            [ThreatSeverity.UNKNOWN]: 'Unknown threat severity requiring assessment and classification',
        };
        return descriptions[severity];
    }
    /**
     * Get severity from numeric score (0-100)
     */
    static fromScore(score) {
        if (score >= 90)
            return ThreatSeverity.CRITICAL;
        if (score >= 70)
            return ThreatSeverity.HIGH;
        if (score >= 40)
            return ThreatSeverity.MEDIUM;
        if (score >= 10)
            return ThreatSeverity.LOW;
        return ThreatSeverity.UNKNOWN;
    }
    /**
     * Get severity from CVSS score
     */
    static fromCVSSScore(cvssScore) {
        if (cvssScore >= 9.0)
            return ThreatSeverity.CRITICAL;
        if (cvssScore >= 7.0)
            return ThreatSeverity.HIGH;
        if (cvssScore >= 4.0)
            return ThreatSeverity.MEDIUM;
        if (cvssScore >= 0.1)
            return ThreatSeverity.LOW;
        return ThreatSeverity.UNKNOWN;
    }
    /**
     * Get severity from string (case-insensitive)
     */
    static fromString(value) {
        const normalized = value.toLowerCase().trim();
        const severities = Object.values(ThreatSeverity);
        return severities.find(s => s === normalized) || null;
    }
    /**
     * Validate severity value
     */
    static isValid(severity) {
        return Object.values(ThreatSeverity).includes(severity);
    }
    /**
     * Get processing priority (1-10) based on threat severity
     */
    static getProcessingPriority(severity) {
        const priorities = {
            [ThreatSeverity.CRITICAL]: 10,
            [ThreatSeverity.HIGH]: 8,
            [ThreatSeverity.MEDIUM]: 6,
            [ThreatSeverity.LOW]: 4,
            [ThreatSeverity.UNKNOWN]: 2,
        };
        return priorities[severity];
    }
    /**
     * Get threat intelligence requirements
     */
    static getThreatIntelRequirements(severity) {
        return {
            enrichment: severity !== ThreatSeverity.UNKNOWN,
            attribution: ThreatSeverityUtils.isHighOrCritical(severity),
            iocGeneration: ThreatSeverityUtils.requiresEnhancedMonitoring(severity),
            sharing: ThreatSeverityUtils.requiresImmediateAction(severity),
        };
    }
    /**
     * Get containment strategy based on threat severity
     */
    static getContainmentStrategy(severity) {
        return {
            isolation: ThreatSeverityUtils.requiresEnhancedMonitoring(severity),
            quarantine: ThreatSeverityUtils.isHighOrCritical(severity),
            networkSegmentation: ThreatSeverityUtils.requiresImmediateAction(severity),
            systemShutdown: severity === ThreatSeverity.CRITICAL,
        };
    }
    /**
     * Get communication requirements based on threat severity
     */
    static getCommunicationRequirements(severity) {
        return {
            internalNotification: severity !== ThreatSeverity.UNKNOWN,
            executiveNotification: ThreatSeverityUtils.isHighOrCritical(severity),
            customerNotification: severity === ThreatSeverity.CRITICAL,
            regulatoryNotification: severity === ThreatSeverity.CRITICAL,
            publicNotification: false, // Determined by specific threat context
        };
    }
    /**
     * Calculate combined severity from multiple threats
     */
    static calculateCombinedSeverity(severities) {
        if (severities.length === 0) {
            return ThreatSeverity.UNKNOWN;
        }
        // If any threat is critical, combined severity is critical
        if (severities.includes(ThreatSeverity.CRITICAL)) {
            return ThreatSeverity.CRITICAL;
        }
        // Calculate weighted average
        const numericValues = severities.map(s => ThreatSeverityUtils.getNumericValue(s));
        const average = numericValues.reduce((sum, val) => sum + val, 0) / numericValues.length;
        // Convert back to severity
        if (average >= 3.5)
            return ThreatSeverity.CRITICAL;
        if (average >= 2.5)
            return ThreatSeverity.HIGH;
        if (average >= 1.5)
            return ThreatSeverity.MEDIUM;
        if (average >= 0.5)
            return ThreatSeverity.LOW;
        return ThreatSeverity.UNKNOWN;
    }
}
exports.ThreatSeverityUtils = ThreatSeverityUtils;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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