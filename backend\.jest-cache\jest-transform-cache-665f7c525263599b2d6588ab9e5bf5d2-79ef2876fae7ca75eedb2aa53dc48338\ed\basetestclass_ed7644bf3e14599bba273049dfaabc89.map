{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\testing\\base\\base-test.class.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6CAAsD;AAEtD,2CAA6D;AAC7D,6CAAgD;AAChD,yDAA2D;AAC3D,qCAAwC;AACxC,+CAAkD;AAClD,mDAAqC;AACrC,oEAA+D;AAC/D,uEAAkE;AAElE;;;;;;;;;;;;GAYG;AACH,MAAsB,aAAa;IAQjC;;OAEG;IACO,KAAK,CAAC,oBAAoB,CAClC,cAAmB,EACnB,WAAkB,EAAE,EACpB,kBAAyB,EAAE;QAE3B,8BAA8B;QAC9B,IAAI,CAAC,YAAY,GAAG,qCAAgB,CAAC,WAAW,EAAE,CAAC;QACnD,MAAM,IAAI,CAAC,YAAY,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;QAEzD,wBAAwB;QACxB,IAAI,CAAC,aAAa,GAAG,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAClD,OAAO,EAAE;gBACP,qBAAY,CAAC,OAAO,CAAC;oBACnB,QAAQ,EAAE,IAAI;oBACd,WAAW,EAAE,CAAC,WAAW,EAAE,MAAM,CAAC;iBACnC,CAAC;gBACF,uBAAa,CAAC,YAAY,CAAC;oBACzB,OAAO,EAAE,CAAC,qBAAY,CAAC;oBACvB,UAAU,EAAE,KAAK,EAAE,aAA4B,EAAE,EAAE,CAAC,CAAC;wBACnD,IAAI,EAAE,UAAU;wBAChB,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC;wBACpD,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC;wBAC7C,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,kBAAkB,EAAE,MAAM,CAAC;wBACvD,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,kBAAkB,EAAE,MAAM,CAAC;wBACvD,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,cAAc,EAAE,eAAe,CAAC;wBAC5D,QAAQ;wBACR,WAAW,EAAE,IAAI;wBACjB,UAAU,EAAE,KAAK;wBACjB,OAAO,EAAE,KAAK;qBACf,CAAC;oBACF,MAAM,EAAE,CAAC,sBAAa,CAAC;iBACxB,CAAC;gBACF,uBAAa,CAAC,UAAU,CAAC,QAAQ,CAAC;gBAClC,kCAAkB,CAAC,OAAO,EAAE;gBAC5B,yBAAc,CAAC,QAAQ,CAAC,EAAE,eAAe,EAAE,KAAK,EAAE,CAAC;gBACnD,eAAS,CAAC,aAAa,CAAC;oBACtB,OAAO,EAAE,CAAC,qBAAY,CAAC;oBACvB,UAAU,EAAE,KAAK,EAAE,aAA4B,EAAE,EAAE,CAAC,CAAC;wBACnD,MAAM,EAAE,aAAa,CAAC,GAAG,CAAC,YAAY,EAAE,aAAa,CAAC;wBACtD,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;qBACjC,CAAC;oBACF,MAAM,EAAE,CAAC,sBAAa,CAAC;iBACxB,CAAC;gBACF,GAAG,cAAc,CAAC,OAAO,IAAI,EAAE;aAChC;YACD,WAAW,EAAE,cAAc,CAAC,WAAW,IAAI,EAAE;YAC7C,SAAS,EAAE;gBACT,GAAG,cAAc,CAAC,SAAS,IAAI,EAAE;gBACjC,GAAG,eAAe;aACnB;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,8BAA8B;QAC9B,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC;QAEtD,+CAA+C;QAC/C,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAEnC,yBAAyB;QACzB,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;QAE3C,qCAAqC;QACrC,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACvC,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,sBAAsB;QACpC,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YACb,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;QACzB,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,MAAM,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;QAC5C,CAAC;IACH,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,aAAa;QAC3B,MAAM,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;QAE3C,8BAA8B;QAC9B,MAAM,YAAY,GAAG,qCAAgB,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;QACtE,MAAM,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,eAAe;QAC7B,MAAM,IAAI,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC;IAChD,CAAC;IAED;;OAEG;IACO,UAAU,CAAI,YAAuC;QAC7D,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAI,YAAY,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACO,aAAa,CAAI,MAAmB;QAC5C,OAAO,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACO,kBAAkB,CAC1B,KAAoD,EACpD,kBAA8B;QAE9B,OAAO;YACL,OAAO,EAAE,KAAK;YACd,QAAQ,EAAE,kBAAkB;SAC7B,CAAC;IACJ,CAAC;IAED;;OAEG;IACO,iBAAiB,CACzB,KAAoD,EACpD,eAAkB;QAElB,MAAM,GAAG,GAAG,IAAI,CAAC,oBAAoB,CAAI,eAAsB,CAAC,CAAC;QACjE,OAAO;YACL,OAAO,EAAE,KAAK;YACd,QAAQ,EAAE,GAAG;SACd,CAAC;IACJ,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,wBAAwB,CACtC,MAAmD,EACnD,IAAY,EACZ,IAAU,EACV,OAAgC;QAEhC,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;QAEnD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,GAAG,CAAC,GAAG,CAAC,eAAe,EAAE,UAAU,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;gBAC/C,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACtD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjB,CAAC;QAED,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,0BAA0B,CACxC,MAAmD,EACnD,IAAY,EACZ,IAAU,EACV,OAAgC;QAEhC,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;QAEnD,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;gBAC/C,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACtD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjB,CAAC;QAED,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,YAAY,CAC1B,SAAiB,EACjB,UAAkB,IAAI;QAEtB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC5B,MAAM,CAAC,IAAI,KAAK,CAAC,SAAS,SAAS,uBAAuB,OAAO,IAAI,CAAC,CAAC,CAAC;YAC1E,CAAC,EAAE,OAAO,CAAC,CAAC;YAEZ,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;YAC7D,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,IAAS,EAAE,EAAE;gBACzC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACpB,OAAO,CAAC,IAAI,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,oBAAoB,CAClC,SAA2B;QAE3B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;QACjC,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAE7C,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,uBAAuB,CACrC,SAA6B,EAC7B,aAAqB,GAAG;QAQxB,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;YACpC,MAAM,EAAE,aAAa,EAAE,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;YACrE,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC5B,CAAC;QAED,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;QAC7D,MAAM,WAAW,GAAG,SAAS,GAAG,UAAU,CAAC;QAC3C,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;QACnC,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;QAEnC,OAAO;YACL,WAAW;YACX,OAAO;YACP,OAAO;YACP,SAAS;YACT,UAAU;SACX,CAAC;IACJ,CAAC;IAED;;OAEG;IACO,uBAAuB,CAC/B,QAAa,EACb,iBAAsC;QAEtC,KAAK,MAAM,CAAC,GAAG,EAAE,YAAY,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE,CAAC;YACpE,MAAM,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;YAErC,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;gBACrC,MAAM,CAAC,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAClD,CAAC;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;gBACvC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAChD,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACxD,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClE,CAAC;YACH,CAAC;iBAAM,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;gBAC5C,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,YAAY,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,mBAAmB,CACjC,MAAmB,EACnB,UAAsB,EACtB,aAAqB;QAErB,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAC9C,MAAM,WAAW,GAAG,MAAM,UAAU,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,UAAiB,EAAE,CAAC,CAAC;QACzE,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,kBAAkB,CAChC,SAAiB,EACjB,YAAkB,EAClB,UAAkB,IAAI;QAEtB,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAE9D,IAAI,YAAY,EAAE,CAAC;gBACjB,MAAM,CAAC,SAAS,CAAC,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;YAChD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,kBAAkB,SAAS,kBAAkB,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAED;;OAEG;IACO,qBAAqB,CAAI,MAAmB;QACpD,OAAO,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB;QACjC,+BAA+B;QAC/B,MAAM,EAAE,cAAc,EAAE,GAAG,wDAAa,gBAAgB,GAAC,CAAC;QAC1D,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,cAAc,CAAC;YACzC,SAAS,EAAE,IAAI;YACf,SAAS,EAAE,IAAI;YACf,oBAAoB,EAAE,IAAI;SAC3B,CAAC,CAAC,CAAC;QAEJ,gCAAgC;QAChC,MAAM,EAAE,mBAAmB,EAAE,GAAG,wDAAa,+CAA+C,GAAC,CAAC;QAC9F,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,mBAAmB,EAAE,CAAC,CAAC;QAErD,mCAAmC;QACnC,MAAM,EAAE,kBAAkB,EAAE,GAAG,wDAAa,kDAAkD,GAAC,CAAC;QAChG,IAAI,CAAC,GAAG,CAAC,qBAAqB,CAAC,IAAI,kBAAkB,EAAE,CAAC,CAAC;IAC3D,CAAC;IAEO,KAAK,CAAC,uBAAuB;QACnC,mBAAmB;QACnB,IAAI,CAAC,QAAQ,GAAG,qCAAgB,CAAC,YAAY,CAAC;YAC5C,KAAK,EAAE,kBAAkB;YACzB,QAAQ,EAAE,UAAU;YACpB,IAAI,EAAE,OAAO;YACb,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC;QAEH,qBAAqB;QACrB,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QACxD,IAAI,CAAC,SAAS,GAAG,MAAM,UAAU,CAAC,SAAS,CAAC;YAC1C,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE;YACrB,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK;YAC1B,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;SACzB,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,YAAiB;QAC1C,aAAa;QACb,IAAI,YAAY,CAAC,KAAK,EAAE,CAAC;YACvB,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YAClD,qEAAqE;YACrE,iDAAiD;QACnD,CAAC;QAED,gCAAgC;QAChC,iEAAiE;IACnE,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,cAAc,CAAC,YAA0B,EAAE;QACzD,MAAM,QAAQ,GAAG,qCAAgB,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAC1D,8BAA8B;QAC9B,OAAO,QAAQ,CAAC;IAClB,CAAC;IAES,KAAK,CAAC,0BAA0B,CAAC,YAA0B,EAAE;QACrE,MAAM,UAAU,GAAG,qCAAgB,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC;QACxE,8BAA8B;QAC9B,OAAO,UAAU,CAAC;IACpB,CAAC;IAES,KAAK,CAAC,0BAA0B,CAAC,YAA0B,EAAE;QACrE,MAAM,YAAY,GAAG,qCAAgB,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC;QAC1E,8BAA8B;QAC9B,OAAO,YAAY,CAAC;IACtB,CAAC;IAES,KAAK,CAAC,sBAAsB,CAAC,YAA0B,EAAE;QACjE,MAAM,OAAO,GAAG,qCAAgB,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;QACjE,8BAA8B;QAC9B,OAAO,OAAO,CAAC;IACjB,CAAC;IAES,KAAK,CAAC,gBAAgB,CAAC,YAA0B,EAAE;QAC3D,MAAM,UAAU,GAAG,qCAAgB,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QAC9D,8BAA8B;QAC9B,OAAO,UAAU,CAAC;IACpB,CAAC;IAES,KAAK,CAAC,qBAAqB,CAAC,YAA0B,EAAE;QAChE,MAAM,eAAe,GAAG,qCAAgB,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;QACxE,8BAA8B;QAC9B,OAAO,eAAe,CAAC;IACzB,CAAC;IAES,KAAK,CAAC,eAAe,CAAC,YAA0B,EAAE;QAC1D,MAAM,SAAS,GAAG,qCAAgB,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAC5D,8BAA8B;QAC9B,OAAO,SAAS,CAAC;IACnB,CAAC;CACF;AAzaD,sCAyaC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\testing\\base\\base-test.class.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { INestApplication } from '@nestjs/common';\r\nimport { ConfigModule, ConfigService } from '@nestjs/config';\r\nimport { TypeOrmModule } from '@nestjs/typeorm';\r\nimport { EventEmitterModule } from '@nestjs/event-emitter';\r\nimport { JwtModule } from '@nestjs/jwt';\r\nimport { PassportModule } from '@nestjs/passport';\r\nimport * as request from 'supertest';\r\nimport { TestDatabaseUtil } from '../utils/test-database.util';\r\nimport { TestFixturesUtil } from '../fixtures/test-fixtures.util';\r\n\r\n/**\r\n * Base Test Class\r\n * \r\n * Comprehensive base class for all test suites providing:\r\n * - NestJS testing module setup with proper dependency injection\r\n * - Database transaction management with automatic rollback\r\n * - Authentication and authorization testing utilities\r\n * - Mock service creation and provider overrides\r\n * - Performance testing and benchmarking utilities\r\n * - Event emission testing and validation\r\n * - HTTP request testing with supertest integration\r\n * - Test data management and cleanup utilities\r\n */\r\nexport abstract class BaseTestClass {\r\n  protected app: INestApplication;\r\n  protected testingModule: TestingModule;\r\n  protected databaseUtil: TestDatabaseUtil;\r\n  protected httpServer: any;\r\n  protected authToken: string;\r\n  protected testUser: any;\r\n\r\n  /**\r\n   * Setup test environment before all tests\r\n   */\r\n  protected async setupTestEnvironment(\r\n    moduleMetadata: any,\r\n    entities: any[] = [],\r\n    customProviders: any[] = []\r\n  ): Promise<void> {\r\n    // Initialize database utility\r\n    this.databaseUtil = TestDatabaseUtil.getInstance();\r\n    await this.databaseUtil.initializeTestDatabase(entities);\r\n\r\n    // Create testing module\r\n    this.testingModule = await Test.createTestingModule({\r\n      imports: [\r\n        ConfigModule.forRoot({\r\n          isGlobal: true,\r\n          envFilePath: ['.env.test', '.env'],\r\n        }),\r\n        TypeOrmModule.forRootAsync({\r\n          imports: [ConfigModule],\r\n          useFactory: async (configService: ConfigService) => ({\r\n            type: 'postgres',\r\n            host: configService.get('TEST_DB_HOST', 'localhost'),\r\n            port: configService.get('TEST_DB_PORT', 5433),\r\n            username: configService.get('TEST_DB_USERNAME', 'test'),\r\n            password: configService.get('TEST_DB_PASSWORD', 'test'),\r\n            database: configService.get('TEST_DB_NAME', 'sentinel_test'),\r\n            entities,\r\n            synchronize: true,\r\n            dropSchema: false,\r\n            logging: false,\r\n          }),\r\n          inject: [ConfigService],\r\n        }),\r\n        TypeOrmModule.forFeature(entities),\r\n        EventEmitterModule.forRoot(),\r\n        PassportModule.register({ defaultStrategy: 'jwt' }),\r\n        JwtModule.registerAsync({\r\n          imports: [ConfigModule],\r\n          useFactory: async (configService: ConfigService) => ({\r\n            secret: configService.get('JWT_SECRET', 'test-secret'),\r\n            signOptions: { expiresIn: '1h' },\r\n          }),\r\n          inject: [ConfigService],\r\n        }),\r\n        ...moduleMetadata.imports || [],\r\n      ],\r\n      controllers: moduleMetadata.controllers || [],\r\n      providers: [\r\n        ...moduleMetadata.providers || [],\r\n        ...customProviders,\r\n      ],\r\n    }).compile();\r\n\r\n    // Create application instance\r\n    this.app = this.testingModule.createNestApplication();\r\n    \r\n    // Setup global pipes, guards, and interceptors\r\n    await this.setupGlobalMiddleware();\r\n    \r\n    // Initialize application\r\n    await this.app.init();\r\n    this.httpServer = this.app.getHttpServer();\r\n\r\n    // Setup test user and authentication\r\n    await this.setupTestAuthentication();\r\n  }\r\n\r\n  /**\r\n   * Cleanup test environment after all tests\r\n   */\r\n  protected async cleanupTestEnvironment(): Promise<void> {\r\n    if (this.app) {\r\n      await this.app.close();\r\n    }\r\n    \r\n    if (this.databaseUtil) {\r\n      await this.databaseUtil.closeConnection();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Setup test data before each test\r\n   */\r\n  protected async setupTestData(): Promise<void> {\r\n    await this.databaseUtil.startTransaction();\r\n    \r\n    // Generate and seed test data\r\n    const testScenario = TestFixturesUtil.generateTestScenario('default');\r\n    await this.seedTestData(testScenario);\r\n  }\r\n\r\n  /**\r\n   * Cleanup test data after each test\r\n   */\r\n  protected async cleanupTestData(): Promise<void> {\r\n    await this.databaseUtil.rollbackTransaction();\r\n  }\r\n\r\n  /**\r\n   * Get service instance from testing module\r\n   */\r\n  protected getService<T>(serviceClass: new (...args: any[]) => T): T {\r\n    return this.testingModule.get<T>(serviceClass);\r\n  }\r\n\r\n  /**\r\n   * Get repository instance from testing module\r\n   */\r\n  protected getRepository<T>(entity: new () => T) {\r\n    return this.databaseUtil.getRepository(entity);\r\n  }\r\n\r\n  /**\r\n   * Create mock provider\r\n   */\r\n  protected createMockProvider<T>(\r\n    token: string | symbol | (new (...args: any[]) => T),\r\n    mockImplementation: Partial<T>\r\n  ): any {\r\n    return {\r\n      provide: token,\r\n      useValue: mockImplementation,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create spy provider\r\n   */\r\n  protected createSpyProvider<T>(\r\n    token: string | symbol | (new (...args: any[]) => T),\r\n    originalService: T\r\n  ): any {\r\n    const spy = jest.createMockFromModule<T>(originalService as any);\r\n    return {\r\n      provide: token,\r\n      useValue: spy,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Make authenticated HTTP request\r\n   */\r\n  protected async makeAuthenticatedRequest(\r\n    method: 'get' | 'post' | 'put' | 'delete' | 'patch',\r\n    path: string,\r\n    data?: any,\r\n    headers?: Record<string, string>\r\n  ): Promise<request.Test> {\r\n    const req = request(this.httpServer)[method](path);\r\n    \r\n    if (this.authToken) {\r\n      req.set('Authorization', `Bearer ${this.authToken}`);\r\n    }\r\n    \r\n    if (headers) {\r\n      Object.entries(headers).forEach(([key, value]) => {\r\n        req.set(key, value);\r\n      });\r\n    }\r\n    \r\n    if (data && ['post', 'put', 'patch'].includes(method)) {\r\n      req.send(data);\r\n    }\r\n    \r\n    return req;\r\n  }\r\n\r\n  /**\r\n   * Make unauthenticated HTTP request\r\n   */\r\n  protected async makeUnauthenticatedRequest(\r\n    method: 'get' | 'post' | 'put' | 'delete' | 'patch',\r\n    path: string,\r\n    data?: any,\r\n    headers?: Record<string, string>\r\n  ): Promise<request.Test> {\r\n    const req = request(this.httpServer)[method](path);\r\n    \r\n    if (headers) {\r\n      Object.entries(headers).forEach(([key, value]) => {\r\n        req.set(key, value);\r\n      });\r\n    }\r\n    \r\n    if (data && ['post', 'put', 'patch'].includes(method)) {\r\n      req.send(data);\r\n    }\r\n    \r\n    return req;\r\n  }\r\n\r\n  /**\r\n   * Wait for event emission\r\n   */\r\n  protected async waitForEvent(\r\n    eventName: string,\r\n    timeout: number = 5000\r\n  ): Promise<any> {\r\n    return new Promise((resolve, reject) => {\r\n      const timer = setTimeout(() => {\r\n        reject(new Error(`Event ${eventName} not emitted within ${timeout}ms`));\r\n      }, timeout);\r\n\r\n      const eventEmitter = this.testingModule.get('EventEmitter2');\r\n      eventEmitter.once(eventName, (data: any) => {\r\n        clearTimeout(timer);\r\n        resolve(data);\r\n      });\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Measure execution time\r\n   */\r\n  protected async measureExecutionTime<T>(\r\n    operation: () => Promise<T>\r\n  ): Promise<{ result: T; executionTime: number }> {\r\n    const startTime = Date.now();\r\n    const result = await operation();\r\n    const executionTime = Date.now() - startTime;\r\n    \r\n    return { result, executionTime };\r\n  }\r\n\r\n  /**\r\n   * Run performance benchmark\r\n   */\r\n  protected async runPerformanceBenchmark(\r\n    operation: () => Promise<any>,\r\n    iterations: number = 100\r\n  ): Promise<{\r\n    averageTime: number;\r\n    minTime: number;\r\n    maxTime: number;\r\n    totalTime: number;\r\n    iterations: number;\r\n  }> {\r\n    const times: number[] = [];\r\n    \r\n    for (let i = 0; i < iterations; i++) {\r\n      const { executionTime } = await this.measureExecutionTime(operation);\r\n      times.push(executionTime);\r\n    }\r\n    \r\n    const totalTime = times.reduce((sum, time) => sum + time, 0);\r\n    const averageTime = totalTime / iterations;\r\n    const minTime = Math.min(...times);\r\n    const maxTime = Math.max(...times);\r\n    \r\n    return {\r\n      averageTime,\r\n      minTime,\r\n      maxTime,\r\n      totalTime,\r\n      iterations,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Assert response structure\r\n   */\r\n  protected assertResponseStructure(\r\n    response: any,\r\n    expectedStructure: Record<string, any>\r\n  ): void {\r\n    for (const [key, expectedType] of Object.entries(expectedStructure)) {\r\n      expect(response).toHaveProperty(key);\r\n      \r\n      if (typeof expectedType === 'string') {\r\n        expect(typeof response[key]).toBe(expectedType);\r\n      } else if (Array.isArray(expectedType)) {\r\n        expect(Array.isArray(response[key])).toBe(true);\r\n        if (expectedType.length > 0 && response[key].length > 0) {\r\n          this.assertResponseStructure(response[key][0], expectedType[0]);\r\n        }\r\n      } else if (typeof expectedType === 'object') {\r\n        this.assertResponseStructure(response[key], expectedType);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Assert database state\r\n   */\r\n  protected async assertDatabaseState<T>(\r\n    entity: new () => T,\r\n    conditions: Partial<T>,\r\n    expectedCount: number\r\n  ): Promise<void> {\r\n    const repository = this.getRepository(entity);\r\n    const actualCount = await repository.count({ where: conditions as any });\r\n    expect(actualCount).toBe(expectedCount);\r\n  }\r\n\r\n  /**\r\n   * Assert event was emitted\r\n   */\r\n  protected async assertEventEmitted(\r\n    eventName: string,\r\n    expectedData?: any,\r\n    timeout: number = 1000\r\n  ): Promise<void> {\r\n    try {\r\n      const eventData = await this.waitForEvent(eventName, timeout);\r\n      \r\n      if (expectedData) {\r\n        expect(eventData).toMatchObject(expectedData);\r\n      }\r\n    } catch (error) {\r\n      throw new Error(`Expected event ${eventName} was not emitted`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create test data factory\r\n   */\r\n  protected createTestDataFactory<T>(entity: new () => T) {\r\n    return this.databaseUtil.createTestDataFactory(entity);\r\n  }\r\n\r\n  /**\r\n   * Private helper methods\r\n   */\r\n  private async setupGlobalMiddleware(): Promise<void> {\r\n    // Setup global validation pipe\r\n    const { ValidationPipe } = await import('@nestjs/common');\r\n    this.app.useGlobalPipes(new ValidationPipe({\r\n      transform: true,\r\n      whitelist: true,\r\n      forbidNonWhitelisted: true,\r\n    }));\r\n\r\n    // Setup global exception filter\r\n    const { HttpExceptionFilter } = await import('../../../common/filters/http-exception.filter');\r\n    this.app.useGlobalFilters(new HttpExceptionFilter());\r\n\r\n    // Setup global logging interceptor\r\n    const { LoggingInterceptor } = await import('../../../common/interceptors/logging.interceptor');\r\n    this.app.useGlobalInterceptors(new LoggingInterceptor());\r\n  }\r\n\r\n  private async setupTestAuthentication(): Promise<void> {\r\n    // Create test user\r\n    this.testUser = TestFixturesUtil.generateUser({\r\n      email: '<EMAIL>',\r\n      username: 'testuser',\r\n      role: 'admin',\r\n      isActive: true,\r\n    });\r\n\r\n    // Generate JWT token\r\n    const jwtService = this.testingModule.get('JwtService');\r\n    this.authToken = await jwtService.signAsync({\r\n      sub: this.testUser.id,\r\n      email: this.testUser.email,\r\n      role: this.testUser.role,\r\n    });\r\n  }\r\n\r\n  private async seedTestData(testScenario: any): Promise<void> {\r\n    // Seed users\r\n    if (testScenario.users) {\r\n      const userRepository = this.getRepository(Object);\r\n      // Note: This would need to be adapted based on actual entity classes\r\n      // await userRepository.save(testScenario.users);\r\n    }\r\n\r\n    // Seed other entities as needed\r\n    // This would be implemented based on the actual entity structure\r\n  }\r\n\r\n  /**\r\n   * Utility methods for common test scenarios\r\n   */\r\n  protected async createTestUser(overrides: Partial<any> = {}): Promise<any> {\r\n    const userData = TestFixturesUtil.generateUser(overrides);\r\n    // Save to database and return\r\n    return userData;\r\n  }\r\n\r\n  protected async createTestReportDefinition(overrides: Partial<any> = {}): Promise<any> {\r\n    const reportData = TestFixturesUtil.generateReportDefinition(overrides);\r\n    // Save to database and return\r\n    return reportData;\r\n  }\r\n\r\n  protected async createTestWorkflowTemplate(overrides: Partial<any> = {}): Promise<any> {\r\n    const workflowData = TestFixturesUtil.generateWorkflowTemplate(overrides);\r\n    // Save to database and return\r\n    return workflowData;\r\n  }\r\n\r\n  protected async createTestJobExecution(overrides: Partial<any> = {}): Promise<any> {\r\n    const jobData = TestFixturesUtil.generateJobExecution(overrides);\r\n    // Save to database and return\r\n    return jobData;\r\n  }\r\n\r\n  protected async createTestMetric(overrides: Partial<any> = {}): Promise<any> {\r\n    const metricData = TestFixturesUtil.generateMetric(overrides);\r\n    // Save to database and return\r\n    return metricData;\r\n  }\r\n\r\n  protected async createTestHealthCheck(overrides: Partial<any> = {}): Promise<any> {\r\n    const healthCheckData = TestFixturesUtil.generateHealthCheck(overrides);\r\n    // Save to database and return\r\n    return healthCheckData;\r\n  }\r\n\r\n  protected async createTestAlert(overrides: Partial<any> = {}): Promise<any> {\r\n    const alertData = TestFixturesUtil.generateAlert(overrides);\r\n    // Save to database and return\r\n    return alertData;\r\n  }\r\n}\r\n"], "version": 3}