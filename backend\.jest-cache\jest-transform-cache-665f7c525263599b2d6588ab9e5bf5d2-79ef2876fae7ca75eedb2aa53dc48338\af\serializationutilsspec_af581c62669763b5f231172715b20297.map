{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\utils\\serialization.utils.spec.ts", "mappings": ";AAAA;;GAEG;;AAEH,yEAIyC;AAEzC,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;IAClC,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,GAAG,GAAG;gBACV,IAAI,EAAE,MAAM;gBACZ,GAAG,EAAE,EAAE;gBACP,MAAM,EAAE,IAAI;aACb,CAAC;YAEF,MAAM,UAAU,GAAG,wCAAkB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACrD,MAAM,YAAY,GAAG,wCAAkB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAEhE,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvC,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAClC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,GAAG,GAAG;gBACV,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE;wBACP,KAAK,EAAE,kBAAkB;wBACzB,WAAW,EAAE;4BACX,KAAK,EAAE,MAAM;yBACd;qBACF;iBACF;gBACD,IAAI,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;aACxB,CAAC;YAEF,MAAM,UAAU,GAAG,wCAAkB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACrD,MAAM,YAAY,GAAG,wCAAkB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAEhE,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC5C,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACjE,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACjE,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qBAAqB,EAAE,GAAG,EAAE;YAC7B,MAAM,GAAG,GAAG;gBACV,SAAS,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;gBAC3C,SAAS,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;aAC5C,CAAC;YAEF,MAAM,UAAU,GAAG,wCAAkB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACrD,MAAM,YAAY,GAAG,wCAAkB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAEhE,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;YACvE,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sBAAsB,EAAE,GAAG,EAAE;YAC9B,MAAM,GAAG,GAAG;gBACV,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBAClB,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;gBACxB,OAAO,EAAE;oBACP,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE;oBACxB,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE;iBAC1B;aACF,CAAC;YAEF,MAAM,UAAU,GAAG,wCAAkB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACrD,MAAM,YAAY,GAAG,wCAAkB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAEhE,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAChD,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;YACtD,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC;gBACnC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE;gBACxB,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE;aAC1B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,GAAG,GAAG;gBACV,SAAS,EAAE,IAAI;gBACf,cAAc,EAAE,SAAS;gBACzB,WAAW,EAAE,EAAE;gBACf,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,KAAK;aACb,CAAC;YAEF,MAAM,OAAO,GAAyB;gBACpC,YAAY,EAAE,IAAI;gBAClB,gBAAgB,EAAE,IAAI;aACvB,CAAC;YAEF,MAAM,UAAU,GAAG,wCAAkB,CAAC,SAAS,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YAC9D,MAAM,YAAY,GAAG,wCAAkB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAEhE,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC1C,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,GAAG,GAAG;gBACV,IAAI,EAAE,MAAM;gBACZ,QAAQ,EAAE,QAAQ;gBAClB,KAAK,EAAE,kBAAkB;aAC1B,CAAC;YAEF,MAAM,OAAO,GAAyB;gBACpC,OAAO,EAAE,CAAC,UAAU,CAAC;aACtB,CAAC;YAEF,MAAM,UAAU,GAAG,wCAAkB,CAAC,SAAS,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YAC9D,MAAM,YAAY,GAAG,wCAAkB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAEhE,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACpD,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,GAAG,GAAG;gBACV,IAAI,EAAE,MAAM;gBACZ,QAAQ,EAAE,QAAQ;gBAClB,KAAK,EAAE,kBAAkB;gBACzB,GAAG,EAAE,EAAE;aACR,CAAC;YAEF,MAAM,OAAO,GAAyB;gBACpC,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;aAC3B,CAAC;YAEF,MAAM,UAAU,GAAG,wCAAkB,CAAC,SAAS,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YAC9D,MAAM,YAAY,GAAG,wCAAkB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAEhE,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACpD,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;YAC9C,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,GAAG,GAAG;gBACV,MAAM,EAAE;oBACN,MAAM,EAAE;wBACN,MAAM,EAAE;4BACN,MAAM,EAAE;gCACN,KAAK,EAAE,MAAM;6BACd;yBACF;qBACF;iBACF;aACF,CAAC;YAEF,MAAM,OAAO,GAAyB;gBACpC,QAAQ,EAAE,CAAC;aACZ,CAAC;YAEF,MAAM,UAAU,GAAG,wCAAkB,CAAC,SAAS,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YAC9D,MAAM,YAAY,GAAG,wCAAkB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAEhE,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,GAAG,GAAG;gBACV,IAAI,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBAC5B,IAAI,EAAE,MAAM;aACb,CAAC;YAEF,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAAE,CAAC;YACpC,iBAAiB,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,IAAU,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;YAE9D,MAAM,OAAO,GAAyB;gBACpC,iBAAiB;aAClB,CAAC;YAEF,MAAM,UAAU,GAAG,wCAAkB,CAAC,SAAS,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YAC9D,MAAM,YAAY,GAAG,wCAAkB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAEhE,MAAM,CAAC,OAAO,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAChD,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,GAAG,GAAG;gBACV,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,IAAI,EAAE,MAAM;aACb,CAAC;YAEF,MAAM,mBAAmB,GAAG,IAAI,GAAG,EAAE,CAAC;YACtC,mBAAmB,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,SAAiB,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YAEjF,MAAM,UAAU,GAAG,wCAAkB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACrD,MAAM,YAAY,GAAG,wCAAkB,CAAC,WAAW,CAAC,UAAU,EAAE;gBAC9D,mBAAmB;aACpB,CAAC,CAAC;YAEH,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,WAAW;gBACf,YAAmB,KAAa;oBAAb,UAAK,GAAL,KAAK,CAAQ;gBAAG,CAAC;aACrC;YAED,MAAM,GAAG,GAAG;gBACV,MAAM,EAAE,IAAI,WAAW,CAAC,MAAM,CAAC;gBAC/B,OAAO,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE;aAC3B,CAAC;YAEF,MAAM,OAAO,GAAyB;gBACpC,aAAa,EAAE,IAAI;aACpB,CAAC;YAEF,MAAM,UAAU,GAAG,wCAAkB,CAAC,SAAS,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YAC9D,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAEtC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;QACzB,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,GAAG,GAAG;gBACV,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE;oBACN,KAAK,EAAE,EAAE;oBACT,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;iBACjB;gBACD,IAAI,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;aAC7B,CAAC;YAEF,MAAM,MAAM,GAAG,wCAAkB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YAEjD,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC7B,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAEvC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,CAAC,wCAAkB,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC9D,MAAM,CAAC,wCAAkB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpD,MAAM,CAAC,wCAAkB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,wCAAkB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,wCAAkB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;QACzB,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,IAAI,GAAG;gBACX,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE;oBACN,KAAK,EAAE,EAAE;oBACT,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;iBACjB;aACF,CAAC;YAEF,MAAM,IAAI,GAAG;gBACX,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE;oBACN,KAAK,EAAE,EAAE;oBACT,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;iBACjB;aACF,CAAC;YAEF,MAAM,IAAI,GAAG;gBACX,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE;oBACN,KAAK,EAAE,EAAE;oBACT,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;iBACjB;aACF,CAAC;YAEF,MAAM,CAAC,wCAAkB,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,wCAAkB,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,CAAC,wCAAkB,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChE,MAAM,CAAC,wCAAkB,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClE,MAAM,CAAC,wCAAkB,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1D,MAAM,CAAC,wCAAkB,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,CAAC,wCAAkB,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,wCAAkB,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qBAAqB,EAAE,GAAG,EAAE;YAC7B,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC;YACrC,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC;YACrC,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC;YAErC,MAAM,CAAC,wCAAkB,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9D,MAAM,CAAC,wCAAkB,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sBAAsB,EAAE,GAAG,EAAE;YAC9B,MAAM,CAAC,wCAAkB,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtE,MAAM,CAAC,wCAAkB,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvE,MAAM,CAAC,wCAAkB,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,GAAG,GAAG;gBACV,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE;oBACP,MAAM,EAAE,aAAa;oBACrB,IAAI,EAAE,SAAS;oBACf,WAAW,EAAE;wBACX,GAAG,EAAE,OAAO;wBACZ,GAAG,EAAE,CAAC,OAAO;qBACd;iBACF;aACF,CAAC;YAEF,MAAM,SAAS,GAAG,wCAAkB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAElD,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC;gBACxB,IAAI,EAAE,MAAM;gBACZ,gBAAgB,EAAE,aAAa;gBAC/B,cAAc,EAAE,SAAS;gBACzB,yBAAyB,EAAE,OAAO;gBAClC,yBAAyB,EAAE,CAAC,OAAO;aACpC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,SAAS,GAAG;gBAChB,IAAI,EAAE,MAAM;gBACZ,gBAAgB,EAAE,aAAa;gBAC/B,cAAc,EAAE,SAAS;gBACzB,yBAAyB,EAAE,OAAO;gBAClC,yBAAyB,EAAE,CAAC,OAAO;aACpC,CAAC;YAEF,MAAM,WAAW,GAAG,wCAAkB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YAE5D,MAAM,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC;gBAC1B,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE;oBACP,MAAM,EAAE,aAAa;oBACrB,IAAI,EAAE,SAAS;oBACf,WAAW,EAAE;wBACX,GAAG,EAAE,OAAO;wBACZ,GAAG,EAAE,CAAC,OAAO;qBACd;iBACF;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,GAAG,GAAG;gBACV,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBAChB,IAAI,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;aAC7B,CAAC;YAEF,MAAM,SAAS,GAAG,wCAAkB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAElD,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,GAAG,GAAG;gBACV,IAAI,EAAE,UAAU;gBAChB,GAAG,EAAE,EAAE;gBACP,MAAM,EAAE,IAAI;gBACZ,IAAI,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;aACxB,CAAC;YAEF,MAAM,WAAW,GAAG,wCAAkB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;YAE1D,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;YACjD,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YACxC,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YAC7C,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAC/C,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,WAAW,GAAG,+DAA+D,CAAC;YAEpF,MAAM,GAAG,GAAG,wCAAkB,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAE5D,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAClC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,yCAAyC;YACrE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAChC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,GAAG,GAAG;gBACV,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE;wBACP,KAAK,EAAE,kBAAkB;qBAC1B;iBACF;aACF,CAAC;YAEF,MAAM,WAAW,GAAG,wCAAkB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;YAC1D,MAAM,MAAM,GAAG,wCAAkB,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAE/D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,GAAG,GAAG;gBACV,IAAI,EAAE,MAAM;gBACZ,SAAS,EAAE,IAAI;gBACf,cAAc,EAAE,SAAS;gBACzB,WAAW,EAAE,EAAE;aAChB,CAAC;YAEF,MAAM,WAAW,GAAG,wCAAkB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;YAE1D,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAC3C,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YAC9C,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAC/C,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,GAAG,GAAG;gBACV,IAAI,EAAE,MAAM;gBACZ,GAAG,EAAE,EAAE;gBACP,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;aAClC,CAAC;YAEF,MAAM,QAAQ,GAAG,wCAAkB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YAEpD,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1C,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACnD,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,GAAG,GAAG;gBACV,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE;wBACP,KAAK,EAAE,kBAAkB;qBAC1B;iBACF;aACF,CAAC;YAEF,MAAM,QAAQ,GAAG,wCAAkB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YAEpD,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAChD,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,QAAQ,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;YAClC,MAAM,QAAQ,GAAG;gBACf,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC;aAC9E,CAAC;YAEF,MAAM,SAAS,GAAG,wCAAkB,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAC7D,MAAM,SAAS,GAAG,wCAAkB,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAE7D,MAAM,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,GAAG,GAAG;gBACV,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC;aAC7E,CAAC;YAEF,MAAM,UAAU,GAAG,wCAAkB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACpD,MAAM,YAAY,GAAG,wCAAkB,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAE/D,MAAM,CAAC,OAAO,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACzC,MAAM,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,GAAG,GAAG;gBACV,IAAI,EAAE,MAAM;gBACZ,GAAG,EAAE,EAAE;gBACP,IAAI,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;gBACvB,OAAO,EAAE;oBACP,KAAK,EAAE,kBAAkB;iBAC1B;aACF,CAAC;YAEF,MAAM,MAAM,GAAG,wCAAkB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;YAEpD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEvD,iCAAiC;YACjC,MAAM,CAAC,wCAAkB,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAElE,0BAA0B;YAC1B,MAAM,UAAU,GAAG;gBACjB,IAAI,EAAE,GAAG,EAAE,mBAAmB;gBAC9B,GAAG,EAAE,IAAI,EAAE,mBAAmB;aAC/B,CAAC;YACF,MAAM,CAAC,wCAAkB,CAAC,cAAc,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,GAAG,GAAG;gBACV,OAAO,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;gBACzC,aAAa,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;aAChD,CAAC;YAEF,uBAAuB;YACvB,MAAM,aAAa,GAAG,wCAAkB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACxD,MAAM,eAAe,GAAG,wCAAkB,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;YACtE,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAErD,mBAAmB;YACnB,MAAM,mBAAmB,GAAG,wCAAkB,CAAC,SAAS,CAAC,GAAG,EAAE;gBAC5D,UAAU,EAAE,WAAW;aACxB,CAAC,CAAC;YACH,MAAM,qBAAqB,GAAG,wCAAkB,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC;YAClF,MAAM,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,GAAG,GAAG;gBACV,IAAI,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;aACvC,CAAC;YAEF,MAAM,eAAe,GAAG,CAAC,IAAU,EAAE,EAAE,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;YAEnE,MAAM,UAAU,GAAG,wCAAkB,CAAC,SAAS,CAAC,GAAG,EAAE;gBACnD,UAAU,EAAE,QAAQ;gBACpB,mBAAmB,EAAE,eAAe;aACrC,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,GAAG,GAAG;gBACV,SAAS,EAAE,sBAAsB;gBACjC,SAAS,EAAE,sBAAsB;gBACjC,YAAY,EAAE,sBAAsB;aACrC,CAAC;YAEF,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;YACjD,MAAM,YAAY,GAAG,wCAAkB,CAAC,WAAW,CAAC,UAAU,EAAE;gBAC9D,YAAY,EAAE,IAAI;aACnB,CAAC,CAAC;YAEH,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB;QAC1E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,GAAG,GAAG;gBACV,SAAS,EAAE,sBAAsB;gBACjC,WAAW,EAAE,sBAAsB;aACpC,CAAC;YAEF,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;YACjD,MAAM,YAAY,GAAG,wCAAkB,CAAC,WAAW,CAAC,UAAU,EAAE;gBAC9D,YAAY,EAAE,IAAI;gBAClB,UAAU,EAAE,CAAC,WAAW,CAAC;aAC1B,CAAC,CAAC;YAEH,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,YAAY,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\utils\\serialization.utils.spec.ts"], "sourcesContent": ["/**\r\n * Serialization Utils Tests\r\n */\r\n\r\nimport {\r\n  SerializationUtils,\r\n  SerializationOptions,\r\n  DeserializationOptions,\r\n} from '../../utils/serialization.utils';\r\n\r\ndescribe('SerializationUtils', () => {\r\n  describe('serialize and deserialize', () => {\r\n    it('should serialize and deserialize simple objects', () => {\r\n      const obj = {\r\n        name: '<PERSON>',\r\n        age: 30,\r\n        active: true,\r\n      };\r\n\r\n      const serialized = SerializationUtils.serialize(obj);\r\n      const deserialized = SerializationUtils.deserialize(serialized);\r\n\r\n      expect(deserialized.name).toBe('John');\r\n      expect(deserialized.age).toBe(30);\r\n      expect(deserialized.active).toBe(true);\r\n    });\r\n\r\n    it('should serialize and deserialize nested objects', () => {\r\n      const obj = {\r\n        user: {\r\n          name: '<PERSON>',\r\n          profile: {\r\n            email: '<EMAIL>',\r\n            preferences: {\r\n              theme: 'dark',\r\n            },\r\n          },\r\n        },\r\n        tags: ['admin', 'user'],\r\n      };\r\n\r\n      const serialized = SerializationUtils.serialize(obj);\r\n      const deserialized = SerializationUtils.deserialize(serialized);\r\n\r\n      expect(deserialized.user.name).toBe('<PERSON>');\r\n      expect(deserialized.user.profile.email).toBe('<EMAIL>');\r\n      expect(deserialized.user.profile.preferences.theme).toBe('dark');\r\n      expect(deserialized.tags).toEqual(['admin', 'user']);\r\n    });\r\n\r\n    it('should handle dates', () => {\r\n      const obj = {\r\n        createdAt: new Date('2023-01-01T12:00:00Z'),\r\n        updatedAt: new Date('2023-01-02T12:00:00Z'),\r\n      };\r\n\r\n      const serialized = SerializationUtils.serialize(obj);\r\n      const deserialized = SerializationUtils.deserialize(serialized);\r\n\r\n      expect(deserialized.createdAt).toBeInstanceOf(Date);\r\n      expect(deserialized.updatedAt).toBeInstanceOf(Date);\r\n      expect(deserialized.createdAt.getTime()).toBe(obj.createdAt.getTime());\r\n      expect(deserialized.updatedAt.getTime()).toBe(obj.updatedAt.getTime());\r\n    });\r\n\r\n    it('should handle arrays', () => {\r\n      const obj = {\r\n        numbers: [1, 2, 3],\r\n        strings: ['a', 'b', 'c'],\r\n        objects: [\r\n          { id: 1, name: 'First' },\r\n          { id: 2, name: 'Second' },\r\n        ],\r\n      };\r\n\r\n      const serialized = SerializationUtils.serialize(obj);\r\n      const deserialized = SerializationUtils.deserialize(serialized);\r\n\r\n      expect(deserialized.numbers).toEqual([1, 2, 3]);\r\n      expect(deserialized.strings).toEqual(['a', 'b', 'c']);\r\n      expect(deserialized.objects).toEqual([\r\n        { id: 1, name: 'First' },\r\n        { id: 2, name: 'Second' },\r\n      ]);\r\n    });\r\n\r\n    it('should handle null and undefined values', () => {\r\n      const obj = {\r\n        nullValue: null,\r\n        undefinedValue: undefined,\r\n        emptyString: '',\r\n        zero: 0,\r\n        false: false,\r\n      };\r\n\r\n      const options: SerializationOptions = {\r\n        includeNulls: true,\r\n        includeUndefined: true,\r\n      };\r\n\r\n      const serialized = SerializationUtils.serialize(obj, options);\r\n      const deserialized = SerializationUtils.deserialize(serialized);\r\n\r\n      expect(deserialized.nullValue).toBe(null);\r\n      expect(deserialized.emptyString).toBe('');\r\n      expect(deserialized.zero).toBe(0);\r\n      expect(deserialized.false).toBe(false);\r\n    });\r\n\r\n    it('should exclude fields when specified', () => {\r\n      const obj = {\r\n        name: 'John',\r\n        password: 'secret',\r\n        email: '<EMAIL>',\r\n      };\r\n\r\n      const options: SerializationOptions = {\r\n        exclude: ['password'],\r\n      };\r\n\r\n      const serialized = SerializationUtils.serialize(obj, options);\r\n      const deserialized = SerializationUtils.deserialize(serialized);\r\n\r\n      expect(deserialized.name).toBe('John');\r\n      expect(deserialized.email).toBe('<EMAIL>');\r\n      expect(deserialized.password).toBeUndefined();\r\n    });\r\n\r\n    it('should include only specified fields', () => {\r\n      const obj = {\r\n        name: 'John',\r\n        password: 'secret',\r\n        email: '<EMAIL>',\r\n        age: 30,\r\n      };\r\n\r\n      const options: SerializationOptions = {\r\n        include: ['name', 'email'],\r\n      };\r\n\r\n      const serialized = SerializationUtils.serialize(obj, options);\r\n      const deserialized = SerializationUtils.deserialize(serialized);\r\n\r\n      expect(deserialized.name).toBe('John');\r\n      expect(deserialized.email).toBe('<EMAIL>');\r\n      expect(deserialized.password).toBeUndefined();\r\n      expect(deserialized.age).toBeUndefined();\r\n    });\r\n\r\n    it('should respect max depth', () => {\r\n      const obj = {\r\n        level1: {\r\n          level2: {\r\n            level3: {\r\n              level4: {\r\n                value: 'deep',\r\n              },\r\n            },\r\n          },\r\n        },\r\n      };\r\n\r\n      const options: SerializationOptions = {\r\n        maxDepth: 2,\r\n      };\r\n\r\n      const serialized = SerializationUtils.serialize(obj, options);\r\n      const deserialized = SerializationUtils.deserialize(serialized);\r\n\r\n      expect(deserialized.level1.level2).toBe('[Max Depth Exceeded]');\r\n    });\r\n\r\n    it('should handle custom serializers', () => {\r\n      const obj = {\r\n        date: new Date('2023-01-01'),\r\n        name: 'John',\r\n      };\r\n\r\n      const customSerializers = new Map();\r\n      customSerializers.set('date', (date: Date) => date.getTime());\r\n\r\n      const options: SerializationOptions = {\r\n        customSerializers,\r\n      };\r\n\r\n      const serialized = SerializationUtils.serialize(obj, options);\r\n      const deserialized = SerializationUtils.deserialize(serialized);\r\n\r\n      expect(typeof deserialized.date).toBe('number');\r\n      expect(deserialized.date).toBe(obj.date.getTime());\r\n    });\r\n\r\n    it('should handle custom deserializers', () => {\r\n      const obj = {\r\n        timestamp: Date.now(),\r\n        name: 'John',\r\n      };\r\n\r\n      const customDeserializers = new Map();\r\n      customDeserializers.set('timestamp', (timestamp: number) => new Date(timestamp));\r\n\r\n      const serialized = SerializationUtils.serialize(obj);\r\n      const deserialized = SerializationUtils.deserialize(serialized, {\r\n        customDeserializers,\r\n      });\r\n\r\n      expect(deserialized.timestamp).toBeInstanceOf(Date);\r\n    });\r\n\r\n    it('should preserve type information when enabled', () => {\r\n      class CustomClass {\r\n        constructor(public value: string) {}\r\n      }\r\n\r\n      const obj = {\r\n        custom: new CustomClass('test'),\r\n        regular: { value: 'test' },\r\n      };\r\n\r\n      const options: SerializationOptions = {\r\n        preserveTypes: true,\r\n      };\r\n\r\n      const serialized = SerializationUtils.serialize(obj, options);\r\n      const parsed = JSON.parse(serialized);\r\n\r\n      expect(parsed.data.custom.__type).toBe('CustomClass');\r\n      expect(parsed.data.regular.__type).toBeUndefined();\r\n    });\r\n  });\r\n\r\n  describe('deepClone', () => {\r\n    it('should deep clone objects', () => {\r\n      const obj = {\r\n        name: 'John',\r\n        nested: {\r\n          value: 42,\r\n          array: [1, 2, 3],\r\n        },\r\n        date: new Date('2023-01-01'),\r\n      };\r\n\r\n      const cloned = SerializationUtils.deepClone(obj);\r\n\r\n      expect(cloned).not.toBe(obj);\r\n      expect(cloned.nested).not.toBe(obj.nested);\r\n      expect(cloned.nested.array).not.toBe(obj.nested.array);\r\n      expect(cloned.date).not.toBe(obj.date);\r\n\r\n      expect(cloned.name).toBe(obj.name);\r\n      expect(cloned.nested.value).toBe(obj.nested.value);\r\n      expect(cloned.nested.array).toEqual(obj.nested.array);\r\n      expect(cloned.date.getTime()).toBe(obj.date.getTime());\r\n    });\r\n\r\n    it('should handle primitive values', () => {\r\n      expect(SerializationUtils.deepClone('string')).toBe('string');\r\n      expect(SerializationUtils.deepClone(123)).toBe(123);\r\n      expect(SerializationUtils.deepClone(true)).toBe(true);\r\n      expect(SerializationUtils.deepClone(null)).toBe(null);\r\n      expect(SerializationUtils.deepClone(undefined)).toBe(undefined);\r\n    });\r\n  });\r\n\r\n  describe('deepEqual', () => {\r\n    it('should compare objects for deep equality', () => {\r\n      const obj1 = {\r\n        name: 'John',\r\n        nested: {\r\n          value: 42,\r\n          array: [1, 2, 3],\r\n        },\r\n      };\r\n\r\n      const obj2 = {\r\n        name: 'John',\r\n        nested: {\r\n          value: 42,\r\n          array: [1, 2, 3],\r\n        },\r\n      };\r\n\r\n      const obj3 = {\r\n        name: 'Jane',\r\n        nested: {\r\n          value: 42,\r\n          array: [1, 2, 3],\r\n        },\r\n      };\r\n\r\n      expect(SerializationUtils.deepEqual(obj1, obj2)).toBe(true);\r\n      expect(SerializationUtils.deepEqual(obj1, obj3)).toBe(false);\r\n    });\r\n\r\n    it('should handle primitive values', () => {\r\n      expect(SerializationUtils.deepEqual('test', 'test')).toBe(true);\r\n      expect(SerializationUtils.deepEqual('test', 'other')).toBe(false);\r\n      expect(SerializationUtils.deepEqual(123, 123)).toBe(true);\r\n      expect(SerializationUtils.deepEqual(123, 456)).toBe(false);\r\n      expect(SerializationUtils.deepEqual(null, null)).toBe(true);\r\n      expect(SerializationUtils.deepEqual(null, undefined)).toBe(false);\r\n    });\r\n\r\n    it('should handle dates', () => {\r\n      const date1 = new Date('2023-01-01');\r\n      const date2 = new Date('2023-01-01');\r\n      const date3 = new Date('2023-01-02');\r\n\r\n      expect(SerializationUtils.deepEqual(date1, date2)).toBe(true);\r\n      expect(SerializationUtils.deepEqual(date1, date3)).toBe(false);\r\n    });\r\n\r\n    it('should handle arrays', () => {\r\n      expect(SerializationUtils.deepEqual([1, 2, 3], [1, 2, 3])).toBe(true);\r\n      expect(SerializationUtils.deepEqual([1, 2, 3], [1, 2, 4])).toBe(false);\r\n      expect(SerializationUtils.deepEqual([1, 2, 3], [1, 2])).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('flatten and unflatten', () => {\r\n    it('should flatten nested objects', () => {\r\n      const obj = {\r\n        name: 'John',\r\n        address: {\r\n          street: '123 Main St',\r\n          city: 'Anytown',\r\n          coordinates: {\r\n            lat: 40.7128,\r\n            lng: -74.0060,\r\n          },\r\n        },\r\n      };\r\n\r\n      const flattened = SerializationUtils.flatten(obj);\r\n\r\n      expect(flattened).toEqual({\r\n        name: 'John',\r\n        'address.street': '123 Main St',\r\n        'address.city': 'Anytown',\r\n        'address.coordinates.lat': 40.7128,\r\n        'address.coordinates.lng': -74.0060,\r\n      });\r\n    });\r\n\r\n    it('should unflatten dot notation objects', () => {\r\n      const flattened = {\r\n        name: 'John',\r\n        'address.street': '123 Main St',\r\n        'address.city': 'Anytown',\r\n        'address.coordinates.lat': 40.7128,\r\n        'address.coordinates.lng': -74.0060,\r\n      };\r\n\r\n      const unflattened = SerializationUtils.unflatten(flattened);\r\n\r\n      expect(unflattened).toEqual({\r\n        name: 'John',\r\n        address: {\r\n          street: '123 Main St',\r\n          city: 'Anytown',\r\n          coordinates: {\r\n            lat: 40.7128,\r\n            lng: -74.0060,\r\n          },\r\n        },\r\n      });\r\n    });\r\n\r\n    it('should handle arrays and dates in flattening', () => {\r\n      const obj = {\r\n        items: [1, 2, 3],\r\n        date: new Date('2023-01-01'),\r\n      };\r\n\r\n      const flattened = SerializationUtils.flatten(obj);\r\n\r\n      expect(flattened.items).toEqual([1, 2, 3]);\r\n      expect(flattened.date).toBeInstanceOf(Date);\r\n    });\r\n  });\r\n\r\n  describe('query string conversion', () => {\r\n    it('should convert object to query string', () => {\r\n      const obj = {\r\n        name: 'John Doe',\r\n        age: 30,\r\n        active: true,\r\n        tags: ['admin', 'user'],\r\n      };\r\n\r\n      const queryString = SerializationUtils.toQueryString(obj);\r\n\r\n      expect(queryString).toContain('name=John%20Doe');\r\n      expect(queryString).toContain('age=30');\r\n      expect(queryString).toContain('active=true');\r\n      expect(queryString).toContain('tags[0]=admin');\r\n      expect(queryString).toContain('tags[1]=user');\r\n    });\r\n\r\n    it('should parse query string to object', () => {\r\n      const queryString = 'name=John%20Doe&age=30&active=true&tags[0]=admin&tags[1]=user';\r\n\r\n      const obj = SerializationUtils.fromQueryString(queryString);\r\n\r\n      expect(obj.name).toBe('John Doe');\r\n      expect(obj.age).toBe('30'); // Query string values are always strings\r\n      expect(obj.active).toBe('true');\r\n      expect(obj.tags).toEqual(['admin', 'user']);\r\n    });\r\n\r\n    it('should handle nested objects in query strings', () => {\r\n      const obj = {\r\n        user: {\r\n          name: 'John',\r\n          profile: {\r\n            email: '<EMAIL>',\r\n          },\r\n        },\r\n      };\r\n\r\n      const queryString = SerializationUtils.toQueryString(obj);\r\n      const parsed = SerializationUtils.fromQueryString(queryString);\r\n\r\n      expect(parsed.user.name).toBe('John');\r\n      expect(parsed.user.profile.email).toBe('<EMAIL>');\r\n    });\r\n\r\n    it('should skip null and undefined values in query strings', () => {\r\n      const obj = {\r\n        name: 'John',\r\n        nullValue: null,\r\n        undefinedValue: undefined,\r\n        emptyString: '',\r\n      };\r\n\r\n      const queryString = SerializationUtils.toQueryString(obj);\r\n\r\n      expect(queryString).toContain('name=John');\r\n      expect(queryString).toContain('emptyString=');\r\n      expect(queryString).not.toContain('nullValue');\r\n      expect(queryString).not.toContain('undefinedValue');\r\n    });\r\n  });\r\n\r\n  describe('FormData conversion', () => {\r\n    it('should convert object to FormData', () => {\r\n      const obj = {\r\n        name: 'John',\r\n        age: 30,\r\n        files: ['file1.txt', 'file2.txt'],\r\n      };\r\n\r\n      const formData = SerializationUtils.toFormData(obj);\r\n\r\n      expect(formData.get('name')).toBe('John');\r\n      expect(formData.get('age')).toBe('30');\r\n      expect(formData.get('files[0]')).toBe('file1.txt');\r\n      expect(formData.get('files[1]')).toBe('file2.txt');\r\n    });\r\n\r\n    it('should handle nested objects in FormData', () => {\r\n      const obj = {\r\n        user: {\r\n          name: 'John',\r\n          profile: {\r\n            email: '<EMAIL>',\r\n          },\r\n        },\r\n      };\r\n\r\n      const formData = SerializationUtils.toFormData(obj);\r\n\r\n      expect(formData.get('user[name]')).toBe('John');\r\n      expect(formData.get('user[profile][email]')).toBe('<EMAIL>');\r\n    });\r\n  });\r\n\r\n  describe('utility functions', () => {\r\n    it('should calculate object size', () => {\r\n      const smallObj = { name: 'John' };\r\n      const largeObj = {\r\n        data: Array.from({ length: 1000 }, (_, i) => ({ id: i, value: `item-${i}` })),\r\n      };\r\n\r\n      const smallSize = SerializationUtils.getObjectSize(smallObj);\r\n      const largeSize = SerializationUtils.getObjectSize(largeObj);\r\n\r\n      expect(smallSize).toBeGreaterThan(0);\r\n      expect(largeSize).toBeGreaterThan(smallSize);\r\n    });\r\n\r\n    it('should compress and decompress objects', () => {\r\n      const obj = {\r\n        name: 'John',\r\n        data: Array.from({ length: 100 }, (_, i) => ({ id: i, value: `item-${i}` })),\r\n      };\r\n\r\n      const compressed = SerializationUtils.compress(obj);\r\n      const decompressed = SerializationUtils.decompress(compressed);\r\n\r\n      expect(typeof compressed).toBe('string');\r\n      expect(decompressed).toEqual(obj);\r\n    });\r\n\r\n    it('should create and validate schemas', () => {\r\n      const obj = {\r\n        name: 'John',\r\n        age: 30,\r\n        tags: ['admin', 'user'],\r\n        profile: {\r\n          email: '<EMAIL>',\r\n        },\r\n      };\r\n\r\n      const schema = SerializationUtils.createSchema(obj);\r\n\r\n      expect(schema.type).toBe('object');\r\n      expect(schema.properties?.name.type).toBe('string');\r\n      expect(schema.properties?.age.type).toBe('number');\r\n      expect(schema.properties?.tags.type).toBe('array');\r\n      expect(schema.properties?.tags.elementType).toBe('string');\r\n      expect(schema.properties?.profile.type).toBe('object');\r\n\r\n      // Validate object against schema\r\n      expect(SerializationUtils.validateSchema(obj, schema)).toBe(true);\r\n\r\n      // Validate invalid object\r\n      const invalidObj = {\r\n        name: 123, // Should be string\r\n        age: '30', // Should be number\r\n      };\r\n      expect(SerializationUtils.validateSchema(invalidObj, schema)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('date handling', () => {\r\n    it('should serialize dates in different formats', () => {\r\n      const obj = {\r\n        isoDate: new Date('2023-01-01T12:00:00Z'),\r\n        timestampDate: new Date('2023-01-01T12:00:00Z'),\r\n      };\r\n\r\n      // ISO format (default)\r\n      const isoSerialized = SerializationUtils.serialize(obj);\r\n      const isoDeserialized = SerializationUtils.deserialize(isoSerialized);\r\n      expect(isoDeserialized.isoDate).toBeInstanceOf(Date);\r\n\r\n      // Timestamp format\r\n      const timestampSerialized = SerializationUtils.serialize(obj, {\r\n        dateFormat: 'timestamp',\r\n      });\r\n      const timestampDeserialized = SerializationUtils.deserialize(timestampSerialized);\r\n      expect(timestampDeserialized.timestampDate).toBeInstanceOf(Date);\r\n    });\r\n\r\n    it('should handle custom date formatter', () => {\r\n      const obj = {\r\n        date: new Date('2023-01-01T12:00:00Z'),\r\n      };\r\n\r\n      const customFormatter = (date: Date) => `custom-${date.getTime()}`;\r\n\r\n      const serialized = SerializationUtils.serialize(obj, {\r\n        dateFormat: 'custom',\r\n        customDateFormatter: customFormatter,\r\n      });\r\n\r\n      const parsed = JSON.parse(serialized);\r\n      expect(parsed.data.date).toMatch(/^custom-\\d+$/);\r\n    });\r\n\r\n    it('should restore dates based on field names', () => {\r\n      const obj = {\r\n        createdAt: '2023-01-01T12:00:00Z',\r\n        updatedAt: '2023-01-02T12:00:00Z',\r\n        regularField: '2023-01-03T12:00:00Z',\r\n      };\r\n\r\n      const serialized = JSON.stringify({ data: obj });\r\n      const deserialized = SerializationUtils.deserialize(serialized, {\r\n        restoreDates: true,\r\n      });\r\n\r\n      expect(deserialized.createdAt).toBeInstanceOf(Date);\r\n      expect(deserialized.updatedAt).toBeInstanceOf(Date);\r\n      expect(deserialized.regularField).toBeInstanceOf(Date); // Auto-detected\r\n    });\r\n\r\n    it('should restore specific date fields', () => {\r\n      const obj = {\r\n        dateField: '2023-01-01T12:00:00Z',\r\n        stringField: '2023-01-02T12:00:00Z',\r\n      };\r\n\r\n      const serialized = JSON.stringify({ data: obj });\r\n      const deserialized = SerializationUtils.deserialize(serialized, {\r\n        restoreDates: true,\r\n        dateFields: ['dateField'],\r\n      });\r\n\r\n      expect(deserialized.dateField).toBeInstanceOf(Date);\r\n      expect(typeof deserialized.stringField).toBe('string');\r\n    });\r\n  });\r\n});"], "version": 3}