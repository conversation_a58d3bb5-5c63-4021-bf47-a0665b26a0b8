{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\repositories\\threat.repository.ts", "mappings": "", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\repositories\\threat.repository.ts"], "sourcesContent": ["import { AggregateRepository, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { ThreatSeverity } from '../enums/threat-severity.enum';\r\nimport { ConfidenceLevel } from '../enums/confidence-level.enum';\r\n\r\n/**\r\n * Threat Entity Interface (placeholder until actual entity is implemented)\r\n * This interface defines the expected structure of the Threat entity\r\n */\r\nexport interface Threat {\r\n  id: UniqueEntityId;\r\n  signature: string;\r\n  category: string;\r\n  type: string;\r\n  severity: ThreatSeverity;\r\n  confidence: ConfidenceLevel;\r\n  riskScore: number;\r\n  sourceIp?: string;\r\n  targetIp?: string;\r\n  tags: string[];\r\n  indicators: string[];\r\n  mitreTechniques: string[];\r\n  description?: string;\r\n  isActive(): boolean;\r\n  isResolved(): boolean;\r\n  isHighSeverity(): boolean;\r\n  isCritical(): boolean;\r\n  isHighRisk(): boolean;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n  resolvedAt?: Date;\r\n}\r\n\r\n/**\r\n * Threat Repository Interface\r\n * \r\n * Provides specialized data access methods for Threat aggregates.\r\n * Focuses on threat detection, analysis, and management operations.\r\n * \r\n * Key responsibilities:\r\n * - Threat persistence and retrieval\r\n * - Threat intelligence integration\r\n * - Attack pattern analysis\r\n * - Risk-based threat prioritization\r\n * - Threat lifecycle management\r\n */\r\nexport interface ThreatRepository extends AggregateRepository<Threat, UniqueEntityId> {\r\n  /**\r\n   * Find threats by severity level\r\n   * Critical for priority-based threat management\r\n   * \r\n   * @param severity The severity level to filter by\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to threats with the specified severity\r\n   */\r\n  findBySeverity(severity: ThreatSeverity, limit?: number): Promise<Threat[]>;\r\n\r\n  /**\r\n   * Find threats by multiple severity levels\r\n   * Useful for filtering high-priority threats (HIGH, CRITICAL)\r\n   * \r\n   * @param severities Array of severity levels to include\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to threats with any of the specified severities\r\n   */\r\n  findBySeverities(severities: ThreatSeverity[], limit?: number): Promise<Threat[]>;\r\n\r\n  /**\r\n   * Find high severity threats (HIGH and CRITICAL)\r\n   * Optimized query for high-priority threat management\r\n   * \r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to high severity threats\r\n   */\r\n  findHighSeverityThreats(limit?: number): Promise<Threat[]>;\r\n\r\n  /**\r\n   * Find critical threats only\r\n   * Most urgent threats requiring immediate attention\r\n   * \r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to critical threats\r\n   */\r\n  findCriticalThreats(limit?: number): Promise<Threat[]>;\r\n\r\n  /**\r\n   * Find active threats (not resolved)\r\n   * Current threats requiring attention\r\n   * \r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to active threats\r\n   */\r\n  findActiveThreats(limit?: number): Promise<Threat[]>;\r\n\r\n  /**\r\n   * Find resolved threats\r\n   * Historical threats for analysis and reporting\r\n   * \r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to resolved threats\r\n   */\r\n  findResolvedThreats(limit?: number): Promise<Threat[]>;\r\n\r\n  /**\r\n   * Find threats with high risk scores\r\n   * Risk-based threat prioritization\r\n   * \r\n   * @param minRiskScore Minimum risk score threshold (default: 70)\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to high-risk threats\r\n   */\r\n  findHighRiskThreats(minRiskScore?: number, limit?: number): Promise<Threat[]>;\r\n\r\n  /**\r\n   * Find threats with high confidence levels\r\n   * Quality-based threat filtering\r\n   * \r\n   * @param minConfidence Minimum confidence threshold (default: 80)\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to high-confidence threats\r\n   */\r\n  findHighConfidenceThreats(minConfidence?: number, limit?: number): Promise<Threat[]>;\r\n\r\n  /**\r\n   * Find recent threats within a time window\r\n   * Real-time threat monitoring\r\n   * \r\n   * @param withinHours Time window in hours (default: 24)\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to recent threats\r\n   */\r\n  findRecentThreats(withinHours?: number, limit?: number): Promise<Threat[]>;\r\n\r\n  /**\r\n   * Find stale threats that haven't been updated\r\n   * Identifies threats that may need attention\r\n   * \r\n   * @param olderThanHours Age threshold in hours (default: 168 = 7 days)\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to stale threats\r\n   */\r\n  findStaleThreats(olderThanHours?: number, limit?: number): Promise<Threat[]>;\r\n\r\n  /**\r\n   * Find threats by category\r\n   * Categorical threat analysis\r\n   * \r\n   * @param categories Array of threat categories\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to threats in the specified categories\r\n   */\r\n  findByCategories(categories: string[], limit?: number): Promise<Threat[]>;\r\n\r\n  /**\r\n   * Find threats by type\r\n   * Type-specific threat analysis\r\n   * \r\n   * @param types Array of threat types\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to threats of the specified types\r\n   */\r\n  findByTypes(types: string[], limit?: number): Promise<Threat[]>;\r\n\r\n  /**\r\n   * Find threats by tags\r\n   * Tag-based threat filtering and categorization\r\n   * \r\n   * @param tags Array of tags to search for\r\n   * @param matchAll Whether to match all tags (AND) or any tag (OR)\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to threats with the specified tags\r\n   */\r\n  findByTags(tags: string[], matchAll?: boolean, limit?: number): Promise<Threat[]>;\r\n\r\n  /**\r\n   * Find threats by indicators\r\n   * IOC-based threat detection and analysis\r\n   * \r\n   * @param indicators Array of threat indicators\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to threats with the specified indicators\r\n   */\r\n  findByIndicators(indicators: string[], limit?: number): Promise<Threat[]>;\r\n\r\n  /**\r\n   * Find threats by MITRE techniques\r\n   * ATT&CK framework-based threat analysis\r\n   * \r\n   * @param techniques Array of MITRE technique IDs\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to threats using the specified techniques\r\n   */\r\n  findByMitreTechniques(techniques: string[], limit?: number): Promise<Threat[]>;\r\n\r\n  /**\r\n   * Find threats by source IP\r\n   * Source-based threat analysis\r\n   * \r\n   * @param sourceIp The source IP address\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to threats from the specified source IP\r\n   */\r\n  findBySourceIp(sourceIp: string, limit?: number): Promise<Threat[]>;\r\n\r\n  /**\r\n   * Find threats by target IP\r\n   * Target-based threat analysis\r\n   * \r\n   * @param targetIp The target IP address\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to threats targeting the specified IP\r\n   */\r\n  findByTargetIp(targetIp: string, limit?: number): Promise<Threat[]>;\r\n\r\n  /**\r\n   * Find threats within a risk score range\r\n   * Risk-based threat filtering\r\n   * \r\n   * @param minScore Minimum risk score\r\n   * @param maxScore Maximum risk score\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to threats within the risk score range\r\n   */\r\n  findByRiskScoreRange(minScore?: number, maxScore?: number, limit?: number): Promise<Threat[]>;\r\n\r\n  /**\r\n   * Count threats by severity\r\n   * Severity distribution analysis\r\n   * \r\n   * @returns Promise resolving to severity counts\r\n   */\r\n  countBySeverity(): Promise<Record<ThreatSeverity, number>>;\r\n\r\n  /**\r\n   * Count threats by category\r\n   * Category distribution analysis\r\n   * \r\n   * @returns Promise resolving to category counts\r\n   */\r\n  countByCategory(): Promise<Record<string, number>>;\r\n\r\n  /**\r\n   * Count threats by type\r\n   * Type distribution analysis\r\n   * \r\n   * @returns Promise resolving to type counts\r\n   */\r\n  countByType(): Promise<Record<string, number>>;\r\n\r\n  /**\r\n   * Get threat statistics for a time period\r\n   * Comprehensive threat metrics and analysis\r\n   * \r\n   * @param startTime Start of the time period\r\n   * @param endTime End of the time period\r\n   * @returns Promise resolving to threat statistics\r\n   */\r\n  getThreatStatistics(startTime: Date, endTime: Date): Promise<{\r\n    totalThreats: number;\r\n    activeThreats: number;\r\n    resolvedThreats: number;\r\n    threatsBySeverity: Record<ThreatSeverity, number>;\r\n    threatsByCategory: Record<string, number>;\r\n    threatsByType: Record<string, number>;\r\n    averageRiskScore: number;\r\n    highRiskThreatCount: number;\r\n    criticalThreatCount: number;\r\n    averageResolutionTime: number;\r\n    topIndicators: Array<{ indicator: string; count: number }>;\r\n    topMitreTechniques: Array<{ technique: string; count: number }>;\r\n    sourceIpDistribution: Record<string, number>;\r\n    targetIpDistribution: Record<string, number>;\r\n  }>;\r\n\r\n  /**\r\n   * Find threats for investigation\r\n   * Selects threats requiring security analyst attention\r\n   * \r\n   * @param criteria Investigation criteria\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to threats requiring investigation\r\n   */\r\n  findForInvestigation(criteria?: {\r\n    minSeverity?: ThreatSeverity;\r\n    minRiskScore?: number;\r\n    minConfidence?: number;\r\n    categories?: string[];\r\n    types?: string[];\r\n    timeRange?: { start: Date; end: Date };\r\n  }, limit?: number): Promise<Threat[]>;\r\n\r\n  /**\r\n   * Find emerging threats\r\n   * Identifies new or evolving threat patterns\r\n   * \r\n   * @param timeWindow Time window for emergence detection in hours\r\n   * @param minOccurrences Minimum occurrences to be considered emerging\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to emerging threats\r\n   */\r\n  findEmergingThreats(timeWindow?: number, minOccurrences?: number, limit?: number): Promise<Threat[]>;\r\n\r\n  /**\r\n   * Find threat trends\r\n   * Analyzes threat patterns over time\r\n   * \r\n   * @param timeWindow Time window for trend analysis in days\r\n   * @returns Promise resolving to threat trend data\r\n   */\r\n  findThreatTrends(timeWindow?: number): Promise<Array<{\r\n    date: Date;\r\n    threatCount: number;\r\n    severityDistribution: Record<ThreatSeverity, number>;\r\n    topCategories: Array<{ category: string; count: number }>;\r\n    riskScoreAverage: number;\r\n  }>>;\r\n\r\n  /**\r\n   * Archive old resolved threats\r\n   * Moves old resolved threats to archive storage\r\n   * \r\n   * @param olderThanDays Age threshold in days\r\n   * @param batchSize Number of threats to archive per batch\r\n   * @returns Promise resolving to the number of archived threats\r\n   */\r\n  archiveOldThreats(olderThanDays: number, batchSize?: number): Promise<number>;\r\n\r\n  /**\r\n   * Bulk update threat status\r\n   * Efficient batch operation for threat management\r\n   * \r\n   * @param threatIds Array of threat IDs to update\r\n   * @param updates Partial threat data to update\r\n   * @returns Promise resolving to the number of updated threats\r\n   */\r\n  bulkUpdateThreats(\r\n    threatIds: UniqueEntityId[],\r\n    updates: Partial<Pick<Threat, 'severity' | 'riskScore' | 'tags'>>\r\n  ): Promise<number>;\r\n}"], "version": 3}