{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\encryption\\hash.service.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,2CAA+C;AAC/C,+CAAiC;AACjC,+CAAiC;AAmB1B,IAAM,WAAW,GAAjB,MAAM,WAAW;IAKtB,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAJxC,qBAAgB,GAAG,QAAQ,CAAC;QAC5B,oBAAe,GAAmB,KAAK,CAAC;QACxC,sBAAiB,GAAG,EAAE,CAAC;IAEoB,CAAC;IAE7D;;OAEG;IACH,IAAI,CAAC,IAAY,EAAE,UAAuB,EAAE;QAC1C,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,gBAAgB,CAAC;QAC7D,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,eAAe,CAAC;QAE1D,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAElB,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,IAAY,EAAE,WAA2B,IAAI,CAAC,eAAe;QAClE,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,IAAY,EAAE,WAA2B,IAAI,CAAC,eAAe;QAClE,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,IAAI,CAAC,IAAY,EAAE,WAA2B,IAAI,CAAC,eAAe;QAChE,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACH,GAAG,CAAC,IAAY,EAAE,WAA2B,IAAI,CAAC,eAAe;QAC/D,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,IAAI,CAAC,IAAY,EAAE,GAAW,EAAE,UAAuB,EAAE;QACvD,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,gBAAgB,CAAC;QAC7D,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,eAAe,CAAC;QAE1D,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;QAC/C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAElB,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,IAAY,EAAE,GAAY;QACnC,MAAM,SAAS,GAAG,GAAG,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,iBAAiB,CAAC,CAAC;QAC3E,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAChE,CAAC;QAED,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,IAAY,EAAE,GAAY;QACnC,MAAM,SAAS,GAAG,GAAG,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,iBAAiB,CAAC,CAAC;QAC3E,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAChE,CAAC;QAED,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,UAA+B,EAAE;QACpE,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,iBAAiB,CAAC;QAEhE,IAAI,CAAC;YACH,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,4BAA4B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,QAAgB,EAAE,IAAY;QACjD,IAAI,CAAC;YACH,OAAO,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,iCAAiC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,SAAiB,IAAI,CAAC,iBAAiB;QACxD,IAAI,CAAC;YACH,OAAO,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACtC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,2BAA2B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CACJ,QAAgB,EAChB,IAAY,EACZ,aAAqB,MAAM,EAC3B,YAAoB,EAAE,EACtB,SAAiB,QAAQ;QAEzB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,GAAG,EAAE,UAAU,EAAE,EAAE;gBAC/E,IAAI,GAAG,EAAE,CAAC;oBACR,MAAM,CAAC,IAAI,KAAK,CAAC,0BAA0B,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;gBAC7D,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;gBACtC,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CACJ,QAAgB,EAChB,IAAY,EACZ,YAAoB,EAAE,EACtB,UAAgC,EAAE;QAElC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,GAAG,EAAE,UAAU,EAAE,EAAE;gBACpE,IAAI,GAAG,EAAE,CAAC;oBACR,MAAM,CAAC,IAAI,KAAK,CAAC,0BAA0B,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;gBAC7D,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;gBACtC,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,SAAiB,EAAE;QACpC,OAAO,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,IAAY,EAAE,SAAiB,EAAE,GAAW,EAAE,YAAoB,QAAQ;QACnF,MAAM,iBAAiB,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;QAE9D,uDAAuD;QACvD,OAAO,MAAM,CAAC,eAAe,CAC3B,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,EAC7B,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,KAAK,CAAC,CACtC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,QAAgB,EAAE,YAAoB,QAAQ;QAC3D,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YAC1C,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;YACzB,MAAM,MAAM,GAAG,EAAE,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAE7C,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE;gBACjC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;gBACpB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE;gBAClC,MAAM,CAAC,IAAI,KAAK,CAAC,wBAAwB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAC7D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,IAAY,EAAE,YAAoB,QAAQ;QACzD,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,IAAY,EAAE,gBAAwB,EAAE,YAAoB,QAAQ;QACjF,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAE9D,6BAA6B;QAC7B,OAAO,MAAM,CAAC,eAAe,CAC3B,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,EAClC,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,CACrC,CAAC;IACJ,CAAC;CACF,CAAA;AA/NY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;yDAMiC,sBAAa,oBAAb,sBAAa;GAL9C,WAAW,CA+NvB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\encryption\\hash.service.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport * as crypto from 'crypto';\r\nimport * as bcrypt from 'bcrypt';\r\n\r\nexport interface HashOptions {\r\n  algorithm?: string;\r\n  encoding?: BufferEncoding;\r\n  salt?: string;\r\n  iterations?: number;\r\n}\r\n\r\nexport interface PasswordHashOptions {\r\n  saltRounds?: number;\r\n}\r\n\r\nexport interface HMACOptions {\r\n  algorithm?: string;\r\n  encoding?: BufferEncoding;\r\n}\r\n\r\n@Injectable()\r\nexport class HashService {\r\n  private readonly defaultAlgorithm = 'sha256';\r\n  private readonly defaultEncoding: BufferEncoding = 'hex';\r\n  private readonly defaultSaltRounds = 12;\r\n\r\n  constructor(private readonly configService: ConfigService) {}\r\n\r\n  /**\r\n   * Hash data using specified algorithm\r\n   */\r\n  hash(data: string, options: HashOptions = {}): string {\r\n    const algorithm = options.algorithm || this.defaultAlgorithm;\r\n    const encoding = options.encoding || this.defaultEncoding;\r\n\r\n    const hash = crypto.createHash(algorithm);\r\n    hash.update(data);\r\n    \r\n    return hash.digest(encoding);\r\n  }\r\n\r\n  /**\r\n   * Hash data with SHA-256\r\n   */\r\n  sha256(data: string, encoding: BufferEncoding = this.defaultEncoding): string {\r\n    return this.hash(data, { algorithm: 'sha256', encoding });\r\n  }\r\n\r\n  /**\r\n   * Hash data with SHA-512\r\n   */\r\n  sha512(data: string, encoding: BufferEncoding = this.defaultEncoding): string {\r\n    return this.hash(data, { algorithm: 'sha512', encoding });\r\n  }\r\n\r\n  /**\r\n   * Hash data with SHA-1 (deprecated, use only for legacy compatibility)\r\n   */\r\n  sha1(data: string, encoding: BufferEncoding = this.defaultEncoding): string {\r\n    return this.hash(data, { algorithm: 'sha1', encoding });\r\n  }\r\n\r\n  /**\r\n   * Hash data with MD5 (deprecated, use only for legacy compatibility)\r\n   */\r\n  md5(data: string, encoding: BufferEncoding = this.defaultEncoding): string {\r\n    return this.hash(data, { algorithm: 'md5', encoding });\r\n  }\r\n\r\n  /**\r\n   * Create HMAC (Hash-based Message Authentication Code)\r\n   */\r\n  hmac(data: string, key: string, options: HMACOptions = {}): string {\r\n    const algorithm = options.algorithm || this.defaultAlgorithm;\r\n    const encoding = options.encoding || this.defaultEncoding;\r\n\r\n    const hmac = crypto.createHmac(algorithm, key);\r\n    hmac.update(data);\r\n    \r\n    return hmac.digest(encoding);\r\n  }\r\n\r\n  /**\r\n   * Create HMAC-SHA256\r\n   */\r\n  hmacSha256(data: string, key?: string): string {\r\n    const secretKey = key || this.configService.get<string>('HMAC_SECRET_KEY');\r\n    if (!secretKey) {\r\n      throw new Error('HMAC secret key not found in configuration');\r\n    }\r\n    \r\n    return this.hmac(data, secretKey, { algorithm: 'sha256' });\r\n  }\r\n\r\n  /**\r\n   * Create HMAC-SHA512\r\n   */\r\n  hmacSha512(data: string, key?: string): string {\r\n    const secretKey = key || this.configService.get<string>('HMAC_SECRET_KEY');\r\n    if (!secretKey) {\r\n      throw new Error('HMAC secret key not found in configuration');\r\n    }\r\n    \r\n    return this.hmac(data, secretKey, { algorithm: 'sha512' });\r\n  }\r\n\r\n  /**\r\n   * Hash password using bcrypt\r\n   */\r\n  async hashPassword(password: string, options: PasswordHashOptions = {}): Promise<string> {\r\n    const saltRounds = options.saltRounds || this.defaultSaltRounds;\r\n    \r\n    try {\r\n      return await bcrypt.hash(password, saltRounds);\r\n    } catch (error) {\r\n      throw new Error(`Password hashing failed: ${error.message}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Verify password against bcrypt hash\r\n   */\r\n  async verifyPassword(password: string, hash: string): Promise<boolean> {\r\n    try {\r\n      return await bcrypt.compare(password, hash);\r\n    } catch (error) {\r\n      throw new Error(`Password verification failed: ${error.message}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Generate salt for password hashing\r\n   */\r\n  async generateSalt(rounds: number = this.defaultSaltRounds): Promise<string> {\r\n    try {\r\n      return await bcrypt.genSalt(rounds);\r\n    } catch (error) {\r\n      throw new Error(`Salt generation failed: ${error.message}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Hash data with PBKDF2 (Password-Based Key Derivation Function 2)\r\n   */\r\n  pbkdf2(\r\n    password: string, \r\n    salt: string, \r\n    iterations: number = 100000, \r\n    keyLength: number = 64, \r\n    digest: string = 'sha256'\r\n  ): Promise<string> {\r\n    return new Promise((resolve, reject) => {\r\n      crypto.pbkdf2(password, salt, iterations, keyLength, digest, (err, derivedKey) => {\r\n        if (err) {\r\n          reject(new Error(`PBKDF2 hashing failed: ${err.message}`));\r\n        } else {\r\n          resolve(derivedKey.toString('hex'));\r\n        }\r\n      });\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Hash data with scrypt\r\n   */\r\n  scrypt(\r\n    password: string, \r\n    salt: string, \r\n    keyLength: number = 64, \r\n    options: crypto.ScryptOptions = {}\r\n  ): Promise<string> {\r\n    return new Promise((resolve, reject) => {\r\n      crypto.scrypt(password, salt, keyLength, options, (err, derivedKey) => {\r\n        if (err) {\r\n          reject(new Error(`Scrypt hashing failed: ${err.message}`));\r\n        } else {\r\n          resolve(derivedKey.toString('hex'));\r\n        }\r\n      });\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Generate cryptographically secure random salt\r\n   */\r\n  generateRandomSalt(length: number = 32): string {\r\n    return crypto.randomBytes(length).toString('hex');\r\n  }\r\n\r\n  /**\r\n   * Verify HMAC\r\n   */\r\n  verifyHmac(data: string, signature: string, key: string, algorithm: string = 'sha256'): boolean {\r\n    const expectedSignature = this.hmac(data, key, { algorithm });\r\n    \r\n    // Use timing-safe comparison to prevent timing attacks\r\n    return crypto.timingSafeEqual(\r\n      Buffer.from(signature, 'hex'),\r\n      Buffer.from(expectedSignature, 'hex')\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Hash file content\r\n   */\r\n  async hashFile(filePath: string, algorithm: string = 'sha256'): Promise<string> {\r\n    return new Promise((resolve, reject) => {\r\n      const hash = crypto.createHash(algorithm);\r\n      const fs = require('fs');\r\n      const stream = fs.createReadStream(filePath);\r\n\r\n      stream.on('data', (data: Buffer) => {\r\n        hash.update(data);\r\n      });\r\n\r\n      stream.on('end', () => {\r\n        resolve(hash.digest('hex'));\r\n      });\r\n\r\n      stream.on('error', (error: Error) => {\r\n        reject(new Error(`File hashing failed: ${error.message}`));\r\n      });\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Generate checksum for data integrity verification\r\n   */\r\n  generateChecksum(data: string, algorithm: string = 'sha256'): string {\r\n    return this.hash(data, { algorithm });\r\n  }\r\n\r\n  /**\r\n   * Verify data integrity using checksum\r\n   */\r\n  verifyChecksum(data: string, expectedChecksum: string, algorithm: string = 'sha256'): boolean {\r\n    const actualChecksum = this.generateChecksum(data, algorithm);\r\n    \r\n    // Use timing-safe comparison\r\n    return crypto.timingSafeEqual(\r\n      Buffer.from(actualChecksum, 'hex'),\r\n      Buffer.from(expectedChecksum, 'hex')\r\n    );\r\n  }\r\n}"], "version": 3}