d844f6ffdf2c9c1cfed4ded8059cbd22
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const security_event_entity_1 = require("./security-event.entity");
const event_metadata_value_object_1 = require("../../value-objects/event-metadata/event-metadata.value-object");
const event_timestamp_value_object_1 = require("../../value-objects/event-metadata/event-timestamp.value-object");
const event_source_value_object_1 = require("../../value-objects/event-metadata/event-source.value-object");
const event_processing_status_enum_1 = require("../../enums/event-processing-status.enum");
const event_source_type_enum_1 = require("../../enums/event-source-type.enum");
const security_event_created_event_1 = require("../../events/security-event-created.event");
const security_event_status_changed_event_1 = require("../../events/security-event-status-changed.event");
describe('SecurityEvent Entity', () => {
    let metadata;
    let rawData;
    beforeEach(() => {
        const timestamp = event_timestamp_value_object_1.EventTimestamp.create();
        const source = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.FIREWALL, 'fw-001');
        metadata = event_metadata_value_object_1.EventMetadata.create(timestamp, source);
        rawData = {
            sourceIp: '*************',
            destinationIp: '*********',
            port: 443,
            protocol: 'TCP',
            action: 'BLOCK',
        };
    });
    describe('Creation', () => {
        it('should create a new security event', () => {
            const event = security_event_entity_1.SecurityEvent.create(metadata, rawData, 'Firewall blocked connection', {
                description: 'Suspicious connection attempt blocked',
                category: 'network',
                subcategory: 'firewall',
                tags: ['blocked', 'suspicious'],
                attributes: { severity: 'medium' },
            });
            expect(event.title).toBe('Firewall blocked connection');
            expect(event.description).toBe('Suspicious connection attempt blocked');
            expect(event.category).toBe('network');
            expect(event.subcategory).toBe('firewall');
            expect(event.status).toBe(event_processing_status_enum_1.EventProcessingStatus.RAW);
            expect(event.tags).toEqual(['blocked', 'suspicious']);
            expect(event.getAttribute('severity')).toBe('medium');
            expect(event.processingAttempts).toBe(0);
        });
        it('should publish SecurityEventCreatedEvent on creation', () => {
            const event = security_event_entity_1.SecurityEvent.create(metadata, rawData, 'Test Event');
            const domainEvents = event.getDomainEvents();
            expect(domainEvents).toHaveLength(1);
            expect(domainEvents[0]).toBeInstanceOf(security_event_created_event_1.SecurityEventCreatedEvent);
            const createdEvent = domainEvents[0];
            expect(createdEvent.eventTitle).toBe('Test Event');
            expect(createdEvent.sourceType).toBe(event_source_type_enum_1.EventSourceType.FIREWALL);
        });
        it('should validate required fields', () => {
            expect(() => security_event_entity_1.SecurityEvent.create(metadata, {}, 'Test')).toThrow('Security event must have raw data');
            expect(() => security_event_entity_1.SecurityEvent.create(metadata, rawData, '')).toThrow('Security event must have a title');
            expect(() => security_event_entity_1.SecurityEvent.create(metadata, rawData, '   ')).toThrow('Security event must have a title');
        });
        it('should validate title length', () => {
            const longTitle = 'a'.repeat(256);
            expect(() => security_event_entity_1.SecurityEvent.create(metadata, rawData, longTitle)).toThrow('Event title cannot exceed 255 characters');
        });
        it('should validate description length', () => {
            const longDescription = 'a'.repeat(2001);
            expect(() => security_event_entity_1.SecurityEvent.create(metadata, rawData, 'Test', { description: longDescription }))
                .toThrow('Event description cannot exceed 2000 characters');
        });
    });
    describe('Status Management', () => {
        let event;
        beforeEach(() => {
            event = security_event_entity_1.SecurityEvent.create(metadata, rawData, 'Test Event');
            event.clearDomainEvents(); // Clear creation event for cleaner tests
        });
        it('should change status to valid next status', () => {
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.NORMALIZING);
            expect(event.status).toBe(event_processing_status_enum_1.EventProcessingStatus.NORMALIZING);
            expect(event.processingAttempts).toBe(1);
            expect(event.lastProcessedAt).toBeDefined();
        });
        it('should publish SecurityEventStatusChangedEvent on status change', () => {
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.NORMALIZING);
            const domainEvents = event.getDomainEvents();
            expect(domainEvents).toHaveLength(1);
            expect(domainEvents[0]).toBeInstanceOf(security_event_status_changed_event_1.SecurityEventStatusChangedEvent);
            const statusEvent = domainEvents[0];
            expect(statusEvent.oldStatus).toBe(event_processing_status_enum_1.EventProcessingStatus.RAW);
            expect(statusEvent.newStatus).toBe(event_processing_status_enum_1.EventProcessingStatus.NORMALIZING);
        });
        it('should reject invalid status transitions', () => {
            expect(() => event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.RESOLVED))
                .toThrow('Invalid status transition from raw to resolved');
        });
        it('should prevent status changes from terminal states', () => {
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.NORMALIZING);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.NORMALIZED);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.ENRICHING);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.ENRICHED);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.ANALYZING);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.ANALYZED);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.RESOLVED);
            expect(() => event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.NORMALIZING))
                .toThrow('Cannot change status from terminal state: resolved');
        });
        it('should handle error status with message', () => {
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.NORMALIZING);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.FAILED, {
                errorMessage: 'Processing failed due to invalid data',
                processingDuration: 5000,
            });
            expect(event.status).toBe(event_processing_status_enum_1.EventProcessingStatus.FAILED);
            expect(event.errorMessage).toBe('Processing failed due to invalid data');
            expect(event.processingDuration).toBe(5000);
        });
        it('should track processing attempts correctly', () => {
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.NORMALIZING);
            expect(event.processingAttempts).toBe(1);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.FAILED);
            expect(event.processingAttempts).toBe(1); // Failed doesn't increment
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.REPROCESSING);
            expect(event.processingAttempts).toBe(2);
        });
    });
    describe('Status Queries', () => {
        let event;
        beforeEach(() => {
            event = security_event_entity_1.SecurityEvent.create(metadata, rawData, 'Test Event');
        });
        it('should identify terminal statuses', () => {
            expect(event.isTerminal()).toBe(false);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.NORMALIZING);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.NORMALIZED);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.ENRICHING);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.ENRICHED);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.ANALYZING);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.ANALYZED);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.RESOLVED);
            expect(event.isTerminal()).toBe(true);
        });
        it('should identify in-progress statuses', () => {
            expect(event.isInProgress()).toBe(false);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.NORMALIZING);
            expect(event.isInProgress()).toBe(true);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.NORMALIZED);
            expect(event.isInProgress()).toBe(false);
        });
        it('should identify statuses requiring attention', () => {
            expect(event.requiresAttention()).toBe(false);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.NORMALIZING);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.FAILED);
            expect(event.requiresAttention()).toBe(true);
        });
        it('should identify retryable events', () => {
            expect(event.canRetry()).toBe(false);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.NORMALIZING);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.FAILED);
            expect(event.canRetry()).toBe(true);
            // Simulate max attempts reached
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.REPROCESSING);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.FAILED);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.REPROCESSING);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.FAILED);
            expect(event.canRetry()).toBe(false);
        });
    });
    describe('Tag Management', () => {
        let event;
        beforeEach(() => {
            event = security_event_entity_1.SecurityEvent.create(metadata, rawData, 'Test Event', {
                tags: ['initial', 'test'],
            });
        });
        it('should add new tags', () => {
            event.addTags(['new', 'additional']);
            expect(event.tags).toEqual(['initial', 'test', 'new', 'additional']);
        });
        it('should not duplicate existing tags', () => {
            event.addTags(['initial', 'new']);
            expect(event.tags).toEqual(['initial', 'test', 'new']);
        });
        it('should remove tags', () => {
            event.removeTags(['initial']);
            expect(event.tags).toEqual(['test']);
        });
        it('should check tag existence', () => {
            expect(event.hasTag('initial')).toBe(true);
            expect(event.hasTag('nonexistent')).toBe(false);
        });
    });
    describe('Attribute Management', () => {
        let event;
        beforeEach(() => {
            event = security_event_entity_1.SecurityEvent.create(metadata, rawData, 'Test Event', {
                attributes: { severity: 'medium', priority: 5 },
            });
        });
        it('should get attribute values', () => {
            expect(event.getAttribute('severity')).toBe('medium');
            expect(event.getAttribute('priority')).toBe(5);
            expect(event.getAttribute('nonexistent')).toBeUndefined();
        });
        it('should set attribute values', () => {
            event.setAttribute('newAttribute', 'value');
            expect(event.getAttribute('newAttribute')).toBe('value');
        });
        it('should remove attributes', () => {
            event.removeAttribute('severity');
            expect(event.getAttribute('severity')).toBeUndefined();
        });
    });
    describe('Convenience Methods', () => {
        let event;
        beforeEach(() => {
            event = security_event_entity_1.SecurityEvent.create(metadata, rawData, 'Test Event');
        });
        it('should mark event as failed', () => {
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.NORMALIZING);
            event.markAsFailed('Test error', 1000);
            expect(event.status).toBe(event_processing_status_enum_1.EventProcessingStatus.FAILED);
            expect(event.errorMessage).toBe('Test error');
            expect(event.processingDuration).toBe(1000);
        });
        it('should mark event as resolved', () => {
            // Progress through valid statuses to reach resolvable state
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.NORMALIZING);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.NORMALIZED);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.ENRICHING);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.ENRICHED);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.ANALYZING);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.ANALYZED);
            event.markAsResolved(5000);
            expect(event.status).toBe(event_processing_status_enum_1.EventProcessingStatus.RESOLVED);
            expect(event.processingDuration).toBe(5000);
        });
        it('should update description', () => {
            event.updateDescription('Updated description');
            expect(event.description).toBe('Updated description');
        });
        it('should validate description length when updating', () => {
            const longDescription = 'a'.repeat(2001);
            expect(() => event.updateDescription(longDescription))
                .toThrow('Description cannot exceed 2000 characters');
        });
    });
    describe('Metadata and Analysis', () => {
        let event;
        beforeEach(() => {
            event = security_event_entity_1.SecurityEvent.create(metadata, rawData, 'Test Event');
        });
        it('should provide event age', () => {
            const age = event.getAge();
            expect(age).toBeGreaterThanOrEqual(0);
            expect(typeof age).toBe('number');
        });
        it('should provide processing summary', () => {
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.NORMALIZING);
            event.changeStatus(event_processing_status_enum_1.EventProcessingStatus.FAILED, { errorMessage: 'Test error' });
            const summary = event.getProcessingSummary();
            expect(summary.status).toBe(event_processing_status_enum_1.EventProcessingStatus.FAILED);
            expect(summary.attempts).toBe(1);
            expect(summary.maxAttempts).toBe(3);
            expect(summary.canRetry).toBe(true);
            expect(summary.isTerminal).toBe(false);
            expect(summary.requiresAttention).toBe(true);
            expect(summary.errorMessage).toBe('Test error');
        });
        it('should serialize to JSON', () => {
            const json = event.toJSON();
            expect(json.id).toBeDefined();
            expect(json.metadata).toBeDefined();
            expect(json.status).toBe(event_processing_status_enum_1.EventProcessingStatus.RAW);
            expect(json.title).toBe('Test Event');
            expect(json.processingSummary).toBeDefined();
            expect(json.age).toBeGreaterThanOrEqual(0);
        });
    });
    describe('Data Access', () => {
        let event;
        beforeEach(() => {
            event = security_event_entity_1.SecurityEvent.create(metadata, rawData, 'Test Event');
        });
        it('should provide immutable access to raw data', () => {
            const data = event.rawData;
            data.newField = 'modified';
            // Original raw data should not be modified
            expect(event.rawData.newField).toBeUndefined();
        });
        it('should provide immutable access to tags', () => {
            event.addTags(['test']);
            const tags = event.tags;
            tags.push('modified');
            // Original tags should not be modified
            expect(event.tags).not.toContain('modified');
        });
        it('should provide immutable access to attributes', () => {
            event.setAttribute('test', 'value');
            const attributes = event.attributes;
            attributes.newField = 'modified';
            // Original attributes should not be modified
            expect(event.attributes.newField).toBeUndefined();
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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