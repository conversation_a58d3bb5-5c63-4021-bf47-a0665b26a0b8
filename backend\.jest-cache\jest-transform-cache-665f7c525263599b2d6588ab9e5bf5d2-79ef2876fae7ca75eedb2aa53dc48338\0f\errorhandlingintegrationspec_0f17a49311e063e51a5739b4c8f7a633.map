{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\__tests__\\integration\\error-handling.integration.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,4GAAuG;AAKvG,wEAAoE;AACpE,0EAAsE;AACtE,wFAAoF;AAEpF,iFAA6E;AAC7E,mFAA+E;AAC/E,iGAA6F;AAC7F,qGAAgG;AAChG,0GAA4F;AAC5F,0GAA4F;AAC5F,sHAAwG;AACxG,8GAAgG;AAChG,uHAAsG;AACtG,yHAAwG;AACxG,mHAAkG;AAClG,wGAAuF;AACvF,4FAA4E;AAC5E,kHAAiG;AACjG,wEAA+D;AAC/D,gFAAuE;AACvE,4EAAmE;AACnE,sFAA4E;AAC5E,kFAAyE;AACzE,gGAAuF;AAGvF,yHAAuG;AACvG,2GAA0F;AAC1F,uGAAsF;AACtF,oGAAgG;AAChG,kGAA6F;AAC7F,wGAAoG;AACpG,kGAA8F;AAC9F,gGAA4F;AAC5F,oGAA+F;AAC/F,sHAAiH;AAEjH;;GAEG;AACH,MAAM,yBAAyB;IAA/B;QACU,WAAM,GAAuB,IAAI,GAAG,EAAE,CAAC;QACvC,kBAAa,GAAkB,IAAI,CAAC;QACpC,eAAU,GAAG,CAAC,CAAC;QACf,cAAS,GAAG,CAAC,CAAC;IAuFxB,CAAC;IArFC,gBAAgB,CAAC,QAAgB,EAAE,SAAS,GAAG,CAAC;QAC9C,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC;QAC9B,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED,kBAAkB;QAChB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;IACtB,CAAC;IAEO,sBAAsB,CAAC,SAAiB;QAC9C,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YACzE,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,QAAQ,SAAS,EAAE,CAAC;gBAClB,KAAK,MAAM;oBACT,MAAM,IAAI,sCAAiB,CAAC,sBAAsB,CAAC,CAAC;gBACtD,KAAK,UAAU;oBACb,MAAM,IAAI,uCAAiB,CAAC,iBAAiB,CAAC,CAAC;gBACjD,KAAK,QAAQ;oBACX,MAAM,IAAI,wCAAkB,CAAC,0CAA0C,CAAC,CAAC;gBAC3E,KAAK,SAAS;oBACZ,MAAM,IAAI,2DAA2B,CAAC,4BAA4B,CAAC,CAAC;gBACtE,KAAK,SAAS;oBACZ,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;gBACzC;oBACE,MAAM,IAAI,KAAK,CAAC,sBAAsB,SAAS,EAAE,CAAC,CAAC;YACvD,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,KAAY;QACrB,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;QACpC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,EAAkB;QAC/B,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;QACxC,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,IAAI,IAAI,CAAC;IAChD,CAAC;IAED,KAAK,CAAC,OAAO;QACX,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;QACvC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;IAC1C,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAkB;QAC7B,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;IACpC,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,KAAW,EAAE,GAAS;QAC1C,IAAI,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,CAAC;QAC/C,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACrD,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YAC3C,OAAO,SAAS,IAAI,KAAK,IAAI,SAAS,IAAI,GAAG,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,MAAmB;QACpC,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,CAAC;QAC5C,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CACrD,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAC5B,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,QAAuB;QAC1C,IAAI,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,CAAC;QAC9C,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CACrD,KAAK,CAAC,QAAQ,KAAK,QAAQ,CAC5B,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,wBAAwB,CAAC,YAAoB,EAAE,UAAwB,EAAE,WAA2B;QACxG,IAAI,CAAC,sBAAsB,CAAC,0BAA0B,CAAC,CAAC;QACxD,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,YAAY,CAAC,CAAC;QACvD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACrD,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YAC3C,OAAO,SAAS,IAAI,UAAU,CAAC;QACjC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK;QACH,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;IACtB,CAAC;CACF;AAED,MAAM,wBAAwB;IAA9B;QACU,kBAAa,GAAkB,IAAI,CAAC;QACpC,eAAU,GAAG,CAAC,CAAC;QACf,cAAS,GAAG,CAAC,CAAC;IA2DxB,CAAC;IAzDC,gBAAgB,CAAC,QAAgB,EAAE,SAAS,GAAG,CAAC;QAC9C,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC;QAC9B,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED,kBAAkB;QAChB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;IACtB,CAAC;IAEO,sBAAsB,CAAC,SAAiB;QAC9C,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YACzE,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,QAAQ,SAAS,EAAE,CAAC;gBAClB,KAAK,cAAc;oBACjB,MAAM,IAAI,0CAAmB,CAAC,oBAAoB,EAAE,SAAS,CAAC,CAAC;gBACjE,KAAK,SAAS;oBACZ,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;gBACxC,KAAK,WAAW;oBACd,MAAM,IAAI,yCAAkB,CAAC,gCAAgC,CAAC,CAAC;gBACjE,KAAK,cAAc;oBACjB,MAAM,IAAI,8CAAqB,CAAC,+CAA+C,CAAC,CAAC;gBACnF;oBACE,MAAM,IAAI,2DAA2B,CAAC,sCAAsC,CAAC,CAAC;YAClF,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,KAAY,EAAE,OAAY;QAC3C,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,CAAC;QAE5C,OAAO;YACL,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE;YAC5B,eAAe,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,CAAC;YACrD,QAAQ,EAAE,GAAG;YACb,eAAe,EAAE,IAAI;YACrB,aAAa,EAAE,IAAI;YACnB,gBAAgB,EAAE,EAAE;YACpB,MAAM,EAAE,EAAE;SACX,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,MAAe,EAAE,OAAY;QAC/C,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC;QAE7C,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAC/B,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CACvD,CAAC;QACF,OAAO;YACL,OAAO,EAAE,IAAI;YACb,WAAW,EAAE,MAAM,CAAC,MAAM;YAC1B,eAAe,EAAE,OAAO,CAAC,MAAM;YAC/B,OAAO;SACR,CAAC;IACJ,CAAC;CACF;AAED,MAAM,wBAAwB;IAA9B;QACU,kBAAa,GAAkB,IAAI,CAAC;QACpC,eAAU,GAAG,CAAC,CAAC;QACf,cAAS,GAAG,CAAC,CAAC;IA+DxB,CAAC;IA7DC,gBAAgB,CAAC,QAAgB,EAAE,SAAS,GAAG,CAAC;QAC9C,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC;QAC9B,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED,kBAAkB;QAChB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;IACtB,CAAC;IAEO,sBAAsB,CAAC,SAAiB;QAC9C,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YACzE,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,QAAQ,SAAS,EAAE,CAAC;gBAClB,KAAK,cAAc;oBACjB,MAAM,IAAI,2DAA2B,CAAC,yCAAyC,CAAC,CAAC;gBACnF,KAAK,SAAS;oBACZ,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;gBAC7C,KAAK,WAAW;oBACd,MAAM,IAAI,yCAAkB,CAAC,qCAAqC,CAAC,CAAC;gBACtE;oBACE,MAAM,IAAI,KAAK,CAAC,2BAA2B,SAAS,EAAE,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,KAAY,EAAE,OAAY;QAC3C,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,CAAC;QAE5C,MAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,KAAK,mCAAa,CAAC,IAAI,IAAI,KAAK,CAAC,QAAQ,KAAK,mCAAa,CAAC,QAAQ;YAChG,CAAC,CAAC,CAAC;oBACC,EAAE,EAAE,8CAAc,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;oBACtC,QAAQ,EAAE,qCAAc,CAAC,IAAI;oBAC7B,UAAU,EAAE,EAAE;oBACd,SAAS,EAAE,qBAAqB;iBACjC,CAAC;YACJ,CAAC,CAAC,EAAE,CAAC;QAEP,OAAO;YACL,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE;YAC5B,OAAO;YACP,UAAU,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YACxC,YAAY,EAAE,EAAE;YAChB,UAAU,EAAE,EAAE;YACd,eAAe,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,EAAE;SACvE,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,MAAe,EAAE,OAAY;QAC/C,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC;QAE7C,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAChC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CACvD,CAAC;QACF,OAAO;YACL,WAAW,EAAE,MAAM,CAAC,MAAM;YAC1B,QAAQ;YACR,iBAAiB,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;SACpD,CAAC;IACJ,CAAC;CACF;AAED,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;IAChD,IAAI,MAAqB,CAAC;IAC1B,IAAI,mBAAgD,CAAC;IACrD,IAAI,eAA0C,CAAC;IAC/C,IAAI,cAAwC,CAAC;IAC7C,IAAI,cAAwC,CAAC;IAE7C,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,eAAe,GAAG,IAAI,yBAAyB,EAAE,CAAC;QAClD,cAAc,GAAG,IAAI,wBAAwB,EAAE,CAAC;QAChD,cAAc,GAAG,IAAI,wBAAwB,EAAE,CAAC;QAEhD,MAAM,GAAG,MAAM,cAAI,CAAC,mBAAmB,CAAC;YACtC,SAAS,EAAE;gBACT,2DAA2B;gBAC3B,EAAE,OAAO,EAAE,kCAAe,EAAE,QAAQ,EAAE,eAAe,EAAE;gBACvD,EAAE,OAAO,EAAE,oCAAgB,EAAE,QAAQ,EAAE,EAAE,EAAE;gBAC3C,EAAE,OAAO,EAAE,kDAAuB,EAAE,QAAQ,EAAE,EAAE,EAAE;gBAClD,EAAE,OAAO,EAAE,qDAAwB,EAAE,QAAQ,EAAE,EAAE,EAAE;gBACnD,EAAE,OAAO,EAAE,0CAAc,EAAE,QAAQ,EAAE,cAAc,EAAE;gBACrD,EAAE,OAAO,EAAE,0CAAc,EAAE,QAAQ,EAAE,cAAc,EAAE;gBACrD,EAAE,OAAO,EAAE,sDAAoB,EAAE,QAAQ,EAAE,EAAE,EAAE;gBAC/C,EAAE,OAAO,EAAE,8CAAgB,EAAE,QAAQ,EAAE,EAAE,EAAE;aAC5C;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAA8B,2DAA2B,CAAC,CAAC;IAC7F,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,eAAe,CAAC,KAAK,EAAE,CAAC;QACxB,eAAe,CAAC,kBAAkB,EAAE,CAAC;QACrC,cAAc,CAAC,kBAAkB,EAAE,CAAC;QACpC,cAAc,CAAC,kBAAkB,EAAE,CAAC;QACpC,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,sCAAsC;YACtC,MAAM,gBAAgB,GAAG;gBACvB,QAAQ,EAAE,IAAI,EAAE,2BAA2B;gBAC3C,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,SAAS;oBACrB,IAAI,EAAE,UAAU;iBACjB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,sBAAsB;gBACtC,QAAQ,EAAE,mCAAa,CAAC,MAAM;gBAC9B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE;gBACX,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC;YAEF,eAAe;YACf,MAAM,CAAC,GAAG,EAAE;gBACV,4BAAY,CAAC,MAAM,CAAC,gBAAuB,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,0BAA0B;YAC1B,MAAM,CAAC,GAAG,EAAE;gBACV,mCAAS,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;YAEhC,2BAA2B;YAC3B,MAAM,CAAC,GAAG,EAAE;gBACV,wBAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,oBAAoB;YAC1C,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;YAEhC,0BAA0B;YAC1B,MAAM,CAAC,GAAG,EAAE;gBACV,mCAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,qBAAqB;YAC/C,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,wCAAwC;YACxC,MAAM,CAAC,GAAG,EAAE;gBACV,8BAAa,CAAC,MAAM,CAAC;oBACnB,SAAS,EAAE,EAAE,EAAE,kBAAkB;oBACjC,KAAK,EAAE,mCAAS,CAAC,MAAM,CAAC,GAAG,CAAC;oBAC5B,QAAQ,EAAE,qCAAc,CAAC,IAAI;oBAC7B,UAAU,EAAE,GAAG,EAAE,2BAA2B;oBAC5C,QAAQ,EAAE,mCAAS,CAAC,MAAM,CAAC,eAAe,CAAC;oBAC3C,UAAU,EAAE,EAAE;oBACd,eAAe,EAAE,EAAE;oBACnB,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;oBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;iBACxB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;YAEhC,iDAAiD;YACjD,MAAM,CAAC,GAAG,EAAE;gBACV,4CAAoB,CAAC,MAAM,CAAC;oBAC1B,KAAK,EAAE,aAAa,EAAE,qBAAqB;oBAC3C,KAAK,EAAE,mCAAS,CAAC,MAAM,CAAC,GAAG,CAAC;oBAC5B,QAAQ,EAAE,mDAAqB,CAAC,IAAI;oBACpC,YAAY,EAAE,wBAAI,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC7B,WAAW,EAAE,oBAAoB;oBACjC,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;oBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;iBACxB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,UAAU;YACV,MAAM,KAAK,GAAG,4BAAY,CAAC,MAAM,CAAC;gBAChC,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBAClE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,SAAS;oBACrB,IAAI,EAAE,UAAU;iBACjB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,sBAAsB;gBACtC,QAAQ,EAAE,mCAAa,CAAC,GAAG;gBAC3B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE;gBACjC,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,eAAe,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAEzC,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,2BAA2B,CAAC,CAAC,KAAK,CAAC,EAAE;gBAC5E,QAAQ,EAAE,KAAY;aACvB,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAC9C,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC7D,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,UAAU;YACV,MAAM,aAAa,GAAG,8CAAc,CAAC,MAAM,EAAE,CAAC;YAC9C,eAAe,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;YAE7C,eAAe;YACf,MAAM,KAAK,GAAG,MAAM,eAAe,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;YAC5D,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,sCAAsC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,UAAU;YACV,MAAM,KAAK,GAAG,4BAAY,CAAC,MAAM,CAAC;gBAChC,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBAClE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,SAAS;oBACrB,IAAI,EAAE,UAAU;iBACjB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,cAAc;gBAC9B,QAAQ,EAAE,mCAAa,CAAC,MAAM;gBAC9B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;gBAC9B,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,MAAM,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClC,eAAe,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAE3C,eAAe;YACf,MAAM,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,wCAAkB,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,UAAU;YACV,eAAe,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YAE5C,eAAe;YACf,MAAM,MAAM,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,2DAA2B,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,UAAU;YACV,MAAM,KAAK,GAAG,4BAAY,CAAC,MAAM,CAAC;gBAChC,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBAClE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,SAAS;oBACrB,IAAI,EAAE,UAAU;iBACjB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,sBAAsB;gBACtC,QAAQ,EAAE,mCAAa,CAAC,MAAM;gBAC9B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE;gBACjC,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,cAAc,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;YAEhD,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,qBAAqB,CAAC,CAAC,KAAK,CAAC,EAAE;gBACtE,QAAQ,EAAE,QAAe;aAC1B,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,gBAAgB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAC3C,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7C,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,UAAU;YACV,MAAM,KAAK,GAAG,4BAAY,CAAC,MAAM,CAAC;gBAChC,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBAClE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,SAAS;oBACrB,IAAI,EAAE,UAAU;iBACjB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,iBAAiB;gBACjC,QAAQ,EAAE,mCAAa,CAAC,IAAI;gBAC5B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,QAAQ,EAAE,eAAe,EAAE;gBACtC,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,cAAc,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;YAEhD,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,2BAA2B,CAAC,CAAC,KAAK,CAAC,EAAE;gBAC5E,QAAQ,EAAE,MAAa;gBACvB,mBAAmB,EAAE,IAAI;aAC1B,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAE9C,8DAA8D;YAC9D,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpD,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,UAAU;YACV,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAChD,4BAAY,CAAC,MAAM,CAAC;gBAClB,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBACzE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,OAAO,CAAC,EAAE;oBACtB,IAAI,EAAE,OAAO,CAAC,EAAE;iBACjB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,sBAAsB;gBACtC,QAAQ,EAAE,mCAAa,CAAC,MAAM;gBAC9B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE;gBACvB,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CACH,CAAC;YAEF,cAAc,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,0BAA0B;YAE3E,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,2BAA2B,CAAC,MAAM,EAAE;gBAC3E,QAAQ,EAAE,QAAe;gBACzB,cAAc,EAAE;oBACd,qBAAqB,EAAE,IAAI;oBAC3B,qBAAqB,EAAE,KAAK;oBAC5B,2BAA2B,EAAE,KAAK;oBAClC,uBAAuB,EAAE,KAAK;oBAC9B,iBAAiB,EAAE,KAAK;oBACxB,gBAAgB,EAAE,KAAK;oBACvB,SAAS,EAAE,CAAC;oBACZ,uBAAuB,EAAE,CAAC;oBAC1B,aAAa,EAAE,CAAC;oBAChB,SAAS,EAAE,KAAK;iBACjB;aACF,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAE9C,yEAAyE;YACzE,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC/D,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,UAAU;YACV,MAAM,KAAK,GAAG,4BAAY,CAAC,MAAM,CAAC;gBAChC,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBACxE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,gBAAgB;oBAC5B,IAAI,EAAE,gBAAgB;iBACvB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,cAAc;gBAC9B,QAAQ,EAAE,mCAAa,CAAC,IAAI;gBAC5B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,SAAS,EAAE,YAAY,EAAE;gBACpC,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,cAAc,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;YAEhD,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,qBAAqB,CAAC,CAAC,KAAK,CAAC,EAAE;gBACtE,QAAQ,EAAE,MAAa;aACxB,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,gBAAgB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAC3C,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7C,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,+DAA+D,EAAE,KAAK,IAAI,EAAE;YAC7E,UAAU;YACV,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACjD,4BAAY,CAAC,MAAM,CAAC;gBAClB,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,WAAW,CAAC,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBAC1E,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,UAAU,CAAC,EAAE;oBACzB,IAAI,EAAE,uBAAuB,CAAC,EAAE;iBACjC,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,sBAAsB;gBACtC,QAAQ,EAAE,mCAAa,CAAC,MAAM;gBAC9B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE;gBACvB,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CACH,CAAC;YAEF,iDAAiD;YACjD,cAAc,CAAC,gBAAgB,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;YAEpD,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,2BAA2B,CAAC,MAAM,EAAE;gBAC3E,QAAQ,EAAE,QAAe;gBACzB,cAAc,EAAE;oBACd,qBAAqB,EAAE,IAAI;oBAC3B,qBAAqB,EAAE,IAAI;oBAC3B,2BAA2B,EAAE,KAAK;oBAClC,uBAAuB,EAAE,KAAK;oBAC9B,iBAAiB,EAAE,KAAK;oBACxB,gBAAgB,EAAE,KAAK;oBACvB,SAAS,EAAE,CAAC;oBACZ,uBAAuB,EAAE,CAAC;oBAC1B,aAAa,EAAE,CAAC;oBAChB,SAAS,EAAE,KAAK;iBACjB;aACF,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAE9C,sEAAsE;YACtE,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACrD,MAAM,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YAE/D,2CAA2C;YAC3C,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAE7D,wCAAwC;YACxC,mBAAmB,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;gBACjD,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,UAAU;YACV,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAChD,4BAAY,CAAC,MAAM,CAAC;gBAClB,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBACxE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,aAAa,CAAC,EAAE;oBAC5B,IAAI,EAAE,aAAa,CAAC,EAAE;iBACvB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,cAAc;gBAC9B,QAAQ,EAAE,mCAAa,CAAC,GAAG;gBAC3B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE;gBAC1B,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CACH,CAAC;YAEF,gDAAgD;YAChD,cAAc,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;YAEnD,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,2BAA2B,CAAC,MAAM,EAAE;gBAC3E,QAAQ,EAAE,KAAY;gBACtB,cAAc,EAAE;oBACd,qBAAqB,EAAE,IAAI;oBAC3B,qBAAqB,EAAE,KAAK;oBAC5B,2BAA2B,EAAE,KAAK;oBAClC,uBAAuB,EAAE,KAAK;oBAC9B,iBAAiB,EAAE,KAAK;oBACxB,gBAAgB,EAAE,KAAK;oBACvB,SAAS,EAAE,CAAC;oBACZ,uBAAuB,EAAE,CAAC;oBAC1B,aAAa,EAAE,CAAC;oBAChB,SAAS,EAAE,KAAK;iBACjB;aACF,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAE9C,iDAAiD;YACjD,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC/D,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc;YAE3E,4DAA4D;YAC5D,MAAM,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACxE,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,UAAU;YACV,MAAM,KAAK,GAAG,4BAAY,CAAC,MAAM,CAAC;gBAChC,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBACxE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,WAAW;oBACvB,IAAI,EAAE,gBAAgB;iBACvB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,sBAAsB;gBACtC,QAAQ,EAAE,mCAAa,CAAC,MAAM;gBAC9B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE;gBAClC,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,MAAM,SAAS,GAAa,EAAE,CAAC;YAE/B,MAAM,oBAAoB,GAAG,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC9E,cAAc,CAAC,YAAY,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;gBAClF,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;gBAC3B,SAAS,EAAE,CAAC;gBACZ,IAAI,SAAS,IAAI,CAAC,EAAE,CAAC;oBACnB,MAAM,IAAI,2DAA2B,CAAC,mBAAmB,CAAC,CAAC;gBAC7D,CAAC;gBACD,OAAO,oBAAoB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;YAEH,MAAM;YACN,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,qBAAqB,CAAC,CAAC,KAAK,CAAC,EAAE;gBACtE,QAAQ,EAAE,QAAe;gBACzB,cAAc,EAAE;oBACd,qBAAqB,EAAE,IAAI;oBAC3B,qBAAqB,EAAE,KAAK;oBAC5B,2BAA2B,EAAE,KAAK;oBAClC,uBAAuB,EAAE,KAAK;oBAC9B,iBAAiB,EAAE,KAAK;oBACxB,gBAAgB,EAAE,KAAK;oBACvB,SAAS,EAAE,CAAC;oBACZ,uBAAuB,EAAE,CAAC;oBAC1B,aAAa,EAAE,CAAC;oBAChB,SAAS,EAAE,KAAK;iBACjB;aACF,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,gBAAgB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAE3C,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5C,MAAM,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACjD,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,2BAA2B;YAEtD,kDAAkD;YAClD,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;gBAC1B,MAAM,eAAe,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;gBACpD,MAAM,gBAAgB,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;gBACrD,MAAM,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;YACpE,UAAU;YACV,MAAM,QAAQ,GAAG,iCAAQ,CAAC,MAAM,EAAE,CAAC;YACnC,MAAM,MAAM,GAAG,6BAAM,CAAC,MAAM,EAAE,CAAC;YAE/B,MAAM,KAAK,GAAG,4BAAY,CAAC,MAAM,CAAC;gBAChC,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBAC1E,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,aAAa;oBACzB,IAAI,EAAE,kBAAkB;iBACzB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,cAAc;gBAC9B,QAAQ,EAAE,mCAAa,CAAC,IAAI;gBAC5B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,SAAS,EAAE,cAAc,EAAE;gBACtC,QAAQ;gBACR,MAAM;aACP,CAAC,CAAC;YAEH,cAAc,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;YAEhD,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,2BAA2B,CAAC,CAAC,KAAK,CAAC,EAAE;gBAC5E,QAAQ,EAAE,QAAQ,CAAC,KAAK;gBACxB,MAAM,EAAE,MAAM,CAAC,KAAK;gBACpB,QAAQ,EAAE,MAAa;aACxB,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAE9C,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAE7D,MAAM,KAAK,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;YAClC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YACtC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,UAAU;YACV,MAAM,KAAK,GAAG,4BAAY,CAAC,MAAM,CAAC;gBAChC,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBAC1E,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,aAAa;oBACzB,IAAI,EAAE,kBAAkB;iBACzB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,iBAAiB;gBACjC,QAAQ,EAAE,mCAAa,CAAC,QAAQ;gBAChC,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,QAAQ,EAAE,eAAe,EAAE;gBACtC,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,sCAAsC;YACtC,cAAc,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;YAChD,cAAc,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;YAEhD,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,2BAA2B,CAAC,CAAC,KAAK,CAAC,EAAE;gBAC5E,QAAQ,EAAE,UAAiB;gBAC3B,YAAY,EAAE,KAAK;aACpB,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAE9C,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAE7D,0CAA0C;YAC1C,MAAM,WAAW,GAAG,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YACjE,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,KAAK,IAAI,EAAE;YACvE,UAAU;YACV,MAAM,KAAK,GAAG,4BAAY,CAAC,MAAM,CAAC;gBAChC,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBACxE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,WAAW;oBACvB,IAAI,EAAE,gBAAgB;iBACvB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,sBAAsB;gBACtC,QAAQ,EAAE,mCAAa,CAAC,MAAM;gBAC9B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE;gBAClC,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,eAAe,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAEzC,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,2BAA2B,CAAC,CAAC,KAAK,CAAC,EAAE;gBAC5E,QAAQ,EAAE,QAAe;aAC1B,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAE9C,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAE7D,MAAM,KAAK,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACrD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;YACxD,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\__tests__\\integration\\error-handling.integration.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { SecurityOrchestratorService } from '../../application/services/security-orchestrator.service';\r\nimport { Event } from '../../domain/entities/event.entity';\r\nimport { Threat } from '../../domain/entities/threat.entity';\r\nimport { Vulnerability } from '../../domain/entities/vulnerability.entity';\r\nimport { ResponseAction } from '../../domain/entities/response-action.entity';\r\nimport { EventFactory } from '../../domain/factories/event.factory';\r\nimport { ThreatFactory } from '../../domain/factories/threat.factory';\r\nimport { VulnerabilityFactory } from '../../domain/factories/vulnerability.factory';\r\nimport { ResponseActionFactory } from '../../domain/factories/response-action.factory';\r\nimport { EventRepository } from '../../domain/repositories/event.repository';\r\nimport { ThreatRepository } from '../../domain/repositories/threat.repository';\r\nimport { VulnerabilityRepository } from '../../domain/repositories/vulnerability.repository';\r\nimport { ResponseActionRepository } from '../../domain/repositories/response-action.repository';\r\nimport { EventProcessor } from '../../domain/interfaces/services/event-processor.interface';\r\nimport { ThreatDetector } from '../../domain/interfaces/services/threat-detector.interface';\r\nimport { VulnerabilityScanner } from '../../domain/interfaces/services/vulnerability-scanner.interface';\r\nimport { ResponseExecutor } from '../../domain/interfaces/services/response-executor.interface';\r\nimport { EventMetadata } from '../../domain/value-objects/event-metadata/event-metadata.value-object';\r\nimport { EventTimestamp } from '../../domain/value-objects/event-metadata/event-timestamp.value-object';\r\nimport { EventSource } from '../../domain/value-objects/event-metadata/event-source.value-object';\r\nimport { IpAddress } from '../../domain/value-objects/network/ip-address.value-object';\r\nimport { Port } from '../../domain/value-objects/network/port.value-object';\r\nimport { CvssScore } from '../../domain/value-objects/threat-indicators/cvss-score.value-object';\r\nimport { EventType } from '../../domain/enums/event-type.enum';\r\nimport { EventSeverity } from '../../domain/enums/event-severity.enum';\r\nimport { EventStatus } from '../../domain/enums/event-status.enum';\r\nimport { EventSourceType } from '../../domain/enums/event-source-type.enum';\r\nimport { ThreatSeverity } from '../../domain/enums/threat-severity.enum';\r\nimport { VulnerabilitySeverity } from '../../domain/enums/vulnerability-severity.enum';\r\nimport { ActionType } from '../../domain/enums/action-type.enum';\r\nimport { ActionStatus } from '../../domain/enums/action-status.enum';\r\nimport { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { TenantId } from '../../../../shared-kernel/value-objects/tenant-id.value-object';\r\nimport { UserId } from '../../../../shared-kernel/value-objects/user-id.value-object';\r\nimport { ValidationException } from '../../../../shared-kernel/exceptions/validation.exception';\r\nimport { NotFoundException } from '../../../../shared-kernel/exceptions/not-found.exception';\r\nimport { UnauthorizedException } from '../../../../shared-kernel/exceptions/unauthorized.exception';\r\nimport { ForbiddenException } from '../../../../shared-kernel/exceptions/forbidden.exception';\r\nimport { ConflictException } from '../../../../shared-kernel/exceptions/conflict.exception';\r\nimport { RateLimitException } from '../../../../shared-kernel/exceptions/rate-limit.exception';\r\nimport { ServiceUnavailableException } from '../../../../shared-kernel/exceptions/service-unavailable.exception';\r\n\r\n/**\r\n * Error-prone mock implementations for testing error handling\r\n */\r\nclass ErrorProneEventRepository implements EventRepository {\r\n  private events: Map<string, Event> = new Map();\r\n  private errorScenario: string | null = null;\r\n  private errorCount = 0;\r\n  private maxErrors = 1;\r\n\r\n  setErrorScenario(scenario: string, maxErrors = 1): void {\r\n    this.errorScenario = scenario;\r\n    this.errorCount = 0;\r\n    this.maxErrors = maxErrors;\r\n  }\r\n\r\n  clearErrorScenario(): void {\r\n    this.errorScenario = null;\r\n    this.errorCount = 0;\r\n  }\r\n\r\n  private throwErrorIfConfigured(operation: string): void {\r\n    if (this.errorScenario === operation && this.errorCount < this.maxErrors) {\r\n      this.errorCount++;\r\n      switch (operation) {\r\n        case 'save':\r\n          throw new ConflictException('Event already exists');\r\n        case 'findById':\r\n          throw new NotFoundException('Event not found');\r\n        case 'delete':\r\n          throw new ForbiddenException('Insufficient permissions to delete event');\r\n        case 'findAll':\r\n          throw new ServiceUnavailableException('Database connection failed');\r\n        case 'timeout':\r\n          throw new Error('Operation timed out');\r\n        default:\r\n          throw new Error(`Simulated error in ${operation}`);\r\n      }\r\n    }\r\n  }\r\n\r\n  async save(event: Event): Promise<void> {\r\n    this.throwErrorIfConfigured('save');\r\n    this.events.set(event.id.toString(), event);\r\n  }\r\n\r\n  async findById(id: UniqueEntityId): Promise<Event | null> {\r\n    this.throwErrorIfConfigured('findById');\r\n    return this.events.get(id.toString()) || null;\r\n  }\r\n\r\n  async findAll(): Promise<Event[]> {\r\n    this.throwErrorIfConfigured('findAll');\r\n    return Array.from(this.events.values());\r\n  }\r\n\r\n  async delete(id: UniqueEntityId): Promise<void> {\r\n    this.throwErrorIfConfigured('delete');\r\n    this.events.delete(id.toString());\r\n  }\r\n\r\n  async findByTimeRange(start: Date, end: Date): Promise<Event[]> {\r\n    this.throwErrorIfConfigured('findByTimeRange');\r\n    return Array.from(this.events.values()).filter(event => {\r\n      const eventTime = event.timestamp.toDate();\r\n      return eventTime >= start && eventTime <= end;\r\n    });\r\n  }\r\n\r\n  async findBySource(source: EventSource): Promise<Event[]> {\r\n    this.throwErrorIfConfigured('findBySource');\r\n    return Array.from(this.events.values()).filter(event => \r\n      event.source.equals(source)\r\n    );\r\n  }\r\n\r\n  async findBySeverity(severity: EventSeverity): Promise<Event[]> {\r\n    this.throwErrorIfConfigured('findBySeverity');\r\n    return Array.from(this.events.values()).filter(event => \r\n      event.severity === severity\r\n    );\r\n  }\r\n\r\n  async findEventsForCorrelation(timeWindowMs: number, eventTypes?: EventType[], minSeverity?: EventSeverity): Promise<Event[]> {\r\n    this.throwErrorIfConfigured('findEventsForCorrelation');\r\n    const cutoffTime = new Date(Date.now() - timeWindowMs);\r\n    return Array.from(this.events.values()).filter(event => {\r\n      const eventTime = event.timestamp.toDate();\r\n      return eventTime >= cutoffTime;\r\n    });\r\n  }\r\n\r\n  clear(): void {\r\n    this.events.clear();\r\n  }\r\n}\r\n\r\nclass ErrorProneEventProcessor implements EventProcessor {\r\n  private errorScenario: string | null = null;\r\n  private errorCount = 0;\r\n  private maxErrors = 1;\r\n\r\n  setErrorScenario(scenario: string, maxErrors = 1): void {\r\n    this.errorScenario = scenario;\r\n    this.errorCount = 0;\r\n    this.maxErrors = maxErrors;\r\n  }\r\n\r\n  clearErrorScenario(): void {\r\n    this.errorScenario = null;\r\n    this.errorCount = 0;\r\n  }\r\n\r\n  private throwErrorIfConfigured(operation: string): void {\r\n    if (this.errorScenario === operation && this.errorCount < this.maxErrors) {\r\n      this.errorCount++;\r\n      switch (operation) {\r\n        case 'processEvent':\r\n          throw new ValidationException('Invalid event data', 'payload');\r\n        case 'timeout':\r\n          throw new Error('Processing timeout');\r\n        case 'rateLimit':\r\n          throw new RateLimitException('Processing rate limit exceeded');\r\n        case 'unauthorized':\r\n          throw new UnauthorizedException('Insufficient permissions for event processing');\r\n        default:\r\n          throw new ServiceUnavailableException('Event processing service unavailable');\r\n      }\r\n    }\r\n  }\r\n\r\n  async processEvent(event: Event, context: any): Promise<any> {\r\n    this.throwErrorIfConfigured('processEvent');\r\n    \r\n    return {\r\n      success: true,\r\n      eventId: event.id.toString(),\r\n      processingSteps: ['normalize', 'enrich', 'correlate'],\r\n      duration: 100,\r\n      normalizedEvent: null,\r\n      enrichedEvent: null,\r\n      correlatedEvents: [],\r\n      errors: [],\r\n    };\r\n  }\r\n\r\n  async processEvents(events: Event[], context: any): Promise<any> {\r\n    this.throwErrorIfConfigured('processEvents');\r\n    \r\n    const results = await Promise.all(\r\n      events.map(event => this.processEvent(event, context))\r\n    );\r\n    return {\r\n      success: true,\r\n      totalEvents: events.length,\r\n      processedEvents: results.length,\r\n      results,\r\n    };\r\n  }\r\n}\r\n\r\nclass ErrorProneThreatDetector implements ThreatDetector {\r\n  private errorScenario: string | null = null;\r\n  private errorCount = 0;\r\n  private maxErrors = 1;\r\n\r\n  setErrorScenario(scenario: string, maxErrors = 1): void {\r\n    this.errorScenario = scenario;\r\n    this.errorCount = 0;\r\n    this.maxErrors = maxErrors;\r\n  }\r\n\r\n  clearErrorScenario(): void {\r\n    this.errorScenario = null;\r\n    this.errorCount = 0;\r\n  }\r\n\r\n  private throwErrorIfConfigured(operation: string): void {\r\n    if (this.errorScenario === operation && this.errorCount < this.maxErrors) {\r\n      this.errorCount++;\r\n      switch (operation) {\r\n        case 'analyzeEvent':\r\n          throw new ServiceUnavailableException('Threat intelligence service unavailable');\r\n        case 'timeout':\r\n          throw new Error('Threat analysis timeout');\r\n        case 'rateLimit':\r\n          throw new RateLimitException('Threat analysis rate limit exceeded');\r\n        default:\r\n          throw new Error(`Threat detection error: ${operation}`);\r\n      }\r\n    }\r\n  }\r\n\r\n  async analyzeEvent(event: Event, context: any): Promise<any> {\r\n    this.throwErrorIfConfigured('analyzeEvent');\r\n    \r\n    const threats = event.severity === EventSeverity.HIGH || event.severity === EventSeverity.CRITICAL\r\n      ? [{ \r\n          id: UniqueEntityId.create().toString(), \r\n          severity: ThreatSeverity.HIGH, \r\n          confidence: 85,\r\n          signature: 'detected-threat-001'\r\n        }]\r\n      : [];\r\n\r\n    return {\r\n      eventId: event.id.toString(),\r\n      threats,\r\n      confidence: threats.length > 0 ? 85 : 10,\r\n      analysisTime: 50,\r\n      indicators: [],\r\n      recommendations: threats.length > 0 ? ['Investigate immediately'] : [],\r\n    };\r\n  }\r\n\r\n  async analyzeEvents(events: Event[], context: any): Promise<any> {\r\n    this.throwErrorIfConfigured('analyzeEvents');\r\n    \r\n    const analyses = await Promise.all(\r\n      events.map(event => this.analyzeEvent(event, context))\r\n    );\r\n    return {\r\n      totalEvents: events.length,\r\n      analyses,\r\n      aggregatedThreats: analyses.flatMap(a => a.threats),\r\n    };\r\n  }\r\n}\r\n\r\ndescribe('Error Handling Integration Tests', () => {\r\n  let module: TestingModule;\r\n  let orchestratorService: SecurityOrchestratorService;\r\n  let eventRepository: ErrorProneEventRepository;\r\n  let eventProcessor: ErrorProneEventProcessor;\r\n  let threatDetector: ErrorProneThreatDetector;\r\n\r\n  beforeEach(async () => {\r\n    eventRepository = new ErrorProneEventRepository();\r\n    eventProcessor = new ErrorProneEventProcessor();\r\n    threatDetector = new ErrorProneThreatDetector();\r\n\r\n    module = await Test.createTestingModule({\r\n      providers: [\r\n        SecurityOrchestratorService,\r\n        { provide: EventRepository, useValue: eventRepository },\r\n        { provide: ThreatRepository, useValue: {} },\r\n        { provide: VulnerabilityRepository, useValue: {} },\r\n        { provide: ResponseActionRepository, useValue: {} },\r\n        { provide: EventProcessor, useValue: eventProcessor },\r\n        { provide: ThreatDetector, useValue: threatDetector },\r\n        { provide: VulnerabilityScanner, useValue: {} },\r\n        { provide: ResponseExecutor, useValue: {} },\r\n      ],\r\n    }).compile();\r\n\r\n    orchestratorService = module.get<SecurityOrchestratorService>(SecurityOrchestratorService);\r\n  });\r\n\r\n  afterEach(async () => {\r\n    eventRepository.clear();\r\n    eventRepository.clearErrorScenario();\r\n    eventProcessor.clearErrorScenario();\r\n    threatDetector.clearErrorScenario();\r\n    await module.close();\r\n  });\r\n\r\n  describe('Domain Validation Errors', () => {\r\n    it('should handle validation errors in entity creation', () => {\r\n      // Arrange - Create invalid event data\r\n      const invalidEventData = {\r\n        metadata: null, // Invalid - required field\r\n        timestamp: EventTimestamp.create(new Date()),\r\n        source: EventSource.create({\r\n          type: EventSourceType.APPLICATION,\r\n          identifier: 'app-001',\r\n          name: 'Test App',\r\n        }),\r\n        type: EventType.AUTHENTICATION_FAILURE,\r\n        severity: EventSeverity.MEDIUM,\r\n        status: EventStatus.RECEIVED,\r\n        payload: {},\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      };\r\n\r\n      // Act & Assert\r\n      expect(() => {\r\n        EventFactory.create(invalidEventData as any);\r\n      }).toThrow(ValidationException);\r\n    });\r\n\r\n    it('should handle validation errors in value object creation', () => {\r\n      // Test invalid IP address\r\n      expect(() => {\r\n        IpAddress.create('invalid-ip');\r\n      }).toThrow(ValidationException);\r\n\r\n      // Test invalid port number\r\n      expect(() => {\r\n        Port.create(70000); // Port out of range\r\n      }).toThrow(ValidationException);\r\n\r\n      // Test invalid CVSS score\r\n      expect(() => {\r\n        CvssScore.create(15.0); // Score out of range\r\n      }).toThrow(ValidationException);\r\n    });\r\n\r\n    it('should handle validation errors in factory methods', () => {\r\n      // Test threat factory with invalid data\r\n      expect(() => {\r\n        ThreatFactory.create({\r\n          signature: '', // Empty signature\r\n          score: CvssScore.create(7.5),\r\n          severity: ThreatSeverity.HIGH,\r\n          confidence: 150, // Invalid confidence > 100\r\n          sourceIp: IpAddress.create('*************'),\r\n          indicators: [],\r\n          mitreTechniques: [],\r\n          tenantId: TenantId.create(),\r\n          userId: UserId.create(),\r\n        });\r\n      }).toThrow(ValidationException);\r\n\r\n      // Test vulnerability factory with invalid CVE ID\r\n      expect(() => {\r\n        VulnerabilityFactory.create({\r\n          cveId: 'INVALID-CVE', // Invalid CVE format\r\n          score: CvssScore.create(8.0),\r\n          severity: VulnerabilitySeverity.HIGH,\r\n          affectedPort: Port.create(80),\r\n          description: 'Test vulnerability',\r\n          tenantId: TenantId.create(),\r\n          userId: UserId.create(),\r\n        });\r\n      }).toThrow(ValidationException);\r\n    });\r\n  });\r\n\r\n  describe('Repository Error Handling', () => {\r\n    it('should handle repository save conflicts', async () => {\r\n      // Arrange\r\n      const event = EventFactory.create({\r\n        metadata: EventMetadata.create({ source: 'test', version: '1.0' }),\r\n        timestamp: EventTimestamp.create(new Date()),\r\n        source: EventSource.create({\r\n          type: EventSourceType.APPLICATION,\r\n          identifier: 'app-001',\r\n          name: 'Test App',\r\n        }),\r\n        type: EventType.AUTHENTICATION_SUCCESS,\r\n        severity: EventSeverity.LOW,\r\n        status: EventStatus.RECEIVED,\r\n        payload: { username: 'testuser' },\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      eventRepository.setErrorScenario('save');\r\n\r\n      // Act\r\n      const result = await orchestratorService.orchestrateSecurityWorkflow([event], {\r\n        priority: 'LOW' as any,\r\n      });\r\n\r\n      // Assert\r\n      expect(result.isSuccess()).toBe(true);\r\n      const orchestrationResult = result.getValue();\r\n      expect(orchestrationResult.success).toBe(false);\r\n      expect(orchestrationResult.errors.length).toBeGreaterThan(0);\r\n      expect(orchestrationResult.errors[0].errorCode).toBe('ORCHESTRATION_FAILED');\r\n    });\r\n\r\n    it('should handle repository not found errors', async () => {\r\n      // Arrange\r\n      const nonExistentId = UniqueEntityId.create();\r\n      eventRepository.setErrorScenario('findById');\r\n\r\n      // Act & Assert\r\n      const event = await eventRepository.findById(nonExistentId);\r\n      expect(event).toBeNull(); // Should handle gracefully, not throw\r\n    });\r\n\r\n    it('should handle repository permission errors', async () => {\r\n      // Arrange\r\n      const event = EventFactory.create({\r\n        metadata: EventMetadata.create({ source: 'test', version: '1.0' }),\r\n        timestamp: EventTimestamp.create(new Date()),\r\n        source: EventSource.create({\r\n          type: EventSourceType.APPLICATION,\r\n          identifier: 'app-001',\r\n          name: 'Test App',\r\n        }),\r\n        type: EventType.SECURITY_ALERT,\r\n        severity: EventSeverity.MEDIUM,\r\n        status: EventStatus.RECEIVED,\r\n        payload: { alertType: 'test' },\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      await eventRepository.save(event);\r\n      eventRepository.setErrorScenario('delete');\r\n\r\n      // Act & Assert\r\n      await expect(eventRepository.delete(event.id)).rejects.toThrow(ForbiddenException);\r\n    });\r\n\r\n    it('should handle repository service unavailable errors', async () => {\r\n      // Arrange\r\n      eventRepository.setErrorScenario('findAll');\r\n\r\n      // Act & Assert\r\n      await expect(eventRepository.findAll()).rejects.toThrow(ServiceUnavailableException);\r\n    });\r\n  });\r\n\r\n  describe('Service Layer Error Handling', () => {\r\n    it('should handle event processing validation errors', async () => {\r\n      // Arrange\r\n      const event = EventFactory.create({\r\n        metadata: EventMetadata.create({ source: 'test', version: '1.0' }),\r\n        timestamp: EventTimestamp.create(new Date()),\r\n        source: EventSource.create({\r\n          type: EventSourceType.APPLICATION,\r\n          identifier: 'app-001',\r\n          name: 'Test App',\r\n        }),\r\n        type: EventType.AUTHENTICATION_FAILURE,\r\n        severity: EventSeverity.MEDIUM,\r\n        status: EventStatus.RECEIVED,\r\n        payload: { username: 'testuser' },\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      eventProcessor.setErrorScenario('processEvent');\r\n\r\n      // Act\r\n      const result = await orchestratorService.processSecurityEvents([event], {\r\n        priority: 'MEDIUM' as any,\r\n      });\r\n\r\n      // Assert\r\n      expect(result.isSuccess()).toBe(true);\r\n      const processingResult = result.getValue();\r\n      expect(processingResult.success).toBe(false);\r\n      expect(processingResult.errors.length).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should handle threat detection service errors', async () => {\r\n      // Arrange\r\n      const event = EventFactory.create({\r\n        metadata: EventMetadata.create({ source: 'test', version: '1.0' }),\r\n        timestamp: EventTimestamp.create(new Date()),\r\n        source: EventSource.create({\r\n          type: EventSourceType.APPLICATION,\r\n          identifier: 'app-001',\r\n          name: 'Test App',\r\n        }),\r\n        type: EventType.NETWORK_INTRUSION,\r\n        severity: EventSeverity.HIGH,\r\n        status: EventStatus.RECEIVED,\r\n        payload: { sourceIp: '*************' },\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      threatDetector.setErrorScenario('analyzeEvent');\r\n\r\n      // Act\r\n      const result = await orchestratorService.orchestrateSecurityWorkflow([event], {\r\n        priority: 'HIGH' as any,\r\n        enableThreatHunting: true,\r\n      });\r\n\r\n      // Assert\r\n      expect(result.isSuccess()).toBe(true);\r\n      const orchestrationResult = result.getValue();\r\n      \r\n      // Should continue processing despite threat detection failure\r\n      expect(orchestrationResult.eventsProcessed).toBe(1);\r\n      expect(orchestrationResult.errors.length).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should handle rate limiting errors', async () => {\r\n      // Arrange\r\n      const events = Array.from({ length: 5 }, (_, i) => \r\n        EventFactory.create({\r\n          metadata: EventMetadata.create({ source: `source-${i}`, version: '1.0' }),\r\n          timestamp: EventTimestamp.create(new Date()),\r\n          source: EventSource.create({\r\n            type: EventSourceType.APPLICATION,\r\n            identifier: `app-${i}`,\r\n            name: `App ${i}`,\r\n          }),\r\n          type: EventType.AUTHENTICATION_FAILURE,\r\n          severity: EventSeverity.MEDIUM,\r\n          status: EventStatus.RECEIVED,\r\n          payload: { attempt: i },\r\n          tenantId: TenantId.create(),\r\n          userId: UserId.create(),\r\n        })\r\n      );\r\n\r\n      eventProcessor.setErrorScenario('rateLimit', 3); // First 3 calls will fail\r\n\r\n      // Act\r\n      const result = await orchestratorService.orchestrateSecurityWorkflow(events, {\r\n        priority: 'MEDIUM' as any,\r\n        workflowConfig: {\r\n          enableEventProcessing: true,\r\n          enableThreatDetection: false,\r\n          enableVulnerabilityScanning: false,\r\n          enableResponseExecution: false,\r\n          enableCorrelation: false,\r\n          enableEnrichment: false,\r\n          batchSize: 1,\r\n          maxConcurrentOperations: 1,\r\n          retryAttempts: 1,\r\n          timeoutMs: 60000,\r\n        },\r\n      });\r\n\r\n      // Assert\r\n      expect(result.isSuccess()).toBe(true);\r\n      const orchestrationResult = result.getValue();\r\n      \r\n      // Should have processed some events successfully after rate limit errors\r\n      expect(orchestrationResult.eventsProcessed).toBeGreaterThan(0);\r\n      expect(orchestrationResult.errors.length).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should handle unauthorized access errors', async () => {\r\n      // Arrange\r\n      const event = EventFactory.create({\r\n        metadata: EventMetadata.create({ source: 'restricted', version: '1.0' }),\r\n        timestamp: EventTimestamp.create(new Date()),\r\n        source: EventSource.create({\r\n          type: EventSourceType.APPLICATION,\r\n          identifier: 'restricted-app',\r\n          name: 'Restricted App',\r\n        }),\r\n        type: EventType.SECURITY_ALERT,\r\n        severity: EventSeverity.HIGH,\r\n        status: EventStatus.RECEIVED,\r\n        payload: { alertType: 'restricted' },\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      eventProcessor.setErrorScenario('unauthorized');\r\n\r\n      // Act\r\n      const result = await orchestratorService.processSecurityEvents([event], {\r\n        priority: 'HIGH' as any,\r\n      });\r\n\r\n      // Assert\r\n      expect(result.isSuccess()).toBe(true);\r\n      const processingResult = result.getValue();\r\n      expect(processingResult.success).toBe(false);\r\n      expect(processingResult.errors.length).toBeGreaterThan(0);\r\n    });\r\n  });\r\n\r\n  describe('Error Recovery and Resilience', () => {\r\n    it('should implement circuit breaker pattern for failing services', async () => {\r\n      // Arrange\r\n      const events = Array.from({ length: 10 }, (_, i) => \r\n        EventFactory.create({\r\n          metadata: EventMetadata.create({ source: `cb-test-${i}`, version: '1.0' }),\r\n          timestamp: EventTimestamp.create(new Date()),\r\n          source: EventSource.create({\r\n            type: EventSourceType.APPLICATION,\r\n            identifier: `cb-app-${i}`,\r\n            name: `Circuit Breaker App ${i}`,\r\n          }),\r\n          type: EventType.AUTHENTICATION_FAILURE,\r\n          severity: EventSeverity.MEDIUM,\r\n          status: EventStatus.RECEIVED,\r\n          payload: { attempt: i },\r\n          tenantId: TenantId.create(),\r\n          userId: UserId.create(),\r\n        })\r\n      );\r\n\r\n      // Configure threat detector to fail consistently\r\n      threatDetector.setErrorScenario('analyzeEvent', 10);\r\n\r\n      // Act\r\n      const result = await orchestratorService.orchestrateSecurityWorkflow(events, {\r\n        priority: 'MEDIUM' as any,\r\n        workflowConfig: {\r\n          enableEventProcessing: true,\r\n          enableThreatDetection: true,\r\n          enableVulnerabilityScanning: false,\r\n          enableResponseExecution: false,\r\n          enableCorrelation: false,\r\n          enableEnrichment: false,\r\n          batchSize: 2,\r\n          maxConcurrentOperations: 3,\r\n          retryAttempts: 1,\r\n          timeoutMs: 60000,\r\n        },\r\n      });\r\n\r\n      // Assert\r\n      expect(result.isSuccess()).toBe(true);\r\n      const orchestrationResult = result.getValue();\r\n      \r\n      // Should continue processing events despite threat detection failures\r\n      expect(orchestrationResult.eventsProcessed).toBe(10);\r\n      expect(orchestrationResult.processingResults).toHaveLength(10);\r\n      \r\n      // Should have errors from threat detection\r\n      expect(orchestrationResult.errors.length).toBeGreaterThan(0);\r\n      \r\n      // But overall processing should succeed\r\n      orchestrationResult.processingResults.forEach(pr => {\r\n        expect(pr.success).toBe(true);\r\n      });\r\n    });\r\n\r\n    it('should handle partial failures in batch processing', async () => {\r\n      // Arrange\r\n      const events = Array.from({ length: 6 }, (_, i) => \r\n        EventFactory.create({\r\n          metadata: EventMetadata.create({ source: `batch-${i}`, version: '1.0' }),\r\n          timestamp: EventTimestamp.create(new Date()),\r\n          source: EventSource.create({\r\n            type: EventSourceType.APPLICATION,\r\n            identifier: `batch-app-${i}`,\r\n            name: `Batch App ${i}`,\r\n          }),\r\n          type: EventType.SECURITY_ALERT,\r\n          severity: EventSeverity.LOW,\r\n          status: EventStatus.RECEIVED,\r\n          payload: { batchIndex: i },\r\n          tenantId: TenantId.create(),\r\n          userId: UserId.create(),\r\n        })\r\n      );\r\n\r\n      // Configure processor to fail on first 2 events\r\n      eventProcessor.setErrorScenario('processEvent', 2);\r\n\r\n      // Act\r\n      const result = await orchestratorService.orchestrateSecurityWorkflow(events, {\r\n        priority: 'LOW' as any,\r\n        workflowConfig: {\r\n          enableEventProcessing: true,\r\n          enableThreatDetection: false,\r\n          enableVulnerabilityScanning: false,\r\n          enableResponseExecution: false,\r\n          enableCorrelation: false,\r\n          enableEnrichment: false,\r\n          batchSize: 3,\r\n          maxConcurrentOperations: 2,\r\n          retryAttempts: 1,\r\n          timeoutMs: 60000,\r\n        },\r\n      });\r\n\r\n      // Assert\r\n      expect(result.isSuccess()).toBe(true);\r\n      const orchestrationResult = result.getValue();\r\n      \r\n      // Should have processed some events successfully\r\n      expect(orchestrationResult.eventsProcessed).toBeGreaterThan(0);\r\n      expect(orchestrationResult.eventsProcessed).toBeLessThan(6); // Some failed\r\n      \r\n      // Should have both successful and failed processing results\r\n      expect(orchestrationResult.processingResults.length).toBeGreaterThan(0);\r\n      expect(orchestrationResult.errors.length).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should implement exponential backoff for retries', async () => {\r\n      // Arrange\r\n      const event = EventFactory.create({\r\n        metadata: EventMetadata.create({ source: 'retry-test', version: '1.0' }),\r\n        timestamp: EventTimestamp.create(new Date()),\r\n        source: EventSource.create({\r\n          type: EventSourceType.APPLICATION,\r\n          identifier: 'retry-app',\r\n          name: 'Retry Test App',\r\n        }),\r\n        type: EventType.AUTHENTICATION_FAILURE,\r\n        severity: EventSeverity.MEDIUM,\r\n        status: EventStatus.RECEIVED,\r\n        payload: { username: 'retryuser' },\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      let callCount = 0;\r\n      const callTimes: number[] = [];\r\n      \r\n      const originalProcessEvent = eventProcessor.processEvent.bind(eventProcessor);\r\n      eventProcessor.processEvent = jest.fn().mockImplementation(async (event, context) => {\r\n        callTimes.push(Date.now());\r\n        callCount++;\r\n        if (callCount <= 2) {\r\n          throw new ServiceUnavailableException('Transient failure');\r\n        }\r\n        return originalProcessEvent(event, context);\r\n      });\r\n\r\n      // Act\r\n      const startTime = Date.now();\r\n      const result = await orchestratorService.processSecurityEvents([event], {\r\n        priority: 'MEDIUM' as any,\r\n        workflowConfig: {\r\n          enableEventProcessing: true,\r\n          enableThreatDetection: false,\r\n          enableVulnerabilityScanning: false,\r\n          enableResponseExecution: false,\r\n          enableCorrelation: false,\r\n          enableEnrichment: false,\r\n          batchSize: 1,\r\n          maxConcurrentOperations: 1,\r\n          retryAttempts: 3,\r\n          timeoutMs: 60000,\r\n        },\r\n      });\r\n\r\n      // Assert\r\n      expect(result.isSuccess()).toBe(true);\r\n      const processingResult = result.getValue();\r\n      \r\n      expect(processingResult.success).toBe(true);\r\n      expect(processingResult.eventsProcessed).toBe(1);\r\n      expect(callCount).toBe(3); // Initial call + 2 retries\r\n      \r\n      // Verify exponential backoff (approximate timing)\r\n      if (callTimes.length >= 3) {\r\n        const firstRetryDelay = callTimes[1] - callTimes[0];\r\n        const secondRetryDelay = callTimes[2] - callTimes[1];\r\n        expect(secondRetryDelay).toBeGreaterThan(firstRetryDelay);\r\n      }\r\n    });\r\n  });\r\n\r\n  describe('Error Propagation and Context', () => {\r\n    it('should maintain error context through service layers', async () => {\r\n      // Arrange\r\n      const tenantId = TenantId.create();\r\n      const userId = UserId.create();\r\n      \r\n      const event = EventFactory.create({\r\n        metadata: EventMetadata.create({ source: 'context-test', version: '1.0' }),\r\n        timestamp: EventTimestamp.create(new Date()),\r\n        source: EventSource.create({\r\n          type: EventSourceType.APPLICATION,\r\n          identifier: 'context-app',\r\n          name: 'Context Test App',\r\n        }),\r\n        type: EventType.SECURITY_ALERT,\r\n        severity: EventSeverity.HIGH,\r\n        status: EventStatus.RECEIVED,\r\n        payload: { alertType: 'context-test' },\r\n        tenantId,\r\n        userId,\r\n      });\r\n\r\n      eventProcessor.setErrorScenario('processEvent');\r\n\r\n      // Act\r\n      const result = await orchestratorService.orchestrateSecurityWorkflow([event], {\r\n        tenantId: tenantId.value,\r\n        userId: userId.value,\r\n        priority: 'HIGH' as any,\r\n      });\r\n\r\n      // Assert\r\n      expect(result.isSuccess()).toBe(true);\r\n      const orchestrationResult = result.getValue();\r\n      \r\n      expect(orchestrationResult.success).toBe(false);\r\n      expect(orchestrationResult.errors.length).toBeGreaterThan(0);\r\n      \r\n      const error = orchestrationResult.errors[0];\r\n      expect(error.eventId).toBeDefined();\r\n      expect(error.timestamp).toBeInstanceOf(Date);\r\n      expect(error.stage).toBeDefined();\r\n      expect(error.errorCode).toBeDefined();\r\n      expect(error.message).toBeDefined();\r\n    });\r\n\r\n    it('should handle cascading errors gracefully', async () => {\r\n      // Arrange\r\n      const event = EventFactory.create({\r\n        metadata: EventMetadata.create({ source: 'cascade-test', version: '1.0' }),\r\n        timestamp: EventTimestamp.create(new Date()),\r\n        source: EventSource.create({\r\n          type: EventSourceType.APPLICATION,\r\n          identifier: 'cascade-app',\r\n          name: 'Cascade Test App',\r\n        }),\r\n        type: EventType.NETWORK_INTRUSION,\r\n        severity: EventSeverity.CRITICAL,\r\n        status: EventStatus.RECEIVED,\r\n        payload: { sourceIp: '*************' },\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      // Configure multiple services to fail\r\n      eventProcessor.setErrorScenario('processEvent');\r\n      threatDetector.setErrorScenario('analyzeEvent');\r\n\r\n      // Act\r\n      const result = await orchestratorService.orchestrateSecurityWorkflow([event], {\r\n        priority: 'CRITICAL' as any,\r\n        autoResponse: false,\r\n      });\r\n\r\n      // Assert\r\n      expect(result.isSuccess()).toBe(true);\r\n      const orchestrationResult = result.getValue();\r\n      \r\n      expect(orchestrationResult.success).toBe(false);\r\n      expect(orchestrationResult.errors.length).toBeGreaterThan(0);\r\n      \r\n      // Should have errors from multiple stages\r\n      const errorStages = orchestrationResult.errors.map(e => e.stage);\r\n      expect(errorStages.length).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should provide detailed error information for debugging', async () => {\r\n      // Arrange\r\n      const event = EventFactory.create({\r\n        metadata: EventMetadata.create({ source: 'debug-test', version: '1.0' }),\r\n        timestamp: EventTimestamp.create(new Date()),\r\n        source: EventSource.create({\r\n          type: EventSourceType.APPLICATION,\r\n          identifier: 'debug-app',\r\n          name: 'Debug Test App',\r\n        }),\r\n        type: EventType.AUTHENTICATION_FAILURE,\r\n        severity: EventSeverity.MEDIUM,\r\n        status: EventStatus.RECEIVED,\r\n        payload: { username: 'debuguser' },\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      eventRepository.setErrorScenario('save');\r\n\r\n      // Act\r\n      const result = await orchestratorService.orchestrateSecurityWorkflow([event], {\r\n        priority: 'MEDIUM' as any,\r\n      });\r\n\r\n      // Assert\r\n      expect(result.isSuccess()).toBe(true);\r\n      const orchestrationResult = result.getValue();\r\n      \r\n      expect(orchestrationResult.success).toBe(false);\r\n      expect(orchestrationResult.errors.length).toBeGreaterThan(0);\r\n      \r\n      const error = orchestrationResult.errors[0];\r\n      expect(error.errorCode).toBe('ORCHESTRATION_FAILED');\r\n      expect(error.message).toContain('Event already exists');\r\n      expect(error.recoverable).toBe(false);\r\n      expect(error.timestamp).toBeInstanceOf(Date);\r\n    });\r\n  });\r\n});"], "version": 3}