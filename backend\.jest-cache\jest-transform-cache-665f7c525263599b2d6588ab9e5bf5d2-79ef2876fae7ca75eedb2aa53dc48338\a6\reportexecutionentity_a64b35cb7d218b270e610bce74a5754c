a029cae0756253b59bbcf480eb599f8e
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReportExecution = void 0;
const typeorm_1 = require("typeorm");
const report_entity_1 = require("./report.entity");
/**
 * Report Execution entity
 * Represents individual executions of reports with their results and metadata
 */
let ReportExecution = class ReportExecution {
    /**
     * Check if execution is running
     */
    get isRunning() {
        return ['pending', 'running'].includes(this.status);
    }
    /**
     * Check if execution is completed
     */
    get isCompleted() {
        return this.status === 'completed';
    }
    /**
     * Check if execution failed
     */
    get isFailed() {
        return ['failed', 'cancelled', 'timeout'].includes(this.status);
    }
    /**
     * Get execution duration in seconds
     */
    get durationSeconds() {
        return this.durationMs ? Math.round(this.durationMs / 1000) : null;
    }
    /**
     * Get human-readable duration
     */
    get durationFormatted() {
        if (!this.durationMs)
            return 'N/A';
        const seconds = Math.floor(this.durationMs / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        if (hours > 0) {
            return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
        }
        else if (minutes > 0) {
            return `${minutes}m ${seconds % 60}s`;
        }
        else {
            return `${seconds}s`;
        }
    }
    /**
     * Get output size in human-readable format
     */
    get outputSizeFormatted() {
        if (!this.outputSizeBytes)
            return 'N/A';
        const bytes = Number(this.outputSizeBytes);
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
    }
    /**
     * Start execution
     */
    start() {
        this.status = 'running';
        this.startedAt = new Date();
        this.progress = {
            percentage: 0,
            currentStep: 'Initializing',
            totalSteps: 1,
            lastUpdate: new Date().toISOString(),
        };
    }
    /**
     * Update execution progress
     */
    updateProgress(percentage, currentStep, totalSteps) {
        if (!this.progress) {
            this.progress = {
                percentage: 0,
                currentStep: 'Starting',
                totalSteps: totalSteps || 1,
                lastUpdate: new Date().toISOString(),
            };
        }
        this.progress.percentage = Math.min(100, Math.max(0, percentage));
        this.progress.currentStep = currentStep;
        if (totalSteps) {
            this.progress.totalSteps = totalSteps;
        }
        this.progress.lastUpdate = new Date().toISOString();
        // Estimate time remaining
        if (this.startedAt && percentage > 0) {
            const elapsed = Date.now() - this.startedAt.getTime();
            const estimated = (elapsed / percentage) * (100 - percentage);
            this.progress.estimatedTimeRemaining = Math.round(estimated);
        }
    }
    /**
     * Complete execution successfully
     */
    complete(outputPath, outputFormat, reportData) {
        this.status = 'completed';
        this.completedAt = new Date();
        this.outputPath = outputPath;
        this.outputFormat = outputFormat;
        this.reportData = reportData;
        if (this.startedAt) {
            this.durationMs = this.completedAt.getTime() - this.startedAt.getTime();
        }
        if (this.progress) {
            this.progress.percentage = 100;
            this.progress.currentStep = 'Completed';
            this.progress.lastUpdate = new Date().toISOString();
        }
    }
    /**
     * Fail execution with error details
     */
    fail(error, code) {
        this.status = 'failed';
        this.completedAt = new Date();
        if (this.startedAt) {
            this.durationMs = this.completedAt.getTime() - this.startedAt.getTime();
        }
        this.errorDetails = {
            message: typeof error === 'string' ? error : error.message,
            code,
            stack: typeof error === 'object' ? error.stack : undefined,
            timestamp: new Date().toISOString(),
        };
    }
    /**
     * Cancel execution
     */
    cancel(reason) {
        this.status = 'cancelled';
        this.completedAt = new Date();
        if (this.startedAt) {
            this.durationMs = this.completedAt.getTime() - this.startedAt.getTime();
        }
        this.errorDetails = {
            message: reason || 'Execution cancelled by user',
            code: 'CANCELLED',
            timestamp: new Date().toISOString(),
        };
    }
    /**
     * Mark execution as timed out
     */
    timeout() {
        this.status = 'timeout';
        this.completedAt = new Date();
        if (this.startedAt) {
            this.durationMs = this.completedAt.getTime() - this.startedAt.getTime();
        }
        this.errorDetails = {
            message: 'Execution timed out',
            code: 'TIMEOUT',
            timestamp: new Date().toISOString(),
        };
    }
    /**
     * Add quality metric
     */
    addQualityMetric(metric, value) {
        if (!this.qualityMetrics) {
            this.qualityMetrics = {
                dataCompleteness: 1,
                dataAccuracy: 1,
                performanceScore: 1,
            };
        }
        this.qualityMetrics[metric] = value;
    }
    /**
     * Add quality issue
     */
    addQualityIssue(type, message, severity) {
        if (!this.qualityMetrics) {
            this.qualityMetrics = {
                dataCompleteness: 1,
                dataAccuracy: 1,
                performanceScore: 1,
            };
        }
        if (!this.qualityMetrics.issues) {
            this.qualityMetrics.issues = [];
        }
        this.qualityMetrics.issues.push({
            type,
            message,
            severity,
        });
    }
};
exports.ReportExecution = ReportExecution;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ReportExecution.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['pending', 'running', 'completed', 'failed', 'cancelled', 'timeout'],
        default: 'pending',
    }),
    __metadata("design:type", String)
], ReportExecution.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'trigger_type',
        type: 'enum',
        enum: ['manual', 'scheduled', 'api', 'webhook'],
        default: 'manual',
    }),
    __metadata("design:type", String)
], ReportExecution.prototype, "triggerType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ReportExecution.prototype, "parameters", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'started_at', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], ReportExecution.prototype, "startedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'completed_at', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], ReportExecution.prototype, "completedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'duration_ms', type: 'integer', nullable: true }),
    __metadata("design:type", Number)
], ReportExecution.prototype, "durationMs", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'records_processed', type: 'integer', nullable: true }),
    __metadata("design:type", Number)
], ReportExecution.prototype, "recordsProcessed", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'output_size_bytes', type: 'bigint', nullable: true }),
    __metadata("design:type", Number)
], ReportExecution.prototype, "outputSizeBytes", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'output_format', nullable: true }),
    __metadata("design:type", String)
], ReportExecution.prototype, "outputFormat", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'output_path', nullable: true }),
    __metadata("design:type", String)
], ReportExecution.prototype, "outputPath", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'report_data', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ReportExecution.prototype, "reportData", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ReportExecution.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'error_details', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ReportExecution.prototype, "errorDetails", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ReportExecution.prototype, "progress", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'quality_metrics', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ReportExecution.prototype, "qualityMetrics", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'executed_by', type: 'uuid' }),
    __metadata("design:type", String)
], ReportExecution.prototype, "executedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'executor_ip', nullable: true }),
    __metadata("design:type", String)
], ReportExecution.prototype, "executorIp", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'executor_user_agent', nullable: true }),
    __metadata("design:type", String)
], ReportExecution.prototype, "executorUserAgent", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], ReportExecution.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], ReportExecution.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => report_entity_1.Report, report => report.executions),
    (0, typeorm_1.JoinColumn)({ name: 'report_id' }),
    __metadata("design:type", typeof (_e = typeof report_entity_1.Report !== "undefined" && report_entity_1.Report) === "function" ? _e : Object)
], ReportExecution.prototype, "report", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'report_id', type: 'uuid' }),
    __metadata("design:type", String)
], ReportExecution.prototype, "reportId", void 0);
exports.ReportExecution = ReportExecution = __decorate([
    (0, typeorm_1.Entity)('report_executions'),
    (0, typeorm_1.Index)(['reportId']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['executedBy']),
    (0, typeorm_1.Index)(['startedAt']),
    (0, typeorm_1.Index)(['completedAt'])
], ReportExecution);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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