5d630a92fb7a2be6e1a3b9a08004beec
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnrichedEventCreatedDomainEvent = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
const event_severity_enum_1 = require("../enums/event-severity.enum");
const enriched_event_entity_1 = require("../entities/enriched-event.entity");
/**
 * Enriched Event Created Domain Event
 *
 * Raised when a new enriched security event is created in the system.
 * This event triggers various downstream processes including:
 * - Correlation pipeline initiation
 * - Threat analysis workflows
 * - Manual review queue management
 * - Metrics collection and monitoring
 * - Audit logging and compliance tracking
 */
class EnrichedEventCreatedDomainEvent extends shared_kernel_1.BaseDomainEvent {
    constructor(aggregateId, eventData, options) {
        super(aggregateId, eventData, options);
    }
    /**
     * Get the normalized event ID
     */
    get normalizedEventId() {
        return this.eventData.normalizedEventId;
    }
    /**
     * Get the type of the enriched event
     */
    get eventType() {
        return this.eventData.eventType;
    }
    /**
     * Get the severity of the enriched event
     */
    get severity() {
        return this.eventData.severity;
    }
    /**
     * Get the enrichment status
     */
    get enrichmentStatus() {
        return this.eventData.enrichmentStatus;
    }
    /**
     * Get the enrichment quality score
     */
    get enrichmentQualityScore() {
        return this.eventData.enrichmentQualityScore;
    }
    /**
     * Get the number of applied rules
     */
    get appliedRulesCount() {
        return this.eventData.appliedRulesCount;
    }
    /**
     * Get the number of enrichment data sources
     */
    get enrichmentDataCount() {
        return this.eventData.enrichmentDataCount;
    }
    /**
     * Get the threat intelligence score
     */
    get threatIntelScore() {
        return this.eventData.threatIntelScore;
    }
    /**
     * Check if the event requires manual review
     */
    get requiresManualReview() {
        return this.eventData.requiresManualReview;
    }
    /**
     * Check if the enriched event is high severity
     */
    isHighSeverity() {
        return [event_severity_enum_1.EventSeverity.HIGH, event_severity_enum_1.EventSeverity.CRITICAL].includes(this.severity);
    }
    /**
     * Check if the enriched event is critical
     */
    isCritical() {
        return this.severity === event_severity_enum_1.EventSeverity.CRITICAL;
    }
    /**
     * Check if the event has high enrichment quality
     */
    hasHighEnrichmentQuality() {
        return (this.enrichmentQualityScore || 0) >= 70;
    }
    /**
     * Check if the event has threat intelligence data
     */
    hasThreatIntelligence() {
        return this.threatIntelScore !== undefined && this.threatIntelScore > 0;
    }
    /**
     * Check if enrichment is completed
     */
    isEnrichmentCompleted() {
        return this.enrichmentStatus === enriched_event_entity_1.EnrichmentStatus.COMPLETED;
    }
    /**
     * Check if the event has high threat intelligence score
     */
    isHighThreatRisk() {
        return (this.threatIntelScore || 0) >= 85;
    }
    /**
     * Get event summary for handlers
     */
    getEventSummary() {
        return {
            enrichedEventId: this.aggregateId.toString(),
            normalizedEventId: this.normalizedEventId.toString(),
            eventType: this.eventType,
            severity: this.severity,
            enrichmentStatus: this.enrichmentStatus,
            enrichmentQualityScore: this.enrichmentQualityScore,
            appliedRulesCount: this.appliedRulesCount,
            enrichmentDataCount: this.enrichmentDataCount,
            threatIntelScore: this.threatIntelScore,
            requiresManualReview: this.requiresManualReview,
            isHighSeverity: this.isHighSeverity(),
            isCritical: this.isCritical(),
            hasHighEnrichmentQuality: this.hasHighEnrichmentQuality(),
            hasThreatIntelligence: this.hasThreatIntelligence(),
            isEnrichmentCompleted: this.isEnrichmentCompleted(),
            isHighThreatRisk: this.isHighThreatRisk(),
        };
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            ...super.toJSON(),
            eventSummary: this.getEventSummary(),
        };
    }
}
exports.EnrichedEventCreatedDomainEvent = EnrichedEventCreatedDomainEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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