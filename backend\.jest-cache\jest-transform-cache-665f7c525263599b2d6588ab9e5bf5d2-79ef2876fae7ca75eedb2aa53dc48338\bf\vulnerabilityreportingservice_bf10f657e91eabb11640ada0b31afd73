c0e38dfb3a9aec7217c68a02d23b4a6a
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var VulnerabilityReportingService_1;
var _a, _b, _c, _d, _e, _f;
Object.defineProperty(exports, "__esModule", { value: true });
exports.VulnerabilityReportingService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const vulnerability_entity_1 = require("../../domain/entities/vulnerability.entity");
const vulnerability_assessment_entity_1 = require("../../domain/entities/vulnerability-assessment.entity");
const vulnerability_scan_entity_1 = require("../../domain/entities/vulnerability-scan.entity");
const vulnerability_exception_entity_1 = require("../../domain/entities/vulnerability-exception.entity");
const asset_entity_1 = require("../../../asset-management/domain/entities/asset.entity");
const logger_service_1 = require("../../../../infrastructure/logging/logger.service");
/**
 * Vulnerability Reporting service
 * Handles vulnerability reporting and analytics
 */
let VulnerabilityReportingService = VulnerabilityReportingService_1 = class VulnerabilityReportingService {
    constructor(vulnerabilityRepository, assessmentRepository, scanRepository, exceptionRepository, assetRepository, loggerService) {
        this.vulnerabilityRepository = vulnerabilityRepository;
        this.assessmentRepository = assessmentRepository;
        this.scanRepository = scanRepository;
        this.exceptionRepository = exceptionRepository;
        this.assetRepository = assetRepository;
        this.loggerService = loggerService;
        this.logger = new common_1.Logger(VulnerabilityReportingService_1.name);
    }
    /**
     * Generate executive summary report
     */
    async generateExecutiveSummary(dateRange) {
        try {
            this.logger.debug('Generating executive summary report', {
                startDate: dateRange.startDate,
                endDate: dateRange.endDate,
            });
            // Get vulnerability metrics
            const vulnerabilityMetrics = await this.getVulnerabilityMetrics(dateRange);
            // Get scan metrics
            const scanMetrics = await this.getScanMetrics(dateRange);
            // Get assessment metrics
            const assessmentMetrics = await this.getAssessmentMetrics(dateRange);
            // Get risk trends
            const riskTrends = await this.getRiskTrends(dateRange);
            // Get top risks
            const topRisks = await this.getTopRisks();
            // Get remediation metrics
            const remediationMetrics = await this.getRemediationMetrics(dateRange);
            const report = {
                reportType: 'executive_summary',
                generatedAt: new Date(),
                dateRange,
                summary: {
                    totalVulnerabilities: vulnerabilityMetrics.total,
                    criticalVulnerabilities: vulnerabilityMetrics.critical,
                    highVulnerabilities: vulnerabilityMetrics.high,
                    newVulnerabilities: vulnerabilityMetrics.new,
                    patchedVulnerabilities: vulnerabilityMetrics.patched,
                    totalScans: scanMetrics.total,
                    successfulScans: scanMetrics.successful,
                    totalAssessments: assessmentMetrics.total,
                    completedAssessments: assessmentMetrics.completed,
                },
                metrics: {
                    vulnerabilities: vulnerabilityMetrics,
                    scans: scanMetrics,
                    assessments: assessmentMetrics,
                    remediation: remediationMetrics,
                },
                trends: riskTrends,
                topRisks,
                recommendations: this.generateRecommendations(vulnerabilityMetrics, scanMetrics, assessmentMetrics),
            };
            this.logger.log('Executive summary report generated successfully', {
                totalVulnerabilities: vulnerabilityMetrics.total,
                criticalCount: vulnerabilityMetrics.critical,
                dateRange,
            });
            return report;
        }
        catch (error) {
            this.logger.error('Failed to generate executive summary report', {
                error: error.message,
                dateRange,
            });
            throw error;
        }
    }
    /**
     * Generate detailed vulnerability report
     */
    async generateDetailedVulnerabilityReport(criteria) {
        try {
            this.logger.debug('Generating detailed vulnerability report', {
                dateRange: criteria.dateRange,
                severities: criteria.severities,
                assetCount: criteria.assetIds?.length || 0,
            });
            const queryBuilder = this.vulnerabilityRepository
                .createQueryBuilder('vuln')
                .leftJoinAndSelect('vuln.affectedAssets', 'assets');
            if (criteria.includeAssessments) {
                queryBuilder.leftJoinAndSelect('vuln.assessments', 'assessments');
            }
            if (criteria.includeExceptions) {
                queryBuilder.leftJoinAndSelect('vuln.exceptions', 'exceptions');
            }
            // Apply filters
            queryBuilder.andWhere('vuln.publishedDate BETWEEN :startDate AND :endDate', {
                startDate: criteria.dateRange.startDate,
                endDate: criteria.dateRange.endDate,
            });
            if (criteria.severities?.length) {
                queryBuilder.andWhere('vuln.severity IN (:...severities)', {
                    severities: criteria.severities
                });
            }
            if (criteria.assetIds?.length) {
                queryBuilder.andWhere('assets.id IN (:...assetIds)', {
                    assetIds: criteria.assetIds
                });
            }
            queryBuilder.orderBy('vuln.severity', 'DESC')
                .addOrderBy('vuln.cvssScore', 'DESC')
                .addOrderBy('vuln.publishedDate', 'DESC');
            const vulnerabilities = await queryBuilder.getMany();
            const report = {
                reportType: 'detailed_vulnerability',
                generatedAt: new Date(),
                criteria,
                summary: {
                    totalVulnerabilities: vulnerabilities.length,
                    bySeverity: this.groupBySeverity(vulnerabilities),
                    byProduct: this.groupByProduct(vulnerabilities),
                    withExploits: vulnerabilities.filter(v => v.hasExploit).length,
                    inTheWild: vulnerabilities.filter(v => v.inTheWild).length,
                    withPatches: vulnerabilities.filter(v => v.patchAvailable).length,
                },
                vulnerabilities: vulnerabilities.map(v => v.exportForReporting()),
            };
            this.logger.log('Detailed vulnerability report generated successfully', {
                vulnerabilityCount: vulnerabilities.length,
                dateRange: criteria.dateRange,
            });
            return report;
        }
        catch (error) {
            this.logger.error('Failed to generate detailed vulnerability report', {
                error: error.message,
                criteria,
            });
            throw error;
        }
    }
    /**
     * Generate scan performance report
     */
    async generateScanPerformanceReport(dateRange) {
        try {
            this.logger.debug('Generating scan performance report', {
                dateRange,
            });
            const scans = await this.scanRepository.find({
                where: {
                    startedAt: (0, typeorm_2.Between)(dateRange.startDate, dateRange.endDate),
                },
                order: { startedAt: 'DESC' },
            });
            const scanMetrics = {
                total: scans.length,
                completed: scans.filter(s => s.isCompleted).length,
                failed: scans.filter(s => s.isFailed).length,
                running: scans.filter(s => s.isRunning).length,
                byType: this.groupScansByType(scans),
                byScanner: this.groupScansByScanner(scans),
                averageDuration: this.calculateAverageDuration(scans),
                totalVulnerabilitiesFound: scans.reduce((sum, s) => sum + s.totalVulnerabilities, 0),
                performanceMetrics: this.calculatePerformanceMetrics(scans),
            };
            const report = {
                reportType: 'scan_performance',
                generatedAt: new Date(),
                dateRange,
                summary: scanMetrics,
                scans: scans.map(s => s.exportForReporting()),
                trends: await this.getScanTrends(dateRange),
            };
            this.logger.log('Scan performance report generated successfully', {
                scanCount: scans.length,
                completedScans: scanMetrics.completed,
                dateRange,
            });
            return report;
        }
        catch (error) {
            this.logger.error('Failed to generate scan performance report', {
                error: error.message,
                dateRange,
            });
            throw error;
        }
    }
    /**
     * Generate compliance report
     */
    async generateComplianceReport(criteria) {
        try {
            this.logger.debug('Generating compliance report', {
                frameworks: criteria.frameworks,
                assetCount: criteria.assetIds?.length || 0,
            });
            // Get all vulnerabilities with compliance context
            const queryBuilder = this.vulnerabilityRepository
                .createQueryBuilder('vuln')
                .leftJoinAndSelect('vuln.affectedAssets', 'assets');
            if (criteria.includeExceptions) {
                queryBuilder.leftJoinAndSelect('vuln.exceptions', 'exceptions');
            }
            if (criteria.assetIds?.length) {
                queryBuilder.andWhere('assets.id IN (:...assetIds)', {
                    assetIds: criteria.assetIds
                });
            }
            const vulnerabilities = await queryBuilder.getMany();
            // Calculate compliance metrics
            const complianceMetrics = this.calculateComplianceMetrics(vulnerabilities, criteria);
            const report = {
                reportType: 'compliance',
                generatedAt: new Date(),
                criteria,
                summary: complianceMetrics.summary,
                frameworks: complianceMetrics.frameworks,
                assets: complianceMetrics.assets,
                vulnerabilities: vulnerabilities
                    .filter(v => this.isComplianceRelevant(v, criteria))
                    .map(v => v.exportForReporting()),
                recommendations: this.generateComplianceRecommendations(complianceMetrics),
            };
            this.logger.log('Compliance report generated successfully', {
                vulnerabilityCount: vulnerabilities.length,
                frameworkCount: criteria.frameworks?.length || 0,
            });
            return report;
        }
        catch (error) {
            this.logger.error('Failed to generate compliance report', {
                error: error.message,
                criteria,
            });
            throw error;
        }
    }
    /**
     * Generate asset risk report
     */
    async generateAssetRiskReport(assetIds) {
        try {
            this.logger.debug('Generating asset risk report', {
                assetCount: assetIds?.length || 0,
            });
            const queryBuilder = this.assetRepository
                .createQueryBuilder('asset')
                .leftJoinAndSelect('asset.vulnerabilities', 'vulnerabilities')
                .leftJoinAndSelect('vulnerabilities.assessments', 'assessments')
                .leftJoinAndSelect('vulnerabilities.exceptions', 'exceptions');
            if (assetIds?.length) {
                queryBuilder.andWhere('asset.id IN (:...assetIds)', { assetIds });
            }
            const assets = await queryBuilder.getMany();
            const assetRisks = assets.map(asset => this.calculateAssetRisk(asset));
            const report = {
                reportType: 'asset_risk',
                generatedAt: new Date(),
                summary: {
                    totalAssets: assets.length,
                    highRiskAssets: assetRisks.filter(a => a.riskLevel === 'high').length,
                    mediumRiskAssets: assetRisks.filter(a => a.riskLevel === 'medium').length,
                    lowRiskAssets: assetRisks.filter(a => a.riskLevel === 'low').length,
                    averageRiskScore: assetRisks.reduce((sum, a) => sum + a.riskScore, 0) / assetRisks.length,
                },
                assets: assetRisks,
                topRisks: assetRisks
                    .sort((a, b) => b.riskScore - a.riskScore)
                    .slice(0, 10),
            };
            this.logger.log('Asset risk report generated successfully', {
                assetCount: assets.length,
                highRiskCount: report.summary.highRiskAssets,
            });
            return report;
        }
        catch (error) {
            this.logger.error('Failed to generate asset risk report', {
                error: error.message,
                assetIds,
            });
            throw error;
        }
    }
    // Private helper methods
    async getVulnerabilityMetrics(dateRange) {
        const total = await this.vulnerabilityRepository.count();
        const critical = await this.vulnerabilityRepository.count({ where: { severity: 'critical' } });
        const high = await this.vulnerabilityRepository.count({ where: { severity: 'high' } });
        const newVulns = await this.vulnerabilityRepository.count({
            where: { publishedDate: (0, typeorm_2.Between)(dateRange.startDate, dateRange.endDate) },
        });
        const patched = await this.vulnerabilityRepository.count({ where: { patchAvailable: true } });
        return { total, critical, high, new: newVulns, patched };
    }
    async getScanMetrics(dateRange) {
        const total = await this.scanRepository.count({
            where: { startedAt: (0, typeorm_2.Between)(dateRange.startDate, dateRange.endDate) },
        });
        const successful = await this.scanRepository.count({
            where: {
                startedAt: (0, typeorm_2.Between)(dateRange.startDate, dateRange.endDate),
                status: 'completed',
            },
        });
        return { total, successful, failed: total - successful };
    }
    async getAssessmentMetrics(dateRange) {
        const total = await this.assessmentRepository.count({
            where: { assessedAt: (0, typeorm_2.Between)(dateRange.startDate, dateRange.endDate) },
        });
        const completed = await this.assessmentRepository.count({
            where: {
                assessedAt: (0, typeorm_2.Between)(dateRange.startDate, dateRange.endDate),
                status: 'completed',
            },
        });
        return { total, completed, pending: total - completed };
    }
    async getRiskTrends(dateRange) {
        // Implementation would calculate risk trends over time
        return [];
    }
    async getTopRisks() {
        const topVulns = await this.vulnerabilityRepository.find({
            where: [
                { severity: 'critical' },
                { severity: 'high', exploitable: true },
            ],
            order: { publishedDate: 'DESC' },
            take: 10,
        });
        return topVulns.map(v => v.getSummary());
    }
    async getRemediationMetrics(dateRange) {
        // Implementation would calculate remediation metrics
        return {
            totalRemediated: 0,
            averageRemediationTime: 0,
            remediationRate: 0,
        };
    }
    generateRecommendations(vulnMetrics, scanMetrics, assessmentMetrics) {
        const recommendations = [];
        if (vulnMetrics.critical > 0) {
            recommendations.push('Prioritize remediation of critical vulnerabilities');
        }
        if (scanMetrics.failed > scanMetrics.successful * 0.1) {
            recommendations.push('Investigate scan failures and improve scan reliability');
        }
        if (assessmentMetrics.pending > assessmentMetrics.completed * 0.2) {
            recommendations.push('Increase assessment capacity to reduce backlog');
        }
        return recommendations;
    }
    groupBySeverity(vulnerabilities) {
        return vulnerabilities.reduce((acc, vuln) => {
            acc[vuln.severity] = (acc[vuln.severity] || 0) + 1;
            return acc;
        }, {});
    }
    groupByProduct(vulnerabilities) {
        const products = {};
        vulnerabilities.forEach(vuln => {
            vuln.getAffectedProductNames().forEach(product => {
                products[product] = (products[product] || 0) + 1;
            });
        });
        return products;
    }
    groupScansByType(scans) {
        return scans.reduce((acc, scan) => {
            acc[scan.scanType] = (acc[scan.scanType] || 0) + 1;
            return acc;
        }, {});
    }
    groupScansByScanner(scans) {
        return scans.reduce((acc, scan) => {
            acc[scan.scannerType] = (acc[scan.scannerType] || 0) + 1;
            return acc;
        }, {});
    }
    calculateAverageDuration(scans) {
        const completedScans = scans.filter(s => s.durationSeconds !== null);
        if (completedScans.length === 0)
            return 0;
        const totalDuration = completedScans.reduce((sum, s) => sum + s.durationSeconds, 0);
        return totalDuration / completedScans.length;
    }
    calculatePerformanceMetrics(scans) {
        // Implementation would calculate detailed performance metrics
        return {
            averageVulnerabilitiesPerScan: 0,
            scanEfficiency: 0,
            resourceUtilization: 0,
        };
    }
    async getScanTrends(dateRange) {
        // Implementation would calculate scan trends over time
        return [];
    }
    calculateComplianceMetrics(vulnerabilities, criteria) {
        // Implementation would calculate compliance-specific metrics
        return {
            summary: {},
            frameworks: {},
            assets: {},
        };
    }
    isComplianceRelevant(vulnerability, criteria) {
        // Implementation would determine if vulnerability is relevant for compliance
        return true;
    }
    generateComplianceRecommendations(metrics) {
        // Implementation would generate compliance-specific recommendations
        return [];
    }
    calculateAssetRisk(asset) {
        const vulnerabilities = asset.vulnerabilities || [];
        const criticalCount = vulnerabilities.filter(v => v.severity === 'critical').length;
        const highCount = vulnerabilities.filter(v => v.severity === 'high').length;
        const riskScore = (criticalCount * 10) + (highCount * 7) + (vulnerabilities.length * 1);
        let riskLevel = 'low';
        if (riskScore >= 50)
            riskLevel = 'high';
        else if (riskScore >= 20)
            riskLevel = 'medium';
        return {
            assetId: asset.id,
            assetName: asset.name,
            assetType: asset.type,
            vulnerabilityCount: vulnerabilities.length,
            criticalCount,
            highCount,
            riskScore,
            riskLevel,
        };
    }
};
exports.VulnerabilityReportingService = VulnerabilityReportingService;
exports.VulnerabilityReportingService = VulnerabilityReportingService = VulnerabilityReportingService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(vulnerability_entity_1.Vulnerability)),
    __param(1, (0, typeorm_1.InjectRepository)(vulnerability_assessment_entity_1.VulnerabilityAssessment)),
    __param(2, (0, typeorm_1.InjectRepository)(vulnerability_scan_entity_1.VulnerabilityScan)),
    __param(3, (0, typeorm_1.InjectRepository)(vulnerability_exception_entity_1.VulnerabilityException)),
    __param(4, (0, typeorm_1.InjectRepository)(asset_entity_1.Asset)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _c : Object, typeof (_d = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _d : Object, typeof (_e = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _e : Object, typeof (_f = typeof logger_service_1.LoggerService !== "undefined" && logger_service_1.LoggerService) === "function" ? _f : Object])
], VulnerabilityReportingService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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