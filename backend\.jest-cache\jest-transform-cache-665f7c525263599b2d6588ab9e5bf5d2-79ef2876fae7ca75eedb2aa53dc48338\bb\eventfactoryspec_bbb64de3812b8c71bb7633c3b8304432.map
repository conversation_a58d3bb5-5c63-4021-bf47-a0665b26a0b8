{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\__tests__\\event.factory.spec.ts", "mappings": ";;AAAA,oDAAoE;AACpE,8DAAoD;AACpD,iEAAwD;AACxD,yEAAgE;AAChE,qEAA4D;AAC5D,2FAAiF;AACjF,+EAAqE;AACrE,6DAA2D;AAE3D,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;IAC5B,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,OAAO,GAAuB;gBAClC,IAAI,EAAE,2BAAS,CAAC,eAAe;gBAC/B,QAAQ,EAAE,mCAAa,CAAC,IAAI;gBAC5B,KAAK,EAAE,aAAa;gBACpB,OAAO,EAAE,EAAE,MAAM,EAAE,cAAc,EAAE;gBACnC,UAAU,EAAE,wCAAe,CAAC,QAAQ;gBACpC,gBAAgB,EAAE,OAAO;aAC1B,CAAC;YAEF,MAAM,KAAK,GAAG,4BAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE3C,MAAM,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,oBAAK,CAAC,CAAC;YACpC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,eAAe,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,cAAc,EAAE,CAAC,CAAC;YAC1D,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,wCAAe,CAAC,QAAQ,CAAC,CAAC;YAClE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACvD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,+BAAW,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU;YACzD,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,oDAAqB,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,QAAQ,GAAG,8BAAc,CAAC,QAAQ,EAAE,CAAC;YAC3C,MAAM,OAAO,GAAuB;gBAClC,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,2BAAS,CAAC,gBAAgB;gBAChC,QAAQ,EAAE,mCAAa,CAAC,QAAQ;gBAChC,KAAK,EAAE,eAAe;gBACtB,OAAO,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE;gBAChC,UAAU,EAAE,wCAAe,CAAC,SAAS;gBACrC,gBAAgB,EAAE,OAAO;aAC1B,CAAC;YAEF,MAAM,KAAK,GAAG,4BAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE3C,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,QAAQ,GAAG,8BAAc,CAAC,QAAQ,EAAE,CAAC;YAC3C,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAEnD,MAAM,OAAO,GAAuB;gBAClC,IAAI,EAAE,2BAAS,CAAC,sBAAsB;gBACtC,QAAQ,EAAE,mCAAa,CAAC,MAAM;gBAC9B,KAAK,EAAE,qBAAqB;gBAC5B,WAAW,EAAE,iCAAiC;gBAC9C,OAAO,EAAE,EAAE,GAAG,EAAE,eAAe,EAAE;gBACjC,UAAU,EAAE,wCAAe,CAAC,qBAAqB;gBACjD,gBAAgB,EAAE,iBAAiB;gBACnC,SAAS;gBACT,IAAI,EAAE,CAAC,eAAe,EAAE,UAAU,CAAC;gBACnC,SAAS,EAAE,EAAE;gBACb,eAAe,EAAE,EAAE;gBACnB,UAAU,EAAE,EAAE,GAAG,EAAE,eAAe,EAAE;gBACpC,aAAa,EAAE,UAAU;gBACzB,aAAa,EAAE,QAAQ;gBACvB,MAAM,EAAE,+BAAW,CAAC,aAAa;gBACjC,gBAAgB,EAAE,oDAAqB,CAAC,QAAQ;gBAChD,cAAc,EAAE;oBACd,IAAI,EAAE,uBAAuB;oBAC7B,OAAO,EAAE,OAAO;oBAChB,MAAM,EAAE,eAAe;oBACvB,QAAQ,EAAE,cAAc;iBACzB;gBACD,eAAe,EAAE;oBACf,QAAQ,EAAE,MAAM;oBAChB,iBAAiB,EAAE,KAAK;oBACxB,aAAa,EAAE,EAAE;iBAClB;aACF,CAAC;YAEF,MAAM,KAAK,GAAG,4BAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE3C,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,sBAAsB,CAAC,CAAC;YAC1D,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,MAAM,CAAC,CAAC;YAClD,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YAClE,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC,CAAC;YAC1D,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACjC,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACvC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,EAAE,GAAG,EAAE,eAAe,EAAE,CAAC,CAAC;YAC3D,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC7C,MAAM,CAAC,KAAK,CAAC,aAAa,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,+BAAW,CAAC,aAAa,CAAC,CAAC;YACrD,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,oDAAqB,CAAC,QAAQ,CAAC,CAAC;YACpE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAC/D,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACjE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACpD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC3D,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC5D,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC9D,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,OAAO,GAAG;gBACd,OAAO,EAAE,0BAA0B;gBACnC,QAAQ,EAAE,MAAM;gBAChB,SAAS,EAAE,sBAAsB;gBACjC,IAAI,EAAE,OAAO;gBACb,SAAS,EAAE,eAAe;gBAC1B,IAAI,EAAE,CAAC,gBAAgB,EAAE,YAAY,CAAC;aACvC,CAAC;YAEF,MAAM,KAAK,GAAG,4BAAY,CAAC,UAAU,CACnC,OAAO,EACP,wCAAe,CAAC,iBAAiB,EACjC,cAAc,CACf,CAAC;YAEF,MAAM,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,oBAAK,CAAC,CAAC;YACpC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACrD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACvC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,wCAAe,CAAC,iBAAiB,CAAC,CAAC;YAC3E,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC9D,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,eAAe,GAAG;gBACtB,OAAO,EAAE,6BAA6B;gBACtC,KAAK,EAAE,OAAO;aACf,CAAC;YAEF,MAAM,KAAK,GAAG,4BAAY,CAAC,UAAU,CACnC,eAAe,EACf,wCAAe,CAAC,iBAAiB,EACjC,OAAO,CACR,CAAC;YAEF,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,aAAa,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,UAAU,GAAG;gBACjB,IAAI,EAAE,iBAAiB;aACxB,CAAC;YAEF,MAAM,KAAK,GAAG,4BAAY,CAAC,UAAU,CACnC,UAAU,EACV,wCAAe,CAAC,kBAAkB,EAClC,QAAQ,CACT,CAAC;YAEF,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,gBAAgB;YAC5D,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,MAAM,CAAC,CAAC,CAAC,mBAAmB;YACtE,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,OAAO,GAAG,EAAE,KAAK,EAAE,oBAAoB,EAAE,CAAC;YAEhD,MAAM,KAAK,GAAG,4BAAY,CAAC,mBAAmB,CAC5C,2BAAS,CAAC,eAAe,EACzB,iBAAiB,EACjB,OAAO,EACP,wCAAe,CAAC,OAAO,EACvB,QAAQ,CACT,CAAC;YAEF,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,eAAe,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC5C,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACjC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,KAAK,GAAG,4BAAY,CAAC,mBAAmB,CAC5C,2BAAS,CAAC,gBAAgB,EAC1B,eAAe,EACf,EAAE,IAAI,EAAE,WAAW,EAAE,EACrB,wCAAe,CAAC,SAAS,EACzB,OAAO,EACP;gBACE,QAAQ,EAAE,mCAAa,CAAC,QAAQ;gBAChC,SAAS,EAAE,EAAE;aACd,CACF,CAAC;YAEF,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,QAAQ,CAAC,CAAC;YACpD,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,KAAK,GAAG,4BAAY,CAAC,mBAAmB,CAC5C,2BAAS,CAAC,WAAW,EACrB,sBAAsB,EACtB,EAAE,OAAO,EAAE,KAAK,EAAE,EAClB,wCAAe,CAAC,GAAG,EACnB,QAAQ,CACT,CAAC;YAEF,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,QAAQ,CAAC,CAAC;YACpD,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACjC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAClE,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YACzC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,WAAW,GAAG,4BAAY,CAAC,MAAM,CAAC;gBACtC,IAAI,EAAE,2BAAS,CAAC,eAAe;gBAC/B,QAAQ,EAAE,mCAAa,CAAC,IAAI;gBAC5B,KAAK,EAAE,eAAe;gBACtB,OAAO,EAAE,EAAE,EAAE,EAAE,eAAe,EAAE;gBAChC,UAAU,EAAE,wCAAe,CAAC,QAAQ;gBACpC,gBAAgB,EAAE,OAAO;gBACzB,IAAI,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC;aAC5B,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,4BAAY,CAAC,qBAAqB,CACxD,WAAW,EACX,2BAAS,CAAC,sBAAsB,EAChC,oBAAoB,EACpB,EAAE,UAAU,EAAE,SAAS,EAAE,CAC1B,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY;YACvE,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,wCAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY;YACzF,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY;YAC9E,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;YACtE,MAAM,CAAC,eAAe,CAAC,aAAa,EAAE,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzE,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,YAAY;YAC/D,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY;YAC9D,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,QAAQ;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,WAAW,GAAG,4BAAY,CAAC,MAAM,CAAC;gBACtC,IAAI,EAAE,2BAAS,CAAC,eAAe;gBAC/B,QAAQ,EAAE,mCAAa,CAAC,IAAI;gBAC5B,KAAK,EAAE,eAAe;gBACtB,OAAO,EAAE,EAAE,EAAE,EAAE,eAAe,EAAE;gBAChC,UAAU,EAAE,wCAAe,CAAC,QAAQ;gBACpC,gBAAgB,EAAE,OAAO;gBACzB,aAAa,EAAE,0BAA0B;aAC1C,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,4BAAY,CAAC,qBAAqB,CACxD,WAAW,EACX,2BAAS,CAAC,sBAAsB,EAChC,oBAAoB,EACpB,EAAE,UAAU,EAAE,SAAS,EAAE,CAC1B,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,UAAU,GAAG;gBACjB,SAAS,EAAE,eAAe;gBAC1B,aAAa,EAAE,IAAI;gBACnB,QAAQ,EAAE,MAAM;gBAChB,UAAU,EAAE,EAAE;gBACd,WAAW,EAAE,oBAAoB;gBACjC,MAAM,EAAE,cAAc;aACvB,CAAC;YAEF,MAAM,KAAK,GAAG,4BAAY,CAAC,sBAAsB,CAC/C,UAAU,EACV,sBAAsB,CACvB,CAAC;YAEF,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,eAAe,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;YAC/D,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACrD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,wCAAe,CAAC,mBAAmB,CAAC,CAAC;YAC7E,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;YACpD,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YACzC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACzD,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC7C,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,oBAAoB,GAAG;gBAC3B,SAAS,EAAE,aAAa;gBACxB,QAAQ,EAAE,CAAC;gBACX,UAAU,EAAE,EAAE;gBACd,aAAa,EAAE,MAAM;aACtB,CAAC;YAEF,MAAM,KAAK,GAAG,4BAAY,CAAC,sBAAsB,CAC/C,oBAAoB,EACpB,gBAAgB,CACjB,CAAC;YAEF,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,qDAAqD;QACpG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,QAAQ,GAAG;gBACf,GAAG,EAAE,eAAe;gBACpB,KAAK,EAAE,uBAAuB;gBAC9B,QAAQ,EAAE,UAAU;gBACpB,SAAS,EAAE,GAAG;gBACd,WAAW,EAAE,4BAA4B;gBACzC,aAAa,EAAE,eAAe;gBAC9B,QAAQ,EAAE,sBAAsB;aACjC,CAAC;YAEF,MAAM,KAAK,GAAG,4BAAY,CAAC,qBAAqB,CAC9C,QAAQ,EACR,iBAAiB,CAClB,CAAC;YAEF,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,sBAAsB,CAAC,CAAC;YAC1D,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YACzD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,QAAQ,CAAC,CAAC;YACpD,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAC7D,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,wCAAe,CAAC,qBAAqB,CAAC,CAAC;YAC/E,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YAC5C,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC7C,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC7D,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,YAAY,GAAG;gBACnB,GAAG,EAAE,eAAe;gBACpB,SAAS,EAAE,GAAG;gBACd,WAAW,EAAE,IAAI;gBACjB,gBAAgB,EAAE,MAAM;aACzB,CAAC;YAEF,MAAM,KAAK,GAAG,4BAAY,CAAC,qBAAqB,CAC9C,YAAY,EACZ,iBAAiB,CAClB,CAAC;YAEF,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,sBAAsB;QACrE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,KAAK,GAAG,4BAAY,CAAC,iBAAiB,CAC1C,2BAAS,CAAC,oBAAoB,EAC9B,mCAAa,CAAC,MAAM,EACpB,yBAAyB,EACzB,6CAA6C,EAC7C,qBAAqB,CACtB,CAAC;YAEF,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,oBAAoB,CAAC,CAAC;YACxD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,MAAM,CAAC,CAAC;YAClD,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YACpD,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;YAC9E,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,wCAAe,CAAC,MAAM,CAAC,CAAC;YAChE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACrE,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YACvC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAC/D,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,aAAa,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAkB,EAAE,CAAC;YAE3E,MAAM,KAAK,GAAG,4BAAY,CAAC,iBAAiB,CAC1C,2BAAS,CAAC,oBAAoB,EAC9B,mCAAa,CAAC,IAAI,EAClB,eAAe,EACf,mCAAmC,EACnC,qBAAqB,EACrB,aAAa,CACd,CAAC;YAEF,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,YAAY,GAAG,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC;YAC9C,MAAM,cAAc,GAAG,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC;YACpD,MAAM,cAAc,GAAG,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC;YACpD,MAAM,gBAAgB,GAAG,EAAE,UAAU,EAAE,YAAY,EAAE,CAAC;YACtD,MAAM,gBAAgB,GAAG,EAAE,UAAU,EAAE,YAAY,EAAE,CAAC;YAEtD,MAAM,CAAC,4BAAY,CAAC,UAAU,CAAC,YAAY,EAAE,wCAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,KAAK,CAAC;iBACjF,IAAI,CAAC,aAAa,CAAC,CAAC;YACvB,MAAM,CAAC,4BAAY,CAAC,UAAU,CAAC,cAAc,EAAE,wCAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,KAAK,CAAC;iBACnF,IAAI,CAAC,eAAe,CAAC,CAAC;YACzB,MAAM,CAAC,4BAAY,CAAC,UAAU,CAAC,cAAc,EAAE,wCAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,KAAK,CAAC;iBACnF,IAAI,CAAC,eAAe,CAAC,CAAC;YACzB,MAAM,CAAC,4BAAY,CAAC,UAAU,CAAC,gBAAgB,EAAE,wCAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,KAAK,CAAC;iBACrF,IAAI,CAAC,YAAY,CAAC,CAAC;YACtB,MAAM,CAAC,4BAAY,CAAC,UAAU,CAAC,gBAAgB,EAAE,wCAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,KAAK,CAAC;iBACrF,IAAI,CAAC,YAAY,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,SAAS,GAAG,sBAAsB,CAAC;YACzC,MAAM,gBAAgB,GAAG,EAAE,SAAS,EAAE,CAAC;YACvC,MAAM,kBAAkB,GAAG,EAAE,YAAY,EAAE,SAAS,EAAE,CAAC;YACvD,MAAM,gBAAgB,GAAG,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC;YAEnD,MAAM,MAAM,GAAG,4BAAY,CAAC,UAAU,CAAC,gBAAgB,EAAE,wCAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YAC1F,MAAM,MAAM,GAAG,4BAAY,CAAC,UAAU,CAAC,kBAAkB,EAAE,wCAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YAC5F,MAAM,MAAM,GAAG,4BAAY,CAAC,UAAU,CAAC,gBAAgB,EAAE,wCAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YAE1F,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YAC1E,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YAC1E,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,WAAW,GAAG,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;YAC7C,MAAM,OAAO,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;YACnC,MAAM,SAAS,GAAG,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;YAC1C,MAAM,MAAM,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC;YAEpC,MAAM,CAAC,4BAAY,CAAC,UAAU,CAAC,WAAW,EAAE,wCAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC;iBACnF,IAAI,CAAC,mCAAa,CAAC,QAAQ,CAAC,CAAC;YAChC,MAAM,CAAC,4BAAY,CAAC,UAAU,CAAC,OAAO,EAAE,wCAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC;iBAC/E,IAAI,CAAC,mCAAa,CAAC,IAAI,CAAC,CAAC;YAC5B,MAAM,CAAC,4BAAY,CAAC,UAAU,CAAC,SAAS,EAAE,wCAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC;iBACjF,IAAI,CAAC,mCAAa,CAAC,MAAM,CAAC,CAAC;YAC9B,MAAM,CAAC,4BAAY,CAAC,UAAU,CAAC,MAAM,EAAE,wCAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC;iBAC9E,IAAI,CAAC,mCAAa,CAAC,GAAG,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,YAAY,GAAG,EAAE,OAAO,EAAE,sCAAsC,EAAE,CAAC;YACzE,MAAM,YAAY,GAAG,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC;YAC1D,MAAM,YAAY,GAAG,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC;YACnE,MAAM,YAAY,GAAG,EAAE,OAAO,EAAE,8BAA8B,EAAE,CAAC;YACjE,MAAM,SAAS,GAAG,EAAE,OAAO,EAAE,mCAAmC,EAAE,CAAC;YACnE,MAAM,WAAW,GAAG,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC;YAE1D,MAAM,CAAC,4BAAY,CAAC,UAAU,CAAC,YAAY,EAAE,wCAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC;iBAChF,IAAI,CAAC,2BAAS,CAAC,aAAa,CAAC,CAAC;YACjC,MAAM,CAAC,4BAAY,CAAC,UAAU,CAAC,YAAY,EAAE,wCAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC;iBAChF,IAAI,CAAC,2BAAS,CAAC,aAAa,CAAC,CAAC;YACjC,MAAM,CAAC,4BAAY,CAAC,UAAU,CAAC,YAAY,EAAE,wCAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC;iBAChF,IAAI,CAAC,2BAAS,CAAC,sBAAsB,CAAC,CAAC;YAC1C,MAAM,CAAC,4BAAY,CAAC,UAAU,CAAC,YAAY,EAAE,wCAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC;iBAChF,IAAI,CAAC,2BAAS,CAAC,gBAAgB,CAAC,CAAC;YACpC,MAAM,CAAC,4BAAY,CAAC,UAAU,CAAC,SAAS,EAAE,wCAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC;iBAC7E,IAAI,CAAC,2BAAS,CAAC,sBAAsB,CAAC,CAAC;YAC1C,MAAM,CAAC,4BAAY,CAAC,UAAU,CAAC,WAAW,EAAE,wCAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC;iBAC/E,IAAI,CAAC,2BAAS,CAAC,eAAe,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\__tests__\\event.factory.spec.ts"], "sourcesContent": ["import { EventFactory, CreateEventOptions } from '../event.factory';\r\nimport { Event } from '../../entities/event.entity';\r\nimport { EventType } from '../../enums/event-type.enum';\r\nimport { EventSeverity } from '../../enums/event-severity.enum';\r\nimport { EventStatus } from '../../enums/event-status.enum';\r\nimport { EventProcessingStatus } from '../../enums/event-processing-status.enum';\r\nimport { EventSourceType } from '../../enums/event-source-type.enum';\r\nimport { UniqueEntityId } from '../../../../shared-kernel';\r\n\r\ndescribe('EventFactory', () => {\r\n  describe('create', () => {\r\n    it('should create event with required options', () => {\r\n      const options: CreateEventOptions = {\r\n        type: EventType.THREAT_DETECTED,\r\n        severity: EventSeverity.HIGH,\r\n        title: 'Test Threat',\r\n        rawData: { threat: 'malicious-ip' },\r\n        sourceType: EventSourceType.FIREWALL,\r\n        sourceIdentifier: 'fw-01',\r\n      };\r\n\r\n      const event = EventFactory.create(options);\r\n\r\n      expect(event).toBeInstanceOf(Event);\r\n      expect(event.type).toBe(EventType.THREAT_DETECTED);\r\n      expect(event.severity).toBe(EventSeverity.HIGH);\r\n      expect(event.title).toBe('Test Threat');\r\n      expect(event.rawData).toEqual({ threat: 'malicious-ip' });\r\n      expect(event.metadata.source.type).toBe(EventSourceType.FIREWALL);\r\n      expect(event.metadata.source.identifier).toBe('fw-01');\r\n      expect(event.status).toBe(EventStatus.ACTIVE); // Default\r\n      expect(event.processingStatus).toBe(EventProcessingStatus.RAW); // Default\r\n    });\r\n\r\n    it('should create event with custom ID', () => {\r\n      const customId = UniqueEntityId.generate();\r\n      const options: CreateEventOptions = {\r\n        id: customId,\r\n        type: EventType.MALWARE_DETECTED,\r\n        severity: EventSeverity.CRITICAL,\r\n        title: 'Malware Alert',\r\n        rawData: { file: 'malware.exe' },\r\n        sourceType: EventSourceType.ANTIVIRUS,\r\n        sourceIdentifier: 'av-01',\r\n      };\r\n\r\n      const event = EventFactory.create(options);\r\n\r\n      expect(event.id.equals(customId)).toBe(true);\r\n    });\r\n\r\n    it('should create event with all optional properties', () => {\r\n      const parentId = UniqueEntityId.generate();\r\n      const timestamp = new Date('2023-01-01T10:00:00Z');\r\n      \r\n      const options: CreateEventOptions = {\r\n        type: EventType.VULNERABILITY_DETECTED,\r\n        severity: EventSeverity.MEDIUM,\r\n        title: 'Vulnerability Found',\r\n        description: 'Critical vulnerability detected',\r\n        rawData: { cve: 'CVE-2023-1234' },\r\n        sourceType: EventSourceType.VULNERABILITY_SCANNER,\r\n        sourceIdentifier: 'vuln-scanner-01',\r\n        timestamp,\r\n        tags: ['vulnerability', 'critical'],\r\n        riskScore: 80,\r\n        confidenceLevel: 90,\r\n        attributes: { cve: 'CVE-2023-1234' },\r\n        correlationId: 'corr-123',\r\n        parentEventId: parentId,\r\n        status: EventStatus.INVESTIGATING,\r\n        processingStatus: EventProcessingStatus.ANALYZED,\r\n        sourceMetadata: {\r\n          name: 'Vulnerability Scanner',\r\n          version: '2.1.0',\r\n          vendor: 'Security Corp',\r\n          location: 'datacenter-1',\r\n        },\r\n        processingHints: {\r\n          priority: 'high',\r\n          skipNormalization: false,\r\n          retentionDays: 90,\r\n        },\r\n      };\r\n\r\n      const event = EventFactory.create(options);\r\n\r\n      expect(event.type).toBe(EventType.VULNERABILITY_DETECTED);\r\n      expect(event.severity).toBe(EventSeverity.MEDIUM);\r\n      expect(event.title).toBe('Vulnerability Found');\r\n      expect(event.description).toBe('Critical vulnerability detected');\r\n      expect(event.tags).toEqual(['vulnerability', 'critical']);\r\n      expect(event.riskScore).toBe(80);\r\n      expect(event.confidenceLevel).toBe(90);\r\n      expect(event.attributes).toEqual({ cve: 'CVE-2023-1234' });\r\n      expect(event.correlationId).toBe('corr-123');\r\n      expect(event.parentEventId?.equals(parentId)).toBe(true);\r\n      expect(event.status).toBe(EventStatus.INVESTIGATING);\r\n      expect(event.processingStatus).toBe(EventProcessingStatus.ANALYZED);\r\n      expect(event.metadata.timestamp.occurredAt).toEqual(timestamp);\r\n      expect(event.metadata.source.name).toBe('Vulnerability Scanner');\r\n      expect(event.metadata.source.version).toBe('2.1.0');\r\n      expect(event.metadata.source.vendor).toBe('Security Corp');\r\n      expect(event.metadata.source.location).toBe('datacenter-1');\r\n      expect(event.metadata.processingHints?.priority).toBe('high');\r\n      expect(event.metadata.processingHints?.retentionDays).toBe(90);\r\n    });\r\n  });\r\n\r\n  describe('fromRawLog', () => {\r\n    it('should create event from raw log data', () => {\r\n      const logData = {\r\n        message: 'Suspicious login attempt',\r\n        severity: 'high',\r\n        timestamp: '2023-01-01T10:00:00Z',\r\n        user: 'admin',\r\n        source_ip: '*************',\r\n        tags: ['authentication', 'suspicious'],\r\n      };\r\n\r\n      const event = EventFactory.fromRawLog(\r\n        logData,\r\n        EventSourceType.DIRECTORY_SERVICE,\r\n        'ad-server-01'\r\n      );\r\n\r\n      expect(event).toBeInstanceOf(Event);\r\n      expect(event.title).toBe('Suspicious login attempt');\r\n      expect(event.severity).toBe(EventSeverity.HIGH);\r\n      expect(event.rawData).toEqual(logData);\r\n      expect(event.metadata.source.type).toBe(EventSourceType.DIRECTORY_SERVICE);\r\n      expect(event.metadata.source.identifier).toBe('ad-server-01');\r\n      expect(event.tags).toEqual(['authentication', 'suspicious']);\r\n    });\r\n\r\n    it('should infer event type from log content', () => {\r\n      const loginFailureLog = {\r\n        message: 'Login failed for user admin',\r\n        level: 'error',\r\n      };\r\n\r\n      const event = EventFactory.fromRawLog(\r\n        loginFailureLog,\r\n        EventSourceType.DIRECTORY_SERVICE,\r\n        'ad-01'\r\n      );\r\n\r\n      expect(event.type).toBe(EventType.LOGIN_FAILURE);\r\n    });\r\n\r\n    it('should handle missing fields gracefully', () => {\r\n      const minimalLog = {\r\n        data: 'some event data',\r\n      };\r\n\r\n      const event = EventFactory.fromRawLog(\r\n        minimalLog,\r\n        EventSourceType.CUSTOM_APPLICATION,\r\n        'app-01'\r\n      );\r\n\r\n      expect(event.title).toBe('Security Event'); // Default title\r\n      expect(event.severity).toBe(EventSeverity.MEDIUM); // Default severity\r\n      expect(event.type).toBe(EventType.CUSTOM); // Default type\r\n    });\r\n  });\r\n\r\n  describe('createSecurityAlert', () => {\r\n    it('should create high-severity security alert', () => {\r\n      const rawData = { alert: 'Intrusion detected' };\r\n      \r\n      const event = EventFactory.createSecurityAlert(\r\n        EventType.THREAT_DETECTED,\r\n        'Intrusion Alert',\r\n        rawData,\r\n        EventSourceType.IDS_IPS,\r\n        'ids-01'\r\n      );\r\n\r\n      expect(event.type).toBe(EventType.THREAT_DETECTED);\r\n      expect(event.severity).toBe(EventSeverity.HIGH);\r\n      expect(event.title).toBe('Intrusion Alert');\r\n      expect(event.riskScore).toBe(80);\r\n      expect(event.metadata.processingHints?.priority).toBe('high');\r\n    });\r\n\r\n    it('should allow overriding default properties', () => {\r\n      const event = EventFactory.createSecurityAlert(\r\n        EventType.MALWARE_DETECTED,\r\n        'Malware Alert',\r\n        { file: 'virus.exe' },\r\n        EventSourceType.ANTIVIRUS,\r\n        'av-01',\r\n        {\r\n          severity: EventSeverity.CRITICAL,\r\n          riskScore: 95,\r\n        }\r\n      );\r\n\r\n      expect(event.severity).toBe(EventSeverity.CRITICAL);\r\n      expect(event.riskScore).toBe(95);\r\n    });\r\n  });\r\n\r\n  describe('createCriticalAlert', () => {\r\n    it('should create critical security alert', () => {\r\n      const event = EventFactory.createCriticalAlert(\r\n        EventType.DATA_BREACH,\r\n        'Data Breach Detected',\r\n        { records: 10000 },\r\n        EventSourceType.DLP,\r\n        'dlp-01'\r\n      );\r\n\r\n      expect(event.severity).toBe(EventSeverity.CRITICAL);\r\n      expect(event.riskScore).toBe(95);\r\n      expect(event.metadata.processingHints?.priority).toBe('critical');\r\n      expect(event.tags).toContain('critical');\r\n      expect(event.tags).toContain('security-alert');\r\n    });\r\n  });\r\n\r\n  describe('createCorrelatedEvent', () => {\r\n    it('should create correlated event from parent', () => {\r\n      const parentEvent = EventFactory.create({\r\n        type: EventType.THREAT_DETECTED,\r\n        severity: EventSeverity.HIGH,\r\n        title: 'Parent Threat',\r\n        rawData: { ip: '*************' },\r\n        sourceType: EventSourceType.FIREWALL,\r\n        sourceIdentifier: 'fw-01',\r\n        tags: ['network', 'threat'],\r\n      });\r\n\r\n      const correlatedEvent = EventFactory.createCorrelatedEvent(\r\n        parentEvent,\r\n        EventType.CONNECTION_ESTABLISHED,\r\n        'Related Connection',\r\n        { connection: 'tcp:443' }\r\n      );\r\n\r\n      expect(correlatedEvent.severity).toBe(EventSeverity.HIGH); // Inherited\r\n      expect(correlatedEvent.metadata.source.type).toBe(EventSourceType.FIREWALL); // Inherited\r\n      expect(correlatedEvent.metadata.source.identifier).toBe('fw-01'); // Inherited\r\n      expect(correlatedEvent.correlationId).toBe(parentEvent.id.toString());\r\n      expect(correlatedEvent.parentEventId?.equals(parentEvent.id)).toBe(true);\r\n      expect(correlatedEvent.tags).toContain('network'); // Inherited\r\n      expect(correlatedEvent.tags).toContain('threat'); // Inherited\r\n      expect(correlatedEvent.tags).toContain('correlated'); // Added\r\n    });\r\n\r\n    it('should use existing correlation ID from parent', () => {\r\n      const parentEvent = EventFactory.create({\r\n        type: EventType.THREAT_DETECTED,\r\n        severity: EventSeverity.HIGH,\r\n        title: 'Parent Threat',\r\n        rawData: { ip: '*************' },\r\n        sourceType: EventSourceType.FIREWALL,\r\n        sourceIdentifier: 'fw-01',\r\n        correlationId: 'existing-correlation-123',\r\n      });\r\n\r\n      const correlatedEvent = EventFactory.createCorrelatedEvent(\r\n        parentEvent,\r\n        EventType.CONNECTION_ESTABLISHED,\r\n        'Related Connection',\r\n        { connection: 'tcp:443' }\r\n      );\r\n\r\n      expect(correlatedEvent.correlationId).toBe('existing-correlation-123');\r\n    });\r\n  });\r\n\r\n  describe('fromThreatIntelligence', () => {\r\n    it('should create event from threat intelligence data', () => {\r\n      const threatData = {\r\n        indicator: '*************',\r\n        indicatorType: 'ip',\r\n        severity: 'high',\r\n        confidence: 85,\r\n        description: 'Known malicious IP',\r\n        source: 'ThreatFeed-1',\r\n      };\r\n\r\n      const event = EventFactory.fromThreatIntelligence(\r\n        threatData,\r\n        'threat-intel-feed-01'\r\n      );\r\n\r\n      expect(event.type).toBe(EventType.THREAT_DETECTED);\r\n      expect(event.title).toBe('Threat Intelligence: *************');\r\n      expect(event.description).toBe('Known malicious IP');\r\n      expect(event.metadata.source.type).toBe(EventSourceType.THREAT_INTELLIGENCE);\r\n      expect(event.tags).toContain('threat-intelligence');\r\n      expect(event.tags).toContain('external');\r\n      expect(event.attributes.indicator).toBe('*************');\r\n      expect(event.attributes.indicatorType).toBe('ip');\r\n      expect(event.attributes.confidence).toBe(85);\r\n      expect(event.attributes.source).toBe('ThreatFeed-1');\r\n    });\r\n\r\n    it('should calculate risk score from threat data', () => {\r\n      const highConfidenceThreat = {\r\n        indicator: 'malware.exe',\r\n        severity: 9,\r\n        confidence: 95,\r\n        indicatorType: 'file',\r\n      };\r\n\r\n      const event = EventFactory.fromThreatIntelligence(\r\n        highConfidenceThreat,\r\n        'threat-feed-01'\r\n      );\r\n\r\n      expect(event.riskScore).toBeGreaterThan(80); // Should be high due to high confidence and severity\r\n    });\r\n  });\r\n\r\n  describe('fromVulnerabilityScan', () => {\r\n    it('should create event from vulnerability scan data', () => {\r\n      const vulnData = {\r\n        cve: 'CVE-2023-1234',\r\n        title: 'Remote Code Execution',\r\n        severity: 'critical',\r\n        cvssScore: 9.8,\r\n        description: 'Critical RCE vulnerability',\r\n        affectedAsset: 'web-server-01',\r\n        solution: 'Apply security patch',\r\n      };\r\n\r\n      const event = EventFactory.fromVulnerabilityScan(\r\n        vulnData,\r\n        'vuln-scanner-01'\r\n      );\r\n\r\n      expect(event.type).toBe(EventType.VULNERABILITY_DETECTED);\r\n      expect(event.title).toBe('Vulnerability: CVE-2023-1234');\r\n      expect(event.severity).toBe(EventSeverity.CRITICAL);\r\n      expect(event.description).toBe('Critical RCE vulnerability');\r\n      expect(event.metadata.source.type).toBe(EventSourceType.VULNERABILITY_SCANNER);\r\n      expect(event.tags).toContain('vulnerability');\r\n      expect(event.tags).toContain('scan-result');\r\n      expect(event.attributes.cve).toBe('CVE-2023-1234');\r\n      expect(event.attributes.cvssScore).toBe(9.8);\r\n      expect(event.attributes.affectedAsset).toBe('web-server-01');\r\n      expect(event.attributes.solution).toBe('Apply security patch');\r\n    });\r\n\r\n    it('should calculate risk score from vulnerability data', () => {\r\n      const criticalVuln = {\r\n        cve: 'CVE-2023-5678',\r\n        cvssScore: 9.5,\r\n        exploitable: true,\r\n        assetCriticality: 'high',\r\n      };\r\n\r\n      const event = EventFactory.fromVulnerabilityScan(\r\n        criticalVuln,\r\n        'vuln-scanner-01'\r\n      );\r\n\r\n      expect(event.riskScore).toBeGreaterThan(90); // Should be very high\r\n    });\r\n  });\r\n\r\n  describe('createManualEvent', () => {\r\n    it('should create manual event', () => {\r\n      const event = EventFactory.createManualEvent(\r\n        EventType.COMPLIANCE_VIOLATION,\r\n        EventSeverity.MEDIUM,\r\n        'Manual Compliance Check',\r\n        'Found policy violation during manual review',\r\n        '<EMAIL>'\r\n      );\r\n\r\n      expect(event.type).toBe(EventType.COMPLIANCE_VIOLATION);\r\n      expect(event.severity).toBe(EventSeverity.MEDIUM);\r\n      expect(event.title).toBe('Manual Compliance Check');\r\n      expect(event.description).toBe('Found policy violation during manual review');\r\n      expect(event.metadata.source.type).toBe(EventSourceType.MANUAL);\r\n      expect(event.metadata.source.identifier).toBe('<EMAIL>');\r\n      expect(event.tags).toContain('manual');\r\n      expect(event.tags).toContain('analyst-created');\r\n      expect(event.attributes.createdBy).toBe('<EMAIL>');\r\n      expect(event.attributes.createdAt).toBeDefined();\r\n    });\r\n\r\n    it('should use provided raw data', () => {\r\n      const customRawData = { policy: 'PCI-DSS', violation: 'unencrypted-data' };\r\n      \r\n      const event = EventFactory.createManualEvent(\r\n        EventType.COMPLIANCE_VIOLATION,\r\n        EventSeverity.HIGH,\r\n        'PCI Violation',\r\n        'Unencrypted cardholder data found',\r\n        '<EMAIL>',\r\n        customRawData\r\n      );\r\n\r\n      expect(event.rawData).toEqual(customRawData);\r\n    });\r\n  });\r\n\r\n  describe('Data Extraction Helpers', () => {\r\n    it('should extract title from various fields', () => {\r\n      const logWithTitle = { title: 'Event Title' };\r\n      const logWithMessage = { message: 'Event Message' };\r\n      const logWithSummary = { summary: 'Event Summary' };\r\n      const logWithEventName = { event_name: 'Event Name' };\r\n      const logWithAlertName = { alert_name: 'Alert Name' };\r\n\r\n      expect(EventFactory.fromRawLog(logWithTitle, EventSourceType.SIEM, 'siem-01').title)\r\n        .toBe('Event Title');\r\n      expect(EventFactory.fromRawLog(logWithMessage, EventSourceType.SIEM, 'siem-01').title)\r\n        .toBe('Event Message');\r\n      expect(EventFactory.fromRawLog(logWithSummary, EventSourceType.SIEM, 'siem-01').title)\r\n        .toBe('Event Summary');\r\n      expect(EventFactory.fromRawLog(logWithEventName, EventSourceType.SIEM, 'siem-01').title)\r\n        .toBe('Event Name');\r\n      expect(EventFactory.fromRawLog(logWithAlertName, EventSourceType.SIEM, 'siem-01').title)\r\n        .toBe('Alert Name');\r\n    });\r\n\r\n    it('should extract timestamp from various fields', () => {\r\n      const timestamp = '2023-01-01T10:00:00Z';\r\n      const logWithTimestamp = { timestamp };\r\n      const logWithAtTimestamp = { '@timestamp': timestamp };\r\n      const logWithEventTime = { event_time: timestamp };\r\n\r\n      const event1 = EventFactory.fromRawLog(logWithTimestamp, EventSourceType.SIEM, 'siem-01');\r\n      const event2 = EventFactory.fromRawLog(logWithAtTimestamp, EventSourceType.SIEM, 'siem-01');\r\n      const event3 = EventFactory.fromRawLog(logWithEventTime, EventSourceType.SIEM, 'siem-01');\r\n\r\n      expect(event1.metadata.timestamp.occurredAt).toEqual(new Date(timestamp));\r\n      expect(event2.metadata.timestamp.occurredAt).toEqual(new Date(timestamp));\r\n      expect(event3.metadata.timestamp.occurredAt).toEqual(new Date(timestamp));\r\n    });\r\n\r\n    it('should infer severity from various indicators', () => {\r\n      const criticalLog = { severity: 'critical' };\r\n      const highLog = { level: 'error' };\r\n      const mediumLog = { priority: 'warning' };\r\n      const lowLog = { severity: 'info' };\r\n\r\n      expect(EventFactory.fromRawLog(criticalLog, EventSourceType.SIEM, 'siem-01').severity)\r\n        .toBe(EventSeverity.CRITICAL);\r\n      expect(EventFactory.fromRawLog(highLog, EventSourceType.SIEM, 'siem-01').severity)\r\n        .toBe(EventSeverity.HIGH);\r\n      expect(EventFactory.fromRawLog(mediumLog, EventSourceType.SIEM, 'siem-01').severity)\r\n        .toBe(EventSeverity.MEDIUM);\r\n      expect(EventFactory.fromRawLog(lowLog, EventSourceType.SIEM, 'siem-01').severity)\r\n        .toBe(EventSeverity.LOW);\r\n    });\r\n\r\n    it('should infer event type from content', () => {\r\n      const loginFailure = { message: 'Authentication failed for user admin' };\r\n      const loginSuccess = { message: 'User login successful' };\r\n      const networkEvent = { message: 'Network connection established' };\r\n      const malwareEvent = { message: 'Malware detected in file.exe' };\r\n      const vulnEvent = { message: 'Vulnerability CVE-2023-1234 found' };\r\n      const threatEvent = { message: 'Threat attack detected' };\r\n\r\n      expect(EventFactory.fromRawLog(loginFailure, EventSourceType.SIEM, 'siem-01').type)\r\n        .toBe(EventType.LOGIN_FAILURE);\r\n      expect(EventFactory.fromRawLog(loginSuccess, EventSourceType.SIEM, 'siem-01').type)\r\n        .toBe(EventType.LOGIN_SUCCESS);\r\n      expect(EventFactory.fromRawLog(networkEvent, EventSourceType.SIEM, 'siem-01').type)\r\n        .toBe(EventType.CONNECTION_ESTABLISHED);\r\n      expect(EventFactory.fromRawLog(malwareEvent, EventSourceType.SIEM, 'siem-01').type)\r\n        .toBe(EventType.MALWARE_DETECTED);\r\n      expect(EventFactory.fromRawLog(vulnEvent, EventSourceType.SIEM, 'siem-01').type)\r\n        .toBe(EventType.VULNERABILITY_DETECTED);\r\n      expect(EventFactory.fromRawLog(threatEvent, EventSourceType.SIEM, 'siem-01').type)\r\n        .toBe(EventType.THREAT_DETECTED);\r\n    });\r\n  });\r\n});"], "version": 3}