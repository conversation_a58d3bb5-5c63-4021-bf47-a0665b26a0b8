ed3d1996741613ed4607d5b9a77fffdd
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const database_config_1 = require("../database.config");
describe('DatabaseConfigurationService', () => {
    let service;
    let configService;
    let mockTypeOrmOptions;
    let originalEnv;
    beforeEach(async () => {
        // Store original environment
        originalEnv = { ...process.env };
        // Set up test environment
        process.env = {
            NODE_ENV: 'test',
            DATABASE_TYPE: 'postgres',
            DATABASE_HOST: 'localhost',
            DATABASE_PORT: '5432',
            DATABASE_USERNAME: 'testuser',
            DATABASE_PASSWORD: 'testpass',
            DATABASE_NAME: 'testdb',
            DATABASE_SCHEMA: 'public',
        };
        // Mock TypeORM options
        mockTypeOrmOptions = {
            type: 'postgres',
            host: 'localhost',
            port: 5432,
            username: 'testuser',
            password: 'testpass',
            database: 'testdb',
            schema: 'public',
            entities: [],
            migrations: [],
            subscribers: [],
            synchronize: false,
            logging: false,
        };
        // Mock ConfigService
        configService = {
            get: jest.fn().mockReturnValue(mockTypeOrmOptions),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                database_config_1.DatabaseConfigurationService,
                {
                    provide: config_1.ConfigService,
                    useValue: configService,
                },
            ],
        }).compile();
        service = module.get(database_config_1.DatabaseConfigurationService);
    });
    afterEach(() => {
        // Restore original environment
        process.env = originalEnv;
        jest.clearAllMocks();
    });
    describe('constructor', () => {
        it('should be defined', () => {
            expect(service).toBeDefined();
        });
        it('should load configuration from environment', () => {
            expect(service.type).toBe('postgres');
            expect(service.host).toBe('localhost');
            expect(service.port).toBe(5432);
            expect(service.username).toBe('testuser');
            expect(service.database).toBe('testdb');
        });
    });
    describe('configuration getters', () => {
        it('should return database type', () => {
            expect(service.type).toBe('postgres');
        });
        it('should return database host', () => {
            expect(service.host).toBe('localhost');
        });
        it('should return database port', () => {
            expect(service.port).toBe(5432);
        });
        it('should return database username', () => {
            expect(service.username).toBe('testuser');
        });
        it('should return database password', () => {
            expect(service.password).toBe('testpass');
        });
        it('should return database name', () => {
            expect(service.database).toBe('testdb');
        });
        it('should return database schema', () => {
            expect(service.schema).toBe('public');
        });
        it('should return SSL configuration', () => {
            const ssl = service.ssl;
            expect(ssl).toHaveProperty('enabled');
            expect(ssl).toHaveProperty('rejectUnauthorized');
        });
        it('should return pool configuration', () => {
            const pool = service.pool;
            expect(pool).toHaveProperty('min');
            expect(pool).toHaveProperty('max');
            expect(pool).toHaveProperty('acquireTimeoutMillis');
        });
        it('should return migration configuration', () => {
            const migration = service.migration;
            expect(migration).toHaveProperty('run');
            expect(migration).toHaveProperty('tableName');
            expect(migration).toHaveProperty('directory');
        });
        it('should return logging configuration', () => {
            const logging = service.logging;
            expect(logging).toHaveProperty('enabled');
            expect(logging).toHaveProperty('logQueries');
            expect(logging).toHaveProperty('logErrors');
        });
    });
    describe('utility methods', () => {
        it('should return complete configuration', () => {
            const config = service.getAll();
            expect(config).toHaveProperty('type');
            expect(config).toHaveProperty('host');
            expect(config).toHaveProperty('port');
            expect(config).toHaveProperty('ssl');
            expect(config).toHaveProperty('pool');
            expect(config).toHaveProperty('migration');
            expect(config).toHaveProperty('logging');
        });
        it('should return TypeORM options', () => {
            const options = service.getTypeOrmOptions();
            expect(options).toEqual(mockTypeOrmOptions);
            expect(configService.get).toHaveBeenCalledWith('database');
        });
        it('should generate connection string', () => {
            const connectionString = service.getConnectionString();
            expect(connectionString).toBe('postgres://testuser:testpass@localhost:5432/testdb');
        });
        it('should check SSL status', () => {
            expect(service.isSSLEnabled()).toBe(false);
            // Test with SSL enabled
            process.env['DATABASE_SSL'] = 'true';
            const sslService = new database_config_1.DatabaseConfigurationService(configService);
            expect(sslService.isSSLEnabled()).toBe(true);
        });
        it('should check migration status', () => {
            expect(service.shouldRunMigrations()).toBe(true);
            // Test with migrations disabled
            process.env['DATABASE_MIGRATIONS_RUN'] = 'false';
            const migrationService = new database_config_1.DatabaseConfigurationService(configService);
            expect(migrationService.shouldRunMigrations()).toBe(false);
        });
        it('should check query logging status', () => {
            // In test environment, logging should be enabled but queries disabled by default
            expect(service.isQueryLoggingEnabled()).toBe(false);
            // Test with query logging enabled
            process.env['DATABASE_LOG_QUERIES'] = 'true';
            const loggingService = new database_config_1.DatabaseConfigurationService(configService);
            expect(loggingService.isQueryLoggingEnabled()).toBe(true);
        });
    });
    describe('environment-specific behavior', () => {
        it('should configure for development environment', () => {
            process.env['NODE_ENV'] = 'development';
            const devService = new database_config_1.DatabaseConfigurationService(configService);
            expect(devService.synchronize).toBe(true);
            expect(devService.logging.enabled).toBe(true);
        });
        it('should configure for production environment', () => {
            process.env['NODE_ENV'] = 'production';
            const prodService = new database_config_1.DatabaseConfigurationService(configService);
            expect(prodService.synchronize).toBe(false);
            expect(prodService.logging.enabled).toBe(false);
        });
        it('should configure for test environment', () => {
            process.env['NODE_ENV'] = 'test';
            const testService = new database_config_1.DatabaseConfigurationService(configService);
            expect(testService.synchronize).toBe(false);
            expect(testService.logging.enabled).toBe(false);
        });
    });
});
describe('Database Configuration Factory', () => {
    let originalEnv;
    beforeEach(() => {
        originalEnv = { ...process.env };
    });
    afterEach(() => {
        process.env = originalEnv;
    });
    describe('databaseConfig factory', () => {
        it('should create TypeORM configuration with defaults', () => {
            process.env = {
                NODE_ENV: 'test',
            };
            const config = (0, database_config_1.databaseConfig)();
            expect(config.type).toBe('postgres');
            expect(config.host).toBe('localhost');
            expect(config.port).toBe(5432);
            expect(config.username).toBe('sentinel_user');
            expect(config.password).toBe('sentinel_password');
            expect(config.database).toBe('sentinel_test'); // Test environment
            expect(config.schema).toBe('public');
            expect(config.ssl).toBe(false);
            expect(config.synchronize).toBe(false);
            expect(config.logging).toBe(false);
        });
        it('should create TypeORM configuration from environment variables', () => {
            process.env = {
                NODE_ENV: 'development',
                DATABASE_TYPE: 'mysql',
                DATABASE_HOST: 'db.example.com',
                DATABASE_PORT: '3306',
                DATABASE_USERNAME: 'myuser',
                DATABASE_PASSWORD: 'mypass',
                DATABASE_NAME: 'mydb',
                DATABASE_SCHEMA: 'myschema',
                DATABASE_SSL: 'true',
                DATABASE_SYNCHRONIZE: 'true',
                DATABASE_LOGGING_ENABLED: 'true',
                DATABASE_LOG_QUERIES: 'true',
            };
            const config = (0, database_config_1.databaseConfig)();
            expect(config.type).toBe('mysql');
            expect(config.host).toBe('db.example.com');
            expect(config.port).toBe(3306);
            expect(config.username).toBe('myuser');
            expect(config.password).toBe('mypass');
            expect(config.database).toBe('mydb');
            expect(config.schema).toBe('myschema');
            expect(config.ssl).toBeTruthy();
            expect(config.synchronize).toBe(true);
            expect(config.logging).toEqual(['query', 'error', 'warn', 'info']);
        });
        it('should configure SSL properly', () => {
            process.env = {
                NODE_ENV: 'production',
                DATABASE_SSL: 'true',
                DATABASE_SSL_REJECT_UNAUTHORIZED: 'true',
                DATABASE_SSL_CA: 'ca-cert',
                DATABASE_SSL_CERT: 'client-cert',
                DATABASE_SSL_KEY: 'client-key',
            };
            const config = (0, database_config_1.databaseConfig)();
            expect(config.ssl).toEqual({
                rejectUnauthorized: true,
                ca: 'ca-cert',
                cert: 'client-cert',
                key: 'client-key',
            });
        });
        it('should configure connection pool', () => {
            process.env = {
                NODE_ENV: 'production',
                DATABASE_POOL_MIN: '5',
                DATABASE_POOL_MAX: '50',
                DATABASE_POOL_ACQUIRE_TIMEOUT: '30000',
                DATABASE_POOL_CREATE_TIMEOUT: '15000',
            };
            const config = (0, database_config_1.databaseConfig)();
            expect(config.extra).toEqual(expect.objectContaining({
                min: 5,
                max: 50,
                acquireTimeoutMillis: 30000,
                createTimeoutMillis: 15000,
            }));
        });
        it('should configure migrations', () => {
            process.env = {
                NODE_ENV: 'production',
                DATABASE_MIGRATIONS_RUN: 'true',
                DATABASE_MIGRATIONS_TABLE: 'custom_migrations',
                DATABASE_MIGRATIONS_TRANSACTION_PER_MIGRATION: 'false',
            };
            const config = (0, database_config_1.databaseConfig)();
            expect(config.migrationsRun).toBe(true);
            expect(config.migrationsTableName).toBe('custom_migrations');
            expect(config.migrationsTransactionMode).toBe('all');
        });
        it('should configure Redis cache when enabled', () => {
            process.env = {
                NODE_ENV: 'production',
                REDIS_ENABLED: 'true',
                REDIS_HOST: 'redis.example.com',
                REDIS_PORT: '6380',
                REDIS_PASSWORD: 'redispass',
                REDIS_CACHE_DB: '2',
                DATABASE_CACHE_DURATION: '60000',
            };
            const config = (0, database_config_1.databaseConfig)();
            expect(config.cache).toEqual({
                type: 'redis',
                options: {
                    host: 'redis.example.com',
                    port: 6380,
                    password: 'redispass',
                    db: 2,
                },
                duration: 60000,
            });
        });
        it('should not configure Redis cache when disabled', () => {
            process.env = {
                NODE_ENV: 'production',
                REDIS_ENABLED: 'false',
            };
            const config = (0, database_config_1.databaseConfig)();
            expect(config.cache).toBeUndefined();
        });
        it('should handle invalid environment values gracefully', () => {
            process.env = {
                NODE_ENV: 'test',
                DATABASE_TYPE: 'invalid-type', // Should fallback to postgres
                DATABASE_PORT: 'invalid-port', // Should fallback to 5432
                DATABASE_POOL_MAX: 'invalid-number', // Should fallback to default
            };
            const config = (0, database_config_1.databaseConfig)();
            expect(config.type).toBe('postgres'); // Fallback
            expect(config.port).toBe(5432); // Fallback
            expect(config.extra.max).toBe(100); // Default value
        });
        it('should configure logging based on environment', () => {
            // Development environment
            process.env = {
                NODE_ENV: 'development',
                DATABASE_LOGGING_ENABLED: 'true',
                DATABASE_LOG_QUERIES: 'true',
            };
            let config = (0, database_config_1.databaseConfig)();
            expect(config.logging).toEqual(['query', 'error', 'warn', 'info']);
            // Production environment
            process.env = {
                NODE_ENV: 'production',
                DATABASE_LOGGING_ENABLED: 'true',
            };
            config = (0, database_config_1.databaseConfig)();
            expect(config.logging).toEqual(['error']);
            // Logging disabled
            process.env = {
                NODE_ENV: 'development',
                DATABASE_LOGGING_ENABLED: 'false',
            };
            config = (0, database_config_1.databaseConfig)();
            expect(config.logging).toBe(false);
        });
        it('should set correct entity, migration, and subscriber paths', () => {
            const config = (0, database_config_1.databaseConfig)();
            expect(config.entities).toEqual(expect.arrayContaining([
                expect.stringContaining('**/*.entity'),
            ]));
            expect(config.migrations).toEqual(expect.arrayContaining([
                expect.stringContaining('database/migrations/*'),
            ]));
            expect(config.subscribers).toEqual(expect.arrayContaining([
                expect.stringContaining('database/subscribers/*'),
            ]));
        });
    });
    describe('Database Type Validation', () => {
        it('should accept valid database types', () => {
            const validTypes = ['postgres', 'mysql', 'mariadb'];
            validTypes.forEach(type => {
                process.env['DATABASE_TYPE'] = type;
                const config = (0, database_config_1.databaseConfig)();
                expect(config.type).toBe(type);
            });
        });
        it('should fallback to postgres for invalid database type', () => {
            process.env['DATABASE_TYPE'] = 'invalid-db-type';
            const config = (0, database_config_1.databaseConfig)();
            expect(config.type).toBe('postgres');
        });
    });
    describe('Environment-specific Database Names', () => {
        it('should append _test to database name in test environment', () => {
            process.env = {
                NODE_ENV: 'test',
                DATABASE_NAME: 'myapp',
            };
            const config = (0, database_config_1.databaseConfig)();
            expect(config.database).toBe('myapp');
        });
        it('should use regular database name in non-test environments', () => {
            process.env = {
                NODE_ENV: 'development',
                DATABASE_NAME: 'myapp',
            };
            const config = (0, database_config_1.databaseConfig)();
            expect(config.database).toBe('myapp');
        });
        it('should use default test database name when not specified', () => {
            process.env = {
                NODE_ENV: 'test',
            };
            const config = (0, database_config_1.databaseConfig)();
            expect(config.database).toBe('sentinel_test');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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