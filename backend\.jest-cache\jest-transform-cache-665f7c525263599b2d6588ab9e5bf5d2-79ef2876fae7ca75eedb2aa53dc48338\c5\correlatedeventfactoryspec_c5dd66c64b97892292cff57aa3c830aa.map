{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\__tests__\\correlated-event.factory.spec.ts", "mappings": ";;AAAA,0EAMqC;AACrC,oFAQgD;AAChD,iFAAwE;AAExE,gEAA8D;AAC9D,iEAAwD;AACxD,yEAAgE;AAChE,qEAA4D;AAC5D,2FAAiF;AACjF,6EAAoE;AACpE,gHAA+F;AAC/F,kHAAiG;AACjG,4GAA2F;AAE3F,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;IACtC,IAAI,iBAAgC,CAAC;IACrC,IAAI,iBAAgC,CAAC;IAErC,UAAU,CAAC,GAAG,EAAE;QACd,6BAA6B;QAC7B,iBAAiB,GAAG,2CAAa,CAAC,MAAM,CAAC;YACvC,SAAS,EAAE,6CAAc,CAAC,GAAG,EAAE;YAC/B,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;gBACzB,IAAI,EAAE,MAAM;gBACZ,UAAU,EAAE,eAAe;gBAC3B,IAAI,EAAE,kBAAkB;aACzB,CAAC;YACF,cAAc,EAAE;gBACd,UAAU,EAAE,IAAI,IAAI,EAAE;gBACtB,WAAW,EAAE,IAAI,IAAI,EAAE;gBACvB,kBAAkB,EAAE,GAAG;gBACvB,OAAO,EAAE,OAAO;aACjB;SACF,CAAC,CAAC;QAEH,6BAA6B;QAC7B,iBAAiB,GAAG;YAClB,EAAE,EAAE,8BAAc,CAAC,MAAM,EAAE;YAC3B,QAAQ,EAAE,iBAAiB;YAC3B,IAAI,EAAE,2BAAS,CAAC,cAAc;YAC9B,QAAQ,EAAE,mCAAa,CAAC,IAAI;YAC5B,MAAM,EAAE,+BAAW,CAAC,MAAM;YAC1B,gBAAgB,EAAE,oDAAqB,CAAC,QAAQ;YAChD,YAAY,EAAE;gBACZ,cAAc,EAAE,YAAY;gBAC5B,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACrC,iBAAiB,EAAE,CAAC,cAAc,EAAE,mBAAmB,CAAC;gBACxD,UAAU,EAAE,EAAE;aACf;YACD,KAAK,EAAE,8BAA8B;YACrC,WAAW,EAAE,iDAAiD;YAC9D,IAAI,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,UAAU,CAAC;YACxC,SAAS,EAAE,EAAE;YACb,eAAe,EAAE,uCAAe,CAAC,IAAI;YACrC,UAAU,EAAE;gBACV,cAAc,EAAE,YAAY;aAC7B;SACe,CAAC;IACrB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,OAAO,GAAiC;gBAC5C,aAAa,EAAE,iBAAiB;gBAChC,cAAc,EAAE;oBACd,cAAc,EAAE,UAAU;oBAC1B,cAAc,EAAE,CAAC;oBACjB,cAAc,EAAE,CAAC,mBAAmB,CAAC;iBACtC;aACF,CAAC;YAEF,MAAM,eAAe,GAAG,iDAAsB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE/D,MAAM,CAAC,eAAe,CAAC,CAAC,cAAc,CAAC,yCAAe,CAAC,CAAC;YACxD,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YACtE,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACrE,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAC1D,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAClE,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,2CAAiB,CAAC,OAAO,CAAC,CAAC;YAC1E,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,QAAQ,GAAG,8BAAc,CAAC,MAAM,EAAE,CAAC;YACzC,MAAM,OAAO,GAAiC;gBAC5C,EAAE,EAAE,QAAQ;gBACZ,aAAa,EAAE,iBAAiB;gBAChC,cAAc,EAAE;oBACd,cAAc,EAAE,UAAU;iBAC3B;aACF,CAAC;YAEF,MAAM,eAAe,GAAG,iDAAsB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE/D,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,IAAI,GAAoB;gBAC5B,EAAE,EAAE,iBAAiB;gBACrB,IAAI,EAAE,2BAA2B;gBACjC,WAAW,EAAE,qCAAqC;gBAClD,IAAI,EAAE,6CAAmB,CAAC,QAAQ;gBAClC,QAAQ,EAAE,GAAG;gBACb,QAAQ,EAAE,KAAK;gBACf,YAAY,EAAE,OAAO;gBACrB,aAAa,EAAE,EAAE;aAClB,CAAC;YAEF,MAAM,OAAO,GAAiC;gBAC5C,aAAa,EAAE,iBAAiB;gBAChC,cAAc,EAAE,EAAE,cAAc,EAAE,UAAU,EAAE;gBAC9C,YAAY,EAAE,CAAC,IAAI,CAAC;aACrB,CAAC;YAEF,MAAM,eAAe,GAAG,iDAAsB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE/D,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrD,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,KAAK,GAAqB;gBAC9B,OAAO,EAAE,8BAAc,CAAC,MAAM,EAAE;gBAChC,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,8CAAoB,CAAC,QAAQ;gBACxC,MAAM,EAAE,iBAAiB;gBACzB,OAAO,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE;gBAChC,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,IAAI;aACb,CAAC;YAEF,MAAM,OAAO,GAAiC;gBAC5C,aAAa,EAAE,iBAAiB;gBAChC,cAAc,EAAE,EAAE,cAAc,EAAE,UAAU,EAAE;gBAC9C,kBAAkB,EAAE,CAAC,KAAK,CAAC;aAC5B,CAAC;YAEF,MAAM,eAAe,GAAG,iDAAsB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE/D,MAAM,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC3D,MAAM,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,OAAO,GAAiC;gBAC5C,aAAa,EAAE,iBAAiB;gBAChC,cAAc,EAAE,EAAE,cAAc,EAAE,UAAU,EAAE;gBAC9C,IAAI,EAAE,2BAAS,CAAC,kBAAkB;gBAClC,QAAQ,EAAE,mCAAa,CAAC,QAAQ;gBAChC,KAAK,EAAE,+BAA+B;aACvC,CAAC;YAEF,MAAM,eAAe,GAAG,iDAAsB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE/D,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,kBAAkB,CAAC,CAAC;YAChE,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,QAAQ,CAAC,CAAC;YAC9D,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,OAAO,GAAiC;gBAC5C,aAAa,EAAE,iBAAiB;gBAChC,cAAc,EAAE,EAAE,cAAc,EAAE,UAAU,EAAE;aAC/C,CAAC;YAEF,MAAM,eAAe,GAAG,iDAAsB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE/D,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;YACpD,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,mBAAmB,GAAqB;gBAC5C,OAAO,EAAE,8BAAc,CAAC,MAAM,EAAE;gBAChC,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,8CAAoB,CAAC,OAAO;gBACvC,MAAM,EAAE,gBAAgB;gBACxB,OAAO,EAAE,EAAE;gBACX,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,IAAI;aACb,CAAC;YAEF,MAAM,OAAO,GAAiC;gBAC5C,aAAa,EAAE,iBAAiB;gBAChC,cAAc,EAAE,EAAE,cAAc,EAAE,UAAU,EAAE;gBAC9C,kBAAkB,EAAE,CAAC,mBAAmB,CAAC;aAC1C,CAAC;YAEF,MAAM,eAAe,GAAG,iDAAsB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE/D,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,uCAAe,CAAC,SAAS,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,MAAM,GAA+B;gBACzC,cAAc,EAAE;oBACd;wBACE,EAAE,EAAE,eAAe;wBACnB,IAAI,EAAE,eAAe;wBACrB,WAAW,EAAE,2BAA2B;wBACxC,IAAI,EAAE,6CAAmB,CAAC,QAAQ;wBAClC,QAAQ,EAAE,GAAG;wBACb,QAAQ,EAAE,KAAK;wBACf,YAAY,EAAE,OAAO;wBACrB,aAAa,EAAE,EAAE;qBAClB;iBACF;gBACD,8BAA8B,EAAE,EAAE;gBAClC,gBAAgB,EAAE,CAAC,6CAAmB,CAAC,QAAQ,EAAE,6CAAmB,CAAC,OAAO,CAAC;aAC9E,CAAC;YAEF,MAAM,eAAe,GAAG,iDAAsB,CAAC,qBAAqB,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;YAEhG,MAAM,CAAC,eAAe,CAAC,CAAC,cAAc,CAAC,yCAAe,CAAC,CAAC;YACxD,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,2CAAiB,CAAC,SAAS,CAAC,CAAC;YAC5E,MAAM,CAAC,eAAe,CAAC,uBAAuB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;YACxE,MAAM,MAAM,GAA+B;gBACzC,cAAc,EAAE;oBACd;wBACE,EAAE,EAAE,eAAe;wBACnB,IAAI,EAAE,eAAe;wBACrB,WAAW,EAAE,0CAA0C;wBACvD,IAAI,EAAE,6CAAmB,CAAC,OAAO;wBACjC,QAAQ,EAAE,GAAG;wBACb,QAAQ,EAAE,IAAI;wBACd,YAAY,EAAE,OAAO;wBACrB,aAAa,EAAE,EAAE;qBAClB;iBACF;gBACD,8BAA8B,EAAE,EAAE,CAAC,oCAAoC;aACxE,CAAC;YAEF,MAAM,eAAe,GAAG,iDAAsB,CAAC,qBAAqB,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;YAEhG,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,2CAAiB,CAAC,MAAM,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mEAAmE,EAAE,GAAG,EAAE;YAC3E,MAAM,MAAM,GAA+B;gBACzC,oCAAoC,EAAE,IAAI;gBAC1C,8BAA8B,EAAE,EAAE;aACnC,CAAC;YAEF,wEAAwE;YACxE,MAAM,qBAAqB,GAAG;gBAC5B,GAAG,iBAAiB;gBACpB,QAAQ,EAAE,mCAAa,CAAC,QAAQ;aAChB,CAAC;YAEnB,MAAM,eAAe,GAAG,iDAAsB,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;YAEpG,MAAM,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;QAC9C,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,kBAAkB,GAAgC;gBACtD,MAAM,EAAE,CAAC,iBAAiB,CAAC;gBAC3B,YAAY,EAAE,OAAO;gBACrB,aAAa,EAAE,uCAAe,CAAC,MAAM;gBACrC,SAAS,EAAE,CAAC;gBACZ,yBAAyB,EAAE,IAAI;aAChC,CAAC;YAEF,MAAM,eAAe,GAAG,iDAAsB,CAAC,8BAA8B,CAC3E,iBAAiB,EACjB,kBAAkB,CACnB,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,CAAC,cAAc,CAAC,yCAAe,CAAC,CAAC;YACxD,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,2CAAiB,CAAC,SAAS,CAAC,CAAC;YAC5E,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,kBAAkB,GAAgC;gBACtD,MAAM,EAAE,CAAC,iBAAiB,CAAC;gBAC3B,YAAY,EAAE,OAAO;gBACrB,aAAa,EAAE,uCAAe,CAAC,GAAG,EAAE,6CAA6C;gBACjF,SAAS,EAAE,CAAC;gBACZ,yBAAyB,EAAE,IAAI;aAChC,CAAC;YAEF,oDAAoD;YACpD,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC;YACnC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,sBAAsB;YAExD,MAAM,eAAe,GAAG,iDAAsB,CAAC,8BAA8B,CAC3E,iBAAiB,EACjB,kBAAkB,CACnB,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YAClD,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YAEtE,+BAA+B;YAC/B,IAAI,CAAC,MAAM,GAAG,cAAc,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,cAAc,GAAG;gBACrB,iBAAiB;gBACjB,EAAE,GAAG,iBAAiB,EAAE,EAAE,EAAE,8BAAc,CAAC,MAAM,EAAE,EAAmB;gBACtE,EAAE,GAAG,iBAAiB,EAAE,EAAE,EAAE,8BAAc,CAAC,MAAM,EAAE,EAAmB;aACvE,CAAC;YAEF,MAAM,YAAY,GAA4B;gBAC5C,cAAc;gBACd,KAAK,EAAE;oBACL;wBACE,EAAE,EAAE,YAAY;wBAChB,IAAI,EAAE,YAAY;wBAClB,WAAW,EAAE,2BAA2B;wBACxC,IAAI,EAAE,6CAAmB,CAAC,QAAQ;wBAClC,QAAQ,EAAE,GAAG;wBACb,QAAQ,EAAE,KAAK;wBACf,YAAY,EAAE,OAAO;wBACrB,aAAa,EAAE,EAAE;qBAClB;iBACF;aACF,CAAC;YAEF,MAAM,MAAM,GAAG,iDAAsB,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;YAEhE,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,oBAAoB,GAAG;gBAC3B,GAAG,iBAAiB;gBACpB,EAAE,EAAE,IAAI,CAAC,8BAA8B;aACjC,CAAC;YAET,MAAM,YAAY,GAA4B;gBAC5C,cAAc,EAAE,CAAC,iBAAiB,EAAE,oBAAoB,CAAC;gBACzD,KAAK,EAAE,EAAE;gBACT,aAAa,EAAE,KAAK;aACrB,CAAC;YAEF,MAAM,MAAM,GAAG,iDAAsB,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;YAEhE,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,oBAAoB,GAAG;gBAC3B,GAAG,iBAAiB;gBACpB,EAAE,EAAE,IAAI,CAAC,8BAA8B;aACjC,CAAC;YAET,MAAM,kBAAkB,GAAG;gBACzB,GAAG,iBAAiB;gBACpB,EAAE,EAAE,8BAAc,CAAC,MAAM,EAAE;aACX,CAAC;YAEnB,MAAM,YAAY,GAA4B;gBAC5C,cAAc,EAAE,CAAC,oBAAoB,EAAE,kBAAkB,CAAC;gBAC1D,KAAK,EAAE,EAAE;gBACT,aAAa,EAAE,IAAI;aACpB,CAAC;YAEF,MAAM,MAAM,GAAG,iDAAsB,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;YAEhE,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,iEAAiE,EAAE,GAAG,EAAE;YACzE,MAAM,eAAe,GAAG,iDAAsB,CAAC,gBAAgB,EAAE,CAAC;YAElE,MAAM,CAAC,eAAe,CAAC,CAAC,cAAc,CAAC,yCAAe,CAAC,CAAC;YACxD,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,2CAAiB,CAAC,SAAS,CAAC,CAAC;YAC5E,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,uCAAe,CAAC,IAAI,CAAC,CAAC;YACnE,MAAM,CAAC,eAAe,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACzD,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrD,MAAM,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC3D,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACxD,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;YAC3E,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,mBAAmB,GAAG;gBAC1B,GAAG,iBAAiB;gBACpB,KAAK,EAAE,mBAAmB;aACV,CAAC;YAEnB,MAAM,SAAS,GAA0C;gBACvD,aAAa,EAAE,mBAAmB;gBAClC,iBAAiB,EAAE,2CAAiB,CAAC,MAAM;gBAC3C,eAAe,EAAE,uCAAe,CAAC,GAAG;gBACpC,uBAAuB,EAAE,EAAE;aAC5B,CAAC;YAEF,MAAM,eAAe,GAAG,iDAAsB,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YAE3E,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACxD,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,2CAAiB,CAAC,MAAM,CAAC,CAAC;YACzE,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,uCAAe,CAAC,GAAG,CAAC,CAAC;YAClE,MAAM,CAAC,eAAe,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,eAAe,GAAG,iDAAsB,CAAC,gBAAgB,EAAE,CAAC;YAElE,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7D,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC,WAAW,EAAE,CAAC;YAC3E,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC,CAAC,mBAAmB,EAAE,eAAe,CAAC,CAAC,CAAC;YAC5G,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxE,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACnE,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC5E,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,GAAG,GAAI,iDAA8B,CAAC,qBAAqB,EAAE,CAAC;YACpE,MAAM,GAAG,GAAI,iDAA8B,CAAC,qBAAqB,EAAE,CAAC;YAEpE,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;YAC5C,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;YAC5C,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,oBAAoB,GAAuB;gBAC/C;oBACE,OAAO,EAAE,8BAAc,CAAC,MAAM,EAAE;oBAChC,UAAU,EAAE,EAAE;oBACd,SAAS,EAAE,8CAAoB,CAAC,KAAK;oBACrC,MAAM,EAAE,OAAO;oBACf,OAAO,EAAE,EAAE;oBACX,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,MAAM,EAAE,GAAG;iBACZ;aACF,CAAC;YAEF,MAAM,qBAAqB,GAAuB;gBAChD;oBACE,OAAO,EAAE,8BAAc,CAAC,MAAM,EAAE;oBAChC,UAAU,EAAE,EAAE;oBACd,SAAS,EAAE,8CAAoB,CAAC,KAAK;oBACrC,MAAM,EAAE,OAAO;oBACf,OAAO,EAAE,EAAE;oBACX,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,MAAM,EAAE,IAAI;iBACb;aACF,CAAC;YAEF,MAAM,aAAa,GAAI,iDAA8B,CAAC,wBAAwB,CAAC,oBAAoB,CAAC,CAAC;YACrG,MAAM,cAAc,GAAI,iDAA8B,CAAC,wBAAwB,CAAC,qBAAqB,CAAC,CAAC;YAEvG,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,uCAAe,CAAC,GAAG,CAAC,CAAC;YAChD,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,uCAAe,CAAC,SAAS,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,YAAY,GAAG;gBACnB,UAAU,EAAE,gBAAgB;gBAC5B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,SAAS,EAAE,eAAe;aAC3B,CAAC;YAEF,MAAM,SAAS,GAAG,CAAC,6CAAmB,CAAC,QAAQ,EAAE,6CAAmB,CAAC,OAAO,CAAC,CAAC;YAE9E,MAAM,cAAc,GAAI,iDAA8B,CAAC,kBAAkB,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;YAEnG,MAAM,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC;gBAC7B,GAAG,YAAY;gBACf,aAAa,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBACjC,sBAAsB,EAAE,SAAS;aAClC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,OAAO,GAAiC;gBAC5C,aAAa,EAAE,IAAW;gBAC1B,cAAc,EAAE,EAAE,cAAc,EAAE,UAAU,EAAE;aAC/C,CAAC;YAEF,MAAM,CAAC,GAAG,EAAE,CAAC,iDAAsB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,OAAO,GAAiC;gBAC5C,aAAa,EAAE,iBAAiB;gBAChC,cAAc,EAAE,IAAW;aAC5B,CAAC;YAEF,MAAM,CAAC,GAAG,EAAE,CAAC,iDAAsB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,YAAY,GAA4B;gBAC5C,cAAc,EAAE,EAAE;gBAClB,KAAK,EAAE,EAAE;aACV,CAAC;YAEF,MAAM,MAAM,GAAG,iDAAsB,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;YAEhE,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\__tests__\\correlated-event.factory.spec.ts"], "sourcesContent": ["import { \r\n  CorrelatedEventFactory, \r\n  CreateCorrelatedEventOptions, \r\n  CorrelationConfig, \r\n  BatchCorrelationOptions, \r\n  AttackChainDetectionOptions \r\n} from '../correlated-event.factory';\r\nimport { \r\n  CorrelatedEvent, \r\n  CorrelationRule, \r\n  CorrelationMatch, \r\n  CorrelationResult, \r\n  Attack<PERSON>hain, \r\n  CorrelationRuleType, \r\n  CorrelationMatchType \r\n} from '../../entities/correlated-event.entity';\r\nimport { CorrelationStatus } from '../../enums/correlation-status.enum';\r\nimport { EnrichedEvent } from '../../entities/enriched-event.entity';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel';\r\nimport { EventType } from '../../enums/event-type.enum';\r\nimport { EventSeverity } from '../../enums/event-severity.enum';\r\nimport { EventStatus } from '../../enums/event-status.enum';\r\nimport { EventProcessingStatus } from '../../enums/event-processing-status.enum';\r\nimport { ConfidenceLevel } from '../../enums/confidence-level.enum';\r\nimport { EventMetadata } from '../../value-objects/event-metadata/event-metadata.value-object';\r\nimport { EventTimestamp } from '../../value-objects/event-metadata/event-timestamp.value-object';\r\nimport { EventSource } from '../../value-objects/event-metadata/event-source.value-object';\r\n\r\ndescribe('CorrelatedEventFactory', () => {\r\n  let mockEnrichedEvent: EnrichedEvent;\r\n  let mockEventMetadata: EventMetadata;\r\n\r\n  beforeEach(() => {\r\n    // Create mock event metadata\r\n    mockEventMetadata = EventMetadata.create({\r\n      timestamp: EventTimestamp.now(),\r\n      source: EventSource.create({\r\n        type: 'SIEM',\r\n        identifier: 'test-siem-001',\r\n        name: 'Test SIEM System'\r\n      }),\r\n      processingInfo: {\r\n        receivedAt: new Date(),\r\n        processedAt: new Date(),\r\n        processingDuration: 100,\r\n        version: '1.0.0'\r\n      }\r\n    });\r\n\r\n    // Create mock enriched event\r\n    mockEnrichedEvent = {\r\n      id: UniqueEntityId.create(),\r\n      metadata: mockEventMetadata,\r\n      type: EventType.SECURITY_ALERT,\r\n      severity: EventSeverity.HIGH,\r\n      status: EventStatus.ACTIVE,\r\n      processingStatus: EventProcessingStatus.ENRICHED,\r\n      enrichedData: {\r\n        original_event: 'test_event',\r\n        enriched_at: new Date().toISOString(),\r\n        threat_indicators: ['malicious_ip', 'suspicious_domain'],\r\n        risk_score: 75\r\n      },\r\n      title: 'Test Enriched Security Event',\r\n      description: 'A test enriched security event for unit testing',\r\n      tags: ['test', 'enrichment', 'security'],\r\n      riskScore: 75,\r\n      confidenceLevel: ConfidenceLevel.HIGH,\r\n      attributes: {\r\n        test_attribute: 'test_value'\r\n      }\r\n    } as EnrichedEvent;\r\n  });\r\n\r\n  describe('create', () => {\r\n    it('should create a CorrelatedEvent with basic options', () => {\r\n      const options: CreateCorrelatedEventOptions = {\r\n        enrichedEvent: mockEnrichedEvent,\r\n        correlatedData: {\r\n          correlation_id: 'corr_123',\r\n          related_events: 3,\r\n          patterns_found: ['temporal_sequence']\r\n        }\r\n      };\r\n\r\n      const correlatedEvent = CorrelatedEventFactory.create(options);\r\n\r\n      expect(correlatedEvent).toBeInstanceOf(CorrelatedEvent);\r\n      expect(correlatedEvent.enrichedEventId).toEqual(mockEnrichedEvent.id);\r\n      expect(correlatedEvent.metadata).toEqual(mockEnrichedEvent.metadata);\r\n      expect(correlatedEvent.type).toBe(mockEnrichedEvent.type);\r\n      expect(correlatedEvent.severity).toBe(mockEnrichedEvent.severity);\r\n      expect(correlatedEvent.correlationStatus).toBe(CorrelationStatus.PENDING);\r\n      expect(correlatedEvent.correlatedData).toEqual(options.correlatedData);\r\n    });\r\n\r\n    it('should create a CorrelatedEvent with custom ID', () => {\r\n      const customId = UniqueEntityId.create();\r\n      const options: CreateCorrelatedEventOptions = {\r\n        id: customId,\r\n        enrichedEvent: mockEnrichedEvent,\r\n        correlatedData: {\r\n          correlation_id: 'corr_123'\r\n        }\r\n      };\r\n\r\n      const correlatedEvent = CorrelatedEventFactory.create(options);\r\n\r\n      expect(correlatedEvent.id).toEqual(customId);\r\n    });\r\n\r\n    it('should create a CorrelatedEvent with applied rules', () => {\r\n      const rule: CorrelationRule = {\r\n        id: 'temporal_rule_1',\r\n        name: 'Temporal Correlation Rule',\r\n        description: 'Rule for temporal event correlation',\r\n        type: CorrelationRuleType.TEMPORAL,\r\n        priority: 100,\r\n        required: false,\r\n        timeWindowMs: 3600000,\r\n        minConfidence: 70\r\n      };\r\n\r\n      const options: CreateCorrelatedEventOptions = {\r\n        enrichedEvent: mockEnrichedEvent,\r\n        correlatedData: { correlation_id: 'corr_123' },\r\n        appliedRules: [rule]\r\n      };\r\n\r\n      const correlatedEvent = CorrelatedEventFactory.create(options);\r\n\r\n      expect(correlatedEvent.appliedRules).toHaveLength(1);\r\n      expect(correlatedEvent.appliedRules[0]).toEqual(rule);\r\n      expect(correlatedEvent.hasAppliedRule('temporal_rule_1')).toBe(true);\r\n    });\r\n\r\n    it('should create a CorrelatedEvent with correlation matches', () => {\r\n      const match: CorrelationMatch = {\r\n        eventId: UniqueEntityId.create(),\r\n        confidence: 85,\r\n        matchType: CorrelationMatchType.TEMPORAL,\r\n        ruleId: 'temporal_rule_1',\r\n        details: { timeWindow: 3600000 },\r\n        timestamp: new Date(),\r\n        weight: 0.85\r\n      };\r\n\r\n      const options: CreateCorrelatedEventOptions = {\r\n        enrichedEvent: mockEnrichedEvent,\r\n        correlatedData: { correlation_id: 'corr_123' },\r\n        correlationMatches: [match]\r\n      };\r\n\r\n      const correlatedEvent = CorrelatedEventFactory.create(options);\r\n\r\n      expect(correlatedEvent.correlationMatches).toHaveLength(1);\r\n      expect(correlatedEvent.correlationMatches[0]).toEqual(match);\r\n    });\r\n\r\n    it('should override enriched event properties when specified', () => {\r\n      const options: CreateCorrelatedEventOptions = {\r\n        enrichedEvent: mockEnrichedEvent,\r\n        correlatedData: { correlation_id: 'corr_123' },\r\n        type: EventType.VULNERABILITY_SCAN,\r\n        severity: EventSeverity.CRITICAL,\r\n        title: 'Custom Correlated Event Title'\r\n      };\r\n\r\n      const correlatedEvent = CorrelatedEventFactory.create(options);\r\n\r\n      expect(correlatedEvent.type).toBe(EventType.VULNERABILITY_SCAN);\r\n      expect(correlatedEvent.severity).toBe(EventSeverity.CRITICAL);\r\n      expect(correlatedEvent.title).toBe('Custom Correlated Event Title');\r\n    });\r\n\r\n    it('should generate correlation ID when not provided', () => {\r\n      const options: CreateCorrelatedEventOptions = {\r\n        enrichedEvent: mockEnrichedEvent,\r\n        correlatedData: { correlation_id: 'corr_123' }\r\n      };\r\n\r\n      const correlatedEvent = CorrelatedEventFactory.create(options);\r\n\r\n      expect(correlatedEvent.correlationId).toBeDefined();\r\n      expect(correlatedEvent.correlationId).toMatch(/^corr_/);\r\n    });\r\n\r\n    it('should calculate confidence level from correlation matches', () => {\r\n      const highConfidenceMatch: CorrelationMatch = {\r\n        eventId: UniqueEntityId.create(),\r\n        confidence: 95,\r\n        matchType: CorrelationMatchType.PATTERN,\r\n        ruleId: 'pattern_rule_1',\r\n        details: {},\r\n        timestamp: new Date(),\r\n        weight: 0.95\r\n      };\r\n\r\n      const options: CreateCorrelatedEventOptions = {\r\n        enrichedEvent: mockEnrichedEvent,\r\n        correlatedData: { correlation_id: 'corr_123' },\r\n        correlationMatches: [highConfidenceMatch]\r\n      };\r\n\r\n      const correlatedEvent = CorrelatedEventFactory.create(options);\r\n\r\n      expect(correlatedEvent.confidenceLevel).toBe(ConfidenceLevel.VERY_HIGH);\r\n    });\r\n  });\r\n\r\n  describe('createWithCorrelation', () => {\r\n    it('should create a CorrelatedEvent with automatic correlation', () => {\r\n      const config: Partial<CorrelationConfig> = {\r\n        availableRules: [\r\n          {\r\n            id: 'temporal_rule',\r\n            name: 'Temporal Rule',\r\n            description: 'Temporal correlation rule',\r\n            type: CorrelationRuleType.TEMPORAL,\r\n            priority: 100,\r\n            required: false,\r\n            timeWindowMs: 3600000,\r\n            minConfidence: 70\r\n          }\r\n        ],\r\n        minCorrelationQualityThreshold: 70,\r\n        enabledRuleTypes: [CorrelationRuleType.TEMPORAL, CorrelationRuleType.SPATIAL]\r\n      };\r\n\r\n      const correlatedEvent = CorrelatedEventFactory.createWithCorrelation(mockEnrichedEvent, config);\r\n\r\n      expect(correlatedEvent).toBeInstanceOf(CorrelatedEvent);\r\n      expect(correlatedEvent.correlationStatus).toBe(CorrelationStatus.COMPLETED);\r\n      expect(correlatedEvent.correlationQualityScore).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should set correlation status to FAILED when correlation fails', () => {\r\n      const config: Partial<CorrelationConfig> = {\r\n        availableRules: [\r\n          {\r\n            id: 'required_rule',\r\n            name: 'Required Rule',\r\n            description: 'Required correlation rule that will fail',\r\n            type: CorrelationRuleType.PATTERN,\r\n            priority: 100,\r\n            required: true,\r\n            timeWindowMs: 3600000,\r\n            minConfidence: 70\r\n          }\r\n        ],\r\n        minCorrelationQualityThreshold: 90 // High threshold to trigger failure\r\n      };\r\n\r\n      const correlatedEvent = CorrelatedEventFactory.createWithCorrelation(mockEnrichedEvent, config);\r\n\r\n      expect(correlatedEvent.correlationStatus).toBe(CorrelationStatus.FAILED);\r\n    });\r\n\r\n    it('should determine manual review requirement based on configuration', () => {\r\n      const config: Partial<CorrelationConfig> = {\r\n        requireManualReviewForHighConfidence: true,\r\n        minCorrelationQualityThreshold: 50\r\n      };\r\n\r\n      // Create enriched event with critical severity to trigger manual review\r\n      const criticalEnrichedEvent = {\r\n        ...mockEnrichedEvent,\r\n        severity: EventSeverity.CRITICAL\r\n      } as EnrichedEvent;\r\n\r\n      const correlatedEvent = CorrelatedEventFactory.createWithCorrelation(criticalEnrichedEvent, config);\r\n\r\n      expect(correlatedEvent.requiresManualReview).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('createWithAttackChainDetection', () => {\r\n    it('should create a CorrelatedEvent with attack chain detection', () => {\r\n      const attackChainOptions: AttackChainDetectionOptions = {\r\n        events: [mockEnrichedEvent],\r\n        timeWindowMs: 3600000,\r\n        minConfidence: ConfidenceLevel.MEDIUM,\r\n        maxStages: 5,\r\n        includeBehavioralAnalysis: true\r\n      };\r\n\r\n      const correlatedEvent = CorrelatedEventFactory.createWithAttackChainDetection(\r\n        mockEnrichedEvent,\r\n        attackChainOptions\r\n      );\r\n\r\n      expect(correlatedEvent).toBeInstanceOf(CorrelatedEvent);\r\n      expect(correlatedEvent.correlationStatus).toBe(CorrelationStatus.COMPLETED);\r\n      expect(correlatedEvent.correlatedData.attack_chain).toBeDefined();\r\n    });\r\n\r\n    it('should include attack chain when detected', () => {\r\n      const attackChainOptions: AttackChainDetectionOptions = {\r\n        events: [mockEnrichedEvent],\r\n        timeWindowMs: 3600000,\r\n        minConfidence: ConfidenceLevel.LOW, // Low threshold to increase detection chance\r\n        maxStages: 5,\r\n        includeBehavioralAnalysis: true\r\n      };\r\n\r\n      // Mock Math.random to ensure attack chain detection\r\n      const originalRandom = Math.random;\r\n      Math.random = jest.fn(() => 0.2); // 20% < 30% threshold\r\n\r\n      const correlatedEvent = CorrelatedEventFactory.createWithAttackChainDetection(\r\n        mockEnrichedEvent,\r\n        attackChainOptions\r\n      );\r\n\r\n      expect(correlatedEvent.hasAttackChain()).toBe(true);\r\n      expect(correlatedEvent.attackChain).toBeDefined();\r\n      expect(correlatedEvent.correlationPatterns).toContain('attack_chain');\r\n\r\n      // Restore original Math.random\r\n      Math.random = originalRandom;\r\n    });\r\n  });\r\n\r\n  describe('createBatch', () => {\r\n    it('should create multiple CorrelatedEvents in batch', () => {\r\n      const enrichedEvents = [\r\n        mockEnrichedEvent,\r\n        { ...mockEnrichedEvent, id: UniqueEntityId.create() } as EnrichedEvent,\r\n        { ...mockEnrichedEvent, id: UniqueEntityId.create() } as EnrichedEvent\r\n      ];\r\n\r\n      const batchOptions: BatchCorrelationOptions = {\r\n        enrichedEvents,\r\n        rules: [\r\n          {\r\n            id: 'batch_rule',\r\n            name: 'Batch Rule',\r\n            description: 'Rule for batch processing',\r\n            type: CorrelationRuleType.TEMPORAL,\r\n            priority: 100,\r\n            required: false,\r\n            timeWindowMs: 3600000,\r\n            minConfidence: 70\r\n          }\r\n        ]\r\n      };\r\n\r\n      const result = CorrelatedEventFactory.createBatch(batchOptions);\r\n\r\n      expect(result.successful).toHaveLength(3);\r\n      expect(result.failed).toHaveLength(0);\r\n      expect(result.summary.total).toBe(3);\r\n      expect(result.summary.successful).toBe(3);\r\n      expect(result.summary.failed).toBe(0);\r\n      expect(result.summary.processingTimeMs).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should handle batch processing failures', () => {\r\n      const invalidEnrichedEvent = {\r\n        ...mockEnrichedEvent,\r\n        id: null // Invalid ID to cause failure\r\n      } as any;\r\n\r\n      const batchOptions: BatchCorrelationOptions = {\r\n        enrichedEvents: [mockEnrichedEvent, invalidEnrichedEvent],\r\n        rules: [],\r\n        stopOnFailure: false\r\n      };\r\n\r\n      const result = CorrelatedEventFactory.createBatch(batchOptions);\r\n\r\n      expect(result.successful).toHaveLength(1);\r\n      expect(result.failed).toHaveLength(1);\r\n      expect(result.summary.successful).toBe(1);\r\n      expect(result.summary.failed).toBe(1);\r\n      expect(result.failed[0].error).toBeDefined();\r\n    });\r\n\r\n    it('should stop on first failure when configured', () => {\r\n      const invalidEnrichedEvent = {\r\n        ...mockEnrichedEvent,\r\n        id: null // Invalid ID to cause failure\r\n      } as any;\r\n\r\n      const validEnrichedEvent = {\r\n        ...mockEnrichedEvent,\r\n        id: UniqueEntityId.create()\r\n      } as EnrichedEvent;\r\n\r\n      const batchOptions: BatchCorrelationOptions = {\r\n        enrichedEvents: [invalidEnrichedEvent, validEnrichedEvent],\r\n        rules: [],\r\n        stopOnFailure: true\r\n      };\r\n\r\n      const result = CorrelatedEventFactory.createBatch(batchOptions);\r\n\r\n      expect(result.successful).toHaveLength(0);\r\n      expect(result.failed).toHaveLength(1);\r\n      expect(result.summary.total).toBe(2);\r\n    });\r\n  });\r\n\r\n  describe('createForTesting', () => {\r\n    it('should create a CorrelatedEvent for testing with default values', () => {\r\n      const correlatedEvent = CorrelatedEventFactory.createForTesting();\r\n\r\n      expect(correlatedEvent).toBeInstanceOf(CorrelatedEvent);\r\n      expect(correlatedEvent.correlationStatus).toBe(CorrelationStatus.COMPLETED);\r\n      expect(correlatedEvent.confidenceLevel).toBe(ConfidenceLevel.HIGH);\r\n      expect(correlatedEvent.correlationQualityScore).toBe(85);\r\n      expect(correlatedEvent.appliedRules).toHaveLength(2);\r\n      expect(correlatedEvent.correlationMatches).toHaveLength(2);\r\n      expect(correlatedEvent.relatedEventIds).toHaveLength(2);\r\n      expect(correlatedEvent.correlationPatterns).toContain('temporal_sequence');\r\n      expect(correlatedEvent.correlationPatterns).toContain('ip_clustering');\r\n    });\r\n\r\n    it('should create a CorrelatedEvent for testing with overrides', () => {\r\n      const customEnrichedEvent = {\r\n        ...mockEnrichedEvent,\r\n        title: 'Custom Test Event'\r\n      } as EnrichedEvent;\r\n\r\n      const overrides: Partial<CreateCorrelatedEventOptions> = {\r\n        enrichedEvent: customEnrichedEvent,\r\n        correlationStatus: CorrelationStatus.FAILED,\r\n        confidenceLevel: ConfidenceLevel.LOW,\r\n        correlationQualityScore: 45\r\n      };\r\n\r\n      const correlatedEvent = CorrelatedEventFactory.createForTesting(overrides);\r\n\r\n      expect(correlatedEvent.title).toBe('Custom Test Event');\r\n      expect(correlatedEvent.correlationStatus).toBe(CorrelationStatus.FAILED);\r\n      expect(correlatedEvent.confidenceLevel).toBe(ConfidenceLevel.LOW);\r\n      expect(correlatedEvent.correlationQualityScore).toBe(45);\r\n    });\r\n\r\n    it('should include mock correlation data for testing', () => {\r\n      const correlatedEvent = CorrelatedEventFactory.createForTesting();\r\n\r\n      expect(correlatedEvent.correlatedData.correlated).toBe(true);\r\n      expect(correlatedEvent.correlatedData.correlation_timestamp).toBeDefined();\r\n      expect(correlatedEvent.correlatedData.correlation_patterns).toEqual(['temporal_sequence', 'ip_clustering']);\r\n      expect(correlatedEvent.correlatedData.attack_chain.detected).toBe(true);\r\n      expect(correlatedEvent.correlatedData.attack_chain.stages).toBe(3);\r\n      expect(correlatedEvent.correlatedData.attack_chain.confidence).toBe('high');\r\n      expect(correlatedEvent.correlatedData.related_events).toBe(5);\r\n    });\r\n  });\r\n\r\n  describe('private helper methods', () => {\r\n    it('should generate unique correlation IDs', () => {\r\n      const id1 = (CorrelatedEventFactory as any).generateCorrelationId();\r\n      const id2 = (CorrelatedEventFactory as any).generateCorrelationId();\r\n\r\n      expect(id1).toMatch(/^corr_\\d+_[a-z0-9]+$/);\r\n      expect(id2).toMatch(/^corr_\\d+_[a-z0-9]+$/);\r\n      expect(id1).not.toBe(id2);\r\n    });\r\n\r\n    it('should calculate confidence level from matches', () => {\r\n      const lowConfidenceMatches: CorrelationMatch[] = [\r\n        {\r\n          eventId: UniqueEntityId.create(),\r\n          confidence: 30,\r\n          matchType: CorrelationMatchType.FUZZY,\r\n          ruleId: 'rule1',\r\n          details: {},\r\n          timestamp: new Date(),\r\n          weight: 0.3\r\n        }\r\n      ];\r\n\r\n      const highConfidenceMatches: CorrelationMatch[] = [\r\n        {\r\n          eventId: UniqueEntityId.create(),\r\n          confidence: 95,\r\n          matchType: CorrelationMatchType.EXACT,\r\n          ruleId: 'rule1',\r\n          details: {},\r\n          timestamp: new Date(),\r\n          weight: 0.95\r\n        }\r\n      ];\r\n\r\n      const lowConfidence = (CorrelatedEventFactory as any).calculateConfidenceLevel(lowConfidenceMatches);\r\n      const highConfidence = (CorrelatedEventFactory as any).calculateConfidenceLevel(highConfidenceMatches);\r\n\r\n      expect(lowConfidence).toBe(ConfidenceLevel.LOW);\r\n      expect(highConfidence).toBe(ConfidenceLevel.VERY_HIGH);\r\n    });\r\n\r\n    it('should correlate event data with rule types', () => {\r\n      const enrichedData = {\r\n        event_type: 'security_alert',\r\n        timestamp: new Date().toISOString(),\r\n        source_ip: '*************'\r\n      };\r\n\r\n      const ruleTypes = [CorrelationRuleType.TEMPORAL, CorrelationRuleType.SPATIAL];\r\n\r\n      const correlatedData = (CorrelatedEventFactory as any).correlateEventData(enrichedData, ruleTypes);\r\n\r\n      expect(correlatedData).toEqual({\r\n        ...enrichedData,\r\n        correlated_at: expect.any(String),\r\n        correlation_rule_types: ruleTypes\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('error handling', () => {\r\n    it('should handle missing enriched event', () => {\r\n      const options: CreateCorrelatedEventOptions = {\r\n        enrichedEvent: null as any,\r\n        correlatedData: { correlation_id: 'corr_123' }\r\n      };\r\n\r\n      expect(() => CorrelatedEventFactory.create(options)).toThrow();\r\n    });\r\n\r\n    it('should handle invalid correlation data', () => {\r\n      const options: CreateCorrelatedEventOptions = {\r\n        enrichedEvent: mockEnrichedEvent,\r\n        correlatedData: null as any\r\n      };\r\n\r\n      expect(() => CorrelatedEventFactory.create(options)).toThrow();\r\n    });\r\n\r\n    it('should handle batch processing with empty array', () => {\r\n      const batchOptions: BatchCorrelationOptions = {\r\n        enrichedEvents: [],\r\n        rules: []\r\n      };\r\n\r\n      const result = CorrelatedEventFactory.createBatch(batchOptions);\r\n\r\n      expect(result.successful).toHaveLength(0);\r\n      expect(result.failed).toHaveLength(0);\r\n      expect(result.summary.total).toBe(0);\r\n      expect(result.summary.successful).toBe(0);\r\n      expect(result.summary.failed).toBe(0);\r\n    });\r\n  });\r\n});"], "version": 3}