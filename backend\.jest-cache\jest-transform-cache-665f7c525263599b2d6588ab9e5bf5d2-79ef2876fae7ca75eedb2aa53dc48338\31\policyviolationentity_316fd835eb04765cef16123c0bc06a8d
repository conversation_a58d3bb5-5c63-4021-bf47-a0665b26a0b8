868b38c374213c3bf92267efe0efd6a1
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PolicyViolation = void 0;
const typeorm_1 = require("typeorm");
const compliance_assessment_entity_1 = require("./compliance-assessment.entity");
const policy_definition_entity_1 = require("./policy-definition.entity");
/**
 * Policy Violation entity
 * Represents violations of compliance policies and controls
 */
let PolicyViolation = class PolicyViolation {
    /**
     * Check if violation is open
     */
    get isOpen() {
        return !['resolved', 'accepted_risk', 'false_positive'].includes(this.status);
    }
    /**
     * Check if violation is critical
     */
    get isCritical() {
        return this.severity === 'critical';
    }
    /**
     * Check if violation is overdue
     */
    get isOverdue() {
        if (!this.isOpen)
            return false;
        // Calculate SLA based on severity
        const slaHours = this.getSlaHours();
        const deadline = new Date(this.detectedAt.getTime() + slaHours * 60 * 60 * 1000);
        return new Date() > deadline;
    }
    /**
     * Get violation age in hours
     */
    get ageInHours() {
        const now = new Date();
        const diffMs = now.getTime() - this.detectedAt.getTime();
        return Math.round(diffMs / (1000 * 60 * 60));
    }
    /**
     * Get time to resolution in hours
     */
    get timeToResolutionHours() {
        if (!this.resolvedAt)
            return null;
        const diffMs = this.resolvedAt.getTime() - this.detectedAt.getTime();
        return Math.round(diffMs / (1000 * 60 * 60));
    }
    /**
     * Get risk score
     */
    get riskScore() {
        return this.details.riskAssessment?.riskScore || this.calculateDefaultRiskScore();
    }
    /**
     * Assign violation to user
     */
    assign(userId, assignedBy) {
        this.assignedTo = userId;
        this.updatedBy = assignedBy;
        if (this.status === 'open') {
            this.status = 'investigating';
        }
    }
    /**
     * Start remediation
     */
    startRemediation(userId) {
        this.status = 'remediation_in_progress';
        this.updatedBy = userId;
    }
    /**
     * Resolve violation
     */
    resolve(userId, resolution) {
        this.status = 'resolved';
        this.resolvedAt = new Date();
        this.updatedBy = userId;
        if (!this.details.remediation) {
            this.details.remediation = {};
        }
        this.details.remediation.resolution = resolution;
        this.details.remediation.resolvedBy = userId;
        this.details.remediation.resolvedAt = this.resolvedAt.toISOString();
    }
    /**
     * Accept risk
     */
    acceptRisk(userId, justification) {
        this.status = 'accepted_risk';
        this.resolvedAt = new Date();
        this.updatedBy = userId;
        if (!this.details.riskAssessment) {
            this.details.riskAssessment = {
                likelihood: 'medium',
                impact: 'medium',
                riskScore: 5,
                riskLevel: 'medium',
            };
        }
        this.details.riskAssessment.acceptanceJustification = justification;
        this.details.riskAssessment.acceptedBy = userId;
        this.details.riskAssessment.acceptedAt = this.resolvedAt.toISOString();
    }
    /**
     * Mark as false positive
     */
    markAsFalsePositive(userId, reason) {
        this.status = 'false_positive';
        this.resolvedAt = new Date();
        this.updatedBy = userId;
        if (!this.details.metadata) {
            this.details.metadata = {};
        }
        this.details.metadata.falsePositiveReason = reason;
        this.details.metadata.markedBy = userId;
        this.details.metadata.markedAt = this.resolvedAt.toISOString();
    }
    /**
     * Add evidence
     */
    addEvidence(evidence) {
        if (!this.details.evidence) {
            this.details.evidence = [];
        }
        this.details.evidence.push({
            ...evidence,
            timestamp: new Date().toISOString(),
        });
    }
    /**
     * Update risk assessment
     */
    updateRiskAssessment(riskAssessment) {
        this.details.riskAssessment = {
            ...this.details.riskAssessment,
            ...riskAssessment,
        };
    }
    /**
     * Add remediation action
     */
    addRemediationAction(action, type) {
        if (!this.details.remediation) {
            this.details.remediation = {};
        }
        const actionKey = `${type}Actions`;
        if (!this.details.remediation[actionKey]) {
            this.details.remediation[actionKey] = [];
        }
        this.details.remediation[actionKey].push(action);
    }
    /**
     * Get SLA hours based on severity
     */
    getSlaHours() {
        const slaMatrix = {
            critical: 4, // 4 hours
            high: 24, // 1 day
            medium: 72, // 3 days
            low: 168, // 1 week
        };
        return slaMatrix[this.severity] || 168;
    }
    /**
     * Calculate default risk score
     */
    calculateDefaultRiskScore() {
        const severityScores = { low: 2, medium: 5, high: 8, critical: 10 };
        return severityScores[this.severity] || 5;
    }
    /**
     * Generate violation summary
     */
    generateSummary() {
        return {
            id: this.id,
            title: this.title,
            violationType: this.violationType,
            severity: this.severity,
            status: this.status,
            detectedAt: this.detectedAt,
            resolvedAt: this.resolvedAt,
            isOpen: this.isOpen,
            isCritical: this.isCritical,
            isOverdue: this.isOverdue,
            ageInHours: this.ageInHours,
            timeToResolutionHours: this.timeToResolutionHours,
            riskScore: this.riskScore,
            assignedTo: this.assignedTo,
            affectedSystemsCount: this.details.affectedSystems?.length || 0,
            affectedUsersCount: this.details.affectedUsers?.length || 0,
            evidenceCount: this.details.evidence?.length || 0,
            tags: this.tags,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }
    /**
     * Export violation for reporting
     */
    exportForReporting() {
        return {
            violation: this.generateSummary(),
            details: this.details,
            policy: this.policy ? {
                id: this.policy.id,
                name: this.policy.name,
                type: this.policy.type,
            } : null,
            assessment: this.assessment ? {
                id: this.assessment.id,
                name: this.assessment.name,
                frameworkId: this.assessment.frameworkId,
            } : null,
            exportedAt: new Date().toISOString(),
        };
    }
    /**
     * Check if violation requires immediate attention
     */
    requiresImmediateAttention() {
        return this.isCritical ||
            this.isOverdue ||
            this.details.compliance?.reportingRequired ||
            this.details.businessImpact?.financialImpact > 100000;
    }
    /**
     * Get compliance frameworks affected
     */
    getAffectedFrameworks() {
        return this.details.compliance?.frameworks || [];
    }
    /**
     * Get affected controls
     */
    getAffectedControls() {
        return this.details.violatedControls || [];
    }
};
exports.PolicyViolation = PolicyViolation;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], PolicyViolation.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], PolicyViolation.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], PolicyViolation.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: [
            'access_control',
            'data_protection',
            'authentication',
            'authorization',
            'encryption',
            'audit_logging',
            'backup_recovery',
            'incident_response',
            'change_management',
            'vulnerability_management',
            'network_security',
            'physical_security',
            'personnel_security',
            'business_continuity',
            'risk_management',
            'compliance_monitoring',
            'other',
        ],
    }),
    __metadata("design:type", String)
], PolicyViolation.prototype, "violationType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['low', 'medium', 'high', 'critical'],
    }),
    __metadata("design:type", String)
], PolicyViolation.prototype, "severity", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['open', 'investigating', 'remediation_planned', 'remediation_in_progress', 'resolved', 'accepted_risk', 'false_positive'],
        default: 'open',
    }),
    __metadata("design:type", String)
], PolicyViolation.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'detected_at', type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], PolicyViolation.prototype, "detectedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'resolved_at', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], PolicyViolation.prototype, "resolvedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    __metadata("design:type", Object)
], PolicyViolation.prototype, "details", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, default: '{}' }),
    __metadata("design:type", Array)
], PolicyViolation.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'detected_by', type: 'uuid' }),
    __metadata("design:type", String)
], PolicyViolation.prototype, "detectedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'assigned_to', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], PolicyViolation.prototype, "assignedTo", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], PolicyViolation.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], PolicyViolation.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], PolicyViolation.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => policy_definition_entity_1.PolicyDefinition, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'policy_id' }),
    __metadata("design:type", typeof (_e = typeof policy_definition_entity_1.PolicyDefinition !== "undefined" && policy_definition_entity_1.PolicyDefinition) === "function" ? _e : Object)
], PolicyViolation.prototype, "policy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'policy_id', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], PolicyViolation.prototype, "policyId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => compliance_assessment_entity_1.ComplianceAssessment, assessment => assessment.violations, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'assessment_id' }),
    __metadata("design:type", typeof (_f = typeof compliance_assessment_entity_1.ComplianceAssessment !== "undefined" && compliance_assessment_entity_1.ComplianceAssessment) === "function" ? _f : Object)
], PolicyViolation.prototype, "assessment", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'assessment_id', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], PolicyViolation.prototype, "assessmentId", void 0);
exports.PolicyViolation = PolicyViolation = __decorate([
    (0, typeorm_1.Entity)('policy_violations'),
    (0, typeorm_1.Index)(['policyId']),
    (0, typeorm_1.Index)(['assessmentId']),
    (0, typeorm_1.Index)(['severity']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['detectedAt']),
    (0, typeorm_1.Index)(['violationType'])
], PolicyViolation);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxtb2R1bGVzXFxjb21wbGlhbmNlLWF1ZGl0XFxkb21haW5cXGVudGl0aWVzXFxwb2xpY3ktdmlvbGF0aW9uLmVudGl0eS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQUEscUNBU2lCO0FBQ2pCLGlGQUFzRTtBQUN0RSx5RUFBOEQ7QUFFOUQ7OztHQUdHO0FBUUksSUFBTSxlQUFlLEdBQXJCLE1BQU0sZUFBZTtJQXlPMUI7O09BRUc7SUFDSCxJQUFJLE1BQU07UUFDUixPQUFPLENBQUMsQ0FBQyxVQUFVLEVBQUUsZUFBZSxFQUFFLGdCQUFnQixDQUFDLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQztJQUNoRixDQUFDO0lBRUQ7O09BRUc7SUFDSCxJQUFJLFVBQVU7UUFDWixPQUFPLElBQUksQ0FBQyxRQUFRLEtBQUssVUFBVSxDQUFDO0lBQ3RDLENBQUM7SUFFRDs7T0FFRztJQUNILElBQUksU0FBUztRQUNYLElBQUksQ0FBQyxJQUFJLENBQUMsTUFBTTtZQUFFLE9BQU8sS0FBSyxDQUFDO1FBRS9CLGtDQUFrQztRQUNsQyxNQUFNLFFBQVEsR0FBRyxJQUFJLENBQUMsV0FBVyxFQUFFLENBQUM7UUFDcEMsTUFBTSxRQUFRLEdBQUcsSUFBSSxJQUFJLENBQUMsSUFBSSxDQUFDLFVBQVUsQ0FBQyxPQUFPLEVBQUUsR0FBRyxRQUFRLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxJQUFJLENBQUMsQ0FBQztRQUVqRixPQUFPLElBQUksSUFBSSxFQUFFLEdBQUcsUUFBUSxDQUFDO0lBQy9CLENBQUM7SUFFRDs7T0FFRztJQUNILElBQUksVUFBVTtRQUNaLE1BQU0sR0FBRyxHQUFHLElBQUksSUFBSSxFQUFFLENBQUM7UUFDdkIsTUFBTSxNQUFNLEdBQUcsR0FBRyxDQUFDLE9BQU8sRUFBRSxHQUFHLElBQUksQ0FBQyxVQUFVLENBQUMsT0FBTyxFQUFFLENBQUM7UUFDekQsT0FBTyxJQUFJLENBQUMsS0FBSyxDQUFDLE1BQU0sR0FBRyxDQUFDLElBQUksR0FBRyxFQUFFLEdBQUcsRUFBRSxDQUFDLENBQUMsQ0FBQztJQUMvQyxDQUFDO0lBRUQ7O09BRUc7SUFDSCxJQUFJLHFCQUFxQjtRQUN2QixJQUFJLENBQUMsSUFBSSxDQUFDLFVBQVU7WUFBRSxPQUFPLElBQUksQ0FBQztRQUNsQyxNQUFNLE1BQU0sR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFDLE9BQU8sRUFBRSxHQUFHLElBQUksQ0FBQyxVQUFVLENBQUMsT0FBTyxFQUFFLENBQUM7UUFDckUsT0FBTyxJQUFJLENBQUMsS0FBSyxDQUFDLE1BQU0sR0FBRyxDQUFDLElBQUksR0FBRyxFQUFFLEdBQUcsRUFBRSxDQUFDLENBQUMsQ0FBQztJQUMvQyxDQUFDO0lBRUQ7O09BRUc7SUFDSCxJQUFJLFNBQVM7UUFDWCxPQUFPLElBQUksQ0FBQyxPQUFPLENBQUMsY0FBYyxFQUFFLFNBQVMsSUFBSSxJQUFJLENBQUMseUJBQXlCLEVBQUUsQ0FBQztJQUNwRixDQUFDO0lBRUQ7O09BRUc7SUFDSCxNQUFNLENBQUMsTUFBYyxFQUFFLFVBQWtCO1FBQ3ZDLElBQUksQ0FBQyxVQUFVLEdBQUcsTUFBTSxDQUFDO1FBQ3pCLElBQUksQ0FBQyxTQUFTLEdBQUcsVUFBVSxDQUFDO1FBRTVCLElBQUksSUFBSSxDQUFDLE1BQU0sS0FBSyxNQUFNLEVBQUUsQ0FBQztZQUMzQixJQUFJLENBQUMsTUFBTSxHQUFHLGVBQWUsQ0FBQztRQUNoQyxDQUFDO0lBQ0gsQ0FBQztJQUVEOztPQUVHO0lBQ0gsZ0JBQWdCLENBQUMsTUFBYztRQUM3QixJQUFJLENBQUMsTUFBTSxHQUFHLHlCQUF5QixDQUFDO1FBQ3hDLElBQUksQ0FBQyxTQUFTLEdBQUcsTUFBTSxDQUFDO0lBQzFCLENBQUM7SUFFRDs7T0FFRztJQUNILE9BQU8sQ0FBQyxNQUFjLEVBQUUsVUFBa0I7UUFDeEMsSUFBSSxDQUFDLE1BQU0sR0FBRyxVQUFVLENBQUM7UUFDekIsSUFBSSxDQUFDLFVBQVUsR0FBRyxJQUFJLElBQUksRUFBRSxDQUFDO1FBQzdCLElBQUksQ0FBQyxTQUFTLEdBQUcsTUFBTSxDQUFDO1FBRXhCLElBQUksQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLFdBQVcsRUFBRSxDQUFDO1lBQzlCLElBQUksQ0FBQyxPQUFPLENBQUMsV0FBVyxHQUFHLEVBQUUsQ0FBQztRQUNoQyxDQUFDO1FBRUQsSUFBSSxDQUFDLE9BQU8sQ0FBQyxXQUFXLENBQUMsVUFBVSxHQUFHLFVBQVUsQ0FBQztRQUNqRCxJQUFJLENBQUMsT0FBTyxDQUFDLFdBQVcsQ0FBQyxVQUFVLEdBQUcsTUFBTSxDQUFDO1FBQzdDLElBQUksQ0FBQyxPQUFPLENBQUMsV0FBVyxDQUFDLFVBQVUsR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFDLFdBQVcsRUFBRSxDQUFDO0lBQ3RFLENBQUM7SUFFRDs7T0FFRztJQUNILFVBQVUsQ0FBQyxNQUFjLEVBQUUsYUFBcUI7UUFDOUMsSUFBSSxDQUFDLE1BQU0sR0FBRyxlQUFlLENBQUM7UUFDOUIsSUFBSSxDQUFDLFVBQVUsR0FBRyxJQUFJLElBQUksRUFBRSxDQUFDO1FBQzdCLElBQUksQ0FBQyxTQUFTLEdBQUcsTUFBTSxDQUFDO1FBRXhCLElBQUksQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLGNBQWMsRUFBRSxDQUFDO1lBQ2pDLElBQUksQ0FBQyxPQUFPLENBQUMsY0FBYyxHQUFHO2dCQUM1QixVQUFVLEVBQUUsUUFBUTtnQkFDcEIsTUFBTSxFQUFFLFFBQVE7Z0JBQ2hCLFNBQVMsRUFBRSxDQUFDO2dCQUNaLFNBQVMsRUFBRSxRQUFRO2FBQ3BCLENBQUM7UUFDSixDQUFDO1FBRUQsSUFBSSxDQUFDLE9BQU8sQ0FBQyxjQUFjLENBQUMsdUJBQXVCLEdBQUcsYUFBYSxDQUFDO1FBQ3BFLElBQUksQ0FBQyxPQUFPLENBQUMsY0FBYyxDQUFDLFVBQVUsR0FBRyxNQUFNLENBQUM7UUFDaEQsSUFBSSxDQUFDLE9BQU8sQ0FBQyxjQUFjLENBQUMsVUFBVSxHQUFHLElBQUksQ0FBQyxVQUFVLENBQUMsV0FBVyxFQUFFLENBQUM7SUFDekUsQ0FBQztJQUVEOztPQUVHO0lBQ0gsbUJBQW1CLENBQUMsTUFBYyxFQUFFLE1BQWM7UUFDaEQsSUFBSSxDQUFDLE1BQU0sR0FBRyxnQkFBZ0IsQ0FBQztRQUMvQixJQUFJLENBQUMsVUFBVSxHQUFHLElBQUksSUFBSSxFQUFFLENBQUM7UUFDN0IsSUFBSSxDQUFDLFNBQVMsR0FBRyxNQUFNLENBQUM7UUFFeEIsSUFBSSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsUUFBUSxFQUFFLENBQUM7WUFDM0IsSUFBSSxDQUFDLE9BQU8sQ0FBQyxRQUFRLEdBQUcsRUFBRSxDQUFDO1FBQzdCLENBQUM7UUFFRCxJQUFJLENBQUMsT0FBTyxDQUFDLFFBQVEsQ0FBQyxtQkFBbUIsR0FBRyxNQUFNLENBQUM7UUFDbkQsSUFBSSxDQUFDLE9BQU8sQ0FBQyxRQUFRLENBQUMsUUFBUSxHQUFHLE1BQU0sQ0FBQztRQUN4QyxJQUFJLENBQUMsT0FBTyxDQUFDLFFBQVEsQ0FBQyxRQUFRLEdBQUcsSUFBSSxDQUFDLFVBQVUsQ0FBQyxXQUFXLEVBQUUsQ0FBQztJQUNqRSxDQUFDO0lBRUQ7O09BRUc7SUFDSCxXQUFXLENBQUMsUUFBYTtRQUN2QixJQUFJLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxRQUFRLEVBQUUsQ0FBQztZQUMzQixJQUFJLENBQUMsT0FBTyxDQUFDLFFBQVEsR0FBRyxFQUFFLENBQUM7UUFDN0IsQ0FBQztRQUVELElBQUksQ0FBQyxPQUFPLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQztZQUN6QixHQUFHLFFBQVE7WUFDWCxTQUFTLEVBQUUsSUFBSSxJQUFJLEVBQUUsQ0FBQyxXQUFXLEVBQUU7U0FDcEMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQztJQUVEOztPQUVHO0lBQ0gsb0JBQW9CLENBQUMsY0FBbUI7UUFDdEMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxjQUFjLEdBQUc7WUFDNUIsR0FBRyxJQUFJLENBQUMsT0FBTyxDQUFDLGNBQWM7WUFDOUIsR0FBRyxjQUFjO1NBQ2xCLENBQUM7SUFDSixDQUFDO0lBRUQ7O09BRUc7SUFDSCxvQkFBb0IsQ0FBQyxNQUFjLEVBQUUsSUFBNkQ7UUFDaEcsSUFBSSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsV0FBVyxFQUFFLENBQUM7WUFDOUIsSUFBSSxDQUFDLE9BQU8sQ0FBQyxXQUFXLEdBQUcsRUFBRSxDQUFDO1FBQ2hDLENBQUM7UUFFRCxNQUFNLFNBQVMsR0FBRyxHQUFHLElBQUksU0FBUyxDQUFDO1FBQ25DLElBQUksQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLFdBQVcsQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDO1lBQ3pDLElBQUksQ0FBQyxPQUFPLENBQUMsV0FBVyxDQUFDLFNBQVMsQ0FBQyxHQUFHLEVBQUUsQ0FBQztRQUMzQyxDQUFDO1FBRUQsSUFBSSxDQUFDLE9BQU8sQ0FBQyxXQUFXLENBQUMsU0FBUyxDQUFDLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDO0lBQ25ELENBQUM7SUFFRDs7T0FFRztJQUNLLFdBQVc7UUFDakIsTUFBTSxTQUFTLEdBQUc7WUFDaEIsUUFBUSxFQUFFLENBQUMsRUFBSSxVQUFVO1lBQ3pCLElBQUksRUFBRSxFQUFFLEVBQU8sUUFBUTtZQUN2QixNQUFNLEVBQUUsRUFBRSxFQUFLLFNBQVM7WUFDeEIsR0FBRyxFQUFFLEdBQUcsRUFBTyxTQUFTO1NBQ3pCLENBQUM7UUFFRixPQUFPLFNBQVMsQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLElBQUksR0FBRyxDQUFDO0lBQ3pDLENBQUM7SUFFRDs7T0FFRztJQUNLLHlCQUF5QjtRQUMvQixNQUFNLGNBQWMsR0FBRyxFQUFFLEdBQUcsRUFBRSxDQUFDLEVBQUUsTUFBTSxFQUFFLENBQUMsRUFBRSxJQUFJLEVBQUUsQ0FBQyxFQUFFLFFBQVEsRUFBRSxFQUFFLEVBQUUsQ0FBQztRQUNwRSxPQUFPLGNBQWMsQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxDQUFDO0lBQzVDLENBQUM7SUFFRDs7T0FFRztJQUNILGVBQWU7UUFDYixPQUFPO1lBQ0wsRUFBRSxFQUFFLElBQUksQ0FBQyxFQUFFO1lBQ1gsS0FBSyxFQUFFLElBQUksQ0FBQyxLQUFLO1lBQ2pCLGFBQWEsRUFBRSxJQUFJLENBQUMsYUFBYTtZQUNqQyxRQUFRLEVBQUUsSUFBSSxDQUFDLFFBQVE7WUFDdkIsTUFBTSxFQUFFLElBQUksQ0FBQyxNQUFNO1lBQ25CLFVBQVUsRUFBRSxJQUFJLENBQUMsVUFBVTtZQUMzQixVQUFVLEVBQUUsSUFBSSxDQUFDLFVBQVU7WUFDM0IsTUFBTSxFQUFFLElBQUksQ0FBQyxNQUFNO1lBQ25CLFVBQVUsRUFBRSxJQUFJLENBQUMsVUFBVTtZQUMzQixTQUFTLEVBQUUsSUFBSSxDQUFDLFNBQVM7WUFDekIsVUFBVSxFQUFFLElBQUksQ0FBQyxVQUFVO1lBQzNCLHFCQUFxQixFQUFFLElBQUksQ0FBQyxxQkFBcUI7WUFDakQsU0FBUyxFQUFFLElBQUksQ0FBQyxTQUFTO1lBQ3pCLFVBQVUsRUFBRSxJQUFJLENBQUMsVUFBVTtZQUMzQixvQkFBb0IsRUFBRSxJQUFJLENBQUMsT0FBTyxDQUFDLGVBQWUsRUFBRSxNQUFNLElBQUksQ0FBQztZQUMvRCxrQkFBa0IsRUFBRSxJQUFJLENBQUMsT0FBTyxDQUFDLGFBQWEsRUFBRSxNQUFNLElBQUksQ0FBQztZQUMzRCxhQUFhLEVBQUUsSUFBSSxDQUFDLE9BQU8sQ0FBQyxRQUFRLEVBQUUsTUFBTSxJQUFJLENBQUM7WUFDakQsSUFBSSxFQUFFLElBQUksQ0FBQyxJQUFJO1lBQ2YsU0FBUyxFQUFFLElBQUksQ0FBQyxTQUFTO1lBQ3pCLFNBQVMsRUFBRSxJQUFJLENBQUMsU0FBUztTQUMxQixDQUFDO0lBQ0osQ0FBQztJQUVEOztPQUVHO0lBQ0gsa0JBQWtCO1FBQ2hCLE9BQU87WUFDTCxTQUFTLEVBQUUsSUFBSSxDQUFDLGVBQWUsRUFBRTtZQUNqQyxPQUFPLEVBQUUsSUFBSSxDQUFDLE9BQU87WUFDckIsTUFBTSxFQUFFLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDO2dCQUNwQixFQUFFLEVBQUUsSUFBSSxDQUFDLE1BQU0sQ0FBQyxFQUFFO2dCQUNsQixJQUFJLEVBQUUsSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJO2dCQUN0QixJQUFJLEVBQUUsSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJO2FBQ3ZCLENBQUMsQ0FBQyxDQUFDLElBQUk7WUFDUixVQUFVLEVBQUUsSUFBSSxDQUFDLFVBQVUsQ0FBQyxDQUFDLENBQUM7Z0JBQzVCLEVBQUUsRUFBRSxJQUFJLENBQUMsVUFBVSxDQUFDLEVBQUU7Z0JBQ3RCLElBQUksRUFBRSxJQUFJLENBQUMsVUFBVSxDQUFDLElBQUk7Z0JBQzFCLFdBQVcsRUFBRSxJQUFJLENBQUMsVUFBVSxDQUFDLFdBQVc7YUFDekMsQ0FBQyxDQUFDLENBQUMsSUFBSTtZQUNSLFVBQVUsRUFBRSxJQUFJLElBQUksRUFBRSxDQUFDLFdBQVcsRUFBRTtTQUNyQyxDQUFDO0lBQ0osQ0FBQztJQUVEOztPQUVHO0lBQ0gsMEJBQTBCO1FBQ3hCLE9BQU8sSUFBSSxDQUFDLFVBQVU7WUFDZixJQUFJLENBQUMsU0FBUztZQUNkLElBQUksQ0FBQyxPQUFPLENBQUMsVUFBVSxFQUFFLGlCQUFpQjtZQUMxQyxJQUFJLENBQUMsT0FBTyxDQUFDLGNBQWMsRUFBRSxlQUFlLEdBQUcsTUFBTSxDQUFDO0lBQy9ELENBQUM7SUFFRDs7T0FFRztJQUNILHFCQUFxQjtRQUNuQixPQUFPLElBQUksQ0FBQyxPQUFPLENBQUMsVUFBVSxFQUFFLFVBQVUsSUFBSSxFQUFFLENBQUM7SUFDbkQsQ0FBQztJQUVEOztPQUVHO0lBQ0gsbUJBQW1CO1FBQ2pCLE9BQU8sSUFBSSxDQUFDLE9BQU8sQ0FBQyxnQkFBZ0IsSUFBSSxFQUFFLENBQUM7SUFDN0MsQ0FBQztDQUNGLENBQUE7QUEvZVksMENBQWU7QUFFMUI7SUFEQyxJQUFBLGdDQUFzQixFQUFDLE1BQU0sQ0FBQzs7MkNBQ3BCO0FBTVg7SUFEQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxNQUFNLEVBQUUsR0FBRyxFQUFFLENBQUM7OzhDQUNWO0FBTWQ7SUFEQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUsTUFBTSxFQUFFLENBQUM7O29EQUNMO0FBMkJwQjtJQXRCQyxJQUFBLGdCQUFNLEVBQUM7UUFDTixJQUFJLEVBQUUsTUFBTTtRQUNaLElBQUksRUFBRTtZQUNKLGdCQUFnQjtZQUNoQixpQkFBaUI7WUFDakIsZ0JBQWdCO1lBQ2hCLGVBQWU7WUFDZixZQUFZO1lBQ1osZUFBZTtZQUNmLGlCQUFpQjtZQUNqQixtQkFBbUI7WUFDbkIsbUJBQW1CO1lBQ25CLDBCQUEwQjtZQUMxQixrQkFBa0I7WUFDbEIsbUJBQW1CO1lBQ25CLG9CQUFvQjtZQUNwQixxQkFBcUI7WUFDckIsaUJBQWlCO1lBQ2pCLHVCQUF1QjtZQUN2QixPQUFPO1NBQ1I7S0FDRixDQUFDOztzREFDb0I7QUFTdEI7SUFKQyxJQUFBLGdCQUFNLEVBQUM7UUFDTixJQUFJLEVBQUUsTUFBTTtRQUNaLElBQUksRUFBRSxDQUFDLEtBQUssRUFBRSxRQUFRLEVBQUUsTUFBTSxFQUFFLFVBQVUsQ0FBQztLQUM1QyxDQUFDOztpREFDK0M7QUFVakQ7SUFMQyxJQUFBLGdCQUFNLEVBQUM7UUFDTixJQUFJLEVBQUUsTUFBTTtRQUNaLElBQUksRUFBRSxDQUFDLE1BQU0sRUFBRSxlQUFlLEVBQUUscUJBQXFCLEVBQUUseUJBQXlCLEVBQUUsVUFBVSxFQUFFLGVBQWUsRUFBRSxnQkFBZ0IsQ0FBQztRQUNoSSxPQUFPLEVBQUUsTUFBTTtLQUNoQixDQUFDOzsrQ0FDYTtBQU1mO0lBREMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLGFBQWEsRUFBRSxJQUFJLEVBQUUsMEJBQTBCLEVBQUUsQ0FBQztrREFDdEQsSUFBSSxvQkFBSixJQUFJO21EQUFDO0FBTWpCO0lBREMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLGFBQWEsRUFBRSxJQUFJLEVBQUUsMEJBQTBCLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDO2tEQUNyRSxJQUFJLG9CQUFKLElBQUk7bURBQUM7QUFNbEI7SUFEQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUsT0FBTyxFQUFFLENBQUM7O2dEQTZHeEI7QUFNRjtJQURDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxNQUFNLEVBQUUsS0FBSyxFQUFFLElBQUksRUFBRSxPQUFPLEVBQUUsSUFBSSxFQUFFLENBQUM7OzZDQUN0QztBQU1mO0lBREMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLGFBQWEsRUFBRSxJQUFJLEVBQUUsTUFBTSxFQUFFLENBQUM7O21EQUMzQjtBQU1uQjtJQURDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxhQUFhLEVBQUUsSUFBSSxFQUFFLE1BQU0sRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7O21EQUMxQztBQU1wQjtJQURDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxZQUFZLEVBQUUsSUFBSSxFQUFFLE1BQU0sRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7O2tEQUMxQztBQUduQjtJQURDLElBQUEsMEJBQWdCLEVBQUMsRUFBRSxJQUFJLEVBQUUsWUFBWSxFQUFFLENBQUM7a0RBQzlCLElBQUksb0JBQUosSUFBSTtrREFBQztBQUdoQjtJQURDLElBQUEsMEJBQWdCLEVBQUMsRUFBRSxJQUFJLEVBQUUsWUFBWSxFQUFFLENBQUM7a0RBQzlCLElBQUksb0JBQUosSUFBSTtrREFBQztBQUtoQjtJQUZDLElBQUEsbUJBQVMsRUFBQyxHQUFHLEVBQUUsQ0FBQywyQ0FBZ0IsRUFBRSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQztJQUNyRCxJQUFBLG9CQUFVLEVBQUMsRUFBRSxJQUFJLEVBQUUsV0FBVyxFQUFFLENBQUM7a0RBQ3pCLDJDQUFnQixvQkFBaEIsMkNBQWdCOytDQUFDO0FBRzFCO0lBREMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLFdBQVcsRUFBRSxJQUFJLEVBQUUsTUFBTSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQzs7aURBQzFDO0FBSWxCO0lBRkMsSUFBQSxtQkFBUyxFQUFDLEdBQUcsRUFBRSxDQUFDLG1EQUFvQixFQUFFLFVBQVUsQ0FBQyxFQUFFLENBQUMsVUFBVSxDQUFDLFVBQVUsRUFBRSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQztJQUM5RixJQUFBLG9CQUFVLEVBQUMsRUFBRSxJQUFJLEVBQUUsZUFBZSxFQUFFLENBQUM7a0RBQ3pCLG1EQUFvQixvQkFBcEIsbURBQW9CO21EQUFDO0FBR2xDO0lBREMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLGVBQWUsRUFBRSxJQUFJLEVBQUUsTUFBTSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQzs7cURBQzFDOzBCQXZPWCxlQUFlO0lBUDNCLElBQUEsZ0JBQU0sRUFBQyxtQkFBbUIsQ0FBQztJQUMzQixJQUFBLGVBQUssRUFBQyxDQUFDLFVBQVUsQ0FBQyxDQUFDO0lBQ25CLElBQUEsZUFBSyxFQUFDLENBQUMsY0FBYyxDQUFDLENBQUM7SUFDdkIsSUFBQSxlQUFLLEVBQUMsQ0FBQyxVQUFVLENBQUMsQ0FBQztJQUNuQixJQUFBLGVBQUssRUFBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDO0lBQ2pCLElBQUEsZUFBSyxFQUFDLENBQUMsWUFBWSxDQUFDLENBQUM7SUFDckIsSUFBQSxlQUFLLEVBQUMsQ0FBQyxlQUFlLENBQUMsQ0FBQztHQUNaLGVBQWUsQ0ErZTNCIiwibmFtZXMiOltdLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTHVrYVxcc2VudGluZWxcXGJhY2tlbmRcXHNyY1xcbW9kdWxlc1xcY29tcGxpYW5jZS1hdWRpdFxcZG9tYWluXFxlbnRpdGllc1xccG9saWN5LXZpb2xhdGlvbi5lbnRpdHkudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcclxuICBFbnRpdHksXHJcbiAgUHJpbWFyeUdlbmVyYXRlZENvbHVtbixcclxuICBDb2x1bW4sXHJcbiAgQ3JlYXRlRGF0ZUNvbHVtbixcclxuICBVcGRhdGVEYXRlQ29sdW1uLFxyXG4gIEluZGV4LFxyXG4gIE1hbnlUb09uZSxcclxuICBKb2luQ29sdW1uLFxyXG59IGZyb20gJ3R5cGVvcm0nO1xyXG5pbXBvcnQgeyBDb21wbGlhbmNlQXNzZXNzbWVudCB9IGZyb20gJy4vY29tcGxpYW5jZS1hc3Nlc3NtZW50LmVudGl0eSc7XHJcbmltcG9ydCB7IFBvbGljeURlZmluaXRpb24gfSBmcm9tICcuL3BvbGljeS1kZWZpbml0aW9uLmVudGl0eSc7XHJcblxyXG4vKipcclxuICogUG9saWN5IFZpb2xhdGlvbiBlbnRpdHlcclxuICogUmVwcmVzZW50cyB2aW9sYXRpb25zIG9mIGNvbXBsaWFuY2UgcG9saWNpZXMgYW5kIGNvbnRyb2xzXHJcbiAqL1xyXG5ARW50aXR5KCdwb2xpY3lfdmlvbGF0aW9ucycpXHJcbkBJbmRleChbJ3BvbGljeUlkJ10pXHJcbkBJbmRleChbJ2Fzc2Vzc21lbnRJZCddKVxyXG5ASW5kZXgoWydzZXZlcml0eSddKVxyXG5ASW5kZXgoWydzdGF0dXMnXSlcclxuQEluZGV4KFsnZGV0ZWN0ZWRBdCddKVxyXG5ASW5kZXgoWyd2aW9sYXRpb25UeXBlJ10pXHJcbmV4cG9ydCBjbGFzcyBQb2xpY3lWaW9sYXRpb24ge1xyXG4gIEBQcmltYXJ5R2VuZXJhdGVkQ29sdW1uKCd1dWlkJylcclxuICBpZDogc3RyaW5nO1xyXG5cclxuICAvKipcclxuICAgKiBWaW9sYXRpb24gdGl0bGVcclxuICAgKi9cclxuICBAQ29sdW1uKHsgbGVuZ3RoOiAyNTUgfSlcclxuICB0aXRsZTogc3RyaW5nO1xyXG5cclxuICAvKipcclxuICAgKiBWaW9sYXRpb24gZGVzY3JpcHRpb25cclxuICAgKi9cclxuICBAQ29sdW1uKHsgdHlwZTogJ3RleHQnIH0pXHJcbiAgZGVzY3JpcHRpb246IHN0cmluZztcclxuXHJcbiAgLyoqXHJcbiAgICogVHlwZSBvZiB2aW9sYXRpb25cclxuICAgKi9cclxuICBAQ29sdW1uKHtcclxuICAgIHR5cGU6ICdlbnVtJyxcclxuICAgIGVudW06IFtcclxuICAgICAgJ2FjY2Vzc19jb250cm9sJyxcclxuICAgICAgJ2RhdGFfcHJvdGVjdGlvbicsXHJcbiAgICAgICdhdXRoZW50aWNhdGlvbicsXHJcbiAgICAgICdhdXRob3JpemF0aW9uJyxcclxuICAgICAgJ2VuY3J5cHRpb24nLFxyXG4gICAgICAnYXVkaXRfbG9nZ2luZycsXHJcbiAgICAgICdiYWNrdXBfcmVjb3ZlcnknLFxyXG4gICAgICAnaW5jaWRlbnRfcmVzcG9uc2UnLFxyXG4gICAgICAnY2hhbmdlX21hbmFnZW1lbnQnLFxyXG4gICAgICAndnVsbmVyYWJpbGl0eV9tYW5hZ2VtZW50JyxcclxuICAgICAgJ25ldHdvcmtfc2VjdXJpdHknLFxyXG4gICAgICAncGh5c2ljYWxfc2VjdXJpdHknLFxyXG4gICAgICAncGVyc29ubmVsX3NlY3VyaXR5JyxcclxuICAgICAgJ2J1c2luZXNzX2NvbnRpbnVpdHknLFxyXG4gICAgICAncmlza19tYW5hZ2VtZW50JyxcclxuICAgICAgJ2NvbXBsaWFuY2VfbW9uaXRvcmluZycsXHJcbiAgICAgICdvdGhlcicsXHJcbiAgICBdLFxyXG4gIH0pXHJcbiAgdmlvbGF0aW9uVHlwZTogc3RyaW5nO1xyXG5cclxuICAvKipcclxuICAgKiBWaW9sYXRpb24gc2V2ZXJpdHlcclxuICAgKi9cclxuICBAQ29sdW1uKHtcclxuICAgIHR5cGU6ICdlbnVtJyxcclxuICAgIGVudW06IFsnbG93JywgJ21lZGl1bScsICdoaWdoJywgJ2NyaXRpY2FsJ10sXHJcbiAgfSlcclxuICBzZXZlcml0eTogJ2xvdycgfCAnbWVkaXVtJyB8ICdoaWdoJyB8ICdjcml0aWNhbCc7XHJcblxyXG4gIC8qKlxyXG4gICAqIFZpb2xhdGlvbiBzdGF0dXNcclxuICAgKi9cclxuICBAQ29sdW1uKHtcclxuICAgIHR5cGU6ICdlbnVtJyxcclxuICAgIGVudW06IFsnb3BlbicsICdpbnZlc3RpZ2F0aW5nJywgJ3JlbWVkaWF0aW9uX3BsYW5uZWQnLCAncmVtZWRpYXRpb25faW5fcHJvZ3Jlc3MnLCAncmVzb2x2ZWQnLCAnYWNjZXB0ZWRfcmlzaycsICdmYWxzZV9wb3NpdGl2ZSddLFxyXG4gICAgZGVmYXVsdDogJ29wZW4nLFxyXG4gIH0pXHJcbiAgc3RhdHVzOiBzdHJpbmc7XHJcblxyXG4gIC8qKlxyXG4gICAqIFdoZW4gdGhlIHZpb2xhdGlvbiB3YXMgZGV0ZWN0ZWRcclxuICAgKi9cclxuICBAQ29sdW1uKHsgbmFtZTogJ2RldGVjdGVkX2F0JywgdHlwZTogJ3RpbWVzdGFtcCB3aXRoIHRpbWUgem9uZScgfSlcclxuICBkZXRlY3RlZEF0OiBEYXRlO1xyXG5cclxuICAvKipcclxuICAgKiBXaGVuIHRoZSB2aW9sYXRpb24gd2FzIHJlc29sdmVkXHJcbiAgICovXHJcbiAgQENvbHVtbih7IG5hbWU6ICdyZXNvbHZlZF9hdCcsIHR5cGU6ICd0aW1lc3RhbXAgd2l0aCB0aW1lIHpvbmUnLCBudWxsYWJsZTogdHJ1ZSB9KVxyXG4gIHJlc29sdmVkQXQ/OiBEYXRlO1xyXG5cclxuICAvKipcclxuICAgKiBWaW9sYXRpb24gZGV0YWlscyBhbmQgY29udGV4dFxyXG4gICAqL1xyXG4gIEBDb2x1bW4oeyB0eXBlOiAnanNvbmInIH0pXHJcbiAgZGV0YWlsczoge1xyXG4gICAgLy8gVmlvbGF0aW9uIHNwZWNpZmljc1xyXG4gICAgdmlvbGF0ZWRDb250cm9scz86IHN0cmluZ1tdO1xyXG4gICAgdmlvbGF0ZWRQb2xpY2llcz86IHN0cmluZ1tdO1xyXG4gICAgdmlvbGF0ZWRSZXF1aXJlbWVudHM/OiBzdHJpbmdbXTtcclxuICAgIFxyXG4gICAgLy8gRXZpZGVuY2UgYW5kIGRldGVjdGlvblxyXG4gICAgZXZpZGVuY2U/OiBBcnJheTx7XHJcbiAgICAgIHR5cGU6ICdsb2cnIHwgJ3NjcmVlbnNob3QnIHwgJ2RvY3VtZW50JyB8ICdzeXN0ZW1fb3V0cHV0JyB8ICd3aXRuZXNzX3N0YXRlbWVudCc7XHJcbiAgICAgIGRlc2NyaXB0aW9uOiBzdHJpbmc7XHJcbiAgICAgIHNvdXJjZTogc3RyaW5nO1xyXG4gICAgICB0aW1lc3RhbXA6IHN0cmluZztcclxuICAgICAgbG9jYXRpb24/OiBzdHJpbmc7XHJcbiAgICAgIGhhc2g/OiBzdHJpbmc7XHJcbiAgICB9PjtcclxuICAgIFxyXG4gICAgZGV0ZWN0aW9uTWV0aG9kPzogJ2F1dG9tYXRlZF9zY2FuJyB8ICdtYW51YWxfcmV2aWV3JyB8ICdhdWRpdCcgfCAnaW5jaWRlbnRfaW52ZXN0aWdhdGlvbicgfCAndXNlcl9yZXBvcnQnO1xyXG4gICAgZGV0ZWN0aW9uU291cmNlPzogc3RyaW5nO1xyXG4gICAgXHJcbiAgICAvLyBBZmZlY3RlZCBlbnRpdGllc1xyXG4gICAgYWZmZWN0ZWRTeXN0ZW1zPzogQXJyYXk8e1xyXG4gICAgICBzeXN0ZW1JZD86IHN0cmluZztcclxuICAgICAgc3lzdGVtTmFtZTogc3RyaW5nO1xyXG4gICAgICBzeXN0ZW1UeXBlOiBzdHJpbmc7XHJcbiAgICAgIGltcGFjdDogJ25vbmUnIHwgJ2xvdycgfCAnbWVkaXVtJyB8ICdoaWdoJztcclxuICAgIH0+O1xyXG4gICAgXHJcbiAgICBhZmZlY3RlZFVzZXJzPzogQXJyYXk8e1xyXG4gICAgICB1c2VySWQ/OiBzdHJpbmc7XHJcbiAgICAgIHVzZXJuYW1lPzogc3RyaW5nO1xyXG4gICAgICByb2xlPzogc3RyaW5nO1xyXG4gICAgICBpbXBhY3Q6IHN0cmluZztcclxuICAgIH0+O1xyXG4gICAgXHJcbiAgICBhZmZlY3RlZERhdGE/OiBBcnJheTx7XHJcbiAgICAgIGRhdGFUeXBlOiBzdHJpbmc7XHJcbiAgICAgIGNsYXNzaWZpY2F0aW9uOiAncHVibGljJyB8ICdpbnRlcm5hbCcgfCAnY29uZmlkZW50aWFsJyB8ICdyZXN0cmljdGVkJztcclxuICAgICAgdm9sdW1lPzogc3RyaW5nO1xyXG4gICAgICBsb2NhdGlvbj86IHN0cmluZztcclxuICAgIH0+O1xyXG4gICAgXHJcbiAgICAvLyBCdXNpbmVzcyBpbXBhY3RcclxuICAgIGJ1c2luZXNzSW1wYWN0Pzoge1xyXG4gICAgICBkZXNjcmlwdGlvbjogc3RyaW5nO1xyXG4gICAgICBmaW5hbmNpYWxJbXBhY3Q/OiBudW1iZXI7XHJcbiAgICAgIG9wZXJhdGlvbmFsSW1wYWN0Pzogc3RyaW5nO1xyXG4gICAgICByZXB1dGF0aW9uYWxJbXBhY3Q/OiBzdHJpbmc7XHJcbiAgICAgIGxlZ2FsSW1wYWN0Pzogc3RyaW5nO1xyXG4gICAgICBjdXN0b21lckltcGFjdD86IHN0cmluZztcclxuICAgIH07XHJcbiAgICBcclxuICAgIC8vIFJpc2sgYXNzZXNzbWVudFxyXG4gICAgcmlza0Fzc2Vzc21lbnQ/OiB7XHJcbiAgICAgIGxpa2VsaWhvb2Q6ICd2ZXJ5X2xvdycgfCAnbG93JyB8ICdtZWRpdW0nIHwgJ2hpZ2gnIHwgJ3ZlcnlfaGlnaCc7XHJcbiAgICAgIGltcGFjdDogJ3ZlcnlfbG93JyB8ICdsb3cnIHwgJ21lZGl1bScgfCAnaGlnaCcgfCAndmVyeV9oaWdoJztcclxuICAgICAgcmlza1Njb3JlOiBudW1iZXI7XHJcbiAgICAgIHJpc2tMZXZlbDogJ2xvdycgfCAnbWVkaXVtJyB8ICdoaWdoJyB8ICdjcml0aWNhbCc7XHJcbiAgICAgIG1pdGlnYXRpbmdGYWN0b3JzPzogc3RyaW5nW107XHJcbiAgICAgIGFnZ3JhdmF0aW5nRmFjdG9ycz86IHN0cmluZ1tdO1xyXG4gICAgfTtcclxuICAgIFxyXG4gICAgLy8gUm9vdCBjYXVzZSBhbmFseXNpc1xyXG4gICAgcm9vdENhdXNlPzoge1xyXG4gICAgICBwcmltYXJ5Q2F1c2U6IHN0cmluZztcclxuICAgICAgY29udHJpYnV0aW5nRmFjdG9yczogc3RyaW5nW107XHJcbiAgICAgIHN5c3RlbWljSXNzdWVzPzogc3RyaW5nW107XHJcbiAgICAgIGh1bWFuRmFjdG9ycz86IHN0cmluZ1tdO1xyXG4gICAgICBwcm9jZXNzRmFpbHVyZXM/OiBzdHJpbmdbXTtcclxuICAgICAgdGVjaG5vbG9neUZhaWx1cmVzPzogc3RyaW5nW107XHJcbiAgICB9O1xyXG4gICAgXHJcbiAgICAvLyBSZW1lZGlhdGlvbiBpbmZvcm1hdGlvblxyXG4gICAgcmVtZWRpYXRpb24/OiB7XHJcbiAgICAgIGltbWVkaWF0ZUFjdGlvbnM/OiBzdHJpbmdbXTtcclxuICAgICAgc2hvcnRUZXJtQWN0aW9ucz86IHN0cmluZ1tdO1xyXG4gICAgICBsb25nVGVybUFjdGlvbnM/OiBzdHJpbmdbXTtcclxuICAgICAgcHJldmVudGl2ZUFjdGlvbnM/OiBzdHJpbmdbXTtcclxuICAgICAgZXN0aW1hdGVkQ29zdD86IG51bWJlcjtcclxuICAgICAgZXN0aW1hdGVkRWZmb3J0Pzogc3RyaW5nO1xyXG4gICAgICB0YXJnZXREYXRlPzogc3RyaW5nO1xyXG4gICAgICBhc3NpZ25lZFRvPzogc3RyaW5nO1xyXG4gICAgICBkZXBlbmRlbmNpZXM/OiBzdHJpbmdbXTtcclxuICAgIH07XHJcbiAgICBcclxuICAgIC8vIENvbXBsaWFuY2UgY29udGV4dFxyXG4gICAgY29tcGxpYW5jZT86IHtcclxuICAgICAgZnJhbWV3b3Jrczogc3RyaW5nW107XHJcbiAgICAgIGNvbnRyb2xzOiBzdHJpbmdbXTtcclxuICAgICAgcmVxdWlyZW1lbnRzOiBzdHJpbmdbXTtcclxuICAgICAgcmVndWxhdG9yeUltcGxpY2F0aW9ucz86IHN0cmluZ1tdO1xyXG4gICAgICByZXBvcnRpbmdSZXF1aXJlZD86IGJvb2xlYW47XHJcbiAgICAgIG5vdGlmaWNhdGlvblJlcXVpcmVkPzogYm9vbGVhbjtcclxuICAgICAgZGVhZGxpbmVzPzogQXJyYXk8e1xyXG4gICAgICAgIHR5cGU6IHN0cmluZztcclxuICAgICAgICBkYXRlOiBzdHJpbmc7XHJcbiAgICAgICAgZGVzY3JpcHRpb246IHN0cmluZztcclxuICAgICAgfT47XHJcbiAgICB9O1xyXG4gICAgXHJcbiAgICAvLyBBZGRpdGlvbmFsIG1ldGFkYXRhXHJcbiAgICBtZXRhZGF0YT86IHtcclxuICAgICAgY29ycmVsYXRpb25JZD86IHN0cmluZztcclxuICAgICAgcmVsYXRlZFZpb2xhdGlvbnM/OiBzdHJpbmdbXTtcclxuICAgICAgcmVsYXRlZEluY2lkZW50cz86IHN0cmluZ1tdO1xyXG4gICAgICBleHRlcm5hbFJlZmVyZW5jZXM/OiBzdHJpbmdbXTtcclxuICAgICAgdGFncz86IHN0cmluZ1tdO1xyXG4gICAgICBjdXN0b21GaWVsZHM/OiBSZWNvcmQ8c3RyaW5nLCBhbnk+O1xyXG4gICAgfTtcclxuICB9O1xyXG5cclxuICAvKipcclxuICAgKiBWaW9sYXRpb24gdGFnc1xyXG4gICAqL1xyXG4gIEBDb2x1bW4oeyB0eXBlOiAndGV4dCcsIGFycmF5OiB0cnVlLCBkZWZhdWx0OiAne30nIH0pXHJcbiAgdGFnczogc3RyaW5nW107XHJcblxyXG4gIC8qKlxyXG4gICAqIFVzZXIgd2hvIGRldGVjdGVkL3JlcG9ydGVkIHRoZSB2aW9sYXRpb25cclxuICAgKi9cclxuICBAQ29sdW1uKHsgbmFtZTogJ2RldGVjdGVkX2J5JywgdHlwZTogJ3V1aWQnIH0pXHJcbiAgZGV0ZWN0ZWRCeTogc3RyaW5nO1xyXG5cclxuICAvKipcclxuICAgKiBVc2VyIGFzc2lnbmVkIHRvIGhhbmRsZSB0aGUgdmlvbGF0aW9uXHJcbiAgICovXHJcbiAgQENvbHVtbih7IG5hbWU6ICdhc3NpZ25lZF90bycsIHR5cGU6ICd1dWlkJywgbnVsbGFibGU6IHRydWUgfSlcclxuICBhc3NpZ25lZFRvPzogc3RyaW5nO1xyXG5cclxuICAvKipcclxuICAgKiBVc2VyIHdobyBsYXN0IHVwZGF0ZWQgdGhlIHZpb2xhdGlvblxyXG4gICAqL1xyXG4gIEBDb2x1bW4oeyBuYW1lOiAndXBkYXRlZF9ieScsIHR5cGU6ICd1dWlkJywgbnVsbGFibGU6IHRydWUgfSlcclxuICB1cGRhdGVkQnk/OiBzdHJpbmc7XHJcblxyXG4gIEBDcmVhdGVEYXRlQ29sdW1uKHsgbmFtZTogJ2NyZWF0ZWRfYXQnIH0pXHJcbiAgY3JlYXRlZEF0OiBEYXRlO1xyXG5cclxuICBAVXBkYXRlRGF0ZUNvbHVtbih7IG5hbWU6ICd1cGRhdGVkX2F0JyB9KVxyXG4gIHVwZGF0ZWRBdDogRGF0ZTtcclxuXHJcbiAgLy8gUmVsYXRpb25zaGlwc1xyXG4gIEBNYW55VG9PbmUoKCkgPT4gUG9saWN5RGVmaW5pdGlvbiwgeyBudWxsYWJsZTogdHJ1ZSB9KVxyXG4gIEBKb2luQ29sdW1uKHsgbmFtZTogJ3BvbGljeV9pZCcgfSlcclxuICBwb2xpY3k/OiBQb2xpY3lEZWZpbml0aW9uO1xyXG5cclxuICBAQ29sdW1uKHsgbmFtZTogJ3BvbGljeV9pZCcsIHR5cGU6ICd1dWlkJywgbnVsbGFibGU6IHRydWUgfSlcclxuICBwb2xpY3lJZD86IHN0cmluZztcclxuXHJcbiAgQE1hbnlUb09uZSgoKSA9PiBDb21wbGlhbmNlQXNzZXNzbWVudCwgYXNzZXNzbWVudCA9PiBhc3Nlc3NtZW50LnZpb2xhdGlvbnMsIHsgbnVsbGFibGU6IHRydWUgfSlcclxuICBASm9pbkNvbHVtbih7IG5hbWU6ICdhc3Nlc3NtZW50X2lkJyB9KVxyXG4gIGFzc2Vzc21lbnQ/OiBDb21wbGlhbmNlQXNzZXNzbWVudDtcclxuXHJcbiAgQENvbHVtbih7IG5hbWU6ICdhc3Nlc3NtZW50X2lkJywgdHlwZTogJ3V1aWQnLCBudWxsYWJsZTogdHJ1ZSB9KVxyXG4gIGFzc2Vzc21lbnRJZD86IHN0cmluZztcclxuXHJcbiAgLyoqXHJcbiAgICogQ2hlY2sgaWYgdmlvbGF0aW9uIGlzIG9wZW5cclxuICAgKi9cclxuICBnZXQgaXNPcGVuKCk6IGJvb2xlYW4ge1xyXG4gICAgcmV0dXJuICFbJ3Jlc29sdmVkJywgJ2FjY2VwdGVkX3Jpc2snLCAnZmFsc2VfcG9zaXRpdmUnXS5pbmNsdWRlcyh0aGlzLnN0YXR1cyk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDaGVjayBpZiB2aW9sYXRpb24gaXMgY3JpdGljYWxcclxuICAgKi9cclxuICBnZXQgaXNDcml0aWNhbCgpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLnNldmVyaXR5ID09PSAnY3JpdGljYWwnO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2hlY2sgaWYgdmlvbGF0aW9uIGlzIG92ZXJkdWVcclxuICAgKi9cclxuICBnZXQgaXNPdmVyZHVlKCk6IGJvb2xlYW4ge1xyXG4gICAgaWYgKCF0aGlzLmlzT3BlbikgcmV0dXJuIGZhbHNlO1xyXG4gICAgXHJcbiAgICAvLyBDYWxjdWxhdGUgU0xBIGJhc2VkIG9uIHNldmVyaXR5XHJcbiAgICBjb25zdCBzbGFIb3VycyA9IHRoaXMuZ2V0U2xhSG91cnMoKTtcclxuICAgIGNvbnN0IGRlYWRsaW5lID0gbmV3IERhdGUodGhpcy5kZXRlY3RlZEF0LmdldFRpbWUoKSArIHNsYUhvdXJzICogNjAgKiA2MCAqIDEwMDApO1xyXG4gICAgXHJcbiAgICByZXR1cm4gbmV3IERhdGUoKSA+IGRlYWRsaW5lO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IHZpb2xhdGlvbiBhZ2UgaW4gaG91cnNcclxuICAgKi9cclxuICBnZXQgYWdlSW5Ib3VycygpOiBudW1iZXIge1xyXG4gICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTtcclxuICAgIGNvbnN0IGRpZmZNcyA9IG5vdy5nZXRUaW1lKCkgLSB0aGlzLmRldGVjdGVkQXQuZ2V0VGltZSgpO1xyXG4gICAgcmV0dXJuIE1hdGgucm91bmQoZGlmZk1zIC8gKDEwMDAgKiA2MCAqIDYwKSk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgdGltZSB0byByZXNvbHV0aW9uIGluIGhvdXJzXHJcbiAgICovXHJcbiAgZ2V0IHRpbWVUb1Jlc29sdXRpb25Ib3VycygpOiBudW1iZXIgfCBudWxsIHtcclxuICAgIGlmICghdGhpcy5yZXNvbHZlZEF0KSByZXR1cm4gbnVsbDtcclxuICAgIGNvbnN0IGRpZmZNcyA9IHRoaXMucmVzb2x2ZWRBdC5nZXRUaW1lKCkgLSB0aGlzLmRldGVjdGVkQXQuZ2V0VGltZSgpO1xyXG4gICAgcmV0dXJuIE1hdGgucm91bmQoZGlmZk1zIC8gKDEwMDAgKiA2MCAqIDYwKSk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgcmlzayBzY29yZVxyXG4gICAqL1xyXG4gIGdldCByaXNrU2NvcmUoKTogbnVtYmVyIHtcclxuICAgIHJldHVybiB0aGlzLmRldGFpbHMucmlza0Fzc2Vzc21lbnQ/LnJpc2tTY29yZSB8fCB0aGlzLmNhbGN1bGF0ZURlZmF1bHRSaXNrU2NvcmUoKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEFzc2lnbiB2aW9sYXRpb24gdG8gdXNlclxyXG4gICAqL1xyXG4gIGFzc2lnbih1c2VySWQ6IHN0cmluZywgYXNzaWduZWRCeTogc3RyaW5nKTogdm9pZCB7XHJcbiAgICB0aGlzLmFzc2lnbmVkVG8gPSB1c2VySWQ7XHJcbiAgICB0aGlzLnVwZGF0ZWRCeSA9IGFzc2lnbmVkQnk7XHJcbiAgICBcclxuICAgIGlmICh0aGlzLnN0YXR1cyA9PT0gJ29wZW4nKSB7XHJcbiAgICAgIHRoaXMuc3RhdHVzID0gJ2ludmVzdGlnYXRpbmcnO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogU3RhcnQgcmVtZWRpYXRpb25cclxuICAgKi9cclxuICBzdGFydFJlbWVkaWF0aW9uKHVzZXJJZDogc3RyaW5nKTogdm9pZCB7XHJcbiAgICB0aGlzLnN0YXR1cyA9ICdyZW1lZGlhdGlvbl9pbl9wcm9ncmVzcyc7XHJcbiAgICB0aGlzLnVwZGF0ZWRCeSA9IHVzZXJJZDtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIFJlc29sdmUgdmlvbGF0aW9uXHJcbiAgICovXHJcbiAgcmVzb2x2ZSh1c2VySWQ6IHN0cmluZywgcmVzb2x1dGlvbjogc3RyaW5nKTogdm9pZCB7XHJcbiAgICB0aGlzLnN0YXR1cyA9ICdyZXNvbHZlZCc7XHJcbiAgICB0aGlzLnJlc29sdmVkQXQgPSBuZXcgRGF0ZSgpO1xyXG4gICAgdGhpcy51cGRhdGVkQnkgPSB1c2VySWQ7XHJcbiAgICBcclxuICAgIGlmICghdGhpcy5kZXRhaWxzLnJlbWVkaWF0aW9uKSB7XHJcbiAgICAgIHRoaXMuZGV0YWlscy5yZW1lZGlhdGlvbiA9IHt9O1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICB0aGlzLmRldGFpbHMucmVtZWRpYXRpb24ucmVzb2x1dGlvbiA9IHJlc29sdXRpb247XHJcbiAgICB0aGlzLmRldGFpbHMucmVtZWRpYXRpb24ucmVzb2x2ZWRCeSA9IHVzZXJJZDtcclxuICAgIHRoaXMuZGV0YWlscy5yZW1lZGlhdGlvbi5yZXNvbHZlZEF0ID0gdGhpcy5yZXNvbHZlZEF0LnRvSVNPU3RyaW5nKCk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBBY2NlcHQgcmlza1xyXG4gICAqL1xyXG4gIGFjY2VwdFJpc2sodXNlcklkOiBzdHJpbmcsIGp1c3RpZmljYXRpb246IHN0cmluZyk6IHZvaWQge1xyXG4gICAgdGhpcy5zdGF0dXMgPSAnYWNjZXB0ZWRfcmlzayc7XHJcbiAgICB0aGlzLnJlc29sdmVkQXQgPSBuZXcgRGF0ZSgpO1xyXG4gICAgdGhpcy51cGRhdGVkQnkgPSB1c2VySWQ7XHJcbiAgICBcclxuICAgIGlmICghdGhpcy5kZXRhaWxzLnJpc2tBc3Nlc3NtZW50KSB7XHJcbiAgICAgIHRoaXMuZGV0YWlscy5yaXNrQXNzZXNzbWVudCA9IHtcclxuICAgICAgICBsaWtlbGlob29kOiAnbWVkaXVtJyxcclxuICAgICAgICBpbXBhY3Q6ICdtZWRpdW0nLFxyXG4gICAgICAgIHJpc2tTY29yZTogNSxcclxuICAgICAgICByaXNrTGV2ZWw6ICdtZWRpdW0nLFxyXG4gICAgICB9O1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICB0aGlzLmRldGFpbHMucmlza0Fzc2Vzc21lbnQuYWNjZXB0YW5jZUp1c3RpZmljYXRpb24gPSBqdXN0aWZpY2F0aW9uO1xyXG4gICAgdGhpcy5kZXRhaWxzLnJpc2tBc3Nlc3NtZW50LmFjY2VwdGVkQnkgPSB1c2VySWQ7XHJcbiAgICB0aGlzLmRldGFpbHMucmlza0Fzc2Vzc21lbnQuYWNjZXB0ZWRBdCA9IHRoaXMucmVzb2x2ZWRBdC50b0lTT1N0cmluZygpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogTWFyayBhcyBmYWxzZSBwb3NpdGl2ZVxyXG4gICAqL1xyXG4gIG1hcmtBc0ZhbHNlUG9zaXRpdmUodXNlcklkOiBzdHJpbmcsIHJlYXNvbjogc3RyaW5nKTogdm9pZCB7XHJcbiAgICB0aGlzLnN0YXR1cyA9ICdmYWxzZV9wb3NpdGl2ZSc7XHJcbiAgICB0aGlzLnJlc29sdmVkQXQgPSBuZXcgRGF0ZSgpO1xyXG4gICAgdGhpcy51cGRhdGVkQnkgPSB1c2VySWQ7XHJcbiAgICBcclxuICAgIGlmICghdGhpcy5kZXRhaWxzLm1ldGFkYXRhKSB7XHJcbiAgICAgIHRoaXMuZGV0YWlscy5tZXRhZGF0YSA9IHt9O1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICB0aGlzLmRldGFpbHMubWV0YWRhdGEuZmFsc2VQb3NpdGl2ZVJlYXNvbiA9IHJlYXNvbjtcclxuICAgIHRoaXMuZGV0YWlscy5tZXRhZGF0YS5tYXJrZWRCeSA9IHVzZXJJZDtcclxuICAgIHRoaXMuZGV0YWlscy5tZXRhZGF0YS5tYXJrZWRBdCA9IHRoaXMucmVzb2x2ZWRBdC50b0lTT1N0cmluZygpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQWRkIGV2aWRlbmNlXHJcbiAgICovXHJcbiAgYWRkRXZpZGVuY2UoZXZpZGVuY2U6IGFueSk6IHZvaWQge1xyXG4gICAgaWYgKCF0aGlzLmRldGFpbHMuZXZpZGVuY2UpIHtcclxuICAgICAgdGhpcy5kZXRhaWxzLmV2aWRlbmNlID0gW107XHJcbiAgICB9XHJcbiAgICBcclxuICAgIHRoaXMuZGV0YWlscy5ldmlkZW5jZS5wdXNoKHtcclxuICAgICAgLi4uZXZpZGVuY2UsXHJcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxyXG4gICAgfSk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBVcGRhdGUgcmlzayBhc3Nlc3NtZW50XHJcbiAgICovXHJcbiAgdXBkYXRlUmlza0Fzc2Vzc21lbnQocmlza0Fzc2Vzc21lbnQ6IGFueSk6IHZvaWQge1xyXG4gICAgdGhpcy5kZXRhaWxzLnJpc2tBc3Nlc3NtZW50ID0ge1xyXG4gICAgICAuLi50aGlzLmRldGFpbHMucmlza0Fzc2Vzc21lbnQsXHJcbiAgICAgIC4uLnJpc2tBc3Nlc3NtZW50LFxyXG4gICAgfTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEFkZCByZW1lZGlhdGlvbiBhY3Rpb25cclxuICAgKi9cclxuICBhZGRSZW1lZGlhdGlvbkFjdGlvbihhY3Rpb246IHN0cmluZywgdHlwZTogJ2ltbWVkaWF0ZScgfCAnc2hvcnRfdGVybScgfCAnbG9uZ190ZXJtJyB8ICdwcmV2ZW50aXZlJyk6IHZvaWQge1xyXG4gICAgaWYgKCF0aGlzLmRldGFpbHMucmVtZWRpYXRpb24pIHtcclxuICAgICAgdGhpcy5kZXRhaWxzLnJlbWVkaWF0aW9uID0ge307XHJcbiAgICB9XHJcbiAgICBcclxuICAgIGNvbnN0IGFjdGlvbktleSA9IGAke3R5cGV9QWN0aW9uc2A7XHJcbiAgICBpZiAoIXRoaXMuZGV0YWlscy5yZW1lZGlhdGlvblthY3Rpb25LZXldKSB7XHJcbiAgICAgIHRoaXMuZGV0YWlscy5yZW1lZGlhdGlvblthY3Rpb25LZXldID0gW107XHJcbiAgICB9XHJcbiAgICBcclxuICAgIHRoaXMuZGV0YWlscy5yZW1lZGlhdGlvblthY3Rpb25LZXldLnB1c2goYWN0aW9uKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCBTTEEgaG91cnMgYmFzZWQgb24gc2V2ZXJpdHlcclxuICAgKi9cclxuICBwcml2YXRlIGdldFNsYUhvdXJzKCk6IG51bWJlciB7XHJcbiAgICBjb25zdCBzbGFNYXRyaXggPSB7XHJcbiAgICAgIGNyaXRpY2FsOiA0LCAgIC8vIDQgaG91cnNcclxuICAgICAgaGlnaDogMjQsICAgICAgLy8gMSBkYXlcclxuICAgICAgbWVkaXVtOiA3MiwgICAgLy8gMyBkYXlzXHJcbiAgICAgIGxvdzogMTY4LCAgICAgIC8vIDEgd2Vla1xyXG4gICAgfTtcclxuICAgIFxyXG4gICAgcmV0dXJuIHNsYU1hdHJpeFt0aGlzLnNldmVyaXR5XSB8fCAxNjg7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDYWxjdWxhdGUgZGVmYXVsdCByaXNrIHNjb3JlXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBjYWxjdWxhdGVEZWZhdWx0Umlza1Njb3JlKCk6IG51bWJlciB7XHJcbiAgICBjb25zdCBzZXZlcml0eVNjb3JlcyA9IHsgbG93OiAyLCBtZWRpdW06IDUsIGhpZ2g6IDgsIGNyaXRpY2FsOiAxMCB9O1xyXG4gICAgcmV0dXJuIHNldmVyaXR5U2NvcmVzW3RoaXMuc2V2ZXJpdHldIHx8IDU7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZW5lcmF0ZSB2aW9sYXRpb24gc3VtbWFyeVxyXG4gICAqL1xyXG4gIGdlbmVyYXRlU3VtbWFyeSgpOiBhbnkge1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAgaWQ6IHRoaXMuaWQsXHJcbiAgICAgIHRpdGxlOiB0aGlzLnRpdGxlLFxyXG4gICAgICB2aW9sYXRpb25UeXBlOiB0aGlzLnZpb2xhdGlvblR5cGUsXHJcbiAgICAgIHNldmVyaXR5OiB0aGlzLnNldmVyaXR5LFxyXG4gICAgICBzdGF0dXM6IHRoaXMuc3RhdHVzLFxyXG4gICAgICBkZXRlY3RlZEF0OiB0aGlzLmRldGVjdGVkQXQsXHJcbiAgICAgIHJlc29sdmVkQXQ6IHRoaXMucmVzb2x2ZWRBdCxcclxuICAgICAgaXNPcGVuOiB0aGlzLmlzT3BlbixcclxuICAgICAgaXNDcml0aWNhbDogdGhpcy5pc0NyaXRpY2FsLFxyXG4gICAgICBpc092ZXJkdWU6IHRoaXMuaXNPdmVyZHVlLFxyXG4gICAgICBhZ2VJbkhvdXJzOiB0aGlzLmFnZUluSG91cnMsXHJcbiAgICAgIHRpbWVUb1Jlc29sdXRpb25Ib3VyczogdGhpcy50aW1lVG9SZXNvbHV0aW9uSG91cnMsXHJcbiAgICAgIHJpc2tTY29yZTogdGhpcy5yaXNrU2NvcmUsXHJcbiAgICAgIGFzc2lnbmVkVG86IHRoaXMuYXNzaWduZWRUbyxcclxuICAgICAgYWZmZWN0ZWRTeXN0ZW1zQ291bnQ6IHRoaXMuZGV0YWlscy5hZmZlY3RlZFN5c3RlbXM/Lmxlbmd0aCB8fCAwLFxyXG4gICAgICBhZmZlY3RlZFVzZXJzQ291bnQ6IHRoaXMuZGV0YWlscy5hZmZlY3RlZFVzZXJzPy5sZW5ndGggfHwgMCxcclxuICAgICAgZXZpZGVuY2VDb3VudDogdGhpcy5kZXRhaWxzLmV2aWRlbmNlPy5sZW5ndGggfHwgMCxcclxuICAgICAgdGFnczogdGhpcy50YWdzLFxyXG4gICAgICBjcmVhdGVkQXQ6IHRoaXMuY3JlYXRlZEF0LFxyXG4gICAgICB1cGRhdGVkQXQ6IHRoaXMudXBkYXRlZEF0LFxyXG4gICAgfTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEV4cG9ydCB2aW9sYXRpb24gZm9yIHJlcG9ydGluZ1xyXG4gICAqL1xyXG4gIGV4cG9ydEZvclJlcG9ydGluZygpOiBhbnkge1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAgdmlvbGF0aW9uOiB0aGlzLmdlbmVyYXRlU3VtbWFyeSgpLFxyXG4gICAgICBkZXRhaWxzOiB0aGlzLmRldGFpbHMsXHJcbiAgICAgIHBvbGljeTogdGhpcy5wb2xpY3kgPyB7XHJcbiAgICAgICAgaWQ6IHRoaXMucG9saWN5LmlkLFxyXG4gICAgICAgIG5hbWU6IHRoaXMucG9saWN5Lm5hbWUsXHJcbiAgICAgICAgdHlwZTogdGhpcy5wb2xpY3kudHlwZSxcclxuICAgICAgfSA6IG51bGwsXHJcbiAgICAgIGFzc2Vzc21lbnQ6IHRoaXMuYXNzZXNzbWVudCA/IHtcclxuICAgICAgICBpZDogdGhpcy5hc3Nlc3NtZW50LmlkLFxyXG4gICAgICAgIG5hbWU6IHRoaXMuYXNzZXNzbWVudC5uYW1lLFxyXG4gICAgICAgIGZyYW1ld29ya0lkOiB0aGlzLmFzc2Vzc21lbnQuZnJhbWV3b3JrSWQsXHJcbiAgICAgIH0gOiBudWxsLFxyXG4gICAgICBleHBvcnRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXHJcbiAgICB9O1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2hlY2sgaWYgdmlvbGF0aW9uIHJlcXVpcmVzIGltbWVkaWF0ZSBhdHRlbnRpb25cclxuICAgKi9cclxuICByZXF1aXJlc0ltbWVkaWF0ZUF0dGVudGlvbigpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLmlzQ3JpdGljYWwgfHwgXHJcbiAgICAgICAgICAgdGhpcy5pc092ZXJkdWUgfHwgXHJcbiAgICAgICAgICAgdGhpcy5kZXRhaWxzLmNvbXBsaWFuY2U/LnJlcG9ydGluZ1JlcXVpcmVkIHx8XHJcbiAgICAgICAgICAgdGhpcy5kZXRhaWxzLmJ1c2luZXNzSW1wYWN0Py5maW5hbmNpYWxJbXBhY3QgPiAxMDAwMDA7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgY29tcGxpYW5jZSBmcmFtZXdvcmtzIGFmZmVjdGVkXHJcbiAgICovXHJcbiAgZ2V0QWZmZWN0ZWRGcmFtZXdvcmtzKCk6IHN0cmluZ1tdIHtcclxuICAgIHJldHVybiB0aGlzLmRldGFpbHMuY29tcGxpYW5jZT8uZnJhbWV3b3JrcyB8fCBbXTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCBhZmZlY3RlZCBjb250cm9sc1xyXG4gICAqL1xyXG4gIGdldEFmZmVjdGVkQ29udHJvbHMoKTogc3RyaW5nW10ge1xyXG4gICAgcmV0dXJuIHRoaXMuZGV0YWlscy52aW9sYXRlZENvbnRyb2xzIHx8IFtdO1xyXG4gIH1cclxufVxyXG4iXSwidmVyc2lvbiI6M30=