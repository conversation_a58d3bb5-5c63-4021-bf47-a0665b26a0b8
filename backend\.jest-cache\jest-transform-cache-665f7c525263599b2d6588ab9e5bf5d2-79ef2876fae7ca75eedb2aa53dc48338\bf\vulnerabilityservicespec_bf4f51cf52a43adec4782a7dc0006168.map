{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\vulnerability.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,6CAAqD;AAErD,2CAAwE;AACxE,mEAA+D;AAC/D,qFAA2E;AAC3E,sFAAkF;AAClF,0FAAsF;AAEtF,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;IACpC,IAAI,OAA6B,CAAC;IAClC,IAAI,UAAqC,CAAC;IAC1C,IAAI,aAA4B,CAAC;IACjC,IAAI,YAA0B,CAAC;IAE/B,MAAM,cAAc,GAAG;QACrB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;KAC9B,CAAC;IAEF,MAAM,iBAAiB,GAAG;QACxB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;QACd,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;KACjB,CAAC;IAEF,MAAM,gBAAgB,GAAG;QACvB,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;KACzB,CAAC;IAEF,MAAM,iBAAiB,GAA2B;QAChD,EAAE,EAAE,sCAAsC;QAC1C,KAAK,EAAE,oBAAoB;QAC3B,WAAW,EAAE,gCAAgC;QAC7C,QAAQ,EAAE,MAAM;QAChB,MAAM,EAAE,MAAM;QACd,QAAQ,EAAE,MAAM;QAChB,MAAM,EAAE,QAAQ;QAChB,YAAY,EAAE,IAAI,IAAI,EAAE;QACxB,SAAS,EAAE,IAAI,IAAI,EAAE;QACrB,SAAS,EAAE,IAAI,IAAI,EAAE;KACtB,CAAC;IAEF,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,4CAAoB;gBACpB;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,oCAAa,CAAC;oBAC1C,QAAQ,EAAE,cAAc;iBACzB;gBACD;oBACE,OAAO,EAAE,8BAAa;oBACtB,QAAQ,EAAE,iBAAiB;iBAC5B;gBACD;oBACE,OAAO,EAAE,4BAAY;oBACrB,QAAQ,EAAE,gBAAgB;iBAC3B;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAAuB,4CAAoB,CAAC,CAAC;QACjE,UAAU,GAAG,MAAM,CAAC,GAAG,CAA4B,IAAA,4BAAkB,EAAC,oCAAa,CAAC,CAAC,CAAC;QACtF,aAAa,GAAG,MAAM,CAAC,GAAG,CAAgB,8BAAa,CAAC,CAAC;QACzD,YAAY,GAAG,MAAM,CAAC,GAAG,CAAe,4BAAY,CAAC,CAAC;IACxD,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,UAAU,GAAG;gBACjB,KAAK,EAAE,oBAAoB;gBAC3B,WAAW,EAAE,kBAAkB;gBAC/B,QAAQ,EAAE,MAAe;aAC1B,CAAC;YACF,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,cAAc,CAAC,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;YACzD,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YAEzD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YAExD,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAChD,MAAM,CAAC,gBAAgB,CAAC;gBACtB,KAAK,EAAE,UAAU,CAAC,KAAK;gBACvB,WAAW,EAAE,UAAU,CAAC,WAAW;gBACnC,QAAQ,EAAE,UAAU,CAAC,QAAQ;aAC9B,CAAC,CACH,CAAC;YACF,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;YACpE,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,MAAM,EACN,QAAQ,EACR,eAAe,EACf,iBAAiB,CAAC,EAAE,EACpB,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;YACtE,MAAM,UAAU,GAAG;gBACjB,WAAW,EAAE,kBAAkB;aAChC,CAAC;YACF,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,4BAAmB,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,KAAK,IAAI,EAAE;YAC5E,MAAM,UAAU,GAAG;gBACjB,KAAK,EAAE,oBAAoB;aAC5B,CAAC;YACF,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,4BAAmB,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,EAAE,GAAG,sCAAsC,CAAC;YAClD,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YAE5D,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAE1C,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBAClD,KAAK,EAAE,EAAE,EAAE,EAAE;gBACb,SAAS,EAAE,CAAC,aAAa,CAAC;aAC3B,CAAC,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,MAAM,EAAE,GAAG,sCAAsC,CAAC;YAClD,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE/C,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAE1C,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;YAC1B,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,yBAAyB,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,KAAK,GAAG,eAAe,CAAC;YAC9B,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YAE5D,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAEhD,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBAClD,KAAK,EAAE,EAAE,KAAK,EAAE;gBAChB,SAAS,EAAE,CAAC,aAAa,CAAC;aAC3B,CAAC,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,EAAE,GAAG,sCAAsC,CAAC;YAClD,MAAM,UAAU,GAAG,EAAE,QAAQ,EAAE,UAAmB,EAAE,CAAC;YACrD,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,iBAAkC,CAAC,CAAC;YACtF,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,GAAG,iBAAiB,EAAE,GAAG,UAAU,EAAE,CAAC,CAAC;YAE/E,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;YAE5D,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;YAClD,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,CAAC;YAC/C,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,MAAM,EACN,QAAQ,EACR,eAAe,EACf,EAAE,EACF,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,KAAK,IAAI,EAAE;YAC3E,MAAM,EAAE,GAAG,sCAAsC,CAAC;YAClD,MAAM,UAAU,GAAG,EAAE,QAAQ,EAAE,UAAmB,EAAE,CAAC;YACrD,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAExD,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,0BAAiB,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,EAAE,GAAG,sCAAsC,CAAC;YAClD,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,iBAAkC,CAAC,CAAC;YACtF,cAAc,CAAC,MAAM,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YAE3D,MAAM,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;YAEjC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;YAClD,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;YACtE,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,MAAM,EACN,QAAQ,EACR,eAAe,EACf,EAAE,EACF,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,KAAK,IAAI,EAAE;YAC3E,MAAM,EAAE,GAAG,sCAAsC,CAAC;YAClD,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAExD,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,0BAAiB,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,cAAc,CAAC,KAAK;iBACjB,qBAAqB,CAAC,GAAG,CAAC,CAAC,QAAQ;iBACnC,qBAAqB,CAAC,EAAE,CAAC,CAAE,WAAW;iBACtC,qBAAqB,CAAC,EAAE,CAAC,CAAE,OAAO;iBAClC,qBAAqB,CAAC,EAAE,CAAC,CAAE,SAAS;iBACpC,qBAAqB,CAAC,EAAE,CAAC,CAAE,MAAM;iBACjC,qBAAqB,CAAC,EAAE,CAAC,CAAE,OAAO;iBAClC,qBAAqB,CAAC,EAAE,CAAC,CAAE,cAAc;iBACzC,qBAAqB,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW;YAEzC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,aAAa,EAAE,CAAC;YAE7C,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,KAAK,EAAE,GAAG;gBACV,UAAU,EAAE;oBACV,QAAQ,EAAE,EAAE;oBACZ,IAAI,EAAE,EAAE;oBACR,MAAM,EAAE,EAAE;oBACV,GAAG,EAAE,EAAE;iBACR;gBACD,QAAQ,EAAE;oBACR,IAAI,EAAE,EAAE;oBACR,UAAU,EAAE,EAAE;oBACd,QAAQ,EAAE,EAAE;iBACb;gBACD,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC9B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\vulnerability.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { getRepositoryToken } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\nimport { NotFoundException, BadRequestException } from '@nestjs/common';\r\nimport { VulnerabilityService } from './vulnerability.service';\r\nimport { Vulnerability } from '../../domain/entities/vulnerability.entity';\r\nimport { LoggerService } from '../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../infrastructure/logging/audit/audit.service';\r\n\r\ndescribe('VulnerabilityService', () => {\r\n  let service: VulnerabilityService;\r\n  let repository: Repository<Vulnerability>;\r\n  let loggerService: LoggerService;\r\n  let auditService: AuditService;\r\n\r\n  const mockRepository = {\r\n    create: jest.fn(),\r\n    save: jest.fn(),\r\n    findOne: jest.fn(),\r\n    find: jest.fn(),\r\n    remove: jest.fn(),\r\n    count: jest.fn(),\r\n    createQueryBuilder: jest.fn(),\r\n  };\r\n\r\n  const mockLoggerService = {\r\n    debug: jest.fn(),\r\n    log: jest.fn(),\r\n    warn: jest.fn(),\r\n    error: jest.fn(),\r\n  };\r\n\r\n  const mockAuditService = {\r\n    logUserAction: jest.fn(),\r\n  };\r\n\r\n  const mockVulnerability: Partial<Vulnerability> = {\r\n    id: '123e4567-e89b-12d3-a456-426614174000',\r\n    title: 'Test Vulnerability',\r\n    description: 'Test vulnerability description',\r\n    severity: 'high',\r\n    status: 'open',\r\n    priority: 'high',\r\n    source: 'manual',\r\n    discoveredAt: new Date(),\r\n    createdAt: new Date(),\r\n    updatedAt: new Date(),\r\n  };\r\n\r\n  beforeEach(async () => {\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        VulnerabilityService,\r\n        {\r\n          provide: getRepositoryToken(Vulnerability),\r\n          useValue: mockRepository,\r\n        },\r\n        {\r\n          provide: LoggerService,\r\n          useValue: mockLoggerService,\r\n        },\r\n        {\r\n          provide: AuditService,\r\n          useValue: mockAuditService,\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    service = module.get<VulnerabilityService>(VulnerabilityService);\r\n    repository = module.get<Repository<Vulnerability>>(getRepositoryToken(Vulnerability));\r\n    loggerService = module.get<LoggerService>(LoggerService);\r\n    auditService = module.get<AuditService>(AuditService);\r\n  });\r\n\r\n  afterEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('create', () => {\r\n    it('should create a vulnerability successfully', async () => {\r\n      const createData = {\r\n        title: 'Test Vulnerability',\r\n        description: 'Test description',\r\n        severity: 'high' as const,\r\n      };\r\n      const userId = 'user-123';\r\n\r\n      mockRepository.create.mockReturnValue(mockVulnerability);\r\n      mockRepository.save.mockResolvedValue(mockVulnerability);\r\n\r\n      const result = await service.create(createData, userId);\r\n\r\n      expect(mockRepository.create).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          title: createData.title,\r\n          description: createData.description,\r\n          severity: createData.severity,\r\n        }),\r\n      );\r\n      expect(mockRepository.save).toHaveBeenCalledWith(mockVulnerability);\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        userId,\r\n        'create',\r\n        'vulnerability',\r\n        mockVulnerability.id,\r\n        expect.any(Object),\r\n      );\r\n      expect(result).toEqual(mockVulnerability);\r\n    });\r\n\r\n    it('should throw BadRequestException when title is missing', async () => {\r\n      const createData = {\r\n        description: 'Test description',\r\n      };\r\n      const userId = 'user-123';\r\n\r\n      await expect(service.create(createData, userId)).rejects.toThrow(BadRequestException);\r\n    });\r\n\r\n    it('should throw BadRequestException when description is missing', async () => {\r\n      const createData = {\r\n        title: 'Test Vulnerability',\r\n      };\r\n      const userId = 'user-123';\r\n\r\n      await expect(service.create(createData, userId)).rejects.toThrow(BadRequestException);\r\n    });\r\n  });\r\n\r\n  describe('findById', () => {\r\n    it('should return vulnerability when found', async () => {\r\n      const id = '123e4567-e89b-12d3-a456-426614174000';\r\n      mockRepository.findOne.mockResolvedValue(mockVulnerability);\r\n\r\n      const result = await service.findById(id);\r\n\r\n      expect(mockRepository.findOne).toHaveBeenCalledWith({\r\n        where: { id },\r\n        relations: ['assessments'],\r\n      });\r\n      expect(result).toEqual(mockVulnerability);\r\n    });\r\n\r\n    it('should return null when vulnerability not found', async () => {\r\n      const id = '123e4567-e89b-12d3-a456-426614174000';\r\n      mockRepository.findOne.mockResolvedValue(null);\r\n\r\n      const result = await service.findById(id);\r\n\r\n      expect(result).toBeNull();\r\n      expect(mockLoggerService.warn).toHaveBeenCalledWith('Vulnerability not found', { id });\r\n    });\r\n  });\r\n\r\n  describe('findByCveId', () => {\r\n    it('should return vulnerability when found by CVE ID', async () => {\r\n      const cveId = 'CVE-2023-1234';\r\n      mockRepository.findOne.mockResolvedValue(mockVulnerability);\r\n\r\n      const result = await service.findByCveId(cveId);\r\n\r\n      expect(mockRepository.findOne).toHaveBeenCalledWith({\r\n        where: { cveId },\r\n        relations: ['assessments'],\r\n      });\r\n      expect(result).toEqual(mockVulnerability);\r\n    });\r\n  });\r\n\r\n  describe('update', () => {\r\n    it('should update vulnerability successfully', async () => {\r\n      const id = '123e4567-e89b-12d3-a456-426614174000';\r\n      const updateData = { severity: 'critical' as const };\r\n      const userId = 'user-123';\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(mockVulnerability as Vulnerability);\r\n      mockRepository.save.mockResolvedValue({ ...mockVulnerability, ...updateData });\r\n\r\n      const result = await service.update(id, updateData, userId);\r\n\r\n      expect(service.findById).toHaveBeenCalledWith(id);\r\n      expect(mockRepository.save).toHaveBeenCalled();\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        userId,\r\n        'update',\r\n        'vulnerability',\r\n        id,\r\n        expect.any(Object),\r\n      );\r\n      expect(result).toEqual(expect.objectContaining(updateData));\r\n    });\r\n\r\n    it('should throw NotFoundException when vulnerability not found', async () => {\r\n      const id = '123e4567-e89b-12d3-a456-426614174000';\r\n      const updateData = { severity: 'critical' as const };\r\n      const userId = 'user-123';\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(null);\r\n\r\n      await expect(service.update(id, updateData, userId)).rejects.toThrow(NotFoundException);\r\n    });\r\n  });\r\n\r\n  describe('delete', () => {\r\n    it('should delete vulnerability successfully', async () => {\r\n      const id = '123e4567-e89b-12d3-a456-426614174000';\r\n      const userId = 'user-123';\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(mockVulnerability as Vulnerability);\r\n      mockRepository.remove.mockResolvedValue(mockVulnerability);\r\n\r\n      await service.delete(id, userId);\r\n\r\n      expect(service.findById).toHaveBeenCalledWith(id);\r\n      expect(mockRepository.remove).toHaveBeenCalledWith(mockVulnerability);\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        userId,\r\n        'delete',\r\n        'vulnerability',\r\n        id,\r\n        expect.any(Object),\r\n      );\r\n    });\r\n\r\n    it('should throw NotFoundException when vulnerability not found', async () => {\r\n      const id = '123e4567-e89b-12d3-a456-426614174000';\r\n      const userId = 'user-123';\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(null);\r\n\r\n      await expect(service.delete(id, userId)).rejects.toThrow(NotFoundException);\r\n    });\r\n  });\r\n\r\n  describe('getStatistics', () => {\r\n    it('should return vulnerability statistics', async () => {\r\n      mockRepository.count\r\n        .mockResolvedValueOnce(100) // total\r\n        .mockResolvedValueOnce(10)  // critical\r\n        .mockResolvedValueOnce(20)  // high\r\n        .mockResolvedValueOnce(30)  // medium\r\n        .mockResolvedValueOnce(40)  // low\r\n        .mockResolvedValueOnce(50)  // open\r\n        .mockResolvedValueOnce(25)  // in_progress\r\n        .mockResolvedValueOnce(25); // resolved\r\n\r\n      const result = await service.getStatistics();\r\n\r\n      expect(result).toEqual({\r\n        total: 100,\r\n        bySeverity: {\r\n          critical: 10,\r\n          high: 20,\r\n          medium: 30,\r\n          low: 40,\r\n        },\r\n        byStatus: {\r\n          open: 50,\r\n          inProgress: 25,\r\n          resolved: 25,\r\n        },\r\n        timestamp: expect.any(String),\r\n      });\r\n    });\r\n  });\r\n});\r\n"], "version": 3}