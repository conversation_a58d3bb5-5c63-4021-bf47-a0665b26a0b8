a5179e1035071594c3a28a009771f4e0
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ThreatIntelligenceService_1;
var _a, _b, _c, _d, _e, _f, _g, _h;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThreatIntelligenceService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const config_1 = require("@nestjs/config");
const threat_intelligence_entity_1 = require("../../domain/entities/threat-intelligence.entity");
const indicator_of_compromise_entity_1 = require("../../domain/entities/indicator-of-compromise.entity");
const threat_actor_entity_1 = require("../../domain/entities/threat-actor.entity");
const threat_campaign_entity_1 = require("../../domain/entities/threat-campaign.entity");
const logger_service_1 = require("../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../infrastructure/logging/audit/audit.service");
const notification_service_1 = require("../../../../infrastructure/notification/notification.service");
/**
 * Service for managing threat intelligence data
 * Provides comprehensive threat intelligence operations including search, correlation, and analysis
 */
let ThreatIntelligenceService = ThreatIntelligenceService_1 = class ThreatIntelligenceService {
    constructor(threatIntelligenceRepository, iocRepository, threatActorRepository, threatCampaignRepository, loggerService, auditService, notificationService, configService) {
        this.threatIntelligenceRepository = threatIntelligenceRepository;
        this.iocRepository = iocRepository;
        this.threatActorRepository = threatActorRepository;
        this.threatCampaignRepository = threatCampaignRepository;
        this.loggerService = loggerService;
        this.auditService = auditService;
        this.notificationService = notificationService;
        this.configService = configService;
        this.logger = new common_1.Logger(ThreatIntelligenceService_1.name);
    }
    /**
     * Create new threat intelligence
     */
    async createThreatIntelligence(threatData, userId) {
        this.logger.debug('Creating threat intelligence', {
            title: threatData.title,
            threatType: threatData.threatType,
            userId,
        });
        try {
            // Check for duplicates based on title and data source
            if (threatData.title && threatData.dataSource) {
                const existing = await this.threatIntelligenceRepository.findOne({
                    where: {
                        title: threatData.title,
                        dataSource: threatData.dataSource,
                    },
                });
                if (existing) {
                    throw new common_1.ConflictException('Threat intelligence with this title and data source already exists');
                }
            }
            // Set default values
            const threat = this.threatIntelligenceRepository.create({
                ...threatData,
                status: threatData.status || threat_intelligence_entity_1.ThreatStatus.ACTIVE,
                confidence: threatData.confidence || threat_intelligence_entity_1.ThreatConfidence.MEDIUM,
                severity: threatData.severity || threat_intelligence_entity_1.ThreatSeverity.MEDIUM,
                firstSeen: threatData.firstSeen || new Date(),
                observationCount: 0,
                isIoc: false,
                isAttributed: !!threatData.threatActorId,
            });
            // Calculate initial risk score
            threat.calculateRiskScore();
            const savedThreat = await this.threatIntelligenceRepository.save(threat);
            // Send notification for critical threats
            if (savedThreat.severity === threat_intelligence_entity_1.ThreatSeverity.CRITICAL) {
                await this.notificationService.sendCriticalThreatAlert({
                    threatId: savedThreat.id,
                    title: savedThreat.title,
                    threatType: savedThreat.threatType,
                    severity: savedThreat.severity,
                    dataSource: savedThreat.dataSource.name,
                });
            }
            // Audit log
            await this.auditService.logUserAction(userId, 'create', 'threat_intelligence', savedThreat.id, {
                title: savedThreat.title,
                threatType: savedThreat.threatType,
                severity: savedThreat.severity,
                dataSource: savedThreat.dataSource.name,
            });
            this.logger.log('Threat intelligence created successfully', {
                threatId: savedThreat.id,
                title: savedThreat.title,
                userId,
            });
            return savedThreat;
        }
        catch (error) {
            this.loggerService.error('Failed to create threat intelligence', {
                error: error.message,
                threatData: {
                    title: threatData.title,
                    threatType: threatData.threatType,
                },
                userId,
            });
            throw error;
        }
    }
    /**
     * Get threat intelligence by ID
     */
    async getThreatIntelligenceById(id) {
        const threat = await this.threatIntelligenceRepository.findOne({
            where: { id },
            relations: ['indicators', 'threatActor', 'threatCampaign'],
        });
        if (!threat) {
            throw new common_1.NotFoundException('Threat intelligence not found');
        }
        return threat;
    }
    /**
     * Search threat intelligence with advanced filtering
     */
    async searchThreatIntelligence(criteria) {
        const { page = 1, limit = 50, threatTypes, severities, statuses, confidenceLevels, tags, searchText, dateRange, riskScoreMin, riskScoreMax, threatActorId, campaignId, dataSource, includeExpired = false, sortBy = 'firstSeen', sortOrder = 'DESC', } = criteria;
        const queryBuilder = this.threatIntelligenceRepository.createQueryBuilder('threat')
            .leftJoinAndSelect('threat.threatActor', 'actor')
            .leftJoinAndSelect('threat.threatCampaign', 'campaign')
            .leftJoinAndSelect('threat.indicators', 'indicators');
        // Apply filters
        if (threatTypes && threatTypes.length > 0) {
            queryBuilder.andWhere('threat.threatType IN (:...threatTypes)', { threatTypes });
        }
        if (severities && severities.length > 0) {
            queryBuilder.andWhere('threat.severity IN (:...severities)', { severities });
        }
        if (statuses && statuses.length > 0) {
            queryBuilder.andWhere('threat.status IN (:...statuses)', { statuses });
        }
        if (confidenceLevels && confidenceLevels.length > 0) {
            queryBuilder.andWhere('threat.confidence IN (:...confidenceLevels)', { confidenceLevels });
        }
        if (tags && tags.length > 0) {
            queryBuilder.andWhere('threat.tags && :tags', { tags });
        }
        if (searchText) {
            queryBuilder.andWhere('(LOWER(threat.title) LIKE LOWER(:searchText) OR LOWER(threat.description) LIKE LOWER(:searchText))', { searchText: `%${searchText}%` });
        }
        if (dateRange) {
            queryBuilder.andWhere('threat.firstSeen BETWEEN :startDate AND :endDate', {
                startDate: dateRange.startDate,
                endDate: dateRange.endDate,
            });
        }
        if (riskScoreMin !== undefined) {
            queryBuilder.andWhere('threat.riskScore >= :riskScoreMin', { riskScoreMin });
        }
        if (riskScoreMax !== undefined) {
            queryBuilder.andWhere('threat.riskScore <= :riskScoreMax', { riskScoreMax });
        }
        if (threatActorId) {
            queryBuilder.andWhere('threat.threatActorId = :threatActorId', { threatActorId });
        }
        if (campaignId) {
            queryBuilder.andWhere('threat.threatCampaignId = :campaignId', { campaignId });
        }
        if (dataSource) {
            queryBuilder.andWhere("threat.dataSource->>'name' = :dataSource", { dataSource });
        }
        if (!includeExpired) {
            queryBuilder.andWhere('(threat.expiresAt IS NULL OR threat.expiresAt > :now)', { now: new Date() });
        }
        // Apply sorting
        if (['firstSeen', 'lastSeen', 'riskScore', 'severity', 'observationCount'].includes(sortBy)) {
            queryBuilder.orderBy(`threat.${sortBy}`, sortOrder);
        }
        else {
            queryBuilder.orderBy('threat.firstSeen', 'DESC');
        }
        // Add secondary sort for consistency
        queryBuilder.addOrderBy('threat.id', 'ASC');
        // Apply pagination
        const offset = (page - 1) * limit;
        queryBuilder.skip(offset).take(limit);
        const [threats, total] = await queryBuilder.getManyAndCount();
        const totalPages = Math.ceil(total / limit);
        return {
            threats,
            total,
            page,
            totalPages,
        };
    }
    /**
     * Update threat intelligence
     */
    async updateThreatIntelligence(id, updates, userId) {
        const threat = await this.getThreatIntelligenceById(id);
        // Track changes for audit
        const changes = {};
        Object.keys(updates).forEach(key => {
            if (threat[key] !== updates[key]) {
                changes[key] = { from: threat[key], to: updates[key] };
            }
        });
        Object.assign(threat, updates);
        // Recalculate risk score if relevant fields changed
        if (updates.severity || updates.confidence || updates.observationCount) {
            threat.calculateRiskScore();
        }
        const updatedThreat = await this.threatIntelligenceRepository.save(threat);
        // Audit log
        await this.auditService.logUserAction(userId, 'update', 'threat_intelligence', id, {
            title: threat.title,
            changes,
        });
        this.logger.log('Threat intelligence updated', {
            threatId: id,
            changes: Object.keys(changes),
            userId,
        });
        return updatedThreat;
    }
    /**
     * Delete threat intelligence
     */
    async deleteThreatIntelligence(id, userId) {
        const threat = await this.getThreatIntelligenceById(id);
        await this.threatIntelligenceRepository.remove(threat);
        // Audit log
        await this.auditService.logUserAction(userId, 'delete', 'threat_intelligence', id, {
            title: threat.title,
            threatType: threat.threatType,
        });
        this.logger.log('Threat intelligence deleted', {
            threatId: id,
            title: threat.title,
            userId,
        });
    }
    /**
     * Record observation of threat intelligence
     */
    async recordObservation(id, context) {
        const threat = await this.getThreatIntelligenceById(id);
        threat.recordObservation();
        threat.calculateRiskScore();
        if (context) {
            threat.customAttributes = {
                ...threat.customAttributes,
                lastObservationContext: context,
            };
        }
        return await this.threatIntelligenceRepository.save(threat);
    }
    /**
     * Find related threat intelligence
     */
    async findRelatedThreats(criteria) {
        const baseThreat = await this.getThreatIntelligenceById(criteria.threatIntelligenceId);
        const results = [];
        // Correlate by threat actor
        if (criteria.correlationTypes.includes('actor') && baseThreat.threatActorId) {
            const actorThreats = await this.threatIntelligenceRepository.find({
                where: {
                    threatActorId: baseThreat.threatActorId,
                    id: (0, typeorm_2.Not)(baseThreat.id),
                },
                relations: ['threatActor'],
                take: 10,
            });
            results.push({
                relatedThreats: actorThreats,
                correlationScore: 0.9,
                correlationType: 'threat_actor',
            });
        }
        // Correlate by campaign
        if (criteria.correlationTypes.includes('campaign') && baseThreat.threatCampaignId) {
            const campaignThreats = await this.threatIntelligenceRepository.find({
                where: {
                    threatCampaignId: baseThreat.threatCampaignId,
                    id: (0, typeorm_2.Not)(baseThreat.id),
                },
                relations: ['threatCampaign'],
                take: 10,
            });
            results.push({
                relatedThreats: campaignThreats,
                correlationScore: 0.8,
                correlationType: 'campaign',
            });
        }
        // Correlate by tags
        if (baseThreat.tags && baseThreat.tags.length > 0) {
            const tagThreats = await this.threatIntelligenceRepository
                .createQueryBuilder('threat')
                .where('threat.tags && :tags', { tags: baseThreat.tags })
                .andWhere('threat.id != :id', { id: baseThreat.id })
                .take(10)
                .getMany();
            if (tagThreats.length > 0) {
                results.push({
                    relatedThreats: tagThreats,
                    correlationScore: 0.6,
                    correlationType: 'tags',
                });
            }
        }
        return results;
    }
    /**
     * Get threat intelligence dashboard data
     */
    async getDashboardData() {
        const [totalThreats, activeThreats, criticalThreats, recentThreats, topThreatTypes, topActors,] = await Promise.all([
            this.threatIntelligenceRepository.count(),
            this.threatIntelligenceRepository.count({ where: { status: threat_intelligence_entity_1.ThreatStatus.ACTIVE } }),
            this.threatIntelligenceRepository.count({
                where: {
                    status: threat_intelligence_entity_1.ThreatStatus.ACTIVE,
                    severity: threat_intelligence_entity_1.ThreatSeverity.CRITICAL,
                },
            }),
            this.threatIntelligenceRepository.count({
                where: {
                    firstSeen: (0, typeorm_2.Between)(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), new Date()),
                },
            }),
            this.getThreatTypeDistribution(),
            this.getTopThreatActors(),
        ]);
        return {
            summary: {
                total: totalThreats,
                active: activeThreats,
                critical: criticalThreats,
                recent: recentThreats,
            },
            distribution: {
                threatTypes: topThreatTypes,
                topActors,
            },
            timestamp: new Date(),
        };
    }
    /**
     * Export threat intelligence for reporting
     */
    async exportThreatIntelligence(criteria, format) {
        const { threats } = await this.searchThreatIntelligence({
            ...criteria,
            limit: 10000, // Large limit for export
        });
        const exportData = threats.map(threat => threat.exportForReporting());
        switch (format) {
            case 'json':
                return {
                    format: 'json',
                    data: exportData,
                    exportedAt: new Date(),
                    count: exportData.length,
                };
            case 'csv':
                return this.convertToCSV(exportData);
            case 'stix':
                return this.convertToSTIX(threats);
            default:
                throw new Error(`Unsupported export format: ${format}`);
        }
    }
    // Private helper methods
    async getThreatTypeDistribution() {
        const result = await this.threatIntelligenceRepository
            .createQueryBuilder('threat')
            .select('threat.threatType', 'type')
            .addSelect('COUNT(*)', 'count')
            .where('threat.status = :status', { status: threat_intelligence_entity_1.ThreatStatus.ACTIVE })
            .groupBy('threat.threatType')
            .orderBy('count', 'DESC')
            .getRawMany();
        return result.map(item => ({
            type: item.type,
            count: parseInt(item.count),
        }));
    }
    async getTopThreatActors() {
        const result = await this.threatActorRepository
            .createQueryBuilder('actor')
            .leftJoin('actor.threatIntelligence', 'threat')
            .select('actor.name', 'name')
            .addSelect('COUNT(threat.id)', 'threatCount')
            .where('threat.status = :status', { status: threat_intelligence_entity_1.ThreatStatus.ACTIVE })
            .groupBy('actor.id')
            .addGroupBy('actor.name')
            .orderBy('threatCount', 'DESC')
            .limit(10)
            .getRawMany();
        return result.map(item => ({
            name: item.name,
            threatCount: parseInt(item.threatCount),
        }));
    }
    convertToCSV(data) {
        // CSV conversion implementation
        const headers = Object.keys(data[0] || {});
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => JSON.stringify(row[header] || '')).join(',')),
        ].join('\n');
        return {
            format: 'csv',
            content: csvContent,
            filename: `threat_intelligence_${new Date().toISOString().split('T')[0]}.csv`,
        };
    }
    convertToSTIX(threats) {
        // STIX 2.1 format conversion
        const stixObjects = threats.map(threat => ({
            type: 'threat-actor',
            id: `threat-actor--${threat.id}`,
            created: threat.createdAt.toISOString(),
            modified: threat.updatedAt.toISOString(),
            name: threat.title,
            description: threat.description,
            labels: [threat.threatType],
            // Add more STIX properties as needed
        }));
        return {
            format: 'stix',
            spec_version: '2.1',
            type: 'bundle',
            id: `bundle--${Date.now()}`,
            objects: stixObjects,
        };
    }
};
exports.ThreatIntelligenceService = ThreatIntelligenceService;
exports.ThreatIntelligenceService = ThreatIntelligenceService = ThreatIntelligenceService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(threat_intelligence_entity_1.ThreatIntelligence)),
    __param(1, (0, typeorm_1.InjectRepository)(indicator_of_compromise_entity_1.IndicatorOfCompromise)),
    __param(2, (0, typeorm_1.InjectRepository)(threat_actor_entity_1.ThreatActor)),
    __param(3, (0, typeorm_1.InjectRepository)(threat_campaign_entity_1.ThreatCampaign)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _c : Object, typeof (_d = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _d : Object, typeof (_e = typeof logger_service_1.LoggerService !== "undefined" && logger_service_1.LoggerService) === "function" ? _e : Object, typeof (_f = typeof audit_service_1.AuditService !== "undefined" && audit_service_1.AuditService) === "function" ? _f : Object, typeof (_g = typeof notification_service_1.NotificationService !== "undefined" && notification_service_1.NotificationService) === "function" ? _g : Object, typeof (_h = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _h : Object])
], ThreatIntelligenceService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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