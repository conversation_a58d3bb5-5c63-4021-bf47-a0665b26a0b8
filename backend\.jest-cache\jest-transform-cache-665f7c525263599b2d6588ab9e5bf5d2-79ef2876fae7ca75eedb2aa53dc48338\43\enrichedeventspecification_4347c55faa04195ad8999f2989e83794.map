{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\enriched-event.specification.ts", "mappings": ";;;AAAA,6DAA8E;AAQ9E;;;;;GAKG;AACH,MAAsB,0BAA2B,SAAQ,iCAAgC;IACvF;;OAEG;IACO,cAAc,CAAC,KAAoB,EAAE,KAAkB;QAC/D,OAAO,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACO,kBAAkB,CAAC,KAAoB,EAAE,UAA2B;QAC5E,OAAO,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACO,gBAAgB,CAAC,KAAoB,EAAE,QAAuB;QACtE,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACO,0BAA0B,CAAC,KAAoB,EAAE,QAAiC;QAC1F,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACO,0BAA0B,CAAC,KAAoB,EAAE,QAA4B;QACrF,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACO,SAAS,CAAC,KAAoB,EAAE,IAAc;QACtD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACO,UAAU,CAAC,KAAoB,EAAE,IAAc;QACvD,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACO,mCAAmC,CAAC,KAAoB,EAAE,QAAiB,EAAE,QAAiB;QACtG,MAAM,YAAY,GAAG,KAAK,CAAC,sBAAsB,CAAC;QAElD,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YAC/B,OAAO,QAAQ,KAAK,SAAS,CAAC,CAAC,oDAAoD;QACrF,CAAC;QAED,IAAI,QAAQ,KAAK,SAAS,IAAI,YAAY,GAAG,QAAQ,EAAE,CAAC;YACtD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,QAAQ,KAAK,SAAS,IAAI,YAAY,GAAG,QAAQ,EAAE,CAAC;YACtD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACO,6BAA6B,CAAC,KAAoB,EAAE,QAAiB,EAAE,QAAiB;QAChG,MAAM,WAAW,GAAG,KAAK,CAAC,gBAAgB,CAAC;QAE3C,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;YAC9B,OAAO,QAAQ,KAAK,SAAS,CAAC,CAAC,oDAAoD;QACrF,CAAC;QAED,IAAI,QAAQ,KAAK,SAAS,IAAI,WAAW,GAAG,QAAQ,EAAE,CAAC;YACrD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,QAAQ,KAAK,SAAS,IAAI,WAAW,GAAG,QAAQ,EAAE,CAAC;YACrD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACO,sBAAsB,CAAC,KAAoB,EAAE,QAAiB,EAAE,QAAiB;QACzF,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;QAElC,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;YAC5B,OAAO,QAAQ,KAAK,SAAS,CAAC,CAAC,oDAAoD;QACrF,CAAC;QAED,IAAI,QAAQ,KAAK,SAAS,IAAI,SAAS,GAAG,QAAQ,EAAE,CAAC;YACnD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,QAAQ,KAAK,SAAS,IAAI,SAAS,GAAG,QAAQ,EAAE,CAAC;YACnD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACO,cAAc,CAAC,KAAoB,EAAE,MAAc;QAC3D,OAAO,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACO,2BAA2B,CAAC,KAAoB,EAAE,MAAwB;QAClF,OAAO,KAAK,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACO,uBAAuB,CAAC,KAAoB,EAAE,IAAY;QAClE,OAAO,KAAK,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;IACxD,CAAC;CACF;AArID,gEAqIC;AAED;;;;GAIG;AACH,MAAa,gCAAiC,SAAQ,0BAA0B;IAC9E,aAAa,CAAC,KAAoB;QAChC,OAAO,KAAK,CAAC,qBAAqB,EAAE,CAAC;IACvC,CAAC;IAED,cAAc;QACZ,OAAO,yCAAyC,CAAC;IACnD,CAAC;CACF;AARD,4EAQC;AAED;;;;GAIG;AACH,MAAa,6BAA8B,SAAQ,0BAA0B;IAC3E,aAAa,CAAC,KAAoB;QAChC,OAAO,KAAK,CAAC,kBAAkB,EAAE,CAAC;IACpC,CAAC;IAED,cAAc;QACZ,OAAO,sCAAsC,CAAC;IAChD,CAAC;CACF;AARD,sEAQC;AAED;;;;GAIG;AACH,MAAa,iCAAkC,SAAQ,0BAA0B;IAC/E,aAAa,CAAC,KAAoB;QAChC,OAAO,KAAK,CAAC,sBAAsB,EAAE,CAAC;IACxC,CAAC;IAED,cAAc;QACZ,OAAO,4CAA4C,CAAC;IACtD,CAAC;CACF;AARD,8EAQC;AAED;;;;GAIG;AACH,MAAa,8BAA+B,SAAQ,0BAA0B;IAC5E,aAAa,CAAC,KAAoB;QAChC,OAAO,KAAK,CAAC,mBAAmB,EAAE,CAAC;IACrC,CAAC;IAED,cAAc;QACZ,OAAO,+CAA+C,CAAC;IACzD,CAAC;CACF;AARD,wEAQC;AAED;;;;GAIG;AACH,MAAa,kCAAmC,SAAQ,0BAA0B;IAChF,aAAa,CAAC,KAAoB;QAChC,OAAO,KAAK,CAAC,wBAAwB,EAAE,CAAC;IAC1C,CAAC;IAED,cAAc;QACZ,OAAO,oDAAoD,CAAC;IAC9D,CAAC;CACF;AARD,gFAQC;AAED;;;;GAIG;AACH,MAAa,gCAAiC,SAAQ,0BAA0B;IAC9E,aAAa,CAAC,KAAoB;QAChC,OAAO,KAAK,CAAC,mBAAmB,EAAE,CAAC;IACrC,CAAC;IAED,cAAc;QACZ,OAAO,sCAAsC,CAAC;IAChD,CAAC;CACF;AARD,4EAQC;AAED;;;;GAIG;AACH,MAAa,iCAAkC,SAAQ,0BAA0B;IAC/E,aAAa,CAAC,KAAoB;QAChC,OAAO,KAAK,CAAC,oBAAoB,CAAC;IACpC,CAAC;IAED,cAAc;QACZ,OAAO,uCAAuC,CAAC;IACjD,CAAC;CACF;AARD,8EAQC;AAED;;;;GAIG;AACH,MAAa,8BAA+B,SAAQ,0BAA0B;IAC5E,aAAa,CAAC,KAAoB;QAChC,OAAO,KAAK,CAAC,mBAAmB,EAAE,CAAC;IACrC,CAAC;IAED,cAAc;QACZ,OAAO,mDAAmD,CAAC;IAC7D,CAAC;CACF;AARD,wEAQC;AAED;;;;GAIG;AACH,MAAa,2BAA4B,SAAQ,0BAA0B;IACzE,aAAa,CAAC,KAAoB;QAChC,OAAO,KAAK,CAAC,gBAAgB,EAAE,CAAC;IAClC,CAAC;IAED,cAAc;QACZ,OAAO,0DAA0D,CAAC;IACpE,CAAC;CACF;AARD,kEAQC;AAED;;;;GAIG;AACH,MAAa,kCAAmC,SAAQ,0BAA0B;IAChF,aAAa,CAAC,KAAoB;QAChC,OAAO,KAAK,CAAC,qBAAqB,EAAE,CAAC;IACvC,CAAC;IAED,cAAc;QACZ,OAAO,6CAA6C,CAAC;IACvD,CAAC;CACF;AARD,gFAQC;AAED;;;;GAIG;AACH,MAAa,8BAA+B,SAAQ,0BAA0B;IAC5E,aAAa,CAAC,KAAoB;QAChC,OAAO,KAAK,CAAC,iBAAiB,EAAE,CAAC;IACnC,CAAC;IAED,cAAc;QACZ,OAAO,oCAAoC,CAAC;IAC9C,CAAC;CACF;AARD,wEAQC;AAED;;;;GAIG;AACH,MAAa,+BAAgC,SAAQ,0BAA0B;IAC7E,aAAa,CAAC,KAAoB;QAChC,OAAO,KAAK,CAAC,kBAAkB,EAAE,CAAC;IACpC,CAAC;IAED,cAAc;QACZ,OAAO,qCAAqC,CAAC;IAC/C,CAAC;CACF;AARD,0EAQC;AAED;;;;GAIG;AACH,MAAa,6BAA8B,SAAQ,0BAA0B;IAC3E,YAA6B,QAA4B;QACvD,KAAK,EAAE,CAAC;QADmB,aAAQ,GAAR,QAAQ,CAAoB;IAEzD,CAAC;IAED,aAAa,CAAC,KAAoB;QAChC,OAAO,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC/D,CAAC;IAED,cAAc;QACZ,OAAO,oCAAoC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IACxE,CAAC;CACF;AAZD,sEAYC;AAED;;;;GAIG;AACH,MAAa,wCAAyC,SAAQ,0BAA0B;IACtF,YACmB,QAAiB,EACjB,QAAiB;QAElC,KAAK,EAAE,CAAC;QAHS,aAAQ,GAAR,QAAQ,CAAS;QACjB,aAAQ,GAAR,QAAQ,CAAS;IAGpC,CAAC;IAED,aAAa,CAAC,KAAoB;QAChC,OAAO,IAAI,CAAC,mCAAmC,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACvF,CAAC;IAED,cAAc;QACZ,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC/D,OAAO,2CAA2C,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;QACzF,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,4CAA4C,IAAI,CAAC,QAAQ,EAAE,CAAC;QACrE,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,2CAA2C,IAAI,CAAC,QAAQ,EAAE,CAAC;QACpE,CAAC;QACD,OAAO,sCAAsC,CAAC;IAChD,CAAC;CACF;AAtBD,4FAsBC;AAED;;;;GAIG;AACH,MAAa,kCAAmC,SAAQ,0BAA0B;IAChF,YACmB,QAAiB,EACjB,QAAiB;QAElC,KAAK,EAAE,CAAC;QAHS,aAAQ,GAAR,QAAQ,CAAS;QACjB,aAAQ,GAAR,QAAQ,CAAS;IAGpC,CAAC;IAED,aAAa,CAAC,KAAoB;QAChC,OAAO,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACjF,CAAC;IAED,cAAc;QACZ,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC/D,OAAO,uDAAuD,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;QACrG,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,wDAAwD,IAAI,CAAC,QAAQ,EAAE,CAAC;QACjF,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,uDAAuD,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChF,CAAC;QACD,OAAO,kDAAkD,CAAC;IAC5D,CAAC;CACF;AAtBD,gFAsBC;AAED;;;;GAIG;AACH,MAAa,wBAAyB,SAAQ,0BAA0B;IACtE,YAA6B,MAAc;QACzC,KAAK,EAAE,CAAC;QADmB,WAAM,GAAN,MAAM,CAAQ;IAE3C,CAAC;IAED,aAAa,CAAC,KAAoB;QAChC,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IACjD,CAAC;IAED,cAAc;QACZ,OAAO,oCAAoC,IAAI,CAAC,MAAM,EAAE,CAAC;IAC3D,CAAC;CACF;AAZD,4DAYC;AAED;;;;GAIG;AACH,MAAa,4BAA6B,SAAQ,0BAA0B;IAC1E,YAA6B,iBAAiC;QAC5D,KAAK,EAAE,CAAC;QADmB,sBAAiB,GAAjB,iBAAiB,CAAgB;IAE9D,CAAC;IAED,aAAa,CAAC,KAAoB;QAChC,OAAO,KAAK,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAChE,CAAC;IAED,cAAc;QACZ,OAAO,+CAA+C,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,EAAE,CAAC;IAC5F,CAAC;CACF;AAZD,oEAYC;AAED;;;;GAIG;AACH,MAAa,gCAAiC,SAAQ,0BAA0B;IAC9E,aAAa,CAAC,KAAoB;QAChC,OAAO,KAAK,CAAC,gCAAgC,EAAE,CAAC;IAClD,CAAC;IAED,cAAc;QACZ,OAAO,yDAAyD,CAAC;IACnE,CAAC;CACF;AARD,4EAQC;AAED;;;;GAIG;AACH,MAAa,0BAA2B,SAAQ,0BAA0B;IACxE,aAAa,CAAC,KAAoB;QAChC,OAAO,KAAK,CAAC,UAAU,KAAK,SAAS,CAAC;IACxC,CAAC;IAED,cAAc;QACZ,OAAO,2CAA2C,CAAC;IACrD,CAAC;CACF;AARD,gEAQC;AAED;;;;GAIG;AACH,MAAa,0BAA2B,SAAQ,0BAA0B;IACxE,aAAa,CAAC,KAAoB;QAChC,OAAO,KAAK,CAAC,oBAAoB,IAAI,KAAK,CAAC,UAAU,KAAK,SAAS,CAAC;IACtE,CAAC;IAED,cAAc;QACZ,OAAO,yCAAyC,CAAC;IACnD,CAAC;CACF;AARD,gEAQC;AAED;;;;GAIG;AACH,MAAa,oCAAqC,SAAQ,0BAA0B;IAClF,YACmB,aAAsB,EACtB,aAAsB;QAEvC,KAAK,EAAE,CAAC;QAHS,kBAAa,GAAb,aAAa,CAAS;QACtB,kBAAa,GAAb,aAAa,CAAS;IAGzC,CAAC;IAED,aAAa,CAAC,KAAoB;QAChC,MAAM,QAAQ,GAAG,KAAK,CAAC,qBAAqB,EAAE,CAAC;QAE/C,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;YACtB,OAAO,KAAK,CAAC,CAAC,wBAAwB;QACxC,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,IAAI,QAAQ,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YACtE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,IAAI,QAAQ,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YACtE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,cAAc;QACZ,MAAM,cAAc,GAAG,CAAC,EAAU,EAAE,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC;QAEjD,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YACzE,OAAO,sCAAsC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;QAC9H,CAAC;aAAM,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YAC5C,OAAO,uCAAuC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;QACrF,CAAC;aAAM,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YAC5C,OAAO,sCAAsC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;QACpF,CAAC;QACD,OAAO,iCAAiC,CAAC;IAC3C,CAAC;CACF;AAtCD,oFAsCC;AAED;;;;GAIG;AACH,MAAa,6BAA8B,SAAQ,0BAA0B;IAC3E,YAA6B,OAA2B;QACtD,KAAK,EAAE,CAAC;QADmB,YAAO,GAAP,OAAO,CAAoB;IAExD,CAAC;IAED,aAAa,CAAC,KAAoB;QAChC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;IACtF,CAAC;IAED,cAAc;QACZ,OAAO,yCAAyC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAC5E,CAAC;CACF;AAZD,sEAYC;AAED;;;;GAIG;AACH,MAAa,+BAAgC,SAAQ,0BAA0B;IAC7E,YAA6B,KAAe;QAC1C,KAAK,EAAE,CAAC;QADmB,UAAK,GAAL,KAAK,CAAU;IAE5C,CAAC;IAED,aAAa,CAAC,KAAoB;QAChC,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;IAC5E,CAAC;IAED,cAAc;QACZ,OAAO,qCAAqC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IACtE,CAAC;CACF;AAZD,0EAYC;AAED;;;;GAIG;AACH,MAAa,wCAAyC,SAAQ,0BAA0B;IACtF,YACmB,QAAiB,EACjB,QAAiB;QAElC,KAAK,EAAE,CAAC;QAHS,aAAQ,GAAR,QAAQ,CAAS;QACjB,aAAQ,GAAR,QAAQ,CAAS;IAGpC,CAAC;IAED,aAAa,CAAC,KAAoB;QAChC,MAAM,QAAQ,GAAG,KAAK,CAAC,yBAAyB,EAAE,CAAC;QAEnD,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,+CAA+C;QACrF,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,cAAc;QACZ,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC/D,OAAO,sDAAsD,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;QACpG,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,uDAAuD,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChF,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,sDAAsD,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC/E,CAAC;QACD,OAAO,iDAAiD,CAAC;IAC3D,CAAC;CACF;AApCD,4FAoCC;AAED;;;;GAIG;AACH,MAAa,iCAAiC;IAA9C;QACU,mBAAc,GAAiC,EAAE,CAAC;IAkP5D,CAAC;IAhPC;;OAEG;IACH,mBAAmB;QACjB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,gCAAgC,EAAE,CAAC,CAAC;QACjE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,6BAA6B,EAAE,CAAC,CAAC;QAC9D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,iCAAiC,EAAE,CAAC,CAAC;QAClE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,8BAA8B,EAAE,CAAC,CAAC;QAC/D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,kCAAkC,EAAE,CAAC,CAAC;QACnE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,gCAAgC,EAAE,CAAC,CAAC;QACjE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,iCAAiC,EAAE,CAAC,CAAC;QAClE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,8BAA8B,EAAE,CAAC,CAAC;QAC/D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,2BAA2B,EAAE,CAAC,CAAC;QAC5D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,kCAAkC,EAAE,CAAC,CAAC;QACnE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,8BAA8B,EAAE,CAAC,CAAC;QAC/D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,+BAA+B,EAAE,CAAC,CAAC;QAChE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,GAAG,QAA4B;QAClD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,6BAA6B,CAAC,QAAQ,CAAC,CAAC,CAAC;QACtE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,2BAA2B,CAAC,QAAiB,EAAE,QAAiB;QAC9D,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,wCAAwC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC3F,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAC,QAAiB,EAAE,QAAiB;QACxD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,kCAAkC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;QACrF,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,MAAc;QAC5B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,wBAAwB,CAAC,MAAM,CAAC,CAAC,CAAC;QAC/D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,iBAAiC;QACnD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,4BAA4B,CAAC,iBAAiB,CAAC,CAAC,CAAC;QAC9E,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,gCAAgC,EAAE,CAAC,CAAC;QACjE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,0BAA0B,EAAE,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,aAAa;QACX,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,0BAA0B,EAAE,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,uBAAuB,CAAC,aAAsB,EAAE,aAAsB;QACpE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,oCAAoC,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC,CAAC;QACjG,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAC,GAAG,OAA2B;QAClD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,6BAA6B,CAAC,OAAO,CAAC,CAAC,CAAC;QACrE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,uBAAuB,CAAC,GAAG,KAAe;QACxC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,+BAA+B,CAAC,KAAK,CAAC,CAAC,CAAC;QACrE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,2BAA2B,CAAC,QAAiB,EAAE,QAAiB;QAC9D,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,wCAAwC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC3F,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC;QAED,4CAA4C;QAC5C,IAAI,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpD,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAA+B,CAAC;QAChF,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,WAAW;QACT,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC;QAED,2CAA2C;QAC3C,IAAI,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpD,QAAQ,GAAG,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAA+B,CAAC;QAC/E,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM;QACX,OAAO,IAAI,iCAAiC,EAAE,CAAC;IACjD,CAAC;CACF;AAnPD,8EAmPC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\enriched-event.specification.ts"], "sourcesContent": ["import { BaseSpecification, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { EnrichedEvent, EnrichmentStatus } from '../entities/enriched-event.entity';\r\nimport { EventType } from '../enums/event-type.enum';\r\nimport { EventSeverity } from '../enums/event-severity.enum';\r\nimport { EventStatus } from '../enums/event-status.enum';\r\nimport { EventProcessingStatus } from '../enums/event-processing-status.enum';\r\nimport { EnrichmentSource } from '../enums/enrichment-source.enum';\r\n\r\n/**\r\n * EnrichedEvent Specification Base Class\r\n * \r\n * Base class for all enriched event-related specifications.\r\n * Provides common functionality for enriched event filtering and validation.\r\n */\r\nexport abstract class EnrichedEventSpecification extends BaseSpecification<EnrichedEvent> {\r\n  /**\r\n   * Helper method to check if enriched event matches any of the provided types\r\n   */\r\n  protected matchesAnyType(event: EnrichedEvent, types: EventType[]): boolean {\r\n    return types.includes(event.type);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if enriched event matches any of the provided severities\r\n   */\r\n  protected matchesAnySeverity(event: EnrichedEvent, severities: EventSeverity[]): boolean {\r\n    return severities.includes(event.severity);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if enriched event matches any of the provided statuses\r\n   */\r\n  protected matchesAnyStatus(event: EnrichedEvent, statuses: EventStatus[]): boolean {\r\n    return statuses.includes(event.status);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if enriched event matches any of the provided processing statuses\r\n   */\r\n  protected matchesAnyProcessingStatus(event: EnrichedEvent, statuses: EventProcessingStatus[]): boolean {\r\n    return statuses.includes(event.processingStatus);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if enriched event matches any of the provided enrichment statuses\r\n   */\r\n  protected matchesAnyEnrichmentStatus(event: EnrichedEvent, statuses: EnrichmentStatus[]): boolean {\r\n    return statuses.includes(event.enrichmentStatus);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if enriched event has any of the provided tags\r\n   */\r\n  protected hasAnyTag(event: EnrichedEvent, tags: string[]): boolean {\r\n    return event.tags.some(tag => tags.includes(tag));\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if enriched event has all of the provided tags\r\n   */\r\n  protected hasAllTags(event: EnrichedEvent, tags: string[]): boolean {\r\n    return tags.every(tag => event.tags.includes(tag));\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if enrichment quality score is within range\r\n   */\r\n  protected isEnrichmentQualityScoreWithinRange(event: EnrichedEvent, minScore?: number, maxScore?: number): boolean {\r\n    const qualityScore = event.enrichmentQualityScore;\r\n    \r\n    if (qualityScore === undefined) {\r\n      return minScore === undefined; // If no min score required, undefined is acceptable\r\n    }\r\n    \r\n    if (minScore !== undefined && qualityScore < minScore) {\r\n      return false;\r\n    }\r\n    \r\n    if (maxScore !== undefined && qualityScore > maxScore) {\r\n      return false;\r\n    }\r\n    \r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if threat intelligence score is within range\r\n   */\r\n  protected isThreatIntelScoreWithinRange(event: EnrichedEvent, minScore?: number, maxScore?: number): boolean {\r\n    const threatScore = event.threatIntelScore;\r\n    \r\n    if (threatScore === undefined) {\r\n      return minScore === undefined; // If no min score required, undefined is acceptable\r\n    }\r\n    \r\n    if (minScore !== undefined && threatScore < minScore) {\r\n      return false;\r\n    }\r\n    \r\n    if (maxScore !== undefined && threatScore > maxScore) {\r\n      return false;\r\n    }\r\n    \r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if risk score is within range\r\n   */\r\n  protected isRiskScoreWithinRange(event: EnrichedEvent, minScore?: number, maxScore?: number): boolean {\r\n    const riskScore = event.riskScore;\r\n    \r\n    if (riskScore === undefined) {\r\n      return minScore === undefined; // If no min score required, undefined is acceptable\r\n    }\r\n    \r\n    if (minScore !== undefined && riskScore < minScore) {\r\n      return false;\r\n    }\r\n    \r\n    if (maxScore !== undefined && riskScore > maxScore) {\r\n      return false;\r\n    }\r\n    \r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if applied rule exists\r\n   */\r\n  protected hasAppliedRule(event: EnrichedEvent, ruleId: string): boolean {\r\n    return event.hasAppliedRule(ruleId);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if enrichment data from source exists\r\n   */\r\n  protected hasEnrichmentDataFromSource(event: EnrichedEvent, source: EnrichmentSource): boolean {\r\n    return event.getEnrichmentDataBySource(source).length > 0;\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if enrichment data of type exists\r\n   */\r\n  protected hasEnrichmentDataOfType(event: EnrichedEvent, type: string): boolean {\r\n    return event.getEnrichmentDataByType(type).length > 0;\r\n  }\r\n}\r\n\r\n/**\r\n * Enrichment Completed Specification\r\n * \r\n * Specification for enriched events that have completed enrichment.\r\n */\r\nexport class EnrichmentCompletedSpecification extends EnrichedEventSpecification {\r\n  isSatisfiedBy(event: EnrichedEvent): boolean {\r\n    return event.isEnrichmentCompleted();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Enriched event has completed enrichment';\r\n  }\r\n}\r\n\r\n/**\r\n * Enrichment Failed Specification\r\n * \r\n * Specification for enriched events that have failed enrichment.\r\n */\r\nexport class EnrichmentFailedSpecification extends EnrichedEventSpecification {\r\n  isSatisfiedBy(event: EnrichedEvent): boolean {\r\n    return event.isEnrichmentFailed();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Enriched event has failed enrichment';\r\n  }\r\n}\r\n\r\n/**\r\n * Enrichment In Progress Specification\r\n * \r\n * Specification for enriched events that are currently being enriched.\r\n */\r\nexport class EnrichmentInProgressSpecification extends EnrichedEventSpecification {\r\n  isSatisfiedBy(event: EnrichedEvent): boolean {\r\n    return event.isEnrichmentInProgress();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Enriched event is currently being enriched';\r\n  }\r\n}\r\n\r\n/**\r\n * Enrichment Partial Specification\r\n * \r\n * Specification for enriched events that have partial enrichment results.\r\n */\r\nexport class EnrichmentPartialSpecification extends EnrichedEventSpecification {\r\n  isSatisfiedBy(event: EnrichedEvent): boolean {\r\n    return event.isEnrichmentPartial();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Enriched event has partial enrichment results';\r\n  }\r\n}\r\n\r\n/**\r\n * High Enrichment Quality Specification\r\n * \r\n * Specification for enriched events with high enrichment quality scores.\r\n */\r\nexport class HighEnrichmentQualitySpecification extends EnrichedEventSpecification {\r\n  isSatisfiedBy(event: EnrichedEvent): boolean {\r\n    return event.hasHighEnrichmentQuality();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Enriched event has high enrichment quality (>= 70)';\r\n  }\r\n}\r\n\r\n/**\r\n * Has Validation Errors Specification\r\n * \r\n * Specification for enriched events that have validation errors.\r\n */\r\nexport class HasValidationErrorsSpecification extends EnrichedEventSpecification {\r\n  isSatisfiedBy(event: EnrichedEvent): boolean {\r\n    return event.hasValidationErrors();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Enriched event has validation errors';\r\n  }\r\n}\r\n\r\n/**\r\n * Requires Manual Review Specification\r\n * \r\n * Specification for enriched events that require manual review.\r\n */\r\nexport class RequiresManualReviewSpecification extends EnrichedEventSpecification {\r\n  isSatisfiedBy(event: EnrichedEvent): boolean {\r\n    return event.requiresManualReview;\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Enriched event requires manual review';\r\n  }\r\n}\r\n\r\n/**\r\n * Ready For Next Stage Specification\r\n * \r\n * Specification for enriched events that are ready for the next processing stage.\r\n */\r\nexport class ReadyForNextStageSpecification extends EnrichedEventSpecification {\r\n  isSatisfiedBy(event: EnrichedEvent): boolean {\r\n    return event.isReadyForNextStage();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Enriched event is ready for next processing stage';\r\n  }\r\n}\r\n\r\n/**\r\n * High Threat Risk Specification\r\n * \r\n * Specification for enriched events with high threat intelligence risk scores.\r\n */\r\nexport class HighThreatRiskSpecification extends EnrichedEventSpecification {\r\n  isSatisfiedBy(event: EnrichedEvent): boolean {\r\n    return event.isHighThreatRisk();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Enriched event has high threat intelligence risk (>= 85)';\r\n  }\r\n}\r\n\r\n/**\r\n * Has Threat Intelligence Specification\r\n * \r\n * Specification for enriched events that have threat intelligence data.\r\n */\r\nexport class HasThreatIntelligenceSpecification extends EnrichedEventSpecification {\r\n  isSatisfiedBy(event: EnrichedEvent): boolean {\r\n    return event.hasThreatIntelligence();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Enriched event has threat intelligence data';\r\n  }\r\n}\r\n\r\n/**\r\n * Has Reputation Data Specification\r\n * \r\n * Specification for enriched events that have reputation data.\r\n */\r\nexport class HasReputationDataSpecification extends EnrichedEventSpecification {\r\n  isSatisfiedBy(event: EnrichedEvent): boolean {\r\n    return event.hasReputationData();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Enriched event has reputation data';\r\n  }\r\n}\r\n\r\n/**\r\n * Has Geolocation Data Specification\r\n * \r\n * Specification for enriched events that have geolocation data.\r\n */\r\nexport class HasGeolocationDataSpecification extends EnrichedEventSpecification {\r\n  isSatisfiedBy(event: EnrichedEvent): boolean {\r\n    return event.hasGeolocationData();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Enriched event has geolocation data';\r\n  }\r\n}\r\n\r\n/**\r\n * Enrichment Status Specification\r\n * \r\n * Specification for enriched events with specific enrichment statuses.\r\n */\r\nexport class EnrichmentStatusSpecification extends EnrichedEventSpecification {\r\n  constructor(private readonly statuses: EnrichmentStatus[]) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: EnrichedEvent): boolean {\r\n    return this.matchesAnyEnrichmentStatus(event, this.statuses);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Enriched event status is one of: ${this.statuses.join(', ')}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Enrichment Quality Score Range Specification\r\n * \r\n * Specification for enriched events within a specific enrichment quality score range.\r\n */\r\nexport class EnrichmentQualityScoreRangeSpecification extends EnrichedEventSpecification {\r\n  constructor(\r\n    private readonly minScore?: number,\r\n    private readonly maxScore?: number\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: EnrichedEvent): boolean {\r\n    return this.isEnrichmentQualityScoreWithinRange(event, this.minScore, this.maxScore);\r\n  }\r\n\r\n  getDescription(): string {\r\n    if (this.minScore !== undefined && this.maxScore !== undefined) {\r\n      return `Enriched event quality score is between ${this.minScore} and ${this.maxScore}`;\r\n    } else if (this.minScore !== undefined) {\r\n      return `Enriched event quality score is at least ${this.minScore}`;\r\n    } else if (this.maxScore !== undefined) {\r\n      return `Enriched event quality score is at most ${this.maxScore}`;\r\n    }\r\n    return 'Enriched event has any quality score';\r\n  }\r\n}\r\n\r\n/**\r\n * Threat Intelligence Score Range Specification\r\n * \r\n * Specification for enriched events within a specific threat intelligence score range.\r\n */\r\nexport class ThreatIntelScoreRangeSpecification extends EnrichedEventSpecification {\r\n  constructor(\r\n    private readonly minScore?: number,\r\n    private readonly maxScore?: number\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: EnrichedEvent): boolean {\r\n    return this.isThreatIntelScoreWithinRange(event, this.minScore, this.maxScore);\r\n  }\r\n\r\n  getDescription(): string {\r\n    if (this.minScore !== undefined && this.maxScore !== undefined) {\r\n      return `Enriched event threat intelligence score is between ${this.minScore} and ${this.maxScore}`;\r\n    } else if (this.minScore !== undefined) {\r\n      return `Enriched event threat intelligence score is at least ${this.minScore}`;\r\n    } else if (this.maxScore !== undefined) {\r\n      return `Enriched event threat intelligence score is at most ${this.maxScore}`;\r\n    }\r\n    return 'Enriched event has any threat intelligence score';\r\n  }\r\n}\r\n\r\n/**\r\n * Applied Rule Specification\r\n * \r\n * Specification for enriched events that have specific rules applied.\r\n */\r\nexport class AppliedRuleSpecification extends EnrichedEventSpecification {\r\n  constructor(private readonly ruleId: string) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: EnrichedEvent): boolean {\r\n    return this.hasAppliedRule(event, this.ruleId);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Enriched event has applied rule: ${this.ruleId}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Normalized Event Specification\r\n * \r\n * Specification for enriched events that reference a specific normalized event.\r\n */\r\nexport class NormalizedEventSpecification extends EnrichedEventSpecification {\r\n  constructor(private readonly normalizedEventId: UniqueEntityId) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: EnrichedEvent): boolean {\r\n    return event.normalizedEventId.equals(this.normalizedEventId);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Enriched event references normalized event: ${this.normalizedEventId.toString()}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Exceeded Max Attempts Specification\r\n * \r\n * Specification for enriched events that have exceeded maximum enrichment attempts.\r\n */\r\nexport class ExceededMaxAttemptsSpecification extends EnrichedEventSpecification {\r\n  isSatisfiedBy(event: EnrichedEvent): boolean {\r\n    return event.hasExceededMaxEnrichmentAttempts();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Enriched event has exceeded maximum enrichment attempts';\r\n  }\r\n}\r\n\r\n/**\r\n * Reviewed Event Specification\r\n * \r\n * Specification for enriched events that have been manually reviewed.\r\n */\r\nexport class ReviewedEventSpecification extends EnrichedEventSpecification {\r\n  isSatisfiedBy(event: EnrichedEvent): boolean {\r\n    return event.reviewedAt !== undefined;\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Enriched event has been manually reviewed';\r\n  }\r\n}\r\n\r\n/**\r\n * Pending Review Specification\r\n * \r\n * Specification for enriched events that are pending manual review.\r\n */\r\nexport class PendingReviewSpecification extends EnrichedEventSpecification {\r\n  isSatisfiedBy(event: EnrichedEvent): boolean {\r\n    return event.requiresManualReview && event.reviewedAt === undefined;\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Enriched event is pending manual review';\r\n  }\r\n}\r\n\r\n/**\r\n * Enrichment Duration Range Specification\r\n * \r\n * Specification for enriched events with enrichment duration within a specific range.\r\n */\r\nexport class EnrichmentDurationRangeSpecification extends EnrichedEventSpecification {\r\n  constructor(\r\n    private readonly minDurationMs?: number,\r\n    private readonly maxDurationMs?: number\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: EnrichedEvent): boolean {\r\n    const duration = event.getEnrichmentDuration();\r\n    \r\n    if (duration === null) {\r\n      return false; // No duration available\r\n    }\r\n    \r\n    if (this.minDurationMs !== undefined && duration < this.minDurationMs) {\r\n      return false;\r\n    }\r\n    \r\n    if (this.maxDurationMs !== undefined && duration > this.maxDurationMs) {\r\n      return false;\r\n    }\r\n    \r\n    return true;\r\n  }\r\n\r\n  getDescription(): string {\r\n    const formatDuration = (ms: number) => `${ms}ms`;\r\n\r\n    if (this.minDurationMs !== undefined && this.maxDurationMs !== undefined) {\r\n      return `Enriched event duration is between ${formatDuration(this.minDurationMs)} and ${formatDuration(this.maxDurationMs)}`;\r\n    } else if (this.minDurationMs !== undefined) {\r\n      return `Enriched event duration is at least ${formatDuration(this.minDurationMs)}`;\r\n    } else if (this.maxDurationMs !== undefined) {\r\n      return `Enriched event duration is at most ${formatDuration(this.maxDurationMs)}`;\r\n    }\r\n    return 'Enriched event has any duration';\r\n  }\r\n}\r\n\r\n/**\r\n * Enrichment Source Specification\r\n * \r\n * Specification for enriched events that have enrichment data from specific sources.\r\n */\r\nexport class EnrichmentSourceSpecification extends EnrichedEventSpecification {\r\n  constructor(private readonly sources: EnrichmentSource[]) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: EnrichedEvent): boolean {\r\n    return this.sources.some(source => this.hasEnrichmentDataFromSource(event, source));\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Enriched event has data from sources: ${this.sources.join(', ')}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Enrichment Data Type Specification\r\n * \r\n * Specification for enriched events that have enrichment data of specific types.\r\n */\r\nexport class EnrichmentDataTypeSpecification extends EnrichedEventSpecification {\r\n  constructor(private readonly types: string[]) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: EnrichedEvent): boolean {\r\n    return this.types.some(type => this.hasEnrichmentDataOfType(event, type));\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Enriched event has data of types: ${this.types.join(', ')}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Average Reputation Score Range Specification\r\n * \r\n * Specification for enriched events with average reputation score within a specific range.\r\n */\r\nexport class AverageReputationScoreRangeSpecification extends EnrichedEventSpecification {\r\n  constructor(\r\n    private readonly minScore?: number,\r\n    private readonly maxScore?: number\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: EnrichedEvent): boolean {\r\n    const avgScore = event.getAverageReputationScore();\r\n    \r\n    if (avgScore === null) {\r\n      return this.minScore === undefined; // If no min score required, null is acceptable\r\n    }\r\n    \r\n    if (this.minScore !== undefined && avgScore < this.minScore) {\r\n      return false;\r\n    }\r\n    \r\n    if (this.maxScore !== undefined && avgScore > this.maxScore) {\r\n      return false;\r\n    }\r\n    \r\n    return true;\r\n  }\r\n\r\n  getDescription(): string {\r\n    if (this.minScore !== undefined && this.maxScore !== undefined) {\r\n      return `Enriched event average reputation score is between ${this.minScore} and ${this.maxScore}`;\r\n    } else if (this.minScore !== undefined) {\r\n      return `Enriched event average reputation score is at least ${this.minScore}`;\r\n    } else if (this.maxScore !== undefined) {\r\n      return `Enriched event average reputation score is at most ${this.maxScore}`;\r\n    }\r\n    return 'Enriched event has any average reputation score';\r\n  }\r\n}\r\n\r\n/**\r\n * Composite EnrichedEvent Specification Builder\r\n * \r\n * Builder for creating complex enriched event specifications using fluent interface.\r\n */\r\nexport class EnrichedEventSpecificationBuilder {\r\n  private specifications: EnrichedEventSpecification[] = [];\r\n\r\n  /**\r\n   * Add enrichment completed filter\r\n   */\r\n  enrichmentCompleted(): EnrichedEventSpecificationBuilder {\r\n    this.specifications.push(new EnrichmentCompletedSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add enrichment failed filter\r\n   */\r\n  enrichmentFailed(): EnrichedEventSpecificationBuilder {\r\n    this.specifications.push(new EnrichmentFailedSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add enrichment in progress filter\r\n   */\r\n  enrichmentInProgress(): EnrichedEventSpecificationBuilder {\r\n    this.specifications.push(new EnrichmentInProgressSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add enrichment partial filter\r\n   */\r\n  enrichmentPartial(): EnrichedEventSpecificationBuilder {\r\n    this.specifications.push(new EnrichmentPartialSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add high enrichment quality filter\r\n   */\r\n  highEnrichmentQuality(): EnrichedEventSpecificationBuilder {\r\n    this.specifications.push(new HighEnrichmentQualitySpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add has validation errors filter\r\n   */\r\n  hasValidationErrors(): EnrichedEventSpecificationBuilder {\r\n    this.specifications.push(new HasValidationErrorsSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add requires manual review filter\r\n   */\r\n  requiresManualReview(): EnrichedEventSpecificationBuilder {\r\n    this.specifications.push(new RequiresManualReviewSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add ready for next stage filter\r\n   */\r\n  readyForNextStage(): EnrichedEventSpecificationBuilder {\r\n    this.specifications.push(new ReadyForNextStageSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add high threat risk filter\r\n   */\r\n  highThreatRisk(): EnrichedEventSpecificationBuilder {\r\n    this.specifications.push(new HighThreatRiskSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add has threat intelligence filter\r\n   */\r\n  hasThreatIntelligence(): EnrichedEventSpecificationBuilder {\r\n    this.specifications.push(new HasThreatIntelligenceSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add has reputation data filter\r\n   */\r\n  hasReputationData(): EnrichedEventSpecificationBuilder {\r\n    this.specifications.push(new HasReputationDataSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add has geolocation data filter\r\n   */\r\n  hasGeolocationData(): EnrichedEventSpecificationBuilder {\r\n    this.specifications.push(new HasGeolocationDataSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add enrichment status filter\r\n   */\r\n  withEnrichmentStatus(...statuses: EnrichmentStatus[]): EnrichedEventSpecificationBuilder {\r\n    this.specifications.push(new EnrichmentStatusSpecification(statuses));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add enrichment quality score range filter\r\n   */\r\n  enrichmentQualityScoreRange(minScore?: number, maxScore?: number): EnrichedEventSpecificationBuilder {\r\n    this.specifications.push(new EnrichmentQualityScoreRangeSpecification(minScore, maxScore));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add threat intelligence score range filter\r\n   */\r\n  threatIntelScoreRange(minScore?: number, maxScore?: number): EnrichedEventSpecificationBuilder {\r\n    this.specifications.push(new ThreatIntelScoreRangeSpecification(minScore, maxScore));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add applied rule filter\r\n   */\r\n  withAppliedRule(ruleId: string): EnrichedEventSpecificationBuilder {\r\n    this.specifications.push(new AppliedRuleSpecification(ruleId));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add normalized event filter\r\n   */\r\n  fromNormalizedEvent(normalizedEventId: UniqueEntityId): EnrichedEventSpecificationBuilder {\r\n    this.specifications.push(new NormalizedEventSpecification(normalizedEventId));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add exceeded max attempts filter\r\n   */\r\n  exceededMaxAttempts(): EnrichedEventSpecificationBuilder {\r\n    this.specifications.push(new ExceededMaxAttemptsSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add reviewed filter\r\n   */\r\n  reviewed(): EnrichedEventSpecificationBuilder {\r\n    this.specifications.push(new ReviewedEventSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add pending review filter\r\n   */\r\n  pendingReview(): EnrichedEventSpecificationBuilder {\r\n    this.specifications.push(new PendingReviewSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add enrichment duration range filter\r\n   */\r\n  enrichmentDurationRange(minDurationMs?: number, maxDurationMs?: number): EnrichedEventSpecificationBuilder {\r\n    this.specifications.push(new EnrichmentDurationRangeSpecification(minDurationMs, maxDurationMs));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add enrichment source filter\r\n   */\r\n  withEnrichmentSources(...sources: EnrichmentSource[]): EnrichedEventSpecificationBuilder {\r\n    this.specifications.push(new EnrichmentSourceSpecification(sources));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add enrichment data type filter\r\n   */\r\n  withEnrichmentDataTypes(...types: string[]): EnrichedEventSpecificationBuilder {\r\n    this.specifications.push(new EnrichmentDataTypeSpecification(types));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add average reputation score range filter\r\n   */\r\n  averageReputationScoreRange(minScore?: number, maxScore?: number): EnrichedEventSpecificationBuilder {\r\n    this.specifications.push(new AverageReputationScoreRangeSpecification(minScore, maxScore));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Build the final specification using AND logic\r\n   */\r\n  build(): EnrichedEventSpecification {\r\n    if (this.specifications.length === 0) {\r\n      throw new Error('At least one specification must be added');\r\n    }\r\n\r\n    if (this.specifications.length === 1) {\r\n      return this.specifications[0];\r\n    }\r\n\r\n    // Combine all specifications with AND logic\r\n    let combined = this.specifications[0];\r\n    for (let i = 1; i < this.specifications.length; i++) {\r\n      combined = combined.and(this.specifications[i]) as EnrichedEventSpecification;\r\n    }\r\n\r\n    return combined;\r\n  }\r\n\r\n  /**\r\n   * Build the final specification using OR logic\r\n   */\r\n  buildWithOr(): EnrichedEventSpecification {\r\n    if (this.specifications.length === 0) {\r\n      throw new Error('At least one specification must be added');\r\n    }\r\n\r\n    if (this.specifications.length === 1) {\r\n      return this.specifications[0];\r\n    }\r\n\r\n    // Combine all specifications with OR logic\r\n    let combined = this.specifications[0];\r\n    for (let i = 1; i < this.specifications.length; i++) {\r\n      combined = combined.or(this.specifications[i]) as EnrichedEventSpecification;\r\n    }\r\n\r\n    return combined;\r\n  }\r\n\r\n  /**\r\n   * Create a new builder instance\r\n   */\r\n  static create(): EnrichedEventSpecificationBuilder {\r\n    return new EnrichedEventSpecificationBuilder();\r\n  }\r\n}"], "version": 3}