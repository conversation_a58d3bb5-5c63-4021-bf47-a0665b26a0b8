{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\domain\\base-specification.spec.ts", "mappings": ";;AAAA,wEAMyC;AAWzC,qCAAqC;AACrC,MAAM,iBAAkB,SAAQ,sCAA6B;IAC3D,YAA6B,YAAoB;QAC/C,KAAK,EAAE,CAAC;QADmB,iBAAY,GAAZ,YAAY,CAAQ;IAEjD,CAAC;IAED,aAAa,CAAC,MAAkB;QAC9B,OAAO,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,YAAY,CAAC;IAC3C,CAAC;IAED,cAAc;QACZ,OAAO,gBAAgB,IAAI,CAAC,YAAY,GAAG,CAAC;IAC9C,CAAC;CACF;AAED,MAAM,qBAAsB,SAAQ,sCAA6B;IAC/D,YAA6B,MAAc,EAAmB,MAAc;QAC1E,KAAK,EAAE,CAAC;QADmB,WAAM,GAAN,MAAM,CAAQ;QAAmB,WAAM,GAAN,MAAM,CAAQ;IAE5E,CAAC;IAED,aAAa,CAAC,MAAkB;QAC9B,OAAO,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC;IAChE,CAAC;IAED,cAAc;QACZ,OAAO,eAAe,IAAI,CAAC,MAAM,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;IACzD,CAAC;CACF;AAED,MAAM,mBAAoB,SAAQ,sCAA6B;IAC7D,YAA6B,cAAqC;QAChE,KAAK,EAAE,CAAC;QADmB,mBAAc,GAAd,cAAc,CAAuB;IAElE,CAAC;IAED,aAAa,CAAC,MAAkB;QAC9B,OAAO,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC,cAAc,CAAC;IAC/C,CAAC;IAED,cAAc;QACZ,OAAO,cAAc,IAAI,CAAC,cAAc,GAAG,CAAC;IAC9C,CAAC;CACF;AAED,MAAM,mBAAoB,SAAQ,sCAA6B;IAC7D,YAA6B,GAAW;QACtC,KAAK,EAAE,CAAC;QADmB,QAAG,GAAH,GAAG,CAAQ;IAExC,CAAC;IAED,aAAa,CAAC,MAAkB;QAC9B,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACxC,CAAC;IAED,cAAc;QACZ,OAAO,YAAY,IAAI,CAAC,GAAG,GAAG,CAAC;IACjC,CAAC;CACF;AAED,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;IACjC,IAAI,UAAsB,CAAC;IAE3B,UAAU,CAAC,GAAG,EAAE;QACd,UAAU,GAAG;YACX,EAAE,EAAE,GAAG;YACP,IAAI,EAAE,UAAU;YAChB,GAAG,EAAE,EAAE;YACP,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,CAAC,WAAW,EAAE,QAAQ,CAAC;SAC9B,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mCAAmC,EAAE,GAAG,EAAE;QACjD,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,IAAI,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAE/C,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,IAAI,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAE/C,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,IAAI,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAE/C,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,IAAI,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAE/C,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,QAAQ,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACnD,MAAM,OAAO,GAAG,IAAI,qBAAqB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAClD,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAEtC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;YACtE,MAAM,QAAQ,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACnD,MAAM,OAAO,GAAG,IAAI,qBAAqB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAClD,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAEtC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,MAAM,QAAQ,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACnD,MAAM,OAAO,GAAG,IAAI,qBAAqB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAClD,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAEtC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,MAAM,QAAQ,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACnD,MAAM,OAAO,GAAG,IAAI,qBAAqB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAClD,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAEtC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,QAAQ,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACnD,MAAM,OAAO,GAAG,IAAI,qBAAqB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAClD,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAEtC,MAAM,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QAChG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,QAAQ,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACnD,MAAM,OAAO,GAAG,IAAI,qBAAqB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAClD,MAAM,MAAM,GAAG,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;YAEpC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,QAAQ,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACnD,MAAM,OAAO,GAAG,IAAI,qBAAqB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAClD,MAAM,MAAM,GAAG,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;YAEpC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,QAAQ,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACnD,MAAM,OAAO,GAAG,IAAI,qBAAqB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAClD,MAAM,MAAM,GAAG,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;YAEpC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,MAAM,QAAQ,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACnD,MAAM,OAAO,GAAG,IAAI,qBAAqB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAClD,MAAM,MAAM,GAAG,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;YAEpC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,QAAQ,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACnD,MAAM,OAAO,GAAG,IAAI,qBAAqB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAClD,MAAM,MAAM,GAAG,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;YAEpC,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QAC9F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,QAAQ,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACnD,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC;YAE/B,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,QAAQ,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACnD,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC;YAE/B,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,QAAQ,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACnD,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC;YAE/B,MAAM,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE;QAClD,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,QAAQ,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACnD,MAAM,OAAO,GAAG,IAAI,qBAAqB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAClD,MAAM,UAAU,GAAG,IAAI,mBAAmB,CAAC,QAAQ,CAAC,CAAC;YAErD,yDAAyD;YACzD,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;YAEzD,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,QAAQ,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACnD,MAAM,UAAU,GAAG,IAAI,mBAAmB,CAAC,UAAU,CAAC,CAAC;YAEvD,iDAAiD;YACjD,MAAM,WAAW,GAAG,QAAQ,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,GAAG,EAAE,CAAC;YAElD,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,MAAM,QAAQ,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACnD,MAAM,OAAO,GAAG,IAAI,qBAAqB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAClD,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;YAEhD,MAAM,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;QAC1G,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,qEAAqE,EAAE,GAAG,EAAE;YAC7E,MAAM,KAAK,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAChD,MAAM,KAAK,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAEhD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8EAA8E,EAAE,GAAG,EAAE;YACtF,MAAM,KAAK,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAChD,MAAM,KAAK,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAEhD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,QAAQ,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACnD,MAAM,OAAO,GAAG,IAAI,qBAAqB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAElD,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,IAAI,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAC/C,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YAE3B,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBACnB,IAAI,EAAE,mBAAmB;gBACzB,WAAW,EAAE,wBAAwB;aACtC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,QAAQ,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACnD,MAAM,OAAO,GAAG,IAAI,qBAAqB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAClD,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACtC,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;YAE9B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBACxB,IAAI,EAAE,mBAAmB;gBACzB,WAAW,EAAE,wBAAwB;aACtC,CAAC,CAAC;YACH,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC;gBACzB,IAAI,EAAE,uBAAuB;gBAC7B,WAAW,EAAE,uBAAuB;aACrC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,IAAI,GAAG,IAAI,4CAAuB,EAAc,CAAC;YAEvD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,EAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,IAAI,GAAG,IAAI,4CAAuB,EAAc,CAAC;YAEvD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,IAAI,GAAG,IAAI,6CAAwB,EAAc,CAAC;YAExD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,EAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,IAAI,GAAG,IAAI,6CAAwB,EAAc,CAAC;YAExD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,IAAI,GAAG,yCAAoB;iBAC9B,UAAU,EAAc;iBACxB,GAAG,CAAC,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;iBACtC,KAAK,EAAE,CAAC;YAEX,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,IAAI,GAAG,yCAAoB;iBAC9B,WAAW,EAAc;iBACzB,EAAE,CAAC,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;iBACrC,KAAK,EAAE,CAAC;YAEX,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,IAAI,GAAG,yCAAoB;iBAC9B,MAAM,CAAC,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;iBACzC,GAAG,CAAC,IAAI,qBAAqB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;iBACtC,EAAE,CAAC,IAAI,mBAAmB,CAAC,QAAQ,CAAC,CAAC;iBACrC,GAAG,EAAE;iBACL,KAAK,EAAE,CAAC;YAEX,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,IAAI,GAAG,yCAAoB;iBAC9B,MAAM,CAAC,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;iBACzC,EAAE,CAAC,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;iBACrC,GAAG,CAAC,IAAI,mBAAmB,CAAC,QAAQ,CAAC,CAAC;iBACtC,KAAK,EAAE,CAAC;YAEX,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,IAAI,cAA+C,CAAC;QAEpD,UAAU,CAAC,GAAG,EAAE;YACd,cAAc,GAAG;gBACf,IAAI,iBAAiB,CAAC,UAAU,CAAC;gBACjC,IAAI,qBAAqB,CAAC,EAAE,EAAE,EAAE,CAAC;gBACjC,IAAI,mBAAmB,CAAC,QAAQ,CAAC;gBACjC,IAAI,mBAAmB,CAAC,WAAW,CAAC;aACrC,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,OAAO,GAAG,uCAAkB,CAAC,WAAW,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;YAE3E,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC;gBACtB,iBAAiB,EAAE,IAAI;gBACvB,qBAAqB,EAAE,IAAI;gBAC3B,mBAAmB,EAAE,IAAI;gBACzB,mBAAmB,EAAE,IAAI;aAC1B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,SAAS,GAAG,uCAAkB,CAAC,aAAa,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;YAE/E,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,gBAAgB,GAAG;gBACvB,IAAI,iBAAiB,CAAC,UAAU,CAAC;gBACjC,IAAI,qBAAqB,CAAC,EAAE,EAAE,EAAE,CAAC;gBACjC,IAAI,mBAAmB,CAAC,UAAU,CAAC;aACpC,CAAC;YAEF,MAAM,WAAW,GAAG,uCAAkB,CAAC,eAAe,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;YAErF,MAAM,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,YAAY,GAAG,uCAAkB,CAAC,YAAY,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;YACjF,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEhC,MAAM,UAAU,GAAG;gBACjB,GAAG,cAAc;gBACjB,IAAI,iBAAiB,CAAC,UAAU,CAAC;aAClC,CAAC;YACF,MAAM,eAAe,GAAG,uCAAkB,CAAC,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;YAChF,MAAM,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,YAAY,GAAG,uCAAkB,CAAC,YAAY,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;YACjF,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEhC,MAAM,SAAS,GAAG;gBAChB,IAAI,iBAAiB,CAAC,UAAU,CAAC;gBACjC,IAAI,qBAAqB,CAAC,EAAE,EAAE,EAAE,CAAC;aAClC,CAAC;YACF,MAAM,aAAa,GAAG,uCAAkB,CAAC,YAAY,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YAC7E,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,CAAC,uCAAkB,CAAC,WAAW,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACnE,MAAM,CAAC,uCAAkB,CAAC,aAAa,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACrE,MAAM,CAAC,uCAAkB,CAAC,eAAe,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACvE,MAAM,CAAC,uCAAkB,CAAC,YAAY,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnE,MAAM,CAAC,uCAAkB,CAAC,YAAY,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,6DAA6D;YAC7D,MAAM,yBAA0B,SAAQ,sCAA6B;gBACnE,YAA6B,YAAoB;oBAC/C,KAAK,EAAE,CAAC;oBADmB,iBAAY,GAAZ,YAAY,CAAQ;gBAEjD,CAAC;gBAED,aAAa,CAAC,MAAkB;oBAC9B,OAAO,MAAM,EAAE,IAAI,KAAK,IAAI,CAAC,YAAY,CAAC;gBAC5C,CAAC;gBAED,cAAc;oBACZ,OAAO,gBAAgB,IAAI,CAAC,YAAY,GAAG,CAAC;gBAC9C,CAAC;aACF;YAED,MAAM,IAAI,GAAG,IAAI,yBAAyB,CAAC,UAAU,CAAC,CAAC;YAEvD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,IAAW,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAC5D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,IAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,kEAAkE;YAClE,MAAM,yBAA0B,SAAQ,sCAA6B;gBACnE,YAA6B,YAAoB;oBAC/C,KAAK,EAAE,CAAC;oBADmB,iBAAY,GAAZ,YAAY,CAAQ;gBAEjD,CAAC;gBAED,aAAa,CAAC,MAAkB;oBAC9B,OAAO,MAAM,EAAE,IAAI,KAAK,IAAI,CAAC,YAAY,CAAC;gBAC5C,CAAC;gBAED,cAAc;oBACZ,OAAO,gBAAgB,IAAI,CAAC,YAAY,GAAG,CAAC;gBAC9C,CAAC;aACF;YAED,MAAM,IAAI,GAAG,IAAI,yBAAyB,CAAC,UAAU,CAAC,CAAC;YAEvD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,SAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YACjE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,SAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,gBAAgB,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAgB,CAAC;YACjE,MAAM,OAAO,GAAG,IAAI,qBAAqB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAElD,MAAM,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YACpE,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,IAAI,IAAI,GAAkC,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAE5E,qCAAqC;YACrC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC5B,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC;YACrD,CAAC;YAED,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sEAAsE,EAAE,GAAG,EAAE;YAC9E,MAAM,eAAgB,SAAQ,sCAA6B;gBACzD,aAAa,CAAC,MAAkB;oBAC9B,OAAO,IAAI,CAAC;gBACd,CAAC;gBAED,cAAc;oBACZ,OAAO,iCAAiC,CAAC;gBAC3C,CAAC;aACF;YAED,MAAM,IAAI,GAAG,IAAI,eAAe,EAAE,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YACtE,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\domain\\base-specification.spec.ts"], "sourcesContent": ["import { \r\n  BaseSpecification, \r\n  AlwaysTrueSpecification, \r\n  AlwaysFalseSpecification,\r\n  SpecificationBuilder,\r\n  SpecificationUtils\r\n} from '../../domain/base-specification';\r\n\r\n// Test entity for specifications\r\ninterface TestEntity {\r\n  id: string;\r\n  name: string;\r\n  age: number;\r\n  status: 'active' | 'inactive';\r\n  tags: string[];\r\n}\r\n\r\n// Test specification implementations\r\nclass NameSpecification extends BaseSpecification<TestEntity> {\r\n  constructor(private readonly expectedName: string) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(entity: TestEntity): boolean {\r\n    return entity.name === this.expectedName;\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Name equals '${this.expectedName}'`;\r\n  }\r\n}\r\n\r\nclass AgeRangeSpecification extends BaseSpecification<TestEntity> {\r\n  constructor(private readonly minAge: number, private readonly maxAge: number) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(entity: TestEntity): boolean {\r\n    return entity.age >= this.minAge && entity.age <= this.maxAge;\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Age between ${this.minAge} and ${this.maxAge}`;\r\n  }\r\n}\r\n\r\nclass StatusSpecification extends BaseSpecification<TestEntity> {\r\n  constructor(private readonly expectedStatus: 'active' | 'inactive') {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(entity: TestEntity): boolean {\r\n    return entity.status === this.expectedStatus;\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Status is '${this.expectedStatus}'`;\r\n  }\r\n}\r\n\r\nclass HasTagSpecification extends BaseSpecification<TestEntity> {\r\n  constructor(private readonly tag: string) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(entity: TestEntity): boolean {\r\n    return entity.tags.includes(this.tag);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Has tag '${this.tag}'`;\r\n  }\r\n}\r\n\r\ndescribe('BaseSpecification', () => {\r\n  let testEntity: TestEntity;\r\n\r\n  beforeEach(() => {\r\n    testEntity = {\r\n      id: '1',\r\n      name: 'John Doe',\r\n      age: 30,\r\n      status: 'active',\r\n      tags: ['developer', 'senior']\r\n    };\r\n  });\r\n\r\n  describe('basic specification functionality', () => {\r\n    it('should satisfy specification when condition is met', () => {\r\n      const spec = new NameSpecification('John Doe');\r\n\r\n      expect(spec.isSatisfiedBy(testEntity)).toBe(true);\r\n    });\r\n\r\n    it('should not satisfy specification when condition is not met', () => {\r\n      const spec = new NameSpecification('Jane Doe');\r\n\r\n      expect(spec.isSatisfiedBy(testEntity)).toBe(false);\r\n    });\r\n\r\n    it('should provide description', () => {\r\n      const spec = new NameSpecification('John Doe');\r\n\r\n      expect(spec.getDescription()).toBe(\"Name equals 'John Doe'\");\r\n    });\r\n\r\n    it('should provide string representation', () => {\r\n      const spec = new NameSpecification('John Doe');\r\n\r\n      expect(spec.toString()).toBe(\"Name equals 'John Doe'\");\r\n    });\r\n  });\r\n\r\n  describe('AND specification', () => {\r\n    it('should satisfy when both specifications are satisfied', () => {\r\n      const nameSpec = new NameSpecification('John Doe');\r\n      const ageSpec = new AgeRangeSpecification(25, 35);\r\n      const andSpec = nameSpec.and(ageSpec);\r\n\r\n      expect(andSpec.isSatisfiedBy(testEntity)).toBe(true);\r\n    });\r\n\r\n    it('should not satisfy when first specification is not satisfied', () => {\r\n      const nameSpec = new NameSpecification('Jane Doe');\r\n      const ageSpec = new AgeRangeSpecification(25, 35);\r\n      const andSpec = nameSpec.and(ageSpec);\r\n\r\n      expect(andSpec.isSatisfiedBy(testEntity)).toBe(false);\r\n    });\r\n\r\n    it('should not satisfy when second specification is not satisfied', () => {\r\n      const nameSpec = new NameSpecification('John Doe');\r\n      const ageSpec = new AgeRangeSpecification(35, 40);\r\n      const andSpec = nameSpec.and(ageSpec);\r\n\r\n      expect(andSpec.isSatisfiedBy(testEntity)).toBe(false);\r\n    });\r\n\r\n    it('should not satisfy when both specifications are not satisfied', () => {\r\n      const nameSpec = new NameSpecification('Jane Doe');\r\n      const ageSpec = new AgeRangeSpecification(35, 40);\r\n      const andSpec = nameSpec.and(ageSpec);\r\n\r\n      expect(andSpec.isSatisfiedBy(testEntity)).toBe(false);\r\n    });\r\n\r\n    it('should provide correct description for AND specification', () => {\r\n      const nameSpec = new NameSpecification('John Doe');\r\n      const ageSpec = new AgeRangeSpecification(25, 35);\r\n      const andSpec = nameSpec.and(ageSpec);\r\n\r\n      expect(andSpec.getDescription()).toBe(\"(Name equals 'John Doe') AND (Age between 25 and 35)\");\r\n    });\r\n  });\r\n\r\n  describe('OR specification', () => {\r\n    it('should satisfy when first specification is satisfied', () => {\r\n      const nameSpec = new NameSpecification('John Doe');\r\n      const ageSpec = new AgeRangeSpecification(35, 40);\r\n      const orSpec = nameSpec.or(ageSpec);\r\n\r\n      expect(orSpec.isSatisfiedBy(testEntity)).toBe(true);\r\n    });\r\n\r\n    it('should satisfy when second specification is satisfied', () => {\r\n      const nameSpec = new NameSpecification('Jane Doe');\r\n      const ageSpec = new AgeRangeSpecification(25, 35);\r\n      const orSpec = nameSpec.or(ageSpec);\r\n\r\n      expect(orSpec.isSatisfiedBy(testEntity)).toBe(true);\r\n    });\r\n\r\n    it('should satisfy when both specifications are satisfied', () => {\r\n      const nameSpec = new NameSpecification('John Doe');\r\n      const ageSpec = new AgeRangeSpecification(25, 35);\r\n      const orSpec = nameSpec.or(ageSpec);\r\n\r\n      expect(orSpec.isSatisfiedBy(testEntity)).toBe(true);\r\n    });\r\n\r\n    it('should not satisfy when both specifications are not satisfied', () => {\r\n      const nameSpec = new NameSpecification('Jane Doe');\r\n      const ageSpec = new AgeRangeSpecification(35, 40);\r\n      const orSpec = nameSpec.or(ageSpec);\r\n\r\n      expect(orSpec.isSatisfiedBy(testEntity)).toBe(false);\r\n    });\r\n\r\n    it('should provide correct description for OR specification', () => {\r\n      const nameSpec = new NameSpecification('John Doe');\r\n      const ageSpec = new AgeRangeSpecification(25, 35);\r\n      const orSpec = nameSpec.or(ageSpec);\r\n\r\n      expect(orSpec.getDescription()).toBe(\"(Name equals 'John Doe') OR (Age between 25 and 35)\");\r\n    });\r\n  });\r\n\r\n  describe('NOT specification', () => {\r\n    it('should satisfy when original specification is not satisfied', () => {\r\n      const nameSpec = new NameSpecification('Jane Doe');\r\n      const notSpec = nameSpec.not();\r\n\r\n      expect(notSpec.isSatisfiedBy(testEntity)).toBe(true);\r\n    });\r\n\r\n    it('should not satisfy when original specification is satisfied', () => {\r\n      const nameSpec = new NameSpecification('John Doe');\r\n      const notSpec = nameSpec.not();\r\n\r\n      expect(notSpec.isSatisfiedBy(testEntity)).toBe(false);\r\n    });\r\n\r\n    it('should provide correct description for NOT specification', () => {\r\n      const nameSpec = new NameSpecification('John Doe');\r\n      const notSpec = nameSpec.not();\r\n\r\n      expect(notSpec.getDescription()).toBe(\"NOT (Name equals 'John Doe')\");\r\n    });\r\n  });\r\n\r\n  describe('complex specification combinations', () => {\r\n    it('should handle complex AND/OR combinations', () => {\r\n      const nameSpec = new NameSpecification('John Doe');\r\n      const ageSpec = new AgeRangeSpecification(25, 35);\r\n      const statusSpec = new StatusSpecification('active');\r\n      \r\n      // (Name = 'John Doe' AND Age 25-35) OR Status = 'active'\r\n      const complexSpec = nameSpec.and(ageSpec).or(statusSpec);\r\n\r\n      expect(complexSpec.isSatisfiedBy(testEntity)).toBe(true);\r\n    });\r\n\r\n    it('should handle NOT with AND/OR combinations', () => {\r\n      const nameSpec = new NameSpecification('Jane Doe');\r\n      const statusSpec = new StatusSpecification('inactive');\r\n      \r\n      // NOT (Name = 'Jane Doe' OR Status = 'inactive')\r\n      const complexSpec = nameSpec.or(statusSpec).not();\r\n\r\n      expect(complexSpec.isSatisfiedBy(testEntity)).toBe(true);\r\n    });\r\n\r\n    it('should provide correct description for complex specifications', () => {\r\n      const nameSpec = new NameSpecification('John Doe');\r\n      const ageSpec = new AgeRangeSpecification(25, 35);\r\n      const complexSpec = nameSpec.and(ageSpec).not();\r\n\r\n      expect(complexSpec.getDescription()).toBe(\"NOT ((Name equals 'John Doe') AND (Age between 25 and 35))\");\r\n    });\r\n  });\r\n\r\n  describe('specification equality', () => {\r\n    it('should be equal to specification of same type with same description', () => {\r\n      const spec1 = new NameSpecification('John Doe');\r\n      const spec2 = new NameSpecification('John Doe');\r\n\r\n      expect(spec1.equals(spec2)).toBe(true);\r\n    });\r\n\r\n    it('should not be equal to specification of same type with different description', () => {\r\n      const spec1 = new NameSpecification('John Doe');\r\n      const spec2 = new NameSpecification('Jane Doe');\r\n\r\n      expect(spec1.equals(spec2)).toBe(false);\r\n    });\r\n\r\n    it('should not be equal to specification of different type', () => {\r\n      const nameSpec = new NameSpecification('John Doe');\r\n      const ageSpec = new AgeRangeSpecification(25, 35);\r\n\r\n      expect(nameSpec.equals(ageSpec)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('JSON serialization', () => {\r\n    it('should serialize to JSON', () => {\r\n      const spec = new NameSpecification('John Doe');\r\n      const json = spec.toJSON();\r\n\r\n      expect(json).toEqual({\r\n        type: 'NameSpecification',\r\n        description: \"Name equals 'John Doe'\"\r\n      });\r\n    });\r\n\r\n    it('should serialize complex specifications to JSON', () => {\r\n      const nameSpec = new NameSpecification('John Doe');\r\n      const ageSpec = new AgeRangeSpecification(25, 35);\r\n      const andSpec = nameSpec.and(ageSpec);\r\n      const json = andSpec.toJSON();\r\n\r\n      expect(json.type).toBe('AndSpecification');\r\n      expect(json.left).toEqual({\r\n        type: 'NameSpecification',\r\n        description: \"Name equals 'John Doe'\"\r\n      });\r\n      expect(json.right).toEqual({\r\n        type: 'AgeRangeSpecification',\r\n        description: 'Age between 25 and 35'\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('AlwaysTrueSpecification', () => {\r\n    it('should always be satisfied', () => {\r\n      const spec = new AlwaysTrueSpecification<TestEntity>();\r\n\r\n      expect(spec.isSatisfiedBy(testEntity)).toBe(true);\r\n      expect(spec.isSatisfiedBy({} as TestEntity)).toBe(true);\r\n    });\r\n\r\n    it('should provide correct description', () => {\r\n      const spec = new AlwaysTrueSpecification<TestEntity>();\r\n\r\n      expect(spec.getDescription()).toBe('Always True');\r\n    });\r\n  });\r\n\r\n  describe('AlwaysFalseSpecification', () => {\r\n    it('should never be satisfied', () => {\r\n      const spec = new AlwaysFalseSpecification<TestEntity>();\r\n\r\n      expect(spec.isSatisfiedBy(testEntity)).toBe(false);\r\n      expect(spec.isSatisfiedBy({} as TestEntity)).toBe(false);\r\n    });\r\n\r\n    it('should provide correct description', () => {\r\n      const spec = new AlwaysFalseSpecification<TestEntity>();\r\n\r\n      expect(spec.getDescription()).toBe('Always False');\r\n    });\r\n  });\r\n\r\n  describe('SpecificationBuilder', () => {\r\n    it('should build specification starting with always true', () => {\r\n      const spec = SpecificationBuilder\r\n        .alwaysTrue<TestEntity>()\r\n        .and(new NameSpecification('John Doe'))\r\n        .build();\r\n\r\n      expect(spec.isSatisfiedBy(testEntity)).toBe(true);\r\n    });\r\n\r\n    it('should build specification starting with always false', () => {\r\n      const spec = SpecificationBuilder\r\n        .alwaysFalse<TestEntity>()\r\n        .or(new NameSpecification('John Doe'))\r\n        .build();\r\n\r\n      expect(spec.isSatisfiedBy(testEntity)).toBe(true);\r\n    });\r\n\r\n    it('should build complex specification with fluent interface', () => {\r\n      const spec = SpecificationBuilder\r\n        .create(new NameSpecification('John Doe'))\r\n        .and(new AgeRangeSpecification(25, 35))\r\n        .or(new StatusSpecification('active'))\r\n        .not()\r\n        .build();\r\n\r\n      expect(spec.isSatisfiedBy(testEntity)).toBe(false);\r\n    });\r\n\r\n    it('should chain multiple operations', () => {\r\n      const spec = SpecificationBuilder\r\n        .create(new NameSpecification('Jane Doe'))\r\n        .or(new NameSpecification('John Doe'))\r\n        .and(new StatusSpecification('active'))\r\n        .build();\r\n\r\n      expect(spec.isSatisfiedBy(testEntity)).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('SpecificationUtils', () => {\r\n    let specifications: BaseSpecification<TestEntity>[];\r\n\r\n    beforeEach(() => {\r\n      specifications = [\r\n        new NameSpecification('John Doe'),\r\n        new AgeRangeSpecification(25, 35),\r\n        new StatusSpecification('active'),\r\n        new HasTagSpecification('developer')\r\n      ];\r\n    });\r\n\r\n    it('should evaluate all specifications', () => {\r\n      const results = SpecificationUtils.evaluateAll(testEntity, specifications);\r\n\r\n      expect(results).toEqual({\r\n        NameSpecification: true,\r\n        AgeRangeSpecification: true,\r\n        StatusSpecification: true,\r\n        HasTagSpecification: true\r\n      });\r\n    });\r\n\r\n    it('should find satisfied specifications', () => {\r\n      const satisfied = SpecificationUtils.findSatisfied(testEntity, specifications);\r\n\r\n      expect(satisfied).toHaveLength(4);\r\n      expect(satisfied).toEqual(specifications);\r\n    });\r\n\r\n    it('should find unsatisfied specifications', () => {\r\n      const unsatisfiedSpecs = [\r\n        new NameSpecification('Jane Doe'),\r\n        new AgeRangeSpecification(35, 40),\r\n        new StatusSpecification('inactive')\r\n      ];\r\n\r\n      const unsatisfied = SpecificationUtils.findUnsatisfied(testEntity, unsatisfiedSpecs);\r\n\r\n      expect(unsatisfied).toHaveLength(3);\r\n      expect(unsatisfied).toEqual(unsatisfiedSpecs);\r\n    });\r\n\r\n    it('should check if all specifications are satisfied', () => {\r\n      const allSatisfied = SpecificationUtils.allSatisfied(testEntity, specifications);\r\n      expect(allSatisfied).toBe(true);\r\n\r\n      const mixedSpecs = [\r\n        ...specifications,\r\n        new NameSpecification('Jane Doe')\r\n      ];\r\n      const notAllSatisfied = SpecificationUtils.allSatisfied(testEntity, mixedSpecs);\r\n      expect(notAllSatisfied).toBe(false);\r\n    });\r\n\r\n    it('should check if any specification is satisfied', () => {\r\n      const anySatisfied = SpecificationUtils.anySatisfied(testEntity, specifications);\r\n      expect(anySatisfied).toBe(true);\r\n\r\n      const noneSpecs = [\r\n        new NameSpecification('Jane Doe'),\r\n        new AgeRangeSpecification(35, 40)\r\n      ];\r\n      const noneSatisfied = SpecificationUtils.anySatisfied(testEntity, noneSpecs);\r\n      expect(noneSatisfied).toBe(false);\r\n    });\r\n\r\n    it('should handle empty specification arrays', () => {\r\n      expect(SpecificationUtils.evaluateAll(testEntity, [])).toEqual({});\r\n      expect(SpecificationUtils.findSatisfied(testEntity, [])).toEqual([]);\r\n      expect(SpecificationUtils.findUnsatisfied(testEntity, [])).toEqual([]);\r\n      expect(SpecificationUtils.allSatisfied(testEntity, [])).toBe(true);\r\n      expect(SpecificationUtils.anySatisfied(testEntity, [])).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('edge cases', () => {\r\n    it('should handle null entity gracefully', () => {\r\n      // Create a specification that handles null entities properly\r\n      class NullSafeNameSpecification extends BaseSpecification<TestEntity> {\r\n        constructor(private readonly expectedName: string) {\r\n          super();\r\n        }\r\n\r\n        isSatisfiedBy(entity: TestEntity): boolean {\r\n          return entity?.name === this.expectedName;\r\n        }\r\n\r\n        getDescription(): string {\r\n          return `Name equals '${this.expectedName}'`;\r\n        }\r\n      }\r\n\r\n      const spec = new NullSafeNameSpecification('John Doe');\r\n\r\n      expect(() => spec.isSatisfiedBy(null as any)).not.toThrow();\r\n      expect(spec.isSatisfiedBy(null as any)).toBe(false);\r\n    });\r\n\r\n    it('should handle undefined entity gracefully', () => {\r\n      // Create a specification that handles undefined entities properly\r\n      class NullSafeNameSpecification extends BaseSpecification<TestEntity> {\r\n        constructor(private readonly expectedName: string) {\r\n          super();\r\n        }\r\n\r\n        isSatisfiedBy(entity: TestEntity): boolean {\r\n          return entity?.name === this.expectedName;\r\n        }\r\n\r\n        getDescription(): string {\r\n          return `Name equals '${this.expectedName}'`;\r\n        }\r\n      }\r\n\r\n      const spec = new NullSafeNameSpecification('John Doe');\r\n\r\n      expect(() => spec.isSatisfiedBy(undefined as any)).not.toThrow();\r\n      expect(spec.isSatisfiedBy(undefined as any)).toBe(false);\r\n    });\r\n\r\n    it('should handle entity with missing properties', () => {\r\n      const incompleteEntity = { id: '1', name: 'John' } as TestEntity;\r\n      const ageSpec = new AgeRangeSpecification(25, 35);\r\n\r\n      expect(() => ageSpec.isSatisfiedBy(incompleteEntity)).not.toThrow();\r\n      expect(ageSpec.isSatisfiedBy(incompleteEntity)).toBe(false);\r\n    });\r\n\r\n    it('should handle deeply nested specifications', () => {\r\n      let spec: BaseSpecification<TestEntity> = new NameSpecification('John Doe');\r\n      \r\n      // Create deeply nested specification\r\n      for (let i = 0; i < 10; i++) {\r\n        spec = spec.and(new StatusSpecification('active'));\r\n      }\r\n\r\n      expect(spec.isSatisfiedBy(testEntity)).toBe(true);\r\n      expect(spec.getDescription()).toContain('AND');\r\n    });\r\n\r\n    it('should handle specifications with special characters in descriptions', () => {\r\n      class SpecialCharSpec extends BaseSpecification<TestEntity> {\r\n        isSatisfiedBy(entity: TestEntity): boolean {\r\n          return true;\r\n        }\r\n\r\n        getDescription(): string {\r\n          return 'Special chars: ()[]{}|&*+?^$\\\\.';\r\n        }\r\n      }\r\n\r\n      const spec = new SpecialCharSpec();\r\n      expect(spec.getDescription()).toBe('Special chars: ()[]{}|&*+?^$\\\\.');\r\n      expect(spec.toString()).toBe('Special chars: ()[]{}|&*+?^$\\\\.');\r\n    });\r\n  });\r\n});"], "version": 3}