3e6cff1254a160de0c5aa15265647d99
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const request = __importStar(require("supertest"));
const common_1 = require("@nestjs/common");
const rate_limit_guard_1 = require("../common/guards/rate-limit.guard");
const rate_limit_decorator_1 = require("../common/decorators/rate-limit.decorator");
const throttler_1 = require("@nestjs/throttler");
// Test controllers with different rate limiting configurations
let RateLimitTestController = class RateLimitTestController {
    getDefault() {
        return { message: 'Default rate limit endpoint' };
    }
    getStrict() {
        return { message: 'Strict rate limit endpoint' };
    }
    getLenient() {
        return { message: 'Lenient rate limit endpoint' };
    }
    postCreate() {
        return { message: 'Create endpoint with rate limit' };
    }
    getPerUser() {
        return { message: 'Per-user rate limit endpoint' };
    }
    getNoLimit() {
        return { message: 'No rate limit endpoint' };
    }
};
__decorate([
    (0, common_1.Get)('default'),
    (0, common_1.UseGuards)(rate_limit_guard_1.RateLimitGuard),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], RateLimitTestController.prototype, "getDefault", null);
__decorate([
    (0, common_1.Get)('strict'),
    (0, common_1.UseGuards)(rate_limit_guard_1.RateLimitGuard),
    (0, rate_limit_decorator_1.RateLimit)({ limit: 3, windowMs: 60000 }) // 3 requests per minute
    ,
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], RateLimitTestController.prototype, "getStrict", null);
__decorate([
    (0, common_1.Get)('lenient'),
    (0, common_1.UseGuards)(rate_limit_guard_1.RateLimitGuard),
    (0, rate_limit_decorator_1.RateLimit)({ limit: 100, windowMs: 60000 }) // 100 requests per minute
    ,
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], RateLimitTestController.prototype, "getLenient", null);
__decorate([
    (0, common_1.Post)('create'),
    (0, common_1.UseGuards)(rate_limit_guard_1.RateLimitGuard),
    (0, rate_limit_decorator_1.RateLimit)({ limit: 5, windowMs: 300000 }) // 5 requests per 5 minutes
    ,
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], RateLimitTestController.prototype, "postCreate", null);
__decorate([
    (0, common_1.Get)('per-user'),
    (0, common_1.UseGuards)(rate_limit_guard_1.RateLimitGuard),
    (0, rate_limit_decorator_1.RateLimit)({
        limit: 10,
        windowMs: 60000,
        keyGenerator: (req) => `user:${req.user?.id || req.ip}`
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], RateLimitTestController.prototype, "getPerUser", null);
__decorate([
    (0, common_1.Get)('no-limit'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], RateLimitTestController.prototype, "getNoLimit", null);
RateLimitTestController = __decorate([
    (0, common_1.Controller)('rate-limit-test')
], RateLimitTestController);
// Mock rate limit guard
class MockRateLimitGuard {
    canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const handler = context.getHandler();
        const rateLimitConfig = Reflect.getMetadata('rateLimit', handler);
        if (!rateLimitConfig) {
            // Use default rate limit
            rateLimitConfig = { limit: 10, windowMs: 60000 };
        }
        const key = rateLimitConfig.keyGenerator
            ? rateLimitConfig.keyGenerator(request)
            : `${request.ip}:${request.route.path}`;
        const now = Date.now();
        const windowStart = now - rateLimitConfig.windowMs;
        let requestData = MockRateLimitGuard.requests.get(key);
        if (!requestData || requestData.resetTime < windowStart) {
            requestData = { count: 0, resetTime: now + rateLimitConfig.windowMs };
        }
        if (requestData.count >= rateLimitConfig.limit) {
            const response = context.switchToHttp().getResponse();
            response.status(429).json({
                statusCode: 429,
                message: 'Too Many Requests',
                error: 'Rate limit exceeded',
                retryAfter: Math.ceil((requestData.resetTime - now) / 1000),
            });
            return false;
        }
        requestData.count++;
        MockRateLimitGuard.requests.set(key, requestData);
        // Add rate limit headers
        const response = context.switchToHttp().getResponse();
        response.setHeader('X-RateLimit-Limit', rateLimitConfig.limit);
        response.setHeader('X-RateLimit-Remaining', rateLimitConfig.limit - requestData.count);
        response.setHeader('X-RateLimit-Reset', Math.ceil(requestData.resetTime / 1000));
        return true;
    }
    static reset() {
        this.requests.clear();
    }
}
MockRateLimitGuard.requests = new Map();
describe('Rate Limiting Integration Tests', () => {
    let app;
    beforeAll(async () => {
        const moduleFixture = await testing_1.Test.createTestingModule({
            imports: [
                config_1.ConfigModule.forRoot({
                    isGlobal: true,
                }),
                throttler_1.ThrottlerModule.forRoot({
                    ttl: 60,
                    limit: 10,
                }),
            ],
            controllers: [RateLimitTestController],
            providers: [
                {
                    provide: rate_limit_guard_1.RateLimitGuard,
                    useClass: MockRateLimitGuard,
                },
            ],
        }).compile();
        app = moduleFixture.createNestApplication();
        await app.init();
    });
    afterAll(async () => {
        await app.close();
    });
    beforeEach(() => {
        MockRateLimitGuard.reset();
    });
    describe('Default Rate Limiting', () => {
        it('should allow requests within limit', async () => {
            // Make 5 requests (within default limit of 10)
            for (let i = 0; i < 5; i++) {
                const response = await request(app.getHttpServer())
                    .get('/rate-limit-test/default')
                    .expect(200);
                expect(response.body.message).toBe('Default rate limit endpoint');
                expect(response.headers['x-ratelimit-limit']).toBe('10');
                expect(response.headers['x-ratelimit-remaining']).toBe(String(10 - (i + 1)));
            }
        });
        it('should block requests exceeding limit', async () => {
            // Make requests up to the limit
            for (let i = 0; i < 10; i++) {
                await request(app.getHttpServer())
                    .get('/rate-limit-test/default')
                    .expect(200);
            }
            // Next request should be blocked
            const response = await request(app.getHttpServer())
                .get('/rate-limit-test/default')
                .expect(429);
            expect(response.body.statusCode).toBe(429);
            expect(response.body.message).toBe('Too Many Requests');
            expect(response.body.error).toBe('Rate limit exceeded');
            expect(response.body.retryAfter).toBeGreaterThan(0);
        });
        it('should include rate limit headers', async () => {
            const response = await request(app.getHttpServer())
                .get('/rate-limit-test/default')
                .expect(200);
            expect(response.headers['x-ratelimit-limit']).toBe('10');
            expect(response.headers['x-ratelimit-remaining']).toBe('9');
            expect(response.headers['x-ratelimit-reset']).toBeDefined();
        });
    });
    describe('Custom Rate Limiting', () => {
        it('should enforce strict rate limits', async () => {
            // Make 3 requests (at the limit)
            for (let i = 0; i < 3; i++) {
                await request(app.getHttpServer())
                    .get('/rate-limit-test/strict')
                    .expect(200);
            }
            // 4th request should be blocked
            await request(app.getHttpServer())
                .get('/rate-limit-test/strict')
                .expect(429);
        });
        it('should allow more requests with lenient limits', async () => {
            // Make 50 requests (well within limit of 100)
            for (let i = 0; i < 50; i++) {
                await request(app.getHttpServer())
                    .get('/rate-limit-test/lenient')
                    .expect(200);
            }
            // Should still be allowed
            await request(app.getHttpServer())
                .get('/rate-limit-test/lenient')
                .expect(200);
        });
        it('should handle different limits for different endpoints', async () => {
            // Use up strict endpoint limit
            for (let i = 0; i < 3; i++) {
                await request(app.getHttpServer())
                    .get('/rate-limit-test/strict')
                    .expect(200);
            }
            // Strict endpoint should be blocked
            await request(app.getHttpServer())
                .get('/rate-limit-test/strict')
                .expect(429);
            // But lenient endpoint should still work
            await request(app.getHttpServer())
                .get('/rate-limit-test/lenient')
                .expect(200);
        });
    });
    describe('HTTP Method Specific Limits', () => {
        it('should apply different limits to POST requests', async () => {
            // Make 5 POST requests (at the limit)
            for (let i = 0; i < 5; i++) {
                await request(app.getHttpServer())
                    .post('/rate-limit-test/create')
                    .expect(201);
            }
            // 6th request should be blocked
            await request(app.getHttpServer())
                .post('/rate-limit-test/create')
                .expect(429);
        });
        it('should not affect other endpoints when one is rate limited', async () => {
            // Use up POST endpoint limit
            for (let i = 0; i < 5; i++) {
                await request(app.getHttpServer())
                    .post('/rate-limit-test/create')
                    .expect(201);
            }
            // POST should be blocked
            await request(app.getHttpServer())
                .post('/rate-limit-test/create')
                .expect(429);
            // But GET should still work
            await request(app.getHttpServer())
                .get('/rate-limit-test/default')
                .expect(200);
        });
    });
    describe('Per-User Rate Limiting', () => {
        it('should track limits per user', async () => {
            // Simulate requests from different users
            const user1Requests = Array(5).fill(null).map(() => request(app.getHttpServer())
                .get('/rate-limit-test/per-user')
                .set('X-User-ID', 'user1'));
            const user2Requests = Array(5).fill(null).map(() => request(app.getHttpServer())
                .get('/rate-limit-test/per-user')
                .set('X-User-ID', 'user2'));
            // Both users should be able to make their requests
            await Promise.all([...user1Requests, ...user2Requests]);
            // Each user should still have remaining quota
            await request(app.getHttpServer())
                .get('/rate-limit-test/per-user')
                .set('X-User-ID', 'user1')
                .expect(200);
            await request(app.getHttpServer())
                .get('/rate-limit-test/per-user')
                .set('X-User-ID', 'user2')
                .expect(200);
        });
    });
    describe('No Rate Limiting', () => {
        it('should not apply rate limits to unguarded endpoints', async () => {
            // Make many requests to endpoint without rate limiting
            const requests = Array(20).fill(null).map(() => request(app.getHttpServer()).get('/rate-limit-test/no-limit'));
            const responses = await Promise.all(requests);
            responses.forEach(response => {
                expect(response.status).toBe(200);
                expect(response.body.message).toBe('No rate limit endpoint');
                expect(response.headers['x-ratelimit-limit']).toBeUndefined();
            });
        });
    });
    describe('Rate Limit Headers', () => {
        it('should provide accurate remaining count', async () => {
            const responses = [];
            for (let i = 0; i < 5; i++) {
                const response = await request(app.getHttpServer())
                    .get('/rate-limit-test/default')
                    .expect(200);
                responses.push(response);
            }
            responses.forEach((response, index) => {
                expect(response.headers['x-ratelimit-limit']).toBe('10');
                expect(response.headers['x-ratelimit-remaining']).toBe(String(10 - (index + 1)));
            });
        });
        it('should provide reset time', async () => {
            const response = await request(app.getHttpServer())
                .get('/rate-limit-test/default')
                .expect(200);
            const resetTime = parseInt(response.headers['x-ratelimit-reset']);
            const currentTime = Math.floor(Date.now() / 1000);
            expect(resetTime).toBeGreaterThan(currentTime);
            expect(resetTime).toBeLessThanOrEqual(currentTime + 60); // Within 1 minute
        });
        it('should provide retry-after header when rate limited', async () => {
            // Use up the limit
            for (let i = 0; i < 10; i++) {
                await request(app.getHttpServer())
                    .get('/rate-limit-test/default')
                    .expect(200);
            }
            // Next request should include retry-after
            const response = await request(app.getHttpServer())
                .get('/rate-limit-test/default')
                .expect(429);
            expect(response.body.retryAfter).toBeGreaterThan(0);
            expect(response.body.retryAfter).toBeLessThanOrEqual(60);
        });
    });
    describe('Concurrent Requests', () => {
        it('should handle concurrent requests correctly', async () => {
            // Make 10 concurrent requests (at the limit)
            const requests = Array(10).fill(null).map(() => request(app.getHttpServer()).get('/rate-limit-test/default'));
            const responses = await Promise.all(requests);
            // All should succeed since they're at the limit
            responses.forEach(response => {
                expect(response.status).toBe(200);
            });
            // Next request should be blocked
            await request(app.getHttpServer())
                .get('/rate-limit-test/default')
                .expect(429);
        });
        it('should handle race conditions properly', async () => {
            // Make requests that would exceed limit if not handled properly
            const requests = Array(15).fill(null).map(() => request(app.getHttpServer()).get('/rate-limit-test/strict'));
            const responses = await Promise.all(requests.map(req => req.then(res => ({ status: res.status }))
                .catch(err => ({ status: err.response?.status || 500 }))));
            const successCount = responses.filter(r => r.status === 200).length;
            const rateLimitedCount = responses.filter(r => r.status === 429).length;
            expect(successCount).toBe(3); // Only 3 should succeed
            expect(rateLimitedCount).toBe(12); // Rest should be rate limited
        });
    });
    describe('Error Handling', () => {
        it('should handle rate limit errors gracefully', async () => {
            // Use up the limit
            for (let i = 0; i < 10; i++) {
                await request(app.getHttpServer())
                    .get('/rate-limit-test/default')
                    .expect(200);
            }
            const response = await request(app.getHttpServer())
                .get('/rate-limit-test/default')
                .expect(429);
            expect(response.body).toEqual({
                statusCode: 429,
                message: 'Too Many Requests',
                error: 'Rate limit exceeded',
                retryAfter: expect.any(Number),
            });
        });
        it('should provide helpful error messages', async () => {
            // Use up strict limit
            for (let i = 0; i < 3; i++) {
                await request(app.getHttpServer())
                    .get('/rate-limit-test/strict')
                    .expect(200);
            }
            const response = await request(app.getHttpServer())
                .get('/rate-limit-test/strict')
                .expect(429);
            expect(response.body.message).toBe('Too Many Requests');
            expect(response.body.error).toBe('Rate limit exceeded');
            expect(typeof response.body.retryAfter).toBe('number');
        });
    });
    describe('Performance', () => {
        it('should handle rate limiting efficiently', async () => {
            const startTime = Date.now();
            // Make multiple requests
            const requests = Array(10).fill(null).map(() => request(app.getHttpServer()).get('/rate-limit-test/default'));
            await Promise.all(requests);
            const endTime = Date.now();
            const duration = endTime - startTime;
            // Should complete quickly even with rate limiting
            expect(duration).toBeLessThan(1000);
        });
        it('should not significantly impact response time', async () => {
            const startTime = Date.now();
            await request(app.getHttpServer())
                .get('/rate-limit-test/default')
                .expect(200);
            const endTime = Date.now();
            const duration = endTime - startTime;
            // Rate limiting should add minimal overhead
            expect(duration).toBeLessThan(100);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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