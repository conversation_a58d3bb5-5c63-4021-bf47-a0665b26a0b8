{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\auth\\guards\\roles.guard.ts", "mappings": ";;;;;;;;;;;;;;AAAA,2CAMwB;AACxB,uCAAyC;AAEzC,uDAAmD;AAoBnD;;;GAGG;AAEI,IAAM,UAAU,kBAAhB,MAAM,UAAU;IAGrB,YACmB,SAAoB,EACpB,WAAwB;QADxB,cAAS,GAAT,SAAS,CAAW;QACpB,gBAAW,GAAX,WAAW,CAAa;QAJ1B,WAAM,GAAG,IAAI,eAAM,CAAC,YAAU,CAAC,IAAI,CAAC,CAAC;IAKnD,CAAC;IAEJ,KAAK,CAAC,WAAW,CAAC,OAAyB;QACzC,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAmB,CAAC;QAErE,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACrD,IAAI,CAAC,aAAa,EAAE,MAAM;YAAE,OAAO,IAAI,CAAC;QAExC,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;QACzB,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAI,CAAC,WAAW,CAAC,uBAAuB,EAAE;gBACxC,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,aAAa;aACd,CAAC,CAAC;QACL,CAAC;QAED,MAAM,aAAa,GAAkB;YACnC,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,SAAS,EAAE,IAAI,CAAC,KAAK;YACrB,aAAa;YACb,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,MAAM,EAAE,OAAO,CAAC,MAAM;SACvB,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,aAAa,CAAC,CAAC;QAE/D,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;YAEpF,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,IAAI,CAAC,WAAW,CAAC,oBAAoB,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC;YAChE,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,aAAa,CAAC,CAAC;YAC9D,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,2BAAkB;gBAAE,MAAM,KAAK,CAAC;YAErD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE;gBACpD,GAAG,aAAa;gBAChB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAChE,CAAC,CAAC;YAEH,MAAM,IAAI,2BAAkB,CAAC,eAAe,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,OAAyB;QAChD,OAAO,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAW,OAAO,EAAE;YACzD,OAAO,CAAC,UAAU,EAAE;YACpB,OAAO,CAAC,QAAQ,EAAE;SACnB,CAAC,CAAC;IACL,CAAC;IAEO,WAAW,CACjB,MAAc,EACd,OAA+B,EAC/B,WAA6B,MAAM;QAEnC,MAAM,OAAO,GAAG,mBAAmB,MAAM,EAAE,CAAC;QAC5C,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAExC,MAAM,YAAY,GAAG,MAAM,KAAK,uBAAuB;YACrD,CAAC,CAAC,yBAAyB;YAC3B,CAAC,CAAC,0BAA0B,CAAC;QAE/B,MAAM,IAAI,2BAAkB,CAAC,YAAY,CAAC,CAAC;IAC7C,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAC9B,MAAc,EACd,SAAmB,EACnB,aAAuB;QAEvB,2CAA2C;QAC3C,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;YACzD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,iDAAiD;QACjD,OAAO,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;IACpE,CAAC;CACF,CAAA;AAzFY,gCAAU;qBAAV,UAAU;IADtB,IAAA,mBAAU,GAAE;yDAKmB,gBAAS,oBAAT,gBAAS,oDACP,0BAAW,oBAAX,0BAAW;GALhC,UAAU,CAyFtB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\auth\\guards\\roles.guard.ts"], "sourcesContent": ["import {\r\n  Injectable,\r\n  CanActivate,\r\n  ExecutionContext,\r\n  ForbiddenException,\r\n  Logger,\r\n} from '@nestjs/common';\r\nimport { Reflector } from '@nestjs/core';\r\nimport { Request } from 'express';\r\nimport { RbacService } from '../rbac/rbac.service';\r\n\r\n// Type definitions for better type safety\r\ninterface AuthenticatedUser {\r\n  id: string;\r\n  roles: string[];\r\n}\r\n\r\ninterface RequestWithUser extends Request {\r\n  user?: AuthenticatedUser;\r\n}\r\n\r\ninterface AccessContext {\r\n  userId: string;\r\n  userRoles: string[];\r\n  requiredRoles: string[];\r\n  url: string;\r\n  method: string;\r\n}\r\n\r\n/**\r\n * Role-based access control guard\r\n * Checks if user has required roles to access a resource\r\n */\r\n@Injectable()\r\nexport class RolesGuard implements CanActivate {\r\n  private readonly logger = new Logger(RolesGuard.name);\r\n\r\n  constructor(\r\n    private readonly reflector: Reflector,\r\n    private readonly rbacService: RbacService,\r\n  ) {}\r\n\r\n  async canActivate(context: ExecutionContext): Promise<boolean> {\r\n    const request = context.switchToHttp().getRequest<RequestWithUser>();\r\n    \r\n    const requiredRoles = this.getRequiredRoles(context);\r\n    if (!requiredRoles?.length) return true;\r\n\r\n    const { user } = request;\r\n    if (!user) {\r\n      this.logAndThrow('no authenticated user', {\r\n        url: request.url,\r\n        method: request.method,\r\n        requiredRoles,\r\n      });\r\n    }\r\n\r\n    const accessContext: AccessContext = {\r\n      userId: user.id,\r\n      userRoles: user.roles,\r\n      requiredRoles,\r\n      url: request.url,\r\n      method: request.method,\r\n    };\r\n\r\n    this.logger.debug('Checking role-based access', accessContext);\r\n\r\n    try {\r\n      const hasAccess = await this.validateUserAccess(user.id, user.roles, requiredRoles);\r\n      \r\n      if (!hasAccess) {\r\n        this.logAndThrow('insufficient roles', accessContext, 'warn');\r\n      }\r\n\r\n      this.logger.debug('Role-based access granted', accessContext);\r\n      return true;\r\n    } catch (error) {\r\n      if (error instanceof ForbiddenException) throw error;\r\n      \r\n      this.logger.error('Error checking role-based access', {\r\n        ...accessContext,\r\n        error: error instanceof Error ? error.message : 'Unknown error',\r\n      });\r\n      \r\n      throw new ForbiddenException('Access denied');\r\n    }\r\n  }\r\n\r\n  private getRequiredRoles(context: ExecutionContext): string[] | undefined {\r\n    return this.reflector.getAllAndOverride<string[]>('roles', [\r\n      context.getHandler(),\r\n      context.getClass(),\r\n    ]);\r\n  }\r\n\r\n  private logAndThrow(\r\n    reason: string,\r\n    context: Partial<AccessContext>,\r\n    logLevel: 'warn' | 'error' = 'warn',\r\n  ): never {\r\n    const message = `Access denied - ${reason}`;\r\n    this.logger[logLevel](message, context);\r\n    \r\n    const errorMessage = reason === 'no authenticated user' \r\n      ? 'Authentication required' \r\n      : 'Insufficient permissions';\r\n    \r\n    throw new ForbiddenException(errorMessage);\r\n  }\r\n\r\n  private async validateUserAccess(\r\n    userId: string,\r\n    userRoles: string[],\r\n    requiredRoles: string[],\r\n  ): Promise<boolean> {\r\n    // Check direct role matches first (faster)\r\n    if (requiredRoles.some(role => userRoles.includes(role))) {\r\n      return true;\r\n    }\r\n\r\n    // Check hierarchical roles if direct match fails\r\n    return this.rbacService.checkRoleHierarchy(userId, requiredRoles);\r\n  }\r\n}"], "version": 3}