{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\response-action-rolled-back.domain-event.ts", "mappings": ";;;AAAA,6DAA4E;AAC5E,gEAAuD;AAkBvD;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,MAAa,mCAAoC,SAAQ,+BAAkD;IACzG,YACE,WAA2B,EAC3B,SAA4C,EAC5C,OAOC;QAED,KAAK,CAAC,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAI,wBAAwB;QAC1B,OAAO,IAAI,CAAC,SAAS,CAAC,wBAAwB,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,wEAAwE;QACxE,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,KAAK,SAAS;YAC5C,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,KAAK,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC9C,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC;YAClD,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,0BAA0B;QACxB,MAAM,eAAe,GAAG;YACtB,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,WAAW;YACtB,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,QAAQ;YACnB,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,mBAAmB;SAC/B,CAAC;QAEF,OAAO,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,MAAM,kBAAkB,GAAG;YACzB,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,QAAQ;YACnB,6BAAU,CAAC,YAAY;YACvB,6BAAU,CAAC,SAAS;YACpB,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,YAAY;YACvB,6BAAU,CAAC,oBAAoB;SAChC,CAAC;QAEF,OAAO,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,MAAM,eAAe,GAAG;YACtB,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,sBAAsB;SAClC,CAAC;QAEF,OAAO,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,uBAAuB;QACrB,MAAM,aAAa,GAAG;YACpB,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,kBAAkB;YAC7B,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,sBAAsB;YACjC,6BAAU,CAAC,gBAAgB;SAC5B,CAAC;QAEF,OAAO,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,IAAI,IAAI,CAAC,0BAA0B,EAAE,EAAE,CAAC;YACtC,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC;QAC5D,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;YACjC,OAAO,MAAM,CAAC,CAAC,8CAA8C;QAC/D,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;YAC9B,OAAO,QAAQ,CAAC,CAAC,kCAAkC;QACrD,CAAC;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE,EAAE,CAAC;YACnC,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,uBAAuB;QACrB,MAAM,YAAY,GAAa,EAAE,CAAC;QAElC,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;YACjC,YAAY,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;YAC3E,YAAY,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;YAChF,YAAY,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC;QACtF,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,KAAK,6BAAU,CAAC,QAAQ,EAAE,CAAC;YACtD,YAAY,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;YAC7E,YAAY,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,KAAK,6BAAU,CAAC,eAAe,EAAE,CAAC;YAC7D,YAAY,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;YAC/D,YAAY,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,KAAK,6BAAU,CAAC,eAAe,EAAE,CAAC;YAC7D,YAAY,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;YACxE,YAAY,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,KAAK,6BAAU,CAAC,eAAe,EAAE,CAAC;YAC7D,YAAY,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAC;YAC/E,YAAY,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,KAAK,6BAAU,CAAC,mBAAmB,EAAE,CAAC;YACjE,YAAY,CAAC,IAAI,CAAC,8DAA8D,CAAC,CAAC;YAClF,YAAY,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QACjE,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,yBAAyB;QACvB,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,gCAAgC;QAChC,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAC7C,OAAO,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QAE5C,IAAI,IAAI,CAAC,0BAA0B,EAAE,EAAE,CAAC;YACtC,OAAO,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;YACvD,OAAO,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;YAClE,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;YACjC,OAAO,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;YAC3D,OAAO,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YAC5C,OAAO,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;YAC9B,OAAO,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;YAC3D,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAC3C,OAAO,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE,EAAE,CAAC;YACnC,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YAC7C,OAAO,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAC1C,OAAO,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QACrD,CAAC;QAED,kCAAkC;QAClC,QAAQ,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;YAClC,KAAK,6BAAU,CAAC,QAAQ;gBACtB,OAAO,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;gBAC1D,OAAO,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;gBACtD,MAAM;YACR,KAAK,6BAAU,CAAC,eAAe;gBAC7B,OAAO,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;gBACpD,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;gBAC3C,MAAM;YACR,KAAK,6BAAU,CAAC,eAAe;gBAC7B,OAAO,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;gBACzC,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;gBAC7C,MAAM;YACR,KAAK,6BAAU,CAAC,cAAc;gBAC5B,OAAO,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;gBACvD,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;gBAC7C,MAAM;QACV,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,qEAAqE;QACrE,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACnC,OAAO,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAE1C,IAAI,IAAI,CAAC,0BAA0B,EAAE,EAAE,CAAC;YACtC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC9B,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAEvC,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;gBACjC,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACpC,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;YACjC,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YACxC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAClC,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;YAC9B,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC/B,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAClC,CAAC;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE,EAAE,CAAC;YACnC,OAAO,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACtC,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,2BAA2B;QACzB,MAAM,cAAc,GAAa,EAAE,CAAC;QAEpC,cAAc,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QAChE,cAAc,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QAEhE,IAAI,IAAI,CAAC,0BAA0B,EAAE,EAAE,CAAC;YACtC,cAAc,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;YACtE,cAAc,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;YACjC,cAAc,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;YACxE,cAAc,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,KAAK,6BAAU,CAAC,WAAW,EAAE,CAAC;YACzD,cAAc,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;YAC7E,cAAc,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QAC7D,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,yBAAyB;QAKvB,IAAI,QAAQ,GAA4C,OAAO,CAAC;QAChE,IAAI,SAAS,GAAuC,OAAO,CAAC;QAC5D,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,IAAI,IAAI,CAAC,0BAA0B,EAAE,EAAE,CAAC;YACtC,QAAQ,GAAG,MAAM,CAAC;YAClB,SAAS,GAAG,WAAW,CAAC;YACxB,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC9B,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAClC,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;YACjC,QAAQ,GAAG,SAAS,CAAC;YACrB,SAAS,GAAG,WAAW,CAAC;YACxB,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC9B,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC9B,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC9B,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;YAC9B,QAAQ,GAAG,QAAQ,CAAC;YACpB,SAAS,GAAG,UAAU,CAAC;YACvB,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACjC,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACrC,CAAC;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE,EAAE,CAAC;YACnC,QAAQ,GAAG,QAAQ,CAAC;YACpB,SAAS,GAAG,UAAU,CAAC;YACvB,KAAK,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAClC,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAChC,CAAC;QAED,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,kBAAkB;QAQhB,OAAO;YACL,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU;YACrC,cAAc,EAAE,IAAI,CAAC,iBAAiB,EAAE;YACxC,kBAAkB,EAAE,IAAI,CAAC,0BAA0B,EAAE;YACrD,WAAW,EAAE,IAAI,CAAC,mBAAmB,EAAE;YACvC,YAAY,EAAE,IAAI,CAAC,oBAAoB,EAAE;YACzC,yBAAyB,EAAE,IAAI,CAAC,uBAAuB,EAAE,CAAC,MAAM;SACjE,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,kBAAkB;QAchB,MAAM,UAAU,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAEpD,OAAO;YACL,SAAS,EAAE,0BAA0B;YACrC,MAAM,EAAE,6BAA6B;YACrC,QAAQ,EAAE,gBAAgB;YAC1B,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;YACvC,IAAI,EAAE,IAAI,CAAC,SAAS;YACpB,QAAQ,EAAE;gBACR,cAAc,EAAE,IAAI,CAAC,iBAAiB,EAAE;gBACxC,kBAAkB,EAAE,IAAI,CAAC,0BAA0B,EAAE;gBACrD,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE;gBACnD,YAAY,EAAE,IAAI,CAAC,oBAAoB,EAAE;gBACzC,kBAAkB,EAAE,UAAU,CAAC,SAAS,KAAK,OAAO;aACrD;SACF,CAAC;IACJ,CAAC;CACF;AA3aD,kFA2aC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\response-action-rolled-back.domain-event.ts"], "sourcesContent": ["import { BaseDomainEvent, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { ActionType } from '../enums/action-type.enum';\r\n\r\n/**\r\n * Response Action Rolled Back Domain Event Data\r\n */\r\nexport interface ResponseActionRolledBackEventData {\r\n  /** Type of action that was rolled back */\r\n  actionType: ActionType;\r\n  /** Who performed the rollback */\r\n  rolledBackBy: string;\r\n  /** When the rollback was performed */\r\n  rolledBackAt: Date;\r\n  /** Rollback execution results */\r\n  rollbackResults?: Record<string, any>;\r\n  /** Original execution results that were rolled back */\r\n  originalExecutionResults?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Response Action Rolled Back Domain Event\r\n * \r\n * Raised when a response action has been successfully rolled back.\r\n * This event indicates that a previously executed action has been\r\n * reversed or undone, typically to restore a previous state.\r\n * \r\n * Key information:\r\n * - Action type and rollback details\r\n * - Who performed the rollback\r\n * - Rollback timestamp and results\r\n * - Original execution context\r\n * \r\n * Use cases:\r\n * - Track action rollback completion\r\n * - Update system state after rollback\r\n * - Notify stakeholders of rollback\r\n * - Generate rollback audit trails\r\n * - Trigger post-rollback validation\r\n * - Update monitoring and dashboards\r\n */\r\nexport class ResponseActionRolledBackDomainEvent extends BaseDomainEvent<ResponseActionRolledBackEventData> {\r\n  constructor(\r\n    aggregateId: UniqueEntityId,\r\n    eventData: ResponseActionRolledBackEventData,\r\n    options?: {\r\n      eventId?: UniqueEntityId;\r\n      occurredOn?: Date;\r\n      eventVersion?: number;\r\n      correlationId?: string;\r\n      causationId?: string;\r\n      metadata?: Record<string, any>;\r\n    }\r\n  ) {\r\n    super(aggregateId, eventData, options);\r\n  }\r\n\r\n  /**\r\n   * Get the action type that was rolled back\r\n   */\r\n  get actionType(): ActionType {\r\n    return this.eventData.actionType;\r\n  }\r\n\r\n  /**\r\n   * Get who performed the rollback\r\n   */\r\n  get rolledBackBy(): string {\r\n    return this.eventData.rolledBackBy;\r\n  }\r\n\r\n  /**\r\n   * Get when the rollback was performed\r\n   */\r\n  get rolledBackAt(): Date {\r\n    return this.eventData.rolledBackAt;\r\n  }\r\n\r\n  /**\r\n   * Get the rollback results\r\n   */\r\n  get rollbackResults(): Record<string, any> | undefined {\r\n    return this.eventData.rollbackResults;\r\n  }\r\n\r\n  /**\r\n   * Get the original execution results\r\n   */\r\n  get originalExecutionResults(): Record<string, any> | undefined {\r\n    return this.eventData.originalExecutionResults;\r\n  }\r\n\r\n  /**\r\n   * Check if the rollback was successful\r\n   */\r\n  isRollbackSuccessful(): boolean {\r\n    // Assume successful if rollback results exist and no error is indicated\r\n    return this.eventData.rollbackResults !== undefined &&\r\n           !this.eventData.rollbackResults?.error;\r\n  }\r\n\r\n  /**\r\n   * Check if this was an automated rollback\r\n   */\r\n  isAutomatedRollback(): boolean {\r\n    return this.eventData.rolledBackBy.includes('system') ||\r\n           this.eventData.rolledBackBy.includes('automation') ||\r\n           this.eventData.rolledBackBy.includes('bot');\r\n  }\r\n\r\n  /**\r\n   * Check if this was a manual rollback\r\n   */\r\n  isManualRollback(): boolean {\r\n    return !this.isAutomatedRollback();\r\n  }\r\n\r\n  /**\r\n   * Check if this is a security-critical action rollback\r\n   */\r\n  isSecurityCriticalRollback(): boolean {\r\n    const criticalActions = [\r\n      ActionType.ISOLATE_SYSTEM,\r\n      ActionType.SHUTDOWN_SYSTEM,\r\n      ActionType.DELETE_FILE,\r\n      ActionType.REMOVE_MALWARE,\r\n      ActionType.DISABLE_ACCOUNT,\r\n      ActionType.BLOCK_IP,\r\n      ActionType.QUARANTINE_FILE,\r\n      ActionType.UPDATE_FIREWALL,\r\n      ActionType.PATCH_VULNERABILITY,\r\n    ];\r\n    \r\n    return criticalActions.includes(this.eventData.actionType);\r\n  }\r\n\r\n  /**\r\n   * Check if this is a containment action rollback\r\n   */\r\n  isContainmentRollback(): boolean {\r\n    const containmentActions = [\r\n      ActionType.ISOLATE_SYSTEM,\r\n      ActionType.QUARANTINE_FILE,\r\n      ActionType.BLOCK_IP,\r\n      ActionType.BLOCK_DOMAIN,\r\n      ActionType.BLOCK_URL,\r\n      ActionType.DISABLE_ACCOUNT,\r\n      ActionType.REVOKE_TOKEN,\r\n      ActionType.TERMINATE_CONNECTION,\r\n    ];\r\n    \r\n    return containmentActions.includes(this.eventData.actionType);\r\n  }\r\n\r\n  /**\r\n   * Check if this is a recovery action rollback\r\n   */\r\n  isRecoveryRollback(): boolean {\r\n    const recoveryActions = [\r\n      ActionType.RESTORE_BACKUP,\r\n      ActionType.REBUILD_SYSTEM,\r\n      ActionType.RESTORE_NETWORK,\r\n      ActionType.ENABLE_SERVICE,\r\n      ActionType.RESET_PASSWORD,\r\n      ActionType.REGENERATE_CERTIFICATE,\r\n    ];\r\n    \r\n    return recoveryActions.includes(this.eventData.actionType);\r\n  }\r\n\r\n  /**\r\n   * Check if this is a configuration change rollback\r\n   */\r\n  isConfigurationRollback(): boolean {\r\n    const configActions = [\r\n      ActionType.UPDATE_FIREWALL,\r\n      ActionType.RECONFIGURE_SYSTEM,\r\n      ActionType.UPDATE_SOFTWARE,\r\n      ActionType.CONFIGURE_SEGMENTATION,\r\n      ActionType.UPDATE_IDS_RULES,\r\n    ];\r\n    \r\n    return configActions.includes(this.eventData.actionType);\r\n  }\r\n\r\n  /**\r\n   * Get rollback impact level\r\n   */\r\n  getRollbackImpact(): 'low' | 'medium' | 'high' | 'critical' {\r\n    if (this.isSecurityCriticalRollback()) {\r\n      return this.isContainmentRollback() ? 'critical' : 'high';\r\n    }\r\n    \r\n    if (this.isContainmentRollback()) {\r\n      return 'high'; // Rolling back containment may expose systems\r\n    }\r\n    \r\n    if (this.isRecoveryRollback()) {\r\n      return 'medium'; // May affect service availability\r\n    }\r\n    \r\n    if (this.isConfigurationRollback()) {\r\n      return 'medium';\r\n    }\r\n    \r\n    return 'low';\r\n  }\r\n\r\n  /**\r\n   * Get security implications of the rollback\r\n   */\r\n  getSecurityImplications(): string[] {\r\n    const implications: string[] = [];\r\n\r\n    if (this.isContainmentRollback()) {\r\n      implications.push('Containment measures removed - systems may be exposed');\r\n      implications.push('Threat may regain access to previously contained resources');\r\n      implications.push('Increased risk exposure until alternative measures implemented');\r\n    }\r\n\r\n    if (this.eventData.actionType === ActionType.BLOCK_IP) {\r\n      implications.push('IP address unblocked - traffic from this IP now allowed');\r\n      implications.push('Monitor for suspicious activity from unblocked IP');\r\n    }\r\n\r\n    if (this.eventData.actionType === ActionType.DISABLE_ACCOUNT) {\r\n      implications.push('User account re-enabled - access restored');\r\n      implications.push('Monitor account activity for suspicious behavior');\r\n    }\r\n\r\n    if (this.eventData.actionType === ActionType.QUARANTINE_FILE) {\r\n      implications.push('File removed from quarantine - file now accessible');\r\n      implications.push('Ensure file is safe before allowing access');\r\n    }\r\n\r\n    if (this.eventData.actionType === ActionType.UPDATE_FIREWALL) {\r\n      implications.push('Firewall rules reverted - network access patterns changed');\r\n      implications.push('Review network traffic for unexpected patterns');\r\n    }\r\n\r\n    if (this.eventData.actionType === ActionType.PATCH_VULNERABILITY) {\r\n      implications.push('Vulnerability patch removed - system may be vulnerable again');\r\n      implications.push('Implement alternative protection measures');\r\n    }\r\n\r\n    return implications;\r\n  }\r\n\r\n  /**\r\n   * Get recommended post-rollback actions\r\n   */\r\n  getRecommendedPostActions(): string[] {\r\n    const actions: string[] = [];\r\n\r\n    // General post-rollback actions\r\n    actions.push('Validate rollback completion');\r\n    actions.push('Document rollback rationale');\r\n\r\n    if (this.isSecurityCriticalRollback()) {\r\n      actions.push('Assess security posture after rollback');\r\n      actions.push('Implement alternative security measures if needed');\r\n      actions.push('Monitor for security incidents');\r\n    }\r\n\r\n    if (this.isContainmentRollback()) {\r\n      actions.push('Implement alternative containment measures');\r\n      actions.push('Monitor for threat activity');\r\n      actions.push('Assess threat landscape changes');\r\n    }\r\n\r\n    if (this.isRecoveryRollback()) {\r\n      actions.push('Verify system functionality after rollback');\r\n      actions.push('Check service availability');\r\n      actions.push('Consider alternative recovery approaches');\r\n    }\r\n\r\n    if (this.isConfigurationRollback()) {\r\n      actions.push('Validate configuration state');\r\n      actions.push('Test system functionality');\r\n      actions.push('Update configuration documentation');\r\n    }\r\n\r\n    // Action-specific recommendations\r\n    switch (this.eventData.actionType) {\r\n      case ActionType.BLOCK_IP:\r\n        actions.push('Monitor traffic from unblocked IP address');\r\n        actions.push('Consider alternative blocking methods');\r\n        break;\r\n      case ActionType.DISABLE_ACCOUNT:\r\n        actions.push('Monitor re-enabled account activity');\r\n        actions.push('Review account permissions');\r\n        break;\r\n      case ActionType.QUARANTINE_FILE:\r\n        actions.push('Re-scan file for threats');\r\n        actions.push('Monitor file access patterns');\r\n        break;\r\n      case ActionType.ISOLATE_SYSTEM:\r\n        actions.push('Monitor system for suspicious activity');\r\n        actions.push('Implement network monitoring');\r\n        break;\r\n    }\r\n\r\n    return actions;\r\n  }\r\n\r\n  /**\r\n   * Get stakeholders to notify\r\n   */\r\n  getNotificationTargets(): string[] {\r\n    const targets: string[] = [];\r\n\r\n    // Always notify the rollback requestor and original action requestor\r\n    targets.push('rollback-requestor');\r\n    targets.push('original-action-requestor');\r\n\r\n    if (this.isSecurityCriticalRollback()) {\r\n      targets.push('security-team');\r\n      targets.push('incident-response-team');\r\n      \r\n      if (this.isContainmentRollback()) {\r\n        targets.push('security-managers');\r\n      }\r\n    }\r\n\r\n    if (this.isContainmentRollback()) {\r\n      targets.push('containment-specialists');\r\n      targets.push('threat-analysts');\r\n    }\r\n\r\n    if (this.isRecoveryRollback()) {\r\n      targets.push('service-owners');\r\n      targets.push('operations-team');\r\n    }\r\n\r\n    if (this.isConfigurationRollback()) {\r\n      targets.push('system-administrators');\r\n      targets.push('configuration-managers');\r\n    }\r\n\r\n    return targets;\r\n  }\r\n\r\n  /**\r\n   * Get compliance considerations\r\n   */\r\n  getComplianceConsiderations(): string[] {\r\n    const considerations: string[] = [];\r\n\r\n    considerations.push('Document rollback decision and rationale');\r\n    considerations.push('Maintain audit trail of rollback process');\r\n\r\n    if (this.isSecurityCriticalRollback()) {\r\n      considerations.push('Ensure rollback approval was properly obtained');\r\n      considerations.push('Document security impact assessment');\r\n    }\r\n\r\n    if (this.isContainmentRollback()) {\r\n      considerations.push('Document risk acceptance for containment removal');\r\n      considerations.push('Ensure alternative controls are in place');\r\n    }\r\n\r\n    if (this.eventData.actionType === ActionType.DELETE_FILE) {\r\n      considerations.push('Ensure data recovery complies with retention policies');\r\n      considerations.push('Document data restoration rationale');\r\n    }\r\n\r\n    return considerations;\r\n  }\r\n\r\n  /**\r\n   * Get monitoring requirements\r\n   */\r\n  getMonitoringRequirements(): {\r\n    duration: 'short' | 'medium' | 'long' | 'ongoing';\r\n    intensity: 'basic' | 'enhanced' | 'intensive';\r\n    focus: string[];\r\n  } {\r\n    let duration: 'short' | 'medium' | 'long' | 'ongoing' = 'short';\r\n    let intensity: 'basic' | 'enhanced' | 'intensive' = 'basic';\r\n    const focus: string[] = [];\r\n\r\n    if (this.isSecurityCriticalRollback()) {\r\n      duration = 'long';\r\n      intensity = 'intensive';\r\n      focus.push('security-events');\r\n      focus.push('threat-indicators');\r\n    }\r\n\r\n    if (this.isContainmentRollback()) {\r\n      duration = 'ongoing';\r\n      intensity = 'intensive';\r\n      focus.push('threat-activity');\r\n      focus.push('network-traffic');\r\n      focus.push('system-access');\r\n    }\r\n\r\n    if (this.isRecoveryRollback()) {\r\n      duration = 'medium';\r\n      intensity = 'enhanced';\r\n      focus.push('system-performance');\r\n      focus.push('service-availability');\r\n    }\r\n\r\n    if (this.isConfigurationRollback()) {\r\n      duration = 'medium';\r\n      intensity = 'enhanced';\r\n      focus.push('configuration-drift');\r\n      focus.push('system-behavior');\r\n    }\r\n\r\n    return { duration, intensity, focus };\r\n  }\r\n\r\n  /**\r\n   * Get rollback metrics\r\n   */\r\n  getRollbackMetrics(): {\r\n    actionType: ActionType;\r\n    rollbackImpact: string;\r\n    isSecurityCritical: boolean;\r\n    isAutomated: boolean;\r\n    isSuccessful: boolean;\r\n    securityImplicationsCount: number;\r\n  } {\r\n    return {\r\n      actionType: this.eventData.actionType,\r\n      rollbackImpact: this.getRollbackImpact(),\r\n      isSecurityCritical: this.isSecurityCriticalRollback(),\r\n      isAutomated: this.isAutomatedRollback(),\r\n      isSuccessful: this.isRollbackSuccessful(),\r\n      securityImplicationsCount: this.getSecurityImplications().length,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert to integration event format\r\n   */\r\n  toIntegrationEvent(): {\r\n    eventType: string;\r\n    action: string;\r\n    resource: string;\r\n    resourceId: string;\r\n    data: ResponseActionRolledBackEventData;\r\n    metadata: {\r\n      rollbackImpact: string;\r\n      isSecurityCritical: boolean;\r\n      isContainmentRollback: boolean;\r\n      isSuccessful: boolean;\r\n      requiresMonitoring: boolean;\r\n    };\r\n  } {\r\n    const monitoring = this.getMonitoringRequirements();\r\n    \r\n    return {\r\n      eventType: 'ResponseActionRolledBack',\r\n      action: 'response_action_rolled_back',\r\n      resource: 'ResponseAction',\r\n      resourceId: this.aggregateId.toString(),\r\n      data: this.eventData,\r\n      metadata: {\r\n        rollbackImpact: this.getRollbackImpact(),\r\n        isSecurityCritical: this.isSecurityCriticalRollback(),\r\n        isContainmentRollback: this.isContainmentRollback(),\r\n        isSuccessful: this.isRollbackSuccessful(),\r\n        requiresMonitoring: monitoring.intensity !== 'basic',\r\n      },\r\n    };\r\n  }\r\n}"], "version": 3}