c2420442612b573e83b01cd11c7c298e
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", { value: true });
exports.VulnerabilityAssessmentController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../../../infrastructure/auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../../../../infrastructure/auth/guards/roles.guard");
const roles_decorator_1 = require("../../../../infrastructure/auth/decorators/roles.decorator");
const current_user_decorator_1 = require("../../../../infrastructure/auth/decorators/current-user.decorator");
const vulnerability_assessment_service_1 = require("../../application/services/vulnerability-assessment.service");
const create_assessment_dto_1 = require("../dto/create-assessment.dto");
const update_assessment_dto_1 = require("../dto/update-assessment.dto");
const search_assessments_dto_1 = require("../dto/search-assessments.dto");
/**
 * Vulnerability Assessment controller
 * Handles vulnerability assessment API endpoints
 */
let VulnerabilityAssessmentController = class VulnerabilityAssessmentController {
    constructor(assessmentService) {
        this.assessmentService = assessmentService;
    }
    /**
     * Search assessments
     */
    async searchAssessments(query) {
        return await this.assessmentService.searchAssessments(query);
    }
    /**
     * Get assessment details
     */
    async getAssessmentDetails(id) {
        return await this.assessmentService.getAssessmentDetails(id);
    }
    /**
     * Create assessment
     */
    async createAssessment(createAssessmentDto, user) {
        return await this.assessmentService.createAssessment(createAssessmentDto, user.id);
    }
    /**
     * Update assessment
     */
    async updateAssessment(id, updateAssessmentDto, user) {
        return await this.assessmentService.updateAssessment(id, updateAssessmentDto, user.id);
    }
    /**
     * Complete assessment
     */
    async completeAssessment(id, user) {
        return await this.assessmentService.completeAssessment(id, user.id);
    }
    /**
     * Review assessment
     */
    async reviewAssessment(id, reviewData, user) {
        return await this.assessmentService.reviewAssessment(id, reviewData, user.id);
    }
    /**
     * Delete assessment
     */
    async deleteAssessment(id, user) {
        await this.assessmentService.deleteAssessment(id, user.id);
    }
    /**
     * Get assessment summary
     */
    async getAssessmentSummary(id) {
        const assessment = await this.assessmentService.getAssessmentDetails(id);
        return assessment.getSummary();
    }
    /**
     * Export assessment for reporting
     */
    async exportAssessment(id) {
        const assessment = await this.assessmentService.getAssessmentDetails(id);
        return assessment.exportForReporting();
    }
    /**
     * Mark as false positive
     */
    async markAsFalsePositive(id, data, user) {
        const assessment = await this.assessmentService.getAssessmentDetails(id);
        assessment.markAsFalsePositive(data.justification);
        return await this.assessmentService.updateAssessment(id, assessment, user.id);
    }
    /**
     * Accept risk
     */
    async acceptRisk(id, data, user) {
        const assessment = await this.assessmentService.getAssessmentDetails(id);
        assessment.acceptRisk(data.justification);
        return await this.assessmentService.updateAssessment(id, assessment, user.id);
    }
    /**
     * Get assessments by vulnerability
     */
    async getAssessmentsByVulnerability(vulnerabilityId) {
        return await this.assessmentService.searchAssessments({
            vulnerabilityIds: [vulnerabilityId],
        });
    }
    /**
     * Get assessments by asset
     */
    async getAssessmentsByAsset(assetId) {
        return await this.assessmentService.searchAssessments({
            assetIds: [assetId],
        });
    }
};
exports.VulnerabilityAssessmentController = VulnerabilityAssessmentController;
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)('admin', 'security_analyst', 'security_manager', 'viewer'),
    (0, swagger_1.ApiOperation)({ summary: 'Search vulnerability assessments with filtering and pagination' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Page number' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Items per page' }),
    (0, swagger_1.ApiQuery)({ name: 'vulnerabilityIds', required: false, type: [String], description: 'Filter by vulnerability IDs' }),
    (0, swagger_1.ApiQuery)({ name: 'assetIds', required: false, type: [String], description: 'Filter by asset IDs' }),
    (0, swagger_1.ApiQuery)({ name: 'statuses', required: false, type: [String], description: 'Filter by statuses' }),
    (0, swagger_1.ApiQuery)({ name: 'assessmentTypes', required: false, type: [String], description: 'Filter by assessment types' }),
    (0, swagger_1.ApiQuery)({ name: 'assessedBy', required: false, type: [String], description: 'Filter by assessor' }),
    (0, swagger_1.ApiQuery)({ name: 'severities', required: false, type: [String], description: 'Filter by assessed severities' }),
    (0, swagger_1.ApiQuery)({ name: 'isFalsePositive', required: false, type: Boolean, description: 'Filter by false positive status' }),
    (0, swagger_1.ApiQuery)({ name: 'isAcceptedRisk', required: false, type: Boolean, description: 'Filter by accepted risk status' }),
    (0, swagger_1.ApiQuery)({ name: 'sortBy', required: false, type: String, description: 'Sort field' }),
    (0, swagger_1.ApiQuery)({ name: 'sortOrder', required: false, enum: ['ASC', 'DESC'], description: 'Sort order' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Assessments retrieved successfully',
    }),
    __param(0, (0, common_1.Query)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_b = typeof search_assessments_dto_1.SearchAssessmentsDto !== "undefined" && search_assessments_dto_1.SearchAssessmentsDto) === "function" ? _b : Object]),
    __metadata("design:returntype", Promise)
], VulnerabilityAssessmentController.prototype, "searchAssessments", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)('admin', 'security_analyst', 'security_manager', 'viewer'),
    (0, swagger_1.ApiOperation)({ summary: 'Get assessment details by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Assessment ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Assessment details retrieved successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Assessment not found',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], VulnerabilityAssessmentController.prototype, "getAssessmentDetails", null);
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)('admin', 'security_analyst', 'security_manager'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new vulnerability assessment' }),
    (0, swagger_1.ApiBody)({ type: create_assessment_dto_1.CreateAssessmentDto }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Assessment created successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid assessment data',
    }),
    __param(0, (0, common_1.Body)(common_1.ValidationPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_c = typeof create_assessment_dto_1.CreateAssessmentDto !== "undefined" && create_assessment_dto_1.CreateAssessmentDto) === "function" ? _c : Object, Object]),
    __metadata("design:returntype", Promise)
], VulnerabilityAssessmentController.prototype, "createAssessment", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, roles_decorator_1.Roles)('admin', 'security_analyst', 'security_manager'),
    (0, swagger_1.ApiOperation)({ summary: 'Update assessment' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Assessment ID' }),
    (0, swagger_1.ApiBody)({ type: update_assessment_dto_1.UpdateAssessmentDto }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Assessment updated successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Assessment not found',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)(common_1.ValidationPipe)),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, typeof (_d = typeof update_assessment_dto_1.UpdateAssessmentDto !== "undefined" && update_assessment_dto_1.UpdateAssessmentDto) === "function" ? _d : Object, Object]),
    __metadata("design:returntype", Promise)
], VulnerabilityAssessmentController.prototype, "updateAssessment", null);
__decorate([
    (0, common_1.Put)(':id/complete'),
    (0, roles_decorator_1.Roles)('admin', 'security_analyst', 'security_manager'),
    (0, swagger_1.ApiOperation)({ summary: 'Complete assessment' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Assessment ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Assessment completed successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Assessment not found',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], VulnerabilityAssessmentController.prototype, "completeAssessment", null);
__decorate([
    (0, common_1.Put)(':id/review'),
    (0, roles_decorator_1.Roles)('admin', 'security_manager'),
    (0, swagger_1.ApiOperation)({ summary: 'Review assessment' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Assessment ID' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                approved: { type: 'boolean' },
                comments: { type: 'string' },
            },
            required: ['approved'],
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Assessment reviewed successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Assessment not found',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], VulnerabilityAssessmentController.prototype, "reviewAssessment", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)('admin', 'security_manager'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete assessment' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Assessment ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NO_CONTENT,
        description: 'Assessment deleted successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Assessment not found',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], VulnerabilityAssessmentController.prototype, "deleteAssessment", null);
__decorate([
    (0, common_1.Get)(':id/summary'),
    (0, roles_decorator_1.Roles)('admin', 'security_analyst', 'security_manager', 'viewer'),
    (0, swagger_1.ApiOperation)({ summary: 'Get assessment summary' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Assessment ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Assessment summary retrieved successfully',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], VulnerabilityAssessmentController.prototype, "getAssessmentSummary", null);
__decorate([
    (0, common_1.Get)(':id/export'),
    (0, roles_decorator_1.Roles)('admin', 'security_analyst', 'security_manager'),
    (0, swagger_1.ApiOperation)({ summary: 'Export assessment data for reporting' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Assessment ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Assessment data exported successfully',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], VulnerabilityAssessmentController.prototype, "exportAssessment", null);
__decorate([
    (0, common_1.Put)(':id/false-positive'),
    (0, roles_decorator_1.Roles)('admin', 'security_analyst', 'security_manager'),
    (0, swagger_1.ApiOperation)({ summary: 'Mark assessment as false positive' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Assessment ID' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                justification: { type: 'string' },
            },
            required: ['justification'],
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Assessment marked as false positive successfully',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], VulnerabilityAssessmentController.prototype, "markAsFalsePositive", null);
__decorate([
    (0, common_1.Put)(':id/accept-risk'),
    (0, roles_decorator_1.Roles)('admin', 'security_manager'),
    (0, swagger_1.ApiOperation)({ summary: 'Accept risk for assessment' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Assessment ID' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                justification: { type: 'string' },
            },
            required: ['justification'],
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Risk accepted successfully',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], VulnerabilityAssessmentController.prototype, "acceptRisk", null);
__decorate([
    (0, common_1.Get)('vulnerability/:vulnerabilityId'),
    (0, roles_decorator_1.Roles)('admin', 'security_analyst', 'security_manager', 'viewer'),
    (0, swagger_1.ApiOperation)({ summary: 'Get assessments for a specific vulnerability' }),
    (0, swagger_1.ApiParam)({ name: 'vulnerabilityId', description: 'Vulnerability ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Assessments retrieved successfully',
    }),
    __param(0, (0, common_1.Param)('vulnerabilityId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], VulnerabilityAssessmentController.prototype, "getAssessmentsByVulnerability", null);
__decorate([
    (0, common_1.Get)('asset/:assetId'),
    (0, roles_decorator_1.Roles)('admin', 'security_analyst', 'security_manager', 'viewer'),
    (0, swagger_1.ApiOperation)({ summary: 'Get assessments for a specific asset' }),
    (0, swagger_1.ApiParam)({ name: 'assetId', description: 'Asset ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Assessments retrieved successfully',
    }),
    __param(0, (0, common_1.Param)('assetId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], VulnerabilityAssessmentController.prototype, "getAssessmentsByAsset", null);
exports.VulnerabilityAssessmentController = VulnerabilityAssessmentController = __decorate([
    (0, swagger_1.ApiTags)('Vulnerability Assessments'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, common_1.Controller)('api/vulnerability-assessments'),
    __metadata("design:paramtypes", [typeof (_a = typeof vulnerability_assessment_service_1.VulnerabilityAssessmentService !== "undefined" && vulnerability_assessment_service_1.VulnerabilityAssessmentService) === "function" ? _a : Object])
], VulnerabilityAssessmentController);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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