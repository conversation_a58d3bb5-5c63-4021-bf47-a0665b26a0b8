ed3030d67a8629c039acf8f4be64da63
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const port_value_object_1 = require("./port.value-object");
describe('Port Value Object', () => {
    describe('Creation and Validation', () => {
        it('should create valid TCP port', () => {
            const port = port_value_object_1.Port.tcp(80);
            expect(port.number).toBe(80);
            expect(port.protocol).toBe(port_value_object_1.PortProtocol.TCP);
            expect(port.isTCP()).toBe(true);
            expect(port.isUDP()).toBe(false);
        });
        it('should create valid UDP port', () => {
            const port = port_value_object_1.Port.udp(53);
            expect(port.number).toBe(53);
            expect(port.protocol).toBe(port_value_object_1.PortProtocol.UDP);
            expect(port.isUDP()).toBe(true);
            expect(port.isTCP()).toBe(false);
        });
        it('should create port with create method', () => {
            const port = port_value_object_1.Port.create(443, port_value_object_1.PortProtocol.TCP);
            expect(port.number).toBe(443);
            expect(port.protocol).toBe(port_value_object_1.PortProtocol.TCP);
        });
        it('should create port from string format', () => {
            const port = port_value_object_1.Port.fromString('tcp/80');
            expect(port.number).toBe(80);
            expect(port.protocol).toBe(port_value_object_1.PortProtocol.TCP);
        });
        it('should validate port number range', () => {
            expect(() => port_value_object_1.Port.tcp(-1)).toThrow('Port number must be between 0 and 65535');
            expect(() => port_value_object_1.Port.tcp(65536)).toThrow('Port number must be between 0 and 65535');
            expect(() => port_value_object_1.Port.tcp(1.5)).toThrow('Port number must be an integer');
        });
        it('should validate protocol', () => {
            expect(() => port_value_object_1.Port.create(80, 'invalid')).toThrow('Invalid protocol: invalid');
        });
        it('should handle edge case port numbers', () => {
            const port0 = port_value_object_1.Port.tcp(0);
            const port65535 = port_value_object_1.Port.tcp(65535);
            expect(port0.number).toBe(0);
            expect(port65535.number).toBe(65535);
        });
    });
    describe('Port Classification', () => {
        it('should classify well-known ports', () => {
            const wellKnownPorts = [0, 80, 443, 1023];
            wellKnownPorts.forEach(portNum => {
                const port = port_value_object_1.Port.tcp(portNum);
                expect(port.getClassification()).toBe(port_value_object_1.PortClass.WELL_KNOWN);
                expect(port.isWellKnown()).toBe(true);
                expect(port.isRegistered()).toBe(false);
                expect(port.isDynamic()).toBe(false);
            });
        });
        it('should classify registered ports', () => {
            const registeredPorts = [1024, 8080, 49151];
            registeredPorts.forEach(portNum => {
                const port = port_value_object_1.Port.tcp(portNum);
                expect(port.getClassification()).toBe(port_value_object_1.PortClass.REGISTERED);
                expect(port.isRegistered()).toBe(true);
                expect(port.isWellKnown()).toBe(false);
                expect(port.isDynamic()).toBe(false);
            });
        });
        it('should classify dynamic ports', () => {
            const dynamicPorts = [49152, 60000, 65535];
            dynamicPorts.forEach(portNum => {
                const port = port_value_object_1.Port.tcp(portNum);
                expect(port.getClassification()).toBe(port_value_object_1.PortClass.DYNAMIC);
                expect(port.isDynamic()).toBe(true);
                expect(port.isWellKnown()).toBe(false);
                expect(port.isRegistered()).toBe(false);
            });
        });
    });
    describe('Service Identification', () => {
        it('should identify common TCP services', () => {
            const services = [
                { port: 22, service: 'SSH' },
                { port: 80, service: 'HTTP' },
                { port: 443, service: 'HTTPS' },
                { port: 25, service: 'SMTP' },
                { port: 21, service: 'FTP Control' },
            ];
            services.forEach(({ port: portNum, service }) => {
                const port = port_value_object_1.Port.tcp(portNum);
                expect(port.getServiceName()).toBe(service);
            });
        });
        it('should identify common UDP services', () => {
            const services = [
                { port: 53, service: 'DNS' },
                { port: 123, service: 'NTP' },
                { port: 161, service: 'SNMP' },
            ];
            services.forEach(({ port: portNum, service }) => {
                const port = port_value_object_1.Port.udp(portNum);
                expect(port.getServiceName()).toBe(service);
            });
        });
        it('should return null for unknown services', () => {
            const port = port_value_object_1.Port.tcp(12345);
            expect(port.getServiceName()).toBeNull();
        });
    });
    describe('Security Risk Assessment', () => {
        it('should identify critical risk ports', () => {
            const criticalPorts = [
                { port: 23, protocol: port_value_object_1.PortProtocol.TCP }, // Telnet
                { port: 21, protocol: port_value_object_1.PortProtocol.TCP }, // FTP
                { port: 135, protocol: port_value_object_1.PortProtocol.TCP }, // RPC
            ];
            criticalPorts.forEach(({ port: portNum, protocol }) => {
                const port = port_value_object_1.Port.create(portNum, protocol);
                expect(port.getRiskLevel()).toBe(port_value_object_1.PortRiskLevel.CRITICAL);
                expect(port.isHighRisk()).toBe(true);
            });
        });
        it('should identify high risk ports', () => {
            const highRiskPorts = [
                { port: 22, protocol: port_value_object_1.PortProtocol.TCP }, // SSH
                { port: 3389, protocol: port_value_object_1.PortProtocol.TCP }, // RDP
                { port: 1433, protocol: port_value_object_1.PortProtocol.TCP }, // SQL Server
            ];
            highRiskPorts.forEach(({ port: portNum, protocol }) => {
                const port = port_value_object_1.Port.create(portNum, protocol);
                expect(port.getRiskLevel()).toBe(port_value_object_1.PortRiskLevel.HIGH);
                expect(port.isHighRisk()).toBe(true);
            });
        });
        it('should identify medium risk ports', () => {
            const mediumRiskPorts = [
                { port: 25, protocol: port_value_object_1.PortProtocol.TCP }, // SMTP
                { port: 80, protocol: port_value_object_1.PortProtocol.TCP }, // HTTP
                { port: 8080, protocol: port_value_object_1.PortProtocol.TCP }, // HTTP Alt
            ];
            mediumRiskPorts.forEach(({ port: portNum, protocol }) => {
                const port = port_value_object_1.Port.create(portNum, protocol);
                expect(port.getRiskLevel()).toBe(port_value_object_1.PortRiskLevel.MEDIUM);
                expect(port.isHighRisk()).toBe(false);
            });
        });
        it('should identify low risk ports', () => {
            const port = port_value_object_1.Port.tcp(12345);
            expect(port.getRiskLevel()).toBe(port_value_object_1.PortRiskLevel.LOW);
            expect(port.isHighRisk()).toBe(false);
        });
        it('should identify commonly attacked ports', () => {
            const commonTargets = [22, 80, 443, 3389, 1433];
            commonTargets.forEach(portNum => {
                const port = port_value_object_1.Port.tcp(portNum);
                expect(port.isCommonlyAttacked()).toBe(true);
            });
        });
        it('should identify ports that should be monitored', () => {
            const monitoredPorts = [22, 80, 443, 23, 21]; // Mix of high-risk and well-known
            monitoredPorts.forEach(portNum => {
                const port = port_value_object_1.Port.tcp(portNum);
                expect(port.shouldMonitor()).toBe(true);
            });
        });
    });
    describe('Security Recommendations', () => {
        it('should provide SSH security recommendations', () => {
            const port = port_value_object_1.Port.tcp(22);
            const recommendations = port.getSecurityRecommendations();
            expect(recommendations).toContain('Use key-based authentication');
            expect(recommendations).toContain('Disable root login');
            expect(recommendations).toContain('Change default port if possible');
        });
        it('should provide Telnet security recommendations', () => {
            const port = port_value_object_1.Port.tcp(23);
            const recommendations = port.getSecurityRecommendations();
            expect(recommendations).toContain('Replace with SSH for secure remote access');
            expect(recommendations).toContain('Consider disabling this service if not required');
        });
        it('should provide HTTP security recommendations', () => {
            const port = port_value_object_1.Port.tcp(80);
            const recommendations = port.getSecurityRecommendations();
            expect(recommendations).toContain('Redirect to HTTPS');
        });
        it('should provide RDP security recommendations', () => {
            const port = port_value_object_1.Port.tcp(3389);
            const recommendations = port.getSecurityRecommendations();
            expect(recommendations).toContain('Enable Network Level Authentication');
            expect(recommendations).toContain('Use VPN for remote access');
        });
        it('should provide general recommendations for commonly attacked ports', () => {
            const port = port_value_object_1.Port.tcp(22);
            const recommendations = port.getSecurityRecommendations();
            expect(recommendations).toContain('Implement fail2ban or similar protection');
            expect(recommendations).toContain('Use non-standard ports when possible');
        });
    });
    describe('Encryption Detection', () => {
        it('should identify encrypted ports', () => {
            const encryptedPorts = [22, 443, 465, 587, 993, 995];
            encryptedPorts.forEach(portNum => {
                const port = port_value_object_1.Port.tcp(portNum);
                expect(port.isEncrypted()).toBe(true);
            });
        });
        it('should identify unencrypted ports', () => {
            const unencryptedPorts = [21, 23, 25, 80, 110, 143];
            unencryptedPorts.forEach(portNum => {
                const port = port_value_object_1.Port.tcp(portNum);
                expect(port.isEncrypted()).toBe(false);
            });
        });
    });
    describe('String Parsing and Formatting', () => {
        it('should parse protocol/port format', () => {
            const testCases = [
                { input: 'tcp/80', expectedPort: 80, expectedProtocol: port_value_object_1.PortProtocol.TCP },
                { input: 'udp/53', expectedPort: 53, expectedProtocol: port_value_object_1.PortProtocol.UDP },
                { input: 'TCP/443', expectedPort: 443, expectedProtocol: port_value_object_1.PortProtocol.TCP },
            ];
            testCases.forEach(({ input, expectedPort, expectedProtocol }) => {
                const port = port_value_object_1.Port.fromString(input);
                expect(port.number).toBe(expectedPort);
                expect(port.protocol).toBe(expectedProtocol);
            });
        });
        it('should handle invalid string formats', () => {
            expect(() => port_value_object_1.Port.fromString('invalid')).toThrow('Port string must be in format "protocol/port"');
            expect(() => port_value_object_1.Port.fromString('tcp/invalid')).toThrow();
            expect(() => port_value_object_1.Port.fromString('invalid/80')).toThrow('Invalid protocol: invalid');
        });
        it('should parse port numbers with fallback to TCP', () => {
            const port = port_value_object_1.Port.parse('80');
            expect(port?.number).toBe(80);
            expect(port?.protocol).toBe(port_value_object_1.PortProtocol.TCP);
        });
        it('should return null for invalid parse input', () => {
            expect(port_value_object_1.Port.parse('invalid')).toBeNull();
            expect(port_value_object_1.Port.parse('')).toBeNull();
        });
        it('should format port as string', () => {
            const port = port_value_object_1.Port.tcp(80);
            expect(port.toString()).toBe('tcp/80');
        });
    });
    describe('Port Information', () => {
        it('should provide comprehensive port information', () => {
            const port = port_value_object_1.Port.tcp(22);
            const info = port.getPortInfo();
            expect(info.number).toBe(22);
            expect(info.protocol).toBe('tcp');
            expect(info.classification).toBe(port_value_object_1.PortClass.WELL_KNOWN);
            expect(info.serviceName).toBe('SSH');
            expect(info.riskLevel).toBe(port_value_object_1.PortRiskLevel.HIGH);
            expect(info.isEncrypted).toBe(true);
            expect(info.isCommonlyAttacked).toBe(true);
            expect(info.recommendations).toBeInstanceOf(Array);
            expect(info.recommendations.length).toBeGreaterThan(0);
        });
        it('should get range category', () => {
            expect(port_value_object_1.Port.tcp(0).getRangeCategory()).toBe('Reserved');
            expect(port_value_object_1.Port.tcp(80).getRangeCategory()).toBe('System/Well-Known');
            expect(port_value_object_1.Port.tcp(8080).getRangeCategory()).toBe('User/Registered');
            expect(port_value_object_1.Port.tcp(60000).getRangeCategory()).toBe('Dynamic/Private');
        });
    });
    describe('Equality and Comparison', () => {
        it('should compare ports for equality', () => {
            const port1 = port_value_object_1.Port.tcp(80);
            const port2 = port_value_object_1.Port.tcp(80);
            const port3 = port_value_object_1.Port.tcp(443);
            const port4 = port_value_object_1.Port.udp(80);
            expect(port1.equals(port2)).toBe(true);
            expect(port1.equals(port3)).toBe(false);
            expect(port1.equals(port4)).toBe(false);
            expect(port1.equals(undefined)).toBe(false);
        });
        it('should handle same instance comparison', () => {
            const port = port_value_object_1.Port.tcp(80);
            expect(port.equals(port)).toBe(true);
        });
    });
    describe('JSON Serialization', () => {
        it('should serialize to JSON', () => {
            const port = port_value_object_1.Port.tcp(22);
            const json = port.toJSON();
            expect(json.number).toBe(22);
            expect(json.protocol).toBe('tcp');
            expect(json.string).toBe('tcp/22');
            expect(json.serviceName).toBe('SSH');
            expect(json.riskLevel).toBe(port_value_object_1.PortRiskLevel.HIGH);
        });
        it('should deserialize from JSON', () => {
            const json = { number: 443, protocol: port_value_object_1.PortProtocol.TCP };
            const port = port_value_object_1.Port.fromJSON(json);
            expect(port.number).toBe(443);
            expect(port.protocol).toBe(port_value_object_1.PortProtocol.TCP);
        });
    });
    describe('Validation Methods', () => {
        it('should validate port without creating instance', () => {
            expect(port_value_object_1.Port.isValid(80, port_value_object_1.PortProtocol.TCP)).toBe(true);
            expect(port_value_object_1.Port.isValid(65536, port_value_object_1.PortProtocol.TCP)).toBe(false);
            expect(port_value_object_1.Port.isValid(-1, port_value_object_1.PortProtocol.TCP)).toBe(false);
        });
    });
    describe('Edge Cases', () => {
        it('should handle port 0', () => {
            const port = port_value_object_1.Port.tcp(0);
            expect(port.number).toBe(0);
            expect(port.getRangeCategory()).toBe('Reserved');
        });
        it('should handle maximum port number', () => {
            const port = port_value_object_1.Port.tcp(65535);
            expect(port.number).toBe(65535);
            expect(port.isDynamic()).toBe(true);
        });
        it('should handle all protocol types', () => {
            const protocols = [port_value_object_1.PortProtocol.TCP, port_value_object_1.PortProtocol.UDP, port_value_object_1.PortProtocol.SCTP, port_value_object_1.PortProtocol.DCCP];
            protocols.forEach(protocol => {
                const port = port_value_object_1.Port.create(80, protocol);
                expect(port.protocol).toBe(protocol);
            });
        });
        it('should handle case-insensitive protocol parsing', () => {
            const port = port_value_object_1.Port.fromString('TCP/80');
            expect(port.protocol).toBe(port_value_object_1.PortProtocol.TCP);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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