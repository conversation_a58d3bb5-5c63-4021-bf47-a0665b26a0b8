9ca5d93c0b86c630a8941045d90ca118
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIModelAggregate = void 0;
const ai_model_entity_1 = require("../entities/ai-model.entity");
const ai_model_factory_1 = require("../factories/ai-model.factory");
class AIModelAggregate {
    constructor(models = []) {
        this.models = new Map();
        models.forEach(model => {
            this.models.set(model.id.toString(), model);
        });
    }
    /**
     * Creates a new AI model and adds it to the aggregate
     */
    createModel(request, id) {
        // Check for duplicate names within the same provider
        const existingModel = this.findModelByNameAndProvider(request.name, request.provider);
        if (existingModel) {
            throw new Error(`Model with name '${request.name}' already exists for provider '${request.provider}'`);
        }
        const model = ai_model_factory_1.AIModelFactory.create(request, id);
        this.models.set(model.id.toString(), model);
        return model;
    }
    /**
     * Adds an existing model to the aggregate
     */
    addModel(model) {
        if (this.models.has(model.id.toString())) {
            throw new Error(`Model with ID '${model.id.toString()}' already exists in aggregate`);
        }
        this.models.set(model.id.toString(), model);
    }
    /**
     * Removes a model from the aggregate
     */
    removeModel(modelId) {
        const model = this.models.get(modelId.toString());
        if (!model) {
            throw new Error(`Model with ID '${modelId.toString()}' not found`);
        }
        // Archive the model before removing
        model.archive();
        this.models.delete(modelId.toString());
    }
    /**
     * Gets a model by ID
     */
    getModel(modelId) {
        return this.models.get(modelId.toString());
    }
    /**
     * Gets all models
     */
    getAllModels() {
        return Array.from(this.models.values());
    }
    /**
     * Gets active models only
     */
    getActiveModels() {
        return Array.from(this.models.values()).filter(model => model.status === ai_model_entity_1.ModelStatus.ACTIVE);
    }
    /**
     * Finds models by criteria
     */
    findModels(criteria) {
        return Array.from(this.models.values()).filter(model => {
            if (criteria.taskType && !model.supportsTaskType(criteria.taskType)) {
                return false;
            }
            if (criteria.provider && model.provider !== criteria.provider) {
                return false;
            }
            if (criteria.modelType && model.modelType !== criteria.modelType) {
                return false;
            }
            if (criteria.minAccuracy && model.performance.accuracy < criteria.minAccuracy) {
                return false;
            }
            if (criteria.maxLatency && model.performance.averageLatency > criteria.maxLatency) {
                return false;
            }
            if (criteria.requiresAvailability && !model.canHandleRequest()) {
                return false;
            }
            return true;
        });
    }
    /**
     * Selects the best model for a task based on criteria
     */
    selectBestModel(criteria) {
        const candidates = this.findModels(criteria);
        if (candidates.length === 0) {
            return undefined;
        }
        if (candidates.length === 1) {
            return candidates[0];
        }
        // Score models based on performance, availability, and load
        const scoredModels = candidates.map(model => ({
            model,
            score: this.calculateModelScore(model, criteria),
        }));
        // Sort by score (highest first)
        scoredModels.sort((a, b) => b.score - a.score);
        return scoredModels[0].model;
    }
    /**
     * Distributes load across multiple models using load balancing
     */
    selectModelForLoadBalancing(criteria, strategy = { type: 'least_loaded' }) {
        const candidates = this.findModels(criteria);
        if (candidates.length === 0) {
            return undefined;
        }
        switch (strategy.type) {
            case 'round_robin':
                return this.selectRoundRobin(candidates);
            case 'weighted':
                return this.selectWeighted(candidates, strategy.weights);
            case 'least_loaded':
                return this.selectLeastLoaded(candidates);
            case 'performance_based':
                return this.selectPerformanceBased(candidates);
            default:
                return candidates[0];
        }
    }
    /**
     * Gets aggregate statistics
     */
    getStatistics() {
        const models = Array.from(this.models.values());
        const statusCounts = models.reduce((acc, model) => {
            acc[model.status] = (acc[model.status] || 0) + 1;
            return acc;
        }, {});
        const providerDistribution = models.reduce((acc, model) => {
            acc[model.provider] = (acc[model.provider] || 0) + 1;
            return acc;
        }, {});
        const typeDistribution = models.reduce((acc, model) => {
            acc[model.modelType] = (acc[model.modelType] || 0) + 1;
            return acc;
        }, {});
        const totalLoad = models.reduce((sum, model) => sum + model.currentLoad, 0);
        const totalUtilization = models.reduce((sum, model) => sum + model.getUtilization(), 0);
        const totalPerformanceScore = models.reduce((sum, model) => sum + model.calculatePerformanceScore(), 0);
        return {
            totalModels: models.length,
            activeModels: statusCounts[ai_model_entity_1.ModelStatus.ACTIVE] || 0,
            inactiveModels: statusCounts[ai_model_entity_1.ModelStatus.INACTIVE] || 0,
            archivedModels: statusCounts[ai_model_entity_1.ModelStatus.ARCHIVED] || 0,
            averagePerformanceScore: models.length > 0 ? totalPerformanceScore / models.length : 0,
            totalLoad,
            averageUtilization: models.length > 0 ? totalUtilization / models.length : 0,
            providerDistribution,
            typeDistribution,
        };
    }
    /**
     * Activates all models matching criteria
     */
    activateModels(criteria) {
        const models = this.findModels(criteria);
        models.forEach(model => model.activate());
        return models;
    }
    /**
     * Deactivates all models matching criteria
     */
    deactivateModels(criteria) {
        const models = this.findModels(criteria);
        models.forEach(model => model.deactivate());
        return models;
    }
    /**
     * Archives all models matching criteria
     */
    archiveModels(criteria) {
        const models = this.findModels(criteria);
        models.forEach(model => model.archive());
        return models;
    }
    /**
     * Gets models that are overloaded
     */
    getOverloadedModels() {
        return Array.from(this.models.values()).filter(model => model.isOverloaded());
    }
    /**
     * Gets models with low performance
     */
    getLowPerformanceModels(threshold = 0.5) {
        return Array.from(this.models.values()).filter(model => model.calculatePerformanceScore() < threshold);
    }
    /**
     * Rebalances load across models
     */
    rebalanceLoad() {
        const activeModels = this.getActiveModels();
        const overloadedModels = activeModels.filter(model => model.isOverloaded());
        if (overloadedModels.length === 0) {
            return;
        }
        // Simple rebalancing: deactivate overloaded models temporarily
        overloadedModels.forEach(model => {
            if (activeModels.length > 1) { // Don't deactivate if it's the only model
                model.deactivate();
            }
        });
    }
    // Private helper methods
    findModelByNameAndProvider(name, provider) {
        return Array.from(this.models.values()).find(model => model.name === name && model.provider === provider);
    }
    calculateModelScore(model, criteria) {
        let score = model.calculatePerformanceScore();
        // Adjust score based on current load
        const utilizationPenalty = model.getUtilization() * 0.2;
        score -= utilizationPenalty;
        // Boost score for models that exactly match criteria
        if (criteria.taskType && model.supportsTaskType(criteria.taskType)) {
            score += 0.1;
        }
        // Apply priority and weight
        score = score * model.priority * model.weight;
        return Math.max(0, Math.min(1, score));
    }
    selectRoundRobin(candidates) {
        // Simple round-robin based on model ID hash
        const timestamp = Date.now();
        const index = timestamp % candidates.length;
        return candidates[index];
    }
    selectWeighted(candidates, weights) {
        if (!weights) {
            return this.selectLeastLoaded(candidates);
        }
        const weightedCandidates = candidates.map(model => ({
            model,
            weight: weights[model.id.toString()] || model.weight,
        }));
        const totalWeight = weightedCandidates.reduce((sum, item) => sum + item.weight, 0);
        const random = Math.random() * totalWeight;
        let currentWeight = 0;
        for (const item of weightedCandidates) {
            currentWeight += item.weight;
            if (random <= currentWeight) {
                return item.model;
            }
        }
        return candidates[0];
    }
    selectLeastLoaded(candidates) {
        return candidates.reduce((least, current) => current.getUtilization() < least.getUtilization() ? current : least);
    }
    selectPerformanceBased(candidates) {
        return candidates.reduce((best, current) => current.calculatePerformanceScore() > best.calculatePerformanceScore() ? current : best);
    }
}
exports.AIModelAggregate = AIModelAggregate;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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