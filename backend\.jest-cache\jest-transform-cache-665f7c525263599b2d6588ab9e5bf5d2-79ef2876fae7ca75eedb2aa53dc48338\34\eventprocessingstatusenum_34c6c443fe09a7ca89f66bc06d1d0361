240dcf23f5add4cee9a104a0d85abc0c
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventProcessingStatusUtils = exports.EventProcessingStatus = void 0;
/**
 * Event Processing Status Enum
 *
 * Represents the various stages of event processing in the security pipeline.
 * Used to track the lifecycle of security events from ingestion to resolution.
 *
 * Processing Flow:
 * RAW -> NORMALIZED -> ENRICHED -> CORRELATED -> ANALYZED -> RESOLVED/ARCHIVED
 */
var EventProcessingStatus;
(function (EventProcessingStatus) {
    /**
     * Event has been received but not yet processed
     * Initial state when event enters the system
     */
    EventProcessingStatus["RAW"] = "raw";
    /**
     * Event is currently being normalized
     * Transformation from raw format to standard schema
     */
    EventProcessingStatus["NORMALIZING"] = "normalizing";
    /**
     * Event has been normalized to standard format
     * Basic parsing and field extraction completed
     */
    EventProcessingStatus["NORMALIZED"] = "normalized";
    /**
     * Event is being enriched with additional context
     * Adding threat intelligence, asset information, etc.
     */
    EventProcessingStatus["ENRICHING"] = "enriching";
    /**
     * Event has been enriched with contextual data
     * Additional metadata and intelligence added
     */
    EventProcessingStatus["ENRICHED"] = "enriched";
    /**
     * Event is being correlated with other events
     * Pattern matching and relationship analysis
     */
    EventProcessingStatus["CORRELATING"] = "correlating";
    /**
     * Event has been correlated with related events
     * Relationships and patterns identified
     */
    EventProcessingStatus["CORRELATED"] = "correlated";
    /**
     * Event is being analyzed for threats
     * AI/ML analysis and risk assessment
     */
    EventProcessingStatus["ANALYZING"] = "analyzing";
    /**
     * Event analysis is complete
     * Threat assessment and scoring finished
     */
    EventProcessingStatus["ANALYZED"] = "analyzed";
    /**
     * Event requires manual review
     * Escalated for human analysis
     */
    EventProcessingStatus["PENDING_REVIEW"] = "pending_review";
    /**
     * Event is under investigation
     * Active incident response in progress
     */
    EventProcessingStatus["INVESTIGATING"] = "investigating";
    /**
     * Event has been resolved
     * Issue addressed and closed
     */
    EventProcessingStatus["RESOLVED"] = "resolved";
    /**
     * Event has been archived
     * Long-term storage for compliance
     */
    EventProcessingStatus["ARCHIVED"] = "archived";
    /**
     * Event processing failed
     * Error occurred during processing
     */
    EventProcessingStatus["FAILED"] = "failed";
    /**
     * Event was discarded as false positive
     * Determined to be benign or noise
     */
    EventProcessingStatus["DISCARDED"] = "discarded";
    /**
     * Event processing was skipped
     * Filtered out by rules or policies
     */
    EventProcessingStatus["SKIPPED"] = "skipped";
    /**
     * Event is on hold
     * Processing paused pending external input
     */
    EventProcessingStatus["ON_HOLD"] = "on_hold";
    /**
     * Event processing timed out
     * Exceeded maximum processing time
     */
    EventProcessingStatus["TIMEOUT"] = "timeout";
    /**
     * Event is being reprocessed
     * Retry after failure or update
     */
    EventProcessingStatus["REPROCESSING"] = "reprocessing";
})(EventProcessingStatus || (exports.EventProcessingStatus = EventProcessingStatus = {}));
/**
 * Event Processing Status Utilities
 */
class EventProcessingStatusUtils {
    /**
     * Get all processing statuses
     */
    static getAllStatuses() {
        return Object.values(EventProcessingStatus);
    }
    /**
     * Get active processing statuses (not terminal states)
     */
    static getActiveStatuses() {
        return [
            EventProcessingStatus.RAW,
            EventProcessingStatus.NORMALIZING,
            EventProcessingStatus.NORMALIZED,
            EventProcessingStatus.ENRICHING,
            EventProcessingStatus.ENRICHED,
            EventProcessingStatus.CORRELATING,
            EventProcessingStatus.CORRELATED,
            EventProcessingStatus.ANALYZING,
            EventProcessingStatus.ANALYZED,
            EventProcessingStatus.PENDING_REVIEW,
            EventProcessingStatus.INVESTIGATING,
            EventProcessingStatus.ON_HOLD,
            EventProcessingStatus.REPROCESSING,
        ];
    }
    /**
     * Get terminal processing statuses (final states)
     */
    static getTerminalStatuses() {
        return [
            EventProcessingStatus.RESOLVED,
            EventProcessingStatus.ARCHIVED,
            EventProcessingStatus.FAILED,
            EventProcessingStatus.DISCARDED,
            EventProcessingStatus.SKIPPED,
            EventProcessingStatus.TIMEOUT,
        ];
    }
    /**
     * Get processing statuses that indicate success
     */
    static getSuccessStatuses() {
        return [
            EventProcessingStatus.NORMALIZED,
            EventProcessingStatus.ENRICHED,
            EventProcessingStatus.CORRELATED,
            EventProcessingStatus.ANALYZED,
            EventProcessingStatus.RESOLVED,
            EventProcessingStatus.ARCHIVED,
        ];
    }
    /**
     * Get processing statuses that indicate failure or issues
     */
    static getFailureStatuses() {
        return [
            EventProcessingStatus.FAILED,
            EventProcessingStatus.TIMEOUT,
        ];
    }
    /**
     * Get processing statuses that are in progress
     */
    static getInProgressStatuses() {
        return [
            EventProcessingStatus.NORMALIZING,
            EventProcessingStatus.ENRICHING,
            EventProcessingStatus.CORRELATING,
            EventProcessingStatus.ANALYZING,
            EventProcessingStatus.INVESTIGATING,
            EventProcessingStatus.REPROCESSING,
        ];
    }
    /**
     * Check if a status is terminal (final state)
     */
    static isTerminal(status) {
        return EventProcessingStatusUtils.getTerminalStatuses().includes(status);
    }
    /**
     * Check if a status is active (not terminal)
     */
    static isActive(status) {
        return EventProcessingStatusUtils.getActiveStatuses().includes(status);
    }
    /**
     * Check if a status indicates success
     */
    static isSuccess(status) {
        return EventProcessingStatusUtils.getSuccessStatuses().includes(status);
    }
    /**
     * Check if a status indicates failure
     */
    static isFailure(status) {
        return EventProcessingStatusUtils.getFailureStatuses().includes(status);
    }
    /**
     * Check if a status is in progress
     */
    static isInProgress(status) {
        return EventProcessingStatusUtils.getInProgressStatuses().includes(status);
    }
    /**
     * Check if a status requires human attention
     */
    static requiresAttention(status) {
        return [
            EventProcessingStatus.PENDING_REVIEW,
            EventProcessingStatus.INVESTIGATING,
            EventProcessingStatus.FAILED,
            EventProcessingStatus.ON_HOLD,
            EventProcessingStatus.TIMEOUT,
        ].includes(status);
    }
    /**
     * Get the next logical status in the processing pipeline
     */
    static getNextStatus(currentStatus) {
        const statusFlow = {
            [EventProcessingStatus.RAW]: EventProcessingStatus.NORMALIZING,
            [EventProcessingStatus.NORMALIZING]: EventProcessingStatus.NORMALIZED,
            [EventProcessingStatus.NORMALIZED]: EventProcessingStatus.ENRICHING,
            [EventProcessingStatus.ENRICHING]: EventProcessingStatus.ENRICHED,
            [EventProcessingStatus.ENRICHED]: EventProcessingStatus.CORRELATING,
            [EventProcessingStatus.CORRELATING]: EventProcessingStatus.CORRELATED,
            [EventProcessingStatus.CORRELATED]: EventProcessingStatus.ANALYZING,
            [EventProcessingStatus.ANALYZING]: EventProcessingStatus.ANALYZED,
            [EventProcessingStatus.ANALYZED]: EventProcessingStatus.RESOLVED,
            [EventProcessingStatus.PENDING_REVIEW]: EventProcessingStatus.INVESTIGATING,
            [EventProcessingStatus.INVESTIGATING]: EventProcessingStatus.RESOLVED,
            [EventProcessingStatus.REPROCESSING]: EventProcessingStatus.NORMALIZED,
            [EventProcessingStatus.ON_HOLD]: null, // Requires manual intervention
            [EventProcessingStatus.RESOLVED]: EventProcessingStatus.ARCHIVED,
            [EventProcessingStatus.ARCHIVED]: null, // Terminal
            [EventProcessingStatus.FAILED]: EventProcessingStatus.REPROCESSING,
            [EventProcessingStatus.DISCARDED]: null, // Terminal
            [EventProcessingStatus.SKIPPED]: null, // Terminal
            [EventProcessingStatus.TIMEOUT]: EventProcessingStatus.REPROCESSING,
        };
        return statusFlow[currentStatus] || null;
    }
    /**
     * Get the previous status in the processing pipeline
     */
    static getPreviousStatus(currentStatus) {
        const reverseFlow = {
            [EventProcessingStatus.RAW]: null, // Initial state
            [EventProcessingStatus.NORMALIZING]: EventProcessingStatus.RAW,
            [EventProcessingStatus.NORMALIZED]: EventProcessingStatus.NORMALIZING,
            [EventProcessingStatus.ENRICHING]: EventProcessingStatus.NORMALIZED,
            [EventProcessingStatus.ENRICHED]: EventProcessingStatus.ENRICHING,
            [EventProcessingStatus.CORRELATING]: EventProcessingStatus.ENRICHED,
            [EventProcessingStatus.CORRELATED]: EventProcessingStatus.CORRELATING,
            [EventProcessingStatus.ANALYZING]: EventProcessingStatus.CORRELATED,
            [EventProcessingStatus.ANALYZED]: EventProcessingStatus.ANALYZING,
            [EventProcessingStatus.PENDING_REVIEW]: EventProcessingStatus.ANALYZED,
            [EventProcessingStatus.INVESTIGATING]: EventProcessingStatus.PENDING_REVIEW,
            [EventProcessingStatus.RESOLVED]: EventProcessingStatus.INVESTIGATING,
            [EventProcessingStatus.ARCHIVED]: EventProcessingStatus.RESOLVED,
            [EventProcessingStatus.FAILED]: null, // Error state
            [EventProcessingStatus.DISCARDED]: null, // Decision state
            [EventProcessingStatus.SKIPPED]: null, // Filter state
            [EventProcessingStatus.ON_HOLD]: null, // Manual state
            [EventProcessingStatus.TIMEOUT]: null, // Error state
            [EventProcessingStatus.REPROCESSING]: EventProcessingStatus.FAILED,
        };
        return reverseFlow[currentStatus] || null;
    }
    /**
     * Get valid transition statuses from current status
     */
    static getValidTransitions(currentStatus) {
        const transitions = {
            [EventProcessingStatus.RAW]: [
                EventProcessingStatus.NORMALIZING,
                EventProcessingStatus.SKIPPED,
                EventProcessingStatus.FAILED,
            ],
            [EventProcessingStatus.NORMALIZING]: [
                EventProcessingStatus.NORMALIZED,
                EventProcessingStatus.FAILED,
                EventProcessingStatus.TIMEOUT,
            ],
            [EventProcessingStatus.NORMALIZED]: [
                EventProcessingStatus.ENRICHING,
                EventProcessingStatus.SKIPPED,
                EventProcessingStatus.FAILED,
            ],
            [EventProcessingStatus.ENRICHING]: [
                EventProcessingStatus.ENRICHED,
                EventProcessingStatus.FAILED,
                EventProcessingStatus.TIMEOUT,
            ],
            [EventProcessingStatus.ENRICHED]: [
                EventProcessingStatus.CORRELATING,
                EventProcessingStatus.ANALYZING,
                EventProcessingStatus.FAILED,
            ],
            [EventProcessingStatus.CORRELATING]: [
                EventProcessingStatus.CORRELATED,
                EventProcessingStatus.FAILED,
                EventProcessingStatus.TIMEOUT,
            ],
            [EventProcessingStatus.CORRELATED]: [
                EventProcessingStatus.ANALYZING,
                EventProcessingStatus.FAILED,
            ],
            [EventProcessingStatus.ANALYZING]: [
                EventProcessingStatus.ANALYZED,
                EventProcessingStatus.FAILED,
                EventProcessingStatus.TIMEOUT,
            ],
            [EventProcessingStatus.ANALYZED]: [
                EventProcessingStatus.PENDING_REVIEW,
                EventProcessingStatus.RESOLVED,
                EventProcessingStatus.DISCARDED,
            ],
            [EventProcessingStatus.PENDING_REVIEW]: [
                EventProcessingStatus.INVESTIGATING,
                EventProcessingStatus.DISCARDED,
                EventProcessingStatus.ON_HOLD,
            ],
            [EventProcessingStatus.INVESTIGATING]: [
                EventProcessingStatus.RESOLVED,
                EventProcessingStatus.ON_HOLD,
                EventProcessingStatus.PENDING_REVIEW,
            ],
            [EventProcessingStatus.RESOLVED]: [
                EventProcessingStatus.ARCHIVED,
                EventProcessingStatus.REPROCESSING,
            ],
            [EventProcessingStatus.ARCHIVED]: [], // Terminal
            [EventProcessingStatus.FAILED]: [
                EventProcessingStatus.REPROCESSING,
                EventProcessingStatus.DISCARDED,
            ],
            [EventProcessingStatus.DISCARDED]: [], // Terminal
            [EventProcessingStatus.SKIPPED]: [], // Terminal
            [EventProcessingStatus.ON_HOLD]: [
                EventProcessingStatus.INVESTIGATING,
                EventProcessingStatus.PENDING_REVIEW,
                EventProcessingStatus.DISCARDED,
            ],
            [EventProcessingStatus.TIMEOUT]: [
                EventProcessingStatus.REPROCESSING,
                EventProcessingStatus.DISCARDED,
            ],
            [EventProcessingStatus.REPROCESSING]: [
                EventProcessingStatus.NORMALIZED,
                EventProcessingStatus.FAILED,
            ],
        };
        return transitions[currentStatus] || [];
    }
    /**
     * Check if a status transition is valid
     */
    static isValidTransition(from, to) {
        return EventProcessingStatusUtils.getValidTransitions(from).includes(to);
    }
    /**
     * Get human-readable description of status
     */
    static getDescription(status) {
        const descriptions = {
            [EventProcessingStatus.RAW]: 'Event received and queued for processing',
            [EventProcessingStatus.NORMALIZING]: 'Converting event to standard format',
            [EventProcessingStatus.NORMALIZED]: 'Event normalized to standard schema',
            [EventProcessingStatus.ENRICHING]: 'Adding contextual information to event',
            [EventProcessingStatus.ENRICHED]: 'Event enriched with additional context',
            [EventProcessingStatus.CORRELATING]: 'Analyzing relationships with other events',
            [EventProcessingStatus.CORRELATED]: 'Event correlated with related events',
            [EventProcessingStatus.ANALYZING]: 'Performing threat analysis and risk assessment',
            [EventProcessingStatus.ANALYZED]: 'Analysis complete, threat assessment available',
            [EventProcessingStatus.PENDING_REVIEW]: 'Awaiting manual review and decision',
            [EventProcessingStatus.INVESTIGATING]: 'Under active investigation',
            [EventProcessingStatus.RESOLVED]: 'Issue resolved and closed',
            [EventProcessingStatus.ARCHIVED]: 'Event archived for long-term storage',
            [EventProcessingStatus.FAILED]: 'Processing failed due to error',
            [EventProcessingStatus.DISCARDED]: 'Event discarded as false positive',
            [EventProcessingStatus.SKIPPED]: 'Event skipped by filtering rules',
            [EventProcessingStatus.ON_HOLD]: 'Processing paused pending external input',
            [EventProcessingStatus.TIMEOUT]: 'Processing timed out',
            [EventProcessingStatus.REPROCESSING]: 'Event being reprocessed after failure',
        };
        return descriptions[status] || 'Unknown status';
    }
    /**
     * Get status priority for sorting (lower number = higher priority)
     */
    static getPriority(status) {
        const priorities = {
            [EventProcessingStatus.FAILED]: 1,
            [EventProcessingStatus.TIMEOUT]: 2,
            [EventProcessingStatus.PENDING_REVIEW]: 3,
            [EventProcessingStatus.INVESTIGATING]: 4,
            [EventProcessingStatus.ON_HOLD]: 5,
            [EventProcessingStatus.ANALYZING]: 6,
            [EventProcessingStatus.CORRELATING]: 7,
            [EventProcessingStatus.ENRICHING]: 8,
            [EventProcessingStatus.NORMALIZING]: 9,
            [EventProcessingStatus.REPROCESSING]: 10,
            [EventProcessingStatus.RAW]: 11,
            [EventProcessingStatus.ANALYZED]: 12,
            [EventProcessingStatus.CORRELATED]: 13,
            [EventProcessingStatus.ENRICHED]: 14,
            [EventProcessingStatus.NORMALIZED]: 15,
            [EventProcessingStatus.RESOLVED]: 16,
            [EventProcessingStatus.DISCARDED]: 17,
            [EventProcessingStatus.SKIPPED]: 18,
            [EventProcessingStatus.ARCHIVED]: 19,
        };
        return priorities[status] || 999;
    }
}
exports.EventProcessingStatusUtils = EventProcessingStatusUtils;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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