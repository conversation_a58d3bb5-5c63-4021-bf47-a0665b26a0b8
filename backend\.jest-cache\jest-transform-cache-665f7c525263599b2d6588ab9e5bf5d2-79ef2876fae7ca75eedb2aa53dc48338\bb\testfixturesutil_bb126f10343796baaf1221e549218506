6e592d729a8e8862f77f572eac75b3e3
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestFixturesUtil = void 0;
const faker_1 = require("@faker-js/faker");
/**
 * Test Fixtures Utility
 *
 * Comprehensive test data fixtures providing:
 * - Realistic mock data generation with faker.js integration
 * - Predefined test scenarios and data sets
 * - Relationship-aware data generation
 * - Performance-optimized fixture creation
 * - Consistent test data across test suites
 * - Configurable data generation patterns
 * - Seed-based reproducible data generation
 * - Complex entity relationship fixtures
 */
class TestFixturesUtil {
    /**
     * Set seed for reproducible data generation
     */
    static setSeed(seed) {
        TestFixturesUtil.seed = seed;
        faker_1.faker.seed(seed);
    }
    /**
     * Reset seed to default
     */
    static resetSeed() {
        TestFixturesUtil.setSeed(12345);
    }
    /**
     * Generate user fixtures
     */
    static generateUser(overrides = {}) {
        const firstName = faker_1.faker.person.firstName();
        const lastName = faker_1.faker.person.lastName();
        const email = faker_1.faker.internet.email({ firstName, lastName });
        return {
            id: faker_1.faker.string.uuid(),
            email,
            username: faker_1.faker.internet.userName({ firstName, lastName }),
            firstName,
            lastName,
            password: faker_1.faker.internet.password({ length: 12 }),
            role: faker_1.faker.helpers.arrayElement(['admin', 'user', 'operator', 'analyst']),
            isActive: faker_1.faker.datatype.boolean({ probability: 0.9 }),
            lastLoginAt: faker_1.faker.date.recent({ days: 30 }),
            createdAt: faker_1.faker.date.past({ years: 2 }),
            updatedAt: faker_1.faker.date.recent({ days: 7 }),
            profile: {
                avatar: faker_1.faker.image.avatar(),
                bio: faker_1.faker.lorem.paragraph(),
                timezone: faker_1.faker.location.timeZone(),
                language: faker_1.faker.helpers.arrayElement(['en', 'es', 'fr', 'de']),
                preferences: {
                    theme: faker_1.faker.helpers.arrayElement(['light', 'dark']),
                    notifications: faker_1.faker.datatype.boolean(),
                    emailUpdates: faker_1.faker.datatype.boolean(),
                },
            },
            ...overrides,
        };
    }
    /**
     * Generate report definition fixtures
     */
    static generateReportDefinition(overrides = {}) {
        return {
            id: faker_1.faker.string.uuid(),
            name: faker_1.faker.company.buzzPhrase(),
            description: faker_1.faker.lorem.paragraph(),
            type: faker_1.faker.helpers.arrayElement(['dashboard', 'scheduled', 'ad_hoc', 'compliance']),
            category: faker_1.faker.helpers.arrayElement(['security', 'compliance', 'performance', 'business']),
            configuration: {
                dataSource: faker_1.faker.helpers.arrayElement(['database', 'api', 'file', 'stream']),
                refreshInterval: faker_1.faker.number.int({ min: 300, max: 86400 }),
                filters: {
                    dateRange: {
                        start: faker_1.faker.date.past({ years: 1 }),
                        end: faker_1.faker.date.recent(),
                    },
                    categories: faker_1.faker.helpers.arrayElements(['security', 'compliance', 'audit'], { min: 1, max: 3 }),
                },
                visualization: {
                    type: faker_1.faker.helpers.arrayElement(['table', 'chart', 'graph', 'dashboard']),
                    layout: faker_1.faker.helpers.arrayElement(['grid', 'list', 'cards']),
                    theme: faker_1.faker.helpers.arrayElement(['default', 'dark', 'light']),
                },
            },
            schedule: {
                enabled: faker_1.faker.datatype.boolean({ probability: 0.7 }),
                frequency: faker_1.faker.helpers.arrayElement(['daily', 'weekly', 'monthly', 'quarterly']),
                time: faker_1.faker.date.recent().toISOString(),
                timezone: faker_1.faker.location.timeZone(),
                recipients: faker_1.faker.helpers.arrayElements([
                    faker_1.faker.internet.email(),
                    faker_1.faker.internet.email(),
                    faker_1.faker.internet.email(),
                ], { min: 1, max: 3 }),
            },
            isActive: faker_1.faker.datatype.boolean({ probability: 0.8 }),
            createdBy: faker_1.faker.string.uuid(),
            updatedBy: faker_1.faker.string.uuid(),
            createdAt: faker_1.faker.date.past({ years: 1 }),
            updatedAt: faker_1.faker.date.recent({ days: 30 }),
            ...overrides,
        };
    }
    /**
     * Generate generated report fixtures
     */
    static generateGeneratedReport(overrides = {}) {
        const status = faker_1.faker.helpers.arrayElement(['pending', 'generating', 'completed', 'failed']);
        const startTime = faker_1.faker.date.recent({ days: 7 });
        const endTime = status === 'completed' ? faker_1.faker.date.between({ from: startTime, to: new Date() }) : null;
        return {
            id: faker_1.faker.string.uuid(),
            reportDefinitionId: faker_1.faker.string.uuid(),
            status,
            format: faker_1.faker.helpers.arrayElement(['pdf', 'excel', 'csv', 'json']),
            parameters: {
                dateRange: {
                    start: faker_1.faker.date.past({ years: 1 }),
                    end: faker_1.faker.date.recent(),
                },
                filters: {
                    department: faker_1.faker.helpers.arrayElement(['IT', 'Security', 'Compliance', 'Operations']),
                    severity: faker_1.faker.helpers.arrayElement(['low', 'medium', 'high', 'critical']),
                },
            },
            data: status === 'completed' ? {
                totalRecords: faker_1.faker.number.int({ min: 100, max: 10000 }),
                summary: {
                    totalItems: faker_1.faker.number.int({ min: 50, max: 5000 }),
                    categories: {
                        security: faker_1.faker.number.int({ min: 10, max: 100 }),
                        compliance: faker_1.faker.number.int({ min: 5, max: 50 }),
                        performance: faker_1.faker.number.int({ min: 20, max: 200 }),
                    },
                },
                charts: [
                    {
                        type: 'pie',
                        title: 'Distribution by Category',
                        data: [
                            { label: 'Security', value: faker_1.faker.number.int({ min: 10, max: 100 }) },
                            { label: 'Compliance', value: faker_1.faker.number.int({ min: 5, max: 50 }) },
                            { label: 'Performance', value: faker_1.faker.number.int({ min: 20, max: 200 }) },
                        ],
                    },
                ],
            } : null,
            metadata: {
                fileSize: status === 'completed' ? faker_1.faker.number.int({ min: 1024, max: 10485760 }) : null,
                filePath: status === 'completed' ? `/reports/${faker_1.faker.string.uuid()}.pdf` : null,
                checksum: status === 'completed' ? faker_1.faker.string.alphanumeric(32) : null,
                generationTime: endTime && startTime ? endTime.getTime() - startTime.getTime() : null,
            },
            error: status === 'failed' ? {
                message: faker_1.faker.lorem.sentence(),
                code: faker_1.faker.helpers.arrayElement(['TIMEOUT', 'DATA_ERROR', 'PERMISSION_DENIED']),
                stack: faker_1.faker.lorem.paragraphs(3),
            } : null,
            startedAt: startTime,
            completedAt: endTime,
            generatedBy: faker_1.faker.string.uuid(),
            createdAt: startTime,
            updatedAt: faker_1.faker.date.recent(),
            ...overrides,
        };
    }
    /**
     * Generate dashboard fixtures
     */
    static generateDashboard(overrides = {}) {
        return {
            id: faker_1.faker.string.uuid(),
            name: faker_1.faker.company.buzzPhrase(),
            description: faker_1.faker.lorem.paragraph(),
            layout: {
                columns: faker_1.faker.number.int({ min: 2, max: 4 }),
                rows: faker_1.faker.number.int({ min: 3, max: 6 }),
                widgets: Array.from({ length: faker_1.faker.number.int({ min: 4, max: 12 }) }, () => ({
                    id: faker_1.faker.string.uuid(),
                    type: faker_1.faker.helpers.arrayElement(['chart', 'table', 'metric', 'text']),
                    title: faker_1.faker.company.buzzPhrase(),
                    position: {
                        x: faker_1.faker.number.int({ min: 0, max: 3 }),
                        y: faker_1.faker.number.int({ min: 0, max: 5 }),
                        width: faker_1.faker.number.int({ min: 1, max: 2 }),
                        height: faker_1.faker.number.int({ min: 1, max: 2 }),
                    },
                    configuration: {
                        dataSource: faker_1.faker.helpers.arrayElement(['metrics', 'reports', 'alerts']),
                        refreshInterval: faker_1.faker.number.int({ min: 30, max: 300 }),
                        visualization: {
                            type: faker_1.faker.helpers.arrayElement(['line', 'bar', 'pie', 'gauge']),
                            colors: faker_1.faker.helpers.arrayElements(['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0'], { min: 2, max: 4 }),
                        },
                    },
                })),
            },
            permissions: {
                public: faker_1.faker.datatype.boolean({ probability: 0.3 }),
                users: faker_1.faker.helpers.arrayElements([
                    faker_1.faker.string.uuid(),
                    faker_1.faker.string.uuid(),
                    faker_1.faker.string.uuid(),
                ], { min: 1, max: 3 }),
                roles: faker_1.faker.helpers.arrayElements(['admin', 'analyst', 'viewer'], { min: 1, max: 2 }),
            },
            isActive: faker_1.faker.datatype.boolean({ probability: 0.9 }),
            createdBy: faker_1.faker.string.uuid(),
            updatedBy: faker_1.faker.string.uuid(),
            createdAt: faker_1.faker.date.past({ years: 1 }),
            updatedAt: faker_1.faker.date.recent({ days: 14 }),
            ...overrides,
        };
    }
    /**
     * Generate workflow template fixtures
     */
    static generateWorkflowTemplate(overrides = {}) {
        const stepCount = faker_1.faker.number.int({ min: 3, max: 8 });
        const steps = Array.from({ length: stepCount }, (_, index) => ({
            id: `step_${index + 1}`,
            name: faker_1.faker.hacker.phrase(),
            type: faker_1.faker.helpers.arrayElement(['notification', 'data_processing', 'condition', 'integration']),
            description: faker_1.faker.lorem.sentence(),
            configuration: {
                timeout: faker_1.faker.number.int({ min: 30, max: 300 }),
                retries: faker_1.faker.number.int({ min: 0, max: 3 }),
                parameters: {
                    message: faker_1.faker.lorem.sentence(),
                    endpoint: faker_1.faker.internet.url(),
                    method: faker_1.faker.helpers.arrayElement(['GET', 'POST', 'PUT', 'DELETE']),
                },
            },
            position: {
                x: faker_1.faker.number.int({ min: 100, max: 800 }),
                y: faker_1.faker.number.int({ min: 100, max: 600 }),
            },
            connections: index < stepCount - 1 ? [{
                    target: `step_${index + 2}`,
                    condition: 'success',
                }] : [],
        }));
        return {
            id: faker_1.faker.string.uuid(),
            name: faker_1.faker.hacker.phrase(),
            description: faker_1.faker.lorem.paragraph(),
            category: faker_1.faker.helpers.arrayElement(['automation', 'notification', 'data_processing', 'integration']),
            version: faker_1.faker.system.semver(),
            steps,
            triggers: [
                {
                    type: faker_1.faker.helpers.arrayElement(['schedule', 'event', 'manual']),
                    configuration: {
                        schedule: faker_1.faker.helpers.arrayElement(['0 0 * * *', '0 */6 * * *', '0 0 * * 0']),
                        event: faker_1.faker.helpers.arrayElement(['user.created', 'alert.triggered', 'report.generated']),
                    },
                },
            ],
            variables: {
                environment: faker_1.faker.helpers.arrayElement(['development', 'staging', 'production']),
                timeout: faker_1.faker.number.int({ min: 300, max: 3600 }),
                retryCount: faker_1.faker.number.int({ min: 1, max: 5 }),
            },
            permissions: {
                execute: faker_1.faker.helpers.arrayElements(['admin', 'operator', 'user'], { min: 1, max: 2 }),
                modify: faker_1.faker.helpers.arrayElements(['admin', 'operator'], { min: 1, max: 2 }),
                view: faker_1.faker.helpers.arrayElements(['admin', 'operator', 'user', 'viewer'], { min: 2, max: 4 }),
            },
            isActive: faker_1.faker.datatype.boolean({ probability: 0.8 }),
            createdBy: faker_1.faker.string.uuid(),
            updatedBy: faker_1.faker.string.uuid(),
            createdAt: faker_1.faker.date.past({ years: 1 }),
            updatedAt: faker_1.faker.date.recent({ days: 30 }),
            ...overrides,
        };
    }
    /**
     * Generate job execution fixtures
     */
    static generateJobExecution(overrides = {}) {
        const status = faker_1.faker.helpers.arrayElement(['pending', 'active', 'completed', 'failed', 'cancelled']);
        const startTime = faker_1.faker.date.recent({ days: 7 });
        const endTime = ['completed', 'failed', 'cancelled'].includes(status)
            ? faker_1.faker.date.between({ from: startTime, to: new Date() })
            : null;
        return {
            id: faker_1.faker.string.uuid(),
            jobId: `job_${faker_1.faker.string.alphanumeric(8)}`,
            jobType: faker_1.faker.helpers.arrayElement(['workflow_execution', 'asset_discovery', 'event_correlation', 'report_generation']),
            queueName: faker_1.faker.helpers.arrayElement(['workflow-execution', 'asset-discovery', 'event-correlation', 'report-generation']),
            status,
            priority: faker_1.faker.number.int({ min: 0, max: 100 }),
            jobData: {
                input: {
                    workflowId: faker_1.faker.string.uuid(),
                    parameters: {
                        timeout: faker_1.faker.number.int({ min: 300, max: 3600 }),
                        retries: faker_1.faker.number.int({ min: 1, max: 3 }),
                    },
                },
                configuration: {
                    parallelExecution: faker_1.faker.datatype.boolean(),
                    maxConcurrency: faker_1.faker.number.int({ min: 1, max: 10 }),
                },
                context: {
                    userId: faker_1.faker.string.uuid(),
                    triggeredBy: faker_1.faker.helpers.arrayElement(['user', 'schedule', 'event']),
                },
                metadata: {
                    source: faker_1.faker.helpers.arrayElement(['api', 'ui', 'schedule']),
                    version: faker_1.faker.system.semver(),
                },
            },
            result: status === 'completed' ? {
                output: {
                    success: true,
                    itemsProcessed: faker_1.faker.number.int({ min: 10, max: 1000 }),
                    duration: faker_1.faker.number.int({ min: 1000, max: 300000 }),
                },
                metrics: {
                    executionTime: endTime ? endTime.getTime() - startTime.getTime() : null,
                    memoryUsage: faker_1.faker.number.int({ min: 50000000, max: 500000000 }),
                    cpuUsage: faker_1.faker.number.int({ min: 1000000, max: 10000000 }),
                    itemsProcessed: faker_1.faker.number.int({ min: 10, max: 1000 }),
                    errorsEncountered: faker_1.faker.number.int({ min: 0, max: 5 }),
                },
            } : null,
            progress: {
                currentStep: status === 'active' ? faker_1.faker.number.int({ min: 1, max: 5 }) :
                    status === 'completed' ? 5 : 0,
                totalSteps: 5,
                percentage: status === 'completed' ? 100 :
                    status === 'active' ? faker_1.faker.number.int({ min: 20, max: 80 }) : 0,
                message: status === 'active' ? 'Processing...' :
                    status === 'completed' ? 'Completed successfully' :
                        status === 'failed' ? 'Failed with errors' : 'Pending execution',
                details: {
                    currentOperation: faker_1.faker.hacker.phrase(),
                    estimatedTimeRemaining: status === 'active' ? faker_1.faker.number.int({ min: 60, max: 3600 }) : null,
                },
            },
            error: status === 'failed' ? {
                message: faker_1.faker.lorem.sentence(),
                stack: faker_1.faker.lorem.paragraphs(3),
                code: faker_1.faker.helpers.arrayElement(['TIMEOUT', 'VALIDATION_ERROR', 'NETWORK_ERROR']),
                type: 'Error',
                retryable: faker_1.faker.datatype.boolean(),
                timestamp: faker_1.faker.date.recent().toISOString(),
            } : null,
            retryInfo: {
                attempt: faker_1.faker.number.int({ min: 0, max: 3 }),
                maxAttempts: 3,
                backoffStrategy: faker_1.faker.helpers.arrayElement(['fixed', 'exponential', 'linear']),
                backoffDelay: faker_1.faker.number.int({ min: 1000, max: 10000 }),
                retryHistory: [],
            },
            scheduledAt: startTime,
            startedAt: ['active', 'completed', 'failed'].includes(status) ? startTime : null,
            completedAt: endTime,
            executionTimeMs: endTime ? endTime.getTime() - startTime.getTime() : null,
            executedBy: ['active', 'completed', 'failed'].includes(status) ? `worker-${faker_1.faker.number.int({ min: 1, max: 10 })}` : null,
            triggeredBy: faker_1.faker.string.uuid(),
            isRecurring: faker_1.faker.datatype.boolean({ probability: 0.3 }),
            isCritical: faker_1.faker.datatype.boolean({ probability: 0.2 }),
            createdAt: startTime,
            updatedAt: faker_1.faker.date.recent(),
            ...overrides,
        };
    }
    /**
     * Generate metric fixtures
     */
    static generateMetric(overrides = {}) {
        return {
            id: faker_1.faker.string.uuid(),
            name: faker_1.faker.helpers.arrayElement([
                'http_requests_total',
                'response_time_seconds',
                'memory_usage_bytes',
                'cpu_usage_percent',
                'disk_usage_bytes',
                'active_users_count',
                'error_rate_percent',
            ]),
            value: faker_1.faker.number.float({ min: 0, max: 1000, fractionDigits: 2 }),
            category: faker_1.faker.helpers.arrayElement(['business', 'technical', 'system', 'security', 'performance']),
            type: faker_1.faker.helpers.arrayElement(['counter', 'gauge', 'histogram', 'summary']),
            unit: faker_1.faker.helpers.arrayElement(['bytes', 'seconds', 'percent', 'count', 'requests']),
            labels: {
                service: faker_1.faker.helpers.arrayElement(['api', 'web', 'worker', 'database']),
                environment: faker_1.faker.helpers.arrayElement(['development', 'staging', 'production']),
                region: faker_1.faker.helpers.arrayElement(['us-east-1', 'us-west-2', 'eu-west-1']),
                instance: `${faker_1.faker.helpers.arrayElement(['api', 'web', 'worker'])}-${faker_1.faker.number.int({ min: 1, max: 10 })}`,
            },
            source: faker_1.faker.helpers.arrayElement(['prometheus', 'application', 'system', 'custom']),
            instance: faker_1.faker.internet.ip(),
            job: faker_1.faker.helpers.arrayElement(['api-server', 'web-server', 'background-worker']),
            timestamp: faker_1.faker.date.recent({ days: 1 }),
            metadata: {
                collector: faker_1.faker.helpers.arrayElement(['node_exporter', 'application', 'custom']),
                version: faker_1.faker.system.semver(),
                tags: faker_1.faker.helpers.arrayElements(['monitoring', 'performance', 'health'], { min: 1, max: 3 }),
            },
            ...overrides,
        };
    }
    /**
     * Generate health check fixtures
     */
    static generateHealthCheck(overrides = {}) {
        const status = faker_1.faker.helpers.arrayElement(['healthy', 'degraded', 'unhealthy', 'unknown']);
        return {
            id: faker_1.faker.string.uuid(),
            name: faker_1.faker.helpers.arrayElement([
                'Database Connection',
                'Redis Cache',
                'External API',
                'File System',
                'Message Queue',
                'Email Service',
            ]),
            category: faker_1.faker.helpers.arrayElement(['application', 'database', 'cache', 'queue', 'external', 'filesystem', 'network']),
            description: faker_1.faker.lorem.sentence(),
            endpoint: faker_1.faker.internet.url(),
            method: faker_1.faker.helpers.arrayElement(['GET', 'POST', 'HEAD', 'CUSTOM']),
            status,
            configuration: {
                timeout: faker_1.faker.number.int({ min: 1000, max: 10000 }),
                retries: faker_1.faker.number.int({ min: 1, max: 5 }),
                expectedStatus: [200, 201],
                headers: {
                    'User-Agent': 'Sentinel-Health-Check/1.0',
                    'Accept': 'application/json',
                },
            },
            schedule: {
                interval: faker_1.faker.number.int({ min: 30, max: 300 }),
                enabled: faker_1.faker.datatype.boolean({ probability: 0.9 }),
            },
            thresholds: {
                responseTime: {
                    warning: faker_1.faker.number.int({ min: 1000, max: 3000 }),
                    critical: faker_1.faker.number.int({ min: 3000, max: 10000 }),
                    emergency: faker_1.faker.number.int({ min: 10000, max: 30000 }),
                },
                availability: {
                    warning: faker_1.faker.number.int({ min: 95, max: 98 }),
                    critical: faker_1.faker.number.int({ min: 90, max: 95 }),
                    emergency: faker_1.faker.number.int({ min: 80, max: 90 }),
                },
            },
            lastResult: {
                status,
                responseTime: faker_1.faker.number.int({ min: 100, max: 5000 }),
                statusCode: status === 'healthy' ? 200 : faker_1.faker.helpers.arrayElement([500, 503, 404]),
                message: status === 'healthy' ? 'Health check passed' : 'Health check failed',
                timestamp: faker_1.faker.date.recent(),
            },
            metrics: {
                totalChecks: faker_1.faker.number.int({ min: 100, max: 10000 }),
                successfulChecks: faker_1.faker.number.int({ min: 80, max: 9500 }),
                failedChecks: faker_1.faker.number.int({ min: 0, max: 500 }),
                averageResponseTime: faker_1.faker.number.int({ min: 200, max: 2000 }),
                availability: faker_1.faker.number.float({ min: 85, max: 100, fractionDigits: 2 }),
                uptime: faker_1.faker.number.int({ min: 86400, max: 2592000 }),
                downtime: faker_1.faker.number.int({ min: 0, max: 3600 }),
            },
            isEnabled: faker_1.faker.datatype.boolean({ probability: 0.9 }),
            isCritical: faker_1.faker.datatype.boolean({ probability: 0.3 }),
            createdBy: faker_1.faker.string.uuid(),
            updatedBy: faker_1.faker.string.uuid(),
            createdAt: faker_1.faker.date.past({ years: 1 }),
            updatedAt: faker_1.faker.date.recent({ days: 7 }),
            ...overrides,
        };
    }
    /**
     * Generate alert fixtures
     */
    static generateAlert(overrides = {}) {
        const severity = faker_1.faker.helpers.arrayElement(['info', 'warning', 'critical', 'emergency']);
        const status = faker_1.faker.helpers.arrayElement(['active', 'acknowledged', 'resolved', 'suppressed']);
        return {
            id: faker_1.faker.string.uuid(),
            name: faker_1.faker.helpers.arrayElement([
                'High CPU Usage',
                'Database Connection Failed',
                'Disk Space Low',
                'Memory Usage Critical',
                'API Response Time High',
                'Security Breach Detected',
            ]),
            description: faker_1.faker.lorem.paragraph(),
            severity,
            category: faker_1.faker.helpers.arrayElement(['system', 'application', 'security', 'performance', 'business']),
            source: faker_1.faker.helpers.arrayElement(['metrics', 'health_check', 'log_analysis', 'user_report']),
            sourceId: faker_1.faker.string.uuid(),
            status,
            escalationLevel: faker_1.faker.number.int({ min: 0, max: 3 }),
            conditions: {
                metric: faker_1.faker.helpers.arrayElement(['cpu_usage', 'memory_usage', 'response_time']),
                operator: faker_1.faker.helpers.arrayElement(['>', '<', '>=', '<=', '==']),
                threshold: faker_1.faker.number.float({ min: 0, max: 100, fractionDigits: 2 }),
                duration: faker_1.faker.number.int({ min: 60, max: 3600 }),
            },
            context: {
                labels: {
                    service: faker_1.faker.helpers.arrayElement(['api', 'web', 'database']),
                    environment: faker_1.faker.helpers.arrayElement(['production', 'staging']),
                    region: faker_1.faker.helpers.arrayElement(['us-east-1', 'us-west-2']),
                },
                annotations: {
                    runbook: faker_1.faker.internet.url(),
                    dashboard: faker_1.faker.internet.url(),
                    description: faker_1.faker.lorem.sentence(),
                },
            },
            notifications: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 3 }) }, () => ({
                id: faker_1.faker.string.uuid(),
                type: faker_1.faker.helpers.arrayElement(['email', 'slack', 'webhook', 'sms']),
                recipient: faker_1.faker.internet.email(),
                status: faker_1.faker.helpers.arrayElement(['pending', 'sent', 'failed']),
                sentAt: faker_1.faker.date.recent(),
            })),
            acknowledgment: status === 'acknowledged' ? {
                acknowledgedBy: faker_1.faker.string.uuid(),
                acknowledgedAt: faker_1.faker.date.recent(),
                notes: faker_1.faker.lorem.sentence(),
                expectedResolutionTime: faker_1.faker.date.future(),
            } : null,
            resolution: status === 'resolved' ? {
                resolvedBy: faker_1.faker.string.uuid(),
                resolvedAt: faker_1.faker.date.recent(),
                resolution: faker_1.faker.helpers.arrayElement(['fixed', 'false_positive', 'duplicate']),
                notes: faker_1.faker.lorem.sentence(),
                rootCause: faker_1.faker.lorem.sentence(),
            } : null,
            suppressedUntil: status === 'suppressed' ? faker_1.faker.date.future() : null,
            createdAt: faker_1.faker.date.recent({ days: 7 }),
            updatedAt: faker_1.faker.date.recent(),
            ...overrides,
        };
    }
    /**
     * Generate complete test scenario
     */
    static generateTestScenario(name) {
        TestFixturesUtil.setSeed(TestFixturesUtil.seed);
        const users = Array.from({ length: 5 }, () => TestFixturesUtil.generateUser());
        const reportDefinitions = Array.from({ length: 10 }, () => TestFixturesUtil.generateReportDefinition({ createdBy: faker_1.faker.helpers.arrayElement(users).id }));
        const generatedReports = Array.from({ length: 25 }, () => TestFixturesUtil.generateGeneratedReport({
            reportDefinitionId: faker_1.faker.helpers.arrayElement(reportDefinitions).id,
            generatedBy: faker_1.faker.helpers.arrayElement(users).id,
        }));
        const dashboards = Array.from({ length: 8 }, () => TestFixturesUtil.generateDashboard({ createdBy: faker_1.faker.helpers.arrayElement(users).id }));
        const workflowTemplates = Array.from({ length: 12 }, () => TestFixturesUtil.generateWorkflowTemplate({ createdBy: faker_1.faker.helpers.arrayElement(users).id }));
        const jobExecutions = Array.from({ length: 50 }, () => TestFixturesUtil.generateJobExecution({ triggeredBy: faker_1.faker.helpers.arrayElement(users).id }));
        const metrics = Array.from({ length: 100 }, () => TestFixturesUtil.generateMetric());
        const healthChecks = Array.from({ length: 15 }, () => TestFixturesUtil.generateHealthCheck({ createdBy: faker_1.faker.helpers.arrayElement(users).id }));
        const alerts = Array.from({ length: 30 }, () => TestFixturesUtil.generateAlert());
        return {
            users,
            reportDefinitions,
            generatedReports,
            dashboards,
            workflowTemplates,
            jobExecutions,
            metrics,
            healthChecks,
            alerts,
        };
    }
}
exports.TestFixturesUtil = TestFixturesUtil;
TestFixturesUtil.seed = 12345;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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