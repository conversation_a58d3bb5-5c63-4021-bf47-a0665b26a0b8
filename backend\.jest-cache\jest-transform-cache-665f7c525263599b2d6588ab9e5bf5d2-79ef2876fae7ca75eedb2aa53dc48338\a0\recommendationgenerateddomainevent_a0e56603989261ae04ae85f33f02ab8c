6f1bf355a0ec85f58133ba7cbc3e83a3
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecommendationGeneratedDomainEvent = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
/**
 * Recommendation Generated Domain Event
 *
 * Raised when the system generates a new security recommendation based on
 * threat analysis, vulnerability assessment, compliance gaps, or other security
 * insights. This event triggers recommendation review and implementation workflows.
 *
 * Key characteristics:
 * - Represents AI or system-generated security recommendations
 * - Contains comprehensive implementation guidance
 * - Includes business justification and risk analysis
 * - Triggers approval and implementation workflows
 *
 * Downstream processes triggered:
 * - Recommendation review and approval
 * - Implementation planning and scheduling
 * - Stakeholder notification and assignment
 * - Progress tracking and monitoring
 * - Success measurement and validation
 */
class RecommendationGeneratedDomainEvent extends shared_kernel_1.BaseDomainEvent {
    constructor(aggregateId, eventData, options) {
        super(aggregateId, eventData, {
            eventVersion: 1,
            ...options,
            metadata: {
                eventType: 'RecommendationGenerated',
                domain: 'Security',
                aggregateType: 'Recommendation',
                processingStage: 'generation',
                ...options?.metadata,
            },
        });
    }
    /**
     * Get the recommendation ID
     */
    get recommendationId() {
        return this.eventData.recommendationId;
    }
    /**
     * Get the recommendation type
     */
    get recommendationType() {
        return this.eventData.recommendationType;
    }
    /**
     * Get the recommendation title
     */
    get title() {
        return this.eventData.title;
    }
    /**
     * Get the recommendation description
     */
    get description() {
        return this.eventData.description;
    }
    /**
     * Get the priority level
     */
    get priority() {
        return this.eventData.priority;
    }
    /**
     * Get the confidence score
     */
    get confidenceScore() {
        return this.eventData.confidenceScore;
    }
    /**
     * Get the risk reduction potential
     */
    get riskReductionPotential() {
        return this.eventData.riskReductionPotential;
    }
    /**
     * Get the implementation effort
     */
    get implementationEffort() {
        return this.eventData.implementationEffort;
    }
    /**
     * Get the estimated implementation time
     */
    get estimatedImplementationTime() {
        return this.eventData.estimatedImplementationTime;
    }
    /**
     * Get the cost estimate
     */
    get costEstimate() {
        return this.eventData.costEstimate;
    }
    /**
     * Get the recommendation source
     */
    get source() {
        return this.eventData.source;
    }
    /**
     * Get the context that triggered the recommendation
     */
    get context() {
        return this.eventData.context;
    }
    /**
     * Get the recommended actions
     */
    get recommendedActions() {
        return this.eventData.recommendedActions;
    }
    /**
     * Get the success criteria
     */
    get successCriteria() {
        return this.eventData.successCriteria;
    }
    /**
     * Get the KPIs
     */
    get kpis() {
        return this.eventData.kpis;
    }
    /**
     * Get the compliance frameworks
     */
    get complianceFrameworks() {
        return this.eventData.complianceFrameworks;
    }
    /**
     * Get the business justification
     */
    get businessJustification() {
        return this.eventData.businessJustification;
    }
    /**
     * Get the implementation timeline
     */
    get timeline() {
        return this.eventData.timeline;
    }
    /**
     * Get the stakeholders
     */
    get stakeholders() {
        return this.eventData.stakeholders;
    }
    /**
     * Get the alternatives
     */
    get alternatives() {
        return this.eventData.alternatives;
    }
    /**
     * Get the dependencies
     */
    get dependencies() {
        return this.eventData.dependencies;
    }
    /**
     * Get the implementation risks
     */
    get implementationRisks() {
        return this.eventData.implementationRisks;
    }
    /**
     * Get the validation requirements
     */
    get validationRequirements() {
        return this.eventData.validationRequirements;
    }
    /**
     * Get the rollback plan
     */
    get rollbackPlan() {
        return this.eventData.rollbackPlan;
    }
    /**
     * Check if recommendation is critical priority
     */
    isCriticalPriority() {
        return this.priority === 'critical';
    }
    /**
     * Check if recommendation is high priority or above
     */
    isHighPriorityOrAbove() {
        return ['high', 'critical'].includes(this.priority);
    }
    /**
     * Check if recommendation has high confidence
     */
    hasHighConfidence() {
        return this.confidenceScore >= 80;
    }
    /**
     * Check if recommendation has low confidence
     */
    hasLowConfidence() {
        return this.confidenceScore < 60;
    }
    /**
     * Check if recommendation has high risk reduction potential
     */
    hasHighRiskReduction() {
        return this.riskReductionPotential >= 70;
    }
    /**
     * Check if recommendation is low effort
     */
    isLowEffort() {
        return this.implementationEffort === 'low';
    }
    /**
     * Check if recommendation is high effort
     */
    isHighEffort() {
        return this.implementationEffort === 'high';
    }
    /**
     * Check if recommendation is quick to implement (under 8 hours)
     */
    isQuickImplementation() {
        return this.estimatedImplementationTime < 8;
    }
    /**
     * Check if recommendation is long implementation (over 40 hours)
     */
    isLongImplementation() {
        return this.estimatedImplementationTime > 40;
    }
    /**
     * Check if recommendation has cost estimate
     */
    hasCostEstimate() {
        return this.costEstimate !== undefined;
    }
    /**
     * Check if recommendation is high cost
     */
    isHighCost() {
        return this.costEstimate?.category === 'high';
    }
    /**
     * Check if recommendation addresses compliance
     */
    addressesCompliance() {
        return (this.complianceFrameworks?.length || 0) > 0;
    }
    /**
     * Check if recommendation has alternatives
     */
    hasAlternatives() {
        return (this.alternatives?.length || 0) > 0;
    }
    /**
     * Check if recommendation has dependencies
     */
    hasDependencies() {
        return this.dependencies.length > 0;
    }
    /**
     * Check if recommendation has high implementation risks
     */
    hasHighImplementationRisks() {
        return this.implementationRisks.some(risk => risk.likelihood === 'high' && risk.impact === 'high');
    }
    /**
     * Check if recommendation has rollback plan
     */
    hasRollbackPlan() {
        return this.rollbackPlan !== undefined;
    }
    /**
     * Check if recommendation is AI-generated
     */
    isAIGenerated() {
        return this.source.type === 'ai_analysis';
    }
    /**
     * Check if recommendation is triggered by threat
     */
    isTriggeredByThreat() {
        return this.context.triggerType === 'threat_detected';
    }
    /**
     * Check if recommendation is triggered by vulnerability
     */
    isTriggeredByVulnerability() {
        return this.context.triggerType === 'vulnerability_found';
    }
    /**
     * Check if recommendation is triggered by incident
     */
    isTriggeredByIncident() {
        return this.context.triggerType === 'incident_occurred';
    }
    /**
     * Get recommendation value score (0-100)
     */
    getValueScore() {
        let score = 0;
        // Risk reduction potential (40% weight)
        score += this.riskReductionPotential * 0.4;
        // Confidence score (30% weight)
        score += this.confidenceScore * 0.3;
        // Implementation effort (20% weight - inverse)
        const effortScore = this.implementationEffort === 'low' ? 100 :
            this.implementationEffort === 'medium' ? 60 : 30;
        score += effortScore * 0.2;
        // Priority (10% weight)
        const priorityScore = this.priority === 'critical' ? 100 :
            this.priority === 'high' ? 80 :
                this.priority === 'medium' ? 60 : 40;
        score += priorityScore * 0.1;
        return Math.round(score);
    }
    /**
     * Get implementation urgency in days
     */
    getImplementationUrgency() {
        switch (this.priority) {
            case 'critical': return 1; // 1 day
            case 'high': return 7; // 1 week
            case 'medium': return 30; // 1 month
            case 'low': return 90; // 3 months
            default: return 90;
        }
    }
    /**
     * Get approval requirements
     */
    getApprovalRequirements() {
        let approvalLevel = 'team_lead';
        const requiredApprovers = [];
        const approvalCriteria = [];
        if (this.isCriticalPriority() || this.isHighCost()) {
            approvalLevel = 'executive';
            requiredApprovers.push('security_director', 'ciso');
            approvalCriteria.push('Executive approval required for critical/high-cost recommendations');
        }
        else if (this.isHighPriorityOrAbove() || this.hasHighImplementationRisks()) {
            approvalLevel = 'director';
            requiredApprovers.push('security_director');
            approvalCriteria.push('Director approval required for high-priority/high-risk recommendations');
        }
        else if (this.implementationEffort === 'high' || this.hasDependencies()) {
            approvalLevel = 'manager';
            requiredApprovers.push('security_manager');
            approvalCriteria.push('Manager approval required for high-effort recommendations');
        }
        if (this.addressesCompliance()) {
            requiredApprovers.push('compliance_officer');
            approvalCriteria.push('Compliance officer approval required for compliance-related recommendations');
        }
        return {
            requiresApproval: requiredApprovers.length > 0,
            approvalLevel,
            requiredApprovers,
            approvalCriteria,
        };
    }
    /**
     * Get notification targets
     */
    getNotificationTargets() {
        const targets = ['security_team'];
        if (this.isCriticalPriority()) {
            targets.push('security_leadership', 'incident_response_team');
        }
        if (this.isHighPriorityOrAbove()) {
            targets.push('security_managers');
        }
        if (this.addressesCompliance()) {
            targets.push('compliance_team');
        }
        if (this.isTriggeredByThreat() || this.isTriggeredByIncident()) {
            targets.push('threat_analysts');
        }
        if (this.isTriggeredByVulnerability()) {
            targets.push('vulnerability_management_team');
        }
        // Add stakeholders
        this.stakeholders.forEach(stakeholder => {
            if (stakeholder.required) {
                targets.push(stakeholder.role);
            }
        });
        return [...new Set(targets)]; // Remove duplicates
    }
    /**
     * Get implementation complexity level
     */
    getComplexityLevel() {
        let complexity = 0;
        if (this.implementationEffort === 'high')
            complexity += 2;
        else if (this.implementationEffort === 'medium')
            complexity += 1;
        if (this.hasDependencies())
            complexity += 1;
        if (this.hasHighImplementationRisks())
            complexity += 1;
        if (this.recommendedActions.length > 5)
            complexity += 1;
        if (this.stakeholders.length > 3)
            complexity += 1;
        if (complexity <= 1)
            return 'simple';
        if (complexity <= 3)
            return 'moderate';
        if (complexity <= 5)
            return 'complex';
        return 'very_complex';
    }
    /**
     * Get recommendation metrics
     */
    getRecommendationMetrics() {
        const approval = this.getApprovalRequirements();
        return {
            recommendationId: this.recommendationId,
            type: this.recommendationType,
            priority: this.priority,
            confidenceScore: this.confidenceScore,
            riskReductionPotential: this.riskReductionPotential,
            valueScore: this.getValueScore(),
            implementationEffort: this.implementationEffort,
            estimatedHours: this.estimatedImplementationTime,
            complexityLevel: this.getComplexityLevel(),
            hasAlternatives: this.hasAlternatives(),
            hasDependencies: this.hasDependencies(),
            addressesCompliance: this.addressesCompliance(),
            requiresApproval: approval.requiresApproval,
            implementationUrgency: this.getImplementationUrgency(),
        };
    }
    /**
     * Convert to integration event for external systems
     */
    toIntegrationEvent() {
        const approval = this.getApprovalRequirements();
        const criticalActions = this.recommendedActions.filter(a => a.priority === 'critical').length;
        const actionTypes = [...new Set(this.recommendedActions.map(a => a.actionType))];
        return {
            eventType: 'RecommendationGenerated',
            version: '1.0',
            timestamp: this.occurredOn.toISOString(),
            data: {
                recommendationId: this.recommendationId,
                recommendation: {
                    type: this.recommendationType,
                    title: this.title,
                    description: this.description,
                    priority: this.priority,
                    confidenceScore: this.confidenceScore,
                    riskReductionPotential: this.riskReductionPotential,
                    valueScore: this.getValueScore(),
                },
                implementation: {
                    effort: this.implementationEffort,
                    estimatedHours: this.estimatedImplementationTime,
                    complexityLevel: this.getComplexityLevel(),
                    urgencyDays: this.getImplementationUrgency(),
                    hasDependencies: this.hasDependencies(),
                    hasRollbackPlan: this.hasRollbackPlan(),
                },
                source: {
                    type: this.source.type,
                    identifier: this.source.identifier,
                    version: this.source.version,
                },
                context: {
                    triggerType: this.context.triggerType,
                    triggerIds: this.context.triggerIds,
                    relatedAssets: this.context.relatedAssets,
                    affectedSystems: this.context.affectedSystems,
                },
                business: {
                    riskMitigation: this.businessJustification.riskMitigation,
                    businessValue: this.businessJustification.businessValue,
                    costBenefit: this.businessJustification.costBenefit,
                    addressesCompliance: this.addressesCompliance(),
                    complianceFrameworks: this.complianceFrameworks,
                },
                approval: {
                    requiresApproval: approval.requiresApproval,
                    approvalLevel: approval.approvalLevel,
                    requiredApprovers: approval.requiredApprovers,
                },
                actions: {
                    count: this.recommendedActions.length,
                    criticalActions,
                    actionTypes,
                },
            },
            metadata: {
                correlationId: this.correlationId,
                causationId: this.causationId,
                domain: 'Security',
                aggregateType: 'Recommendation',
                processingStage: 'generation',
            },
        };
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            ...super.toJSON(),
            eventData: this.eventData,
            analysis: {
                isCriticalPriority: this.isCriticalPriority(),
                isHighPriorityOrAbove: this.isHighPriorityOrAbove(),
                hasHighConfidence: this.hasHighConfidence(),
                hasLowConfidence: this.hasLowConfidence(),
                hasHighRiskReduction: this.hasHighRiskReduction(),
                isLowEffort: this.isLowEffort(),
                isHighEffort: this.isHighEffort(),
                isQuickImplementation: this.isQuickImplementation(),
                isLongImplementation: this.isLongImplementation(),
                hasCostEstimate: this.hasCostEstimate(),
                isHighCost: this.isHighCost(),
                addressesCompliance: this.addressesCompliance(),
                hasAlternatives: this.hasAlternatives(),
                hasDependencies: this.hasDependencies(),
                hasHighImplementationRisks: this.hasHighImplementationRisks(),
                hasRollbackPlan: this.hasRollbackPlan(),
                isAIGenerated: this.isAIGenerated(),
                isTriggeredByThreat: this.isTriggeredByThreat(),
                isTriggeredByVulnerability: this.isTriggeredByVulnerability(),
                isTriggeredByIncident: this.isTriggeredByIncident(),
                valueScore: this.getValueScore(),
                implementationUrgency: this.getImplementationUrgency(),
                complexityLevel: this.getComplexityLevel(),
                approvalRequirements: this.getApprovalRequirements(),
                notificationTargets: this.getNotificationTargets(),
                recommendationMetrics: this.getRecommendationMetrics(),
            },
        };
    }
    /**
     * Create from JSON representation
     */
    static fromJSON(json) {
        return new RecommendationGeneratedDomainEvent(shared_kernel_1.UniqueEntityId.fromString(json.aggregateId), json.eventData, {
            eventId: shared_kernel_1.UniqueEntityId.fromString(json.eventId),
            occurredOn: new Date(json.occurredOn),
            eventVersion: json.eventVersion,
            correlationId: json.correlationId,
            causationId: json.causationId,
            metadata: json.metadata,
        });
    }
    /**
     * Get human-readable description
     */
    getDescription() {
        const priorityText = this.priority.toUpperCase();
        const confidenceText = this.hasHighConfidence() ? 'high confidence' : 'moderate confidence';
        const effortText = this.implementationEffort;
        const triggerText = this.context.triggerType.replace('_', ' ');
        return `${priorityText} priority ${this.recommendationType} recommendation "${this.title}" generated with ${confidenceText} (${effortText} effort) triggered by ${triggerText}`;
    }
    /**
     * Get event summary for logging
     */
    getSummary() {
        const approval = this.getApprovalRequirements();
        return {
            eventType: 'RecommendationGenerated',
            recommendationId: this.recommendationId,
            type: this.recommendationType,
            title: this.title,
            priority: this.priority,
            confidenceScore: this.confidenceScore,
            riskReductionPotential: this.riskReductionPotential,
            valueScore: this.getValueScore(),
            implementationEffort: this.implementationEffort,
            triggerType: this.context.triggerType,
            requiresApproval: approval.requiresApproval,
            timestamp: this.occurredOn.toISOString(),
        };
    }
}
exports.RecommendationGeneratedDomainEvent = RecommendationGeneratedDomainEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxjb3JlXFxzZWN1cml0eVxcZG9tYWluXFxldmVudHNcXHJlY29tbWVuZGF0aW9uLWdlbmVyYXRlZC5kb21haW4tZXZlbnQudHMiLCJtYXBwaW5ncyI6Ijs7O0FBQUEsNkRBQTRFO0FBbUg1RTs7Ozs7Ozs7Ozs7Ozs7Ozs7OztHQW1CRztBQUNILE1BQWEsa0NBQW1DLFNBQVEsK0JBQWlEO0lBQ3ZHLFlBQ0UsV0FBMkIsRUFDM0IsU0FBMkMsRUFDM0MsT0FPQztRQUVELEtBQUssQ0FBQyxXQUFXLEVBQUUsU0FBUyxFQUFFO1lBQzVCLFlBQVksRUFBRSxDQUFDO1lBQ2YsR0FBRyxPQUFPO1lBQ1YsUUFBUSxFQUFFO2dCQUNSLFNBQVMsRUFBRSx5QkFBeUI7Z0JBQ3BDLE1BQU0sRUFBRSxVQUFVO2dCQUNsQixhQUFhLEVBQUUsZ0JBQWdCO2dCQUMvQixlQUFlLEVBQUUsWUFBWTtnQkFDN0IsR0FBRyxPQUFPLEVBQUUsUUFBUTthQUNyQjtTQUNGLENBQUMsQ0FBQztJQUNMLENBQUM7SUFFRDs7T0FFRztJQUNILElBQUksZ0JBQWdCO1FBQ2xCLE9BQU8sSUFBSSxDQUFDLFNBQVMsQ0FBQyxnQkFBZ0IsQ0FBQztJQUN6QyxDQUFDO0lBRUQ7O09BRUc7SUFDSCxJQUFJLGtCQUFrQjtRQUNwQixPQUFPLElBQUksQ0FBQyxTQUFTLENBQUMsa0JBQWtCLENBQUM7SUFDM0MsQ0FBQztJQUVEOztPQUVHO0lBQ0gsSUFBSSxLQUFLO1FBQ1AsT0FBTyxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQztJQUM5QixDQUFDO0lBRUQ7O09BRUc7SUFDSCxJQUFJLFdBQVc7UUFDYixPQUFPLElBQUksQ0FBQyxTQUFTLENBQUMsV0FBVyxDQUFDO0lBQ3BDLENBQUM7SUFFRDs7T0FFRztJQUNILElBQUksUUFBUTtRQUNWLE9BQU8sSUFBSSxDQUFDLFNBQVMsQ0FBQyxRQUFRLENBQUM7SUFDakMsQ0FBQztJQUVEOztPQUVHO0lBQ0gsSUFBSSxlQUFlO1FBQ2pCLE9BQU8sSUFBSSxDQUFDLFNBQVMsQ0FBQyxlQUFlLENBQUM7SUFDeEMsQ0FBQztJQUVEOztPQUVHO0lBQ0gsSUFBSSxzQkFBc0I7UUFDeEIsT0FBTyxJQUFJLENBQUMsU0FBUyxDQUFDLHNCQUFzQixDQUFDO0lBQy9DLENBQUM7SUFFRDs7T0FFRztJQUNILElBQUksb0JBQW9CO1FBQ3RCLE9BQU8sSUFBSSxDQUFDLFNBQVMsQ0FBQyxvQkFBb0IsQ0FBQztJQUM3QyxDQUFDO0lBRUQ7O09BRUc7SUFDSCxJQUFJLDJCQUEyQjtRQUM3QixPQUFPLElBQUksQ0FBQyxTQUFTLENBQUMsMkJBQTJCLENBQUM7SUFDcEQsQ0FBQztJQUVEOztPQUVHO0lBQ0gsSUFBSSxZQUFZO1FBQ2QsT0FBTyxJQUFJLENBQUMsU0FBUyxDQUFDLFlBQVksQ0FBQztJQUNyQyxDQUFDO0lBRUQ7O09BRUc7SUFDSCxJQUFJLE1BQU07UUFDUixPQUFPLElBQUksQ0FBQyxTQUFTLENBQUMsTUFBTSxDQUFDO0lBQy9CLENBQUM7SUFFRDs7T0FFRztJQUNILElBQUksT0FBTztRQUNULE9BQU8sSUFBSSxDQUFDLFNBQVMsQ0FBQyxPQUFPLENBQUM7SUFDaEMsQ0FBQztJQUVEOztPQUVHO0lBQ0gsSUFBSSxrQkFBa0I7UUFDcEIsT0FBTyxJQUFJLENBQUMsU0FBUyxDQUFDLGtCQUFrQixDQUFDO0lBQzNDLENBQUM7SUFFRDs7T0FFRztJQUNILElBQUksZUFBZTtRQUNqQixPQUFPLElBQUksQ0FBQyxTQUFTLENBQUMsZUFBZSxDQUFDO0lBQ3hDLENBQUM7SUFFRDs7T0FFRztJQUNILElBQUksSUFBSTtRQUNOLE9BQU8sSUFBSSxDQUFDLFNBQVMsQ0FBQyxJQUFJLENBQUM7SUFDN0IsQ0FBQztJQUVEOztPQUVHO0lBQ0gsSUFBSSxvQkFBb0I7UUFDdEIsT0FBTyxJQUFJLENBQUMsU0FBUyxDQUFDLG9CQUFvQixDQUFDO0lBQzdDLENBQUM7SUFFRDs7T0FFRztJQUNILElBQUkscUJBQXFCO1FBQ3ZCLE9BQU8sSUFBSSxDQUFDLFNBQVMsQ0FBQyxxQkFBcUIsQ0FBQztJQUM5QyxDQUFDO0lBRUQ7O09BRUc7SUFDSCxJQUFJLFFBQVE7UUFDVixPQUFPLElBQUksQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDO0lBQ2pDLENBQUM7SUFFRDs7T0FFRztJQUNILElBQUksWUFBWTtRQUNkLE9BQU8sSUFBSSxDQUFDLFNBQVMsQ0FBQyxZQUFZLENBQUM7SUFDckMsQ0FBQztJQUVEOztPQUVHO0lBQ0gsSUFBSSxZQUFZO1FBQ2QsT0FBTyxJQUFJLENBQUMsU0FBUyxDQUFDLFlBQVksQ0FBQztJQUNyQyxDQUFDO0lBRUQ7O09BRUc7SUFDSCxJQUFJLFlBQVk7UUFDZCxPQUFPLElBQUksQ0FBQyxTQUFTLENBQUMsWUFBWSxDQUFDO0lBQ3JDLENBQUM7SUFFRDs7T0FFRztJQUNILElBQUksbUJBQW1CO1FBQ3JCLE9BQU8sSUFBSSxDQUFDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQztJQUM1QyxDQUFDO0lBRUQ7O09BRUc7SUFDSCxJQUFJLHNCQUFzQjtRQUN4QixPQUFPLElBQUksQ0FBQyxTQUFTLENBQUMsc0JBQXNCLENBQUM7SUFDL0MsQ0FBQztJQUVEOztPQUVHO0lBQ0gsSUFBSSxZQUFZO1FBQ2QsT0FBTyxJQUFJLENBQUMsU0FBUyxDQUFDLFlBQVksQ0FBQztJQUNyQyxDQUFDO0lBRUQ7O09BRUc7SUFDSCxrQkFBa0I7UUFDaEIsT0FBTyxJQUFJLENBQUMsUUFBUSxLQUFLLFVBQVUsQ0FBQztJQUN0QyxDQUFDO0lBRUQ7O09BRUc7SUFDSCxxQkFBcUI7UUFDbkIsT0FBTyxDQUFDLE1BQU0sRUFBRSxVQUFVLENBQUMsQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDO0lBQ3RELENBQUM7SUFFRDs7T0FFRztJQUNILGlCQUFpQjtRQUNmLE9BQU8sSUFBSSxDQUFDLGVBQWUsSUFBSSxFQUFFLENBQUM7SUFDcEMsQ0FBQztJQUVEOztPQUVHO0lBQ0gsZ0JBQWdCO1FBQ2QsT0FBTyxJQUFJLENBQUMsZUFBZSxHQUFHLEVBQUUsQ0FBQztJQUNuQyxDQUFDO0lBRUQ7O09BRUc7SUFDSCxvQkFBb0I7UUFDbEIsT0FBTyxJQUFJLENBQUMsc0JBQXNCLElBQUksRUFBRSxDQUFDO0lBQzNDLENBQUM7SUFFRDs7T0FFRztJQUNILFdBQVc7UUFDVCxPQUFPLElBQUksQ0FBQyxvQkFBb0IsS0FBSyxLQUFLLENBQUM7SUFDN0MsQ0FBQztJQUVEOztPQUVHO0lBQ0gsWUFBWTtRQUNWLE9BQU8sSUFBSSxDQUFDLG9CQUFvQixLQUFLLE1BQU0sQ0FBQztJQUM5QyxDQUFDO0lBRUQ7O09BRUc7SUFDSCxxQkFBcUI7UUFDbkIsT0FBTyxJQUFJLENBQUMsMkJBQTJCLEdBQUcsQ0FBQyxDQUFDO0lBQzlDLENBQUM7SUFFRDs7T0FFRztJQUNILG9CQUFvQjtRQUNsQixPQUFPLElBQUksQ0FBQywyQkFBMkIsR0FBRyxFQUFFLENBQUM7SUFDL0MsQ0FBQztJQUVEOztPQUVHO0lBQ0gsZUFBZTtRQUNiLE9BQU8sSUFBSSxDQUFDLFlBQVksS0FBSyxTQUFTLENBQUM7SUFDekMsQ0FBQztJQUVEOztPQUVHO0lBQ0gsVUFBVTtRQUNSLE9BQU8sSUFBSSxDQUFDLFlBQVksRUFBRSxRQUFRLEtBQUssTUFBTSxDQUFDO0lBQ2hELENBQUM7SUFFRDs7T0FFRztJQUNILG1CQUFtQjtRQUNqQixPQUFPLENBQUMsSUFBSSxDQUFDLG9CQUFvQixFQUFFLE1BQU0sSUFBSSxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUM7SUFDdEQsQ0FBQztJQUVEOztPQUVHO0lBQ0gsZUFBZTtRQUNiLE9BQU8sQ0FBQyxJQUFJLENBQUMsWUFBWSxFQUFFLE1BQU0sSUFBSSxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUM7SUFDOUMsQ0FBQztJQUVEOztPQUVHO0lBQ0gsZUFBZTtRQUNiLE9BQU8sSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDO0lBQ3RDLENBQUM7SUFFRDs7T0FFRztJQUNILDBCQUEwQjtRQUN4QixPQUFPLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FDMUMsSUFBSSxDQUFDLFVBQVUsS0FBSyxNQUFNLElBQUksSUFBSSxDQUFDLE1BQU0sS0FBSyxNQUFNLENBQ3JELENBQUM7SUFDSixDQUFDO0lBRUQ7O09BRUc7SUFDSCxlQUFlO1FBQ2IsT0FBTyxJQUFJLENBQUMsWUFBWSxLQUFLLFNBQVMsQ0FBQztJQUN6QyxDQUFDO0lBRUQ7O09BRUc7SUFDSCxhQUFhO1FBQ1gsT0FBTyxJQUFJLENBQUMsTUFBTSxDQUFDLElBQUksS0FBSyxhQUFhLENBQUM7SUFDNUMsQ0FBQztJQUVEOztPQUVHO0lBQ0gsbUJBQW1CO1FBQ2pCLE9BQU8sSUFBSSxDQUFDLE9BQU8sQ0FBQyxXQUFXLEtBQUssaUJBQWlCLENBQUM7SUFDeEQsQ0FBQztJQUVEOztPQUVHO0lBQ0gsMEJBQTBCO1FBQ3hCLE9BQU8sSUFBSSxDQUFDLE9BQU8sQ0FBQyxXQUFXLEtBQUsscUJBQXFCLENBQUM7SUFDNUQsQ0FBQztJQUVEOztPQUVHO0lBQ0gscUJBQXFCO1FBQ25CLE9BQU8sSUFBSSxDQUFDLE9BQU8sQ0FBQyxXQUFXLEtBQUssbUJBQW1CLENBQUM7SUFDMUQsQ0FBQztJQUVEOztPQUVHO0lBQ0gsYUFBYTtRQUNYLElBQUksS0FBSyxHQUFHLENBQUMsQ0FBQztRQUVkLHdDQUF3QztRQUN4QyxLQUFLLElBQUksSUFBSSxDQUFDLHNCQUFzQixHQUFHLEdBQUcsQ0FBQztRQUUzQyxnQ0FBZ0M7UUFDaEMsS0FBSyxJQUFJLElBQUksQ0FBQyxlQUFlLEdBQUcsR0FBRyxDQUFDO1FBRXBDLCtDQUErQztRQUMvQyxNQUFNLFdBQVcsR0FBRyxJQUFJLENBQUMsb0JBQW9CLEtBQUssS0FBSyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQztZQUM1QyxJQUFJLENBQUMsb0JBQW9CLEtBQUssUUFBUSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQztRQUNwRSxLQUFLLElBQUksV0FBVyxHQUFHLEdBQUcsQ0FBQztRQUUzQix3QkFBd0I7UUFDeEIsTUFBTSxhQUFhLEdBQUcsSUFBSSxDQUFDLFFBQVEsS0FBSyxVQUFVLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDO1lBQ3JDLElBQUksQ0FBQyxRQUFRLEtBQUssTUFBTSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQztnQkFDL0IsSUFBSSxDQUFDLFFBQVEsS0FBSyxRQUFRLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDO1FBQzFELEtBQUssSUFBSSxhQUFhLEdBQUcsR0FBRyxDQUFDO1FBRTdCLE9BQU8sSUFBSSxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUMsQ0FBQztJQUMzQixDQUFDO0lBRUQ7O09BRUc7SUFDSCx3QkFBd0I7UUFDdEIsUUFBUSxJQUFJLENBQUMsUUFBUSxFQUFFLENBQUM7WUFDdEIsS0FBSyxVQUFVLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFJLFFBQVE7WUFDdEMsS0FBSyxNQUFNLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFRLFNBQVM7WUFDdkMsS0FBSyxRQUFRLENBQUMsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxDQUFLLFVBQVU7WUFDeEMsS0FBSyxLQUFLLENBQUMsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxDQUFRLFdBQVc7WUFDekMsT0FBTyxDQUFDLENBQUMsT0FBTyxFQUFFLENBQUM7UUFDckIsQ0FBQztJQUNILENBQUM7SUFFRDs7T0FFRztJQUNILHVCQUF1QjtRQU1yQixJQUFJLGFBQWEsR0FBdUQsV0FBVyxDQUFDO1FBQ3BGLE1BQU0saUJBQWlCLEdBQWEsRUFBRSxDQUFDO1FBQ3ZDLE1BQU0sZ0JBQWdCLEdBQWEsRUFBRSxDQUFDO1FBRXRDLElBQUksSUFBSSxDQUFDLGtCQUFrQixFQUFFLElBQUksSUFBSSxDQUFDLFVBQVUsRUFBRSxFQUFFLENBQUM7WUFDbkQsYUFBYSxHQUFHLFdBQVcsQ0FBQztZQUM1QixpQkFBaUIsQ0FBQyxJQUFJLENBQUMsbUJBQW1CLEVBQUUsTUFBTSxDQUFDLENBQUM7WUFDcEQsZ0JBQWdCLENBQUMsSUFBSSxDQUFDLG9FQUFvRSxDQUFDLENBQUM7UUFDOUYsQ0FBQzthQUFNLElBQUksSUFBSSxDQUFDLHFCQUFxQixFQUFFLElBQUksSUFBSSxDQUFDLDBCQUEwQixFQUFFLEVBQUUsQ0FBQztZQUM3RSxhQUFhLEdBQUcsVUFBVSxDQUFDO1lBQzNCLGlCQUFpQixDQUFDLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxDQUFDO1lBQzVDLGdCQUFnQixDQUFDLElBQUksQ0FBQyx3RUFBd0UsQ0FBQyxDQUFDO1FBQ2xHLENBQUM7YUFBTSxJQUFJLElBQUksQ0FBQyxvQkFBb0IsS0FBSyxNQUFNLElBQUksSUFBSSxDQUFDLGVBQWUsRUFBRSxFQUFFLENBQUM7WUFDMUUsYUFBYSxHQUFHLFNBQVMsQ0FBQztZQUMxQixpQkFBaUIsQ0FBQyxJQUFJLENBQUMsa0JBQWtCLENBQUMsQ0FBQztZQUMzQyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUMsMkRBQTJELENBQUMsQ0FBQztRQUNyRixDQUFDO1FBRUQsSUFBSSxJQUFJLENBQUMsbUJBQW1CLEVBQUUsRUFBRSxDQUFDO1lBQy9CLGlCQUFpQixDQUFDLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDO1lBQzdDLGdCQUFnQixDQUFDLElBQUksQ0FBQyw2RUFBNkUsQ0FBQyxDQUFDO1FBQ3ZHLENBQUM7UUFFRCxPQUFPO1lBQ0wsZ0JBQWdCLEVBQUUsaUJBQWlCLENBQUMsTUFBTSxHQUFHLENBQUM7WUFDOUMsYUFBYTtZQUNiLGlCQUFpQjtZQUNqQixnQkFBZ0I7U0FDakIsQ0FBQztJQUNKLENBQUM7SUFFRDs7T0FFRztJQUNILHNCQUFzQjtRQUNwQixNQUFNLE9BQU8sR0FBYSxDQUFDLGVBQWUsQ0FBQyxDQUFDO1FBRTVDLElBQUksSUFBSSxDQUFDLGtCQUFrQixFQUFFLEVBQUUsQ0FBQztZQUM5QixPQUFPLENBQUMsSUFBSSxDQUFDLHFCQUFxQixFQUFFLHdCQUF3QixDQUFDLENBQUM7UUFDaEUsQ0FBQztRQUVELElBQUksSUFBSSxDQUFDLHFCQUFxQixFQUFFLEVBQUUsQ0FBQztZQUNqQyxPQUFPLENBQUMsSUFBSSxDQUFDLG1CQUFtQixDQUFDLENBQUM7UUFDcEMsQ0FBQztRQUVELElBQUksSUFBSSxDQUFDLG1CQUFtQixFQUFFLEVBQUUsQ0FBQztZQUMvQixPQUFPLENBQUMsSUFBSSxDQUFDLGlCQUFpQixDQUFDLENBQUM7UUFDbEMsQ0FBQztRQUVELElBQUksSUFBSSxDQUFDLG1CQUFtQixFQUFFLElBQUksSUFBSSxDQUFDLHFCQUFxQixFQUFFLEVBQUUsQ0FBQztZQUMvRCxPQUFPLENBQUMsSUFBSSxDQUFDLGlCQUFpQixDQUFDLENBQUM7UUFDbEMsQ0FBQztRQUVELElBQUksSUFBSSxDQUFDLDBCQUEwQixFQUFFLEVBQUUsQ0FBQztZQUN0QyxPQUFPLENBQUMsSUFBSSxDQUFDLCtCQUErQixDQUFDLENBQUM7UUFDaEQsQ0FBQztRQUVELG1CQUFtQjtRQUNuQixJQUFJLENBQUMsWUFBWSxDQUFDLE9BQU8sQ0FBQyxXQUFXLENBQUMsRUFBRTtZQUN0QyxJQUFJLFdBQVcsQ0FBQyxRQUFRLEVBQUUsQ0FBQztnQkFDekIsT0FBTyxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDakMsQ0FBQztRQUNILENBQUMsQ0FBQyxDQUFDO1FBRUgsT0FBTyxDQUFDLEdBQUcsSUFBSSxHQUFHLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxDQUFDLG9CQUFvQjtJQUNwRCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxrQkFBa0I7UUFDaEIsSUFBSSxVQUFVLEdBQUcsQ0FBQyxDQUFDO1FBRW5CLElBQUksSUFBSSxDQUFDLG9CQUFvQixLQUFLLE1BQU07WUFBRSxVQUFVLElBQUksQ0FBQyxDQUFDO2FBQ3JELElBQUksSUFBSSxDQUFDLG9CQUFvQixLQUFLLFFBQVE7WUFBRSxVQUFVLElBQUksQ0FBQyxDQUFDO1FBRWpFLElBQUksSUFBSSxDQUFDLGVBQWUsRUFBRTtZQUFFLFVBQVUsSUFBSSxDQUFDLENBQUM7UUFDNUMsSUFBSSxJQUFJLENBQUMsMEJBQTBCLEVBQUU7WUFBRSxVQUFVLElBQUksQ0FBQyxDQUFDO1FBQ3ZELElBQUksSUFBSSxDQUFDLGtCQUFrQixDQUFDLE1BQU0sR0FBRyxDQUFDO1lBQUUsVUFBVSxJQUFJLENBQUMsQ0FBQztRQUN4RCxJQUFJLElBQUksQ0FBQyxZQUFZLENBQUMsTUFBTSxHQUFHLENBQUM7WUFBRSxVQUFVLElBQUksQ0FBQyxDQUFDO1FBRWxELElBQUksVUFBVSxJQUFJLENBQUM7WUFBRSxPQUFPLFFBQVEsQ0FBQztRQUNyQyxJQUFJLFVBQVUsSUFBSSxDQUFDO1lBQUUsT0FBTyxVQUFVLENBQUM7UUFDdkMsSUFBSSxVQUFVLElBQUksQ0FBQztZQUFFLE9BQU8sU0FBUyxDQUFDO1FBQ3RDLE9BQU8sY0FBYyxDQUFDO0lBQ3hCLENBQUM7SUFFRDs7T0FFRztJQUNILHdCQUF3QjtRQWdCdEIsTUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLHVCQUF1QixFQUFFLENBQUM7UUFFaEQsT0FBTztZQUNMLGdCQUFnQixFQUFFLElBQUksQ0FBQyxnQkFBZ0I7WUFDdkMsSUFBSSxFQUFFLElBQUksQ0FBQyxrQkFBa0I7WUFDN0IsUUFBUSxFQUFFLElBQUksQ0FBQyxRQUFRO1lBQ3ZCLGVBQWUsRUFBRSxJQUFJLENBQUMsZUFBZTtZQUNyQyxzQkFBc0IsRUFBRSxJQUFJLENBQUMsc0JBQXNCO1lBQ25ELFVBQVUsRUFBRSxJQUFJLENBQUMsYUFBYSxFQUFFO1lBQ2hDLG9CQUFvQixFQUFFLElBQUksQ0FBQyxvQkFBb0I7WUFDL0MsY0FBYyxFQUFFLElBQUksQ0FBQywyQkFBMkI7WUFDaEQsZUFBZSxFQUFFLElBQUksQ0FBQyxrQkFBa0IsRUFBRTtZQUMxQyxlQUFlLEVBQUUsSUFBSSxDQUFDLGVBQWUsRUFBRTtZQUN2QyxlQUFlLEVBQUUsSUFBSSxDQUFDLGVBQWUsRUFBRTtZQUN2QyxtQkFBbUIsRUFBRSxJQUFJLENBQUMsbUJBQW1CLEVBQUU7WUFDL0MsZ0JBQWdCLEVBQUUsUUFBUSxDQUFDLGdCQUFnQjtZQUMzQyxxQkFBcUIsRUFBRSxJQUFJLENBQUMsd0JBQXdCLEVBQUU7U0FDdkQsQ0FBQztJQUNKLENBQUM7SUFFRDs7T0FFRztJQUNILGtCQUFrQjtRQTREaEIsTUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLHVCQUF1QixFQUFFLENBQUM7UUFDaEQsTUFBTSxlQUFlLEdBQUcsSUFBSSxDQUFDLGtCQUFrQixDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxRQUFRLEtBQUssVUFBVSxDQUFDLENBQUMsTUFBTSxDQUFDO1FBQzlGLE1BQU0sV0FBVyxHQUFHLENBQUMsR0FBRyxJQUFJLEdBQUcsQ0FBQyxJQUFJLENBQUMsa0JBQWtCLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLFVBQVUsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUVqRixPQUFPO1lBQ0wsU0FBUyxFQUFFLHlCQUF5QjtZQUNwQyxPQUFPLEVBQUUsS0FBSztZQUNkLFNBQVMsRUFBRSxJQUFJLENBQUMsVUFBVSxDQUFDLFdBQVcsRUFBRTtZQUN4QyxJQUFJLEVBQUU7Z0JBQ0osZ0JBQWdCLEVBQUUsSUFBSSxDQUFDLGdCQUFnQjtnQkFDdkMsY0FBYyxFQUFFO29CQUNkLElBQUksRUFBRSxJQUFJLENBQUMsa0JBQWtCO29CQUM3QixLQUFLLEVBQUUsSUFBSSxDQUFDLEtBQUs7b0JBQ2pCLFdBQVcsRUFBRSxJQUFJLENBQUMsV0FBVztvQkFDN0IsUUFBUSxFQUFFLElBQUksQ0FBQyxRQUFRO29CQUN2QixlQUFlLEVBQUUsSUFBSSxDQUFDLGVBQWU7b0JBQ3JDLHNCQUFzQixFQUFFLElBQUksQ0FBQyxzQkFBc0I7b0JBQ25ELFVBQVUsRUFBRSxJQUFJLENBQUMsYUFBYSxFQUFFO2lCQUNqQztnQkFDRCxjQUFjLEVBQUU7b0JBQ2QsTUFBTSxFQUFFLElBQUksQ0FBQyxvQkFBb0I7b0JBQ2pDLGNBQWMsRUFBRSxJQUFJLENBQUMsMkJBQTJCO29CQUNoRCxlQUFlLEVBQUUsSUFBSSxDQUFDLGtCQUFrQixFQUFFO29CQUMxQyxXQUFXLEVBQUUsSUFBSSxDQUFDLHdCQUF3QixFQUFFO29CQUM1QyxlQUFlLEVBQUUsSUFBSSxDQUFDLGVBQWUsRUFBRTtvQkFDdkMsZUFBZSxFQUFFLElBQUksQ0FBQyxlQUFlLEVBQUU7aUJBQ3hDO2dCQUNELE1BQU0sRUFBRTtvQkFDTixJQUFJLEVBQUUsSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJO29CQUN0QixVQUFVLEVBQUUsSUFBSSxDQUFDLE1BQU0sQ0FBQyxVQUFVO29CQUNsQyxPQUFPLEVBQUUsSUFBSSxDQUFDLE1BQU0sQ0FBQyxPQUFPO2lCQUM3QjtnQkFDRCxPQUFPLEVBQUU7b0JBQ1AsV0FBVyxFQUFFLElBQUksQ0FBQyxPQUFPLENBQUMsV0FBVztvQkFDckMsVUFBVSxFQUFFLElBQUksQ0FBQyxPQUFPLENBQUMsVUFBVTtvQkFDbkMsYUFBYSxFQUFFLElBQUksQ0FBQyxPQUFPLENBQUMsYUFBYTtvQkFDekMsZUFBZSxFQUFFLElBQUksQ0FBQyxPQUFPLENBQUMsZUFBZTtpQkFDOUM7Z0JBQ0QsUUFBUSxFQUFFO29CQUNSLGNBQWMsRUFBRSxJQUFJLENBQUMscUJBQXFCLENBQUMsY0FBYztvQkFDekQsYUFBYSxFQUFFLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxhQUFhO29CQUN2RCxXQUFXLEVBQUUsSUFBSSxDQUFDLHFCQUFxQixDQUFDLFdBQVc7b0JBQ25ELG1CQUFtQixFQUFFLElBQUksQ0FBQyxtQkFBbUIsRUFBRTtvQkFDL0Msb0JBQW9CLEVBQUUsSUFBSSxDQUFDLG9CQUFvQjtpQkFDaEQ7Z0JBQ0QsUUFBUSxFQUFFO29CQUNSLGdCQUFnQixFQUFFLFFBQVEsQ0FBQyxnQkFBZ0I7b0JBQzNDLGFBQWEsRUFBRSxRQUFRLENBQUMsYUFBYTtvQkFDckMsaUJBQWlCLEVBQUUsUUFBUSxDQUFDLGlCQUFpQjtpQkFDOUM7Z0JBQ0QsT0FBTyxFQUFFO29CQUNQLEtBQUssRUFBRSxJQUFJLENBQUMsa0JBQWtCLENBQUMsTUFBTTtvQkFDckMsZUFBZTtvQkFDZixXQUFXO2lCQUNaO2FBQ0Y7WUFDRCxRQUFRLEVBQUU7Z0JBQ1IsYUFBYSxFQUFFLElBQUksQ0FBQyxhQUFhO2dCQUNqQyxXQUFXLEVBQUUsSUFBSSxDQUFDLFdBQVc7Z0JBQzdCLE1BQU0sRUFBRSxVQUFVO2dCQUNsQixhQUFhLEVBQUUsZ0JBQWdCO2dCQUMvQixlQUFlLEVBQUUsWUFBWTthQUM5QjtTQUNGLENBQUM7SUFDSixDQUFDO0lBRUQ7O09BRUc7SUFDSSxNQUFNO1FBQ1gsT0FBTztZQUNMLEdBQUcsS0FBSyxDQUFDLE1BQU0sRUFBRTtZQUNqQixTQUFTLEVBQUUsSUFBSSxDQUFDLFNBQVM7WUFDekIsUUFBUSxFQUFFO2dCQUNSLGtCQUFrQixFQUFFLElBQUksQ0FBQyxrQkFBa0IsRUFBRTtnQkFDN0MscUJBQXFCLEVBQUUsSUFBSSxDQUFDLHFCQUFxQixFQUFFO2dCQUNuRCxpQkFBaUIsRUFBRSxJQUFJLENBQUMsaUJBQWlCLEVBQUU7Z0JBQzNDLGdCQUFnQixFQUFFLElBQUksQ0FBQyxnQkFBZ0IsRUFBRTtnQkFDekMsb0JBQW9CLEVBQUUsSUFBSSxDQUFDLG9CQUFvQixFQUFFO2dCQUNqRCxXQUFXLEVBQUUsSUFBSSxDQUFDLFdBQVcsRUFBRTtnQkFDL0IsWUFBWSxFQUFFLElBQUksQ0FBQyxZQUFZLEVBQUU7Z0JBQ2pDLHFCQUFxQixFQUFFLElBQUksQ0FBQyxxQkFBcUIsRUFBRTtnQkFDbkQsb0JBQW9CLEVBQUUsSUFBSSxDQUFDLG9CQUFvQixFQUFFO2dCQUNqRCxlQUFlLEVBQUUsSUFBSSxDQUFDLGVBQWUsRUFBRTtnQkFDdkMsVUFBVSxFQUFFLElBQUksQ0FBQyxVQUFVLEVBQUU7Z0JBQzdCLG1CQUFtQixFQUFFLElBQUksQ0FBQyxtQkFBbUIsRUFBRTtnQkFDL0MsZUFBZSxFQUFFLElBQUksQ0FBQyxlQUFlLEVBQUU7Z0JBQ3ZDLGVBQWUsRUFBRSxJQUFJLENBQUMsZUFBZSxFQUFFO2dCQUN2QywwQkFBMEIsRUFBRSxJQUFJLENBQUMsMEJBQTBCLEVBQUU7Z0JBQzdELGVBQWUsRUFBRSxJQUFJLENBQUMsZUFBZSxFQUFFO2dCQUN2QyxhQUFhLEVBQUUsSUFBSSxDQUFDLGFBQWEsRUFBRTtnQkFDbkMsbUJBQW1CLEVBQUUsSUFBSSxDQUFDLG1CQUFtQixFQUFFO2dCQUMvQywwQkFBMEIsRUFBRSxJQUFJLENBQUMsMEJBQTBCLEVBQUU7Z0JBQzdELHFCQUFxQixFQUFFLElBQUksQ0FBQyxxQkFBcUIsRUFBRTtnQkFDbkQsVUFBVSxFQUFFLElBQUksQ0FBQyxhQUFhLEVBQUU7Z0JBQ2hDLHFCQUFxQixFQUFFLElBQUksQ0FBQyx3QkFBd0IsRUFBRTtnQkFDdEQsZUFBZSxFQUFFLElBQUksQ0FBQyxrQkFBa0IsRUFBRTtnQkFDMUMsb0JBQW9CLEVBQUUsSUFBSSxDQUFDLHVCQUF1QixFQUFFO2dCQUNwRCxtQkFBbUIsRUFBRSxJQUFJLENBQUMsc0JBQXNCLEVBQUU7Z0JBQ2xELHFCQUFxQixFQUFFLElBQUksQ0FBQyx3QkFBd0IsRUFBRTthQUN2RDtTQUNGLENBQUM7SUFDSixDQUFDO0lBRUQ7O09BRUc7SUFDSCxNQUFNLENBQUMsUUFBUSxDQUFDLElBQXlCO1FBQ3ZDLE9BQU8sSUFBSSxrQ0FBa0MsQ0FDM0MsOEJBQWMsQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxFQUMzQyxJQUFJLENBQUMsU0FBUyxFQUNkO1lBQ0UsT0FBTyxFQUFFLDhCQUFjLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUM7WUFDaEQsVUFBVSxFQUFFLElBQUksSUFBSSxDQUFDLElBQUksQ0FBQyxVQUFVLENBQUM7WUFDckMsWUFBWSxFQUFFLElBQUksQ0FBQyxZQUFZO1lBQy9CLGFBQWEsRUFBRSxJQUFJLENBQUMsYUFBYTtZQUNqQyxXQUFXLEVBQUUsSUFBSSxDQUFDLFdBQVc7WUFDN0IsUUFBUSxFQUFFLElBQUksQ0FBQyxRQUFRO1NBQ3hCLENBQ0YsQ0FBQztJQUNKLENBQUM7SUFFRDs7T0FFRztJQUNILGNBQWM7UUFDWixNQUFNLFlBQVksR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLFdBQVcsRUFBRSxDQUFDO1FBQ2pELE1BQU0sY0FBYyxHQUFHLElBQUksQ0FBQyxpQkFBaUIsRUFBRSxDQUFDLENBQUMsQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDLENBQUMscUJBQXFCLENBQUM7UUFDNUYsTUFBTSxVQUFVLEdBQUcsSUFBSSxDQUFDLG9CQUFvQixDQUFDO1FBQzdDLE1BQU0sV0FBVyxHQUFHLElBQUksQ0FBQyxPQUFPLENBQUMsV0FBVyxDQUFDLE9BQU8sQ0FBQyxHQUFHLEVBQUUsR0FBRyxDQUFDLENBQUM7UUFFL0QsT0FBTyxHQUFHLFlBQVksYUFBYSxJQUFJLENBQUMsa0JBQWtCLG9CQUFvQixJQUFJLENBQUMsS0FBSyxvQkFBb0IsY0FBYyxLQUFLLFVBQVUseUJBQXlCLFdBQVcsRUFBRSxDQUFDO0lBQ2xMLENBQUM7SUFFRDs7T0FFRztJQUNILFVBQVU7UUFjUixNQUFNLFFBQVEsR0FBRyxJQUFJLENBQUMsdUJBQXVCLEVBQUUsQ0FBQztRQUVoRCxPQUFPO1lBQ0wsU0FBUyxFQUFFLHlCQUF5QjtZQUNwQyxnQkFBZ0IsRUFBRSxJQUFJLENBQUMsZ0JBQWdCO1lBQ3ZDLElBQUksRUFBRSxJQUFJLENBQUMsa0JBQWtCO1lBQzdCLEtBQUssRUFBRSxJQUFJLENBQUMsS0FBSztZQUNqQixRQUFRLEVBQUUsSUFBSSxDQUFDLFFBQVE7WUFDdkIsZUFBZSxFQUFFLElBQUksQ0FBQyxlQUFlO1lBQ3JDLHNCQUFzQixFQUFFLElBQUksQ0FBQyxzQkFBc0I7WUFDbkQsVUFBVSxFQUFFLElBQUksQ0FBQyxhQUFhLEVBQUU7WUFDaEMsb0JBQW9CLEVBQUUsSUFBSSxDQUFDLG9CQUFvQjtZQUMvQyxXQUFXLEVBQUUsSUFBSSxDQUFDLE9BQU8sQ0FBQyxXQUFXO1lBQ3JDLGdCQUFnQixFQUFFLFFBQVEsQ0FBQyxnQkFBZ0I7WUFDM0MsU0FBUyxFQUFFLElBQUksQ0FBQyxVQUFVLENBQUMsV0FBVyxFQUFFO1NBQ3pDLENBQUM7SUFDSixDQUFDO0NBQ0Y7QUFydUJELGdGQXF1QkMiLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxjb3JlXFxzZWN1cml0eVxcZG9tYWluXFxldmVudHNcXHJlY29tbWVuZGF0aW9uLWdlbmVyYXRlZC5kb21haW4tZXZlbnQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQmFzZURvbWFpbkV2ZW50LCBVbmlxdWVFbnRpdHlJZCB9IGZyb20gJy4uLy4uLy4uLy4uL3NoYXJlZC1rZXJuZWwnO1xyXG5pbXBvcnQgeyBBY3Rpb25UeXBlIH0gZnJvbSAnLi4vZW51bXMvYWN0aW9uLXR5cGUuZW51bSc7XHJcblxyXG4vKipcclxuICogUmVjb21tZW5kYXRpb24gR2VuZXJhdGVkIERvbWFpbiBFdmVudCBEYXRhXHJcbiAqL1xyXG5leHBvcnQgaW50ZXJmYWNlIFJlY29tbWVuZGF0aW9uR2VuZXJhdGVkRXZlbnREYXRhIHtcclxuICAvKiogSUQgb2YgdGhlIHJlY29tbWVuZGF0aW9uICovXHJcbiAgcmVjb21tZW5kYXRpb25JZDogc3RyaW5nO1xyXG4gIC8qKiBUeXBlIG9mIHJlY29tbWVuZGF0aW9uICovXHJcbiAgcmVjb21tZW5kYXRpb25UeXBlOiAnc2VjdXJpdHlfYWN0aW9uJyB8ICdwb2xpY3lfdXBkYXRlJyB8ICdjb25maWd1cmF0aW9uX2NoYW5nZScgfCAncHJvY2Vzc19pbXByb3ZlbWVudCcgfCAndHJhaW5pbmcnIHwgJ2NvbXBsaWFuY2UnIHwgJ3RocmVhdF9odW50aW5nJztcclxuICAvKiogUmVjb21tZW5kYXRpb24gdGl0bGUgKi9cclxuICB0aXRsZTogc3RyaW5nO1xyXG4gIC8qKiBEZXRhaWxlZCByZWNvbW1lbmRhdGlvbiBkZXNjcmlwdGlvbiAqL1xyXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XHJcbiAgLyoqIFByaW9yaXR5IGxldmVsIG9mIHRoZSByZWNvbW1lbmRhdGlvbiAqL1xyXG4gIHByaW9yaXR5OiAnbG93JyB8ICdtZWRpdW0nIHwgJ2hpZ2gnIHwgJ2NyaXRpY2FsJztcclxuICAvKiogQ29uZmlkZW5jZSBzY29yZSBpbiB0aGUgcmVjb21tZW5kYXRpb24gKDAtMTAwKSAqL1xyXG4gIGNvbmZpZGVuY2VTY29yZTogbnVtYmVyO1xyXG4gIC8qKiBSaXNrIHJlZHVjdGlvbiBwb3RlbnRpYWwgKDAtMTAwKSAqL1xyXG4gIHJpc2tSZWR1Y3Rpb25Qb3RlbnRpYWw6IG51bWJlcjtcclxuICAvKiogSW1wbGVtZW50YXRpb24gZWZmb3J0IHJlcXVpcmVkICovXHJcbiAgaW1wbGVtZW50YXRpb25FZmZvcnQ6ICdsb3cnIHwgJ21lZGl1bScgfCAnaGlnaCc7XHJcbiAgLyoqIEVzdGltYXRlZCBpbXBsZW1lbnRhdGlvbiB0aW1lIGluIGhvdXJzICovXHJcbiAgZXN0aW1hdGVkSW1wbGVtZW50YXRpb25UaW1lOiBudW1iZXI7XHJcbiAgLyoqIENvc3QgZXN0aW1hdGUgZm9yIGltcGxlbWVudGF0aW9uICovXHJcbiAgY29zdEVzdGltYXRlPzoge1xyXG4gICAgYW1vdW50OiBudW1iZXI7XHJcbiAgICBjdXJyZW5jeTogc3RyaW5nO1xyXG4gICAgY2F0ZWdvcnk6ICdsb3cnIHwgJ21lZGl1bScgfCAnaGlnaCc7XHJcbiAgfTtcclxuICAvKiogU291cmNlIHRoYXQgZ2VuZXJhdGVkIHRoZSByZWNvbW1lbmRhdGlvbiAqL1xyXG4gIHNvdXJjZToge1xyXG4gICAgdHlwZTogJ2FpX2FuYWx5c2lzJyB8ICd0aHJlYXRfaW50ZWxsaWdlbmNlJyB8ICdjb21wbGlhbmNlX3NjYW4nIHwgJ3Z1bG5lcmFiaWxpdHlfYXNzZXNzbWVudCcgfCAnaW5jaWRlbnRfYW5hbHlzaXMnIHwgJ21hbnVhbF9yZXZpZXcnO1xyXG4gICAgaWRlbnRpZmllcjogc3RyaW5nO1xyXG4gICAgdmVyc2lvbj86IHN0cmluZztcclxuICB9O1xyXG4gIC8qKiBDb250ZXh0IHRoYXQgdHJpZ2dlcmVkIHRoZSByZWNvbW1lbmRhdGlvbiAqL1xyXG4gIGNvbnRleHQ6IHtcclxuICAgIHRyaWdnZXJUeXBlOiAndGhyZWF0X2RldGVjdGVkJyB8ICd2dWxuZXJhYmlsaXR5X2ZvdW5kJyB8ICdpbmNpZGVudF9vY2N1cnJlZCcgfCAnY29tcGxpYW5jZV9nYXAnIHwgJ3BvbGljeV92aW9sYXRpb24nIHwgJ3NjaGVkdWxlZF9yZXZpZXcnO1xyXG4gICAgdHJpZ2dlcklkczogc3RyaW5nW107XHJcbiAgICByZWxhdGVkQXNzZXRzPzogc3RyaW5nW107XHJcbiAgICBhZmZlY3RlZFN5c3RlbXM/OiBzdHJpbmdbXTtcclxuICB9O1xyXG4gIC8qKiBSZWNvbW1lbmRlZCBhY3Rpb25zICovXHJcbiAgcmVjb21tZW5kZWRBY3Rpb25zOiBBcnJheTx7XHJcbiAgICBhY3Rpb25UeXBlOiBBY3Rpb25UeXBlO1xyXG4gICAgZGVzY3JpcHRpb246IHN0cmluZztcclxuICAgIHByaW9yaXR5OiAnbG93JyB8ICdtZWRpdW0nIHwgJ2hpZ2gnIHwgJ2NyaXRpY2FsJztcclxuICAgIGVzdGltYXRlZER1cmF0aW9uOiBudW1iZXI7IC8vIG1pbnV0ZXNcclxuICAgIHByZXJlcXVpc2l0ZXM/OiBzdHJpbmdbXTtcclxuICAgIHJpc2tzPzogc3RyaW5nW107XHJcbiAgfT47XHJcbiAgLyoqIFN1Y2Nlc3MgY3JpdGVyaWEgZm9yIHRoZSByZWNvbW1lbmRhdGlvbiAqL1xyXG4gIHN1Y2Nlc3NDcml0ZXJpYTogc3RyaW5nW107XHJcbiAgLyoqIEtleSBwZXJmb3JtYW5jZSBpbmRpY2F0b3JzIHRvIG1lYXN1cmUgc3VjY2VzcyAqL1xyXG4gIGtwaXM6IEFycmF5PHtcclxuICAgIG5hbWU6IHN0cmluZztcclxuICAgIGRlc2NyaXB0aW9uOiBzdHJpbmc7XHJcbiAgICB0YXJnZXRWYWx1ZTogc3RyaW5nO1xyXG4gICAgbWVhc3VyZW1lbnRNZXRob2Q6IHN0cmluZztcclxuICB9PjtcclxuICAvKiogQ29tcGxpYW5jZSBmcmFtZXdvcmtzIHRoaXMgcmVjb21tZW5kYXRpb24gYWRkcmVzc2VzICovXHJcbiAgY29tcGxpYW5jZUZyYW1ld29ya3M/OiBzdHJpbmdbXTtcclxuICAvKiogQnVzaW5lc3MganVzdGlmaWNhdGlvbiAqL1xyXG4gIGJ1c2luZXNzSnVzdGlmaWNhdGlvbjoge1xyXG4gICAgcmlza01pdGlnYXRpb246IHN0cmluZztcclxuICAgIGJ1c2luZXNzVmFsdWU6IHN0cmluZztcclxuICAgIGNvc3RCZW5lZml0OiBzdHJpbmc7XHJcbiAgfTtcclxuICAvKiogSW1wbGVtZW50YXRpb24gdGltZWxpbmUgKi9cclxuICB0aW1lbGluZToge1xyXG4gICAgcmVjb21tZW5kZWRTdGFydERhdGU6IERhdGU7XHJcbiAgICByZWNvbW1lbmRlZENvbXBsZXRpb25EYXRlOiBEYXRlO1xyXG4gICAgbWlsZXN0b25lczogQXJyYXk8e1xyXG4gICAgICBuYW1lOiBzdHJpbmc7XHJcbiAgICAgIGRlc2NyaXB0aW9uOiBzdHJpbmc7XHJcbiAgICAgIHRhcmdldERhdGU6IERhdGU7XHJcbiAgICAgIGRlcGVuZGVuY2llcz86IHN0cmluZ1tdO1xyXG4gICAgfT47XHJcbiAgfTtcclxuICAvKiogU3Rha2Vob2xkZXJzIGludm9sdmVkICovXHJcbiAgc3Rha2Vob2xkZXJzOiBBcnJheTx7XHJcbiAgICByb2xlOiBzdHJpbmc7XHJcbiAgICByZXNwb25zaWJpbGl0eTogc3RyaW5nO1xyXG4gICAgcmVxdWlyZWQ6IGJvb2xlYW47XHJcbiAgfT47XHJcbiAgLyoqIEFsdGVybmF0aXZlIGFwcHJvYWNoZXMgKi9cclxuICBhbHRlcm5hdGl2ZXM/OiBBcnJheTx7XHJcbiAgICB0aXRsZTogc3RyaW5nO1xyXG4gICAgZGVzY3JpcHRpb246IHN0cmluZztcclxuICAgIHByb3M6IHN0cmluZ1tdO1xyXG4gICAgY29uczogc3RyaW5nW107XHJcbiAgICBlZmZvcnQ6ICdsb3cnIHwgJ21lZGl1bScgfCAnaGlnaCc7XHJcbiAgICByaXNrUmVkdWN0aW9uOiBudW1iZXI7IC8vIDAtMTAwXHJcbiAgfT47XHJcbiAgLyoqIERlcGVuZGVuY2llcyBhbmQgcHJlcmVxdWlzaXRlcyAqL1xyXG4gIGRlcGVuZGVuY2llczogc3RyaW5nW107XHJcbiAgLyoqIFBvdGVudGlhbCByaXNrcyBvZiBpbXBsZW1lbnRhdGlvbiAqL1xyXG4gIGltcGxlbWVudGF0aW9uUmlza3M6IEFycmF5PHtcclxuICAgIHJpc2s6IHN0cmluZztcclxuICAgIGxpa2VsaWhvb2Q6ICdsb3cnIHwgJ21lZGl1bScgfCAnaGlnaCc7XHJcbiAgICBpbXBhY3Q6ICdsb3cnIHwgJ21lZGl1bScgfCAnaGlnaCc7XHJcbiAgICBtaXRpZ2F0aW9uOiBzdHJpbmc7XHJcbiAgfT47XHJcbiAgLyoqIFZhbGlkYXRpb24gYW5kIHRlc3RpbmcgcmVxdWlyZW1lbnRzICovXHJcbiAgdmFsaWRhdGlvblJlcXVpcmVtZW50czogc3RyaW5nW107XHJcbiAgLyoqIFJvbGxiYWNrIHBsYW4gKi9cclxuICByb2xsYmFja1BsYW4/OiB7XHJcbiAgICBkZXNjcmlwdGlvbjogc3RyaW5nO1xyXG4gICAgc3RlcHM6IHN0cmluZ1tdO1xyXG4gICAgZXN0aW1hdGVkVGltZTogbnVtYmVyOyAvLyBtaW51dGVzXHJcbiAgfTtcclxufVxyXG5cclxuLyoqXHJcbiAqIFJlY29tbWVuZGF0aW9uIEdlbmVyYXRlZCBEb21haW4gRXZlbnRcclxuICogXHJcbiAqIFJhaXNlZCB3aGVuIHRoZSBzeXN0ZW0gZ2VuZXJhdGVzIGEgbmV3IHNlY3VyaXR5IHJlY29tbWVuZGF0aW9uIGJhc2VkIG9uXHJcbiAqIHRocmVhdCBhbmFseXNpcywgdnVsbmVyYWJpbGl0eSBhc3Nlc3NtZW50LCBjb21wbGlhbmNlIGdhcHMsIG9yIG90aGVyIHNlY3VyaXR5XHJcbiAqIGluc2lnaHRzLiBUaGlzIGV2ZW50IHRyaWdnZXJzIHJlY29tbWVuZGF0aW9uIHJldmlldyBhbmQgaW1wbGVtZW50YXRpb24gd29ya2Zsb3dzLlxyXG4gKiBcclxuICogS2V5IGNoYXJhY3RlcmlzdGljczpcclxuICogLSBSZXByZXNlbnRzIEFJIG9yIHN5c3RlbS1nZW5lcmF0ZWQgc2VjdXJpdHkgcmVjb21tZW5kYXRpb25zXHJcbiAqIC0gQ29udGFpbnMgY29tcHJlaGVuc2l2ZSBpbXBsZW1lbnRhdGlvbiBndWlkYW5jZVxyXG4gKiAtIEluY2x1ZGVzIGJ1c2luZXNzIGp1c3RpZmljYXRpb24gYW5kIHJpc2sgYW5hbHlzaXNcclxuICogLSBUcmlnZ2VycyBhcHByb3ZhbCBhbmQgaW1wbGVtZW50YXRpb24gd29ya2Zsb3dzXHJcbiAqIFxyXG4gKiBEb3duc3RyZWFtIHByb2Nlc3NlcyB0cmlnZ2VyZWQ6XHJcbiAqIC0gUmVjb21tZW5kYXRpb24gcmV2aWV3IGFuZCBhcHByb3ZhbFxyXG4gKiAtIEltcGxlbWVudGF0aW9uIHBsYW5uaW5nIGFuZCBzY2hlZHVsaW5nXHJcbiAqIC0gU3Rha2Vob2xkZXIgbm90aWZpY2F0aW9uIGFuZCBhc3NpZ25tZW50XHJcbiAqIC0gUHJvZ3Jlc3MgdHJhY2tpbmcgYW5kIG1vbml0b3JpbmdcclxuICogLSBTdWNjZXNzIG1lYXN1cmVtZW50IGFuZCB2YWxpZGF0aW9uXHJcbiAqL1xyXG5leHBvcnQgY2xhc3MgUmVjb21tZW5kYXRpb25HZW5lcmF0ZWREb21haW5FdmVudCBleHRlbmRzIEJhc2VEb21haW5FdmVudDxSZWNvbW1lbmRhdGlvbkdlbmVyYXRlZEV2ZW50RGF0YT4ge1xyXG4gIGNvbnN0cnVjdG9yKFxyXG4gICAgYWdncmVnYXRlSWQ6IFVuaXF1ZUVudGl0eUlkLFxyXG4gICAgZXZlbnREYXRhOiBSZWNvbW1lbmRhdGlvbkdlbmVyYXRlZEV2ZW50RGF0YSxcclxuICAgIG9wdGlvbnM/OiB7XHJcbiAgICAgIGV2ZW50SWQ/OiBVbmlxdWVFbnRpdHlJZDtcclxuICAgICAgb2NjdXJyZWRPbj86IERhdGU7XHJcbiAgICAgIGV2ZW50VmVyc2lvbj86IG51bWJlcjtcclxuICAgICAgY29ycmVsYXRpb25JZD86IHN0cmluZztcclxuICAgICAgY2F1c2F0aW9uSWQ/OiBzdHJpbmc7XHJcbiAgICAgIG1ldGFkYXRhPzogUmVjb3JkPHN0cmluZywgYW55PjtcclxuICAgIH1cclxuICApIHtcclxuICAgIHN1cGVyKGFnZ3JlZ2F0ZUlkLCBldmVudERhdGEsIHtcclxuICAgICAgZXZlbnRWZXJzaW9uOiAxLFxyXG4gICAgICAuLi5vcHRpb25zLFxyXG4gICAgICBtZXRhZGF0YToge1xyXG4gICAgICAgIGV2ZW50VHlwZTogJ1JlY29tbWVuZGF0aW9uR2VuZXJhdGVkJyxcclxuICAgICAgICBkb21haW46ICdTZWN1cml0eScsXHJcbiAgICAgICAgYWdncmVnYXRlVHlwZTogJ1JlY29tbWVuZGF0aW9uJyxcclxuICAgICAgICBwcm9jZXNzaW5nU3RhZ2U6ICdnZW5lcmF0aW9uJyxcclxuICAgICAgICAuLi5vcHRpb25zPy5tZXRhZGF0YSxcclxuICAgICAgfSxcclxuICAgIH0pO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IHRoZSByZWNvbW1lbmRhdGlvbiBJRFxyXG4gICAqL1xyXG4gIGdldCByZWNvbW1lbmRhdGlvbklkKCk6IHN0cmluZyB7XHJcbiAgICByZXR1cm4gdGhpcy5ldmVudERhdGEucmVjb21tZW5kYXRpb25JZDtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCB0aGUgcmVjb21tZW5kYXRpb24gdHlwZVxyXG4gICAqL1xyXG4gIGdldCByZWNvbW1lbmRhdGlvblR5cGUoKTogUmVjb21tZW5kYXRpb25HZW5lcmF0ZWRFdmVudERhdGFbJ3JlY29tbWVuZGF0aW9uVHlwZSddIHtcclxuICAgIHJldHVybiB0aGlzLmV2ZW50RGF0YS5yZWNvbW1lbmRhdGlvblR5cGU7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgdGhlIHJlY29tbWVuZGF0aW9uIHRpdGxlXHJcbiAgICovXHJcbiAgZ2V0IHRpdGxlKCk6IHN0cmluZyB7XHJcbiAgICByZXR1cm4gdGhpcy5ldmVudERhdGEudGl0bGU7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgdGhlIHJlY29tbWVuZGF0aW9uIGRlc2NyaXB0aW9uXHJcbiAgICovXHJcbiAgZ2V0IGRlc2NyaXB0aW9uKCk6IHN0cmluZyB7XHJcbiAgICByZXR1cm4gdGhpcy5ldmVudERhdGEuZGVzY3JpcHRpb247XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgdGhlIHByaW9yaXR5IGxldmVsXHJcbiAgICovXHJcbiAgZ2V0IHByaW9yaXR5KCk6IFJlY29tbWVuZGF0aW9uR2VuZXJhdGVkRXZlbnREYXRhWydwcmlvcml0eSddIHtcclxuICAgIHJldHVybiB0aGlzLmV2ZW50RGF0YS5wcmlvcml0eTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCB0aGUgY29uZmlkZW5jZSBzY29yZVxyXG4gICAqL1xyXG4gIGdldCBjb25maWRlbmNlU2NvcmUoKTogbnVtYmVyIHtcclxuICAgIHJldHVybiB0aGlzLmV2ZW50RGF0YS5jb25maWRlbmNlU2NvcmU7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgdGhlIHJpc2sgcmVkdWN0aW9uIHBvdGVudGlhbFxyXG4gICAqL1xyXG4gIGdldCByaXNrUmVkdWN0aW9uUG90ZW50aWFsKCk6IG51bWJlciB7XHJcbiAgICByZXR1cm4gdGhpcy5ldmVudERhdGEucmlza1JlZHVjdGlvblBvdGVudGlhbDtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCB0aGUgaW1wbGVtZW50YXRpb24gZWZmb3J0XHJcbiAgICovXHJcbiAgZ2V0IGltcGxlbWVudGF0aW9uRWZmb3J0KCk6IFJlY29tbWVuZGF0aW9uR2VuZXJhdGVkRXZlbnREYXRhWydpbXBsZW1lbnRhdGlvbkVmZm9ydCddIHtcclxuICAgIHJldHVybiB0aGlzLmV2ZW50RGF0YS5pbXBsZW1lbnRhdGlvbkVmZm9ydDtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCB0aGUgZXN0aW1hdGVkIGltcGxlbWVudGF0aW9uIHRpbWVcclxuICAgKi9cclxuICBnZXQgZXN0aW1hdGVkSW1wbGVtZW50YXRpb25UaW1lKCk6IG51bWJlciB7XHJcbiAgICByZXR1cm4gdGhpcy5ldmVudERhdGEuZXN0aW1hdGVkSW1wbGVtZW50YXRpb25UaW1lO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IHRoZSBjb3N0IGVzdGltYXRlXHJcbiAgICovXHJcbiAgZ2V0IGNvc3RFc3RpbWF0ZSgpOiBSZWNvbW1lbmRhdGlvbkdlbmVyYXRlZEV2ZW50RGF0YVsnY29zdEVzdGltYXRlJ10ge1xyXG4gICAgcmV0dXJuIHRoaXMuZXZlbnREYXRhLmNvc3RFc3RpbWF0ZTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCB0aGUgcmVjb21tZW5kYXRpb24gc291cmNlXHJcbiAgICovXHJcbiAgZ2V0IHNvdXJjZSgpOiBSZWNvbW1lbmRhdGlvbkdlbmVyYXRlZEV2ZW50RGF0YVsnc291cmNlJ10ge1xyXG4gICAgcmV0dXJuIHRoaXMuZXZlbnREYXRhLnNvdXJjZTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCB0aGUgY29udGV4dCB0aGF0IHRyaWdnZXJlZCB0aGUgcmVjb21tZW5kYXRpb25cclxuICAgKi9cclxuICBnZXQgY29udGV4dCgpOiBSZWNvbW1lbmRhdGlvbkdlbmVyYXRlZEV2ZW50RGF0YVsnY29udGV4dCddIHtcclxuICAgIHJldHVybiB0aGlzLmV2ZW50RGF0YS5jb250ZXh0O1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IHRoZSByZWNvbW1lbmRlZCBhY3Rpb25zXHJcbiAgICovXHJcbiAgZ2V0IHJlY29tbWVuZGVkQWN0aW9ucygpOiBSZWNvbW1lbmRhdGlvbkdlbmVyYXRlZEV2ZW50RGF0YVsncmVjb21tZW5kZWRBY3Rpb25zJ10ge1xyXG4gICAgcmV0dXJuIHRoaXMuZXZlbnREYXRhLnJlY29tbWVuZGVkQWN0aW9ucztcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCB0aGUgc3VjY2VzcyBjcml0ZXJpYVxyXG4gICAqL1xyXG4gIGdldCBzdWNjZXNzQ3JpdGVyaWEoKTogc3RyaW5nW10ge1xyXG4gICAgcmV0dXJuIHRoaXMuZXZlbnREYXRhLnN1Y2Nlc3NDcml0ZXJpYTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCB0aGUgS1BJc1xyXG4gICAqL1xyXG4gIGdldCBrcGlzKCk6IFJlY29tbWVuZGF0aW9uR2VuZXJhdGVkRXZlbnREYXRhWydrcGlzJ10ge1xyXG4gICAgcmV0dXJuIHRoaXMuZXZlbnREYXRhLmtwaXM7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgdGhlIGNvbXBsaWFuY2UgZnJhbWV3b3Jrc1xyXG4gICAqL1xyXG4gIGdldCBjb21wbGlhbmNlRnJhbWV3b3JrcygpOiBzdHJpbmdbXSB8IHVuZGVmaW5lZCB7XHJcbiAgICByZXR1cm4gdGhpcy5ldmVudERhdGEuY29tcGxpYW5jZUZyYW1ld29ya3M7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgdGhlIGJ1c2luZXNzIGp1c3RpZmljYXRpb25cclxuICAgKi9cclxuICBnZXQgYnVzaW5lc3NKdXN0aWZpY2F0aW9uKCk6IFJlY29tbWVuZGF0aW9uR2VuZXJhdGVkRXZlbnREYXRhWydidXNpbmVzc0p1c3RpZmljYXRpb24nXSB7XHJcbiAgICByZXR1cm4gdGhpcy5ldmVudERhdGEuYnVzaW5lc3NKdXN0aWZpY2F0aW9uO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IHRoZSBpbXBsZW1lbnRhdGlvbiB0aW1lbGluZVxyXG4gICAqL1xyXG4gIGdldCB0aW1lbGluZSgpOiBSZWNvbW1lbmRhdGlvbkdlbmVyYXRlZEV2ZW50RGF0YVsndGltZWxpbmUnXSB7XHJcbiAgICByZXR1cm4gdGhpcy5ldmVudERhdGEudGltZWxpbmU7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgdGhlIHN0YWtlaG9sZGVyc1xyXG4gICAqL1xyXG4gIGdldCBzdGFrZWhvbGRlcnMoKTogUmVjb21tZW5kYXRpb25HZW5lcmF0ZWRFdmVudERhdGFbJ3N0YWtlaG9sZGVycyddIHtcclxuICAgIHJldHVybiB0aGlzLmV2ZW50RGF0YS5zdGFrZWhvbGRlcnM7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgdGhlIGFsdGVybmF0aXZlc1xyXG4gICAqL1xyXG4gIGdldCBhbHRlcm5hdGl2ZXMoKTogUmVjb21tZW5kYXRpb25HZW5lcmF0ZWRFdmVudERhdGFbJ2FsdGVybmF0aXZlcyddIHtcclxuICAgIHJldHVybiB0aGlzLmV2ZW50RGF0YS5hbHRlcm5hdGl2ZXM7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgdGhlIGRlcGVuZGVuY2llc1xyXG4gICAqL1xyXG4gIGdldCBkZXBlbmRlbmNpZXMoKTogc3RyaW5nW10ge1xyXG4gICAgcmV0dXJuIHRoaXMuZXZlbnREYXRhLmRlcGVuZGVuY2llcztcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCB0aGUgaW1wbGVtZW50YXRpb24gcmlza3NcclxuICAgKi9cclxuICBnZXQgaW1wbGVtZW50YXRpb25SaXNrcygpOiBSZWNvbW1lbmRhdGlvbkdlbmVyYXRlZEV2ZW50RGF0YVsnaW1wbGVtZW50YXRpb25SaXNrcyddIHtcclxuICAgIHJldHVybiB0aGlzLmV2ZW50RGF0YS5pbXBsZW1lbnRhdGlvblJpc2tzO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IHRoZSB2YWxpZGF0aW9uIHJlcXVpcmVtZW50c1xyXG4gICAqL1xyXG4gIGdldCB2YWxpZGF0aW9uUmVxdWlyZW1lbnRzKCk6IHN0cmluZ1tdIHtcclxuICAgIHJldHVybiB0aGlzLmV2ZW50RGF0YS52YWxpZGF0aW9uUmVxdWlyZW1lbnRzO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IHRoZSByb2xsYmFjayBwbGFuXHJcbiAgICovXHJcbiAgZ2V0IHJvbGxiYWNrUGxhbigpOiBSZWNvbW1lbmRhdGlvbkdlbmVyYXRlZEV2ZW50RGF0YVsncm9sbGJhY2tQbGFuJ10ge1xyXG4gICAgcmV0dXJuIHRoaXMuZXZlbnREYXRhLnJvbGxiYWNrUGxhbjtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENoZWNrIGlmIHJlY29tbWVuZGF0aW9uIGlzIGNyaXRpY2FsIHByaW9yaXR5XHJcbiAgICovXHJcbiAgaXNDcml0aWNhbFByaW9yaXR5KCk6IGJvb2xlYW4ge1xyXG4gICAgcmV0dXJuIHRoaXMucHJpb3JpdHkgPT09ICdjcml0aWNhbCc7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDaGVjayBpZiByZWNvbW1lbmRhdGlvbiBpcyBoaWdoIHByaW9yaXR5IG9yIGFib3ZlXHJcbiAgICovXHJcbiAgaXNIaWdoUHJpb3JpdHlPckFib3ZlKCk6IGJvb2xlYW4ge1xyXG4gICAgcmV0dXJuIFsnaGlnaCcsICdjcml0aWNhbCddLmluY2x1ZGVzKHRoaXMucHJpb3JpdHkpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2hlY2sgaWYgcmVjb21tZW5kYXRpb24gaGFzIGhpZ2ggY29uZmlkZW5jZVxyXG4gICAqL1xyXG4gIGhhc0hpZ2hDb25maWRlbmNlKCk6IGJvb2xlYW4ge1xyXG4gICAgcmV0dXJuIHRoaXMuY29uZmlkZW5jZVNjb3JlID49IDgwO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2hlY2sgaWYgcmVjb21tZW5kYXRpb24gaGFzIGxvdyBjb25maWRlbmNlXHJcbiAgICovXHJcbiAgaGFzTG93Q29uZmlkZW5jZSgpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLmNvbmZpZGVuY2VTY29yZSA8IDYwO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2hlY2sgaWYgcmVjb21tZW5kYXRpb24gaGFzIGhpZ2ggcmlzayByZWR1Y3Rpb24gcG90ZW50aWFsXHJcbiAgICovXHJcbiAgaGFzSGlnaFJpc2tSZWR1Y3Rpb24oKTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gdGhpcy5yaXNrUmVkdWN0aW9uUG90ZW50aWFsID49IDcwO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2hlY2sgaWYgcmVjb21tZW5kYXRpb24gaXMgbG93IGVmZm9ydFxyXG4gICAqL1xyXG4gIGlzTG93RWZmb3J0KCk6IGJvb2xlYW4ge1xyXG4gICAgcmV0dXJuIHRoaXMuaW1wbGVtZW50YXRpb25FZmZvcnQgPT09ICdsb3cnO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2hlY2sgaWYgcmVjb21tZW5kYXRpb24gaXMgaGlnaCBlZmZvcnRcclxuICAgKi9cclxuICBpc0hpZ2hFZmZvcnQoKTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gdGhpcy5pbXBsZW1lbnRhdGlvbkVmZm9ydCA9PT0gJ2hpZ2gnO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2hlY2sgaWYgcmVjb21tZW5kYXRpb24gaXMgcXVpY2sgdG8gaW1wbGVtZW50ICh1bmRlciA4IGhvdXJzKVxyXG4gICAqL1xyXG4gIGlzUXVpY2tJbXBsZW1lbnRhdGlvbigpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLmVzdGltYXRlZEltcGxlbWVudGF0aW9uVGltZSA8IDg7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDaGVjayBpZiByZWNvbW1lbmRhdGlvbiBpcyBsb25nIGltcGxlbWVudGF0aW9uIChvdmVyIDQwIGhvdXJzKVxyXG4gICAqL1xyXG4gIGlzTG9uZ0ltcGxlbWVudGF0aW9uKCk6IGJvb2xlYW4ge1xyXG4gICAgcmV0dXJuIHRoaXMuZXN0aW1hdGVkSW1wbGVtZW50YXRpb25UaW1lID4gNDA7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDaGVjayBpZiByZWNvbW1lbmRhdGlvbiBoYXMgY29zdCBlc3RpbWF0ZVxyXG4gICAqL1xyXG4gIGhhc0Nvc3RFc3RpbWF0ZSgpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLmNvc3RFc3RpbWF0ZSAhPT0gdW5kZWZpbmVkO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2hlY2sgaWYgcmVjb21tZW5kYXRpb24gaXMgaGlnaCBjb3N0XHJcbiAgICovXHJcbiAgaXNIaWdoQ29zdCgpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLmNvc3RFc3RpbWF0ZT8uY2F0ZWdvcnkgPT09ICdoaWdoJztcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENoZWNrIGlmIHJlY29tbWVuZGF0aW9uIGFkZHJlc3NlcyBjb21wbGlhbmNlXHJcbiAgICovXHJcbiAgYWRkcmVzc2VzQ29tcGxpYW5jZSgpOiBib29sZWFuIHtcclxuICAgIHJldHVybiAodGhpcy5jb21wbGlhbmNlRnJhbWV3b3Jrcz8ubGVuZ3RoIHx8IDApID4gMDtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENoZWNrIGlmIHJlY29tbWVuZGF0aW9uIGhhcyBhbHRlcm5hdGl2ZXNcclxuICAgKi9cclxuICBoYXNBbHRlcm5hdGl2ZXMoKTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gKHRoaXMuYWx0ZXJuYXRpdmVzPy5sZW5ndGggfHwgMCkgPiAwO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2hlY2sgaWYgcmVjb21tZW5kYXRpb24gaGFzIGRlcGVuZGVuY2llc1xyXG4gICAqL1xyXG4gIGhhc0RlcGVuZGVuY2llcygpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLmRlcGVuZGVuY2llcy5sZW5ndGggPiAwO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2hlY2sgaWYgcmVjb21tZW5kYXRpb24gaGFzIGhpZ2ggaW1wbGVtZW50YXRpb24gcmlza3NcclxuICAgKi9cclxuICBoYXNIaWdoSW1wbGVtZW50YXRpb25SaXNrcygpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLmltcGxlbWVudGF0aW9uUmlza3Muc29tZShyaXNrID0+IFxyXG4gICAgICByaXNrLmxpa2VsaWhvb2QgPT09ICdoaWdoJyAmJiByaXNrLmltcGFjdCA9PT0gJ2hpZ2gnXHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2hlY2sgaWYgcmVjb21tZW5kYXRpb24gaGFzIHJvbGxiYWNrIHBsYW5cclxuICAgKi9cclxuICBoYXNSb2xsYmFja1BsYW4oKTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gdGhpcy5yb2xsYmFja1BsYW4gIT09IHVuZGVmaW5lZDtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENoZWNrIGlmIHJlY29tbWVuZGF0aW9uIGlzIEFJLWdlbmVyYXRlZFxyXG4gICAqL1xyXG4gIGlzQUlHZW5lcmF0ZWQoKTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gdGhpcy5zb3VyY2UudHlwZSA9PT0gJ2FpX2FuYWx5c2lzJztcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENoZWNrIGlmIHJlY29tbWVuZGF0aW9uIGlzIHRyaWdnZXJlZCBieSB0aHJlYXRcclxuICAgKi9cclxuICBpc1RyaWdnZXJlZEJ5VGhyZWF0KCk6IGJvb2xlYW4ge1xyXG4gICAgcmV0dXJuIHRoaXMuY29udGV4dC50cmlnZ2VyVHlwZSA9PT0gJ3RocmVhdF9kZXRlY3RlZCc7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDaGVjayBpZiByZWNvbW1lbmRhdGlvbiBpcyB0cmlnZ2VyZWQgYnkgdnVsbmVyYWJpbGl0eVxyXG4gICAqL1xyXG4gIGlzVHJpZ2dlcmVkQnlWdWxuZXJhYmlsaXR5KCk6IGJvb2xlYW4ge1xyXG4gICAgcmV0dXJuIHRoaXMuY29udGV4dC50cmlnZ2VyVHlwZSA9PT0gJ3Z1bG5lcmFiaWxpdHlfZm91bmQnO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2hlY2sgaWYgcmVjb21tZW5kYXRpb24gaXMgdHJpZ2dlcmVkIGJ5IGluY2lkZW50XHJcbiAgICovXHJcbiAgaXNUcmlnZ2VyZWRCeUluY2lkZW50KCk6IGJvb2xlYW4ge1xyXG4gICAgcmV0dXJuIHRoaXMuY29udGV4dC50cmlnZ2VyVHlwZSA9PT0gJ2luY2lkZW50X29jY3VycmVkJztcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCByZWNvbW1lbmRhdGlvbiB2YWx1ZSBzY29yZSAoMC0xMDApXHJcbiAgICovXHJcbiAgZ2V0VmFsdWVTY29yZSgpOiBudW1iZXIge1xyXG4gICAgbGV0IHNjb3JlID0gMDtcclxuICAgIFxyXG4gICAgLy8gUmlzayByZWR1Y3Rpb24gcG90ZW50aWFsICg0MCUgd2VpZ2h0KVxyXG4gICAgc2NvcmUgKz0gdGhpcy5yaXNrUmVkdWN0aW9uUG90ZW50aWFsICogMC40O1xyXG4gICAgXHJcbiAgICAvLyBDb25maWRlbmNlIHNjb3JlICgzMCUgd2VpZ2h0KVxyXG4gICAgc2NvcmUgKz0gdGhpcy5jb25maWRlbmNlU2NvcmUgKiAwLjM7XHJcbiAgICBcclxuICAgIC8vIEltcGxlbWVudGF0aW9uIGVmZm9ydCAoMjAlIHdlaWdodCAtIGludmVyc2UpXHJcbiAgICBjb25zdCBlZmZvcnRTY29yZSA9IHRoaXMuaW1wbGVtZW50YXRpb25FZmZvcnQgPT09ICdsb3cnID8gMTAwIDogXHJcbiAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5pbXBsZW1lbnRhdGlvbkVmZm9ydCA9PT0gJ21lZGl1bScgPyA2MCA6IDMwO1xyXG4gICAgc2NvcmUgKz0gZWZmb3J0U2NvcmUgKiAwLjI7XHJcbiAgICBcclxuICAgIC8vIFByaW9yaXR5ICgxMCUgd2VpZ2h0KVxyXG4gICAgY29uc3QgcHJpb3JpdHlTY29yZSA9IHRoaXMucHJpb3JpdHkgPT09ICdjcml0aWNhbCcgPyAxMDAgOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5wcmlvcml0eSA9PT0gJ2hpZ2gnID8gODAgOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5wcmlvcml0eSA9PT0gJ21lZGl1bScgPyA2MCA6IDQwO1xyXG4gICAgc2NvcmUgKz0gcHJpb3JpdHlTY29yZSAqIDAuMTtcclxuICAgIFxyXG4gICAgcmV0dXJuIE1hdGgucm91bmQoc2NvcmUpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IGltcGxlbWVudGF0aW9uIHVyZ2VuY3kgaW4gZGF5c1xyXG4gICAqL1xyXG4gIGdldEltcGxlbWVudGF0aW9uVXJnZW5jeSgpOiBudW1iZXIge1xyXG4gICAgc3dpdGNoICh0aGlzLnByaW9yaXR5KSB7XHJcbiAgICAgIGNhc2UgJ2NyaXRpY2FsJzogcmV0dXJuIDE7ICAgIC8vIDEgZGF5XHJcbiAgICAgIGNhc2UgJ2hpZ2gnOiByZXR1cm4gNzsgICAgICAgIC8vIDEgd2Vla1xyXG4gICAgICBjYXNlICdtZWRpdW0nOiByZXR1cm4gMzA7ICAgICAvLyAxIG1vbnRoXHJcbiAgICAgIGNhc2UgJ2xvdyc6IHJldHVybiA5MDsgICAgICAgIC8vIDMgbW9udGhzXHJcbiAgICAgIGRlZmF1bHQ6IHJldHVybiA5MDtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCBhcHByb3ZhbCByZXF1aXJlbWVudHNcclxuICAgKi9cclxuICBnZXRBcHByb3ZhbFJlcXVpcmVtZW50cygpOiB7XHJcbiAgICByZXF1aXJlc0FwcHJvdmFsOiBib29sZWFuO1xyXG4gICAgYXBwcm92YWxMZXZlbDogJ3RlYW1fbGVhZCcgfCAnbWFuYWdlcicgfCAnZGlyZWN0b3InIHwgJ2V4ZWN1dGl2ZSc7XHJcbiAgICByZXF1aXJlZEFwcHJvdmVyczogc3RyaW5nW107XHJcbiAgICBhcHByb3ZhbENyaXRlcmlhOiBzdHJpbmdbXTtcclxuICB9IHtcclxuICAgIGxldCBhcHByb3ZhbExldmVsOiAndGVhbV9sZWFkJyB8ICdtYW5hZ2VyJyB8ICdkaXJlY3RvcicgfCAnZXhlY3V0aXZlJyA9ICd0ZWFtX2xlYWQnO1xyXG4gICAgY29uc3QgcmVxdWlyZWRBcHByb3ZlcnM6IHN0cmluZ1tdID0gW107XHJcbiAgICBjb25zdCBhcHByb3ZhbENyaXRlcmlhOiBzdHJpbmdbXSA9IFtdO1xyXG4gICAgXHJcbiAgICBpZiAodGhpcy5pc0NyaXRpY2FsUHJpb3JpdHkoKSB8fCB0aGlzLmlzSGlnaENvc3QoKSkge1xyXG4gICAgICBhcHByb3ZhbExldmVsID0gJ2V4ZWN1dGl2ZSc7XHJcbiAgICAgIHJlcXVpcmVkQXBwcm92ZXJzLnB1c2goJ3NlY3VyaXR5X2RpcmVjdG9yJywgJ2Npc28nKTtcclxuICAgICAgYXBwcm92YWxDcml0ZXJpYS5wdXNoKCdFeGVjdXRpdmUgYXBwcm92YWwgcmVxdWlyZWQgZm9yIGNyaXRpY2FsL2hpZ2gtY29zdCByZWNvbW1lbmRhdGlvbnMnKTtcclxuICAgIH0gZWxzZSBpZiAodGhpcy5pc0hpZ2hQcmlvcml0eU9yQWJvdmUoKSB8fCB0aGlzLmhhc0hpZ2hJbXBsZW1lbnRhdGlvblJpc2tzKCkpIHtcclxuICAgICAgYXBwcm92YWxMZXZlbCA9ICdkaXJlY3Rvcic7XHJcbiAgICAgIHJlcXVpcmVkQXBwcm92ZXJzLnB1c2goJ3NlY3VyaXR5X2RpcmVjdG9yJyk7XHJcbiAgICAgIGFwcHJvdmFsQ3JpdGVyaWEucHVzaCgnRGlyZWN0b3IgYXBwcm92YWwgcmVxdWlyZWQgZm9yIGhpZ2gtcHJpb3JpdHkvaGlnaC1yaXNrIHJlY29tbWVuZGF0aW9ucycpO1xyXG4gICAgfSBlbHNlIGlmICh0aGlzLmltcGxlbWVudGF0aW9uRWZmb3J0ID09PSAnaGlnaCcgfHwgdGhpcy5oYXNEZXBlbmRlbmNpZXMoKSkge1xyXG4gICAgICBhcHByb3ZhbExldmVsID0gJ21hbmFnZXInO1xyXG4gICAgICByZXF1aXJlZEFwcHJvdmVycy5wdXNoKCdzZWN1cml0eV9tYW5hZ2VyJyk7XHJcbiAgICAgIGFwcHJvdmFsQ3JpdGVyaWEucHVzaCgnTWFuYWdlciBhcHByb3ZhbCByZXF1aXJlZCBmb3IgaGlnaC1lZmZvcnQgcmVjb21tZW5kYXRpb25zJyk7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIGlmICh0aGlzLmFkZHJlc3Nlc0NvbXBsaWFuY2UoKSkge1xyXG4gICAgICByZXF1aXJlZEFwcHJvdmVycy5wdXNoKCdjb21wbGlhbmNlX29mZmljZXInKTtcclxuICAgICAgYXBwcm92YWxDcml0ZXJpYS5wdXNoKCdDb21wbGlhbmNlIG9mZmljZXIgYXBwcm92YWwgcmVxdWlyZWQgZm9yIGNvbXBsaWFuY2UtcmVsYXRlZCByZWNvbW1lbmRhdGlvbnMnKTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgcmVxdWlyZXNBcHByb3ZhbDogcmVxdWlyZWRBcHByb3ZlcnMubGVuZ3RoID4gMCxcclxuICAgICAgYXBwcm92YWxMZXZlbCxcclxuICAgICAgcmVxdWlyZWRBcHByb3ZlcnMsXHJcbiAgICAgIGFwcHJvdmFsQ3JpdGVyaWEsXHJcbiAgICB9O1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IG5vdGlmaWNhdGlvbiB0YXJnZXRzXHJcbiAgICovXHJcbiAgZ2V0Tm90aWZpY2F0aW9uVGFyZ2V0cygpOiBzdHJpbmdbXSB7XHJcbiAgICBjb25zdCB0YXJnZXRzOiBzdHJpbmdbXSA9IFsnc2VjdXJpdHlfdGVhbSddO1xyXG4gICAgXHJcbiAgICBpZiAodGhpcy5pc0NyaXRpY2FsUHJpb3JpdHkoKSkge1xyXG4gICAgICB0YXJnZXRzLnB1c2goJ3NlY3VyaXR5X2xlYWRlcnNoaXAnLCAnaW5jaWRlbnRfcmVzcG9uc2VfdGVhbScpO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICBpZiAodGhpcy5pc0hpZ2hQcmlvcml0eU9yQWJvdmUoKSkge1xyXG4gICAgICB0YXJnZXRzLnB1c2goJ3NlY3VyaXR5X21hbmFnZXJzJyk7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIGlmICh0aGlzLmFkZHJlc3Nlc0NvbXBsaWFuY2UoKSkge1xyXG4gICAgICB0YXJnZXRzLnB1c2goJ2NvbXBsaWFuY2VfdGVhbScpO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICBpZiAodGhpcy5pc1RyaWdnZXJlZEJ5VGhyZWF0KCkgfHwgdGhpcy5pc1RyaWdnZXJlZEJ5SW5jaWRlbnQoKSkge1xyXG4gICAgICB0YXJnZXRzLnB1c2goJ3RocmVhdF9hbmFseXN0cycpO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICBpZiAodGhpcy5pc1RyaWdnZXJlZEJ5VnVsbmVyYWJpbGl0eSgpKSB7XHJcbiAgICAgIHRhcmdldHMucHVzaCgndnVsbmVyYWJpbGl0eV9tYW5hZ2VtZW50X3RlYW0nKTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgLy8gQWRkIHN0YWtlaG9sZGVyc1xyXG4gICAgdGhpcy5zdGFrZWhvbGRlcnMuZm9yRWFjaChzdGFrZWhvbGRlciA9PiB7XHJcbiAgICAgIGlmIChzdGFrZWhvbGRlci5yZXF1aXJlZCkge1xyXG4gICAgICAgIHRhcmdldHMucHVzaChzdGFrZWhvbGRlci5yb2xlKTtcclxuICAgICAgfVxyXG4gICAgfSk7XHJcbiAgICBcclxuICAgIHJldHVybiBbLi4ubmV3IFNldCh0YXJnZXRzKV07IC8vIFJlbW92ZSBkdXBsaWNhdGVzXHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgaW1wbGVtZW50YXRpb24gY29tcGxleGl0eSBsZXZlbFxyXG4gICAqL1xyXG4gIGdldENvbXBsZXhpdHlMZXZlbCgpOiAnc2ltcGxlJyB8ICdtb2RlcmF0ZScgfCAnY29tcGxleCcgfCAndmVyeV9jb21wbGV4JyB7XHJcbiAgICBsZXQgY29tcGxleGl0eSA9IDA7XHJcbiAgICBcclxuICAgIGlmICh0aGlzLmltcGxlbWVudGF0aW9uRWZmb3J0ID09PSAnaGlnaCcpIGNvbXBsZXhpdHkgKz0gMjtcclxuICAgIGVsc2UgaWYgKHRoaXMuaW1wbGVtZW50YXRpb25FZmZvcnQgPT09ICdtZWRpdW0nKSBjb21wbGV4aXR5ICs9IDE7XHJcbiAgICBcclxuICAgIGlmICh0aGlzLmhhc0RlcGVuZGVuY2llcygpKSBjb21wbGV4aXR5ICs9IDE7XHJcbiAgICBpZiAodGhpcy5oYXNIaWdoSW1wbGVtZW50YXRpb25SaXNrcygpKSBjb21wbGV4aXR5ICs9IDE7XHJcbiAgICBpZiAodGhpcy5yZWNvbW1lbmRlZEFjdGlvbnMubGVuZ3RoID4gNSkgY29tcGxleGl0eSArPSAxO1xyXG4gICAgaWYgKHRoaXMuc3Rha2Vob2xkZXJzLmxlbmd0aCA+IDMpIGNvbXBsZXhpdHkgKz0gMTtcclxuICAgIFxyXG4gICAgaWYgKGNvbXBsZXhpdHkgPD0gMSkgcmV0dXJuICdzaW1wbGUnO1xyXG4gICAgaWYgKGNvbXBsZXhpdHkgPD0gMykgcmV0dXJuICdtb2RlcmF0ZSc7XHJcbiAgICBpZiAoY29tcGxleGl0eSA8PSA1KSByZXR1cm4gJ2NvbXBsZXgnO1xyXG4gICAgcmV0dXJuICd2ZXJ5X2NvbXBsZXgnO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IHJlY29tbWVuZGF0aW9uIG1ldHJpY3NcclxuICAgKi9cclxuICBnZXRSZWNvbW1lbmRhdGlvbk1ldHJpY3MoKToge1xyXG4gICAgcmVjb21tZW5kYXRpb25JZDogc3RyaW5nO1xyXG4gICAgdHlwZTogc3RyaW5nO1xyXG4gICAgcHJpb3JpdHk6IHN0cmluZztcclxuICAgIGNvbmZpZGVuY2VTY29yZTogbnVtYmVyO1xyXG4gICAgcmlza1JlZHVjdGlvblBvdGVudGlhbDogbnVtYmVyO1xyXG4gICAgdmFsdWVTY29yZTogbnVtYmVyO1xyXG4gICAgaW1wbGVtZW50YXRpb25FZmZvcnQ6IHN0cmluZztcclxuICAgIGVzdGltYXRlZEhvdXJzOiBudW1iZXI7XHJcbiAgICBjb21wbGV4aXR5TGV2ZWw6IHN0cmluZztcclxuICAgIGhhc0FsdGVybmF0aXZlczogYm9vbGVhbjtcclxuICAgIGhhc0RlcGVuZGVuY2llczogYm9vbGVhbjtcclxuICAgIGFkZHJlc3Nlc0NvbXBsaWFuY2U6IGJvb2xlYW47XHJcbiAgICByZXF1aXJlc0FwcHJvdmFsOiBib29sZWFuO1xyXG4gICAgaW1wbGVtZW50YXRpb25VcmdlbmN5OiBudW1iZXI7XHJcbiAgfSB7XHJcbiAgICBjb25zdCBhcHByb3ZhbCA9IHRoaXMuZ2V0QXBwcm92YWxSZXF1aXJlbWVudHMoKTtcclxuICAgIFxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgcmVjb21tZW5kYXRpb25JZDogdGhpcy5yZWNvbW1lbmRhdGlvbklkLFxyXG4gICAgICB0eXBlOiB0aGlzLnJlY29tbWVuZGF0aW9uVHlwZSxcclxuICAgICAgcHJpb3JpdHk6IHRoaXMucHJpb3JpdHksXHJcbiAgICAgIGNvbmZpZGVuY2VTY29yZTogdGhpcy5jb25maWRlbmNlU2NvcmUsXHJcbiAgICAgIHJpc2tSZWR1Y3Rpb25Qb3RlbnRpYWw6IHRoaXMucmlza1JlZHVjdGlvblBvdGVudGlhbCxcclxuICAgICAgdmFsdWVTY29yZTogdGhpcy5nZXRWYWx1ZVNjb3JlKCksXHJcbiAgICAgIGltcGxlbWVudGF0aW9uRWZmb3J0OiB0aGlzLmltcGxlbWVudGF0aW9uRWZmb3J0LFxyXG4gICAgICBlc3RpbWF0ZWRIb3VyczogdGhpcy5lc3RpbWF0ZWRJbXBsZW1lbnRhdGlvblRpbWUsXHJcbiAgICAgIGNvbXBsZXhpdHlMZXZlbDogdGhpcy5nZXRDb21wbGV4aXR5TGV2ZWwoKSxcclxuICAgICAgaGFzQWx0ZXJuYXRpdmVzOiB0aGlzLmhhc0FsdGVybmF0aXZlcygpLFxyXG4gICAgICBoYXNEZXBlbmRlbmNpZXM6IHRoaXMuaGFzRGVwZW5kZW5jaWVzKCksXHJcbiAgICAgIGFkZHJlc3Nlc0NvbXBsaWFuY2U6IHRoaXMuYWRkcmVzc2VzQ29tcGxpYW5jZSgpLFxyXG4gICAgICByZXF1aXJlc0FwcHJvdmFsOiBhcHByb3ZhbC5yZXF1aXJlc0FwcHJvdmFsLFxyXG4gICAgICBpbXBsZW1lbnRhdGlvblVyZ2VuY3k6IHRoaXMuZ2V0SW1wbGVtZW50YXRpb25VcmdlbmN5KCksXHJcbiAgICB9O1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ29udmVydCB0byBpbnRlZ3JhdGlvbiBldmVudCBmb3IgZXh0ZXJuYWwgc3lzdGVtc1xyXG4gICAqL1xyXG4gIHRvSW50ZWdyYXRpb25FdmVudCgpOiB7XHJcbiAgICBldmVudFR5cGU6IHN0cmluZztcclxuICAgIHZlcnNpb246IHN0cmluZztcclxuICAgIHRpbWVzdGFtcDogc3RyaW5nO1xyXG4gICAgZGF0YToge1xyXG4gICAgICByZWNvbW1lbmRhdGlvbklkOiBzdHJpbmc7XHJcbiAgICAgIHJlY29tbWVuZGF0aW9uOiB7XHJcbiAgICAgICAgdHlwZTogc3RyaW5nO1xyXG4gICAgICAgIHRpdGxlOiBzdHJpbmc7XHJcbiAgICAgICAgZGVzY3JpcHRpb246IHN0cmluZztcclxuICAgICAgICBwcmlvcml0eTogc3RyaW5nO1xyXG4gICAgICAgIGNvbmZpZGVuY2VTY29yZTogbnVtYmVyO1xyXG4gICAgICAgIHJpc2tSZWR1Y3Rpb25Qb3RlbnRpYWw6IG51bWJlcjtcclxuICAgICAgICB2YWx1ZVNjb3JlOiBudW1iZXI7XHJcbiAgICAgIH07XHJcbiAgICAgIGltcGxlbWVudGF0aW9uOiB7XHJcbiAgICAgICAgZWZmb3J0OiBzdHJpbmc7XHJcbiAgICAgICAgZXN0aW1hdGVkSG91cnM6IG51bWJlcjtcclxuICAgICAgICBjb21wbGV4aXR5TGV2ZWw6IHN0cmluZztcclxuICAgICAgICB1cmdlbmN5RGF5czogbnVtYmVyO1xyXG4gICAgICAgIGhhc0RlcGVuZGVuY2llczogYm9vbGVhbjtcclxuICAgICAgICBoYXNSb2xsYmFja1BsYW46IGJvb2xlYW47XHJcbiAgICAgIH07XHJcbiAgICAgIHNvdXJjZToge1xyXG4gICAgICAgIHR5cGU6IHN0cmluZztcclxuICAgICAgICBpZGVudGlmaWVyOiBzdHJpbmc7XHJcbiAgICAgICAgdmVyc2lvbj86IHN0cmluZztcclxuICAgICAgfTtcclxuICAgICAgY29udGV4dDoge1xyXG4gICAgICAgIHRyaWdnZXJUeXBlOiBzdHJpbmc7XHJcbiAgICAgICAgdHJpZ2dlcklkczogc3RyaW5nW107XHJcbiAgICAgICAgcmVsYXRlZEFzc2V0cz86IHN0cmluZ1tdO1xyXG4gICAgICAgIGFmZmVjdGVkU3lzdGVtcz86IHN0cmluZ1tdO1xyXG4gICAgICB9O1xyXG4gICAgICBidXNpbmVzczoge1xyXG4gICAgICAgIHJpc2tNaXRpZ2F0aW9uOiBzdHJpbmc7XHJcbiAgICAgICAgYnVzaW5lc3NWYWx1ZTogc3RyaW5nO1xyXG4gICAgICAgIGNvc3RCZW5lZml0OiBzdHJpbmc7XHJcbiAgICAgICAgYWRkcmVzc2VzQ29tcGxpYW5jZTogYm9vbGVhbjtcclxuICAgICAgICBjb21wbGlhbmNlRnJhbWV3b3Jrcz86IHN0cmluZ1tdO1xyXG4gICAgICB9O1xyXG4gICAgICBhcHByb3ZhbDoge1xyXG4gICAgICAgIHJlcXVpcmVzQXBwcm92YWw6IGJvb2xlYW47XHJcbiAgICAgICAgYXBwcm92YWxMZXZlbDogc3RyaW5nO1xyXG4gICAgICAgIHJlcXVpcmVkQXBwcm92ZXJzOiBzdHJpbmdbXTtcclxuICAgICAgfTtcclxuICAgICAgYWN0aW9uczoge1xyXG4gICAgICAgIGNvdW50OiBudW1iZXI7XHJcbiAgICAgICAgY3JpdGljYWxBY3Rpb25zOiBudW1iZXI7XHJcbiAgICAgICAgYWN0aW9uVHlwZXM6IEFjdGlvblR5cGVbXTtcclxuICAgICAgfTtcclxuICAgIH07XHJcbiAgICBtZXRhZGF0YToge1xyXG4gICAgICBjb3JyZWxhdGlvbklkPzogc3RyaW5nO1xyXG4gICAgICBjYXVzYXRpb25JZD86IHN0cmluZztcclxuICAgICAgZG9tYWluOiBzdHJpbmc7XHJcbiAgICAgIGFnZ3JlZ2F0ZVR5cGU6IHN0cmluZztcclxuICAgICAgcHJvY2Vzc2luZ1N0YWdlOiBzdHJpbmc7XHJcbiAgICB9O1xyXG4gIH0ge1xyXG4gICAgY29uc3QgYXBwcm92YWwgPSB0aGlzLmdldEFwcHJvdmFsUmVxdWlyZW1lbnRzKCk7XHJcbiAgICBjb25zdCBjcml0aWNhbEFjdGlvbnMgPSB0aGlzLnJlY29tbWVuZGVkQWN0aW9ucy5maWx0ZXIoYSA9PiBhLnByaW9yaXR5ID09PSAnY3JpdGljYWwnKS5sZW5ndGg7XHJcbiAgICBjb25zdCBhY3Rpb25UeXBlcyA9IFsuLi5uZXcgU2V0KHRoaXMucmVjb21tZW5kZWRBY3Rpb25zLm1hcChhID0+IGEuYWN0aW9uVHlwZSkpXTtcclxuICAgIFxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgZXZlbnRUeXBlOiAnUmVjb21tZW5kYXRpb25HZW5lcmF0ZWQnLFxyXG4gICAgICB2ZXJzaW9uOiAnMS4wJyxcclxuICAgICAgdGltZXN0YW1wOiB0aGlzLm9jY3VycmVkT24udG9JU09TdHJpbmcoKSxcclxuICAgICAgZGF0YToge1xyXG4gICAgICAgIHJlY29tbWVuZGF0aW9uSWQ6IHRoaXMucmVjb21tZW5kYXRpb25JZCxcclxuICAgICAgICByZWNvbW1lbmRhdGlvbjoge1xyXG4gICAgICAgICAgdHlwZTogdGhpcy5yZWNvbW1lbmRhdGlvblR5cGUsXHJcbiAgICAgICAgICB0aXRsZTogdGhpcy50aXRsZSxcclxuICAgICAgICAgIGRlc2NyaXB0aW9uOiB0aGlzLmRlc2NyaXB0aW9uLFxyXG4gICAgICAgICAgcHJpb3JpdHk6IHRoaXMucHJpb3JpdHksXHJcbiAgICAgICAgICBjb25maWRlbmNlU2NvcmU6IHRoaXMuY29uZmlkZW5jZVNjb3JlLFxyXG4gICAgICAgICAgcmlza1JlZHVjdGlvblBvdGVudGlhbDogdGhpcy5yaXNrUmVkdWN0aW9uUG90ZW50aWFsLFxyXG4gICAgICAgICAgdmFsdWVTY29yZTogdGhpcy5nZXRWYWx1ZVNjb3JlKCksXHJcbiAgICAgICAgfSxcclxuICAgICAgICBpbXBsZW1lbnRhdGlvbjoge1xyXG4gICAgICAgICAgZWZmb3J0OiB0aGlzLmltcGxlbWVudGF0aW9uRWZmb3J0LFxyXG4gICAgICAgICAgZXN0aW1hdGVkSG91cnM6IHRoaXMuZXN0aW1hdGVkSW1wbGVtZW50YXRpb25UaW1lLFxyXG4gICAgICAgICAgY29tcGxleGl0eUxldmVsOiB0aGlzLmdldENvbXBsZXhpdHlMZXZlbCgpLFxyXG4gICAgICAgICAgdXJnZW5jeURheXM6IHRoaXMuZ2V0SW1wbGVtZW50YXRpb25VcmdlbmN5KCksXHJcbiAgICAgICAgICBoYXNEZXBlbmRlbmNpZXM6IHRoaXMuaGFzRGVwZW5kZW5jaWVzKCksXHJcbiAgICAgICAgICBoYXNSb2xsYmFja1BsYW46IHRoaXMuaGFzUm9sbGJhY2tQbGFuKCksXHJcbiAgICAgICAgfSxcclxuICAgICAgICBzb3VyY2U6IHtcclxuICAgICAgICAgIHR5cGU6IHRoaXMuc291cmNlLnR5cGUsXHJcbiAgICAgICAgICBpZGVudGlmaWVyOiB0aGlzLnNvdXJjZS5pZGVudGlmaWVyLFxyXG4gICAgICAgICAgdmVyc2lvbjogdGhpcy5zb3VyY2UudmVyc2lvbixcclxuICAgICAgICB9LFxyXG4gICAgICAgIGNvbnRleHQ6IHtcclxuICAgICAgICAgIHRyaWdnZXJUeXBlOiB0aGlzLmNvbnRleHQudHJpZ2dlclR5cGUsXHJcbiAgICAgICAgICB0cmlnZ2VySWRzOiB0aGlzLmNvbnRleHQudHJpZ2dlcklkcyxcclxuICAgICAgICAgIHJlbGF0ZWRBc3NldHM6IHRoaXMuY29udGV4dC5yZWxhdGVkQXNzZXRzLFxyXG4gICAgICAgICAgYWZmZWN0ZWRTeXN0ZW1zOiB0aGlzLmNvbnRleHQuYWZmZWN0ZWRTeXN0ZW1zLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgYnVzaW5lc3M6IHtcclxuICAgICAgICAgIHJpc2tNaXRpZ2F0aW9uOiB0aGlzLmJ1c2luZXNzSnVzdGlmaWNhdGlvbi5yaXNrTWl0aWdhdGlvbixcclxuICAgICAgICAgIGJ1c2luZXNzVmFsdWU6IHRoaXMuYnVzaW5lc3NKdXN0aWZpY2F0aW9uLmJ1c2luZXNzVmFsdWUsXHJcbiAgICAgICAgICBjb3N0QmVuZWZpdDogdGhpcy5idXNpbmVzc0p1c3RpZmljYXRpb24uY29zdEJlbmVmaXQsXHJcbiAgICAgICAgICBhZGRyZXNzZXNDb21wbGlhbmNlOiB0aGlzLmFkZHJlc3Nlc0NvbXBsaWFuY2UoKSxcclxuICAgICAgICAgIGNvbXBsaWFuY2VGcmFtZXdvcmtzOiB0aGlzLmNvbXBsaWFuY2VGcmFtZXdvcmtzLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgYXBwcm92YWw6IHtcclxuICAgICAgICAgIHJlcXVpcmVzQXBwcm92YWw6IGFwcHJvdmFsLnJlcXVpcmVzQXBwcm92YWwsXHJcbiAgICAgICAgICBhcHByb3ZhbExldmVsOiBhcHByb3ZhbC5hcHByb3ZhbExldmVsLFxyXG4gICAgICAgICAgcmVxdWlyZWRBcHByb3ZlcnM6IGFwcHJvdmFsLnJlcXVpcmVkQXBwcm92ZXJzLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgYWN0aW9uczoge1xyXG4gICAgICAgICAgY291bnQ6IHRoaXMucmVjb21tZW5kZWRBY3Rpb25zLmxlbmd0aCxcclxuICAgICAgICAgIGNyaXRpY2FsQWN0aW9ucyxcclxuICAgICAgICAgIGFjdGlvblR5cGVzLFxyXG4gICAgICAgIH0sXHJcbiAgICAgIH0sXHJcbiAgICAgIG1ldGFkYXRhOiB7XHJcbiAgICAgICAgY29ycmVsYXRpb25JZDogdGhpcy5jb3JyZWxhdGlvbklkLFxyXG4gICAgICAgIGNhdXNhdGlvbklkOiB0aGlzLmNhdXNhdGlvbklkLFxyXG4gICAgICAgIGRvbWFpbjogJ1NlY3VyaXR5JyxcclxuICAgICAgICBhZ2dyZWdhdGVUeXBlOiAnUmVjb21tZW5kYXRpb24nLFxyXG4gICAgICAgIHByb2Nlc3NpbmdTdGFnZTogJ2dlbmVyYXRpb24nLFxyXG4gICAgICB9LFxyXG4gICAgfTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENvbnZlcnQgdG8gSlNPTiByZXByZXNlbnRhdGlvblxyXG4gICAqL1xyXG4gIHB1YmxpYyB0b0pTT04oKTogUmVjb3JkPHN0cmluZywgYW55PiB7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICAuLi5zdXBlci50b0pTT04oKSxcclxuICAgICAgZXZlbnREYXRhOiB0aGlzLmV2ZW50RGF0YSxcclxuICAgICAgYW5hbHlzaXM6IHtcclxuICAgICAgICBpc0NyaXRpY2FsUHJpb3JpdHk6IHRoaXMuaXNDcml0aWNhbFByaW9yaXR5KCksXHJcbiAgICAgICAgaXNIaWdoUHJpb3JpdHlPckFib3ZlOiB0aGlzLmlzSGlnaFByaW9yaXR5T3JBYm92ZSgpLFxyXG4gICAgICAgIGhhc0hpZ2hDb25maWRlbmNlOiB0aGlzLmhhc0hpZ2hDb25maWRlbmNlKCksXHJcbiAgICAgICAgaGFzTG93Q29uZmlkZW5jZTogdGhpcy5oYXNMb3dDb25maWRlbmNlKCksXHJcbiAgICAgICAgaGFzSGlnaFJpc2tSZWR1Y3Rpb246IHRoaXMuaGFzSGlnaFJpc2tSZWR1Y3Rpb24oKSxcclxuICAgICAgICBpc0xvd0VmZm9ydDogdGhpcy5pc0xvd0VmZm9ydCgpLFxyXG4gICAgICAgIGlzSGlnaEVmZm9ydDogdGhpcy5pc0hpZ2hFZmZvcnQoKSxcclxuICAgICAgICBpc1F1aWNrSW1wbGVtZW50YXRpb246IHRoaXMuaXNRdWlja0ltcGxlbWVudGF0aW9uKCksXHJcbiAgICAgICAgaXNMb25nSW1wbGVtZW50YXRpb246IHRoaXMuaXNMb25nSW1wbGVtZW50YXRpb24oKSxcclxuICAgICAgICBoYXNDb3N0RXN0aW1hdGU6IHRoaXMuaGFzQ29zdEVzdGltYXRlKCksXHJcbiAgICAgICAgaXNIaWdoQ29zdDogdGhpcy5pc0hpZ2hDb3N0KCksXHJcbiAgICAgICAgYWRkcmVzc2VzQ29tcGxpYW5jZTogdGhpcy5hZGRyZXNzZXNDb21wbGlhbmNlKCksXHJcbiAgICAgICAgaGFzQWx0ZXJuYXRpdmVzOiB0aGlzLmhhc0FsdGVybmF0aXZlcygpLFxyXG4gICAgICAgIGhhc0RlcGVuZGVuY2llczogdGhpcy5oYXNEZXBlbmRlbmNpZXMoKSxcclxuICAgICAgICBoYXNIaWdoSW1wbGVtZW50YXRpb25SaXNrczogdGhpcy5oYXNIaWdoSW1wbGVtZW50YXRpb25SaXNrcygpLFxyXG4gICAgICAgIGhhc1JvbGxiYWNrUGxhbjogdGhpcy5oYXNSb2xsYmFja1BsYW4oKSxcclxuICAgICAgICBpc0FJR2VuZXJhdGVkOiB0aGlzLmlzQUlHZW5lcmF0ZWQoKSxcclxuICAgICAgICBpc1RyaWdnZXJlZEJ5VGhyZWF0OiB0aGlzLmlzVHJpZ2dlcmVkQnlUaHJlYXQoKSxcclxuICAgICAgICBpc1RyaWdnZXJlZEJ5VnVsbmVyYWJpbGl0eTogdGhpcy5pc1RyaWdnZXJlZEJ5VnVsbmVyYWJpbGl0eSgpLFxyXG4gICAgICAgIGlzVHJpZ2dlcmVkQnlJbmNpZGVudDogdGhpcy5pc1RyaWdnZXJlZEJ5SW5jaWRlbnQoKSxcclxuICAgICAgICB2YWx1ZVNjb3JlOiB0aGlzLmdldFZhbHVlU2NvcmUoKSxcclxuICAgICAgICBpbXBsZW1lbnRhdGlvblVyZ2VuY3k6IHRoaXMuZ2V0SW1wbGVtZW50YXRpb25VcmdlbmN5KCksXHJcbiAgICAgICAgY29tcGxleGl0eUxldmVsOiB0aGlzLmdldENvbXBsZXhpdHlMZXZlbCgpLFxyXG4gICAgICAgIGFwcHJvdmFsUmVxdWlyZW1lbnRzOiB0aGlzLmdldEFwcHJvdmFsUmVxdWlyZW1lbnRzKCksXHJcbiAgICAgICAgbm90aWZpY2F0aW9uVGFyZ2V0czogdGhpcy5nZXROb3RpZmljYXRpb25UYXJnZXRzKCksXHJcbiAgICAgICAgcmVjb21tZW5kYXRpb25NZXRyaWNzOiB0aGlzLmdldFJlY29tbWVuZGF0aW9uTWV0cmljcygpLFxyXG4gICAgICB9LFxyXG4gICAgfTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENyZWF0ZSBmcm9tIEpTT04gcmVwcmVzZW50YXRpb25cclxuICAgKi9cclxuICBzdGF0aWMgZnJvbUpTT04oanNvbjogUmVjb3JkPHN0cmluZywgYW55Pik6IFJlY29tbWVuZGF0aW9uR2VuZXJhdGVkRG9tYWluRXZlbnQge1xyXG4gICAgcmV0dXJuIG5ldyBSZWNvbW1lbmRhdGlvbkdlbmVyYXRlZERvbWFpbkV2ZW50KFxyXG4gICAgICBVbmlxdWVFbnRpdHlJZC5mcm9tU3RyaW5nKGpzb24uYWdncmVnYXRlSWQpLFxyXG4gICAgICBqc29uLmV2ZW50RGF0YSxcclxuICAgICAge1xyXG4gICAgICAgIGV2ZW50SWQ6IFVuaXF1ZUVudGl0eUlkLmZyb21TdHJpbmcoanNvbi5ldmVudElkKSxcclxuICAgICAgICBvY2N1cnJlZE9uOiBuZXcgRGF0ZShqc29uLm9jY3VycmVkT24pLFxyXG4gICAgICAgIGV2ZW50VmVyc2lvbjoganNvbi5ldmVudFZlcnNpb24sXHJcbiAgICAgICAgY29ycmVsYXRpb25JZDoganNvbi5jb3JyZWxhdGlvbklkLFxyXG4gICAgICAgIGNhdXNhdGlvbklkOiBqc29uLmNhdXNhdGlvbklkLFxyXG4gICAgICAgIG1ldGFkYXRhOiBqc29uLm1ldGFkYXRhLFxyXG4gICAgICB9XHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IGh1bWFuLXJlYWRhYmxlIGRlc2NyaXB0aW9uXHJcbiAgICovXHJcbiAgZ2V0RGVzY3JpcHRpb24oKTogc3RyaW5nIHtcclxuICAgIGNvbnN0IHByaW9yaXR5VGV4dCA9IHRoaXMucHJpb3JpdHkudG9VcHBlckNhc2UoKTtcclxuICAgIGNvbnN0IGNvbmZpZGVuY2VUZXh0ID0gdGhpcy5oYXNIaWdoQ29uZmlkZW5jZSgpID8gJ2hpZ2ggY29uZmlkZW5jZScgOiAnbW9kZXJhdGUgY29uZmlkZW5jZSc7XHJcbiAgICBjb25zdCBlZmZvcnRUZXh0ID0gdGhpcy5pbXBsZW1lbnRhdGlvbkVmZm9ydDtcclxuICAgIGNvbnN0IHRyaWdnZXJUZXh0ID0gdGhpcy5jb250ZXh0LnRyaWdnZXJUeXBlLnJlcGxhY2UoJ18nLCAnICcpO1xyXG4gICAgXHJcbiAgICByZXR1cm4gYCR7cHJpb3JpdHlUZXh0fSBwcmlvcml0eSAke3RoaXMucmVjb21tZW5kYXRpb25UeXBlfSByZWNvbW1lbmRhdGlvbiBcIiR7dGhpcy50aXRsZX1cIiBnZW5lcmF0ZWQgd2l0aCAke2NvbmZpZGVuY2VUZXh0fSAoJHtlZmZvcnRUZXh0fSBlZmZvcnQpIHRyaWdnZXJlZCBieSAke3RyaWdnZXJUZXh0fWA7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgZXZlbnQgc3VtbWFyeSBmb3IgbG9nZ2luZ1xyXG4gICAqL1xyXG4gIGdldFN1bW1hcnkoKToge1xyXG4gICAgZXZlbnRUeXBlOiBzdHJpbmc7XHJcbiAgICByZWNvbW1lbmRhdGlvbklkOiBzdHJpbmc7XHJcbiAgICB0eXBlOiBzdHJpbmc7XHJcbiAgICB0aXRsZTogc3RyaW5nO1xyXG4gICAgcHJpb3JpdHk6IHN0cmluZztcclxuICAgIGNvbmZpZGVuY2VTY29yZTogbnVtYmVyO1xyXG4gICAgcmlza1JlZHVjdGlvblBvdGVudGlhbDogbnVtYmVyO1xyXG4gICAgdmFsdWVTY29yZTogbnVtYmVyO1xyXG4gICAgaW1wbGVtZW50YXRpb25FZmZvcnQ6IHN0cmluZztcclxuICAgIHRyaWdnZXJUeXBlOiBzdHJpbmc7XHJcbiAgICByZXF1aXJlc0FwcHJvdmFsOiBib29sZWFuO1xyXG4gICAgdGltZXN0YW1wOiBzdHJpbmc7XHJcbiAgfSB7XHJcbiAgICBjb25zdCBhcHByb3ZhbCA9IHRoaXMuZ2V0QXBwcm92YWxSZXF1aXJlbWVudHMoKTtcclxuICAgIFxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgZXZlbnRUeXBlOiAnUmVjb21tZW5kYXRpb25HZW5lcmF0ZWQnLFxyXG4gICAgICByZWNvbW1lbmRhdGlvbklkOiB0aGlzLnJlY29tbWVuZGF0aW9uSWQsXHJcbiAgICAgIHR5cGU6IHRoaXMucmVjb21tZW5kYXRpb25UeXBlLFxyXG4gICAgICB0aXRsZTogdGhpcy50aXRsZSxcclxuICAgICAgcHJpb3JpdHk6IHRoaXMucHJpb3JpdHksXHJcbiAgICAgIGNvbmZpZGVuY2VTY29yZTogdGhpcy5jb25maWRlbmNlU2NvcmUsXHJcbiAgICAgIHJpc2tSZWR1Y3Rpb25Qb3RlbnRpYWw6IHRoaXMucmlza1JlZHVjdGlvblBvdGVudGlhbCxcclxuICAgICAgdmFsdWVTY29yZTogdGhpcy5nZXRWYWx1ZVNjb3JlKCksXHJcbiAgICAgIGltcGxlbWVudGF0aW9uRWZmb3J0OiB0aGlzLmltcGxlbWVudGF0aW9uRWZmb3J0LFxyXG4gICAgICB0cmlnZ2VyVHlwZTogdGhpcy5jb250ZXh0LnRyaWdnZXJUeXBlLFxyXG4gICAgICByZXF1aXJlc0FwcHJvdmFsOiBhcHByb3ZhbC5yZXF1aXJlc0FwcHJvdmFsLFxyXG4gICAgICB0aW1lc3RhbXA6IHRoaXMub2NjdXJyZWRPbi50b0lTT1N0cmluZygpLFxyXG4gICAgfTtcclxuICB9XHJcbn0iXSwidmVyc2lvbiI6M30=