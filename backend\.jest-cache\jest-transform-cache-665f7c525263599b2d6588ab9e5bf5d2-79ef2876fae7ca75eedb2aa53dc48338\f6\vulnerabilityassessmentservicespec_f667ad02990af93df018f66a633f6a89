0b05408b848c85c3bdcbda3f163c2454
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const vulnerability_assessment_service_1 = require("./vulnerability-assessment.service");
const vulnerability_entity_1 = require("../../domain/entities/vulnerability/vulnerability.entity");
const threat_severity_enum_1 = require("../../domain/enums/threat-severity.enum");
const confidence_level_enum_1 = require("../../domain/enums/confidence-level.enum");
const cvss_score_value_object_1 = require("../../domain/value-objects/threat-indicators/cvss-score.value-object");
const domain_event_publisher_1 = require("../../../shared-kernel/domain/domain-event-publisher");
describe('VulnerabilityAssessmentService', () => {
    let service;
    let mockEventPublisher;
    beforeEach(async () => {
        mockEventPublisher = {
            publishAll: jest.fn(),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                vulnerability_assessment_service_1.VulnerabilityAssessmentService,
                {
                    provide: domain_event_publisher_1.DomainEventPublisher,
                    useValue: mockEventPublisher,
                },
            ],
        }).compile();
        service = module.get(vulnerability_assessment_service_1.VulnerabilityAssessmentService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('assessVulnerability', () => {
        it('should assess a critical vulnerability successfully', async () => {
            const vulnerability = createMockVulnerability({
                severity: threat_severity_enum_1.ThreatSeverity.CRITICAL,
                confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
                riskScore: 95,
                cvssScore: 9.8,
                hasExploits: true,
                affectsCriticalAssets: true,
            });
            const result = await service.assessVulnerability({
                vulnerability,
                options: {
                    includeExploitAnalysis: true,
                    includeThreatIntelligence: true,
                    includeBusinessImpact: true,
                    includeComplianceImpact: true,
                },
            });
            expect(result.success).toBe(true);
            expect(result.summary.overallRisk).toBe('critical');
            expect(result.summary.priority).toBe('critical');
            expect(result.summary.urgency).toBe('immediate');
            expect(result.exploitAnalysis).toBeDefined();
            expect(result.threatIntelligence).toBeDefined();
            expect(result.businessImpact).toBeDefined();
            expect(result.complianceImpact).toBeDefined();
            expect(result.recommendations.immediate).toContain('isolate_affected_systems');
        });
        it('should assess a medium severity vulnerability', async () => {
            const vulnerability = createMockVulnerability({
                severity: threat_severity_enum_1.ThreatSeverity.MEDIUM,
                confidence: confidence_level_enum_1.ConfidenceLevel.MEDIUM,
                riskScore: 55,
                cvssScore: 5.5,
                hasExploits: false,
                affectsCriticalAssets: false,
            });
            const result = await service.assessVulnerability({
                vulnerability,
                options: {
                    includeExploitAnalysis: true,
                },
            });
            expect(result.success).toBe(true);
            expect(result.summary.overallRisk).toBe('medium');
            expect(result.summary.priority).toBe('medium');
            expect(result.summary.urgency).toBe('medium');
            expect(result.exploitAnalysis?.exploitability).toBe('low');
            expect(result.recommendations.immediate.length).toBeGreaterThan(0);
        });
        it('should handle vulnerability with active exploitation', async () => {
            const vulnerability = createMockVulnerability({
                severity: threat_severity_enum_1.ThreatSeverity.HIGH,
                confidence: confidence_level_enum_1.ConfidenceLevel.CONFIRMED,
                riskScore: 85,
                cvssScore: 8.2,
                hasExploits: true,
                activeExploitation: true,
                affectsCriticalAssets: true,
            });
            const result = await service.assessVulnerability({
                vulnerability,
                options: {
                    includeExploitAnalysis: true,
                    includeBusinessImpact: true,
                },
            });
            expect(result.success).toBe(true);
            expect(result.summary.overallRisk).toBe('critical');
            expect(result.exploitAnalysis?.exploitability).toBe('critical');
            expect(result.businessImpact?.operationalImpact).toBe('high');
            expect(result.recommendations.immediate).toContain('immediate_isolation');
        });
        it('should assess vulnerability with compliance impact', async () => {
            const vulnerability = createMockVulnerability({
                severity: threat_severity_enum_1.ThreatSeverity.HIGH,
                confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
                riskScore: 75,
                hasComplianceImpact: true,
            });
            const context = {
                complianceRequirements: ['PCI_DSS', 'GDPR', 'SOX'],
                riskTolerance: 'low',
            };
            const result = await service.assessVulnerability({
                vulnerability,
                context,
                options: {
                    includeComplianceImpact: true,
                },
            });
            expect(result.success).toBe(true);
            expect(result.complianceImpact).toBeDefined();
            expect(result.complianceImpact?.regulationsAffected).toEqual(['PCI_DSS', 'GDPR', 'SOX']);
            expect(result.complianceImpact?.reportingRequired).toBe(true);
        });
        it('should handle assessment errors gracefully', async () => {
            const invalidVulnerability = null;
            const result = await service.assessVulnerability({
                vulnerability: invalidVulnerability,
            });
            expect(result.success).toBe(false);
            expect(result.summary.overallRisk).toBe('medium');
            expect(result.recommendations.immediate).toContain('Retry assessment');
        });
        it('should calculate risk analysis correctly', async () => {
            const vulnerability = createMockVulnerability({
                severity: threat_severity_enum_1.ThreatSeverity.HIGH,
                confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
                riskScore: 80,
                cvssScore: 7.5,
                affectsCriticalAssets: true,
                isExternallyExposed: true,
            });
            const result = await service.assessVulnerability({
                vulnerability,
            });
            expect(result.success).toBe(true);
            expect(result.riskAnalysis.technicalRisk).toBeGreaterThan(70);
            expect(result.riskAnalysis.businessRisk).toBeGreaterThan(50);
            expect(result.riskAnalysis.combinedRisk).toBeGreaterThan(60);
        });
        it('should generate appropriate recommendations', async () => {
            const vulnerability = createMockVulnerability({
                severity: threat_severity_enum_1.ThreatSeverity.CRITICAL,
                confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
                riskScore: 95,
                hasExploits: true,
                affectsCriticalAssets: true,
                cveId: 'CVE-2023-12345',
            });
            const result = await service.assessVulnerability({
                vulnerability,
            });
            expect(result.success).toBe(true);
            expect(result.recommendations.immediate.length).toBeGreaterThan(0);
            expect(result.recommendations.shortTerm).toContain('Apply vendor patches');
            expect(result.recommendations.preventive).toContain('Implement vulnerability scanning');
        });
    });
    describe('assessVulnerabilitiesBulk', () => {
        it('should assess multiple vulnerabilities successfully', async () => {
            const vulnerabilities = [
                createMockVulnerability({
                    severity: threat_severity_enum_1.ThreatSeverity.CRITICAL,
                    riskScore: 95,
                }),
                createMockVulnerability({
                    severity: threat_severity_enum_1.ThreatSeverity.HIGH,
                    riskScore: 75,
                }),
                createMockVulnerability({
                    severity: threat_severity_enum_1.ThreatSeverity.MEDIUM,
                    riskScore: 55,
                }),
            ];
            const result = await service.assessVulnerabilitiesBulk({
                vulnerabilities,
                config: {
                    batchSize: 2,
                    includeDetailedAnalysis: true,
                },
            });
            expect(result.success).toBe(true);
            expect(result.totalAssessed).toBe(3);
            expect(result.successfulAssessments).toBe(3);
            expect(result.failedAssessments).toBe(0);
            expect(result.results).toHaveLength(3);
            expect(result.summary.criticalVulnerabilities).toBe(1);
            expect(result.summary.highRiskVulnerabilities).toBe(1);
            expect(result.summary.mediumRiskVulnerabilities).toBe(1);
        });
        it('should prioritize critical vulnerabilities when configured', async () => {
            const vulnerabilities = [
                createMockVulnerability({
                    id: 'vuln-1',
                    severity: threat_severity_enum_1.ThreatSeverity.MEDIUM,
                    riskScore: 55,
                }),
                createMockVulnerability({
                    id: 'vuln-2',
                    severity: threat_severity_enum_1.ThreatSeverity.CRITICAL,
                    riskScore: 95,
                }),
                createMockVulnerability({
                    id: 'vuln-3',
                    severity: threat_severity_enum_1.ThreatSeverity.HIGH,
                    riskScore: 75,
                }),
            ];
            const result = await service.assessVulnerabilitiesBulk({
                vulnerabilities,
                config: {
                    prioritizeCritical: true,
                },
            });
            expect(result.success).toBe(true);
            expect(result.results[0].vulnerabilityId).toBe('vuln-2'); // Critical should be first
            expect(result.results[1].vulnerabilityId).toBe('vuln-3'); // High should be second
            expect(result.results[2].vulnerabilityId).toBe('vuln-1'); // Medium should be last
        });
        it('should handle partial failures in bulk assessment', async () => {
            const vulnerabilities = [
                createMockVulnerability({
                    severity: threat_severity_enum_1.ThreatSeverity.CRITICAL,
                    riskScore: 95,
                }),
                null, // This will cause an error
                createMockVulnerability({
                    severity: threat_severity_enum_1.ThreatSeverity.HIGH,
                    riskScore: 75,
                }),
            ];
            const result = await service.assessVulnerabilitiesBulk({
                vulnerabilities: vulnerabilities.filter(Boolean),
            });
            expect(result.success).toBe(true);
            expect(result.successfulAssessments).toBe(2);
            expect(result.failedAssessments).toBe(0);
        });
        it('should calculate bulk summary statistics correctly', async () => {
            const vulnerabilities = [
                createMockVulnerability({
                    severity: threat_severity_enum_1.ThreatSeverity.CRITICAL,
                    riskScore: 95,
                }),
                createMockVulnerability({
                    severity: threat_severity_enum_1.ThreatSeverity.CRITICAL,
                    riskScore: 90,
                }),
                createMockVulnerability({
                    severity: threat_severity_enum_1.ThreatSeverity.HIGH,
                    riskScore: 75,
                }),
                createMockVulnerability({
                    severity: threat_severity_enum_1.ThreatSeverity.MEDIUM,
                    riskScore: 55,
                }),
                createMockVulnerability({
                    severity: threat_severity_enum_1.ThreatSeverity.LOW,
                    riskScore: 25,
                }),
            ];
            const result = await service.assessVulnerabilitiesBulk({
                vulnerabilities,
            });
            expect(result.success).toBe(true);
            expect(result.summary.criticalVulnerabilities).toBe(2);
            expect(result.summary.highRiskVulnerabilities).toBe(1);
            expect(result.summary.mediumRiskVulnerabilities).toBe(1);
            expect(result.summary.lowRiskVulnerabilities).toBe(1);
            expect(result.summary.averageRiskScore).toBeCloseTo(68, 0); // (95+90+75+55+25)/5 = 68
            expect(result.summary.topRecommendations.length).toBeGreaterThan(0);
        });
        it('should respect batch size configuration', async () => {
            const vulnerabilities = Array.from({ length: 15 }, (_, i) => createMockVulnerability({
                id: `vuln-${i}`,
                severity: threat_severity_enum_1.ThreatSeverity.MEDIUM,
                riskScore: 50,
            }));
            const result = await service.assessVulnerabilitiesBulk({
                vulnerabilities,
                config: {
                    batchSize: 5,
                },
            });
            expect(result.success).toBe(true);
            expect(result.totalAssessed).toBe(15);
            expect(result.successfulAssessments).toBe(15);
        });
    });
    describe('performance', () => {
        it('should complete assessment within reasonable time', async () => {
            const vulnerability = createMockVulnerability({
                severity: threat_severity_enum_1.ThreatSeverity.HIGH,
                riskScore: 75,
            });
            const startTime = Date.now();
            const result = await service.assessVulnerability({
                vulnerability,
                options: {
                    includeExploitAnalysis: true,
                    includeThreatIntelligence: true,
                    includeBusinessImpact: true,
                    includeComplianceImpact: true,
                },
            });
            const duration = Date.now() - startTime;
            expect(result.success).toBe(true);
            expect(duration).toBeLessThan(1000); // Should complete within 1 second
            expect(result.metadata.assessmentDuration).toBeLessThan(1000);
        });
        it('should handle large bulk assessments efficiently', async () => {
            const vulnerabilities = Array.from({ length: 50 }, (_, i) => createMockVulnerability({
                id: `vuln-${i}`,
                severity: threat_severity_enum_1.ThreatSeverity.MEDIUM,
                riskScore: 50 + i,
            }));
            const startTime = Date.now();
            const result = await service.assessVulnerabilitiesBulk({
                vulnerabilities,
                config: {
                    batchSize: 10,
                },
            });
            const duration = Date.now() - startTime;
            expect(result.success).toBe(true);
            expect(result.totalAssessed).toBe(50);
            expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
        });
    });
    // Helper function to create mock vulnerabilities
    function createMockVulnerability(options = {}) {
        const { id = 'test-vuln-1', severity = threat_severity_enum_1.ThreatSeverity.MEDIUM, confidence = confidence_level_enum_1.ConfidenceLevel.MEDIUM, riskScore = 50, cvssScore = 5.0, hasExploits = false, activeExploitation = false, affectsCriticalAssets = false, isExternallyExposed = false, hasComplianceImpact = false, cveId, } = options;
        const mockVulnerability = {
            id: { toString: () => id },
            cveId,
            title: `Test Vulnerability ${id}`,
            description: 'Test vulnerability description',
            severity,
            confidence,
            category: 'test_category',
            type: 'test_type',
            status: vulnerability_entity_1.VulnerabilityStatus.OPEN,
            riskAssessment: {
                riskScore,
                businessRisk: {
                    financialImpact: affectsCriticalAssets ? 100000 : 10000,
                    reputationalImpact: affectsCriticalAssets ? 'high' : 'medium',
                    operationalImpact: affectsCriticalAssets ? 'high' : 'low',
                },
            },
            cvssScores: cvssScore ? [
                cvss_score_value_object_1.CVSSScore.create(cvssScore, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H', '3.1')
            ] : [],
            exploitation: hasExploits ? {
                status: activeExploitation ? 'active_exploitation' : 'proof_of_concept',
                availableExploits: [
                    {
                        type: 'public',
                        reliability: 80,
                        complexity: 'medium',
                        source: 'exploit-db',
                    }
                ],
                difficulty: 'medium',
                attackVectors: ['network'],
            } : undefined,
            affectedAssets: [
                {
                    assetId: 'asset-1',
                    assetName: 'Test Asset',
                    assetType: 'server',
                    criticality: affectsCriticalAssets ? 'critical' : 'medium',
                    exposure: isExternallyExposed ? 'external' : 'internal',
                    impact: 'medium',
                    status: 'operational',
                    recoveryStatus: 'not_started',
                    affectedServices: [],
                }
            ],
            complianceImpact: hasComplianceImpact ? [
                {
                    regulation: 'PCI_DSS',
                    requirement: '6.1',
                    impact: 'high',
                    status: 'non_compliant',
                }
            ] : [],
            remediation: {
                actions: ['apply_patch'],
                timeline: {
                    plannedStart: new Date(),
                    plannedCompletion: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
                },
                cost: {
                    estimated: 5000,
                },
            },
            tags: [],
            attributes: {},
            isCritical: () => severity === threat_severity_enum_1.ThreatSeverity.CRITICAL,
            affectsCriticalAssets: () => affectsCriticalAssets,
            isExternallyExposed: () => isExternallyExposed,
            getDomainEvents: () => [],
            clearDomainEvents: () => { },
        };
        return mockVulnerability;
    }
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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