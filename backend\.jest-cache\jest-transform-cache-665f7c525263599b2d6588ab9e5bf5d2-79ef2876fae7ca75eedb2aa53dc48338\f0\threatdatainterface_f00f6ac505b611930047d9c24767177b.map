{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\data\\threat-data.interface.ts", "mappings": ";;;AAgHA;;GAEG;AACH,IAAY,uBAaX;AAbD,WAAY,uBAAuB;IACjC,iCAAiC;IACjC,kCAAO,CAAA;IACP,8BAA8B;IAC9B,kCAAO,CAAA;IACP,6BAA6B;IAC7B,kCAAO,CAAA;IACP,kCAAkC;IAClC,kCAAO,CAAA;IACP,wBAAwB;IACxB,kCAAO,CAAA;IACP,uBAAuB;IACvB,kCAAO,CAAA;AACT,CAAC,EAbW,uBAAuB,uCAAvB,uBAAuB,QAalC;AAED;;GAEG;AACH,IAAY,cAqBX;AArBD,WAAY,cAAc;IACxB,qCAAmB,CAAA;IACnB,uCAAqB,CAAA;IACrB,2CAAyB,CAAA;IACzB,6BAAW,CAAA;IACX,mCAAiB,CAAA;IACjB,qCAAmB,CAAA;IACnB,iDAA+B,CAAA;IAC/B,iCAAe,CAAA;IACf,+BAAa,CAAA;IACb,+BAAa,CAAA;IACb,mDAAiC,CAAA;IACjC,+CAA6B,CAAA;IAC7B,2DAAyC,CAAA;IACzC,+BAAa,CAAA;IACb,6CAA2B,CAAA;IAC3B,qDAAmC,CAAA;IACnC,2CAAyB,CAAA;IACzB,6CAA2B,CAAA;IAC3B,+CAA6B,CAAA;IAC7B,iCAAe,CAAA;AACjB,CAAC,EArBW,cAAc,8BAAd,cAAc,QAqBzB;AAED;;GAEG;AACH,IAAY,UAWX;AAXD,WAAY,UAAU;IACpB,qCAAuB,CAAA;IACvB,mCAAqB,CAAA;IACrB,6BAAe,CAAA;IACf,iCAAmB,CAAA;IACnB,2BAAa,CAAA;IACb,qCAAuB,CAAA;IACvB,6CAA+B,CAAA;IAC/B,mCAAqB,CAAA;IACrB,+BAAiB,CAAA;IACjB,6BAAe,CAAA;AACjB,CAAC,EAXW,UAAU,0BAAV,UAAU,QAWrB;AAED;;GAEG;AACH,IAAY,YASX;AATD,WAAY,YAAY;IACtB,iCAAiB,CAAA;IACjB,qCAAqB,CAAA;IACrB,mCAAmB,CAAA;IACnB,mCAAmB,CAAA;IACnB,6CAA6B,CAAA;IAC7B,uCAAuB,CAAA;IACvB,uCAAuB,CAAA;IACvB,mCAAmB,CAAA;AACrB,CAAC,EATW,YAAY,4BAAZ,YAAY,QASvB;AAkCD;;GAEG;AACH,IAAY,mBAeX;AAfD,WAAY,mBAAmB;IAC7B,gDAAyB,CAAA;IACzB,wCAAiB,CAAA;IACjB,kCAAW,CAAA;IACX,sCAAe,CAAA;IACf,8CAAuB,CAAA;IACvB,oDAA6B,CAAA;IAC7B,sCAAe,CAAA;IACf,gDAAyB,CAAA;IACzB,kDAA2B,CAAA;IAC3B,kCAAW,CAAA;IACX,kCAAW,CAAA;IACX,8CAAuB,CAAA;IACvB,gDAAyB,CAAA;IACzB,sCAAe,CAAA;AACjB,CAAC,EAfW,mBAAmB,mCAAnB,mBAAmB,QAe9B;AAED;;GAEG;AACH,IAAY,qBAMX;AAND,WAAY,qBAAqB;IAC/B,0CAAiB,CAAA;IACjB,8CAAqB,CAAA;IACrB,oDAA2B,CAAA;IAC3B,4CAAmB,CAAA;IACnB,sDAA6B,CAAA;AAC/B,CAAC,EANW,qBAAqB,qCAArB,qBAAqB,QAMhC;AAsDD;;GAEG;AACH,IAAY,eAQX;AARD,WAAY,eAAe;IACzB,gDAA6B,CAAA;IAC7B,oDAAiC,CAAA;IACjC,4CAAyB,CAAA;IACzB,sCAAmB,CAAA;IACnB,0CAAuB,CAAA;IACvB,kDAA+B,CAAA;IAC/B,sCAAmB,CAAA;AACrB,CAAC,EARW,eAAe,+BAAf,eAAe,QAQ1B;AAED;;GAEG;AACH,IAAY,qBAQX;AARD,WAAY,qBAAqB;IAC/B,gDAAuB,CAAA;IACvB,gDAAuB,CAAA;IACvB,8CAAqB,CAAA;IACrB,8CAAqB,CAAA;IACrB,4CAAmB,CAAA;IACnB,gDAAuB,CAAA;IACvB,4CAAmB,CAAA;AACrB,CAAC,EARW,qBAAqB,qCAArB,qBAAqB,QAQhC;AAED;;GAEG;AACH,IAAY,yBAOX;AAPD,WAAY,yBAAyB;IACnC,gDAAmB,CAAA;IACnB,0DAA6B,CAAA;IAC7B,kDAAqB,CAAA;IACrB,8CAAiB,CAAA;IACjB,oDAAuB,CAAA;IACvB,oDAAuB,CAAA;AACzB,CAAC,EAPW,yBAAyB,yCAAzB,yBAAyB,QAOpC;AAyBD;;GAEG;AACH,IAAY,WAgBX;AAhBD,WAAY,WAAW;IACrB,gCAAiB,CAAA;IACjB,8BAAe,CAAA;IACf,4BAAa,CAAA;IACb,wCAAyB,CAAA;IACzB,kCAAmB,CAAA;IACnB,gCAAiB,CAAA;IACjB,kCAAmB,CAAA;IACnB,oCAAqB,CAAA;IACrB,sCAAuB,CAAA;IACvB,gCAAiB,CAAA;IACjB,0BAAW,CAAA;IACX,gCAAiB,CAAA;IACjB,kCAAmB,CAAA;IACnB,0CAA2B,CAAA;IAC3B,8BAAe,CAAA;AACjB,CAAC,EAhBW,WAAW,2BAAX,WAAW,QAgBtB;AA+BD;;GAEG;AACH,IAAY,oBAQX;AARD,WAAY,oBAAoB;IAC9B,yDAAiC,CAAA;IACjC,uDAA+B,CAAA;IAC/B,6CAAqB,CAAA;IACrB,qDAA6B,CAAA;IAC7B,qDAA6B,CAAA;IAC7B,2DAAmC,CAAA;IACnC,iEAAyC,CAAA;AAC3C,CAAC,EARW,oBAAoB,oCAApB,oBAAoB,QAQ/B;AAqFD;;GAEG;AACH,IAAY,oBAOX;AAPD,WAAY,oBAAoB;IAC9B,iDAAyB,CAAA;IACzB,+CAAuB,CAAA;IACvB,iDAAyB,CAAA;IACzB,+CAAuB,CAAA;IACvB,6CAAqB,CAAA;IACrB,qDAA6B,CAAA;AAC/B,CAAC,EAPW,oBAAoB,oCAApB,oBAAoB,QAO/B;AA+BD;;GAEG;AACH,IAAY,uBAQX;AARD,WAAY,uBAAuB;IACjC,wCAAa,CAAA;IACb,0CAAe,CAAA;IACf,0CAAe,CAAA;IACf,gDAAqB,CAAA;IACrB,0CAAe,CAAA;IACf,sCAAW,CAAA;IACX,4CAAiB,CAAA;AACnB,CAAC,EARW,uBAAuB,uCAAvB,uBAAuB,QAQlC;AAyBD;;GAEG;AACH,IAAY,UAKX;AALD,WAAY,UAAU;IACpB,yBAAW,CAAA;IACX,6BAAe,CAAA;IACf,6BAAe,CAAA;IACf,6BAAe,CAAA;AACjB,CAAC,EALW,UAAU,0BAAV,UAAU,QAKrB;AAED;;GAEG;AACH,IAAY,mBAOX;AAPD,WAAY,mBAAmB;IAC7B,sCAAe,CAAA;IACf,0CAAmB,CAAA;IACnB,wCAAiB,CAAA;IACjB,sCAAe,CAAA;IACf,sCAAe,CAAA;IACf,kDAA2B,CAAA;AAC7B,CAAC,EAPW,mBAAmB,mCAAnB,mBAAmB,QAO9B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\data\\threat-data.interface.ts"], "sourcesContent": ["import { ThreatSeverity } from '../../enums/threat-severity.enum';\r\nimport { ConfidenceLevel } from '../../enums/confidence-level.enum';\r\n\r\n/**\r\n * Threat Data Interface for External Integration\r\n * \r\n * This interface defines the structure for threat intelligence data received from\r\n * external threat intelligence feeds, security vendors, government agencies,\r\n * and threat research organizations. It supports standardized threat data exchange,\r\n * serialization, and versioning for threat intelligence integration.\r\n */\r\nexport interface ThreatData {\r\n  /** Data format version for backward compatibility */\r\n  readonly version: string;\r\n  \r\n  /** Unique identifier for the threat from external source */\r\n  readonly externalId: string;\r\n  \r\n  /** Threat intelligence source identifier */\r\n  readonly sourceId: string;\r\n  \r\n  /** Source organization or vendor */\r\n  readonly sourceOrganization: string;\r\n  \r\n  /** Source reliability rating */\r\n  readonly sourceReliability: ThreatSourceReliability;\r\n  \r\n  /** Threat name or title */\r\n  readonly name: string;\r\n  \r\n  /** Detailed threat description */\r\n  readonly description: string;\r\n  \r\n  /** Threat severity level */\r\n  readonly severity: ThreatSeverity;\r\n  \r\n  /** Threat category */\r\n  readonly category: ThreatCategory;\r\n  \r\n  /** Threat subcategory */\r\n  readonly subcategory?: string;\r\n  \r\n  /** Threat type classification */\r\n  readonly type: ThreatType;\r\n  \r\n  /** Confidence level in threat assessment */\r\n  readonly confidence: ConfidenceLevel;\r\n  \r\n  /** Threat status */\r\n  readonly status: ThreatStatus;\r\n  \r\n  /** First observed timestamp */\r\n  readonly firstSeen: string;\r\n  \r\n  /** Last observed timestamp */\r\n  readonly lastSeen: string;\r\n  \r\n  /** Threat expiration date if applicable */\r\n  readonly expiresAt?: string;\r\n  \r\n  /** Indicators of Compromise */\r\n  readonly indicators: ThreatIndicatorData[];\r\n  \r\n  /** CVSS scores if applicable */\r\n  readonly cvssScores?: ThreatCVSSData[];\r\n  \r\n  /** Threat actor attribution */\r\n  readonly attribution?: ThreatAttributionData;\r\n  \r\n  /** Malware family information */\r\n  readonly malwareFamily?: ThreatMalwareFamilyData;\r\n  \r\n  /** Attack techniques and tactics */\r\n  readonly attackTechniques: ThreatAttackTechniqueData[];\r\n  \r\n  /** Targeted industries or sectors */\r\n  readonly targetedSectors?: string[];\r\n  \r\n  /** Targeted countries or regions */\r\n  readonly targetedRegions?: string[];\r\n  \r\n  /** Affected platforms and technologies */\r\n  readonly affectedPlatforms?: string[];\r\n  \r\n  /** Kill chain phases */\r\n  readonly killChainPhases?: ThreatKillChainPhase[];\r\n  \r\n  /** Threat timeline and campaign information */\r\n  readonly timeline?: ThreatTimelineData;\r\n  \r\n  /** Mitigation recommendations */\r\n  readonly mitigations?: ThreatMitigationData[];\r\n  \r\n  /** Detection rules and signatures */\r\n  readonly detectionRules?: ThreatDetectionRuleData[];\r\n  \r\n  /** Related threats and campaigns */\r\n  readonly relatedThreats?: string[];\r\n  \r\n  /** Threat intelligence sharing information */\r\n  readonly sharingInfo: ThreatSharingInfo;\r\n  \r\n  /** Custom attributes from source */\r\n  readonly attributes?: Record<string, any>;\r\n  \r\n  /** Threat tags for categorization */\r\n  readonly tags?: string[];\r\n  \r\n  /** Data quality metrics */\r\n  readonly qualityMetrics?: ThreatDataQualityMetrics;\r\n}\r\n\r\n/**\r\n * Threat Source Reliability Levels\r\n */\r\nexport enum ThreatSourceReliability {\r\n  /** Completely reliable source */\r\n  A = 'A',\r\n  /** Usually reliable source */\r\n  B = 'B',\r\n  /** Fairly reliable source */\r\n  C = 'C',\r\n  /** Not usually reliable source */\r\n  D = 'D',\r\n  /** Unreliable source */\r\n  E = 'E',\r\n  /** Cannot be judged */\r\n  F = 'F',\r\n}\r\n\r\n/**\r\n * Threat Categories\r\n */\r\nexport enum ThreatCategory {\r\n  MALWARE = 'malware',\r\n  PHISHING = 'phishing',\r\n  RANSOMWARE = 'ransomware',\r\n  APT = 'apt',\r\n  BOTNET = 'botnet',\r\n  EXPLOIT = 'exploit',\r\n  VULNERABILITY = 'vulnerability',\r\n  FRAUD = 'fraud',\r\n  SPAM = 'spam',\r\n  SCAM = 'scam',\r\n  INSIDER_THREAT = 'insider_threat',\r\n  SUPPLY_CHAIN = 'supply_chain',\r\n  SOCIAL_ENGINEERING = 'social_engineering',\r\n  DDOS = 'ddos',\r\n  DATA_BREACH = 'data_breach',\r\n  CYBER_ESPIONAGE = 'cyber_espionage',\r\n  HACKTIVISM = 'hacktivism',\r\n  CYBER_CRIME = 'cyber_crime',\r\n  NATION_STATE = 'nation_state',\r\n  OTHER = 'other',\r\n}\r\n\r\n/**\r\n * Threat Types\r\n */\r\nexport enum ThreatType {\r\n  INDICATOR = 'indicator',\r\n  CAMPAIGN = 'campaign',\r\n  ACTOR = 'actor',\r\n  MALWARE = 'malware',\r\n  TOOL = 'tool',\r\n  TECHNIQUE = 'technique',\r\n  VULNERABILITY = 'vulnerability',\r\n  INCIDENT = 'incident',\r\n  REPORT = 'report',\r\n  OTHER = 'other',\r\n}\r\n\r\n/**\r\n * Threat Status\r\n */\r\nexport enum ThreatStatus {\r\n  ACTIVE = 'active',\r\n  INACTIVE = 'inactive',\r\n  EXPIRED = 'expired',\r\n  REVOKED = 'revoked',\r\n  UNDER_REVIEW = 'under_review',\r\n  CONFIRMED = 'confirmed',\r\n  SUSPECTED = 'suspected',\r\n  UNKNOWN = 'unknown',\r\n}\r\n\r\n/**\r\n * Threat Indicator Data\r\n */\r\nexport interface ThreatIndicatorData {\r\n  /** Indicator type */\r\n  readonly type: ThreatIndicatorType;\r\n  \r\n  /** Indicator value */\r\n  readonly value: string;\r\n  \r\n  /** Indicator description */\r\n  readonly description?: string;\r\n  \r\n  /** Indicator confidence level */\r\n  readonly confidence: ConfidenceLevel;\r\n  \r\n  /** First seen timestamp */\r\n  readonly firstSeen: string;\r\n  \r\n  /** Last seen timestamp */\r\n  readonly lastSeen: string;\r\n  \r\n  /** Indicator status */\r\n  readonly status: ThreatIndicatorStatus;\r\n  \r\n  /** Indicator tags */\r\n  readonly tags?: string[];\r\n  \r\n  /** Context information */\r\n  readonly context?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Threat Indicator Types\r\n */\r\nexport enum ThreatIndicatorType {\r\n  IP_ADDRESS = 'ip_address',\r\n  DOMAIN = 'domain',\r\n  URL = 'url',\r\n  EMAIL = 'email',\r\n  FILE_HASH = 'file_hash',\r\n  REGISTRY_KEY = 'registry_key',\r\n  MUTEX = 'mutex',\r\n  USER_AGENT = 'user_agent',\r\n  CERTIFICATE = 'certificate',\r\n  ASN = 'asn',\r\n  CVE = 'cve',\r\n  YARA_RULE = 'yara_rule',\r\n  SIGMA_RULE = 'sigma_rule',\r\n  OTHER = 'other',\r\n}\r\n\r\n/**\r\n * Threat Indicator Status\r\n */\r\nexport enum ThreatIndicatorStatus {\r\n  ACTIVE = 'active',\r\n  INACTIVE = 'inactive',\r\n  WHITELISTED = 'whitelisted',\r\n  EXPIRED = 'expired',\r\n  UNDER_REVIEW = 'under_review',\r\n}\r\n\r\n/**\r\n * Threat CVSS Data\r\n */\r\nexport interface ThreatCVSSData {\r\n  /** CVSS version */\r\n  readonly version: string;\r\n  \r\n  /** CVSS score */\r\n  readonly score: number;\r\n  \r\n  /** CVSS vector string */\r\n  readonly vector: string;\r\n  \r\n  /** Severity rating */\r\n  readonly severity: string;\r\n  \r\n  /** Exploitability score */\r\n  readonly exploitabilityScore?: number;\r\n  \r\n  /** Impact score */\r\n  readonly impactScore?: number;\r\n}\r\n\r\n/**\r\n * Threat Attribution Data\r\n */\r\nexport interface ThreatAttributionData {\r\n  /** Threat actor name */\r\n  readonly actorName?: string;\r\n  \r\n  /** Actor aliases */\r\n  readonly aliases?: string[];\r\n  \r\n  /** Actor type */\r\n  readonly actorType: ThreatActorType;\r\n  \r\n  /** Suspected country of origin */\r\n  readonly country?: string;\r\n  \r\n  /** Motivation */\r\n  readonly motivation?: ThreatActorMotivation[];\r\n  \r\n  /** Sophistication level */\r\n  readonly sophistication: ThreatActorSophistication;\r\n  \r\n  /** Attribution confidence */\r\n  readonly confidence: ConfidenceLevel;\r\n  \r\n  /** Supporting evidence */\r\n  readonly evidence?: string[];\r\n}\r\n\r\n/**\r\n * Threat Actor Types\r\n */\r\nexport enum ThreatActorType {\r\n  NATION_STATE = 'nation_state',\r\n  CYBER_CRIMINAL = 'cyber_criminal',\r\n  HACKTIVIST = 'hacktivist',\r\n  INSIDER = 'insider',\r\n  TERRORIST = 'terrorist',\r\n  SCRIPT_KIDDIE = 'script_kiddie',\r\n  UNKNOWN = 'unknown',\r\n}\r\n\r\n/**\r\n * Threat Actor Motivations\r\n */\r\nexport enum ThreatActorMotivation {\r\n  FINANCIAL = 'financial',\r\n  ESPIONAGE = 'espionage',\r\n  SABOTAGE = 'sabotage',\r\n  IDEOLOGY = 'ideology',\r\n  REVENGE = 'revenge',\r\n  NOTORIETY = 'notoriety',\r\n  UNKNOWN = 'unknown',\r\n}\r\n\r\n/**\r\n * Threat Actor Sophistication Levels\r\n */\r\nexport enum ThreatActorSophistication {\r\n  MINIMAL = 'minimal',\r\n  INTERMEDIATE = 'intermediate',\r\n  ADVANCED = 'advanced',\r\n  EXPERT = 'expert',\r\n  INNOVATOR = 'innovator',\r\n  STRATEGIC = 'strategic',\r\n}\r\n\r\n/**\r\n * Threat Malware Family Data\r\n */\r\nexport interface ThreatMalwareFamilyData {\r\n  /** Malware family name */\r\n  readonly name: string;\r\n  \r\n  /** Family aliases */\r\n  readonly aliases?: string[];\r\n  \r\n  /** Malware type */\r\n  readonly type: MalwareType;\r\n  \r\n  /** Platform targets */\r\n  readonly platforms?: string[];\r\n  \r\n  /** Capabilities */\r\n  readonly capabilities?: string[];\r\n  \r\n  /** Variants */\r\n  readonly variants?: string[];\r\n}\r\n\r\n/**\r\n * Malware Types\r\n */\r\nexport enum MalwareType {\r\n  TROJAN = 'trojan',\r\n  VIRUS = 'virus',\r\n  WORM = 'worm',\r\n  RANSOMWARE = 'ransomware',\r\n  SPYWARE = 'spyware',\r\n  ADWARE = 'adware',\r\n  ROOTKIT = 'rootkit',\r\n  BACKDOOR = 'backdoor',\r\n  KEYLOGGER = 'keylogger',\r\n  BOTNET = 'botnet',\r\n  RAT = 'rat',\r\n  LOADER = 'loader',\r\n  DROPPER = 'dropper',\r\n  CRYPTOMINER = 'cryptominer',\r\n  OTHER = 'other',\r\n}\r\n\r\n/**\r\n * Threat Attack Technique Data\r\n */\r\nexport interface ThreatAttackTechniqueData {\r\n  /** MITRE ATT&CK technique ID */\r\n  readonly techniqueId: string;\r\n  \r\n  /** Technique name */\r\n  readonly name: string;\r\n  \r\n  /** Tactic */\r\n  readonly tactic: string;\r\n  \r\n  /** Sub-techniques */\r\n  readonly subTechniques?: string[];\r\n  \r\n  /** Platforms */\r\n  readonly platforms?: string[];\r\n  \r\n  /** Data sources */\r\n  readonly dataSources?: string[];\r\n  \r\n  /** Mitigations */\r\n  readonly mitigations?: string[];\r\n  \r\n  /** Detection methods */\r\n  readonly detections?: string[];\r\n}\r\n\r\n/**\r\n * Threat Kill Chain Phases\r\n */\r\nexport enum ThreatKillChainPhase {\r\n  RECONNAISSANCE = 'reconnaissance',\r\n  WEAPONIZATION = 'weaponization',\r\n  DELIVERY = 'delivery',\r\n  EXPLOITATION = 'exploitation',\r\n  INSTALLATION = 'installation',\r\n  COMMAND_CONTROL = 'command_control',\r\n  ACTIONS_OBJECTIVES = 'actions_objectives',\r\n}\r\n\r\n/**\r\n * Threat Timeline Data\r\n */\r\nexport interface ThreatTimelineData {\r\n  /** Campaign start date */\r\n  readonly campaignStart?: string;\r\n  \r\n  /** Campaign end date */\r\n  readonly campaignEnd?: string;\r\n  \r\n  /** Key events in threat timeline */\r\n  readonly events: ThreatTimelineEvent[];\r\n  \r\n  /** Campaign duration in days */\r\n  readonly duration?: number;\r\n  \r\n  /** Activity patterns */\r\n  readonly patterns?: ThreatActivityPattern[];\r\n}\r\n\r\n/**\r\n * Threat Timeline Event\r\n */\r\nexport interface ThreatTimelineEvent {\r\n  /** Event timestamp */\r\n  readonly timestamp: string;\r\n  \r\n  /** Event type */\r\n  readonly type: string;\r\n  \r\n  /** Event description */\r\n  readonly description: string;\r\n  \r\n  /** Event severity */\r\n  readonly severity?: ThreatSeverity;\r\n  \r\n  /** Related indicators */\r\n  readonly indicators?: string[];\r\n}\r\n\r\n/**\r\n * Threat Activity Pattern\r\n */\r\nexport interface ThreatActivityPattern {\r\n  /** Pattern type */\r\n  readonly type: 'temporal' | 'geographical' | 'behavioral';\r\n  \r\n  /** Pattern description */\r\n  readonly description: string;\r\n  \r\n  /** Pattern confidence */\r\n  readonly confidence: ConfidenceLevel;\r\n  \r\n  /** Pattern data */\r\n  readonly data: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Threat Mitigation Data\r\n */\r\nexport interface ThreatMitigationData {\r\n  /** Mitigation type */\r\n  readonly type: ThreatMitigationType;\r\n  \r\n  /** Mitigation description */\r\n  readonly description: string;\r\n  \r\n  /** Implementation difficulty */\r\n  readonly difficulty: 'low' | 'medium' | 'high';\r\n  \r\n  /** Effectiveness rating */\r\n  readonly effectiveness: 'low' | 'medium' | 'high';\r\n  \r\n  /** Implementation cost */\r\n  readonly cost?: 'low' | 'medium' | 'high';\r\n  \r\n  /** Required technologies */\r\n  readonly technologies?: string[];\r\n  \r\n  /** Implementation steps */\r\n  readonly steps?: string[];\r\n}\r\n\r\n/**\r\n * Threat Mitigation Types\r\n */\r\nexport enum ThreatMitigationType {\r\n  PREVENTIVE = 'preventive',\r\n  DETECTIVE = 'detective',\r\n  CORRECTIVE = 'corrective',\r\n  DETERRENT = 'deterrent',\r\n  RECOVERY = 'recovery',\r\n  COMPENSATING = 'compensating',\r\n}\r\n\r\n/**\r\n * Threat Detection Rule Data\r\n */\r\nexport interface ThreatDetectionRuleData {\r\n  /** Rule type */\r\n  readonly type: ThreatDetectionRuleType;\r\n  \r\n  /** Rule name */\r\n  readonly name: string;\r\n  \r\n  /** Rule content */\r\n  readonly content: string;\r\n  \r\n  /** Rule format */\r\n  readonly format: string;\r\n  \r\n  /** Rule confidence */\r\n  readonly confidence: ConfidenceLevel;\r\n  \r\n  /** False positive rate */\r\n  readonly falsePositiveRate?: 'low' | 'medium' | 'high';\r\n  \r\n  /** Rule tags */\r\n  readonly tags?: string[];\r\n  \r\n  /** Rule references */\r\n  readonly references?: string[];\r\n}\r\n\r\n/**\r\n * Threat Detection Rule Types\r\n */\r\nexport enum ThreatDetectionRuleType {\r\n  YARA = 'yara',\r\n  SIGMA = 'sigma',\r\n  SNORT = 'snort',\r\n  SURICATA = 'suricata',\r\n  REGEX = 'regex',\r\n  IOC = 'ioc',\r\n  CUSTOM = 'custom',\r\n}\r\n\r\n/**\r\n * Threat Sharing Information\r\n */\r\nexport interface ThreatSharingInfo {\r\n  /** Traffic Light Protocol (TLP) marking */\r\n  readonly tlp: TLPMarking;\r\n  \r\n  /** Sharing restrictions */\r\n  readonly restrictions?: string[];\r\n  \r\n  /** Permitted actions */\r\n  readonly permittedActions?: ThreatSharingAction[];\r\n  \r\n  /** Sharing agreement reference */\r\n  readonly agreementRef?: string;\r\n  \r\n  /** Copyright information */\r\n  readonly copyright?: string;\r\n  \r\n  /** License information */\r\n  readonly license?: string;\r\n}\r\n\r\n/**\r\n * Traffic Light Protocol Markings\r\n */\r\nexport enum TLPMarking {\r\n  RED = 'red',\r\n  AMBER = 'amber',\r\n  GREEN = 'green',\r\n  WHITE = 'white',\r\n}\r\n\r\n/**\r\n * Threat Sharing Actions\r\n */\r\nexport enum ThreatSharingAction {\r\n  SHARE = 'share',\r\n  ANALYZE = 'analyze',\r\n  DETECT = 'detect',\r\n  BLOCK = 'block',\r\n  ALERT = 'alert',\r\n  INVESTIGATE = 'investigate',\r\n}\r\n\r\n/**\r\n * Threat Data Quality Metrics\r\n */\r\nexport interface ThreatDataQualityMetrics {\r\n  /** Completeness score (0-100) */\r\n  readonly completeness: number;\r\n  \r\n  /** Accuracy score (0-100) */\r\n  readonly accuracy: number;\r\n  \r\n  /** Timeliness score (0-100) */\r\n  readonly timeliness: number;\r\n  \r\n  /** Relevance score (0-100) */\r\n  readonly relevance: number;\r\n  \r\n  /** Overall quality score (0-100) */\r\n  readonly overallScore: number;\r\n  \r\n  /** Quality assessment timestamp */\r\n  readonly assessedAt: string;\r\n  \r\n  /** Quality issues identified */\r\n  readonly issues?: string[];\r\n}\r\n\r\n/**\r\n * Threat Data Validator Interface\r\n */\r\nexport interface ThreatDataValidator {\r\n  /**\r\n   * Validate threat data structure and content\r\n   */\r\n  validate(data: ThreatData): ThreatDataValidationResult;\r\n  \r\n  /**\r\n   * Validate data format version compatibility\r\n   */\r\n  validateVersion(version: string): boolean;\r\n  \r\n  /**\r\n   * Sanitize and normalize threat data\r\n   */\r\n  sanitize(data: ThreatData): ThreatData;\r\n  \r\n  /**\r\n   * Validate threat indicators\r\n   */\r\n  validateIndicators(indicators: ThreatIndicatorData[]): ThreatIndicatorValidationResult[];\r\n}\r\n\r\n/**\r\n * Threat Data Validation Result\r\n */\r\nexport interface ThreatDataValidationResult {\r\n  /** Validation success status */\r\n  readonly isValid: boolean;\r\n  \r\n  /** Validation errors */\r\n  readonly errors: ThreatDataValidationError[];\r\n  \r\n  /** Validation warnings */\r\n  readonly warnings: ThreatDataValidationWarning[];\r\n  \r\n  /** Sanitized data if validation passed */\r\n  readonly sanitizedData?: ThreatData;\r\n  \r\n  /** Quality assessment */\r\n  readonly qualityAssessment?: ThreatDataQualityMetrics;\r\n}\r\n\r\n/**\r\n * Threat Data Validation Error\r\n */\r\nexport interface ThreatDataValidationError {\r\n  /** Error code */\r\n  readonly code: string;\r\n  \r\n  /** Error message */\r\n  readonly message: string;\r\n  \r\n  /** Field path where error occurred */\r\n  readonly field?: string;\r\n  \r\n  /** Invalid value */\r\n  readonly value?: any;\r\n  \r\n  /** Error severity */\r\n  readonly severity: 'error' | 'warning';\r\n  \r\n  /** Suggested fix */\r\n  readonly suggestion?: string;\r\n}\r\n\r\n/**\r\n * Threat Data Validation Warning\r\n */\r\nexport interface ThreatDataValidationWarning {\r\n  /** Warning code */\r\n  readonly code: string;\r\n  \r\n  /** Warning message */\r\n  readonly message: string;\r\n  \r\n  /** Field path where warning occurred */\r\n  readonly field?: string;\r\n  \r\n  /** Warning value */\r\n  readonly value?: any;\r\n  \r\n  /** Suggested action */\r\n  readonly suggestion?: string;\r\n}\r\n\r\n/**\r\n * Threat Indicator Validation Result\r\n */\r\nexport interface ThreatIndicatorValidationResult {\r\n  /** Indicator being validated */\r\n  readonly indicator: ThreatIndicatorData;\r\n  \r\n  /** Validation success status */\r\n  readonly isValid: boolean;\r\n  \r\n  /** Validation errors */\r\n  readonly errors: string[];\r\n  \r\n  /** Validation warnings */\r\n  readonly warnings: string[];\r\n  \r\n  /** Enrichment suggestions */\r\n  readonly enrichmentSuggestions?: string[];\r\n}\r\n\r\n/**\r\n * Threat Data Serializer Interface\r\n */\r\nexport interface ThreatDataSerializer {\r\n  /**\r\n   * Serialize threat data to JSON\r\n   */\r\n  toJson(data: ThreatData): string;\r\n  \r\n  /**\r\n   * Deserialize JSON to threat data\r\n   */\r\n  fromJson(json: string): ThreatData;\r\n  \r\n  /**\r\n   * Serialize to STIX format\r\n   */\r\n  toStix(data: ThreatData): string;\r\n  \r\n  /**\r\n   * Deserialize from STIX format\r\n   */\r\n  fromStix(stix: string): ThreatData;\r\n  \r\n  /**\r\n   * Serialize to binary format for efficient storage\r\n   */\r\n  toBinary(data: ThreatData): Buffer;\r\n  \r\n  /**\r\n   * Deserialize from binary format\r\n   */\r\n  fromBinary(buffer: Buffer): ThreatData;\r\n  \r\n  /**\r\n   * Get supported format versions\r\n   */\r\n  getSupportedVersions(): string[];\r\n}\r\n\r\n/**\r\n * Threat Data Transformer Interface\r\n */\r\nexport interface ThreatDataTransformer {\r\n  /**\r\n   * Transform external threat data to internal format\r\n   */\r\n  transform(externalData: any, sourceFormat: string): ThreatData;\r\n  \r\n  /**\r\n   * Transform internal threat data to external format\r\n   */\r\n  transformToExternal(internalData: ThreatData, targetFormat: string): any;\r\n  \r\n  /**\r\n   * Get supported source formats\r\n   */\r\n  getSupportedSourceFormats(): string[];\r\n  \r\n  /**\r\n   * Get supported target formats\r\n   */\r\n  getSupportedTargetFormats(): string[];\r\n  \r\n  /**\r\n   * Enrich threat data with additional context\r\n   */\r\n  enrich(data: ThreatData, enrichmentSources: string[]): Promise<ThreatData>;\r\n}"], "version": 3}