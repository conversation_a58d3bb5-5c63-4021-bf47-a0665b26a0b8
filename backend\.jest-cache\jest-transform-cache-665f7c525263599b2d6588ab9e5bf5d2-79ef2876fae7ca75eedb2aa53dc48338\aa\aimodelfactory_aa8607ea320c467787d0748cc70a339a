9bc3c04db41f8db6dc3a31da639d425d
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIModelFactory = void 0;
const unique_entity_id_value_object_1 = require("../../../../shared-kernel/value-objects/unique-entity-id.value-object");
const ai_model_entity_1 = require("../entities/ai-model.entity");
class AIModelFactory {
    /**
     * Creates a new AI Model entity
     */
    static create(request, id) {
        this.validateCreateRequest(request);
        const props = {
            name: request.name.trim(),
            version: request.version.trim(),
            provider: request.provider,
            modelType: request.modelType,
            configuration: this.createDefaultConfiguration(request.configuration),
            performance: this.createDefaultPerformance(),
            status: ai_model_entity_1.ModelStatus.INACTIVE, // New models start as inactive
            capabilities: this.createDefaultCapabilities(request.capabilities),
            resourceRequirements: this.createDefaultResourceRequirements(request.resourceRequirements),
            supportedTaskTypes: [...request.supportedTaskTypes],
            tags: request.tags ? [...request.tags] : [],
            priority: request.priority ?? 1.0,
            weight: request.weight ?? 1.0,
            maxConcurrentRequests: request.maxConcurrentRequests ?? 100,
            lastHealthCheck: undefined,
            lastUsed: undefined,
            deployedAt: undefined,
            metadata: request.metadata ? { ...request.metadata } : {},
        };
        return ai_model_entity_1.AIModel.create(props, id);
    }
    /**
     * Reconstitutes an AI Model entity from persistence data
     */
    static reconstitute(data) {
        this.validateReconstitutionData(data);
        const props = {
            name: data.name,
            version: data.version,
            provider: data.provider,
            modelType: data.modelType,
            configuration: data.configuration,
            performance: data.performance,
            status: data.status,
            capabilities: data.capabilities,
            resourceRequirements: data.resourceRequirements,
            supportedTaskTypes: data.supportedTaskTypes,
            tags: data.tags,
            priority: data.priority,
            weight: data.weight,
            maxConcurrentRequests: data.maxConcurrentRequests,
            currentLoad: data.currentLoad,
            lastHealthCheck: data.lastHealthCheck,
            lastUsed: data.lastUsed,
            deployedAt: data.deployedAt,
            metadata: data.metadata,
            createdAt: data.createdAt,
            updatedAt: data.updatedAt,
        };
        const id = unique_entity_id_value_object_1.UniqueEntityId.fromString(data.id);
        return ai_model_entity_1.AIModel.reconstitute(props, id);
    }
    /**
     * Creates a default configuration
     */
    static createDefaultConfiguration(partial) {
        return {
            endpoint: partial?.endpoint,
            apiKey: partial?.apiKey,
            timeout: partial?.timeout ?? 30000, // 30 seconds
            retries: partial?.retries ?? 3,
            batchSize: partial?.batchSize ?? 10,
            customSettings: partial?.customSettings ?? {},
        };
    }
    /**
     * Creates default performance metrics
     */
    static createDefaultPerformance() {
        const now = new Date();
        return {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            averageLatency: 0,
            p95Latency: 0,
            p99Latency: 0,
            accuracy: 0,
            precision: 0,
            recall: 0,
            f1Score: 0,
            throughput: 0,
            lastUpdated: now,
        };
    }
    /**
     * Creates default capabilities
     */
    static createDefaultCapabilities(partial) {
        return {
            maxInputLength: partial?.maxInputLength ?? 4096,
            maxOutputLength: partial?.maxOutputLength ?? 2048,
            supportsBatch: partial?.supportsBatch ?? false,
            supportsStreaming: partial?.supportsStreaming ?? false,
            supportsFineTuning: partial?.supportsFineTuning ?? false,
            languages: partial?.languages ?? ['en'],
            modalities: partial?.modalities ?? ['text'],
        };
    }
    /**
     * Creates default resource requirements
     */
    static createDefaultResourceRequirements(partial) {
        return {
            cpu: partial?.cpu ?? 1,
            memory: partial?.memory ?? 1024, // MB
            gpu: partial?.gpu ?? 0,
            storage: partial?.storage ?? 1024, // MB
            bandwidth: partial?.bandwidth ?? 100, // Mbps
        };
    }
    /**
     * Validates create request
     */
    static validateCreateRequest(request) {
        if (!request.name || request.name.trim().length === 0) {
            throw new Error('Model name is required');
        }
        if (request.name.length > 255) {
            throw new Error('Model name cannot exceed 255 characters');
        }
        if (!request.version || request.version.trim().length === 0) {
            throw new Error('Model version is required');
        }
        if (request.version.length > 50) {
            throw new Error('Model version cannot exceed 50 characters');
        }
        if (!Object.values(ai_model_entity_1.AIProvider).includes(request.provider)) {
            throw new Error('Invalid AI provider');
        }
        if (!Object.values(ai_model_entity_1.ModelType).includes(request.modelType)) {
            throw new Error('Invalid model type');
        }
        if (!request.supportedTaskTypes || request.supportedTaskTypes.length === 0) {
            throw new Error('At least one supported task type is required');
        }
        if (request.supportedTaskTypes.some(task => !task || task.trim().length === 0)) {
            throw new Error('All supported task types must be non-empty strings');
        }
        if (request.priority !== undefined && (request.priority < 0 || request.priority > 10)) {
            throw new Error('Priority must be between 0 and 10');
        }
        if (request.weight !== undefined && (request.weight < 0 || request.weight > 10)) {
            throw new Error('Weight must be between 0 and 10');
        }
        if (request.maxConcurrentRequests !== undefined && request.maxConcurrentRequests <= 0) {
            throw new Error('Max concurrent requests must be greater than 0');
        }
        if (request.configuration?.timeout !== undefined && request.configuration.timeout <= 0) {
            throw new Error('Configuration timeout must be greater than 0');
        }
        if (request.configuration?.retries !== undefined && request.configuration.retries < 0) {
            throw new Error('Configuration retries cannot be negative');
        }
        if (request.configuration?.batchSize !== undefined && request.configuration.batchSize <= 0) {
            throw new Error('Configuration batch size must be greater than 0');
        }
        if (request.tags && request.tags.some(tag => !tag || tag.trim().length === 0)) {
            throw new Error('All tags must be non-empty strings');
        }
    }
    /**
     * Validates reconstitution data
     */
    static validateReconstitutionData(data) {
        if (!data.id || !unique_entity_id_value_object_1.UniqueEntityId.isValid(data.id)) {
            throw new Error('Valid ID is required for reconstitution');
        }
        if (!data.name || data.name.trim().length === 0) {
            throw new Error('Model name is required');
        }
        if (!data.version || data.version.trim().length === 0) {
            throw new Error('Model version is required');
        }
        if (!Object.values(ai_model_entity_1.AIProvider).includes(data.provider)) {
            throw new Error('Invalid AI provider');
        }
        if (!Object.values(ai_model_entity_1.ModelType).includes(data.modelType)) {
            throw new Error('Invalid model type');
        }
        if (!Object.values(ai_model_entity_1.ModelStatus).includes(data.status)) {
            throw new Error('Invalid model status');
        }
        if (!data.supportedTaskTypes || data.supportedTaskTypes.length === 0) {
            throw new Error('At least one supported task type is required');
        }
        if (data.priority < 0 || data.priority > 10) {
            throw new Error('Priority must be between 0 and 10');
        }
        if (data.weight < 0 || data.weight > 10) {
            throw new Error('Weight must be between 0 and 10');
        }
        if (data.maxConcurrentRequests <= 0) {
            throw new Error('Max concurrent requests must be greater than 0');
        }
        if (data.currentLoad < 0) {
            throw new Error('Current load cannot be negative');
        }
        if (!data.createdAt || !(data.createdAt instanceof Date)) {
            throw new Error('Valid creation date is required');
        }
        if (!data.updatedAt || !(data.updatedAt instanceof Date)) {
            throw new Error('Valid update date is required');
        }
    }
    /**
     * Creates a test AI Model with minimal configuration
     */
    static createForTesting(overrides) {
        const defaultRequest = {
            name: 'Test Model',
            version: '1.0.0',
            provider: ai_model_entity_1.AIProvider.PYTHON_AI,
            modelType: ai_model_entity_1.ModelType.CLASSIFICATION,
            supportedTaskTypes: ['classification', 'analysis'],
            tags: ['test'],
            priority: 1.0,
            weight: 1.0,
            maxConcurrentRequests: 10,
            metadata: { test: true },
        };
        const request = { ...defaultRequest, ...overrides };
        return this.create(request);
    }
}
exports.AIModelFactory = AIModelFactory;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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