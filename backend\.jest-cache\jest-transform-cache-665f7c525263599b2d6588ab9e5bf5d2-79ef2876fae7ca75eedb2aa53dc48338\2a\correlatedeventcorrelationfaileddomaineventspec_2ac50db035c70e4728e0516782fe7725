2f88d46f42f9b33b9b16c930cd6fb912
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const correlated_event_correlation_failed_domain_event_1 = require("../correlated-event-correlation-failed.domain-event");
const shared_kernel_1 = require("../../../../../shared-kernel");
describe('CorrelatedEventCorrelationFailedDomainEvent', () => {
    let eventData;
    let aggregateId;
    beforeEach(() => {
        aggregateId = shared_kernel_1.UniqueEntityId.create();
        eventData = {
            enrichedEventId: shared_kernel_1.UniqueEntityId.create(),
            error: 'Correlation engine timeout',
            attempt: 2,
            maxAttemptsExceeded: false,
            failedAt: new Date(),
            errorCode: 'TIMEOUT',
            errorDetails: 'Correlation processing exceeded 120 seconds timeout',
            failedRules: ['temporal_rule_1', 'spatial_rule_2'],
            processingContext: {
                correlationId: 'corr_123',
                eventCount: 1000,
                ruleCount: 5
            },
            isRetryable: true,
            retryDelayMs: 5000
        };
    });
    describe('creation', () => {
        it('should create domain event with required data', () => {
            const domainEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, eventData);
            expect(domainEvent.aggregateId).toEqual(aggregateId);
            expect(domainEvent.eventData).toEqual(eventData);
            expect(domainEvent.eventName).toBe('CorrelatedEventCorrelationFailedDomainEvent');
            expect(domainEvent.occurredOn).toBeInstanceOf(Date);
            expect(domainEvent.eventId).toBeDefined();
        });
        it('should create domain event with custom options', () => {
            const customEventId = shared_kernel_1.UniqueEntityId.create();
            const customOccurredOn = new Date('2023-01-01T00:00:00Z');
            const correlationId = 'corr_123';
            const domainEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, eventData, {
                eventId: customEventId,
                occurredOn: customOccurredOn,
                correlationId,
                eventVersion: 2,
                metadata: { custom: 'data' }
            });
            expect(domainEvent.eventId).toEqual(customEventId);
            expect(domainEvent.occurredOn).toEqual(customOccurredOn);
            expect(domainEvent.correlationId).toBe(correlationId);
            expect(domainEvent.eventVersion).toBe(2);
            expect(domainEvent.metadata).toEqual({ custom: 'data' });
        });
    });
    describe('property getters', () => {
        let domainEvent;
        beforeEach(() => {
            domainEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, eventData);
        });
        it('should return enriched event ID', () => {
            expect(domainEvent.enrichedEventId).toEqual(eventData.enrichedEventId);
        });
        it('should return error message', () => {
            expect(domainEvent.error).toBe(eventData.error);
        });
        it('should return attempt number', () => {
            expect(domainEvent.attempt).toBe(eventData.attempt);
        });
        it('should return max attempts exceeded flag', () => {
            expect(domainEvent.maxAttemptsExceeded).toBe(eventData.maxAttemptsExceeded);
        });
        it('should return failed at timestamp', () => {
            expect(domainEvent.failedAt).toEqual(eventData.failedAt);
        });
        it('should return error code', () => {
            expect(domainEvent.errorCode).toBe(eventData.errorCode);
        });
        it('should return error details', () => {
            expect(domainEvent.errorDetails).toBe(eventData.errorDetails);
        });
        it('should return failed rules', () => {
            expect(domainEvent.failedRules).toEqual(eventData.failedRules);
        });
        it('should return processing context', () => {
            expect(domainEvent.processingContext).toEqual(eventData.processingContext);
        });
        it('should return is retryable flag', () => {
            expect(domainEvent.isRetryable).toBe(eventData.isRetryable);
        });
        it('should return retry delay', () => {
            expect(domainEvent.retryDelayMs).toBe(eventData.retryDelayMs);
        });
        it('should use occurred on when failed at is not provided', () => {
            const eventWithoutFailedAt = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                failedAt: undefined
            });
            expect(eventWithoutFailedAt.failedAt).toEqual(eventWithoutFailedAt.occurredOn);
        });
        it('should return empty array for failed rules when not provided', () => {
            const eventWithoutFailedRules = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                failedRules: undefined
            });
            expect(eventWithoutFailedRules.failedRules).toEqual([]);
        });
        it('should return empty object for processing context when not provided', () => {
            const eventWithoutContext = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                processingContext: undefined
            });
            expect(eventWithoutContext.processingContext).toEqual({});
        });
        it('should default is retryable to true when not provided', () => {
            const eventWithoutRetryable = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                isRetryable: undefined
            });
            expect(eventWithoutRetryable.isRetryable).toBe(true);
        });
        it('should calculate retry delay when not provided', () => {
            const eventWithoutRetryDelay = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                retryDelayMs: undefined
            });
            expect(eventWithoutRetryDelay.retryDelayMs).toBeGreaterThan(0);
        });
    });
    describe('failure analysis methods', () => {
        let domainEvent;
        beforeEach(() => {
            domainEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, eventData);
        });
        it('should identify first failure', () => {
            const firstFailureEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                attempt: 1
            });
            const repeatedFailureEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                attempt: 3
            });
            expect(firstFailureEvent.isFirstFailure()).toBe(true);
            expect(repeatedFailureEvent.isFirstFailure()).toBe(false);
        });
        it('should identify repeated failure', () => {
            const firstFailureEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                attempt: 1
            });
            const repeatedFailureEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                attempt: 3
            });
            expect(firstFailureEvent.isRepeatedFailure()).toBe(false);
            expect(repeatedFailureEvent.isRepeatedFailure()).toBe(true);
        });
        it('should determine when to trigger alerts', () => {
            const maxAttemptsEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                maxAttemptsExceeded: true
            });
            const repeatedFailureEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                attempt: 2
            });
            const nonRetryableEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                isRetryable: false
            });
            const firstFailureEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                attempt: 1
            });
            expect(maxAttemptsEvent.shouldTriggerAlert()).toBe(true);
            expect(repeatedFailureEvent.shouldTriggerAlert()).toBe(true);
            expect(nonRetryableEvent.shouldTriggerAlert()).toBe(true);
            expect(firstFailureEvent.shouldTriggerAlert()).toBe(false);
        });
        it('should identify critical failures', () => {
            const maxAttemptsEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                maxAttemptsExceeded: true
            });
            const criticalErrorEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                errorCode: 'CORRELATION_ENGINE_DOWN'
            });
            const nonRetryableEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                isRetryable: false
            });
            const normalFailureEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                attempt: 1,
                errorCode: 'TIMEOUT'
            });
            expect(maxAttemptsEvent.isCriticalFailure()).toBe(true);
            expect(criticalErrorEvent.isCriticalFailure()).toBe(true);
            expect(nonRetryableEvent.isCriticalFailure()).toBe(true);
            expect(normalFailureEvent.isCriticalFailure()).toBe(false);
        });
    });
    describe('failure categorization', () => {
        it('should categorize transient errors', () => {
            const timeoutEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                errorCode: 'TIMEOUT'
            });
            const networkEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                errorCode: 'NETWORK_ERROR'
            });
            const serviceEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                errorCode: 'SERVICE_UNAVAILABLE'
            });
            expect(timeoutEvent.getFailureCategory()).toBe('transient');
            expect(networkEvent.getFailureCategory()).toBe('transient');
            expect(serviceEvent.getFailureCategory()).toBe('transient');
        });
        it('should categorize configuration errors', () => {
            const configEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                errorCode: 'INVALID_RULE_CONFIG'
            });
            const missingConfigEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                errorCode: 'MISSING_CONFIGURATION'
            });
            expect(configEvent.getFailureCategory()).toBe('configuration');
            expect(missingConfigEvent.getFailureCategory()).toBe('configuration');
        });
        it('should categorize data errors', () => {
            const dataEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                errorCode: 'INVALID_EVENT_DATA'
            });
            const validationEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                errorCode: 'DATA_VALIDATION_FAILED'
            });
            expect(dataEvent.getFailureCategory()).toBe('data');
            expect(validationEvent.getFailureCategory()).toBe('data');
        });
        it('should categorize system errors', () => {
            const engineEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                errorCode: 'CORRELATION_ENGINE_DOWN'
            });
            const dbEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                errorCode: 'DATABASE_CONNECTION_FAILED'
            });
            expect(engineEvent.getFailureCategory()).toBe('system');
            expect(dbEvent.getFailureCategory()).toBe('system');
        });
        it('should categorize unknown errors', () => {
            const unknownEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                errorCode: 'UNKNOWN_ERROR'
            });
            const noCodeEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                errorCode: undefined
            });
            expect(unknownEvent.getFailureCategory()).toBe('unknown');
            expect(noCodeEvent.getFailureCategory()).toBe('unknown');
        });
    });
    describe('failure severity assessment', () => {
        it('should assess critical severity', () => {
            const criticalEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                maxAttemptsExceeded: true
            });
            expect(criticalEvent.getFailureSeverity()).toBe('critical');
        });
        it('should assess high severity', () => {
            const highSeverityEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                maxAttemptsExceeded: true
            });
            expect(highSeverityEvent.getFailureSeverity()).toBe('critical'); // max attempts exceeded is critical
        });
        it('should assess medium severity', () => {
            const mediumSeverityEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                attempt: 2,
                maxAttemptsExceeded: false
            });
            expect(mediumSeverityEvent.getFailureSeverity()).toBe('medium');
        });
        it('should assess low severity', () => {
            const lowSeverityEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                attempt: 1,
                maxAttemptsExceeded: false
            });
            expect(lowSeverityEvent.getFailureSeverity()).toBe('low');
        });
    });
    describe('recovery actions', () => {
        it('should recommend retry for retryable failures', () => {
            const retryableEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                isRetryable: true,
                maxAttemptsExceeded: false,
                retryDelayMs: 5000
            });
            const actions = retryableEvent.getRecoveryActions();
            expect(actions).toContain('Schedule retry in 5000ms');
        });
        it('should recommend transient error actions', () => {
            const transientEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                errorCode: 'TIMEOUT'
            });
            const actions = transientEvent.getRecoveryActions();
            expect(actions).toContain('Monitor system resources');
            expect(actions).toContain('Check network connectivity');
            expect(actions).toContain('Verify service availability');
        });
        it('should recommend configuration error actions', () => {
            const configEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                errorCode: 'INVALID_RULE_CONFIG'
            });
            const actions = configEvent.getRecoveryActions();
            expect(actions).toContain('Review correlation rule configuration');
            expect(actions).toContain('Validate rule parameters');
            expect(actions).toContain('Check configuration syntax');
        });
        it('should recommend data error actions', () => {
            const dataEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                errorCode: 'INVALID_EVENT_DATA'
            });
            const actions = dataEvent.getRecoveryActions();
            expect(actions).toContain('Validate input event data');
            expect(actions).toContain('Check data format and schema');
            expect(actions).toContain('Review data transformation logic');
        });
        it('should recommend system error actions', () => {
            const systemEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                errorCode: 'CORRELATION_ENGINE_DOWN'
            });
            const actions = systemEvent.getRecoveryActions();
            expect(actions).toContain('Check system health and resources');
            expect(actions).toContain('Verify database connectivity');
            expect(actions).toContain('Review system logs');
            expect(actions).toContain('Escalate to operations team');
        });
        it('should recommend max attempts exceeded actions', () => {
            const maxAttemptsEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                maxAttemptsExceeded: true
            });
            const actions = maxAttemptsEvent.getRecoveryActions();
            expect(actions).toContain('Move to dead letter queue');
            expect(actions).toContain('Generate manual review task');
            expect(actions).toContain('Notify operations team');
        });
        it('should recommend alert actions when needed', () => {
            const alertEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                attempt: 2 // Should trigger alert
            });
            const actions = alertEvent.getRecoveryActions();
            expect(actions).toContain('Generate failure alert');
            expect(actions).toContain('Update monitoring dashboards');
        });
    });
    describe('escalation requirements', () => {
        it('should require management escalation for critical failures', () => {
            const criticalEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                errorCode: 'CORRELATION_ENGINE_DOWN'
            });
            const escalation = criticalEvent.getEscalationRequirements();
            expect(escalation.shouldEscalate).toBe(true);
            expect(escalation.escalationLevel).toBe('management');
            expect(escalation.urgency).toBe('critical');
            expect(escalation.timeoutMinutes).toBe(15);
        });
        it('should require ops escalation for high severity failures', () => {
            const highSeverityEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                maxAttemptsExceeded: true
            });
            const escalation = highSeverityEvent.getEscalationRequirements();
            expect(escalation.shouldEscalate).toBe(true);
            expect(escalation.escalationLevel).toBe('ops');
            expect(escalation.urgency).toBe('high');
            expect(escalation.timeoutMinutes).toBe(30);
        });
        it('should require ops escalation for system errors', () => {
            const systemEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                attempt: 2,
                errorCode: 'DATABASE_CONNECTION_FAILED'
            });
            const escalation = systemEvent.getEscalationRequirements();
            expect(escalation.shouldEscalate).toBe(true);
            expect(escalation.escalationLevel).toBe('ops');
            expect(escalation.urgency).toBe('medium');
            expect(escalation.timeoutMinutes).toBe(60);
        });
        it('should require dev escalation for configuration errors', () => {
            const configEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                errorCode: 'INVALID_RULE_CONFIG'
            });
            const escalation = configEvent.getEscalationRequirements();
            expect(escalation.shouldEscalate).toBe(true);
            expect(escalation.escalationLevel).toBe('dev');
            expect(escalation.urgency).toBe('medium');
            expect(escalation.timeoutMinutes).toBe(120);
        });
        it('should not require escalation for low severity transient errors', () => {
            const lowSeverityEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                attempt: 1,
                errorCode: 'TIMEOUT'
            });
            const escalation = lowSeverityEvent.getEscalationRequirements();
            expect(escalation.shouldEscalate).toBe(false);
            expect(escalation.escalationLevel).toBe('ops');
            expect(escalation.urgency).toBe('low');
            expect(escalation.timeoutMinutes).toBe(240);
        });
    });
    describe('metrics calculation', () => {
        it('should calculate failure metrics', () => {
            const domainEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, eventData);
            const metrics = domainEvent.getMetricsToUpdate();
            const failureMetric = metrics.find(m => m.metric === 'correlation_failures');
            expect(failureMetric).toBeDefined();
            expect(failureMetric.value).toBe(1);
            expect(failureMetric.tags).toEqual({
                attempt: '2',
                category: 'transient',
                severity: 'medium',
                error_code: 'TIMEOUT'
            });
        });
        it('should calculate max attempts exceeded metrics', () => {
            const maxAttemptsEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                maxAttemptsExceeded: true
            });
            const metrics = maxAttemptsEvent.getMetricsToUpdate();
            const maxAttemptsMetric = metrics.find(m => m.metric === 'correlation_max_attempts_exceeded');
            expect(maxAttemptsMetric).toBeDefined();
            expect(maxAttemptsMetric.value).toBe(1);
        });
        it('should calculate retry metrics for retryable failures', () => {
            const retryableEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                isRetryable: true,
                maxAttemptsExceeded: false
            });
            const metrics = retryableEvent.getMetricsToUpdate();
            const retryMetric = metrics.find(m => m.metric === 'correlation_retries_scheduled');
            expect(retryMetric).toBeDefined();
            expect(retryMetric.value).toBe(1);
            expect(retryMetric.tags).toEqual({
                attempt: '2',
                delay_ms: '5000'
            });
        });
        it('should calculate alert metrics when alerts are triggered', () => {
            const alertEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                attempt: 2 // Should trigger alert
            });
            const metrics = alertEvent.getMetricsToUpdate();
            const alertMetric = metrics.find(m => m.metric === 'correlation_failure_alerts');
            expect(alertMetric).toBeDefined();
            expect(alertMetric.value).toBe(1);
            expect(alertMetric.tags).toEqual({
                severity: 'medium'
            });
        });
    });
    describe('retry delay calculation', () => {
        it('should calculate exponential backoff delay', () => {
            const attempt1Event = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                attempt: 1,
                retryDelayMs: undefined
            });
            const attempt2Event = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                attempt: 2,
                retryDelayMs: undefined
            });
            const attempt3Event = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                attempt: 3,
                retryDelayMs: undefined
            });
            expect(attempt1Event.retryDelayMs).toBeGreaterThanOrEqual(1000);
            expect(attempt1Event.retryDelayMs).toBeLessThan(1500); // With jitter
            expect(attempt2Event.retryDelayMs).toBeGreaterThanOrEqual(2000);
            expect(attempt2Event.retryDelayMs).toBeLessThan(3000); // With jitter
            expect(attempt3Event.retryDelayMs).toBeGreaterThanOrEqual(4000);
            expect(attempt3Event.retryDelayMs).toBeLessThan(6000); // With jitter
        });
        it('should cap retry delay at maximum', () => {
            const highAttemptEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, {
                ...eventData,
                attempt: 10,
                retryDelayMs: undefined
            });
            expect(highAttemptEvent.retryDelayMs).toBeLessThanOrEqual(330000); // 300000 + 10% jitter
        });
    });
    describe('event summary', () => {
        it('should provide comprehensive event summary', () => {
            const domainEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, eventData);
            const summary = domainEvent.getEventSummary();
            expect(summary.correlatedEventId).toBe(aggregateId.toString());
            expect(summary.enrichedEventId).toBe(eventData.enrichedEventId.toString());
            expect(summary.error).toBe(eventData.error);
            expect(summary.attempt).toBe(eventData.attempt);
            expect(summary.maxAttemptsExceeded).toBe(eventData.maxAttemptsExceeded);
            expect(summary.failedAt).toEqual(eventData.failedAt);
            expect(summary.errorCode).toBe(eventData.errorCode);
            expect(summary.errorDetails).toBe(eventData.errorDetails);
            expect(summary.failedRules).toEqual(eventData.failedRules);
            expect(summary.isRetryable).toBe(eventData.isRetryable);
            expect(summary.retryDelayMs).toBe(eventData.retryDelayMs);
            expect(summary.isFirstFailure).toBe(false);
            expect(summary.isRepeatedFailure).toBe(true);
            expect(summary.shouldTriggerAlert).toBe(true);
            expect(summary.isCriticalFailure).toBe(false);
            expect(summary.failureCategory).toBe('transient');
            expect(summary.failureSeverity).toBe('medium');
            expect(summary.recoveryActions).toBeInstanceOf(Array);
            expect(summary.escalationRequirements).toBeDefined();
            expect(summary.metricsToUpdate).toBeInstanceOf(Array);
        });
    });
    describe('JSON serialization', () => {
        it('should serialize to JSON with event summary', () => {
            const domainEvent = new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(aggregateId, eventData);
            const json = domainEvent.toJSON();
            expect(json.eventName).toBe('CorrelatedEventCorrelationFailedDomainEvent');
            expect(json.aggregateId).toBe(aggregateId.toString());
            expect(json.eventData).toEqual(eventData);
            expect(json.eventSummary).toBeDefined();
            expect(json.occurredOn).toBeInstanceOf(Date);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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