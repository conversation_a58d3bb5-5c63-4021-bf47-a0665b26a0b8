d47929f7ddacdd3db4e552de28fc54bb
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CVSSScore = exports.CVSSSeverity = exports.CVSSVersion = void 0;
const base_value_object_1 = require("../../../../../shared-kernel/value-objects/base-value-object");
/**
 * CVSS Version
 */
var CVSSVersion;
(function (CVSSVersion) {
    CVSSVersion["V2"] = "2.0";
    CVSSVersion["V3_0"] = "3.0";
    CVSSVersion["V3_1"] = "3.1";
    CVSSVersion["V4_0"] = "4.0";
})(CVSSVersion || (exports.CVSSVersion = CVSSVersion = {}));
/**
 * CVSS Severity Rating
 */
var CVSSSeverity;
(function (CVSSSeverity) {
    CVSSSeverity["NONE"] = "none";
    CVSSSeverity["LOW"] = "low";
    CVSSSeverity["MEDIUM"] = "medium";
    CVSSSeverity["HIGH"] = "high";
    CVSSSeverity["CRITICAL"] = "critical";
})(CVSSSeverity || (exports.CVSSSeverity = CVSSSeverity = {}));
/**
 * CVSS Score Value Object
 *
 * Represents a Common Vulnerability Scoring System (CVSS) score with validation
 * and utility methods for vulnerability assessment and risk management.
 *
 * Key features:
 * - CVSS v2, v3.0, v3.1, and v4.0 support
 * - Vector string parsing and validation
 * - Severity classification
 * - Risk assessment utilities
 * - Temporal and environmental scoring
 * - Compliance and reporting support
 */
class CVSSScore extends base_value_object_1.BaseValueObject {
    constructor(props) {
        super(props);
    }
    validate() {
        // Validate base score
        if (this._value.baseScore < CVSSScore.MIN_SCORE || this._value.baseScore > CVSSScore.MAX_SCORE) {
            throw new Error(`CVSS base score must be between ${CVSSScore.MIN_SCORE} and ${CVSSScore.MAX_SCORE}`);
        }
        // Validate temporal score if provided
        if (this._value.temporalScore !== undefined) {
            if (this._value.temporalScore < CVSSScore.MIN_SCORE || this._value.temporalScore > CVSSScore.MAX_SCORE) {
                throw new Error(`CVSS temporal score must be between ${CVSSScore.MIN_SCORE} and ${CVSSScore.MAX_SCORE}`);
            }
        }
        // Validate environmental score if provided
        if (this._value.environmentalScore !== undefined) {
            if (this._value.environmentalScore < CVSSScore.MIN_SCORE || this._value.environmentalScore > CVSSScore.MAX_SCORE) {
                throw new Error(`CVSS environmental score must be between ${CVSSScore.MIN_SCORE} and ${CVSSScore.MAX_SCORE}`);
            }
        }
        // Validate version
        if (!Object.values(CVSSVersion).includes(this._value.version)) {
            throw new Error(`Invalid CVSS version: ${this._value.version}`);
        }
        // Validate vector string format
        if (!this._value.vectorString || this._value.vectorString.trim().length === 0) {
            throw new Error('CVSS vector string is required');
        }
        this.validateVectorString();
        // Validate subscores if provided
        if (this._value.exploitabilityScore !== undefined) {
            if (this._value.exploitabilityScore < 0 || this._value.exploitabilityScore > 10) {
                throw new Error('CVSS exploitability score must be between 0 and 10');
            }
        }
        if (this._value.impactScore !== undefined) {
            if (this._value.impactScore < 0 || this._value.impactScore > 10) {
                throw new Error('CVSS impact score must be between 0 and 10');
            }
        }
    }
    validateVectorString() {
        const vector = this._value.vectorString.trim();
        // Basic format validation based on version
        switch (this._value.version) {
            case CVSSVersion.V2:
                this.validateV2VectorString(vector);
                break;
            case CVSSVersion.V3_0:
            case CVSSVersion.V3_1:
                this.validateV3VectorString(vector);
                break;
            case CVSSVersion.V4_0:
                this.validateV4VectorString(vector);
                break;
            default:
                throw new Error(`Unsupported CVSS version: ${this._value.version}`);
        }
    }
    validateV2VectorString(vector) {
        // CVSS v2 format: (AV:L/AC:M/Au:N/C:P/I:P/A:P)
        const v2Regex = /^\(AV:[LAN]\/AC:[HML]\/Au:[MSN]\/C:[NPC]\/I:[NPC]\/A:[NPC]\)$/;
        if (!v2Regex.test(vector)) {
            throw new Error('Invalid CVSS v2 vector string format');
        }
    }
    validateV3VectorString(vector) {
        // CVSS v3 format: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
        const v3Regex = /^CVSS:3\.[01]\/AV:[NALP]\/AC:[LH]\/PR:[NLH]\/UI:[NR]\/S:[UC]\/C:[NLH]\/I:[NLH]\/A:[NLH]/;
        if (!v3Regex.test(vector)) {
            throw new Error('Invalid CVSS v3 vector string format');
        }
    }
    validateV4VectorString(vector) {
        // CVSS v4 format: CVSS:4.0/AV:N/AC:L/AT:N/PR:N/UI:N/VC:H/VI:H/VA:H/SC:N/SI:N/SA:N
        const v4Regex = /^CVSS:4\.0\/AV:[NALP]\/AC:[LH]\/AT:[NP]\/PR:[NLH]\/UI:[NPA]\/VC:[HLN]\/VI:[HLN]\/VA:[HLN]\/SC:[HLN]\/SI:[HLN]\/SA:[HLN]/;
        if (!v4Regex.test(vector)) {
            throw new Error('Invalid CVSS v4 vector string format');
        }
    }
    /**
     * Create a CVSS score with base score and vector
     */
    static create(baseScore, version, vectorString, options) {
        return new CVSSScore({
            baseScore,
            version,
            vectorString,
            calculatedAt: new Date(),
            ...options,
        });
    }
    /**
     * Create CVSS v3.1 score
     */
    static createV3_1(baseScore, vectorString, options) {
        return CVSSScore.create(baseScore, CVSSVersion.V3_1, vectorString, options);
    }
    /**
     * Create CVSS v4.0 score
     */
    static createV4_0(baseScore, vectorString, options) {
        return CVSSScore.create(baseScore, CVSSVersion.V4_0, vectorString, options);
    }
    /**
     * Parse CVSS score from vector string
     */
    static fromVectorString(vectorString, source) {
        const version = CVSSScore.detectVersion(vectorString);
        const baseScore = CVSSScore.calculateBaseScoreFromVector(vectorString, version);
        return CVSSScore.create(baseScore, version, vectorString, { source });
    }
    static detectVersion(vectorString) {
        if (vectorString.startsWith('CVSS:4.0'))
            return CVSSVersion.V4_0;
        if (vectorString.startsWith('CVSS:3.1'))
            return CVSSVersion.V3_1;
        if (vectorString.startsWith('CVSS:3.0'))
            return CVSSVersion.V3_0;
        if (vectorString.startsWith('(AV:'))
            return CVSSVersion.V2;
        throw new Error('Unable to detect CVSS version from vector string');
    }
    static calculateBaseScoreFromVector(vectorString, version) {
        // This is a simplified implementation
        // In practice, you would implement the full CVSS calculation algorithm
        // For now, return a placeholder that would be calculated based on the vector
        // This should be replaced with actual CVSS calculation logic
        return 7.5; // Placeholder
    }
    /**
     * Get base score
     */
    get baseScore() {
        return this._value.baseScore;
    }
    /**
     * Get temporal score
     */
    get temporalScore() {
        return this._value.temporalScore;
    }
    /**
     * Get environmental score
     */
    get environmentalScore() {
        return this._value.environmentalScore;
    }
    /**
     * Get CVSS version
     */
    get version() {
        return this._value.version;
    }
    /**
     * Get vector string
     */
    get vectorString() {
        return this._value.vectorString;
    }
    /**
     * Get exploitability score
     */
    get exploitabilityScore() {
        return this._value.exploitabilityScore;
    }
    /**
     * Get impact score
     */
    get impactScore() {
        return this._value.impactScore;
    }
    /**
     * Get calculation date
     */
    get calculatedAt() {
        return this._value.calculatedAt;
    }
    /**
     * Get source
     */
    get source() {
        return this._value.source;
    }
    /**
     * Get the effective score (environmental > temporal > base)
     */
    getEffectiveScore() {
        return this._value.environmentalScore ||
            this._value.temporalScore ||
            this._value.baseScore;
    }
    /**
     * Get severity rating based on score
     */
    getSeverity() {
        const score = this.getEffectiveScore();
        if (score === 0.0)
            return CVSSSeverity.NONE;
        if (score >= 0.1 && score <= 3.9)
            return CVSSSeverity.LOW;
        if (score >= 4.0 && score <= 6.9)
            return CVSSSeverity.MEDIUM;
        if (score >= 7.0 && score <= 8.9)
            return CVSSSeverity.HIGH;
        if (score >= 9.0 && score <= 10.0)
            return CVSSSeverity.CRITICAL;
        return CVSSSeverity.NONE;
    }
    /**
     * Check if score is critical
     */
    isCritical() {
        return this.getSeverity() === CVSSSeverity.CRITICAL;
    }
    /**
     * Check if score is high or critical
     */
    isHighOrCritical() {
        const severity = this.getSeverity();
        return severity === CVSSSeverity.HIGH || severity === CVSSSeverity.CRITICAL;
    }
    /**
     * Check if score requires immediate attention
     */
    requiresImmediateAttention() {
        return this.getEffectiveScore() >= 7.0;
    }
    /**
     * Get risk level for business impact
     */
    getRiskLevel() {
        const severity = this.getSeverity();
        switch (severity) {
            case CVSSSeverity.CRITICAL: return 'critical';
            case CVSSSeverity.HIGH: return 'high';
            case CVSSSeverity.MEDIUM: return 'medium';
            case CVSSSeverity.LOW:
            case CVSSSeverity.NONE:
            default:
                return 'low';
        }
    }
    /**
     * Get priority score (1-10) for remediation
     */
    getPriorityScore() {
        const score = this.getEffectiveScore();
        return Math.ceil(score); // Convert 0-10 scale to 1-10 priority
    }
    /**
     * Get remediation timeline based on score
     */
    getRemediationTimeline() {
        const severity = this.getSeverity();
        switch (severity) {
            case CVSSSeverity.CRITICAL:
                return {
                    immediate: true,
                    days: 1,
                    description: 'Immediate remediation required within 24 hours',
                };
            case CVSSSeverity.HIGH:
                return {
                    immediate: false,
                    days: 7,
                    description: 'High priority remediation within 7 days',
                };
            case CVSSSeverity.MEDIUM:
                return {
                    immediate: false,
                    days: 30,
                    description: 'Medium priority remediation within 30 days',
                };
            case CVSSSeverity.LOW:
                return {
                    immediate: false,
                    days: 90,
                    description: 'Low priority remediation within 90 days',
                };
            default:
                return {
                    immediate: false,
                    days: 365,
                    description: 'No immediate remediation required',
                };
        }
    }
    /**
     * Get compliance requirements based on score
     */
    getComplianceRequirements() {
        const isHighRisk = this.isHighOrCritical();
        return {
            pciDss: isHighRisk,
            sox: this.isCritical(),
            hipaa: isHighRisk,
            gdpr: isHighRisk,
            reporting: this.getEffectiveScore() >= 4.0,
        };
    }
    /**
     * Create a new score with temporal metrics
     */
    withTemporalScore(temporalScore) {
        return new CVSSScore({
            ...this._value,
            temporalScore,
        });
    }
    /**
     * Create a new score with environmental metrics
     */
    withEnvironmentalScore(environmentalScore) {
        return new CVSSScore({
            ...this._value,
            environmentalScore,
        });
    }
    /**
     * Create a new score with subscores
     */
    withSubscores(exploitabilityScore, impactScore) {
        return new CVSSScore({
            ...this._value,
            exploitabilityScore,
            impactScore,
        });
    }
    /**
     * Get score summary for reporting
     */
    getScoreSummary() {
        const remediation = this.getRemediationTimeline();
        return {
            baseScore: this._value.baseScore,
            temporalScore: this._value.temporalScore,
            environmentalScore: this._value.environmentalScore,
            effectiveScore: this.getEffectiveScore(),
            severity: this.getSeverity(),
            riskLevel: this.getRiskLevel(),
            priorityScore: this.getPriorityScore(),
            requiresImmediateAttention: this.requiresImmediateAttention(),
            remediationDays: remediation.days,
        };
    }
    /**
     * Compare CVSS scores
     */
    equals(other) {
        if (!other) {
            return false;
        }
        if (this === other) {
            return true;
        }
        return this._value.baseScore === other._value.baseScore &&
            this._value.version === other._value.version &&
            this._value.vectorString === other._value.vectorString;
    }
    /**
     * Get string representation
     */
    toString() {
        return `CVSS ${this._value.version}: ${this._value.baseScore} (${this.getSeverity()})`;
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            baseScore: this._value.baseScore,
            temporalScore: this._value.temporalScore,
            environmentalScore: this._value.environmentalScore,
            version: this._value.version,
            vectorString: this._value.vectorString,
            exploitabilityScore: this._value.exploitabilityScore,
            impactScore: this._value.impactScore,
            calculatedAt: this._value.calculatedAt?.toISOString(),
            source: this._value.source,
            summary: this.getScoreSummary(),
            compliance: this.getComplianceRequirements(),
            remediation: this.getRemediationTimeline(),
        };
    }
    /**
     * Create CVSSScore from JSON
     */
    static fromJSON(json) {
        return new CVSSScore({
            baseScore: json.baseScore,
            temporalScore: json.temporalScore,
            environmentalScore: json.environmentalScore,
            version: json.version,
            vectorString: json.vectorString,
            exploitabilityScore: json.exploitabilityScore,
            impactScore: json.impactScore,
            calculatedAt: json.calculatedAt ? new Date(json.calculatedAt) : undefined,
            source: json.source,
        });
    }
    /**
     * Validate CVSS score format without creating instance
     */
    static isValid(baseScore, version, vectorString) {
        try {
            new CVSSScore({ baseScore, version, vectorString });
            return true;
        }
        catch {
            return false;
        }
    }
}
exports.CVSSScore = CVSSScore;
CVSSScore.MIN_SCORE = 0.0;
CVSSScore.MAX_SCORE = 10.0;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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