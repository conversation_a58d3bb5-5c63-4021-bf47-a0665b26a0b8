{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\infrastructure\\jobs\\threat-analysis.processor.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,uCAAkG;AAClG,2CAAwC;AACxC,+BAA2B;AAC3B,6CAAmD;AACnD,qCAAqC;AAErC,iGAAsF;AACtF,mFAAwE;AACxE,yGAA6F;AAC7F,wGAAmG;AACnG,sFAAkF;AAClF,uGAAmG;AA6CnG;;;GAGG;AAEI,IAAM,uBAAuB,+BAA7B,MAAM,uBAAuB;IAGlC,YAEE,gBAAiE,EAEjE,eAAyD,EAEzD,aAAiE,EAChD,yBAAoD,EACpD,aAA4B,EAC5B,mBAAwC;QAPxC,qBAAgB,GAAhB,gBAAgB,CAAgC;QAEhD,oBAAe,GAAf,eAAe,CAAyB;QAExC,kBAAa,GAAb,aAAa,CAAmC;QAChD,8BAAyB,GAAzB,yBAAyB,CAA2B;QACpD,kBAAa,GAAb,aAAa,CAAe;QAC5B,wBAAmB,GAAnB,mBAAmB,CAAqB;QAX1C,WAAM,GAAG,IAAI,eAAM,CAAC,yBAAuB,CAAC,IAAI,CAAC,CAAC;IAYhE,CAAC;IAEJ;;OAEG;IAEG,AAAN,KAAK,CAAC,yBAAyB,CAAC,GAA+B;QAC7D,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAEtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE;YACnD,KAAK,EAAE,GAAG,CAAC,EAAE;YACb,QAAQ;YACR,MAAM;SACP,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;gBACjD,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE;gBACvB,SAAS,EAAE,CAAC,YAAY,EAAE,aAAa,CAAC;aACzC,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,kCAAkC,QAAQ,EAAE,CAAC,CAAC;YAChE,CAAC;YAED,MAAM,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAEvB,iDAAiD;YACjD,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;YAExE,MAAM,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAEvB,iDAAiD;YACjD,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,CAAC;YAE3E,MAAM,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAEvB,+BAA+B;YAC/B,MAAM,UAAU,GAAG,IAAI,CAAC,8BAA8B,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAC;YAE5F,MAAM,MAAM,GAA8B;gBACxC,QAAQ;gBACR,eAAe;gBACf,UAAU;gBACV,YAAY,EAAE,IAAI,IAAI,EAAE;aACzB,CAAC;YAEF,8DAA8D;YAC9D,IAAI,UAAU,GAAG,GAAG,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;gBAC5E,MAAM,QAAQ,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;gBACpC,MAAM,CAAC,aAAa,GAAG,QAAQ,CAAC,OAAO,CAAC;gBACxC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC;gBAC3B,MAAM,CAAC,gBAAgB,GAAG;oBACxB,GAAG,MAAM,CAAC,gBAAgB;oBAC1B,mBAAmB,EAAE,MAAM;oBAC3B,eAAe,EAAE,IAAI,IAAI,EAAE;iBAC5B,CAAC;gBAEF,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAEzC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,EAAE;oBAC5C,QAAQ;oBACR,OAAO,EAAE,QAAQ,CAAC,OAAO;oBACzB,SAAS,EAAE,QAAQ,CAAC,SAAS;oBAC7B,UAAU;iBACX,CAAC,CAAC;YACL,CAAC;YAED,MAAM,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAExB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gCAAgC,EAAE;gBAChD,KAAK,EAAE,GAAG,CAAC,EAAE;gBACb,QAAQ;gBACR,eAAe,EAAE,eAAe,CAAC,MAAM;gBACvC,UAAU;aACX,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,6BAA6B,EAAE;gBACtD,KAAK,EAAE,GAAG,CAAC,EAAE;gBACb,QAAQ;gBACR,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IAEG,AAAN,KAAK,CAAC,mBAAmB,CAAC,GAA+B;QACvD,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAEtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;YAC7C,KAAK,EAAE,GAAG,CAAC,EAAE;YACb,QAAQ;YACR,MAAM;SACP,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;gBACjD,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE;gBACvB,SAAS,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,gBAAgB,CAAC;aAC3D,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,kCAAkC,QAAQ,EAAE,CAAC,CAAC;YAChE,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC;YAElC,MAAM,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAEvB,iDAAiD;YACjD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;YAElE,MAAM,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAEvB,MAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,CAAC;YAE7D,MAAM,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAEvB,+BAA+B;YAC/B,MAAM,CAAC,SAAS,GAAG,QAAQ,CAAC;YAC5B,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAEzC,MAAM,MAAM,GAAwB;gBAClC,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,OAAO,EAAE,cAAc;gBACvB,YAAY,EAAE,IAAI,IAAI,EAAE;aACzB,CAAC;YAEF,MAAM,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAExB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0BAA0B,EAAE;gBAC1C,KAAK,EAAE,GAAG,CAAC,EAAE;gBACb,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,MAAM,EAAE,QAAQ,GAAG,CAAC,QAAQ,IAAI,CAAC,CAAC;aACnC,CAAC,CAAC;YAEH,kDAAkD;YAClD,IAAI,QAAQ,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC,GAAG,GAAG,EAAE,CAAC;gBACpD,MAAM,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC7E,CAAC;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,uBAAuB,EAAE;gBAChD,KAAK,EAAE,GAAG,CAAC,EAAE;gBACb,QAAQ;gBACR,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IAEG,AAAN,KAAK,CAAC,mBAAmB,CAAC,GAA+B;QACvD,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAEpC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;YAC7C,KAAK,EAAE,GAAG,CAAC,EAAE;YACb,QAAQ,EAAE,MAAM,EAAE,MAAM;YACxB,MAAM;SACP,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACnC,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;YACxD,CAAC;YAED,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC;YAChC,IAAI,cAAc,GAAG,CAAC,CAAC;YACvB,IAAI,aAAa,GAAG,CAAC,CAAC;YAEtB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBAC3B,IAAI,CAAC;oBACH,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;oBAEvE,IAAI,GAAG,EAAE,CAAC;wBACR,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;wBACnD,IAAI,gBAAgB,CAAC,OAAO,EAAE,CAAC;4BAC7B,aAAa,EAAE,CAAC;wBAClB,CAAC;oBACH,CAAC;oBAED,cAAc,EAAE,CAAC;oBAEjB,kBAAkB;oBAClB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,cAAc,GAAG,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC;oBAChE,MAAM,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAE7B,sBAAsB;oBACtB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;gBACzD,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,sBAAsB,EAAE;wBAC9C,KAAK;wBACL,KAAK,EAAE,KAAK,CAAC,OAAO;qBACrB,CAAC,CAAC;oBACH,cAAc,EAAE,CAAC;gBACnB,CAAC;YACH,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0BAA0B,EAAE;gBAC1C,KAAK,EAAE,GAAG,CAAC,EAAE;gBACb,SAAS;gBACT,cAAc;gBACd,aAAa;aACd,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,uBAAuB,EAAE;gBAChD,KAAK,EAAE,GAAG,CAAC,EAAE;gBACb,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IAEG,AAAN,KAAK,CAAC,uBAAuB,CAAC,GAA+B;QAC3D,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAEtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;YACjD,KAAK,EAAE,GAAG,CAAC,EAAE;YACb,QAAQ;YACR,MAAM;SACP,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,kBAAkB,CAAC;gBAC3E,oBAAoB,EAAE,QAAQ;gBAC9B,gBAAgB,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,YAAY,CAAC;gBACnE,UAAU,EAAE,EAAE;gBACd,mBAAmB,EAAE,GAAG;aACzB,CAAC,CAAC;YAEH,MAAM,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAExB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,8BAA8B,EAAE;gBAC9C,KAAK,EAAE,GAAG,CAAC,EAAE;gBACb,QAAQ;gBACR,gBAAgB,EAAE,YAAY,CAAC,MAAM;aACtC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,2BAA2B,EAAE;gBACpD,KAAK,EAAE,GAAG,CAAC,EAAE;gBACb,QAAQ;gBACR,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IAEH,QAAQ,CAAC,GAAQ;QACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;YAC/C,KAAK,EAAE,GAAG,CAAC,EAAE;YACb,OAAO,EAAE,GAAG,CAAC,IAAI;YACjB,YAAY,EAAE,GAAG,CAAC,IAAI,CAAC,YAAY;SACpC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IAEH,WAAW,CAAC,GAAQ,EAAE,MAAW;QAC/B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,EAAE;YAC/C,KAAK,EAAE,GAAG,CAAC,EAAE;YACb,OAAO,EAAE,GAAG,CAAC,IAAI;YACjB,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,SAAS;YACpC,YAAY,EAAE,GAAG,CAAC,IAAI,CAAC,YAAY;SACpC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IAEH,QAAQ,CAAC,GAAQ,EAAE,KAAY;QAC7B,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,4BAA4B,EAAE;YACrD,KAAK,EAAE,GAAG,CAAC,EAAE;YACb,OAAO,EAAE,GAAG,CAAC,IAAI;YACjB,KAAK,EAAE,KAAK,CAAC,OAAO;YACpB,YAAY,EAAE,GAAG,CAAC,IAAI,CAAC,YAAY;YACnC,QAAQ,EAAE,GAAG,CAAC,YAAY;SAC3B,CAAC,CAAC;IACL,CAAC;IAED,yBAAyB;IAEjB,KAAK,CAAC,yBAAyB,CAAC,MAA0B;QAChE,MAAM,OAAO,GAAG;YACd,UAAU,EAAE,MAAM,CAAC,WAAW,EAAE,UAAU,IAAI,EAAE;YAChD,OAAO,EAAE,MAAM,CAAC,WAAW,EAAE,OAAO,IAAI,EAAE;YAC1C,eAAe,EAAE,MAAM,CAAC,eAAe,IAAI,EAAE;YAC7C,iBAAiB,EAAE,MAAM,CAAC,iBAAiB,IAAI,EAAE;YACjD,aAAa,EAAE,MAAM,CAAC,gBAAgB,EAAE,aAAa;YACrD,cAAc,EAAE,MAAM,CAAC,gBAAgB,EAAE,cAAc;YACvD,gBAAgB,EAAE,MAAM,CAAC,kBAAkB;YAC3C,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBACzC,IAAI,EAAE,GAAG,CAAC,OAAO;gBACjB,KAAK,EAAE,GAAG,CAAC,KAAK;aACjB,CAAC,CAAC,IAAI,EAAE;SACV,CAAC;QAEF,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,OAAY;QAM5C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YAC7C,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;SAC1B,CAAC,CAAC;QAEH,MAAM,OAAO,GAAG,EAAE,CAAC;QAEnB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,MAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YACvD,IAAI,KAAK,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC;gBAC3B,OAAO,CAAC,IAAI,CAAC;oBACX,OAAO,EAAE,KAAK,CAAC,EAAE;oBACjB,SAAS,EAAE,KAAK,CAAC,IAAI;oBACrB,UAAU,EAAE,KAAK,CAAC,UAAU;oBAC5B,OAAO,EAAE,KAAK,CAAC,OAAO;iBACvB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACzE,CAAC;IAEO,mBAAmB,CAAC,KAAkB,EAAE,OAAY;QAC1D,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,MAAM,QAAQ,GAAG,EAAE,CAAC;QAEpB,yBAAyB;QACzB,IAAI,OAAO,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;YAChE,MAAM,WAAW,GAAG,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CACrD,KAAK,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAClC,CAAC,MAAM,CAAC;YACT,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;gBACpB,KAAK,IAAI,CAAC,CAAC;gBACX,OAAO,CAAC,IAAI,CAAC,4BAA4B,WAAW,WAAW,CAAC,CAAC;YACnE,CAAC;QACH,CAAC;QAED,yBAAyB;QACzB,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;YAC1D,MAAM,cAAc,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CACnD,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,CAChD,CAAC,MAAM,CAAC;YACT,IAAI,cAAc,GAAG,CAAC,EAAE,CAAC;gBACvB,KAAK,IAAI,CAAC,CAAC;gBACX,OAAO,CAAC,IAAI,CAAC,4BAA4B,cAAc,WAAW,CAAC,CAAC;YACtE,CAAC;QACH,CAAC;QAED,6BAA6B;QAC7B,IAAI,OAAO,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,iBAAiB,EAAE,CAAC;YACpE,MAAM,YAAY,GAAG,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CACxD,KAAK,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,CACpC,CAAC,MAAM,CAAC;YACT,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;gBACrB,KAAK,IAAI,CAAC,CAAC;gBACX,OAAO,CAAC,IAAI,CAAC,8BAA8B,YAAY,WAAW,CAAC,CAAC;YACtE,CAAC;QACH,CAAC;QAED,0BAA0B;QAC1B,IAAI,OAAO,CAAC,aAAa,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;YACzC,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CACrC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC,CACnE,CAAC;YACF,IAAI,SAAS,EAAE,CAAC;gBACd,KAAK,IAAI,CAAC,CAAC;gBACX,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAC7C,CAAC;QACH,CAAC;QAED,OAAO;YACL,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,QAAQ,EAAE,GAAG,CAAC;YAC3C,OAAO;SACR,CAAC;IACJ,CAAC;IAEO,8BAA8B,CAAC,OAAY,EAAE,eAAsB;QACzE,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAE3C,MAAM,QAAQ,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;QAErC,6CAA6C;QAC7C,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,UAAU,IAAI,GAAG,CAAC;QACpB,CAAC;QAED,sEAAsE;QACtE,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,MAAM,UAAU,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;YACtC,IAAI,QAAQ,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC;gBACtD,UAAU,IAAI,IAAI,CAAC;YACrB,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IAChD,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,MAA0B;QAM9D,MAAM,OAAO,GAAG,EAAE,CAAC;QAEnB,kBAAkB;QAClB,MAAM,cAAc,GAAG,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;QAC7E,OAAO,CAAC,IAAI,CAAC;YACX,MAAM,EAAE,UAAU;YAClB,MAAM,EAAE,IAAI;YACZ,KAAK,EAAE,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC;YACtC,MAAM,EAAE,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI;SAC/C,CAAC,CAAC;QAEH,oBAAoB;QACpB,MAAM,gBAAgB,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;QACrE,OAAO,CAAC,IAAI,CAAC;YACX,MAAM,EAAE,YAAY;YACpB,MAAM,EAAE,GAAG;YACX,KAAK,EAAE,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC;YAC1C,MAAM,EAAE,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,GAAG;SAClD,CAAC,CAAC;QAEH,qBAAqB;QACrB,MAAM,gBAAgB,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,OAAO,CAAC,IAAI,CAAC;YACX,MAAM,EAAE,aAAa;YACrB,MAAM,EAAE,IAAI;YACZ,KAAK,EAAE,gBAAgB;YACvB,MAAM,EAAE,gBAAgB,GAAG,IAAI;SAChC,CAAC,CAAC;QAEH,wBAAwB;QACxB,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,gBAAgB,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QACnE,OAAO,CAAC,IAAI,CAAC;YACX,MAAM,EAAE,uBAAuB;YAC/B,MAAM,EAAE,GAAG;YACX,KAAK,EAAE,gBAAgB;YACvB,MAAM,EAAE,gBAAgB,GAAG,GAAG;SAC/B,CAAC,CAAC;QAEH,iBAAiB;QACjB,MAAM,iBAAiB,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;YACzC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YAC9E,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAEhF,MAAM,YAAY,GAAG,iBAAiB,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC9B,iBAAiB,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7B,iBAAiB,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,OAAO,CAAC,IAAI,CAAC;YACX,MAAM,EAAE,SAAS;YACjB,MAAM,EAAE,IAAI;YACZ,KAAK,EAAE,YAAY;YACnB,MAAM,EAAE,YAAY,GAAG,IAAI;SAC5B,CAAC,CAAC;QAEH,mBAAmB;QACnB,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QACpE,OAAO,CAAC,IAAI,CAAC;YACX,MAAM,EAAE,WAAW;YACnB,MAAM,EAAE,GAAG;YACX,KAAK,EAAE,QAAQ;YACf,MAAM,EAAE,QAAQ,GAAG,GAAG;SACvB,CAAC,CAAC;QAEH,4BAA4B;QAC5B,MAAM,eAAe,GAAG,CAAC,WAAW,EAAE,YAAY,EAAE,QAAQ,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;QACvF,MAAM,eAAe,GAAG,MAAM,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CACvD,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAC1C,CAAC;QACF,MAAM,aAAa,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,OAAO,CAAC,IAAI,CAAC;YACX,MAAM,EAAE,oBAAoB;YAC5B,MAAM,EAAE,IAAI;YACZ,KAAK,EAAE,aAAa;YACpB,MAAM,EAAE,aAAa,GAAG,IAAI;SAC7B,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,sBAAsB,CAAC,OAAiF;QAC9G,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAClE,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IACrC,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,GAA0B;QAChD,IAAI,CAAC;YACH,uCAAuC;YACvC,yEAAyE;YAEzE,MAAM,cAAc,GAAG;gBACrB,MAAM,EAAE,oBAAoB;gBAC5B,UAAU,EAAE,IAAI,IAAI,EAAE;gBACtB,IAAI,EAAE;oBACJ,QAAQ,EAAE,IAAI;oBACd,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB;aACF,CAAC;YAEF,GAAG,CAAC,aAAa,CAAC,oBAAoB,EAAE,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAClE,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAEnC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC;QAClD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,2BAA2B,CACvC,MAA0B,EAC1B,QAAgB,EAChB,QAAgB,EAChB,MAAe;QAEf,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;YACnC,MAAM,UAAU,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC;YAE1D,MAAM,YAAY,GAAG;gBACnB,KAAK,EAAE,sBAAsB;gBAC7B,OAAO,EAAE,WAAW,MAAM,CAAC,KAAK,WAAW,UAAU,SAAS,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;gBAC7G,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,SAAkB,CAAC,CAAC,CAAC,MAAe;gBACvD,IAAI,EAAE;oBACJ,QAAQ,EAAE,MAAM,CAAC,EAAE;oBACnB,QAAQ;oBACR,QAAQ;oBACR,MAAM;iBACP;aACF,CAAC;YAEF,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAAC;oBAClD,MAAM;oBACN,GAAG,YAAY;iBAChB,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAAC;oBAClD,KAAK,EAAE,CAAC,gBAAgB,EAAE,kBAAkB,CAAC;oBAC7C,GAAG,YAAY;iBAChB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,0CAA0C,EAAE;gBACnE,QAAQ,EAAE,MAAM,CAAC,EAAE;gBACnB,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;CACF,CAAA;AAjlBY,0DAAuB;AAmB5B;IADL,IAAA,cAAO,EAAC,sBAAsB,CAAC;;yDACK,UAAG,oBAAH,UAAG;wDAA0B,OAAO,oBAAP,OAAO;wEAgFxE;AAMK;IADL,IAAA,cAAO,EAAC,gBAAgB,CAAC;;yDACK,UAAG,oBAAH,UAAG;wDAA0B,OAAO,oBAAP,OAAO;kEAoElE;AAMK;IADL,IAAA,cAAO,EAAC,gBAAgB,CAAC;;yDACK,UAAG,oBAAH,UAAG;wDAA0B,OAAO,oBAAP,OAAO;kEA2DlE;AAMK;IADL,IAAA,cAAO,EAAC,oBAAoB,CAAC;;yDACK,UAAG,oBAAH,UAAG;wDAA0B,OAAO,oBAAP,OAAO;sEAgCtE;AAMD;IADC,IAAA,oBAAa,GAAE;;yDACF,UAAG,oBAAH,UAAG;;uDAMhB;AAMD;IADC,IAAA,uBAAgB,GAAE;;yDACF,UAAG,oBAAH,UAAG;;0DAOnB;AAMD;IADC,IAAA,oBAAa,GAAE;;yDACF,UAAG,oBAAH,UAAG,oDAAS,KAAK,oBAAL,KAAK;;uDAQ9B;kCA3TU,uBAAuB;IADnC,IAAA,gBAAS,EAAC,iBAAiB,CAAC;IAKxB,WAAA,IAAA,0BAAgB,EAAC,+CAAkB,CAAC,CAAA;IAEpC,WAAA,IAAA,0BAAgB,EAAC,iCAAW,CAAC,CAAA;IAE7B,WAAA,IAAA,0BAAgB,EAAC,sDAAqB,CAAC,CAAA;yDAHL,oBAAU,oBAAV,oBAAU,oDAEX,oBAAU,oBAAV,oBAAU,oDAEZ,oBAAU,oBAAV,oBAAU,oDACE,uDAAyB,oBAAzB,uDAAyB,oDACrC,8BAAa,oBAAb,8BAAa,oDACP,0CAAmB,oBAAnB,0CAAmB;GAZhD,uBAAuB,CAilBnC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\infrastructure\\jobs\\threat-analysis.processor.ts"], "sourcesContent": ["import { Processor, Process, OnQueueActive, OnQueueCompleted, OnQueueFailed } from '@nestjs/bull';\r\nimport { Logger } from '@nestjs/common';\r\nimport { Job } from 'bull';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { ThreatIntelligence } from '../../domain/entities/threat-intelligence.entity';\r\nimport { ThreatActor } from '../../domain/entities/threat-actor.entity';\r\nimport { IndicatorOfCompromise } from '../../domain/entities/indicator-of-compromise.entity';\r\nimport { ThreatIntelligenceService } from '../../application/services/threat-intelligence.service';\r\nimport { LoggerService } from '../../../../infrastructure/logging/logger.service';\r\nimport { NotificationService } from '../../../../infrastructure/notification/notification.service';\r\n\r\n/**\r\n * Job data for threat analysis operations\r\n */\r\nexport interface ThreatAnalysisJobData {\r\n  threatId?: string;\r\n  actorId?: string;\r\n  iocIds?: string[];\r\n  analysisType: 'attribution' | 'scoring' | 'correlation' | 'enrichment';\r\n  userId?: string;\r\n  priority?: number;\r\n}\r\n\r\n/**\r\n * Attribution analysis result\r\n */\r\nexport interface AttributionAnalysisResult {\r\n  threatId: string;\r\n  suggestedActors: Array<{\r\n    actorId: string;\r\n    actorName: string;\r\n    confidence: number;\r\n    reasons: string[];\r\n  }>;\r\n  confidence: number;\r\n  analysisDate: Date;\r\n}\r\n\r\n/**\r\n * Threat scoring result\r\n */\r\nexport interface ThreatScoringResult {\r\n  threatId: string;\r\n  oldScore?: number;\r\n  newScore: number;\r\n  factors: Array<{\r\n    factor: string;\r\n    weight: number;\r\n    value: number;\r\n    impact: number;\r\n  }>;\r\n  analysisDate: Date;\r\n}\r\n\r\n/**\r\n * Processor for threat analysis and intelligence operations\r\n * Handles attribution analysis, threat scoring, and correlation tasks\r\n */\r\n@Processor('threat-analysis')\r\nexport class ThreatAnalysisProcessor {\r\n  private readonly logger = new Logger(ThreatAnalysisProcessor.name);\r\n\r\n  constructor(\r\n    @InjectRepository(ThreatIntelligence)\r\n    private readonly threatRepository: Repository<ThreatIntelligence>,\r\n    @InjectRepository(ThreatActor)\r\n    private readonly actorRepository: Repository<ThreatActor>,\r\n    @InjectRepository(IndicatorOfCompromise)\r\n    private readonly iocRepository: Repository<IndicatorOfCompromise>,\r\n    private readonly threatIntelligenceService: ThreatIntelligenceService,\r\n    private readonly loggerService: LoggerService,\r\n    private readonly notificationService: NotificationService,\r\n  ) {}\r\n\r\n  /**\r\n   * Process threat attribution analysis\r\n   */\r\n  @Process('attribution-analysis')\r\n  async handleAttributionAnalysis(job: Job<ThreatAnalysisJobData>): Promise<AttributionAnalysisResult> {\r\n    const { threatId, userId } = job.data;\r\n    \r\n    this.logger.debug('Processing attribution analysis', {\r\n      jobId: job.id,\r\n      threatId,\r\n      userId,\r\n    });\r\n\r\n    try {\r\n      const threat = await this.threatRepository.findOne({\r\n        where: { id: threatId },\r\n        relations: ['indicators', 'threatActor'],\r\n      });\r\n\r\n      if (!threat) {\r\n        throw new Error(`Threat intelligence not found: ${threatId}`);\r\n      }\r\n\r\n      await job.progress(25);\r\n\r\n      // Analyze threat characteristics for attribution\r\n      const attributionFactors = await this.analyzeAttributionFactors(threat);\r\n      \r\n      await job.progress(50);\r\n\r\n      // Find potential threat actors based on analysis\r\n      const suggestedActors = await this.findPotentialActors(attributionFactors);\r\n      \r\n      await job.progress(75);\r\n\r\n      // Calculate overall confidence\r\n      const confidence = this.calculateAttributionConfidence(attributionFactors, suggestedActors);\r\n\r\n      const result: AttributionAnalysisResult = {\r\n        threatId,\r\n        suggestedActors,\r\n        confidence,\r\n        analysisDate: new Date(),\r\n      };\r\n\r\n      // Update threat with attribution if confidence is high enough\r\n      if (confidence > 0.8 && suggestedActors.length > 0 && !threat.threatActorId) {\r\n        const topActor = suggestedActors[0];\r\n        threat.threatActorId = topActor.actorId;\r\n        threat.isAttributed = true;\r\n        threat.customAttributes = {\r\n          ...threat.customAttributes,\r\n          attributionAnalysis: result,\r\n          attributionDate: new Date(),\r\n        };\r\n        \r\n        await this.threatRepository.save(threat);\r\n        \r\n        this.logger.log('Threat attributed to actor', {\r\n          threatId,\r\n          actorId: topActor.actorId,\r\n          actorName: topActor.actorName,\r\n          confidence,\r\n        });\r\n      }\r\n\r\n      await job.progress(100);\r\n\r\n      this.logger.log('Attribution analysis completed', {\r\n        jobId: job.id,\r\n        threatId,\r\n        suggestedActors: suggestedActors.length,\r\n        confidence,\r\n      });\r\n\r\n      return result;\r\n    } catch (error) {\r\n      this.loggerService.error('Attribution analysis failed', {\r\n        jobId: job.id,\r\n        threatId,\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Process threat scoring analysis\r\n   */\r\n  @Process('threat-scoring')\r\n  async handleThreatScoring(job: Job<ThreatAnalysisJobData>): Promise<ThreatScoringResult> {\r\n    const { threatId, userId } = job.data;\r\n    \r\n    this.logger.debug('Processing threat scoring', {\r\n      jobId: job.id,\r\n      threatId,\r\n      userId,\r\n    });\r\n\r\n    try {\r\n      const threat = await this.threatRepository.findOne({\r\n        where: { id: threatId },\r\n        relations: ['indicators', 'threatActor', 'threatCampaign'],\r\n      });\r\n\r\n      if (!threat) {\r\n        throw new Error(`Threat intelligence not found: ${threatId}`);\r\n      }\r\n\r\n      const oldScore = threat.riskScore;\r\n      \r\n      await job.progress(25);\r\n\r\n      // Calculate new risk score with detailed factors\r\n      const scoringFactors = await this.calculateScoringFactors(threat);\r\n      \r\n      await job.progress(50);\r\n\r\n      const newScore = this.calculateWeightedScore(scoringFactors);\r\n      \r\n      await job.progress(75);\r\n\r\n      // Update threat with new score\r\n      threat.riskScore = newScore;\r\n      await this.threatRepository.save(threat);\r\n\r\n      const result: ThreatScoringResult = {\r\n        threatId,\r\n        oldScore,\r\n        newScore,\r\n        factors: scoringFactors,\r\n        analysisDate: new Date(),\r\n      };\r\n\r\n      await job.progress(100);\r\n\r\n      this.logger.log('Threat scoring completed', {\r\n        jobId: job.id,\r\n        threatId,\r\n        oldScore,\r\n        newScore,\r\n        change: newScore - (oldScore || 0),\r\n      });\r\n\r\n      // Send notification for significant score changes\r\n      if (oldScore && Math.abs(newScore - oldScore) > 2.0) {\r\n        await this.sendScoreChangeNotification(threat, oldScore, newScore, userId);\r\n      }\r\n\r\n      return result;\r\n    } catch (error) {\r\n      this.loggerService.error('Threat scoring failed', {\r\n        jobId: job.id,\r\n        threatId,\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Process IOC enrichment\r\n   */\r\n  @Process('ioc-enrichment')\r\n  async handleIOCEnrichment(job: Job<ThreatAnalysisJobData>): Promise<void> {\r\n    const { iocIds, userId } = job.data;\r\n    \r\n    this.logger.debug('Processing IOC enrichment', {\r\n      jobId: job.id,\r\n      iocCount: iocIds?.length,\r\n      userId,\r\n    });\r\n\r\n    try {\r\n      if (!iocIds || iocIds.length === 0) {\r\n        throw new Error('No IOC IDs provided for enrichment');\r\n      }\r\n\r\n      const totalIOCs = iocIds.length;\r\n      let processedCount = 0;\r\n      let enrichedCount = 0;\r\n\r\n      for (const iocId of iocIds) {\r\n        try {\r\n          const ioc = await this.iocRepository.findOne({ where: { id: iocId } });\r\n          \r\n          if (ioc) {\r\n            const enrichmentResult = await this.enrichIOC(ioc);\r\n            if (enrichmentResult.success) {\r\n              enrichedCount++;\r\n            }\r\n          }\r\n          \r\n          processedCount++;\r\n          \r\n          // Update progress\r\n          const progress = Math.round((processedCount / totalIOCs) * 100);\r\n          await job.progress(progress);\r\n          \r\n          // Rate limiting delay\r\n          await new Promise(resolve => setTimeout(resolve, 200));\r\n        } catch (error) {\r\n          this.loggerService.warn('Failed to enrich IOC', {\r\n            iocId,\r\n            error: error.message,\r\n          });\r\n          processedCount++;\r\n        }\r\n      }\r\n\r\n      this.logger.log('IOC enrichment completed', {\r\n        jobId: job.id,\r\n        totalIOCs,\r\n        processedCount,\r\n        enrichedCount,\r\n      });\r\n    } catch (error) {\r\n      this.loggerService.error('IOC enrichment failed', {\r\n        jobId: job.id,\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Process threat correlation analysis\r\n   */\r\n  @Process('threat-correlation')\r\n  async handleThreatCorrelation(job: Job<ThreatAnalysisJobData>): Promise<void> {\r\n    const { threatId, userId } = job.data;\r\n    \r\n    this.logger.debug('Processing threat correlation', {\r\n      jobId: job.id,\r\n      threatId,\r\n      userId,\r\n    });\r\n\r\n    try {\r\n      const correlations = await this.threatIntelligenceService.findRelatedThreats({\r\n        threatIntelligenceId: threatId,\r\n        correlationTypes: ['actor', 'campaign', 'indicators', 'techniques'],\r\n        timeWindow: 90,\r\n        confidenceThreshold: 0.6,\r\n      });\r\n\r\n      await job.progress(100);\r\n\r\n      this.logger.log('Threat correlation completed', {\r\n        jobId: job.id,\r\n        threatId,\r\n        correlationCount: correlations.length,\r\n      });\r\n    } catch (error) {\r\n      this.loggerService.error('Threat correlation failed', {\r\n        jobId: job.id,\r\n        threatId,\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handle job activation\r\n   */\r\n  @OnQueueActive()\r\n  onActive(job: Job) {\r\n    this.logger.debug('Threat analysis job started', {\r\n      jobId: job.id,\r\n      jobName: job.name,\r\n      analysisType: job.data.analysisType,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Handle job completion\r\n   */\r\n  @OnQueueCompleted()\r\n  onCompleted(job: Job, result: any) {\r\n    this.logger.log('Threat analysis job completed', {\r\n      jobId: job.id,\r\n      jobName: job.name,\r\n      duration: Date.now() - job.timestamp,\r\n      analysisType: job.data.analysisType,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Handle job failure\r\n   */\r\n  @OnQueueFailed()\r\n  onFailed(job: Job, error: Error) {\r\n    this.loggerService.error('Threat analysis job failed', {\r\n      jobId: job.id,\r\n      jobName: job.name,\r\n      error: error.message,\r\n      analysisType: job.data.analysisType,\r\n      attempts: job.attemptsMade,\r\n    });\r\n  }\r\n\r\n  // Private helper methods\r\n\r\n  private async analyzeAttributionFactors(threat: ThreatIntelligence): Promise<any> {\r\n    const factors = {\r\n      techniques: threat.mitreAttack?.techniques || [],\r\n      tactics: threat.mitreAttack?.tactics || [],\r\n      targetedSectors: threat.targetedSectors || [],\r\n      targetedCountries: threat.targetedCountries || [],\r\n      malwareFamily: threat.technicalDetails?.malwareFamily,\r\n      infrastructure: threat.technicalDetails?.infrastructure,\r\n      behaviorPatterns: threat.behavioralAnalysis,\r\n      indicators: threat.indicators?.map(ioc => ({\r\n        type: ioc.iocType,\r\n        value: ioc.value,\r\n      })) || [],\r\n    };\r\n\r\n    return factors;\r\n  }\r\n\r\n  private async findPotentialActors(factors: any): Promise<Array<{\r\n    actorId: string;\r\n    actorName: string;\r\n    confidence: number;\r\n    reasons: string[];\r\n  }>> {\r\n    const actors = await this.actorRepository.find({\r\n      where: { isActive: true },\r\n    });\r\n\r\n    const matches = [];\r\n\r\n    for (const actor of actors) {\r\n      const match = this.calculateActorMatch(actor, factors);\r\n      if (match.confidence > 0.3) {\r\n        matches.push({\r\n          actorId: actor.id,\r\n          actorName: actor.name,\r\n          confidence: match.confidence,\r\n          reasons: match.reasons,\r\n        });\r\n      }\r\n    }\r\n\r\n    return matches.sort((a, b) => b.confidence - a.confidence).slice(0, 5);\r\n  }\r\n\r\n  private calculateActorMatch(actor: ThreatActor, factors: any): { confidence: number; reasons: string[] } {\r\n    let score = 0;\r\n    const reasons = [];\r\n    const maxScore = 10;\r\n\r\n    // Check targeted sectors\r\n    if (factors.targetedSectors.length > 0 && actor.targetedSectors) {\r\n      const sectorMatch = factors.targetedSectors.filter(s => \r\n        actor.targetedSectors.includes(s)\r\n      ).length;\r\n      if (sectorMatch > 0) {\r\n        score += 2;\r\n        reasons.push(`Targets similar sectors (${sectorMatch} matches)`);\r\n      }\r\n    }\r\n\r\n    // Check MITRE techniques\r\n    if (factors.techniques.length > 0 && actor.attackPatterns) {\r\n      const techniqueMatch = factors.techniques.filter(t => \r\n        actor.attackPatterns.some(p => p.mitreId === t)\r\n      ).length;\r\n      if (techniqueMatch > 0) {\r\n        score += 3;\r\n        reasons.push(`Uses similar techniques (${techniqueMatch} matches)`);\r\n      }\r\n    }\r\n\r\n    // Check geographic targeting\r\n    if (factors.targetedCountries.length > 0 && actor.targetedCountries) {\r\n      const countryMatch = factors.targetedCountries.filter(c => \r\n        actor.targetedCountries.includes(c)\r\n      ).length;\r\n      if (countryMatch > 0) {\r\n        score += 2;\r\n        reasons.push(`Targets similar countries (${countryMatch} matches)`);\r\n      }\r\n    }\r\n\r\n    // Check tools and malware\r\n    if (factors.malwareFamily && actor.tools) {\r\n      const toolMatch = actor.tools.some(t => \r\n        t.name.toLowerCase().includes(factors.malwareFamily.toLowerCase())\r\n      );\r\n      if (toolMatch) {\r\n        score += 3;\r\n        reasons.push('Uses similar malware/tools');\r\n      }\r\n    }\r\n\r\n    return {\r\n      confidence: Math.min(score / maxScore, 1.0),\r\n      reasons,\r\n    };\r\n  }\r\n\r\n  private calculateAttributionConfidence(factors: any, suggestedActors: any[]): number {\r\n    if (suggestedActors.length === 0) return 0;\r\n    \r\n    const topActor = suggestedActors[0];\r\n    let confidence = topActor.confidence;\r\n\r\n    // Boost confidence if multiple factors align\r\n    if (topActor.reasons.length > 2) {\r\n      confidence += 0.1;\r\n    }\r\n\r\n    // Reduce confidence if there are competing actors with similar scores\r\n    if (suggestedActors.length > 1) {\r\n      const secondBest = suggestedActors[1];\r\n      if (topActor.confidence - secondBest.confidence < 0.2) {\r\n        confidence -= 0.15;\r\n      }\r\n    }\r\n\r\n    return Math.min(Math.max(confidence, 0), 1.0);\r\n  }\r\n\r\n  private async calculateScoringFactors(threat: ThreatIntelligence): Promise<Array<{\r\n    factor: string;\r\n    weight: number;\r\n    value: number;\r\n    impact: number;\r\n  }>> {\r\n    const factors = [];\r\n\r\n    // Severity factor\r\n    const severityValues = { critical: 10, high: 7, medium: 5, low: 3, info: 1 };\r\n    factors.push({\r\n      factor: 'severity',\r\n      weight: 0.25,\r\n      value: severityValues[threat.severity],\r\n      impact: severityValues[threat.severity] * 0.25,\r\n    });\r\n\r\n    // Confidence factor\r\n    const confidenceValues = { high: 10, medium: 7, low: 4, unknown: 2 };\r\n    factors.push({\r\n      factor: 'confidence',\r\n      weight: 0.2,\r\n      value: confidenceValues[threat.confidence],\r\n      impact: confidenceValues[threat.confidence] * 0.2,\r\n    });\r\n\r\n    // Attribution factor\r\n    const attributionValue = threat.isAttributed ? 8 : 4;\r\n    factors.push({\r\n      factor: 'attribution',\r\n      weight: 0.15,\r\n      value: attributionValue,\r\n      impact: attributionValue * 0.15,\r\n    });\r\n\r\n    // Observation frequency\r\n    const observationValue = Math.min(threat.observationCount / 5, 10);\r\n    factors.push({\r\n      factor: 'observation_frequency',\r\n      weight: 0.1,\r\n      value: observationValue,\r\n      impact: observationValue * 0.1,\r\n    });\r\n\r\n    // Recency factor\r\n    const daysSinceLastSeen = threat.lastSeen ? \r\n      Math.floor((Date.now() - threat.lastSeen.getTime()) / (1000 * 60 * 60 * 24)) : \r\n      Math.floor((Date.now() - threat.firstSeen.getTime()) / (1000 * 60 * 60 * 24));\r\n    \r\n    const recencyValue = daysSinceLastSeen <= 7 ? 10 : \r\n                        daysSinceLastSeen <= 30 ? 7 : \r\n                        daysSinceLastSeen <= 90 ? 4 : 2;\r\n    factors.push({\r\n      factor: 'recency',\r\n      weight: 0.15,\r\n      value: recencyValue,\r\n      impact: recencyValue * 0.15,\r\n    });\r\n\r\n    // IOC count factor\r\n    const iocValue = Math.min((threat.indicators?.length || 0) / 3, 10);\r\n    factors.push({\r\n      factor: 'ioc_count',\r\n      weight: 0.1,\r\n      value: iocValue,\r\n      impact: iocValue * 0.1,\r\n    });\r\n\r\n    // Critical sector targeting\r\n    const criticalSectors = ['financial', 'healthcare', 'energy', 'government', 'defense'];\r\n    const targetsCritical = threat.targetedSectors?.some(s => \r\n      criticalSectors.includes(s.toLowerCase())\r\n    );\r\n    const criticalValue = targetsCritical ? 8 : 5;\r\n    factors.push({\r\n      factor: 'critical_targeting',\r\n      weight: 0.05,\r\n      value: criticalValue,\r\n      impact: criticalValue * 0.05,\r\n    });\r\n\r\n    return factors;\r\n  }\r\n\r\n  private calculateWeightedScore(factors: Array<{ factor: string; weight: number; value: number; impact: number }>): number {\r\n    const totalImpact = factors.reduce((sum, f) => sum + f.impact, 0);\r\n    return Math.min(totalImpact, 10.0);\r\n  }\r\n\r\n  private async enrichIOC(ioc: IndicatorOfCompromise): Promise<{ success: boolean; error?: string }> {\r\n    try {\r\n      // Placeholder for IOC enrichment logic\r\n      // In a real implementation, this would call external APIs for enrichment\r\n      \r\n      const enrichmentData = {\r\n        source: 'automated_analysis',\r\n        enrichedAt: new Date(),\r\n        data: {\r\n          analyzed: true,\r\n          timestamp: new Date(),\r\n        },\r\n      };\r\n\r\n      ioc.addEnrichment('automated_analysis', enrichmentData.data, 0.7);\r\n      await this.iocRepository.save(ioc);\r\n\r\n      return { success: true };\r\n    } catch (error) {\r\n      return { success: false, error: error.message };\r\n    }\r\n  }\r\n\r\n  private async sendScoreChangeNotification(\r\n    threat: ThreatIntelligence,\r\n    oldScore: number,\r\n    newScore: number,\r\n    userId?: string,\r\n  ): Promise<void> {\r\n    try {\r\n      const change = newScore - oldScore;\r\n      const changeType = change > 0 ? 'increased' : 'decreased';\r\n      \r\n      const notification = {\r\n        title: 'Threat Score Updated',\r\n        message: `Threat \"${threat.title}\" score ${changeType} from ${oldScore.toFixed(1)} to ${newScore.toFixed(1)}`,\r\n        type: change > 2 ? 'warning' as const : 'info' as const,\r\n        data: {\r\n          threatId: threat.id,\r\n          oldScore,\r\n          newScore,\r\n          change,\r\n        },\r\n      };\r\n\r\n      if (userId) {\r\n        await this.notificationService.sendUserNotification({\r\n          userId,\r\n          ...notification,\r\n        });\r\n      } else {\r\n        await this.notificationService.sendRoleNotification({\r\n          roles: ['threat_analyst', 'security_analyst'],\r\n          ...notification,\r\n        });\r\n      }\r\n    } catch (error) {\r\n      this.loggerService.error('Failed to send score change notification', {\r\n        threatId: threat.id,\r\n        error: error.message,\r\n      });\r\n    }\r\n  }\r\n}\r\n"], "version": 3}