{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\database\\seeds\\seed-runner.service.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,6CAAmD;AACnD,qCAAqC;AACrC,2CAA+C;AAC/C,yDAAuD;AACvD,2CAAyC;AACzC,uDAAqD;AAErD;;;GAGG;AAEI,IAAM,iBAAiB,yBAAvB,MAAM,iBAAiB;IAK5B,YACsB,UAAsB,EAC1C,aAA4B;QANb,WAAM,GAAG,IAAI,eAAM,CAAC,mBAAiB,CAAC,IAAI,CAAC,CAAC;QAQ3D,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,WAAoB;QACjC,MAAM,GAAG,GAAG,WAAW,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,UAAU,EAAE,aAAa,CAAC,CAAC;QAErF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kCAAkC,GAAG,EAAE,CAAC,CAAC;QAEzD,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAErC,QAAQ,GAAG,EAAE,CAAC;gBACZ,KAAK,aAAa;oBAChB,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBACjC,MAAM;gBACR,KAAK,MAAM;oBACT,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;oBAC1B,MAAM;gBACR,KAAK,YAAY;oBACf,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAChC,MAAM;gBACR;oBACE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,GAAG,6BAA6B,CAAC,CAAC;oBAC3E,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACrC,CAAC;YAED,MAAM,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;YACpC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iDAAiD,GAAG,EAAE,CAAC,CAAC;QAE1E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0CAA0C,GAAG,EAAE,EAAE;gBACjE,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB;QAC/B,IAAI,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,EAAE,CAAC;YAC7C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;YACnE,OAAO;QACT,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,oCAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACtD,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY;QACxB,MAAM,MAAM,GAAG,IAAI,sBAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE/C,kCAAkC;QAClC,IAAI,CAAC;YACH,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,qCAAqC;YACrC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6CAA6C,EAAE;gBAC/D,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;QACL,CAAC;QAED,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB;QAC9B,IAAI,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE,CAAC;YAC5C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;YAClE,OAAO;QACT,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,kCAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACrD,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;QAEpB,mCAAmC;QACnC,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,EAAE,CAAC;QACtC,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,WAAoB;QACtC,MAAM,GAAG,GAAG,WAAW,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,UAAU,EAAE,aAAa,CAAC,CAAC;QAErF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wCAAwC,GAAG,EAAE,CAAC,CAAC;QAE/D,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACrC,MAAM,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;YAEnC,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAE3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gDAAgD,GAAG,EAAE,EAAE;gBACvE,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe;QACnB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QAE5C,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,sBAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC/C,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;YAEvB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;gBAC5C,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa;QACjB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAErC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;OAI3C,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG;gBACb,YAAY,EAAE,CAAC,aAAa,EAAE,MAAM,EAAE,YAAY,CAAC;gBACnD,UAAU,EAAE,OAAO;gBACnB,mBAAmB,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,UAAU,EAAE,aAAa,CAAC;aAC/E,CAAC;YAEF,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;gBAC7C,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,OAAO;gBACL,KAAK,EAAE,gCAAgC;gBACvC,YAAY,EAAE,CAAC,aAAa,EAAE,MAAM,EAAE,YAAY,CAAC;gBACnD,UAAU,EAAE,EAAE;gBACd,mBAAmB,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,UAAU,EAAE,aAAa,CAAC;aAC/E,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB;QACnC,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;;;;;KAU3B,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc,CAAC,WAAmB;QAC9C,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;OAI1C,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;YAElB,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACvC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,6DAA6D;YAC7D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,WAAmB,EAAE,UAAmB,IAAI,EAAE,YAAqB,EAAE,aAAsB;QAC3H,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;;;OAQ3B,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC,CAAC;QAC1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE;gBACnD,WAAW;gBACX,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,4DAA4D;QAC9D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,WAAmB;QAClD,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;OAE3B,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE;gBACzD,WAAW;gBACX,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;QAEjD,IAAI,CAAC;YACH,MAAM,cAAc,GAAG;gBACrB,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,gBAAgB,EAAE,YAAY;gBAC9D,iBAAiB,EAAE,qBAAqB,EAAE,gCAAgC;gBAC1E,yBAAyB,EAAE,4BAA4B;gBACvD,QAAQ,EAAE,iBAAiB,EAAE,uBAAuB,EAAE,qBAAqB;aAC5E,CAAC;YAEF,KAAK,MAAM,KAAK,IAAI,cAAc,EAAE,CAAC;gBACnC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;SAI1C,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;gBAEZ,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;oBACpC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,KAAK,aAAa,CAAC,CAAC;oBACzD,OAAO,KAAK,CAAC;gBACf,CAAC;YACH,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;YACrD,OAAO,IAAI,CAAC;QAEd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE;gBACrD,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB;QACpB,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAC9B,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,qCAAqC,CAAC;gBAC5D,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,qCAAqC,CAAC;gBAC5D,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,sCAAsC,CAAC;gBAC7D,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,+CAA+C,CAAC;gBACtE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,+CAA+C,CAAC;gBACtE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,mDAAmD,CAAC;aAC3E,CAAC,CAAC;YAEH,OAAO;gBACL,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;gBAClC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;gBAClC,MAAM,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;gBACnC,eAAe,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;gBAC5C,eAAe,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;gBAC5C,mBAAmB,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;gBAChD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE;gBACrD,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,OAAO;gBACL,KAAK,EAAE,wCAAwC;gBAC/C,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AArUY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;IAOR,WAAA,IAAA,0BAAgB,GAAE,CAAA;yDAAa,oBAAU,oBAAV,oBAAU,oDAC3B,sBAAa,oBAAb,sBAAa;GAPnB,iBAAiB,CAqU7B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\database\\seeds\\seed-runner.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { InjectDataSource } from '@nestjs/typeorm';\r\nimport { DataSource } from 'typeorm';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { DevelopmentSeeder } from './development.seed';\r\nimport { TestSeeder } from './test.seed';\r\nimport { ProductionSeeder } from './production.seed';\r\n\r\n/**\r\n * Seed runner service for managing database seeding operations\r\n * Handles environment-specific seeding and seed execution tracking\r\n */\r\n@Injectable()\r\nexport class SeedRunnerService {\r\n  private readonly logger = new Logger(SeedRunnerService.name);\r\n  private readonly dataSource: DataSource;\r\n  private readonly configService: ConfigService;\r\n\r\n  constructor(\r\n    @InjectDataSource() dataSource: DataSource,\r\n    configService: ConfigService,\r\n  ) {\r\n    this.dataSource = dataSource;\r\n    this.configService = configService;\r\n  }\r\n\r\n  /**\r\n   * Run seeds for the current environment\r\n   */\r\n  async runSeeds(environment?: string): Promise<void> {\r\n    const env = environment || this.configService.get<string>('NODE_ENV', 'development');\r\n    \r\n    this.logger.log(`Running seeds for environment: ${env}`);\r\n\r\n    try {\r\n      await this.createSeedTrackingTable();\r\n\r\n      switch (env) {\r\n        case 'development':\r\n          await this.runDevelopmentSeeds();\r\n          break;\r\n        case 'test':\r\n          await this.runTestSeeds();\r\n          break;\r\n        case 'production':\r\n          await this.runProductionSeeds();\r\n          break;\r\n        default:\r\n          this.logger.warn(`Unknown environment: ${env}, running development seeds`);\r\n          await this.runDevelopmentSeeds();\r\n      }\r\n\r\n      await this.recordSeedExecution(env);\r\n      this.logger.log(`Seeds completed successfully for environment: ${env}`);\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Seed execution failed for environment: ${env}`, {\r\n        error: error.message,\r\n        stack: error.stack,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Run development environment seeds\r\n   */\r\n  private async runDevelopmentSeeds(): Promise<void> {\r\n    if (await this.isSeedExecuted('development')) {\r\n      this.logger.log('Development seeds already executed, skipping...');\r\n      return;\r\n    }\r\n\r\n    const seeder = new DevelopmentSeeder(this.dataSource);\r\n    await seeder.seed();\r\n  }\r\n\r\n  /**\r\n   * Run test environment seeds\r\n   */\r\n  private async runTestSeeds(): Promise<void> {\r\n    const seeder = new TestSeeder(this.dataSource);\r\n    \r\n    // Always clean up test data first\r\n    try {\r\n      await seeder.cleanup();\r\n    } catch (error) {\r\n      // Ignore cleanup errors on first run\r\n      this.logger.debug('Test cleanup failed (expected on first run)', {\r\n        error: error.message,\r\n      });\r\n    }\r\n\r\n    await seeder.seed();\r\n  }\r\n\r\n  /**\r\n   * Run production environment seeds\r\n   */\r\n  private async runProductionSeeds(): Promise<void> {\r\n    if (await this.isSeedExecuted('production')) {\r\n      this.logger.log('Production seeds already executed, skipping...');\r\n      return;\r\n    }\r\n\r\n    const seeder = new ProductionSeeder(this.dataSource);\r\n    await seeder.seed();\r\n\r\n    // Verify production seed integrity\r\n    const isValid = await seeder.verify();\r\n    if (!isValid) {\r\n      throw new Error('Production seed integrity verification failed');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Force re-run seeds (ignoring previous execution)\r\n   */\r\n  async forceRunSeeds(environment?: string): Promise<void> {\r\n    const env = environment || this.configService.get<string>('NODE_ENV', 'development');\r\n    \r\n    this.logger.log(`Force running seeds for environment: ${env}`);\r\n\r\n    try {\r\n      await this.createSeedTrackingTable();\r\n      await this.clearSeedExecution(env);\r\n\r\n      await this.runSeeds(env);\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Force seed execution failed for environment: ${env}`, {\r\n        error: error.message,\r\n        stack: error.stack,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Clean up test data\r\n   */\r\n  async cleanupTestData(): Promise<void> {\r\n    this.logger.log('Cleaning up test data...');\r\n\r\n    try {\r\n      const seeder = new TestSeeder(this.dataSource);\r\n      await seeder.cleanup();\r\n      \r\n      this.logger.log('Test data cleanup completed successfully');\r\n    } catch (error) {\r\n      this.logger.error('Test data cleanup failed', {\r\n        error: error.message,\r\n        stack: error.stack,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get seed execution status\r\n   */\r\n  async getSeedStatus(): Promise<Record<string, any>> {\r\n    try {\r\n      await this.createSeedTrackingTable();\r\n\r\n      const results = await this.dataSource.query(`\r\n        SELECT environment, executed_at, success\r\n        FROM seed_executions\r\n        ORDER BY executed_at DESC\r\n      `);\r\n\r\n      const status = {\r\n        environments: ['development', 'test', 'production'],\r\n        executions: results,\r\n        current_environment: this.configService.get<string>('NODE_ENV', 'development'),\r\n      };\r\n\r\n      return status;\r\n\r\n    } catch (error) {\r\n      this.logger.error('Failed to get seed status', {\r\n        error: error.message,\r\n      });\r\n      return {\r\n        error: 'Failed to retrieve seed status',\r\n        environments: ['development', 'test', 'production'],\r\n        executions: [],\r\n        current_environment: this.configService.get<string>('NODE_ENV', 'development'),\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create seed tracking table if it doesn't exist\r\n   */\r\n  private async createSeedTrackingTable(): Promise<void> {\r\n    await this.dataSource.query(`\r\n      CREATE TABLE IF NOT EXISTS seed_executions (\r\n        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\r\n        environment VARCHAR(50) NOT NULL,\r\n        executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,\r\n        success BOOLEAN NOT NULL DEFAULT true,\r\n        error_message TEXT,\r\n        execution_time_ms INTEGER,\r\n        UNIQUE(environment)\r\n      )\r\n    `);\r\n  }\r\n\r\n  /**\r\n   * Check if seeds have been executed for an environment\r\n   */\r\n  private async isSeedExecuted(environment: string): Promise<boolean> {\r\n    try {\r\n      const result = await this.dataSource.query(`\r\n        SELECT COUNT(*) as count \r\n        FROM seed_executions \r\n        WHERE environment = $1 AND success = true\r\n      `, [environment]);\r\n\r\n      return parseInt(result[0].count) > 0;\r\n    } catch (error) {\r\n      // If table doesn't exist or query fails, assume not executed\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Record seed execution\r\n   */\r\n  private async recordSeedExecution(environment: string, success: boolean = true, errorMessage?: string, executionTime?: number): Promise<void> {\r\n    try {\r\n      await this.dataSource.query(`\r\n        INSERT INTO seed_executions (environment, success, error_message, execution_time_ms)\r\n        VALUES ($1, $2, $3, $4)\r\n        ON CONFLICT (environment) DO UPDATE SET\r\n          executed_at = CURRENT_TIMESTAMP,\r\n          success = EXCLUDED.success,\r\n          error_message = EXCLUDED.error_message,\r\n          execution_time_ms = EXCLUDED.execution_time_ms\r\n      `, [environment, success, errorMessage, executionTime]);\r\n    } catch (error) {\r\n      this.logger.error('Failed to record seed execution', {\r\n        environment,\r\n        error: error.message,\r\n      });\r\n      // Don't throw here to avoid masking the original seed error\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Clear seed execution record\r\n   */\r\n  private async clearSeedExecution(environment: string): Promise<void> {\r\n    try {\r\n      await this.dataSource.query(`\r\n        DELETE FROM seed_executions WHERE environment = $1\r\n      `, [environment]);\r\n    } catch (error) {\r\n      this.logger.error('Failed to clear seed execution record', {\r\n        environment,\r\n        error: error.message,\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate database schema before seeding\r\n   */\r\n  async validateSchema(): Promise<boolean> {\r\n    this.logger.log('Validating database schema...');\r\n\r\n    try {\r\n      const requiredTables = [\r\n        'users', 'roles', 'user_roles', 'refresh_tokens', 'audit_logs',\r\n        'security_events', 'security_event_tags', 'security_event_tag_assignments',\r\n        'security_event_comments', 'security_event_attachments',\r\n        'assets', 'vulnerabilities', 'asset_vulnerabilities', 'vulnerability_scans'\r\n      ];\r\n\r\n      for (const table of requiredTables) {\r\n        const result = await this.dataSource.query(`\r\n          SELECT COUNT(*) as count\r\n          FROM information_schema.tables\r\n          WHERE table_schema = 'public' AND table_name = $1\r\n        `, [table]);\r\n\r\n        if (parseInt(result[0].count) === 0) {\r\n          this.logger.error(`Required table '${table}' not found`);\r\n          return false;\r\n        }\r\n      }\r\n\r\n      this.logger.log('Database schema validation passed');\r\n      return true;\r\n\r\n    } catch (error) {\r\n      this.logger.error('Database schema validation failed', {\r\n        error: error.message,\r\n      });\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get database statistics after seeding\r\n   */\r\n  async getDatabaseStats(): Promise<Record<string, any>> {\r\n    try {\r\n      const stats = await Promise.all([\r\n        this.dataSource.query('SELECT COUNT(*) as count FROM users'),\r\n        this.dataSource.query('SELECT COUNT(*) as count FROM roles'),\r\n        this.dataSource.query('SELECT COUNT(*) as count FROM assets'),\r\n        this.dataSource.query('SELECT COUNT(*) as count FROM vulnerabilities'),\r\n        this.dataSource.query('SELECT COUNT(*) as count FROM security_events'),\r\n        this.dataSource.query('SELECT COUNT(*) as count FROM security_event_tags'),\r\n      ]);\r\n\r\n      return {\r\n        users: parseInt(stats[0][0].count),\r\n        roles: parseInt(stats[1][0].count),\r\n        assets: parseInt(stats[2][0].count),\r\n        vulnerabilities: parseInt(stats[3][0].count),\r\n        security_events: parseInt(stats[4][0].count),\r\n        security_event_tags: parseInt(stats[5][0].count),\r\n        timestamp: new Date().toISOString(),\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error('Failed to get database statistics', {\r\n        error: error.message,\r\n      });\r\n      return {\r\n        error: 'Failed to retrieve database statistics',\r\n        timestamp: new Date().toISOString(),\r\n      };\r\n    }\r\n  }\r\n}"], "version": 3}