40ae7f47e9ec5c4244fd355e70391cb2
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventSourceTypeUtils = exports.EventSourceType = void 0;
/**
 * Event Source Type Enum
 *
 * Represents the various types of sources that can generate security events.
 * Used to categorize and route events based on their origin.
 */
var EventSourceType;
(function (EventSourceType) {
    /**
     * Network Infrastructure Sources
     */
    /** Network firewalls and security appliances */
    EventSourceType["FIREWALL"] = "firewall";
    /** Intrusion Detection/Prevention Systems */
    EventSourceType["IDS_IPS"] = "ids_ips";
    /** Network routers and switches */
    EventSourceType["NETWORK_DEVICE"] = "network_device";
    /** Load balancers and proxies */
    EventSourceType["LOAD_BALANCER"] = "load_balancer";
    /** VPN concentrators and gateways */
    EventSourceType["VPN_GATEWAY"] = "vpn_gateway";
    /** Network access control systems */
    EventSourceType["NAC"] = "nac";
    /** DNS servers and security services */
    EventSourceType["DNS_SERVER"] = "dns_server";
    /** DHCP servers */
    EventSourceType["DHCP_SERVER"] = "dhcp_server";
    /**
     * Endpoint Sources
     */
    /** Endpoint Detection and Response systems */
    EventSourceType["EDR"] = "edr";
    /** Antivirus and anti-malware solutions */
    EventSourceType["ANTIVIRUS"] = "antivirus";
    /** Host-based Intrusion Detection Systems */
    EventSourceType["HIDS"] = "hids";
    /** Operating system logs (Windows, Linux, macOS) */
    EventSourceType["OPERATING_SYSTEM"] = "operating_system";
    /** Mobile device management systems */
    EventSourceType["MDM"] = "mdm";
    /** USB and removable media controls */
    EventSourceType["USB_CONTROL"] = "usb_control";
    /**
     * Application Sources
     */
    /** Web application firewalls */
    EventSourceType["WAF"] = "waf";
    /** Application performance monitoring */
    EventSourceType["APM"] = "apm";
    /** Database activity monitoring */
    EventSourceType["DATABASE"] = "database";
    /** Web servers (Apache, Nginx, IIS) */
    EventSourceType["WEB_SERVER"] = "web_server";
    /** Application servers */
    EventSourceType["APPLICATION_SERVER"] = "application_server";
    /** Custom applications */
    EventSourceType["CUSTOM_APPLICATION"] = "custom_application";
    /** API gateways and management platforms */
    EventSourceType["API_GATEWAY"] = "api_gateway";
    /**
     * Cloud Sources
     */
    /** Amazon Web Services */
    EventSourceType["AWS"] = "aws";
    /** Microsoft Azure */
    EventSourceType["AZURE"] = "azure";
    /** Google Cloud Platform */
    EventSourceType["GCP"] = "gcp";
    /** Cloud Access Security Brokers */
    EventSourceType["CASB"] = "casb";
    /** Cloud workload protection platforms */
    EventSourceType["CWPP"] = "cwpp";
    /** Container security platforms */
    EventSourceType["CONTAINER_SECURITY"] = "container_security";
    /** Kubernetes security */
    EventSourceType["KUBERNETES"] = "kubernetes";
    /**
     * Identity and Access Management
     */
    /** Active Directory and LDAP */
    EventSourceType["DIRECTORY_SERVICE"] = "directory_service";
    /** Single Sign-On systems */
    EventSourceType["SSO"] = "sso";
    /** Multi-factor authentication systems */
    EventSourceType["MFA"] = "mfa";
    /** Privileged Access Management */
    EventSourceType["PAM"] = "pam";
    /** Identity governance and administration */
    EventSourceType["IGA"] = "iga";
    /**
     * Security Tools
     */
    /** Security Information and Event Management */
    EventSourceType["SIEM"] = "siem";
    /** Security Orchestration, Automation and Response */
    EventSourceType["SOAR"] = "soar";
    /** Vulnerability scanners */
    EventSourceType["VULNERABILITY_SCANNER"] = "vulnerability_scanner";
    /** Threat intelligence platforms */
    EventSourceType["THREAT_INTELLIGENCE"] = "threat_intelligence";
    /** Deception technology */
    EventSourceType["DECEPTION"] = "deception";
    /** Sandbox analysis systems */
    EventSourceType["SANDBOX"] = "sandbox";
    /** Digital forensics tools */
    EventSourceType["FORENSICS"] = "forensics";
    /**
     * Email and Communication
     */
    /** Email security gateways */
    EventSourceType["EMAIL_SECURITY"] = "email_security";
    /** Data Loss Prevention systems */
    EventSourceType["DLP"] = "dlp";
    /** Unified communications security */
    EventSourceType["UC_SECURITY"] = "uc_security";
    /**
     * Physical Security
     */
    /** Physical access control systems */
    EventSourceType["PHYSICAL_ACCESS"] = "physical_access";
    /** Video surveillance systems */
    EventSourceType["CCTV"] = "cctv";
    /** Environmental monitoring */
    EventSourceType["ENVIRONMENTAL"] = "environmental";
    /**
     * IoT and OT
     */
    /** Internet of Things devices */
    EventSourceType["IOT_DEVICE"] = "iot_device";
    /** Operational Technology systems */
    EventSourceType["OT_SYSTEM"] = "ot_system";
    /** Industrial Control Systems */
    EventSourceType["ICS"] = "ics";
    /** SCADA systems */
    EventSourceType["SCADA"] = "scada";
    /**
     * External Sources
     */
    /** External threat feeds */
    EventSourceType["EXTERNAL_FEED"] = "external_feed";
    /** Third-party security services */
    EventSourceType["THIRD_PARTY"] = "third_party";
    /** Government and law enforcement alerts */
    EventSourceType["GOVERNMENT"] = "government";
    /** Industry sharing groups */
    EventSourceType["INDUSTRY_SHARING"] = "industry_sharing";
    /**
     * Internal Sources
     */
    /** Manual event creation by analysts */
    EventSourceType["MANUAL"] = "manual";
    /** Automated internal processes */
    EventSourceType["INTERNAL_AUTOMATION"] = "internal_automation";
    /** Compliance and audit systems */
    EventSourceType["COMPLIANCE"] = "compliance";
    /** Risk management systems */
    EventSourceType["RISK_MANAGEMENT"] = "risk_management";
    /**
     * Unknown or Other
     */
    /** Unknown or unidentified source */
    EventSourceType["UNKNOWN"] = "unknown";
    /** Other sources not categorized above */
    EventSourceType["OTHER"] = "other";
})(EventSourceType || (exports.EventSourceType = EventSourceType = {}));
/**
 * Event Source Type Utilities
 */
class EventSourceTypeUtils {
    /**
     * Get all source types
     */
    static getAllSourceTypes() {
        return Object.values(EventSourceType);
    }
    /**
     * Get network infrastructure source types
     */
    static getNetworkSourceTypes() {
        return [
            EventSourceType.FIREWALL,
            EventSourceType.IDS_IPS,
            EventSourceType.NETWORK_DEVICE,
            EventSourceType.LOAD_BALANCER,
            EventSourceType.VPN_GATEWAY,
            EventSourceType.NAC,
            EventSourceType.DNS_SERVER,
            EventSourceType.DHCP_SERVER,
        ];
    }
    /**
     * Get endpoint source types
     */
    static getEndpointSourceTypes() {
        return [
            EventSourceType.EDR,
            EventSourceType.ANTIVIRUS,
            EventSourceType.HIDS,
            EventSourceType.OPERATING_SYSTEM,
            EventSourceType.MDM,
            EventSourceType.USB_CONTROL,
        ];
    }
    /**
     * Get application source types
     */
    static getApplicationSourceTypes() {
        return [
            EventSourceType.WAF,
            EventSourceType.APM,
            EventSourceType.DATABASE,
            EventSourceType.WEB_SERVER,
            EventSourceType.APPLICATION_SERVER,
            EventSourceType.CUSTOM_APPLICATION,
            EventSourceType.API_GATEWAY,
        ];
    }
    /**
     * Get cloud source types
     */
    static getCloudSourceTypes() {
        return [
            EventSourceType.AWS,
            EventSourceType.AZURE,
            EventSourceType.GCP,
            EventSourceType.CASB,
            EventSourceType.CWPP,
            EventSourceType.CONTAINER_SECURITY,
            EventSourceType.KUBERNETES,
        ];
    }
    /**
     * Get identity and access management source types
     */
    static getIdentitySourceTypes() {
        return [
            EventSourceType.DIRECTORY_SERVICE,
            EventSourceType.SSO,
            EventSourceType.MFA,
            EventSourceType.PAM,
            EventSourceType.IGA,
        ];
    }
    /**
     * Get security tool source types
     */
    static getSecurityToolSourceTypes() {
        return [
            EventSourceType.SIEM,
            EventSourceType.SOAR,
            EventSourceType.VULNERABILITY_SCANNER,
            EventSourceType.THREAT_INTELLIGENCE,
            EventSourceType.DECEPTION,
            EventSourceType.SANDBOX,
            EventSourceType.FORENSICS,
        ];
    }
    /**
     * Get external source types
     */
    static getExternalSourceTypes() {
        return [
            EventSourceType.EXTERNAL_FEED,
            EventSourceType.THIRD_PARTY,
            EventSourceType.GOVERNMENT,
            EventSourceType.INDUSTRY_SHARING,
        ];
    }
    /**
     * Check if source type is network-related
     */
    static isNetworkSource(sourceType) {
        return EventSourceTypeUtils.getNetworkSourceTypes().includes(sourceType);
    }
    /**
     * Check if source type is endpoint-related
     */
    static isEndpointSource(sourceType) {
        return EventSourceTypeUtils.getEndpointSourceTypes().includes(sourceType);
    }
    /**
     * Check if source type is cloud-related
     */
    static isCloudSource(sourceType) {
        return EventSourceTypeUtils.getCloudSourceTypes().includes(sourceType);
    }
    /**
     * Check if source type is external
     */
    static isExternalSource(sourceType) {
        return EventSourceTypeUtils.getExternalSourceTypes().includes(sourceType);
    }
    /**
     * Check if source type is a security tool
     */
    static isSecurityTool(sourceType) {
        return EventSourceTypeUtils.getSecurityToolSourceTypes().includes(sourceType);
    }
    /**
     * Get category for a source type
     */
    static getCategory(sourceType) {
        if (EventSourceTypeUtils.isNetworkSource(sourceType))
            return 'Network';
        if (EventSourceTypeUtils.isEndpointSource(sourceType))
            return 'Endpoint';
        if (EventSourceTypeUtils.getApplicationSourceTypes().includes(sourceType))
            return 'Application';
        if (EventSourceTypeUtils.isCloudSource(sourceType))
            return 'Cloud';
        if (EventSourceTypeUtils.getIdentitySourceTypes().includes(sourceType))
            return 'Identity';
        if (EventSourceTypeUtils.isSecurityTool(sourceType))
            return 'Security Tool';
        if ([EventSourceType.EMAIL_SECURITY, EventSourceType.DLP, EventSourceType.UC_SECURITY].includes(sourceType))
            return 'Communication';
        if ([EventSourceType.PHYSICAL_ACCESS, EventSourceType.CCTV, EventSourceType.ENVIRONMENTAL].includes(sourceType))
            return 'Physical';
        if ([EventSourceType.IOT_DEVICE, EventSourceType.OT_SYSTEM, EventSourceType.ICS, EventSourceType.SCADA].includes(sourceType))
            return 'IoT/OT';
        if (EventSourceTypeUtils.isExternalSource(sourceType))
            return 'External';
        if ([EventSourceType.MANUAL, EventSourceType.INTERNAL_AUTOMATION, EventSourceType.COMPLIANCE, EventSourceType.RISK_MANAGEMENT].includes(sourceType))
            return 'Internal';
        return 'Other';
    }
    /**
     * Get human-readable description
     */
    static getDescription(sourceType) {
        const descriptions = {
            [EventSourceType.FIREWALL]: 'Network firewall and security appliance',
            [EventSourceType.IDS_IPS]: 'Intrusion Detection/Prevention System',
            [EventSourceType.NETWORK_DEVICE]: 'Network router, switch, or infrastructure device',
            [EventSourceType.LOAD_BALANCER]: 'Load balancer or proxy server',
            [EventSourceType.VPN_GATEWAY]: 'VPN concentrator or gateway',
            [EventSourceType.NAC]: 'Network Access Control system',
            [EventSourceType.DNS_SERVER]: 'DNS server or security service',
            [EventSourceType.DHCP_SERVER]: 'DHCP server',
            [EventSourceType.EDR]: 'Endpoint Detection and Response system',
            [EventSourceType.ANTIVIRUS]: 'Antivirus or anti-malware solution',
            [EventSourceType.HIDS]: 'Host-based Intrusion Detection System',
            [EventSourceType.OPERATING_SYSTEM]: 'Operating system logs and events',
            [EventSourceType.MDM]: 'Mobile Device Management system',
            [EventSourceType.USB_CONTROL]: 'USB and removable media control',
            [EventSourceType.WAF]: 'Web Application Firewall',
            [EventSourceType.APM]: 'Application Performance Monitoring',
            [EventSourceType.DATABASE]: 'Database activity monitoring',
            [EventSourceType.WEB_SERVER]: 'Web server (Apache, Nginx, IIS)',
            [EventSourceType.APPLICATION_SERVER]: 'Application server',
            [EventSourceType.CUSTOM_APPLICATION]: 'Custom application',
            [EventSourceType.API_GATEWAY]: 'API gateway or management platform',
            [EventSourceType.AWS]: 'Amazon Web Services',
            [EventSourceType.AZURE]: 'Microsoft Azure',
            [EventSourceType.GCP]: 'Google Cloud Platform',
            [EventSourceType.CASB]: 'Cloud Access Security Broker',
            [EventSourceType.CWPP]: 'Cloud Workload Protection Platform',
            [EventSourceType.CONTAINER_SECURITY]: 'Container security platform',
            [EventSourceType.KUBERNETES]: 'Kubernetes security',
            [EventSourceType.DIRECTORY_SERVICE]: 'Active Directory or LDAP service',
            [EventSourceType.SSO]: 'Single Sign-On system',
            [EventSourceType.MFA]: 'Multi-Factor Authentication system',
            [EventSourceType.PAM]: 'Privileged Access Management',
            [EventSourceType.IGA]: 'Identity Governance and Administration',
            [EventSourceType.SIEM]: 'Security Information and Event Management',
            [EventSourceType.SOAR]: 'Security Orchestration, Automation and Response',
            [EventSourceType.VULNERABILITY_SCANNER]: 'Vulnerability scanner',
            [EventSourceType.THREAT_INTELLIGENCE]: 'Threat intelligence platform',
            [EventSourceType.DECEPTION]: 'Deception technology',
            [EventSourceType.SANDBOX]: 'Sandbox analysis system',
            [EventSourceType.FORENSICS]: 'Digital forensics tool',
            [EventSourceType.EMAIL_SECURITY]: 'Email security gateway',
            [EventSourceType.DLP]: 'Data Loss Prevention system',
            [EventSourceType.UC_SECURITY]: 'Unified Communications security',
            [EventSourceType.PHYSICAL_ACCESS]: 'Physical access control system',
            [EventSourceType.CCTV]: 'Video surveillance system',
            [EventSourceType.ENVIRONMENTAL]: 'Environmental monitoring system',
            [EventSourceType.IOT_DEVICE]: 'Internet of Things device',
            [EventSourceType.OT_SYSTEM]: 'Operational Technology system',
            [EventSourceType.ICS]: 'Industrial Control System',
            [EventSourceType.SCADA]: 'SCADA system',
            [EventSourceType.EXTERNAL_FEED]: 'External threat feed',
            [EventSourceType.THIRD_PARTY]: 'Third-party security service',
            [EventSourceType.GOVERNMENT]: 'Government or law enforcement alert',
            [EventSourceType.INDUSTRY_SHARING]: 'Industry sharing group',
            [EventSourceType.MANUAL]: 'Manual event creation',
            [EventSourceType.INTERNAL_AUTOMATION]: 'Internal automated process',
            [EventSourceType.COMPLIANCE]: 'Compliance and audit system',
            [EventSourceType.RISK_MANAGEMENT]: 'Risk management system',
            [EventSourceType.UNKNOWN]: 'Unknown or unidentified source',
            [EventSourceType.OTHER]: 'Other source type',
        };
        return descriptions[sourceType] || 'Unknown source type';
    }
    /**
     * Get reliability score for source type (0-100)
     */
    static getReliabilityScore(sourceType) {
        const scores = {
            // High reliability sources
            [EventSourceType.SIEM]: 95,
            [EventSourceType.EDR]: 90,
            [EventSourceType.IDS_IPS]: 90,
            [EventSourceType.FIREWALL]: 85,
            [EventSourceType.OPERATING_SYSTEM]: 85,
            // Medium-high reliability
            [EventSourceType.ANTIVIRUS]: 80,
            [EventSourceType.WAF]: 80,
            [EventSourceType.VULNERABILITY_SCANNER]: 80,
            [EventSourceType.DIRECTORY_SERVICE]: 80,
            // Medium reliability
            [EventSourceType.NETWORK_DEVICE]: 75,
            [EventSourceType.DATABASE]: 75,
            [EventSourceType.WEB_SERVER]: 75,
            [EventSourceType.AWS]: 75,
            [EventSourceType.AZURE]: 75,
            [EventSourceType.GCP]: 75,
            // Lower reliability or external sources
            [EventSourceType.EXTERNAL_FEED]: 60,
            [EventSourceType.THIRD_PARTY]: 60,
            [EventSourceType.IOT_DEVICE]: 50,
            [EventSourceType.MANUAL]: 70,
            [EventSourceType.UNKNOWN]: 30,
            [EventSourceType.OTHER]: 40,
        };
        return scores[sourceType] || 50; // Default medium reliability
    }
    /**
     * Check if source type typically generates high-volume events
     */
    static isHighVolumeSource(sourceType) {
        return [
            EventSourceType.FIREWALL,
            EventSourceType.NETWORK_DEVICE,
            EventSourceType.WEB_SERVER,
            EventSourceType.DNS_SERVER,
            EventSourceType.OPERATING_SYSTEM,
            EventSourceType.DATABASE,
        ].includes(sourceType);
    }
    /**
     * Get recommended processing priority (1-10, higher = more priority)
     */
    static getProcessingPriority(sourceType) {
        const priorities = {
            // Critical security tools
            [EventSourceType.EDR]: 10,
            [EventSourceType.IDS_IPS]: 9,
            [EventSourceType.DECEPTION]: 9,
            [EventSourceType.THREAT_INTELLIGENCE]: 8,
            // Important security sources
            [EventSourceType.FIREWALL]: 7,
            [EventSourceType.WAF]: 7,
            [EventSourceType.ANTIVIRUS]: 7,
            [EventSourceType.PAM]: 8,
            // Standard sources
            [EventSourceType.OPERATING_SYSTEM]: 6,
            [EventSourceType.DIRECTORY_SERVICE]: 6,
            [EventSourceType.NETWORK_DEVICE]: 5,
            [EventSourceType.WEB_SERVER]: 5,
            // Lower priority
            [EventSourceType.IOT_DEVICE]: 4,
            [EventSourceType.ENVIRONMENTAL]: 3,
            [EventSourceType.MANUAL]: 6,
            [EventSourceType.UNKNOWN]: 2,
        };
        return priorities[sourceType] || 5; // Default medium priority
    }
}
exports.EventSourceTypeUtils = EventSourceTypeUtils;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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