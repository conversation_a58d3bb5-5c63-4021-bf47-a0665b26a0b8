{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\domain\\entities\\indicator-of-compromise.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCASiB;AACjB,qDAA+G;AAE/G,6EAAkE;AAElE;;GAEG;AACH,IAAY,OAmCX;AAnCD,WAAY,OAAO;IACjB,qBAAqB;IACrB,oCAAyB,CAAA;IACzB,4BAAiB,CAAA;IACjB,sBAAW,CAAA;IACX,0BAAe,CAAA;IACf,oCAAyB,CAAA;IAEzB,kBAAkB;IAClB,0CAA+B,CAAA;IAC/B,4CAAiC,CAAA;IACjC,gDAAqC,CAAA;IACrC,kCAAuB,CAAA;IACvB,kCAAuB,CAAA;IACvB,kCAAuB,CAAA;IAEvB,sBAAsB;IACtB,wCAA6B,CAAA;IAC7B,4CAAiC,CAAA;IAEjC,qBAAqB;IACrB,wCAA6B,CAAA;IAC7B,wCAA6B,CAAA;IAC7B,wCAA6B,CAAA;IAE7B,yBAAyB;IACzB,8DAAmD,CAAA;IACnD,oDAAyC,CAAA;IAEzC,mBAAmB;IACnB,0BAAe,CAAA;IACf,kCAAuB,CAAA;IACvB,wCAA6B,CAAA;IAC7B,kCAAuB,CAAA;IACvB,4BAAiB,CAAA;AACnB,CAAC,EAnCW,OAAO,uBAAP,OAAO,QAmClB;AAED;;GAEG;AACH,IAAY,SAOX;AAPD,WAAY,SAAS;IACnB,8BAAiB,CAAA;IACjB,kCAAqB,CAAA;IACrB,gCAAmB,CAAA;IACnB,8CAAiC,CAAA;IACjC,wCAA2B,CAAA;IAC3B,0CAA6B,CAAA;AAC/B,CAAC,EAPW,SAAS,yBAAT,SAAS,QAOpB;AAED;;GAEG;AACH,IAAY,aAKX;AALD,WAAY,aAAa;IACvB,8BAAa,CAAA;IACb,kCAAiB,CAAA;IACjB,4BAAW,CAAA;IACX,oCAAmB,CAAA;AACrB,CAAC,EALW,aAAa,6BAAb,aAAa,QAKxB;AAwBD;;;GAGG;AAOI,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IA8HhC;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM;YAChC,CAAC,IAAI,CAAC,aAAa;YACnB,CAAC,IAAI,CAAC,eAAe;YACrB,CAAC,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACrF,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,OAAa;QACrB,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;QAC1B,IAAI,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;QAE3B,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,cAAc,GAAG;gBACpB,GAAG,IAAI,CAAC,cAAc;gBACtB,cAAc,EAAE,OAAO;aACxB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,aAAa;QACX,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;QAEhC,QAAQ,IAAI,CAAC,OAAO,EAAE,CAAC;YACrB,KAAK,OAAO,CAAC,UAAU;gBACrB,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YACtC,KAAK,OAAO,CAAC,MAAM;gBACjB,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YACnC,KAAK,OAAO,CAAC,GAAG;gBACd,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAChC,KAAK,OAAO,CAAC,KAAK;gBAChB,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAClC,KAAK,OAAO,CAAC,aAAa;gBACxB,OAAO,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzC,KAAK,OAAO,CAAC,cAAc;gBACzB,OAAO,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzC,KAAK,OAAO,CAAC,gBAAgB;gBAC3B,OAAO,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzC;gBACE,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;QAEnC,QAAQ,IAAI,CAAC,OAAO,EAAE,CAAC;YACrB,KAAK,OAAO,CAAC,UAAU;gBACrB,sCAAsC;gBACtC,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;gBACnD,MAAM;YACR,KAAK,OAAO,CAAC,MAAM;gBACjB,UAAU,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;gBACtC,sBAAsB;gBACtB,IAAI,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;oBAC7B,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACvC,CAAC;gBACD,MAAM;YACR,KAAK,OAAO,CAAC,GAAG;gBACd,UAAU,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;gBACtC,MAAM;YACR,KAAK,OAAO,CAAC,KAAK;gBAChB,UAAU,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;gBACtC,MAAM;YACR,KAAK,OAAO,CAAC,aAAa,CAAC;YAC3B,KAAK,OAAO,CAAC,cAAc,CAAC;YAC5B,KAAK,OAAO,CAAC,gBAAgB;gBAC3B,UAAU,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;gBACtC,MAAM;QACV,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,MAAc,EAAE,IAAyB,EAAE,aAAqB,GAAG;QAC/E,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QAC3B,CAAC;QAED,8CAA8C;QAC9C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;QAE3E,qBAAqB;QACrB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;YACvB,MAAM;YACN,UAAU,EAAE,IAAI,IAAI,EAAE;YACtB,IAAI;YACJ,UAAU;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,MAAc,EAAE,OAAgB;QAClD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC;QAClC,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,cAAc,CAAC;QAEvC,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,gBAAgB,GAAG;gBACtB,GAAG,IAAI,CAAC,gBAAgB;gBACxB,oBAAoB,EAAE,OAAO;gBAC7B,iBAAiB,EAAE,IAAI,IAAI,EAAE;aAC9B,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,MAAc,EAAE,OAAgB;QAC7C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;QAC9B,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,WAAW,CAAC;QAEpC,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,gBAAgB,GAAG;gBACtB,GAAG,IAAI,CAAC,gBAAgB;gBACxB,gBAAgB,EAAE,OAAO;gBACzB,aAAa,EAAE,IAAI,IAAI,EAAE;aAC1B,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,6BAA6B;QAC7B,MAAM,gBAAgB,GAAG;YACvB,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,GAAG;YACzB,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,GAAG;YAC3B,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,GAAG;YACxB,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,GAAG;SAC7B,CAAC;QACF,KAAK,IAAI,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE3C,sBAAsB;QACtB,IAAI,IAAI,CAAC,QAAQ,GAAG,EAAE,EAAE,CAAC;YACvB,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,EAAE,CAAC;YAC7B,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,EAAE,CAAC;YAC7B,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;QAED,iBAAiB;QACjB,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;YACrC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YAC3E,IAAI,CAAC,SAAS,CAAC;QAEjB,IAAI,gBAAgB,IAAI,CAAC,EAAE,CAAC;YAC1B,KAAK,IAAI,GAAG,CAAC,CAAC,cAAc;QAC9B,CAAC;aAAM,IAAI,gBAAgB,IAAI,CAAC,EAAE,CAAC;YACjC,KAAK,IAAI,GAAG,CAAC,CAAC,SAAS;QACzB,CAAC;aAAM,IAAI,gBAAgB,IAAI,EAAE,EAAE,CAAC;YAClC,KAAK,IAAI,GAAG,CAAC,CAAC,kBAAkB;QAClC,CAAC;QAED,kCAAkC;QAClC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5B,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;QAED,0BAA0B;QAC1B,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1D,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;YACjH,KAAK,IAAI,aAAa,CAAC;QACzB,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,WAAW,EAAE,IAAI,CAAC,oBAAoB,EAAE;YACxC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,iBAAiB,EAAE,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE;YAChE,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;IAED,6BAA6B;IACrB,gBAAgB,CAAC,EAAU;QACjC,MAAM,SAAS,GAAG,6FAA6F,CAAC;QAChH,MAAM,SAAS,GAAG,4CAA4C,CAAC;QAC/D,OAAO,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAClD,CAAC;IAEO,aAAa,CAAC,MAAc;QAClC,MAAM,WAAW,GAAG,qGAAqG,CAAC;QAC1H,OAAO,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAClC,CAAC;IAEO,UAAU,CAAC,GAAW;QAC5B,IAAI,CAAC;YACH,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;YACb,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEO,YAAY,CAAC,KAAa;QAChC,MAAM,UAAU,GAAG,4BAA4B,CAAC;QAChD,OAAO,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChC,CAAC;CACF,CAAA;AAhaY,sDAAqB;AAEhC;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;iDACpB;AAOX;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,OAAO;KACd,CAAC;IACD,IAAA,wBAAM,EAAC,OAAO,CAAC;;sDACC;AAIjB;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IACxB,IAAA,0BAAQ,GAAE;;oDACG;AAKd;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACxD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;0DACU;AAQrB;IANC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,SAAS,CAAC,MAAM;KAC1B,CAAC;IACD,IAAA,wBAAM,EAAC,SAAS,CAAC;;qDACA;AAQlB;IANC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,aAAa;QACnB,OAAO,EAAE,aAAa,CAAC,MAAM;KAC9B,CAAC;IACD,IAAA,wBAAM,EAAC,aAAa,CAAC;;yDACI;AAI1B;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC;IAC5C,IAAA,wBAAM,GAAE;kDACE,IAAI,oBAAJ,IAAI;wDAAC;AAKhB;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC5D,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,GAAE;kDACE,IAAI,oBAAJ,IAAI;uDAAC;AAKhB;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC5D,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,GAAE;kDACG,IAAI,oBAAJ,IAAI;wDAAC;AAKjB;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACrD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;;mDACM;AAKhB;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACzC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;+DACyB;AAKpC;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACtD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;;6DACwB;AAIlC;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IACvC,IAAA,0BAAQ,GAAE;;uDACM;AAKjB;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC5D,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,GAAE;kDACC,IAAI,oBAAJ,IAAI;sDAAC;AAKf;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACnE,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;uDACO;AAIlB;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;IAC3C,IAAA,2BAAS,GAAE;;4DACW;AAKvB;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACxC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;8DACc;AAIzB;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;IAC3C,IAAA,2BAAS,GAAE;;8DACa;AAKzB;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACxC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;kEACkB;AAK7B;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACzC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;kDACM,MAAM,oBAAN,MAAM;6DAAc;AAKrC;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACrD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;;0DACa;AAKvB;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACzC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;kDACQ,MAAM,oBAAN,MAAM;+DAAc;AAKvC;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,+CAAkB,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACtF,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,wBAAwB,EAAE,CAAC;kDAC1B,+CAAkB,oBAAlB,+CAAkB;iEAAC;AAGxC;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mEACX;AAG9B;IADC,IAAA,0BAAgB,GAAE;kDACR,IAAI,oBAAJ,IAAI;wDAAC;AAGhB;IADC,IAAA,0BAAgB,GAAE;kDACR,IAAI,oBAAJ,IAAI;wDAAC;gCA5HL,qBAAqB;IANjC,IAAA,gBAAM,EAAC,0BAA0B,CAAC;IAClC,IAAA,eAAK,EAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IAC5B,IAAA,eAAK,EAAC,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,2CAA2C;;IAC/E,IAAA,eAAK,EAAC,CAAC,sBAAsB,CAAC,CAAC;IAC/B,IAAA,eAAK,EAAC,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;IAChC,IAAA,eAAK,EAAC,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;GACnB,qBAAqB,CAgajC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\domain\\entities\\indicator-of-compromise.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  ManyToOne,\r\n  JoinColumn,\r\n} from 'typeorm';\r\nimport { IsEnum, IsOptional, IsString, IsN<PERSON>ber, IsBoolean, IsArray, IsObject, IsDate } from 'class-validator';\r\n\r\nimport { ThreatIntelligence } from './threat-intelligence.entity';\r\n\r\n/**\r\n * IOC types based on common threat intelligence standards\r\n */\r\nexport enum IOCType {\r\n  // Network indicators\r\n  IP_ADDRESS = 'ip_address',\r\n  DOMAIN = 'domain',\r\n  URL = 'url',\r\n  EMAIL = 'email',\r\n  USER_AGENT = 'user_agent',\r\n  \r\n  // File indicators\r\n  FILE_HASH_MD5 = 'file_hash_md5',\r\n  FILE_HASH_SHA1 = 'file_hash_sha1',\r\n  FILE_HASH_SHA256 = 'file_hash_sha256',\r\n  FILE_NAME = 'file_name',\r\n  FILE_PATH = 'file_path',\r\n  FILE_SIZE = 'file_size',\r\n  \r\n  // Registry indicators\r\n  REGISTRY_KEY = 'registry_key',\r\n  REGISTRY_VALUE = 'registry_value',\r\n  \r\n  // Process indicators\r\n  PROCESS_NAME = 'process_name',\r\n  PROCESS_PATH = 'process_path',\r\n  COMMAND_LINE = 'command_line',\r\n  \r\n  // Certificate indicators\r\n  CERTIFICATE_FINGERPRINT = 'certificate_fingerprint',\r\n  CERTIFICATE_SERIAL = 'certificate_serial',\r\n  \r\n  // Other indicators\r\n  MUTEX = 'mutex',\r\n  PIPE_NAME = 'pipe_name',\r\n  SERVICE_NAME = 'service_name',\r\n  YARA_RULE = 'yara_rule',\r\n  CUSTOM = 'custom',\r\n}\r\n\r\n/**\r\n * IOC status\r\n */\r\nexport enum IOCStatus {\r\n  ACTIVE = 'active',\r\n  INACTIVE = 'inactive',\r\n  EXPIRED = 'expired',\r\n  FALSE_POSITIVE = 'false_positive',\r\n  WHITELISTED = 'whitelisted',\r\n  UNDER_REVIEW = 'under_review',\r\n}\r\n\r\n/**\r\n * IOC confidence levels\r\n */\r\nexport enum IOCConfidence {\r\n  HIGH = 'high',\r\n  MEDIUM = 'medium',\r\n  LOW = 'low',\r\n  UNKNOWN = 'unknown',\r\n}\r\n\r\n/**\r\n * Detection context information\r\n */\r\nexport interface DetectionContext {\r\n  detectionMethod: string;\r\n  detectionTool?: string;\r\n  detectionRule?: string;\r\n  detectionTime: Date;\r\n  analyst?: string;\r\n  confidence: IOCConfidence;\r\n}\r\n\r\n/**\r\n * Enrichment data from external sources\r\n */\r\nexport interface EnrichmentData {\r\n  source: string;\r\n  enrichedAt: Date;\r\n  data: Record<string, any>;\r\n  confidence: number;\r\n}\r\n\r\n/**\r\n * Indicator of Compromise entity\r\n * Represents specific observable artifacts that indicate potential compromise\r\n */\r\n@Entity('indicators_of_compromise')\r\n@Index(['iocType', 'status'])\r\n@Index(['value'], { unique: false }) // Allow duplicates with different contexts\r\n@Index(['threatIntelligenceId'])\r\n@Index(['firstSeen', 'lastSeen'])\r\n@Index(['confidence', 'status'])\r\nexport class IndicatorOfCompromise {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  @Column({\r\n    type: 'enum',\r\n    enum: IOCType,\r\n  })\r\n  @IsEnum(IOCType)\r\n  iocType: IOCType;\r\n\r\n  @Column({ type: 'text' })\r\n  @IsString()\r\n  value: string;\r\n\r\n  @Column({ type: 'varchar', length: 500, nullable: true })\r\n  @IsOptional()\r\n  @IsString()\r\n  description?: string;\r\n\r\n  @Column({\r\n    type: 'enum',\r\n    enum: IOCStatus,\r\n    default: IOCStatus.ACTIVE,\r\n  })\r\n  @IsEnum(IOCStatus)\r\n  status: IOCStatus;\r\n\r\n  @Column({\r\n    type: 'enum',\r\n    enum: IOCConfidence,\r\n    default: IOCConfidence.MEDIUM,\r\n  })\r\n  @IsEnum(IOCConfidence)\r\n  confidence: IOCConfidence;\r\n\r\n  @Column({ type: 'timestamp with time zone' })\r\n  @IsDate()\r\n  firstSeen: Date;\r\n\r\n  @Column({ type: 'timestamp with time zone', nullable: true })\r\n  @IsOptional()\r\n  @IsDate()\r\n  lastSeen?: Date;\r\n\r\n  @Column({ type: 'timestamp with time zone', nullable: true })\r\n  @IsOptional()\r\n  @IsDate()\r\n  expiresAt?: Date;\r\n\r\n  @Column({ type: 'text', array: true, nullable: true })\r\n  @IsOptional()\r\n  @IsArray()\r\n  tags?: string[];\r\n\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  @IsOptional()\r\n  @IsObject()\r\n  detectionContext?: DetectionContext;\r\n\r\n  @Column({ type: 'jsonb', array: true, nullable: true })\r\n  @IsOptional()\r\n  @IsArray()\r\n  enrichmentData?: EnrichmentData[];\r\n\r\n  @Column({ type: 'integer', default: 0 })\r\n  @IsNumber()\r\n  hitCount: number;\r\n\r\n  @Column({ type: 'timestamp with time zone', nullable: true })\r\n  @IsOptional()\r\n  @IsDate()\r\n  lastHit?: Date;\r\n\r\n  @Column({ type: 'decimal', precision: 3, scale: 1, nullable: true })\r\n  @IsOptional()\r\n  @IsNumber()\r\n  severity?: number;\r\n\r\n  @Column({ type: 'boolean', default: false })\r\n  @IsBoolean()\r\n  isWhitelisted: boolean;\r\n\r\n  @Column({ type: 'text', nullable: true })\r\n  @IsOptional()\r\n  @IsString()\r\n  whitelistReason?: string;\r\n\r\n  @Column({ type: 'boolean', default: false })\r\n  @IsBoolean()\r\n  isFalsePositive: boolean;\r\n\r\n  @Column({ type: 'text', nullable: true })\r\n  @IsOptional()\r\n  @IsString()\r\n  falsePositiveReason?: string;\r\n\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  @IsOptional()\r\n  @IsObject()\r\n  contextualData?: Record<string, any>;\r\n\r\n  @Column({ type: 'text', array: true, nullable: true })\r\n  @IsOptional()\r\n  @IsArray()\r\n  relatedIOCs?: string[];\r\n\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  @IsOptional()\r\n  @IsObject()\r\n  customAttributes?: Record<string, any>;\r\n\r\n  // Relationships\r\n  @ManyToOne(() => ThreatIntelligence, (threat) => threat.indicators, { nullable: true })\r\n  @JoinColumn({ name: 'threat_intelligence_id' })\r\n  threatIntelligence?: ThreatIntelligence;\r\n\r\n  @Column({ type: 'uuid', nullable: true })\r\n  threatIntelligenceId?: string;\r\n\r\n  @CreateDateColumn()\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn()\r\n  updatedAt: Date;\r\n\r\n  /**\r\n   * Check if IOC is currently active\r\n   */\r\n  get isActive(): boolean {\r\n    return this.status === IOCStatus.ACTIVE && \r\n           !this.isWhitelisted && \r\n           !this.isFalsePositive &&\r\n           (!this.expiresAt || this.expiresAt > new Date());\r\n  }\r\n\r\n  /**\r\n   * Check if IOC is expired\r\n   */\r\n  get isExpired(): boolean {\r\n    return this.expiresAt ? this.expiresAt <= new Date() : false;\r\n  }\r\n\r\n  /**\r\n   * Get age in days\r\n   */\r\n  get ageInDays(): number {\r\n    return Math.floor((Date.now() - this.firstSeen.getTime()) / (1000 * 60 * 60 * 24));\r\n  }\r\n\r\n  /**\r\n   * Get IOC summary\r\n   */\r\n  getSummary(): any {\r\n    return {\r\n      id: this.id,\r\n      iocType: this.iocType,\r\n      value: this.value,\r\n      status: this.status,\r\n      confidence: this.confidence,\r\n      isActive: this.isActive,\r\n      isExpired: this.isExpired,\r\n      ageInDays: this.ageInDays,\r\n      hitCount: this.hitCount,\r\n      lastHit: this.lastHit,\r\n      severity: this.severity,\r\n      isWhitelisted: this.isWhitelisted,\r\n      isFalsePositive: this.isFalsePositive,\r\n      threatIntelligenceId: this.threatIntelligenceId,\r\n      tags: this.tags,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Record a hit/detection of this IOC\r\n   */\r\n  recordHit(context?: any): void {\r\n    this.hitCount += 1;\r\n    this.lastHit = new Date();\r\n    this.lastSeen = new Date();\r\n\r\n    if (context) {\r\n      this.contextualData = {\r\n        ...this.contextualData,\r\n        lastHitContext: context,\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate IOC value based on type\r\n   */\r\n  validateValue(): boolean {\r\n    const value = this.value.trim();\r\n\r\n    switch (this.iocType) {\r\n      case IOCType.IP_ADDRESS:\r\n        return this.isValidIPAddress(value);\r\n      case IOCType.DOMAIN:\r\n        return this.isValidDomain(value);\r\n      case IOCType.URL:\r\n        return this.isValidURL(value);\r\n      case IOCType.EMAIL:\r\n        return this.isValidEmail(value);\r\n      case IOCType.FILE_HASH_MD5:\r\n        return /^[a-fA-F0-9]{32}$/.test(value);\r\n      case IOCType.FILE_HASH_SHA1:\r\n        return /^[a-fA-F0-9]{40}$/.test(value);\r\n      case IOCType.FILE_HASH_SHA256:\r\n        return /^[a-fA-F0-9]{64}$/.test(value);\r\n      default:\r\n        return value.length > 0;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Normalize IOC value based on type\r\n   */\r\n  normalizeValue(): void {\r\n    let normalized = this.value.trim();\r\n\r\n    switch (this.iocType) {\r\n      case IOCType.IP_ADDRESS:\r\n        // Remove leading zeros from IP octets\r\n        normalized = normalized.replace(/\\b0+(\\d)/g, '$1');\r\n        break;\r\n      case IOCType.DOMAIN:\r\n        normalized = normalized.toLowerCase();\r\n        // Remove trailing dot\r\n        if (normalized.endsWith('.')) {\r\n          normalized = normalized.slice(0, -1);\r\n        }\r\n        break;\r\n      case IOCType.URL:\r\n        normalized = normalized.toLowerCase();\r\n        break;\r\n      case IOCType.EMAIL:\r\n        normalized = normalized.toLowerCase();\r\n        break;\r\n      case IOCType.FILE_HASH_MD5:\r\n      case IOCType.FILE_HASH_SHA1:\r\n      case IOCType.FILE_HASH_SHA256:\r\n        normalized = normalized.toLowerCase();\r\n        break;\r\n    }\r\n\r\n    this.value = normalized;\r\n  }\r\n\r\n  /**\r\n   * Add enrichment data\r\n   */\r\n  addEnrichment(source: string, data: Record<string, any>, confidence: number = 0.8): void {\r\n    if (!this.enrichmentData) {\r\n      this.enrichmentData = [];\r\n    }\r\n\r\n    // Remove existing enrichment from same source\r\n    this.enrichmentData = this.enrichmentData.filter(e => e.source !== source);\r\n\r\n    // Add new enrichment\r\n    this.enrichmentData.push({\r\n      source,\r\n      enrichedAt: new Date(),\r\n      data,\r\n      confidence,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Mark as false positive\r\n   */\r\n  markAsFalsePositive(reason: string, analyst?: string): void {\r\n    this.isFalsePositive = true;\r\n    this.falsePositiveReason = reason;\r\n    this.status = IOCStatus.FALSE_POSITIVE;\r\n    \r\n    if (analyst) {\r\n      this.customAttributes = {\r\n        ...this.customAttributes,\r\n        falsePositiveAnalyst: analyst,\r\n        falsePositiveDate: new Date(),\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Add to whitelist\r\n   */\r\n  addToWhitelist(reason: string, analyst?: string): void {\r\n    this.isWhitelisted = true;\r\n    this.whitelistReason = reason;\r\n    this.status = IOCStatus.WHITELISTED;\r\n    \r\n    if (analyst) {\r\n      this.customAttributes = {\r\n        ...this.customAttributes,\r\n        whitelistAnalyst: analyst,\r\n        whitelistDate: new Date(),\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove from whitelist\r\n   */\r\n  removeFromWhitelist(): void {\r\n    this.isWhitelisted = false;\r\n    this.whitelistReason = null;\r\n    this.status = IOCStatus.ACTIVE;\r\n  }\r\n\r\n  /**\r\n   * Calculate threat score based on various factors\r\n   */\r\n  calculateThreatScore(): number {\r\n    let score = 0;\r\n\r\n    // Base score from confidence\r\n    const confidenceScores = {\r\n      [IOCConfidence.HIGH]: 8.0,\r\n      [IOCConfidence.MEDIUM]: 6.0,\r\n      [IOCConfidence.LOW]: 4.0,\r\n      [IOCConfidence.UNKNOWN]: 2.0,\r\n    };\r\n    score += confidenceScores[this.confidence];\r\n\r\n    // Hit frequency boost\r\n    if (this.hitCount > 10) {\r\n      score += 2.0;\r\n    } else if (this.hitCount > 5) {\r\n      score += 1.0;\r\n    } else if (this.hitCount > 0) {\r\n      score += 0.5;\r\n    }\r\n\r\n    // Recency factor\r\n    const daysSinceLastHit = this.lastHit ? \r\n      Math.floor((Date.now() - this.lastHit.getTime()) / (1000 * 60 * 60 * 24)) : \r\n      this.ageInDays;\r\n    \r\n    if (daysSinceLastHit <= 1) {\r\n      score += 2.0; // Very recent\r\n    } else if (daysSinceLastHit <= 7) {\r\n      score += 1.0; // Recent\r\n    } else if (daysSinceLastHit <= 30) {\r\n      score += 0.5; // Somewhat recent\r\n    }\r\n\r\n    // Threat intelligence association\r\n    if (this.threatIntelligence) {\r\n      score += 1.0;\r\n    }\r\n\r\n    // Enrichment data quality\r\n    if (this.enrichmentData && this.enrichmentData.length > 0) {\r\n      const avgConfidence = this.enrichmentData.reduce((sum, e) => sum + e.confidence, 0) / this.enrichmentData.length;\r\n      score += avgConfidence;\r\n    }\r\n\r\n    return Math.min(score, 10.0);\r\n  }\r\n\r\n  /**\r\n   * Export IOC for reporting\r\n   */\r\n  exportForReporting(): any {\r\n    return {\r\n      id: this.id,\r\n      iocType: this.iocType,\r\n      value: this.value,\r\n      description: this.description,\r\n      status: this.status,\r\n      confidence: this.confidence,\r\n      firstSeen: this.firstSeen,\r\n      lastSeen: this.lastSeen,\r\n      expiresAt: this.expiresAt,\r\n      tags: this.tags,\r\n      hitCount: this.hitCount,\r\n      lastHit: this.lastHit,\r\n      severity: this.severity,\r\n      isWhitelisted: this.isWhitelisted,\r\n      isFalsePositive: this.isFalsePositive,\r\n      threatScore: this.calculateThreatScore(),\r\n      threatIntelligenceId: this.threatIntelligenceId,\r\n      enrichmentSources: this.enrichmentData?.map(e => e.source) || [],\r\n      createdAt: this.createdAt,\r\n      updatedAt: this.updatedAt,\r\n    };\r\n  }\r\n\r\n  // Private validation methods\r\n  private isValidIPAddress(ip: string): boolean {\r\n    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\r\n    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;\r\n    return ipv4Regex.test(ip) || ipv6Regex.test(ip);\r\n  }\r\n\r\n  private isValidDomain(domain: string): boolean {\r\n    const domainRegex = /^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$/;\r\n    return domainRegex.test(domain);\r\n  }\r\n\r\n  private isValidURL(url: string): boolean {\r\n    try {\r\n      new URL(url);\r\n      return true;\r\n    } catch {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  private isValidEmail(email: string): boolean {\r\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n    return emailRegex.test(email);\r\n  }\r\n}\r\n"], "version": 3}