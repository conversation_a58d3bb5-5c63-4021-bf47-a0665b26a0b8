8c1d25af23cf558ff7a30e3a79d124e0
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var VulnerabilityAnalysisService_1;
var _a, _b, _c, _d, _e, _f, _g;
Object.defineProperty(exports, "__esModule", { value: true });
exports.VulnerabilityAnalysisService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const bull_1 = require("@nestjs/bull");
const bull_2 = require("bull");
const analysis_job_entity_1 = require("../../domain/entities/analysis-job.entity");
const prediction_entity_1 = require("../../domain/entities/prediction.entity");
const model_configuration_entity_1 = require("../../domain/entities/model-configuration.entity");
const ai_service_provider_service_1 = require("../../infrastructure/services/ai-service-provider.service");
const logger_service_1 = require("../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../infrastructure/logging/audit/audit.service");
/**
 * Vulnerability Analysis service
 * Provides AI-powered vulnerability analysis capabilities
 */
let VulnerabilityAnalysisService = VulnerabilityAnalysisService_1 = class VulnerabilityAnalysisService {
    constructor(analysisJobRepository, predictionRepository, modelConfigRepository, analysisQueue, aiServiceProvider, loggerService, auditService) {
        this.analysisJobRepository = analysisJobRepository;
        this.predictionRepository = predictionRepository;
        this.modelConfigRepository = modelConfigRepository;
        this.analysisQueue = analysisQueue;
        this.aiServiceProvider = aiServiceProvider;
        this.loggerService = loggerService;
        this.auditService = auditService;
        this.logger = new common_1.Logger(VulnerabilityAnalysisService_1.name);
    }
    /**
     * Analyze vulnerability severity using AI
     * @param vulnerabilityData Vulnerability data to analyze
     * @param userId User requesting the analysis
     * @returns Analysis job
     */
    async analyzeSeverity(vulnerabilityData, userId) {
        try {
            this.logger.debug('Starting vulnerability severity analysis', {
                cveId: vulnerabilityData.cveId,
                userId,
            });
            // Get default model for vulnerability analysis
            const model = await this.getDefaultModel('vulnerability_scanner');
            // Create analysis job
            const job = this.analysisJobRepository.create({
                type: 'vulnerability_analysis',
                title: `Vulnerability Severity Analysis - ${vulnerabilityData.cveId || 'Unknown'}`,
                description: 'AI-powered vulnerability severity assessment',
                status: 'pending',
                priority: this.determinePriority(vulnerabilityData),
                inputData: vulnerabilityData,
                modelConfigurationId: model.id,
                createdBy: userId,
                configuration: {
                    timeout: 300000, // 5 minutes
                    retries: 2,
                    confidenceThreshold: 0.7,
                },
            });
            const savedJob = await this.analysisJobRepository.save(job);
            // Queue job for processing
            await this.analysisQueue.add('analyze-vulnerability-severity', {
                jobId: savedJob.id,
                vulnerabilityData,
                modelId: model.id,
            }, {
                priority: this.getQueuePriority(job.priority),
                attempts: 3,
                backoff: 'exponential',
                delay: 0,
            });
            // Log audit event
            await this.auditService.logUserAction(userId, 'create', 'analysis_job', savedJob.id, {
                type: 'vulnerability_analysis',
                cveId: vulnerabilityData.cveId,
                modelId: model.id,
            });
            this.logger.log('Vulnerability severity analysis job created', {
                jobId: savedJob.id,
                cveId: vulnerabilityData.cveId,
                userId,
            });
            return savedJob;
        }
        catch (error) {
            this.logger.error('Failed to create vulnerability severity analysis job', {
                error: error.message,
                cveId: vulnerabilityData.cveId,
                userId,
            });
            throw error;
        }
    }
    /**
     * Analyze exploit probability using AI
     * @param vulnerabilityData Vulnerability data
     * @param userId User requesting the analysis
     * @returns Analysis job
     */
    async analyzeExploitProbability(vulnerabilityData, userId) {
        try {
            this.logger.debug('Starting exploit probability analysis', {
                cveId: vulnerabilityData.cveId,
                userId,
            });
            const model = await this.getDefaultModel('threat_classifier');
            const job = this.analysisJobRepository.create({
                type: 'vulnerability_analysis',
                title: `Exploit Probability Analysis - ${vulnerabilityData.cveId || 'Unknown'}`,
                description: 'AI-powered exploit probability assessment',
                status: 'pending',
                priority: this.determinePriority(vulnerabilityData),
                inputData: vulnerabilityData,
                modelConfigurationId: model.id,
                createdBy: userId,
                configuration: {
                    timeout: 180000, // 3 minutes
                    retries: 2,
                    confidenceThreshold: 0.6,
                },
            });
            const savedJob = await this.analysisJobRepository.save(job);
            await this.analysisQueue.add('analyze-exploit-probability', {
                jobId: savedJob.id,
                vulnerabilityData,
                modelId: model.id,
            }, {
                priority: this.getQueuePriority(job.priority),
                attempts: 3,
                backoff: 'exponential',
            });
            await this.auditService.logUserAction(userId, 'create', 'analysis_job', savedJob.id, {
                type: 'exploit_probability_analysis',
                cveId: vulnerabilityData.cveId,
                modelId: model.id,
            });
            return savedJob;
        }
        catch (error) {
            this.logger.error('Failed to create exploit probability analysis job', {
                error: error.message,
                cveId: vulnerabilityData.cveId,
                userId,
            });
            throw error;
        }
    }
    /**
     * Generate remediation recommendations using AI
     * @param vulnerabilityData Vulnerability data
     * @param environmentContext Environment context
     * @param userId User requesting the analysis
     * @returns Analysis job
     */
    async generateRemediationRecommendations(vulnerabilityData, environmentContext, userId) {
        try {
            this.logger.debug('Starting remediation recommendation generation', {
                cveId: vulnerabilityData.cveId,
                userId,
            });
            const model = await this.getDefaultModel('nlp_processor');
            const job = this.analysisJobRepository.create({
                type: 'vulnerability_analysis',
                title: `Remediation Recommendations - ${vulnerabilityData.cveId || 'Unknown'}`,
                description: 'AI-generated vulnerability remediation recommendations',
                status: 'pending',
                priority: this.determinePriority(vulnerabilityData),
                inputData: {
                    vulnerability: vulnerabilityData,
                    environment: environmentContext,
                },
                modelConfigurationId: model.id,
                createdBy: userId,
                configuration: {
                    timeout: 240000, // 4 minutes
                    retries: 2,
                    confidenceThreshold: 0.5,
                },
            });
            const savedJob = await this.analysisJobRepository.save(job);
            await this.analysisQueue.add('generate-remediation-recommendations', {
                jobId: savedJob.id,
                vulnerabilityData,
                environmentContext,
                modelId: model.id,
            }, {
                priority: this.getQueuePriority(job.priority),
                attempts: 3,
                backoff: 'exponential',
            });
            await this.auditService.logUserAction(userId, 'create', 'analysis_job', savedJob.id, {
                type: 'remediation_recommendations',
                cveId: vulnerabilityData.cveId,
                modelId: model.id,
            });
            return savedJob;
        }
        catch (error) {
            this.logger.error('Failed to create remediation recommendation job', {
                error: error.message,
                cveId: vulnerabilityData.cveId,
                userId,
            });
            throw error;
        }
    }
    /**
     * Perform batch vulnerability analysis
     * @param vulnerabilities Array of vulnerabilities to analyze
     * @param analysisType Type of analysis to perform
     * @param userId User requesting the analysis
     * @returns Analysis job
     */
    async batchAnalyze(vulnerabilities, analysisType, userId) {
        try {
            this.logger.debug('Starting batch vulnerability analysis', {
                count: vulnerabilities.length,
                analysisType,
                userId,
            });
            const modelType = this.getModelTypeForAnalysis(analysisType);
            const model = await this.getDefaultModel(modelType);
            const job = this.analysisJobRepository.create({
                type: 'vulnerability_analysis',
                title: `Batch ${analysisType} Analysis - ${vulnerabilities.length} vulnerabilities`,
                description: `AI-powered batch ${analysisType} analysis`,
                status: 'pending',
                priority: 'normal',
                inputData: {
                    vulnerabilities,
                    analysisType,
                    batchSize: Math.min(vulnerabilities.length, 10),
                },
                modelConfigurationId: model.id,
                createdBy: userId,
                configuration: {
                    timeout: 600000, // 10 minutes
                    retries: 1,
                    batchSize: 10,
                    confidenceThreshold: 0.6,
                },
                totalStages: Math.ceil(vulnerabilities.length / 10),
            });
            const savedJob = await this.analysisJobRepository.save(job);
            await this.analysisQueue.add('batch-vulnerability-analysis', {
                jobId: savedJob.id,
                vulnerabilities,
                analysisType,
                modelId: model.id,
            }, {
                priority: this.getQueuePriority(job.priority),
                attempts: 2,
                backoff: 'exponential',
            });
            await this.auditService.logUserAction(userId, 'create', 'analysis_job', savedJob.id, {
                type: 'batch_vulnerability_analysis',
                analysisType,
                count: vulnerabilities.length,
                modelId: model.id,
            });
            return savedJob;
        }
        catch (error) {
            this.logger.error('Failed to create batch vulnerability analysis job', {
                error: error.message,
                count: vulnerabilities.length,
                analysisType,
                userId,
            });
            throw error;
        }
    }
    /**
     * Get analysis job by ID
     * @param jobId Job ID
     * @returns Analysis job
     */
    async getAnalysisJob(jobId) {
        return await this.analysisJobRepository.findOne({
            where: { id: jobId },
            relations: ['modelConfiguration'],
        });
    }
    /**
     * Get predictions for an analysis job
     * @param jobId Job ID
     * @returns Array of predictions
     */
    async getJobPredictions(jobId) {
        return await this.predictionRepository.find({
            where: { analysisJobId: jobId },
            relations: ['modelConfiguration'],
            order: { createdAt: 'DESC' },
        });
    }
    /**
     * Cancel analysis job
     * @param jobId Job ID
     * @param userId User cancelling the job
     */
    async cancelAnalysisJob(jobId, userId) {
        try {
            const job = await this.getAnalysisJob(jobId);
            if (!job) {
                throw new Error('Analysis job not found');
            }
            if (job.isTerminal) {
                throw new Error('Cannot cancel completed job');
            }
            job.markAsCancelled('Cancelled by user');
            await this.analysisJobRepository.save(job);
            // Remove from queue if still pending
            if (job.status === 'queued') {
                // Implementation would depend on Bull queue job tracking
            }
            await this.auditService.logUserAction(userId, 'cancel', 'analysis_job', jobId, { reason: 'user_request' });
            this.logger.log('Analysis job cancelled', { jobId, userId });
        }
        catch (error) {
            this.logger.error('Failed to cancel analysis job', {
                jobId,
                userId,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Get default model configuration for analysis type
     * @param modelType Model type
     * @returns Model configuration
     */
    async getDefaultModel(modelType) {
        const model = await this.modelConfigRepository.findOne({
            where: {
                type: modelType,
                status: 'active',
                isDefault: true,
            },
        });
        if (!model) {
            throw new Error(`No default model found for type: ${modelType}`);
        }
        return model;
    }
    /**
     * Determine job priority based on vulnerability data
     * @param vulnerabilityData Vulnerability data
     * @returns Priority level
     */
    determinePriority(vulnerabilityData) {
        if (vulnerabilityData.cvssScore >= 9.0 || vulnerabilityData.exploitAvailable) {
            return 'urgent';
        }
        if (vulnerabilityData.cvssScore >= 7.0) {
            return 'high';
        }
        if (vulnerabilityData.cvssScore >= 4.0) {
            return 'normal';
        }
        return 'low';
    }
    /**
     * Get queue priority from job priority
     * @param priority Job priority
     * @returns Queue priority
     */
    getQueuePriority(priority) {
        const priorities = {
            urgent: 1,
            high: 2,
            normal: 3,
            low: 4,
        };
        return priorities[priority] || 3;
    }
    /**
     * Get model type for analysis type
     * @param analysisType Analysis type
     * @returns Model type
     */
    getModelTypeForAnalysis(analysisType) {
        const mapping = {
            severity: 'vulnerability_scanner',
            exploit_probability: 'threat_classifier',
            remediation: 'nlp_processor',
        };
        return mapping[analysisType] || 'vulnerability_scanner';
    }
};
exports.VulnerabilityAnalysisService = VulnerabilityAnalysisService;
exports.VulnerabilityAnalysisService = VulnerabilityAnalysisService = VulnerabilityAnalysisService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(analysis_job_entity_1.AnalysisJob)),
    __param(1, (0, typeorm_1.InjectRepository)(prediction_entity_1.Prediction)),
    __param(2, (0, typeorm_1.InjectRepository)(model_configuration_entity_1.ModelConfiguration)),
    __param(3, (0, bull_1.InjectQueue)('ai-analysis')),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _c : Object, typeof (_d = typeof bull_2.Queue !== "undefined" && bull_2.Queue) === "function" ? _d : Object, typeof (_e = typeof ai_service_provider_service_1.AIServiceProvider !== "undefined" && ai_service_provider_service_1.AIServiceProvider) === "function" ? _e : Object, typeof (_f = typeof logger_service_1.LoggerService !== "undefined" && logger_service_1.LoggerService) === "function" ? _f : Object, typeof (_g = typeof audit_service_1.AuditService !== "undefined" && audit_service_1.AuditService) === "function" ? _g : Object])
], VulnerabilityAnalysisService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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