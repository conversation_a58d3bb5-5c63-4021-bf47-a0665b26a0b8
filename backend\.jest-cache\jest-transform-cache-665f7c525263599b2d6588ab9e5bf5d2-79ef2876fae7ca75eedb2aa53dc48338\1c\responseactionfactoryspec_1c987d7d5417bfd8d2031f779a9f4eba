1013775cb651956e3bb58eb907592c60
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const response_action_factory_1 = require("../response-action.factory");
const response_action_entity_1 = require("../../entities/response-action.entity");
const shared_kernel_1 = require("../../../../../shared-kernel");
const action_type_enum_1 = require("../../enums/action-type.enum");
const action_status_enum_1 = require("../../enums/action-status.enum");
describe('ResponseActionFactory', () => {
    let baseOptions;
    beforeEach(() => {
        baseOptions = {
            actionType: action_type_enum_1.ActionType.BLOCK_IP,
            title: 'Block malicious IP',
            parameters: { ipAddress: '*************' },
            target: {
                type: 'system',
                id: 'firewall-001',
                name: 'Main Firewall',
            },
        };
    });
    describe('create', () => {
        it('should create a ResponseAction with basic options', () => {
            const action = response_action_factory_1.ResponseActionFactory.create(baseOptions);
            expect(action).toBeInstanceOf(response_action_entity_1.ResponseAction);
            expect(action.actionType).toBe(action_type_enum_1.ActionType.BLOCK_IP);
            expect(action.title).toBe('Block malicious IP');
            expect(action.parameters.ipAddress).toBe('*************');
            expect(action.target?.type).toBe('system');
        });
        it('should apply defaults based on action type', () => {
            const action = response_action_factory_1.ResponseActionFactory.create(baseOptions);
            expect(action.isAutomated).toBe(true); // BLOCK_IP is automated
            expect(action.isReversible).toBe(true); // BLOCK_IP is reversible
            expect(action.priority).toBe('high'); // BLOCK_IP is containment action, so high priority
            expect(action.maxRetries).toBe(3); // Default for automated actions
            expect(action.timeoutMinutes).toBeGreaterThan(0);
        });
        it('should override defaults with provided options', () => {
            const action = response_action_factory_1.ResponseActionFactory.create({
                ...baseOptions,
                priority: 'critical',
                isAutomated: false,
                maxRetries: 5,
                timeoutMinutes: 60,
            });
            expect(action.priority).toBe('critical');
            expect(action.isAutomated).toBe(false);
            expect(action.maxRetries).toBe(5);
            expect(action.timeoutMinutes).toBe(60);
        });
        it('should create with custom ID', () => {
            const customId = shared_kernel_1.UniqueEntityId.generate();
            const action = response_action_factory_1.ResponseActionFactory.create({
                ...baseOptions,
                id: customId,
            });
            expect(action.id.equals(customId)).toBe(true);
        });
        it('should set up rollback info for reversible actions', () => {
            const action = response_action_factory_1.ResponseActionFactory.create(baseOptions);
            expect(action.rollbackInfo).toBeDefined();
            expect(action.rollbackInfo?.canRollback).toBe(true);
            expect(action.rollbackInfo?.rollbackSteps).toContain('Remove IP from block list');
        });
        it('should generate appropriate tags based on action characteristics', () => {
            const action = response_action_factory_1.ResponseActionFactory.create(baseOptions);
            expect(action.tags).toContain('automated');
            expect(action.tags).toContain('reversible');
        });
        it('should set success criteria based on action type', () => {
            const action = response_action_factory_1.ResponseActionFactory.create(baseOptions);
            expect(action.successCriteria).toContain('IP address blocked in firewall');
            expect(action.successCriteria).toContain('Traffic from IP stopped');
        });
    });
    describe('createContainmentAction', () => {
        it('should create a containment action', () => {
            const action = response_action_factory_1.ResponseActionFactory.createContainmentAction(action_type_enum_1.ActionType.ISOLATE_SYSTEM, 'Isolate compromised system', { type: 'system', id: 'server-001' }, { systemId: 'server-001' });
            expect(action.actionType).toBe(action_type_enum_1.ActionType.ISOLATE_SYSTEM);
            expect(action.priority).toBe('high');
            expect(action.tags).toContain('containment');
            expect(action.tags).toContain('security');
        });
        it('should throw error for non-containment action type', () => {
            expect(() => {
                response_action_factory_1.ResponseActionFactory.createContainmentAction(action_type_enum_1.ActionType.SEND_EMAIL, 'Send email', { type: 'system', id: 'server-001' }, { recipient: '<EMAIL>' });
            }).toThrow('send_email is not a containment action type');
        });
    });
    describe('createEradicationAction', () => {
        it('should create an eradication action', () => {
            const action = response_action_factory_1.ResponseActionFactory.createEradicationAction(action_type_enum_1.ActionType.REMOVE_MALWARE, 'Remove detected malware', { type: 'system', id: 'server-001' }, { malwareId: 'malware-123' });
            expect(action.actionType).toBe(action_type_enum_1.ActionType.REMOVE_MALWARE);
            expect(action.priority).toBe('high');
            expect(action.approvalRequired).toBe(true);
            expect(action.tags).toContain('eradication');
            expect(action.tags).toContain('security');
        });
        it('should throw error for non-eradication action type', () => {
            expect(() => {
                response_action_factory_1.ResponseActionFactory.createEradicationAction(action_type_enum_1.ActionType.SEND_EMAIL, 'Send email', { type: 'system', id: 'server-001' }, { recipient: '<EMAIL>' });
            }).toThrow('send_email is not an eradication action type');
        });
    });
    describe('createRecoveryAction', () => {
        it('should create a recovery action', () => {
            const action = response_action_factory_1.ResponseActionFactory.createRecoveryAction(action_type_enum_1.ActionType.RESTORE_BACKUP, 'Restore from backup', { type: 'system', id: 'server-001' }, { backupId: 'backup-123' });
            expect(action.actionType).toBe(action_type_enum_1.ActionType.RESTORE_BACKUP);
            expect(action.priority).toBe('normal');
            expect(action.tags).toContain('recovery');
            expect(action.tags).toContain('restoration');
        });
        it('should throw error for non-recovery action type', () => {
            expect(() => {
                response_action_factory_1.ResponseActionFactory.createRecoveryAction(action_type_enum_1.ActionType.SEND_EMAIL, 'Send email', { type: 'system', id: 'server-001' }, { recipient: '<EMAIL>' });
            }).toThrow('send_email is not a recovery action type');
        });
    });
    describe('createNotificationAction', () => {
        it('should create a notification action', () => {
            const action = response_action_factory_1.ResponseActionFactory.createNotificationAction(action_type_enum_1.ActionType.SEND_EMAIL, 'Send security alert', { recipients: ['<EMAIL>'], subject: 'Security Alert' });
            expect(action.actionType).toBe(action_type_enum_1.ActionType.SEND_EMAIL);
            expect(action.priority).toBe('normal');
            expect(action.isAutomated).toBe(true);
            expect(action.approvalRequired).toBe(false);
            expect(action.tags).toContain('notification');
            expect(action.tags).toContain('communication');
        });
        it('should throw error for non-notification action type', () => {
            expect(() => {
                response_action_factory_1.ResponseActionFactory.createNotificationAction(action_type_enum_1.ActionType.BLOCK_IP, 'Block IP', { ipAddress: '*************' });
            }).toThrow('block_ip is not a notification action type');
        });
    });
    describe('createAutomatedAction', () => {
        it('should create an automated action', () => {
            const action = response_action_factory_1.ResponseActionFactory.createAutomatedAction(action_type_enum_1.ActionType.BLOCK_IP, 'Auto-block IP', { ipAddress: '*************' });
            expect(action.actionType).toBe(action_type_enum_1.ActionType.BLOCK_IP);
            expect(action.isAutomated).toBe(true);
            expect(action.status).toBe(action_status_enum_1.ActionStatus.APPROVED); // Auto-approved
        });
        it('should throw error for non-automated action type', () => {
            expect(() => {
                response_action_factory_1.ResponseActionFactory.createAutomatedAction(action_type_enum_1.ActionType.MANUAL_INVESTIGATION, 'Manual investigation', { target: 'system-001' });
            }).toThrow('manual_investigation is not an automated action type');
        });
    });
    describe('createManualAction', () => {
        it('should create a manual action', () => {
            const action = response_action_factory_1.ResponseActionFactory.createManualAction(action_type_enum_1.ActionType.MANUAL_INVESTIGATION, 'Investigate incident', { incidentId: 'incident-123' });
            expect(action.actionType).toBe(action_type_enum_1.ActionType.MANUAL_INVESTIGATION);
            expect(action.isAutomated).toBe(false);
            expect(action.approvalRequired).toBe(true);
            expect(action.tags).toContain('manual');
            expect(action.tags).toContain('human-required');
        });
        it('should throw error for non-manual action type', () => {
            expect(() => {
                response_action_factory_1.ResponseActionFactory.createManualAction(action_type_enum_1.ActionType.BLOCK_IP, 'Block IP', { ipAddress: '*************' });
            }).toThrow('block_ip is not a manual action type');
        });
    });
    describe('createHighPriorityAction', () => {
        it('should create a high priority action', () => {
            const action = response_action_factory_1.ResponseActionFactory.createHighPriorityAction(action_type_enum_1.ActionType.BLOCK_IP, 'Urgent IP block', { ipAddress: '*************' });
            expect(action.priority).toBe('high');
            expect(action.tags).toContain('high-priority');
            expect(action.tags).toContain('urgent');
        });
    });
    describe('createCriticalAction', () => {
        it('should create a critical action', () => {
            const action = response_action_factory_1.ResponseActionFactory.createCriticalAction(action_type_enum_1.ActionType.SHUTDOWN_SYSTEM, 'Emergency shutdown', { systemId: 'server-001' });
            expect(action.priority).toBe('critical');
            expect(action.approvalRequired).toBe(true);
            expect(action.approvalLevel).toBe('manager');
            expect(action.tags).toContain('critical');
            expect(action.tags).toContain('emergency');
        });
    });
    describe('createChainedAction', () => {
        it('should create a chained action', () => {
            const parentAction = response_action_factory_1.ResponseActionFactory.create(baseOptions);
            const childAction = response_action_factory_1.ResponseActionFactory.createChainedAction(parentAction, action_type_enum_1.ActionType.SEND_EMAIL, 'Notify of IP block', { recipients: ['<EMAIL>'], subject: 'IP Block Notification' });
            expect(childAction.parentActionId?.equals(parentAction.id)).toBe(true);
            expect(childAction.correlationId).toBe(parentAction.id.toString());
            expect(childAction.priority).toBe(parentAction.priority);
            expect(childAction.tags).toContain('chained');
            expect(parentAction.childActionIds).toContain(childAction.id);
        });
    });
    describe('fromEventResponse', () => {
        it('should create action from event response', () => {
            const eventId = shared_kernel_1.UniqueEntityId.generate();
            const action = response_action_factory_1.ResponseActionFactory.fromEventResponse(eventId, action_type_enum_1.ActionType.BLOCK_IP, 'Block IP from event', { ipAddress: '*************' });
            expect(action.relatedEventId?.equals(eventId)).toBe(true);
            expect(action.target?.type).toBe('event');
            expect(action.target?.id).toBe(eventId.toString());
            expect(action.tags).toContain('event-response');
            expect(action.tags).toContain('automated-response');
        });
    });
    describe('fromThreatResponse', () => {
        it('should create action from threat response', () => {
            const threatId = shared_kernel_1.UniqueEntityId.generate();
            const action = response_action_factory_1.ResponseActionFactory.fromThreatResponse(threatId, action_type_enum_1.ActionType.ISOLATE_SYSTEM, 'Isolate system from threat', { systemId: 'server-001' });
            expect(action.relatedThreatId?.equals(threatId)).toBe(true);
            expect(action.target?.type).toBe('threat');
            expect(action.target?.id).toBe(threatId.toString());
            expect(action.priority).toBe('high');
            expect(action.tags).toContain('threat-response');
            expect(action.tags).toContain('security');
        });
    });
    describe('fromVulnerabilityResponse', () => {
        it('should create action from vulnerability response', () => {
            const vulnerabilityId = shared_kernel_1.UniqueEntityId.generate();
            const action = response_action_factory_1.ResponseActionFactory.fromVulnerabilityResponse(vulnerabilityId, action_type_enum_1.ActionType.PATCH_VULNERABILITY, 'Patch vulnerability', { patchId: 'patch-123' });
            expect(action.relatedVulnerabilityId?.equals(vulnerabilityId)).toBe(true);
            expect(action.target?.type).toBe('vulnerability');
            expect(action.target?.id).toBe(vulnerabilityId.toString());
            expect(action.tags).toContain('vulnerability-response');
            expect(action.tags).toContain('remediation');
        });
    });
    describe('validation', () => {
        it('should validate action configuration', () => {
            expect(() => {
                response_action_factory_1.ResponseActionFactory.validateActionConfiguration(baseOptions);
            }).not.toThrow();
        });
        it('should throw error for missing action type', () => {
            const invalidOptions = { ...baseOptions };
            delete invalidOptions.actionType;
            expect(() => {
                response_action_factory_1.ResponseActionFactory.validateActionConfiguration(invalidOptions);
            }).toThrow('Action type is required');
        });
        it('should throw error for empty title', () => {
            const invalidOptions = { ...baseOptions, title: '' };
            expect(() => {
                response_action_factory_1.ResponseActionFactory.validateActionConfiguration(invalidOptions);
            }).toThrow('Action title is required');
        });
        it('should throw error for missing parameters', () => {
            const invalidOptions = { ...baseOptions };
            delete invalidOptions.parameters;
            expect(() => {
                response_action_factory_1.ResponseActionFactory.validateActionConfiguration(invalidOptions);
            }).toThrow('Action parameters are required');
        });
    });
    describe('action type specific validation', () => {
        it('should validate BLOCK_IP parameters', () => {
            expect(() => {
                response_action_factory_1.ResponseActionFactory.create({
                    actionType: action_type_enum_1.ActionType.BLOCK_IP,
                    title: 'Block IP',
                    parameters: { ipAddress: '*************' },
                });
            }).not.toThrow();
            expect(() => {
                response_action_factory_1.ResponseActionFactory.create({
                    actionType: action_type_enum_1.ActionType.BLOCK_IP,
                    title: 'Block IP',
                    parameters: {},
                });
            }).toThrow('IP address parameter is required for BLOCK_IP action');
        });
        it('should validate BLOCK_DOMAIN parameters', () => {
            expect(() => {
                response_action_factory_1.ResponseActionFactory.create({
                    actionType: action_type_enum_1.ActionType.BLOCK_DOMAIN,
                    title: 'Block domain',
                    parameters: { domain: 'malicious.com' },
                });
            }).not.toThrow();
            expect(() => {
                response_action_factory_1.ResponseActionFactory.create({
                    actionType: action_type_enum_1.ActionType.BLOCK_DOMAIN,
                    title: 'Block domain',
                    parameters: {},
                });
            }).toThrow('Domain parameter is required for BLOCK_DOMAIN action');
        });
        it('should validate DISABLE_ACCOUNT parameters', () => {
            expect(() => {
                response_action_factory_1.ResponseActionFactory.create({
                    actionType: action_type_enum_1.ActionType.DISABLE_ACCOUNT,
                    title: 'Disable account',
                    parameters: { accountId: 'user-123' },
                });
            }).not.toThrow();
            expect(() => {
                response_action_factory_1.ResponseActionFactory.create({
                    actionType: action_type_enum_1.ActionType.DISABLE_ACCOUNT,
                    title: 'Disable account',
                    parameters: { username: 'john.doe' },
                });
            }).not.toThrow();
            expect(() => {
                response_action_factory_1.ResponseActionFactory.create({
                    actionType: action_type_enum_1.ActionType.DISABLE_ACCOUNT,
                    title: 'Disable account',
                    parameters: {},
                });
            }).toThrow('Account ID or username parameter is required for DISABLE_ACCOUNT action');
        });
        it('should validate QUARANTINE_FILE parameters', () => {
            expect(() => {
                response_action_factory_1.ResponseActionFactory.create({
                    actionType: action_type_enum_1.ActionType.QUARANTINE_FILE,
                    title: 'Quarantine file',
                    parameters: { filePath: '/tmp/malware.exe' },
                });
            }).not.toThrow();
            expect(() => {
                response_action_factory_1.ResponseActionFactory.create({
                    actionType: action_type_enum_1.ActionType.QUARANTINE_FILE,
                    title: 'Quarantine file',
                    parameters: { fileHash: 'abc123' },
                });
            }).not.toThrow();
            expect(() => {
                response_action_factory_1.ResponseActionFactory.create({
                    actionType: action_type_enum_1.ActionType.QUARANTINE_FILE,
                    title: 'Quarantine file',
                    parameters: {},
                });
            }).toThrow('File path or hash parameter is required for QUARANTINE_FILE action');
        });
        it('should validate SEND_EMAIL parameters', () => {
            expect(() => {
                response_action_factory_1.ResponseActionFactory.create({
                    actionType: action_type_enum_1.ActionType.SEND_EMAIL,
                    title: 'Send email',
                    parameters: { recipients: ['<EMAIL>'], subject: 'Alert' },
                });
            }).not.toThrow();
            expect(() => {
                response_action_factory_1.ResponseActionFactory.create({
                    actionType: action_type_enum_1.ActionType.SEND_EMAIL,
                    title: 'Send email',
                    parameters: { recipients: ['<EMAIL>'] },
                });
            }).toThrow('Recipients and subject parameters are required for SEND_EMAIL action');
        });
    });
    describe('default generation', () => {
        it('should generate appropriate defaults for high-risk actions', () => {
            const action = response_action_factory_1.ResponseActionFactory.create({
                actionType: action_type_enum_1.ActionType.SHUTDOWN_SYSTEM,
                title: 'Emergency shutdown',
                parameters: { systemId: 'server-001' },
            });
            expect(action.priority).toBe('high');
            expect(action.approvalRequired).toBe(true);
            expect(action.tags).toContain('high-risk');
        });
        it('should generate appropriate defaults for containment actions', () => {
            const action = response_action_factory_1.ResponseActionFactory.create({
                actionType: action_type_enum_1.ActionType.ISOLATE_SYSTEM,
                title: 'Isolate system',
                parameters: { systemId: 'server-001' },
            });
            expect(action.priority).toBe('high');
            expect(action.tags).toContain('containment');
        });
        it('should generate appropriate defaults for notification actions', () => {
            const action = response_action_factory_1.ResponseActionFactory.create({
                actionType: action_type_enum_1.ActionType.SEND_EMAIL,
                title: 'Send notification',
                parameters: { recipients: ['<EMAIL>'], subject: 'Alert' },
            });
            expect(action.tags).toContain('notification');
            expect(action.isAutomated).toBe(true);
            expect(action.approvalRequired).toBe(false);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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