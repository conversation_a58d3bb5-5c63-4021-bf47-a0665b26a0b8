988c9199d69f5ba0dd834dcbe43cbbf5
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const is_cve_id_validator_1 = require("../is-cve-id.validator");
class TestCveDto {
}
__decorate([
    (0, is_cve_id_validator_1.IsCveId)(),
    __metadata("design:type", String)
], TestCveDto.prototype, "cveId", void 0);
__decorate([
    (0, is_cve_id_validator_1.IsCveIdArray)(),
    __metadata("design:type", Array)
], TestCveDto.prototype, "cveIds", void 0);
describe('CVE ID Validator', () => {
    let constraint;
    beforeEach(() => {
        constraint = new is_cve_id_validator_1.IsCveIdConstraint();
    });
    describe('IsCveIdConstraint', () => {
        describe('validate', () => {
            it('should validate correct CVE ID format', () => {
                const validCveIds = [
                    'CVE-2021-1234',
                    'CVE-2023-12345',
                    'CVE-2024-123456',
                    'CVE-2020-0001',
                    'CVE-1999-9999',
                ];
                validCveIds.forEach(cveId => {
                    expect(constraint.validate(cveId, {})).toBe(true);
                });
            });
            it('should reject invalid CVE ID formats', () => {
                const invalidCveIds = [
                    'CVE-21-1234', // Invalid year format
                    'CVE-2021-123', // Too few digits in sequence
                    'cve-2021-1234', // Lowercase
                    'CVE-2021-', // Missing sequence
                    'CVE-2021-abcd', // Non-numeric sequence
                    '2021-1234', // Missing CVE prefix
                    'CVE-1998-1234', // Year too early
                    'CVE-2030-1234', // Year too far in future
                    '', // Empty string
                    null, // Null value
                    undefined, // Undefined value
                    123, // Number instead of string
                ];
                invalidCveIds.forEach(cveId => {
                    expect(constraint.validate(cveId, {})).toBe(false);
                });
            });
            it('should validate year range correctly', () => {
                const currentYear = new Date().getFullYear();
                // Valid years
                expect(constraint.validate('CVE-1999-1234', {})).toBe(true);
                expect(constraint.validate(`CVE-${currentYear}-1234`, {})).toBe(true);
                expect(constraint.validate(`CVE-${currentYear + 1}-1234`, {})).toBe(true);
                // Invalid years
                expect(constraint.validate('CVE-1998-1234', {})).toBe(false);
                expect(constraint.validate(`CVE-${currentYear + 2}-1234`, {})).toBe(false);
            });
        });
        describe('defaultMessage', () => {
            it('should return appropriate error message', () => {
                const message = constraint.defaultMessage({
                    property: 'cveId',
                });
                expect(message).toContain('cveId');
                expect(message).toContain('CVE identifier');
            });
        });
    });
    describe('CveIdUtils', () => {
        describe('extractYear', () => {
            it('should extract year from valid CVE ID', () => {
                expect(is_cve_id_validator_1.CveIdUtils.extractYear('CVE-2021-1234')).toBe(2021);
                expect(is_cve_id_validator_1.CveIdUtils.extractYear('CVE-2023-56789')).toBe(2023);
            });
            it('should return null for invalid CVE ID', () => {
                expect(is_cve_id_validator_1.CveIdUtils.extractYear('invalid')).toBeNull();
                expect(is_cve_id_validator_1.CveIdUtils.extractYear('CVE-21-1234')).toBeNull();
            });
        });
        describe('extractSequenceNumber', () => {
            it('should extract sequence number from valid CVE ID', () => {
                expect(is_cve_id_validator_1.CveIdUtils.extractSequenceNumber('CVE-2021-1234')).toBe(1234);
                expect(is_cve_id_validator_1.CveIdUtils.extractSequenceNumber('CVE-2023-56789')).toBe(56789);
            });
            it('should return null for invalid CVE ID', () => {
                expect(is_cve_id_validator_1.CveIdUtils.extractSequenceNumber('invalid')).toBeNull();
                expect(is_cve_id_validator_1.CveIdUtils.extractSequenceNumber('CVE-21-1234')).toBeNull();
            });
        });
        describe('generateCveId', () => {
            it('should generate valid CVE ID from components', () => {
                expect(is_cve_id_validator_1.CveIdUtils.generateCveId(2021, 1234)).toBe('CVE-2021-1234');
                expect(is_cve_id_validator_1.CveIdUtils.generateCveId(2023, 123)).toBe('CVE-2023-0123');
            });
        });
        describe('isValid', () => {
            it('should validate CVE ID correctly', () => {
                expect(is_cve_id_validator_1.CveIdUtils.isValid('CVE-2021-1234')).toBe(true);
                expect(is_cve_id_validator_1.CveIdUtils.isValid('invalid')).toBe(false);
            });
        });
        describe('normalize', () => {
            it('should normalize valid CVE ID', () => {
                expect(is_cve_id_validator_1.CveIdUtils.normalize('cve-2021-1234')).toBe('CVE-2021-1234');
                expect(is_cve_id_validator_1.CveIdUtils.normalize('CVE-2021-1234')).toBe('CVE-2021-1234');
            });
            it('should return null for invalid CVE ID', () => {
                expect(is_cve_id_validator_1.CveIdUtils.normalize('invalid')).toBeNull();
            });
        });
    });
    describe('DTO Validation', () => {
        it('should validate DTO with valid CVE ID', async () => {
            const dto = (0, class_transformer_1.plainToClass)(TestCveDto, {
                cveId: 'CVE-2021-1234',
                cveIds: ['CVE-2021-1234', 'CVE-2022-5678'],
            });
            const errors = await (0, class_validator_1.validate)(dto);
            expect(errors).toHaveLength(0);
        });
        it('should reject DTO with invalid CVE ID', async () => {
            const dto = (0, class_transformer_1.plainToClass)(TestCveDto, {
                cveId: 'invalid-cve',
                cveIds: ['CVE-2021-1234', 'invalid-cve'],
            });
            const errors = await (0, class_validator_1.validate)(dto);
            expect(errors.length).toBeGreaterThan(0);
            const cveIdError = errors.find(error => error.property === 'cveId');
            const cveIdsError = errors.find(error => error.property === 'cveIds');
            expect(cveIdError).toBeDefined();
            expect(cveIdsError).toBeDefined();
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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