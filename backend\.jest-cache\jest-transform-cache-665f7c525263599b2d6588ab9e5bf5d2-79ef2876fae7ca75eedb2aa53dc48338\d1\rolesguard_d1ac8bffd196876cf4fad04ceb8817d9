73c1762524265fb1944835bb1701fd77
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RolesGuard_1;
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RolesGuard = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const rbac_service_1 = require("../rbac/rbac.service");
/**
 * Role-based access control guard
 * Checks if user has required roles to access a resource
 */
let RolesGuard = RolesGuard_1 = class RolesGuard {
    constructor(reflector, rbacService) {
        this.reflector = reflector;
        this.rbacService = rbacService;
        this.logger = new common_1.Logger(RolesGuard_1.name);
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const requiredRoles = this.getRequiredRoles(context);
        if (!requiredRoles?.length)
            return true;
        const { user } = request;
        if (!user) {
            this.logAndThrow('no authenticated user', {
                url: request.url,
                method: request.method,
                requiredRoles,
            });
        }
        const accessContext = {
            userId: user.id,
            userRoles: user.roles,
            requiredRoles,
            url: request.url,
            method: request.method,
        };
        this.logger.debug('Checking role-based access', accessContext);
        try {
            const hasAccess = await this.validateUserAccess(user.id, user.roles, requiredRoles);
            if (!hasAccess) {
                this.logAndThrow('insufficient roles', accessContext, 'warn');
            }
            this.logger.debug('Role-based access granted', accessContext);
            return true;
        }
        catch (error) {
            if (error instanceof common_1.ForbiddenException)
                throw error;
            this.logger.error('Error checking role-based access', {
                ...accessContext,
                error: error instanceof Error ? error.message : 'Unknown error',
            });
            throw new common_1.ForbiddenException('Access denied');
        }
    }
    getRequiredRoles(context) {
        return this.reflector.getAllAndOverride('roles', [
            context.getHandler(),
            context.getClass(),
        ]);
    }
    logAndThrow(reason, context, logLevel = 'warn') {
        const message = `Access denied - ${reason}`;
        this.logger[logLevel](message, context);
        const errorMessage = reason === 'no authenticated user'
            ? 'Authentication required'
            : 'Insufficient permissions';
        throw new common_1.ForbiddenException(errorMessage);
    }
    async validateUserAccess(userId, userRoles, requiredRoles) {
        // Check direct role matches first (faster)
        if (requiredRoles.some(role => userRoles.includes(role))) {
            return true;
        }
        // Check hierarchical roles if direct match fails
        return this.rbacService.checkRoleHierarchy(userId, requiredRoles);
    }
};
exports.RolesGuard = RolesGuard;
exports.RolesGuard = RolesGuard = RolesGuard_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof core_1.Reflector !== "undefined" && core_1.Reflector) === "function" ? _a : Object, typeof (_b = typeof rbac_service_1.RbacService !== "undefined" && rbac_service_1.RbacService) === "function" ? _b : Object])
], RolesGuard);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxpbmZyYXN0cnVjdHVyZVxcYXV0aFxcZ3VhcmRzXFxyb2xlcy5ndWFyZC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUFBLDJDQU13QjtBQUN4Qix1Q0FBeUM7QUFFekMsdURBQW1EO0FBb0JuRDs7O0dBR0c7QUFFSSxJQUFNLFVBQVUsa0JBQWhCLE1BQU0sVUFBVTtJQUdyQixZQUNtQixTQUFvQixFQUNwQixXQUF3QjtRQUR4QixjQUFTLEdBQVQsU0FBUyxDQUFXO1FBQ3BCLGdCQUFXLEdBQVgsV0FBVyxDQUFhO1FBSjFCLFdBQU0sR0FBRyxJQUFJLGVBQU0sQ0FBQyxZQUFVLENBQUMsSUFBSSxDQUFDLENBQUM7SUFLbkQsQ0FBQztJQUVKLEtBQUssQ0FBQyxXQUFXLENBQUMsT0FBeUI7UUFDekMsTUFBTSxPQUFPLEdBQUcsT0FBTyxDQUFDLFlBQVksRUFBRSxDQUFDLFVBQVUsRUFBbUIsQ0FBQztRQUVyRSxNQUFNLGFBQWEsR0FBRyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsT0FBTyxDQUFDLENBQUM7UUFDckQsSUFBSSxDQUFDLGFBQWEsRUFBRSxNQUFNO1lBQUUsT0FBTyxJQUFJLENBQUM7UUFFeEMsTUFBTSxFQUFFLElBQUksRUFBRSxHQUFHLE9BQU8sQ0FBQztRQUN6QixJQUFJLENBQUMsSUFBSSxFQUFFLENBQUM7WUFDVixJQUFJLENBQUMsV0FBVyxDQUFDLHVCQUF1QixFQUFFO2dCQUN4QyxHQUFHLEVBQUUsT0FBTyxDQUFDLEdBQUc7Z0JBQ2hCLE1BQU0sRUFBRSxPQUFPLENBQUMsTUFBTTtnQkFDdEIsYUFBYTthQUNkLENBQUMsQ0FBQztRQUNMLENBQUM7UUFFRCxNQUFNLGFBQWEsR0FBa0I7WUFDbkMsTUFBTSxFQUFFLElBQUksQ0FBQyxFQUFFO1lBQ2YsU0FBUyxFQUFFLElBQUksQ0FBQyxLQUFLO1lBQ3JCLGFBQWE7WUFDYixHQUFHLEVBQUUsT0FBTyxDQUFDLEdBQUc7WUFDaEIsTUFBTSxFQUFFLE9BQU8sQ0FBQyxNQUFNO1NBQ3ZCLENBQUM7UUFFRixJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyw0QkFBNEIsRUFBRSxhQUFhLENBQUMsQ0FBQztRQUUvRCxJQUFJLENBQUM7WUFDSCxNQUFNLFNBQVMsR0FBRyxNQUFNLElBQUksQ0FBQyxrQkFBa0IsQ0FBQyxJQUFJLENBQUMsRUFBRSxFQUFFLElBQUksQ0FBQyxLQUFLLEVBQUUsYUFBYSxDQUFDLENBQUM7WUFFcEYsSUFBSSxDQUFDLFNBQVMsRUFBRSxDQUFDO2dCQUNmLElBQUksQ0FBQyxXQUFXLENBQUMsb0JBQW9CLEVBQUUsYUFBYSxFQUFFLE1BQU0sQ0FBQyxDQUFDO1lBQ2hFLENBQUM7WUFFRCxJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQywyQkFBMkIsRUFBRSxhQUFhLENBQUMsQ0FBQztZQUM5RCxPQUFPLElBQUksQ0FBQztRQUNkLENBQUM7UUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1lBQ2YsSUFBSSxLQUFLLFlBQVksMkJBQWtCO2dCQUFFLE1BQU0sS0FBSyxDQUFDO1lBRXJELElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLGtDQUFrQyxFQUFFO2dCQUNwRCxHQUFHLGFBQWE7Z0JBQ2hCLEtBQUssRUFBRSxLQUFLLFlBQVksS0FBSyxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxlQUFlO2FBQ2hFLENBQUMsQ0FBQztZQUVILE1BQU0sSUFBSSwyQkFBa0IsQ0FBQyxlQUFlLENBQUMsQ0FBQztRQUNoRCxDQUFDO0lBQ0gsQ0FBQztJQUVPLGdCQUFnQixDQUFDLE9BQXlCO1FBQ2hELE9BQU8sSUFBSSxDQUFDLFNBQVMsQ0FBQyxpQkFBaUIsQ0FBVyxPQUFPLEVBQUU7WUFDekQsT0FBTyxDQUFDLFVBQVUsRUFBRTtZQUNwQixPQUFPLENBQUMsUUFBUSxFQUFFO1NBQ25CLENBQUMsQ0FBQztJQUNMLENBQUM7SUFFTyxXQUFXLENBQ2pCLE1BQWMsRUFDZCxPQUErQixFQUMvQixXQUE2QixNQUFNO1FBRW5DLE1BQU0sT0FBTyxHQUFHLG1CQUFtQixNQUFNLEVBQUUsQ0FBQztRQUM1QyxJQUFJLENBQUMsTUFBTSxDQUFDLFFBQVEsQ0FBQyxDQUFDLE9BQU8sRUFBRSxPQUFPLENBQUMsQ0FBQztRQUV4QyxNQUFNLFlBQVksR0FBRyxNQUFNLEtBQUssdUJBQXVCO1lBQ3JELENBQUMsQ0FBQyx5QkFBeUI7WUFDM0IsQ0FBQyxDQUFDLDBCQUEwQixDQUFDO1FBRS9CLE1BQU0sSUFBSSwyQkFBa0IsQ0FBQyxZQUFZLENBQUMsQ0FBQztJQUM3QyxDQUFDO0lBRU8sS0FBSyxDQUFDLGtCQUFrQixDQUM5QixNQUFjLEVBQ2QsU0FBbUIsRUFDbkIsYUFBdUI7UUFFdkIsMkNBQTJDO1FBQzNDLElBQUksYUFBYSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLFNBQVMsQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLENBQUMsRUFBRSxDQUFDO1lBQ3pELE9BQU8sSUFBSSxDQUFDO1FBQ2QsQ0FBQztRQUVELGlEQUFpRDtRQUNqRCxPQUFPLElBQUksQ0FBQyxXQUFXLENBQUMsa0JBQWtCLENBQUMsTUFBTSxFQUFFLGFBQWEsQ0FBQyxDQUFDO0lBQ3BFLENBQUM7Q0FDRixDQUFBO0FBekZZLGdDQUFVO3FCQUFWLFVBQVU7SUFEdEIsSUFBQSxtQkFBVSxHQUFFO3lEQUttQixnQkFBUyxvQkFBVCxnQkFBUyxvREFDUCwwQkFBVyxvQkFBWCwwQkFBVztHQUxoQyxVQUFVLENBeUZ0QiIsIm5hbWVzIjpbXSwic291cmNlcyI6WyJDOlxcVXNlcnNcXEx1a2FcXHNlbnRpbmVsXFxiYWNrZW5kXFxzcmNcXGluZnJhc3RydWN0dXJlXFxhdXRoXFxndWFyZHNcXHJvbGVzLmd1YXJkLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7XHJcbiAgSW5qZWN0YWJsZSxcclxuICBDYW5BY3RpdmF0ZSxcclxuICBFeGVjdXRpb25Db250ZXh0LFxyXG4gIEZvcmJpZGRlbkV4Y2VwdGlvbixcclxuICBMb2dnZXIsXHJcbn0gZnJvbSAnQG5lc3Rqcy9jb21tb24nO1xyXG5pbXBvcnQgeyBSZWZsZWN0b3IgfSBmcm9tICdAbmVzdGpzL2NvcmUnO1xyXG5pbXBvcnQgeyBSZXF1ZXN0IH0gZnJvbSAnZXhwcmVzcyc7XHJcbmltcG9ydCB7IFJiYWNTZXJ2aWNlIH0gZnJvbSAnLi4vcmJhYy9yYmFjLnNlcnZpY2UnO1xyXG5cclxuLy8gVHlwZSBkZWZpbml0aW9ucyBmb3IgYmV0dGVyIHR5cGUgc2FmZXR5XHJcbmludGVyZmFjZSBBdXRoZW50aWNhdGVkVXNlciB7XHJcbiAgaWQ6IHN0cmluZztcclxuICByb2xlczogc3RyaW5nW107XHJcbn1cclxuXHJcbmludGVyZmFjZSBSZXF1ZXN0V2l0aFVzZXIgZXh0ZW5kcyBSZXF1ZXN0IHtcclxuICB1c2VyPzogQXV0aGVudGljYXRlZFVzZXI7XHJcbn1cclxuXHJcbmludGVyZmFjZSBBY2Nlc3NDb250ZXh0IHtcclxuICB1c2VySWQ6IHN0cmluZztcclxuICB1c2VyUm9sZXM6IHN0cmluZ1tdO1xyXG4gIHJlcXVpcmVkUm9sZXM6IHN0cmluZ1tdO1xyXG4gIHVybDogc3RyaW5nO1xyXG4gIG1ldGhvZDogc3RyaW5nO1xyXG59XHJcblxyXG4vKipcclxuICogUm9sZS1iYXNlZCBhY2Nlc3MgY29udHJvbCBndWFyZFxyXG4gKiBDaGVja3MgaWYgdXNlciBoYXMgcmVxdWlyZWQgcm9sZXMgdG8gYWNjZXNzIGEgcmVzb3VyY2VcclxuICovXHJcbkBJbmplY3RhYmxlKClcclxuZXhwb3J0IGNsYXNzIFJvbGVzR3VhcmQgaW1wbGVtZW50cyBDYW5BY3RpdmF0ZSB7XHJcbiAgcHJpdmF0ZSByZWFkb25seSBsb2dnZXIgPSBuZXcgTG9nZ2VyKFJvbGVzR3VhcmQubmFtZSk7XHJcblxyXG4gIGNvbnN0cnVjdG9yKFxyXG4gICAgcHJpdmF0ZSByZWFkb25seSByZWZsZWN0b3I6IFJlZmxlY3RvcixcclxuICAgIHByaXZhdGUgcmVhZG9ubHkgcmJhY1NlcnZpY2U6IFJiYWNTZXJ2aWNlLFxyXG4gICkge31cclxuXHJcbiAgYXN5bmMgY2FuQWN0aXZhdGUoY29udGV4dDogRXhlY3V0aW9uQ29udGV4dCk6IFByb21pc2U8Ym9vbGVhbj4ge1xyXG4gICAgY29uc3QgcmVxdWVzdCA9IGNvbnRleHQuc3dpdGNoVG9IdHRwKCkuZ2V0UmVxdWVzdDxSZXF1ZXN0V2l0aFVzZXI+KCk7XHJcbiAgICBcclxuICAgIGNvbnN0IHJlcXVpcmVkUm9sZXMgPSB0aGlzLmdldFJlcXVpcmVkUm9sZXMoY29udGV4dCk7XHJcbiAgICBpZiAoIXJlcXVpcmVkUm9sZXM/Lmxlbmd0aCkgcmV0dXJuIHRydWU7XHJcblxyXG4gICAgY29uc3QgeyB1c2VyIH0gPSByZXF1ZXN0O1xyXG4gICAgaWYgKCF1c2VyKSB7XHJcbiAgICAgIHRoaXMubG9nQW5kVGhyb3coJ25vIGF1dGhlbnRpY2F0ZWQgdXNlcicsIHtcclxuICAgICAgICB1cmw6IHJlcXVlc3QudXJsLFxyXG4gICAgICAgIG1ldGhvZDogcmVxdWVzdC5tZXRob2QsXHJcbiAgICAgICAgcmVxdWlyZWRSb2xlcyxcclxuICAgICAgfSk7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgYWNjZXNzQ29udGV4dDogQWNjZXNzQ29udGV4dCA9IHtcclxuICAgICAgdXNlcklkOiB1c2VyLmlkLFxyXG4gICAgICB1c2VyUm9sZXM6IHVzZXIucm9sZXMsXHJcbiAgICAgIHJlcXVpcmVkUm9sZXMsXHJcbiAgICAgIHVybDogcmVxdWVzdC51cmwsXHJcbiAgICAgIG1ldGhvZDogcmVxdWVzdC5tZXRob2QsXHJcbiAgICB9O1xyXG5cclxuICAgIHRoaXMubG9nZ2VyLmRlYnVnKCdDaGVja2luZyByb2xlLWJhc2VkIGFjY2VzcycsIGFjY2Vzc0NvbnRleHQpO1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IGhhc0FjY2VzcyA9IGF3YWl0IHRoaXMudmFsaWRhdGVVc2VyQWNjZXNzKHVzZXIuaWQsIHVzZXIucm9sZXMsIHJlcXVpcmVkUm9sZXMpO1xyXG4gICAgICBcclxuICAgICAgaWYgKCFoYXNBY2Nlc3MpIHtcclxuICAgICAgICB0aGlzLmxvZ0FuZFRocm93KCdpbnN1ZmZpY2llbnQgcm9sZXMnLCBhY2Nlc3NDb250ZXh0LCAnd2FybicpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICB0aGlzLmxvZ2dlci5kZWJ1ZygnUm9sZS1iYXNlZCBhY2Nlc3MgZ3JhbnRlZCcsIGFjY2Vzc0NvbnRleHQpO1xyXG4gICAgICByZXR1cm4gdHJ1ZTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGlmIChlcnJvciBpbnN0YW5jZW9mIEZvcmJpZGRlbkV4Y2VwdGlvbikgdGhyb3cgZXJyb3I7XHJcbiAgICAgIFxyXG4gICAgICB0aGlzLmxvZ2dlci5lcnJvcignRXJyb3IgY2hlY2tpbmcgcm9sZS1iYXNlZCBhY2Nlc3MnLCB7XHJcbiAgICAgICAgLi4uYWNjZXNzQ29udGV4dCxcclxuICAgICAgICBlcnJvcjogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnVW5rbm93biBlcnJvcicsXHJcbiAgICAgIH0pO1xyXG4gICAgICBcclxuICAgICAgdGhyb3cgbmV3IEZvcmJpZGRlbkV4Y2VwdGlvbignQWNjZXNzIGRlbmllZCcpO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBnZXRSZXF1aXJlZFJvbGVzKGNvbnRleHQ6IEV4ZWN1dGlvbkNvbnRleHQpOiBzdHJpbmdbXSB8IHVuZGVmaW5lZCB7XHJcbiAgICByZXR1cm4gdGhpcy5yZWZsZWN0b3IuZ2V0QWxsQW5kT3ZlcnJpZGU8c3RyaW5nW10+KCdyb2xlcycsIFtcclxuICAgICAgY29udGV4dC5nZXRIYW5kbGVyKCksXHJcbiAgICAgIGNvbnRleHQuZ2V0Q2xhc3MoKSxcclxuICAgIF0pO1xyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBsb2dBbmRUaHJvdyhcclxuICAgIHJlYXNvbjogc3RyaW5nLFxyXG4gICAgY29udGV4dDogUGFydGlhbDxBY2Nlc3NDb250ZXh0PixcclxuICAgIGxvZ0xldmVsOiAnd2FybicgfCAnZXJyb3InID0gJ3dhcm4nLFxyXG4gICk6IG5ldmVyIHtcclxuICAgIGNvbnN0IG1lc3NhZ2UgPSBgQWNjZXNzIGRlbmllZCAtICR7cmVhc29ufWA7XHJcbiAgICB0aGlzLmxvZ2dlcltsb2dMZXZlbF0obWVzc2FnZSwgY29udGV4dCk7XHJcbiAgICBcclxuICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IHJlYXNvbiA9PT0gJ25vIGF1dGhlbnRpY2F0ZWQgdXNlcicgXHJcbiAgICAgID8gJ0F1dGhlbnRpY2F0aW9uIHJlcXVpcmVkJyBcclxuICAgICAgOiAnSW5zdWZmaWNpZW50IHBlcm1pc3Npb25zJztcclxuICAgIFxyXG4gICAgdGhyb3cgbmV3IEZvcmJpZGRlbkV4Y2VwdGlvbihlcnJvck1lc3NhZ2UpO1xyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBhc3luYyB2YWxpZGF0ZVVzZXJBY2Nlc3MoXHJcbiAgICB1c2VySWQ6IHN0cmluZyxcclxuICAgIHVzZXJSb2xlczogc3RyaW5nW10sXHJcbiAgICByZXF1aXJlZFJvbGVzOiBzdHJpbmdbXSxcclxuICApOiBQcm9taXNlPGJvb2xlYW4+IHtcclxuICAgIC8vIENoZWNrIGRpcmVjdCByb2xlIG1hdGNoZXMgZmlyc3QgKGZhc3RlcilcclxuICAgIGlmIChyZXF1aXJlZFJvbGVzLnNvbWUocm9sZSA9PiB1c2VyUm9sZXMuaW5jbHVkZXMocm9sZSkpKSB7XHJcbiAgICAgIHJldHVybiB0cnVlO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIENoZWNrIGhpZXJhcmNoaWNhbCByb2xlcyBpZiBkaXJlY3QgbWF0Y2ggZmFpbHNcclxuICAgIHJldHVybiB0aGlzLnJiYWNTZXJ2aWNlLmNoZWNrUm9sZUhpZXJhcmNoeSh1c2VySWQsIHJlcXVpcmVkUm9sZXMpO1xyXG4gIH1cclxufSJdLCJ2ZXJzaW9uIjozfQ==