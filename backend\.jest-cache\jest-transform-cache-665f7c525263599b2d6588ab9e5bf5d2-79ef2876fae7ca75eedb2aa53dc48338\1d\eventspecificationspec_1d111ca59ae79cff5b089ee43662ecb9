28edee0885f3e2ec1a6525b2ddf85093
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const event_specification_1 = require("../event.specification");
const event_factory_1 = require("../../factories/event.factory");
const event_type_enum_1 = require("../../enums/event-type.enum");
const event_severity_enum_1 = require("../../enums/event-severity.enum");
const event_status_enum_1 = require("../../enums/event-status.enum");
const event_processing_status_enum_1 = require("../../enums/event-processing-status.enum");
const event_source_type_enum_1 = require("../../enums/event-source-type.enum");
describe('Event Specifications', () => {
    let highSeverityEvent;
    let lowSeverityEvent;
    let criticalEvent;
    let activeEvent;
    let resolvedEvent;
    let highRiskEvent;
    let lowRiskEvent;
    let recentEvent;
    let oldEvent;
    beforeEach(() => {
        // Create test events with different characteristics
        highSeverityEvent = event_factory_1.EventFactory.create({
            type: event_type_enum_1.EventType.THREAT_DETECTED,
            severity: event_severity_enum_1.EventSeverity.HIGH,
            title: 'High Severity Threat',
            rawData: { threat: 'high-threat' },
            sourceType: event_source_type_enum_1.EventSourceType.FIREWALL,
            sourceIdentifier: 'fw-01',
            riskScore: 80,
            tags: ['threat', 'network'],
        });
        lowSeverityEvent = event_factory_1.EventFactory.create({
            type: event_type_enum_1.EventType.SYSTEM_STARTUP,
            severity: event_severity_enum_1.EventSeverity.LOW,
            title: 'System Startup',
            rawData: { system: 'started' },
            sourceType: event_source_type_enum_1.EventSourceType.OPERATING_SYSTEM,
            sourceIdentifier: 'server-01',
            riskScore: 20,
            tags: ['system', 'startup'],
        });
        criticalEvent = event_factory_1.EventFactory.create({
            type: event_type_enum_1.EventType.DATA_BREACH,
            severity: event_severity_enum_1.EventSeverity.CRITICAL,
            title: 'Data Breach',
            rawData: { breach: 'critical' },
            sourceType: event_source_type_enum_1.EventSourceType.DLP,
            sourceIdentifier: 'dlp-01',
            riskScore: 95,
            tags: ['breach', 'critical'],
        });
        activeEvent = event_factory_1.EventFactory.create({
            type: event_type_enum_1.EventType.MALWARE_DETECTED,
            severity: event_severity_enum_1.EventSeverity.HIGH,
            title: 'Active Malware',
            rawData: { malware: 'active' },
            sourceType: event_source_type_enum_1.EventSourceType.ANTIVIRUS,
            sourceIdentifier: 'av-01',
            status: event_status_enum_1.EventStatus.ACTIVE,
        });
        resolvedEvent = event_factory_1.EventFactory.create({
            type: event_type_enum_1.EventType.VULNERABILITY_DETECTED,
            severity: event_severity_enum_1.EventSeverity.MEDIUM,
            title: 'Resolved Vulnerability',
            rawData: { vuln: 'resolved' },
            sourceType: event_source_type_enum_1.EventSourceType.VULNERABILITY_SCANNER,
            sourceIdentifier: 'vs-01',
            status: event_status_enum_1.EventStatus.RESOLVED,
        });
        highRiskEvent = event_factory_1.EventFactory.create({
            type: event_type_enum_1.EventType.THREAT_DETECTED,
            severity: event_severity_enum_1.EventSeverity.HIGH,
            title: 'High Risk Event',
            rawData: { risk: 'high' },
            sourceType: event_source_type_enum_1.EventSourceType.THREAT_INTELLIGENCE,
            sourceIdentifier: 'ti-01',
            riskScore: 85,
        });
        lowRiskEvent = event_factory_1.EventFactory.create({
            type: event_type_enum_1.EventType.AUDIT_LOG,
            severity: event_severity_enum_1.EventSeverity.LOW,
            title: 'Low Risk Event',
            rawData: { audit: 'log' },
            sourceType: event_source_type_enum_1.EventSourceType.COMPLIANCE,
            sourceIdentifier: 'audit-01',
            riskScore: 15,
        });
        recentEvent = event_factory_1.EventFactory.create({
            type: event_type_enum_1.EventType.LOGIN_SUCCESS,
            severity: event_severity_enum_1.EventSeverity.LOW,
            title: 'Recent Login',
            rawData: { login: 'recent' },
            sourceType: event_source_type_enum_1.EventSourceType.DIRECTORY_SERVICE,
            sourceIdentifier: 'ad-01',
            timestamp: new Date(), // Current time
        });
        // Create an old event (simulate by creating with past timestamp)
        const pastDate = new Date();
        pastDate.setHours(pastDate.getHours() - 25); // 25 hours ago
        oldEvent = event_factory_1.EventFactory.create({
            type: event_type_enum_1.EventType.LOGIN_SUCCESS,
            severity: event_severity_enum_1.EventSeverity.LOW,
            title: 'Old Login',
            rawData: { login: 'old' },
            sourceType: event_source_type_enum_1.EventSourceType.DIRECTORY_SERVICE,
            sourceIdentifier: 'ad-01',
            timestamp: pastDate,
        });
        // Simulate resolution for resolved event
        resolvedEvent.changeStatus(event_status_enum_1.EventStatus.RESOLVED, 'analyst', 'Fixed');
    });
    describe('HighSeverityEventSpecification', () => {
        let spec;
        beforeEach(() => {
            spec = new event_specification_1.HighSeverityEventSpecification();
        });
        it('should satisfy high severity events', () => {
            expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(true);
            expect(spec.isSatisfiedBy(criticalEvent)).toBe(true);
        });
        it('should not satisfy low severity events', () => {
            expect(spec.isSatisfiedBy(lowSeverityEvent)).toBe(false);
        });
        it('should have correct description', () => {
            expect(spec.getDescription()).toBe('Event has high or critical severity');
        });
    });
    describe('CriticalEventSpecification', () => {
        let spec;
        beforeEach(() => {
            spec = new event_specification_1.CriticalEventSpecification();
        });
        it('should satisfy only critical events', () => {
            expect(spec.isSatisfiedBy(criticalEvent)).toBe(true);
            expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(false);
            expect(spec.isSatisfiedBy(lowSeverityEvent)).toBe(false);
        });
        it('should have correct description', () => {
            expect(spec.getDescription()).toBe('Event has critical severity');
        });
    });
    describe('ActiveEventSpecification', () => {
        let spec;
        beforeEach(() => {
            spec = new event_specification_1.ActiveEventSpecification();
        });
        it('should satisfy active events', () => {
            expect(spec.isSatisfiedBy(activeEvent)).toBe(true);
            expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(true); // Default is ACTIVE
        });
        it('should not satisfy resolved events', () => {
            expect(spec.isSatisfiedBy(resolvedEvent)).toBe(false);
        });
        it('should have correct description', () => {
            expect(spec.getDescription()).toBe('Event is active (not resolved or closed)');
        });
    });
    describe('HighRiskEventSpecification', () => {
        let spec;
        beforeEach(() => {
            spec = new event_specification_1.HighRiskEventSpecification();
        });
        it('should satisfy high risk events', () => {
            expect(spec.isSatisfiedBy(highRiskEvent)).toBe(true);
            expect(spec.isSatisfiedBy(criticalEvent)).toBe(true);
        });
        it('should not satisfy low risk events', () => {
            expect(spec.isSatisfiedBy(lowRiskEvent)).toBe(false);
        });
        it('should have correct description', () => {
            expect(spec.getDescription()).toBe('Event has high risk score (>= 70)');
        });
    });
    describe('RecentEventSpecification', () => {
        it('should satisfy recent events with default time window', () => {
            const spec = new event_specification_1.RecentEventSpecification();
            expect(spec.isSatisfiedBy(recentEvent)).toBe(true);
            expect(spec.isSatisfiedBy(oldEvent)).toBe(false);
        });
        it('should satisfy events within custom time window', () => {
            const spec = new event_specification_1.RecentEventSpecification(30 * 60 * 1000); // 30 minutes
            expect(spec.isSatisfiedBy(recentEvent)).toBe(true);
        });
        it('should have correct description', () => {
            const spec = new event_specification_1.RecentEventSpecification(3600000); // 1 hour
            expect(spec.getDescription()).toBe('Event occurred within 1h 0m');
        });
    });
    describe('StaleEventSpecification', () => {
        it('should satisfy stale events with default time window', () => {
            const spec = new event_specification_1.StaleEventSpecification();
            expect(spec.isSatisfiedBy(oldEvent)).toBe(true);
            expect(spec.isSatisfiedBy(recentEvent)).toBe(false);
        });
        it('should have correct description', () => {
            const spec = new event_specification_1.StaleEventSpecification(86400000); // 24 hours
            expect(spec.getDescription()).toBe('Event is older than 24 hours');
        });
    });
    describe('EventTypeSpecification', () => {
        it('should satisfy events of specified types', () => {
            const spec = new event_specification_1.EventTypeSpecification([event_type_enum_1.EventType.THREAT_DETECTED, event_type_enum_1.EventType.MALWARE_DETECTED]);
            expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(true); // THREAT_DETECTED
            expect(spec.isSatisfiedBy(activeEvent)).toBe(true); // MALWARE_DETECTED
            expect(spec.isSatisfiedBy(lowSeverityEvent)).toBe(false); // SYSTEM_STARTUP
        });
        it('should have correct description', () => {
            const spec = new event_specification_1.EventTypeSpecification([event_type_enum_1.EventType.THREAT_DETECTED, event_type_enum_1.EventType.MALWARE_DETECTED]);
            expect(spec.getDescription()).toBe('Event type is one of: THREAT_DETECTED, MALWARE_DETECTED');
        });
    });
    describe('EventSeveritySpecification', () => {
        it('should satisfy events of specified severities', () => {
            const spec = new event_specification_1.EventSeveritySpecification([event_severity_enum_1.EventSeverity.HIGH, event_severity_enum_1.EventSeverity.CRITICAL]);
            expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(true);
            expect(spec.isSatisfiedBy(criticalEvent)).toBe(true);
            expect(spec.isSatisfiedBy(lowSeverityEvent)).toBe(false);
        });
        it('should have correct description', () => {
            const spec = new event_specification_1.EventSeveritySpecification([event_severity_enum_1.EventSeverity.HIGH, event_severity_enum_1.EventSeverity.CRITICAL]);
            expect(spec.getDescription()).toBe('Event severity is one of: HIGH, CRITICAL');
        });
    });
    describe('EventStatusSpecification', () => {
        it('should satisfy events of specified statuses', () => {
            const spec = new event_specification_1.EventStatusSpecification([event_status_enum_1.EventStatus.ACTIVE, event_status_enum_1.EventStatus.INVESTIGATING]);
            expect(spec.isSatisfiedBy(activeEvent)).toBe(true);
            expect(spec.isSatisfiedBy(resolvedEvent)).toBe(false);
        });
        it('should have correct description', () => {
            const spec = new event_specification_1.EventStatusSpecification([event_status_enum_1.EventStatus.ACTIVE]);
            expect(spec.getDescription()).toBe('Event status is one of: ACTIVE');
        });
    });
    describe('EventProcessingStatusSpecification', () => {
        it('should satisfy events of specified processing statuses', () => {
            const spec = new event_specification_1.EventProcessingStatusSpecification([event_processing_status_enum_1.EventProcessingStatus.RAW, event_processing_status_enum_1.EventProcessingStatus.NORMALIZED]);
            expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(true); // Default is RAW
            expect(spec.isSatisfiedBy(lowSeverityEvent)).toBe(true); // Default is RAW
        });
        it('should have correct description', () => {
            const spec = new event_specification_1.EventProcessingStatusSpecification([event_processing_status_enum_1.EventProcessingStatus.RAW]);
            expect(spec.getDescription()).toBe('Event processing status is one of: raw');
        });
    });
    describe('EventSourceTypeSpecification', () => {
        it('should satisfy events from specified source types', () => {
            const spec = new event_specification_1.EventSourceTypeSpecification([event_source_type_enum_1.EventSourceType.FIREWALL, event_source_type_enum_1.EventSourceType.ANTIVIRUS]);
            expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(true); // FIREWALL
            expect(spec.isSatisfiedBy(activeEvent)).toBe(true); // ANTIVIRUS
            expect(spec.isSatisfiedBy(lowSeverityEvent)).toBe(false); // OPERATING_SYSTEM
        });
        it('should have correct description', () => {
            const spec = new event_specification_1.EventSourceTypeSpecification([event_source_type_enum_1.EventSourceType.FIREWALL]);
            expect(spec.getDescription()).toBe('Event source type is one of: firewall');
        });
    });
    describe('EventTagSpecification', () => {
        it('should satisfy events with any of the specified tags', () => {
            const spec = new event_specification_1.EventTagSpecification(['threat', 'malware'], false);
            expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(true); // Has 'threat' tag
            expect(spec.isSatisfiedBy(lowSeverityEvent)).toBe(false); // Has neither tag
        });
        it('should satisfy events with all of the specified tags', () => {
            const spec = new event_specification_1.EventTagSpecification(['threat', 'network'], true);
            expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(true); // Has both tags
            expect(spec.isSatisfiedBy(criticalEvent)).toBe(false); // Missing 'network' tag
        });
        it('should have correct description for any tags', () => {
            const spec = new event_specification_1.EventTagSpecification(['threat', 'malware'], false);
            expect(spec.getDescription()).toBe('Event has any of these tags: threat, malware');
        });
        it('should have correct description for all tags', () => {
            const spec = new event_specification_1.EventTagSpecification(['threat', 'network'], true);
            expect(spec.getDescription()).toBe('Event has all of these tags: threat, network');
        });
    });
    describe('EventRiskScoreRangeSpecification', () => {
        it('should satisfy events within risk score range', () => {
            const spec = new event_specification_1.EventRiskScoreRangeSpecification(70, 90);
            expect(spec.isSatisfiedBy(highRiskEvent)).toBe(true); // Score 85
            expect(spec.isSatisfiedBy(lowRiskEvent)).toBe(false); // Score 15
            expect(spec.isSatisfiedBy(criticalEvent)).toBe(false); // Score 95 (above max)
        });
        it('should satisfy events with minimum score only', () => {
            const spec = new event_specification_1.EventRiskScoreRangeSpecification(70);
            expect(spec.isSatisfiedBy(highRiskEvent)).toBe(true); // Score 85
            expect(spec.isSatisfiedBy(criticalEvent)).toBe(true); // Score 95
            expect(spec.isSatisfiedBy(lowRiskEvent)).toBe(false); // Score 15
        });
        it('should satisfy events with maximum score only', () => {
            const spec = new event_specification_1.EventRiskScoreRangeSpecification(undefined, 50);
            expect(spec.isSatisfiedBy(lowRiskEvent)).toBe(true); // Score 15
            expect(spec.isSatisfiedBy(highRiskEvent)).toBe(false); // Score 85
        });
        it('should have correct descriptions', () => {
            const rangeSpec = new event_specification_1.EventRiskScoreRangeSpecification(70, 90);
            expect(rangeSpec.getDescription()).toBe('Event risk score is between 70 and 90');
            const minSpec = new event_specification_1.EventRiskScoreRangeSpecification(70);
            expect(minSpec.getDescription()).toBe('Event risk score is at least 70');
            const maxSpec = new event_specification_1.EventRiskScoreRangeSpecification(undefined, 50);
            expect(maxSpec.getDescription()).toBe('Event risk score is at most 50');
        });
    });
    describe('EventCorrelationSpecification', () => {
        let correlatedEvent;
        let parentEvent;
        beforeEach(() => {
            parentEvent = event_factory_1.EventFactory.create({
                type: event_type_enum_1.EventType.THREAT_DETECTED,
                severity: event_severity_enum_1.EventSeverity.HIGH,
                title: 'Parent Event',
                rawData: { parent: true },
                sourceType: event_source_type_enum_1.EventSourceType.FIREWALL,
                sourceIdentifier: 'fw-01',
            });
            correlatedEvent = event_factory_1.EventFactory.createCorrelatedEvent(parentEvent, event_type_enum_1.EventType.CONNECTION_ESTABLISHED, 'Child Event', { child: true });
        });
        it('should satisfy events with specific correlation ID', () => {
            const spec = new event_specification_1.EventCorrelationSpecification(correlatedEvent.correlationId);
            expect(spec.isSatisfiedBy(correlatedEvent)).toBe(true);
            expect(spec.isSatisfiedBy(parentEvent)).toBe(false);
        });
        it('should satisfy events with specific parent ID', () => {
            const spec = new event_specification_1.EventCorrelationSpecification(undefined, parentEvent.id);
            expect(spec.isSatisfiedBy(correlatedEvent)).toBe(true);
            expect(spec.isSatisfiedBy(parentEvent)).toBe(false);
        });
        it('should satisfy events with any correlation', () => {
            const spec = new event_specification_1.EventCorrelationSpecification(undefined, undefined, true);
            expect(spec.isSatisfiedBy(correlatedEvent)).toBe(true);
            expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(false); // No correlation
        });
        it('should satisfy events with no correlation', () => {
            const spec = new event_specification_1.EventCorrelationSpecification(undefined, undefined, false);
            expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(true); // No correlation
            expect(spec.isSatisfiedBy(correlatedEvent)).toBe(false);
        });
    });
    describe('FailedProcessingEventSpecification', () => {
        let failedEvent;
        beforeEach(() => {
            failedEvent = event_factory_1.EventFactory.create({
                type: event_type_enum_1.EventType.THREAT_DETECTED,
                severity: event_severity_enum_1.EventSeverity.HIGH,
                title: 'Failed Event',
                rawData: { failed: true },
                sourceType: event_source_type_enum_1.EventSourceType.FIREWALL,
                sourceIdentifier: 'fw-01',
            });
            failedEvent.changeProcessingStatus(event_processing_status_enum_1.EventProcessingStatus.FAILED);
        });
        it('should satisfy events with failed processing', () => {
            const spec = new event_specification_1.FailedProcessingEventSpecification();
            expect(spec.isSatisfiedBy(failedEvent)).toBe(true);
            expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(false);
        });
        it('should have correct description', () => {
            const spec = new event_specification_1.FailedProcessingEventSpecification();
            expect(spec.getDescription()).toBe('Event processing has failed');
        });
    });
    describe('MaxAttemptsExceededSpecification', () => {
        let maxAttemptsEvent;
        beforeEach(() => {
            maxAttemptsEvent = event_factory_1.EventFactory.create({
                type: event_type_enum_1.EventType.THREAT_DETECTED,
                severity: event_severity_enum_1.EventSeverity.HIGH,
                title: 'Max Attempts Event',
                rawData: { attempts: 'max' },
                sourceType: event_source_type_enum_1.EventSourceType.FIREWALL,
                sourceIdentifier: 'fw-01',
            });
            // Record 5 attempts to exceed max
            for (let i = 0; i < 5; i++) {
                maxAttemptsEvent.recordProcessingAttempt();
            }
        });
        it('should satisfy events that exceeded max attempts', () => {
            const spec = new event_specification_1.MaxAttemptsExceededSpecification();
            expect(spec.isSatisfiedBy(maxAttemptsEvent)).toBe(true);
            expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(false);
        });
        it('should have correct description', () => {
            const spec = new event_specification_1.MaxAttemptsExceededSpecification();
            expect(spec.getDescription()).toBe('Event has exceeded maximum processing attempts');
        });
    });
    describe('RequiresAttentionSpecification', () => {
        let investigatingEvent;
        beforeEach(() => {
            investigatingEvent = event_factory_1.EventFactory.create({
                type: event_type_enum_1.EventType.THREAT_DETECTED,
                severity: event_severity_enum_1.EventSeverity.MEDIUM,
                title: 'Investigating Event',
                rawData: { investigating: true },
                sourceType: event_source_type_enum_1.EventSourceType.FIREWALL,
                sourceIdentifier: 'fw-01',
                status: event_status_enum_1.EventStatus.INVESTIGATING,
            });
        });
        it('should satisfy events requiring attention', () => {
            const spec = new event_specification_1.RequiresAttentionSpecification();
            expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(true); // High severity
            expect(spec.isSatisfiedBy(criticalEvent)).toBe(true); // Critical severity
            expect(spec.isSatisfiedBy(highRiskEvent)).toBe(true); // High risk
            expect(spec.isSatisfiedBy(investigatingEvent)).toBe(true); // Under investigation
            expect(spec.isSatisfiedBy(lowSeverityEvent)).toBe(false); // Low severity, low risk
        });
        it('should have correct description', () => {
            const spec = new event_specification_1.RequiresAttentionSpecification();
            expect(spec.getDescription()).toBe('Event requires human attention (high severity, high risk, failed processing, or under investigation)');
        });
    });
    describe('SecurityAlertSpecification', () => {
        it('should satisfy security alert events', () => {
            const spec = new event_specification_1.SecurityAlertSpecification();
            expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(true); // THREAT_DETECTED
            expect(spec.isSatisfiedBy(activeEvent)).toBe(true); // MALWARE_DETECTED
            expect(spec.isSatisfiedBy(criticalEvent)).toBe(true); // High severity
            expect(spec.isSatisfiedBy(lowSeverityEvent)).toBe(false); // SYSTEM_STARTUP, low severity
        });
        it('should satisfy events with security-alert tag', () => {
            const taggedEvent = event_factory_1.EventFactory.create({
                type: event_type_enum_1.EventType.CUSTOM,
                severity: event_severity_enum_1.EventSeverity.LOW,
                title: 'Tagged Event',
                rawData: { tagged: true },
                sourceType: event_source_type_enum_1.EventSourceType.CUSTOM_APPLICATION,
                sourceIdentifier: 'app-01',
                tags: ['security-alert'],
            });
            const spec = new event_specification_1.SecurityAlertSpecification();
            expect(spec.isSatisfiedBy(taggedEvent)).toBe(true);
        });
        it('should have correct description', () => {
            const spec = new event_specification_1.SecurityAlertSpecification();
            expect(spec.getDescription()).toBe('Event is a security alert (threat, malware, vulnerability, or high severity)');
        });
    });
    describe('EventSpecificationBuilder', () => {
        it('should build specification with single condition', () => {
            const spec = event_specification_1.EventSpecificationBuilder.create()
                .highSeverity()
                .build();
            expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(true);
            expect(spec.isSatisfiedBy(lowSeverityEvent)).toBe(false);
        });
        it('should build specification with multiple AND conditions', () => {
            const spec = event_specification_1.EventSpecificationBuilder.create()
                .highSeverity()
                .active()
                .highRisk()
                .build();
            expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(true); // High severity, active, high risk
            expect(spec.isSatisfiedBy(resolvedEvent)).toBe(false); // Not active
            expect(spec.isSatisfiedBy(lowRiskEvent)).toBe(false); // Not high risk
        });
        it('should build specification with multiple OR conditions', () => {
            const spec = event_specification_1.EventSpecificationBuilder.create()
                .critical()
                .highRisk()
                .buildWithOr();
            expect(spec.isSatisfiedBy(criticalEvent)).toBe(true); // Critical
            expect(spec.isSatisfiedBy(highRiskEvent)).toBe(true); // High risk
            expect(spec.isSatisfiedBy(lowSeverityEvent)).toBe(false); // Neither critical nor high risk
        });
        it('should build specification with various filters', () => {
            const spec = event_specification_1.EventSpecificationBuilder.create()
                .ofTypes(event_type_enum_1.EventType.THREAT_DETECTED, event_type_enum_1.EventType.MALWARE_DETECTED)
                .withSeverities(event_severity_enum_1.EventSeverity.HIGH, event_severity_enum_1.EventSeverity.CRITICAL)
                .fromSources(event_source_type_enum_1.EventSourceType.FIREWALL, event_source_type_enum_1.EventSourceType.ANTIVIRUS)
                .withTags(['threat', 'security'], false)
                .riskScoreRange(70, 100)
                .recent(3600000)
                .build();
            expect(spec.isSatisfiedBy(highSeverityEvent)).toBe(true); // Matches all criteria
            expect(spec.isSatisfiedBy(lowSeverityEvent)).toBe(false); // Wrong type, severity, source
        });
        it('should throw error when building without conditions', () => {
            expect(() => {
                event_specification_1.EventSpecificationBuilder.create().build();
            }).toThrow('At least one specification must be added');
        });
    });
    describe('Specification Composition', () => {
        it('should combine specifications with AND', () => {
            const highSeveritySpec = new event_specification_1.HighSeverityEventSpecification();
            const activeSpec = new event_specification_1.ActiveEventSpecification();
            const combinedSpec = highSeveritySpec.and(activeSpec);
            expect(combinedSpec.isSatisfiedBy(highSeverityEvent)).toBe(true); // High severity and active
            expect(combinedSpec.isSatisfiedBy(resolvedEvent)).toBe(false); // Not active
        });
        it('should combine specifications with OR', () => {
            const criticalSpec = new event_specification_1.CriticalEventSpecification();
            const highRiskSpec = new event_specification_1.HighRiskEventSpecification();
            const combinedSpec = criticalSpec.or(highRiskSpec);
            expect(combinedSpec.isSatisfiedBy(criticalEvent)).toBe(true); // Critical
            expect(combinedSpec.isSatisfiedBy(highRiskEvent)).toBe(true); // High risk
            expect(combinedSpec.isSatisfiedBy(lowSeverityEvent)).toBe(false); // Neither
        });
        it('should negate specifications with NOT', () => {
            const highSeveritySpec = new event_specification_1.HighSeverityEventSpecification();
            const notHighSeveritySpec = highSeveritySpec.not();
            expect(notHighSeveritySpec.isSatisfiedBy(lowSeverityEvent)).toBe(true); // Not high severity
            expect(notHighSeveritySpec.isSatisfiedBy(highSeverityEvent)).toBe(false); // High severity
        });
        it('should create complex combinations', () => {
            const highSeveritySpec = new event_specification_1.HighSeverityEventSpecification();
            const activeSpec = new event_specification_1.ActiveEventSpecification();
            const highRiskSpec = new event_specification_1.HighRiskEventSpecification();
            // (High severity AND active) OR high risk
            const complexSpec = highSeveritySpec.and(activeSpec).or(highRiskSpec);
            expect(complexSpec.isSatisfiedBy(highSeverityEvent)).toBe(true); // High severity, active, and high risk
            expect(complexSpec.isSatisfiedBy(highRiskEvent)).toBe(true); // High risk (even if not high severity)
            expect(complexSpec.isSatisfiedBy(lowRiskEvent)).toBe(false); // None of the conditions
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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