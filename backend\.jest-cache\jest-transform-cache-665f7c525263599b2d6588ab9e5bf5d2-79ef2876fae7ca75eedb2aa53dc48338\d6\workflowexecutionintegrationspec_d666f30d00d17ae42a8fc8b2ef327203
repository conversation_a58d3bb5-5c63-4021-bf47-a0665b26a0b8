7d157b4c61b5e36293f10fc172d1ae48
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const event_emitter_1 = require("@nestjs/event-emitter");
const integration_test_base_1 = require("../base/integration-test.base");
const test_configuration_service_1 = require("../config/test-configuration.service");
const test_data_service_1 = require("../fixtures/test-data.service");
const workflow_execution_module_1 = require("../../workflow-execution/workflow-execution.module");
const workflow_execution_engine_service_1 = require("../../workflow-execution/services/workflow-execution-engine.service");
const workflow_state_manager_service_1 = require("../../workflow-execution/services/workflow-state-manager.service");
const monitoring_module_1 = require("../../monitoring/monitoring.module");
/**
 * Workflow Execution Integration Tests
 *
 * Comprehensive integration testing for workflow execution engine including:
 * - End-to-end workflow execution scenarios
 * - Parallel processing validation
 * - Conditional logic testing
 * - Error handling and recovery
 * - State management and persistence
 * - Performance and load testing
 * - Cross-module integration validation
 */
describe('Workflow Execution Integration Tests', () => {
    let testSuite;
    beforeEach(async () => {
        testSuite = new WorkflowExecutionIntegrationTest();
        await testSuite.beforeEach();
    });
    afterEach(async () => {
        await testSuite.afterEach();
    });
    describe('Workflow Execution Lifecycle', () => {
        it('should start and complete a simple workflow execution', async () => {
            await testSuite.testSimpleWorkflowExecution();
        });
        it('should handle workflow execution with parallel steps', async () => {
            await testSuite.testParallelWorkflowExecution();
        });
        it('should execute conditional workflow branches correctly', async () => {
            await testSuite.testConditionalWorkflowExecution();
        });
        it('should handle workflow execution with loops', async () => {
            await testSuite.testLoopWorkflowExecution();
        });
        it('should manage workflow execution state correctly', async () => {
            await testSuite.testWorkflowStateManagement();
        });
    });
    describe('Error Handling and Recovery', () => {
        it('should handle step failures with retry mechanisms', async () => {
            await testSuite.testStepFailureRetry();
        });
        it('should execute compensation workflows on failure', async () => {
            await testSuite.testCompensationWorkflow();
        });
        it('should rollback to checkpoints on critical failures', async () => {
            await testSuite.testCheckpointRollback();
        });
        it('should handle external service failures with circuit breakers', async () => {
            await testSuite.testCircuitBreakerHandling();
        });
    });
    describe('Performance and Scalability', () => {
        it('should handle concurrent workflow executions', async () => {
            await testSuite.testConcurrentExecutions();
        });
        it('should meet performance benchmarks for execution startup', async () => {
            await testSuite.testExecutionStartupPerformance();
        });
        it('should efficiently manage memory during long-running workflows', async () => {
            await testSuite.testMemoryManagement();
        });
        it('should scale parallel step execution effectively', async () => {
            await testSuite.testParallelScaling();
        });
    });
    describe('API Integration', () => {
        it('should provide complete REST API functionality', async () => {
            await testSuite.testRestApiIntegration();
        });
        it('should handle authentication and authorization correctly', async () => {
            await testSuite.testAuthenticationIntegration();
        });
        it('should validate input data and return appropriate errors', async () => {
            await testSuite.testInputValidation();
        });
        it('should provide real-time execution monitoring', async () => {
            await testSuite.testRealTimeMonitoring();
        });
    });
    describe('Cross-Module Integration', () => {
        it('should integrate with monitoring infrastructure', async () => {
            await testSuite.testMonitoringIntegration();
        });
        it('should emit events for workflow lifecycle', async () => {
            await testSuite.testEventEmission();
        });
        it('should integrate with notification systems', async () => {
            await testSuite.testNotificationIntegration();
        });
        it('should maintain data consistency across modules', async () => {
            await testSuite.testDataConsistency();
        });
    });
});
class WorkflowExecutionIntegrationTest extends integration_test_base_1.IntegrationTestBase {
    async createTestingModule() {
        return testing_1.Test.createTestingModule({
            imports: [
                config_1.ConfigModule.forRoot({
                    isGlobal: true,
                    envFilePath: '.env.test',
                }),
                typeorm_1.TypeOrmModule.forRootAsync({
                    useFactory: (configService) => configService.getTestDatabaseConfig(),
                    inject: [test_configuration_service_1.TestConfigurationService],
                }),
                event_emitter_1.EventEmitterModule.forRoot(),
                workflow_execution_module_1.WorkflowExecutionModule,
                monitoring_module_1.MonitoringModule,
            ],
            providers: [
                test_configuration_service_1.TestConfigurationService,
                test_data_service_1.TestDataService,
            ],
        }).compile();
    }
    async setupTestData() {
        this.executionEngine = this.testingModule.get(workflow_execution_engine_service_1.WorkflowExecutionEngineService);
        this.stateManager = this.testingModule.get(workflow_state_manager_service_1.WorkflowStateManagerService);
        // Generate test templates
        this.testTemplates = await this.testDataService.generateWorkflowTemplates(5);
    }
    async testSimpleWorkflowExecution() {
        // Get simple template
        const template = this.testTemplates.find(t => t.definition.steps.length <= 3);
        expect(template).toBeDefined();
        // Start execution
        const { result: execution } = await this.measurePerformance(() => this.executionEngine.startExecution({
            templateId: template.id,
            executionName: 'Test Simple Execution',
            inputData: { test: 'data' },
            triggeredBy: 'test-user',
            priority: 'medium',
        }), 'simple_execution_start');
        expect(execution).toBeDefined();
        expect(execution.status).toBe('pending');
        // Wait for completion
        await this.waitFor(async () => {
            const updated = await this.stateManager.loadExecutionState(execution.id);
            return updated?.status === 'completed' || updated?.status === 'failed';
        }, 15000);
        // Verify final state
        const finalExecution = await this.stateManager.loadExecutionState(execution.id);
        expect(finalExecution.status).toBe('completed');
        expect(finalExecution.completedAt).toBeDefined();
        expect(finalExecution.duration).toBeGreaterThan(0);
        // Verify events were emitted
        const startEvent = this.getEventHistory('workflow.execution.started');
        const completeEvent = this.getEventHistory('workflow.execution.completed');
        expect(startEvent).toHaveLength(1);
        expect(completeEvent).toHaveLength(1);
    }
    async testParallelWorkflowExecution() {
        // Create template with parallel steps
        const parallelTemplate = await this.createParallelWorkflowTemplate();
        // Start execution
        const execution = await this.executionEngine.startExecution({
            templateId: parallelTemplate.id,
            executionName: 'Test Parallel Execution',
            inputData: { items: [1, 2, 3, 4, 5] },
            triggeredBy: 'test-user',
            executionConfig: {
                maxParallelSteps: 3,
                enableMetrics: true,
            },
        });
        // Wait for completion
        await this.waitFor(async () => {
            const updated = await this.stateManager.loadExecutionState(execution.id);
            return updated?.status === 'completed';
        }, 20000);
        // Verify parallel execution efficiency
        const finalExecution = await this.stateManager.loadExecutionState(execution.id);
        expect(finalExecution.performanceMetrics?.parallelEfficiency).toBeGreaterThan(50);
        // Verify all parallel steps completed
        const parallelSteps = Object.values(finalExecution.executionState.stepStates)
            .filter((state) => state.status === 'completed');
        expect(parallelSteps.length).toBeGreaterThan(1);
    }
    async testConditionalWorkflowExecution() {
        // Create template with conditional steps
        const conditionalTemplate = await this.createConditionalWorkflowTemplate();
        // Test true branch
        const trueExecution = await this.executionEngine.startExecution({
            templateId: conditionalTemplate.id,
            executionName: 'Test Conditional True',
            inputData: { condition: true },
            triggeredBy: 'test-user',
        });
        await this.waitFor(async () => {
            const updated = await this.stateManager.loadExecutionState(trueExecution.id);
            return updated?.status === 'completed';
        });
        // Test false branch
        const falseExecution = await this.executionEngine.startExecution({
            templateId: conditionalTemplate.id,
            executionName: 'Test Conditional False',
            inputData: { condition: false },
            triggeredBy: 'test-user',
        });
        await this.waitFor(async () => {
            const updated = await this.stateManager.loadExecutionState(falseExecution.id);
            return updated?.status === 'completed';
        });
        // Verify different branches were executed
        const trueResult = await this.stateManager.loadExecutionState(trueExecution.id);
        const falseResult = await this.stateManager.loadExecutionState(falseExecution.id);
        expect(trueResult.executionState.conditionalBranches).toBeDefined();
        expect(falseResult.executionState.conditionalBranches).toBeDefined();
        const trueBranch = Object.values(trueResult.executionState.conditionalBranches)[0];
        const falseBranch = Object.values(falseResult.executionState.conditionalBranches)[0];
        expect(trueBranch.selectedBranch).toBe('true');
        expect(falseBranch.selectedBranch).toBe('false');
    }
    async testLoopWorkflowExecution() {
        // Create template with loop steps
        const loopTemplate = await this.createLoopWorkflowTemplate();
        const execution = await this.executionEngine.startExecution({
            templateId: loopTemplate.id,
            executionName: 'Test Loop Execution',
            inputData: { items: ['a', 'b', 'c'] },
            triggeredBy: 'test-user',
        });
        await this.waitFor(async () => {
            const updated = await this.stateManager.loadExecutionState(execution.id);
            return updated?.status === 'completed';
        }, 25000);
        // Verify loop execution
        const finalExecution = await this.stateManager.loadExecutionState(execution.id);
        expect(finalExecution.executionState.loops).toBeDefined();
        const loopState = Object.values(finalExecution.executionState.loops)[0];
        expect(loopState.completedIterations).toBe(3);
        expect(loopState.results).toHaveLength(3);
    }
    async testWorkflowStateManagement() {
        const template = this.testTemplates[0];
        // Start execution
        const execution = await this.executionEngine.startExecution({
            templateId: template.id,
            executionName: 'Test State Management',
            inputData: { test: 'state' },
            triggeredBy: 'test-user',
        });
        // Create checkpoint
        const checkpointId = await this.stateManager.createStateCheckpoint(execution.id);
        expect(checkpointId).toBeDefined();
        // Modify state
        await this.stateManager.saveExecutionState(execution.id, {
            contextData: { modified: true },
        });
        // Verify state change
        let modifiedExecution = await this.stateManager.loadExecutionState(execution.id);
        expect(modifiedExecution.contextData.modified).toBe(true);
        // Rollback to checkpoint
        await this.stateManager.restoreFromCheckpoint(execution.id, checkpointId);
        // Verify rollback
        const rolledBackExecution = await this.stateManager.loadExecutionState(execution.id);
        expect(rolledBackExecution.contextData.modified).toBeUndefined();
    }
    async testStepFailureRetry() {
        // Create template with failing step
        const failingTemplate = await this.createFailingWorkflowTemplate();
        const execution = await this.executionEngine.startExecution({
            templateId: failingTemplate.id,
            executionName: 'Test Retry Mechanism',
            inputData: { shouldFail: true },
            triggeredBy: 'test-user',
            executionConfig: {
                enableAutoRetry: true,
                defaultMaxRetries: 3,
            },
        });
        await this.waitFor(async () => {
            const updated = await this.stateManager.loadExecutionState(execution.id);
            return updated?.status === 'failed' || updated?.status === 'completed';
        }, 30000);
        // Verify retry attempts
        const finalExecution = await this.stateManager.loadExecutionState(execution.id);
        expect(finalExecution.retryConfig?.retryHistory).toBeDefined();
        expect(finalExecution.retryConfig.retryHistory.length).toBeGreaterThan(0);
    }
    async testRestApiIntegration() {
        const template = this.testTemplates[0];
        // Test start execution endpoint
        const startResponse = await this.post('/workflow-execution/start')
            .send({
            templateId: template.id,
            executionName: 'API Test Execution',
            inputData: { api: 'test' },
            priority: 'high',
        });
        this.expectValidResponse(startResponse, 201);
        const executionId = startResponse.body.id;
        // Test get status endpoint
        const statusResponse = await this.get(`/workflow-execution/${executionId}/status`);
        this.expectValidResponse(statusResponse);
        expect(statusResponse.body.status).toBeDefined();
        // Test get details endpoint
        const detailsResponse = await this.get(`/workflow-execution/${executionId}?includeSteps=true`);
        this.expectValidResponse(detailsResponse);
        expect(detailsResponse.body.steps).toBeDefined();
        // Test pause endpoint
        const pauseResponse = await this.post(`/workflow-execution/${executionId}/pause`)
            .send({ reason: 'Testing pause functionality' });
        this.expectValidResponse(pauseResponse);
        // Test resume endpoint
        const resumeResponse = await this.post(`/workflow-execution/${executionId}/resume`);
        this.expectValidResponse(resumeResponse);
    }
    async testAuthenticationIntegration() {
        const template = this.testTemplates[0];
        // Test unauthorized access
        const unauthorizedResponse = await request(this.httpServer)
            .post('/workflow-execution/start')
            .send({
            templateId: template.id,
            executionName: 'Unauthorized Test',
            inputData: {},
        });
        this.expectUnauthorized(unauthorizedResponse);
        // Test insufficient permissions
        const viewerResponse = await this.post('/workflow-execution/start', 'test-viewer')
            .send({
            templateId: template.id,
            executionName: 'Viewer Test',
            inputData: {},
        });
        this.expectForbidden(viewerResponse);
        // Test valid permissions
        const userResponse = await this.post('/workflow-execution/start', 'test-user')
            .send({
            templateId: template.id,
            executionName: 'User Test',
            inputData: {},
        });
        this.expectValidResponse(userResponse, 201);
    }
    async testRealTimeMonitoring() {
        const template = this.testTemplates[0];
        // Start execution
        const execution = await this.executionEngine.startExecution({
            templateId: template.id,
            executionName: 'Monitoring Test',
            inputData: { monitor: true },
            triggeredBy: 'test-user',
            executionConfig: { enableMetrics: true },
        });
        // Wait for some execution progress
        await this.sleep(2000);
        // Test metrics endpoint
        const metricsResponse = await this.get(`/workflow-execution/${execution.id}/metrics`);
        this.expectValidResponse(metricsResponse);
        expect(metricsResponse.body.performance).toBeDefined();
        expect(metricsResponse.body.resourceUtilization).toBeDefined();
    }
    async testEventEmission() {
        const template = this.testTemplates[0];
        this.clearEventHistory();
        const execution = await this.executionEngine.startExecution({
            templateId: template.id,
            executionName: 'Event Test',
            inputData: { events: true },
            triggeredBy: 'test-user',
        });
        // Wait for execution events
        await this.waitForEvent('workflow.execution.started', 5000);
        // Verify event data
        const startEvent = this.getEventHistory('workflow.execution.started')[0];
        expect(startEvent.args[0].executionId).toBe(execution.id);
        expect(startEvent.args[0].templateId).toBe(template.id);
    }
    /**
     * Helper methods for creating test templates
     */
    async createParallelWorkflowTemplate() {
        return this.testDataService.templateRepository.save({
            name: 'Parallel Test Template',
            description: 'Template with parallel steps',
            definition: {
                steps: [
                    {
                        id: 'parallel_step',
                        type: 'parallel',
                        config: {
                            steps: [
                                { id: 'p1', type: 'notification', config: { message: 'Parallel 1' } },
                                { id: 'p2', type: 'notification', config: { message: 'Parallel 2' } },
                                { id: 'p3', type: 'notification', config: { message: 'Parallel 3' } },
                            ],
                            maxConcurrency: 3,
                        },
                    },
                ],
            },
            createdBy: 'test-system',
            updatedBy: 'test-system',
        });
    }
    async createConditionalWorkflowTemplate() {
        return this.testDataService.templateRepository.save({
            name: 'Conditional Test Template',
            description: 'Template with conditional logic',
            definition: {
                steps: [
                    {
                        id: 'conditional_step',
                        type: 'conditional',
                        config: {
                            condition: { type: 'comparison', left: 'condition', operator: 'equals', right: true },
                            trueBranch: [
                                { id: 'true_step', type: 'notification', config: { message: 'True branch' } },
                            ],
                            falseBranch: [
                                { id: 'false_step', type: 'notification', config: { message: 'False branch' } },
                            ],
                        },
                    },
                ],
            },
            createdBy: 'test-system',
            updatedBy: 'test-system',
        });
    }
    async createLoopWorkflowTemplate() {
        return this.testDataService.templateRepository.save({
            name: 'Loop Test Template',
            description: 'Template with loop processing',
            definition: {
                steps: [
                    {
                        id: 'loop_step',
                        type: 'loop',
                        config: {
                            type: 'for_each',
                            iterableVariable: 'items',
                            itemVariable: 'item',
                            steps: [
                                { id: 'process_item', type: 'notification', config: { message: 'Processing item' } },
                            ],
                        },
                    },
                ],
            },
            createdBy: 'test-system',
            updatedBy: 'test-system',
        });
    }
    async createFailingWorkflowTemplate() {
        return this.testDataService.templateRepository.save({
            name: 'Failing Test Template',
            description: 'Template that fails for retry testing',
            definition: {
                steps: [
                    {
                        id: 'failing_step',
                        type: 'external_service',
                        config: {
                            service: 'failing_service',
                            endpoint: 'http://localhost:9999/fail',
                            method: 'POST',
                            timeout: 5000,
                        },
                    },
                ],
            },
            createdBy: 'test-system',
            updatedBy: 'test-system',
        });
    }
    // Additional test methods would be implemented here...
    async testCompensationWorkflow() {
        // Implementation for compensation workflow testing
    }
    async testCheckpointRollback() {
        // Implementation for checkpoint rollback testing
    }
    async testCircuitBreakerHandling() {
        // Implementation for circuit breaker testing
    }
    async testConcurrentExecutions() {
        // Implementation for concurrent execution testing
    }
    async testExecutionStartupPerformance() {
        // Implementation for startup performance testing
    }
    async testMemoryManagement() {
        // Implementation for memory management testing
    }
    async testParallelScaling() {
        // Implementation for parallel scaling testing
    }
    async testInputValidation() {
        // Implementation for input validation testing
    }
    async testMonitoringIntegration() {
        // Implementation for monitoring integration testing
    }
    async testNotificationIntegration() {
        // Implementation for notification integration testing
    }
    async testDataConsistency() {
        // Implementation for data consistency testing
    }
}
__decorate([
    (0, integration_test_base_1.IntegrationTest)('Simple workflow execution end-to-end'),
    (0, integration_test_base_1.PerformanceTest)(10000) // 10 seconds max
    ,
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
], WorkflowExecutionIntegrationTest.prototype, "testSimpleWorkflowExecution", null);
__decorate([
    (0, integration_test_base_1.IntegrationTest)('Parallel workflow execution with concurrent steps'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_b = typeof Promise !== "undefined" && Promise) === "function" ? _b : Object)
], WorkflowExecutionIntegrationTest.prototype, "testParallelWorkflowExecution", null);
__decorate([
    (0, integration_test_base_1.IntegrationTest)('Conditional workflow execution with branching logic'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_c = typeof Promise !== "undefined" && Promise) === "function" ? _c : Object)
], WorkflowExecutionIntegrationTest.prototype, "testConditionalWorkflowExecution", null);
__decorate([
    (0, integration_test_base_1.IntegrationTest)('Loop workflow execution with iteration control'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_d = typeof Promise !== "undefined" && Promise) === "function" ? _d : Object)
], WorkflowExecutionIntegrationTest.prototype, "testLoopWorkflowExecution", null);
__decorate([
    (0, integration_test_base_1.IntegrationTest)('Workflow state management and persistence'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_e = typeof Promise !== "undefined" && Promise) === "function" ? _e : Object)
], WorkflowExecutionIntegrationTest.prototype, "testWorkflowStateManagement", null);
__decorate([
    (0, integration_test_base_1.IntegrationTest)('Step failure retry mechanisms'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_f = typeof Promise !== "undefined" && Promise) === "function" ? _f : Object)
], WorkflowExecutionIntegrationTest.prototype, "testStepFailureRetry", null);
__decorate([
    (0, integration_test_base_1.IntegrationTest)('REST API integration and functionality'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_g = typeof Promise !== "undefined" && Promise) === "function" ? _g : Object)
], WorkflowExecutionIntegrationTest.prototype, "testRestApiIntegration", null);
__decorate([
    (0, integration_test_base_1.IntegrationTest)('Authentication and authorization integration'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_h = typeof Promise !== "undefined" && Promise) === "function" ? _h : Object)
], WorkflowExecutionIntegrationTest.prototype, "testAuthenticationIntegration", null);
__decorate([
    (0, integration_test_base_1.IntegrationTest)('Real-time monitoring integration'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_j = typeof Promise !== "undefined" && Promise) === "function" ? _j : Object)
], WorkflowExecutionIntegrationTest.prototype, "testRealTimeMonitoring", null);
__decorate([
    (0, integration_test_base_1.IntegrationTest)('Event emission and handling'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_k = typeof Promise !== "undefined" && Promise) === "function" ? _k : Object)
], WorkflowExecutionIntegrationTest.prototype, "testEventEmission", null);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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