{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\entities\\ai-model.entity.ts", "mappings": ";;;AAAA,8FAAyF;AAEzF,2FAA8E;AAC9E,yGAA2F;AAC3F,mHAAqG;AACrG,uHAAyG;AAkCzG,IAAY,UASX;AATD,WAAY,UAAU;IACpB,+BAAiB,CAAA;IACjB,iCAAmB,CAAA;IACnB,uCAAyB,CAAA;IACzB,qCAAuB,CAAA;IACvB,2CAA6B,CAAA;IAC7B,2CAA6B,CAAA;IAC7B,qCAAuB,CAAA;IACvB,qCAAuB,CAAA;AACzB,CAAC,EATW,UAAU,0BAAV,UAAU,QASrB;AAED,IAAY,SAWX;AAXD,WAAY,SAAS;IACnB,8CAAiC,CAAA;IACjC,8CAAiC,CAAA;IACjC,sCAAyB,CAAA;IACzB,sCAAyB,CAAA;IACzB,oDAAuC,CAAA;IACvC,wDAA2C,CAAA;IAC3C,wBAAW,CAAA;IACX,gDAAmC,CAAA;IACnC,8DAAiD,CAAA;IACjD,kCAAqB,CAAA;AACvB,CAAC,EAXW,SAAS,yBAAT,SAAS,QAWpB;AAED,IAAY,WAQX;AARD,WAAY,WAAW;IACrB,gCAAiB,CAAA;IACjB,oCAAqB,CAAA;IACrB,oCAAqB,CAAA;IACrB,sCAAuB,CAAA;IACvB,0CAA2B,CAAA;IAC3B,oCAAqB,CAAA;IACrB,gCAAiB,CAAA;AACnB,CAAC,EARW,WAAW,2BAAX,WAAW,QAQtB;AAED,IAAY,YAKX;AALD,WAAY,YAAY;IACtB,mCAAmB,CAAA;IACnB,uCAAuB,CAAA;IACvB,qCAAqB,CAAA;IACrB,mCAAmB,CAAA;AACrB,CAAC,EALW,YAAY,4BAAZ,YAAY,QAKvB;AA4CD,MAAa,OAAQ,SAAQ,uCAA+B;IAC1D,YAAoB,KAAmB,EAAE,EAAmB;QAC1D,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QACjB,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAEM,MAAM,CAAC,MAAM,CAAC,KAAoE,EAAE,EAAmB;QAC5G,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC;YAC1B,GAAG,KAAK;YACR,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,GAAG;YACd,SAAS,EAAE,GAAG;SACf,EAAE,EAAE,CAAC,CAAC;QAEP,OAAO,CAAC,cAAc,CAAC,IAAI,mDAAmB,CAAC,OAAO,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;QACzG,OAAO,OAAO,CAAC;IACjB,CAAC;IAEM,MAAM,CAAC,YAAY,CAAC,KAAmB,EAAE,EAAkB;QAChE,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAChC,CAAC;IAED,UAAU;IACV,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IACzB,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAC5B,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC7B,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;IAC9B,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAC3B,CAAC;IAED,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;IAClC,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;IAChC,CAAC;IAED,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;IACjC,CAAC;IAED,IAAI,oBAAoB;QACtB,OAAO,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC;IACzC,CAAC;IAED,IAAI,kBAAkB;QACpB,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;IAC5C,CAAC;IAED,IAAI,IAAI;QACN,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC7B,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAC3B,CAAC;IAED,IAAI,qBAAqB;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC;IAC1C,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;IAChC,CAAC;IAED,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;IACpC,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC7B,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;IACpC,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;IAC9B,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;IAC9B,CAAC;IAED,iBAAiB;IAEjB;;OAEG;IACI,QAAQ;QACb,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,WAAW,CAAC,QAAQ,EAAE,CAAC;YAC/C,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QACzC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;QACvC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAElC,IAAI,CAAC,cAAc,CAAC,IAAI,gEAAyB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,cAAc,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;IACnH,CAAC;IAED;;OAEG;IACI,UAAU;QACf,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QACzC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC,QAAQ,CAAC;QACzC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAElC,IAAI,CAAC,cAAc,CAAC,IAAI,gEAAyB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,cAAc,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;IACrH,CAAC;IAED;;OAEG;IACI,OAAO;QACZ,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QACzC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC,QAAQ,CAAC;QACzC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAElC,IAAI,CAAC,cAAc,CAAC,IAAI,gEAAyB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,cAAc,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;IACrH,CAAC;IAED;;OAEG;IACI,wBAAwB,CAAC,OAAkC;QAChE,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG;YACvB,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW;YACzB,GAAG,OAAO;YACV,WAAW,EAAE,IAAI,IAAI,EAAE;SACxB,CAAC;QACF,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAElC,IAAI,CAAC,cAAc,CAAC,IAAI,0EAA8B,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;IAC3F,CAAC;IAED;;OAEG;IACI,UAAU,CAAC,KAAa;QAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC,CAAC;QAE5D,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC;YAC/C,MAAM,IAAI,KAAK,CAAC,6CAA6C,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC;QACnG,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC;QACjC,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;QACjC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;IACpC,CAAC;IAED;;OAEG;IACI,gBAAgB;QACrB,OAAO,CACL,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,WAAW,CAAC,MAAM;YACxC,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAC1D,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,QAAgB;QACtC,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACI,eAAe;QACpB,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,aAAa,KAAK,CAAC,EAAE,CAAC;YAC/C,OAAO,GAAG,CAAC;QACb,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,aAAa,CAAC;IAC1F,CAAC;IAED;;OAEG;IACI,cAAc;QACnB,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,aAAa,KAAK,CAAC,EAAE,CAAC;YAC/C,OAAO,GAAG,CAAC;QACb,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,aAAa,CAAC;IAC1F,CAAC;IAED;;OAEG;IACI,yBAAyB;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;QAEvC,MAAM,aAAa,GAAG,OAAO,CAAC,QAAQ,CAAC;QACvC,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC,CAAC,CAAC,uBAAuB;QAC7F,MAAM,iBAAiB,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACjD,MAAM,gBAAgB,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAE/C,OAAO,CAAC,aAAa,GAAG,GAAG,GAAG,YAAY,GAAG,GAAG,GAAG,iBAAiB,GAAG,IAAI,GAAG,gBAAgB,GAAG,IAAI,CAAC,CAAC;IACzG,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,MAAmC;QAC5D,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG;YACzB,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa;YAC3B,GAAG,MAAM;SACV,CAAC;QACF,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAElC,IAAI,CAAC,cAAc,CAAC,IAAI,8EAAgC,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;IAC/F,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,GAAW;QACvB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC1B,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QACpC,CAAC;IACH,CAAC;IAED;;OAEG;IACI,SAAS,CAAC,GAAW;QAC1B,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;QACzD,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;IACpC,CAAC;IAED;;OAEG;IACI,YAAY;QACjB,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC;QACjF,OAAO,cAAc,GAAG,GAAG,CAAC,CAAC,gBAAgB;IAC/C,CAAC;IAED;;OAEG;IACI,cAAc;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC;IACnE,CAAC;IAED;;OAEG;IACI,iBAAiB;QACtB,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,IAAI,IAAI,EAAE,CAAC;QACxC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;IACpC,CAAC;IAED;;OAEG;IACI,cAAc;QACnB,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QACnC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;QACvC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;IACpC,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,QAA6B;QACjD,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACpB,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ;YACtB,GAAG,QAAQ;SACZ,CAAC;QACF,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;IACpC,CAAC;IAES,kBAAkB;QAC1B,KAAK,CAAC,kBAAkB,EAAE,CAAC;QAE3B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5D,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClE,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7D,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC;YAC7D,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;QACxC,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5D,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,EAAE,CAAC;YACxD,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACpD,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,qBAAqB,IAAI,CAAC,EAAE,CAAC;YAC1C,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/C,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,IAAI,CAAC,EAAE,CAAC;YAC1C,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,GAAG,CAAC,EAAE,CAAC;YACzC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,SAAS,IAAI,CAAC,EAAE,CAAC;YAC5C,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;CACF;AAtWD,0BAsWC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\entities\\ai-model.entity.ts"], "sourcesContent": ["import { BaseAggregateRoot } from '../../../../shared-kernel/domain/base-aggregate-root';\r\nimport { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { AIModelCreatedEvent } from '../events/ai-model-created.domain-event';\r\nimport { AIModelStatusChangedEvent } from '../events/ai-model-status-changed.domain-event';\r\nimport { AIModelPerformanceUpdatedEvent } from '../events/ai-model-performance-updated.domain-event';\r\nimport { AIModelConfigurationUpdatedEvent } from '../events/ai-model-configuration-updated.domain-event';\r\n\r\n/**\r\n * AI Model Entity\r\n * \r\n * Domain entity representing an AI model with its configuration,\r\n * performance metrics, and operational status. Implements domain\r\n * logic for model lifecycle management and validation.\r\n */\r\n\r\nexport interface AIModelProps {\r\n  name: string;\r\n  version: string;\r\n  provider: AIProvider;\r\n  modelType: ModelType;\r\n  configuration: ModelConfiguration;\r\n  performance: ModelPerformance;\r\n  status: ModelStatus;\r\n  capabilities: ModelCapabilities;\r\n  resourceRequirements: ResourceRequirements;\r\n  supportedTaskTypes: string[];\r\n  tags: string[];\r\n  priority: number;\r\n  weight: number;\r\n  maxConcurrentRequests: number;\r\n  currentLoad: number;\r\n  lastHealthCheck?: Date;\r\n  lastUsed?: Date;\r\n  deployedAt?: Date;\r\n  metadata: Record<string, any>;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n}\r\n\r\nexport enum AIProvider {\r\n  OPENAI = 'openai',\r\n  BEDROCK = 'bedrock',\r\n  TENSORFLOW = 'tensorflow',\r\n  PYTHON_AI = 'python_ai',\r\n  HUGGING_FACE = 'hugging_face',\r\n  AZURE_OPENAI = 'azure_openai',\r\n  GOOGLE_AI = 'google_ai',\r\n  ANTHROPIC = 'anthropic'\r\n}\r\n\r\nexport enum ModelType {\r\n  LANGUAGE_MODEL = 'language_model',\r\n  CLASSIFICATION = 'classification',\r\n  REGRESSION = 'regression',\r\n  CLUSTERING = 'clustering',\r\n  ANOMALY_DETECTION = 'anomaly_detection',\r\n  PATTERN_RECOGNITION = 'pattern_recognition',\r\n  NLP = 'nlp',\r\n  COMPUTER_VISION = 'computer_vision',\r\n  REINFORCEMENT_LEARNING = 'reinforcement_learning',\r\n  ENSEMBLE = 'ensemble'\r\n}\r\n\r\nexport enum ModelStatus {\r\n  ACTIVE = 'active',\r\n  INACTIVE = 'inactive',\r\n  TRAINING = 'training',\r\n  DEPLOYING = 'deploying',\r\n  MAINTENANCE = 'maintenance',\r\n  ARCHIVED = 'archived',\r\n  FAILED = 'failed'\r\n}\r\n\r\nexport enum HealthStatus {\r\n  HEALTHY = 'healthy',\r\n  UNHEALTHY = 'unhealthy',\r\n  DEGRADED = 'degraded',\r\n  UNKNOWN = 'unknown'\r\n}\r\n\r\nexport interface ModelConfiguration {\r\n  endpoint?: string;\r\n  apiKey?: string;\r\n  timeout: number;\r\n  retries: number;\r\n  batchSize: number;\r\n  customSettings: Record<string, any>;\r\n}\r\n\r\nexport interface ModelPerformance {\r\n  totalRequests: number;\r\n  successfulRequests: number;\r\n  failedRequests: number;\r\n  averageLatency: number;\r\n  p95Latency: number;\r\n  p99Latency: number;\r\n  accuracy: number;\r\n  precision: number;\r\n  recall: number;\r\n  f1Score: number;\r\n  throughput: number;\r\n  lastUpdated: Date;\r\n}\r\n\r\nexport interface ModelCapabilities {\r\n  maxInputLength: number;\r\n  maxOutputLength: number;\r\n  supportsBatch: boolean;\r\n  supportsStreaming: boolean;\r\n  supportsFineTuning: boolean;\r\n  languages: string[];\r\n  modalities: string[];\r\n}\r\n\r\nexport interface ResourceRequirements {\r\n  cpu: number;\r\n  memory: number;\r\n  gpu: number;\r\n  storage: number;\r\n  bandwidth: number;\r\n}\r\n\r\nexport class AIModel extends BaseAggregateRoot<AIModelProps> {\r\n  private constructor(props: AIModelProps, id?: UniqueEntityId) {\r\n    super(props, id);\r\n    this.validateInvariants();\r\n  }\r\n\r\n  public static create(props: Omit<AIModelProps, 'createdAt' | 'updatedAt' | 'currentLoad'>, id?: UniqueEntityId): AIModel {\r\n    const now = new Date();\r\n    const aiModel = new AIModel({\r\n      ...props,\r\n      currentLoad: 0,\r\n      createdAt: now,\r\n      updatedAt: now\r\n    }, id);\r\n\r\n    aiModel.addDomainEvent(new AIModelCreatedEvent(aiModel.id, props.name, props.provider, props.modelType));\r\n    return aiModel;\r\n  }\r\n\r\n  public static reconstitute(props: AIModelProps, id: UniqueEntityId): AIModel {\r\n    return new AIModel(props, id);\r\n  }\r\n\r\n  // Getters\r\n  get name(): string {\r\n    return this.props.name;\r\n  }\r\n\r\n  get version(): string {\r\n    return this.props.version;\r\n  }\r\n\r\n  get provider(): AIProvider {\r\n    return this.props.provider;\r\n  }\r\n\r\n  get modelType(): ModelType {\r\n    return this.props.modelType;\r\n  }\r\n\r\n  get status(): ModelStatus {\r\n    return this.props.status;\r\n  }\r\n\r\n  get configuration(): ModelConfiguration {\r\n    return this.props.configuration;\r\n  }\r\n\r\n  get performance(): ModelPerformance {\r\n    return this.props.performance;\r\n  }\r\n\r\n  get capabilities(): ModelCapabilities {\r\n    return this.props.capabilities;\r\n  }\r\n\r\n  get resourceRequirements(): ResourceRequirements {\r\n    return this.props.resourceRequirements;\r\n  }\r\n\r\n  get supportedTaskTypes(): string[] {\r\n    return [...this.props.supportedTaskTypes];\r\n  }\r\n\r\n  get tags(): string[] {\r\n    return [...this.props.tags];\r\n  }\r\n\r\n  get priority(): number {\r\n    return this.props.priority;\r\n  }\r\n\r\n  get weight(): number {\r\n    return this.props.weight;\r\n  }\r\n\r\n  get maxConcurrentRequests(): number {\r\n    return this.props.maxConcurrentRequests;\r\n  }\r\n\r\n  get currentLoad(): number {\r\n    return this.props.currentLoad;\r\n  }\r\n\r\n  get lastHealthCheck(): Date | undefined {\r\n    return this.props.lastHealthCheck;\r\n  }\r\n\r\n  get lastUsed(): Date | undefined {\r\n    return this.props.lastUsed;\r\n  }\r\n\r\n  get deployedAt(): Date | undefined {\r\n    return this.props.deployedAt;\r\n  }\r\n\r\n  get metadata(): Record<string, any> {\r\n    return { ...this.props.metadata };\r\n  }\r\n\r\n  get createdAt(): Date {\r\n    return this.props.createdAt;\r\n  }\r\n\r\n  get updatedAt(): Date {\r\n    return this.props.updatedAt;\r\n  }\r\n\r\n  // Domain methods\r\n\r\n  /**\r\n   * Activates the AI model\r\n   */\r\n  public activate(): void {\r\n    if (this.props.status === ModelStatus.ARCHIVED) {\r\n      throw new Error('Cannot activate archived model');\r\n    }\r\n    \r\n    const previousStatus = this.props.status;\r\n    this.props.status = ModelStatus.ACTIVE;\r\n    this.props.updatedAt = new Date();\r\n    \r\n    this.addDomainEvent(new AIModelStatusChangedEvent(this.id, this.props.name, previousStatus, ModelStatus.ACTIVE));\r\n  }\r\n\r\n  /**\r\n   * Deactivates the AI model\r\n   */\r\n  public deactivate(): void {\r\n    const previousStatus = this.props.status;\r\n    this.props.status = ModelStatus.INACTIVE;\r\n    this.props.updatedAt = new Date();\r\n    \r\n    this.addDomainEvent(new AIModelStatusChangedEvent(this.id, this.props.name, previousStatus, ModelStatus.INACTIVE));\r\n  }\r\n\r\n  /**\r\n   * Archives the AI model\r\n   */\r\n  public archive(): void {\r\n    const previousStatus = this.props.status;\r\n    this.props.status = ModelStatus.ARCHIVED;\r\n    this.props.updatedAt = new Date();\r\n    \r\n    this.addDomainEvent(new AIModelStatusChangedEvent(this.id, this.props.name, previousStatus, ModelStatus.ARCHIVED));\r\n  }\r\n\r\n  /**\r\n   * Updates performance metrics\r\n   */\r\n  public updatePerformanceMetrics(metrics: Partial<ModelPerformance>): void {\r\n    this.props.performance = {\r\n      ...this.props.performance,\r\n      ...metrics,\r\n      lastUpdated: new Date(),\r\n    };\r\n    this.props.updatedAt = new Date();\r\n\r\n    this.addDomainEvent(new AIModelPerformanceUpdatedEvent(this.id, this.props.performance));\r\n  }\r\n\r\n  /**\r\n   * Updates current load\r\n   */\r\n  public updateLoad(delta: number): void {\r\n    const newLoad = Math.max(0, this.props.currentLoad + delta);\r\n    \r\n    if (newLoad > this.props.maxConcurrentRequests) {\r\n      throw new Error(`Load exceeds maximum concurrent requests: ${this.props.maxConcurrentRequests}`);\r\n    }\r\n    \r\n    this.props.currentLoad = newLoad;\r\n    this.props.lastUsed = new Date();\r\n    this.props.updatedAt = new Date();\r\n  }\r\n\r\n  /**\r\n   * Checks if model can handle additional requests\r\n   */\r\n  public canHandleRequest(): boolean {\r\n    return (\r\n      this.props.status === ModelStatus.ACTIVE &&\r\n      this.props.currentLoad < this.props.maxConcurrentRequests\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Checks if model supports a specific task type\r\n   */\r\n  public supportsTaskType(taskType: string): boolean {\r\n    return this.props.supportedTaskTypes.includes(taskType);\r\n  }\r\n\r\n  /**\r\n   * Gets model availability percentage\r\n   */\r\n  public getAvailability(): number {\r\n    if (this.props.performance.totalRequests === 0) {\r\n      return 1.0;\r\n    }\r\n    \r\n    return this.props.performance.successfulRequests / this.props.performance.totalRequests;\r\n  }\r\n\r\n  /**\r\n   * Gets model success rate\r\n   */\r\n  public getSuccessRate(): number {\r\n    if (this.props.performance.totalRequests === 0) {\r\n      return 1.0;\r\n    }\r\n    \r\n    return this.props.performance.successfulRequests / this.props.performance.totalRequests;\r\n  }\r\n\r\n  /**\r\n   * Calculates model performance score\r\n   */\r\n  public calculatePerformanceScore(): number {\r\n    const metrics = this.props.performance;\r\n    \r\n    const accuracyScore = metrics.accuracy;\r\n    const latencyScore = Math.max(0, 1 - metrics.averageLatency / 10000); // Normalize to 10s max\r\n    const availabilityScore = this.getAvailability();\r\n    const successRateScore = this.getSuccessRate();\r\n\r\n    return (accuracyScore * 0.3 + latencyScore * 0.2 + availabilityScore * 0.25 + successRateScore * 0.25);\r\n  }\r\n\r\n  /**\r\n   * Updates model configuration\r\n   */\r\n  public updateConfiguration(config: Partial<ModelConfiguration>): void {\r\n    this.props.configuration = {\r\n      ...this.props.configuration,\r\n      ...config,\r\n    };\r\n    this.props.updatedAt = new Date();\r\n\r\n    this.addDomainEvent(new AIModelConfigurationUpdatedEvent(this.id, this.props.configuration));\r\n  }\r\n\r\n  /**\r\n   * Adds a tag to the model\r\n   */\r\n  public addTag(tag: string): void {\r\n    if (!this.props.tags.includes(tag)) {\r\n      this.props.tags.push(tag);\r\n      this.props.updatedAt = new Date();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Removes a tag from the model\r\n   */\r\n  public removeTag(tag: string): void {\r\n    this.props.tags = this.props.tags.filter(t => t !== tag);\r\n    this.props.updatedAt = new Date();\r\n  }\r\n\r\n  /**\r\n   * Checks if model is overloaded\r\n   */\r\n  public isOverloaded(): boolean {\r\n    const loadPercentage = this.props.currentLoad / this.props.maxConcurrentRequests;\r\n    return loadPercentage > 0.8; // 80% threshold\r\n  }\r\n\r\n  /**\r\n   * Gets model utilization percentage\r\n   */\r\n  public getUtilization(): number {\r\n    return this.props.currentLoad / this.props.maxConcurrentRequests;\r\n  }\r\n\r\n  /**\r\n   * Updates health check timestamp\r\n   */\r\n  public recordHealthCheck(): void {\r\n    this.props.lastHealthCheck = new Date();\r\n    this.props.updatedAt = new Date();\r\n  }\r\n\r\n  /**\r\n   * Updates deployment timestamp\r\n   */\r\n  public markAsDeployed(): void {\r\n    this.props.deployedAt = new Date();\r\n    this.props.status = ModelStatus.ACTIVE;\r\n    this.props.updatedAt = new Date();\r\n  }\r\n\r\n  /**\r\n   * Updates metadata\r\n   */\r\n  public updateMetadata(metadata: Record<string, any>): void {\r\n    this.props.metadata = {\r\n      ...this.props.metadata,\r\n      ...metadata,\r\n    };\r\n    this.props.updatedAt = new Date();\r\n  }\r\n\r\n  protected validateInvariants(): void {\r\n    super.validateInvariants();\r\n\r\n    if (!this.props.name || this.props.name.trim().length === 0) {\r\n      throw new Error('AI Model name is required');\r\n    }\r\n\r\n    if (!this.props.version || this.props.version.trim().length === 0) {\r\n      throw new Error('AI Model version is required');\r\n    }\r\n\r\n    if (!Object.values(AIProvider).includes(this.props.provider)) {\r\n      throw new Error('Invalid AI provider');\r\n    }\r\n\r\n    if (!Object.values(ModelType).includes(this.props.modelType)) {\r\n      throw new Error('Invalid model type');\r\n    }\r\n\r\n    if (!Object.values(ModelStatus).includes(this.props.status)) {\r\n      throw new Error('Invalid model status');\r\n    }\r\n\r\n    if (this.props.priority < 0 || this.props.priority > 10) {\r\n      throw new Error('Priority must be between 0 and 10');\r\n    }\r\n\r\n    if (this.props.weight < 0 || this.props.weight > 10) {\r\n      throw new Error('Weight must be between 0 and 10');\r\n    }\r\n\r\n    if (this.props.maxConcurrentRequests <= 0) {\r\n      throw new Error('Max concurrent requests must be greater than 0');\r\n    }\r\n\r\n    if (this.props.currentLoad < 0) {\r\n      throw new Error('Current load cannot be negative');\r\n    }\r\n\r\n    if (this.props.supportedTaskTypes.length === 0) {\r\n      throw new Error('Model must support at least one task type');\r\n    }\r\n\r\n    if (this.props.configuration.timeout <= 0) {\r\n      throw new Error('Configuration timeout must be greater than 0');\r\n    }\r\n\r\n    if (this.props.configuration.retries < 0) {\r\n      throw new Error('Configuration retries cannot be negative');\r\n    }\r\n\r\n    if (this.props.configuration.batchSize <= 0) {\r\n      throw new Error('Configuration batch size must be greater than 0');\r\n    }\r\n  }\r\n}"], "version": 3}