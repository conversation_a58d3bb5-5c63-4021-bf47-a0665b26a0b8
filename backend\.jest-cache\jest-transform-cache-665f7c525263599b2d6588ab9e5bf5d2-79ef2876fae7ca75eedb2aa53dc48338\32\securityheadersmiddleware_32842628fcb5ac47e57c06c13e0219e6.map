{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\headers\\security-headers.middleware.ts", "mappings": ";;;;;;;;;;;;;AAAA,2CAA4D;AAE5D,2CAA+C;AAC/C,6CAAyC;AACzC,+CAA2C;AAE3C;;;GAGG;AAEI,IAAM,yBAAyB,GAA/B,MAAM,yBAAyB;IACpC,YACmB,aAA4B,EAC5B,SAAoB,EACpB,UAAsB;QAFtB,kBAAa,GAAb,aAAa,CAAe;QAC5B,cAAS,GAAT,SAAS,CAAW;QACpB,eAAU,GAAV,UAAU,CAAY;IACtC,CAAC;IAEJ,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;QACjD,IAAI,CAAC;YACH,0BAA0B;YAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC;YACrD,GAAG,CAAC,SAAS,CAAC,yBAAyB,EAAE,SAAS,CAAC,CAAC;QACtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACvD,2BAA2B;YAC3B,GAAG,CAAC,SAAS,CAAC,yBAAyB,EAAE,oBAAoB,CAAC,CAAC;QACjE,CAAC;QAED,IAAI,CAAC;YACH,mCAAmC;YACnC,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC9B,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC;gBACxD,GAAG,CAAC,SAAS,CAAC,2BAA2B,EAAE,UAAU,CAAC,CAAC;YACzD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACxD,+CAA+C;YAC/C,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC9B,GAAG,CAAC,SAAS,CAAC,2BAA2B,EAAE,kBAAkB,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;QAED,yCAAyC;QACzC,GAAG,CAAC,SAAS,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;QAEzC,sDAAsD;QACtD,GAAG,CAAC,SAAS,CAAC,wBAAwB,EAAE,SAAS,CAAC,CAAC;QAEnD,4DAA4D;QAC5D,GAAG,CAAC,SAAS,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAC;QAEnD,iDAAiD;QACjD,GAAG,CAAC,SAAS,CAAC,iBAAiB,EAAE,iCAAiC,CAAC,CAAC;QAEpE,gDAAgD;QAChD,MAAM,iBAAiB,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAC3D,GAAG,CAAC,SAAS,CAAC,oBAAoB,EAAE,iBAAiB,CAAC,CAAC;QAEvD,mDAAmD;QACnD,GAAG,CAAC,SAAS,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QAE/C,oEAAoE;QACpE,GAAG,CAAC,SAAS,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC;QAE9C,oEAAoE;QACpE,GAAG,CAAC,SAAS,CAAC,mCAAmC,EAAE,MAAM,CAAC,CAAC;QAE3D,wCAAwC;QACxC,IAAI,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YACvC,GAAG,CAAC,SAAS,CAAC,eAAe,EAAE,8CAA8C,CAAC,CAAC;YAC/E,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YACpC,GAAG,CAAC,SAAS,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;QAChC,CAAC;QAED,4BAA4B;QAC5B,GAAG,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;QACjC,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAE3B,qDAAqD;QACrD,GAAG,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC,CAAC;QAC/E,GAAG,CAAC,SAAS,CAAC,cAAc,EAAE,GAAG,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;QAE3F,IAAI,EAAE,CAAC;IACT,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,GAAY;QAClC,qCAAqC;QACrC,OAAO,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,OAAO,CAAC;IACpE,CAAC;IAED;;OAEG;IACK,yBAAyB;QAC/B,MAAM,QAAQ,GAAG;YACf,kBAAkB;YAClB,yBAAyB;YACzB,aAAa;YACb,YAAY;YACZ,WAAW;YACX,0BAA0B;YAC1B,oBAAoB;YACpB,oBAAoB;YACpB,oBAAoB;YACpB,iCAAiC;YACjC,oCAAoC;YACpC,eAAe;YACf,gBAAgB;YAChB,cAAc;YACd,iBAAiB;YACjB,iBAAiB;YACjB,eAAe;YACf,SAAS;YACT,wBAAwB;YACxB,YAAY;YACZ,uBAAuB;YACvB,8BAA8B;YAC9B,qBAAqB;YACrB,aAAa;YACb,QAAQ;YACR,cAAc;YACd,wBAAwB;SACzB,CAAC;QAEF,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,IAAY;QACtC,MAAM,iBAAiB,GAAG;YACxB,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,cAAc;YACd,cAAc;YACd,kBAAkB;YAClB,oBAAoB;SACrB,CAAC;QAEF,OAAO,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;IACnE,CAAC;IAED;;OAEG;IACK,iBAAiB;QACvB,OAAO,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACxE,CAAC;CACF,CAAA;AAhJY,8DAAyB;oCAAzB,yBAAyB;IADrC,IAAA,mBAAU,GAAE;yDAGuB,sBAAa,oBAAb,sBAAa,oDACjB,sBAAS,oBAAT,sBAAS,oDACR,wBAAU,oBAAV,wBAAU;GAJ9B,yBAAyB,CAgJrC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\headers\\security-headers.middleware.ts"], "sourcesContent": ["import { Injectable, NestMiddleware } from '@nestjs/common';\r\nimport { Request, Response, NextFunction } from 'express';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { CspConfig } from './csp.config';\r\nimport { HstsConfig } from './hsts.config';\r\n\r\n/**\r\n * Middleware to apply comprehensive security headers to all HTTP responses\r\n * Implements OWASP security header recommendations\r\n */\r\n@Injectable()\r\nexport class SecurityHeadersMiddleware implements NestMiddleware {\r\n  constructor(\r\n    private readonly configService: ConfigService,\r\n    private readonly cspConfig: CspConfig,\r\n    private readonly hstsConfig: HstsConfig,\r\n  ) {}\r\n\r\n  use(req: Request, res: Response, next: NextFunction): void {\r\n    try {\r\n      // Content Security Policy\r\n      const cspHeader = this.cspConfig.generateCSPHeader();\r\n      res.setHeader('Content-Security-Policy', cspHeader);\r\n    } catch (error) {\r\n      console.error('Failed to generate CSP header:', error);\r\n      // Set a basic fallback CSP\r\n      res.setHeader('Content-Security-Policy', \"default-src 'self'\");\r\n    }\r\n\r\n    try {\r\n      // Strict Transport Security (HSTS)\r\n      if (this.shouldApplyHSTS(req)) {\r\n        const hstsHeader = this.hstsConfig.generateHSTSHeader();\r\n        res.setHeader('Strict-Transport-Security', hstsHeader);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to generate HSTS header:', error);\r\n      // Set a basic fallback HSTS for HTTPS requests\r\n      if (this.shouldApplyHSTS(req)) {\r\n        res.setHeader('Strict-Transport-Security', 'max-age=31536000');\r\n      }\r\n    }\r\n\r\n    // X-Frame-Options - Prevent clickjacking\r\n    res.setHeader('X-Frame-Options', 'DENY');\r\n\r\n    // X-Content-Type-Options - Prevent MIME type sniffing\r\n    res.setHeader('X-Content-Type-Options', 'nosniff');\r\n\r\n    // X-XSS-Protection - Enable XSS filtering (legacy browsers)\r\n    res.setHeader('X-XSS-Protection', '1; mode=block');\r\n\r\n    // Referrer Policy - Control referrer information\r\n    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');\r\n\r\n    // Permissions Policy - Control browser features\r\n    const permissionsPolicy = this.generatePermissionsPolicy();\r\n    res.setHeader('Permissions-Policy', permissionsPolicy);\r\n\r\n    // X-DNS-Prefetch-Control - Control DNS prefetching\r\n    res.setHeader('X-DNS-Prefetch-Control', 'off');\r\n\r\n    // X-Download-Options - Prevent file downloads from opening directly\r\n    res.setHeader('X-Download-Options', 'noopen');\r\n\r\n    // X-Permitted-Cross-Domain-Policies - Control cross-domain policies\r\n    res.setHeader('X-Permitted-Cross-Domain-Policies', 'none');\r\n\r\n    // Cache-Control for sensitive endpoints\r\n    if (this.isSensitiveEndpoint(req.path)) {\r\n      res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, private');\r\n      res.setHeader('Pragma', 'no-cache');\r\n      res.setHeader('Expires', '0');\r\n    }\r\n\r\n    // Remove server information\r\n    res.removeHeader('X-Powered-By');\r\n    res.removeHeader('Server');\r\n\r\n    // Add custom security headers for API identification\r\n    res.setHeader('X-API-Version', this.configService.get('app.version', '1.0.0'));\r\n    res.setHeader('X-Request-ID', req.headers['x-correlation-id'] || this.generateRequestId());\r\n\r\n    next();\r\n  }\r\n\r\n  /**\r\n   * Determine if HSTS should be applied based on request context\r\n   */\r\n  private shouldApplyHSTS(req: Request): boolean {\r\n    // Only apply HSTS for HTTPS requests\r\n    return req.secure || req.headers['x-forwarded-proto'] === 'https';\r\n  }\r\n\r\n  /**\r\n   * Generate Permissions Policy header value\r\n   */\r\n  private generatePermissionsPolicy(): string {\r\n    const policies = [\r\n      'accelerometer=()',\r\n      'ambient-light-sensor=()',\r\n      'autoplay=()',\r\n      'battery=()',\r\n      'camera=()',\r\n      'cross-origin-isolated=()',\r\n      'display-capture=()',\r\n      'document-domain=()',\r\n      'encrypted-media=()',\r\n      'execution-while-not-rendered=()',\r\n      'execution-while-out-of-viewport=()',\r\n      'fullscreen=()',\r\n      'geolocation=()',\r\n      'gyroscope=()',\r\n      'keyboard-map=()',\r\n      'magnetometer=()',\r\n      'microphone=()',\r\n      'midi=()',\r\n      'navigation-override=()',\r\n      'payment=()',\r\n      'picture-in-picture=()',\r\n      'publickey-credentials-get=()',\r\n      'screen-wake-lock=()',\r\n      'sync-xhr=()',\r\n      'usb=()',\r\n      'web-share=()',\r\n      'xr-spatial-tracking=()',\r\n    ];\r\n\r\n    return policies.join(', ');\r\n  }\r\n\r\n  /**\r\n   * Check if the endpoint contains sensitive data\r\n   */\r\n  private isSensitiveEndpoint(path: string): boolean {\r\n    const sensitivePatterns = [\r\n      '/auth',\r\n      '/login',\r\n      '/logout',\r\n      '/admin',\r\n      '/api/v1/auth',\r\n      '/api/v2/auth',\r\n      '/health/detailed',\r\n      '/metrics/sensitive',\r\n    ];\r\n\r\n    return sensitivePatterns.some(pattern => path.includes(pattern));\r\n  }\r\n\r\n  /**\r\n   * Generate a unique request ID for tracking\r\n   */\r\n  private generateRequestId(): string {\r\n    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n  }\r\n}"], "version": 3}