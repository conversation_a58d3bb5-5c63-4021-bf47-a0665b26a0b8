{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\response-action.entity.ts", "mappings": ";;;AAAA,6DAA8E;AAE9E,oEAA2D;AAC3D,yGAAkG;AAClG,uHAA+G;AAC/G,2GAAoG;AACpG,uGAAgG;AAChG,iHAAyG;AA0GzG;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,MAAa,cAAe,SAAQ,iCAAsC;IAUxE,YAAY,KAA0B,EAAE,EAAmB;QACzD,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QACjB,IAAI,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,KAA0B,EAAE,EAAmB;QAC3D,MAAM,MAAM,GAAG,IAAI,cAAc,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAE7C,uCAAuC;QACvC,MAAM,CAAC,cAAc,CAAC,IAAI,uEAAgC,CACxD,MAAM,CAAC,EAAE,EACT;YACE,UAAU,EAAE,KAAK,CAAC,UAAU;YAC5B,MAAM,EAAE,KAAK,CAAC,MAAM;YACpB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,QAAQ,EAAE,KAAK,CAAC,QAAQ;YACxB,WAAW,EAAE,KAAK,CAAC,WAAW;YAC9B,gBAAgB,EAAE,KAAK,CAAC,gBAAgB;YACxC,MAAM,EAAE,KAAK,CAAC,MAAM;YACpB,cAAc,EAAE,KAAK,CAAC,cAAc,EAAE,QAAQ,EAAE;YAChD,eAAe,EAAE,KAAK,CAAC,eAAe,EAAE,QAAQ,EAAE;YAClD,sBAAsB,EAAE,KAAK,CAAC,sBAAsB,EAAE,QAAQ,EAAE;SACjE,CACF,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;IAES,kBAAkB;QAC1B,KAAK,CAAC,kBAAkB,EAAE,CAAC;QAE3B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9D,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,cAAc,CAAC,gBAAgB,EAAE,CAAC;YAC9D,MAAM,IAAI,KAAK,CAAC,sCAAsC,cAAc,CAAC,gBAAgB,aAAa,CAAC,CAAC;QACtG,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,cAAc,CAAC,sBAAsB,EAAE,CAAC;YACpG,MAAM,IAAI,KAAK,CAAC,4CAA4C,cAAc,CAAC,sBAAsB,aAAa,CAAC,CAAC;QAClH,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;YACzC,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;YAC1C,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,EAAE,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,SAAS,EAAE,CAAC;YAC9C,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE,CAAC;YAC/C,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,CAAC,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,cAAc,CAAC,QAAQ,EAAE,CAAC;YACrD,MAAM,IAAI,KAAK,CAAC,wCAAwC,cAAc,CAAC,QAAQ,OAAO,CAAC,CAAC;QAC1F,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE,CAAC;YAC9C,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,IAAI,CAAC,EAAE,CAAC;YACnC,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACtC,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACjE,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YACxC,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;QACvE,CAAC;QAED,uCAAuC;QACvC,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACnC,CAAC;IAEO,yBAAyB;QAC/B,6DAA6D;QAC7D,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,iCAAY,CAAC,QAAQ,EAAE,CAAC;YAChD,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;gBAC1D,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;YACrE,CAAC;QACH,CAAC;QAED,8DAA8D;QAC9D,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,iCAAY,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,iCAAY,CAAC,MAAM,EAAE,CAAC;YAC9F,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;YACpE,CAAC;QACH,CAAC;QAED,kDAAkD;QAClD,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;YACtD,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAChE,CAAC;QAED,mEAAmE;QACnE,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;YACzD,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;QACjF,CAAC;IACH,CAAC;IAED,UAAU;IACV,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAC3B,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;IAC1B,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;IAChC,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;IACtC,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;IAClE,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC7B,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;IAChC,CAAC;IAED,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;IACjC,CAAC;IAED,IAAI,wBAAwB;QAC1B,OAAO,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC;IAC7C,CAAC;IAED,IAAI,qBAAqB;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC;IAC1C,CAAC;IAED,IAAI,mBAAmB;QACrB,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;IAC7C,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;IACrC,CAAC;IAED,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;IAClC,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;IAChC,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;IAChC,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;IACtF,CAAC;IAED,IAAI,eAAe;QACjB,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;IACzC,CAAC;IAED,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC;IACvC,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;IACnC,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC;IACtC,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;IAChC,CAAC;IAED,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;IAC9E,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;IACpF,CAAC;IAED,IAAI,IAAI;QACN,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;IACpC,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;IACnC,CAAC;IAED,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;IACpC,CAAC;IAED,IAAI,sBAAsB;QACxB,OAAO,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC;IAC3C,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;IACnC,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;IACxC,CAAC;IAED,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;IAClC,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;IACnC,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC7B,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED,mBAAmB;IAEnB;;OAEG;IACH,YAAY,CAAC,SAAuB,EAAE,SAAkB,EAAE,KAAc;QACtE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YACpC,OAAO,CAAC,mBAAmB;QAC7B,CAAC;QAED,6BAA6B;QAC7B,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,CAAC;YAChE,MAAM,IAAI,KAAK,CAAC,kCAAkC,IAAI,CAAC,KAAK,CAAC,MAAM,OAAO,SAAS,EAAE,CAAC,CAAC;QACzF,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QACpC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC;QAE9B,+BAA+B;QAC/B,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QAE9C,IAAI,CAAC,cAAc,CAAC,IAAI,oFAAsC,CAC5D,IAAI,CAAC,EAAE,EACP;YACE,SAAS;YACT,SAAS;YACT,SAAS;YACT,KAAK;YACL,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CACF,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAEO,kBAAkB,CAAC,SAAuB,EAAE,SAAkB;QACpE,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QAEvB,QAAQ,SAAS,EAAE,CAAC;YAClB,KAAK,iCAAY,CAAC,QAAQ;gBACxB,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,SAAS,CAAC;gBAClC,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gBAC5B,MAAM;YACR,KAAK,iCAAY,CAAC,SAAS;gBACzB,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,SAAS,CAAC;gBAClC,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gBAC5B,MAAM;YACR,KAAK,iCAAY,CAAC,SAAS;gBACzB,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,SAAS,CAAC;gBACnC,MAAM;YACR,KAAK,iCAAY,CAAC,OAAO;gBACvB,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;gBAC3B,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gBAC5B,MAAM;QACV,CAAC;IACH,CAAC;IAEO,uBAAuB,CAAC,IAAkB,EAAE,EAAgB;QAClE,MAAM,gBAAgB,GAAyC;YAC7D,CAAC,iCAAY,CAAC,OAAO,CAAC,EAAE,CAAC,iCAAY,CAAC,QAAQ,EAAE,iCAAY,CAAC,QAAQ,EAAE,iCAAY,CAAC,SAAS,CAAC;YAC9F,CAAC,iCAAY,CAAC,QAAQ,CAAC,EAAE,CAAC,iCAAY,CAAC,MAAM,EAAE,iCAAY,CAAC,SAAS,EAAE,iCAAY,CAAC,SAAS,CAAC;YAC9F,CAAC,iCAAY,CAAC,MAAM,CAAC,EAAE,CAAC,iCAAY,CAAC,SAAS,EAAE,iCAAY,CAAC,SAAS,EAAE,iCAAY,CAAC,OAAO,CAAC;YAC7F,CAAC,iCAAY,CAAC,SAAS,CAAC,EAAE;gBACxB,iCAAY,CAAC,SAAS,EAAE,iCAAY,CAAC,MAAM,EAAE,iCAAY,CAAC,OAAO;gBACjE,iCAAY,CAAC,MAAM,EAAE,iCAAY,CAAC,SAAS,EAAE,iCAAY,CAAC,OAAO;gBACjE,iCAAY,CAAC,mBAAmB;aACjC;YACD,CAAC,iCAAY,CAAC,MAAM,CAAC,EAAE,CAAC,iCAAY,CAAC,SAAS,EAAE,iCAAY,CAAC,SAAS,EAAE,iCAAY,CAAC,OAAO,CAAC;YAC7F,CAAC,iCAAY,CAAC,QAAQ,CAAC,EAAE,CAAC,iCAAY,CAAC,SAAS,EAAE,iCAAY,CAAC,MAAM,EAAE,iCAAY,CAAC,SAAS,EAAE,iCAAY,CAAC,OAAO,CAAC;YACpH,CAAC,iCAAY,CAAC,mBAAmB,CAAC,EAAE,CAAC,iCAAY,CAAC,SAAS,EAAE,iCAAY,CAAC,SAAS,EAAE,iCAAY,CAAC,OAAO,CAAC;YAC1G,CAAC,iCAAY,CAAC,OAAO,CAAC,EAAE,CAAC,iCAAY,CAAC,SAAS,EAAE,iCAAY,CAAC,SAAS,EAAE,iCAAY,CAAC,MAAM,EAAE,iCAAY,CAAC,SAAS,CAAC;YACrH,CAAC,iCAAY,CAAC,SAAS,CAAC,EAAE,CAAC,iCAAY,CAAC,MAAM,EAAE,iCAAY,CAAC,SAAS,EAAE,iCAAY,CAAC,OAAO,CAAC;YAC7F,CAAC,iCAAY,CAAC,OAAO,CAAC,EAAE,CAAC,iCAAY,CAAC,MAAM,EAAE,iCAAY,CAAC,SAAS,EAAE,iCAAY,CAAC,QAAQ,CAAC;YAC5F,CAAC,iCAAY,CAAC,MAAM,CAAC,EAAE,CAAC,iCAAY,CAAC,QAAQ,EAAE,iCAAY,CAAC,SAAS,CAAC;YACtE,CAAC,iCAAY,CAAC,OAAO,CAAC,EAAE,CAAC,iCAAY,CAAC,QAAQ,EAAE,iCAAY,CAAC,SAAS,CAAC;YACvE,CAAC,iCAAY,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,WAAW;YACzC,CAAC,iCAAY,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,WAAW;YACzC,CAAC,iCAAY,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,WAAW;YACvC,CAAC,iCAAY,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,WAAW;YACxC,CAAC,iCAAY,CAAC,OAAO,CAAC,EAAE,CAAC,iCAAY,CAAC,OAAO,EAAE,iCAAY,CAAC,SAAS,CAAC;SACvE,CAAC;QAEF,OAAO,gBAAgB,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,UAAkB,EAAE,gBAAsC;QAChE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,iCAAY,CAAC,SAAS,EAAE,CAAC;YACjD,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;QACvE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QAC/C,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;QACnC,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAEnC,4BAA4B;QAC5B,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;YAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,CAAC;YACpF,IAAI,SAAS,EAAE,CAAC;gBACd,IAAI,CAAC,KAAK,CAAC,qBAAqB,GAAG,IAAI,CAAC,KAAK,CAC3C,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CACtE,CAAC;YACJ,CAAC;QACH,CAAC;QAED,yBAAyB;QACzB,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAE/B,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,iCAAY,CAAC,MAAM,CAAC;QAC/F,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC;QAE9B,IAAI,CAAC,cAAc,CAAC,IAAI,yEAAiC,CACvD,IAAI,CAAC,EAAE,EACP;YACE,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU;YACjC,UAAU;YACV,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU;YACjC,gBAAgB;YAChB,kBAAkB,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB;YACjD,qBAAqB,EAAE,IAAI,CAAC,KAAK,CAAC,qBAAqB;SACxD,CACF,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAI,CAAC,KAAa,EAAE,UAAmB;QACrC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,iCAAY,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,iCAAY,CAAC,QAAQ,EAAE,CAAC;YAChG,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;QAC5E,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,KAAK,CAAC;QAClC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,iCAAY,CAAC,MAAM,CAAC;QACxC,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAEtC,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;QACrC,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;YAC3B,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QACrC,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,IAAI,qEAA+B,CACrD,IAAI,CAAC,EAAE,EACP;YACE,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU;YACjC,KAAK;YACL,UAAU;YACV,QAAQ,EAAE,IAAI,IAAI,EAAE;YACpB,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU;YACjC,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE;SAC1B,CACF,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAe;QACnB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC;QAC3B,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,iCAAY,CAAC,QAAQ,CAAC;QAC1C,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,SAAS,CAAC;QACtC,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAEzF,IAAI,CAAC,cAAc,CAAC,IAAI,oFAAsC,CAC5D,IAAI,CAAC,EAAE,EACP;YACE,SAAS,EAAE,iCAAY,CAAC,MAAM;YAC9B,SAAS,EAAE,iCAAY,CAAC,QAAQ;YAChC,SAAS,EAAE,OAAO;YAClB,KAAK,EAAE,iBAAiB,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE;YAC/C,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CACF,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,YAAoB,EAAE,eAAqC;QAClE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG;YAC3B,YAAY;YACZ,YAAY,EAAE,IAAI,IAAI,EAAE;YACxB,eAAe;SAChB,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,IAAI,8EAAmC,CACzD,IAAI,CAAC,EAAE,EACP;YACE,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU;YACjC,YAAY;YACZ,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,YAAY;YACrD,eAAe;YACf,wBAAwB,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB;SACtD,CACF,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,YAAiD;QAC/D,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,YAAY,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,aAA6B;QAC1C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC;YACpE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,aAA6B;QAC7C,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;IAChG,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,aAAqB;QACpC,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,IAAc;QACpB,MAAM,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB;QAEjF,IAAI,OAAO,CAAC,MAAM,GAAG,cAAc,CAAC,QAAQ,EAAE,CAAC;YAC7C,MAAM,IAAI,KAAK,CAAC,wCAAwC,cAAc,CAAC,QAAQ,OAAO,CAAC,CAAC;QAC1F,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,IAAc;QACvB,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,QAA6B;QAC1C,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,QAAQ,EAAE,CAAC;IAChE,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,GAAW;QACxB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IAClC,CAAC;IAED,gBAAgB;IAEhB;;OAEG;IACH,UAAU;QACR,OAAO;YACL,iCAAY,CAAC,SAAS;YACtB,iCAAY,CAAC,MAAM;YACnB,iCAAY,CAAC,SAAS;YACtB,iCAAY,CAAC,OAAO;YACpB,iCAAY,CAAC,OAAO;YACpB,iCAAY,CAAC,QAAQ;SACtB,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO,CAAC,iCAAY,CAAC,SAAS,EAAE,iCAAY,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IACpF,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,CAAC,iCAAY,CAAC,MAAM,EAAE,iCAAY,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IACjF,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,OAAO,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU;YAC7C,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY;YACvB,IAAI,CAAC,YAAY,EAAE;YACnB,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU;YACtB,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,WAAW,KAAK,IAAI,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,iCAAY,CAAC,OAAO,CAAC;IACnF,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,SAAS;QACP,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YAC5B,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;IAChE,CAAC;IAED;;OAEG;IACH,WAAW;QACT,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,iCAAY,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;YAC1E,MAAM,cAAc,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;YACpF,OAAO,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;QACpD,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,GAAW;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,IAAc;QACtB,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,IAAc;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,0BAA0B;QACxB,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,IAAI,IAAI,EAAE,CAAC;QACtD,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,wBAAwB,IAAI,EAAE,CAAC;QAEnE,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,gBAAgB,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACK,uBAAuB;QAC7B,sCAAsC;QACtC,+DAA+D;QAC/D,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,SAAS;YAC1C,IAAI,CAAC,KAAK,CAAC,cAAc,KAAK,SAAS,CAAC;IACzE,CAAC;IAED;;OAEG;IACH,UAAU;QAeR,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE;YACtB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK;YACvB,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU;YACjC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;YACzB,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC7B,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;YACnC,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAC7C,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY;YACrC,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE;YAClB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE;YACzB,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE;YACjC,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE;YACzB,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE;SAChC,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,GAAG,KAAK,CAAC,MAAM,EAAE;YACjB,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU;YACjC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;YACzB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK;YACvB,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;YACnC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU;YACjC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;YACzB,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC7B,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;YACnC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY;YACrC,wBAAwB,EAAE,IAAI,CAAC,KAAK,CAAC,wBAAwB;YAC7D,qBAAqB,EAAE,IAAI,CAAC,KAAK,CAAC,qBAAqB;YACvD,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB;YACnD,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAC7C,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa;YACvC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU;YACjC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,WAAW,EAAE;YAChD,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;YACnC,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,WAAW,EAAE;YAClD,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU;YACjC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,WAAW,EAAE;YAChD,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAC7C,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe;YAC3C,kBAAkB,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB;YACjD,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc;YACzC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU;YACjC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU;YACjC,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,iBAAiB;YAC/C,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,WAAW,EAAE;YAClD,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY;YACrC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU;YACjC,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;gBAC5C,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe;gBAC7B,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,YAAY,CAAC,WAAW,EAAE;aACpE,CAAC,CAAC,CAAC,SAAS;YACb,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC7B,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,QAAQ,EAAE;YACrD,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,QAAQ,EAAE;YACvD,sBAAsB,EAAE,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAE,QAAQ,EAAE;YACrE,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,QAAQ,EAAE;YACrD,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC;YAClE,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa;YACvC,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc;YACzC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC7B,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,WAAW,EAAE;YAChD,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE;SAC3B,CAAC;IACJ,CAAC;;AAv1BH,wCAw1BC;AAv1ByB,+BAAgB,GAAG,GAAG,CAAC;AACvB,qCAAsB,GAAG,IAAI,CAAC;AAC9B,uBAAQ,GAAG,EAAE,CAAC;AACd,sCAAuB,GAAG,EAAE,CAAC;AAC7B,kCAAmB,GAAG,CAAC,CAAC;AACxB,0CAA2B,GAAG,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\response-action.entity.ts"], "sourcesContent": ["import { BaseAggregateRoot, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { ActionType } from '../enums/action-type.enum';\r\nimport { ActionStatus } from '../enums/action-status.enum';\r\nimport { ResponseActionCreatedDomainEvent } from '../events/response-action-created.domain-event';\r\nimport { ResponseActionStatusChangedDomainEvent } from '../events/response-action-status-changed.domain-event';\r\nimport { ResponseActionExecutedDomainEvent } from '../events/response-action-executed.domain-event';\r\nimport { ResponseActionFailedDomainEvent } from '../events/response-action-failed.domain-event';\r\nimport { ResponseActionRolledBackDomainEvent } from '../events/response-action-rolled-back.domain-event';\r\n\r\n/**\r\n * Response Action Entity Properties\r\n */\r\nexport interface ResponseActionProps {\r\n  /** Type of action to be executed */\r\n  actionType: ActionType;\r\n  /** Current status of the action */\r\n  status: ActionStatus;\r\n  /** Action title/name */\r\n  title: string;\r\n  /** Detailed description of the action */\r\n  description?: string;\r\n  /** Action parameters and configuration */\r\n  parameters: Record<string, any>;\r\n  /** Target entity or resource for the action */\r\n  target?: {\r\n    type: 'event' | 'threat' | 'vulnerability' | 'system' | 'user' | 'network' | 'custom';\r\n    id: string;\r\n    name?: string;\r\n    metadata?: Record<string, any>;\r\n  };\r\n  /** Priority level of the action */\r\n  priority: 'low' | 'normal' | 'high' | 'critical';\r\n  /** Whether the action is automated or manual */\r\n  isAutomated: boolean;\r\n  /** Whether the action is reversible */\r\n  isReversible: boolean;\r\n  /** Estimated execution time in minutes */\r\n  estimatedDurationMinutes?: number;\r\n  /** Actual execution time in minutes */\r\n  actualDurationMinutes?: number;\r\n  /** Required permissions for execution */\r\n  requiredPermissions: string[];\r\n  /** Approval requirements */\r\n  approvalRequired: boolean;\r\n  /** Approval level required */\r\n  approvalLevel?: 'analyst' | 'senior' | 'manager' | 'director';\r\n  /** Who approved the action */\r\n  approvedBy?: string;\r\n  /** When the action was approved */\r\n  approvedAt?: Date;\r\n  /** Who scheduled the action */\r\n  scheduledBy?: string;\r\n  /** When the action is scheduled to run */\r\n  scheduledAt?: Date;\r\n  /** Who executed the action */\r\n  executedBy?: string;\r\n  /** When the action was executed */\r\n  executedAt?: Date;\r\n  /** Execution results */\r\n  executionResults?: Record<string, any>;\r\n  /** Success criteria for validation */\r\n  successCriteria: string[];\r\n  /** Whether success criteria were met */\r\n  successCriteriaMet?: boolean;\r\n  /** Execution error if any */\r\n  executionError?: string;\r\n  /** Retry count */\r\n  retryCount: number;\r\n  /** Maximum retry attempts */\r\n  maxRetries: number;\r\n  /** Retry delay in minutes */\r\n  retryDelayMinutes: number;\r\n  /** Next retry time */\r\n  nextRetryAt?: Date;\r\n  /** Rollback information */\r\n  rollbackInfo?: {\r\n    canRollback: boolean;\r\n    rollbackSteps: string[];\r\n    rollbackData?: Record<string, any>;\r\n  };\r\n  /** Whether the action was rolled back */\r\n  rolledBack: boolean;\r\n  /** Rollback execution details */\r\n  rollbackDetails?: {\r\n    rolledBackBy: string;\r\n    rolledBackAt: Date;\r\n    rollbackResults?: Record<string, any>;\r\n    rollbackError?: string;\r\n  };\r\n  /** Tags for categorization */\r\n  tags: string[];\r\n  /** Additional metadata */\r\n  metadata: Record<string, any>;\r\n  /** Related event ID that triggered this action */\r\n  relatedEventId?: UniqueEntityId;\r\n  /** Related threat ID */\r\n  relatedThreatId?: UniqueEntityId;\r\n  /** Related vulnerability ID */\r\n  relatedVulnerabilityId?: UniqueEntityId;\r\n  /** Parent action ID for chained actions */\r\n  parentActionId?: UniqueEntityId;\r\n  /** Child action IDs */\r\n  childActionIds: UniqueEntityId[];\r\n  /** Correlation ID for grouping related actions */\r\n  correlationId?: string;\r\n  /** Timeout in minutes */\r\n  timeoutMinutes: number;\r\n  /** Whether the action timed out */\r\n  timedOut: boolean;\r\n  /** Timeout timestamp */\r\n  timedOutAt?: Date;\r\n}\r\n\r\n/**\r\n * Response Action Entity\r\n * \r\n * Represents an automated or manual response action that can be executed\r\n * in response to security events, threats, and vulnerabilities.\r\n * \r\n * Key responsibilities:\r\n * - Maintain action state and lifecycle\r\n * - Enforce business rules and execution constraints\r\n * - Generate domain events for state changes\r\n * - Support action chaining and correlation\r\n * - Handle retry logic and rollback capabilities\r\n * - Track execution metrics and results\r\n * \r\n * Business Rules:\r\n * - Actions must have valid type and parameters\r\n * - Status transitions must follow defined workflows\r\n * - Approval required for high-risk actions\r\n * - Retry limits must be respected\r\n * - Rollback only allowed for reversible actions\r\n * - Timeout handling for long-running actions\r\n */\r\nexport class ResponseAction extends BaseAggregateRoot<ResponseActionProps> {\r\n  private static readonly MAX_TITLE_LENGTH = 200;\r\n  private static readonly MAX_DESCRIPTION_LENGTH = 2000;\r\n  private static readonly MAX_TAGS = 20;\r\n  private static readonly DEFAULT_TIMEOUT_MINUTES = 60;\r\n  private static readonly DEFAULT_MAX_RETRIES = 3;\r\n  private static readonly DEFAULT_RETRY_DELAY_MINUTES = 5;\r\n\r\n  private readonly _createdAt: Date;\r\n\r\n  constructor(props: ResponseActionProps, id?: UniqueEntityId) {\r\n    super(props, id);\r\n    this._createdAt = new Date();\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Create a new ResponseAction\r\n   */\r\n  static create(props: ResponseActionProps, id?: UniqueEntityId): ResponseAction {\r\n    const action = new ResponseAction(props, id);\r\n    \r\n    // Add domain event for action creation\r\n    action.addDomainEvent(new ResponseActionCreatedDomainEvent(\r\n      action.id,\r\n      {\r\n        actionType: props.actionType,\r\n        status: props.status,\r\n        title: props.title,\r\n        priority: props.priority,\r\n        isAutomated: props.isAutomated,\r\n        approvalRequired: props.approvalRequired,\r\n        target: props.target,\r\n        relatedEventId: props.relatedEventId?.toString(),\r\n        relatedThreatId: props.relatedThreatId?.toString(),\r\n        relatedVulnerabilityId: props.relatedVulnerabilityId?.toString(),\r\n      }\r\n    ));\r\n\r\n    return action;\r\n  }\r\n\r\n  protected validateInvariants(): void {\r\n    super.validateInvariants();\r\n\r\n    if (!this.props.actionType) {\r\n      throw new Error('ResponseAction must have an action type');\r\n    }\r\n\r\n    if (!this.props.status) {\r\n      throw new Error('ResponseAction must have a status');\r\n    }\r\n\r\n    if (!this.props.title || this.props.title.trim().length === 0) {\r\n      throw new Error('ResponseAction must have a non-empty title');\r\n    }\r\n\r\n    if (this.props.title.length > ResponseAction.MAX_TITLE_LENGTH) {\r\n      throw new Error(`ResponseAction title cannot exceed ${ResponseAction.MAX_TITLE_LENGTH} characters`);\r\n    }\r\n\r\n    if (this.props.description && this.props.description.length > ResponseAction.MAX_DESCRIPTION_LENGTH) {\r\n      throw new Error(`ResponseAction description cannot exceed ${ResponseAction.MAX_DESCRIPTION_LENGTH} characters`);\r\n    }\r\n\r\n    if (!this.props.parameters) {\r\n      throw new Error('ResponseAction must have parameters');\r\n    }\r\n\r\n    if (!this.props.priority) {\r\n      throw new Error('ResponseAction must have a priority');\r\n    }\r\n\r\n    if (this.props.isAutomated === undefined) {\r\n      throw new Error('ResponseAction must specify if it is automated');\r\n    }\r\n\r\n    if (this.props.isReversible === undefined) {\r\n      throw new Error('ResponseAction must specify if it is reversible');\r\n    }\r\n\r\n    if (!Array.isArray(this.props.requiredPermissions)) {\r\n      throw new Error('ResponseAction must have required permissions array');\r\n    }\r\n\r\n    if (this.props.approvalRequired === undefined) {\r\n      throw new Error('ResponseAction must specify if approval is required');\r\n    }\r\n\r\n    if (!Array.isArray(this.props.successCriteria)) {\r\n      throw new Error('ResponseAction must have success criteria array');\r\n    }\r\n\r\n    if (this.props.retryCount < 0) {\r\n      throw new Error('Retry count cannot be negative');\r\n    }\r\n\r\n    if (this.props.maxRetries < 0) {\r\n      throw new Error('Max retries cannot be negative');\r\n    }\r\n\r\n    if (this.props.retryDelayMinutes < 0) {\r\n      throw new Error('Retry delay cannot be negative');\r\n    }\r\n\r\n    if (!Array.isArray(this.props.tags)) {\r\n      throw new Error('ResponseAction must have tags array');\r\n    }\r\n\r\n    if (this.props.tags.length > ResponseAction.MAX_TAGS) {\r\n      throw new Error(`ResponseAction cannot have more than ${ResponseAction.MAX_TAGS} tags`);\r\n    }\r\n\r\n    if (!this.props.metadata) {\r\n      throw new Error('ResponseAction must have metadata object');\r\n    }\r\n\r\n    if (!Array.isArray(this.props.childActionIds)) {\r\n      throw new Error('ResponseAction must have child action IDs array');\r\n    }\r\n\r\n    if (this.props.timeoutMinutes <= 0) {\r\n      throw new Error('Timeout minutes must be positive');\r\n    }\r\n\r\n    if (this.props.timedOut === undefined) {\r\n      throw new Error('ResponseAction must specify if it timed out');\r\n    }\r\n\r\n    if (this.props.rolledBack === undefined) {\r\n      throw new Error('ResponseAction must specify if it was rolled back');\r\n    }\r\n\r\n    // Validate status-specific constraints\r\n    this.validateStatusConstraints();\r\n  }\r\n\r\n  private validateStatusConstraints(): void {\r\n    // If action is approved, it should have approval information\r\n    if (this.props.status === ActionStatus.APPROVED) {\r\n      if (this.props.approvalRequired && !this.props.approvedBy) {\r\n        throw new Error('Approved actions must have approver information');\r\n      }\r\n    }\r\n\r\n    // If action is executed, it should have execution information\r\n    if (this.props.status === ActionStatus.COMPLETED || this.props.status === ActionStatus.FAILED) {\r\n      if (!this.props.executedAt) {\r\n        throw new Error('Executed actions must have execution timestamp');\r\n      }\r\n    }\r\n\r\n    // If action is rolled back, it must be reversible\r\n    if (this.props.rolledBack && !this.props.isReversible) {\r\n      throw new Error('Only reversible actions can be rolled back');\r\n    }\r\n\r\n    // If action has rollback details, it must be marked as rolled back\r\n    if (this.props.rollbackDetails && !this.props.rolledBack) {\r\n      throw new Error('Actions with rollback details must be marked as rolled back');\r\n    }\r\n  }\r\n\r\n  // Getters\r\n  get actionType(): ActionType {\r\n    return this.props.actionType;\r\n  }\r\n\r\n  get status(): ActionStatus {\r\n    return this.props.status;\r\n  }\r\n\r\n  get title(): string {\r\n    return this.props.title;\r\n  }\r\n\r\n  get description(): string | undefined {\r\n    return this.props.description;\r\n  }\r\n\r\n  get parameters(): Record<string, any> {\r\n    return { ...this.props.parameters };\r\n  }\r\n\r\n  get target(): ResponseActionProps['target'] {\r\n    return this.props.target ? { ...this.props.target } : undefined;\r\n  }\r\n\r\n  get priority(): ResponseActionProps['priority'] {\r\n    return this.props.priority;\r\n  }\r\n\r\n  get isAutomated(): boolean {\r\n    return this.props.isAutomated;\r\n  }\r\n\r\n  get isReversible(): boolean {\r\n    return this.props.isReversible;\r\n  }\r\n\r\n  get estimatedDurationMinutes(): number | undefined {\r\n    return this.props.estimatedDurationMinutes;\r\n  }\r\n\r\n  get actualDurationMinutes(): number | undefined {\r\n    return this.props.actualDurationMinutes;\r\n  }\r\n\r\n  get requiredPermissions(): string[] {\r\n    return [...this.props.requiredPermissions];\r\n  }\r\n\r\n  get approvalRequired(): boolean {\r\n    return this.props.approvalRequired;\r\n  }\r\n\r\n  get approvalLevel(): ResponseActionProps['approvalLevel'] {\r\n    return this.props.approvalLevel;\r\n  }\r\n\r\n  get approvedBy(): string | undefined {\r\n    return this.props.approvedBy;\r\n  }\r\n\r\n  get approvedAt(): Date | undefined {\r\n    return this.props.approvedAt;\r\n  }\r\n\r\n  get scheduledBy(): string | undefined {\r\n    return this.props.scheduledBy;\r\n  }\r\n\r\n  get scheduledAt(): Date | undefined {\r\n    return this.props.scheduledAt;\r\n  }\r\n\r\n  get executedBy(): string | undefined {\r\n    return this.props.executedBy;\r\n  }\r\n\r\n  get executedAt(): Date | undefined {\r\n    return this.props.executedAt;\r\n  }\r\n\r\n  get executionResults(): Record<string, any> | undefined {\r\n    return this.props.executionResults ? { ...this.props.executionResults } : undefined;\r\n  }\r\n\r\n  get successCriteria(): string[] {\r\n    return [...this.props.successCriteria];\r\n  }\r\n\r\n  get successCriteriaMet(): boolean | undefined {\r\n    return this.props.successCriteriaMet;\r\n  }\r\n\r\n  get executionError(): string | undefined {\r\n    return this.props.executionError;\r\n  }\r\n\r\n  get retryCount(): number {\r\n    return this.props.retryCount;\r\n  }\r\n\r\n  get maxRetries(): number {\r\n    return this.props.maxRetries;\r\n  }\r\n\r\n  get retryDelayMinutes(): number {\r\n    return this.props.retryDelayMinutes;\r\n  }\r\n\r\n  get nextRetryAt(): Date | undefined {\r\n    return this.props.nextRetryAt;\r\n  }\r\n\r\n  get rollbackInfo(): ResponseActionProps['rollbackInfo'] {\r\n    return this.props.rollbackInfo ? { ...this.props.rollbackInfo } : undefined;\r\n  }\r\n\r\n  get rolledBack(): boolean {\r\n    return this.props.rolledBack;\r\n  }\r\n\r\n  get rollbackDetails(): ResponseActionProps['rollbackDetails'] {\r\n    return this.props.rollbackDetails ? { ...this.props.rollbackDetails } : undefined;\r\n  }\r\n\r\n  get tags(): string[] {\r\n    return [...this.props.tags];\r\n  }\r\n\r\n  get metadata(): Record<string, any> {\r\n    return { ...this.props.metadata };\r\n  }\r\n\r\n  get relatedEventId(): UniqueEntityId | undefined {\r\n    return this.props.relatedEventId;\r\n  }\r\n\r\n  get relatedThreatId(): UniqueEntityId | undefined {\r\n    return this.props.relatedThreatId;\r\n  }\r\n\r\n  get relatedVulnerabilityId(): UniqueEntityId | undefined {\r\n    return this.props.relatedVulnerabilityId;\r\n  }\r\n\r\n  get parentActionId(): UniqueEntityId | undefined {\r\n    return this.props.parentActionId;\r\n  }\r\n\r\n  get childActionIds(): UniqueEntityId[] {\r\n    return [...this.props.childActionIds];\r\n  }\r\n\r\n  get correlationId(): string | undefined {\r\n    return this.props.correlationId;\r\n  }\r\n\r\n  get timeoutMinutes(): number {\r\n    return this.props.timeoutMinutes;\r\n  }\r\n\r\n  get timedOut(): boolean {\r\n    return this.props.timedOut;\r\n  }\r\n\r\n  get timedOutAt(): Date | undefined {\r\n    return this.props.timedOutAt;\r\n  }\r\n\r\n  get createdAt(): Date {\r\n    return this._createdAt;\r\n  }\r\n\r\n  // Business methods\r\n\r\n  /**\r\n   * Change action status\r\n   */\r\n  changeStatus(newStatus: ActionStatus, changedBy?: string, notes?: string): void {\r\n    if (this.props.status === newStatus) {\r\n      return; // No change needed\r\n    }\r\n\r\n    // Validate status transition\r\n    if (!this.isValidStatusTransition(this.props.status, newStatus)) {\r\n      throw new Error(`Invalid status transition from ${this.props.status} to ${newStatus}`);\r\n    }\r\n\r\n    const oldStatus = this.props.status;\r\n    this.props.status = newStatus;\r\n\r\n    // Handle status-specific logic\r\n    this.handleStatusChange(newStatus, changedBy);\r\n\r\n    this.addDomainEvent(new ResponseActionStatusChangedDomainEvent(\r\n      this.id,\r\n      {\r\n        oldStatus,\r\n        newStatus,\r\n        changedBy,\r\n        notes,\r\n        timestamp: new Date(),\r\n      }\r\n    ));\r\n\r\n    this.validateInvariants();\r\n  }\r\n\r\n  private handleStatusChange(newStatus: ActionStatus, changedBy?: string): void {\r\n    const now = new Date();\r\n\r\n    switch (newStatus) {\r\n      case ActionStatus.APPROVED:\r\n        this.props.approvedBy = changedBy;\r\n        this.props.approvedAt = now;\r\n        break;\r\n      case ActionStatus.EXECUTING:\r\n        this.props.executedBy = changedBy;\r\n        this.props.executedAt = now;\r\n        break;\r\n      case ActionStatus.SCHEDULED:\r\n        this.props.scheduledBy = changedBy;\r\n        break;\r\n      case ActionStatus.TIMEOUT:\r\n        this.props.timedOut = true;\r\n        this.props.timedOutAt = now;\r\n        break;\r\n    }\r\n  }\r\n\r\n  private isValidStatusTransition(from: ActionStatus, to: ActionStatus): boolean {\r\n    const validTransitions: Record<ActionStatus, ActionStatus[]> = {\r\n      [ActionStatus.PENDING]: [ActionStatus.APPROVED, ActionStatus.REJECTED, ActionStatus.CANCELLED],\r\n      [ActionStatus.APPROVED]: [ActionStatus.QUEUED, ActionStatus.SCHEDULED, ActionStatus.CANCELLED],\r\n      [ActionStatus.QUEUED]: [ActionStatus.EXECUTING, ActionStatus.CANCELLED, ActionStatus.ON_HOLD],\r\n      [ActionStatus.EXECUTING]: [\r\n        ActionStatus.COMPLETED, ActionStatus.FAILED, ActionStatus.PARTIAL,\r\n        ActionStatus.PAUSED, ActionStatus.CANCELLED, ActionStatus.TIMEOUT,\r\n        ActionStatus.MANUAL_INTERVENTION\r\n      ],\r\n      [ActionStatus.PAUSED]: [ActionStatus.EXECUTING, ActionStatus.CANCELLED, ActionStatus.ON_HOLD],\r\n      [ActionStatus.RETRYING]: [ActionStatus.EXECUTING, ActionStatus.FAILED, ActionStatus.CANCELLED, ActionStatus.TIMEOUT],\r\n      [ActionStatus.MANUAL_INTERVENTION]: [ActionStatus.EXECUTING, ActionStatus.CANCELLED, ActionStatus.ON_HOLD],\r\n      [ActionStatus.PARTIAL]: [ActionStatus.EXECUTING, ActionStatus.COMPLETED, ActionStatus.FAILED, ActionStatus.CANCELLED],\r\n      [ActionStatus.SCHEDULED]: [ActionStatus.QUEUED, ActionStatus.CANCELLED, ActionStatus.ON_HOLD],\r\n      [ActionStatus.ON_HOLD]: [ActionStatus.QUEUED, ActionStatus.CANCELLED, ActionStatus.REJECTED],\r\n      [ActionStatus.FAILED]: [ActionStatus.RETRYING, ActionStatus.CANCELLED],\r\n      [ActionStatus.TIMEOUT]: [ActionStatus.RETRYING, ActionStatus.CANCELLED],\r\n      [ActionStatus.COMPLETED]: [], // Terminal\r\n      [ActionStatus.CANCELLED]: [], // Terminal\r\n      [ActionStatus.SKIPPED]: [], // Terminal\r\n      [ActionStatus.REJECTED]: [], // Terminal\r\n      [ActionStatus.UNKNOWN]: [ActionStatus.PENDING, ActionStatus.CANCELLED],\r\n    };\r\n\r\n    return validTransitions[from]?.includes(to) || false;\r\n  }\r\n\r\n  /**\r\n   * Execute the action\r\n   */\r\n  execute(executedBy: string, executionResults?: Record<string, any>): void {\r\n    if (this.props.status !== ActionStatus.EXECUTING) {\r\n      throw new Error('Action must be in executing status to be executed');\r\n    }\r\n\r\n    this.props.executionResults = executionResults;\r\n    this.props.executedBy = executedBy;\r\n    this.props.executedAt = new Date();\r\n\r\n    // Calculate actual duration\r\n    if (this.props.executedAt) {\r\n      const startTime = this.props.scheduledAt || this.props.approvedAt || this.createdAt;\r\n      if (startTime) {\r\n        this.props.actualDurationMinutes = Math.round(\r\n          (this.props.executedAt.getTime() - startTime.getTime()) / (1000 * 60)\r\n        );\r\n      }\r\n    }\r\n\r\n    // Check success criteria\r\n    this.evaluateSuccessCriteria();\r\n\r\n    const newStatus = this.props.successCriteriaMet ? ActionStatus.COMPLETED : ActionStatus.FAILED;\r\n    this.props.status = newStatus;\r\n\r\n    this.addDomainEvent(new ResponseActionExecutedDomainEvent(\r\n      this.id,\r\n      {\r\n        actionType: this.props.actionType,\r\n        executedBy,\r\n        executedAt: this.props.executedAt,\r\n        executionResults,\r\n        successCriteriaMet: this.props.successCriteriaMet,\r\n        actualDurationMinutes: this.props.actualDurationMinutes,\r\n      }\r\n    ));\r\n\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Fail the action execution\r\n   */\r\n  fail(error: string, executedBy?: string): void {\r\n    if (this.props.status !== ActionStatus.EXECUTING && this.props.status !== ActionStatus.RETRYING) {\r\n      throw new Error('Action must be in executing or retrying status to fail');\r\n    }\r\n\r\n    this.props.executionError = error;\r\n    this.props.status = ActionStatus.FAILED;\r\n    this.props.successCriteriaMet = false;\r\n\r\n    if (executedBy) {\r\n      this.props.executedBy = executedBy;\r\n    }\r\n\r\n    if (!this.props.executedAt) {\r\n      this.props.executedAt = new Date();\r\n    }\r\n\r\n    this.addDomainEvent(new ResponseActionFailedDomainEvent(\r\n      this.id,\r\n      {\r\n        actionType: this.props.actionType,\r\n        error,\r\n        executedBy,\r\n        failedAt: new Date(),\r\n        retryCount: this.props.retryCount,\r\n        canRetry: this.canRetry(),\r\n      }\r\n    ));\r\n\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Retry the action\r\n   */\r\n  retry(retryBy: string): void {\r\n    if (!this.canRetry()) {\r\n      throw new Error('Action cannot be retried');\r\n    }\r\n\r\n    this.props.retryCount += 1;\r\n    this.props.status = ActionStatus.RETRYING;\r\n    this.props.executionError = undefined;\r\n    this.props.nextRetryAt = new Date(Date.now() + this.props.retryDelayMinutes * 60 * 1000);\r\n\r\n    this.addDomainEvent(new ResponseActionStatusChangedDomainEvent(\r\n      this.id,\r\n      {\r\n        oldStatus: ActionStatus.FAILED,\r\n        newStatus: ActionStatus.RETRYING,\r\n        changedBy: retryBy,\r\n        notes: `Retry attempt ${this.props.retryCount}`,\r\n        timestamp: new Date(),\r\n      }\r\n    ));\r\n\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Rollback the action\r\n   */\r\n  rollback(rolledBackBy: string, rollbackResults?: Record<string, any>): void {\r\n    if (!this.canRollback()) {\r\n      throw new Error('Action cannot be rolled back');\r\n    }\r\n\r\n    this.props.rolledBack = true;\r\n    this.props.rollbackDetails = {\r\n      rolledBackBy,\r\n      rolledBackAt: new Date(),\r\n      rollbackResults,\r\n    };\r\n\r\n    this.addDomainEvent(new ResponseActionRolledBackDomainEvent(\r\n      this.id,\r\n      {\r\n        actionType: this.props.actionType,\r\n        rolledBackBy,\r\n        rolledBackAt: this.props.rollbackDetails.rolledBackAt,\r\n        rollbackResults,\r\n        originalExecutionResults: this.props.executionResults,\r\n      }\r\n    ));\r\n\r\n    this.validateInvariants();\r\n  }\r\n\r\n  /**\r\n   * Set rollback information\r\n   */\r\n  setRollbackInfo(rollbackInfo: ResponseActionProps['rollbackInfo']): void {\r\n    this.props.rollbackInfo = rollbackInfo;\r\n  }\r\n\r\n  /**\r\n   * Add child action\r\n   */\r\n  addChildAction(childActionId: UniqueEntityId): void {\r\n    if (!this.props.childActionIds.some(id => id.equals(childActionId))) {\r\n      this.props.childActionIds.push(childActionId);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove child action\r\n   */\r\n  removeChildAction(childActionId: UniqueEntityId): void {\r\n    this.props.childActionIds = this.props.childActionIds.filter(id => !id.equals(childActionId));\r\n  }\r\n\r\n  /**\r\n   * Set correlation ID\r\n   */\r\n  setCorrelationId(correlationId: string): void {\r\n    this.props.correlationId = correlationId;\r\n  }\r\n\r\n  /**\r\n   * Add tags\r\n   */\r\n  addTags(tags: string[]): void {\r\n    const newTags = [...new Set([...this.props.tags, ...tags])]; // Remove duplicates\r\n\r\n    if (newTags.length > ResponseAction.MAX_TAGS) {\r\n      throw new Error(`ResponseAction cannot have more than ${ResponseAction.MAX_TAGS} tags`);\r\n    }\r\n\r\n    this.props.tags = newTags;\r\n  }\r\n\r\n  /**\r\n   * Remove tags\r\n   */\r\n  removeTags(tags: string[]): void {\r\n    this.props.tags = this.props.tags.filter(tag => !tags.includes(tag));\r\n  }\r\n\r\n  /**\r\n   * Update metadata\r\n   */\r\n  updateMetadata(metadata: Record<string, any>): void {\r\n    this.props.metadata = { ...this.props.metadata, ...metadata };\r\n  }\r\n\r\n  /**\r\n   * Remove metadata key\r\n   */\r\n  removeMetadata(key: string): void {\r\n    delete this.props.metadata[key];\r\n  }\r\n\r\n  // Query methods\r\n\r\n  /**\r\n   * Check if action is terminal (completed, failed, cancelled, etc.)\r\n   */\r\n  isTerminal(): boolean {\r\n    return [\r\n      ActionStatus.COMPLETED,\r\n      ActionStatus.FAILED,\r\n      ActionStatus.CANCELLED,\r\n      ActionStatus.TIMEOUT,\r\n      ActionStatus.SKIPPED,\r\n      ActionStatus.REJECTED,\r\n    ].includes(this.props.status);\r\n  }\r\n\r\n  /**\r\n   * Check if action is active (not terminal)\r\n   */\r\n  isActive(): boolean {\r\n    return !this.isTerminal();\r\n  }\r\n\r\n  /**\r\n   * Check if action is successful\r\n   */\r\n  isSuccessful(): boolean {\r\n    return [ActionStatus.COMPLETED, ActionStatus.PARTIAL].includes(this.props.status);\r\n  }\r\n\r\n  /**\r\n   * Check if action has failed\r\n   */\r\n  hasFailed(): boolean {\r\n    return [ActionStatus.FAILED, ActionStatus.TIMEOUT].includes(this.props.status);\r\n  }\r\n\r\n  /**\r\n   * Check if action can be retried\r\n   */\r\n  canRetry(): boolean {\r\n    return this.hasFailed() && \r\n           this.props.retryCount < this.props.maxRetries &&\r\n           !this.props.timedOut;\r\n  }\r\n\r\n  /**\r\n   * Check if action can be rolled back\r\n   */\r\n  canRollback(): boolean {\r\n    return this.props.isReversible &&\r\n           this.isSuccessful() &&\r\n           !this.props.rolledBack &&\r\n           this.props.rollbackInfo?.canRollback === true;\r\n  }\r\n\r\n  /**\r\n   * Check if action requires approval\r\n   */\r\n  requiresApproval(): boolean {\r\n    return this.props.approvalRequired && this.props.status === ActionStatus.PENDING;\r\n  }\r\n\r\n  /**\r\n   * Check if action is high priority\r\n   */\r\n  isHighPriority(): boolean {\r\n    return ['high', 'critical'].includes(this.props.priority);\r\n  }\r\n\r\n  /**\r\n   * Check if action is overdue\r\n   */\r\n  isOverdue(): boolean {\r\n    if (!this.props.scheduledAt) {\r\n      return false;\r\n    }\r\n    return new Date() > this.props.scheduledAt && this.isActive();\r\n  }\r\n\r\n  /**\r\n   * Check if action has timed out\r\n   */\r\n  hasTimedOut(): boolean {\r\n    if (this.props.timedOut) {\r\n      return true;\r\n    }\r\n\r\n    if (this.props.status === ActionStatus.EXECUTING && this.props.executedAt) {\r\n      const elapsedMinutes = (Date.now() - this.props.executedAt.getTime()) / (1000 * 60);\r\n      return elapsedMinutes > this.props.timeoutMinutes;\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Check if action has specific tag\r\n   */\r\n  hasTag(tag: string): boolean {\r\n    return this.props.tags.includes(tag);\r\n  }\r\n\r\n  /**\r\n   * Check if action has any of the specified tags\r\n   */\r\n  hasAnyTag(tags: string[]): boolean {\r\n    return tags.some(tag => this.hasTag(tag));\r\n  }\r\n\r\n  /**\r\n   * Check if action has all of the specified tags\r\n   */\r\n  hasAllTags(tags: string[]): boolean {\r\n    return tags.every(tag => this.hasTag(tag));\r\n  }\r\n\r\n  /**\r\n   * Get action age in milliseconds\r\n   */\r\n  getAge(): number {\r\n    return Date.now() - this._createdAt.getTime();\r\n  }\r\n\r\n  /**\r\n   * Get estimated completion time\r\n   */\r\n  getEstimatedCompletionTime(): Date | null {\r\n    if (this.isTerminal()) {\r\n      return null;\r\n    }\r\n\r\n    const baseTime = this.props.scheduledAt || new Date();\r\n    const estimatedMinutes = this.props.estimatedDurationMinutes || 30;\r\n    \r\n    return new Date(baseTime.getTime() + estimatedMinutes * 60 * 1000);\r\n  }\r\n\r\n  /**\r\n   * Evaluate success criteria\r\n   */\r\n  private evaluateSuccessCriteria(): void {\r\n    // This is a simplified implementation\r\n    // In a real system, this would involve more complex validation\r\n    this.props.successCriteriaMet = this.props.executionResults !== undefined &&\r\n                                   this.props.executionError === undefined;\r\n  }\r\n\r\n  /**\r\n   * Get action summary for display\r\n   */\r\n  getSummary(): {\r\n    id: string;\r\n    title: string;\r\n    actionType: ActionType;\r\n    status: ActionStatus;\r\n    priority: string;\r\n    isAutomated: boolean;\r\n    approvalRequired: boolean;\r\n    isReversible: boolean;\r\n    age: number;\r\n    isActive: boolean;\r\n    isSuccessful: boolean;\r\n    canRetry: boolean;\r\n    canRollback: boolean;\r\n  } {\r\n    return {\r\n      id: this.id.toString(),\r\n      title: this.props.title,\r\n      actionType: this.props.actionType,\r\n      status: this.props.status,\r\n      priority: this.props.priority,\r\n      isAutomated: this.props.isAutomated,\r\n      approvalRequired: this.props.approvalRequired,\r\n      isReversible: this.props.isReversible,\r\n      age: this.getAge(),\r\n      isActive: this.isActive(),\r\n      isSuccessful: this.isSuccessful(),\r\n      canRetry: this.canRetry(),\r\n      canRollback: this.canRollback(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      ...super.toJSON(),\r\n      actionType: this.props.actionType,\r\n      status: this.props.status,\r\n      title: this.props.title,\r\n      description: this.props.description,\r\n      parameters: this.props.parameters,\r\n      target: this.props.target,\r\n      priority: this.props.priority,\r\n      isAutomated: this.props.isAutomated,\r\n      isReversible: this.props.isReversible,\r\n      estimatedDurationMinutes: this.props.estimatedDurationMinutes,\r\n      actualDurationMinutes: this.props.actualDurationMinutes,\r\n      requiredPermissions: this.props.requiredPermissions,\r\n      approvalRequired: this.props.approvalRequired,\r\n      approvalLevel: this.props.approvalLevel,\r\n      approvedBy: this.props.approvedBy,\r\n      approvedAt: this.props.approvedAt?.toISOString(),\r\n      scheduledBy: this.props.scheduledBy,\r\n      scheduledAt: this.props.scheduledAt?.toISOString(),\r\n      executedBy: this.props.executedBy,\r\n      executedAt: this.props.executedAt?.toISOString(),\r\n      executionResults: this.props.executionResults,\r\n      successCriteria: this.props.successCriteria,\r\n      successCriteriaMet: this.props.successCriteriaMet,\r\n      executionError: this.props.executionError,\r\n      retryCount: this.props.retryCount,\r\n      maxRetries: this.props.maxRetries,\r\n      retryDelayMinutes: this.props.retryDelayMinutes,\r\n      nextRetryAt: this.props.nextRetryAt?.toISOString(),\r\n      rollbackInfo: this.props.rollbackInfo,\r\n      rolledBack: this.props.rolledBack,\r\n      rollbackDetails: this.props.rollbackDetails ? {\r\n        ...this.props.rollbackDetails,\r\n        rolledBackAt: this.props.rollbackDetails.rolledBackAt.toISOString(),\r\n      } : undefined,\r\n      tags: this.props.tags,\r\n      metadata: this.props.metadata,\r\n      relatedEventId: this.props.relatedEventId?.toString(),\r\n      relatedThreatId: this.props.relatedThreatId?.toString(),\r\n      relatedVulnerabilityId: this.props.relatedVulnerabilityId?.toString(),\r\n      parentActionId: this.props.parentActionId?.toString(),\r\n      childActionIds: this.props.childActionIds.map(id => id.toString()),\r\n      correlationId: this.props.correlationId,\r\n      timeoutMinutes: this.props.timeoutMinutes,\r\n      timedOut: this.props.timedOut,\r\n      timedOutAt: this.props.timedOutAt?.toISOString(),\r\n      summary: this.getSummary(),\r\n    };\r\n  }\r\n}"], "version": 3}