7feae3358879154e52ba67a5312008d2
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const request = __importStar(require("supertest"));
const threat_intelligence_controller_1 = require("../threat-intelligence.controller");
const threat_intelligence_service_1 = require("../../../application/services/threat-intelligence.service");
const threat_intelligence_entity_1 = require("../../../domain/entities/threat-intelligence.entity");
const indicator_of_compromise_entity_1 = require("../../../domain/entities/indicator-of-compromise.entity");
const threat_actor_entity_1 = require("../../../domain/entities/threat-actor.entity");
const threat_campaign_entity_1 = require("../../../domain/entities/threat-campaign.entity");
const logger_service_1 = require("../../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../../infrastructure/logging/audit/audit.service");
const notification_service_1 = require("../../../../../infrastructure/notification/notification.service");
const jwt_auth_guard_1 = require("../../../../../infrastructure/auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../../../../../infrastructure/auth/guards/roles.guard");
describe('ThreatIntelligenceController (Integration)', () => {
    let app;
    let threatIntelligenceService;
    let configService;
    const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        roles: ['threat_analyst'],
    };
    const mockThreatIntelligence = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        title: 'Test APT Campaign',
        description: 'Advanced persistent threat targeting healthcare sector',
        threatType: threat_intelligence_entity_1.ThreatType.APT,
        severity: threat_intelligence_entity_1.ThreatSeverity.HIGH,
        confidence: threat_intelligence_entity_1.ThreatConfidence.HIGH,
        status: threat_intelligence_entity_1.ThreatStatus.ACTIVE,
        firstSeen: new Date('2023-01-01'),
        lastSeen: new Date('2023-01-15'),
        tags: ['apt29', 'healthcare', 'phishing'],
        dataSource: {
            name: 'CrowdStrike Intelligence',
            type: 'commercial',
            reliability: 'A',
            url: 'https://intelligence.crowdstrike.com',
            lastUpdated: new Date(),
            confidence: threat_intelligence_entity_1.ThreatConfidence.HIGH,
        },
        mitreAttack: {
            tactics: ['initial-access', 'persistence'],
            techniques: ['T1566.001', 'T1053.005'],
        },
        targetedSectors: ['healthcare', 'financial'],
        targetedCountries: ['US', 'UK'],
        riskScore: 8.5,
        observationCount: 5,
        isIoc: false,
        isAttributed: true,
        createdAt: new Date('2023-01-01'),
        updatedAt: new Date('2023-01-15'),
    };
    beforeAll(async () => {
        const moduleFixture = await testing_1.Test.createTestingModule({
            imports: [
                config_1.ConfigModule.forRoot({
                    isGlobal: true,
                    envFilePath: '.env.test',
                }),
                typeorm_1.TypeOrmModule.forRootAsync({
                    imports: [config_1.ConfigModule],
                    useFactory: (configService) => ({
                        type: 'postgres',
                        host: configService.get('DB_HOST', 'localhost'),
                        port: configService.get('DB_PORT', 5432),
                        username: configService.get('DB_USERNAME', 'test'),
                        password: configService.get('DB_PASSWORD', 'test'),
                        database: configService.get('DB_DATABASE', 'sentinel_test'),
                        entities: [threat_intelligence_entity_1.ThreatIntelligence, indicator_of_compromise_entity_1.IndicatorOfCompromise, threat_actor_entity_1.ThreatActor, threat_campaign_entity_1.ThreatCampaign],
                        synchronize: true,
                        dropSchema: true,
                    }),
                    inject: [config_1.ConfigService],
                }),
                typeorm_1.TypeOrmModule.forFeature([threat_intelligence_entity_1.ThreatIntelligence, indicator_of_compromise_entity_1.IndicatorOfCompromise, threat_actor_entity_1.ThreatActor, threat_campaign_entity_1.ThreatCampaign]),
            ],
            controllers: [threat_intelligence_controller_1.ThreatIntelligenceController],
            providers: [
                threat_intelligence_service_1.ThreatIntelligenceService,
                {
                    provide: logger_service_1.LoggerService,
                    useValue: {
                        error: jest.fn(),
                        warn: jest.fn(),
                        log: jest.fn(),
                        debug: jest.fn(),
                    },
                },
                {
                    provide: audit_service_1.AuditService,
                    useValue: {
                        logUserAction: jest.fn(),
                        logSecurityEvent: jest.fn(),
                    },
                },
                {
                    provide: notification_service_1.NotificationService,
                    useValue: {
                        sendCriticalThreatAlert: jest.fn(),
                        sendUserNotification: jest.fn(),
                        sendRoleNotification: jest.fn(),
                    },
                },
            ],
        })
            .overrideGuard(jwt_auth_guard_1.JwtAuthGuard)
            .useValue({
            canActivate: (context) => {
                const request = context.switchToHttp().getRequest();
                request.user = mockUser;
                return true;
            },
        })
            .overrideGuard(roles_guard_1.RolesGuard)
            .useValue({
            canActivate: () => true,
        })
            .compile();
        app = moduleFixture.createNestApplication();
        threatIntelligenceService = moduleFixture.get(threat_intelligence_service_1.ThreatIntelligenceService);
        configService = moduleFixture.get(config_1.ConfigService);
        await app.init();
    });
    afterAll(async () => {
        await app.close();
    });
    beforeEach(async () => {
        // Clean up database before each test
        const threatRepository = threatIntelligenceService['threatIntelligenceRepository'];
        await threatRepository.clear();
    });
    describe('POST /api/threat-intelligence', () => {
        it('should create threat intelligence successfully', async () => {
            const createDto = {
                title: 'New APT Campaign',
                description: 'New advanced persistent threat',
                threatType: threat_intelligence_entity_1.ThreatType.APT,
                severity: threat_intelligence_entity_1.ThreatSeverity.HIGH,
                confidence: threat_intelligence_entity_1.ThreatConfidence.MEDIUM,
                firstSeen: '2023-01-01T00:00:00Z',
                dataSource: {
                    name: 'Test Source',
                    type: 'commercial',
                    reliability: 'A',
                    url: 'https://test.com',
                    lastUpdated: '2023-01-01T00:00:00Z',
                    confidence: 'high',
                },
                tags: ['test', 'apt'],
                targetedSectors: ['healthcare'],
            };
            const response = await request(app.getHttpServer())
                .post('/api/threat-intelligence')
                .send(createDto)
                .expect(201);
            expect(response.body).toMatchObject({
                title: createDto.title,
                description: createDto.description,
                threatType: createDto.threatType,
                severity: createDto.severity,
                confidence: createDto.confidence,
                tags: createDto.tags,
                targetedSectors: createDto.targetedSectors,
            });
            expect(response.body.id).toBeDefined();
            expect(response.body.createdAt).toBeDefined();
            expect(response.body.updatedAt).toBeDefined();
        });
        it('should return 400 for invalid data', async () => {
            const invalidDto = {
                title: '', // Invalid: empty title
                description: 'Description',
                threatType: 'invalid_type', // Invalid enum value
                firstSeen: 'invalid_date', // Invalid date format
            };
            await request(app.getHttpServer())
                .post('/api/threat-intelligence')
                .send(invalidDto)
                .expect(400);
        });
        it('should return 409 for duplicate threat intelligence', async () => {
            const createDto = {
                title: 'Duplicate Threat',
                description: 'Description',
                threatType: threat_intelligence_entity_1.ThreatType.MALWARE,
                firstSeen: '2023-01-01T00:00:00Z',
                dataSource: {
                    name: 'Test Source',
                    type: 'commercial',
                    reliability: 'A',
                    url: 'https://test.com',
                    lastUpdated: '2023-01-01T00:00:00Z',
                    confidence: 'high',
                },
            };
            // Create first threat
            await request(app.getHttpServer())
                .post('/api/threat-intelligence')
                .send(createDto)
                .expect(201);
            // Try to create duplicate
            await request(app.getHttpServer())
                .post('/api/threat-intelligence')
                .send(createDto)
                .expect(409);
        });
    });
    describe('GET /api/threat-intelligence/:id', () => {
        it('should return threat intelligence by ID', async () => {
            // Create threat intelligence first
            const created = await threatIntelligenceService.createThreatIntelligence({
                title: 'Test Threat',
                description: 'Test description',
                threatType: threat_intelligence_entity_1.ThreatType.APT,
                firstSeen: new Date(),
                dataSource: mockThreatIntelligence.dataSource,
            }, mockUser.id);
            const response = await request(app.getHttpServer())
                .get(`/api/threat-intelligence/${created.id}`)
                .expect(200);
            expect(response.body).toMatchObject({
                id: created.id,
                title: 'Test Threat',
                description: 'Test description',
                threatType: threat_intelligence_entity_1.ThreatType.APT,
            });
        });
        it('should return 404 for non-existent threat intelligence', async () => {
            await request(app.getHttpServer())
                .get('/api/threat-intelligence/123e4567-e89b-12d3-a456-************')
                .expect(404);
        });
        it('should return 400 for invalid UUID format', async () => {
            await request(app.getHttpServer())
                .get('/api/threat-intelligence/invalid-uuid')
                .expect(400);
        });
    });
    describe('GET /api/threat-intelligence/search', () => {
        beforeEach(async () => {
            // Create test data
            await threatIntelligenceService.createThreatIntelligence({
                title: 'APT29 Campaign',
                description: 'APT29 targeting healthcare',
                threatType: threat_intelligence_entity_1.ThreatType.APT,
                severity: threat_intelligence_entity_1.ThreatSeverity.HIGH,
                firstSeen: new Date('2023-01-01'),
                tags: ['apt29', 'healthcare'],
                dataSource: mockThreatIntelligence.dataSource,
            }, mockUser.id);
            await threatIntelligenceService.createThreatIntelligence({
                title: 'Malware Campaign',
                description: 'Banking malware',
                threatType: threat_intelligence_entity_1.ThreatType.MALWARE,
                severity: threat_intelligence_entity_1.ThreatSeverity.MEDIUM,
                firstSeen: new Date('2023-01-02'),
                tags: ['banking', 'malware'],
                dataSource: mockThreatIntelligence.dataSource,
            }, mockUser.id);
        });
        it('should search threat intelligence with filters', async () => {
            const response = await request(app.getHttpServer())
                .get('/api/threat-intelligence/search')
                .query({
                threatTypes: [threat_intelligence_entity_1.ThreatType.APT],
                severities: [threat_intelligence_entity_1.ThreatSeverity.HIGH],
                page: 1,
                limit: 10,
            })
                .expect(200);
            expect(response.body.threats).toHaveLength(1);
            expect(response.body.threats[0].threatType).toBe(threat_intelligence_entity_1.ThreatType.APT);
            expect(response.body.threats[0].severity).toBe(threat_intelligence_entity_1.ThreatSeverity.HIGH);
            expect(response.body.total).toBe(1);
            expect(response.body.page).toBe(1);
            expect(response.body.totalPages).toBe(1);
        });
        it('should search with text query', async () => {
            const response = await request(app.getHttpServer())
                .get('/api/threat-intelligence/search')
                .query({
                searchText: 'healthcare',
                page: 1,
                limit: 10,
            })
                .expect(200);
            expect(response.body.threats).toHaveLength(1);
            expect(response.body.threats[0].title).toContain('APT29');
        });
        it('should handle pagination', async () => {
            const response = await request(app.getHttpServer())
                .get('/api/threat-intelligence/search')
                .query({
                page: 1,
                limit: 1,
            })
                .expect(200);
            expect(response.body.threats).toHaveLength(1);
            expect(response.body.total).toBe(2);
            expect(response.body.totalPages).toBe(2);
        });
        it('should return empty results for no matches', async () => {
            const response = await request(app.getHttpServer())
                .get('/api/threat-intelligence/search')
                .query({
                searchText: 'nonexistent',
            })
                .expect(200);
            expect(response.body.threats).toHaveLength(0);
            expect(response.body.total).toBe(0);
        });
    });
    describe('PUT /api/threat-intelligence/:id', () => {
        it('should update threat intelligence successfully', async () => {
            // Create threat intelligence first
            const created = await threatIntelligenceService.createThreatIntelligence({
                title: 'Original Title',
                description: 'Original description',
                threatType: threat_intelligence_entity_1.ThreatType.MALWARE,
                severity: threat_intelligence_entity_1.ThreatSeverity.MEDIUM,
                firstSeen: new Date(),
                dataSource: mockThreatIntelligence.dataSource,
            }, mockUser.id);
            const updateDto = {
                title: 'Updated Title',
                severity: threat_intelligence_entity_1.ThreatSeverity.HIGH,
                tags: ['updated', 'test'],
            };
            const response = await request(app.getHttpServer())
                .put(`/api/threat-intelligence/${created.id}`)
                .send(updateDto)
                .expect(200);
            expect(response.body).toMatchObject({
                id: created.id,
                title: 'Updated Title',
                severity: threat_intelligence_entity_1.ThreatSeverity.HIGH,
                tags: ['updated', 'test'],
            });
        });
        it('should return 404 for non-existent threat intelligence', async () => {
            const updateDto = {
                title: 'Updated Title',
            };
            await request(app.getHttpServer())
                .put('/api/threat-intelligence/123e4567-e89b-12d3-a456-************')
                .send(updateDto)
                .expect(404);
        });
    });
    describe('DELETE /api/threat-intelligence/:id', () => {
        it('should delete threat intelligence successfully', async () => {
            // Create threat intelligence first
            const created = await threatIntelligenceService.createThreatIntelligence({
                title: 'To Delete',
                description: 'Will be deleted',
                threatType: threat_intelligence_entity_1.ThreatType.MALWARE,
                firstSeen: new Date(),
                dataSource: mockThreatIntelligence.dataSource,
            }, mockUser.id);
            await request(app.getHttpServer())
                .delete(`/api/threat-intelligence/${created.id}`)
                .expect(204);
            // Verify it's deleted
            await request(app.getHttpServer())
                .get(`/api/threat-intelligence/${created.id}`)
                .expect(404);
        });
        it('should return 404 for non-existent threat intelligence', async () => {
            await request(app.getHttpServer())
                .delete('/api/threat-intelligence/123e4567-e89b-12d3-a456-************')
                .expect(404);
        });
    });
    describe('POST /api/threat-intelligence/:id/observe', () => {
        it('should record observation successfully', async () => {
            // Create threat intelligence first
            const created = await threatIntelligenceService.createThreatIntelligence({
                title: 'Observable Threat',
                description: 'Test threat',
                threatType: threat_intelligence_entity_1.ThreatType.MALWARE,
                firstSeen: new Date(),
                dataSource: mockThreatIntelligence.dataSource,
            }, mockUser.id);
            const observationContext = {
                source: 'test_system',
                timestamp: new Date(),
                details: 'Test observation',
            };
            const response = await request(app.getHttpServer())
                .post(`/api/threat-intelligence/${created.id}/observe`)
                .send({ context: observationContext })
                .expect(200);
            expect(response.body.observationCount).toBeGreaterThan(0);
            expect(response.body.lastObserved).toBeDefined();
        });
    });
    describe('GET /api/threat-intelligence/dashboard', () => {
        it('should return dashboard data', async () => {
            // Create some test data
            await threatIntelligenceService.createThreatIntelligence({
                title: 'Dashboard Test 1',
                description: 'Test',
                threatType: threat_intelligence_entity_1.ThreatType.APT,
                severity: threat_intelligence_entity_1.ThreatSeverity.CRITICAL,
                firstSeen: new Date(),
                dataSource: mockThreatIntelligence.dataSource,
            }, mockUser.id);
            const response = await request(app.getHttpServer())
                .get('/api/threat-intelligence/dashboard')
                .expect(200);
            expect(response.body).toHaveProperty('summary');
            expect(response.body).toHaveProperty('distribution');
            expect(response.body).toHaveProperty('timestamp');
            expect(response.body.summary).toHaveProperty('total');
            expect(response.body.summary).toHaveProperty('active');
            expect(response.body.summary).toHaveProperty('critical');
            expect(response.body.summary).toHaveProperty('recent');
        });
    });
    describe('POST /api/threat-intelligence/export', () => {
        it('should export threat intelligence in JSON format', async () => {
            // Create test data
            await threatIntelligenceService.createThreatIntelligence({
                title: 'Export Test',
                description: 'Test export',
                threatType: threat_intelligence_entity_1.ThreatType.MALWARE,
                firstSeen: new Date(),
                dataSource: mockThreatIntelligence.dataSource,
            }, mockUser.id);
            const exportRequest = {
                format: 'json',
                criteria: {
                    threatTypes: [threat_intelligence_entity_1.ThreatType.MALWARE],
                },
            };
            const response = await request(app.getHttpServer())
                .post('/api/threat-intelligence/export')
                .send(exportRequest)
                .expect(200);
            expect(response.body).toHaveProperty('format', 'json');
            expect(response.body).toHaveProperty('data');
            expect(response.body).toHaveProperty('exportedAt');
            expect(response.body).toHaveProperty('count');
            expect(Array.isArray(response.body.data)).toBe(true);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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