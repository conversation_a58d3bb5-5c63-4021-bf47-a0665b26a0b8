{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\domain\\base-aggregate-root.spec.ts", "mappings": ";;AAAA,0EAAqE;AACrE,sEAAiE;AACjE,qGAAmF;AAEnF,oBAAoB;AACpB,MAAM,eAAgB,SAAQ,mCAAoC;IAChE,YAAY,WAA2B,EAAE,OAAe;QACtD,KAAK,CAAC,WAAW,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IAClC,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;IAChC,CAAC;CACF;AAED,MAAM,gBAAiB,SAAQ,mCAAkC;IAC/D,YAAY,WAA2B,EAAE,KAAa;QACpD,KAAK,CAAC,WAAW,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;IAChC,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;IAC9B,CAAC;CACF;AAQD,MAAM,iBAAkB,SAAQ,uCAAqC;IACnE,YAAY,KAAyB,EAAE,EAAmB;QACxD,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IACnB,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IACzB,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAC3B,CAAC;IAED,2CAA2C;IAC3C,UAAU,CAAC,OAAe;QACxB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QAC/B,IAAI,CAAC,KAAa,CAAC,IAAI,GAAG,OAAO,CAAC;QAEnC,IAAI,CAAC,cAAc,CAAC,IAAI,eAAe,CAAC,IAAI,CAAC,EAAE,EAAE,qBAAqB,OAAO,OAAO,OAAO,EAAE,CAAC,CAAC,CAAC;IAClG,CAAC;IAED,8CAA8C;IAC9C,QAAQ;QACL,IAAI,CAAC,KAAa,CAAC,MAAM,GAAG,QAAQ,CAAC;QAEtC,IAAI,CAAC,cAAc,CAAC,IAAI,eAAe,CAAC,IAAI,CAAC,EAAE,EAAE,qBAAqB,CAAC,CAAC,CAAC;QACzE,IAAI,CAAC,cAAc,CAAC,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACxD,CAAC;IAED,sCAAsC;IACtC,qBAAqB;QACnB,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED,wCAAwC;IAC9B,kBAAkB;QAC1B,KAAK,CAAC,kBAAkB,EAAE,CAAC;QAE3B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5D,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;CACF;AAED,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;IACjC,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,KAAK,GAAuB;gBAChC,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;aACnB,CAAC;YAEF,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAE/C,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC9C,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,8CAAc,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,KAAK,GAAuB;gBAChC,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;aACnB,CAAC;YACF,MAAM,EAAE,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YAErC,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YAEnD,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC;gBACtC,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;aACnB,CAAC,CAAC;YAEH,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC;gBACtC,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;aACnB,CAAC,CAAC;YAEH,SAAS,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAEjC,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;YAClE,MAAM,CAAE,SAAS,CAAC,YAAY,CAAC,CAAC,CAAqB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QACtH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC;gBACtC,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;aACnB,CAAC,CAAC;YAEH,SAAS,CAAC,QAAQ,EAAE,CAAC;YAErB,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;YAClE,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC;gBACtC,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;aACnB,CAAC,CAAC;YAEH,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,SAAS,CAAC,EAAE,EAAE,cAAc,CAAC,CAAC;YAEhE,2BAA2B;YAC1B,SAAiB,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACxC,SAAiB,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAEzC,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC;gBACtC,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;aACnB,CAAC,CAAC;YAEH,SAAS,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAEjC,MAAM,OAAO,GAAG,SAAS,CAAC,YAAY,CAAC;YACvC,MAAM,OAAO,GAAG,SAAS,CAAC,YAAY,CAAC;YAEvC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,4BAA4B;YAC/D,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC;gBACtC,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;aACnB,CAAC,CAAC;YAEH,SAAS,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YACjC,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAE/C,SAAS,CAAC,WAAW,EAAE,CAAC;YACxB,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC;gBACtC,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;aACnB,CAAC,CAAC;YAEH,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,gBAAgB;YACtC,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAE/C,MAAM,aAAa,GAAG,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/C,SAAiB,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAEpD,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC;gBACtC,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;aACnB,CAAC,CAAC;YAEH,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,4CAA4C;YAElE,MAAM,UAAU,GAAG,SAAS,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;YAC9D,MAAM,aAAa,GAAG,SAAS,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;YAElE,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;YACtD,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC;gBACtC,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;aACnB,CAAC,CAAC;YAEH,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE/D,SAAS,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAEjC,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9D,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC;gBACtC,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;aACnB,CAAC,CAAC;YAEH,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAErD,SAAS,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAEjC,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC;gBACtC,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;aACnB,CAAC,CAAC;YAEH,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,gBAAgB;YAEtC,MAAM,iBAAiB,GAAG,SAAS,CAAC,oBAAoB,EAAE,CAAC;YAC3D,MAAM,CAAC,iBAAiB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAE1C,iCAAiC;YACjC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,gBAAgB,EAAE,CAAC;YAE7C,MAAM,gBAAgB,GAAG,SAAS,CAAC,oBAAoB,EAAE,CAAC;YAC1D,MAAM,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC;gBACtC,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;aACnB,CAAC,CAAC;YAEH,SAAS,CAAC,QAAQ,EAAE,CAAC;YAErB,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEpD,SAAS,CAAC,qBAAqB,EAAE,CAAC;YAElC,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC;gBACtC,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;aACnB,CAAC,CAAC;YAEH,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAEvC,SAAS,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YACjC,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAEvC,SAAS,CAAC,QAAQ,EAAE,CAAC;YACrB,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,0BAA0B;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wBAAwB,EAAE,GAAG,EAAE;YAChC,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC;gBACtC,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;aACnB,CAAC,CAAC;YAEH,SAAS,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAEjC,MAAM,QAAQ,GAAG,SAAS,CAAC,cAAc,EAAE,CAAC;YAE5C,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC3D,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACjC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC;YACzE,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC;gBACtC,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;aACnB,CAAC,CAAC;YAEH,MAAM,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC;gBACtC,IAAI,EAAE,EAAE;gBACR,MAAM,EAAE,UAAU;aACnB,CAAC,CAAC;YAEH,MAAM,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,wDAAwD;YACxD,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAC7D,SAAS,CAAC,KAAK,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;YACrD,SAAS,CAAC,GAAG,GAAG,IAAI,CAAC;YAErB,MAAM,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;QAC5F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC;gBACtC,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;aACnB,CAAC,CAAC;YAEH,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,EAAE,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,KAAK,GAAuB;gBAChC,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;aACnB,CAAC;YAEF,MAAM,UAAU,GAAG,IAAI,iBAAiB,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YACpD,MAAM,UAAU,GAAG,IAAI,iBAAiB,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YAEpD,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qEAAqE,EAAE,GAAG,EAAE;YAC7E,MAAM,EAAE,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,KAAK,GAAuB;gBAChC,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;aACnB,CAAC;YAEF,MAAM,UAAU,GAAG,IAAI,iBAAiB,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YACpD,MAAM,UAAU,GAAG,IAAI,iBAAiB,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YAEpD,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,kBAAkB;YAErD,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,KAAK,GAAuB;gBAChC,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;aACnB,CAAC;YAEF,MAAM,UAAU,GAAG,IAAI,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,UAAU,GAAG,IAAI,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAEhD,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC;gBACtC,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;aACnB,CAAC,CAAC;YAEH,SAAS,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAEjC,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC;YAEhC,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAClD,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAC5C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC;gBACtC,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;aACnB,CAAC,CAAC;YAEH,SAAS,CAAC,QAAQ,EAAE,CAAC;YAErB,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC;YAEhC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;YAC5E,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAC1D,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;QACvB,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC;gBACtC,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;aACnB,CAAC,CAAC;YAEH,SAAS,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAEjC,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;YAEhC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAClC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAC5C,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC;gBACtC,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;aACnB,CAAC,CAAC;YAEH,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;YAEhC,kBAAkB;YAClB,SAAS,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;YAEtC,+BAA+B;YAC/B,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC1C,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAM3B,MAAM,qBAAsB,SAAQ,iBAAiB;YACnD,YAAY,KAA6B,EAAE,EAAmB;gBAC5D,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YACnB,CAAC;YAED,IAAI,QAAQ;gBACV,OAAQ,IAAI,CAAC,KAAgC,CAAC,QAAQ,CAAC;YACzD,CAAC;YAED,cAAc,CAAC,WAAmB;gBAC/B,IAAI,CAAC,KAAa,CAAC,QAAQ,GAAG,WAAW,CAAC;gBAC3C,IAAI,CAAC,cAAc,CAAC,IAAI,eAAe,CAAC,IAAI,CAAC,EAAE,EAAE,uBAAuB,WAAW,EAAE,CAAC,CAAC,CAAC;YAC1F,CAAC;SACF;QAED,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,KAAK,GAA2B;gBACpC,IAAI,EAAE,oBAAoB;gBAC1B,MAAM,EAAE,QAAQ;gBAChB,QAAQ,EAAE,eAAe;aAC1B,CAAC;YAEF,MAAM,SAAS,GAAG,IAAI,qBAAqB,CAAC,KAAK,CAAC,CAAC;YAEnD,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;YACxD,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;YACpD,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,uCAAiB,CAAC,CAAC;YACpD,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,KAAK,GAA2B;gBACpC,IAAI,EAAE,oBAAoB;gBAC1B,MAAM,EAAE,QAAQ;gBAChB,QAAQ,EAAE,eAAe;aAC1B,CAAC;YAEF,MAAM,SAAS,GAAG,IAAI,qBAAqB,CAAC,KAAK,CAAC,CAAC;YAEnD,SAAS,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAEzC,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC;gBACtC,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;aACnB,CAAC,CAAC;YAEH,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACnE,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/D,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEzD,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,mBAAmB;YAC5C,SAAS,CAAC,qBAAqB,EAAE,CAAC,CAAC,mBAAmB;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC;gBACtC,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;aACnB,CAAC,CAAC;YAEH,MAAM,gBAAgB,GAAG,IAAI,eAAe,CAAC,SAAS,CAAC,EAAE,EAAE,cAAc,CAAC,CAAC;YAE3E,MAAM,CAAC,GAAG,EAAE,CAAE,SAAiB,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YACnF,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC;gBACtC,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;aACnB,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,SAAS,CAAC,cAAc,EAAE,CAAC;YAE5C,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACjC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\domain\\base-aggregate-root.spec.ts"], "sourcesContent": ["import { BaseAggregateRoot } from '../../domain/base-aggregate-root';\r\nimport { BaseDomainEvent } from '../../domain/base-domain-event';\r\nimport { UniqueEntityId } from '../../value-objects/unique-entity-id.value-object';\r\n\r\n// Test domain event\r\nclass TestDomainEvent extends BaseDomainEvent<{ message: string }> {\r\n  constructor(aggregateId: UniqueEntityId, message: string) {\r\n    super(aggregateId, { message });\r\n  }\r\n\r\n  get message(): string {\r\n    return this.eventData.message;\r\n  }\r\n}\r\n\r\nclass AnotherTestEvent extends BaseDomainEvent<{ value: number }> {\r\n  constructor(aggregateId: UniqueEntityId, value: number) {\r\n    super(aggregateId, { value });\r\n  }\r\n\r\n  get value(): number {\r\n    return this.eventData.value;\r\n  }\r\n}\r\n\r\n// Test aggregate root\r\ninterface TestAggregateProps {\r\n  name: string;\r\n  status: string;\r\n}\r\n\r\nclass TestAggregateRoot extends BaseAggregateRoot<TestAggregateProps> {\r\n  constructor(props: TestAggregateProps, id?: UniqueEntityId) {\r\n    super(props, id);\r\n  }\r\n\r\n  get name(): string {\r\n    return this.props.name;\r\n  }\r\n\r\n  get status(): string {\r\n    return this.props.status;\r\n  }\r\n\r\n  // Business method that raises domain event\r\n  changeName(newName: string): void {\r\n    const oldName = this.props.name;\r\n    (this.props as any).name = newName;\r\n    \r\n    this.addDomainEvent(new TestDomainEvent(this.id, `Name changed from ${oldName} to ${newName}`));\r\n  }\r\n\r\n  // Business method that raises multiple events\r\n  activate(): void {\r\n    (this.props as any).status = 'active';\r\n    \r\n    this.addDomainEvent(new TestDomainEvent(this.id, 'Aggregate activated'));\r\n    this.addDomainEvent(new AnotherTestEvent(this.id, 1));\r\n  }\r\n\r\n  // Method to test invariant validation\r\n  validateTestInvariant(): void {\r\n    this.validateInvariants();\r\n  }\r\n\r\n  // Override to add custom business rules\r\n  protected validateInvariants(): void {\r\n    super.validateInvariants();\r\n    \r\n    if (!this.props.name || this.props.name.trim().length === 0) {\r\n      throw new Error('Name cannot be empty');\r\n    }\r\n  }\r\n}\r\n\r\ndescribe('BaseAggregateRoot', () => {\r\n  describe('construction', () => {\r\n    it('should create aggregate with provided props', () => {\r\n      const props: TestAggregateProps = {\r\n        name: 'Test Aggregate',\r\n        status: 'inactive'\r\n      };\r\n      \r\n      const aggregate = new TestAggregateRoot(props);\r\n      \r\n      expect(aggregate.name).toBe('Test Aggregate');\r\n      expect(aggregate.status).toBe('inactive');\r\n      expect(aggregate.id).toBeInstanceOf(UniqueEntityId);\r\n    });\r\n\r\n    it('should create aggregate with provided ID', () => {\r\n      const props: TestAggregateProps = {\r\n        name: 'Test Aggregate',\r\n        status: 'inactive'\r\n      };\r\n      const id = UniqueEntityId.generate();\r\n      \r\n      const aggregate = new TestAggregateRoot(props, id);\r\n      \r\n      expect(aggregate.id.equals(id)).toBe(true);\r\n    });\r\n\r\n    it('should start with no domain events', () => {\r\n      const aggregate = new TestAggregateRoot({\r\n        name: 'Test Aggregate',\r\n        status: 'inactive'\r\n      });\r\n      \r\n      expect(aggregate.domainEvents).toHaveLength(0);\r\n      expect(aggregate.hasUnpublishedEvents()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('domain event management', () => {\r\n    it('should add domain events', () => {\r\n      const aggregate = new TestAggregateRoot({\r\n        name: 'Test Aggregate',\r\n        status: 'inactive'\r\n      });\r\n      \r\n      aggregate.changeName('New Name');\r\n      \r\n      expect(aggregate.domainEvents).toHaveLength(1);\r\n      expect(aggregate.domainEvents[0]).toBeInstanceOf(TestDomainEvent);\r\n      expect((aggregate.domainEvents[0] as TestDomainEvent).message).toBe('Name changed from Test Aggregate to New Name');\r\n    });\r\n\r\n    it('should add multiple domain events', () => {\r\n      const aggregate = new TestAggregateRoot({\r\n        name: 'Test Aggregate',\r\n        status: 'inactive'\r\n      });\r\n      \r\n      aggregate.activate();\r\n      \r\n      expect(aggregate.domainEvents).toHaveLength(2);\r\n      expect(aggregate.domainEvents[0]).toBeInstanceOf(TestDomainEvent);\r\n      expect(aggregate.domainEvents[1]).toBeInstanceOf(AnotherTestEvent);\r\n    });\r\n\r\n    it('should prevent duplicate events', () => {\r\n      const aggregate = new TestAggregateRoot({\r\n        name: 'Test Aggregate',\r\n        status: 'inactive'\r\n      });\r\n      \r\n      const event = new TestDomainEvent(aggregate.id, 'Test message');\r\n      \r\n      // Add the same event twice\r\n      (aggregate as any).addDomainEvent(event);\r\n      (aggregate as any).addDomainEvent(event);\r\n      \r\n      expect(aggregate.domainEvents).toHaveLength(1);\r\n    });\r\n\r\n    it('should return copy of domain events', () => {\r\n      const aggregate = new TestAggregateRoot({\r\n        name: 'Test Aggregate',\r\n        status: 'inactive'\r\n      });\r\n      \r\n      aggregate.changeName('New Name');\r\n      \r\n      const events1 = aggregate.domainEvents;\r\n      const events2 = aggregate.domainEvents;\r\n      \r\n      expect(events1).not.toBe(events2); // Different array instances\r\n      expect(events1).toEqual(events2); // Same content\r\n    });\r\n\r\n    it('should clear domain events', () => {\r\n      const aggregate = new TestAggregateRoot({\r\n        name: 'Test Aggregate',\r\n        status: 'inactive'\r\n      });\r\n      \r\n      aggregate.changeName('New Name');\r\n      expect(aggregate.domainEvents).toHaveLength(1);\r\n      \r\n      aggregate.clearEvents();\r\n      expect(aggregate.domainEvents).toHaveLength(0);\r\n    });\r\n\r\n    it('should remove specific domain event', () => {\r\n      const aggregate = new TestAggregateRoot({\r\n        name: 'Test Aggregate',\r\n        status: 'inactive'\r\n      });\r\n      \r\n      aggregate.activate(); // Adds 2 events\r\n      expect(aggregate.domainEvents).toHaveLength(2);\r\n      \r\n      const eventToRemove = aggregate.domainEvents[0];\r\n      (aggregate as any).removeDomainEvent(eventToRemove);\r\n      \r\n      expect(aggregate.domainEvents).toHaveLength(1);\r\n      expect(aggregate.domainEvents[0]).not.toBe(eventToRemove);\r\n    });\r\n  });\r\n\r\n  describe('event querying', () => {\r\n    it('should get events by type', () => {\r\n      const aggregate = new TestAggregateRoot({\r\n        name: 'Test Aggregate',\r\n        status: 'inactive'\r\n      });\r\n      \r\n      aggregate.activate(); // Adds TestDomainEvent and AnotherTestEvent\r\n      \r\n      const testEvents = aggregate.getEventsByType(TestDomainEvent);\r\n      const anotherEvents = aggregate.getEventsByType(AnotherTestEvent);\r\n      \r\n      expect(testEvents).toHaveLength(1);\r\n      expect(anotherEvents).toHaveLength(1);\r\n      expect(testEvents[0]).toBeInstanceOf(TestDomainEvent);\r\n      expect(anotherEvents[0]).toBeInstanceOf(AnotherTestEvent);\r\n    });\r\n\r\n    it('should check if has events of specific type', () => {\r\n      const aggregate = new TestAggregateRoot({\r\n        name: 'Test Aggregate',\r\n        status: 'inactive'\r\n      });\r\n      \r\n      expect(aggregate.hasEventsOfType(TestDomainEvent)).toBe(false);\r\n      \r\n      aggregate.changeName('New Name');\r\n      \r\n      expect(aggregate.hasEventsOfType(TestDomainEvent)).toBe(true);\r\n      expect(aggregate.hasEventsOfType(AnotherTestEvent)).toBe(false);\r\n    });\r\n\r\n    it('should check for unpublished events', () => {\r\n      const aggregate = new TestAggregateRoot({\r\n        name: 'Test Aggregate',\r\n        status: 'inactive'\r\n      });\r\n      \r\n      expect(aggregate.hasUnpublishedEvents()).toBe(false);\r\n      \r\n      aggregate.changeName('New Name');\r\n      \r\n      expect(aggregate.hasUnpublishedEvents()).toBe(true);\r\n    });\r\n\r\n    it('should get unpublished events', () => {\r\n      const aggregate = new TestAggregateRoot({\r\n        name: 'Test Aggregate',\r\n        status: 'inactive'\r\n      });\r\n      \r\n      aggregate.activate(); // Adds 2 events\r\n      \r\n      const unpublishedEvents = aggregate.getUnpublishedEvents();\r\n      expect(unpublishedEvents).toHaveLength(2);\r\n      \r\n      // Mark first event as dispatched\r\n      aggregate.domainEvents[0].markAsDispatched();\r\n      \r\n      const stillUnpublished = aggregate.getUnpublishedEvents();\r\n      expect(stillUnpublished).toHaveLength(1);\r\n    });\r\n\r\n    it('should mark events for dispatch', () => {\r\n      const aggregate = new TestAggregateRoot({\r\n        name: 'Test Aggregate',\r\n        status: 'inactive'\r\n      });\r\n      \r\n      aggregate.activate();\r\n      \r\n      expect(aggregate.hasUnpublishedEvents()).toBe(true);\r\n      \r\n      aggregate.markEventsForDispatch();\r\n      \r\n      expect(aggregate.hasUnpublishedEvents()).toBe(false);\r\n      expect(aggregate.domainEvents.every(e => e.isDispatched)).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('versioning', () => {\r\n    it('should track version based on event count', () => {\r\n      const aggregate = new TestAggregateRoot({\r\n        name: 'Test Aggregate',\r\n        status: 'inactive'\r\n      });\r\n      \r\n      expect(aggregate.getVersion()).toBe(0);\r\n      \r\n      aggregate.changeName('New Name');\r\n      expect(aggregate.getVersion()).toBe(1);\r\n      \r\n      aggregate.activate();\r\n      expect(aggregate.getVersion()).toBe(3); // +2 events from activate\r\n    });\r\n\r\n    it('should create snapshot', () => {\r\n      const aggregate = new TestAggregateRoot({\r\n        name: 'Test Aggregate',\r\n        status: 'inactive'\r\n      });\r\n      \r\n      aggregate.changeName('New Name');\r\n      \r\n      const snapshot = aggregate.createSnapshot();\r\n      \r\n      expect(snapshot.aggregateId).toBe(aggregate.id.toString());\r\n      expect(snapshot.version).toBe(1);\r\n      expect(snapshot.state).toEqual({ name: 'New Name', status: 'inactive' });\r\n      expect(snapshot.timestamp).toBeInstanceOf(Date);\r\n    });\r\n  });\r\n\r\n  describe('invariant validation', () => {\r\n    it('should validate invariants', () => {\r\n      const aggregate = new TestAggregateRoot({\r\n        name: 'Test Aggregate',\r\n        status: 'inactive'\r\n      });\r\n      \r\n      expect(() => aggregate.validateTestInvariant()).not.toThrow();\r\n    });\r\n\r\n    it('should throw error for invalid invariants', () => {\r\n      const aggregate = new TestAggregateRoot({\r\n        name: '',\r\n        status: 'inactive'\r\n      });\r\n      \r\n      expect(() => aggregate.validateTestInvariant()).toThrow('Name cannot be empty');\r\n    });\r\n\r\n    it('should validate base invariants', () => {\r\n      // Create aggregate with null ID (bypassing constructor)\r\n      const aggregate = Object.create(TestAggregateRoot.prototype);\r\n      aggregate.props = { name: 'Test', status: 'active' };\r\n      aggregate._id = null;\r\n      \r\n      expect(() => aggregate.validateTestInvariant()).toThrow('Aggregate must have a valid ID');\r\n    });\r\n  });\r\n\r\n  describe('equality comparison', () => {\r\n    it('should be equal to itself', () => {\r\n      const aggregate = new TestAggregateRoot({\r\n        name: 'Test Aggregate',\r\n        status: 'inactive'\r\n      });\r\n      \r\n      expect(aggregate.equals(aggregate)).toBe(true);\r\n    });\r\n\r\n    it('should be equal to aggregate with same ID and version', () => {\r\n      const id = UniqueEntityId.generate();\r\n      const props: TestAggregateProps = {\r\n        name: 'Test Aggregate',\r\n        status: 'inactive'\r\n      };\r\n      \r\n      const aggregate1 = new TestAggregateRoot(props, id);\r\n      const aggregate2 = new TestAggregateRoot(props, id);\r\n      \r\n      expect(aggregate1.equals(aggregate2)).toBe(true);\r\n    });\r\n\r\n    it('should not be equal to aggregate with same ID but different version', () => {\r\n      const id = UniqueEntityId.generate();\r\n      const props: TestAggregateProps = {\r\n        name: 'Test Aggregate',\r\n        status: 'inactive'\r\n      };\r\n      \r\n      const aggregate1 = new TestAggregateRoot(props, id);\r\n      const aggregate2 = new TestAggregateRoot(props, id);\r\n      \r\n      aggregate1.changeName('New Name'); // Changes version\r\n      \r\n      expect(aggregate1.equals(aggregate2)).toBe(false);\r\n    });\r\n\r\n    it('should not be equal to aggregate with different ID', () => {\r\n      const props: TestAggregateProps = {\r\n        name: 'Test Aggregate',\r\n        status: 'inactive'\r\n      };\r\n      \r\n      const aggregate1 = new TestAggregateRoot(props);\r\n      const aggregate2 = new TestAggregateRoot(props);\r\n      \r\n      expect(aggregate1.equals(aggregate2)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('JSON serialization', () => {\r\n    it('should serialize to JSON with events and version', () => {\r\n      const aggregate = new TestAggregateRoot({\r\n        name: 'Test Aggregate',\r\n        status: 'inactive'\r\n      });\r\n      \r\n      aggregate.changeName('New Name');\r\n      \r\n      const json = aggregate.toJSON();\r\n      \r\n      expect(json).toHaveProperty('id');\r\n      expect(json).toHaveProperty('name', 'New Name');\r\n      expect(json).toHaveProperty('status', 'inactive');\r\n      expect(json).toHaveProperty('domainEvents');\r\n      expect(json).toHaveProperty('version', 1);\r\n      expect(json.domainEvents).toHaveLength(1);\r\n    });\r\n\r\n    it('should include event metadata in JSON', () => {\r\n      const aggregate = new TestAggregateRoot({\r\n        name: 'Test Aggregate',\r\n        status: 'inactive'\r\n      });\r\n      \r\n      aggregate.activate();\r\n      \r\n      const json = aggregate.toJSON();\r\n      \r\n      expect(json.domainEvents).toHaveLength(2);\r\n      expect(json.domainEvents[0]).toHaveProperty('eventType', 'TestDomainEvent');\r\n      expect(json.domainEvents[0]).toHaveProperty('eventId');\r\n      expect(json.domainEvents[0]).toHaveProperty('occurredOn');\r\n      expect(json.domainEvents[0]).toHaveProperty('isDispatched', false);\r\n    });\r\n  });\r\n\r\n  describe('cloning', () => {\r\n    it('should create a deep copy', () => {\r\n      const aggregate = new TestAggregateRoot({\r\n        name: 'Test Aggregate',\r\n        status: 'inactive'\r\n      });\r\n      \r\n      aggregate.changeName('New Name');\r\n      \r\n      const clone = aggregate.clone();\r\n      \r\n      expect(clone).not.toBe(aggregate);\r\n      expect(clone.id.equals(aggregate.id)).toBe(true);\r\n      expect(clone.name).toBe(aggregate.name);\r\n      expect(clone.status).toBe(aggregate.status);\r\n      expect(clone.domainEvents).toHaveLength(aggregate.domainEvents.length);\r\n    });\r\n\r\n    it('should create independent clone', () => {\r\n      const aggregate = new TestAggregateRoot({\r\n        name: 'Test Aggregate',\r\n        status: 'inactive'\r\n      });\r\n      \r\n      const clone = aggregate.clone();\r\n      \r\n      // Modify original\r\n      aggregate.changeName('Modified Name');\r\n      \r\n      // Clone should not be affected\r\n      expect(clone.name).toBe('Test Aggregate');\r\n      expect(clone.domainEvents).toHaveLength(0);\r\n    });\r\n  });\r\n\r\n  describe('inheritance', () => {\r\n    // Extended aggregate for testing inheritance\r\n    interface ExtendedAggregateProps extends TestAggregateProps {\r\n      category: string;\r\n    }\r\n\r\n    class ExtendedAggregateRoot extends TestAggregateRoot {\r\n      constructor(props: ExtendedAggregateProps, id?: UniqueEntityId) {\r\n        super(props, id);\r\n      }\r\n\r\n      get category(): string {\r\n        return (this.props as ExtendedAggregateProps).category;\r\n      }\r\n\r\n      changeCategory(newCategory: string): void {\r\n        (this.props as any).category = newCategory;\r\n        this.addDomainEvent(new TestDomainEvent(this.id, `Category changed to ${newCategory}`));\r\n      }\r\n    }\r\n\r\n    it('should support inheritance', () => {\r\n      const props: ExtendedAggregateProps = {\r\n        name: 'Extended Aggregate',\r\n        status: 'active',\r\n        category: 'Test Category'\r\n      };\r\n      \r\n      const aggregate = new ExtendedAggregateRoot(props);\r\n      \r\n      expect(aggregate).toBeInstanceOf(ExtendedAggregateRoot);\r\n      expect(aggregate).toBeInstanceOf(TestAggregateRoot);\r\n      expect(aggregate).toBeInstanceOf(BaseAggregateRoot);\r\n      expect(aggregate.category).toBe('Test Category');\r\n    });\r\n\r\n    it('should maintain base functionality in derived classes', () => {\r\n      const props: ExtendedAggregateProps = {\r\n        name: 'Extended Aggregate',\r\n        status: 'active',\r\n        category: 'Test Category'\r\n      };\r\n      \r\n      const aggregate = new ExtendedAggregateRoot(props);\r\n      \r\n      aggregate.changeCategory('New Category');\r\n      \r\n      expect(aggregate.domainEvents).toHaveLength(1);\r\n      expect(aggregate.getVersion()).toBe(1);\r\n    });\r\n  });\r\n\r\n  describe('edge cases', () => {\r\n    it('should handle empty event list operations', () => {\r\n      const aggregate = new TestAggregateRoot({\r\n        name: 'Test Aggregate',\r\n        status: 'inactive'\r\n      });\r\n      \r\n      expect(aggregate.getEventsByType(TestDomainEvent)).toHaveLength(0);\r\n      expect(aggregate.hasEventsOfType(TestDomainEvent)).toBe(false);\r\n      expect(aggregate.getUnpublishedEvents()).toHaveLength(0);\r\n      \r\n      aggregate.clearEvents(); // Should not throw\r\n      aggregate.markEventsForDispatch(); // Should not throw\r\n    });\r\n\r\n    it('should handle removing non-existent event', () => {\r\n      const aggregate = new TestAggregateRoot({\r\n        name: 'Test Aggregate',\r\n        status: 'inactive'\r\n      });\r\n      \r\n      const nonExistentEvent = new TestDomainEvent(aggregate.id, 'Non-existent');\r\n      \r\n      expect(() => (aggregate as any).removeDomainEvent(nonExistentEvent)).not.toThrow();\r\n      expect(aggregate.domainEvents).toHaveLength(0);\r\n    });\r\n\r\n    it('should handle snapshot with no events', () => {\r\n      const aggregate = new TestAggregateRoot({\r\n        name: 'Test Aggregate',\r\n        status: 'inactive'\r\n      });\r\n      \r\n      const snapshot = aggregate.createSnapshot();\r\n      \r\n      expect(snapshot.version).toBe(0);\r\n      expect(snapshot.state).toEqual({ name: 'Test Aggregate', status: 'inactive' });\r\n    });\r\n  });\r\n});"], "version": 3}