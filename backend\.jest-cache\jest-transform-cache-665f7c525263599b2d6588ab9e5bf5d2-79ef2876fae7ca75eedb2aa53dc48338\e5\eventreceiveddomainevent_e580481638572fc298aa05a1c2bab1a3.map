{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\event-received.domain-event.ts", "mappings": ";;;AAAA,6DAA4E;AAE5E,sEAA6D;AA4B7D;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAa,wBAAyB,SAAQ,+BAAuC;IACnF,YACE,WAA2B,EAC3B,SAAiC,EACjC,OAOC;QAED,KAAK,CAAC,WAAW,EAAE,SAAS,EAAE;YAC5B,YAAY,EAAE,CAAC;YACf,GAAG,OAAO;YACV,QAAQ,EAAE;gBACR,SAAS,EAAE,eAAe;gBAC1B,MAAM,EAAE,UAAU;gBAClB,aAAa,EAAE,OAAO;gBACtB,eAAe,EAAE,QAAQ;gBACzB,GAAG,OAAO,EAAE,QAAQ;aACrB;SACF,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,QAAQ,KAAK,MAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,UAAU,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,QAAQ,KAAK,UAAU,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,CAAC,mCAAa,CAAC,IAAI,EAAE,mCAAa,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,QAAQ,KAAK,mCAAa,CAAC,QAAQ,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,MAAM;IACnD,CAAC;IAED;;OAEG;IACH,YAAY;QACV,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,CAAC;QAC/D,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,CAAC;QACxD,MAAM,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,4BAA4B;QAE/D,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC,GAAG,WAAW,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,CAAC;QAC/D,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,CAAC;QACxD,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,YAAY,GAAG,YAAY,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,2BAA2B;QACzB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,oBAAoB,EAAE,GAAG,IAAI,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAAE,OAAO,CAAC,CAAC,CAAG,WAAW;QACtD,IAAI,IAAI,CAAC,cAAc,EAAE;YAAE,OAAO,CAAC,CAAC,CAAO,YAAY;QACvD,IAAI,IAAI,CAAC,cAAc,EAAE;YAAE,OAAO,EAAE,CAAC,CAAM,aAAa;QACxD,OAAO,EAAE,CAAC,CAAiC,qBAAqB;IAClE,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAAE,OAAO,iBAAiB,CAAC;QACxD,IAAI,IAAI,CAAC,cAAc,EAAE;YAAE,OAAO,sBAAsB,CAAC;QACzD,IAAI,IAAI,CAAC,cAAc,EAAE;YAAE,OAAO,sBAAsB,CAAC;QACzD,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,sBAAsB;QAKpB,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;QAC/D,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAEzC,OAAO;YACL,GAAG,EAAE,UAAU,IAAI,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ;YAChD,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ;YAC/C,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK;SAC9C,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,gBAAgB;QAYd,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;YAC5B,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;YACxC,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,QAAQ;YACnC,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,CAAC;YAC5B,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,EAAE;YAC9C,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE;YAC5B,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE;YAC5B,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;SAC3C,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,oBAAoB;QAOlB,MAAM,cAAc,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;QACpE,MAAM,iBAAiB,GAAG,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CACrD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,IAAI,CAClE,CAAC;QAEF,MAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;QAC3D,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QAE7C,mDAAmD;QACnD,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;QACrD,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAChE,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,EAAE,CACtD,CAAC,MAAM,CAAC;QACT,MAAM,YAAY,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,cAAc,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE5F,IAAI,aAAa,GAA6B,MAAM,CAAC;QACrD,IAAI,CAAC,iBAAiB,IAAI,CAAC,iBAAiB,IAAI,CAAC,cAAc,EAAE,CAAC;YAChE,aAAa,GAAG,MAAM,CAAC;QACzB,CAAC;aAAM,IAAI,YAAY,GAAG,EAAE,EAAE,CAAC;YAC7B,aAAa,GAAG,MAAM,CAAC;QACzB,CAAC;QAED,OAAO;YACL,iBAAiB;YACjB,iBAAiB;YACjB,cAAc;YACd,aAAa;YACb,YAAY;SACb,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,MAAM,KAAK,GAAa,CAAC,0BAA0B,CAAC,CAAC;QAErD,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5C,IAAI,OAAO,CAAC,aAAa,KAAK,MAAM,EAAE,CAAC;YACrC,KAAK,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACzC,CAAC;QAED,KAAK,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAErC,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;YAC1B,KAAK,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;YACxB,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACrC,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;YACxB,KAAK,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACvC,CAAC;QAED,KAAK,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAE3C,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,kBAAkB;QAsChB,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5C,OAAO;YACL,SAAS,EAAE,eAAe;YAC1B,OAAO,EAAE,KAAK;YACd,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;YACxC,IAAI,EAAE;gBACJ,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;gBACpC,KAAK,EAAE;oBACL,IAAI,EAAE,IAAI,CAAC,SAAS;oBACpB,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;oBACzC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE;iBACxD;gBACD,MAAM,EAAE;oBACN,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;oBACtB,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;iBACnC;gBACD,UAAU,EAAE;oBACV,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,QAAQ;oBACnC,KAAK,EAAE,IAAI,CAAC,kBAAkB,EAAE;oBAChC,OAAO,EAAE,IAAI,CAAC,qBAAqB,EAAE;oBACrC,OAAO,EAAE,IAAI,CAAC,oBAAoB,EAAE;iBACrC;gBACD,OAAO,EAAE;oBACP,aAAa,EAAE,OAAO,CAAC,aAAa;oBACpC,YAAY,EAAE,OAAO,CAAC,YAAY;oBAClC,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE;oBAC5B,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE;iBAC7B;aACF;YACD,QAAQ,EAAE;gBACR,aAAa,EAAE,IAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,MAAM,EAAE,UAAU;gBAClB,aAAa,EAAE,OAAO;gBACtB,eAAe,EAAE,QAAQ;aAC1B;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,GAAG,KAAK,CAAC,MAAM,EAAE;YACjB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE;gBACR,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE;gBACrC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,EAAE;gBAC7C,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE;gBACrC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,EAAE;gBAC7C,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE;gBACjC,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE;gBACjC,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,EAAE;gBAC9C,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;gBAC1C,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,EAAE;gBAChD,mBAAmB,EAAE,IAAI,CAAC,sBAAsB,EAAE;gBAClD,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,EAAE;gBAC9C,mBAAmB,EAAE,IAAI,CAAC,sBAAsB,EAAE;aACnD;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,IAAyB;QACvC,OAAO,IAAI,wBAAwB,CACjC,8BAAc,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,EAC3C,IAAI,CAAC,SAAS,EACd;YACE,OAAO,EAAE,8BAAc,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC;YAChD,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;YACrC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;QACjD,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,QAAQ,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;QACzE,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;QACxD,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;QAExD,OAAO,GAAG,YAAY,aAAa,IAAI,CAAC,SAAS,wBAAwB,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,YAAY,GAAG,SAAS,GAAG,SAAS,EAAE,CAAC;IAC3I,CAAC;IAED;;OAEG;IACH,UAAU;QAUR,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5C,OAAO;YACL,SAAS,EAAE,eAAe;YAC1B,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;YACpC,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;YAC9B,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,QAAQ;YACnC,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,EAAE;YAC9C,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;SACzC,CAAC;IACJ,CAAC;CACF;AArcD,4DAqcC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\event-received.domain-event.ts"], "sourcesContent": ["import { BaseDomainEvent, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { EventType } from '../enums/event-type.enum';\r\nimport { EventSeverity } from '../enums/event-severity.enum';\r\nimport { EventSource } from '../value-objects/event-metadata/event-source.value-object';\r\nimport { EventTimestamp } from '../value-objects/event-metadata/event-timestamp.value-object';\r\n\r\n/**\r\n * Event Received Domain Event Data\r\n */\r\nexport interface EventReceivedEventData {\r\n  /** Type of the received event */\r\n  eventType: EventType;\r\n  /** Severity of the received event */\r\n  severity: EventSeverity;\r\n  /** Source of the received event */\r\n  source: EventSource;\r\n  /** Timestamp when the event was received */\r\n  receivedAt: EventTimestamp;\r\n  /** Original event timestamp */\r\n  originalTimestamp: EventTimestamp;\r\n  /** Raw event data as received */\r\n  rawData: Record<string, any>;\r\n  /** Event title or description */\r\n  title: string;\r\n  /** Size of the raw event data in bytes */\r\n  dataSize?: number;\r\n  /** Processing priority */\r\n  priority?: 'low' | 'normal' | 'high' | 'critical';\r\n}\r\n\r\n/**\r\n * Event Received Domain Event\r\n * \r\n * Raised when a new security event is received by the system from external sources.\r\n * This is the first event in the security event processing pipeline and triggers\r\n * the normalization process.\r\n * \r\n * Key characteristics:\r\n * - Marks the entry point of events into the system\r\n * - Contains raw, unprocessed event data\r\n * - Triggers downstream processing workflows\r\n * - Used for intake metrics and monitoring\r\n * \r\n * Downstream processes triggered:\r\n * - Event validation and parsing\r\n * - Event normalization pipeline\r\n * - Initial threat assessment\r\n * - Event routing and classification\r\n * - Intake metrics collection\r\n */\r\nexport class EventReceivedDomainEvent extends BaseDomainEvent<EventReceivedEventData> {\r\n  constructor(\r\n    aggregateId: UniqueEntityId,\r\n    eventData: EventReceivedEventData,\r\n    options?: {\r\n      eventId?: UniqueEntityId;\r\n      occurredOn?: Date;\r\n      eventVersion?: number;\r\n      correlationId?: string;\r\n      causationId?: string;\r\n      metadata?: Record<string, any>;\r\n    }\r\n  ) {\r\n    super(aggregateId, eventData, {\r\n      eventVersion: 1,\r\n      ...options,\r\n      metadata: {\r\n        eventType: 'EventReceived',\r\n        domain: 'Security',\r\n        aggregateType: 'Event',\r\n        processingStage: 'intake',\r\n        ...options?.metadata,\r\n      },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get the event type\r\n   */\r\n  get eventType(): EventType {\r\n    return this.eventData.eventType;\r\n  }\r\n\r\n  /**\r\n   * Get the event severity\r\n   */\r\n  get severity(): EventSeverity {\r\n    return this.eventData.severity;\r\n  }\r\n\r\n  /**\r\n   * Get the event source\r\n   */\r\n  get source(): EventSource {\r\n    return this.eventData.source;\r\n  }\r\n\r\n  /**\r\n   * Get when the event was received\r\n   */\r\n  get receivedAt(): EventTimestamp {\r\n    return this.eventData.receivedAt;\r\n  }\r\n\r\n  /**\r\n   * Get the original event timestamp\r\n   */\r\n  get originalTimestamp(): EventTimestamp {\r\n    return this.eventData.originalTimestamp;\r\n  }\r\n\r\n  /**\r\n   * Get the raw event data\r\n   */\r\n  get rawData(): Record<string, any> {\r\n    return this.eventData.rawData;\r\n  }\r\n\r\n  /**\r\n   * Get the event title\r\n   */\r\n  get title(): string {\r\n    return this.eventData.title;\r\n  }\r\n\r\n  /**\r\n   * Get the data size in bytes\r\n   */\r\n  get dataSize(): number | undefined {\r\n    return this.eventData.dataSize;\r\n  }\r\n\r\n  /**\r\n   * Get the processing priority\r\n   */\r\n  get priority(): 'low' | 'normal' | 'high' | 'critical' | undefined {\r\n    return this.eventData.priority;\r\n  }\r\n\r\n  /**\r\n   * Check if the event is high priority\r\n   */\r\n  isHighPriority(): boolean {\r\n    return this.priority === 'high' || this.priority === 'critical';\r\n  }\r\n\r\n  /**\r\n   * Check if the event is critical priority\r\n   */\r\n  isCriticalPriority(): boolean {\r\n    return this.priority === 'critical';\r\n  }\r\n\r\n  /**\r\n   * Check if the event is high severity\r\n   */\r\n  isHighSeverity(): boolean {\r\n    return [EventSeverity.HIGH, EventSeverity.CRITICAL].includes(this.severity);\r\n  }\r\n\r\n  /**\r\n   * Check if the event is critical severity\r\n   */\r\n  isCriticalSeverity(): boolean {\r\n    return this.severity === EventSeverity.CRITICAL;\r\n  }\r\n\r\n  /**\r\n   * Check if the event is large (over 1MB)\r\n   */\r\n  isLargeEvent(): boolean {\r\n    return (this.dataSize || 0) > 1024 * 1024; // 1MB\r\n  }\r\n\r\n  /**\r\n   * Check if the event is stale (received more than 5 minutes after original timestamp)\r\n   */\r\n  isStaleEvent(): boolean {\r\n    const originalTime = this.originalTimestamp.toDate().getTime();\r\n    const receivedTime = this.receivedAt.toDate().getTime();\r\n    const fiveMinutes = 5 * 60 * 1000; // 5 minutes in milliseconds\r\n    \r\n    return (receivedTime - originalTime) > fiveMinutes;\r\n  }\r\n\r\n  /**\r\n   * Get processing latency in milliseconds\r\n   */\r\n  getProcessingLatency(): number {\r\n    const originalTime = this.originalTimestamp.toDate().getTime();\r\n    const receivedTime = this.receivedAt.toDate().getTime();\r\n    return Math.max(0, receivedTime - originalTime);\r\n  }\r\n\r\n  /**\r\n   * Get processing latency in seconds\r\n   */\r\n  getProcessingLatencySeconds(): number {\r\n    return Math.floor(this.getProcessingLatency() / 1000);\r\n  }\r\n\r\n  /**\r\n   * Get recommended processing timeout in minutes\r\n   */\r\n  getRecommendedTimeout(): number {\r\n    if (this.isCriticalPriority()) return 1;   // 1 minute\r\n    if (this.isHighPriority()) return 5;       // 5 minutes\r\n    if (this.isHighSeverity()) return 15;      // 15 minutes\r\n    return 30;                                 // 30 minutes default\r\n  }\r\n\r\n  /**\r\n   * Get processing queue name\r\n   */\r\n  getProcessingQueue(): string {\r\n    if (this.isCriticalPriority()) return 'critical-events';\r\n    if (this.isHighPriority()) return 'high-priority-events';\r\n    if (this.isHighSeverity()) return 'high-severity-events';\r\n    return 'standard-events';\r\n  }\r\n\r\n  /**\r\n   * Get required processing resources\r\n   */\r\n  getProcessingResources(): {\r\n    cpu: 'low' | 'medium' | 'high';\r\n    memory: 'low' | 'medium' | 'high';\r\n    storage: 'low' | 'medium' | 'high';\r\n  } {\r\n    const isComplex = this.isLargeEvent() || this.isHighSeverity();\r\n    const isPriority = this.isHighPriority();\r\n    \r\n    return {\r\n      cpu: isPriority || isComplex ? 'high' : 'medium',\r\n      memory: this.isLargeEvent() ? 'high' : 'medium',\r\n      storage: this.isLargeEvent() ? 'high' : 'low',\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get intake metrics\r\n   */\r\n  getIntakeMetrics(): {\r\n    eventType: EventType;\r\n    severity: EventSeverity;\r\n    sourceType: string;\r\n    sourceIdentifier: string;\r\n    priority: string;\r\n    dataSize: number;\r\n    processingLatency: number;\r\n    isStale: boolean;\r\n    isLarge: boolean;\r\n    processingQueue: string;\r\n  } {\r\n    return {\r\n      eventType: this.eventType,\r\n      severity: this.severity,\r\n      sourceType: this.source.type,\r\n      sourceIdentifier: this.source.identifier,\r\n      priority: this.priority || 'normal',\r\n      dataSize: this.dataSize || 0,\r\n      processingLatency: this.getProcessingLatency(),\r\n      isStale: this.isStaleEvent(),\r\n      isLarge: this.isLargeEvent(),\r\n      processingQueue: this.getProcessingQueue(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get quality indicators\r\n   */\r\n  getQualityIndicators(): {\r\n    hasRequiredFields: boolean;\r\n    hasValidTimestamp: boolean;\r\n    hasValidSource: boolean;\r\n    dataIntegrity: 'good' | 'fair' | 'poor';\r\n    completeness: number; // 0-100\r\n  } {\r\n    const requiredFields = ['eventType', 'severity', 'source', 'title'];\r\n    const hasRequiredFields = requiredFields.every(field => \r\n      this.rawData[field] !== undefined && this.rawData[field] !== null\r\n    );\r\n    \r\n    const hasValidTimestamp = this.originalTimestamp.isValid();\r\n    const hasValidSource = this.source.isValid();\r\n    \r\n    // Calculate completeness based on available fields\r\n    const totalFields = Object.keys(this.rawData).length;\r\n    const nonEmptyFields = Object.values(this.rawData).filter(value => \r\n      value !== null && value !== undefined && value !== ''\r\n    ).length;\r\n    const completeness = totalFields > 0 ? Math.round((nonEmptyFields / totalFields) * 100) : 0;\r\n    \r\n    let dataIntegrity: 'good' | 'fair' | 'poor' = 'good';\r\n    if (!hasRequiredFields || !hasValidTimestamp || !hasValidSource) {\r\n      dataIntegrity = 'poor';\r\n    } else if (completeness < 70) {\r\n      dataIntegrity = 'fair';\r\n    }\r\n    \r\n    return {\r\n      hasRequiredFields,\r\n      hasValidTimestamp,\r\n      hasValidSource,\r\n      dataIntegrity,\r\n      completeness,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get next processing steps\r\n   */\r\n  getNextProcessingSteps(): string[] {\r\n    const steps: string[] = ['validate_event_structure'];\r\n    \r\n    const quality = this.getQualityIndicators();\r\n    \r\n    if (quality.dataIntegrity === 'poor') {\r\n      steps.push('data_quality_remediation');\r\n    }\r\n    \r\n    steps.push('normalize_event_format');\r\n    \r\n    if (this.isHighSeverity()) {\r\n      steps.push('priority_threat_assessment');\r\n    }\r\n    \r\n    if (this.isStaleEvent()) {\r\n      steps.push('stale_event_handling');\r\n    }\r\n    \r\n    if (this.isLargeEvent()) {\r\n      steps.push('large_event_processing');\r\n    }\r\n    \r\n    steps.push('route_to_processing_pipeline');\r\n    \r\n    return steps;\r\n  }\r\n\r\n  /**\r\n   * Convert to integration event for external systems\r\n   */\r\n  toIntegrationEvent(): {\r\n    eventType: string;\r\n    version: string;\r\n    timestamp: string;\r\n    data: {\r\n      eventId: string;\r\n      event: {\r\n        type: EventType;\r\n        severity: EventSeverity;\r\n        title: string;\r\n        receivedAt: string;\r\n        originalTimestamp: string;\r\n      };\r\n      source: {\r\n        type: string;\r\n        identifier: string;\r\n      };\r\n      processing: {\r\n        priority: string;\r\n        queue: string;\r\n        timeout: number;\r\n        latency: number;\r\n      };\r\n      quality: {\r\n        dataIntegrity: string;\r\n        completeness: number;\r\n        isStale: boolean;\r\n        isLarge: boolean;\r\n      };\r\n    };\r\n    metadata: {\r\n      correlationId?: string;\r\n      causationId?: string;\r\n      domain: string;\r\n      aggregateType: string;\r\n      processingStage: string;\r\n    };\r\n  } {\r\n    const quality = this.getQualityIndicators();\r\n    \r\n    return {\r\n      eventType: 'EventReceived',\r\n      version: '1.0',\r\n      timestamp: this.occurredOn.toISOString(),\r\n      data: {\r\n        eventId: this.aggregateId.toString(),\r\n        event: {\r\n          type: this.eventType,\r\n          severity: this.severity,\r\n          title: this.title,\r\n          receivedAt: this.receivedAt.toISOString(),\r\n          originalTimestamp: this.originalTimestamp.toISOString(),\r\n        },\r\n        source: {\r\n          type: this.source.type,\r\n          identifier: this.source.identifier,\r\n        },\r\n        processing: {\r\n          priority: this.priority || 'normal',\r\n          queue: this.getProcessingQueue(),\r\n          timeout: this.getRecommendedTimeout(),\r\n          latency: this.getProcessingLatency(),\r\n        },\r\n        quality: {\r\n          dataIntegrity: quality.dataIntegrity,\r\n          completeness: quality.completeness,\r\n          isStale: this.isStaleEvent(),\r\n          isLarge: this.isLargeEvent(),\r\n        },\r\n      },\r\n      metadata: {\r\n        correlationId: this.correlationId,\r\n        causationId: this.causationId,\r\n        domain: 'Security',\r\n        aggregateType: 'Event',\r\n        processingStage: 'intake',\r\n      },\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      ...super.toJSON(),\r\n      eventData: this.eventData,\r\n      analysis: {\r\n        isHighPriority: this.isHighPriority(),\r\n        isCriticalPriority: this.isCriticalPriority(),\r\n        isHighSeverity: this.isHighSeverity(),\r\n        isCriticalSeverity: this.isCriticalSeverity(),\r\n        isLargeEvent: this.isLargeEvent(),\r\n        isStaleEvent: this.isStaleEvent(),\r\n        processingLatency: this.getProcessingLatency(),\r\n        processingQueue: this.getProcessingQueue(),\r\n        recommendedTimeout: this.getRecommendedTimeout(),\r\n        processingResources: this.getProcessingResources(),\r\n        qualityIndicators: this.getQualityIndicators(),\r\n        nextProcessingSteps: this.getNextProcessingSteps(),\r\n      },\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create from JSON representation\r\n   */\r\n  static fromJSON(json: Record<string, any>): EventReceivedDomainEvent {\r\n    return new EventReceivedDomainEvent(\r\n      UniqueEntityId.fromString(json.aggregateId),\r\n      json.eventData,\r\n      {\r\n        eventId: UniqueEntityId.fromString(json.eventId),\r\n        occurredOn: new Date(json.occurredOn),\r\n        eventVersion: json.eventVersion,\r\n        correlationId: json.correlationId,\r\n        causationId: json.causationId,\r\n        metadata: json.metadata,\r\n      }\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get human-readable description\r\n   */\r\n  getDescription(): string {\r\n    const severityText = this.severity.toLowerCase();\r\n    const priorityText = this.priority ? ` (${this.priority} priority)` : '';\r\n    const staleText = this.isStaleEvent() ? ' [STALE]' : '';\r\n    const largeText = this.isLargeEvent() ? ' [LARGE]' : '';\r\n    \r\n    return `${severityText} severity ${this.eventType} event received from ${this.source.identifier}${priorityText}${staleText}${largeText}`;\r\n  }\r\n\r\n  /**\r\n   * Get event summary for logging\r\n   */\r\n  getSummary(): {\r\n    eventType: string;\r\n    eventId: string;\r\n    severity: string;\r\n    source: string;\r\n    priority: string;\r\n    processingLatency: number;\r\n    qualityScore: number;\r\n    timestamp: string;\r\n  } {\r\n    const quality = this.getQualityIndicators();\r\n    \r\n    return {\r\n      eventType: 'EventReceived',\r\n      eventId: this.aggregateId.toString(),\r\n      severity: this.severity,\r\n      source: this.source.identifier,\r\n      priority: this.priority || 'normal',\r\n      processingLatency: this.getProcessingLatency(),\r\n      qualityScore: quality.completeness,\r\n      timestamp: this.occurredOn.toISOString(),\r\n    };\r\n  }\r\n}"], "version": 3}