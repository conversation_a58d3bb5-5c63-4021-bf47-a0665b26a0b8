{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\__tests__\\enriched-event.factory.spec.ts", "mappings": ";;AAAA,sEAA6I;AAC7I,gFAAuF;AACvF,oFAA8F;AAC9F,gEAA8D;AAC9D,gHAA+F;AAC/F,kHAAiG;AACjG,4GAA2F;AAC3F,iEAAwD;AACxD,yEAAgE;AAChE,qEAA4D;AAC5D,2FAAiF;AACjF,+EAAqE;AACrE,+EAAsE;AAEtE,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;IACpC,IAAI,mBAAoC,CAAC;IACzC,IAAI,YAA2B,CAAC;IAEhC,UAAU,CAAC,GAAG,EAAE;QACd,uBAAuB;QACvB,MAAM,WAAW,GAAG,uCAAW,CAAC,MAAM,CACpC,wCAAe,CAAC,IAAI,EACpB,eAAe,CAChB,CAAC;QACF,MAAM,cAAc,GAAG,6CAAc,CAAC,MAAM,EAAE,CAAC;QAC/C,YAAY,GAAG,2CAAa,CAAC,MAAM,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAEjE,+BAA+B;QAC/B,mBAAmB,GAAG;YACpB,EAAE,EAAE,8BAAc,CAAC,MAAM,EAAE;YAC3B,QAAQ,EAAE,YAAY;YACtB,IAAI,EAAE,2BAAS,CAAC,eAAe;YAC/B,QAAQ,EAAE,mCAAa,CAAC,MAAM;YAC9B,MAAM,EAAE,+BAAW,CAAC,MAAM;YAC1B,gBAAgB,EAAE,oDAAqB,CAAC,UAAU;YAClD,mBAAmB,EAAE,6CAAmB,CAAC,SAAS;YAClD,cAAc,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,IAAI,EAAE;YAC1D,KAAK,EAAE,uBAAuB;YAC9B,WAAW,EAAE,yBAAyB;YACtC,IAAI,EAAE,CAAC,MAAM,EAAE,YAAY,CAAC;YAC5B,SAAS,EAAE,EAAE;YACb,eAAe,EAAE,EAAE;YACnB,UAAU,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;YAC1B,aAAa,EAAE,sBAAsB;YACrC,aAAa,EAAE,SAAS;SACN,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,OAAO,GAA+B;gBAC1C,eAAe,EAAE,mBAAmB;gBACpC,YAAY,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,EAAE,EAAE;aACnD,CAAC;YAEF,MAAM,aAAa,GAAG,6CAAoB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE3D,MAAM,CAAC,aAAa,CAAC,CAAC,cAAc,CAAC,qCAAa,CAAC,CAAC;YACpD,MAAM,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;YACxE,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,eAAe,CAAC,CAAC;YAC3D,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,MAAM,CAAC,CAAC;YAC1D,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,wCAAgB,CAAC,OAAO,CAAC,CAAC;YACtE,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,oDAAqB,CAAC,QAAQ,CAAC,CAAC;YAC5E,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,QAAQ,GAAG,8BAAc,CAAC,MAAM,EAAE,CAAC;YACzC,MAAM,OAAO,GAA+B;gBAC1C,EAAE,EAAE,QAAQ;gBACZ,eAAe,EAAE,mBAAmB;gBACpC,YAAY,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;aACjC,CAAC;YAEF,MAAM,aAAa,GAAG,6CAAoB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE3D,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,OAAO,GAA+B;gBAC1C,eAAe,EAAE,mBAAmB;gBACpC,YAAY,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;aACjC,CAAC;YAEF,MAAM,aAAa,GAAG,6CAAoB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE3D,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAC5D,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;YACxE,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAC7D,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;YACpE,MAAM,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC;YAChF,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,OAAO,GAA+B;gBAC1C,eAAe,EAAE,mBAAmB;gBACpC,YAAY,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;gBAChC,IAAI,EAAE,2BAAS,CAAC,gBAAgB;gBAChC,QAAQ,EAAE,mCAAa,CAAC,IAAI;gBAC5B,KAAK,EAAE,cAAc;gBACrB,SAAS,EAAE,EAAE;gBACb,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;aAC7B,CAAC;YAEF,MAAM,aAAa,GAAG,6CAAoB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE3D,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,gBAAgB,CAAC,CAAC;YAC5D,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACjD,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACzC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,OAAO,GAA+B;gBAC1C,eAAe,EAAE,mBAAmB;gBACpC,YAAY,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;gBAChC,UAAU,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE;aAC9C,CAAC;YAEF,MAAM,aAAa,GAAG,6CAAoB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE3D,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC;gBACvC,IAAI,EAAE,IAAI;gBACV,MAAM,EAAE,IAAI;gBACZ,QAAQ,EAAE,KAAK;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,cAAc,GAAG,CAAC;oBACtB,MAAM,EAAE,yCAAgB,CAAC,aAAa;oBACtC,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;oBACnB,UAAU,EAAE,EAAE;oBACd,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,CAAC;oBACpB,EAAE,EAAE,WAAW;oBACf,IAAI,EAAE,WAAW;oBACjB,WAAW,EAAE,aAAa;oBAC1B,QAAQ,EAAE,GAAG;oBACb,QAAQ,EAAE,KAAK;oBACf,OAAO,EAAE,CAAC,yCAAgB,CAAC,aAAa,CAAC;iBAC1C,CAAC,CAAC;YAEH,MAAM,OAAO,GAA+B;gBAC1C,eAAe,EAAE,mBAAmB;gBACpC,YAAY,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;gBAChC,cAAc;gBACd,YAAY;gBACZ,gBAAgB,EAAE,wCAAgB,CAAC,SAAS;gBAC5C,sBAAsB,EAAE,EAAE;gBAC1B,gBAAgB,EAAE,EAAE;aACrB,CAAC;YAEF,MAAM,aAAa,GAAG,6CAAoB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE3D,MAAM,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAC7D,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YACzD,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,wCAAgB,CAAC,SAAS,CAAC,CAAC;YACxE,MAAM,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACtD,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,OAAO,GAA+B;gBAC1C,eAAe,EAAE,mBAAmB;gBACpC,YAAY,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;gBAChC,YAAY,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE;gBAClC,WAAW,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE;gBACrC,cAAc,EAAE,EAAE,MAAM,EAAE,gBAAgB,EAAE;gBAC5C,kBAAkB,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;gBACrC,gBAAgB,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE;aACrC,CAAC;YAEF,MAAM,aAAa,GAAG,6CAAoB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE3D,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;YACjE,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,CAAC;YACpE,MAAM,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,gBAAgB,EAAE,CAAC,CAAC;YAC3E,MAAM,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YACpE,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,MAAM,GAA8B;gBACxC,cAAc,EAAE,EAAE;gBAClB,cAAc,EAAE,CAAC,yCAAgB,CAAC,aAAa,EAAE,yCAAgB,CAAC,cAAc,CAAC;gBACjF,6BAA6B,EAAE,EAAE;aAClC,CAAC;YAEF,MAAM,aAAa,GAAG,6CAAoB,CAAC,oBAAoB,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;YAE7F,MAAM,CAAC,aAAa,CAAC,CAAC,cAAc,CAAC,qCAAa,CAAC,CAAC;YACpD,MAAM,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;YACxE,MAAM,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC,WAAW,EAAE,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,QAAQ,GAAG;gBACf,EAAE,EAAE,oBAAoB;gBACxB,IAAI,EAAE,oBAAoB;gBAC1B,WAAW,EAAE,yCAAyC;gBACtD,QAAQ,EAAE,GAAG;gBACb,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,CAAC,yCAAgB,CAAC,aAAa,CAAC;aAC1C,CAAC;YAEF,MAAM,MAAM,GAA8B;gBACxC,cAAc,EAAE,CAAC,QAAQ,CAAC;gBAC1B,cAAc,EAAE,CAAC,yCAAgB,CAAC,aAAa,CAAC;gBAChD,6BAA6B,EAAE,EAAE;aAClC,CAAC;YAEF,MAAM,aAAa,GAAG,6CAAoB,CAAC,oBAAoB,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;YAE7F,MAAM,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAChE,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,MAAM,GAA8B;gBACxC,8BAA8B,EAAE,IAAI;gBACpC,8BAA8B,EAAE,IAAI;gBACpC,6BAA6B,EAAE,EAAE,EAAE,0CAA0C;aAC9E,CAAC;YAEF,MAAM,aAAa,GAAG,6CAAoB,CAAC,oBAAoB,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;YAE7F,wDAAwD;YACxD,MAAM,CAAC,aAAa,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,kBAAkB,GAAiC;gBACvD,OAAO,EAAE,CAAC,yCAAgB,CAAC,uBAAuB,EAAE,yCAAgB,CAAC,KAAK,CAAC;gBAC3E,aAAa,EAAE,EAAE;gBACjB,WAAW,EAAE,EAAE;gBACf,iBAAiB,EAAE,IAAI;gBACvB,kBAAkB,EAAE,IAAI;aACzB,CAAC;YAEF,MAAM,aAAa,GAAG,6CAAoB,CAAC,4BAA4B,CACrE,mBAAmB,EACnB,kBAAkB,CACnB,CAAC;YAEF,MAAM,CAAC,aAAa,CAAC,CAAC,cAAc,CAAC,qCAAa,CAAC,CAAC;YACpD,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE,CAAC;YACrD,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,wCAAgB,CAAC,SAAS,CAAC,CAAC;YACxE,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;QACvE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,kBAAkB,GAAiC;gBACvD,OAAO,EAAE,CAAC,yCAAgB,CAAC,uBAAuB,CAAC;gBACnD,aAAa,EAAE,EAAE;gBACjB,WAAW,EAAE,EAAE;gBACf,iBAAiB,EAAE,IAAI;gBACvB,kBAAkB,EAAE,KAAK;aAC1B,CAAC;YAEF,MAAM,aAAa,GAAG,6CAAoB,CAAC,4BAA4B,CACrE,mBAAmB,EACnB,kBAAkB,CACnB,CAAC;YAEF,MAAM,WAAW,GAAG,aAAa,CAAC,YAAY,CAAC,mBAAmB,CAAC;YACnE,MAAM,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YAClC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;YACxC,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;YAC/C,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE,CAAC;YACnD,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,gBAAgB,GAAG;gBACvB,mBAAmB;gBACnB,EAAE,GAAG,mBAAmB,EAAE,EAAE,EAAE,8BAAc,CAAC,MAAM,EAAE,EAAqB;gBAC1E,EAAE,GAAG,mBAAmB,EAAE,EAAE,EAAE,8BAAc,CAAC,MAAM,EAAE,EAAqB;aAC3E,CAAC;YAEF,MAAM,KAAK,GAAG,CAAC;oBACb,EAAE,EAAE,YAAY;oBAChB,IAAI,EAAE,YAAY;oBAClB,WAAW,EAAE,6BAA6B;oBAC1C,QAAQ,EAAE,EAAE;oBACZ,QAAQ,EAAE,KAAK;oBACf,OAAO,EAAE,CAAC,yCAAgB,CAAC,aAAa,CAAC;iBAC1C,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,6CAAoB,CAAC,WAAW,CAAC;gBAC9C,gBAAgB;gBAChB,KAAK;gBACL,OAAO,EAAE,CAAC,yCAAgB,CAAC,aAAa,CAAC;aAC1C,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,sBAAsB,GAAG,IAAW,CAAC;YAC3C,MAAM,gBAAgB,GAAG,CAAC,mBAAmB,EAAE,sBAAsB,CAAC,CAAC;YAEvE,MAAM,MAAM,GAAG,6CAAoB,CAAC,WAAW,CAAC;gBAC9C,gBAAgB;gBAChB,KAAK,EAAE,EAAE;aACV,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACtE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,sBAAsB,GAAG,IAAW,CAAC;YAC3C,MAAM,gBAAgB,GAAG,CAAC,sBAAsB,EAAE,mBAAmB,CAAC,CAAC;YAEvE,MAAM,MAAM,GAAG,6CAAoB,CAAC,WAAW,CAAC;gBAC9C,gBAAgB;gBAChB,KAAK,EAAE,EAAE;gBACT,aAAa,EAAE,IAAI;aACpB,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,aAAa,GAAG,6CAAoB,CAAC,gBAAgB,EAAE,CAAC;YAE9D,MAAM,CAAC,aAAa,CAAC,CAAC,cAAc,CAAC,qCAAa,CAAC,CAAC;YACpD,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,wCAAgB,CAAC,SAAS,CAAC,CAAC;YACxE,MAAM,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACtD,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAChD,MAAM,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,SAAS,GAAG;gBAChB,eAAe,EAAE,mBAAmB;gBACpC,gBAAgB,EAAE,wCAAgB,CAAC,MAAM;gBACzC,sBAAsB,EAAE,EAAE;gBAC1B,gBAAgB,EAAE,EAAE;aACrB,CAAC;YAEF,MAAM,aAAa,GAAG,6CAAoB,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YAEvE,MAAM,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;YACxE,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,wCAAgB,CAAC,MAAM,CAAC,CAAC;YACrE,MAAM,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACtD,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,aAAa,GAAG,6CAAoB,CAAC,gBAAgB,EAAE,CAAC;YAE9D,MAAM,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAErD,MAAM,gBAAgB,GAAG,aAAa,CAAC,yBAAyB,CAAC,yCAAgB,CAAC,aAAa,CAAC,CAAC;YACjG,MAAM,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAEpD,MAAM,eAAe,GAAG,aAAa,CAAC,yBAAyB,CAAC,yCAAgB,CAAC,cAAc,CAAC,CAAC;YACjG,MAAM,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,aAAa,GAAG,6CAAoB,CAAC,gBAAgB,EAAE,CAAC;YAE9D,MAAM,YAAY,GAAG,aAAa,CAAC,YAAY,CAAC;YAChD,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;YACvD,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9C,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,OAAO,GAA+B;gBAC1C,eAAe,EAAE,mBAAmB;gBACpC,YAAY,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;gBAChC,sBAAsB,EAAE,GAAG,EAAE,gBAAgB;aAC9C,CAAC;YAEF,MAAM,CAAC,GAAG,EAAE,CAAC,6CAAoB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CACxD,oDAAoD,CACrD,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,OAAO,GAA+B;gBAC1C,eAAe,EAAE,mBAAmB;gBACpC,YAAY,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;gBAChC,gBAAgB,EAAE,CAAC,EAAE,EAAE,gBAAgB;aACxC,CAAC;YAEF,MAAM,CAAC,GAAG,EAAE,CAAC,6CAAoB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CACxD,qDAAqD,CACtD,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,OAAO,GAA+B;gBAC1C,eAAe,EAAE,IAAW;gBAC5B,YAAY,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;aACjC,CAAC;YAEF,MAAM,CAAC,GAAG,EAAE,CAAC,6CAAoB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;QAC/D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,aAAa,GAAG,6CAAoB,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,CAAC;YAErF,MAAM,CAAC,aAAa,CAAC,CAAC,cAAc,CAAC,qCAAa,CAAC,CAAC;YACpD,MAAM,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC,WAAW,EAAE,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,aAAa,GAA8B;gBAC/C,6BAA6B,EAAE,EAAE;gBACjC,8BAA8B,EAAE,KAAK;aACtC,CAAC;YAEF,MAAM,aAAa,GAAG,6CAAoB,CAAC,oBAAoB,CAAC,mBAAmB,EAAE,aAAa,CAAC,CAAC;YAEpG,MAAM,CAAC,aAAa,CAAC,CAAC,cAAc,CAAC,qCAAa,CAAC,CAAC;YACpD,kEAAkE;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,MAAM,GAA8B;gBACxC,cAAc,EAAE,CAAC,yCAAgB,CAAC,aAAa,CAAC,EAAE,6BAA6B;gBAC/E,cAAc,EAAE,CAAC;wBACf,EAAE,EAAE,UAAU;wBACd,IAAI,EAAE,kBAAkB;wBACxB,WAAW,EAAE,uBAAuB;wBACpC,QAAQ,EAAE,EAAE;wBACZ,QAAQ,EAAE,KAAK;wBACf,OAAO,EAAE,CAAC,yCAAgB,CAAC,cAAc,CAAC,EAAE,yBAAyB;qBACtE,CAAC;aACH,CAAC;YAEF,MAAM,aAAa,GAAG,6CAAoB,CAAC,oBAAoB,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;YAE7F,MAAM,CAAC,aAAa,CAAC,CAAC,cAAc,CAAC,qCAAa,CAAC,CAAC;YACpD,oEAAoE;QACtE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\__tests__\\enriched-event.factory.spec.ts"], "sourcesContent": ["import { EnrichedEventFactory, CreateEnrichedEventOptions, EnrichmentConfig, ThreatIntelEnrichmentOptions } from '../enriched-event.factory';\r\nimport { EnrichedEvent, EnrichmentStatus } from '../../entities/enriched-event.entity';\r\nimport { NormalizedEvent, NormalizationStatus } from '../../entities/normalized-event.entity';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel';\r\nimport { EventMetadata } from '../../value-objects/event-metadata/event-metadata.value-object';\r\nimport { EventTimestamp } from '../../value-objects/event-metadata/event-timestamp.value-object';\r\nimport { EventSource } from '../../value-objects/event-metadata/event-source.value-object';\r\nimport { EventType } from '../../enums/event-type.enum';\r\nimport { EventSeverity } from '../../enums/event-severity.enum';\r\nimport { EventStatus } from '../../enums/event-status.enum';\r\nimport { EventProcessingStatus } from '../../enums/event-processing-status.enum';\r\nimport { EventSourceType } from '../../enums/event-source-type.enum';\r\nimport { EnrichmentSource } from '../../enums/enrichment-source.enum';\r\n\r\ndescribe('EnrichedEventFactory', () => {\r\n  let mockNormalizedEvent: NormalizedEvent;\r\n  let mockMetadata: EventMetadata;\r\n\r\n  beforeEach(() => {\r\n    // Create mock metadata\r\n    const eventSource = EventSource.create(\r\n      EventSourceType.SIEM,\r\n      'test-siem-001'\r\n    );\r\n    const eventTimestamp = EventTimestamp.create();\r\n    mockMetadata = EventMetadata.create(eventTimestamp, eventSource);\r\n\r\n    // Create mock normalized event\r\n    mockNormalizedEvent = {\r\n      id: UniqueEntityId.create(),\r\n      metadata: mockMetadata,\r\n      type: EventType.THREAT_DETECTED,\r\n      severity: EventSeverity.MEDIUM,\r\n      status: EventStatus.ACTIVE,\r\n      processingStatus: EventProcessingStatus.NORMALIZED,\r\n      normalizationStatus: NormalizationStatus.COMPLETED,\r\n      normalizedData: { event_type: 'threat', normalized: true },\r\n      title: 'Test Normalized Event',\r\n      description: 'A test normalized event',\r\n      tags: ['test', 'normalized'],\r\n      riskScore: 60,\r\n      confidenceLevel: 75,\r\n      attributes: { test: true },\r\n      correlationId: 'test-correlation-001',\r\n      parentEventId: undefined,\r\n    } as NormalizedEvent;\r\n  });\r\n\r\n  describe('create', () => {\r\n    it('should create enriched event with basic options', () => {\r\n      const options: CreateEnrichedEventOptions = {\r\n        normalizedEvent: mockNormalizedEvent,\r\n        enrichedData: { enriched: true, threat_score: 45 },\r\n      };\r\n\r\n      const enrichedEvent = EnrichedEventFactory.create(options);\r\n\r\n      expect(enrichedEvent).toBeInstanceOf(EnrichedEvent);\r\n      expect(enrichedEvent.normalizedEventId).toEqual(mockNormalizedEvent.id);\r\n      expect(enrichedEvent.type).toBe(EventType.THREAT_DETECTED);\r\n      expect(enrichedEvent.severity).toBe(EventSeverity.MEDIUM);\r\n      expect(enrichedEvent.enrichmentStatus).toBe(EnrichmentStatus.PENDING);\r\n      expect(enrichedEvent.processingStatus).toBe(EventProcessingStatus.ENRICHED);\r\n      expect(enrichedEvent.enrichedData.enriched).toBe(true);\r\n      expect(enrichedEvent.enrichedData.threat_score).toBe(45);\r\n    });\r\n\r\n    it('should create enriched event with custom ID', () => {\r\n      const customId = UniqueEntityId.create();\r\n      const options: CreateEnrichedEventOptions = {\r\n        id: customId,\r\n        normalizedEvent: mockNormalizedEvent,\r\n        enrichedData: { enriched: true },\r\n      };\r\n\r\n      const enrichedEvent = EnrichedEventFactory.create(options);\r\n\r\n      expect(enrichedEvent.id).toEqual(customId);\r\n    });\r\n\r\n    it('should inherit properties from normalized event', () => {\r\n      const options: CreateEnrichedEventOptions = {\r\n        normalizedEvent: mockNormalizedEvent,\r\n        enrichedData: { enriched: true },\r\n      };\r\n\r\n      const enrichedEvent = EnrichedEventFactory.create(options);\r\n\r\n      expect(enrichedEvent.title).toBe(mockNormalizedEvent.title);\r\n      expect(enrichedEvent.description).toBe(mockNormalizedEvent.description);\r\n      expect(enrichedEvent.tags).toEqual(mockNormalizedEvent.tags);\r\n      expect(enrichedEvent.riskScore).toBe(mockNormalizedEvent.riskScore);\r\n      expect(enrichedEvent.confidenceLevel).toBe(mockNormalizedEvent.confidenceLevel);\r\n      expect(enrichedEvent.correlationId).toBe(mockNormalizedEvent.correlationId);\r\n    });\r\n\r\n    it('should override properties when provided', () => {\r\n      const options: CreateEnrichedEventOptions = {\r\n        normalizedEvent: mockNormalizedEvent,\r\n        enrichedData: { enriched: true },\r\n        type: EventType.MALWARE_DETECTED,\r\n        severity: EventSeverity.HIGH,\r\n        title: 'Custom Title',\r\n        riskScore: 85,\r\n        tags: ['custom', 'enriched'],\r\n      };\r\n\r\n      const enrichedEvent = EnrichedEventFactory.create(options);\r\n\r\n      expect(enrichedEvent.type).toBe(EventType.MALWARE_DETECTED);\r\n      expect(enrichedEvent.severity).toBe(EventSeverity.HIGH);\r\n      expect(enrichedEvent.title).toBe('Custom Title');\r\n      expect(enrichedEvent.riskScore).toBe(85);\r\n      expect(enrichedEvent.tags).toEqual(['custom', 'enriched']);\r\n    });\r\n\r\n    it('should merge attributes from normalized event and options', () => {\r\n      const options: CreateEnrichedEventOptions = {\r\n        normalizedEvent: mockNormalizedEvent,\r\n        enrichedData: { enriched: true },\r\n        attributes: { custom: true, override: 'new' },\r\n      };\r\n\r\n      const enrichedEvent = EnrichedEventFactory.create(options);\r\n\r\n      expect(enrichedEvent.attributes).toEqual({\r\n        test: true,\r\n        custom: true,\r\n        override: 'new',\r\n      });\r\n    });\r\n\r\n    it('should set enrichment data and rules', () => {\r\n      const enrichmentData = [{\r\n        source: EnrichmentSource.IP_REPUTATION,\r\n        type: 'reputation',\r\n        data: { score: 75 },\r\n        confidence: 85,\r\n        timestamp: new Date(),\r\n      }];\r\n\r\n      const appliedRules = [{\r\n        id: 'test-rule',\r\n        name: 'Test Rule',\r\n        description: 'A test rule',\r\n        priority: 100,\r\n        required: false,\r\n        sources: [EnrichmentSource.IP_REPUTATION],\r\n      }];\r\n\r\n      const options: CreateEnrichedEventOptions = {\r\n        normalizedEvent: mockNormalizedEvent,\r\n        enrichedData: { enriched: true },\r\n        enrichmentData,\r\n        appliedRules,\r\n        enrichmentStatus: EnrichmentStatus.COMPLETED,\r\n        enrichmentQualityScore: 90,\r\n        threatIntelScore: 55,\r\n      };\r\n\r\n      const enrichedEvent = EnrichedEventFactory.create(options);\r\n\r\n      expect(enrichedEvent.enrichmentData).toEqual(enrichmentData);\r\n      expect(enrichedEvent.appliedRules).toEqual(appliedRules);\r\n      expect(enrichedEvent.enrichmentStatus).toBe(EnrichmentStatus.COMPLETED);\r\n      expect(enrichedEvent.enrichmentQualityScore).toBe(90);\r\n      expect(enrichedEvent.threatIntelScore).toBe(55);\r\n    });\r\n\r\n    it('should set context information', () => {\r\n      const options: CreateEnrichedEventOptions = {\r\n        normalizedEvent: mockNormalizedEvent,\r\n        enrichedData: { enriched: true },\r\n        assetContext: { owner: 'IT Team' },\r\n        userContext: { username: 'john.doe' },\r\n        networkContext: { subnet: '***********/24' },\r\n        geolocationContext: { country: 'US' },\r\n        reputationScores: { virustotal: 85 },\r\n      };\r\n\r\n      const enrichedEvent = EnrichedEventFactory.create(options);\r\n\r\n      expect(enrichedEvent.assetContext).toEqual({ owner: 'IT Team' });\r\n      expect(enrichedEvent.userContext).toEqual({ username: 'john.doe' });\r\n      expect(enrichedEvent.networkContext).toEqual({ subnet: '***********/24' });\r\n      expect(enrichedEvent.geolocationContext).toEqual({ country: 'US' });\r\n      expect(enrichedEvent.reputationScores).toEqual({ virustotal: 85 });\r\n    });\r\n  });\r\n\r\n  describe('createWithEnrichment', () => {\r\n    it('should create enriched event with automatic enrichment', () => {\r\n      const config: Partial<EnrichmentConfig> = {\r\n        availableRules: [],\r\n        enabledSources: [EnrichmentSource.IP_REPUTATION, EnrichmentSource.IP_GEOLOCATION],\r\n        minEnrichmentQualityThreshold: 70,\r\n      };\r\n\r\n      const enrichedEvent = EnrichedEventFactory.createWithEnrichment(mockNormalizedEvent, config);\r\n\r\n      expect(enrichedEvent).toBeInstanceOf(EnrichedEvent);\r\n      expect(enrichedEvent.normalizedEventId).toEqual(mockNormalizedEvent.id);\r\n      expect(enrichedEvent.enrichmentQualityScore).toBeDefined();\r\n    });\r\n\r\n    it('should apply enrichment rules and calculate quality score', () => {\r\n      const mockRule = {\r\n        id: 'ip-reputation-rule',\r\n        name: 'IP Reputation Rule',\r\n        description: 'Enriches events with IP reputation data',\r\n        priority: 100,\r\n        required: true,\r\n        sources: [EnrichmentSource.IP_REPUTATION],\r\n      };\r\n\r\n      const config: Partial<EnrichmentConfig> = {\r\n        availableRules: [mockRule],\r\n        enabledSources: [EnrichmentSource.IP_REPUTATION],\r\n        minEnrichmentQualityThreshold: 70,\r\n      };\r\n\r\n      const enrichedEvent = EnrichedEventFactory.createWithEnrichment(mockNormalizedEvent, config);\r\n\r\n      expect(enrichedEvent.enrichmentQualityScore).toBeGreaterThan(0);\r\n      expect(enrichedEvent.enrichmentStatus).toBeDefined();\r\n    });\r\n\r\n    it('should determine manual review requirement', () => {\r\n      const config: Partial<EnrichmentConfig> = {\r\n        requireManualReviewForHighRisk: true,\r\n        requireManualReviewForCritical: true,\r\n        minEnrichmentQualityThreshold: 90, // High threshold to trigger manual review\r\n      };\r\n\r\n      const enrichedEvent = EnrichedEventFactory.createWithEnrichment(mockNormalizedEvent, config);\r\n\r\n      // Should require manual review due to low quality score\r\n      expect(enrichedEvent.requiresManualReview).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('createWithThreatIntelligence', () => {\r\n    it('should create enriched event with threat intelligence focus', () => {\r\n      const threatIntelOptions: ThreatIntelEnrichmentOptions = {\r\n        sources: [EnrichmentSource.COMMERCIAL_THREAT_INTEL, EnrichmentSource.OSINT],\r\n        minConfidence: 70,\r\n        maxAgeHours: 24,\r\n        includeReputation: true,\r\n        includeGeolocation: true,\r\n      };\r\n\r\n      const enrichedEvent = EnrichedEventFactory.createWithThreatIntelligence(\r\n        mockNormalizedEvent,\r\n        threatIntelOptions\r\n      );\r\n\r\n      expect(enrichedEvent).toBeInstanceOf(EnrichedEvent);\r\n      expect(enrichedEvent.threatIntelScore).toBeDefined();\r\n      expect(enrichedEvent.enrichmentStatus).toBe(EnrichmentStatus.COMPLETED);\r\n      expect(enrichedEvent.enrichedData.threat_intelligence).toBeDefined();\r\n    });\r\n\r\n    it('should include threat intelligence context in enriched data', () => {\r\n      const threatIntelOptions: ThreatIntelEnrichmentOptions = {\r\n        sources: [EnrichmentSource.COMMERCIAL_THREAT_INTEL],\r\n        minConfidence: 50,\r\n        maxAgeHours: 48,\r\n        includeReputation: true,\r\n        includeGeolocation: false,\r\n      };\r\n\r\n      const enrichedEvent = EnrichedEventFactory.createWithThreatIntelligence(\r\n        mockNormalizedEvent,\r\n        threatIntelOptions\r\n      );\r\n\r\n      const threatIntel = enrichedEvent.enrichedData.threat_intelligence;\r\n      expect(threatIntel).toBeDefined();\r\n      expect(threatIntel.score).toBeDefined();\r\n      expect(threatIntel.sources_used).toBeDefined();\r\n      expect(threatIntel.indicators_found).toBeDefined();\r\n      expect(threatIntel.confidence).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('createBatch', () => {\r\n    it('should create multiple enriched events successfully', () => {\r\n      const normalizedEvents = [\r\n        mockNormalizedEvent,\r\n        { ...mockNormalizedEvent, id: UniqueEntityId.create() } as NormalizedEvent,\r\n        { ...mockNormalizedEvent, id: UniqueEntityId.create() } as NormalizedEvent,\r\n      ];\r\n\r\n      const rules = [{\r\n        id: 'batch-rule',\r\n        name: 'Batch Rule',\r\n        description: 'A rule for batch processing',\r\n        priority: 50,\r\n        required: false,\r\n        sources: [EnrichmentSource.IP_REPUTATION],\r\n      }];\r\n\r\n      const result = EnrichedEventFactory.createBatch({\r\n        normalizedEvents,\r\n        rules,\r\n        sources: [EnrichmentSource.IP_REPUTATION],\r\n      });\r\n\r\n      expect(result.successful).toHaveLength(3);\r\n      expect(result.failed).toHaveLength(0);\r\n      expect(result.summary.total).toBe(3);\r\n      expect(result.summary.successful).toBe(3);\r\n      expect(result.summary.failed).toBe(0);\r\n      expect(result.summary.processingTimeMs).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should handle failures in batch processing', () => {\r\n      const invalidNormalizedEvent = null as any;\r\n      const normalizedEvents = [mockNormalizedEvent, invalidNormalizedEvent];\r\n\r\n      const result = EnrichedEventFactory.createBatch({\r\n        normalizedEvents,\r\n        rules: [],\r\n      });\r\n\r\n      expect(result.successful).toHaveLength(1);\r\n      expect(result.failed).toHaveLength(1);\r\n      expect(result.failed[0].normalizedEvent).toBe(invalidNormalizedEvent);\r\n      expect(result.failed[0].error).toBeDefined();\r\n    });\r\n\r\n    it('should stop on first failure when configured', () => {\r\n      const invalidNormalizedEvent = null as any;\r\n      const normalizedEvents = [invalidNormalizedEvent, mockNormalizedEvent];\r\n\r\n      const result = EnrichedEventFactory.createBatch({\r\n        normalizedEvents,\r\n        rules: [],\r\n        stopOnFailure: true,\r\n      });\r\n\r\n      expect(result.successful).toHaveLength(0);\r\n      expect(result.failed).toHaveLength(1);\r\n      expect(result.summary.total).toBe(2);\r\n      expect(result.summary.successful).toBe(0);\r\n      expect(result.summary.failed).toBe(1);\r\n    });\r\n  });\r\n\r\n  describe('createForTesting', () => {\r\n    it('should create enriched event for testing with defaults', () => {\r\n      const enrichedEvent = EnrichedEventFactory.createForTesting();\r\n\r\n      expect(enrichedEvent).toBeInstanceOf(EnrichedEvent);\r\n      expect(enrichedEvent.enrichmentStatus).toBe(EnrichmentStatus.COMPLETED);\r\n      expect(enrichedEvent.enrichmentQualityScore).toBe(85);\r\n      expect(enrichedEvent.threatIntelScore).toBe(45);\r\n      expect(enrichedEvent.enrichmentData).toHaveLength(2);\r\n    });\r\n\r\n    it('should create enriched event for testing with overrides', () => {\r\n      const overrides = {\r\n        normalizedEvent: mockNormalizedEvent,\r\n        enrichmentStatus: EnrichmentStatus.FAILED,\r\n        enrichmentQualityScore: 30,\r\n        threatIntelScore: 90,\r\n      };\r\n\r\n      const enrichedEvent = EnrichedEventFactory.createForTesting(overrides);\r\n\r\n      expect(enrichedEvent.normalizedEventId).toEqual(mockNormalizedEvent.id);\r\n      expect(enrichedEvent.enrichmentStatus).toBe(EnrichmentStatus.FAILED);\r\n      expect(enrichedEvent.enrichmentQualityScore).toBe(30);\r\n      expect(enrichedEvent.threatIntelScore).toBe(90);\r\n    });\r\n\r\n    it('should include mock enrichment data for testing', () => {\r\n      const enrichedEvent = EnrichedEventFactory.createForTesting();\r\n\r\n      expect(enrichedEvent.enrichmentData).toHaveLength(2);\r\n      \r\n      const ipReputationData = enrichedEvent.getEnrichmentDataBySource(EnrichmentSource.IP_REPUTATION);\r\n      expect(ipReputationData).toHaveLength(1);\r\n      expect(ipReputationData[0].type).toBe('reputation');\r\n      \r\n      const geolocationData = enrichedEvent.getEnrichmentDataBySource(EnrichmentSource.IP_GEOLOCATION);\r\n      expect(geolocationData).toHaveLength(1);\r\n      expect(geolocationData[0].type).toBe('geolocation');\r\n    });\r\n\r\n    it('should include mock context data for testing', () => {\r\n      const enrichedEvent = EnrichedEventFactory.createForTesting();\r\n\r\n      const enrichedData = enrichedEvent.enrichedData;\r\n      expect(enrichedData.enriched).toBe(true);\r\n      expect(enrichedData.threat_intelligence).toBeDefined();\r\n      expect(enrichedData.reputation).toBeDefined();\r\n      expect(enrichedData.geolocation).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('error handling', () => {\r\n    it('should throw error for invalid enriched event properties', () => {\r\n      const options: CreateEnrichedEventOptions = {\r\n        normalizedEvent: mockNormalizedEvent,\r\n        enrichedData: { enriched: true },\r\n        enrichmentQualityScore: 150, // Invalid score\r\n      };\r\n\r\n      expect(() => EnrichedEventFactory.create(options)).toThrow(\r\n        'Enrichment quality score must be between 0 and 100'\r\n      );\r\n    });\r\n\r\n    it('should throw error for invalid threat intelligence score', () => {\r\n      const options: CreateEnrichedEventOptions = {\r\n        normalizedEvent: mockNormalizedEvent,\r\n        enrichedData: { enriched: true },\r\n        threatIntelScore: -10, // Invalid score\r\n      };\r\n\r\n      expect(() => EnrichedEventFactory.create(options)).toThrow(\r\n        'Threat intelligence score must be between 0 and 100'\r\n      );\r\n    });\r\n\r\n    it('should handle missing normalized event gracefully', () => {\r\n      const options: CreateEnrichedEventOptions = {\r\n        normalizedEvent: null as any,\r\n        enrichedData: { enriched: true },\r\n      };\r\n\r\n      expect(() => EnrichedEventFactory.create(options)).toThrow();\r\n    });\r\n  });\r\n\r\n  describe('configuration handling', () => {\r\n    it('should use default configuration when none provided', () => {\r\n      const enrichedEvent = EnrichedEventFactory.createWithEnrichment(mockNormalizedEvent);\r\n\r\n      expect(enrichedEvent).toBeInstanceOf(EnrichedEvent);\r\n      expect(enrichedEvent.enrichmentQualityScore).toBeDefined();\r\n    });\r\n\r\n    it('should merge provided configuration with defaults', () => {\r\n      const partialConfig: Partial<EnrichmentConfig> = {\r\n        minEnrichmentQualityThreshold: 80,\r\n        requireManualReviewForHighRisk: false,\r\n      };\r\n\r\n      const enrichedEvent = EnrichedEventFactory.createWithEnrichment(mockNormalizedEvent, partialConfig);\r\n\r\n      expect(enrichedEvent).toBeInstanceOf(EnrichedEvent);\r\n      // Should use the custom threshold for manual review determination\r\n    });\r\n\r\n    it('should respect enabled sources configuration', () => {\r\n      const config: Partial<EnrichmentConfig> = {\r\n        enabledSources: [EnrichmentSource.IP_REPUTATION], // Only IP reputation enabled\r\n        availableRules: [{\r\n          id: 'geo-rule',\r\n          name: 'Geolocation Rule',\r\n          description: 'Adds geolocation data',\r\n          priority: 50,\r\n          required: false,\r\n          sources: [EnrichmentSource.IP_GEOLOCATION], // This should be skipped\r\n        }],\r\n      };\r\n\r\n      const enrichedEvent = EnrichedEventFactory.createWithEnrichment(mockNormalizedEvent, config);\r\n\r\n      expect(enrichedEvent).toBeInstanceOf(EnrichedEvent);\r\n      // The geolocation rule should not be applied due to disabled source\r\n    });\r\n  });\r\n});"], "version": 3}