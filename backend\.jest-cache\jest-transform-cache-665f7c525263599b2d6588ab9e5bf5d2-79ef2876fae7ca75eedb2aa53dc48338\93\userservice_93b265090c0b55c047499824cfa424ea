84fa3d8c132e630cea559dbead82ed2e
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var UserService_1;
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_entity_1 = require("../../domain/entities/user.entity");
const role_entity_1 = require("../../domain/entities/role.entity");
const logger_service_1 = require("../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../infrastructure/logging/audit/audit.service");
const password_service_1 = require("../../../../infrastructure/auth/services/password.service");
/**
 * User service that handles user management operations
 * Provides CRUD operations and business logic for users
 */
let UserService = UserService_1 = class UserService {
    constructor(userRepository, roleRepository, loggerService, auditService, passwordService) {
        this.userRepository = userRepository;
        this.roleRepository = roleRepository;
        this.loggerService = loggerService;
        this.auditService = auditService;
        this.passwordService = passwordService;
        this.logger = new common_1.Logger(UserService_1.name);
    }
    /**
     * Create a new user
     * @param userData User data
     * @param createdBy User ID of the creator
     * @returns Created user
     */
    async create(userData, createdBy) {
        try {
            this.logger.debug('Creating new user', {
                email: userData.email,
                createdBy,
            });
            // Validate required fields
            if (!userData.email || !userData.firstName || !userData.lastName) {
                throw new common_1.BadRequestException('Email, first name, and last name are required');
            }
            // Check if user already exists
            const existingUser = await this.userRepository.findOne({
                where: { email: userData.email.toLowerCase() },
            });
            if (existingUser) {
                throw new common_1.ConflictException('User with this email already exists');
            }
            // Hash password if provided
            let passwordHash = '';
            if (userData.passwordHash) {
                passwordHash = await this.passwordService.hashPassword(userData.passwordHash);
            }
            // Get default role if no roles specified
            let roles = userData.roles || [];
            if (roles.length === 0) {
                const defaultRole = await this.getDefaultRole();
                if (defaultRole) {
                    roles = [defaultRole];
                }
            }
            // Create user
            const user = this.userRepository.create({
                ...userData,
                email: userData.email.toLowerCase(),
                passwordHash,
                roles,
                status: userData.status || 'pending_verification',
                emailVerified: userData.emailVerified || false,
            });
            const savedUser = await this.userRepository.save(user);
            // Log audit event
            await this.auditService.logUserAction(createdBy, 'create', 'user', savedUser.id, {
                email: savedUser.email,
                firstName: savedUser.firstName,
                lastName: savedUser.lastName,
                roles: savedUser.roleNames,
            });
            this.logger.log('User created successfully', {
                userId: savedUser.id,
                email: savedUser.email,
                createdBy,
            });
            return savedUser;
        }
        catch (error) {
            this.logger.error('Failed to create user', {
                error: error.message,
                email: userData.email,
                createdBy,
            });
            throw error;
        }
    }
    /**
     * Find user by ID
     * @param id User ID
     * @returns User or null
     */
    async findById(id) {
        try {
            const user = await this.userRepository.findOne({
                where: { id },
                relations: ['roles'],
            });
            if (!user) {
                this.logger.warn('User not found', { id });
                return null;
            }
            return user;
        }
        catch (error) {
            this.logger.error('Failed to find user by ID', {
                id,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Find user by email
     * @param email User email
     * @returns User or null
     */
    async findByEmail(email) {
        try {
            return await this.userRepository.findOne({
                where: { email: email.toLowerCase() },
                relations: ['roles'],
            });
        }
        catch (error) {
            this.logger.error('Failed to find user by email', {
                email,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Find users with pagination and filtering
     * @param options Query options
     * @returns Paginated users
     */
    async findMany(options) {
        try {
            const { page = 1, limit = 20, status, roles, search, sortBy = 'createdAt', sortOrder = 'DESC', } = options;
            const queryBuilder = this.userRepository.createQueryBuilder('user')
                .leftJoinAndSelect('user.roles', 'role');
            // Apply filters
            if (status && status.length > 0) {
                queryBuilder.andWhere('user.status IN (:...status)', { status });
            }
            if (roles && roles.length > 0) {
                queryBuilder.andWhere('role.name IN (:...roles)', { roles });
            }
            if (search) {
                queryBuilder.andWhere('(user.firstName ILIKE :search OR user.lastName ILIKE :search OR user.email ILIKE :search)', { search: `%${search}%` });
            }
            // Apply sorting
            queryBuilder.orderBy(`user.${sortBy}`, sortOrder);
            // Apply pagination
            const offset = (page - 1) * limit;
            queryBuilder.skip(offset).take(limit);
            const [users, total] = await queryBuilder.getManyAndCount();
            const totalPages = Math.ceil(total / limit);
            this.logger.debug('Users retrieved', {
                total,
                page,
                limit,
                totalPages,
                filters: { status, roles, search },
            });
            return {
                users,
                total,
                page,
                totalPages,
            };
        }
        catch (error) {
            this.logger.error('Failed to find users', {
                error: error.message,
                options,
            });
            throw error;
        }
    }
    /**
     * Update user
     * @param id User ID
     * @param updateData Update data
     * @param updatedBy User performing the update
     * @returns Updated user
     */
    async update(id, updateData, updatedBy) {
        try {
            const user = await this.findById(id);
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            this.logger.debug('Updating user', {
                userId: id,
                updateData: this.sanitizeUserData(updateData),
                updatedBy,
            });
            // Store original data for audit
            const originalData = {
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                status: user.status,
                roles: user.roleNames,
            };
            // Check for email conflicts if email is being updated
            if (updateData.email && updateData.email !== user.email) {
                const existingUser = await this.findByEmail(updateData.email);
                if (existingUser && existingUser.id !== id) {
                    throw new common_1.ConflictException('User with this email already exists');
                }
                updateData.email = updateData.email.toLowerCase();
            }
            // Hash password if being updated
            if (updateData.passwordHash) {
                updateData.passwordHash = await this.passwordService.hashPassword(updateData.passwordHash);
                updateData.passwordChangedAt = new Date();
            }
            // Update fields
            Object.assign(user, updateData);
            const updatedUser = await this.userRepository.save(user);
            // Log audit event
            await this.auditService.logUserAction(updatedBy, 'update', 'user', id, {
                originalData,
                updateData: this.sanitizeUserData(updateData),
            });
            this.logger.log('User updated successfully', {
                userId: id,
                updatedBy,
            });
            return updatedUser;
        }
        catch (error) {
            this.logger.error('Failed to update user', {
                userId: id,
                error: error.message,
                updatedBy,
            });
            throw error;
        }
    }
    /**
     * Delete user
     * @param id User ID
     * @param deletedBy User performing the deletion
     */
    async delete(id, deletedBy) {
        try {
            const user = await this.findById(id);
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            this.logger.debug('Deleting user', {
                userId: id,
                deletedBy,
            });
            await this.userRepository.remove(user);
            // Log audit event
            await this.auditService.logUserAction(deletedBy, 'delete', 'user', id, {
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
            });
            this.logger.log('User deleted successfully', {
                userId: id,
                deletedBy,
            });
        }
        catch (error) {
            this.logger.error('Failed to delete user', {
                userId: id,
                error: error.message,
                deletedBy,
            });
            throw error;
        }
    }
    /**
     * Assign roles to user
     * @param userId User ID
     * @param roleIds Array of role IDs
     * @param assignedBy User performing the assignment
     * @returns Updated user
     */
    async assignRoles(userId, roleIds, assignedBy) {
        try {
            const user = await this.findById(userId);
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            const roles = await this.roleRepository.findByIds(roleIds);
            if (roles.length !== roleIds.length) {
                throw new common_1.BadRequestException('One or more roles not found');
            }
            const originalRoles = user.roleNames;
            user.roles = roles;
            const updatedUser = await this.userRepository.save(user);
            // Log audit event
            await this.auditService.logUserAction(assignedBy, 'assign_roles', 'user', userId, {
                originalRoles,
                newRoles: updatedUser.roleNames,
            });
            this.logger.log('Roles assigned to user', {
                userId,
                roles: updatedUser.roleNames,
                assignedBy,
            });
            return updatedUser;
        }
        catch (error) {
            this.logger.error('Failed to assign roles to user', {
                userId,
                roleIds,
                error: error.message,
                assignedBy,
            });
            throw error;
        }
    }
    /**
     * Get user statistics
     * @returns User statistics
     */
    async getStatistics() {
        try {
            const [total, active, inactive, suspended, pendingVerification, emailVerified, emailUnverified,] = await Promise.all([
                this.userRepository.count(),
                this.userRepository.count({ where: { status: 'active' } }),
                this.userRepository.count({ where: { status: 'inactive' } }),
                this.userRepository.count({ where: { status: 'suspended' } }),
                this.userRepository.count({ where: { status: 'pending_verification' } }),
                this.userRepository.count({ where: { emailVerified: true } }),
                this.userRepository.count({ where: { emailVerified: false } }),
            ]);
            return {
                total,
                byStatus: {
                    active,
                    inactive,
                    suspended,
                    pendingVerification,
                },
                byEmailVerification: {
                    verified: emailVerified,
                    unverified: emailUnverified,
                },
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            this.logger.error('Failed to get user statistics', {
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Get default role for new users
     * @returns Default role or null
     */
    async getDefaultRole() {
        try {
            return await this.roleRepository.findOne({
                where: { isDefault: true, isActive: true },
            });
        }
        catch (error) {
            this.logger.error('Error getting default role', {
                error: error.message,
            });
            return null;
        }
    }
    /**
     * Sanitize user data for logging
     * @param data User data
     * @returns Sanitized data
     */
    sanitizeUserData(data) {
        const sanitized = { ...data };
        // Remove sensitive fields
        delete sanitized.passwordHash;
        delete sanitized.twoFactorSecret;
        delete sanitized.passwordResetToken;
        delete sanitized.emailVerificationToken;
        return sanitized;
    }
};
exports.UserService = UserService;
exports.UserService = UserService = UserService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(role_entity_1.Role)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof logger_service_1.LoggerService !== "undefined" && logger_service_1.LoggerService) === "function" ? _c : Object, typeof (_d = typeof audit_service_1.AuditService !== "undefined" && audit_service_1.AuditService) === "function" ? _d : Object, typeof (_e = typeof password_service_1.PasswordService !== "undefined" && password_service_1.PasswordService) === "function" ? _e : Object])
], UserService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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