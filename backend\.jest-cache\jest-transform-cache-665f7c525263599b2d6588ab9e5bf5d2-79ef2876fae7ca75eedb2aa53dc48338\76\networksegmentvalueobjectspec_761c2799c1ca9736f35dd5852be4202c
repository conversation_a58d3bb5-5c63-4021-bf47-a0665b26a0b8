d2dc2d7249f1f195e21014545d8c99aa
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const network_segment_value_object_1 = require("./network-segment.value-object");
const ip_address_value_object_1 = require("./ip-address.value-object");
describe('NetworkSegment Value Object', () => {
    describe('Creation and Validation', () => {
        it('should create valid IPv4 network segment', () => {
            const segment = network_segment_value_object_1.NetworkSegment.fromCIDR('***********/24');
            expect(segment.networkAddress).toBe('***********');
            expect(segment.prefixLength).toBe(24);
            expect(segment.type).toBe(network_segment_value_object_1.NetworkSegmentType.IPv4);
            expect(segment.isIPv4()).toBe(true);
            expect(segment.isIPv6()).toBe(false);
        });
        it('should create valid IPv6 network segment', () => {
            const segment = network_segment_value_object_1.NetworkSegment.fromCIDR('2001:db8::/32');
            expect(segment.networkAddress).toBe('2001:db8::');
            expect(segment.prefixLength).toBe(32);
            expect(segment.type).toBe(network_segment_value_object_1.NetworkSegmentType.IPv6);
            expect(segment.isIPv6()).toBe(true);
            expect(segment.isIPv4()).toBe(false);
        });
        it('should create network segment with create method', () => {
            const segment = network_segment_value_object_1.NetworkSegment.create('10.0.0.0', 8);
            expect(segment.networkAddress).toBe('10.0.0.0');
            expect(segment.prefixLength).toBe(8);
        });
        it('should validate CIDR format', () => {
            expect(() => network_segment_value_object_1.NetworkSegment.fromCIDR('invalid')).toThrow('CIDR notation must be in format "address/prefix"');
            expect(() => network_segment_value_object_1.NetworkSegment.fromCIDR('***********')).toThrow('CIDR notation must be in format "address/prefix"');
            expect(() => network_segment_value_object_1.NetworkSegment.fromCIDR('***********/invalid')).toThrow('Invalid prefix length in CIDR notation');
        });
        it('should validate prefix length range for IPv4', () => {
            expect(() => network_segment_value_object_1.NetworkSegment.create('***********', -1)).toThrow('Prefix length must be between 0 and 32');
            expect(() => network_segment_value_object_1.NetworkSegment.create('***********', 33)).toThrow('Prefix length must be between 0 and 32');
        });
        it('should validate prefix length range for IPv6', () => {
            expect(() => network_segment_value_object_1.NetworkSegment.create('2001:db8::', -1)).toThrow('Prefix length must be between 0 and 128');
            expect(() => network_segment_value_object_1.NetworkSegment.create('2001:db8::', 129)).toThrow('Prefix length must be between 0 and 128');
        });
        it('should validate network address alignment', () => {
            // *********** is not a valid network address for /24
            expect(() => network_segment_value_object_1.NetworkSegment.fromCIDR('***********/24')).toThrow('Invalid network address. Expected ***********/24');
        });
        it('should accept valid network addresses', () => {
            const validNetworks = [
                '***********/24',
                '10.0.0.0/8',
                '**********/12',
                '0.0.0.0/0',
                '***********28/25',
            ];
            validNetworks.forEach(cidr => {
                expect(() => network_segment_value_object_1.NetworkSegment.fromCIDR(cidr)).not.toThrow();
            });
        });
    });
    describe('Predefined Network Segments', () => {
        it('should create private class A network', () => {
            const segment = network_segment_value_object_1.NetworkSegment.privateClassA();
            expect(segment.toCIDR()).toBe('10.0.0.0/8');
            expect(segment.isPrivate()).toBe(true);
        });
        it('should create private class B network', () => {
            const segment = network_segment_value_object_1.NetworkSegment.privateClassB();
            expect(segment.toCIDR()).toBe('**********/12');
            expect(segment.isPrivate()).toBe(true);
        });
        it('should create private class C network', () => {
            const segment = network_segment_value_object_1.NetworkSegment.privateClassC();
            expect(segment.toCIDR()).toBe('***********/16');
            expect(segment.isPrivate()).toBe(true);
        });
        it('should create localhost network', () => {
            const segment = network_segment_value_object_1.NetworkSegment.localhost();
            expect(segment.toCIDR()).toBe('*********/8');
            expect(segment.classify()).toBe(network_segment_value_object_1.NetworkSegmentClass.LOOPBACK);
        });
    });
    describe('Network Classification', () => {
        it('should classify private networks', () => {
            const privateNetworks = [
                '10.0.0.0/8',
                '**********/12',
                '***********/16',
            ];
            privateNetworks.forEach(cidr => {
                const segment = network_segment_value_object_1.NetworkSegment.fromCIDR(cidr);
                expect(segment.classify()).toBe(network_segment_value_object_1.NetworkSegmentClass.PRIVATE);
                expect(segment.isPrivate()).toBe(true);
                expect(segment.isPublic()).toBe(false);
            });
        });
        it('should classify public networks', () => {
            const publicNetworks = [
                '*******/24',
                '*******/24',
            ];
            publicNetworks.forEach(cidr => {
                const segment = network_segment_value_object_1.NetworkSegment.fromCIDR(cidr);
                expect(segment.classify()).toBe(network_segment_value_object_1.NetworkSegmentClass.PUBLIC);
                expect(segment.isPublic()).toBe(true);
                expect(segment.isPrivate()).toBe(false);
            });
        });
        it('should classify loopback networks', () => {
            const segment = network_segment_value_object_1.NetworkSegment.fromCIDR('*********/8');
            expect(segment.classify()).toBe(network_segment_value_object_1.NetworkSegmentClass.LOOPBACK);
        });
        it('should classify multicast networks', () => {
            const segment = network_segment_value_object_1.NetworkSegment.fromCIDR('*********/4');
            expect(segment.classify()).toBe(network_segment_value_object_1.NetworkSegmentClass.MULTICAST);
        });
    });
    describe('IP Address Membership', () => {
        it('should check if IP address belongs to network', () => {
            const segment = network_segment_value_object_1.NetworkSegment.fromCIDR('***********/24');
            expect(segment.contains('***********')).toBe(true);
            expect(segment.contains('***********00')).toBe(true);
            expect(segment.contains('*************')).toBe(true);
            expect(segment.contains('***********')).toBe(false);
            expect(segment.contains('********')).toBe(false);
        });
        it('should check IP address membership with IPAddress objects', () => {
            const segment = network_segment_value_object_1.NetworkSegment.fromCIDR('10.0.0.0/8');
            const ip1 = ip_address_value_object_1.IPAddress.fromString('********');
            const ip2 = ip_address_value_object_1.IPAddress.fromString('***********');
            expect(segment.contains(ip1)).toBe(true);
            expect(segment.contains(ip2)).toBe(false);
        });
        it('should handle type mismatches', () => {
            const ipv4Segment = network_segment_value_object_1.NetworkSegment.fromCIDR('***********/24');
            const ipv6Address = '2001:db8::1';
            expect(ipv4Segment.contains(ipv6Address)).toBe(false);
        });
    });
    describe('Network Calculations', () => {
        it('should calculate total addresses for IPv4', () => {
            const testCases = [
                { cidr: '***********/24', expected: BigInt(256) },
                { cidr: '10.0.0.0/8', expected: BigInt(16777216) },
                { cidr: '***********/30', expected: BigInt(4) },
                { cidr: '***********/32', expected: BigInt(1) },
            ];
            testCases.forEach(({ cidr, expected }) => {
                const segment = network_segment_value_object_1.NetworkSegment.fromCIDR(cidr);
                expect(segment.getTotalAddresses()).toBe(expected);
            });
        });
        it('should calculate usable addresses for IPv4', () => {
            const testCases = [
                { cidr: '***********/24', expected: BigInt(254) }, // 256 - 2 (network + broadcast)
                { cidr: '***********/30', expected: BigInt(2) }, // 4 - 2 (network + broadcast)
                { cidr: '***********/31', expected: BigInt(2) }, // Point-to-point, no broadcast
                { cidr: '***********/32', expected: BigInt(1) }, // Host route
            ];
            testCases.forEach(({ cidr, expected }) => {
                const segment = network_segment_value_object_1.NetworkSegment.fromCIDR(cidr);
                expect(segment.getUsableAddresses()).toBe(expected);
            });
        });
        it('should calculate subnet mask for IPv4', () => {
            const testCases = [
                { cidr: '***********/24', expected: '*************' },
                { cidr: '10.0.0.0/8', expected: '*********' },
                { cidr: '***********/30', expected: '***************' },
                { cidr: '0.0.0.0/0', expected: '0.0.0.0' },
            ];
            testCases.forEach(({ cidr, expected }) => {
                const segment = network_segment_value_object_1.NetworkSegment.fromCIDR(cidr);
                expect(segment.getSubnetMask()).toBe(expected);
            });
        });
        it('should calculate wildcard mask for IPv4', () => {
            const testCases = [
                { cidr: '***********/24', expected: '*********' },
                { cidr: '10.0.0.0/8', expected: '*************' },
                { cidr: '***********/30', expected: '*******' },
            ];
            testCases.forEach(({ cidr, expected }) => {
                const segment = network_segment_value_object_1.NetworkSegment.fromCIDR(cidr);
                expect(segment.getWildcardMask()).toBe(expected);
            });
        });
        it('should calculate broadcast address for IPv4', () => {
            const testCases = [
                { cidr: '***********/24', expected: '*************' },
                { cidr: '10.0.0.0/8', expected: '**************' },
                { cidr: '***********28/25', expected: '*************' },
            ];
            testCases.forEach(({ cidr, expected }) => {
                const segment = network_segment_value_object_1.NetworkSegment.fromCIDR(cidr);
                expect(segment.getBroadcastAddress().toString()).toBe(expected);
            });
        });
        it('should calculate first host address', () => {
            const testCases = [
                { cidr: '***********/24', expected: '***********' },
                { cidr: '10.0.0.0/8', expected: '********' },
                { cidr: '***********/31', expected: '***********' }, // Point-to-point
                { cidr: '***********/32', expected: '***********' }, // Host route
            ];
            testCases.forEach(({ cidr, expected }) => {
                const segment = network_segment_value_object_1.NetworkSegment.fromCIDR(cidr);
                expect(segment.getFirstHostAddress().toString()).toBe(expected);
            });
        });
        it('should calculate last host address', () => {
            const testCases = [
                { cidr: '***********/24', expected: '*************' },
                { cidr: '***********/30', expected: '***********' },
                { cidr: '***********/31', expected: '***********' }, // Point-to-point
                { cidr: '***********/32', expected: '***********' }, // Host route
            ];
            testCases.forEach(({ cidr, expected }) => {
                const segment = network_segment_value_object_1.NetworkSegment.fromCIDR(cidr);
                expect(segment.getLastHostAddress().toString()).toBe(expected);
            });
        });
        it('should throw error for IPv6 operations not implemented', () => {
            const segment = network_segment_value_object_1.NetworkSegment.fromCIDR('2001:db8::/32');
            expect(() => segment.getSubnetMask()).toThrow('Subnet mask is only applicable to IPv4 networks');
            expect(() => segment.getWildcardMask()).toThrow('Wildcard mask is only applicable to IPv4 networks');
            expect(() => segment.getBroadcastAddress()).toThrow('Broadcast address is only applicable to IPv4 networks');
        });
    });
    describe('Subnet Operations', () => {
        it('should split network into smaller subnets', () => {
            const segment = network_segment_value_object_1.NetworkSegment.fromCIDR('***********/24');
            const subnets = segment.split(26); // Split /24 into /26 subnets
            expect(subnets).toHaveLength(4);
            expect(subnets[0].toCIDR()).toBe('***********/26');
            expect(subnets[1].toCIDR()).toBe('************/26');
            expect(subnets[2].toCIDR()).toBe('***********28/26');
            expect(subnets[3].toCIDR()).toBe('***********92/26');
        });
        it('should validate subnet splitting parameters', () => {
            const segment = network_segment_value_object_1.NetworkSegment.fromCIDR('***********/24');
            expect(() => segment.split(24)).toThrow('New prefix length must be greater than current prefix length');
            expect(() => segment.split(23)).toThrow('New prefix length must be greater than current prefix length');
            expect(() => segment.split(33)).toThrow('New prefix length cannot exceed 32');
        });
        it('should check network overlap', () => {
            const segment1 = network_segment_value_object_1.NetworkSegment.fromCIDR('***********/24');
            const segment2 = network_segment_value_object_1.NetworkSegment.fromCIDR('***********28/25');
            const segment3 = network_segment_value_object_1.NetworkSegment.fromCIDR('***********/24');
            expect(segment1.overlaps(segment2)).toBe(true);
            expect(segment1.overlaps(segment3)).toBe(false);
        });
        it('should check subnet relationships', () => {
            const parent = network_segment_value_object_1.NetworkSegment.fromCIDR('***********/16');
            const child = network_segment_value_object_1.NetworkSegment.fromCIDR('***********/24');
            const unrelated = network_segment_value_object_1.NetworkSegment.fromCIDR('10.0.0.0/24');
            expect(child.isSubnetOf(parent)).toBe(true);
            expect(parent.isSupernetOf(child)).toBe(true);
            expect(child.isSubnetOf(unrelated)).toBe(false);
            expect(unrelated.isSupernetOf(child)).toBe(false);
        });
        it('should handle type mismatches in network operations', () => {
            const ipv4Segment = network_segment_value_object_1.NetworkSegment.fromCIDR('***********/24');
            const ipv6Segment = network_segment_value_object_1.NetworkSegment.fromCIDR('2001:db8::/32');
            expect(ipv4Segment.overlaps(ipv6Segment)).toBe(false);
            expect(ipv4Segment.isSubnetOf(ipv6Segment)).toBe(false);
        });
    });
    describe('Security Assessment', () => {
        it('should assess public network security risk', () => {
            const segment = network_segment_value_object_1.NetworkSegment.fromCIDR('*******/24');
            const risk = segment.getSecurityRisk();
            expect(risk.riskLevel).toBe('high');
            expect(risk.reasons).toContain('Public network segment exposed to internet');
            expect(risk.recommendations).toContain('Implement strict firewall rules');
        });
        it('should assess large network security risk', () => {
            const segment = network_segment_value_object_1.NetworkSegment.fromCIDR('10.0.0.0/8');
            const risk = segment.getSecurityRisk();
            expect(risk.reasons).toContain('Very large network segment');
            expect(risk.recommendations).toContain('Consider network segmentation');
        });
        it('should assess broad network range risk', () => {
            const segment = network_segment_value_object_1.NetworkSegment.fromCIDR('0.0.0.0/0');
            const risk = segment.getSecurityRisk();
            expect(risk.reasons).toContain('Very broad network range');
            expect(risk.recommendations).toContain('Implement network segmentation');
        });
        it('should provide standard assessment for normal networks', () => {
            const segment = network_segment_value_object_1.NetworkSegment.fromCIDR('***********/24');
            const risk = segment.getSecurityRisk();
            expect(risk.riskLevel).toBe('low');
            expect(risk.reasons).toContain('Standard network segment');
        });
    });
    describe('Network Information', () => {
        it('should provide comprehensive network information for IPv4', () => {
            const segment = network_segment_value_object_1.NetworkSegment.fromCIDR('***********/24');
            const info = segment.getNetworkInfo();
            expect(info.cidr).toBe('***********/24');
            expect(info.type).toBe(network_segment_value_object_1.NetworkSegmentType.IPv4);
            expect(info.networkAddress).toBe('***********');
            expect(info.prefixLength).toBe(24);
            expect(info.totalAddresses).toBe('256');
            expect(info.usableAddresses).toBe('254');
            expect(info.subnetMask).toBe('*************');
            expect(info.wildcardMask).toBe('*********');
            expect(info.broadcastAddress).toBe('*************');
            expect(info.firstHost).toBe('***********');
            expect(info.lastHost).toBe('*************');
            expect(info.isPrivate).toBe(true);
            expect(info.isPublic).toBe(false);
        });
        it('should provide limited information for IPv6', () => {
            const segment = network_segment_value_object_1.NetworkSegment.fromCIDR('2001:db8::/32');
            const info = segment.getNetworkInfo();
            expect(info.cidr).toBe('2001:db8::/32');
            expect(info.type).toBe(network_segment_value_object_1.NetworkSegmentType.IPv6);
            expect(info.subnetMask).toBeUndefined();
            expect(info.broadcastAddress).toBeUndefined();
        });
    });
    describe('String Operations', () => {
        it('should convert to CIDR string', () => {
            const segment = network_segment_value_object_1.NetworkSegment.fromCIDR('***********/24');
            expect(segment.toCIDR()).toBe('***********/24');
            expect(segment.toString()).toBe('***********/24');
        });
        it('should parse various input formats', () => {
            const cidrSegment = network_segment_value_object_1.NetworkSegment.parse('***********/24');
            const hostSegment = network_segment_value_object_1.NetworkSegment.parse('***********');
            expect(cidrSegment?.toCIDR()).toBe('***********/24');
            expect(hostSegment?.toCIDR()).toBe('***********/32');
        });
        it('should return null for invalid parse input', () => {
            expect(network_segment_value_object_1.NetworkSegment.parse('invalid')).toBeNull();
            expect(network_segment_value_object_1.NetworkSegment.parse('')).toBeNull();
        });
        it('should validate CIDR format', () => {
            expect(network_segment_value_object_1.NetworkSegment.isValidCIDR('***********/24')).toBe(true);
            expect(network_segment_value_object_1.NetworkSegment.isValidCIDR('2001:db8::/32')).toBe(true);
            expect(network_segment_value_object_1.NetworkSegment.isValidCIDR('invalid')).toBe(false);
            expect(network_segment_value_object_1.NetworkSegment.isValidCIDR('***********')).toBe(false);
        });
    });
    describe('Equality and Comparison', () => {
        it('should compare network segments for equality', () => {
            const segment1 = network_segment_value_object_1.NetworkSegment.fromCIDR('***********/24');
            const segment2 = network_segment_value_object_1.NetworkSegment.fromCIDR('***********/24');
            const segment3 = network_segment_value_object_1.NetworkSegment.fromCIDR('***********/24');
            const segment4 = network_segment_value_object_1.NetworkSegment.fromCIDR('***********/25');
            expect(segment1.equals(segment2)).toBe(true);
            expect(segment1.equals(segment3)).toBe(false);
            expect(segment1.equals(segment4)).toBe(false);
            expect(segment1.equals(undefined)).toBe(false);
        });
        it('should handle same instance comparison', () => {
            const segment = network_segment_value_object_1.NetworkSegment.fromCIDR('***********/24');
            expect(segment.equals(segment)).toBe(true);
        });
    });
    describe('JSON Serialization', () => {
        it('should serialize to JSON', () => {
            const segment = network_segment_value_object_1.NetworkSegment.fromCIDR('***********/24');
            const json = segment.toJSON();
            expect(json.cidr).toBe('***********/24');
            expect(json.networkAddress).toBe('***********');
            expect(json.prefixLength).toBe(24);
            expect(json.securityRisk).toBeDefined();
            expect(json.securityRisk.riskLevel).toBeDefined();
        });
        it('should deserialize from JSON', () => {
            const json = { networkAddress: '***********', prefixLength: 24 };
            const segment = network_segment_value_object_1.NetworkSegment.fromJSON(json);
            expect(segment.toCIDR()).toBe('***********/24');
        });
    });
    describe('Edge Cases', () => {
        it('should handle /0 network (default route)', () => {
            const segment = network_segment_value_object_1.NetworkSegment.fromCIDR('0.0.0.0/0');
            expect(segment.getTotalAddresses()).toBe(BigInt('4294967296')); // 2^32
            expect(segment.getSubnetMask()).toBe('0.0.0.0');
        });
        it('should handle /32 host route', () => {
            const segment = network_segment_value_object_1.NetworkSegment.fromCIDR('***********/32');
            expect(segment.getTotalAddresses()).toBe(BigInt(1));
            expect(segment.getUsableAddresses()).toBe(BigInt(1));
            expect(segment.getFirstHostAddress().toString()).toBe('***********');
            expect(segment.getLastHostAddress().toString()).toBe('***********');
        });
        it('should handle /31 point-to-point network', () => {
            const segment = network_segment_value_object_1.NetworkSegment.fromCIDR('***********/31');
            expect(segment.getTotalAddresses()).toBe(BigInt(2));
            expect(segment.getUsableAddresses()).toBe(BigInt(2));
            expect(segment.getFirstHostAddress().toString()).toBe('***********');
            expect(segment.getLastHostAddress().toString()).toBe('***********');
        });
        it('should handle empty network address validation', () => {
            expect(() => network_segment_value_object_1.NetworkSegment.create('', 24)).toThrow('Network address cannot be empty');
            expect(() => network_segment_value_object_1.NetworkSegment.create('   ', 24)).toThrow('Network address cannot be empty');
        });
        it('should handle non-integer prefix length', () => {
            expect(() => network_segment_value_object_1.NetworkSegment.create('***********', 24.5)).toThrow('Prefix length must be an integer');
        });
        it('should handle IPv6 operations not implemented', () => {
            const segment = network_segment_value_object_1.NetworkSegment.fromCIDR('2001:db8::/32');
            expect(() => segment.split(64)).toThrow('IPv6 subnet splitting not implemented');
            expect(() => segment.getLastHostAddress()).toThrow('IPv6 last host address calculation not implemented');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************