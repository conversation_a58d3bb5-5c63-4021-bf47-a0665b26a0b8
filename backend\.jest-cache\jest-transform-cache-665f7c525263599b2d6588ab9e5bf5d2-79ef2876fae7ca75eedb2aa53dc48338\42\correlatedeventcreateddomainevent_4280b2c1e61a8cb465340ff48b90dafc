305e9940c0aa423241915bab2b5b5055
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CorrelatedEventCreatedDomainEvent = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
const event_severity_enum_1 = require("../enums/event-severity.enum");
const correlation_status_enum_1 = require("../enums/correlation-status.enum");
const confidence_level_enum_1 = require("../enums/confidence-level.enum");
/**
 * Correlated Event Created Domain Event
 *
 * Raised when a new correlated security event is created in the system.
 * This event triggers various downstream processes including:
 * - Threat analysis workflows
 * - Attack chain analysis
 * - Response action planning
 * - Manual review queue management
 * - Metrics collection and monitoring
 * - Audit logging and compliance tracking
 * - Alert generation for high-confidence correlations
 */
class CorrelatedEventCreatedDomainEvent extends shared_kernel_1.BaseDomainEvent {
    constructor(aggregateId, eventData, options) {
        super(aggregateId, eventData, options);
    }
    /**
     * Get the enriched event ID
     */
    get enrichedEventId() {
        return this.eventData.enrichedEventId;
    }
    /**
     * Get the type of the correlated event
     */
    get eventType() {
        return this.eventData.eventType;
    }
    /**
     * Get the severity of the correlated event
     */
    get severity() {
        return this.eventData.severity;
    }
    /**
     * Get the correlation status
     */
    get correlationStatus() {
        return this.eventData.correlationStatus;
    }
    /**
     * Get the correlation quality score
     */
    get correlationQualityScore() {
        return this.eventData.correlationQualityScore;
    }
    /**
     * Get the number of applied rules
     */
    get appliedRulesCount() {
        return this.eventData.appliedRulesCount;
    }
    /**
     * Get the number of correlation matches
     */
    get correlationMatchesCount() {
        return this.eventData.correlationMatchesCount;
    }
    /**
     * Get the number of related events
     */
    get relatedEventsCount() {
        return this.eventData.relatedEventsCount;
    }
    /**
     * Get the confidence level
     */
    get confidenceLevel() {
        return this.eventData.confidenceLevel;
    }
    /**
     * Check if the event has an attack chain
     */
    get hasAttackChain() {
        return this.eventData.hasAttackChain;
    }
    /**
     * Check if the event requires manual review
     */
    get requiresManualReview() {
        return this.eventData.requiresManualReview;
    }
    /**
     * Check if the correlated event is high severity
     */
    isHighSeverity() {
        return [event_severity_enum_1.EventSeverity.HIGH, event_severity_enum_1.EventSeverity.CRITICAL].includes(this.severity);
    }
    /**
     * Check if the correlated event is critical
     */
    isCritical() {
        return this.severity === event_severity_enum_1.EventSeverity.CRITICAL;
    }
    /**
     * Check if the event has high correlation quality
     */
    hasHighCorrelationQuality() {
        return (this.correlationQualityScore || 0) >= 70;
    }
    /**
     * Check if correlation is completed
     */
    isCorrelationCompleted() {
        return this.correlationStatus === correlation_status_enum_1.CorrelationStatus.COMPLETED;
    }
    /**
     * Check if the event has high confidence correlation
     */
    isHighConfidenceCorrelation() {
        return [confidence_level_enum_1.ConfidenceLevel.HIGH, confidence_level_enum_1.ConfidenceLevel.VERY_HIGH, confidence_level_enum_1.ConfidenceLevel.CONFIRMED].includes(this.confidenceLevel);
    }
    /**
     * Check if the event has multiple related events
     */
    hasMultipleRelatedEvents() {
        return this.relatedEventsCount > 1;
    }
    /**
     * Check if the event has correlation matches
     */
    hasCorrelationMatches() {
        return this.correlationMatchesCount > 0;
    }
    /**
     * Check if the event is part of a potential attack campaign
     */
    isPotentialAttackCampaign() {
        return this.hasAttackChain && this.hasMultipleRelatedEvents() && this.isHighConfidenceCorrelation();
    }
    /**
     * Get event priority for processing
     */
    getEventPriority() {
        if (this.isCritical() && this.isPotentialAttackCampaign()) {
            return 'critical';
        }
        if (this.isHighSeverity() && this.hasAttackChain) {
            return 'high';
        }
        if (this.hasHighCorrelationQuality() && this.hasCorrelationMatches()) {
            return 'medium';
        }
        return 'low';
    }
    /**
     * Get recommended actions based on event characteristics
     */
    getRecommendedActions() {
        const actions = [];
        if (this.isPotentialAttackCampaign()) {
            actions.push('Initiate incident response procedures');
            actions.push('Notify security operations center');
            actions.push('Activate threat hunting team');
        }
        if (this.hasAttackChain) {
            actions.push('Analyze attack chain progression');
            actions.push('Identify attack vectors and techniques');
            actions.push('Assess potential impact and scope');
        }
        if (this.isHighConfidenceCorrelation()) {
            actions.push('Validate correlation findings');
            actions.push('Gather additional evidence');
            actions.push('Prepare containment measures');
        }
        if (this.requiresManualReview) {
            actions.push('Schedule manual review');
            actions.push('Assign to security analyst');
            actions.push('Prioritize based on severity and confidence');
        }
        if (this.hasMultipleRelatedEvents()) {
            actions.push('Investigate related events');
            actions.push('Look for additional correlations');
            actions.push('Map event relationships');
        }
        return actions;
    }
    /**
     * Get alert level based on event characteristics
     */
    getAlertLevel() {
        if (this.isPotentialAttackCampaign()) {
            return 'critical';
        }
        if (this.hasAttackChain && this.isHighSeverity()) {
            return 'alert';
        }
        if (this.isHighConfidenceCorrelation() || this.hasMultipleRelatedEvents()) {
            return 'warning';
        }
        return 'info';
    }
    /**
     * Get escalation requirements
     */
    getEscalationRequirements() {
        if (this.isPotentialAttackCampaign()) {
            return {
                shouldEscalate: true,
                escalationLevel: 'management',
                timeoutMinutes: 15,
                reason: 'Potential coordinated attack campaign detected',
            };
        }
        if (this.isCritical() && this.hasAttackChain) {
            return {
                shouldEscalate: true,
                escalationLevel: 'tier3',
                timeoutMinutes: 30,
                reason: 'Critical severity event with attack chain identified',
            };
        }
        if (this.isHighSeverity() && this.isHighConfidenceCorrelation()) {
            return {
                shouldEscalate: true,
                escalationLevel: 'tier2',
                timeoutMinutes: 60,
                reason: 'High severity event with high confidence correlation',
            };
        }
        if (this.hasHighCorrelationQuality() && this.hasCorrelationMatches()) {
            return {
                shouldEscalate: true,
                escalationLevel: 'tier1',
                timeoutMinutes: 120,
                reason: 'Quality correlation with multiple matches found',
            };
        }
        return {
            shouldEscalate: false,
            escalationLevel: 'tier1',
            timeoutMinutes: 240,
            reason: 'Standard correlation processing',
        };
    }
    /**
     * Get event summary for handlers
     */
    getEventSummary() {
        return {
            correlatedEventId: this.aggregateId.toString(),
            enrichedEventId: this.enrichedEventId.toString(),
            eventType: this.eventType,
            severity: this.severity,
            correlationStatus: this.correlationStatus,
            correlationQualityScore: this.correlationQualityScore,
            appliedRulesCount: this.appliedRulesCount,
            correlationMatchesCount: this.correlationMatchesCount,
            relatedEventsCount: this.relatedEventsCount,
            confidenceLevel: this.confidenceLevel,
            hasAttackChain: this.hasAttackChain,
            requiresManualReview: this.requiresManualReview,
            isHighSeverity: this.isHighSeverity(),
            isCritical: this.isCritical(),
            hasHighCorrelationQuality: this.hasHighCorrelationQuality(),
            isCorrelationCompleted: this.isCorrelationCompleted(),
            isHighConfidenceCorrelation: this.isHighConfidenceCorrelation(),
            hasMultipleRelatedEvents: this.hasMultipleRelatedEvents(),
            hasCorrelationMatches: this.hasCorrelationMatches(),
            isPotentialAttackCampaign: this.isPotentialAttackCampaign(),
            eventPriority: this.getEventPriority(),
            alertLevel: this.getAlertLevel(),
            recommendedActions: this.getRecommendedActions(),
            escalationRequirements: this.getEscalationRequirements(),
        };
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            ...super.toJSON(),
            eventSummary: this.getEventSummary(),
        };
    }
}
exports.CorrelatedEventCreatedDomainEvent = CorrelatedEventCreatedDomainEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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