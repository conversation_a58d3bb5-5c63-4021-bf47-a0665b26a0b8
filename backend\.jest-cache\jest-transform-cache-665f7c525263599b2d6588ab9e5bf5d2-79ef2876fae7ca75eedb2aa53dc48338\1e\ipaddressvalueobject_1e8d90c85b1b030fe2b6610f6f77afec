204e03ca58bfce78b65203ea23cbb92a
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IPAddress = exports.IPAddressClass = exports.IPAddressType = void 0;
const base_value_object_1 = require("../../../../../shared-kernel/value-objects/base-value-object");
/**
 * IP Address Type
 */
var IPAddressType;
(function (IPAddressType) {
    IPAddressType["IPv4"] = "ipv4";
    IPAddressType["IPv6"] = "ipv6";
})(IPAddressType || (exports.IPAddressType = IPAddressType = {}));
/**
 * IP Address Classification
 */
var IPAddressClass;
(function (IPAddressClass) {
    IPAddressClass["PUBLIC"] = "public";
    IPAddressClass["PRIVATE"] = "private";
    IPAddressClass["LOOPBACK"] = "loopback";
    IPAddressClass["MULTICAST"] = "multicast";
    IPAddressClass["BROADCAST"] = "broadcast";
    IPAddressClass["LINK_LOCAL"] = "link_local";
    IPAddressClass["RESERVED"] = "reserved";
})(IPAddressClass || (exports.IPAddressClass = IPAddressClass = {}));
/**
 * IP Address Value Object
 *
 * Represents an IP address (IPv4 or IPv6) with validation and utility methods.
 * Provides classification, network analysis, and security-related functionality.
 *
 * Key features:
 * - IPv4 and IPv6 support
 * - Address classification (public, private, etc.)
 * - Network range checking
 * - Geolocation support preparation
 * - Security analysis utilities
 */
class IPAddress extends base_value_object_1.BaseValueObject {
    constructor(value) {
        super(value);
    }
    initializeProperties() {
        this._type = this.determineType();
        if (this._type === IPAddressType.IPv4) {
            this._octets = this.parseIPv4Octets();
        }
        else {
            this._segments = this.parseIPv6Segments();
        }
    }
    validate() {
        if (!this._value || this._value.trim().length === 0) {
            throw new Error('IP address cannot be empty');
        }
        const trimmed = this._value.trim();
        if (!this.isValidIPv4(trimmed) && !this.isValidIPv6(trimmed)) {
            throw new Error(`Invalid IP address format: ${trimmed}`);
        }
    }
    determineType() {
        if (this.isValidIPv4(this._value)) {
            return IPAddressType.IPv4;
        }
        else if (this.isValidIPv6(this._value)) {
            return IPAddressType.IPv6;
        }
        throw new Error('Unable to determine IP address type');
    }
    isValidIPv4(address) {
        return IPAddress.IPv4_REGEX.test(address);
    }
    isValidIPv6(address) {
        // Handle special cases first
        if (address === '::' || address === '::1') {
            return true;
        }
        // Reject addresses with multiple :: sequences
        if ((address.match(/::/g) || []).length > 1) {
            return false;
        }
        // Check for full IPv6 format
        if (IPAddress.IPv6_REGEX.test(address)) {
            return true;
        }
        // Check for compressed IPv6 format
        if (IPAddress.IPv6_COMPRESSED_REGEX.test(address)) {
            return true;
        }
        // Additional validation for common IPv6 patterns
        const ipv6Pattern = /^([0-9a-fA-F]{0,4}:){1,7}[0-9a-fA-F]{0,4}$|^::([0-9a-fA-F]{0,4}:){0,6}[0-9a-fA-F]{0,4}$|^([0-9a-fA-F]{0,4}:){1,6}::$|^([0-9a-fA-F]{0,4}:){1,6}:([0-9a-fA-F]{0,4}:){0,5}[0-9a-fA-F]{0,4}$/;
        return ipv6Pattern.test(address);
    }
    parseIPv4Octets() {
        return this._value.split('.').map(octet => parseInt(octet, 10));
    }
    parseIPv6Segments() {
        // Expand compressed IPv6 addresses
        let expanded = this._value;
        if (expanded.includes('::')) {
            const parts = expanded.split('::');
            const leftParts = parts[0] ? parts[0].split(':') : [];
            const rightParts = parts[1] ? parts[1].split(':') : [];
            const missingParts = 8 - leftParts.length - rightParts.length;
            const middleParts = Array(missingParts).fill('0000');
            expanded = [...leftParts, ...middleParts, ...rightParts].join(':');
        }
        return expanded.split(':').map(segment => segment.padStart(4, '0'));
    }
    /**
     * Create an IP address from string
     */
    static fromString(address) {
        return new IPAddress(address);
    }
    /**
     * Create an IPv4 address from octets
     */
    static fromIPv4Octets(octet1, octet2, octet3, octet4) {
        const address = `${octet1}.${octet2}.${octet3}.${octet4}`;
        return new IPAddress(address);
    }
    /**
     * Create localhost IPv4 address
     */
    static localhost() {
        return new IPAddress('127.0.0.1');
    }
    /**
     * Create localhost IPv6 address
     */
    static localhostIPv6() {
        return new IPAddress('::1');
    }
    /**
     * Get IP address type
     */
    get type() {
        return this._type;
    }
    /**
     * Check if this is an IPv4 address
     */
    isIPv4() {
        return this._type === IPAddressType.IPv4;
    }
    /**
     * Check if this is an IPv6 address
     */
    isIPv6() {
        return this._type === IPAddressType.IPv6;
    }
    /**
     * Get IPv4 octets (only for IPv4 addresses)
     */
    getOctets() {
        if (!this.isIPv4()) {
            throw new Error('Cannot get octets for non-IPv4 address');
        }
        return [...this._octets];
    }
    /**
     * Get IPv6 segments (only for IPv6 addresses)
     */
    getSegments() {
        if (!this.isIPv6()) {
            throw new Error('Cannot get segments for non-IPv6 address');
        }
        return [...this._segments];
    }
    /**
     * Classify the IP address
     */
    classify() {
        if (this.isIPv4()) {
            return this.classifyIPv4();
        }
        else {
            return this.classifyIPv6();
        }
    }
    classifyIPv4() {
        const octets = this._octets;
        const [first, second, third, fourth] = octets;
        // Loopback (*********/8)
        if (first === 127) {
            return IPAddressClass.LOOPBACK;
        }
        // Multicast (*********/4)
        if (first >= 224 && first <= 239) {
            return IPAddressClass.MULTICAST;
        }
        // Broadcast
        if (first === 255 && second === 255 && third === 255 && fourth === 255) {
            return IPAddressClass.BROADCAST;
        }
        // Link-local (***********/16)
        if (first === 169 && second === 254) {
            return IPAddressClass.LINK_LOCAL;
        }
        // Private ranges
        if (first === 10 || // 10.0.0.0/8
            (first === 172 && second >= 16 && second <= 31) || // **********/12
            (first === 192 && second === 168) // ***********/16
        ) {
            return IPAddressClass.PRIVATE;
        }
        // Reserved ranges
        if (first === 0 || first >= 240) {
            return IPAddressClass.RESERVED;
        }
        return IPAddressClass.PUBLIC;
    }
    classifyIPv6() {
        const address = this._value.toLowerCase();
        // Loopback (::1)
        if (address === '::1') {
            return IPAddressClass.LOOPBACK;
        }
        // Link-local (fe80::/10)
        if (address.startsWith('fe8') || address.startsWith('fe9') ||
            address.startsWith('fea') || address.startsWith('feb')) {
            return IPAddressClass.LINK_LOCAL;
        }
        // Multicast (ff00::/8)
        if (address.startsWith('ff')) {
            return IPAddressClass.MULTICAST;
        }
        // Private/Unique Local (fc00::/7)
        if (address.startsWith('fc') || address.startsWith('fd')) {
            return IPAddressClass.PRIVATE;
        }
        // Reserved (including documentation addresses)
        if (address.startsWith('::') || address.startsWith('2001:db8')) {
            return IPAddressClass.RESERVED;
        }
        // Check for other reserved ranges
        if (address.startsWith('2001:0db8') || address.startsWith('2001:db8')) {
            return IPAddressClass.RESERVED;
        }
        return IPAddressClass.PUBLIC;
    }
    /**
     * Check if address is private
     */
    isPrivate() {
        return this.classify() === IPAddressClass.PRIVATE;
    }
    /**
     * Check if address is public
     */
    isPublic() {
        return this.classify() === IPAddressClass.PUBLIC;
    }
    /**
     * Check if address is loopback
     */
    isLoopback() {
        return this.classify() === IPAddressClass.LOOPBACK;
    }
    /**
     * Check if address is multicast
     */
    isMulticast() {
        return this.classify() === IPAddressClass.MULTICAST;
    }
    /**
     * Check if address is in a specific network range
     */
    isInNetwork(networkAddress, prefixLength) {
        if (this.isIPv4()) {
            return this.isInIPv4Network(networkAddress, prefixLength);
        }
        else {
            return this.isInIPv6Network(networkAddress, prefixLength);
        }
    }
    isInIPv4Network(networkAddress, prefixLength) {
        const networkIP = new IPAddress(networkAddress);
        if (!networkIP.isIPv4()) {
            return false;
        }
        const thisInt = this.toIPv4Integer();
        const networkInt = networkIP.toIPv4Integer();
        const mask = (0xFFFFFFFF << (32 - prefixLength)) >>> 0;
        return (thisInt & mask) === (networkInt & mask);
    }
    isInIPv6Network(networkAddress, prefixLength) {
        // Simplified IPv6 network checking
        const networkIP = new IPAddress(networkAddress);
        if (!networkIP.isIPv6()) {
            return false;
        }
        const thisSegments = this.getSegments();
        const networkSegments = networkIP.getSegments();
        const bitsToCheck = prefixLength;
        let checkedBits = 0;
        for (let i = 0; i < 8 && checkedBits < bitsToCheck; i++) {
            const thisSegmentInt = parseInt(thisSegments[i], 16);
            const networkSegmentInt = parseInt(networkSegments[i], 16);
            const remainingBits = Math.min(16, bitsToCheck - checkedBits);
            const mask = (0xFFFF << (16 - remainingBits)) & 0xFFFF;
            if ((thisSegmentInt & mask) !== (networkSegmentInt & mask)) {
                return false;
            }
            checkedBits += 16;
        }
        return true;
    }
    /**
     * Convert IPv4 to 32-bit integer
     */
    toIPv4Integer() {
        if (!this.isIPv4()) {
            throw new Error('Cannot convert non-IPv4 address to integer');
        }
        const octets = this._octets;
        return (octets[0] << 24) + (octets[1] << 16) + (octets[2] << 8) + octets[3];
    }
    /**
     * Get reverse DNS lookup format
     */
    getReverseDNSFormat() {
        if (this.isIPv4()) {
            const octets = this._octets;
            return `${octets[3]}.${octets[2]}.${octets[1]}.${octets[0]}.in-addr.arpa`;
        }
        else {
            // IPv6 reverse DNS is more complex, simplified implementation
            const segments = this.getSegments();
            const chars = segments.join('').split('').reverse();
            return chars.join('.') + '.ip6.arpa';
        }
    }
    /**
     * Get network address for given prefix length
     */
    getNetworkAddress(prefixLength) {
        if (this.isIPv4()) {
            const thisInt = this.toIPv4Integer();
            const mask = (0xFFFFFFFF << (32 - prefixLength)) >>> 0;
            const networkInt = thisInt & mask;
            const octet1 = (networkInt >>> 24) & 0xFF;
            const octet2 = (networkInt >>> 16) & 0xFF;
            const octet3 = (networkInt >>> 8) & 0xFF;
            const octet4 = networkInt & 0xFF;
            return IPAddress.fromIPv4Octets(octet1, octet2, octet3, octet4);
        }
        else {
            // Simplified IPv6 network address calculation
            throw new Error('IPv6 network address calculation not implemented');
        }
    }
    /**
     * Check if this IP address could be suspicious
     */
    isSuspicious() {
        // Basic heuristics for suspicious IP addresses
        if (this.isPrivate() || this.isLoopback()) {
            return false; // Internal addresses are generally not suspicious
        }
        if (this.isIPv4()) {
            const octets = this._octets;
            // Check for common suspicious patterns
            // All octets the same (but exclude common public DNS like *******)
            if (octets.every(octet => octet === octets[0]) && octets[0] !== 8) {
                return true;
            }
            // Sequential octets
            if (octets[1] === octets[0] + 1 && octets[2] === octets[1] + 1 && octets[3] === octets[2] + 1) {
                return true;
            }
        }
        return false;
    }
    /**
     * Get geographic region hint based on IP ranges
     */
    getRegionHint() {
        if (!this.isPublic()) {
            return null;
        }
        // This is a simplified implementation
        // In practice, you would use a GeoIP database
        if (this.isIPv4()) {
            const firstOctet = this._octets[0];
            // Very basic regional hints based on first octet
            if (firstOctet >= 1 && firstOctet <= 126)
                return 'North America';
            if (firstOctet >= 128 && firstOctet <= 191)
                return 'Europe/Asia';
            if (firstOctet >= 192 && firstOctet <= 223)
                return 'Asia Pacific';
        }
        return null;
    }
    /**
     * Compare IP addresses for equality
     */
    equals(other) {
        if (!other) {
            return false;
        }
        if (this === other) {
            return true;
        }
        // Normalize both addresses for comparison
        return this._value.toLowerCase() === other._value.toLowerCase();
    }
    /**
     * Get string representation
     */
    toString() {
        return this._value;
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            address: this._value,
            type: this._type,
            classification: this.classify(),
            isPrivate: this.isPrivate(),
            isPublic: this.isPublic(),
            isLoopback: this.isLoopback(),
            isMulticast: this.isMulticast(),
            isSuspicious: this.isSuspicious(),
            regionHint: this.getRegionHint(),
            reverseDNS: this.getReverseDNSFormat(),
        };
    }
    /**
     * Create IPAddress from JSON
     */
    static fromJSON(json) {
        return new IPAddress(json.address);
    }
    /**
     * Validate IP address format without creating instance
     */
    static isValid(address) {
        try {
            new IPAddress(address);
            return true;
        }
        catch {
            return false;
        }
    }
}
exports.IPAddress = IPAddress;
IPAddress.IPv4_REGEX = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
IPAddress.IPv6_REGEX = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
IPAddress.IPv6_COMPRESSED_REGEX = /^(([0-9a-fA-F]{1,4}:)*)?::([0-9a-fA-F]{1,4}:)*[0-9a-fA-F]{1,4}$|^::$|^::1$/;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxjb3JlXFxzZWN1cml0eVxcZG9tYWluXFx2YWx1ZS1vYmplY3RzXFxuZXR3b3JrXFxpcC1hZGRyZXNzLnZhbHVlLW9iamVjdC50cyIsIm1hcHBpbmdzIjoiOzs7QUFBQSxvR0FBK0Y7QUFFL0Y7O0dBRUc7QUFDSCxJQUFZLGFBR1g7QUFIRCxXQUFZLGFBQWE7SUFDdkIsOEJBQWEsQ0FBQTtJQUNiLDhCQUFhLENBQUE7QUFDZixDQUFDLEVBSFcsYUFBYSw2QkFBYixhQUFhLFFBR3hCO0FBRUQ7O0dBRUc7QUFDSCxJQUFZLGNBUVg7QUFSRCxXQUFZLGNBQWM7SUFDeEIsbUNBQWlCLENBQUE7SUFDakIscUNBQW1CLENBQUE7SUFDbkIsdUNBQXFCLENBQUE7SUFDckIseUNBQXVCLENBQUE7SUFDdkIseUNBQXVCLENBQUE7SUFDdkIsMkNBQXlCLENBQUE7SUFDekIsdUNBQXFCLENBQUE7QUFDdkIsQ0FBQyxFQVJXLGNBQWMsOEJBQWQsY0FBYyxRQVF6QjtBQUVEOzs7Ozs7Ozs7Ozs7R0FZRztBQUNILE1BQWEsU0FBVSxTQUFRLG1DQUF1QjtJQVNwRCxZQUFZLEtBQWE7UUFDdkIsS0FBSyxDQUFDLEtBQUssQ0FBQyxDQUFDO0lBQ2YsQ0FBQztJQUVTLG9CQUFvQjtRQUMzQixJQUFZLENBQUMsS0FBSyxHQUFHLElBQUksQ0FBQyxhQUFhLEVBQUUsQ0FBQztRQUUzQyxJQUFLLElBQVksQ0FBQyxLQUFLLEtBQUssYUFBYSxDQUFDLElBQUksRUFBRSxDQUFDO1lBQzlDLElBQVksQ0FBQyxPQUFPLEdBQUcsSUFBSSxDQUFDLGVBQWUsRUFBRSxDQUFDO1FBQ2pELENBQUM7YUFBTSxDQUFDO1lBQ0wsSUFBWSxDQUFDLFNBQVMsR0FBRyxJQUFJLENBQUMsaUJBQWlCLEVBQUUsQ0FBQztRQUNyRCxDQUFDO0lBQ0gsQ0FBQztJQUVTLFFBQVE7UUFDaEIsSUFBSSxDQUFDLElBQUksQ0FBQyxNQUFNLElBQUksSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLEVBQUUsQ0FBQyxNQUFNLEtBQUssQ0FBQyxFQUFFLENBQUM7WUFDcEQsTUFBTSxJQUFJLEtBQUssQ0FBQyw0QkFBNEIsQ0FBQyxDQUFDO1FBQ2hELENBQUM7UUFFRCxNQUFNLE9BQU8sR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLElBQUksRUFBRSxDQUFDO1FBRW5DLElBQUksQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxPQUFPLENBQUMsRUFBRSxDQUFDO1lBQzdELE1BQU0sSUFBSSxLQUFLLENBQUMsOEJBQThCLE9BQU8sRUFBRSxDQUFDLENBQUM7UUFDM0QsQ0FBQztJQUNILENBQUM7SUFFTyxhQUFhO1FBQ25CLElBQUksSUFBSSxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLEVBQUUsQ0FBQztZQUNsQyxPQUFPLGFBQWEsQ0FBQyxJQUFJLENBQUM7UUFDNUIsQ0FBQzthQUFNLElBQUksSUFBSSxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLEVBQUUsQ0FBQztZQUN6QyxPQUFPLGFBQWEsQ0FBQyxJQUFJLENBQUM7UUFDNUIsQ0FBQztRQUNELE1BQU0sSUFBSSxLQUFLLENBQUMscUNBQXFDLENBQUMsQ0FBQztJQUN6RCxDQUFDO0lBRU8sV0FBVyxDQUFDLE9BQWU7UUFDakMsT0FBTyxTQUFTLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQztJQUM1QyxDQUFDO0lBRU8sV0FBVyxDQUFDLE9BQWU7UUFDakMsNkJBQTZCO1FBQzdCLElBQUksT0FBTyxLQUFLLElBQUksSUFBSSxPQUFPLEtBQUssS0FBSyxFQUFFLENBQUM7WUFDMUMsT0FBTyxJQUFJLENBQUM7UUFDZCxDQUFDO1FBRUQsOENBQThDO1FBQzlDLElBQUksQ0FBQyxPQUFPLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxJQUFJLEVBQUUsQ0FBQyxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUUsQ0FBQztZQUM1QyxPQUFPLEtBQUssQ0FBQztRQUNmLENBQUM7UUFFRCw2QkFBNkI7UUFDN0IsSUFBSSxTQUFTLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsRUFBRSxDQUFDO1lBQ3ZDLE9BQU8sSUFBSSxDQUFDO1FBQ2QsQ0FBQztRQUVELG1DQUFtQztRQUNuQyxJQUFJLFNBQVMsQ0FBQyxxQkFBcUIsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLEVBQUUsQ0FBQztZQUNsRCxPQUFPLElBQUksQ0FBQztRQUNkLENBQUM7UUFFRCxpREFBaUQ7UUFDakQsTUFBTSxXQUFXLEdBQUcsMExBQTBMLENBQUM7UUFFL00sT0FBTyxXQUFXLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxDQUFDO0lBQ25DLENBQUM7SUFFTyxlQUFlO1FBQ3JCLE9BQU8sSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUMsR0FBRyxDQUFDLEtBQUssQ0FBQyxFQUFFLENBQUMsUUFBUSxDQUFDLEtBQUssRUFBRSxFQUFFLENBQUMsQ0FBQyxDQUFDO0lBQ2xFLENBQUM7SUFFTyxpQkFBaUI7UUFDdkIsbUNBQW1DO1FBQ25DLElBQUksUUFBUSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUM7UUFDM0IsSUFBSSxRQUFRLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUM7WUFDNUIsTUFBTSxLQUFLLEdBQUcsUUFBUSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUNuQyxNQUFNLFNBQVMsR0FBRyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQztZQUN0RCxNQUFNLFVBQVUsR0FBRyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQztZQUN2RCxNQUFNLFlBQVksR0FBRyxDQUFDLEdBQUcsU0FBUyxDQUFDLE1BQU0sR0FBRyxVQUFVLENBQUMsTUFBTSxDQUFDO1lBQzlELE1BQU0sV0FBVyxHQUFHLEtBQUssQ0FBQyxZQUFZLENBQUMsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUM7WUFDckQsUUFBUSxHQUFHLENBQUMsR0FBRyxTQUFTLEVBQUUsR0FBRyxXQUFXLEVBQUUsR0FBRyxVQUFVLENBQUMsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUM7UUFDckUsQ0FBQztRQUNELE9BQU8sUUFBUSxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsQ0FBQyxHQUFHLENBQUMsT0FBTyxDQUFDLEVBQUUsQ0FBQyxPQUFPLENBQUMsUUFBUSxDQUFDLENBQUMsRUFBRSxHQUFHLENBQUMsQ0FBQyxDQUFDO0lBQ3RFLENBQUM7SUFFRDs7T0FFRztJQUNILE1BQU0sQ0FBQyxVQUFVLENBQUMsT0FBZTtRQUMvQixPQUFPLElBQUksU0FBUyxDQUFDLE9BQU8sQ0FBQyxDQUFDO0lBQ2hDLENBQUM7SUFFRDs7T0FFRztJQUNILE1BQU0sQ0FBQyxjQUFjLENBQUMsTUFBYyxFQUFFLE1BQWMsRUFBRSxNQUFjLEVBQUUsTUFBYztRQUNsRixNQUFNLE9BQU8sR0FBRyxHQUFHLE1BQU0sSUFBSSxNQUFNLElBQUksTUFBTSxJQUFJLE1BQU0sRUFBRSxDQUFDO1FBQzFELE9BQU8sSUFBSSxTQUFTLENBQUMsT0FBTyxDQUFDLENBQUM7SUFDaEMsQ0FBQztJQUVEOztPQUVHO0lBQ0gsTUFBTSxDQUFDLFNBQVM7UUFDZCxPQUFPLElBQUksU0FBUyxDQUFDLFdBQVcsQ0FBQyxDQUFDO0lBQ3BDLENBQUM7SUFFRDs7T0FFRztJQUNILE1BQU0sQ0FBQyxhQUFhO1FBQ2xCLE9BQU8sSUFBSSxTQUFTLENBQUMsS0FBSyxDQUFDLENBQUM7SUFDOUIsQ0FBQztJQUVEOztPQUVHO0lBQ0gsSUFBSSxJQUFJO1FBQ04sT0FBTyxJQUFJLENBQUMsS0FBSyxDQUFDO0lBQ3BCLENBQUM7SUFFRDs7T0FFRztJQUNILE1BQU07UUFDSixPQUFPLElBQUksQ0FBQyxLQUFLLEtBQUssYUFBYSxDQUFDLElBQUksQ0FBQztJQUMzQyxDQUFDO0lBRUQ7O09BRUc7SUFDSCxNQUFNO1FBQ0osT0FBTyxJQUFJLENBQUMsS0FBSyxLQUFLLGFBQWEsQ0FBQyxJQUFJLENBQUM7SUFDM0MsQ0FBQztJQUVEOztPQUVHO0lBQ0gsU0FBUztRQUNQLElBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxFQUFFLEVBQUUsQ0FBQztZQUNuQixNQUFNLElBQUksS0FBSyxDQUFDLHdDQUF3QyxDQUFDLENBQUM7UUFDNUQsQ0FBQztRQUNELE9BQU8sQ0FBQyxHQUFHLElBQUksQ0FBQyxPQUFRLENBQUMsQ0FBQztJQUM1QixDQUFDO0lBRUQ7O09BRUc7SUFDSCxXQUFXO1FBQ1QsSUFBSSxDQUFDLElBQUksQ0FBQyxNQUFNLEVBQUUsRUFBRSxDQUFDO1lBQ25CLE1BQU0sSUFBSSxLQUFLLENBQUMsMENBQTBDLENBQUMsQ0FBQztRQUM5RCxDQUFDO1FBQ0QsT0FBTyxDQUFDLEdBQUcsSUFBSSxDQUFDLFNBQVUsQ0FBQyxDQUFDO0lBQzlCLENBQUM7SUFFRDs7T0FFRztJQUNILFFBQVE7UUFDTixJQUFJLElBQUksQ0FBQyxNQUFNLEVBQUUsRUFBRSxDQUFDO1lBQ2xCLE9BQU8sSUFBSSxDQUFDLFlBQVksRUFBRSxDQUFDO1FBQzdCLENBQUM7YUFBTSxDQUFDO1lBQ04sT0FBTyxJQUFJLENBQUMsWUFBWSxFQUFFLENBQUM7UUFDN0IsQ0FBQztJQUNILENBQUM7SUFFTyxZQUFZO1FBQ2xCLE1BQU0sTUFBTSxHQUFHLElBQUksQ0FBQyxPQUFRLENBQUM7UUFDN0IsTUFBTSxDQUFDLEtBQUssRUFBRSxNQUFNLEVBQUUsS0FBSyxFQUFFLE1BQU0sQ0FBQyxHQUFHLE1BQU0sQ0FBQztRQUU5Qyx5QkFBeUI7UUFDekIsSUFBSSxLQUFLLEtBQUssR0FBRyxFQUFFLENBQUM7WUFDbEIsT0FBTyxjQUFjLENBQUMsUUFBUSxDQUFDO1FBQ2pDLENBQUM7UUFFRCwwQkFBMEI7UUFDMUIsSUFBSSxLQUFLLElBQUksR0FBRyxJQUFJLEtBQUssSUFBSSxHQUFHLEVBQUUsQ0FBQztZQUNqQyxPQUFPLGNBQWMsQ0FBQyxTQUFTLENBQUM7UUFDbEMsQ0FBQztRQUVELFlBQVk7UUFDWixJQUFJLEtBQUssS0FBSyxHQUFHLElBQUksTUFBTSxLQUFLLEdBQUcsSUFBSSxLQUFLLEtBQUssR0FBRyxJQUFJLE1BQU0sS0FBSyxHQUFHLEVBQUUsQ0FBQztZQUN2RSxPQUFPLGNBQWMsQ0FBQyxTQUFTLENBQUM7UUFDbEMsQ0FBQztRQUVELDhCQUE4QjtRQUM5QixJQUFJLEtBQUssS0FBSyxHQUFHLElBQUksTUFBTSxLQUFLLEdBQUcsRUFBRSxDQUFDO1lBQ3BDLE9BQU8sY0FBYyxDQUFDLFVBQVUsQ0FBQztRQUNuQyxDQUFDO1FBRUQsaUJBQWlCO1FBQ2pCLElBQ0UsS0FBSyxLQUFLLEVBQUUsSUFBSSxhQUFhO1lBQzdCLENBQUMsS0FBSyxLQUFLLEdBQUcsSUFBSSxNQUFNLElBQUksRUFBRSxJQUFJLE1BQU0sSUFBSSxFQUFFLENBQUMsSUFBSSxnQkFBZ0I7WUFDbkUsQ0FBQyxLQUFLLEtBQUssR0FBRyxJQUFJLE1BQU0sS0FBSyxHQUFHLENBQUMsQ0FBQyxpQkFBaUI7VUFDbkQsQ0FBQztZQUNELE9BQU8sY0FBYyxDQUFDLE9BQU8sQ0FBQztRQUNoQyxDQUFDO1FBRUQsa0JBQWtCO1FBQ2xCLElBQUksS0FBSyxLQUFLLENBQUMsSUFBSSxLQUFLLElBQUksR0FBRyxFQUFFLENBQUM7WUFDaEMsT0FBTyxjQUFjLENBQUMsUUFBUSxDQUFDO1FBQ2pDLENBQUM7UUFFRCxPQUFPLGNBQWMsQ0FBQyxNQUFNLENBQUM7SUFDL0IsQ0FBQztJQUVPLFlBQVk7UUFDbEIsTUFBTSxPQUFPLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxXQUFXLEVBQUUsQ0FBQztRQUUxQyxpQkFBaUI7UUFDakIsSUFBSSxPQUFPLEtBQUssS0FBSyxFQUFFLENBQUM7WUFDdEIsT0FBTyxjQUFjLENBQUMsUUFBUSxDQUFDO1FBQ2pDLENBQUM7UUFFRCx5QkFBeUI7UUFDekIsSUFBSSxPQUFPLENBQUMsVUFBVSxDQUFDLEtBQUssQ0FBQyxJQUFJLE9BQU8sQ0FBQyxVQUFVLENBQUMsS0FBSyxDQUFDO1lBQ3RELE9BQU8sQ0FBQyxVQUFVLENBQUMsS0FBSyxDQUFDLElBQUksT0FBTyxDQUFDLFVBQVUsQ0FBQyxLQUFLLENBQUMsRUFBRSxDQUFDO1lBQzNELE9BQU8sY0FBYyxDQUFDLFVBQVUsQ0FBQztRQUNuQyxDQUFDO1FBRUQsdUJBQXVCO1FBQ3ZCLElBQUksT0FBTyxDQUFDLFVBQVUsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDO1lBQzdCLE9BQU8sY0FBYyxDQUFDLFNBQVMsQ0FBQztRQUNsQyxDQUFDO1FBRUQsa0NBQWtDO1FBQ2xDLElBQUksT0FBTyxDQUFDLFVBQVUsQ0FBQyxJQUFJLENBQUMsSUFBSSxPQUFPLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUM7WUFDekQsT0FBTyxjQUFjLENBQUMsT0FBTyxDQUFDO1FBQ2hDLENBQUM7UUFFRCwrQ0FBK0M7UUFDL0MsSUFBSSxPQUFPLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxJQUFJLE9BQU8sQ0FBQyxVQUFVLENBQUMsVUFBVSxDQUFDLEVBQUUsQ0FBQztZQUMvRCxPQUFPLGNBQWMsQ0FBQyxRQUFRLENBQUM7UUFDakMsQ0FBQztRQUVELGtDQUFrQztRQUNsQyxJQUFJLE9BQU8sQ0FBQyxVQUFVLENBQUMsV0FBVyxDQUFDLElBQUksT0FBTyxDQUFDLFVBQVUsQ0FBQyxVQUFVLENBQUMsRUFBRSxDQUFDO1lBQ3RFLE9BQU8sY0FBYyxDQUFDLFFBQVEsQ0FBQztRQUNqQyxDQUFDO1FBRUQsT0FBTyxjQUFjLENBQUMsTUFBTSxDQUFDO0lBQy9CLENBQUM7SUFFRDs7T0FFRztJQUNILFNBQVM7UUFDUCxPQUFPLElBQUksQ0FBQyxRQUFRLEVBQUUsS0FBSyxjQUFjLENBQUMsT0FBTyxDQUFDO0lBQ3BELENBQUM7SUFFRDs7T0FFRztJQUNILFFBQVE7UUFDTixPQUFPLElBQUksQ0FBQyxRQUFRLEVBQUUsS0FBSyxjQUFjLENBQUMsTUFBTSxDQUFDO0lBQ25ELENBQUM7SUFFRDs7T0FFRztJQUNILFVBQVU7UUFDUixPQUFPLElBQUksQ0FBQyxRQUFRLEVBQUUsS0FBSyxjQUFjLENBQUMsUUFBUSxDQUFDO0lBQ3JELENBQUM7SUFFRDs7T0FFRztJQUNILFdBQVc7UUFDVCxPQUFPLElBQUksQ0FBQyxRQUFRLEVBQUUsS0FBSyxjQUFjLENBQUMsU0FBUyxDQUFDO0lBQ3RELENBQUM7SUFFRDs7T0FFRztJQUNILFdBQVcsQ0FBQyxjQUFzQixFQUFFLFlBQW9CO1FBQ3RELElBQUksSUFBSSxDQUFDLE1BQU0sRUFBRSxFQUFFLENBQUM7WUFDbEIsT0FBTyxJQUFJLENBQUMsZUFBZSxDQUFDLGNBQWMsRUFBRSxZQUFZLENBQUMsQ0FBQztRQUM1RCxDQUFDO2FBQU0sQ0FBQztZQUNOLE9BQU8sSUFBSSxDQUFDLGVBQWUsQ0FBQyxjQUFjLEVBQUUsWUFBWSxDQUFDLENBQUM7UUFDNUQsQ0FBQztJQUNILENBQUM7SUFFTyxlQUFlLENBQUMsY0FBc0IsRUFBRSxZQUFvQjtRQUNsRSxNQUFNLFNBQVMsR0FBRyxJQUFJLFNBQVMsQ0FBQyxjQUFjLENBQUMsQ0FBQztRQUNoRCxJQUFJLENBQUMsU0FBUyxDQUFDLE1BQU0sRUFBRSxFQUFFLENBQUM7WUFDeEIsT0FBTyxLQUFLLENBQUM7UUFDZixDQUFDO1FBRUQsTUFBTSxPQUFPLEdBQUcsSUFBSSxDQUFDLGFBQWEsRUFBRSxDQUFDO1FBQ3JDLE1BQU0sVUFBVSxHQUFHLFNBQVMsQ0FBQyxhQUFhLEVBQUUsQ0FBQztRQUM3QyxNQUFNLElBQUksR0FBRyxDQUFDLFVBQVUsSUFBSSxDQUFDLEVBQUUsR0FBRyxZQUFZLENBQUMsQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUV2RCxPQUFPLENBQUMsT0FBTyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsVUFBVSxHQUFHLElBQUksQ0FBQyxDQUFDO0lBQ2xELENBQUM7SUFFTyxlQUFlLENBQUMsY0FBc0IsRUFBRSxZQUFvQjtRQUNsRSxtQ0FBbUM7UUFDbkMsTUFBTSxTQUFTLEdBQUcsSUFBSSxTQUFTLENBQUMsY0FBYyxDQUFDLENBQUM7UUFDaEQsSUFBSSxDQUFDLFNBQVMsQ0FBQyxNQUFNLEVBQUUsRUFBRSxDQUFDO1lBQ3hCLE9BQU8sS0FBSyxDQUFDO1FBQ2YsQ0FBQztRQUVELE1BQU0sWUFBWSxHQUFHLElBQUksQ0FBQyxXQUFXLEVBQUUsQ0FBQztRQUN4QyxNQUFNLGVBQWUsR0FBRyxTQUFTLENBQUMsV0FBVyxFQUFFLENBQUM7UUFFaEQsTUFBTSxXQUFXLEdBQUcsWUFBWSxDQUFDO1FBQ2pDLElBQUksV0FBVyxHQUFHLENBQUMsQ0FBQztRQUVwQixLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsQ0FBQyxJQUFJLFdBQVcsR0FBRyxXQUFXLEVBQUUsQ0FBQyxFQUFFLEVBQUUsQ0FBQztZQUN4RCxNQUFNLGNBQWMsR0FBRyxRQUFRLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDO1lBQ3JELE1BQU0saUJBQWlCLEdBQUcsUUFBUSxDQUFDLGVBQWUsQ0FBQyxDQUFDLENBQUMsRUFBRSxFQUFFLENBQUMsQ0FBQztZQUUzRCxNQUFNLGFBQWEsR0FBRyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsRUFBRSxXQUFXLEdBQUcsV0FBVyxDQUFDLENBQUM7WUFDOUQsTUFBTSxJQUFJLEdBQUcsQ0FBQyxNQUFNLElBQUksQ0FBQyxFQUFFLEdBQUcsYUFBYSxDQUFDLENBQUMsR0FBRyxNQUFNLENBQUM7WUFFdkQsSUFBSSxDQUFDLGNBQWMsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLGlCQUFpQixHQUFHLElBQUksQ0FBQyxFQUFFLENBQUM7Z0JBQzNELE9BQU8sS0FBSyxDQUFDO1lBQ2YsQ0FBQztZQUVELFdBQVcsSUFBSSxFQUFFLENBQUM7UUFDcEIsQ0FBQztRQUVELE9BQU8sSUFBSSxDQUFDO0lBQ2QsQ0FBQztJQUVEOztPQUVHO0lBQ0gsYUFBYTtRQUNYLElBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxFQUFFLEVBQUUsQ0FBQztZQUNuQixNQUFNLElBQUksS0FBSyxDQUFDLDRDQUE0QyxDQUFDLENBQUM7UUFDaEUsQ0FBQztRQUNELE1BQU0sTUFBTSxHQUFHLElBQUksQ0FBQyxPQUFRLENBQUM7UUFDN0IsT0FBTyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsSUFBSSxFQUFFLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsSUFBSSxFQUFFLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsR0FBRyxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUM7SUFDOUUsQ0FBQztJQUVEOztPQUVHO0lBQ0gsbUJBQW1CO1FBQ2pCLElBQUksSUFBSSxDQUFDLE1BQU0sRUFBRSxFQUFFLENBQUM7WUFDbEIsTUFBTSxNQUFNLEdBQUcsSUFBSSxDQUFDLE9BQVEsQ0FBQztZQUM3QixPQUFPLEdBQUcsTUFBTSxDQUFDLENBQUMsQ0FBQyxJQUFJLE1BQU0sQ0FBQyxDQUFDLENBQUMsSUFBSSxNQUFNLENBQUMsQ0FBQyxDQUFDLElBQUksTUFBTSxDQUFDLENBQUMsQ0FBQyxlQUFlLENBQUM7UUFDNUUsQ0FBQzthQUFNLENBQUM7WUFDTiw4REFBOEQ7WUFDOUQsTUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLFdBQVcsRUFBRSxDQUFDO1lBQ3BDLE1BQU0sS0FBSyxHQUFHLFFBQVEsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUMsS0FBSyxDQUFDLEVBQUUsQ0FBQyxDQUFDLE9BQU8sRUFBRSxDQUFDO1lBQ3BELE9BQU8sS0FBSyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsR0FBRyxXQUFXLENBQUM7UUFDdkMsQ0FBQztJQUNILENBQUM7SUFFRDs7T0FFRztJQUNILGlCQUFpQixDQUFDLFlBQW9CO1FBQ3BDLElBQUksSUFBSSxDQUFDLE1BQU0sRUFBRSxFQUFFLENBQUM7WUFDbEIsTUFBTSxPQUFPLEdBQUcsSUFBSSxDQUFDLGFBQWEsRUFBRSxDQUFDO1lBQ3JDLE1BQU0sSUFBSSxHQUFHLENBQUMsVUFBVSxJQUFJLENBQUMsRUFBRSxHQUFHLFlBQVksQ0FBQyxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBQ3ZELE1BQU0sVUFBVSxHQUFHLE9BQU8sR0FBRyxJQUFJLENBQUM7WUFFbEMsTUFBTSxNQUFNLEdBQUcsQ0FBQyxVQUFVLEtBQUssRUFBRSxDQUFDLEdBQUcsSUFBSSxDQUFDO1lBQzFDLE1BQU0sTUFBTSxHQUFHLENBQUMsVUFBVSxLQUFLLEVBQUUsQ0FBQyxHQUFHLElBQUksQ0FBQztZQUMxQyxNQUFNLE1BQU0sR0FBRyxDQUFDLFVBQVUsS0FBSyxDQUFDLENBQUMsR0FBRyxJQUFJLENBQUM7WUFDekMsTUFBTSxNQUFNLEdBQUcsVUFBVSxHQUFHLElBQUksQ0FBQztZQUVqQyxPQUFPLFNBQVMsQ0FBQyxjQUFjLENBQUMsTUFBTSxFQUFFLE1BQU0sRUFBRSxNQUFNLEVBQUUsTUFBTSxDQUFDLENBQUM7UUFDbEUsQ0FBQzthQUFNLENBQUM7WUFDTiw4Q0FBOEM7WUFDOUMsTUFBTSxJQUFJLEtBQUssQ0FBQyxrREFBa0QsQ0FBQyxDQUFDO1FBQ3RFLENBQUM7SUFDSCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxZQUFZO1FBQ1YsK0NBQStDO1FBQy9DLElBQUksSUFBSSxDQUFDLFNBQVMsRUFBRSxJQUFJLElBQUksQ0FBQyxVQUFVLEVBQUUsRUFBRSxDQUFDO1lBQzFDLE9BQU8sS0FBSyxDQUFDLENBQUMsa0RBQWtEO1FBQ2xFLENBQUM7UUFFRCxJQUFJLElBQUksQ0FBQyxNQUFNLEVBQUUsRUFBRSxDQUFDO1lBQ2xCLE1BQU0sTUFBTSxHQUFHLElBQUksQ0FBQyxPQUFRLENBQUM7WUFFN0IsdUNBQXVDO1lBQ3ZDLG1FQUFtRTtZQUNuRSxJQUFJLE1BQU0sQ0FBQyxLQUFLLENBQUMsS0FBSyxDQUFDLEVBQUUsQ0FBQyxLQUFLLEtBQUssTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksTUFBTSxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsRUFBRSxDQUFDO2dCQUNsRSxPQUFPLElBQUksQ0FBQztZQUNkLENBQUM7WUFFRCxvQkFBb0I7WUFDcEIsSUFBSSxNQUFNLENBQUMsQ0FBQyxDQUFDLEtBQUssTUFBTSxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsSUFBSSxNQUFNLENBQUMsQ0FBQyxDQUFDLEtBQUssTUFBTSxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsSUFBSSxNQUFNLENBQUMsQ0FBQyxDQUFDLEtBQUssTUFBTSxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDO2dCQUM5RixPQUFPLElBQUksQ0FBQztZQUNkLENBQUM7UUFDSCxDQUFDO1FBRUQsT0FBTyxLQUFLLENBQUM7SUFDZixDQUFDO0lBRUQ7O09BRUc7SUFDSCxhQUFhO1FBQ1gsSUFBSSxDQUFDLElBQUksQ0FBQyxRQUFRLEVBQUUsRUFBRSxDQUFDO1lBQ3JCLE9BQU8sSUFBSSxDQUFDO1FBQ2QsQ0FBQztRQUVELHNDQUFzQztRQUN0Qyw4Q0FBOEM7UUFDOUMsSUFBSSxJQUFJLENBQUMsTUFBTSxFQUFFLEVBQUUsQ0FBQztZQUNsQixNQUFNLFVBQVUsR0FBRyxJQUFJLENBQUMsT0FBUSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBRXBDLGlEQUFpRDtZQUNqRCxJQUFJLFVBQVUsSUFBSSxDQUFDLElBQUksVUFBVSxJQUFJLEdBQUc7Z0JBQUUsT0FBTyxlQUFlLENBQUM7WUFDakUsSUFBSSxVQUFVLElBQUksR0FBRyxJQUFJLFVBQVUsSUFBSSxHQUFHO2dCQUFFLE9BQU8sYUFBYSxDQUFDO1lBQ2pFLElBQUksVUFBVSxJQUFJLEdBQUcsSUFBSSxVQUFVLElBQUksR0FBRztnQkFBRSxPQUFPLGNBQWMsQ0FBQztRQUNwRSxDQUFDO1FBRUQsT0FBTyxJQUFJLENBQUM7SUFDZCxDQUFDO0lBRUQ7O09BRUc7SUFDSSxNQUFNLENBQUMsS0FBaUI7UUFDN0IsSUFBSSxDQUFDLEtBQUssRUFBRSxDQUFDO1lBQ1gsT0FBTyxLQUFLLENBQUM7UUFDZixDQUFDO1FBRUQsSUFBSSxJQUFJLEtBQUssS0FBSyxFQUFFLENBQUM7WUFDbkIsT0FBTyxJQUFJLENBQUM7UUFDZCxDQUFDO1FBRUQsMENBQTBDO1FBQzFDLE9BQU8sSUFBSSxDQUFDLE1BQU0sQ0FBQyxXQUFXLEVBQUUsS0FBSyxLQUFLLENBQUMsTUFBTSxDQUFDLFdBQVcsRUFBRSxDQUFDO0lBQ2xFLENBQUM7SUFFRDs7T0FFRztJQUNJLFFBQVE7UUFDYixPQUFPLElBQUksQ0FBQyxNQUFNLENBQUM7SUFDckIsQ0FBQztJQUVEOztPQUVHO0lBQ0ksTUFBTTtRQUNYLE9BQU87WUFDTCxPQUFPLEVBQUUsSUFBSSxDQUFDLE1BQU07WUFDcEIsSUFBSSxFQUFFLElBQUksQ0FBQyxLQUFLO1lBQ2hCLGNBQWMsRUFBRSxJQUFJLENBQUMsUUFBUSxFQUFFO1lBQy9CLFNBQVMsRUFBRSxJQUFJLENBQUMsU0FBUyxFQUFFO1lBQzNCLFFBQVEsRUFBRSxJQUFJLENBQUMsUUFBUSxFQUFFO1lBQ3pCLFVBQVUsRUFBRSxJQUFJLENBQUMsVUFBVSxFQUFFO1lBQzdCLFdBQVcsRUFBRSxJQUFJLENBQUMsV0FBVyxFQUFFO1lBQy9CLFlBQVksRUFBRSxJQUFJLENBQUMsWUFBWSxFQUFFO1lBQ2pDLFVBQVUsRUFBRSxJQUFJLENBQUMsYUFBYSxFQUFFO1lBQ2hDLFVBQVUsRUFBRSxJQUFJLENBQUMsbUJBQW1CLEVBQUU7U0FDdkMsQ0FBQztJQUNKLENBQUM7SUFFRDs7T0FFRztJQUNILE1BQU0sQ0FBQyxRQUFRLENBQUMsSUFBeUI7UUFDdkMsT0FBTyxJQUFJLFNBQVMsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUM7SUFDckMsQ0FBQztJQUVEOztPQUVHO0lBQ0gsTUFBTSxDQUFDLE9BQU8sQ0FBQyxPQUFlO1FBQzVCLElBQUksQ0FBQztZQUNILElBQUksU0FBUyxDQUFDLE9BQU8sQ0FBQyxDQUFDO1lBQ3ZCLE9BQU8sSUFBSSxDQUFDO1FBQ2QsQ0FBQztRQUFDLE1BQU0sQ0FBQztZQUNQLE9BQU8sS0FBSyxDQUFDO1FBQ2YsQ0FBQztJQUNILENBQUM7O0FBeGVILDhCQXllQztBQXhleUIsb0JBQVUsR0FBRyw2RkFBNkYsQ0FBQztBQUMzRyxvQkFBVSxHQUFHLDRDQUE0QyxDQUFDO0FBQzFELCtCQUFxQixHQUFHLDRFQUE0RSxDQUFDIiwibmFtZXMiOltdLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTHVrYVxcc2VudGluZWxcXGJhY2tlbmRcXHNyY1xcY29yZVxcc2VjdXJpdHlcXGRvbWFpblxcdmFsdWUtb2JqZWN0c1xcbmV0d29ya1xcaXAtYWRkcmVzcy52YWx1ZS1vYmplY3QudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQmFzZVZhbHVlT2JqZWN0IH0gZnJvbSAnLi4vLi4vLi4vLi4vLi4vc2hhcmVkLWtlcm5lbC92YWx1ZS1vYmplY3RzL2Jhc2UtdmFsdWUtb2JqZWN0JztcclxuXHJcbi8qKlxyXG4gKiBJUCBBZGRyZXNzIFR5cGVcclxuICovXHJcbmV4cG9ydCBlbnVtIElQQWRkcmVzc1R5cGUge1xyXG4gIElQdjQgPSAnaXB2NCcsXHJcbiAgSVB2NiA9ICdpcHY2JyxcclxufVxyXG5cclxuLyoqXHJcbiAqIElQIEFkZHJlc3MgQ2xhc3NpZmljYXRpb25cclxuICovXHJcbmV4cG9ydCBlbnVtIElQQWRkcmVzc0NsYXNzIHtcclxuICBQVUJMSUMgPSAncHVibGljJyxcclxuICBQUklWQVRFID0gJ3ByaXZhdGUnLFxyXG4gIExPT1BCQUNLID0gJ2xvb3BiYWNrJyxcclxuICBNVUxUSUNBU1QgPSAnbXVsdGljYXN0JyxcclxuICBCUk9BRENBU1QgPSAnYnJvYWRjYXN0JyxcclxuICBMSU5LX0xPQ0FMID0gJ2xpbmtfbG9jYWwnLFxyXG4gIFJFU0VSVkVEID0gJ3Jlc2VydmVkJyxcclxufVxyXG5cclxuLyoqXHJcbiAqIElQIEFkZHJlc3MgVmFsdWUgT2JqZWN0XHJcbiAqIFxyXG4gKiBSZXByZXNlbnRzIGFuIElQIGFkZHJlc3MgKElQdjQgb3IgSVB2Nikgd2l0aCB2YWxpZGF0aW9uIGFuZCB1dGlsaXR5IG1ldGhvZHMuXHJcbiAqIFByb3ZpZGVzIGNsYXNzaWZpY2F0aW9uLCBuZXR3b3JrIGFuYWx5c2lzLCBhbmQgc2VjdXJpdHktcmVsYXRlZCBmdW5jdGlvbmFsaXR5LlxyXG4gKiBcclxuICogS2V5IGZlYXR1cmVzOlxyXG4gKiAtIElQdjQgYW5kIElQdjYgc3VwcG9ydFxyXG4gKiAtIEFkZHJlc3MgY2xhc3NpZmljYXRpb24gKHB1YmxpYywgcHJpdmF0ZSwgZXRjLilcclxuICogLSBOZXR3b3JrIHJhbmdlIGNoZWNraW5nXHJcbiAqIC0gR2VvbG9jYXRpb24gc3VwcG9ydCBwcmVwYXJhdGlvblxyXG4gKiAtIFNlY3VyaXR5IGFuYWx5c2lzIHV0aWxpdGllc1xyXG4gKi9cclxuZXhwb3J0IGNsYXNzIElQQWRkcmVzcyBleHRlbmRzIEJhc2VWYWx1ZU9iamVjdDxzdHJpbmc+IHtcclxuICBwcml2YXRlIHN0YXRpYyByZWFkb25seSBJUHY0X1JFR0VYID0gL14oPzooPzoyNVswLTVdfDJbMC00XVswLTldfFswMV0/WzAtOV1bMC05XT8pXFwuKXszfSg/OjI1WzAtNV18MlswLTRdWzAtOV18WzAxXT9bMC05XVswLTldPykkLztcclxuICBwcml2YXRlIHN0YXRpYyByZWFkb25seSBJUHY2X1JFR0VYID0gL14oPzpbMC05YS1mQS1GXXsxLDR9Oil7N31bMC05YS1mQS1GXXsxLDR9JC87XHJcbiAgcHJpdmF0ZSBzdGF0aWMgcmVhZG9ubHkgSVB2Nl9DT01QUkVTU0VEX1JFR0VYID0gL14oKFswLTlhLWZBLUZdezEsNH06KSopPzo6KFswLTlhLWZBLUZdezEsNH06KSpbMC05YS1mQS1GXXsxLDR9JHxeOjokfF46OjEkLztcclxuXHJcbiAgcHJpdmF0ZSByZWFkb25seSBfdHlwZTogSVBBZGRyZXNzVHlwZTtcclxuICBwcml2YXRlIHJlYWRvbmx5IF9vY3RldHM/OiBudW1iZXJbXTsgLy8gRm9yIElQdjRcclxuICBwcml2YXRlIHJlYWRvbmx5IF9zZWdtZW50cz86IHN0cmluZ1tdOyAvLyBGb3IgSVB2NlxyXG5cclxuICBjb25zdHJ1Y3Rvcih2YWx1ZTogc3RyaW5nKSB7XHJcbiAgICBzdXBlcih2YWx1ZSk7XHJcbiAgfVxyXG5cclxuICBwcm90ZWN0ZWQgaW5pdGlhbGl6ZVByb3BlcnRpZXMoKTogdm9pZCB7XHJcbiAgICAodGhpcyBhcyBhbnkpLl90eXBlID0gdGhpcy5kZXRlcm1pbmVUeXBlKCk7XHJcbiAgICBcclxuICAgIGlmICgodGhpcyBhcyBhbnkpLl90eXBlID09PSBJUEFkZHJlc3NUeXBlLklQdjQpIHtcclxuICAgICAgKHRoaXMgYXMgYW55KS5fb2N0ZXRzID0gdGhpcy5wYXJzZUlQdjRPY3RldHMoKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgICh0aGlzIGFzIGFueSkuX3NlZ21lbnRzID0gdGhpcy5wYXJzZUlQdjZTZWdtZW50cygpO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgcHJvdGVjdGVkIHZhbGlkYXRlKCk6IHZvaWQge1xyXG4gICAgaWYgKCF0aGlzLl92YWx1ZSB8fCB0aGlzLl92YWx1ZS50cmltKCkubGVuZ3RoID09PSAwKSB7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcignSVAgYWRkcmVzcyBjYW5ub3QgYmUgZW1wdHknKTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCB0cmltbWVkID0gdGhpcy5fdmFsdWUudHJpbSgpO1xyXG4gICAgXHJcbiAgICBpZiAoIXRoaXMuaXNWYWxpZElQdjQodHJpbW1lZCkgJiYgIXRoaXMuaXNWYWxpZElQdjYodHJpbW1lZCkpIHtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKGBJbnZhbGlkIElQIGFkZHJlc3MgZm9ybWF0OiAke3RyaW1tZWR9YCk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBwcml2YXRlIGRldGVybWluZVR5cGUoKTogSVBBZGRyZXNzVHlwZSB7XHJcbiAgICBpZiAodGhpcy5pc1ZhbGlkSVB2NCh0aGlzLl92YWx1ZSkpIHtcclxuICAgICAgcmV0dXJuIElQQWRkcmVzc1R5cGUuSVB2NDtcclxuICAgIH0gZWxzZSBpZiAodGhpcy5pc1ZhbGlkSVB2Nih0aGlzLl92YWx1ZSkpIHtcclxuICAgICAgcmV0dXJuIElQQWRkcmVzc1R5cGUuSVB2NjtcclxuICAgIH1cclxuICAgIHRocm93IG5ldyBFcnJvcignVW5hYmxlIHRvIGRldGVybWluZSBJUCBhZGRyZXNzIHR5cGUnKTtcclxuICB9XHJcblxyXG4gIHByaXZhdGUgaXNWYWxpZElQdjQoYWRkcmVzczogc3RyaW5nKTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gSVBBZGRyZXNzLklQdjRfUkVHRVgudGVzdChhZGRyZXNzKTtcclxuICB9XHJcblxyXG4gIHByaXZhdGUgaXNWYWxpZElQdjYoYWRkcmVzczogc3RyaW5nKTogYm9vbGVhbiB7XHJcbiAgICAvLyBIYW5kbGUgc3BlY2lhbCBjYXNlcyBmaXJzdFxyXG4gICAgaWYgKGFkZHJlc3MgPT09ICc6OicgfHwgYWRkcmVzcyA9PT0gJzo6MScpIHtcclxuICAgICAgcmV0dXJuIHRydWU7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIC8vIFJlamVjdCBhZGRyZXNzZXMgd2l0aCBtdWx0aXBsZSA6OiBzZXF1ZW5jZXNcclxuICAgIGlmICgoYWRkcmVzcy5tYXRjaCgvOjovZykgfHwgW10pLmxlbmd0aCA+IDEpIHtcclxuICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICAvLyBDaGVjayBmb3IgZnVsbCBJUHY2IGZvcm1hdFxyXG4gICAgaWYgKElQQWRkcmVzcy5JUHY2X1JFR0VYLnRlc3QoYWRkcmVzcykpIHtcclxuICAgICAgcmV0dXJuIHRydWU7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIC8vIENoZWNrIGZvciBjb21wcmVzc2VkIElQdjYgZm9ybWF0XHJcbiAgICBpZiAoSVBBZGRyZXNzLklQdjZfQ09NUFJFU1NFRF9SRUdFWC50ZXN0KGFkZHJlc3MpKSB7XHJcbiAgICAgIHJldHVybiB0cnVlO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICAvLyBBZGRpdGlvbmFsIHZhbGlkYXRpb24gZm9yIGNvbW1vbiBJUHY2IHBhdHRlcm5zXHJcbiAgICBjb25zdCBpcHY2UGF0dGVybiA9IC9eKFswLTlhLWZBLUZdezAsNH06KXsxLDd9WzAtOWEtZkEtRl17MCw0fSR8Xjo6KFswLTlhLWZBLUZdezAsNH06KXswLDZ9WzAtOWEtZkEtRl17MCw0fSR8XihbMC05YS1mQS1GXXswLDR9Oil7MSw2fTo6JHxeKFswLTlhLWZBLUZdezAsNH06KXsxLDZ9OihbMC05YS1mQS1GXXswLDR9Oil7MCw1fVswLTlhLWZBLUZdezAsNH0kLztcclxuICAgIFxyXG4gICAgcmV0dXJuIGlwdjZQYXR0ZXJuLnRlc3QoYWRkcmVzcyk7XHJcbiAgfVxyXG5cclxuICBwcml2YXRlIHBhcnNlSVB2NE9jdGV0cygpOiBudW1iZXJbXSB7XHJcbiAgICByZXR1cm4gdGhpcy5fdmFsdWUuc3BsaXQoJy4nKS5tYXAob2N0ZXQgPT4gcGFyc2VJbnQob2N0ZXQsIDEwKSk7XHJcbiAgfVxyXG5cclxuICBwcml2YXRlIHBhcnNlSVB2NlNlZ21lbnRzKCk6IHN0cmluZ1tdIHtcclxuICAgIC8vIEV4cGFuZCBjb21wcmVzc2VkIElQdjYgYWRkcmVzc2VzXHJcbiAgICBsZXQgZXhwYW5kZWQgPSB0aGlzLl92YWx1ZTtcclxuICAgIGlmIChleHBhbmRlZC5pbmNsdWRlcygnOjonKSkge1xyXG4gICAgICBjb25zdCBwYXJ0cyA9IGV4cGFuZGVkLnNwbGl0KCc6OicpO1xyXG4gICAgICBjb25zdCBsZWZ0UGFydHMgPSBwYXJ0c1swXSA/IHBhcnRzWzBdLnNwbGl0KCc6JykgOiBbXTtcclxuICAgICAgY29uc3QgcmlnaHRQYXJ0cyA9IHBhcnRzWzFdID8gcGFydHNbMV0uc3BsaXQoJzonKSA6IFtdO1xyXG4gICAgICBjb25zdCBtaXNzaW5nUGFydHMgPSA4IC0gbGVmdFBhcnRzLmxlbmd0aCAtIHJpZ2h0UGFydHMubGVuZ3RoO1xyXG4gICAgICBjb25zdCBtaWRkbGVQYXJ0cyA9IEFycmF5KG1pc3NpbmdQYXJ0cykuZmlsbCgnMDAwMCcpO1xyXG4gICAgICBleHBhbmRlZCA9IFsuLi5sZWZ0UGFydHMsIC4uLm1pZGRsZVBhcnRzLCAuLi5yaWdodFBhcnRzXS5qb2luKCc6Jyk7XHJcbiAgICB9XHJcbiAgICByZXR1cm4gZXhwYW5kZWQuc3BsaXQoJzonKS5tYXAoc2VnbWVudCA9PiBzZWdtZW50LnBhZFN0YXJ0KDQsICcwJykpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ3JlYXRlIGFuIElQIGFkZHJlc3MgZnJvbSBzdHJpbmdcclxuICAgKi9cclxuICBzdGF0aWMgZnJvbVN0cmluZyhhZGRyZXNzOiBzdHJpbmcpOiBJUEFkZHJlc3Mge1xyXG4gICAgcmV0dXJuIG5ldyBJUEFkZHJlc3MoYWRkcmVzcyk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDcmVhdGUgYW4gSVB2NCBhZGRyZXNzIGZyb20gb2N0ZXRzXHJcbiAgICovXHJcbiAgc3RhdGljIGZyb21JUHY0T2N0ZXRzKG9jdGV0MTogbnVtYmVyLCBvY3RldDI6IG51bWJlciwgb2N0ZXQzOiBudW1iZXIsIG9jdGV0NDogbnVtYmVyKTogSVBBZGRyZXNzIHtcclxuICAgIGNvbnN0IGFkZHJlc3MgPSBgJHtvY3RldDF9LiR7b2N0ZXQyfS4ke29jdGV0M30uJHtvY3RldDR9YDtcclxuICAgIHJldHVybiBuZXcgSVBBZGRyZXNzKGFkZHJlc3MpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ3JlYXRlIGxvY2FsaG9zdCBJUHY0IGFkZHJlc3NcclxuICAgKi9cclxuICBzdGF0aWMgbG9jYWxob3N0KCk6IElQQWRkcmVzcyB7XHJcbiAgICByZXR1cm4gbmV3IElQQWRkcmVzcygnMTI3LjAuMC4xJyk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDcmVhdGUgbG9jYWxob3N0IElQdjYgYWRkcmVzc1xyXG4gICAqL1xyXG4gIHN0YXRpYyBsb2NhbGhvc3RJUHY2KCk6IElQQWRkcmVzcyB7XHJcbiAgICByZXR1cm4gbmV3IElQQWRkcmVzcygnOjoxJyk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgSVAgYWRkcmVzcyB0eXBlXHJcbiAgICovXHJcbiAgZ2V0IHR5cGUoKTogSVBBZGRyZXNzVHlwZSB7XHJcbiAgICByZXR1cm4gdGhpcy5fdHlwZTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENoZWNrIGlmIHRoaXMgaXMgYW4gSVB2NCBhZGRyZXNzXHJcbiAgICovXHJcbiAgaXNJUHY0KCk6IGJvb2xlYW4ge1xyXG4gICAgcmV0dXJuIHRoaXMuX3R5cGUgPT09IElQQWRkcmVzc1R5cGUuSVB2NDtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENoZWNrIGlmIHRoaXMgaXMgYW4gSVB2NiBhZGRyZXNzXHJcbiAgICovXHJcbiAgaXNJUHY2KCk6IGJvb2xlYW4ge1xyXG4gICAgcmV0dXJuIHRoaXMuX3R5cGUgPT09IElQQWRkcmVzc1R5cGUuSVB2NjtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCBJUHY0IG9jdGV0cyAob25seSBmb3IgSVB2NCBhZGRyZXNzZXMpXHJcbiAgICovXHJcbiAgZ2V0T2N0ZXRzKCk6IG51bWJlcltdIHtcclxuICAgIGlmICghdGhpcy5pc0lQdjQoKSkge1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0Nhbm5vdCBnZXQgb2N0ZXRzIGZvciBub24tSVB2NCBhZGRyZXNzJyk7XHJcbiAgICB9XHJcbiAgICByZXR1cm4gWy4uLnRoaXMuX29jdGV0cyFdO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IElQdjYgc2VnbWVudHMgKG9ubHkgZm9yIElQdjYgYWRkcmVzc2VzKVxyXG4gICAqL1xyXG4gIGdldFNlZ21lbnRzKCk6IHN0cmluZ1tdIHtcclxuICAgIGlmICghdGhpcy5pc0lQdjYoKSkge1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0Nhbm5vdCBnZXQgc2VnbWVudHMgZm9yIG5vbi1JUHY2IGFkZHJlc3MnKTtcclxuICAgIH1cclxuICAgIHJldHVybiBbLi4udGhpcy5fc2VnbWVudHMhXTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENsYXNzaWZ5IHRoZSBJUCBhZGRyZXNzXHJcbiAgICovXHJcbiAgY2xhc3NpZnkoKTogSVBBZGRyZXNzQ2xhc3Mge1xyXG4gICAgaWYgKHRoaXMuaXNJUHY0KCkpIHtcclxuICAgICAgcmV0dXJuIHRoaXMuY2xhc3NpZnlJUHY0KCk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICByZXR1cm4gdGhpcy5jbGFzc2lmeUlQdjYoKTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIHByaXZhdGUgY2xhc3NpZnlJUHY0KCk6IElQQWRkcmVzc0NsYXNzIHtcclxuICAgIGNvbnN0IG9jdGV0cyA9IHRoaXMuX29jdGV0cyE7XHJcbiAgICBjb25zdCBbZmlyc3QsIHNlY29uZCwgdGhpcmQsIGZvdXJ0aF0gPSBvY3RldHM7XHJcblxyXG4gICAgLy8gTG9vcGJhY2sgKDEyNy4wLjAuMC84KVxyXG4gICAgaWYgKGZpcnN0ID09PSAxMjcpIHtcclxuICAgICAgcmV0dXJuIElQQWRkcmVzc0NsYXNzLkxPT1BCQUNLO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIE11bHRpY2FzdCAoMjI0LjAuMC4wLzQpXHJcbiAgICBpZiAoZmlyc3QgPj0gMjI0ICYmIGZpcnN0IDw9IDIzOSkge1xyXG4gICAgICByZXR1cm4gSVBBZGRyZXNzQ2xhc3MuTVVMVElDQVNUO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIEJyb2FkY2FzdFxyXG4gICAgaWYgKGZpcnN0ID09PSAyNTUgJiYgc2Vjb25kID09PSAyNTUgJiYgdGhpcmQgPT09IDI1NSAmJiBmb3VydGggPT09IDI1NSkge1xyXG4gICAgICByZXR1cm4gSVBBZGRyZXNzQ2xhc3MuQlJPQURDQVNUO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIExpbmstbG9jYWwgKDE2OS4yNTQuMC4wLzE2KVxyXG4gICAgaWYgKGZpcnN0ID09PSAxNjkgJiYgc2Vjb25kID09PSAyNTQpIHtcclxuICAgICAgcmV0dXJuIElQQWRkcmVzc0NsYXNzLkxJTktfTE9DQUw7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gUHJpdmF0ZSByYW5nZXNcclxuICAgIGlmIChcclxuICAgICAgZmlyc3QgPT09IDEwIHx8IC8vIDEwLjAuMC4wLzhcclxuICAgICAgKGZpcnN0ID09PSAxNzIgJiYgc2Vjb25kID49IDE2ICYmIHNlY29uZCA8PSAzMSkgfHwgLy8gMTcyLjE2LjAuMC8xMlxyXG4gICAgICAoZmlyc3QgPT09IDE5MiAmJiBzZWNvbmQgPT09IDE2OCkgLy8gMTkyLjE2OC4wLjAvMTZcclxuICAgICkge1xyXG4gICAgICByZXR1cm4gSVBBZGRyZXNzQ2xhc3MuUFJJVkFURTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBSZXNlcnZlZCByYW5nZXNcclxuICAgIGlmIChmaXJzdCA9PT0gMCB8fCBmaXJzdCA+PSAyNDApIHtcclxuICAgICAgcmV0dXJuIElQQWRkcmVzc0NsYXNzLlJFU0VSVkVEO1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiBJUEFkZHJlc3NDbGFzcy5QVUJMSUM7XHJcbiAgfVxyXG5cclxuICBwcml2YXRlIGNsYXNzaWZ5SVB2NigpOiBJUEFkZHJlc3NDbGFzcyB7XHJcbiAgICBjb25zdCBhZGRyZXNzID0gdGhpcy5fdmFsdWUudG9Mb3dlckNhc2UoKTtcclxuXHJcbiAgICAvLyBMb29wYmFjayAoOjoxKVxyXG4gICAgaWYgKGFkZHJlc3MgPT09ICc6OjEnKSB7XHJcbiAgICAgIHJldHVybiBJUEFkZHJlc3NDbGFzcy5MT09QQkFDSztcclxuICAgIH1cclxuXHJcbiAgICAvLyBMaW5rLWxvY2FsIChmZTgwOjovMTApXHJcbiAgICBpZiAoYWRkcmVzcy5zdGFydHNXaXRoKCdmZTgnKSB8fCBhZGRyZXNzLnN0YXJ0c1dpdGgoJ2ZlOScpIHx8IFxyXG4gICAgICAgIGFkZHJlc3Muc3RhcnRzV2l0aCgnZmVhJykgfHwgYWRkcmVzcy5zdGFydHNXaXRoKCdmZWInKSkge1xyXG4gICAgICByZXR1cm4gSVBBZGRyZXNzQ2xhc3MuTElOS19MT0NBTDtcclxuICAgIH1cclxuXHJcbiAgICAvLyBNdWx0aWNhc3QgKGZmMDA6Oi84KVxyXG4gICAgaWYgKGFkZHJlc3Muc3RhcnRzV2l0aCgnZmYnKSkge1xyXG4gICAgICByZXR1cm4gSVBBZGRyZXNzQ2xhc3MuTVVMVElDQVNUO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIFByaXZhdGUvVW5pcXVlIExvY2FsIChmYzAwOjovNylcclxuICAgIGlmIChhZGRyZXNzLnN0YXJ0c1dpdGgoJ2ZjJykgfHwgYWRkcmVzcy5zdGFydHNXaXRoKCdmZCcpKSB7XHJcbiAgICAgIHJldHVybiBJUEFkZHJlc3NDbGFzcy5QUklWQVRFO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIFJlc2VydmVkIChpbmNsdWRpbmcgZG9jdW1lbnRhdGlvbiBhZGRyZXNzZXMpXHJcbiAgICBpZiAoYWRkcmVzcy5zdGFydHNXaXRoKCc6OicpIHx8IGFkZHJlc3Muc3RhcnRzV2l0aCgnMjAwMTpkYjgnKSkge1xyXG4gICAgICByZXR1cm4gSVBBZGRyZXNzQ2xhc3MuUkVTRVJWRUQ7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gQ2hlY2sgZm9yIG90aGVyIHJlc2VydmVkIHJhbmdlc1xyXG4gICAgaWYgKGFkZHJlc3Muc3RhcnRzV2l0aCgnMjAwMTowZGI4JykgfHwgYWRkcmVzcy5zdGFydHNXaXRoKCcyMDAxOmRiOCcpKSB7XHJcbiAgICAgIHJldHVybiBJUEFkZHJlc3NDbGFzcy5SRVNFUlZFRDtcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gSVBBZGRyZXNzQ2xhc3MuUFVCTElDO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2hlY2sgaWYgYWRkcmVzcyBpcyBwcml2YXRlXHJcbiAgICovXHJcbiAgaXNQcml2YXRlKCk6IGJvb2xlYW4ge1xyXG4gICAgcmV0dXJuIHRoaXMuY2xhc3NpZnkoKSA9PT0gSVBBZGRyZXNzQ2xhc3MuUFJJVkFURTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENoZWNrIGlmIGFkZHJlc3MgaXMgcHVibGljXHJcbiAgICovXHJcbiAgaXNQdWJsaWMoKTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gdGhpcy5jbGFzc2lmeSgpID09PSBJUEFkZHJlc3NDbGFzcy5QVUJMSUM7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDaGVjayBpZiBhZGRyZXNzIGlzIGxvb3BiYWNrXHJcbiAgICovXHJcbiAgaXNMb29wYmFjaygpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLmNsYXNzaWZ5KCkgPT09IElQQWRkcmVzc0NsYXNzLkxPT1BCQUNLO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2hlY2sgaWYgYWRkcmVzcyBpcyBtdWx0aWNhc3RcclxuICAgKi9cclxuICBpc011bHRpY2FzdCgpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLmNsYXNzaWZ5KCkgPT09IElQQWRkcmVzc0NsYXNzLk1VTFRJQ0FTVDtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENoZWNrIGlmIGFkZHJlc3MgaXMgaW4gYSBzcGVjaWZpYyBuZXR3b3JrIHJhbmdlXHJcbiAgICovXHJcbiAgaXNJbk5ldHdvcmsobmV0d29ya0FkZHJlc3M6IHN0cmluZywgcHJlZml4TGVuZ3RoOiBudW1iZXIpOiBib29sZWFuIHtcclxuICAgIGlmICh0aGlzLmlzSVB2NCgpKSB7XHJcbiAgICAgIHJldHVybiB0aGlzLmlzSW5JUHY0TmV0d29yayhuZXR3b3JrQWRkcmVzcywgcHJlZml4TGVuZ3RoKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHJldHVybiB0aGlzLmlzSW5JUHY2TmV0d29yayhuZXR3b3JrQWRkcmVzcywgcHJlZml4TGVuZ3RoKTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIHByaXZhdGUgaXNJbklQdjROZXR3b3JrKG5ldHdvcmtBZGRyZXNzOiBzdHJpbmcsIHByZWZpeExlbmd0aDogbnVtYmVyKTogYm9vbGVhbiB7XHJcbiAgICBjb25zdCBuZXR3b3JrSVAgPSBuZXcgSVBBZGRyZXNzKG5ldHdvcmtBZGRyZXNzKTtcclxuICAgIGlmICghbmV0d29ya0lQLmlzSVB2NCgpKSB7XHJcbiAgICAgIHJldHVybiBmYWxzZTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCB0aGlzSW50ID0gdGhpcy50b0lQdjRJbnRlZ2VyKCk7XHJcbiAgICBjb25zdCBuZXR3b3JrSW50ID0gbmV0d29ya0lQLnRvSVB2NEludGVnZXIoKTtcclxuICAgIGNvbnN0IG1hc2sgPSAoMHhGRkZGRkZGRiA8PCAoMzIgLSBwcmVmaXhMZW5ndGgpKSA+Pj4gMDtcclxuXHJcbiAgICByZXR1cm4gKHRoaXNJbnQgJiBtYXNrKSA9PT0gKG5ldHdvcmtJbnQgJiBtYXNrKTtcclxuICB9XHJcblxyXG4gIHByaXZhdGUgaXNJbklQdjZOZXR3b3JrKG5ldHdvcmtBZGRyZXNzOiBzdHJpbmcsIHByZWZpeExlbmd0aDogbnVtYmVyKTogYm9vbGVhbiB7XHJcbiAgICAvLyBTaW1wbGlmaWVkIElQdjYgbmV0d29yayBjaGVja2luZ1xyXG4gICAgY29uc3QgbmV0d29ya0lQID0gbmV3IElQQWRkcmVzcyhuZXR3b3JrQWRkcmVzcyk7XHJcbiAgICBpZiAoIW5ldHdvcmtJUC5pc0lQdjYoKSkge1xyXG4gICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgdGhpc1NlZ21lbnRzID0gdGhpcy5nZXRTZWdtZW50cygpO1xyXG4gICAgY29uc3QgbmV0d29ya1NlZ21lbnRzID0gbmV0d29ya0lQLmdldFNlZ21lbnRzKCk7XHJcbiAgICBcclxuICAgIGNvbnN0IGJpdHNUb0NoZWNrID0gcHJlZml4TGVuZ3RoO1xyXG4gICAgbGV0IGNoZWNrZWRCaXRzID0gMDtcclxuXHJcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IDggJiYgY2hlY2tlZEJpdHMgPCBiaXRzVG9DaGVjazsgaSsrKSB7XHJcbiAgICAgIGNvbnN0IHRoaXNTZWdtZW50SW50ID0gcGFyc2VJbnQodGhpc1NlZ21lbnRzW2ldLCAxNik7XHJcbiAgICAgIGNvbnN0IG5ldHdvcmtTZWdtZW50SW50ID0gcGFyc2VJbnQobmV0d29ya1NlZ21lbnRzW2ldLCAxNik7XHJcbiAgICAgIFxyXG4gICAgICBjb25zdCByZW1haW5pbmdCaXRzID0gTWF0aC5taW4oMTYsIGJpdHNUb0NoZWNrIC0gY2hlY2tlZEJpdHMpO1xyXG4gICAgICBjb25zdCBtYXNrID0gKDB4RkZGRiA8PCAoMTYgLSByZW1haW5pbmdCaXRzKSkgJiAweEZGRkY7XHJcbiAgICAgIFxyXG4gICAgICBpZiAoKHRoaXNTZWdtZW50SW50ICYgbWFzaykgIT09IChuZXR3b3JrU2VnbWVudEludCAmIG1hc2spKSB7XHJcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICBjaGVja2VkQml0cyArPSAxNjtcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gdHJ1ZTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENvbnZlcnQgSVB2NCB0byAzMi1iaXQgaW50ZWdlclxyXG4gICAqL1xyXG4gIHRvSVB2NEludGVnZXIoKTogbnVtYmVyIHtcclxuICAgIGlmICghdGhpcy5pc0lQdjQoKSkge1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0Nhbm5vdCBjb252ZXJ0IG5vbi1JUHY0IGFkZHJlc3MgdG8gaW50ZWdlcicpO1xyXG4gICAgfVxyXG4gICAgY29uc3Qgb2N0ZXRzID0gdGhpcy5fb2N0ZXRzITtcclxuICAgIHJldHVybiAob2N0ZXRzWzBdIDw8IDI0KSArIChvY3RldHNbMV0gPDwgMTYpICsgKG9jdGV0c1syXSA8PCA4KSArIG9jdGV0c1szXTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCByZXZlcnNlIEROUyBsb29rdXAgZm9ybWF0XHJcbiAgICovXHJcbiAgZ2V0UmV2ZXJzZUROU0Zvcm1hdCgpOiBzdHJpbmcge1xyXG4gICAgaWYgKHRoaXMuaXNJUHY0KCkpIHtcclxuICAgICAgY29uc3Qgb2N0ZXRzID0gdGhpcy5fb2N0ZXRzITtcclxuICAgICAgcmV0dXJuIGAke29jdGV0c1szXX0uJHtvY3RldHNbMl19LiR7b2N0ZXRzWzFdfS4ke29jdGV0c1swXX0uaW4tYWRkci5hcnBhYDtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIC8vIElQdjYgcmV2ZXJzZSBETlMgaXMgbW9yZSBjb21wbGV4LCBzaW1wbGlmaWVkIGltcGxlbWVudGF0aW9uXHJcbiAgICAgIGNvbnN0IHNlZ21lbnRzID0gdGhpcy5nZXRTZWdtZW50cygpO1xyXG4gICAgICBjb25zdCBjaGFycyA9IHNlZ21lbnRzLmpvaW4oJycpLnNwbGl0KCcnKS5yZXZlcnNlKCk7XHJcbiAgICAgIHJldHVybiBjaGFycy5qb2luKCcuJykgKyAnLmlwNi5hcnBhJztcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCBuZXR3b3JrIGFkZHJlc3MgZm9yIGdpdmVuIHByZWZpeCBsZW5ndGhcclxuICAgKi9cclxuICBnZXROZXR3b3JrQWRkcmVzcyhwcmVmaXhMZW5ndGg6IG51bWJlcik6IElQQWRkcmVzcyB7XHJcbiAgICBpZiAodGhpcy5pc0lQdjQoKSkge1xyXG4gICAgICBjb25zdCB0aGlzSW50ID0gdGhpcy50b0lQdjRJbnRlZ2VyKCk7XHJcbiAgICAgIGNvbnN0IG1hc2sgPSAoMHhGRkZGRkZGRiA8PCAoMzIgLSBwcmVmaXhMZW5ndGgpKSA+Pj4gMDtcclxuICAgICAgY29uc3QgbmV0d29ya0ludCA9IHRoaXNJbnQgJiBtYXNrO1xyXG4gICAgICBcclxuICAgICAgY29uc3Qgb2N0ZXQxID0gKG5ldHdvcmtJbnQgPj4+IDI0KSAmIDB4RkY7XHJcbiAgICAgIGNvbnN0IG9jdGV0MiA9IChuZXR3b3JrSW50ID4+PiAxNikgJiAweEZGO1xyXG4gICAgICBjb25zdCBvY3RldDMgPSAobmV0d29ya0ludCA+Pj4gOCkgJiAweEZGO1xyXG4gICAgICBjb25zdCBvY3RldDQgPSBuZXR3b3JrSW50ICYgMHhGRjtcclxuICAgICAgXHJcbiAgICAgIHJldHVybiBJUEFkZHJlc3MuZnJvbUlQdjRPY3RldHMob2N0ZXQxLCBvY3RldDIsIG9jdGV0Mywgb2N0ZXQ0KTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIC8vIFNpbXBsaWZpZWQgSVB2NiBuZXR3b3JrIGFkZHJlc3MgY2FsY3VsYXRpb25cclxuICAgICAgdGhyb3cgbmV3IEVycm9yKCdJUHY2IG5ldHdvcmsgYWRkcmVzcyBjYWxjdWxhdGlvbiBub3QgaW1wbGVtZW50ZWQnKTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENoZWNrIGlmIHRoaXMgSVAgYWRkcmVzcyBjb3VsZCBiZSBzdXNwaWNpb3VzXHJcbiAgICovXHJcbiAgaXNTdXNwaWNpb3VzKCk6IGJvb2xlYW4ge1xyXG4gICAgLy8gQmFzaWMgaGV1cmlzdGljcyBmb3Igc3VzcGljaW91cyBJUCBhZGRyZXNzZXNcclxuICAgIGlmICh0aGlzLmlzUHJpdmF0ZSgpIHx8IHRoaXMuaXNMb29wYmFjaygpKSB7XHJcbiAgICAgIHJldHVybiBmYWxzZTsgLy8gSW50ZXJuYWwgYWRkcmVzc2VzIGFyZSBnZW5lcmFsbHkgbm90IHN1c3BpY2lvdXNcclxuICAgIH1cclxuXHJcbiAgICBpZiAodGhpcy5pc0lQdjQoKSkge1xyXG4gICAgICBjb25zdCBvY3RldHMgPSB0aGlzLl9vY3RldHMhO1xyXG4gICAgICBcclxuICAgICAgLy8gQ2hlY2sgZm9yIGNvbW1vbiBzdXNwaWNpb3VzIHBhdHRlcm5zXHJcbiAgICAgIC8vIEFsbCBvY3RldHMgdGhlIHNhbWUgKGJ1dCBleGNsdWRlIGNvbW1vbiBwdWJsaWMgRE5TIGxpa2UgOC44LjguOClcclxuICAgICAgaWYgKG9jdGV0cy5ldmVyeShvY3RldCA9PiBvY3RldCA9PT0gb2N0ZXRzWzBdKSAmJiBvY3RldHNbMF0gIT09IDgpIHtcclxuICAgICAgICByZXR1cm4gdHJ1ZTtcclxuICAgICAgfVxyXG4gICAgICBcclxuICAgICAgLy8gU2VxdWVudGlhbCBvY3RldHNcclxuICAgICAgaWYgKG9jdGV0c1sxXSA9PT0gb2N0ZXRzWzBdICsgMSAmJiBvY3RldHNbMl0gPT09IG9jdGV0c1sxXSArIDEgJiYgb2N0ZXRzWzNdID09PSBvY3RldHNbMl0gKyAxKSB7XHJcbiAgICAgICAgcmV0dXJuIHRydWU7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gZmFsc2U7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgZ2VvZ3JhcGhpYyByZWdpb24gaGludCBiYXNlZCBvbiBJUCByYW5nZXNcclxuICAgKi9cclxuICBnZXRSZWdpb25IaW50KCk6IHN0cmluZyB8IG51bGwge1xyXG4gICAgaWYgKCF0aGlzLmlzUHVibGljKCkpIHtcclxuICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gVGhpcyBpcyBhIHNpbXBsaWZpZWQgaW1wbGVtZW50YXRpb25cclxuICAgIC8vIEluIHByYWN0aWNlLCB5b3Ugd291bGQgdXNlIGEgR2VvSVAgZGF0YWJhc2VcclxuICAgIGlmICh0aGlzLmlzSVB2NCgpKSB7XHJcbiAgICAgIGNvbnN0IGZpcnN0T2N0ZXQgPSB0aGlzLl9vY3RldHMhWzBdO1xyXG4gICAgICBcclxuICAgICAgLy8gVmVyeSBiYXNpYyByZWdpb25hbCBoaW50cyBiYXNlZCBvbiBmaXJzdCBvY3RldFxyXG4gICAgICBpZiAoZmlyc3RPY3RldCA+PSAxICYmIGZpcnN0T2N0ZXQgPD0gMTI2KSByZXR1cm4gJ05vcnRoIEFtZXJpY2EnO1xyXG4gICAgICBpZiAoZmlyc3RPY3RldCA+PSAxMjggJiYgZmlyc3RPY3RldCA8PSAxOTEpIHJldHVybiAnRXVyb3BlL0FzaWEnO1xyXG4gICAgICBpZiAoZmlyc3RPY3RldCA+PSAxOTIgJiYgZmlyc3RPY3RldCA8PSAyMjMpIHJldHVybiAnQXNpYSBQYWNpZmljJztcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gbnVsbDtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENvbXBhcmUgSVAgYWRkcmVzc2VzIGZvciBlcXVhbGl0eVxyXG4gICAqL1xyXG4gIHB1YmxpYyBlcXVhbHMob3RoZXI/OiBJUEFkZHJlc3MpOiBib29sZWFuIHtcclxuICAgIGlmICghb3RoZXIpIHtcclxuICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgfVxyXG5cclxuICAgIGlmICh0aGlzID09PSBvdGhlcikge1xyXG4gICAgICByZXR1cm4gdHJ1ZTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBOb3JtYWxpemUgYm90aCBhZGRyZXNzZXMgZm9yIGNvbXBhcmlzb25cclxuICAgIHJldHVybiB0aGlzLl92YWx1ZS50b0xvd2VyQ2FzZSgpID09PSBvdGhlci5fdmFsdWUudG9Mb3dlckNhc2UoKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCBzdHJpbmcgcmVwcmVzZW50YXRpb25cclxuICAgKi9cclxuICBwdWJsaWMgdG9TdHJpbmcoKTogc3RyaW5nIHtcclxuICAgIHJldHVybiB0aGlzLl92YWx1ZTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENvbnZlcnQgdG8gSlNPTiByZXByZXNlbnRhdGlvblxyXG4gICAqL1xyXG4gIHB1YmxpYyB0b0pTT04oKTogUmVjb3JkPHN0cmluZywgYW55PiB7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBhZGRyZXNzOiB0aGlzLl92YWx1ZSxcclxuICAgICAgdHlwZTogdGhpcy5fdHlwZSxcclxuICAgICAgY2xhc3NpZmljYXRpb246IHRoaXMuY2xhc3NpZnkoKSxcclxuICAgICAgaXNQcml2YXRlOiB0aGlzLmlzUHJpdmF0ZSgpLFxyXG4gICAgICBpc1B1YmxpYzogdGhpcy5pc1B1YmxpYygpLFxyXG4gICAgICBpc0xvb3BiYWNrOiB0aGlzLmlzTG9vcGJhY2soKSxcclxuICAgICAgaXNNdWx0aWNhc3Q6IHRoaXMuaXNNdWx0aWNhc3QoKSxcclxuICAgICAgaXNTdXNwaWNpb3VzOiB0aGlzLmlzU3VzcGljaW91cygpLFxyXG4gICAgICByZWdpb25IaW50OiB0aGlzLmdldFJlZ2lvbkhpbnQoKSxcclxuICAgICAgcmV2ZXJzZUROUzogdGhpcy5nZXRSZXZlcnNlRE5TRm9ybWF0KCksXHJcbiAgICB9O1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ3JlYXRlIElQQWRkcmVzcyBmcm9tIEpTT05cclxuICAgKi9cclxuICBzdGF0aWMgZnJvbUpTT04oanNvbjogUmVjb3JkPHN0cmluZywgYW55Pik6IElQQWRkcmVzcyB7XHJcbiAgICByZXR1cm4gbmV3IElQQWRkcmVzcyhqc29uLmFkZHJlc3MpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogVmFsaWRhdGUgSVAgYWRkcmVzcyBmb3JtYXQgd2l0aG91dCBjcmVhdGluZyBpbnN0YW5jZVxyXG4gICAqL1xyXG4gIHN0YXRpYyBpc1ZhbGlkKGFkZHJlc3M6IHN0cmluZyk6IGJvb2xlYW4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgbmV3IElQQWRkcmVzcyhhZGRyZXNzKTtcclxuICAgICAgcmV0dXJuIHRydWU7XHJcbiAgICB9IGNhdGNoIHtcclxuICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG4iXSwidmVyc2lvbiI6M30=