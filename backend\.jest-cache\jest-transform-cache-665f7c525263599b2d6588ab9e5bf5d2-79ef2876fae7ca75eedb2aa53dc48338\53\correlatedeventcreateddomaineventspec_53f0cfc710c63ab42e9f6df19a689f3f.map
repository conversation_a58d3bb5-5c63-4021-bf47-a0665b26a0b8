{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\correlated-event-created.domain-event.spec.ts", "mappings": ";;AAAA,oGAGkD;AAClD,gEAA8D;AAC9D,iEAAwD;AACxD,yEAAgE;AAChE,iFAAwE;AACxE,6EAAoE;AAEpE,QAAQ,CAAC,mCAAmC,EAAE,GAAG,EAAE;IACjD,IAAI,SAA0C,CAAC;IAC/C,IAAI,WAA2B,CAAC;IAEhC,UAAU,CAAC,GAAG,EAAE;QACd,WAAW,GAAG,8BAAc,CAAC,MAAM,EAAE,CAAC;QACtC,SAAS,GAAG;YACV,eAAe,EAAE,8BAAc,CAAC,MAAM,EAAE;YACxC,SAAS,EAAE,2BAAS,CAAC,cAAc;YACnC,QAAQ,EAAE,mCAAa,CAAC,IAAI;YAC5B,iBAAiB,EAAE,2CAAiB,CAAC,SAAS;YAC9C,uBAAuB,EAAE,EAAE;YAC3B,iBAAiB,EAAE,CAAC;YACpB,uBAAuB,EAAE,CAAC;YAC1B,kBAAkB,EAAE,CAAC;YACrB,eAAe,EAAE,uCAAe,CAAC,IAAI;YACrC,cAAc,EAAE,IAAI;YACpB,oBAAoB,EAAE,KAAK;SAC5B,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,WAAW,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAElF,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YACrD,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACjD,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YACxE,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,aAAa,GAAG,8BAAc,CAAC,MAAM,EAAE,CAAC;YAC9C,MAAM,gBAAgB,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAC1D,MAAM,aAAa,GAAG,UAAU,CAAC;YAEjC,MAAM,WAAW,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE,SAAS,EAAE;gBAChF,OAAO,EAAE,aAAa;gBACtB,UAAU,EAAE,gBAAgB;gBAC5B,aAAa;gBACb,YAAY,EAAE,CAAC;gBACf,QAAQ,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE;aAC7B,CAAC,CAAC;YAEH,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YACnD,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YACzD,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACtD,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,IAAI,WAA8C,CAAC;QAEnD,UAAU,CAAC,GAAG,EAAE;YACd,WAAW,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wBAAwB,EAAE,GAAG,EAAE;YAChC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,CAAC,WAAW,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;QACtF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,CAAC,WAAW,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;QACtF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,IAAI,WAA8C,CAAC;QAEnD,UAAU,CAAC,GAAG,EAAE;YACd,WAAW,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,iBAAiB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,QAAQ,EAAE,mCAAa,CAAC,IAAI;aAC7B,CAAC,CAAC;YAEH,MAAM,qBAAqB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAC/E,GAAG,SAAS;gBACZ,QAAQ,EAAE,mCAAa,CAAC,QAAQ;aACjC,CAAC,CAAC;YAEH,MAAM,mBAAmB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAC7E,GAAG,SAAS;gBACZ,QAAQ,EAAE,mCAAa,CAAC,MAAM;aAC/B,CAAC,CAAC;YAEH,MAAM,CAAC,iBAAiB,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,qBAAqB,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1D,MAAM,CAAC,mBAAmB,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,aAAa,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,QAAQ,EAAE,mCAAa,CAAC,QAAQ;aACjC,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACnE,GAAG,SAAS;gBACZ,QAAQ,EAAE,mCAAa,CAAC,IAAI;aAC7B,CAAC,CAAC;YAEH,MAAM,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,gBAAgB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAC1E,GAAG,SAAS;gBACZ,uBAAuB,EAAE,EAAE;aAC5B,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACzE,GAAG,SAAS;gBACZ,uBAAuB,EAAE,EAAE;aAC5B,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACxE,GAAG,SAAS;gBACZ,uBAAuB,EAAE,SAAS;aACnC,CAAC,CAAC;YAEH,MAAM,CAAC,gBAAgB,CAAC,yBAAyB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChE,MAAM,CAAC,eAAe,CAAC,yBAAyB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChE,MAAM,CAAC,cAAc,CAAC,yBAAyB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,cAAc,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACxE,GAAG,SAAS;gBACZ,iBAAiB,EAAE,2CAAiB,CAAC,SAAS;aAC/C,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,iBAAiB,EAAE,2CAAiB,CAAC,OAAO;aAC7C,CAAC,CAAC;YAEH,MAAM,CAAC,cAAc,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3D,MAAM,CAAC,YAAY,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,mBAAmB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAC7E,GAAG,SAAS;gBACZ,eAAe,EAAE,uCAAe,CAAC,IAAI;aACtC,CAAC,CAAC;YAEH,MAAM,uBAAuB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACjF,GAAG,SAAS;gBACZ,eAAe,EAAE,uCAAe,CAAC,SAAS;aAC3C,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACxE,GAAG,SAAS;gBACZ,eAAe,EAAE,uCAAe,CAAC,SAAS;aAC3C,CAAC,CAAC;YAEH,MAAM,qBAAqB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAC/E,GAAG,SAAS;gBACZ,eAAe,EAAE,uCAAe,CAAC,MAAM;aACxC,CAAC,CAAC;YAEH,MAAM,CAAC,mBAAmB,CAAC,2BAA2B,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrE,MAAM,CAAC,uBAAuB,CAAC,2BAA2B,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzE,MAAM,CAAC,cAAc,CAAC,2BAA2B,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChE,MAAM,CAAC,qBAAqB,CAAC,2BAA2B,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,oBAAoB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAC9E,GAAG,SAAS;gBACZ,kBAAkB,EAAE,CAAC;aACtB,CAAC,CAAC;YAEH,MAAM,kBAAkB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAC5E,GAAG,SAAS;gBACZ,kBAAkB,EAAE,CAAC;aACtB,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACxE,GAAG,SAAS;gBACZ,kBAAkB,EAAE,CAAC;aACtB,CAAC,CAAC;YAEH,MAAM,CAAC,oBAAoB,CAAC,wBAAwB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnE,MAAM,CAAC,kBAAkB,CAAC,wBAAwB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClE,MAAM,CAAC,cAAc,CAAC,wBAAwB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,gBAAgB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAC1E,GAAG,SAAS;gBACZ,uBAAuB,EAAE,CAAC;aAC3B,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACxE,GAAG,SAAS;gBACZ,uBAAuB,EAAE,CAAC;aAC3B,CAAC,CAAC;YAEH,MAAM,CAAC,gBAAgB,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,cAAc,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,mBAAmB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAC7E,GAAG,SAAS;gBACZ,cAAc,EAAE,IAAI;gBACpB,kBAAkB,EAAE,CAAC;gBACrB,eAAe,EAAE,uCAAe,CAAC,IAAI;aACtC,CAAC,CAAC;YAEH,MAAM,gBAAgB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAC1E,GAAG,SAAS;gBACZ,cAAc,EAAE,KAAK;gBACrB,kBAAkB,EAAE,CAAC;gBACrB,eAAe,EAAE,uCAAe,CAAC,GAAG;aACrC,CAAC,CAAC;YAEH,MAAM,CAAC,mBAAmB,CAAC,yBAAyB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnE,MAAM,CAAC,gBAAgB,CAAC,yBAAyB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sCAAsC,EAAE,GAAG,EAAE;QACpD,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,aAAa,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,QAAQ,EAAE,mCAAa,CAAC,QAAQ;gBAChC,cAAc,EAAE,IAAI;gBACpB,kBAAkB,EAAE,CAAC;gBACrB,eAAe,EAAE,uCAAe,CAAC,IAAI;aACtC,CAAC,CAAC;YAEH,MAAM,CAAC,aAAa,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oEAAoE,EAAE,GAAG,EAAE;YAC5E,MAAM,iBAAiB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,QAAQ,EAAE,mCAAa,CAAC,IAAI;gBAC5B,cAAc,EAAE,IAAI;gBACpB,kBAAkB,EAAE,CAAC;gBACrB,eAAe,EAAE,uCAAe,CAAC,MAAM;aACxC,CAAC,CAAC;YAEH,MAAM,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uEAAuE,EAAE,GAAG,EAAE;YAC/E,MAAM,mBAAmB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAC7E,GAAG,SAAS;gBACZ,QAAQ,EAAE,mCAAa,CAAC,MAAM;gBAC9B,cAAc,EAAE,KAAK;gBACrB,uBAAuB,EAAE,EAAE;gBAC3B,uBAAuB,EAAE,CAAC;aAC3B,CAAC,CAAC;YAEH,MAAM,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,gBAAgB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAC1E,GAAG,SAAS;gBACZ,QAAQ,EAAE,mCAAa,CAAC,GAAG;gBAC3B,cAAc,EAAE,KAAK;gBACrB,uBAAuB,EAAE,EAAE;gBAC3B,uBAAuB,EAAE,CAAC;aAC3B,CAAC,CAAC;YAEH,MAAM,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,aAAa,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,QAAQ,EAAE,mCAAa,CAAC,QAAQ;gBAChC,cAAc,EAAE,IAAI;gBACpB,kBAAkB,EAAE,CAAC;gBACrB,eAAe,EAAE,uCAAe,CAAC,IAAI;aACtC,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACpE,GAAG,SAAS;gBACZ,QAAQ,EAAE,mCAAa,CAAC,IAAI;gBAC5B,cAAc,EAAE,IAAI;aACrB,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,QAAQ,EAAE,mCAAa,CAAC,MAAM;gBAC9B,cAAc,EAAE,KAAK;gBACrB,eAAe,EAAE,uCAAe,CAAC,IAAI;aACtC,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACnE,GAAG,SAAS;gBACZ,QAAQ,EAAE,mCAAa,CAAC,GAAG;gBAC3B,cAAc,EAAE,KAAK;gBACrB,eAAe,EAAE,uCAAe,CAAC,GAAG;aACrC,CAAC,CAAC;YAEH,MAAM,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACvD,MAAM,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACjD,MAAM,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACrD,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,aAAa,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,QAAQ,EAAE,mCAAa,CAAC,QAAQ;gBAChC,cAAc,EAAE,IAAI;gBACpB,kBAAkB,EAAE,CAAC;gBACrB,eAAe,EAAE,uCAAe,CAAC,IAAI;aACtC,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,aAAa,CAAC,yBAAyB,EAAE,CAAC;YAE7D,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACtD,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC3C,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wEAAwE,EAAE,GAAG,EAAE;YAChF,MAAM,aAAa,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,QAAQ,EAAE,mCAAa,CAAC,QAAQ;gBAChC,cAAc,EAAE,IAAI;gBACpB,kBAAkB,EAAE,CAAC;gBACrB,eAAe,EAAE,uCAAe,CAAC,MAAM;aACxC,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,aAAa,CAAC,yBAAyB,EAAE,CAAC;YAE7D,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACjD,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC3C,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wEAAwE,EAAE,GAAG,EAAE;YAChF,MAAM,iBAAiB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,QAAQ,EAAE,mCAAa,CAAC,IAAI;gBAC5B,cAAc,EAAE,KAAK;gBACrB,eAAe,EAAE,uCAAe,CAAC,IAAI;aACtC,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,iBAAiB,CAAC,yBAAyB,EAAE,CAAC;YAEjE,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACjD,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC3C,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sEAAsE,EAAE,GAAG,EAAE;YAC9E,MAAM,YAAY,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,QAAQ,EAAE,mCAAa,CAAC,MAAM;gBAC9B,cAAc,EAAE,KAAK;gBACrB,uBAAuB,EAAE,EAAE;gBAC3B,uBAAuB,EAAE,CAAC;gBAC1B,eAAe,EAAE,uCAAe,CAAC,MAAM;aACxC,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,YAAY,CAAC,yBAAyB,EAAE,CAAC;YAE5D,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACjD,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC5C,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,aAAa,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,QAAQ,EAAE,mCAAa,CAAC,GAAG;gBAC3B,cAAc,EAAE,KAAK;gBACrB,uBAAuB,EAAE,EAAE;gBAC3B,uBAAuB,EAAE,CAAC;gBAC1B,eAAe,EAAE,uCAAe,CAAC,GAAG;aACrC,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,aAAa,CAAC,yBAAyB,EAAE,CAAC;YAE7D,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACjD,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC5C,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,aAAa,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,QAAQ,EAAE,mCAAa,CAAC,QAAQ;gBAChC,cAAc,EAAE,IAAI;gBACpB,kBAAkB,EAAE,CAAC;gBACrB,eAAe,EAAE,uCAAe,CAAC,IAAI;aACtC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,aAAa,CAAC,qBAAqB,EAAE,CAAC;YAEtD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,uCAAuC,CAAC,CAAC;YACnE,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,mCAAmC,CAAC,CAAC;YAC/D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sEAAsE,EAAE,GAAG,EAAE;YAC9E,MAAM,gBAAgB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAC1E,GAAG,SAAS;gBACZ,cAAc,EAAE,IAAI;aACrB,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,gBAAgB,CAAC,qBAAqB,EAAE,CAAC;YAEzD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC;YAC9D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,wCAAwC,CAAC,CAAC;YACpE,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,mCAAmC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;YACtE,MAAM,mBAAmB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAC7E,GAAG,SAAS;gBACZ,eAAe,EAAE,uCAAe,CAAC,IAAI;aACtC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,mBAAmB,CAAC,qBAAqB,EAAE,CAAC;YAE5D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAC;YAC3D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;YACxD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,WAAW,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBACrE,GAAG,SAAS;gBACZ,oBAAoB,EAAE,IAAI;aAC3B,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,WAAW,CAAC,qBAAqB,EAAE,CAAC;YAEpD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;YACxD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,6CAA6C,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,oBAAoB,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE;gBAC9E,GAAG,SAAS;gBACZ,kBAAkB,EAAE,CAAC;aACtB,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,oBAAoB,CAAC,qBAAqB,EAAE,CAAC;YAE7D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;YACxD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC;YAC9D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,WAAW,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAClF,MAAM,OAAO,GAAG,WAAW,CAAC,eAAe,EAAE,CAAC;YAE9C,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC/D,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC3E,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;YACpE,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;YAChF,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;YACpE,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;YAChF,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;YACtE,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAChE,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YAC9D,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;YAC1E,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvC,MAAM,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACzC,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACzD,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,WAAW,EAAE,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,WAAW,GAAG,IAAI,yEAAiC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAClF,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC;YAElC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YACjE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\correlated-event-created.domain-event.spec.ts"], "sourcesContent": ["import { \r\n  CorrelatedEventCreatedDomainEvent, \r\n  CorrelatedEventCreatedEventData \r\n} from '../correlated-event-created.domain-event';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel';\r\nimport { EventType } from '../../enums/event-type.enum';\r\nimport { EventSeverity } from '../../enums/event-severity.enum';\r\nimport { CorrelationStatus } from '../../enums/correlation-status.enum';\r\nimport { ConfidenceLevel } from '../../enums/confidence-level.enum';\r\n\r\ndescribe('CorrelatedEventCreatedDomainEvent', () => {\r\n  let eventData: CorrelatedEventCreatedEventData;\r\n  let aggregateId: UniqueEntityId;\r\n\r\n  beforeEach(() => {\r\n    aggregateId = UniqueEntityId.create();\r\n    eventData = {\r\n      enrichedEventId: UniqueEntityId.create(),\r\n      eventType: EventType.SECURITY_ALERT,\r\n      severity: EventSeverity.HIGH,\r\n      correlationStatus: CorrelationStatus.COMPLETED,\r\n      correlationQualityScore: 85,\r\n      appliedRulesCount: 3,\r\n      correlationMatchesCount: 5,\r\n      relatedEventsCount: 7,\r\n      confidenceLevel: ConfidenceLevel.HIGH,\r\n      hasAttackChain: true,\r\n      requiresManualReview: false\r\n    };\r\n  });\r\n\r\n  describe('creation', () => {\r\n    it('should create domain event with required data', () => {\r\n      const domainEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, eventData);\r\n\r\n      expect(domainEvent.aggregateId).toEqual(aggregateId);\r\n      expect(domainEvent.eventData).toEqual(eventData);\r\n      expect(domainEvent.eventName).toBe('CorrelatedEventCreatedDomainEvent');\r\n      expect(domainEvent.occurredOn).toBeInstanceOf(Date);\r\n      expect(domainEvent.eventId).toBeDefined();\r\n    });\r\n\r\n    it('should create domain event with custom options', () => {\r\n      const customEventId = UniqueEntityId.create();\r\n      const customOccurredOn = new Date('2023-01-01T00:00:00Z');\r\n      const correlationId = 'corr_123';\r\n\r\n      const domainEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, eventData, {\r\n        eventId: customEventId,\r\n        occurredOn: customOccurredOn,\r\n        correlationId,\r\n        eventVersion: 2,\r\n        metadata: { custom: 'data' }\r\n      });\r\n\r\n      expect(domainEvent.eventId).toEqual(customEventId);\r\n      expect(domainEvent.occurredOn).toEqual(customOccurredOn);\r\n      expect(domainEvent.correlationId).toBe(correlationId);\r\n      expect(domainEvent.eventVersion).toBe(2);\r\n      expect(domainEvent.metadata).toEqual({ custom: 'data' });\r\n    });\r\n  });\r\n\r\n  describe('property getters', () => {\r\n    let domainEvent: CorrelatedEventCreatedDomainEvent;\r\n\r\n    beforeEach(() => {\r\n      domainEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, eventData);\r\n    });\r\n\r\n    it('should return enriched event ID', () => {\r\n      expect(domainEvent.enrichedEventId).toEqual(eventData.enrichedEventId);\r\n    });\r\n\r\n    it('should return event type', () => {\r\n      expect(domainEvent.eventType).toBe(eventData.eventType);\r\n    });\r\n\r\n    it('should return severity', () => {\r\n      expect(domainEvent.severity).toBe(eventData.severity);\r\n    });\r\n\r\n    it('should return correlation status', () => {\r\n      expect(domainEvent.correlationStatus).toBe(eventData.correlationStatus);\r\n    });\r\n\r\n    it('should return correlation quality score', () => {\r\n      expect(domainEvent.correlationQualityScore).toBe(eventData.correlationQualityScore);\r\n    });\r\n\r\n    it('should return applied rules count', () => {\r\n      expect(domainEvent.appliedRulesCount).toBe(eventData.appliedRulesCount);\r\n    });\r\n\r\n    it('should return correlation matches count', () => {\r\n      expect(domainEvent.correlationMatchesCount).toBe(eventData.correlationMatchesCount);\r\n    });\r\n\r\n    it('should return related events count', () => {\r\n      expect(domainEvent.relatedEventsCount).toBe(eventData.relatedEventsCount);\r\n    });\r\n\r\n    it('should return confidence level', () => {\r\n      expect(domainEvent.confidenceLevel).toBe(eventData.confidenceLevel);\r\n    });\r\n\r\n    it('should return has attack chain flag', () => {\r\n      expect(domainEvent.hasAttackChain).toBe(eventData.hasAttackChain);\r\n    });\r\n\r\n    it('should return requires manual review flag', () => {\r\n      expect(domainEvent.requiresManualReview).toBe(eventData.requiresManualReview);\r\n    });\r\n  });\r\n\r\n  describe('business logic methods', () => {\r\n    let domainEvent: CorrelatedEventCreatedDomainEvent;\r\n\r\n    beforeEach(() => {\r\n      domainEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, eventData);\r\n    });\r\n\r\n    it('should identify high severity events', () => {\r\n      const highSeverityEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        severity: EventSeverity.HIGH\r\n      });\r\n\r\n      const criticalSeverityEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        severity: EventSeverity.CRITICAL\r\n      });\r\n\r\n      const mediumSeverityEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        severity: EventSeverity.MEDIUM\r\n      });\r\n\r\n      expect(highSeverityEvent.isHighSeverity()).toBe(true);\r\n      expect(criticalSeverityEvent.isHighSeverity()).toBe(true);\r\n      expect(mediumSeverityEvent.isHighSeverity()).toBe(false);\r\n    });\r\n\r\n    it('should identify critical events', () => {\r\n      const criticalEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        severity: EventSeverity.CRITICAL\r\n      });\r\n\r\n      const highEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        severity: EventSeverity.HIGH\r\n      });\r\n\r\n      expect(criticalEvent.isCritical()).toBe(true);\r\n      expect(highEvent.isCritical()).toBe(false);\r\n    });\r\n\r\n    it('should identify high correlation quality', () => {\r\n      const highQualityEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        correlationQualityScore: 85\r\n      });\r\n\r\n      const lowQualityEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        correlationQualityScore: 50\r\n      });\r\n\r\n      const noQualityEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        correlationQualityScore: undefined\r\n      });\r\n\r\n      expect(highQualityEvent.hasHighCorrelationQuality()).toBe(true);\r\n      expect(lowQualityEvent.hasHighCorrelationQuality()).toBe(false);\r\n      expect(noQualityEvent.hasHighCorrelationQuality()).toBe(false);\r\n    });\r\n\r\n    it('should identify completed correlation', () => {\r\n      const completedEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        correlationStatus: CorrelationStatus.COMPLETED\r\n      });\r\n\r\n      const pendingEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        correlationStatus: CorrelationStatus.PENDING\r\n      });\r\n\r\n      expect(completedEvent.isCorrelationCompleted()).toBe(true);\r\n      expect(pendingEvent.isCorrelationCompleted()).toBe(false);\r\n    });\r\n\r\n    it('should identify high confidence correlation', () => {\r\n      const highConfidenceEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        confidenceLevel: ConfidenceLevel.HIGH\r\n      });\r\n\r\n      const veryHighConfidenceEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        confidenceLevel: ConfidenceLevel.VERY_HIGH\r\n      });\r\n\r\n      const confirmedEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        confidenceLevel: ConfidenceLevel.CONFIRMED\r\n      });\r\n\r\n      const mediumConfidenceEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        confidenceLevel: ConfidenceLevel.MEDIUM\r\n      });\r\n\r\n      expect(highConfidenceEvent.isHighConfidenceCorrelation()).toBe(true);\r\n      expect(veryHighConfidenceEvent.isHighConfidenceCorrelation()).toBe(true);\r\n      expect(confirmedEvent.isHighConfidenceCorrelation()).toBe(true);\r\n      expect(mediumConfidenceEvent.isHighConfidenceCorrelation()).toBe(false);\r\n    });\r\n\r\n    it('should identify events with multiple related events', () => {\r\n      const multipleRelatedEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        relatedEventsCount: 5\r\n      });\r\n\r\n      const singleRelatedEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        relatedEventsCount: 1\r\n      });\r\n\r\n      const noRelatedEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        relatedEventsCount: 0\r\n      });\r\n\r\n      expect(multipleRelatedEvent.hasMultipleRelatedEvents()).toBe(true);\r\n      expect(singleRelatedEvent.hasMultipleRelatedEvents()).toBe(false);\r\n      expect(noRelatedEvent.hasMultipleRelatedEvents()).toBe(false);\r\n    });\r\n\r\n    it('should identify events with correlation matches', () => {\r\n      const withMatchesEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        correlationMatchesCount: 3\r\n      });\r\n\r\n      const noMatchesEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        correlationMatchesCount: 0\r\n      });\r\n\r\n      expect(withMatchesEvent.hasCorrelationMatches()).toBe(true);\r\n      expect(noMatchesEvent.hasCorrelationMatches()).toBe(false);\r\n    });\r\n\r\n    it('should identify potential attack campaigns', () => {\r\n      const attackCampaignEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        hasAttackChain: true,\r\n        relatedEventsCount: 5,\r\n        confidenceLevel: ConfidenceLevel.HIGH\r\n      });\r\n\r\n      const nonCampaignEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        hasAttackChain: false,\r\n        relatedEventsCount: 1,\r\n        confidenceLevel: ConfidenceLevel.LOW\r\n      });\r\n\r\n      expect(attackCampaignEvent.isPotentialAttackCampaign()).toBe(true);\r\n      expect(nonCampaignEvent.isPotentialAttackCampaign()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('priority and alert level calculation', () => {\r\n    it('should calculate critical priority for attack campaigns', () => {\r\n      const criticalEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        severity: EventSeverity.CRITICAL,\r\n        hasAttackChain: true,\r\n        relatedEventsCount: 5,\r\n        confidenceLevel: ConfidenceLevel.HIGH\r\n      });\r\n\r\n      expect(criticalEvent.getEventPriority()).toBe('critical');\r\n    });\r\n\r\n    it('should calculate high priority for high severity with attack chain', () => {\r\n      const highPriorityEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        severity: EventSeverity.HIGH,\r\n        hasAttackChain: true,\r\n        relatedEventsCount: 2,\r\n        confidenceLevel: ConfidenceLevel.MEDIUM\r\n      });\r\n\r\n      expect(highPriorityEvent.getEventPriority()).toBe('high');\r\n    });\r\n\r\n    it('should calculate medium priority for quality correlation with matches', () => {\r\n      const mediumPriorityEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        severity: EventSeverity.MEDIUM,\r\n        hasAttackChain: false,\r\n        correlationQualityScore: 85,\r\n        correlationMatchesCount: 3\r\n      });\r\n\r\n      expect(mediumPriorityEvent.getEventPriority()).toBe('medium');\r\n    });\r\n\r\n    it('should calculate low priority for basic events', () => {\r\n      const lowPriorityEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        severity: EventSeverity.LOW,\r\n        hasAttackChain: false,\r\n        correlationQualityScore: 50,\r\n        correlationMatchesCount: 0\r\n      });\r\n\r\n      expect(lowPriorityEvent.getEventPriority()).toBe('low');\r\n    });\r\n\r\n    it('should calculate alert levels correctly', () => {\r\n      const criticalAlert = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        severity: EventSeverity.CRITICAL,\r\n        hasAttackChain: true,\r\n        relatedEventsCount: 5,\r\n        confidenceLevel: ConfidenceLevel.HIGH\r\n      });\r\n\r\n      const alertLevel = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        severity: EventSeverity.HIGH,\r\n        hasAttackChain: true\r\n      });\r\n\r\n      const warningLevel = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        severity: EventSeverity.MEDIUM,\r\n        hasAttackChain: false,\r\n        confidenceLevel: ConfidenceLevel.HIGH\r\n      });\r\n\r\n      const infoLevel = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        severity: EventSeverity.LOW,\r\n        hasAttackChain: false,\r\n        confidenceLevel: ConfidenceLevel.LOW\r\n      });\r\n\r\n      expect(criticalAlert.getAlertLevel()).toBe('critical');\r\n      expect(alertLevel.getAlertLevel()).toBe('alert');\r\n      expect(warningLevel.getAlertLevel()).toBe('warning');\r\n      expect(infoLevel.getAlertLevel()).toBe('info');\r\n    });\r\n  });\r\n\r\n  describe('escalation requirements', () => {\r\n    it('should require management escalation for attack campaigns', () => {\r\n      const campaignEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        severity: EventSeverity.CRITICAL,\r\n        hasAttackChain: true,\r\n        relatedEventsCount: 5,\r\n        confidenceLevel: ConfidenceLevel.HIGH\r\n      });\r\n\r\n      const escalation = campaignEvent.getEscalationRequirements();\r\n\r\n      expect(escalation.shouldEscalate).toBe(true);\r\n      expect(escalation.escalationLevel).toBe('management');\r\n      expect(escalation.timeoutMinutes).toBe(15);\r\n      expect(escalation.reason).toBe('Potential coordinated attack campaign detected');\r\n    });\r\n\r\n    it('should require tier3 escalation for critical events with attack chains', () => {\r\n      const criticalEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        severity: EventSeverity.CRITICAL,\r\n        hasAttackChain: true,\r\n        relatedEventsCount: 2,\r\n        confidenceLevel: ConfidenceLevel.MEDIUM\r\n      });\r\n\r\n      const escalation = criticalEvent.getEscalationRequirements();\r\n\r\n      expect(escalation.shouldEscalate).toBe(true);\r\n      expect(escalation.escalationLevel).toBe('tier3');\r\n      expect(escalation.timeoutMinutes).toBe(30);\r\n      expect(escalation.reason).toBe('Critical severity event with attack chain identified');\r\n    });\r\n\r\n    it('should require tier2 escalation for high severity with high confidence', () => {\r\n      const highSeverityEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        severity: EventSeverity.HIGH,\r\n        hasAttackChain: false,\r\n        confidenceLevel: ConfidenceLevel.HIGH\r\n      });\r\n\r\n      const escalation = highSeverityEvent.getEscalationRequirements();\r\n\r\n      expect(escalation.shouldEscalate).toBe(true);\r\n      expect(escalation.escalationLevel).toBe('tier2');\r\n      expect(escalation.timeoutMinutes).toBe(60);\r\n      expect(escalation.reason).toBe('High severity event with high confidence correlation');\r\n    });\r\n\r\n    it('should require tier1 escalation for quality correlation with matches', () => {\r\n      const qualityEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        severity: EventSeverity.MEDIUM,\r\n        hasAttackChain: false,\r\n        correlationQualityScore: 85,\r\n        correlationMatchesCount: 3,\r\n        confidenceLevel: ConfidenceLevel.MEDIUM\r\n      });\r\n\r\n      const escalation = qualityEvent.getEscalationRequirements();\r\n\r\n      expect(escalation.shouldEscalate).toBe(true);\r\n      expect(escalation.escalationLevel).toBe('tier1');\r\n      expect(escalation.timeoutMinutes).toBe(120);\r\n      expect(escalation.reason).toBe('Quality correlation with multiple matches found');\r\n    });\r\n\r\n    it('should not require escalation for standard events', () => {\r\n      const standardEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        severity: EventSeverity.LOW,\r\n        hasAttackChain: false,\r\n        correlationQualityScore: 50,\r\n        correlationMatchesCount: 0,\r\n        confidenceLevel: ConfidenceLevel.LOW\r\n      });\r\n\r\n      const escalation = standardEvent.getEscalationRequirements();\r\n\r\n      expect(escalation.shouldEscalate).toBe(false);\r\n      expect(escalation.escalationLevel).toBe('tier1');\r\n      expect(escalation.timeoutMinutes).toBe(240);\r\n      expect(escalation.reason).toBe('Standard correlation processing');\r\n    });\r\n  });\r\n\r\n  describe('recommended actions', () => {\r\n    it('should recommend incident response for attack campaigns', () => {\r\n      const campaignEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        severity: EventSeverity.CRITICAL,\r\n        hasAttackChain: true,\r\n        relatedEventsCount: 5,\r\n        confidenceLevel: ConfidenceLevel.HIGH\r\n      });\r\n\r\n      const actions = campaignEvent.getRecommendedActions();\r\n\r\n      expect(actions).toContain('Initiate incident response procedures');\r\n      expect(actions).toContain('Notify security operations center');\r\n      expect(actions).toContain('Activate threat hunting team');\r\n    });\r\n\r\n    it('should recommend attack chain analysis for events with attack chains', () => {\r\n      const attackChainEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        hasAttackChain: true\r\n      });\r\n\r\n      const actions = attackChainEvent.getRecommendedActions();\r\n\r\n      expect(actions).toContain('Analyze attack chain progression');\r\n      expect(actions).toContain('Identify attack vectors and techniques');\r\n      expect(actions).toContain('Assess potential impact and scope');\r\n    });\r\n\r\n    it('should recommend validation for high confidence correlations', () => {\r\n      const highConfidenceEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        confidenceLevel: ConfidenceLevel.HIGH\r\n      });\r\n\r\n      const actions = highConfidenceEvent.getRecommendedActions();\r\n\r\n      expect(actions).toContain('Validate correlation findings');\r\n      expect(actions).toContain('Gather additional evidence');\r\n      expect(actions).toContain('Prepare containment measures');\r\n    });\r\n\r\n    it('should recommend manual review scheduling', () => {\r\n      const reviewEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        requiresManualReview: true\r\n      });\r\n\r\n      const actions = reviewEvent.getRecommendedActions();\r\n\r\n      expect(actions).toContain('Schedule manual review');\r\n      expect(actions).toContain('Assign to security analyst');\r\n      expect(actions).toContain('Prioritize based on severity and confidence');\r\n    });\r\n\r\n    it('should recommend related event investigation', () => {\r\n      const multipleRelatedEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        relatedEventsCount: 5\r\n      });\r\n\r\n      const actions = multipleRelatedEvent.getRecommendedActions();\r\n\r\n      expect(actions).toContain('Investigate related events');\r\n      expect(actions).toContain('Look for additional correlations');\r\n      expect(actions).toContain('Map event relationships');\r\n    });\r\n  });\r\n\r\n  describe('event summary', () => {\r\n    it('should provide comprehensive event summary', () => {\r\n      const domainEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, eventData);\r\n      const summary = domainEvent.getEventSummary();\r\n\r\n      expect(summary.correlatedEventId).toBe(aggregateId.toString());\r\n      expect(summary.enrichedEventId).toBe(eventData.enrichedEventId.toString());\r\n      expect(summary.eventType).toBe(eventData.eventType);\r\n      expect(summary.severity).toBe(eventData.severity);\r\n      expect(summary.correlationStatus).toBe(eventData.correlationStatus);\r\n      expect(summary.correlationQualityScore).toBe(eventData.correlationQualityScore);\r\n      expect(summary.appliedRulesCount).toBe(eventData.appliedRulesCount);\r\n      expect(summary.correlationMatchesCount).toBe(eventData.correlationMatchesCount);\r\n      expect(summary.relatedEventsCount).toBe(eventData.relatedEventsCount);\r\n      expect(summary.confidenceLevel).toBe(eventData.confidenceLevel);\r\n      expect(summary.hasAttackChain).toBe(eventData.hasAttackChain);\r\n      expect(summary.requiresManualReview).toBe(eventData.requiresManualReview);\r\n      expect(summary.isHighSeverity).toBe(true);\r\n      expect(summary.isCritical).toBe(false);\r\n      expect(summary.hasHighCorrelationQuality).toBe(true);\r\n      expect(summary.isCorrelationCompleted).toBe(true);\r\n      expect(summary.isHighConfidenceCorrelation).toBe(true);\r\n      expect(summary.hasMultipleRelatedEvents).toBe(true);\r\n      expect(summary.hasCorrelationMatches).toBe(true);\r\n      expect(summary.isPotentialAttackCampaign).toBe(true);\r\n      expect(summary.eventPriority).toBeDefined();\r\n      expect(summary.alertLevel).toBeDefined();\r\n      expect(summary.recommendedActions).toBeInstanceOf(Array);\r\n      expect(summary.escalationRequirements).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('JSON serialization', () => {\r\n    it('should serialize to JSON with event summary', () => {\r\n      const domainEvent = new CorrelatedEventCreatedDomainEvent(aggregateId, eventData);\r\n      const json = domainEvent.toJSON();\r\n\r\n      expect(json.eventName).toBe('CorrelatedEventCreatedDomainEvent');\r\n      expect(json.aggregateId).toBe(aggregateId.toString());\r\n      expect(json.eventData).toEqual(eventData);\r\n      expect(json.eventSummary).toBeDefined();\r\n      expect(json.occurredOn).toBeInstanceOf(Date);\r\n    });\r\n  });\r\n});"], "version": 3}