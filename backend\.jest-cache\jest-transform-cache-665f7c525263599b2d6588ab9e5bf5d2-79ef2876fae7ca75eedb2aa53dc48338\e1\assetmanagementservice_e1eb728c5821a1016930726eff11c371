181cb3ea9c592a92e43fe2edbbaaed26
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AssetManagementService_1;
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetManagementService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const asset_entity_1 = require("../../domain/entities/asset.entity");
const vulnerability_entity_1 = require("../../domain/entities/vulnerability.entity");
const logger_service_1 = require("../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../infrastructure/logging/audit/audit.service");
/**
 * Asset Management service
 * Handles asset discovery, inventory, and lifecycle management
 */
let AssetManagementService = AssetManagementService_1 = class AssetManagementService {
    constructor(assetRepository, vulnerabilityRepository, loggerService, auditService) {
        this.assetRepository = assetRepository;
        this.vulnerabilityRepository = vulnerabilityRepository;
        this.loggerService = loggerService;
        this.auditService = auditService;
        this.logger = new common_1.Logger(AssetManagementService_1.name);
    }
    /**
     * Create a new asset
     * @param assetData Asset creation data
     * @param userId User creating the asset
     * @returns Created asset
     */
    async createAsset(assetData, userId) {
        try {
            this.logger.debug('Creating new asset', {
                name: assetData.name,
                type: assetData.type,
                userId,
            });
            // Check for duplicate assets
            if (assetData.ipAddress) {
                const existingAsset = await this.assetRepository.findOne({
                    where: { ipAddress: assetData.ipAddress },
                });
                if (existingAsset) {
                    throw new common_1.BadRequestException(`Asset with IP address ${assetData.ipAddress} already exists`);
                }
            }
            const asset = this.assetRepository.create({
                ...assetData,
                status: 'active',
                firstDiscovered: new Date(),
                lastSeen: new Date(),
                createdBy: userId,
                discoveryMethod: 'manual',
            });
            // Calculate initial risk score
            asset.calculateRiskScore();
            const savedAsset = await this.assetRepository.save(asset);
            await this.auditService.logUserAction(userId, 'create', 'asset', savedAsset.id, {
                name: assetData.name,
                type: assetData.type,
                ipAddress: assetData.ipAddress,
            });
            this.logger.log('Asset created successfully', {
                assetId: savedAsset.id,
                name: assetData.name,
                userId,
            });
            return savedAsset;
        }
        catch (error) {
            this.logger.error('Failed to create asset', {
                error: error.message,
                assetData,
                userId,
            });
            throw error;
        }
    }
    /**
     * Find asset by ID
     * @param id Asset ID
     * @returns Asset or null
     */
    async findById(id) {
        try {
            return await this.assetRepository.findOne({
                where: { id },
                relations: ['vulnerabilities', 'scanResults'],
            });
        }
        catch (error) {
            this.logger.error('Failed to find asset by ID', {
                id,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Find assets with filtering and pagination
     * @param options Query options
     * @returns Paginated assets
     */
    async findMany(options) {
        try {
            const { page = 1, limit = 20, type, status, criticality, environment, search, tags, ipAddress, hostname, sortBy = 'createdAt', sortOrder = 'DESC', } = options;
            const queryBuilder = this.assetRepository.createQueryBuilder('asset');
            // Apply filters
            if (type && type.length > 0) {
                queryBuilder.andWhere('asset.type IN (:...type)', { type });
            }
            if (status && status.length > 0) {
                queryBuilder.andWhere('asset.status IN (:...status)', { status });
            }
            if (criticality && criticality.length > 0) {
                queryBuilder.andWhere('asset.criticality IN (:...criticality)', { criticality });
            }
            if (environment && environment.length > 0) {
                queryBuilder.andWhere('asset.environment IN (:...environment)', { environment });
            }
            if (search) {
                queryBuilder.andWhere('(asset.name ILIKE :search OR asset.description ILIKE :search OR asset.hostname ILIKE :search)', { search: `%${search}%` });
            }
            if (tags && tags.length > 0) {
                queryBuilder.andWhere('asset.tags ?& :tags', { tags });
            }
            if (ipAddress) {
                queryBuilder.andWhere('asset.ipAddress = :ipAddress', { ipAddress });
            }
            if (hostname) {
                queryBuilder.andWhere('asset.hostname ILIKE :hostname', { hostname: `%${hostname}%` });
            }
            // Apply sorting
            queryBuilder.orderBy(`asset.${sortBy}`, sortOrder);
            // Apply pagination
            const offset = (page - 1) * limit;
            queryBuilder.skip(offset).take(limit);
            const [assets, total] = await queryBuilder.getManyAndCount();
            const totalPages = Math.ceil(total / limit);
            this.logger.debug('Assets retrieved', {
                total,
                page,
                limit,
                totalPages,
                filters: { type, status, criticality, environment, search, tags },
            });
            return {
                assets,
                total,
                page,
                totalPages,
            };
        }
        catch (error) {
            this.logger.error('Failed to find assets', {
                error: error.message,
                options,
            });
            throw error;
        }
    }
    /**
     * Update asset
     * @param id Asset ID
     * @param updateData Update data
     * @param userId User updating the asset
     * @returns Updated asset
     */
    async updateAsset(id, updateData, userId) {
        try {
            const asset = await this.findById(id);
            if (!asset) {
                throw new common_1.NotFoundException('Asset not found');
            }
            this.logger.debug('Updating asset', {
                assetId: id,
                updateData,
                userId,
            });
            // Update asset properties
            Object.assign(asset, updateData, {
                updatedBy: userId,
            });
            // Recalculate risk score if relevant fields changed
            if (updateData.criticality || updateData.vulnerabilityCounts) {
                asset.calculateRiskScore();
            }
            const updatedAsset = await this.assetRepository.save(asset);
            await this.auditService.logUserAction(userId, 'update', 'asset', id, {
                changes: updateData,
                previousValues: {
                    name: asset.name,
                    status: asset.status,
                    criticality: asset.criticality,
                },
            });
            this.logger.log('Asset updated successfully', {
                assetId: id,
                userId,
            });
            return updatedAsset;
        }
        catch (error) {
            this.logger.error('Failed to update asset', {
                assetId: id,
                error: error.message,
                userId,
            });
            throw error;
        }
    }
    /**
     * Delete asset
     * @param id Asset ID
     * @param userId User deleting the asset
     */
    async deleteAsset(id, userId) {
        try {
            const asset = await this.findById(id);
            if (!asset) {
                throw new common_1.NotFoundException('Asset not found');
            }
            this.logger.debug('Deleting asset', {
                assetId: id,
                userId,
            });
            await this.assetRepository.remove(asset);
            await this.auditService.logUserAction(userId, 'delete', 'asset', id, {
                name: asset.name,
                type: asset.type,
                ipAddress: asset.ipAddress,
            });
            this.logger.log('Asset deleted successfully', {
                assetId: id,
                userId,
            });
        }
        catch (error) {
            this.logger.error('Failed to delete asset', {
                assetId: id,
                error: error.message,
                userId,
            });
            throw error;
        }
    }
    /**
     * Discover assets from network scan
     * @param discoveryData Discovery data from network scan
     * @param userId User initiating discovery
     * @returns Discovered assets
     */
    async discoverAssets(discoveryData, userId) {
        try {
            this.logger.debug('Discovering assets', {
                count: discoveryData.length,
                userId,
            });
            const discoveredAssets = [];
            for (const data of discoveryData) {
                // Check if asset already exists
                let asset = await this.assetRepository.findOne({
                    where: { ipAddress: data.ipAddress },
                });
                if (asset) {
                    // Update existing asset
                    asset.updateLastSeen();
                    asset.hostname = data.hostname || asset.hostname;
                    asset.macAddress = data.macAddress || asset.macAddress;
                    asset.operatingSystem = data.operatingSystem || asset.operatingSystem;
                    asset.services = data.services || asset.services;
                    asset.discoveryMethod = data.discoveryMethod;
                    asset.updatedBy = userId;
                }
                else {
                    // Create new asset
                    asset = this.assetRepository.create({
                        name: data.hostname || data.ipAddress,
                        type: this.determineAssetType(data),
                        ipAddress: data.ipAddress,
                        hostname: data.hostname,
                        macAddress: data.macAddress,
                        operatingSystem: data.operatingSystem,
                        services: data.services,
                        status: 'active',
                        criticality: 'medium',
                        environment: 'production',
                        discoveryMethod: data.discoveryMethod,
                        firstDiscovered: new Date(),
                        lastSeen: new Date(),
                        createdBy: userId,
                    });
                    asset.calculateRiskScore();
                }
                const savedAsset = await this.assetRepository.save(asset);
                discoveredAssets.push(savedAsset);
            }
            await this.auditService.logUserAction(userId, 'discover', 'asset', null, {
                discoveredCount: discoveredAssets.length,
                method: discoveryData[0]?.discoveryMethod,
            });
            this.logger.log('Asset discovery completed', {
                discoveredCount: discoveredAssets.length,
                userId,
            });
            return discoveredAssets;
        }
        catch (error) {
            this.logger.error('Failed to discover assets', {
                error: error.message,
                userId,
            });
            throw error;
        }
    }
    /**
     * Update asset vulnerability counts
     * @param assetId Asset ID
     * @returns Updated asset
     */
    async updateVulnerabilityCounts(assetId) {
        try {
            const asset = await this.findById(assetId);
            if (!asset) {
                throw new common_1.NotFoundException('Asset not found');
            }
            // Count vulnerabilities by severity
            const vulnerabilityCounts = await this.vulnerabilityRepository
                .createQueryBuilder('vuln')
                .select('vuln.severity', 'severity')
                .addSelect('COUNT(*)', 'count')
                .where('vuln.assetId = :assetId', { assetId })
                .andWhere('vuln.status IN (:...statuses)', { statuses: ['open', 'confirmed'] })
                .groupBy('vuln.severity')
                .getRawMany();
            const counts = {
                critical: 0,
                high: 0,
                medium: 0,
                low: 0,
                info: 0,
                total: 0,
            };
            vulnerabilityCounts.forEach(({ severity, count }) => {
                counts[severity] = parseInt(count);
                counts.total += parseInt(count);
            });
            asset.updateVulnerabilityCounts(counts);
            return await this.assetRepository.save(asset);
        }
        catch (error) {
            this.logger.error('Failed to update vulnerability counts', {
                assetId,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Get assets due for scanning
     * @returns Assets due for scanning
     */
    async getAssetsDueForScanning() {
        try {
            return await this.assetRepository
                .createQueryBuilder('asset')
                .where('asset.isScannable = :scannable', { scannable: true })
                .andWhere('asset.status = :status', { status: 'active' })
                .andWhere('(asset.lastScanDate IS NULL OR asset.lastScanDate + INTERVAL \'1 hour\' * asset.scanFrequency <= NOW())')
                .orderBy('asset.criticality', 'DESC')
                .addOrderBy('asset.lastScanDate', 'ASC')
                .getMany();
        }
        catch (error) {
            this.logger.error('Failed to get assets due for scanning', {
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Get asset statistics
     * @returns Asset statistics
     */
    async getStatistics() {
        try {
            const [totalAssets, activeAssets, criticalAssets, assetsByType, assetsByEnvironment, assetsWithVulnerabilities,] = await Promise.all([
                this.assetRepository.count(),
                this.assetRepository.count({ where: { status: 'active' } }),
                this.assetRepository.count({ where: { criticality: 'critical' } }),
                this.getAssetCountsByType(),
                this.getAssetCountsByEnvironment(),
                this.getAssetsWithVulnerabilities(),
            ]);
            return {
                total: totalAssets,
                active: activeAssets,
                critical: criticalAssets,
                byType: assetsByType,
                byEnvironment: assetsByEnvironment,
                withVulnerabilities: assetsWithVulnerabilities,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            this.logger.error('Failed to get asset statistics', {
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Determine asset type based on discovery data
     * @param data Discovery data
     * @returns Asset type
     */
    determineAssetType(data) {
        if (data.services) {
            const serviceNames = data.services.map(s => s.name.toLowerCase());
            if (serviceNames.includes('http') || serviceNames.includes('https')) {
                return 'web_application';
            }
            if (serviceNames.includes('ssh') || serviceNames.includes('rdp')) {
                return 'server';
            }
            if (serviceNames.includes('snmp')) {
                return 'network_device';
            }
        }
        if (data.operatingSystem) {
            const os = data.operatingSystem.name?.toLowerCase() || '';
            if (os.includes('windows') || os.includes('linux') || os.includes('unix')) {
                return 'server';
            }
        }
        return 'server'; // Default type
    }
    /**
     * Get asset counts by type
     * @returns Asset counts by type
     */
    async getAssetCountsByType() {
        const result = await this.assetRepository
            .createQueryBuilder('asset')
            .select('asset.type', 'type')
            .addSelect('COUNT(*)', 'count')
            .groupBy('asset.type')
            .getRawMany();
        return result.reduce((acc, item) => {
            acc[item.type] = parseInt(item.count);
            return acc;
        }, {});
    }
    /**
     * Get asset counts by environment
     * @returns Asset counts by environment
     */
    async getAssetCountsByEnvironment() {
        const result = await this.assetRepository
            .createQueryBuilder('asset')
            .select('asset.environment', 'environment')
            .addSelect('COUNT(*)', 'count')
            .groupBy('asset.environment')
            .getRawMany();
        return result.reduce((acc, item) => {
            acc[item.environment] = parseInt(item.count);
            return acc;
        }, {});
    }
    /**
     * Get count of assets with vulnerabilities
     * @returns Count of assets with vulnerabilities
     */
    async getAssetsWithVulnerabilities() {
        const result = await this.assetRepository
            .createQueryBuilder('asset')
            .innerJoin('asset.vulnerabilities', 'vuln')
            .where('vuln.status IN (:...statuses)', { statuses: ['open', 'confirmed'] })
            .getCount();
        return result;
    }
};
exports.AssetManagementService = AssetManagementService;
exports.AssetManagementService = AssetManagementService = AssetManagementService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(asset_entity_1.Asset)),
    __param(1, (0, typeorm_1.InjectRepository)(vulnerability_entity_1.Vulnerability)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof logger_service_1.LoggerService !== "undefined" && logger_service_1.LoggerService) === "function" ? _c : Object, typeof (_d = typeof audit_service_1.AuditService !== "undefined" && audit_service_1.AuditService) === "function" ? _d : Object])
], AssetManagementService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxtb2R1bGVzXFx2dWxuZXJhYmlsaXR5LW1hbmFnZW1lbnRcXGFwcGxpY2F0aW9uXFxzZXJ2aWNlc1xcYXNzZXQtbWFuYWdlbWVudC5zZXJ2aWNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsMkNBQTRGO0FBQzVGLDZDQUFtRDtBQUNuRCxxQ0FBeUU7QUFDekUscUVBQTJEO0FBQzNELHFGQUEyRTtBQUMzRSxzRkFBa0Y7QUFDbEYsMEZBQXNGO0FBRXRGOzs7R0FHRztBQUVJLElBQU0sc0JBQXNCLDhCQUE1QixNQUFNLHNCQUFzQjtJQUdqQyxZQUVFLGVBQW1ELEVBRW5ELHVCQUFtRSxFQUNsRCxhQUE0QixFQUM1QixZQUEwQjtRQUoxQixvQkFBZSxHQUFmLGVBQWUsQ0FBbUI7UUFFbEMsNEJBQXVCLEdBQXZCLHVCQUF1QixDQUEyQjtRQUNsRCxrQkFBYSxHQUFiLGFBQWEsQ0FBZTtRQUM1QixpQkFBWSxHQUFaLFlBQVksQ0FBYztRQVI1QixXQUFNLEdBQUcsSUFBSSxlQUFNLENBQUMsd0JBQXNCLENBQUMsSUFBSSxDQUFDLENBQUM7SUFTL0QsQ0FBQztJQUVKOzs7OztPQUtHO0lBQ0gsS0FBSyxDQUFDLFdBQVcsQ0FDZixTQVlDLEVBQ0QsTUFBYztRQUVkLElBQUksQ0FBQztZQUNILElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLG9CQUFvQixFQUFFO2dCQUN0QyxJQUFJLEVBQUUsU0FBUyxDQUFDLElBQUk7Z0JBQ3BCLElBQUksRUFBRSxTQUFTLENBQUMsSUFBSTtnQkFDcEIsTUFBTTthQUNQLENBQUMsQ0FBQztZQUVILDZCQUE2QjtZQUM3QixJQUFJLFNBQVMsQ0FBQyxTQUFTLEVBQUUsQ0FBQztnQkFDeEIsTUFBTSxhQUFhLEdBQUcsTUFBTSxJQUFJLENBQUMsZUFBZSxDQUFDLE9BQU8sQ0FBQztvQkFDdkQsS0FBSyxFQUFFLEVBQUUsU0FBUyxFQUFFLFNBQVMsQ0FBQyxTQUFTLEVBQUU7aUJBQzFDLENBQUMsQ0FBQztnQkFDSCxJQUFJLGFBQWEsRUFBRSxDQUFDO29CQUNsQixNQUFNLElBQUksNEJBQW1CLENBQUMseUJBQXlCLFNBQVMsQ0FBQyxTQUFTLGlCQUFpQixDQUFDLENBQUM7Z0JBQy9GLENBQUM7WUFDSCxDQUFDO1lBRUQsTUFBTSxLQUFLLEdBQUcsSUFBSSxDQUFDLGVBQWUsQ0FBQyxNQUFNLENBQUM7Z0JBQ3hDLEdBQUcsU0FBUztnQkFDWixNQUFNLEVBQUUsUUFBUTtnQkFDaEIsZUFBZSxFQUFFLElBQUksSUFBSSxFQUFFO2dCQUMzQixRQUFRLEVBQUUsSUFBSSxJQUFJLEVBQUU7Z0JBQ3BCLFNBQVMsRUFBRSxNQUFNO2dCQUNqQixlQUFlLEVBQUUsUUFBUTthQUMxQixDQUFDLENBQUM7WUFFSCwrQkFBK0I7WUFDL0IsS0FBSyxDQUFDLGtCQUFrQixFQUFFLENBQUM7WUFFM0IsTUFBTSxVQUFVLEdBQUcsTUFBTSxJQUFJLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUUxRCxNQUFNLElBQUksQ0FBQyxZQUFZLENBQUMsYUFBYSxDQUNuQyxNQUFNLEVBQ04sUUFBUSxFQUNSLE9BQU8sRUFDUCxVQUFVLENBQUMsRUFBRSxFQUNiO2dCQUNFLElBQUksRUFBRSxTQUFTLENBQUMsSUFBSTtnQkFDcEIsSUFBSSxFQUFFLFNBQVMsQ0FBQyxJQUFJO2dCQUNwQixTQUFTLEVBQUUsU0FBUyxDQUFDLFNBQVM7YUFDL0IsQ0FDRixDQUFDO1lBRUYsSUFBSSxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsNEJBQTRCLEVBQUU7Z0JBQzVDLE9BQU8sRUFBRSxVQUFVLENBQUMsRUFBRTtnQkFDdEIsSUFBSSxFQUFFLFNBQVMsQ0FBQyxJQUFJO2dCQUNwQixNQUFNO2FBQ1AsQ0FBQyxDQUFDO1lBRUgsT0FBTyxVQUFVLENBQUM7UUFDcEIsQ0FBQztRQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7WUFDZixJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyx3QkFBd0IsRUFBRTtnQkFDMUMsS0FBSyxFQUFFLEtBQUssQ0FBQyxPQUFPO2dCQUNwQixTQUFTO2dCQUNULE1BQU07YUFDUCxDQUFDLENBQUM7WUFDSCxNQUFNLEtBQUssQ0FBQztRQUNkLENBQUM7SUFDSCxDQUFDO0lBRUQ7Ozs7T0FJRztJQUNILEtBQUssQ0FBQyxRQUFRLENBQUMsRUFBVTtRQUN2QixJQUFJLENBQUM7WUFDSCxPQUFPLE1BQU0sSUFBSSxDQUFDLGVBQWUsQ0FBQyxPQUFPLENBQUM7Z0JBQ3hDLEtBQUssRUFBRSxFQUFFLEVBQUUsRUFBRTtnQkFDYixTQUFTLEVBQUUsQ0FBQyxpQkFBaUIsRUFBRSxhQUFhLENBQUM7YUFDOUMsQ0FBQyxDQUFDO1FBQ0wsQ0FBQztRQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7WUFDZixJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyw0QkFBNEIsRUFBRTtnQkFDOUMsRUFBRTtnQkFDRixLQUFLLEVBQUUsS0FBSyxDQUFDLE9BQU87YUFDckIsQ0FBQyxDQUFDO1lBQ0gsTUFBTSxLQUFLLENBQUM7UUFDZCxDQUFDO0lBQ0gsQ0FBQztJQUVEOzs7O09BSUc7SUFDSCxLQUFLLENBQUMsUUFBUSxDQUFDLE9BYWQ7UUFDQyxJQUFJLENBQUM7WUFDSCxNQUFNLEVBQ0osSUFBSSxHQUFHLENBQUMsRUFDUixLQUFLLEdBQUcsRUFBRSxFQUNWLElBQUksRUFDSixNQUFNLEVBQ04sV0FBVyxFQUNYLFdBQVcsRUFDWCxNQUFNLEVBQ04sSUFBSSxFQUNKLFNBQVMsRUFDVCxRQUFRLEVBQ1IsTUFBTSxHQUFHLFdBQVcsRUFDcEIsU0FBUyxHQUFHLE1BQU0sR0FDbkIsR0FBRyxPQUFPLENBQUM7WUFFWixNQUFNLFlBQVksR0FBRyxJQUFJLENBQUMsZUFBZSxDQUFDLGtCQUFrQixDQUFDLE9BQU8sQ0FBQyxDQUFDO1lBRXRFLGdCQUFnQjtZQUNoQixJQUFJLElBQUksSUFBSSxJQUFJLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRSxDQUFDO2dCQUM1QixZQUFZLENBQUMsUUFBUSxDQUFDLDBCQUEwQixFQUFFLEVBQUUsSUFBSSxFQUFFLENBQUMsQ0FBQztZQUM5RCxDQUFDO1lBRUQsSUFBSSxNQUFNLElBQUksTUFBTSxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUUsQ0FBQztnQkFDaEMsWUFBWSxDQUFDLFFBQVEsQ0FBQyw4QkFBOEIsRUFBRSxFQUFFLE1BQU0sRUFBRSxDQUFDLENBQUM7WUFDcEUsQ0FBQztZQUVELElBQUksV0FBVyxJQUFJLFdBQVcsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUM7Z0JBQzFDLFlBQVksQ0FBQyxRQUFRLENBQUMsd0NBQXdDLEVBQUUsRUFBRSxXQUFXLEVBQUUsQ0FBQyxDQUFDO1lBQ25GLENBQUM7WUFFRCxJQUFJLFdBQVcsSUFBSSxXQUFXLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRSxDQUFDO2dCQUMxQyxZQUFZLENBQUMsUUFBUSxDQUFDLHdDQUF3QyxFQUFFLEVBQUUsV0FBVyxFQUFFLENBQUMsQ0FBQztZQUNuRixDQUFDO1lBRUQsSUFBSSxNQUFNLEVBQUUsQ0FBQztnQkFDWCxZQUFZLENBQUMsUUFBUSxDQUNuQiwrRkFBK0YsRUFDL0YsRUFBRSxNQUFNLEVBQUUsSUFBSSxNQUFNLEdBQUcsRUFBRSxDQUMxQixDQUFDO1lBQ0osQ0FBQztZQUVELElBQUksSUFBSSxJQUFJLElBQUksQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUM7Z0JBQzVCLFlBQVksQ0FBQyxRQUFRLENBQUMscUJBQXFCLEVBQUUsRUFBRSxJQUFJLEVBQUUsQ0FBQyxDQUFDO1lBQ3pELENBQUM7WUFFRCxJQUFJLFNBQVMsRUFBRSxDQUFDO2dCQUNkLFlBQVksQ0FBQyxRQUFRLENBQUMsOEJBQThCLEVBQUUsRUFBRSxTQUFTLEVBQUUsQ0FBQyxDQUFDO1lBQ3ZFLENBQUM7WUFFRCxJQUFJLFFBQVEsRUFBRSxDQUFDO2dCQUNiLFlBQVksQ0FBQyxRQUFRLENBQUMsZ0NBQWdDLEVBQUUsRUFBRSxRQUFRLEVBQUUsSUFBSSxRQUFRLEdBQUcsRUFBRSxDQUFDLENBQUM7WUFDekYsQ0FBQztZQUVELGdCQUFnQjtZQUNoQixZQUFZLENBQUMsT0FBTyxDQUFDLFNBQVMsTUFBTSxFQUFFLEVBQUUsU0FBUyxDQUFDLENBQUM7WUFFbkQsbUJBQW1CO1lBQ25CLE1BQU0sTUFBTSxHQUFHLENBQUMsSUFBSSxHQUFHLENBQUMsQ0FBQyxHQUFHLEtBQUssQ0FBQztZQUNsQyxZQUFZLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUV0QyxNQUFNLENBQUMsTUFBTSxFQUFFLEtBQUssQ0FBQyxHQUFHLE1BQU0sWUFBWSxDQUFDLGVBQWUsRUFBRSxDQUFDO1lBQzdELE1BQU0sVUFBVSxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUMsS0FBSyxHQUFHLEtBQUssQ0FBQyxDQUFDO1lBRTVDLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLGtCQUFrQixFQUFFO2dCQUNwQyxLQUFLO2dCQUNMLElBQUk7Z0JBQ0osS0FBSztnQkFDTCxVQUFVO2dCQUNWLE9BQU8sRUFBRSxFQUFFLElBQUksRUFBRSxNQUFNLEVBQUUsV0FBVyxFQUFFLFdBQVcsRUFBRSxNQUFNLEVBQUUsSUFBSSxFQUFFO2FBQ2xFLENBQUMsQ0FBQztZQUVILE9BQU87Z0JBQ0wsTUFBTTtnQkFDTixLQUFLO2dCQUNMLElBQUk7Z0JBQ0osVUFBVTthQUNYLENBQUM7UUFDSixDQUFDO1FBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztZQUNmLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLHVCQUF1QixFQUFFO2dCQUN6QyxLQUFLLEVBQUUsS0FBSyxDQUFDLE9BQU87Z0JBQ3BCLE9BQU87YUFDUixDQUFDLENBQUM7WUFDSCxNQUFNLEtBQUssQ0FBQztRQUNkLENBQUM7SUFDSCxDQUFDO0lBRUQ7Ozs7OztPQU1HO0lBQ0gsS0FBSyxDQUFDLFdBQVcsQ0FDZixFQUFVLEVBQ1YsVUFBMEIsRUFDMUIsTUFBYztRQUVkLElBQUksQ0FBQztZQUNILE1BQU0sS0FBSyxHQUFHLE1BQU0sSUFBSSxDQUFDLFFBQVEsQ0FBQyxFQUFFLENBQUMsQ0FBQztZQUN0QyxJQUFJLENBQUMsS0FBSyxFQUFFLENBQUM7Z0JBQ1gsTUFBTSxJQUFJLDBCQUFpQixDQUFDLGlCQUFpQixDQUFDLENBQUM7WUFDakQsQ0FBQztZQUVELElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLGdCQUFnQixFQUFFO2dCQUNsQyxPQUFPLEVBQUUsRUFBRTtnQkFDWCxVQUFVO2dCQUNWLE1BQU07YUFDUCxDQUFDLENBQUM7WUFFSCwwQkFBMEI7WUFDMUIsTUFBTSxDQUFDLE1BQU0sQ0FBQyxLQUFLLEVBQUUsVUFBVSxFQUFFO2dCQUMvQixTQUFTLEVBQUUsTUFBTTthQUNsQixDQUFDLENBQUM7WUFFSCxvREFBb0Q7WUFDcEQsSUFBSSxVQUFVLENBQUMsV0FBVyxJQUFJLFVBQVUsQ0FBQyxtQkFBbUIsRUFBRSxDQUFDO2dCQUM3RCxLQUFLLENBQUMsa0JBQWtCLEVBQUUsQ0FBQztZQUM3QixDQUFDO1lBRUQsTUFBTSxZQUFZLEdBQUcsTUFBTSxJQUFJLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUU1RCxNQUFNLElBQUksQ0FBQyxZQUFZLENBQUMsYUFBYSxDQUNuQyxNQUFNLEVBQ04sUUFBUSxFQUNSLE9BQU8sRUFDUCxFQUFFLEVBQ0Y7Z0JBQ0UsT0FBTyxFQUFFLFVBQVU7Z0JBQ25CLGNBQWMsRUFBRTtvQkFDZCxJQUFJLEVBQUUsS0FBSyxDQUFDLElBQUk7b0JBQ2hCLE1BQU0sRUFBRSxLQUFLLENBQUMsTUFBTTtvQkFDcEIsV0FBVyxFQUFFLEtBQUssQ0FBQyxXQUFXO2lCQUMvQjthQUNGLENBQ0YsQ0FBQztZQUVGLElBQUksQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLDRCQUE0QixFQUFFO2dCQUM1QyxPQUFPLEVBQUUsRUFBRTtnQkFDWCxNQUFNO2FBQ1AsQ0FBQyxDQUFDO1lBRUgsT0FBTyxZQUFZLENBQUM7UUFDdEIsQ0FBQztRQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7WUFDZixJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyx3QkFBd0IsRUFBRTtnQkFDMUMsT0FBTyxFQUFFLEVBQUU7Z0JBQ1gsS0FBSyxFQUFFLEtBQUssQ0FBQyxPQUFPO2dCQUNwQixNQUFNO2FBQ1AsQ0FBQyxDQUFDO1lBQ0gsTUFBTSxLQUFLLENBQUM7UUFDZCxDQUFDO0lBQ0gsQ0FBQztJQUVEOzs7O09BSUc7SUFDSCxLQUFLLENBQUMsV0FBVyxDQUFDLEVBQVUsRUFBRSxNQUFjO1FBQzFDLElBQUksQ0FBQztZQUNILE1BQU0sS0FBSyxHQUFHLE1BQU0sSUFBSSxDQUFDLFFBQVEsQ0FBQyxFQUFFLENBQUMsQ0FBQztZQUN0QyxJQUFJLENBQUMsS0FBSyxFQUFFLENBQUM7Z0JBQ1gsTUFBTSxJQUFJLDBCQUFpQixDQUFDLGlCQUFpQixDQUFDLENBQUM7WUFDakQsQ0FBQztZQUVELElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLGdCQUFnQixFQUFFO2dCQUNsQyxPQUFPLEVBQUUsRUFBRTtnQkFDWCxNQUFNO2FBQ1AsQ0FBQyxDQUFDO1lBRUgsTUFBTSxJQUFJLENBQUMsZUFBZSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUV6QyxNQUFNLElBQUksQ0FBQyxZQUFZLENBQUMsYUFBYSxDQUNuQyxNQUFNLEVBQ04sUUFBUSxFQUNSLE9BQU8sRUFDUCxFQUFFLEVBQ0Y7Z0JBQ0UsSUFBSSxFQUFFLEtBQUssQ0FBQyxJQUFJO2dCQUNoQixJQUFJLEVBQUUsS0FBSyxDQUFDLElBQUk7Z0JBQ2hCLFNBQVMsRUFBRSxLQUFLLENBQUMsU0FBUzthQUMzQixDQUNGLENBQUM7WUFFRixJQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyw0QkFBNEIsRUFBRTtnQkFDNUMsT0FBTyxFQUFFLEVBQUU7Z0JBQ1gsTUFBTTthQUNQLENBQUMsQ0FBQztRQUNMLENBQUM7UUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1lBQ2YsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsd0JBQXdCLEVBQUU7Z0JBQzFDLE9BQU8sRUFBRSxFQUFFO2dCQUNYLEtBQUssRUFBRSxLQUFLLENBQUMsT0FBTztnQkFDcEIsTUFBTTthQUNQLENBQUMsQ0FBQztZQUNILE1BQU0sS0FBSyxDQUFDO1FBQ2QsQ0FBQztJQUNILENBQUM7SUFFRDs7Ozs7T0FLRztJQUNILEtBQUssQ0FBQyxjQUFjLENBQ2xCLGFBT0UsRUFDRixNQUFjO1FBRWQsSUFBSSxDQUFDO1lBQ0gsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsb0JBQW9CLEVBQUU7Z0JBQ3RDLEtBQUssRUFBRSxhQUFhLENBQUMsTUFBTTtnQkFDM0IsTUFBTTthQUNQLENBQUMsQ0FBQztZQUVILE1BQU0sZ0JBQWdCLEdBQVksRUFBRSxDQUFDO1lBRXJDLEtBQUssTUFBTSxJQUFJLElBQUksYUFBYSxFQUFFLENBQUM7Z0JBQ2pDLGdDQUFnQztnQkFDaEMsSUFBSSxLQUFLLEdBQUcsTUFBTSxJQUFJLENBQUMsZUFBZSxDQUFDLE9BQU8sQ0FBQztvQkFDN0MsS0FBSyxFQUFFLEVBQUUsU0FBUyxFQUFFLElBQUksQ0FBQyxTQUFTLEVBQUU7aUJBQ3JDLENBQUMsQ0FBQztnQkFFSCxJQUFJLEtBQUssRUFBRSxDQUFDO29CQUNWLHdCQUF3QjtvQkFDeEIsS0FBSyxDQUFDLGNBQWMsRUFBRSxDQUFDO29CQUN2QixLQUFLLENBQUMsUUFBUSxHQUFHLElBQUksQ0FBQyxRQUFRLElBQUksS0FBSyxDQUFDLFFBQVEsQ0FBQztvQkFDakQsS0FBSyxDQUFDLFVBQVUsR0FBRyxJQUFJLENBQUMsVUFBVSxJQUFJLEtBQUssQ0FBQyxVQUFVLENBQUM7b0JBQ3ZELEtBQUssQ0FBQyxlQUFlLEdBQUcsSUFBSSxDQUFDLGVBQWUsSUFBSSxLQUFLLENBQUMsZUFBZSxDQUFDO29CQUN0RSxLQUFLLENBQUMsUUFBUSxHQUFHLElBQUksQ0FBQyxRQUFRLElBQUksS0FBSyxDQUFDLFFBQVEsQ0FBQztvQkFDakQsS0FBSyxDQUFDLGVBQWUsR0FBRyxJQUFJLENBQUMsZUFBZSxDQUFDO29CQUM3QyxLQUFLLENBQUMsU0FBUyxHQUFHLE1BQU0sQ0FBQztnQkFDM0IsQ0FBQztxQkFBTSxDQUFDO29CQUNOLG1CQUFtQjtvQkFDbkIsS0FBSyxHQUFHLElBQUksQ0FBQyxlQUFlLENBQUMsTUFBTSxDQUFDO3dCQUNsQyxJQUFJLEVBQUUsSUFBSSxDQUFDLFFBQVEsSUFBSSxJQUFJLENBQUMsU0FBUzt3QkFDckMsSUFBSSxFQUFFLElBQUksQ0FBQyxrQkFBa0IsQ0FBQyxJQUFJLENBQUM7d0JBQ25DLFNBQVMsRUFBRSxJQUFJLENBQUMsU0FBUzt3QkFDekIsUUFBUSxFQUFFLElBQUksQ0FBQyxRQUFRO3dCQUN2QixVQUFVLEVBQUUsSUFBSSxDQUFDLFVBQVU7d0JBQzNCLGVBQWUsRUFBRSxJQUFJLENBQUMsZUFBZTt3QkFDckMsUUFBUSxFQUFFLElBQUksQ0FBQyxRQUFRO3dCQUN2QixNQUFNLEVBQUUsUUFBUTt3QkFDaEIsV0FBVyxFQUFFLFFBQVE7d0JBQ3JCLFdBQVcsRUFBRSxZQUFZO3dCQUN6QixlQUFlLEVBQUUsSUFBSSxDQUFDLGVBQWU7d0JBQ3JDLGVBQWUsRUFBRSxJQUFJLElBQUksRUFBRTt3QkFDM0IsUUFBUSxFQUFFLElBQUksSUFBSSxFQUFFO3dCQUNwQixTQUFTLEVBQUUsTUFBTTtxQkFDbEIsQ0FBQyxDQUFDO29CQUVILEtBQUssQ0FBQyxrQkFBa0IsRUFBRSxDQUFDO2dCQUM3QixDQUFDO2dCQUVELE1BQU0sVUFBVSxHQUFHLE1BQU0sSUFBSSxDQUFDLGVBQWUsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7Z0JBQzFELGdCQUFnQixDQUFDLElBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQztZQUNwQyxDQUFDO1lBRUQsTUFBTSxJQUFJLENBQUMsWUFBWSxDQUFDLGFBQWEsQ0FDbkMsTUFBTSxFQUNOLFVBQVUsRUFDVixPQUFPLEVBQ1AsSUFBSSxFQUNKO2dCQUNFLGVBQWUsRUFBRSxnQkFBZ0IsQ0FBQyxNQUFNO2dCQUN4QyxNQUFNLEVBQUUsYUFBYSxDQUFDLENBQUMsQ0FBQyxFQUFFLGVBQWU7YUFDMUMsQ0FDRixDQUFDO1lBRUYsSUFBSSxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsMkJBQTJCLEVBQUU7Z0JBQzNDLGVBQWUsRUFBRSxnQkFBZ0IsQ0FBQyxNQUFNO2dCQUN4QyxNQUFNO2FBQ1AsQ0FBQyxDQUFDO1lBRUgsT0FBTyxnQkFBZ0IsQ0FBQztRQUMxQixDQUFDO1FBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztZQUNmLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLDJCQUEyQixFQUFFO2dCQUM3QyxLQUFLLEVBQUUsS0FBSyxDQUFDLE9BQU87Z0JBQ3BCLE1BQU07YUFDUCxDQUFDLENBQUM7WUFDSCxNQUFNLEtBQUssQ0FBQztRQUNkLENBQUM7SUFDSCxDQUFDO0lBRUQ7Ozs7T0FJRztJQUNILEtBQUssQ0FBQyx5QkFBeUIsQ0FBQyxPQUFlO1FBQzdDLElBQUksQ0FBQztZQUNILE1BQU0sS0FBSyxHQUFHLE1BQU0sSUFBSSxDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsQ0FBQztZQUMzQyxJQUFJLENBQUMsS0FBSyxFQUFFLENBQUM7Z0JBQ1gsTUFBTSxJQUFJLDBCQUFpQixDQUFDLGlCQUFpQixDQUFDLENBQUM7WUFDakQsQ0FBQztZQUVELG9DQUFvQztZQUNwQyxNQUFNLG1CQUFtQixHQUFHLE1BQU0sSUFBSSxDQUFDLHVCQUF1QjtpQkFDM0Qsa0JBQWtCLENBQUMsTUFBTSxDQUFDO2lCQUMxQixNQUFNLENBQUMsZUFBZSxFQUFFLFVBQVUsQ0FBQztpQkFDbkMsU0FBUyxDQUFDLFVBQVUsRUFBRSxPQUFPLENBQUM7aUJBQzlCLEtBQUssQ0FBQyx5QkFBeUIsRUFBRSxFQUFFLE9BQU8sRUFBRSxDQUFDO2lCQUM3QyxRQUFRLENBQUMsK0JBQStCLEVBQUUsRUFBRSxRQUFRLEVBQUUsQ0FBQyxNQUFNLEVBQUUsV0FBVyxDQUFDLEVBQUUsQ0FBQztpQkFDOUUsT0FBTyxDQUFDLGVBQWUsQ0FBQztpQkFDeEIsVUFBVSxFQUFFLENBQUM7WUFFaEIsTUFBTSxNQUFNLEdBQUc7Z0JBQ2IsUUFBUSxFQUFFLENBQUM7Z0JBQ1gsSUFBSSxFQUFFLENBQUM7Z0JBQ1AsTUFBTSxFQUFFLENBQUM7Z0JBQ1QsR0FBRyxFQUFFLENBQUM7Z0JBQ04sSUFBSSxFQUFFLENBQUM7Z0JBQ1AsS0FBSyxFQUFFLENBQUM7YUFDVCxDQUFDO1lBRUYsbUJBQW1CLENBQUMsT0FBTyxDQUFDLENBQUMsRUFBRSxRQUFRLEVBQUUsS0FBSyxFQUFFLEVBQUUsRUFBRTtnQkFDbEQsTUFBTSxDQUFDLFFBQVEsQ0FBQyxHQUFHLFFBQVEsQ0FBQyxLQUFLLENBQUMsQ0FBQztnQkFDbkMsTUFBTSxDQUFDLEtBQUssSUFBSSxRQUFRLENBQUMsS0FBSyxDQUFDLENBQUM7WUFDbEMsQ0FBQyxDQUFDLENBQUM7WUFFSCxLQUFLLENBQUMseUJBQXlCLENBQUMsTUFBTSxDQUFDLENBQUM7WUFDeEMsT0FBTyxNQUFNLElBQUksQ0FBQyxlQUFlLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDO1FBQ2hELENBQUM7UUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1lBQ2YsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsdUNBQXVDLEVBQUU7Z0JBQ3pELE9BQU87Z0JBQ1AsS0FBSyxFQUFFLEtBQUssQ0FBQyxPQUFPO2FBQ3JCLENBQUMsQ0FBQztZQUNILE1BQU0sS0FBSyxDQUFDO1FBQ2QsQ0FBQztJQUNILENBQUM7SUFFRDs7O09BR0c7SUFDSCxLQUFLLENBQUMsdUJBQXVCO1FBQzNCLElBQUksQ0FBQztZQUNILE9BQU8sTUFBTSxJQUFJLENBQUMsZUFBZTtpQkFDOUIsa0JBQWtCLENBQUMsT0FBTyxDQUFDO2lCQUMzQixLQUFLLENBQUMsZ0NBQWdDLEVBQUUsRUFBRSxTQUFTLEVBQUUsSUFBSSxFQUFFLENBQUM7aUJBQzVELFFBQVEsQ0FBQyx3QkFBd0IsRUFBRSxFQUFFLE1BQU0sRUFBRSxRQUFRLEVBQUUsQ0FBQztpQkFDeEQsUUFBUSxDQUNQLHlHQUF5RyxDQUMxRztpQkFDQSxPQUFPLENBQUMsbUJBQW1CLEVBQUUsTUFBTSxDQUFDO2lCQUNwQyxVQUFVLENBQUMsb0JBQW9CLEVBQUUsS0FBSyxDQUFDO2lCQUN2QyxPQUFPLEVBQUUsQ0FBQztRQUNmLENBQUM7UUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1lBQ2YsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsdUNBQXVDLEVBQUU7Z0JBQ3pELEtBQUssRUFBRSxLQUFLLENBQUMsT0FBTzthQUNyQixDQUFDLENBQUM7WUFDSCxNQUFNLEtBQUssQ0FBQztRQUNkLENBQUM7SUFDSCxDQUFDO0lBRUQ7OztPQUdHO0lBQ0gsS0FBSyxDQUFDLGFBQWE7UUFDakIsSUFBSSxDQUFDO1lBQ0gsTUFBTSxDQUNKLFdBQVcsRUFDWCxZQUFZLEVBQ1osY0FBYyxFQUNkLFlBQVksRUFDWixtQkFBbUIsRUFDbkIseUJBQXlCLEVBQzFCLEdBQUcsTUFBTSxPQUFPLENBQUMsR0FBRyxDQUFDO2dCQUNwQixJQUFJLENBQUMsZUFBZSxDQUFDLEtBQUssRUFBRTtnQkFDNUIsSUFBSSxDQUFDLGVBQWUsQ0FBQyxLQUFLLENBQUMsRUFBRSxLQUFLLEVBQUUsRUFBRSxNQUFNLEVBQUUsUUFBUSxFQUFFLEVBQUUsQ0FBQztnQkFDM0QsSUFBSSxDQUFDLGVBQWUsQ0FBQyxLQUFLLENBQUMsRUFBRSxLQUFLLEVBQUUsRUFBRSxXQUFXLEVBQUUsVUFBVSxFQUFFLEVBQUUsQ0FBQztnQkFDbEUsSUFBSSxDQUFDLG9CQUFvQixFQUFFO2dCQUMzQixJQUFJLENBQUMsMkJBQTJCLEVBQUU7Z0JBQ2xDLElBQUksQ0FBQyw0QkFBNEIsRUFBRTthQUNwQyxDQUFDLENBQUM7WUFFSCxPQUFPO2dCQUNMLEtBQUssRUFBRSxXQUFXO2dCQUNsQixNQUFNLEVBQUUsWUFBWTtnQkFDcEIsUUFBUSxFQUFFLGNBQWM7Z0JBQ3hCLE1BQU0sRUFBRSxZQUFZO2dCQUNwQixhQUFhLEVBQUUsbUJBQW1CO2dCQUNsQyxtQkFBbUIsRUFBRSx5QkFBeUI7Z0JBQzlDLFNBQVMsRUFBRSxJQUFJLElBQUksRUFBRSxDQUFDLFdBQVcsRUFBRTthQUNwQyxDQUFDO1FBQ0osQ0FBQztRQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7WUFDZixJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxnQ0FBZ0MsRUFBRTtnQkFDbEQsS0FBSyxFQUFFLEtBQUssQ0FBQyxPQUFPO2FBQ3JCLENBQUMsQ0FBQztZQUNILE1BQU0sS0FBSyxDQUFDO1FBQ2QsQ0FBQztJQUNILENBQUM7SUFFRDs7OztPQUlHO0lBQ0ssa0JBQWtCLENBQUMsSUFBUztRQUNsQyxJQUFJLElBQUksQ0FBQyxRQUFRLEVBQUUsQ0FBQztZQUNsQixNQUFNLFlBQVksR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsV0FBVyxFQUFFLENBQUMsQ0FBQztZQUVsRSxJQUFJLFlBQVksQ0FBQyxRQUFRLENBQUMsTUFBTSxDQUFDLElBQUksWUFBWSxDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsRUFBRSxDQUFDO2dCQUNwRSxPQUFPLGlCQUFpQixDQUFDO1lBQzNCLENBQUM7WUFDRCxJQUFJLFlBQVksQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUFDLElBQUksWUFBWSxDQUFDLFFBQVEsQ0FBQyxLQUFLLENBQUMsRUFBRSxDQUFDO2dCQUNqRSxPQUFPLFFBQVEsQ0FBQztZQUNsQixDQUFDO1lBQ0QsSUFBSSxZQUFZLENBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQyxFQUFFLENBQUM7Z0JBQ2xDLE9BQU8sZ0JBQWdCLENBQUM7WUFDMUIsQ0FBQztRQUNILENBQUM7UUFFRCxJQUFJLElBQUksQ0FBQyxlQUFlLEVBQUUsQ0FBQztZQUN6QixNQUFNLEVBQUUsR0FBRyxJQUFJLENBQUMsZUFBZSxDQUFDLElBQUksRUFBRSxXQUFXLEVBQUUsSUFBSSxFQUFFLENBQUM7WUFDMUQsSUFBSSxFQUFFLENBQUMsUUFBUSxDQUFDLFNBQVMsQ0FBQyxJQUFJLEVBQUUsQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLElBQUksRUFBRSxDQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUMsRUFBRSxDQUFDO2dCQUMxRSxPQUFPLFFBQVEsQ0FBQztZQUNsQixDQUFDO1FBQ0gsQ0FBQztRQUVELE9BQU8sUUFBUSxDQUFDLENBQUMsZUFBZTtJQUNsQyxDQUFDO0lBRUQ7OztPQUdHO0lBQ0ssS0FBSyxDQUFDLG9CQUFvQjtRQUNoQyxNQUFNLE1BQU0sR0FBRyxNQUFNLElBQUksQ0FBQyxlQUFlO2FBQ3RDLGtCQUFrQixDQUFDLE9BQU8sQ0FBQzthQUMzQixNQUFNLENBQUMsWUFBWSxFQUFFLE1BQU0sQ0FBQzthQUM1QixTQUFTLENBQUMsVUFBVSxFQUFFLE9BQU8sQ0FBQzthQUM5QixPQUFPLENBQUMsWUFBWSxDQUFDO2FBQ3JCLFVBQVUsRUFBRSxDQUFDO1FBRWhCLE9BQU8sTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLEdBQUcsRUFBRSxJQUFJLEVBQUUsRUFBRTtZQUNqQyxHQUFHLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxHQUFHLFFBQVEsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7WUFDdEMsT0FBTyxHQUFHLENBQUM7UUFDYixDQUFDLEVBQUUsRUFBRSxDQUFDLENBQUM7SUFDVCxDQUFDO0lBRUQ7OztPQUdHO0lBQ0ssS0FBSyxDQUFDLDJCQUEyQjtRQUN2QyxNQUFNLE1BQU0sR0FBRyxNQUFNLElBQUksQ0FBQyxlQUFlO2FBQ3RDLGtCQUFrQixDQUFDLE9BQU8sQ0FBQzthQUMzQixNQUFNLENBQUMsbUJBQW1CLEVBQUUsYUFBYSxDQUFDO2FBQzFDLFNBQVMsQ0FBQyxVQUFVLEVBQUUsT0FBTyxDQUFDO2FBQzlCLE9BQU8sQ0FBQyxtQkFBbUIsQ0FBQzthQUM1QixVQUFVLEVBQUUsQ0FBQztRQUVoQixPQUFPLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxHQUFHLEVBQUUsSUFBSSxFQUFFLEVBQUU7WUFDakMsR0FBRyxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsR0FBRyxRQUFRLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBQzdDLE9BQU8sR0FBRyxDQUFDO1FBQ2IsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDO0lBQ1QsQ0FBQztJQUVEOzs7T0FHRztJQUNLLEtBQUssQ0FBQyw0QkFBNEI7UUFDeEMsTUFBTSxNQUFNLEdBQUcsTUFBTSxJQUFJLENBQUMsZUFBZTthQUN0QyxrQkFBa0IsQ0FBQyxPQUFPLENBQUM7YUFDM0IsU0FBUyxDQUFDLHVCQUF1QixFQUFFLE1BQU0sQ0FBQzthQUMxQyxLQUFLLENBQUMsK0JBQStCLEVBQUUsRUFBRSxRQUFRLEVBQUUsQ0FBQyxNQUFNLEVBQUUsV0FBVyxDQUFDLEVBQUUsQ0FBQzthQUMzRSxRQUFRLEVBQUUsQ0FBQztRQUVkLE9BQU8sTUFBTSxDQUFDO0lBQ2hCLENBQUM7Q0FDRixDQUFBO0FBcm1CWSx3REFBc0I7aUNBQXRCLHNCQUFzQjtJQURsQyxJQUFBLG1CQUFVLEdBQUU7SUFLUixXQUFBLElBQUEsMEJBQWdCLEVBQUMsb0JBQUssQ0FBQyxDQUFBO0lBRXZCLFdBQUEsSUFBQSwwQkFBZ0IsRUFBQyxvQ0FBYSxDQUFDLENBQUE7eURBREUsb0JBQVUsb0JBQVYsb0JBQVUsb0RBRUYsb0JBQVUsb0JBQVYsb0JBQVUsb0RBQ3BCLDhCQUFhLG9CQUFiLDhCQUFhLG9EQUNkLDRCQUFZLG9CQUFaLDRCQUFZO0dBVGxDLHNCQUFzQixDQXFtQmxDIiwibmFtZXMiOltdLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTHVrYVxcc2VudGluZWxcXGJhY2tlbmRcXHNyY1xcbW9kdWxlc1xcdnVsbmVyYWJpbGl0eS1tYW5hZ2VtZW50XFxhcHBsaWNhdGlvblxcc2VydmljZXNcXGFzc2V0LW1hbmFnZW1lbnQuc2VydmljZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBJbmplY3RhYmxlLCBMb2dnZXIsIE5vdEZvdW5kRXhjZXB0aW9uLCBCYWRSZXF1ZXN0RXhjZXB0aW9uIH0gZnJvbSAnQG5lc3Rqcy9jb21tb24nO1xyXG5pbXBvcnQgeyBJbmplY3RSZXBvc2l0b3J5IH0gZnJvbSAnQG5lc3Rqcy90eXBlb3JtJztcclxuaW1wb3J0IHsgUmVwb3NpdG9yeSwgRmluZE1hbnlPcHRpb25zLCBMaWtlLCBJbiwgQmV0d2VlbiB9IGZyb20gJ3R5cGVvcm0nO1xyXG5pbXBvcnQgeyBBc3NldCB9IGZyb20gJy4uLy4uL2RvbWFpbi9lbnRpdGllcy9hc3NldC5lbnRpdHknO1xyXG5pbXBvcnQgeyBWdWxuZXJhYmlsaXR5IH0gZnJvbSAnLi4vLi4vZG9tYWluL2VudGl0aWVzL3Z1bG5lcmFiaWxpdHkuZW50aXR5JztcclxuaW1wb3J0IHsgTG9nZ2VyU2VydmljZSB9IGZyb20gJy4uLy4uLy4uLy4uL2luZnJhc3RydWN0dXJlL2xvZ2dpbmcvbG9nZ2VyLnNlcnZpY2UnO1xyXG5pbXBvcnQgeyBBdWRpdFNlcnZpY2UgfSBmcm9tICcuLi8uLi8uLi8uLi9pbmZyYXN0cnVjdHVyZS9sb2dnaW5nL2F1ZGl0L2F1ZGl0LnNlcnZpY2UnO1xyXG5cclxuLyoqXHJcbiAqIEFzc2V0IE1hbmFnZW1lbnQgc2VydmljZVxyXG4gKiBIYW5kbGVzIGFzc2V0IGRpc2NvdmVyeSwgaW52ZW50b3J5LCBhbmQgbGlmZWN5Y2xlIG1hbmFnZW1lbnRcclxuICovXHJcbkBJbmplY3RhYmxlKClcclxuZXhwb3J0IGNsYXNzIEFzc2V0TWFuYWdlbWVudFNlcnZpY2Uge1xyXG4gIHByaXZhdGUgcmVhZG9ubHkgbG9nZ2VyID0gbmV3IExvZ2dlcihBc3NldE1hbmFnZW1lbnRTZXJ2aWNlLm5hbWUpO1xyXG5cclxuICBjb25zdHJ1Y3RvcihcclxuICAgIEBJbmplY3RSZXBvc2l0b3J5KEFzc2V0KVxyXG4gICAgcHJpdmF0ZSByZWFkb25seSBhc3NldFJlcG9zaXRvcnk6IFJlcG9zaXRvcnk8QXNzZXQ+LFxyXG4gICAgQEluamVjdFJlcG9zaXRvcnkoVnVsbmVyYWJpbGl0eSlcclxuICAgIHByaXZhdGUgcmVhZG9ubHkgdnVsbmVyYWJpbGl0eVJlcG9zaXRvcnk6IFJlcG9zaXRvcnk8VnVsbmVyYWJpbGl0eT4sXHJcbiAgICBwcml2YXRlIHJlYWRvbmx5IGxvZ2dlclNlcnZpY2U6IExvZ2dlclNlcnZpY2UsXHJcbiAgICBwcml2YXRlIHJlYWRvbmx5IGF1ZGl0U2VydmljZTogQXVkaXRTZXJ2aWNlLFxyXG4gICkge31cclxuXHJcbiAgLyoqXHJcbiAgICogQ3JlYXRlIGEgbmV3IGFzc2V0XHJcbiAgICogQHBhcmFtIGFzc2V0RGF0YSBBc3NldCBjcmVhdGlvbiBkYXRhXHJcbiAgICogQHBhcmFtIHVzZXJJZCBVc2VyIGNyZWF0aW5nIHRoZSBhc3NldFxyXG4gICAqIEByZXR1cm5zIENyZWF0ZWQgYXNzZXRcclxuICAgKi9cclxuICBhc3luYyBjcmVhdGVBc3NldChcclxuICAgIGFzc2V0RGF0YToge1xyXG4gICAgICBuYW1lOiBzdHJpbmc7XHJcbiAgICAgIHR5cGU6IHN0cmluZztcclxuICAgICAgZGVzY3JpcHRpb24/OiBzdHJpbmc7XHJcbiAgICAgIGlwQWRkcmVzcz86IHN0cmluZztcclxuICAgICAgaG9zdG5hbWU/OiBzdHJpbmc7XHJcbiAgICAgIGNyaXRpY2FsaXR5PzogJ2xvdycgfCAnbWVkaXVtJyB8ICdoaWdoJyB8ICdjcml0aWNhbCc7XHJcbiAgICAgIGVudmlyb25tZW50PzogJ3Byb2R1Y3Rpb24nIHwgJ3N0YWdpbmcnIHwgJ2RldmVsb3BtZW50JyB8ICd0ZXN0aW5nJyB8ICdzYW5kYm94JztcclxuICAgICAgb3duZXI/OiBhbnk7XHJcbiAgICAgIGxvY2F0aW9uPzogYW55O1xyXG4gICAgICB0YWdzPzogc3RyaW5nW107XHJcbiAgICAgIG1ldGFkYXRhPzogUmVjb3JkPHN0cmluZywgYW55PjtcclxuICAgIH0sXHJcbiAgICB1c2VySWQ6IHN0cmluZyxcclxuICApOiBQcm9taXNlPEFzc2V0PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICB0aGlzLmxvZ2dlci5kZWJ1ZygnQ3JlYXRpbmcgbmV3IGFzc2V0Jywge1xyXG4gICAgICAgIG5hbWU6IGFzc2V0RGF0YS5uYW1lLFxyXG4gICAgICAgIHR5cGU6IGFzc2V0RGF0YS50eXBlLFxyXG4gICAgICAgIHVzZXJJZCxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICAvLyBDaGVjayBmb3IgZHVwbGljYXRlIGFzc2V0c1xyXG4gICAgICBpZiAoYXNzZXREYXRhLmlwQWRkcmVzcykge1xyXG4gICAgICAgIGNvbnN0IGV4aXN0aW5nQXNzZXQgPSBhd2FpdCB0aGlzLmFzc2V0UmVwb3NpdG9yeS5maW5kT25lKHtcclxuICAgICAgICAgIHdoZXJlOiB7IGlwQWRkcmVzczogYXNzZXREYXRhLmlwQWRkcmVzcyB9LFxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIGlmIChleGlzdGluZ0Fzc2V0KSB7XHJcbiAgICAgICAgICB0aHJvdyBuZXcgQmFkUmVxdWVzdEV4Y2VwdGlvbihgQXNzZXQgd2l0aCBJUCBhZGRyZXNzICR7YXNzZXREYXRhLmlwQWRkcmVzc30gYWxyZWFkeSBleGlzdHNgKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IGFzc2V0ID0gdGhpcy5hc3NldFJlcG9zaXRvcnkuY3JlYXRlKHtcclxuICAgICAgICAuLi5hc3NldERhdGEsXHJcbiAgICAgICAgc3RhdHVzOiAnYWN0aXZlJyxcclxuICAgICAgICBmaXJzdERpc2NvdmVyZWQ6IG5ldyBEYXRlKCksXHJcbiAgICAgICAgbGFzdFNlZW46IG5ldyBEYXRlKCksXHJcbiAgICAgICAgY3JlYXRlZEJ5OiB1c2VySWQsXHJcbiAgICAgICAgZGlzY292ZXJ5TWV0aG9kOiAnbWFudWFsJyxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICAvLyBDYWxjdWxhdGUgaW5pdGlhbCByaXNrIHNjb3JlXHJcbiAgICAgIGFzc2V0LmNhbGN1bGF0ZVJpc2tTY29yZSgpO1xyXG5cclxuICAgICAgY29uc3Qgc2F2ZWRBc3NldCA9IGF3YWl0IHRoaXMuYXNzZXRSZXBvc2l0b3J5LnNhdmUoYXNzZXQpO1xyXG5cclxuICAgICAgYXdhaXQgdGhpcy5hdWRpdFNlcnZpY2UubG9nVXNlckFjdGlvbihcclxuICAgICAgICB1c2VySWQsXHJcbiAgICAgICAgJ2NyZWF0ZScsXHJcbiAgICAgICAgJ2Fzc2V0JyxcclxuICAgICAgICBzYXZlZEFzc2V0LmlkLFxyXG4gICAgICAgIHtcclxuICAgICAgICAgIG5hbWU6IGFzc2V0RGF0YS5uYW1lLFxyXG4gICAgICAgICAgdHlwZTogYXNzZXREYXRhLnR5cGUsXHJcbiAgICAgICAgICBpcEFkZHJlc3M6IGFzc2V0RGF0YS5pcEFkZHJlc3MsXHJcbiAgICAgICAgfSxcclxuICAgICAgKTtcclxuXHJcbiAgICAgIHRoaXMubG9nZ2VyLmxvZygnQXNzZXQgY3JlYXRlZCBzdWNjZXNzZnVsbHknLCB7XHJcbiAgICAgICAgYXNzZXRJZDogc2F2ZWRBc3NldC5pZCxcclxuICAgICAgICBuYW1lOiBhc3NldERhdGEubmFtZSxcclxuICAgICAgICB1c2VySWQsXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgcmV0dXJuIHNhdmVkQXNzZXQ7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICB0aGlzLmxvZ2dlci5lcnJvcignRmFpbGVkIHRvIGNyZWF0ZSBhc3NldCcsIHtcclxuICAgICAgICBlcnJvcjogZXJyb3IubWVzc2FnZSxcclxuICAgICAgICBhc3NldERhdGEsXHJcbiAgICAgICAgdXNlcklkLFxyXG4gICAgICB9KTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBGaW5kIGFzc2V0IGJ5IElEXHJcbiAgICogQHBhcmFtIGlkIEFzc2V0IElEXHJcbiAgICogQHJldHVybnMgQXNzZXQgb3IgbnVsbFxyXG4gICAqL1xyXG4gIGFzeW5jIGZpbmRCeUlkKGlkOiBzdHJpbmcpOiBQcm9taXNlPEFzc2V0IHwgbnVsbD4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgcmV0dXJuIGF3YWl0IHRoaXMuYXNzZXRSZXBvc2l0b3J5LmZpbmRPbmUoe1xyXG4gICAgICAgIHdoZXJlOiB7IGlkIH0sXHJcbiAgICAgICAgcmVsYXRpb25zOiBbJ3Z1bG5lcmFiaWxpdGllcycsICdzY2FuUmVzdWx0cyddLFxyXG4gICAgICB9KTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIHRoaXMubG9nZ2VyLmVycm9yKCdGYWlsZWQgdG8gZmluZCBhc3NldCBieSBJRCcsIHtcclxuICAgICAgICBpZCxcclxuICAgICAgICBlcnJvcjogZXJyb3IubWVzc2FnZSxcclxuICAgICAgfSk7XHJcbiAgICAgIHRocm93IGVycm9yO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogRmluZCBhc3NldHMgd2l0aCBmaWx0ZXJpbmcgYW5kIHBhZ2luYXRpb25cclxuICAgKiBAcGFyYW0gb3B0aW9ucyBRdWVyeSBvcHRpb25zXHJcbiAgICogQHJldHVybnMgUGFnaW5hdGVkIGFzc2V0c1xyXG4gICAqL1xyXG4gIGFzeW5jIGZpbmRNYW55KG9wdGlvbnM6IHtcclxuICAgIHBhZ2U/OiBudW1iZXI7XHJcbiAgICBsaW1pdD86IG51bWJlcjtcclxuICAgIHR5cGU/OiBzdHJpbmdbXTtcclxuICAgIHN0YXR1cz86IHN0cmluZ1tdO1xyXG4gICAgY3JpdGljYWxpdHk/OiBzdHJpbmdbXTtcclxuICAgIGVudmlyb25tZW50Pzogc3RyaW5nW107XHJcbiAgICBzZWFyY2g/OiBzdHJpbmc7XHJcbiAgICB0YWdzPzogc3RyaW5nW107XHJcbiAgICBpcEFkZHJlc3M/OiBzdHJpbmc7XHJcbiAgICBob3N0bmFtZT86IHN0cmluZztcclxuICAgIHNvcnRCeT86IHN0cmluZztcclxuICAgIHNvcnRPcmRlcj86ICdBU0MnIHwgJ0RFU0MnO1xyXG4gIH0pOiBQcm9taXNlPHsgYXNzZXRzOiBBc3NldFtdOyB0b3RhbDogbnVtYmVyOyBwYWdlOiBudW1iZXI7IHRvdGFsUGFnZXM6IG51bWJlciB9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCB7XHJcbiAgICAgICAgcGFnZSA9IDEsXHJcbiAgICAgICAgbGltaXQgPSAyMCxcclxuICAgICAgICB0eXBlLFxyXG4gICAgICAgIHN0YXR1cyxcclxuICAgICAgICBjcml0aWNhbGl0eSxcclxuICAgICAgICBlbnZpcm9ubWVudCxcclxuICAgICAgICBzZWFyY2gsXHJcbiAgICAgICAgdGFncyxcclxuICAgICAgICBpcEFkZHJlc3MsXHJcbiAgICAgICAgaG9zdG5hbWUsXHJcbiAgICAgICAgc29ydEJ5ID0gJ2NyZWF0ZWRBdCcsXHJcbiAgICAgICAgc29ydE9yZGVyID0gJ0RFU0MnLFxyXG4gICAgICB9ID0gb3B0aW9ucztcclxuXHJcbiAgICAgIGNvbnN0IHF1ZXJ5QnVpbGRlciA9IHRoaXMuYXNzZXRSZXBvc2l0b3J5LmNyZWF0ZVF1ZXJ5QnVpbGRlcignYXNzZXQnKTtcclxuXHJcbiAgICAgIC8vIEFwcGx5IGZpbHRlcnNcclxuICAgICAgaWYgKHR5cGUgJiYgdHlwZS5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgcXVlcnlCdWlsZGVyLmFuZFdoZXJlKCdhc3NldC50eXBlIElOICg6Li4udHlwZSknLCB7IHR5cGUgfSk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGlmIChzdGF0dXMgJiYgc3RhdHVzLmxlbmd0aCA+IDApIHtcclxuICAgICAgICBxdWVyeUJ1aWxkZXIuYW5kV2hlcmUoJ2Fzc2V0LnN0YXR1cyBJTiAoOi4uLnN0YXR1cyknLCB7IHN0YXR1cyB9KTtcclxuICAgICAgfVxyXG5cclxuICAgICAgaWYgKGNyaXRpY2FsaXR5ICYmIGNyaXRpY2FsaXR5Lmxlbmd0aCA+IDApIHtcclxuICAgICAgICBxdWVyeUJ1aWxkZXIuYW5kV2hlcmUoJ2Fzc2V0LmNyaXRpY2FsaXR5IElOICg6Li4uY3JpdGljYWxpdHkpJywgeyBjcml0aWNhbGl0eSB9KTtcclxuICAgICAgfVxyXG5cclxuICAgICAgaWYgKGVudmlyb25tZW50ICYmIGVudmlyb25tZW50Lmxlbmd0aCA+IDApIHtcclxuICAgICAgICBxdWVyeUJ1aWxkZXIuYW5kV2hlcmUoJ2Fzc2V0LmVudmlyb25tZW50IElOICg6Li4uZW52aXJvbm1lbnQpJywgeyBlbnZpcm9ubWVudCB9KTtcclxuICAgICAgfVxyXG5cclxuICAgICAgaWYgKHNlYXJjaCkge1xyXG4gICAgICAgIHF1ZXJ5QnVpbGRlci5hbmRXaGVyZShcclxuICAgICAgICAgICcoYXNzZXQubmFtZSBJTElLRSA6c2VhcmNoIE9SIGFzc2V0LmRlc2NyaXB0aW9uIElMSUtFIDpzZWFyY2ggT1IgYXNzZXQuaG9zdG5hbWUgSUxJS0UgOnNlYXJjaCknLFxyXG4gICAgICAgICAgeyBzZWFyY2g6IGAlJHtzZWFyY2h9JWAgfSxcclxuICAgICAgICApO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBpZiAodGFncyAmJiB0YWdzLmxlbmd0aCA+IDApIHtcclxuICAgICAgICBxdWVyeUJ1aWxkZXIuYW5kV2hlcmUoJ2Fzc2V0LnRhZ3MgPyYgOnRhZ3MnLCB7IHRhZ3MgfSk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGlmIChpcEFkZHJlc3MpIHtcclxuICAgICAgICBxdWVyeUJ1aWxkZXIuYW5kV2hlcmUoJ2Fzc2V0LmlwQWRkcmVzcyA9IDppcEFkZHJlc3MnLCB7IGlwQWRkcmVzcyB9KTtcclxuICAgICAgfVxyXG5cclxuICAgICAgaWYgKGhvc3RuYW1lKSB7XHJcbiAgICAgICAgcXVlcnlCdWlsZGVyLmFuZFdoZXJlKCdhc3NldC5ob3N0bmFtZSBJTElLRSA6aG9zdG5hbWUnLCB7IGhvc3RuYW1lOiBgJSR7aG9zdG5hbWV9JWAgfSk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIEFwcGx5IHNvcnRpbmdcclxuICAgICAgcXVlcnlCdWlsZGVyLm9yZGVyQnkoYGFzc2V0LiR7c29ydEJ5fWAsIHNvcnRPcmRlcik7XHJcblxyXG4gICAgICAvLyBBcHBseSBwYWdpbmF0aW9uXHJcbiAgICAgIGNvbnN0IG9mZnNldCA9IChwYWdlIC0gMSkgKiBsaW1pdDtcclxuICAgICAgcXVlcnlCdWlsZGVyLnNraXAob2Zmc2V0KS50YWtlKGxpbWl0KTtcclxuXHJcbiAgICAgIGNvbnN0IFthc3NldHMsIHRvdGFsXSA9IGF3YWl0IHF1ZXJ5QnVpbGRlci5nZXRNYW55QW5kQ291bnQoKTtcclxuICAgICAgY29uc3QgdG90YWxQYWdlcyA9IE1hdGguY2VpbCh0b3RhbCAvIGxpbWl0KTtcclxuXHJcbiAgICAgIHRoaXMubG9nZ2VyLmRlYnVnKCdBc3NldHMgcmV0cmlldmVkJywge1xyXG4gICAgICAgIHRvdGFsLFxyXG4gICAgICAgIHBhZ2UsXHJcbiAgICAgICAgbGltaXQsXHJcbiAgICAgICAgdG90YWxQYWdlcyxcclxuICAgICAgICBmaWx0ZXJzOiB7IHR5cGUsIHN0YXR1cywgY3JpdGljYWxpdHksIGVudmlyb25tZW50LCBzZWFyY2gsIHRhZ3MgfSxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICByZXR1cm4ge1xyXG4gICAgICAgIGFzc2V0cyxcclxuICAgICAgICB0b3RhbCxcclxuICAgICAgICBwYWdlLFxyXG4gICAgICAgIHRvdGFsUGFnZXMsXHJcbiAgICAgIH07XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICB0aGlzLmxvZ2dlci5lcnJvcignRmFpbGVkIHRvIGZpbmQgYXNzZXRzJywge1xyXG4gICAgICAgIGVycm9yOiBlcnJvci5tZXNzYWdlLFxyXG4gICAgICAgIG9wdGlvbnMsXHJcbiAgICAgIH0pO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIFVwZGF0ZSBhc3NldFxyXG4gICAqIEBwYXJhbSBpZCBBc3NldCBJRFxyXG4gICAqIEBwYXJhbSB1cGRhdGVEYXRhIFVwZGF0ZSBkYXRhXHJcbiAgICogQHBhcmFtIHVzZXJJZCBVc2VyIHVwZGF0aW5nIHRoZSBhc3NldFxyXG4gICAqIEByZXR1cm5zIFVwZGF0ZWQgYXNzZXRcclxuICAgKi9cclxuICBhc3luYyB1cGRhdGVBc3NldChcclxuICAgIGlkOiBzdHJpbmcsXHJcbiAgICB1cGRhdGVEYXRhOiBQYXJ0aWFsPEFzc2V0PixcclxuICAgIHVzZXJJZDogc3RyaW5nLFxyXG4gICk6IFByb21pc2U8QXNzZXQ+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IGFzc2V0ID0gYXdhaXQgdGhpcy5maW5kQnlJZChpZCk7XHJcbiAgICAgIGlmICghYXNzZXQpIHtcclxuICAgICAgICB0aHJvdyBuZXcgTm90Rm91bmRFeGNlcHRpb24oJ0Fzc2V0IG5vdCBmb3VuZCcpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICB0aGlzLmxvZ2dlci5kZWJ1ZygnVXBkYXRpbmcgYXNzZXQnLCB7XHJcbiAgICAgICAgYXNzZXRJZDogaWQsXHJcbiAgICAgICAgdXBkYXRlRGF0YSxcclxuICAgICAgICB1c2VySWQsXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgLy8gVXBkYXRlIGFzc2V0IHByb3BlcnRpZXNcclxuICAgICAgT2JqZWN0LmFzc2lnbihhc3NldCwgdXBkYXRlRGF0YSwge1xyXG4gICAgICAgIHVwZGF0ZWRCeTogdXNlcklkLFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIC8vIFJlY2FsY3VsYXRlIHJpc2sgc2NvcmUgaWYgcmVsZXZhbnQgZmllbGRzIGNoYW5nZWRcclxuICAgICAgaWYgKHVwZGF0ZURhdGEuY3JpdGljYWxpdHkgfHwgdXBkYXRlRGF0YS52dWxuZXJhYmlsaXR5Q291bnRzKSB7XHJcbiAgICAgICAgYXNzZXQuY2FsY3VsYXRlUmlza1Njb3JlKCk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IHVwZGF0ZWRBc3NldCA9IGF3YWl0IHRoaXMuYXNzZXRSZXBvc2l0b3J5LnNhdmUoYXNzZXQpO1xyXG5cclxuICAgICAgYXdhaXQgdGhpcy5hdWRpdFNlcnZpY2UubG9nVXNlckFjdGlvbihcclxuICAgICAgICB1c2VySWQsXHJcbiAgICAgICAgJ3VwZGF0ZScsXHJcbiAgICAgICAgJ2Fzc2V0JyxcclxuICAgICAgICBpZCxcclxuICAgICAgICB7XHJcbiAgICAgICAgICBjaGFuZ2VzOiB1cGRhdGVEYXRhLFxyXG4gICAgICAgICAgcHJldmlvdXNWYWx1ZXM6IHtcclxuICAgICAgICAgICAgbmFtZTogYXNzZXQubmFtZSxcclxuICAgICAgICAgICAgc3RhdHVzOiBhc3NldC5zdGF0dXMsXHJcbiAgICAgICAgICAgIGNyaXRpY2FsaXR5OiBhc3NldC5jcml0aWNhbGl0eSxcclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgfSxcclxuICAgICAgKTtcclxuXHJcbiAgICAgIHRoaXMubG9nZ2VyLmxvZygnQXNzZXQgdXBkYXRlZCBzdWNjZXNzZnVsbHknLCB7XHJcbiAgICAgICAgYXNzZXRJZDogaWQsXHJcbiAgICAgICAgdXNlcklkLFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIHJldHVybiB1cGRhdGVkQXNzZXQ7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICB0aGlzLmxvZ2dlci5lcnJvcignRmFpbGVkIHRvIHVwZGF0ZSBhc3NldCcsIHtcclxuICAgICAgICBhc3NldElkOiBpZCxcclxuICAgICAgICBlcnJvcjogZXJyb3IubWVzc2FnZSxcclxuICAgICAgICB1c2VySWQsXHJcbiAgICAgIH0pO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIERlbGV0ZSBhc3NldFxyXG4gICAqIEBwYXJhbSBpZCBBc3NldCBJRFxyXG4gICAqIEBwYXJhbSB1c2VySWQgVXNlciBkZWxldGluZyB0aGUgYXNzZXRcclxuICAgKi9cclxuICBhc3luYyBkZWxldGVBc3NldChpZDogc3RyaW5nLCB1c2VySWQ6IHN0cmluZyk6IFByb21pc2U8dm9pZD4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgYXNzZXQgPSBhd2FpdCB0aGlzLmZpbmRCeUlkKGlkKTtcclxuICAgICAgaWYgKCFhc3NldCkge1xyXG4gICAgICAgIHRocm93IG5ldyBOb3RGb3VuZEV4Y2VwdGlvbignQXNzZXQgbm90IGZvdW5kJyk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHRoaXMubG9nZ2VyLmRlYnVnKCdEZWxldGluZyBhc3NldCcsIHtcclxuICAgICAgICBhc3NldElkOiBpZCxcclxuICAgICAgICB1c2VySWQsXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgYXdhaXQgdGhpcy5hc3NldFJlcG9zaXRvcnkucmVtb3ZlKGFzc2V0KTtcclxuXHJcbiAgICAgIGF3YWl0IHRoaXMuYXVkaXRTZXJ2aWNlLmxvZ1VzZXJBY3Rpb24oXHJcbiAgICAgICAgdXNlcklkLFxyXG4gICAgICAgICdkZWxldGUnLFxyXG4gICAgICAgICdhc3NldCcsXHJcbiAgICAgICAgaWQsXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgbmFtZTogYXNzZXQubmFtZSxcclxuICAgICAgICAgIHR5cGU6IGFzc2V0LnR5cGUsXHJcbiAgICAgICAgICBpcEFkZHJlc3M6IGFzc2V0LmlwQWRkcmVzcyxcclxuICAgICAgICB9LFxyXG4gICAgICApO1xyXG5cclxuICAgICAgdGhpcy5sb2dnZXIubG9nKCdBc3NldCBkZWxldGVkIHN1Y2Nlc3NmdWxseScsIHtcclxuICAgICAgICBhc3NldElkOiBpZCxcclxuICAgICAgICB1c2VySWQsXHJcbiAgICAgIH0pO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgdGhpcy5sb2dnZXIuZXJyb3IoJ0ZhaWxlZCB0byBkZWxldGUgYXNzZXQnLCB7XHJcbiAgICAgICAgYXNzZXRJZDogaWQsXHJcbiAgICAgICAgZXJyb3I6IGVycm9yLm1lc3NhZ2UsXHJcbiAgICAgICAgdXNlcklkLFxyXG4gICAgICB9KTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBEaXNjb3ZlciBhc3NldHMgZnJvbSBuZXR3b3JrIHNjYW5cclxuICAgKiBAcGFyYW0gZGlzY292ZXJ5RGF0YSBEaXNjb3ZlcnkgZGF0YSBmcm9tIG5ldHdvcmsgc2NhblxyXG4gICAqIEBwYXJhbSB1c2VySWQgVXNlciBpbml0aWF0aW5nIGRpc2NvdmVyeVxyXG4gICAqIEByZXR1cm5zIERpc2NvdmVyZWQgYXNzZXRzXHJcbiAgICovXHJcbiAgYXN5bmMgZGlzY292ZXJBc3NldHMoXHJcbiAgICBkaXNjb3ZlcnlEYXRhOiBBcnJheTx7XHJcbiAgICAgIGlwQWRkcmVzczogc3RyaW5nO1xyXG4gICAgICBob3N0bmFtZT86IHN0cmluZztcclxuICAgICAgbWFjQWRkcmVzcz86IHN0cmluZztcclxuICAgICAgb3BlcmF0aW5nU3lzdGVtPzogYW55O1xyXG4gICAgICBzZXJ2aWNlcz86IGFueVtdO1xyXG4gICAgICBkaXNjb3ZlcnlNZXRob2Q6IHN0cmluZztcclxuICAgIH0+LFxyXG4gICAgdXNlcklkOiBzdHJpbmcsXHJcbiAgKTogUHJvbWlzZTxBc3NldFtdPiB7XHJcbiAgICB0cnkge1xyXG4gICAgICB0aGlzLmxvZ2dlci5kZWJ1ZygnRGlzY292ZXJpbmcgYXNzZXRzJywge1xyXG4gICAgICAgIGNvdW50OiBkaXNjb3ZlcnlEYXRhLmxlbmd0aCxcclxuICAgICAgICB1c2VySWQsXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgY29uc3QgZGlzY292ZXJlZEFzc2V0czogQXNzZXRbXSA9IFtdO1xyXG5cclxuICAgICAgZm9yIChjb25zdCBkYXRhIG9mIGRpc2NvdmVyeURhdGEpIHtcclxuICAgICAgICAvLyBDaGVjayBpZiBhc3NldCBhbHJlYWR5IGV4aXN0c1xyXG4gICAgICAgIGxldCBhc3NldCA9IGF3YWl0IHRoaXMuYXNzZXRSZXBvc2l0b3J5LmZpbmRPbmUoe1xyXG4gICAgICAgICAgd2hlcmU6IHsgaXBBZGRyZXNzOiBkYXRhLmlwQWRkcmVzcyB9LFxyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICBpZiAoYXNzZXQpIHtcclxuICAgICAgICAgIC8vIFVwZGF0ZSBleGlzdGluZyBhc3NldFxyXG4gICAgICAgICAgYXNzZXQudXBkYXRlTGFzdFNlZW4oKTtcclxuICAgICAgICAgIGFzc2V0Lmhvc3RuYW1lID0gZGF0YS5ob3N0bmFtZSB8fCBhc3NldC5ob3N0bmFtZTtcclxuICAgICAgICAgIGFzc2V0Lm1hY0FkZHJlc3MgPSBkYXRhLm1hY0FkZHJlc3MgfHwgYXNzZXQubWFjQWRkcmVzcztcclxuICAgICAgICAgIGFzc2V0Lm9wZXJhdGluZ1N5c3RlbSA9IGRhdGEub3BlcmF0aW5nU3lzdGVtIHx8IGFzc2V0Lm9wZXJhdGluZ1N5c3RlbTtcclxuICAgICAgICAgIGFzc2V0LnNlcnZpY2VzID0gZGF0YS5zZXJ2aWNlcyB8fCBhc3NldC5zZXJ2aWNlcztcclxuICAgICAgICAgIGFzc2V0LmRpc2NvdmVyeU1ldGhvZCA9IGRhdGEuZGlzY292ZXJ5TWV0aG9kO1xyXG4gICAgICAgICAgYXNzZXQudXBkYXRlZEJ5ID0gdXNlcklkO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAvLyBDcmVhdGUgbmV3IGFzc2V0XHJcbiAgICAgICAgICBhc3NldCA9IHRoaXMuYXNzZXRSZXBvc2l0b3J5LmNyZWF0ZSh7XHJcbiAgICAgICAgICAgIG5hbWU6IGRhdGEuaG9zdG5hbWUgfHwgZGF0YS5pcEFkZHJlc3MsXHJcbiAgICAgICAgICAgIHR5cGU6IHRoaXMuZGV0ZXJtaW5lQXNzZXRUeXBlKGRhdGEpLFxyXG4gICAgICAgICAgICBpcEFkZHJlc3M6IGRhdGEuaXBBZGRyZXNzLFxyXG4gICAgICAgICAgICBob3N0bmFtZTogZGF0YS5ob3N0bmFtZSxcclxuICAgICAgICAgICAgbWFjQWRkcmVzczogZGF0YS5tYWNBZGRyZXNzLFxyXG4gICAgICAgICAgICBvcGVyYXRpbmdTeXN0ZW06IGRhdGEub3BlcmF0aW5nU3lzdGVtLFxyXG4gICAgICAgICAgICBzZXJ2aWNlczogZGF0YS5zZXJ2aWNlcyxcclxuICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJyxcclxuICAgICAgICAgICAgY3JpdGljYWxpdHk6ICdtZWRpdW0nLFxyXG4gICAgICAgICAgICBlbnZpcm9ubWVudDogJ3Byb2R1Y3Rpb24nLFxyXG4gICAgICAgICAgICBkaXNjb3ZlcnlNZXRob2Q6IGRhdGEuZGlzY292ZXJ5TWV0aG9kLFxyXG4gICAgICAgICAgICBmaXJzdERpc2NvdmVyZWQ6IG5ldyBEYXRlKCksXHJcbiAgICAgICAgICAgIGxhc3RTZWVuOiBuZXcgRGF0ZSgpLFxyXG4gICAgICAgICAgICBjcmVhdGVkQnk6IHVzZXJJZCxcclxuICAgICAgICAgIH0pO1xyXG5cclxuICAgICAgICAgIGFzc2V0LmNhbGN1bGF0ZVJpc2tTY29yZSgpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgY29uc3Qgc2F2ZWRBc3NldCA9IGF3YWl0IHRoaXMuYXNzZXRSZXBvc2l0b3J5LnNhdmUoYXNzZXQpO1xyXG4gICAgICAgIGRpc2NvdmVyZWRBc3NldHMucHVzaChzYXZlZEFzc2V0KTtcclxuICAgICAgfVxyXG5cclxuICAgICAgYXdhaXQgdGhpcy5hdWRpdFNlcnZpY2UubG9nVXNlckFjdGlvbihcclxuICAgICAgICB1c2VySWQsXHJcbiAgICAgICAgJ2Rpc2NvdmVyJyxcclxuICAgICAgICAnYXNzZXQnLFxyXG4gICAgICAgIG51bGwsXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgZGlzY292ZXJlZENvdW50OiBkaXNjb3ZlcmVkQXNzZXRzLmxlbmd0aCxcclxuICAgICAgICAgIG1ldGhvZDogZGlzY292ZXJ5RGF0YVswXT8uZGlzY292ZXJ5TWV0aG9kLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICk7XHJcblxyXG4gICAgICB0aGlzLmxvZ2dlci5sb2coJ0Fzc2V0IGRpc2NvdmVyeSBjb21wbGV0ZWQnLCB7XHJcbiAgICAgICAgZGlzY292ZXJlZENvdW50OiBkaXNjb3ZlcmVkQXNzZXRzLmxlbmd0aCxcclxuICAgICAgICB1c2VySWQsXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgcmV0dXJuIGRpc2NvdmVyZWRBc3NldHM7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICB0aGlzLmxvZ2dlci5lcnJvcignRmFpbGVkIHRvIGRpc2NvdmVyIGFzc2V0cycsIHtcclxuICAgICAgICBlcnJvcjogZXJyb3IubWVzc2FnZSxcclxuICAgICAgICB1c2VySWQsXHJcbiAgICAgIH0pO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIFVwZGF0ZSBhc3NldCB2dWxuZXJhYmlsaXR5IGNvdW50c1xyXG4gICAqIEBwYXJhbSBhc3NldElkIEFzc2V0IElEXHJcbiAgICogQHJldHVybnMgVXBkYXRlZCBhc3NldFxyXG4gICAqL1xyXG4gIGFzeW5jIHVwZGF0ZVZ1bG5lcmFiaWxpdHlDb3VudHMoYXNzZXRJZDogc3RyaW5nKTogUHJvbWlzZTxBc3NldD4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgYXNzZXQgPSBhd2FpdCB0aGlzLmZpbmRCeUlkKGFzc2V0SWQpO1xyXG4gICAgICBpZiAoIWFzc2V0KSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IE5vdEZvdW5kRXhjZXB0aW9uKCdBc3NldCBub3QgZm91bmQnKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gQ291bnQgdnVsbmVyYWJpbGl0aWVzIGJ5IHNldmVyaXR5XHJcbiAgICAgIGNvbnN0IHZ1bG5lcmFiaWxpdHlDb3VudHMgPSBhd2FpdCB0aGlzLnZ1bG5lcmFiaWxpdHlSZXBvc2l0b3J5XHJcbiAgICAgICAgLmNyZWF0ZVF1ZXJ5QnVpbGRlcigndnVsbicpXHJcbiAgICAgICAgLnNlbGVjdCgndnVsbi5zZXZlcml0eScsICdzZXZlcml0eScpXHJcbiAgICAgICAgLmFkZFNlbGVjdCgnQ09VTlQoKiknLCAnY291bnQnKVxyXG4gICAgICAgIC53aGVyZSgndnVsbi5hc3NldElkID0gOmFzc2V0SWQnLCB7IGFzc2V0SWQgfSlcclxuICAgICAgICAuYW5kV2hlcmUoJ3Z1bG4uc3RhdHVzIElOICg6Li4uc3RhdHVzZXMpJywgeyBzdGF0dXNlczogWydvcGVuJywgJ2NvbmZpcm1lZCddIH0pXHJcbiAgICAgICAgLmdyb3VwQnkoJ3Z1bG4uc2V2ZXJpdHknKVxyXG4gICAgICAgIC5nZXRSYXdNYW55KCk7XHJcblxyXG4gICAgICBjb25zdCBjb3VudHMgPSB7XHJcbiAgICAgICAgY3JpdGljYWw6IDAsXHJcbiAgICAgICAgaGlnaDogMCxcclxuICAgICAgICBtZWRpdW06IDAsXHJcbiAgICAgICAgbG93OiAwLFxyXG4gICAgICAgIGluZm86IDAsXHJcbiAgICAgICAgdG90YWw6IDAsXHJcbiAgICAgIH07XHJcblxyXG4gICAgICB2dWxuZXJhYmlsaXR5Q291bnRzLmZvckVhY2goKHsgc2V2ZXJpdHksIGNvdW50IH0pID0+IHtcclxuICAgICAgICBjb3VudHNbc2V2ZXJpdHldID0gcGFyc2VJbnQoY291bnQpO1xyXG4gICAgICAgIGNvdW50cy50b3RhbCArPSBwYXJzZUludChjb3VudCk7XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgYXNzZXQudXBkYXRlVnVsbmVyYWJpbGl0eUNvdW50cyhjb3VudHMpO1xyXG4gICAgICByZXR1cm4gYXdhaXQgdGhpcy5hc3NldFJlcG9zaXRvcnkuc2F2ZShhc3NldCk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICB0aGlzLmxvZ2dlci5lcnJvcignRmFpbGVkIHRvIHVwZGF0ZSB2dWxuZXJhYmlsaXR5IGNvdW50cycsIHtcclxuICAgICAgICBhc3NldElkLFxyXG4gICAgICAgIGVycm9yOiBlcnJvci5tZXNzYWdlLFxyXG4gICAgICB9KTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgYXNzZXRzIGR1ZSBmb3Igc2Nhbm5pbmdcclxuICAgKiBAcmV0dXJucyBBc3NldHMgZHVlIGZvciBzY2FubmluZ1xyXG4gICAqL1xyXG4gIGFzeW5jIGdldEFzc2V0c0R1ZUZvclNjYW5uaW5nKCk6IFByb21pc2U8QXNzZXRbXT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgcmV0dXJuIGF3YWl0IHRoaXMuYXNzZXRSZXBvc2l0b3J5XHJcbiAgICAgICAgLmNyZWF0ZVF1ZXJ5QnVpbGRlcignYXNzZXQnKVxyXG4gICAgICAgIC53aGVyZSgnYXNzZXQuaXNTY2FubmFibGUgPSA6c2Nhbm5hYmxlJywgeyBzY2FubmFibGU6IHRydWUgfSlcclxuICAgICAgICAuYW5kV2hlcmUoJ2Fzc2V0LnN0YXR1cyA9IDpzdGF0dXMnLCB7IHN0YXR1czogJ2FjdGl2ZScgfSlcclxuICAgICAgICAuYW5kV2hlcmUoXHJcbiAgICAgICAgICAnKGFzc2V0Lmxhc3RTY2FuRGF0ZSBJUyBOVUxMIE9SIGFzc2V0Lmxhc3RTY2FuRGF0ZSArIElOVEVSVkFMIFxcJzEgaG91clxcJyAqIGFzc2V0LnNjYW5GcmVxdWVuY3kgPD0gTk9XKCkpJyxcclxuICAgICAgICApXHJcbiAgICAgICAgLm9yZGVyQnkoJ2Fzc2V0LmNyaXRpY2FsaXR5JywgJ0RFU0MnKVxyXG4gICAgICAgIC5hZGRPcmRlckJ5KCdhc3NldC5sYXN0U2NhbkRhdGUnLCAnQVNDJylcclxuICAgICAgICAuZ2V0TWFueSgpO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgdGhpcy5sb2dnZXIuZXJyb3IoJ0ZhaWxlZCB0byBnZXQgYXNzZXRzIGR1ZSBmb3Igc2Nhbm5pbmcnLCB7XHJcbiAgICAgICAgZXJyb3I6IGVycm9yLm1lc3NhZ2UsXHJcbiAgICAgIH0pO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCBhc3NldCBzdGF0aXN0aWNzXHJcbiAgICogQHJldHVybnMgQXNzZXQgc3RhdGlzdGljc1xyXG4gICAqL1xyXG4gIGFzeW5jIGdldFN0YXRpc3RpY3MoKTogUHJvbWlzZTxhbnk+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IFtcclxuICAgICAgICB0b3RhbEFzc2V0cyxcclxuICAgICAgICBhY3RpdmVBc3NldHMsXHJcbiAgICAgICAgY3JpdGljYWxBc3NldHMsXHJcbiAgICAgICAgYXNzZXRzQnlUeXBlLFxyXG4gICAgICAgIGFzc2V0c0J5RW52aXJvbm1lbnQsXHJcbiAgICAgICAgYXNzZXRzV2l0aFZ1bG5lcmFiaWxpdGllcyxcclxuICAgICAgXSA9IGF3YWl0IFByb21pc2UuYWxsKFtcclxuICAgICAgICB0aGlzLmFzc2V0UmVwb3NpdG9yeS5jb3VudCgpLFxyXG4gICAgICAgIHRoaXMuYXNzZXRSZXBvc2l0b3J5LmNvdW50KHsgd2hlcmU6IHsgc3RhdHVzOiAnYWN0aXZlJyB9IH0pLFxyXG4gICAgICAgIHRoaXMuYXNzZXRSZXBvc2l0b3J5LmNvdW50KHsgd2hlcmU6IHsgY3JpdGljYWxpdHk6ICdjcml0aWNhbCcgfSB9KSxcclxuICAgICAgICB0aGlzLmdldEFzc2V0Q291bnRzQnlUeXBlKCksXHJcbiAgICAgICAgdGhpcy5nZXRBc3NldENvdW50c0J5RW52aXJvbm1lbnQoKSxcclxuICAgICAgICB0aGlzLmdldEFzc2V0c1dpdGhWdWxuZXJhYmlsaXRpZXMoKSxcclxuICAgICAgXSk7XHJcblxyXG4gICAgICByZXR1cm4ge1xyXG4gICAgICAgIHRvdGFsOiB0b3RhbEFzc2V0cyxcclxuICAgICAgICBhY3RpdmU6IGFjdGl2ZUFzc2V0cyxcclxuICAgICAgICBjcml0aWNhbDogY3JpdGljYWxBc3NldHMsXHJcbiAgICAgICAgYnlUeXBlOiBhc3NldHNCeVR5cGUsXHJcbiAgICAgICAgYnlFbnZpcm9ubWVudDogYXNzZXRzQnlFbnZpcm9ubWVudCxcclxuICAgICAgICB3aXRoVnVsbmVyYWJpbGl0aWVzOiBhc3NldHNXaXRoVnVsbmVyYWJpbGl0aWVzLFxyXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxyXG4gICAgICB9O1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgdGhpcy5sb2dnZXIuZXJyb3IoJ0ZhaWxlZCB0byBnZXQgYXNzZXQgc3RhdGlzdGljcycsIHtcclxuICAgICAgICBlcnJvcjogZXJyb3IubWVzc2FnZSxcclxuICAgICAgfSk7XHJcbiAgICAgIHRocm93IGVycm9yO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogRGV0ZXJtaW5lIGFzc2V0IHR5cGUgYmFzZWQgb24gZGlzY292ZXJ5IGRhdGFcclxuICAgKiBAcGFyYW0gZGF0YSBEaXNjb3ZlcnkgZGF0YVxyXG4gICAqIEByZXR1cm5zIEFzc2V0IHR5cGVcclxuICAgKi9cclxuICBwcml2YXRlIGRldGVybWluZUFzc2V0VHlwZShkYXRhOiBhbnkpOiBzdHJpbmcge1xyXG4gICAgaWYgKGRhdGEuc2VydmljZXMpIHtcclxuICAgICAgY29uc3Qgc2VydmljZU5hbWVzID0gZGF0YS5zZXJ2aWNlcy5tYXAocyA9PiBzLm5hbWUudG9Mb3dlckNhc2UoKSk7XHJcbiAgICAgIFxyXG4gICAgICBpZiAoc2VydmljZU5hbWVzLmluY2x1ZGVzKCdodHRwJykgfHwgc2VydmljZU5hbWVzLmluY2x1ZGVzKCdodHRwcycpKSB7XHJcbiAgICAgICAgcmV0dXJuICd3ZWJfYXBwbGljYXRpb24nO1xyXG4gICAgICB9XHJcbiAgICAgIGlmIChzZXJ2aWNlTmFtZXMuaW5jbHVkZXMoJ3NzaCcpIHx8IHNlcnZpY2VOYW1lcy5pbmNsdWRlcygncmRwJykpIHtcclxuICAgICAgICByZXR1cm4gJ3NlcnZlcic7XHJcbiAgICAgIH1cclxuICAgICAgaWYgKHNlcnZpY2VOYW1lcy5pbmNsdWRlcygnc25tcCcpKSB7XHJcbiAgICAgICAgcmV0dXJuICduZXR3b3JrX2RldmljZSc7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBpZiAoZGF0YS5vcGVyYXRpbmdTeXN0ZW0pIHtcclxuICAgICAgY29uc3Qgb3MgPSBkYXRhLm9wZXJhdGluZ1N5c3RlbS5uYW1lPy50b0xvd2VyQ2FzZSgpIHx8ICcnO1xyXG4gICAgICBpZiAob3MuaW5jbHVkZXMoJ3dpbmRvd3MnKSB8fCBvcy5pbmNsdWRlcygnbGludXgnKSB8fCBvcy5pbmNsdWRlcygndW5peCcpKSB7XHJcbiAgICAgICAgcmV0dXJuICdzZXJ2ZXInO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuICdzZXJ2ZXInOyAvLyBEZWZhdWx0IHR5cGVcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCBhc3NldCBjb3VudHMgYnkgdHlwZVxyXG4gICAqIEByZXR1cm5zIEFzc2V0IGNvdW50cyBieSB0eXBlXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBhc3luYyBnZXRBc3NldENvdW50c0J5VHlwZSgpOiBQcm9taXNlPFJlY29yZDxzdHJpbmcsIG51bWJlcj4+IHtcclxuICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHRoaXMuYXNzZXRSZXBvc2l0b3J5XHJcbiAgICAgIC5jcmVhdGVRdWVyeUJ1aWxkZXIoJ2Fzc2V0JylcclxuICAgICAgLnNlbGVjdCgnYXNzZXQudHlwZScsICd0eXBlJylcclxuICAgICAgLmFkZFNlbGVjdCgnQ09VTlQoKiknLCAnY291bnQnKVxyXG4gICAgICAuZ3JvdXBCeSgnYXNzZXQudHlwZScpXHJcbiAgICAgIC5nZXRSYXdNYW55KCk7XHJcblxyXG4gICAgcmV0dXJuIHJlc3VsdC5yZWR1Y2UoKGFjYywgaXRlbSkgPT4ge1xyXG4gICAgICBhY2NbaXRlbS50eXBlXSA9IHBhcnNlSW50KGl0ZW0uY291bnQpO1xyXG4gICAgICByZXR1cm4gYWNjO1xyXG4gICAgfSwge30pO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IGFzc2V0IGNvdW50cyBieSBlbnZpcm9ubWVudFxyXG4gICAqIEByZXR1cm5zIEFzc2V0IGNvdW50cyBieSBlbnZpcm9ubWVudFxyXG4gICAqL1xyXG4gIHByaXZhdGUgYXN5bmMgZ2V0QXNzZXRDb3VudHNCeUVudmlyb25tZW50KCk6IFByb21pc2U8UmVjb3JkPHN0cmluZywgbnVtYmVyPj4ge1xyXG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgdGhpcy5hc3NldFJlcG9zaXRvcnlcclxuICAgICAgLmNyZWF0ZVF1ZXJ5QnVpbGRlcignYXNzZXQnKVxyXG4gICAgICAuc2VsZWN0KCdhc3NldC5lbnZpcm9ubWVudCcsICdlbnZpcm9ubWVudCcpXHJcbiAgICAgIC5hZGRTZWxlY3QoJ0NPVU5UKCopJywgJ2NvdW50JylcclxuICAgICAgLmdyb3VwQnkoJ2Fzc2V0LmVudmlyb25tZW50JylcclxuICAgICAgLmdldFJhd01hbnkoKTtcclxuXHJcbiAgICByZXR1cm4gcmVzdWx0LnJlZHVjZSgoYWNjLCBpdGVtKSA9PiB7XHJcbiAgICAgIGFjY1tpdGVtLmVudmlyb25tZW50XSA9IHBhcnNlSW50KGl0ZW0uY291bnQpO1xyXG4gICAgICByZXR1cm4gYWNjO1xyXG4gICAgfSwge30pO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IGNvdW50IG9mIGFzc2V0cyB3aXRoIHZ1bG5lcmFiaWxpdGllc1xyXG4gICAqIEByZXR1cm5zIENvdW50IG9mIGFzc2V0cyB3aXRoIHZ1bG5lcmFiaWxpdGllc1xyXG4gICAqL1xyXG4gIHByaXZhdGUgYXN5bmMgZ2V0QXNzZXRzV2l0aFZ1bG5lcmFiaWxpdGllcygpOiBQcm9taXNlPG51bWJlcj4ge1xyXG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgdGhpcy5hc3NldFJlcG9zaXRvcnlcclxuICAgICAgLmNyZWF0ZVF1ZXJ5QnVpbGRlcignYXNzZXQnKVxyXG4gICAgICAuaW5uZXJKb2luKCdhc3NldC52dWxuZXJhYmlsaXRpZXMnLCAndnVsbicpXHJcbiAgICAgIC53aGVyZSgndnVsbi5zdGF0dXMgSU4gKDouLi5zdGF0dXNlcyknLCB7IHN0YXR1c2VzOiBbJ29wZW4nLCAnY29uZmlybWVkJ10gfSlcclxuICAgICAgLmdldENvdW50KCk7XHJcblxyXG4gICAgcmV0dXJuIHJlc3VsdDtcclxuICB9XHJcbn1cclxuIl0sInZlcnNpb24iOjN9