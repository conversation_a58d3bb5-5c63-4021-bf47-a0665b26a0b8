fb6c293e80fa8de72f55b09dcfbcc73f
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const unique_entity_id_value_object_1 = require("../../../../../shared-kernel/value-objects/unique-entity-id.value-object");
const ai_model_entity_1 = require("../../entities/ai-model.entity");
const ai_model_factory_1 = require("../../factories/ai-model.factory");
const ai_model_aggregate_1 = require("../ai-model.aggregate");
describe('AIModelAggregate', () => {
    let aggregate;
    beforeEach(() => {
        aggregate = new ai_model_aggregate_1.AIModelAggregate();
    });
    describe('Model Management', () => {
        it('should create and add a new model', () => {
            const request = {
                name: 'Test Model',
                version: '1.0.0',
                provider: ai_model_entity_1.AIProvider.OPENAI,
                modelType: ai_model_entity_1.ModelType.LANGUAGE_MODEL,
                supportedTaskTypes: ['text-generation'],
            };
            const model = aggregate.createModel(request);
            expect(model.name).toBe('Test Model');
            expect(aggregate.getAllModels()).toHaveLength(1);
            expect(aggregate.getModel(model.id)).toBe(model);
        });
        it('should throw error when creating duplicate model', () => {
            const request = {
                name: 'Test Model',
                version: '1.0.0',
                provider: ai_model_entity_1.AIProvider.OPENAI,
                modelType: ai_model_entity_1.ModelType.LANGUAGE_MODEL,
                supportedTaskTypes: ['text-generation'],
            };
            aggregate.createModel(request);
            expect(() => aggregate.createModel(request)).toThrow("Model with name 'Test Model' already exists for provider 'openai'");
        });
        it('should add existing model', () => {
            const model = ai_model_factory_1.AIModelFactory.createForTesting();
            aggregate.addModel(model);
            expect(aggregate.getAllModels()).toHaveLength(1);
            expect(aggregate.getModel(model.id)).toBe(model);
        });
        it('should throw error when adding duplicate model', () => {
            const model = ai_model_factory_1.AIModelFactory.createForTesting();
            aggregate.addModel(model);
            expect(() => aggregate.addModel(model)).toThrow(`Model with ID '${model.id.toString()}' already exists in aggregate`);
        });
        it('should remove model', () => {
            const model = ai_model_factory_1.AIModelFactory.createForTesting();
            aggregate.addModel(model);
            aggregate.removeModel(model.id);
            expect(aggregate.getAllModels()).toHaveLength(0);
            expect(model.status).toBe(ai_model_entity_1.ModelStatus.ARCHIVED);
        });
        it('should throw error when removing non-existent model', () => {
            const nonExistentId = unique_entity_id_value_object_1.UniqueEntityId.generate();
            expect(() => aggregate.removeModel(nonExistentId)).toThrow(`Model with ID '${nonExistentId.toString()}' not found`);
        });
    });
    describe('Model Querying', () => {
        beforeEach(() => {
            // Create test models
            const models = [
                ai_model_factory_1.AIModelFactory.createForTesting({
                    name: 'OpenAI GPT',
                    provider: ai_model_entity_1.AIProvider.OPENAI,
                    modelType: ai_model_entity_1.ModelType.LANGUAGE_MODEL,
                    supportedTaskTypes: ['text-generation', 'classification'],
                }),
                ai_model_factory_1.AIModelFactory.createForTesting({
                    name: 'Bedrock Claude',
                    provider: ai_model_entity_1.AIProvider.BEDROCK,
                    modelType: ai_model_entity_1.ModelType.LANGUAGE_MODEL,
                    supportedTaskTypes: ['text-generation'],
                }),
                ai_model_factory_1.AIModelFactory.createForTesting({
                    name: 'TensorFlow Classifier',
                    provider: ai_model_entity_1.AIProvider.TENSORFLOW,
                    modelType: ai_model_entity_1.ModelType.CLASSIFICATION,
                    supportedTaskTypes: ['classification', 'anomaly-detection'],
                }),
            ];
            models.forEach(model => {
                aggregate.addModel(model);
                model.activate(); // Activate for testing
            });
        });
        it('should get all models', () => {
            const models = aggregate.getAllModels();
            expect(models).toHaveLength(3);
        });
        it('should get active models only', () => {
            const models = aggregate.getActiveModels();
            expect(models).toHaveLength(3);
            expect(models.every(model => model.status === ai_model_entity_1.ModelStatus.ACTIVE)).toBe(true);
        });
        it('should find models by task type', () => {
            const criteria = {
                taskType: 'text-generation',
            };
            const models = aggregate.findModels(criteria);
            expect(models).toHaveLength(2);
            expect(models.every(model => model.supportsTaskType('text-generation'))).toBe(true);
        });
        it('should find models by provider', () => {
            const criteria = {
                provider: ai_model_entity_1.AIProvider.OPENAI,
            };
            const models = aggregate.findModels(criteria);
            expect(models).toHaveLength(1);
            expect(models[0].provider).toBe(ai_model_entity_1.AIProvider.OPENAI);
        });
        it('should find models by model type', () => {
            const criteria = {
                modelType: ai_model_entity_1.ModelType.LANGUAGE_MODEL,
            };
            const models = aggregate.findModels(criteria);
            expect(models).toHaveLength(2);
            expect(models.every(model => model.modelType === ai_model_entity_1.ModelType.LANGUAGE_MODEL)).toBe(true);
        });
        it('should find models by multiple criteria', () => {
            const criteria = {
                taskType: 'classification',
                modelType: ai_model_entity_1.ModelType.CLASSIFICATION,
            };
            const models = aggregate.findModels(criteria);
            expect(models).toHaveLength(1);
            expect(models[0].modelType).toBe(ai_model_entity_1.ModelType.CLASSIFICATION);
            expect(models[0].supportsTaskType('classification')).toBe(true);
        });
        it('should find models by performance criteria', () => {
            // Update performance for one model
            const models = aggregate.getAllModels();
            models[0].updatePerformanceMetrics({ accuracy: 0.95, averageLatency: 100 });
            const criteria = {
                minAccuracy: 0.9,
                maxLatency: 200,
            };
            const foundModels = aggregate.findModels(criteria);
            expect(foundModels).toHaveLength(1);
            expect(foundModels[0].performance.accuracy).toBeGreaterThanOrEqual(0.9);
            expect(foundModels[0].performance.averageLatency).toBeLessThanOrEqual(200);
        });
        it('should find models requiring availability', () => {
            // Make one model unavailable
            const models = aggregate.getAllModels();
            models[0].updateLoad(models[0].maxConcurrentRequests);
            const criteria = {
                requiresAvailability: true,
            };
            const availableModels = aggregate.findModels(criteria);
            expect(availableModels).toHaveLength(2);
            expect(availableModels.every(model => model.canHandleRequest())).toBe(true);
        });
    });
    describe('Model Selection', () => {
        beforeEach(() => {
            // Create test models with different performance characteristics
            const models = [
                ai_model_factory_1.AIModelFactory.createForTesting({
                    name: 'High Performance',
                    supportedTaskTypes: ['classification'],
                    priority: 8,
                    weight: 3,
                }),
                ai_model_factory_1.AIModelFactory.createForTesting({
                    name: 'Medium Performance',
                    supportedTaskTypes: ['classification'],
                    priority: 5,
                    weight: 2,
                }),
                ai_model_factory_1.AIModelFactory.createForTesting({
                    name: 'Low Performance',
                    supportedTaskTypes: ['classification'],
                    priority: 2,
                    weight: 1,
                }),
            ];
            models.forEach(model => {
                aggregate.addModel(model);
                model.activate();
            });
            // Set different performance metrics
            models[0].updatePerformanceMetrics({ accuracy: 0.95, averageLatency: 100 });
            models[1].updatePerformanceMetrics({ accuracy: 0.85, averageLatency: 200 });
            models[2].updatePerformanceMetrics({ accuracy: 0.75, averageLatency: 300 });
        });
        it('should select best model based on criteria', () => {
            const criteria = {
                taskType: 'classification',
            };
            const bestModel = aggregate.selectBestModel(criteria);
            expect(bestModel).toBeDefined();
            expect(bestModel.name).toBe('High Performance');
        });
        it('should return undefined when no models match criteria', () => {
            const criteria = {
                taskType: 'non-existent-task',
            };
            const bestModel = aggregate.selectBestModel(criteria);
            expect(bestModel).toBeUndefined();
        });
        it('should return single model when only one matches', () => {
            // Deactivate all but one model
            const models = aggregate.getAllModels();
            models[1].deactivate();
            models[2].deactivate();
            const criteria = {
                taskType: 'classification',
                requiresAvailability: true,
            };
            const bestModel = aggregate.selectBestModel(criteria);
            expect(bestModel).toBeDefined();
            expect(bestModel.name).toBe('High Performance');
        });
    });
    describe('Load Balancing', () => {
        beforeEach(() => {
            const models = [
                ai_model_factory_1.AIModelFactory.createForTesting({ name: 'Model 1', supportedTaskTypes: ['classification'] }),
                ai_model_factory_1.AIModelFactory.createForTesting({ name: 'Model 2', supportedTaskTypes: ['classification'] }),
                ai_model_factory_1.AIModelFactory.createForTesting({ name: 'Model 3', supportedTaskTypes: ['classification'] }),
            ];
            models.forEach(model => {
                aggregate.addModel(model);
                model.activate();
            });
            // Set different loads
            models[0].updateLoad(5);
            models[1].updateLoad(2);
            models[2].updateLoad(8);
        });
        it('should select least loaded model', () => {
            const criteria = {
                taskType: 'classification',
            };
            const strategy = {
                type: 'least_loaded',
            };
            const selectedModel = aggregate.selectModelForLoadBalancing(criteria, strategy);
            expect(selectedModel).toBeDefined();
            expect(selectedModel.name).toBe('Model 2'); // Has load of 2
        });
        it('should select model using round robin', () => {
            const criteria = {
                taskType: 'classification',
            };
            const strategy = {
                type: 'round_robin',
            };
            const selectedModel = aggregate.selectModelForLoadBalancing(criteria, strategy);
            expect(selectedModel).toBeDefined();
        });
        it('should select model using performance-based strategy', () => {
            // Update performance metrics
            const models = aggregate.getAllModels();
            models[0].updatePerformanceMetrics({ accuracy: 0.95 });
            models[1].updatePerformanceMetrics({ accuracy: 0.85 });
            models[2].updatePerformanceMetrics({ accuracy: 0.75 });
            const criteria = {
                taskType: 'classification',
            };
            const strategy = {
                type: 'performance_based',
            };
            const selectedModel = aggregate.selectModelForLoadBalancing(criteria, strategy);
            expect(selectedModel).toBeDefined();
            expect(selectedModel.name).toBe('Model 1'); // Highest accuracy
        });
        it('should return undefined when no models match criteria', () => {
            const criteria = {
                taskType: 'non-existent-task',
            };
            const strategy = {
                type: 'least_loaded',
            };
            const selectedModel = aggregate.selectModelForLoadBalancing(criteria, strategy);
            expect(selectedModel).toBeUndefined();
        });
    });
    describe('Statistics', () => {
        beforeEach(() => {
            const models = [
                ai_model_factory_1.AIModelFactory.createForTesting({
                    name: 'OpenAI Model',
                    provider: ai_model_entity_1.AIProvider.OPENAI,
                    modelType: ai_model_entity_1.ModelType.LANGUAGE_MODEL,
                }),
                ai_model_factory_1.AIModelFactory.createForTesting({
                    name: 'Bedrock Model',
                    provider: ai_model_entity_1.AIProvider.BEDROCK,
                    modelType: ai_model_entity_1.ModelType.LANGUAGE_MODEL,
                }),
                ai_model_factory_1.AIModelFactory.createForTesting({
                    name: 'TensorFlow Model',
                    provider: ai_model_entity_1.AIProvider.TENSORFLOW,
                    modelType: ai_model_entity_1.ModelType.CLASSIFICATION,
                }),
            ];
            models.forEach(model => aggregate.addModel(model));
            models[0].activate();
            models[1].activate();
            // models[2] remains inactive
            models[0].updateLoad(5);
            models[1].updateLoad(3);
        });
        it('should calculate aggregate statistics', () => {
            const stats = aggregate.getStatistics();
            expect(stats.totalModels).toBe(3);
            expect(stats.activeModels).toBe(2);
            expect(stats.inactiveModels).toBe(1);
            expect(stats.archivedModels).toBe(0);
            expect(stats.totalLoad).toBe(8);
            expect(stats.averageUtilization).toBeGreaterThan(0);
            expect(stats.providerDistribution[ai_model_entity_1.AIProvider.OPENAI]).toBe(1);
            expect(stats.providerDistribution[ai_model_entity_1.AIProvider.BEDROCK]).toBe(1);
            expect(stats.providerDistribution[ai_model_entity_1.AIProvider.TENSORFLOW]).toBe(1);
            expect(stats.typeDistribution[ai_model_entity_1.ModelType.LANGUAGE_MODEL]).toBe(2);
            expect(stats.typeDistribution[ai_model_entity_1.ModelType.CLASSIFICATION]).toBe(1);
        });
    });
    describe('Bulk Operations', () => {
        beforeEach(() => {
            const models = [
                ai_model_factory_1.AIModelFactory.createForTesting({
                    name: 'OpenAI Model',
                    provider: ai_model_entity_1.AIProvider.OPENAI,
                    supportedTaskTypes: ['text-generation'],
                }),
                ai_model_factory_1.AIModelFactory.createForTesting({
                    name: 'Bedrock Model',
                    provider: ai_model_entity_1.AIProvider.BEDROCK,
                    supportedTaskTypes: ['text-generation'],
                }),
                ai_model_factory_1.AIModelFactory.createForTesting({
                    name: 'TensorFlow Model',
                    provider: ai_model_entity_1.AIProvider.TENSORFLOW,
                    supportedTaskTypes: ['classification'],
                }),
            ];
            models.forEach(model => aggregate.addModel(model));
        });
        it('should activate models matching criteria', () => {
            const criteria = {
                taskType: 'text-generation',
            };
            const activatedModels = aggregate.activateModels(criteria);
            expect(activatedModels).toHaveLength(2);
            expect(activatedModels.every(model => model.status === ai_model_entity_1.ModelStatus.ACTIVE)).toBe(true);
        });
        it('should deactivate models matching criteria', () => {
            // First activate all models
            aggregate.getAllModels().forEach(model => model.activate());
            const criteria = {
                provider: ai_model_entity_1.AIProvider.OPENAI,
            };
            const deactivatedModels = aggregate.deactivateModels(criteria);
            expect(deactivatedModels).toHaveLength(1);
            expect(deactivatedModels[0].status).toBe(ai_model_entity_1.ModelStatus.INACTIVE);
        });
        it('should archive models matching criteria', () => {
            const criteria = {
                provider: ai_model_entity_1.AIProvider.TENSORFLOW,
            };
            const archivedModels = aggregate.archiveModels(criteria);
            expect(archivedModels).toHaveLength(1);
            expect(archivedModels[0].status).toBe(ai_model_entity_1.ModelStatus.ARCHIVED);
        });
    });
    describe('Performance Monitoring', () => {
        beforeEach(() => {
            const models = [
                ai_model_factory_1.AIModelFactory.createForTesting({ name: 'Normal Model' }),
                ai_model_factory_1.AIModelFactory.createForTesting({ name: 'Overloaded Model' }),
                ai_model_factory_1.AIModelFactory.createForTesting({ name: 'Low Performance Model' }),
            ];
            models.forEach(model => aggregate.addModel(model));
            // Make one model overloaded
            models[1].updateLoad(9); // 90% utilization (overloaded threshold is 80%)
            // Make one model low performance
            models[2].updatePerformanceMetrics({ accuracy: 0.3 });
        });
        it('should identify overloaded models', () => {
            const overloadedModels = aggregate.getOverloadedModels();
            expect(overloadedModels).toHaveLength(1);
            expect(overloadedModels[0].name).toBe('Overloaded Model');
        });
        it('should identify low performance models', () => {
            const lowPerformanceModels = aggregate.getLowPerformanceModels(0.5);
            expect(lowPerformanceModels).toHaveLength(1);
            expect(lowPerformanceModels[0].name).toBe('Low Performance Model');
        });
        it('should rebalance load', () => {
            // Activate models first
            aggregate.getAllModels().forEach(model => model.activate());
            const overloadedBefore = aggregate.getOverloadedModels();
            expect(overloadedBefore).toHaveLength(1);
            aggregate.rebalanceLoad();
            const overloadedAfter = aggregate.getOverloadedModels();
            expect(overloadedAfter).toHaveLength(0);
        });
        it('should not deactivate last active model during rebalancing', () => {
            // Only activate the overloaded model
            const models = aggregate.getAllModels();
            models[1].activate();
            aggregate.rebalanceLoad();
            expect(models[1].status).toBe(ai_model_entity_1.ModelStatus.ACTIVE); // Should remain active
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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