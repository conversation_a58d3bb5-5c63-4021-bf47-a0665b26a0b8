441a49abb73632d26f2714f3b03e2fa1
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const throttler_1 = require("@nestjs/throttler");
const config_1 = require("@nestjs/config");
// Infrastructure modules
const config_module_1 = require("./infrastructure/config/config.module");
const database_module_1 = require("./infrastructure/database/database.module");
const auth_module_1 = require("./infrastructure/auth/auth.module");
const logging_module_1 = require("./infrastructure/logging/logging.module");
// Common filters, interceptors, and middleware
const global_exception_filter_1 = require("./common/filters/global-exception.filter");
const logging_interceptor_1 = require("./common/interceptors/logging.interceptor");
const response_transform_interceptor_1 = require("./common/interceptors/response-transform.interceptor");
const correlation_id_middleware_1 = require("./common/middleware/correlation-id.middleware");
// API modules
const api_module_1 = require("./api/api.module");
// Feature modules
const vulnerability_management_module_1 = require("./modules/vulnerability-management/vulnerability-management.module");
const ai_module_1 = require("./modules/ai/ai.module");
// Application components
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const health_controller_1 = require("./health.controller");
const metrics_controller_1 = require("./metrics.controller");
/**
 * Main application module for the Sentinel vulnerability assessment platform
 * Configures all infrastructure modules and global providers
 */
let AppModule = class AppModule {
    /**
     * Configure middleware for the application
     */
    configure(consumer) {
        consumer
            .apply(correlation_id_middleware_1.CorrelationIdMiddleware)
            .forRoutes('*');
    }
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            // Configuration module (must be first)
            config_module_1.ConfigModule,
            // Rate limiting
            throttler_1.ThrottlerModule.forRootAsync({
                imports: [config_1.ConfigModule],
                inject: [config_1.ConfigService],
                useFactory: (configService) => {
                    const securityConfig = configService.get('security');
                    return [
                        {
                            ttl: securityConfig?.rateLimit?.windowMs || 60000,
                            limit: securityConfig?.rateLimit?.max || 100,
                        },
                    ];
                },
            }),
            // Infrastructure modules
            database_module_1.DatabaseModule,
            auth_module_1.AuthModule,
            logging_module_1.LoggingModule,
            // API modules
            api_module_1.ApiModule,
            // Feature modules
            vulnerability_management_module_1.VulnerabilityManagementModule,
            ai_module_1.AiModule,
        ],
        controllers: [app_controller_1.AppController, health_controller_1.HealthController, metrics_controller_1.MetricsController],
        providers: [
            app_service_1.AppService,
            health_controller_1.HealthService,
            metrics_controller_1.MetricsService,
            // Global exception filter
            {
                provide: core_1.APP_FILTER,
                useClass: global_exception_filter_1.GlobalExceptionFilter,
            },
            // Global interceptors
            {
                provide: core_1.APP_INTERCEPTOR,
                useClass: logging_interceptor_1.LoggingInterceptor,
            },
            {
                provide: core_1.APP_INTERCEPTOR,
                useClass: response_transform_interceptor_1.ResponseTransformInterceptor,
            },
            // Global rate limiting guard
            {
                provide: core_1.APP_GUARD,
                useClass: throttler_1.ThrottlerGuard,
            },
        ],
    })
], AppModule);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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