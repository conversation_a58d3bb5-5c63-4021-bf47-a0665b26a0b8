921c7d27703b65074fa989a7dd1de426
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompliancePolicy = exports.ComplianceFindingStatus = exports.ComplianceFindingSeverity = exports.ComplianceCheckFrequency = exports.CompliancePriority = exports.ComplianceEvidenceType = exports.ComplianceControlType = exports.ComplianceStatus = exports.ComplianceFramework = void 0;
const base_entity_1 = require("../../../../shared-kernel/domain/base-entity");
const unique_entity_id_value_object_1 = require("../../../../shared-kernel/value-objects/unique-entity-id.value-object");
const timestamp_value_object_1 = require("../../../../shared-kernel/value-objects/timestamp.value-object");
const validation_exception_1 = require("../../../../shared-kernel/exceptions/validation.exception");
var ComplianceFramework;
(function (ComplianceFramework) {
    ComplianceFramework["SOC2"] = "SOC2";
    ComplianceFramework["GDPR"] = "GDPR";
    ComplianceFramework["HIPAA"] = "HIPAA";
    ComplianceFramework["PCI_DSS"] = "PCI_DSS";
    ComplianceFramework["ISO_27001"] = "ISO_27001";
    ComplianceFramework["NIST"] = "NIST";
    ComplianceFramework["CIS"] = "CIS";
    ComplianceFramework["COBIT"] = "COBIT";
})(ComplianceFramework || (exports.ComplianceFramework = ComplianceFramework = {}));
var ComplianceStatus;
(function (ComplianceStatus) {
    ComplianceStatus["COMPLIANT"] = "COMPLIANT";
    ComplianceStatus["NON_COMPLIANT"] = "NON_COMPLIANT";
    ComplianceStatus["PARTIALLY_COMPLIANT"] = "PARTIALLY_COMPLIANT";
    ComplianceStatus["NOT_ASSESSED"] = "NOT_ASSESSED";
    ComplianceStatus["REMEDIATION_REQUIRED"] = "REMEDIATION_REQUIRED";
})(ComplianceStatus || (exports.ComplianceStatus = ComplianceStatus = {}));
var ComplianceControlType;
(function (ComplianceControlType) {
    ComplianceControlType["PREVENTIVE"] = "PREVENTIVE";
    ComplianceControlType["DETECTIVE"] = "DETECTIVE";
    ComplianceControlType["CORRECTIVE"] = "CORRECTIVE";
    ComplianceControlType["COMPENSATING"] = "COMPENSATING";
})(ComplianceControlType || (exports.ComplianceControlType = ComplianceControlType = {}));
var ComplianceEvidenceType;
(function (ComplianceEvidenceType) {
    ComplianceEvidenceType["DOCUMENT"] = "DOCUMENT";
    ComplianceEvidenceType["SCREENSHOT"] = "SCREENSHOT";
    ComplianceEvidenceType["LOG_ENTRY"] = "LOG_ENTRY";
    ComplianceEvidenceType["CONFIGURATION"] = "CONFIGURATION";
    ComplianceEvidenceType["AUDIT_REPORT"] = "AUDIT_REPORT";
    ComplianceEvidenceType["CERTIFICATE"] = "CERTIFICATE";
    ComplianceEvidenceType["POLICY"] = "POLICY";
    ComplianceEvidenceType["PROCEDURE"] = "PROCEDURE";
})(ComplianceEvidenceType || (exports.ComplianceEvidenceType = ComplianceEvidenceType = {}));
var CompliancePriority;
(function (CompliancePriority) {
    CompliancePriority["CRITICAL"] = "CRITICAL";
    CompliancePriority["HIGH"] = "HIGH";
    CompliancePriority["MEDIUM"] = "MEDIUM";
    CompliancePriority["LOW"] = "LOW";
})(CompliancePriority || (exports.CompliancePriority = CompliancePriority = {}));
var ComplianceCheckFrequency;
(function (ComplianceCheckFrequency) {
    ComplianceCheckFrequency["CONTINUOUS"] = "CONTINUOUS";
    ComplianceCheckFrequency["DAILY"] = "DAILY";
    ComplianceCheckFrequency["WEEKLY"] = "WEEKLY";
    ComplianceCheckFrequency["MONTHLY"] = "MONTHLY";
    ComplianceCheckFrequency["QUARTERLY"] = "QUARTERLY";
    ComplianceCheckFrequency["ANNUALLY"] = "ANNUALLY";
})(ComplianceCheckFrequency || (exports.ComplianceCheckFrequency = ComplianceCheckFrequency = {}));
var ComplianceFindingSeverity;
(function (ComplianceFindingSeverity) {
    ComplianceFindingSeverity["CRITICAL"] = "CRITICAL";
    ComplianceFindingSeverity["HIGH"] = "HIGH";
    ComplianceFindingSeverity["MEDIUM"] = "MEDIUM";
    ComplianceFindingSeverity["LOW"] = "LOW";
    ComplianceFindingSeverity["INFORMATIONAL"] = "INFORMATIONAL";
})(ComplianceFindingSeverity || (exports.ComplianceFindingSeverity = ComplianceFindingSeverity = {}));
var ComplianceFindingStatus;
(function (ComplianceFindingStatus) {
    ComplianceFindingStatus["OPEN"] = "OPEN";
    ComplianceFindingStatus["IN_PROGRESS"] = "IN_PROGRESS";
    ComplianceFindingStatus["RESOLVED"] = "RESOLVED";
    ComplianceFindingStatus["ACCEPTED_RISK"] = "ACCEPTED_RISK";
    ComplianceFindingStatus["FALSE_POSITIVE"] = "FALSE_POSITIVE";
})(ComplianceFindingStatus || (exports.ComplianceFindingStatus = ComplianceFindingStatus = {}));
class CompliancePolicy extends base_entity_1.BaseEntity {
    constructor(props) {
        super(props, props.id);
        this.validateProps(props);
        this._name = props.name;
        this._description = props.description;
        this._framework = props.framework;
        this._version = props.version;
        this._tenantId = props.tenantId;
        this._controls = props.controls;
        this._enabled = props.enabled;
        this._createdBy = props.createdBy;
        this._lastModifiedBy = props.lastModifiedBy;
        this._assessments = props.assessments || [];
        this._reports = props.reports || [];
    }
    static create(props) {
        return new CompliancePolicy({
            ...props,
            lastModifiedBy: props.createdBy,
            assessments: [],
            reports: []
        });
    }
    assessCompliance(controlId, assessedBy) {
        const control = this._controls.find(c => c.id === controlId);
        if (!control) {
            throw new validation_exception_1.ValidationException(`Control with ID '${controlId}' not found`, []);
        }
        // Perform automated checks if available
        const findings = this.performAutomatedChecks(control);
        // Determine overall status based on findings
        const status = this.determineComplianceStatus(findings);
        const assessment = {
            id: unique_entity_id_value_object_1.UniqueEntityId.generate(),
            policyId: this.id,
            controlId,
            assessedBy,
            assessedAt: timestamp_value_object_1.Timestamp.now(),
            status,
            findings,
            recommendations: this.generateRecommendations(findings),
            nextAssessmentDue: this.calculateNextAssessmentDate(control),
            evidence: control.evidence
        };
        this._assessments.push(assessment);
        // Update control status
        control.status = status;
        control.lastAssessed = assessment.assessedAt;
        control.nextAssessment = assessment.nextAssessmentDue;
        this.touch();
        return assessment;
    }
    generateComplianceReport(framework, reportPeriod, generatedBy) {
        const frameworkControls = this._controls.filter(c => c.framework === framework);
        const recentAssessments = this._assessments.filter(a => a.assessedAt.value >= reportPeriod.startDate.value &&
            a.assessedAt.value <= reportPeriod.endDate.value);
        const controlsSummary = this.calculateControlsSummary(frameworkControls);
        const allFindings = recentAssessments.flatMap(a => a.findings);
        const recommendations = this.generateFrameworkRecommendations(framework, allFindings);
        const report = {
            id: unique_entity_id_value_object_1.UniqueEntityId.generate(),
            policyId: this.id,
            framework,
            generatedAt: timestamp_value_object_1.Timestamp.now(),
            generatedBy,
            reportPeriod,
            overallStatus: this.calculateOverallStatus(controlsSummary),
            controlsSummary,
            findings: allFindings,
            recommendations,
            executiveSummary: this.generateExecutiveSummary(framework, controlsSummary, allFindings)
        };
        this._reports.push(report);
        this.touch();
        return report;
    }
    addControl(control, modifiedBy) {
        this.validateControl(control);
        // Check for duplicate control IDs
        if (this._controls.some(c => c.id === control.id)) {
            throw new validation_exception_1.ValidationException(`Control with ID '${control.id}' already exists`, []);
        }
        this._controls.push(control);
        this._lastModifiedBy = modifiedBy;
        this.touch();
    }
    updateControl(controlId, updates, modifiedBy) {
        const controlIndex = this._controls.findIndex(c => c.id === controlId);
        if (controlIndex === -1) {
            throw new validation_exception_1.ValidationException(`Control with ID '${controlId}' not found`, []);
        }
        const updatedControl = { ...this._controls[controlIndex], ...updates };
        this.validateControl(updatedControl);
        this._controls[controlIndex] = updatedControl;
        this._lastModifiedBy = modifiedBy;
        this.touch();
    }
    removeControl(controlId, modifiedBy) {
        const controlIndex = this._controls.findIndex(c => c.id === controlId);
        if (controlIndex === -1) {
            throw new validation_exception_1.ValidationException(`Control with ID '${controlId}' not found`, []);
        }
        this._controls.splice(controlIndex, 1);
        this._lastModifiedBy = modifiedBy;
        this.touch();
    }
    addEvidence(controlId, evidence, modifiedBy) {
        const control = this._controls.find(c => c.id === controlId);
        if (!control) {
            throw new validation_exception_1.ValidationException(`Control with ID '${controlId}' not found`, []);
        }
        control.evidence.push(evidence);
        this._lastModifiedBy = modifiedBy;
        this.touch();
    }
    getControlsRequiringAssessment() {
        const now = timestamp_value_object_1.Timestamp.now();
        return this._controls.filter(control => {
            if (!control.nextAssessment)
                return true;
            return control.nextAssessment.value <= now.value;
        });
    }
    getComplianceStatusSummary() {
        const summary = {};
        for (const framework of Object.values(ComplianceFramework)) {
            const frameworkControls = this._controls.filter(c => c.framework === framework);
            if (frameworkControls.length === 0) {
                summary[framework] = ComplianceStatus.NOT_ASSESSED;
                continue;
            }
            const statuses = frameworkControls.map(c => c.status);
            summary[framework] = this.aggregateComplianceStatus(statuses);
        }
        return summary;
    }
    performAutomatedChecks(control) {
        const findings = [];
        if (!control.automatedCheck) {
            return findings;
        }
        // Simulate automated compliance checks based on control type
        switch (control.type) {
            case ComplianceControlType.PREVENTIVE:
                findings.push(...this.checkPreventiveControls(control));
                break;
            case ComplianceControlType.DETECTIVE:
                findings.push(...this.checkDetectiveControls(control));
                break;
            case ComplianceControlType.CORRECTIVE:
                findings.push(...this.checkCorrectiveControls(control));
                break;
        }
        return findings;
    }
    checkPreventiveControls(control) {
        // Placeholder for preventive control checks
        // In real implementation, this would integrate with security tools
        return [];
    }
    checkDetectiveControls(control) {
        // Placeholder for detective control checks
        // In real implementation, this would check monitoring systems
        return [];
    }
    checkCorrectiveControls(control) {
        // Placeholder for corrective control checks
        // In real implementation, this would verify remediation processes
        return [];
    }
    determineComplianceStatus(findings) {
        if (findings.length === 0) {
            return ComplianceStatus.COMPLIANT;
        }
        const criticalFindings = findings.filter(f => f.severity === ComplianceFindingSeverity.CRITICAL);
        const highFindings = findings.filter(f => f.severity === ComplianceFindingSeverity.HIGH);
        if (criticalFindings.length > 0) {
            return ComplianceStatus.NON_COMPLIANT;
        }
        if (highFindings.length > 0) {
            return ComplianceStatus.PARTIALLY_COMPLIANT;
        }
        return ComplianceStatus.PARTIALLY_COMPLIANT;
    }
    generateRecommendations(findings) {
        return findings.map(f => f.recommendation).filter(r => r && r.trim().length > 0);
    }
    calculateNextAssessmentDate(control) {
        const now = new Date();
        let nextDate = new Date(now);
        switch (control.checkFrequency) {
            case ComplianceCheckFrequency.DAILY:
                nextDate.setDate(now.getDate() + 1);
                break;
            case ComplianceCheckFrequency.WEEKLY:
                nextDate.setDate(now.getDate() + 7);
                break;
            case ComplianceCheckFrequency.MONTHLY:
                nextDate.setMonth(now.getMonth() + 1);
                break;
            case ComplianceCheckFrequency.QUARTERLY:
                nextDate.setMonth(now.getMonth() + 3);
                break;
            case ComplianceCheckFrequency.ANNUALLY:
                nextDate.setFullYear(now.getFullYear() + 1);
                break;
            default:
                nextDate.setMonth(now.getMonth() + 3); // Default to quarterly
        }
        return timestamp_value_object_1.Timestamp.create(nextDate);
    }
    calculateControlsSummary(controls) {
        const summary = {
            total: controls.length,
            compliant: 0,
            nonCompliant: 0,
            partiallyCompliant: 0,
            notAssessed: 0
        };
        controls.forEach(control => {
            switch (control.status) {
                case ComplianceStatus.COMPLIANT:
                    summary.compliant++;
                    break;
                case ComplianceStatus.NON_COMPLIANT:
                    summary.nonCompliant++;
                    break;
                case ComplianceStatus.PARTIALLY_COMPLIANT:
                    summary.partiallyCompliant++;
                    break;
                case ComplianceStatus.NOT_ASSESSED:
                    summary.notAssessed++;
                    break;
            }
        });
        return summary;
    }
    calculateOverallStatus(summary) {
        if (summary.total === 0)
            return ComplianceStatus.NOT_ASSESSED;
        if (summary.nonCompliant > 0)
            return ComplianceStatus.NON_COMPLIANT;
        if (summary.partiallyCompliant > 0)
            return ComplianceStatus.PARTIALLY_COMPLIANT;
        if (summary.notAssessed > 0)
            return ComplianceStatus.NOT_ASSESSED;
        return ComplianceStatus.COMPLIANT;
    }
    generateFrameworkRecommendations(framework, findings) {
        const recommendations = new Set();
        findings.forEach(finding => {
            if (finding.recommendation) {
                recommendations.add(finding.recommendation);
            }
        });
        // Add framework-specific recommendations
        switch (framework) {
            case ComplianceFramework.SOC2:
                recommendations.add('Ensure continuous monitoring of security controls');
                recommendations.add('Maintain comprehensive audit logs');
                break;
            case ComplianceFramework.GDPR:
                recommendations.add('Implement data protection by design and by default');
                recommendations.add('Maintain records of processing activities');
                break;
            // Add more framework-specific recommendations as needed
        }
        return Array.from(recommendations);
    }
    generateExecutiveSummary(framework, summary, findings) {
        const compliancePercentage = Math.round((summary.compliant / summary.total) * 100);
        const criticalFindings = findings.filter(f => f.severity === ComplianceFindingSeverity.CRITICAL).length;
        return `${framework} Compliance Assessment: ${compliancePercentage}% compliant. ` +
            `${summary.total} controls assessed, ${summary.compliant} compliant, ` +
            `${summary.nonCompliant} non-compliant. ${criticalFindings} critical findings require immediate attention.`;
    }
    aggregateComplianceStatus(statuses) {
        if (statuses.includes(ComplianceStatus.NON_COMPLIANT)) {
            return ComplianceStatus.NON_COMPLIANT;
        }
        if (statuses.includes(ComplianceStatus.PARTIALLY_COMPLIANT)) {
            return ComplianceStatus.PARTIALLY_COMPLIANT;
        }
        if (statuses.includes(ComplianceStatus.NOT_ASSESSED)) {
            return ComplianceStatus.NOT_ASSESSED;
        }
        return ComplianceStatus.COMPLIANT;
    }
    touch() {
        // Update the last modified timestamp
        // This would typically update an updatedAt field if we had one
    }
    validateProps(props) {
        if (!props.name || props.name.trim().length === 0) {
            throw new validation_exception_1.ValidationException('Policy name is required', []);
        }
        if (!props.description || props.description.trim().length === 0) {
            throw new validation_exception_1.ValidationException('Policy description is required', []);
        }
        if (!Object.values(ComplianceFramework).includes(props.framework)) {
            throw new validation_exception_1.ValidationException('Valid compliance framework is required', []);
        }
        if (!props.version || props.version.trim().length === 0) {
            throw new validation_exception_1.ValidationException('Policy version is required', []);
        }
        if (!props.tenantId) {
            throw new validation_exception_1.ValidationException('Tenant ID is required', []);
        }
        if (!props.createdBy) {
            throw new validation_exception_1.ValidationException('Created by user ID is required', []);
        }
        if (!Array.isArray(props.controls)) {
            throw new validation_exception_1.ValidationException('Controls must be an array', []);
        }
        props.controls.forEach((control) => this.validateControl(control));
    }
    validateControl(control) {
        if (!control.id || control.id.trim().length === 0) {
            throw new validation_exception_1.ValidationException('Control ID is required', []);
        }
        if (!control.name || control.name.trim().length === 0) {
            throw new validation_exception_1.ValidationException('Control name is required', []);
        }
        if (!control.controlId || control.controlId.trim().length === 0) {
            throw new validation_exception_1.ValidationException('Control framework ID is required', []);
        }
        if (!Object.values(ComplianceFramework).includes(control.framework)) {
            throw new validation_exception_1.ValidationException('Valid compliance framework is required for control', []);
        }
        if (!Object.values(ComplianceControlType).includes(control.type)) {
            throw new validation_exception_1.ValidationException('Valid control type is required', []);
        }
        if (!control.responsible) {
            throw new validation_exception_1.ValidationException('Control responsible user is required', []);
        }
        if (!Array.isArray(control.requirements)) {
            throw new validation_exception_1.ValidationException('Control requirements must be an array', []);
        }
        if (!Array.isArray(control.evidence)) {
            throw new validation_exception_1.ValidationException('Control evidence must be an array', []);
        }
    }
    // Getters
    get name() { return this._name; }
    get description() { return this._description; }
    get framework() { return this._framework; }
    get version() { return this._version; }
    get tenantId() { return this._tenantId; }
    get controls() { return [...this._controls]; }
    get enabled() { return this._enabled; }
    get createdBy() { return this._createdBy; }
    get lastModifiedBy() { return this._lastModifiedBy; }
    get assessments() { return [...this._assessments]; }
    get reports() { return [...this._reports]; }
}
exports.CompliancePolicy = CompliancePolicy;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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