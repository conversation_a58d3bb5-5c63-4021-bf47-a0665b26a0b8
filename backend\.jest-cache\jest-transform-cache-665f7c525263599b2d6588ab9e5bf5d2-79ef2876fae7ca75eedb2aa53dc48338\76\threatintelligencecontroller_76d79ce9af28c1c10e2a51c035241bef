ac5cee711a7ca97884d543986602b7a8
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThreatIntelligenceController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const threat_intelligence_service_1 = require("../../application/services/threat-intelligence.service");
const jwt_auth_guard_1 = require("../../../../infrastructure/auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../../../../infrastructure/auth/guards/roles.guard");
const roles_decorator_1 = require("../../../../infrastructure/auth/decorators/roles.decorator");
const create_threat_intelligence_dto_1 = require("../dto/create-threat-intelligence.dto");
const update_threat_intelligence_dto_1 = require("../dto/update-threat-intelligence.dto");
const threat_intelligence_search_dto_1 = require("../dto/threat-intelligence-search.dto");
const threat_correlation_dto_1 = require("../dto/threat-correlation.dto");
const threat_intelligence_response_dto_1 = require("../dto/threat-intelligence-response.dto");
/**
 * Controller for threat intelligence management
 * Provides RESTful endpoints for threat intelligence operations
 */
let ThreatIntelligenceController = class ThreatIntelligenceController {
    constructor(threatIntelligenceService) {
        this.threatIntelligenceService = threatIntelligenceService;
    }
    /**
     * Get threat intelligence dashboard data
     */
    async getDashboard() {
        return await this.threatIntelligenceService.getDashboardData();
    }
    /**
     * Search threat intelligence with advanced filtering
     */
    async searchThreatIntelligence(searchDto) {
        const criteria = {
            page: searchDto.page,
            limit: searchDto.limit,
            threatTypes: searchDto.threatTypes,
            severities: searchDto.severities,
            statuses: searchDto.statuses,
            confidenceLevels: searchDto.confidenceLevels,
            tags: searchDto.tags,
            searchText: searchDto.searchText,
            dateRange: searchDto.startDate && searchDto.endDate ? {
                startDate: new Date(searchDto.startDate),
                endDate: new Date(searchDto.endDate),
            } : undefined,
            riskScoreMin: searchDto.riskScoreMin,
            riskScoreMax: searchDto.riskScoreMax,
            threatActorId: searchDto.threatActorId,
            campaignId: searchDto.campaignId,
            dataSource: searchDto.dataSource,
            includeExpired: searchDto.includeExpired,
            sortBy: searchDto.sortBy,
            sortOrder: searchDto.sortOrder,
        };
        return await this.threatIntelligenceService.searchThreatIntelligence(criteria);
    }
    /**
     * Get all threat intelligence (paginated)
     */
    async getAllThreatIntelligence(page = 1, limit = 50) {
        return await this.threatIntelligenceService.searchThreatIntelligence({
            page,
            limit,
        });
    }
    /**
     * Get threat intelligence by ID
     */
    async getThreatIntelligenceById(id) {
        return await this.threatIntelligenceService.getThreatIntelligenceById(id);
    }
    /**
     * Create new threat intelligence
     */
    async createThreatIntelligence(createDto, req) {
        const userId = req.user.id;
        return await this.threatIntelligenceService.createThreatIntelligence(createDto, userId);
    }
    /**
     * Update threat intelligence
     */
    async updateThreatIntelligence(id, updateDto, req) {
        const userId = req.user.id;
        return await this.threatIntelligenceService.updateThreatIntelligence(id, updateDto, userId);
    }
    /**
     * Delete threat intelligence
     */
    async deleteThreatIntelligence(id, req) {
        const userId = req.user.id;
        await this.threatIntelligenceService.deleteThreatIntelligence(id, userId);
    }
    /**
     * Record observation of threat intelligence
     */
    async recordObservation(id, body) {
        return await this.threatIntelligenceService.recordObservation(id, body.context);
    }
    /**
     * Find related threat intelligence
     */
    async findRelatedThreats(id, correlationDto) {
        const criteria = {
            threatIntelligenceId: id,
            correlationTypes: correlationDto.correlationTypes,
            timeWindow: correlationDto.timeWindow,
            confidenceThreshold: correlationDto.confidenceThreshold,
        };
        return await this.threatIntelligenceService.findRelatedThreats(criteria);
    }
    /**
     * Export threat intelligence
     */
    async exportThreatIntelligence(body) {
        const { format, criteria = {} } = body;
        return await this.threatIntelligenceService.exportThreatIntelligence(criteria, format);
    }
    /**
     * Get threat intelligence statistics
     */
    async getStatistics() {
        return await this.threatIntelligenceService.getDashboardData();
    }
    /**
     * Get threat type distribution
     */
    async getThreatTypeDistribution() {
        const dashboardData = await this.threatIntelligenceService.getDashboardData();
        return dashboardData.distribution.threatTypes;
    }
    /**
     * Get top threat actors
     */
    async getTopThreatActors() {
        const dashboardData = await this.threatIntelligenceService.getDashboardData();
        return dashboardData.distribution.topActors;
    }
};
exports.ThreatIntelligenceController = ThreatIntelligenceController;
__decorate([
    (0, common_1.Get)('dashboard'),
    (0, roles_decorator_1.Roles)('security_analyst', 'threat_analyst', 'admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get threat intelligence dashboard data' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dashboard data retrieved successfully',
        type: Object,
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_b = typeof Promise !== "undefined" && Promise) === "function" ? _b : Object)
], ThreatIntelligenceController.prototype, "getDashboard", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, roles_decorator_1.Roles)('security_analyst', 'threat_analyst', 'admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Search threat intelligence with advanced filtering' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'threatTypes', required: false, type: [String] }),
    (0, swagger_1.ApiQuery)({ name: 'severities', required: false, type: [String] }),
    (0, swagger_1.ApiQuery)({ name: 'searchText', required: false, type: String }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Threat intelligence search results',
        type: Object,
    }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_c = typeof threat_intelligence_search_dto_1.ThreatIntelligenceSearchDto !== "undefined" && threat_intelligence_search_dto_1.ThreatIntelligenceSearchDto) === "function" ? _c : Object]),
    __metadata("design:returntype", typeof (_d = typeof Promise !== "undefined" && Promise) === "function" ? _d : Object)
], ThreatIntelligenceController.prototype, "searchThreatIntelligence", null);
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)('security_analyst', 'threat_analyst', 'admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all threat intelligence with pagination' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Threat intelligence list retrieved successfully',
        type: [threat_intelligence_response_dto_1.ThreatIntelligenceResponseDto],
    }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", typeof (_e = typeof Promise !== "undefined" && Promise) === "function" ? _e : Object)
], ThreatIntelligenceController.prototype, "getAllThreatIntelligence", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)('security_analyst', 'threat_analyst', 'admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get threat intelligence by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Threat intelligence ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Threat intelligence retrieved successfully',
        type: threat_intelligence_response_dto_1.ThreatIntelligenceResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Threat intelligence not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_f = typeof Promise !== "undefined" && Promise) === "function" ? _f : Object)
], ThreatIntelligenceController.prototype, "getThreatIntelligenceById", null);
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)('threat_analyst', 'admin'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({ summary: 'Create new threat intelligence' }),
    (0, swagger_1.ApiBody)({ type: create_threat_intelligence_dto_1.CreateThreatIntelligenceDto }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Threat intelligence created successfully',
        type: threat_intelligence_response_dto_1.ThreatIntelligenceResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid input data' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Threat intelligence already exists' }),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true })),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_g = typeof create_threat_intelligence_dto_1.CreateThreatIntelligenceDto !== "undefined" && create_threat_intelligence_dto_1.CreateThreatIntelligenceDto) === "function" ? _g : Object, Object]),
    __metadata("design:returntype", typeof (_h = typeof Promise !== "undefined" && Promise) === "function" ? _h : Object)
], ThreatIntelligenceController.prototype, "createThreatIntelligence", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, roles_decorator_1.Roles)('threat_analyst', 'admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Update threat intelligence' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Threat intelligence ID' }),
    (0, swagger_1.ApiBody)({ type: update_threat_intelligence_dto_1.UpdateThreatIntelligenceDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Threat intelligence updated successfully',
        type: threat_intelligence_response_dto_1.ThreatIntelligenceResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Threat intelligence not found' }),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true })),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, typeof (_j = typeof update_threat_intelligence_dto_1.UpdateThreatIntelligenceDto !== "undefined" && update_threat_intelligence_dto_1.UpdateThreatIntelligenceDto) === "function" ? _j : Object, Object]),
    __metadata("design:returntype", typeof (_k = typeof Promise !== "undefined" && Promise) === "function" ? _k : Object)
], ThreatIntelligenceController.prototype, "updateThreatIntelligence", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)('threat_analyst', 'admin'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Delete threat intelligence' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Threat intelligence ID' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Threat intelligence deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Threat intelligence not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", typeof (_l = typeof Promise !== "undefined" && Promise) === "function" ? _l : Object)
], ThreatIntelligenceController.prototype, "deleteThreatIntelligence", null);
__decorate([
    (0, common_1.Post)(':id/observe'),
    (0, roles_decorator_1.Roles)('security_analyst', 'threat_analyst', 'admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Record observation of threat intelligence' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Threat intelligence ID' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                context: {
                    type: 'object',
                    description: 'Observation context',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Observation recorded successfully',
        type: threat_intelligence_response_dto_1.ThreatIntelligenceResponseDto,
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", typeof (_m = typeof Promise !== "undefined" && Promise) === "function" ? _m : Object)
], ThreatIntelligenceController.prototype, "recordObservation", null);
__decorate([
    (0, common_1.Post)(':id/correlate'),
    (0, roles_decorator_1.Roles)('security_analyst', 'threat_analyst', 'admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Find related threat intelligence' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Threat intelligence ID' }),
    (0, swagger_1.ApiBody)({ type: threat_correlation_dto_1.ThreatCorrelationDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Related threats found successfully',
        type: Object,
    }),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true })),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, typeof (_o = typeof threat_correlation_dto_1.ThreatCorrelationDto !== "undefined" && threat_correlation_dto_1.ThreatCorrelationDto) === "function" ? _o : Object]),
    __metadata("design:returntype", typeof (_p = typeof Promise !== "undefined" && Promise) === "function" ? _p : Object)
], ThreatIntelligenceController.prototype, "findRelatedThreats", null);
__decorate([
    (0, common_1.Post)('export'),
    (0, roles_decorator_1.Roles)('security_analyst', 'threat_analyst', 'admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Export threat intelligence data' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                format: {
                    type: 'string',
                    enum: ['json', 'csv', 'stix'],
                    description: 'Export format',
                },
                criteria: {
                    type: 'object',
                    description: 'Search criteria for export',
                },
            },
            required: ['format'],
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Data exported successfully',
        type: Object,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", typeof (_q = typeof Promise !== "undefined" && Promise) === "function" ? _q : Object)
], ThreatIntelligenceController.prototype, "exportThreatIntelligence", null);
__decorate([
    (0, common_1.Get)('stats/overview'),
    (0, roles_decorator_1.Roles)('security_analyst', 'threat_analyst', 'admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get threat intelligence statistics' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Statistics retrieved successfully',
        type: Object,
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_r = typeof Promise !== "undefined" && Promise) === "function" ? _r : Object)
], ThreatIntelligenceController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)('stats/threat-types'),
    (0, roles_decorator_1.Roles)('security_analyst', 'threat_analyst', 'admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get threat type distribution' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Threat type distribution retrieved successfully',
        type: Object,
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_s = typeof Promise !== "undefined" && Promise) === "function" ? _s : Object)
], ThreatIntelligenceController.prototype, "getThreatTypeDistribution", null);
__decorate([
    (0, common_1.Get)('stats/top-actors'),
    (0, roles_decorator_1.Roles)('security_analyst', 'threat_analyst', 'admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get top threat actors' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Top threat actors retrieved successfully',
        type: Object,
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_t = typeof Promise !== "undefined" && Promise) === "function" ? _t : Object)
], ThreatIntelligenceController.prototype, "getTopThreatActors", null);
exports.ThreatIntelligenceController = ThreatIntelligenceController = __decorate([
    (0, swagger_1.ApiTags)('Threat Intelligence'),
    (0, common_1.Controller)('api/threat-intelligence'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [typeof (_a = typeof threat_intelligence_service_1.ThreatIntelligenceService !== "undefined" && threat_intelligence_service_1.ThreatIntelligenceService) === "function" ? _a : Object])
], ThreatIntelligenceController);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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