bb762deeede4574177bf5182e48130e9
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var LocalStrategy_1;
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocalStrategy = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const passport_local_1 = require("passport-local");
const auth_service_1 = require("../auth.service");
/**
 * Local authentication strategy for Passport
 * Validates username/password credentials for login
 */
let LocalStrategy = LocalStrategy_1 = class LocalStrategy extends (0, passport_1.PassportStrategy)(passport_local_1.Strategy, 'local') {
    constructor(authService) {
        super({
            usernameField: 'email', // Use email instead of username
            passwordField: 'password',
            passReqToCallback: true, // Pass request object to validate method
        });
        this.authService = authService;
        this.logger = new common_1.Logger(LocalStrategy_1.name);
    }
    /**
     * Validate user credentials
     * This method is called automatically by Passport during local authentication
     *
     * @param request Express request object
     * @param email User email address
     * @param password User password
     * @returns Promise<any> User object if credentials are valid
     */
    async validate(request, email, password) {
        try {
            this.logger.debug('Validating local authentication credentials', {
                email,
                ipAddress: request.ip,
                userAgent: request.get('User-Agent'),
            });
            // Validate input
            if (!email || !password) {
                this.logger.warn('Missing email or password in local authentication', {
                    hasEmail: !!email,
                    hasPassword: !!password,
                    ipAddress: request.ip,
                });
                throw new common_1.UnauthorizedException('Email and password are required');
            }
            // Normalize email
            const normalizedEmail = email.toLowerCase().trim();
            // Validate email format
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(normalizedEmail)) {
                this.logger.warn('Invalid email format in local authentication', {
                    email: normalizedEmail,
                    ipAddress: request.ip,
                });
                throw new common_1.UnauthorizedException('Invalid email format');
            }
            // Validate password length
            if (password.length < 8) {
                this.logger.warn('Password too short in local authentication', {
                    email: normalizedEmail,
                    passwordLength: password.length,
                    ipAddress: request.ip,
                });
                throw new common_1.UnauthorizedException('Invalid credentials');
            }
            // Authenticate user
            const user = await this.authService.validateUser(normalizedEmail, password);
            if (!user) {
                this.logger.warn('Local authentication failed - invalid credentials', {
                    email: normalizedEmail,
                    ipAddress: request.ip,
                    userAgent: request.get('User-Agent'),
                });
                throw new common_1.UnauthorizedException('Invalid credentials');
            }
            // Log successful authentication
            this.logger.log('Local authentication successful', {
                userId: user.id,
                email: user.email,
                ipAddress: request.ip,
                userAgent: request.get('User-Agent'),
            });
            // Return user object with request metadata
            return {
                ...user,
                authMetadata: {
                    ipAddress: request.ip,
                    userAgent: request.get('User-Agent'),
                    timestamp: new Date(),
                    method: 'local',
                },
            };
        }
        catch (error) {
            this.logger.error('Local authentication error', {
                email,
                error: error.message,
                ipAddress: request.ip,
                userAgent: request.get('User-Agent'),
            });
            if (error instanceof common_1.UnauthorizedException) {
                throw error;
            }
            throw new common_1.UnauthorizedException('Authentication failed');
        }
    }
};
exports.LocalStrategy = LocalStrategy;
exports.LocalStrategy = LocalStrategy = LocalStrategy_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof auth_service_1.AuthService !== "undefined" && auth_service_1.AuthService) === "function" ? _a : Object])
], LocalStrategy);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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