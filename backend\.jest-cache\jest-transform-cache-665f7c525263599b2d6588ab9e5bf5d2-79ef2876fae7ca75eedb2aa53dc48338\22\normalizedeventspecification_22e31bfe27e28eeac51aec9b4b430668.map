{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\normalized-event.specification.ts", "mappings": ";;;AAAA,6DAA8E;AAO9E;;;;;GAKG;AACH,MAAsB,4BAA6B,SAAQ,iCAAkC;IAC3F;;OAEG;IACO,cAAc,CAAC,KAAsB,EAAE,KAAkB;QACjE,OAAO,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACO,kBAAkB,CAAC,KAAsB,EAAE,UAA2B;QAC9E,OAAO,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACO,gBAAgB,CAAC,KAAsB,EAAE,QAAuB;QACxE,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACO,0BAA0B,CAAC,KAAsB,EAAE,QAAiC;QAC5F,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACO,6BAA6B,CAAC,KAAsB,EAAE,QAA+B;QAC7F,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACO,SAAS,CAAC,KAAsB,EAAE,IAAc;QACxD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACO,UAAU,CAAC,KAAsB,EAAE,IAAc;QACzD,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACO,6BAA6B,CAAC,KAAsB,EAAE,QAAiB,EAAE,QAAiB;QAClG,MAAM,gBAAgB,GAAG,KAAK,CAAC,gBAAgB,CAAC;QAEhD,IAAI,gBAAgB,KAAK,SAAS,EAAE,CAAC;YACnC,OAAO,QAAQ,KAAK,SAAS,CAAC,CAAC,oDAAoD;QACrF,CAAC;QAED,IAAI,QAAQ,KAAK,SAAS,IAAI,gBAAgB,GAAG,QAAQ,EAAE,CAAC;YAC1D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,QAAQ,KAAK,SAAS,IAAI,gBAAgB,GAAG,QAAQ,EAAE,CAAC;YAC1D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACO,sBAAsB,CAAC,KAAsB,EAAE,QAAiB,EAAE,QAAiB;QAC3F,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;QAElC,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;YAC5B,OAAO,QAAQ,KAAK,SAAS,CAAC,CAAC,oDAAoD;QACrF,CAAC;QAED,IAAI,QAAQ,KAAK,SAAS,IAAI,SAAS,GAAG,QAAQ,EAAE,CAAC;YACnD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,QAAQ,KAAK,SAAS,IAAI,SAAS,GAAG,QAAQ,EAAE,CAAC;YACnD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACO,oBAAoB,CAAC,KAAsB,EAAE,OAAe;QACpE,OAAO,KAAK,CAAC,aAAa,KAAK,OAAO,CAAC;IACzC,CAAC;IAED;;OAEG;IACO,cAAc,CAAC,KAAsB,EAAE,MAAc;QAC7D,OAAO,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;IACtC,CAAC;CACF;AAzGD,oEAyGC;AAED;;;;GAIG;AACH,MAAa,mCAAoC,SAAQ,4BAA4B;IACnF,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,wBAAwB,EAAE,CAAC;IAC1C,CAAC;IAED,cAAc;QACZ,OAAO,8CAA8C,CAAC;IACxD,CAAC;CACF;AARD,kFAQC;AAED;;;;GAIG;AACH,MAAa,gCAAiC,SAAQ,4BAA4B;IAChF,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,qBAAqB,EAAE,CAAC;IACvC,CAAC;IAED,cAAc;QACZ,OAAO,2CAA2C,CAAC;IACrD,CAAC;CACF;AARD,4EAQC;AAED;;;;GAIG;AACH,MAAa,oCAAqC,SAAQ,4BAA4B;IACpF,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,yBAAyB,EAAE,CAAC;IAC3C,CAAC;IAED,cAAc;QACZ,OAAO,gDAAgD,CAAC;IAC1D,CAAC;CACF;AARD,oFAQC;AAED;;;;GAIG;AACH,MAAa,4BAA6B,SAAQ,4BAA4B;IAC5E,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,kBAAkB,EAAE,CAAC;IACpC,CAAC;IAED,cAAc;QACZ,OAAO,gDAAgD,CAAC;IAC1D,CAAC;CACF;AARD,oEAQC;AAED;;;;GAIG;AACH,MAAa,gCAAiC,SAAQ,4BAA4B;IAChF,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,mBAAmB,EAAE,CAAC;IACrC,CAAC;IAED,cAAc;QACZ,OAAO,wCAAwC,CAAC;IAClD,CAAC;CACF;AARD,4EAQC;AAED;;;;GAIG;AACH,MAAa,iCAAkC,SAAQ,4BAA4B;IACjF,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,oBAAoB,CAAC;IACpC,CAAC;IAED,cAAc;QACZ,OAAO,yCAAyC,CAAC;IACnD,CAAC;CACF;AARD,8EAQC;AAED;;;;GAIG;AACH,MAAa,8BAA+B,SAAQ,4BAA4B;IAC9E,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,mBAAmB,EAAE,CAAC;IACrC,CAAC;IAED,cAAc;QACZ,OAAO,qDAAqD,CAAC;IAC/D,CAAC;CACF;AARD,wEAQC;AAED;;;;GAIG;AACH,MAAa,gCAAiC,SAAQ,4BAA4B;IAChF,YAA6B,QAA+B;QAC1D,KAAK,EAAE,CAAC;QADmB,aAAQ,GAAR,QAAQ,CAAuB;IAE5D,CAAC;IAED,aAAa,CAAC,KAAsB;QAClC,OAAO,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAClE,CAAC;IAED,cAAc;QACZ,OAAO,sCAAsC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAC1E,CAAC;CACF;AAZD,4EAYC;AAED;;;;GAIG;AACH,MAAa,0BAA2B,SAAQ,4BAA4B;IAC1E,YAA6B,OAAe;QAC1C,KAAK,EAAE,CAAC;QADmB,YAAO,GAAP,OAAO,CAAQ;IAE5C,CAAC;IAED,aAAa,CAAC,KAAsB;QAClC,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IACxD,CAAC;IAED,cAAc;QACZ,OAAO,yCAAyC,IAAI,CAAC,OAAO,EAAE,CAAC;IACjE,CAAC;CACF;AAZD,gEAYC;AAED;;;;GAIG;AACH,MAAa,kCAAmC,SAAQ,4BAA4B;IAClF,YACmB,QAAiB,EACjB,QAAiB;QAElC,KAAK,EAAE,CAAC;QAHS,aAAQ,GAAR,QAAQ,CAAS;QACjB,aAAQ,GAAR,QAAQ,CAAS;IAGpC,CAAC;IAED,aAAa,CAAC,KAAsB;QAClC,OAAO,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACjF,CAAC;IAED,cAAc;QACZ,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC/D,OAAO,kDAAkD,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChG,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,mDAAmD,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC5E,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,kDAAkD,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC3E,CAAC;QACD,OAAO,6CAA6C,CAAC;IACvD,CAAC;CACF;AAtBD,gFAsBC;AAED;;;;GAIG;AACH,MAAa,wBAAyB,SAAQ,4BAA4B;IACxE,YAA6B,MAAc;QACzC,KAAK,EAAE,CAAC;QADmB,WAAM,GAAN,MAAM,CAAQ;IAE3C,CAAC;IAED,aAAa,CAAC,KAAsB;QAClC,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IACjD,CAAC;IAED,cAAc;QACZ,OAAO,sCAAsC,IAAI,CAAC,MAAM,EAAE,CAAC;IAC7D,CAAC;CACF;AAZD,4DAYC;AAED;;;;GAIG;AACH,MAAa,0BAA2B,SAAQ,4BAA4B;IAC1E,YAA6B,eAA+B;QAC1D,KAAK,EAAE,CAAC;QADmB,oBAAe,GAAf,eAAe,CAAgB;IAE5D,CAAC;IAED,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAC5D,CAAC;IAED,cAAc;QACZ,OAAO,+CAA+C,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,EAAE,CAAC;IAC1F,CAAC;CACF;AAZD,gEAYC;AAED;;;;GAIG;AACH,MAAa,gCAAiC,SAAQ,4BAA4B;IAChF,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,mCAAmC,EAAE,CAAC;IACrD,CAAC;IAED,cAAc;QACZ,OAAO,8DAA8D,CAAC;IACxE,CAAC;CACF;AARD,4EAQC;AAED;;;;GAIG;AACH,MAAa,oCAAqC,SAAQ,4BAA4B;IACpF,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,UAAU,EAAE,CAAC;IAC5B,CAAC;IAED,cAAc;QACZ,OAAO,8CAA8C,CAAC;IACxD,CAAC;CACF;AARD,oFAQC;AAED;;;;GAIG;AACH,MAAa,0BAA2B,SAAQ,4BAA4B;IAC1E,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,UAAU,KAAK,SAAS,CAAC;IACxC,CAAC;IAED,cAAc;QACZ,OAAO,6CAA6C,CAAC;IACvD,CAAC;CACF;AARD,gEAQC;AAED;;;;GAIG;AACH,MAAa,0BAA2B,SAAQ,4BAA4B;IAC1E,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,oBAAoB,IAAI,KAAK,CAAC,UAAU,KAAK,SAAS,CAAC;IACtE,CAAC;IAED,cAAc;QACZ,OAAO,2CAA2C,CAAC;IACrD,CAAC;CACF;AARD,gEAQC;AAED;;;;GAIG;AACH,MAAa,uCAAwC,SAAQ,4BAA4B;IACvF,YACmB,aAAsB,EACtB,aAAsB;QAEvC,KAAK,EAAE,CAAC;QAHS,kBAAa,GAAb,aAAa,CAAS;QACtB,kBAAa,GAAb,aAAa,CAAS;IAGzC,CAAC;IAED,aAAa,CAAC,KAAsB;QAClC,MAAM,QAAQ,GAAG,KAAK,CAAC,wBAAwB,EAAE,CAAC;QAElD,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;YACtB,OAAO,KAAK,CAAC,CAAC,wBAAwB;QACxC,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,IAAI,QAAQ,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YACtE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,IAAI,QAAQ,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YACtE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,cAAc;QACZ,MAAM,cAAc,GAAG,CAAC,EAAU,EAAE,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC;QAEjD,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YACzE,OAAO,wCAAwC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;QAChI,CAAC;aAAM,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YAC5C,OAAO,yCAAyC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;QACvF,CAAC;aAAM,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YAC5C,OAAO,wCAAwC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;QACtF,CAAC;QACD,OAAO,mCAAmC,CAAC;IAC7C,CAAC;CACF;AAtCD,0FAsCC;AAED;;;;GAIG;AACH,MAAa,mCAAmC;IAAhD;QACU,mBAAc,GAAmC,EAAE,CAAC;IA0L9D,CAAC;IAxLC;;OAEG;IACH,sBAAsB;QACpB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,mCAAmC,EAAE,CAAC,CAAC;QACpE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,gCAAgC,EAAE,CAAC,CAAC;QACjE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,uBAAuB;QACrB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,oCAAoC,EAAE,CAAC,CAAC;QACrE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,eAAe;QACb,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,4BAA4B,EAAE,CAAC,CAAC;QAC7D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,gCAAgC,EAAE,CAAC,CAAC;QACjE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,iCAAiC,EAAE,CAAC,CAAC;QAClE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,8BAA8B,EAAE,CAAC,CAAC;QAC/D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,uBAAuB,CAAC,GAAG,QAA+B;QACxD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,gCAAgC,CAAC,QAAQ,CAAC,CAAC,CAAC;QACzE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,OAAe;QAC/B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,0BAA0B,CAAC,OAAO,CAAC,CAAC,CAAC;QAClE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAC,QAAiB,EAAE,QAAiB;QACxD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,kCAAkC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;QACrF,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,MAAc;QAC5B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,wBAAwB,CAAC,MAAM,CAAC,CAAC,CAAC;QAC/D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,eAA+B;QAC/C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,0BAA0B,CAAC,eAAe,CAAC,CAAC,CAAC;QAC1E,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,gCAAgC,EAAE,CAAC,CAAC;QACjE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,oCAAoC,EAAE,CAAC,CAAC;QACrE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,0BAA0B,EAAE,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,aAAa;QACX,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,0BAA0B,EAAE,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,0BAA0B,CAAC,aAAsB,EAAE,aAAsB;QACvE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,uCAAuC,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC,CAAC;QACpG,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC;QAED,4CAA4C;QAC5C,IAAI,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpD,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAiC,CAAC;QAClF,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,WAAW;QACT,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC;QAED,2CAA2C;QAC3C,IAAI,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpD,QAAQ,GAAG,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAiC,CAAC;QACjF,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM;QACX,OAAO,IAAI,mCAAmC,EAAE,CAAC;IACnD,CAAC;CACF;AA3LD,kFA2LC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\normalized-event.specification.ts"], "sourcesContent": ["import { BaseSpecification, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { NormalizedEvent, NormalizationStatus } from '../entities/normalized-event.entity';\r\nimport { EventType } from '../enums/event-type.enum';\r\nimport { EventSeverity } from '../enums/event-severity.enum';\r\nimport { EventStatus } from '../enums/event-status.enum';\r\nimport { EventProcessingStatus } from '../enums/event-processing-status.enum';\r\n\r\n/**\r\n * NormalizedEvent Specification Base Class\r\n * \r\n * Base class for all normalized event-related specifications.\r\n * Provides common functionality for normalized event filtering and validation.\r\n */\r\nexport abstract class NormalizedEventSpecification extends BaseSpecification<NormalizedEvent> {\r\n  /**\r\n   * Helper method to check if normalized event matches any of the provided types\r\n   */\r\n  protected matchesAnyType(event: NormalizedEvent, types: EventType[]): boolean {\r\n    return types.includes(event.type);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if normalized event matches any of the provided severities\r\n   */\r\n  protected matchesAnySeverity(event: NormalizedEvent, severities: EventSeverity[]): boolean {\r\n    return severities.includes(event.severity);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if normalized event matches any of the provided statuses\r\n   */\r\n  protected matchesAnyStatus(event: NormalizedEvent, statuses: EventStatus[]): boolean {\r\n    return statuses.includes(event.status);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if normalized event matches any of the provided processing statuses\r\n   */\r\n  protected matchesAnyProcessingStatus(event: NormalizedEvent, statuses: EventProcessingStatus[]): boolean {\r\n    return statuses.includes(event.processingStatus);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if normalized event matches any of the provided normalization statuses\r\n   */\r\n  protected matchesAnyNormalizationStatus(event: NormalizedEvent, statuses: NormalizationStatus[]): boolean {\r\n    return statuses.includes(event.normalizationStatus);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if normalized event has any of the provided tags\r\n   */\r\n  protected hasAnyTag(event: NormalizedEvent, tags: string[]): boolean {\r\n    return event.tags.some(tag => tags.includes(tag));\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if normalized event has all of the provided tags\r\n   */\r\n  protected hasAllTags(event: NormalizedEvent, tags: string[]): boolean {\r\n    return tags.every(tag => event.tags.includes(tag));\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if data quality score is within range\r\n   */\r\n  protected isDataQualityScoreWithinRange(event: NormalizedEvent, minScore?: number, maxScore?: number): boolean {\r\n    const dataQualityScore = event.dataQualityScore;\r\n    \r\n    if (dataQualityScore === undefined) {\r\n      return minScore === undefined; // If no min score required, undefined is acceptable\r\n    }\r\n    \r\n    if (minScore !== undefined && dataQualityScore < minScore) {\r\n      return false;\r\n    }\r\n    \r\n    if (maxScore !== undefined && dataQualityScore > maxScore) {\r\n      return false;\r\n    }\r\n    \r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if risk score is within range\r\n   */\r\n  protected isRiskScoreWithinRange(event: NormalizedEvent, minScore?: number, maxScore?: number): boolean {\r\n    const riskScore = event.riskScore;\r\n    \r\n    if (riskScore === undefined) {\r\n      return minScore === undefined; // If no min score required, undefined is acceptable\r\n    }\r\n    \r\n    if (minScore !== undefined && riskScore < minScore) {\r\n      return false;\r\n    }\r\n    \r\n    if (maxScore !== undefined && riskScore > maxScore) {\r\n      return false;\r\n    }\r\n    \r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if schema version matches\r\n   */\r\n  protected matchesSchemaVersion(event: NormalizedEvent, version: string): boolean {\r\n    return event.schemaVersion === version;\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if applied rule exists\r\n   */\r\n  protected hasAppliedRule(event: NormalizedEvent, ruleId: string): boolean {\r\n    return event.hasAppliedRule(ruleId);\r\n  }\r\n}\r\n\r\n/**\r\n * Normalization Completed Specification\r\n * \r\n * Specification for normalized events that have completed normalization.\r\n */\r\nexport class NormalizationCompletedSpecification extends NormalizedEventSpecification {\r\n  isSatisfiedBy(event: NormalizedEvent): boolean {\r\n    return event.isNormalizationCompleted();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Normalized event has completed normalization';\r\n  }\r\n}\r\n\r\n/**\r\n * Normalization Failed Specification\r\n * \r\n * Specification for normalized events that have failed normalization.\r\n */\r\nexport class NormalizationFailedSpecification extends NormalizedEventSpecification {\r\n  isSatisfiedBy(event: NormalizedEvent): boolean {\r\n    return event.isNormalizationFailed();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Normalized event has failed normalization';\r\n  }\r\n}\r\n\r\n/**\r\n * Normalization In Progress Specification\r\n * \r\n * Specification for normalized events that are currently being normalized.\r\n */\r\nexport class NormalizationInProgressSpecification extends NormalizedEventSpecification {\r\n  isSatisfiedBy(event: NormalizedEvent): boolean {\r\n    return event.isNormalizationInProgress();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Normalized event is currently being normalized';\r\n  }\r\n}\r\n\r\n/**\r\n * High Data Quality Specification\r\n * \r\n * Specification for normalized events with high data quality scores.\r\n */\r\nexport class HighDataQualitySpecification extends NormalizedEventSpecification {\r\n  isSatisfiedBy(event: NormalizedEvent): boolean {\r\n    return event.hasHighDataQuality();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Normalized event has high data quality (>= 60)';\r\n  }\r\n}\r\n\r\n/**\r\n * Has Validation Errors Specification\r\n * \r\n * Specification for normalized events that have validation errors.\r\n */\r\nexport class HasValidationErrorsSpecification extends NormalizedEventSpecification {\r\n  isSatisfiedBy(event: NormalizedEvent): boolean {\r\n    return event.hasValidationErrors();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Normalized event has validation errors';\r\n  }\r\n}\r\n\r\n/**\r\n * Requires Manual Review Specification\r\n * \r\n * Specification for normalized events that require manual review.\r\n */\r\nexport class RequiresManualReviewSpecification extends NormalizedEventSpecification {\r\n  isSatisfiedBy(event: NormalizedEvent): boolean {\r\n    return event.requiresManualReview;\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Normalized event requires manual review';\r\n  }\r\n}\r\n\r\n/**\r\n * Ready For Next Stage Specification\r\n * \r\n * Specification for normalized events that are ready for the next processing stage.\r\n */\r\nexport class ReadyForNextStageSpecification extends NormalizedEventSpecification {\r\n  isSatisfiedBy(event: NormalizedEvent): boolean {\r\n    return event.isReadyForNextStage();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Normalized event is ready for next processing stage';\r\n  }\r\n}\r\n\r\n/**\r\n * Normalization Status Specification\r\n * \r\n * Specification for normalized events with specific normalization statuses.\r\n */\r\nexport class NormalizationStatusSpecification extends NormalizedEventSpecification {\r\n  constructor(private readonly statuses: NormalizationStatus[]) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: NormalizedEvent): boolean {\r\n    return this.matchesAnyNormalizationStatus(event, this.statuses);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Normalized event status is one of: ${this.statuses.join(', ')}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Schema Version Specification\r\n * \r\n * Specification for normalized events with specific schema versions.\r\n */\r\nexport class SchemaVersionSpecification extends NormalizedEventSpecification {\r\n  constructor(private readonly version: string) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: NormalizedEvent): boolean {\r\n    return this.matchesSchemaVersion(event, this.version);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Normalized event uses schema version: ${this.version}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Data Quality Score Range Specification\r\n * \r\n * Specification for normalized events within a specific data quality score range.\r\n */\r\nexport class DataQualityScoreRangeSpecification extends NormalizedEventSpecification {\r\n  constructor(\r\n    private readonly minScore?: number,\r\n    private readonly maxScore?: number\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: NormalizedEvent): boolean {\r\n    return this.isDataQualityScoreWithinRange(event, this.minScore, this.maxScore);\r\n  }\r\n\r\n  getDescription(): string {\r\n    if (this.minScore !== undefined && this.maxScore !== undefined) {\r\n      return `Normalized event data quality score is between ${this.minScore} and ${this.maxScore}`;\r\n    } else if (this.minScore !== undefined) {\r\n      return `Normalized event data quality score is at least ${this.minScore}`;\r\n    } else if (this.maxScore !== undefined) {\r\n      return `Normalized event data quality score is at most ${this.maxScore}`;\r\n    }\r\n    return 'Normalized event has any data quality score';\r\n  }\r\n}\r\n\r\n/**\r\n * Applied Rule Specification\r\n * \r\n * Specification for normalized events that have specific rules applied.\r\n */\r\nexport class AppliedRuleSpecification extends NormalizedEventSpecification {\r\n  constructor(private readonly ruleId: string) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: NormalizedEvent): boolean {\r\n    return this.hasAppliedRule(event, this.ruleId);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Normalized event has applied rule: ${this.ruleId}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Original Event Specification\r\n * \r\n * Specification for normalized events that reference a specific original event.\r\n */\r\nexport class OriginalEventSpecification extends NormalizedEventSpecification {\r\n  constructor(private readonly originalEventId: UniqueEntityId) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: NormalizedEvent): boolean {\r\n    return event.originalEventId.equals(this.originalEventId);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Normalized event references original event: ${this.originalEventId.toString()}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Exceeded Max Attempts Specification\r\n * \r\n * Specification for normalized events that have exceeded maximum normalization attempts.\r\n */\r\nexport class ExceededMaxAttemptsSpecification extends NormalizedEventSpecification {\r\n  isSatisfiedBy(event: NormalizedEvent): boolean {\r\n    return event.hasExceededMaxNormalizationAttempts();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Normalized event has exceeded maximum normalization attempts';\r\n  }\r\n}\r\n\r\n/**\r\n * High Risk Normalized Event Specification\r\n * \r\n * Specification for normalized events with high risk scores.\r\n */\r\nexport class HighRiskNormalizedEventSpecification extends NormalizedEventSpecification {\r\n  isSatisfiedBy(event: NormalizedEvent): boolean {\r\n    return event.isHighRisk();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Normalized event has high risk score (>= 80)';\r\n  }\r\n}\r\n\r\n/**\r\n * Reviewed Event Specification\r\n * \r\n * Specification for normalized events that have been manually reviewed.\r\n */\r\nexport class ReviewedEventSpecification extends NormalizedEventSpecification {\r\n  isSatisfiedBy(event: NormalizedEvent): boolean {\r\n    return event.reviewedAt !== undefined;\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Normalized event has been manually reviewed';\r\n  }\r\n}\r\n\r\n/**\r\n * Pending Review Specification\r\n * \r\n * Specification for normalized events that are pending manual review.\r\n */\r\nexport class PendingReviewSpecification extends NormalizedEventSpecification {\r\n  isSatisfiedBy(event: NormalizedEvent): boolean {\r\n    return event.requiresManualReview && event.reviewedAt === undefined;\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Normalized event is pending manual review';\r\n  }\r\n}\r\n\r\n/**\r\n * Normalization Duration Range Specification\r\n * \r\n * Specification for normalized events with normalization duration within a specific range.\r\n */\r\nexport class NormalizationDurationRangeSpecification extends NormalizedEventSpecification {\r\n  constructor(\r\n    private readonly minDurationMs?: number,\r\n    private readonly maxDurationMs?: number\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: NormalizedEvent): boolean {\r\n    const duration = event.getNormalizationDuration();\r\n    \r\n    if (duration === null) {\r\n      return false; // No duration available\r\n    }\r\n    \r\n    if (this.minDurationMs !== undefined && duration < this.minDurationMs) {\r\n      return false;\r\n    }\r\n    \r\n    if (this.maxDurationMs !== undefined && duration > this.maxDurationMs) {\r\n      return false;\r\n    }\r\n    \r\n    return true;\r\n  }\r\n\r\n  getDescription(): string {\r\n    const formatDuration = (ms: number) => `${ms}ms`;\r\n\r\n    if (this.minDurationMs !== undefined && this.maxDurationMs !== undefined) {\r\n      return `Normalized event duration is between ${formatDuration(this.minDurationMs)} and ${formatDuration(this.maxDurationMs)}`;\r\n    } else if (this.minDurationMs !== undefined) {\r\n      return `Normalized event duration is at least ${formatDuration(this.minDurationMs)}`;\r\n    } else if (this.maxDurationMs !== undefined) {\r\n      return `Normalized event duration is at most ${formatDuration(this.maxDurationMs)}`;\r\n    }\r\n    return 'Normalized event has any duration';\r\n  }\r\n}\r\n\r\n/**\r\n * Composite NormalizedEvent Specification Builder\r\n * \r\n * Builder for creating complex normalized event specifications using fluent interface.\r\n */\r\nexport class NormalizedEventSpecificationBuilder {\r\n  private specifications: NormalizedEventSpecification[] = [];\r\n\r\n  /**\r\n   * Add normalization completed filter\r\n   */\r\n  normalizationCompleted(): NormalizedEventSpecificationBuilder {\r\n    this.specifications.push(new NormalizationCompletedSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add normalization failed filter\r\n   */\r\n  normalizationFailed(): NormalizedEventSpecificationBuilder {\r\n    this.specifications.push(new NormalizationFailedSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add normalization in progress filter\r\n   */\r\n  normalizationInProgress(): NormalizedEventSpecificationBuilder {\r\n    this.specifications.push(new NormalizationInProgressSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add high data quality filter\r\n   */\r\n  highDataQuality(): NormalizedEventSpecificationBuilder {\r\n    this.specifications.push(new HighDataQualitySpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add has validation errors filter\r\n   */\r\n  hasValidationErrors(): NormalizedEventSpecificationBuilder {\r\n    this.specifications.push(new HasValidationErrorsSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add requires manual review filter\r\n   */\r\n  requiresManualReview(): NormalizedEventSpecificationBuilder {\r\n    this.specifications.push(new RequiresManualReviewSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add ready for next stage filter\r\n   */\r\n  readyForNextStage(): NormalizedEventSpecificationBuilder {\r\n    this.specifications.push(new ReadyForNextStageSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add normalization status filter\r\n   */\r\n  withNormalizationStatus(...statuses: NormalizationStatus[]): NormalizedEventSpecificationBuilder {\r\n    this.specifications.push(new NormalizationStatusSpecification(statuses));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add schema version filter\r\n   */\r\n  withSchemaVersion(version: string): NormalizedEventSpecificationBuilder {\r\n    this.specifications.push(new SchemaVersionSpecification(version));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add data quality score range filter\r\n   */\r\n  dataQualityScoreRange(minScore?: number, maxScore?: number): NormalizedEventSpecificationBuilder {\r\n    this.specifications.push(new DataQualityScoreRangeSpecification(minScore, maxScore));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add applied rule filter\r\n   */\r\n  withAppliedRule(ruleId: string): NormalizedEventSpecificationBuilder {\r\n    this.specifications.push(new AppliedRuleSpecification(ruleId));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add original event filter\r\n   */\r\n  fromOriginalEvent(originalEventId: UniqueEntityId): NormalizedEventSpecificationBuilder {\r\n    this.specifications.push(new OriginalEventSpecification(originalEventId));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add exceeded max attempts filter\r\n   */\r\n  exceededMaxAttempts(): NormalizedEventSpecificationBuilder {\r\n    this.specifications.push(new ExceededMaxAttemptsSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add high risk filter\r\n   */\r\n  highRisk(): NormalizedEventSpecificationBuilder {\r\n    this.specifications.push(new HighRiskNormalizedEventSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add reviewed filter\r\n   */\r\n  reviewed(): NormalizedEventSpecificationBuilder {\r\n    this.specifications.push(new ReviewedEventSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add pending review filter\r\n   */\r\n  pendingReview(): NormalizedEventSpecificationBuilder {\r\n    this.specifications.push(new PendingReviewSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add normalization duration range filter\r\n   */\r\n  normalizationDurationRange(minDurationMs?: number, maxDurationMs?: number): NormalizedEventSpecificationBuilder {\r\n    this.specifications.push(new NormalizationDurationRangeSpecification(minDurationMs, maxDurationMs));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Build the final specification using AND logic\r\n   */\r\n  build(): NormalizedEventSpecification {\r\n    if (this.specifications.length === 0) {\r\n      throw new Error('At least one specification must be added');\r\n    }\r\n\r\n    if (this.specifications.length === 1) {\r\n      return this.specifications[0];\r\n    }\r\n\r\n    // Combine all specifications with AND logic\r\n    let combined = this.specifications[0];\r\n    for (let i = 1; i < this.specifications.length; i++) {\r\n      combined = combined.and(this.specifications[i]) as NormalizedEventSpecification;\r\n    }\r\n\r\n    return combined;\r\n  }\r\n\r\n  /**\r\n   * Build the final specification using OR logic\r\n   */\r\n  buildWithOr(): NormalizedEventSpecification {\r\n    if (this.specifications.length === 0) {\r\n      throw new Error('At least one specification must be added');\r\n    }\r\n\r\n    if (this.specifications.length === 1) {\r\n      return this.specifications[0];\r\n    }\r\n\r\n    // Combine all specifications with OR logic\r\n    let combined = this.specifications[0];\r\n    for (let i = 1; i < this.specifications.length; i++) {\r\n      combined = combined.or(this.specifications[i]) as NormalizedEventSpecification;\r\n    }\r\n\r\n    return combined;\r\n  }\r\n\r\n  /**\r\n   * Create a new builder instance\r\n   */\r\n  static create(): NormalizedEventSpecificationBuilder {\r\n    return new NormalizedEventSpecificationBuilder();\r\n  }\r\n}"], "version": 3}