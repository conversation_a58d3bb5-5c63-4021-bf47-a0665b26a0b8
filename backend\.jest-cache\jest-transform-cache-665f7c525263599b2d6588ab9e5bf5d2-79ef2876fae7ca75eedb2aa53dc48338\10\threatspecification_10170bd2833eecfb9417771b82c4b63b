a0e63158005a833e781dcbfc7ed0c665
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThreatSpecificationBuilder = exports.IndicatorsCountSpecification = exports.AffectedAssetsSpecification = exports.RequiresImmediateAttentionSpecification = exports.ThreatMalwareFamilySpecification = exports.ThreatAttributionSpecification = exports.ThreatAgeRangeSpecification = exports.ThreatConfidenceRangeSpecification = exports.ThreatRiskScoreRangeSpecification = exports.ThreatTagSpecification = exports.ThreatTypeSpecification = exports.ThreatCategorySpecification = exports.ThreatSeveritySpecification = exports.StaleThreatSpecification = exports.RecentThreatSpecification = exports.HighConfidenceThreatSpecification = exports.HighRiskThreatSpecification = exports.ResolvedThreatSpecification = exports.ActiveThreatSpecification = exports.CriticalThreatSpecification = exports.HighSeverityThreatSpecification = exports.ThreatSpecification = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
const threat_severity_enum_1 = require("../enums/threat-severity.enum");
/**
 * Threat Specification Base Class
 *
 * Base class for all threat-related specifications.
 * Provides common functionality for threat filtering and validation.
 */
class ThreatSpecification extends shared_kernel_1.BaseSpecification {
    /**
     * Helper method to check if threat matches any of the provided severities
     */
    matchesAnySeverity(threat, severities) {
        return severities.includes(threat.severity);
    }
    /**
     * Helper method to check if threat matches any of the provided categories
     */
    matchesAnyCategory(threat, categories) {
        return categories.includes(threat.category);
    }
    /**
     * Helper method to check if threat matches any of the provided types
     */
    matchesAnyType(threat, types) {
        return types.includes(threat.type);
    }
    /**
     * Helper method to check if threat has any of the provided tags
     */
    hasAnyTag(threat, tags) {
        return threat.tags.some(tag => tags.includes(tag));
    }
    /**
     * Helper method to check if threat has all of the provided tags
     */
    hasAllTags(threat, tags) {
        return tags.every(tag => threat.tags.includes(tag));
    }
    /**
     * Helper method to check if threat age is within range
     */
    isAgeWithinRange(threat, minAgeHours, maxAgeHours) {
        const ageHours = threat.getAge();
        if (minAgeHours !== undefined && ageHours < minAgeHours) {
            return false;
        }
        if (maxAgeHours !== undefined && ageHours > maxAgeHours) {
            return false;
        }
        return true;
    }
    /**
     * Helper method to check if confidence is within range
     */
    isConfidenceWithinRange(threat, minConfidence, maxConfidence) {
        const confidence = threat.confidence;
        if (minConfidence !== undefined && confidence < minConfidence) {
            return false;
        }
        if (maxConfidence !== undefined && confidence > maxConfidence) {
            return false;
        }
        return true;
    }
    /**
     * Helper method to check if risk score is within range
     */
    isRiskScoreWithinRange(threat, minScore, maxScore) {
        const riskScore = threat.riskAssessment.riskScore;
        if (minScore !== undefined && riskScore < minScore) {
            return false;
        }
        if (maxScore !== undefined && riskScore > maxScore) {
            return false;
        }
        return true;
    }
}
exports.ThreatSpecification = ThreatSpecification;
/**
 * High Severity Threat Specification
 *
 * Specification for threats with high or critical severity.
 */
class HighSeverityThreatSpecification extends ThreatSpecification {
    isSatisfiedBy(threat) {
        return threat.isHighSeverity();
    }
    getDescription() {
        return 'Threat has high or critical severity';
    }
}
exports.HighSeverityThreatSpecification = HighSeverityThreatSpecification;
/**
 * Critical Threat Specification
 *
 * Specification for threats with critical severity only.
 */
class CriticalThreatSpecification extends ThreatSpecification {
    isSatisfiedBy(threat) {
        return threat.severity === threat_severity_enum_1.ThreatSeverity.CRITICAL;
    }
    getDescription() {
        return 'Threat has critical severity';
    }
}
exports.CriticalThreatSpecification = CriticalThreatSpecification;
/**
 * Active Threat Specification
 *
 * Specification for threats that are currently active (not resolved).
 */
class ActiveThreatSpecification extends ThreatSpecification {
    isSatisfiedBy(threat) {
        return threat.isActive();
    }
    getDescription() {
        return 'Threat is active (not resolved)';
    }
}
exports.ActiveThreatSpecification = ActiveThreatSpecification;
/**
 * Resolved Threat Specification
 *
 * Specification for threats that have been resolved.
 */
class ResolvedThreatSpecification extends ThreatSpecification {
    isSatisfiedBy(threat) {
        return threat.isResolved();
    }
    getDescription() {
        return 'Threat has been resolved';
    }
}
exports.ResolvedThreatSpecification = ResolvedThreatSpecification;
/**
 * High Risk Threat Specification
 *
 * Specification for threats with high risk scores.
 */
class HighRiskThreatSpecification extends ThreatSpecification {
    constructor(minRiskScore = 70) {
        super();
        this.minRiskScore = minRiskScore;
    }
    isSatisfiedBy(threat) {
        return threat.riskAssessment.riskScore >= this.minRiskScore;
    }
    getDescription() {
        return `Threat has high risk score (>= ${this.minRiskScore})`;
    }
}
exports.HighRiskThreatSpecification = HighRiskThreatSpecification;
/**
 * High Confidence Threat Specification
 *
 * Specification for threats with high confidence levels.
 */
class HighConfidenceThreatSpecification extends ThreatSpecification {
    constructor(minConfidence = 80) {
        super();
        this.minConfidence = minConfidence;
    }
    isSatisfiedBy(threat) {
        return threat.confidence >= this.minConfidence;
    }
    getDescription() {
        return `Threat has high confidence (>= ${this.minConfidence})`;
    }
}
exports.HighConfidenceThreatSpecification = HighConfidenceThreatSpecification;
/**
 * Recent Threat Specification
 *
 * Specification for threats that were detected recently.
 */
class RecentThreatSpecification extends ThreatSpecification {
    constructor(withinHours = 24) {
        super();
        this.withinHours = withinHours;
    }
    isSatisfiedBy(threat) {
        return threat.getAge() <= this.withinHours;
    }
    getDescription() {
        return `Threat was detected within ${this.withinHours} hours`;
    }
}
exports.RecentThreatSpecification = RecentThreatSpecification;
/**
 * Stale Threat Specification
 *
 * Specification for threats that are considered stale.
 */
class StaleThreatSpecification extends ThreatSpecification {
    constructor(olderThanHours = 168) {
        super();
        this.olderThanHours = olderThanHours;
    }
    isSatisfiedBy(threat) {
        return threat.getAge() > this.olderThanHours;
    }
    getDescription() {
        return `Threat is older than ${this.olderThanHours} hours`;
    }
}
exports.StaleThreatSpecification = StaleThreatSpecification;
/**
 * Threat Severity Specification
 *
 * Specification for threats of specific severities.
 */
class ThreatSeveritySpecification extends ThreatSpecification {
    constructor(severities) {
        super();
        this.severities = severities;
    }
    isSatisfiedBy(threat) {
        return this.matchesAnySeverity(threat, this.severities);
    }
    getDescription() {
        return `Threat severity is one of: ${this.severities.join(', ')}`;
    }
}
exports.ThreatSeveritySpecification = ThreatSeveritySpecification;
/**
 * Threat Category Specification
 *
 * Specification for threats of specific categories.
 */
class ThreatCategorySpecification extends ThreatSpecification {
    constructor(categories) {
        super();
        this.categories = categories;
    }
    isSatisfiedBy(threat) {
        return this.matchesAnyCategory(threat, this.categories);
    }
    getDescription() {
        return `Threat category is one of: ${this.categories.join(', ')}`;
    }
}
exports.ThreatCategorySpecification = ThreatCategorySpecification;
/**
 * Threat Type Specification
 *
 * Specification for threats of specific types.
 */
class ThreatTypeSpecification extends ThreatSpecification {
    constructor(types) {
        super();
        this.types = types;
    }
    isSatisfiedBy(threat) {
        return this.matchesAnyType(threat, this.types);
    }
    getDescription() {
        return `Threat type is one of: ${this.types.join(', ')}`;
    }
}
exports.ThreatTypeSpecification = ThreatTypeSpecification;
/**
 * Threat Tag Specification
 *
 * Specification for threats with specific tags.
 */
class ThreatTagSpecification extends ThreatSpecification {
    constructor(tags, requireAll = false) {
        super();
        this.tags = tags;
        this.requireAll = requireAll;
    }
    isSatisfiedBy(threat) {
        return this.requireAll
            ? this.hasAllTags(threat, this.tags)
            : this.hasAnyTag(threat, this.tags);
    }
    getDescription() {
        const operator = this.requireAll ? 'all' : 'any';
        return `Threat has ${operator} of these tags: ${this.tags.join(', ')}`;
    }
}
exports.ThreatTagSpecification = ThreatTagSpecification;
/**
 * Threat Risk Score Range Specification
 *
 * Specification for threats within a specific risk score range.
 */
class ThreatRiskScoreRangeSpecification extends ThreatSpecification {
    constructor(minScore, maxScore) {
        super();
        this.minScore = minScore;
        this.maxScore = maxScore;
    }
    isSatisfiedBy(threat) {
        return this.isRiskScoreWithinRange(threat, this.minScore, this.maxScore);
    }
    getDescription() {
        if (this.minScore !== undefined && this.maxScore !== undefined) {
            return `Threat risk score is between ${this.minScore} and ${this.maxScore}`;
        }
        else if (this.minScore !== undefined) {
            return `Threat risk score is at least ${this.minScore}`;
        }
        else if (this.maxScore !== undefined) {
            return `Threat risk score is at most ${this.maxScore}`;
        }
        return 'Threat has any risk score';
    }
}
exports.ThreatRiskScoreRangeSpecification = ThreatRiskScoreRangeSpecification;
/**
 * Threat Confidence Range Specification
 *
 * Specification for threats within a specific confidence range.
 */
class ThreatConfidenceRangeSpecification extends ThreatSpecification {
    constructor(minConfidence, maxConfidence) {
        super();
        this.minConfidence = minConfidence;
        this.maxConfidence = maxConfidence;
    }
    isSatisfiedBy(threat) {
        return this.isConfidenceWithinRange(threat, this.minConfidence, this.maxConfidence);
    }
    getDescription() {
        if (this.minConfidence !== undefined && this.maxConfidence !== undefined) {
            return `Threat confidence is between ${this.minConfidence} and ${this.maxConfidence}`;
        }
        else if (this.minConfidence !== undefined) {
            return `Threat confidence is at least ${this.minConfidence}`;
        }
        else if (this.maxConfidence !== undefined) {
            return `Threat confidence is at most ${this.maxConfidence}`;
        }
        return 'Threat has any confidence level';
    }
}
exports.ThreatConfidenceRangeSpecification = ThreatConfidenceRangeSpecification;
/**
 * Threat Age Range Specification
 *
 * Specification for threats within a specific age range.
 */
class ThreatAgeRangeSpecification extends ThreatSpecification {
    constructor(minAgeHours, maxAgeHours) {
        super();
        this.minAgeHours = minAgeHours;
        this.maxAgeHours = maxAgeHours;
    }
    isSatisfiedBy(threat) {
        return this.isAgeWithinRange(threat, this.minAgeHours, this.maxAgeHours);
    }
    getDescription() {
        if (this.minAgeHours !== undefined && this.maxAgeHours !== undefined) {
            return `Threat age is between ${this.minAgeHours} and ${this.maxAgeHours} hours`;
        }
        else if (this.minAgeHours !== undefined) {
            return `Threat is at least ${this.minAgeHours} hours old`;
        }
        else if (this.maxAgeHours !== undefined) {
            return `Threat is at most ${this.maxAgeHours} hours old`;
        }
        return 'Threat has any age';
    }
}
exports.ThreatAgeRangeSpecification = ThreatAgeRangeSpecification;
/**
 * Threat Attribution Specification
 *
 * Specification for threats with specific attribution properties.
 */
class ThreatAttributionSpecification extends ThreatSpecification {
    constructor(actor, campaign, hasAttribution = true) {
        super();
        this.actor = actor;
        this.campaign = campaign;
        this.hasAttribution = hasAttribution;
    }
    isSatisfiedBy(threat) {
        const attribution = threat.attribution;
        if (!this.hasAttribution) {
            return !attribution;
        }
        if (!attribution) {
            return false;
        }
        if (this.actor && attribution.actor !== this.actor) {
            return false;
        }
        if (this.campaign && !attribution.campaigns.includes(this.campaign)) {
            return false;
        }
        return true;
    }
    getDescription() {
        if (!this.hasAttribution) {
            return 'Threat has no attribution information';
        }
        if (this.actor && this.campaign) {
            return `Threat is attributed to actor "${this.actor}" in campaign "${this.campaign}"`;
        }
        else if (this.actor) {
            return `Threat is attributed to actor "${this.actor}"`;
        }
        else if (this.campaign) {
            return `Threat is part of campaign "${this.campaign}"`;
        }
        return 'Threat has attribution information';
    }
}
exports.ThreatAttributionSpecification = ThreatAttributionSpecification;
/**
 * Threat Malware Family Specification
 *
 * Specification for threats associated with specific malware families.
 */
class ThreatMalwareFamilySpecification extends ThreatSpecification {
    constructor(familyName, hasMalwareFamily = true) {
        super();
        this.familyName = familyName;
        this.hasMalwareFamily = hasMalwareFamily;
    }
    isSatisfiedBy(threat) {
        const malwareFamily = threat.malwareFamily;
        if (!this.hasMalwareFamily) {
            return !malwareFamily;
        }
        if (!malwareFamily) {
            return false;
        }
        if (this.familyName && malwareFamily.name !== this.familyName) {
            return false;
        }
        return true;
    }
    getDescription() {
        if (!this.hasMalwareFamily) {
            return 'Threat has no malware family information';
        }
        if (this.familyName) {
            return `Threat is associated with malware family "${this.familyName}"`;
        }
        return 'Threat has malware family information';
    }
}
exports.ThreatMalwareFamilySpecification = ThreatMalwareFamilySpecification;
/**
 * Requires Immediate Attention Specification
 *
 * Specification for threats that require immediate attention.
 */
class RequiresImmediateAttentionSpecification extends ThreatSpecification {
    isSatisfiedBy(threat) {
        return threat.requiresImmediateAttention();
    }
    getDescription() {
        return 'Threat requires immediate attention (critical severity or high severity with high confidence)';
    }
}
exports.RequiresImmediateAttentionSpecification = RequiresImmediateAttentionSpecification;
/**
 * Affected Assets Specification
 *
 * Specification for threats affecting specific assets or asset counts.
 */
class AffectedAssetsSpecification extends ThreatSpecification {
    constructor(assetNames, minAssetCount, maxAssetCount) {
        super();
        this.assetNames = assetNames;
        this.minAssetCount = minAssetCount;
        this.maxAssetCount = maxAssetCount;
    }
    isSatisfiedBy(threat) {
        const affectedAssets = threat.affectedAssets;
        // Check asset count range
        if (this.minAssetCount !== undefined && affectedAssets.length < this.minAssetCount) {
            return false;
        }
        if (this.maxAssetCount !== undefined && affectedAssets.length > this.maxAssetCount) {
            return false;
        }
        // Check specific asset names
        if (this.assetNames && this.assetNames.length > 0) {
            return this.assetNames.some(assetName => affectedAssets.includes(assetName));
        }
        return true;
    }
    getDescription() {
        const conditions = [];
        if (this.minAssetCount !== undefined && this.maxAssetCount !== undefined) {
            conditions.push(`affects ${this.minAssetCount}-${this.maxAssetCount} assets`);
        }
        else if (this.minAssetCount !== undefined) {
            conditions.push(`affects at least ${this.minAssetCount} assets`);
        }
        else if (this.maxAssetCount !== undefined) {
            conditions.push(`affects at most ${this.maxAssetCount} assets`);
        }
        if (this.assetNames && this.assetNames.length > 0) {
            conditions.push(`affects assets: ${this.assetNames.join(', ')}`);
        }
        return conditions.length > 0
            ? `Threat ${conditions.join(' and ')}`
            : 'Threat affects any number of assets';
    }
}
exports.AffectedAssetsSpecification = AffectedAssetsSpecification;
/**
 * Indicators Count Specification
 *
 * Specification for threats with specific indicator counts.
 */
class IndicatorsCountSpecification extends ThreatSpecification {
    constructor(minCount, maxCount) {
        super();
        this.minCount = minCount;
        this.maxCount = maxCount;
    }
    isSatisfiedBy(threat) {
        const indicatorCount = threat.indicators.length;
        if (this.minCount !== undefined && indicatorCount < this.minCount) {
            return false;
        }
        if (this.maxCount !== undefined && indicatorCount > this.maxCount) {
            return false;
        }
        return true;
    }
    getDescription() {
        if (this.minCount !== undefined && this.maxCount !== undefined) {
            return `Threat has ${this.minCount}-${this.maxCount} indicators`;
        }
        else if (this.minCount !== undefined) {
            return `Threat has at least ${this.minCount} indicators`;
        }
        else if (this.maxCount !== undefined) {
            return `Threat has at most ${this.maxCount} indicators`;
        }
        return 'Threat has any number of indicators';
    }
}
exports.IndicatorsCountSpecification = IndicatorsCountSpecification;
/**
 * Composite Threat Specification Builder
 *
 * Builder for creating complex threat specifications using fluent interface.
 */
class ThreatSpecificationBuilder {
    constructor() {
        this.specifications = [];
    }
    /**
     * Add high severity filter
     */
    highSeverity() {
        this.specifications.push(new HighSeverityThreatSpecification());
        return this;
    }
    /**
     * Add critical severity filter
     */
    critical() {
        this.specifications.push(new CriticalThreatSpecification());
        return this;
    }
    /**
     * Add active status filter
     */
    active() {
        this.specifications.push(new ActiveThreatSpecification());
        return this;
    }
    /**
     * Add resolved status filter
     */
    resolved() {
        this.specifications.push(new ResolvedThreatSpecification());
        return this;
    }
    /**
     * Add high risk filter
     */
    highRisk(minRiskScore = 70) {
        this.specifications.push(new HighRiskThreatSpecification(minRiskScore));
        return this;
    }
    /**
     * Add high confidence filter
     */
    highConfidence(minConfidence = 80) {
        this.specifications.push(new HighConfidenceThreatSpecification(minConfidence));
        return this;
    }
    /**
     * Add recent threats filter
     */
    recent(withinHours = 24) {
        this.specifications.push(new RecentThreatSpecification(withinHours));
        return this;
    }
    /**
     * Add severity filter
     */
    withSeverities(...severities) {
        this.specifications.push(new ThreatSeveritySpecification(severities));
        return this;
    }
    /**
     * Add category filter
     */
    withCategories(...categories) {
        this.specifications.push(new ThreatCategorySpecification(categories));
        return this;
    }
    /**
     * Add type filter
     */
    withTypes(...types) {
        this.specifications.push(new ThreatTypeSpecification(types));
        return this;
    }
    /**
     * Add tag filter
     */
    withTags(tags, requireAll = false) {
        this.specifications.push(new ThreatTagSpecification(tags, requireAll));
        return this;
    }
    /**
     * Add risk score range filter
     */
    riskScoreRange(minScore, maxScore) {
        this.specifications.push(new ThreatRiskScoreRangeSpecification(minScore, maxScore));
        return this;
    }
    /**
     * Add confidence range filter
     */
    confidenceRange(minConfidence, maxConfidence) {
        this.specifications.push(new ThreatConfidenceRangeSpecification(minConfidence, maxConfidence));
        return this;
    }
    /**
     * Add requires immediate attention filter
     */
    requiresImmediateAttention() {
        this.specifications.push(new RequiresImmediateAttentionSpecification());
        return this;
    }
    /**
     * Add attribution filter
     */
    withAttribution(actor, campaign) {
        this.specifications.push(new ThreatAttributionSpecification(actor, campaign, true));
        return this;
    }
    /**
     * Add malware family filter
     */
    withMalwareFamily(familyName) {
        this.specifications.push(new ThreatMalwareFamilySpecification(familyName, true));
        return this;
    }
    /**
     * Add affected assets filter
     */
    affectingAssets(assetNames, minCount, maxCount) {
        this.specifications.push(new AffectedAssetsSpecification(assetNames, minCount, maxCount));
        return this;
    }
    /**
     * Add indicators count filter
     */
    withIndicatorCount(minCount, maxCount) {
        this.specifications.push(new IndicatorsCountSpecification(minCount, maxCount));
        return this;
    }
    /**
     * Build the final specification using AND logic
     */
    build() {
        if (this.specifications.length === 0) {
            throw new Error('At least one specification must be added');
        }
        if (this.specifications.length === 1) {
            return this.specifications[0];
        }
        // Combine all specifications with AND logic
        let combined = this.specifications[0];
        for (let i = 1; i < this.specifications.length; i++) {
            combined = combined.and(this.specifications[i]);
        }
        return combined;
    }
    /**
     * Build the final specification using OR logic
     */
    buildWithOr() {
        if (this.specifications.length === 0) {
            throw new Error('At least one specification must be added');
        }
        if (this.specifications.length === 1) {
            return this.specifications[0];
        }
        // Combine all specifications with OR logic
        let combined = this.specifications[0];
        for (let i = 1; i < this.specifications.length; i++) {
            combined = combined.or(this.specifications[i]);
        }
        return combined;
    }
    /**
     * Create a new builder instance
     */
    static create() {
        return new ThreatSpecificationBuilder();
    }
}
exports.ThreatSpecificationBuilder = ThreatSpecificationBuilder;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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