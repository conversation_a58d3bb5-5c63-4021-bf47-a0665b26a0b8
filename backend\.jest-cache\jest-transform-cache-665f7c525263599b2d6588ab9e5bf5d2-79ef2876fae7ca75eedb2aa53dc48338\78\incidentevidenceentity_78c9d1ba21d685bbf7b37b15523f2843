d0ff364a68573482c18f7affbe665c9b
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", { value: true });
exports.IncidentEvidence = void 0;
const typeorm_1 = require("typeorm");
const incident_entity_1 = require("./incident.entity");
/**
 * Incident Evidence entity
 * Represents digital evidence collected during incident response
 */
let IncidentEvidence = class IncidentEvidence {
    /**
     * Check if evidence integrity is verified
     */
    get isIntegrityVerified() {
        return this.qualityMetrics?.integrityVerified || false;
    }
    /**
     * Check if evidence is under legal hold
     */
    get isUnderLegalHold() {
        return this.legalInfo?.legalHold || false;
    }
    /**
     * Get evidence size in human-readable format
     */
    get fileSizeFormatted() {
        const size = this.metadata.fileSize;
        if (!size)
            return 'Unknown';
        const units = ['B', 'KB', 'MB', 'GB', 'TB'];
        let unitIndex = 0;
        let fileSize = size;
        while (fileSize >= 1024 && unitIndex < units.length - 1) {
            fileSize /= 1024;
            unitIndex++;
        }
        return `${fileSize.toFixed(1)} ${units[unitIndex]}`;
    }
    /**
     * Get evidence age in days
     */
    get ageInDays() {
        const now = new Date();
        const diffMs = now.getTime() - this.collectedAt.getTime();
        return Math.floor(diffMs / (1000 * 60 * 60 * 24));
    }
    /**
     * Add chain of custody entry
     */
    addChainOfCustodyEntry(action, userId, options = {}) {
        this.chainOfCustody.push({
            action,
            timestamp: new Date().toISOString(),
            userId,
            ...options,
        });
    }
    /**
     * Verify evidence integrity
     */
    verifyIntegrity(method = 'hash_comparison') {
        // This would implement actual integrity verification
        // For now, we'll simulate the process
        if (!this.qualityMetrics) {
            this.qualityMetrics = {
                integrityVerified: false,
                completeness: 1,
                accuracy: 1,
                reliability: 1,
            };
        }
        // Simulate integrity check
        const isValid = this.metadata.hashes?.sha256 !== undefined;
        this.qualityMetrics.integrityVerified = isValid;
        this.qualityMetrics.integrityMethod = method;
        this.qualityMetrics.integrityTimestamp = new Date().toISOString();
        if (!isValid && this.qualityMetrics.issues) {
            this.qualityMetrics.issues.push({
                type: 'hash_mismatch',
                description: 'Evidence integrity could not be verified',
                severity: 'high',
                impact: 'Evidence may be corrupted or tampered with',
            });
        }
        return isValid;
    }
    /**
     * Calculate evidence hash
     */
    calculateHash(algorithm = 'sha256') {
        // This would implement actual hash calculation
        // For now, we'll return a simulated hash
        const timestamp = Date.now().toString();
        const data = `${this.id}-${this.name}-${timestamp}`;
        // Simulate hash based on algorithm
        switch (algorithm) {
            case 'md5':
                return `md5-${data.slice(0, 32)}`;
            case 'sha1':
                return `sha1-${data.slice(0, 40)}`;
            case 'sha256':
                return `sha256-${data.slice(0, 64)}`;
            case 'sha512':
                return `sha512-${data.slice(0, 128)}`;
            default:
                return data;
        }
    }
    /**
     * Update evidence hashes
     */
    updateHashes() {
        if (!this.metadata.hashes) {
            this.metadata.hashes = {};
        }
        this.metadata.hashes.md5 = this.calculateHash('md5');
        this.metadata.hashes.sha1 = this.calculateHash('sha1');
        this.metadata.hashes.sha256 = this.calculateHash('sha256');
        this.metadata.hashes.sha512 = this.calculateHash('sha512');
    }
    /**
     * Add access log entry
     */
    addAccessLog(userId, action, purpose, approved = true, approvedBy) {
        if (!this.legalInfo) {
            this.legalInfo = {
                legalHold: false,
                privacyClassification: 'internal',
            };
        }
        if (!this.legalInfo.accessLog) {
            this.legalInfo.accessLog = [];
        }
        this.legalInfo.accessLog.push({
            userId,
            timestamp: new Date().toISOString(),
            action,
            purpose,
            approved,
            approvedBy,
        });
    }
    /**
     * Set legal hold
     */
    setLegalHold(reason, setBy) {
        if (!this.legalInfo) {
            this.legalInfo = {
                legalHold: false,
                privacyClassification: 'internal',
            };
        }
        this.legalInfo.legalHold = true;
        this.legalInfo.legalHoldReason = reason;
        this.legalInfo.legalHoldBy = setBy;
        this.legalInfo.legalHoldDate = new Date().toISOString();
        // Add chain of custody entry
        this.addChainOfCustodyEntry('transferred', setBy, {
            purpose: 'Legal hold applied',
            notes: reason,
        });
    }
    /**
     * Release legal hold
     */
    releaseLegalHold(releasedBy, reason) {
        if (this.legalInfo) {
            this.legalInfo.legalHold = false;
            // Add chain of custody entry
            this.addChainOfCustodyEntry('transferred', releasedBy, {
                purpose: 'Legal hold released',
                notes: reason || 'Legal hold no longer required',
            });
        }
    }
    /**
     * Archive evidence
     */
    archive(archivedBy, location) {
        this.status = 'archived';
        // Update storage info
        if (!this.storageInfo) {
            this.storageInfo = {
                storageType: 'local',
                storagePath: '/archive',
                encrypted: true,
            };
        }
        if (location) {
            this.storageInfo.storagePath = location;
        }
        // Add chain of custody entry
        this.addChainOfCustodyEntry('archived', archivedBy, {
            location: this.storageInfo.storagePath,
            purpose: 'Long-term storage',
        });
    }
    /**
     * Validate evidence completeness
     */
    validateCompleteness() {
        const issues = [];
        // Check required metadata
        if (!this.metadata.fileName) {
            issues.push('File name is missing');
        }
        if (!this.metadata.fileSize) {
            issues.push('File size is missing');
        }
        if (!this.metadata.hashes?.sha256) {
            issues.push('SHA256 hash is missing');
        }
        // Check chain of custody
        if (this.chainOfCustody.length === 0) {
            issues.push('Chain of custody is empty');
        }
        // Check collection details
        if (!this.metadata.collectionMethod) {
            issues.push('Collection method is not documented');
        }
        return {
            isComplete: issues.length === 0,
            issues,
        };
    }
};
exports.IncidentEvidence = IncidentEvidence;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], IncidentEvidence.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], IncidentEvidence.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], IncidentEvidence.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: [
            'log_file',
            'memory_dump',
            'disk_image',
            'network_capture',
            'screenshot',
            'document',
            'email',
            'database_export',
            'configuration_file',
            'registry_export',
            'malware_sample',
            'forensic_artifact',
            'other',
        ],
    }),
    __metadata("design:type", String)
], IncidentEvidence.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['collecting', 'collected', 'processing', 'analyzed', 'archived', 'corrupted'],
        default: 'collecting',
    }),
    __metadata("design:type", String)
], IncidentEvidence.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    __metadata("design:type", Object)
], IncidentEvidence.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'chain_of_custody', type: 'jsonb' }),
    __metadata("design:type", typeof (_a = typeof Array !== "undefined" && Array) === "function" ? _a : Object)
], IncidentEvidence.prototype, "chainOfCustody", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'storage_info', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], IncidentEvidence.prototype, "storageInfo", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'legal_info', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], IncidentEvidence.prototype, "legalInfo", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'quality_metrics', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], IncidentEvidence.prototype, "qualityMetrics", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'collected_at', type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], IncidentEvidence.prototype, "collectedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'collected_by', type: 'uuid' }),
    __metadata("design:type", String)
], IncidentEvidence.prototype, "collectedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, default: '{}' }),
    __metadata("design:type", Array)
], IncidentEvidence.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], IncidentEvidence.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], IncidentEvidence.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], IncidentEvidence.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => incident_entity_1.Incident, incident => incident.evidence, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'incident_id' }),
    __metadata("design:type", typeof (_e = typeof incident_entity_1.Incident !== "undefined" && incident_entity_1.Incident) === "function" ? _e : Object)
], IncidentEvidence.prototype, "incident", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'incident_id', type: 'uuid' }),
    __metadata("design:type", String)
], IncidentEvidence.prototype, "incidentId", void 0);
exports.IncidentEvidence = IncidentEvidence = __decorate([
    (0, typeorm_1.Entity)('incident_evidence'),
    (0, typeorm_1.Index)(['incidentId']),
    (0, typeorm_1.Index)(['type']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['collectedBy']),
    (0, typeorm_1.Index)(['collectedAt'])
], IncidentEvidence);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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