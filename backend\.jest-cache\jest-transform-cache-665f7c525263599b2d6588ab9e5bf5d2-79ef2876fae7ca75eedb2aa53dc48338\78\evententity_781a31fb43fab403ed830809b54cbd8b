4faf974796d3441e762b0373887992a6
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Event = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
const event_severity_enum_1 = require("../enums/event-severity.enum");
const event_status_enum_1 = require("../enums/event-status.enum");
const event_processing_status_enum_1 = require("../enums/event-processing-status.enum");
const event_created_domain_event_1 = require("../events/event-created.domain-event");
const event_status_changed_domain_event_1 = require("../events/event-status-changed.domain-event");
const event_processing_status_changed_domain_event_1 = require("../events/event-processing-status-changed.domain-event");
/**
 * Event Entity
 *
 * Represents a security event in the system. Events are the core domain objects
 * that capture security-related activities, incidents, and observations.
 *
 * Key responsibilities:
 * - Maintain event state and lifecycle
 * - Enforce business rules and invariants
 * - Generate domain events for state changes
 * - Provide event analysis and scoring capabilities
 * - Support event correlation and relationships
 *
 * Business Rules:
 * - Events must have valid metadata and type
 * - Status transitions must follow defined workflows
 * - Risk scores must be between 0-100
 * - Processing attempts are tracked and limited
 * - Resolution requires proper authorization
 */
class Event extends shared_kernel_1.BaseAggregateRoot {
    constructor(props, id) {
        super(props, id);
        this.validateInvariants();
    }
    /**
     * Create a new Event
     */
    static create(props, id) {
        const event = new Event(props, id);
        // Add domain event for event creation
        event.addDomainEvent(new event_created_domain_event_1.EventCreatedDomainEvent(event.id, {
            eventType: props.type,
            severity: props.severity,
            source: props.metadata.source,
            timestamp: props.metadata.timestamp,
            title: props.title,
            riskScore: props.riskScore,
        }));
        return event;
    }
    validateInvariants() {
        super.validateInvariants();
        if (!this.props.metadata) {
            throw new Error('Event must have metadata');
        }
        if (!this.props.type) {
            throw new Error('Event must have a type');
        }
        if (!this.props.severity) {
            throw new Error('Event must have a severity');
        }
        if (!this.props.status) {
            throw new Error('Event must have a status');
        }
        if (!this.props.processingStatus) {
            throw new Error('Event must have a processing status');
        }
        if (!this.props.rawData) {
            throw new Error('Event must have raw data');
        }
        if (!this.props.title || this.props.title.trim().length === 0) {
            throw new Error('Event must have a non-empty title');
        }
        if (this.props.title.length > Event.MAX_TITLE_LENGTH) {
            throw new Error(`Event title cannot exceed ${Event.MAX_TITLE_LENGTH} characters`);
        }
        if (this.props.description && this.props.description.length > Event.MAX_DESCRIPTION_LENGTH) {
            throw new Error(`Event description cannot exceed ${Event.MAX_DESCRIPTION_LENGTH} characters`);
        }
        if (this.props.riskScore !== undefined && (this.props.riskScore < 0 || this.props.riskScore > 100)) {
            throw new Error('Risk score must be between 0 and 100');
        }
        if (this.props.confidenceLevel !== undefined && (this.props.confidenceLevel < 0 || this.props.confidenceLevel > 100)) {
            throw new Error('Confidence level must be between 0 and 100');
        }
        if (this.props.tags && this.props.tags.length > Event.MAX_TAGS) {
            throw new Error(`Event cannot have more than ${Event.MAX_TAGS} tags`);
        }
        if (this.props.processingAttempts !== undefined && this.props.processingAttempts < 0) {
            throw new Error('Processing attempts cannot be negative');
        }
        // Validate status consistency
        this.validateStatusConsistency();
    }
    validateStatusConsistency() {
        // If event is resolved, it should have resolution information
        if (this.props.status === event_status_enum_1.EventStatus.RESOLVED) {
            if (!this.props.resolvedAt) {
                throw new Error('Resolved events must have a resolution timestamp');
            }
        }
        // If event has resolution info, it should be resolved or closed
        if (this.props.resolvedAt && ![event_status_enum_1.EventStatus.RESOLVED, event_status_enum_1.EventStatus.CLOSED].includes(this.props.status)) {
            throw new Error('Only resolved or closed events can have resolution information');
        }
        // Processing status should be consistent with event status
        if (this.props.status === event_status_enum_1.EventStatus.RESOLVED &&
            ![event_processing_status_enum_1.EventProcessingStatus.RESOLVED, event_processing_status_enum_1.EventProcessingStatus.ARCHIVED].includes(this.props.processingStatus)) {
            throw new Error('Resolved events must have resolved or archived processing status');
        }
    }
    // Getters
    get metadata() {
        return this.props.metadata;
    }
    get type() {
        return this.props.type;
    }
    get severity() {
        return this.props.severity;
    }
    get status() {
        return this.props.status;
    }
    get processingStatus() {
        return this.props.processingStatus;
    }
    get rawData() {
        return { ...this.props.rawData };
    }
    get normalizedData() {
        return this.props.normalizedData ? { ...this.props.normalizedData } : undefined;
    }
    get title() {
        return this.props.title;
    }
    get description() {
        return this.props.description;
    }
    get tags() {
        return this.props.tags ? [...this.props.tags] : [];
    }
    get riskScore() {
        return this.props.riskScore;
    }
    get confidenceLevel() {
        return this.props.confidenceLevel;
    }
    get attributes() {
        return this.props.attributes ? { ...this.props.attributes } : {};
    }
    get correlationId() {
        return this.props.correlationId;
    }
    get parentEventId() {
        return this.props.parentEventId;
    }
    get processingAttempts() {
        return this.props.processingAttempts || 0;
    }
    get lastProcessingError() {
        return this.props.lastProcessingError;
    }
    get lastProcessedAt() {
        return this.props.lastProcessedAt;
    }
    get resolvedAt() {
        return this.props.resolvedAt;
    }
    get resolvedBy() {
        return this.props.resolvedBy;
    }
    get resolutionNotes() {
        return this.props.resolutionNotes;
    }
    // Business methods
    /**
     * Change event status
     */
    changeStatus(newStatus, changedBy, notes) {
        if (this.props.status === newStatus) {
            return; // No change needed
        }
        const oldStatus = this.props.status;
        this.props.status = newStatus;
        // Handle resolution
        if (newStatus === event_status_enum_1.EventStatus.RESOLVED) {
            this.props.resolvedAt = new Date();
            this.props.resolvedBy = changedBy;
            this.props.resolutionNotes = notes;
        }
        else if (oldStatus === event_status_enum_1.EventStatus.RESOLVED) {
            // Reopening resolved event
            this.props.resolvedAt = undefined;
            this.props.resolvedBy = undefined;
            this.props.resolutionNotes = undefined;
        }
        this.addDomainEvent(new event_status_changed_domain_event_1.EventStatusChangedDomainEvent(this.id, {
            oldStatus,
            newStatus,
            changedBy,
            notes,
            timestamp: new Date(),
        }));
        this.validateInvariants();
    }
    /**
     * Change processing status
     */
    changeProcessingStatus(newStatus) {
        if (this.props.processingStatus === newStatus) {
            return; // No change needed
        }
        const oldStatus = this.props.processingStatus;
        this.props.processingStatus = newStatus;
        this.props.lastProcessedAt = new Date();
        this.addDomainEvent(new event_processing_status_changed_domain_event_1.EventProcessingStatusChangedDomainEvent(this.id, {
            oldStatus,
            newStatus,
            timestamp: new Date(),
        }));
        this.validateInvariants();
    }
    /**
     * Update normalized data
     */
    updateNormalizedData(normalizedData) {
        this.props.normalizedData = { ...normalizedData };
        if (this.props.processingStatus === event_processing_status_enum_1.EventProcessingStatus.RAW) {
            this.changeProcessingStatus(event_processing_status_enum_1.EventProcessingStatus.NORMALIZED);
        }
    }
    /**
     * Update risk score
     */
    updateRiskScore(riskScore) {
        if (riskScore < 0 || riskScore > 100) {
            throw new Error('Risk score must be between 0 and 100');
        }
        this.props.riskScore = riskScore;
    }
    /**
     * Update confidence level
     */
    updateConfidenceLevel(confidenceLevel) {
        if (confidenceLevel < 0 || confidenceLevel > 100) {
            throw new Error('Confidence level must be between 0 and 100');
        }
        this.props.confidenceLevel = confidenceLevel;
    }
    /**
     * Add tags
     */
    addTags(tags) {
        const currentTags = this.props.tags || [];
        const newTags = [...new Set([...currentTags, ...tags])]; // Remove duplicates
        if (newTags.length > Event.MAX_TAGS) {
            throw new Error(`Event cannot have more than ${Event.MAX_TAGS} tags`);
        }
        this.props.tags = newTags;
    }
    /**
     * Remove tags
     */
    removeTags(tags) {
        if (!this.props.tags) {
            return;
        }
        this.props.tags = this.props.tags.filter(tag => !tags.includes(tag));
    }
    /**
     * Set correlation ID
     */
    setCorrelationId(correlationId) {
        this.props.correlationId = correlationId;
    }
    /**
     * Set parent event
     */
    setParentEvent(parentEventId) {
        this.props.parentEventId = parentEventId;
    }
    /**
     * Record processing attempt
     */
    recordProcessingAttempt(error) {
        this.props.processingAttempts = (this.props.processingAttempts || 0) + 1;
        this.props.lastProcessedAt = new Date();
        if (error) {
            this.props.lastProcessingError = error;
        }
    }
    /**
     * Clear processing error
     */
    clearProcessingError() {
        this.props.lastProcessingError = undefined;
    }
    /**
     * Update attributes
     */
    updateAttributes(attributes) {
        this.props.attributes = { ...this.props.attributes, ...attributes };
    }
    /**
     * Remove attribute
     */
    removeAttribute(key) {
        if (this.props.attributes) {
            delete this.props.attributes[key];
        }
    }
    // Query methods
    /**
     * Check if event is active (not resolved or closed)
     */
    isActive() {
        return ![event_status_enum_1.EventStatus.RESOLVED, event_status_enum_1.EventStatus.CLOSED, event_status_enum_1.EventStatus.FALSE_POSITIVE].includes(this.props.status);
    }
    /**
     * Check if event is resolved
     */
    isResolved() {
        return this.props.status === event_status_enum_1.EventStatus.RESOLVED;
    }
    /**
     * Check if event is high severity
     */
    isHighSeverity() {
        return [event_severity_enum_1.EventSeverity.HIGH, event_severity_enum_1.EventSeverity.CRITICAL].includes(this.props.severity);
    }
    /**
     * Check if event is critical
     */
    isCritical() {
        return this.props.severity === event_severity_enum_1.EventSeverity.CRITICAL;
    }
    /**
     * Check if event has high risk score
     */
    isHighRisk() {
        return (this.props.riskScore || 0) >= 70;
    }
    /**
     * Check if event processing has failed
     */
    hasProcessingFailed() {
        return this.props.processingStatus === event_processing_status_enum_1.EventProcessingStatus.FAILED;
    }
    /**
     * Check if event has exceeded max processing attempts
     */
    hasExceededMaxAttempts() {
        return this.processingAttempts >= Event.MAX_PROCESSING_ATTEMPTS;
    }
    /**
     * Check if event is recent
     */
    isRecent(withinMs = 3600000) {
        return this.metadata.timestamp.getAge() <= withinMs;
    }
    /**
     * Check if event is stale
     */
    isStale(olderThanMs = 86400000) {
        return this.metadata.timestamp.getAge() > olderThanMs;
    }
    /**
     * Check if event has specific tag
     */
    hasTag(tag) {
        return this.tags.includes(tag);
    }
    /**
     * Check if event has any of the specified tags
     */
    hasAnyTag(tags) {
        return tags.some(tag => this.hasTag(tag));
    }
    /**
     * Check if event has all of the specified tags
     */
    hasAllTags(tags) {
        return tags.every(tag => this.hasTag(tag));
    }
    /**
     * Get event age in milliseconds
     */
    getAge() {
        return this.metadata.timestamp.getAge();
    }
    /**
     * Get processing delay
     */
    getProcessingDelay() {
        return this.metadata.timestamp.getProcessingDelay();
    }
    /**
     * Get event summary for display
     */
    getSummary() {
        return {
            id: this.id.toString(),
            title: this.props.title,
            type: this.props.type,
            severity: this.props.severity,
            status: this.props.status,
            processingStatus: this.props.processingStatus,
            riskScore: this.props.riskScore,
            age: this.getAge(),
            source: this.metadata.source.toString(),
            isActive: this.isActive(),
            isHighRisk: this.isHighRisk(),
        };
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            ...super.toJSON(),
            metadata: this.props.metadata.toJSON(),
            type: this.props.type,
            severity: this.props.severity,
            status: this.props.status,
            processingStatus: this.props.processingStatus,
            rawData: this.props.rawData,
            normalizedData: this.props.normalizedData,
            title: this.props.title,
            description: this.props.description,
            tags: this.props.tags,
            riskScore: this.props.riskScore,
            confidenceLevel: this.props.confidenceLevel,
            attributes: this.props.attributes,
            correlationId: this.props.correlationId,
            parentEventId: this.props.parentEventId?.toString(),
            processingAttempts: this.props.processingAttempts,
            lastProcessingError: this.props.lastProcessingError,
            lastProcessedAt: this.props.lastProcessedAt?.toISOString(),
            resolvedAt: this.props.resolvedAt?.toISOString(),
            resolvedBy: this.props.resolvedBy,
            resolutionNotes: this.props.resolutionNotes,
            summary: this.getSummary(),
        };
    }
}
exports.Event = Event;
Event.MAX_PROCESSING_ATTEMPTS = 5;
Event.MAX_TITLE_LENGTH = 200;
Event.MAX_DESCRIPTION_LENGTH = 2000;
Event.MAX_TAGS = 20;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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