b22c9016e772e258a2bec4115898d078
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CorrelatedEvent = exports.CorrelationType = void 0;
const base_aggregate_root_1 = require("../../../../shared-kernel/domain/base-aggregate-root");
const confidence_level_enum_1 = require("../../enums/confidence-level.enum");
const events_correlated_event_1 = require("../../events/events-correlated.event");
/**
 * Correlation Type
 */
var CorrelationType;
(function (CorrelationType) {
    CorrelationType["TEMPORAL"] = "temporal";
    CorrelationType["SPATIAL"] = "spatial";
    CorrelationType["BEHAVIORAL"] = "behavioral";
    CorrelationType["INDICATOR"] = "indicator";
    CorrelationType["CAMPAIGN"] = "campaign";
    CorrelationType["ATTACK_CHAIN"] = "attack_chain";
    CorrelationType["HYBRID"] = "hybrid";
})(CorrelationType || (exports.CorrelationType = CorrelationType = {}));
/**
 * Correlated Event Entity
 *
 * Represents a group of enriched events that have been correlated based on
 * temporal, spatial, behavioral, or indicator-based relationships.
 *
 * Key responsibilities:
 * - Store correlation analysis results
 * - Track correlation confidence and scoring
 * - Provide correlation pattern insights
 * - Support attack chain reconstruction
 * - Enable campaign attribution
 *
 * Business Rules:
 * - Must reference at least two enriched events
 * - Correlation score must reflect analysis quality
 * - High-confidence correlations trigger threat escalation
 * - Attack chain analysis requires temporal ordering
 */
class CorrelatedEvent extends base_aggregate_root_1.BaseAggregateRoot {
    constructor(props, id) {
        super(props, id);
    }
    validate() {
        if (!this.props.primaryEventId) {
            throw new Error('Correlated event must have a primary event');
        }
        if (!this.props.relatedEventIds || this.props.relatedEventIds.length === 0) {
            throw new Error('Correlated event must have related events');
        }
        const totalEvents = 1 + this.props.relatedEventIds.length;
        if (totalEvents < CorrelatedEvent.MIN_EVENTS_FOR_CORRELATION) {
            throw new Error(`Correlation requires at least ${CorrelatedEvent.MIN_EVENTS_FOR_CORRELATION} events`);
        }
        if (!this.props.correlationAnalysis) {
            throw new Error('Correlated event must have correlation analysis');
        }
        if (!this.props.correlationMetadata) {
            throw new Error('Correlated event must have correlation metadata');
        }
        if (!Object.values(confidence_level_enum_1.ConfidenceLevel).includes(this.props.confidence)) {
            throw new Error(`Invalid confidence level: ${this.props.confidence}`);
        }
        if (this.props.score < CorrelatedEvent.MIN_CORRELATION_SCORE ||
            this.props.score > CorrelatedEvent.MAX_CORRELATION_SCORE) {
            throw new Error(`Correlation score must be between ${CorrelatedEvent.MIN_CORRELATION_SCORE} and ${CorrelatedEvent.MAX_CORRELATION_SCORE}`);
        }
        // Validate correlation type
        if (!Object.values(CorrelationType).includes(this.props.correlationAnalysis.type)) {
            throw new Error(`Invalid correlation type: ${this.props.correlationAnalysis.type}`);
        }
    }
    /**
     * Create a correlated event from enriched events
     */
    static create(primaryEvent, relatedEvents, correlationAnalysis, correlationMetadata, options) {
        const confidence = CorrelatedEvent.calculateConfidence(correlationAnalysis);
        const score = CorrelatedEvent.calculateScore(correlationAnalysis, correlationMetadata);
        const props = {
            primaryEventId: primaryEvent.id,
            relatedEventIds: relatedEvents.map(event => event.id),
            correlationAnalysis,
            correlationMetadata,
            confidence,
            score,
            errors: options?.errors || [],
        };
        const correlatedEvent = new CorrelatedEvent(props);
        // Publish domain event
        correlatedEvent.addDomainEvent(new events_correlated_event_1.EventsCorrelatedEvent(correlatedEvent.id, {
            correlationId: correlatedEvent.id.toString(),
            primaryEventId: primaryEvent.id.toString(),
            relatedEventIds: relatedEvents.map(e => e.id.toString()),
            correlationType: correlationAnalysis.type,
            confidence: confidence,
            score,
            eventCount: 1 + relatedEvents.length,
            commonIndicatorCount: correlationAnalysis.commonIndicators.length,
            hasAttackChain: !!correlationAnalysis.attackChain,
            hasCampaignAttribution: !!correlationAnalysis.campaignCorrelation?.campaignId,
            timestamp: new Date().toISOString(),
        }));
        return correlatedEvent;
    }
    static calculateConfidence(analysis) {
        let confidenceScore = 0;
        // Base confidence from patterns
        const avgPatternConfidence = analysis.patterns.length > 0
            ? analysis.patterns.reduce((sum, p) => sum + p.confidence, 0) / analysis.patterns.length
            : 0;
        confidenceScore += avgPatternConfidence * 0.4;
        // Confidence from common indicators
        confidenceScore += Math.min(analysis.commonIndicators.length * 10, 30);
        // Confidence from correlation type
        const typeConfidence = {
            [CorrelationType.INDICATOR]: 20,
            [CorrelationType.TEMPORAL]: 15,
            [CorrelationType.SPATIAL]: 15,
            [CorrelationType.BEHAVIORAL]: 10,
            [CorrelationType.CAMPAIGN]: 25,
            [CorrelationType.ATTACK_CHAIN]: 30,
            [CorrelationType.HYBRID]: 20,
        };
        confidenceScore += typeConfidence[analysis.type] || 10;
        // Bonus for attack chain or campaign correlation
        if (analysis.attackChain) {
            confidenceScore += analysis.attackChain.confidence * 0.2;
        }
        if (analysis.campaignCorrelation) {
            confidenceScore += analysis.campaignCorrelation.campaignConfidence * 0.2;
        }
        return confidence_level_enum_1.ConfidenceLevel.fromNumericValue(Math.min(100, confidenceScore));
    }
    static calculateScore(analysis, metadata) {
        let score = 0;
        // Base score from pattern strength
        const avgPatternStrength = analysis.patterns.length > 0
            ? analysis.patterns.reduce((sum, p) => sum + p.strength, 0) / analysis.patterns.length
            : 0;
        score += avgPatternStrength * 40;
        // Score from common indicators
        score += Math.min(analysis.commonIndicators.length * 5, 25);
        // Score from correlation completeness
        if (analysis.attackChain) {
            score += analysis.attackChain.completeness * 0.2;
        }
        // Score from temporal regularity
        if (analysis.temporalCorrelation) {
            score += analysis.temporalCorrelation.regularity * 0.15;
        }
        // Score from spatial proximity
        if (analysis.spatialCorrelation) {
            const avgProximity = (analysis.spatialCorrelation.networkProximity +
                analysis.spatialCorrelation.geographicProximity) / 2;
            score += avgProximity * 0.1;
        }
        // Penalty for errors
        const errorPenalty = metadata.performanceMetrics.falsePositives * 2;
        score = Math.max(0, score - errorPenalty);
        return Math.min(100, Math.round(score));
    }
    /**
     * Get primary event ID
     */
    get primaryEventId() {
        return this.props.primaryEventId;
    }
    /**
     * Get related event IDs
     */
    get relatedEventIds() {
        return [...this.props.relatedEventIds];
    }
    /**
     * Get all event IDs (primary + related)
     */
    get allEventIds() {
        return [this.props.primaryEventId, ...this.props.relatedEventIds];
    }
    /**
     * Get correlation analysis
     */
    get correlationAnalysis() {
        return this.props.correlationAnalysis;
    }
    /**
     * Get correlation metadata
     */
    get correlationMetadata() {
        return this.props.correlationMetadata;
    }
    /**
     * Get confidence level
     */
    get confidence() {
        return this.props.confidence;
    }
    /**
     * Get correlation score
     */
    get score() {
        return this.props.score;
    }
    /**
     * Get correlation errors
     */
    get errors() {
        return [...this.props.errors];
    }
    /**
     * Get correlation type
     */
    get correlationType() {
        return this.props.correlationAnalysis.type;
    }
    /**
     * Get event count
     */
    get eventCount() {
        return 1 + this.props.relatedEventIds.length;
    }
    /**
     * Check if correlation is high confidence
     */
    isHighConfidence() {
        return this.props.score >= CorrelatedEvent.HIGH_CONFIDENCE_THRESHOLD;
    }
    /**
     * Check if correlation has attack chain
     */
    hasAttackChain() {
        return !!this.props.correlationAnalysis.attackChain;
    }
    /**
     * Check if correlation has campaign attribution
     */
    hasCampaignAttribution() {
        return !!this.props.correlationAnalysis.campaignCorrelation?.campaignId;
    }
    /**
     * Check if correlation has common indicators
     */
    hasCommonIndicators() {
        return this.props.correlationAnalysis.commonIndicators.length > 0;
    }
    /**
     * Check if correlation spans multiple assets
     */
    spansMultipleAssets() {
        return this.props.correlationAnalysis.spatialCorrelation?.assetRelationships.length > 0;
    }
    /**
     * Check if correlation indicates coordinated attack
     */
    indicatesCoordinatedAttack() {
        return this.hasAttackChain() ||
            this.hasCampaignAttribution() ||
            (this.correlationType === CorrelationType.TEMPORAL && this.isHighConfidence());
    }
    /**
     * Get threat escalation priority
     */
    getThreatEscalationPriority() {
        if (this.indicatesCoordinatedAttack() && this.isHighConfidence()) {
            return 'critical';
        }
        if (this.hasAttackChain() || this.hasCampaignAttribution()) {
            return 'high';
        }
        if (this.isHighConfidence() && this.hasCommonIndicators()) {
            return 'medium';
        }
        return 'low';
    }
    /**
     * Get investigation recommendations
     */
    getInvestigationRecommendations() {
        const recommendations = [];
        if (this.hasAttackChain()) {
            recommendations.push('Analyze complete attack chain progression');
            recommendations.push('Identify missing attack phases');
            recommendations.push('Assess potential future attack steps');
        }
        if (this.hasCampaignAttribution()) {
            recommendations.push('Research threat actor TTPs');
            recommendations.push('Check for additional campaign indicators');
            recommendations.push('Review historical campaign activities');
        }
        if (this.hasCommonIndicators()) {
            recommendations.push('Investigate all systems with common indicators');
            recommendations.push('Expand IOC hunting across environment');
        }
        if (this.spansMultipleAssets()) {
            recommendations.push('Assess lateral movement paths');
            recommendations.push('Review network segmentation effectiveness');
        }
        if (this.correlationType === CorrelationType.TEMPORAL) {
            recommendations.push('Analyze event timing patterns');
            recommendations.push('Look for additional events in time window');
        }
        return recommendations;
    }
    /**
     * Get correlation summary
     */
    getCorrelationSummary() {
        return {
            type: this.correlationType,
            eventCount: this.eventCount,
            confidence: this.confidence,
            score: this.score,
            hasAttackChain: this.hasAttackChain(),
            hasCampaignAttribution: this.hasCampaignAttribution(),
            commonIndicatorCount: this.props.correlationAnalysis.commonIndicators.length,
            threatPriority: this.getThreatEscalationPriority(),
        };
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            id: this.id.toString(),
            primaryEventId: this.props.primaryEventId.toString(),
            relatedEventIds: this.props.relatedEventIds.map(id => id.toString()),
            correlationAnalysis: this.props.correlationAnalysis,
            correlationMetadata: this.props.correlationMetadata,
            confidence: this.props.confidence,
            score: this.props.score,
            errors: this.props.errors,
            analysis: {
                isHighConfidence: this.isHighConfidence(),
                hasAttackChain: this.hasAttackChain(),
                hasCampaignAttribution: this.hasCampaignAttribution(),
                hasCommonIndicators: this.hasCommonIndicators(),
                spansMultipleAssets: this.spansMultipleAssets(),
                indicatesCoordinatedAttack: this.indicatesCoordinatedAttack(),
                threatEscalationPriority: this.getThreatEscalationPriority(),
                investigationRecommendations: this.getInvestigationRecommendations(),
                correlationSummary: this.getCorrelationSummary(),
            },
            createdAt: this.createdAt?.toISOString(),
            updatedAt: this.updatedAt?.toISOString(),
        };
    }
}
exports.CorrelatedEvent = CorrelatedEvent;
CorrelatedEvent.MIN_EVENTS_FOR_CORRELATION = 2;
CorrelatedEvent.MIN_CORRELATION_SCORE = 0;
CorrelatedEvent.MAX_CORRELATION_SCORE = 100;
CorrelatedEvent.HIGH_CONFIDENCE_THRESHOLD = 80;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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