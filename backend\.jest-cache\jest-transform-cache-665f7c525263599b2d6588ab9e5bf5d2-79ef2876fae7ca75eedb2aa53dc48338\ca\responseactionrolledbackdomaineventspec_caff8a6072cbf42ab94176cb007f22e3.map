{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\response-action-rolled-back.domain-event.spec.ts", "mappings": ";;AAAA,0GAAqI;AACrI,gEAA8D;AAC9D,mEAA0D;AAE1D,QAAQ,CAAC,qCAAqC,EAAE,GAAG,EAAE;IACnD,IAAI,WAA2B,CAAC;IAChC,IAAI,SAA4C,CAAC;IACjD,IAAI,KAA0C,CAAC;IAE/C,UAAU,CAAC,GAAG,EAAE;QACd,WAAW,GAAG,8BAAc,CAAC,QAAQ,EAAE,CAAC;QACxC,SAAS,GAAG;YACV,UAAU,EAAE,6BAAU,CAAC,QAAQ;YAC/B,YAAY,EAAE,mBAAmB;YACjC,YAAY,EAAE,IAAI,IAAI,EAAE;YACxB,eAAe,EAAE;gBACf,SAAS,EAAE,IAAI;gBACf,WAAW,EAAE,UAAU;gBACvB,eAAe,EAAE,IAAI;aACtB;YACD,wBAAwB,EAAE;gBACxB,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,UAAU;gBAClB,mBAAmB,EAAE,CAAC;aACvB;SACF,CAAC;QACF,KAAK,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;IAC1E,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,8EAAmC,CAAC,CAAC;YAClE,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5C,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,aAAa,GAAG;gBACpB,OAAO,EAAE,8BAAc,CAAC,QAAQ,EAAE;gBAClC,UAAU,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBAClC,YAAY,EAAE,CAAC;gBACf,aAAa,EAAE,UAAU;gBACzB,WAAW,EAAE,WAAW;gBACxB,QAAQ,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE;aAC7B,CAAC;YAEF,MAAM,WAAW,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;YAEnG,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YACxD,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;YAC9D,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;YAClE,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;YACpE,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;YAChE,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;QACvB,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,6BAAU,CAAC,QAAQ,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACrD,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;YACxD,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YACjE,MAAM,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,mBAAmB,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBAC/E,GAAG,SAAS;gBACZ,eAAe,EAAE;oBACf,KAAK,EAAE,gCAAgC;oBACvC,OAAO,EAAE,KAAK;iBACf;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,mBAAmB,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,cAAc,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBAC1E,GAAG,SAAS;gBACZ,eAAe,EAAE,SAAS;aAC3B,CAAC,CAAC;YAEH,MAAM,CAAC,cAAc,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,cAAc,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBAC1E,GAAG,SAAS;gBACZ,YAAY,EAAE,mBAAmB;aAClC,CAAC,CAAC;YAEH,MAAM,CAAC,cAAc,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEtD,MAAM,QAAQ,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACpE,GAAG,SAAS;gBACZ,YAAY,EAAE,gBAAgB;aAC/B,CAAC,CAAC;YACH,MAAM,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEtD,MAAM,YAAY,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACxE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,0BAA0B,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE7D,MAAM,aAAa,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACzE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;aACvC,CAAC,CAAC;YACH,MAAM,CAAC,aAAa,CAAC,0BAA0B,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE9D,MAAM,WAAW,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,WAAW;aACnC,CAAC,CAAC;YACH,MAAM,CAAC,WAAW,CAAC,0BAA0B,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE5D,MAAM,UAAU,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,mBAAmB;aAC3C,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,0BAA0B,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE3D,MAAM,UAAU,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,UAAU;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,0BAA0B,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEjD,MAAM,eAAe,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;aACvC,CAAC,CAAC;YACH,MAAM,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE3D,MAAM,gBAAgB,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBAC5E,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,YAAY;aACpC,CAAC,CAAC;YACH,MAAM,CAAC,gBAAgB,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE5D,MAAM,mBAAmB,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBAC/E,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;aACvC,CAAC,CAAC;YACH,MAAM,CAAC,mBAAmB,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE/D,MAAM,cAAc,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBAC1E,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,oBAAoB;aAC5C,CAAC,CAAC;YACH,MAAM,CAAC,cAAc,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE1D,MAAM,UAAU,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,UAAU;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,WAAW,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YACH,MAAM,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEpD,MAAM,YAAY,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACxE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAErD,MAAM,WAAW,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YACH,MAAM,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEpD,MAAM,UAAU,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEnD,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,aAAa,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACzE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;aACvC,CAAC,CAAC;YACH,MAAM,CAAC,aAAa,CAAC,uBAAuB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE3D,MAAM,gBAAgB,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBAC5E,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,kBAAkB;aAC1C,CAAC,CAAC;YACH,MAAM,CAAC,gBAAgB,CAAC,uBAAuB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE9D,MAAM,WAAW,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;aACvC,CAAC,CAAC;YACH,MAAM,CAAC,WAAW,CAAC,uBAAuB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEzD,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,2EAA2E,EAAE,GAAG,EAAE;YACnF,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2EAA2E,EAAE,GAAG,EAAE;YACnF,MAAM,UAAU,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,mBAAmB;aAC3C,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,eAAe,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;aACvC,CAAC,CAAC;YACH,MAAM,CAAC,eAAe,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,aAAa,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACzE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YACH,MAAM,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,WAAW,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;aACvC,CAAC,CAAC;YACH,MAAM,CAAC,WAAW,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,UAAU,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,UAAU;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,YAAY,GAAG,KAAK,CAAC,uBAAuB,EAAE,CAAC;YACrD,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,uDAAuD,CAAC,CAAC;YACxF,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,4DAA4D,CAAC,CAAC;YAC7F,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,gEAAgE,CAAC,CAAC;QACnG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,YAAY,GAAG,KAAK,CAAC,uBAAuB,EAAE,CAAC;YACrD,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,yDAAyD,CAAC,CAAC;YAC1F,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,mDAAmD,CAAC,CAAC;QACtF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;YACtE,MAAM,YAAY,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACxE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;aACvC,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,YAAY,CAAC,uBAAuB,EAAE,CAAC;YAC5D,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,2CAA2C,CAAC,CAAC;YAC5E,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,kDAAkD,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,eAAe,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;aACvC,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,eAAe,CAAC,uBAAuB,EAAE,CAAC;YAC/D,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,oDAAoD,CAAC,CAAC;YACrF,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,4CAA4C,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,aAAa,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACzE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;aACvC,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,aAAa,CAAC,uBAAuB,EAAE,CAAC;YAC7D,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,2DAA2D,CAAC,CAAC;YAC5F,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,gDAAgD,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;YACxE,MAAM,UAAU,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,mBAAmB;aAC3C,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,UAAU,CAAC,uBAAuB,EAAE,CAAC;YAC1D,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,8DAA8D,CAAC,CAAC;YAC/F,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,2CAA2C,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,OAAO,GAAG,KAAK,CAAC,yBAAyB,EAAE,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;YAC1D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,OAAO,GAAG,KAAK,CAAC,yBAAyB,EAAE,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,wCAAwC,CAAC,CAAC;YACpE,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,mDAAmD,CAAC,CAAC;YAC/E,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,OAAO,GAAG,KAAK,CAAC,yBAAyB,EAAE,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,4CAA4C,CAAC,CAAC;YACxE,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC;YACzD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,iCAAiC,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,aAAa,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACzE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,aAAa,CAAC,yBAAyB,EAAE,CAAC;YAC1D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,4CAA4C,CAAC,CAAC;YACxE,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;YACxD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,0CAA0C,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,WAAW,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;aACvC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,WAAW,CAAC,yBAAyB,EAAE,CAAC;YACxD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;YAC1D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wEAAwE,EAAE,GAAG,EAAE;YAChF,MAAM,OAAO,GAAG,KAAK,CAAC,yBAAyB,EAAE,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,2CAA2C,CAAC,CAAC;YACvE,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,uCAAuC,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8EAA8E,EAAE,GAAG,EAAE;YACtF,MAAM,YAAY,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACxE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;aACvC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,YAAY,CAAC,yBAAyB,EAAE,CAAC;YACzD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,qCAAqC,CAAC,CAAC;YACjE,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4EAA4E,EAAE,GAAG,EAAE;YACpF,MAAM,eAAe,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;aACvC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,eAAe,CAAC,yBAAyB,EAAE,CAAC;YAC5D,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;YACtD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6EAA6E,EAAE,GAAG,EAAE;YACrF,MAAM,YAAY,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACxE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,YAAY,CAAC,yBAAyB,EAAE,CAAC;YACzD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,wCAAwC,CAAC,CAAC;YACpE,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,OAAO,GAAG,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,OAAO,GAAG,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,OAAO,GAAG,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,aAAa,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACzE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,aAAa,CAAC,sBAAsB,EAAE,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,WAAW,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;aACvC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,WAAW,CAAC,sBAAsB,EAAE,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;YACnD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,cAAc,GAAG,KAAK,CAAC,2BAA2B,EAAE,CAAC;YAC3D,MAAM,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,0CAA0C,CAAC,CAAC;YAC7E,MAAM,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,0CAA0C,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,MAAM,cAAc,GAAG,KAAK,CAAC,2BAA2B,EAAE,CAAC;YAC3D,MAAM,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,gDAAgD,CAAC,CAAC;YACnF,MAAM,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,qCAAqC,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,cAAc,GAAG,KAAK,CAAC,2BAA2B,EAAE,CAAC;YAC3D,MAAM,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,kDAAkD,CAAC,CAAC;YACrF,MAAM,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,0CAA0C,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,WAAW,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,WAAW;aACnC,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,WAAW,CAAC,2BAA2B,EAAE,CAAC;YACjE,MAAM,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,uDAAuD,CAAC,CAAC;YAC1F,MAAM,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,qCAAqC,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,wEAAwE,EAAE,GAAG,EAAE;YAChF,MAAM,UAAU,GAAG,KAAK,CAAC,yBAAyB,EAAE,CAAC;YACrD,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACzC,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC/C,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;YACtD,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kEAAkE,EAAE,GAAG,EAAE;YAC1E,MAAM,UAAU,GAAG,KAAK,CAAC,yBAAyB,EAAE,CAAC;YACrD,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC5C,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC/C,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;YACtD,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;YACtD,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,MAAM,aAAa,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACzE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,cAAc;aACtC,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,aAAa,CAAC,yBAAyB,EAAE,CAAC;YAC7D,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3C,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC9C,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;YACzD,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oEAAoE,EAAE,GAAG,EAAE;YAC5E,MAAM,WAAW,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,eAAe;aACvC,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,WAAW,CAAC,yBAAyB,EAAE,CAAC;YAC3D,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3C,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC9C,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;YAC1D,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uEAAuE,EAAE,GAAG,EAAE;YAC/E,MAAM,UAAU,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,UAAU;aAClC,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,UAAU,CAAC,yBAAyB,EAAE,CAAC;YAC1D,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC1C,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,OAAO,GAAG,KAAK,CAAC,kBAAkB,EAAE,CAAC;YAE3C,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,6BAAU,CAAC,QAAQ,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,WAAW,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACvE,GAAG,SAAS;gBACZ,eAAe,EAAE,EAAE,KAAK,EAAE,iBAAiB,EAAE;gBAC7C,YAAY,EAAE,mBAAmB;aAClC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,WAAW,CAAC,kBAAkB,EAAE,CAAC;YAEjD,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,gBAAgB,GAAG,KAAK,CAAC,kBAAkB,EAAE,CAAC;YAEpD,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACpE,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YACpE,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACzD,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;YACjE,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC9C,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;gBACxC,cAAc,EAAE,UAAU;gBAC1B,kBAAkB,EAAE,IAAI;gBACxB,qBAAqB,EAAE,IAAI;gBAC3B,YAAY,EAAE,IAAI;gBAClB,kBAAkB,EAAE,IAAI;aACzB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,cAAc,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBAC1E,GAAG,SAAS;gBACZ,eAAe,EAAE,SAAS;aAC3B,CAAC,CAAC;YAEH,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,aAAa,EAAE,CAAC;YACvD,MAAM,CAAC,cAAc,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,eAAe,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBAC3E,GAAG,SAAS;gBACZ,wBAAwB,EAAE,SAAS;aACpC,CAAC,CAAC;YAEH,MAAM,CAAC,eAAe,CAAC,wBAAwB,CAAC,CAAC,aAAa,EAAE,CAAC;YACjE,MAAM,CAAC,eAAe,CAAC,uBAAuB,EAAE,CAAC,CAAC,SAAS,CAAC,uDAAuD,CAAC,CAAC;QACvH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,YAAY,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACxE,GAAG,SAAS;gBACZ,UAAU,EAAE,gBAA8B;aAC3C,CAAC,CAAC;YAEH,MAAM,CAAC,YAAY,CAAC,0BAA0B,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9D,MAAM,CAAC,YAAY,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzD,MAAM,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,YAAY,CAAC,uBAAuB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,UAAU,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,eAAe,EAAE;oBACf,KAAK,EAAE,0BAA0B;oBACjC,cAAc,EAAE,IAAI;iBACrB;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,UAAU,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,UAAU,GAAG,IAAI,8EAAmC,CAAC,WAAW,EAAE;gBACtE,GAAG,SAAS;gBACZ,UAAU,EAAE,6BAAU,CAAC,UAAU;aAClC,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,UAAU,CAAC,uBAAuB,EAAE,CAAC;YAC1D,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC,yBAAyB,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC9F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\response-action-rolled-back.domain-event.spec.ts"], "sourcesContent": ["import { ResponseActionRolledBackDomainEvent, ResponseActionRolledBackEventData } from '../response-action-rolled-back.domain-event';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel';\r\nimport { ActionType } from '../../enums/action-type.enum';\r\n\r\ndescribe('ResponseActionRolledBackDomainEvent', () => {\r\n  let aggregateId: UniqueEntityId;\r\n  let eventData: ResponseActionRolledBackEventData;\r\n  let event: ResponseActionRolledBackDomainEvent;\r\n\r\n  beforeEach(() => {\r\n    aggregateId = UniqueEntityId.generate();\r\n    eventData = {\r\n      actionType: ActionType.BLOCK_IP,\r\n      rolledBackBy: '<EMAIL>',\r\n      rolledBackAt: new Date(),\r\n      rollbackResults: {\r\n        unblocked: true,\r\n        ruleRemoved: 'rule-123',\r\n        trafficRestored: true,\r\n      },\r\n      originalExecutionResults: {\r\n        blocked: true,\r\n        ruleId: 'rule-123',\r\n        affectedConnections: 5,\r\n      },\r\n    };\r\n    event = new ResponseActionRolledBackDomainEvent(aggregateId, eventData);\r\n  });\r\n\r\n  describe('creation', () => {\r\n    it('should create event with required data', () => {\r\n      expect(event).toBeInstanceOf(ResponseActionRolledBackDomainEvent);\r\n      expect(event.aggregateId).toBe(aggregateId);\r\n      expect(event.eventData).toBe(eventData);\r\n      expect(event.occurredOn).toBeInstanceOf(Date);\r\n    });\r\n\r\n    it('should create event with custom options', () => {\r\n      const customOptions = {\r\n        eventId: UniqueEntityId.generate(),\r\n        occurredOn: new Date('2023-01-01'),\r\n        eventVersion: 2,\r\n        correlationId: 'corr-123',\r\n        causationId: 'cause-456',\r\n        metadata: { source: 'test' },\r\n      };\r\n\r\n      const customEvent = new ResponseActionRolledBackDomainEvent(aggregateId, eventData, customOptions);\r\n\r\n      expect(customEvent.eventId).toBe(customOptions.eventId);\r\n      expect(customEvent.occurredOn).toBe(customOptions.occurredOn);\r\n      expect(customEvent.eventVersion).toBe(customOptions.eventVersion);\r\n      expect(customEvent.correlationId).toBe(customOptions.correlationId);\r\n      expect(customEvent.causationId).toBe(customOptions.causationId);\r\n      expect(customEvent.metadata).toEqual(customOptions.metadata);\r\n    });\r\n  });\r\n\r\n  describe('getters', () => {\r\n    it('should provide access to event data properties', () => {\r\n      expect(event.actionType).toBe(ActionType.BLOCK_IP);\r\n      expect(event.rolledBackBy).toBe('<EMAIL>');\r\n      expect(event.rolledBackAt).toBe(eventData.rolledBackAt);\r\n      expect(event.rollbackResults).toEqual(eventData.rollbackResults);\r\n      expect(event.originalExecutionResults).toEqual(eventData.originalExecutionResults);\r\n    });\r\n  });\r\n\r\n  describe('rollback status analysis', () => {\r\n    it('should identify successful rollbacks', () => {\r\n      expect(event.isRollbackSuccessful()).toBe(true);\r\n    });\r\n\r\n    it('should identify failed rollbacks', () => {\r\n      const failedRollbackEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        rollbackResults: {\r\n          error: 'Failed to remove firewall rule',\r\n          success: false,\r\n        },\r\n      });\r\n\r\n      expect(failedRollbackEvent.isRollbackSuccessful()).toBe(false);\r\n    });\r\n\r\n    it('should handle rollbacks without results', () => {\r\n      const noResultsEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        rollbackResults: undefined,\r\n      });\r\n\r\n      expect(noResultsEvent.isRollbackSuccessful()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('rollback type analysis', () => {\r\n    it('should identify automated rollbacks', () => {\r\n      const automatedEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        rolledBackBy: 'system@automation',\r\n      });\r\n\r\n      expect(automatedEvent.isAutomatedRollback()).toBe(true);\r\n      expect(automatedEvent.isManualRollback()).toBe(false);\r\n\r\n      const botEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        rolledBackBy: 'automation-bot',\r\n      });\r\n      expect(botEvent.isAutomatedRollback()).toBe(true);\r\n    });\r\n\r\n    it('should identify manual rollbacks', () => {\r\n      expect(event.isAutomatedRollback()).toBe(false);\r\n      expect(event.isManualRollback()).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('action type classification', () => {\r\n    it('should identify security-critical rollbacks', () => {\r\n      expect(event.isSecurityCriticalRollback()).toBe(true);\r\n\r\n      const isolateEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.ISOLATE_SYSTEM,\r\n      });\r\n      expect(isolateEvent.isSecurityCriticalRollback()).toBe(true);\r\n\r\n      const shutdownEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.SHUTDOWN_SYSTEM,\r\n      });\r\n      expect(shutdownEvent.isSecurityCriticalRollback()).toBe(true);\r\n\r\n      const deleteEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.DELETE_FILE,\r\n      });\r\n      expect(deleteEvent.isSecurityCriticalRollback()).toBe(true);\r\n\r\n      const patchEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.PATCH_VULNERABILITY,\r\n      });\r\n      expect(patchEvent.isSecurityCriticalRollback()).toBe(true);\r\n\r\n      const emailEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.SEND_EMAIL,\r\n      });\r\n      expect(emailEvent.isSecurityCriticalRollback()).toBe(false);\r\n    });\r\n\r\n    it('should identify containment rollbacks', () => {\r\n      expect(event.isContainmentRollback()).toBe(true);\r\n\r\n      const quarantineEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.QUARANTINE_FILE,\r\n      });\r\n      expect(quarantineEvent.isContainmentRollback()).toBe(true);\r\n\r\n      const blockDomainEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.BLOCK_DOMAIN,\r\n      });\r\n      expect(blockDomainEvent.isContainmentRollback()).toBe(true);\r\n\r\n      const disableAccountEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.DISABLE_ACCOUNT,\r\n      });\r\n      expect(disableAccountEvent.isContainmentRollback()).toBe(true);\r\n\r\n      const terminateEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.TERMINATE_CONNECTION,\r\n      });\r\n      expect(terminateEvent.isContainmentRollback()).toBe(true);\r\n\r\n      const emailEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.SEND_EMAIL,\r\n      });\r\n      expect(emailEvent.isContainmentRollback()).toBe(false);\r\n    });\r\n\r\n    it('should identify recovery rollbacks', () => {\r\n      const backupEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.RESTORE_BACKUP,\r\n      });\r\n      expect(backupEvent.isRecoveryRollback()).toBe(true);\r\n\r\n      const rebuildEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.REBUILD_SYSTEM,\r\n      });\r\n      expect(rebuildEvent.isRecoveryRollback()).toBe(true);\r\n\r\n      const enableEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.ENABLE_SERVICE,\r\n      });\r\n      expect(enableEvent.isRecoveryRollback()).toBe(true);\r\n\r\n      const resetEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.RESET_PASSWORD,\r\n      });\r\n      expect(resetEvent.isRecoveryRollback()).toBe(true);\r\n\r\n      expect(event.isRecoveryRollback()).toBe(false);\r\n    });\r\n\r\n    it('should identify configuration rollbacks', () => {\r\n      const firewallEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.UPDATE_FIREWALL,\r\n      });\r\n      expect(firewallEvent.isConfigurationRollback()).toBe(true);\r\n\r\n      const reconfigureEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.RECONFIGURE_SYSTEM,\r\n      });\r\n      expect(reconfigureEvent.isConfigurationRollback()).toBe(true);\r\n\r\n      const updateEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.UPDATE_SOFTWARE,\r\n      });\r\n      expect(updateEvent.isConfigurationRollback()).toBe(true);\r\n\r\n      expect(event.isConfigurationRollback()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('rollback impact assessment', () => {\r\n    it('should assess critical impact for security-critical containment rollbacks', () => {\r\n      expect(event.getRollbackImpact()).toBe('critical');\r\n    });\r\n\r\n    it('should assess high impact for security-critical non-containment rollbacks', () => {\r\n      const patchEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.PATCH_VULNERABILITY,\r\n      });\r\n      expect(patchEvent.getRollbackImpact()).toBe('high');\r\n    });\r\n\r\n    it('should assess high impact for containment rollbacks', () => {\r\n      const quarantineEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.QUARANTINE_FILE,\r\n      });\r\n      expect(quarantineEvent.getRollbackImpact()).toBe('critical');\r\n    });\r\n\r\n    it('should assess medium impact for recovery rollbacks', () => {\r\n      const recoveryEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.RESTORE_BACKUP,\r\n      });\r\n      expect(recoveryEvent.getRollbackImpact()).toBe('medium');\r\n    });\r\n\r\n    it('should assess medium impact for configuration rollbacks', () => {\r\n      const configEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.UPDATE_FIREWALL,\r\n      });\r\n      expect(configEvent.getRollbackImpact()).toBe('medium');\r\n    });\r\n\r\n    it('should assess low impact for other rollbacks', () => {\r\n      const emailEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.SEND_EMAIL,\r\n      });\r\n      expect(emailEvent.getRollbackImpact()).toBe('low');\r\n    });\r\n  });\r\n\r\n  describe('security implications', () => {\r\n    it('should identify implications for containment rollbacks', () => {\r\n      const implications = event.getSecurityImplications();\r\n      expect(implications).toContain('Containment measures removed - systems may be exposed');\r\n      expect(implications).toContain('Threat may regain access to previously contained resources');\r\n      expect(implications).toContain('Increased risk exposure until alternative measures implemented');\r\n    });\r\n\r\n    it('should identify implications for IP blocking rollbacks', () => {\r\n      const implications = event.getSecurityImplications();\r\n      expect(implications).toContain('IP address unblocked - traffic from this IP now allowed');\r\n      expect(implications).toContain('Monitor for suspicious activity from unblocked IP');\r\n    });\r\n\r\n    it('should identify implications for account disabling rollbacks', () => {\r\n      const accountEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.DISABLE_ACCOUNT,\r\n      });\r\n\r\n      const implications = accountEvent.getSecurityImplications();\r\n      expect(implications).toContain('User account re-enabled - access restored');\r\n      expect(implications).toContain('Monitor account activity for suspicious behavior');\r\n    });\r\n\r\n    it('should identify implications for file quarantine rollbacks', () => {\r\n      const quarantineEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.QUARANTINE_FILE,\r\n      });\r\n\r\n      const implications = quarantineEvent.getSecurityImplications();\r\n      expect(implications).toContain('File removed from quarantine - file now accessible');\r\n      expect(implications).toContain('Ensure file is safe before allowing access');\r\n    });\r\n\r\n    it('should identify implications for firewall rollbacks', () => {\r\n      const firewallEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.UPDATE_FIREWALL,\r\n      });\r\n\r\n      const implications = firewallEvent.getSecurityImplications();\r\n      expect(implications).toContain('Firewall rules reverted - network access patterns changed');\r\n      expect(implications).toContain('Review network traffic for unexpected patterns');\r\n    });\r\n\r\n    it('should identify implications for vulnerability patch rollbacks', () => {\r\n      const patchEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.PATCH_VULNERABILITY,\r\n      });\r\n\r\n      const implications = patchEvent.getSecurityImplications();\r\n      expect(implications).toContain('Vulnerability patch removed - system may be vulnerable again');\r\n      expect(implications).toContain('Implement alternative protection measures');\r\n    });\r\n  });\r\n\r\n  describe('post-rollback recommendations', () => {\r\n    it('should recommend general post-rollback actions', () => {\r\n      const actions = event.getRecommendedPostActions();\r\n      expect(actions).toContain('Validate rollback completion');\r\n      expect(actions).toContain('Document rollback rationale');\r\n    });\r\n\r\n    it('should recommend actions for security-critical rollbacks', () => {\r\n      const actions = event.getRecommendedPostActions();\r\n      expect(actions).toContain('Assess security posture after rollback');\r\n      expect(actions).toContain('Implement alternative security measures if needed');\r\n      expect(actions).toContain('Monitor for security incidents');\r\n    });\r\n\r\n    it('should recommend actions for containment rollbacks', () => {\r\n      const actions = event.getRecommendedPostActions();\r\n      expect(actions).toContain('Implement alternative containment measures');\r\n      expect(actions).toContain('Monitor for threat activity');\r\n      expect(actions).toContain('Assess threat landscape changes');\r\n    });\r\n\r\n    it('should recommend actions for recovery rollbacks', () => {\r\n      const recoveryEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.RESTORE_BACKUP,\r\n      });\r\n\r\n      const actions = recoveryEvent.getRecommendedPostActions();\r\n      expect(actions).toContain('Verify system functionality after rollback');\r\n      expect(actions).toContain('Check service availability');\r\n      expect(actions).toContain('Consider alternative recovery approaches');\r\n    });\r\n\r\n    it('should recommend actions for configuration rollbacks', () => {\r\n      const configEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.UPDATE_FIREWALL,\r\n      });\r\n\r\n      const actions = configEvent.getRecommendedPostActions();\r\n      expect(actions).toContain('Validate configuration state');\r\n      expect(actions).toContain('Test system functionality');\r\n      expect(actions).toContain('Update configuration documentation');\r\n    });\r\n\r\n    it('should recommend action-specific post-rollback actions for IP blocking', () => {\r\n      const actions = event.getRecommendedPostActions();\r\n      expect(actions).toContain('Monitor traffic from unblocked IP address');\r\n      expect(actions).toContain('Consider alternative blocking methods');\r\n    });\r\n\r\n    it('should recommend action-specific post-rollback actions for account disabling', () => {\r\n      const accountEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.DISABLE_ACCOUNT,\r\n      });\r\n\r\n      const actions = accountEvent.getRecommendedPostActions();\r\n      expect(actions).toContain('Monitor re-enabled account activity');\r\n      expect(actions).toContain('Review account permissions');\r\n    });\r\n\r\n    it('should recommend action-specific post-rollback actions for file quarantine', () => {\r\n      const quarantineEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.QUARANTINE_FILE,\r\n      });\r\n\r\n      const actions = quarantineEvent.getRecommendedPostActions();\r\n      expect(actions).toContain('Re-scan file for threats');\r\n      expect(actions).toContain('Monitor file access patterns');\r\n    });\r\n\r\n    it('should recommend action-specific post-rollback actions for system isolation', () => {\r\n      const isolateEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.ISOLATE_SYSTEM,\r\n      });\r\n\r\n      const actions = isolateEvent.getRecommendedPostActions();\r\n      expect(actions).toContain('Monitor system for suspicious activity');\r\n      expect(actions).toContain('Implement network monitoring');\r\n    });\r\n  });\r\n\r\n  describe('notification targets', () => {\r\n    it('should identify basic notification targets', () => {\r\n      const targets = event.getNotificationTargets();\r\n      expect(targets).toContain('rollback-requestor');\r\n      expect(targets).toContain('original-action-requestor');\r\n    });\r\n\r\n    it('should identify targets for security-critical rollbacks', () => {\r\n      const targets = event.getNotificationTargets();\r\n      expect(targets).toContain('security-team');\r\n      expect(targets).toContain('incident-response-team');\r\n    });\r\n\r\n    it('should identify targets for containment rollbacks', () => {\r\n      const targets = event.getNotificationTargets();\r\n      expect(targets).toContain('security-managers');\r\n      expect(targets).toContain('containment-specialists');\r\n      expect(targets).toContain('threat-analysts');\r\n    });\r\n\r\n    it('should identify targets for recovery rollbacks', () => {\r\n      const recoveryEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.RESTORE_BACKUP,\r\n      });\r\n\r\n      const targets = recoveryEvent.getNotificationTargets();\r\n      expect(targets).toContain('service-owners');\r\n      expect(targets).toContain('operations-team');\r\n    });\r\n\r\n    it('should identify targets for configuration rollbacks', () => {\r\n      const configEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.UPDATE_FIREWALL,\r\n      });\r\n\r\n      const targets = configEvent.getNotificationTargets();\r\n      expect(targets).toContain('system-administrators');\r\n      expect(targets).toContain('configuration-managers');\r\n    });\r\n  });\r\n\r\n  describe('compliance considerations', () => {\r\n    it('should provide general compliance considerations', () => {\r\n      const considerations = event.getComplianceConsiderations();\r\n      expect(considerations).toContain('Document rollback decision and rationale');\r\n      expect(considerations).toContain('Maintain audit trail of rollback process');\r\n    });\r\n\r\n    it('should provide considerations for security-critical rollbacks', () => {\r\n      const considerations = event.getComplianceConsiderations();\r\n      expect(considerations).toContain('Ensure rollback approval was properly obtained');\r\n      expect(considerations).toContain('Document security impact assessment');\r\n    });\r\n\r\n    it('should provide considerations for containment rollbacks', () => {\r\n      const considerations = event.getComplianceConsiderations();\r\n      expect(considerations).toContain('Document risk acceptance for containment removal');\r\n      expect(considerations).toContain('Ensure alternative controls are in place');\r\n    });\r\n\r\n    it('should provide considerations for file deletion rollbacks', () => {\r\n      const deleteEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.DELETE_FILE,\r\n      });\r\n\r\n      const considerations = deleteEvent.getComplianceConsiderations();\r\n      expect(considerations).toContain('Ensure data recovery complies with retention policies');\r\n      expect(considerations).toContain('Document data restoration rationale');\r\n    });\r\n  });\r\n\r\n  describe('monitoring requirements', () => {\r\n    it('should provide monitoring requirements for security-critical rollbacks', () => {\r\n      const monitoring = event.getMonitoringRequirements();\r\n      expect(monitoring.duration).toBe('long');\r\n      expect(monitoring.intensity).toBe('intensive');\r\n      expect(monitoring.focus).toContain('security-events');\r\n      expect(monitoring.focus).toContain('threat-indicators');\r\n    });\r\n\r\n    it('should provide monitoring requirements for containment rollbacks', () => {\r\n      const monitoring = event.getMonitoringRequirements();\r\n      expect(monitoring.duration).toBe('ongoing');\r\n      expect(monitoring.intensity).toBe('intensive');\r\n      expect(monitoring.focus).toContain('threat-activity');\r\n      expect(monitoring.focus).toContain('network-traffic');\r\n      expect(monitoring.focus).toContain('system-access');\r\n    });\r\n\r\n    it('should provide monitoring requirements for recovery rollbacks', () => {\r\n      const recoveryEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.RESTORE_BACKUP,\r\n      });\r\n\r\n      const monitoring = recoveryEvent.getMonitoringRequirements();\r\n      expect(monitoring.duration).toBe('medium');\r\n      expect(monitoring.intensity).toBe('enhanced');\r\n      expect(monitoring.focus).toContain('system-performance');\r\n      expect(monitoring.focus).toContain('service-availability');\r\n    });\r\n\r\n    it('should provide monitoring requirements for configuration rollbacks', () => {\r\n      const configEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.UPDATE_FIREWALL,\r\n      });\r\n\r\n      const monitoring = configEvent.getMonitoringRequirements();\r\n      expect(monitoring.duration).toBe('medium');\r\n      expect(monitoring.intensity).toBe('enhanced');\r\n      expect(monitoring.focus).toContain('configuration-drift');\r\n      expect(monitoring.focus).toContain('system-behavior');\r\n    });\r\n\r\n    it('should provide basic monitoring requirements for low-impact rollbacks', () => {\r\n      const emailEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.SEND_EMAIL,\r\n      });\r\n\r\n      const monitoring = emailEvent.getMonitoringRequirements();\r\n      expect(monitoring.duration).toBe('short');\r\n      expect(monitoring.intensity).toBe('basic');\r\n    });\r\n  });\r\n\r\n  describe('rollback metrics', () => {\r\n    it('should generate comprehensive rollback metrics', () => {\r\n      const metrics = event.getRollbackMetrics();\r\n\r\n      expect(metrics.actionType).toBe(ActionType.BLOCK_IP);\r\n      expect(metrics.rollbackImpact).toBe('critical');\r\n      expect(metrics.isSecurityCritical).toBe(true);\r\n      expect(metrics.isAutomated).toBe(false);\r\n      expect(metrics.isSuccessful).toBe(true);\r\n      expect(metrics.securityImplicationsCount).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should generate metrics for failed rollbacks', () => {\r\n      const failedEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        rollbackResults: { error: 'Rollback failed' },\r\n        rolledBackBy: 'system@automation',\r\n      });\r\n\r\n      const metrics = failedEvent.getRollbackMetrics();\r\n\r\n      expect(metrics.isSuccessful).toBe(false);\r\n      expect(metrics.isAutomated).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('integration event conversion', () => {\r\n    it('should convert to integration event format', () => {\r\n      const integrationEvent = event.toIntegrationEvent();\r\n\r\n      expect(integrationEvent.eventType).toBe('ResponseActionRolledBack');\r\n      expect(integrationEvent.action).toBe('response_action_rolled_back');\r\n      expect(integrationEvent.resource).toBe('ResponseAction');\r\n      expect(integrationEvent.resourceId).toBe(aggregateId.toString());\r\n      expect(integrationEvent.data).toBe(eventData);\r\n      expect(integrationEvent.metadata).toEqual({\r\n        rollbackImpact: 'critical',\r\n        isSecurityCritical: true,\r\n        isContainmentRollback: true,\r\n        isSuccessful: true,\r\n        requiresMonitoring: true,\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('edge cases', () => {\r\n    it('should handle events without rollback results', () => {\r\n      const noResultsEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        rollbackResults: undefined,\r\n      });\r\n\r\n      expect(noResultsEvent.rollbackResults).toBeUndefined();\r\n      expect(noResultsEvent.isRollbackSuccessful()).toBe(false);\r\n    });\r\n\r\n    it('should handle events without original execution results', () => {\r\n      const noOriginalEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        originalExecutionResults: undefined,\r\n      });\r\n\r\n      expect(noOriginalEvent.originalExecutionResults).toBeUndefined();\r\n      expect(noOriginalEvent.getSecurityImplications()).toContain('Containment measures removed - systems may be exposed');\r\n    });\r\n\r\n    it('should handle unknown action types gracefully', () => {\r\n      const unknownEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: 'UNKNOWN_ACTION' as ActionType,\r\n      });\r\n\r\n      expect(unknownEvent.isSecurityCriticalRollback()).toBe(false);\r\n      expect(unknownEvent.isContainmentRollback()).toBe(false);\r\n      expect(unknownEvent.isRecoveryRollback()).toBe(false);\r\n      expect(unknownEvent.isConfigurationRollback()).toBe(false);\r\n      expect(unknownEvent.getRollbackImpact()).toBe('low');\r\n    });\r\n\r\n    it('should handle rollback results with error property', () => {\r\n      const errorEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        rollbackResults: {\r\n          error: 'Partial rollback failure',\r\n          partialSuccess: true,\r\n        },\r\n      });\r\n\r\n      expect(errorEvent.isRollbackSuccessful()).toBe(false);\r\n    });\r\n\r\n    it('should handle empty security implications gracefully', () => {\r\n      const emailEvent = new ResponseActionRolledBackDomainEvent(aggregateId, {\r\n        ...eventData,\r\n        actionType: ActionType.SEND_EMAIL,\r\n      });\r\n\r\n      const implications = emailEvent.getSecurityImplications();\r\n      expect(Array.isArray(implications)).toBe(true);\r\n      expect(emailEvent.getRollbackMetrics().securityImplicationsCount).toBe(implications.length);\r\n    });\r\n  });\r\n});"], "version": 3}