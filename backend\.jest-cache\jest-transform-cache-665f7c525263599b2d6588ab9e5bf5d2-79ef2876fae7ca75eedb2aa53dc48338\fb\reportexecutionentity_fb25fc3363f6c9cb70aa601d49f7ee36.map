{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\entities\\report-execution.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCASiB;AACjB,yEAA8D;AAE9D;;;;;;;;;;;GAWG;AAMI,IAAM,eAAe,GAArB,MAAM,eAAe;IAqN1B,yBAAyB;IAEzB;;OAEG;IACH,oBAAoB;QAClB,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO,CAAC,CAAC;QAE9B,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,IAAI,EAAE,CAAC;QAC/C,OAAO,OAAO,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,IAAI,CAAC,MAAM,KAAK,SAAS,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC;IAChE,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,IAAI,IAAI,CAAC,MAAM,KAAK,WAAW,CAAC;IAC9F,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAAE,OAAO,KAAK,CAAC;QAEnC,IAAI,IAAI,CAAC,YAAY,EAAE,WAAW,KAAK,KAAK;YAAE,OAAO,KAAK,CAAC;QAE3D,MAAM,UAAU,GAAG,CAAC,CAAC;QACrB,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,EAAE,UAAU,IAAI,CAAC,CAAC;QAEtD,OAAO,UAAU,GAAG,UAAU,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1E,OAAO,CAAC,CAAC;QACX,CAAC;QAED,IAAI,KAAK,GAAG,GAAG,CAAC;QAEhB,iCAAiC;QACjC,MAAM,kBAAkB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,4BAA4B;QAC9G,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,kBAAkB,GAAG,GAAG,CAAC;QAE/C,8BAA8B;QAC9B,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,GAAG,CAAC;QACvD,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,UAAU,GAAG,GAAG,CAAC;QAEvC,+BAA+B;QAC/B,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,2BAA2B;QAClI,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,WAAW,GAAG,GAAG,CAAC;QAExC,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,WAAW;YAAE,OAAO,CAAC,CAAC;QAEhD,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;QAC5F,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,YAAY,GAAG,QAAQ,GAAG,WAAW,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,UAAkB,EAAE,IAAa;QAC9C,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC,CAAC;QACjE,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAC1B,CAAC;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,cAAoB;QAChC,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;QAC1B,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QAC9B,IAAI,CAAC,kBAAkB,GAAG,GAAG,CAAC;QAC9B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAEnD,IAAI,cAAc,EAAE,CAAC;YACnB,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACvC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,KAAY,EAAE,IAAa;QACpC,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;QACvB,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QAC9B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAEnD,IAAI,CAAC,YAAY,GAAG;YAClB,SAAS,EAAE,KAAK,CAAC,IAAI,IAAI,eAAe;YACxC,YAAY,EAAE,KAAK,CAAC,OAAO;YAC3B,UAAU,EAAE,KAAK,CAAC,KAAK;YACvB,UAAU,EAAE,IAAI,IAAI,IAAI,CAAC,WAAW;YACpC,UAAU,EAAE,CAAC,IAAI,CAAC,YAAY,EAAE,UAAU,IAAI,CAAC,CAAC,GAAG,CAAC;YACpD,WAAW,EAAE,IAAI,IAAI,EAAE;YACvB,WAAW,EAAE,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;YAC7C,oBAAoB,EAAE,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC;SAC/D,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAe;QACpB,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;QAC1B,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QAC9B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACnD,IAAI,CAAC,WAAW,GAAG,cAAc,MAAM,IAAI,gBAAgB,EAAE,CAAC;IAChE,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,KAAa,EAAE,OAA4B;QAC1D,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC;YAClC,KAAK;YACL,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,OAAO;SACR,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,MAAc,EAAE,SAAkB,EAAE,KAAc;QACrE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,IAAI,CAAC,cAAc,GAAG;gBACpB,MAAM,EAAE,SAAS;gBACjB,UAAU,EAAE,EAAE;gBACd,gBAAgB,EAAE,CAAC;gBACnB,MAAM,EAAE,SAAS;aAClB,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,MAAa,CAAC;QAC3C,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;QACvC,IAAI,CAAC,cAAc,CAAC,aAAa,GAAG,IAAI,IAAI,EAAE,CAAC;QAE/C,IAAI,MAAM,KAAK,WAAW,EAAE,CAAC;YAC3B,IAAI,CAAC,cAAc,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;YAC7C,IAAI,SAAS,EAAE,CAAC;gBACd,IAAI,CAAC,cAAc,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,IAAI,EAAE,CAAC;gBAC5E,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC;oBACrC,SAAS;oBACT,WAAW,EAAE,IAAI,IAAI,EAAE;oBACvB,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM;iBACnC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,IAAI,EAAE,CAAC;YAC9D,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,UAAU;QASR,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,QAAQ,EAAE,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7D,gBAAgB,EAAE,IAAI,CAAC,WAAW,EAAE,gBAAgB,IAAI,CAAC;YACzD,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC1C,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC5C,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,YAAY;SAC9C,CAAC;IACJ,CAAC;IAED,yBAAyB;IAEjB,oBAAoB,CAAC,KAAY;QACvC,MAAM,kBAAkB,GAAG;YACzB,mBAAmB;YACnB,uBAAuB;YACvB,gBAAgB;YAChB,kBAAkB;SACnB,CAAC;QAEF,OAAO,CAAC,kBAAkB,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC;IAEO,4BAA4B,CAAC,KAAY;QAC/C,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,eAAe;gBAClB,KAAK,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;gBACjE,KAAK,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;gBAC1D,MAAM;YACR,KAAK,cAAc;gBACjB,KAAK,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;gBACxD,KAAK,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;gBAClD,MAAM;YACR,KAAK,mBAAmB;gBACtB,KAAK,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;gBAClE,KAAK,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;gBAC3C,MAAM;YACR,KAAK,mBAAmB;gBACtB,KAAK,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;gBAC9C,KAAK,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;gBACnD,MAAM;YACR;gBACE,KAAK,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;gBACtD,KAAK,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QACrE,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;CACF,CAAA;AA5cY,0CAAe;AAE1B;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;2CACpB;AAIX;IAFC,IAAA,gBAAM,GAAE;IACR,IAAA,eAAK,GAAE;;2DACmB;AAQ3B;IANC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC;QAC3E,OAAO,EAAE,SAAS;KACnB,CAAC;IACD,IAAA,eAAK,GAAE;;+CACO;AAOf;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE,mBAAmB,CAAC;KACrE,CAAC;IACD,IAAA,eAAK,GAAE;;sDACc;AAItB;IAFC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC1B,IAAA,eAAK,GAAE;;mDACW;AAInB;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,mBAAmB,EAAE,CAAC;IACjE,IAAA,eAAK,GAAE;kDACG,IAAI,oBAAJ,IAAI;kDAAC;AAGhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACjC,IAAI,oBAAJ,IAAI;oDAAC;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;wDACpB;AAGxB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;2DACb;AAG3B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;oDACrB;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;;yDAWxB;AAGF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;oDAajC;AAGT;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uDAajC;AAGT;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDAUjC;AAGT;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;;2DA0BxB;AAGF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uDAkBjC;AAGT;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;;mDAyBxB;AAGF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uDAcjC;AAGT;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;;iDAWxB;AAGF;IADC,IAAA,0BAAgB,GAAE;kDACR,IAAI,oBAAJ,IAAI;kDAAC;AAGhB;IADC,IAAA,0BAAgB,GAAE;kDACR,IAAI,oBAAJ,IAAI;kDAAC;AAKhB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,2CAAgB,EAAE,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;IACtE,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,CAAC;kDACzB,2CAAgB,oBAAhB,2CAAgB;yDAAC;0BAnNxB,eAAe;IAL3B,IAAA,gBAAM,EAAC,mBAAmB,CAAC;IAC3B,IAAA,eAAK,EAAC,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC;IACvC,IAAA,eAAK,EAAC,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;IAClC,IAAA,eAAK,EAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;IAC9B,IAAA,eAAK,EAAC,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC;GACzB,eAAe,CA4c3B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\entities\\report-execution.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  ManyToOne,\r\n  JoinColumn,\r\n} from 'typeorm';\r\nimport { ReportDefinition } from './report-definition.entity';\r\n\r\n/**\r\n * Report Execution Entity\r\n * \r\n * Tracks individual report execution instances including:\r\n * - Execution status and progress tracking\r\n * - Performance metrics and timing data\r\n * - Generated output and export information\r\n * - Error handling and debugging information\r\n * - User context and access audit trail\r\n * - Resource usage and optimization metrics\r\n * - Cache management and invalidation tracking\r\n */\r\n@Entity('report_executions')\r\n@Index(['reportDefinitionId', 'status'])\r\n@Index(['executedBy', 'startedAt'])\r\n@Index(['status', 'startedAt'])\r\n@Index(['executionType', 'createdAt'])\r\nexport class ReportExecution {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  @Column()\r\n  @Index()\r\n  reportDefinitionId: string;\r\n\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['pending', 'running', 'completed', 'failed', 'cancelled', 'timeout'],\r\n    default: 'pending',\r\n  })\r\n  @Index()\r\n  status: string;\r\n\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['manual', 'scheduled', 'api', 'webhook', 'dashboard_refresh'],\r\n  })\r\n  @Index()\r\n  executionType: string;\r\n\r\n  @Column({ nullable: true })\r\n  @Index()\r\n  executedBy: string;\r\n\r\n  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })\r\n  @Index()\r\n  startedAt: Date;\r\n\r\n  @Column({ type: 'timestamp', nullable: true })\r\n  completedAt: Date;\r\n\r\n  @Column({ type: 'integer', nullable: true })\r\n  executionTimeMs: number;\r\n\r\n  @Column({ type: 'integer', default: 0 })\r\n  progressPercentage: number;\r\n\r\n  @Column({ type: 'text', nullable: true })\r\n  currentStep: string;\r\n\r\n  @Column({ type: 'jsonb' })\r\n  executionContext: {\r\n    userAgent?: string;\r\n    ipAddress?: string;\r\n    sessionId?: string;\r\n    requestId?: string;\r\n    filters?: Record<string, any>;\r\n    parameters?: Record<string, any>;\r\n    exportFormat?: string;\r\n    deliveryMethod?: string;\r\n    priority?: 'low' | 'medium' | 'high' | 'urgent';\r\n  };\r\n\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  dataMetrics: {\r\n    recordsProcessed: number;\r\n    dataSourcesQueried: number;\r\n    cacheHitRate: number;\r\n    queryExecutionTimes: {\r\n      source: string;\r\n      executionTime: number;\r\n      recordCount: number;\r\n    }[];\r\n    aggregationTime: number;\r\n    renderingTime: number;\r\n    totalDataSize: number;\r\n  } | null;\r\n\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  outputMetadata: {\r\n    format: string;\r\n    fileSize: number;\r\n    fileName: string;\r\n    filePath?: string;\r\n    downloadUrl?: string;\r\n    expiresAt?: Date;\r\n    checksum?: string;\r\n    compressionRatio?: number;\r\n    pageCount?: number;\r\n    chartCount?: number;\r\n    tableCount?: number;\r\n  } | null;\r\n\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  errorDetails: {\r\n    errorCode: string;\r\n    errorMessage: string;\r\n    stackTrace?: string;\r\n    failedStep?: string;\r\n    retryCount?: number;\r\n    lastRetryAt?: Date;\r\n    isRetryable?: boolean;\r\n    troubleshootingHints?: string[];\r\n  } | null;\r\n\r\n  @Column({ type: 'jsonb' })\r\n  performanceMetrics: {\r\n    memoryUsage: {\r\n      peak: number;\r\n      average: number;\r\n      final: number;\r\n    };\r\n    cpuUsage: {\r\n      peak: number;\r\n      average: number;\r\n    };\r\n    networkIO: {\r\n      bytesIn: number;\r\n      bytesOut: number;\r\n      requestCount: number;\r\n    };\r\n    diskIO: {\r\n      bytesRead: number;\r\n      bytesWritten: number;\r\n    };\r\n    cacheMetrics: {\r\n      hits: number;\r\n      misses: number;\r\n      evictions: number;\r\n      hitRate: number;\r\n    };\r\n  };\r\n\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  qualityMetrics: {\r\n    dataQuality: {\r\n      completeness: number;\r\n      accuracy: number;\r\n      consistency: number;\r\n      timeliness: number;\r\n    };\r\n    visualizationQuality: {\r\n      renderingAccuracy: number;\r\n      responsiveness: number;\r\n      accessibility: number;\r\n    };\r\n    userSatisfaction: {\r\n      rating?: number;\r\n      feedback?: string;\r\n      usabilityScore?: number;\r\n    };\r\n  } | null;\r\n\r\n  @Column({ type: 'jsonb' })\r\n  auditTrail: {\r\n    accessedData: {\r\n      source: string;\r\n      tables: string[];\r\n      recordCount: number;\r\n      sensitivityLevel: string;\r\n    }[];\r\n    permissions: {\r\n      userId: string;\r\n      roles: string[];\r\n      permissions: string[];\r\n      dataFilters: Record<string, any>;\r\n    };\r\n    compliance: {\r\n      frameworks: string[];\r\n      dataClassification: string;\r\n      retentionRequired: boolean;\r\n      auditRequired: boolean;\r\n    };\r\n    securityEvents: {\r\n      event: string;\r\n      timestamp: Date;\r\n      details: Record<string, any>;\r\n    }[];\r\n  };\r\n\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  deliveryStatus: {\r\n    method: string;\r\n    recipients: string[];\r\n    deliveredAt?: Date;\r\n    deliveryAttempts: number;\r\n    lastAttemptAt?: Date;\r\n    status: 'pending' | 'delivered' | 'failed' | 'partial';\r\n    errors?: string[];\r\n    confirmations?: {\r\n      recipient: string;\r\n      confirmedAt: Date;\r\n      method: string;\r\n    }[];\r\n  } | null;\r\n\r\n  @Column({ type: 'jsonb' })\r\n  metadata: {\r\n    version: string;\r\n    environment: string;\r\n    serverInstance: string;\r\n    executionQueue?: string;\r\n    priority: number;\r\n    tags: string[];\r\n    correlationId?: string;\r\n    parentExecutionId?: string;\r\n    childExecutionIds?: string[];\r\n  };\r\n\r\n  @CreateDateColumn()\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn()\r\n  updatedAt: Date;\r\n\r\n  // Relationships\r\n  @ManyToOne(() => ReportDefinition, definition => definition.executions)\r\n  @JoinColumn({ name: 'reportDefinitionId' })\r\n  reportDefinition: ReportDefinition;\r\n\r\n  // Business Logic Methods\r\n\r\n  /**\r\n   * Calculate execution duration\r\n   */\r\n  getExecutionDuration(): number {\r\n    if (!this.startedAt) return 0;\r\n    \r\n    const endTime = this.completedAt || new Date();\r\n    return endTime.getTime() - this.startedAt.getTime();\r\n  }\r\n\r\n  /**\r\n   * Check if execution is still running\r\n   */\r\n  isRunning(): boolean {\r\n    return this.status === 'running' || this.status === 'pending';\r\n  }\r\n\r\n  /**\r\n   * Check if execution completed successfully\r\n   */\r\n  isSuccessful(): boolean {\r\n    return this.status === 'completed';\r\n  }\r\n\r\n  /**\r\n   * Check if execution failed\r\n   */\r\n  isFailed(): boolean {\r\n    return this.status === 'failed' || this.status === 'timeout' || this.status === 'cancelled';\r\n  }\r\n\r\n  /**\r\n   * Check if execution can be retried\r\n   */\r\n  canRetry(): boolean {\r\n    if (!this.isFailed()) return false;\r\n    \r\n    if (this.errorDetails?.isRetryable === false) return false;\r\n    \r\n    const maxRetries = 3;\r\n    const retryCount = this.errorDetails?.retryCount || 0;\r\n    \r\n    return retryCount < maxRetries;\r\n  }\r\n\r\n  /**\r\n   * Get execution efficiency score\r\n   */\r\n  getEfficiencyScore(): number {\r\n    if (!this.isSuccessful() || !this.dataMetrics || !this.performanceMetrics) {\r\n      return 0;\r\n    }\r\n\r\n    let score = 100;\r\n\r\n    // Deduct for high execution time\r\n    const executionTimeScore = Math.max(0, 100 - (this.executionTimeMs / 1000 / 60)); // Deduct 1 point per minute\r\n    score = score * 0.3 + executionTimeScore * 0.3;\r\n\r\n    // Add for high cache hit rate\r\n    const cacheScore = this.dataMetrics.cacheHitRate * 100;\r\n    score = score * 0.7 + cacheScore * 0.3;\r\n\r\n    // Deduct for high memory usage\r\n    const memoryScore = Math.max(0, 100 - (this.performanceMetrics.memoryUsage.peak / 1024 / 1024 / 100)); // Deduct 1 point per 100MB\r\n    score = score * 0.8 + memoryScore * 0.2;\r\n\r\n    return Math.round(Math.max(0, Math.min(100, score)));\r\n  }\r\n\r\n  /**\r\n   * Get data quality score\r\n   */\r\n  getDataQualityScore(): number {\r\n    if (!this.qualityMetrics?.dataQuality) return 0;\r\n\r\n    const { completeness, accuracy, consistency, timeliness } = this.qualityMetrics.dataQuality;\r\n    return Math.round((completeness + accuracy + consistency + timeliness) / 4);\r\n  }\r\n\r\n  /**\r\n   * Update progress\r\n   */\r\n  updateProgress(percentage: number, step?: string): void {\r\n    this.progressPercentage = Math.max(0, Math.min(100, percentage));\r\n    if (step) {\r\n      this.currentStep = step;\r\n    }\r\n    this.updatedAt = new Date();\r\n  }\r\n\r\n  /**\r\n   * Mark as completed\r\n   */\r\n  markCompleted(outputMetadata?: any): void {\r\n    this.status = 'completed';\r\n    this.completedAt = new Date();\r\n    this.progressPercentage = 100;\r\n    this.currentStep = 'Completed';\r\n    this.executionTimeMs = this.getExecutionDuration();\r\n    \r\n    if (outputMetadata) {\r\n      this.outputMetadata = outputMetadata;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Mark as failed\r\n   */\r\n  markFailed(error: Error, step?: string): void {\r\n    this.status = 'failed';\r\n    this.completedAt = new Date();\r\n    this.executionTimeMs = this.getExecutionDuration();\r\n    \r\n    this.errorDetails = {\r\n      errorCode: error.name || 'UNKNOWN_ERROR',\r\n      errorMessage: error.message,\r\n      stackTrace: error.stack,\r\n      failedStep: step || this.currentStep,\r\n      retryCount: (this.errorDetails?.retryCount || 0) + 1,\r\n      lastRetryAt: new Date(),\r\n      isRetryable: this.determineIfRetryable(error),\r\n      troubleshootingHints: this.generateTroubleshootingHints(error),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Cancel execution\r\n   */\r\n  cancel(reason?: string): void {\r\n    this.status = 'cancelled';\r\n    this.completedAt = new Date();\r\n    this.executionTimeMs = this.getExecutionDuration();\r\n    this.currentStep = `Cancelled: ${reason || 'User requested'}`;\r\n  }\r\n\r\n  /**\r\n   * Add security event to audit trail\r\n   */\r\n  addSecurityEvent(event: string, details: Record<string, any>): void {\r\n    this.auditTrail.securityEvents.push({\r\n      event,\r\n      timestamp: new Date(),\r\n      details,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Update delivery status\r\n   */\r\n  updateDeliveryStatus(status: string, recipient?: string, error?: string): void {\r\n    if (!this.deliveryStatus) {\r\n      this.deliveryStatus = {\r\n        method: 'unknown',\r\n        recipients: [],\r\n        deliveryAttempts: 0,\r\n        status: 'pending',\r\n      };\r\n    }\r\n\r\n    this.deliveryStatus.status = status as any;\r\n    this.deliveryStatus.deliveryAttempts++;\r\n    this.deliveryStatus.lastAttemptAt = new Date();\r\n\r\n    if (status === 'delivered') {\r\n      this.deliveryStatus.deliveredAt = new Date();\r\n      if (recipient) {\r\n        this.deliveryStatus.confirmations = this.deliveryStatus.confirmations || [];\r\n        this.deliveryStatus.confirmations.push({\r\n          recipient,\r\n          confirmedAt: new Date(),\r\n          method: this.deliveryStatus.method,\r\n        });\r\n      }\r\n    }\r\n\r\n    if (error) {\r\n      this.deliveryStatus.errors = this.deliveryStatus.errors || [];\r\n      this.deliveryStatus.errors.push(error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get execution summary\r\n   */\r\n  getSummary(): {\r\n    id: string;\r\n    status: string;\r\n    duration: number;\r\n    recordsProcessed: number;\r\n    efficiencyScore: number;\r\n    dataQualityScore: number;\r\n    errorMessage?: string;\r\n  } {\r\n    return {\r\n      id: this.id,\r\n      status: this.status,\r\n      duration: this.executionTimeMs || this.getExecutionDuration(),\r\n      recordsProcessed: this.dataMetrics?.recordsProcessed || 0,\r\n      efficiencyScore: this.getEfficiencyScore(),\r\n      dataQualityScore: this.getDataQualityScore(),\r\n      errorMessage: this.errorDetails?.errorMessage,\r\n    };\r\n  }\r\n\r\n  // Private helper methods\r\n\r\n  private determineIfRetryable(error: Error): boolean {\r\n    const nonRetryableErrors = [\r\n      'PERMISSION_DENIED',\r\n      'INVALID_CONFIGURATION',\r\n      'DATA_NOT_FOUND',\r\n      'VALIDATION_ERROR',\r\n    ];\r\n\r\n    return !nonRetryableErrors.includes(error.name);\r\n  }\r\n\r\n  private generateTroubleshootingHints(error: Error): string[] {\r\n    const hints: string[] = [];\r\n\r\n    switch (error.name) {\r\n      case 'TIMEOUT_ERROR':\r\n        hints.push('Consider reducing the data range or adding filters');\r\n        hints.push('Check if data sources are responding slowly');\r\n        break;\r\n      case 'MEMORY_ERROR':\r\n        hints.push('Reduce the amount of data being processed');\r\n        hints.push('Enable data streaming or pagination');\r\n        break;\r\n      case 'PERMISSION_DENIED':\r\n        hints.push('Verify user has access to all required data sources');\r\n        hints.push('Check role-based permissions');\r\n        break;\r\n      case 'DATA_SOURCE_ERROR':\r\n        hints.push('Verify data source connectivity');\r\n        hints.push('Check if required tables/views exist');\r\n        break;\r\n      default:\r\n        hints.push('Check the error details and stack trace');\r\n        hints.push('Contact system administrator if the issue persists');\r\n    }\r\n\r\n    return hints;\r\n  }\r\n}\r\n"], "version": 3}