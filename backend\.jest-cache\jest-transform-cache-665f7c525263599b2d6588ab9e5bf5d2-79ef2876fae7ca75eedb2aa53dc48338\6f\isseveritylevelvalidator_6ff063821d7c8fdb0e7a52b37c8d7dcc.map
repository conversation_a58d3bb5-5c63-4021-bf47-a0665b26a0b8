{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\validation\\validators\\is-severity-level.validator.ts", "mappings": ";;;;;;;;;AAmKA,0CAaC;AAQD,sDAaC;AAOD,wDAQC;AAOD,kDASC;AAQD,oDA0BC;AAtQD,qDAMyB;AAEzB;;;GAGG;AACH,IAAY,aAOX;AAPD,WAAY,aAAa;IACvB,sCAAqB,CAAA;IACrB,8BAAa,CAAA;IACb,kCAAiB,CAAA;IACjB,4BAAW,CAAA;IACX,gDAA+B,CAAA;IAC/B,8BAAa,CAAA;AACf,CAAC,EAPW,aAAa,6BAAb,aAAa,QAOxB;AAED;;;GAGG;AACH,IAAY,eAUX;AAVD,WAAY,eAAe;IACzB,qDAAQ,CAAA;IACR,6DAAa,CAAA;IACb,6DAAa,CAAA;IACb,iEAAgB,CAAA;IAChB,mEAAgB,CAAA;IAChB,6DAAc,CAAA;IACd,+DAAc,CAAA;IACd,qEAAkB,CAAA;IAClB,sEAAmB,CAAA;AACrB,CAAC,EAVW,eAAe,+BAAf,eAAe,QAU1B;AAgBD;;;GAGG;AAEI,IAAM,yBAAyB,GAA/B,MAAM,yBAAyB;IACpC;;;;;OAKG;IACH,QAAQ,CAAC,KAAU,EAAE,IAAyB;QAC5C,MAAM,cAAc,GAA8B;YAChD,YAAY,EAAE,IAAI;YAClB,WAAW,EAAE,IAAI;YACjB,aAAa,EAAE,KAAK;SACrB,CAAC;QAEF,MAAM,OAAO,GAA8B;YACzC,GAAG,cAAc;YACjB,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;SAC/B,CAAC;QAEF,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;YAC1C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,iCAAiC;QACjC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,EAAE,CAAC;gBACnC,OAAO,KAAK,CAAC;YACf,CAAC;YACD,OAAO,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;QAC7C,CAAC;QAED,yBAAyB;QACzB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,EAAE,CAAC;gBAClC,OAAO,KAAK,CAAC;YACf,CAAC;YACD,OAAO,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QACrD,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;OAIG;IACK,uBAAuB,CAAC,KAAa;QAC3C,OAAO,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC;IACvE,CAAC;IAED;;;;;OAKG;IACK,sBAAsB,CAC5B,KAAa,EACb,OAAkC;QAElC,MAAM,eAAe,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;QAE5E,4BAA4B;QAC5B,IAAI,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5D,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa;gBACxC,CAAC,CAAC,OAAO,CAAC,YAAY;gBACtB,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;YAC3D,OAAO,YAAY,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;QAChD,CAAC;QAED,iCAAiC;QACjC,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QACpD,OAAO,cAAc,CAAC,QAAQ,CAAC,eAAgC,CAAC,CAAC;IACnE,CAAC;IAED;;;;OAIG;IACH,cAAc,CAAC,IAAyB;QACtC,MAAM,OAAO,GAA8B,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACrE,MAAM,cAAc,GAAG,EAAE,CAAC;QAE1B,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,EAAE,CAAC;YAClC,IAAI,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5D,cAAc,CAAC,IAAI,CAAC,WAAW,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACpE,CAAC;iBAAM,CAAC;gBACN,cAAc,CAAC,IAAI,CAAC,WAAW,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC5E,CAAC;QACH,CAAC;QAED,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,EAAE,CAAC;YACnC,cAAc,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QACnD,CAAC;QAED,OAAO,GAAG,IAAI,CAAC,QAAQ,YAAY,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;IACnE,CAAC;CACF,CAAA;AAnGY,8DAAyB;oCAAzB,yBAAyB;IADrC,IAAA,qCAAmB,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;GAClD,yBAAyB,CAmGrC;AAED;;;;;GAKG;AACH,SAAgB,eAAe,CAC7B,OAAmC,EACnC,iBAAqC;IAErC,OAAO,UAAU,MAAc,EAAE,YAAoB;QACnD,IAAA,mCAAiB,EAAC;YAChB,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,OAAO,EAAE,iBAAiB;YAC1B,WAAW,EAAE,CAAC,OAAO,CAAC;YACtB,SAAS,EAAE,yBAAyB;SACrC,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AACH,SAAgB,qBAAqB,CACnC,YAAuB,EACvB,iBAAqC;IAErC,OAAO,eAAe,CACpB;QACE,YAAY,EAAE,KAAK;QACnB,WAAW,EAAE,IAAI;QACjB,YAAY;QACZ,aAAa,EAAE,KAAK;KACrB,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACH,SAAgB,sBAAsB,CAAC,iBAAqC;IAC1E,OAAO,eAAe,CACpB;QACE,YAAY,EAAE,IAAI;QAClB,WAAW,EAAE,KAAK;KACnB,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACH,SAAgB,mBAAmB,CAAC,iBAAqC;IACvE,OAAO,eAAe,CACpB;QACE,YAAY,EAAE,IAAI;QAClB,WAAW,EAAE,IAAI;QACjB,aAAa,EAAE,KAAK;KACrB,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AACH,SAAgB,oBAAoB,CAClC,OAAmC,EACnC,iBAAqC;IAErC,OAAO,UAAU,MAAc,EAAE,YAAoB;QACnD,IAAA,mCAAiB,EAAC;YAChB,IAAI,EAAE,sBAAsB;YAC5B,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,OAAO,EAAE,iBAAiB;YAC1B,WAAW,EAAE,CAAC,OAAO,CAAC;YACtB,SAAS,EAAE;gBACT,QAAQ,CAAC,KAAU,EAAE,IAAyB;oBAC5C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;wBAC1B,OAAO,KAAK,CAAC;oBACf,CAAC;oBAED,MAAM,UAAU,GAAG,IAAI,yBAAyB,EAAE,CAAC;oBACnD,OAAO,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;gBAC9D,CAAC;gBACD,cAAc,CAAC,IAAyB;oBACtC,OAAO,GAAG,IAAI,CAAC,QAAQ,4CAA4C,CAAC;gBACtE,CAAC;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAa,kBAAkB;IAC7B;;;;OAIG;IACH,MAAM,CAAC,eAAe,CAAC,eAAuB;QAC5C,IAAI,eAAe,KAAK,CAAC,EAAE,CAAC;YAC1B,OAAO,aAAa,CAAC,IAAI,CAAC;QAC5B,CAAC;aAAM,IAAI,eAAe,IAAI,eAAe,CAAC,OAAO,IAAI,eAAe,IAAI,eAAe,CAAC,OAAO,EAAE,CAAC;YACpG,OAAO,aAAa,CAAC,GAAG,CAAC;QAC3B,CAAC;aAAM,IAAI,eAAe,IAAI,eAAe,CAAC,UAAU,IAAI,eAAe,IAAI,eAAe,CAAC,UAAU,EAAE,CAAC;YAC1G,OAAO,aAAa,CAAC,MAAM,CAAC;QAC9B,CAAC;aAAM,IAAI,eAAe,IAAI,eAAe,CAAC,QAAQ,IAAI,eAAe,IAAI,eAAe,CAAC,QAAQ,EAAE,CAAC;YACtG,OAAO,aAAa,CAAC,IAAI,CAAC;QAC5B,CAAC;aAAM,IAAI,eAAe,IAAI,eAAe,CAAC,YAAY,IAAI,eAAe,IAAI,eAAe,CAAC,YAAY,EAAE,CAAC;YAC9G,OAAO,aAAa,CAAC,QAAQ,CAAC;QAChC,CAAC;aAAM,CAAC;YACN,OAAO,aAAa,CAAC,aAAa,CAAC;QACrC,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,oBAAoB,CAAC,cAAsC;QAChE,MAAM,eAAe,GAAG,cAAc,CAAC,WAAW,EAAmB,CAAC;QAEtE,QAAQ,eAAe,EAAE,CAAC;YACxB,KAAK,aAAa,CAAC,IAAI;gBACrB,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAChB,KAAK,aAAa,CAAC,GAAG;gBACpB,OAAO,CAAC,eAAe,CAAC,OAAO,EAAE,eAAe,CAAC,OAAO,CAAC,CAAC;YAC5D,KAAK,aAAa,CAAC,MAAM;gBACvB,OAAO,CAAC,eAAe,CAAC,UAAU,EAAE,eAAe,CAAC,UAAU,CAAC,CAAC;YAClE,KAAK,aAAa,CAAC,IAAI;gBACrB,OAAO,CAAC,eAAe,CAAC,QAAQ,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC;YAC9D,KAAK,aAAa,CAAC,QAAQ;gBACzB,OAAO,CAAC,eAAe,CAAC,YAAY,EAAE,eAAe,CAAC,YAAY,CAAC,CAAC;YACtE,KAAK,aAAa,CAAC,aAAa,CAAC;YACjC;gBACE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,WAAW,CAAC,QAAyB;QAC1C,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACjC,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,MAAM,eAAe,GAAG,QAAQ,CAAC,WAAW,EAAmB,CAAC;QAChE,QAAQ,eAAe,EAAE,CAAC;YACxB,KAAK,aAAa,CAAC,QAAQ;gBACzB,OAAO,EAAE,CAAC;YACZ,KAAK,aAAa,CAAC,IAAI;gBACrB,OAAO,CAAC,CAAC;YACX,KAAK,aAAa,CAAC,MAAM;gBACvB,OAAO,CAAC,CAAC;YACX,KAAK,aAAa,CAAC,GAAG;gBACpB,OAAO,CAAC,CAAC;YACX,KAAK,aAAa,CAAC,aAAa;gBAC9B,OAAO,CAAC,CAAC;YACX,KAAK,aAAa,CAAC,IAAI,CAAC;YACxB;gBACE,OAAO,CAAC,CAAC;QACb,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,OAAO,CAAC,SAA0B,EAAE,SAA0B;QACnE,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QAE9C,IAAI,SAAS,GAAG,SAAS;YAAE,OAAO,CAAC,CAAC,CAAC;QACrC,IAAI,SAAS,GAAG,SAAS;YAAE,OAAO,CAAC,CAAC;QACpC,OAAO,CAAC,CAAC;IACX,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,cAAc,CAAC,UAA+B;QACnD,OAAO,CAAC,GAAG,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC5D,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,OAAO,CAAC,QAAa,EAAE,OAAmC;QAC/D,MAAM,UAAU,GAAG,IAAI,yBAAyB,EAAE,CAAC;QACnD,OAAO,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,WAAW,EAAE,CAAC,OAAO,CAAC,EAAyB,CAAC,CAAC;IAC1F,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,SAAS,CAAC,QAAyB;QACxC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACjC,OAAO,QAAQ,CAAC,WAAW,EAAE,CAAC;QAChC,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,YAAY,CAAC,QAAyB;QAC3C,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAE5C,IAAI,QAAQ,IAAI,CAAC;YAAE,OAAO,SAAS,CAAC,CAAC,iBAAiB;QACtD,IAAI,QAAQ,IAAI,CAAC;YAAE,OAAO,SAAS,CAAC,CAAC,gBAAgB;QACrD,IAAI,QAAQ,IAAI,CAAC;YAAE,OAAO,SAAS,CAAC,CAAC,kBAAkB;QACvD,IAAI,QAAQ,IAAI,CAAC;YAAE,OAAO,SAAS,CAAC,CAAC,cAAc;QACnD,OAAO,SAAS,CAAC,CAAC,mBAAmB;IACvC,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,cAAc,CAAC,QAAyB;QAC7C,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACjC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAC5C,CAAC;QAED,MAAM,eAAe,GAAG,QAAQ,CAAC,WAAW,EAAmB,CAAC;QAChE,QAAQ,eAAe,EAAE,CAAC;YACxB,KAAK,aAAa,CAAC,QAAQ;gBACzB,OAAO,2DAA2D,CAAC;YACrE,KAAK,aAAa,CAAC,IAAI;gBACrB,OAAO,6DAA6D,CAAC;YACvE,KAAK,aAAa,CAAC,MAAM;gBACvB,OAAO,yEAAyE,CAAC;YACnF,KAAK,aAAa,CAAC,GAAG;gBACpB,OAAO,gEAAgE,CAAC;YAC1E,KAAK,aAAa,CAAC,aAAa;gBAC9B,OAAO,gDAAgD,CAAC;YAC1D,KAAK,aAAa,CAAC,IAAI,CAAC;YACxB;gBACE,OAAO,+BAA+B,CAAC;QAC3C,CAAC;IACH,CAAC;CACF;AAzKD,gDAyKC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\validation\\validators\\is-severity-level.validator.ts"], "sourcesContent": ["import {\r\n  registerDecorator,\r\n  ValidationOptions,\r\n  ValidatorConstraint,\r\n  ValidatorConstraintInterface,\r\n  ValidationArguments,\r\n} from 'class-validator';\r\n\r\n/**\r\n * Severity level enumeration\r\n * Based on CVSS and industry standards\r\n */\r\nexport enum SeverityLevel {\r\n  CRITICAL = 'critical',\r\n  HIGH = 'high',\r\n  MEDIUM = 'medium',\r\n  LOW = 'low',\r\n  INFORMATIONAL = 'informational',\r\n  NONE = 'none',\r\n}\r\n\r\n/**\r\n * Numeric severity scale (0-10)\r\n * Aligned with CVSS scoring\r\n */\r\nexport enum NumericSeverity {\r\n  NONE = 0,\r\n  LOW_MIN = 0.1,\r\n  LOW_MAX = 3.9,\r\n  MEDIUM_MIN = 4.0,\r\n  MEDIUM_MAX = 6.9,\r\n  HIGH_MIN = 7.0,\r\n  HIGH_MAX = 8.9,\r\n  CRITICAL_MIN = 9.0,\r\n  CRITICAL_MAX = 10.0,\r\n}\r\n\r\n/**\r\n * Severity level validation options\r\n */\r\nexport interface SeverityValidationOptions {\r\n  /** Allow numeric severity values (0-10) */\r\n  allowNumeric?: boolean;\r\n  /** Allow string severity levels */\r\n  allowString?: boolean;\r\n  /** Custom allowed severity levels */\r\n  customLevels?: string[];\r\n  /** Case sensitive validation */\r\n  caseSensitive?: boolean;\r\n}\r\n\r\n/**\r\n * Severity level validator constraint\r\n * Validates severity levels in both string and numeric formats\r\n */\r\n@ValidatorConstraint({ name: 'isSeverityLevel', async: false })\r\nexport class IsSeverityLevelConstraint implements ValidatorConstraintInterface {\r\n  /**\r\n   * Validate severity level\r\n   * @param value Value to validate\r\n   * @param args Validation arguments\r\n   * @returns boolean indicating if value is valid severity level\r\n   */\r\n  validate(value: any, args: ValidationArguments): boolean {\r\n    const defaultOptions: SeverityValidationOptions = {\r\n      allowNumeric: true,\r\n      allowString: true,\r\n      caseSensitive: false,\r\n    };\r\n    \r\n    const options: SeverityValidationOptions = {\r\n      ...defaultOptions,\r\n      ...(args.constraints[0] || {}),\r\n    };\r\n\r\n    if (value === null || value === undefined) {\r\n      return false;\r\n    }\r\n\r\n    // Handle numeric severity (0-10)\r\n    if (typeof value === 'number') {\r\n      if (options.allowNumeric === false) {\r\n        return false;\r\n      }\r\n      return this.validateNumericSeverity(value);\r\n    }\r\n\r\n    // Handle string severity\r\n    if (typeof value === 'string') {\r\n      if (options.allowString === false) {\r\n        return false;\r\n      }\r\n      return this.validateStringSeverity(value, options);\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Validate numeric severity (0-10 scale)\r\n   * @param value Numeric value to validate\r\n   * @returns boolean indicating validity\r\n   */\r\n  private validateNumericSeverity(value: number): boolean {\r\n    return value >= 0 && value <= 10 && !isNaN(value) && isFinite(value);\r\n  }\r\n\r\n  /**\r\n   * Validate string severity level\r\n   * @param value String value to validate\r\n   * @param options Validation options\r\n   * @returns boolean indicating validity\r\n   */\r\n  private validateStringSeverity(\r\n    value: string,\r\n    options: SeverityValidationOptions,\r\n  ): boolean {\r\n    const normalizedValue = options.caseSensitive ? value : value.toLowerCase();\r\n\r\n    // Check custom levels first\r\n    if (options.customLevels && options.customLevels.length > 0) {\r\n      const customLevels = options.caseSensitive\r\n        ? options.customLevels\r\n        : options.customLevels.map(level => level.toLowerCase());\r\n      return customLevels.includes(normalizedValue);\r\n    }\r\n\r\n    // Check standard severity levels\r\n    const standardLevels = Object.values(SeverityLevel);\r\n    return standardLevels.includes(normalizedValue as SeverityLevel);\r\n  }\r\n\r\n  /**\r\n   * Default error message for severity level validation\r\n   * @param args Validation arguments\r\n   * @returns Error message\r\n   */\r\n  defaultMessage(args: ValidationArguments): string {\r\n    const options: SeverityValidationOptions = args.constraints[0] || {};\r\n    const allowedFormats = [];\r\n\r\n    if (options.allowString !== false) {\r\n      if (options.customLevels && options.customLevels.length > 0) {\r\n        allowedFormats.push(`one of: ${options.customLevels.join(', ')}`);\r\n      } else {\r\n        allowedFormats.push(`one of: ${Object.values(SeverityLevel).join(', ')}`);\r\n      }\r\n    }\r\n\r\n    if (options.allowNumeric !== false) {\r\n      allowedFormats.push('a number between 0 and 10');\r\n    }\r\n\r\n    return `${args.property} must be ${allowedFormats.join(' or ')}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Severity level validation decorator\r\n * @param options Severity validation options\r\n * @param validationOptions Validation options\r\n * @returns Property decorator\r\n */\r\nexport function IsSeverityLevel(\r\n  options?: SeverityValidationOptions,\r\n  validationOptions?: ValidationOptions,\r\n) {\r\n  return function (object: Object, propertyName: string) {\r\n    registerDecorator({\r\n      target: object.constructor,\r\n      propertyName: propertyName,\r\n      options: validationOptions,\r\n      constraints: [options],\r\n      validator: IsSeverityLevelConstraint,\r\n    });\r\n  };\r\n}\r\n\r\n/**\r\n * String severity level validation decorator\r\n * @param customLevels Custom allowed severity levels\r\n * @param validationOptions Validation options\r\n * @returns Property decorator\r\n */\r\nexport function IsStringSeverityLevel(\r\n  customLevels?: string[],\r\n  validationOptions?: ValidationOptions,\r\n) {\r\n  return IsSeverityLevel(\r\n    {\r\n      allowNumeric: false,\r\n      allowString: true,\r\n      customLevels,\r\n      caseSensitive: false,\r\n    },\r\n    validationOptions,\r\n  );\r\n}\r\n\r\n/**\r\n * Numeric severity level validation decorator\r\n * @param validationOptions Validation options\r\n * @returns Property decorator\r\n */\r\nexport function IsNumericSeverityLevel(validationOptions?: ValidationOptions) {\r\n  return IsSeverityLevel(\r\n    {\r\n      allowNumeric: true,\r\n      allowString: false,\r\n    },\r\n    validationOptions,\r\n  );\r\n}\r\n\r\n/**\r\n * CVSS severity level validation decorator\r\n * @param validationOptions Validation options\r\n * @returns Property decorator\r\n */\r\nexport function IsCvssSeverityLevel(validationOptions?: ValidationOptions) {\r\n  return IsSeverityLevel(\r\n    {\r\n      allowNumeric: true,\r\n      allowString: true,\r\n      caseSensitive: false,\r\n    },\r\n    validationOptions,\r\n  );\r\n}\r\n\r\n/**\r\n * Severity level array validation decorator\r\n * @param options Severity validation options\r\n * @param validationOptions Validation options\r\n * @returns Property decorator\r\n */\r\nexport function IsSeverityLevelArray(\r\n  options?: SeverityValidationOptions,\r\n  validationOptions?: ValidationOptions,\r\n) {\r\n  return function (object: Object, propertyName: string) {\r\n    registerDecorator({\r\n      name: 'isSeverityLevelArray',\r\n      target: object.constructor,\r\n      propertyName: propertyName,\r\n      options: validationOptions,\r\n      constraints: [options],\r\n      validator: {\r\n        validate(value: any, args: ValidationArguments) {\r\n          if (!Array.isArray(value)) {\r\n            return false;\r\n          }\r\n\r\n          const constraint = new IsSeverityLevelConstraint();\r\n          return value.every(item => constraint.validate(item, args));\r\n        },\r\n        defaultMessage(args: ValidationArguments) {\r\n          return `${args.property} must be an array of valid severity levels`;\r\n        },\r\n      },\r\n    });\r\n  };\r\n}\r\n\r\n/**\r\n * Utility functions for severity level handling\r\n */\r\nexport class SeverityLevelUtils {\r\n  /**\r\n   * Convert numeric severity to string level\r\n   * @param numericSeverity Numeric severity (0-10)\r\n   * @returns String severity level\r\n   */\r\n  static numericToString(numericSeverity: number): SeverityLevel {\r\n    if (numericSeverity === 0) {\r\n      return SeverityLevel.NONE;\r\n    } else if (numericSeverity >= NumericSeverity.LOW_MIN && numericSeverity <= NumericSeverity.LOW_MAX) {\r\n      return SeverityLevel.LOW;\r\n    } else if (numericSeverity >= NumericSeverity.MEDIUM_MIN && numericSeverity <= NumericSeverity.MEDIUM_MAX) {\r\n      return SeverityLevel.MEDIUM;\r\n    } else if (numericSeverity >= NumericSeverity.HIGH_MIN && numericSeverity <= NumericSeverity.HIGH_MAX) {\r\n      return SeverityLevel.HIGH;\r\n    } else if (numericSeverity >= NumericSeverity.CRITICAL_MIN && numericSeverity <= NumericSeverity.CRITICAL_MAX) {\r\n      return SeverityLevel.CRITICAL;\r\n    } else {\r\n      return SeverityLevel.INFORMATIONAL;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Convert string severity to numeric range\r\n   * @param stringSeverity String severity level\r\n   * @returns Numeric severity range [min, max]\r\n   */\r\n  static stringToNumericRange(stringSeverity: string | SeverityLevel): [number, number] {\r\n    const normalizedLevel = stringSeverity.toLowerCase() as SeverityLevel;\r\n\r\n    switch (normalizedLevel) {\r\n      case SeverityLevel.NONE:\r\n        return [0, 0];\r\n      case SeverityLevel.LOW:\r\n        return [NumericSeverity.LOW_MIN, NumericSeverity.LOW_MAX];\r\n      case SeverityLevel.MEDIUM:\r\n        return [NumericSeverity.MEDIUM_MIN, NumericSeverity.MEDIUM_MAX];\r\n      case SeverityLevel.HIGH:\r\n        return [NumericSeverity.HIGH_MIN, NumericSeverity.HIGH_MAX];\r\n      case SeverityLevel.CRITICAL:\r\n        return [NumericSeverity.CRITICAL_MIN, NumericSeverity.CRITICAL_MAX];\r\n      case SeverityLevel.INFORMATIONAL:\r\n      default:\r\n        return [0, 0];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get severity level priority (higher number = higher priority)\r\n   * @param severity Severity level\r\n   * @returns Priority number\r\n   */\r\n  static getPriority(severity: string | number): number {\r\n    if (typeof severity === 'number') {\r\n      return severity;\r\n    }\r\n\r\n    const normalizedLevel = severity.toLowerCase() as SeverityLevel;\r\n    switch (normalizedLevel) {\r\n      case SeverityLevel.CRITICAL:\r\n        return 10;\r\n      case SeverityLevel.HIGH:\r\n        return 8;\r\n      case SeverityLevel.MEDIUM:\r\n        return 5;\r\n      case SeverityLevel.LOW:\r\n        return 2;\r\n      case SeverityLevel.INFORMATIONAL:\r\n        return 1;\r\n      case SeverityLevel.NONE:\r\n      default:\r\n        return 0;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Compare two severity levels\r\n   * @param severity1 First severity level\r\n   * @param severity2 Second severity level\r\n   * @returns Comparison result (-1, 0, 1)\r\n   */\r\n  static compare(severity1: string | number, severity2: string | number): number {\r\n    const priority1 = this.getPriority(severity1);\r\n    const priority2 = this.getPriority(severity2);\r\n\r\n    if (priority1 < priority2) return -1;\r\n    if (priority1 > priority2) return 1;\r\n    return 0;\r\n  }\r\n\r\n  /**\r\n   * Sort severity levels by priority (highest first)\r\n   * @param severities Array of severity levels\r\n   * @returns Sorted array\r\n   */\r\n  static sortByPriority(severities: (string | number)[]): (string | number)[] {\r\n    return [...severities].sort((a, b) => this.compare(b, a));\r\n  }\r\n\r\n  /**\r\n   * Validate severity level without decorator\r\n   * @param severity Severity level to validate\r\n   * @param options Validation options\r\n   * @returns boolean indicating validity\r\n   */\r\n  static isValid(severity: any, options?: SeverityValidationOptions): boolean {\r\n    const constraint = new IsSeverityLevelConstraint();\r\n    return constraint.validate(severity, { constraints: [options] } as ValidationArguments);\r\n  }\r\n\r\n  /**\r\n   * Normalize severity level format\r\n   * @param severity Severity level\r\n   * @returns Normalized severity level or null if invalid\r\n   */\r\n  static normalize(severity: string | number): string | number | null {\r\n    if (!this.isValid(severity)) {\r\n      return null;\r\n    }\r\n\r\n    if (typeof severity === 'string') {\r\n      return severity.toLowerCase();\r\n    }\r\n\r\n    return severity;\r\n  }\r\n\r\n  /**\r\n   * Get color code for severity level (for UI display)\r\n   * @param severity Severity level\r\n   * @returns Color code\r\n   */\r\n  static getColorCode(severity: string | number): string {\r\n    const priority = this.getPriority(severity);\r\n\r\n    if (priority >= 9) return '#dc3545'; // Critical - Red\r\n    if (priority >= 7) return '#fd7e14'; // High - Orange\r\n    if (priority >= 4) return '#ffc107'; // Medium - Yellow\r\n    if (priority >= 1) return '#28a745'; // Low - Green\r\n    return '#6c757d'; // None/Info - Gray\r\n  }\r\n\r\n  /**\r\n   * Get severity level description\r\n   * @param severity Severity level\r\n   * @returns Human-readable description\r\n   */\r\n  static getDescription(severity: string | number): string {\r\n    if (typeof severity === 'number') {\r\n      severity = this.numericToString(severity);\r\n    }\r\n\r\n    const normalizedLevel = severity.toLowerCase() as SeverityLevel;\r\n    switch (normalizedLevel) {\r\n      case SeverityLevel.CRITICAL:\r\n        return 'Critical vulnerabilities that require immediate attention';\r\n      case SeverityLevel.HIGH:\r\n        return 'High-risk vulnerabilities that should be addressed promptly';\r\n      case SeverityLevel.MEDIUM:\r\n        return 'Medium-risk vulnerabilities that should be addressed in a timely manner';\r\n      case SeverityLevel.LOW:\r\n        return 'Low-risk vulnerabilities that can be addressed as time permits';\r\n      case SeverityLevel.INFORMATIONAL:\r\n        return 'Informational findings that may be of interest';\r\n      case SeverityLevel.NONE:\r\n      default:\r\n        return 'No security impact identified';\r\n    }\r\n  }\r\n}"], "version": 3}