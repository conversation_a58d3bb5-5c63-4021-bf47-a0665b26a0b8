a2ea7fb7224bfc9eaccc170a6ac25d9a
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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