4f63084064851618d51e822c225751c2
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vulnerability_specification_1 = require("../vulnerability.specification");
const vulnerability_entity_1 = require("../../entities/vulnerability/vulnerability.entity");
const threat_severity_enum_1 = require("../../enums/threat-severity.enum");
const confidence_level_enum_1 = require("../../enums/confidence-level.enum");
const cvss_score_value_object_1 = require("../../value-objects/threat-indicators/cvss-score.value-object");
describe('VulnerabilitySpecification', () => {
    let mockAffectedAssets;
    let mockDiscovery;
    let criticalVulnerability;
    let highVulnerability;
    let mediumVulnerability;
    let lowVulnerability;
    beforeEach(() => {
        mockAffectedAssets = [{
                assetId: 'asset-1',
                assetName: 'Web Server 01',
                assetType: 'server',
                criticality: 'critical',
                affectedComponents: [{
                        name: 'Apache HTTP Server',
                        version: '2.4.41',
                        type: 'software',
                    }],
                exposure: 'external',
                businessImpact: ['customer_facing_service'],
            }];
        // Use a recent date to avoid issues with stale/overdue tests
        const recentDate = new Date();
        recentDate.setDate(recentDate.getDate() - 1); // 1 day ago
        mockDiscovery = {
            method: 'automated_scan',
            source: 'Nessus Scanner',
            discoveredAt: recentDate,
            discoveredBy: 'Security Team',
            details: 'Discovered during routine vulnerability scan',
        };
        // Create test vulnerabilities with different severities
        criticalVulnerability = vulnerability_entity_1.Vulnerability.create('Critical RCE Vulnerability', 'Remote code execution vulnerability', threat_severity_enum_1.ThreatSeverity.CRITICAL, 'remote_code_execution', 'buffer_overflow', mockAffectedAssets, mockDiscovery, {
            cveId: 'CVE-2024-1234',
            cvssScores: [cvss_score_value_object_1.CVSSScore.createV3_1(9.8, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H')],
            confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
            tags: ['critical', 'rce', 'external'],
        });
        highVulnerability = vulnerability_entity_1.Vulnerability.create('High Severity SQL Injection', 'SQL injection vulnerability in login form', threat_severity_enum_1.ThreatSeverity.HIGH, 'injection', 'sql_injection', [{
                ...mockAffectedAssets[0],
                criticality: 'high',
                exposure: 'internal',
            }], mockDiscovery, {
            cveId: 'CVE-2024-5678',
            cvssScores: [cvss_score_value_object_1.CVSSScore.createV3_1(8.1, 'CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:N')],
            confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
            tags: ['high', 'injection'],
        });
        mediumVulnerability = vulnerability_entity_1.Vulnerability.create('Medium XSS Vulnerability', 'Cross-site scripting vulnerability', threat_severity_enum_1.ThreatSeverity.MEDIUM, 'injection', 'xss', [{
                ...mockAffectedAssets[0],
                criticality: 'medium',
                exposure: 'internal',
            }], mockDiscovery, {
            cvssScores: [cvss_score_value_object_1.CVSSScore.createV3_1(6.1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N')],
            confidence: confidence_level_enum_1.ConfidenceLevel.MEDIUM,
            tags: ['medium', 'xss'],
        });
        lowVulnerability = vulnerability_entity_1.Vulnerability.create('Low Information Disclosure', 'Information disclosure vulnerability', threat_severity_enum_1.ThreatSeverity.LOW, 'information_disclosure', 'info_leak', [{
                ...mockAffectedAssets[0],
                criticality: 'low',
                exposure: 'internal',
            }], mockDiscovery, {
            cvssScores: [cvss_score_value_object_1.CVSSScore.createV3_1(3.7, 'CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:L/I:N/A:N')],
            confidence: confidence_level_enum_1.ConfidenceLevel.LOW,
            tags: ['low', 'info-disclosure'],
        });
    });
    describe('CriticalVulnerabilitySpecification', () => {
        let spec;
        beforeEach(() => {
            spec = new vulnerability_specification_1.CriticalVulnerabilitySpecification();
        });
        it('should identify critical vulnerabilities', () => {
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true);
            expect(spec.isSatisfiedBy(highVulnerability)).toBe(false);
            expect(spec.isSatisfiedBy(mediumVulnerability)).toBe(false);
            expect(spec.isSatisfiedBy(lowVulnerability)).toBe(false);
        });
        it('should provide correct description', () => {
            expect(spec.getDescription()).toBe('Vulnerability has critical severity');
        });
    });
    describe('HighSeverityVulnerabilitySpecification', () => {
        let spec;
        beforeEach(() => {
            spec = new vulnerability_specification_1.HighSeverityVulnerabilitySpecification();
        });
        it('should identify high and critical vulnerabilities', () => {
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true);
            expect(spec.isSatisfiedBy(highVulnerability)).toBe(true);
            expect(spec.isSatisfiedBy(mediumVulnerability)).toBe(false);
            expect(spec.isSatisfiedBy(lowVulnerability)).toBe(false);
        });
        it('should provide correct description', () => {
            expect(spec.getDescription()).toBe('Vulnerability has high or critical severity');
        });
    });
    describe('ActiveVulnerabilitySpecification', () => {
        let spec;
        beforeEach(() => {
            spec = new vulnerability_specification_1.ActiveVulnerabilitySpecification();
        });
        it('should identify active vulnerabilities', () => {
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true);
            // Change status to remediated
            criticalVulnerability.changeStatus(vulnerability_entity_1.VulnerabilityStatus.REMEDIATED, 'Fixed by patch');
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(false);
            // Change status to false positive
            criticalVulnerability.changeStatus(vulnerability_entity_1.VulnerabilityStatus.FALSE_POSITIVE, 'Not a real vulnerability');
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(false);
        });
        it('should provide correct description', () => {
            expect(spec.getDescription()).toBe('Vulnerability is active (not remediated, closed, or false positive)');
        });
    });
    describe('RemediatedVulnerabilitySpecification', () => {
        let spec;
        beforeEach(() => {
            spec = new vulnerability_specification_1.RemediatedVulnerabilitySpecification();
        });
        it('should identify remediated vulnerabilities', () => {
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(false);
            criticalVulnerability.changeStatus(vulnerability_entity_1.VulnerabilityStatus.REMEDIATED, 'Fixed by patch');
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true);
            criticalVulnerability.changeStatus(vulnerability_entity_1.VulnerabilityStatus.VERIFIED, 'Verified as fixed');
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true);
            criticalVulnerability.changeStatus(vulnerability_entity_1.VulnerabilityStatus.CLOSED, 'Closed');
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true);
        });
    });
    describe('HighRiskVulnerabilitySpecification', () => {
        let spec;
        beforeEach(() => {
            spec = new vulnerability_specification_1.HighRiskVulnerabilitySpecification(80);
        });
        it('should identify high risk vulnerabilities', () => {
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // Critical should have high risk score
            expect(spec.isSatisfiedBy(lowVulnerability)).toBe(false);
        });
        it('should use custom risk threshold', () => {
            const customSpec = new vulnerability_specification_1.HighRiskVulnerabilitySpecification(50);
            expect(customSpec.getDescription()).toBe('Vulnerability has high risk score (>= 50)');
        });
    });
    describe('HighConfidenceVulnerabilitySpecification', () => {
        let spec;
        beforeEach(() => {
            spec = new vulnerability_specification_1.HighConfidenceVulnerabilitySpecification();
        });
        it('should identify high confidence vulnerabilities', () => {
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // HIGH confidence
            expect(spec.isSatisfiedBy(highVulnerability)).toBe(true); // HIGH confidence
            expect(spec.isSatisfiedBy(mediumVulnerability)).toBe(false); // MEDIUM confidence
            expect(spec.isSatisfiedBy(lowVulnerability)).toBe(false); // LOW confidence
        });
    });
    describe('RecentVulnerabilitySpecification', () => {
        let spec;
        beforeEach(() => {
            spec = new vulnerability_specification_1.RecentVulnerabilitySpecification(7); // Within 7 days
        });
        it('should identify recent vulnerabilities', () => {
            // Create a vulnerability discovered today
            const recentVulnerability = vulnerability_entity_1.Vulnerability.create('Recent Vulnerability', 'Recently discovered vulnerability', threat_severity_enum_1.ThreatSeverity.MEDIUM, 'general', 'unknown', mockAffectedAssets, {
                ...mockDiscovery,
                discoveredAt: new Date(), // Today
            });
            expect(spec.isSatisfiedBy(recentVulnerability)).toBe(true);
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // Also recent (1 day ago)
        });
        it('should provide correct description', () => {
            expect(spec.getDescription()).toBe('Vulnerability was discovered within 7 days');
        });
    });
    describe('StaleVulnerabilitySpecification', () => {
        let spec;
        beforeEach(() => {
            spec = new vulnerability_specification_1.StaleVulnerabilitySpecification(30); // Older than 30 days
        });
        it('should identify stale vulnerabilities', () => {
            // Create an old vulnerability
            const oldVulnerability = vulnerability_entity_1.Vulnerability.create('Old Vulnerability', 'Old vulnerability', threat_severity_enum_1.ThreatSeverity.MEDIUM, 'general', 'unknown', mockAffectedAssets, {
                ...mockDiscovery,
                discoveredAt: new Date('2023-01-01T00:00:00Z'), // Very old
            });
            expect(spec.isSatisfiedBy(oldVulnerability)).toBe(true);
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(false);
        });
    });
    describe('CVEVulnerabilitySpecification', () => {
        it('should identify vulnerabilities with CVE IDs', () => {
            const spec = new vulnerability_specification_1.CVEVulnerabilitySpecification();
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // Has CVE-2024-1234
            expect(spec.isSatisfiedBy(mediumVulnerability)).toBe(false); // No CVE ID
        });
        it('should identify specific CVE', () => {
            const spec = new vulnerability_specification_1.CVEVulnerabilitySpecification('CVE-2024-1234');
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true);
            expect(spec.isSatisfiedBy(highVulnerability)).toBe(false); // Different CVE
        });
    });
    describe('ZeroDayVulnerabilitySpecification', () => {
        let spec;
        beforeEach(() => {
            spec = new vulnerability_specification_1.ZeroDayVulnerabilitySpecification();
        });
        it('should identify zero-day vulnerabilities', () => {
            expect(spec.isSatisfiedBy(mediumVulnerability)).toBe(true); // No CVE ID
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(false); // Has CVE ID
            // Create vulnerability with zero-day tag
            const zeroDayVuln = vulnerability_entity_1.Vulnerability.create('Zero Day', 'Zero day vulnerability', threat_severity_enum_1.ThreatSeverity.CRITICAL, 'zero-day', 'unknown', mockAffectedAssets, mockDiscovery, {
                cveId: 'CVE-2024-9999',
                tags: ['zero-day'],
            });
            expect(spec.isSatisfiedBy(zeroDayVuln)).toBe(true); // Has zero-day tag
        });
    });
    describe('ActivelyExploitedVulnerabilitySpecification', () => {
        let spec;
        beforeEach(() => {
            spec = new vulnerability_specification_1.ActivelyExploitedVulnerabilitySpecification();
        });
        it('should identify actively exploited vulnerabilities', () => {
            // Create vulnerability with active exploitation
            const exploitedVuln = vulnerability_entity_1.Vulnerability.create('Exploited Vulnerability', 'Actively exploited vulnerability', threat_severity_enum_1.ThreatSeverity.CRITICAL, 'exploitation', 'active', mockAffectedAssets, mockDiscovery, {
                exploitation: {
                    status: 'active_exploitation',
                    difficulty: 'low',
                    availableExploits: [],
                    attackVectors: [],
                    prerequisites: [],
                },
            });
            expect(spec.isSatisfiedBy(exploitedVuln)).toBe(true);
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(false);
        });
    });
    describe('ExternallyExposedVulnerabilitySpecification', () => {
        let spec;
        beforeEach(() => {
            spec = new vulnerability_specification_1.ExternallyExposedVulnerabilitySpecification();
        });
        it('should identify externally exposed vulnerabilities', () => {
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // External exposure
            expect(spec.isSatisfiedBy(highVulnerability)).toBe(false); // Internal exposure
        });
    });
    describe('CriticalAssetVulnerabilitySpecification', () => {
        let spec;
        beforeEach(() => {
            spec = new vulnerability_specification_1.CriticalAssetVulnerabilitySpecification();
        });
        it('should identify vulnerabilities affecting critical assets', () => {
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // Critical asset
            expect(spec.isSatisfiedBy(highVulnerability)).toBe(false); // High criticality asset
        });
    });
    describe('HighCVSSVulnerabilitySpecification', () => {
        let spec;
        beforeEach(() => {
            spec = new vulnerability_specification_1.HighCVSSVulnerabilitySpecification(7.0);
        });
        it('should identify vulnerabilities with high CVSS scores', () => {
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // CVSS 9.8
            expect(spec.isSatisfiedBy(highVulnerability)).toBe(true); // CVSS 8.1
            expect(spec.isSatisfiedBy(mediumVulnerability)).toBe(false); // CVSS 6.1
            expect(spec.isSatisfiedBy(lowVulnerability)).toBe(false); // CVSS 3.7
        });
    });
    describe('OverdueRemediationSpecification', () => {
        let spec;
        beforeEach(() => {
            spec = new vulnerability_specification_1.OverdueRemediationSpecification();
        });
        it('should identify overdue vulnerabilities', () => {
            // Create an old critical vulnerability that should be overdue
            const oldCriticalVuln = vulnerability_entity_1.Vulnerability.create('Old Critical Vulnerability', 'Old critical vulnerability', threat_severity_enum_1.ThreatSeverity.CRITICAL, 'critical', 'old', mockAffectedAssets, {
                ...mockDiscovery,
                discoveredAt: new Date('2023-01-01T00:00:00Z'), // Very old
            });
            expect(spec.isSatisfiedBy(oldCriticalVuln)).toBe(true);
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(false); // Recent critical vulnerability (1 day old) is not yet overdue
        });
    });
    describe('RequiresImmediateAttentionSpecification', () => {
        let spec;
        beforeEach(() => {
            spec = new vulnerability_specification_1.RequiresImmediateAttentionSpecification();
        });
        it('should identify vulnerabilities requiring immediate attention', () => {
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true);
            expect(spec.isSatisfiedBy(lowVulnerability)).toBe(false);
        });
    });
    describe('VulnerabilitySeveritySpecification', () => {
        let spec;
        beforeEach(() => {
            spec = new vulnerability_specification_1.VulnerabilitySeveritySpecification([threat_severity_enum_1.ThreatSeverity.CRITICAL, threat_severity_enum_1.ThreatSeverity.HIGH]);
        });
        it('should identify vulnerabilities with specified severities', () => {
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true);
            expect(spec.isSatisfiedBy(highVulnerability)).toBe(true);
            expect(spec.isSatisfiedBy(mediumVulnerability)).toBe(false);
            expect(spec.isSatisfiedBy(lowVulnerability)).toBe(false);
        });
        it('should provide correct description', () => {
            expect(spec.getDescription()).toBe('Vulnerability severity is one of: critical, high');
        });
    });
    describe('VulnerabilityStatusSpecification', () => {
        let spec;
        beforeEach(() => {
            spec = new vulnerability_specification_1.VulnerabilityStatusSpecification([vulnerability_entity_1.VulnerabilityStatus.DISCOVERED, vulnerability_entity_1.VulnerabilityStatus.CONFIRMED]);
        });
        it('should identify vulnerabilities with specified statuses', () => {
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // DISCOVERED status
            criticalVulnerability.changeStatus(vulnerability_entity_1.VulnerabilityStatus.CONFIRMED, 'Confirmed');
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true);
            criticalVulnerability.changeStatus(vulnerability_entity_1.VulnerabilityStatus.REMEDIATED, 'Fixed');
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(false);
        });
    });
    describe('VulnerabilityCategorySpecification', () => {
        let spec;
        beforeEach(() => {
            spec = new vulnerability_specification_1.VulnerabilityCategorySpecification(['injection', 'remote_code_execution']);
        });
        it('should identify vulnerabilities with specified categories', () => {
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // remote_code_execution
            expect(spec.isSatisfiedBy(highVulnerability)).toBe(true); // injection
            expect(spec.isSatisfiedBy(lowVulnerability)).toBe(false); // information_disclosure
        });
    });
    describe('VulnerabilityTypeSpecification', () => {
        let spec;
        beforeEach(() => {
            spec = new vulnerability_specification_1.VulnerabilityTypeSpecification(['buffer_overflow', 'sql_injection']);
        });
        it('should identify vulnerabilities with specified types', () => {
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // buffer_overflow
            expect(spec.isSatisfiedBy(highVulnerability)).toBe(true); // sql_injection
            expect(spec.isSatisfiedBy(mediumVulnerability)).toBe(false); // xss
        });
    });
    describe('VulnerabilityTagSpecification', () => {
        it('should identify vulnerabilities with any of the specified tags', () => {
            const spec = new vulnerability_specification_1.VulnerabilityTagSpecification(['critical', 'high'], false);
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // Has 'critical' tag
            expect(spec.isSatisfiedBy(highVulnerability)).toBe(true); // Has 'high' tag
            expect(spec.isSatisfiedBy(mediumVulnerability)).toBe(false); // Has neither tag
        });
        it('should identify vulnerabilities with all specified tags', () => {
            const spec = new vulnerability_specification_1.VulnerabilityTagSpecification(['critical', 'rce'], true);
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // Has both tags
            expect(spec.isSatisfiedBy(highVulnerability)).toBe(false); // Missing 'critical' tag
        });
    });
    describe('VulnerabilityRiskScoreRangeSpecification', () => {
        let spec;
        beforeEach(() => {
            spec = new vulnerability_specification_1.VulnerabilityRiskScoreRangeSpecification(70, 100);
        });
        it('should identify vulnerabilities within risk score range', () => {
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // High risk score
            expect(spec.isSatisfiedBy(lowVulnerability)).toBe(false); // Low risk score
        });
        it('should handle min-only range', () => {
            const minOnlySpec = new vulnerability_specification_1.VulnerabilityRiskScoreRangeSpecification(80, undefined);
            expect(minOnlySpec.getDescription()).toBe('Vulnerability risk score is at least 80');
        });
        it('should handle max-only range', () => {
            const maxOnlySpec = new vulnerability_specification_1.VulnerabilityRiskScoreRangeSpecification(undefined, 50);
            expect(maxOnlySpec.getDescription()).toBe('Vulnerability risk score is at most 50');
        });
    });
    describe('VulnerabilityCVSSScoreRangeSpecification', () => {
        let spec;
        beforeEach(() => {
            spec = new vulnerability_specification_1.VulnerabilityCVSSScoreRangeSpecification(7.0, 10.0);
        });
        it('should identify vulnerabilities within CVSS score range', () => {
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // CVSS 9.8
            expect(spec.isSatisfiedBy(highVulnerability)).toBe(true); // CVSS 8.1
            expect(spec.isSatisfiedBy(mediumVulnerability)).toBe(false); // CVSS 6.1
        });
    });
    describe('VulnerabilityAgeRangeSpecification', () => {
        let spec;
        beforeEach(() => {
            spec = new vulnerability_specification_1.VulnerabilityAgeRangeSpecification(1, 30); // 1-30 days old
        });
        it('should identify vulnerabilities within age range', () => {
            // Create a vulnerability with a specific discovery date for predictable testing
            const specificDate = new Date('2024-01-15T00:00:00Z');
            const testVuln = vulnerability_entity_1.Vulnerability.create('Test Vulnerability', 'Test vulnerability for age testing', threat_severity_enum_1.ThreatSeverity.MEDIUM, 'general', 'test', mockAffectedAssets, {
                ...mockDiscovery,
                discoveredAt: specificDate,
            });
            // Mock current date to make calculations predictable (5 days later)
            const mockDate = new Date('2024-01-20T00:00:00Z');
            jest.spyOn(Date, 'now').mockReturnValue(mockDate.getTime());
            expect(spec.isSatisfiedBy(testVuln)).toBe(true); // 5 days old, within 1-30 day range
            Date.now = jest.fn().mockRestore();
        });
    });
    describe('AffectedAssetCountSpecification', () => {
        let spec;
        beforeEach(() => {
            spec = new vulnerability_specification_1.AffectedAssetCountSpecification(1, 5);
        });
        it('should identify vulnerabilities with specified asset count', () => {
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // 1 asset
            // Create vulnerability with multiple assets
            const multiAssetVuln = vulnerability_entity_1.Vulnerability.create('Multi Asset Vulnerability', 'Affects multiple assets', threat_severity_enum_1.ThreatSeverity.MEDIUM, 'general', 'multi', [mockAffectedAssets[0], { ...mockAffectedAssets[0], assetId: 'asset-2' }], mockDiscovery);
            expect(spec.isSatisfiedBy(multiAssetVuln)).toBe(true); // 2 assets
        });
    });
    describe('DiscoveryMethodSpecification', () => {
        let spec;
        beforeEach(() => {
            spec = new vulnerability_specification_1.DiscoveryMethodSpecification(['automated_scan', 'manual_testing']);
        });
        it('should identify vulnerabilities discovered by specified methods', () => {
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true); // automated_scan
            const manualVuln = vulnerability_entity_1.Vulnerability.create('Manual Discovery', 'Manually discovered vulnerability', threat_severity_enum_1.ThreatSeverity.MEDIUM, 'general', 'manual', mockAffectedAssets, {
                ...mockDiscovery,
                method: 'manual_testing',
            });
            expect(spec.isSatisfiedBy(manualVuln)).toBe(true);
            const threatIntelVuln = vulnerability_entity_1.Vulnerability.create('Threat Intel Discovery', 'Discovered via threat intelligence', threat_severity_enum_1.ThreatSeverity.HIGH, 'general', 'intel', mockAffectedAssets, {
                ...mockDiscovery,
                method: 'threat_intelligence',
            });
            expect(spec.isSatisfiedBy(threatIntelVuln)).toBe(false);
        });
    });
    describe('VulnerabilitySpecificationBuilder', () => {
        it('should build complex specifications with AND logic', () => {
            const spec = vulnerability_specification_1.VulnerabilitySpecificationBuilder.create()
                .critical()
                .active()
                .externallyExposed()
                .build();
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true);
            expect(spec.isSatisfiedBy(highVulnerability)).toBe(false); // Not critical
            expect(spec.getDescription()).toContain('AND');
        });
        it('should build complex specifications with OR logic', () => {
            const spec = vulnerability_specification_1.VulnerabilitySpecificationBuilder.create()
                .critical()
                .highSeverity()
                .buildWithOr();
            expect(spec.isSatisfiedBy(criticalVulnerability)).toBe(true);
            expect(spec.isSatisfiedBy(highVulnerability)).toBe(true);
            expect(spec.isSatisfiedBy(mediumVulnerability)).toBe(false);
            expect(spec.getDescription()).toContain('OR');
        });
        it('should handle single specification', () => {
            const spec = vulnerability_specification_1.VulnerabilitySpecificationBuilder.create()
                .critical()
                .build();
            expect(spec).toBeInstanceOf(vulnerability_specification_1.CriticalVulnerabilitySpecification);
        });
        it('should throw error for empty builder', () => {
            expect(() => {
                vulnerability_specification_1.VulnerabilitySpecificationBuilder.create().build();
            }).toThrow('At least one specification must be added');
        });
        it('should support all builder methods', () => {
            const builder = vulnerability_specification_1.VulnerabilitySpecificationBuilder.create()
                .critical()
                .highSeverity()
                .active()
                .remediated()
                .highRisk(80)
                .highConfidence()
                .recent(7)
                .stale(90)
                .withCVE('CVE-2024-1234')
                .zeroDay()
                .activelyExploited()
                .externallyExposed()
                .affectsCriticalAssets()
                .highCVSS(7.0)
                .overdueRemediation()
                .requiresImmediateAttention()
                .withSeverities(threat_severity_enum_1.ThreatSeverity.CRITICAL, threat_severity_enum_1.ThreatSeverity.HIGH)
                .withStatuses(vulnerability_entity_1.VulnerabilityStatus.DISCOVERED)
                .withCategories('injection')
                .withTypes('sql_injection')
                .withTags(['critical'], false)
                .riskScoreRange(70, 100)
                .cvssScoreRange(7.0, 10.0)
                .ageRange(1, 30)
                .affectedAssetCount(1, 5)
                .discoveredBy('automated_scan');
            expect(builder).toBeInstanceOf(vulnerability_specification_1.VulnerabilitySpecificationBuilder);
            const spec = builder.build();
            expect(spec).toBeDefined();
        });
    });
    describe('specification combinations', () => {
        it('should combine specifications with AND logic', () => {
            const criticalSpec = new vulnerability_specification_1.CriticalVulnerabilitySpecification();
            const activeSpec = new vulnerability_specification_1.ActiveVulnerabilitySpecification();
            const combinedSpec = criticalSpec.and(activeSpec);
            expect(combinedSpec.isSatisfiedBy(criticalVulnerability)).toBe(true);
            criticalVulnerability.changeStatus(vulnerability_entity_1.VulnerabilityStatus.REMEDIATED, 'Fixed');
            expect(combinedSpec.isSatisfiedBy(criticalVulnerability)).toBe(false);
        });
        it('should combine specifications with OR logic', () => {
            const criticalSpec = new vulnerability_specification_1.CriticalVulnerabilitySpecification();
            const highSpec = new vulnerability_specification_1.HighSeverityVulnerabilitySpecification();
            const combinedSpec = criticalSpec.or(highSpec);
            expect(combinedSpec.isSatisfiedBy(criticalVulnerability)).toBe(true);
            expect(combinedSpec.isSatisfiedBy(highVulnerability)).toBe(true);
            expect(combinedSpec.isSatisfiedBy(mediumVulnerability)).toBe(false);
        });
        it('should negate specifications', () => {
            const criticalSpec = new vulnerability_specification_1.CriticalVulnerabilitySpecification();
            const notCriticalSpec = criticalSpec.not();
            expect(notCriticalSpec.isSatisfiedBy(criticalVulnerability)).toBe(false);
            expect(notCriticalSpec.isSatisfiedBy(highVulnerability)).toBe(true);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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