a74e28fc8f252db06afbe624f31d3d62
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnrichedEventSpecificationBuilder = exports.AverageReputationScoreRangeSpecification = exports.EnrichmentDataTypeSpecification = exports.EnrichmentSourceSpecification = exports.EnrichmentDurationRangeSpecification = exports.PendingReviewSpecification = exports.ReviewedEventSpecification = exports.ExceededMaxAttemptsSpecification = exports.NormalizedEventSpecification = exports.AppliedRuleSpecification = exports.ThreatIntelScoreRangeSpecification = exports.EnrichmentQualityScoreRangeSpecification = exports.EnrichmentStatusSpecification = exports.HasGeolocationDataSpecification = exports.HasReputationDataSpecification = exports.HasThreatIntelligenceSpecification = exports.HighThreatRiskSpecification = exports.ReadyForNextStageSpecification = exports.RequiresManualReviewSpecification = exports.HasValidationErrorsSpecification = exports.HighEnrichmentQualitySpecification = exports.EnrichmentPartialSpecification = exports.EnrichmentInProgressSpecification = exports.EnrichmentFailedSpecification = exports.EnrichmentCompletedSpecification = exports.EnrichedEventSpecification = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
/**
 * EnrichedEvent Specification Base Class
 *
 * Base class for all enriched event-related specifications.
 * Provides common functionality for enriched event filtering and validation.
 */
class EnrichedEventSpecification extends shared_kernel_1.BaseSpecification {
    /**
     * Helper method to check if enriched event matches any of the provided types
     */
    matchesAnyType(event, types) {
        return types.includes(event.type);
    }
    /**
     * Helper method to check if enriched event matches any of the provided severities
     */
    matchesAnySeverity(event, severities) {
        return severities.includes(event.severity);
    }
    /**
     * Helper method to check if enriched event matches any of the provided statuses
     */
    matchesAnyStatus(event, statuses) {
        return statuses.includes(event.status);
    }
    /**
     * Helper method to check if enriched event matches any of the provided processing statuses
     */
    matchesAnyProcessingStatus(event, statuses) {
        return statuses.includes(event.processingStatus);
    }
    /**
     * Helper method to check if enriched event matches any of the provided enrichment statuses
     */
    matchesAnyEnrichmentStatus(event, statuses) {
        return statuses.includes(event.enrichmentStatus);
    }
    /**
     * Helper method to check if enriched event has any of the provided tags
     */
    hasAnyTag(event, tags) {
        return event.tags.some(tag => tags.includes(tag));
    }
    /**
     * Helper method to check if enriched event has all of the provided tags
     */
    hasAllTags(event, tags) {
        return tags.every(tag => event.tags.includes(tag));
    }
    /**
     * Helper method to check if enrichment quality score is within range
     */
    isEnrichmentQualityScoreWithinRange(event, minScore, maxScore) {
        const qualityScore = event.enrichmentQualityScore;
        if (qualityScore === undefined) {
            return minScore === undefined; // If no min score required, undefined is acceptable
        }
        if (minScore !== undefined && qualityScore < minScore) {
            return false;
        }
        if (maxScore !== undefined && qualityScore > maxScore) {
            return false;
        }
        return true;
    }
    /**
     * Helper method to check if threat intelligence score is within range
     */
    isThreatIntelScoreWithinRange(event, minScore, maxScore) {
        const threatScore = event.threatIntelScore;
        if (threatScore === undefined) {
            return minScore === undefined; // If no min score required, undefined is acceptable
        }
        if (minScore !== undefined && threatScore < minScore) {
            return false;
        }
        if (maxScore !== undefined && threatScore > maxScore) {
            return false;
        }
        return true;
    }
    /**
     * Helper method to check if risk score is within range
     */
    isRiskScoreWithinRange(event, minScore, maxScore) {
        const riskScore = event.riskScore;
        if (riskScore === undefined) {
            return minScore === undefined; // If no min score required, undefined is acceptable
        }
        if (minScore !== undefined && riskScore < minScore) {
            return false;
        }
        if (maxScore !== undefined && riskScore > maxScore) {
            return false;
        }
        return true;
    }
    /**
     * Helper method to check if applied rule exists
     */
    hasAppliedRule(event, ruleId) {
        return event.hasAppliedRule(ruleId);
    }
    /**
     * Helper method to check if enrichment data from source exists
     */
    hasEnrichmentDataFromSource(event, source) {
        return event.getEnrichmentDataBySource(source).length > 0;
    }
    /**
     * Helper method to check if enrichment data of type exists
     */
    hasEnrichmentDataOfType(event, type) {
        return event.getEnrichmentDataByType(type).length > 0;
    }
}
exports.EnrichedEventSpecification = EnrichedEventSpecification;
/**
 * Enrichment Completed Specification
 *
 * Specification for enriched events that have completed enrichment.
 */
class EnrichmentCompletedSpecification extends EnrichedEventSpecification {
    isSatisfiedBy(event) {
        return event.isEnrichmentCompleted();
    }
    getDescription() {
        return 'Enriched event has completed enrichment';
    }
}
exports.EnrichmentCompletedSpecification = EnrichmentCompletedSpecification;
/**
 * Enrichment Failed Specification
 *
 * Specification for enriched events that have failed enrichment.
 */
class EnrichmentFailedSpecification extends EnrichedEventSpecification {
    isSatisfiedBy(event) {
        return event.isEnrichmentFailed();
    }
    getDescription() {
        return 'Enriched event has failed enrichment';
    }
}
exports.EnrichmentFailedSpecification = EnrichmentFailedSpecification;
/**
 * Enrichment In Progress Specification
 *
 * Specification for enriched events that are currently being enriched.
 */
class EnrichmentInProgressSpecification extends EnrichedEventSpecification {
    isSatisfiedBy(event) {
        return event.isEnrichmentInProgress();
    }
    getDescription() {
        return 'Enriched event is currently being enriched';
    }
}
exports.EnrichmentInProgressSpecification = EnrichmentInProgressSpecification;
/**
 * Enrichment Partial Specification
 *
 * Specification for enriched events that have partial enrichment results.
 */
class EnrichmentPartialSpecification extends EnrichedEventSpecification {
    isSatisfiedBy(event) {
        return event.isEnrichmentPartial();
    }
    getDescription() {
        return 'Enriched event has partial enrichment results';
    }
}
exports.EnrichmentPartialSpecification = EnrichmentPartialSpecification;
/**
 * High Enrichment Quality Specification
 *
 * Specification for enriched events with high enrichment quality scores.
 */
class HighEnrichmentQualitySpecification extends EnrichedEventSpecification {
    isSatisfiedBy(event) {
        return event.hasHighEnrichmentQuality();
    }
    getDescription() {
        return 'Enriched event has high enrichment quality (>= 70)';
    }
}
exports.HighEnrichmentQualitySpecification = HighEnrichmentQualitySpecification;
/**
 * Has Validation Errors Specification
 *
 * Specification for enriched events that have validation errors.
 */
class HasValidationErrorsSpecification extends EnrichedEventSpecification {
    isSatisfiedBy(event) {
        return event.hasValidationErrors();
    }
    getDescription() {
        return 'Enriched event has validation errors';
    }
}
exports.HasValidationErrorsSpecification = HasValidationErrorsSpecification;
/**
 * Requires Manual Review Specification
 *
 * Specification for enriched events that require manual review.
 */
class RequiresManualReviewSpecification extends EnrichedEventSpecification {
    isSatisfiedBy(event) {
        return event.requiresManualReview;
    }
    getDescription() {
        return 'Enriched event requires manual review';
    }
}
exports.RequiresManualReviewSpecification = RequiresManualReviewSpecification;
/**
 * Ready For Next Stage Specification
 *
 * Specification for enriched events that are ready for the next processing stage.
 */
class ReadyForNextStageSpecification extends EnrichedEventSpecification {
    isSatisfiedBy(event) {
        return event.isReadyForNextStage();
    }
    getDescription() {
        return 'Enriched event is ready for next processing stage';
    }
}
exports.ReadyForNextStageSpecification = ReadyForNextStageSpecification;
/**
 * High Threat Risk Specification
 *
 * Specification for enriched events with high threat intelligence risk scores.
 */
class HighThreatRiskSpecification extends EnrichedEventSpecification {
    isSatisfiedBy(event) {
        return event.isHighThreatRisk();
    }
    getDescription() {
        return 'Enriched event has high threat intelligence risk (>= 85)';
    }
}
exports.HighThreatRiskSpecification = HighThreatRiskSpecification;
/**
 * Has Threat Intelligence Specification
 *
 * Specification for enriched events that have threat intelligence data.
 */
class HasThreatIntelligenceSpecification extends EnrichedEventSpecification {
    isSatisfiedBy(event) {
        return event.hasThreatIntelligence();
    }
    getDescription() {
        return 'Enriched event has threat intelligence data';
    }
}
exports.HasThreatIntelligenceSpecification = HasThreatIntelligenceSpecification;
/**
 * Has Reputation Data Specification
 *
 * Specification for enriched events that have reputation data.
 */
class HasReputationDataSpecification extends EnrichedEventSpecification {
    isSatisfiedBy(event) {
        return event.hasReputationData();
    }
    getDescription() {
        return 'Enriched event has reputation data';
    }
}
exports.HasReputationDataSpecification = HasReputationDataSpecification;
/**
 * Has Geolocation Data Specification
 *
 * Specification for enriched events that have geolocation data.
 */
class HasGeolocationDataSpecification extends EnrichedEventSpecification {
    isSatisfiedBy(event) {
        return event.hasGeolocationData();
    }
    getDescription() {
        return 'Enriched event has geolocation data';
    }
}
exports.HasGeolocationDataSpecification = HasGeolocationDataSpecification;
/**
 * Enrichment Status Specification
 *
 * Specification for enriched events with specific enrichment statuses.
 */
class EnrichmentStatusSpecification extends EnrichedEventSpecification {
    constructor(statuses) {
        super();
        this.statuses = statuses;
    }
    isSatisfiedBy(event) {
        return this.matchesAnyEnrichmentStatus(event, this.statuses);
    }
    getDescription() {
        return `Enriched event status is one of: ${this.statuses.join(', ')}`;
    }
}
exports.EnrichmentStatusSpecification = EnrichmentStatusSpecification;
/**
 * Enrichment Quality Score Range Specification
 *
 * Specification for enriched events within a specific enrichment quality score range.
 */
class EnrichmentQualityScoreRangeSpecification extends EnrichedEventSpecification {
    constructor(minScore, maxScore) {
        super();
        this.minScore = minScore;
        this.maxScore = maxScore;
    }
    isSatisfiedBy(event) {
        return this.isEnrichmentQualityScoreWithinRange(event, this.minScore, this.maxScore);
    }
    getDescription() {
        if (this.minScore !== undefined && this.maxScore !== undefined) {
            return `Enriched event quality score is between ${this.minScore} and ${this.maxScore}`;
        }
        else if (this.minScore !== undefined) {
            return `Enriched event quality score is at least ${this.minScore}`;
        }
        else if (this.maxScore !== undefined) {
            return `Enriched event quality score is at most ${this.maxScore}`;
        }
        return 'Enriched event has any quality score';
    }
}
exports.EnrichmentQualityScoreRangeSpecification = EnrichmentQualityScoreRangeSpecification;
/**
 * Threat Intelligence Score Range Specification
 *
 * Specification for enriched events within a specific threat intelligence score range.
 */
class ThreatIntelScoreRangeSpecification extends EnrichedEventSpecification {
    constructor(minScore, maxScore) {
        super();
        this.minScore = minScore;
        this.maxScore = maxScore;
    }
    isSatisfiedBy(event) {
        return this.isThreatIntelScoreWithinRange(event, this.minScore, this.maxScore);
    }
    getDescription() {
        if (this.minScore !== undefined && this.maxScore !== undefined) {
            return `Enriched event threat intelligence score is between ${this.minScore} and ${this.maxScore}`;
        }
        else if (this.minScore !== undefined) {
            return `Enriched event threat intelligence score is at least ${this.minScore}`;
        }
        else if (this.maxScore !== undefined) {
            return `Enriched event threat intelligence score is at most ${this.maxScore}`;
        }
        return 'Enriched event has any threat intelligence score';
    }
}
exports.ThreatIntelScoreRangeSpecification = ThreatIntelScoreRangeSpecification;
/**
 * Applied Rule Specification
 *
 * Specification for enriched events that have specific rules applied.
 */
class AppliedRuleSpecification extends EnrichedEventSpecification {
    constructor(ruleId) {
        super();
        this.ruleId = ruleId;
    }
    isSatisfiedBy(event) {
        return this.hasAppliedRule(event, this.ruleId);
    }
    getDescription() {
        return `Enriched event has applied rule: ${this.ruleId}`;
    }
}
exports.AppliedRuleSpecification = AppliedRuleSpecification;
/**
 * Normalized Event Specification
 *
 * Specification for enriched events that reference a specific normalized event.
 */
class NormalizedEventSpecification extends EnrichedEventSpecification {
    constructor(normalizedEventId) {
        super();
        this.normalizedEventId = normalizedEventId;
    }
    isSatisfiedBy(event) {
        return event.normalizedEventId.equals(this.normalizedEventId);
    }
    getDescription() {
        return `Enriched event references normalized event: ${this.normalizedEventId.toString()}`;
    }
}
exports.NormalizedEventSpecification = NormalizedEventSpecification;
/**
 * Exceeded Max Attempts Specification
 *
 * Specification for enriched events that have exceeded maximum enrichment attempts.
 */
class ExceededMaxAttemptsSpecification extends EnrichedEventSpecification {
    isSatisfiedBy(event) {
        return event.hasExceededMaxEnrichmentAttempts();
    }
    getDescription() {
        return 'Enriched event has exceeded maximum enrichment attempts';
    }
}
exports.ExceededMaxAttemptsSpecification = ExceededMaxAttemptsSpecification;
/**
 * Reviewed Event Specification
 *
 * Specification for enriched events that have been manually reviewed.
 */
class ReviewedEventSpecification extends EnrichedEventSpecification {
    isSatisfiedBy(event) {
        return event.reviewedAt !== undefined;
    }
    getDescription() {
        return 'Enriched event has been manually reviewed';
    }
}
exports.ReviewedEventSpecification = ReviewedEventSpecification;
/**
 * Pending Review Specification
 *
 * Specification for enriched events that are pending manual review.
 */
class PendingReviewSpecification extends EnrichedEventSpecification {
    isSatisfiedBy(event) {
        return event.requiresManualReview && event.reviewedAt === undefined;
    }
    getDescription() {
        return 'Enriched event is pending manual review';
    }
}
exports.PendingReviewSpecification = PendingReviewSpecification;
/**
 * Enrichment Duration Range Specification
 *
 * Specification for enriched events with enrichment duration within a specific range.
 */
class EnrichmentDurationRangeSpecification extends EnrichedEventSpecification {
    constructor(minDurationMs, maxDurationMs) {
        super();
        this.minDurationMs = minDurationMs;
        this.maxDurationMs = maxDurationMs;
    }
    isSatisfiedBy(event) {
        const duration = event.getEnrichmentDuration();
        if (duration === null) {
            return false; // No duration available
        }
        if (this.minDurationMs !== undefined && duration < this.minDurationMs) {
            return false;
        }
        if (this.maxDurationMs !== undefined && duration > this.maxDurationMs) {
            return false;
        }
        return true;
    }
    getDescription() {
        const formatDuration = (ms) => `${ms}ms`;
        if (this.minDurationMs !== undefined && this.maxDurationMs !== undefined) {
            return `Enriched event duration is between ${formatDuration(this.minDurationMs)} and ${formatDuration(this.maxDurationMs)}`;
        }
        else if (this.minDurationMs !== undefined) {
            return `Enriched event duration is at least ${formatDuration(this.minDurationMs)}`;
        }
        else if (this.maxDurationMs !== undefined) {
            return `Enriched event duration is at most ${formatDuration(this.maxDurationMs)}`;
        }
        return 'Enriched event has any duration';
    }
}
exports.EnrichmentDurationRangeSpecification = EnrichmentDurationRangeSpecification;
/**
 * Enrichment Source Specification
 *
 * Specification for enriched events that have enrichment data from specific sources.
 */
class EnrichmentSourceSpecification extends EnrichedEventSpecification {
    constructor(sources) {
        super();
        this.sources = sources;
    }
    isSatisfiedBy(event) {
        return this.sources.some(source => this.hasEnrichmentDataFromSource(event, source));
    }
    getDescription() {
        return `Enriched event has data from sources: ${this.sources.join(', ')}`;
    }
}
exports.EnrichmentSourceSpecification = EnrichmentSourceSpecification;
/**
 * Enrichment Data Type Specification
 *
 * Specification for enriched events that have enrichment data of specific types.
 */
class EnrichmentDataTypeSpecification extends EnrichedEventSpecification {
    constructor(types) {
        super();
        this.types = types;
    }
    isSatisfiedBy(event) {
        return this.types.some(type => this.hasEnrichmentDataOfType(event, type));
    }
    getDescription() {
        return `Enriched event has data of types: ${this.types.join(', ')}`;
    }
}
exports.EnrichmentDataTypeSpecification = EnrichmentDataTypeSpecification;
/**
 * Average Reputation Score Range Specification
 *
 * Specification for enriched events with average reputation score within a specific range.
 */
class AverageReputationScoreRangeSpecification extends EnrichedEventSpecification {
    constructor(minScore, maxScore) {
        super();
        this.minScore = minScore;
        this.maxScore = maxScore;
    }
    isSatisfiedBy(event) {
        const avgScore = event.getAverageReputationScore();
        if (avgScore === null) {
            return this.minScore === undefined; // If no min score required, null is acceptable
        }
        if (this.minScore !== undefined && avgScore < this.minScore) {
            return false;
        }
        if (this.maxScore !== undefined && avgScore > this.maxScore) {
            return false;
        }
        return true;
    }
    getDescription() {
        if (this.minScore !== undefined && this.maxScore !== undefined) {
            return `Enriched event average reputation score is between ${this.minScore} and ${this.maxScore}`;
        }
        else if (this.minScore !== undefined) {
            return `Enriched event average reputation score is at least ${this.minScore}`;
        }
        else if (this.maxScore !== undefined) {
            return `Enriched event average reputation score is at most ${this.maxScore}`;
        }
        return 'Enriched event has any average reputation score';
    }
}
exports.AverageReputationScoreRangeSpecification = AverageReputationScoreRangeSpecification;
/**
 * Composite EnrichedEvent Specification Builder
 *
 * Builder for creating complex enriched event specifications using fluent interface.
 */
class EnrichedEventSpecificationBuilder {
    constructor() {
        this.specifications = [];
    }
    /**
     * Add enrichment completed filter
     */
    enrichmentCompleted() {
        this.specifications.push(new EnrichmentCompletedSpecification());
        return this;
    }
    /**
     * Add enrichment failed filter
     */
    enrichmentFailed() {
        this.specifications.push(new EnrichmentFailedSpecification());
        return this;
    }
    /**
     * Add enrichment in progress filter
     */
    enrichmentInProgress() {
        this.specifications.push(new EnrichmentInProgressSpecification());
        return this;
    }
    /**
     * Add enrichment partial filter
     */
    enrichmentPartial() {
        this.specifications.push(new EnrichmentPartialSpecification());
        return this;
    }
    /**
     * Add high enrichment quality filter
     */
    highEnrichmentQuality() {
        this.specifications.push(new HighEnrichmentQualitySpecification());
        return this;
    }
    /**
     * Add has validation errors filter
     */
    hasValidationErrors() {
        this.specifications.push(new HasValidationErrorsSpecification());
        return this;
    }
    /**
     * Add requires manual review filter
     */
    requiresManualReview() {
        this.specifications.push(new RequiresManualReviewSpecification());
        return this;
    }
    /**
     * Add ready for next stage filter
     */
    readyForNextStage() {
        this.specifications.push(new ReadyForNextStageSpecification());
        return this;
    }
    /**
     * Add high threat risk filter
     */
    highThreatRisk() {
        this.specifications.push(new HighThreatRiskSpecification());
        return this;
    }
    /**
     * Add has threat intelligence filter
     */
    hasThreatIntelligence() {
        this.specifications.push(new HasThreatIntelligenceSpecification());
        return this;
    }
    /**
     * Add has reputation data filter
     */
    hasReputationData() {
        this.specifications.push(new HasReputationDataSpecification());
        return this;
    }
    /**
     * Add has geolocation data filter
     */
    hasGeolocationData() {
        this.specifications.push(new HasGeolocationDataSpecification());
        return this;
    }
    /**
     * Add enrichment status filter
     */
    withEnrichmentStatus(...statuses) {
        this.specifications.push(new EnrichmentStatusSpecification(statuses));
        return this;
    }
    /**
     * Add enrichment quality score range filter
     */
    enrichmentQualityScoreRange(minScore, maxScore) {
        this.specifications.push(new EnrichmentQualityScoreRangeSpecification(minScore, maxScore));
        return this;
    }
    /**
     * Add threat intelligence score range filter
     */
    threatIntelScoreRange(minScore, maxScore) {
        this.specifications.push(new ThreatIntelScoreRangeSpecification(minScore, maxScore));
        return this;
    }
    /**
     * Add applied rule filter
     */
    withAppliedRule(ruleId) {
        this.specifications.push(new AppliedRuleSpecification(ruleId));
        return this;
    }
    /**
     * Add normalized event filter
     */
    fromNormalizedEvent(normalizedEventId) {
        this.specifications.push(new NormalizedEventSpecification(normalizedEventId));
        return this;
    }
    /**
     * Add exceeded max attempts filter
     */
    exceededMaxAttempts() {
        this.specifications.push(new ExceededMaxAttemptsSpecification());
        return this;
    }
    /**
     * Add reviewed filter
     */
    reviewed() {
        this.specifications.push(new ReviewedEventSpecification());
        return this;
    }
    /**
     * Add pending review filter
     */
    pendingReview() {
        this.specifications.push(new PendingReviewSpecification());
        return this;
    }
    /**
     * Add enrichment duration range filter
     */
    enrichmentDurationRange(minDurationMs, maxDurationMs) {
        this.specifications.push(new EnrichmentDurationRangeSpecification(minDurationMs, maxDurationMs));
        return this;
    }
    /**
     * Add enrichment source filter
     */
    withEnrichmentSources(...sources) {
        this.specifications.push(new EnrichmentSourceSpecification(sources));
        return this;
    }
    /**
     * Add enrichment data type filter
     */
    withEnrichmentDataTypes(...types) {
        this.specifications.push(new EnrichmentDataTypeSpecification(types));
        return this;
    }
    /**
     * Add average reputation score range filter
     */
    averageReputationScoreRange(minScore, maxScore) {
        this.specifications.push(new AverageReputationScoreRangeSpecification(minScore, maxScore));
        return this;
    }
    /**
     * Build the final specification using AND logic
     */
    build() {
        if (this.specifications.length === 0) {
            throw new Error('At least one specification must be added');
        }
        if (this.specifications.length === 1) {
            return this.specifications[0];
        }
        // Combine all specifications with AND logic
        let combined = this.specifications[0];
        for (let i = 1; i < this.specifications.length; i++) {
            combined = combined.and(this.specifications[i]);
        }
        return combined;
    }
    /**
     * Build the final specification using OR logic
     */
    buildWithOr() {
        if (this.specifications.length === 0) {
            throw new Error('At least one specification must be added');
        }
        if (this.specifications.length === 1) {
            return this.specifications[0];
        }
        // Combine all specifications with OR logic
        let combined = this.specifications[0];
        for (let i = 1; i < this.specifications.length; i++) {
            combined = combined.or(this.specifications[i]);
        }
        return combined;
    }
    /**
     * Create a new builder instance
     */
    static create() {
        return new EnrichedEventSpecificationBuilder();
    }
}
exports.EnrichedEventSpecificationBuilder = EnrichedEventSpecificationBuilder;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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