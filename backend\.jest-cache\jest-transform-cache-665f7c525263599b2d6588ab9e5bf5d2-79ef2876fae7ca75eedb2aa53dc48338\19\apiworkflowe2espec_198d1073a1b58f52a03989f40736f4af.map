{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\__tests__\\e2e\\api-workflow.e2e-spec.ts", "mappings": ";;;;;AAAA,6CAAsD;AAEtD,2CAA8C;AAC9C,0DAAgC;AAChC,iDAA6C;AAE7C,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;IAClC,IAAI,GAAqB,CAAC;IAC1B,IAAI,SAAiB,CAAC;IACtB,IAAI,MAAc,CAAC;IAEnB,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,aAAa,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAClE,OAAO,EAAE;gBACP,qBAAY,CAAC,OAAO,CAAC;oBACnB,QAAQ,EAAE,IAAI;oBACd,WAAW,EAAE,WAAW;iBACzB,CAAC;gBACF,sBAAS;aACV;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,GAAG,GAAG,aAAa,CAAC,qBAAqB,EAAE,CAAC;QAC5C,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC;QAEjB,+CAA+C;QAC/C,MAAM,YAAY,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;aACpD,IAAI,CAAC,uBAAuB,CAAC;aAC7B,IAAI,CAAC;YACJ,KAAK,EAAE,sBAAsB;YAC7B,QAAQ,EAAE,oBAAoB;YAC9B,IAAI,EAAE,eAAe;SACtB,CAAC,CAAC;QAEL,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;QACtD,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAC1C,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,gBAAgB,CAAC;iBACrB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,MAAM,EAAE,IAAI;gBACZ,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC1B,OAAO,EAAE,OAAO;gBAChB,WAAW,EAAE,MAAM;aACpB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,gBAAgB,CAAC;iBACrB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC1B,OAAO,EAAE,OAAO;gBAChB,WAAW,EAAE,MAAM;gBACnB,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC7B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,gBAAgB,CAAC;iBACrB,GAAG,CAAC,QAAQ,EAAE,sCAAsC,CAAC;iBACrD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,gBAAgB,CAAC;iBACrB,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC;iBAC3B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,kBAAkB,CAAC;iBACvB,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,gBAAgB,CAAC;iBACrB,KAAK,CAAC;gBACL,QAAQ,EAAE,MAAM;gBAChB,MAAM,EAAE,MAAM;aACf,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,QAAQ,GAAG;gBACf,IAAI,EAAE,WAAW;gBACjB,WAAW,EAAE,kBAAkB;gBAC/B,QAAQ,EAAE,MAAM;aACjB,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC,QAAQ,CAAC;iBACd,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAC5B,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACtB,GAAG,QAAQ;oBACX,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC7B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC9B,CAAC;gBACF,OAAO,EAAE,2BAA2B;gBACpC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC9B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,uBAAuB;YACvB,MAAM,cAAc,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACtD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC;gBACJ,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,sBAAsB;gBACnC,QAAQ,EAAE,MAAM;aACjB,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,MAAM,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAE3C,iBAAiB;YACjB,MAAM,UAAU,GAAG;gBACjB,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,qBAAqB;gBAClC,QAAQ,EAAE,SAAS;aACpB,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,sBAAsB,MAAM,EAAE,CAAC;iBACnC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC,UAAU,CAAC;iBAChB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAChC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,EAAE,EAAE,MAAM;gBACV,GAAG,UAAU;gBACb,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC9B,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,uBAAuB;YACvB,MAAM,cAAc,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACtD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC;gBACJ,IAAI,EAAE,gBAAgB;gBACtB,WAAW,EAAE,iBAAiB;gBAC9B,QAAQ,EAAE,MAAM;aACjB,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,MAAM,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAE3C,iBAAiB;YACjB,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,MAAM,CAAC,sBAAsB,MAAM,EAAE,CAAC;iBACtC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAEhE,sBAAsB;YACtB,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,sBAAsB,MAAM,EAAE,CAAC;iBACnC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,SAAS,CAAC,KAAK,IAAI,EAAE;YACnB,+CAA+C;YAC/C,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;gBAClD,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,EAAE;gBAC1B,WAAW,EAAE,wBAAwB,CAAC,GAAG,CAAC,EAAE;gBAC5C,QAAQ,EAAE,iBAAiB;aAC5B,CAAC,CAAC,CAAC;YAEJ,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAC/B,IAAI,CAAC,oBAAoB,CAAC;qBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;qBAC3C,IAAI,CAAC,IAAI,CAAC,CAAC;YAChB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,oBAAoB,CAAC;iBACzB,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;gBACvB,OAAO,EAAE,8BAA8B;gBACvC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,UAAU,EAAE;oBACV,IAAI,EAAE,CAAC;oBACP,KAAK,EAAE,EAAE;oBACT,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACzB,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC9B,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC;oBAC5B,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,CAAC;iBACV;gBACD,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC7B,CAAC,CAAC;YAEH,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,oBAAoB,CAAC;iBACzB,KAAK,CAAC;gBACL,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,CAAC;aACT,CAAC;iBACD,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC;gBACvC,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,CAAC;gBACR,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBACzB,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC9B,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC;gBAC5B,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,CAAC;aACV,CAAC,CAAC;YAEH,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,oBAAoB,CAAC;iBACzB,KAAK,CAAC;gBACL,MAAM,EAAE,MAAM;gBACd,SAAS,EAAE,KAAK;gBAChB,KAAK,EAAE,CAAC;aACT,CAAC;iBACD,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;YACjC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAExC,iBAAiB;YACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACtC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,oBAAoB,CAAC;iBACzB,KAAK,CAAC;gBACL,eAAe,EAAE,iBAAiB;gBAClC,WAAW,EAAE,aAAa;aAC3B,CAAC;iBACD,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACrD,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBAChC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBAC9C,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC;gBACJ,WAAW,EAAE,oBAAoB;aAClC,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;YAC3C,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC;gBACJ,IAAI,EAAE,GAAG,EAAE,mBAAmB;gBAC9B,WAAW,EAAE,sBAAsB;gBACnC,QAAQ,EAAE,MAAM;aACjB,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC;gBACJ,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,WAAW;gBAClC,WAAW,EAAE,wBAAwB;gBACrC,QAAQ,EAAE,MAAM;aACjB,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;YAC1C,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC;gBACJ,IAAI,EAAE,yCAAyC;gBAC/C,WAAW,EAAE,sBAAsB;gBACnC,QAAQ,EAAE,MAAM;aACjB,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACnD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,oCAAoC,CAAC;iBACzC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,UAAU,EAAE,GAAG;gBACf,OAAO,EAAE,gBAAgB;gBACzB,KAAK,EAAE,WAAW;gBAClB,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,IAAI,EAAE,oCAAoC;gBAC1C,MAAM,EAAE,KAAK;gBACb,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC9B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,oBAAoB,CAAC;iBACzB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,qBAAqB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;YAC3C,wDAAwD;YACxD,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAC/C,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACzB,GAAG,CAAC,gBAAgB,CAAC,CACzB,CAAC;YAEF,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CACrD,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;iBAC5D,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,QAAQ,EAAE,MAAM,IAAI,GAAG,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC,CACxE,CAAC,CAAC;YAEH,uCAAuC;YACvC,MAAM,oBAAoB,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,GAAG,CAAC,CAAC;YAErE,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC3E,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;YACjF,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,gBAAgB,CAAC;iBACrB,GAAG,CAAC,QAAQ,EAAE,kBAAkB,CAAC;iBACjC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACrE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,gBAAgB,CAAC;iBACrB,GAAG,CAAC,QAAQ,EAAE,sCAAsC,CAAC;iBACrD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,KAAK,IAAI,EAAE;YACzC,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,gBAAgB,CAAC;iBACrB,GAAG,CAAC,iBAAiB,EAAE,eAAe,CAAC;iBACvC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,0DAA0D;YAC1D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,gBAAgB,CAAC;iBACrB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,oCAAoC;YACpC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YACvC,uEAAuE;YACvE,4DAA4D;YAC5D,sEAAsE;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,OAAO,CAAC,gBAAgB,CAAC;iBACzB,GAAG,CAAC,QAAQ,EAAE,uBAAuB,CAAC;iBACtC,GAAG,CAAC,+BAA+B,EAAE,KAAK,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QACxE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,aAAa,GAAG,sBAAsB,CAAC;YAE7C,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,gBAAgB,CAAC;iBACrB,GAAG,CAAC,kBAAkB,EAAE,aAAa,CAAC;iBACtC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,qFAAqF;YACrF,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,gBAAgB,CAAC;iBACrB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC3B,MAAM,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAC;YAErC,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,yBAAyB;YAC9D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,gBAAgB,CAAC;iBACrB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,MAAM,EAAE,IAAI;gBACZ,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC1B,OAAO,EAAE,OAAO;gBAChB,WAAW,EAAE,MAAM;aACpB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,yBAAyB,CAAC;iBAC9B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,MAAM,EAAE,IAAI;gBACZ,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC1B,OAAO,EAAE,OAAO;gBAChB,WAAW,EAAE,MAAM;gBACnB,QAAQ,EAAE;oBACR,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC5B,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACzB,aAAa,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAClC;gBACD,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC1B,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aACxB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAC/C,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC,GAAG,CAAC,gBAAgB,CAAC,CACnD,CAAC;YAEF,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAE9C,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC3B,MAAM,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAC;YAErC,8BAA8B;YAC9B,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAC3B,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;YAEH,gDAAgD;YAChD,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,YAAY,GAAG;gBACnB,IAAI,EAAE,YAAY;gBAClB,WAAW,EAAE,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,oBAAoB;gBACnD,QAAQ,EAAE,kBAAkB;gBAC5B,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;oBAC/C,GAAG,EAAE,MAAM,CAAC,EAAE;oBACd,KAAK,EAAE,QAAQ,CAAC,EAAE;iBACnB,CAAC,CAAC;aACJ,CAAC;YAEF,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC,YAAY,CAAC;iBAClB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC3B,MAAM,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAC;YAErC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,2CAA2C;QAClF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,iBAAiB;YACjB,MAAM,cAAc,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACtD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC;gBACJ,IAAI,EAAE,uBAAuB;gBAC7B,WAAW,EAAE,0BAA0B;gBACvC,QAAQ,EAAE,aAAa;aACxB,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,MAAM,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAE3C,oBAAoB;YACpB,MAAM,WAAW,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACnD,GAAG,CAAC,sBAAsB,MAAM,EAAE,CAAC;iBACnC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEhE,kBAAkB;YAClB,MAAM,cAAc,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACtD,GAAG,CAAC,sBAAsB,MAAM,EAAE,CAAC;iBACnC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC;gBACJ,IAAI,EAAE,+BAA+B;gBACrC,WAAW,EAAE,qBAAqB;gBAClC,QAAQ,EAAE,qBAAqB;aAChC,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,oBAAoB;YACpB,MAAM,kBAAkB,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC1D,GAAG,CAAC,sBAAsB,MAAM,EAAE,CAAC;iBACnC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAChF,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\__tests__\\e2e\\api-workflow.e2e-spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { INestApplication } from '@nestjs/common';\r\nimport { ConfigModule } from '@nestjs/config';\r\nimport request from 'supertest';\r\nimport { AppModule } from '../../app.module';\r\n\r\ndescribe('API Workflow (e2e)', () => {\r\n  let app: INestApplication;\r\n  let authToken: string;\r\n  let userId: string;\r\n\r\n  beforeAll(async () => {\r\n    const moduleFixture: TestingModule = await Test.createTestingModule({\r\n      imports: [\r\n        ConfigModule.forRoot({\r\n          isGlobal: true,\r\n          envFilePath: '.env.test',\r\n        }),\r\n        AppModule,\r\n      ],\r\n    }).compile();\r\n\r\n    app = moduleFixture.createNestApplication();\r\n    await app.init();\r\n\r\n    // Setup authentication for protected endpoints\r\n    const authResponse = await request(app.getHttpServer())\r\n      .post('/api/v1/auth/register')\r\n      .send({\r\n        email: '<EMAIL>',\r\n        password: 'SecurePassword123!',\r\n        name: 'API Test User',\r\n      });\r\n\r\n    authToken = authResponse.body.data.tokens.accessToken;\r\n    userId = authResponse.body.data.user.id;\r\n  });\r\n\r\n  afterAll(async () => {\r\n    await app.close();\r\n  });\r\n\r\n  describe('API Versioning', () => {\r\n    it('should handle v1 API requests', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/health')\r\n        .expect(200);\r\n\r\n      expect(response.body).toEqual({\r\n        status: 'ok',\r\n        timestamp: expect.any(String),\r\n        uptime: expect.any(Number),\r\n        version: '1.0.0',\r\n        environment: 'test',\r\n      });\r\n    });\r\n\r\n    it('should handle v2 API requests', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v2/health')\r\n        .expect(200);\r\n\r\n      expect(response.body).toEqual({\r\n        status: 'healthy',\r\n        timestamp: expect.any(String),\r\n        uptime: expect.any(Number),\r\n        version: '2.0.0',\r\n        environment: 'test',\r\n        services: expect.any(Object),\r\n      });\r\n    });\r\n\r\n    it('should include version headers', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/health')\r\n        .set('Accept', 'application/vnd.api+json;version=1.0')\r\n        .expect(200);\r\n\r\n      expect(response.headers['x-api-version']).toBe('1.0');\r\n    });\r\n\r\n    it('should handle version negotiation', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/health')\r\n        .set('X-API-Version', '1.0')\r\n        .expect(200);\r\n\r\n      expect(response.body.version).toBe('1.0.0');\r\n    });\r\n\r\n    it('should reject unsupported versions', async () => {\r\n      await request(app.getHttpServer())\r\n        .get('/api/v999/health')\r\n        .expect(404);\r\n    });\r\n  });\r\n\r\n  describe('Request/Response Flow', () => {\r\n    it('should handle GET requests with query parameters', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/health')\r\n        .query({\r\n          detailed: 'true',\r\n          format: 'json',\r\n        })\r\n        .expect(200);\r\n\r\n      expect(response.body.status).toBe('ok');\r\n    });\r\n\r\n    it('should handle POST requests with JSON body', async () => {\r\n      const testData = {\r\n        name: 'Test Item',\r\n        description: 'Test Description',\r\n        category: 'test',\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/test/items')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send(testData)\r\n        .expect(201);\r\n\r\n      expect(response.body).toEqual({\r\n        success: true,\r\n        data: expect.objectContaining({\r\n          id: expect.any(String),\r\n          ...testData,\r\n          createdAt: expect.any(String),\r\n          updatedAt: expect.any(String),\r\n        }),\r\n        message: 'Item created successfully',\r\n        timestamp: expect.any(String),\r\n      });\r\n    });\r\n\r\n    it('should handle PUT requests for updates', async () => {\r\n      // First create an item\r\n      const createResponse = await request(app.getHttpServer())\r\n        .post('/api/v1/test/items')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send({\r\n          name: 'Original Item',\r\n          description: 'Original Description',\r\n          category: 'test',\r\n        })\r\n        .expect(201);\r\n\r\n      const itemId = createResponse.body.data.id;\r\n\r\n      // Then update it\r\n      const updateData = {\r\n        name: 'Updated Item',\r\n        description: 'Updated Description',\r\n        category: 'updated',\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .put(`/api/v1/test/items/${itemId}`)\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send(updateData)\r\n        .expect(200);\r\n\r\n      expect(response.body.data).toEqual(\r\n        expect.objectContaining({\r\n          id: itemId,\r\n          ...updateData,\r\n          updatedAt: expect.any(String),\r\n        })\r\n      );\r\n    });\r\n\r\n    it('should handle DELETE requests', async () => {\r\n      // First create an item\r\n      const createResponse = await request(app.getHttpServer())\r\n        .post('/api/v1/test/items')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send({\r\n          name: 'Item to Delete',\r\n          description: 'Will be deleted',\r\n          category: 'test',\r\n        })\r\n        .expect(201);\r\n\r\n      const itemId = createResponse.body.data.id;\r\n\r\n      // Then delete it\r\n      const response = await request(app.getHttpServer())\r\n        .delete(`/api/v1/test/items/${itemId}`)\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .expect(200);\r\n\r\n      expect(response.body.message).toBe('Item deleted successfully');\r\n\r\n      // Verify it's deleted\r\n      await request(app.getHttpServer())\r\n        .get(`/api/v1/test/items/${itemId}`)\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .expect(404);\r\n    });\r\n  });\r\n\r\n  describe('Pagination Workflow', () => {\r\n    beforeAll(async () => {\r\n      // Create multiple items for pagination testing\r\n      const items = Array.from({ length: 25 }, (_, i) => ({\r\n        name: `Test Item ${i + 1}`,\r\n        description: `Description for item ${i + 1}`,\r\n        category: 'pagination-test',\r\n      }));\r\n\r\n      for (const item of items) {\r\n        await request(app.getHttpServer())\r\n          .post('/api/v1/test/items')\r\n          .set('Authorization', `Bearer ${authToken}`)\r\n          .send(item);\r\n      }\r\n    });\r\n\r\n    it('should handle default pagination', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/test/items')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .expect(200);\r\n\r\n      expect(response.body).toEqual({\r\n        success: true,\r\n        data: expect.any(Array),\r\n        message: 'Items retrieved successfully',\r\n        timestamp: expect.any(String),\r\n        pagination: {\r\n          page: 1,\r\n          limit: 10,\r\n          total: expect.any(Number),\r\n          totalPages: expect.any(Number),\r\n          hasNext: expect.any(Boolean),\r\n          hasPrev: false,\r\n          offset: 0,\r\n        },\r\n        metadata: expect.any(Object),\r\n      });\r\n\r\n      expect(response.body.data.length).toBeLessThanOrEqual(10);\r\n    });\r\n\r\n    it('should handle custom pagination parameters', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/test/items')\r\n        .query({\r\n          page: 2,\r\n          limit: 5,\r\n        })\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .expect(200);\r\n\r\n      expect(response.body.pagination).toEqual({\r\n        page: 2,\r\n        limit: 5,\r\n        total: expect.any(Number),\r\n        totalPages: expect.any(Number),\r\n        hasNext: expect.any(Boolean),\r\n        hasPrev: true,\r\n        offset: 5,\r\n      });\r\n\r\n      expect(response.body.data.length).toBeLessThanOrEqual(5);\r\n    });\r\n\r\n    it('should handle sorting parameters', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/test/items')\r\n        .query({\r\n          sortBy: 'name',\r\n          sortOrder: 'asc',\r\n          limit: 5,\r\n        })\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .expect(200);\r\n\r\n      const items = response.body.data;\r\n      expect(items.length).toBeGreaterThan(0);\r\n      \r\n      // Verify sorting\r\n      for (let i = 1; i < items.length; i++) {\r\n        expect(items[i].name >= items[i - 1].name).toBe(true);\r\n      }\r\n    });\r\n\r\n    it('should handle filtering parameters', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/test/items')\r\n        .query({\r\n          filter_category: 'pagination-test',\r\n          filter_name: 'Test Item 1',\r\n        })\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .expect(200);\r\n\r\n      expect(response.body.data.length).toBeGreaterThan(0);\r\n      response.body.data.forEach(item => {\r\n        expect(item.category).toBe('pagination-test');\r\n        expect(item.name).toContain('Test Item 1');\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Validation Workflow', () => {\r\n    it('should validate required fields', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/test/items')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send({\r\n          description: 'Missing name field',\r\n        })\r\n        .expect(400);\r\n\r\n      expect(response.body.message).toContain('name');\r\n    });\r\n\r\n    it('should validate field types', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/test/items')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send({\r\n          name: 123, // Should be string\r\n          description: 'Type validation test',\r\n          category: 'test',\r\n        })\r\n        .expect(400);\r\n\r\n      expect(response.body.message).toContain('name must be a string');\r\n    });\r\n\r\n    it('should validate field lengths', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/test/items')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send({\r\n          name: 'a'.repeat(256), // Too long\r\n          description: 'Length validation test',\r\n          category: 'test',\r\n        })\r\n        .expect(400);\r\n\r\n      expect(response.body.message).toContain('name must be shorter than');\r\n    });\r\n\r\n    it('should sanitize input data', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/test/items')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send({\r\n          name: '<script>alert(\"xss\")</script>Clean Name',\r\n          description: 'XSS test description',\r\n          category: 'test',\r\n        })\r\n        .expect(201);\r\n\r\n      expect(response.body.data.name).toBe('Clean Name');\r\n      expect(response.body.data.name).not.toContain('<script>');\r\n    });\r\n  });\r\n\r\n  describe('Error Handling Workflow', () => {\r\n    it('should handle 404 errors for non-existent resources', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/test/items/non-existent-id')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .expect(404);\r\n\r\n      expect(response.body).toEqual({\r\n        statusCode: 404,\r\n        message: 'Item not found',\r\n        error: 'Not Found',\r\n        timestamp: expect.any(String),\r\n        path: '/api/v1/test/items/non-existent-id',\r\n        method: 'GET',\r\n        requestId: expect.any(String),\r\n      });\r\n    });\r\n\r\n    it('should handle authorization errors', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/test/items')\r\n        .expect(401);\r\n\r\n      expect(response.body.statusCode).toBe(401);\r\n      expect(response.body.message).toContain('Unauthorized');\r\n    });\r\n\r\n    it('should handle permission errors', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/admin/users')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .expect(403);\r\n\r\n      expect(response.body.statusCode).toBe(403);\r\n      expect(response.body.message).toContain('Forbidden');\r\n    });\r\n\r\n    it('should handle rate limiting', async () => {\r\n      // Make multiple rapid requests to trigger rate limiting\r\n      const requests = Array.from({ length: 20 }, () =>\r\n        request(app.getHttpServer())\r\n          .get('/api/v1/health')\r\n      );\r\n\r\n      const responses = await Promise.all(requests.map(req => \r\n        req.then(res => ({ status: res.status, headers: res.headers }))\r\n          .catch(err => ({ status: err.response?.status || 500, headers: {} }))\r\n      ));\r\n\r\n      // Some requests should be rate limited\r\n      const rateLimitedResponses = responses.filter(r => r.status === 429);\r\n      \r\n      if (rateLimitedResponses.length > 0) {\r\n        expect(rateLimitedResponses[0].headers['x-ratelimit-limit']).toBeDefined();\r\n        expect(rateLimitedResponses[0].headers['x-ratelimit-remaining']).toBeDefined();\r\n      }\r\n    });\r\n  });\r\n\r\n  describe('Content Negotiation', () => {\r\n    it('should handle JSON content type', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/health')\r\n        .set('Accept', 'application/json')\r\n        .expect(200);\r\n\r\n      expect(response.headers['content-type']).toMatch(/application\\/json/);\r\n    });\r\n\r\n    it('should handle different API versions in Accept header', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/health')\r\n        .set('Accept', 'application/vnd.api+json;version=1.0')\r\n        .expect(200);\r\n\r\n      expect(response.body.version).toBe('1.0.0');\r\n    });\r\n\r\n    it('should handle compression', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/health')\r\n        .set('Accept-Encoding', 'gzip, deflate')\r\n        .expect(200);\r\n\r\n      // Response should be successful regardless of compression\r\n      expect(response.body.status).toBe('ok');\r\n    });\r\n  });\r\n\r\n  describe('Security Headers', () => {\r\n    it('should include security headers in responses', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/health')\r\n        .expect(200);\r\n\r\n      // Check for common security headers\r\n      expect(response.headers).toBeDefined();\r\n      // In a real implementation, you would check for specific headers like:\r\n      // expect(response.headers['x-frame-options']).toBe('DENY');\r\n      // expect(response.headers['x-content-type-options']).toBe('nosniff');\r\n    });\r\n\r\n    it('should handle CORS preflight requests', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .options('/api/v1/health')\r\n        .set('Origin', 'http://localhost:3000')\r\n        .set('Access-Control-Request-Method', 'GET')\r\n        .expect(200);\r\n\r\n      expect(response.headers['access-control-allow-origin']).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('Monitoring and Observability', () => {\r\n    it('should include correlation IDs in responses', async () => {\r\n      const correlationId = 'test-correlation-123';\r\n      \r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/health')\r\n        .set('X-Correlation-ID', correlationId)\r\n        .expect(200);\r\n\r\n      // In a real implementation, the correlation ID would be included in response headers\r\n      expect(response.headers).toBeDefined();\r\n    });\r\n\r\n    it('should track request metrics', async () => {\r\n      const startTime = Date.now();\r\n      \r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/health')\r\n        .expect(200);\r\n      \r\n      const endTime = Date.now();\r\n      const duration = endTime - startTime;\r\n\r\n      expect(duration).toBeLessThan(1000); // Should respond quickly\r\n      expect(response.body.uptime).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should provide health check information', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/health')\r\n        .expect(200);\r\n\r\n      expect(response.body).toEqual({\r\n        status: 'ok',\r\n        timestamp: expect.any(String),\r\n        uptime: expect.any(Number),\r\n        version: '1.0.0',\r\n        environment: 'test',\r\n      });\r\n    });\r\n\r\n    it('should provide detailed health information', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/health/detailed')\r\n        .expect(200);\r\n\r\n      expect(response.body).toEqual({\r\n        status: 'ok',\r\n        timestamp: expect.any(String),\r\n        uptime: expect.any(Number),\r\n        version: '1.0.0',\r\n        environment: 'test',\r\n        services: {\r\n          database: expect.any(String),\r\n          redis: expect.any(String),\r\n          external_apis: expect.any(String),\r\n        },\r\n        memory: expect.any(Object),\r\n        cpu: expect.any(Object),\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Performance', () => {\r\n    it('should handle concurrent requests efficiently', async () => {\r\n      const startTime = Date.now();\r\n      \r\n      const requests = Array.from({ length: 10 }, () =>\r\n        request(app.getHttpServer()).get('/api/v1/health')\r\n      );\r\n\r\n      const responses = await Promise.all(requests);\r\n      \r\n      const endTime = Date.now();\r\n      const duration = endTime - startTime;\r\n\r\n      // All requests should succeed\r\n      responses.forEach(response => {\r\n        expect(response.status).toBe(200);\r\n      });\r\n\r\n      // Should handle concurrent requests efficiently\r\n      expect(duration).toBeLessThan(2000);\r\n    });\r\n\r\n    it('should handle large payloads efficiently', async () => {\r\n      const largePayload = {\r\n        name: 'Large Item',\r\n        description: 'A'.repeat(1000), // Large description\r\n        category: 'performance-test',\r\n        metadata: Array.from({ length: 100 }, (_, i) => ({\r\n          key: `key${i}`,\r\n          value: `value${i}`,\r\n        })),\r\n      };\r\n\r\n      const startTime = Date.now();\r\n      \r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/test/items')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send(largePayload)\r\n        .expect(201);\r\n      \r\n      const endTime = Date.now();\r\n      const duration = endTime - startTime;\r\n\r\n      expect(response.body.data.name).toBe(largePayload.name);\r\n      expect(duration).toBeLessThan(2000); // Should handle large payloads efficiently\r\n    });\r\n  });\r\n\r\n  describe('Data Consistency', () => {\r\n    it('should maintain data consistency across operations', async () => {\r\n      // Create an item\r\n      const createResponse = await request(app.getHttpServer())\r\n        .post('/api/v1/test/items')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send({\r\n          name: 'Consistency Test Item',\r\n          description: 'Testing data consistency',\r\n          category: 'consistency',\r\n        })\r\n        .expect(201);\r\n\r\n      const itemId = createResponse.body.data.id;\r\n\r\n      // Retrieve the item\r\n      const getResponse = await request(app.getHttpServer())\r\n        .get(`/api/v1/test/items/${itemId}`)\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .expect(200);\r\n\r\n      expect(getResponse.body.data).toEqual(createResponse.body.data);\r\n\r\n      // Update the item\r\n      const updateResponse = await request(app.getHttpServer())\r\n        .put(`/api/v1/test/items/${itemId}`)\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send({\r\n          name: 'Updated Consistency Test Item',\r\n          description: 'Updated description',\r\n          category: 'updated-consistency',\r\n        })\r\n        .expect(200);\r\n\r\n      // Verify the update\r\n      const getUpdatedResponse = await request(app.getHttpServer())\r\n        .get(`/api/v1/test/items/${itemId}`)\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .expect(200);\r\n\r\n      expect(getUpdatedResponse.body.data.name).toBe('Updated Consistency Test Item');\r\n      expect(getUpdatedResponse.body.data.updatedAt).not.toBe(createResponse.body.data.updatedAt);\r\n    });\r\n  });\r\n});"], "version": 3}