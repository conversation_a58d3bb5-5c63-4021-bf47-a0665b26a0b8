2d03c04c26158731ed1faf66dbaf9657
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const report_definition_controller_1 = require("../../../controllers/report-definition.controller");
const report_definition_service_1 = require("../../../services/report-definition.service");
const report_definition_response_dto_1 = require("../../../dto/report-definition-response.dto");
/**
 * Unit Tests for Report Definition Controller
 *
 * Tests all controller endpoints including:
 * - Request/response validation and transformation
 * - Error handling and HTTP status codes
 * - Authentication and authorization
 * - Input validation and sanitization
 * - Response formatting and serialization
 */
describe('ReportDefinitionController', () => {
    let controller;
    let service;
    const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        roles: ['compliance_admin'],
    };
    const mockReportDefinition = {
        id: 'report-123',
        name: 'Test Report',
        description: 'Test report description',
        category: 'compliance',
        type: 'tabular',
        dataSource: 'compliance',
        query: 'SELECT * FROM compliance_data',
        parameters: [],
        outputFormats: ['pdf', 'excel'],
        schedule: {
            enabled: true,
            cronExpression: '0 9 * * 1',
            timezone: 'UTC',
        },
        isActive: true,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        createdBy: 'user-123',
        updatedBy: 'user-123',
    };
    beforeEach(async () => {
        const mockService = {
            createReportDefinition: jest.fn(),
            getReportDefinitionById: jest.fn(),
            updateReportDefinition: jest.fn(),
            deleteReportDefinition: jest.fn(),
            getReportDefinitions: jest.fn(),
            validateReportDefinition: jest.fn(),
            duplicateReportDefinition: jest.fn(),
            getReportDefinitionVersions: jest.fn(),
        };
        const module = await testing_1.Test.createTestingModule({
            controllers: [report_definition_controller_1.ReportDefinitionController],
            providers: [
                {
                    provide: report_definition_service_1.ReportDefinitionService,
                    useValue: mockService,
                },
            ],
        }).compile();
        controller = module.get(report_definition_controller_1.ReportDefinitionController);
        service = module.get(report_definition_service_1.ReportDefinitionService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('createReportDefinition', () => {
        const createDto = {
            name: 'New Report',
            description: 'New report description',
            category: 'compliance',
            type: 'tabular',
            dataSource: 'compliance',
            query: 'SELECT * FROM compliance_data',
            parameters: [],
            outputFormats: ['pdf'],
        };
        it('should create a new report definition successfully', async () => {
            service.createReportDefinition.mockResolvedValue(mockReportDefinition);
            const result = await controller.createReportDefinition(createDto, mockUser);
            expect(service.createReportDefinition).toHaveBeenCalledWith(createDto, mockUser.id);
            expect(result).toBeInstanceOf(report_definition_response_dto_1.ReportDefinitionResponseDto);
            expect(result.id).toBe(mockReportDefinition.id);
            expect(result.name).toBe(mockReportDefinition.name);
        });
        it('should handle validation errors', async () => {
            const invalidDto = { ...createDto, name: '' };
            service.createReportDefinition.mockRejectedValue(new Error('Validation failed'));
            await expect(controller.createReportDefinition(invalidDto, mockUser))
                .rejects.toThrow('Validation failed');
            expect(service.createReportDefinition).toHaveBeenCalledWith(invalidDto, mockUser.id);
        });
        it('should handle duplicate name errors', async () => {
            service.createReportDefinition.mockRejectedValue(new Error('Report definition with this name already exists'));
            await expect(controller.createReportDefinition(createDto, mockUser))
                .rejects.toThrow('Report definition with this name already exists');
        });
        it('should validate required fields', async () => {
            const incompleteDto = { name: 'Test' };
            await expect(controller.createReportDefinition(incompleteDto, mockUser))
                .rejects.toThrow();
        });
    });
    describe('getReportDefinitionById', () => {
        it('should return report definition by ID', async () => {
            service.getReportDefinitionById.mockResolvedValue(mockReportDefinition);
            const result = await controller.getReportDefinitionById('report-123', mockUser);
            expect(service.getReportDefinitionById).toHaveBeenCalledWith('report-123');
            expect(result).toBeInstanceOf(report_definition_response_dto_1.ReportDefinitionResponseDto);
            expect(result.id).toBe('report-123');
        });
        it('should throw 404 for non-existent report definition', async () => {
            service.getReportDefinitionById.mockResolvedValue(null);
            await expect(controller.getReportDefinitionById('non-existent', mockUser))
                .rejects.toThrow('Report definition not found');
        });
        it('should validate UUID format', async () => {
            await expect(controller.getReportDefinitionById('invalid-uuid', mockUser))
                .rejects.toThrow();
        });
    });
    describe('updateReportDefinition', () => {
        const updateDto = {
            name: 'Updated Report',
            description: 'Updated description',
        };
        it('should update report definition successfully', async () => {
            const updatedReport = { ...mockReportDefinition, ...updateDto };
            service.updateReportDefinition.mockResolvedValue(updatedReport);
            const result = await controller.updateReportDefinition('report-123', updateDto, mockUser);
            expect(service.updateReportDefinition).toHaveBeenCalledWith('report-123', updateDto, mockUser.id);
            expect(result).toBeInstanceOf(report_definition_response_dto_1.ReportDefinitionResponseDto);
            expect(result.name).toBe(updateDto.name);
        });
        it('should handle partial updates', async () => {
            const partialUpdate = { description: 'New description only' };
            const updatedReport = { ...mockReportDefinition, ...partialUpdate };
            service.updateReportDefinition.mockResolvedValue(updatedReport);
            const result = await controller.updateReportDefinition('report-123', partialUpdate, mockUser);
            expect(result.description).toBe(partialUpdate.description);
            expect(result.name).toBe(mockReportDefinition.name); // Should remain unchanged
        });
        it('should throw 404 for non-existent report definition', async () => {
            service.updateReportDefinition.mockRejectedValue(new Error('Report definition not found'));
            await expect(controller.updateReportDefinition('non-existent', updateDto, mockUser))
                .rejects.toThrow('Report definition not found');
        });
    });
    describe('deleteReportDefinition', () => {
        it('should delete report definition successfully', async () => {
            service.deleteReportDefinition.mockResolvedValue(undefined);
            await controller.deleteReportDefinition('report-123', mockUser);
            expect(service.deleteReportDefinition).toHaveBeenCalledWith('report-123', mockUser.id);
        });
        it('should throw 404 for non-existent report definition', async () => {
            service.deleteReportDefinition.mockRejectedValue(new Error('Report definition not found'));
            await expect(controller.deleteReportDefinition('non-existent', mockUser))
                .rejects.toThrow('Report definition not found');
        });
    });
    describe('getReportDefinitions', () => {
        const mockPaginatedResult = {
            reportDefinitions: [mockReportDefinition],
            total: 1,
            page: 1,
            limit: 20,
            totalPages: 1,
        };
        it('should return paginated report definitions', async () => {
            service.getReportDefinitions.mockResolvedValue(mockPaginatedResult);
            const result = await controller.getReportDefinitions(1, 20, 'compliance', 'tabular', 'test', mockUser);
            expect(service.getReportDefinitions).toHaveBeenCalledWith({
                page: 1,
                limit: 20,
                category: 'compliance',
                type: 'tabular',
                search: 'test',
            });
            expect(result.reportDefinitions).toHaveLength(1);
            expect(result.total).toBe(1);
            expect(result.page).toBe(1);
        });
        it('should handle default pagination parameters', async () => {
            service.getReportDefinitions.mockResolvedValue(mockPaginatedResult);
            await controller.getReportDefinitions(undefined, undefined, undefined, undefined, undefined, mockUser);
            expect(service.getReportDefinitions).toHaveBeenCalledWith({
                page: 1,
                limit: 20,
                category: undefined,
                type: undefined,
                search: undefined,
            });
        });
        it('should validate pagination limits', async () => {
            service.getReportDefinitions.mockResolvedValue(mockPaginatedResult);
            await controller.getReportDefinitions(1, 150, undefined, undefined, undefined, mockUser);
            expect(service.getReportDefinitions).toHaveBeenCalledWith({
                page: 1,
                limit: 100, // Should be capped at 100
                category: undefined,
                type: undefined,
                search: undefined,
            });
        });
    });
    describe('validateReportDefinition', () => {
        const validationDto = {
            name: 'Test Report',
            dataSource: 'compliance',
            query: 'SELECT * FROM compliance_data',
            parameters: [],
        };
        it('should validate report definition successfully', async () => {
            const validationResult = {
                isValid: true,
                errors: [],
                warnings: [],
            };
            service.validateReportDefinition.mockResolvedValue(validationResult);
            const result = await controller.validateReportDefinition(validationDto, mockUser);
            expect(service.validateReportDefinition).toHaveBeenCalledWith(validationDto);
            expect(result.isValid).toBe(true);
            expect(result.errors).toEqual([]);
        });
        it('should return validation errors', async () => {
            const validationResult = {
                isValid: false,
                errors: ['Invalid query syntax', 'Missing required parameter'],
                warnings: ['Performance may be impacted'],
            };
            service.validateReportDefinition.mockResolvedValue(validationResult);
            const result = await controller.validateReportDefinition(validationDto, mockUser);
            expect(result.isValid).toBe(false);
            expect(result.errors).toHaveLength(2);
            expect(result.warnings).toHaveLength(1);
        });
    });
    describe('duplicateReportDefinition', () => {
        it('should duplicate report definition successfully', async () => {
            const duplicatedReport = {
                ...mockReportDefinition,
                id: 'report-456',
                name: 'Copy of Test Report',
            };
            service.duplicateReportDefinition.mockResolvedValue(duplicatedReport);
            const result = await controller.duplicateReportDefinition('report-123', mockUser);
            expect(service.duplicateReportDefinition).toHaveBeenCalledWith('report-123', mockUser.id);
            expect(result).toBeInstanceOf(report_definition_response_dto_1.ReportDefinitionResponseDto);
            expect(result.id).toBe('report-456');
            expect(result.name).toBe('Copy of Test Report');
        });
        it('should throw 404 for non-existent source report', async () => {
            service.duplicateReportDefinition.mockRejectedValue(new Error('Source report definition not found'));
            await expect(controller.duplicateReportDefinition('non-existent', mockUser))
                .rejects.toThrow('Source report definition not found');
        });
    });
    describe('getReportDefinitionVersions', () => {
        const mockVersions = [
            { ...mockReportDefinition, version: 1 },
            { ...mockReportDefinition, version: 2, name: 'Updated Test Report' },
        ];
        it('should return report definition versions', async () => {
            service.getReportDefinitionVersions.mockResolvedValue(mockVersions);
            const result = await controller.getReportDefinitionVersions('report-123', mockUser);
            expect(service.getReportDefinitionVersions).toHaveBeenCalledWith('report-123');
            expect(result).toHaveLength(2);
            expect(result[0].version).toBe(1);
            expect(result[1].version).toBe(2);
        });
        it('should throw 404 for non-existent report definition', async () => {
            service.getReportDefinitionVersions.mockRejectedValue(new Error('Report definition not found'));
            await expect(controller.getReportDefinitionVersions('non-existent', mockUser))
                .rejects.toThrow('Report definition not found');
        });
    });
    describe('error handling', () => {
        it('should handle service errors gracefully', async () => {
            service.getReportDefinitionById.mockRejectedValue(new Error('Database connection failed'));
            await expect(controller.getReportDefinitionById('report-123', mockUser))
                .rejects.toThrow('Database connection failed');
        });
        it('should handle validation pipe errors', async () => {
            const invalidDto = { name: 123 }; // Invalid type
            await expect(controller.createReportDefinition(invalidDto, mockUser))
                .rejects.toThrow();
        });
    });
    describe('authorization', () => {
        it('should allow access for compliance_admin role', async () => {
            service.getReportDefinitionById.mockResolvedValue(mockReportDefinition);
            const result = await controller.getReportDefinitionById('report-123', mockUser);
            expect(result).toBeDefined();
        });
        it('should handle unauthorized access', async () => {
            const unauthorizedUser = { ...mockUser, roles: ['viewer'] };
            // This would be handled by guards in actual implementation
            // Here we're just testing the controller logic
            service.getReportDefinitionById.mockResolvedValue(mockReportDefinition);
            const result = await controller.getReportDefinitionById('report-123', unauthorizedUser);
            expect(result).toBeDefined();
        });
    });
    describe('response transformation', () => {
        it('should transform entity to response DTO', async () => {
            service.getReportDefinitionById.mockResolvedValue(mockReportDefinition);
            const result = await controller.getReportDefinitionById('report-123', mockUser);
            expect(result).toBeInstanceOf(report_definition_response_dto_1.ReportDefinitionResponseDto);
            expect(result).toHaveProperty('id');
            expect(result).toHaveProperty('name');
            expect(result).toHaveProperty('description');
            expect(result).toHaveProperty('metadata');
        });
        it('should include metadata in response', async () => {
            service.getReportDefinitionById.mockResolvedValue(mockReportDefinition);
            const result = await controller.getReportDefinitionById('report-123', mockUser);
            expect(result.metadata).toBeDefined();
            expect(result.metadata.createdAt).toBeDefined();
            expect(result.metadata.updatedAt).toBeDefined();
            expect(result.metadata.createdBy).toBe('user-123');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxtb2R1bGVzXFxyZXBvcnRpbmdcXHRlc3RzXFx1bml0XFxjb250cm9sbGVyc1xccmVwb3J0LWRlZmluaXRpb24uY29udHJvbGxlci5zcGVjLnRzIiwibWFwcGluZ3MiOiI7O0FBQUEsNkNBQXNEO0FBRXRELG9HQUErRjtBQUMvRiwyRkFBc0Y7QUFHdEYsZ0dBQTBGO0FBRTFGOzs7Ozs7Ozs7R0FTRztBQUNILFFBQVEsQ0FBQyw0QkFBNEIsRUFBRSxHQUFHLEVBQUU7SUFDMUMsSUFBSSxVQUFzQyxDQUFDO0lBQzNDLElBQUksT0FBNkMsQ0FBQztJQUVsRCxNQUFNLFFBQVEsR0FBRztRQUNmLEVBQUUsRUFBRSxVQUFVO1FBQ2QsS0FBSyxFQUFFLGtCQUFrQjtRQUN6QixLQUFLLEVBQUUsQ0FBQyxrQkFBa0IsQ0FBQztLQUM1QixDQUFDO0lBRUYsTUFBTSxvQkFBb0IsR0FBRztRQUMzQixFQUFFLEVBQUUsWUFBWTtRQUNoQixJQUFJLEVBQUUsYUFBYTtRQUNuQixXQUFXLEVBQUUseUJBQXlCO1FBQ3RDLFFBQVEsRUFBRSxZQUFZO1FBQ3RCLElBQUksRUFBRSxTQUFTO1FBQ2YsVUFBVSxFQUFFLFlBQVk7UUFDeEIsS0FBSyxFQUFFLCtCQUErQjtRQUN0QyxVQUFVLEVBQUUsRUFBRTtRQUNkLGFBQWEsRUFBRSxDQUFDLEtBQUssRUFBRSxPQUFPLENBQUM7UUFDL0IsUUFBUSxFQUFFO1lBQ1IsT0FBTyxFQUFFLElBQUk7WUFDYixjQUFjLEVBQUUsV0FBVztZQUMzQixRQUFRLEVBQUUsS0FBSztTQUNoQjtRQUNELFFBQVEsRUFBRSxJQUFJO1FBQ2QsU0FBUyxFQUFFLElBQUksSUFBSSxDQUFDLFlBQVksQ0FBQztRQUNqQyxTQUFTLEVBQUUsSUFBSSxJQUFJLENBQUMsWUFBWSxDQUFDO1FBQ2pDLFNBQVMsRUFBRSxVQUFVO1FBQ3JCLFNBQVMsRUFBRSxVQUFVO0tBQ3RCLENBQUM7SUFFRixVQUFVLENBQUMsS0FBSyxJQUFJLEVBQUU7UUFDcEIsTUFBTSxXQUFXLEdBQUc7WUFDbEIsc0JBQXNCLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtZQUNqQyx1QkFBdUIsRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1lBQ2xDLHNCQUFzQixFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7WUFDakMsc0JBQXNCLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtZQUNqQyxvQkFBb0IsRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1lBQy9CLHdCQUF3QixFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7WUFDbkMseUJBQXlCLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtZQUNwQywyQkFBMkIsRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1NBQ3ZDLENBQUM7UUFFRixNQUFNLE1BQU0sR0FBa0IsTUFBTSxjQUFJLENBQUMsbUJBQW1CLENBQUM7WUFDM0QsV0FBVyxFQUFFLENBQUMseURBQTBCLENBQUM7WUFDekMsU0FBUyxFQUFFO2dCQUNUO29CQUNFLE9BQU8sRUFBRSxtREFBdUI7b0JBQ2hDLFFBQVEsRUFBRSxXQUFXO2lCQUN0QjthQUNGO1NBQ0YsQ0FBQyxDQUFDLE9BQU8sRUFBRSxDQUFDO1FBRWIsVUFBVSxHQUFHLE1BQU0sQ0FBQyxHQUFHLENBQTZCLHlEQUEwQixDQUFDLENBQUM7UUFDaEYsT0FBTyxHQUFHLE1BQU0sQ0FBQyxHQUFHLENBQUMsbURBQXVCLENBQUMsQ0FBQztJQUNoRCxDQUFDLENBQUMsQ0FBQztJQUVILFNBQVMsQ0FBQyxHQUFHLEVBQUU7UUFDYixJQUFJLENBQUMsYUFBYSxFQUFFLENBQUM7SUFDdkIsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsd0JBQXdCLEVBQUUsR0FBRyxFQUFFO1FBQ3RDLE1BQU0sU0FBUyxHQUE4QjtZQUMzQyxJQUFJLEVBQUUsWUFBWTtZQUNsQixXQUFXLEVBQUUsd0JBQXdCO1lBQ3JDLFFBQVEsRUFBRSxZQUFZO1lBQ3RCLElBQUksRUFBRSxTQUFTO1lBQ2YsVUFBVSxFQUFFLFlBQVk7WUFDeEIsS0FBSyxFQUFFLCtCQUErQjtZQUN0QyxVQUFVLEVBQUUsRUFBRTtZQUNkLGFBQWEsRUFBRSxDQUFDLEtBQUssQ0FBQztTQUN2QixDQUFDO1FBRUYsRUFBRSxDQUFDLG9EQUFvRCxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQ2xFLE9BQU8sQ0FBQyxzQkFBc0IsQ0FBQyxpQkFBaUIsQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDO1lBRXZFLE1BQU0sTUFBTSxHQUFHLE1BQU0sVUFBVSxDQUFDLHNCQUFzQixDQUFDLFNBQVMsRUFBRSxRQUFRLENBQUMsQ0FBQztZQUU1RSxNQUFNLENBQUMsT0FBTyxDQUFDLHNCQUFzQixDQUFDLENBQUMsb0JBQW9CLENBQUMsU0FBUyxFQUFFLFFBQVEsQ0FBQyxFQUFFLENBQUMsQ0FBQztZQUNwRixNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsY0FBYyxDQUFDLDREQUEyQixDQUFDLENBQUM7WUFDM0QsTUFBTSxDQUFDLE1BQU0sQ0FBQyxFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsb0JBQW9CLENBQUMsRUFBRSxDQUFDLENBQUM7WUFDaEQsTUFBTSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxJQUFJLENBQUMsb0JBQW9CLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDdEQsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsaUNBQWlDLEVBQUUsS0FBSyxJQUFJLEVBQUU7WUFDL0MsTUFBTSxVQUFVLEdBQUcsRUFBRSxHQUFHLFNBQVMsRUFBRSxJQUFJLEVBQUUsRUFBRSxFQUFFLENBQUM7WUFDOUMsT0FBTyxDQUFDLHNCQUFzQixDQUFDLGlCQUFpQixDQUFDLElBQUksS0FBSyxDQUFDLG1CQUFtQixDQUFDLENBQUMsQ0FBQztZQUVqRixNQUFNLE1BQU0sQ0FBQyxVQUFVLENBQUMsc0JBQXNCLENBQUMsVUFBVSxFQUFFLFFBQVEsQ0FBQyxDQUFDO2lCQUNsRSxPQUFPLENBQUMsT0FBTyxDQUFDLG1CQUFtQixDQUFDLENBQUM7WUFFeEMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxzQkFBc0IsQ0FBQyxDQUFDLG9CQUFvQixDQUFDLFVBQVUsRUFBRSxRQUFRLENBQUMsRUFBRSxDQUFDLENBQUM7UUFDdkYsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMscUNBQXFDLEVBQUUsS0FBSyxJQUFJLEVBQUU7WUFDbkQsT0FBTyxDQUFDLHNCQUFzQixDQUFDLGlCQUFpQixDQUM5QyxJQUFJLEtBQUssQ0FBQyxpREFBaUQsQ0FBQyxDQUM3RCxDQUFDO1lBRUYsTUFBTSxNQUFNLENBQUMsVUFBVSxDQUFDLHNCQUFzQixDQUFDLFNBQVMsRUFBRSxRQUFRLENBQUMsQ0FBQztpQkFDakUsT0FBTyxDQUFDLE9BQU8sQ0FBQyxpREFBaUQsQ0FBQyxDQUFDO1FBQ3hFLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLGlDQUFpQyxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQy9DLE1BQU0sYUFBYSxHQUFHLEVBQUUsSUFBSSxFQUFFLE1BQU0sRUFBK0IsQ0FBQztZQUVwRSxNQUFNLE1BQU0sQ0FBQyxVQUFVLENBQUMsc0JBQXNCLENBQUMsYUFBYSxFQUFFLFFBQVEsQ0FBQyxDQUFDO2lCQUNyRSxPQUFPLENBQUMsT0FBTyxFQUFFLENBQUM7UUFDdkIsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyx5QkFBeUIsRUFBRSxHQUFHLEVBQUU7UUFDdkMsRUFBRSxDQUFDLHVDQUF1QyxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQ3JELE9BQU8sQ0FBQyx1QkFBdUIsQ0FBQyxpQkFBaUIsQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDO1lBRXhFLE1BQU0sTUFBTSxHQUFHLE1BQU0sVUFBVSxDQUFDLHVCQUF1QixDQUFDLFlBQVksRUFBRSxRQUFRLENBQUMsQ0FBQztZQUVoRixNQUFNLENBQUMsT0FBTyxDQUFDLHVCQUF1QixDQUFDLENBQUMsb0JBQW9CLENBQUMsWUFBWSxDQUFDLENBQUM7WUFDM0UsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLGNBQWMsQ0FBQyw0REFBMkIsQ0FBQyxDQUFDO1lBQzNELE1BQU0sQ0FBQyxNQUFNLENBQUMsRUFBRSxDQUFDLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDO1FBQ3ZDLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLHFEQUFxRCxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQ25FLE9BQU8sQ0FBQyx1QkFBdUIsQ0FBQyxpQkFBaUIsQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUV4RCxNQUFNLE1BQU0sQ0FBQyxVQUFVLENBQUMsdUJBQXVCLENBQUMsY0FBYyxFQUFFLFFBQVEsQ0FBQyxDQUFDO2lCQUN2RSxPQUFPLENBQUMsT0FBTyxDQUFDLDZCQUE2QixDQUFDLENBQUM7UUFDcEQsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsNkJBQTZCLEVBQUUsS0FBSyxJQUFJLEVBQUU7WUFDM0MsTUFBTSxNQUFNLENBQUMsVUFBVSxDQUFDLHVCQUF1QixDQUFDLGNBQWMsRUFBRSxRQUFRLENBQUMsQ0FBQztpQkFDdkUsT0FBTyxDQUFDLE9BQU8sRUFBRSxDQUFDO1FBQ3ZCLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsd0JBQXdCLEVBQUUsR0FBRyxFQUFFO1FBQ3RDLE1BQU0sU0FBUyxHQUE4QjtZQUMzQyxJQUFJLEVBQUUsZ0JBQWdCO1lBQ3RCLFdBQVcsRUFBRSxxQkFBcUI7U0FDbkMsQ0FBQztRQUVGLEVBQUUsQ0FBQyw4Q0FBOEMsRUFBRSxLQUFLLElBQUksRUFBRTtZQUM1RCxNQUFNLGFBQWEsR0FBRyxFQUFFLEdBQUcsb0JBQW9CLEVBQUUsR0FBRyxTQUFTLEVBQUUsQ0FBQztZQUNoRSxPQUFPLENBQUMsc0JBQXNCLENBQUMsaUJBQWlCLENBQUMsYUFBYSxDQUFDLENBQUM7WUFFaEUsTUFBTSxNQUFNLEdBQUcsTUFBTSxVQUFVLENBQUMsc0JBQXNCLENBQUMsWUFBWSxFQUFFLFNBQVMsRUFBRSxRQUFRLENBQUMsQ0FBQztZQUUxRixNQUFNLENBQUMsT0FBTyxDQUFDLHNCQUFzQixDQUFDLENBQUMsb0JBQW9CLENBQUMsWUFBWSxFQUFFLFNBQVMsRUFBRSxRQUFRLENBQUMsRUFBRSxDQUFDLENBQUM7WUFDbEcsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLGNBQWMsQ0FBQyw0REFBMkIsQ0FBQyxDQUFDO1lBQzNELE1BQU0sQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUMzQyxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQywrQkFBK0IsRUFBRSxLQUFLLElBQUksRUFBRTtZQUM3QyxNQUFNLGFBQWEsR0FBRyxFQUFFLFdBQVcsRUFBRSxzQkFBc0IsRUFBRSxDQUFDO1lBQzlELE1BQU0sYUFBYSxHQUFHLEVBQUUsR0FBRyxvQkFBb0IsRUFBRSxHQUFHLGFBQWEsRUFBRSxDQUFDO1lBQ3BFLE9BQU8sQ0FBQyxzQkFBc0IsQ0FBQyxpQkFBaUIsQ0FBQyxhQUFhLENBQUMsQ0FBQztZQUVoRSxNQUFNLE1BQU0sR0FBRyxNQUFNLFVBQVUsQ0FBQyxzQkFBc0IsQ0FBQyxZQUFZLEVBQUUsYUFBYSxFQUFFLFFBQVEsQ0FBQyxDQUFDO1lBRTlGLE1BQU0sQ0FBQyxNQUFNLENBQUMsV0FBVyxDQUFDLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxXQUFXLENBQUMsQ0FBQztZQUMzRCxNQUFNLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFDLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLDBCQUEwQjtRQUNqRixDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyxxREFBcUQsRUFBRSxLQUFLLElBQUksRUFBRTtZQUNuRSxPQUFPLENBQUMsc0JBQXNCLENBQUMsaUJBQWlCLENBQUMsSUFBSSxLQUFLLENBQUMsNkJBQTZCLENBQUMsQ0FBQyxDQUFDO1lBRTNGLE1BQU0sTUFBTSxDQUFDLFVBQVUsQ0FBQyxzQkFBc0IsQ0FBQyxjQUFjLEVBQUUsU0FBUyxFQUFFLFFBQVEsQ0FBQyxDQUFDO2lCQUNqRixPQUFPLENBQUMsT0FBTyxDQUFDLDZCQUE2QixDQUFDLENBQUM7UUFDcEQsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyx3QkFBd0IsRUFBRSxHQUFHLEVBQUU7UUFDdEMsRUFBRSxDQUFDLDhDQUE4QyxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQzVELE9BQU8sQ0FBQyxzQkFBc0IsQ0FBQyxpQkFBaUIsQ0FBQyxTQUFTLENBQUMsQ0FBQztZQUU1RCxNQUFNLFVBQVUsQ0FBQyxzQkFBc0IsQ0FBQyxZQUFZLEVBQUUsUUFBUSxDQUFDLENBQUM7WUFFaEUsTUFBTSxDQUFDLE9BQU8sQ0FBQyxzQkFBc0IsQ0FBQyxDQUFDLG9CQUFvQixDQUFDLFlBQVksRUFBRSxRQUFRLENBQUMsRUFBRSxDQUFDLENBQUM7UUFDekYsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMscURBQXFELEVBQUUsS0FBSyxJQUFJLEVBQUU7WUFDbkUsT0FBTyxDQUFDLHNCQUFzQixDQUFDLGlCQUFpQixDQUFDLElBQUksS0FBSyxDQUFDLDZCQUE2QixDQUFDLENBQUMsQ0FBQztZQUUzRixNQUFNLE1BQU0sQ0FBQyxVQUFVLENBQUMsc0JBQXNCLENBQUMsY0FBYyxFQUFFLFFBQVEsQ0FBQyxDQUFDO2lCQUN0RSxPQUFPLENBQUMsT0FBTyxDQUFDLDZCQUE2QixDQUFDLENBQUM7UUFDcEQsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyxzQkFBc0IsRUFBRSxHQUFHLEVBQUU7UUFDcEMsTUFBTSxtQkFBbUIsR0FBRztZQUMxQixpQkFBaUIsRUFBRSxDQUFDLG9CQUFvQixDQUFDO1lBQ3pDLEtBQUssRUFBRSxDQUFDO1lBQ1IsSUFBSSxFQUFFLENBQUM7WUFDUCxLQUFLLEVBQUUsRUFBRTtZQUNULFVBQVUsRUFBRSxDQUFDO1NBQ2QsQ0FBQztRQUVGLEVBQUUsQ0FBQyw0Q0FBNEMsRUFBRSxLQUFLLElBQUksRUFBRTtZQUMxRCxPQUFPLENBQUMsb0JBQW9CLENBQUMsaUJBQWlCLENBQUMsbUJBQW1CLENBQUMsQ0FBQztZQUVwRSxNQUFNLE1BQU0sR0FBRyxNQUFNLFVBQVUsQ0FBQyxvQkFBb0IsQ0FDbEQsQ0FBQyxFQUFFLEVBQUUsRUFBRSxZQUFZLEVBQUUsU0FBUyxFQUFFLE1BQU0sRUFBRSxRQUFRLENBQ2pELENBQUM7WUFFRixNQUFNLENBQUMsT0FBTyxDQUFDLG9CQUFvQixDQUFDLENBQUMsb0JBQW9CLENBQUM7Z0JBQ3hELElBQUksRUFBRSxDQUFDO2dCQUNQLEtBQUssRUFBRSxFQUFFO2dCQUNULFFBQVEsRUFBRSxZQUFZO2dCQUN0QixJQUFJLEVBQUUsU0FBUztnQkFDZixNQUFNLEVBQUUsTUFBTTthQUNmLENBQUMsQ0FBQztZQUNILE1BQU0sQ0FBQyxNQUFNLENBQUMsaUJBQWlCLENBQUMsQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDakQsTUFBTSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDN0IsTUFBTSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDOUIsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsNkNBQTZDLEVBQUUsS0FBSyxJQUFJLEVBQUU7WUFDM0QsT0FBTyxDQUFDLG9CQUFvQixDQUFDLGlCQUFpQixDQUFDLG1CQUFtQixDQUFDLENBQUM7WUFFcEUsTUFBTSxVQUFVLENBQUMsb0JBQW9CLENBQUMsU0FBUyxFQUFFLFNBQVMsRUFBRSxTQUFTLEVBQUUsU0FBUyxFQUFFLFNBQVMsRUFBRSxRQUFRLENBQUMsQ0FBQztZQUV2RyxNQUFNLENBQUMsT0FBTyxDQUFDLG9CQUFvQixDQUFDLENBQUMsb0JBQW9CLENBQUM7Z0JBQ3hELElBQUksRUFBRSxDQUFDO2dCQUNQLEtBQUssRUFBRSxFQUFFO2dCQUNULFFBQVEsRUFBRSxTQUFTO2dCQUNuQixJQUFJLEVBQUUsU0FBUztnQkFDZixNQUFNLEVBQUUsU0FBUzthQUNsQixDQUFDLENBQUM7UUFDTCxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyxtQ0FBbUMsRUFBRSxLQUFLLElBQUksRUFBRTtZQUNqRCxPQUFPLENBQUMsb0JBQW9CLENBQUMsaUJBQWlCLENBQUMsbUJBQW1CLENBQUMsQ0FBQztZQUVwRSxNQUFNLFVBQVUsQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDLEVBQUUsR0FBRyxFQUFFLFNBQVMsRUFBRSxTQUFTLEVBQUUsU0FBUyxFQUFFLFFBQVEsQ0FBQyxDQUFDO1lBRXpGLE1BQU0sQ0FBQyxPQUFPLENBQUMsb0JBQW9CLENBQUMsQ0FBQyxvQkFBb0IsQ0FBQztnQkFDeEQsSUFBSSxFQUFFLENBQUM7Z0JBQ1AsS0FBSyxFQUFFLEdBQUcsRUFBRSwwQkFBMEI7Z0JBQ3RDLFFBQVEsRUFBRSxTQUFTO2dCQUNuQixJQUFJLEVBQUUsU0FBUztnQkFDZixNQUFNLEVBQUUsU0FBUzthQUNsQixDQUFDLENBQUM7UUFDTCxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLDBCQUEwQixFQUFFLEdBQUcsRUFBRTtRQUN4QyxNQUFNLGFBQWEsR0FBRztZQUNwQixJQUFJLEVBQUUsYUFBYTtZQUNuQixVQUFVLEVBQUUsWUFBWTtZQUN4QixLQUFLLEVBQUUsK0JBQStCO1lBQ3RDLFVBQVUsRUFBRSxFQUFFO1NBQ2YsQ0FBQztRQUVGLEVBQUUsQ0FBQyxnREFBZ0QsRUFBRSxLQUFLLElBQUksRUFBRTtZQUM5RCxNQUFNLGdCQUFnQixHQUFHO2dCQUN2QixPQUFPLEVBQUUsSUFBSTtnQkFDYixNQUFNLEVBQUUsRUFBRTtnQkFDVixRQUFRLEVBQUUsRUFBRTthQUNiLENBQUM7WUFDRixPQUFPLENBQUMsd0JBQXdCLENBQUMsaUJBQWlCLENBQUMsZ0JBQWdCLENBQUMsQ0FBQztZQUVyRSxNQUFNLE1BQU0sR0FBRyxNQUFNLFVBQVUsQ0FBQyx3QkFBd0IsQ0FBQyxhQUFhLEVBQUUsUUFBUSxDQUFDLENBQUM7WUFFbEYsTUFBTSxDQUFDLE9BQU8sQ0FBQyx3QkFBd0IsQ0FBQyxDQUFDLG9CQUFvQixDQUFDLGFBQWEsQ0FBQyxDQUFDO1lBQzdFLE1BQU0sQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQ2xDLE1BQU0sQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsT0FBTyxDQUFDLEVBQUUsQ0FBQyxDQUFDO1FBQ3BDLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLGlDQUFpQyxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQy9DLE1BQU0sZ0JBQWdCLEdBQUc7Z0JBQ3ZCLE9BQU8sRUFBRSxLQUFLO2dCQUNkLE1BQU0sRUFBRSxDQUFDLHNCQUFzQixFQUFFLDRCQUE0QixDQUFDO2dCQUM5RCxRQUFRLEVBQUUsQ0FBQyw2QkFBNkIsQ0FBQzthQUMxQyxDQUFDO1lBQ0YsT0FBTyxDQUFDLHdCQUF3QixDQUFDLGlCQUFpQixDQUFDLGdCQUFnQixDQUFDLENBQUM7WUFFckUsTUFBTSxNQUFNLEdBQUcsTUFBTSxVQUFVLENBQUMsd0JBQXdCLENBQUMsYUFBYSxFQUFFLFFBQVEsQ0FBQyxDQUFDO1lBRWxGLE1BQU0sQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBQ25DLE1BQU0sQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ3RDLE1BQU0sQ0FBQyxNQUFNLENBQUMsUUFBUSxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQzFDLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsMkJBQTJCLEVBQUUsR0FBRyxFQUFFO1FBQ3pDLEVBQUUsQ0FBQyxpREFBaUQsRUFBRSxLQUFLLElBQUksRUFBRTtZQUMvRCxNQUFNLGdCQUFnQixHQUFHO2dCQUN2QixHQUFHLG9CQUFvQjtnQkFDdkIsRUFBRSxFQUFFLFlBQVk7Z0JBQ2hCLElBQUksRUFBRSxxQkFBcUI7YUFDNUIsQ0FBQztZQUNGLE9BQU8sQ0FBQyx5QkFBeUIsQ0FBQyxpQkFBaUIsQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDO1lBRXRFLE1BQU0sTUFBTSxHQUFHLE1BQU0sVUFBVSxDQUFDLHlCQUF5QixDQUFDLFlBQVksRUFBRSxRQUFRLENBQUMsQ0FBQztZQUVsRixNQUFNLENBQUMsT0FBTyxDQUFDLHlCQUF5QixDQUFDLENBQUMsb0JBQW9CLENBQUMsWUFBWSxFQUFFLFFBQVEsQ0FBQyxFQUFFLENBQUMsQ0FBQztZQUMxRixNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsY0FBYyxDQUFDLDREQUEyQixDQUFDLENBQUM7WUFDM0QsTUFBTSxDQUFDLE1BQU0sQ0FBQyxFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUM7WUFDckMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxJQUFJLENBQUMscUJBQXFCLENBQUMsQ0FBQztRQUNsRCxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyxpREFBaUQsRUFBRSxLQUFLLElBQUksRUFBRTtZQUMvRCxPQUFPLENBQUMseUJBQXlCLENBQUMsaUJBQWlCLENBQUMsSUFBSSxLQUFLLENBQUMsb0NBQW9DLENBQUMsQ0FBQyxDQUFDO1lBRXJHLE1BQU0sTUFBTSxDQUFDLFVBQVUsQ0FBQyx5QkFBeUIsQ0FBQyxjQUFjLEVBQUUsUUFBUSxDQUFDLENBQUM7aUJBQ3pFLE9BQU8sQ0FBQyxPQUFPLENBQUMsb0NBQW9DLENBQUMsQ0FBQztRQUMzRCxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLDZCQUE2QixFQUFFLEdBQUcsRUFBRTtRQUMzQyxNQUFNLFlBQVksR0FBRztZQUNuQixFQUFFLEdBQUcsb0JBQW9CLEVBQUUsT0FBTyxFQUFFLENBQUMsRUFBRTtZQUN2QyxFQUFFLEdBQUcsb0JBQW9CLEVBQUUsT0FBTyxFQUFFLENBQUMsRUFBRSxJQUFJLEVBQUUscUJBQXFCLEVBQUU7U0FDckUsQ0FBQztRQUVGLEVBQUUsQ0FBQywwQ0FBMEMsRUFBRSxLQUFLLElBQUksRUFBRTtZQUN4RCxPQUFPLENBQUMsMkJBQTJCLENBQUMsaUJBQWlCLENBQUMsWUFBWSxDQUFDLENBQUM7WUFFcEUsTUFBTSxNQUFNLEdBQUcsTUFBTSxVQUFVLENBQUMsMkJBQTJCLENBQUMsWUFBWSxFQUFFLFFBQVEsQ0FBQyxDQUFDO1lBRXBGLE1BQU0sQ0FBQyxPQUFPLENBQUMsMkJBQTJCLENBQUMsQ0FBQyxvQkFBb0IsQ0FBQyxZQUFZLENBQUMsQ0FBQztZQUMvRSxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQy9CLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ2xDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3BDLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLHFEQUFxRCxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQ25FLE9BQU8sQ0FBQywyQkFBMkIsQ0FBQyxpQkFBaUIsQ0FBQyxJQUFJLEtBQUssQ0FBQyw2QkFBNkIsQ0FBQyxDQUFDLENBQUM7WUFFaEcsTUFBTSxNQUFNLENBQUMsVUFBVSxDQUFDLDJCQUEyQixDQUFDLGNBQWMsRUFBRSxRQUFRLENBQUMsQ0FBQztpQkFDM0UsT0FBTyxDQUFDLE9BQU8sQ0FBQyw2QkFBNkIsQ0FBQyxDQUFDO1FBQ3BELENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsZ0JBQWdCLEVBQUUsR0FBRyxFQUFFO1FBQzlCLEVBQUUsQ0FBQyx5Q0FBeUMsRUFBRSxLQUFLLElBQUksRUFBRTtZQUN2RCxPQUFPLENBQUMsdUJBQXVCLENBQUMsaUJBQWlCLENBQUMsSUFBSSxLQUFLLENBQUMsNEJBQTRCLENBQUMsQ0FBQyxDQUFDO1lBRTNGLE1BQU0sTUFBTSxDQUFDLFVBQVUsQ0FBQyx1QkFBdUIsQ0FBQyxZQUFZLEVBQUUsUUFBUSxDQUFDLENBQUM7aUJBQ3JFLE9BQU8sQ0FBQyxPQUFPLENBQUMsNEJBQTRCLENBQUMsQ0FBQztRQUNuRCxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyxzQ0FBc0MsRUFBRSxLQUFLLElBQUksRUFBRTtZQUNwRCxNQUFNLFVBQVUsR0FBRyxFQUFFLElBQUksRUFBRSxHQUFHLEVBQVMsQ0FBQyxDQUFDLGVBQWU7WUFFeEQsTUFBTSxNQUFNLENBQUMsVUFBVSxDQUFDLHNCQUFzQixDQUFDLFVBQVUsRUFBRSxRQUFRLENBQUMsQ0FBQztpQkFDbEUsT0FBTyxDQUFDLE9BQU8sRUFBRSxDQUFDO1FBQ3ZCLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsZUFBZSxFQUFFLEdBQUcsRUFBRTtRQUM3QixFQUFFLENBQUMsK0NBQStDLEVBQUUsS0FBSyxJQUFJLEVBQUU7WUFDN0QsT0FBTyxDQUFDLHVCQUF1QixDQUFDLGlCQUFpQixDQUFDLG9CQUFvQixDQUFDLENBQUM7WUFFeEUsTUFBTSxNQUFNLEdBQUcsTUFBTSxVQUFVLENBQUMsdUJBQXVCLENBQUMsWUFBWSxFQUFFLFFBQVEsQ0FBQyxDQUFDO1lBRWhGLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxXQUFXLEVBQUUsQ0FBQztRQUMvQixDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyxtQ0FBbUMsRUFBRSxLQUFLLElBQUksRUFBRTtZQUNqRCxNQUFNLGdCQUFnQixHQUFHLEVBQUUsR0FBRyxRQUFRLEVBQUUsS0FBSyxFQUFFLENBQUMsUUFBUSxDQUFDLEVBQUUsQ0FBQztZQUU1RCwyREFBMkQ7WUFDM0QsK0NBQStDO1lBQy9DLE9BQU8sQ0FBQyx1QkFBdUIsQ0FBQyxpQkFBaUIsQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDO1lBRXhFLE1BQU0sTUFBTSxHQUFHLE1BQU0sVUFBVSxDQUFDLHVCQUF1QixDQUFDLFlBQVksRUFBRSxnQkFBZ0IsQ0FBQyxDQUFDO1lBRXhGLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxXQUFXLEVBQUUsQ0FBQztRQUMvQixDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLHlCQUF5QixFQUFFLEdBQUcsRUFBRTtRQUN2QyxFQUFFLENBQUMseUNBQXlDLEVBQUUsS0FBSyxJQUFJLEVBQUU7WUFDdkQsT0FBTyxDQUFDLHVCQUF1QixDQUFDLGlCQUFpQixDQUFDLG9CQUFvQixDQUFDLENBQUM7WUFFeEUsTUFBTSxNQUFNLEdBQUcsTUFBTSxVQUFVLENBQUMsdUJBQXVCLENBQUMsWUFBWSxFQUFFLFFBQVEsQ0FBQyxDQUFDO1lBRWhGLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxjQUFjLENBQUMsNERBQTJCLENBQUMsQ0FBQztZQUMzRCxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQ3BDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxjQUFjLENBQUMsTUFBTSxDQUFDLENBQUM7WUFDdEMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLGNBQWMsQ0FBQyxhQUFhLENBQUMsQ0FBQztZQUM3QyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsY0FBYyxDQUFDLFVBQVUsQ0FBQyxDQUFDO1FBQzVDLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLHFDQUFxQyxFQUFFLEtBQUssSUFBSSxFQUFFO1lBQ25ELE9BQU8sQ0FBQyx1QkFBdUIsQ0FBQyxpQkFBaUIsQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDO1lBRXhFLE1BQU0sTUFBTSxHQUFHLE1BQU0sVUFBVSxDQUFDLHVCQUF1QixDQUFDLFlBQVksRUFBRSxRQUFRLENBQUMsQ0FBQztZQUVoRixNQUFNLENBQUMsTUFBTSxDQUFDLFFBQVEsQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDO1lBQ3RDLE1BQU0sQ0FBQyxNQUFNLENBQUMsUUFBUSxDQUFDLFNBQVMsQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDO1lBQ2hELE1BQU0sQ0FBQyxNQUFNLENBQUMsUUFBUSxDQUFDLFNBQVMsQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDO1lBQ2hELE1BQU0sQ0FBQyxNQUFNLENBQUMsUUFBUSxDQUFDLFNBQVMsQ0FBQyxDQUFDLElBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQztRQUNyRCxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0FBQ0wsQ0FBQyxDQUFDLENBQUMiLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxtb2R1bGVzXFxyZXBvcnRpbmdcXHRlc3RzXFx1bml0XFxjb250cm9sbGVyc1xccmVwb3J0LWRlZmluaXRpb24uY29udHJvbGxlci5zcGVjLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFRlc3QsIFRlc3RpbmdNb2R1bGUgfSBmcm9tICdAbmVzdGpzL3Rlc3RpbmcnO1xyXG5pbXBvcnQgeyBIdHRwU3RhdHVzIH0gZnJvbSAnQG5lc3Rqcy9jb21tb24nO1xyXG5pbXBvcnQgeyBSZXBvcnREZWZpbml0aW9uQ29udHJvbGxlciB9IGZyb20gJy4uLy4uLy4uL2NvbnRyb2xsZXJzL3JlcG9ydC1kZWZpbml0aW9uLmNvbnRyb2xsZXInO1xyXG5pbXBvcnQgeyBSZXBvcnREZWZpbml0aW9uU2VydmljZSB9IGZyb20gJy4uLy4uLy4uL3NlcnZpY2VzL3JlcG9ydC1kZWZpbml0aW9uLnNlcnZpY2UnO1xyXG5pbXBvcnQgeyBDcmVhdGVSZXBvcnREZWZpbml0aW9uRHRvIH0gZnJvbSAnLi4vLi4vLi4vZHRvL2NyZWF0ZS1yZXBvcnQtZGVmaW5pdGlvbi5kdG8nO1xyXG5pbXBvcnQgeyBVcGRhdGVSZXBvcnREZWZpbml0aW9uRHRvIH0gZnJvbSAnLi4vLi4vLi4vZHRvL3VwZGF0ZS1yZXBvcnQtZGVmaW5pdGlvbi5kdG8nO1xyXG5pbXBvcnQgeyBSZXBvcnREZWZpbml0aW9uUmVzcG9uc2VEdG8gfSBmcm9tICcuLi8uLi8uLi9kdG8vcmVwb3J0LWRlZmluaXRpb24tcmVzcG9uc2UuZHRvJztcclxuXHJcbi8qKlxyXG4gKiBVbml0IFRlc3RzIGZvciBSZXBvcnQgRGVmaW5pdGlvbiBDb250cm9sbGVyXHJcbiAqIFxyXG4gKiBUZXN0cyBhbGwgY29udHJvbGxlciBlbmRwb2ludHMgaW5jbHVkaW5nOlxyXG4gKiAtIFJlcXVlc3QvcmVzcG9uc2UgdmFsaWRhdGlvbiBhbmQgdHJhbnNmb3JtYXRpb25cclxuICogLSBFcnJvciBoYW5kbGluZyBhbmQgSFRUUCBzdGF0dXMgY29kZXNcclxuICogLSBBdXRoZW50aWNhdGlvbiBhbmQgYXV0aG9yaXphdGlvblxyXG4gKiAtIElucHV0IHZhbGlkYXRpb24gYW5kIHNhbml0aXphdGlvblxyXG4gKiAtIFJlc3BvbnNlIGZvcm1hdHRpbmcgYW5kIHNlcmlhbGl6YXRpb25cclxuICovXHJcbmRlc2NyaWJlKCdSZXBvcnREZWZpbml0aW9uQ29udHJvbGxlcicsICgpID0+IHtcclxuICBsZXQgY29udHJvbGxlcjogUmVwb3J0RGVmaW5pdGlvbkNvbnRyb2xsZXI7XHJcbiAgbGV0IHNlcnZpY2U6IGplc3QuTW9ja2VkPFJlcG9ydERlZmluaXRpb25TZXJ2aWNlPjtcclxuXHJcbiAgY29uc3QgbW9ja1VzZXIgPSB7XHJcbiAgICBpZDogJ3VzZXItMTIzJyxcclxuICAgIGVtYWlsOiAndGVzdEBleGFtcGxlLmNvbScsXHJcbiAgICByb2xlczogWydjb21wbGlhbmNlX2FkbWluJ10sXHJcbiAgfTtcclxuXHJcbiAgY29uc3QgbW9ja1JlcG9ydERlZmluaXRpb24gPSB7XHJcbiAgICBpZDogJ3JlcG9ydC0xMjMnLFxyXG4gICAgbmFtZTogJ1Rlc3QgUmVwb3J0JyxcclxuICAgIGRlc2NyaXB0aW9uOiAnVGVzdCByZXBvcnQgZGVzY3JpcHRpb24nLFxyXG4gICAgY2F0ZWdvcnk6ICdjb21wbGlhbmNlJyxcclxuICAgIHR5cGU6ICd0YWJ1bGFyJyxcclxuICAgIGRhdGFTb3VyY2U6ICdjb21wbGlhbmNlJyxcclxuICAgIHF1ZXJ5OiAnU0VMRUNUICogRlJPTSBjb21wbGlhbmNlX2RhdGEnLFxyXG4gICAgcGFyYW1ldGVyczogW10sXHJcbiAgICBvdXRwdXRGb3JtYXRzOiBbJ3BkZicsICdleGNlbCddLFxyXG4gICAgc2NoZWR1bGU6IHtcclxuICAgICAgZW5hYmxlZDogdHJ1ZSxcclxuICAgICAgY3JvbkV4cHJlc3Npb246ICcwIDkgKiAqIDEnLFxyXG4gICAgICB0aW1lem9uZTogJ1VUQycsXHJcbiAgICB9LFxyXG4gICAgaXNBY3RpdmU6IHRydWUsXHJcbiAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCcyMDI0LTAxLTAxJyksXHJcbiAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCcyMDI0LTAxLTAxJyksXHJcbiAgICBjcmVhdGVkQnk6ICd1c2VyLTEyMycsXHJcbiAgICB1cGRhdGVkQnk6ICd1c2VyLTEyMycsXHJcbiAgfTtcclxuXHJcbiAgYmVmb3JlRWFjaChhc3luYyAoKSA9PiB7XHJcbiAgICBjb25zdCBtb2NrU2VydmljZSA9IHtcclxuICAgICAgY3JlYXRlUmVwb3J0RGVmaW5pdGlvbjogamVzdC5mbigpLFxyXG4gICAgICBnZXRSZXBvcnREZWZpbml0aW9uQnlJZDogamVzdC5mbigpLFxyXG4gICAgICB1cGRhdGVSZXBvcnREZWZpbml0aW9uOiBqZXN0LmZuKCksXHJcbiAgICAgIGRlbGV0ZVJlcG9ydERlZmluaXRpb246IGplc3QuZm4oKSxcclxuICAgICAgZ2V0UmVwb3J0RGVmaW5pdGlvbnM6IGplc3QuZm4oKSxcclxuICAgICAgdmFsaWRhdGVSZXBvcnREZWZpbml0aW9uOiBqZXN0LmZuKCksXHJcbiAgICAgIGR1cGxpY2F0ZVJlcG9ydERlZmluaXRpb246IGplc3QuZm4oKSxcclxuICAgICAgZ2V0UmVwb3J0RGVmaW5pdGlvblZlcnNpb25zOiBqZXN0LmZuKCksXHJcbiAgICB9O1xyXG5cclxuICAgIGNvbnN0IG1vZHVsZTogVGVzdGluZ01vZHVsZSA9IGF3YWl0IFRlc3QuY3JlYXRlVGVzdGluZ01vZHVsZSh7XHJcbiAgICAgIGNvbnRyb2xsZXJzOiBbUmVwb3J0RGVmaW5pdGlvbkNvbnRyb2xsZXJdLFxyXG4gICAgICBwcm92aWRlcnM6IFtcclxuICAgICAgICB7XHJcbiAgICAgICAgICBwcm92aWRlOiBSZXBvcnREZWZpbml0aW9uU2VydmljZSxcclxuICAgICAgICAgIHVzZVZhbHVlOiBtb2NrU2VydmljZSxcclxuICAgICAgICB9LFxyXG4gICAgICBdLFxyXG4gICAgfSkuY29tcGlsZSgpO1xyXG5cclxuICAgIGNvbnRyb2xsZXIgPSBtb2R1bGUuZ2V0PFJlcG9ydERlZmluaXRpb25Db250cm9sbGVyPihSZXBvcnREZWZpbml0aW9uQ29udHJvbGxlcik7XHJcbiAgICBzZXJ2aWNlID0gbW9kdWxlLmdldChSZXBvcnREZWZpbml0aW9uU2VydmljZSk7XHJcbiAgfSk7XHJcblxyXG4gIGFmdGVyRWFjaCgoKSA9PiB7XHJcbiAgICBqZXN0LmNsZWFyQWxsTW9ja3MoKTtcclxuICB9KTtcclxuXHJcbiAgZGVzY3JpYmUoJ2NyZWF0ZVJlcG9ydERlZmluaXRpb24nLCAoKSA9PiB7XHJcbiAgICBjb25zdCBjcmVhdGVEdG86IENyZWF0ZVJlcG9ydERlZmluaXRpb25EdG8gPSB7XHJcbiAgICAgIG5hbWU6ICdOZXcgUmVwb3J0JyxcclxuICAgICAgZGVzY3JpcHRpb246ICdOZXcgcmVwb3J0IGRlc2NyaXB0aW9uJyxcclxuICAgICAgY2F0ZWdvcnk6ICdjb21wbGlhbmNlJyxcclxuICAgICAgdHlwZTogJ3RhYnVsYXInLFxyXG4gICAgICBkYXRhU291cmNlOiAnY29tcGxpYW5jZScsXHJcbiAgICAgIHF1ZXJ5OiAnU0VMRUNUICogRlJPTSBjb21wbGlhbmNlX2RhdGEnLFxyXG4gICAgICBwYXJhbWV0ZXJzOiBbXSxcclxuICAgICAgb3V0cHV0Rm9ybWF0czogWydwZGYnXSxcclxuICAgIH07XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBjcmVhdGUgYSBuZXcgcmVwb3J0IGRlZmluaXRpb24gc3VjY2Vzc2Z1bGx5JywgYXN5bmMgKCkgPT4ge1xyXG4gICAgICBzZXJ2aWNlLmNyZWF0ZVJlcG9ydERlZmluaXRpb24ubW9ja1Jlc29sdmVkVmFsdWUobW9ja1JlcG9ydERlZmluaXRpb24pO1xyXG5cclxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgY29udHJvbGxlci5jcmVhdGVSZXBvcnREZWZpbml0aW9uKGNyZWF0ZUR0bywgbW9ja1VzZXIpO1xyXG5cclxuICAgICAgZXhwZWN0KHNlcnZpY2UuY3JlYXRlUmVwb3J0RGVmaW5pdGlvbikudG9IYXZlQmVlbkNhbGxlZFdpdGgoY3JlYXRlRHRvLCBtb2NrVXNlci5pZCk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQpLnRvQmVJbnN0YW5jZU9mKFJlcG9ydERlZmluaXRpb25SZXNwb25zZUR0byk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQuaWQpLnRvQmUobW9ja1JlcG9ydERlZmluaXRpb24uaWQpO1xyXG4gICAgICBleHBlY3QocmVzdWx0Lm5hbWUpLnRvQmUobW9ja1JlcG9ydERlZmluaXRpb24ubmFtZSk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIGhhbmRsZSB2YWxpZGF0aW9uIGVycm9ycycsIGFzeW5jICgpID0+IHtcclxuICAgICAgY29uc3QgaW52YWxpZER0byA9IHsgLi4uY3JlYXRlRHRvLCBuYW1lOiAnJyB9O1xyXG4gICAgICBzZXJ2aWNlLmNyZWF0ZVJlcG9ydERlZmluaXRpb24ubW9ja1JlamVjdGVkVmFsdWUobmV3IEVycm9yKCdWYWxpZGF0aW9uIGZhaWxlZCcpKTtcclxuXHJcbiAgICAgIGF3YWl0IGV4cGVjdChjb250cm9sbGVyLmNyZWF0ZVJlcG9ydERlZmluaXRpb24oaW52YWxpZER0bywgbW9ja1VzZXIpKVxyXG4gICAgICAgIC5yZWplY3RzLnRvVGhyb3coJ1ZhbGlkYXRpb24gZmFpbGVkJyk7XHJcblxyXG4gICAgICBleHBlY3Qoc2VydmljZS5jcmVhdGVSZXBvcnREZWZpbml0aW9uKS50b0hhdmVCZWVuQ2FsbGVkV2l0aChpbnZhbGlkRHRvLCBtb2NrVXNlci5pZCk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIGhhbmRsZSBkdXBsaWNhdGUgbmFtZSBlcnJvcnMnLCBhc3luYyAoKSA9PiB7XHJcbiAgICAgIHNlcnZpY2UuY3JlYXRlUmVwb3J0RGVmaW5pdGlvbi5tb2NrUmVqZWN0ZWRWYWx1ZShcclxuICAgICAgICBuZXcgRXJyb3IoJ1JlcG9ydCBkZWZpbml0aW9uIHdpdGggdGhpcyBuYW1lIGFscmVhZHkgZXhpc3RzJylcclxuICAgICAgKTtcclxuXHJcbiAgICAgIGF3YWl0IGV4cGVjdChjb250cm9sbGVyLmNyZWF0ZVJlcG9ydERlZmluaXRpb24oY3JlYXRlRHRvLCBtb2NrVXNlcikpXHJcbiAgICAgICAgLnJlamVjdHMudG9UaHJvdygnUmVwb3J0IGRlZmluaXRpb24gd2l0aCB0aGlzIG5hbWUgYWxyZWFkeSBleGlzdHMnKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgdmFsaWRhdGUgcmVxdWlyZWQgZmllbGRzJywgYXN5bmMgKCkgPT4ge1xyXG4gICAgICBjb25zdCBpbmNvbXBsZXRlRHRvID0geyBuYW1lOiAnVGVzdCcgfSBhcyBDcmVhdGVSZXBvcnREZWZpbml0aW9uRHRvO1xyXG5cclxuICAgICAgYXdhaXQgZXhwZWN0KGNvbnRyb2xsZXIuY3JlYXRlUmVwb3J0RGVmaW5pdGlvbihpbmNvbXBsZXRlRHRvLCBtb2NrVXNlcikpXHJcbiAgICAgICAgLnJlamVjdHMudG9UaHJvdygpO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCdnZXRSZXBvcnREZWZpbml0aW9uQnlJZCcsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgcmV0dXJuIHJlcG9ydCBkZWZpbml0aW9uIGJ5IElEJywgYXN5bmMgKCkgPT4ge1xyXG4gICAgICBzZXJ2aWNlLmdldFJlcG9ydERlZmluaXRpb25CeUlkLm1vY2tSZXNvbHZlZFZhbHVlKG1vY2tSZXBvcnREZWZpbml0aW9uKTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGNvbnRyb2xsZXIuZ2V0UmVwb3J0RGVmaW5pdGlvbkJ5SWQoJ3JlcG9ydC0xMjMnLCBtb2NrVXNlcik7XHJcblxyXG4gICAgICBleHBlY3Qoc2VydmljZS5nZXRSZXBvcnREZWZpbml0aW9uQnlJZCkudG9IYXZlQmVlbkNhbGxlZFdpdGgoJ3JlcG9ydC0xMjMnKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdCkudG9CZUluc3RhbmNlT2YoUmVwb3J0RGVmaW5pdGlvblJlc3BvbnNlRHRvKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdC5pZCkudG9CZSgncmVwb3J0LTEyMycpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCB0aHJvdyA0MDQgZm9yIG5vbi1leGlzdGVudCByZXBvcnQgZGVmaW5pdGlvbicsIGFzeW5jICgpID0+IHtcclxuICAgICAgc2VydmljZS5nZXRSZXBvcnREZWZpbml0aW9uQnlJZC5tb2NrUmVzb2x2ZWRWYWx1ZShudWxsKTtcclxuXHJcbiAgICAgIGF3YWl0IGV4cGVjdChjb250cm9sbGVyLmdldFJlcG9ydERlZmluaXRpb25CeUlkKCdub24tZXhpc3RlbnQnLCBtb2NrVXNlcikpXHJcbiAgICAgICAgLnJlamVjdHMudG9UaHJvdygnUmVwb3J0IGRlZmluaXRpb24gbm90IGZvdW5kJyk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIHZhbGlkYXRlIFVVSUQgZm9ybWF0JywgYXN5bmMgKCkgPT4ge1xyXG4gICAgICBhd2FpdCBleHBlY3QoY29udHJvbGxlci5nZXRSZXBvcnREZWZpbml0aW9uQnlJZCgnaW52YWxpZC11dWlkJywgbW9ja1VzZXIpKVxyXG4gICAgICAgIC5yZWplY3RzLnRvVGhyb3coKTtcclxuICAgIH0pO1xyXG4gIH0pO1xyXG5cclxuICBkZXNjcmliZSgndXBkYXRlUmVwb3J0RGVmaW5pdGlvbicsICgpID0+IHtcclxuICAgIGNvbnN0IHVwZGF0ZUR0bzogVXBkYXRlUmVwb3J0RGVmaW5pdGlvbkR0byA9IHtcclxuICAgICAgbmFtZTogJ1VwZGF0ZWQgUmVwb3J0JyxcclxuICAgICAgZGVzY3JpcHRpb246ICdVcGRhdGVkIGRlc2NyaXB0aW9uJyxcclxuICAgIH07XHJcblxyXG4gICAgaXQoJ3Nob3VsZCB1cGRhdGUgcmVwb3J0IGRlZmluaXRpb24gc3VjY2Vzc2Z1bGx5JywgYXN5bmMgKCkgPT4ge1xyXG4gICAgICBjb25zdCB1cGRhdGVkUmVwb3J0ID0geyAuLi5tb2NrUmVwb3J0RGVmaW5pdGlvbiwgLi4udXBkYXRlRHRvIH07XHJcbiAgICAgIHNlcnZpY2UudXBkYXRlUmVwb3J0RGVmaW5pdGlvbi5tb2NrUmVzb2x2ZWRWYWx1ZSh1cGRhdGVkUmVwb3J0KTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGNvbnRyb2xsZXIudXBkYXRlUmVwb3J0RGVmaW5pdGlvbigncmVwb3J0LTEyMycsIHVwZGF0ZUR0bywgbW9ja1VzZXIpO1xyXG5cclxuICAgICAgZXhwZWN0KHNlcnZpY2UudXBkYXRlUmVwb3J0RGVmaW5pdGlvbikudG9IYXZlQmVlbkNhbGxlZFdpdGgoJ3JlcG9ydC0xMjMnLCB1cGRhdGVEdG8sIG1vY2tVc2VyLmlkKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdCkudG9CZUluc3RhbmNlT2YoUmVwb3J0RGVmaW5pdGlvblJlc3BvbnNlRHRvKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdC5uYW1lKS50b0JlKHVwZGF0ZUR0by5uYW1lKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgaGFuZGxlIHBhcnRpYWwgdXBkYXRlcycsIGFzeW5jICgpID0+IHtcclxuICAgICAgY29uc3QgcGFydGlhbFVwZGF0ZSA9IHsgZGVzY3JpcHRpb246ICdOZXcgZGVzY3JpcHRpb24gb25seScgfTtcclxuICAgICAgY29uc3QgdXBkYXRlZFJlcG9ydCA9IHsgLi4ubW9ja1JlcG9ydERlZmluaXRpb24sIC4uLnBhcnRpYWxVcGRhdGUgfTtcclxuICAgICAgc2VydmljZS51cGRhdGVSZXBvcnREZWZpbml0aW9uLm1vY2tSZXNvbHZlZFZhbHVlKHVwZGF0ZWRSZXBvcnQpO1xyXG5cclxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgY29udHJvbGxlci51cGRhdGVSZXBvcnREZWZpbml0aW9uKCdyZXBvcnQtMTIzJywgcGFydGlhbFVwZGF0ZSwgbW9ja1VzZXIpO1xyXG5cclxuICAgICAgZXhwZWN0KHJlc3VsdC5kZXNjcmlwdGlvbikudG9CZShwYXJ0aWFsVXBkYXRlLmRlc2NyaXB0aW9uKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdC5uYW1lKS50b0JlKG1vY2tSZXBvcnREZWZpbml0aW9uLm5hbWUpOyAvLyBTaG91bGQgcmVtYWluIHVuY2hhbmdlZFxyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCB0aHJvdyA0MDQgZm9yIG5vbi1leGlzdGVudCByZXBvcnQgZGVmaW5pdGlvbicsIGFzeW5jICgpID0+IHtcclxuICAgICAgc2VydmljZS51cGRhdGVSZXBvcnREZWZpbml0aW9uLm1vY2tSZWplY3RlZFZhbHVlKG5ldyBFcnJvcignUmVwb3J0IGRlZmluaXRpb24gbm90IGZvdW5kJykpO1xyXG5cclxuICAgICAgYXdhaXQgZXhwZWN0KGNvbnRyb2xsZXIudXBkYXRlUmVwb3J0RGVmaW5pdGlvbignbm9uLWV4aXN0ZW50JywgdXBkYXRlRHRvLCBtb2NrVXNlcikpXHJcbiAgICAgICAgLnJlamVjdHMudG9UaHJvdygnUmVwb3J0IGRlZmluaXRpb24gbm90IGZvdW5kJyk7XHJcbiAgICB9KTtcclxuICB9KTtcclxuXHJcbiAgZGVzY3JpYmUoJ2RlbGV0ZVJlcG9ydERlZmluaXRpb24nLCAoKSA9PiB7XHJcbiAgICBpdCgnc2hvdWxkIGRlbGV0ZSByZXBvcnQgZGVmaW5pdGlvbiBzdWNjZXNzZnVsbHknLCBhc3luYyAoKSA9PiB7XHJcbiAgICAgIHNlcnZpY2UuZGVsZXRlUmVwb3J0RGVmaW5pdGlvbi5tb2NrUmVzb2x2ZWRWYWx1ZSh1bmRlZmluZWQpO1xyXG5cclxuICAgICAgYXdhaXQgY29udHJvbGxlci5kZWxldGVSZXBvcnREZWZpbml0aW9uKCdyZXBvcnQtMTIzJywgbW9ja1VzZXIpO1xyXG5cclxuICAgICAgZXhwZWN0KHNlcnZpY2UuZGVsZXRlUmVwb3J0RGVmaW5pdGlvbikudG9IYXZlQmVlbkNhbGxlZFdpdGgoJ3JlcG9ydC0xMjMnLCBtb2NrVXNlci5pZCk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIHRocm93IDQwNCBmb3Igbm9uLWV4aXN0ZW50IHJlcG9ydCBkZWZpbml0aW9uJywgYXN5bmMgKCkgPT4ge1xyXG4gICAgICBzZXJ2aWNlLmRlbGV0ZVJlcG9ydERlZmluaXRpb24ubW9ja1JlamVjdGVkVmFsdWUobmV3IEVycm9yKCdSZXBvcnQgZGVmaW5pdGlvbiBub3QgZm91bmQnKSk7XHJcblxyXG4gICAgICBhd2FpdCBleHBlY3QoY29udHJvbGxlci5kZWxldGVSZXBvcnREZWZpbml0aW9uKCdub24tZXhpc3RlbnQnLCBtb2NrVXNlcikpXHJcbiAgICAgICAgLnJlamVjdHMudG9UaHJvdygnUmVwb3J0IGRlZmluaXRpb24gbm90IGZvdW5kJyk7XHJcbiAgICB9KTtcclxuICB9KTtcclxuXHJcbiAgZGVzY3JpYmUoJ2dldFJlcG9ydERlZmluaXRpb25zJywgKCkgPT4ge1xyXG4gICAgY29uc3QgbW9ja1BhZ2luYXRlZFJlc3VsdCA9IHtcclxuICAgICAgcmVwb3J0RGVmaW5pdGlvbnM6IFttb2NrUmVwb3J0RGVmaW5pdGlvbl0sXHJcbiAgICAgIHRvdGFsOiAxLFxyXG4gICAgICBwYWdlOiAxLFxyXG4gICAgICBsaW1pdDogMjAsXHJcbiAgICAgIHRvdGFsUGFnZXM6IDEsXHJcbiAgICB9O1xyXG5cclxuICAgIGl0KCdzaG91bGQgcmV0dXJuIHBhZ2luYXRlZCByZXBvcnQgZGVmaW5pdGlvbnMnLCBhc3luYyAoKSA9PiB7XHJcbiAgICAgIHNlcnZpY2UuZ2V0UmVwb3J0RGVmaW5pdGlvbnMubW9ja1Jlc29sdmVkVmFsdWUobW9ja1BhZ2luYXRlZFJlc3VsdCk7XHJcblxyXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBjb250cm9sbGVyLmdldFJlcG9ydERlZmluaXRpb25zKFxyXG4gICAgICAgIDEsIDIwLCAnY29tcGxpYW5jZScsICd0YWJ1bGFyJywgJ3Rlc3QnLCBtb2NrVXNlclxyXG4gICAgICApO1xyXG5cclxuICAgICAgZXhwZWN0KHNlcnZpY2UuZ2V0UmVwb3J0RGVmaW5pdGlvbnMpLnRvSGF2ZUJlZW5DYWxsZWRXaXRoKHtcclxuICAgICAgICBwYWdlOiAxLFxyXG4gICAgICAgIGxpbWl0OiAyMCxcclxuICAgICAgICBjYXRlZ29yeTogJ2NvbXBsaWFuY2UnLFxyXG4gICAgICAgIHR5cGU6ICd0YWJ1bGFyJyxcclxuICAgICAgICBzZWFyY2g6ICd0ZXN0JyxcclxuICAgICAgfSk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQucmVwb3J0RGVmaW5pdGlvbnMpLnRvSGF2ZUxlbmd0aCgxKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdC50b3RhbCkudG9CZSgxKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdC5wYWdlKS50b0JlKDEpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBoYW5kbGUgZGVmYXVsdCBwYWdpbmF0aW9uIHBhcmFtZXRlcnMnLCBhc3luYyAoKSA9PiB7XHJcbiAgICAgIHNlcnZpY2UuZ2V0UmVwb3J0RGVmaW5pdGlvbnMubW9ja1Jlc29sdmVkVmFsdWUobW9ja1BhZ2luYXRlZFJlc3VsdCk7XHJcblxyXG4gICAgICBhd2FpdCBjb250cm9sbGVyLmdldFJlcG9ydERlZmluaXRpb25zKHVuZGVmaW5lZCwgdW5kZWZpbmVkLCB1bmRlZmluZWQsIHVuZGVmaW5lZCwgdW5kZWZpbmVkLCBtb2NrVXNlcik7XHJcblxyXG4gICAgICBleHBlY3Qoc2VydmljZS5nZXRSZXBvcnREZWZpbml0aW9ucykudG9IYXZlQmVlbkNhbGxlZFdpdGgoe1xyXG4gICAgICAgIHBhZ2U6IDEsXHJcbiAgICAgICAgbGltaXQ6IDIwLFxyXG4gICAgICAgIGNhdGVnb3J5OiB1bmRlZmluZWQsXHJcbiAgICAgICAgdHlwZTogdW5kZWZpbmVkLFxyXG4gICAgICAgIHNlYXJjaDogdW5kZWZpbmVkLFxyXG4gICAgICB9KTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgdmFsaWRhdGUgcGFnaW5hdGlvbiBsaW1pdHMnLCBhc3luYyAoKSA9PiB7XHJcbiAgICAgIHNlcnZpY2UuZ2V0UmVwb3J0RGVmaW5pdGlvbnMubW9ja1Jlc29sdmVkVmFsdWUobW9ja1BhZ2luYXRlZFJlc3VsdCk7XHJcblxyXG4gICAgICBhd2FpdCBjb250cm9sbGVyLmdldFJlcG9ydERlZmluaXRpb25zKDEsIDE1MCwgdW5kZWZpbmVkLCB1bmRlZmluZWQsIHVuZGVmaW5lZCwgbW9ja1VzZXIpO1xyXG5cclxuICAgICAgZXhwZWN0KHNlcnZpY2UuZ2V0UmVwb3J0RGVmaW5pdGlvbnMpLnRvSGF2ZUJlZW5DYWxsZWRXaXRoKHtcclxuICAgICAgICBwYWdlOiAxLFxyXG4gICAgICAgIGxpbWl0OiAxMDAsIC8vIFNob3VsZCBiZSBjYXBwZWQgYXQgMTAwXHJcbiAgICAgICAgY2F0ZWdvcnk6IHVuZGVmaW5lZCxcclxuICAgICAgICB0eXBlOiB1bmRlZmluZWQsXHJcbiAgICAgICAgc2VhcmNoOiB1bmRlZmluZWQsXHJcbiAgICAgIH0pO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCd2YWxpZGF0ZVJlcG9ydERlZmluaXRpb24nLCAoKSA9PiB7XHJcbiAgICBjb25zdCB2YWxpZGF0aW9uRHRvID0ge1xyXG4gICAgICBuYW1lOiAnVGVzdCBSZXBvcnQnLFxyXG4gICAgICBkYXRhU291cmNlOiAnY29tcGxpYW5jZScsXHJcbiAgICAgIHF1ZXJ5OiAnU0VMRUNUICogRlJPTSBjb21wbGlhbmNlX2RhdGEnLFxyXG4gICAgICBwYXJhbWV0ZXJzOiBbXSxcclxuICAgIH07XHJcblxyXG4gICAgaXQoJ3Nob3VsZCB2YWxpZGF0ZSByZXBvcnQgZGVmaW5pdGlvbiBzdWNjZXNzZnVsbHknLCBhc3luYyAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IHZhbGlkYXRpb25SZXN1bHQgPSB7XHJcbiAgICAgICAgaXNWYWxpZDogdHJ1ZSxcclxuICAgICAgICBlcnJvcnM6IFtdLFxyXG4gICAgICAgIHdhcm5pbmdzOiBbXSxcclxuICAgICAgfTtcclxuICAgICAgc2VydmljZS52YWxpZGF0ZVJlcG9ydERlZmluaXRpb24ubW9ja1Jlc29sdmVkVmFsdWUodmFsaWRhdGlvblJlc3VsdCk7XHJcblxyXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBjb250cm9sbGVyLnZhbGlkYXRlUmVwb3J0RGVmaW5pdGlvbih2YWxpZGF0aW9uRHRvLCBtb2NrVXNlcik7XHJcblxyXG4gICAgICBleHBlY3Qoc2VydmljZS52YWxpZGF0ZVJlcG9ydERlZmluaXRpb24pLnRvSGF2ZUJlZW5DYWxsZWRXaXRoKHZhbGlkYXRpb25EdG8pO1xyXG4gICAgICBleHBlY3QocmVzdWx0LmlzVmFsaWQpLnRvQmUodHJ1ZSk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQuZXJyb3JzKS50b0VxdWFsKFtdKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgcmV0dXJuIHZhbGlkYXRpb24gZXJyb3JzJywgYXN5bmMgKCkgPT4ge1xyXG4gICAgICBjb25zdCB2YWxpZGF0aW9uUmVzdWx0ID0ge1xyXG4gICAgICAgIGlzVmFsaWQ6IGZhbHNlLFxyXG4gICAgICAgIGVycm9yczogWydJbnZhbGlkIHF1ZXJ5IHN5bnRheCcsICdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlciddLFxyXG4gICAgICAgIHdhcm5pbmdzOiBbJ1BlcmZvcm1hbmNlIG1heSBiZSBpbXBhY3RlZCddLFxyXG4gICAgICB9O1xyXG4gICAgICBzZXJ2aWNlLnZhbGlkYXRlUmVwb3J0RGVmaW5pdGlvbi5tb2NrUmVzb2x2ZWRWYWx1ZSh2YWxpZGF0aW9uUmVzdWx0KTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGNvbnRyb2xsZXIudmFsaWRhdGVSZXBvcnREZWZpbml0aW9uKHZhbGlkYXRpb25EdG8sIG1vY2tVc2VyKTtcclxuXHJcbiAgICAgIGV4cGVjdChyZXN1bHQuaXNWYWxpZCkudG9CZShmYWxzZSk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQuZXJyb3JzKS50b0hhdmVMZW5ndGgoMik7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQud2FybmluZ3MpLnRvSGF2ZUxlbmd0aCgxKTtcclxuICAgIH0pO1xyXG4gIH0pO1xyXG5cclxuICBkZXNjcmliZSgnZHVwbGljYXRlUmVwb3J0RGVmaW5pdGlvbicsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgZHVwbGljYXRlIHJlcG9ydCBkZWZpbml0aW9uIHN1Y2Nlc3NmdWxseScsIGFzeW5jICgpID0+IHtcclxuICAgICAgY29uc3QgZHVwbGljYXRlZFJlcG9ydCA9IHtcclxuICAgICAgICAuLi5tb2NrUmVwb3J0RGVmaW5pdGlvbixcclxuICAgICAgICBpZDogJ3JlcG9ydC00NTYnLFxyXG4gICAgICAgIG5hbWU6ICdDb3B5IG9mIFRlc3QgUmVwb3J0JyxcclxuICAgICAgfTtcclxuICAgICAgc2VydmljZS5kdXBsaWNhdGVSZXBvcnREZWZpbml0aW9uLm1vY2tSZXNvbHZlZFZhbHVlKGR1cGxpY2F0ZWRSZXBvcnQpO1xyXG5cclxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgY29udHJvbGxlci5kdXBsaWNhdGVSZXBvcnREZWZpbml0aW9uKCdyZXBvcnQtMTIzJywgbW9ja1VzZXIpO1xyXG5cclxuICAgICAgZXhwZWN0KHNlcnZpY2UuZHVwbGljYXRlUmVwb3J0RGVmaW5pdGlvbikudG9IYXZlQmVlbkNhbGxlZFdpdGgoJ3JlcG9ydC0xMjMnLCBtb2NrVXNlci5pZCk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQpLnRvQmVJbnN0YW5jZU9mKFJlcG9ydERlZmluaXRpb25SZXNwb25zZUR0byk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQuaWQpLnRvQmUoJ3JlcG9ydC00NTYnKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdC5uYW1lKS50b0JlKCdDb3B5IG9mIFRlc3QgUmVwb3J0Jyk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIHRocm93IDQwNCBmb3Igbm9uLWV4aXN0ZW50IHNvdXJjZSByZXBvcnQnLCBhc3luYyAoKSA9PiB7XHJcbiAgICAgIHNlcnZpY2UuZHVwbGljYXRlUmVwb3J0RGVmaW5pdGlvbi5tb2NrUmVqZWN0ZWRWYWx1ZShuZXcgRXJyb3IoJ1NvdXJjZSByZXBvcnQgZGVmaW5pdGlvbiBub3QgZm91bmQnKSk7XHJcblxyXG4gICAgICBhd2FpdCBleHBlY3QoY29udHJvbGxlci5kdXBsaWNhdGVSZXBvcnREZWZpbml0aW9uKCdub24tZXhpc3RlbnQnLCBtb2NrVXNlcikpXHJcbiAgICAgICAgLnJlamVjdHMudG9UaHJvdygnU291cmNlIHJlcG9ydCBkZWZpbml0aW9uIG5vdCBmb3VuZCcpO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCdnZXRSZXBvcnREZWZpbml0aW9uVmVyc2lvbnMnLCAoKSA9PiB7XHJcbiAgICBjb25zdCBtb2NrVmVyc2lvbnMgPSBbXHJcbiAgICAgIHsgLi4ubW9ja1JlcG9ydERlZmluaXRpb24sIHZlcnNpb246IDEgfSxcclxuICAgICAgeyAuLi5tb2NrUmVwb3J0RGVmaW5pdGlvbiwgdmVyc2lvbjogMiwgbmFtZTogJ1VwZGF0ZWQgVGVzdCBSZXBvcnQnIH0sXHJcbiAgICBdO1xyXG5cclxuICAgIGl0KCdzaG91bGQgcmV0dXJuIHJlcG9ydCBkZWZpbml0aW9uIHZlcnNpb25zJywgYXN5bmMgKCkgPT4ge1xyXG4gICAgICBzZXJ2aWNlLmdldFJlcG9ydERlZmluaXRpb25WZXJzaW9ucy5tb2NrUmVzb2x2ZWRWYWx1ZShtb2NrVmVyc2lvbnMpO1xyXG5cclxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgY29udHJvbGxlci5nZXRSZXBvcnREZWZpbml0aW9uVmVyc2lvbnMoJ3JlcG9ydC0xMjMnLCBtb2NrVXNlcik7XHJcblxyXG4gICAgICBleHBlY3Qoc2VydmljZS5nZXRSZXBvcnREZWZpbml0aW9uVmVyc2lvbnMpLnRvSGF2ZUJlZW5DYWxsZWRXaXRoKCdyZXBvcnQtMTIzJyk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQpLnRvSGF2ZUxlbmd0aCgyKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdFswXS52ZXJzaW9uKS50b0JlKDEpO1xyXG4gICAgICBleHBlY3QocmVzdWx0WzFdLnZlcnNpb24pLnRvQmUoMik7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIHRocm93IDQwNCBmb3Igbm9uLWV4aXN0ZW50IHJlcG9ydCBkZWZpbml0aW9uJywgYXN5bmMgKCkgPT4ge1xyXG4gICAgICBzZXJ2aWNlLmdldFJlcG9ydERlZmluaXRpb25WZXJzaW9ucy5tb2NrUmVqZWN0ZWRWYWx1ZShuZXcgRXJyb3IoJ1JlcG9ydCBkZWZpbml0aW9uIG5vdCBmb3VuZCcpKTtcclxuXHJcbiAgICAgIGF3YWl0IGV4cGVjdChjb250cm9sbGVyLmdldFJlcG9ydERlZmluaXRpb25WZXJzaW9ucygnbm9uLWV4aXN0ZW50JywgbW9ja1VzZXIpKVxyXG4gICAgICAgIC5yZWplY3RzLnRvVGhyb3coJ1JlcG9ydCBkZWZpbml0aW9uIG5vdCBmb3VuZCcpO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCdlcnJvciBoYW5kbGluZycsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgaGFuZGxlIHNlcnZpY2UgZXJyb3JzIGdyYWNlZnVsbHknLCBhc3luYyAoKSA9PiB7XHJcbiAgICAgIHNlcnZpY2UuZ2V0UmVwb3J0RGVmaW5pdGlvbkJ5SWQubW9ja1JlamVjdGVkVmFsdWUobmV3IEVycm9yKCdEYXRhYmFzZSBjb25uZWN0aW9uIGZhaWxlZCcpKTtcclxuXHJcbiAgICAgIGF3YWl0IGV4cGVjdChjb250cm9sbGVyLmdldFJlcG9ydERlZmluaXRpb25CeUlkKCdyZXBvcnQtMTIzJywgbW9ja1VzZXIpKVxyXG4gICAgICAgIC5yZWplY3RzLnRvVGhyb3coJ0RhdGFiYXNlIGNvbm5lY3Rpb24gZmFpbGVkJyk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIGhhbmRsZSB2YWxpZGF0aW9uIHBpcGUgZXJyb3JzJywgYXN5bmMgKCkgPT4ge1xyXG4gICAgICBjb25zdCBpbnZhbGlkRHRvID0geyBuYW1lOiAxMjMgfSBhcyBhbnk7IC8vIEludmFsaWQgdHlwZVxyXG5cclxuICAgICAgYXdhaXQgZXhwZWN0KGNvbnRyb2xsZXIuY3JlYXRlUmVwb3J0RGVmaW5pdGlvbihpbnZhbGlkRHRvLCBtb2NrVXNlcikpXHJcbiAgICAgICAgLnJlamVjdHMudG9UaHJvdygpO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCdhdXRob3JpemF0aW9uJywgKCkgPT4ge1xyXG4gICAgaXQoJ3Nob3VsZCBhbGxvdyBhY2Nlc3MgZm9yIGNvbXBsaWFuY2VfYWRtaW4gcm9sZScsIGFzeW5jICgpID0+IHtcclxuICAgICAgc2VydmljZS5nZXRSZXBvcnREZWZpbml0aW9uQnlJZC5tb2NrUmVzb2x2ZWRWYWx1ZShtb2NrUmVwb3J0RGVmaW5pdGlvbik7XHJcblxyXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBjb250cm9sbGVyLmdldFJlcG9ydERlZmluaXRpb25CeUlkKCdyZXBvcnQtMTIzJywgbW9ja1VzZXIpO1xyXG5cclxuICAgICAgZXhwZWN0KHJlc3VsdCkudG9CZURlZmluZWQoKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgaGFuZGxlIHVuYXV0aG9yaXplZCBhY2Nlc3MnLCBhc3luYyAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IHVuYXV0aG9yaXplZFVzZXIgPSB7IC4uLm1vY2tVc2VyLCByb2xlczogWyd2aWV3ZXInXSB9O1xyXG5cclxuICAgICAgLy8gVGhpcyB3b3VsZCBiZSBoYW5kbGVkIGJ5IGd1YXJkcyBpbiBhY3R1YWwgaW1wbGVtZW50YXRpb25cclxuICAgICAgLy8gSGVyZSB3ZSdyZSBqdXN0IHRlc3RpbmcgdGhlIGNvbnRyb2xsZXIgbG9naWNcclxuICAgICAgc2VydmljZS5nZXRSZXBvcnREZWZpbml0aW9uQnlJZC5tb2NrUmVzb2x2ZWRWYWx1ZShtb2NrUmVwb3J0RGVmaW5pdGlvbik7XHJcblxyXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBjb250cm9sbGVyLmdldFJlcG9ydERlZmluaXRpb25CeUlkKCdyZXBvcnQtMTIzJywgdW5hdXRob3JpemVkVXNlcik7XHJcblxyXG4gICAgICBleHBlY3QocmVzdWx0KS50b0JlRGVmaW5lZCgpO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCdyZXNwb25zZSB0cmFuc2Zvcm1hdGlvbicsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgdHJhbnNmb3JtIGVudGl0eSB0byByZXNwb25zZSBEVE8nLCBhc3luYyAoKSA9PiB7XHJcbiAgICAgIHNlcnZpY2UuZ2V0UmVwb3J0RGVmaW5pdGlvbkJ5SWQubW9ja1Jlc29sdmVkVmFsdWUobW9ja1JlcG9ydERlZmluaXRpb24pO1xyXG5cclxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgY29udHJvbGxlci5nZXRSZXBvcnREZWZpbml0aW9uQnlJZCgncmVwb3J0LTEyMycsIG1vY2tVc2VyKTtcclxuXHJcbiAgICAgIGV4cGVjdChyZXN1bHQpLnRvQmVJbnN0YW5jZU9mKFJlcG9ydERlZmluaXRpb25SZXNwb25zZUR0byk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQpLnRvSGF2ZVByb3BlcnR5KCdpZCcpO1xyXG4gICAgICBleHBlY3QocmVzdWx0KS50b0hhdmVQcm9wZXJ0eSgnbmFtZScpO1xyXG4gICAgICBleHBlY3QocmVzdWx0KS50b0hhdmVQcm9wZXJ0eSgnZGVzY3JpcHRpb24nKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdCkudG9IYXZlUHJvcGVydHkoJ21ldGFkYXRhJyk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIGluY2x1ZGUgbWV0YWRhdGEgaW4gcmVzcG9uc2UnLCBhc3luYyAoKSA9PiB7XHJcbiAgICAgIHNlcnZpY2UuZ2V0UmVwb3J0RGVmaW5pdGlvbkJ5SWQubW9ja1Jlc29sdmVkVmFsdWUobW9ja1JlcG9ydERlZmluaXRpb24pO1xyXG5cclxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgY29udHJvbGxlci5nZXRSZXBvcnREZWZpbml0aW9uQnlJZCgncmVwb3J0LTEyMycsIG1vY2tVc2VyKTtcclxuXHJcbiAgICAgIGV4cGVjdChyZXN1bHQubWV0YWRhdGEpLnRvQmVEZWZpbmVkKCk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQubWV0YWRhdGEuY3JlYXRlZEF0KS50b0JlRGVmaW5lZCgpO1xyXG4gICAgICBleHBlY3QocmVzdWx0Lm1ldGFkYXRhLnVwZGF0ZWRBdCkudG9CZURlZmluZWQoKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdC5tZXRhZGF0YS5jcmVhdGVkQnkpLnRvQmUoJ3VzZXItMTIzJyk7XHJcbiAgICB9KTtcclxuICB9KTtcclxufSk7XHJcbiJdLCJ2ZXJzaW9uIjozfQ==