878bd9b397c93be52604250f5ecd2498
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComplianceAssessment = void 0;
const typeorm_1 = require("typeorm");
const compliance_framework_entity_1 = require("./compliance-framework.entity");
const policy_violation_entity_1 = require("./policy-violation.entity");
/**
 * Compliance Assessment entity
 * Represents assessments of compliance against specific frameworks
 */
let ComplianceAssessment = class ComplianceAssessment {
    /**
     * Check if assessment is overdue
     */
    get isOverdue() {
        if (!this.nextAssessmentDate)
            return false;
        return new Date() > this.nextAssessmentDate;
    }
    /**
     * Check if assessment is compliant
     */
    get isCompliant() {
        return this.results?.overallStatus === 'compliant';
    }
    /**
     * Get assessment age in days
     */
    get ageInDays() {
        const now = new Date();
        const diffMs = now.getTime() - this.assessmentDate.getTime();
        return Math.floor(diffMs / (1000 * 60 * 60 * 24));
    }
    /**
     * Get compliance percentage
     */
    get compliancePercentage() {
        return this.results?.compliancePercentage || 0;
    }
    /**
     * Get critical findings count
     */
    get criticalFindingsCount() {
        return this.results?.findings?.filter(f => f.severity === 'critical').length || 0;
    }
    /**
     * Get high findings count
     */
    get highFindingsCount() {
        return this.results?.findings?.filter(f => f.severity === 'high').length || 0;
    }
    /**
     * Start assessment
     */
    start(userId) {
        this.status = 'in_progress';
        this.updatedBy = userId;
        if (!this.methodology) {
            this.methodology = {
                approach: 'standard',
                methodology: 'control_testing',
            };
        }
    }
    /**
     * Complete assessment
     */
    complete(userId, results) {
        this.status = 'completed';
        this.results = results;
        this.updatedBy = userId;
        // Calculate next assessment date based on framework requirements
        this.calculateNextAssessmentDate();
    }
    /**
     * Fail assessment
     */
    fail(userId, reason) {
        this.status = 'failed';
        this.updatedBy = userId;
        if (!this.metadata) {
            this.metadata = {};
        }
        this.metadata.failureReason = reason;
    }
    /**
     * Calculate next assessment date
     */
    calculateNextAssessmentDate() {
        if (!this.framework?.configuration.assessment?.assessmentFrequency)
            return;
        const frequency = this.framework.configuration.assessment.assessmentFrequency;
        const nextDate = new Date(this.assessmentDate);
        switch (frequency) {
            case 'monthly':
                nextDate.setMonth(nextDate.getMonth() + 1);
                break;
            case 'quarterly':
                nextDate.setMonth(nextDate.getMonth() + 3);
                break;
            case 'semi_annually':
                nextDate.setMonth(nextDate.getMonth() + 6);
                break;
            case 'annually':
                nextDate.setFullYear(nextDate.getFullYear() + 1);
                break;
        }
        this.nextAssessmentDate = nextDate;
    }
    /**
     * Add finding
     */
    addFinding(finding) {
        if (!this.results) {
            this.results = {};
        }
        if (!this.results.findings) {
            this.results.findings = [];
        }
        this.results.findings.push({
            id: `finding-${Date.now()}`,
            ...finding,
        });
    }
    /**
     * Update control result
     */
    updateControlResult(domainId, controlId, result) {
        if (!this.results) {
            this.results = { domainResults: [] };
        }
        if (!this.results.domainResults) {
            this.results.domainResults = [];
        }
        let domain = this.results.domainResults.find(d => d.domainId === domainId);
        if (!domain) {
            domain = {
                domainId,
                domainName: '',
                score: 0,
                status: 'non_compliant',
                controlResults: [],
            };
            this.results.domainResults.push(domain);
        }
        const existingIndex = domain.controlResults.findIndex(c => c.controlId === controlId);
        if (existingIndex >= 0) {
            domain.controlResults[existingIndex] = { ...domain.controlResults[existingIndex], ...result };
        }
        else {
            domain.controlResults.push({ controlId, ...result });
        }
        // Recalculate domain score
        this.recalculateDomainScore(domainId);
    }
    /**
     * Recalculate domain score
     */
    recalculateDomainScore(domainId) {
        const domain = this.results?.domainResults?.find(d => d.domainId === domainId);
        if (!domain)
            return;
        const compliantControls = domain.controlResults.filter(c => c.status === 'compliant').length;
        const totalControls = domain.controlResults.filter(c => c.status !== 'not_applicable').length;
        if (totalControls > 0) {
            domain.score = (compliantControls / totalControls) * 100;
            if (domain.score >= 95) {
                domain.status = 'compliant';
            }
            else if (domain.score >= 70) {
                domain.status = 'partially_compliant';
            }
            else {
                domain.status = 'non_compliant';
            }
        }
        // Recalculate overall score
        this.recalculateOverallScore();
    }
    /**
     * Recalculate overall assessment score
     */
    recalculateOverallScore() {
        if (!this.results?.domainResults)
            return;
        const totalScore = this.results.domainResults.reduce((sum, domain) => sum + domain.score, 0);
        const domainCount = this.results.domainResults.length;
        if (domainCount > 0) {
            this.results.overallScore = totalScore / domainCount;
            this.results.compliancePercentage = this.results.overallScore;
            if (this.results.overallScore >= 95) {
                this.results.overallStatus = 'compliant';
            }
            else if (this.results.overallScore >= 70) {
                this.results.overallStatus = 'partially_compliant';
            }
            else {
                this.results.overallStatus = 'non_compliant';
            }
        }
    }
    /**
     * Generate assessment summary
     */
    generateSummary() {
        return {
            id: this.id,
            name: this.name,
            frameworkId: this.frameworkId,
            assessmentType: this.assessmentType,
            status: this.status,
            assessmentDate: this.assessmentDate,
            nextAssessmentDate: this.nextAssessmentDate,
            isOverdue: this.isOverdue,
            isCompliant: this.isCompliant,
            compliancePercentage: this.compliancePercentage,
            criticalFindings: this.criticalFindingsCount,
            highFindings: this.highFindingsCount,
            ageInDays: this.ageInDays,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }
    /**
     * Export assessment for reporting
     */
    exportForReporting() {
        return {
            assessment: this.generateSummary(),
            scope: this.scope,
            results: this.results,
            methodology: this.methodology,
            metadata: this.metadata,
            exportedAt: new Date().toISOString(),
        };
    }
};
exports.ComplianceAssessment = ComplianceAssessment;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ComplianceAssessment.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], ComplianceAssessment.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], ComplianceAssessment.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['self_assessment', 'internal_audit', 'external_audit', 'certification', 'continuous_monitoring'],
    }),
    __metadata("design:type", String)
], ComplianceAssessment.prototype, "assessmentType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['planned', 'in_progress', 'completed', 'failed', 'cancelled'],
        default: 'planned',
    }),
    __metadata("design:type", String)
], ComplianceAssessment.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'assessment_date', type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], ComplianceAssessment.prototype, "assessmentDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'next_assessment_date', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], ComplianceAssessment.prototype, "nextAssessmentDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    __metadata("design:type", Object)
], ComplianceAssessment.prototype, "scope", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ComplianceAssessment.prototype, "results", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ComplianceAssessment.prototype, "methodology", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ComplianceAssessment.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, default: '{}' }),
    __metadata("design:type", Array)
], ComplianceAssessment.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', type: 'uuid' }),
    __metadata("design:type", String)
], ComplianceAssessment.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], ComplianceAssessment.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], ComplianceAssessment.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], ComplianceAssessment.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => compliance_framework_entity_1.ComplianceFramework, framework => framework.assessments),
    (0, typeorm_1.JoinColumn)({ name: 'framework_id' }),
    __metadata("design:type", typeof (_e = typeof compliance_framework_entity_1.ComplianceFramework !== "undefined" && compliance_framework_entity_1.ComplianceFramework) === "function" ? _e : Object)
], ComplianceAssessment.prototype, "framework", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'framework_id', type: 'uuid' }),
    __metadata("design:type", String)
], ComplianceAssessment.prototype, "frameworkId", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => policy_violation_entity_1.PolicyViolation, violation => violation.assessment),
    __metadata("design:type", Array)
], ComplianceAssessment.prototype, "violations", void 0);
exports.ComplianceAssessment = ComplianceAssessment = __decorate([
    (0, typeorm_1.Entity)('compliance_assessments'),
    (0, typeorm_1.Index)(['frameworkId']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['assessmentType']),
    (0, typeorm_1.Index)(['assessmentDate']),
    (0, typeorm_1.Index)(['nextAssessmentDate'])
], ComplianceAssessment);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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