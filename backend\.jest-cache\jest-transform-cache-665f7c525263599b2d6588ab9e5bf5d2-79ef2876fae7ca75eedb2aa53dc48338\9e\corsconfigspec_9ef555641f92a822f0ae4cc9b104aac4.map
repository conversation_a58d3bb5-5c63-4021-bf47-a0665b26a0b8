{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\headers\\__tests__\\cors.config.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,2CAA+C;AAC/C,gDAA4C;AAE5C,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;IAC1B,IAAI,UAAsB,CAAC;IAC3B,IAAI,aAAyC,CAAC;IAE9C,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,iBAAiB,GAAG;YACxB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;SACf,CAAC;QAEF,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,wBAAU;gBACV;oBACE,OAAO,EAAE,sBAAa;oBACtB,QAAQ,EAAE,iBAAiB;iBAC5B;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,UAAU,GAAG,MAAM,CAAC,GAAG,CAAa,wBAAU,CAAC,CAAC;QAChD,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,sBAAa,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,kEAAkE,EAAE,GAAG,EAAE;YAC1E,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,YAAY,CAAC;gBAC5C,IAAI,GAAG,KAAK,qBAAqB;oBAAE,OAAO,CAAC,0BAA0B,CAAC,CAAC;gBACvE,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG,UAAU,CAAC,cAAc,EAAE,CAAC;YAEhD,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC;YAC1F,MAAM,CAAC,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,SAAS,CAAC;gBACzC,IAAI,GAAG,KAAK,qBAAqB;oBAAE,OAAO,CAAC,8BAA8B,CAAC,CAAC;gBAC3E,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG,UAAU,CAAC,cAAc,EAAE,CAAC;YAEhD,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oEAAoE,EAAE,GAAG,EAAE;YAC5E,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,aAAa,CAAC;gBAC7C,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG,UAAU,CAAC,cAAc,EAAE,CAAC;YAEhD,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,MAAM,CAAC;gBACtC,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG,UAAU,CAAC,cAAc,EAAE,CAAC;YAEhD,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC7C,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC7C,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mCAAmC,EAAE,GAAG,EAAE;QACjD,UAAU,CAAC,GAAG,EAAE;YACd,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,YAAY,CAAC;gBAC5C,IAAI,GAAG,KAAK,qBAAqB;oBAAE,OAAO,CAAC,0BAA0B,EAAE,4BAA4B,CAAC,CAAC;gBACrG,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,CAAC,IAAI,EAAE,EAAE;YAC5D,MAAM,WAAW,GAAG,UAAU,CAAC,cAAc,EAAE,CAAC;YAChD,MAAM,cAAc,GAAG,WAAW,CAAC,MAAkB,CAAC;YAEtD,cAAc,CAAC,0BAA0B,EAAE,CAAC,KAAmB,EAAE,OAAgB,EAAE,EAAE;gBACnF,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;gBACzB,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC3B,IAAI,EAAE,CAAC;YACT,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,CAAC,IAAI,EAAE,EAAE;YACzE,MAAM,WAAW,GAAG,UAAU,CAAC,cAAc,EAAE,CAAC;YAChD,MAAM,cAAc,GAAG,WAAW,CAAC,MAAkB,CAAC;YAEtD,cAAc,CAAC,SAAS,EAAE,CAAC,KAAmB,EAAE,OAAgB,EAAE,EAAE;gBAClE,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;gBACzB,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC3B,IAAI,EAAE,CAAC;YACT,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,CAAC,IAAI,EAAE,EAAE;YACjE,MAAM,WAAW,GAAG,UAAU,CAAC,cAAc,EAAE,CAAC;YAChD,MAAM,cAAc,GAAG,WAAW,CAAC,MAAkB,CAAC;YAEtD,cAAc,CAAC,uBAAuB,EAAE,CAAC,KAAmB,EAAE,OAAgB,EAAE,EAAE;gBAChF,MAAM,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBACpC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBACnD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC5B,IAAI,EAAE,CAAC;YACT,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,aAAa,CAAC;gBAC7C,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,UAAU,CAAC,kBAAkB,EAAE,CAAC;YAEhD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,YAAY,CAAC;gBAC5C,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,+CAA+C;YAC/C,IAAI,CAAC,KAAK,CAAC,UAAiB,EAAE,0BAA0B,CAAC,CAAC,eAAe,CAAC;gBACxE,MAAM,EAAE,IAAI;gBACZ,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;gBACxB,cAAc,EAAE,CAAC,cAAc,CAAC;gBAChC,WAAW,EAAE,IAAI;aAClB,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,UAAU,CAAC,kBAAkB,EAAE,CAAC;YAEhD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,GAAG,EAAE;gBACxC,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,UAAU,CAAC,kBAAkB,EAAE,CAAC;YAEhD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,aAAa,CAAC;gBAC7C,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,UAAU,CAAC,oBAAoB,EAAE,CAAC;YAElD,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,YAAY,CAAC;gBAC5C,IAAI,GAAG,KAAK,qBAAqB;oBAAE,OAAO,CAAC,0BAA0B,CAAC,CAAC;gBACvE,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,UAAU,CAAC,oBAAoB,EAAE,CAAC;YAElD,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,SAAS,CAAC;gBACzC,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,UAAU,CAAC,oBAAoB,EAAE,CAAC;YAElD,MAAM,CAAC,OAAO,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnD,MAAM,CAAC,OAAO,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qCAAqC,EAAE,GAAG,EAAE;QACnD,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,SAAS,CAAC;gBACzC,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG,UAAU,CAAC,cAAc,EAAE,CAAC;YAEhD,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YAC7D,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,aAAa,CAAC;gBAC7C,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG,UAAU,CAAC,cAAc,EAAE,CAAC;YAEhD,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YAC5D,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,YAAY,CAAC;gBAC5C,IAAI,GAAG,KAAK,qBAAqB;oBAAE,OAAO,CAAC,0BAA0B,CAAC,CAAC;gBACvE,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG,UAAU,CAAC,cAAc,EAAE,CAAC;YAEhD,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YACjE,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YAChE,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\headers\\__tests__\\cors.config.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { CorsConfig } from '../cors.config';\r\n\r\ndescribe('CorsConfig', () => {\r\n  let corsConfig: CorsConfig;\r\n  let configService: jest.Mocked<ConfigService>;\r\n\r\n  beforeEach(async () => {\r\n    const mockConfigService = {\r\n      get: jest.fn(),\r\n    };\r\n\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        CorsConfig,\r\n        {\r\n          provide: ConfigService,\r\n          useValue: mockConfigService,\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    corsConfig = module.get<CorsConfig>(CorsConfig);\r\n    configService = module.get(ConfigService);\r\n  });\r\n\r\n  describe('getCorsOptions', () => {\r\n    it('should return production CORS options for production environment', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'production';\r\n        if (key === 'cors.allowedOrigins') return ['https://app.sentinel.com'];\r\n        return defaultValue;\r\n      });\r\n\r\n      const corsOptions = corsConfig.getCorsOptions();\r\n\r\n      expect(corsOptions.credentials).toBe(true);\r\n      expect(corsOptions.maxAge).toBe(86400);\r\n      expect(corsOptions.methods).toEqual(['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS']);\r\n      expect(typeof corsOptions.origin).toBe('function');\r\n    });\r\n\r\n    it('should return staging CORS options for staging environment', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'staging';\r\n        if (key === 'cors.allowedOrigins') return ['https://staging.sentinel.com'];\r\n        return defaultValue;\r\n      });\r\n\r\n      const corsOptions = corsConfig.getCorsOptions();\r\n\r\n      expect(corsOptions.credentials).toBe(true);\r\n      expect(corsOptions.maxAge).toBe(3600);\r\n      expect(corsOptions.methods).toContain('HEAD');\r\n    });\r\n\r\n    it('should return development CORS options for development environment', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'development';\r\n        return defaultValue;\r\n      });\r\n\r\n      const corsOptions = corsConfig.getCorsOptions();\r\n\r\n      expect(corsOptions.origin).toBe(true);\r\n      expect(corsOptions.credentials).toBe(true);\r\n      expect(corsOptions.maxAge).toBe(300);\r\n    });\r\n\r\n    it('should return test CORS options for test environment', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'test';\r\n        return defaultValue;\r\n      });\r\n\r\n      const corsOptions = corsConfig.getCorsOptions();\r\n\r\n      expect(corsOptions.origin).toBe(true);\r\n      expect(corsOptions.allowedHeaders).toBe('*');\r\n      expect(corsOptions.exposedHeaders).toBe('*');\r\n      expect(corsOptions.maxAge).toBe(0);\r\n    });\r\n  });\r\n\r\n  describe('production CORS origin validation', () => {\r\n    beforeEach(() => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'production';\r\n        if (key === 'cors.allowedOrigins') return ['https://app.sentinel.com', 'https://admin.sentinel.com'];\r\n        return defaultValue;\r\n      });\r\n    });\r\n\r\n    it('should allow requests from whitelisted origins', (done) => {\r\n      const corsOptions = corsConfig.getCorsOptions();\r\n      const originCallback = corsOptions.origin as Function;\r\n\r\n      originCallback('https://app.sentinel.com', (error: Error | null, allowed: boolean) => {\r\n        expect(error).toBeNull();\r\n        expect(allowed).toBe(true);\r\n        done();\r\n      });\r\n    });\r\n\r\n    it('should allow requests with no origin (mobile apps, Postman)', (done) => {\r\n      const corsOptions = corsConfig.getCorsOptions();\r\n      const originCallback = corsOptions.origin as Function;\r\n\r\n      originCallback(undefined, (error: Error | null, allowed: boolean) => {\r\n        expect(error).toBeNull();\r\n        expect(allowed).toBe(true);\r\n        done();\r\n      });\r\n    });\r\n\r\n    it('should reject requests from non-whitelisted origins', (done) => {\r\n      const corsOptions = corsConfig.getCorsOptions();\r\n      const originCallback = corsOptions.origin as Function;\r\n\r\n      originCallback('https://malicious.com', (error: Error | null, allowed: boolean) => {\r\n        expect(error).toBeInstanceOf(Error);\r\n        expect(error?.message).toBe('Not allowed by CORS');\r\n        expect(allowed).toBe(false);\r\n        done();\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('validateCorsConfig', () => {\r\n    it('should validate valid CORS configuration', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'development';\r\n        return defaultValue;\r\n      });\r\n\r\n      const isValid = corsConfig.validateCorsConfig();\r\n\r\n      expect(isValid).toBe(true);\r\n    });\r\n\r\n    it('should fail validation for production with wildcard origin', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'production';\r\n        return defaultValue;\r\n      });\r\n\r\n      // Mock getCorsOptions to return invalid config\r\n      jest.spyOn(corsConfig as any, 'getProductionCorsOptions').mockReturnValue({\r\n        origin: true,\r\n        methods: ['GET', 'POST'],\r\n        allowedHeaders: ['Content-Type'],\r\n        credentials: true,\r\n      });\r\n\r\n      const isValid = corsConfig.validateCorsConfig();\r\n\r\n      expect(isValid).toBe(false);\r\n    });\r\n\r\n    it('should handle validation errors gracefully', () => {\r\n      configService.get.mockImplementation(() => {\r\n        throw new Error('Configuration error');\r\n      });\r\n\r\n      const isValid = corsConfig.validateCorsConfig();\r\n\r\n      expect(isValid).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('getCorsConfigSummary', () => {\r\n    it('should return configuration summary for development', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'development';\r\n        return defaultValue;\r\n      });\r\n\r\n      const summary = corsConfig.getCorsConfigSummary();\r\n\r\n      expect(summary.environment).toBe('development');\r\n      expect(summary.allowsCredentials).toBe(true);\r\n      expect(summary.originPolicy).toBe('allow-all');\r\n      expect(typeof summary.maxAge).toBe('number');\r\n    });\r\n\r\n    it('should return configuration summary for production', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'production';\r\n        if (key === 'cors.allowedOrigins') return ['https://app.sentinel.com'];\r\n        return defaultValue;\r\n      });\r\n\r\n      const summary = corsConfig.getCorsConfigSummary();\r\n\r\n      expect(summary.environment).toBe('production');\r\n      expect(summary.originPolicy).toBe('whitelist');\r\n      expect(summary.maxAge).toBe(86400);\r\n    });\r\n\r\n    it('should handle array methods and headers', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'staging';\r\n        return defaultValue;\r\n      });\r\n\r\n      const summary = corsConfig.getCorsConfigSummary();\r\n\r\n      expect(typeof summary.methodsCount).toBe('number');\r\n      expect(typeof summary.headersCount).toBe('number');\r\n    });\r\n  });\r\n\r\n  describe('environment-specific configurations', () => {\r\n    it('should include debug headers in staging', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'staging';\r\n        return defaultValue;\r\n      });\r\n\r\n      const corsOptions = corsConfig.getCorsOptions();\r\n\r\n      expect(corsOptions.allowedHeaders).toContain('X-Debug-Mode');\r\n      expect(corsOptions.exposedHeaders).toContain('X-Debug-Info');\r\n    });\r\n\r\n    it('should include test headers in development', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'development';\r\n        return defaultValue;\r\n      });\r\n\r\n      const corsOptions = corsConfig.getCorsOptions();\r\n\r\n      expect(corsOptions.allowedHeaders).toContain('X-Test-Mode');\r\n      expect(corsOptions.exposedHeaders).toContain('X-Performance-Metrics');\r\n    });\r\n\r\n    it('should have strict headers in production', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'production';\r\n        if (key === 'cors.allowedOrigins') return ['https://app.sentinel.com'];\r\n        return defaultValue;\r\n      });\r\n\r\n      const corsOptions = corsConfig.getCorsOptions();\r\n\r\n      expect(corsOptions.allowedHeaders).not.toContain('X-Debug-Mode');\r\n      expect(corsOptions.allowedHeaders).not.toContain('X-Test-Mode');\r\n      expect(corsOptions.maxAge).toBe(86400);\r\n    });\r\n  });\r\n});"], "version": 3}