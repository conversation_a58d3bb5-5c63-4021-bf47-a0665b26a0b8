b4360f0270c14f26f5ed94f5ea254f85
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var JwtStrategy_1;
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.JwtStrategy = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const passport_jwt_1 = require("passport-jwt");
const config_1 = require("@nestjs/config");
const auth_service_1 = require("../auth.service");
const jwt_config_1 = require("../../config/jwt.config");
/**
 * JWT authentication strategy for Passport
 * Validates JWT tokens and extracts user information
 */
let JwtStrategy = JwtStrategy_1 = class JwtStrategy extends (0, passport_1.PassportStrategy)(passport_jwt_1.Strategy, 'jwt') {
    constructor(configService, authService) {
        const jwtConfig = configService.get('jwt');
        const verifyOptions = jwt_config_1.JwtConfigHelper.getVerifyOptions(jwtConfig);
        const secretOrKey = jwt_config_1.JwtConfigHelper.getSecretOrKey(jwtConfig, false);
        super({
            jwtFromRequest: passport_jwt_1.ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: jwtConfig.ignoreExpiration,
            secretOrKey,
            issuer: jwtConfig.issuer,
            audience: jwtConfig.audience,
            algorithms: [jwtConfig.algorithm],
            clockTolerance: jwtConfig.clockTolerance,
        });
        this.configService = configService;
        this.authService = authService;
        this.logger = new common_1.Logger(JwtStrategy_1.name);
    }
    /**
     * Validate JWT payload and return user object
     * This method is called automatically by Passport when a JWT token is provided
     *
     * @param payload JWT payload containing user information
     * @returns Promise<any> User object with roles and permissions
     */
    async validate(payload) {
        try {
            this.logger.debug('Validating JWT payload', {
                sub: payload.sub,
                email: payload.email,
                iat: payload.iat,
                exp: payload.exp,
            });
            // Validate payload structure
            if (!payload.sub || !payload.email) {
                this.logger.warn('Invalid JWT payload structure', { payload });
                throw new common_1.UnauthorizedException('Invalid token payload');
            }
            // Check token expiration (additional check)
            const currentTime = Math.floor(Date.now() / 1000);
            if (payload.exp && payload.exp < currentTime) {
                this.logger.warn('JWT token has expired', {
                    exp: payload.exp,
                    currentTime,
                    sub: payload.sub,
                });
                throw new common_1.UnauthorizedException('Token has expired');
            }
            // Validate user exists and is active
            const user = await this.authService.validateJwtPayload(payload);
            if (!user) {
                this.logger.warn('User not found for JWT payload', {
                    sub: payload.sub,
                    email: payload.email,
                });
                throw new common_1.UnauthorizedException('User not found');
            }
            // Return user object with additional JWT payload data
            const validatedUser = {
                id: user.id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                roles: payload.roles || [],
                permissions: payload.permissions || [],
                isActive: user.isActive,
                emailVerified: user.emailVerified,
                lastLoginAt: user.lastLoginAt,
                // Include JWT metadata
                tokenIssuedAt: payload.iat,
                tokenExpiresAt: payload.exp,
            };
            this.logger.debug('JWT validation successful', {
                userId: user.id,
                email: user.email,
                roles: validatedUser.roles,
                permissionCount: validatedUser.permissions.length,
            });
            return validatedUser;
        }
        catch (error) {
            this.logger.error('JWT validation failed', {
                error: error.message,
                payload: {
                    sub: payload?.sub,
                    email: payload?.email,
                    iat: payload?.iat,
                    exp: payload?.exp,
                },
            });
            if (error instanceof common_1.UnauthorizedException) {
                throw error;
            }
            throw new common_1.UnauthorizedException('Token validation failed');
        }
    }
};
exports.JwtStrategy = JwtStrategy;
exports.JwtStrategy = JwtStrategy = JwtStrategy_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object, typeof (_b = typeof auth_service_1.AuthService !== "undefined" && auth_service_1.AuthService) === "function" ? _b : Object])
], JwtStrategy);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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