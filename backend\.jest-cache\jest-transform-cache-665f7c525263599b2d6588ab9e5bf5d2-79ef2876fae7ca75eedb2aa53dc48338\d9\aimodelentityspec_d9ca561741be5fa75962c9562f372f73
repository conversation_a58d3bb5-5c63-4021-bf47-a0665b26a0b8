688d6cf5d34fe454c6c9a1b691acb76d
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const unique_entity_id_value_object_1 = require("../../../../../shared-kernel/value-objects/unique-entity-id.value-object");
const ai_model_entity_1 = require("../ai-model.entity");
const ai_model_created_domain_event_1 = require("../../events/ai-model-created.domain-event");
const ai_model_status_changed_domain_event_1 = require("../../events/ai-model-status-changed.domain-event");
const ai_model_performance_updated_domain_event_1 = require("../../events/ai-model-performance-updated.domain-event");
const ai_model_configuration_updated_domain_event_1 = require("../../events/ai-model-configuration-updated.domain-event");
describe('AIModel Entity', () => {
    let validProps;
    let mockConfiguration;
    let mockPerformance;
    let mockCapabilities;
    let mockResourceRequirements;
    beforeEach(() => {
        mockConfiguration = {
            endpoint: 'https://api.example.com',
            apiKey: 'test-key',
            timeout: 30000,
            retries: 3,
            batchSize: 10,
            customSettings: {},
        };
        mockPerformance = {
            totalRequests: 100,
            successfulRequests: 95,
            failedRequests: 5,
            averageLatency: 200,
            p95Latency: 300,
            p99Latency: 500,
            accuracy: 0.95,
            precision: 0.92,
            recall: 0.88,
            f1Score: 0.90,
            throughput: 50,
            lastUpdated: new Date(),
        };
        mockCapabilities = {
            maxInputLength: 4096,
            maxOutputLength: 2048,
            supportsBatch: true,
            supportsStreaming: false,
            supportsFineTuning: true,
            languages: ['en', 'es'],
            modalities: ['text'],
        };
        mockResourceRequirements = {
            cpu: 2,
            memory: 2048,
            gpu: 1,
            storage: 1024,
            bandwidth: 100,
        };
        validProps = {
            name: 'Test Model',
            version: '1.0.0',
            provider: ai_model_entity_1.AIProvider.OPENAI,
            modelType: ai_model_entity_1.ModelType.LANGUAGE_MODEL,
            configuration: mockConfiguration,
            performance: mockPerformance,
            status: ai_model_entity_1.ModelStatus.ACTIVE,
            capabilities: mockCapabilities,
            resourceRequirements: mockResourceRequirements,
            supportedTaskTypes: ['text-generation', 'classification'],
            tags: ['test', 'nlp'],
            priority: 5.0,
            weight: 2.0,
            maxConcurrentRequests: 100,
            lastHealthCheck: new Date(),
            lastUsed: new Date(),
            deployedAt: new Date(),
            metadata: { version: '1.0' },
        };
    });
    describe('Creation', () => {
        it('should create a valid AI model', () => {
            const model = ai_model_entity_1.AIModel.create(validProps);
            expect(model).toBeInstanceOf(ai_model_entity_1.AIModel);
            expect(model.name).toBe(validProps.name);
            expect(model.version).toBe(validProps.version);
            expect(model.provider).toBe(validProps.provider);
            expect(model.modelType).toBe(validProps.modelType);
            expect(model.status).toBe(validProps.status);
            expect(model.currentLoad).toBe(0);
            expect(model.createdAt).toBeInstanceOf(Date);
            expect(model.updatedAt).toBeInstanceOf(Date);
        });
        it('should generate a domain event when created', () => {
            const model = ai_model_entity_1.AIModel.create(validProps);
            const events = model.domainEvents;
            expect(events).toHaveLength(1);
            expect(events[0]).toBeInstanceOf(ai_model_created_domain_event_1.AIModelCreatedEvent);
            expect(events[0].modelName).toBe(validProps.name);
        });
        it('should create with custom ID', () => {
            const customId = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const model = ai_model_entity_1.AIModel.create(validProps, customId);
            expect(model.id.equals(customId)).toBe(true);
        });
        it('should throw error for invalid name', () => {
            const invalidProps = { ...validProps, name: '' };
            expect(() => ai_model_entity_1.AIModel.create(invalidProps)).toThrow('AI Model name is required');
        });
        it('should throw error for invalid version', () => {
            const invalidProps = { ...validProps, version: '' };
            expect(() => ai_model_entity_1.AIModel.create(invalidProps)).toThrow('AI Model version is required');
        });
        it('should throw error for invalid priority', () => {
            const invalidProps = { ...validProps, priority: 15 };
            expect(() => ai_model_entity_1.AIModel.create(invalidProps)).toThrow('Priority must be between 0 and 10');
        });
        it('should throw error for invalid weight', () => {
            const invalidProps = { ...validProps, weight: -1 };
            expect(() => ai_model_entity_1.AIModel.create(invalidProps)).toThrow('Weight must be between 0 and 10');
        });
        it('should throw error for invalid max concurrent requests', () => {
            const invalidProps = { ...validProps, maxConcurrentRequests: 0 };
            expect(() => ai_model_entity_1.AIModel.create(invalidProps)).toThrow('Max concurrent requests must be greater than 0');
        });
        it('should throw error for empty supported task types', () => {
            const invalidProps = { ...validProps, supportedTaskTypes: [] };
            expect(() => ai_model_entity_1.AIModel.create(invalidProps)).toThrow('Model must support at least one task type');
        });
        it('should throw error for invalid configuration timeout', () => {
            const invalidProps = {
                ...validProps,
                configuration: { ...mockConfiguration, timeout: 0 }
            };
            expect(() => ai_model_entity_1.AIModel.create(invalidProps)).toThrow('Configuration timeout must be greater than 0');
        });
    });
    describe('Reconstitution', () => {
        it('should reconstitute from valid props', () => {
            const id = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const now = new Date();
            const props = {
                ...validProps,
                currentLoad: 5,
                createdAt: now,
                updatedAt: now,
            };
            const model = ai_model_entity_1.AIModel.reconstitute(props, id);
            expect(model.id.equals(id)).toBe(true);
            expect(model.name).toBe(props.name);
            expect(model.currentLoad).toBe(5);
            expect(model.createdAt).toBe(now);
            expect(model.updatedAt).toBe(now);
        });
    });
    describe('Status Management', () => {
        let model;
        beforeEach(() => {
            model = ai_model_entity_1.AIModel.create(validProps);
            model.clearEvents(); // Clear creation event
        });
        it('should activate model', () => {
            model.deactivate();
            model.clearEvents();
            model.activate();
            expect(model.status).toBe(ai_model_entity_1.ModelStatus.ACTIVE);
            expect(model.domainEvents).toHaveLength(1);
            expect(model.domainEvents[0]).toBeInstanceOf(ai_model_status_changed_domain_event_1.AIModelStatusChangedEvent);
        });
        it('should deactivate model', () => {
            model.deactivate();
            expect(model.status).toBe(ai_model_entity_1.ModelStatus.INACTIVE);
            expect(model.domainEvents).toHaveLength(1);
            expect(model.domainEvents[0]).toBeInstanceOf(ai_model_status_changed_domain_event_1.AIModelStatusChangedEvent);
        });
        it('should archive model', () => {
            model.archive();
            expect(model.status).toBe(ai_model_entity_1.ModelStatus.ARCHIVED);
            expect(model.domainEvents).toHaveLength(1);
            expect(model.domainEvents[0]).toBeInstanceOf(ai_model_status_changed_domain_event_1.AIModelStatusChangedEvent);
        });
        it('should not activate archived model', () => {
            model.archive();
            model.clearEvents();
            expect(() => model.activate()).toThrow('Cannot activate archived model');
        });
    });
    describe('Load Management', () => {
        let model;
        beforeEach(() => {
            model = ai_model_entity_1.AIModel.create(validProps);
        });
        it('should update load correctly', () => {
            model.updateLoad(10);
            expect(model.currentLoad).toBe(10);
            expect(model.lastUsed).toBeInstanceOf(Date);
        });
        it('should not allow negative load', () => {
            model.updateLoad(-5);
            expect(model.currentLoad).toBe(0);
        });
        it('should throw error when exceeding max concurrent requests', () => {
            expect(() => model.updateLoad(150)).toThrow('Load exceeds maximum concurrent requests: 100');
        });
        it('should check if can handle request', () => {
            expect(model.canHandleRequest()).toBe(true);
            model.updateLoad(100);
            expect(model.canHandleRequest()).toBe(false);
            model.deactivate();
            model.updateLoad(-50);
            expect(model.canHandleRequest()).toBe(false);
        });
        it('should calculate utilization correctly', () => {
            model.updateLoad(50);
            expect(model.getUtilization()).toBe(0.5);
        });
        it('should detect overloaded state', () => {
            model.updateLoad(85);
            expect(model.isOverloaded()).toBe(true);
        });
    });
    describe('Performance Management', () => {
        let model;
        beforeEach(() => {
            model = ai_model_entity_1.AIModel.create(validProps);
            model.clearEvents();
        });
        it('should update performance metrics', () => {
            const newMetrics = {
                accuracy: 0.98,
                precision: 0.95,
                totalRequests: 200,
            };
            model.updatePerformanceMetrics(newMetrics);
            expect(model.performance.accuracy).toBe(0.98);
            expect(model.performance.precision).toBe(0.95);
            expect(model.performance.totalRequests).toBe(200);
            expect(model.domainEvents).toHaveLength(1);
            expect(model.domainEvents[0]).toBeInstanceOf(ai_model_performance_updated_domain_event_1.AIModelPerformanceUpdatedEvent);
        });
        it('should calculate availability correctly', () => {
            const availability = model.getAvailability();
            expect(availability).toBe(0.95); // 95/100
        });
        it('should calculate success rate correctly', () => {
            const successRate = model.getSuccessRate();
            expect(successRate).toBe(0.95); // 95/100
        });
        it('should calculate performance score', () => {
            const score = model.calculatePerformanceScore();
            expect(score).toBeGreaterThan(0);
            expect(score).toBeLessThanOrEqual(1);
        });
    });
    describe('Configuration Management', () => {
        let model;
        beforeEach(() => {
            model = ai_model_entity_1.AIModel.create(validProps);
            model.clearEvents();
        });
        it('should update configuration', () => {
            const newConfig = {
                timeout: 60000,
                retries: 5,
            };
            model.updateConfiguration(newConfig);
            expect(model.configuration.timeout).toBe(60000);
            expect(model.configuration.retries).toBe(5);
            expect(model.domainEvents).toHaveLength(1);
            expect(model.domainEvents[0]).toBeInstanceOf(ai_model_configuration_updated_domain_event_1.AIModelConfigurationUpdatedEvent);
        });
    });
    describe('Tag Management', () => {
        let model;
        beforeEach(() => {
            model = ai_model_entity_1.AIModel.create(validProps);
        });
        it('should add tag', () => {
            model.addTag('new-tag');
            expect(model.tags).toContain('new-tag');
        });
        it('should not add duplicate tag', () => {
            const initialLength = model.tags.length;
            model.addTag('test'); // Already exists
            expect(model.tags).toHaveLength(initialLength);
        });
        it('should remove tag', () => {
            model.removeTag('test');
            expect(model.tags).not.toContain('test');
        });
    });
    describe('Task Type Support', () => {
        let model;
        beforeEach(() => {
            model = ai_model_entity_1.AIModel.create(validProps);
        });
        it('should check task type support', () => {
            expect(model.supportsTaskType('text-generation')).toBe(true);
            expect(model.supportsTaskType('image-generation')).toBe(false);
        });
    });
    describe('Utility Methods', () => {
        let model;
        beforeEach(() => {
            model = ai_model_entity_1.AIModel.create(validProps);
        });
        it('should record health check', () => {
            const beforeTime = new Date();
            model.recordHealthCheck();
            const afterTime = new Date();
            expect(model.lastHealthCheck).toBeInstanceOf(Date);
            expect(model.lastHealthCheck.getTime()).toBeGreaterThanOrEqual(beforeTime.getTime());
            expect(model.lastHealthCheck.getTime()).toBeLessThanOrEqual(afterTime.getTime());
        });
        it('should mark as deployed', () => {
            model.markAsDeployed();
            expect(model.deployedAt).toBeInstanceOf(Date);
            expect(model.status).toBe(ai_model_entity_1.ModelStatus.ACTIVE);
        });
        it('should update metadata', () => {
            const newMetadata = { newKey: 'newValue' };
            model.updateMetadata(newMetadata);
            expect(model.metadata.newKey).toBe('newValue');
            expect(model.metadata.version).toBe('1.0'); // Original metadata preserved
        });
    });
    describe('Immutability', () => {
        let model;
        beforeEach(() => {
            model = ai_model_entity_1.AIModel.create(validProps);
        });
        it('should return copies of arrays', () => {
            const tags = model.tags;
            const supportedTaskTypes = model.supportedTaskTypes;
            tags.push('modified');
            supportedTaskTypes.push('modified');
            expect(model.tags).not.toContain('modified');
            expect(model.supportedTaskTypes).not.toContain('modified');
        });
        it('should return copy of metadata', () => {
            const metadata = model.metadata;
            metadata.modified = true;
            expect(model.metadata.modified).toBeUndefined();
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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