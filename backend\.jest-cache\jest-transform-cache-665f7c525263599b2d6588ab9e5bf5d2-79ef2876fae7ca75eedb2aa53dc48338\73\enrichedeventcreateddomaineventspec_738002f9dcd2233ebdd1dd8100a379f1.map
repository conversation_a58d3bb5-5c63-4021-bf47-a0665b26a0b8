{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\enriched-event-created.domain-event.spec.ts", "mappings": ";;AAAA,gGAAwH;AACxH,gEAA8D;AAC9D,iEAAwD;AACxD,yEAAgE;AAChE,gFAAwE;AAExE,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;IAC/C,IAAI,SAAwC,CAAC;IAC7C,IAAI,WAA2B,CAAC;IAChC,IAAI,iBAAiC,CAAC;IAEtC,UAAU,CAAC,GAAG,EAAE;QACd,WAAW,GAAG,8BAAc,CAAC,MAAM,EAAE,CAAC;QACtC,iBAAiB,GAAG,8BAAc,CAAC,MAAM,EAAE,CAAC;QAE5C,SAAS,GAAG;YACV,iBAAiB;YACjB,SAAS,EAAE,2BAAS,CAAC,eAAe;YACpC,QAAQ,EAAE,mCAAa,CAAC,IAAI;YAC5B,gBAAgB,EAAE,wCAAgB,CAAC,SAAS;YAC5C,sBAAsB,EAAE,EAAE;YAC1B,iBAAiB,EAAE,CAAC;YACpB,mBAAmB,EAAE,CAAC;YACtB,gBAAgB,EAAE,EAAE;YACpB,oBAAoB,EAAE,IAAI;SAC3B,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,WAAW,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAEhF,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YACrD,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACjD,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,aAAa,GAAG,8BAAc,CAAC,MAAM,EAAE,CAAC;YAC9C,MAAM,gBAAgB,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC;YAChD,MAAM,aAAa,GAAG,sBAAsB,CAAC;YAC7C,MAAM,WAAW,GAAG,oBAAoB,CAAC;YACzC,MAAM,QAAQ,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;YAEpC,MAAM,WAAW,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE,SAAS,EAAE;gBAC9E,OAAO,EAAE,aAAa;gBACtB,UAAU,EAAE,gBAAgB;gBAC5B,YAAY,EAAE,CAAC;gBACf,aAAa;gBACb,WAAW;gBACX,QAAQ;aACT,CAAC,CAAC;YAEH,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YACnD,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YACzD,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACtD,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAClD,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;QACvB,IAAI,WAA4C,CAAC;QAEjD,UAAU,CAAC,GAAG,EAAE;YACd,WAAW,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,eAAe,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,IAAI,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,wCAAgB,CAAC,SAAS,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,IAAI,WAA4C,CAAC;QAEjD,UAAU,CAAC,GAAG,EAAE;YACd,WAAW,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEhD,MAAM,kBAAkB,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,mCAAa,CAAC,MAAM,EAAE,CAAC;YAC5E,MAAM,WAAW,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;YACzF,MAAM,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE7C,MAAM,YAAY,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,mCAAa,CAAC,QAAQ,EAAE,CAAC;YACxE,MAAM,aAAa,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;YACrF,MAAM,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,CAAC,WAAW,CAAC,wBAAwB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE1D,MAAM,cAAc,GAAG,EAAE,GAAG,SAAS,EAAE,sBAAsB,EAAE,EAAE,EAAE,CAAC;YACpE,MAAM,eAAe,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;YACzF,MAAM,CAAC,eAAe,CAAC,wBAAwB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE/D,MAAM,aAAa,GAAG,EAAE,GAAG,SAAS,EAAE,sBAAsB,EAAE,SAAS,EAAE,CAAC;YAC1E,MAAM,cAAc,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;YACvF,MAAM,CAAC,cAAc,CAAC,wBAAwB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,CAAC,WAAW,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEvD,MAAM,iBAAiB,GAAG,EAAE,GAAG,SAAS,EAAE,gBAAgB,EAAE,SAAS,EAAE,CAAC;YACxE,MAAM,kBAAkB,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;YAC/F,MAAM,CAAC,kBAAkB,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE/D,MAAM,mBAAmB,GAAG,EAAE,GAAG,SAAS,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;YAClE,MAAM,oBAAoB,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC;YACnG,MAAM,CAAC,oBAAoB,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,CAAC,WAAW,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEvD,MAAM,WAAW,GAAG,EAAE,GAAG,SAAS,EAAE,gBAAgB,EAAE,wCAAgB,CAAC,OAAO,EAAE,CAAC;YACjF,MAAM,YAAY,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YACnF,MAAM,CAAC,YAAY,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,CAAC,WAAW,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEnD,MAAM,cAAc,GAAG,EAAE,GAAG,SAAS,EAAE,gBAAgB,EAAE,EAAE,EAAE,CAAC;YAC9D,MAAM,eAAe,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;YACzF,MAAM,CAAC,eAAe,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEtD,MAAM,YAAY,GAAG,EAAE,GAAG,SAAS,EAAE,gBAAgB,EAAE,SAAS,EAAE,CAAC;YACnE,MAAM,aAAa,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;YACrF,MAAM,CAAC,aAAa,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,IAAI,WAA4C,CAAC;QAEjD,UAAU,CAAC,GAAG,EAAE;YACd,WAAW,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,OAAO,GAAG,WAAW,CAAC,eAAe,EAAE,CAAC;YAE9C,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC;gBACtB,eAAe,EAAE,WAAW,CAAC,QAAQ,EAAE;gBACvC,iBAAiB,EAAE,iBAAiB,CAAC,QAAQ,EAAE;gBAC/C,SAAS,EAAE,2BAAS,CAAC,eAAe;gBACpC,QAAQ,EAAE,mCAAa,CAAC,IAAI;gBAC5B,gBAAgB,EAAE,wCAAgB,CAAC,SAAS;gBAC5C,sBAAsB,EAAE,EAAE;gBAC1B,iBAAiB,EAAE,CAAC;gBACpB,mBAAmB,EAAE,CAAC;gBACtB,gBAAgB,EAAE,EAAE;gBACpB,oBAAoB,EAAE,IAAI;gBAC1B,cAAc,EAAE,IAAI;gBACpB,UAAU,EAAE,KAAK;gBACjB,wBAAwB,EAAE,IAAI;gBAC9B,qBAAqB,EAAE,IAAI;gBAC3B,qBAAqB,EAAE,IAAI;gBAC3B,gBAAgB,EAAE,KAAK;aACxB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,WAAW,GAAkC;gBACjD,iBAAiB;gBACjB,SAAS,EAAE,2BAAS,CAAC,MAAM;gBAC3B,QAAQ,EAAE,mCAAa,CAAC,GAAG;gBAC3B,gBAAgB,EAAE,wCAAgB,CAAC,OAAO;gBAC1C,iBAAiB,EAAE,CAAC;gBACpB,mBAAmB,EAAE,CAAC;gBACtB,oBAAoB,EAAE,KAAK;aAC5B,CAAC;YAEF,MAAM,YAAY,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YACnF,MAAM,OAAO,GAAG,YAAY,CAAC,eAAe,EAAE,CAAC;YAE/C,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,aAAa,EAAE,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,aAAa,EAAE,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,IAAI,WAA4C,CAAC;QAEjD,UAAU,CAAC,GAAG,EAAE;YACd,WAAW,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC;YAElC,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YACvC,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YACzC,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAE5C,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,eAAe,EAAE,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC;YAElC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACtC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,UAAU,GAAG;gBACjB,EAAE,QAAQ,EAAE,mCAAa,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE;gBACjE,EAAE,QAAQ,EAAE,mCAAa,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE;gBACpE,EAAE,QAAQ,EAAE,mCAAa,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE;gBACjE,EAAE,QAAQ,EAAE,mCAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE;aACrE,CAAC;YAEF,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,EAAE,EAAE;gBACtD,MAAM,QAAQ,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,CAAC;gBAC5C,MAAM,SAAS,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBAE7E,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAChD,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,QAAQ,GAAG;gBACf,wCAAgB,CAAC,OAAO;gBACxB,wCAAgB,CAAC,WAAW;gBAC5B,wCAAgB,CAAC,SAAS;gBAC1B,wCAAgB,CAAC,MAAM;gBACvB,wCAAgB,CAAC,OAAO;gBACxB,wCAAgB,CAAC,OAAO;aACzB,CAAC;YAEF,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACxB,MAAM,QAAQ,GAAG,EAAE,GAAG,SAAS,EAAE,gBAAgB,EAAE,MAAM,EAAE,CAAC;gBAC5D,MAAM,SAAS,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBAE7E,MAAM,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,wCAAgB,CAAC,SAAS,CAAC,CAAC;YACxF,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,SAAS,GAAG;gBAChB,EAAE,KAAK,EAAE,CAAC,EAAE,cAAc,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE;gBACtD,EAAE,KAAK,EAAE,CAAC,EAAE,cAAc,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE;gBACrD,EAAE,KAAK,EAAE,EAAE,EAAE,cAAc,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE;gBACtD,EAAE,KAAK,EAAE,EAAE,EAAE,cAAc,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE;gBACrD,EAAE,KAAK,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE;aACvD,CAAC;YAEF,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,UAAU,EAAE,EAAE,EAAE;gBAC1D,MAAM,QAAQ,GAAG,EAAE,GAAG,SAAS,EAAE,gBAAgB,EAAE,KAAK,EAAE,CAAC;gBAC3D,MAAM,SAAS,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBAE7E,MAAM,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAC/D,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,SAAS,GAAG;gBAChB,EAAE,KAAK,EAAE,CAAC,EAAE,cAAc,EAAE,KAAK,EAAE;gBACnC,EAAE,KAAK,EAAE,EAAE,EAAE,cAAc,EAAE,KAAK,EAAE;gBACpC,EAAE,KAAK,EAAE,EAAE,EAAE,cAAc,EAAE,IAAI,EAAE;gBACnC,EAAE,KAAK,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE;aACrC,CAAC;YAEF,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE;gBAC9C,MAAM,QAAQ,GAAG,EAAE,GAAG,SAAS,EAAE,sBAAsB,EAAE,KAAK,EAAE,CAAC;gBACjE,MAAM,SAAS,GAAG,IAAI,qEAA+B,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBAE7E,MAAM,CAAC,SAAS,CAAC,wBAAwB,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACpE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\enriched-event-created.domain-event.spec.ts"], "sourcesContent": ["import { EnrichedEventCreatedDomainEvent, EnrichedEventCreatedEventData } from '../enriched-event-created.domain-event';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel';\r\nimport { EventType } from '../../enums/event-type.enum';\r\nimport { EventSeverity } from '../../enums/event-severity.enum';\r\nimport { EnrichmentStatus } from '../../entities/enriched-event.entity';\r\n\r\ndescribe('EnrichedEventCreatedDomainEvent', () => {\r\n  let eventData: EnrichedEventCreatedEventData;\r\n  let aggregateId: UniqueEntityId;\r\n  let normalizedEventId: UniqueEntityId;\r\n\r\n  beforeEach(() => {\r\n    aggregateId = UniqueEntityId.create();\r\n    normalizedEventId = UniqueEntityId.create();\r\n\r\n    eventData = {\r\n      normalizedEventId,\r\n      eventType: EventType.THREAT_DETECTED,\r\n      severity: EventSeverity.HIGH,\r\n      enrichmentStatus: EnrichmentStatus.COMPLETED,\r\n      enrichmentQualityScore: 85,\r\n      appliedRulesCount: 3,\r\n      enrichmentDataCount: 5,\r\n      threatIntelScore: 75,\r\n      requiresManualReview: true,\r\n    };\r\n  });\r\n\r\n  describe('creation', () => {\r\n    it('should create domain event with required data', () => {\r\n      const domainEvent = new EnrichedEventCreatedDomainEvent(aggregateId, eventData);\r\n\r\n      expect(domainEvent.aggregateId).toEqual(aggregateId);\r\n      expect(domainEvent.eventData).toEqual(eventData);\r\n      expect(domainEvent.occurredOn).toBeInstanceOf(Date);\r\n      expect(domainEvent.eventId).toBeDefined();\r\n    });\r\n\r\n    it('should create domain event with custom options', () => {\r\n      const customEventId = UniqueEntityId.create();\r\n      const customOccurredOn = new Date('2023-01-01');\r\n      const correlationId = 'test-correlation-001';\r\n      const causationId = 'test-causation-001';\r\n      const metadata = { source: 'test' };\r\n\r\n      const domainEvent = new EnrichedEventCreatedDomainEvent(aggregateId, eventData, {\r\n        eventId: customEventId,\r\n        occurredOn: customOccurredOn,\r\n        eventVersion: 2,\r\n        correlationId,\r\n        causationId,\r\n        metadata,\r\n      });\r\n\r\n      expect(domainEvent.eventId).toEqual(customEventId);\r\n      expect(domainEvent.occurredOn).toEqual(customOccurredOn);\r\n      expect(domainEvent.eventVersion).toBe(2);\r\n      expect(domainEvent.correlationId).toBe(correlationId);\r\n      expect(domainEvent.causationId).toBe(causationId);\r\n      expect(domainEvent.metadata).toEqual(metadata);\r\n    });\r\n  });\r\n\r\n  describe('getters', () => {\r\n    let domainEvent: EnrichedEventCreatedDomainEvent;\r\n\r\n    beforeEach(() => {\r\n      domainEvent = new EnrichedEventCreatedDomainEvent(aggregateId, eventData);\r\n    });\r\n\r\n    it('should provide access to normalized event ID', () => {\r\n      expect(domainEvent.normalizedEventId).toEqual(normalizedEventId);\r\n    });\r\n\r\n    it('should provide access to event type', () => {\r\n      expect(domainEvent.eventType).toBe(EventType.THREAT_DETECTED);\r\n    });\r\n\r\n    it('should provide access to severity', () => {\r\n      expect(domainEvent.severity).toBe(EventSeverity.HIGH);\r\n    });\r\n\r\n    it('should provide access to enrichment status', () => {\r\n      expect(domainEvent.enrichmentStatus).toBe(EnrichmentStatus.COMPLETED);\r\n    });\r\n\r\n    it('should provide access to enrichment quality score', () => {\r\n      expect(domainEvent.enrichmentQualityScore).toBe(85);\r\n    });\r\n\r\n    it('should provide access to applied rules count', () => {\r\n      expect(domainEvent.appliedRulesCount).toBe(3);\r\n    });\r\n\r\n    it('should provide access to enrichment data count', () => {\r\n      expect(domainEvent.enrichmentDataCount).toBe(5);\r\n    });\r\n\r\n    it('should provide access to threat intelligence score', () => {\r\n      expect(domainEvent.threatIntelScore).toBe(75);\r\n    });\r\n\r\n    it('should provide access to manual review requirement', () => {\r\n      expect(domainEvent.requiresManualReview).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('query methods', () => {\r\n    let domainEvent: EnrichedEventCreatedDomainEvent;\r\n\r\n    beforeEach(() => {\r\n      domainEvent = new EnrichedEventCreatedDomainEvent(aggregateId, eventData);\r\n    });\r\n\r\n    it('should check if event is high severity', () => {\r\n      expect(domainEvent.isHighSeverity()).toBe(true);\r\n\r\n      const mediumSeverityData = { ...eventData, severity: EventSeverity.MEDIUM };\r\n      const mediumEvent = new EnrichedEventCreatedDomainEvent(aggregateId, mediumSeverityData);\r\n      expect(mediumEvent.isHighSeverity()).toBe(false);\r\n    });\r\n\r\n    it('should check if event is critical', () => {\r\n      expect(domainEvent.isCritical()).toBe(false);\r\n\r\n      const criticalData = { ...eventData, severity: EventSeverity.CRITICAL };\r\n      const criticalEvent = new EnrichedEventCreatedDomainEvent(aggregateId, criticalData);\r\n      expect(criticalEvent.isCritical()).toBe(true);\r\n    });\r\n\r\n    it('should check if event has high enrichment quality', () => {\r\n      expect(domainEvent.hasHighEnrichmentQuality()).toBe(true);\r\n\r\n      const lowQualityData = { ...eventData, enrichmentQualityScore: 50 };\r\n      const lowQualityEvent = new EnrichedEventCreatedDomainEvent(aggregateId, lowQualityData);\r\n      expect(lowQualityEvent.hasHighEnrichmentQuality()).toBe(false);\r\n\r\n      const noQualityData = { ...eventData, enrichmentQualityScore: undefined };\r\n      const noQualityEvent = new EnrichedEventCreatedDomainEvent(aggregateId, noQualityData);\r\n      expect(noQualityEvent.hasHighEnrichmentQuality()).toBe(false);\r\n    });\r\n\r\n    it('should check if event has threat intelligence', () => {\r\n      expect(domainEvent.hasThreatIntelligence()).toBe(true);\r\n\r\n      const noThreatIntelData = { ...eventData, threatIntelScore: undefined };\r\n      const noThreatIntelEvent = new EnrichedEventCreatedDomainEvent(aggregateId, noThreatIntelData);\r\n      expect(noThreatIntelEvent.hasThreatIntelligence()).toBe(false);\r\n\r\n      const zeroThreatIntelData = { ...eventData, threatIntelScore: 0 };\r\n      const zeroThreatIntelEvent = new EnrichedEventCreatedDomainEvent(aggregateId, zeroThreatIntelData);\r\n      expect(zeroThreatIntelEvent.hasThreatIntelligence()).toBe(false);\r\n    });\r\n\r\n    it('should check if enrichment is completed', () => {\r\n      expect(domainEvent.isEnrichmentCompleted()).toBe(true);\r\n\r\n      const pendingData = { ...eventData, enrichmentStatus: EnrichmentStatus.PENDING };\r\n      const pendingEvent = new EnrichedEventCreatedDomainEvent(aggregateId, pendingData);\r\n      expect(pendingEvent.isEnrichmentCompleted()).toBe(false);\r\n    });\r\n\r\n    it('should check if event is high threat risk', () => {\r\n      expect(domainEvent.isHighThreatRisk()).toBe(false);\r\n\r\n      const highThreatData = { ...eventData, threatIntelScore: 90 };\r\n      const highThreatEvent = new EnrichedEventCreatedDomainEvent(aggregateId, highThreatData);\r\n      expect(highThreatEvent.isHighThreatRisk()).toBe(true);\r\n\r\n      const noThreatData = { ...eventData, threatIntelScore: undefined };\r\n      const noThreatEvent = new EnrichedEventCreatedDomainEvent(aggregateId, noThreatData);\r\n      expect(noThreatEvent.isHighThreatRisk()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('event summary', () => {\r\n    let domainEvent: EnrichedEventCreatedDomainEvent;\r\n\r\n    beforeEach(() => {\r\n      domainEvent = new EnrichedEventCreatedDomainEvent(aggregateId, eventData);\r\n    });\r\n\r\n    it('should generate comprehensive event summary', () => {\r\n      const summary = domainEvent.getEventSummary();\r\n\r\n      expect(summary).toEqual({\r\n        enrichedEventId: aggregateId.toString(),\r\n        normalizedEventId: normalizedEventId.toString(),\r\n        eventType: EventType.THREAT_DETECTED,\r\n        severity: EventSeverity.HIGH,\r\n        enrichmentStatus: EnrichmentStatus.COMPLETED,\r\n        enrichmentQualityScore: 85,\r\n        appliedRulesCount: 3,\r\n        enrichmentDataCount: 5,\r\n        threatIntelScore: 75,\r\n        requiresManualReview: true,\r\n        isHighSeverity: true,\r\n        isCritical: false,\r\n        hasHighEnrichmentQuality: true,\r\n        hasThreatIntelligence: true,\r\n        isEnrichmentCompleted: true,\r\n        isHighThreatRisk: false,\r\n      });\r\n    });\r\n\r\n    it('should handle undefined optional values in summary', () => {\r\n      const minimalData: EnrichedEventCreatedEventData = {\r\n        normalizedEventId,\r\n        eventType: EventType.CUSTOM,\r\n        severity: EventSeverity.LOW,\r\n        enrichmentStatus: EnrichmentStatus.PENDING,\r\n        appliedRulesCount: 0,\r\n        enrichmentDataCount: 0,\r\n        requiresManualReview: false,\r\n      };\r\n\r\n      const minimalEvent = new EnrichedEventCreatedDomainEvent(aggregateId, minimalData);\r\n      const summary = minimalEvent.getEventSummary();\r\n\r\n      expect(summary.enrichmentQualityScore).toBeUndefined();\r\n      expect(summary.threatIntelScore).toBeUndefined();\r\n      expect(summary.hasHighEnrichmentQuality).toBe(false);\r\n      expect(summary.hasThreatIntelligence).toBe(false);\r\n      expect(summary.isHighThreatRisk).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('serialization', () => {\r\n    let domainEvent: EnrichedEventCreatedDomainEvent;\r\n\r\n    beforeEach(() => {\r\n      domainEvent = new EnrichedEventCreatedDomainEvent(aggregateId, eventData);\r\n    });\r\n\r\n    it('should serialize to JSON with event summary', () => {\r\n      const json = domainEvent.toJSON();\r\n\r\n      expect(json).toHaveProperty('eventId');\r\n      expect(json).toHaveProperty('aggregateId');\r\n      expect(json).toHaveProperty('occurredOn');\r\n      expect(json).toHaveProperty('eventData');\r\n      expect(json).toHaveProperty('eventSummary');\r\n\r\n      expect(json.eventData).toEqual(eventData);\r\n      expect(json.eventSummary).toEqual(domainEvent.getEventSummary());\r\n    });\r\n\r\n    it('should include base domain event properties in JSON', () => {\r\n      const json = domainEvent.toJSON();\r\n\r\n      expect(json.eventId).toBeDefined();\r\n      expect(json.aggregateId).toBe(aggregateId.toString());\r\n      expect(json.occurredOn).toBeDefined();\r\n      expect(json.eventVersion).toBe(1); // Default version\r\n    });\r\n  });\r\n\r\n  describe('edge cases', () => {\r\n    it('should handle all severity levels correctly', () => {\r\n      const severities = [\r\n        { severity: EventSeverity.LOW, isHigh: false, isCritical: false },\r\n        { severity: EventSeverity.MEDIUM, isHigh: false, isCritical: false },\r\n        { severity: EventSeverity.HIGH, isHigh: true, isCritical: false },\r\n        { severity: EventSeverity.CRITICAL, isHigh: true, isCritical: true },\r\n      ];\r\n\r\n      severities.forEach(({ severity, isHigh, isCritical }) => {\r\n        const testData = { ...eventData, severity };\r\n        const testEvent = new EnrichedEventCreatedDomainEvent(aggregateId, testData);\r\n\r\n        expect(testEvent.isHighSeverity()).toBe(isHigh);\r\n        expect(testEvent.isCritical()).toBe(isCritical);\r\n      });\r\n    });\r\n\r\n    it('should handle all enrichment statuses correctly', () => {\r\n      const statuses = [\r\n        EnrichmentStatus.PENDING,\r\n        EnrichmentStatus.IN_PROGRESS,\r\n        EnrichmentStatus.COMPLETED,\r\n        EnrichmentStatus.FAILED,\r\n        EnrichmentStatus.PARTIAL,\r\n        EnrichmentStatus.SKIPPED,\r\n      ];\r\n\r\n      statuses.forEach(status => {\r\n        const testData = { ...eventData, enrichmentStatus: status };\r\n        const testEvent = new EnrichedEventCreatedDomainEvent(aggregateId, testData);\r\n\r\n        expect(testEvent.isEnrichmentCompleted()).toBe(status === EnrichmentStatus.COMPLETED);\r\n      });\r\n    });\r\n\r\n    it('should handle boundary values for threat intelligence score', () => {\r\n      const testCases = [\r\n        { score: 0, hasThreatIntel: false, isHighRisk: false },\r\n        { score: 1, hasThreatIntel: true, isHighRisk: false },\r\n        { score: 84, hasThreatIntel: true, isHighRisk: false },\r\n        { score: 85, hasThreatIntel: true, isHighRisk: true },\r\n        { score: 100, hasThreatIntel: true, isHighRisk: true },\r\n      ];\r\n\r\n      testCases.forEach(({ score, hasThreatIntel, isHighRisk }) => {\r\n        const testData = { ...eventData, threatIntelScore: score };\r\n        const testEvent = new EnrichedEventCreatedDomainEvent(aggregateId, testData);\r\n\r\n        expect(testEvent.hasThreatIntelligence()).toBe(hasThreatIntel);\r\n        expect(testEvent.isHighThreatRisk()).toBe(isHighRisk);\r\n      });\r\n    });\r\n\r\n    it('should handle boundary values for enrichment quality score', () => {\r\n      const testCases = [\r\n        { score: 0, hasHighQuality: false },\r\n        { score: 69, hasHighQuality: false },\r\n        { score: 70, hasHighQuality: true },\r\n        { score: 100, hasHighQuality: true },\r\n      ];\r\n\r\n      testCases.forEach(({ score, hasHighQuality }) => {\r\n        const testData = { ...eventData, enrichmentQualityScore: score };\r\n        const testEvent = new EnrichedEventCreatedDomainEvent(aggregateId, testData);\r\n\r\n        expect(testEvent.hasHighEnrichmentQuality()).toBe(hasHighQuality);\r\n      });\r\n    });\r\n  });\r\n});"], "version": 3}