{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\application\\services\\correlation.service.ts", "mappings": ";;;;;;;;;;;;;;AAAA,2CAA4C;AAE5C,iGAA4H;AAG5H,iGAA4F;AAC5F,yEAAsE;AA2DtE;;;;;;;;;;;;GAYG;AAEI,IAAM,kBAAkB,0BAAxB,MAAM,kBAAkB;IAW7B,YACmB,cAAoC;QAApC,mBAAc,GAAd,cAAc,CAAsB;QAXtC,WAAM,GAAG,IAAI,eAAM,CAAC,oBAAkB,CAAC,IAAI,CAAC,CAAC;QAC7C,kBAAa,GAAsB;YAClD,cAAc,EAAE,EAAE,EAAE,aAAa;YACjC,SAAS,EAAE,CAAC;YACZ,aAAa,EAAE,EAAE;YACjB,SAAS,EAAE,GAAG;YACd,YAAY,EAAE,MAAM,CAAC,MAAM,CAAC,yCAAe,CAAC;YAC5C,UAAU,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,CAAC;SAC/D,CAAC;IAIC,CAAC;IAEJ;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,OAA2B;QAC/C,MAAM,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;QAC5D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE;YAC7C,UAAU,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM;YACjC,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,MAAM,CAAC,cAAc;YACvD,YAAY,EAAE,MAAM,CAAC,YAAY;SAClC,CAAC,CAAC;QAEH,MAAM,MAAM,GAAsB;YAChC,OAAO,EAAE,KAAK;YACd,gBAAgB,EAAE,EAAE;YACpB,QAAQ,EAAE,CAAC;YACX,UAAU,EAAE;gBACV,eAAe,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM;gBACtC,iBAAiB,EAAE,CAAC;gBACpB,iBAAiB,EAAE,CAAC;gBACpB,gBAAgB,EAAE,EAAqC;aACxD;YACD,MAAM,EAAE,EAAE;SACX,CAAC;QAEF,IAAI,CAAC;YACH,4BAA4B;YAC5B,MAAM,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAEzE,IAAI,cAAc,CAAC,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;gBAC7C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE;oBACtD,cAAc,EAAE,cAAc,CAAC,MAAM;oBACrC,WAAW,EAAE,MAAM,CAAC,SAAS;iBAC9B,CAAC,CAAC;gBACH,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,qCAAqC;gBAC5D,OAAO,MAAM,CAAC;YAChB,CAAC;YAED,yCAAyC;YACzC,MAAM,YAAY,GAAsB,EAAE,CAAC;YAE3C,KAAK,MAAM,eAAe,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBAClD,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;oBACxE,SAAS;gBACX,CAAC;gBAED,IAAI,CAAC;oBACH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CACxD,eAAe,EACf,cAAc,EACd,MAAM,CACP,CAAC;oBACF,YAAY,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,CAAC;gBACzC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,eAAe,SAAS,EAAE;wBAC9D,KAAK,EAAE,KAAK,CAAC,OAAO;wBACpB,UAAU,EAAE,cAAc,CAAC,MAAM;qBAClC,CAAC,CAAC;oBAEH,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;wBACjB,IAAI,EAAE,eAAe;wBACrB,OAAO,EAAE,KAAK,CAAC,OAAO;wBACtB,cAAc,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC;qBACzD,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,uDAAuD;YACvD,MAAM,kBAAkB,GAAG,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;YAEtE,yCAAyC;YACzC,KAAK,MAAM,WAAW,IAAI,kBAAkB,EAAE,CAAC;gBAC7C,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,WAAW,CAAC,eAAe,EAAE,CAAC,CAAC;gBACpE,WAAW,CAAC,iBAAiB,EAAE,CAAC;YAClC,CAAC;YAED,MAAM,CAAC,gBAAgB,GAAG,kBAAkB,CAAC;YAC7C,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,CAAC;YACjE,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;YAEtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE;gBAC9C,eAAe,EAAE,cAAc,CAAC,MAAM;gBACtC,iBAAiB,EAAE,kBAAkB,CAAC,MAAM;gBAC5C,iBAAiB,EAAE,MAAM,CAAC,UAAU,CAAC,iBAAiB;gBACtD,QAAQ,EAAE,MAAM,CAAC,QAAQ;aAC1B,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;gBAC5C,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,KAAK,EAAE,KAAK,CAAC,KAAK;gBAClB,UAAU,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM;aAClC,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;gBACjB,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,cAAc,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC;aACzD,CAAC,CAAC;QACL,CAAC;gBAAS,CAAC;YACT,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAC3C,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAC5B,QAAuB,EACvB,YAA6B,EAC7B,MAAmC;QAEnC,MAAM,iBAAiB,GAAG,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE,GAAG,MAAM,EAAE,CAAC;QAE/D,0CAA0C;QAC1C,MAAM,UAAU,GAAG,iBAAiB,CAAC,cAAc,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,0BAA0B;QAC3F,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,UAAU,CAAC,CAAC;QAErD,MAAM,cAAc,GAAG,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CACjD,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,IAAI,UAAU,CACjD,CAAC;QAEF,oBAAoB;QACpB,MAAM,SAAS,GAAG,CAAC,QAAQ,EAAE,GAAG,cAAc,CAAC,CAAC;QAEhD,MAAM,kBAAkB,GAAuB;YAC7C,MAAM,EAAE,SAAS;YACjB,MAAM,EAAE,iBAAiB;YACzB,UAAU,EAAE,CAAC,yCAAe,CAAC,QAAQ,EAAE,yCAAe,CAAC,SAAS,CAAC;SAClE,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC;QAC9D,OAAO,MAAM,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,oBAAoB,CAC1B,MAAuB,EACvB,MAAyB;QAEzB,OAAO,MAAM;aACV,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC;aAC9C,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,eAAe,IAAI,MAAM,CAAC,aAAa,CAAC;aAC9D,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,SAAS,CAAC;aAC1B,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,eAAe,GAAG,CAAC,CAAC,eAAe,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAClC,IAAqB,EACrB,MAAuB,EACvB,MAAyB;QAEzB,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,yCAAe,CAAC,QAAQ;gBAC3B,OAAO,IAAI,CAAC,0BAA0B,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YACzD,KAAK,yCAAe,CAAC,OAAO;gBAC1B,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YACxD,KAAK,yCAAe,CAAC,SAAS;gBAC5B,OAAO,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAC1D,KAAK,yCAAe,CAAC,UAAU;gBAC7B,OAAO,IAAI,CAAC,4BAA4B,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAC3D,KAAK,yCAAe,CAAC,QAAQ;gBAC3B,OAAO,IAAI,CAAC,0BAA0B,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YACzD,KAAK,yCAAe,CAAC,YAAY;gBAC/B,OAAO,IAAI,CAAC,6BAA6B,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAC5D;gBACE,OAAO,EAAE,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,0BAA0B,CACtC,MAAuB,EACvB,MAAyB;QAEzB,MAAM,YAAY,GAAsB,EAAE,CAAC;QAC3C,MAAM,UAAU,GAAG,MAAM,CAAC,cAAc,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,0BAA0B;QAEhF,+BAA+B;QAC/B,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAE9D,KAAK,MAAM,KAAK,IAAI,UAAU,EAAE,CAAC;YAC/B,IAAI,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,SAAS;gBAAE,SAAS;YAE9C,MAAM,CAAC,YAAY,EAAE,GAAG,aAAa,CAAC,GAAG,KAAK,CAAC;YAE/C,MAAM,QAAQ,GAAwB;gBACpC,IAAI,EAAE,yCAAe,CAAC,QAAQ;gBAC9B,QAAQ,EAAE,CAAC;wBACT,IAAI,EAAE,gBAAgB;wBACtB,WAAW,EAAE,GAAG,KAAK,CAAC,MAAM,kBAAkB,MAAM,CAAC,cAAc,UAAU;wBAC7E,UAAU,EAAE,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC;wBACnD,cAAc,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC;wBAC/C,UAAU,EAAE,EAAE,UAAU,EAAE,MAAM,CAAC,cAAc,EAAE;wBACjD,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC;qBAC3C,CAAC;gBACF,gBAAgB,EAAE,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;gBAClD,mBAAmB,EAAE;oBACnB,UAAU;oBACV,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;wBACrC,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE;wBAC5B,SAAS,EAAE,KAAK,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE;wBACxC,KAAK,EAAE,KAAK;qBACb,CAAC,CAAC;oBACH,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;oBACvC,WAAW,EAAE,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC;oBACjD,UAAU,EAAE,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC;iBACpD;aACF,CAAC;YAEF,MAAM,WAAW,GAAG,yCAAe,CAAC,MAAM,CACxC,YAAY,EACZ,aAAa,EACb,QAAQ,EACR;gBACE,aAAa,EAAE,KAAK;gBACpB,cAAc,EAAE,CAAC,qBAAqB,CAAC;gBACvC,kBAAkB,EAAE,CAAC;gBACrB,YAAY,EAAE,IAAI,IAAI,EAAE;gBACxB,WAAW,EAAE,CAAC,iBAAiB,CAAC;gBAChC,YAAY,EAAE,CAAC,iBAAiB,CAAC;gBACjC,kBAAkB,EAAE;oBAClB,eAAe,EAAE,KAAK,CAAC,MAAM;oBAC7B,iBAAiB,EAAE,CAAC;oBACpB,cAAc,EAAE,CAAC;oBACjB,cAAc,EAAE,IAAI;iBACrB;aACF,CACF,CAAC;YAEF,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACjC,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,2BAA2B,CACvC,MAAuB,EACvB,MAAyB;QAEzB,MAAM,YAAY,GAAsB,EAAE,CAAC;QAE3C,oCAAoC;QACpC,MAAM,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;QAE7D,KAAK,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,IAAI,eAAe,EAAE,CAAC;YAClD,IAAI,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,SAAS;gBAAE,SAAS;YAE9C,MAAM,CAAC,YAAY,EAAE,GAAG,aAAa,CAAC,GAAG,KAAK,CAAC;YAE/C,MAAM,QAAQ,GAAwB;gBACpC,IAAI,EAAE,yCAAe,CAAC,SAAS;gBAC/B,QAAQ,EAAE,CAAC;wBACT,IAAI,EAAE,mBAAmB;wBACzB,WAAW,EAAE,GAAG,UAAU,CAAC,MAAM,6BAA6B,KAAK,CAAC,MAAM,SAAS;wBACnF,UAAU,EAAE,IAAI,CAAC,4BAA4B,CAAC,UAAU,EAAE,KAAK,CAAC;wBAChE,cAAc,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC;wBAC/C,UAAU,EAAE,EAAE,cAAc,EAAE,UAAU,CAAC,MAAM,EAAE;wBACjD,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;qBAC/C,CAAC;gBACF,gBAAgB,EAAE,UAAU;aAC7B,CAAC;YAEF,MAAM,WAAW,GAAG,yCAAe,CAAC,MAAM,CACxC,YAAY,EACZ,aAAa,EACb,QAAQ,EACR;gBACE,aAAa,EAAE,KAAK;gBACpB,cAAc,EAAE,CAAC,oBAAoB,CAAC;gBACtC,kBAAkB,EAAE,CAAC;gBACrB,YAAY,EAAE,IAAI,IAAI,EAAE;gBACxB,WAAW,EAAE,CAAC,qBAAqB,CAAC;gBACpC,YAAY,EAAE,CAAC,mBAAmB,CAAC;gBACnC,kBAAkB,EAAE;oBAClB,eAAe,EAAE,KAAK,CAAC,MAAM;oBAC7B,iBAAiB,EAAE,CAAC;oBACpB,cAAc,EAAE,CAAC;oBACjB,cAAc,EAAE,IAAI;iBACrB;aACF,CACF,CAAC;YAEF,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACjC,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,yBAAyB,CACrC,MAAuB,EACvB,MAAyB;QAEzB,4EAA4E;QAC5E,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,4BAA4B,CACxC,MAAuB,EACvB,MAAyB;QAEzB,uEAAuE;QACvE,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,0BAA0B,CACtC,MAAuB,EACvB,MAAyB;QAEzB,oEAAoE;QACpE,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,6BAA6B,CACzC,MAAuB,EACvB,MAAyB;QAEzB,gFAAgF;QAChF,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,MAAuB,EAAE,UAAkB;QACnE,MAAM,MAAM,GAAsB,EAAE,CAAC;QACrC,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACxC,CAAC,CAAC,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,CAC9D,CAAC;QAEF,IAAI,YAAY,GAAoB,EAAE,CAAC;QACvC,IAAI,cAAc,GAAkB,IAAI,CAAC;QAEzC,KAAK,MAAM,KAAK,IAAI,YAAY,EAAE,CAAC;YACjC,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;YAElD,IAAI,cAAc,KAAK,IAAI,IAAI,SAAS,GAAG,cAAc,IAAI,UAAU,EAAE,CAAC;gBACxE,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACzB,IAAI,cAAc,KAAK,IAAI,EAAE,CAAC;oBAC5B,cAAc,GAAG,SAAS,CAAC;gBAC7B,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,YAAY,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;oBAC7B,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC5B,CAAC;gBACD,YAAY,GAAG,CAAC,KAAK,CAAC,CAAC;gBACvB,cAAc,GAAG,SAAS,CAAC;YAC7B,CAAC;QACH,CAAC;QAED,IAAI,YAAY,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAC7B,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC5B,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,MAAuB;QACrD,MAAM,MAAM,GAAG,IAAI,GAAG,EAA2B,CAAC;QAElD,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;YACpC,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC;gBAAE,SAAS;YAEtC,4CAA4C;YAC5C,MAAM,GAAG,GAAG,UAAU;iBACnB,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,KAAK,EAAE,CAAC;iBACtC,IAAI,EAAE;iBACN,IAAI,CAAC,GAAG,CAAC,CAAC;YAEb,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBACrB,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;YACtB,CAAC;YACD,MAAM,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC;QAED,6BAA6B;QAC7B,MAAM,MAAM,GAAG,IAAI,GAAG,EAA0B,CAAC;QACjD,KAAK,MAAM,CAAC,GAAG,EAAE,UAAU,CAAC,IAAI,MAAM,EAAE,CAAC;YACvC,IAAI,UAAU,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;gBAC3B,MAAM,UAAU,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;gBAC5C,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,MAAuB;QAClD,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,EAAE,CAAC;QAEnC,MAAM,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,GAAG,CAC/C,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,KAAK,EAAE,CAAC,CACxD,CAAC,CAAC;QAEH,MAAM,mBAAmB,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE,CACnE,IAAI,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CACjD,CAAC;QAEF,8BAA8B;QAC9B,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAC7B,OAAO,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CACxC,mBAAmB,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,KAAK,EAAE,CAAC,CACpD,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,2BAA2B,CAAC,MAAuB;QACzD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;QACxD,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC;QACjG,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,cAAc,GAAG,YAAY,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACK,4BAA4B,CAAC,UAAiB,EAAE,MAAuB;QAC7E,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,UAAU,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;QAC5D,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACxD,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,cAAc,GAAG,eAAe,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,MAAuB;QAC/C,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACxC,CAAC,CAAC,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,CAC9D,CAAC;QAEF,MAAM,IAAI,GAAa,EAAE,CAAC;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;gBAC1C,CAAC,YAAY,CAAC,CAAC,GAAC,CAAC,CAAC,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAC3D,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,wBAAwB,CAAC,MAAuB;QACtD,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAEhD,IAAI,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,sBAAsB;YAC9D,OAAO,cAAc,CAAC;QACxB,CAAC;QAED,IAAI,QAAQ,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACzB,OAAO,YAAY,CAAC;QACtB,CAAC;QAED,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;QAC7E,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;QAErG,IAAI,QAAQ,GAAG,MAAM,GAAG,GAAG,EAAE,CAAC;YAC5B,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,2BAA2B,CAAC,MAAuB;QACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAChD,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,GAAG,CAAC;QAEtC,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;QAC7E,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;QACrG,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEnC,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC;QAC9D,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,YAA+B;QAC7D,MAAM,IAAI,GAAG,IAAI,GAAG,EAAU,CAAC;QAC/B,MAAM,MAAM,GAAsB,EAAE,CAAC;QAErC,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;YACvC,MAAM,GAAG,GAAG,WAAW,CAAC,WAAW;iBAChC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC;iBACxB,IAAI,EAAE;iBACN,IAAI,CAAC,GAAG,CAAC,CAAC;YAEb,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBACnB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACd,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC3B,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,YAA+B;QAMzD,MAAM,gBAAgB,GAAG,EAAqC,CAAC;QAE/D,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,MAAM,CAAC,yCAAe,CAAC,EAAE,CAAC;YAClD,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7B,CAAC;QAED,IAAI,eAAe,GAAG,CAAC,CAAC;QACxB,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;YACvC,gBAAgB,CAAC,WAAW,CAAC,eAAe,CAAC,EAAE,CAAC;YAChD,eAAe,IAAI,WAAW,CAAC,KAAK,CAAC;QACvC,CAAC;QAED,OAAO;YACL,eAAe,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;YACvE,iBAAiB,EAAE,YAAY,CAAC,MAAM;YACtC,iBAAiB,EAAE,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACtF,gBAAgB;SACjB,CAAC;IACJ,CAAC;CACF,CAAA;AAjkBY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;yDAawB,6CAAoB,oBAApB,6CAAoB;GAZ5C,kBAAkB,CAikB9B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\application\\services\\correlation.service.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { EnrichedEvent } from '../../domain/entities/event/enriched-event.entity';\r\nimport { CorrelatedEvent, CorrelationType, CorrelationAnalysis } from '../../domain/entities/event/correlated-event.entity';\r\nimport { IOC } from '../../domain/value-objects/threat-indicators/ioc.value-object';\r\nimport { ConfidenceLevel } from '../../domain/enums/confidence-level.enum';\r\nimport { DomainEventPublisher } from '../../../shared-kernel/domain/domain-event-publisher';\r\nimport { Logger } from '../../../shared-kernel/infrastructure/logger';\r\n\r\n/**\r\n * Correlation Configuration\r\n */\r\nexport interface CorrelationConfig {\r\n  /** Time window for temporal correlation (minutes) */\r\n  temporalWindow: number;\r\n  /** Minimum events required for correlation */\r\n  minEvents: number;\r\n  /** Minimum confidence threshold */\r\n  minConfidence: number;\r\n  /** Maximum events to correlate */\r\n  maxEvents: number;\r\n  /** Enable different correlation types */\r\n  enabledTypes: CorrelationType[];\r\n  /** Correlation algorithms to use */\r\n  algorithms: string[];\r\n}\r\n\r\n/**\r\n * Correlation Request\r\n */\r\nexport interface CorrelationRequest {\r\n  /** Events to correlate */\r\n  events: EnrichedEvent[];\r\n  /** Correlation configuration */\r\n  config?: Partial<CorrelationConfig>;\r\n  /** Time window override */\r\n  timeWindow?: number;\r\n  /** Focus on specific correlation types */\r\n  focusTypes?: CorrelationType[];\r\n}\r\n\r\n/**\r\n * Correlation Result\r\n */\r\nexport interface CorrelationResult {\r\n  /** Correlation success */\r\n  success: boolean;\r\n  /** Correlated events found */\r\n  correlatedEvents: CorrelatedEvent[];\r\n  /** Processing duration */\r\n  duration: number;\r\n  /** Correlation statistics */\r\n  statistics: {\r\n    eventsProcessed: number;\r\n    correlationsFound: number;\r\n    averageConfidence: number;\r\n    typeDistribution: Record<CorrelationType, number>;\r\n  };\r\n  /** Processing errors */\r\n  errors: Array<{\r\n    type: string;\r\n    message: string;\r\n    affectedEvents: string[];\r\n  }>;\r\n}\r\n\r\n/**\r\n * Correlation Application Service\r\n * \r\n * Analyzes enriched events to identify relationships and patterns\r\n * that indicate coordinated attacks or related security incidents.\r\n * \r\n * Key responsibilities:\r\n * - Multi-dimensional event correlation\r\n * - Attack chain reconstruction\r\n * - Campaign attribution\r\n * - Pattern recognition and analysis\r\n * - Threat escalation based on correlations\r\n */\r\n@Injectable()\r\nexport class CorrelationService {\r\n  private readonly logger = new Logger(CorrelationService.name);\r\n  private readonly defaultConfig: CorrelationConfig = {\r\n    temporalWindow: 60, // 60 minutes\r\n    minEvents: 2,\r\n    minConfidence: 50,\r\n    maxEvents: 100,\r\n    enabledTypes: Object.values(CorrelationType),\r\n    algorithms: ['temporal', 'spatial', 'indicator', 'behavioral'],\r\n  };\r\n\r\n  constructor(\r\n    private readonly eventPublisher: DomainEventPublisher\r\n  ) {}\r\n\r\n  /**\r\n   * Correlate enriched events\r\n   */\r\n  async correlateEvents(request: CorrelationRequest): Promise<CorrelationResult> {\r\n    const config = { ...this.defaultConfig, ...request.config };\r\n    const startTime = Date.now();\r\n    \r\n    this.logger.info('Starting event correlation', {\r\n      eventCount: request.events.length,\r\n      timeWindow: request.timeWindow || config.temporalWindow,\r\n      enabledTypes: config.enabledTypes,\r\n    });\r\n\r\n    const result: CorrelationResult = {\r\n      success: false,\r\n      correlatedEvents: [],\r\n      duration: 0,\r\n      statistics: {\r\n        eventsProcessed: request.events.length,\r\n        correlationsFound: 0,\r\n        averageConfidence: 0,\r\n        typeDistribution: {} as Record<CorrelationType, number>,\r\n      },\r\n      errors: [],\r\n    };\r\n\r\n    try {\r\n      // Filter and prepare events\r\n      const eligibleEvents = this.filterEligibleEvents(request.events, config);\r\n      \r\n      if (eligibleEvents.length < config.minEvents) {\r\n        this.logger.warn('Insufficient events for correlation', {\r\n          eligibleEvents: eligibleEvents.length,\r\n          minRequired: config.minEvents,\r\n        });\r\n        result.success = true; // Not an error, just no correlations\r\n        return result;\r\n      }\r\n\r\n      // Perform different types of correlation\r\n      const correlations: CorrelatedEvent[] = [];\r\n\r\n      for (const correlationType of config.enabledTypes) {\r\n        if (request.focusTypes && !request.focusTypes.includes(correlationType)) {\r\n          continue;\r\n        }\r\n\r\n        try {\r\n          const typeCorrelations = await this.performCorrelationType(\r\n            correlationType,\r\n            eligibleEvents,\r\n            config\r\n          );\r\n          correlations.push(...typeCorrelations);\r\n        } catch (error) {\r\n          this.logger.error(`Correlation type ${correlationType} failed`, {\r\n            error: error.message,\r\n            eventCount: eligibleEvents.length,\r\n          });\r\n          \r\n          result.errors.push({\r\n            type: correlationType,\r\n            message: error.message,\r\n            affectedEvents: eligibleEvents.map(e => e.id.toString()),\r\n          });\r\n        }\r\n      }\r\n\r\n      // Remove duplicate correlations and merge similar ones\r\n      const uniqueCorrelations = this.deduplicateCorrelations(correlations);\r\n\r\n      // Publish domain events for correlations\r\n      for (const correlation of uniqueCorrelations) {\r\n        await this.eventPublisher.publishAll(correlation.getDomainEvents());\r\n        correlation.clearDomainEvents();\r\n      }\r\n\r\n      result.correlatedEvents = uniqueCorrelations;\r\n      result.statistics = this.calculateStatistics(uniqueCorrelations);\r\n      result.success = true;\r\n\r\n      this.logger.info('Event correlation completed', {\r\n        eventsProcessed: eligibleEvents.length,\r\n        correlationsFound: uniqueCorrelations.length,\r\n        averageConfidence: result.statistics.averageConfidence,\r\n        duration: result.duration,\r\n      });\r\n\r\n    } catch (error) {\r\n      this.logger.error('Event correlation failed', {\r\n        error: error.message,\r\n        stack: error.stack,\r\n        eventCount: request.events.length,\r\n      });\r\n\r\n      result.errors.push({\r\n        type: 'general',\r\n        message: error.message,\r\n        affectedEvents: request.events.map(e => e.id.toString()),\r\n      });\r\n    } finally {\r\n      result.duration = Date.now() - startTime;\r\n    }\r\n\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * Perform real-time correlation for streaming events\r\n   */\r\n  async correlateStreamingEvents(\r\n    newEvent: EnrichedEvent,\r\n    recentEvents: EnrichedEvent[],\r\n    config?: Partial<CorrelationConfig>\r\n  ): Promise<CorrelatedEvent[]> {\r\n    const correlationConfig = { ...this.defaultConfig, ...config };\r\n    \r\n    // Filter recent events within time window\r\n    const timeWindow = correlationConfig.temporalWindow * 60 * 1000; // Convert to milliseconds\r\n    const cutoffTime = new Date(Date.now() - timeWindow);\r\n    \r\n    const eligibleEvents = recentEvents.filter(event => \r\n      event.createdAt && event.createdAt >= cutoffTime\r\n    );\r\n\r\n    // Add the new event\r\n    const allEvents = [newEvent, ...eligibleEvents];\r\n\r\n    const correlationRequest: CorrelationRequest = {\r\n      events: allEvents,\r\n      config: correlationConfig,\r\n      focusTypes: [CorrelationType.TEMPORAL, CorrelationType.INDICATOR],\r\n    };\r\n\r\n    const result = await this.correlateEvents(correlationRequest);\r\n    return result.correlatedEvents;\r\n  }\r\n\r\n  /**\r\n   * Filter events eligible for correlation\r\n   */\r\n  private filterEligibleEvents(\r\n    events: EnrichedEvent[],\r\n    config: CorrelationConfig\r\n  ): EnrichedEvent[] {\r\n    return events\r\n      .filter(event => event.isReadyForCorrelation())\r\n      .filter(event => event.enrichmentScore >= config.minConfidence)\r\n      .slice(0, config.maxEvents)\r\n      .sort((a, b) => b.enrichmentScore - a.enrichmentScore);\r\n  }\r\n\r\n  /**\r\n   * Perform specific correlation type\r\n   */\r\n  private async performCorrelationType(\r\n    type: CorrelationType,\r\n    events: EnrichedEvent[],\r\n    config: CorrelationConfig\r\n  ): Promise<CorrelatedEvent[]> {\r\n    switch (type) {\r\n      case CorrelationType.TEMPORAL:\r\n        return this.performTemporalCorrelation(events, config);\r\n      case CorrelationType.SPATIAL:\r\n        return this.performSpatialCorrelation(events, config);\r\n      case CorrelationType.INDICATOR:\r\n        return this.performIndicatorCorrelation(events, config);\r\n      case CorrelationType.BEHAVIORAL:\r\n        return this.performBehavioralCorrelation(events, config);\r\n      case CorrelationType.CAMPAIGN:\r\n        return this.performCampaignCorrelation(events, config);\r\n      case CorrelationType.ATTACK_CHAIN:\r\n        return this.performAttackChainCorrelation(events, config);\r\n      default:\r\n        return [];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Perform temporal correlation\r\n   */\r\n  private async performTemporalCorrelation(\r\n    events: EnrichedEvent[],\r\n    config: CorrelationConfig\r\n  ): Promise<CorrelatedEvent[]> {\r\n    const correlations: CorrelatedEvent[] = [];\r\n    const timeWindow = config.temporalWindow * 60 * 1000; // Convert to milliseconds\r\n\r\n    // Group events by time windows\r\n    const timeGroups = this.groupEventsByTime(events, timeWindow);\r\n\r\n    for (const group of timeGroups) {\r\n      if (group.length < config.minEvents) continue;\r\n\r\n      const [primaryEvent, ...relatedEvents] = group;\r\n      \r\n      const analysis: CorrelationAnalysis = {\r\n        type: CorrelationType.TEMPORAL,\r\n        patterns: [{\r\n          type: 'temporal_burst',\r\n          description: `${group.length} events within ${config.temporalWindow} minutes`,\r\n          confidence: this.calculateTemporalConfidence(group),\r\n          matchingEvents: group.map(e => e.id.toString()),\r\n          attributes: { timeWindow: config.temporalWindow },\r\n          strength: Math.min(1.0, group.length / 10),\r\n        }],\r\n        commonIndicators: this.findCommonIndicators(group),\r\n        temporalCorrelation: {\r\n          timeWindow,\r\n          sequence: group.map((event, index) => ({\r\n            eventId: event.id.toString(),\r\n            timestamp: event.createdAt || new Date(),\r\n            order: index,\r\n          })),\r\n          timeGaps: this.calculateTimeGaps(group),\r\n          patternType: this.determineTemporalPattern(group),\r\n          regularity: this.calculateTemporalRegularity(group),\r\n        },\r\n      };\r\n\r\n      const correlation = CorrelatedEvent.create(\r\n        primaryEvent,\r\n        relatedEvents,\r\n        analysis,\r\n        {\r\n          engineVersion: '1.0',\r\n          algorithmsUsed: ['temporal_clustering'],\r\n          processingDuration: 0,\r\n          correlatedAt: new Date(),\r\n          dataSources: ['enriched_events'],\r\n          rulesApplied: ['temporal_window'],\r\n          performanceMetrics: {\r\n            eventsProcessed: group.length,\r\n            correlationsFound: 1,\r\n            falsePositives: 0,\r\n            processingRate: 1000,\r\n          },\r\n        }\r\n      );\r\n\r\n      correlations.push(correlation);\r\n    }\r\n\r\n    return correlations;\r\n  }\r\n\r\n  /**\r\n   * Perform indicator-based correlation\r\n   */\r\n  private async performIndicatorCorrelation(\r\n    events: EnrichedEvent[],\r\n    config: CorrelationConfig\r\n  ): Promise<CorrelatedEvent[]> {\r\n    const correlations: CorrelatedEvent[] = [];\r\n\r\n    // Group events by common indicators\r\n    const indicatorGroups = this.groupEventsByIndicators(events);\r\n\r\n    for (const [indicators, group] of indicatorGroups) {\r\n      if (group.length < config.minEvents) continue;\r\n\r\n      const [primaryEvent, ...relatedEvents] = group;\r\n      \r\n      const analysis: CorrelationAnalysis = {\r\n        type: CorrelationType.INDICATOR,\r\n        patterns: [{\r\n          type: 'common_indicators',\r\n          description: `${indicators.length} common indicators across ${group.length} events`,\r\n          confidence: this.calculateIndicatorConfidence(indicators, group),\r\n          matchingEvents: group.map(e => e.id.toString()),\r\n          attributes: { indicatorCount: indicators.length },\r\n          strength: Math.min(1.0, indicators.length / 5),\r\n        }],\r\n        commonIndicators: indicators,\r\n      };\r\n\r\n      const correlation = CorrelatedEvent.create(\r\n        primaryEvent,\r\n        relatedEvents,\r\n        analysis,\r\n        {\r\n          engineVersion: '1.0',\r\n          algorithmsUsed: ['indicator_matching'],\r\n          processingDuration: 0,\r\n          correlatedAt: new Date(),\r\n          dataSources: ['threat_intelligence'],\r\n          rulesApplied: ['common_indicators'],\r\n          performanceMetrics: {\r\n            eventsProcessed: group.length,\r\n            correlationsFound: 1,\r\n            falsePositives: 0,\r\n            processingRate: 1000,\r\n          },\r\n        }\r\n      );\r\n\r\n      correlations.push(correlation);\r\n    }\r\n\r\n    return correlations;\r\n  }\r\n\r\n  /**\r\n   * Perform spatial correlation\r\n   */\r\n  private async performSpatialCorrelation(\r\n    events: EnrichedEvent[],\r\n    config: CorrelationConfig\r\n  ): Promise<CorrelatedEvent[]> {\r\n    // Implementation would analyze network proximity, geographic location, etc.\r\n    return [];\r\n  }\r\n\r\n  /**\r\n   * Perform behavioral correlation\r\n   */\r\n  private async performBehavioralCorrelation(\r\n    events: EnrichedEvent[],\r\n    config: CorrelationConfig\r\n  ): Promise<CorrelatedEvent[]> {\r\n    // Implementation would analyze user behavior patterns, anomalies, etc.\r\n    return [];\r\n  }\r\n\r\n  /**\r\n   * Perform campaign correlation\r\n   */\r\n  private async performCampaignCorrelation(\r\n    events: EnrichedEvent[],\r\n    config: CorrelationConfig\r\n  ): Promise<CorrelatedEvent[]> {\r\n    // Implementation would analyze threat actor attribution, TTPs, etc.\r\n    return [];\r\n  }\r\n\r\n  /**\r\n   * Perform attack chain correlation\r\n   */\r\n  private async performAttackChainCorrelation(\r\n    events: EnrichedEvent[],\r\n    config: CorrelationConfig\r\n  ): Promise<CorrelatedEvent[]> {\r\n    // Implementation would analyze MITRE ATT&CK techniques, kill chain phases, etc.\r\n    return [];\r\n  }\r\n\r\n  /**\r\n   * Group events by time windows\r\n   */\r\n  private groupEventsByTime(events: EnrichedEvent[], timeWindow: number): EnrichedEvent[][] {\r\n    const groups: EnrichedEvent[][] = [];\r\n    const sortedEvents = events.sort((a, b) => \r\n      (a.createdAt?.getTime() || 0) - (b.createdAt?.getTime() || 0)\r\n    );\r\n\r\n    let currentGroup: EnrichedEvent[] = [];\r\n    let groupStartTime: number | null = null;\r\n\r\n    for (const event of sortedEvents) {\r\n      const eventTime = event.createdAt?.getTime() || 0;\r\n      \r\n      if (groupStartTime === null || eventTime - groupStartTime <= timeWindow) {\r\n        currentGroup.push(event);\r\n        if (groupStartTime === null) {\r\n          groupStartTime = eventTime;\r\n        }\r\n      } else {\r\n        if (currentGroup.length >= 2) {\r\n          groups.push(currentGroup);\r\n        }\r\n        currentGroup = [event];\r\n        groupStartTime = eventTime;\r\n      }\r\n    }\r\n\r\n    if (currentGroup.length >= 2) {\r\n      groups.push(currentGroup);\r\n    }\r\n\r\n    return groups;\r\n  }\r\n\r\n  /**\r\n   * Group events by common indicators\r\n   */\r\n  private groupEventsByIndicators(events: EnrichedEvent[]): Map<IOC[], EnrichedEvent[]> {\r\n    const groups = new Map<string, EnrichedEvent[]>();\r\n\r\n    for (const event of events) {\r\n      const indicators = event.indicators;\r\n      if (indicators.length === 0) continue;\r\n\r\n      // Create a key from sorted indicator values\r\n      const key = indicators\r\n        .map(ioc => `${ioc.type}:${ioc.value}`)\r\n        .sort()\r\n        .join('|');\r\n\r\n      if (!groups.has(key)) {\r\n        groups.set(key, []);\r\n      }\r\n      groups.get(key)!.push(event);\r\n    }\r\n\r\n    // Convert back to IOC[] keys\r\n    const result = new Map<IOC[], EnrichedEvent[]>();\r\n    for (const [key, eventGroup] of groups) {\r\n      if (eventGroup.length >= 2) {\r\n        const indicators = eventGroup[0].indicators;\r\n        result.set(indicators, eventGroup);\r\n      }\r\n    }\r\n\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * Find common indicators across events\r\n   */\r\n  private findCommonIndicators(events: EnrichedEvent[]): IOC[] {\r\n    if (events.length === 0) return [];\r\n\r\n    const indicatorSets = events.map(event => new Set(\r\n      event.indicators.map(ioc => `${ioc.type}:${ioc.value}`)\r\n    ));\r\n\r\n    const commonIndicatorKeys = indicatorSets.reduce((common, current) => \r\n      new Set([...common].filter(x => current.has(x)))\r\n    );\r\n\r\n    // Convert back to IOC objects\r\n    const firstEvent = events[0];\r\n    return firstEvent.indicators.filter(ioc => \r\n      commonIndicatorKeys.has(`${ioc.type}:${ioc.value}`)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Calculate temporal confidence\r\n   */\r\n  private calculateTemporalConfidence(events: EnrichedEvent[]): number {\r\n    const baseConfidence = Math.min(90, events.length * 15);\r\n    const qualityBonus = events.reduce((sum, e) => sum + e.enrichmentScore, 0) / events.length * 0.1;\r\n    return Math.min(100, baseConfidence + qualityBonus);\r\n  }\r\n\r\n  /**\r\n   * Calculate indicator confidence\r\n   */\r\n  private calculateIndicatorConfidence(indicators: IOC[], events: EnrichedEvent[]): number {\r\n    const baseConfidence = Math.min(95, indicators.length * 20);\r\n    const eventCountBonus = Math.min(20, events.length * 5);\r\n    return Math.min(100, baseConfidence + eventCountBonus);\r\n  }\r\n\r\n  /**\r\n   * Calculate time gaps between events\r\n   */\r\n  private calculateTimeGaps(events: EnrichedEvent[]): number[] {\r\n    const sortedEvents = events.sort((a, b) => \r\n      (a.createdAt?.getTime() || 0) - (b.createdAt?.getTime() || 0)\r\n    );\r\n\r\n    const gaps: number[] = [];\r\n    for (let i = 1; i < sortedEvents.length; i++) {\r\n      const gap = (sortedEvents[i].createdAt?.getTime() || 0) - \r\n                   (sortedEvents[i-1].createdAt?.getTime() || 0);\r\n      gaps.push(gap);\r\n    }\r\n\r\n    return gaps;\r\n  }\r\n\r\n  /**\r\n   * Determine temporal pattern type\r\n   */\r\n  private determineTemporalPattern(events: EnrichedEvent[]): 'burst' | 'periodic' | 'sequential' | 'simultaneous' {\r\n    const timeGaps = this.calculateTimeGaps(events);\r\n    \r\n    if (timeGaps.every(gap => gap < 60000)) { // All within 1 minute\r\n      return 'simultaneous';\r\n    }\r\n    \r\n    if (timeGaps.length <= 2) {\r\n      return 'sequential';\r\n    }\r\n\r\n    const avgGap = timeGaps.reduce((sum, gap) => sum + gap, 0) / timeGaps.length;\r\n    const variance = timeGaps.reduce((sum, gap) => sum + Math.pow(gap - avgGap, 2), 0) / timeGaps.length;\r\n    \r\n    if (variance < avgGap * 0.1) {\r\n      return 'periodic';\r\n    }\r\n    \r\n    return 'burst';\r\n  }\r\n\r\n  /**\r\n   * Calculate temporal regularity\r\n   */\r\n  private calculateTemporalRegularity(events: EnrichedEvent[]): number {\r\n    const timeGaps = this.calculateTimeGaps(events);\r\n    if (timeGaps.length === 0) return 100;\r\n\r\n    const avgGap = timeGaps.reduce((sum, gap) => sum + gap, 0) / timeGaps.length;\r\n    const variance = timeGaps.reduce((sum, gap) => sum + Math.pow(gap - avgGap, 2), 0) / timeGaps.length;\r\n    const stdDev = Math.sqrt(variance);\r\n    \r\n    const regularity = Math.max(0, 100 - (stdDev / avgGap) * 100);\r\n    return Math.min(100, regularity);\r\n  }\r\n\r\n  /**\r\n   * Remove duplicate correlations\r\n   */\r\n  private deduplicateCorrelations(correlations: CorrelatedEvent[]): CorrelatedEvent[] {\r\n    const seen = new Set<string>();\r\n    const unique: CorrelatedEvent[] = [];\r\n\r\n    for (const correlation of correlations) {\r\n      const key = correlation.allEventIds\r\n        .map(id => id.toString())\r\n        .sort()\r\n        .join('|');\r\n\r\n      if (!seen.has(key)) {\r\n        seen.add(key);\r\n        unique.push(correlation);\r\n      }\r\n    }\r\n\r\n    return unique;\r\n  }\r\n\r\n  /**\r\n   * Calculate correlation statistics\r\n   */\r\n  private calculateStatistics(correlations: CorrelatedEvent[]): {\r\n    eventsProcessed: number;\r\n    correlationsFound: number;\r\n    averageConfidence: number;\r\n    typeDistribution: Record<CorrelationType, number>;\r\n  } {\r\n    const typeDistribution = {} as Record<CorrelationType, number>;\r\n    \r\n    for (const type of Object.values(CorrelationType)) {\r\n      typeDistribution[type] = 0;\r\n    }\r\n\r\n    let totalConfidence = 0;\r\n    for (const correlation of correlations) {\r\n      typeDistribution[correlation.correlationType]++;\r\n      totalConfidence += correlation.score;\r\n    }\r\n\r\n    return {\r\n      eventsProcessed: correlations.reduce((sum, c) => sum + c.eventCount, 0),\r\n      correlationsFound: correlations.length,\r\n      averageConfidence: correlations.length > 0 ? totalConfidence / correlations.length : 0,\r\n      typeDistribution,\r\n    };\r\n  }\r\n}\r\n"], "version": 3}