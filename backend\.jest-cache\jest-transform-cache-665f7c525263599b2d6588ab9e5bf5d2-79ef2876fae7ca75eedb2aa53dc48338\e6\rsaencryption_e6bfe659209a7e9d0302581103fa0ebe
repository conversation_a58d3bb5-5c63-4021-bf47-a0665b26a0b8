53c0ea71f086e4daa5e730aca5c58ca8
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RSAEncryption = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const crypto = __importStar(require("crypto"));
let RSAEncryption = class RSAEncryption {
    constructor(configService) {
        this.configService = configService;
        this.keySize = 4096;
        this.defaultPadding = crypto.constants.RSA_PKCS1_OAEP_PADDING;
        this.defaultOaepHash = 'sha256';
    }
    /**
     * Generate RSA key pair
     */
    async generateKeyPair(keySize = this.keySize) {
        return new Promise((resolve, reject) => {
            crypto.generateKeyPair('rsa', {
                modulusLength: keySize,
                publicKeyEncoding: {
                    type: 'spki',
                    format: 'pem',
                },
                privateKeyEncoding: {
                    type: 'pkcs8',
                    format: 'pem',
                },
            }, (err, publicKey, privateKey) => {
                if (err) {
                    reject(new Error(`Failed to generate RSA key pair: ${err.message}`));
                }
                else {
                    resolve({ publicKey, privateKey });
                }
            });
        });
    }
    /**
     * Encrypt data using RSA public key
     */
    async encrypt(data, publicKey, options = {}) {
        try {
            const encrypted = crypto.publicEncrypt({
                key: publicKey,
                padding: options.padding || this.defaultPadding,
                oaepHash: options.oaepHash || this.defaultOaepHash,
            }, Buffer.from(data, 'utf8'));
            return encrypted.toString('base64');
        }
        catch (error) {
            throw new Error(`RSA encryption failed: ${error.message}`);
        }
    }
    /**
     * Decrypt data using RSA private key
     */
    async decrypt(encryptedData, privateKey, options = {}) {
        try {
            const decrypted = crypto.privateDecrypt({
                key: privateKey,
                padding: options.padding || this.defaultPadding,
                oaepHash: options.oaepHash || this.defaultOaepHash,
            }, Buffer.from(encryptedData, 'base64'));
            return decrypted.toString('utf8');
        }
        catch (error) {
            throw new Error(`RSA decryption failed: ${error.message}`);
        }
    }
    /**
     * Sign data using RSA private key
     */
    async sign(data, privateKey, algorithm = 'sha256') {
        try {
            const sign = crypto.createSign(`RSA-${algorithm.toUpperCase()}`);
            sign.update(data);
            sign.end();
            const signature = sign.sign(privateKey, 'base64');
            return signature;
        }
        catch (error) {
            throw new Error(`RSA signing failed: ${error.message}`);
        }
    }
    /**
     * Verify signature using RSA public key
     */
    async verify(data, signature, publicKey, algorithm = 'sha256') {
        try {
            const verify = crypto.createVerify(`RSA-${algorithm.toUpperCase()}`);
            verify.update(data);
            verify.end();
            return verify.verify(publicKey, signature, 'base64');
        }
        catch (error) {
            throw new Error(`RSA verification failed: ${error.message}`);
        }
    }
    /**
     * Extract public key from private key
     */
    extractPublicKey(privateKey) {
        try {
            const keyObject = crypto.createPrivateKey(privateKey);
            const publicKey = crypto.createPublicKey(keyObject);
            return publicKey.export({
                type: 'spki',
                format: 'pem',
            });
        }
        catch (error) {
            throw new Error(`Failed to extract public key: ${error.message}`);
        }
    }
    /**
     * Validate RSA key format
     */
    validatePrivateKey(privateKey) {
        try {
            crypto.createPrivateKey(privateKey);
            return true;
        }
        catch {
            return false;
        }
    }
    /**
     * Validate RSA public key format
     */
    validatePublicKey(publicKey) {
        try {
            crypto.createPublicKey(publicKey);
            return true;
        }
        catch {
            return false;
        }
    }
    /**
     * Get key size from RSA key
     */
    getKeySize(key) {
        try {
            const keyObject = crypto.createPublicKey(key);
            return keyObject.asymmetricKeySize * 8; // Convert bytes to bits
        }
        catch {
            try {
                const keyObject = crypto.createPrivateKey(key);
                return keyObject.asymmetricKeySize * 8; // Convert bytes to bits
            }
            catch {
                throw new Error('Invalid RSA key format');
            }
        }
    }
    /**
     * Encrypt large data by chunking (RSA has size limitations)
     */
    async encryptLargeData(data, publicKey) {
        const keySize = this.getKeySize(publicKey);
        const maxChunkSize = Math.floor(keySize / 8) - 42; // Account for OAEP padding
        const chunks = [];
        for (let i = 0; i < data.length; i += maxChunkSize) {
            const chunk = data.slice(i, i + maxChunkSize);
            const encryptedChunk = await this.encrypt(chunk, publicKey);
            chunks.push(encryptedChunk);
        }
        return chunks;
    }
    /**
     * Decrypt large data from chunks
     */
    async decryptLargeData(encryptedChunks, privateKey) {
        const decryptedChunks = [];
        for (const chunk of encryptedChunks) {
            const decryptedChunk = await this.decrypt(chunk, privateKey);
            decryptedChunks.push(decryptedChunk);
        }
        return decryptedChunks.join('');
    }
};
exports.RSAEncryption = RSAEncryption;
exports.RSAEncryption = RSAEncryption = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], RSAEncryption);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxpbmZyYXN0cnVjdHVyZVxcc2VjdXJpdHlcXGVuY3J5cHRpb25cXHJzYS5lbmNyeXB0aW9uLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSwyQ0FBNEM7QUFDNUMsMkNBQStDO0FBQy9DLCtDQUFpQztBQWMxQixJQUFNLGFBQWEsR0FBbkIsTUFBTSxhQUFhO0lBS3hCLFlBQTZCLGFBQTRCO1FBQTVCLGtCQUFhLEdBQWIsYUFBYSxDQUFlO1FBSnhDLFlBQU8sR0FBRyxJQUFJLENBQUM7UUFDZixtQkFBYyxHQUFHLE1BQU0sQ0FBQyxTQUFTLENBQUMsc0JBQXNCLENBQUM7UUFDekQsb0JBQWUsR0FBRyxRQUFRLENBQUM7SUFFZ0IsQ0FBQztJQUU3RDs7T0FFRztJQUNILEtBQUssQ0FBQyxlQUFlLENBQUMsVUFBa0IsSUFBSSxDQUFDLE9BQU87UUFDbEQsT0FBTyxJQUFJLE9BQU8sQ0FBQyxDQUFDLE9BQU8sRUFBRSxNQUFNLEVBQUUsRUFBRTtZQUNyQyxNQUFNLENBQUMsZUFBZSxDQUNwQixLQUFLLEVBQ0w7Z0JBQ0UsYUFBYSxFQUFFLE9BQU87Z0JBQ3RCLGlCQUFpQixFQUFFO29CQUNqQixJQUFJLEVBQUUsTUFBTTtvQkFDWixNQUFNLEVBQUUsS0FBSztpQkFDZDtnQkFDRCxrQkFBa0IsRUFBRTtvQkFDbEIsSUFBSSxFQUFFLE9BQU87b0JBQ2IsTUFBTSxFQUFFLEtBQUs7aUJBQ2Q7YUFDRixFQUNELENBQUMsR0FBRyxFQUFFLFNBQVMsRUFBRSxVQUFVLEVBQUUsRUFBRTtnQkFDN0IsSUFBSSxHQUFHLEVBQUUsQ0FBQztvQkFDUixNQUFNLENBQUMsSUFBSSxLQUFLLENBQUMsb0NBQW9DLEdBQUcsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxDQUFDLENBQUM7Z0JBQ3ZFLENBQUM7cUJBQU0sQ0FBQztvQkFDTixPQUFPLENBQUMsRUFBRSxTQUFTLEVBQUUsVUFBVSxFQUFFLENBQUMsQ0FBQztnQkFDckMsQ0FBQztZQUNILENBQUMsQ0FDRixDQUFDO1FBQ0osQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxLQUFLLENBQUMsT0FBTyxDQUNYLElBQVksRUFDWixTQUFpQixFQUNqQixVQUFnQyxFQUFFO1FBRWxDLElBQUksQ0FBQztZQUNILE1BQU0sU0FBUyxHQUFHLE1BQU0sQ0FBQyxhQUFhLENBQ3BDO2dCQUNFLEdBQUcsRUFBRSxTQUFTO2dCQUNkLE9BQU8sRUFBRSxPQUFPLENBQUMsT0FBTyxJQUFJLElBQUksQ0FBQyxjQUFjO2dCQUMvQyxRQUFRLEVBQUUsT0FBTyxDQUFDLFFBQVEsSUFBSSxJQUFJLENBQUMsZUFBZTthQUNuRCxFQUNELE1BQU0sQ0FBQyxJQUFJLENBQUMsSUFBSSxFQUFFLE1BQU0sQ0FBQyxDQUMxQixDQUFDO1lBRUYsT0FBTyxTQUFTLENBQUMsUUFBUSxDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBQ3RDLENBQUM7UUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1lBQ2YsTUFBTSxJQUFJLEtBQUssQ0FBQywwQkFBMEIsS0FBSyxDQUFDLE9BQU8sRUFBRSxDQUFDLENBQUM7UUFDN0QsQ0FBQztJQUNILENBQUM7SUFFRDs7T0FFRztJQUNILEtBQUssQ0FBQyxPQUFPLENBQ1gsYUFBcUIsRUFDckIsVUFBa0IsRUFDbEIsVUFBZ0MsRUFBRTtRQUVsQyxJQUFJLENBQUM7WUFDSCxNQUFNLFNBQVMsR0FBRyxNQUFNLENBQUMsY0FBYyxDQUNyQztnQkFDRSxHQUFHLEVBQUUsVUFBVTtnQkFDZixPQUFPLEVBQUUsT0FBTyxDQUFDLE9BQU8sSUFBSSxJQUFJLENBQUMsY0FBYztnQkFDL0MsUUFBUSxFQUFFLE9BQU8sQ0FBQyxRQUFRLElBQUksSUFBSSxDQUFDLGVBQWU7YUFDbkQsRUFDRCxNQUFNLENBQUMsSUFBSSxDQUFDLGFBQWEsRUFBRSxRQUFRLENBQUMsQ0FDckMsQ0FBQztZQUVGLE9BQU8sU0FBUyxDQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUMsQ0FBQztRQUNwQyxDQUFDO1FBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztZQUNmLE1BQU0sSUFBSSxLQUFLLENBQUMsMEJBQTBCLEtBQUssQ0FBQyxPQUFPLEVBQUUsQ0FBQyxDQUFDO1FBQzdELENBQUM7SUFDSCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxLQUFLLENBQUMsSUFBSSxDQUFDLElBQVksRUFBRSxVQUFrQixFQUFFLFlBQW9CLFFBQVE7UUFDdkUsSUFBSSxDQUFDO1lBQ0gsTUFBTSxJQUFJLEdBQUcsTUFBTSxDQUFDLFVBQVUsQ0FBQyxPQUFPLFNBQVMsQ0FBQyxXQUFXLEVBQUUsRUFBRSxDQUFDLENBQUM7WUFDakUsSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUNsQixJQUFJLENBQUMsR0FBRyxFQUFFLENBQUM7WUFFWCxNQUFNLFNBQVMsR0FBRyxJQUFJLENBQUMsSUFBSSxDQUFDLFVBQVUsRUFBRSxRQUFRLENBQUMsQ0FBQztZQUNsRCxPQUFPLFNBQVMsQ0FBQztRQUNuQixDQUFDO1FBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztZQUNmLE1BQU0sSUFBSSxLQUFLLENBQUMsdUJBQXVCLEtBQUssQ0FBQyxPQUFPLEVBQUUsQ0FBQyxDQUFDO1FBQzFELENBQUM7SUFDSCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxLQUFLLENBQUMsTUFBTSxDQUNWLElBQVksRUFDWixTQUFpQixFQUNqQixTQUFpQixFQUNqQixZQUFvQixRQUFRO1FBRTVCLElBQUksQ0FBQztZQUNILE1BQU0sTUFBTSxHQUFHLE1BQU0sQ0FBQyxZQUFZLENBQUMsT0FBTyxTQUFTLENBQUMsV0FBVyxFQUFFLEVBQUUsQ0FBQyxDQUFDO1lBQ3JFLE1BQU0sQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDcEIsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDO1lBRWIsT0FBTyxNQUFNLENBQUMsTUFBTSxDQUFDLFNBQVMsRUFBRSxTQUFTLEVBQUUsUUFBUSxDQUFDLENBQUM7UUFDdkQsQ0FBQztRQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7WUFDZixNQUFNLElBQUksS0FBSyxDQUFDLDRCQUE0QixLQUFLLENBQUMsT0FBTyxFQUFFLENBQUMsQ0FBQztRQUMvRCxDQUFDO0lBQ0gsQ0FBQztJQUVEOztPQUVHO0lBQ0gsZ0JBQWdCLENBQUMsVUFBa0I7UUFDakMsSUFBSSxDQUFDO1lBQ0gsTUFBTSxTQUFTLEdBQUcsTUFBTSxDQUFDLGdCQUFnQixDQUFDLFVBQVUsQ0FBQyxDQUFDO1lBQ3RELE1BQU0sU0FBUyxHQUFHLE1BQU0sQ0FBQyxlQUFlLENBQUMsU0FBUyxDQUFDLENBQUM7WUFFcEQsT0FBTyxTQUFTLENBQUMsTUFBTSxDQUFDO2dCQUN0QixJQUFJLEVBQUUsTUFBTTtnQkFDWixNQUFNLEVBQUUsS0FBSzthQUNkLENBQVcsQ0FBQztRQUNmLENBQUM7UUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1lBQ2YsTUFBTSxJQUFJLEtBQUssQ0FBQyxpQ0FBaUMsS0FBSyxDQUFDLE9BQU8sRUFBRSxDQUFDLENBQUM7UUFDcEUsQ0FBQztJQUNILENBQUM7SUFFRDs7T0FFRztJQUNILGtCQUFrQixDQUFDLFVBQWtCO1FBQ25DLElBQUksQ0FBQztZQUNILE1BQU0sQ0FBQyxnQkFBZ0IsQ0FBQyxVQUFVLENBQUMsQ0FBQztZQUNwQyxPQUFPLElBQUksQ0FBQztRQUNkLENBQUM7UUFBQyxNQUFNLENBQUM7WUFDUCxPQUFPLEtBQUssQ0FBQztRQUNmLENBQUM7SUFDSCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxpQkFBaUIsQ0FBQyxTQUFpQjtRQUNqQyxJQUFJLENBQUM7WUFDSCxNQUFNLENBQUMsZUFBZSxDQUFDLFNBQVMsQ0FBQyxDQUFDO1lBQ2xDLE9BQU8sSUFBSSxDQUFDO1FBQ2QsQ0FBQztRQUFDLE1BQU0sQ0FBQztZQUNQLE9BQU8sS0FBSyxDQUFDO1FBQ2YsQ0FBQztJQUNILENBQUM7SUFFRDs7T0FFRztJQUNILFVBQVUsQ0FBQyxHQUFXO1FBQ3BCLElBQUksQ0FBQztZQUNILE1BQU0sU0FBUyxHQUFHLE1BQU0sQ0FBQyxlQUFlLENBQUMsR0FBRyxDQUFDLENBQUM7WUFDOUMsT0FBTyxTQUFTLENBQUMsaUJBQWtCLEdBQUcsQ0FBQyxDQUFDLENBQUMsd0JBQXdCO1FBQ25FLENBQUM7UUFBQyxNQUFNLENBQUM7WUFDUCxJQUFJLENBQUM7Z0JBQ0gsTUFBTSxTQUFTLEdBQUcsTUFBTSxDQUFDLGdCQUFnQixDQUFDLEdBQUcsQ0FBQyxDQUFDO2dCQUMvQyxPQUFPLFNBQVMsQ0FBQyxpQkFBa0IsR0FBRyxDQUFDLENBQUMsQ0FBQyx3QkFBd0I7WUFDbkUsQ0FBQztZQUFDLE1BQU0sQ0FBQztnQkFDUCxNQUFNLElBQUksS0FBSyxDQUFDLHdCQUF3QixDQUFDLENBQUM7WUFDNUMsQ0FBQztRQUNILENBQUM7SUFDSCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxLQUFLLENBQUMsZ0JBQWdCLENBQUMsSUFBWSxFQUFFLFNBQWlCO1FBQ3BELE1BQU0sT0FBTyxHQUFHLElBQUksQ0FBQyxVQUFVLENBQUMsU0FBUyxDQUFDLENBQUM7UUFDM0MsTUFBTSxZQUFZLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLEdBQUcsQ0FBQyxDQUFDLEdBQUcsRUFBRSxDQUFDLENBQUMsMkJBQTJCO1FBRTlFLE1BQU0sTUFBTSxHQUFhLEVBQUUsQ0FBQztRQUM1QixLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsSUFBSSxDQUFDLE1BQU0sRUFBRSxDQUFDLElBQUksWUFBWSxFQUFFLENBQUM7WUFDbkQsTUFBTSxLQUFLLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLEVBQUUsQ0FBQyxHQUFHLFlBQVksQ0FBQyxDQUFDO1lBQzlDLE1BQU0sY0FBYyxHQUFHLE1BQU0sSUFBSSxDQUFDLE9BQU8sQ0FBQyxLQUFLLEVBQUUsU0FBUyxDQUFDLENBQUM7WUFDNUQsTUFBTSxDQUFDLElBQUksQ0FBQyxjQUFjLENBQUMsQ0FBQztRQUM5QixDQUFDO1FBRUQsT0FBTyxNQUFNLENBQUM7SUFDaEIsQ0FBQztJQUVEOztPQUVHO0lBQ0gsS0FBSyxDQUFDLGdCQUFnQixDQUFDLGVBQXlCLEVBQUUsVUFBa0I7UUFDbEUsTUFBTSxlQUFlLEdBQWEsRUFBRSxDQUFDO1FBRXJDLEtBQUssTUFBTSxLQUFLLElBQUksZUFBZSxFQUFFLENBQUM7WUFDcEMsTUFBTSxjQUFjLEdBQUcsTUFBTSxJQUFJLENBQUMsT0FBTyxDQUFDLEtBQUssRUFBRSxVQUFVLENBQUMsQ0FBQztZQUM3RCxlQUFlLENBQUMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxDQUFDO1FBQ3ZDLENBQUM7UUFFRCxPQUFPLGVBQWUsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUM7SUFDbEMsQ0FBQztDQUNGLENBQUE7QUFoTlksc0NBQWE7d0JBQWIsYUFBYTtJQUR6QixJQUFBLG1CQUFVLEdBQUU7eURBTWlDLHNCQUFhLG9CQUFiLHNCQUFhO0dBTDlDLGFBQWEsQ0FnTnpCIiwibmFtZXMiOltdLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTHVrYVxcc2VudGluZWxcXGJhY2tlbmRcXHNyY1xcaW5mcmFzdHJ1Y3R1cmVcXHNlY3VyaXR5XFxlbmNyeXB0aW9uXFxyc2EuZW5jcnlwdGlvbi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBJbmplY3RhYmxlIH0gZnJvbSAnQG5lc3Rqcy9jb21tb24nO1xyXG5pbXBvcnQgeyBDb25maWdTZXJ2aWNlIH0gZnJvbSAnQG5lc3Rqcy9jb25maWcnO1xyXG5pbXBvcnQgKiBhcyBjcnlwdG8gZnJvbSAnY3J5cHRvJztcclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgUlNBS2V5UGFpciB7XHJcbiAgcHVibGljS2V5OiBzdHJpbmc7XHJcbiAgcHJpdmF0ZUtleTogc3RyaW5nO1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIFJTQUVuY3J5cHRpb25PcHRpb25zIHtcclxuICBwYWRkaW5nPzogbnVtYmVyO1xyXG4gIG9hZXBIYXNoPzogc3RyaW5nO1xyXG4gIG1nZj86IG51bWJlcjtcclxufVxyXG5cclxuQEluamVjdGFibGUoKVxyXG5leHBvcnQgY2xhc3MgUlNBRW5jcnlwdGlvbiB7XHJcbiAgcHJpdmF0ZSByZWFkb25seSBrZXlTaXplID0gNDA5NjtcclxuICBwcml2YXRlIHJlYWRvbmx5IGRlZmF1bHRQYWRkaW5nID0gY3J5cHRvLmNvbnN0YW50cy5SU0FfUEtDUzFfT0FFUF9QQURESU5HO1xyXG4gIHByaXZhdGUgcmVhZG9ubHkgZGVmYXVsdE9hZXBIYXNoID0gJ3NoYTI1Nic7XHJcblxyXG4gIGNvbnN0cnVjdG9yKHByaXZhdGUgcmVhZG9ubHkgY29uZmlnU2VydmljZTogQ29uZmlnU2VydmljZSkge31cclxuXHJcbiAgLyoqXHJcbiAgICogR2VuZXJhdGUgUlNBIGtleSBwYWlyXHJcbiAgICovXHJcbiAgYXN5bmMgZ2VuZXJhdGVLZXlQYWlyKGtleVNpemU6IG51bWJlciA9IHRoaXMua2V5U2l6ZSk6IFByb21pc2U8UlNBS2V5UGFpcj4ge1xyXG4gICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHtcclxuICAgICAgY3J5cHRvLmdlbmVyYXRlS2V5UGFpcihcclxuICAgICAgICAncnNhJyxcclxuICAgICAgICB7XHJcbiAgICAgICAgICBtb2R1bHVzTGVuZ3RoOiBrZXlTaXplLFxyXG4gICAgICAgICAgcHVibGljS2V5RW5jb2Rpbmc6IHtcclxuICAgICAgICAgICAgdHlwZTogJ3Nwa2knLFxyXG4gICAgICAgICAgICBmb3JtYXQ6ICdwZW0nLFxyXG4gICAgICAgICAgfSxcclxuICAgICAgICAgIHByaXZhdGVLZXlFbmNvZGluZzoge1xyXG4gICAgICAgICAgICB0eXBlOiAncGtjczgnLFxyXG4gICAgICAgICAgICBmb3JtYXQ6ICdwZW0nLFxyXG4gICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIChlcnIsIHB1YmxpY0tleSwgcHJpdmF0ZUtleSkgPT4ge1xyXG4gICAgICAgICAgaWYgKGVycikge1xyXG4gICAgICAgICAgICByZWplY3QobmV3IEVycm9yKGBGYWlsZWQgdG8gZ2VuZXJhdGUgUlNBIGtleSBwYWlyOiAke2Vyci5tZXNzYWdlfWApKTtcclxuICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIHJlc29sdmUoeyBwdWJsaWNLZXksIHByaXZhdGVLZXkgfSk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSxcclxuICAgICAgKTtcclxuICAgIH0pO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogRW5jcnlwdCBkYXRhIHVzaW5nIFJTQSBwdWJsaWMga2V5XHJcbiAgICovXHJcbiAgYXN5bmMgZW5jcnlwdChcclxuICAgIGRhdGE6IHN0cmluZywgXHJcbiAgICBwdWJsaWNLZXk6IHN0cmluZywgXHJcbiAgICBvcHRpb25zOiBSU0FFbmNyeXB0aW9uT3B0aW9ucyA9IHt9XHJcbiAgKTogUHJvbWlzZTxzdHJpbmc+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IGVuY3J5cHRlZCA9IGNyeXB0by5wdWJsaWNFbmNyeXB0KFxyXG4gICAgICAgIHtcclxuICAgICAgICAgIGtleTogcHVibGljS2V5LFxyXG4gICAgICAgICAgcGFkZGluZzogb3B0aW9ucy5wYWRkaW5nIHx8IHRoaXMuZGVmYXVsdFBhZGRpbmcsXHJcbiAgICAgICAgICBvYWVwSGFzaDogb3B0aW9ucy5vYWVwSGFzaCB8fCB0aGlzLmRlZmF1bHRPYWVwSGFzaCxcclxuICAgICAgICB9LFxyXG4gICAgICAgIEJ1ZmZlci5mcm9tKGRhdGEsICd1dGY4JyksXHJcbiAgICAgICk7XHJcblxyXG4gICAgICByZXR1cm4gZW5jcnlwdGVkLnRvU3RyaW5nKCdiYXNlNjQnKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcihgUlNBIGVuY3J5cHRpb24gZmFpbGVkOiAke2Vycm9yLm1lc3NhZ2V9YCk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBEZWNyeXB0IGRhdGEgdXNpbmcgUlNBIHByaXZhdGUga2V5XHJcbiAgICovXHJcbiAgYXN5bmMgZGVjcnlwdChcclxuICAgIGVuY3J5cHRlZERhdGE6IHN0cmluZywgXHJcbiAgICBwcml2YXRlS2V5OiBzdHJpbmcsIFxyXG4gICAgb3B0aW9uczogUlNBRW5jcnlwdGlvbk9wdGlvbnMgPSB7fVxyXG4gICk6IFByb21pc2U8c3RyaW5nPiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCBkZWNyeXB0ZWQgPSBjcnlwdG8ucHJpdmF0ZURlY3J5cHQoXHJcbiAgICAgICAge1xyXG4gICAgICAgICAga2V5OiBwcml2YXRlS2V5LFxyXG4gICAgICAgICAgcGFkZGluZzogb3B0aW9ucy5wYWRkaW5nIHx8IHRoaXMuZGVmYXVsdFBhZGRpbmcsXHJcbiAgICAgICAgICBvYWVwSGFzaDogb3B0aW9ucy5vYWVwSGFzaCB8fCB0aGlzLmRlZmF1bHRPYWVwSGFzaCxcclxuICAgICAgICB9LFxyXG4gICAgICAgIEJ1ZmZlci5mcm9tKGVuY3J5cHRlZERhdGEsICdiYXNlNjQnKSxcclxuICAgICAgKTtcclxuXHJcbiAgICAgIHJldHVybiBkZWNyeXB0ZWQudG9TdHJpbmcoJ3V0ZjgnKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcihgUlNBIGRlY3J5cHRpb24gZmFpbGVkOiAke2Vycm9yLm1lc3NhZ2V9YCk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBTaWduIGRhdGEgdXNpbmcgUlNBIHByaXZhdGUga2V5XHJcbiAgICovXHJcbiAgYXN5bmMgc2lnbihkYXRhOiBzdHJpbmcsIHByaXZhdGVLZXk6IHN0cmluZywgYWxnb3JpdGhtOiBzdHJpbmcgPSAnc2hhMjU2Jyk6IFByb21pc2U8c3RyaW5nPiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCBzaWduID0gY3J5cHRvLmNyZWF0ZVNpZ24oYFJTQS0ke2FsZ29yaXRobS50b1VwcGVyQ2FzZSgpfWApO1xyXG4gICAgICBzaWduLnVwZGF0ZShkYXRhKTtcclxuICAgICAgc2lnbi5lbmQoKTtcclxuXHJcbiAgICAgIGNvbnN0IHNpZ25hdHVyZSA9IHNpZ24uc2lnbihwcml2YXRlS2V5LCAnYmFzZTY0Jyk7XHJcbiAgICAgIHJldHVybiBzaWduYXR1cmU7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYFJTQSBzaWduaW5nIGZhaWxlZDogJHtlcnJvci5tZXNzYWdlfWApO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogVmVyaWZ5IHNpZ25hdHVyZSB1c2luZyBSU0EgcHVibGljIGtleVxyXG4gICAqL1xyXG4gIGFzeW5jIHZlcmlmeShcclxuICAgIGRhdGE6IHN0cmluZywgXHJcbiAgICBzaWduYXR1cmU6IHN0cmluZywgXHJcbiAgICBwdWJsaWNLZXk6IHN0cmluZywgXHJcbiAgICBhbGdvcml0aG06IHN0cmluZyA9ICdzaGEyNTYnXHJcbiAgKTogUHJvbWlzZTxib29sZWFuPiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCB2ZXJpZnkgPSBjcnlwdG8uY3JlYXRlVmVyaWZ5KGBSU0EtJHthbGdvcml0aG0udG9VcHBlckNhc2UoKX1gKTtcclxuICAgICAgdmVyaWZ5LnVwZGF0ZShkYXRhKTtcclxuICAgICAgdmVyaWZ5LmVuZCgpO1xyXG5cclxuICAgICAgcmV0dXJuIHZlcmlmeS52ZXJpZnkocHVibGljS2V5LCBzaWduYXR1cmUsICdiYXNlNjQnKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcihgUlNBIHZlcmlmaWNhdGlvbiBmYWlsZWQ6ICR7ZXJyb3IubWVzc2FnZX1gKTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEV4dHJhY3QgcHVibGljIGtleSBmcm9tIHByaXZhdGUga2V5XHJcbiAgICovXHJcbiAgZXh0cmFjdFB1YmxpY0tleShwcml2YXRlS2V5OiBzdHJpbmcpOiBzdHJpbmcge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3Qga2V5T2JqZWN0ID0gY3J5cHRvLmNyZWF0ZVByaXZhdGVLZXkocHJpdmF0ZUtleSk7XHJcbiAgICAgIGNvbnN0IHB1YmxpY0tleSA9IGNyeXB0by5jcmVhdGVQdWJsaWNLZXkoa2V5T2JqZWN0KTtcclxuICAgICAgXHJcbiAgICAgIHJldHVybiBwdWJsaWNLZXkuZXhwb3J0KHtcclxuICAgICAgICB0eXBlOiAnc3BraScsXHJcbiAgICAgICAgZm9ybWF0OiAncGVtJyxcclxuICAgICAgfSkgYXMgc3RyaW5nO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZXh0cmFjdCBwdWJsaWMga2V5OiAke2Vycm9yLm1lc3NhZ2V9YCk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBWYWxpZGF0ZSBSU0Ega2V5IGZvcm1hdFxyXG4gICAqL1xyXG4gIHZhbGlkYXRlUHJpdmF0ZUtleShwcml2YXRlS2V5OiBzdHJpbmcpOiBib29sZWFuIHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNyeXB0by5jcmVhdGVQcml2YXRlS2V5KHByaXZhdGVLZXkpO1xyXG4gICAgICByZXR1cm4gdHJ1ZTtcclxuICAgIH0gY2F0Y2gge1xyXG4gICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBWYWxpZGF0ZSBSU0EgcHVibGljIGtleSBmb3JtYXRcclxuICAgKi9cclxuICB2YWxpZGF0ZVB1YmxpY0tleShwdWJsaWNLZXk6IHN0cmluZyk6IGJvb2xlYW4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY3J5cHRvLmNyZWF0ZVB1YmxpY0tleShwdWJsaWNLZXkpO1xyXG4gICAgICByZXR1cm4gdHJ1ZTtcclxuICAgIH0gY2F0Y2gge1xyXG4gICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQga2V5IHNpemUgZnJvbSBSU0Ega2V5XHJcbiAgICovXHJcbiAgZ2V0S2V5U2l6ZShrZXk6IHN0cmluZyk6IG51bWJlciB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCBrZXlPYmplY3QgPSBjcnlwdG8uY3JlYXRlUHVibGljS2V5KGtleSk7XHJcbiAgICAgIHJldHVybiBrZXlPYmplY3QuYXN5bW1ldHJpY0tleVNpemUhICogODsgLy8gQ29udmVydCBieXRlcyB0byBiaXRzXHJcbiAgICB9IGNhdGNoIHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zdCBrZXlPYmplY3QgPSBjcnlwdG8uY3JlYXRlUHJpdmF0ZUtleShrZXkpO1xyXG4gICAgICAgIHJldHVybiBrZXlPYmplY3QuYXN5bW1ldHJpY0tleVNpemUhICogODsgLy8gQ29udmVydCBieXRlcyB0byBiaXRzXHJcbiAgICAgIH0gY2F0Y2gge1xyXG4gICAgICAgIHRocm93IG5ldyBFcnJvcignSW52YWxpZCBSU0Ega2V5IGZvcm1hdCcpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBFbmNyeXB0IGxhcmdlIGRhdGEgYnkgY2h1bmtpbmcgKFJTQSBoYXMgc2l6ZSBsaW1pdGF0aW9ucylcclxuICAgKi9cclxuICBhc3luYyBlbmNyeXB0TGFyZ2VEYXRhKGRhdGE6IHN0cmluZywgcHVibGljS2V5OiBzdHJpbmcpOiBQcm9taXNlPHN0cmluZ1tdPiB7XHJcbiAgICBjb25zdCBrZXlTaXplID0gdGhpcy5nZXRLZXlTaXplKHB1YmxpY0tleSk7XHJcbiAgICBjb25zdCBtYXhDaHVua1NpemUgPSBNYXRoLmZsb29yKGtleVNpemUgLyA4KSAtIDQyOyAvLyBBY2NvdW50IGZvciBPQUVQIHBhZGRpbmdcclxuICAgIFxyXG4gICAgY29uc3QgY2h1bmtzOiBzdHJpbmdbXSA9IFtdO1xyXG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBkYXRhLmxlbmd0aDsgaSArPSBtYXhDaHVua1NpemUpIHtcclxuICAgICAgY29uc3QgY2h1bmsgPSBkYXRhLnNsaWNlKGksIGkgKyBtYXhDaHVua1NpemUpO1xyXG4gICAgICBjb25zdCBlbmNyeXB0ZWRDaHVuayA9IGF3YWl0IHRoaXMuZW5jcnlwdChjaHVuaywgcHVibGljS2V5KTtcclxuICAgICAgY2h1bmtzLnB1c2goZW5jcnlwdGVkQ2h1bmspO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICByZXR1cm4gY2h1bmtzO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogRGVjcnlwdCBsYXJnZSBkYXRhIGZyb20gY2h1bmtzXHJcbiAgICovXHJcbiAgYXN5bmMgZGVjcnlwdExhcmdlRGF0YShlbmNyeXB0ZWRDaHVua3M6IHN0cmluZ1tdLCBwcml2YXRlS2V5OiBzdHJpbmcpOiBQcm9taXNlPHN0cmluZz4ge1xyXG4gICAgY29uc3QgZGVjcnlwdGVkQ2h1bmtzOiBzdHJpbmdbXSA9IFtdO1xyXG4gICAgXHJcbiAgICBmb3IgKGNvbnN0IGNodW5rIG9mIGVuY3J5cHRlZENodW5rcykge1xyXG4gICAgICBjb25zdCBkZWNyeXB0ZWRDaHVuayA9IGF3YWl0IHRoaXMuZGVjcnlwdChjaHVuaywgcHJpdmF0ZUtleSk7XHJcbiAgICAgIGRlY3J5cHRlZENodW5rcy5wdXNoKGRlY3J5cHRlZENodW5rKTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgcmV0dXJuIGRlY3J5cHRlZENodW5rcy5qb2luKCcnKTtcclxuICB9XHJcbn0iXSwidmVyc2lvbiI6M30=