{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\__tests__\\vulnerability-feed.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,6CAAqD;AAErD,8EAAyE;AACzE,kGAAuF;AACvF,wFAA8E;AAC9E,yFAAqF;AACrF,6FAAyF;AACzF,oIAA8H;AAE9H,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;IACxC,IAAI,OAAiC,CAAC;IACtC,IAAI,cAA0D,CAAC;IAC/D,IAAI,uBAA+D,CAAC;IACpE,IAAI,aAAyC,CAAC;IAC9C,IAAI,YAAuC,CAAC;IAC5C,IAAI,sBAAwE,CAAC;IAE7E,MAAM,QAAQ,GAAG;QACf,EAAE,EAAE,sCAAsC;QAC1C,IAAI,EAAE,WAAW;QACjB,WAAW,EAAE,uBAAuB;QACpC,QAAQ,EAAE,KAAK;QACf,QAAQ,EAAE,MAAM;QAChB,GAAG,EAAE,kDAAkD;QACvD,MAAM,EAAE,MAAM;QACd,UAAU,EAAE;YACV,YAAY,EAAE,IAAI;YAClB,SAAS,EAAE,IAAI;SAChB;QACD,QAAQ,EAAE,MAAM;QAChB,MAAM,EAAE,QAAQ;QAChB,QAAQ,EAAE,IAAI;QACd,UAAU,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;QAC5C,UAAU,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;QAC5C,SAAS,EAAE;YACT,UAAU,EAAE,EAAE;YACd,eAAe,EAAE,CAAC;YAClB,WAAW,EAAE,CAAC;YACd,gBAAgB,EAAE,GAAG;SACtB;QACD,cAAc,EAAE;YACd,iBAAiB,EAAE,EAAE;YACrB,aAAa,EAAE,EAAE;YACjB,SAAS,EAAE,CAAC;SACb;QACD,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;QACrB,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC7B,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;QACnB,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;QACrB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE;QAC1B,oBAAoB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC/B,SAAS,EAAE,IAAI;QACf,eAAe,EAAE,EAAE;QACnB,kBAAkB,EAAE,CAAC;QACrB,SAAS,EAAE,KAAK;QAChB,YAAY,EAAE,IAAI;KACnB,CAAC;IAEF,MAAM,iBAAiB,GAAG;QACxB,EAAE,EAAE,UAAU;QACd,UAAU,EAAE,eAAe;QAC3B,KAAK,EAAE,oBAAoB;QAC3B,QAAQ,EAAE,MAAM;KACjB,CAAC;IAEF,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,cAAc,GAAG;YACrB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;YACjB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;YACf,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;YACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;YAClB,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;YACpB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;YAChB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;YACjB,kBAAkB,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;gBACjC,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBAC7C,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACjC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACpC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACnC,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACtC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBAChC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBAChC,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE;gBAC1B,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;aACnB,CAAC,CAAC;SACJ,CAAC;QAEF,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,qDAAwB;gBACxB;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,6CAAiB,CAAC;oBAC9C,QAAQ,EAAE,cAAc;iBACzB;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,oCAAa,CAAC;oBAC1C,QAAQ,EAAE,cAAc;iBACzB;gBACD;oBACE,OAAO,EAAE,8BAAa;oBACtB,QAAQ,EAAE;wBACR,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;wBAChB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;wBACd,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;wBACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;qBACjB;iBACF;gBACD;oBACE,OAAO,EAAE,4BAAY;oBACrB,QAAQ,EAAE;wBACR,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;qBACzB;iBACF;gBACD;oBACE,OAAO,EAAE,4EAAmC;oBAC5C,QAAQ,EAAE;wBACR,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE;wBACvB,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;wBACzB,qBAAqB,EAAE,IAAI,CAAC,EAAE,EAAE;wBAChC,uBAAuB,EAAE,IAAI,CAAC,EAAE,EAAE;wBAClC,oBAAoB,EAAE,IAAI,CAAC,EAAE,EAAE;wBAC/B,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE;qBAC5B;iBACF;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAA2B,qDAAwB,CAAC,CAAC;QACzE,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC,IAAA,4BAAkB,EAAC,6CAAiB,CAAC,CAAC,CAAC;QACnE,uBAAuB,GAAG,MAAM,CAAC,GAAG,CAAC,IAAA,4BAAkB,EAAC,oCAAa,CAAC,CAAC,CAAC;QACxE,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,8BAAa,CAAC,CAAC;QAC1C,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,4BAAY,CAAC,CAAC;QACxC,sBAAsB,GAAG,MAAM,CAAC,GAAG,CAAC,4EAAmC,CAAC,CAAC;IAC3E,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mBAAmB,EAAE,GAAG,EAAE;QAC3B,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,0BAA0B,EAAE,KAAK,IAAI,EAAE;YACxC,MAAM,QAAQ,GAAG;gBACf,IAAI,EAAE,UAAU;gBAChB,WAAW,EAAE,sBAAsB;gBACnC,QAAQ,EAAE,KAAc;gBACxB,QAAQ,EAAE,MAAM;gBAChB,GAAG,EAAE,kDAAkD;gBACvD,MAAM,EAAE,MAAe;gBACvB,UAAU,EAAE;oBACV,YAAY,EAAE,IAAI;oBAClB,SAAS,EAAE,IAAI;iBAChB;aACF,CAAC;YAEF,cAAc,CAAC,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YAChD,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAEhD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAE9D,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAChD,MAAM,CAAC,gBAAgB,CAAC;gBACtB,GAAG,QAAQ;gBACX,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,KAAK;aAChB,CAAC,CACH,CAAC;YACF,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAC3D,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACrD,UAAU,EACV,QAAQ,EACR,oBAAoB,EACpB,QAAQ,CAAC,EAAE,EACX,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,MAAM,QAAQ,GAAG;gBACf,IAAI,EAAE,eAAe;gBACrB,QAAQ,EAAE,KAAc;gBACxB,GAAG,EAAE,qBAAqB;gBAC1B,MAAM,EAAE,MAAe;gBACvB,UAAU,EAAE,EAAE;aACf,CAAC;YAEF,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAEnD,MAAM,MAAM,CACV,OAAO,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC,CACzC,CAAC,OAAO,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;YAC1C,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAEnD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,sCAAsC,CAAC,CAAC;YAEpF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACjC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBAClD,KAAK,EAAE,EAAE,EAAE,EAAE,sCAAsC,EAAE;aACtD,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE/C,MAAM,MAAM,CACV,OAAO,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAC1C,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,gBAAgB,GAAG,cAAc,CAAC,kBAAkB,EAAE,CAAC;YAC7D,gBAAgB,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAEpE,MAAM,cAAc,GAAG;gBACrB,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,EAAE;gBACT,SAAS,EAAE,CAAC,KAAK,CAAC;gBAClB,QAAQ,EAAE,CAAC,QAAQ,CAAC;gBACpB,SAAS,EAAE,CAAC,MAAM,CAAC;aACpB,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;YAEzD,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CACpD,kCAAkC,EAClC,EAAE,SAAS,EAAE,CAAC,KAAK,CAAC,EAAE,CACvB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,MAAM,gBAAgB,GAAG,cAAc,CAAC,kBAAkB,EAAE,CAAC;YAC7D,gBAAgB,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YAE5D,MAAM,cAAc,GAAG;gBACrB,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,EAAE;gBACT,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,IAAI;gBACjB,UAAU,EAAE,IAAI;aACjB,CAAC;YAEF,MAAM,OAAO,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;YAE1C,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CACpD,2BAA2B,EAC3B,EAAE,QAAQ,EAAE,IAAI,EAAE,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,YAAY,GAAG,EAAE,GAAG,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;YAC1E,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YACvD,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC;gBACpC,GAAG,YAAY;gBACf,MAAM,EAAE,QAAQ;gBAChB,QAAQ,EAAE,IAAI;aACf,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,YAAY,CAAC,sCAAsC,EAAE,UAAU,CAAC,CAAC;YAE9F,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACjD,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;YAC/D,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACrD,UAAU,EACV,UAAU,EACV,oBAAoB,EACpB,YAAY,CAAC,EAAE,EACf,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE/C,MAAM,MAAM,CACV,OAAO,CAAC,YAAY,CAAC,iBAAiB,EAAE,UAAU,CAAC,CACpD,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACnD,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC;gBACpC,GAAG,QAAQ;gBACX,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,KAAK;aAChB,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,sCAAsC,EAAE,UAAU,CAAC,CAAC;YAEhG,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,gBAAgB,EAAE,CAAC;YAC/C,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAC3D,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACrD,UAAU,EACV,YAAY,EACZ,oBAAoB,EACpB,QAAQ,CAAC,EAAE,EACX,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,yBAAyB,EAAE,KAAK,IAAI,EAAE;YACvC,MAAM,OAAO,GAAG,EAAE,GAAG,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;YACjD,MAAM,YAAY,GAAG;gBACnB,eAAe,EAAE;oBACf;wBACE,UAAU,EAAE,eAAe;wBAC3B,KAAK,EAAE,mBAAmB;wBAC1B,QAAQ,EAAE,MAAM;qBACjB;iBACF;gBACD,QAAQ,EAAE;oBACR,MAAM,EAAE,KAAK;oBACb,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,UAAU,EAAE,CAAC;iBACd;aACF,CAAC;YAEF,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAClD,sBAAsB,CAAC,YAAY,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YACpE,sBAAsB,CAAC,gBAAgB,CAAC,iBAAiB,CAAC;gBACxD,YAAY,EAAE,CAAC;gBACf,YAAY,EAAE,CAAC;gBACf,cAAc,EAAE,CAAC;gBACjB,YAAY,EAAE,GAAG;gBACjB,iBAAiB,EAAE,GAAG;aACvB,CAAC,CAAC;YACH,uBAAuB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YACxD,uBAAuB,CAAC,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;YAClE,uBAAuB,CAAC,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YAClE,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAE/C,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC,sCAAsC,EAAE,UAAU,CAAC,CAAC;YAE1F,MAAM,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC,oBAAoB,CAC9D,MAAM,CAAC,gBAAgB,CAAC;gBACtB,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,UAAU,EAAE,OAAO,CAAC,UAAU;aAC/B,CAAC,CACH,CAAC;YACF,MAAM,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACnE,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACnD,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACrD,UAAU,EACV,MAAM,EACN,oBAAoB,EACpB,OAAO,CAAC,EAAE,EACV,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,MAAM,SAAS,GAAG,EAAE,GAAG,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;YACpD,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACpD,sBAAsB,CAAC,YAAY,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;YAClF,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC;gBACpC,GAAG,SAAS;gBACZ,MAAM,EAAE,OAAO;aAChB,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC,sCAAsC,EAAE,UAAU,CAAC,CAAC;YAE1F,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC3C,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,oBAAoB,CAC9C,qBAAqB,EACrB,MAAM,CAAC,gBAAgB,CAAC;gBACtB,MAAM,EAAE,SAAS,CAAC,EAAE;gBACpB,KAAK,EAAE,eAAe;aACvB,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,YAAY,GAAG,EAAE,GAAG,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;YAC1E,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAEvD,MAAM,MAAM,CACV,OAAO,CAAC,QAAQ,CAAC,sCAAsC,EAAE,UAAU,CAAC,CACrE,CAAC,OAAO,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,OAAO,GAAG;gBACd,WAAW,EAAE,qBAAqB;gBAClC,QAAQ,EAAE,UAAmB;gBAC7B,UAAU,EAAE;oBACV,YAAY,EAAE,IAAI;oBAClB,SAAS,EAAE,GAAG;iBACf;aACF,CAAC;YAEF,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACnD,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC;gBACpC,GAAG,QAAQ;gBACX,GAAG,OAAO;aACX,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,sCAAsC,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;YAErG,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,CAAC;YAC/C,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACrD,UAAU,EACV,QAAQ,EACR,oBAAoB,EACpB,sCAAsC,EACtC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC5B,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,YAAY,GAAG,EAAE,GAAG,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;YAC1E,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YACvD,cAAc,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAEtD,MAAM,OAAO,CAAC,UAAU,CAAC,sCAAsC,EAAE,UAAU,CAAC,CAAC;YAE7E,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;YACjE,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACrD,UAAU,EACV,QAAQ,EACR,oBAAoB,EACpB,YAAY,CAAC,EAAE,EACf,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,KAAK,IAAI,EAAE;YACvE,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAEnD,MAAM,MAAM,CACV,OAAO,CAAC,UAAU,CAAC,sCAAsC,EAAE,UAAU,CAAC,CACvE,CAAC,OAAO,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\__tests__\\vulnerability-feed.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { getRepositoryToken } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\nimport { VulnerabilityFeedService } from '../vulnerability-feed.service';\r\nimport { VulnerabilityFeed } from '../../../domain/entities/vulnerability-feed.entity';\r\nimport { Vulnerability } from '../../../domain/entities/vulnerability.entity';\r\nimport { LoggerService } from '../../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../../infrastructure/logging/audit/audit.service';\r\nimport { VulnerabilityFeedIntegrationService } from '../../../infrastructure/services/vulnerability-feed-integration.service';\r\n\r\ndescribe('VulnerabilityFeedService', () => {\r\n  let service: VulnerabilityFeedService;\r\n  let feedRepository: jest.Mocked<Repository<VulnerabilityFeed>>;\r\n  let vulnerabilityRepository: jest.Mocked<Repository<Vulnerability>>;\r\n  let loggerService: jest.Mocked<LoggerService>;\r\n  let auditService: jest.Mocked<AuditService>;\r\n  let feedIntegrationService: jest.Mocked<VulnerabilityFeedIntegrationService>;\r\n\r\n  const mockFeed = {\r\n    id: '123e4567-e89b-12d3-a456-426614174000',\r\n    name: 'Test Feed',\r\n    description: 'Test feed description',\r\n    feedType: 'nvd',\r\n    provider: 'NIST',\r\n    url: 'https://services.nvd.nist.gov/rest/json/cves/2.0',\r\n    format: 'json',\r\n    feedConfig: {\r\n      syncInterval: 3600,\r\n      batchSize: 1000,\r\n    },\r\n    priority: 'high',\r\n    status: 'active',\r\n    isActive: true,\r\n    lastSyncAt: new Date('2023-12-01T10:00:00Z'),\r\n    nextSyncAt: new Date('2023-12-01T11:00:00Z'),\r\n    syncStats: {\r\n      totalSyncs: 10,\r\n      successfulSyncs: 9,\r\n      failedSyncs: 1,\r\n      lastSyncDuration: 300,\r\n    },\r\n    qualityMetrics: {\r\n      completenessScore: 95,\r\n      accuracyScore: 98,\r\n      errorRate: 2,\r\n    },\r\n    getSummary: jest.fn(),\r\n    exportForReporting: jest.fn(),\r\n    activate: jest.fn(),\r\n    deactivate: jest.fn(),\r\n    pause: jest.fn(),\r\n    resume: jest.fn(),\r\n    updateSyncStats: jest.fn(),\r\n    updateQualityMetrics: jest.fn(),\r\n    isHealthy: true,\r\n    syncSuccessRate: 90,\r\n    hoursSinceLastSync: 1,\r\n    isOverdue: false,\r\n    isDueForSync: true,\r\n  };\r\n\r\n  const mockVulnerability = {\r\n    id: 'vuln-123',\r\n    identifier: 'CVE-2023-1234',\r\n    title: 'Test Vulnerability',\r\n    severity: 'high',\r\n  };\r\n\r\n  beforeEach(async () => {\r\n    const mockRepository = {\r\n      create: jest.fn(),\r\n      save: jest.fn(),\r\n      find: jest.fn(),\r\n      findOne: jest.fn(),\r\n      findOneBy: jest.fn(),\r\n      count: jest.fn(),\r\n      remove: jest.fn(),\r\n      createQueryBuilder: jest.fn(() => ({\r\n        leftJoinAndSelect: jest.fn().mockReturnThis(),\r\n        where: jest.fn().mockReturnThis(),\r\n        andWhere: jest.fn().mockReturnThis(),\r\n        orderBy: jest.fn().mockReturnThis(),\r\n        addOrderBy: jest.fn().mockReturnThis(),\r\n        skip: jest.fn().mockReturnThis(),\r\n        take: jest.fn().mockReturnThis(),\r\n        getManyAndCount: jest.fn(),\r\n        getMany: jest.fn(),\r\n      })),\r\n    };\r\n\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        VulnerabilityFeedService,\r\n        {\r\n          provide: getRepositoryToken(VulnerabilityFeed),\r\n          useValue: mockRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(Vulnerability),\r\n          useValue: mockRepository,\r\n        },\r\n        {\r\n          provide: LoggerService,\r\n          useValue: {\r\n            debug: jest.fn(),\r\n            log: jest.fn(),\r\n            warn: jest.fn(),\r\n            error: jest.fn(),\r\n          },\r\n        },\r\n        {\r\n          provide: AuditService,\r\n          useValue: {\r\n            logUserAction: jest.fn(),\r\n          },\r\n        },\r\n        {\r\n          provide: VulnerabilityFeedIntegrationService,\r\n          useValue: {\r\n            fetchNVDData: jest.fn(),\r\n            fetchMITREData: jest.fn(),\r\n            fetchVendorAdvisories: jest.fn(),\r\n            fetchThreatIntelligence: jest.fn(),\r\n            fetchExploitDatabase: jest.fn(),\r\n            validateFeedData: jest.fn(),\r\n          },\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    service = module.get<VulnerabilityFeedService>(VulnerabilityFeedService);\r\n    feedRepository = module.get(getRepositoryToken(VulnerabilityFeed));\r\n    vulnerabilityRepository = module.get(getRepositoryToken(Vulnerability));\r\n    loggerService = module.get(LoggerService);\r\n    auditService = module.get(AuditService);\r\n    feedIntegrationService = module.get(VulnerabilityFeedIntegrationService);\r\n  });\r\n\r\n  it('should be defined', () => {\r\n    expect(service).toBeDefined();\r\n  });\r\n\r\n  describe('createFeed', () => {\r\n    it('should create a new feed', async () => {\r\n      const feedData = {\r\n        name: 'New Feed',\r\n        description: 'New feed description',\r\n        feedType: 'nvd' as const,\r\n        provider: 'NIST',\r\n        url: 'https://services.nvd.nist.gov/rest/json/cves/2.0',\r\n        format: 'json' as const,\r\n        feedConfig: {\r\n          syncInterval: 3600,\r\n          batchSize: 1000,\r\n        },\r\n      };\r\n\r\n      feedRepository.create.mockReturnValue(mockFeed);\r\n      feedRepository.save.mockResolvedValue(mockFeed);\r\n\r\n      const result = await service.createFeed(feedData, 'user-123');\r\n\r\n      expect(feedRepository.create).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          ...feedData,\r\n          status: 'inactive',\r\n          isActive: false,\r\n        })\r\n      );\r\n      expect(feedRepository.save).toHaveBeenCalledWith(mockFeed);\r\n      expect(auditService.logUserAction).toHaveBeenCalledWith(\r\n        'user-123',\r\n        'create',\r\n        'vulnerability_feed',\r\n        mockFeed.id,\r\n        expect.any(Object)\r\n      );\r\n    });\r\n\r\n    it('should throw error when feed with same name exists', async () => {\r\n      const feedData = {\r\n        name: 'Existing Feed',\r\n        feedType: 'nvd' as const,\r\n        url: 'https://example.com',\r\n        format: 'json' as const,\r\n        feedConfig: {},\r\n      };\r\n\r\n      feedRepository.findOne.mockResolvedValue(mockFeed);\r\n\r\n      await expect(\r\n        service.createFeed(feedData, 'user-123')\r\n      ).rejects.toThrow('Feed with this name already exists');\r\n    });\r\n  });\r\n\r\n  describe('getFeedDetails', () => {\r\n    it('should return feed details', async () => {\r\n      feedRepository.findOne.mockResolvedValue(mockFeed);\r\n\r\n      const result = await service.getFeedDetails('123e4567-e89b-12d3-a456-426614174000');\r\n\r\n      expect(result).toEqual(mockFeed);\r\n      expect(feedRepository.findOne).toHaveBeenCalledWith({\r\n        where: { id: '123e4567-e89b-12d3-a456-426614174000' },\r\n      });\r\n    });\r\n\r\n    it('should throw NotFoundException when feed not found', async () => {\r\n      feedRepository.findOne.mockResolvedValue(null);\r\n\r\n      await expect(\r\n        service.getFeedDetails('non-existent-id')\r\n      ).rejects.toThrow('Feed not found');\r\n    });\r\n  });\r\n\r\n  describe('searchFeeds', () => {\r\n    it('should search feeds with filters', async () => {\r\n      const mockQueryBuilder = feedRepository.createQueryBuilder();\r\n      mockQueryBuilder.getManyAndCount.mockResolvedValue([[mockFeed], 1]);\r\n\r\n      const searchCriteria = {\r\n        page: 1,\r\n        limit: 10,\r\n        feedTypes: ['nvd'],\r\n        statuses: ['active'],\r\n        providers: ['NIST'],\r\n      };\r\n\r\n      const result = await service.searchFeeds(searchCriteria);\r\n\r\n      expect(result).toHaveProperty('feeds');\r\n      expect(result).toHaveProperty('total');\r\n      expect(result).toHaveProperty('page');\r\n      expect(result).toHaveProperty('totalPages');\r\n      expect(result.feeds).toHaveLength(1);\r\n      expect(result.total).toBe(1);\r\n      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(\r\n        'feed.feedType IN (:...feedTypes)',\r\n        { feedTypes: ['nvd'] }\r\n      );\r\n    });\r\n\r\n    it('should handle boolean filters', async () => {\r\n      const mockQueryBuilder = feedRepository.createQueryBuilder();\r\n      mockQueryBuilder.getManyAndCount.mockResolvedValue([[], 0]);\r\n\r\n      const searchCriteria = {\r\n        page: 1,\r\n        limit: 10,\r\n        isActive: true,\r\n        healthyOnly: true,\r\n        dueForSync: true,\r\n      };\r\n\r\n      await service.searchFeeds(searchCriteria);\r\n\r\n      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(\r\n        'feed.isActive = :isActive',\r\n        { isActive: true }\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('activateFeed', () => {\r\n    it('should activate an inactive feed', async () => {\r\n      const inactiveFeed = { ...mockFeed, status: 'inactive', isActive: false };\r\n      feedRepository.findOne.mockResolvedValue(inactiveFeed);\r\n      feedRepository.save.mockResolvedValue({\r\n        ...inactiveFeed,\r\n        status: 'active',\r\n        isActive: true,\r\n      });\r\n\r\n      const result = await service.activateFeed('123e4567-e89b-12d3-a456-426614174000', 'user-123');\r\n\r\n      expect(inactiveFeed.activate).toHaveBeenCalled();\r\n      expect(feedRepository.save).toHaveBeenCalledWith(inactiveFeed);\r\n      expect(auditService.logUserAction).toHaveBeenCalledWith(\r\n        'user-123',\r\n        'activate',\r\n        'vulnerability_feed',\r\n        inactiveFeed.id,\r\n        expect.any(Object)\r\n      );\r\n    });\r\n\r\n    it('should throw error when feed not found', async () => {\r\n      feedRepository.findOne.mockResolvedValue(null);\r\n\r\n      await expect(\r\n        service.activateFeed('non-existent-id', 'user-123')\r\n      ).rejects.toThrow('Feed not found');\r\n    });\r\n  });\r\n\r\n  describe('deactivateFeed', () => {\r\n    it('should deactivate an active feed', async () => {\r\n      feedRepository.findOne.mockResolvedValue(mockFeed);\r\n      feedRepository.save.mockResolvedValue({\r\n        ...mockFeed,\r\n        status: 'inactive',\r\n        isActive: false,\r\n      });\r\n\r\n      const result = await service.deactivateFeed('123e4567-e89b-12d3-a456-426614174000', 'user-123');\r\n\r\n      expect(mockFeed.deactivate).toHaveBeenCalled();\r\n      expect(feedRepository.save).toHaveBeenCalledWith(mockFeed);\r\n      expect(auditService.logUserAction).toHaveBeenCalledWith(\r\n        'user-123',\r\n        'deactivate',\r\n        'vulnerability_feed',\r\n        mockFeed.id,\r\n        expect.any(Object)\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('syncFeed', () => {\r\n    it('should sync an NVD feed', async () => {\r\n      const nvdFeed = { ...mockFeed, feedType: 'nvd' };\r\n      const mockFeedData = {\r\n        vulnerabilities: [\r\n          {\r\n            identifier: 'CVE-2023-5678',\r\n            title: 'New Vulnerability',\r\n            severity: 'high',\r\n          },\r\n        ],\r\n        metadata: {\r\n          source: 'nvd',\r\n          fetchedAt: new Date(),\r\n          totalCount: 1,\r\n        },\r\n      };\r\n\r\n      feedRepository.findOne.mockResolvedValue(nvdFeed);\r\n      feedIntegrationService.fetchNVDData.mockResolvedValue(mockFeedData);\r\n      feedIntegrationService.validateFeedData.mockResolvedValue({\r\n        totalRecords: 1,\r\n        validRecords: 1,\r\n        invalidRecords: 0,\r\n        qualityScore: 100,\r\n        completenessScore: 100,\r\n      });\r\n      vulnerabilityRepository.findOne.mockResolvedValue(null);\r\n      vulnerabilityRepository.create.mockReturnValue(mockVulnerability);\r\n      vulnerabilityRepository.save.mockResolvedValue(mockVulnerability);\r\n      feedRepository.save.mockResolvedValue(nvdFeed);\r\n\r\n      const result = await service.syncFeed('123e4567-e89b-12d3-a456-426614174000', 'user-123');\r\n\r\n      expect(feedIntegrationService.fetchNVDData).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          url: nvdFeed.url,\r\n          feedConfig: nvdFeed.feedConfig,\r\n        })\r\n      );\r\n      expect(feedIntegrationService.validateFeedData).toHaveBeenCalled();\r\n      expect(nvdFeed.updateSyncStats).toHaveBeenCalled();\r\n      expect(auditService.logUserAction).toHaveBeenCalledWith(\r\n        'user-123',\r\n        'sync',\r\n        'vulnerability_feed',\r\n        nvdFeed.id,\r\n        expect.any(Object)\r\n      );\r\n    });\r\n\r\n    it('should handle sync errors gracefully', async () => {\r\n      const errorFeed = { ...mockFeed, status: 'active' };\r\n      feedRepository.findOne.mockResolvedValue(errorFeed);\r\n      feedIntegrationService.fetchNVDData.mockRejectedValue(new Error('Network error'));\r\n      feedRepository.save.mockResolvedValue({\r\n        ...errorFeed,\r\n        status: 'error',\r\n      });\r\n\r\n      const result = await service.syncFeed('123e4567-e89b-12d3-a456-426614174000', 'user-123');\r\n\r\n      expect(result.success).toBe(false);\r\n      expect(result.error).toBe('Network error');\r\n      expect(loggerService.error).toHaveBeenCalledWith(\r\n        'Failed to sync feed',\r\n        expect.objectContaining({\r\n          feedId: errorFeed.id,\r\n          error: 'Network error',\r\n        })\r\n      );\r\n    });\r\n\r\n    it('should throw error when feed is not active', async () => {\r\n      const inactiveFeed = { ...mockFeed, status: 'inactive', isActive: false };\r\n      feedRepository.findOne.mockResolvedValue(inactiveFeed);\r\n\r\n      await expect(\r\n        service.syncFeed('123e4567-e89b-12d3-a456-426614174000', 'user-123')\r\n      ).rejects.toThrow('Feed is not active');\r\n    });\r\n  });\r\n\r\n  describe('updateFeed', () => {\r\n    it('should update an existing feed', async () => {\r\n      const updates = {\r\n        description: 'Updated description',\r\n        priority: 'critical' as const,\r\n        feedConfig: {\r\n          syncInterval: 1800,\r\n          batchSize: 500,\r\n        },\r\n      };\r\n\r\n      feedRepository.findOne.mockResolvedValue(mockFeed);\r\n      feedRepository.save.mockResolvedValue({\r\n        ...mockFeed,\r\n        ...updates,\r\n      });\r\n\r\n      const result = await service.updateFeed('123e4567-e89b-12d3-a456-426614174000', updates, 'user-123');\r\n\r\n      expect(feedRepository.save).toHaveBeenCalled();\r\n      expect(auditService.logUserAction).toHaveBeenCalledWith(\r\n        'user-123',\r\n        'update',\r\n        'vulnerability_feed',\r\n        '123e4567-e89b-12d3-a456-426614174000',\r\n        expect.objectContaining({\r\n          name: mockFeed.name,\r\n          changes: expect.any(Object),\r\n        })\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('deleteFeed', () => {\r\n    it('should delete an inactive feed', async () => {\r\n      const inactiveFeed = { ...mockFeed, status: 'inactive', isActive: false };\r\n      feedRepository.findOne.mockResolvedValue(inactiveFeed);\r\n      feedRepository.remove.mockResolvedValue(inactiveFeed);\r\n\r\n      await service.deleteFeed('123e4567-e89b-12d3-a456-426614174000', 'user-123');\r\n\r\n      expect(feedRepository.remove).toHaveBeenCalledWith(inactiveFeed);\r\n      expect(auditService.logUserAction).toHaveBeenCalledWith(\r\n        'user-123',\r\n        'delete',\r\n        'vulnerability_feed',\r\n        inactiveFeed.id,\r\n        expect.any(Object)\r\n      );\r\n    });\r\n\r\n    it('should throw error when trying to delete an active feed', async () => {\r\n      feedRepository.findOne.mockResolvedValue(mockFeed);\r\n\r\n      await expect(\r\n        service.deleteFeed('123e4567-e89b-12d3-a456-426614174000', 'user-123')\r\n      ).rejects.toThrow('Cannot delete active feed');\r\n    });\r\n  });\r\n});\r\n"], "version": 3}