{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\application\\services\\threat-intelligence.service.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,2CAA0F;AAC1F,6CAAmD;AACnD,qCAA8E;AAC9E,2CAA+C;AAE/C,iGAAkJ;AAClJ,yGAA6F;AAC7F,mFAAwE;AACxE,yFAA8E;AAC9E,sFAAkF;AAClF,0FAAsF;AACtF,uGAAmG;AAsCnG;;;GAGG;AAEI,IAAM,yBAAyB,iCAA/B,MAAM,yBAAyB;IAGpC,YAEE,4BAA6E,EAE7E,aAAiE,EAEjE,qBAA+D,EAE/D,wBAAqE,EACpD,aAA4B,EAC5B,YAA0B,EAC1B,mBAAwC,EACxC,aAA4B;QAV5B,iCAA4B,GAA5B,4BAA4B,CAAgC;QAE5D,kBAAa,GAAb,aAAa,CAAmC;QAEhD,0BAAqB,GAArB,qBAAqB,CAAyB;QAE9C,6BAAwB,GAAxB,wBAAwB,CAA4B;QACpD,kBAAa,GAAb,aAAa,CAAe;QAC5B,iBAAY,GAAZ,YAAY,CAAc;QAC1B,wBAAmB,GAAnB,mBAAmB,CAAqB;QACxC,kBAAa,GAAb,aAAa,CAAe;QAd9B,WAAM,GAAG,IAAI,eAAM,CAAC,2BAAyB,CAAC,IAAI,CAAC,CAAC;IAelE,CAAC;IAEJ;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAC5B,UAAuC,EACvC,MAAc;QAEd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;YAChD,KAAK,EAAE,UAAU,CAAC,KAAK;YACvB,UAAU,EAAE,UAAU,CAAC,UAAU;YACjC,MAAM;SACP,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,sDAAsD;YACtD,IAAI,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;gBAC9C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC;oBAC/D,KAAK,EAAE;wBACL,KAAK,EAAE,UAAU,CAAC,KAAK;wBACvB,UAAU,EAAE,UAAU,CAAC,UAAiB;qBACzC;iBACF,CAAC,CAAC;gBAEH,IAAI,QAAQ,EAAE,CAAC;oBACb,MAAM,IAAI,0BAAiB,CAAC,oEAAoE,CAAC,CAAC;gBACpG,CAAC;YACH,CAAC;YAED,qBAAqB;YACrB,MAAM,MAAM,GAAG,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC;gBACtD,GAAG,UAAU;gBACb,MAAM,EAAE,UAAU,CAAC,MAAM,IAAI,yCAAY,CAAC,MAAM;gBAChD,UAAU,EAAE,UAAU,CAAC,UAAU,IAAI,6CAAgB,CAAC,MAAM;gBAC5D,QAAQ,EAAE,UAAU,CAAC,QAAQ,IAAI,2CAAc,CAAC,MAAM;gBACtD,SAAS,EAAE,UAAU,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE;gBAC7C,gBAAgB,EAAE,CAAC;gBACnB,KAAK,EAAE,KAAK;gBACZ,YAAY,EAAE,CAAC,CAAC,UAAU,CAAC,aAAa;aACzC,CAAC,CAAC;YAEH,+BAA+B;YAC/B,MAAM,CAAC,kBAAkB,EAAE,CAAC;YAE5B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAEzE,yCAAyC;YACzC,IAAI,WAAW,CAAC,QAAQ,KAAK,2CAAc,CAAC,QAAQ,EAAE,CAAC;gBACrD,MAAM,IAAI,CAAC,mBAAmB,CAAC,uBAAuB,CAAC;oBACrD,QAAQ,EAAE,WAAW,CAAC,EAAE;oBACxB,KAAK,EAAE,WAAW,CAAC,KAAK;oBACxB,UAAU,EAAE,WAAW,CAAC,UAAU;oBAClC,QAAQ,EAAE,WAAW,CAAC,QAAQ;oBAC9B,UAAU,EAAE,WAAW,CAAC,UAAU,CAAC,IAAI;iBACxC,CAAC,CAAC;YACL,CAAC;YAED,YAAY;YACZ,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,qBAAqB,EACrB,WAAW,CAAC,EAAE,EACd;gBACE,KAAK,EAAE,WAAW,CAAC,KAAK;gBACxB,UAAU,EAAE,WAAW,CAAC,UAAU;gBAClC,QAAQ,EAAE,WAAW,CAAC,QAAQ;gBAC9B,UAAU,EAAE,WAAW,CAAC,UAAU,CAAC,IAAI;aACxC,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0CAA0C,EAAE;gBAC1D,QAAQ,EAAE,WAAW,CAAC,EAAE;gBACxB,KAAK,EAAE,WAAW,CAAC,KAAK;gBACxB,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,sCAAsC,EAAE;gBAC/D,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,UAAU,EAAE;oBACV,KAAK,EAAE,UAAU,CAAC,KAAK;oBACvB,UAAU,EAAE,UAAU,CAAC,UAAU;iBAClC;gBACD,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,yBAAyB,CAAC,EAAU;QACxC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC;YAC7D,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,SAAS,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,gBAAgB,CAAC;SAC3D,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,0BAAiB,CAAC,+BAA+B,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAC5B,QAA0C;QAO1C,MAAM,EACJ,IAAI,GAAG,CAAC,EACR,KAAK,GAAG,EAAE,EACV,WAAW,EACX,UAAU,EACV,QAAQ,EACR,gBAAgB,EAChB,IAAI,EACJ,UAAU,EACV,SAAS,EACT,YAAY,EACZ,YAAY,EACZ,aAAa,EACb,UAAU,EACV,UAAU,EACV,cAAc,GAAG,KAAK,EACtB,MAAM,GAAG,WAAW,EACpB,SAAS,GAAG,MAAM,GACnB,GAAG,QAAQ,CAAC;QAEb,MAAM,YAAY,GAAG,IAAI,CAAC,4BAA4B,CAAC,kBAAkB,CAAC,QAAQ,CAAC;aAChF,iBAAiB,CAAC,oBAAoB,EAAE,OAAO,CAAC;aAChD,iBAAiB,CAAC,uBAAuB,EAAE,UAAU,CAAC;aACtD,iBAAiB,CAAC,mBAAmB,EAAE,YAAY,CAAC,CAAC;QAExD,gBAAgB;QAChB,IAAI,WAAW,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1C,YAAY,CAAC,QAAQ,CAAC,wCAAwC,EAAE,EAAE,WAAW,EAAE,CAAC,CAAC;QACnF,CAAC;QAED,IAAI,UAAU,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxC,YAAY,CAAC,QAAQ,CAAC,qCAAqC,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC;QAC/E,CAAC;QAED,IAAI,QAAQ,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpC,YAAY,CAAC,QAAQ,CAAC,iCAAiC,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpD,YAAY,CAAC,QAAQ,CAAC,6CAA6C,EAAE,EAAE,gBAAgB,EAAE,CAAC,CAAC;QAC7F,CAAC;QAED,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,YAAY,CAAC,QAAQ,CAAC,sBAAsB,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,UAAU,EAAE,CAAC;YACf,YAAY,CAAC,QAAQ,CACnB,oGAAoG,EACpG,EAAE,UAAU,EAAE,IAAI,UAAU,GAAG,EAAE,CAClC,CAAC;QACJ,CAAC;QAED,IAAI,SAAS,EAAE,CAAC;YACd,YAAY,CAAC,QAAQ,CAAC,kDAAkD,EAAE;gBACxE,SAAS,EAAE,SAAS,CAAC,SAAS;gBAC9B,OAAO,EAAE,SAAS,CAAC,OAAO;aAC3B,CAAC,CAAC;QACL,CAAC;QAED,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YAC/B,YAAY,CAAC,QAAQ,CAAC,mCAAmC,EAAE,EAAE,YAAY,EAAE,CAAC,CAAC;QAC/E,CAAC;QAED,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YAC/B,YAAY,CAAC,QAAQ,CAAC,mCAAmC,EAAE,EAAE,YAAY,EAAE,CAAC,CAAC;QAC/E,CAAC;QAED,IAAI,aAAa,EAAE,CAAC;YAClB,YAAY,CAAC,QAAQ,CAAC,uCAAuC,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;QACpF,CAAC;QAED,IAAI,UAAU,EAAE,CAAC;YACf,YAAY,CAAC,QAAQ,CAAC,uCAAuC,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC;QACjF,CAAC;QAED,IAAI,UAAU,EAAE,CAAC;YACf,YAAY,CAAC,QAAQ,CAAC,0CAA0C,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC;QACpF,CAAC;QAED,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,YAAY,CAAC,QAAQ,CACnB,uDAAuD,EACvD,EAAE,GAAG,EAAE,IAAI,IAAI,EAAE,EAAE,CACpB,CAAC;QACJ,CAAC;QAED,gBAAgB;QAChB,IAAI,CAAC,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,kBAAkB,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5F,YAAY,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE,EAAE,SAAS,CAAC,CAAC;QACtD,CAAC;aAAM,CAAC;YACN,YAAY,CAAC,OAAO,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;QACnD,CAAC;QAED,qCAAqC;QACrC,YAAY,CAAC,UAAU,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QAE5C,mBAAmB;QACnB,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAClC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEtC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,MAAM,YAAY,CAAC,eAAe,EAAE,CAAC;QAC9D,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;QAE5C,OAAO;YACL,OAAO;YACP,KAAK;YACL,IAAI;YACJ,UAAU;SACX,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAC5B,EAAU,EACV,OAAoC,EACpC,MAAc;QAEd,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,EAAE,CAAC,CAAC;QAExD,0BAA0B;QAC1B,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACjC,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;gBACjC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;YACzD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAE/B,oDAAoD;QACpD,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;YACvE,MAAM,CAAC,kBAAkB,EAAE,CAAC;QAC9B,CAAC;QAED,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAE3E,YAAY;QACZ,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,qBAAqB,EACrB,EAAE,EACF;YACE,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,OAAO;SACR,CACF,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,6BAA6B,EAAE;YAC7C,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;YAC7B,MAAM;SACP,CAAC,CAAC;QAEH,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAAC,EAAU,EAAE,MAAc;QACvD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,EAAE,CAAC,CAAC;QAExD,MAAM,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAEvD,YAAY;QACZ,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,qBAAqB,EACrB,EAAE,EACF;YACE,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,UAAU,EAAE,MAAM,CAAC,UAAU;SAC9B,CACF,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,6BAA6B,EAAE;YAC7C,QAAQ,EAAE,EAAE;YACZ,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,MAAM;SACP,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,EAAU,EAAE,OAAa;QAC/C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,EAAE,CAAC,CAAC;QAExD,MAAM,CAAC,iBAAiB,EAAE,CAAC;QAC3B,MAAM,CAAC,kBAAkB,EAAE,CAAC;QAE5B,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,CAAC,gBAAgB,GAAG;gBACxB,GAAG,MAAM,CAAC,gBAAgB;gBAC1B,sBAAsB,EAAE,OAAO;aAChC,CAAC;QACJ,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CACtB,QAA6B;QAM7B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;QACvF,MAAM,OAAO,GAAG,EAAE,CAAC;QAEnB,4BAA4B;QAC5B,IAAI,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC,aAAa,EAAE,CAAC;YAC5E,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC;gBAChE,KAAK,EAAE;oBACL,aAAa,EAAE,UAAU,CAAC,aAAa;oBACvC,EAAE,EAAE,IAAA,aAAG,EAAC,UAAU,CAAC,EAAE,CAAC;iBACvB;gBACD,SAAS,EAAE,CAAC,aAAa,CAAC;gBAC1B,IAAI,EAAE,EAAE;aACT,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CAAC;gBACX,cAAc,EAAE,YAAY;gBAC5B,gBAAgB,EAAE,GAAG;gBACrB,eAAe,EAAE,cAAc;aAChC,CAAC,CAAC;QACL,CAAC;QAED,wBAAwB;QACxB,IAAI,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,gBAAgB,EAAE,CAAC;YAClF,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC;gBACnE,KAAK,EAAE;oBACL,gBAAgB,EAAE,UAAU,CAAC,gBAAgB;oBAC7C,EAAE,EAAE,IAAA,aAAG,EAAC,UAAU,CAAC,EAAE,CAAC;iBACvB;gBACD,SAAS,EAAE,CAAC,gBAAgB,CAAC;gBAC7B,IAAI,EAAE,EAAE;aACT,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CAAC;gBACX,cAAc,EAAE,eAAe;gBAC/B,gBAAgB,EAAE,GAAG;gBACrB,eAAe,EAAE,UAAU;aAC5B,CAAC,CAAC;QACL,CAAC;QAED,oBAAoB;QACpB,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,4BAA4B;iBACvD,kBAAkB,CAAC,QAAQ,CAAC;iBAC5B,KAAK,CAAC,sBAAsB,EAAE,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,EAAE,CAAC;iBACxD,QAAQ,CAAC,kBAAkB,EAAE,EAAE,EAAE,EAAE,UAAU,CAAC,EAAE,EAAE,CAAC;iBACnD,IAAI,CAAC,EAAE,CAAC;iBACR,OAAO,EAAE,CAAC;YAEb,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC1B,OAAO,CAAC,IAAI,CAAC;oBACX,cAAc,EAAE,UAAU;oBAC1B,gBAAgB,EAAE,GAAG;oBACrB,eAAe,EAAE,MAAM;iBACxB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB;QACpB,MAAM,CACJ,YAAY,EACZ,aAAa,EACb,eAAe,EACf,aAAa,EACb,cAAc,EACd,SAAS,EACV,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACpB,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE;YACzC,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,yCAAY,CAAC,MAAM,EAAE,EAAE,CAAC;YACnF,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC;gBACtC,KAAK,EAAE;oBACL,MAAM,EAAE,yCAAY,CAAC,MAAM;oBAC3B,QAAQ,EAAE,2CAAc,CAAC,QAAQ;iBAClC;aACF,CAAC;YACF,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC;gBACtC,KAAK,EAAE;oBACL,SAAS,EAAE,IAAA,iBAAO,EAChB,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAC9C,IAAI,IAAI,EAAE,CACX;iBACF;aACF,CAAC;YACF,IAAI,CAAC,yBAAyB,EAAE;YAChC,IAAI,CAAC,kBAAkB,EAAE;SAC1B,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE;gBACP,KAAK,EAAE,YAAY;gBACnB,MAAM,EAAE,aAAa;gBACrB,QAAQ,EAAE,eAAe;gBACzB,MAAM,EAAE,aAAa;aACtB;YACD,YAAY,EAAE;gBACZ,WAAW,EAAE,cAAc;gBAC3B,SAAS;aACV;YACD,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAC5B,QAA0C,EAC1C,MAA+B;QAE/B,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC;YACtD,GAAG,QAAQ;YACX,KAAK,EAAE,KAAK,EAAE,yBAAyB;SACxC,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAEtE,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,MAAM;gBACT,OAAO;oBACL,MAAM,EAAE,MAAM;oBACd,IAAI,EAAE,UAAU;oBAChB,UAAU,EAAE,IAAI,IAAI,EAAE;oBACtB,KAAK,EAAE,UAAU,CAAC,MAAM;iBACzB,CAAC;YACJ,KAAK,KAAK;gBACR,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YACvC,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YACrC;gBACE,MAAM,IAAI,KAAK,CAAC,8BAA8B,MAAM,EAAE,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED,yBAAyB;IACjB,KAAK,CAAC,yBAAyB;QACrC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,4BAA4B;aACnD,kBAAkB,CAAC,QAAQ,CAAC;aAC5B,MAAM,CAAC,mBAAmB,EAAE,MAAM,CAAC;aACnC,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC;aAC9B,KAAK,CAAC,yBAAyB,EAAE,EAAE,MAAM,EAAE,yCAAY,CAAC,MAAM,EAAE,CAAC;aACjE,OAAO,CAAC,mBAAmB,CAAC;aAC5B,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC;aACxB,UAAU,EAAE,CAAC;QAEhB,OAAO,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACzB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;SAC5B,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC9B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,qBAAqB;aAC5C,kBAAkB,CAAC,OAAO,CAAC;aAC3B,QAAQ,CAAC,0BAA0B,EAAE,QAAQ,CAAC;aAC9C,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC;aAC5B,SAAS,CAAC,kBAAkB,EAAE,aAAa,CAAC;aAC5C,KAAK,CAAC,yBAAyB,EAAE,EAAE,MAAM,EAAE,yCAAY,CAAC,MAAM,EAAE,CAAC;aACjE,OAAO,CAAC,UAAU,CAAC;aACnB,UAAU,CAAC,YAAY,CAAC;aACxB,OAAO,CAAC,aAAa,EAAE,MAAM,CAAC;aAC9B,KAAK,CAAC,EAAE,CAAC;aACT,UAAU,EAAE,CAAC;QAEhB,OAAO,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACzB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC;SACxC,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,YAAY,CAAC,IAAW;QAC9B,gCAAgC;QAChC,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;QAC3C,MAAM,UAAU,GAAG;YACjB,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;YACjB,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAChB,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CACnB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAClC,CAAC,IAAI,CAAC,GAAG,CAAC,CACZ;SACF,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEb,OAAO;YACL,MAAM,EAAE,KAAK;YACb,OAAO,EAAE,UAAU;YACnB,QAAQ,EAAE,uBAAuB,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;SAC9E,CAAC;IACJ,CAAC;IAEO,aAAa,CAAC,OAA6B;QACjD,6BAA6B;QAC7B,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACzC,IAAI,EAAE,cAAc;YACpB,EAAE,EAAE,iBAAiB,MAAM,CAAC,EAAE,EAAE;YAChC,OAAO,EAAE,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE;YACvC,QAAQ,EAAE,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE;YACxC,IAAI,EAAE,MAAM,CAAC,KAAK;YAClB,WAAW,EAAE,MAAM,CAAC,WAAW;YAC/B,MAAM,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC;YAC3B,qCAAqC;SACtC,CAAC,CAAC,CAAC;QAEJ,OAAO;YACL,MAAM,EAAE,MAAM;YACd,YAAY,EAAE,KAAK;YACnB,IAAI,EAAE,QAAQ;YACd,EAAE,EAAE,WAAW,IAAI,CAAC,GAAG,EAAE,EAAE;YAC3B,OAAO,EAAE,WAAW;SACrB,CAAC;IACJ,CAAC;CACF,CAAA;AArjBY,8DAAyB;oCAAzB,yBAAyB;IADrC,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,+CAAkB,CAAC,CAAA;IAEpC,WAAA,IAAA,0BAAgB,EAAC,sDAAqB,CAAC,CAAA;IAEvC,WAAA,IAAA,0BAAgB,EAAC,iCAAW,CAAC,CAAA;IAE7B,WAAA,IAAA,0BAAgB,EAAC,uCAAc,CAAC,CAAA;yDALc,oBAAU,oBAAV,oBAAU,oDAEzB,oBAAU,oBAAV,oBAAU,oDAEF,oBAAU,oBAAV,oBAAU,oDAEP,oBAAU,oBAAV,oBAAU,oDACrB,8BAAa,oBAAb,8BAAa,oDACd,4BAAY,oBAAZ,4BAAY,oDACL,0CAAmB,oBAAnB,0CAAmB,oDACzB,sBAAa,oBAAb,sBAAa;GAfpC,yBAAyB,CAqjBrC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\application\\services\\threat-intelligence.service.ts"], "sourcesContent": ["import { Injectable, NotFoundException, ConflictException, Logger } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository, FindManyOptions, Like, In, Between, Not } from 'typeorm';\r\nimport { ConfigService } from '@nestjs/config';\r\n\r\nimport { ThreatIntelligence, ThreatStatus, ThreatType, ThreatSeverity, ThreatConfidence } from '../../domain/entities/threat-intelligence.entity';\r\nimport { IndicatorOfCompromise } from '../../domain/entities/indicator-of-compromise.entity';\r\nimport { ThreatActor } from '../../domain/entities/threat-actor.entity';\r\nimport { ThreatCampaign } from '../../domain/entities/threat-campaign.entity';\r\nimport { LoggerService } from '../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../infrastructure/logging/audit/audit.service';\r\nimport { NotificationService } from '../../../../infrastructure/notification/notification.service';\r\n\r\n/**\r\n * Search criteria for threat intelligence\r\n */\r\nexport interface ThreatIntelligenceSearchCriteria {\r\n  page?: number;\r\n  limit?: number;\r\n  threatTypes?: ThreatType[];\r\n  severities?: ThreatSeverity[];\r\n  statuses?: ThreatStatus[];\r\n  confidenceLevels?: ThreatConfidence[];\r\n  tags?: string[];\r\n  searchText?: string;\r\n  dateRange?: {\r\n    startDate: Date;\r\n    endDate: Date;\r\n  };\r\n  riskScoreMin?: number;\r\n  riskScoreMax?: number;\r\n  threatActorId?: string;\r\n  campaignId?: string;\r\n  dataSource?: string;\r\n  includeExpired?: boolean;\r\n  sortBy?: string;\r\n  sortOrder?: 'ASC' | 'DESC';\r\n}\r\n\r\n/**\r\n * Threat intelligence correlation criteria\r\n */\r\nexport interface CorrelationCriteria {\r\n  threatIntelligenceId: string;\r\n  correlationTypes: ('actor' | 'campaign' | 'indicators' | 'techniques' | 'infrastructure')[];\r\n  timeWindow?: number; // days\r\n  confidenceThreshold?: number;\r\n}\r\n\r\n/**\r\n * Service for managing threat intelligence data\r\n * Provides comprehensive threat intelligence operations including search, correlation, and analysis\r\n */\r\n@Injectable()\r\nexport class ThreatIntelligenceService {\r\n  private readonly logger = new Logger(ThreatIntelligenceService.name);\r\n\r\n  constructor(\r\n    @InjectRepository(ThreatIntelligence)\r\n    private readonly threatIntelligenceRepository: Repository<ThreatIntelligence>,\r\n    @InjectRepository(IndicatorOfCompromise)\r\n    private readonly iocRepository: Repository<IndicatorOfCompromise>,\r\n    @InjectRepository(ThreatActor)\r\n    private readonly threatActorRepository: Repository<ThreatActor>,\r\n    @InjectRepository(ThreatCampaign)\r\n    private readonly threatCampaignRepository: Repository<ThreatCampaign>,\r\n    private readonly loggerService: LoggerService,\r\n    private readonly auditService: AuditService,\r\n    private readonly notificationService: NotificationService,\r\n    private readonly configService: ConfigService,\r\n  ) {}\r\n\r\n  /**\r\n   * Create new threat intelligence\r\n   */\r\n  async createThreatIntelligence(\r\n    threatData: Partial<ThreatIntelligence>,\r\n    userId: string,\r\n  ): Promise<ThreatIntelligence> {\r\n    this.logger.debug('Creating threat intelligence', {\r\n      title: threatData.title,\r\n      threatType: threatData.threatType,\r\n      userId,\r\n    });\r\n\r\n    try {\r\n      // Check for duplicates based on title and data source\r\n      if (threatData.title && threatData.dataSource) {\r\n        const existing = await this.threatIntelligenceRepository.findOne({\r\n          where: {\r\n            title: threatData.title,\r\n            dataSource: threatData.dataSource as any,\r\n          },\r\n        });\r\n\r\n        if (existing) {\r\n          throw new ConflictException('Threat intelligence with this title and data source already exists');\r\n        }\r\n      }\r\n\r\n      // Set default values\r\n      const threat = this.threatIntelligenceRepository.create({\r\n        ...threatData,\r\n        status: threatData.status || ThreatStatus.ACTIVE,\r\n        confidence: threatData.confidence || ThreatConfidence.MEDIUM,\r\n        severity: threatData.severity || ThreatSeverity.MEDIUM,\r\n        firstSeen: threatData.firstSeen || new Date(),\r\n        observationCount: 0,\r\n        isIoc: false,\r\n        isAttributed: !!threatData.threatActorId,\r\n      });\r\n\r\n      // Calculate initial risk score\r\n      threat.calculateRiskScore();\r\n\r\n      const savedThreat = await this.threatIntelligenceRepository.save(threat);\r\n\r\n      // Send notification for critical threats\r\n      if (savedThreat.severity === ThreatSeverity.CRITICAL) {\r\n        await this.notificationService.sendCriticalThreatAlert({\r\n          threatId: savedThreat.id,\r\n          title: savedThreat.title,\r\n          threatType: savedThreat.threatType,\r\n          severity: savedThreat.severity,\r\n          dataSource: savedThreat.dataSource.name,\r\n        });\r\n      }\r\n\r\n      // Audit log\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'create',\r\n        'threat_intelligence',\r\n        savedThreat.id,\r\n        {\r\n          title: savedThreat.title,\r\n          threatType: savedThreat.threatType,\r\n          severity: savedThreat.severity,\r\n          dataSource: savedThreat.dataSource.name,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Threat intelligence created successfully', {\r\n        threatId: savedThreat.id,\r\n        title: savedThreat.title,\r\n        userId,\r\n      });\r\n\r\n      return savedThreat;\r\n    } catch (error) {\r\n      this.loggerService.error('Failed to create threat intelligence', {\r\n        error: error.message,\r\n        threatData: {\r\n          title: threatData.title,\r\n          threatType: threatData.threatType,\r\n        },\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get threat intelligence by ID\r\n   */\r\n  async getThreatIntelligenceById(id: string): Promise<ThreatIntelligence> {\r\n    const threat = await this.threatIntelligenceRepository.findOne({\r\n      where: { id },\r\n      relations: ['indicators', 'threatActor', 'threatCampaign'],\r\n    });\r\n\r\n    if (!threat) {\r\n      throw new NotFoundException('Threat intelligence not found');\r\n    }\r\n\r\n    return threat;\r\n  }\r\n\r\n  /**\r\n   * Search threat intelligence with advanced filtering\r\n   */\r\n  async searchThreatIntelligence(\r\n    criteria: ThreatIntelligenceSearchCriteria,\r\n  ): Promise<{\r\n    threats: ThreatIntelligence[];\r\n    total: number;\r\n    page: number;\r\n    totalPages: number;\r\n  }> {\r\n    const {\r\n      page = 1,\r\n      limit = 50,\r\n      threatTypes,\r\n      severities,\r\n      statuses,\r\n      confidenceLevels,\r\n      tags,\r\n      searchText,\r\n      dateRange,\r\n      riskScoreMin,\r\n      riskScoreMax,\r\n      threatActorId,\r\n      campaignId,\r\n      dataSource,\r\n      includeExpired = false,\r\n      sortBy = 'firstSeen',\r\n      sortOrder = 'DESC',\r\n    } = criteria;\r\n\r\n    const queryBuilder = this.threatIntelligenceRepository.createQueryBuilder('threat')\r\n      .leftJoinAndSelect('threat.threatActor', 'actor')\r\n      .leftJoinAndSelect('threat.threatCampaign', 'campaign')\r\n      .leftJoinAndSelect('threat.indicators', 'indicators');\r\n\r\n    // Apply filters\r\n    if (threatTypes && threatTypes.length > 0) {\r\n      queryBuilder.andWhere('threat.threatType IN (:...threatTypes)', { threatTypes });\r\n    }\r\n\r\n    if (severities && severities.length > 0) {\r\n      queryBuilder.andWhere('threat.severity IN (:...severities)', { severities });\r\n    }\r\n\r\n    if (statuses && statuses.length > 0) {\r\n      queryBuilder.andWhere('threat.status IN (:...statuses)', { statuses });\r\n    }\r\n\r\n    if (confidenceLevels && confidenceLevels.length > 0) {\r\n      queryBuilder.andWhere('threat.confidence IN (:...confidenceLevels)', { confidenceLevels });\r\n    }\r\n\r\n    if (tags && tags.length > 0) {\r\n      queryBuilder.andWhere('threat.tags && :tags', { tags });\r\n    }\r\n\r\n    if (searchText) {\r\n      queryBuilder.andWhere(\r\n        '(LOWER(threat.title) LIKE LOWER(:searchText) OR LOWER(threat.description) LIKE LOWER(:searchText))',\r\n        { searchText: `%${searchText}%` },\r\n      );\r\n    }\r\n\r\n    if (dateRange) {\r\n      queryBuilder.andWhere('threat.firstSeen BETWEEN :startDate AND :endDate', {\r\n        startDate: dateRange.startDate,\r\n        endDate: dateRange.endDate,\r\n      });\r\n    }\r\n\r\n    if (riskScoreMin !== undefined) {\r\n      queryBuilder.andWhere('threat.riskScore >= :riskScoreMin', { riskScoreMin });\r\n    }\r\n\r\n    if (riskScoreMax !== undefined) {\r\n      queryBuilder.andWhere('threat.riskScore <= :riskScoreMax', { riskScoreMax });\r\n    }\r\n\r\n    if (threatActorId) {\r\n      queryBuilder.andWhere('threat.threatActorId = :threatActorId', { threatActorId });\r\n    }\r\n\r\n    if (campaignId) {\r\n      queryBuilder.andWhere('threat.threatCampaignId = :campaignId', { campaignId });\r\n    }\r\n\r\n    if (dataSource) {\r\n      queryBuilder.andWhere(\"threat.dataSource->>'name' = :dataSource\", { dataSource });\r\n    }\r\n\r\n    if (!includeExpired) {\r\n      queryBuilder.andWhere(\r\n        '(threat.expiresAt IS NULL OR threat.expiresAt > :now)',\r\n        { now: new Date() },\r\n      );\r\n    }\r\n\r\n    // Apply sorting\r\n    if (['firstSeen', 'lastSeen', 'riskScore', 'severity', 'observationCount'].includes(sortBy)) {\r\n      queryBuilder.orderBy(`threat.${sortBy}`, sortOrder);\r\n    } else {\r\n      queryBuilder.orderBy('threat.firstSeen', 'DESC');\r\n    }\r\n\r\n    // Add secondary sort for consistency\r\n    queryBuilder.addOrderBy('threat.id', 'ASC');\r\n\r\n    // Apply pagination\r\n    const offset = (page - 1) * limit;\r\n    queryBuilder.skip(offset).take(limit);\r\n\r\n    const [threats, total] = await queryBuilder.getManyAndCount();\r\n    const totalPages = Math.ceil(total / limit);\r\n\r\n    return {\r\n      threats,\r\n      total,\r\n      page,\r\n      totalPages,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Update threat intelligence\r\n   */\r\n  async updateThreatIntelligence(\r\n    id: string,\r\n    updates: Partial<ThreatIntelligence>,\r\n    userId: string,\r\n  ): Promise<ThreatIntelligence> {\r\n    const threat = await this.getThreatIntelligenceById(id);\r\n\r\n    // Track changes for audit\r\n    const changes = {};\r\n    Object.keys(updates).forEach(key => {\r\n      if (threat[key] !== updates[key]) {\r\n        changes[key] = { from: threat[key], to: updates[key] };\r\n      }\r\n    });\r\n\r\n    Object.assign(threat, updates);\r\n\r\n    // Recalculate risk score if relevant fields changed\r\n    if (updates.severity || updates.confidence || updates.observationCount) {\r\n      threat.calculateRiskScore();\r\n    }\r\n\r\n    const updatedThreat = await this.threatIntelligenceRepository.save(threat);\r\n\r\n    // Audit log\r\n    await this.auditService.logUserAction(\r\n      userId,\r\n      'update',\r\n      'threat_intelligence',\r\n      id,\r\n      {\r\n        title: threat.title,\r\n        changes,\r\n      },\r\n    );\r\n\r\n    this.logger.log('Threat intelligence updated', {\r\n      threatId: id,\r\n      changes: Object.keys(changes),\r\n      userId,\r\n    });\r\n\r\n    return updatedThreat;\r\n  }\r\n\r\n  /**\r\n   * Delete threat intelligence\r\n   */\r\n  async deleteThreatIntelligence(id: string, userId: string): Promise<void> {\r\n    const threat = await this.getThreatIntelligenceById(id);\r\n\r\n    await this.threatIntelligenceRepository.remove(threat);\r\n\r\n    // Audit log\r\n    await this.auditService.logUserAction(\r\n      userId,\r\n      'delete',\r\n      'threat_intelligence',\r\n      id,\r\n      {\r\n        title: threat.title,\r\n        threatType: threat.threatType,\r\n      },\r\n    );\r\n\r\n    this.logger.log('Threat intelligence deleted', {\r\n      threatId: id,\r\n      title: threat.title,\r\n      userId,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Record observation of threat intelligence\r\n   */\r\n  async recordObservation(id: string, context?: any): Promise<ThreatIntelligence> {\r\n    const threat = await this.getThreatIntelligenceById(id);\r\n\r\n    threat.recordObservation();\r\n    threat.calculateRiskScore();\r\n\r\n    if (context) {\r\n      threat.customAttributes = {\r\n        ...threat.customAttributes,\r\n        lastObservationContext: context,\r\n      };\r\n    }\r\n\r\n    return await this.threatIntelligenceRepository.save(threat);\r\n  }\r\n\r\n  /**\r\n   * Find related threat intelligence\r\n   */\r\n  async findRelatedThreats(\r\n    criteria: CorrelationCriteria,\r\n  ): Promise<{\r\n    relatedThreats: ThreatIntelligence[];\r\n    correlationScore: number;\r\n    correlationType: string;\r\n  }[]> {\r\n    const baseThreat = await this.getThreatIntelligenceById(criteria.threatIntelligenceId);\r\n    const results = [];\r\n\r\n    // Correlate by threat actor\r\n    if (criteria.correlationTypes.includes('actor') && baseThreat.threatActorId) {\r\n      const actorThreats = await this.threatIntelligenceRepository.find({\r\n        where: {\r\n          threatActorId: baseThreat.threatActorId,\r\n          id: Not(baseThreat.id),\r\n        },\r\n        relations: ['threatActor'],\r\n        take: 10,\r\n      });\r\n\r\n      results.push({\r\n        relatedThreats: actorThreats,\r\n        correlationScore: 0.9,\r\n        correlationType: 'threat_actor',\r\n      });\r\n    }\r\n\r\n    // Correlate by campaign\r\n    if (criteria.correlationTypes.includes('campaign') && baseThreat.threatCampaignId) {\r\n      const campaignThreats = await this.threatIntelligenceRepository.find({\r\n        where: {\r\n          threatCampaignId: baseThreat.threatCampaignId,\r\n          id: Not(baseThreat.id),\r\n        },\r\n        relations: ['threatCampaign'],\r\n        take: 10,\r\n      });\r\n\r\n      results.push({\r\n        relatedThreats: campaignThreats,\r\n        correlationScore: 0.8,\r\n        correlationType: 'campaign',\r\n      });\r\n    }\r\n\r\n    // Correlate by tags\r\n    if (baseThreat.tags && baseThreat.tags.length > 0) {\r\n      const tagThreats = await this.threatIntelligenceRepository\r\n        .createQueryBuilder('threat')\r\n        .where('threat.tags && :tags', { tags: baseThreat.tags })\r\n        .andWhere('threat.id != :id', { id: baseThreat.id })\r\n        .take(10)\r\n        .getMany();\r\n\r\n      if (tagThreats.length > 0) {\r\n        results.push({\r\n          relatedThreats: tagThreats,\r\n          correlationScore: 0.6,\r\n          correlationType: 'tags',\r\n        });\r\n      }\r\n    }\r\n\r\n    return results;\r\n  }\r\n\r\n  /**\r\n   * Get threat intelligence dashboard data\r\n   */\r\n  async getDashboardData(): Promise<any> {\r\n    const [\r\n      totalThreats,\r\n      activeThreats,\r\n      criticalThreats,\r\n      recentThreats,\r\n      topThreatTypes,\r\n      topActors,\r\n    ] = await Promise.all([\r\n      this.threatIntelligenceRepository.count(),\r\n      this.threatIntelligenceRepository.count({ where: { status: ThreatStatus.ACTIVE } }),\r\n      this.threatIntelligenceRepository.count({ \r\n        where: { \r\n          status: ThreatStatus.ACTIVE,\r\n          severity: ThreatSeverity.CRITICAL,\r\n        },\r\n      }),\r\n      this.threatIntelligenceRepository.count({\r\n        where: {\r\n          firstSeen: Between(\r\n            new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),\r\n            new Date(),\r\n          ),\r\n        },\r\n      }),\r\n      this.getThreatTypeDistribution(),\r\n      this.getTopThreatActors(),\r\n    ]);\r\n\r\n    return {\r\n      summary: {\r\n        total: totalThreats,\r\n        active: activeThreats,\r\n        critical: criticalThreats,\r\n        recent: recentThreats,\r\n      },\r\n      distribution: {\r\n        threatTypes: topThreatTypes,\r\n        topActors,\r\n      },\r\n      timestamp: new Date(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Export threat intelligence for reporting\r\n   */\r\n  async exportThreatIntelligence(\r\n    criteria: ThreatIntelligenceSearchCriteria,\r\n    format: 'json' | 'csv' | 'stix',\r\n  ): Promise<any> {\r\n    const { threats } = await this.searchThreatIntelligence({\r\n      ...criteria,\r\n      limit: 10000, // Large limit for export\r\n    });\r\n\r\n    const exportData = threats.map(threat => threat.exportForReporting());\r\n\r\n    switch (format) {\r\n      case 'json':\r\n        return {\r\n          format: 'json',\r\n          data: exportData,\r\n          exportedAt: new Date(),\r\n          count: exportData.length,\r\n        };\r\n      case 'csv':\r\n        return this.convertToCSV(exportData);\r\n      case 'stix':\r\n        return this.convertToSTIX(threats);\r\n      default:\r\n        throw new Error(`Unsupported export format: ${format}`);\r\n    }\r\n  }\r\n\r\n  // Private helper methods\r\n  private async getThreatTypeDistribution(): Promise<any[]> {\r\n    const result = await this.threatIntelligenceRepository\r\n      .createQueryBuilder('threat')\r\n      .select('threat.threatType', 'type')\r\n      .addSelect('COUNT(*)', 'count')\r\n      .where('threat.status = :status', { status: ThreatStatus.ACTIVE })\r\n      .groupBy('threat.threatType')\r\n      .orderBy('count', 'DESC')\r\n      .getRawMany();\r\n\r\n    return result.map(item => ({\r\n      type: item.type,\r\n      count: parseInt(item.count),\r\n    }));\r\n  }\r\n\r\n  private async getTopThreatActors(): Promise<any[]> {\r\n    const result = await this.threatActorRepository\r\n      .createQueryBuilder('actor')\r\n      .leftJoin('actor.threatIntelligence', 'threat')\r\n      .select('actor.name', 'name')\r\n      .addSelect('COUNT(threat.id)', 'threatCount')\r\n      .where('threat.status = :status', { status: ThreatStatus.ACTIVE })\r\n      .groupBy('actor.id')\r\n      .addGroupBy('actor.name')\r\n      .orderBy('threatCount', 'DESC')\r\n      .limit(10)\r\n      .getRawMany();\r\n\r\n    return result.map(item => ({\r\n      name: item.name,\r\n      threatCount: parseInt(item.threatCount),\r\n    }));\r\n  }\r\n\r\n  private convertToCSV(data: any[]): any {\r\n    // CSV conversion implementation\r\n    const headers = Object.keys(data[0] || {});\r\n    const csvContent = [\r\n      headers.join(','),\r\n      ...data.map(row => \r\n        headers.map(header => \r\n          JSON.stringify(row[header] || '')\r\n        ).join(',')\r\n      ),\r\n    ].join('\\n');\r\n\r\n    return {\r\n      format: 'csv',\r\n      content: csvContent,\r\n      filename: `threat_intelligence_${new Date().toISOString().split('T')[0]}.csv`,\r\n    };\r\n  }\r\n\r\n  private convertToSTIX(threats: ThreatIntelligence[]): any {\r\n    // STIX 2.1 format conversion\r\n    const stixObjects = threats.map(threat => ({\r\n      type: 'threat-actor',\r\n      id: `threat-actor--${threat.id}`,\r\n      created: threat.createdAt.toISOString(),\r\n      modified: threat.updatedAt.toISOString(),\r\n      name: threat.title,\r\n      description: threat.description,\r\n      labels: [threat.threatType],\r\n      // Add more STIX properties as needed\r\n    }));\r\n\r\n    return {\r\n      format: 'stix',\r\n      spec_version: '2.1',\r\n      type: 'bundle',\r\n      id: `bundle--${Date.now()}`,\r\n      objects: stixObjects,\r\n    };\r\n  }\r\n}\r\n"], "version": 3}