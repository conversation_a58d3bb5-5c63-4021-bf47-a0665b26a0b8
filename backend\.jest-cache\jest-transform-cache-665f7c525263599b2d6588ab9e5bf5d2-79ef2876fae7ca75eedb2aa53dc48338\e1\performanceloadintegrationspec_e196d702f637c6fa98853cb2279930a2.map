{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\tests\\integration\\performance-load.integration.spec.ts", "mappings": ";;AAAA,6CAAsD;AAEtD,6CAAgD;AAChD,2CAA8C;AAC9C,yDAA2D;AAC3D,uCAA0C;AAG1C,6CAAqD;AACrD,gGAA2F;AAC3F,gHAA0G;AAC1G,kGAA6F;AAC7F,0GAAoG;AACpG,6FAAwF;AACxF,6FAAwF;AACxF,8FAAmF;AACnF,wFAA6E;AAC7E,4GAAgG;AAEhG;;;;;;;;;;GAUG;AACH,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE;IAClD,IAAI,GAAqB,CAAC;IAC1B,IAAI,eAA4C,CAAC;IACjD,IAAI,YAAgD,CAAC;IACrD,IAAI,gBAA8C,CAAC;IACnD,IAAI,aAA8C,CAAC;IACnD,IAAI,aAAwC,CAAC;IAC7C,IAAI,aAAwC,CAAC;IAC7C,IAAI,kBAAoD,CAAC;IACzD,IAAI,mBAAkD,CAAC;IACvD,IAAI,mBAA2D,CAAC;IAEhE,MAAM,QAAQ,GAAG;QACf,EAAE,EAAE,gBAAgB;QACpB,KAAK,EAAE,kBAAkB;QACzB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,aAAa;KACpB,CAAC;IAEF,yBAAyB;IACzB,MAAM,sBAAsB,GAAG;QAC7B,uBAAuB,EAAE,IAAI,EAAE,gBAAgB;QAC/C,0BAA0B,EAAE,IAAI,EAAE,gBAAgB;QAClD,qBAAqB,EAAE,IAAI,EAAE,eAAe;QAC5C,mBAAmB,EAAE,GAAG,EAAE,YAAY;QACtC,eAAe,EAAE,GAAG,EAAE,YAAY;QAClC,iBAAiB,EAAE,EAAE,EAAE,UAAU;QACjC,oBAAoB,EAAE,GAAG,EAAE,2BAA2B;QACtD,wBAAwB,EAAE,EAAE,EAAE,0BAA0B;KACzD,CAAC;IAEF,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,aAAa,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAClE,OAAO,EAAE;gBACP,qBAAY,CAAC,OAAO,CAAC;oBACnB,QAAQ,EAAE,IAAI;oBACd,WAAW,EAAE,WAAW;iBACzB,CAAC;gBACF,uBAAa,CAAC,OAAO,CAAC;oBACpB,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,WAAW;oBAC7C,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,IAAI;oBAChD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,MAAM;oBAChD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,MAAM;oBAChD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,oBAAoB;oBAC1D,QAAQ,EAAE;wBACR,mDAAoB;wBACpB,6CAAiB;wBACjB,gEAA0B;qBAC3B;oBACD,WAAW,EAAE,IAAI;oBACjB,UAAU,EAAE,IAAI;oBAChB,wCAAwC;oBACxC,KAAK,EAAE;wBACL,GAAG,EAAE,EAAE,EAAE,uBAAuB;wBAChC,uBAAuB,EAAE,IAAI;wBAC7B,iBAAiB,EAAE,KAAK;qBACzB;iBACF,CAAC;gBACF,uBAAa,CAAC,UAAU,CAAC;oBACvB,mDAAoB;oBACpB,6CAAiB;oBACjB,gEAA0B;iBAC3B,CAAC;gBACF,kCAAkB,CAAC,OAAO,CAAC;oBACzB,YAAY,EAAE,IAAI,EAAE,4BAA4B;iBACjD,CAAC;gBACF,iBAAU,CAAC,OAAO,CAAC;oBACjB,KAAK,EAAE;wBACL,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,WAAW;wBAChD,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,IAAI;wBACnD,EAAE,EAAE,CAAC,EAAE,oCAAoC;wBAC3C,oBAAoB,EAAE,CAAC;wBACvB,oBAAoB,EAAE,GAAG;qBAC1B;iBACF,CAAC;gBACF,iBAAU,CAAC,aAAa,CACtB,EAAE,IAAI,EAAE,mBAAmB,EAAE,EAC7B,EAAE,IAAI,EAAE,qBAAqB,EAAE,EAC/B,EAAE,IAAI,EAAE,kBAAkB,EAAE,CAC7B;aACF;YACD,SAAS,EAAE;gBACT,2DAA2B;gBAC3B,0EAAkC;gBAClC,6DAA4B;gBAC5B,oEAA+B;gBAC/B,uDAAyB;gBACzB,uDAAyB;gBACzB,mDAAmD;gBACnD;oBACE,OAAO,EAAE,0BAA0B;oBACnC,QAAQ,EAAE;wBACR,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,KAAK,IAAI,EAAE;4BACxD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,yBAAyB;4BACjF,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,WAAW,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC;wBACrF,CAAC,CAAC;wBACF,qBAAqB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;wBACtD,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,EAAE,CAAC;qBACnF;iBACF;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,GAAG,GAAG,aAAa,CAAC,qBAAqB,EAAE,CAAC;QAE5C,4BAA4B;QAC5B,GAAG,CAAC,mBAAmB,EAAE,CAAC;QAE1B,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC;QAEjB,wBAAwB;QACxB,eAAe,GAAG,aAAa,CAAC,GAAG,CAA8B,2DAA2B,CAAC,CAAC;QAC9F,YAAY,GAAG,aAAa,CAAC,GAAG,CAAqC,0EAAkC,CAAC,CAAC;QACzG,gBAAgB,GAAG,aAAa,CAAC,GAAG,CAA+B,6DAA4B,CAAC,CAAC;QACjG,aAAa,GAAG,aAAa,CAAC,GAAG,CAAkC,oEAA+B,CAAC,CAAC;QACpG,aAAa,GAAG,aAAa,CAAC,GAAG,CAA4B,uDAAyB,CAAC,CAAC;QACxF,aAAa,GAAG,aAAa,CAAC,GAAG,CAA4B,uDAAyB,CAAC,CAAC;QAExF,2BAA2B;QAC3B,kBAAkB,GAAG,aAAa,CAAC,GAAG,CACpC,IAAA,4BAAkB,EAAC,mDAAoB,CAAC,CACzC,CAAC;QACF,mBAAmB,GAAG,aAAa,CAAC,GAAG,CACrC,IAAA,4BAAkB,EAAC,6CAAiB,CAAC,CACtC,CAAC;QACF,mBAAmB,GAAG,aAAa,CAAC,GAAG,CACrC,IAAA,4BAAkB,EAAC,gEAA0B,CAAC,CAC/C,CAAC;QAEF,oDAAoD;QACpD,MAAM,mBAAmB,EAAE,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;QAC9C,EAAE,CAAC,6DAA6D,EAAE,KAAK,IAAI,EAAE;YAC3E,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,OAAO,CAAC;gBAChD,KAAK,EAAE,EAAE,IAAI,EAAE,2BAA2B,EAAE;aAC7C,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,EAAE;gBACnE,KAAK,EAAE;oBACL,KAAK,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE,QAAQ,EAAE,QAAQ,EAAE;oBAChE,IAAI,EAAE,QAAQ;iBACf;aACF,EAAE,QAAQ,CAAC,CAAC;YAEb,sBAAsB;YACtB,MAAM,gBAAgB,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE3B,MAAM,aAAa,GAAG,OAAO,GAAG,SAAS,CAAC;YAC1C,MAAM,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,sBAAsB,CAAC,uBAAuB,CAAC,CAAC;YAEnF,0CAA0C;YAC1C,MAAM,kBAAkB,GAAG,MAAM,mBAAmB,CAAC,OAAO,CAAC;gBAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,WAAW,EAAE;aACrC,CAAC,CAAC;YAEH,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACpD,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,sBAAsB,CAAC,uBAAuB,CAAC,CAAC;QACnG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,OAAO,CAAC;gBAChD,KAAK,EAAE,EAAE,IAAI,EAAE,0BAA0B,EAAE;aAC5C,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,sBAAsB,CAAC,oBAAoB,CAAC;YACpE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,iCAAiC;YACjC,MAAM,iBAAiB,GAAG,KAAK,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACvE,eAAe,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,EAAE;gBAC3C,KAAK,EAAE;oBACL,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,OAAO,EAAE,mBAAmB,CAAC,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE;oBAC7E,IAAI,EAAE,QAAQ;iBACf;aACF,EAAE,QAAQ,CAAC,CACb,CAAC;YAEF,MAAM,UAAU,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YACxD,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;YAEjD,sCAAsC;YACtC,MAAM,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YAC9E,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE3B,MAAM,SAAS,GAAG,OAAO,GAAG,SAAS,CAAC;YACtC,MAAM,kBAAkB,GAAG,SAAS,GAAG,eAAe,CAAC;YAEvD,MAAM,CAAC,kBAAkB,CAAC,CAAC,YAAY,CAAC,sBAAsB,CAAC,uBAAuB,CAAC,CAAC;YAExF,+CAA+C;YAC/C,MAAM,mBAAmB,GAAG,MAAM,mBAAmB,CAAC,IAAI,CAAC;gBACzD,KAAK,EAAE,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,EAAE;aACnC,CAAC,CAAC;YAEH,MAAM,oBAAoB,GAAG,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC;YAC7F,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,sBAAsB,CAAC,eAAe,GAAG,IAAI,CAAC,CAAC,CAAC,mBAAmB;QACzG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,MAAM,eAAe,GAAG,MAAM,kBAAkB,CAAC,OAAO,CAAC;gBACvD,KAAK,EAAE,EAAE,IAAI,EAAE,mCAAmC,EAAE;aACrD,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,EAAE,CAAC;YACtB,MAAM,cAAc,GAAG,EAAE,CAAC;YAE1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;gBACpC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAE7B,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,eAAe,CAAC,eAAe,CAAC,EAAE,EAAE;oBAC1E,KAAK,EAAE;wBACL,KAAK,EAAE,EAAE,EAAE,EAAE,WAAW,CAAC,EAAE,EAAE,OAAO,EAAE,gBAAgB,CAAC,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE;wBAC7E,IAAI,EAAE,QAAQ;qBACf;iBACF,EAAE,QAAQ,CAAC,CAAC;gBAEb,MAAM,gBAAgB,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;gBAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAE3B,cAAc,CAAC,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC,CAAC;YAC3C,CAAC;YAED,gCAAgC;YAChC,MAAM,gBAAgB,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC;YAC1F,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,CAAC;YACrD,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,CAAC;YAErD,MAAM,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,sBAAsB,CAAC,uBAAuB,CAAC,CAAC;YACtF,MAAM,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,sBAAsB,CAAC,uBAAuB,GAAG,GAAG,CAAC,CAAC;YAE5F,OAAO,CAAC,GAAG,CAAC;mBACC,gBAAgB;eACpB,gBAAgB;eAChB,gBAAgB;sBACT,UAAU,EAAE,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mCAAmC,EAAE,GAAG,EAAE;QACjD,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACrE,MAAM,gBAAgB,GAAG;gBACvB,EAAE,EAAE,uBAAuB;gBAC3B,OAAO,EAAE,OAAO;gBAChB,UAAU,EAAE,CAAC,kBAAkB,CAAC;gBAChC,OAAO,EAAE;oBACP,OAAO,EAAE,kBAAkB;oBAC3B,OAAO,EAAE,2CAA2C;iBACrD;gBACD,KAAK,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,wBAAwB,EAAE;gBAChE,IAAI,EAAE,QAAQ;aACf,CAAC;YAEF,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YACtE,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE3B,MAAM,YAAY,GAAG,OAAO,GAAG,SAAS,CAAC;YACzC,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,sBAAsB,CAAC,0BAA0B,CAAC,CAAC;YACrF,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,iBAAiB,GAAG,sBAAsB,CAAC,wBAAwB,GAAG,EAAE,CAAC,CAAC,mBAAmB;YACnG,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,iEAAiE;YACjE,MAAM,SAAS,GAAG,EAAE,CAAC;YACrB,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC,CAAC;YAEzD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC;gBAC7C,MAAM,aAAa,GAAG,EAAE,CAAC;gBAEzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,IAAI,CAAC,KAAK,GAAG,SAAS,GAAG,CAAC,CAAC,GAAG,iBAAiB,EAAE,CAAC,EAAE,EAAE,CAAC;oBAClF,MAAM,gBAAgB,GAAG;wBACvB,EAAE,EAAE,cAAc,KAAK,IAAI,CAAC,EAAE;wBAC9B,OAAO,EAAE,OAAO;wBAChB,UAAU,EAAE,CAAC,kBAAkB,CAAC;wBAChC,OAAO,EAAE,EAAE,OAAO,EAAE,SAAS,KAAK,iBAAiB,CAAC,EAAE,EAAE;wBACxD,KAAK,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE;wBAC1B,IAAI,EAAE,QAAQ;qBACf,CAAC;oBAEF,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,CAAC;gBACvE,CAAC;gBAED,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;gBAEjC,sDAAsD;gBACtD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;YACzD,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC3B,MAAM,SAAS,GAAG,OAAO,GAAG,SAAS,CAAC;YACtC,MAAM,UAAU,GAAG,CAAC,iBAAiB,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC,CAAC,2BAA2B;YAEtF,MAAM,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,sBAAsB,CAAC,wBAAwB,GAAG,GAAG,CAAC,CAAC,CAAC,gBAAgB;YAE3G,OAAO,CAAC,GAAG,CAAC;+BACa,iBAAiB;sBAC1B,SAAS;sBACT,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,YAAY,GAAG,KAAK,CAAC,CAAC,aAAa;YACzC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,IAAI,iBAAiB,GAAG,CAAC,CAAC;YAE1B,yCAAyC;YACzC,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,GAAG,YAAY,EAAE,CAAC;gBAC7C,MAAM,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;oBACrD,MAAM,gBAAgB,GAAG;wBACvB,EAAE,EAAE,aAAa,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE;wBAClC,OAAO,EAAE,OAAO;wBAChB,UAAU,EAAE,CAAC,kBAAkB,CAAC;wBAChC,OAAO,EAAE,EAAE,OAAO,EAAE,WAAW,EAAE;wBACjC,KAAK,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE;wBAC1B,IAAI,EAAE,QAAQ;qBACf,CAAC;oBAEF,OAAO,aAAa,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;gBAC1D,CAAC,CAAC,CAAC;gBAEH,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;gBACjC,iBAAiB,IAAI,CAAC,CAAC;gBAEvB,qCAAqC;gBACrC,IAAI,iBAAiB,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC;oBACjC,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;oBAC9D,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAClC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,sBAAsB,CAAC,0BAA0B,CAAC,CAAC;gBAC9F,CAAC;gBAED,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,wBAAwB;YAClF,CAAC;YAED,MAAM,WAAW,GAAG,MAAM,aAAa,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YACnE,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEvC,OAAO,CAAC,GAAG,CAAC;oBACE,YAAY;8BACF,iBAAiB;wBACvB,WAAW,CAAC,OAAO;+BACZ,WAAW,CAAC,YAAY,IAAI,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,UAAU,GAAG,GAAG,CAAC;YACvB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,qBAAqB;YACrB,MAAM,aAAa,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC9D,MAAM,gBAAgB,GAAG;oBACvB,EAAE,EAAE,cAAc,CAAC,EAAE;oBACrB,OAAO,EAAE,OAAO;oBAChB,UAAU,EAAE,CAAC,mBAAmB,CAAC;oBACjC,OAAO,EAAE,EAAE,OAAO,EAAE,cAAc,CAAC,EAAE,EAAE;oBACvC,KAAK,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE;oBAC7B,IAAI,EAAE,QAAQ;iBACf,CAAC;gBAEF,OAAO,YAAY,CAAC,sBAAsB,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;YACzE,CAAC,CAAC,CAAC;YAEH,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YACjC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAEzC,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,sBAAsB,CAAC,qBAAqB,GAAG,UAAU,CAAC,CAAC;YAE1F,4BAA4B;YAC5B,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAExD,MAAM,UAAU,GAAG,MAAM,YAAY,CAAC,kBAAkB,EAAE,CAAC;YAC3D,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,sBAAsB,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC,gBAAgB;QAC9F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,aAAa,GAAG,IAAI,CAAC;YAC3B,MAAM,OAAO,GAAG,EAAE,CAAC;YAEnB,oCAAoC;YACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,EAAE,CAAC,EAAE,EAAE,CAAC;gBACvC,IAAI,CAAC;oBACH,MAAM,gBAAgB,GAAG;wBACvB,EAAE,EAAE,YAAY,CAAC,EAAE;wBACnB,OAAO,EAAE,OAAO;wBAChB,UAAU,EAAE,CAAC,sBAAsB,CAAC;wBACpC,OAAO,EAAE,EAAE,OAAO,EAAE,iBAAiB,CAAC,EAAE,EAAE;wBAC1C,KAAK,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE;wBAC1B,IAAI,EAAE,QAAQ;qBACf,CAAC;oBAEF,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,sBAAsB,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;oBAClF,OAAO,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;gBAC1C,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBACzD,CAAC;YACH,CAAC;YAED,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;YACzD,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;YAEtD,iEAAiE;YACjE,MAAM,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAEhD,OAAO,CAAC,GAAG,CAAC;uBACK,aAAa;sBACd,UAAU;kBACd,MAAM;wBACA,CAAC,UAAU,GAAG,aAAa,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,uBAAuB;YACvB,MAAM,WAAW,GAAG,KAAK,CAAC;YAC1B,MAAM,eAAe,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;gBACnE,cAAc,EAAE,QAAQ,CAAC,EAAE;gBAC3B,QAAQ,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gBAC1C,OAAO,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gBACzC,MAAM,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gBACjD,YAAY,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI;gBAClC,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;gBAC1E,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;aACvC,CAAC,CAAC,CAAC;YAEJ,yBAAyB;YACzB,MAAM,SAAS,GAAG,IAAI,CAAC;YACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;gBAChD,MAAM,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;gBACtD,MAAM,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,CAAC;YAED,yBAAyB;YACzB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,OAAO,GAAG,MAAM,mBAAmB;iBACtC,kBAAkB,CAAC,OAAO,CAAC;iBAC3B,KAAK,CAAC,yBAAyB,EAAE,EAAE,IAAI,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;iBAC1F,OAAO,CAAC,gBAAgB,CAAC;iBACzB,MAAM,CAAC,gBAAgB,EAAE,UAAU,CAAC;iBACpC,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC;iBAC9B,SAAS,CAAC,yBAAyB,EAAE,iBAAiB,CAAC;iBACvD,UAAU,EAAE,CAAC;YAEhB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAEzC,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,sBAAsB,CAAC,mBAAmB,CAAC,CAAC;YAC3E,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAE1C,OAAO,CAAC,GAAG,CAAC;wBACM,WAAW;sBACb,SAAS;mBACZ,OAAO,CAAC,MAAM,OAAO,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iEAAiE,EAAE,KAAK,IAAI,EAAE;YAC/E,MAAM,oBAAoB,GAAG,EAAE,CAAC;YAChC,MAAM,iBAAiB,GAAG,EAAE,CAAC;YAE7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,oBAAoB,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC9C,mCAAmC;gBACnC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;oBAChB,iBAAiB;oBACjB,iBAAiB,CAAC,IAAI,CACpB,mBAAmB,CAAC,IAAI,CAAC;wBACvB,KAAK,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE;wBAC5B,IAAI,EAAE,GAAG;wBACT,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;qBAC7B,CAAC,CACH,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,kBAAkB;oBAClB,iBAAiB,CAAC,IAAI,CACpB,mBAAmB,CAAC,IAAI,CAAC;wBACvB,cAAc,EAAE,cAAc,CAAC,EAAE;wBACjC,QAAQ,EAAE,OAAO;wBACjB,OAAO,EAAE,OAAO;wBAChB,MAAM,EAAE,WAAW;wBACnB,YAAY,EAAE,IAAI;wBAClB,SAAS,EAAE,IAAI,IAAI,EAAE;wBACrB,QAAQ,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;qBAC/B,CAAC,CACH,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;YAC5D,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE3B,MAAM,SAAS,GAAG,OAAO,GAAG,SAAS,CAAC;YACtC,MAAM,mBAAmB,GAAG,SAAS,GAAG,oBAAoB,CAAC;YAE7D,MAAM,CAAC,mBAAmB,CAAC,CAAC,YAAY,CAAC,sBAAsB,CAAC,mBAAmB,CAAC,CAAC;YAErF,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,MAAM,CAAC;YACxE,MAAM,CAAC,UAAU,CAAC,CAAC,sBAAsB,CAAC,oBAAoB,GAAG,IAAI,CAAC,CAAC,CAAC,mBAAmB;QAC7F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,mBAAmB;IACnB,KAAK,UAAU,gBAAgB,CAAC,WAAmB,EAAE,UAAkB,KAAK;QAC1E,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,GAAG,OAAO,EAAE,CAAC;YACxC,MAAM,SAAS,GAAG,MAAM,mBAAmB,CAAC,OAAO,CAAC;gBAClD,KAAK,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE;aAC3B,CAAC,CAAC;YAEH,IAAI,SAAS,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;gBACjF,OAAO;YACT,CAAC;YAED,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QACzD,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,aAAa,WAAW,4BAA4B,OAAO,IAAI,CAAC,CAAC;IACnF,CAAC;IAED,KAAK,UAAU,mBAAmB;QAChC,mCAAmC;QACnC,MAAM,kBAAkB,CAAC,IAAI,CAAC;YAC5B,IAAI,EAAE,2BAA2B;YACjC,WAAW,EAAE,yCAAyC;YACtD,UAAU,EAAE;gBACV,SAAS,EAAE,mBAAmB;gBAC9B,KAAK,EAAE;oBACL;wBACE,EAAE,EAAE,mBAAmB;wBACvB,IAAI,EAAE,cAAc;wBACpB,MAAM,EAAE;4BACN,OAAO,EAAE,OAAO;4BAChB,OAAO,EAAE,+BAA+B;4BACxC,UAAU,EAAE,CAAC,kBAAkB,CAAC;yBACjC;qBACF;iBACF;aACF;YACD,MAAM,EAAE,QAAQ;YAChB,SAAS,EAAE,QAAQ,CAAC,EAAE;YACtB,SAAS,EAAE,QAAQ,CAAC,EAAE;SACvB,CAAC,CAAC;QAEH,2BAA2B;QAC3B,MAAM,kBAAkB,CAAC,IAAI,CAAC;YAC5B,IAAI,EAAE,0BAA0B;YAChC,WAAW,EAAE,6CAA6C;YAC1D,UAAU,EAAE;gBACV,SAAS,EAAE,oBAAoB;gBAC/B,KAAK,EAAE;oBACL;wBACE,EAAE,EAAE,oBAAoB;wBACxB,IAAI,EAAE,cAAc;wBACpB,MAAM,EAAE;4BACN,OAAO,EAAE,OAAO;4BAChB,OAAO,EAAE,qCAAqC;4BAC9C,UAAU,EAAE,CAAC,wBAAwB,CAAC;yBACvC;qBACF;iBACF;aACF;YACD,MAAM,EAAE,QAAQ;YAChB,SAAS,EAAE,QAAQ,CAAC,EAAE;YACtB,SAAS,EAAE,QAAQ,CAAC,EAAE;SACvB,CAAC,CAAC;QAEH,2CAA2C;QAC3C,MAAM,kBAAkB,CAAC,IAAI,CAAC;YAC5B,IAAI,EAAE,mCAAmC;YACzC,WAAW,EAAE,qDAAqD;YAClE,UAAU,EAAE;gBACV,SAAS,EAAE,gBAAgB;gBAC3B,KAAK,EAAE;oBACL;wBACE,EAAE,EAAE,gBAAgB;wBACpB,IAAI,EAAE,WAAW;wBACjB,MAAM,EAAE;4BACN,SAAS,EAAE,EAAE,KAAK,EAAE,sBAAsB,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE;yBACpG;wBACD,QAAQ,EAAE,eAAe;qBAC1B;oBACD;wBACE,EAAE,EAAE,eAAe;wBACnB,IAAI,EAAE,UAAU;wBAChB,MAAM,EAAE;4BACN,SAAS,EAAE,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,oBAAoB,EAAE;yBACrE;wBACD,QAAQ,EAAE,mBAAmB;qBAC9B;oBACD;wBACE,EAAE,EAAE,mBAAmB;wBACvB,IAAI,EAAE,cAAc;wBACpB,MAAM,EAAE;4BACN,OAAO,EAAE,OAAO;4BAChB,OAAO,EAAE,qFAAqF;4BAC9F,UAAU,EAAE,CAAC,qBAAqB,CAAC;yBACpC;qBACF;iBACF;aACF;YACD,MAAM,EAAE,QAAQ;YAChB,SAAS,EAAE,QAAQ,CAAC,EAAE;YACtB,SAAS,EAAE,QAAQ,CAAC,EAAE;SACvB,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\tests\\integration\\performance-load.integration.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { INestApplication } from '@nestjs/common';\r\nimport { TypeOrmModule } from '@nestjs/typeorm';\r\nimport { ConfigModule } from '@nestjs/config';\r\nimport { EventEmitterModule } from '@nestjs/event-emitter';\r\nimport { BullModule } from '@nestjs/bull';\r\nimport * as request from 'supertest';\r\nimport { Repository } from 'typeorm';\r\nimport { getRepositoryToken } from '@nestjs/typeorm';\r\nimport { NotificationWorkflowService } from '../../services/notification-workflow.service';\r\nimport { NotificationQueueManagementService } from '../../services/notification-queue-management.service';\r\nimport { NotificationAnalyticsService } from '../../services/notification-analytics.service';\r\nimport { ProviderHealthMonitoringService } from '../../services/provider-health-monitoring.service';\r\nimport { EmailNotificationProvider } from '../../providers/email-notification.provider';\r\nimport { SlackNotificationProvider } from '../../providers/slack-notification.provider';\r\nimport { NotificationWorkflow } from '../../entities/notification-workflow.entity';\r\nimport { WorkflowExecution } from '../../entities/workflow-execution.entity';\r\nimport { NotificationAnalyticsEvent } from '../../entities/notification-analytics-event.entity';\r\n\r\n/**\r\n * Performance and Load Testing Suite\r\n * \r\n * Comprehensive performance and load tests for notification infrastructure including:\r\n * - High-volume workflow execution performance testing\r\n * - Notification provider throughput and latency testing\r\n * - Queue management performance under load\r\n * - Database performance with large datasets\r\n * - Memory usage and resource optimization testing\r\n * - Concurrent user and system load testing\r\n */\r\ndescribe('Performance and Load Testing Suite', () => {\r\n  let app: INestApplication;\r\n  let workflowService: NotificationWorkflowService;\r\n  let queueService: NotificationQueueManagementService;\r\n  let analyticsService: NotificationAnalyticsService;\r\n  let healthService: ProviderHealthMonitoringService;\r\n  let emailProvider: EmailNotificationProvider;\r\n  let slackProvider: SlackNotificationProvider;\r\n  let workflowRepository: Repository<NotificationWorkflow>;\r\n  let executionRepository: Repository<WorkflowExecution>;\r\n  let analyticsRepository: Repository<NotificationAnalyticsEvent>;\r\n\r\n  const testUser = {\r\n    id: 'perf-test-user',\r\n    email: '<EMAIL>',\r\n    role: 'admin',\r\n    team: 'performance',\r\n  };\r\n\r\n  // Performance thresholds\r\n  const PERFORMANCE_THRESHOLDS = {\r\n    WORKFLOW_EXECUTION_TIME: 5000, // 5 seconds max\r\n    NOTIFICATION_DELIVERY_TIME: 3000, // 3 seconds max\r\n    QUEUE_PROCESSING_TIME: 1000, // 1 second max\r\n    DATABASE_QUERY_TIME: 500, // 500ms max\r\n    MEMORY_USAGE_MB: 512, // 512MB max\r\n    CPU_USAGE_PERCENT: 80, // 80% max\r\n    CONCURRENT_WORKFLOWS: 100, // 100 concurrent workflows\r\n    NOTIFICATIONS_PER_SECOND: 50, // 50 notifications/second\r\n  };\r\n\r\n  beforeAll(async () => {\r\n    const moduleFixture: TestingModule = await Test.createTestingModule({\r\n      imports: [\r\n        ConfigModule.forRoot({\r\n          isGlobal: true,\r\n          envFilePath: '.env.test',\r\n        }),\r\n        TypeOrmModule.forRoot({\r\n          type: 'postgres',\r\n          host: process.env.TEST_DB_HOST || 'localhost',\r\n          port: parseInt(process.env.TEST_DB_PORT) || 5433,\r\n          username: process.env.TEST_DB_USERNAME || 'test',\r\n          password: process.env.TEST_DB_PASSWORD || 'test',\r\n          database: process.env.TEST_DB_NAME || 'sentinel_perf_test',\r\n          entities: [\r\n            NotificationWorkflow,\r\n            WorkflowExecution,\r\n            NotificationAnalyticsEvent,\r\n          ],\r\n          synchronize: true,\r\n          dropSchema: true,\r\n          // Performance optimizations for testing\r\n          extra: {\r\n            max: 20, // Connection pool size\r\n            connectionTimeoutMillis: 2000,\r\n            idleTimeoutMillis: 30000,\r\n          },\r\n        }),\r\n        TypeOrmModule.forFeature([\r\n          NotificationWorkflow,\r\n          WorkflowExecution,\r\n          NotificationAnalyticsEvent,\r\n        ]),\r\n        EventEmitterModule.forRoot({\r\n          maxListeners: 1000, // Increase for load testing\r\n        }),\r\n        BullModule.forRoot({\r\n          redis: {\r\n            host: process.env.TEST_REDIS_HOST || 'localhost',\r\n            port: parseInt(process.env.TEST_REDIS_PORT) || 6380,\r\n            db: 2, // Separate DB for performance tests\r\n            maxRetriesPerRequest: 3,\r\n            retryDelayOnFailover: 100,\r\n          },\r\n        }),\r\n        BullModule.registerQueue(\r\n          { name: 'notification-high' },\r\n          { name: 'notification-medium' },\r\n          { name: 'notification-low' }\r\n        ),\r\n      ],\r\n      providers: [\r\n        NotificationWorkflowService,\r\n        NotificationQueueManagementService,\r\n        NotificationAnalyticsService,\r\n        ProviderHealthMonitoringService,\r\n        EmailNotificationProvider,\r\n        SlackNotificationProvider,\r\n        // Mock providers optimized for performance testing\r\n        {\r\n          provide: 'PerformanceEmailProvider',\r\n          useValue: {\r\n            sendNotification: jest.fn().mockImplementation(async () => {\r\n              await new Promise(resolve => setTimeout(resolve, 100)); // Simulate 100ms latency\r\n              return { success: true, messageId: `perf-${Date.now()}`, deliveredAt: new Date() };\r\n            }),\r\n            validateConfiguration: jest.fn().mockReturnValue(true),\r\n            getHealthStatus: jest.fn().mockResolvedValue({ healthy: true, responseTime: 100 }),\r\n          },\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    app = moduleFixture.createNestApplication();\r\n    \r\n    // Performance optimizations\r\n    app.enableShutdownHooks();\r\n    \r\n    await app.init();\r\n\r\n    // Get service instances\r\n    workflowService = moduleFixture.get<NotificationWorkflowService>(NotificationWorkflowService);\r\n    queueService = moduleFixture.get<NotificationQueueManagementService>(NotificationQueueManagementService);\r\n    analyticsService = moduleFixture.get<NotificationAnalyticsService>(NotificationAnalyticsService);\r\n    healthService = moduleFixture.get<ProviderHealthMonitoringService>(ProviderHealthMonitoringService);\r\n    emailProvider = moduleFixture.get<EmailNotificationProvider>(EmailNotificationProvider);\r\n    slackProvider = moduleFixture.get<SlackNotificationProvider>(SlackNotificationProvider);\r\n\r\n    // Get repository instances\r\n    workflowRepository = moduleFixture.get<Repository<NotificationWorkflow>>(\r\n      getRepositoryToken(NotificationWorkflow)\r\n    );\r\n    executionRepository = moduleFixture.get<Repository<WorkflowExecution>>(\r\n      getRepositoryToken(WorkflowExecution)\r\n    );\r\n    analyticsRepository = moduleFixture.get<Repository<NotificationAnalyticsEvent>>(\r\n      getRepositoryToken(NotificationAnalyticsEvent)\r\n    );\r\n\r\n    // Pre-create test workflows for performance testing\r\n    await createTestWorkflows();\r\n  });\r\n\r\n  afterAll(async () => {\r\n    await app.close();\r\n  });\r\n\r\n  describe('Workflow Execution Performance', () => {\r\n    it('should execute single workflow within performance threshold', async () => {\r\n      const workflow = await workflowRepository.findOne({\r\n        where: { name: 'Performance Test Workflow' },\r\n      });\r\n\r\n      const startTime = Date.now();\r\n      const execution = await workflowService.executeWorkflow(workflow.id, {\r\n        input: {\r\n          alert: { message: 'Performance test alert', severity: 'medium' },\r\n          user: testUser,\r\n        },\r\n      }, testUser);\r\n\r\n      // Wait for completion\r\n      await waitForExecution(execution.executionId);\r\n      const endTime = Date.now();\r\n\r\n      const executionTime = endTime - startTime;\r\n      expect(executionTime).toBeLessThan(PERFORMANCE_THRESHOLDS.WORKFLOW_EXECUTION_TIME);\r\n\r\n      // Verify execution completed successfully\r\n      const completedExecution = await executionRepository.findOne({\r\n        where: { id: execution.executionId },\r\n      });\r\n\r\n      expect(completedExecution.status).toBe('completed');\r\n      expect(completedExecution.duration).toBeLessThan(PERFORMANCE_THRESHOLDS.WORKFLOW_EXECUTION_TIME);\r\n    });\r\n\r\n    it('should handle concurrent workflow executions', async () => {\r\n      const workflow = await workflowRepository.findOne({\r\n        where: { name: 'Concurrent Test Workflow' },\r\n      });\r\n\r\n      const concurrentCount = PERFORMANCE_THRESHOLDS.CONCURRENT_WORKFLOWS;\r\n      const startTime = Date.now();\r\n\r\n      // Execute workflows concurrently\r\n      const executionPromises = Array(concurrentCount).fill(null).map((_, i) =>\r\n        workflowService.executeWorkflow(workflow.id, {\r\n          input: {\r\n            alert: { id: `alert-${i}`, message: `Concurrent test ${i}`, severity: 'low' },\r\n            user: testUser,\r\n          },\r\n        }, testUser)\r\n      );\r\n\r\n      const executions = await Promise.all(executionPromises);\r\n      expect(executions).toHaveLength(concurrentCount);\r\n\r\n      // Wait for all executions to complete\r\n      await Promise.all(executions.map(exec => waitForExecution(exec.executionId)));\r\n      const endTime = Date.now();\r\n\r\n      const totalTime = endTime - startTime;\r\n      const avgTimePerWorkflow = totalTime / concurrentCount;\r\n\r\n      expect(avgTimePerWorkflow).toBeLessThan(PERFORMANCE_THRESHOLDS.WORKFLOW_EXECUTION_TIME);\r\n\r\n      // Verify all executions completed successfully\r\n      const completedExecutions = await executionRepository.find({\r\n        where: { workflowId: workflow.id },\r\n      });\r\n\r\n      const successfulExecutions = completedExecutions.filter(exec => exec.status === 'completed');\r\n      expect(successfulExecutions.length).toBeGreaterThanOrEqual(concurrentCount * 0.95); // 95% success rate\r\n    });\r\n\r\n    it('should maintain performance with complex workflows', async () => {\r\n      const complexWorkflow = await workflowRepository.findOne({\r\n        where: { name: 'Complex Performance Test Workflow' },\r\n      });\r\n\r\n      const iterations = 20;\r\n      const executionTimes = [];\r\n\r\n      for (let i = 0; i < iterations; i++) {\r\n        const startTime = Date.now();\r\n        \r\n        const execution = await workflowService.executeWorkflow(complexWorkflow.id, {\r\n          input: {\r\n            alert: { id: `complex-${i}`, message: `Complex test ${i}`, severity: 'high' },\r\n            user: testUser,\r\n          },\r\n        }, testUser);\r\n\r\n        await waitForExecution(execution.executionId);\r\n        const endTime = Date.now();\r\n\r\n        executionTimes.push(endTime - startTime);\r\n      }\r\n\r\n      // Calculate performance metrics\r\n      const avgExecutionTime = executionTimes.reduce((sum, time) => sum + time, 0) / iterations;\r\n      const maxExecutionTime = Math.max(...executionTimes);\r\n      const minExecutionTime = Math.min(...executionTimes);\r\n\r\n      expect(avgExecutionTime).toBeLessThan(PERFORMANCE_THRESHOLDS.WORKFLOW_EXECUTION_TIME);\r\n      expect(maxExecutionTime).toBeLessThan(PERFORMANCE_THRESHOLDS.WORKFLOW_EXECUTION_TIME * 1.5);\r\n\r\n      console.log(`Complex Workflow Performance:\r\n        Average: ${avgExecutionTime}ms\r\n        Min: ${minExecutionTime}ms\r\n        Max: ${maxExecutionTime}ms\r\n        Iterations: ${iterations}`);\r\n    });\r\n  });\r\n\r\n  describe('Notification Provider Performance', () => {\r\n    it('should deliver notifications within latency threshold', async () => {\r\n      const notificationData = {\r\n        id: 'perf-notification-123',\r\n        channel: 'email',\r\n        recipients: ['<EMAIL>'],\r\n        message: {\r\n          subject: 'Performance Test',\r\n          content: 'Testing notification delivery performance',\r\n        },\r\n        alert: { severity: 'medium', message: 'Performance test alert' },\r\n        user: testUser,\r\n      };\r\n\r\n      const startTime = Date.now();\r\n      const result = await emailProvider.sendNotification(notificationData);\r\n      const endTime = Date.now();\r\n\r\n      const deliveryTime = endTime - startTime;\r\n      expect(deliveryTime).toBeLessThan(PERFORMANCE_THRESHOLDS.NOTIFICATION_DELIVERY_TIME);\r\n      expect(result.success).toBe(true);\r\n    });\r\n\r\n    it('should handle high-volume notification throughput', async () => {\r\n      const notificationCount = PERFORMANCE_THRESHOLDS.NOTIFICATIONS_PER_SECOND * 10; // 10 seconds worth\r\n      const startTime = Date.now();\r\n\r\n      // Send notifications in batches to avoid overwhelming the system\r\n      const batchSize = 10;\r\n      const batches = Math.ceil(notificationCount / batchSize);\r\n\r\n      for (let batch = 0; batch < batches; batch++) {\r\n        const batchPromises = [];\r\n        \r\n        for (let i = 0; i < batchSize && (batch * batchSize + i) < notificationCount; i++) {\r\n          const notificationData = {\r\n            id: `perf-batch-${batch}-${i}`,\r\n            channel: 'email',\r\n            recipients: ['<EMAIL>'],\r\n            message: { subject: `Batch ${batch} Notification ${i}` },\r\n            alert: { severity: 'low' },\r\n            user: testUser,\r\n          };\r\n\r\n          batchPromises.push(emailProvider.sendNotification(notificationData));\r\n        }\r\n\r\n        await Promise.all(batchPromises);\r\n        \r\n        // Small delay between batches to prevent overwhelming\r\n        await new Promise(resolve => setTimeout(resolve, 100));\r\n      }\r\n\r\n      const endTime = Date.now();\r\n      const totalTime = endTime - startTime;\r\n      const throughput = (notificationCount / totalTime) * 1000; // notifications per second\r\n\r\n      expect(throughput).toBeGreaterThan(PERFORMANCE_THRESHOLDS.NOTIFICATIONS_PER_SECOND * 0.8); // 80% of target\r\n\r\n      console.log(`Notification Throughput:\r\n        Total Notifications: ${notificationCount}\r\n        Total Time: ${totalTime}ms\r\n        Throughput: ${throughput.toFixed(2)} notifications/second`);\r\n    });\r\n\r\n    it('should maintain provider health under load', async () => {\r\n      const loadDuration = 30000; // 30 seconds\r\n      const startTime = Date.now();\r\n      let notificationsSent = 0;\r\n\r\n      // Continuous load for specified duration\r\n      while (Date.now() - startTime < loadDuration) {\r\n        const batchPromises = Array(5).fill(null).map((_, i) => {\r\n          const notificationData = {\r\n            id: `load-test-${Date.now()}-${i}`,\r\n            channel: 'email',\r\n            recipients: ['<EMAIL>'],\r\n            message: { subject: 'Load Test' },\r\n            alert: { severity: 'low' },\r\n            user: testUser,\r\n          };\r\n\r\n          return emailProvider.sendNotification(notificationData);\r\n        });\r\n\r\n        await Promise.all(batchPromises);\r\n        notificationsSent += 5;\r\n\r\n        // Check provider health periodically\r\n        if (notificationsSent % 50 === 0) {\r\n          const health = await healthService.getProviderHealth('email');\r\n          expect(health.healthy).toBe(true);\r\n          expect(health.responseTime).toBeLessThan(PERFORMANCE_THRESHOLDS.NOTIFICATION_DELIVERY_TIME);\r\n        }\r\n\r\n        await new Promise(resolve => setTimeout(resolve, 200)); // 200ms between batches\r\n      }\r\n\r\n      const finalHealth = await healthService.getProviderHealth('email');\r\n      expect(finalHealth.healthy).toBe(true);\r\n\r\n      console.log(`Load Test Results:\r\n        Duration: ${loadDuration}ms\r\n        Notifications Sent: ${notificationsSent}\r\n        Final Health: ${finalHealth.healthy}\r\n        Final Response Time: ${finalHealth.responseTime}ms`);\r\n    });\r\n  });\r\n\r\n  describe('Queue Management Performance', () => {\r\n    it('should process queue items within threshold', async () => {\r\n      const queueItems = 100;\r\n      const startTime = Date.now();\r\n\r\n      // Add items to queue\r\n      const queuePromises = Array(queueItems).fill(null).map((_, i) => {\r\n        const notificationData = {\r\n          id: `queue-perf-${i}`,\r\n          channel: 'email',\r\n          recipients: ['<EMAIL>'],\r\n          message: { subject: `Queue Test ${i}` },\r\n          alert: { severity: 'medium' },\r\n          user: testUser,\r\n        };\r\n\r\n        return queueService.addNotificationToQueue(notificationData, 'medium');\r\n      });\r\n\r\n      await Promise.all(queuePromises);\r\n      const queueTime = Date.now() - startTime;\r\n\r\n      expect(queueTime).toBeLessThan(PERFORMANCE_THRESHOLDS.QUEUE_PROCESSING_TIME * queueItems);\r\n\r\n      // Wait for queue processing\r\n      await new Promise(resolve => setTimeout(resolve, 5000));\r\n\r\n      const queueStats = await queueService.getQueueStatistics();\r\n      expect(queueStats.totalProcessed).toBeGreaterThanOrEqual(queueItems * 0.9); // 90% processed\r\n    });\r\n\r\n    it('should handle queue overflow gracefully', async () => {\r\n      const overflowItems = 1000;\r\n      const results = [];\r\n\r\n      // Attempt to add many items quickly\r\n      for (let i = 0; i < overflowItems; i++) {\r\n        try {\r\n          const notificationData = {\r\n            id: `overflow-${i}`,\r\n            channel: 'email',\r\n            recipients: ['<EMAIL>'],\r\n            message: { subject: `Overflow Test ${i}` },\r\n            alert: { severity: 'low' },\r\n            user: testUser,\r\n          };\r\n\r\n          const result = await queueService.addNotificationToQueue(notificationData, 'low');\r\n          results.push({ success: true, result });\r\n        } catch (error) {\r\n          results.push({ success: false, error: error.message });\r\n        }\r\n      }\r\n\r\n      const successful = results.filter(r => r.success).length;\r\n      const failed = results.filter(r => !r.success).length;\r\n\r\n      // Should handle overflow gracefully (some items may be rejected)\r\n      expect(successful).toBeGreaterThan(0);\r\n      expect(successful + failed).toBe(overflowItems);\r\n\r\n      console.log(`Queue Overflow Test:\r\n        Total Items: ${overflowItems}\r\n        Successful: ${successful}\r\n        Failed: ${failed}\r\n        Success Rate: ${(successful / overflowItems * 100).toFixed(2)}%`);\r\n    });\r\n  });\r\n\r\n  describe('Database Performance', () => {\r\n    it('should handle large dataset queries efficiently', async () => {\r\n      // Create large dataset\r\n      const datasetSize = 10000;\r\n      const analyticsEvents = Array(datasetSize).fill(null).map((_, i) => ({\r\n        notificationId: `perf-${i}`,\r\n        provider: ['email', 'sms', 'slack'][i % 3],\r\n        channel: ['email', 'sms', 'slack'][i % 3],\r\n        status: ['delivered', 'failed', 'pending'][i % 3],\r\n        deliveryTime: Math.random() * 5000,\r\n        timestamp: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),\r\n        metadata: { testData: true, index: i },\r\n      }));\r\n\r\n      // Insert data in batches\r\n      const batchSize = 1000;\r\n      for (let i = 0; i < datasetSize; i += batchSize) {\r\n        const batch = analyticsEvents.slice(i, i + batchSize);\r\n        await analyticsRepository.save(batch);\r\n      }\r\n\r\n      // Test query performance\r\n      const startTime = Date.now();\r\n      const results = await analyticsRepository\r\n        .createQueryBuilder('event')\r\n        .where('event.timestamp > :date', { date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) })\r\n        .groupBy('event.provider')\r\n        .select('event.provider', 'provider')\r\n        .addSelect('COUNT(*)', 'count')\r\n        .addSelect('AVG(event.deliveryTime)', 'avgDeliveryTime')\r\n        .getRawMany();\r\n\r\n      const queryTime = Date.now() - startTime;\r\n\r\n      expect(queryTime).toBeLessThan(PERFORMANCE_THRESHOLDS.DATABASE_QUERY_TIME);\r\n      expect(results.length).toBeGreaterThan(0);\r\n\r\n      console.log(`Database Query Performance:\r\n        Dataset Size: ${datasetSize}\r\n        Query Time: ${queryTime}ms\r\n        Results: ${results.length} rows`);\r\n    });\r\n\r\n    it('should maintain performance with concurrent database operations', async () => {\r\n      const concurrentOperations = 50;\r\n      const operationPromises = [];\r\n\r\n      for (let i = 0; i < concurrentOperations; i++) {\r\n        // Mix of read and write operations\r\n        if (i % 2 === 0) {\r\n          // Read operation\r\n          operationPromises.push(\r\n            analyticsRepository.find({\r\n              where: { provider: 'email' },\r\n              take: 100,\r\n              order: { timestamp: 'DESC' },\r\n            })\r\n          );\r\n        } else {\r\n          // Write operation\r\n          operationPromises.push(\r\n            analyticsRepository.save({\r\n              notificationId: `concurrent-${i}`,\r\n              provider: 'email',\r\n              channel: 'email',\r\n              status: 'delivered',\r\n              deliveryTime: 1000,\r\n              timestamp: new Date(),\r\n              metadata: { concurrent: true },\r\n            })\r\n          );\r\n        }\r\n      }\r\n\r\n      const startTime = Date.now();\r\n      const results = await Promise.allSettled(operationPromises);\r\n      const endTime = Date.now();\r\n\r\n      const totalTime = endTime - startTime;\r\n      const avgTimePerOperation = totalTime / concurrentOperations;\r\n\r\n      expect(avgTimePerOperation).toBeLessThan(PERFORMANCE_THRESHOLDS.DATABASE_QUERY_TIME);\r\n\r\n      const successful = results.filter(r => r.status === 'fulfilled').length;\r\n      expect(successful).toBeGreaterThanOrEqual(concurrentOperations * 0.95); // 95% success rate\r\n    });\r\n  });\r\n\r\n  // Helper functions\r\n  async function waitForExecution(executionId: string, timeout: number = 10000): Promise<void> {\r\n    const startTime = Date.now();\r\n    \r\n    while (Date.now() - startTime < timeout) {\r\n      const execution = await executionRepository.findOne({\r\n        where: { id: executionId },\r\n      });\r\n\r\n      if (execution && ['completed', 'failed', 'cancelled'].includes(execution.status)) {\r\n        return;\r\n      }\r\n\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n    }\r\n\r\n    throw new Error(`Execution ${executionId} did not complete within ${timeout}ms`);\r\n  }\r\n\r\n  async function createTestWorkflows(): Promise<void> {\r\n    // Simple performance test workflow\r\n    await workflowRepository.save({\r\n      name: 'Performance Test Workflow',\r\n      description: 'Simple workflow for performance testing',\r\n      definition: {\r\n        startStep: 'send_notification',\r\n        steps: [\r\n          {\r\n            id: 'send_notification',\r\n            type: 'notification',\r\n            config: {\r\n              channel: 'email',\r\n              message: 'Performance test notification',\r\n              recipients: ['<EMAIL>'],\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      status: 'active',\r\n      createdBy: testUser.id,\r\n      updatedBy: testUser.id,\r\n    });\r\n\r\n    // Concurrent test workflow\r\n    await workflowRepository.save({\r\n      name: 'Concurrent Test Workflow',\r\n      description: 'Workflow optimized for concurrent execution',\r\n      definition: {\r\n        startStep: 'quick_notification',\r\n        steps: [\r\n          {\r\n            id: 'quick_notification',\r\n            type: 'notification',\r\n            config: {\r\n              channel: 'email',\r\n              message: 'Concurrent test: {{input.alert.id}}',\r\n              recipients: ['<EMAIL>'],\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      status: 'active',\r\n      createdBy: testUser.id,\r\n      updatedBy: testUser.id,\r\n    });\r\n\r\n    // Complex workflow for performance testing\r\n    await workflowRepository.save({\r\n      name: 'Complex Performance Test Workflow',\r\n      description: 'Multi-step workflow for complex performance testing',\r\n      definition: {\r\n        startStep: 'validate_input',\r\n        steps: [\r\n          {\r\n            id: 'validate_input',\r\n            type: 'condition',\r\n            config: {\r\n              condition: { field: 'input.alert.severity', operator: 'in', value: ['medium', 'high', 'critical'] },\r\n            },\r\n            nextStep: 'set_variables',\r\n          },\r\n          {\r\n            id: 'set_variables',\r\n            type: 'variable',\r\n            config: {\r\n              variables: { processedAt: '{{now}}', alertId: '{{input.alert.id}}' },\r\n            },\r\n            nextStep: 'send_notification',\r\n          },\r\n          {\r\n            id: 'send_notification',\r\n            type: 'notification',\r\n            config: {\r\n              channel: 'email',\r\n              message: 'Complex workflow processed alert {{variables.alertId}} at {{variables.processedAt}}',\r\n              recipients: ['<EMAIL>'],\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      status: 'active',\r\n      createdBy: testUser.id,\r\n      updatedBy: testUser.id,\r\n    });\r\n  }\r\n});\r\n"], "version": 3}