{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\__tests__\\e2e\\integration-workflow.e2e-spec.ts", "mappings": ";;;;;AAAA,6CAAsD;AAEtD,2CAA8C;AAC9C,0DAAgC;AAChC,iDAA6C;AAE7C,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;IAC1C,IAAI,GAAqB,CAAC;IAC1B,IAAI,UAAkB,CAAC;IACvB,IAAI,SAAiB,CAAC;IACtB,IAAI,WAAmB,CAAC;IACxB,IAAI,aAAqB,CAAC;IAE1B,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,aAAa,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAClE,OAAO,EAAE;gBACP,qBAAY,CAAC,OAAO,CAAC;oBACnB,QAAQ,EAAE,IAAI;oBACd,WAAW,EAAE,WAAW;iBACzB,CAAC;gBACF,sBAAS;aACV;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,GAAG,GAAG,aAAa,CAAC,qBAAqB,EAAE,CAAC;QAC5C,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC;QAEjB,mBAAmB;QACnB,MAAM,aAAa,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;aACrD,IAAI,CAAC,uBAAuB,CAAC;aAC7B,IAAI,CAAC;YACJ,KAAK,EAAE,mBAAmB;YAC1B,QAAQ,EAAE,mBAAmB;YAC7B,IAAI,EAAE,YAAY;YAClB,KAAK,EAAE,CAAC,OAAO,CAAC;SACjB,CAAC,CAAC;QAEL,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;QACxD,WAAW,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QAE9C,qBAAqB;QACrB,MAAM,YAAY,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;aACpD,IAAI,CAAC,uBAAuB,CAAC;aAC7B,IAAI,CAAC;YACJ,KAAK,EAAE,kBAAkB;YACzB,QAAQ,EAAE,kBAAkB;YAC5B,IAAI,EAAE,cAAc;SACrB,CAAC,CAAC;QAEL,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;QACtD,aAAa,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IACjD,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAChD,IAAI,eAAuB,CAAC;QAC5B,IAAI,eAAuB,CAAC;QAC5B,IAAI,aAAqB,CAAC;QAE1B,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,iBAAiB,GAAG;gBACxB,SAAS,EAAE,wBAAwB;gBACnC,QAAQ,EAAE,MAAM;gBAChB,WAAW,EAAE,yDAAyD;gBACtE,QAAQ,EAAE,eAAe;gBACzB,QAAQ,EAAE,WAAW;gBACrB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,QAAQ,EAAE;oBACR,eAAe,EAAE,UAAU;oBAC3B,iBAAiB,EAAE,eAAe;oBAClC,KAAK,EAAE,eAAe;oBACtB,gBAAgB,EAAE,YAAY;iBAC/B;aACF,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,yBAAyB,CAAC;iBAC/B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC,iBAAiB,CAAC;iBACvB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAC5B,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACtB,GAAG,iBAAiB;oBACpB,MAAM,EAAE,MAAM;oBACd,SAAS,EAAE,aAAa;oBACxB,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC7B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC9B,CAAC;gBACF,OAAO,EAAE,qCAAqC;gBAC9C,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC9B,CAAC,CAAC;YAEH,eAAe,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,2BAA2B,eAAe,EAAE,CAAC;iBACjD,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAChC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,EAAE,EAAE,eAAe;gBACnB,SAAS,EAAE,wBAAwB;gBACnC,QAAQ,EAAE,MAAM;gBAChB,MAAM,EAAE,MAAM;aACf,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mEAAmE,EAAE,KAAK,IAAI,EAAE;YACjF,MAAM,iBAAiB,GAAG;gBACxB,KAAK,EAAE,eAAe;gBACtB,KAAK,EAAE,sCAAsC;gBAC7C,WAAW,EAAE,0FAA0F;gBACvG,QAAQ,EAAE,MAAM;gBAChB,SAAS,EAAE,GAAG;gBACd,eAAe,EAAE,CAAC,UAAU,EAAE,mBAAmB,CAAC;gBAClD,aAAa,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACvC,MAAM,EAAE,MAAM;gBACd,eAAe,EAAE,eAAe;gBAChC,WAAW,EAAE;oBACX,KAAK,EAAE;wBACL,yBAAyB;wBACzB,iCAAiC;wBACjC,wBAAwB;qBACzB;oBACD,eAAe,EAAE,SAAS;oBAC1B,QAAQ,EAAE,MAAM;iBACjB;aACF,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,kDAAkD,CAAC;iBACxD,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC,iBAAiB,CAAC;iBACvB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAChC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBACtB,KAAK,EAAE,eAAe;gBACtB,QAAQ,EAAE,MAAM;gBAChB,MAAM,EAAE,MAAM;gBACd,eAAe,EAAE,eAAe;aACjC,CAAC,CACH,CAAC;YAEF,eAAe,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,eAAe,GAAG;gBACtB,UAAU,EAAE,SAAS;gBACrB,QAAQ,EAAE,UAAU;gBACpB,KAAK,EAAE,qCAAqC;gBAC5C,WAAW,EAAE,2DAA2D;gBACxE,UAAU,EAAE;oBACV;wBACE,IAAI,EAAE,IAAI;wBACV,KAAK,EAAE,eAAe;wBACtB,UAAU,EAAE,MAAM;qBACnB;oBACD;wBACE,IAAI,EAAE,QAAQ;wBACd,KAAK,EAAE,sBAAsB;wBAC7B,UAAU,EAAE,QAAQ;qBACrB;iBACF;gBACD,IAAI,EAAE;oBACJ,2CAA2C;oBAC3C,uBAAuB;iBACxB;gBACD,OAAO,EAAE,CAAC,mBAAmB,EAAE,mBAAmB,CAAC;gBACnD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,QAAQ,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBAClC,QAAQ,EAAE,IAAI;aACf,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,qCAAqC,CAAC;iBAC3C,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC,eAAe,CAAC;iBACrB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAChC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBACtB,UAAU,EAAE,SAAS;gBACrB,QAAQ,EAAE,UAAU;gBACpB,QAAQ,EAAE,IAAI;aACf,CAAC,CACH,CAAC;YAEF,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,KAAK,IAAI,EAAE;YACxE,MAAM,eAAe,GAAG;gBACtB,eAAe,EAAE,eAAe;gBAChC,oBAAoB,EAAE,aAAa;gBACnC,eAAe,EAAE,UAAU;gBAC3B,UAAU,EAAE,MAAM;gBAClB,iBAAiB,EAAE,CAAC,eAAe,CAAC;gBACpC,aAAa,EAAE,kDAAkD;aAClE,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,gCAAgC,CAAC;iBACtC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC,eAAe,CAAC;iBACrB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAChC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBACtB,eAAe,EAAE,eAAe;gBAChC,oBAAoB,EAAE,aAAa;gBACnC,eAAe,EAAE,UAAU;gBAC3B,UAAU,EAAE,MAAM;aACnB,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,sCAAsC,CAAC;iBAC5C,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC;gBACJ,eAAe,EAAE,eAAe;gBAChC,eAAe,EAAE,eAAe;gBAChC,oBAAoB,EAAE,aAAa;aACpC,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAChC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBACtB,eAAe,EAAE,eAAe;gBAChC,eAAe,EAAE,MAAM,CAAC,eAAe,CAAC;oBACtC,MAAM,CAAC,gBAAgB,CAAC;wBACtB,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC1B,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC5B,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;qBAChC,CAAC;iBACH,CAAC;gBACF,eAAe,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBACnC,eAAe,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aACpC,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;YACpE,MAAM,UAAU,GAAG;gBACjB,MAAM,EAAE,eAAe;gBACvB,UAAU,EAAE,WAAW;gBACvB,KAAK,EAAE,6CAA6C;aACrD,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,2BAA2B,eAAe,EAAE,CAAC;iBACjD,GAAG,CAAC,eAAe,EAAE,UAAU,UAAU,EAAE,CAAC;iBAC5C,IAAI,CAAC,UAAU,CAAC;iBAChB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAChC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,EAAE,EAAE,eAAe;gBACnB,MAAM,EAAE,eAAe;gBACvB,UAAU,EAAE,WAAW;aACxB,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,eAAe,GAAG;gBACtB,eAAe,EAAE,eAAe;gBAChC,MAAM,EAAE,aAAa;gBACrB,cAAc,EAAE,CAAC,yBAAyB,CAAC;gBAC3C,cAAc,EAAE,CAAC,iCAAiC,EAAE,wBAAwB,CAAC;gBAC7E,mBAAmB,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE;gBAC7E,UAAU,EAAE,WAAW;aACxB,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,8CAA8C,CAAC;iBACpD,GAAG,CAAC,eAAe,EAAE,UAAU,UAAU,EAAE,CAAC;iBAC5C,IAAI,CAAC,eAAe,CAAC;iBACrB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAChC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBACtB,eAAe,EAAE,eAAe;gBAChC,MAAM,EAAE,aAAa;gBACrB,cAAc,EAAE,CAAC,yBAAyB,CAAC;aAC5C,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,SAAS,GAAG;gBAChB,MAAM,EAAE,UAAU;gBAClB,UAAU,EAAE,oCAAoC;gBAChD,UAAU,EAAE,WAAW;gBACvB,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACrC,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,2BAA2B,eAAe,EAAE,CAAC;iBACjD,GAAG,CAAC,eAAe,EAAE,UAAU,UAAU,EAAE,CAAC;iBAC5C,IAAI,CAAC,SAAS,CAAC;iBACf,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAChC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,EAAE,EAAE,eAAe;gBACnB,MAAM,EAAE,UAAU;gBAClB,UAAU,EAAE,WAAW;aACxB,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAChD,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,6BAA6B,CAAC;iBAClC,KAAK,CAAC;gBACL,SAAS,EAAE,IAAI;gBACf,cAAc,EAAE,yCAAyC;aAC1D,CAAC;iBACD,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBACjC,SAAS,EAAE,IAAI;gBACf,OAAO,EAAE;oBACP,cAAc,EAAE;wBACd,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBACzB,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC5B,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC9B,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;qBACzB;oBACD,eAAe,EAAE;wBACf,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBACzB,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC5B,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC9B,iBAAiB,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;qBACtC;oBACD,OAAO,EAAE;wBACP,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBACzB,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC1B,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC1B,aAAa,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;qBACjC;iBACF;gBACD,OAAO,EAAE;oBACP,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC7B,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAClC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBACnC;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oDAAoD,CAAC;iBAC1D,GAAG,CAAC,eAAe,EAAE,UAAU,UAAU,EAAE,CAAC;iBAC5C,IAAI,CAAC;gBACJ,KAAK,EAAE,aAAa;gBACpB,kBAAkB,EAAE,IAAI;gBACxB,MAAM,EAAE,MAAM;aACf,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBACjC,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC5B,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC/B,KAAK,EAAE,aAAa;gBACpB,OAAO,EAAE;oBACP,oBAAoB,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACxC,aAAa,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACjC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC7B,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC/B,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC7B;gBACD,eAAe,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;gBAClC,eAAe,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;gBAClC,MAAM,EAAE,WAAW;aACpB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,uCAAuC,CAAC;iBAC5C,KAAK,CAAC;gBACL,SAAS,EAAE,KAAK;gBAChB,WAAW,EAAE,sBAAsB;aACpC,CAAC;iBACD,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBACjC,SAAS,EAAE,KAAK;gBAChB,WAAW,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,KAAK,CAAC;gBAC3C,QAAQ,EAAE;oBACR,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAChC,eAAe,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;oBAClC,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;oBAC/B,sBAAsB,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC1C,iBAAiB,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBACtC;gBACD,eAAe,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;gBAClC,cAAc,EAAE;oBACd,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC/B,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;oBAC3B,oBAAoB,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;iBACxC;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mCAAmC,EAAE,GAAG,EAAE;QACjD,IAAI,SAAiB,CAAC;QAEtB,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,QAAQ,GAAG;gBACf,KAAK,EAAE,qBAAqB;gBAC5B,QAAQ,EAAE,qBAAqB;gBAC/B,IAAI,EAAE,kBAAkB;gBACxB,KAAK,EAAE,CAAC,SAAS,CAAC;gBAClB,WAAW,EAAE,CAAC,sBAAsB,EAAE,uBAAuB,EAAE,sBAAsB,CAAC;aACvF,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,qBAAqB,CAAC;iBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,UAAU,EAAE,CAAC;iBAC5C,IAAI,CAAC,QAAQ,CAAC;iBACd,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CACrC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBACtB,KAAK,EAAE,qBAAqB;gBAC5B,IAAI,EAAE,kBAAkB;gBACxB,KAAK,EAAE,CAAC,SAAS,CAAC;gBAClB,WAAW,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC,sBAAsB,CAAC,CAAC;aAC9D,CAAC,CACH,CAAC;YAEF,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,gCAAgC;YAChC,MAAM,aAAa,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACrD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC;gBACJ,KAAK,EAAE,qBAAqB;gBAC5B,QAAQ,EAAE,qBAAqB;aAChC,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,YAAY,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;YAEhE,yCAAyC;YACzC,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,yBAAyB,CAAC;iBAC9B,GAAG,CAAC,eAAe,EAAE,UAAU,YAAY,EAAE,CAAC;iBAC9C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,+CAA+C;YAC/C,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,qBAAqB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,YAAY,EAAE,CAAC;iBAC9C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,sEAAsE;YACtE,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,MAAM,CAAC,iCAAiC,CAAC;iBACzC,GAAG,CAAC,eAAe,EAAE,UAAU,YAAY,EAAE,CAAC;iBAC9C,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,UAAU,GAAG;gBACjB,WAAW,EAAE;oBACX,sBAAsB;oBACtB,uBAAuB;oBACvB,wBAAwB;oBACxB,sBAAsB;oBACtB,uBAAuB;iBACxB;aACF,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,uBAAuB,SAAS,cAAc,CAAC;iBACnD,GAAG,CAAC,eAAe,EAAE,UAAU,UAAU,EAAE,CAAC;iBAC5C,IAAI,CAAC,UAAU,CAAC;iBAChB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAC5C,MAAM,CAAC,eAAe,CAAC;gBACrB,sBAAsB;gBACtB,uBAAuB;gBACvB,wBAAwB;aACzB,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,oCAAoC,SAAS,EAAE,CAAC;iBACpD,GAAG,CAAC,eAAe,EAAE,UAAU,UAAU,EAAE,CAAC;iBAC5C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CACzC,MAAM,CAAC,eAAe,CAAC;gBACrB,MAAM,CAAC,gBAAgB,CAAC;oBACtB,MAAM,EAAE,cAAc;oBACtB,WAAW,EAAE,WAAW;oBACxB,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC9B,CAAC;gBACF,MAAM,CAAC,gBAAgB,CAAC;oBACtB,MAAM,EAAE,qBAAqB;oBAC7B,WAAW,EAAE,WAAW;oBACxB,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC9B,CAAC;aACH,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0CAA0C,EAAE,GAAG,EAAE;QACxD,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,UAAU,GAAG;gBACjB,QAAQ,EAAE;oBACR,kBAAkB,EAAE,IAAI;oBACxB,UAAU,EAAE,2CAA2C;oBACvD,iBAAiB,EAAE,QAAQ;iBAC5B;gBACD,QAAQ,EAAE;oBACR,iBAAiB,EAAE,IAAI;oBACvB,YAAY,EAAE,KAAK;oBACnB,SAAS,EAAE,eAAe;iBAC3B;gBACD,SAAS,EAAE;oBACT,cAAc,EAAE,KAAK;oBACrB,SAAS,EAAE,MAAM;oBACjB,kBAAkB,EAAE,MAAM;iBAC3B;aACF,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,oCAAoC,CAAC;iBACzC,GAAG,CAAC,eAAe,EAAE,UAAU,UAAU,EAAE,CAAC;iBAC5C,IAAI,CAAC,UAAU,CAAC;iBAChB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,CAC9C,MAAM,CAAC,gBAAgB,CAAC;gBACtB,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAChC,kBAAkB,EAAE,IAAI;oBACxB,iBAAiB,EAAE,QAAQ;iBAC5B,CAAC;gBACF,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAChC,iBAAiB,EAAE,IAAI;oBACvB,YAAY,EAAE,KAAK;iBACpB,CAAC;aACH,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,uBAAuB,CAAC;iBAC5B,GAAG,CAAC,eAAe,EAAE,UAAU,UAAU,EAAE,CAAC;iBAC5C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC1B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,UAAU,EAAE;oBACV,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC/B,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC5B,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACzB,gBAAgB,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACpC,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC9B,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC1B,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBACxB;gBACD,OAAO,EAAE;oBACP,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC1B,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAChC,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC9B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC9B;gBACD,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;gBAC/B,eAAe,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;aACnC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,UAAU,EAAE,CAAC;iBAC5C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,WAAW,EAAE;oBACX,iBAAiB,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACrC,mBAAmB,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACvC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC7B,iBAAiB,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBACtC;gBACD,MAAM,EAAE;oBACN,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC5B,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC/B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC7B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC9B;gBACD,QAAQ,EAAE;oBACR,kBAAkB,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACtC,aAAa,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACjC,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC/B,aAAa,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAClC;gBACD,QAAQ,EAAE;oBACR,uBAAuB,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC3C,uBAAuB,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC3C,eAAe,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACnC,eAAe,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBACpC;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC/C,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,aAAa,GAAG;gBACpB,QAAQ,EAAE,iBAAiB;gBAC3B,MAAM,EAAE,MAAM;gBACd,SAAS,EAAE;oBACT,KAAK,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE;oBACnE,GAAG,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBAC9B;gBACD,OAAO,EAAE;oBACP,QAAQ,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC;oBAC9B,MAAM,EAAE,CAAC,MAAM,EAAE,eAAe,CAAC;iBAClC;aACF,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,sBAAsB,CAAC;iBAC5B,GAAG,CAAC,eAAe,EAAE,UAAU,UAAU,EAAE,CAAC;iBAC5C,IAAI,CAAC,aAAa,CAAC;iBACnB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC5B,MAAM,EAAE,YAAY;oBACpB,mBAAmB,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBACxC;gBACD,OAAO,EAAE,iCAAiC;gBAC1C,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC9B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,6BAA6B;YAC7B,MAAM,cAAc,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACtD,IAAI,CAAC,sBAAsB,CAAC;iBAC5B,GAAG,CAAC,eAAe,EAAE,UAAU,UAAU,EAAE,CAAC;iBAC5C,IAAI,CAAC;gBACJ,QAAQ,EAAE,iBAAiB;gBAC3B,MAAM,EAAE,KAAK;gBACb,SAAS,EAAE;oBACT,KAAK,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE;oBACpE,GAAG,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBAC9B;aACF,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,QAAQ,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;YAEnD,mBAAmB;YACnB,MAAM,cAAc,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACtD,GAAG,CAAC,wBAAwB,QAAQ,SAAS,CAAC;iBAC9C,GAAG,CAAC,eAAe,EAAE,UAAU,UAAU,EAAE,CAAC;iBAC5C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBACvC,QAAQ,EAAE,QAAQ;gBAClB,MAAM,EAAE,MAAM,CAAC,cAAc,CAAC,iCAAiC,CAAC;gBAChE,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC5B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC/B,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC/B,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC7B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;YAC3C,MAAM,aAAa,GAAG;gBACpB,UAAU,EAAE,MAAM;gBAClB,WAAW,EAAE,IAAI;gBACjB,oBAAoB,EAAE,IAAI;gBAC1B,WAAW,EAAE,IAAI;gBACjB,UAAU,EAAE,IAAI;aACjB,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,sBAAsB,CAAC;iBAC5B,GAAG,CAAC,eAAe,EAAE,UAAU,UAAU,EAAE,CAAC;iBAC5C,IAAI,CAAC,aAAa,CAAC;iBACnB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC5B,MAAM,EAAE,WAAW;oBACnB,UAAU,EAAE,MAAM;oBAClB,aAAa,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACjC,iBAAiB,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBACtC;gBACD,OAAO,EAAE,uCAAuC;gBAChD,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC9B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mCAAmC,EAAE,GAAG,EAAE;QACjD,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,0BAA0B;YAC1B,MAAM,aAAa,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACrD,IAAI,CAAC,yBAAyB,CAAC;iBAC/B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC;gBACJ,SAAS,EAAE,mBAAmB;gBAC9B,QAAQ,EAAE,UAAU;gBACpB,WAAW,EAAE,sCAAsC;gBACnD,QAAQ,EAAE,aAAa;gBACvB,QAAQ,EAAE,YAAY;gBACtB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAE3C,oCAAoC;YACpC,MAAM,iBAAiB,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACzD,GAAG,CAAC,iCAAiC,CAAC;iBACtC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;iBACpB,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAChD,MAAM,CAAC,eAAe,CAAC;gBACrB,MAAM,CAAC,gBAAgB,CAAC;oBACtB,EAAE,EAAE,OAAO;oBACX,SAAS,EAAE,mBAAmB;oBAC9B,QAAQ,EAAE,UAAU;iBACrB,CAAC;aACH,CAAC,CACH,CAAC;YAEF,2CAA2C;YAC3C,MAAM,gBAAgB,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACxD,GAAG,CAAC,+BAA+B,OAAO,EAAE,CAAC;iBAC7C,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CACxC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,OAAO,EAAE,OAAO;gBAChB,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAClC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,eAAe,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;aACnC,CAAC,CACH,CAAC;YAEF,gCAAgC;YAChC,MAAM,aAAa,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACrD,GAAG,CAAC,qCAAqC,CAAC;iBAC1C,KAAK,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;iBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,UAAU,EAAE,CAAC;iBAC5C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAClD,MAAM,CAAC,eAAe,CAAC;gBACrB,MAAM,CAAC,gBAAgB,CAAC;oBACtB,MAAM,EAAE,wBAAwB;oBAChC,UAAU,EAAE,OAAO;oBACnB,WAAW,EAAE,aAAa;oBAC1B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC9B,CAAC;aACH,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,wCAAwC;YACxC,MAAM,UAAU,GAAG;gBACjB,yBAAyB;gBACzB,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACpC,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBACzB,IAAI,CAAC,yBAAyB,CAAC;qBAC/B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;qBAC3C,IAAI,CAAC;oBACJ,SAAS,EAAE,kBAAkB;oBAC7B,QAAQ,EAAE,MAAM;oBAChB,WAAW,EAAE,qBAAqB,CAAC,GAAG,CAAC,EAAE;oBACzC,QAAQ,EAAE,aAAa,GAAG,GAAG,CAAC,EAAE;oBAChC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC,CAAC,CACL;gBACD,yBAAyB;gBACzB,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACpC,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBACzB,IAAI,CAAC,kDAAkD,CAAC;qBACxD,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;qBAC3C,IAAI,CAAC;oBACJ,KAAK,EAAE,YAAY,IAAI,GAAG,CAAC,EAAE;oBAC7B,KAAK,EAAE,sBAAsB,CAAC,GAAG,CAAC,EAAE;oBACpC,QAAQ,EAAE,QAAQ;oBAClB,SAAS,EAAE,GAAG;oBACd,MAAM,EAAE,MAAM;iBACf,CAAC,CACL;gBACD,mBAAmB;gBACnB,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,CAChC,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBACzB,GAAG,CAAC,6BAA6B,CAAC;qBAClC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC,CAC/C;aACF,CAAC;YAEF,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CACjC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAClB,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;iBACpD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,QAAQ,EAAE,MAAM,IAAI,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,CAC3E,CACF,CAAC;YAEF,iCAAiC;YACjC,MAAM,YAAY,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;YAC9F,MAAM,eAAe,GAAG,UAAU,CAAC,MAAM,CAAC;YAE1C,MAAM,CAAC,YAAY,GAAG,eAAe,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,mBAAmB;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,+CAA+C;YAC/C,MAAM,aAAa,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACrD,IAAI,CAAC,yBAAyB,CAAC;iBAC/B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC;gBACJ,SAAS,EAAE,aAAa;gBACxB,QAAQ,EAAE,UAAU;gBACpB,WAAW,EAAE,gCAAgC;gBAC7C,QAAQ,EAAE,cAAc;gBACxB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAE3C,MAAM,YAAY,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACpD,IAAI,CAAC,kDAAkD,CAAC;iBACxD,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC;gBACJ,KAAK,EAAE,eAAe;gBACtB,KAAK,EAAE,6BAA6B;gBACpC,QAAQ,EAAE,UAAU;gBACpB,eAAe,EAAE,OAAO;gBACxB,MAAM,EAAE,MAAM;aACf,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAEzC,yCAAyC;YACzC,MAAM,mBAAmB,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC3D,GAAG,CAAC,2BAA2B,OAAO,EAAE,CAAC;iBACzC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,kBAAkB,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC1D,GAAG,CAAC,oDAAoD,MAAM,EAAE,CAAC;iBACjE,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAEnE,6CAA6C;YAC7C,MAAM,mBAAmB,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC3D,GAAG,CAAC,gCAAgC,CAAC;iBACrC,KAAK,CAAC,EAAE,eAAe,EAAE,OAAO,EAAE,CAAC;iBACnC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CACxD,MAAM,CAAC,eAAe,CAAC;gBACrB,MAAM,CAAC,gBAAgB,CAAC;oBACtB,eAAe,EAAE,OAAO;oBACxB,sBAAsB,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC,CAAC;iBACzD,CAAC;aACH,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\__tests__\\e2e\\integration-workflow.e2e-spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { INestApplication } from '@nestjs/common';\r\nimport { ConfigModule } from '@nestjs/config';\r\nimport request from 'supertest';\r\nimport { AppModule } from '../../app.module';\r\n\r\ndescribe('Integration Workflow (e2e)', () => {\r\n  let app: INestApplication;\r\n  let adminToken: string;\r\n  let userToken: string;\r\n  let adminUserId: string;\r\n  let regularUserId: string;\r\n\r\n  beforeAll(async () => {\r\n    const moduleFixture: TestingModule = await Test.createTestingModule({\r\n      imports: [\r\n        ConfigModule.forRoot({\r\n          isGlobal: true,\r\n          envFilePath: '.env.test',\r\n        }),\r\n        AppModule,\r\n      ],\r\n    }).compile();\r\n\r\n    app = moduleFixture.createNestApplication();\r\n    await app.init();\r\n\r\n    // Setup admin user\r\n    const adminResponse = await request(app.getHttpServer())\r\n      .post('/api/v1/auth/register')\r\n      .send({\r\n        email: '<EMAIL>',\r\n        password: 'AdminPassword123!',\r\n        name: 'Admin User',\r\n        roles: ['admin'],\r\n      });\r\n\r\n    adminToken = adminResponse.body.data.tokens.accessToken;\r\n    adminUserId = adminResponse.body.data.user.id;\r\n\r\n    // Setup regular user\r\n    const userResponse = await request(app.getHttpServer())\r\n      .post('/api/v1/auth/register')\r\n      .send({\r\n        email: '<EMAIL>',\r\n        password: 'UserPassword123!',\r\n        name: 'Regular User',\r\n      });\r\n\r\n    userToken = userResponse.body.data.tokens.accessToken;\r\n    regularUserId = userResponse.body.data.user.id;\r\n  });\r\n\r\n  afterAll(async () => {\r\n    await app.close();\r\n  });\r\n\r\n  describe('Complete Security Event Workflow', () => {\r\n    let securityEventId: string;\r\n    let vulnerabilityId: string;\r\n    let threatIntelId: string;\r\n\r\n    it('should create a security event', async () => {\r\n      const securityEventData = {\r\n        eventType: 'vulnerability_detected',\r\n        severity: 'high',\r\n        description: 'SQL injection vulnerability detected in web application',\r\n        sourceIp: '*************',\r\n        targetIp: '*********',\r\n        timestamp: new Date().toISOString(),\r\n        metadata: {\r\n          applicationName: 'WebApp-1',\r\n          vulnerabilityType: 'SQL Injection',\r\n          cveId: 'CVE-2024-0001',\r\n          affectedEndpoint: '/api/users',\r\n        },\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/security-events')\r\n        .set('Authorization', `Bearer ${userToken}`)\r\n        .send(securityEventData)\r\n        .expect(201);\r\n\r\n      expect(response.body).toEqual({\r\n        success: true,\r\n        data: expect.objectContaining({\r\n          id: expect.any(String),\r\n          ...securityEventData,\r\n          status: 'open',\r\n          createdBy: regularUserId,\r\n          createdAt: expect.any(String),\r\n          updatedAt: expect.any(String),\r\n        }),\r\n        message: 'Security event created successfully',\r\n        timestamp: expect.any(String),\r\n      });\r\n\r\n      securityEventId = response.body.data.id;\r\n    });\r\n\r\n    it('should retrieve the created security event', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get(`/api/v1/security-events/${securityEventId}`)\r\n        .set('Authorization', `Bearer ${userToken}`)\r\n        .expect(200);\r\n\r\n      expect(response.body.data).toEqual(\r\n        expect.objectContaining({\r\n          id: securityEventId,\r\n          eventType: 'vulnerability_detected',\r\n          severity: 'high',\r\n          status: 'open',\r\n        })\r\n      );\r\n    });\r\n\r\n    it('should create a vulnerability record linked to the security event', async () => {\r\n      const vulnerabilityData = {\r\n        cveId: 'CVE-2024-0001',\r\n        title: 'SQL Injection in User Authentication',\r\n        description: 'The application is vulnerable to SQL injection attacks in the user authentication module',\r\n        severity: 'high',\r\n        cvssScore: 8.5,\r\n        affectedSystems: ['WebApp-1', 'Database-Server-1'],\r\n        discoveryDate: new Date().toISOString(),\r\n        status: 'open',\r\n        securityEventId: securityEventId,\r\n        remediation: {\r\n          steps: [\r\n            'Update input validation',\r\n            'Implement parameterized queries',\r\n            'Apply security patches',\r\n          ],\r\n          estimatedEffort: '4 hours',\r\n          priority: 'high',\r\n        },\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/vulnerability-management/vulnerabilities')\r\n        .set('Authorization', `Bearer ${userToken}`)\r\n        .send(vulnerabilityData)\r\n        .expect(201);\r\n\r\n      expect(response.body.data).toEqual(\r\n        expect.objectContaining({\r\n          id: expect.any(String),\r\n          cveId: 'CVE-2024-0001',\r\n          severity: 'high',\r\n          status: 'open',\r\n          securityEventId: securityEventId,\r\n        })\r\n      );\r\n\r\n      vulnerabilityId = response.body.data.id;\r\n    });\r\n\r\n    it('should create threat intelligence data', async () => {\r\n      const threatIntelData = {\r\n        threatType: 'malware',\r\n        severity: 'critical',\r\n        title: 'Advanced Persistent Threat Campaign',\r\n        description: 'Sophisticated malware campaign targeting web applications',\r\n        indicators: [\r\n          {\r\n            type: 'ip',\r\n            value: '*************',\r\n            confidence: 'high',\r\n          },\r\n          {\r\n            type: 'domain',\r\n            value: 'malicious-domain.com',\r\n            confidence: 'medium',\r\n          },\r\n        ],\r\n        ttps: [\r\n          'T1190 - Exploit Public-Facing Application',\r\n          'T1505.003 - Web Shell',\r\n        ],\r\n        sources: ['Internal Analysis', 'Threat Feed Alpha'],\r\n        firstSeen: new Date().toISOString(),\r\n        lastSeen: new Date().toISOString(),\r\n        isActive: true,\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/threat-intelligence/threats')\r\n        .set('Authorization', `Bearer ${userToken}`)\r\n        .send(threatIntelData)\r\n        .expect(201);\r\n\r\n      expect(response.body.data).toEqual(\r\n        expect.objectContaining({\r\n          id: expect.any(String),\r\n          threatType: 'malware',\r\n          severity: 'critical',\r\n          isActive: true,\r\n        })\r\n      );\r\n\r\n      threatIntelId = response.body.data.id;\r\n    });\r\n\r\n    it('should correlate security event with threat intelligence', async () => {\r\n      const correlationData = {\r\n        securityEventId: securityEventId,\r\n        threatIntelligenceId: threatIntelId,\r\n        correlationType: 'ip_match',\r\n        confidence: 'high',\r\n        matchedIndicators: ['*************'],\r\n        analysisNotes: 'Source IP matches known malicious infrastructure',\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/analytics/correlations')\r\n        .set('Authorization', `Bearer ${userToken}`)\r\n        .send(correlationData)\r\n        .expect(201);\r\n\r\n      expect(response.body.data).toEqual(\r\n        expect.objectContaining({\r\n          id: expect.any(String),\r\n          securityEventId: securityEventId,\r\n          threatIntelligenceId: threatIntelId,\r\n          correlationType: 'ip_match',\r\n          confidence: 'high',\r\n        })\r\n      );\r\n    });\r\n\r\n    it('should generate automated response recommendations', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/automation/generate-response')\r\n        .set('Authorization', `Bearer ${userToken}`)\r\n        .send({\r\n          securityEventId: securityEventId,\r\n          vulnerabilityId: vulnerabilityId,\r\n          threatIntelligenceId: threatIntelId,\r\n        })\r\n        .expect(201);\r\n\r\n      expect(response.body.data).toEqual(\r\n        expect.objectContaining({\r\n          id: expect.any(String),\r\n          securityEventId: securityEventId,\r\n          recommendations: expect.arrayContaining([\r\n            expect.objectContaining({\r\n              action: expect.any(String),\r\n              priority: expect.any(String),\r\n              description: expect.any(String),\r\n            }),\r\n          ]),\r\n          automationLevel: expect.any(String),\r\n          estimatedImpact: expect.any(String),\r\n        })\r\n      );\r\n    });\r\n\r\n    it('should update security event status through workflow', async () => {\r\n      const updateData = {\r\n        status: 'investigating',\r\n        assignedTo: adminUserId,\r\n        notes: 'Assigned to security team for investigation',\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .put(`/api/v1/security-events/${securityEventId}`)\r\n        .set('Authorization', `Bearer ${adminToken}`)\r\n        .send(updateData)\r\n        .expect(200);\r\n\r\n      expect(response.body.data).toEqual(\r\n        expect.objectContaining({\r\n          id: securityEventId,\r\n          status: 'investigating',\r\n          assignedTo: adminUserId,\r\n        })\r\n      );\r\n    });\r\n\r\n    it('should track remediation progress', async () => {\r\n      const remediationData = {\r\n        vulnerabilityId: vulnerabilityId,\r\n        status: 'in_progress',\r\n        completedSteps: ['Update input validation'],\r\n        remainingSteps: ['Implement parameterized queries', 'Apply security patches'],\r\n        estimatedCompletion: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),\r\n        assignedTo: adminUserId,\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/vulnerability-management/remediation')\r\n        .set('Authorization', `Bearer ${adminToken}`)\r\n        .send(remediationData)\r\n        .expect(201);\r\n\r\n      expect(response.body.data).toEqual(\r\n        expect.objectContaining({\r\n          id: expect.any(String),\r\n          vulnerabilityId: vulnerabilityId,\r\n          status: 'in_progress',\r\n          completedSteps: ['Update input validation'],\r\n        })\r\n      );\r\n    });\r\n\r\n    it('should close the security event after remediation', async () => {\r\n      const closeData = {\r\n        status: 'resolved',\r\n        resolution: 'Vulnerability patched and verified',\r\n        resolvedBy: adminUserId,\r\n        resolvedAt: new Date().toISOString(),\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .put(`/api/v1/security-events/${securityEventId}`)\r\n        .set('Authorization', `Bearer ${adminToken}`)\r\n        .send(closeData)\r\n        .expect(200);\r\n\r\n      expect(response.body.data).toEqual(\r\n        expect.objectContaining({\r\n          id: securityEventId,\r\n          status: 'resolved',\r\n          resolvedBy: adminUserId,\r\n        })\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('Analytics and Reporting Workflow', () => {\r\n    it('should generate security metrics dashboard', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/analytics/dashboard')\r\n        .query({\r\n          timeRange: '7d',\r\n          includeMetrics: 'security_events,vulnerabilities,threats',\r\n        })\r\n        .set('Authorization', `Bearer ${userToken}`)\r\n        .expect(200);\r\n\r\n      expect(response.body.data).toEqual({\r\n        timeRange: '7d',\r\n        metrics: {\r\n          securityEvents: {\r\n            total: expect.any(Number),\r\n            byStatus: expect.any(Object),\r\n            bySeverity: expect.any(Object),\r\n            trend: expect.any(Array),\r\n          },\r\n          vulnerabilities: {\r\n            total: expect.any(Number),\r\n            byStatus: expect.any(Object),\r\n            bySeverity: expect.any(Object),\r\n            avgResolutionTime: expect.any(Number),\r\n          },\r\n          threats: {\r\n            total: expect.any(Number),\r\n            active: expect.any(Number),\r\n            byType: expect.any(Object),\r\n            topIndicators: expect.any(Array),\r\n          },\r\n        },\r\n        summary: {\r\n          riskScore: expect.any(Number),\r\n          trendDirection: expect.any(String),\r\n          criticalIssues: expect.any(Number),\r\n        },\r\n      });\r\n    });\r\n\r\n    it('should generate vulnerability assessment report', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/analytics/reports/vulnerability-assessment')\r\n        .set('Authorization', `Bearer ${adminToken}`)\r\n        .send({\r\n          scope: 'all_systems',\r\n          includeRemediation: true,\r\n          format: 'json',\r\n        })\r\n        .expect(201);\r\n\r\n      expect(response.body.data).toEqual({\r\n        reportId: expect.any(String),\r\n        generatedAt: expect.any(String),\r\n        scope: 'all_systems',\r\n        summary: {\r\n          totalVulnerabilities: expect.any(Number),\r\n          criticalCount: expect.any(Number),\r\n          highCount: expect.any(Number),\r\n          mediumCount: expect.any(Number),\r\n          lowCount: expect.any(Number),\r\n        },\r\n        vulnerabilities: expect.any(Array),\r\n        recommendations: expect.any(Array),\r\n        status: 'completed',\r\n      });\r\n    });\r\n\r\n    it('should generate threat landscape analysis', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/threat-intelligence/landscape')\r\n        .query({\r\n          timeframe: '30d',\r\n          threatTypes: 'malware,phishing,apt',\r\n        })\r\n        .set('Authorization', `Bearer ${userToken}`)\r\n        .expect(200);\r\n\r\n      expect(response.body.data).toEqual({\r\n        timeframe: '30d',\r\n        threatTypes: ['malware', 'phishing', 'apt'],\r\n        analysis: {\r\n          totalThreats: expect.any(Number),\r\n          emergingThreats: expect.any(Array),\r\n          threatTrends: expect.any(Array),\r\n          geographicDistribution: expect.any(Object),\r\n          industryTargeting: expect.any(Object),\r\n        },\r\n        recommendations: expect.any(Array),\r\n        riskAssessment: {\r\n          overallRisk: expect.any(String),\r\n          keyRisks: expect.any(Array),\r\n          mitigationStrategies: expect.any(Array),\r\n        },\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('User Management and RBAC Workflow', () => {\r\n    let newUserId: string;\r\n\r\n    it('should create a new user with specific roles', async () => {\r\n      const userData = {\r\n        email: '<EMAIL>',\r\n        password: 'AnalystPassword123!',\r\n        name: 'Security Analyst',\r\n        roles: ['analyst'],\r\n        permissions: ['read:security_events', 'write:security_events', 'read:vulnerabilities'],\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/admin/users')\r\n        .set('Authorization', `Bearer ${adminToken}`)\r\n        .send(userData)\r\n        .expect(201);\r\n\r\n      expect(response.body.data.user).toEqual(\r\n        expect.objectContaining({\r\n          id: expect.any(String),\r\n          email: '<EMAIL>',\r\n          name: 'Security Analyst',\r\n          roles: ['analyst'],\r\n          permissions: expect.arrayContaining(['read:security_events']),\r\n        })\r\n      );\r\n\r\n      newUserId = response.body.data.user.id;\r\n    });\r\n\r\n    it('should verify role-based access for the new user', async () => {\r\n      // Login as the new analyst user\r\n      const loginResponse = await request(app.getHttpServer())\r\n        .post('/api/v1/auth/login')\r\n        .send({\r\n          email: '<EMAIL>',\r\n          password: 'AnalystPassword123!',\r\n        })\r\n        .expect(200);\r\n\r\n      const analystToken = loginResponse.body.data.tokens.accessToken;\r\n\r\n      // Should be able to read security events\r\n      await request(app.getHttpServer())\r\n        .get('/api/v1/security-events')\r\n        .set('Authorization', `Bearer ${analystToken}`)\r\n        .expect(200);\r\n\r\n      // Should NOT be able to access admin endpoints\r\n      await request(app.getHttpServer())\r\n        .get('/api/v1/admin/users')\r\n        .set('Authorization', `Bearer ${analystToken}`)\r\n        .expect(403);\r\n\r\n      // Should NOT be able to delete security events (no delete permission)\r\n      await request(app.getHttpServer())\r\n        .delete('/api/v1/security-events/some-id')\r\n        .set('Authorization', `Bearer ${analystToken}`)\r\n        .expect(403);\r\n    });\r\n\r\n    it('should update user permissions', async () => {\r\n      const updateData = {\r\n        permissions: [\r\n          'read:security_events',\r\n          'write:security_events',\r\n          'delete:security_events',\r\n          'read:vulnerabilities',\r\n          'write:vulnerabilities',\r\n        ],\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .put(`/api/v1/admin/users/${newUserId}/permissions`)\r\n        .set('Authorization', `Bearer ${adminToken}`)\r\n        .send(updateData)\r\n        .expect(200);\r\n\r\n      expect(response.body.data.permissions).toEqual(\r\n        expect.arrayContaining([\r\n          'read:security_events',\r\n          'write:security_events',\r\n          'delete:security_events',\r\n        ])\r\n      );\r\n    });\r\n\r\n    it('should audit user permission changes', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get(`/api/v1/admin/audit/user-changes/${newUserId}`)\r\n        .set('Authorization', `Bearer ${adminToken}`)\r\n        .expect(200);\r\n\r\n      expect(response.body.data.auditLog).toEqual(\r\n        expect.arrayContaining([\r\n          expect.objectContaining({\r\n            action: 'user_created',\r\n            performedBy: adminUserId,\r\n            timestamp: expect.any(String),\r\n          }),\r\n          expect.objectContaining({\r\n            action: 'permissions_updated',\r\n            performedBy: adminUserId,\r\n            timestamp: expect.any(String),\r\n          }),\r\n        ])\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('System Configuration and Health Workflow', () => {\r\n    it('should update system configuration', async () => {\r\n      const configData = {\r\n        alerting: {\r\n          emailNotifications: true,\r\n          webhookUrl: 'https://hooks.example.com/security-alerts',\r\n          severityThreshold: 'medium',\r\n        },\r\n        scanning: {\r\n          automaticScanning: true,\r\n          scanInterval: '24h',\r\n          scanDepth: 'comprehensive',\r\n        },\r\n        retention: {\r\n          securityEvents: '90d',\r\n          auditLogs: '365d',\r\n          threatIntelligence: '180d',\r\n        },\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .put('/api/v1/admin/system/configuration')\r\n        .set('Authorization', `Bearer ${adminToken}`)\r\n        .send(configData)\r\n        .expect(200);\r\n\r\n      expect(response.body.data.configuration).toEqual(\r\n        expect.objectContaining({\r\n          alerting: expect.objectContaining({\r\n            emailNotifications: true,\r\n            severityThreshold: 'medium',\r\n          }),\r\n          scanning: expect.objectContaining({\r\n            automaticScanning: true,\r\n            scanInterval: '24h',\r\n          }),\r\n        })\r\n      );\r\n    });\r\n\r\n    it('should perform comprehensive system health check', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/health/comprehensive')\r\n        .set('Authorization', `Bearer ${adminToken}`)\r\n        .expect(200);\r\n\r\n      expect(response.body).toEqual({\r\n        status: expect.any(String),\r\n        timestamp: expect.any(String),\r\n        components: {\r\n          application: expect.any(String),\r\n          database: expect.any(String),\r\n          redis: expect.any(String),\r\n          externalServices: expect.any(String),\r\n          fileSystem: expect.any(String),\r\n          memory: expect.any(String),\r\n          cpu: expect.any(String),\r\n        },\r\n        metrics: {\r\n          uptime: expect.any(Number),\r\n          responseTime: expect.any(Number),\r\n          throughput: expect.any(Number),\r\n          errorRate: expect.any(Number),\r\n        },\r\n        dependencies: expect.any(Array),\r\n        recommendations: expect.any(Array),\r\n      });\r\n    });\r\n\r\n    it('should validate system performance metrics', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/metrics/performance')\r\n        .set('Authorization', `Bearer ${adminToken}`)\r\n        .expect(200);\r\n\r\n      expect(response.body).toEqual({\r\n        timestamp: expect.any(String),\r\n        application: {\r\n          requestsPerSecond: expect.any(Number),\r\n          averageResponseTime: expect.any(Number),\r\n          errorRate: expect.any(Number),\r\n          activeConnections: expect.any(Number),\r\n        },\r\n        system: {\r\n          cpuUsage: expect.any(Number),\r\n          memoryUsage: expect.any(Number),\r\n          diskUsage: expect.any(Number),\r\n          networkIO: expect.any(Object),\r\n        },\r\n        database: {\r\n          connectionPoolSize: expect.any(Number),\r\n          activeQueries: expect.any(Number),\r\n          slowQueries: expect.any(Number),\r\n          cacheHitRatio: expect.any(Number),\r\n        },\r\n        business: {\r\n          securityEventsProcessed: expect.any(Number),\r\n          vulnerabilitiesDetected: expect.any(Number),\r\n          threatsAnalyzed: expect.any(Number),\r\n          alertsGenerated: expect.any(Number),\r\n        },\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Data Export and Backup Workflow', () => {\r\n    it('should export security events data', async () => {\r\n      const exportRequest = {\r\n        dataType: 'security_events',\r\n        format: 'json',\r\n        dateRange: {\r\n          start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),\r\n          end: new Date().toISOString(),\r\n        },\r\n        filters: {\r\n          severity: ['high', 'critical'],\r\n          status: ['open', 'investigating'],\r\n        },\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/admin/export')\r\n        .set('Authorization', `Bearer ${adminToken}`)\r\n        .send(exportRequest)\r\n        .expect(202);\r\n\r\n      expect(response.body).toEqual({\r\n        success: true,\r\n        data: {\r\n          exportId: expect.any(String),\r\n          status: 'processing',\r\n          estimatedCompletion: expect.any(String),\r\n        },\r\n        message: 'Export job started successfully',\r\n        timestamp: expect.any(String),\r\n      });\r\n    });\r\n\r\n    it('should check export job status', async () => {\r\n      // First create an export job\r\n      const exportResponse = await request(app.getHttpServer())\r\n        .post('/api/v1/admin/export')\r\n        .set('Authorization', `Bearer ${adminToken}`)\r\n        .send({\r\n          dataType: 'vulnerabilities',\r\n          format: 'csv',\r\n          dateRange: {\r\n            start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),\r\n            end: new Date().toISOString(),\r\n          },\r\n        })\r\n        .expect(202);\r\n\r\n      const exportId = exportResponse.body.data.exportId;\r\n\r\n      // Check the status\r\n      const statusResponse = await request(app.getHttpServer())\r\n        .get(`/api/v1/admin/export/${exportId}/status`)\r\n        .set('Authorization', `Bearer ${adminToken}`)\r\n        .expect(200);\r\n\r\n      expect(statusResponse.body.data).toEqual({\r\n        exportId: exportId,\r\n        status: expect.stringMatching(/^(processing|completed|failed)$/),\r\n        progress: expect.any(Number),\r\n        createdAt: expect.any(String),\r\n        completedAt: expect.any(String),\r\n        downloadUrl: expect.any(String),\r\n        fileSize: expect.any(Number),\r\n      });\r\n    });\r\n\r\n    it('should create system backup', async () => {\r\n      const backupRequest = {\r\n        backupType: 'full',\r\n        includeData: true,\r\n        includeConfiguration: true,\r\n        compression: true,\r\n        encryption: true,\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/admin/backup')\r\n        .set('Authorization', `Bearer ${adminToken}`)\r\n        .send(backupRequest)\r\n        .expect(202);\r\n\r\n      expect(response.body).toEqual({\r\n        success: true,\r\n        data: {\r\n          backupId: expect.any(String),\r\n          status: 'initiated',\r\n          backupType: 'full',\r\n          estimatedSize: expect.any(String),\r\n          estimatedDuration: expect.any(String),\r\n        },\r\n        message: 'Backup process initiated successfully',\r\n        timestamp: expect.any(String),\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('End-to-End Integration Validation', () => {\r\n    it('should validate complete data flow integrity', async () => {\r\n      // Create a security event\r\n      const eventResponse = await request(app.getHttpServer())\r\n        .post('/api/v1/security-events')\r\n        .set('Authorization', `Bearer ${userToken}`)\r\n        .send({\r\n          eventType: 'intrusion_attempt',\r\n          severity: 'critical',\r\n          description: 'Unauthorized access attempt detected',\r\n          sourceIp: '***********',\r\n          targetIp: '**********',\r\n          timestamp: new Date().toISOString(),\r\n        })\r\n        .expect(201);\r\n\r\n      const eventId = eventResponse.body.data.id;\r\n\r\n      // Verify event appears in analytics\r\n      const analyticsResponse = await request(app.getHttpServer())\r\n        .get('/api/v1/analytics/recent-events')\r\n        .query({ limit: 10 })\r\n        .set('Authorization', `Bearer ${userToken}`)\r\n        .expect(200);\r\n\r\n      expect(analyticsResponse.body.data.events).toEqual(\r\n        expect.arrayContaining([\r\n          expect.objectContaining({\r\n            id: eventId,\r\n            eventType: 'intrusion_attempt',\r\n            severity: 'critical',\r\n          }),\r\n        ])\r\n      );\r\n\r\n      // Verify event triggers automated analysis\r\n      const analysisResponse = await request(app.getHttpServer())\r\n        .get(`/api/v1/automation/analysis/${eventId}`)\r\n        .set('Authorization', `Bearer ${userToken}`)\r\n        .expect(200);\r\n\r\n      expect(analysisResponse.body.data).toEqual(\r\n        expect.objectContaining({\r\n          eventId: eventId,\r\n          analysisStatus: expect.any(String),\r\n          riskScore: expect.any(Number),\r\n          recommendations: expect.any(Array),\r\n        })\r\n      );\r\n\r\n      // Verify audit trail is created\r\n      const auditResponse = await request(app.getHttpServer())\r\n        .get('/api/v1/admin/audit/security-events')\r\n        .query({ eventId: eventId })\r\n        .set('Authorization', `Bearer ${adminToken}`)\r\n        .expect(200);\r\n\r\n      expect(auditResponse.body.data.auditEntries).toEqual(\r\n        expect.arrayContaining([\r\n          expect.objectContaining({\r\n            action: 'security_event_created',\r\n            resourceId: eventId,\r\n            performedBy: regularUserId,\r\n            timestamp: expect.any(String),\r\n          }),\r\n        ])\r\n      );\r\n    });\r\n\r\n    it('should validate system resilience under load', async () => {\r\n      // Create multiple concurrent operations\r\n      const operations = [\r\n        // Create security events\r\n        ...Array.from({ length: 5 }, (_, i) =>\r\n          request(app.getHttpServer())\r\n            .post('/api/v1/security-events')\r\n            .set('Authorization', `Bearer ${userToken}`)\r\n            .send({\r\n              eventType: 'malware_detected',\r\n              severity: 'high',\r\n              description: `Malware detection ${i + 1}`,\r\n              sourceIp: `192.168.1.${100 + i}`,\r\n              timestamp: new Date().toISOString(),\r\n            })\r\n        ),\r\n        // Create vulnerabilities\r\n        ...Array.from({ length: 3 }, (_, i) =>\r\n          request(app.getHttpServer())\r\n            .post('/api/v1/vulnerability-management/vulnerabilities')\r\n            .set('Authorization', `Bearer ${userToken}`)\r\n            .send({\r\n              cveId: `CVE-2024-${1000 + i}`,\r\n              title: `Test Vulnerability ${i + 1}`,\r\n              severity: 'medium',\r\n              cvssScore: 6.5,\r\n              status: 'open',\r\n            })\r\n        ),\r\n        // Generate reports\r\n        ...Array.from({ length: 2 }, () =>\r\n          request(app.getHttpServer())\r\n            .get('/api/v1/analytics/dashboard')\r\n            .set('Authorization', `Bearer ${userToken}`)\r\n        ),\r\n      ];\r\n\r\n      const responses = await Promise.all(\r\n        operations.map(op =>\r\n          op.then(res => ({ status: res.status, success: true }))\r\n            .catch(err => ({ status: err.response?.status || 500, success: false }))\r\n        )\r\n      );\r\n\r\n      // Most operations should succeed\r\n      const successCount = responses.filter(r => r.success && [200, 201].includes(r.status)).length;\r\n      const totalOperations = operations.length;\r\n      \r\n      expect(successCount / totalOperations).toBeGreaterThan(0.8); // 80% success rate\r\n    });\r\n\r\n    it('should validate data consistency across services', async () => {\r\n      // Create related data across multiple services\r\n      const eventResponse = await request(app.getHttpServer())\r\n        .post('/api/v1/security-events')\r\n        .set('Authorization', `Bearer ${userToken}`)\r\n        .send({\r\n          eventType: 'data_breach',\r\n          severity: 'critical',\r\n          description: 'Potential data breach detected',\r\n          sourceIp: '************',\r\n          timestamp: new Date().toISOString(),\r\n        })\r\n        .expect(201);\r\n\r\n      const eventId = eventResponse.body.data.id;\r\n\r\n      const vulnResponse = await request(app.getHttpServer())\r\n        .post('/api/v1/vulnerability-management/vulnerabilities')\r\n        .set('Authorization', `Bearer ${userToken}`)\r\n        .send({\r\n          cveId: 'CVE-2024-9999',\r\n          title: 'Data Exposure Vulnerability',\r\n          severity: 'critical',\r\n          securityEventId: eventId,\r\n          status: 'open',\r\n        })\r\n        .expect(201);\r\n\r\n      const vulnId = vulnResponse.body.data.id;\r\n\r\n      // Verify cross-references are maintained\r\n      const eventDetailResponse = await request(app.getHttpServer())\r\n        .get(`/api/v1/security-events/${eventId}`)\r\n        .set('Authorization', `Bearer ${userToken}`)\r\n        .expect(200);\r\n\r\n      const vulnDetailResponse = await request(app.getHttpServer())\r\n        .get(`/api/v1/vulnerability-management/vulnerabilities/${vulnId}`)\r\n        .set('Authorization', `Bearer ${userToken}`)\r\n        .expect(200);\r\n\r\n      expect(vulnDetailResponse.body.data.securityEventId).toBe(eventId);\r\n      \r\n      // Verify analytics reflect the relationships\r\n      const correlationResponse = await request(app.getHttpServer())\r\n        .get('/api/v1/analytics/correlations')\r\n        .query({ securityEventId: eventId })\r\n        .set('Authorization', `Bearer ${userToken}`)\r\n        .expect(200);\r\n\r\n      expect(correlationResponse.body.data.correlations).toEqual(\r\n        expect.arrayContaining([\r\n          expect.objectContaining({\r\n            securityEventId: eventId,\r\n            relatedVulnerabilities: expect.arrayContaining([vulnId]),\r\n          }),\r\n        ])\r\n      );\r\n    });\r\n  });\r\n});"], "version": 3}