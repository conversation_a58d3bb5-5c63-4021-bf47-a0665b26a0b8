0bcd00c6aae2be63bda3a19b4197e9d0
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const response_action_entity_1 = require("../response-action.entity");
const shared_kernel_1 = require("../../../../../shared-kernel");
const action_type_enum_1 = require("../../enums/action-type.enum");
const action_status_enum_1 = require("../../enums/action-status.enum");
const response_action_created_domain_event_1 = require("../../events/response-action-created.domain-event");
const response_action_status_changed_domain_event_1 = require("../../events/response-action-status-changed.domain-event");
const response_action_executed_domain_event_1 = require("../../events/response-action-executed.domain-event");
const response_action_failed_domain_event_1 = require("../../events/response-action-failed.domain-event");
const response_action_rolled_back_domain_event_1 = require("../../events/response-action-rolled-back.domain-event");
describe('ResponseAction Entity', () => {
    let validProps;
    beforeEach(() => {
        validProps = {
            actionType: action_type_enum_1.ActionType.BLOCK_IP,
            status: action_status_enum_1.ActionStatus.PENDING,
            title: 'Block malicious IP address',
            description: 'Block IP address identified as malicious',
            parameters: { ipAddress: '*************' },
            target: {
                type: 'system',
                id: 'firewall-001',
                name: 'Main Firewall',
            },
            priority: 'high',
            isAutomated: true,
            isReversible: true,
            estimatedDurationMinutes: 5,
            requiredPermissions: ['network.firewall.write'],
            approvalRequired: false,
            successCriteria: ['IP address blocked in firewall', 'Traffic from IP stopped'],
            retryCount: 0,
            maxRetries: 3,
            retryDelayMinutes: 5,
            rollbackInfo: {
                canRollback: true,
                rollbackSteps: ['Remove IP from block list', 'Update firewall rules'],
            },
            rolledBack: false,
            tags: ['security', 'containment'],
            metadata: { source: 'threat-detection' },
            childActionIds: [],
            timeoutMinutes: 30,
            timedOut: false,
        };
    });
    describe('creation', () => {
        it('should create a valid ResponseAction', () => {
            const action = response_action_entity_1.ResponseAction.create(validProps);
            expect(action).toBeInstanceOf(response_action_entity_1.ResponseAction);
            expect(action.actionType).toBe(action_type_enum_1.ActionType.BLOCK_IP);
            expect(action.status).toBe(action_status_enum_1.ActionStatus.PENDING);
            expect(action.title).toBe('Block malicious IP address');
            expect(action.priority).toBe('high');
            expect(action.isAutomated).toBe(true);
            expect(action.isReversible).toBe(true);
        });
        it('should generate ResponseActionCreatedDomainEvent on creation', () => {
            const action = response_action_entity_1.ResponseAction.create(validProps);
            const events = action.getUnpublishedEvents();
            expect(events).toHaveLength(1);
            expect(events[0]).toBeInstanceOf(response_action_created_domain_event_1.ResponseActionCreatedDomainEvent);
            const createdEvent = events[0];
            expect(createdEvent.actionType).toBe(action_type_enum_1.ActionType.BLOCK_IP);
            expect(createdEvent.title).toBe('Block malicious IP address');
            expect(createdEvent.priority).toBe('high');
        });
        it('should create with custom ID', () => {
            const customId = shared_kernel_1.UniqueEntityId.generate();
            const action = response_action_entity_1.ResponseAction.create(validProps, customId);
            expect(action.id.equals(customId)).toBe(true);
        });
    });
    describe('validation', () => {
        it('should throw error if actionType is missing', () => {
            const invalidProps = { ...validProps };
            delete invalidProps.actionType;
            expect(() => response_action_entity_1.ResponseAction.create(invalidProps)).toThrow('ResponseAction must have an action type');
        });
        it('should throw error if status is missing', () => {
            const invalidProps = { ...validProps };
            delete invalidProps.status;
            expect(() => response_action_entity_1.ResponseAction.create(invalidProps)).toThrow('ResponseAction must have a status');
        });
        it('should throw error if title is empty', () => {
            const invalidProps = { ...validProps, title: '' };
            expect(() => response_action_entity_1.ResponseAction.create(invalidProps)).toThrow('ResponseAction must have a non-empty title');
        });
        it('should throw error if title is too long', () => {
            const invalidProps = { ...validProps, title: 'a'.repeat(201) };
            expect(() => response_action_entity_1.ResponseAction.create(invalidProps)).toThrow('ResponseAction title cannot exceed 200 characters');
        });
        it('should throw error if description is too long', () => {
            const invalidProps = { ...validProps, description: 'a'.repeat(2001) };
            expect(() => response_action_entity_1.ResponseAction.create(invalidProps)).toThrow('ResponseAction description cannot exceed 2000 characters');
        });
        it('should throw error if parameters is missing', () => {
            const invalidProps = { ...validProps };
            delete invalidProps.parameters;
            expect(() => response_action_entity_1.ResponseAction.create(invalidProps)).toThrow('ResponseAction must have parameters');
        });
        it('should throw error if priority is missing', () => {
            const invalidProps = { ...validProps };
            delete invalidProps.priority;
            expect(() => response_action_entity_1.ResponseAction.create(invalidProps)).toThrow('ResponseAction must have a priority');
        });
        it('should throw error if isAutomated is undefined', () => {
            const invalidProps = { ...validProps };
            delete invalidProps.isAutomated;
            expect(() => response_action_entity_1.ResponseAction.create(invalidProps)).toThrow('ResponseAction must specify if it is automated');
        });
        it('should throw error if isReversible is undefined', () => {
            const invalidProps = { ...validProps };
            delete invalidProps.isReversible;
            expect(() => response_action_entity_1.ResponseAction.create(invalidProps)).toThrow('ResponseAction must specify if it is reversible');
        });
        it('should throw error if requiredPermissions is not an array', () => {
            const invalidProps = { ...validProps, requiredPermissions: 'not-an-array' };
            expect(() => response_action_entity_1.ResponseAction.create(invalidProps)).toThrow('ResponseAction must have required permissions array');
        });
        it('should throw error if approvalRequired is undefined', () => {
            const invalidProps = { ...validProps };
            delete invalidProps.approvalRequired;
            expect(() => response_action_entity_1.ResponseAction.create(invalidProps)).toThrow('ResponseAction must specify if approval is required');
        });
        it('should throw error if successCriteria is not an array', () => {
            const invalidProps = { ...validProps, successCriteria: 'not-an-array' };
            expect(() => response_action_entity_1.ResponseAction.create(invalidProps)).toThrow('ResponseAction must have success criteria array');
        });
        it('should throw error if retryCount is negative', () => {
            const invalidProps = { ...validProps, retryCount: -1 };
            expect(() => response_action_entity_1.ResponseAction.create(invalidProps)).toThrow('Retry count cannot be negative');
        });
        it('should throw error if maxRetries is negative', () => {
            const invalidProps = { ...validProps, maxRetries: -1 };
            expect(() => response_action_entity_1.ResponseAction.create(invalidProps)).toThrow('Max retries cannot be negative');
        });
        it('should throw error if retryDelayMinutes is negative', () => {
            const invalidProps = { ...validProps, retryDelayMinutes: -1 };
            expect(() => response_action_entity_1.ResponseAction.create(invalidProps)).toThrow('Retry delay cannot be negative');
        });
        it('should throw error if tags is not an array', () => {
            const invalidProps = { ...validProps, tags: 'not-an-array' };
            expect(() => response_action_entity_1.ResponseAction.create(invalidProps)).toThrow('ResponseAction must have tags array');
        });
        it('should throw error if too many tags', () => {
            const invalidProps = { ...validProps, tags: Array(21).fill('tag') };
            expect(() => response_action_entity_1.ResponseAction.create(invalidProps)).toThrow('ResponseAction cannot have more than 20 tags');
        });
        it('should throw error if metadata is missing', () => {
            const invalidProps = { ...validProps };
            delete invalidProps.metadata;
            expect(() => response_action_entity_1.ResponseAction.create(invalidProps)).toThrow('ResponseAction must have metadata object');
        });
        it('should throw error if childActionIds is not an array', () => {
            const invalidProps = { ...validProps, childActionIds: 'not-an-array' };
            expect(() => response_action_entity_1.ResponseAction.create(invalidProps)).toThrow('ResponseAction must have child action IDs array');
        });
        it('should throw error if timeoutMinutes is not positive', () => {
            const invalidProps = { ...validProps, timeoutMinutes: 0 };
            expect(() => response_action_entity_1.ResponseAction.create(invalidProps)).toThrow('Timeout minutes must be positive');
        });
        it('should throw error if timedOut is undefined', () => {
            const invalidProps = { ...validProps };
            delete invalidProps.timedOut;
            expect(() => response_action_entity_1.ResponseAction.create(invalidProps)).toThrow('ResponseAction must specify if it timed out');
        });
        it('should throw error if rolledBack is undefined', () => {
            const invalidProps = { ...validProps };
            delete invalidProps.rolledBack;
            expect(() => response_action_entity_1.ResponseAction.create(invalidProps)).toThrow('ResponseAction must specify if it was rolled back');
        });
    });
    describe('status changes', () => {
        let action;
        beforeEach(() => {
            action = response_action_entity_1.ResponseAction.create(validProps);
            action.clearEvents(); // Clear creation event
        });
        it('should change status successfully', () => {
            action.changeStatus(action_status_enum_1.ActionStatus.APPROVED, 'admin', 'Approved for execution');
            expect(action.status).toBe(action_status_enum_1.ActionStatus.APPROVED);
            expect(action.approvedBy).toBe('admin');
            expect(action.approvedAt).toBeDefined();
            const events = action.getUnpublishedEvents();
            expect(events).toHaveLength(1);
            expect(events[0]).toBeInstanceOf(response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent);
        });
        it('should not change status if same status', () => {
            action.changeStatus(action_status_enum_1.ActionStatus.PENDING);
            const events = action.getUnpublishedEvents();
            expect(events).toHaveLength(0);
        });
        it('should throw error for invalid status transition', () => {
            expect(() => action.changeStatus(action_status_enum_1.ActionStatus.COMPLETED)).toThrow('Invalid status transition');
        });
        it('should handle timeout status change', () => {
            action.changeStatus(action_status_enum_1.ActionStatus.APPROVED);
            action.changeStatus(action_status_enum_1.ActionStatus.QUEUED);
            action.changeStatus(action_status_enum_1.ActionStatus.EXECUTING);
            action.changeStatus(action_status_enum_1.ActionStatus.TIMEOUT);
            expect(action.status).toBe(action_status_enum_1.ActionStatus.TIMEOUT);
            expect(action.timedOut).toBe(true);
            expect(action.timedOutAt).toBeDefined();
        });
    });
    describe('execution', () => {
        let action;
        beforeEach(() => {
            action = response_action_entity_1.ResponseAction.create({ ...validProps, status: action_status_enum_1.ActionStatus.EXECUTING });
            action.clearEvents();
        });
        it('should execute successfully', () => {
            const executionResults = { blocked: true, ruleId: 'rule-123' };
            action.execute('system', executionResults);
            expect(action.status).toBe(action_status_enum_1.ActionStatus.COMPLETED);
            expect(action.executedBy).toBe('system');
            expect(action.executedAt).toBeDefined();
            expect(action.executionResults).toEqual(executionResults);
            expect(action.successCriteriaMet).toBe(true);
            const events = action.getUnpublishedEvents();
            expect(events).toHaveLength(1);
            expect(events[0]).toBeInstanceOf(response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent);
        });
        it('should throw error if not in executing status', () => {
            const pendingAction = response_action_entity_1.ResponseAction.create(validProps);
            expect(() => pendingAction.execute('system')).toThrow('Action must be in executing status to be executed');
        });
    });
    describe('failure handling', () => {
        let action;
        beforeEach(() => {
            action = response_action_entity_1.ResponseAction.create({ ...validProps, status: action_status_enum_1.ActionStatus.EXECUTING });
            action.clearEvents();
        });
        it('should handle failure', () => {
            const error = 'Network timeout';
            action.fail(error, 'system');
            expect(action.status).toBe(action_status_enum_1.ActionStatus.FAILED);
            expect(action.executionError).toBe(error);
            expect(action.successCriteriaMet).toBe(false);
            const events = action.getUnpublishedEvents();
            expect(events).toHaveLength(1);
            expect(events[0]).toBeInstanceOf(response_action_failed_domain_event_1.ResponseActionFailedDomainEvent);
        });
        it('should throw error if not in executing or retrying status', () => {
            const pendingAction = response_action_entity_1.ResponseAction.create(validProps);
            expect(() => pendingAction.fail('error')).toThrow('Action must be in executing or retrying status to fail');
        });
    });
    describe('retry logic', () => {
        let action;
        beforeEach(() => {
            action = response_action_entity_1.ResponseAction.create({ ...validProps, status: action_status_enum_1.ActionStatus.FAILED, maxRetries: 3, executedAt: new Date() });
            action.clearEvents();
        });
        it('should retry successfully', () => {
            // Set executedAt for failed action
            const failedAction = response_action_entity_1.ResponseAction.create({
                ...validProps,
                status: action_status_enum_1.ActionStatus.FAILED,
                maxRetries: 3,
                executedAt: new Date()
            });
            failedAction.clearEvents();
            failedAction.retry('admin');
            expect(failedAction.status).toBe(action_status_enum_1.ActionStatus.RETRYING);
            expect(failedAction.retryCount).toBe(1);
            expect(failedAction.executionError).toBeUndefined();
            expect(failedAction.nextRetryAt).toBeDefined();
            const events = failedAction.getUnpublishedEvents();
            expect(events).toHaveLength(1);
            expect(events[0]).toBeInstanceOf(response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent);
        });
        it('should not retry if max retries exceeded', () => {
            const maxRetriesAction = response_action_entity_1.ResponseAction.create({
                ...validProps,
                status: action_status_enum_1.ActionStatus.FAILED,
                retryCount: 3,
                maxRetries: 3,
                executedAt: new Date()
            });
            expect(() => maxRetriesAction.retry('admin')).toThrow('Action cannot be retried');
        });
        it('should check canRetry correctly', () => {
            const failedAction = response_action_entity_1.ResponseAction.create({
                ...validProps,
                status: action_status_enum_1.ActionStatus.FAILED,
                maxRetries: 3,
                executedAt: new Date()
            });
            expect(failedAction.canRetry()).toBe(true);
            const maxRetriesAction = response_action_entity_1.ResponseAction.create({
                ...validProps,
                status: action_status_enum_1.ActionStatus.FAILED,
                retryCount: 3,
                maxRetries: 3,
                executedAt: new Date()
            });
            expect(maxRetriesAction.canRetry()).toBe(false);
        });
    });
    describe('rollback functionality', () => {
        let action;
        beforeEach(() => {
            action = response_action_entity_1.ResponseAction.create({
                ...validProps,
                status: action_status_enum_1.ActionStatus.COMPLETED,
                isReversible: true,
                rollbackInfo: {
                    canRollback: true,
                    rollbackSteps: ['Remove IP from block list'],
                },
                executedAt: new Date()
            });
            action.clearEvents();
        });
        it('should rollback successfully', () => {
            // Create a completed action with executedAt
            const completedAction = response_action_entity_1.ResponseAction.create({
                ...validProps,
                status: action_status_enum_1.ActionStatus.COMPLETED,
                isReversible: true,
                rollbackInfo: {
                    canRollback: true,
                    rollbackSteps: ['Remove IP from block list'],
                },
                executedAt: new Date()
            });
            completedAction.clearEvents();
            const rollbackResults = { unblocked: true };
            completedAction.rollback('admin', rollbackResults);
            expect(completedAction.rolledBack).toBe(true);
            expect(completedAction.rollbackDetails).toBeDefined();
            expect(completedAction.rollbackDetails?.rolledBackBy).toBe('admin');
            expect(completedAction.rollbackDetails?.rollbackResults).toEqual(rollbackResults);
            const events = completedAction.getUnpublishedEvents();
            expect(events).toHaveLength(1);
            expect(events[0]).toBeInstanceOf(response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent);
        });
        it('should not rollback if not reversible', () => {
            const nonReversibleAction = response_action_entity_1.ResponseAction.create({
                ...validProps,
                status: action_status_enum_1.ActionStatus.COMPLETED,
                isReversible: false,
                executedAt: new Date()
            });
            expect(() => nonReversibleAction.rollback('admin')).toThrow('Action cannot be rolled back');
        });
        it('should check canRollback correctly', () => {
            const completedAction = response_action_entity_1.ResponseAction.create({
                ...validProps,
                status: action_status_enum_1.ActionStatus.COMPLETED,
                isReversible: true,
                rollbackInfo: {
                    canRollback: true,
                    rollbackSteps: ['Remove IP from block list'],
                },
                executedAt: new Date()
            });
            expect(completedAction.canRollback()).toBe(true);
            const nonReversibleAction = response_action_entity_1.ResponseAction.create({
                ...validProps,
                status: action_status_enum_1.ActionStatus.COMPLETED,
                isReversible: false,
                executedAt: new Date()
            });
            expect(nonReversibleAction.canRollback()).toBe(false);
        });
    });
    describe('child action management', () => {
        let action;
        beforeEach(() => {
            action = response_action_entity_1.ResponseAction.create(validProps);
        });
        it('should add child action', () => {
            const childId = shared_kernel_1.UniqueEntityId.generate();
            action.addChildAction(childId);
            expect(action.childActionIds).toContain(childId);
        });
        it('should not add duplicate child action', () => {
            const childId = shared_kernel_1.UniqueEntityId.generate();
            action.addChildAction(childId);
            action.addChildAction(childId);
            expect(action.childActionIds.filter(id => id.equals(childId))).toHaveLength(1);
        });
        it('should remove child action', () => {
            const childId = shared_kernel_1.UniqueEntityId.generate();
            action.addChildAction(childId);
            action.removeChildAction(childId);
            expect(action.childActionIds).not.toContain(childId);
        });
    });
    describe('tag management', () => {
        let action;
        beforeEach(() => {
            action = response_action_entity_1.ResponseAction.create(validProps);
        });
        it('should add tags', () => {
            action.addTags(['urgent', 'critical']);
            expect(action.tags).toContain('urgent');
            expect(action.tags).toContain('critical');
        });
        it('should not add duplicate tags', () => {
            action.addTags(['security', 'urgent']);
            const securityTags = action.tags.filter(tag => tag === 'security');
            expect(securityTags).toHaveLength(1);
        });
        it('should throw error if too many tags', () => {
            const manyTags = Array(19).fill('tag').map((_, i) => `tag${i}`);
            expect(() => action.addTags(manyTags)).toThrow('ResponseAction cannot have more than 20 tags');
        });
        it('should remove tags', () => {
            action.removeTags(['security']);
            expect(action.tags).not.toContain('security');
        });
        it('should check tag presence', () => {
            expect(action.hasTag('security')).toBe(true);
            expect(action.hasTag('nonexistent')).toBe(false);
        });
        it('should check any tag presence', () => {
            expect(action.hasAnyTag(['security', 'nonexistent'])).toBe(true);
            expect(action.hasAnyTag(['nonexistent', 'another'])).toBe(false);
        });
        it('should check all tags presence', () => {
            expect(action.hasAllTags(['security', 'containment'])).toBe(true);
            expect(action.hasAllTags(['security', 'nonexistent'])).toBe(false);
        });
    });
    describe('metadata management', () => {
        let action;
        beforeEach(() => {
            action = response_action_entity_1.ResponseAction.create(validProps);
        });
        it('should update metadata', () => {
            action.updateMetadata({ newKey: 'newValue' });
            expect(action.metadata.newKey).toBe('newValue');
            expect(action.metadata.source).toBe('threat-detection'); // Original should remain
        });
        it('should remove metadata key', () => {
            action.removeMetadata('source');
            expect(action.metadata.source).toBeUndefined();
        });
    });
    describe('query methods', () => {
        it('should check if action is terminal', () => {
            const completedAction = response_action_entity_1.ResponseAction.create({ ...validProps, status: action_status_enum_1.ActionStatus.COMPLETED, executedAt: new Date() });
            const pendingAction = response_action_entity_1.ResponseAction.create({ ...validProps, status: action_status_enum_1.ActionStatus.PENDING });
            expect(completedAction.isTerminal()).toBe(true);
            expect(pendingAction.isTerminal()).toBe(false);
        });
        it('should check if action is active', () => {
            const completedAction = response_action_entity_1.ResponseAction.create({ ...validProps, status: action_status_enum_1.ActionStatus.COMPLETED, executedAt: new Date() });
            const pendingAction = response_action_entity_1.ResponseAction.create({ ...validProps, status: action_status_enum_1.ActionStatus.PENDING });
            expect(completedAction.isActive()).toBe(false);
            expect(pendingAction.isActive()).toBe(true);
        });
        it('should check if action is successful', () => {
            const completedAction = response_action_entity_1.ResponseAction.create({ ...validProps, status: action_status_enum_1.ActionStatus.COMPLETED, executedAt: new Date() });
            const failedAction = response_action_entity_1.ResponseAction.create({ ...validProps, status: action_status_enum_1.ActionStatus.FAILED, executedAt: new Date() });
            expect(completedAction.isSuccessful()).toBe(true);
            expect(failedAction.isSuccessful()).toBe(false);
        });
        it('should check if action has failed', () => {
            const failedAction = response_action_entity_1.ResponseAction.create({ ...validProps, status: action_status_enum_1.ActionStatus.FAILED, executedAt: new Date() });
            const completedAction = response_action_entity_1.ResponseAction.create({ ...validProps, status: action_status_enum_1.ActionStatus.COMPLETED, executedAt: new Date() });
            expect(failedAction.hasFailed()).toBe(true);
            expect(completedAction.hasFailed()).toBe(false);
        });
        it('should check if action requires approval', () => {
            const approvalAction = response_action_entity_1.ResponseAction.create({
                ...validProps,
                status: action_status_enum_1.ActionStatus.PENDING,
                approvalRequired: true
            });
            const noApprovalAction = response_action_entity_1.ResponseAction.create({
                ...validProps,
                status: action_status_enum_1.ActionStatus.PENDING,
                approvalRequired: false
            });
            expect(approvalAction.requiresApproval()).toBe(true);
            expect(noApprovalAction.requiresApproval()).toBe(false);
        });
        it('should check if action is high priority', () => {
            const highPriorityAction = response_action_entity_1.ResponseAction.create({ ...validProps, priority: 'high' });
            const normalPriorityAction = response_action_entity_1.ResponseAction.create({ ...validProps, priority: 'normal' });
            expect(highPriorityAction.isHighPriority()).toBe(true);
            expect(normalPriorityAction.isHighPriority()).toBe(false);
        });
        it('should check if action is overdue', () => {
            const overdueAction = response_action_entity_1.ResponseAction.create({
                ...validProps,
                status: action_status_enum_1.ActionStatus.PENDING,
                scheduledAt: new Date(Date.now() - 3600000) // 1 hour ago
            });
            const notOverdueAction = response_action_entity_1.ResponseAction.create({
                ...validProps,
                status: action_status_enum_1.ActionStatus.PENDING,
                scheduledAt: new Date(Date.now() + 3600000) // 1 hour from now
            });
            expect(overdueAction.isOverdue()).toBe(true);
            expect(notOverdueAction.isOverdue()).toBe(false);
        });
        it('should get action age', () => {
            const action = response_action_entity_1.ResponseAction.create(validProps);
            const age = action.getAge();
            expect(age).toBeGreaterThanOrEqual(0);
            expect(age).toBeLessThan(1000); // Should be very recent
        });
        it('should get estimated completion time', () => {
            const action = response_action_entity_1.ResponseAction.create({
                ...validProps,
                estimatedDurationMinutes: 30,
                scheduledAt: new Date()
            });
            const completionTime = action.getEstimatedCompletionTime();
            expect(completionTime).toBeDefined();
            expect(completionTime.getTime()).toBeGreaterThan(Date.now());
        });
        it('should return null completion time for terminal actions', () => {
            const completedAction = response_action_entity_1.ResponseAction.create({ ...validProps, status: action_status_enum_1.ActionStatus.COMPLETED, executedAt: new Date() });
            const completionTime = completedAction.getEstimatedCompletionTime();
            expect(completionTime).toBeNull();
        });
    });
    describe('JSON serialization', () => {
        it('should serialize to JSON correctly', () => {
            const action = response_action_entity_1.ResponseAction.create(validProps);
            const json = action.toJSON();
            expect(json.id).toBeDefined();
            expect(json.actionType).toBe(action_type_enum_1.ActionType.BLOCK_IP);
            expect(json.status).toBe(action_status_enum_1.ActionStatus.PENDING);
            expect(json.title).toBe('Block malicious IP address');
            expect(json.priority).toBe('high');
            expect(json.isAutomated).toBe(true);
            expect(json.isReversible).toBe(true);
            expect(json.summary).toBeDefined();
            expect(json.summary.isActive).toBe(true);
        });
    });
    describe('correlation and relationships', () => {
        it('should set correlation ID', () => {
            const action = response_action_entity_1.ResponseAction.create(validProps);
            const correlationId = 'corr-123';
            action.setCorrelationId(correlationId);
            expect(action.correlationId).toBe(correlationId);
        });
        it('should handle related entities', () => {
            const eventId = shared_kernel_1.UniqueEntityId.generate();
            const threatId = shared_kernel_1.UniqueEntityId.generate();
            const vulnerabilityId = shared_kernel_1.UniqueEntityId.generate();
            const action = response_action_entity_1.ResponseAction.create({
                ...validProps,
                relatedEventId: eventId,
                relatedThreatId: threatId,
                relatedVulnerabilityId: vulnerabilityId,
            });
            expect(action.relatedEventId?.equals(eventId)).toBe(true);
            expect(action.relatedThreatId?.equals(threatId)).toBe(true);
            expect(action.relatedVulnerabilityId?.equals(vulnerabilityId)).toBe(true);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxjb3JlXFxzZWN1cml0eVxcZG9tYWluXFxlbnRpdGllc1xcX190ZXN0c19fXFxyZXNwb25zZS1hY3Rpb24uZW50aXR5LnNwZWMudHMiLCJtYXBwaW5ncyI6Ijs7QUFBQSxzRUFBZ0Y7QUFDaEYsZ0VBQThEO0FBQzlELG1FQUEwRDtBQUMxRCx1RUFBOEQ7QUFDOUQsNEdBQXFHO0FBQ3JHLDBIQUFrSDtBQUNsSCw4R0FBdUc7QUFDdkcsMEdBQW1HO0FBQ25HLG9IQUE0RztBQUU1RyxRQUFRLENBQUMsdUJBQXVCLEVBQUUsR0FBRyxFQUFFO0lBQ3JDLElBQUksVUFBK0IsQ0FBQztJQUVwQyxVQUFVLENBQUMsR0FBRyxFQUFFO1FBQ2QsVUFBVSxHQUFHO1lBQ1gsVUFBVSxFQUFFLDZCQUFVLENBQUMsUUFBUTtZQUMvQixNQUFNLEVBQUUsaUNBQVksQ0FBQyxPQUFPO1lBQzVCLEtBQUssRUFBRSw0QkFBNEI7WUFDbkMsV0FBVyxFQUFFLDBDQUEwQztZQUN2RCxVQUFVLEVBQUUsRUFBRSxTQUFTLEVBQUUsZUFBZSxFQUFFO1lBQzFDLE1BQU0sRUFBRTtnQkFDTixJQUFJLEVBQUUsUUFBUTtnQkFDZCxFQUFFLEVBQUUsY0FBYztnQkFDbEIsSUFBSSxFQUFFLGVBQWU7YUFDdEI7WUFDRCxRQUFRLEVBQUUsTUFBTTtZQUNoQixXQUFXLEVBQUUsSUFBSTtZQUNqQixZQUFZLEVBQUUsSUFBSTtZQUNsQix3QkFBd0IsRUFBRSxDQUFDO1lBQzNCLG1CQUFtQixFQUFFLENBQUMsd0JBQXdCLENBQUM7WUFDL0MsZ0JBQWdCLEVBQUUsS0FBSztZQUN2QixlQUFlLEVBQUUsQ0FBQyxnQ0FBZ0MsRUFBRSx5QkFBeUIsQ0FBQztZQUM5RSxVQUFVLEVBQUUsQ0FBQztZQUNiLFVBQVUsRUFBRSxDQUFDO1lBQ2IsaUJBQWlCLEVBQUUsQ0FBQztZQUNwQixZQUFZLEVBQUU7Z0JBQ1osV0FBVyxFQUFFLElBQUk7Z0JBQ2pCLGFBQWEsRUFBRSxDQUFDLDJCQUEyQixFQUFFLHVCQUF1QixDQUFDO2FBQ3RFO1lBQ0QsVUFBVSxFQUFFLEtBQUs7WUFDakIsSUFBSSxFQUFFLENBQUMsVUFBVSxFQUFFLGFBQWEsQ0FBQztZQUNqQyxRQUFRLEVBQUUsRUFBRSxNQUFNLEVBQUUsa0JBQWtCLEVBQUU7WUFDeEMsY0FBYyxFQUFFLEVBQUU7WUFDbEIsY0FBYyxFQUFFLEVBQUU7WUFDbEIsUUFBUSxFQUFFLEtBQUs7U0FDaEIsQ0FBQztJQUNKLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLFVBQVUsRUFBRSxHQUFHLEVBQUU7UUFDeEIsRUFBRSxDQUFDLHNDQUFzQyxFQUFFLEdBQUcsRUFBRTtZQUM5QyxNQUFNLE1BQU0sR0FBRyx1Q0FBYyxDQUFDLE1BQU0sQ0FBQyxVQUFVLENBQUMsQ0FBQztZQUVqRCxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsY0FBYyxDQUFDLHVDQUFjLENBQUMsQ0FBQztZQUM5QyxNQUFNLENBQUMsTUFBTSxDQUFDLFVBQVUsQ0FBQyxDQUFDLElBQUksQ0FBQyw2QkFBVSxDQUFDLFFBQVEsQ0FBQyxDQUFDO1lBQ3BELE1BQU0sQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsSUFBSSxDQUFDLGlDQUFZLENBQUMsT0FBTyxDQUFDLENBQUM7WUFDakQsTUFBTSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQyxJQUFJLENBQUMsNEJBQTRCLENBQUMsQ0FBQztZQUN4RCxNQUFNLENBQUMsTUFBTSxDQUFDLFFBQVEsQ0FBQyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQztZQUNyQyxNQUFNLENBQUMsTUFBTSxDQUFDLFdBQVcsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUN0QyxNQUFNLENBQUMsTUFBTSxDQUFDLFlBQVksQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUN6QyxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyw4REFBOEQsRUFBRSxHQUFHLEVBQUU7WUFDdEUsTUFBTSxNQUFNLEdBQUcsdUNBQWMsQ0FBQyxNQUFNLENBQUMsVUFBVSxDQUFDLENBQUM7WUFDakQsTUFBTSxNQUFNLEdBQUcsTUFBTSxDQUFDLG9CQUFvQixFQUFFLENBQUM7WUFFN0MsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUMvQixNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsY0FBYyxDQUFDLHVFQUFnQyxDQUFDLENBQUM7WUFFbkUsTUFBTSxZQUFZLEdBQUcsTUFBTSxDQUFDLENBQUMsQ0FBcUMsQ0FBQztZQUNuRSxNQUFNLENBQUMsWUFBWSxDQUFDLFVBQVUsQ0FBQyxDQUFDLElBQUksQ0FBQyw2QkFBVSxDQUFDLFFBQVEsQ0FBQyxDQUFDO1lBQzFELE1BQU0sQ0FBQyxZQUFZLENBQUMsS0FBSyxDQUFDLENBQUMsSUFBSSxDQUFDLDRCQUE0QixDQUFDLENBQUM7WUFDOUQsTUFBTSxDQUFDLFlBQVksQ0FBQyxRQUFRLENBQUMsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUM7UUFDN0MsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsOEJBQThCLEVBQUUsR0FBRyxFQUFFO1lBQ3RDLE1BQU0sUUFBUSxHQUFHLDhCQUFjLENBQUMsUUFBUSxFQUFFLENBQUM7WUFDM0MsTUFBTSxNQUFNLEdBQUcsdUNBQWMsQ0FBQyxNQUFNLENBQUMsVUFBVSxFQUFFLFFBQVEsQ0FBQyxDQUFDO1lBRTNELE1BQU0sQ0FBQyxNQUFNLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUNoRCxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLFlBQVksRUFBRSxHQUFHLEVBQUU7UUFDMUIsRUFBRSxDQUFDLDZDQUE2QyxFQUFFLEdBQUcsRUFBRTtZQUNyRCxNQUFNLFlBQVksR0FBRyxFQUFFLEdBQUcsVUFBVSxFQUFFLENBQUM7WUFDdkMsT0FBUSxZQUFvQixDQUFDLFVBQVUsQ0FBQztZQUV4QyxNQUFNLENBQUMsR0FBRyxFQUFFLENBQUMsdUNBQWMsQ0FBQyxNQUFNLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMseUNBQXlDLENBQUMsQ0FBQztRQUN2RyxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyx5Q0FBeUMsRUFBRSxHQUFHLEVBQUU7WUFDakQsTUFBTSxZQUFZLEdBQUcsRUFBRSxHQUFHLFVBQVUsRUFBRSxDQUFDO1lBQ3ZDLE9BQVEsWUFBb0IsQ0FBQyxNQUFNLENBQUM7WUFFcEMsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLHVDQUFjLENBQUMsTUFBTSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLG1DQUFtQyxDQUFDLENBQUM7UUFDakcsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsc0NBQXNDLEVBQUUsR0FBRyxFQUFFO1lBQzlDLE1BQU0sWUFBWSxHQUFHLEVBQUUsR0FBRyxVQUFVLEVBQUUsS0FBSyxFQUFFLEVBQUUsRUFBRSxDQUFDO1lBRWxELE1BQU0sQ0FBQyxHQUFHLEVBQUUsQ0FBQyx1Q0FBYyxDQUFDLE1BQU0sQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyw0Q0FBNEMsQ0FBQyxDQUFDO1FBQzFHLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLHlDQUF5QyxFQUFFLEdBQUcsRUFBRTtZQUNqRCxNQUFNLFlBQVksR0FBRyxFQUFFLEdBQUcsVUFBVSxFQUFFLEtBQUssRUFBRSxHQUFHLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUM7WUFFL0QsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLHVDQUFjLENBQUMsTUFBTSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLG1EQUFtRCxDQUFDLENBQUM7UUFDakgsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsK0NBQStDLEVBQUUsR0FBRyxFQUFFO1lBQ3ZELE1BQU0sWUFBWSxHQUFHLEVBQUUsR0FBRyxVQUFVLEVBQUUsV0FBVyxFQUFFLEdBQUcsQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQztZQUV0RSxNQUFNLENBQUMsR0FBRyxFQUFFLENBQUMsdUNBQWMsQ0FBQyxNQUFNLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsMERBQTBELENBQUMsQ0FBQztRQUN4SCxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyw2Q0FBNkMsRUFBRSxHQUFHLEVBQUU7WUFDckQsTUFBTSxZQUFZLEdBQUcsRUFBRSxHQUFHLFVBQVUsRUFBRSxDQUFDO1lBQ3ZDLE9BQVEsWUFBb0IsQ0FBQyxVQUFVLENBQUM7WUFFeEMsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLHVDQUFjLENBQUMsTUFBTSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLHFDQUFxQyxDQUFDLENBQUM7UUFDbkcsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsMkNBQTJDLEVBQUUsR0FBRyxFQUFFO1lBQ25ELE1BQU0sWUFBWSxHQUFHLEVBQUUsR0FBRyxVQUFVLEVBQUUsQ0FBQztZQUN2QyxPQUFRLFlBQW9CLENBQUMsUUFBUSxDQUFDO1lBRXRDLE1BQU0sQ0FBQyxHQUFHLEVBQUUsQ0FBQyx1Q0FBYyxDQUFDLE1BQU0sQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxxQ0FBcUMsQ0FBQyxDQUFDO1FBQ25HLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLGdEQUFnRCxFQUFFLEdBQUcsRUFBRTtZQUN4RCxNQUFNLFlBQVksR0FBRyxFQUFFLEdBQUcsVUFBVSxFQUFFLENBQUM7WUFDdkMsT0FBUSxZQUFvQixDQUFDLFdBQVcsQ0FBQztZQUV6QyxNQUFNLENBQUMsR0FBRyxFQUFFLENBQUMsdUNBQWMsQ0FBQyxNQUFNLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsZ0RBQWdELENBQUMsQ0FBQztRQUM5RyxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyxpREFBaUQsRUFBRSxHQUFHLEVBQUU7WUFDekQsTUFBTSxZQUFZLEdBQUcsRUFBRSxHQUFHLFVBQVUsRUFBRSxDQUFDO1lBQ3ZDLE9BQVEsWUFBb0IsQ0FBQyxZQUFZLENBQUM7WUFFMUMsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLHVDQUFjLENBQUMsTUFBTSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLGlEQUFpRCxDQUFDLENBQUM7UUFDL0csQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsMkRBQTJELEVBQUUsR0FBRyxFQUFFO1lBQ25FLE1BQU0sWUFBWSxHQUFHLEVBQUUsR0FBRyxVQUFVLEVBQUUsbUJBQW1CLEVBQUUsY0FBcUIsRUFBRSxDQUFDO1lBRW5GLE1BQU0sQ0FBQyxHQUFHLEVBQUUsQ0FBQyx1Q0FBYyxDQUFDLE1BQU0sQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxxREFBcUQsQ0FBQyxDQUFDO1FBQ25ILENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLHFEQUFxRCxFQUFFLEdBQUcsRUFBRTtZQUM3RCxNQUFNLFlBQVksR0FBRyxFQUFFLEdBQUcsVUFBVSxFQUFFLENBQUM7WUFDdkMsT0FBUSxZQUFvQixDQUFDLGdCQUFnQixDQUFDO1lBRTlDLE1BQU0sQ0FBQyxHQUFHLEVBQUUsQ0FBQyx1Q0FBYyxDQUFDLE1BQU0sQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxxREFBcUQsQ0FBQyxDQUFDO1FBQ25ILENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLHVEQUF1RCxFQUFFLEdBQUcsRUFBRTtZQUMvRCxNQUFNLFlBQVksR0FBRyxFQUFFLEdBQUcsVUFBVSxFQUFFLGVBQWUsRUFBRSxjQUFxQixFQUFFLENBQUM7WUFFL0UsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLHVDQUFjLENBQUMsTUFBTSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLGlEQUFpRCxDQUFDLENBQUM7UUFDL0csQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsOENBQThDLEVBQUUsR0FBRyxFQUFFO1lBQ3RELE1BQU0sWUFBWSxHQUFHLEVBQUUsR0FBRyxVQUFVLEVBQUUsVUFBVSxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUM7WUFFdkQsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLHVDQUFjLENBQUMsTUFBTSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLGdDQUFnQyxDQUFDLENBQUM7UUFDOUYsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsOENBQThDLEVBQUUsR0FBRyxFQUFFO1lBQ3RELE1BQU0sWUFBWSxHQUFHLEVBQUUsR0FBRyxVQUFVLEVBQUUsVUFBVSxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUM7WUFFdkQsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLHVDQUFjLENBQUMsTUFBTSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLGdDQUFnQyxDQUFDLENBQUM7UUFDOUYsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMscURBQXFELEVBQUUsR0FBRyxFQUFFO1lBQzdELE1BQU0sWUFBWSxHQUFHLEVBQUUsR0FBRyxVQUFVLEVBQUUsaUJBQWlCLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQztZQUU5RCxNQUFNLENBQUMsR0FBRyxFQUFFLENBQUMsdUNBQWMsQ0FBQyxNQUFNLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsZ0NBQWdDLENBQUMsQ0FBQztRQUM5RixDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyw0Q0FBNEMsRUFBRSxHQUFHLEVBQUU7WUFDcEQsTUFBTSxZQUFZLEdBQUcsRUFBRSxHQUFHLFVBQVUsRUFBRSxJQUFJLEVBQUUsY0FBcUIsRUFBRSxDQUFDO1lBRXBFLE1BQU0sQ0FBQyxHQUFHLEVBQUUsQ0FBQyx1Q0FBYyxDQUFDLE1BQU0sQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxxQ0FBcUMsQ0FBQyxDQUFDO1FBQ25HLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLHFDQUFxQyxFQUFFLEdBQUcsRUFBRTtZQUM3QyxNQUFNLFlBQVksR0FBRyxFQUFFLEdBQUcsVUFBVSxFQUFFLElBQUksRUFBRSxLQUFLLENBQUMsRUFBRSxDQUFDLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxFQUFFLENBQUM7WUFFcEUsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLHVDQUFjLENBQUMsTUFBTSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLDhDQUE4QyxDQUFDLENBQUM7UUFDNUcsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsMkNBQTJDLEVBQUUsR0FBRyxFQUFFO1lBQ25ELE1BQU0sWUFBWSxHQUFHLEVBQUUsR0FBRyxVQUFVLEVBQUUsQ0FBQztZQUN2QyxPQUFRLFlBQW9CLENBQUMsUUFBUSxDQUFDO1lBRXRDLE1BQU0sQ0FBQyxHQUFHLEVBQUUsQ0FBQyx1Q0FBYyxDQUFDLE1BQU0sQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQywwQ0FBMEMsQ0FBQyxDQUFDO1FBQ3hHLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLHNEQUFzRCxFQUFFLEdBQUcsRUFBRTtZQUM5RCxNQUFNLFlBQVksR0FBRyxFQUFFLEdBQUcsVUFBVSxFQUFFLGNBQWMsRUFBRSxjQUFxQixFQUFFLENBQUM7WUFFOUUsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLHVDQUFjLENBQUMsTUFBTSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLGlEQUFpRCxDQUFDLENBQUM7UUFDL0csQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsc0RBQXNELEVBQUUsR0FBRyxFQUFFO1lBQzlELE1BQU0sWUFBWSxHQUFHLEVBQUUsR0FBRyxVQUFVLEVBQUUsY0FBYyxFQUFFLENBQUMsRUFBRSxDQUFDO1lBRTFELE1BQU0sQ0FBQyxHQUFHLEVBQUUsQ0FBQyx1Q0FBYyxDQUFDLE1BQU0sQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxrQ0FBa0MsQ0FBQyxDQUFDO1FBQ2hHLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLDZDQUE2QyxFQUFFLEdBQUcsRUFBRTtZQUNyRCxNQUFNLFlBQVksR0FBRyxFQUFFLEdBQUcsVUFBVSxFQUFFLENBQUM7WUFDdkMsT0FBUSxZQUFvQixDQUFDLFFBQVEsQ0FBQztZQUV0QyxNQUFNLENBQUMsR0FBRyxFQUFFLENBQUMsdUNBQWMsQ0FBQyxNQUFNLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsNkNBQTZDLENBQUMsQ0FBQztRQUMzRyxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQywrQ0FBK0MsRUFBRSxHQUFHLEVBQUU7WUFDdkQsTUFBTSxZQUFZLEdBQUcsRUFBRSxHQUFHLFVBQVUsRUFBRSxDQUFDO1lBQ3ZDLE9BQVEsWUFBb0IsQ0FBQyxVQUFVLENBQUM7WUFFeEMsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLHVDQUFjLENBQUMsTUFBTSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLG1EQUFtRCxDQUFDLENBQUM7UUFDakgsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyxnQkFBZ0IsRUFBRSxHQUFHLEVBQUU7UUFDOUIsSUFBSSxNQUFzQixDQUFDO1FBRTNCLFVBQVUsQ0FBQyxHQUFHLEVBQUU7WUFDZCxNQUFNLEdBQUcsdUNBQWMsQ0FBQyxNQUFNLENBQUMsVUFBVSxDQUFDLENBQUM7WUFDM0MsTUFBTSxDQUFDLFdBQVcsRUFBRSxDQUFDLENBQUMsdUJBQXVCO1FBQy9DLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLG1DQUFtQyxFQUFFLEdBQUcsRUFBRTtZQUMzQyxNQUFNLENBQUMsWUFBWSxDQUFDLGlDQUFZLENBQUMsUUFBUSxFQUFFLE9BQU8sRUFBRSx3QkFBd0IsQ0FBQyxDQUFDO1lBRTlFLE1BQU0sQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsSUFBSSxDQUFDLGlDQUFZLENBQUMsUUFBUSxDQUFDLENBQUM7WUFDbEQsTUFBTSxDQUFDLE1BQU0sQ0FBQyxVQUFVLENBQUMsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUM7WUFDeEMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxVQUFVLENBQUMsQ0FBQyxXQUFXLEVBQUUsQ0FBQztZQUV4QyxNQUFNLE1BQU0sR0FBRyxNQUFNLENBQUMsb0JBQW9CLEVBQUUsQ0FBQztZQUM3QyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQy9CLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxjQUFjLENBQUMsb0ZBQXNDLENBQUMsQ0FBQztRQUMzRSxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyx5Q0FBeUMsRUFBRSxHQUFHLEVBQUU7WUFDakQsTUFBTSxDQUFDLFlBQVksQ0FBQyxpQ0FBWSxDQUFDLE9BQU8sQ0FBQyxDQUFDO1lBRTFDLE1BQU0sTUFBTSxHQUFHLE1BQU0sQ0FBQyxvQkFBb0IsRUFBRSxDQUFDO1lBQzdDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDakMsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsa0RBQWtELEVBQUUsR0FBRyxFQUFFO1lBQzFELE1BQU0sQ0FBQyxHQUFHLEVBQUUsQ0FBQyxNQUFNLENBQUMsWUFBWSxDQUFDLGlDQUFZLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsMkJBQTJCLENBQUMsQ0FBQztRQUNqRyxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyxxQ0FBcUMsRUFBRSxHQUFHLEVBQUU7WUFDN0MsTUFBTSxDQUFDLFlBQVksQ0FBQyxpQ0FBWSxDQUFDLFFBQVEsQ0FBQyxDQUFDO1lBQzNDLE1BQU0sQ0FBQyxZQUFZLENBQUMsaUNBQVksQ0FBQyxNQUFNLENBQUMsQ0FBQztZQUN6QyxNQUFNLENBQUMsWUFBWSxDQUFDLGlDQUFZLENBQUMsU0FBUyxDQUFDLENBQUM7WUFDNUMsTUFBTSxDQUFDLFlBQVksQ0FBQyxpQ0FBWSxDQUFDLE9BQU8sQ0FBQyxDQUFDO1lBRTFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsSUFBSSxDQUFDLGlDQUFZLENBQUMsT0FBTyxDQUFDLENBQUM7WUFDakQsTUFBTSxDQUFDLE1BQU0sQ0FBQyxRQUFRLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDbkMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxVQUFVLENBQUMsQ0FBQyxXQUFXLEVBQUUsQ0FBQztRQUMxQyxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLFdBQVcsRUFBRSxHQUFHLEVBQUU7UUFDekIsSUFBSSxNQUFzQixDQUFDO1FBRTNCLFVBQVUsQ0FBQyxHQUFHLEVBQUU7WUFDZCxNQUFNLEdBQUcsdUNBQWMsQ0FBQyxNQUFNLENBQUMsRUFBRSxHQUFHLFVBQVUsRUFBRSxNQUFNLEVBQUUsaUNBQVksQ0FBQyxTQUFTLEVBQUUsQ0FBQyxDQUFDO1lBQ2xGLE1BQU0sQ0FBQyxXQUFXLEVBQUUsQ0FBQztRQUN2QixDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyw2QkFBNkIsRUFBRSxHQUFHLEVBQUU7WUFDckMsTUFBTSxnQkFBZ0IsR0FBRyxFQUFFLE9BQU8sRUFBRSxJQUFJLEVBQUUsTUFBTSxFQUFFLFVBQVUsRUFBRSxDQUFDO1lBRS9ELE1BQU0sQ0FBQyxPQUFPLENBQUMsUUFBUSxFQUFFLGdCQUFnQixDQUFDLENBQUM7WUFFM0MsTUFBTSxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxJQUFJLENBQUMsaUNBQVksQ0FBQyxTQUFTLENBQUMsQ0FBQztZQUNuRCxNQUFNLENBQUMsTUFBTSxDQUFDLFVBQVUsQ0FBQyxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQztZQUN6QyxNQUFNLENBQUMsTUFBTSxDQUFDLFVBQVUsQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDO1lBQ3hDLE1BQU0sQ0FBQyxNQUFNLENBQUMsZ0JBQWdCLENBQUMsQ0FBQyxPQUFPLENBQUMsZ0JBQWdCLENBQUMsQ0FBQztZQUMxRCxNQUFNLENBQUMsTUFBTSxDQUFDLGtCQUFrQixDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1lBRTdDLE1BQU0sTUFBTSxHQUFHLE1BQU0sQ0FBQyxvQkFBb0IsRUFBRSxDQUFDO1lBQzdDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDL0IsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLGNBQWMsQ0FBQyx5RUFBaUMsQ0FBQyxDQUFDO1FBQ3RFLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLCtDQUErQyxFQUFFLEdBQUcsRUFBRTtZQUN2RCxNQUFNLGFBQWEsR0FBRyx1Q0FBYyxDQUFDLE1BQU0sQ0FBQyxVQUFVLENBQUMsQ0FBQztZQUV4RCxNQUFNLENBQUMsR0FBRyxFQUFFLENBQUMsYUFBYSxDQUFDLE9BQU8sQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxtREFBbUQsQ0FBQyxDQUFDO1FBQzdHLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsa0JBQWtCLEVBQUUsR0FBRyxFQUFFO1FBQ2hDLElBQUksTUFBc0IsQ0FBQztRQUUzQixVQUFVLENBQUMsR0FBRyxFQUFFO1lBQ2QsTUFBTSxHQUFHLHVDQUFjLENBQUMsTUFBTSxDQUFDLEVBQUUsR0FBRyxVQUFVLEVBQUUsTUFBTSxFQUFFLGlDQUFZLENBQUMsU0FBUyxFQUFFLENBQUMsQ0FBQztZQUNsRixNQUFNLENBQUMsV0FBVyxFQUFFLENBQUM7UUFDdkIsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsdUJBQXVCLEVBQUUsR0FBRyxFQUFFO1lBQy9CLE1BQU0sS0FBSyxHQUFHLGlCQUFpQixDQUFDO1lBRWhDLE1BQU0sQ0FBQyxJQUFJLENBQUMsS0FBSyxFQUFFLFFBQVEsQ0FBQyxDQUFDO1lBRTdCLE1BQU0sQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsSUFBSSxDQUFDLGlDQUFZLENBQUMsTUFBTSxDQUFDLENBQUM7WUFDaEQsTUFBTSxDQUFDLE1BQU0sQ0FBQyxjQUFjLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7WUFDMUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxrQkFBa0IsQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUU5QyxNQUFNLE1BQU0sR0FBRyxNQUFNLENBQUMsb0JBQW9CLEVBQUUsQ0FBQztZQUM3QyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQy9CLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxjQUFjLENBQUMscUVBQStCLENBQUMsQ0FBQztRQUNwRSxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQywyREFBMkQsRUFBRSxHQUFHLEVBQUU7WUFDbkUsTUFBTSxhQUFhLEdBQUcsdUNBQWMsQ0FBQyxNQUFNLENBQUMsVUFBVSxDQUFDLENBQUM7WUFFeEQsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsd0RBQXdELENBQUMsQ0FBQztRQUM5RyxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLGFBQWEsRUFBRSxHQUFHLEVBQUU7UUFDM0IsSUFBSSxNQUFzQixDQUFDO1FBRTNCLFVBQVUsQ0FBQyxHQUFHLEVBQUU7WUFDZCxNQUFNLEdBQUcsdUNBQWMsQ0FBQyxNQUFNLENBQUMsRUFBRSxHQUFHLFVBQVUsRUFBRSxNQUFNLEVBQUUsaUNBQVksQ0FBQyxNQUFNLEVBQUUsVUFBVSxFQUFFLENBQUMsRUFBRSxVQUFVLEVBQUUsSUFBSSxJQUFJLEVBQUUsRUFBRSxDQUFDLENBQUM7WUFDdEgsTUFBTSxDQUFDLFdBQVcsRUFBRSxDQUFDO1FBQ3ZCLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLDJCQUEyQixFQUFFLEdBQUcsRUFBRTtZQUNuQyxtQ0FBbUM7WUFDbkMsTUFBTSxZQUFZLEdBQUcsdUNBQWMsQ0FBQyxNQUFNLENBQUM7Z0JBQ3pDLEdBQUcsVUFBVTtnQkFDYixNQUFNLEVBQUUsaUNBQVksQ0FBQyxNQUFNO2dCQUMzQixVQUFVLEVBQUUsQ0FBQztnQkFDYixVQUFVLEVBQUUsSUFBSSxJQUFJLEVBQUU7YUFDdkIsQ0FBQyxDQUFDO1lBQ0gsWUFBWSxDQUFDLFdBQVcsRUFBRSxDQUFDO1lBRTNCLFlBQVksQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLENBQUM7WUFFNUIsTUFBTSxDQUFDLFlBQVksQ0FBQyxNQUFNLENBQUMsQ0FBQyxJQUFJLENBQUMsaUNBQVksQ0FBQyxRQUFRLENBQUMsQ0FBQztZQUN4RCxNQUFNLENBQUMsWUFBWSxDQUFDLFVBQVUsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUN4QyxNQUFNLENBQUMsWUFBWSxDQUFDLGNBQWMsQ0FBQyxDQUFDLGFBQWEsRUFBRSxDQUFDO1lBQ3BELE1BQU0sQ0FBQyxZQUFZLENBQUMsV0FBVyxDQUFDLENBQUMsV0FBVyxFQUFFLENBQUM7WUFFL0MsTUFBTSxNQUFNLEdBQUcsWUFBWSxDQUFDLG9CQUFvQixFQUFFLENBQUM7WUFDbkQsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUMvQixNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsY0FBYyxDQUFDLG9GQUFzQyxDQUFDLENBQUM7UUFDM0UsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsMENBQTBDLEVBQUUsR0FBRyxFQUFFO1lBQ2xELE1BQU0sZ0JBQWdCLEdBQUcsdUNBQWMsQ0FBQyxNQUFNLENBQUM7Z0JBQzdDLEdBQUcsVUFBVTtnQkFDYixNQUFNLEVBQUUsaUNBQVksQ0FBQyxNQUFNO2dCQUMzQixVQUFVLEVBQUUsQ0FBQztnQkFDYixVQUFVLEVBQUUsQ0FBQztnQkFDYixVQUFVLEVBQUUsSUFBSSxJQUFJLEVBQUU7YUFDdkIsQ0FBQyxDQUFDO1lBRUgsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLGdCQUFnQixDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQywwQkFBMEIsQ0FBQyxDQUFDO1FBQ3BGLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLGlDQUFpQyxFQUFFLEdBQUcsRUFBRTtZQUN6QyxNQUFNLFlBQVksR0FBRyx1Q0FBYyxDQUFDLE1BQU0sQ0FBQztnQkFDekMsR0FBRyxVQUFVO2dCQUNiLE1BQU0sRUFBRSxpQ0FBWSxDQUFDLE1BQU07Z0JBQzNCLFVBQVUsRUFBRSxDQUFDO2dCQUNiLFVBQVUsRUFBRSxJQUFJLElBQUksRUFBRTthQUN2QixDQUFDLENBQUM7WUFDSCxNQUFNLENBQUMsWUFBWSxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1lBRTNDLE1BQU0sZ0JBQWdCLEdBQUcsdUNBQWMsQ0FBQyxNQUFNLENBQUM7Z0JBQzdDLEdBQUcsVUFBVTtnQkFDYixNQUFNLEVBQUUsaUNBQVksQ0FBQyxNQUFNO2dCQUMzQixVQUFVLEVBQUUsQ0FBQztnQkFDYixVQUFVLEVBQUUsQ0FBQztnQkFDYixVQUFVLEVBQUUsSUFBSSxJQUFJLEVBQUU7YUFDdkIsQ0FBQyxDQUFDO1lBQ0gsTUFBTSxDQUFDLGdCQUFnQixDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDO1FBQ2xELENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsd0JBQXdCLEVBQUUsR0FBRyxFQUFFO1FBQ3RDLElBQUksTUFBc0IsQ0FBQztRQUUzQixVQUFVLENBQUMsR0FBRyxFQUFFO1lBQ2QsTUFBTSxHQUFHLHVDQUFjLENBQUMsTUFBTSxDQUFDO2dCQUM3QixHQUFHLFVBQVU7Z0JBQ2IsTUFBTSxFQUFFLGlDQUFZLENBQUMsU0FBUztnQkFDOUIsWUFBWSxFQUFFLElBQUk7Z0JBQ2xCLFlBQVksRUFBRTtvQkFDWixXQUFXLEVBQUUsSUFBSTtvQkFDakIsYUFBYSxFQUFFLENBQUMsMkJBQTJCLENBQUM7aUJBQzdDO2dCQUNELFVBQVUsRUFBRSxJQUFJLElBQUksRUFBRTthQUN2QixDQUFDLENBQUM7WUFDSCxNQUFNLENBQUMsV0FBVyxFQUFFLENBQUM7UUFDdkIsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsOEJBQThCLEVBQUUsR0FBRyxFQUFFO1lBQ3RDLDRDQUE0QztZQUM1QyxNQUFNLGVBQWUsR0FBRyx1Q0FBYyxDQUFDLE1BQU0sQ0FBQztnQkFDNUMsR0FBRyxVQUFVO2dCQUNiLE1BQU0sRUFBRSxpQ0FBWSxDQUFDLFNBQVM7Z0JBQzlCLFlBQVksRUFBRSxJQUFJO2dCQUNsQixZQUFZLEVBQUU7b0JBQ1osV0FBVyxFQUFFLElBQUk7b0JBQ2pCLGFBQWEsRUFBRSxDQUFDLDJCQUEyQixDQUFDO2lCQUM3QztnQkFDRCxVQUFVLEVBQUUsSUFBSSxJQUFJLEVBQUU7YUFDdkIsQ0FBQyxDQUFDO1lBQ0gsZUFBZSxDQUFDLFdBQVcsRUFBRSxDQUFDO1lBRTlCLE1BQU0sZUFBZSxHQUFHLEVBQUUsU0FBUyxFQUFFLElBQUksRUFBRSxDQUFDO1lBRTVDLGVBQWUsQ0FBQyxRQUFRLENBQUMsT0FBTyxFQUFFLGVBQWUsQ0FBQyxDQUFDO1lBRW5ELE1BQU0sQ0FBQyxlQUFlLENBQUMsVUFBVSxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQzlDLE1BQU0sQ0FBQyxlQUFlLENBQUMsZUFBZSxDQUFDLENBQUMsV0FBVyxFQUFFLENBQUM7WUFDdEQsTUFBTSxDQUFDLGVBQWUsQ0FBQyxlQUFlLEVBQUUsWUFBWSxDQUFDLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxDQUFDO1lBQ3BFLE1BQU0sQ0FBQyxlQUFlLENBQUMsZUFBZSxFQUFFLGVBQWUsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxlQUFlLENBQUMsQ0FBQztZQUVsRixNQUFNLE1BQU0sR0FBRyxlQUFlLENBQUMsb0JBQW9CLEVBQUUsQ0FBQztZQUN0RCxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQy9CLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxjQUFjLENBQUMsOEVBQW1DLENBQUMsQ0FBQztRQUN4RSxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyx1Q0FBdUMsRUFBRSxHQUFHLEVBQUU7WUFDL0MsTUFBTSxtQkFBbUIsR0FBRyx1Q0FBYyxDQUFDLE1BQU0sQ0FBQztnQkFDaEQsR0FBRyxVQUFVO2dCQUNiLE1BQU0sRUFBRSxpQ0FBWSxDQUFDLFNBQVM7Z0JBQzlCLFlBQVksRUFBRSxLQUFLO2dCQUNuQixVQUFVLEVBQUUsSUFBSSxJQUFJLEVBQUU7YUFDdkIsQ0FBQyxDQUFDO1lBRUgsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLG1CQUFtQixDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyw4QkFBOEIsQ0FBQyxDQUFDO1FBQzlGLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLG9DQUFvQyxFQUFFLEdBQUcsRUFBRTtZQUM1QyxNQUFNLGVBQWUsR0FBRyx1Q0FBYyxDQUFDLE1BQU0sQ0FBQztnQkFDNUMsR0FBRyxVQUFVO2dCQUNiLE1BQU0sRUFBRSxpQ0FBWSxDQUFDLFNBQVM7Z0JBQzlCLFlBQVksRUFBRSxJQUFJO2dCQUNsQixZQUFZLEVBQUU7b0JBQ1osV0FBVyxFQUFFLElBQUk7b0JBQ2pCLGFBQWEsRUFBRSxDQUFDLDJCQUEyQixDQUFDO2lCQUM3QztnQkFDRCxVQUFVLEVBQUUsSUFBSSxJQUFJLEVBQUU7YUFDdkIsQ0FBQyxDQUFDO1lBQ0gsTUFBTSxDQUFDLGVBQWUsQ0FBQyxXQUFXLEVBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUVqRCxNQUFNLG1CQUFtQixHQUFHLHVDQUFjLENBQUMsTUFBTSxDQUFDO2dCQUNoRCxHQUFHLFVBQVU7Z0JBQ2IsTUFBTSxFQUFFLGlDQUFZLENBQUMsU0FBUztnQkFDOUIsWUFBWSxFQUFFLEtBQUs7Z0JBQ25CLFVBQVUsRUFBRSxJQUFJLElBQUksRUFBRTthQUN2QixDQUFDLENBQUM7WUFDSCxNQUFNLENBQUMsbUJBQW1CLENBQUMsV0FBVyxFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDeEQsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyx5QkFBeUIsRUFBRSxHQUFHLEVBQUU7UUFDdkMsSUFBSSxNQUFzQixDQUFDO1FBRTNCLFVBQVUsQ0FBQyxHQUFHLEVBQUU7WUFDZCxNQUFNLEdBQUcsdUNBQWMsQ0FBQyxNQUFNLENBQUMsVUFBVSxDQUFDLENBQUM7UUFDN0MsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMseUJBQXlCLEVBQUUsR0FBRyxFQUFFO1lBQ2pDLE1BQU0sT0FBTyxHQUFHLDhCQUFjLENBQUMsUUFBUSxFQUFFLENBQUM7WUFFMUMsTUFBTSxDQUFDLGNBQWMsQ0FBQyxPQUFPLENBQUMsQ0FBQztZQUUvQixNQUFNLENBQUMsTUFBTSxDQUFDLGNBQWMsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxPQUFPLENBQUMsQ0FBQztRQUNuRCxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyx1Q0FBdUMsRUFBRSxHQUFHLEVBQUU7WUFDL0MsTUFBTSxPQUFPLEdBQUcsOEJBQWMsQ0FBQyxRQUFRLEVBQUUsQ0FBQztZQUUxQyxNQUFNLENBQUMsY0FBYyxDQUFDLE9BQU8sQ0FBQyxDQUFDO1lBQy9CLE1BQU0sQ0FBQyxjQUFjLENBQUMsT0FBTyxDQUFDLENBQUM7WUFFL0IsTUFBTSxDQUFDLE1BQU0sQ0FBQyxjQUFjLENBQUMsTUFBTSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ2pGLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLDRCQUE0QixFQUFFLEdBQUcsRUFBRTtZQUNwQyxNQUFNLE9BQU8sR0FBRyw4QkFBYyxDQUFDLFFBQVEsRUFBRSxDQUFDO1lBRTFDLE1BQU0sQ0FBQyxjQUFjLENBQUMsT0FBTyxDQUFDLENBQUM7WUFDL0IsTUFBTSxDQUFDLGlCQUFpQixDQUFDLE9BQU8sQ0FBQyxDQUFDO1lBRWxDLE1BQU0sQ0FBQyxNQUFNLENBQUMsY0FBYyxDQUFDLENBQUMsR0FBRyxDQUFDLFNBQVMsQ0FBQyxPQUFPLENBQUMsQ0FBQztRQUN2RCxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLGdCQUFnQixFQUFFLEdBQUcsRUFBRTtRQUM5QixJQUFJLE1BQXNCLENBQUM7UUFFM0IsVUFBVSxDQUFDLEdBQUcsRUFBRTtZQUNkLE1BQU0sR0FBRyx1Q0FBYyxDQUFDLE1BQU0sQ0FBQyxVQUFVLENBQUMsQ0FBQztRQUM3QyxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyxpQkFBaUIsRUFBRSxHQUFHLEVBQUU7WUFDekIsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDLFFBQVEsRUFBRSxVQUFVLENBQUMsQ0FBQyxDQUFDO1lBRXZDLE1BQU0sQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1lBQ3hDLE1BQU0sQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLENBQUMsU0FBUyxDQUFDLFVBQVUsQ0FBQyxDQUFDO1FBQzVDLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLCtCQUErQixFQUFFLEdBQUcsRUFBRTtZQUN2QyxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUMsVUFBVSxFQUFFLFFBQVEsQ0FBQyxDQUFDLENBQUM7WUFFdkMsTUFBTSxZQUFZLEdBQUcsTUFBTSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLEtBQUssVUFBVSxDQUFDLENBQUM7WUFDbkUsTUFBTSxDQUFDLFlBQVksQ0FBQyxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUN2QyxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyxxQ0FBcUMsRUFBRSxHQUFHLEVBQUU7WUFDN0MsTUFBTSxRQUFRLEdBQUcsS0FBSyxDQUFDLEVBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxNQUFNLENBQUMsRUFBRSxDQUFDLENBQUM7WUFFaEUsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsOENBQThDLENBQUMsQ0FBQztRQUNqRyxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyxvQkFBb0IsRUFBRSxHQUFHLEVBQUU7WUFDNUIsTUFBTSxDQUFDLFVBQVUsQ0FBQyxDQUFDLFVBQVUsQ0FBQyxDQUFDLENBQUM7WUFFaEMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxHQUFHLENBQUMsU0FBUyxDQUFDLFVBQVUsQ0FBQyxDQUFDO1FBQ2hELENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLDJCQUEyQixFQUFFLEdBQUcsRUFBRTtZQUNuQyxNQUFNLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxVQUFVLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUM3QyxNQUFNLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxhQUFhLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUNuRCxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQywrQkFBK0IsRUFBRSxHQUFHLEVBQUU7WUFDdkMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxTQUFTLENBQUMsQ0FBQyxVQUFVLEVBQUUsYUFBYSxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUNqRSxNQUFNLENBQUMsTUFBTSxDQUFDLFNBQVMsQ0FBQyxDQUFDLGFBQWEsRUFBRSxTQUFTLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDO1FBQ25FLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLGdDQUFnQyxFQUFFLEdBQUcsRUFBRTtZQUN4QyxNQUFNLENBQUMsTUFBTSxDQUFDLFVBQVUsQ0FBQyxDQUFDLFVBQVUsRUFBRSxhQUFhLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQ2xFLE1BQU0sQ0FBQyxNQUFNLENBQUMsVUFBVSxDQUFDLENBQUMsVUFBVSxFQUFFLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDckUsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyxxQkFBcUIsRUFBRSxHQUFHLEVBQUU7UUFDbkMsSUFBSSxNQUFzQixDQUFDO1FBRTNCLFVBQVUsQ0FBQyxHQUFHLEVBQUU7WUFDZCxNQUFNLEdBQUcsdUNBQWMsQ0FBQyxNQUFNLENBQUMsVUFBVSxDQUFDLENBQUM7UUFDN0MsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsd0JBQXdCLEVBQUUsR0FBRyxFQUFFO1lBQ2hDLE1BQU0sQ0FBQyxjQUFjLENBQUMsRUFBRSxNQUFNLEVBQUUsVUFBVSxFQUFFLENBQUMsQ0FBQztZQUU5QyxNQUFNLENBQUMsTUFBTSxDQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUMsQ0FBQyxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUM7WUFDaEQsTUFBTSxDQUFDLE1BQU0sQ0FBQyxRQUFRLENBQUMsTUFBTSxDQUFDLENBQUMsSUFBSSxDQUFDLGtCQUFrQixDQUFDLENBQUMsQ0FBQyx5QkFBeUI7UUFDcEYsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsNEJBQTRCLEVBQUUsR0FBRyxFQUFFO1lBQ3BDLE1BQU0sQ0FBQyxjQUFjLENBQUMsUUFBUSxDQUFDLENBQUM7WUFFaEMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxRQUFRLENBQUMsTUFBTSxDQUFDLENBQUMsYUFBYSxFQUFFLENBQUM7UUFDakQsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyxlQUFlLEVBQUUsR0FBRyxFQUFFO1FBQzdCLEVBQUUsQ0FBQyxvQ0FBb0MsRUFBRSxHQUFHLEVBQUU7WUFDNUMsTUFBTSxlQUFlLEdBQUcsdUNBQWMsQ0FBQyxNQUFNLENBQUMsRUFBRSxHQUFHLFVBQVUsRUFBRSxNQUFNLEVBQUUsaUNBQVksQ0FBQyxTQUFTLEVBQUUsVUFBVSxFQUFFLElBQUksSUFBSSxFQUFFLEVBQUUsQ0FBQyxDQUFDO1lBQ3pILE1BQU0sYUFBYSxHQUFHLHVDQUFjLENBQUMsTUFBTSxDQUFDLEVBQUUsR0FBRyxVQUFVLEVBQUUsTUFBTSxFQUFFLGlDQUFZLENBQUMsT0FBTyxFQUFFLENBQUMsQ0FBQztZQUU3RixNQUFNLENBQUMsZUFBZSxDQUFDLFVBQVUsRUFBRSxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQ2hELE1BQU0sQ0FBQyxhQUFhLENBQUMsVUFBVSxFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDakQsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsa0NBQWtDLEVBQUUsR0FBRyxFQUFFO1lBQzFDLE1BQU0sZUFBZSxHQUFHLHVDQUFjLENBQUMsTUFBTSxDQUFDLEVBQUUsR0FBRyxVQUFVLEVBQUUsTUFBTSxFQUFFLGlDQUFZLENBQUMsU0FBUyxFQUFFLFVBQVUsRUFBRSxJQUFJLElBQUksRUFBRSxFQUFFLENBQUMsQ0FBQztZQUN6SCxNQUFNLGFBQWEsR0FBRyx1Q0FBYyxDQUFDLE1BQU0sQ0FBQyxFQUFFLEdBQUcsVUFBVSxFQUFFLE1BQU0sRUFBRSxpQ0FBWSxDQUFDLE9BQU8sRUFBRSxDQUFDLENBQUM7WUFFN0YsTUFBTSxDQUFDLGVBQWUsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUMvQyxNQUFNLENBQUMsYUFBYSxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1FBQzlDLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLHNDQUFzQyxFQUFFLEdBQUcsRUFBRTtZQUM5QyxNQUFNLGVBQWUsR0FBRyx1Q0FBYyxDQUFDLE1BQU0sQ0FBQyxFQUFFLEdBQUcsVUFBVSxFQUFFLE1BQU0sRUFBRSxpQ0FBWSxDQUFDLFNBQVMsRUFBRSxVQUFVLEVBQUUsSUFBSSxJQUFJLEVBQUUsRUFBRSxDQUFDLENBQUM7WUFDekgsTUFBTSxZQUFZLEdBQUcsdUNBQWMsQ0FBQyxNQUFNLENBQUMsRUFBRSxHQUFHLFVBQVUsRUFBRSxNQUFNLEVBQUUsaUNBQVksQ0FBQyxNQUFNLEVBQUUsVUFBVSxFQUFFLElBQUksSUFBSSxFQUFFLEVBQUUsQ0FBQyxDQUFDO1lBRW5ILE1BQU0sQ0FBQyxlQUFlLENBQUMsWUFBWSxFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDbEQsTUFBTSxDQUFDLFlBQVksQ0FBQyxZQUFZLEVBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUNsRCxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyxtQ0FBbUMsRUFBRSxHQUFHLEVBQUU7WUFDM0MsTUFBTSxZQUFZLEdBQUcsdUNBQWMsQ0FBQyxNQUFNLENBQUMsRUFBRSxHQUFHLFVBQVUsRUFBRSxNQUFNLEVBQUUsaUNBQVksQ0FBQyxNQUFNLEVBQUUsVUFBVSxFQUFFLElBQUksSUFBSSxFQUFFLEVBQUUsQ0FBQyxDQUFDO1lBQ25ILE1BQU0sZUFBZSxHQUFHLHVDQUFjLENBQUMsTUFBTSxDQUFDLEVBQUUsR0FBRyxVQUFVLEVBQUUsTUFBTSxFQUFFLGlDQUFZLENBQUMsU0FBUyxFQUFFLFVBQVUsRUFBRSxJQUFJLElBQUksRUFBRSxFQUFFLENBQUMsQ0FBQztZQUV6SCxNQUFNLENBQUMsWUFBWSxDQUFDLFNBQVMsRUFBRSxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQzVDLE1BQU0sQ0FBQyxlQUFlLENBQUMsU0FBUyxFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDbEQsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsMENBQTBDLEVBQUUsR0FBRyxFQUFFO1lBQ2xELE1BQU0sY0FBYyxHQUFHLHVDQUFjLENBQUMsTUFBTSxDQUFDO2dCQUMzQyxHQUFHLFVBQVU7Z0JBQ2IsTUFBTSxFQUFFLGlDQUFZLENBQUMsT0FBTztnQkFDNUIsZ0JBQWdCLEVBQUUsSUFBSTthQUN2QixDQUFDLENBQUM7WUFDSCxNQUFNLGdCQUFnQixHQUFHLHVDQUFjLENBQUMsTUFBTSxDQUFDO2dCQUM3QyxHQUFHLFVBQVU7Z0JBQ2IsTUFBTSxFQUFFLGlDQUFZLENBQUMsT0FBTztnQkFDNUIsZ0JBQWdCLEVBQUUsS0FBSzthQUN4QixDQUFDLENBQUM7WUFFSCxNQUFNLENBQUMsY0FBYyxDQUFDLGdCQUFnQixFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDckQsTUFBTSxDQUFDLGdCQUFnQixDQUFDLGdCQUFnQixFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDMUQsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMseUNBQXlDLEVBQUUsR0FBRyxFQUFFO1lBQ2pELE1BQU0sa0JBQWtCLEdBQUcsdUNBQWMsQ0FBQyxNQUFNLENBQUMsRUFBRSxHQUFHLFVBQVUsRUFBRSxRQUFRLEVBQUUsTUFBTSxFQUFFLENBQUMsQ0FBQztZQUN0RixNQUFNLG9CQUFvQixHQUFHLHVDQUFjLENBQUMsTUFBTSxDQUFDLEVBQUUsR0FBRyxVQUFVLEVBQUUsUUFBUSxFQUFFLFFBQVEsRUFBRSxDQUFDLENBQUM7WUFFMUYsTUFBTSxDQUFDLGtCQUFrQixDQUFDLGNBQWMsRUFBRSxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQ3ZELE1BQU0sQ0FBQyxvQkFBb0IsQ0FBQyxjQUFjLEVBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUM1RCxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyxtQ0FBbUMsRUFBRSxHQUFHLEVBQUU7WUFDM0MsTUFBTSxhQUFhLEdBQUcsdUNBQWMsQ0FBQyxNQUFNLENBQUM7Z0JBQzFDLEdBQUcsVUFBVTtnQkFDYixNQUFNLEVBQUUsaUNBQVksQ0FBQyxPQUFPO2dCQUM1QixXQUFXLEVBQUUsSUFBSSxJQUFJLENBQUMsSUFBSSxDQUFDLEdBQUcsRUFBRSxHQUFHLE9BQU8sQ0FBQyxDQUFDLGFBQWE7YUFDMUQsQ0FBQyxDQUFDO1lBQ0gsTUFBTSxnQkFBZ0IsR0FBRyx1Q0FBYyxDQUFDLE1BQU0sQ0FBQztnQkFDN0MsR0FBRyxVQUFVO2dCQUNiLE1BQU0sRUFBRSxpQ0FBWSxDQUFDLE9BQU87Z0JBQzVCLFdBQVcsRUFBRSxJQUFJLElBQUksQ0FBQyxJQUFJLENBQUMsR0FBRyxFQUFFLEdBQUcsT0FBTyxDQUFDLENBQUMsa0JBQWtCO2FBQy9ELENBQUMsQ0FBQztZQUVILE1BQU0sQ0FBQyxhQUFhLENBQUMsU0FBUyxFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDN0MsTUFBTSxDQUFDLGdCQUFnQixDQUFDLFNBQVMsRUFBRSxDQUFDLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDO1FBQ25ELENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLHVCQUF1QixFQUFFLEdBQUcsRUFBRTtZQUMvQixNQUFNLE1BQU0sR0FBRyx1Q0FBYyxDQUFDLE1BQU0sQ0FBQyxVQUFVLENBQUMsQ0FBQztZQUNqRCxNQUFNLEdBQUcsR0FBRyxNQUFNLENBQUMsTUFBTSxFQUFFLENBQUM7WUFFNUIsTUFBTSxDQUFDLEdBQUcsQ0FBQyxDQUFDLHNCQUFzQixDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ3RDLE1BQU0sQ0FBQyxHQUFHLENBQUMsQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyx3QkFBd0I7UUFDMUQsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsc0NBQXNDLEVBQUUsR0FBRyxFQUFFO1lBQzlDLE1BQU0sTUFBTSxHQUFHLHVDQUFjLENBQUMsTUFBTSxDQUFDO2dCQUNuQyxHQUFHLFVBQVU7Z0JBQ2Isd0JBQXdCLEVBQUUsRUFBRTtnQkFDNUIsV0FBVyxFQUFFLElBQUksSUFBSSxFQUFFO2FBQ3hCLENBQUMsQ0FBQztZQUNILE1BQU0sY0FBYyxHQUFHLE1BQU0sQ0FBQywwQkFBMEIsRUFBRSxDQUFDO1lBRTNELE1BQU0sQ0FBQyxjQUFjLENBQUMsQ0FBQyxXQUFXLEVBQUUsQ0FBQztZQUNyQyxNQUFNLENBQUMsY0FBZSxDQUFDLE9BQU8sRUFBRSxDQUFDLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxHQUFHLEVBQUUsQ0FBQyxDQUFDO1FBQ2hFLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLHlEQUF5RCxFQUFFLEdBQUcsRUFBRTtZQUNqRSxNQUFNLGVBQWUsR0FBRyx1Q0FBYyxDQUFDLE1BQU0sQ0FBQyxFQUFFLEdBQUcsVUFBVSxFQUFFLE1BQU0sRUFBRSxpQ0FBWSxDQUFDLFNBQVMsRUFBRSxVQUFVLEVBQUUsSUFBSSxJQUFJLEVBQUUsRUFBRSxDQUFDLENBQUM7WUFDekgsTUFBTSxjQUFjLEdBQUcsZUFBZSxDQUFDLDBCQUEwQixFQUFFLENBQUM7WUFFcEUsTUFBTSxDQUFDLGNBQWMsQ0FBQyxDQUFDLFFBQVEsRUFBRSxDQUFDO1FBQ3BDLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsb0JBQW9CLEVBQUUsR0FBRyxFQUFFO1FBQ2xDLEVBQUUsQ0FBQyxvQ0FBb0MsRUFBRSxHQUFHLEVBQUU7WUFDNUMsTUFBTSxNQUFNLEdBQUcsdUNBQWMsQ0FBQyxNQUFNLENBQUMsVUFBVSxDQUFDLENBQUM7WUFDakQsTUFBTSxJQUFJLEdBQUcsTUFBTSxDQUFDLE1BQU0sRUFBRSxDQUFDO1lBRTdCLE1BQU0sQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUMsV0FBVyxFQUFFLENBQUM7WUFDOUIsTUFBTSxDQUFDLElBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQyxJQUFJLENBQUMsNkJBQVUsQ0FBQyxRQUFRLENBQUMsQ0FBQztZQUNsRCxNQUFNLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDLElBQUksQ0FBQyxpQ0FBWSxDQUFDLE9BQU8sQ0FBQyxDQUFDO1lBQy9DLE1BQU0sQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsSUFBSSxDQUFDLDRCQUE0QixDQUFDLENBQUM7WUFDdEQsTUFBTSxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUM7WUFDbkMsTUFBTSxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDcEMsTUFBTSxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDckMsTUFBTSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQyxXQUFXLEVBQUUsQ0FBQztZQUNuQyxNQUFNLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxRQUFRLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDM0MsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQywrQkFBK0IsRUFBRSxHQUFHLEVBQUU7UUFDN0MsRUFBRSxDQUFDLDJCQUEyQixFQUFFLEdBQUcsRUFBRTtZQUNuQyxNQUFNLE1BQU0sR0FBRyx1Q0FBYyxDQUFDLE1BQU0sQ0FBQyxVQUFVLENBQUMsQ0FBQztZQUNqRCxNQUFNLGFBQWEsR0FBRyxVQUFVLENBQUM7WUFFakMsTUFBTSxDQUFDLGdCQUFnQixDQUFDLGFBQWEsQ0FBQyxDQUFDO1lBRXZDLE1BQU0sQ0FBQyxNQUFNLENBQUMsYUFBYSxDQUFDLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDO1FBQ25ELENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLGdDQUFnQyxFQUFFLEdBQUcsRUFBRTtZQUN4QyxNQUFNLE9BQU8sR0FBRyw4QkFBYyxDQUFDLFFBQVEsRUFBRSxDQUFDO1lBQzFDLE1BQU0sUUFBUSxHQUFHLDhCQUFjLENBQUMsUUFBUSxFQUFFLENBQUM7WUFDM0MsTUFBTSxlQUFlLEdBQUcsOEJBQWMsQ0FBQyxRQUFRLEVBQUUsQ0FBQztZQUVsRCxNQUFNLE1BQU0sR0FBRyx1Q0FBYyxDQUFDLE1BQU0sQ0FBQztnQkFDbkMsR0FBRyxVQUFVO2dCQUNiLGNBQWMsRUFBRSxPQUFPO2dCQUN2QixlQUFlLEVBQUUsUUFBUTtnQkFDekIsc0JBQXNCLEVBQUUsZUFBZTthQUN4QyxDQUFDLENBQUM7WUFFSCxNQUFNLENBQUMsTUFBTSxDQUFDLGNBQWMsRUFBRSxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDMUQsTUFBTSxDQUFDLE1BQU0sQ0FBQyxlQUFlLEVBQUUsTUFBTSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQzVELE1BQU0sQ0FBQyxNQUFNLENBQUMsc0JBQXNCLEVBQUUsTUFBTSxDQUFDLGVBQWUsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1FBQzVFLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7QUFDTCxDQUFDLENBQUMsQ0FBQyIsIm5hbWVzIjpbXSwic291cmNlcyI6WyJDOlxcVXNlcnNcXEx1a2FcXHNlbnRpbmVsXFxiYWNrZW5kXFxzcmNcXGNvcmVcXHNlY3VyaXR5XFxkb21haW5cXGVudGl0aWVzXFxfX3Rlc3RzX19cXHJlc3BvbnNlLWFjdGlvbi5lbnRpdHkuc3BlYy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBSZXNwb25zZUFjdGlvbiwgUmVzcG9uc2VBY3Rpb25Qcm9wcyB9IGZyb20gJy4uL3Jlc3BvbnNlLWFjdGlvbi5lbnRpdHknO1xyXG5pbXBvcnQgeyBVbmlxdWVFbnRpdHlJZCB9IGZyb20gJy4uLy4uLy4uLy4uLy4uL3NoYXJlZC1rZXJuZWwnO1xyXG5pbXBvcnQgeyBBY3Rpb25UeXBlIH0gZnJvbSAnLi4vLi4vZW51bXMvYWN0aW9uLXR5cGUuZW51bSc7XHJcbmltcG9ydCB7IEFjdGlvblN0YXR1cyB9IGZyb20gJy4uLy4uL2VudW1zL2FjdGlvbi1zdGF0dXMuZW51bSc7XHJcbmltcG9ydCB7IFJlc3BvbnNlQWN0aW9uQ3JlYXRlZERvbWFpbkV2ZW50IH0gZnJvbSAnLi4vLi4vZXZlbnRzL3Jlc3BvbnNlLWFjdGlvbi1jcmVhdGVkLmRvbWFpbi1ldmVudCc7XHJcbmltcG9ydCB7IFJlc3BvbnNlQWN0aW9uU3RhdHVzQ2hhbmdlZERvbWFpbkV2ZW50IH0gZnJvbSAnLi4vLi4vZXZlbnRzL3Jlc3BvbnNlLWFjdGlvbi1zdGF0dXMtY2hhbmdlZC5kb21haW4tZXZlbnQnO1xyXG5pbXBvcnQgeyBSZXNwb25zZUFjdGlvbkV4ZWN1dGVkRG9tYWluRXZlbnQgfSBmcm9tICcuLi8uLi9ldmVudHMvcmVzcG9uc2UtYWN0aW9uLWV4ZWN1dGVkLmRvbWFpbi1ldmVudCc7XHJcbmltcG9ydCB7IFJlc3BvbnNlQWN0aW9uRmFpbGVkRG9tYWluRXZlbnQgfSBmcm9tICcuLi8uLi9ldmVudHMvcmVzcG9uc2UtYWN0aW9uLWZhaWxlZC5kb21haW4tZXZlbnQnO1xyXG5pbXBvcnQgeyBSZXNwb25zZUFjdGlvblJvbGxlZEJhY2tEb21haW5FdmVudCB9IGZyb20gJy4uLy4uL2V2ZW50cy9yZXNwb25zZS1hY3Rpb24tcm9sbGVkLWJhY2suZG9tYWluLWV2ZW50JztcclxuXHJcbmRlc2NyaWJlKCdSZXNwb25zZUFjdGlvbiBFbnRpdHknLCAoKSA9PiB7XHJcbiAgbGV0IHZhbGlkUHJvcHM6IFJlc3BvbnNlQWN0aW9uUHJvcHM7XHJcblxyXG4gIGJlZm9yZUVhY2goKCkgPT4ge1xyXG4gICAgdmFsaWRQcm9wcyA9IHtcclxuICAgICAgYWN0aW9uVHlwZTogQWN0aW9uVHlwZS5CTE9DS19JUCxcclxuICAgICAgc3RhdHVzOiBBY3Rpb25TdGF0dXMuUEVORElORyxcclxuICAgICAgdGl0bGU6ICdCbG9jayBtYWxpY2lvdXMgSVAgYWRkcmVzcycsXHJcbiAgICAgIGRlc2NyaXB0aW9uOiAnQmxvY2sgSVAgYWRkcmVzcyBpZGVudGlmaWVkIGFzIG1hbGljaW91cycsXHJcbiAgICAgIHBhcmFtZXRlcnM6IHsgaXBBZGRyZXNzOiAnMTkyLjE2OC4xLjEwMCcgfSxcclxuICAgICAgdGFyZ2V0OiB7XHJcbiAgICAgICAgdHlwZTogJ3N5c3RlbScsXHJcbiAgICAgICAgaWQ6ICdmaXJld2FsbC0wMDEnLFxyXG4gICAgICAgIG5hbWU6ICdNYWluIEZpcmV3YWxsJyxcclxuICAgICAgfSxcclxuICAgICAgcHJpb3JpdHk6ICdoaWdoJyxcclxuICAgICAgaXNBdXRvbWF0ZWQ6IHRydWUsXHJcbiAgICAgIGlzUmV2ZXJzaWJsZTogdHJ1ZSxcclxuICAgICAgZXN0aW1hdGVkRHVyYXRpb25NaW51dGVzOiA1LFxyXG4gICAgICByZXF1aXJlZFBlcm1pc3Npb25zOiBbJ25ldHdvcmsuZmlyZXdhbGwud3JpdGUnXSxcclxuICAgICAgYXBwcm92YWxSZXF1aXJlZDogZmFsc2UsXHJcbiAgICAgIHN1Y2Nlc3NDcml0ZXJpYTogWydJUCBhZGRyZXNzIGJsb2NrZWQgaW4gZmlyZXdhbGwnLCAnVHJhZmZpYyBmcm9tIElQIHN0b3BwZWQnXSxcclxuICAgICAgcmV0cnlDb3VudDogMCxcclxuICAgICAgbWF4UmV0cmllczogMyxcclxuICAgICAgcmV0cnlEZWxheU1pbnV0ZXM6IDUsXHJcbiAgICAgIHJvbGxiYWNrSW5mbzoge1xyXG4gICAgICAgIGNhblJvbGxiYWNrOiB0cnVlLFxyXG4gICAgICAgIHJvbGxiYWNrU3RlcHM6IFsnUmVtb3ZlIElQIGZyb20gYmxvY2sgbGlzdCcsICdVcGRhdGUgZmlyZXdhbGwgcnVsZXMnXSxcclxuICAgICAgfSxcclxuICAgICAgcm9sbGVkQmFjazogZmFsc2UsXHJcbiAgICAgIHRhZ3M6IFsnc2VjdXJpdHknLCAnY29udGFpbm1lbnQnXSxcclxuICAgICAgbWV0YWRhdGE6IHsgc291cmNlOiAndGhyZWF0LWRldGVjdGlvbicgfSxcclxuICAgICAgY2hpbGRBY3Rpb25JZHM6IFtdLFxyXG4gICAgICB0aW1lb3V0TWludXRlczogMzAsXHJcbiAgICAgIHRpbWVkT3V0OiBmYWxzZSxcclxuICAgIH07XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCdjcmVhdGlvbicsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgY3JlYXRlIGEgdmFsaWQgUmVzcG9uc2VBY3Rpb24nLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGFjdGlvbiA9IFJlc3BvbnNlQWN0aW9uLmNyZWF0ZSh2YWxpZFByb3BzKTtcclxuXHJcbiAgICAgIGV4cGVjdChhY3Rpb24pLnRvQmVJbnN0YW5jZU9mKFJlc3BvbnNlQWN0aW9uKTtcclxuICAgICAgZXhwZWN0KGFjdGlvbi5hY3Rpb25UeXBlKS50b0JlKEFjdGlvblR5cGUuQkxPQ0tfSVApO1xyXG4gICAgICBleHBlY3QoYWN0aW9uLnN0YXR1cykudG9CZShBY3Rpb25TdGF0dXMuUEVORElORyk7XHJcbiAgICAgIGV4cGVjdChhY3Rpb24udGl0bGUpLnRvQmUoJ0Jsb2NrIG1hbGljaW91cyBJUCBhZGRyZXNzJyk7XHJcbiAgICAgIGV4cGVjdChhY3Rpb24ucHJpb3JpdHkpLnRvQmUoJ2hpZ2gnKTtcclxuICAgICAgZXhwZWN0KGFjdGlvbi5pc0F1dG9tYXRlZCkudG9CZSh0cnVlKTtcclxuICAgICAgZXhwZWN0KGFjdGlvbi5pc1JldmVyc2libGUpLnRvQmUodHJ1ZSk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIGdlbmVyYXRlIFJlc3BvbnNlQWN0aW9uQ3JlYXRlZERvbWFpbkV2ZW50IG9uIGNyZWF0aW9uJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBhY3Rpb24gPSBSZXNwb25zZUFjdGlvbi5jcmVhdGUodmFsaWRQcm9wcyk7XHJcbiAgICAgIGNvbnN0IGV2ZW50cyA9IGFjdGlvbi5nZXRVbnB1Ymxpc2hlZEV2ZW50cygpO1xyXG5cclxuICAgICAgZXhwZWN0KGV2ZW50cykudG9IYXZlTGVuZ3RoKDEpO1xyXG4gICAgICBleHBlY3QoZXZlbnRzWzBdKS50b0JlSW5zdGFuY2VPZihSZXNwb25zZUFjdGlvbkNyZWF0ZWREb21haW5FdmVudCk7XHJcbiAgICAgIFxyXG4gICAgICBjb25zdCBjcmVhdGVkRXZlbnQgPSBldmVudHNbMF0gYXMgUmVzcG9uc2VBY3Rpb25DcmVhdGVkRG9tYWluRXZlbnQ7XHJcbiAgICAgIGV4cGVjdChjcmVhdGVkRXZlbnQuYWN0aW9uVHlwZSkudG9CZShBY3Rpb25UeXBlLkJMT0NLX0lQKTtcclxuICAgICAgZXhwZWN0KGNyZWF0ZWRFdmVudC50aXRsZSkudG9CZSgnQmxvY2sgbWFsaWNpb3VzIElQIGFkZHJlc3MnKTtcclxuICAgICAgZXhwZWN0KGNyZWF0ZWRFdmVudC5wcmlvcml0eSkudG9CZSgnaGlnaCcpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBjcmVhdGUgd2l0aCBjdXN0b20gSUQnLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGN1c3RvbUlkID0gVW5pcXVlRW50aXR5SWQuZ2VuZXJhdGUoKTtcclxuICAgICAgY29uc3QgYWN0aW9uID0gUmVzcG9uc2VBY3Rpb24uY3JlYXRlKHZhbGlkUHJvcHMsIGN1c3RvbUlkKTtcclxuXHJcbiAgICAgIGV4cGVjdChhY3Rpb24uaWQuZXF1YWxzKGN1c3RvbUlkKSkudG9CZSh0cnVlKTtcclxuICAgIH0pO1xyXG4gIH0pO1xyXG5cclxuICBkZXNjcmliZSgndmFsaWRhdGlvbicsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgdGhyb3cgZXJyb3IgaWYgYWN0aW9uVHlwZSBpcyBtaXNzaW5nJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBpbnZhbGlkUHJvcHMgPSB7IC4uLnZhbGlkUHJvcHMgfTtcclxuICAgICAgZGVsZXRlIChpbnZhbGlkUHJvcHMgYXMgYW55KS5hY3Rpb25UeXBlO1xyXG5cclxuICAgICAgZXhwZWN0KCgpID0+IFJlc3BvbnNlQWN0aW9uLmNyZWF0ZShpbnZhbGlkUHJvcHMpKS50b1Rocm93KCdSZXNwb25zZUFjdGlvbiBtdXN0IGhhdmUgYW4gYWN0aW9uIHR5cGUnKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgdGhyb3cgZXJyb3IgaWYgc3RhdHVzIGlzIG1pc3NpbmcnLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGludmFsaWRQcm9wcyA9IHsgLi4udmFsaWRQcm9wcyB9O1xyXG4gICAgICBkZWxldGUgKGludmFsaWRQcm9wcyBhcyBhbnkpLnN0YXR1cztcclxuXHJcbiAgICAgIGV4cGVjdCgoKSA9PiBSZXNwb25zZUFjdGlvbi5jcmVhdGUoaW52YWxpZFByb3BzKSkudG9UaHJvdygnUmVzcG9uc2VBY3Rpb24gbXVzdCBoYXZlIGEgc3RhdHVzJyk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIHRocm93IGVycm9yIGlmIHRpdGxlIGlzIGVtcHR5JywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBpbnZhbGlkUHJvcHMgPSB7IC4uLnZhbGlkUHJvcHMsIHRpdGxlOiAnJyB9O1xyXG5cclxuICAgICAgZXhwZWN0KCgpID0+IFJlc3BvbnNlQWN0aW9uLmNyZWF0ZShpbnZhbGlkUHJvcHMpKS50b1Rocm93KCdSZXNwb25zZUFjdGlvbiBtdXN0IGhhdmUgYSBub24tZW1wdHkgdGl0bGUnKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgdGhyb3cgZXJyb3IgaWYgdGl0bGUgaXMgdG9vIGxvbmcnLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGludmFsaWRQcm9wcyA9IHsgLi4udmFsaWRQcm9wcywgdGl0bGU6ICdhJy5yZXBlYXQoMjAxKSB9O1xyXG5cclxuICAgICAgZXhwZWN0KCgpID0+IFJlc3BvbnNlQWN0aW9uLmNyZWF0ZShpbnZhbGlkUHJvcHMpKS50b1Rocm93KCdSZXNwb25zZUFjdGlvbiB0aXRsZSBjYW5ub3QgZXhjZWVkIDIwMCBjaGFyYWN0ZXJzJyk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIHRocm93IGVycm9yIGlmIGRlc2NyaXB0aW9uIGlzIHRvbyBsb25nJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBpbnZhbGlkUHJvcHMgPSB7IC4uLnZhbGlkUHJvcHMsIGRlc2NyaXB0aW9uOiAnYScucmVwZWF0KDIwMDEpIH07XHJcblxyXG4gICAgICBleHBlY3QoKCkgPT4gUmVzcG9uc2VBY3Rpb24uY3JlYXRlKGludmFsaWRQcm9wcykpLnRvVGhyb3coJ1Jlc3BvbnNlQWN0aW9uIGRlc2NyaXB0aW9uIGNhbm5vdCBleGNlZWQgMjAwMCBjaGFyYWN0ZXJzJyk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIHRocm93IGVycm9yIGlmIHBhcmFtZXRlcnMgaXMgbWlzc2luZycsICgpID0+IHtcclxuICAgICAgY29uc3QgaW52YWxpZFByb3BzID0geyAuLi52YWxpZFByb3BzIH07XHJcbiAgICAgIGRlbGV0ZSAoaW52YWxpZFByb3BzIGFzIGFueSkucGFyYW1ldGVycztcclxuXHJcbiAgICAgIGV4cGVjdCgoKSA9PiBSZXNwb25zZUFjdGlvbi5jcmVhdGUoaW52YWxpZFByb3BzKSkudG9UaHJvdygnUmVzcG9uc2VBY3Rpb24gbXVzdCBoYXZlIHBhcmFtZXRlcnMnKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgdGhyb3cgZXJyb3IgaWYgcHJpb3JpdHkgaXMgbWlzc2luZycsICgpID0+IHtcclxuICAgICAgY29uc3QgaW52YWxpZFByb3BzID0geyAuLi52YWxpZFByb3BzIH07XHJcbiAgICAgIGRlbGV0ZSAoaW52YWxpZFByb3BzIGFzIGFueSkucHJpb3JpdHk7XHJcblxyXG4gICAgICBleHBlY3QoKCkgPT4gUmVzcG9uc2VBY3Rpb24uY3JlYXRlKGludmFsaWRQcm9wcykpLnRvVGhyb3coJ1Jlc3BvbnNlQWN0aW9uIG11c3QgaGF2ZSBhIHByaW9yaXR5Jyk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIHRocm93IGVycm9yIGlmIGlzQXV0b21hdGVkIGlzIHVuZGVmaW5lZCcsICgpID0+IHtcclxuICAgICAgY29uc3QgaW52YWxpZFByb3BzID0geyAuLi52YWxpZFByb3BzIH07XHJcbiAgICAgIGRlbGV0ZSAoaW52YWxpZFByb3BzIGFzIGFueSkuaXNBdXRvbWF0ZWQ7XHJcblxyXG4gICAgICBleHBlY3QoKCkgPT4gUmVzcG9uc2VBY3Rpb24uY3JlYXRlKGludmFsaWRQcm9wcykpLnRvVGhyb3coJ1Jlc3BvbnNlQWN0aW9uIG11c3Qgc3BlY2lmeSBpZiBpdCBpcyBhdXRvbWF0ZWQnKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgdGhyb3cgZXJyb3IgaWYgaXNSZXZlcnNpYmxlIGlzIHVuZGVmaW5lZCcsICgpID0+IHtcclxuICAgICAgY29uc3QgaW52YWxpZFByb3BzID0geyAuLi52YWxpZFByb3BzIH07XHJcbiAgICAgIGRlbGV0ZSAoaW52YWxpZFByb3BzIGFzIGFueSkuaXNSZXZlcnNpYmxlO1xyXG5cclxuICAgICAgZXhwZWN0KCgpID0+IFJlc3BvbnNlQWN0aW9uLmNyZWF0ZShpbnZhbGlkUHJvcHMpKS50b1Rocm93KCdSZXNwb25zZUFjdGlvbiBtdXN0IHNwZWNpZnkgaWYgaXQgaXMgcmV2ZXJzaWJsZScpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCB0aHJvdyBlcnJvciBpZiByZXF1aXJlZFBlcm1pc3Npb25zIGlzIG5vdCBhbiBhcnJheScsICgpID0+IHtcclxuICAgICAgY29uc3QgaW52YWxpZFByb3BzID0geyAuLi52YWxpZFByb3BzLCByZXF1aXJlZFBlcm1pc3Npb25zOiAnbm90LWFuLWFycmF5JyBhcyBhbnkgfTtcclxuXHJcbiAgICAgIGV4cGVjdCgoKSA9PiBSZXNwb25zZUFjdGlvbi5jcmVhdGUoaW52YWxpZFByb3BzKSkudG9UaHJvdygnUmVzcG9uc2VBY3Rpb24gbXVzdCBoYXZlIHJlcXVpcmVkIHBlcm1pc3Npb25zIGFycmF5Jyk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIHRocm93IGVycm9yIGlmIGFwcHJvdmFsUmVxdWlyZWQgaXMgdW5kZWZpbmVkJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBpbnZhbGlkUHJvcHMgPSB7IC4uLnZhbGlkUHJvcHMgfTtcclxuICAgICAgZGVsZXRlIChpbnZhbGlkUHJvcHMgYXMgYW55KS5hcHByb3ZhbFJlcXVpcmVkO1xyXG5cclxuICAgICAgZXhwZWN0KCgpID0+IFJlc3BvbnNlQWN0aW9uLmNyZWF0ZShpbnZhbGlkUHJvcHMpKS50b1Rocm93KCdSZXNwb25zZUFjdGlvbiBtdXN0IHNwZWNpZnkgaWYgYXBwcm92YWwgaXMgcmVxdWlyZWQnKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgdGhyb3cgZXJyb3IgaWYgc3VjY2Vzc0NyaXRlcmlhIGlzIG5vdCBhbiBhcnJheScsICgpID0+IHtcclxuICAgICAgY29uc3QgaW52YWxpZFByb3BzID0geyAuLi52YWxpZFByb3BzLCBzdWNjZXNzQ3JpdGVyaWE6ICdub3QtYW4tYXJyYXknIGFzIGFueSB9O1xyXG5cclxuICAgICAgZXhwZWN0KCgpID0+IFJlc3BvbnNlQWN0aW9uLmNyZWF0ZShpbnZhbGlkUHJvcHMpKS50b1Rocm93KCdSZXNwb25zZUFjdGlvbiBtdXN0IGhhdmUgc3VjY2VzcyBjcml0ZXJpYSBhcnJheScpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCB0aHJvdyBlcnJvciBpZiByZXRyeUNvdW50IGlzIG5lZ2F0aXZlJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBpbnZhbGlkUHJvcHMgPSB7IC4uLnZhbGlkUHJvcHMsIHJldHJ5Q291bnQ6IC0xIH07XHJcblxyXG4gICAgICBleHBlY3QoKCkgPT4gUmVzcG9uc2VBY3Rpb24uY3JlYXRlKGludmFsaWRQcm9wcykpLnRvVGhyb3coJ1JldHJ5IGNvdW50IGNhbm5vdCBiZSBuZWdhdGl2ZScpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCB0aHJvdyBlcnJvciBpZiBtYXhSZXRyaWVzIGlzIG5lZ2F0aXZlJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBpbnZhbGlkUHJvcHMgPSB7IC4uLnZhbGlkUHJvcHMsIG1heFJldHJpZXM6IC0xIH07XHJcblxyXG4gICAgICBleHBlY3QoKCkgPT4gUmVzcG9uc2VBY3Rpb24uY3JlYXRlKGludmFsaWRQcm9wcykpLnRvVGhyb3coJ01heCByZXRyaWVzIGNhbm5vdCBiZSBuZWdhdGl2ZScpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCB0aHJvdyBlcnJvciBpZiByZXRyeURlbGF5TWludXRlcyBpcyBuZWdhdGl2ZScsICgpID0+IHtcclxuICAgICAgY29uc3QgaW52YWxpZFByb3BzID0geyAuLi52YWxpZFByb3BzLCByZXRyeURlbGF5TWludXRlczogLTEgfTtcclxuXHJcbiAgICAgIGV4cGVjdCgoKSA9PiBSZXNwb25zZUFjdGlvbi5jcmVhdGUoaW52YWxpZFByb3BzKSkudG9UaHJvdygnUmV0cnkgZGVsYXkgY2Fubm90IGJlIG5lZ2F0aXZlJyk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIHRocm93IGVycm9yIGlmIHRhZ3MgaXMgbm90IGFuIGFycmF5JywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBpbnZhbGlkUHJvcHMgPSB7IC4uLnZhbGlkUHJvcHMsIHRhZ3M6ICdub3QtYW4tYXJyYXknIGFzIGFueSB9O1xyXG5cclxuICAgICAgZXhwZWN0KCgpID0+IFJlc3BvbnNlQWN0aW9uLmNyZWF0ZShpbnZhbGlkUHJvcHMpKS50b1Rocm93KCdSZXNwb25zZUFjdGlvbiBtdXN0IGhhdmUgdGFncyBhcnJheScpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCB0aHJvdyBlcnJvciBpZiB0b28gbWFueSB0YWdzJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBpbnZhbGlkUHJvcHMgPSB7IC4uLnZhbGlkUHJvcHMsIHRhZ3M6IEFycmF5KDIxKS5maWxsKCd0YWcnKSB9O1xyXG5cclxuICAgICAgZXhwZWN0KCgpID0+IFJlc3BvbnNlQWN0aW9uLmNyZWF0ZShpbnZhbGlkUHJvcHMpKS50b1Rocm93KCdSZXNwb25zZUFjdGlvbiBjYW5ub3QgaGF2ZSBtb3JlIHRoYW4gMjAgdGFncycpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCB0aHJvdyBlcnJvciBpZiBtZXRhZGF0YSBpcyBtaXNzaW5nJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBpbnZhbGlkUHJvcHMgPSB7IC4uLnZhbGlkUHJvcHMgfTtcclxuICAgICAgZGVsZXRlIChpbnZhbGlkUHJvcHMgYXMgYW55KS5tZXRhZGF0YTtcclxuXHJcbiAgICAgIGV4cGVjdCgoKSA9PiBSZXNwb25zZUFjdGlvbi5jcmVhdGUoaW52YWxpZFByb3BzKSkudG9UaHJvdygnUmVzcG9uc2VBY3Rpb24gbXVzdCBoYXZlIG1ldGFkYXRhIG9iamVjdCcpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCB0aHJvdyBlcnJvciBpZiBjaGlsZEFjdGlvbklkcyBpcyBub3QgYW4gYXJyYXknLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGludmFsaWRQcm9wcyA9IHsgLi4udmFsaWRQcm9wcywgY2hpbGRBY3Rpb25JZHM6ICdub3QtYW4tYXJyYXknIGFzIGFueSB9O1xyXG5cclxuICAgICAgZXhwZWN0KCgpID0+IFJlc3BvbnNlQWN0aW9uLmNyZWF0ZShpbnZhbGlkUHJvcHMpKS50b1Rocm93KCdSZXNwb25zZUFjdGlvbiBtdXN0IGhhdmUgY2hpbGQgYWN0aW9uIElEcyBhcnJheScpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCB0aHJvdyBlcnJvciBpZiB0aW1lb3V0TWludXRlcyBpcyBub3QgcG9zaXRpdmUnLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGludmFsaWRQcm9wcyA9IHsgLi4udmFsaWRQcm9wcywgdGltZW91dE1pbnV0ZXM6IDAgfTtcclxuXHJcbiAgICAgIGV4cGVjdCgoKSA9PiBSZXNwb25zZUFjdGlvbi5jcmVhdGUoaW52YWxpZFByb3BzKSkudG9UaHJvdygnVGltZW91dCBtaW51dGVzIG11c3QgYmUgcG9zaXRpdmUnKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgdGhyb3cgZXJyb3IgaWYgdGltZWRPdXQgaXMgdW5kZWZpbmVkJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBpbnZhbGlkUHJvcHMgPSB7IC4uLnZhbGlkUHJvcHMgfTtcclxuICAgICAgZGVsZXRlIChpbnZhbGlkUHJvcHMgYXMgYW55KS50aW1lZE91dDtcclxuXHJcbiAgICAgIGV4cGVjdCgoKSA9PiBSZXNwb25zZUFjdGlvbi5jcmVhdGUoaW52YWxpZFByb3BzKSkudG9UaHJvdygnUmVzcG9uc2VBY3Rpb24gbXVzdCBzcGVjaWZ5IGlmIGl0IHRpbWVkIG91dCcpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCB0aHJvdyBlcnJvciBpZiByb2xsZWRCYWNrIGlzIHVuZGVmaW5lZCcsICgpID0+IHtcclxuICAgICAgY29uc3QgaW52YWxpZFByb3BzID0geyAuLi52YWxpZFByb3BzIH07XHJcbiAgICAgIGRlbGV0ZSAoaW52YWxpZFByb3BzIGFzIGFueSkucm9sbGVkQmFjaztcclxuXHJcbiAgICAgIGV4cGVjdCgoKSA9PiBSZXNwb25zZUFjdGlvbi5jcmVhdGUoaW52YWxpZFByb3BzKSkudG9UaHJvdygnUmVzcG9uc2VBY3Rpb24gbXVzdCBzcGVjaWZ5IGlmIGl0IHdhcyByb2xsZWQgYmFjaycpO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCdzdGF0dXMgY2hhbmdlcycsICgpID0+IHtcclxuICAgIGxldCBhY3Rpb246IFJlc3BvbnNlQWN0aW9uO1xyXG5cclxuICAgIGJlZm9yZUVhY2goKCkgPT4ge1xyXG4gICAgICBhY3Rpb24gPSBSZXNwb25zZUFjdGlvbi5jcmVhdGUodmFsaWRQcm9wcyk7XHJcbiAgICAgIGFjdGlvbi5jbGVhckV2ZW50cygpOyAvLyBDbGVhciBjcmVhdGlvbiBldmVudFxyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBjaGFuZ2Ugc3RhdHVzIHN1Y2Nlc3NmdWxseScsICgpID0+IHtcclxuICAgICAgYWN0aW9uLmNoYW5nZVN0YXR1cyhBY3Rpb25TdGF0dXMuQVBQUk9WRUQsICdhZG1pbicsICdBcHByb3ZlZCBmb3IgZXhlY3V0aW9uJyk7XHJcblxyXG4gICAgICBleHBlY3QoYWN0aW9uLnN0YXR1cykudG9CZShBY3Rpb25TdGF0dXMuQVBQUk9WRUQpO1xyXG4gICAgICBleHBlY3QoYWN0aW9uLmFwcHJvdmVkQnkpLnRvQmUoJ2FkbWluJyk7XHJcbiAgICAgIGV4cGVjdChhY3Rpb24uYXBwcm92ZWRBdCkudG9CZURlZmluZWQoKTtcclxuXHJcbiAgICAgIGNvbnN0IGV2ZW50cyA9IGFjdGlvbi5nZXRVbnB1Ymxpc2hlZEV2ZW50cygpO1xyXG4gICAgICBleHBlY3QoZXZlbnRzKS50b0hhdmVMZW5ndGgoMSk7XHJcbiAgICAgIGV4cGVjdChldmVudHNbMF0pLnRvQmVJbnN0YW5jZU9mKFJlc3BvbnNlQWN0aW9uU3RhdHVzQ2hhbmdlZERvbWFpbkV2ZW50KTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgbm90IGNoYW5nZSBzdGF0dXMgaWYgc2FtZSBzdGF0dXMnLCAoKSA9PiB7XHJcbiAgICAgIGFjdGlvbi5jaGFuZ2VTdGF0dXMoQWN0aW9uU3RhdHVzLlBFTkRJTkcpO1xyXG5cclxuICAgICAgY29uc3QgZXZlbnRzID0gYWN0aW9uLmdldFVucHVibGlzaGVkRXZlbnRzKCk7XHJcbiAgICAgIGV4cGVjdChldmVudHMpLnRvSGF2ZUxlbmd0aCgwKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgdGhyb3cgZXJyb3IgZm9yIGludmFsaWQgc3RhdHVzIHRyYW5zaXRpb24nLCAoKSA9PiB7XHJcbiAgICAgIGV4cGVjdCgoKSA9PiBhY3Rpb24uY2hhbmdlU3RhdHVzKEFjdGlvblN0YXR1cy5DT01QTEVURUQpKS50b1Rocm93KCdJbnZhbGlkIHN0YXR1cyB0cmFuc2l0aW9uJyk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIGhhbmRsZSB0aW1lb3V0IHN0YXR1cyBjaGFuZ2UnLCAoKSA9PiB7XHJcbiAgICAgIGFjdGlvbi5jaGFuZ2VTdGF0dXMoQWN0aW9uU3RhdHVzLkFQUFJPVkVEKTtcclxuICAgICAgYWN0aW9uLmNoYW5nZVN0YXR1cyhBY3Rpb25TdGF0dXMuUVVFVUVEKTtcclxuICAgICAgYWN0aW9uLmNoYW5nZVN0YXR1cyhBY3Rpb25TdGF0dXMuRVhFQ1VUSU5HKTtcclxuICAgICAgYWN0aW9uLmNoYW5nZVN0YXR1cyhBY3Rpb25TdGF0dXMuVElNRU9VVCk7XHJcblxyXG4gICAgICBleHBlY3QoYWN0aW9uLnN0YXR1cykudG9CZShBY3Rpb25TdGF0dXMuVElNRU9VVCk7XHJcbiAgICAgIGV4cGVjdChhY3Rpb24udGltZWRPdXQpLnRvQmUodHJ1ZSk7XHJcbiAgICAgIGV4cGVjdChhY3Rpb24udGltZWRPdXRBdCkudG9CZURlZmluZWQoKTtcclxuICAgIH0pO1xyXG4gIH0pO1xyXG5cclxuICBkZXNjcmliZSgnZXhlY3V0aW9uJywgKCkgPT4ge1xyXG4gICAgbGV0IGFjdGlvbjogUmVzcG9uc2VBY3Rpb247XHJcblxyXG4gICAgYmVmb3JlRWFjaCgoKSA9PiB7XHJcbiAgICAgIGFjdGlvbiA9IFJlc3BvbnNlQWN0aW9uLmNyZWF0ZSh7IC4uLnZhbGlkUHJvcHMsIHN0YXR1czogQWN0aW9uU3RhdHVzLkVYRUNVVElORyB9KTtcclxuICAgICAgYWN0aW9uLmNsZWFyRXZlbnRzKCk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIGV4ZWN1dGUgc3VjY2Vzc2Z1bGx5JywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBleGVjdXRpb25SZXN1bHRzID0geyBibG9ja2VkOiB0cnVlLCBydWxlSWQ6ICdydWxlLTEyMycgfTtcclxuICAgICAgXHJcbiAgICAgIGFjdGlvbi5leGVjdXRlKCdzeXN0ZW0nLCBleGVjdXRpb25SZXN1bHRzKTtcclxuXHJcbiAgICAgIGV4cGVjdChhY3Rpb24uc3RhdHVzKS50b0JlKEFjdGlvblN0YXR1cy5DT01QTEVURUQpO1xyXG4gICAgICBleHBlY3QoYWN0aW9uLmV4ZWN1dGVkQnkpLnRvQmUoJ3N5c3RlbScpO1xyXG4gICAgICBleHBlY3QoYWN0aW9uLmV4ZWN1dGVkQXQpLnRvQmVEZWZpbmVkKCk7XHJcbiAgICAgIGV4cGVjdChhY3Rpb24uZXhlY3V0aW9uUmVzdWx0cykudG9FcXVhbChleGVjdXRpb25SZXN1bHRzKTtcclxuICAgICAgZXhwZWN0KGFjdGlvbi5zdWNjZXNzQ3JpdGVyaWFNZXQpLnRvQmUodHJ1ZSk7XHJcblxyXG4gICAgICBjb25zdCBldmVudHMgPSBhY3Rpb24uZ2V0VW5wdWJsaXNoZWRFdmVudHMoKTtcclxuICAgICAgZXhwZWN0KGV2ZW50cykudG9IYXZlTGVuZ3RoKDEpO1xyXG4gICAgICBleHBlY3QoZXZlbnRzWzBdKS50b0JlSW5zdGFuY2VPZihSZXNwb25zZUFjdGlvbkV4ZWN1dGVkRG9tYWluRXZlbnQpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCB0aHJvdyBlcnJvciBpZiBub3QgaW4gZXhlY3V0aW5nIHN0YXR1cycsICgpID0+IHtcclxuICAgICAgY29uc3QgcGVuZGluZ0FjdGlvbiA9IFJlc3BvbnNlQWN0aW9uLmNyZWF0ZSh2YWxpZFByb3BzKTtcclxuICAgICAgXHJcbiAgICAgIGV4cGVjdCgoKSA9PiBwZW5kaW5nQWN0aW9uLmV4ZWN1dGUoJ3N5c3RlbScpKS50b1Rocm93KCdBY3Rpb24gbXVzdCBiZSBpbiBleGVjdXRpbmcgc3RhdHVzIHRvIGJlIGV4ZWN1dGVkJyk7XHJcbiAgICB9KTtcclxuICB9KTtcclxuXHJcbiAgZGVzY3JpYmUoJ2ZhaWx1cmUgaGFuZGxpbmcnLCAoKSA9PiB7XHJcbiAgICBsZXQgYWN0aW9uOiBSZXNwb25zZUFjdGlvbjtcclxuXHJcbiAgICBiZWZvcmVFYWNoKCgpID0+IHtcclxuICAgICAgYWN0aW9uID0gUmVzcG9uc2VBY3Rpb24uY3JlYXRlKHsgLi4udmFsaWRQcm9wcywgc3RhdHVzOiBBY3Rpb25TdGF0dXMuRVhFQ1VUSU5HIH0pO1xyXG4gICAgICBhY3Rpb24uY2xlYXJFdmVudHMoKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgaGFuZGxlIGZhaWx1cmUnLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGVycm9yID0gJ05ldHdvcmsgdGltZW91dCc7XHJcbiAgICAgIFxyXG4gICAgICBhY3Rpb24uZmFpbChlcnJvciwgJ3N5c3RlbScpO1xyXG5cclxuICAgICAgZXhwZWN0KGFjdGlvbi5zdGF0dXMpLnRvQmUoQWN0aW9uU3RhdHVzLkZBSUxFRCk7XHJcbiAgICAgIGV4cGVjdChhY3Rpb24uZXhlY3V0aW9uRXJyb3IpLnRvQmUoZXJyb3IpO1xyXG4gICAgICBleHBlY3QoYWN0aW9uLnN1Y2Nlc3NDcml0ZXJpYU1ldCkudG9CZShmYWxzZSk7XHJcblxyXG4gICAgICBjb25zdCBldmVudHMgPSBhY3Rpb24uZ2V0VW5wdWJsaXNoZWRFdmVudHMoKTtcclxuICAgICAgZXhwZWN0KGV2ZW50cykudG9IYXZlTGVuZ3RoKDEpO1xyXG4gICAgICBleHBlY3QoZXZlbnRzWzBdKS50b0JlSW5zdGFuY2VPZihSZXNwb25zZUFjdGlvbkZhaWxlZERvbWFpbkV2ZW50KTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgdGhyb3cgZXJyb3IgaWYgbm90IGluIGV4ZWN1dGluZyBvciByZXRyeWluZyBzdGF0dXMnLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IHBlbmRpbmdBY3Rpb24gPSBSZXNwb25zZUFjdGlvbi5jcmVhdGUodmFsaWRQcm9wcyk7XHJcbiAgICAgIFxyXG4gICAgICBleHBlY3QoKCkgPT4gcGVuZGluZ0FjdGlvbi5mYWlsKCdlcnJvcicpKS50b1Rocm93KCdBY3Rpb24gbXVzdCBiZSBpbiBleGVjdXRpbmcgb3IgcmV0cnlpbmcgc3RhdHVzIHRvIGZhaWwnKTtcclxuICAgIH0pO1xyXG4gIH0pO1xyXG5cclxuICBkZXNjcmliZSgncmV0cnkgbG9naWMnLCAoKSA9PiB7XHJcbiAgICBsZXQgYWN0aW9uOiBSZXNwb25zZUFjdGlvbjtcclxuXHJcbiAgICBiZWZvcmVFYWNoKCgpID0+IHtcclxuICAgICAgYWN0aW9uID0gUmVzcG9uc2VBY3Rpb24uY3JlYXRlKHsgLi4udmFsaWRQcm9wcywgc3RhdHVzOiBBY3Rpb25TdGF0dXMuRkFJTEVELCBtYXhSZXRyaWVzOiAzLCBleGVjdXRlZEF0OiBuZXcgRGF0ZSgpIH0pO1xyXG4gICAgICBhY3Rpb24uY2xlYXJFdmVudHMoKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgcmV0cnkgc3VjY2Vzc2Z1bGx5JywgKCkgPT4ge1xyXG4gICAgICAvLyBTZXQgZXhlY3V0ZWRBdCBmb3IgZmFpbGVkIGFjdGlvblxyXG4gICAgICBjb25zdCBmYWlsZWRBY3Rpb24gPSBSZXNwb25zZUFjdGlvbi5jcmVhdGUoeyBcclxuICAgICAgICAuLi52YWxpZFByb3BzLCBcclxuICAgICAgICBzdGF0dXM6IEFjdGlvblN0YXR1cy5GQUlMRUQsIFxyXG4gICAgICAgIG1heFJldHJpZXM6IDMsXHJcbiAgICAgICAgZXhlY3V0ZWRBdDogbmV3IERhdGUoKVxyXG4gICAgICB9KTtcclxuICAgICAgZmFpbGVkQWN0aW9uLmNsZWFyRXZlbnRzKCk7XHJcbiAgICAgIFxyXG4gICAgICBmYWlsZWRBY3Rpb24ucmV0cnkoJ2FkbWluJyk7XHJcblxyXG4gICAgICBleHBlY3QoZmFpbGVkQWN0aW9uLnN0YXR1cykudG9CZShBY3Rpb25TdGF0dXMuUkVUUllJTkcpO1xyXG4gICAgICBleHBlY3QoZmFpbGVkQWN0aW9uLnJldHJ5Q291bnQpLnRvQmUoMSk7XHJcbiAgICAgIGV4cGVjdChmYWlsZWRBY3Rpb24uZXhlY3V0aW9uRXJyb3IpLnRvQmVVbmRlZmluZWQoKTtcclxuICAgICAgZXhwZWN0KGZhaWxlZEFjdGlvbi5uZXh0UmV0cnlBdCkudG9CZURlZmluZWQoKTtcclxuXHJcbiAgICAgIGNvbnN0IGV2ZW50cyA9IGZhaWxlZEFjdGlvbi5nZXRVbnB1Ymxpc2hlZEV2ZW50cygpO1xyXG4gICAgICBleHBlY3QoZXZlbnRzKS50b0hhdmVMZW5ndGgoMSk7XHJcbiAgICAgIGV4cGVjdChldmVudHNbMF0pLnRvQmVJbnN0YW5jZU9mKFJlc3BvbnNlQWN0aW9uU3RhdHVzQ2hhbmdlZERvbWFpbkV2ZW50KTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgbm90IHJldHJ5IGlmIG1heCByZXRyaWVzIGV4Y2VlZGVkJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBtYXhSZXRyaWVzQWN0aW9uID0gUmVzcG9uc2VBY3Rpb24uY3JlYXRlKHsgXHJcbiAgICAgICAgLi4udmFsaWRQcm9wcywgXHJcbiAgICAgICAgc3RhdHVzOiBBY3Rpb25TdGF0dXMuRkFJTEVELCBcclxuICAgICAgICByZXRyeUNvdW50OiAzLCBcclxuICAgICAgICBtYXhSZXRyaWVzOiAzLFxyXG4gICAgICAgIGV4ZWN1dGVkQXQ6IG5ldyBEYXRlKClcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBleHBlY3QoKCkgPT4gbWF4UmV0cmllc0FjdGlvbi5yZXRyeSgnYWRtaW4nKSkudG9UaHJvdygnQWN0aW9uIGNhbm5vdCBiZSByZXRyaWVkJyk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIGNoZWNrIGNhblJldHJ5IGNvcnJlY3RseScsICgpID0+IHtcclxuICAgICAgY29uc3QgZmFpbGVkQWN0aW9uID0gUmVzcG9uc2VBY3Rpb24uY3JlYXRlKHsgXHJcbiAgICAgICAgLi4udmFsaWRQcm9wcywgXHJcbiAgICAgICAgc3RhdHVzOiBBY3Rpb25TdGF0dXMuRkFJTEVELCBcclxuICAgICAgICBtYXhSZXRyaWVzOiAzLFxyXG4gICAgICAgIGV4ZWN1dGVkQXQ6IG5ldyBEYXRlKClcclxuICAgICAgfSk7XHJcbiAgICAgIGV4cGVjdChmYWlsZWRBY3Rpb24uY2FuUmV0cnkoKSkudG9CZSh0cnVlKTtcclxuXHJcbiAgICAgIGNvbnN0IG1heFJldHJpZXNBY3Rpb24gPSBSZXNwb25zZUFjdGlvbi5jcmVhdGUoeyBcclxuICAgICAgICAuLi52YWxpZFByb3BzLCBcclxuICAgICAgICBzdGF0dXM6IEFjdGlvblN0YXR1cy5GQUlMRUQsIFxyXG4gICAgICAgIHJldHJ5Q291bnQ6IDMsIFxyXG4gICAgICAgIG1heFJldHJpZXM6IDMsXHJcbiAgICAgICAgZXhlY3V0ZWRBdDogbmV3IERhdGUoKVxyXG4gICAgICB9KTtcclxuICAgICAgZXhwZWN0KG1heFJldHJpZXNBY3Rpb24uY2FuUmV0cnkoKSkudG9CZShmYWxzZSk7XHJcbiAgICB9KTtcclxuICB9KTtcclxuXHJcbiAgZGVzY3JpYmUoJ3JvbGxiYWNrIGZ1bmN0aW9uYWxpdHknLCAoKSA9PiB7XHJcbiAgICBsZXQgYWN0aW9uOiBSZXNwb25zZUFjdGlvbjtcclxuXHJcbiAgICBiZWZvcmVFYWNoKCgpID0+IHtcclxuICAgICAgYWN0aW9uID0gUmVzcG9uc2VBY3Rpb24uY3JlYXRlKHsgXHJcbiAgICAgICAgLi4udmFsaWRQcm9wcywgXHJcbiAgICAgICAgc3RhdHVzOiBBY3Rpb25TdGF0dXMuQ09NUExFVEVELFxyXG4gICAgICAgIGlzUmV2ZXJzaWJsZTogdHJ1ZSxcclxuICAgICAgICByb2xsYmFja0luZm86IHtcclxuICAgICAgICAgIGNhblJvbGxiYWNrOiB0cnVlLFxyXG4gICAgICAgICAgcm9sbGJhY2tTdGVwczogWydSZW1vdmUgSVAgZnJvbSBibG9jayBsaXN0J10sXHJcbiAgICAgICAgfSxcclxuICAgICAgICBleGVjdXRlZEF0OiBuZXcgRGF0ZSgpXHJcbiAgICAgIH0pO1xyXG4gICAgICBhY3Rpb24uY2xlYXJFdmVudHMoKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgcm9sbGJhY2sgc3VjY2Vzc2Z1bGx5JywgKCkgPT4ge1xyXG4gICAgICAvLyBDcmVhdGUgYSBjb21wbGV0ZWQgYWN0aW9uIHdpdGggZXhlY3V0ZWRBdFxyXG4gICAgICBjb25zdCBjb21wbGV0ZWRBY3Rpb24gPSBSZXNwb25zZUFjdGlvbi5jcmVhdGUoeyBcclxuICAgICAgICAuLi52YWxpZFByb3BzLCBcclxuICAgICAgICBzdGF0dXM6IEFjdGlvblN0YXR1cy5DT01QTEVURUQsXHJcbiAgICAgICAgaXNSZXZlcnNpYmxlOiB0cnVlLFxyXG4gICAgICAgIHJvbGxiYWNrSW5mbzoge1xyXG4gICAgICAgICAgY2FuUm9sbGJhY2s6IHRydWUsXHJcbiAgICAgICAgICByb2xsYmFja1N0ZXBzOiBbJ1JlbW92ZSBJUCBmcm9tIGJsb2NrIGxpc3QnXSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIGV4ZWN1dGVkQXQ6IG5ldyBEYXRlKClcclxuICAgICAgfSk7XHJcbiAgICAgIGNvbXBsZXRlZEFjdGlvbi5jbGVhckV2ZW50cygpO1xyXG4gICAgICBcclxuICAgICAgY29uc3Qgcm9sbGJhY2tSZXN1bHRzID0geyB1bmJsb2NrZWQ6IHRydWUgfTtcclxuICAgICAgXHJcbiAgICAgIGNvbXBsZXRlZEFjdGlvbi5yb2xsYmFjaygnYWRtaW4nLCByb2xsYmFja1Jlc3VsdHMpO1xyXG5cclxuICAgICAgZXhwZWN0KGNvbXBsZXRlZEFjdGlvbi5yb2xsZWRCYWNrKS50b0JlKHRydWUpO1xyXG4gICAgICBleHBlY3QoY29tcGxldGVkQWN0aW9uLnJvbGxiYWNrRGV0YWlscykudG9CZURlZmluZWQoKTtcclxuICAgICAgZXhwZWN0KGNvbXBsZXRlZEFjdGlvbi5yb2xsYmFja0RldGFpbHM/LnJvbGxlZEJhY2tCeSkudG9CZSgnYWRtaW4nKTtcclxuICAgICAgZXhwZWN0KGNvbXBsZXRlZEFjdGlvbi5yb2xsYmFja0RldGFpbHM/LnJvbGxiYWNrUmVzdWx0cykudG9FcXVhbChyb2xsYmFja1Jlc3VsdHMpO1xyXG5cclxuICAgICAgY29uc3QgZXZlbnRzID0gY29tcGxldGVkQWN0aW9uLmdldFVucHVibGlzaGVkRXZlbnRzKCk7XHJcbiAgICAgIGV4cGVjdChldmVudHMpLnRvSGF2ZUxlbmd0aCgxKTtcclxuICAgICAgZXhwZWN0KGV2ZW50c1swXSkudG9CZUluc3RhbmNlT2YoUmVzcG9uc2VBY3Rpb25Sb2xsZWRCYWNrRG9tYWluRXZlbnQpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBub3Qgcm9sbGJhY2sgaWYgbm90IHJldmVyc2libGUnLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IG5vblJldmVyc2libGVBY3Rpb24gPSBSZXNwb25zZUFjdGlvbi5jcmVhdGUoeyBcclxuICAgICAgICAuLi52YWxpZFByb3BzLCBcclxuICAgICAgICBzdGF0dXM6IEFjdGlvblN0YXR1cy5DT01QTEVURUQsXHJcbiAgICAgICAgaXNSZXZlcnNpYmxlOiBmYWxzZSxcclxuICAgICAgICBleGVjdXRlZEF0OiBuZXcgRGF0ZSgpXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgZXhwZWN0KCgpID0+IG5vblJldmVyc2libGVBY3Rpb24ucm9sbGJhY2soJ2FkbWluJykpLnRvVGhyb3coJ0FjdGlvbiBjYW5ub3QgYmUgcm9sbGVkIGJhY2snKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgY2hlY2sgY2FuUm9sbGJhY2sgY29ycmVjdGx5JywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBjb21wbGV0ZWRBY3Rpb24gPSBSZXNwb25zZUFjdGlvbi5jcmVhdGUoeyBcclxuICAgICAgICAuLi52YWxpZFByb3BzLCBcclxuICAgICAgICBzdGF0dXM6IEFjdGlvblN0YXR1cy5DT01QTEVURUQsXHJcbiAgICAgICAgaXNSZXZlcnNpYmxlOiB0cnVlLFxyXG4gICAgICAgIHJvbGxiYWNrSW5mbzoge1xyXG4gICAgICAgICAgY2FuUm9sbGJhY2s6IHRydWUsXHJcbiAgICAgICAgICByb2xsYmFja1N0ZXBzOiBbJ1JlbW92ZSBJUCBmcm9tIGJsb2NrIGxpc3QnXSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIGV4ZWN1dGVkQXQ6IG5ldyBEYXRlKClcclxuICAgICAgfSk7XHJcbiAgICAgIGV4cGVjdChjb21wbGV0ZWRBY3Rpb24uY2FuUm9sbGJhY2soKSkudG9CZSh0cnVlKTtcclxuXHJcbiAgICAgIGNvbnN0IG5vblJldmVyc2libGVBY3Rpb24gPSBSZXNwb25zZUFjdGlvbi5jcmVhdGUoeyBcclxuICAgICAgICAuLi52YWxpZFByb3BzLCBcclxuICAgICAgICBzdGF0dXM6IEFjdGlvblN0YXR1cy5DT01QTEVURUQsXHJcbiAgICAgICAgaXNSZXZlcnNpYmxlOiBmYWxzZSxcclxuICAgICAgICBleGVjdXRlZEF0OiBuZXcgRGF0ZSgpXHJcbiAgICAgIH0pO1xyXG4gICAgICBleHBlY3Qobm9uUmV2ZXJzaWJsZUFjdGlvbi5jYW5Sb2xsYmFjaygpKS50b0JlKGZhbHNlKTtcclxuICAgIH0pO1xyXG4gIH0pO1xyXG5cclxuICBkZXNjcmliZSgnY2hpbGQgYWN0aW9uIG1hbmFnZW1lbnQnLCAoKSA9PiB7XHJcbiAgICBsZXQgYWN0aW9uOiBSZXNwb25zZUFjdGlvbjtcclxuXHJcbiAgICBiZWZvcmVFYWNoKCgpID0+IHtcclxuICAgICAgYWN0aW9uID0gUmVzcG9uc2VBY3Rpb24uY3JlYXRlKHZhbGlkUHJvcHMpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBhZGQgY2hpbGQgYWN0aW9uJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBjaGlsZElkID0gVW5pcXVlRW50aXR5SWQuZ2VuZXJhdGUoKTtcclxuICAgICAgXHJcbiAgICAgIGFjdGlvbi5hZGRDaGlsZEFjdGlvbihjaGlsZElkKTtcclxuXHJcbiAgICAgIGV4cGVjdChhY3Rpb24uY2hpbGRBY3Rpb25JZHMpLnRvQ29udGFpbihjaGlsZElkKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgbm90IGFkZCBkdXBsaWNhdGUgY2hpbGQgYWN0aW9uJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBjaGlsZElkID0gVW5pcXVlRW50aXR5SWQuZ2VuZXJhdGUoKTtcclxuICAgICAgXHJcbiAgICAgIGFjdGlvbi5hZGRDaGlsZEFjdGlvbihjaGlsZElkKTtcclxuICAgICAgYWN0aW9uLmFkZENoaWxkQWN0aW9uKGNoaWxkSWQpO1xyXG5cclxuICAgICAgZXhwZWN0KGFjdGlvbi5jaGlsZEFjdGlvbklkcy5maWx0ZXIoaWQgPT4gaWQuZXF1YWxzKGNoaWxkSWQpKSkudG9IYXZlTGVuZ3RoKDEpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCByZW1vdmUgY2hpbGQgYWN0aW9uJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBjaGlsZElkID0gVW5pcXVlRW50aXR5SWQuZ2VuZXJhdGUoKTtcclxuICAgICAgXHJcbiAgICAgIGFjdGlvbi5hZGRDaGlsZEFjdGlvbihjaGlsZElkKTtcclxuICAgICAgYWN0aW9uLnJlbW92ZUNoaWxkQWN0aW9uKGNoaWxkSWQpO1xyXG5cclxuICAgICAgZXhwZWN0KGFjdGlvbi5jaGlsZEFjdGlvbklkcykubm90LnRvQ29udGFpbihjaGlsZElkKTtcclxuICAgIH0pO1xyXG4gIH0pO1xyXG5cclxuICBkZXNjcmliZSgndGFnIG1hbmFnZW1lbnQnLCAoKSA9PiB7XHJcbiAgICBsZXQgYWN0aW9uOiBSZXNwb25zZUFjdGlvbjtcclxuXHJcbiAgICBiZWZvcmVFYWNoKCgpID0+IHtcclxuICAgICAgYWN0aW9uID0gUmVzcG9uc2VBY3Rpb24uY3JlYXRlKHZhbGlkUHJvcHMpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBhZGQgdGFncycsICgpID0+IHtcclxuICAgICAgYWN0aW9uLmFkZFRhZ3MoWyd1cmdlbnQnLCAnY3JpdGljYWwnXSk7XHJcblxyXG4gICAgICBleHBlY3QoYWN0aW9uLnRhZ3MpLnRvQ29udGFpbigndXJnZW50Jyk7XHJcbiAgICAgIGV4cGVjdChhY3Rpb24udGFncykudG9Db250YWluKCdjcml0aWNhbCcpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBub3QgYWRkIGR1cGxpY2F0ZSB0YWdzJywgKCkgPT4ge1xyXG4gICAgICBhY3Rpb24uYWRkVGFncyhbJ3NlY3VyaXR5JywgJ3VyZ2VudCddKTtcclxuXHJcbiAgICAgIGNvbnN0IHNlY3VyaXR5VGFncyA9IGFjdGlvbi50YWdzLmZpbHRlcih0YWcgPT4gdGFnID09PSAnc2VjdXJpdHknKTtcclxuICAgICAgZXhwZWN0KHNlY3VyaXR5VGFncykudG9IYXZlTGVuZ3RoKDEpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCB0aHJvdyBlcnJvciBpZiB0b28gbWFueSB0YWdzJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBtYW55VGFncyA9IEFycmF5KDE5KS5maWxsKCd0YWcnKS5tYXAoKF8sIGkpID0+IGB0YWcke2l9YCk7XHJcbiAgICAgIFxyXG4gICAgICBleHBlY3QoKCkgPT4gYWN0aW9uLmFkZFRhZ3MobWFueVRhZ3MpKS50b1Rocm93KCdSZXNwb25zZUFjdGlvbiBjYW5ub3QgaGF2ZSBtb3JlIHRoYW4gMjAgdGFncycpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCByZW1vdmUgdGFncycsICgpID0+IHtcclxuICAgICAgYWN0aW9uLnJlbW92ZVRhZ3MoWydzZWN1cml0eSddKTtcclxuXHJcbiAgICAgIGV4cGVjdChhY3Rpb24udGFncykubm90LnRvQ29udGFpbignc2VjdXJpdHknKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgY2hlY2sgdGFnIHByZXNlbmNlJywgKCkgPT4ge1xyXG4gICAgICBleHBlY3QoYWN0aW9uLmhhc1RhZygnc2VjdXJpdHknKSkudG9CZSh0cnVlKTtcclxuICAgICAgZXhwZWN0KGFjdGlvbi5oYXNUYWcoJ25vbmV4aXN0ZW50JykpLnRvQmUoZmFsc2UpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBjaGVjayBhbnkgdGFnIHByZXNlbmNlJywgKCkgPT4ge1xyXG4gICAgICBleHBlY3QoYWN0aW9uLmhhc0FueVRhZyhbJ3NlY3VyaXR5JywgJ25vbmV4aXN0ZW50J10pKS50b0JlKHRydWUpO1xyXG4gICAgICBleHBlY3QoYWN0aW9uLmhhc0FueVRhZyhbJ25vbmV4aXN0ZW50JywgJ2Fub3RoZXInXSkpLnRvQmUoZmFsc2UpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBjaGVjayBhbGwgdGFncyBwcmVzZW5jZScsICgpID0+IHtcclxuICAgICAgZXhwZWN0KGFjdGlvbi5oYXNBbGxUYWdzKFsnc2VjdXJpdHknLCAnY29udGFpbm1lbnQnXSkpLnRvQmUodHJ1ZSk7XHJcbiAgICAgIGV4cGVjdChhY3Rpb24uaGFzQWxsVGFncyhbJ3NlY3VyaXR5JywgJ25vbmV4aXN0ZW50J10pKS50b0JlKGZhbHNlKTtcclxuICAgIH0pO1xyXG4gIH0pO1xyXG5cclxuICBkZXNjcmliZSgnbWV0YWRhdGEgbWFuYWdlbWVudCcsICgpID0+IHtcclxuICAgIGxldCBhY3Rpb246IFJlc3BvbnNlQWN0aW9uO1xyXG5cclxuICAgIGJlZm9yZUVhY2goKCkgPT4ge1xyXG4gICAgICBhY3Rpb24gPSBSZXNwb25zZUFjdGlvbi5jcmVhdGUodmFsaWRQcm9wcyk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIHVwZGF0ZSBtZXRhZGF0YScsICgpID0+IHtcclxuICAgICAgYWN0aW9uLnVwZGF0ZU1ldGFkYXRhKHsgbmV3S2V5OiAnbmV3VmFsdWUnIH0pO1xyXG5cclxuICAgICAgZXhwZWN0KGFjdGlvbi5tZXRhZGF0YS5uZXdLZXkpLnRvQmUoJ25ld1ZhbHVlJyk7XHJcbiAgICAgIGV4cGVjdChhY3Rpb24ubWV0YWRhdGEuc291cmNlKS50b0JlKCd0aHJlYXQtZGV0ZWN0aW9uJyk7IC8vIE9yaWdpbmFsIHNob3VsZCByZW1haW5cclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgcmVtb3ZlIG1ldGFkYXRhIGtleScsICgpID0+IHtcclxuICAgICAgYWN0aW9uLnJlbW92ZU1ldGFkYXRhKCdzb3VyY2UnKTtcclxuXHJcbiAgICAgIGV4cGVjdChhY3Rpb24ubWV0YWRhdGEuc291cmNlKS50b0JlVW5kZWZpbmVkKCk7XHJcbiAgICB9KTtcclxuICB9KTtcclxuXHJcbiAgZGVzY3JpYmUoJ3F1ZXJ5IG1ldGhvZHMnLCAoKSA9PiB7XHJcbiAgICBpdCgnc2hvdWxkIGNoZWNrIGlmIGFjdGlvbiBpcyB0ZXJtaW5hbCcsICgpID0+IHtcclxuICAgICAgY29uc3QgY29tcGxldGVkQWN0aW9uID0gUmVzcG9uc2VBY3Rpb24uY3JlYXRlKHsgLi4udmFsaWRQcm9wcywgc3RhdHVzOiBBY3Rpb25TdGF0dXMuQ09NUExFVEVELCBleGVjdXRlZEF0OiBuZXcgRGF0ZSgpIH0pO1xyXG4gICAgICBjb25zdCBwZW5kaW5nQWN0aW9uID0gUmVzcG9uc2VBY3Rpb24uY3JlYXRlKHsgLi4udmFsaWRQcm9wcywgc3RhdHVzOiBBY3Rpb25TdGF0dXMuUEVORElORyB9KTtcclxuXHJcbiAgICAgIGV4cGVjdChjb21wbGV0ZWRBY3Rpb24uaXNUZXJtaW5hbCgpKS50b0JlKHRydWUpO1xyXG4gICAgICBleHBlY3QocGVuZGluZ0FjdGlvbi5pc1Rlcm1pbmFsKCkpLnRvQmUoZmFsc2UpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBjaGVjayBpZiBhY3Rpb24gaXMgYWN0aXZlJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBjb21wbGV0ZWRBY3Rpb24gPSBSZXNwb25zZUFjdGlvbi5jcmVhdGUoeyAuLi52YWxpZFByb3BzLCBzdGF0dXM6IEFjdGlvblN0YXR1cy5DT01QTEVURUQsIGV4ZWN1dGVkQXQ6IG5ldyBEYXRlKCkgfSk7XHJcbiAgICAgIGNvbnN0IHBlbmRpbmdBY3Rpb24gPSBSZXNwb25zZUFjdGlvbi5jcmVhdGUoeyAuLi52YWxpZFByb3BzLCBzdGF0dXM6IEFjdGlvblN0YXR1cy5QRU5ESU5HIH0pO1xyXG5cclxuICAgICAgZXhwZWN0KGNvbXBsZXRlZEFjdGlvbi5pc0FjdGl2ZSgpKS50b0JlKGZhbHNlKTtcclxuICAgICAgZXhwZWN0KHBlbmRpbmdBY3Rpb24uaXNBY3RpdmUoKSkudG9CZSh0cnVlKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgY2hlY2sgaWYgYWN0aW9uIGlzIHN1Y2Nlc3NmdWwnLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGNvbXBsZXRlZEFjdGlvbiA9IFJlc3BvbnNlQWN0aW9uLmNyZWF0ZSh7IC4uLnZhbGlkUHJvcHMsIHN0YXR1czogQWN0aW9uU3RhdHVzLkNPTVBMRVRFRCwgZXhlY3V0ZWRBdDogbmV3IERhdGUoKSB9KTtcclxuICAgICAgY29uc3QgZmFpbGVkQWN0aW9uID0gUmVzcG9uc2VBY3Rpb24uY3JlYXRlKHsgLi4udmFsaWRQcm9wcywgc3RhdHVzOiBBY3Rpb25TdGF0dXMuRkFJTEVELCBleGVjdXRlZEF0OiBuZXcgRGF0ZSgpIH0pO1xyXG5cclxuICAgICAgZXhwZWN0KGNvbXBsZXRlZEFjdGlvbi5pc1N1Y2Nlc3NmdWwoKSkudG9CZSh0cnVlKTtcclxuICAgICAgZXhwZWN0KGZhaWxlZEFjdGlvbi5pc1N1Y2Nlc3NmdWwoKSkudG9CZShmYWxzZSk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIGNoZWNrIGlmIGFjdGlvbiBoYXMgZmFpbGVkJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBmYWlsZWRBY3Rpb24gPSBSZXNwb25zZUFjdGlvbi5jcmVhdGUoeyAuLi52YWxpZFByb3BzLCBzdGF0dXM6IEFjdGlvblN0YXR1cy5GQUlMRUQsIGV4ZWN1dGVkQXQ6IG5ldyBEYXRlKCkgfSk7XHJcbiAgICAgIGNvbnN0IGNvbXBsZXRlZEFjdGlvbiA9IFJlc3BvbnNlQWN0aW9uLmNyZWF0ZSh7IC4uLnZhbGlkUHJvcHMsIHN0YXR1czogQWN0aW9uU3RhdHVzLkNPTVBMRVRFRCwgZXhlY3V0ZWRBdDogbmV3IERhdGUoKSB9KTtcclxuXHJcbiAgICAgIGV4cGVjdChmYWlsZWRBY3Rpb24uaGFzRmFpbGVkKCkpLnRvQmUodHJ1ZSk7XHJcbiAgICAgIGV4cGVjdChjb21wbGV0ZWRBY3Rpb24uaGFzRmFpbGVkKCkpLnRvQmUoZmFsc2UpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBjaGVjayBpZiBhY3Rpb24gcmVxdWlyZXMgYXBwcm92YWwnLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGFwcHJvdmFsQWN0aW9uID0gUmVzcG9uc2VBY3Rpb24uY3JlYXRlKHsgXHJcbiAgICAgICAgLi4udmFsaWRQcm9wcywgXHJcbiAgICAgICAgc3RhdHVzOiBBY3Rpb25TdGF0dXMuUEVORElORywgXHJcbiAgICAgICAgYXBwcm92YWxSZXF1aXJlZDogdHJ1ZSBcclxuICAgICAgfSk7XHJcbiAgICAgIGNvbnN0IG5vQXBwcm92YWxBY3Rpb24gPSBSZXNwb25zZUFjdGlvbi5jcmVhdGUoeyBcclxuICAgICAgICAuLi52YWxpZFByb3BzLCBcclxuICAgICAgICBzdGF0dXM6IEFjdGlvblN0YXR1cy5QRU5ESU5HLCBcclxuICAgICAgICBhcHByb3ZhbFJlcXVpcmVkOiBmYWxzZSBcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBleHBlY3QoYXBwcm92YWxBY3Rpb24ucmVxdWlyZXNBcHByb3ZhbCgpKS50b0JlKHRydWUpO1xyXG4gICAgICBleHBlY3Qobm9BcHByb3ZhbEFjdGlvbi5yZXF1aXJlc0FwcHJvdmFsKCkpLnRvQmUoZmFsc2UpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBjaGVjayBpZiBhY3Rpb24gaXMgaGlnaCBwcmlvcml0eScsICgpID0+IHtcclxuICAgICAgY29uc3QgaGlnaFByaW9yaXR5QWN0aW9uID0gUmVzcG9uc2VBY3Rpb24uY3JlYXRlKHsgLi4udmFsaWRQcm9wcywgcHJpb3JpdHk6ICdoaWdoJyB9KTtcclxuICAgICAgY29uc3Qgbm9ybWFsUHJpb3JpdHlBY3Rpb24gPSBSZXNwb25zZUFjdGlvbi5jcmVhdGUoeyAuLi52YWxpZFByb3BzLCBwcmlvcml0eTogJ25vcm1hbCcgfSk7XHJcblxyXG4gICAgICBleHBlY3QoaGlnaFByaW9yaXR5QWN0aW9uLmlzSGlnaFByaW9yaXR5KCkpLnRvQmUodHJ1ZSk7XHJcbiAgICAgIGV4cGVjdChub3JtYWxQcmlvcml0eUFjdGlvbi5pc0hpZ2hQcmlvcml0eSgpKS50b0JlKGZhbHNlKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgY2hlY2sgaWYgYWN0aW9uIGlzIG92ZXJkdWUnLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IG92ZXJkdWVBY3Rpb24gPSBSZXNwb25zZUFjdGlvbi5jcmVhdGUoeyBcclxuICAgICAgICAuLi52YWxpZFByb3BzLCBcclxuICAgICAgICBzdGF0dXM6IEFjdGlvblN0YXR1cy5QRU5ESU5HLFxyXG4gICAgICAgIHNjaGVkdWxlZEF0OiBuZXcgRGF0ZShEYXRlLm5vdygpIC0gMzYwMDAwMCkgLy8gMSBob3VyIGFnb1xyXG4gICAgICB9KTtcclxuICAgICAgY29uc3Qgbm90T3ZlcmR1ZUFjdGlvbiA9IFJlc3BvbnNlQWN0aW9uLmNyZWF0ZSh7IFxyXG4gICAgICAgIC4uLnZhbGlkUHJvcHMsIFxyXG4gICAgICAgIHN0YXR1czogQWN0aW9uU3RhdHVzLlBFTkRJTkcsXHJcbiAgICAgICAgc2NoZWR1bGVkQXQ6IG5ldyBEYXRlKERhdGUubm93KCkgKyAzNjAwMDAwKSAvLyAxIGhvdXIgZnJvbSBub3dcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBleHBlY3Qob3ZlcmR1ZUFjdGlvbi5pc092ZXJkdWUoKSkudG9CZSh0cnVlKTtcclxuICAgICAgZXhwZWN0KG5vdE92ZXJkdWVBY3Rpb24uaXNPdmVyZHVlKCkpLnRvQmUoZmFsc2UpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBnZXQgYWN0aW9uIGFnZScsICgpID0+IHtcclxuICAgICAgY29uc3QgYWN0aW9uID0gUmVzcG9uc2VBY3Rpb24uY3JlYXRlKHZhbGlkUHJvcHMpO1xyXG4gICAgICBjb25zdCBhZ2UgPSBhY3Rpb24uZ2V0QWdlKCk7XHJcblxyXG4gICAgICBleHBlY3QoYWdlKS50b0JlR3JlYXRlclRoYW5PckVxdWFsKDApO1xyXG4gICAgICBleHBlY3QoYWdlKS50b0JlTGVzc1RoYW4oMTAwMCk7IC8vIFNob3VsZCBiZSB2ZXJ5IHJlY2VudFxyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBnZXQgZXN0aW1hdGVkIGNvbXBsZXRpb24gdGltZScsICgpID0+IHtcclxuICAgICAgY29uc3QgYWN0aW9uID0gUmVzcG9uc2VBY3Rpb24uY3JlYXRlKHsgXHJcbiAgICAgICAgLi4udmFsaWRQcm9wcywgXHJcbiAgICAgICAgZXN0aW1hdGVkRHVyYXRpb25NaW51dGVzOiAzMCxcclxuICAgICAgICBzY2hlZHVsZWRBdDogbmV3IERhdGUoKVxyXG4gICAgICB9KTtcclxuICAgICAgY29uc3QgY29tcGxldGlvblRpbWUgPSBhY3Rpb24uZ2V0RXN0aW1hdGVkQ29tcGxldGlvblRpbWUoKTtcclxuXHJcbiAgICAgIGV4cGVjdChjb21wbGV0aW9uVGltZSkudG9CZURlZmluZWQoKTtcclxuICAgICAgZXhwZWN0KGNvbXBsZXRpb25UaW1lIS5nZXRUaW1lKCkpLnRvQmVHcmVhdGVyVGhhbihEYXRlLm5vdygpKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgcmV0dXJuIG51bGwgY29tcGxldGlvbiB0aW1lIGZvciB0ZXJtaW5hbCBhY3Rpb25zJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBjb21wbGV0ZWRBY3Rpb24gPSBSZXNwb25zZUFjdGlvbi5jcmVhdGUoeyAuLi52YWxpZFByb3BzLCBzdGF0dXM6IEFjdGlvblN0YXR1cy5DT01QTEVURUQsIGV4ZWN1dGVkQXQ6IG5ldyBEYXRlKCkgfSk7XHJcbiAgICAgIGNvbnN0IGNvbXBsZXRpb25UaW1lID0gY29tcGxldGVkQWN0aW9uLmdldEVzdGltYXRlZENvbXBsZXRpb25UaW1lKCk7XHJcblxyXG4gICAgICBleHBlY3QoY29tcGxldGlvblRpbWUpLnRvQmVOdWxsKCk7XHJcbiAgICB9KTtcclxuICB9KTtcclxuXHJcbiAgZGVzY3JpYmUoJ0pTT04gc2VyaWFsaXphdGlvbicsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgc2VyaWFsaXplIHRvIEpTT04gY29ycmVjdGx5JywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBhY3Rpb24gPSBSZXNwb25zZUFjdGlvbi5jcmVhdGUodmFsaWRQcm9wcyk7XHJcbiAgICAgIGNvbnN0IGpzb24gPSBhY3Rpb24udG9KU09OKCk7XHJcblxyXG4gICAgICBleHBlY3QoanNvbi5pZCkudG9CZURlZmluZWQoKTtcclxuICAgICAgZXhwZWN0KGpzb24uYWN0aW9uVHlwZSkudG9CZShBY3Rpb25UeXBlLkJMT0NLX0lQKTtcclxuICAgICAgZXhwZWN0KGpzb24uc3RhdHVzKS50b0JlKEFjdGlvblN0YXR1cy5QRU5ESU5HKTtcclxuICAgICAgZXhwZWN0KGpzb24udGl0bGUpLnRvQmUoJ0Jsb2NrIG1hbGljaW91cyBJUCBhZGRyZXNzJyk7XHJcbiAgICAgIGV4cGVjdChqc29uLnByaW9yaXR5KS50b0JlKCdoaWdoJyk7XHJcbiAgICAgIGV4cGVjdChqc29uLmlzQXV0b21hdGVkKS50b0JlKHRydWUpO1xyXG4gICAgICBleHBlY3QoanNvbi5pc1JldmVyc2libGUpLnRvQmUodHJ1ZSk7XHJcbiAgICAgIGV4cGVjdChqc29uLnN1bW1hcnkpLnRvQmVEZWZpbmVkKCk7XHJcbiAgICAgIGV4cGVjdChqc29uLnN1bW1hcnkuaXNBY3RpdmUpLnRvQmUodHJ1ZSk7XHJcbiAgICB9KTtcclxuICB9KTtcclxuXHJcbiAgZGVzY3JpYmUoJ2NvcnJlbGF0aW9uIGFuZCByZWxhdGlvbnNoaXBzJywgKCkgPT4ge1xyXG4gICAgaXQoJ3Nob3VsZCBzZXQgY29ycmVsYXRpb24gSUQnLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGFjdGlvbiA9IFJlc3BvbnNlQWN0aW9uLmNyZWF0ZSh2YWxpZFByb3BzKTtcclxuICAgICAgY29uc3QgY29ycmVsYXRpb25JZCA9ICdjb3JyLTEyMyc7XHJcblxyXG4gICAgICBhY3Rpb24uc2V0Q29ycmVsYXRpb25JZChjb3JyZWxhdGlvbklkKTtcclxuXHJcbiAgICAgIGV4cGVjdChhY3Rpb24uY29ycmVsYXRpb25JZCkudG9CZShjb3JyZWxhdGlvbklkKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgaGFuZGxlIHJlbGF0ZWQgZW50aXRpZXMnLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGV2ZW50SWQgPSBVbmlxdWVFbnRpdHlJZC5nZW5lcmF0ZSgpO1xyXG4gICAgICBjb25zdCB0aHJlYXRJZCA9IFVuaXF1ZUVudGl0eUlkLmdlbmVyYXRlKCk7XHJcbiAgICAgIGNvbnN0IHZ1bG5lcmFiaWxpdHlJZCA9IFVuaXF1ZUVudGl0eUlkLmdlbmVyYXRlKCk7XHJcblxyXG4gICAgICBjb25zdCBhY3Rpb24gPSBSZXNwb25zZUFjdGlvbi5jcmVhdGUoe1xyXG4gICAgICAgIC4uLnZhbGlkUHJvcHMsXHJcbiAgICAgICAgcmVsYXRlZEV2ZW50SWQ6IGV2ZW50SWQsXHJcbiAgICAgICAgcmVsYXRlZFRocmVhdElkOiB0aHJlYXRJZCxcclxuICAgICAgICByZWxhdGVkVnVsbmVyYWJpbGl0eUlkOiB2dWxuZXJhYmlsaXR5SWQsXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgZXhwZWN0KGFjdGlvbi5yZWxhdGVkRXZlbnRJZD8uZXF1YWxzKGV2ZW50SWQpKS50b0JlKHRydWUpO1xyXG4gICAgICBleHBlY3QoYWN0aW9uLnJlbGF0ZWRUaHJlYXRJZD8uZXF1YWxzKHRocmVhdElkKSkudG9CZSh0cnVlKTtcclxuICAgICAgZXhwZWN0KGFjdGlvbi5yZWxhdGVkVnVsbmVyYWJpbGl0eUlkPy5lcXVhbHModnVsbmVyYWJpbGl0eUlkKSkudG9CZSh0cnVlKTtcclxuICAgIH0pO1xyXG4gIH0pO1xyXG59KTsiXSwidmVyc2lvbiI6M30=