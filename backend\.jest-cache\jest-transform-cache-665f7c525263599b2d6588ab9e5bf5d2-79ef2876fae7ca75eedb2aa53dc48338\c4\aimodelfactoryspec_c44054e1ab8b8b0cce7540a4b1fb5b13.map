{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\factories\\__tests__\\ai-model.factory.spec.ts", "mappings": ";;AAAA,4HAA0G;AAC1G,oEAAoF;AACpF,0DAA+F;AAE/F,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;IAC9B,IAAI,kBAAwC,CAAC;IAC7C,IAAI,uBAA2C,CAAC;IAEhD,UAAU,CAAC,GAAG,EAAE;QACd,kBAAkB,GAAG;YACnB,IAAI,EAAE,YAAY;YAClB,OAAO,EAAE,OAAO;YAChB,QAAQ,EAAE,4BAAU,CAAC,MAAM;YAC3B,SAAS,EAAE,2BAAS,CAAC,cAAc;YACnC,kBAAkB,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;YACzD,IAAI,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;YACrB,QAAQ,EAAE,GAAG;YACb,MAAM,EAAE,GAAG;YACX,qBAAqB,EAAE,GAAG;YAC1B,QAAQ,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE;SAC7B,CAAC;QAEF,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,uBAAuB,GAAG;YACxB,EAAE,EAAE,8CAAc,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;YACxC,IAAI,EAAE,YAAY;YAClB,OAAO,EAAE,OAAO;YAChB,QAAQ,EAAE,4BAAU,CAAC,MAAM;YAC3B,SAAS,EAAE,2BAAS,CAAC,cAAc;YACnC,aAAa,EAAE;gBACb,QAAQ,EAAE,yBAAyB;gBACnC,MAAM,EAAE,UAAU;gBAClB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,CAAC;gBACV,SAAS,EAAE,EAAE;gBACb,cAAc,EAAE,EAAE;aACnB;YACD,WAAW,EAAE;gBACX,aAAa,EAAE,GAAG;gBAClB,kBAAkB,EAAE,EAAE;gBACtB,cAAc,EAAE,CAAC;gBACjB,cAAc,EAAE,GAAG;gBACnB,UAAU,EAAE,GAAG;gBACf,UAAU,EAAE,GAAG;gBACf,QAAQ,EAAE,IAAI;gBACd,SAAS,EAAE,IAAI;gBACf,MAAM,EAAE,IAAI;gBACZ,OAAO,EAAE,IAAI;gBACb,UAAU,EAAE,EAAE;gBACd,WAAW,EAAE,GAAG;aACjB;YACD,MAAM,EAAE,6BAAW,CAAC,MAAM;YAC1B,YAAY,EAAE;gBACZ,cAAc,EAAE,IAAI;gBACpB,eAAe,EAAE,IAAI;gBACrB,aAAa,EAAE,IAAI;gBACnB,iBAAiB,EAAE,KAAK;gBACxB,kBAAkB,EAAE,IAAI;gBACxB,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;gBACvB,UAAU,EAAE,CAAC,MAAM,CAAC;aACrB;YACD,oBAAoB,EAAE;gBACpB,GAAG,EAAE,CAAC;gBACN,MAAM,EAAE,IAAI;gBACZ,GAAG,EAAE,CAAC;gBACN,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,GAAG;aACf;YACD,kBAAkB,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;YACzD,IAAI,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;YACrB,QAAQ,EAAE,GAAG;YACb,MAAM,EAAE,GAAG;YACX,qBAAqB,EAAE,GAAG;YAC1B,WAAW,EAAE,EAAE;YACf,eAAe,EAAE,GAAG;YACpB,QAAQ,EAAE,GAAG;YACb,UAAU,EAAE,GAAG;YACf,QAAQ,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE;YAC5B,SAAS,EAAE,GAAG;YACd,SAAS,EAAE,GAAG;SACf,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,KAAK,GAAG,iCAAc,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;YAExD,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YACvD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YACzD,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;YAC3D,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,6BAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,4BAA4B;YAC7E,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;YAChF,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YACzD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;YACrD,MAAM,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,CAAC;YACnF,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,QAAQ,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YAC3C,MAAM,KAAK,GAAG,iCAAc,CAAC,MAAM,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;YAElE,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oEAAoE,EAAE,GAAG,EAAE;YAC5E,MAAM,cAAc,GAAyB;gBAC3C,IAAI,EAAE,eAAe;gBACrB,OAAO,EAAE,OAAO;gBAChB,QAAQ,EAAE,4BAAU,CAAC,SAAS;gBAC9B,SAAS,EAAE,2BAAS,CAAC,cAAc;gBACnC,kBAAkB,EAAE,CAAC,gBAAgB,CAAC;aACvC,CAAC;YAEF,MAAM,KAAK,GAAG,iCAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAEpD,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC/B,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACjC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC/B,MAAM,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACnC,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,iBAAiB,GAAyB;gBAC9C,GAAG,kBAAkB;gBACrB,aAAa,EAAE;oBACb,QAAQ,EAAE,wBAAwB;oBAClC,OAAO,EAAE,KAAK;iBACf;aACF,CAAC;YAEF,MAAM,KAAK,GAAG,iCAAc,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YAEvD,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YACpE,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB;YAC7D,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,gBAAgB;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,qBAAqB,GAAyB;gBAClD,GAAG,kBAAkB;gBACrB,IAAI,EAAE,gBAAgB;gBACtB,OAAO,EAAE,WAAW;aACrB,CAAC;YAEF,MAAM,KAAK,GAAG,iCAAc,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;YAE3D,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACtC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;YAC1B,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;gBAC3C,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;gBAE3D,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;YACxF,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;gBACrD,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;gBAE9D,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;YACxF,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;gBAC9C,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;gBAExE,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,yCAAyC,CAAC,CAAC;YACzG,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;gBAC9C,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;gBAE9D,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;YAC3F,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;gBACjD,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,OAAO,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC;gBAE1E,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,2CAA2C,CAAC,CAAC;YAC3G,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;gBACjD,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,QAAQ,EAAE,SAAuB,EAAE,CAAC;gBAEpF,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;YACrF,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;gBACnD,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,SAAS,EAAE,SAAsB,EAAE,CAAC;gBAEpF,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;YACpF,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;gBAC3D,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,kBAAkB,EAAE,EAAE,EAAE,CAAC;gBAEzE,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,8CAA8C,CAAC,CAAC;YAC9G,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,kBAAkB,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC;gBAEpF,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,oDAAoD,CAAC,CAAC;YACpH,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;gBACjD,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;gBAE/D,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAC;YACnG,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;gBAClD,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC;gBAE/D,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAC;YACnG,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;gBAC/C,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;gBAE7D,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;YACjG,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;gBAC7D,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,qBAAqB,EAAE,CAAC,EAAE,CAAC;gBAE3E,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,gDAAgD,CAAC,CAAC;YAChH,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;gBACjE,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,qBAAqB,EAAE,CAAC,CAAC,EAAE,CAAC;gBAE5E,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,gDAAgD,CAAC,CAAC;YAChH,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;gBAC9D,MAAM,cAAc,GAAyB;oBAC3C,GAAG,kBAAkB;oBACrB,aAAa,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE;iBAC9B,CAAC;gBAEF,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,8CAA8C,CAAC,CAAC;YAC9G,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;gBAC/D,MAAM,cAAc,GAAyB;oBAC3C,GAAG,kBAAkB;oBACrB,aAAa,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE;iBAC/B,CAAC;gBAEF,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,0CAA0C,CAAC,CAAC;YAC1G,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;gBACjE,MAAM,cAAc,GAAyB;oBAC3C,GAAG,kBAAkB;oBACrB,aAAa,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE;iBAChC,CAAC;gBAEF,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,iDAAiD,CAAC,CAAC;YACjH,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;gBACnD,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,IAAI,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC;gBAEtE,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;YACpG,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,KAAK,GAAG,iCAAc,CAAC,YAAY,CAAC,uBAAuB,CAAC,CAAC;YAEnE,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,EAAE,CAAC,CAAC;YAC7D,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;YAC5D,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;YAC9D,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;YAChE,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;YAC1D,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;YACpE,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;YAChE,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;YAC1B,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;gBAC3C,MAAM,WAAW,GAAG,EAAE,GAAG,uBAAuB,EAAE,EAAE,EAAE,YAAY,EAAE,CAAC;gBAErE,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,yCAAyC,CAAC,CAAC;YAC5G,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;gBAC3C,MAAM,WAAW,GAAG,EAAE,GAAG,uBAAuB,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;gBAE7D,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;YAC3F,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;gBAC9C,MAAM,WAAW,GAAG,EAAE,GAAG,uBAAuB,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;gBAEhE,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;YAC9F,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;gBACjD,MAAM,WAAW,GAAG,EAAE,GAAG,uBAAuB,EAAE,QAAQ,EAAE,SAAuB,EAAE,CAAC;gBAEtF,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;YACxF,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;gBACnD,MAAM,WAAW,GAAG,EAAE,GAAG,uBAAuB,EAAE,SAAS,EAAE,SAAsB,EAAE,CAAC;gBAEtF,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;YACvF,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;gBAC/C,MAAM,WAAW,GAAG,EAAE,GAAG,uBAAuB,EAAE,MAAM,EAAE,SAAwB,EAAE,CAAC;gBAErF,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;YACzF,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;gBAC3D,MAAM,WAAW,GAAG,EAAE,GAAG,uBAAuB,EAAE,kBAAkB,EAAE,EAAE,EAAE,CAAC;gBAE3E,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,8CAA8C,CAAC,CAAC;YACjH,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;gBACjD,MAAM,WAAW,GAAG,EAAE,GAAG,uBAAuB,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;gBAEjE,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAC;YACtG,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;gBAC/C,MAAM,WAAW,GAAG,EAAE,GAAG,uBAAuB,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;gBAE/D,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;YACpG,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;gBAChE,MAAM,WAAW,GAAG,EAAE,GAAG,uBAAuB,EAAE,qBAAqB,EAAE,CAAC,EAAE,CAAC;gBAE7E,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,gDAAgD,CAAC,CAAC;YACnH,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;gBACtD,MAAM,WAAW,GAAG,EAAE,GAAG,uBAAuB,EAAE,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC;gBAEpE,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;YACpG,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;gBACtD,MAAM,WAAW,GAAG,EAAE,GAAG,uBAAuB,EAAE,SAAS,EAAE,SAAgB,EAAE,CAAC;gBAEhF,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;YACpG,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;gBACpD,MAAM,WAAW,GAAG,EAAE,GAAG,uBAAuB,EAAE,SAAS,EAAE,SAAgB,EAAE,CAAC;gBAEhF,MAAM,CAAC,GAAG,EAAE,CAAC,iCAAc,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAC;YAClG,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,KAAK,GAAG,iCAAc,CAAC,gBAAgB,EAAE,CAAC;YAEhD,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACtC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACpC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,4BAAU,CAAC,SAAS,CAAC,CAAC;YAClD,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,cAAc,CAAC,CAAC;YACvD,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,OAAO,CAAC,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAC,CAAC;YACzE,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACjC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC/B,MAAM,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC7C,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,SAAS,GAAG;gBAChB,IAAI,EAAE,mBAAmB;gBACzB,QAAQ,EAAE,4BAAU,CAAC,MAAM;gBAC3B,qBAAqB,EAAE,EAAE;aAC1B,CAAC;YAEF,MAAM,KAAK,GAAG,iCAAc,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YAEzD,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAC7C,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,4BAAU,CAAC,MAAM,CAAC,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC7C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,0BAA0B;QACjE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\factories\\__tests__\\ai-model.factory.spec.ts"], "sourcesContent": ["import { UniqueEntityId } from '../../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { AIProvider, ModelType, ModelStatus } from '../../entities/ai-model.entity';\r\nimport { AIModelFactory, CreateAIModelRequest, ReconstitutionData } from '../ai-model.factory';\r\n\r\ndescribe('AIModelFactory', () => {\r\n  let validCreateRequest: CreateAIModelRequest;\r\n  let validReconstitutionData: ReconstitutionData;\r\n\r\n  beforeEach(() => {\r\n    validCreateRequest = {\r\n      name: 'Test Model',\r\n      version: '1.0.0',\r\n      provider: AIProvider.OPENAI,\r\n      modelType: ModelType.LANGUAGE_MODEL,\r\n      supportedTaskTypes: ['text-generation', 'classification'],\r\n      tags: ['test', 'nlp'],\r\n      priority: 5.0,\r\n      weight: 2.0,\r\n      maxConcurrentRequests: 100,\r\n      metadata: { version: '1.0' },\r\n    };\r\n\r\n    const now = new Date();\r\n    validReconstitutionData = {\r\n      id: UniqueEntityId.generate().toString(),\r\n      name: 'Test Model',\r\n      version: '1.0.0',\r\n      provider: AIProvider.OPENAI,\r\n      modelType: ModelType.LANGUAGE_MODEL,\r\n      configuration: {\r\n        endpoint: 'https://api.example.com',\r\n        apiKey: 'test-key',\r\n        timeout: 30000,\r\n        retries: 3,\r\n        batchSize: 10,\r\n        customSettings: {},\r\n      },\r\n      performance: {\r\n        totalRequests: 100,\r\n        successfulRequests: 95,\r\n        failedRequests: 5,\r\n        averageLatency: 200,\r\n        p95Latency: 300,\r\n        p99Latency: 500,\r\n        accuracy: 0.95,\r\n        precision: 0.92,\r\n        recall: 0.88,\r\n        f1Score: 0.90,\r\n        throughput: 50,\r\n        lastUpdated: now,\r\n      },\r\n      status: ModelStatus.ACTIVE,\r\n      capabilities: {\r\n        maxInputLength: 4096,\r\n        maxOutputLength: 2048,\r\n        supportsBatch: true,\r\n        supportsStreaming: false,\r\n        supportsFineTuning: true,\r\n        languages: ['en', 'es'],\r\n        modalities: ['text'],\r\n      },\r\n      resourceRequirements: {\r\n        cpu: 2,\r\n        memory: 2048,\r\n        gpu: 1,\r\n        storage: 1024,\r\n        bandwidth: 100,\r\n      },\r\n      supportedTaskTypes: ['text-generation', 'classification'],\r\n      tags: ['test', 'nlp'],\r\n      priority: 5.0,\r\n      weight: 2.0,\r\n      maxConcurrentRequests: 100,\r\n      currentLoad: 10,\r\n      lastHealthCheck: now,\r\n      lastUsed: now,\r\n      deployedAt: now,\r\n      metadata: { version: '1.0' },\r\n      createdAt: now,\r\n      updatedAt: now,\r\n    };\r\n  });\r\n\r\n  describe('create', () => {\r\n    it('should create a valid AI model', () => {\r\n      const model = AIModelFactory.create(validCreateRequest);\r\n\r\n      expect(model.name).toBe(validCreateRequest.name);\r\n      expect(model.version).toBe(validCreateRequest.version);\r\n      expect(model.provider).toBe(validCreateRequest.provider);\r\n      expect(model.modelType).toBe(validCreateRequest.modelType);\r\n      expect(model.status).toBe(ModelStatus.INACTIVE); // New models start inactive\r\n      expect(model.supportedTaskTypes).toEqual(validCreateRequest.supportedTaskTypes);\r\n      expect(model.tags).toEqual(validCreateRequest.tags);\r\n      expect(model.priority).toBe(validCreateRequest.priority);\r\n      expect(model.weight).toBe(validCreateRequest.weight);\r\n      expect(model.maxConcurrentRequests).toBe(validCreateRequest.maxConcurrentRequests);\r\n      expect(model.currentLoad).toBe(0);\r\n      expect(model.createdAt).toBeInstanceOf(Date);\r\n      expect(model.updatedAt).toBeInstanceOf(Date);\r\n    });\r\n\r\n    it('should create with custom ID', () => {\r\n      const customId = UniqueEntityId.generate();\r\n      const model = AIModelFactory.create(validCreateRequest, customId);\r\n\r\n      expect(model.id.equals(customId)).toBe(true);\r\n    });\r\n\r\n    it('should create with default values when optional fields are omitted', () => {\r\n      const minimalRequest: CreateAIModelRequest = {\r\n        name: 'Minimal Model',\r\n        version: '1.0.0',\r\n        provider: AIProvider.PYTHON_AI,\r\n        modelType: ModelType.CLASSIFICATION,\r\n        supportedTaskTypes: ['classification'],\r\n      };\r\n\r\n      const model = AIModelFactory.create(minimalRequest);\r\n\r\n      expect(model.tags).toEqual([]);\r\n      expect(model.priority).toBe(1.0);\r\n      expect(model.weight).toBe(1.0);\r\n      expect(model.maxConcurrentRequests).toBe(100);\r\n      expect(model.metadata).toEqual({});\r\n      expect(model.configuration.timeout).toBe(30000);\r\n      expect(model.configuration.retries).toBe(3);\r\n      expect(model.configuration.batchSize).toBe(10);\r\n      expect(model.capabilities.maxInputLength).toBe(4096);\r\n      expect(model.capabilities.languages).toEqual(['en']);\r\n    });\r\n\r\n    it('should create with partial configuration', () => {\r\n      const requestWithConfig: CreateAIModelRequest = {\r\n        ...validCreateRequest,\r\n        configuration: {\r\n          endpoint: 'https://custom.api.com',\r\n          timeout: 60000,\r\n        },\r\n      };\r\n\r\n      const model = AIModelFactory.create(requestWithConfig);\r\n\r\n      expect(model.configuration.endpoint).toBe('https://custom.api.com');\r\n      expect(model.configuration.timeout).toBe(60000);\r\n      expect(model.configuration.retries).toBe(3); // Default value\r\n      expect(model.configuration.batchSize).toBe(10); // Default value\r\n    });\r\n\r\n    it('should trim whitespace from name and version', () => {\r\n      const requestWithWhitespace: CreateAIModelRequest = {\r\n        ...validCreateRequest,\r\n        name: '  Test Model  ',\r\n        version: '  1.0.0  ',\r\n      };\r\n\r\n      const model = AIModelFactory.create(requestWithWhitespace);\r\n\r\n      expect(model.name).toBe('Test Model');\r\n      expect(model.version).toBe('1.0.0');\r\n    });\r\n\r\n    describe('validation', () => {\r\n      it('should throw error for empty name', () => {\r\n        const invalidRequest = { ...validCreateRequest, name: '' };\r\n\r\n        expect(() => AIModelFactory.create(invalidRequest)).toThrow('Model name is required');\r\n      });\r\n\r\n      it('should throw error for whitespace-only name', () => {\r\n        const invalidRequest = { ...validCreateRequest, name: '   ' };\r\n\r\n        expect(() => AIModelFactory.create(invalidRequest)).toThrow('Model name is required');\r\n      });\r\n\r\n      it('should throw error for name too long', () => {\r\n        const invalidRequest = { ...validCreateRequest, name: 'a'.repeat(256) };\r\n\r\n        expect(() => AIModelFactory.create(invalidRequest)).toThrow('Model name cannot exceed 255 characters');\r\n      });\r\n\r\n      it('should throw error for empty version', () => {\r\n        const invalidRequest = { ...validCreateRequest, version: '' };\r\n\r\n        expect(() => AIModelFactory.create(invalidRequest)).toThrow('Model version is required');\r\n      });\r\n\r\n      it('should throw error for version too long', () => {\r\n        const invalidRequest = { ...validCreateRequest, version: 'a'.repeat(51) };\r\n\r\n        expect(() => AIModelFactory.create(invalidRequest)).toThrow('Model version cannot exceed 50 characters');\r\n      });\r\n\r\n      it('should throw error for invalid provider', () => {\r\n        const invalidRequest = { ...validCreateRequest, provider: 'invalid' as AIProvider };\r\n\r\n        expect(() => AIModelFactory.create(invalidRequest)).toThrow('Invalid AI provider');\r\n      });\r\n\r\n      it('should throw error for invalid model type', () => {\r\n        const invalidRequest = { ...validCreateRequest, modelType: 'invalid' as ModelType };\r\n\r\n        expect(() => AIModelFactory.create(invalidRequest)).toThrow('Invalid model type');\r\n      });\r\n\r\n      it('should throw error for empty supported task types', () => {\r\n        const invalidRequest = { ...validCreateRequest, supportedTaskTypes: [] };\r\n\r\n        expect(() => AIModelFactory.create(invalidRequest)).toThrow('At least one supported task type is required');\r\n      });\r\n\r\n      it('should throw error for empty task type in array', () => {\r\n        const invalidRequest = { ...validCreateRequest, supportedTaskTypes: ['valid', ''] };\r\n\r\n        expect(() => AIModelFactory.create(invalidRequest)).toThrow('All supported task types must be non-empty strings');\r\n      });\r\n\r\n      it('should throw error for invalid priority', () => {\r\n        const invalidRequest = { ...validCreateRequest, priority: 15 };\r\n\r\n        expect(() => AIModelFactory.create(invalidRequest)).toThrow('Priority must be between 0 and 10');\r\n      });\r\n\r\n      it('should throw error for negative priority', () => {\r\n        const invalidRequest = { ...validCreateRequest, priority: -1 };\r\n\r\n        expect(() => AIModelFactory.create(invalidRequest)).toThrow('Priority must be between 0 and 10');\r\n      });\r\n\r\n      it('should throw error for invalid weight', () => {\r\n        const invalidRequest = { ...validCreateRequest, weight: 15 };\r\n\r\n        expect(() => AIModelFactory.create(invalidRequest)).toThrow('Weight must be between 0 and 10');\r\n      });\r\n\r\n      it('should throw error for zero max concurrent requests', () => {\r\n        const invalidRequest = { ...validCreateRequest, maxConcurrentRequests: 0 };\r\n\r\n        expect(() => AIModelFactory.create(invalidRequest)).toThrow('Max concurrent requests must be greater than 0');\r\n      });\r\n\r\n      it('should throw error for negative max concurrent requests', () => {\r\n        const invalidRequest = { ...validCreateRequest, maxConcurrentRequests: -1 };\r\n\r\n        expect(() => AIModelFactory.create(invalidRequest)).toThrow('Max concurrent requests must be greater than 0');\r\n      });\r\n\r\n      it('should throw error for invalid configuration timeout', () => {\r\n        const invalidRequest: CreateAIModelRequest = {\r\n          ...validCreateRequest,\r\n          configuration: { timeout: 0 },\r\n        };\r\n\r\n        expect(() => AIModelFactory.create(invalidRequest)).toThrow('Configuration timeout must be greater than 0');\r\n      });\r\n\r\n      it('should throw error for negative configuration retries', () => {\r\n        const invalidRequest: CreateAIModelRequest = {\r\n          ...validCreateRequest,\r\n          configuration: { retries: -1 },\r\n        };\r\n\r\n        expect(() => AIModelFactory.create(invalidRequest)).toThrow('Configuration retries cannot be negative');\r\n      });\r\n\r\n      it('should throw error for invalid configuration batch size', () => {\r\n        const invalidRequest: CreateAIModelRequest = {\r\n          ...validCreateRequest,\r\n          configuration: { batchSize: 0 },\r\n        };\r\n\r\n        expect(() => AIModelFactory.create(invalidRequest)).toThrow('Configuration batch size must be greater than 0');\r\n      });\r\n\r\n      it('should throw error for empty tag in array', () => {\r\n        const invalidRequest = { ...validCreateRequest, tags: ['valid', ''] };\r\n\r\n        expect(() => AIModelFactory.create(invalidRequest)).toThrow('All tags must be non-empty strings');\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('reconstitute', () => {\r\n    it('should reconstitute a valid AI model', () => {\r\n      const model = AIModelFactory.reconstitute(validReconstitutionData);\r\n\r\n      expect(model.id.toString()).toBe(validReconstitutionData.id);\r\n      expect(model.name).toBe(validReconstitutionData.name);\r\n      expect(model.version).toBe(validReconstitutionData.version);\r\n      expect(model.provider).toBe(validReconstitutionData.provider);\r\n      expect(model.modelType).toBe(validReconstitutionData.modelType);\r\n      expect(model.status).toBe(validReconstitutionData.status);\r\n      expect(model.currentLoad).toBe(validReconstitutionData.currentLoad);\r\n      expect(model.createdAt).toBe(validReconstitutionData.createdAt);\r\n      expect(model.updatedAt).toBe(validReconstitutionData.updatedAt);\r\n    });\r\n\r\n    describe('validation', () => {\r\n      it('should throw error for invalid ID', () => {\r\n        const invalidData = { ...validReconstitutionData, id: 'invalid-id' };\r\n\r\n        expect(() => AIModelFactory.reconstitute(invalidData)).toThrow('Valid ID is required for reconstitution');\r\n      });\r\n\r\n      it('should throw error for empty name', () => {\r\n        const invalidData = { ...validReconstitutionData, name: '' };\r\n\r\n        expect(() => AIModelFactory.reconstitute(invalidData)).toThrow('Model name is required');\r\n      });\r\n\r\n      it('should throw error for empty version', () => {\r\n        const invalidData = { ...validReconstitutionData, version: '' };\r\n\r\n        expect(() => AIModelFactory.reconstitute(invalidData)).toThrow('Model version is required');\r\n      });\r\n\r\n      it('should throw error for invalid provider', () => {\r\n        const invalidData = { ...validReconstitutionData, provider: 'invalid' as AIProvider };\r\n\r\n        expect(() => AIModelFactory.reconstitute(invalidData)).toThrow('Invalid AI provider');\r\n      });\r\n\r\n      it('should throw error for invalid model type', () => {\r\n        const invalidData = { ...validReconstitutionData, modelType: 'invalid' as ModelType };\r\n\r\n        expect(() => AIModelFactory.reconstitute(invalidData)).toThrow('Invalid model type');\r\n      });\r\n\r\n      it('should throw error for invalid status', () => {\r\n        const invalidData = { ...validReconstitutionData, status: 'invalid' as ModelStatus };\r\n\r\n        expect(() => AIModelFactory.reconstitute(invalidData)).toThrow('Invalid model status');\r\n      });\r\n\r\n      it('should throw error for empty supported task types', () => {\r\n        const invalidData = { ...validReconstitutionData, supportedTaskTypes: [] };\r\n\r\n        expect(() => AIModelFactory.reconstitute(invalidData)).toThrow('At least one supported task type is required');\r\n      });\r\n\r\n      it('should throw error for invalid priority', () => {\r\n        const invalidData = { ...validReconstitutionData, priority: 15 };\r\n\r\n        expect(() => AIModelFactory.reconstitute(invalidData)).toThrow('Priority must be between 0 and 10');\r\n      });\r\n\r\n      it('should throw error for invalid weight', () => {\r\n        const invalidData = { ...validReconstitutionData, weight: -1 };\r\n\r\n        expect(() => AIModelFactory.reconstitute(invalidData)).toThrow('Weight must be between 0 and 10');\r\n      });\r\n\r\n      it('should throw error for invalid max concurrent requests', () => {\r\n        const invalidData = { ...validReconstitutionData, maxConcurrentRequests: 0 };\r\n\r\n        expect(() => AIModelFactory.reconstitute(invalidData)).toThrow('Max concurrent requests must be greater than 0');\r\n      });\r\n\r\n      it('should throw error for negative current load', () => {\r\n        const invalidData = { ...validReconstitutionData, currentLoad: -1 };\r\n\r\n        expect(() => AIModelFactory.reconstitute(invalidData)).toThrow('Current load cannot be negative');\r\n      });\r\n\r\n      it('should throw error for invalid creation date', () => {\r\n        const invalidData = { ...validReconstitutionData, createdAt: 'invalid' as any };\r\n\r\n        expect(() => AIModelFactory.reconstitute(invalidData)).toThrow('Valid creation date is required');\r\n      });\r\n\r\n      it('should throw error for invalid update date', () => {\r\n        const invalidData = { ...validReconstitutionData, updatedAt: 'invalid' as any };\r\n\r\n        expect(() => AIModelFactory.reconstitute(invalidData)).toThrow('Valid update date is required');\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('createForTesting', () => {\r\n    it('should create a test model with default values', () => {\r\n      const model = AIModelFactory.createForTesting();\r\n\r\n      expect(model.name).toBe('Test Model');\r\n      expect(model.version).toBe('1.0.0');\r\n      expect(model.provider).toBe(AIProvider.PYTHON_AI);\r\n      expect(model.modelType).toBe(ModelType.CLASSIFICATION);\r\n      expect(model.supportedTaskTypes).toEqual(['classification', 'analysis']);\r\n      expect(model.tags).toEqual(['test']);\r\n      expect(model.priority).toBe(1.0);\r\n      expect(model.weight).toBe(1.0);\r\n      expect(model.maxConcurrentRequests).toBe(10);\r\n      expect(model.metadata.test).toBe(true);\r\n    });\r\n\r\n    it('should create a test model with overrides', () => {\r\n      const overrides = {\r\n        name: 'Custom Test Model',\r\n        provider: AIProvider.OPENAI,\r\n        maxConcurrentRequests: 50,\r\n      };\r\n\r\n      const model = AIModelFactory.createForTesting(overrides);\r\n\r\n      expect(model.name).toBe('Custom Test Model');\r\n      expect(model.provider).toBe(AIProvider.OPENAI);\r\n      expect(model.maxConcurrentRequests).toBe(50);\r\n      expect(model.version).toBe('1.0.0'); // Default value preserved\r\n    });\r\n  });\r\n});"], "version": 3}