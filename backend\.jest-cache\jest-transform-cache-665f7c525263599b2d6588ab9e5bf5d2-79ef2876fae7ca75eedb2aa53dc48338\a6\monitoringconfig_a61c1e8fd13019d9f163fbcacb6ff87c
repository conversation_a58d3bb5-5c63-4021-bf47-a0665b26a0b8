4cf9cf1e8e0e65c91c4b632291eb5198
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.monitoringConfig = void 0;
const config_1 = require("@nestjs/config");
// Helper functions for cleaner environment variable parsing
const parseBoolean = (value, defaultValue = false) => {
    if (value === undefined)
        return defaultValue;
    return value.toLowerCase() === 'true';
};
const parseNumber = (value, defaultValue) => {
    const parsed = parseInt(value || '', 10);
    return isNaN(parsed) ? defaultValue : parsed;
};
const parseFloatSafe = (value, defaultValue) => {
    const parsed = parseFloat(value || '');
    return isNaN(parsed) ? defaultValue : parsed;
};
const parseStringArray = (value, delimiter = ',') => {
    return value ? value.split(delimiter).map(item => item.trim()) : [];
};
const parseJsonSafe = (value, defaultValue) => {
    if (!value)
        return defaultValue;
    try {
        return JSON.parse(value);
    }
    catch {
        return defaultValue;
    }
};
// Constants for better maintainability
const DEFAULT_TIMEOUTS = {
    HEALTH_CHECK: 5000,
    DATABASE: 3000,
    REDIS: 3000,
    PROMETHEUS: 5000,
    EXTERNAL_SERVICE: 5000,
};
const DEFAULT_THRESHOLDS = {
    MEMORY_MB: 150,
    DISK_PERCENT: 80,
    SLOW_REQUEST_MS: 1000,
    SYSTEM_ALERT_PERCENT: 80,
    EVENT_LOOP_MS: 100,
    ERROR_RATE: 0.05,
    RESPONSE_TIME_MS: 2000,
};
const DEFAULT_INTERVALS = {
    MEMORY_MONITORING: 30000,
    CPU_MONITORING: 30000,
    EVENT_LOOP_MONITORING: 10000,
    ALERT_WINDOW: 300,
};
const METRIC_BUCKETS = {
    http: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10],
    database: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5],
    redis: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1],
    ai: [0.1, 0.5, 1, 2, 5, 10, 30, 60],
};
/**
 * Monitoring and observability configuration
 * Provides settings for health checks, metrics, and performance monitoring
 */
exports.monitoringConfig = (0, config_1.registerAs)('monitoring', () => ({
    health: {
        enabled: !parseBoolean(process.env['HEALTH_CHECK_ENABLED'], true),
        endpoint: process.env['HEALTH_CHECK_ENDPOINT'] || '/health',
        timeout: parseNumber(process.env['HEALTH_CHECK_TIMEOUT'], DEFAULT_TIMEOUTS.HEALTH_CHECK),
        checks: {
            database: {
                enabled: !parseBoolean(process.env['HEALTH_CHECK_DATABASE_ENABLED'], true),
                timeout: parseNumber(process.env['HEALTH_CHECK_DATABASE_TIMEOUT'], DEFAULT_TIMEOUTS.DATABASE),
                query: process.env['HEALTH_CHECK_DATABASE_QUERY'] || 'SELECT 1',
            },
            redis: {
                enabled: !parseBoolean(process.env['HEALTH_CHECK_REDIS_ENABLED'], true),
                timeout: parseNumber(process.env['HEALTH_CHECK_REDIS_TIMEOUT'], DEFAULT_TIMEOUTS.REDIS),
                command: process.env['HEALTH_CHECK_REDIS_COMMAND'] || 'ping',
            },
            memory: {
                enabled: !parseBoolean(process.env['HEALTH_CHECK_MEMORY_ENABLED'], true),
                heapThreshold: parseNumber(process.env['HEALTH_CHECK_MEMORY_HEAP_THRESHOLD'], DEFAULT_THRESHOLDS.MEMORY_MB),
                rssThreshold: parseNumber(process.env['HEALTH_CHECK_MEMORY_RSS_THRESHOLD'], DEFAULT_THRESHOLDS.MEMORY_MB),
            },
            disk: {
                enabled: parseBoolean(process.env['HEALTH_CHECK_DISK_ENABLED']),
                threshold: parseNumber(process.env['HEALTH_CHECK_DISK_THRESHOLD'], DEFAULT_THRESHOLDS.DISK_PERCENT),
                path: process.env['HEALTH_CHECK_DISK_PATH'] || '/',
            },
            external: {
                enabled: parseBoolean(process.env['HEALTH_CHECK_EXTERNAL_ENABLED']),
                services: [
                    {
                        name: 'ai-service',
                        url: `${process.env['AI_SERVICE_URL']}/health`,
                        timeout: DEFAULT_TIMEOUTS.EXTERNAL_SERVICE,
                        enabled: parseBoolean(process.env['AI_SERVICE_ENABLED']),
                    },
                    {
                        name: 'threat-intel-api',
                        url: `${process.env['THREAT_INTEL_URL']}/health`,
                        timeout: DEFAULT_TIMEOUTS.EXTERNAL_SERVICE,
                        enabled: parseBoolean(process.env['THREAT_INTEL_ENABLED']),
                    },
                ].filter(service => service.enabled),
            },
        },
    },
    metrics: {
        enabled: parseBoolean(process.env['METRICS_ENABLED']),
        endpoint: process.env['METRICS_ENDPOINT'] || '/metrics',
        port: parseNumber(process.env['METRICS_PORT'], 9090),
        path: process.env['METRICS_PATH'] || '/metrics',
        prometheus: {
            enabled: parseBoolean(process.env['PROMETHEUS_ENABLED']),
            defaultLabels: {
                app: process.env['APP_NAME'] || 'sentinel-backend',
                version: process.env['APP_VERSION'] || '1.0.0',
                environment: process.env['NODE_ENV'] || 'development',
            },
            collectDefaultMetrics: !parseBoolean(process.env['PROMETHEUS_COLLECT_DEFAULT'], true),
            timeout: parseNumber(process.env['PROMETHEUS_TIMEOUT'], DEFAULT_TIMEOUTS.PROMETHEUS),
        },
        custom: {
            enabled: parseBoolean(process.env['CUSTOM_METRICS_ENABLED']),
            prefix: process.env['METRICS_PREFIX'] || 'sentinel_',
            buckets: METRIC_BUCKETS,
            tracking: {
                http: !parseBoolean(process.env['HTTP_METRICS_ENABLED'], true),
                database: parseBoolean(process.env['DATABASE_METRICS_ENABLED']),
                redis: parseBoolean(process.env['REDIS_METRICS_ENABLED']),
                ai: parseBoolean(process.env['AI_METRICS_ENABLED']),
            },
        },
    },
    performance: {
        enabled: parseBoolean(process.env['PERFORMANCE_MONITORING_ENABLED']),
        monitoring: {
            requests: {
                enabled: !parseBoolean(process.env['PERFORMANCE_REQUESTS_ENABLED'], true),
                slowThreshold: parseNumber(process.env['PERFORMANCE_SLOW_REQUEST_THRESHOLD'], DEFAULT_THRESHOLDS.SLOW_REQUEST_MS),
                logSlow: parseBoolean(process.env['PERFORMANCE_LOG_SLOW_REQUESTS']),
                trackUserAgent: parseBoolean(process.env['PERFORMANCE_TRACK_USER_AGENT']),
            },
            system: {
                memory: {
                    enabled: parseBoolean(process.env['PERFORMANCE_MEMORY_ENABLED']),
                    interval: parseNumber(process.env['PERFORMANCE_MEMORY_INTERVAL'], DEFAULT_INTERVALS.MEMORY_MONITORING),
                    alertThreshold: parseNumber(process.env['PERFORMANCE_MEMORY_ALERT_THRESHOLD'], DEFAULT_THRESHOLDS.SYSTEM_ALERT_PERCENT),
                },
                cpu: {
                    enabled: parseBoolean(process.env['PERFORMANCE_CPU_ENABLED']),
                    interval: parseNumber(process.env['PERFORMANCE_CPU_INTERVAL'], DEFAULT_INTERVALS.CPU_MONITORING),
                    alertThreshold: parseNumber(process.env['PERFORMANCE_CPU_ALERT_THRESHOLD'], DEFAULT_THRESHOLDS.SYSTEM_ALERT_PERCENT),
                },
                eventLoop: {
                    enabled: parseBoolean(process.env['PERFORMANCE_EVENT_LOOP_ENABLED']),
                    interval: parseNumber(process.env['PERFORMANCE_EVENT_LOOP_INTERVAL'], DEFAULT_INTERVALS.EVENT_LOOP_MONITORING),
                    alertThreshold: parseNumber(process.env['PERFORMANCE_EVENT_LOOP_ALERT_THRESHOLD'], DEFAULT_THRESHOLDS.EVENT_LOOP_MS),
                },
            },
        },
    },
    tracing: {
        enabled: parseBoolean(process.env['TRACING_ENABLED']),
        serviceName: process.env['TRACING_SERVICE_NAME'] || 'sentinel-backend',
        serviceVersion: process.env['APP_VERSION'] || '1.0.0',
        providers: {
            jaeger: {
                enabled: parseBoolean(process.env['JAEGER_ENABLED']),
                endpoint: process.env['TRACING_JAEGER_ENDPOINT'] || 'http://localhost:14268/api/traces',
                agentHost: process.env['JAEGER_AGENT_HOST'] || 'localhost',
                agentPort: parseNumber(process.env['JAEGER_AGENT_PORT'], 6832),
                samplingRate: parseFloatSafe(process.env['JAEGER_SAMPLING_RATE'], 0.1),
            },
            openTelemetry: {
                enabled: parseBoolean(process.env['OTEL_ENABLED']),
                ...(process.env['OTEL_EXPORTER_OTLP_ENDPOINT'] && { endpoint: process.env['OTEL_EXPORTER_OTLP_ENDPOINT'] }),
                ...(process.env['OTEL_EXPORTER_OTLP_HEADERS'] && { headers: process.env['OTEL_EXPORTER_OTLP_HEADERS'] }),
                samplingRate: parseFloatSafe(process.env['OTEL_SAMPLING_RATE'], 0.1),
            },
        },
        traces: {
            httpRequests: !parseBoolean(process.env['TRACE_HTTP_REQUESTS'], true),
            databaseQueries: parseBoolean(process.env['TRACE_DATABASE_QUERIES']),
            redisCommands: parseBoolean(process.env['TRACE_REDIS_COMMANDS']),
            externalCalls: !parseBoolean(process.env['TRACE_EXTERNAL_CALLS'], true),
            aiServiceCalls: !parseBoolean(process.env['TRACE_AI_SERVICE_CALLS'], true),
        },
    },
    alerting: {
        enabled: parseBoolean(process.env['ALERTING_ENABLED']),
        channels: {
            email: {
                enabled: parseBoolean(process.env['ALERT_EMAIL_ENABLED']),
                recipients: parseStringArray(process.env['ALERT_EMAIL_RECIPIENTS']),
                ...(process.env['ALERT_SMTP_HOST'] && { smtpHost: process.env['ALERT_SMTP_HOST'] }),
                smtpPort: parseNumber(process.env['ALERT_SMTP_PORT'], 587),
                ...(process.env['ALERT_SMTP_USER'] && { smtpUser: process.env['ALERT_SMTP_USER'] }),
                ...(process.env['ALERT_SMTP_PASSWORD'] && { smtpPassword: process.env['ALERT_SMTP_PASSWORD'] }),
            },
            slack: {
                enabled: parseBoolean(process.env['ALERT_SLACK_ENABLED']),
                ...(process.env['ALERT_SLACK_WEBHOOK_URL'] && { webhookUrl: process.env['ALERT_SLACK_WEBHOOK_URL'] }),
                channel: process.env['ALERT_SLACK_CHANNEL'] || '#alerts',
                username: process.env['ALERT_SLACK_USERNAME'] || 'Sentinel Bot',
            },
            webhook: {
                enabled: parseBoolean(process.env['ALERT_WEBHOOK_ENABLED']),
                ...(process.env['ALERT_WEBHOOK_URL'] && { url: process.env['ALERT_WEBHOOK_URL'] }),
                headers: parseJsonSafe(process.env['ALERT_WEBHOOK_HEADERS'], {}),
            },
        },
        rules: {
            highErrorRate: {
                enabled: parseBoolean(process.env['ALERT_HIGH_ERROR_RATE_ENABLED']),
                threshold: parseFloatSafe(process.env['ALERT_HIGH_ERROR_RATE_THRESHOLD'], DEFAULT_THRESHOLDS.ERROR_RATE),
                window: parseNumber(process.env['ALERT_HIGH_ERROR_RATE_WINDOW'], DEFAULT_INTERVALS.ALERT_WINDOW),
            },
            highResponseTime: {
                enabled: parseBoolean(process.env['ALERT_HIGH_RESPONSE_TIME_ENABLED']),
                threshold: parseNumber(process.env['ALERT_HIGH_RESPONSE_TIME_THRESHOLD'], DEFAULT_THRESHOLDS.RESPONSE_TIME_MS),
                window: parseNumber(process.env['ALERT_HIGH_RESPONSE_TIME_WINDOW'], DEFAULT_INTERVALS.ALERT_WINDOW),
            },
            highMemoryUsage: {
                enabled: parseBoolean(process.env['ALERT_HIGH_MEMORY_USAGE_ENABLED']),
                threshold: parseNumber(process.env['ALERT_HIGH_MEMORY_USAGE_THRESHOLD'], DEFAULT_THRESHOLDS.SYSTEM_ALERT_PERCENT),
                window: parseNumber(process.env['ALERT_HIGH_MEMORY_USAGE_WINDOW'], DEFAULT_INTERVALS.ALERT_WINDOW),
            },
            databaseConnectionFailure: {
                enabled: parseBoolean(process.env['ALERT_DB_CONNECTION_FAILURE_ENABLED']),
                threshold: parseNumber(process.env['ALERT_DB_CONNECTION_FAILURE_THRESHOLD'], 3),
                window: parseNumber(process.env['ALERT_DB_CONNECTION_FAILURE_WINDOW'], 60),
            },
        },
    },
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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