d72095e99e0c8593371052a21a387fbf
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f;
Object.defineProperty(exports, "__esModule", { value: true });
exports.VulnerabilityFeed = void 0;
const typeorm_1 = require("typeorm");
/**
 * Vulnerability Feed entity
 * Represents external vulnerability data sources and feeds
 */
let VulnerabilityFeed = class VulnerabilityFeed {
    /**
     * Check if feed is due for sync
     */
    get isDueForSync() {
        if (!this.isActive || this.status === 'syncing')
            return false;
        if (!this.nextSyncAt)
            return true;
        return new Date() >= this.nextSyncAt;
    }
    /**
     * Check if feed is healthy
     */
    get isHealthy() {
        if (!this.syncStats)
            return true;
        const errorRate = this.syncStats.totalSyncs > 0
            ? (this.syncStats.failedSyncs / this.syncStats.totalSyncs) * 100
            : 0;
        return errorRate < 10; // Less than 10% error rate
    }
    /**
     * Get sync success rate
     */
    get syncSuccessRate() {
        if (!this.syncStats || this.syncStats.totalSyncs === 0)
            return 0;
        return (this.syncStats.successfulSyncs / this.syncStats.totalSyncs) * 100;
    }
    /**
     * Get time since last sync in hours
     */
    get hoursSinceLastSync() {
        if (!this.lastSyncAt)
            return null;
        const now = new Date();
        const diffMs = now.getTime() - this.lastSyncAt.getTime();
        return Math.floor(diffMs / (1000 * 60 * 60));
    }
    /**
     * Check if feed is overdue for sync
     */
    get isOverdue() {
        const hours = this.hoursSinceLastSync;
        if (hours === null)
            return true;
        // Consider overdue if more than 2x the sync interval
        const intervalHours = this.feedConfig.syncInterval / 60;
        return hours > (intervalHours * 2);
    }
    /**
     * Start sync
     */
    startSync() {
        this.status = 'syncing';
        this.lastSyncAttemptAt = new Date();
    }
    /**
     * Complete sync successfully
     */
    completeSync(stats) {
        this.status = 'active';
        this.lastSyncAt = new Date();
        if (!this.syncStats) {
            this.syncStats = {
                totalSyncs: 0,
                successfulSyncs: 0,
                failedSyncs: 0,
                totalRecordsProcessed: 0,
                totalRecordsAdded: 0,
                totalRecordsUpdated: 0,
                totalRecordsSkipped: 0,
                totalErrors: 0,
            };
        }
        this.syncStats.totalSyncs++;
        this.syncStats.successfulSyncs++;
        this.syncStats.lastSyncDuration = stats.duration;
        this.syncStats.totalRecordsProcessed += stats.recordsProcessed || 0;
        this.syncStats.totalRecordsAdded += stats.recordsAdded || 0;
        this.syncStats.totalRecordsUpdated += stats.recordsUpdated || 0;
        this.syncStats.totalRecordsSkipped += stats.recordsSkipped || 0;
        // Calculate average sync duration
        if (this.syncStats.averageSyncDuration) {
            this.syncStats.averageSyncDuration =
                (this.syncStats.averageSyncDuration + stats.duration) / 2;
        }
        else {
            this.syncStats.averageSyncDuration = stats.duration;
        }
        this.scheduleNextSync();
    }
    /**
     * Fail sync
     */
    failSync(error) {
        this.status = 'error';
        if (!this.syncStats) {
            this.syncStats = {
                totalSyncs: 0,
                successfulSyncs: 0,
                failedSyncs: 0,
                totalRecordsProcessed: 0,
                totalRecordsAdded: 0,
                totalRecordsUpdated: 0,
                totalRecordsSkipped: 0,
                totalErrors: 0,
            };
        }
        this.syncStats.totalSyncs++;
        this.syncStats.failedSyncs++;
        this.syncStats.totalErrors++;
        this.syncStats.lastErrorMessage = error;
        this.syncStats.lastErrorAt = new Date().toISOString();
        this.scheduleNextSync();
    }
    /**
     * Pause feed
     */
    pause() {
        this.status = 'paused';
        this.nextSyncAt = null;
    }
    /**
     * Resume feed
     */
    resume() {
        this.status = 'active';
        this.scheduleNextSync();
    }
    /**
     * Activate feed
     */
    activate() {
        this.isActive = true;
        this.status = 'active';
        this.scheduleNextSync();
    }
    /**
     * Deactivate feed
     */
    deactivate() {
        this.isActive = false;
        this.status = 'inactive';
        this.nextSyncAt = null;
    }
    /**
     * Schedule next sync
     */
    scheduleNextSync() {
        if (!this.isActive)
            return;
        const now = new Date();
        const intervalMs = this.feedConfig.syncInterval * 60 * 1000;
        this.nextSyncAt = new Date(now.getTime() + intervalMs);
    }
    /**
     * Add tag to feed
     */
    addTag(tag) {
        if (!this.tags.includes(tag)) {
            this.tags.push(tag);
        }
    }
    /**
     * Remove tag from feed
     */
    removeTag(tag) {
        this.tags = this.tags.filter(t => t !== tag);
    }
    /**
     * Get feed summary
     */
    getSummary() {
        return {
            id: this.id,
            name: this.name,
            feedType: this.feedType,
            provider: this.provider,
            status: this.status,
            isActive: this.isActive,
            priority: this.priority,
            isDueForSync: this.isDueForSync,
            isHealthy: this.isHealthy,
            isOverdue: this.isOverdue,
            syncSuccessRate: this.syncSuccessRate,
            hoursSinceLastSync: this.hoursSinceLastSync,
            lastSyncAt: this.lastSyncAt,
            nextSyncAt: this.nextSyncAt,
            tags: this.tags,
        };
    }
    /**
     * Export feed for reporting
     */
    exportForReporting() {
        return {
            feed: this.getSummary(),
            description: this.description,
            url: this.url,
            format: this.format,
            feedConfig: this.feedConfig,
            syncStats: this.syncStats,
            qualityMetrics: this.qualityMetrics,
            validationRules: this.validationRules,
            customAttributes: this.customAttributes,
            exportedAt: new Date().toISOString(),
        };
    }
};
exports.VulnerabilityFeed = VulnerabilityFeed;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], VulnerabilityFeed.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], VulnerabilityFeed.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], VulnerabilityFeed.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'feed_type',
        type: 'enum',
        enum: ['nvd', 'mitre', 'vendor', 'commercial', 'threat_intelligence', 'exploit_db', 'github_advisory', 'custom'],
    }),
    __metadata("design:type", String)
], VulnerabilityFeed.prototype, "feedType", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], VulnerabilityFeed.prototype, "provider", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], VulnerabilityFeed.prototype, "url", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['json', 'xml', 'csv', 'rss', 'atom', 'api', 'custom'],
        default: 'json',
    }),
    __metadata("design:type", String)
], VulnerabilityFeed.prototype, "format", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'auth_config', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], VulnerabilityFeed.prototype, "authConfig", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'feed_config', type: 'jsonb' }),
    __metadata("design:type", Object)
], VulnerabilityFeed.prototype, "feedConfig", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['active', 'inactive', 'error', 'syncing', 'paused'],
        default: 'inactive',
    }),
    __metadata("design:type", String)
], VulnerabilityFeed.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_active', default: true }),
    __metadata("design:type", Boolean)
], VulnerabilityFeed.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['low', 'medium', 'high', 'critical'],
        default: 'medium',
    }),
    __metadata("design:type", String)
], VulnerabilityFeed.prototype, "priority", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'sync_stats', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], VulnerabilityFeed.prototype, "syncStats", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'quality_metrics', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], VulnerabilityFeed.prototype, "qualityMetrics", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'validation_rules', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], VulnerabilityFeed.prototype, "validationRules", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_sync_at', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], VulnerabilityFeed.prototype, "lastSyncAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_sync_attempt_at', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], VulnerabilityFeed.prototype, "lastSyncAttemptAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'next_sync_at', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], VulnerabilityFeed.prototype, "nextSyncAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, default: '{}' }),
    __metadata("design:type", Array)
], VulnerabilityFeed.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'custom_attributes', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_d = typeof Record !== "undefined" && Record) === "function" ? _d : Object)
], VulnerabilityFeed.prototype, "customAttributes", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_e = typeof Date !== "undefined" && Date) === "function" ? _e : Object)
], VulnerabilityFeed.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_f = typeof Date !== "undefined" && Date) === "function" ? _f : Object)
], VulnerabilityFeed.prototype, "updatedAt", void 0);
exports.VulnerabilityFeed = VulnerabilityFeed = __decorate([
    (0, typeorm_1.Entity)('vulnerability_feeds'),
    (0, typeorm_1.Index)(['feedType']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['lastSyncAt']),
    (0, typeorm_1.Index)(['isActive'])
], VulnerabilityFeed);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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