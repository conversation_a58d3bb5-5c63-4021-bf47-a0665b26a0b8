63fbdb32a541a7601301a6866d47528a
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
const rate_limit_decorator_1 = require("../../decorators/rate-limit.decorator");
const rate_limit_exception_1 = require("../../exceptions/rate-limit.exception");
describe('Rate Limit Decorator', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });
    describe('@RateLimit decorator', () => {
        it('should allow requests within rate limit', async () => {
            var _a;
            let callCount = 0;
            class TestService {
                async limitedMethod() {
                    callCount++;
                    return `call-${callCount}`;
                }
            }
            __decorate([
                (0, rate_limit_decorator_1.RateLimit)({ maxRequests: 3, windowMs: 1000 }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "limitedMethod", null);
            const service = new TestService();
            // Should allow 3 requests
            const result1 = await service.limitedMethod();
            const result2 = await service.limitedMethod();
            const result3 = await service.limitedMethod();
            expect(result1).toBe('call-1');
            expect(result2).toBe('call-2');
            expect(result3).toBe('call-3');
            expect(callCount).toBe(3);
        });
        it('should block requests exceeding rate limit', async () => {
            var _a;
            let callCount = 0;
            class TestService {
                async limitedMethod() {
                    callCount++;
                    return `call-${callCount}`;
                }
            }
            __decorate([
                (0, rate_limit_decorator_1.RateLimit)({ maxRequests: 2, windowMs: 1000 }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "limitedMethod", null);
            const service = new TestService();
            // First two requests should succeed
            await service.limitedMethod();
            await service.limitedMethod();
            expect(callCount).toBe(2);
            // Third request should be rate limited
            await expect(service.limitedMethod()).rejects.toThrow(rate_limit_exception_1.RateLimitException);
            expect(callCount).toBe(2); // Method not called
        });
        it('should reset rate limit after time window', async () => {
            var _a;
            let callCount = 0;
            class TestService {
                async shortWindowMethod() {
                    callCount++;
                    return `call-${callCount}`;
                }
            }
            __decorate([
                (0, rate_limit_decorator_1.RateLimit)({ maxRequests: 1, windowMs: 50 }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "shortWindowMethod", null);
            const service = new TestService();
            // First request should succeed
            await service.shortWindowMethod();
            expect(callCount).toBe(1);
            // Second request should be rate limited
            await expect(service.shortWindowMethod()).rejects.toThrow(rate_limit_exception_1.RateLimitException);
            expect(callCount).toBe(1);
            // Wait for window to reset
            await new Promise(resolve => setTimeout(resolve, 60));
            // Request after window reset should succeed
            await service.shortWindowMethod();
            expect(callCount).toBe(2);
        });
        it('should use custom key generator', async () => {
            var _a;
            let callCount = 0;
            class TestService {
                async perUserMethod(userId) {
                    callCount++;
                    return `call-${userId}-${callCount}`;
                }
            }
            __decorate([
                (0, rate_limit_decorator_1.RateLimit)({
                    maxRequests: 1,
                    windowMs: 1000,
                    keyGenerator: (args) => `user-${args[0]}`
                }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", [String]),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "perUserMethod", null);
            const service = new TestService();
            // Different users should have separate rate limits
            await service.perUserMethod('user1');
            await service.perUserMethod('user2');
            expect(callCount).toBe(2);
            // Same user should be rate limited
            await expect(service.perUserMethod('user1')).rejects.toThrow(rate_limit_exception_1.RateLimitException);
            expect(callCount).toBe(2);
        });
        it('should handle skipSuccessfulRequests option', async () => {
            var _a;
            let callCount = 0;
            class TestService {
                async skipSuccessMethod() {
                    callCount++;
                    return `success-${callCount}`;
                }
            }
            __decorate([
                (0, rate_limit_decorator_1.RateLimit)({
                    maxRequests: 2,
                    windowMs: 1000,
                    skipSuccessfulRequests: true
                }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "skipSuccessMethod", null);
            const service = new TestService();
            // Multiple successful requests should not count against limit
            await service.skipSuccessMethod();
            await service.skipSuccessMethod();
            await service.skipSuccessMethod();
            await service.skipSuccessMethod();
            expect(callCount).toBe(4); // All requests succeeded
        });
        it('should handle skipFailedRequests option', async () => {
            var _a;
            let callCount = 0;
            class TestService {
                async skipFailMethod() {
                    callCount++;
                    throw new Error('Method failed');
                }
            }
            __decorate([
                (0, rate_limit_decorator_1.RateLimit)({
                    maxRequests: 2,
                    windowMs: 1000,
                    skipFailedRequests: true
                }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "skipFailMethod", null);
            const service = new TestService();
            // Multiple failed requests should not count against limit
            await expect(service.skipFailMethod()).rejects.toThrow('Method failed');
            await expect(service.skipFailMethod()).rejects.toThrow('Method failed');
            await expect(service.skipFailMethod()).rejects.toThrow('Method failed');
            expect(callCount).toBe(3); // All requests were attempted
        });
        it('should provide detailed error information', async () => {
            var _a;
            class TestService {
                async detailedErrorMethod() {
                    return 'success';
                }
            }
            __decorate([
                (0, rate_limit_decorator_1.RateLimit)({
                    maxRequests: 1,
                    windowMs: 1000,
                    message: 'Custom rate limit message'
                }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "detailedErrorMethod", null);
            const service = new TestService();
            // First request succeeds
            await service.detailedErrorMethod();
            // Second request should provide detailed error
            try {
                await service.detailedErrorMethod();
                fail('Should have thrown RateLimitException');
            }
            catch (error) {
                expect(error).toBeInstanceOf(rate_limit_exception_1.RateLimitException);
                const rateLimitError = error;
                expect(rateLimitError.message).toBe('Custom rate limit message');
                expect(rateLimitError.limit).toBe(1);
                expect(rateLimitError.windowSize).toBe(1);
                expect(rateLimitError.retryAfter).toBeGreaterThan(0);
            }
        });
        it('should clean up expired entries', async () => {
            var _a;
            class TestService {
                async cleanupTestMethod() {
                    return 'success';
                }
            }
            __decorate([
                (0, rate_limit_decorator_1.RateLimit)({ maxRequests: 1, windowMs: 50 }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "cleanupTestMethod", null);
            const service = new TestService();
            // Make request to create entry
            await service.cleanupTestMethod();
            // Should be rate limited immediately
            await expect(service.cleanupTestMethod()).rejects.toThrow(rate_limit_exception_1.RateLimitException);
            // Wait for entry to expire
            await new Promise(resolve => setTimeout(resolve, 60));
            // Should work again after cleanup
            await service.cleanupTestMethod();
        });
    });
    describe('@UserRateLimit decorator', () => {
        it('should rate limit per user', async () => {
            var _a;
            let callCount = 0;
            class TestService {
                async userLimitedMethod(context) {
                    callCount++;
                    return `user-call-${callCount}`;
                }
            }
            __decorate([
                (0, rate_limit_decorator_1.UserRateLimit)(1, 1000, 'User rate limit exceeded'),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", [Object]),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "userLimitedMethod", null);
            const service = new TestService();
            // Different users should have separate limits
            await service.userLimitedMethod({ userId: 'user1' });
            await service.userLimitedMethod({ userId: 'user2' });
            expect(callCount).toBe(2);
            // Same user should be rate limited
            await expect(service.userLimitedMethod({ userId: 'user1' }))
                .rejects.toThrow('User rate limit exceeded');
            expect(callCount).toBe(2);
        });
        it('should handle nested user object', async () => {
            var _a;
            class TestService {
                async nestedUserMethod(context) {
                    return 'success';
                }
            }
            __decorate([
                (0, rate_limit_decorator_1.UserRateLimit)(1, 1000),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", [Object]),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "nestedUserMethod", null);
            const service = new TestService();
            await service.nestedUserMethod({ user: { id: 'user1' } });
            await expect(service.nestedUserMethod({ user: { id: 'user1' } }))
                .rejects.toThrow(rate_limit_exception_1.RateLimitException);
        });
        it('should handle anonymous users', async () => {
            var _a;
            class TestService {
                async anonymousMethod(context) {
                    return 'anonymous success';
                }
            }
            __decorate([
                (0, rate_limit_decorator_1.UserRateLimit)(1, 1000),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", [Object]),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "anonymousMethod", null);
            const service = new TestService();
            await service.anonymousMethod({ data: 'test' });
            // Anonymous users should also be rate limited
            await expect(service.anonymousMethod({ data: 'test' }))
                .rejects.toThrow(rate_limit_exception_1.RateLimitException);
        });
    });
    describe('@GlobalRateLimit decorator', () => {
        it('should apply global rate limit across all calls', async () => {
            var _a;
            let callCount = 0;
            class TestService {
                async globalMethod(param) {
                    callCount++;
                    return `global-${param}-${callCount}`;
                }
            }
            __decorate([
                (0, rate_limit_decorator_1.GlobalRateLimit)(2, 1000),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", [String]),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "globalMethod", null);
            const service = new TestService();
            // First two calls should succeed
            await service.globalMethod('a');
            await service.globalMethod('b');
            expect(callCount).toBe(2);
            // Third call should be rate limited regardless of parameters
            await expect(service.globalMethod('c')).rejects.toThrow(rate_limit_exception_1.RateLimitException);
            expect(callCount).toBe(2);
        });
    });
    describe('@IpRateLimit decorator', () => {
        it('should rate limit per IP address', async () => {
            var _a;
            let callCount = 0;
            class TestService {
                async ipLimitedMethod(request) {
                    callCount++;
                    return `ip-call-${callCount}`;
                }
            }
            __decorate([
                (0, rate_limit_decorator_1.IpRateLimit)(1, 1000),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", [Object]),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "ipLimitedMethod", null);
            const service = new TestService();
            // Different IPs should have separate limits
            await service.ipLimitedMethod({ ip: '***********' });
            await service.ipLimitedMethod({ ip: '***********' });
            expect(callCount).toBe(2);
            // Same IP should be rate limited
            await expect(service.ipLimitedMethod({ ip: '***********' }))
                .rejects.toThrow(rate_limit_exception_1.RateLimitException);
            expect(callCount).toBe(2);
        });
        it('should handle different IP field names', async () => {
            var _a, _b;
            class TestService {
                async clientIpMethod(request) {
                    return 'success';
                }
                async remoteAddressMethod(request) {
                    return 'success';
                }
            }
            __decorate([
                (0, rate_limit_decorator_1.IpRateLimit)(1, 1000),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", [Object]),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "clientIpMethod", null);
            __decorate([
                (0, rate_limit_decorator_1.IpRateLimit)(1, 1000),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", [Object]),
                __metadata("design:returntype", typeof (_b = typeof Promise !== "undefined" && Promise) === "function" ? _b : Object)
            ], TestService.prototype, "remoteAddressMethod", null);
            const service = new TestService();
            await service.clientIpMethod({ clientIp: '********' });
            await expect(service.clientIpMethod({ clientIp: '********' }))
                .rejects.toThrow(rate_limit_exception_1.RateLimitException);
            await service.remoteAddressMethod({ remoteAddress: '********' });
            await expect(service.remoteAddressMethod({ remoteAddress: '********' }))
                .rejects.toThrow(rate_limit_exception_1.RateLimitException);
        });
        it('should handle unknown IP gracefully', async () => {
            var _a;
            class TestService {
                async unknownIpMethod(request) {
                    return 'success';
                }
            }
            __decorate([
                (0, rate_limit_decorator_1.IpRateLimit)(1, 1000),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", [Object]),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "unknownIpMethod", null);
            const service = new TestService();
            await service.unknownIpMethod({ data: 'test' });
            // Unknown IP should still be rate limited under 'unknown-ip' key
            await expect(service.unknownIpMethod({ data: 'test' }))
                .rejects.toThrow(rate_limit_exception_1.RateLimitException);
        });
    });
    describe('edge cases and error handling', () => {
        it('should handle concurrent requests correctly', async () => {
            var _a;
            let callCount = 0;
            class TestService {
                async concurrentMethod() {
                    callCount++;
                    // Simulate async operation
                    await new Promise(resolve => setTimeout(resolve, 10));
                    return `concurrent-${callCount}`;
                }
            }
            __decorate([
                (0, rate_limit_decorator_1.RateLimit)({ maxRequests: 2, windowMs: 1000 }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "concurrentMethod", null);
            const service = new TestService();
            // Make 3 concurrent requests
            const promises = [
                service.concurrentMethod(),
                service.concurrentMethod(),
                service.concurrentMethod(),
            ];
            const results = await Promise.allSettled(promises);
            // Due to the nature of concurrent requests and in-memory rate limiting,
            // the exact behavior may vary. We expect at least some rate limiting to occur.
            const successful = results.filter(r => r.status === 'fulfilled');
            const failed = results.filter(r => r.status === 'rejected');
            // At least one request should be rate limited, but due to race conditions
            // in concurrent execution, the exact number may vary
            expect(successful.length + failed.length).toBe(3);
            expect(failed.length).toBeGreaterThanOrEqual(0);
            expect(callCount).toBeGreaterThanOrEqual(2);
        });
        it('should handle method exceptions without affecting rate limit', async () => {
            var _a;
            let callCount = 0;
            class TestService {
                async exceptionMethod(shouldThrow) {
                    callCount++;
                    if (shouldThrow) {
                        throw new Error('Method exception');
                    }
                    return `success-${callCount}`;
                }
            }
            __decorate([
                (0, rate_limit_decorator_1.RateLimit)({ maxRequests: 2, windowMs: 1000 }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", [Boolean]),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "exceptionMethod", null);
            const service = new TestService();
            // First call throws exception but counts against limit
            await expect(service.exceptionMethod(true)).rejects.toThrow('Method exception');
            expect(callCount).toBe(1);
            // Second call succeeds and counts against limit
            await service.exceptionMethod(false);
            expect(callCount).toBe(2);
            // Third call should be rate limited
            await expect(service.exceptionMethod(false)).rejects.toThrow(rate_limit_exception_1.RateLimitException);
            expect(callCount).toBe(2);
        });
        it('should provide accurate retry-after information', async () => {
            var _a;
            class TestService {
                async retryAfterMethod() {
                    return 'success';
                }
            }
            __decorate([
                (0, rate_limit_decorator_1.RateLimit)({ maxRequests: 1, windowMs: 1000 }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "retryAfterMethod", null);
            const service = new TestService();
            const startTime = Date.now();
            await service.retryAfterMethod();
            try {
                await service.retryAfterMethod();
                fail('Should have thrown RateLimitException');
            }
            catch (error) {
                const rateLimitError = error;
                const retryAfter = rateLimitError.retryAfter;
                expect(retryAfter).toBeGreaterThan(0);
                expect(retryAfter).toBeLessThanOrEqual(1); // Should be <= 1 second
                // Verify reset time is approximately correct
                const expectedResetTime = startTime + 1000;
                const actualResetTime = rateLimitError.resetTime.getTime();
                expect(Math.abs(actualResetTime - expectedResetTime)).toBeLessThan(100);
            }
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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