8590c84a49025edc6ab7013fe2adf8f1
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VulnerabilityDetectionMethod = exports.VulnerabilityPriority = exports.VulnerabilityFixType = exports.VulnerabilityRemediationStatus = exports.VulnerabilityExploitMaturity = exports.VulnerabilityExploitationComplexity = exports.VulnerabilityExploitationStatus = exports.VulnerabilityReferenceType = exports.VulnerabilityStatus = exports.VulnerabilityType = exports.VulnerabilityCategory = exports.VulnerabilitySourceReliability = void 0;
/**
 * Vulnerability Source Reliability Levels
 */
var VulnerabilitySourceReliability;
(function (VulnerabilitySourceReliability) {
    /** Completely reliable source */
    VulnerabilitySourceReliability["A"] = "A";
    /** Usually reliable source */
    VulnerabilitySourceReliability["B"] = "B";
    /** Fairly reliable source */
    VulnerabilitySourceReliability["C"] = "C";
    /** Not usually reliable source */
    VulnerabilitySourceReliability["D"] = "D";
    /** Unreliable source */
    VulnerabilitySourceReliability["E"] = "E";
    /** Cannot be judged */
    VulnerabilitySourceReliability["F"] = "F";
})(VulnerabilitySourceReliability || (exports.VulnerabilitySourceReliability = VulnerabilitySourceReliability = {}));
/**
 * Vulnerability Categories
 */
var VulnerabilityCategory;
(function (VulnerabilityCategory) {
    VulnerabilityCategory["AUTHENTICATION"] = "authentication";
    VulnerabilityCategory["AUTHORIZATION"] = "authorization";
    VulnerabilityCategory["BUFFER_OVERFLOW"] = "buffer_overflow";
    VulnerabilityCategory["CODE_INJECTION"] = "code_injection";
    VulnerabilityCategory["COMMAND_INJECTION"] = "command_injection";
    VulnerabilityCategory["CROSS_SITE_SCRIPTING"] = "cross_site_scripting";
    VulnerabilityCategory["CROSS_SITE_REQUEST_FORGERY"] = "cross_site_request_forgery";
    VulnerabilityCategory["CRYPTOGRAPHIC"] = "cryptographic";
    VulnerabilityCategory["DATA_EXPOSURE"] = "data_exposure";
    VulnerabilityCategory["DENIAL_OF_SERVICE"] = "denial_of_service";
    VulnerabilityCategory["DESERIALIZATION"] = "deserialization";
    VulnerabilityCategory["DIRECTORY_TRAVERSAL"] = "directory_traversal";
    VulnerabilityCategory["FILE_INCLUSION"] = "file_inclusion";
    VulnerabilityCategory["INFORMATION_DISCLOSURE"] = "information_disclosure";
    VulnerabilityCategory["INPUT_VALIDATION"] = "input_validation";
    VulnerabilityCategory["INSECURE_CONFIGURATION"] = "insecure_configuration";
    VulnerabilityCategory["INSECURE_STORAGE"] = "insecure_storage";
    VulnerabilityCategory["LDAP_INJECTION"] = "ldap_injection";
    VulnerabilityCategory["MEMORY_CORRUPTION"] = "memory_corruption";
    VulnerabilityCategory["MISSING_AUTHENTICATION"] = "missing_authentication";
    VulnerabilityCategory["MISSING_AUTHORIZATION"] = "missing_authorization";
    VulnerabilityCategory["MISSING_ENCRYPTION"] = "missing_encryption";
    VulnerabilityCategory["PATH_TRAVERSAL"] = "path_traversal";
    VulnerabilityCategory["PRIVILEGE_ESCALATION"] = "privilege_escalation";
    VulnerabilityCategory["RACE_CONDITION"] = "race_condition";
    VulnerabilityCategory["REMOTE_CODE_EXECUTION"] = "remote_code_execution";
    VulnerabilityCategory["SESSION_MANAGEMENT"] = "session_management";
    VulnerabilityCategory["SQL_INJECTION"] = "sql_injection";
    VulnerabilityCategory["WEAK_CRYPTOGRAPHY"] = "weak_cryptography";
    VulnerabilityCategory["XML_INJECTION"] = "xml_injection";
    VulnerabilityCategory["OTHER"] = "other";
})(VulnerabilityCategory || (exports.VulnerabilityCategory = VulnerabilityCategory = {}));
/**
 * Vulnerability Types
 */
var VulnerabilityType;
(function (VulnerabilityType) {
    VulnerabilityType["DESIGN_FLAW"] = "design_flaw";
    VulnerabilityType["IMPLEMENTATION_BUG"] = "implementation_bug";
    VulnerabilityType["CONFIGURATION_ERROR"] = "configuration_error";
    VulnerabilityType["MISSING_PATCH"] = "missing_patch";
    VulnerabilityType["WEAK_CREDENTIALS"] = "weak_credentials";
    VulnerabilityType["INSECURE_DEFAULT"] = "insecure_default";
    VulnerabilityType["LOGIC_ERROR"] = "logic_error";
    VulnerabilityType["RACE_CONDITION"] = "race_condition";
    VulnerabilityType["TIME_OF_CHECK_TIME_OF_USE"] = "time_of_check_time_of_use";
    VulnerabilityType["OTHER"] = "other";
})(VulnerabilityType || (exports.VulnerabilityType = VulnerabilityType = {}));
/**
 * Vulnerability Status
 */
var VulnerabilityStatus;
(function (VulnerabilityStatus) {
    VulnerabilityStatus["PUBLISHED"] = "published";
    VulnerabilityStatus["MODIFIED"] = "modified";
    VulnerabilityStatus["ANALYZED"] = "analyzed";
    VulnerabilityStatus["AWAITING_ANALYSIS"] = "awaiting_analysis";
    VulnerabilityStatus["UNDERGOING_ANALYSIS"] = "undergoing_analysis";
    VulnerabilityStatus["REJECTED"] = "rejected";
    VulnerabilityStatus["DISPUTED"] = "disputed";
    VulnerabilityStatus["RESERVED"] = "reserved";
    VulnerabilityStatus["WITHDRAWN"] = "withdrawn";
})(VulnerabilityStatus || (exports.VulnerabilityStatus = VulnerabilityStatus = {}));
/**
 * Vulnerability Reference Types
 */
var VulnerabilityReferenceType;
(function (VulnerabilityReferenceType) {
    VulnerabilityReferenceType["ADVISORY"] = "advisory";
    VulnerabilityReferenceType["ARTICLE"] = "article";
    VulnerabilityReferenceType["EXPLOIT"] = "exploit";
    VulnerabilityReferenceType["ISSUE"] = "issue";
    VulnerabilityReferenceType["MAILING_LIST"] = "mailing_list";
    VulnerabilityReferenceType["PATCH"] = "patch";
    VulnerabilityReferenceType["REPORT"] = "report";
    VulnerabilityReferenceType["TOOL"] = "tool";
    VulnerabilityReferenceType["VENDOR"] = "vendor";
    VulnerabilityReferenceType["WEB"] = "web";
    VulnerabilityReferenceType["OTHER"] = "other";
})(VulnerabilityReferenceType || (exports.VulnerabilityReferenceType = VulnerabilityReferenceType = {}));
/**
 * Vulnerability Exploitation Status
 */
var VulnerabilityExploitationStatus;
(function (VulnerabilityExploitationStatus) {
    VulnerabilityExploitationStatus["NOT_EXPLOITABLE"] = "not_exploitable";
    VulnerabilityExploitationStatus["THEORETICAL"] = "theoretical";
    VulnerabilityExploitationStatus["PROOF_OF_CONCEPT"] = "proof_of_concept";
    VulnerabilityExploitationStatus["FUNCTIONAL"] = "functional";
    VulnerabilityExploitationStatus["HIGH"] = "high";
    VulnerabilityExploitationStatus["ACTIVE"] = "active";
    VulnerabilityExploitationStatus["UNKNOWN"] = "unknown";
})(VulnerabilityExploitationStatus || (exports.VulnerabilityExploitationStatus = VulnerabilityExploitationStatus = {}));
/**
 * Vulnerability Exploitation Complexity
 */
var VulnerabilityExploitationComplexity;
(function (VulnerabilityExploitationComplexity) {
    VulnerabilityExploitationComplexity["LOW"] = "low";
    VulnerabilityExploitationComplexity["MEDIUM"] = "medium";
    VulnerabilityExploitationComplexity["HIGH"] = "high";
    VulnerabilityExploitationComplexity["UNKNOWN"] = "unknown";
})(VulnerabilityExploitationComplexity || (exports.VulnerabilityExploitationComplexity = VulnerabilityExploitationComplexity = {}));
/**
 * Vulnerability Exploit Maturity
 */
var VulnerabilityExploitMaturity;
(function (VulnerabilityExploitMaturity) {
    VulnerabilityExploitMaturity["UNPROVEN"] = "unproven";
    VulnerabilityExploitMaturity["PROOF_OF_CONCEPT"] = "proof_of_concept";
    VulnerabilityExploitMaturity["FUNCTIONAL"] = "functional";
    VulnerabilityExploitMaturity["HIGH"] = "high";
    VulnerabilityExploitMaturity["NOT_DEFINED"] = "not_defined";
})(VulnerabilityExploitMaturity || (exports.VulnerabilityExploitMaturity = VulnerabilityExploitMaturity = {}));
/**
 * Vulnerability Remediation Status
 */
var VulnerabilityRemediationStatus;
(function (VulnerabilityRemediationStatus) {
    VulnerabilityRemediationStatus["AVAILABLE"] = "available";
    VulnerabilityRemediationStatus["PARTIAL"] = "partial";
    VulnerabilityRemediationStatus["PLANNED"] = "planned";
    VulnerabilityRemediationStatus["UNAVAILABLE"] = "unavailable";
    VulnerabilityRemediationStatus["WILL_NOT_FIX"] = "will_not_fix";
    VulnerabilityRemediationStatus["UNKNOWN"] = "unknown";
})(VulnerabilityRemediationStatus || (exports.VulnerabilityRemediationStatus = VulnerabilityRemediationStatus = {}));
/**
 * Vulnerability Fix Types
 */
var VulnerabilityFixType;
(function (VulnerabilityFixType) {
    VulnerabilityFixType["PATCH"] = "patch";
    VulnerabilityFixType["UPDATE"] = "update";
    VulnerabilityFixType["UPGRADE"] = "upgrade";
    VulnerabilityFixType["CONFIGURATION"] = "configuration";
    VulnerabilityFixType["REPLACEMENT"] = "replacement";
    VulnerabilityFixType["OTHER"] = "other";
})(VulnerabilityFixType || (exports.VulnerabilityFixType = VulnerabilityFixType = {}));
/**
 * Vulnerability Priority
 */
var VulnerabilityPriority;
(function (VulnerabilityPriority) {
    VulnerabilityPriority["CRITICAL"] = "critical";
    VulnerabilityPriority["HIGH"] = "high";
    VulnerabilityPriority["MEDIUM"] = "medium";
    VulnerabilityPriority["LOW"] = "low";
    VulnerabilityPriority["INFORMATIONAL"] = "informational";
})(VulnerabilityPriority || (exports.VulnerabilityPriority = VulnerabilityPriority = {}));
/**
 * Vulnerability Detection Methods
 */
var VulnerabilityDetectionMethod;
(function (VulnerabilityDetectionMethod) {
    VulnerabilityDetectionMethod["STATIC_ANALYSIS"] = "static_analysis";
    VulnerabilityDetectionMethod["DYNAMIC_ANALYSIS"] = "dynamic_analysis";
    VulnerabilityDetectionMethod["INTERACTIVE_ANALYSIS"] = "interactive_analysis";
    VulnerabilityDetectionMethod["MANUAL_REVIEW"] = "manual_review";
    VulnerabilityDetectionMethod["PENETRATION_TESTING"] = "penetration_testing";
    VulnerabilityDetectionMethod["AUTOMATED_SCANNING"] = "automated_scanning";
    VulnerabilityDetectionMethod["SIGNATURE_BASED"] = "signature_based";
    VulnerabilityDetectionMethod["BEHAVIORAL_ANALYSIS"] = "behavioral_analysis";
    VulnerabilityDetectionMethod["OTHER"] = "other";
})(VulnerabilityDetectionMethod || (exports.VulnerabilityDetectionMethod = VulnerabilityDetectionMethod = {}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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