09072aecbfc07239cf7d07601cbcf20f
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const event_factory_1 = require("../../domain/factories/event.factory");
const threat_factory_1 = require("../../domain/factories/threat.factory");
const vulnerability_factory_1 = require("../../domain/factories/vulnerability.factory");
const response_action_factory_1 = require("../../domain/factories/response-action.factory");
const event_metadata_value_object_1 = require("../../domain/value-objects/event-metadata/event-metadata.value-object");
const event_timestamp_value_object_1 = require("../../domain/value-objects/event-metadata/event-timestamp.value-object");
const event_source_value_object_1 = require("../../domain/value-objects/event-metadata/event-source.value-object");
const ip_address_value_object_1 = require("../../domain/value-objects/network/ip-address.value-object");
const port_value_object_1 = require("../../domain/value-objects/network/port.value-object");
const cvss_score_value_object_1 = require("../../domain/value-objects/threat-indicators/cvss-score.value-object");
const event_type_enum_1 = require("../../domain/enums/event-type.enum");
const event_severity_enum_1 = require("../../domain/enums/event-severity.enum");
const event_status_enum_1 = require("../../domain/enums/event-status.enum");
const event_source_type_enum_1 = require("../../domain/enums/event-source-type.enum");
const threat_severity_enum_1 = require("../../domain/enums/threat-severity.enum");
const vulnerability_severity_enum_1 = require("../../domain/enums/vulnerability-severity.enum");
const action_type_enum_1 = require("../../domain/enums/action-type.enum");
const action_status_enum_1 = require("../../domain/enums/action-status.enum");
const tenant_id_value_object_1 = require("../../../../shared-kernel/value-objects/tenant-id.value-object");
const user_id_value_object_1 = require("../../../../shared-kernel/value-objects/user-id.value-object");
/**
 * Mock Domain Event Handler for testing event propagation
 */
class MockDomainEventHandler {
    constructor() {
        this.handledEvents = [];
    }
    handle(event) {
        this.handledEvents.push(event);
    }
    getHandledEvents() {
        return this.handledEvents;
    }
    getEventsByType(eventType) {
        return this.handledEvents.filter(event => event.constructor.name === eventType);
    }
    clear() {
        this.handledEvents = [];
    }
}
describe('Domain Events Integration Tests', () => {
    let module;
    let eventHandler;
    beforeEach(async () => {
        eventHandler = new MockDomainEventHandler();
        module = await testing_1.Test.createTestingModule({
            providers: [
                { provide: 'DomainEventHandler', useValue: eventHandler },
            ],
        }).compile();
    });
    afterEach(async () => {
        await module.close();
    });
    describe('Event Entity Domain Events', () => {
        it('should generate EventCreated domain event when event is created', () => {
            // Arrange & Act
            const event = event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'test', version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: 'app-001',
                    name: 'Test App',
                }),
                type: event_type_enum_1.EventType.AUTHENTICATION_SUCCESS,
                severity: event_severity_enum_1.EventSeverity.LOW,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { username: 'testuser' },
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            // Assert
            const domainEvents = event.getUncommittedEvents();
            expect(domainEvents.length).toBeGreaterThan(0);
            const eventCreatedEvents = domainEvents.filter(e => e.constructor.name === 'EventCreatedDomainEvent');
            expect(eventCreatedEvents).toHaveLength(1);
            const eventCreated = eventCreatedEvents[0];
            expect(eventCreated.aggregateId.equals(event.id)).toBe(true);
            expect(eventCreated.occurredOn).toBeInstanceOf(Date);
        });
        it('should generate EventStatusChanged domain event when status changes', () => {
            // Arrange
            const event = event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'test', version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: 'app-001',
                    name: 'Test App',
                }),
                type: event_type_enum_1.EventType.SECURITY_ALERT,
                severity: event_severity_enum_1.EventSeverity.HIGH,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { alertType: 'suspicious-activity' },
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            // Clear initial events
            event.markEventsAsCommitted();
            // Act
            event.markAsProcessing();
            // Assert
            const domainEvents = event.getUncommittedEvents();
            const statusChangedEvents = domainEvents.filter(e => e.constructor.name === 'EventStatusChangedDomainEvent');
            expect(statusChangedEvents).toHaveLength(1);
            const statusChanged = statusChangedEvents[0];
            expect(statusChanged.aggregateId.equals(event.id)).toBe(true);
        });
        it('should generate multiple domain events for complex state transitions', () => {
            // Arrange
            const event = event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'firewall', version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.FIREWALL,
                    identifier: 'fw-001',
                    name: 'Firewall',
                }),
                type: event_type_enum_1.EventType.NETWORK_INTRUSION,
                severity: event_severity_enum_1.EventSeverity.CRITICAL,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { sourceIp: '*************', targetPort: 443 },
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            // Act - Simulate complete processing workflow
            event.markAsProcessing();
            event.markAsProcessed();
            // Assert
            const allEvents = event.getUncommittedEvents();
            expect(allEvents.length).toBeGreaterThan(2); // At least creation + 2 status changes
            const eventTypes = allEvents.map(e => e.constructor.name);
            expect(eventTypes).toContain('EventCreatedDomainEvent');
            expect(eventTypes).toContain('EventStatusChangedDomainEvent');
            // Should have multiple status change events
            const statusChangeEvents = allEvents.filter(e => e.constructor.name === 'EventStatusChangedDomainEvent');
            expect(statusChangeEvents.length).toBeGreaterThanOrEqual(2);
        });
    });
    describe('Threat Entity Domain Events', () => {
        it('should generate ThreatDetected domain event when threat is created', () => {
            // Arrange & Act
            const threat = threat_factory_1.ThreatFactory.create({
                signature: 'malware-signature-001',
                score: cvss_score_value_object_1.CvssScore.create(8.5),
                severity: threat_severity_enum_1.ThreatSeverity.HIGH,
                confidence: 90,
                sourceIp: ip_address_value_object_1.IpAddress.create('*************'),
                indicators: ['suspicious-hash', 'malicious-domain'],
                mitreTechniques: ['T1190', 'T1055'],
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            // Assert
            const domainEvents = threat.getUncommittedEvents();
            expect(domainEvents.length).toBeGreaterThan(0);
            const threatDetectedEvents = domainEvents.filter(e => e.constructor.name === 'ThreatDetectedDomainEvent');
            expect(threatDetectedEvents).toHaveLength(1);
            const threatDetected = threatDetectedEvents[0];
            expect(threatDetected.aggregateId.equals(threat.id)).toBe(true);
        });
        it('should generate ThreatMitigated domain event when threat is mitigated', () => {
            // Arrange
            const threat = threat_factory_1.ThreatFactory.create({
                signature: 'threat-signature-001',
                score: cvss_score_value_object_1.CvssScore.create(7.0),
                severity: threat_severity_enum_1.ThreatSeverity.MEDIUM,
                confidence: 85,
                sourceIp: ip_address_value_object_1.IpAddress.create('********'),
                indicators: ['ioc-001'],
                mitreTechniques: ['T1001'],
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            // Clear initial events
            threat.markEventsAsCommitted();
            // Act
            threat.markAsMitigated();
            // Assert
            const domainEvents = threat.getUncommittedEvents();
            const mitigatedEvents = domainEvents.filter(e => e.constructor.name === 'ThreatMitigatedDomainEvent');
            expect(mitigatedEvents).toHaveLength(1);
            const mitigated = mitigatedEvents[0];
            expect(mitigated.aggregateId.equals(threat.id)).toBe(true);
        });
    });
    describe('Vulnerability Entity Domain Events', () => {
        it('should generate VulnerabilityFound domain event when vulnerability is created', () => {
            // Arrange & Act
            const vulnerability = vulnerability_factory_1.VulnerabilityFactory.create({
                cveId: 'CVE-2023-1234',
                score: cvss_score_value_object_1.CvssScore.create(9.0),
                severity: vulnerability_severity_enum_1.VulnerabilitySeverity.CRITICAL,
                affectedPort: port_value_object_1.Port.create(80),
                description: 'Critical remote code execution vulnerability',
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            // Assert
            const domainEvents = vulnerability.getUncommittedEvents();
            expect(domainEvents.length).toBeGreaterThan(0);
            const vulnFoundEvents = domainEvents.filter(e => e.constructor.name === 'VulnerabilityFoundDomainEvent');
            expect(vulnFoundEvents).toHaveLength(1);
            const vulnFound = vulnFoundEvents[0];
            expect(vulnFound.aggregateId.equals(vulnerability.id)).toBe(true);
        });
        it('should generate VulnerabilityStatusChanged domain event when status changes', () => {
            // Arrange
            const vulnerability = vulnerability_factory_1.VulnerabilityFactory.create({
                cveId: 'CVE-2023-5678',
                score: cvss_score_value_object_1.CvssScore.create(6.5),
                severity: vulnerability_severity_enum_1.VulnerabilitySeverity.MEDIUM,
                affectedPort: port_value_object_1.Port.create(443),
                description: 'Medium severity vulnerability',
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            // Clear initial events
            vulnerability.markEventsAsCommitted();
            // Act
            vulnerability.markAsPatched();
            // Assert
            const domainEvents = vulnerability.getUncommittedEvents();
            const statusChangedEvents = domainEvents.filter(e => e.constructor.name === 'VulnerabilityStatusChangedEvent');
            expect(statusChangedEvents).toHaveLength(1);
        });
    });
    describe('ResponseAction Entity Domain Events', () => {
        it('should generate ResponseActionCreated domain event when action is created', () => {
            // Arrange & Act
            const responseAction = response_action_factory_1.ResponseActionFactory.create({
                type: action_type_enum_1.ActionType.BLOCK_IP,
                parameters: { ipAddress: '*************', duration: 3600 },
                priority: 1,
                status: action_status_enum_1.ActionStatus.PENDING,
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            // Assert
            const domainEvents = responseAction.getUncommittedEvents();
            expect(domainEvents.length).toBeGreaterThan(0);
            const actionCreatedEvents = domainEvents.filter(e => e.constructor.name === 'ResponseActionCreatedDomainEvent');
            expect(actionCreatedEvents).toHaveLength(1);
            const actionCreated = actionCreatedEvents[0];
            expect(actionCreated.aggregateId.equals(responseAction.id)).toBe(true);
        });
        it('should generate ResponseActionExecuted domain event when action is executed', () => {
            // Arrange
            const responseAction = response_action_factory_1.ResponseActionFactory.create({
                type: action_type_enum_1.ActionType.ISOLATE_HOST,
                parameters: { hostId: 'host-001', reason: 'malware-detected' },
                priority: 1,
                status: action_status_enum_1.ActionStatus.PENDING,
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            // Clear initial events
            responseAction.markEventsAsCommitted();
            // Act
            responseAction.execute();
            // Assert
            const domainEvents = responseAction.getUncommittedEvents();
            const executedEvents = domainEvents.filter(e => e.constructor.name === 'ResponseActionExecutedDomainEvent');
            expect(executedEvents).toHaveLength(1);
            const executed = executedEvents[0];
            expect(executed.aggregateId.equals(responseAction.id)).toBe(true);
        });
        it('should generate ResponseActionStatusChanged domain event when status changes', () => {
            // Arrange
            const responseAction = response_action_factory_1.ResponseActionFactory.create({
                type: action_type_enum_1.ActionType.QUARANTINE_FILE,
                parameters: { filePath: '/tmp/malware.exe', reason: 'malware-detected' },
                priority: 2,
                status: action_status_enum_1.ActionStatus.PENDING,
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            // Clear initial events
            responseAction.markEventsAsCommitted();
            // Act
            responseAction.execute();
            responseAction.complete();
            // Assert
            const domainEvents = responseAction.getUncommittedEvents();
            const statusChangedEvents = domainEvents.filter(e => e.constructor.name === 'ResponseActionStatusChangedDomainEvent');
            expect(statusChangedEvents.length).toBeGreaterThanOrEqual(2); // Execute + Complete
        });
        it('should generate ResponseActionFailed domain event when action fails', () => {
            // Arrange
            const responseAction = response_action_factory_1.ResponseActionFactory.create({
                type: action_type_enum_1.ActionType.RESTART_SERVICE,
                parameters: { serviceName: 'test-service' },
                priority: 3,
                status: action_status_enum_1.ActionStatus.PENDING,
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            // Clear initial events
            responseAction.markEventsAsCommitted();
            // Act
            responseAction.execute();
            responseAction.fail('Service not found');
            // Assert
            const domainEvents = responseAction.getUncommittedEvents();
            const failedEvents = domainEvents.filter(e => e.constructor.name === 'ResponseActionFailedDomainEvent');
            expect(failedEvents).toHaveLength(1);
            const failed = failedEvents[0];
            expect(failed.aggregateId.equals(responseAction.id)).toBe(true);
        });
        it('should generate ResponseActionRolledBack domain event when action is rolled back', () => {
            // Arrange
            const responseAction = response_action_factory_1.ResponseActionFactory.create({
                type: action_type_enum_1.ActionType.BLOCK_IP,
                parameters: { ipAddress: '********', duration: 1800 },
                priority: 1,
                status: action_status_enum_1.ActionStatus.COMPLETED,
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            // Clear initial events
            responseAction.markEventsAsCommitted();
            // Act
            responseAction.rollback();
            // Assert
            const domainEvents = responseAction.getUncommittedEvents();
            const rolledBackEvents = domainEvents.filter(e => e.constructor.name === 'ResponseActionRolledBackDomainEvent');
            expect(rolledBackEvents).toHaveLength(1);
            const rolledBack = rolledBackEvents[0];
            expect(rolledBack.aggregateId.equals(responseAction.id)).toBe(true);
        });
    });
    describe('Event Propagation Workflow', () => {
        it('should propagate events through complete security workflow', () => {
            // Arrange
            const tenantId = tenant_id_value_object_1.TenantId.create();
            const userId = user_id_value_object_1.UserId.create();
            // Act - Create entities that would be part of a security workflow
            const event = event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'ids', version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.IDS,
                    identifier: 'ids-001',
                    name: 'Intrusion Detection System',
                }),
                type: event_type_enum_1.EventType.NETWORK_INTRUSION,
                severity: event_severity_enum_1.EventSeverity.HIGH,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { sourceIp: '*************', targetPort: 443 },
                tenantId,
                userId,
            });
            const threat = threat_factory_1.ThreatFactory.create({
                signature: 'network-intrusion-001',
                score: cvss_score_value_object_1.CvssScore.create(8.0),
                severity: threat_severity_enum_1.ThreatSeverity.HIGH,
                confidence: 95,
                sourceIp: ip_address_value_object_1.IpAddress.create('*************'),
                indicators: ['suspicious-traffic-pattern'],
                mitreTechniques: ['T1190'],
                tenantId,
                userId,
            });
            const responseAction = response_action_factory_1.ResponseActionFactory.create({
                type: action_type_enum_1.ActionType.BLOCK_IP,
                parameters: { ipAddress: '*************', duration: 3600 },
                priority: 1,
                status: action_status_enum_1.ActionStatus.PENDING,
                tenantId,
                userId,
            });
            // Assert - Collect all domain events from the workflow
            const allDomainEvents = [
                ...event.getUncommittedEvents(),
                ...threat.getUncommittedEvents(),
                ...responseAction.getUncommittedEvents(),
            ];
            expect(allDomainEvents.length).toBeGreaterThan(0);
            // Verify we have events from each entity type
            const eventTypes = allDomainEvents.map(e => e.constructor.name);
            expect(eventTypes).toContain('EventCreatedDomainEvent');
            expect(eventTypes).toContain('ThreatDetectedDomainEvent');
            expect(eventTypes).toContain('ResponseActionCreatedDomainEvent');
            // Verify all events have proper correlation
            allDomainEvents.forEach(domainEvent => {
                expect(domainEvent.occurredOn).toBeInstanceOf(Date);
                expect(domainEvent.eventId).toBeDefined();
                expect(domainEvent.aggregateId).toBeDefined();
            });
        });
        it('should handle event ordering and timing correctly', () => {
            // Arrange
            const startTime = new Date();
            // Act - Create entities with slight delays to test ordering
            const event1 = event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'source1', version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: 'app-001',
                    name: 'App 1',
                }),
                type: event_type_enum_1.EventType.AUTHENTICATION_FAILURE,
                severity: event_severity_enum_1.EventSeverity.MEDIUM,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { username: 'user1' },
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            // Small delay
            const event2 = event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'source2', version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.APPLICATION,
                    identifier: 'app-002',
                    name: 'App 2',
                }),
                type: event_type_enum_1.EventType.AUTHENTICATION_FAILURE,
                severity: event_severity_enum_1.EventSeverity.MEDIUM,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { username: 'user2' },
                tenantId: tenant_id_value_object_1.TenantId.create(),
                userId: user_id_value_object_1.UserId.create(),
            });
            // Assert - Verify event timing
            const events1 = event1.getUncommittedEvents();
            const events2 = event2.getUncommittedEvents();
            expect(events1.length).toBeGreaterThan(0);
            expect(events2.length).toBeGreaterThan(0);
            // All events should have occurred after start time
            [...events1, ...events2].forEach(domainEvent => {
                expect(domainEvent.occurredOn.getTime()).toBeGreaterThanOrEqual(startTime.getTime());
            });
            // Events from event2 should have occurred after or at the same time as events from event1
            const event1Times = events1.map(e => e.occurredOn.getTime());
            const event2Times = events2.map(e => e.occurredOn.getTime());
            const maxEvent1Time = Math.max(...event1Times);
            const minEvent2Time = Math.min(...event2Times);
            expect(minEvent2Time).toBeGreaterThanOrEqual(maxEvent1Time);
        });
    });
    describe('Event Correlation and Causality', () => {
        it('should maintain causal relationships between related domain events', () => {
            // Arrange
            const tenantId = tenant_id_value_object_1.TenantId.create();
            const userId = user_id_value_object_1.UserId.create();
            // Act - Create a sequence of related events
            const securityEvent = event_factory_1.EventFactory.create({
                metadata: event_metadata_value_object_1.EventMetadata.create({ source: 'firewall', version: '1.0' }),
                timestamp: event_timestamp_value_object_1.EventTimestamp.create(new Date()),
                source: event_source_value_object_1.EventSource.create({
                    type: event_source_type_enum_1.EventSourceType.FIREWALL,
                    identifier: 'fw-001',
                    name: 'Firewall',
                }),
                type: event_type_enum_1.EventType.NETWORK_INTRUSION,
                severity: event_severity_enum_1.EventSeverity.CRITICAL,
                status: event_status_enum_1.EventStatus.RECEIVED,
                payload: { sourceIp: '*************' },
                tenantId,
                userId,
            });
            // Process the event
            securityEvent.markAsProcessing();
            securityEvent.markAsProcessed();
            // Create threat based on the event
            const threat = threat_factory_1.ThreatFactory.create({
                signature: 'intrusion-threat-001',
                score: cvss_score_value_object_1.CvssScore.create(9.0),
                severity: threat_severity_enum_1.ThreatSeverity.CRITICAL,
                confidence: 98,
                sourceIp: ip_address_value_object_1.IpAddress.create('*************'),
                indicators: ['network-intrusion-pattern'],
                mitreTechniques: ['T1190'],
                tenantId,
                userId,
            });
            // Create response action based on the threat
            const responseAction = response_action_factory_1.ResponseActionFactory.create({
                type: action_type_enum_1.ActionType.BLOCK_IP,
                parameters: { ipAddress: '*************', duration: 7200 },
                priority: 1,
                status: action_status_enum_1.ActionStatus.PENDING,
                tenantId,
                userId,
            });
            // Execute the response action
            responseAction.execute();
            responseAction.complete();
            // Assert - Verify causal chain through domain events
            const eventDomainEvents = securityEvent.getUncommittedEvents();
            const threatDomainEvents = threat.getUncommittedEvents();
            const actionDomainEvents = responseAction.getUncommittedEvents();
            // Verify temporal ordering
            const allEvents = [...eventDomainEvents, ...threatDomainEvents, ...actionDomainEvents];
            const sortedEvents = allEvents.sort((a, b) => a.occurredOn.getTime() - b.occurredOn.getTime());
            // First events should be from the security event
            const firstEventType = sortedEvents[0].constructor.name;
            expect(firstEventType).toBe('EventCreatedDomainEvent');
            // Should have proper sequence of events
            const eventTypeSequence = sortedEvents.map(e => e.constructor.name);
            expect(eventTypeSequence).toContain('EventCreatedDomainEvent');
            expect(eventTypeSequence).toContain('EventStatusChangedDomainEvent');
            expect(eventTypeSequence).toContain('ThreatDetectedDomainEvent');
            expect(eventTypeSequence).toContain('ResponseActionCreatedDomainEvent');
            expect(eventTypeSequence).toContain('ResponseActionExecutedDomainEvent');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************