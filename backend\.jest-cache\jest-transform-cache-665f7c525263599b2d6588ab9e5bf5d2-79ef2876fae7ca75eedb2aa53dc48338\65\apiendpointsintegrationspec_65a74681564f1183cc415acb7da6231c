224691bf739dbfc81add358d8d5b79ca
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const common_1 = require("@nestjs/common");
const base_test_class_1 = require("../base/base-test.class");
const reporting_module_1 = require("../../reporting.module");
const background_processors_module_1 = require("../../background-processors/background-processors.module");
const monitoring_module_1 = require("../../monitoring/monitoring.module");
const report_definition_entity_1 = require("../../entities/report-definition.entity");
const generated_report_entity_1 = require("../../entities/generated-report.entity");
const dashboard_entity_1 = require("../../entities/dashboard.entity");
const job_execution_entity_1 = require("../../background-processors/entities/job-execution.entity");
const metric_entity_1 = require("../../monitoring/entities/metric.entity");
const health_check_entity_1 = require("../../monitoring/entities/health-check.entity");
const alert_entity_1 = require("../../monitoring/entities/alert.entity");
/**
 * API Endpoints Integration Tests
 *
 * Comprehensive API endpoint testing providing:
 * - Request/response validation with comprehensive schema testing
 * - Authentication and authorization testing with role-based access control
 * - Error handling verification with proper HTTP status codes
 * - Pagination, filtering, and sorting functionality validation
 * - Input validation and sanitization testing
 * - Rate limiting and security controls verification
 * - Performance testing with response time benchmarking
 * - Cross-endpoint integration and workflow validation
 */
describe('API Endpoints Integration Tests', () => {
    let app;
    let testingModule;
    let baseTest;
    // Test entities
    const testEntities = [
        report_definition_entity_1.ReportDefinition,
        generated_report_entity_1.GeneratedReport,
        dashboard_entity_1.Dashboard,
        job_execution_entity_1.JobExecution,
        metric_entity_1.Metric,
        health_check_entity_1.HealthCheck,
        alert_entity_1.Alert,
    ];
    beforeAll(async () => {
        // Create base test instance
        baseTest = new (class extends base_test_class_1.BaseTestClass {
        })();
        // Setup test environment
        await baseTest.setupTestEnvironment({
            imports: [
                reporting_module_1.ReportingModule,
                background_processors_module_1.BackgroundProcessorsModule,
                monitoring_module_1.MonitoringModule,
            ],
        }, testEntities);
        app = baseTest.app;
        testingModule = baseTest.testingModule;
    });
    afterAll(async () => {
        await baseTest.cleanupTestEnvironment();
    });
    beforeEach(async () => {
        await baseTest.setupTestData();
    });
    afterEach(async () => {
        await baseTest.cleanupTestData();
    });
    describe('Report Definition Endpoints', () => {
        describe('POST /reporting/report-definitions', () => {
            it('should create a new report definition with valid data', async () => {
                // Arrange
                const reportData = {
                    name: 'Test Security Report',
                    description: 'A comprehensive security analysis report',
                    type: 'dashboard',
                    category: 'security',
                    configuration: {
                        dataSource: 'database',
                        refreshInterval: 3600,
                        filters: {
                            dateRange: {
                                start: '2024-01-01T00:00:00Z',
                                end: '2024-12-31T23:59:59Z',
                            },
                            categories: ['security', 'compliance'],
                        },
                        visualization: {
                            type: 'dashboard',
                            layout: 'grid',
                            theme: 'default',
                        },
                    },
                    schedule: {
                        enabled: true,
                        frequency: 'daily',
                        time: '09:00',
                        timezone: 'UTC',
                        recipients: ['<EMAIL>'],
                    },
                };
                // Act
                const response = await baseTest.makeAuthenticatedRequest('post', '/reporting/report-definitions', reportData);
                // Assert
                expect(response.status).toBe(common_1.HttpStatus.CREATED);
                expect(response.body).toMatchObject({
                    id: expect.any(String),
                    name: reportData.name,
                    description: reportData.description,
                    type: reportData.type,
                    category: reportData.category,
                    isActive: true,
                    createdBy: baseTest.testUser.id,
                });
                baseTest.assertResponseStructure(response.body, {
                    id: 'string',
                    name: 'string',
                    description: 'string',
                    type: 'string',
                    category: 'string',
                    configuration: 'object',
                    schedule: 'object',
                    isActive: 'boolean',
                    createdBy: 'string',
                    createdAt: 'string',
                    updatedAt: 'string',
                });
                // Verify database persistence
                await baseTest.assertDatabaseState(report_definition_entity_1.ReportDefinition, { name: reportData.name }, 1);
            });
            it('should return 400 for invalid report definition data', async () => {
                // Arrange
                const invalidData = {
                    name: '', // Invalid: empty name
                    type: 'invalid_type', // Invalid: not in enum
                    configuration: 'invalid', // Invalid: should be object
                };
                // Act
                const response = await baseTest.makeAuthenticatedRequest('post', '/reporting/report-definitions', invalidData);
                // Assert
                expect(response.status).toBe(common_1.HttpStatus.BAD_REQUEST);
                expect(response.body).toMatchObject({
                    statusCode: 400,
                    message: expect.any(Array),
                    error: 'Bad Request',
                });
            });
            it('should return 401 for unauthenticated requests', async () => {
                // Arrange
                const reportData = {
                    name: 'Unauthorized Report',
                    type: 'dashboard',
                    category: 'security',
                };
                // Act
                const response = await baseTest.makeUnauthenticatedRequest('post', '/reporting/report-definitions', reportData);
                // Assert
                expect(response.status).toBe(common_1.HttpStatus.UNAUTHORIZED);
            });
        });
        describe('GET /reporting/report-definitions', () => {
            it('should return paginated list of report definitions', async () => {
                // Arrange
                const reportDefinitions = await Promise.all([
                    baseTest.createTestReportDefinition({ name: 'Report 1', category: 'security' }),
                    baseTest.createTestReportDefinition({ name: 'Report 2', category: 'compliance' }),
                    baseTest.createTestReportDefinition({ name: 'Report 3', category: 'performance' }),
                ]);
                // Act
                const response = await baseTest.makeAuthenticatedRequest('get', '/reporting/report-definitions?page=1&limit=2&sortBy=name&sortOrder=asc');
                // Assert
                expect(response.status).toBe(common_1.HttpStatus.OK);
                expect(response.body).toMatchObject({
                    data: expect.any(Array),
                    pagination: {
                        page: 1,
                        limit: 2,
                        total: expect.any(Number),
                        totalPages: expect.any(Number),
                    },
                });
                expect(response.body.data).toHaveLength(2);
                expect(response.body.data[0].name).toBe('Report 1');
                expect(response.body.data[1].name).toBe('Report 2');
            });
            it('should filter report definitions by category', async () => {
                // Arrange
                await Promise.all([
                    baseTest.createTestReportDefinition({ category: 'security' }),
                    baseTest.createTestReportDefinition({ category: 'security' }),
                    baseTest.createTestReportDefinition({ category: 'compliance' }),
                ]);
                // Act
                const response = await baseTest.makeAuthenticatedRequest('get', '/reporting/report-definitions?category=security');
                // Assert
                expect(response.status).toBe(common_1.HttpStatus.OK);
                expect(response.body.data).toHaveLength(2);
                expect(response.body.data.every((report) => report.category === 'security')).toBe(true);
            });
            it('should search report definitions by name', async () => {
                // Arrange
                await Promise.all([
                    baseTest.createTestReportDefinition({ name: 'Security Analysis Report' }),
                    baseTest.createTestReportDefinition({ name: 'Performance Dashboard' }),
                    baseTest.createTestReportDefinition({ name: 'Security Compliance Report' }),
                ]);
                // Act
                const response = await baseTest.makeAuthenticatedRequest('get', '/reporting/report-definitions?search=Security');
                // Assert
                expect(response.status).toBe(common_1.HttpStatus.OK);
                expect(response.body.data).toHaveLength(2);
                expect(response.body.data.every((report) => report.name.toLowerCase().includes('security'))).toBe(true);
            });
        });
        describe('GET /reporting/report-definitions/:id', () => {
            it('should return specific report definition', async () => {
                // Arrange
                const reportDefinition = await baseTest.createTestReportDefinition({
                    name: 'Detailed Report',
                    description: 'A detailed test report',
                });
                // Act
                const response = await baseTest.makeAuthenticatedRequest('get', `/reporting/report-definitions/${reportDefinition.id}`);
                // Assert
                expect(response.status).toBe(common_1.HttpStatus.OK);
                expect(response.body).toMatchObject({
                    id: reportDefinition.id,
                    name: 'Detailed Report',
                    description: 'A detailed test report',
                });
            });
            it('should return 404 for non-existent report definition', async () => {
                // Act
                const response = await baseTest.makeAuthenticatedRequest('get', '/reporting/report-definitions/non-existent-id');
                // Assert
                expect(response.status).toBe(common_1.HttpStatus.NOT_FOUND);
            });
        });
    });
    describe('Background Processors Endpoints', () => {
        describe('POST /background-processors/jobs', () => {
            it('should add job to queue successfully', async () => {
                // Arrange
                const jobData = {
                    queueName: 'workflow-execution',
                    jobType: 'execute-workflow',
                    jobData: {
                        workflowId: 'test-workflow',
                        templateId: 'template-123',
                        inputData: { param: 'value' },
                    },
                    priority: 10,
                };
                // Act
                const response = await baseTest.makeAuthenticatedRequest('post', '/background-processors/jobs', jobData);
                // Assert
                expect(response.status).toBe(common_1.HttpStatus.CREATED);
                expect(response.body).toMatchObject({
                    jobExecutionId: expect.any(String),
                    jobId: expect.any(String),
                    queueName: 'workflow-execution',
                    jobType: 'execute-workflow',
                    status: 'pending',
                    priority: 10,
                });
                // Verify database persistence
                await baseTest.assertDatabaseState(job_execution_entity_1.JobExecution, { jobType: 'execute-workflow' }, 1);
            });
            it('should validate job data structure', async () => {
                // Arrange
                const invalidJobData = {
                    queueName: 'invalid-queue',
                    jobType: '', // Invalid: empty
                    // Missing jobData
                };
                // Act
                const response = await baseTest.makeAuthenticatedRequest('post', '/background-processors/jobs', invalidJobData);
                // Assert
                expect(response.status).toBe(common_1.HttpStatus.BAD_REQUEST);
            });
        });
        describe('GET /background-processors/jobs/:id', () => {
            it('should return job execution status', async () => {
                // Arrange
                const jobExecution = await baseTest.createTestJobExecution({
                    status: 'active',
                    progress: {
                        currentStep: 2,
                        totalSteps: 5,
                        percentage: 40,
                        message: 'Processing step 2 of 5',
                        details: {},
                        steps: [],
                    },
                });
                // Act
                const response = await baseTest.makeAuthenticatedRequest('get', `/background-processors/jobs/${jobExecution.id}`);
                // Assert
                expect(response.status).toBe(common_1.HttpStatus.OK);
                expect(response.body).toMatchObject({
                    job: {
                        id: jobExecution.id,
                        status: 'active',
                        progress: {
                            currentStep: 2,
                            totalSteps: 5,
                            percentage: 40,
                            message: 'Processing step 2 of 5',
                        },
                    },
                    metrics: expect.any(Object),
                });
            });
        });
        describe('GET /background-processors/health', () => {
            it('should return system health status', async () => {
                // Act
                const response = await baseTest.makeAuthenticatedRequest('get', '/background-processors/health');
                // Assert
                expect(response.status).toBe(common_1.HttpStatus.OK);
                expect(response.body).toMatchObject({
                    health: {
                        status: expect.stringMatching(/^(healthy|degraded|unhealthy)$/),
                        queues: expect.any(Number),
                        healthyQueues: expect.any(Number),
                        degradedQueues: expect.any(Number),
                        unhealthyQueues: expect.any(Number),
                        totalJobs: expect.any(Number),
                        activeJobs: expect.any(Number),
                        failedJobs: expect.any(Number),
                        issues: expect.any(Array),
                    },
                    queues: expect.any(Array),
                    timestamp: expect.any(String),
                });
            });
        });
    });
    describe('Monitoring Endpoints', () => {
        describe('POST /monitoring/metrics', () => {
            it('should record metric successfully', async () => {
                // Arrange
                const metricData = {
                    name: 'test_metric',
                    value: 42.5,
                    category: 'performance',
                    type: 'gauge',
                    unit: 'seconds',
                    labels: {
                        service: 'api',
                        environment: 'test',
                    },
                    source: 'test_suite',
                };
                // Act
                const response = await baseTest.makeAuthenticatedRequest('post', '/monitoring/metrics', metricData);
                // Assert
                expect(response.status).toBe(common_1.HttpStatus.CREATED);
                expect(response.body).toMatchObject({
                    id: expect.any(String),
                    name: 'test_metric',
                    value: 42.5,
                    category: 'performance',
                    type: 'gauge',
                    recorded: true,
                });
                // Verify database persistence
                await baseTest.assertDatabaseState(metric_entity_1.Metric, { name: 'test_metric' }, 1);
            });
        });
        describe('GET /monitoring/metrics', () => {
            it('should return filtered metrics', async () => {
                // Arrange
                await Promise.all([
                    baseTest.createTestMetric({ name: 'cpu_usage', category: 'system' }),
                    baseTest.createTestMetric({ name: 'memory_usage', category: 'system' }),
                    baseTest.createTestMetric({ name: 'response_time', category: 'performance' }),
                ]);
                // Act
                const response = await baseTest.makeAuthenticatedRequest('get', '/monitoring/metrics?category=system&limit=10');
                // Assert
                expect(response.status).toBe(common_1.HttpStatus.OK);
                expect(response.body).toMatchObject({
                    data: expect.any(Array),
                    pagination: expect.any(Object),
                });
                expect(response.body.data).toHaveLength(2);
                expect(response.body.data.every((metric) => metric.category === 'system')).toBe(true);
            });
        });
        describe('GET /monitoring/health', () => {
            it('should return overall system health', async () => {
                // Act
                const response = await baseTest.makeAuthenticatedRequest('get', '/monitoring/health');
                // Assert
                expect(response.status).toBe(common_1.HttpStatus.OK);
                expect(response.body).toMatchObject({
                    status: expect.stringMatching(/^(healthy|degraded|unhealthy)$/),
                    checks: expect.any(Array),
                    timestamp: expect.any(String),
                    uptime: expect.any(Number),
                });
            });
        });
    });
    describe('Performance Testing', () => {
        it('should handle concurrent API requests', async () => {
            // Arrange
            const concurrentRequests = 20;
            const reportData = {
                name: 'Concurrent Test Report',
                type: 'dashboard',
                category: 'performance',
            };
            // Act
            const { result: responses, executionTime } = await baseTest.measureExecutionTime(async () => {
                const promises = Array.from({ length: concurrentRequests }, (_, i) => baseTest.makeAuthenticatedRequest('post', '/reporting/report-definitions', { ...reportData, name: `${reportData.name} ${i}` }));
                return await Promise.all(promises);
            });
            // Assert
            expect(responses).toHaveLength(concurrentRequests);
            expect(responses.every(response => response.status === common_1.HttpStatus.CREATED)).toBe(true);
            expect(executionTime).toBeLessThan(10000); // Should complete within 10 seconds
            console.log(`Concurrent API Requests (${concurrentRequests} requests):`, {
                totalTime: `${executionTime}ms`,
                averageTimePerRequest: `${executionTime / concurrentRequests}ms`,
                requestsPerSecond: Math.round((concurrentRequests / executionTime) * 1000),
            });
        });
        it('should meet response time requirements', async () => {
            // Arrange
            const reportDefinition = await baseTest.createTestReportDefinition();
            // Act
            const benchmark = await baseTest.runPerformanceBenchmark(async () => {
                const response = await baseTest.makeAuthenticatedRequest('get', `/reporting/report-definitions/${reportDefinition.id}`);
                expect(response.status).toBe(common_1.HttpStatus.OK);
                return response;
            }, 50);
            // Assert
            expect(benchmark.averageTime).toBeLessThan(500); // Average under 500ms
            expect(benchmark.maxTime).toBeLessThan(1000); // Max under 1 second
            expect(benchmark.minTime).toBeGreaterThan(0);
            console.log('API Response Time Benchmark:', {
                averageTime: `${benchmark.averageTime}ms`,
                minTime: `${benchmark.minTime}ms`,
                maxTime: `${benchmark.maxTime}ms`,
                iterations: benchmark.iterations,
            });
        });
    });
    describe('Error Handling', () => {
        it('should handle malformed JSON requests', async () => {
            // Act
            const response = await baseTest.makeAuthenticatedRequest('post', '/reporting/report-definitions', undefined, { 'Content-Type': 'application/json' }).send('{ invalid json }');
            // Assert
            expect(response.status).toBe(common_1.HttpStatus.BAD_REQUEST);
            expect(response.body).toMatchObject({
                statusCode: 400,
                error: 'Bad Request',
            });
        });
        it('should handle large request payloads', async () => {
            // Arrange
            const largeData = {
                name: 'Large Report',
                type: 'dashboard',
                category: 'performance',
                configuration: {
                    largeArray: Array.from({ length: 10000 }, (_, i) => ({
                        id: i,
                        data: `Large data item ${i}`.repeat(100),
                    })),
                },
            };
            // Act
            const response = await baseTest.makeAuthenticatedRequest('post', '/reporting/report-definitions', largeData);
            // Assert
            // Should either succeed or return appropriate error for payload size
            expect([common_1.HttpStatus.CREATED, common_1.HttpStatus.REQUEST_ENTITY_TOO_LARGE])
                .toContain(response.status);
        });
        it('should handle database connection errors gracefully', async () => {
            // Note: This would require mocking database connection failures
            // Implementation depends on specific database mocking strategy
            // For now, verify that the endpoint structure is correct
            const response = await baseTest.makeAuthenticatedRequest('get', '/reporting/report-definitions');
            expect([common_1.HttpStatus.OK, common_1.HttpStatus.INTERNAL_SERVER_ERROR])
                .toContain(response.status);
        });
    });
    describe('Security Testing', () => {
        it('should enforce role-based access control', async () => {
            // This test would require setting up different user roles
            // and testing access restrictions
            // For now, verify that authentication is required
            const response = await baseTest.makeUnauthenticatedRequest('delete', '/reporting/report-definitions/test-id');
            expect(response.status).toBe(common_1.HttpStatus.UNAUTHORIZED);
        });
        it('should sanitize input data', async () => {
            // Arrange
            const maliciousData = {
                name: '<script>alert("xss")</script>',
                description: 'DROP TABLE users; --',
                type: 'dashboard',
                category: 'security',
            };
            // Act
            const response = await baseTest.makeAuthenticatedRequest('post', '/reporting/report-definitions', maliciousData);
            // Assert
            if (response.status === common_1.HttpStatus.CREATED) {
                expect(response.body.name).not.toContain('<script>');
                expect(response.body.description).not.toContain('DROP TABLE');
            }
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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