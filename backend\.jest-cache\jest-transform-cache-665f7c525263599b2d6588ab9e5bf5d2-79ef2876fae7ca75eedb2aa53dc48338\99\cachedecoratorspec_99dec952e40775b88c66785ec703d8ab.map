{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\decorators\\cache.decorator.spec.ts", "mappings": ";;;;;;;;;;;AAAA,sEAAyF;AACzF,kEAAsE;AAEtE,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;IAC/B,IAAI,aAAoC,CAAC;IAEzC,UAAU,CAAC,GAAG,EAAE;QACd,aAAa,GAAG,IAAI,sCAAqB,CAAC;YACxC,UAAU,EAAE,KAAK,EAAE,WAAW;YAC9B,OAAO,EAAE,GAAG;SACb,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,aAAa,CAAC,OAAO,EAAE,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;;YAC3C,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,MAAM,WAAW;gBAAjB;oBACE,kBAAa,GAAG,aAAa,CAAC;gBAOhC,CAAC;gBAJO,AAAN,KAAK,CAAC,kBAAkB,CAAC,KAAa;oBACpC,SAAS,EAAE,CAAC;oBACZ,OAAO,UAAU,KAAK,IAAI,SAAS,EAAE,CAAC;gBACxC,CAAC;aACF;YAJO;gBADL,IAAA,uBAAK,EAAC,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;;;oEACmB,OAAO,oBAAP,OAAO;iEAG/C;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAElC,mCAAmC;YACnC,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;YACzD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACtC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE1B,0CAA0C;YAC1C,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;YACzD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,4BAA4B;YACnE,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,0BAA0B;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,KAAK,IAAI,EAAE;;YACvE,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,MAAM,WAAW;gBAAjB;oBACE,kBAAa,GAAG,aAAa,CAAC;gBAOhC,CAAC;gBAJO,AAAN,KAAK,CAAC,cAAc,CAAC,IAAY,EAAE,IAAY;oBAC7C,SAAS,EAAE,CAAC;oBACZ,OAAO,GAAG,IAAI,IAAI,IAAI,IAAI,SAAS,EAAE,CAAC;gBACxC,CAAC;aACF;YAJO;gBADL,IAAA,uBAAK,GAAE;;;oEAC0C,OAAO,oBAAP,OAAO;6DAGxD;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAElC,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACrD,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACrD,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,qBAAqB;YAE3E,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9B,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9B,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,gBAAgB;YAC/C,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,wBAAwB;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;;YAC/C,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,MAAM,WAAW;gBAAjB;oBACE,kBAAa,GAAG,aAAa,CAAC;gBAUhC,CAAC;gBAJO,AAAN,KAAK,CAAC,eAAe,CAAC,EAAU,EAAE,IAAS;oBACzC,SAAS,EAAE,CAAC;oBACZ,OAAO,UAAU,EAAE,IAAI,SAAS,EAAE,CAAC;gBACrC,CAAC;aACF;YAJO;gBAJL,IAAA,uBAAK,EAAC;oBACL,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC,EAAE;oBAClC,GAAG,EAAE,KAAK;iBACX,CAAC;;;oEAC4C,OAAO,oBAAP,OAAO;8DAGpD;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAElC,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;YACzE,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC,0BAA0B;YAEpG,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACrC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,uCAAuC;YAC7E,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;;YACtE,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,MAAM,WAAW;gBACf,oBAAoB;gBAGd,AAAN,KAAK,CAAC,cAAc,CAAC,KAAa;oBAChC,SAAS,EAAE,CAAC;oBACZ,OAAO,UAAU,KAAK,IAAI,SAAS,EAAE,CAAC;gBACxC,CAAC;aACF;YAJO;gBADL,IAAA,uBAAK,GAAE;;;oEAC6B,OAAO,oBAAP,OAAO;6DAG3C;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAElC,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACrD,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAErD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACtC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,aAAa;YACpD,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;;YACrD,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,MAAM,YAAY,GAAG;gBACnB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;gBAC/D,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;aAChE,CAAC;YAEF,MAAM,WAAW;gBAAjB;oBACE,kBAAa,GAAG,YAAY,CAAC;gBAO/B,CAAC;gBAJO,AAAN,KAAK,CAAC,sBAAsB,CAAC,KAAa;oBACxC,SAAS,EAAE,CAAC;oBACZ,OAAO,UAAU,KAAK,IAAI,SAAS,EAAE,CAAC;gBACxC,CAAC;aACF;YAJO;gBADL,IAAA,uBAAK,GAAE;;;oEACqC,OAAO,oBAAP,OAAO;qEAGnD;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAElC,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;YAC7D,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;YAE7D,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACtC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,2BAA2B;YAClE,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;;YAC7C,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,MAAM,WAAW;gBAAjB;oBACE,kBAAa,GAAG,aAAa,CAAC;gBAOhC,CAAC;gBAJO,AAAN,KAAK,CAAC,cAAc,CAAC,KAAa;oBAChC,SAAS,EAAE,CAAC;oBACZ,OAAO,UAAU,KAAK,IAAI,SAAS,EAAE,CAAC;gBACxC,CAAC;aACF;YAJO;gBADL,IAAA,uBAAK,EAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,WAAW;;;;oEACM,OAAO,oBAAP,OAAO;6DAG3C;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAElC,aAAa;YACb,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACtC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE1B,6CAA6C;YAC7C,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACtC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE1B,yBAAyB;YACzB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;YAEtD,4DAA4D;YAC5D,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACtC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;;YACrD,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,MAAM,WAAW;gBAAjB;oBACE,kBAAa,GAAG,aAAa,CAAC;gBAYhC,CAAC;gBATO,AAAN,KAAK,CAAC,OAAO,CAAC,EAAU;oBACtB,SAAS,EAAE,CAAC;oBACZ,OAAO,QAAQ,EAAE,IAAI,SAAS,EAAE,CAAC;gBACnC,CAAC;gBAGK,AAAN,KAAK,CAAC,UAAU,CAAC,EAAU;oBACzB,mBAAmB;gBACrB,CAAC;aACF;YATO;gBADL,IAAA,uBAAK,GAAE;;;oEACmB,OAAO,oBAAP,OAAO;sDAGjC;YAGK;gBADL,IAAA,iCAAe,EAAC,8BAA8B,CAAC;;;oEAClB,OAAO,oBAAP,OAAO;yDAEpC;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAElC,kBAAkB;YAClB,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACpC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE1B,qBAAqB;YACrB,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACpC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE1B,mBAAmB;YACnB,MAAM,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YAEjC,0BAA0B;YAC1B,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACpC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;;YACrD,MAAM,WAAW;gBAAjB;oBACE,kBAAa,GAAG,aAAa,CAAC;gBAMhC,CAAC;gBAHO,AAAN,KAAK,CAAC,kBAAkB;oBACtB,2CAA2C;gBAC7C,CAAC;aACF;YAHO;gBADL,IAAA,iCAAe,EAAC,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;;;oEACd,OAAO,oBAAP,OAAO;iEAElC;YAGH,qBAAqB;YACrB,MAAM,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YAC1C,MAAM,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YAC1C,MAAM,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YAE1C,MAAM,CAAC,MAAM,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEnD,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,OAAO,CAAC,kBAAkB,EAAE,CAAC;YAEnC,MAAM,CAAC,MAAM,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;;YAChD,MAAM,WAAW;gBAAjB;oBACE,kBAAa,GAAG,aAAa,CAAC;gBAMhC,CAAC;gBAHO,AAAN,KAAK,CAAC,iBAAiB,CAAC,EAAU;oBAChC,YAAY;gBACd,CAAC;aACF;YAHO;gBADL,IAAA,iCAAe,EAAC,CAAC,IAAI,EAAE,EAAE,CAAC,eAAe,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;;;oEACf,OAAO,oBAAP,OAAO;gEAE3C;YAGH,qBAAqB;YACrB,MAAM,aAAa,CAAC,GAAG,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,aAAa,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE9D,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAEvC,MAAM,CAAC,MAAM,aAAa,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;;YAClE,MAAM,YAAY,GAAG;gBACnB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;aAChE,CAAC;YAEF,MAAM,WAAW;gBAAjB;oBACE,kBAAa,GAAG,YAAY,CAAC;gBAM/B,CAAC;gBAHO,AAAN,KAAK,CAAC,6BAA6B;oBACjC,OAAO,SAAS,CAAC;gBACnB,CAAC;aACF;YAHO;gBADL,IAAA,iCAAe,EAAC,UAAU,CAAC;;;oEACW,OAAO,oBAAP,OAAO;4EAE7C;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAElC,oDAAoD;YACpD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,6BAA6B,EAAE,CAAC;YAC7D,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC/B,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;;YAC5D,MAAM,WAAW;gBAAjB;oBACE,kBAAa,GAAG,aAAa,CAAC;gBAMhC,CAAC;gBAHO,AAAN,KAAK,CAAC,aAAa;oBACjB,kCAAkC;gBACpC,CAAC;aACF;YAHO;gBADL,IAAA,+BAAa,GAAE;;;oEACO,OAAO,oBAAP,OAAO;4DAE7B;YAGH,uCAAuC;YACvC,MAAM,aAAa,CAAC,GAAG,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC;YACzD,MAAM,aAAa,CAAC,GAAG,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC;YACzD,MAAM,aAAa,CAAC,GAAG,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC;YAEzD,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,OAAO,CAAC,aAAa,EAAE,CAAC;YAE9B,sEAAsE;YACtE,MAAM,CAAC,MAAM,aAAa,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnE,MAAM,CAAC,MAAM,aAAa,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnE,MAAM,CAAC,MAAM,aAAa,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;;YAC9D,MAAM,YAAY,GAAG;gBACnB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;aAC9D,CAAC;YAEF,MAAM,WAAW;gBAAjB;oBACE,kBAAa,GAAG,YAAY,CAAC;gBAM/B,CAAC;gBAHO,AAAN,KAAK,CAAC,yBAAyB;oBAC7B,OAAO,SAAS,CAAC;gBACnB,CAAC;aACF;YAHO;gBADL,IAAA,+BAAa,GAAE;;;oEACmB,OAAO,oBAAP,OAAO;wEAEzC;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAElC,gDAAgD;YAChD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,yBAAyB,EAAE,CAAC;YACzD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC/B,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;;YACpD,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,MAAM,WAAW;gBAAjB;oBACE,kBAAa,GAAG,aAAa,CAAC;gBAYhC,CAAC;gBATO,AAAN,KAAK,CAAC,YAAY,CAAC,EAAU;oBAC3B,SAAS,EAAE,CAAC;oBACZ,OAAO,UAAU,EAAE,IAAI,SAAS,EAAE,CAAC;gBACrC,CAAC;gBAGK,AAAN,KAAK,CAAC,gBAAgB,CAAC,EAAU;oBAC/B,yBAAyB;gBAC3B,CAAC;aACF;YATO;gBADL,IAAA,uBAAK,EAAC,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;;;oEACU,OAAO,oBAAP,OAAO;2DAGtC;YAGK;gBADL,IAAA,iCAAe,EAAC,CAAC,IAAI,EAAE,EAAE,CAAC,8BAA8B,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;;;oEACjC,OAAO,oBAAP,OAAO;+DAE1C;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAElC,aAAa;YACb,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YACnD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACtC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE1B,gBAAgB;YAChB,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YACnD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACtC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE1B,aAAa;YACb,MAAM,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAEvC,0BAA0B;YAC1B,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YACnD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACtC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\decorators\\cache.decorator.spec.ts"], "sourcesContent": ["import { <PERSON><PERSON>, CacheInvalidate, CacheEvictAll } from '../../decorators/cache.decorator';\r\nimport { InMemoryCacheStrategy } from '../../patterns/cache-strategy';\r\n\r\ndescribe('Cache Decorator', () => {\r\n  let cacheStrategy: InMemoryCacheStrategy;\r\n\r\n  beforeEach(() => {\r\n    cacheStrategy = new InMemoryCacheStrategy({\r\n      defaultTtl: 60000, // 1 minute\r\n      maxSize: 100,\r\n    });\r\n  });\r\n\r\n  afterEach(() => {\r\n    cacheStrategy.destroy();\r\n  });\r\n\r\n  describe('@Cache decorator', () => {\r\n    it('should cache method results', async () => {\r\n      let callCount = 0;\r\n\r\n      class TestService {\r\n        cacheStrategy = cacheStrategy;\r\n\r\n        @Cache({ ttl: 30000 })\r\n        async expensiveOperation(input: string): Promise<string> {\r\n          callCount++;\r\n          return `result-${input}-${callCount}`;\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      \r\n      // First call should execute method\r\n      const result1 = await service.expensiveOperation('test');\r\n      expect(result1).toBe('result-test-1');\r\n      expect(callCount).toBe(1);\r\n\r\n      // Second call should return cached result\r\n      const result2 = await service.expensiveOperation('test');\r\n      expect(result2).toBe('result-test-1'); // Same result as first call\r\n      expect(callCount).toBe(1); // Method not called again\r\n    });\r\n\r\n    it('should use different cache keys for different arguments', async () => {\r\n      let callCount = 0;\r\n\r\n      class TestService {\r\n        cacheStrategy = cacheStrategy;\r\n\r\n        @Cache()\r\n        async methodWithArgs(arg1: string, arg2: number): Promise<string> {\r\n          callCount++;\r\n          return `${arg1}-${arg2}-${callCount}`;\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      \r\n      const result1 = await service.methodWithArgs('a', 1);\r\n      const result2 = await service.methodWithArgs('a', 2);\r\n      const result3 = await service.methodWithArgs('a', 1); // Same as first call\r\n\r\n      expect(result1).toBe('a-1-1');\r\n      expect(result2).toBe('a-2-2');\r\n      expect(result3).toBe('a-1-1'); // Cached result\r\n      expect(callCount).toBe(2); // Only two unique calls\r\n    });\r\n\r\n    it('should use custom key generator', async () => {\r\n      let callCount = 0;\r\n\r\n      class TestService {\r\n        cacheStrategy = cacheStrategy;\r\n\r\n        @Cache({ \r\n          key: (args) => `custom-${args[0]}`,\r\n          ttl: 30000 \r\n        })\r\n        async customKeyMethod(id: string, data: any): Promise<string> {\r\n          callCount++;\r\n          return `result-${id}-${callCount}`;\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      \r\n      const result1 = await service.customKeyMethod('123', { prop: 'value1' });\r\n      const result2 = await service.customKeyMethod('123', { prop: 'value2' }); // Different data, same ID\r\n\r\n      expect(result1).toBe('result-123-1');\r\n      expect(result2).toBe('result-123-1'); // Cached result despite different data\r\n      expect(callCount).toBe(1);\r\n    });\r\n\r\n    it('should execute method when no cache strategy available', async () => {\r\n      let callCount = 0;\r\n\r\n      class TestService {\r\n        // No cache strategy\r\n\r\n        @Cache()\r\n        async uncachedMethod(input: string): Promise<string> {\r\n          callCount++;\r\n          return `result-${input}-${callCount}`;\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      \r\n      const result1 = await service.uncachedMethod('test');\r\n      const result2 = await service.uncachedMethod('test');\r\n\r\n      expect(result1).toBe('result-test-1');\r\n      expect(result2).toBe('result-test-2'); // Not cached\r\n      expect(callCount).toBe(2);\r\n    });\r\n\r\n    it('should handle cache errors gracefully', async () => {\r\n      let callCount = 0;\r\n      const failingCache = {\r\n        get: jest.fn().mockRejectedValue(new Error('Cache get failed')),\r\n        set: jest.fn().mockRejectedValue(new Error('Cache set failed')),\r\n      };\r\n\r\n      class TestService {\r\n        cacheStrategy = failingCache;\r\n\r\n        @Cache()\r\n        async methodWithFailingCache(input: string): Promise<string> {\r\n          callCount++;\r\n          return `result-${input}-${callCount}`;\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      \r\n      const result1 = await service.methodWithFailingCache('test');\r\n      const result2 = await service.methodWithFailingCache('test');\r\n\r\n      expect(result1).toBe('result-test-1');\r\n      expect(result2).toBe('result-test-2'); // Not cached due to errors\r\n      expect(callCount).toBe(2);\r\n    });\r\n\r\n    it('should respect TTL expiration', async () => {\r\n      let callCount = 0;\r\n\r\n      class TestService {\r\n        cacheStrategy = cacheStrategy;\r\n\r\n        @Cache({ ttl: 50 }) // 50ms TTL\r\n        async shortTtlMethod(input: string): Promise<string> {\r\n          callCount++;\r\n          return `result-${input}-${callCount}`;\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      \r\n      // First call\r\n      const result1 = await service.shortTtlMethod('test');\r\n      expect(result1).toBe('result-test-1');\r\n      expect(callCount).toBe(1);\r\n\r\n      // Second call immediately - should be cached\r\n      const result2 = await service.shortTtlMethod('test');\r\n      expect(result2).toBe('result-test-1');\r\n      expect(callCount).toBe(1);\r\n\r\n      // Wait for TTL to expire\r\n      await new Promise(resolve => setTimeout(resolve, 60));\r\n\r\n      // Third call after expiration - should execute method again\r\n      const result3 = await service.shortTtlMethod('test');\r\n      expect(result3).toBe('result-test-2');\r\n      expect(callCount).toBe(2);\r\n    });\r\n  });\r\n\r\n  describe('@CacheInvalidate decorator', () => {\r\n    it('should invalidate specific cache keys', async () => {\r\n      let callCount = 0;\r\n\r\n      class TestService {\r\n        cacheStrategy = cacheStrategy;\r\n\r\n        @Cache()\r\n        async getData(id: string): Promise<string> {\r\n          callCount++;\r\n          return `data-${id}-${callCount}`;\r\n        }\r\n\r\n        @CacheInvalidate('TestService.getData:[\"test\"]')\r\n        async updateData(id: string): Promise<void> {\r\n          // Update operation\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      \r\n      // Cache some data\r\n      const result1 = await service.getData('test');\r\n      expect(result1).toBe('data-test-1');\r\n      expect(callCount).toBe(1);\r\n\r\n      // Verify it's cached\r\n      const result2 = await service.getData('test');\r\n      expect(result2).toBe('data-test-1');\r\n      expect(callCount).toBe(1);\r\n\r\n      // Invalidate cache\r\n      await service.updateData('test');\r\n\r\n      // Should fetch fresh data\r\n      const result3 = await service.getData('test');\r\n      expect(result3).toBe('data-test-2');\r\n      expect(callCount).toBe(2);\r\n    });\r\n\r\n    it('should invalidate multiple cache keys', async () => {\r\n      class TestService {\r\n        cacheStrategy = cacheStrategy;\r\n\r\n        @CacheInvalidate(['key1', 'key2', 'key3'])\r\n        async invalidateMultiple(): Promise<void> {\r\n          // Operation that invalidates multiple keys\r\n        }\r\n      }\r\n\r\n      // Pre-populate cache\r\n      await cacheStrategy.set('key1', 'value1');\r\n      await cacheStrategy.set('key2', 'value2');\r\n      await cacheStrategy.set('key3', 'value3');\r\n\r\n      expect(await cacheStrategy.has('key1')).toBe(true);\r\n      expect(await cacheStrategy.has('key2')).toBe(true);\r\n      expect(await cacheStrategy.has('key3')).toBe(true);\r\n\r\n      const service = new TestService();\r\n      await service.invalidateMultiple();\r\n\r\n      expect(await cacheStrategy.has('key1')).toBe(false);\r\n      expect(await cacheStrategy.has('key2')).toBe(false);\r\n      expect(await cacheStrategy.has('key3')).toBe(false);\r\n    });\r\n\r\n    it('should use dynamic key generator', async () => {\r\n      class TestService {\r\n        cacheStrategy = cacheStrategy;\r\n\r\n        @CacheInvalidate((args) => `dynamic-key-${args[0]}`)\r\n        async invalidateDynamic(id: string): Promise<void> {\r\n          // Operation\r\n        }\r\n      }\r\n\r\n      // Pre-populate cache\r\n      await cacheStrategy.set('dynamic-key-123', 'value');\r\n      expect(await cacheStrategy.has('dynamic-key-123')).toBe(true);\r\n\r\n      const service = new TestService();\r\n      await service.invalidateDynamic('123');\r\n\r\n      expect(await cacheStrategy.has('dynamic-key-123')).toBe(false);\r\n    });\r\n\r\n    it('should handle cache invalidation errors gracefully', async () => {\r\n      const failingCache = {\r\n        delete: jest.fn().mockRejectedValue(new Error('Delete failed')),\r\n      };\r\n\r\n      class TestService {\r\n        cacheStrategy = failingCache;\r\n\r\n        @CacheInvalidate('some-key')\r\n        async methodWithFailingInvalidation(): Promise<string> {\r\n          return 'success';\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      \r\n      // Should not throw error even if invalidation fails\r\n      const result = await service.methodWithFailingInvalidation();\r\n      expect(result).toBe('success');\r\n      expect(failingCache.delete).toHaveBeenCalledWith('some-key');\r\n    });\r\n  });\r\n\r\n  describe('@CacheEvictAll decorator', () => {\r\n    it('should clear all cache entries for the class', async () => {\r\n      class TestService {\r\n        cacheStrategy = cacheStrategy;\r\n\r\n        @CacheEvictAll()\r\n        async clearAllCache(): Promise<void> {\r\n          // Operation that clears all cache\r\n        }\r\n      }\r\n\r\n      // Pre-populate cache with various keys\r\n      await cacheStrategy.set('TestService.method1', 'value1');\r\n      await cacheStrategy.set('TestService.method2', 'value2');\r\n      await cacheStrategy.set('OtherService.method', 'value3');\r\n\r\n      const service = new TestService();\r\n      await service.clearAllCache();\r\n\r\n      // TestService cache should be cleared, but OtherService should remain\r\n      expect(await cacheStrategy.has('TestService.method1')).toBe(false);\r\n      expect(await cacheStrategy.has('TestService.method2')).toBe(false);\r\n      expect(await cacheStrategy.has('OtherService.method')).toBe(true);\r\n    });\r\n\r\n    it('should handle cache eviction errors gracefully', async () => {\r\n      const failingCache = {\r\n        clear: jest.fn().mockRejectedValue(new Error('Clear failed')),\r\n      };\r\n\r\n      class TestService {\r\n        cacheStrategy = failingCache;\r\n\r\n        @CacheEvictAll()\r\n        async methodWithFailingEviction(): Promise<string> {\r\n          return 'success';\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      \r\n      // Should not throw error even if eviction fails\r\n      const result = await service.methodWithFailingEviction();\r\n      expect(result).toBe('success');\r\n      expect(failingCache.clear).toHaveBeenCalledWith('TestService.*');\r\n    });\r\n  });\r\n\r\n  describe('integration scenarios', () => {\r\n    it('should work with multiple decorators', async () => {\r\n      let callCount = 0;\r\n\r\n      class TestService {\r\n        cacheStrategy = cacheStrategy;\r\n\r\n        @Cache({ ttl: 30000 })\r\n        async cachedMethod(id: string): Promise<string> {\r\n          callCount++;\r\n          return `cached-${id}-${callCount}`;\r\n        }\r\n\r\n        @CacheInvalidate((args) => `TestService.cachedMethod:[\"${args[0]}\"]`)\r\n        async invalidateMethod(id: string): Promise<void> {\r\n          // Invalidation operation\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      \r\n      // Cache data\r\n      const result1 = await service.cachedMethod('test');\r\n      expect(result1).toBe('cached-test-1');\r\n      expect(callCount).toBe(1);\r\n\r\n      // Verify cached\r\n      const result2 = await service.cachedMethod('test');\r\n      expect(result2).toBe('cached-test-1');\r\n      expect(callCount).toBe(1);\r\n\r\n      // Invalidate\r\n      await service.invalidateMethod('test');\r\n\r\n      // Should fetch fresh data\r\n      const result3 = await service.cachedMethod('test');\r\n      expect(result3).toBe('cached-test-2');\r\n      expect(callCount).toBe(2);\r\n    });\r\n  });\r\n});"], "version": 3}