1929e2019a67b17ce0587623b3ebd782
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIModelCreatedEvent = void 0;
const base_domain_event_1 = require("../../../../shared-kernel/domain/base-domain-event");
/**
 * AI Model Created Domain Event
 *
 * Published when a new AI model is created in the system.
 * This event can trigger various downstream processes such as
 * model registration, health check initialization, and monitoring setup.
 */
class AIModelCreatedEvent extends base_domain_event_1.BaseDomainEvent {
    constructor(modelId, modelName, provider, modelType, eventId, occurredOn) {
        super(eventId, occurredOn);
        this.modelId = modelId;
        this.modelName = modelName;
        this.provider = provider;
        this.modelType = modelType;
    }
    getEventName() {
        return 'AIModelCreated';
    }
    getEventVersion() {
        return '1.0';
    }
    getEventData() {
        return {
            modelId: this.modelId.toString(),
            modelName: this.modelName,
            provider: this.provider,
            modelType: this.modelType,
        };
    }
}
exports.AIModelCreatedEvent = AIModelCreatedEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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