6b48488e3ff158f8a37437720668496e
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var VulnerabilityScanService_1;
var _a, _b, _c, _d, _e, _f;
Object.defineProperty(exports, "__esModule", { value: true });
exports.VulnerabilityScanService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const schedule_1 = require("@nestjs/schedule");
const vulnerability_scan_entity_1 = require("../../domain/entities/vulnerability-scan.entity");
const asset_entity_1 = require("../../../asset-management/domain/entities/asset.entity");
const logger_service_1 = require("../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../infrastructure/logging/audit/audit.service");
const notification_service_1 = require("../../../../infrastructure/notification/notification.service");
/**
 * Vulnerability Scan service
 * Handles vulnerability scanning operations and management
 */
let VulnerabilityScanService = VulnerabilityScanService_1 = class VulnerabilityScanService {
    constructor(scanRepository, assetRepository, loggerService, auditService, notificationService) {
        this.scanRepository = scanRepository;
        this.assetRepository = assetRepository;
        this.loggerService = loggerService;
        this.auditService = auditService;
        this.notificationService = notificationService;
        this.logger = new common_1.Logger(VulnerabilityScanService_1.name);
    }
    /**
     * Create vulnerability scan
     */
    async createScan(scanData, userId) {
        try {
            this.logger.debug('Creating vulnerability scan', {
                name: scanData.name,
                scanType: scanData.scanType,
                scannerType: scanData.scannerType,
                userId,
            });
            const scan = this.scanRepository.create({
                ...scanData,
                status: 'pending',
                initiatedBy: userId,
            });
            const savedScan = await this.scanRepository.save(scan);
            await this.auditService.logUserAction(userId, 'create', 'vulnerability_scan', savedScan.id, {
                name: scanData.name,
                scanType: scanData.scanType,
                scannerType: scanData.scannerType,
            });
            this.logger.log('Vulnerability scan created successfully', {
                scanId: savedScan.id,
                name: scanData.name,
                userId,
            });
            return savedScan;
        }
        catch (error) {
            this.logger.error('Failed to create vulnerability scan', {
                error: error.message,
                scanData,
                userId,
            });
            throw error;
        }
    }
    /**
     * Get scan details
     */
    async getScanDetails(id) {
        try {
            const scan = await this.scanRepository.findOne({
                where: { id },
                relations: ['assessments', 'scannedAssets'],
            });
            if (!scan) {
                throw new common_1.NotFoundException('Scan not found');
            }
            this.logger.debug('Scan details retrieved', {
                scanId: id,
                name: scan.name,
                status: scan.status,
            });
            return scan;
        }
        catch (error) {
            this.logger.error('Failed to get scan details', {
                scanId: id,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Search scans with filtering
     */
    async searchScans(criteria) {
        try {
            const page = criteria.page || 1;
            const limit = Math.min(criteria.limit || 50, 1000);
            const offset = (page - 1) * limit;
            const queryBuilder = this.scanRepository
                .createQueryBuilder('scan')
                .leftJoinAndSelect('scan.assessments', 'assessments')
                .leftJoinAndSelect('scan.scannedAssets', 'assets');
            // Apply filters
            if (criteria.scanTypes?.length) {
                queryBuilder.andWhere('scan.scanType IN (:...scanTypes)', { scanTypes: criteria.scanTypes });
            }
            if (criteria.scannerTypes?.length) {
                queryBuilder.andWhere('scan.scannerType IN (:...scannerTypes)', { scannerTypes: criteria.scannerTypes });
            }
            if (criteria.statuses?.length) {
                queryBuilder.andWhere('scan.status IN (:...statuses)', { statuses: criteria.statuses });
            }
            if (criteria.initiatedBy?.length) {
                queryBuilder.andWhere('scan.initiatedBy IN (:...initiatedBy)', { initiatedBy: criteria.initiatedBy });
            }
            if (criteria.startedAfter) {
                queryBuilder.andWhere('scan.startedAt >= :startedAfter', { startedAfter: criteria.startedAfter });
            }
            if (criteria.startedBefore) {
                queryBuilder.andWhere('scan.startedAt <= :startedBefore', { startedBefore: criteria.startedBefore });
            }
            if (criteria.completedAfter) {
                queryBuilder.andWhere('scan.completedAt >= :completedAfter', { completedAfter: criteria.completedAfter });
            }
            if (criteria.completedBefore) {
                queryBuilder.andWhere('scan.completedAt <= :completedBefore', { completedBefore: criteria.completedBefore });
            }
            if (criteria.tags?.length) {
                queryBuilder.andWhere('scan.tags && :tags', { tags: criteria.tags });
            }
            if (criteria.searchText) {
                queryBuilder.andWhere('(scan.name ILIKE :searchText OR scan.description ILIKE :searchText)', { searchText: `%${criteria.searchText}%` });
            }
            // Apply sorting
            const sortBy = criteria.sortBy || 'createdAt';
            const sortOrder = criteria.sortOrder || 'DESC';
            queryBuilder.orderBy(`scan.${sortBy}`, sortOrder);
            // Apply pagination
            queryBuilder.skip(offset).take(limit);
            const [scans, total] = await queryBuilder.getManyAndCount();
            this.logger.debug('Scan search completed', {
                total,
                page,
                limit,
                criteriaCount: Object.keys(criteria).length,
            });
            return {
                scans,
                total,
                page,
                totalPages: Math.ceil(total / limit),
            };
        }
        catch (error) {
            this.logger.error('Failed to search scans', {
                error: error.message,
                criteria,
            });
            throw error;
        }
    }
    /**
     * Start scan
     */
    async startScan(id, userId) {
        try {
            const scan = await this.scanRepository.findOne({
                where: { id },
            });
            if (!scan) {
                throw new common_1.NotFoundException('Scan not found');
            }
            if (scan.status !== 'pending') {
                throw new Error(`Cannot start scan in ${scan.status} status`);
            }
            this.logger.debug('Starting vulnerability scan', {
                scanId: id,
                name: scan.name,
                userId,
            });
            scan.start();
            const savedScan = await this.scanRepository.save(scan);
            // Send notification if configured
            if (scan.scanConfig.notifications?.onStart) {
                await this.notificationService.sendScanStartedNotification({
                    scanId: id,
                    scanName: scan.name,
                    scanType: scan.scanType,
                    initiatedBy: userId,
                    recipients: scan.scanConfig.notifications.recipients,
                });
            }
            await this.auditService.logUserAction(userId, 'start', 'vulnerability_scan', id, {
                name: scan.name,
                scanType: scan.scanType,
            });
            this.logger.log('Vulnerability scan started successfully', {
                scanId: id,
                name: scan.name,
                userId,
            });
            return savedScan;
        }
        catch (error) {
            this.logger.error('Failed to start vulnerability scan', {
                scanId: id,
                error: error.message,
                userId,
            });
            throw error;
        }
    }
    /**
     * Complete scan
     */
    async completeScan(id, results, userId) {
        try {
            const scan = await this.scanRepository.findOne({
                where: { id },
            });
            if (!scan) {
                throw new common_1.NotFoundException('Scan not found');
            }
            this.logger.debug('Completing vulnerability scan', {
                scanId: id,
                name: scan.name,
                vulnerabilityCount: results.summary?.totalVulnerabilities || 0,
            });
            scan.complete(results);
            const savedScan = await this.scanRepository.save(scan);
            // Send notification if configured
            if (scan.scanConfig.notifications?.onComplete) {
                await this.notificationService.sendScanCompletedNotification({
                    scanId: id,
                    scanName: scan.name,
                    scanType: scan.scanType,
                    results: results.summary,
                    recipients: scan.scanConfig.notifications.recipients,
                });
            }
            if (userId) {
                await this.auditService.logUserAction(userId, 'complete', 'vulnerability_scan', id, {
                    name: scan.name,
                    vulnerabilityCount: results.summary?.totalVulnerabilities || 0,
                    duration: scan.durationSeconds,
                });
            }
            this.logger.log('Vulnerability scan completed successfully', {
                scanId: id,
                name: scan.name,
                vulnerabilityCount: results.summary?.totalVulnerabilities || 0,
                duration: scan.durationFormatted,
            });
            return savedScan;
        }
        catch (error) {
            this.logger.error('Failed to complete vulnerability scan', {
                scanId: id,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Fail scan
     */
    async failScan(id, error, userId) {
        try {
            const scan = await this.scanRepository.findOne({
                where: { id },
            });
            if (!scan) {
                throw new common_1.NotFoundException('Scan not found');
            }
            this.logger.debug('Failing vulnerability scan', {
                scanId: id,
                name: scan.name,
                error,
            });
            scan.fail(error);
            const savedScan = await this.scanRepository.save(scan);
            // Send notification if configured
            if (scan.scanConfig.notifications?.onError) {
                await this.notificationService.sendScanFailedNotification({
                    scanId: id,
                    scanName: scan.name,
                    scanType: scan.scanType,
                    error,
                    recipients: scan.scanConfig.notifications.recipients,
                });
            }
            if (userId) {
                await this.auditService.logUserAction(userId, 'fail', 'vulnerability_scan', id, {
                    name: scan.name,
                    error,
                });
            }
            this.logger.log('Vulnerability scan failed', {
                scanId: id,
                name: scan.name,
                error,
            });
            return savedScan;
        }
        catch (error) {
            this.logger.error('Failed to fail vulnerability scan', {
                scanId: id,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Cancel scan
     */
    async cancelScan(id, reason, userId) {
        try {
            const scan = await this.scanRepository.findOne({
                where: { id },
            });
            if (!scan) {
                throw new common_1.NotFoundException('Scan not found');
            }
            this.logger.debug('Cancelling vulnerability scan', {
                scanId: id,
                name: scan.name,
                reason,
                userId,
            });
            scan.cancel(reason);
            const savedScan = await this.scanRepository.save(scan);
            await this.auditService.logUserAction(userId, 'cancel', 'vulnerability_scan', id, {
                name: scan.name,
                reason,
            });
            this.logger.log('Vulnerability scan cancelled successfully', {
                scanId: id,
                name: scan.name,
                reason,
                userId,
            });
            return savedScan;
        }
        catch (error) {
            this.logger.error('Failed to cancel vulnerability scan', {
                scanId: id,
                error: error.message,
                userId,
            });
            throw error;
        }
    }
    /**
     * Update scan progress
     */
    async updateScanProgress(id, progress) {
        try {
            const scan = await this.scanRepository.findOne({
                where: { id },
            });
            if (!scan) {
                throw new common_1.NotFoundException('Scan not found');
            }
            scan.updateProgress(progress);
            const savedScan = await this.scanRepository.save(scan);
            this.logger.debug('Scan progress updated', {
                scanId: id,
                progress: progress.overallProgress,
                currentPhase: progress.currentPhase,
            });
            return savedScan;
        }
        catch (error) {
            this.logger.error('Failed to update scan progress', {
                scanId: id,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Delete scan
     */
    async deleteScan(id, userId) {
        try {
            const scan = await this.scanRepository.findOne({
                where: { id },
            });
            if (!scan) {
                throw new common_1.NotFoundException('Scan not found');
            }
            if (scan.isRunning) {
                throw new Error('Cannot delete running scan');
            }
            this.logger.debug('Deleting vulnerability scan', {
                scanId: id,
                name: scan.name,
                userId,
            });
            await this.scanRepository.remove(scan);
            await this.auditService.logUserAction(userId, 'delete', 'vulnerability_scan', id, {
                name: scan.name,
                scanType: scan.scanType,
            });
            this.logger.log('Vulnerability scan deleted successfully', {
                scanId: id,
                name: scan.name,
                userId,
            });
        }
        catch (error) {
            this.logger.error('Failed to delete vulnerability scan', {
                scanId: id,
                error: error.message,
                userId,
            });
            throw error;
        }
    }
    /**
     * Monitor scheduled scans
     */
    async monitorScheduledScans() {
        try {
            this.logger.debug('Checking for scheduled scans');
            const now = new Date();
            const scheduledScans = await this.scanRepository.find({
                where: {
                    status: 'pending',
                    scheduledAt: (0, typeorm_2.Between)(new Date(now.getTime() - 60000), now), // Within last minute
                },
            });
            for (const scan of scheduledScans) {
                try {
                    await this.startScan(scan.id, scan.initiatedBy);
                }
                catch (error) {
                    this.logger.error('Failed to start scheduled scan', {
                        scanId: scan.id,
                        error: error.message,
                    });
                }
            }
            if (scheduledScans.length > 0) {
                this.logger.log('Processed scheduled scans', {
                    count: scheduledScans.length,
                });
            }
        }
        catch (error) {
            this.logger.error('Failed to monitor scheduled scans', {
                error: error.message,
            });
        }
    }
};
exports.VulnerabilityScanService = VulnerabilityScanService;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_MINUTE),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_f = typeof Promise !== "undefined" && Promise) === "function" ? _f : Object)
], VulnerabilityScanService.prototype, "monitorScheduledScans", null);
exports.VulnerabilityScanService = VulnerabilityScanService = VulnerabilityScanService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(vulnerability_scan_entity_1.VulnerabilityScan)),
    __param(1, (0, typeorm_1.InjectRepository)(asset_entity_1.Asset)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof logger_service_1.LoggerService !== "undefined" && logger_service_1.LoggerService) === "function" ? _c : Object, typeof (_d = typeof audit_service_1.AuditService !== "undefined" && audit_service_1.AuditService) === "function" ? _d : Object, typeof (_e = typeof notification_service_1.NotificationService !== "undefined" && notification_service_1.NotificationService) === "function" ? _e : Object])
], VulnerabilityScanService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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