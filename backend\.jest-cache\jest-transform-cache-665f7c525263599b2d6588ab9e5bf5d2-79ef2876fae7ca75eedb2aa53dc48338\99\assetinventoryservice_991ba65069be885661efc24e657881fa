e2f1cc84334dd138bbef52c857336e70
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AssetInventoryService_1;
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetInventoryService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const schedule_1 = require("@nestjs/schedule");
const asset_entity_1 = require("../../domain/entities/asset.entity");
const asset_group_entity_1 = require("../../domain/entities/asset-group.entity");
const asset_configuration_entity_1 = require("../../domain/entities/asset-configuration.entity");
const asset_vulnerability_entity_1 = require("../../domain/entities/asset-vulnerability.entity");
const asset_relationship_entity_1 = require("../../domain/entities/asset-relationship.entity");
const logger_service_1 = require("../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../infrastructure/logging/audit/audit.service");
const notification_service_1 = require("../../../../infrastructure/notification/notification.service");
const asset_discovery_service_1 = require("./asset-discovery.service");
const asset_classification_service_1 = require("./asset-classification.service");
/**
 * Asset Inventory service
 * Handles comprehensive asset inventory management, tracking, and lifecycle operations
 */
let AssetInventoryService = AssetInventoryService_1 = class AssetInventoryService {
    constructor(assetRepository, assetGroupRepository, configurationRepository, vulnerabilityRepository, relationshipRepository, loggerService, auditService, notificationService, assetDiscoveryService, assetClassificationService) {
        this.assetRepository = assetRepository;
        this.assetGroupRepository = assetGroupRepository;
        this.configurationRepository = configurationRepository;
        this.vulnerabilityRepository = vulnerabilityRepository;
        this.relationshipRepository = relationshipRepository;
        this.loggerService = loggerService;
        this.auditService = auditService;
        this.notificationService = notificationService;
        this.assetDiscoveryService = assetDiscoveryService;
        this.assetClassificationService = assetClassificationService;
        this.logger = new common_1.Logger(AssetInventoryService_1.name);
    }
    /**
     * Get asset inventory dashboard
     * @returns Comprehensive inventory dashboard data
     */
    async getInventoryDashboard() {
        try {
            this.logger.debug('Generating asset inventory dashboard');
            const [totalAssets, assetsByType, assetsByStatus, assetsByCriticality, assetsByEnvironment, onlineAssets, assetsWithAgents, vulnerableAssets, recentlyDiscovered, inventoryTrends,] = await Promise.all([
                this.getTotalAssetsCount(),
                this.getAssetsByType(),
                this.getAssetsByStatus(),
                this.getAssetsByCriticality(),
                this.getAssetsByEnvironment(),
                this.getOnlineAssetsCount(),
                this.getAssetsWithAgentsCount(),
                this.getVulnerableAssetsCount(),
                this.getRecentlyDiscoveredAssets(7), // Last 7 days
                this.getInventoryTrends(30), // Last 30 days
            ]);
            const dashboard = {
                summary: {
                    totalAssets,
                    onlineAssets,
                    offlineAssets: totalAssets - onlineAssets,
                    assetsWithAgents,
                    vulnerableAssets,
                    agentCoverage: totalAssets > 0 ? Math.round((assetsWithAgents / totalAssets) * 100) : 0,
                    vulnerabilityRate: totalAssets > 0 ? Math.round((vulnerableAssets / totalAssets) * 100) : 0,
                },
                breakdown: {
                    byType: assetsByType,
                    byStatus: assetsByStatus,
                    byCriticality: assetsByCriticality,
                    byEnvironment: assetsByEnvironment,
                },
                recentActivity: {
                    recentlyDiscovered,
                    trends: inventoryTrends,
                },
                timestamp: new Date().toISOString(),
            };
            this.logger.log('Asset inventory dashboard generated successfully', {
                totalAssets,
                onlineAssets,
                vulnerableAssets,
            });
            return dashboard;
        }
        catch (error) {
            this.logger.error('Failed to generate asset inventory dashboard', {
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Search assets with advanced filtering
     * @param criteria Search criteria
     * @returns Paginated asset results
     */
    async searchAssets(criteria) {
        try {
            const { page = 1, limit = 50, types, statuses, criticalities, environments, locations, groupIds, tags, hasAgent, isOnline, hasVulnerabilities, searchText, sortBy = 'name', sortOrder = 'ASC', } = criteria;
            const queryBuilder = this.assetRepository.createQueryBuilder('asset')
                .leftJoinAndSelect('asset.group', 'group')
                .leftJoinAndSelect('asset.vulnerabilities', 'vulnerabilities');
            // Apply filters
            if (types && types.length > 0) {
                queryBuilder.andWhere('asset.type IN (:...types)', { types });
            }
            if (statuses && statuses.length > 0) {
                queryBuilder.andWhere('asset.status IN (:...statuses)', { statuses });
            }
            if (criticalities && criticalities.length > 0) {
                queryBuilder.andWhere('asset.criticality IN (:...criticalities)', { criticalities });
            }
            if (environments && environments.length > 0) {
                queryBuilder.andWhere('asset.environment IN (:...environments)', { environments });
            }
            if (locations && locations.length > 0) {
                queryBuilder.andWhere('asset.location IN (:...locations)', { locations });
            }
            if (groupIds && groupIds.length > 0) {
                queryBuilder.andWhere('asset.groupId IN (:...groupIds)', { groupIds });
            }
            if (tags && tags.length > 0) {
                queryBuilder.andWhere('asset.tags && :tags', { tags });
            }
            if (hasAgent !== undefined) {
                if (hasAgent) {
                    queryBuilder.andWhere("asset.discovery->>'agentInstalled' = 'true'");
                }
                else {
                    queryBuilder.andWhere("(asset.discovery->>'agentInstalled' IS NULL OR asset.discovery->>'agentInstalled' = 'false')");
                }
            }
            if (isOnline !== undefined) {
                const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
                if (isOnline) {
                    queryBuilder.andWhere('asset.lastSeen > :fiveMinutesAgo', { fiveMinutesAgo });
                }
                else {
                    queryBuilder.andWhere('(asset.lastSeen IS NULL OR asset.lastSeen <= :fiveMinutesAgo)', { fiveMinutesAgo });
                }
            }
            if (hasVulnerabilities !== undefined) {
                if (hasVulnerabilities) {
                    queryBuilder.andWhere('vulnerabilities.id IS NOT NULL');
                }
                else {
                    queryBuilder.andWhere('vulnerabilities.id IS NULL');
                }
            }
            if (searchText) {
                queryBuilder.andWhere('(asset.name ILIKE :searchText OR asset.hostname ILIKE :searchText OR asset.ipAddress ILIKE :searchText OR asset.description ILIKE :searchText)', { searchText: `%${searchText}%` });
            }
            // Apply sorting
            queryBuilder.orderBy(`asset.${sortBy}`, sortOrder);
            // Apply pagination
            const offset = (page - 1) * limit;
            queryBuilder.skip(offset).take(limit);
            const [assets, total] = await queryBuilder.getManyAndCount();
            const totalPages = Math.ceil(total / limit);
            this.logger.debug('Assets searched successfully', {
                total,
                page,
                limit,
                totalPages,
                filters: { types, statuses, criticalities, environments, hasAgent, isOnline },
            });
            return {
                assets,
                total,
                page,
                totalPages,
            };
        }
        catch (error) {
            this.logger.error('Failed to search assets', {
                error: error.message,
                criteria,
            });
            throw error;
        }
    }
    /**
     * Create new asset
     * @param assetData Asset data
     * @param userId User creating the asset
     * @returns Created asset
     */
    async createAsset(assetData, userId) {
        try {
            this.logger.debug('Creating new asset', {
                name: assetData.name,
                type: assetData.type,
                userId,
            });
            // Validate group if specified
            if (assetData.groupId) {
                const group = await this.assetGroupRepository.findOne({
                    where: { id: assetData.groupId, isActive: true },
                });
                if (!group) {
                    throw new common_1.NotFoundException('Asset group not found or inactive');
                }
            }
            const asset = this.assetRepository.create({
                ...assetData,
                status: 'unknown',
                discoveredAt: new Date(),
                lastSeen: new Date(),
                createdBy: userId,
                ipAddresses: assetData.ipAddress ? [assetData.ipAddress] : [],
                tags: assetData.tags || [],
            });
            const savedAsset = await this.assetRepository.save(asset);
            // Auto-classify asset
            await this.assetClassificationService.classifyAsset(savedAsset.id);
            // Auto-assign to groups based on rules
            await this.autoAssignToGroups(savedAsset);
            await this.auditService.logUserAction(userId, 'create', 'asset', savedAsset.id, {
                assetName: assetData.name,
                assetType: assetData.type,
                environment: assetData.environment,
                criticality: assetData.criticality,
            });
            this.logger.log('Asset created successfully', {
                assetId: savedAsset.id,
                assetName: assetData.name,
                userId,
            });
            return savedAsset;
        }
        catch (error) {
            this.logger.error('Failed to create asset', {
                error: error.message,
                assetData,
                userId,
            });
            throw error;
        }
    }
    /**
     * Update asset
     * @param assetId Asset ID
     * @param updates Asset updates
     * @param userId User updating the asset
     * @returns Updated asset
     */
    async updateAsset(assetId, updates, userId) {
        try {
            const asset = await this.assetRepository.findOne({
                where: { id: assetId },
                relations: ['group'],
            });
            if (!asset) {
                throw new common_1.NotFoundException('Asset not found');
            }
            this.logger.debug('Updating asset', {
                assetId,
                updates: Object.keys(updates),
                userId,
            });
            // Validate group if being updated
            if (updates.groupId && updates.groupId !== asset.groupId) {
                const group = await this.assetGroupRepository.findOne({
                    where: { id: updates.groupId, isActive: true },
                });
                if (!group) {
                    throw new common_1.NotFoundException('Asset group not found or inactive');
                }
            }
            // Track changes for audit
            const changes = this.trackAssetChanges(asset, updates);
            Object.assign(asset, updates);
            asset.updatedBy = userId;
            const updatedAsset = await this.assetRepository.save(asset);
            await this.auditService.logUserAction(userId, 'update', 'asset', assetId, {
                assetName: asset.name,
                changes,
            });
            this.logger.log('Asset updated successfully', {
                assetId,
                userId,
                changesCount: Object.keys(changes).length,
            });
            return updatedAsset;
        }
        catch (error) {
            this.logger.error('Failed to update asset', {
                assetId,
                error: error.message,
                userId,
            });
            throw error;
        }
    }
    /**
     * Delete asset
     * @param assetId Asset ID
     * @param userId User deleting the asset
     */
    async deleteAsset(assetId, userId) {
        try {
            const asset = await this.assetRepository.findOne({
                where: { id: assetId },
            });
            if (!asset) {
                throw new common_1.NotFoundException('Asset not found');
            }
            this.logger.debug('Deleting asset', {
                assetId,
                assetName: asset.name,
                userId,
            });
            await this.assetRepository.remove(asset);
            await this.auditService.logUserAction(userId, 'delete', 'asset', assetId, {
                assetName: asset.name,
                assetType: asset.type,
            });
            this.logger.log('Asset deleted successfully', {
                assetId,
                assetName: asset.name,
                userId,
            });
        }
        catch (error) {
            this.logger.error('Failed to delete asset', {
                assetId,
                error: error.message,
                userId,
            });
            throw error;
        }
    }
    /**
     * Get asset details with relationships
     * @param assetId Asset ID
     * @returns Asset with full details
     */
    async getAssetDetails(assetId) {
        try {
            const asset = await this.assetRepository.findOne({
                where: { id: assetId },
                relations: [
                    'group',
                    'configurations',
                    'vulnerabilities',
                    'sourceRelationships',
                    'targetRelationships',
                    'dependencies',
                    'relatedAssets',
                ],
            });
            if (!asset) {
                throw new common_1.NotFoundException('Asset not found');
            }
            this.logger.debug('Asset details retrieved', {
                assetId,
                assetName: asset.name,
            });
            return asset;
        }
        catch (error) {
            this.logger.error('Failed to get asset details', {
                assetId,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Update asset last seen timestamp
     * @param assetId Asset ID
     */
    async updateLastSeen(assetId) {
        try {
            await this.assetRepository.update(assetId, {
                lastSeen: new Date(),
            });
            this.logger.debug('Asset last seen updated', { assetId });
        }
        catch (error) {
            this.logger.error('Failed to update asset last seen', {
                assetId,
                error: error.message,
            });
        }
    }
    /**
     * Bulk update asset statuses
     * @param assetIds Asset IDs
     * @param status New status
     * @param userId User performing the update
     */
    async bulkUpdateStatus(assetIds, status, userId) {
        try {
            this.logger.debug('Bulk updating asset statuses', {
                assetCount: assetIds.length,
                status,
                userId,
            });
            await this.assetRepository.update({ id: (0, typeorm_2.In)(assetIds) }, { status, updatedBy: userId });
            await this.auditService.logUserAction(userId, 'bulk_update', 'asset', null, {
                assetIds,
                status,
                assetCount: assetIds.length,
            });
            this.logger.log('Asset statuses updated successfully', {
                assetCount: assetIds.length,
                status,
                userId,
            });
        }
        catch (error) {
            this.logger.error('Failed to bulk update asset statuses', {
                error: error.message,
                assetIds,
                status,
                userId,
            });
            throw error;
        }
    }
    /**
     * Monitor asset inventory for changes and issues
     */
    async monitorInventory() {
        try {
            this.logger.debug('Starting inventory monitoring');
            // Check for offline assets
            await this.checkOfflineAssets();
            // Check for assets without agents
            await this.checkAssetsWithoutAgents();
            // Check for unclassified assets
            await this.checkUnclassifiedAssets();
            // Update inventory statistics
            await this.updateInventoryStatistics();
            this.logger.log('Inventory monitoring completed');
        }
        catch (error) {
            this.logger.error('Failed to complete inventory monitoring', {
                error: error.message,
            });
        }
    }
    // Private helper methods
    async getTotalAssetsCount() {
        return await this.assetRepository.count();
    }
    async getAssetsByType() {
        const result = await this.assetRepository
            .createQueryBuilder('asset')
            .select('asset.type', 'type')
            .addSelect('COUNT(*)', 'count')
            .groupBy('asset.type')
            .getRawMany();
        return result.reduce((acc, item) => {
            acc[item.type] = parseInt(item.count);
            return acc;
        }, {});
    }
    async getAssetsByStatus() {
        const result = await this.assetRepository
            .createQueryBuilder('asset')
            .select('asset.status', 'status')
            .addSelect('COUNT(*)', 'count')
            .groupBy('asset.status')
            .getRawMany();
        return result.reduce((acc, item) => {
            acc[item.status] = parseInt(item.count);
            return acc;
        }, {});
    }
    async getAssetsByCriticality() {
        const result = await this.assetRepository
            .createQueryBuilder('asset')
            .select('asset.criticality', 'criticality')
            .addSelect('COUNT(*)', 'count')
            .groupBy('asset.criticality')
            .getRawMany();
        return result.reduce((acc, item) => {
            acc[item.criticality] = parseInt(item.count);
            return acc;
        }, {});
    }
    async getAssetsByEnvironment() {
        const result = await this.assetRepository
            .createQueryBuilder('asset')
            .select('asset.environment', 'environment')
            .addSelect('COUNT(*)', 'count')
            .groupBy('asset.environment')
            .getRawMany();
        return result.reduce((acc, item) => {
            acc[item.environment] = parseInt(item.count);
            return acc;
        }, {});
    }
    async getOnlineAssetsCount() {
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
        return await this.assetRepository.count({
            where: { lastSeen: (0, typeorm_2.Between)(fiveMinutesAgo, new Date()) },
        });
    }
    async getAssetsWithAgentsCount() {
        return await this.assetRepository
            .createQueryBuilder('asset')
            .where("asset.discovery->>'agentInstalled' = 'true'")
            .getCount();
    }
    async getVulnerableAssetsCount() {
        return await this.assetRepository
            .createQueryBuilder('asset')
            .innerJoin('asset.vulnerabilities', 'vulnerability')
            .where('vulnerability.status IN (:...statuses)', { statuses: ['open', 'confirmed'] })
            .getCount();
    }
    async getRecentlyDiscoveredAssets(days) {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        return await this.assetRepository.find({
            where: { discoveredAt: (0, typeorm_2.Between)(startDate, new Date()) },
            order: { discoveredAt: 'DESC' },
            take: 10,
        });
    }
    async getInventoryTrends(days) {
        // Implementation for inventory trends
        // This would track asset discovery, status changes, etc. over time
        return [];
    }
    async autoAssignToGroups(asset) {
        try {
            const groups = await this.assetGroupRepository.find({
                where: { isActive: true },
            });
            for (const group of groups) {
                if (group.matchesAutoAssignmentRules(asset)) {
                    asset.groupId = group.id;
                    await this.assetRepository.save(asset);
                    break; // Assign to first matching group
                }
            }
        }
        catch (error) {
            this.logger.error('Failed to auto-assign asset to groups', {
                assetId: asset.id,
                error: error.message,
            });
        }
    }
    trackAssetChanges(original, updates) {
        const changes = {};
        Object.keys(updates).forEach(key => {
            if (original[key] !== updates[key]) {
                changes[key] = {
                    from: original[key],
                    to: updates[key],
                };
            }
        });
        return changes;
    }
    async checkOfflineAssets() {
        const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
        const offlineAssets = await this.assetRepository.find({
            where: {
                lastSeen: (0, typeorm_2.Between)(new Date('1900-01-01'), oneHourAgo),
                status: 'active',
            },
        });
        if (offlineAssets.length > 0) {
            await this.notificationService.sendAssetOfflineNotification(offlineAssets);
        }
    }
    async checkAssetsWithoutAgents() {
        const assetsWithoutAgents = await this.assetRepository
            .createQueryBuilder('asset')
            .where("(asset.discovery->>'agentInstalled' IS NULL OR asset.discovery->>'agentInstalled' = 'false')")
            .andWhere('asset.status = :status', { status: 'active' })
            .getMany();
        // Log for monitoring purposes
        this.logger.debug('Assets without agents detected', {
            count: assetsWithoutAgents.length,
        });
    }
    async checkUnclassifiedAssets() {
        const unclassifiedAssets = await this.assetRepository.find({
            where: { type: 'other' },
        });
        for (const asset of unclassifiedAssets) {
            await this.assetClassificationService.classifyAsset(asset.id);
        }
    }
    async updateInventoryStatistics() {
        // Update cached statistics for groups
        const groups = await this.assetGroupRepository.find();
        for (const group of groups) {
            const statistics = await this.calculateGroupStatistics(group.id);
            group.updateStatistics(statistics);
            await this.assetGroupRepository.save(group);
        }
    }
    async calculateGroupStatistics(groupId) {
        const assets = await this.assetRepository.find({
            where: { groupId },
        });
        const statistics = {
            totalAssets: assets.length,
            assetsByType: {},
            assetsByStatus: {},
            assetsByCriticality: {},
        };
        assets.forEach(asset => {
            // Count by type
            statistics.assetsByType[asset.type] = (statistics.assetsByType[asset.type] || 0) + 1;
            // Count by status
            statistics.assetsByStatus[asset.status] = (statistics.assetsByStatus[asset.status] || 0) + 1;
            // Count by criticality
            statistics.assetsByCriticality[asset.criticality] = (statistics.assetsByCriticality[asset.criticality] || 0) + 1;
        });
        return statistics;
    }
};
exports.AssetInventoryService = AssetInventoryService;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_HOUR),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_l = typeof Promise !== "undefined" && Promise) === "function" ? _l : Object)
], AssetInventoryService.prototype, "monitorInventory", null);
exports.AssetInventoryService = AssetInventoryService = AssetInventoryService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(asset_entity_1.Asset)),
    __param(1, (0, typeorm_1.InjectRepository)(asset_group_entity_1.AssetGroup)),
    __param(2, (0, typeorm_1.InjectRepository)(asset_configuration_entity_1.AssetConfiguration)),
    __param(3, (0, typeorm_1.InjectRepository)(asset_vulnerability_entity_1.AssetVulnerability)),
    __param(4, (0, typeorm_1.InjectRepository)(asset_relationship_entity_1.AssetRelationship)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _c : Object, typeof (_d = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _d : Object, typeof (_e = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _e : Object, typeof (_f = typeof logger_service_1.LoggerService !== "undefined" && logger_service_1.LoggerService) === "function" ? _f : Object, typeof (_g = typeof audit_service_1.AuditService !== "undefined" && audit_service_1.AuditService) === "function" ? _g : Object, typeof (_h = typeof notification_service_1.NotificationService !== "undefined" && notification_service_1.NotificationService) === "function" ? _h : Object, typeof (_j = typeof asset_discovery_service_1.AssetDiscoveryService !== "undefined" && asset_discovery_service_1.AssetDiscoveryService) === "function" ? _j : Object, typeof (_k = typeof asset_classification_service_1.AssetClassificationService !== "undefined" && asset_classification_service_1.AssetClassificationService) === "function" ? _k : Object])
], AssetInventoryService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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