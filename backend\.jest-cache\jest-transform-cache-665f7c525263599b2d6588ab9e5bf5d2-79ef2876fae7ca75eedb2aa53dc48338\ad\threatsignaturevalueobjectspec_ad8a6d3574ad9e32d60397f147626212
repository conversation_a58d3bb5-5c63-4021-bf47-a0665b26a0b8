929341d6071adb4a53256ab69a15e474
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const threat_signature_value_object_1 = require("../threat-signature.value-object");
const confidence_level_enum_1 = require("../../../enums/confidence-level.enum");
describe('ThreatSignature Value Object', () => {
    describe('creation', () => {
        it('should create valid threat signature with required properties', () => {
            // Arrange & Act
            const signature = threat_signature_value_object_1.ThreatSignature.create('sig-001', 'Malware Detection Rule', threat_signature_value_object_1.SignatureType.YARA, threat_signature_value_object_1.SignatureCategory.MALWARE, threat_signature_value_object_1.SignatureSeverity.HIGH, 'rule MalwareDetection { strings: $a = "malicious" condition: $a }', 'Detects known malware patterns', 'security-team');
            // Assert
            expect(signature.id).toBe('sig-001');
            expect(signature.name).toBe('Malware Detection Rule');
            expect(signature.type).toBe(threat_signature_value_object_1.SignatureType.YARA);
            expect(signature.category).toBe(threat_signature_value_object_1.SignatureCategory.MALWARE);
            expect(signature.severity).toBe(threat_signature_value_object_1.SignatureSeverity.HIGH);
            expect(signature.content).toBe('rule MalwareDetection { strings: $a = "malicious" condition: $a }');
            expect(signature.description).toBe('Detects known malware patterns');
            expect(signature.author).toBe('security-team');
            expect(signature.confidence).toBe(confidence_level_enum_1.ConfidenceLevel.MEDIUM);
            expect(signature.version).toBe('1.0');
            expect(signature.createdAt).toBeInstanceOf(Date);
            expect(signature.modifiedAt).toBeInstanceOf(Date);
        });
        it('should create signature with optional properties', () => {
            // Arrange & Act
            const signature = threat_signature_value_object_1.ThreatSignature.create('sig-002', 'Advanced Threat Detection', threat_signature_value_object_1.SignatureType.SNORT, threat_signature_value_object_1.SignatureCategory.EXPLOIT, threat_signature_value_object_1.SignatureSeverity.CRITICAL, 'alert tcp any any -> any 80 (msg:"Exploit detected"; content:"exploit"; sid:1001;)', 'Detects advanced exploitation attempts', 'threat-intel-team', {
                confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
                version: '2.1',
                tags: ['exploit', 'network', 'critical'],
                mitreAttackTechniques: ['T1190', 'T1068'],
                targetPlatforms: ['windows', 'linux'],
                detectionMethod: 'pattern_match',
                detectionParameters: { sensitivity: 'high', threshold: 0.8 },
                source: 'internal-research',
                references: ['https://example.com/threat-report'],
            });
            // Assert
            expect(signature.confidence).toBe(confidence_level_enum_1.ConfidenceLevel.HIGH);
            expect(signature.version).toBe('2.1');
            expect(signature.tags).toEqual(['exploit', 'network', 'critical']);
            expect(signature.mitreAttackTechniques).toEqual(['T1190', 'T1068']);
            expect(signature.targetPlatforms).toEqual(['windows', 'linux']);
            expect(signature.detectionLogic.method).toBe('pattern_match');
            expect(signature.detectionLogic.parameters).toEqual({ sensitivity: 'high', threshold: 0.8 });
            expect(signature.metadata.source).toBe('internal-research');
            expect(signature.metadata.references).toEqual(['https://example.com/threat-report']);
        });
    });
    describe('validation', () => {
        it('should require signature ID', () => {
            expect(() => threat_signature_value_object_1.ThreatSignature.create('', 'Test Signature', threat_signature_value_object_1.SignatureType.YARA, threat_signature_value_object_1.SignatureCategory.MALWARE, threat_signature_value_object_1.SignatureSeverity.MEDIUM, 'rule test {}', 'Test description', 'author')).toThrow('Threat signature must have an ID');
        });
        it('should require signature name', () => {
            expect(() => threat_signature_value_object_1.ThreatSignature.create('sig-001', '', threat_signature_value_object_1.SignatureType.YARA, threat_signature_value_object_1.SignatureCategory.MALWARE, threat_signature_value_object_1.SignatureSeverity.MEDIUM, 'rule test {}', 'Test description', 'author')).toThrow('Threat signature must have a name');
        });
        it('should require valid signature type', () => {
            expect(() => new threat_signature_value_object_1.ThreatSignature({
                id: 'sig-001',
                name: 'Test Signature',
                type: 'invalid',
                category: threat_signature_value_object_1.SignatureCategory.MALWARE,
                severity: threat_signature_value_object_1.SignatureSeverity.MEDIUM,
                content: 'rule test {}',
                description: 'Test description',
                confidence: confidence_level_enum_1.ConfidenceLevel.MEDIUM,
                version: '1.0',
                author: 'author',
                createdAt: new Date(),
                modifiedAt: new Date(),
                tags: [],
                mitreAttackTechniques: [],
                targetPlatforms: [],
                detectionLogic: {
                    method: 'pattern_match',
                    parameters: {},
                    falsePositiveRate: 0.05,
                    truePositiveRate: 0.85,
                },
                performance: {
                    processingTime: 0,
                    memoryUsage: 0,
                    cpuUsage: 0,
                    throughput: 0,
                },
                validation: {
                    isValidated: false,
                },
                usage: {
                    totalDetections: 0,
                    truePositives: 0,
                    falsePositives: 0,
                    detectionRate: 0,
                },
                metadata: {
                    source: 'custom',
                    references: [],
                    relatedSignatures: [],
                },
            })).toThrow('Invalid signature type: invalid');
        });
        it('should require valid signature category', () => {
            expect(() => new threat_signature_value_object_1.ThreatSignature({
                id: 'sig-001',
                name: 'Test Signature',
                type: threat_signature_value_object_1.SignatureType.YARA,
                category: 'invalid',
                severity: threat_signature_value_object_1.SignatureSeverity.MEDIUM,
                content: 'rule test {}',
                description: 'Test description',
                confidence: confidence_level_enum_1.ConfidenceLevel.MEDIUM,
                version: '1.0',
                author: 'author',
                createdAt: new Date(),
                modifiedAt: new Date(),
                tags: [],
                mitreAttackTechniques: [],
                targetPlatforms: [],
                detectionLogic: {
                    method: 'pattern_match',
                    parameters: {},
                    falsePositiveRate: 0.05,
                    truePositiveRate: 0.85,
                },
                performance: {
                    processingTime: 0,
                    memoryUsage: 0,
                    cpuUsage: 0,
                    throughput: 0,
                },
                validation: {
                    isValidated: false,
                },
                usage: {
                    totalDetections: 0,
                    truePositives: 0,
                    falsePositives: 0,
                    detectionRate: 0,
                },
                metadata: {
                    source: 'custom',
                    references: [],
                    relatedSignatures: [],
                },
            })).toThrow('Invalid signature category: invalid');
        });
        it('should require signature content', () => {
            expect(() => threat_signature_value_object_1.ThreatSignature.create('sig-001', 'Test Signature', threat_signature_value_object_1.SignatureType.YARA, threat_signature_value_object_1.SignatureCategory.MALWARE, threat_signature_value_object_1.SignatureSeverity.MEDIUM, '', 'Test description', 'author')).toThrow('Threat signature must have content');
        });
        it('should enforce content length limit', () => {
            const longContent = 'x'.repeat(100001); // Exceeds 100KB limit
            expect(() => threat_signature_value_object_1.ThreatSignature.create('sig-001', 'Test Signature', threat_signature_value_object_1.SignatureType.YARA, threat_signature_value_object_1.SignatureCategory.MALWARE, threat_signature_value_object_1.SignatureSeverity.MEDIUM, longContent, 'Test description', 'author')).toThrow('Signature content exceeds maximum length of 100000 characters');
        });
        it('should require valid confidence level', () => {
            expect(() => new threat_signature_value_object_1.ThreatSignature({
                id: 'sig-001',
                name: 'Test Signature',
                type: threat_signature_value_object_1.SignatureType.YARA,
                category: threat_signature_value_object_1.SignatureCategory.MALWARE,
                severity: threat_signature_value_object_1.SignatureSeverity.MEDIUM,
                content: 'rule test {}',
                description: 'Test description',
                confidence: 'invalid',
                version: '1.0',
                author: 'author',
                createdAt: new Date(),
                modifiedAt: new Date(),
                tags: [],
                mitreAttackTechniques: [],
                targetPlatforms: [],
                detectionLogic: {
                    method: 'pattern_match',
                    parameters: {},
                    falsePositiveRate: 0.05,
                    truePositiveRate: 0.85,
                },
                performance: {
                    processingTime: 0,
                    memoryUsage: 0,
                    cpuUsage: 0,
                    throughput: 0,
                },
                validation: {
                    isValidated: false,
                },
                usage: {
                    totalDetections: 0,
                    truePositives: 0,
                    falsePositives: 0,
                    detectionRate: 0,
                },
                metadata: {
                    source: 'custom',
                    references: [],
                    relatedSignatures: [],
                },
            })).toThrow('Invalid confidence level: invalid');
        });
        it('should validate false positive rate range', () => {
            expect(() => new threat_signature_value_object_1.ThreatSignature({
                id: 'sig-001',
                name: 'Test Signature',
                type: threat_signature_value_object_1.SignatureType.YARA,
                category: threat_signature_value_object_1.SignatureCategory.MALWARE,
                severity: threat_signature_value_object_1.SignatureSeverity.MEDIUM,
                content: 'rule test {}',
                description: 'Test description',
                confidence: confidence_level_enum_1.ConfidenceLevel.MEDIUM,
                version: '1.0',
                author: 'author',
                createdAt: new Date(),
                modifiedAt: new Date(),
                tags: [],
                mitreAttackTechniques: [],
                targetPlatforms: [],
                detectionLogic: {
                    method: 'pattern_match',
                    parameters: {},
                    falsePositiveRate: 1.5, // Invalid: > 1
                    truePositiveRate: 0.85,
                },
                performance: {
                    processingTime: 0,
                    memoryUsage: 0,
                    cpuUsage: 0,
                    throughput: 0,
                },
                validation: {
                    isValidated: false,
                },
                usage: {
                    totalDetections: 0,
                    truePositives: 0,
                    falsePositives: 0,
                    detectionRate: 0,
                },
                metadata: {
                    source: 'custom',
                    references: [],
                    relatedSignatures: [],
                },
            })).toThrow('False positive rate must be between 0 and 1');
        });
        it('should validate performance metrics', () => {
            expect(() => new threat_signature_value_object_1.ThreatSignature({
                id: 'sig-001',
                name: 'Test Signature',
                type: threat_signature_value_object_1.SignatureType.YARA,
                category: threat_signature_value_object_1.SignatureCategory.MALWARE,
                severity: threat_signature_value_object_1.SignatureSeverity.MEDIUM,
                content: 'rule test {}',
                description: 'Test description',
                confidence: confidence_level_enum_1.ConfidenceLevel.MEDIUM,
                version: '1.0',
                author: 'author',
                createdAt: new Date(),
                modifiedAt: new Date(),
                tags: [],
                mitreAttackTechniques: [],
                targetPlatforms: [],
                detectionLogic: {
                    method: 'pattern_match',
                    parameters: {},
                    falsePositiveRate: 0.05,
                    truePositiveRate: 0.85,
                },
                performance: {
                    processingTime: -100, // Invalid: negative
                    memoryUsage: 0,
                    cpuUsage: 0,
                    throughput: 0,
                },
                validation: {
                    isValidated: false,
                },
                usage: {
                    totalDetections: 0,
                    truePositives: 0,
                    falsePositives: 0,
                    detectionRate: 0,
                },
                metadata: {
                    source: 'custom',
                    references: [],
                    relatedSignatures: [],
                },
            })).toThrow('Processing time cannot be negative');
        });
        it('should validate usage statistics', () => {
            expect(() => new threat_signature_value_object_1.ThreatSignature({
                id: 'sig-001',
                name: 'Test Signature',
                type: threat_signature_value_object_1.SignatureType.YARA,
                category: threat_signature_value_object_1.SignatureCategory.MALWARE,
                severity: threat_signature_value_object_1.SignatureSeverity.MEDIUM,
                content: 'rule test {}',
                description: 'Test description',
                confidence: confidence_level_enum_1.ConfidenceLevel.MEDIUM,
                version: '1.0',
                author: 'author',
                createdAt: new Date(),
                modifiedAt: new Date(),
                tags: [],
                mitreAttackTechniques: [],
                targetPlatforms: [],
                detectionLogic: {
                    method: 'pattern_match',
                    parameters: {},
                    falsePositiveRate: 0.05,
                    truePositiveRate: 0.85,
                },
                performance: {
                    processingTime: 0,
                    memoryUsage: 0,
                    cpuUsage: 0,
                    throughput: 0,
                },
                validation: {
                    isValidated: false,
                },
                usage: {
                    totalDetections: 100,
                    truePositives: 150, // Invalid: > totalDetections
                    falsePositives: 0,
                    detectionRate: 0,
                },
                metadata: {
                    source: 'custom',
                    references: [],
                    relatedSignatures: [],
                },
            })).toThrow('True positives must be between 0 and total detections');
        });
    });
    describe('business logic', () => {
        it('should check if signature is validated', () => {
            const unvalidatedSignature = threat_signature_value_object_1.ThreatSignature.create('sig-001', 'Test Signature', threat_signature_value_object_1.SignatureType.YARA, threat_signature_value_object_1.SignatureCategory.MALWARE, threat_signature_value_object_1.SignatureSeverity.MEDIUM, 'rule test {}', 'Test description', 'author');
            const validatedSignature = new threat_signature_value_object_1.ThreatSignature({
                ...unvalidatedSignature.value,
                validation: {
                    isValidated: true,
                    validatedAt: new Date(),
                    validatedBy: 'security-expert',
                },
            });
            expect(unvalidatedSignature.isValidated()).toBe(false);
            expect(validatedSignature.isValidated()).toBe(true);
        });
        it('should check if signature is production ready', () => {
            const productionReadySignature = new threat_signature_value_object_1.ThreatSignature({
                id: 'sig-001',
                name: 'Production Signature',
                type: threat_signature_value_object_1.SignatureType.YARA,
                category: threat_signature_value_object_1.SignatureCategory.MALWARE,
                severity: threat_signature_value_object_1.SignatureSeverity.HIGH,
                content: 'rule test {}',
                description: 'Production ready signature',
                confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
                version: '1.0',
                author: 'author',
                createdAt: new Date(),
                modifiedAt: new Date(),
                tags: [],
                mitreAttackTechniques: [],
                targetPlatforms: [],
                detectionLogic: {
                    method: 'pattern_match',
                    parameters: {},
                    falsePositiveRate: 0.05, // Low false positive rate
                    truePositiveRate: 0.85,
                },
                performance: {
                    processingTime: 0,
                    memoryUsage: 0,
                    cpuUsage: 0,
                    throughput: 0,
                },
                validation: {
                    isValidated: true, // Validated
                },
                usage: {
                    totalDetections: 0,
                    truePositives: 0,
                    falsePositives: 0,
                    detectionRate: 0,
                },
                metadata: {
                    source: 'custom',
                    references: [],
                    relatedSignatures: [],
                },
            });
            expect(productionReadySignature.isProductionReady()).toBe(true);
        });
        it('should check if signature is high performance', () => {
            const highPerformanceSignature = new threat_signature_value_object_1.ThreatSignature({
                id: 'sig-001',
                name: 'High Performance Signature',
                type: threat_signature_value_object_1.SignatureType.YARA,
                category: threat_signature_value_object_1.SignatureCategory.MALWARE,
                severity: threat_signature_value_object_1.SignatureSeverity.MEDIUM,
                content: 'rule test {}',
                description: 'High performance signature',
                confidence: confidence_level_enum_1.ConfidenceLevel.MEDIUM,
                version: '1.0',
                author: 'author',
                createdAt: new Date(),
                modifiedAt: new Date(),
                tags: [],
                mitreAttackTechniques: [],
                targetPlatforms: [],
                detectionLogic: {
                    method: 'pattern_match',
                    parameters: {},
                    falsePositiveRate: 0.05,
                    truePositiveRate: 0.85,
                },
                performance: {
                    processingTime: 50, // < 100ms
                    memoryUsage: 1024,
                    cpuUsage: 5, // < 10%
                    throughput: 2000, // > 1000 events/sec
                },
                validation: {
                    isValidated: false,
                },
                usage: {
                    totalDetections: 0,
                    truePositives: 0,
                    falsePositives: 0,
                    detectionRate: 0,
                },
                metadata: {
                    source: 'custom',
                    references: [],
                    relatedSignatures: [],
                },
            });
            expect(highPerformanceSignature.isHighPerformance()).toBe(true);
        });
        it('should check if signature is effective', () => {
            const effectiveSignature = new threat_signature_value_object_1.ThreatSignature({
                id: 'sig-001',
                name: 'Effective Signature',
                type: threat_signature_value_object_1.SignatureType.YARA,
                category: threat_signature_value_object_1.SignatureCategory.MALWARE,
                severity: threat_signature_value_object_1.SignatureSeverity.MEDIUM,
                content: 'rule test {}',
                description: 'Effective signature',
                confidence: confidence_level_enum_1.ConfidenceLevel.MEDIUM,
                version: '1.0',
                author: 'author',
                createdAt: new Date(),
                modifiedAt: new Date(),
                tags: [],
                mitreAttackTechniques: [],
                targetPlatforms: [],
                detectionLogic: {
                    method: 'pattern_match',
                    parameters: {},
                    falsePositiveRate: 0.05,
                    truePositiveRate: 0.85,
                },
                performance: {
                    processingTime: 0,
                    memoryUsage: 0,
                    cpuUsage: 0,
                    throughput: 0,
                },
                validation: {
                    isValidated: false,
                },
                usage: {
                    totalDetections: 100,
                    truePositives: 85, // 85% accuracy
                    falsePositives: 15,
                    detectionRate: 0,
                },
                metadata: {
                    source: 'custom',
                    references: [],
                    relatedSignatures: [],
                },
            });
            const ineffectiveSignature = new threat_signature_value_object_1.ThreatSignature({
                ...effectiveSignature.value,
                usage: {
                    totalDetections: 0, // No detections
                    truePositives: 0,
                    falsePositives: 0,
                    detectionRate: 0,
                },
            });
            expect(effectiveSignature.isEffective()).toBe(true);
            expect(ineffectiveSignature.isEffective()).toBe(false);
        });
        it('should calculate effectiveness score', () => {
            const signature = new threat_signature_value_object_1.ThreatSignature({
                id: 'sig-001',
                name: 'Test Signature',
                type: threat_signature_value_object_1.SignatureType.YARA,
                category: threat_signature_value_object_1.SignatureCategory.MALWARE,
                severity: threat_signature_value_object_1.SignatureSeverity.MEDIUM,
                content: 'rule test {}',
                description: 'Test signature',
                confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
                version: '1.0',
                author: 'author',
                createdAt: new Date(),
                modifiedAt: new Date(),
                tags: [],
                mitreAttackTechniques: [],
                targetPlatforms: [],
                detectionLogic: {
                    method: 'pattern_match',
                    parameters: {},
                    falsePositiveRate: 0.05,
                    truePositiveRate: 0.85,
                },
                performance: {
                    processingTime: 50,
                    memoryUsage: 1024,
                    cpuUsage: 5,
                    throughput: 2000,
                },
                validation: {
                    isValidated: false,
                },
                usage: {
                    totalDetections: 100,
                    truePositives: 90, // 90% accuracy
                    falsePositives: 10,
                    detectionRate: 0,
                },
                metadata: {
                    source: 'custom',
                    references: [],
                    relatedSignatures: [],
                },
            });
            const effectivenessScore = signature.getEffectivenessScore();
            expect(effectivenessScore).toBeGreaterThan(0);
            expect(effectivenessScore).toBeLessThanOrEqual(100);
        });
        it('should calculate confidence score', () => {
            const highConfidenceSignature = threat_signature_value_object_1.ThreatSignature.create('sig-001', 'High Confidence Signature', threat_signature_value_object_1.SignatureType.YARA, threat_signature_value_object_1.SignatureCategory.MALWARE, threat_signature_value_object_1.SignatureSeverity.MEDIUM, 'rule test {}', 'High confidence signature', 'author', { confidence: confidence_level_enum_1.ConfidenceLevel.HIGH });
            const lowConfidenceSignature = threat_signature_value_object_1.ThreatSignature.create('sig-002', 'Low Confidence Signature', threat_signature_value_object_1.SignatureType.YARA, threat_signature_value_object_1.SignatureCategory.MALWARE, threat_signature_value_object_1.SignatureSeverity.MEDIUM, 'rule test {}', 'Low confidence signature', 'author', { confidence: confidence_level_enum_1.ConfidenceLevel.LOW });
            expect(highConfidenceSignature.getConfidenceScore()).toBeGreaterThan(lowConfidenceSignature.getConfidenceScore());
        });
        it('should calculate signature age', () => {
            const pastDate = new Date(Date.now() - (5 * 24 * 60 * 60 * 1000)); // 5 days ago
            const signature = new threat_signature_value_object_1.ThreatSignature({
                id: 'sig-001',
                name: 'Old Signature',
                type: threat_signature_value_object_1.SignatureType.YARA,
                category: threat_signature_value_object_1.SignatureCategory.MALWARE,
                severity: threat_signature_value_object_1.SignatureSeverity.MEDIUM,
                content: 'rule test {}',
                description: 'Old signature',
                confidence: confidence_level_enum_1.ConfidenceLevel.MEDIUM,
                version: '1.0',
                author: 'author',
                createdAt: pastDate,
                modifiedAt: pastDate,
                tags: [],
                mitreAttackTechniques: [],
                targetPlatforms: [],
                detectionLogic: {
                    method: 'pattern_match',
                    parameters: {},
                    falsePositiveRate: 0.05,
                    truePositiveRate: 0.85,
                },
                performance: {
                    processingTime: 0,
                    memoryUsage: 0,
                    cpuUsage: 0,
                    throughput: 0,
                },
                validation: {
                    isValidated: false,
                },
                usage: {
                    totalDetections: 0,
                    truePositives: 0,
                    falsePositives: 0,
                    detectionRate: 0,
                },
                metadata: {
                    source: 'custom',
                    references: [],
                    relatedSignatures: [],
                },
            });
            expect(signature.getAge()).toBe(5);
        });
        it('should determine if signature needs update', () => {
            const oldIneffectiveSignature = new threat_signature_value_object_1.ThreatSignature({
                id: 'sig-001',
                name: 'Old Ineffective Signature',
                type: threat_signature_value_object_1.SignatureType.YARA,
                category: threat_signature_value_object_1.SignatureCategory.MALWARE,
                severity: threat_signature_value_object_1.SignatureSeverity.MEDIUM,
                content: 'rule test {}',
                description: 'Old ineffective signature',
                confidence: confidence_level_enum_1.ConfidenceLevel.MEDIUM,
                version: '1.0',
                author: 'author',
                createdAt: new Date(Date.now() - (400 * 24 * 60 * 60 * 1000)), // 400 days ago
                modifiedAt: new Date(Date.now() - (400 * 24 * 60 * 60 * 1000)),
                tags: [],
                mitreAttackTechniques: [],
                targetPlatforms: [],
                detectionLogic: {
                    method: 'pattern_match',
                    parameters: {},
                    falsePositiveRate: 0.05,
                    truePositiveRate: 0.85,
                },
                performance: {
                    processingTime: 0,
                    memoryUsage: 0,
                    cpuUsage: 0,
                    throughput: 0,
                },
                validation: {
                    isValidated: false,
                },
                usage: {
                    totalDetections: 100,
                    truePositives: 30, // 30% accuracy - poor effectiveness
                    falsePositives: 70,
                    detectionRate: 0,
                },
                metadata: {
                    source: 'custom',
                    references: [],
                    relatedSignatures: [],
                },
            });
            expect(oldIneffectiveSignature.needsUpdate()).toBe(true);
        });
        it('should provide quality rating', () => {
            const excellentSignature = new threat_signature_value_object_1.ThreatSignature({
                id: 'sig-001',
                name: 'Excellent Signature',
                type: threat_signature_value_object_1.SignatureType.YARA,
                category: threat_signature_value_object_1.SignatureCategory.MALWARE,
                severity: threat_signature_value_object_1.SignatureSeverity.MEDIUM,
                content: 'rule test {}',
                description: 'Excellent signature',
                confidence: confidence_level_enum_1.ConfidenceLevel.CONFIRMED,
                version: '1.0',
                author: 'author',
                createdAt: new Date(),
                modifiedAt: new Date(),
                tags: [],
                mitreAttackTechniques: [],
                targetPlatforms: [],
                detectionLogic: {
                    method: 'pattern_match',
                    parameters: {},
                    falsePositiveRate: 0.01,
                    truePositiveRate: 0.95,
                },
                performance: {
                    processingTime: 10,
                    memoryUsage: 512,
                    cpuUsage: 2,
                    throughput: 5000,
                },
                validation: {
                    isValidated: true,
                },
                usage: {
                    totalDetections: 1000,
                    truePositives: 950,
                    falsePositives: 50,
                    detectionRate: 0,
                },
                metadata: {
                    source: 'custom',
                    references: [],
                    relatedSignatures: [],
                },
            });
            expect(excellentSignature.getQualityRating()).toBe('excellent');
        });
        it('should provide recommended actions', () => {
            const unvalidatedSignature = threat_signature_value_object_1.ThreatSignature.create('sig-001', 'Unvalidated Signature', threat_signature_value_object_1.SignatureType.YARA, threat_signature_value_object_1.SignatureCategory.MALWARE, threat_signature_value_object_1.SignatureSeverity.MEDIUM, 'rule test {}', 'Unvalidated signature', 'author');
            const actions = unvalidatedSignature.getRecommendedActions();
            expect(actions).toContain('Validate signature');
        });
    });
    describe('serialization', () => {
        it('should serialize to JSON correctly', () => {
            const signature = threat_signature_value_object_1.ThreatSignature.create('sig-001', 'Test Signature', threat_signature_value_object_1.SignatureType.YARA, threat_signature_value_object_1.SignatureCategory.MALWARE, threat_signature_value_object_1.SignatureSeverity.HIGH, 'rule test {}', 'Test signature for serialization', 'security-team', {
                tags: ['test', 'malware'],
                mitreAttackTechniques: ['T1055'],
            });
            const json = signature.toJSON();
            expect(json.id).toBe('sig-001');
            expect(json.name).toBe('Test Signature');
            expect(json.type).toBe(threat_signature_value_object_1.SignatureType.YARA);
            expect(json.category).toBe(threat_signature_value_object_1.SignatureCategory.MALWARE);
            expect(json.severity).toBe(threat_signature_value_object_1.SignatureSeverity.HIGH);
            expect(json.content).toBe('rule test {}');
            expect(json.description).toBe('Test signature for serialization');
            expect(json.author).toBe('security-team');
            expect(json.tags).toEqual(['test', 'malware']);
            expect(json.mitreAttackTechniques).toEqual(['T1055']);
            expect(json.analysis).toBeDefined();
            expect(json.analysis.isValidated).toBe(false);
            expect(json.analysis.effectivenessScore).toBeDefined();
            expect(json.analysis.qualityRating).toBeDefined();
        });
        it('should deserialize from JSON correctly', () => {
            const originalSignature = threat_signature_value_object_1.ThreatSignature.create('sig-002', 'Original Signature', threat_signature_value_object_1.SignatureType.SNORT, threat_signature_value_object_1.SignatureCategory.EXPLOIT, threat_signature_value_object_1.SignatureSeverity.CRITICAL, 'alert tcp any any -> any 80', 'Original signature for deserialization', 'threat-team');
            const json = originalSignature.toJSON();
            const deserializedSignature = threat_signature_value_object_1.ThreatSignature.fromJSON(json);
            expect(deserializedSignature.id).toBe(originalSignature.id);
            expect(deserializedSignature.name).toBe(originalSignature.name);
            expect(deserializedSignature.type).toBe(originalSignature.type);
            expect(deserializedSignature.category).toBe(originalSignature.category);
            expect(deserializedSignature.severity).toBe(originalSignature.severity);
            expect(deserializedSignature.content).toBe(originalSignature.content);
            expect(deserializedSignature.description).toBe(originalSignature.description);
            expect(deserializedSignature.author).toBe(originalSignature.author);
        });
    });
    describe('immutability', () => {
        it('should return defensive copies of arrays', () => {
            const signature = threat_signature_value_object_1.ThreatSignature.create('sig-001', 'Test Signature', threat_signature_value_object_1.SignatureType.YARA, threat_signature_value_object_1.SignatureCategory.MALWARE, threat_signature_value_object_1.SignatureSeverity.MEDIUM, 'rule test {}', 'Test signature', 'author', {
                tags: ['original', 'tag'],
                mitreAttackTechniques: ['T1055'],
                targetPlatforms: ['windows'],
            });
            const tags = signature.tags;
            const techniques = signature.mitreAttackTechniques;
            const platforms = signature.targetPlatforms;
            // Modify the returned arrays
            tags.push('modified');
            techniques.push('T9999');
            platforms.push('modified');
            // Original signature should be unchanged
            expect(signature.tags).toEqual(['original', 'tag']);
            expect(signature.mitreAttackTechniques).toEqual(['T1055']);
            expect(signature.targetPlatforms).toEqual(['windows']);
        });
        it('should return defensive copies of objects', () => {
            const signature = threat_signature_value_object_1.ThreatSignature.create('sig-001', 'Test Signature', threat_signature_value_object_1.SignatureType.YARA, threat_signature_value_object_1.SignatureCategory.MALWARE, threat_signature_value_object_1.SignatureSeverity.MEDIUM, 'rule test {}', 'Test signature', 'author');
            const detectionLogic = signature.detectionLogic;
            const performance = signature.performance;
            const validation = signature.validation;
            const usage = signature.usage;
            const metadata = signature.metadata;
            // Modify the returned objects
            detectionLogic.falsePositiveRate = 0.99;
            performance.processingTime = 9999;
            validation.isValidated = true;
            usage.totalDetections = 9999;
            metadata.source = 'modified';
            // Original signature should be unchanged
            expect(signature.detectionLogic.falsePositiveRate).toBe(0.05);
            expect(signature.performance.processingTime).toBe(0);
            expect(signature.validation.isValidated).toBe(false);
            expect(signature.usage.totalDetections).toBe(0);
            expect(signature.metadata.source).toBe('custom');
        });
    });
    describe('edge cases', () => {
        it('should handle empty arrays and objects gracefully', () => {
            const signature = threat_signature_value_object_1.ThreatSignature.create('sig-001', 'Minimal Signature', threat_signature_value_object_1.SignatureType.CUSTOM, threat_signature_value_object_1.SignatureCategory.MALWARE, threat_signature_value_object_1.SignatureSeverity.LOW, 'minimal rule', 'Minimal signature with no optional data', 'author');
            expect(signature.tags).toEqual([]);
            expect(signature.mitreAttackTechniques).toEqual([]);
            expect(signature.targetPlatforms).toEqual([]);
            expect(signature.metadata.references).toEqual([]);
            expect(signature.metadata.relatedSignatures).toEqual([]);
        });
        it('should handle whitespace in string inputs', () => {
            const signature = threat_signature_value_object_1.ThreatSignature.create('  sig-001  ', '  Test Signature  ', threat_signature_value_object_1.SignatureType.YARA, threat_signature_value_object_1.SignatureCategory.MALWARE, threat_signature_value_object_1.SignatureSeverity.MEDIUM, '  rule test {}  ', '  Test description  ', '  author  ');
            expect(signature.id).toBe('sig-001');
            expect(signature.name).toBe('Test Signature');
            expect(signature.content).toBe('rule test {}');
            expect(signature.description).toBe('Test description');
            expect(signature.author).toBe('author');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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