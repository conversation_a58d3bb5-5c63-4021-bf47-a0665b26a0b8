97d817b0625a64ee77125b945cc7186e
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const forbidden_exception_1 = require("../../exceptions/forbidden.exception");
describe('ForbiddenException', () => {
    describe('constructor', () => {
        it('should create exception with default message', () => {
            const exception = new forbidden_exception_1.ForbiddenException();
            expect(exception).toBeInstanceOf(forbidden_exception_1.ForbiddenException);
            expect(exception.message).toBe('Access to this resource is forbidden');
            expect(exception.code).toBe('FORBIDDEN');
            expect(exception.severity).toBe('medium');
            expect(exception.category).toBe('authorization');
        });
        it('should create exception with custom message and options', () => {
            const exception = new forbidden_exception_1.ForbiddenException('Custom forbidden message', {
                resource: 'User',
                action: 'delete',
                requiredPermissions: ['user:delete'],
                userPermissions: ['user:read'],
                correlationId: 'test-correlation-id',
            });
            expect(exception.message).toBe('Custom forbidden message');
            expect(exception.resource).toBe('User');
            expect(exception.action).toBe('delete');
            expect(exception.requiredPermissions).toEqual(['user:delete']);
            expect(exception.userPermissions).toEqual(['user:read']);
            expect(exception.correlationId).toBe('test-correlation-id');
        });
    });
    describe('static factory methods', () => {
        describe('insufficientPermissions', () => {
            it('should create exception for insufficient permissions', () => {
                const requiredPermissions = ['user:delete', 'user:admin'];
                const userPermissions = ['user:read', 'user:update'];
                const exception = forbidden_exception_1.ForbiddenException.insufficientPermissions('User', 'delete', requiredPermissions, userPermissions);
                expect(exception.message).toBe('Insufficient permissions to delete User. Missing: user:delete, user:admin');
                expect(exception.resource).toBe('User');
                expect(exception.action).toBe('delete');
                expect(exception.requiredPermissions).toEqual(requiredPermissions);
                expect(exception.userPermissions).toEqual(userPermissions);
                expect(exception.getMissingPermissions()).toEqual(['user:delete', 'user:admin']);
                expect(exception.isPermissionBased()).toBe(true);
            });
            it('should handle empty user permissions', () => {
                const exception = forbidden_exception_1.ForbiddenException.insufficientPermissions('Document', 'read', ['doc:read']);
                expect(exception.getMissingPermissions()).toEqual(['doc:read']);
            });
        });
        describe('insufficientRoles', () => {
            it('should create exception for insufficient roles', () => {
                const requiredRoles = ['admin', 'moderator'];
                const userRoles = ['user'];
                const exception = forbidden_exception_1.ForbiddenException.insufficientRoles('System', 'configure', requiredRoles, userRoles);
                expect(exception.message).toBe('Insufficient roles to configure System. Required: admin or moderator');
                expect(exception.resource).toBe('System');
                expect(exception.action).toBe('configure');
                expect(exception.requiredRoles).toEqual(requiredRoles);
                expect(exception.userRoles).toEqual(userRoles);
                expect(exception.isRoleBased()).toBe(true);
            });
            it('should throw error if user has required roles', () => {
                expect(() => {
                    forbidden_exception_1.ForbiddenException.insufficientRoles('System', 'configure', ['admin'], ['admin', 'user']);
                }).toThrow('User has required roles - this should not create a ForbiddenException');
            });
        });
        describe('resourceAccessDenied', () => {
            it('should create exception for resource access denial with ID', () => {
                const exception = forbidden_exception_1.ForbiddenException.resourceAccessDenied('Document', 'doc-123', {
                    reason: 'private_document',
                });
                expect(exception.message).toBe("Access denied to Document 'doc-123'");
                expect(exception.resource).toBe('Document');
                expect(exception.context.resourceId).toBe('doc-123');
                expect(exception.context.reason).toBe('private_document');
            });
            it('should create exception for resource access denial without ID', () => {
                const exception = forbidden_exception_1.ForbiddenException.resourceAccessDenied('Documents');
                expect(exception.message).toBe('Access denied to Documents');
                expect(exception.resource).toBe('Documents');
            });
        });
        describe('actionNotAllowed', () => {
            it('should create exception for action not allowed with resource', () => {
                const exception = forbidden_exception_1.ForbiddenException.actionNotAllowed('delete', 'User', {
                    reason: 'system_user',
                    allowedActions: ['read', 'update'],
                });
                expect(exception.message).toBe("Action 'delete' is not allowed on User");
                expect(exception.action).toBe('delete');
                expect(exception.resource).toBe('User');
                expect(exception.context.reason).toBe('system_user');
                expect(exception.getAllowedActions()).toEqual(['read', 'update']);
            });
            it('should create exception for action not allowed without resource', () => {
                const exception = forbidden_exception_1.ForbiddenException.actionNotAllowed('export');
                expect(exception.message).toBe("Action 'export' is not allowed");
                expect(exception.action).toBe('export');
            });
        });
        describe('ownershipRequired', () => {
            it('should create exception for ownership requirement', () => {
                const exception = forbidden_exception_1.ForbiddenException.ownershipRequired('Document', 'doc-123', 'user-456', 'user-789');
                expect(exception.message).toBe("Only the owner can access Document 'doc-123'");
                expect(exception.resource).toBe('Document');
                expect(exception.context.resourceId).toBe('doc-123');
                expect(exception.context.userId).toBe('user-456');
                expect(exception.context.ownerId).toBe('user-789');
                expect(exception.isOwnershipBased()).toBe(true);
                const ownershipInfo = exception.getOwnershipInfo();
                expect(ownershipInfo).toEqual({
                    userId: 'user-456',
                    ownerId: 'user-789',
                });
            });
        });
        describe('tenantIsolationViolation', () => {
            it('should create exception for tenant isolation violation', () => {
                const exception = forbidden_exception_1.ForbiddenException.tenantIsolationViolation('Data', 'data-123', 'tenant-456', 'tenant-789');
                expect(exception.message).toBe("Access denied: Data 'data-123' belongs to a different tenant");
                expect(exception.resource).toBe('Data');
                expect(exception.context.resourceId).toBe('data-123');
                expect(exception.context.userTenantId).toBe('tenant-456');
                expect(exception.context.resourceTenantId).toBe('tenant-789');
                expect(exception.isTenantIsolationViolation()).toBe(true);
                const tenantInfo = exception.getTenantInfo();
                expect(tenantInfo).toEqual({
                    userTenantId: 'tenant-456',
                    resourceTenantId: 'tenant-789',
                });
            });
        });
        describe('timeRestricted', () => {
            it('should create exception for time-based access restriction', () => {
                const currentTime = new Date('2023-01-01T22:00:00Z');
                const exception = forbidden_exception_1.ForbiddenException.timeRestricted('System', 'backup', {
                    allowedTimeStart: '09:00',
                    allowedTimeEnd: '17:00',
                    currentTime,
                });
                expect(exception.message).toBe('Access to System for backup is restricted at this time');
                expect(exception.resource).toBe('System');
                expect(exception.action).toBe('backup');
                expect(exception.isTimeRestricted()).toBe(true);
                const timeInfo = exception.getTimeRestrictionInfo();
                expect(timeInfo).toEqual({
                    allowedTimeStart: '09:00',
                    allowedTimeEnd: '17:00',
                    currentTime,
                });
            });
        });
    });
    describe('type checking methods', () => {
        it('should correctly identify permission-based restrictions', () => {
            const exception = forbidden_exception_1.ForbiddenException.insufficientPermissions('User', 'delete', ['user:delete']);
            expect(exception.isPermissionBased()).toBe(true);
            expect(exception.isRoleBased()).toBe(false);
            expect(exception.isOwnershipBased()).toBe(false);
            expect(exception.isTenantIsolationViolation()).toBe(false);
            expect(exception.isTimeRestricted()).toBe(false);
        });
        it('should correctly identify role-based restrictions', () => {
            const exception = forbidden_exception_1.ForbiddenException.insufficientRoles('System', 'configure', ['admin'], ['user']);
            expect(exception.isPermissionBased()).toBe(false);
            expect(exception.isRoleBased()).toBe(true);
            expect(exception.isOwnershipBased()).toBe(false);
            expect(exception.isTenantIsolationViolation()).toBe(false);
            expect(exception.isTimeRestricted()).toBe(false);
        });
        it('should correctly identify ownership-based restrictions', () => {
            const exception = forbidden_exception_1.ForbiddenException.ownershipRequired('Doc', 'doc-1', 'user-1', 'user-2');
            expect(exception.isPermissionBased()).toBe(false);
            expect(exception.isRoleBased()).toBe(false);
            expect(exception.isOwnershipBased()).toBe(true);
            expect(exception.isTenantIsolationViolation()).toBe(false);
            expect(exception.isTimeRestricted()).toBe(false);
        });
    });
    describe('getMissingPermissions', () => {
        it('should return missing permissions', () => {
            const exception = forbidden_exception_1.ForbiddenException.insufficientPermissions('User', 'delete', ['user:delete', 'user:admin'], ['user:read', 'user:update']);
            expect(exception.getMissingPermissions()).toEqual(['user:delete', 'user:admin']);
        });
        it('should return empty array when no permissions specified', () => {
            const exception = new forbidden_exception_1.ForbiddenException();
            expect(exception.getMissingPermissions()).toEqual([]);
        });
    });
    describe('getMissingRoles', () => {
        it('should return missing roles', () => {
            const exception = forbidden_exception_1.ForbiddenException.insufficientRoles('System', 'configure', ['admin', 'moderator'], ['user']);
            expect(exception.getMissingRoles()).toEqual(['admin', 'moderator']);
        });
        it('should return required roles when no user roles specified', () => {
            const exception = new forbidden_exception_1.ForbiddenException('Test', {
                requiredRoles: ['admin'],
            });
            expect(exception.getMissingRoles()).toEqual(['admin']);
        });
    });
    describe('getUserMessage', () => {
        it('should return user-friendly message for ownership requirement', () => {
            const exception = forbidden_exception_1.ForbiddenException.ownershipRequired('Doc', 'doc-1', 'user-1', 'user-2');
            expect(exception.getUserMessage()).toBe('You can only access resources that you own');
        });
        it('should return user-friendly message for tenant isolation', () => {
            const exception = forbidden_exception_1.ForbiddenException.tenantIsolationViolation('Data', 'data-1', 'tenant-1', 'tenant-2');
            expect(exception.getUserMessage()).toBe('You can only access resources within your organization');
        });
        it('should return user-friendly message for time restriction', () => {
            const exception = forbidden_exception_1.ForbiddenException.timeRestricted('System', 'backup');
            expect(exception.getUserMessage()).toBe('Access to this resource is not allowed at this time');
        });
        it('should return user-friendly message for permission-based restriction', () => {
            const exception = forbidden_exception_1.ForbiddenException.insufficientPermissions('User', 'delete', ['user:delete']);
            expect(exception.getUserMessage()).toBe('You do not have the required permissions to perform this action');
        });
        it('should return user-friendly message for role-based restriction', () => {
            const exception = forbidden_exception_1.ForbiddenException.insufficientRoles('System', 'configure', ['admin'], ['user']);
            expect(exception.getUserMessage()).toBe('You do not have the required role to perform this action');
        });
        it('should return default message for general restriction', () => {
            const exception = new forbidden_exception_1.ForbiddenException();
            expect(exception.getUserMessage()).toBe('You do not have permission to access this resource');
        });
    });
    describe('toApiResponse', () => {
        it('should convert to API response format', () => {
            const exception = forbidden_exception_1.ForbiddenException.insufficientPermissions('User', 'delete', ['user:delete'], ['user:read']);
            const response = exception.toApiResponse();
            expect(response).toEqual({
                error: 'You do not have the required permissions to perform this action',
                code: 'FORBIDDEN',
                details: {
                    resource: 'User',
                    action: 'delete',
                    reason: 'insufficient_privileges',
                    missingPermissions: ['user:delete'],
                    missingRoles: [],
                    allowedActions: [],
                },
            });
        });
    });
    describe('toJSON', () => {
        it('should convert to JSON with detailed information', () => {
            const exception = forbidden_exception_1.ForbiddenException.insufficientPermissions('User', 'delete', ['user:delete', 'user:admin'], ['user:read']);
            const json = exception.toJSON();
            expect(json).toMatchObject({
                name: 'ForbiddenException',
                code: 'FORBIDDEN',
                severity: 'medium',
                category: 'authorization',
                resource: 'User',
                action: 'delete',
                requiredPermissions: ['user:delete', 'user:admin'],
                userPermissions: ['user:read'],
                missingPermissions: ['user:delete', 'user:admin'],
                missingRoles: [],
                reason: 'insufficient_privileges',
                isPermissionBased: true,
                isRoleBased: false,
                isOwnershipBased: false,
                isTenantIsolationViolation: false,
                isTimeRestricted: false,
                ownershipInfo: null,
                tenantInfo: null,
                timeRestrictionInfo: null,
                allowedActions: [],
            });
        });
    });
    describe('inheritance', () => {
        it('should be instance of Error and DomainException', () => {
            const exception = new forbidden_exception_1.ForbiddenException();
            expect(exception).toBeInstanceOf(Error);
            expect(exception.name).toBe('ForbiddenException');
            expect(exception.code).toBe('FORBIDDEN');
        });
        it('should maintain proper prototype chain', () => {
            const exception = new forbidden_exception_1.ForbiddenException();
            expect(exception instanceof forbidden_exception_1.ForbiddenException).toBe(true);
            expect(exception instanceof Error).toBe(true);
        });
    });
    describe('context and correlation', () => {
        it('should preserve context and correlation ID', () => {
            const context = { sessionId: 'session123', ipAddress: '***********' };
            const correlationId = 'correlation-456';
            const exception = new forbidden_exception_1.ForbiddenException('Test message', {
                context,
                correlationId,
            });
            expect(exception.context).toMatchObject(context);
            expect(exception.correlationId).toBe(correlationId);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************