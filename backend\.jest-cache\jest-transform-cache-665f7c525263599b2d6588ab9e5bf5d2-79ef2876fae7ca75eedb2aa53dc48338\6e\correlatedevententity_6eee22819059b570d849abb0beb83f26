9d473c43695e715335fbdf79ac763dce
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CorrelatedEvent = exports.CorrelationMatchType = exports.CorrelationRuleType = exports.CorrelationStatus = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
const event_severity_enum_1 = require("../enums/event-severity.enum");
const correlation_status_enum_1 = require("../enums/correlation-status.enum");
Object.defineProperty(exports, "CorrelationStatus", { enumerable: true, get: function () { return correlation_status_enum_1.CorrelationStatus; } });
const correlated_event_created_domain_event_1 = require("../events/correlated-event-created.domain-event");
const correlated_event_status_changed_domain_event_1 = require("../events/correlated-event-status-changed.domain-event");
const correlated_event_correlation_failed_domain_event_1 = require("../events/correlated-event-correlation-failed.domain-event");
/**
 * Correlation Rule Type Enum
 */
var CorrelationRuleType;
(function (CorrelationRuleType) {
    CorrelationRuleType["TEMPORAL"] = "TEMPORAL";
    CorrelationRuleType["SPATIAL"] = "SPATIAL";
    CorrelationRuleType["PATTERN"] = "PATTERN";
    CorrelationRuleType["BEHAVIORAL"] = "BEHAVIORAL";
    CorrelationRuleType["SIGNATURE"] = "SIGNATURE";
    CorrelationRuleType["STATISTICAL"] = "STATISTICAL";
    CorrelationRuleType["SEMANTIC"] = "SEMANTIC";
    CorrelationRuleType["CAUSAL"] = "CAUSAL";
})(CorrelationRuleType || (exports.CorrelationRuleType = CorrelationRuleType = {}));
/**
 * Correlation Match Type Enum
 */
var CorrelationMatchType;
(function (CorrelationMatchType) {
    CorrelationMatchType["EXACT"] = "EXACT";
    CorrelationMatchType["PARTIAL"] = "PARTIAL";
    CorrelationMatchType["FUZZY"] = "FUZZY";
    CorrelationMatchType["PATTERN"] = "PATTERN";
    CorrelationMatchType["TEMPORAL"] = "TEMPORAL";
    CorrelationMatchType["SPATIAL"] = "SPATIAL";
    CorrelationMatchType["SEMANTIC"] = "SEMANTIC";
    CorrelationMatchType["STATISTICAL"] = "STATISTICAL";
})(CorrelationMatchType || (exports.CorrelationMatchType = CorrelationMatchType = {}));
/**
 * CorrelatedEvent Entity
 *
 * Represents a security event that has been processed through the correlation pipeline.
 * Correlated events have relationships with other events and provide context about
 * attack patterns, sequences, and coordinated activities.
 *
 * Key responsibilities:
 * - Maintain correlated event state and lifecycle
 * - Enforce correlation business rules and data quality standards
 * - Track correlation process and applied rules
 * - Generate domain events for correlation state changes
 * - Support attack chain analysis and pattern recognition
 * - Manage manual review workflow for complex correlations
 * - Maintain relationships with related events
 *
 * Business Rules:
 * - Correlated events must reference a valid enriched event
 * - Correlation matches must include confidence scores and rule attribution
 * - Attack chains must have chronological ordering and valid stages
 * - High-confidence correlations may trigger automated responses
 * - Correlation attempts are tracked and limited
 * - Failed correlation must preserve data integrity and provide fallback
 */
class CorrelatedEvent extends shared_kernel_1.BaseAggregateRoot {
    constructor(props, id) {
        super(props, id);
        this.validateInvariants();
    }
    /**
     * Create a new CorrelatedEvent
     */
    static create(props, id) {
        const correlatedEvent = new CorrelatedEvent(props, id);
        // Add domain event for correlated event creation
        correlatedEvent.addDomainEvent(new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(correlatedEvent.id, {
            enrichedEventId: props.enrichedEventId,
            eventType: props.type,
            severity: props.severity,
            correlationStatus: props.correlationStatus,
            correlationQualityScore: props.correlationQualityScore,
            appliedRulesCount: props.appliedRules.length,
            correlationMatchesCount: props.correlationMatches.length,
            relatedEventsCount: props.relatedEventIds.length,
            confidenceLevel: props.confidenceLevel,
            hasAttackChain: !!props.attackChain,
            requiresManualReview: props.requiresManualReview || false,
        }));
        return correlatedEvent;
    }
    validateInvariants() {
        super.validateInvariants();
        if (!this.props.enrichedEventId) {
            throw new Error('CorrelatedEvent must reference an enriched event');
        }
        if (!this.props.metadata) {
            throw new Error('CorrelatedEvent must have metadata');
        }
        if (!this.props.type) {
            throw new Error('CorrelatedEvent must have a type');
        }
        if (!this.props.severity) {
            throw new Error('CorrelatedEvent must have a severity');
        }
        if (!this.props.status) {
            throw new Error('CorrelatedEvent must have a status');
        }
        if (!this.props.processingStatus) {
            throw new Error('CorrelatedEvent must have a processing status');
        }
        if (!this.props.correlationStatus) {
            throw new Error('CorrelatedEvent must have a correlation status');
        }
        if (!this.props.enrichedData) {
            throw new Error('CorrelatedEvent must have enriched data');
        }
        if (!this.props.correlatedData) {
            throw new Error('CorrelatedEvent must have correlated data');
        }
        if (!this.props.title || this.props.title.trim().length === 0) {
            throw new Error('CorrelatedEvent must have a non-empty title');
        }
        if (!this.props.confidenceLevel) {
            throw new Error('CorrelatedEvent must have a confidence level');
        }
        if (!this.props.correlationId || this.props.correlationId.trim().length === 0) {
            throw new Error('CorrelatedEvent must have a correlation ID');
        }
        if (!Array.isArray(this.props.childEventIds)) {
            throw new Error('CorrelatedEvent must have child event IDs array');
        }
        if (!Array.isArray(this.props.appliedRules)) {
            throw new Error('CorrelatedEvent must have applied rules array');
        }
        if (!Array.isArray(this.props.correlationMatches)) {
            throw new Error('CorrelatedEvent must have correlation matches array');
        }
        if (!Array.isArray(this.props.relatedEventIds)) {
            throw new Error('CorrelatedEvent must have related event IDs array');
        }
        if (!Array.isArray(this.props.correlationPatterns)) {
            throw new Error('CorrelatedEvent must have correlation patterns array');
        }
        if (this.props.correlationMatches.length > CorrelatedEvent.MAX_CORRELATION_MATCHES) {
            throw new Error(`CorrelatedEvent cannot have more than ${CorrelatedEvent.MAX_CORRELATION_MATCHES} correlation matches`);
        }
        if (this.props.relatedEventIds.length > CorrelatedEvent.MAX_RELATED_EVENTS) {
            throw new Error(`CorrelatedEvent cannot have more than ${CorrelatedEvent.MAX_RELATED_EVENTS} related events`);
        }
        if (this.props.correlationQualityScore !== undefined &&
            (this.props.correlationQualityScore < 0 || this.props.correlationQualityScore > 100)) {
            throw new Error('Correlation quality score must be between 0 and 100');
        }
        if (this.props.riskScore !== undefined &&
            (this.props.riskScore < 0 || this.props.riskScore > 100)) {
            throw new Error('Risk score must be between 0 and 100');
        }
        if (this.props.correlationAttempts !== undefined && this.props.correlationAttempts < 0) {
            throw new Error('Correlation attempts cannot be negative');
        }
        if (this.props.validationErrors &&
            this.props.validationErrors.length > CorrelatedEvent.MAX_VALIDATION_ERRORS) {
            throw new Error(`Cannot have more than ${CorrelatedEvent.MAX_VALIDATION_ERRORS} validation errors`);
        }
        // Validate correlation status consistency
        this.validateCorrelationStatusConsistency();
        // Validate correlation data integrity
        this.validateCorrelationDataIntegrity();
        // Validate attack chain if present
        if (this.props.attackChain) {
            this.validateAttackChain();
        }
    }
    validateCorrelationStatusConsistency() {
        // If correlation is completed, it should have completion timestamp
        if (this.props.correlationStatus === correlation_status_enum_1.CorrelationStatus.COMPLETED) {
            if (!this.props.correlationCompletedAt) {
                throw new Error('Completed correlation must have completion timestamp');
            }
            if (!this.props.correlationResult) {
                throw new Error('Completed correlation must have result');
            }
        }
        // If correlation failed, it should have error information
        if (this.props.correlationStatus === correlation_status_enum_1.CorrelationStatus.FAILED) {
            if (!this.props.lastCorrelationError &&
                (!this.props.correlationResult || this.props.correlationResult.errors.length === 0)) {
                throw new Error('Failed correlation must have error information');
            }
        }
        // If correlation is in progress, it should have started timestamp
        if (this.props.correlationStatus === correlation_status_enum_1.CorrelationStatus.IN_PROGRESS) {
            if (!this.props.correlationStartedAt) {
                throw new Error('In-progress correlation must have start timestamp');
            }
        }
        // Manual review consistency
        if (this.props.requiresManualReview && this.props.reviewedAt) {
            if (!this.props.reviewedBy) {
                throw new Error('Reviewed events must have reviewer information');
            }
        }
    }
    validateCorrelationDataIntegrity() {
        // Validate correlation matches
        for (const match of this.props.correlationMatches) {
            if (!match.eventId) {
                throw new Error('Correlation match must have an event ID');
            }
            if (match.confidence < 0 || match.confidence > 100) {
                throw new Error('Correlation match confidence must be between 0 and 100');
            }
            if (!match.ruleId) {
                throw new Error('Correlation match must have a rule ID');
            }
            if (!match.timestamp) {
                throw new Error('Correlation match must have a timestamp');
            }
            if (match.weight < 0 || match.weight > 1) {
                throw new Error('Correlation match weight must be between 0 and 1');
            }
        }
        // Validate temporal correlation if present
        if (this.props.temporalCorrelation) {
            const temporal = this.props.temporalCorrelation;
            if (temporal.timeWindow <= 0) {
                throw new Error('Temporal correlation time window must be positive');
            }
            if (temporal.confidence < 0 || temporal.confidence > 100) {
                throw new Error('Temporal correlation confidence must be between 0 and 100');
            }
        }
        // Validate spatial correlation if present
        if (this.props.spatialCorrelation) {
            const spatial = this.props.spatialCorrelation;
            if (spatial.confidence < 0 || spatial.confidence > 100) {
                throw new Error('Spatial correlation confidence must be between 0 and 100');
            }
        }
        // Validate behavioral correlation if present
        if (this.props.behavioralCorrelation) {
            const behavioral = this.props.behavioralCorrelation;
            if (behavioral.anomalyScore < 0 || behavioral.anomalyScore > 100) {
                throw new Error('Behavioral correlation anomaly score must be between 0 and 100');
            }
            if (behavioral.confidence < 0 || behavioral.confidence > 100) {
                throw new Error('Behavioral correlation confidence must be between 0 and 100');
            }
        }
    }
    validateAttackChain() {
        const attackChain = this.props.attackChain;
        if (!attackChain.id || attackChain.id.trim().length === 0) {
            throw new Error('Attack chain must have an ID');
        }
        if (!attackChain.name || attackChain.name.trim().length === 0) {
            throw new Error('Attack chain must have a name');
        }
        if (!Array.isArray(attackChain.stages) || attackChain.stages.length === 0) {
            throw new Error('Attack chain must have at least one stage');
        }
        if (!attackChain.timeline) {
            throw new Error('Attack chain must have timeline information');
        }
        // Validate stages are in chronological order
        for (let i = 1; i < attackChain.stages.length; i++) {
            const prevStage = attackChain.stages[i - 1];
            const currentStage = attackChain.stages[i];
            if (currentStage.order <= prevStage.order) {
                throw new Error('Attack chain stages must be in chronological order');
            }
            if (currentStage.timestamp < prevStage.timestamp) {
                throw new Error('Attack chain stage timestamps must be chronological');
            }
        }
        // Validate timeline consistency
        const timeline = attackChain.timeline;
        if (timeline.endTime < timeline.startTime) {
            throw new Error('Attack chain end time must be after start time');
        }
        const calculatedDuration = timeline.endTime.getTime() - timeline.startTime.getTime();
        if (Math.abs(timeline.duration - calculatedDuration) > 1000) { // Allow 1 second tolerance
            throw new Error('Attack chain duration must match start and end times');
        }
    }
    // Getters
    get enrichedEventId() {
        return this.props.enrichedEventId;
    }
    get metadata() {
        return this.props.metadata;
    }
    get type() {
        return this.props.type;
    }
    get severity() {
        return this.props.severity;
    }
    get status() {
        return this.props.status;
    }
    get processingStatus() {
        return this.props.processingStatus;
    }
    get correlationStatus() {
        return this.props.correlationStatus;
    }
    get enrichedData() {
        return { ...this.props.enrichedData };
    }
    get correlatedData() {
        return { ...this.props.correlatedData };
    }
    get title() {
        return this.props.title;
    }
    get description() {
        return this.props.description;
    }
    get tags() {
        return this.props.tags ? [...this.props.tags] : [];
    }
    get riskScore() {
        return this.props.riskScore;
    }
    get confidenceLevel() {
        return this.props.confidenceLevel;
    }
    get attributes() {
        return this.props.attributes ? { ...this.props.attributes } : {};
    }
    get correlationId() {
        return this.props.correlationId;
    }
    get parentEventId() {
        return this.props.parentEventId;
    }
    get childEventIds() {
        return [...this.props.childEventIds];
    }
    get appliedRules() {
        return [...this.props.appliedRules];
    }
    get correlationMatches() {
        return [...this.props.correlationMatches];
    }
    get correlationResult() {
        return this.props.correlationResult ? { ...this.props.correlationResult } : undefined;
    }
    get correlationStartedAt() {
        return this.props.correlationStartedAt;
    }
    get correlationCompletedAt() {
        return this.props.correlationCompletedAt;
    }
    get correlationAttempts() {
        return this.props.correlationAttempts || 0;
    }
    get lastCorrelationError() {
        return this.props.lastCorrelationError;
    }
    get attackChain() {
        return this.props.attackChain ? { ...this.props.attackChain } : undefined;
    }
    get relatedEventIds() {
        return [...this.props.relatedEventIds];
    }
    get correlationPatterns() {
        return [...this.props.correlationPatterns];
    }
    get temporalCorrelation() {
        return this.props.temporalCorrelation ? { ...this.props.temporalCorrelation } : undefined;
    }
    get spatialCorrelation() {
        return this.props.spatialCorrelation ? { ...this.props.spatialCorrelation } : undefined;
    }
    get behavioralCorrelation() {
        return this.props.behavioralCorrelation ? { ...this.props.behavioralCorrelation } : undefined;
    }
    get requiresManualReview() {
        return this.props.requiresManualReview || false;
    }
    get reviewNotes() {
        return this.props.reviewNotes;
    }
    get reviewedBy() {
        return this.props.reviewedBy;
    }
    get reviewedAt() {
        return this.props.reviewedAt;
    }
    get correlationQualityScore() {
        return this.props.correlationQualityScore;
    }
    get validationErrors() {
        return this.props.validationErrors ? [...this.props.validationErrors] : [];
    }
    // Business methods
    /**
     * Start correlation process
     */
    startCorrelation() {
        if (this.props.correlationStatus !== correlation_status_enum_1.CorrelationStatus.PENDING) {
            throw new Error('Can only start correlation for pending events');
        }
        this.props.correlationStatus = correlation_status_enum_1.CorrelationStatus.IN_PROGRESS;
        this.props.correlationStartedAt = new Date();
        this.props.correlationAttempts = (this.props.correlationAttempts || 0) + 1;
        this.validateInvariants();
    }
    /**
     * Complete correlation process
     */
    completeCorrelation(result) {
        if (this.props.correlationStatus !== correlation_status_enum_1.CorrelationStatus.IN_PROGRESS) {
            throw new Error('Can only complete correlation for in-progress events');
        }
        this.props.correlationStatus = result.success ? correlation_status_enum_1.CorrelationStatus.COMPLETED : correlation_status_enum_1.CorrelationStatus.PARTIAL;
        this.props.correlationCompletedAt = new Date();
        this.props.correlationResult = result;
        this.props.lastCorrelationError = undefined;
        // Calculate correlation quality score based on result
        this.calculateCorrelationQualityScore(result);
        // Determine if manual review is required
        this.determineManualReviewRequirement();
        this.addDomainEvent(new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(this.id, {
            oldStatus: correlation_status_enum_1.CorrelationStatus.IN_PROGRESS,
            newStatus: this.props.correlationStatus,
            result,
            correlationQualityScore: this.props.correlationQualityScore,
            requiresManualReview: this.props.requiresManualReview || false,
        }));
        this.validateInvariants();
    }
    /**
     * Fail correlation process
     */
    failCorrelation(error, result) {
        if (this.props.correlationStatus !== correlation_status_enum_1.CorrelationStatus.IN_PROGRESS) {
            throw new Error('Can only fail correlation for in-progress events');
        }
        this.props.correlationStatus = correlation_status_enum_1.CorrelationStatus.FAILED;
        this.props.lastCorrelationError = error;
        if (result) {
            this.props.correlationResult = {
                success: false,
                appliedRules: result.appliedRules || [],
                failedRules: result.failedRules || [],
                warnings: result.warnings || [],
                errors: result.errors || [error],
                processingDurationMs: result.processingDurationMs || 0,
                confidenceScore: result.confidenceScore || 0,
                rulesUsed: result.rulesUsed || 0,
                matchesFound: result.matchesFound || 0,
                patternsIdentified: result.patternsIdentified || [],
            };
        }
        this.addDomainEvent(new correlated_event_correlation_failed_domain_event_1.CorrelatedEventCorrelationFailedDomainEvent(this.id, {
            enrichedEventId: this.props.enrichedEventId,
            error,
            attempt: this.correlationAttempts,
            maxAttemptsExceeded: this.hasExceededMaxCorrelationAttempts(),
        }));
        this.validateInvariants();
    }
    /**
     * Skip correlation process
     */
    skipCorrelation(reason) {
        if (![correlation_status_enum_1.CorrelationStatus.PENDING, correlation_status_enum_1.CorrelationStatus.FAILED].includes(this.props.correlationStatus)) {
            throw new Error('Can only skip correlation for pending or failed events');
        }
        this.props.correlationStatus = correlation_status_enum_1.CorrelationStatus.SKIPPED;
        this.props.lastCorrelationError = undefined;
        this.props.reviewNotes = reason;
        this.validateInvariants();
    }
    /**
     * Reset correlation for retry
     */
    resetCorrelation() {
        if (this.hasExceededMaxCorrelationAttempts()) {
            throw new Error('Cannot reset correlation: maximum attempts exceeded');
        }
        this.props.correlationStatus = correlation_status_enum_1.CorrelationStatus.PENDING;
        this.props.correlationStartedAt = undefined;
        this.props.correlationCompletedAt = undefined;
        this.props.lastCorrelationError = undefined;
        this.props.correlationResult = undefined;
        this.validateInvariants();
    }
    /**
     * Add correlation match
     */
    addCorrelationMatch(match) {
        if (this.props.correlationMatches.length >= CorrelatedEvent.MAX_CORRELATION_MATCHES) {
            throw new Error(`Cannot add more than ${CorrelatedEvent.MAX_CORRELATION_MATCHES} correlation matches`);
        }
        // Check for duplicate match
        const existingMatch = this.props.correlationMatches.find(m => m.eventId.equals(match.eventId) && m.ruleId === match.ruleId);
        if (existingMatch) {
            // Update existing match with higher confidence
            if (match.confidence > existingMatch.confidence) {
                Object.assign(existingMatch, match);
            }
        }
        else {
            // Add new match
            this.props.correlationMatches.push(match);
        }
        this.validateInvariants();
    }
    /**
     * Add related event
     */
    addRelatedEvent(eventId) {
        if (this.props.relatedEventIds.length >= CorrelatedEvent.MAX_RELATED_EVENTS) {
            throw new Error(`Cannot add more than ${CorrelatedEvent.MAX_RELATED_EVENTS} related events`);
        }
        // Check for duplicate
        const exists = this.props.relatedEventIds.some(id => id.equals(eventId));
        if (!exists) {
            this.props.relatedEventIds.push(eventId);
        }
    }
    /**
     * Add child event
     */
    addChildEvent(eventId) {
        // Check for duplicate
        const exists = this.props.childEventIds.some(id => id.equals(eventId));
        if (!exists) {
            this.props.childEventIds.push(eventId);
        }
    }
    /**
     * Set attack chain
     */
    setAttackChain(attackChain) {
        this.props.attackChain = attackChain;
        this.validateInvariants();
    }
    /**
     * Add correlation pattern
     */
    addCorrelationPattern(pattern) {
        if (!this.props.correlationPatterns.includes(pattern)) {
            this.props.correlationPatterns.push(pattern);
        }
    }
    /**
     * Update temporal correlation
     */
    updateTemporalCorrelation(temporal) {
        this.props.temporalCorrelation = temporal;
        this.validateInvariants();
    }
    /**
     * Update spatial correlation
     */
    updateSpatialCorrelation(spatial) {
        this.props.spatialCorrelation = spatial;
        this.validateInvariants();
    }
    /**
     * Update behavioral correlation
     */
    updateBehavioralCorrelation(behavioral) {
        this.props.behavioralCorrelation = behavioral;
        this.validateInvariants();
    }
    /**
     * Update correlated data
     */
    updateCorrelatedData(correlatedData) {
        this.props.correlatedData = { ...this.props.correlatedData, ...correlatedData };
    }
    /**
     * Add applied correlation rule
     */
    addAppliedRule(rule) {
        const existingRule = this.props.appliedRules.find(r => r.id === rule.id);
        if (!existingRule) {
            this.props.appliedRules.push(rule);
        }
    }
    /**
     * Update correlation quality score
     */
    updateCorrelationQualityScore(score) {
        if (score < 0 || score > 100) {
            throw new Error('Correlation quality score must be between 0 and 100');
        }
        this.props.correlationQualityScore = score;
        this.determineManualReviewRequirement();
    }
    /**
     * Add validation errors
     */
    addValidationErrors(errors) {
        const currentErrors = this.props.validationErrors || [];
        const newErrors = [...currentErrors, ...errors];
        if (newErrors.length > CorrelatedEvent.MAX_VALIDATION_ERRORS) {
            throw new Error(`Cannot have more than ${CorrelatedEvent.MAX_VALIDATION_ERRORS} validation errors`);
        }
        this.props.validationErrors = newErrors;
    }
    /**
     * Clear validation errors
     */
    clearValidationErrors() {
        this.props.validationErrors = [];
    }
    /**
     * Mark for manual review
     */
    markForManualReview(reason) {
        this.props.requiresManualReview = true;
        this.props.reviewNotes = reason;
    }
    /**
     * Complete manual review
     */
    completeManualReview(reviewedBy, notes) {
        if (!this.props.requiresManualReview) {
            throw new Error('Event is not marked for manual review');
        }
        this.props.reviewedBy = reviewedBy;
        this.props.reviewedAt = new Date();
        if (notes) {
            this.props.reviewNotes = notes;
        }
    }
    // Query methods
    /**
     * Check if correlation is completed
     */
    isCorrelationCompleted() {
        return this.props.correlationStatus === correlation_status_enum_1.CorrelationStatus.COMPLETED;
    }
    /**
     * Check if correlation failed
     */
    isCorrelationFailed() {
        return this.props.correlationStatus === correlation_status_enum_1.CorrelationStatus.FAILED;
    }
    /**
     * Check if correlation is in progress
     */
    isCorrelationInProgress() {
        return this.props.correlationStatus === correlation_status_enum_1.CorrelationStatus.IN_PROGRESS;
    }
    /**
     * Check if correlation was skipped
     */
    isCorrelationSkipped() {
        return this.props.correlationStatus === correlation_status_enum_1.CorrelationStatus.SKIPPED;
    }
    /**
     * Check if correlation is partial
     */
    isCorrelationPartial() {
        return this.props.correlationStatus === correlation_status_enum_1.CorrelationStatus.PARTIAL;
    }
    /**
     * Check if event has high correlation quality
     */
    hasHighCorrelationQuality() {
        return (this.props.correlationQualityScore || 0) >= CorrelatedEvent.MIN_CORRELATION_QUALITY_SCORE;
    }
    /**
     * Check if event has validation errors
     */
    hasValidationErrors() {
        return (this.props.validationErrors?.length || 0) > 0;
    }
    /**
     * Check if event has exceeded max correlation attempts
     */
    hasExceededMaxCorrelationAttempts() {
        return this.correlationAttempts >= CorrelatedEvent.MAX_CORRELATION_ATTEMPTS;
    }
    /**
     * Check if event is ready for next processing stage
     */
    isReadyForNextStage() {
        return (this.isCorrelationCompleted() || this.isCorrelationPartial()) &&
            this.hasHighCorrelationQuality() &&
            !this.hasValidationErrors() &&
            (!this.requiresManualReview || this.reviewedAt !== undefined);
    }
    /**
     * Check if event has high confidence correlation
     */
    isHighConfidenceCorrelation() {
        return (this.props.correlationResult?.confidenceScore || 0) >= CorrelatedEvent.HIGH_CONFIDENCE_THRESHOLD;
    }
    /**
     * Check if event has attack chain
     */
    hasAttackChain() {
        return !!this.props.attackChain;
    }
    /**
     * Check if event has temporal correlation
     */
    hasTemporalCorrelation() {
        return !!this.props.temporalCorrelation;
    }
    /**
     * Check if event has spatial correlation
     */
    hasSpatialCorrelation() {
        return !!this.props.spatialCorrelation;
    }
    /**
     * Check if event has behavioral correlation
     */
    hasBehavioralCorrelation() {
        return !!this.props.behavioralCorrelation;
    }
    /**
     * Get correlation duration
     */
    getCorrelationDuration() {
        if (!this.props.correlationStartedAt) {
            return null;
        }
        const endTime = this.props.correlationCompletedAt || new Date();
        return endTime.getTime() - this.props.correlationStartedAt.getTime();
    }
    /**
     * Get applied rule names
     */
    getAppliedRuleNames() {
        return this.props.appliedRules.map(rule => rule.name);
    }
    /**
     * Check if specific rule was applied
     */
    hasAppliedRule(ruleId) {
        return this.props.appliedRules.some(rule => rule.id === ruleId);
    }
    /**
     * Get matches by rule
     */
    getMatchesByRule(ruleId) {
        return this.props.correlationMatches.filter(match => match.ruleId === ruleId);
    }
    /**
     * Get matches by type
     */
    getMatchesByType(matchType) {
        return this.props.correlationMatches.filter(match => match.matchType === matchType);
    }
    /**
     * Get average match confidence
     */
    getAverageMatchConfidence() {
        if (this.props.correlationMatches.length === 0)
            return null;
        const totalConfidence = this.props.correlationMatches.reduce((sum, match) => sum + match.confidence, 0);
        return totalConfidence / this.props.correlationMatches.length;
    }
    /**
     * Get highest confidence match
     */
    getHighestConfidenceMatch() {
        if (this.props.correlationMatches.length === 0)
            return null;
        return this.props.correlationMatches.reduce((highest, match) => match.confidence > highest.confidence ? match : highest);
    }
    // Private helper methods
    calculateCorrelationQualityScore(result) {
        let score = 100;
        // Reduce score for failed rules
        const failedRulesPenalty = result.failedRules.length * 15;
        score -= failedRulesPenalty;
        // Reduce score for warnings
        const warningsPenalty = result.warnings.length * 5;
        score -= warningsPenalty;
        // Reduce score for errors
        const errorsPenalty = result.errors.length * 20;
        score -= errorsPenalty;
        // Reduce score for low confidence
        if (result.confidenceScore < 70) {
            score -= (70 - result.confidenceScore);
        }
        // Boost score for patterns identified
        const patternsBonus = Math.min(result.patternsIdentified.length * 5, 20);
        score += patternsBonus;
        // Boost score for matches found
        const matchesBonus = Math.min(result.matchesFound * 2, 15);
        score += matchesBonus;
        this.props.correlationQualityScore = Math.max(0, Math.min(100, score));
    }
    determineManualReviewRequirement() {
        let requiresReview = false;
        // High confidence correlations with attack chains require review
        if (this.hasAttackChain() && this.isHighConfidenceCorrelation()) {
            requiresReview = true;
        }
        // Low correlation quality requires review
        if (!this.hasHighCorrelationQuality()) {
            requiresReview = true;
        }
        // Critical events require review
        if (this.severity === event_severity_enum_1.EventSeverity.CRITICAL) {
            requiresReview = true;
        }
        // Too many validation errors require review
        if (this.hasValidationErrors() && this.validationErrors.length > 5) {
            requiresReview = true;
        }
        this.props.requiresManualReview = requiresReview;
    }
}
exports.CorrelatedEvent = CorrelatedEvent;
CorrelatedEvent.MAX_CORRELATION_ATTEMPTS = 3;
CorrelatedEvent.MIN_CORRELATION_QUALITY_SCORE = 70;
CorrelatedEvent.HIGH_CONFIDENCE_THRESHOLD = 85;
CorrelatedEvent.MAX_VALIDATION_ERRORS = 10;
CorrelatedEvent.MAX_CORRELATION_MATCHES = 100;
CorrelatedEvent.MAX_RELATED_EVENTS = 500;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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