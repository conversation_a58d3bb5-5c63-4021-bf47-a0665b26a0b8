{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\recommendation-generated.domain-event.spec.ts", "mappings": ";;AAAA,oGAAgI;AAChI,gEAA8D;AAC9D,mEAA0D;AAE1D,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE;IAClD,IAAI,WAA2B,CAAC;IAChC,IAAI,SAA2C,CAAC;IAEhD,UAAU,CAAC,GAAG,EAAE;QACd,WAAW,GAAG,8BAAc,CAAC,QAAQ,EAAE,CAAC;QACxC,SAAS,GAAG;YACV,gBAAgB,EAAE,SAAS;YAC3B,kBAAkB,EAAE,iBAAiB;YACrC,KAAK,EAAE,uCAAuC;YAC9C,WAAW,EAAE,oEAAoE;YACjF,QAAQ,EAAE,MAAM;YAChB,eAAe,EAAE,EAAE;YACnB,sBAAsB,EAAE,EAAE;YAC1B,oBAAoB,EAAE,QAAQ;YAC9B,2BAA2B,EAAE,EAAE;YAC/B,YAAY,EAAE;gBACZ,MAAM,EAAE,KAAK;gBACb,QAAQ,EAAE,KAAK;gBACf,QAAQ,EAAE,QAAQ;aACnB;YACD,MAAM,EAAE;gBACN,IAAI,EAAE,aAAa;gBACnB,UAAU,EAAE,oBAAoB;gBAChC,OAAO,EAAE,OAAO;aACjB;YACD,OAAO,EAAE;gBACP,WAAW,EAAE,qBAAqB;gBAClC,UAAU,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;gBACpC,aAAa,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;gBACzC,eAAe,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC;aAChD;YACD,kBAAkB,EAAE;gBAClB;oBACE,UAAU,EAAE,6BAAU,CAAC,oBAAoB;oBAC3C,WAAW,EAAE,iDAAiD;oBAC9D,QAAQ,EAAE,MAAM;oBAChB,iBAAiB,EAAE,GAAG;oBACtB,aAAa,EAAE,CAAC,cAAc,EAAE,oBAAoB,CAAC;oBACrD,KAAK,EAAE,CAAC,8BAA8B,CAAC;iBACxC;gBACD;oBACE,UAAU,EAAE,6BAAU,CAAC,cAAc;oBACrC,WAAW,EAAE,sCAAsC;oBACnD,QAAQ,EAAE,QAAQ;oBAClB,iBAAiB,EAAE,EAAE;oBACrB,aAAa,EAAE,CAAC,mBAAmB,EAAE,qBAAqB,CAAC;iBAC5D;aACF;YACD,eAAe,EAAE;gBACf,6CAA6C;gBAC7C,uCAAuC;gBACvC,2DAA2D;aAC5D;YACD,IAAI,EAAE;gBACJ;oBACE,IAAI,EAAE,mBAAmB;oBACzB,WAAW,EAAE,sCAAsC;oBACnD,WAAW,EAAE,KAAK;oBAClB,iBAAiB,EAAE,+BAA+B;iBACnD;gBACD;oBACE,IAAI,EAAE,0BAA0B;oBAChC,WAAW,EAAE,qDAAqD;oBAClE,WAAW,EAAE,GAAG;oBAChB,iBAAiB,EAAE,4BAA4B;iBAChD;aACF;YACD,oBAAoB,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC;YAClD,qBAAqB,EAAE;gBACrB,cAAc,EAAE,iDAAiD;gBACjE,aAAa,EAAE,4DAA4D;gBAC3E,WAAW,EAAE,kDAAkD;aAChE;YACD,QAAQ,EAAE;gBACR,oBAAoB,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,kBAAkB;gBACxF,yBAAyB,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,oBAAoB;gBAChG,UAAU,EAAE;oBACV;wBACE,IAAI,EAAE,gBAAgB;wBACtB,WAAW,EAAE,2CAA2C;wBACxD,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;wBAC3D,YAAY,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;qBACrD;oBACD;wBACE,IAAI,EAAE,sBAAsB;wBAC5B,WAAW,EAAE,2BAA2B;wBACxC,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;wBAC3D,YAAY,EAAE,CAAC,mBAAmB,CAAC;qBACpC;iBACF;aACF;YACD,YAAY,EAAE;gBACZ;oBACE,IAAI,EAAE,eAAe;oBACrB,cAAc,EAAE,4CAA4C;oBAC5D,QAAQ,EAAE,IAAI;iBACf;gBACD;oBACE,IAAI,EAAE,eAAe;oBACrB,cAAc,EAAE,uCAAuC;oBACvD,QAAQ,EAAE,IAAI;iBACf;gBACD;oBACE,IAAI,EAAE,eAAe;oBACrB,cAAc,EAAE,4BAA4B;oBAC5C,QAAQ,EAAE,KAAK;iBAChB;aACF;YACD,YAAY,EAAE;gBACZ;oBACE,KAAK,EAAE,qCAAqC;oBAC5C,WAAW,EAAE,8CAA8C;oBAC3D,IAAI,EAAE,CAAC,4BAA4B,EAAE,wBAAwB,CAAC;oBAC9D,IAAI,EAAE,CAAC,qBAAqB,EAAE,mBAAmB,CAAC;oBAClD,MAAM,EAAE,MAAM;oBACd,aAAa,EAAE,EAAE;iBAClB;aACF;YACD,YAAY,EAAE,CAAC,iBAAiB,EAAE,oBAAoB,EAAE,oBAAoB,CAAC;YAC7E,mBAAmB,EAAE;gBACnB;oBACE,IAAI,EAAE,iCAAiC;oBACvC,UAAU,EAAE,QAAQ;oBACpB,MAAM,EAAE,QAAQ;oBAChB,UAAU,EAAE,4CAA4C;iBACzD;gBACD;oBACE,IAAI,EAAE,8BAA8B;oBACpC,UAAU,EAAE,KAAK;oBACjB,MAAM,EAAE,MAAM;oBACd,UAAU,EAAE,yCAAyC;iBACtD;aACF;YACD,sBAAsB,EAAE;gBACtB,2CAA2C;gBAC3C,yBAAyB;gBACzB,+BAA+B;aAChC;YACD,YAAY,EAAE;gBACZ,WAAW,EAAE,sEAAsE;gBACnF,KAAK,EAAE;oBACL,kDAAkD;oBAClD,qCAAqC;oBACrC,gCAAgC;oBAChC,wBAAwB;iBACzB;gBACD,aAAa,EAAE,EAAE;aAClB;SACF,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAE7E,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YAClE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACpC,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACvC,MAAM,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAE7E,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YACjE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC5D,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,YAAY,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,UAAmB,EAAE,CAAC;YACrE,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;YAEhF,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAE7E,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,UAAU,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,QAAiB,EAAE,CAAC;YACjE,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;YAE9E,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC7E,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,iBAAiB,GAAG,EAAE,GAAG,SAAS,EAAE,eAAe,EAAE,EAAE,EAAE,CAAC;YAChE,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;YAErF,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC7E,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,WAAW,GAAG,EAAE,GAAG,SAAS,EAAE,sBAAsB,EAAE,EAAE,EAAE,CAAC;YACjE,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YAE/E,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,aAAa,GAAG,EAAE,GAAG,SAAS,EAAE,oBAAoB,EAAE,KAAc,EAAE,CAAC;YAC7E,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;YAEjF,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,cAAc,GAAG,EAAE,GAAG,SAAS,EAAE,oBAAoB,EAAE,MAAe,EAAE,CAAC;YAC/E,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;YAElF,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,SAAS,GAAG,EAAE,GAAG,SAAS,EAAE,2BAA2B,EAAE,CAAC,EAAE,CAAC;YACnE,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAE7E,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,QAAQ,GAAG,EAAE,GAAG,SAAS,EAAE,2BAA2B,EAAE,EAAE,EAAE,CAAC;YACnE,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;YAE5E,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClD,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC7E,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,YAAY,GAAG;gBACnB,GAAG,SAAS;gBACZ,YAAY,EAAE,EAAE,GAAG,SAAS,CAAC,YAAa,EAAE,QAAQ,EAAE,MAAe,EAAE;aACxE,CAAC;YACF,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;YAEhF,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,UAAU,GAAG,EAAE,GAAG,SAAS,EAAE,CAAC;YACpC,OAAO,UAAU,CAAC,YAAY,CAAC;YAC/B,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;YAE9E,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC7E,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,gBAAgB,GAAG,EAAE,GAAG,SAAS,EAAE,CAAC;YAC1C,OAAO,gBAAgB,CAAC,oBAAoB,CAAC;YAC7C,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;YAEpF,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC7E,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC7E,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;YACxE,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC7E,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,+BAA+B;QACzF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC7E,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC7E,MAAM,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC7E,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,UAAU,GAAG;gBACjB,GAAG,SAAS;gBACZ,OAAO,EAAE,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE,WAAW,EAAE,iBAA0B,EAAE;aAC3E,CAAC;YACF,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;YAE9E,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC7E,MAAM,UAAU,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;YAEzC,MAAM,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,UAAU,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;YAC5C,oFAAoF;YACpF,sCAAsC;YACtC,MAAM,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uEAAuE,EAAE,GAAG,EAAE;YAC/E,MAAM,YAAY,GAAG;gBACnB,GAAG,SAAS;gBACZ,eAAe,EAAE,EAAE;gBACnB,sBAAsB,EAAE,EAAE;gBAC1B,oBAAoB,EAAE,MAAe;gBACrC,QAAQ,EAAE,KAAc;aACzB,CAAC;YACF,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;YAChF,MAAM,UAAU,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;YAEzC,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,YAAY,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,UAAmB,EAAE,CAAC;YACrE,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;YAEhF,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC7E,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,UAAU,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,QAAiB,EAAE,CAAC;YACjE,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;YAE9E,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,OAAO,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,KAAc,EAAE,CAAC;YAC3D,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;YAE3E,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW;QAChE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,YAAY,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,UAAmB,EAAE,CAAC;YACrE,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;YAChF,MAAM,QAAQ,GAAG,KAAK,CAAC,uBAAuB,EAAE,CAAC;YAEjD,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACjD,MAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;YAClE,MAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC7E,MAAM,QAAQ,GAAG,KAAK,CAAC,uBAAuB,EAAE,CAAC;YAEjD,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAChD,MAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kEAAkE,EAAE,GAAG,EAAE;YAC1E,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC7E,MAAM,QAAQ,GAAG,KAAK,CAAC,uBAAuB,EAAE,CAAC;YAEjD,MAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,sEAAsE,EAAE,GAAG,EAAE;YAC9E,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC7E,MAAM,OAAO,GAAG,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAE/C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;YAC7C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC,oBAAoB;YAChE,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC,oBAAoB;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qEAAqE,EAAE,GAAG,EAAE;YAC7E,MAAM,UAAU,GAAG;gBACjB,GAAG,SAAS;gBACZ,OAAO,EAAE,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE,WAAW,EAAE,iBAA0B,EAAE;aAC3E,CAAC;YACF,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;YAC9E,MAAM,OAAO,GAAG,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAE/C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC7E,MAAM,UAAU,GAAG,KAAK,CAAC,kBAAkB,EAAE,CAAC;YAE9C,kFAAkF;YAClF,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,UAAU,GAAG;gBACjB,GAAG,SAAS;gBACZ,oBAAoB,EAAE,KAAc;gBACpC,YAAY,EAAE,EAAE;gBAChB,kBAAkB,EAAE,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;gBACrD,YAAY,EAAE,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gBACzC,mBAAmB,EAAE,EAAE;aACxB,CAAC;YACF,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;YAC9E,MAAM,UAAU,GAAG,KAAK,CAAC,kBAAkB,EAAE,CAAC;YAE9C,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC7E,MAAM,OAAO,GAAG,KAAK,CAAC,wBAAwB,EAAE,CAAC;YAEjD,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC7C,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC7E,MAAM,gBAAgB,GAAG,KAAK,CAAC,kBAAkB,EAAE,CAAC;YAEpD,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YACnE,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7C,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC/D,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YACjG,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACnE,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnE,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC9D,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAC9E,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtE,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnE,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC7E,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;YAE5B,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC7E,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;YAC5B,MAAM,iBAAiB,GAAG,0EAAkC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAE5E,MAAM,CAAC,iBAAiB,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3E,MAAM,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACxE,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAClD,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC7E,MAAM,WAAW,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;YAE3C,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAC/C,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;YACjD,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,uCAAuC,CAAC,CAAC;YACvE,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;YACjD,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAC/C,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC7E,MAAM,OAAO,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;YAEnC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YAC1D,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC7C,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YACpE,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACxD,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,kBAAkB,GAAG,EAAE,GAAG,SAAS,EAAE,CAAC;YAC5C,OAAO,kBAAkB,CAAC,YAAY,CAAC;YACvC,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;YAEtF,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,kBAAkB,GAAG,EAAE,GAAG,SAAS,EAAE,YAAY,EAAE,EAAE,EAAE,CAAC;YAC9D,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;YAEtF,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,cAAc,GAAG,EAAE,GAAG,SAAS,EAAE,CAAC;YACxC,OAAO,cAAc,CAAC,YAAY,CAAC;YACnC,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;YAElF,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,YAAY,GAAG;gBACnB,GAAG,SAAS;gBACZ,mBAAmB,EAAE;oBACnB;wBACE,IAAI,EAAE,gBAAgB;wBACtB,UAAU,EAAE,MAAM;wBAClB,MAAM,EAAE,MAAM;wBACd,UAAU,EAAE,uBAAuB;qBACpC;iBACF;aACF,CAAC;YACF,MAAM,KAAK,GAAG,IAAI,0EAAkC,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;YAEhF,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\recommendation-generated.domain-event.spec.ts"], "sourcesContent": ["import { RecommendationGeneratedDomainEvent, RecommendationGeneratedEventData } from '../recommendation-generated.domain-event';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel';\r\nimport { ActionType } from '../../enums/action-type.enum';\r\n\r\ndescribe('RecommendationGeneratedDomainEvent', () => {\r\n  let aggregateId: UniqueEntityId;\r\n  let eventData: RecommendationGeneratedEventData;\r\n\r\n  beforeEach(() => {\r\n    aggregateId = UniqueEntityId.generate();\r\n    eventData = {\r\n      recommendationId: 'rec-001',\r\n      recommendationType: 'security_action',\r\n      title: 'Implement Multi-Factor Authentication',\r\n      description: 'Deploy MFA across all critical systems to enhance security posture',\r\n      priority: 'high',\r\n      confidenceScore: 85,\r\n      riskReductionPotential: 75,\r\n      implementationEffort: 'medium',\r\n      estimatedImplementationTime: 24,\r\n      costEstimate: {\r\n        amount: 15000,\r\n        currency: 'USD',\r\n        category: 'medium'\r\n      },\r\n      source: {\r\n        type: 'ai_analysis',\r\n        identifier: 'security-ai-engine',\r\n        version: '2.1.0'\r\n      },\r\n      context: {\r\n        triggerType: 'vulnerability_found',\r\n        triggerIds: ['vuln-001', 'vuln-002'],\r\n        relatedAssets: ['asset-001', 'asset-002'],\r\n        affectedSystems: ['auth-system', 'user-portal']\r\n      },\r\n      recommendedActions: [\r\n        {\r\n          actionType: ActionType.UPDATE_CONFIGURATION,\r\n          description: 'Configure MFA settings in authentication system',\r\n          priority: 'high',\r\n          estimatedDuration: 120,\r\n          prerequisites: ['admin_access', 'maintenance_window'],\r\n          risks: ['temporary_service_disruption']\r\n        },\r\n        {\r\n          actionType: ActionType.ENABLE_SERVICE,\r\n          description: 'Enable MFA enforcement for all users',\r\n          priority: 'medium',\r\n          estimatedDuration: 60,\r\n          prerequisites: ['user_notification', 'training_completion']\r\n        }\r\n      ],\r\n      successCriteria: [\r\n        'MFA enabled for 100% of privileged accounts',\r\n        'MFA adoption rate >95% within 30 days',\r\n        'Zero authentication-related incidents post-implementation'\r\n      ],\r\n      kpis: [\r\n        {\r\n          name: 'MFA Adoption Rate',\r\n          description: 'Percentage of users with MFA enabled',\r\n          targetValue: '95%',\r\n          measurementMethod: 'Authentication system metrics'\r\n        },\r\n        {\r\n          name: 'Authentication Incidents',\r\n          description: 'Number of authentication-related security incidents',\r\n          targetValue: '0',\r\n          measurementMethod: 'Security incident tracking'\r\n        }\r\n      ],\r\n      complianceFrameworks: ['SOC2', 'ISO27001', 'NIST'],\r\n      businessJustification: {\r\n        riskMitigation: 'Reduces risk of credential-based attacks by 80%',\r\n        businessValue: 'Protects customer data and maintains regulatory compliance',\r\n        costBenefit: 'ROI of 300% through prevented security incidents'\r\n      },\r\n      timeline: {\r\n        recommendedStartDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 1 week from now\r\n        recommendedCompletionDate: new Date(Date.now() + 37 * 24 * 60 * 60 * 1000), // 5+ weeks from now\r\n        milestones: [\r\n          {\r\n            name: 'Planning Phase',\r\n            description: 'Complete planning and resource allocation',\r\n            targetDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),\r\n            dependencies: ['budget_approval', 'team_assignment']\r\n          },\r\n          {\r\n            name: 'Implementation Phase',\r\n            description: 'Deploy MFA infrastructure',\r\n            targetDate: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000),\r\n            dependencies: ['planning_complete']\r\n          }\r\n        ]\r\n      },\r\n      stakeholders: [\r\n        {\r\n          role: 'security_team',\r\n          responsibility: 'Technical implementation and configuration',\r\n          required: true\r\n        },\r\n        {\r\n          role: 'it_operations',\r\n          responsibility: 'Infrastructure support and monitoring',\r\n          required: true\r\n        },\r\n        {\r\n          role: 'user_training',\r\n          responsibility: 'User education and support',\r\n          required: false\r\n        }\r\n      ],\r\n      alternatives: [\r\n        {\r\n          title: 'Single Sign-On (SSO) Implementation',\r\n          description: 'Implement SSO with built-in MFA capabilities',\r\n          pros: ['Simplified user experience', 'Centralized management'],\r\n          cons: ['Higher initial cost', 'Vendor dependency'],\r\n          effort: 'high',\r\n          riskReduction: 70\r\n        }\r\n      ],\r\n      dependencies: ['budget_approval', 'maintenance_window', 'user_communication'],\r\n      implementationRisks: [\r\n        {\r\n          risk: 'User resistance to MFA adoption',\r\n          likelihood: 'medium',\r\n          impact: 'medium',\r\n          mitigation: 'Comprehensive training and gradual rollout'\r\n        },\r\n        {\r\n          risk: 'Technical integration issues',\r\n          likelihood: 'low',\r\n          impact: 'high',\r\n          mitigation: 'Thorough testing in staging environment'\r\n        }\r\n      ],\r\n      validationRequirements: [\r\n        'Penetration testing of MFA implementation',\r\n        'User acceptance testing',\r\n        'Performance impact assessment'\r\n      ],\r\n      rollbackPlan: {\r\n        description: 'Disable MFA enforcement and revert to previous authentication method',\r\n        steps: [\r\n          'Disable MFA enforcement in authentication system',\r\n          'Notify users of temporary reversion',\r\n          'Investigate and resolve issues',\r\n          'Plan re-implementation'\r\n        ],\r\n        estimatedTime: 30\r\n      }\r\n    };\r\n  });\r\n\r\n  describe('constructor', () => {\r\n    it('should create event with valid data', () => {\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, eventData);\r\n\r\n      expect(event.aggregateId).toEqual(aggregateId);\r\n      expect(event.recommendationId).toBe('rec-001');\r\n      expect(event.title).toBe('Implement Multi-Factor Authentication');\r\n      expect(event.priority).toBe('high');\r\n      expect(event.confidenceScore).toBe(85);\r\n      expect(event.riskReductionPotential).toBe(75);\r\n    });\r\n\r\n    it('should set correct metadata', () => {\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, eventData);\r\n\r\n      expect(event.metadata.eventType).toBe('RecommendationGenerated');\r\n      expect(event.metadata.domain).toBe('Security');\r\n      expect(event.metadata.aggregateType).toBe('Recommendation');\r\n      expect(event.metadata.processingStage).toBe('generation');\r\n    });\r\n  });\r\n\r\n  describe('priority checks', () => {\r\n    it('should identify critical priority recommendations', () => {\r\n      const criticalData = { ...eventData, priority: 'critical' as const };\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, criticalData);\r\n      \r\n      expect(event.isCriticalPriority()).toBe(true);\r\n      expect(event.isHighPriorityOrAbove()).toBe(true);\r\n    });\r\n\r\n    it('should identify high priority recommendations', () => {\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, eventData);\r\n      \r\n      expect(event.isCriticalPriority()).toBe(false);\r\n      expect(event.isHighPriorityOrAbove()).toBe(true);\r\n    });\r\n\r\n    it('should identify medium priority recommendations', () => {\r\n      const mediumData = { ...eventData, priority: 'medium' as const };\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, mediumData);\r\n      \r\n      expect(event.isHighPriorityOrAbove()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('confidence checks', () => {\r\n    it('should identify high confidence recommendations', () => {\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, eventData);\r\n      expect(event.hasHighConfidence()).toBe(true);\r\n      expect(event.hasLowConfidence()).toBe(false);\r\n    });\r\n\r\n    it('should identify low confidence recommendations', () => {\r\n      const lowConfidenceData = { ...eventData, confidenceScore: 45 };\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, lowConfidenceData);\r\n      \r\n      expect(event.hasHighConfidence()).toBe(false);\r\n      expect(event.hasLowConfidence()).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('risk reduction checks', () => {\r\n    it('should identify high risk reduction potential', () => {\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, eventData);\r\n      expect(event.hasHighRiskReduction()).toBe(true);\r\n    });\r\n\r\n    it('should identify low risk reduction potential', () => {\r\n      const lowRiskData = { ...eventData, riskReductionPotential: 45 };\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, lowRiskData);\r\n      \r\n      expect(event.hasHighRiskReduction()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('effort checks', () => {\r\n    it('should identify low effort recommendations', () => {\r\n      const lowEffortData = { ...eventData, implementationEffort: 'low' as const };\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, lowEffortData);\r\n      \r\n      expect(event.isLowEffort()).toBe(true);\r\n      expect(event.isHighEffort()).toBe(false);\r\n    });\r\n\r\n    it('should identify high effort recommendations', () => {\r\n      const highEffortData = { ...eventData, implementationEffort: 'high' as const };\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, highEffortData);\r\n      \r\n      expect(event.isLowEffort()).toBe(false);\r\n      expect(event.isHighEffort()).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('implementation time checks', () => {\r\n    it('should identify quick implementation recommendations', () => {\r\n      const quickData = { ...eventData, estimatedImplementationTime: 6 };\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, quickData);\r\n      \r\n      expect(event.isQuickImplementation()).toBe(true);\r\n      expect(event.isLongImplementation()).toBe(false);\r\n    });\r\n\r\n    it('should identify long implementation recommendations', () => {\r\n      const longData = { ...eventData, estimatedImplementationTime: 50 };\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, longData);\r\n      \r\n      expect(event.isQuickImplementation()).toBe(false);\r\n      expect(event.isLongImplementation()).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('cost checks', () => {\r\n    it('should identify recommendations with cost estimates', () => {\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, eventData);\r\n      expect(event.hasCostEstimate()).toBe(true);\r\n    });\r\n\r\n    it('should identify high cost recommendations', () => {\r\n      const highCostData = {\r\n        ...eventData,\r\n        costEstimate: { ...eventData.costEstimate!, category: 'high' as const }\r\n      };\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, highCostData);\r\n      \r\n      expect(event.isHighCost()).toBe(true);\r\n    });\r\n\r\n    it('should handle recommendations without cost estimates', () => {\r\n      const noCostData = { ...eventData };\r\n      delete noCostData.costEstimate;\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, noCostData);\r\n      \r\n      expect(event.hasCostEstimate()).toBe(false);\r\n      expect(event.isHighCost()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('compliance checks', () => {\r\n    it('should identify compliance-related recommendations', () => {\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, eventData);\r\n      expect(event.addressesCompliance()).toBe(true);\r\n    });\r\n\r\n    it('should handle recommendations without compliance frameworks', () => {\r\n      const noComplianceData = { ...eventData };\r\n      delete noComplianceData.complianceFrameworks;\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, noComplianceData);\r\n      \r\n      expect(event.addressesCompliance()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('alternatives and dependencies', () => {\r\n    it('should identify recommendations with alternatives', () => {\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, eventData);\r\n      expect(event.hasAlternatives()).toBe(true);\r\n    });\r\n\r\n    it('should identify recommendations with dependencies', () => {\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, eventData);\r\n      expect(event.hasDependencies()).toBe(true);\r\n    });\r\n\r\n    it('should identify recommendations with high implementation risks', () => {\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, eventData);\r\n      expect(event.hasHighImplementationRisks()).toBe(false); // Current risks are medium/low\r\n    });\r\n\r\n    it('should identify recommendations with rollback plans', () => {\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, eventData);\r\n      expect(event.hasRollbackPlan()).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('source and trigger checks', () => {\r\n    it('should identify AI-generated recommendations', () => {\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, eventData);\r\n      expect(event.isAIGenerated()).toBe(true);\r\n    });\r\n\r\n    it('should identify vulnerability-triggered recommendations', () => {\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, eventData);\r\n      expect(event.isTriggeredByVulnerability()).toBe(true);\r\n      expect(event.isTriggeredByThreat()).toBe(false);\r\n      expect(event.isTriggeredByIncident()).toBe(false);\r\n    });\r\n\r\n    it('should identify threat-triggered recommendations', () => {\r\n      const threatData = {\r\n        ...eventData,\r\n        context: { ...eventData.context, triggerType: 'threat_detected' as const }\r\n      };\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, threatData);\r\n      \r\n      expect(event.isTriggeredByThreat()).toBe(true);\r\n      expect(event.isTriggeredByVulnerability()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('value score calculation', () => {\r\n    it('should calculate value score correctly', () => {\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, eventData);\r\n      const valueScore = event.getValueScore();\r\n      \r\n      expect(valueScore).toBeGreaterThan(0);\r\n      expect(valueScore).toBeLessThanOrEqual(100);\r\n      // With high risk reduction (75), high confidence (85), medium effort, high priority\r\n      // Should result in a good value score\r\n      expect(valueScore).toBeGreaterThan(70);\r\n    });\r\n\r\n    it('should calculate lower value score for low confidence recommendations', () => {\r\n      const lowValueData = {\r\n        ...eventData,\r\n        confidenceScore: 30,\r\n        riskReductionPotential: 40,\r\n        implementationEffort: 'high' as const,\r\n        priority: 'low' as const\r\n      };\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, lowValueData);\r\n      const valueScore = event.getValueScore();\r\n      \r\n      expect(valueScore).toBeLessThan(50);\r\n    });\r\n  });\r\n\r\n  describe('implementation urgency', () => {\r\n    it('should set appropriate urgency for critical priority', () => {\r\n      const criticalData = { ...eventData, priority: 'critical' as const };\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, criticalData);\r\n      \r\n      expect(event.getImplementationUrgency()).toBe(1); // 1 day\r\n    });\r\n\r\n    it('should set appropriate urgency for high priority', () => {\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, eventData);\r\n      expect(event.getImplementationUrgency()).toBe(7); // 1 week\r\n    });\r\n\r\n    it('should set appropriate urgency for medium priority', () => {\r\n      const mediumData = { ...eventData, priority: 'medium' as const };\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, mediumData);\r\n      \r\n      expect(event.getImplementationUrgency()).toBe(30); // 1 month\r\n    });\r\n\r\n    it('should set appropriate urgency for low priority', () => {\r\n      const lowData = { ...eventData, priority: 'low' as const };\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, lowData);\r\n      \r\n      expect(event.getImplementationUrgency()).toBe(90); // 3 months\r\n    });\r\n  });\r\n\r\n  describe('approval requirements', () => {\r\n    it('should require executive approval for critical priority', () => {\r\n      const criticalData = { ...eventData, priority: 'critical' as const };\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, criticalData);\r\n      const approval = event.getApprovalRequirements();\r\n      \r\n      expect(approval.requiresApproval).toBe(true);\r\n      expect(approval.approvalLevel).toBe('executive');\r\n      expect(approval.requiredApprovers).toContain('security_director');\r\n      expect(approval.requiredApprovers).toContain('ciso');\r\n    });\r\n\r\n    it('should require director approval for high priority', () => {\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, eventData);\r\n      const approval = event.getApprovalRequirements();\r\n      \r\n      expect(approval.requiresApproval).toBe(true);\r\n      expect(approval.approvalLevel).toBe('director');\r\n      expect(approval.requiredApprovers).toContain('security_director');\r\n    });\r\n\r\n    it('should include compliance officer for compliance recommendations', () => {\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, eventData);\r\n      const approval = event.getApprovalRequirements();\r\n      \r\n      expect(approval.requiredApprovers).toContain('compliance_officer');\r\n    });\r\n  });\r\n\r\n  describe('notification targets', () => {\r\n    it('should include appropriate targets for high priority recommendations', () => {\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, eventData);\r\n      const targets = event.getNotificationTargets();\r\n      \r\n      expect(targets).toContain('security_team');\r\n      expect(targets).toContain('security_managers');\r\n      expect(targets).toContain('compliance_team');\r\n      expect(targets).toContain('security_team'); // From stakeholders\r\n      expect(targets).toContain('it_operations'); // From stakeholders\r\n    });\r\n\r\n    it('should include threat analysts for threat-triggered recommendations', () => {\r\n      const threatData = {\r\n        ...eventData,\r\n        context: { ...eventData.context, triggerType: 'threat_detected' as const }\r\n      };\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, threatData);\r\n      const targets = event.getNotificationTargets();\r\n      \r\n      expect(targets).toContain('threat_analysts');\r\n    });\r\n  });\r\n\r\n  describe('complexity level', () => {\r\n    it('should calculate complexity correctly', () => {\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, eventData);\r\n      const complexity = event.getComplexityLevel();\r\n      \r\n      // Medium effort + dependencies + 2 actions + 3 stakeholders = moderate complexity\r\n      expect(complexity).toBe('moderate');\r\n    });\r\n\r\n    it('should identify simple recommendations', () => {\r\n      const simpleData = {\r\n        ...eventData,\r\n        implementationEffort: 'low' as const,\r\n        dependencies: [],\r\n        recommendedActions: [eventData.recommendedActions[0]],\r\n        stakeholders: [eventData.stakeholders[0]],\r\n        implementationRisks: []\r\n      };\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, simpleData);\r\n      const complexity = event.getComplexityLevel();\r\n      \r\n      expect(complexity).toBe('simple');\r\n    });\r\n  });\r\n\r\n  describe('recommendation metrics', () => {\r\n    it('should generate comprehensive metrics', () => {\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, eventData);\r\n      const metrics = event.getRecommendationMetrics();\r\n      \r\n      expect(metrics.recommendationId).toBe('rec-001');\r\n      expect(metrics.type).toBe('security_action');\r\n      expect(metrics.priority).toBe('high');\r\n      expect(metrics.confidenceScore).toBe(85);\r\n      expect(metrics.riskReductionPotential).toBe(75);\r\n      expect(metrics.valueScore).toBeGreaterThan(0);\r\n      expect(metrics.implementationEffort).toBe('medium');\r\n      expect(metrics.estimatedHours).toBe(24);\r\n      expect(metrics.complexityLevel).toBe('moderate');\r\n      expect(metrics.hasAlternatives).toBe(true);\r\n      expect(metrics.hasDependencies).toBe(true);\r\n      expect(metrics.addressesCompliance).toBe(true);\r\n      expect(metrics.requiresApproval).toBe(true);\r\n      expect(metrics.implementationUrgency).toBe(7);\r\n    });\r\n  });\r\n\r\n  describe('integration event conversion', () => {\r\n    it('should convert to integration event format', () => {\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, eventData);\r\n      const integrationEvent = event.toIntegrationEvent();\r\n      \r\n      expect(integrationEvent.eventType).toBe('RecommendationGenerated');\r\n      expect(integrationEvent.version).toBe('1.0');\r\n      expect(integrationEvent.data.recommendationId).toBe('rec-001');\r\n      expect(integrationEvent.data.recommendation.title).toBe('Implement Multi-Factor Authentication');\r\n      expect(integrationEvent.data.recommendation.priority).toBe('high');\r\n      expect(integrationEvent.data.implementation.effort).toBe('medium');\r\n      expect(integrationEvent.data.source.type).toBe('ai_analysis');\r\n      expect(integrationEvent.data.context.triggerType).toBe('vulnerability_found');\r\n      expect(integrationEvent.data.business.addressesCompliance).toBe(true);\r\n      expect(integrationEvent.data.approval.requiresApproval).toBe(true);\r\n      expect(integrationEvent.data.actions.count).toBe(2);\r\n    });\r\n  });\r\n\r\n  describe('JSON serialization', () => {\r\n    it('should serialize to JSON correctly', () => {\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, eventData);\r\n      const json = event.toJSON();\r\n      \r\n      expect(json.eventData).toEqual(eventData);\r\n      expect(json.analysis.isHighPriorityOrAbove).toBe(true);\r\n      expect(json.analysis.hasHighConfidence).toBe(true);\r\n      expect(json.analysis.addressesCompliance).toBe(true);\r\n      expect(json.analysis.valueScore).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should deserialize from JSON correctly', () => {\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, eventData);\r\n      const json = event.toJSON();\r\n      const deserializedEvent = RecommendationGeneratedDomainEvent.fromJSON(json);\r\n      \r\n      expect(deserializedEvent.aggregateId.equals(event.aggregateId)).toBe(true);\r\n      expect(deserializedEvent.recommendationId).toBe(event.recommendationId);\r\n      expect(deserializedEvent.title).toBe(event.title);\r\n      expect(deserializedEvent.priority).toBe(event.priority);\r\n    });\r\n  });\r\n\r\n  describe('human-readable description', () => {\r\n    it('should generate appropriate description', () => {\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, eventData);\r\n      const description = event.getDescription();\r\n      \r\n      expect(description).toContain('HIGH priority');\r\n      expect(description).toContain('security_action');\r\n      expect(description).toContain('Implement Multi-Factor Authentication');\r\n      expect(description).toContain('high confidence');\r\n      expect(description).toContain('medium effort');\r\n      expect(description).toContain('vulnerability found');\r\n    });\r\n  });\r\n\r\n  describe('event summary', () => {\r\n    it('should generate comprehensive summary', () => {\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, eventData);\r\n      const summary = event.getSummary();\r\n      \r\n      expect(summary.eventType).toBe('RecommendationGenerated');\r\n      expect(summary.recommendationId).toBe('rec-001');\r\n      expect(summary.type).toBe('security_action');\r\n      expect(summary.title).toBe('Implement Multi-Factor Authentication');\r\n      expect(summary.priority).toBe('high');\r\n      expect(summary.confidenceScore).toBe(85);\r\n      expect(summary.riskReductionPotential).toBe(75);\r\n      expect(summary.implementationEffort).toBe('medium');\r\n      expect(summary.triggerType).toBe('vulnerability_found');\r\n      expect(summary.requiresApproval).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('edge cases', () => {\r\n    it('should handle recommendations without alternatives', () => {\r\n      const noAlternativesData = { ...eventData };\r\n      delete noAlternativesData.alternatives;\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, noAlternativesData);\r\n      \r\n      expect(event.hasAlternatives()).toBe(false);\r\n    });\r\n\r\n    it('should handle recommendations without dependencies', () => {\r\n      const noDependenciesData = { ...eventData, dependencies: [] };\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, noDependenciesData);\r\n      \r\n      expect(event.hasDependencies()).toBe(false);\r\n    });\r\n\r\n    it('should handle recommendations without rollback plan', () => {\r\n      const noRollbackData = { ...eventData };\r\n      delete noRollbackData.rollbackPlan;\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, noRollbackData);\r\n      \r\n      expect(event.hasRollbackPlan()).toBe(false);\r\n    });\r\n\r\n    it('should handle high implementation risks', () => {\r\n      const highRiskData = {\r\n        ...eventData,\r\n        implementationRisks: [\r\n          {\r\n            risk: 'System failure',\r\n            likelihood: 'high',\r\n            impact: 'high',\r\n            mitigation: 'Comprehensive testing'\r\n          }\r\n        ]\r\n      };\r\n      const event = new RecommendationGeneratedDomainEvent(aggregateId, highRiskData);\r\n      \r\n      expect(event.hasHighImplementationRisks()).toBe(true);\r\n    });\r\n  });\r\n});"], "version": 3}