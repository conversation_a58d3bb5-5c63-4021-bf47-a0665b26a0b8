9540f56b72e0b2fbd0a7a3e8af5748b6
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIModelStatusChangedEvent = void 0;
const base_domain_event_1 = require("../../../../shared-kernel/domain/base-domain-event");
/**
 * AI Model Status Changed Domain Event
 *
 * Published when an AI model's status changes (e.g., active to inactive).
 * This event can trigger various downstream processes such as
 * load balancer updates, monitoring adjustments, and notification systems.
 */
class AIModelStatusChangedEvent extends base_domain_event_1.BaseDomainEvent {
    constructor(modelId, modelName, previousStatus, newStatus, eventId, occurredOn) {
        super(eventId, occurredOn);
        this.modelId = modelId;
        this.modelName = modelName;
        this.previousStatus = previousStatus;
        this.newStatus = newStatus;
    }
    getEventName() {
        return 'AIModelStatusChanged';
    }
    getEventVersion() {
        return '1.0';
    }
    getEventData() {
        return {
            modelId: this.modelId.toString(),
            modelName: this.modelName,
            previousStatus: this.previousStatus,
            newStatus: this.newStatus,
        };
    }
}
exports.AIModelStatusChangedEvent = AIModelStatusChangedEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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