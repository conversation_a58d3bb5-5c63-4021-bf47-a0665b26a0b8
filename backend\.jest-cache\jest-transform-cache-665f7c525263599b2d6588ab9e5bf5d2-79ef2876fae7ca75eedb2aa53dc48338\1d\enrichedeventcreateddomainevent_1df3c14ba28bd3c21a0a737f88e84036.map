{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\enriched-event-created.domain-event.ts", "mappings": ";;;AAAA,6DAA4E;AAE5E,sEAA6D;AAC7D,6EAAqE;AA0BrE;;;;;;;;;;GAUG;AACH,MAAa,+BAAgC,SAAQ,+BAA8C;IACjG,YACE,WAA2B,EAC3B,SAAwC,EACxC,OAOC;QAED,KAAK,CAAC,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,sBAAsB;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,oBAAoB;QACtB,OAAO,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,CAAC,mCAAa,CAAC,IAAI,EAAE,mCAAa,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,QAAQ,KAAK,mCAAa,CAAC,QAAQ,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,OAAO,CAAC,IAAI,CAAC,sBAAsB,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,gBAAgB,KAAK,SAAS,IAAI,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,gBAAgB,KAAK,wCAAgB,CAAC,SAAS,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,eAAe;QAkBb,OAAO;YACL,eAAe,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;YAC5C,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE;YACpD,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,sBAAsB,EAAE,IAAI,CAAC,sBAAsB;YACnD,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;YAC7C,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE;YACrC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE;YAC7B,wBAAwB,EAAE,IAAI,CAAC,wBAAwB,EAAE;YACzD,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE;YACnD,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE;YACnD,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EAAE;SAC1C,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,GAAG,KAAK,CAAC,MAAM,EAAE;YACjB,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE;SACrC,CAAC;IACJ,CAAC;CACF;AA3KD,0EA2KC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\enriched-event-created.domain-event.ts"], "sourcesContent": ["import { BaseDomainEvent, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { EventType } from '../enums/event-type.enum';\r\nimport { EventSeverity } from '../enums/event-severity.enum';\r\nimport { EnrichmentStatus } from '../entities/enriched-event.entity';\r\n\r\n/**\r\n * Enriched Event Created Domain Event Data\r\n */\r\nexport interface EnrichedEventCreatedEventData {\r\n  /** Normalized event ID that was enriched */\r\n  normalizedEventId: UniqueEntityId;\r\n  /** Type of the enriched event */\r\n  eventType: EventType;\r\n  /** Severity of the enriched event */\r\n  severity: EventSeverity;\r\n  /** Current enrichment status */\r\n  enrichmentStatus: EnrichmentStatus;\r\n  /** Enrichment quality score */\r\n  enrichmentQualityScore?: number;\r\n  /** Number of applied enrichment rules */\r\n  appliedRulesCount: number;\r\n  /** Number of enrichment data sources */\r\n  enrichmentDataCount: number;\r\n  /** Threat intelligence score */\r\n  threatIntelScore?: number;\r\n  /** Whether the event requires manual review */\r\n  requiresManualReview: boolean;\r\n}\r\n\r\n/**\r\n * Enriched Event Created Domain Event\r\n * \r\n * Raised when a new enriched security event is created in the system.\r\n * This event triggers various downstream processes including:\r\n * - Correlation pipeline initiation\r\n * - Threat analysis workflows\r\n * - Manual review queue management\r\n * - Metrics collection and monitoring\r\n * - Audit logging and compliance tracking\r\n */\r\nexport class EnrichedEventCreatedDomainEvent extends BaseDomainEvent<EnrichedEventCreatedEventData> {\r\n  constructor(\r\n    aggregateId: UniqueEntityId,\r\n    eventData: EnrichedEventCreatedEventData,\r\n    options?: {\r\n      eventId?: UniqueEntityId;\r\n      occurredOn?: Date;\r\n      eventVersion?: number;\r\n      correlationId?: string;\r\n      causationId?: string;\r\n      metadata?: Record<string, any>;\r\n    }\r\n  ) {\r\n    super(aggregateId, eventData, options);\r\n  }\r\n\r\n  /**\r\n   * Get the normalized event ID\r\n   */\r\n  get normalizedEventId(): UniqueEntityId {\r\n    return this.eventData.normalizedEventId;\r\n  }\r\n\r\n  /**\r\n   * Get the type of the enriched event\r\n   */\r\n  get eventType(): EventType {\r\n    return this.eventData.eventType;\r\n  }\r\n\r\n  /**\r\n   * Get the severity of the enriched event\r\n   */\r\n  get severity(): EventSeverity {\r\n    return this.eventData.severity;\r\n  }\r\n\r\n  /**\r\n   * Get the enrichment status\r\n   */\r\n  get enrichmentStatus(): EnrichmentStatus {\r\n    return this.eventData.enrichmentStatus;\r\n  }\r\n\r\n  /**\r\n   * Get the enrichment quality score\r\n   */\r\n  get enrichmentQualityScore(): number | undefined {\r\n    return this.eventData.enrichmentQualityScore;\r\n  }\r\n\r\n  /**\r\n   * Get the number of applied rules\r\n   */\r\n  get appliedRulesCount(): number {\r\n    return this.eventData.appliedRulesCount;\r\n  }\r\n\r\n  /**\r\n   * Get the number of enrichment data sources\r\n   */\r\n  get enrichmentDataCount(): number {\r\n    return this.eventData.enrichmentDataCount;\r\n  }\r\n\r\n  /**\r\n   * Get the threat intelligence score\r\n   */\r\n  get threatIntelScore(): number | undefined {\r\n    return this.eventData.threatIntelScore;\r\n  }\r\n\r\n  /**\r\n   * Check if the event requires manual review\r\n   */\r\n  get requiresManualReview(): boolean {\r\n    return this.eventData.requiresManualReview;\r\n  }\r\n\r\n  /**\r\n   * Check if the enriched event is high severity\r\n   */\r\n  isHighSeverity(): boolean {\r\n    return [EventSeverity.HIGH, EventSeverity.CRITICAL].includes(this.severity);\r\n  }\r\n\r\n  /**\r\n   * Check if the enriched event is critical\r\n   */\r\n  isCritical(): boolean {\r\n    return this.severity === EventSeverity.CRITICAL;\r\n  }\r\n\r\n  /**\r\n   * Check if the event has high enrichment quality\r\n   */\r\n  hasHighEnrichmentQuality(): boolean {\r\n    return (this.enrichmentQualityScore || 0) >= 70;\r\n  }\r\n\r\n  /**\r\n   * Check if the event has threat intelligence data\r\n   */\r\n  hasThreatIntelligence(): boolean {\r\n    return this.threatIntelScore !== undefined && this.threatIntelScore > 0;\r\n  }\r\n\r\n  /**\r\n   * Check if enrichment is completed\r\n   */\r\n  isEnrichmentCompleted(): boolean {\r\n    return this.enrichmentStatus === EnrichmentStatus.COMPLETED;\r\n  }\r\n\r\n  /**\r\n   * Check if the event has high threat intelligence score\r\n   */\r\n  isHighThreatRisk(): boolean {\r\n    return (this.threatIntelScore || 0) >= 85;\r\n  }\r\n\r\n  /**\r\n   * Get event summary for handlers\r\n   */\r\n  getEventSummary(): {\r\n    enrichedEventId: string;\r\n    normalizedEventId: string;\r\n    eventType: EventType;\r\n    severity: EventSeverity;\r\n    enrichmentStatus: EnrichmentStatus;\r\n    enrichmentQualityScore?: number;\r\n    appliedRulesCount: number;\r\n    enrichmentDataCount: number;\r\n    threatIntelScore?: number;\r\n    requiresManualReview: boolean;\r\n    isHighSeverity: boolean;\r\n    isCritical: boolean;\r\n    hasHighEnrichmentQuality: boolean;\r\n    hasThreatIntelligence: boolean;\r\n    isEnrichmentCompleted: boolean;\r\n    isHighThreatRisk: boolean;\r\n  } {\r\n    return {\r\n      enrichedEventId: this.aggregateId.toString(),\r\n      normalizedEventId: this.normalizedEventId.toString(),\r\n      eventType: this.eventType,\r\n      severity: this.severity,\r\n      enrichmentStatus: this.enrichmentStatus,\r\n      enrichmentQualityScore: this.enrichmentQualityScore,\r\n      appliedRulesCount: this.appliedRulesCount,\r\n      enrichmentDataCount: this.enrichmentDataCount,\r\n      threatIntelScore: this.threatIntelScore,\r\n      requiresManualReview: this.requiresManualReview,\r\n      isHighSeverity: this.isHighSeverity(),\r\n      isCritical: this.isCritical(),\r\n      hasHighEnrichmentQuality: this.hasHighEnrichmentQuality(),\r\n      hasThreatIntelligence: this.hasThreatIntelligence(),\r\n      isEnrichmentCompleted: this.isEnrichmentCompleted(),\r\n      isHighThreatRisk: this.isHighThreatRisk(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      ...super.toJSON(),\r\n      eventSummary: this.getEventSummary(),\r\n    };\r\n  }\r\n}"], "version": 3}