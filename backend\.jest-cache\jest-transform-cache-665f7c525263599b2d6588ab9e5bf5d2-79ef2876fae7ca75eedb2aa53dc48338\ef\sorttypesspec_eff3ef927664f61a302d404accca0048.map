{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\types\\sort.types.spec.ts", "mappings": ";AAAA;;GAEG;;AAEH,yCAA+B;AAE/B,yCAAqC;AA0BrC,uDAMgC;AAEhC,IAAA,oBAAQ,EAAC,WAAW,EAAE,GAAG,EAAE;IACzB,IAAA,oBAAQ,EAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,IAAA,cAAE,EAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,UAAU,GAAe;gBAC7B,IAAI,EAAE;oBACJ,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,0BAAa,CAAC,GAAG,EAAE;oBAC/C,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,0BAAa,CAAC,IAAI,EAAE;iBACjD;aACF,CAAC;YAEF,MAAM,MAAM,GAAe;gBACzB,aAAa,EAAE,CAAC;gBAChB,cAAc,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;gBAChC,gBAAgB,EAAE,0BAAa,CAAC,GAAG;gBACnC,kBAAkB,EAAE,IAAI;gBACxB,eAAe,EAAE,KAAK;aACvB,CAAC;YAEF,MAAM,MAAM,GAAG,sBAAS,CAAC,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YAE1D,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,UAAU,GAAe;gBAC7B,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;oBACzC,KAAK,EAAE,QAAQ,CAAC,EAAE;oBAClB,SAAS,EAAE,0BAAa,CAAC,GAAG;iBAC7B,CAAC,CAAC;aACJ,CAAC;YAEF,MAAM,MAAM,GAAe;gBACzB,aAAa,EAAE,CAAC;gBAChB,cAAc,EAAE,EAAE;gBAClB,gBAAgB,EAAE,0BAAa,CAAC,GAAG;gBACnC,kBAAkB,EAAE,IAAI;gBACxB,eAAe,EAAE,KAAK;aACvB,CAAC;YAEF,MAAM,MAAM,GAAG,sBAAS,CAAC,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YAE1D,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,uDAAuD,CAAC,CAAC;QAC3F,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,UAAU,GAAe;gBAC7B,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,SAAS,EAAE,0BAAa,CAAC,GAAG,EAAE,CAAC;aAC9D,CAAC;YAEF,MAAM,MAAM,GAAe;gBACzB,aAAa,EAAE,CAAC;gBAChB,cAAc,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;gBAChC,gBAAgB,EAAE,0BAAa,CAAC,GAAG;gBACnC,kBAAkB,EAAE,IAAI;gBACxB,eAAe,EAAE,KAAK;aACvB,CAAC;YAEF,MAAM,MAAM,GAAG,sBAAS,CAAC,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YAE1D,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,UAAU,GAAe;gBAC7B,IAAI,EAAE;oBACJ,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,0BAAa,CAAC,GAAG,EAAE;oBAC/C,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,0BAAa,CAAC,IAAI,EAAE;iBACjD;aACF,CAAC;YAEF,MAAM,MAAM,GAAG,sBAAS,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YAElD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,mCAAmC,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,WAAW,EAAE,GAAG,EAAE;QACzB,IAAA,cAAE,EAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,KAAK,GAAG,EAAE,IAAI,EAAE,iBAAiB,EAAE,CAAC;YAC1C,MAAM,MAAM,GAAG,sBAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAE1C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;gBAC7B,KAAK,EAAE,MAAM;gBACb,SAAS,EAAE,0BAAa,CAAC,GAAG;gBAC5B,QAAQ,EAAE,CAAC;aACZ,CAAC,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;gBAC7B,KAAK,EAAE,MAAM;gBACb,SAAS,EAAE,0BAAa,CAAC,IAAI;gBAC7B,QAAQ,EAAE,CAAC;aACZ,CAAC,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;gBAC7B,KAAK,EAAE,KAAK;gBACZ,SAAS,EAAE,0BAAa,CAAC,GAAG;gBAC5B,QAAQ,EAAE,CAAC;aACZ,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,KAAK,GAAG,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,CAAC;YAC1C,MAAM,MAAM,GAAG,sBAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAE1C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,0BAAa,CAAC,IAAI,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,IAAA,cAAE,EAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,UAAU,GAAe;gBAC7B,IAAI,EAAE;oBACJ,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,0BAAa,CAAC,GAAG,EAAE;oBAC/C,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,0BAAa,CAAC,IAAI,EAAE;iBACjD;aACF,CAAC;YAEF,MAAM,MAAM,GAAG,sBAAS,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;YAEnD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,UAAU,GAAe,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;YAC5C,MAAM,MAAM,GAAG,sBAAS,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;YAEnD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,WAAW,EAAE,GAAG,EAAE;QACzB,MAAM,QAAQ,GAAG;YACf,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE;YAC1D,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE;YACxD,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE;SACvD,CAAC;QAEF,IAAA,cAAE,EAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,UAAU,GAAe;gBAC7B,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,0BAAa,CAAC,GAAG,EAAE,CAAC;aACxD,CAAC;YAEF,MAAM,MAAM,GAAG,sBAAS,CAAC,SAAS,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAEzD,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,UAAU,GAAe;gBAC7B,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,0BAAa,CAAC,IAAI,EAAE,CAAC;aACxD,CAAC;YAEF,MAAM,MAAM,GAAG,sBAAS,CAAC,SAAS,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAEzD,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,UAAU,GAAe;gBAC7B,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,0BAAa,CAAC,GAAG,EAAE,CAAC;aACxD,CAAC;YAEF,MAAM,MAAM,GAAG,sBAAS,CAAC,SAAS,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAEzD,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;YACxE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;YACxE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,IAAI,GAAG;gBACX,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE;gBAC1B,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE;gBACxB,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE;aAC3B,CAAC;YAEF,MAAM,UAAU,GAAe;gBAC7B,IAAI,EAAE;oBACJ,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,0BAAa,CAAC,GAAG,EAAE;oBAC/C,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,0BAAa,CAAC,IAAI,EAAE;iBAChD;aACF,CAAC;YAEF,MAAM,MAAM,GAAG,sBAAS,CAAC,SAAS,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;YAErD,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,UAAU,GAAe;gBAC7B,IAAI,EAAE,CAAC;wBACL,KAAK,EAAE,MAAM;wBACb,SAAS,EAAE,0BAAa,CAAC,GAAG;wBAC5B,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM;qBACpD,CAAC;aACH,CAAC;YAEF,MAAM,MAAM,GAAG,sBAAS,CAAC,SAAS,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAEzD,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,eAAe;YACnD,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;YACrD,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,eAAe;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,IAAA,cAAE,EAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,KAAK,GAAG,sBAAS,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YAEvC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACjC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,0BAAa,CAAC,GAAG,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,KAAK,GAAG,sBAAS,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YAExC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACjC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,0BAAa,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,UAAU,GAAG,sBAAS,CAAC,MAAM,CACjC,sBAAS,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,EACxB,sBAAS,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAC1B,CAAC;YAEF,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,yBAAyB;YACxE,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,0BAA0B;QAC3E,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,CAAC,sBAAS,CAAC,gBAAgB,CAAC,0BAAa,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,0BAAa,CAAC,IAAI,CAAC,CAAC;YAC/E,MAAM,CAAC,sBAAS,CAAC,gBAAgB,CAAC,0BAAa,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,0BAAa,CAAC,GAAG,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,CAAC,sBAAS,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,sBAAS,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC,sBAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,UAAU,GAAe;gBAC7B,IAAI,EAAE;oBACJ,sBAAS,CAAC,GAAG,CAAC,MAAM,CAAC;oBACrB,sBAAS,CAAC,IAAI,CAAC,MAAM,CAAC;iBACvB;aACF,CAAC;YAEF,MAAM,MAAM,GAAG,sBAAS,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;YAEzD,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,IAAA,cAAE,EAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,UAAU,GAAe;gBAC7B,IAAI,EAAE;oBACJ,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,0BAAa,CAAC,GAAG,EAAE;oBAC/C,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,0BAAa,CAAC,IAAI,EAAE;iBACjD;aACF,CAAC;YAEF,MAAM,MAAM,GAAG,sBAAS,CAAC,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAE3D,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,UAAU,GAAe;gBAC7B,IAAI,EAAE;oBACJ,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,0BAAa,CAAC,GAAG,EAAE;oBAC/C,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,0BAAa,CAAC,IAAI,EAAE;iBACjD;aACF,CAAC;YAEF,MAAM,MAAM,GAAG,sBAAS,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAEjD,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,IAAI,EAAE,CAAC;gBACP,IAAI,EAAE,CAAC,CAAC;aACT,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\types\\sort.types.spec.ts"], "sourcesContent": ["/**\r\n * Sort Types Tests\r\n */\r\n\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport {\r\n  SortDirection,\r\n  SortUtils,\r\n  SortField,\r\n  SortParams,\r\n  SortConfig,\r\n} from '../../types/sort.types';\r\n\r\ndescribe('SortUtils', () => {\r\n  describe('validateSort', () => {\r\n    it('should validate sort parameters successfully', () => {\r\n      const sortParams: SortParams = {\r\n        sort: [\r\n          { field: 'name', direction: SortDirection.ASC },\r\n          { field: 'date', direction: SortDirection.DESC },\r\n        ],\r\n      };\r\n\r\n      const config: SortConfig = {\r\n        maxSortFields: 5,\r\n        sortableFields: ['name', 'date'],\r\n        defaultDirection: SortDirection.ASC,\r\n        allowCaseSensitive: true,\r\n        allowCustomSort: false,\r\n      };\r\n\r\n      const result = SortUtils.validateSort(sortParams, config);\r\n\r\n      expect(result.isValid).toBe(true);\r\n      expect(result.errors).toHaveLength(0);\r\n    });\r\n\r\n    it('should fail validation when exceeding max sort fields', () => {\r\n      const sortParams: SortParams = {\r\n        sort: Array.from({ length: 6 }, (_, i) => ({\r\n          field: `field${i}`,\r\n          direction: SortDirection.ASC,\r\n        })),\r\n      };\r\n\r\n      const config: SortConfig = {\r\n        maxSortFields: 5,\r\n        sortableFields: [],\r\n        defaultDirection: SortDirection.ASC,\r\n        allowCaseSensitive: true,\r\n        allowCustomSort: false,\r\n      };\r\n\r\n      const result = SortUtils.validateSort(sortParams, config);\r\n\r\n      expect(result.isValid).toBe(false);\r\n      expect(result.errors).toContain('Number of sort fields (6) exceeds maximum allowed (5)');\r\n    });\r\n\r\n    it('should fail validation for non-sortable fields', () => {\r\n      const sortParams: SortParams = {\r\n        sort: [{ field: 'restricted', direction: SortDirection.ASC }],\r\n      };\r\n\r\n      const config: SortConfig = {\r\n        maxSortFields: 5,\r\n        sortableFields: ['name', 'date'],\r\n        defaultDirection: SortDirection.ASC,\r\n        allowCaseSensitive: true,\r\n        allowCustomSort: false,\r\n      };\r\n\r\n      const result = SortUtils.validateSort(sortParams, config);\r\n\r\n      expect(result.isValid).toBe(false);\r\n      expect(result.errors).toContain(\"Field 'restricted' is not sortable\");\r\n    });\r\n\r\n    it('should detect duplicate sort fields', () => {\r\n      const sortParams: SortParams = {\r\n        sort: [\r\n          { field: 'name', direction: SortDirection.ASC },\r\n          { field: 'name', direction: SortDirection.DESC },\r\n        ],\r\n      };\r\n\r\n      const result = SortUtils.validateSort(sortParams);\r\n\r\n      expect(result.isValid).toBe(false);\r\n      expect(result.errors).toContain(\"Duplicate sort field 'name' found\");\r\n    });\r\n  });\r\n\r\n  describe('fromQuery', () => {\r\n    it('should parse sort from query string', () => {\r\n      const query = { sort: 'name,-date,+age' };\r\n      const result = SortUtils.fromQuery(query);\r\n\r\n      expect(result.sort).toHaveLength(3);\r\n      expect(result.sort[0]).toEqual({\r\n        field: 'name',\r\n        direction: SortDirection.ASC,\r\n        priority: 0,\r\n      });\r\n      expect(result.sort[1]).toEqual({\r\n        field: 'date',\r\n        direction: SortDirection.DESC,\r\n        priority: 1,\r\n      });\r\n      expect(result.sort[2]).toEqual({\r\n        field: 'age',\r\n        direction: SortDirection.ASC,\r\n        priority: 2,\r\n      });\r\n    });\r\n\r\n    it('should handle array sort parameter', () => {\r\n      const query = { sort: ['name', '-date'] };\r\n      const result = SortUtils.fromQuery(query);\r\n\r\n      expect(result.sort).toHaveLength(2);\r\n      expect(result.sort[0].field).toBe('name');\r\n      expect(result.sort[1].field).toBe('date');\r\n      expect(result.sort[1].direction).toBe(SortDirection.DESC);\r\n    });\r\n  });\r\n\r\n  describe('toQueryString', () => {\r\n    it('should convert sort parameters to query string', () => {\r\n      const sortParams: SortParams = {\r\n        sort: [\r\n          { field: 'name', direction: SortDirection.ASC },\r\n          { field: 'date', direction: SortDirection.DESC },\r\n        ],\r\n      };\r\n\r\n      const result = SortUtils.toQueryString(sortParams);\r\n\r\n      expect(result).toBe('sort=name%2C-date');\r\n    });\r\n\r\n    it('should return empty string for empty sort', () => {\r\n      const sortParams: SortParams = { sort: [] };\r\n      const result = SortUtils.toQueryString(sortParams);\r\n\r\n      expect(result).toBe('');\r\n    });\r\n  });\r\n\r\n  describe('applySort', () => {\r\n    const testData = [\r\n      { name: 'Charlie', age: 30, date: new Date('2023-01-01') },\r\n      { name: 'Alice', age: 25, date: new Date('2023-02-01') },\r\n      { name: 'Bob', age: 35, date: new Date('2022-12-01') },\r\n    ];\r\n\r\n    it('should sort by string field ascending', () => {\r\n      const sortParams: SortParams = {\r\n        sort: [{ field: 'name', direction: SortDirection.ASC }],\r\n      };\r\n\r\n      const result = SortUtils.applySort(testData, sortParams);\r\n\r\n      expect(result[0].name).toBe('Alice');\r\n      expect(result[1].name).toBe('Bob');\r\n      expect(result[2].name).toBe('Charlie');\r\n    });\r\n\r\n    it('should sort by number field descending', () => {\r\n      const sortParams: SortParams = {\r\n        sort: [{ field: 'age', direction: SortDirection.DESC }],\r\n      };\r\n\r\n      const result = SortUtils.applySort(testData, sortParams);\r\n\r\n      expect(result[0].age).toBe(35);\r\n      expect(result[1].age).toBe(30);\r\n      expect(result[2].age).toBe(25);\r\n    });\r\n\r\n    it('should sort by date field', () => {\r\n      const sortParams: SortParams = {\r\n        sort: [{ field: 'date', direction: SortDirection.ASC }],\r\n      };\r\n\r\n      const result = SortUtils.applySort(testData, sortParams);\r\n\r\n      expect(result[0].date.getTime()).toBe(new Date('2022-12-01').getTime());\r\n      expect(result[1].date.getTime()).toBe(new Date('2023-01-01').getTime());\r\n      expect(result[2].date.getTime()).toBe(new Date('2023-02-01').getTime());\r\n    });\r\n\r\n    it('should handle multiple sort fields', () => {\r\n      const data = [\r\n        { name: 'Alice', age: 30 },\r\n        { name: 'Bob', age: 25 },\r\n        { name: 'Alice', age: 25 },\r\n      ];\r\n\r\n      const sortParams: SortParams = {\r\n        sort: [\r\n          { field: 'name', direction: SortDirection.ASC },\r\n          { field: 'age', direction: SortDirection.DESC },\r\n        ],\r\n      };\r\n\r\n      const result = SortUtils.applySort(data, sortParams);\r\n\r\n      expect(result[0]).toEqual({ name: 'Alice', age: 30 });\r\n      expect(result[1]).toEqual({ name: 'Alice', age: 25 });\r\n      expect(result[2]).toEqual({ name: 'Bob', age: 25 });\r\n    });\r\n\r\n    it('should handle custom sort function', () => {\r\n      const sortParams: SortParams = {\r\n        sort: [{\r\n          field: 'name',\r\n          direction: SortDirection.ASC,\r\n          customSort: (a, b) => a.name.length - b.name.length,\r\n        }],\r\n      };\r\n\r\n      const result = SortUtils.applySort(testData, sortParams);\r\n\r\n      expect(result[0].name).toBe('Bob'); // 3 characters\r\n      expect(result[1].name).toBe('Alice'); // 5 characters\r\n      expect(result[2].name).toBe('Charlie'); // 7 characters\r\n    });\r\n  });\r\n\r\n  describe('helper methods', () => {\r\n    it('should create ascending sort field', () => {\r\n      const field = SortUtils.asc('name', 1);\r\n\r\n      expect(field.field).toBe('name');\r\n      expect(field.direction).toBe(SortDirection.ASC);\r\n      expect(field.priority).toBe(1);\r\n    });\r\n\r\n    it('should create descending sort field', () => {\r\n      const field = SortUtils.desc('date', 2);\r\n\r\n      expect(field.field).toBe('date');\r\n      expect(field.direction).toBe(SortDirection.DESC);\r\n      expect(field.priority).toBe(2);\r\n    });\r\n\r\n    it('should create sort parameters from fields', () => {\r\n      const sortParams = SortUtils.create(\r\n        SortUtils.asc('name', 1),\r\n        SortUtils.desc('date', 0)\r\n      );\r\n\r\n      expect(sortParams.sort).toHaveLength(2);\r\n      expect(sortParams.sort[0].field).toBe('date'); // Priority 0 comes first\r\n      expect(sortParams.sort[1].field).toBe('name'); // Priority 1 comes second\r\n    });\r\n\r\n    it('should reverse sort direction', () => {\r\n      expect(SortUtils.reverseDirection(SortDirection.ASC)).toBe(SortDirection.DESC);\r\n      expect(SortUtils.reverseDirection(SortDirection.DESC)).toBe(SortDirection.ASC);\r\n    });\r\n\r\n    it('should check if sort parameters are empty', () => {\r\n      expect(SortUtils.isEmpty({ sort: [] })).toBe(true);\r\n      expect(SortUtils.isEmpty({ sort: [SortUtils.asc('name')] })).toBe(false);\r\n    });\r\n\r\n    it('should get referenced fields', () => {\r\n      const sortParams: SortParams = {\r\n        sort: [\r\n          SortUtils.asc('name'),\r\n          SortUtils.desc('date'),\r\n        ],\r\n      };\r\n\r\n      const fields = SortUtils.getReferencedFields(sortParams);\r\n\r\n      expect(fields).toEqual(['name', 'date']);\r\n    });\r\n  });\r\n\r\n  describe('SQL and MongoDB conversion', () => {\r\n    it('should convert to SQL ORDER BY clause', () => {\r\n      const sortParams: SortParams = {\r\n        sort: [\r\n          { field: 'name', direction: SortDirection.ASC },\r\n          { field: 'date', direction: SortDirection.DESC },\r\n        ],\r\n      };\r\n\r\n      const result = SortUtils.toSqlOrderBy(sortParams, 'users');\r\n\r\n      expect(result).toBe('ORDER BY users.name ASC, users.date DESC');\r\n    });\r\n\r\n    it('should convert to MongoDB sort object', () => {\r\n      const sortParams: SortParams = {\r\n        sort: [\r\n          { field: 'name', direction: SortDirection.ASC },\r\n          { field: 'date', direction: SortDirection.DESC },\r\n        ],\r\n      };\r\n\r\n      const result = SortUtils.toMongoSort(sortParams);\r\n\r\n      expect(result).toEqual({\r\n        name: 1,\r\n        date: -1,\r\n      });\r\n    });\r\n  });\r\n});"], "version": 3}