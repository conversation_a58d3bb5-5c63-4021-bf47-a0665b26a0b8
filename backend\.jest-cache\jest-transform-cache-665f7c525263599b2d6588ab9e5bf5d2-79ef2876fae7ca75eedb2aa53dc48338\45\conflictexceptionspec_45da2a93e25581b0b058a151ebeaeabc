4e805d04174d98fcf5b6d25d10d048e4
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const conflict_exception_1 = require("../../exceptions/conflict.exception");
describe('ConflictException', () => {
    describe('constructor', () => {
        it('should create exception with required parameters', () => {
            const exception = new conflict_exception_1.ConflictException('Test conflict', 'test_conflict');
            expect(exception).toBeInstanceOf(conflict_exception_1.ConflictException);
            expect(exception.message).toBe('Test conflict');
            expect(exception.code).toBe('CONFLICT');
            expect(exception.severity).toBe('medium');
            expect(exception.category).toBe('conflict');
            expect(exception.conflictType).toBe('test_conflict');
        });
        it('should create exception with all options', () => {
            const exception = new conflict_exception_1.ConflictException('Test conflict', 'duplicate_resource', {
                resourceType: 'User',
                resourceId: 'user-123',
                conflictingValue: '<EMAIL>',
                existingValue: '<EMAIL>',
                conflictField: 'email',
                correlationId: 'test-correlation-id',
            });
            expect(exception.message).toBe('Test conflict');
            expect(exception.conflictType).toBe('duplicate_resource');
            expect(exception.resourceType).toBe('User');
            expect(exception.resourceId).toBe('user-123');
            expect(exception.conflictingValue).toBe('<EMAIL>');
            expect(exception.existingValue).toBe('<EMAIL>');
            expect(exception.conflictField).toBe('email');
            expect(exception.correlationId).toBe('test-correlation-id');
        });
    });
    describe('static factory methods', () => {
        describe('duplicateResource', () => {
            it('should create exception for duplicate resource', () => {
                const exception = conflict_exception_1.ConflictException.duplicateResource('User', 'email', '<EMAIL>', {
                    resourceId: 'user-123',
                    existingResourceId: 'user-456',
                });
                expect(exception.message).toBe("User with email '<EMAIL>' already exists");
                expect(exception.conflictType).toBe('duplicate_resource');
                expect(exception.resourceType).toBe('User');
                expect(exception.conflictField).toBe('email');
                expect(exception.conflictingValue).toBe('<EMAIL>');
                expect(exception.getExistingResourceId()).toBe('user-456');
                expect(exception.isDuplicateResource()).toBe(true);
            });
        });
        describe('uniqueConstraintViolation', () => {
            it('should create exception for unique constraint violation', () => {
                const exception = conflict_exception_1.ConflictException.uniqueConstraintViolation('Product', 'unique_sku', 'SKU-123');
                expect(exception.message).toBe("Unique constraint 'unique_sku' violated for Product");
                expect(exception.conflictType).toBe('unique_constraint_violation');
                expect(exception.resourceType).toBe('Product');
                expect(exception.conflictField).toBe('unique_sku');
                expect(exception.conflictingValue).toBe('SKU-123');
                expect(exception.isUniqueConstraintViolation()).toBe(true);
            });
        });
        describe('versionConflict', () => {
            it('should create exception for version conflict', () => {
                const exception = conflict_exception_1.ConflictException.versionConflict('Document', 'doc-123', 5, 7);
                expect(exception.message).toBe("Version conflict for Document 'doc-123': expected 5, got 7");
                expect(exception.conflictType).toBe('version_conflict');
                expect(exception.resourceType).toBe('Document');
                expect(exception.resourceId).toBe('doc-123');
                expect(exception.conflictField).toBe('version');
                expect(exception.conflictingValue).toBe(5);
                expect(exception.existingValue).toBe(7);
                expect(exception.isVersionConflict()).toBe(true);
                const versionInfo = exception.getVersionInfo();
                expect(versionInfo).toEqual({ expected: 5, actual: 7 });
            });
        });
        describe('stateConflict', () => {
            it('should create exception for state conflict', () => {
                const exception = conflict_exception_1.ConflictException.stateConflict('Order', 'order-123', 'shipped', 'pending', 'cancel', {
                    allowedStates: ['pending', 'processing'],
                });
                expect(exception.message).toBe("Cannot cancel Order 'order-123' in state 'shipped'. Required state: 'pending'");
                expect(exception.conflictType).toBe('state_conflict');
                expect(exception.resourceType).toBe('Order');
                expect(exception.resourceId).toBe('order-123');
                expect(exception.conflictField).toBe('state');
                expect(exception.conflictingValue).toBe('pending');
                expect(exception.existingValue).toBe('shipped');
                expect(exception.isStateConflict()).toBe(true);
                const stateInfo = exception.getStateInfo();
                expect(stateInfo).toEqual({
                    current: 'shipped',
                    required: 'pending',
                    operation: 'cancel',
                    allowedStates: ['pending', 'processing'],
                });
            });
        });
        describe('businessRuleConflict', () => {
            it('should create exception for business rule conflict', () => {
                const exception = conflict_exception_1.ConflictException.businessRuleConflict('max_concurrent_sessions', 'User', 'user-123', 'User cannot have more than 3 concurrent sessions', {
                    conflictingValue: 4,
                });
                expect(exception.message).toBe("Business rule 'max_concurrent_sessions' conflict for User 'user-123': User cannot have more than 3 concurrent sessions");
                expect(exception.conflictType).toBe('business_rule_conflict');
                expect(exception.resourceType).toBe('User');
                expect(exception.resourceId).toBe('user-123');
                expect(exception.conflictingValue).toBe(4);
                expect(exception.isBusinessRuleConflict()).toBe(true);
                const ruleInfo = exception.getBusinessRuleInfo();
                expect(ruleInfo).toEqual({
                    ruleName: 'max_concurrent_sessions',
                    ruleDescription: 'User cannot have more than 3 concurrent sessions',
                });
            });
        });
        describe('concurrentModification', () => {
            it('should create exception for concurrent modification', () => {
                const lastModifiedAt = new Date('2023-01-01T12:00:00Z');
                const exception = conflict_exception_1.ConflictException.concurrentModification('Document', 'doc-123', 'update', {
                    conflictingOperation: 'delete',
                    lastModifiedBy: 'user-456',
                    lastModifiedAt,
                });
                expect(exception.message).toBe("Concurrent modification detected for Document 'doc-123' during update");
                expect(exception.conflictType).toBe('concurrent_modification');
                expect(exception.resourceType).toBe('Document');
                expect(exception.resourceId).toBe('doc-123');
                expect(exception.isConcurrentModification()).toBe(true);
                const modInfo = exception.getConcurrentModificationInfo();
                expect(modInfo).toEqual({
                    operation: 'update',
                    conflictingOperation: 'delete',
                    lastModifiedBy: 'user-456',
                    lastModifiedAt,
                });
            });
        });
        describe('dependencyConflict', () => {
            it('should create exception for dependency conflict', () => {
                const dependentIds = ['child-1', 'child-2', 'child-3'];
                const exception = conflict_exception_1.ConflictException.dependencyConflict('Category', 'cat-123', 'Product', dependentIds, 'delete');
                expect(exception.message).toBe("Cannot delete Category 'cat-123' due to dependent Product: child-1, child-2, child-3");
                expect(exception.conflictType).toBe('dependency_conflict');
                expect(exception.resourceType).toBe('Category');
                expect(exception.resourceId).toBe('cat-123');
                expect(exception.isDependencyConflict()).toBe(true);
                const depInfo = exception.getDependencyInfo();
                expect(depInfo).toEqual({
                    dependentResourceType: 'Product',
                    dependentResourceIds: dependentIds,
                    dependentCount: 3,
                    operation: 'delete',
                });
            });
            it('should truncate long dependency lists', () => {
                const dependentIds = ['child-1', 'child-2', 'child-3', 'child-4', 'child-5'];
                const exception = conflict_exception_1.ConflictException.dependencyConflict('Category', 'cat-123', 'Product', dependentIds, 'delete');
                expect(exception.message).toBe("Cannot delete Category 'cat-123' due to dependent Product: child-1, child-2, child-3 and 2 more");
            });
        });
        describe('resourceLocked', () => {
            it('should create exception for resource lock conflict', () => {
                const lockAcquiredAt = new Date('2023-01-01T12:00:00Z');
                const lockExpiresAt = new Date('2023-01-01T13:00:00Z');
                const exception = conflict_exception_1.ConflictException.resourceLocked('Document', 'doc-123', 'user-456', lockAcquiredAt, 'edit', {
                    lockType: 'exclusive',
                    lockExpiresAt,
                });
                expect(exception.message).toBe("Document 'doc-123' is locked by 'user-456' and cannot be used for edit");
                expect(exception.conflictType).toBe('resource_locked');
                expect(exception.resourceType).toBe('Document');
                expect(exception.resourceId).toBe('doc-123');
                expect(exception.isResourceLocked()).toBe(true);
                const lockInfo = exception.getLockInfo();
                expect(lockInfo).toEqual({
                    lockedBy: 'user-456',
                    lockAcquiredAt,
                    lockType: 'exclusive',
                    lockExpiresAt,
                    operation: 'edit',
                });
            });
        });
    });
    describe('type checking methods', () => {
        it('should correctly identify conflict types', () => {
            const duplicateException = conflict_exception_1.ConflictException.duplicateResource('User', 'email', '<EMAIL>');
            const versionException = conflict_exception_1.ConflictException.versionConflict('Doc', 'doc-1', 1, 2);
            const stateException = conflict_exception_1.ConflictException.stateConflict('Order', 'order-1', 'shipped', 'pending', 'cancel');
            expect(duplicateException.isDuplicateResource()).toBe(true);
            expect(duplicateException.isVersionConflict()).toBe(false);
            expect(duplicateException.isStateConflict()).toBe(false);
            expect(versionException.isDuplicateResource()).toBe(false);
            expect(versionException.isVersionConflict()).toBe(true);
            expect(versionException.isStateConflict()).toBe(false);
            expect(stateException.isDuplicateResource()).toBe(false);
            expect(stateException.isVersionConflict()).toBe(false);
            expect(stateException.isStateConflict()).toBe(true);
        });
    });
    describe('getUserMessage', () => {
        it('should return user-friendly message for duplicate resource', () => {
            const exception = conflict_exception_1.ConflictException.duplicateResource('User', 'email', '<EMAIL>');
            expect(exception.getUserMessage()).toBe('This user already exists');
        });
        it('should return user-friendly message for unique constraint violation', () => {
            const exception = conflict_exception_1.ConflictException.uniqueConstraintViolation('Product', 'sku', 'SKU-123');
            expect(exception.getUserMessage()).toBe('A product with this information already exists');
        });
        it('should return user-friendly message for version conflict', () => {
            const exception = conflict_exception_1.ConflictException.versionConflict('Document', 'doc-1', 1, 2);
            expect(exception.getUserMessage()).toBe('This resource has been modified by another user. Please refresh and try again');
        });
        it('should return user-friendly message for state conflict', () => {
            const exception = conflict_exception_1.ConflictException.stateConflict('Order', 'order-1', 'shipped', 'pending', 'cancel');
            expect(exception.getUserMessage()).toBe('This operation cannot be performed due to the current state of the resource');
        });
        it('should return user-friendly message for business rule conflict', () => {
            const exception = conflict_exception_1.ConflictException.businessRuleConflict('rule', 'User', 'user-1', 'Rule violated');
            expect(exception.getUserMessage()).toBe('This operation violates a business rule and cannot be completed');
        });
        it('should return user-friendly message for concurrent modification', () => {
            const exception = conflict_exception_1.ConflictException.concurrentModification('Doc', 'doc-1', 'update');
            expect(exception.getUserMessage()).toBe('This resource is being modified by another user. Please try again later');
        });
        it('should return user-friendly message for dependency conflict', () => {
            const exception = conflict_exception_1.ConflictException.dependencyConflict('Cat', 'cat-1', 'Product', ['prod-1'], 'delete');
            expect(exception.getUserMessage()).toBe('This operation cannot be completed due to dependent resources');
        });
        it('should return user-friendly message for resource locked', () => {
            const exception = conflict_exception_1.ConflictException.resourceLocked('Doc', 'doc-1', 'user-1', new Date(), 'edit');
            expect(exception.getUserMessage()).toBe('This resource is currently locked and cannot be modified');
        });
        it('should return default message for unknown conflict type', () => {
            const exception = new conflict_exception_1.ConflictException('Test conflict', 'unknown_conflict');
            expect(exception.getUserMessage()).toBe('A conflict occurred while processing your request');
        });
    });
    describe('getResolutionSuggestions', () => {
        it('should provide suggestions for duplicate resource', () => {
            const exception = conflict_exception_1.ConflictException.duplicateResource('User', 'email', '<EMAIL>');
            const suggestions = exception.getResolutionSuggestions();
            expect(suggestions).toContain('Use a different value for the conflicting field');
            expect(suggestions).toContain('Update the existing resource instead of creating a new one');
        });
        it('should provide suggestions for version conflict', () => {
            const exception = conflict_exception_1.ConflictException.versionConflict('Doc', 'doc-1', 1, 2);
            const suggestions = exception.getResolutionSuggestions();
            expect(suggestions).toContain('Refresh the resource and retry the operation');
            expect(suggestions).toContain('Merge your changes with the latest version');
        });
        it('should provide suggestions for state conflict with allowed states', () => {
            const exception = conflict_exception_1.ConflictException.stateConflict('Order', 'order-1', 'shipped', 'pending', 'cancel', {
                allowedStates: ['pending', 'processing'],
            });
            const suggestions = exception.getResolutionSuggestions();
            expect(suggestions).toContain('Change the resource state to one of: pending, processing');
            expect(suggestions).toContain('Wait for the resource to reach the required state');
        });
        it('should provide suggestions for resource locked with expiration', () => {
            const lockExpiresAt = new Date(Date.now() + 3600000); // 1 hour from now
            const exception = conflict_exception_1.ConflictException.resourceLocked('Doc', 'doc-1', 'user-1', new Date(), 'edit', {
                lockExpiresAt,
            });
            const suggestions = exception.getResolutionSuggestions();
            expect(suggestions).toContain(`Wait until the lock expires at ${lockExpiresAt.toISOString()}`);
            expect(suggestions).toContain('Contact the user who has the lock');
        });
    });
    describe('toApiResponse', () => {
        it('should convert to API response format', () => {
            const exception = conflict_exception_1.ConflictException.duplicateResource('User', 'email', '<EMAIL>');
            const response = exception.toApiResponse();
            expect(response).toEqual({
                error: 'This user already exists',
                code: 'CONFLICT',
                details: {
                    conflictType: 'duplicate_resource',
                    resourceType: 'User',
                    resourceId: undefined,
                    conflictField: 'email',
                    resolutionSuggestions: [
                        'Use a different value for the conflicting field',
                        'Update the existing resource instead of creating a new one',
                    ],
                },
            });
        });
    });
    describe('toJSON', () => {
        it('should convert to JSON with detailed information', () => {
            const exception = conflict_exception_1.ConflictException.versionConflict('Document', 'doc-123', 5, 7);
            const json = exception.toJSON();
            expect(json).toMatchObject({
                name: 'ConflictException',
                code: 'CONFLICT',
                severity: 'medium',
                category: 'conflict',
                conflictType: 'version_conflict',
                resourceType: 'Document',
                resourceId: 'doc-123',
                conflictingValue: 5,
                existingValue: 7,
                conflictField: 'version',
                isDuplicateResource: false,
                isVersionConflict: true,
                isStateConflict: false,
                versionInfo: { expected: 5, actual: 7 },
                stateInfo: null,
                businessRuleInfo: null,
                dependencyInfo: null,
                lockInfo: null,
            });
        });
    });
    describe('inheritance', () => {
        it('should be instance of Error and DomainException', () => {
            const exception = new conflict_exception_1.ConflictException('Test conflict', 'test_conflict');
            expect(exception).toBeInstanceOf(Error);
            expect(exception.name).toBe('ConflictException');
            expect(exception.code).toBe('CONFLICT');
        });
        it('should maintain proper prototype chain', () => {
            const exception = new conflict_exception_1.ConflictException('Test conflict', 'test_conflict');
            expect(exception instanceof conflict_exception_1.ConflictException).toBe(true);
            expect(exception instanceof Error).toBe(true);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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