9273e6869de5f8e98dfe1ab1d1ecb957
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NormalizedEventCreatedDomainEvent = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
const event_severity_enum_1 = require("../enums/event-severity.enum");
const normalized_event_entity_1 = require("../entities/normalized-event.entity");
/**
 * Normalized Event Created Domain Event
 *
 * Raised when a new normalized security event is created in the system.
 * This event triggers various downstream processes including:
 * - Enrichment pipeline initiation
 * - Data quality monitoring
 * - Manual review queue management
 * - Metrics collection
 * - Audit logging
 */
class NormalizedEventCreatedDomainEvent extends shared_kernel_1.BaseDomainEvent {
    constructor(aggregateId, eventData, options) {
        super(aggregateId, eventData, options);
    }
    /**
     * Get the original event ID
     */
    get originalEventId() {
        return this.eventData.originalEventId;
    }
    /**
     * Get the type of the normalized event
     */
    get eventType() {
        return this.eventData.eventType;
    }
    /**
     * Get the severity of the normalized event
     */
    get severity() {
        return this.eventData.severity;
    }
    /**
     * Get the normalization status
     */
    get normalizationStatus() {
        return this.eventData.normalizationStatus;
    }
    /**
     * Get the schema version
     */
    get schemaVersion() {
        return this.eventData.schemaVersion;
    }
    /**
     * Get the data quality score
     */
    get dataQualityScore() {
        return this.eventData.dataQualityScore;
    }
    /**
     * Get the number of applied rules
     */
    get appliedRulesCount() {
        return this.eventData.appliedRulesCount;
    }
    /**
     * Check if the event requires manual review
     */
    get requiresManualReview() {
        return this.eventData.requiresManualReview;
    }
    /**
     * Check if the normalized event is high severity
     */
    isHighSeverity() {
        return [event_severity_enum_1.EventSeverity.HIGH, event_severity_enum_1.EventSeverity.CRITICAL].includes(this.severity);
    }
    /**
     * Check if the normalized event is critical
     */
    isCritical() {
        return this.severity === event_severity_enum_1.EventSeverity.CRITICAL;
    }
    /**
     * Check if the event has high data quality
     */
    hasHighDataQuality() {
        return (this.dataQualityScore || 0) >= 60;
    }
    /**
     * Check if normalization is completed
     */
    isNormalizationCompleted() {
        return this.normalizationStatus === normalized_event_entity_1.NormalizationStatus.COMPLETED;
    }
    /**
     * Get event summary for handlers
     */
    getEventSummary() {
        return {
            normalizedEventId: this.aggregateId.toString(),
            originalEventId: this.originalEventId.toString(),
            eventType: this.eventType,
            severity: this.severity,
            normalizationStatus: this.normalizationStatus,
            schemaVersion: this.schemaVersion,
            dataQualityScore: this.dataQualityScore,
            appliedRulesCount: this.appliedRulesCount,
            requiresManualReview: this.requiresManualReview,
            isHighSeverity: this.isHighSeverity(),
            isCritical: this.isCritical(),
            hasHighDataQuality: this.hasHighDataQuality(),
            isNormalizationCompleted: this.isNormalizationCompleted(),
        };
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            ...super.toJSON(),
            eventSummary: this.getEventSummary(),
        };
    }
}
exports.NormalizedEventCreatedDomainEvent = NormalizedEventCreatedDomainEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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