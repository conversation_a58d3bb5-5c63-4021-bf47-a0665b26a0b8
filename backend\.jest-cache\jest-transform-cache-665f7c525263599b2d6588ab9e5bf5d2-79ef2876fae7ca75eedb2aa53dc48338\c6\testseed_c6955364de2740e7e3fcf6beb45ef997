57dbb94be9ce94f1d60f26baae08b886
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestSeeder = void 0;
const common_1 = require("@nestjs/common");
const bcrypt = __importStar(require("bcrypt"));
/**
 * Test environment database seeder
 * Creates minimal test data for automated testing
 */
class TestSeeder {
    constructor(dataSource) {
        this.dataSource = dataSource;
        this.logger = new common_1.Logger(TestSeeder.name);
    }
    /**
     * Execute all test seeds
     */
    async seed() {
        this.logger.log('Starting test database seeding...');
        try {
            await this.seedTestRoles();
            await this.seedTestUsers();
            await this.seedTestAssets();
            await this.seedTestVulnerabilities();
            this.logger.log('Test database seeding completed successfully');
        }
        catch (error) {
            this.logger.error('Test database seeding failed', {
                error: error.message,
                stack: error.stack,
            });
            throw error;
        }
    }
    /**
     * Seed minimal test roles
     */
    async seedTestRoles() {
        this.logger.debug('Seeding test roles...');
        const roles = [
            {
                name: 'test_admin',
                description: 'Test administrator role',
                permissions: ['*'],
                is_system_role: false,
            },
            {
                name: 'test_user',
                description: 'Test user role',
                permissions: ['read'],
                is_system_role: false,
            },
        ];
        for (const role of roles) {
            await this.dataSource.query(`
        INSERT INTO roles (name, description, permissions, is_system_role)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (name) DO UPDATE SET
          description = EXCLUDED.description,
          permissions = EXCLUDED.permissions,
          is_system_role = EXCLUDED.is_system_role,
          updated_at = CURRENT_TIMESTAMP
      `, [role.name, role.description, JSON.stringify(role.permissions), role.is_system_role]);
        }
        this.logger.debug(`Seeded ${roles.length} test roles`);
    }
    /**
     * Seed minimal test users
     */
    async seedTestUsers() {
        this.logger.debug('Seeding test users...');
        const passwordHash = await bcrypt.hash('test123', 10);
        const users = [
            {
                email: '<EMAIL>',
                password_hash: passwordHash,
                first_name: 'Test',
                last_name: 'Admin',
                role: 'test_admin',
                is_active: true,
                email_verified: true,
            },
            {
                email: '<EMAIL>',
                password_hash: passwordHash,
                first_name: 'Test',
                last_name: 'User',
                role: 'test_user',
                is_active: true,
                email_verified: true,
            },
        ];
        for (const user of users) {
            await this.dataSource.query(`
        INSERT INTO users (email, password_hash, first_name, last_name, role, is_active, email_verified)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        ON CONFLICT (email) DO UPDATE SET
          password_hash = EXCLUDED.password_hash,
          first_name = EXCLUDED.first_name,
          last_name = EXCLUDED.last_name,
          role = EXCLUDED.role,
          is_active = EXCLUDED.is_active,
          email_verified = EXCLUDED.email_verified,
          updated_at = CURRENT_TIMESTAMP
      `, [user.email, user.password_hash, user.first_name, user.last_name, user.role, user.is_active, user.email_verified]);
        }
        this.logger.debug(`Seeded ${users.length} test users`);
    }
    /**
     * Seed minimal test assets
     */
    async seedTestAssets() {
        this.logger.debug('Seeding test assets...');
        const assets = [
            {
                name: 'Test Server',
                type: 'SERVER',
                ip_address: '********',
                hostname: 'test-server',
                operating_system: 'Linux',
                os_version: '20.04',
                location: 'Test Lab',
                owner: 'Test Team',
                criticality: 'MEDIUM',
                status: 'ACTIVE',
                metadata: { environment: 'test' }
            },
            {
                name: 'Test Workstation',
                type: 'WORKSTATION',
                ip_address: '********',
                hostname: 'test-ws',
                operating_system: 'Windows',
                os_version: '10',
                location: 'Test Lab',
                owner: 'Test Team',
                criticality: 'LOW',
                status: 'ACTIVE',
                metadata: { environment: 'test' }
            },
        ];
        for (const asset of assets) {
            await this.dataSource.query(`
        INSERT INTO assets (name, type, ip_address, hostname, operating_system, os_version, location, owner, criticality, status, metadata)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        ON CONFLICT (name) DO UPDATE SET
          type = EXCLUDED.type,
          ip_address = EXCLUDED.ip_address,
          hostname = EXCLUDED.hostname,
          operating_system = EXCLUDED.operating_system,
          os_version = EXCLUDED.os_version,
          location = EXCLUDED.location,
          owner = EXCLUDED.owner,
          criticality = EXCLUDED.criticality,
          status = EXCLUDED.status,
          metadata = EXCLUDED.metadata,
          updated_at = CURRENT_TIMESTAMP
      `, [
                asset.name, asset.type, asset.ip_address, asset.hostname,
                asset.operating_system, asset.os_version, asset.location, asset.owner,
                asset.criticality, asset.status, JSON.stringify(asset.metadata)
            ]);
        }
        this.logger.debug(`Seeded ${assets.length} test assets`);
    }
    /**
     * Seed minimal test vulnerabilities
     */
    async seedTestVulnerabilities() {
        this.logger.debug('Seeding test vulnerabilities...');
        const vulnerabilities = [
            {
                cve_id: 'CVE-2023-TEST-001',
                title: 'Test Vulnerability High',
                description: 'Test vulnerability for high severity testing',
                severity: 'HIGH',
                cvss_score: 8.0,
                cvss_vector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:N',
                cwe_id: 'CWE-79',
                affected_software: [{ name: 'TestApp', versions: ['1.0.0'] }],
                references: [{ url: 'https://example.com/test', type: 'test' }],
                exploit_available: false,
                patch_available: true,
                published_date: new Date('2023-01-01'),
                modified_date: new Date('2023-01-01')
            },
            {
                cve_id: 'CVE-2023-TEST-002',
                title: 'Test Vulnerability Medium',
                description: 'Test vulnerability for medium severity testing',
                severity: 'MEDIUM',
                cvss_score: 5.0,
                cvss_vector: 'CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:N',
                cwe_id: 'CWE-89',
                affected_software: [{ name: 'TestApp', versions: ['2.0.0'] }],
                references: [{ url: 'https://example.com/test2', type: 'test' }],
                exploit_available: false,
                patch_available: false,
                published_date: new Date('2023-02-01'),
                modified_date: new Date('2023-02-01')
            },
        ];
        for (const vuln of vulnerabilities) {
            await this.dataSource.query(`
        INSERT INTO vulnerabilities (cve_id, title, description, severity, cvss_score, cvss_vector, cwe_id, affected_software, references, exploit_available, patch_available, published_date, modified_date)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
        ON CONFLICT (cve_id) DO UPDATE SET
          title = EXCLUDED.title,
          description = EXCLUDED.description,
          severity = EXCLUDED.severity,
          cvss_score = EXCLUDED.cvss_score,
          cvss_vector = EXCLUDED.cvss_vector,
          cwe_id = EXCLUDED.cwe_id,
          affected_software = EXCLUDED.affected_software,
          references = EXCLUDED.references,
          exploit_available = EXCLUDED.exploit_available,
          patch_available = EXCLUDED.patch_available,
          published_date = EXCLUDED.published_date,
          modified_date = EXCLUDED.modified_date,
          updated_at = CURRENT_TIMESTAMP
      `, [
                vuln.cve_id, vuln.title, vuln.description, vuln.severity,
                vuln.cvss_score, vuln.cvss_vector, vuln.cwe_id,
                JSON.stringify(vuln.affected_software), JSON.stringify(vuln.references),
                vuln.exploit_available, vuln.patch_available,
                vuln.published_date, vuln.modified_date
            ]);
        }
        this.logger.debug(`Seeded ${vulnerabilities.length} test vulnerabilities`);
    }
    /**
     * Clean up test data
     */
    async cleanup() {
        this.logger.log('Cleaning up test database...');
        try {
            // Clean up in reverse dependency order
            await this.dataSource.query('DELETE FROM asset_vulnerabilities WHERE 1=1');
            await this.dataSource.query('DELETE FROM security_event_attachments WHERE 1=1');
            await this.dataSource.query('DELETE FROM security_event_comments WHERE 1=1');
            await this.dataSource.query('DELETE FROM security_event_tag_assignments WHERE 1=1');
            await this.dataSource.query('DELETE FROM security_events WHERE 1=1');
            await this.dataSource.query('DELETE FROM security_event_tags WHERE 1=1');
            await this.dataSource.query('DELETE FROM vulnerability_scans WHERE 1=1');
            await this.dataSource.query('DELETE FROM vulnerabilities WHERE cve_id LIKE \'CVE-2023-TEST-%\'');
            await this.dataSource.query('DELETE FROM assets WHERE metadata->>\'environment\' = \'test\'');
            await this.dataSource.query('DELETE FROM refresh_tokens WHERE 1=1');
            await this.dataSource.query('DELETE FROM user_roles WHERE 1=1');
            await this.dataSource.query('DELETE FROM users WHERE email LIKE \'%@example.com\'');
            await this.dataSource.query('DELETE FROM roles WHERE name LIKE \'test_%\'');
            await this.dataSource.query('DELETE FROM audit_logs WHERE 1=1');
            this.logger.log('Test database cleanup completed successfully');
        }
        catch (error) {
            this.logger.error('Test database cleanup failed', {
                error: error.message,
                stack: error.stack,
            });
            throw error;
        }
    }
}
exports.TestSeeder = TestSeeder;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************