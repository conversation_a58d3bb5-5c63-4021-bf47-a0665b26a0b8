{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\logging.config.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,iDAAmC;AAGnC,iDAAiD;AACjD,IAAY,QAKX;AALD,WAAY,QAAQ;IAClB,2BAAe,CAAA;IACf,yBAAa,CAAA;IACb,yBAAa,CAAA;IACb,2BAAe,CAAA;AACjB,CAAC,EALW,QAAQ,wBAAR,QAAQ,QAKnB;AAED,IAAY,SAIX;AAJD,WAAY,SAAS;IACnB,0BAAa,CAAA;IACb,8BAAiB,CAAA;IACjB,kCAAqB,CAAA;AACvB,CAAC,EAJW,SAAS,yBAAT,SAAS,QAIpB;AAED,IAAY,aAMX;AAND,WAAY,aAAa;IACvB,sCAAqB,CAAA;IACrB,kCAAiB,CAAA;IACjB,4BAAW,CAAA;IACX,gCAAe,CAAA;IACf,8BAAa,CAAA;AACf,CAAC,EANW,aAAa,6BAAb,aAAa,QAMxB;AAED,IAAY,eAIX;AAJD,WAAY,eAAe;IACzB,kDAA+B,CAAA;IAC/B,oCAAiB,CAAA;IACjB,sCAAmB,CAAA;AACrB,CAAC,EAJW,eAAe,+BAAf,eAAe,QAI1B;AAuGD,sDAAsD;AACtD,MAAM,YAAY,GAAG,CAAC,KAAyB,EAAE,YAAqB,EAAW,EAAE,CACjF,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC;AAExD,MAAM,YAAY,GAAG,CAAC,KAAyB,EAAE,YAAoB,EAAU,EAAE,CAC/E,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC;AAE7D,MAAM,SAAS,GAAG,CAChB,KAAyB,EACzB,UAAa,EACb,YAAwB,EACZ,EAAE;IACd,IAAI,CAAC,KAAK;QAAE,OAAO,YAAY,CAAC;IAChC,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;IAC7C,OAAO,UAAU,CAAC,QAAQ,CAAC,KAAmB,CAAC,CAAC,CAAC,CAAE,KAAoB,CAAC,CAAC,CAAC,YAAY,CAAC;AACzF,CAAC,CAAC;AAEF,MAAM,gBAAgB,GAAG,CAAC,KAAyB,EAAE,YAAsB,EAAY,EAAE,CACvF,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,YAAY,CAAC;AAE7D,+BAA+B;AAC/B,MAAM,SAAS,GAAG,CAAC,GAAW,EAAsB,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxE,MAAM,YAAY,GAAG,GAAY,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,YAAY,CAAC;AAE3E,oDAAoD;AACpD,MAAM,sBAAsB,GAAG,CAAC,cAAuB,EAA0D,EAAE;IACjH,IAAI,CAAC,cAAc;QAAE,OAAO,SAAS,CAAC;IACtC,OAAO,CAAC,IAAa,EAAE,GAAa,EAAW,EAAE,CAAC,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC;AACzE,CAAC,CAAC;AAEF,2BAA2B;AAC3B,MAAM,0BAA0B,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;AAC3F,MAAM,qBAAqB,GAAG,KAAK,CAAC;AACpC,MAAM,sBAAsB,GAAG,CAAC,CAAC;AACjC,MAAM,sBAAsB,GAAG,KAAK,CAAC;AACrC,MAAM,uBAAuB,GAAG,EAAE,CAAC;AACnC,MAAM,4BAA4B,GAAG,KAAK,CAAC;AAC3C,MAAM,6BAA6B,GAAG,CAAC,CAAC;AACxC,MAAM,6BAA6B,GAAG,IAAI,CAAC;AAC3C,MAAM,4BAA4B,GAAG,IAAI,CAAC;AAC1C,MAAM,0BAA0B,GAAG,kBAAkB,CAAC;AACtD,MAAM,yBAAyB,GAAG,eAAe,CAAC;AAClD,MAAM,8BAA8B,GAAG,GAAG,CAAC;AAC3C,MAAM,kCAAkC,GAAG,IAAI,CAAC;AAChD,MAAM,2BAA2B,GAAG,YAAY,CAAC;AACjD,MAAM,sBAAsB,GAAG,EAAE,CAAC;AAClC,MAAM,wBAAwB,GAAG,cAAc,CAAC;AAEhD;;;GAGG;AACU,QAAA,aAAa,GAAG,IAAA,mBAAU,EAAC,SAAS,EAAE,GAAkB,EAAE;IACrE,MAAM,MAAM,GAAG,YAAY,EAAE,CAAC;IAC9B,MAAM,YAAY,GAAG,SAAS,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;IAEhF,OAAO;QACL,gBAAgB;QAChB,KAAK,EAAE,YAAY;QACnB,MAAM,EAAE,SAAS,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,SAAS,EAAE,SAAS,CAAC,IAAI,CAAC;QACrE,MAAM,EAAE,YAAY,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,KAAK,CAAC;QAEpD,kBAAkB;QAClB,OAAO,EAAE;YACP,OAAO,EAAE,YAAY,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAAE,IAAI,CAAC;YAC7D,KAAK,EAAE,SAAS,CAAC,SAAS,CAAC,mBAAmB,CAAC,EAAE,QAAQ,EAAE,YAAY,CAAC;YACxE,QAAQ,EAAE,CAAC,MAAM;YACjB,SAAS,EAAE,IAAI;YACf,WAAW,EAAE,CAAC,MAAM;SACrB;QAED,eAAe;QACf,IAAI,EAAE;YACJ,OAAO,EAAE,YAAY,CAAC,SAAS,CAAC,kBAAkB,CAAC,EAAE,KAAK,CAAC;YAC3D,KAAK,EAAE,SAAS,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC;YACtE,QAAQ,EAAE,SAAS,CAAC,eAAe,CAAC,IAAI,cAAc;YACtD,OAAO,EAAE,SAAS,CAAC,mBAAmB,CAAC,IAAI,qBAAqB;YAChE,QAAQ,EAAE,YAAY,CAAC,SAAS,CAAC,oBAAoB,CAAC,EAAE,sBAAsB,CAAC;YAC/E,QAAQ,EAAE,IAAI;YACd,aAAa,EAAE,IAAI;SACpB;QAED,qBAAqB;QACrB,SAAS,EAAE;YACT,OAAO,EAAE,YAAY,CAAC,SAAS,CAAC,wBAAwB,CAAC,EAAE,KAAK,CAAC;YACjE,KAAK,EAAE,QAAQ,CAAC,KAAK;YACrB,QAAQ,EAAE,SAAS,CAAC,qBAAqB,CAAC,IAAI,gBAAgB;YAC9D,OAAO,EAAE,SAAS,CAAC,yBAAyB,CAAC,IAAI,qBAAqB;YACtE,QAAQ,EAAE,YAAY,CAAC,SAAS,CAAC,0BAA0B,CAAC,EAAE,sBAAsB,CAAC;YACrF,QAAQ,EAAE,IAAI;YACd,aAAa,EAAE,IAAI;SACpB;QAED,gBAAgB;QAChB,KAAK,EAAE;YACL,OAAO,EAAE,YAAY,CAAC,SAAS,CAAC,mBAAmB,CAAC,EAAE,KAAK,CAAC;YAC5D,KAAK,EAAE,QAAQ,CAAC,IAAI;YACpB,QAAQ,EAAE,SAAS,CAAC,gBAAgB,CAAC,IAAI,gBAAgB;YACzD,OAAO,EAAE,SAAS,CAAC,oBAAoB,CAAC,IAAI,sBAAsB;YAClE,QAAQ,EAAE,YAAY,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAAE,uBAAuB,CAAC;YACjF,QAAQ,EAAE,IAAI;YACd,aAAa,EAAE,IAAI;YACnB,MAAM,EAAE,SAAS,CAAC,IAAI;SACvB;QAED,sBAAsB;QACtB,WAAW,EAAE;YACX,OAAO,EAAE,YAAY,CAAC,SAAS,CAAC,yBAAyB,CAAC,EAAE,KAAK,CAAC;YAClE,KAAK,EAAE,QAAQ,CAAC,KAAK;YACrB,QAAQ,EAAE,SAAS,CAAC,sBAAsB,CAAC,IAAI,sBAAsB;YACrE,OAAO,EAAE,SAAS,CAAC,0BAA0B,CAAC,IAAI,4BAA4B;YAC9E,QAAQ,EAAE,YAAY,CAAC,SAAS,CAAC,2BAA2B,CAAC,EAAE,6BAA6B,CAAC;YAC7F,SAAS,EAAE,YAAY,CAAC,SAAS,CAAC,2BAA2B,CAAC,EAAE,6BAA6B,CAAC;SAC/F;QAED,8CAA8C;QAC9C,IAAI,EAAE;YACJ,OAAO,EAAE,YAAY,CAAC,SAAS,CAAC,kBAAkB,CAAC,EAAE,KAAK,CAAC;YAC3D,KAAK,EAAE,SAAS,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC;YACtE,MAAM,EAAE,SAAS,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,aAAa,EAAE,aAAa,CAAC,QAAQ,CAAC;YACtF,GAAG,CAAC,YAAY,CAAC,SAAS,CAAC,0BAA0B,CAAC,EAAE,KAAK,CAAC,IAAI;gBAChE,IAAI,EAAE,sBAAsB,CAAC,IAAI,CAAE;aACpC,CAAC;SACH;QAED,yBAAyB;QACzB,QAAQ,EAAE;YACR,OAAO,EAAE,YAAY,CAAC,SAAS,CAAC,sBAAsB,CAAC,EAAE,KAAK,CAAC;YAC/D,KAAK,EAAE,QAAQ,CAAC,KAAK;YACrB,UAAU,EAAE,YAAY,CAAC,SAAS,CAAC,sBAAsB,CAAC,EAAE,KAAK,CAAC;YAClE,SAAS,EAAE,YAAY,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAAE,IAAI,CAAC;YAC/D,cAAc,EAAE,YAAY,CAAC,SAAS,CAAC,2BAA2B,CAAC,EAAE,KAAK,CAAC;YAC3E,kBAAkB,EAAE,YAAY,CAAC,SAAS,CAAC,6BAA6B,CAAC,EAAE,4BAA4B,CAAC;SACzG;QAED,2BAA2B;QAC3B,QAAQ,EAAE;YACR,OAAO,EAAE,YAAY,CAAC,SAAS,CAAC,sBAAsB,CAAC,EAAE,KAAK,CAAC;YAC/D,KAAK,EAAE,QAAQ,CAAC,IAAI;YACpB,WAAW,EAAE,YAAY,CAAC,SAAS,CAAC,uBAAuB,CAAC,EAAE,KAAK,CAAC;YACpE,YAAY,EAAE,YAAY,CAAC,SAAS,CAAC,wBAAwB,CAAC,EAAE,KAAK,CAAC;YACtE,SAAS,EAAE,YAAY,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAAE,IAAI,CAAC;YAC/D,iBAAiB,EAAE,YAAY,CAAC,SAAS,CAAC,oBAAoB,CAAC,EAAE,IAAI,CAAC;SACvE;QAED,0BAA0B;QAC1B,WAAW,EAAE;YACX,OAAO,EAAE,YAAY,CAAC,SAAS,CAAC,yBAAyB,CAAC,EAAE,IAAI,CAAC;YACjE,UAAU,EAAE,SAAS,CAAC,wBAAwB,CAAC,IAAI,0BAA0B;YAC7E,iBAAiB,EAAE,YAAY,CAAC,SAAS,CAAC,0BAA0B,CAAC,EAAE,IAAI,CAAC;SAC7E;QAED,kBAAkB;QAClB,WAAW,EAAE;YACX,OAAO,EAAE,YAAY,CAAC,SAAS,CAAC,yBAAyB,CAAC,EAAE,KAAK,CAAC;YAClE,IAAI,EAAE,SAAS,CAAC,SAAS,CAAC,sBAAsB,CAAC,EAAE,eAAe,EAAE,eAAe,CAAC,aAAa,CAAC;YAClG,GAAG,CAAC,SAAS,CAAC,0BAA0B,CAAC,IAAI,EAAE,QAAQ,EAAE,SAAS,CAAC,0BAA0B,CAAE,EAAE,CAAC;YAClG,GAAG,CAAC,SAAS,CAAC,yBAAyB,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,CAAC,yBAAyB,CAAE,EAAE,CAAC;YAC9F,KAAK,EAAE,SAAS,CAAC,uBAAuB,CAAC,IAAI,yBAAyB;YACtE,SAAS,EAAE,YAAY,CAAC,SAAS,CAAC,4BAA4B,CAAC,EAAE,8BAA8B,CAAC;YAChG,aAAa,EAAE,YAAY,CAAC,SAAS,CAAC,gCAAgC,CAAC,EAAE,kCAAkC,CAAC;SAC7G;QAED,yBAAyB;QACzB,OAAO,EAAE;YACP,OAAO,EAAE,YAAY,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAAE,IAAI,CAAC;YAC7D,MAAM,EAAE,gBAAgB,CAAC,SAAS,CAAC,oBAAoB,CAAC,EAAE,0BAA0B,CAAC;YACrF,WAAW,EAAE,SAAS,CAAC,yBAAyB,CAAC,IAAI,2BAA2B;YAChF,cAAc,EAAE,YAAY,CAAC,SAAS,CAAC,6BAA6B,CAAC,EAAE,KAAK,CAAC;SAC9E;QAED,gBAAgB;QAChB,SAAS,EAAE;YACT,OAAO,EAAE,YAAY,CAAC,SAAS,CAAC,uBAAuB,CAAC,EAAE,KAAK,CAAC;YAChE,IAAI,EAAE,YAAY,CAAC,SAAS,CAAC,oBAAoB,CAAC,EAAE,sBAAsB,CAAC;YAC3E,kBAAkB,EAAE,YAAY,CAAC,SAAS,CAAC,2BAA2B,CAAC,EAAE,IAAI,CAAC;YAC9E,eAAe,EAAE,SAAS,CAAC,gCAAgC,CAAC,IAAI,wBAAwB;SACzF;KACF,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH;;GAEG;AACI,MAAM,mBAAmB,GAAG,CAAC,MAAqB,EAA0B,EAAE;IACnF,MAAM,OAAO,GAA6B;QACxC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE;QAC1B,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;KACvC,CAAC;IAEF,4BAA4B;IAC5B,IAAI,MAAM,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;QAC/B,OAAO,CAAC,IAAI,CACV,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE;YACtB,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC;YAC5C,OAAO,aAAa,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAC3D,CAAC,CAAC,EAAE,CACL,CAAC;IACJ,CAAC;IAED,0BAA0B;IAC1B,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QAC3B,OAAO,CAAC,IAAI,CACV,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE;YACtB,MAAM,MAAM,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;YAC3B,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBACtC,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;oBAClB,MAAM,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC7C,CAAC;YACH,CAAC,CAAC,CAAC;YACH,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC,EAAE,CACL,CAAC;IACJ,CAAC;IAED,0BAA0B;IAC1B,OAAO,CAAC,IAAI,CACV,MAAM,CAAC,MAAM,KAAK,SAAS,CAAC,IAAI;QAC9B,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE;QACvB,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CAC5B,CAAC;IAEF,OAAO,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAC5C,CAAC,CAAC;AAvCW,QAAA,mBAAmB,uBAuC9B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\logging.config.ts"], "sourcesContent": ["import { registerAs } from '@nestjs/config';\r\nimport * as winston from 'winston';\r\nimport { Request, Response } from 'express';\r\n\r\n// Enums for better type safety and configuration\r\nexport enum LogLevel {\r\n  ERROR = 'error',\r\n  WARN = 'warn',\r\n  INFO = 'info',\r\n  DEBUG = 'debug',\r\n}\r\n\r\nexport enum LogFormat {\r\n  JSON = 'json',\r\n  SIMPLE = 'simple',\r\n  COMBINED = 'combined',\r\n}\r\n\r\nexport enum HttpLogFormat {\r\n  COMBINED = 'combined',\r\n  COMMON = 'common',\r\n  DEV = 'dev',\r\n  SHORT = 'short',\r\n  TINY = 'tiny',\r\n}\r\n\r\nexport enum AggregationType {\r\n  ELASTICSEARCH = 'elasticsearch',\r\n  SPLUNK = 'splunk',\r\n  DATADOG = 'datadog',\r\n}\r\n\r\n// Base configuration interfaces\r\ninterface BaseLogConfig {\r\n  enabled: boolean;\r\n  level: LogLevel;\r\n}\r\n\r\ninterface FileLogConfigBase extends BaseLogConfig {\r\n  filename: string;\r\n  maxSize: string;\r\n  maxFiles: number;\r\n  tailable: boolean;\r\n  zippedArchive: boolean;\r\n}\r\n\r\n// Comprehensive type definitions\r\nexport interface ConsoleLogConfig extends BaseLogConfig {\r\n  colorize: boolean;\r\n  timestamp: boolean;\r\n  prettyPrint: boolean;\r\n}\r\n\r\nexport interface FileLogConfig extends FileLogConfigBase {}\r\n\r\nexport interface AuditLogConfig extends Omit<FileLogConfigBase, 'level'> {\r\n  level: LogLevel.INFO;\r\n  format: LogFormat.JSON;\r\n}\r\n\r\nexport interface PerformanceLogConfig extends Omit<FileLogConfigBase, 'level' | 'tailable' | 'zippedArchive'> {\r\n  level: LogLevel.DEBUG;\r\n  threshold: number;\r\n}\r\n\r\nexport interface HttpLogConfig extends BaseLogConfig {\r\n  format: HttpLogFormat;\r\n  skip?: (req: Request, res: Response) => boolean;\r\n}\r\n\r\nexport interface DatabaseLogConfig extends Omit<BaseLogConfig, 'level'> {\r\n  level: LogLevel.DEBUG;\r\n  logQueries: boolean;\r\n  logErrors: boolean;\r\n  logSlowQueries: boolean;\r\n  slowQueryThreshold: number;\r\n}\r\n\r\nexport interface ExternalLogConfig extends BaseLogConfig {\r\n  logRequests: boolean;\r\n  logResponses: boolean;\r\n  logErrors: boolean;\r\n  maskSensitiveData: boolean;\r\n}\r\n\r\nexport interface CorrelationConfig {\r\n  enabled: boolean;\r\n  headerName: string;\r\n  generateIfMissing: boolean;\r\n}\r\n\r\nexport interface AggregationConfig {\r\n  enabled: boolean;\r\n  type: AggregationType;\r\n  endpoint?: string;\r\n  apiKey?: string;\r\n  index: string;\r\n  batchSize: number;\r\n  flushInterval: number;\r\n}\r\n\r\nexport interface MaskingConfig {\r\n  enabled: boolean;\r\n  fields: string[];\r\n  replacement: string;\r\n  preserveLength: boolean;\r\n}\r\n\r\nexport interface RetentionConfig {\r\n  enabled: boolean;\r\n  days: number;\r\n  compressionEnabled: boolean;\r\n  archiveLocation: string;\r\n}\r\n\r\nexport interface LoggingConfig {\r\n  level: LogLevel;\r\n  format: LogFormat;\r\n  silent: boolean;\r\n  console: ConsoleLogConfig;\r\n  file: FileLogConfig;\r\n  errorFile: FileLogConfig;\r\n  audit: AuditLogConfig;\r\n  performance: PerformanceLogConfig;\r\n  http: HttpLogConfig;\r\n  database: DatabaseLogConfig;\r\n  external: ExternalLogConfig;\r\n  correlation: CorrelationConfig;\r\n  aggregation: AggregationConfig;\r\n  masking: MaskingConfig;\r\n  retention: RetentionConfig;\r\n}\r\n\r\n// Utility functions for cleaner configuration parsing\r\nconst parseBoolean = (value: string | undefined, defaultValue: boolean): boolean =>\r\n  value !== undefined ? value === 'true' : defaultValue;\r\n\r\nconst parseInteger = (value: string | undefined, defaultValue: number): number =>\r\n  value ? parseInt(value, 10) || defaultValue : defaultValue;\r\n\r\nconst parseEnum = <T extends Record<string, string>>(\r\n  value: string | undefined,\r\n  enumObject: T,\r\n  defaultValue: T[keyof T]\r\n): T[keyof T] => {\r\n  if (!value) return defaultValue;\r\n  const enumValues = Object.values(enumObject);\r\n  return enumValues.includes(value as T[keyof T]) ? (value as T[keyof T]) : defaultValue;\r\n};\r\n\r\nconst parseStringArray = (value: string | undefined, defaultValue: string[]): string[] =>\r\n  value?.split(',').map(item => item.trim()) ?? defaultValue;\r\n\r\n// Environment variable helpers\r\nconst getEnvVar = (key: string): string | undefined => process.env[key];\r\nconst isProduction = (): boolean => getEnvVar('NODE_ENV') === 'production';\r\n\r\n// Skip function for HTTP logging - fixed type issue\r\nconst createHttpSkipFunction = (skipSuccessful: boolean): ((req: Request, res: Response) => boolean) | undefined => {\r\n  if (!skipSuccessful) return undefined;\r\n  return (_req: Request, res: Response): boolean => res.statusCode < 400;\r\n};\r\n\r\n// Default values constants\r\nconst DEFAULT_LOG_MASKING_FIELDS = ['password', 'token', 'secret', 'key', 'authorization'];\r\nconst DEFAULT_FILE_MAX_SIZE = '10m';\r\nconst DEFAULT_FILE_MAX_FILES = 5;\r\nconst DEFAULT_AUDIT_MAX_SIZE = '50m';\r\nconst DEFAULT_AUDIT_MAX_FILES = 10;\r\nconst DEFAULT_PERFORMANCE_MAX_SIZE = '20m';\r\nconst DEFAULT_PERFORMANCE_MAX_FILES = 3;\r\nconst DEFAULT_PERFORMANCE_THRESHOLD = 1000;\r\nconst DEFAULT_SLOW_QUERY_THRESHOLD = 1000;\r\nconst DEFAULT_CORRELATION_HEADER = 'x-correlation-id';\r\nconst DEFAULT_AGGREGATION_INDEX = 'sentinel-logs';\r\nconst DEFAULT_AGGREGATION_BATCH_SIZE = 100;\r\nconst DEFAULT_AGGREGATION_FLUSH_INTERVAL = 5000;\r\nconst DEFAULT_MASKING_REPLACEMENT = '[REDACTED]';\r\nconst DEFAULT_RETENTION_DAYS = 30;\r\nconst DEFAULT_ARCHIVE_LOCATION = 'logs/archive';\r\n\r\n/**\r\n * Optimized logging configuration with comprehensive type safety\r\n * Provides structured logging setup for development and production environments\r\n */\r\nexport const loggingConfig = registerAs('logging', (): LoggingConfig => {\r\n  const isProd = isProduction();\r\n  const defaultLevel = parseEnum(getEnvVar('LOG_LEVEL'), LogLevel, LogLevel.INFO);\r\n\r\n  return {\r\n    // Core settings\r\n    level: defaultLevel,\r\n    format: parseEnum(getEnvVar('LOG_FORMAT'), LogFormat, LogFormat.JSON),\r\n    silent: parseBoolean(getEnvVar('LOG_SILENT'), false),\r\n\r\n    // Console logging\r\n    console: {\r\n      enabled: parseBoolean(getEnvVar('LOG_CONSOLE_ENABLED'), true),\r\n      level: parseEnum(getEnvVar('LOG_CONSOLE_LEVEL'), LogLevel, defaultLevel),\r\n      colorize: !isProd,\r\n      timestamp: true,\r\n      prettyPrint: !isProd,\r\n    },\r\n\r\n    // File logging\r\n    file: {\r\n      enabled: parseBoolean(getEnvVar('LOG_FILE_ENABLED'), false),\r\n      level: parseEnum(getEnvVar('LOG_FILE_LEVEL'), LogLevel, LogLevel.INFO),\r\n      filename: getEnvVar('LOG_FILE_PATH') || 'logs/app.log',\r\n      maxSize: getEnvVar('LOG_FILE_MAX_SIZE') || DEFAULT_FILE_MAX_SIZE,\r\n      maxFiles: parseInteger(getEnvVar('LOG_FILE_MAX_FILES'), DEFAULT_FILE_MAX_FILES),\r\n      tailable: true,\r\n      zippedArchive: true,\r\n    },\r\n\r\n    // Error file logging\r\n    errorFile: {\r\n      enabled: parseBoolean(getEnvVar('LOG_ERROR_FILE_ENABLED'), false),\r\n      level: LogLevel.ERROR,\r\n      filename: getEnvVar('LOG_ERROR_FILE_PATH') || 'logs/error.log',\r\n      maxSize: getEnvVar('LOG_ERROR_FILE_MAX_SIZE') || DEFAULT_FILE_MAX_SIZE,\r\n      maxFiles: parseInteger(getEnvVar('LOG_ERROR_FILE_MAX_FILES'), DEFAULT_FILE_MAX_FILES),\r\n      tailable: true,\r\n      zippedArchive: true,\r\n    },\r\n\r\n    // Audit logging\r\n    audit: {\r\n      enabled: parseBoolean(getEnvVar('LOG_AUDIT_ENABLED'), false),\r\n      level: LogLevel.INFO,\r\n      filename: getEnvVar('LOG_AUDIT_PATH') || 'logs/audit.log',\r\n      maxSize: getEnvVar('LOG_AUDIT_MAX_SIZE') || DEFAULT_AUDIT_MAX_SIZE,\r\n      maxFiles: parseInteger(getEnvVar('LOG_AUDIT_MAX_FILES'), DEFAULT_AUDIT_MAX_FILES),\r\n      tailable: true,\r\n      zippedArchive: true,\r\n      format: LogFormat.JSON,\r\n    },\r\n\r\n    // Performance logging\r\n    performance: {\r\n      enabled: parseBoolean(getEnvVar('LOG_PERFORMANCE_ENABLED'), false),\r\n      level: LogLevel.DEBUG,\r\n      filename: getEnvVar('LOG_PERFORMANCE_PATH') || 'logs/performance.log',\r\n      maxSize: getEnvVar('LOG_PERFORMANCE_MAX_SIZE') || DEFAULT_PERFORMANCE_MAX_SIZE,\r\n      maxFiles: parseInteger(getEnvVar('LOG_PERFORMANCE_MAX_FILES'), DEFAULT_PERFORMANCE_MAX_FILES),\r\n      threshold: parseInteger(getEnvVar('LOG_PERFORMANCE_THRESHOLD'), DEFAULT_PERFORMANCE_THRESHOLD),\r\n    },\r\n\r\n    // HTTP request logging - Fixed the type issue\r\n    http: {\r\n      enabled: parseBoolean(getEnvVar('LOG_HTTP_ENABLED'), false),\r\n      level: parseEnum(getEnvVar('LOG_HTTP_LEVEL'), LogLevel, LogLevel.INFO),\r\n      format: parseEnum(getEnvVar('LOG_HTTP_FORMAT'), HttpLogFormat, HttpLogFormat.COMBINED),\r\n      ...(parseBoolean(getEnvVar('LOG_HTTP_SKIP_SUCCESSFUL'), false) && {\r\n        skip: createHttpSkipFunction(true)!\r\n      }),\r\n    },\r\n\r\n    // Database query logging\r\n    database: {\r\n      enabled: parseBoolean(getEnvVar('LOG_DATABASE_ENABLED'), false),\r\n      level: LogLevel.DEBUG,\r\n      logQueries: parseBoolean(getEnvVar('LOG_DATABASE_QUERIES'), false),\r\n      logErrors: parseBoolean(getEnvVar('LOG_DATABASE_ERRORS'), true),\r\n      logSlowQueries: parseBoolean(getEnvVar('LOG_DATABASE_SLOW_QUERIES'), false),\r\n      slowQueryThreshold: parseInteger(getEnvVar('LOG_DATABASE_SLOW_THRESHOLD'), DEFAULT_SLOW_QUERY_THRESHOLD),\r\n    },\r\n\r\n    // External service logging\r\n    external: {\r\n      enabled: parseBoolean(getEnvVar('LOG_EXTERNAL_ENABLED'), false),\r\n      level: LogLevel.INFO,\r\n      logRequests: parseBoolean(getEnvVar('LOG_EXTERNAL_REQUESTS'), false),\r\n      logResponses: parseBoolean(getEnvVar('LOG_EXTERNAL_RESPONSES'), false),\r\n      logErrors: parseBoolean(getEnvVar('LOG_EXTERNAL_ERRORS'), true),\r\n      maskSensitiveData: parseBoolean(getEnvVar('LOG_MASK_SENSITIVE'), true),\r\n    },\r\n\r\n    // Correlation ID settings\r\n    correlation: {\r\n      enabled: parseBoolean(getEnvVar('LOG_CORRELATION_ENABLED'), true),\r\n      headerName: getEnvVar('LOG_CORRELATION_HEADER') || DEFAULT_CORRELATION_HEADER,\r\n      generateIfMissing: parseBoolean(getEnvVar('LOG_CORRELATION_GENERATE'), true),\r\n    },\r\n\r\n    // Log aggregation\r\n    aggregation: {\r\n      enabled: parseBoolean(getEnvVar('LOG_AGGREGATION_ENABLED'), false),\r\n      type: parseEnum(getEnvVar('LOG_AGGREGATION_TYPE'), AggregationType, AggregationType.ELASTICSEARCH),\r\n      ...(getEnvVar('LOG_AGGREGATION_ENDPOINT') && { endpoint: getEnvVar('LOG_AGGREGATION_ENDPOINT')! }),\r\n      ...(getEnvVar('LOG_AGGREGATION_API_KEY') && { apiKey: getEnvVar('LOG_AGGREGATION_API_KEY')! }),\r\n      index: getEnvVar('LOG_AGGREGATION_INDEX') || DEFAULT_AGGREGATION_INDEX,\r\n      batchSize: parseInteger(getEnvVar('LOG_AGGREGATION_BATCH_SIZE'), DEFAULT_AGGREGATION_BATCH_SIZE),\r\n      flushInterval: parseInteger(getEnvVar('LOG_AGGREGATION_FLUSH_INTERVAL'), DEFAULT_AGGREGATION_FLUSH_INTERVAL),\r\n    },\r\n\r\n    // Sensitive data masking\r\n    masking: {\r\n      enabled: parseBoolean(getEnvVar('LOG_MASKING_ENABLED'), true),\r\n      fields: parseStringArray(getEnvVar('LOG_MASKING_FIELDS'), DEFAULT_LOG_MASKING_FIELDS),\r\n      replacement: getEnvVar('LOG_MASKING_REPLACEMENT') || DEFAULT_MASKING_REPLACEMENT,\r\n      preserveLength: parseBoolean(getEnvVar('LOG_MASKING_PRESERVE_LENGTH'), false),\r\n    },\r\n\r\n    // Log retention\r\n    retention: {\r\n      enabled: parseBoolean(getEnvVar('LOG_RETENTION_ENABLED'), false),\r\n      days: parseInteger(getEnvVar('LOG_RETENTION_DAYS'), DEFAULT_RETENTION_DAYS),\r\n      compressionEnabled: parseBoolean(getEnvVar('LOG_RETENTION_COMPRESSION'), true),\r\n      archiveLocation: getEnvVar('LOG_RETENTION_ARCHIVE_LOCATION') || DEFAULT_ARCHIVE_LOCATION,\r\n    },\r\n  };\r\n});\r\n\r\n/**\r\n * Creates Winston format configuration with type safety\r\n */\r\nexport const createWinstonFormat = (config: LoggingConfig): winston.Logform.Format => {\r\n  const formats: winston.Logform.Format[] = [\r\n    winston.format.timestamp(),\r\n    winston.format.errors({ stack: true }),\r\n  ];\r\n\r\n  // Add correlation ID format\r\n  if (config.correlation.enabled) {\r\n    formats.push(\r\n      winston.format((info) => {\r\n        const correlationId = info['correlationId'];\r\n        return correlationId ? { ...info, correlationId } : info;\r\n      })()\r\n    );\r\n  }\r\n\r\n  // Add data masking format\r\n  if (config.masking.enabled) {\r\n    formats.push(\r\n      winston.format((info) => {\r\n        const masked = { ...info };\r\n        config.masking.fields.forEach((field) => {\r\n          if (masked[field]) {\r\n            masked[field] = config.masking.replacement;\r\n          }\r\n        });\r\n        return masked;\r\n      })()\r\n    );\r\n  }\r\n\r\n  // Add final output format\r\n  formats.push(\r\n    config.format === LogFormat.JSON\r\n      ? winston.format.json()\r\n      : winston.format.simple()\r\n  );\r\n\r\n  return winston.format.combine(...formats);\r\n};"], "version": 3}