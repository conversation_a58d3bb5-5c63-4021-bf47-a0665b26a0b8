e6f90104fa8430ee3e2b0039bcfdc45f
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const event_emitter_1 = require("@nestjs/event-emitter");
const index_1 = require("./index");
const validation_1 = require("./validation");
const configuration_1 = __importStar(require("./configuration"));
const environment_validator_1 = require("./environment.validator");
const config_change_detector_service_1 = require("./config-change-detector.service");
const config_validator_1 = require("./validators/config.validator");
const NODE_ENV = process.env['NODE_ENV'] ?? 'development';
const ENV_FILE_PATHS = [
    `.env.${NODE_ENV}`,
    '.env.local',
    '.env'
];
const CONFIG_LOADERS = [
    index_1.databaseConfig,
    index_1.authConfig,
    index_1.jwtConfig,
    index_1.oauthConfig,
    index_1.aiConfig,
    index_1.redisConfig,
    index_1.loggingConfig,
    index_1.securityConfig,
    index_1.monitoringConfig,
    configuration_1.default,
];
/**
 * Centralized configuration module for Sentinel vulnerability assessment platform.
 *
 * @description Provides environment-specific loading, validation, type-safety, and hot-reload support
 * @module ConfigModule
 */
let ConfigModule = class ConfigModule {
};
exports.ConfigModule = ConfigModule;
exports.ConfigModule = ConfigModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                cache: true,
                expandVariables: true,
                envFilePath: ENV_FILE_PATHS,
                load: CONFIG_LOADERS,
                validationSchema: validation_1.configValidationSchema,
                validationOptions: {
                    allowUnknown: true,
                    abortEarly: false,
                },
            }),
            event_emitter_1.EventEmitterModule.forRoot({
                wildcard: false,
                delimiter: '.',
                newListener: false,
                removeListener: false,
                maxListeners: 10,
                verboseMemoryLeak: false,
                ignoreErrors: false,
            }),
        ],
        providers: [
            configuration_1.SentinelConfigurationService,
            index_1.DatabaseConfigurationService,
            environment_validator_1.EnvironmentValidator,
            config_change_detector_service_1.ConfigChangeDetectorService,
            config_validator_1.ConfigValidationService,
        ],
        exports: [
            config_1.ConfigModule,
            configuration_1.SentinelConfigurationService,
            index_1.DatabaseConfigurationService,
            environment_validator_1.EnvironmentValidator,
            config_change_detector_service_1.ConfigChangeDetectorService,
            config_validator_1.ConfigValidationService,
        ],
    })
], ConfigModule);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxpbmZyYXN0cnVjdHVyZVxcY29uZmlnXFxjb25maWcubW9kdWxlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLDJDQUF3QztBQUN4QywyQ0FBa0U7QUFDbEUseURBQTJEO0FBQzNELG1DQVdpQjtBQUNqQiw2Q0FBc0Q7QUFDdEQsaUVBQXNGO0FBQ3RGLG1FQUErRDtBQUMvRCxxRkFBK0U7QUFDL0Usb0VBQXdFO0FBRXhFLE1BQU0sUUFBUSxHQUFHLE9BQU8sQ0FBQyxHQUFHLENBQUMsVUFBVSxDQUFDLElBQUksYUFBYSxDQUFDO0FBRTFELE1BQU0sY0FBYyxHQUFHO0lBQ3JCLFFBQVEsUUFBUSxFQUFFO0lBQ2xCLFlBQVk7SUFDWixNQUFNO0NBQ1AsQ0FBQztBQUVGLE1BQU0sY0FBYyxHQUFHO0lBQ3JCLHNCQUFjO0lBQ2Qsa0JBQVU7SUFDVixpQkFBUztJQUNULG1CQUFXO0lBQ1gsZ0JBQVE7SUFDUixtQkFBVztJQUNYLHFCQUFhO0lBQ2Isc0JBQWM7SUFDZCx3QkFBZ0I7SUFDaEIsdUJBQXFCO0NBQ3RCLENBQUM7QUFFRjs7Ozs7R0FLRztBQXlDSSxJQUFNLFlBQVksR0FBbEIsTUFBTSxZQUFZO0NBQUcsQ0FBQTtBQUFmLG9DQUFZO3VCQUFaLFlBQVk7SUF4Q3hCLElBQUEsZUFBTSxFQUFDO1FBQ04sT0FBTyxFQUFFO1lBQ1AscUJBQWdCLENBQUMsT0FBTyxDQUFDO2dCQUN2QixRQUFRLEVBQUUsSUFBSTtnQkFDZCxLQUFLLEVBQUUsSUFBSTtnQkFDWCxlQUFlLEVBQUUsSUFBSTtnQkFDckIsV0FBVyxFQUFFLGNBQWM7Z0JBQzNCLElBQUksRUFBRSxjQUFjO2dCQUNwQixnQkFBZ0IsRUFBRSxtQ0FBc0I7Z0JBQ3hDLGlCQUFpQixFQUFFO29CQUNqQixZQUFZLEVBQUUsSUFBSTtvQkFDbEIsVUFBVSxFQUFFLEtBQUs7aUJBQ2xCO2FBQ0YsQ0FBQztZQUNGLGtDQUFrQixDQUFDLE9BQU8sQ0FBQztnQkFDekIsUUFBUSxFQUFFLEtBQUs7Z0JBQ2YsU0FBUyxFQUFFLEdBQUc7Z0JBQ2QsV0FBVyxFQUFFLEtBQUs7Z0JBQ2xCLGNBQWMsRUFBRSxLQUFLO2dCQUNyQixZQUFZLEVBQUUsRUFBRTtnQkFDaEIsaUJBQWlCLEVBQUUsS0FBSztnQkFDeEIsWUFBWSxFQUFFLEtBQUs7YUFDcEIsQ0FBQztTQUNIO1FBQ0QsU0FBUyxFQUFFO1lBQ1QsNENBQTRCO1lBQzVCLG9DQUE0QjtZQUM1Qiw0Q0FBb0I7WUFDcEIsNERBQTJCO1lBQzNCLDBDQUF1QjtTQUN4QjtRQUNELE9BQU8sRUFBRTtZQUNQLHFCQUFnQjtZQUNoQiw0Q0FBNEI7WUFDNUIsb0NBQTRCO1lBQzVCLDRDQUFvQjtZQUNwQiw0REFBMkI7WUFDM0IsMENBQXVCO1NBQ3hCO0tBQ0YsQ0FBQztHQUNXLFlBQVksQ0FBRyIsIm5hbWVzIjpbXSwic291cmNlcyI6WyJDOlxcVXNlcnNcXEx1a2FcXHNlbnRpbmVsXFxiYWNrZW5kXFxzcmNcXGluZnJhc3RydWN0dXJlXFxjb25maWdcXGNvbmZpZy5tb2R1bGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTW9kdWxlIH0gZnJvbSAnQG5lc3Rqcy9jb21tb24nO1xyXG5pbXBvcnQgeyBDb25maWdNb2R1bGUgYXMgTmVzdENvbmZpZ01vZHVsZSB9IGZyb20gJ0BuZXN0anMvY29uZmlnJztcclxuaW1wb3J0IHsgRXZlbnRFbWl0dGVyTW9kdWxlIH0gZnJvbSAnQG5lc3Rqcy9ldmVudC1lbWl0dGVyJztcclxuaW1wb3J0IHsgXHJcbiAgZGF0YWJhc2VDb25maWcsIFxyXG4gIERhdGFiYXNlQ29uZmlndXJhdGlvblNlcnZpY2UsXHJcbiAgYXV0aENvbmZpZyxcclxuICBqd3RDb25maWcsXHJcbiAgb2F1dGhDb25maWcsXHJcbiAgYWlDb25maWcsIFxyXG4gIHJlZGlzQ29uZmlnLCBcclxuICBsb2dnaW5nQ29uZmlnLCBcclxuICBzZWN1cml0eUNvbmZpZywgXHJcbiAgbW9uaXRvcmluZ0NvbmZpZyBcclxufSBmcm9tICcuL2luZGV4JztcclxuaW1wb3J0IHsgY29uZmlnVmFsaWRhdGlvblNjaGVtYSB9IGZyb20gJy4vdmFsaWRhdGlvbic7XHJcbmltcG9ydCBzZW50aW5lbENvbmZpZ3VyYXRpb24sIHsgU2VudGluZWxDb25maWd1cmF0aW9uU2VydmljZSB9IGZyb20gJy4vY29uZmlndXJhdGlvbic7XHJcbmltcG9ydCB7IEVudmlyb25tZW50VmFsaWRhdG9yIH0gZnJvbSAnLi9lbnZpcm9ubWVudC52YWxpZGF0b3InO1xyXG5pbXBvcnQgeyBDb25maWdDaGFuZ2VEZXRlY3RvclNlcnZpY2UgfSBmcm9tICcuL2NvbmZpZy1jaGFuZ2UtZGV0ZWN0b3Iuc2VydmljZSc7XHJcbmltcG9ydCB7IENvbmZpZ1ZhbGlkYXRpb25TZXJ2aWNlIH0gZnJvbSAnLi92YWxpZGF0b3JzL2NvbmZpZy52YWxpZGF0b3InO1xyXG5cclxuY29uc3QgTk9ERV9FTlYgPSBwcm9jZXNzLmVudlsnTk9ERV9FTlYnXSA/PyAnZGV2ZWxvcG1lbnQnO1xyXG5cclxuY29uc3QgRU5WX0ZJTEVfUEFUSFMgPSBbXHJcbiAgYC5lbnYuJHtOT0RFX0VOVn1gLFxyXG4gICcuZW52LmxvY2FsJyxcclxuICAnLmVudidcclxuXTtcclxuXHJcbmNvbnN0IENPTkZJR19MT0FERVJTID0gW1xyXG4gIGRhdGFiYXNlQ29uZmlnLFxyXG4gIGF1dGhDb25maWcsXHJcbiAgand0Q29uZmlnLFxyXG4gIG9hdXRoQ29uZmlnLFxyXG4gIGFpQ29uZmlnLFxyXG4gIHJlZGlzQ29uZmlnLFxyXG4gIGxvZ2dpbmdDb25maWcsXHJcbiAgc2VjdXJpdHlDb25maWcsXHJcbiAgbW9uaXRvcmluZ0NvbmZpZyxcclxuICBzZW50aW5lbENvbmZpZ3VyYXRpb24sXHJcbl07XHJcblxyXG4vKipcclxuICogQ2VudHJhbGl6ZWQgY29uZmlndXJhdGlvbiBtb2R1bGUgZm9yIFNlbnRpbmVsIHZ1bG5lcmFiaWxpdHkgYXNzZXNzbWVudCBwbGF0Zm9ybS5cclxuICogXHJcbiAqIEBkZXNjcmlwdGlvbiBQcm92aWRlcyBlbnZpcm9ubWVudC1zcGVjaWZpYyBsb2FkaW5nLCB2YWxpZGF0aW9uLCB0eXBlLXNhZmV0eSwgYW5kIGhvdC1yZWxvYWQgc3VwcG9ydFxyXG4gKiBAbW9kdWxlIENvbmZpZ01vZHVsZVxyXG4gKi9cclxuQE1vZHVsZSh7XHJcbiAgaW1wb3J0czogW1xyXG4gICAgTmVzdENvbmZpZ01vZHVsZS5mb3JSb290KHtcclxuICAgICAgaXNHbG9iYWw6IHRydWUsXHJcbiAgICAgIGNhY2hlOiB0cnVlLFxyXG4gICAgICBleHBhbmRWYXJpYWJsZXM6IHRydWUsXHJcbiAgICAgIGVudkZpbGVQYXRoOiBFTlZfRklMRV9QQVRIUyxcclxuICAgICAgbG9hZDogQ09ORklHX0xPQURFUlMsXHJcbiAgICAgIHZhbGlkYXRpb25TY2hlbWE6IGNvbmZpZ1ZhbGlkYXRpb25TY2hlbWEsXHJcbiAgICAgIHZhbGlkYXRpb25PcHRpb25zOiB7XHJcbiAgICAgICAgYWxsb3dVbmtub3duOiB0cnVlLFxyXG4gICAgICAgIGFib3J0RWFybHk6IGZhbHNlLFxyXG4gICAgICB9LFxyXG4gICAgfSksXHJcbiAgICBFdmVudEVtaXR0ZXJNb2R1bGUuZm9yUm9vdCh7XHJcbiAgICAgIHdpbGRjYXJkOiBmYWxzZSxcclxuICAgICAgZGVsaW1pdGVyOiAnLicsXHJcbiAgICAgIG5ld0xpc3RlbmVyOiBmYWxzZSxcclxuICAgICAgcmVtb3ZlTGlzdGVuZXI6IGZhbHNlLFxyXG4gICAgICBtYXhMaXN0ZW5lcnM6IDEwLFxyXG4gICAgICB2ZXJib3NlTWVtb3J5TGVhazogZmFsc2UsXHJcbiAgICAgIGlnbm9yZUVycm9yczogZmFsc2UsXHJcbiAgICB9KSxcclxuICBdLFxyXG4gIHByb3ZpZGVyczogW1xyXG4gICAgU2VudGluZWxDb25maWd1cmF0aW9uU2VydmljZSxcclxuICAgIERhdGFiYXNlQ29uZmlndXJhdGlvblNlcnZpY2UsXHJcbiAgICBFbnZpcm9ubWVudFZhbGlkYXRvcixcclxuICAgIENvbmZpZ0NoYW5nZURldGVjdG9yU2VydmljZSxcclxuICAgIENvbmZpZ1ZhbGlkYXRpb25TZXJ2aWNlLFxyXG4gIF0sXHJcbiAgZXhwb3J0czogW1xyXG4gICAgTmVzdENvbmZpZ01vZHVsZSxcclxuICAgIFNlbnRpbmVsQ29uZmlndXJhdGlvblNlcnZpY2UsXHJcbiAgICBEYXRhYmFzZUNvbmZpZ3VyYXRpb25TZXJ2aWNlLFxyXG4gICAgRW52aXJvbm1lbnRWYWxpZGF0b3IsXHJcbiAgICBDb25maWdDaGFuZ2VEZXRlY3RvclNlcnZpY2UsXHJcbiAgICBDb25maWdWYWxpZGF0aW9uU2VydmljZSxcclxuICBdLFxyXG59KVxyXG5leHBvcnQgY2xhc3MgQ29uZmlnTW9kdWxlIHt9Il0sInZlcnNpb24iOjN9