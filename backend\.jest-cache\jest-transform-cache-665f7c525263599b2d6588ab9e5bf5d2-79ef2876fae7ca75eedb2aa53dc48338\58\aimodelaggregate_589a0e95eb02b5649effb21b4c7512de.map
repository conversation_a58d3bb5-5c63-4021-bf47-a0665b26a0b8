{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\aggregates\\ai-model.aggregate.ts", "mappings": ";;;AACA,iEAA0F;AAC1F,oEAAqF;AAwBrF,MAAa,gBAAgB;IAG3B,YAAY,SAAoB,EAAE;QAF1B,WAAM,GAAyB,IAAI,GAAG,EAAE,CAAC;QAG/C,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACrB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,WAAW,CAAC,OAA6B,EAAE,EAAmB;QACnE,qDAAqD;QACrD,MAAM,aAAa,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;QACtF,IAAI,aAAa,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,oBAAoB,OAAO,CAAC,IAAI,kCAAkC,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;QACzG,CAAC;QAED,MAAM,KAAK,GAAG,iCAAc,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QACjD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;QAE5C,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACI,QAAQ,CAAC,KAAc;QAC5B,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC;YACzC,MAAM,IAAI,KAAK,CAAC,kBAAkB,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,+BAA+B,CAAC,CAAC;QACxF,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACI,WAAW,CAAC,OAAuB;QACxC,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QAClD,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,kBAAkB,OAAO,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC;QAED,oCAAoC;QACpC,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACI,QAAQ,CAAC,OAAuB;QACrC,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,YAAY;QACjB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACI,eAAe;QACpB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,6BAAW,CAAC,MAAM,CAAC,CAAC;IAC/F,CAAC;IAED;;OAEG;IACI,UAAU,CAAC,QAAgC;QAChD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACrD,IAAI,QAAQ,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACpE,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,QAAQ,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,KAAK,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBAC9D,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,QAAQ,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,KAAK,QAAQ,CAAC,SAAS,EAAE,CAAC;gBACjE,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,QAAQ,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,CAAC,QAAQ,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;gBAC9E,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,QAAQ,CAAC,UAAU,IAAI,KAAK,CAAC,WAAW,CAAC,cAAc,GAAG,QAAQ,CAAC,UAAU,EAAE,CAAC;gBAClF,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,QAAQ,CAAC,oBAAoB,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,EAAE,CAAC;gBAC/D,OAAO,KAAK,CAAC;YACf,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,QAAgC;QACrD,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAE7C,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAO,UAAU,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC;QAED,4DAA4D;QAC5D,MAAM,YAAY,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAC5C,KAAK;YACL,KAAK,EAAE,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,QAAQ,CAAC;SACjD,CAAC,CAAC,CAAC;QAEJ,gCAAgC;QAChC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;QAE/C,OAAO,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,2BAA2B,CAChC,QAAgC,EAChC,WAAkC,EAAE,IAAI,EAAE,cAAc,EAAE;QAE1D,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAE7C,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,QAAQ,QAAQ,CAAC,IAAI,EAAE,CAAC;YACtB,KAAK,aAAa;gBAChB,OAAO,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;YAE3C,KAAK,UAAU;gBACb,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;YAE3D,KAAK,cAAc;gBACjB,OAAO,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAE5C,KAAK,mBAAmB;gBACtB,OAAO,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;YAEjD;gBACE,OAAO,UAAU,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC;IACH,CAAC;IAED;;OAEG;IACI,aAAa;QAWlB,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;QAEhD,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;YAChD,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACjD,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAA4B,CAAC,CAAC;QAEjC,MAAM,oBAAoB,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;YACxD,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACrD,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAA4B,CAAC,CAAC;QAEjC,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;YACpD,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACvD,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAA4B,CAAC,CAAC;QAEjC,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QAC5E,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC,CAAC;QACxF,MAAM,qBAAqB,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,yBAAyB,EAAE,EAAE,CAAC,CAAC,CAAC;QAExG,OAAO;YACL,WAAW,EAAE,MAAM,CAAC,MAAM;YAC1B,YAAY,EAAE,YAAY,CAAC,6BAAW,CAAC,MAAM,CAAC,IAAI,CAAC;YACnD,cAAc,EAAE,YAAY,CAAC,6BAAW,CAAC,QAAQ,CAAC,IAAI,CAAC;YACvD,cAAc,EAAE,YAAY,CAAC,6BAAW,CAAC,QAAQ,CAAC,IAAI,CAAC;YACvD,uBAAuB,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,qBAAqB,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACtF,SAAS;YACT,kBAAkB,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC5E,oBAAoB;YACpB,gBAAgB;SACjB,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,QAAgC;QACpD,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QACzC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC1C,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,QAAgC;QACtD,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QACzC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC;QAC5C,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,QAAgC;QACnD,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QACzC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACzC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,mBAAmB;QACxB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACI,uBAAuB,CAAC,YAAoB,GAAG;QACpD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CACrD,KAAK,CAAC,yBAAyB,EAAE,GAAG,SAAS,CAC9C,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,aAAa;QAClB,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC5C,MAAM,gBAAgB,GAAG,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC;QAE5E,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClC,OAAO;QACT,CAAC;QAED,+DAA+D;QAC/D,gBAAgB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC/B,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,0CAA0C;gBACvE,KAAK,CAAC,UAAU,EAAE,CAAC;YACrB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,yBAAyB;IAEjB,0BAA0B,CAAC,IAAY,EAAE,QAAoB;QACnE,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CACnD,KAAK,CAAC,IAAI,KAAK,IAAI,IAAI,KAAK,CAAC,QAAQ,KAAK,QAAQ,CACnD,CAAC;IACJ,CAAC;IAEO,mBAAmB,CAAC,KAAc,EAAE,QAAgC;QAC1E,IAAI,KAAK,GAAG,KAAK,CAAC,yBAAyB,EAAE,CAAC;QAE9C,qCAAqC;QACrC,MAAM,kBAAkB,GAAG,KAAK,CAAC,cAAc,EAAE,GAAG,GAAG,CAAC;QACxD,KAAK,IAAI,kBAAkB,CAAC;QAE5B,qDAAqD;QACrD,IAAI,QAAQ,CAAC,QAAQ,IAAI,KAAK,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnE,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;QAED,4BAA4B;QAC5B,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC;QAE9C,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IACzC,CAAC;IAEO,gBAAgB,CAAC,UAAqB;QAC5C,4CAA4C;QAC5C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,KAAK,GAAG,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC;QAC5C,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAEO,cAAc,CAAC,UAAqB,EAAE,OAAgC;QAC5E,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;QAC5C,CAAC;QAED,MAAM,kBAAkB,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAClD,KAAK;YACL,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,IAAI,KAAK,CAAC,MAAM;SACrD,CAAC,CAAC,CAAC;QAEJ,MAAM,WAAW,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACnF,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,WAAW,CAAC;QAE3C,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,KAAK,MAAM,IAAI,IAAI,kBAAkB,EAAE,CAAC;YACtC,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC;YAC7B,IAAI,MAAM,IAAI,aAAa,EAAE,CAAC;gBAC5B,OAAO,IAAI,CAAC,KAAK,CAAC;YACpB,CAAC;QACH,CAAC;QAED,OAAO,UAAU,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC;IAEO,iBAAiB,CAAC,UAAqB;QAC7C,OAAO,UAAU,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,CAC1C,OAAO,CAAC,cAAc,EAAE,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CACpE,CAAC;IACJ,CAAC;IAEO,sBAAsB,CAAC,UAAqB;QAClD,OAAO,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE,CACzC,OAAO,CAAC,yBAAyB,EAAE,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CACxF,CAAC;IACJ,CAAC;CACF;AAnVD,4CAmVC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\aggregates\\ai-model.aggregate.ts"], "sourcesContent": ["import { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { AIModel, AIProvider, ModelType, ModelStatus } from '../entities/ai-model.entity';\r\nimport { AIModelFactory, CreateAIModelRequest } from '../factories/ai-model.factory';\r\n\r\n/**\r\n * AI Model Aggregate\r\n * \r\n * Aggregate service that manages AI Model domain operations.\r\n * Provides high-level business operations and ensures consistency\r\n * across multiple AI models and related entities.\r\n */\r\n\r\nexport interface ModelSelectionCriteria {\r\n  taskType?: string;\r\n  provider?: AIProvider;\r\n  modelType?: ModelType;\r\n  minAccuracy?: number;\r\n  maxLatency?: number;\r\n  requiresAvailability?: boolean;\r\n}\r\n\r\nexport interface LoadBalancingStrategy {\r\n  type: 'round_robin' | 'weighted' | 'least_loaded' | 'performance_based';\r\n  weights?: Record<string, number>;\r\n}\r\n\r\nexport class AIModelAggregate {\r\n  private models: Map<string, AIModel> = new Map();\r\n\r\n  constructor(models: AIModel[] = []) {\r\n    models.forEach(model => {\r\n      this.models.set(model.id.toString(), model);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Creates a new AI model and adds it to the aggregate\r\n   */\r\n  public createModel(request: CreateAIModelRequest, id?: UniqueEntityId): AIModel {\r\n    // Check for duplicate names within the same provider\r\n    const existingModel = this.findModelByNameAndProvider(request.name, request.provider);\r\n    if (existingModel) {\r\n      throw new Error(`Model with name '${request.name}' already exists for provider '${request.provider}'`);\r\n    }\r\n\r\n    const model = AIModelFactory.create(request, id);\r\n    this.models.set(model.id.toString(), model);\r\n    \r\n    return model;\r\n  }\r\n\r\n  /**\r\n   * Adds an existing model to the aggregate\r\n   */\r\n  public addModel(model: AIModel): void {\r\n    if (this.models.has(model.id.toString())) {\r\n      throw new Error(`Model with ID '${model.id.toString()}' already exists in aggregate`);\r\n    }\r\n\r\n    this.models.set(model.id.toString(), model);\r\n  }\r\n\r\n  /**\r\n   * Removes a model from the aggregate\r\n   */\r\n  public removeModel(modelId: UniqueEntityId): void {\r\n    const model = this.models.get(modelId.toString());\r\n    if (!model) {\r\n      throw new Error(`Model with ID '${modelId.toString()}' not found`);\r\n    }\r\n\r\n    // Archive the model before removing\r\n    model.archive();\r\n    this.models.delete(modelId.toString());\r\n  }\r\n\r\n  /**\r\n   * Gets a model by ID\r\n   */\r\n  public getModel(modelId: UniqueEntityId): AIModel | undefined {\r\n    return this.models.get(modelId.toString());\r\n  }\r\n\r\n  /**\r\n   * Gets all models\r\n   */\r\n  public getAllModels(): AIModel[] {\r\n    return Array.from(this.models.values());\r\n  }\r\n\r\n  /**\r\n   * Gets active models only\r\n   */\r\n  public getActiveModels(): AIModel[] {\r\n    return Array.from(this.models.values()).filter(model => model.status === ModelStatus.ACTIVE);\r\n  }\r\n\r\n  /**\r\n   * Finds models by criteria\r\n   */\r\n  public findModels(criteria: ModelSelectionCriteria): AIModel[] {\r\n    return Array.from(this.models.values()).filter(model => {\r\n      if (criteria.taskType && !model.supportsTaskType(criteria.taskType)) {\r\n        return false;\r\n      }\r\n\r\n      if (criteria.provider && model.provider !== criteria.provider) {\r\n        return false;\r\n      }\r\n\r\n      if (criteria.modelType && model.modelType !== criteria.modelType) {\r\n        return false;\r\n      }\r\n\r\n      if (criteria.minAccuracy && model.performance.accuracy < criteria.minAccuracy) {\r\n        return false;\r\n      }\r\n\r\n      if (criteria.maxLatency && model.performance.averageLatency > criteria.maxLatency) {\r\n        return false;\r\n      }\r\n\r\n      if (criteria.requiresAvailability && !model.canHandleRequest()) {\r\n        return false;\r\n      }\r\n\r\n      return true;\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Selects the best model for a task based on criteria\r\n   */\r\n  public selectBestModel(criteria: ModelSelectionCriteria): AIModel | undefined {\r\n    const candidates = this.findModels(criteria);\r\n    \r\n    if (candidates.length === 0) {\r\n      return undefined;\r\n    }\r\n\r\n    if (candidates.length === 1) {\r\n      return candidates[0];\r\n    }\r\n\r\n    // Score models based on performance, availability, and load\r\n    const scoredModels = candidates.map(model => ({\r\n      model,\r\n      score: this.calculateModelScore(model, criteria),\r\n    }));\r\n\r\n    // Sort by score (highest first)\r\n    scoredModels.sort((a, b) => b.score - a.score);\r\n\r\n    return scoredModels[0].model;\r\n  }\r\n\r\n  /**\r\n   * Distributes load across multiple models using load balancing\r\n   */\r\n  public selectModelForLoadBalancing(\r\n    criteria: ModelSelectionCriteria,\r\n    strategy: LoadBalancingStrategy = { type: 'least_loaded' }\r\n  ): AIModel | undefined {\r\n    const candidates = this.findModels(criteria);\r\n    \r\n    if (candidates.length === 0) {\r\n      return undefined;\r\n    }\r\n\r\n    switch (strategy.type) {\r\n      case 'round_robin':\r\n        return this.selectRoundRobin(candidates);\r\n      \r\n      case 'weighted':\r\n        return this.selectWeighted(candidates, strategy.weights);\r\n      \r\n      case 'least_loaded':\r\n        return this.selectLeastLoaded(candidates);\r\n      \r\n      case 'performance_based':\r\n        return this.selectPerformanceBased(candidates);\r\n      \r\n      default:\r\n        return candidates[0];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Gets aggregate statistics\r\n   */\r\n  public getStatistics(): {\r\n    totalModels: number;\r\n    activeModels: number;\r\n    inactiveModels: number;\r\n    archivedModels: number;\r\n    averagePerformanceScore: number;\r\n    totalLoad: number;\r\n    averageUtilization: number;\r\n    providerDistribution: Record<string, number>;\r\n    typeDistribution: Record<string, number>;\r\n  } {\r\n    const models = Array.from(this.models.values());\r\n    \r\n    const statusCounts = models.reduce((acc, model) => {\r\n      acc[model.status] = (acc[model.status] || 0) + 1;\r\n      return acc;\r\n    }, {} as Record<string, number>);\r\n\r\n    const providerDistribution = models.reduce((acc, model) => {\r\n      acc[model.provider] = (acc[model.provider] || 0) + 1;\r\n      return acc;\r\n    }, {} as Record<string, number>);\r\n\r\n    const typeDistribution = models.reduce((acc, model) => {\r\n      acc[model.modelType] = (acc[model.modelType] || 0) + 1;\r\n      return acc;\r\n    }, {} as Record<string, number>);\r\n\r\n    const totalLoad = models.reduce((sum, model) => sum + model.currentLoad, 0);\r\n    const totalUtilization = models.reduce((sum, model) => sum + model.getUtilization(), 0);\r\n    const totalPerformanceScore = models.reduce((sum, model) => sum + model.calculatePerformanceScore(), 0);\r\n\r\n    return {\r\n      totalModels: models.length,\r\n      activeModels: statusCounts[ModelStatus.ACTIVE] || 0,\r\n      inactiveModels: statusCounts[ModelStatus.INACTIVE] || 0,\r\n      archivedModels: statusCounts[ModelStatus.ARCHIVED] || 0,\r\n      averagePerformanceScore: models.length > 0 ? totalPerformanceScore / models.length : 0,\r\n      totalLoad,\r\n      averageUtilization: models.length > 0 ? totalUtilization / models.length : 0,\r\n      providerDistribution,\r\n      typeDistribution,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Activates all models matching criteria\r\n   */\r\n  public activateModels(criteria: ModelSelectionCriteria): AIModel[] {\r\n    const models = this.findModels(criteria);\r\n    models.forEach(model => model.activate());\r\n    return models;\r\n  }\r\n\r\n  /**\r\n   * Deactivates all models matching criteria\r\n   */\r\n  public deactivateModels(criteria: ModelSelectionCriteria): AIModel[] {\r\n    const models = this.findModels(criteria);\r\n    models.forEach(model => model.deactivate());\r\n    return models;\r\n  }\r\n\r\n  /**\r\n   * Archives all models matching criteria\r\n   */\r\n  public archiveModels(criteria: ModelSelectionCriteria): AIModel[] {\r\n    const models = this.findModels(criteria);\r\n    models.forEach(model => model.archive());\r\n    return models;\r\n  }\r\n\r\n  /**\r\n   * Gets models that are overloaded\r\n   */\r\n  public getOverloadedModels(): AIModel[] {\r\n    return Array.from(this.models.values()).filter(model => model.isOverloaded());\r\n  }\r\n\r\n  /**\r\n   * Gets models with low performance\r\n   */\r\n  public getLowPerformanceModels(threshold: number = 0.5): AIModel[] {\r\n    return Array.from(this.models.values()).filter(model => \r\n      model.calculatePerformanceScore() < threshold\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Rebalances load across models\r\n   */\r\n  public rebalanceLoad(): void {\r\n    const activeModels = this.getActiveModels();\r\n    const overloadedModels = activeModels.filter(model => model.isOverloaded());\r\n    \r\n    if (overloadedModels.length === 0) {\r\n      return;\r\n    }\r\n\r\n    // Simple rebalancing: deactivate overloaded models temporarily\r\n    overloadedModels.forEach(model => {\r\n      if (activeModels.length > 1) { // Don't deactivate if it's the only model\r\n        model.deactivate();\r\n      }\r\n    });\r\n  }\r\n\r\n  // Private helper methods\r\n\r\n  private findModelByNameAndProvider(name: string, provider: AIProvider): AIModel | undefined {\r\n    return Array.from(this.models.values()).find(model => \r\n      model.name === name && model.provider === provider\r\n    );\r\n  }\r\n\r\n  private calculateModelScore(model: AIModel, criteria: ModelSelectionCriteria): number {\r\n    let score = model.calculatePerformanceScore();\r\n    \r\n    // Adjust score based on current load\r\n    const utilizationPenalty = model.getUtilization() * 0.2;\r\n    score -= utilizationPenalty;\r\n    \r\n    // Boost score for models that exactly match criteria\r\n    if (criteria.taskType && model.supportsTaskType(criteria.taskType)) {\r\n      score += 0.1;\r\n    }\r\n    \r\n    // Apply priority and weight\r\n    score = score * model.priority * model.weight;\r\n    \r\n    return Math.max(0, Math.min(1, score));\r\n  }\r\n\r\n  private selectRoundRobin(candidates: AIModel[]): AIModel {\r\n    // Simple round-robin based on model ID hash\r\n    const timestamp = Date.now();\r\n    const index = timestamp % candidates.length;\r\n    return candidates[index];\r\n  }\r\n\r\n  private selectWeighted(candidates: AIModel[], weights?: Record<string, number>): AIModel {\r\n    if (!weights) {\r\n      return this.selectLeastLoaded(candidates);\r\n    }\r\n\r\n    const weightedCandidates = candidates.map(model => ({\r\n      model,\r\n      weight: weights[model.id.toString()] || model.weight,\r\n    }));\r\n\r\n    const totalWeight = weightedCandidates.reduce((sum, item) => sum + item.weight, 0);\r\n    const random = Math.random() * totalWeight;\r\n    \r\n    let currentWeight = 0;\r\n    for (const item of weightedCandidates) {\r\n      currentWeight += item.weight;\r\n      if (random <= currentWeight) {\r\n        return item.model;\r\n      }\r\n    }\r\n\r\n    return candidates[0];\r\n  }\r\n\r\n  private selectLeastLoaded(candidates: AIModel[]): AIModel {\r\n    return candidates.reduce((least, current) => \r\n      current.getUtilization() < least.getUtilization() ? current : least\r\n    );\r\n  }\r\n\r\n  private selectPerformanceBased(candidates: AIModel[]): AIModel {\r\n    return candidates.reduce((best, current) => \r\n      current.calculatePerformanceScore() > best.calculatePerformanceScore() ? current : best\r\n    );\r\n  }\r\n}"], "version": 3}