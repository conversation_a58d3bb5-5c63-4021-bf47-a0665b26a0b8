{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\application\\services\\ioc.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,6CAAqD;AAErD,2CAAwE;AACxE,+CAA2C;AAC3C,iEAAuD;AACvD,iFAAsE;AACtE,mFAAwE;AACxE,sFAAkF;AAClF,0FAAsF;AAEtF,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;IAC1B,IAAI,OAAmB,CAAC;IACxB,IAAI,aAA8B,CAAC;IACnC,IAAI,oBAA4C,CAAC;IACjD,IAAI,qBAA8C,CAAC;IACnD,IAAI,aAA4B,CAAC;IACjC,IAAI,YAA0B,CAAC;IAE/B,MAAM,iBAAiB,GAAG;QACxB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;KAC9B,CAAC;IAEF,MAAM,wBAAwB,GAAG;QAC/B,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;KACnB,CAAC;IAEF,MAAM,yBAAyB,GAAG;QAChC,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;KAClB,CAAC;IAEF,MAAM,iBAAiB,GAAG;QACxB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;QACd,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;KACjB,CAAC;IAEF,MAAM,gBAAgB,GAAG;QACvB,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;KACzB,CAAC;IAEF,MAAM,OAAO,GAAiB;QAC5B,EAAE,EAAE,sCAAsC;QAC1C,IAAI,EAAE,YAAY;QAClB,KAAK,EAAE,eAAe;QACtB,WAAW,EAAE,sBAAsB;QACnC,UAAU,EAAE,EAAE;QACd,QAAQ,EAAE,MAAM;QAChB,MAAM,EAAE,QAAQ;QAChB,GAAG,EAAE,OAAO;QACZ,SAAS,EAAE,IAAI,IAAI,CAAC,0BAA0B,CAAC;QAC/C,QAAQ,EAAE,IAAI,IAAI,CAAC,0BAA0B,CAAC;QAC9C,gBAAgB,EAAE,CAAC;QACnB,WAAW,EAAE,IAAI;QACjB,WAAW,EAAE,KAAK;QAClB,SAAS,EAAE,KAAK;QAChB,QAAQ,EAAE,IAAI;QACd,SAAS,EAAE,EAAE;QACb,SAAS,EAAE,GAAG;QACd,MAAM,EAAE,KAAK;QACb,kBAAkB,EAAE,IAAI;QACxB,eAAe,EAAE,eAAe;QAChC,SAAS,EAAE,IAAI,IAAI,EAAE;QACrB,SAAS,EAAE,IAAI,IAAI,EAAE;QACrB,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;QACzB,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE;QAC1B,mBAAmB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC9B,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;KAC7B,CAAC;IAEF,MAAM,eAAe,GAAyB;QAC5C,EAAE,EAAE,WAAW;QACf,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,KAAK;QACX,cAAc,EAAE,UAAU;KAC3B,CAAC;IAEF,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,wBAAU;gBACV;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,gBAAG,CAAC;oBAChC,QAAQ,EAAE,iBAAiB;iBAC5B;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,+BAAU,CAAC;oBACvC,QAAQ,EAAE,wBAAwB;iBACnC;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,iCAAW,CAAC;oBACxC,QAAQ,EAAE,yBAAyB;iBACpC;gBACD;oBACE,OAAO,EAAE,8BAAa;oBACtB,QAAQ,EAAE,iBAAiB;iBAC5B;gBACD;oBACE,OAAO,EAAE,4BAAY;oBACrB,QAAQ,EAAE,gBAAgB;iBAC3B;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAAa,wBAAU,CAAC,CAAC;QAC7C,aAAa,GAAG,MAAM,CAAC,GAAG,CAAkB,IAAA,4BAAkB,EAAC,gBAAG,CAAC,CAAC,CAAC;QACrE,oBAAoB,GAAG,MAAM,CAAC,GAAG,CAAyB,IAAA,4BAAkB,EAAC,+BAAU,CAAC,CAAC,CAAC;QAC1F,qBAAqB,GAAG,MAAM,CAAC,GAAG,CAA0B,IAAA,4BAAkB,EAAC,iCAAW,CAAC,CAAC,CAAC;QAC7F,aAAa,GAAG,MAAM,CAAC,GAAG,CAAgB,8BAAa,CAAC,CAAC;QACzD,YAAY,GAAG,MAAM,CAAC,GAAG,CAAe,4BAAY,CAAC,CAAC;IACxD,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,UAAU,GAAG;gBACjB,IAAI,EAAE,YAAY;gBAClB,KAAK,EAAE,eAAe;gBACtB,WAAW,EAAE,sBAAsB;gBACnC,UAAU,EAAE,EAAE;gBACd,QAAQ,EAAE,MAAe;aAC1B,CAAC;YACF,MAAM,SAAS,GAAG,aAAa,CAAC;YAEhC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAClE,iBAAiB,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAClD,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAElD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YAE3D,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC;YAC3F,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACpD,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAC7D,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,SAAS,EACT,QAAQ,EACR,KAAK,EACL,OAAO,CAAC,EAAE,EACV,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,MAAM,UAAU,GAAG;gBACjB,IAAI,EAAE,YAAY;gBAClB,KAAK,EAAE,eAAe;gBACtB,WAAW,EAAE,sBAAsB;aACpC,CAAC;YACF,MAAM,SAAS,GAAG,aAAa,CAAC;YAEhC,MAAM,WAAW,GAAG,EAAE,GAAG,OAAO,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;YACxD,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC,iBAAiB,CAAC,WAAkB,CAAC,CAAC;YAChF,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;YAEtD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YAE3D,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC;YAC3F,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACtD,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;YACjE,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,SAAS,EACT,iBAAiB,EACjB,KAAK,EACL,WAAW,CAAC,EAAE,EACd,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mEAAmE,EAAE,KAAK,IAAI,EAAE;YACjF,MAAM,UAAU,GAAG;gBACjB,IAAI,EAAE,YAAY;gBAClB,gBAAgB;aACjB,CAAC;YACF,MAAM,SAAS,GAAG,aAAa,CAAC;YAEhC,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,4BAAmB,CAAC,CAAC;QAC3F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,MAAM,EAAE,GAAG,sCAAsC,CAAC;YAClD,iBAAiB,CAAC,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAErD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAE1C,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBACrD,KAAK,EAAE,EAAE,EAAE,EAAE;gBACb,SAAS,EAAE,CAAC,YAAY,EAAE,cAAc,CAAC;aAC1C,CAAC,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,EAAE,GAAG,sCAAsC,CAAC;YAClD,iBAAiB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAElD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAE1C,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;YAC1B,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,eAAe,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,MAAM,IAAI,GAAG,YAAY,CAAC;YAC1B,MAAM,KAAK,GAAG,eAAe,CAAC;YAC9B,iBAAiB,CAAC,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAErD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,kBAAkB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAE7D,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBACrD,KAAK,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;gBACtB,SAAS,EAAE,CAAC,YAAY,EAAE,cAAc,CAAC;aAC1C,CAAC,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,EAAE,GAAG,sCAAsC,CAAC;YAClD,MAAM,UAAU,GAAG,EAAE,UAAU,EAAE,EAAE,EAAE,QAAQ,EAAE,UAAmB,EAAE,CAAC;YACrE,MAAM,SAAS,GAAG,aAAa,CAAC;YAEhC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,OAAc,CAAC,CAAC;YAClE,MAAM,UAAU,GAAG,EAAE,GAAG,OAAO,EAAE,GAAG,UAAU,EAAE,CAAC;YACjD,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAErD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;YAE/D,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;YAClD,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,CAAC;YAClD,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,SAAS,EACT,QAAQ,EACR,KAAK,EACL,EAAE,EACF,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,EAAE,GAAG,sCAAsC,CAAC;YAClD,MAAM,UAAU,GAAG,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC;YACtC,MAAM,SAAS,GAAG,aAAa,CAAC;YAEhC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAExD,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,0BAAiB,CAAC,CAAC;QAC7F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,EAAE,GAAG,sCAAsC,CAAC;YAClD,MAAM,SAAS,GAAG,WAAW,CAAC;YAE9B,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,OAAc,CAAC,CAAC;YAClE,iBAAiB,CAAC,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAEpD,MAAM,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;YAEpC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;YAClD,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAC/D,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,SAAS,EACT,QAAQ,EACR,KAAK,EACL,EAAE,EACF,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,EAAE,GAAG,sCAAsC,CAAC;YAClD,MAAM,SAAS,GAAG,WAAW,CAAC;YAE9B,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAExD,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,0BAAiB,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,MAAM,QAAQ,GAAG;gBACf,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,eAAe,EAAE;gBAC9C,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,eAAe,EAAE;aAC3C,CAAC;YACF,MAAM,SAAS,GAAG,aAAa,CAAC;YAEhC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC;iBAC1B,qBAAqB,CAAC,EAAE,GAAG,OAAO,EAAE,gBAAgB,EAAE,CAAC,EAAS,CAAC;iBACjE,qBAAqB,CAAC,EAAE,GAAG,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC,EAAS,CAAC,CAAC;YAElF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;YAE7D,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,SAAS,EACT,aAAa,EACb,KAAK,EACL,IAAI,EACJ,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,QAAQ,GAAG;gBACf,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,eAAe,EAAE;gBAC9C,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,eAAe,EAAE;aAC5C,CAAC;YACF,MAAM,SAAS,GAAG,aAAa,CAAC;YAEhC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC;iBAC1B,qBAAqB,CAAC,EAAE,GAAG,OAAO,EAAE,gBAAgB,EAAE,CAAC,EAAS,CAAC;iBACjE,qBAAqB,CAAC,IAAI,4BAAmB,CAAC,kBAAkB,CAAC,CAAC,CAAC;YAEtE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;YAE7D,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;YACpE,MAAM,KAAK,GAAG,sCAAsC,CAAC;YACrD,MAAM,cAAc,GAAG,CAAC,WAAW,CAAC,CAAC;YACrC,MAAM,SAAS,GAAG,aAAa,CAAC;YAEhC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,OAAc,CAAC,CAAC;YAClE,yBAAyB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC;YACtE,MAAM,UAAU,GAAG,EAAE,GAAG,OAAO,EAAE,YAAY,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC;YACnE,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAErD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,yBAAyB,CAAC,KAAK,EAAE,cAAc,EAAE,SAAS,CAAC,CAAC;YAEzF,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC;gBAC5D,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aACvB,CAAC,CAAC;YACH,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,CAAC;YAClD,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,SAAS,EACT,yBAAyB,EACzB,KAAK,EACL,KAAK,EACL,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,KAAK,GAAG,sCAAsC,CAAC;YACrD,MAAM,cAAc,GAAG,CAAC,WAAW,CAAC,CAAC;YACrC,MAAM,SAAS,GAAG,aAAa,CAAC;YAEhC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAExD,MAAM,MAAM,CACV,OAAO,CAAC,yBAAyB,CAAC,KAAK,EAAE,cAAc,EAAE,SAAS,CAAC,CACpE,CAAC,OAAO,CAAC,OAAO,CAAC,0BAAiB,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,KAAK,IAAI,EAAE;YAC5E,MAAM,KAAK,GAAG,sCAAsC,CAAC;YACrD,MAAM,cAAc,GAAG,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YAClD,MAAM,SAAS,GAAG,aAAa,CAAC;YAEhC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,OAAc,CAAC,CAAC;YAClE,yBAAyB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,iBAAiB;YAExF,MAAM,MAAM,CACV,OAAO,CAAC,yBAAyB,CAAC,KAAK,EAAE,cAAc,EAAE,SAAS,CAAC,CACpE,CAAC,OAAO,CAAC,OAAO,CAAC,4BAAmB,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,iBAAiB,CAAC,KAAK;iBACpB,qBAAqB,CAAC,GAAG,CAAC,CAAC,QAAQ;iBACnC,qBAAqB,CAAC,EAAE,CAAC,CAAE,SAAS;iBACpC,qBAAqB,CAAC,EAAE,CAAC,CAAE,WAAW;iBACtC,qBAAqB,CAAC,EAAE,CAAC,CAAE,UAAU;iBACrC,qBAAqB,CAAC,CAAC,CAAC,CAAG,WAAW;iBACtC,qBAAqB,CAAC,EAAE,CAAC,CAAE,OAAO;iBAClC,qBAAqB,CAAC,EAAE,CAAC,CAAE,SAAS;iBACpC,qBAAqB,CAAC,EAAE,CAAC,CAAE,MAAM;iBACjC,qBAAqB,CAAC,EAAE,CAAC,CAAE,eAAe;iBAC1C,qBAAqB,CAAC,EAAE,CAAC,CAAE,UAAU;iBACrC,qBAAqB,CAAC,EAAE,CAAC,CAAE,SAAS;iBACpC,qBAAqB,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO;YAErC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,aAAa,EAAE,CAAC;YAE7C,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,KAAK,EAAE,GAAG;gBACV,QAAQ,EAAE;oBACR,MAAM,EAAE,EAAE;oBACV,QAAQ,EAAE,EAAE;oBACZ,OAAO,EAAE,EAAE;iBACZ;gBACD,UAAU,EAAE;oBACV,QAAQ,EAAE,CAAC;oBACX,IAAI,EAAE,EAAE;oBACR,MAAM,EAAE,EAAE;oBACV,GAAG,EAAE,EAAE;iBACR;gBACD,MAAM,EAAE;oBACN,WAAW,EAAE,EAAE;oBACf,OAAO,EAAE,EAAE;oBACX,MAAM,EAAE,EAAE;oBACV,IAAI,EAAE,EAAE;oBACR,KAAK,EAAE,EAAE;iBACV;gBACD,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC9B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\application\\services\\ioc.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { getRepositoryToken } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\nimport { NotFoundException, BadRequestException } from '@nestjs/common';\r\nimport { IOCService } from './ioc.service';\r\nimport { IOC } from '../../domain/entities/ioc.entity';\r\nimport { ThreatFeed } from '../../domain/entities/threat-feed.entity';\r\nimport { ThreatActor } from '../../domain/entities/threat-actor.entity';\r\nimport { LoggerService } from '../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../infrastructure/logging/audit/audit.service';\r\n\r\ndescribe('IOCService', () => {\r\n  let service: IOCService;\r\n  let iocRepository: Repository<IOC>;\r\n  let threatFeedRepository: Repository<ThreatFeed>;\r\n  let threatActorRepository: Repository<ThreatActor>;\r\n  let loggerService: LoggerService;\r\n  let auditService: AuditService;\r\n\r\n  const mockIOCRepository = {\r\n    create: jest.fn(),\r\n    save: jest.fn(),\r\n    findOne: jest.fn(),\r\n    find: jest.fn(),\r\n    remove: jest.fn(),\r\n    count: jest.fn(),\r\n    createQueryBuilder: jest.fn(),\r\n  };\r\n\r\n  const mockThreatFeedRepository = {\r\n    findOne: jest.fn(),\r\n  };\r\n\r\n  const mockThreatActorRepository = {\r\n    findBy: jest.fn(),\r\n  };\r\n\r\n  const mockLoggerService = {\r\n    debug: jest.fn(),\r\n    log: jest.fn(),\r\n    warn: jest.fn(),\r\n    error: jest.fn(),\r\n  };\r\n\r\n  const mockAuditService = {\r\n    logUserAction: jest.fn(),\r\n  };\r\n\r\n  const mockIOC: Partial<IOC> = {\r\n    id: '123e4567-e89b-12d3-a456-426614174000',\r\n    type: 'ip_address',\r\n    value: '*************',\r\n    description: 'Malicious IP address',\r\n    confidence: 85,\r\n    severity: 'high',\r\n    status: 'active',\r\n    tlp: 'amber',\r\n    firstSeen: new Date('2023-12-01T10:00:00.000Z'),\r\n    lastSeen: new Date('2023-12-01T15:30:00.000Z'),\r\n    observationCount: 1,\r\n    isMonitored: true,\r\n    isValidated: false,\r\n    isExpired: false,\r\n    isActive: true,\r\n    ageInDays: 15,\r\n    riskScore: 8.5,\r\n    isHash: false,\r\n    isNetworkIndicator: true,\r\n    normalizedValue: '*************',\r\n    createdAt: new Date(),\r\n    updatedAt: new Date(),\r\n    updateLastSeen: jest.fn(),\r\n    markAsValidated: jest.fn(),\r\n    markAsFalsePositive: jest.fn(),\r\n    addEnrichmentData: jest.fn(),\r\n  };\r\n\r\n  const mockThreatActor: Partial<ThreatActor> = {\r\n    id: 'actor-123',\r\n    name: 'APT28',\r\n    type: 'apt',\r\n    sophistication: 'advanced',\r\n  };\r\n\r\n  beforeEach(async () => {\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        IOCService,\r\n        {\r\n          provide: getRepositoryToken(IOC),\r\n          useValue: mockIOCRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(ThreatFeed),\r\n          useValue: mockThreatFeedRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(ThreatActor),\r\n          useValue: mockThreatActorRepository,\r\n        },\r\n        {\r\n          provide: LoggerService,\r\n          useValue: mockLoggerService,\r\n        },\r\n        {\r\n          provide: AuditService,\r\n          useValue: mockAuditService,\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    service = module.get<IOCService>(IOCService);\r\n    iocRepository = module.get<Repository<IOC>>(getRepositoryToken(IOC));\r\n    threatFeedRepository = module.get<Repository<ThreatFeed>>(getRepositoryToken(ThreatFeed));\r\n    threatActorRepository = module.get<Repository<ThreatActor>>(getRepositoryToken(ThreatActor));\r\n    loggerService = module.get<LoggerService>(LoggerService);\r\n    auditService = module.get<AuditService>(AuditService);\r\n  });\r\n\r\n  afterEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('create', () => {\r\n    it('should create an IOC successfully', async () => {\r\n      const createData = {\r\n        type: 'ip_address',\r\n        value: '*************',\r\n        description: 'Malicious IP address',\r\n        confidence: 85,\r\n        severity: 'high' as const,\r\n      };\r\n      const createdBy = 'analyst-123';\r\n\r\n      jest.spyOn(service, 'findByTypeAndValue').mockResolvedValue(null);\r\n      mockIOCRepository.create.mockReturnValue(mockIOC);\r\n      mockIOCRepository.save.mockResolvedValue(mockIOC);\r\n\r\n      const result = await service.create(createData, createdBy);\r\n\r\n      expect(service.findByTypeAndValue).toHaveBeenCalledWith(createData.type, createData.value);\r\n      expect(mockIOCRepository.create).toHaveBeenCalled();\r\n      expect(mockIOCRepository.save).toHaveBeenCalledWith(mockIOC);\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        createdBy,\r\n        'create',\r\n        'ioc',\r\n        mockIOC.id,\r\n        expect.any(Object),\r\n      );\r\n      expect(result).toEqual(mockIOC);\r\n    });\r\n\r\n    it('should update existing IOC when duplicate is found', async () => {\r\n      const createData = {\r\n        type: 'ip_address',\r\n        value: '*************',\r\n        description: 'Malicious IP address',\r\n      };\r\n      const createdBy = 'analyst-123';\r\n\r\n      const existingIOC = { ...mockIOC, observationCount: 2 };\r\n      jest.spyOn(service, 'findByTypeAndValue').mockResolvedValue(existingIOC as IOC);\r\n      mockIOCRepository.save.mockResolvedValue(existingIOC);\r\n\r\n      const result = await service.create(createData, createdBy);\r\n\r\n      expect(service.findByTypeAndValue).toHaveBeenCalledWith(createData.type, createData.value);\r\n      expect(existingIOC.updateLastSeen).toHaveBeenCalled();\r\n      expect(mockIOCRepository.save).toHaveBeenCalledWith(existingIOC);\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        createdBy,\r\n        'update_existing',\r\n        'ioc',\r\n        existingIOC.id,\r\n        expect.any(Object),\r\n      );\r\n      expect(result).toEqual(existingIOC);\r\n    });\r\n\r\n    it('should throw BadRequestException when required fields are missing', async () => {\r\n      const createData = {\r\n        type: 'ip_address',\r\n        // Missing value\r\n      };\r\n      const createdBy = 'analyst-123';\r\n\r\n      await expect(service.create(createData, createdBy)).rejects.toThrow(BadRequestException);\r\n    });\r\n  });\r\n\r\n  describe('findById', () => {\r\n    it('should return IOC when found', async () => {\r\n      const id = '123e4567-e89b-12d3-a456-426614174000';\r\n      mockIOCRepository.findOne.mockResolvedValue(mockIOC);\r\n\r\n      const result = await service.findById(id);\r\n\r\n      expect(mockIOCRepository.findOne).toHaveBeenCalledWith({\r\n        where: { id },\r\n        relations: ['threatFeed', 'threatActors'],\r\n      });\r\n      expect(result).toEqual(mockIOC);\r\n    });\r\n\r\n    it('should return null when IOC not found', async () => {\r\n      const id = '123e4567-e89b-12d3-a456-426614174000';\r\n      mockIOCRepository.findOne.mockResolvedValue(null);\r\n\r\n      const result = await service.findById(id);\r\n\r\n      expect(result).toBeNull();\r\n      expect(mockLoggerService.warn).toHaveBeenCalledWith('IOC not found', { id });\r\n    });\r\n  });\r\n\r\n  describe('findByTypeAndValue', () => {\r\n    it('should return IOC when found by type and value', async () => {\r\n      const type = 'ip_address';\r\n      const value = '*************';\r\n      mockIOCRepository.findOne.mockResolvedValue(mockIOC);\r\n\r\n      const result = await service.findByTypeAndValue(type, value);\r\n\r\n      expect(mockIOCRepository.findOne).toHaveBeenCalledWith({\r\n        where: { type, value },\r\n        relations: ['threatFeed', 'threatActors'],\r\n      });\r\n      expect(result).toEqual(mockIOC);\r\n    });\r\n  });\r\n\r\n  describe('update', () => {\r\n    it('should update IOC successfully', async () => {\r\n      const id = '123e4567-e89b-12d3-a456-426614174000';\r\n      const updateData = { confidence: 90, severity: 'critical' as const };\r\n      const updatedBy = 'analyst-123';\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(mockIOC as IOC);\r\n      const updatedIOC = { ...mockIOC, ...updateData };\r\n      mockIOCRepository.save.mockResolvedValue(updatedIOC);\r\n\r\n      const result = await service.update(id, updateData, updatedBy);\r\n\r\n      expect(service.findById).toHaveBeenCalledWith(id);\r\n      expect(mockIOCRepository.save).toHaveBeenCalled();\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        updatedBy,\r\n        'update',\r\n        'ioc',\r\n        id,\r\n        expect.any(Object),\r\n      );\r\n      expect(result).toEqual(updatedIOC);\r\n    });\r\n\r\n    it('should throw NotFoundException when IOC not found', async () => {\r\n      const id = '123e4567-e89b-12d3-a456-426614174000';\r\n      const updateData = { confidence: 90 };\r\n      const updatedBy = 'analyst-123';\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(null);\r\n\r\n      await expect(service.update(id, updateData, updatedBy)).rejects.toThrow(NotFoundException);\r\n    });\r\n  });\r\n\r\n  describe('delete', () => {\r\n    it('should delete IOC successfully', async () => {\r\n      const id = '123e4567-e89b-12d3-a456-426614174000';\r\n      const deletedBy = 'admin-123';\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(mockIOC as IOC);\r\n      mockIOCRepository.remove.mockResolvedValue(mockIOC);\r\n\r\n      await service.delete(id, deletedBy);\r\n\r\n      expect(service.findById).toHaveBeenCalledWith(id);\r\n      expect(mockIOCRepository.remove).toHaveBeenCalledWith(mockIOC);\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        deletedBy,\r\n        'delete',\r\n        'ioc',\r\n        id,\r\n        expect.any(Object),\r\n      );\r\n    });\r\n\r\n    it('should throw NotFoundException when IOC not found', async () => {\r\n      const id = '123e4567-e89b-12d3-a456-426614174000';\r\n      const deletedBy = 'admin-123';\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(null);\r\n\r\n      await expect(service.delete(id, deletedBy)).rejects.toThrow(NotFoundException);\r\n    });\r\n  });\r\n\r\n  describe('bulkCreate', () => {\r\n    it('should bulk create IOCs successfully', async () => {\r\n      const iocsData = [\r\n        { type: 'ip_address', value: '*************' },\r\n        { type: 'domain', value: 'malicious.com' },\r\n      ];\r\n      const createdBy = 'analyst-123';\r\n\r\n      jest.spyOn(service, 'create')\r\n        .mockResolvedValueOnce({ ...mockIOC, observationCount: 1 } as IOC)\r\n        .mockResolvedValueOnce({ ...mockIOC, id: 'ioc-2', observationCount: 1 } as IOC);\r\n\r\n      const result = await service.bulkCreate(iocsData, createdBy);\r\n\r\n      expect(service.create).toHaveBeenCalledTimes(2);\r\n      expect(result.created).toHaveLength(2);\r\n      expect(result.updated).toHaveLength(0);\r\n      expect(result.errors).toHaveLength(0);\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        createdBy,\r\n        'bulk_create',\r\n        'ioc',\r\n        null,\r\n        expect.any(Object),\r\n      );\r\n    });\r\n\r\n    it('should handle errors during bulk creation', async () => {\r\n      const iocsData = [\r\n        { type: 'ip_address', value: '*************' },\r\n        { type: 'invalid', value: 'invalid-value' },\r\n      ];\r\n      const createdBy = 'analyst-123';\r\n\r\n      jest.spyOn(service, 'create')\r\n        .mockResolvedValueOnce({ ...mockIOC, observationCount: 1 } as IOC)\r\n        .mockRejectedValueOnce(new BadRequestException('Invalid IOC type'));\r\n\r\n      const result = await service.bulkCreate(iocsData, createdBy);\r\n\r\n      expect(result.created).toHaveLength(1);\r\n      expect(result.updated).toHaveLength(0);\r\n      expect(result.errors).toHaveLength(1);\r\n      expect(result.errors[0].error).toBe('Invalid IOC type');\r\n    });\r\n  });\r\n\r\n  describe('associateWithThreatActors', () => {\r\n    it('should associate IOC with threat actors successfully', async () => {\r\n      const iocId = '123e4567-e89b-12d3-a456-426614174000';\r\n      const threatActorIds = ['actor-123'];\r\n      const updatedBy = 'analyst-123';\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(mockIOC as IOC);\r\n      mockThreatActorRepository.findBy.mockResolvedValue([mockThreatActor]);\r\n      const updatedIOC = { ...mockIOC, threatActors: [mockThreatActor] };\r\n      mockIOCRepository.save.mockResolvedValue(updatedIOC);\r\n\r\n      const result = await service.associateWithThreatActors(iocId, threatActorIds, updatedBy);\r\n\r\n      expect(service.findById).toHaveBeenCalledWith(iocId);\r\n      expect(mockThreatActorRepository.findBy).toHaveBeenCalledWith({\r\n        id: expect.any(Object),\r\n      });\r\n      expect(mockIOCRepository.save).toHaveBeenCalled();\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        updatedBy,\r\n        'associate_threat_actors',\r\n        'ioc',\r\n        iocId,\r\n        expect.any(Object),\r\n      );\r\n      expect(result).toEqual(updatedIOC);\r\n    });\r\n\r\n    it('should throw NotFoundException when IOC not found', async () => {\r\n      const iocId = '123e4567-e89b-12d3-a456-426614174000';\r\n      const threatActorIds = ['actor-123'];\r\n      const updatedBy = 'analyst-123';\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(null);\r\n\r\n      await expect(\r\n        service.associateWithThreatActors(iocId, threatActorIds, updatedBy)\r\n      ).rejects.toThrow(NotFoundException);\r\n    });\r\n\r\n    it('should throw BadRequestException when threat actor not found', async () => {\r\n      const iocId = '123e4567-e89b-12d3-a456-426614174000';\r\n      const threatActorIds = ['actor-123', 'actor-456'];\r\n      const updatedBy = 'analyst-123';\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(mockIOC as IOC);\r\n      mockThreatActorRepository.findBy.mockResolvedValue([mockThreatActor]); // Only one found\r\n\r\n      await expect(\r\n        service.associateWithThreatActors(iocId, threatActorIds, updatedBy)\r\n      ).rejects.toThrow(BadRequestException);\r\n    });\r\n  });\r\n\r\n  describe('getStatistics', () => {\r\n    it('should return IOC statistics', async () => {\r\n      mockIOCRepository.count\r\n        .mockResolvedValueOnce(100) // total\r\n        .mockResolvedValueOnce(80)  // active\r\n        .mockResolvedValueOnce(10)  // inactive\r\n        .mockResolvedValueOnce(10)  // expired\r\n        .mockResolvedValueOnce(5)   // critical\r\n        .mockResolvedValueOnce(15)  // high\r\n        .mockResolvedValueOnce(50)  // medium\r\n        .mockResolvedValueOnce(30)  // low\r\n        .mockResolvedValueOnce(25)  // ip addresses\r\n        .mockResolvedValueOnce(30)  // domains\r\n        .mockResolvedValueOnce(20)  // hashes\r\n        .mockResolvedValueOnce(15); // urls\r\n\r\n      const result = await service.getStatistics();\r\n\r\n      expect(result).toEqual({\r\n        total: 100,\r\n        byStatus: {\r\n          active: 80,\r\n          inactive: 10,\r\n          expired: 10,\r\n        },\r\n        bySeverity: {\r\n          critical: 5,\r\n          high: 15,\r\n          medium: 50,\r\n          low: 30,\r\n        },\r\n        byType: {\r\n          ipAddresses: 25,\r\n          domains: 30,\r\n          hashes: 20,\r\n          urls: 15,\r\n          other: 10,\r\n        },\r\n        timestamp: expect.any(String),\r\n      });\r\n    });\r\n  });\r\n});\r\n"], "version": 3}