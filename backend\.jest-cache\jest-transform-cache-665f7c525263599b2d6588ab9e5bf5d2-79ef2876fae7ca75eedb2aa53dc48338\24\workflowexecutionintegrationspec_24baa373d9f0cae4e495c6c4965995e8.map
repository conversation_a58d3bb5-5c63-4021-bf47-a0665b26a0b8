{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\tests\\integration\\workflow-execution.integration.spec.ts", "mappings": ";;AAAA,6CAAsD;AAEtD,6CAAgD;AAChD,2CAA8C;AAC9C,yDAA2D;AAC3D,uCAA0C;AAG1C,6CAAqD;AACrD,gGAA2F;AAC3F,oFAA+E;AAC/E,8FAAiF;AACjF,0FAAqF;AACrF,gHAA0G;AAC1G,sHAAgH;AAChH,8FAAmF;AACnF,wFAA6E;AAC7E,wGAA4F;AAC5F,sFAA2E;AAC3E,8FAAmF;AAEnF;;;;;;;;;;GAUG;AACH,QAAQ,CAAC,sCAAsC,EAAE,GAAG,EAAE;IACpD,IAAI,GAAqB,CAAC;IAC1B,IAAI,eAA4C,CAAC;IACjD,IAAI,cAAqC,CAAC;IAC1C,IAAI,UAA8B,CAAC;IACnC,IAAI,gBAA0C,CAAC;IAC/C,IAAI,YAAgD,CAAC;IACrD,IAAI,eAAsD,CAAC;IAC3D,IAAI,kBAAoD,CAAC;IACzD,IAAI,mBAAkD,CAAC;IACvD,IAAI,iBAAuD,CAAC;IAC5D,IAAI,kBAAgD,CAAC;IACrD,IAAI,kBAAoD,CAAC;IAEzD,MAAM,QAAQ,GAAG;QACf,EAAE,EAAE,eAAe;QACnB,KAAK,EAAE,kBAAkB;QACzB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,SAAS;KAChB,CAAC;IAEF,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,aAAa,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAClE,OAAO,EAAE;gBACP,qBAAY,CAAC,OAAO,CAAC;oBACnB,QAAQ,EAAE,IAAI;oBACd,WAAW,EAAE,WAAW;iBACzB,CAAC;gBACF,uBAAa,CAAC,OAAO,CAAC;oBACpB,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,WAAW;oBAC7C,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,IAAI;oBAChD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,MAAM;oBAChD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,MAAM;oBAChD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,eAAe;oBACrD,QAAQ,EAAE;wBACR,mDAAoB;wBACpB,6CAAiB;wBACjB,4DAAwB;wBACxB,2CAAgB;wBAChB,mDAAoB;qBACrB;oBACD,WAAW,EAAE,IAAI;oBACjB,UAAU,EAAE,IAAI;iBACjB,CAAC;gBACF,uBAAa,CAAC,UAAU,CAAC;oBACvB,mDAAoB;oBACpB,6CAAiB;oBACjB,4DAAwB;oBACxB,2CAAgB;oBAChB,mDAAoB;iBACrB,CAAC;gBACF,kCAAkB,CAAC,OAAO,EAAE;gBAC5B,iBAAU,CAAC,OAAO,CAAC;oBACjB,KAAK,EAAE;wBACL,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,WAAW;wBAChD,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,IAAI;wBACnD,EAAE,EAAE,CAAC,EAAE,6BAA6B;qBACrC;iBACF,CAAC;gBACF,iBAAU,CAAC,aAAa,CACtB,EAAE,IAAI,EAAE,mBAAmB,EAAE,EAC7B,EAAE,IAAI,EAAE,qBAAqB,EAAE,EAC/B,EAAE,IAAI,EAAE,kBAAkB,EAAE,CAC7B;aACF;YACD,SAAS,EAAE;gBACT,2DAA2B;gBAC3B,+CAAqB;gBACrB,iDAAkB;gBAClB,qDAAwB;gBACxB,0EAAkC;gBAClC,gFAAqC;gBACrC,2CAA2C;gBAC3C;oBACE,OAAO,EAAE,2BAA2B;oBACpC,QAAQ,EAAE;wBACR,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC;wBACvF,qBAAqB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;wBACtD,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;qBAChE;iBACF;gBACD;oBACE,OAAO,EAAE,2BAA2B;oBACpC,QAAQ,EAAE;wBACR,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,CAAC;wBACxF,qBAAqB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;wBACtD,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;qBAChE;iBACF;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,GAAG,GAAG,aAAa,CAAC,qBAAqB,EAAE,CAAC;QAC5C,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC;QAEjB,wBAAwB;QACxB,eAAe,GAAG,aAAa,CAAC,GAAG,CAA8B,2DAA2B,CAAC,CAAC;QAC9F,cAAc,GAAG,aAAa,CAAC,GAAG,CAAwB,+CAAqB,CAAC,CAAC;QACjF,UAAU,GAAG,aAAa,CAAC,GAAG,CAAqB,iDAAkB,CAAC,CAAC;QACvE,gBAAgB,GAAG,aAAa,CAAC,GAAG,CAA2B,qDAAwB,CAAC,CAAC;QACzF,YAAY,GAAG,aAAa,CAAC,GAAG,CAAqC,0EAAkC,CAAC,CAAC;QACzG,eAAe,GAAG,aAAa,CAAC,GAAG,CAAwC,gFAAqC,CAAC,CAAC;QAElH,2BAA2B;QAC3B,kBAAkB,GAAG,aAAa,CAAC,GAAG,CACpC,IAAA,4BAAkB,EAAC,mDAAoB,CAAC,CACzC,CAAC;QACF,mBAAmB,GAAG,aAAa,CAAC,GAAG,CACrC,IAAA,4BAAkB,EAAC,6CAAiB,CAAC,CACtC,CAAC;QACF,iBAAiB,GAAG,aAAa,CAAC,GAAG,CACnC,IAAA,4BAAkB,EAAC,4DAAwB,CAAC,CAC7C,CAAC;QACF,kBAAkB,GAAG,aAAa,CAAC,GAAG,CACpC,IAAA,4BAAkB,EAAC,2CAAgB,CAAC,CACrC,CAAC;QACF,kBAAkB,GAAG,aAAa,CAAC,GAAG,CACpC,IAAA,4BAAkB,EAAC,mDAAoB,CAAC,CACzC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,qCAAqC;QACrC,MAAM,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACnC,MAAM,mBAAmB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACrC,MAAM,kBAAkB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACpC,MAAM,kBAAkB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACpC,MAAM,kBAAkB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,0DAA0D,EAAE,KAAK,IAAI,EAAE;YACxE,iCAAiC;YACjC,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,IAAI,CAAC;gBAC7C,IAAI,EAAE,qBAAqB;gBAC3B,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,+BAA+B;gBACxC,OAAO,EAAE,gEAAgE;gBACzE,SAAS,EAAE,CAAC,eAAe,EAAE,gBAAgB,CAAC;gBAC9C,QAAQ,EAAE,IAAI;gBACd,SAAS,EAAE,QAAQ,CAAC,EAAE;gBACtB,SAAS,EAAE,QAAQ,CAAC,EAAE;aACvB,CAAC,CAAC;YAEH,2BAA2B;YAC3B,MAAM,YAAY,GAAG;gBACnB,IAAI,EAAE,2BAA2B;gBACjC,WAAW,EAAE,oCAAoC;gBACjD,UAAU,EAAE;oBACV,SAAS,EAAE,mBAAmB;oBAC9B,KAAK,EAAE;wBACL;4BACE,EAAE,EAAE,mBAAmB;4BACvB,IAAI,EAAE,cAAc;4BACpB,MAAM,EAAE;gCACN,OAAO,EAAE,OAAO;gCAChB,UAAU,EAAE,QAAQ,CAAC,EAAE;gCACvB,UAAU,EAAE,CAAC,mBAAmB,CAAC;gCACjC,QAAQ,EAAE,MAAM;6BACjB;yBACF;qBACF;iBACF;gBACD,MAAM,EAAE,QAAQ;gBAChB,QAAQ,EAAE,mBAAmB;aAC9B,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,eAAe,CAAC,cAAc,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAC9E,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAC/B,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;YAElC,uBAAuB;YACvB,MAAM,aAAa,GAAG;gBACpB,KAAK,EAAE;oBACL,KAAK,EAAE;wBACL,EAAE,EAAE,WAAW;wBACf,OAAO,EAAE,6BAA6B;wBACtC,QAAQ,EAAE,UAAU;wBACpB,QAAQ,EAAE,EAAE;qBACb;oBACD,IAAI,EAAE,QAAQ;iBACf;gBACD,OAAO,EAAE;oBACP,WAAW,EAAE,MAAM;oBACnB,eAAe,EAAE,CAAC;iBACnB;aACF,CAAC;YAEF,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;YAC9F,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAChC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YAC5C,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAEzC,iCAAiC;YACjC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAExD,0CAA0C;YAC1C,MAAM,kBAAkB,GAAG,MAAM,mBAAmB,CAAC,OAAO,CAAC;gBAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,WAAW,EAAE;gBACpC,SAAS,EAAE,CAAC,UAAU,CAAC;aACxB,CAAC,CAAC;YAEH,MAAM,CAAC,kBAAkB,CAAC,CAAC,WAAW,EAAE,CAAC;YACzC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACpD,MAAM,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClD,MAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE9C,kCAAkC;YAClC,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACpD,MAAM,WAAW,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACnD,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACrD,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC7C,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;YACtE,6CAA6C;YAC7C,MAAM,YAAY,GAAG;gBACnB,IAAI,EAAE,2BAA2B;gBACjC,WAAW,EAAE,0CAA0C;gBACvD,UAAU,EAAE;oBACV,SAAS,EAAE,mBAAmB;oBAC9B,KAAK,EAAE;wBACL;4BACE,EAAE,EAAE,mBAAmB;4BACvB,IAAI,EAAE,cAAc;4BACpB,MAAM,EAAE;gCACN,OAAO,EAAE,OAAO;gCAChB,UAAU,EAAE,uBAAuB;gCACnC,UAAU,EAAE,CAAC,mBAAmB,CAAC;6BAClC;yBACF;qBACF;iBACF;gBACD,MAAM,EAAE,QAAQ;aACjB,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,eAAe,CAAC,cAAc,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAE9E,uBAAuB;YACvB,MAAM,aAAa,GAAG;gBACpB,KAAK,EAAE;oBACL,KAAK,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE;oBACjD,IAAI,EAAE,QAAQ;iBACf;aACF,CAAC;YAEF,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;YAE9F,iCAAiC;YACjC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAExD,0BAA0B;YAC1B,MAAM,eAAe,GAAG,MAAM,mBAAmB,CAAC,OAAO,CAAC;gBACxD,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,WAAW,EAAE;gBACpC,SAAS,EAAE,CAAC,UAAU,CAAC;aACxB,CAAC,CAAC;YAEH,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC9C,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,6DAA6D,EAAE,KAAK,IAAI,EAAE;YAC3E,yCAAyC;YACzC,MAAM,YAAY,GAAG;gBACnB,IAAI,EAAE,iCAAiC;gBACvC,WAAW,EAAE,kCAAkC;gBAC/C,UAAU,EAAE;oBACV,SAAS,EAAE,gBAAgB;oBAC3B,KAAK,EAAE;wBACL;4BACE,EAAE,EAAE,gBAAgB;4BACpB,IAAI,EAAE,WAAW;4BACjB,MAAM,EAAE;gCACN,SAAS,EAAE;oCACT,KAAK,EAAE,sBAAsB;oCAC7B,QAAQ,EAAE,IAAI;oCACd,KAAK,EAAE,UAAU;iCAClB;6BACF;4BACD,SAAS,EAAE;gCACT;oCACE,MAAM,EAAE,qBAAqB;oCAC7B,SAAS,EAAE;wCACT,KAAK,EAAE,mBAAmB;wCAC1B,QAAQ,EAAE,IAAI;wCACd,KAAK,EAAE,IAAI;qCACZ;iCACF;gCACD;oCACE,MAAM,EAAE,mBAAmB;oCAC3B,SAAS,EAAE;wCACT,KAAK,EAAE,mBAAmB;wCAC1B,QAAQ,EAAE,IAAI;wCACd,KAAK,EAAE,KAAK;qCACb;iCACF;6BACF;yBACF;wBACD;4BACE,EAAE,EAAE,qBAAqB;4BACzB,IAAI,EAAE,cAAc;4BACpB,MAAM,EAAE;gCACN,OAAO,EAAE,OAAO;gCAChB,OAAO,EAAE,mCAAmC;gCAC5C,UAAU,EAAE,CAAC,oBAAoB,EAAE,qBAAqB,CAAC;gCACzD,QAAQ,EAAE,UAAU;6BACrB;yBACF;wBACD;4BACE,EAAE,EAAE,mBAAmB;4BACvB,IAAI,EAAE,cAAc;4BACpB,MAAM,EAAE;gCACN,OAAO,EAAE,OAAO;gCAChB,OAAO,EAAE,gCAAgC;gCACzC,UAAU,EAAE,CAAC,kBAAkB,CAAC;gCAChC,QAAQ,EAAE,QAAQ;6BACnB;yBACF;qBACF;iBACF;gBACD,MAAM,EAAE,QAAQ;aACjB,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,eAAe,CAAC,cAAc,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAE9E,2BAA2B;YAC3B,MAAM,qBAAqB,GAAG;gBAC5B,KAAK,EAAE;oBACL,KAAK,EAAE;wBACL,OAAO,EAAE,eAAe;wBACxB,QAAQ,EAAE,UAAU;qBACrB;oBACD,IAAI,EAAE,QAAQ;iBACf;aACF,CAAC;YAEF,MAAM,iBAAiB,GAAG,MAAM,eAAe,CAAC,eAAe,CAC7D,QAAQ,CAAC,EAAE,EACX,qBAAqB,EACrB,QAAQ,CACT,CAAC;YAEF,qBAAqB;YACrB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAExD,MAAM,0BAA0B,GAAG,MAAM,mBAAmB,CAAC,OAAO,CAAC;gBACnE,KAAK,EAAE,EAAE,EAAE,EAAE,iBAAiB,CAAC,WAAW,EAAE;gBAC5C,SAAS,EAAE,CAAC,UAAU,CAAC;aACxB,CAAC,CAAC;YAEH,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5D,MAAM,CAAC,0BAA0B,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,oCAAoC;YAE/F,0CAA0C;YAC1C,MAAM,mBAAmB,GAAG,0BAA0B,CAAC,QAAQ,CAAC,IAAI,CAClE,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,KAAK,qBAAqB,CAC5C,CAAC;YACF,MAAM,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1C,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAErD,yBAAyB;YACzB,MAAM,mBAAmB,GAAG;gBAC1B,KAAK,EAAE;oBACL,KAAK,EAAE;wBACL,OAAO,EAAE,aAAa;wBACtB,QAAQ,EAAE,KAAK;qBAChB;oBACD,IAAI,EAAE,QAAQ;iBACf;aACF,CAAC;YAEF,MAAM,eAAe,GAAG,MAAM,eAAe,CAAC,eAAe,CAC3D,QAAQ,CAAC,EAAE,EACX,mBAAmB,EACnB,QAAQ,CACT,CAAC;YAEF,qBAAqB;YACrB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAExD,MAAM,wBAAwB,GAAG,MAAM,mBAAmB,CAAC,OAAO,CAAC;gBACjE,KAAK,EAAE,EAAE,EAAE,EAAE,eAAe,CAAC,WAAW,EAAE;gBAC1C,SAAS,EAAE,CAAC,UAAU,CAAC;aACxB,CAAC,CAAC;YAEH,MAAM,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC1D,MAAM,CAAC,wBAAwB,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,kCAAkC;YAE3F,wCAAwC;YACxC,MAAM,iBAAiB,GAAG,wBAAwB,CAAC,QAAQ,CAAC,IAAI,CAC9D,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,KAAK,mBAAmB,CAC1C,CAAC;YACF,MAAM,CAAC,iBAAiB,CAAC,CAAC,WAAW,EAAE,CAAC;YACxC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACrE,MAAM,YAAY,GAAG;gBACnB,IAAI,EAAE,+BAA+B;gBACrC,WAAW,EAAE,kCAAkC;gBAC/C,UAAU,EAAE;oBACV,SAAS,EAAE,eAAe;oBAC1B,KAAK,EAAE;wBACL;4BACE,EAAE,EAAE,eAAe;4BACnB,IAAI,EAAE,UAAU;4BAChB,MAAM,EAAE;gCACN,SAAS,EAAE;oCACT,SAAS,EAAE,IAAI,EAAE,uBAAuB;oCACxC,mBAAmB,EAAE,wCAAwC;iCAC9D;6BACF;4BACD,QAAQ,EAAE,WAAW;yBACtB;wBACD;4BACE,EAAE,EAAE,WAAW;4BACf,IAAI,EAAE,OAAO;4BACb,MAAM,EAAE;gCACN,KAAK,EAAE,yBAAyB;6BACjC;4BACD,QAAQ,EAAE,mBAAmB;yBAC9B;wBACD;4BACE,EAAE,EAAE,mBAAmB;4BACvB,IAAI,EAAE,cAAc;4BACpB,MAAM,EAAE;gCACN,OAAO,EAAE,OAAO;gCAChB,OAAO,EAAE,mCAAmC;gCAC5C,UAAU,EAAE,CAAC,qBAAqB,CAAC;6BACpC;yBACF;qBACF;iBACF;gBACD,MAAM,EAAE,QAAQ;aACjB,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,eAAe,CAAC,cAAc,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAE9E,MAAM,aAAa,GAAG;gBACpB,KAAK,EAAE;oBACL,KAAK,EAAE;wBACL,OAAO,EAAE,6BAA6B;wBACtC,QAAQ,EAAE,QAAQ;qBACnB;oBACD,IAAI,EAAE,QAAQ;iBACf;aACF,CAAC;YAEF,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;YAE9F,iCAAiC;YACjC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAExD,MAAM,kBAAkB,GAAG,MAAM,mBAAmB,CAAC,OAAO,CAAC;gBAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,WAAW,EAAE;gBACpC,SAAS,EAAE,CAAC,UAAU,CAAC;aACxB,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC3B,MAAM,aAAa,GAAG,OAAO,GAAG,SAAS,CAAC;YAE1C,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACpD,MAAM,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClD,MAAM,CAAC,aAAa,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,6CAA6C;YAE1F,0CAA0C;YAC1C,MAAM,YAAY,GAAG,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CACnD,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CACxD,CAAC;YAEF,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACrD,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACjD,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAEzD,sCAAsC;YACtC,MAAM,YAAY,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC;gBAC5C,SAAS,EAAE,IAAI;gBACf,mBAAmB,EAAE,4CAA4C;aAClE,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,uCAAuC;YACvC,MAAM,YAAY,GAAG;gBACnB,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE;qBACxB,qBAAqB,CAAC,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;qBACnD,qBAAqB,CAAC,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;qBACvD,qBAAqB,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,eAAe,EAAE,CAAC;gBACvE,qBAAqB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;gBACtD,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;aACjE,CAAC;YAEF,2CAA2C;YAC3C,MAAM,YAAY,GAAG;gBACnB,IAAI,EAAE,qBAAqB;gBAC3B,WAAW,EAAE,sBAAsB;gBACnC,UAAU,EAAE;oBACV,SAAS,EAAE,mBAAmB;oBAC9B,KAAK,EAAE;wBACL;4BACE,EAAE,EAAE,mBAAmB;4BACvB,IAAI,EAAE,cAAc;4BACpB,MAAM,EAAE;gCACN,OAAO,EAAE,OAAO;gCAChB,OAAO,EAAE,oBAAoB;gCAC7B,UAAU,EAAE,CAAC,mBAAmB,CAAC;6BAClC;4BACD,aAAa,EAAE;gCACb,KAAK,EAAE;oCACL,WAAW,EAAE,CAAC;oCACd,KAAK,EAAE,GAAG;iCACX;6BACF;yBACF;qBACF;iBACF;gBACD,MAAM,EAAE,QAAQ;aACjB,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,eAAe,CAAC,cAAc,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAE9E,MAAM,aAAa,GAAG;gBACpB,KAAK,EAAE;oBACL,KAAK,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAE;oBACpD,IAAI,EAAE,QAAQ;iBACf;aACF,CAAC;YAEF,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;YAE9F,kCAAkC;YAClC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAExD,MAAM,kBAAkB,GAAG,MAAM,mBAAmB,CAAC,OAAO,CAAC;gBAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,WAAW,EAAE;gBACpC,SAAS,EAAE,CAAC,UAAU,CAAC;aACxB,CAAC,CAAC;YAEH,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACpD,MAAM,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAElD,kCAAkC;YAClC,MAAM,WAAW,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACnD,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,2CAA2C;YACnF,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,YAAY,GAAG;gBACnB,IAAI,EAAE,wBAAwB;gBAC9B,WAAW,EAAE,yBAAyB;gBACtC,UAAU,EAAE;oBACV,SAAS,EAAE,sBAAsB;oBACjC,KAAK,EAAE;wBACL;4BACE,EAAE,EAAE,sBAAsB;4BAC1B,IAAI,EAAE,cAAc;4BACpB,MAAM,EAAE;gCACN,OAAO,EAAE,iBAAiB,EAAE,iBAAiB;gCAC7C,OAAO,EAAE,sBAAsB;gCAC/B,UAAU,EAAE,CAAC,qBAAqB,CAAC;6BACpC;4BACD,aAAa,EAAE;gCACb,YAAY,EAAE,uBAAuB;6BACtC;yBACF;wBACD;4BACE,EAAE,EAAE,uBAAuB;4BAC3B,IAAI,EAAE,cAAc;4BACpB,MAAM,EAAE;gCACN,OAAO,EAAE,OAAO;gCAChB,OAAO,EAAE,gDAAgD;gCACzD,UAAU,EAAE,CAAC,sBAAsB,CAAC;6BACrC;yBACF;qBACF;iBACF;gBACD,MAAM,EAAE,QAAQ;aACjB,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,eAAe,CAAC,cAAc,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAE9E,MAAM,aAAa,GAAG;gBACpB,KAAK,EAAE;oBACL,KAAK,EAAE,EAAE,OAAO,EAAE,eAAe,EAAE,QAAQ,EAAE,QAAQ,EAAE;oBACvD,IAAI,EAAE,QAAQ;iBACf;aACF,CAAC;YAEF,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;YAE9F,qBAAqB;YACrB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAExD,MAAM,kBAAkB,GAAG,MAAM,mBAAmB,CAAC,OAAO,CAAC;gBAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,WAAW,EAAE;gBACpC,SAAS,EAAE,CAAC,UAAU,CAAC;aACxB,CAAC,CAAC;YAEH,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACpD,MAAM,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,qCAAqC;YAExF,6BAA6B;YAC7B,MAAM,cAAc,GAAG,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CACrD,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,KAAK,sBAAsB,CAC7C,CAAC;YACF,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAE7C,iCAAiC;YACjC,MAAM,eAAe,GAAG,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CACtD,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,KAAK,uBAAuB,CAC9C,CAAC;YACF,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,YAAY,GAAG;gBACnB,IAAI,EAAE,0BAA0B;gBAChC,WAAW,EAAE,2BAA2B;gBACxC,UAAU,EAAE;oBACV,SAAS,EAAE,mBAAmB;oBAC9B,KAAK,EAAE;wBACL;4BACE,EAAE,EAAE,mBAAmB;4BACvB,IAAI,EAAE,cAAc;4BACpB,MAAM,EAAE;gCACN,OAAO,EAAE,OAAO;gCAChB,OAAO,EAAE,qCAAqC;gCAC9C,UAAU,EAAE,CAAC,wBAAwB,CAAC;6BACvC;yBACF;qBACF;iBACF;gBACD,MAAM,EAAE,QAAQ;aACjB,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,eAAe,CAAC,cAAc,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAE9E,0CAA0C;YAC1C,MAAM,oBAAoB,GAAG,EAAE,CAAC;YAChC,MAAM,iBAAiB,GAAG,EAAE,CAAC;YAE7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,oBAAoB,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC9C,MAAM,aAAa,GAAG;oBACpB,KAAK,EAAE;wBACL,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,OAAO,EAAE,mBAAmB,CAAC,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE;wBAChF,IAAI,EAAE,QAAQ;qBACf;iBACF,CAAC;gBAEF,iBAAiB,CAAC,IAAI,CACpB,eAAe,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,EAAE,aAAa,EAAE,QAAQ,CAAC,CACtE,CAAC;YACJ,CAAC;YAED,MAAM,UAAU,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YACxD,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;YAEtD,sCAAsC;YACtC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAExD,+CAA+C;YAC/C,MAAM,mBAAmB,GAAG,MAAM,mBAAmB,CAAC,IAAI,CAAC;gBACzD,KAAK,EAAE,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,EAAE;aACnC,CAAC,CAAC;YAEH,MAAM,CAAC,mBAAmB,CAAC,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;YAC/D,mBAAmB,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBACtC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAC3C,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,MAAM,YAAY,GAAG;gBACnB,IAAI,EAAE,2BAA2B;gBACjC,WAAW,EAAE,+BAA+B;gBAC5C,UAAU,EAAE;oBACV,SAAS,EAAE,OAAO;oBAClB,KAAK,EAAE;wBACL,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE;wBAC/F,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,EAAE,SAAS,EAAE,EAAE,KAAK,EAAE,gBAAgB,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE;wBAC1I,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,kBAAkB,EAAE,UAAU,EAAE,CAAC,kBAAkB,CAAC,EAAE,EAAE;qBACnI;iBACF;gBACD,MAAM,EAAE,QAAQ;aACjB,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,eAAe,CAAC,cAAc,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAE9E,MAAM,aAAa,GAAG;gBACpB,KAAK,EAAE;oBACL,KAAK,EAAE,EAAE,OAAO,EAAE,kBAAkB,EAAE,QAAQ,EAAE,QAAQ,EAAE;oBAC1D,IAAI,EAAE,QAAQ;iBACf;aACF,CAAC;YAEF,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;YAE9F,qBAAqB;YACrB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAExD,MAAM,kBAAkB,GAAG,MAAM,mBAAmB,CAAC,OAAO,CAAC;gBAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,WAAW,EAAE;gBACpC,SAAS,EAAE,CAAC,UAAU,CAAC;aACxB,CAAC,CAAC;YAEH,MAAM,kBAAkB,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAElD,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACpD,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAClD,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACvD,MAAM,CAAC,kBAAkB,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,oCAAoC;YAEpF,wCAAwC;YACxC,kBAAkB,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBAC5C,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;gBACvC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;gBAC5C,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;gBACxC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YAC5C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\tests\\integration\\workflow-execution.integration.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { INestApplication } from '@nestjs/common';\r\nimport { TypeOrmModule } from '@nestjs/typeorm';\r\nimport { ConfigModule } from '@nestjs/config';\r\nimport { EventEmitterModule } from '@nestjs/event-emitter';\r\nimport { BullModule } from '@nestjs/bull';\r\nimport * as request from 'supertest';\r\nimport { Repository } from 'typeorm';\r\nimport { getRepositoryToken } from '@nestjs/typeorm';\r\nimport { NotificationWorkflowService } from '../../services/notification-workflow.service';\r\nimport { WorkflowEngineService } from '../../services/workflow-engine.service';\r\nimport { WorkflowRuleEngine } from '../../services/workflow-rule-engine.service';\r\nimport { WorkflowSchedulerService } from '../../services/workflow-scheduler.service';\r\nimport { NotificationQueueManagementService } from '../../services/notification-queue-management.service';\r\nimport { NotificationTemplateManagementService } from '../../services/notification-template-management.service';\r\nimport { NotificationWorkflow } from '../../entities/notification-workflow.entity';\r\nimport { WorkflowExecution } from '../../entities/workflow-execution.entity';\r\nimport { WorkflowExecutionContext } from '../../entities/workflow-execution-context.entity';\r\nimport { WorkflowSchedule } from '../../entities/workflow-schedule.entity';\r\nimport { NotificationTemplate } from '../../entities/notification-template.entity';\r\n\r\n/**\r\n * Workflow Execution Integration Tests\r\n * \r\n * Comprehensive integration tests for workflow execution including:\r\n * - End-to-end workflow execution scenarios with real components\r\n * - Multi-step workflow validation with conditional logic\r\n * - Error handling and recovery mechanism testing\r\n * - Performance and timing validation under load\r\n * - Integration with notification providers and templates\r\n * - Real-time monitoring and analytics validation\r\n */\r\ndescribe('Workflow Execution Integration Tests', () => {\r\n  let app: INestApplication;\r\n  let workflowService: NotificationWorkflowService;\r\n  let workflowEngine: WorkflowEngineService;\r\n  let ruleEngine: WorkflowRuleEngine;\r\n  let schedulerService: WorkflowSchedulerService;\r\n  let queueService: NotificationQueueManagementService;\r\n  let templateService: NotificationTemplateManagementService;\r\n  let workflowRepository: Repository<NotificationWorkflow>;\r\n  let executionRepository: Repository<WorkflowExecution>;\r\n  let contextRepository: Repository<WorkflowExecutionContext>;\r\n  let scheduleRepository: Repository<WorkflowSchedule>;\r\n  let templateRepository: Repository<NotificationTemplate>;\r\n\r\n  const testUser = {\r\n    id: 'test-user-123',\r\n    email: '<EMAIL>',\r\n    role: 'admin',\r\n    team: 'testing',\r\n  };\r\n\r\n  beforeAll(async () => {\r\n    const moduleFixture: TestingModule = await Test.createTestingModule({\r\n      imports: [\r\n        ConfigModule.forRoot({\r\n          isGlobal: true,\r\n          envFilePath: '.env.test',\r\n        }),\r\n        TypeOrmModule.forRoot({\r\n          type: 'postgres',\r\n          host: process.env.TEST_DB_HOST || 'localhost',\r\n          port: parseInt(process.env.TEST_DB_PORT) || 5433,\r\n          username: process.env.TEST_DB_USERNAME || 'test',\r\n          password: process.env.TEST_DB_PASSWORD || 'test',\r\n          database: process.env.TEST_DB_NAME || 'sentinel_test',\r\n          entities: [\r\n            NotificationWorkflow,\r\n            WorkflowExecution,\r\n            WorkflowExecutionContext,\r\n            WorkflowSchedule,\r\n            NotificationTemplate,\r\n          ],\r\n          synchronize: true,\r\n          dropSchema: true,\r\n        }),\r\n        TypeOrmModule.forFeature([\r\n          NotificationWorkflow,\r\n          WorkflowExecution,\r\n          WorkflowExecutionContext,\r\n          WorkflowSchedule,\r\n          NotificationTemplate,\r\n        ]),\r\n        EventEmitterModule.forRoot(),\r\n        BullModule.forRoot({\r\n          redis: {\r\n            host: process.env.TEST_REDIS_HOST || 'localhost',\r\n            port: parseInt(process.env.TEST_REDIS_PORT) || 6380,\r\n            db: 1, // Use different DB for tests\r\n          },\r\n        }),\r\n        BullModule.registerQueue(\r\n          { name: 'notification-high' },\r\n          { name: 'notification-medium' },\r\n          { name: 'notification-low' }\r\n        ),\r\n      ],\r\n      providers: [\r\n        NotificationWorkflowService,\r\n        WorkflowEngineService,\r\n        WorkflowRuleEngine,\r\n        WorkflowSchedulerService,\r\n        NotificationQueueManagementService,\r\n        NotificationTemplateManagementService,\r\n        // Mock providers for external dependencies\r\n        {\r\n          provide: 'EmailNotificationProvider',\r\n          useValue: {\r\n            sendNotification: jest.fn().mockResolvedValue({ success: true, messageId: 'test-123' }),\r\n            validateConfiguration: jest.fn().mockReturnValue(true),\r\n            getHealthStatus: jest.fn().mockResolvedValue({ healthy: true }),\r\n          },\r\n        },\r\n        {\r\n          provide: 'SlackNotificationProvider',\r\n          useValue: {\r\n            sendNotification: jest.fn().mockResolvedValue({ success: true, messageId: 'slack-123' }),\r\n            validateConfiguration: jest.fn().mockReturnValue(true),\r\n            getHealthStatus: jest.fn().mockResolvedValue({ healthy: true }),\r\n          },\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    app = moduleFixture.createNestApplication();\r\n    await app.init();\r\n\r\n    // Get service instances\r\n    workflowService = moduleFixture.get<NotificationWorkflowService>(NotificationWorkflowService);\r\n    workflowEngine = moduleFixture.get<WorkflowEngineService>(WorkflowEngineService);\r\n    ruleEngine = moduleFixture.get<WorkflowRuleEngine>(WorkflowRuleEngine);\r\n    schedulerService = moduleFixture.get<WorkflowSchedulerService>(WorkflowSchedulerService);\r\n    queueService = moduleFixture.get<NotificationQueueManagementService>(NotificationQueueManagementService);\r\n    templateService = moduleFixture.get<NotificationTemplateManagementService>(NotificationTemplateManagementService);\r\n\r\n    // Get repository instances\r\n    workflowRepository = moduleFixture.get<Repository<NotificationWorkflow>>(\r\n      getRepositoryToken(NotificationWorkflow)\r\n    );\r\n    executionRepository = moduleFixture.get<Repository<WorkflowExecution>>(\r\n      getRepositoryToken(WorkflowExecution)\r\n    );\r\n    contextRepository = moduleFixture.get<Repository<WorkflowExecutionContext>>(\r\n      getRepositoryToken(WorkflowExecutionContext)\r\n    );\r\n    scheduleRepository = moduleFixture.get<Repository<WorkflowSchedule>>(\r\n      getRepositoryToken(WorkflowSchedule)\r\n    );\r\n    templateRepository = moduleFixture.get<Repository<NotificationTemplate>>(\r\n      getRepositoryToken(NotificationTemplate)\r\n    );\r\n  });\r\n\r\n  afterAll(async () => {\r\n    await app.close();\r\n  });\r\n\r\n  beforeEach(async () => {\r\n    // Clean up database before each test\r\n    await contextRepository.delete({});\r\n    await executionRepository.delete({});\r\n    await scheduleRepository.delete({});\r\n    await workflowRepository.delete({});\r\n    await templateRepository.delete({});\r\n  });\r\n\r\n  describe('Simple Workflow Execution', () => {\r\n    it('should execute a simple notification workflow end-to-end', async () => {\r\n      // Create a notification template\r\n      const template = await templateRepository.save({\r\n        name: 'Test Alert Template',\r\n        type: 'email',\r\n        subject: 'Test Alert: {{alert.message}}',\r\n        content: 'Alert Details: {{alert.message}}\\nSeverity: {{alert.severity}}',\r\n        variables: ['alert.message', 'alert.severity'],\r\n        isActive: true,\r\n        createdBy: testUser.id,\r\n        updatedBy: testUser.id,\r\n      });\r\n\r\n      // Create a simple workflow\r\n      const workflowData = {\r\n        name: 'Simple Alert Notification',\r\n        description: 'Send email notification for alerts',\r\n        definition: {\r\n          startStep: 'send_notification',\r\n          steps: [\r\n            {\r\n              id: 'send_notification',\r\n              type: 'notification',\r\n              config: {\r\n                channel: 'email',\r\n                templateId: template.id,\r\n                recipients: ['<EMAIL>'],\r\n                priority: 'high',\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        status: 'active',\r\n        category: 'incident_response',\r\n      };\r\n\r\n      const workflow = await workflowService.createWorkflow(workflowData, testUser);\r\n      expect(workflow).toBeDefined();\r\n      expect(workflow.id).toBeDefined();\r\n\r\n      // Execute the workflow\r\n      const executionData = {\r\n        input: {\r\n          alert: {\r\n            id: 'alert-123',\r\n            message: 'Database connection failure',\r\n            severity: 'critical',\r\n            priority: 95,\r\n          },\r\n          user: testUser,\r\n        },\r\n        context: {\r\n          environment: 'test',\r\n          escalationLevel: 1,\r\n        },\r\n      };\r\n\r\n      const execution = await workflowService.executeWorkflow(workflow.id, executionData, testUser);\r\n      expect(execution).toBeDefined();\r\n      expect(execution.executionId).toBeDefined();\r\n      expect(execution.status).toBe('running');\r\n\r\n      // Wait for execution to complete\r\n      await new Promise(resolve => setTimeout(resolve, 2000));\r\n\r\n      // Verify execution completed successfully\r\n      const completedExecution = await executionRepository.findOne({\r\n        where: { id: execution.executionId },\r\n        relations: ['contexts'],\r\n      });\r\n\r\n      expect(completedExecution).toBeDefined();\r\n      expect(completedExecution.status).toBe('completed');\r\n      expect(completedExecution.stepsCompleted).toBe(1);\r\n      expect(completedExecution.totalSteps).toBe(1);\r\n\r\n      // Verify step context was created\r\n      expect(completedExecution.contexts).toHaveLength(1);\r\n      const stepContext = completedExecution.contexts[0];\r\n      expect(stepContext.stepId).toBe('send_notification');\r\n      expect(stepContext.status).toBe('completed');\r\n      expect(stepContext.result).toBeDefined();\r\n    });\r\n\r\n    it('should handle workflow execution with invalid template', async () => {\r\n      // Create workflow with non-existent template\r\n      const workflowData = {\r\n        name: 'Invalid Template Workflow',\r\n        description: 'Workflow with invalid template reference',\r\n        definition: {\r\n          startStep: 'send_notification',\r\n          steps: [\r\n            {\r\n              id: 'send_notification',\r\n              type: 'notification',\r\n              config: {\r\n                channel: 'email',\r\n                templateId: 'non-existent-template',\r\n                recipients: ['<EMAIL>'],\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        status: 'active',\r\n      };\r\n\r\n      const workflow = await workflowService.createWorkflow(workflowData, testUser);\r\n\r\n      // Execute the workflow\r\n      const executionData = {\r\n        input: {\r\n          alert: { message: 'Test alert', severity: 'low' },\r\n          user: testUser,\r\n        },\r\n      };\r\n\r\n      const execution = await workflowService.executeWorkflow(workflow.id, executionData, testUser);\r\n\r\n      // Wait for execution to complete\r\n      await new Promise(resolve => setTimeout(resolve, 2000));\r\n\r\n      // Verify execution failed\r\n      const failedExecution = await executionRepository.findOne({\r\n        where: { id: execution.executionId },\r\n        relations: ['contexts'],\r\n      });\r\n\r\n      expect(failedExecution.status).toBe('failed');\r\n      expect(failedExecution.error).toContain('template');\r\n    });\r\n  });\r\n\r\n  describe('Multi-Step Workflow Execution', () => {\r\n    it('should execute a multi-step workflow with conditional logic', async () => {\r\n      // Create workflow with conditional steps\r\n      const workflowData = {\r\n        name: 'Conditional Escalation Workflow',\r\n        description: 'Escalate based on alert severity',\r\n        definition: {\r\n          startStep: 'check_severity',\r\n          steps: [\r\n            {\r\n              id: 'check_severity',\r\n              type: 'condition',\r\n              config: {\r\n                condition: {\r\n                  field: 'input.alert.severity',\r\n                  operator: 'eq',\r\n                  value: 'critical',\r\n                },\r\n              },\r\n              nextSteps: [\r\n                {\r\n                  stepId: 'send_critical_alert',\r\n                  condition: {\r\n                    field: 'stepResult.result',\r\n                    operator: 'eq',\r\n                    value: true,\r\n                  },\r\n                },\r\n                {\r\n                  stepId: 'send_normal_alert',\r\n                  condition: {\r\n                    field: 'stepResult.result',\r\n                    operator: 'eq',\r\n                    value: false,\r\n                  },\r\n                },\r\n              ],\r\n            },\r\n            {\r\n              id: 'send_critical_alert',\r\n              type: 'notification',\r\n              config: {\r\n                channel: 'email',\r\n                message: 'CRITICAL: {{input.alert.message}}',\r\n                recipients: ['<EMAIL>', '<EMAIL>'],\r\n                priority: 'critical',\r\n              },\r\n            },\r\n            {\r\n              id: 'send_normal_alert',\r\n              type: 'notification',\r\n              config: {\r\n                channel: 'email',\r\n                message: 'Alert: {{input.alert.message}}',\r\n                recipients: ['<EMAIL>'],\r\n                priority: 'medium',\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        status: 'active',\r\n      };\r\n\r\n      const workflow = await workflowService.createWorkflow(workflowData, testUser);\r\n\r\n      // Test critical alert path\r\n      const criticalExecutionData = {\r\n        input: {\r\n          alert: {\r\n            message: 'Database down',\r\n            severity: 'critical',\r\n          },\r\n          user: testUser,\r\n        },\r\n      };\r\n\r\n      const criticalExecution = await workflowService.executeWorkflow(\r\n        workflow.id,\r\n        criticalExecutionData,\r\n        testUser\r\n      );\r\n\r\n      // Wait for execution\r\n      await new Promise(resolve => setTimeout(resolve, 3000));\r\n\r\n      const completedCriticalExecution = await executionRepository.findOne({\r\n        where: { id: criticalExecution.executionId },\r\n        relations: ['contexts'],\r\n      });\r\n\r\n      expect(completedCriticalExecution.status).toBe('completed');\r\n      expect(completedCriticalExecution.stepsCompleted).toBe(2); // condition + critical notification\r\n\r\n      // Verify critical alert step was executed\r\n      const criticalStepContext = completedCriticalExecution.contexts.find(\r\n        ctx => ctx.stepId === 'send_critical_alert'\r\n      );\r\n      expect(criticalStepContext).toBeDefined();\r\n      expect(criticalStepContext.status).toBe('completed');\r\n\r\n      // Test normal alert path\r\n      const normalExecutionData = {\r\n        input: {\r\n          alert: {\r\n            message: 'Minor issue',\r\n            severity: 'low',\r\n          },\r\n          user: testUser,\r\n        },\r\n      };\r\n\r\n      const normalExecution = await workflowService.executeWorkflow(\r\n        workflow.id,\r\n        normalExecutionData,\r\n        testUser\r\n      );\r\n\r\n      // Wait for execution\r\n      await new Promise(resolve => setTimeout(resolve, 3000));\r\n\r\n      const completedNormalExecution = await executionRepository.findOne({\r\n        where: { id: normalExecution.executionId },\r\n        relations: ['contexts'],\r\n      });\r\n\r\n      expect(completedNormalExecution.status).toBe('completed');\r\n      expect(completedNormalExecution.stepsCompleted).toBe(2); // condition + normal notification\r\n\r\n      // Verify normal alert step was executed\r\n      const normalStepContext = completedNormalExecution.contexts.find(\r\n        ctx => ctx.stepId === 'send_normal_alert'\r\n      );\r\n      expect(normalStepContext).toBeDefined();\r\n      expect(normalStepContext.status).toBe('completed');\r\n    });\r\n\r\n    it('should execute workflow with delay and variable steps', async () => {\r\n      const workflowData = {\r\n        name: 'Delayed Notification Workflow',\r\n        description: 'Wait before sending notification',\r\n        definition: {\r\n          startStep: 'set_variables',\r\n          steps: [\r\n            {\r\n              id: 'set_variables',\r\n              type: 'variable',\r\n              config: {\r\n                variables: {\r\n                  delayTime: 1000, // 1 second for testing\r\n                  notificationMessage: 'Delayed alert: {{input.alert.message}}',\r\n                },\r\n              },\r\n              nextStep: 'wait_step',\r\n            },\r\n            {\r\n              id: 'wait_step',\r\n              type: 'delay',\r\n              config: {\r\n                delay: '{{variables.delayTime}}',\r\n              },\r\n              nextStep: 'send_notification',\r\n            },\r\n            {\r\n              id: 'send_notification',\r\n              type: 'notification',\r\n              config: {\r\n                channel: 'email',\r\n                message: '{{variables.notificationMessage}}',\r\n                recipients: ['<EMAIL>'],\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        status: 'active',\r\n      };\r\n\r\n      const workflow = await workflowService.createWorkflow(workflowData, testUser);\r\n\r\n      const executionData = {\r\n        input: {\r\n          alert: {\r\n            message: 'System maintenance required',\r\n            severity: 'medium',\r\n          },\r\n          user: testUser,\r\n        },\r\n      };\r\n\r\n      const startTime = Date.now();\r\n      const execution = await workflowService.executeWorkflow(workflow.id, executionData, testUser);\r\n\r\n      // Wait for execution to complete\r\n      await new Promise(resolve => setTimeout(resolve, 4000));\r\n\r\n      const completedExecution = await executionRepository.findOne({\r\n        where: { id: execution.executionId },\r\n        relations: ['contexts'],\r\n      });\r\n\r\n      const endTime = Date.now();\r\n      const executionTime = endTime - startTime;\r\n\r\n      expect(completedExecution.status).toBe('completed');\r\n      expect(completedExecution.stepsCompleted).toBe(3);\r\n      expect(executionTime).toBeGreaterThan(1000); // Should take at least 1 second due to delay\r\n\r\n      // Verify all steps were executed in order\r\n      const stepContexts = completedExecution.contexts.sort(\r\n        (a, b) => a.startedAt.getTime() - b.startedAt.getTime()\r\n      );\r\n\r\n      expect(stepContexts[0].stepId).toBe('set_variables');\r\n      expect(stepContexts[1].stepId).toBe('wait_step');\r\n      expect(stepContexts[2].stepId).toBe('send_notification');\r\n\r\n      // Verify variables were set correctly\r\n      const variableStep = stepContexts[0];\r\n      expect(variableStep.result.variables).toEqual({\r\n        delayTime: 1000,\r\n        notificationMessage: 'Delayed alert: System maintenance required',\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Error Handling and Recovery', () => {\r\n    it('should handle step failures with retry mechanism', async () => {\r\n      // Mock a failing notification provider\r\n      const mockProvider = {\r\n        sendNotification: jest.fn()\r\n          .mockRejectedValueOnce(new Error('Network timeout'))\r\n          .mockRejectedValueOnce(new Error('Service unavailable'))\r\n          .mockResolvedValueOnce({ success: true, messageId: 'retry-success' }),\r\n        validateConfiguration: jest.fn().mockReturnValue(true),\r\n        getHealthStatus: jest.fn().mockResolvedValue({ healthy: false }),\r\n      };\r\n\r\n      // Create workflow with retry configuration\r\n      const workflowData = {\r\n        name: 'Retry Test Workflow',\r\n        description: 'Test retry mechanism',\r\n        definition: {\r\n          startStep: 'send_notification',\r\n          steps: [\r\n            {\r\n              id: 'send_notification',\r\n              type: 'notification',\r\n              config: {\r\n                channel: 'email',\r\n                message: 'Test retry message',\r\n                recipients: ['<EMAIL>'],\r\n              },\r\n              errorHandling: {\r\n                retry: {\r\n                  maxAttempts: 3,\r\n                  delay: 500,\r\n                },\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        status: 'active',\r\n      };\r\n\r\n      const workflow = await workflowService.createWorkflow(workflowData, testUser);\r\n\r\n      const executionData = {\r\n        input: {\r\n          alert: { message: 'Retry test', severity: 'medium' },\r\n          user: testUser,\r\n        },\r\n      };\r\n\r\n      const execution = await workflowService.executeWorkflow(workflow.id, executionData, testUser);\r\n\r\n      // Wait for execution with retries\r\n      await new Promise(resolve => setTimeout(resolve, 5000));\r\n\r\n      const completedExecution = await executionRepository.findOne({\r\n        where: { id: execution.executionId },\r\n        relations: ['contexts'],\r\n      });\r\n\r\n      expect(completedExecution.status).toBe('completed');\r\n      expect(completedExecution.stepsCompleted).toBe(1);\r\n\r\n      // Verify retry attempts were made\r\n      const stepContext = completedExecution.contexts[0];\r\n      expect(stepContext.retryCount).toBe(2); // Failed twice, succeeded on third attempt\r\n      expect(stepContext.status).toBe('completed');\r\n    });\r\n\r\n    it('should execute fallback step when main step fails', async () => {\r\n      const workflowData = {\r\n        name: 'Fallback Test Workflow',\r\n        description: 'Test fallback mechanism',\r\n        definition: {\r\n          startStep: 'primary_notification',\r\n          steps: [\r\n            {\r\n              id: 'primary_notification',\r\n              type: 'notification',\r\n              config: {\r\n                channel: 'invalid_channel', // This will fail\r\n                message: 'Primary notification',\r\n                recipients: ['<EMAIL>'],\r\n              },\r\n              errorHandling: {\r\n                fallbackStep: 'fallback_notification',\r\n              },\r\n            },\r\n            {\r\n              id: 'fallback_notification',\r\n              type: 'notification',\r\n              config: {\r\n                channel: 'email',\r\n                message: 'Fallback notification: {{input.alert.message}}',\r\n                recipients: ['<EMAIL>'],\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        status: 'active',\r\n      };\r\n\r\n      const workflow = await workflowService.createWorkflow(workflowData, testUser);\r\n\r\n      const executionData = {\r\n        input: {\r\n          alert: { message: 'Fallback test', severity: 'medium' },\r\n          user: testUser,\r\n        },\r\n      };\r\n\r\n      const execution = await workflowService.executeWorkflow(workflow.id, executionData, testUser);\r\n\r\n      // Wait for execution\r\n      await new Promise(resolve => setTimeout(resolve, 3000));\r\n\r\n      const completedExecution = await executionRepository.findOne({\r\n        where: { id: execution.executionId },\r\n        relations: ['contexts'],\r\n      });\r\n\r\n      expect(completedExecution.status).toBe('completed');\r\n      expect(completedExecution.stepsCompleted).toBe(2); // Primary failed, fallback succeeded\r\n\r\n      // Verify primary step failed\r\n      const primaryContext = completedExecution.contexts.find(\r\n        ctx => ctx.stepId === 'primary_notification'\r\n      );\r\n      expect(primaryContext.status).toBe('failed');\r\n\r\n      // Verify fallback step succeeded\r\n      const fallbackContext = completedExecution.contexts.find(\r\n        ctx => ctx.stepId === 'fallback_notification'\r\n      );\r\n      expect(fallbackContext.status).toBe('completed');\r\n    });\r\n  });\r\n\r\n  describe('Performance and Load Testing', () => {\r\n    it('should handle concurrent workflow executions', async () => {\r\n      const workflowData = {\r\n        name: 'Concurrent Test Workflow',\r\n        description: 'Test concurrent execution',\r\n        definition: {\r\n          startStep: 'send_notification',\r\n          steps: [\r\n            {\r\n              id: 'send_notification',\r\n              type: 'notification',\r\n              config: {\r\n                channel: 'email',\r\n                message: 'Concurrent test: {{input.alert.id}}',\r\n                recipients: ['<EMAIL>'],\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        status: 'active',\r\n      };\r\n\r\n      const workflow = await workflowService.createWorkflow(workflowData, testUser);\r\n\r\n      // Execute multiple workflows concurrently\r\n      const concurrentExecutions = 10;\r\n      const executionPromises = [];\r\n\r\n      for (let i = 0; i < concurrentExecutions; i++) {\r\n        const executionData = {\r\n          input: {\r\n            alert: { id: `alert-${i}`, message: `Concurrent test ${i}`, severity: 'medium' },\r\n            user: testUser,\r\n          },\r\n        };\r\n\r\n        executionPromises.push(\r\n          workflowService.executeWorkflow(workflow.id, executionData, testUser)\r\n        );\r\n      }\r\n\r\n      const executions = await Promise.all(executionPromises);\r\n      expect(executions).toHaveLength(concurrentExecutions);\r\n\r\n      // Wait for all executions to complete\r\n      await new Promise(resolve => setTimeout(resolve, 5000));\r\n\r\n      // Verify all executions completed successfully\r\n      const completedExecutions = await executionRepository.find({\r\n        where: { workflowId: workflow.id },\r\n      });\r\n\r\n      expect(completedExecutions).toHaveLength(concurrentExecutions);\r\n      completedExecutions.forEach(execution => {\r\n        expect(execution.status).toBe('completed');\r\n        expect(execution.stepsCompleted).toBe(1);\r\n      });\r\n    });\r\n\r\n    it('should measure workflow execution performance', async () => {\r\n      const workflowData = {\r\n        name: 'Performance Test Workflow',\r\n        description: 'Measure execution performance',\r\n        definition: {\r\n          startStep: 'step1',\r\n          steps: [\r\n            { id: 'step1', type: 'variable', config: { variables: { test: 'value1' } }, nextStep: 'step2' },\r\n            { id: 'step2', type: 'condition', config: { condition: { field: 'variables.test', operator: 'eq', value: 'value1' } }, nextStep: 'step3' },\r\n            { id: 'step3', type: 'notification', config: { channel: 'email', message: 'Performance test', recipients: ['<EMAIL>'] } },\r\n          ],\r\n        },\r\n        status: 'active',\r\n      };\r\n\r\n      const workflow = await workflowService.createWorkflow(workflowData, testUser);\r\n\r\n      const executionData = {\r\n        input: {\r\n          alert: { message: 'Performance test', severity: 'medium' },\r\n          user: testUser,\r\n        },\r\n      };\r\n\r\n      const startTime = Date.now();\r\n      const execution = await workflowService.executeWorkflow(workflow.id, executionData, testUser);\r\n\r\n      // Wait for execution\r\n      await new Promise(resolve => setTimeout(resolve, 3000));\r\n\r\n      const completedExecution = await executionRepository.findOne({\r\n        where: { id: execution.executionId },\r\n        relations: ['contexts'],\r\n      });\r\n\r\n      const totalExecutionTime = Date.now() - startTime;\r\n\r\n      expect(completedExecution.status).toBe('completed');\r\n      expect(completedExecution.duration).toBeDefined();\r\n      expect(completedExecution.duration).toBeGreaterThan(0);\r\n      expect(totalExecutionTime).toBeLessThan(10000); // Should complete within 10 seconds\r\n\r\n      // Verify step-level performance metrics\r\n      completedExecution.contexts.forEach(context => {\r\n        expect(context.duration).toBeDefined();\r\n        expect(context.duration).toBeGreaterThan(0);\r\n        expect(context.startedAt).toBeDefined();\r\n        expect(context.completedAt).toBeDefined();\r\n      });\r\n    });\r\n  });\r\n});\r\n"], "version": 3}