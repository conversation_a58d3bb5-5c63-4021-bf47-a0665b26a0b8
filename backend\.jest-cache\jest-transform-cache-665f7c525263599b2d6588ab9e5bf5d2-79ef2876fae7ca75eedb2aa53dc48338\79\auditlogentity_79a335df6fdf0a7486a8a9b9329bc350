08abe850650db63bbb79bf1364902baa
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditLog = void 0;
const typeorm_1 = require("typeorm");
/**
 * Audit Log entity
 * Represents comprehensive audit trail for all system activities
 */
let AuditLog = class AuditLog {
    /**
     * Check if audit log is expired
     */
    get isExpired() {
        if (this.legalHold)
            return false;
        const expirationDate = new Date(this.timestamp);
        expirationDate.setDate(expirationDate.getDate() + this.retentionDays);
        return new Date() > expirationDate;
    }
    /**
     * Get audit log age in days
     */
    get ageInDays() {
        const now = new Date();
        const diffMs = now.getTime() - this.timestamp.getTime();
        return Math.floor(diffMs / (1000 * 60 * 60 * 24));
    }
    /**
     * Check if audit log is high risk
     */
    get isHighRisk() {
        return this.severity === 'critical' ||
            this.details.security?.anomalyDetected ||
            this.details.compliance?.policyViolation ||
            this.action === 'delete' && this.containsSensitiveData;
    }
    /**
     * Get formatted timestamp
     */
    get formattedTimestamp() {
        return this.timestamp.toISOString();
    }
    /**
     * Add tag to audit log
     */
    addTag(tag) {
        if (!this.tags.includes(tag)) {
            this.tags.push(tag);
        }
    }
    /**
     * Remove tag from audit log
     */
    removeTag(tag) {
        this.tags = this.tags.filter(t => t !== tag);
    }
    /**
     * Mark as compliance relevant
     */
    markAsComplianceRelevant(frameworks, controls) {
        this.complianceRelevant = true;
        if (!this.details.compliance) {
            this.details.compliance = {};
        }
        if (frameworks) {
            this.details.compliance.frameworks = frameworks;
        }
        if (controls) {
            this.details.compliance.controls = controls;
        }
        this.addTag('compliance');
    }
    /**
     * Set legal hold
     */
    setLegalHold(reason) {
        this.legalHold = true;
        if (!this.details.metadata) {
            this.details.metadata = {};
        }
        this.details.metadata.legalHoldReason = reason;
        this.details.metadata.legalHoldDate = new Date().toISOString();
        this.addTag('legal_hold');
    }
    /**
     * Release legal hold
     */
    releaseLegalHold(releasedBy, reason) {
        this.legalHold = false;
        if (!this.details.metadata) {
            this.details.metadata = {};
        }
        this.details.metadata.legalHoldReleased = true;
        this.details.metadata.legalHoldReleasedBy = releasedBy;
        this.details.metadata.legalHoldReleaseDate = new Date().toISOString();
        this.details.metadata.legalHoldReleaseReason = reason;
        this.removeTag('legal_hold');
    }
    /**
     * Calculate integrity hash
     */
    calculateIntegrityHash() {
        const data = {
            userId: this.userId,
            action: this.action,
            resourceType: this.resourceType,
            resourceId: this.resourceId,
            description: this.description,
            timestamp: this.timestamp.toISOString(),
            details: this.details,
        };
        // In a real implementation, use a proper cryptographic hash
        const dataString = JSON.stringify(data);
        return `sha256-${Buffer.from(dataString).toString('base64').slice(0, 32)}`;
    }
    /**
     * Verify integrity
     */
    verifyIntegrity() {
        if (!this.integrityHash)
            return false;
        return this.integrityHash === this.calculateIntegrityHash();
    }
    /**
     * Sanitize sensitive data for display
     */
    sanitizeForDisplay() {
        const sanitized = { ...this };
        if (this.containsSensitiveData) {
            // Remove or mask sensitive fields
            if (sanitized.details.request?.body) {
                sanitized.details.request.body = '[REDACTED]';
            }
            if (sanitized.details.changes?.before) {
                sanitized.details.changes.before = '[REDACTED]';
            }
            if (sanitized.details.changes?.after) {
                sanitized.details.changes.after = '[REDACTED]';
            }
        }
        return sanitized;
    }
    /**
     * Export audit log for compliance reporting
     */
    exportForCompliance() {
        return {
            id: this.id,
            timestamp: this.formattedTimestamp,
            userId: this.userId,
            action: this.action,
            resourceType: this.resourceType,
            resourceId: this.resourceId,
            description: this.description,
            severity: this.severity,
            complianceRelevant: this.complianceRelevant,
            complianceFrameworks: this.details.compliance?.frameworks || [],
            complianceControls: this.details.compliance?.controls || [],
            policyViolation: this.details.compliance?.policyViolation || false,
            dataClassification: this.details.compliance?.dataClassification,
            legalHold: this.legalHold,
            integrityHash: this.integrityHash,
            tags: this.tags,
        };
    }
    /**
     * Check if audit log matches search criteria
     */
    matchesSearchCriteria(criteria) {
        // Check user
        if (criteria.userId && this.userId !== criteria.userId) {
            return false;
        }
        // Check actions
        if (criteria.actions && !criteria.actions.includes(this.action)) {
            return false;
        }
        // Check resource types
        if (criteria.resourceTypes && !criteria.resourceTypes.includes(this.resourceType)) {
            return false;
        }
        // Check severity
        if (criteria.severity && !criteria.severity.includes(this.severity)) {
            return false;
        }
        // Check date range
        if (criteria.dateRange) {
            if (this.timestamp < criteria.dateRange.start || this.timestamp > criteria.dateRange.end) {
                return false;
            }
        }
        // Check tags
        if (criteria.tags && !criteria.tags.some(tag => this.tags.includes(tag))) {
            return false;
        }
        // Check compliance relevance
        if (criteria.complianceRelevant !== undefined && this.complianceRelevant !== criteria.complianceRelevant) {
            return false;
        }
        // Check sensitive data
        if (criteria.containsSensitiveData !== undefined && this.containsSensitiveData !== criteria.containsSensitiveData) {
            return false;
        }
        // Check search text
        if (criteria.searchText) {
            const searchText = criteria.searchText.toLowerCase();
            const searchableText = `${this.description} ${this.resourceType} ${this.action}`.toLowerCase();
            if (!searchableText.includes(searchText)) {
                return false;
            }
        }
        return true;
    }
    /**
     * Create audit log for user action
     */
    static createUserAction(userId, action, resourceType, resourceId, description, details) {
        return {
            userId,
            action,
            resourceType,
            resourceId,
            description,
            details: details || {},
            severity: 'info',
            complianceRelevant: false,
            containsSensitiveData: false,
            tags: ['user_action'],
        };
    }
    /**
     * Create audit log for system action
     */
    static createSystemAction(action, resourceType, resourceId, description, details) {
        return {
            userId: null,
            action,
            resourceType,
            resourceId,
            description,
            details: details || {},
            severity: 'info',
            complianceRelevant: false,
            containsSensitiveData: false,
            tags: ['system_action'],
        };
    }
    /**
     * Create audit log for security event
     */
    static createSecurityEvent(userId, action, description, severity, details) {
        return {
            userId,
            action,
            resourceType: 'security',
            description,
            details: details || {},
            severity,
            complianceRelevant: true,
            containsSensitiveData: true,
            tags: ['security', 'compliance'],
        };
    }
};
exports.AuditLog = AuditLog;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], AuditLog.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], AuditLog.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'session_id', nullable: true }),
    __metadata("design:type", String)
], AuditLog.prototype, "sessionId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: [
            'create',
            'read',
            'update',
            'delete',
            'login',
            'logout',
            'access',
            'export',
            'import',
            'approve',
            'reject',
            'escalate',
            'assign',
            'execute',
            'configure',
            'backup',
            'restore',
            'other',
        ],
    }),
    __metadata("design:type", String)
], AuditLog.prototype, "action", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'resource_type' }),
    __metadata("design:type", String)
], AuditLog.prototype, "resourceType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'resource_id', nullable: true }),
    __metadata("design:type", String)
], AuditLog.prototype, "resourceId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], AuditLog.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    __metadata("design:type", Object)
], AuditLog.prototype, "details", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['info', 'warning', 'error', 'critical'],
        default: 'info',
    }),
    __metadata("design:type", String)
], AuditLog.prototype, "severity", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'compliance_relevant', default: false }),
    __metadata("design:type", Boolean)
], AuditLog.prototype, "complianceRelevant", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'contains_sensitive_data', default: false }),
    __metadata("design:type", Boolean)
], AuditLog.prototype, "containsSensitiveData", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'retention_days', type: 'integer', default: 2555 }) // 7 years default
    ,
    __metadata("design:type", Number)
], AuditLog.prototype, "retentionDays", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'legal_hold', default: false }),
    __metadata("design:type", Boolean)
], AuditLog.prototype, "legalHold", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'integrity_hash', nullable: true }),
    __metadata("design:type", String)
], AuditLog.prototype, "integrityHash", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'previous_hash', nullable: true }),
    __metadata("design:type", String)
], AuditLog.prototype, "previousHash", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, default: '{}' }),
    __metadata("design:type", Array)
], AuditLog.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'timestamp' }),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], AuditLog.prototype, "timestamp", void 0);
exports.AuditLog = AuditLog = __decorate([
    (0, typeorm_1.Entity)('audit_logs'),
    (0, typeorm_1.Index)(['userId']),
    (0, typeorm_1.Index)(['action']),
    (0, typeorm_1.Index)(['resourceType']),
    (0, typeorm_1.Index)(['resourceId']),
    (0, typeorm_1.Index)(['timestamp']),
    (0, typeorm_1.Index)(['severity']),
    (0, typeorm_1.Index)(['complianceRelevant']),
    (0, typeorm_1.Index)(['sessionId'])
], AuditLog);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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