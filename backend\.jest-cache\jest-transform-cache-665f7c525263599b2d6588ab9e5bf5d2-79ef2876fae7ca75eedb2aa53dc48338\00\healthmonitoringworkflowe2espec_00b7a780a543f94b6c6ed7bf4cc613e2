8a54e3f0a10edc810aacd87a66d8adee
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const supertest_1 = __importDefault(require("supertest"));
const app_module_1 = require("../../app.module");
describe('Health Monitoring Workflow (e2e)', () => {
    let app;
    beforeAll(async () => {
        const moduleFixture = await testing_1.Test.createTestingModule({
            imports: [
                config_1.ConfigModule.forRoot({
                    isGlobal: true,
                    envFilePath: '.env.test',
                }),
                app_module_1.AppModule,
            ],
        }).compile();
        app = moduleFixture.createNestApplication();
        await app.init();
    });
    afterAll(async () => {
        await app.close();
    });
    describe('Basic Health Checks', () => {
        it('should return basic health status', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/health')
                .expect(200);
            expect(response.body).toEqual({
                status: 'ok',
                timestamp: expect.any(String),
                uptime: expect.any(Number),
                version: expect.any(String),
                environment: 'test',
            });
            // Validate timestamp format
            expect(new Date(response.body.timestamp).toISOString()).toBe(response.body.timestamp);
            // Validate uptime is positive
            expect(response.body.uptime).toBeGreaterThan(0);
        });
        it('should return health status with proper headers', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/health')
                .expect(200);
            expect(response.headers['content-type']).toMatch(/application\/json/);
            expect(response.headers['cache-control']).toBe('no-cache, no-store, must-revalidate');
        });
        it('should handle health check requests quickly', async () => {
            const startTime = Date.now();
            await (0, supertest_1.default)(app.getHttpServer())
                .get('/health')
                .expect(200);
            const endTime = Date.now();
            const duration = endTime - startTime;
            // Health checks should be fast
            expect(duration).toBeLessThan(500);
        });
    });
    describe('Detailed Health Checks', () => {
        it('should return detailed health information', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/health/detailed')
                .expect(200);
            expect(response.body).toEqual({
                status: 'ok',
                timestamp: expect.any(String),
                uptime: expect.any(Number),
                version: expect.any(String),
                environment: 'test',
                services: {
                    database: expect.any(String),
                    redis: expect.any(String),
                    external_apis: expect.any(String),
                },
                memory: {
                    used: expect.any(Number),
                    total: expect.any(Number),
                    percentage: expect.any(Number),
                },
                cpu: {
                    usage: expect.any(Number),
                    loadAverage: expect.any(Array),
                },
                disk: {
                    used: expect.any(Number),
                    total: expect.any(Number),
                    percentage: expect.any(Number),
                },
            });
            // Validate service statuses
            expect(['healthy', 'degraded', 'unhealthy']).toContain(response.body.services.database);
            expect(['healthy', 'degraded', 'unhealthy']).toContain(response.body.services.redis);
            expect(['healthy', 'degraded', 'unhealthy']).toContain(response.body.services.external_apis);
            // Validate memory metrics
            expect(response.body.memory.used).toBeGreaterThan(0);
            expect(response.body.memory.total).toBeGreaterThan(response.body.memory.used);
            expect(response.body.memory.percentage).toBeGreaterThanOrEqual(0);
            expect(response.body.memory.percentage).toBeLessThanOrEqual(100);
            // Validate CPU metrics
            expect(response.body.cpu.usage).toBeGreaterThanOrEqual(0);
            expect(response.body.cpu.usage).toBeLessThanOrEqual(100);
            expect(Array.isArray(response.body.cpu.loadAverage)).toBe(true);
        });
        it('should include dependency health checks', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/health/dependencies')
                .expect(200);
            expect(response.body).toEqual({
                status: expect.any(String),
                timestamp: expect.any(String),
                dependencies: {
                    database: {
                        status: expect.any(String),
                        responseTime: expect.any(Number),
                        lastChecked: expect.any(String),
                        details: expect.any(Object),
                    },
                    redis: {
                        status: expect.any(String),
                        responseTime: expect.any(Number),
                        lastChecked: expect.any(String),
                        details: expect.any(Object),
                    },
                    ai_service: {
                        status: expect.any(String),
                        responseTime: expect.any(Number),
                        lastChecked: expect.any(String),
                        details: expect.any(Object),
                    },
                    external_threat_intel: {
                        status: expect.any(String),
                        responseTime: expect.any(Number),
                        lastChecked: expect.any(String),
                        details: expect.any(Object),
                    },
                },
            });
            // Validate dependency statuses
            Object.values(response.body.dependencies).forEach((dep) => {
                expect(['healthy', 'degraded', 'unhealthy']).toContain(dep.status);
                expect(dep.responseTime).toBeGreaterThanOrEqual(0);
                expect(new Date(dep.lastChecked).toISOString()).toBe(dep.lastChecked);
            });
        });
    });
    describe('Readiness Checks', () => {
        it('should return readiness status', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/health/ready')
                .expect(200);
            expect(response.body).toEqual({
                status: 'ready',
                timestamp: expect.any(String),
                checks: {
                    database_connection: expect.any(Boolean),
                    redis_connection: expect.any(Boolean),
                    configuration_loaded: expect.any(Boolean),
                    migrations_applied: expect.any(Boolean),
                },
            });
            // All readiness checks should pass in test environment
            expect(response.body.checks.database_connection).toBe(true);
            expect(response.body.checks.redis_connection).toBe(true);
            expect(response.body.checks.configuration_loaded).toBe(true);
            expect(response.body.checks.migrations_applied).toBe(true);
        });
        it('should handle readiness check failures gracefully', async () => {
            // This test would require mocking service failures
            // For now, we'll test that the endpoint responds appropriately
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/health/ready');
            expect([200, 503]).toContain(response.status);
            if (response.status === 503) {
                expect(response.body.status).toBe('not_ready');
                expect(response.body.checks).toBeDefined();
            }
        });
    });
    describe('Liveness Checks', () => {
        it('should return liveness status', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/health/live')
                .expect(200);
            expect(response.body).toEqual({
                status: 'alive',
                timestamp: expect.any(String),
                uptime: expect.any(Number),
                pid: expect.any(Number),
                memory: {
                    heapUsed: expect.any(Number),
                    heapTotal: expect.any(Number),
                    external: expect.any(Number),
                    rss: expect.any(Number),
                },
            });
            expect(response.body.uptime).toBeGreaterThan(0);
            expect(response.body.pid).toBeGreaterThan(0);
            expect(response.body.memory.heapUsed).toBeGreaterThan(0);
            expect(response.body.memory.heapTotal).toBeGreaterThan(response.body.memory.heapUsed);
        });
        it('should respond quickly to liveness checks', async () => {
            const startTime = Date.now();
            await (0, supertest_1.default)(app.getHttpServer())
                .get('/health/live')
                .expect(200);
            const endTime = Date.now();
            const duration = endTime - startTime;
            // Liveness checks should be very fast
            expect(duration).toBeLessThan(100);
        });
    });
    describe('Metrics Endpoint', () => {
        it('should return application metrics', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/metrics')
                .expect(200);
            expect(response.body).toEqual({
                timestamp: expect.any(String),
                application: {
                    uptime: expect.any(Number),
                    version: expect.any(String),
                    environment: 'test',
                    requests: {
                        total: expect.any(Number),
                        rate: expect.any(Number),
                        errors: expect.any(Number),
                        errorRate: expect.any(Number),
                    },
                },
                system: {
                    memory: {
                        used: expect.any(Number),
                        total: expect.any(Number),
                        percentage: expect.any(Number),
                    },
                    cpu: {
                        usage: expect.any(Number),
                        loadAverage: expect.any(Array),
                    },
                    disk: {
                        used: expect.any(Number),
                        total: expect.any(Number),
                        percentage: expect.any(Number),
                    },
                },
                business: {
                    active_users: expect.any(Number),
                    vulnerabilities_detected: expect.any(Number),
                    threats_analyzed: expect.any(Number),
                    security_events: expect.any(Number),
                },
            });
            // Validate metrics ranges
            expect(response.body.application.requests.total).toBeGreaterThanOrEqual(0);
            expect(response.body.application.requests.rate).toBeGreaterThanOrEqual(0);
            expect(response.body.application.requests.errors).toBeGreaterThanOrEqual(0);
            expect(response.body.application.requests.errorRate).toBeGreaterThanOrEqual(0);
            expect(response.body.application.requests.errorRate).toBeLessThanOrEqual(100);
        });
        it('should return Prometheus-formatted metrics', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/metrics/prometheus')
                .set('Accept', 'text/plain')
                .expect(200);
            expect(response.headers['content-type']).toMatch(/text\/plain/);
            expect(response.text).toContain('# HELP');
            expect(response.text).toContain('# TYPE');
            expect(response.text).toContain('http_requests_total');
            expect(response.text).toContain('process_cpu_seconds_total');
            expect(response.text).toContain('nodejs_heap_size_used_bytes');
        });
    });
    describe('Performance Monitoring', () => {
        it('should track response times', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/metrics/performance')
                .expect(200);
            expect(response.body).toEqual({
                timestamp: expect.any(String),
                responseTime: {
                    p50: expect.any(Number),
                    p95: expect.any(Number),
                    p99: expect.any(Number),
                    average: expect.any(Number),
                    min: expect.any(Number),
                    max: expect.any(Number),
                },
                throughput: {
                    requestsPerSecond: expect.any(Number),
                    requestsPerMinute: expect.any(Number),
                },
                errors: {
                    rate: expect.any(Number),
                    count: expect.any(Number),
                    types: expect.any(Object),
                },
            });
            // Validate performance metrics
            expect(response.body.responseTime.p50).toBeGreaterThan(0);
            expect(response.body.responseTime.p95).toBeGreaterThanOrEqual(response.body.responseTime.p50);
            expect(response.body.responseTime.p99).toBeGreaterThanOrEqual(response.body.responseTime.p95);
            expect(response.body.throughput.requestsPerSecond).toBeGreaterThanOrEqual(0);
        });
        it('should monitor memory usage over time', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/metrics/memory')
                .expect(200);
            expect(response.body).toEqual({
                timestamp: expect.any(String),
                current: {
                    heapUsed: expect.any(Number),
                    heapTotal: expect.any(Number),
                    external: expect.any(Number),
                    rss: expect.any(Number),
                },
                history: expect.any(Array),
                trends: {
                    heapGrowthRate: expect.any(Number),
                    memoryLeakDetected: expect.any(Boolean),
                },
            });
            // Validate memory metrics
            expect(response.body.current.heapUsed).toBeGreaterThan(0);
            expect(response.body.current.heapTotal).toBeGreaterThan(response.body.current.heapUsed);
            expect(response.body.history.length).toBeGreaterThanOrEqual(0);
        });
    });
    describe('Custom Health Indicators', () => {
        it('should check database health', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/health/database')
                .expect(200);
            expect(response.body).toEqual({
                status: expect.any(String),
                timestamp: expect.any(String),
                database: {
                    connection: expect.any(String),
                    responseTime: expect.any(Number),
                    activeConnections: expect.any(Number),
                    maxConnections: expect.any(Number),
                    queries: {
                        total: expect.any(Number),
                        slow: expect.any(Number),
                        failed: expect.any(Number),
                    },
                },
            });
            expect(['healthy', 'degraded', 'unhealthy']).toContain(response.body.status);
            expect(['connected', 'disconnected', 'connecting']).toContain(response.body.database.connection);
            expect(response.body.database.responseTime).toBeGreaterThan(0);
            expect(response.body.database.activeConnections).toBeGreaterThanOrEqual(0);
            expect(response.body.database.maxConnections).toBeGreaterThan(0);
        });
        it('should check Redis health', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/health/redis')
                .expect(200);
            expect(response.body).toEqual({
                status: expect.any(String),
                timestamp: expect.any(String),
                redis: {
                    connection: expect.any(String),
                    responseTime: expect.any(Number),
                    memory: {
                        used: expect.any(Number),
                        peak: expect.any(Number),
                    },
                    keyspace: {
                        keys: expect.any(Number),
                        expires: expect.any(Number),
                    },
                },
            });
            expect(['healthy', 'degraded', 'unhealthy']).toContain(response.body.status);
            expect(['connected', 'disconnected', 'connecting']).toContain(response.body.redis.connection);
        });
        it('should check external API health', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/health/external-apis')
                .expect(200);
            expect(response.body).toEqual({
                status: expect.any(String),
                timestamp: expect.any(String),
                apis: {
                    threat_intelligence: {
                        status: expect.any(String),
                        responseTime: expect.any(Number),
                        lastChecked: expect.any(String),
                        errorRate: expect.any(Number),
                    },
                    vulnerability_scanner: {
                        status: expect.any(String),
                        responseTime: expect.any(Number),
                        lastChecked: expect.any(String),
                        errorRate: expect.any(Number),
                    },
                    ai_service: {
                        status: expect.any(String),
                        responseTime: expect.any(Number),
                        lastChecked: expect.any(String),
                        errorRate: expect.any(Number),
                    },
                },
            });
            Object.values(response.body.apis).forEach((api) => {
                expect(['healthy', 'degraded', 'unhealthy']).toContain(api.status);
                expect(api.responseTime).toBeGreaterThanOrEqual(0);
                expect(api.errorRate).toBeGreaterThanOrEqual(0);
                expect(api.errorRate).toBeLessThanOrEqual(100);
            });
        });
    });
    describe('Health Check Aggregation', () => {
        it('should aggregate all health checks', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/health/aggregate')
                .expect(200);
            expect(response.body).toEqual({
                status: expect.any(String),
                timestamp: expect.any(String),
                overall: {
                    healthy: expect.any(Number),
                    degraded: expect.any(Number),
                    unhealthy: expect.any(Number),
                    total: expect.any(Number),
                },
                components: {
                    application: expect.any(String),
                    database: expect.any(String),
                    redis: expect.any(String),
                    external_apis: expect.any(String),
                    system: expect.any(String),
                },
                summary: expect.any(String),
            });
            expect(['healthy', 'degraded', 'unhealthy']).toContain(response.body.status);
            expect(response.body.overall.total).toBe(response.body.overall.healthy +
                response.body.overall.degraded +
                response.body.overall.unhealthy);
        });
        it('should determine overall status correctly', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/health/aggregate')
                .expect(200);
            // If any component is unhealthy, overall should be unhealthy
            // If any component is degraded (and none unhealthy), overall should be degraded
            // If all components are healthy, overall should be healthy
            const componentStatuses = Object.values(response.body.components);
            const hasUnhealthy = componentStatuses.includes('unhealthy');
            const hasDegraded = componentStatuses.includes('degraded');
            if (hasUnhealthy) {
                expect(response.body.status).toBe('unhealthy');
            }
            else if (hasDegraded) {
                expect(response.body.status).toBe('degraded');
            }
            else {
                expect(response.body.status).toBe('healthy');
            }
        });
    });
    describe('Health Check Caching', () => {
        it('should cache health check results appropriately', async () => {
            // Make multiple requests quickly
            const responses = await Promise.all([
                (0, supertest_1.default)(app.getHttpServer()).get('/health/detailed'),
                (0, supertest_1.default)(app.getHttpServer()).get('/health/detailed'),
                (0, supertest_1.default)(app.getHttpServer()).get('/health/detailed'),
            ]);
            responses.forEach(response => {
                expect(response.status).toBe(200);
            });
            // Timestamps should be very close (indicating caching)
            const timestamps = responses.map(r => new Date(r.body.timestamp).getTime());
            const maxDiff = Math.max(...timestamps) - Math.min(...timestamps);
            expect(maxDiff).toBeLessThan(1000); // Within 1 second
        });
    });
    describe('Error Scenarios', () => {
        it('should handle health check timeouts gracefully', async () => {
            // This would require mocking slow dependencies
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/health/dependencies')
                .timeout(5000);
            expect([200, 503]).toContain(response.status);
        });
        it('should provide meaningful error messages', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/health/nonexistent')
                .expect(404);
            expect(response.body.message).toContain('not found');
        });
    });
    describe('Concurrent Health Checks', () => {
        it('should handle concurrent health check requests', async () => {
            const requests = Array.from({ length: 10 }, () => (0, supertest_1.default)(app.getHttpServer()).get('/health'));
            const responses = await Promise.all(requests);
            responses.forEach(response => {
                expect(response.status).toBe(200);
                expect(response.body.status).toBe('ok');
            });
        });
        it('should maintain performance under load', async () => {
            const startTime = Date.now();
            const requests = Array.from({ length: 20 }, () => (0, supertest_1.default)(app.getHttpServer()).get('/health/detailed'));
            await Promise.all(requests);
            const endTime = Date.now();
            const duration = endTime - startTime;
            // Should handle concurrent requests efficiently
            expect(duration).toBeLessThan(3000);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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