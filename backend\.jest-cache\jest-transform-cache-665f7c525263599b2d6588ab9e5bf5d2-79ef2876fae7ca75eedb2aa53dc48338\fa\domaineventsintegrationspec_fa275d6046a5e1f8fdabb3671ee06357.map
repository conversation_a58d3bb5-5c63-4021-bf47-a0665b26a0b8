{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\__tests__\\integration\\domain-events.integration.spec.ts", "mappings": ";;AAAA,6CAAsD;AAKtD,wEAAoE;AACpE,0EAAsE;AACtE,wFAAoF;AACpF,4FAAuF;AACvF,uHAAsG;AACtG,yHAAwG;AACxG,mHAAkG;AAClG,wGAAuF;AACvF,4FAA4E;AAC5E,kHAAiG;AACjG,wEAA+D;AAC/D,gFAAuE;AACvE,4EAAmE;AACnE,sFAA4E;AAC5E,kFAAyE;AACzE,gGAAuF;AACvF,0EAAiE;AACjE,8EAAqE;AACrE,2GAA0F;AAC1F,uGAAsF;AAGtF;;GAEG;AACH,MAAM,sBAAsB;IAA5B;QACU,kBAAa,GAAsB,EAAE,CAAC;IAiBhD,CAAC;IAfC,MAAM,CAAC,KAAsB;QAC3B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IAED,gBAAgB;QACd,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAED,eAAe,CAAC,SAAiB;QAC/B,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;IAClF,CAAC;IAED,KAAK;QACH,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;IAC1B,CAAC;CACF;AAED,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;IAC/C,IAAI,MAAqB,CAAC;IAC1B,IAAI,YAAoC,CAAC;IAEzC,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,YAAY,GAAG,IAAI,sBAAsB,EAAE,CAAC;QAE5C,MAAM,GAAG,MAAM,cAAI,CAAC,mBAAmB,CAAC;YACtC,SAAS,EAAE;gBACT,EAAE,OAAO,EAAE,oBAAoB,EAAE,QAAQ,EAAE,YAAY,EAAE;aAC1D;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;IACf,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,iEAAiE,EAAE,GAAG,EAAE;YACzE,gBAAgB;YAChB,MAAM,KAAK,GAAG,4BAAY,CAAC,MAAM,CAAC;gBAChC,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBAClE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,SAAS;oBACrB,IAAI,EAAE,UAAU;iBACjB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,sBAAsB;gBACtC,QAAQ,EAAE,mCAAa,CAAC,GAAG;gBAC3B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE;gBACjC,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,YAAY,GAAG,KAAK,CAAC,oBAAoB,EAAE,CAAC;YAClD,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAE/C,MAAM,kBAAkB,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,KAAK,yBAAyB,CAAC,CAAC;YACtG,MAAM,CAAC,kBAAkB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAE3C,MAAM,YAAY,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7D,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qEAAqE,EAAE,GAAG,EAAE;YAC7E,UAAU;YACV,MAAM,KAAK,GAAG,4BAAY,CAAC,MAAM,CAAC;gBAChC,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBAClE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,SAAS;oBACrB,IAAI,EAAE,UAAU;iBACjB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,cAAc;gBAC9B,QAAQ,EAAE,mCAAa,CAAC,IAAI;gBAC5B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,SAAS,EAAE,qBAAqB,EAAE;gBAC7C,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,uBAAuB;YACvB,KAAK,CAAC,qBAAqB,EAAE,CAAC;YAE9B,MAAM;YACN,KAAK,CAAC,gBAAgB,EAAE,CAAC;YAEzB,SAAS;YACT,MAAM,YAAY,GAAG,KAAK,CAAC,oBAAoB,EAAE,CAAC;YAClD,MAAM,mBAAmB,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,KAAK,+BAA+B,CAAC,CAAC;YAC7G,MAAM,CAAC,mBAAmB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAE5C,MAAM,aAAa,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sEAAsE,EAAE,GAAG,EAAE;YAC9E,UAAU;YACV,MAAM,KAAK,GAAG,4BAAY,CAAC,MAAM,CAAC;gBAChC,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBACtE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,QAAQ;oBAC9B,UAAU,EAAE,QAAQ;oBACpB,IAAI,EAAE,UAAU;iBACjB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,iBAAiB;gBACjC,QAAQ,EAAE,mCAAa,CAAC,QAAQ;gBAChC,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,QAAQ,EAAE,eAAe,EAAE,UAAU,EAAE,GAAG,EAAE;gBACvD,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,8CAA8C;YAC9C,KAAK,CAAC,gBAAgB,EAAE,CAAC;YACzB,KAAK,CAAC,eAAe,EAAE,CAAC;YAExB,SAAS;YACT,MAAM,SAAS,GAAG,KAAK,CAAC,oBAAoB,EAAE,CAAC;YAC/C,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,uCAAuC;YAEpF,MAAM,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAC1D,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;YACxD,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAC;YAE9D,4CAA4C;YAC5C,MAAM,kBAAkB,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,KAAK,+BAA+B,CAAC,CAAC;YACzG,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,oEAAoE,EAAE,GAAG,EAAE;YAC5E,gBAAgB;YAChB,MAAM,MAAM,GAAG,8BAAa,CAAC,MAAM,CAAC;gBAClC,SAAS,EAAE,uBAAuB;gBAClC,KAAK,EAAE,mCAAS,CAAC,MAAM,CAAC,GAAG,CAAC;gBAC5B,QAAQ,EAAE,qCAAc,CAAC,IAAI;gBAC7B,UAAU,EAAE,EAAE;gBACd,QAAQ,EAAE,mCAAS,CAAC,MAAM,CAAC,eAAe,CAAC;gBAC3C,UAAU,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,CAAC;gBACnD,eAAe,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;gBACnC,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,YAAY,GAAG,MAAM,CAAC,oBAAoB,EAAE,CAAC;YACnD,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAE/C,MAAM,oBAAoB,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,KAAK,2BAA2B,CAAC,CAAC;YAC1G,MAAM,CAAC,oBAAoB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAE7C,MAAM,cAAc,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uEAAuE,EAAE,GAAG,EAAE;YAC/E,UAAU;YACV,MAAM,MAAM,GAAG,8BAAa,CAAC,MAAM,CAAC;gBAClC,SAAS,EAAE,sBAAsB;gBACjC,KAAK,EAAE,mCAAS,CAAC,MAAM,CAAC,GAAG,CAAC;gBAC5B,QAAQ,EAAE,qCAAc,CAAC,MAAM;gBAC/B,UAAU,EAAE,EAAE;gBACd,QAAQ,EAAE,mCAAS,CAAC,MAAM,CAAC,UAAU,CAAC;gBACtC,UAAU,EAAE,CAAC,SAAS,CAAC;gBACvB,eAAe,EAAE,CAAC,OAAO,CAAC;gBAC1B,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,uBAAuB;YACvB,MAAM,CAAC,qBAAqB,EAAE,CAAC;YAE/B,MAAM;YACN,MAAM,CAAC,eAAe,EAAE,CAAC;YAEzB,SAAS;YACT,MAAM,YAAY,GAAG,MAAM,CAAC,oBAAoB,EAAE,CAAC;YACnD,MAAM,eAAe,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,KAAK,4BAA4B,CAAC,CAAC;YACtG,MAAM,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAExC,MAAM,SAAS,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE;QAClD,EAAE,CAAC,+EAA+E,EAAE,GAAG,EAAE;YACvF,gBAAgB;YAChB,MAAM,aAAa,GAAG,4CAAoB,CAAC,MAAM,CAAC;gBAChD,KAAK,EAAE,eAAe;gBACtB,KAAK,EAAE,mCAAS,CAAC,MAAM,CAAC,GAAG,CAAC;gBAC5B,QAAQ,EAAE,mDAAqB,CAAC,QAAQ;gBACxC,YAAY,EAAE,wBAAI,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC7B,WAAW,EAAE,8CAA8C;gBAC3D,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,YAAY,GAAG,aAAa,CAAC,oBAAoB,EAAE,CAAC;YAC1D,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAE/C,MAAM,eAAe,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,KAAK,+BAA+B,CAAC,CAAC;YACzG,MAAM,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAExC,MAAM,SAAS,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6EAA6E,EAAE,GAAG,EAAE;YACrF,UAAU;YACV,MAAM,aAAa,GAAG,4CAAoB,CAAC,MAAM,CAAC;gBAChD,KAAK,EAAE,eAAe;gBACtB,KAAK,EAAE,mCAAS,CAAC,MAAM,CAAC,GAAG,CAAC;gBAC5B,QAAQ,EAAE,mDAAqB,CAAC,MAAM;gBACtC,YAAY,EAAE,wBAAI,CAAC,MAAM,CAAC,GAAG,CAAC;gBAC9B,WAAW,EAAE,+BAA+B;gBAC5C,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,uBAAuB;YACvB,aAAa,CAAC,qBAAqB,EAAE,CAAC;YAEtC,MAAM;YACN,aAAa,CAAC,aAAa,EAAE,CAAC;YAE9B,SAAS;YACT,MAAM,YAAY,GAAG,aAAa,CAAC,oBAAoB,EAAE,CAAC;YAC1D,MAAM,mBAAmB,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,KAAK,iCAAiC,CAAC,CAAC;YAC/G,MAAM,CAAC,mBAAmB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qCAAqC,EAAE,GAAG,EAAE;QACnD,EAAE,CAAC,2EAA2E,EAAE,GAAG,EAAE;YACnF,gBAAgB;YAChB,MAAM,cAAc,GAAG,+CAAqB,CAAC,MAAM,CAAC;gBAClD,IAAI,EAAE,6BAAU,CAAC,QAAQ;gBACzB,UAAU,EAAE,EAAE,SAAS,EAAE,eAAe,EAAE,QAAQ,EAAE,IAAI,EAAE;gBAC1D,QAAQ,EAAE,CAAC;gBACX,MAAM,EAAE,iCAAY,CAAC,OAAO;gBAC5B,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,YAAY,GAAG,cAAc,CAAC,oBAAoB,EAAE,CAAC;YAC3D,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAE/C,MAAM,mBAAmB,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,KAAK,kCAAkC,CAAC,CAAC;YAChH,MAAM,CAAC,mBAAmB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAE5C,MAAM,aAAa,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6EAA6E,EAAE,GAAG,EAAE;YACrF,UAAU;YACV,MAAM,cAAc,GAAG,+CAAqB,CAAC,MAAM,CAAC;gBAClD,IAAI,EAAE,6BAAU,CAAC,YAAY;gBAC7B,UAAU,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,kBAAkB,EAAE;gBAC9D,QAAQ,EAAE,CAAC;gBACX,MAAM,EAAE,iCAAY,CAAC,OAAO;gBAC5B,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,uBAAuB;YACvB,cAAc,CAAC,qBAAqB,EAAE,CAAC;YAEvC,MAAM;YACN,cAAc,CAAC,OAAO,EAAE,CAAC;YAEzB,SAAS;YACT,MAAM,YAAY,GAAG,cAAc,CAAC,oBAAoB,EAAE,CAAC;YAC3D,MAAM,cAAc,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,KAAK,mCAAmC,CAAC,CAAC;YAC5G,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEvC,MAAM,QAAQ,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8EAA8E,EAAE,GAAG,EAAE;YACtF,UAAU;YACV,MAAM,cAAc,GAAG,+CAAqB,CAAC,MAAM,CAAC;gBAClD,IAAI,EAAE,6BAAU,CAAC,eAAe;gBAChC,UAAU,EAAE,EAAE,QAAQ,EAAE,kBAAkB,EAAE,MAAM,EAAE,kBAAkB,EAAE;gBACxE,QAAQ,EAAE,CAAC;gBACX,MAAM,EAAE,iCAAY,CAAC,OAAO;gBAC5B,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,uBAAuB;YACvB,cAAc,CAAC,qBAAqB,EAAE,CAAC;YAEvC,MAAM;YACN,cAAc,CAAC,OAAO,EAAE,CAAC;YACzB,cAAc,CAAC,QAAQ,EAAE,CAAC;YAE1B,SAAS;YACT,MAAM,YAAY,GAAG,cAAc,CAAC,oBAAoB,EAAE,CAAC;YAC3D,MAAM,mBAAmB,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,KAAK,wCAAwC,CAAC,CAAC;YACtH,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB;QACrF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qEAAqE,EAAE,GAAG,EAAE;YAC7E,UAAU;YACV,MAAM,cAAc,GAAG,+CAAqB,CAAC,MAAM,CAAC;gBAClD,IAAI,EAAE,6BAAU,CAAC,eAAe;gBAChC,UAAU,EAAE,EAAE,WAAW,EAAE,cAAc,EAAE;gBAC3C,QAAQ,EAAE,CAAC;gBACX,MAAM,EAAE,iCAAY,CAAC,OAAO;gBAC5B,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,uBAAuB;YACvB,cAAc,CAAC,qBAAqB,EAAE,CAAC;YAEvC,MAAM;YACN,cAAc,CAAC,OAAO,EAAE,CAAC;YACzB,cAAc,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAEzC,SAAS;YACT,MAAM,YAAY,GAAG,cAAc,CAAC,oBAAoB,EAAE,CAAC;YAC3D,MAAM,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,KAAK,iCAAiC,CAAC,CAAC;YACxG,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAErC,MAAM,MAAM,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kFAAkF,EAAE,GAAG,EAAE;YAC1F,UAAU;YACV,MAAM,cAAc,GAAG,+CAAqB,CAAC,MAAM,CAAC;gBAClD,IAAI,EAAE,6BAAU,CAAC,QAAQ;gBACzB,UAAU,EAAE,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,EAAE;gBACrD,QAAQ,EAAE,CAAC;gBACX,MAAM,EAAE,iCAAY,CAAC,SAAS;gBAC9B,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,uBAAuB;YACvB,cAAc,CAAC,qBAAqB,EAAE,CAAC;YAEvC,MAAM;YACN,cAAc,CAAC,QAAQ,EAAE,CAAC;YAE1B,SAAS;YACT,MAAM,YAAY,GAAG,cAAc,CAAC,oBAAoB,EAAE,CAAC;YAC3D,MAAM,gBAAgB,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,KAAK,qCAAqC,CAAC,CAAC;YAChH,MAAM,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEzC,MAAM,UAAU,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,UAAU;YACV,MAAM,QAAQ,GAAG,iCAAQ,CAAC,MAAM,EAAE,CAAC;YACnC,MAAM,MAAM,GAAG,6BAAM,CAAC,MAAM,EAAE,CAAC;YAE/B,kEAAkE;YAClE,MAAM,KAAK,GAAG,4BAAY,CAAC,MAAM,CAAC;gBAChC,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBACjE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,GAAG;oBACzB,UAAU,EAAE,SAAS;oBACrB,IAAI,EAAE,4BAA4B;iBACnC,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,iBAAiB;gBACjC,QAAQ,EAAE,mCAAa,CAAC,IAAI;gBAC5B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,QAAQ,EAAE,eAAe,EAAE,UAAU,EAAE,GAAG,EAAE;gBACvD,QAAQ;gBACR,MAAM;aACP,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,8BAAa,CAAC,MAAM,CAAC;gBAClC,SAAS,EAAE,uBAAuB;gBAClC,KAAK,EAAE,mCAAS,CAAC,MAAM,CAAC,GAAG,CAAC;gBAC5B,QAAQ,EAAE,qCAAc,CAAC,IAAI;gBAC7B,UAAU,EAAE,EAAE;gBACd,QAAQ,EAAE,mCAAS,CAAC,MAAM,CAAC,eAAe,CAAC;gBAC3C,UAAU,EAAE,CAAC,4BAA4B,CAAC;gBAC1C,eAAe,EAAE,CAAC,OAAO,CAAC;gBAC1B,QAAQ;gBACR,MAAM;aACP,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,+CAAqB,CAAC,MAAM,CAAC;gBAClD,IAAI,EAAE,6BAAU,CAAC,QAAQ;gBACzB,UAAU,EAAE,EAAE,SAAS,EAAE,eAAe,EAAE,QAAQ,EAAE,IAAI,EAAE;gBAC1D,QAAQ,EAAE,CAAC;gBACX,MAAM,EAAE,iCAAY,CAAC,OAAO;gBAC5B,QAAQ;gBACR,MAAM;aACP,CAAC,CAAC;YAEH,uDAAuD;YACvD,MAAM,eAAe,GAAG;gBACtB,GAAG,KAAK,CAAC,oBAAoB,EAAE;gBAC/B,GAAG,MAAM,CAAC,oBAAoB,EAAE;gBAChC,GAAG,cAAc,CAAC,oBAAoB,EAAE;aACzC,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAElD,8CAA8C;YAC9C,MAAM,UAAU,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAChE,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;YACxD,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;YAC1D,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC;YAEjE,4CAA4C;YAC5C,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;gBACpC,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;gBACpD,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC1C,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YAChD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,UAAU;YACV,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAE7B,4DAA4D;YAC5D,MAAM,MAAM,GAAG,4BAAY,CAAC,MAAM,CAAC;gBACjC,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBACrE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,SAAS;oBACrB,IAAI,EAAE,OAAO;iBACd,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,sBAAsB;gBACtC,QAAQ,EAAE,mCAAa,CAAC,MAAM;gBAC9B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE;gBAC9B,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,cAAc;YACd,MAAM,MAAM,GAAG,4BAAY,CAAC,MAAM,CAAC;gBACjC,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBACrE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,WAAW;oBACjC,UAAU,EAAE,SAAS;oBACrB,IAAI,EAAE,OAAO;iBACd,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,sBAAsB;gBACtC,QAAQ,EAAE,mCAAa,CAAC,MAAM;gBAC9B,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE;gBAC9B,QAAQ,EAAE,iCAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,EAAE,6BAAM,CAAC,MAAM,EAAE;aACxB,CAAC,CAAC;YAEH,+BAA+B;YAC/B,MAAM,OAAO,GAAG,MAAM,CAAC,oBAAoB,EAAE,CAAC;YAC9C,MAAM,OAAO,GAAG,MAAM,CAAC,oBAAoB,EAAE,CAAC;YAE9C,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAE1C,mDAAmD;YACnD,CAAC,GAAG,OAAO,EAAE,GAAG,OAAO,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;gBAC7C,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,sBAAsB,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;YACvF,CAAC,CAAC,CAAC;YAEH,0FAA0F;YAC1F,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;YAC7D,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;YAE7D,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC;YAC/C,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC;YAE/C,MAAM,CAAC,aAAa,CAAC,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC/C,EAAE,CAAC,oEAAoE,EAAE,GAAG,EAAE;YAC5E,UAAU;YACV,MAAM,QAAQ,GAAG,iCAAQ,CAAC,MAAM,EAAE,CAAC;YACnC,MAAM,MAAM,GAAG,6BAAM,CAAC,MAAM,EAAE,CAAC;YAE/B,4CAA4C;YAC5C,MAAM,aAAa,GAAG,4BAAY,CAAC,MAAM,CAAC;gBACxC,QAAQ,EAAE,2CAAa,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBACtE,SAAS,EAAE,6CAAc,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,wCAAe,CAAC,QAAQ;oBAC9B,UAAU,EAAE,QAAQ;oBACpB,IAAI,EAAE,UAAU;iBACjB,CAAC;gBACF,IAAI,EAAE,2BAAS,CAAC,iBAAiB;gBACjC,QAAQ,EAAE,mCAAa,CAAC,QAAQ;gBAChC,MAAM,EAAE,+BAAW,CAAC,QAAQ;gBAC5B,OAAO,EAAE,EAAE,QAAQ,EAAE,eAAe,EAAE;gBACtC,QAAQ;gBACR,MAAM;aACP,CAAC,CAAC;YAEH,oBAAoB;YACpB,aAAa,CAAC,gBAAgB,EAAE,CAAC;YACjC,aAAa,CAAC,eAAe,EAAE,CAAC;YAEhC,mCAAmC;YACnC,MAAM,MAAM,GAAG,8BAAa,CAAC,MAAM,CAAC;gBAClC,SAAS,EAAE,sBAAsB;gBACjC,KAAK,EAAE,mCAAS,CAAC,MAAM,CAAC,GAAG,CAAC;gBAC5B,QAAQ,EAAE,qCAAc,CAAC,QAAQ;gBACjC,UAAU,EAAE,EAAE;gBACd,QAAQ,EAAE,mCAAS,CAAC,MAAM,CAAC,eAAe,CAAC;gBAC3C,UAAU,EAAE,CAAC,2BAA2B,CAAC;gBACzC,eAAe,EAAE,CAAC,OAAO,CAAC;gBAC1B,QAAQ;gBACR,MAAM;aACP,CAAC,CAAC;YAEH,6CAA6C;YAC7C,MAAM,cAAc,GAAG,+CAAqB,CAAC,MAAM,CAAC;gBAClD,IAAI,EAAE,6BAAU,CAAC,QAAQ;gBACzB,UAAU,EAAE,EAAE,SAAS,EAAE,eAAe,EAAE,QAAQ,EAAE,IAAI,EAAE;gBAC1D,QAAQ,EAAE,CAAC;gBACX,MAAM,EAAE,iCAAY,CAAC,OAAO;gBAC5B,QAAQ;gBACR,MAAM;aACP,CAAC,CAAC;YAEH,8BAA8B;YAC9B,cAAc,CAAC,OAAO,EAAE,CAAC;YACzB,cAAc,CAAC,QAAQ,EAAE,CAAC;YAE1B,qDAAqD;YACrD,MAAM,iBAAiB,GAAG,aAAa,CAAC,oBAAoB,EAAE,CAAC;YAC/D,MAAM,kBAAkB,GAAG,MAAM,CAAC,oBAAoB,EAAE,CAAC;YACzD,MAAM,kBAAkB,GAAG,cAAc,CAAC,oBAAoB,EAAE,CAAC;YAEjE,2BAA2B;YAC3B,MAAM,SAAS,GAAG,CAAC,GAAG,iBAAiB,EAAE,GAAG,kBAAkB,EAAE,GAAG,kBAAkB,CAAC,CAAC;YACvF,MAAM,YAAY,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;YAE/F,iDAAiD;YACjD,MAAM,cAAc,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC;YACxD,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YAEvD,wCAAwC;YACxC,MAAM,iBAAiB,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACpE,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;YAC/D,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAC;YACrE,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;YACjE,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC;YACxE,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,mCAAmC,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\__tests__\\integration\\domain-events.integration.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { Event } from '../../domain/entities/event.entity';\r\nimport { Threat } from '../../domain/entities/threat.entity';\r\nimport { Vulnerability } from '../../domain/entities/vulnerability.entity';\r\nimport { ResponseAction } from '../../domain/entities/response-action.entity';\r\nimport { EventFactory } from '../../domain/factories/event.factory';\r\nimport { ThreatFactory } from '../../domain/factories/threat.factory';\r\nimport { VulnerabilityFactory } from '../../domain/factories/vulnerability.factory';\r\nimport { ResponseActionFactory } from '../../domain/factories/response-action.factory';\r\nimport { EventMetadata } from '../../domain/value-objects/event-metadata/event-metadata.value-object';\r\nimport { EventTimestamp } from '../../domain/value-objects/event-metadata/event-timestamp.value-object';\r\nimport { EventSource } from '../../domain/value-objects/event-metadata/event-source.value-object';\r\nimport { IpAddress } from '../../domain/value-objects/network/ip-address.value-object';\r\nimport { Port } from '../../domain/value-objects/network/port.value-object';\r\nimport { CvssScore } from '../../domain/value-objects/threat-indicators/cvss-score.value-object';\r\nimport { EventType } from '../../domain/enums/event-type.enum';\r\nimport { EventSeverity } from '../../domain/enums/event-severity.enum';\r\nimport { EventStatus } from '../../domain/enums/event-status.enum';\r\nimport { EventSourceType } from '../../domain/enums/event-source-type.enum';\r\nimport { ThreatSeverity } from '../../domain/enums/threat-severity.enum';\r\nimport { VulnerabilitySeverity } from '../../domain/enums/vulnerability-severity.enum';\r\nimport { ActionType } from '../../domain/enums/action-type.enum';\r\nimport { ActionStatus } from '../../domain/enums/action-status.enum';\r\nimport { TenantId } from '../../../../shared-kernel/value-objects/tenant-id.value-object';\r\nimport { UserId } from '../../../../shared-kernel/value-objects/user-id.value-object';\r\nimport { BaseDomainEvent } from '../../../../shared-kernel/domain/base-domain-event';\r\n\r\n/**\r\n * Mock Domain Event Handler for testing event propagation\r\n */\r\nclass MockDomainEventHandler {\r\n  private handledEvents: BaseDomainEvent[] = [];\r\n\r\n  handle(event: BaseDomainEvent): void {\r\n    this.handledEvents.push(event);\r\n  }\r\n\r\n  getHandledEvents(): BaseDomainEvent[] {\r\n    return this.handledEvents;\r\n  }\r\n\r\n  getEventsByType(eventType: string): BaseDomainEvent[] {\r\n    return this.handledEvents.filter(event => event.constructor.name === eventType);\r\n  }\r\n\r\n  clear(): void {\r\n    this.handledEvents = [];\r\n  }\r\n}\r\n\r\ndescribe('Domain Events Integration Tests', () => {\r\n  let module: TestingModule;\r\n  let eventHandler: MockDomainEventHandler;\r\n\r\n  beforeEach(async () => {\r\n    eventHandler = new MockDomainEventHandler();\r\n\r\n    module = await Test.createTestingModule({\r\n      providers: [\r\n        { provide: 'DomainEventHandler', useValue: eventHandler },\r\n      ],\r\n    }).compile();\r\n  });\r\n\r\n  afterEach(async () => {\r\n    await module.close();\r\n  });\r\n\r\n  describe('Event Entity Domain Events', () => {\r\n    it('should generate EventCreated domain event when event is created', () => {\r\n      // Arrange & Act\r\n      const event = EventFactory.create({\r\n        metadata: EventMetadata.create({ source: 'test', version: '1.0' }),\r\n        timestamp: EventTimestamp.create(new Date()),\r\n        source: EventSource.create({\r\n          type: EventSourceType.APPLICATION,\r\n          identifier: 'app-001',\r\n          name: 'Test App',\r\n        }),\r\n        type: EventType.AUTHENTICATION_SUCCESS,\r\n        severity: EventSeverity.LOW,\r\n        status: EventStatus.RECEIVED,\r\n        payload: { username: 'testuser' },\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      // Assert\r\n      const domainEvents = event.getUncommittedEvents();\r\n      expect(domainEvents.length).toBeGreaterThan(0);\r\n      \r\n      const eventCreatedEvents = domainEvents.filter(e => e.constructor.name === 'EventCreatedDomainEvent');\r\n      expect(eventCreatedEvents).toHaveLength(1);\r\n      \r\n      const eventCreated = eventCreatedEvents[0];\r\n      expect(eventCreated.aggregateId.equals(event.id)).toBe(true);\r\n      expect(eventCreated.occurredOn).toBeInstanceOf(Date);\r\n    });\r\n\r\n    it('should generate EventStatusChanged domain event when status changes', () => {\r\n      // Arrange\r\n      const event = EventFactory.create({\r\n        metadata: EventMetadata.create({ source: 'test', version: '1.0' }),\r\n        timestamp: EventTimestamp.create(new Date()),\r\n        source: EventSource.create({\r\n          type: EventSourceType.APPLICATION,\r\n          identifier: 'app-001',\r\n          name: 'Test App',\r\n        }),\r\n        type: EventType.SECURITY_ALERT,\r\n        severity: EventSeverity.HIGH,\r\n        status: EventStatus.RECEIVED,\r\n        payload: { alertType: 'suspicious-activity' },\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      // Clear initial events\r\n      event.markEventsAsCommitted();\r\n\r\n      // Act\r\n      event.markAsProcessing();\r\n\r\n      // Assert\r\n      const domainEvents = event.getUncommittedEvents();\r\n      const statusChangedEvents = domainEvents.filter(e => e.constructor.name === 'EventStatusChangedDomainEvent');\r\n      expect(statusChangedEvents).toHaveLength(1);\r\n      \r\n      const statusChanged = statusChangedEvents[0];\r\n      expect(statusChanged.aggregateId.equals(event.id)).toBe(true);\r\n    });\r\n\r\n    it('should generate multiple domain events for complex state transitions', () => {\r\n      // Arrange\r\n      const event = EventFactory.create({\r\n        metadata: EventMetadata.create({ source: 'firewall', version: '1.0' }),\r\n        timestamp: EventTimestamp.create(new Date()),\r\n        source: EventSource.create({\r\n          type: EventSourceType.FIREWALL,\r\n          identifier: 'fw-001',\r\n          name: 'Firewall',\r\n        }),\r\n        type: EventType.NETWORK_INTRUSION,\r\n        severity: EventSeverity.CRITICAL,\r\n        status: EventStatus.RECEIVED,\r\n        payload: { sourceIp: '*************', targetPort: 443 },\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      // Act - Simulate complete processing workflow\r\n      event.markAsProcessing();\r\n      event.markAsProcessed();\r\n\r\n      // Assert\r\n      const allEvents = event.getUncommittedEvents();\r\n      expect(allEvents.length).toBeGreaterThan(2); // At least creation + 2 status changes\r\n\r\n      const eventTypes = allEvents.map(e => e.constructor.name);\r\n      expect(eventTypes).toContain('EventCreatedDomainEvent');\r\n      expect(eventTypes).toContain('EventStatusChangedDomainEvent');\r\n      \r\n      // Should have multiple status change events\r\n      const statusChangeEvents = allEvents.filter(e => e.constructor.name === 'EventStatusChangedDomainEvent');\r\n      expect(statusChangeEvents.length).toBeGreaterThanOrEqual(2);\r\n    });\r\n  });\r\n\r\n  describe('Threat Entity Domain Events', () => {\r\n    it('should generate ThreatDetected domain event when threat is created', () => {\r\n      // Arrange & Act\r\n      const threat = ThreatFactory.create({\r\n        signature: 'malware-signature-001',\r\n        score: CvssScore.create(8.5),\r\n        severity: ThreatSeverity.HIGH,\r\n        confidence: 90,\r\n        sourceIp: IpAddress.create('*************'),\r\n        indicators: ['suspicious-hash', 'malicious-domain'],\r\n        mitreTechniques: ['T1190', 'T1055'],\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      // Assert\r\n      const domainEvents = threat.getUncommittedEvents();\r\n      expect(domainEvents.length).toBeGreaterThan(0);\r\n      \r\n      const threatDetectedEvents = domainEvents.filter(e => e.constructor.name === 'ThreatDetectedDomainEvent');\r\n      expect(threatDetectedEvents).toHaveLength(1);\r\n      \r\n      const threatDetected = threatDetectedEvents[0];\r\n      expect(threatDetected.aggregateId.equals(threat.id)).toBe(true);\r\n    });\r\n\r\n    it('should generate ThreatMitigated domain event when threat is mitigated', () => {\r\n      // Arrange\r\n      const threat = ThreatFactory.create({\r\n        signature: 'threat-signature-001',\r\n        score: CvssScore.create(7.0),\r\n        severity: ThreatSeverity.MEDIUM,\r\n        confidence: 85,\r\n        sourceIp: IpAddress.create('********'),\r\n        indicators: ['ioc-001'],\r\n        mitreTechniques: ['T1001'],\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      // Clear initial events\r\n      threat.markEventsAsCommitted();\r\n\r\n      // Act\r\n      threat.markAsMitigated();\r\n\r\n      // Assert\r\n      const domainEvents = threat.getUncommittedEvents();\r\n      const mitigatedEvents = domainEvents.filter(e => e.constructor.name === 'ThreatMitigatedDomainEvent');\r\n      expect(mitigatedEvents).toHaveLength(1);\r\n      \r\n      const mitigated = mitigatedEvents[0];\r\n      expect(mitigated.aggregateId.equals(threat.id)).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('Vulnerability Entity Domain Events', () => {\r\n    it('should generate VulnerabilityFound domain event when vulnerability is created', () => {\r\n      // Arrange & Act\r\n      const vulnerability = VulnerabilityFactory.create({\r\n        cveId: 'CVE-2023-1234',\r\n        score: CvssScore.create(9.0),\r\n        severity: VulnerabilitySeverity.CRITICAL,\r\n        affectedPort: Port.create(80),\r\n        description: 'Critical remote code execution vulnerability',\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      // Assert\r\n      const domainEvents = vulnerability.getUncommittedEvents();\r\n      expect(domainEvents.length).toBeGreaterThan(0);\r\n      \r\n      const vulnFoundEvents = domainEvents.filter(e => e.constructor.name === 'VulnerabilityFoundDomainEvent');\r\n      expect(vulnFoundEvents).toHaveLength(1);\r\n      \r\n      const vulnFound = vulnFoundEvents[0];\r\n      expect(vulnFound.aggregateId.equals(vulnerability.id)).toBe(true);\r\n    });\r\n\r\n    it('should generate VulnerabilityStatusChanged domain event when status changes', () => {\r\n      // Arrange\r\n      const vulnerability = VulnerabilityFactory.create({\r\n        cveId: 'CVE-2023-5678',\r\n        score: CvssScore.create(6.5),\r\n        severity: VulnerabilitySeverity.MEDIUM,\r\n        affectedPort: Port.create(443),\r\n        description: 'Medium severity vulnerability',\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      // Clear initial events\r\n      vulnerability.markEventsAsCommitted();\r\n\r\n      // Act\r\n      vulnerability.markAsPatched();\r\n\r\n      // Assert\r\n      const domainEvents = vulnerability.getUncommittedEvents();\r\n      const statusChangedEvents = domainEvents.filter(e => e.constructor.name === 'VulnerabilityStatusChangedEvent');\r\n      expect(statusChangedEvents).toHaveLength(1);\r\n    });\r\n  });\r\n\r\n  describe('ResponseAction Entity Domain Events', () => {\r\n    it('should generate ResponseActionCreated domain event when action is created', () => {\r\n      // Arrange & Act\r\n      const responseAction = ResponseActionFactory.create({\r\n        type: ActionType.BLOCK_IP,\r\n        parameters: { ipAddress: '*************', duration: 3600 },\r\n        priority: 1,\r\n        status: ActionStatus.PENDING,\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      // Assert\r\n      const domainEvents = responseAction.getUncommittedEvents();\r\n      expect(domainEvents.length).toBeGreaterThan(0);\r\n      \r\n      const actionCreatedEvents = domainEvents.filter(e => e.constructor.name === 'ResponseActionCreatedDomainEvent');\r\n      expect(actionCreatedEvents).toHaveLength(1);\r\n      \r\n      const actionCreated = actionCreatedEvents[0];\r\n      expect(actionCreated.aggregateId.equals(responseAction.id)).toBe(true);\r\n    });\r\n\r\n    it('should generate ResponseActionExecuted domain event when action is executed', () => {\r\n      // Arrange\r\n      const responseAction = ResponseActionFactory.create({\r\n        type: ActionType.ISOLATE_HOST,\r\n        parameters: { hostId: 'host-001', reason: 'malware-detected' },\r\n        priority: 1,\r\n        status: ActionStatus.PENDING,\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      // Clear initial events\r\n      responseAction.markEventsAsCommitted();\r\n\r\n      // Act\r\n      responseAction.execute();\r\n\r\n      // Assert\r\n      const domainEvents = responseAction.getUncommittedEvents();\r\n      const executedEvents = domainEvents.filter(e => e.constructor.name === 'ResponseActionExecutedDomainEvent');\r\n      expect(executedEvents).toHaveLength(1);\r\n      \r\n      const executed = executedEvents[0];\r\n      expect(executed.aggregateId.equals(responseAction.id)).toBe(true);\r\n    });\r\n\r\n    it('should generate ResponseActionStatusChanged domain event when status changes', () => {\r\n      // Arrange\r\n      const responseAction = ResponseActionFactory.create({\r\n        type: ActionType.QUARANTINE_FILE,\r\n        parameters: { filePath: '/tmp/malware.exe', reason: 'malware-detected' },\r\n        priority: 2,\r\n        status: ActionStatus.PENDING,\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      // Clear initial events\r\n      responseAction.markEventsAsCommitted();\r\n\r\n      // Act\r\n      responseAction.execute();\r\n      responseAction.complete();\r\n\r\n      // Assert\r\n      const domainEvents = responseAction.getUncommittedEvents();\r\n      const statusChangedEvents = domainEvents.filter(e => e.constructor.name === 'ResponseActionStatusChangedDomainEvent');\r\n      expect(statusChangedEvents.length).toBeGreaterThanOrEqual(2); // Execute + Complete\r\n    });\r\n\r\n    it('should generate ResponseActionFailed domain event when action fails', () => {\r\n      // Arrange\r\n      const responseAction = ResponseActionFactory.create({\r\n        type: ActionType.RESTART_SERVICE,\r\n        parameters: { serviceName: 'test-service' },\r\n        priority: 3,\r\n        status: ActionStatus.PENDING,\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      // Clear initial events\r\n      responseAction.markEventsAsCommitted();\r\n\r\n      // Act\r\n      responseAction.execute();\r\n      responseAction.fail('Service not found');\r\n\r\n      // Assert\r\n      const domainEvents = responseAction.getUncommittedEvents();\r\n      const failedEvents = domainEvents.filter(e => e.constructor.name === 'ResponseActionFailedDomainEvent');\r\n      expect(failedEvents).toHaveLength(1);\r\n      \r\n      const failed = failedEvents[0];\r\n      expect(failed.aggregateId.equals(responseAction.id)).toBe(true);\r\n    });\r\n\r\n    it('should generate ResponseActionRolledBack domain event when action is rolled back', () => {\r\n      // Arrange\r\n      const responseAction = ResponseActionFactory.create({\r\n        type: ActionType.BLOCK_IP,\r\n        parameters: { ipAddress: '********', duration: 1800 },\r\n        priority: 1,\r\n        status: ActionStatus.COMPLETED,\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      // Clear initial events\r\n      responseAction.markEventsAsCommitted();\r\n\r\n      // Act\r\n      responseAction.rollback();\r\n\r\n      // Assert\r\n      const domainEvents = responseAction.getUncommittedEvents();\r\n      const rolledBackEvents = domainEvents.filter(e => e.constructor.name === 'ResponseActionRolledBackDomainEvent');\r\n      expect(rolledBackEvents).toHaveLength(1);\r\n      \r\n      const rolledBack = rolledBackEvents[0];\r\n      expect(rolledBack.aggregateId.equals(responseAction.id)).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('Event Propagation Workflow', () => {\r\n    it('should propagate events through complete security workflow', () => {\r\n      // Arrange\r\n      const tenantId = TenantId.create();\r\n      const userId = UserId.create();\r\n\r\n      // Act - Create entities that would be part of a security workflow\r\n      const event = EventFactory.create({\r\n        metadata: EventMetadata.create({ source: 'ids', version: '1.0' }),\r\n        timestamp: EventTimestamp.create(new Date()),\r\n        source: EventSource.create({\r\n          type: EventSourceType.IDS,\r\n          identifier: 'ids-001',\r\n          name: 'Intrusion Detection System',\r\n        }),\r\n        type: EventType.NETWORK_INTRUSION,\r\n        severity: EventSeverity.HIGH,\r\n        status: EventStatus.RECEIVED,\r\n        payload: { sourceIp: '*************', targetPort: 443 },\r\n        tenantId,\r\n        userId,\r\n      });\r\n\r\n      const threat = ThreatFactory.create({\r\n        signature: 'network-intrusion-001',\r\n        score: CvssScore.create(8.0),\r\n        severity: ThreatSeverity.HIGH,\r\n        confidence: 95,\r\n        sourceIp: IpAddress.create('*************'),\r\n        indicators: ['suspicious-traffic-pattern'],\r\n        mitreTechniques: ['T1190'],\r\n        tenantId,\r\n        userId,\r\n      });\r\n\r\n      const responseAction = ResponseActionFactory.create({\r\n        type: ActionType.BLOCK_IP,\r\n        parameters: { ipAddress: '*************', duration: 3600 },\r\n        priority: 1,\r\n        status: ActionStatus.PENDING,\r\n        tenantId,\r\n        userId,\r\n      });\r\n\r\n      // Assert - Collect all domain events from the workflow\r\n      const allDomainEvents = [\r\n        ...event.getUncommittedEvents(),\r\n        ...threat.getUncommittedEvents(),\r\n        ...responseAction.getUncommittedEvents(),\r\n      ];\r\n\r\n      expect(allDomainEvents.length).toBeGreaterThan(0);\r\n      \r\n      // Verify we have events from each entity type\r\n      const eventTypes = allDomainEvents.map(e => e.constructor.name);\r\n      expect(eventTypes).toContain('EventCreatedDomainEvent');\r\n      expect(eventTypes).toContain('ThreatDetectedDomainEvent');\r\n      expect(eventTypes).toContain('ResponseActionCreatedDomainEvent');\r\n\r\n      // Verify all events have proper correlation\r\n      allDomainEvents.forEach(domainEvent => {\r\n        expect(domainEvent.occurredOn).toBeInstanceOf(Date);\r\n        expect(domainEvent.eventId).toBeDefined();\r\n        expect(domainEvent.aggregateId).toBeDefined();\r\n      });\r\n    });\r\n\r\n    it('should handle event ordering and timing correctly', () => {\r\n      // Arrange\r\n      const startTime = new Date();\r\n      \r\n      // Act - Create entities with slight delays to test ordering\r\n      const event1 = EventFactory.create({\r\n        metadata: EventMetadata.create({ source: 'source1', version: '1.0' }),\r\n        timestamp: EventTimestamp.create(new Date()),\r\n        source: EventSource.create({\r\n          type: EventSourceType.APPLICATION,\r\n          identifier: 'app-001',\r\n          name: 'App 1',\r\n        }),\r\n        type: EventType.AUTHENTICATION_FAILURE,\r\n        severity: EventSeverity.MEDIUM,\r\n        status: EventStatus.RECEIVED,\r\n        payload: { username: 'user1' },\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      // Small delay\r\n      const event2 = EventFactory.create({\r\n        metadata: EventMetadata.create({ source: 'source2', version: '1.0' }),\r\n        timestamp: EventTimestamp.create(new Date()),\r\n        source: EventSource.create({\r\n          type: EventSourceType.APPLICATION,\r\n          identifier: 'app-002',\r\n          name: 'App 2',\r\n        }),\r\n        type: EventType.AUTHENTICATION_FAILURE,\r\n        severity: EventSeverity.MEDIUM,\r\n        status: EventStatus.RECEIVED,\r\n        payload: { username: 'user2' },\r\n        tenantId: TenantId.create(),\r\n        userId: UserId.create(),\r\n      });\r\n\r\n      // Assert - Verify event timing\r\n      const events1 = event1.getUncommittedEvents();\r\n      const events2 = event2.getUncommittedEvents();\r\n\r\n      expect(events1.length).toBeGreaterThan(0);\r\n      expect(events2.length).toBeGreaterThan(0);\r\n\r\n      // All events should have occurred after start time\r\n      [...events1, ...events2].forEach(domainEvent => {\r\n        expect(domainEvent.occurredOn.getTime()).toBeGreaterThanOrEqual(startTime.getTime());\r\n      });\r\n\r\n      // Events from event2 should have occurred after or at the same time as events from event1\r\n      const event1Times = events1.map(e => e.occurredOn.getTime());\r\n      const event2Times = events2.map(e => e.occurredOn.getTime());\r\n      \r\n      const maxEvent1Time = Math.max(...event1Times);\r\n      const minEvent2Time = Math.min(...event2Times);\r\n      \r\n      expect(minEvent2Time).toBeGreaterThanOrEqual(maxEvent1Time);\r\n    });\r\n  });\r\n\r\n  describe('Event Correlation and Causality', () => {\r\n    it('should maintain causal relationships between related domain events', () => {\r\n      // Arrange\r\n      const tenantId = TenantId.create();\r\n      const userId = UserId.create();\r\n\r\n      // Act - Create a sequence of related events\r\n      const securityEvent = EventFactory.create({\r\n        metadata: EventMetadata.create({ source: 'firewall', version: '1.0' }),\r\n        timestamp: EventTimestamp.create(new Date()),\r\n        source: EventSource.create({\r\n          type: EventSourceType.FIREWALL,\r\n          identifier: 'fw-001',\r\n          name: 'Firewall',\r\n        }),\r\n        type: EventType.NETWORK_INTRUSION,\r\n        severity: EventSeverity.CRITICAL,\r\n        status: EventStatus.RECEIVED,\r\n        payload: { sourceIp: '*************' },\r\n        tenantId,\r\n        userId,\r\n      });\r\n\r\n      // Process the event\r\n      securityEvent.markAsProcessing();\r\n      securityEvent.markAsProcessed();\r\n\r\n      // Create threat based on the event\r\n      const threat = ThreatFactory.create({\r\n        signature: 'intrusion-threat-001',\r\n        score: CvssScore.create(9.0),\r\n        severity: ThreatSeverity.CRITICAL,\r\n        confidence: 98,\r\n        sourceIp: IpAddress.create('*************'),\r\n        indicators: ['network-intrusion-pattern'],\r\n        mitreTechniques: ['T1190'],\r\n        tenantId,\r\n        userId,\r\n      });\r\n\r\n      // Create response action based on the threat\r\n      const responseAction = ResponseActionFactory.create({\r\n        type: ActionType.BLOCK_IP,\r\n        parameters: { ipAddress: '*************', duration: 7200 },\r\n        priority: 1,\r\n        status: ActionStatus.PENDING,\r\n        tenantId,\r\n        userId,\r\n      });\r\n\r\n      // Execute the response action\r\n      responseAction.execute();\r\n      responseAction.complete();\r\n\r\n      // Assert - Verify causal chain through domain events\r\n      const eventDomainEvents = securityEvent.getUncommittedEvents();\r\n      const threatDomainEvents = threat.getUncommittedEvents();\r\n      const actionDomainEvents = responseAction.getUncommittedEvents();\r\n\r\n      // Verify temporal ordering\r\n      const allEvents = [...eventDomainEvents, ...threatDomainEvents, ...actionDomainEvents];\r\n      const sortedEvents = allEvents.sort((a, b) => a.occurredOn.getTime() - b.occurredOn.getTime());\r\n\r\n      // First events should be from the security event\r\n      const firstEventType = sortedEvents[0].constructor.name;\r\n      expect(firstEventType).toBe('EventCreatedDomainEvent');\r\n\r\n      // Should have proper sequence of events\r\n      const eventTypeSequence = sortedEvents.map(e => e.constructor.name);\r\n      expect(eventTypeSequence).toContain('EventCreatedDomainEvent');\r\n      expect(eventTypeSequence).toContain('EventStatusChangedDomainEvent');\r\n      expect(eventTypeSequence).toContain('ThreatDetectedDomainEvent');\r\n      expect(eventTypeSequence).toContain('ResponseActionCreatedDomainEvent');\r\n      expect(eventTypeSequence).toContain('ResponseActionExecutedDomainEvent');\r\n    });\r\n  });\r\n});"], "version": 3}