9fd54414205a5480b11edc814275c08b
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j;
Object.defineProperty(exports, "__esModule", { value: true });
exports.Incident = void 0;
const typeorm_1 = require("typeorm");
const incident_task_entity_1 = require("./incident-task.entity");
const incident_evidence_entity_1 = require("./incident-evidence.entity");
const incident_communication_entity_1 = require("./incident-communication.entity");
const incident_timeline_entity_1 = require("./incident-timeline.entity");
const response_plan_entity_1 = require("./response-plan.entity");
/**
 * Incident entity
 * Represents security incidents with complete lifecycle management
 */
let Incident = class Incident {
    /**
     * Check if incident is open
     */
    get isOpen() {
        return !['closed', 'false_positive'].includes(this.status);
    }
    /**
     * Check if incident is critical
     */
    get isCritical() {
        return this.severity === 'critical' || this.priority === 'urgent';
    }
    /**
     * Check if incident is overdue
     */
    get isOverdue() {
        if (!this.isOpen)
            return false;
        const now = new Date();
        const slaMinutes = this.getSlaMinutes();
        const deadline = new Date(this.detectedAt.getTime() + slaMinutes * 60000);
        return now > deadline;
    }
    /**
     * Get incident age in hours
     */
    get ageInHours() {
        const now = new Date();
        const diffMs = now.getTime() - this.detectedAt.getTime();
        return Math.round(diffMs / (1000 * 60 * 60));
    }
    /**
     * Get time to resolution in hours
     */
    get timeToResolutionHours() {
        if (!this.resolvedAt)
            return null;
        const diffMs = this.resolvedAt.getTime() - this.detectedAt.getTime();
        return Math.round(diffMs / (1000 * 60 * 60));
    }
    /**
     * Acknowledge incident
     */
    acknowledge(userId) {
        if (this.status === 'new') {
            this.status = 'investigating';
            this.acknowledgedAt = new Date();
            this.assignedTo = userId;
            // Calculate time to acknowledge
            if (!this.metrics)
                this.metrics = {};
            const diffMs = this.acknowledgedAt.getTime() - this.detectedAt.getTime();
            this.metrics.timeToAcknowledge = Math.round(diffMs / (1000 * 60));
        }
    }
    /**
     * Escalate incident
     */
    escalate(reason, escalatedBy) {
        if (!this.metrics)
            this.metrics = {};
        const currentLevel = this.metrics.escalationLevel || 0;
        this.metrics.escalationLevel = currentLevel + 1;
        if (!this.metrics.escalationHistory) {
            this.metrics.escalationHistory = [];
        }
        this.metrics.escalationHistory.push({
            level: this.metrics.escalationLevel,
            timestamp: new Date().toISOString(),
            reason,
            escalatedBy,
        });
        // Auto-increase priority on escalation
        if (this.priority === 'low')
            this.priority = 'medium';
        else if (this.priority === 'medium')
            this.priority = 'high';
        else if (this.priority === 'high')
            this.priority = 'urgent';
    }
    /**
     * Resolve incident
     */
    resolve(userId, resolution) {
        this.status = 'post_incident';
        this.resolvedAt = new Date();
        this.updatedBy = userId;
        // Calculate time to resolution
        if (!this.metrics)
            this.metrics = {};
        const diffMs = this.resolvedAt.getTime() - this.detectedAt.getTime();
        this.metrics.timeToResolution = Math.round(diffMs / (1000 * 60));
        // Check SLA compliance
        const slaMinutes = this.getSlaMinutes();
        this.metrics.slaCompliant = this.metrics.timeToResolution <= slaMinutes;
        if (!this.metrics.slaCompliant) {
            this.metrics.slaBreach = {
                type: 'resolution',
                breachTime: this.metrics.timeToResolution - slaMinutes,
            };
        }
    }
    /**
     * Close incident
     */
    close(userId) {
        this.status = 'closed';
        this.closedAt = new Date();
        this.updatedBy = userId;
    }
    /**
     * Mark as false positive
     */
    markAsFalsePositive(userId, reason) {
        this.status = 'false_positive';
        this.closedAt = new Date();
        this.updatedBy = userId;
        if (reason && this.details) {
            this.details.falsePositiveReason = reason;
        }
    }
    /**
     * Add team member to response team
     */
    addTeamMember(userId, role, permissions = []) {
        if (!this.responseTeam) {
            this.responseTeam = [];
        }
        // Remove if already exists
        this.responseTeam = this.responseTeam.filter(member => member.userId !== userId);
        // Add new member
        this.responseTeam.push({
            userId,
            role,
            assignedAt: new Date().toISOString(),
            permissions,
        });
    }
    /**
     * Remove team member from response team
     */
    removeTeamMember(userId) {
        if (this.responseTeam) {
            this.responseTeam = this.responseTeam.filter(member => member.userId !== userId);
        }
    }
    /**
     * Add tag to incident
     */
    addTag(tag) {
        if (!this.tags.includes(tag)) {
            this.tags.push(tag);
        }
    }
    /**
     * Remove tag from incident
     */
    removeTag(tag) {
        this.tags = this.tags.filter(t => t !== tag);
    }
    /**
     * Check if user has permission
     */
    hasPermission(userId, permission) {
        if (this.assignedTo === userId)
            return true;
        const teamMember = this.responseTeam?.find(member => member.userId === userId);
        return teamMember?.permissions.includes(permission) || false;
    }
    /**
     * Get SLA minutes based on severity and priority
     */
    getSlaMinutes() {
        // SLA matrix based on severity and priority
        const slaMatrix = {
            critical: { urgent: 15, high: 30, medium: 60, low: 120 },
            high: { urgent: 30, high: 60, medium: 120, low: 240 },
            medium: { urgent: 60, high: 120, medium: 240, low: 480 },
            low: { urgent: 120, high: 240, medium: 480, low: 960 },
        };
        return slaMatrix[this.severity][this.priority];
    }
    /**
     * Calculate risk score
     */
    calculateRiskScore() {
        const severityScores = { low: 1, medium: 2, high: 3, critical: 4 };
        const priorityScores = { low: 1, medium: 2, high: 3, urgent: 4 };
        const severityScore = severityScores[this.severity];
        const priorityScore = priorityScores[this.priority];
        // Additional factors
        let multiplier = 1;
        if (this.details?.businessImpact?.dataCompromised)
            multiplier += 0.5;
        if (this.details?.businessImpact?.serviceDisruption)
            multiplier += 0.3;
        if (this.details?.affectedSystems?.length > 5)
            multiplier += 0.2;
        return Math.min(10, (severityScore + priorityScore) * multiplier);
    }
    /**
     * Get affected asset count
     */
    get affectedAssetCount() {
        return this.details?.affectedSystems?.length || 0;
    }
    /**
     * Get IOC count
     */
    get iocCount() {
        return this.details?.iocs?.length || 0;
    }
};
exports.Incident = Incident;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Incident.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], Incident.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], Incident.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: [
            'new',
            'investigating',
            'containment',
            'eradication',
            'recovery',
            'post_incident',
            'closed',
            'false_positive',
        ],
        default: 'new',
    }),
    __metadata("design:type", String)
], Incident.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['low', 'medium', 'high', 'critical'],
    }),
    __metadata("design:type", String)
], Incident.prototype, "severity", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['low', 'medium', 'high', 'urgent'],
        default: 'medium',
    }),
    __metadata("design:type", String)
], Incident.prototype, "priority", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: [
            'malware',
            'phishing',
            'data_breach',
            'unauthorized_access',
            'denial_of_service',
            'insider_threat',
            'vulnerability_exploitation',
            'social_engineering',
            'physical_security',
            'compliance_violation',
            'other',
        ],
    }),
    __metadata("design:type", String)
], Incident.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['automated_detection', 'user_report', 'external_report', 'threat_intelligence', 'vulnerability_scan', 'manual'],
        default: 'manual',
    }),
    __metadata("design:type", String)
], Incident.prototype, "source", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'detected_at', type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], Incident.prototype, "detectedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'occurred_at', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], Incident.prototype, "occurredAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'acknowledged_at', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], Incident.prototype, "acknowledgedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'resolved_at', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], Incident.prototype, "resolvedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'closed_at', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_e = typeof Date !== "undefined" && Date) === "function" ? _e : Object)
], Incident.prototype, "closedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    __metadata("design:type", Object)
], Incident.prototype, "details", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Incident.prototype, "metrics", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'response_plan_id', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], Incident.prototype, "responsePlanId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'response_phase', nullable: true }),
    __metadata("design:type", String)
], Incident.prototype, "responsePhase", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'assigned_to', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], Incident.prototype, "assignedTo", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'response_team', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_f = typeof Array !== "undefined" && Array) === "function" ? _f : Object)
], Incident.prototype, "responseTeam", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, default: '{}' }),
    __metadata("design:type", Array)
], Incident.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['public', 'internal', 'confidential', 'restricted'],
        default: 'internal',
    }),
    __metadata("design:type", String)
], Incident.prototype, "confidentiality", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', type: 'uuid' }),
    __metadata("design:type", String)
], Incident.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], Incident.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_g = typeof Date !== "undefined" && Date) === "function" ? _g : Object)
], Incident.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_h = typeof Date !== "undefined" && Date) === "function" ? _h : Object)
], Incident.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => incident_task_entity_1.IncidentTask, task => task.incident, { cascade: true }),
    __metadata("design:type", Array)
], Incident.prototype, "tasks", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => incident_evidence_entity_1.IncidentEvidence, evidence => evidence.incident, { cascade: true }),
    __metadata("design:type", Array)
], Incident.prototype, "evidence", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => incident_communication_entity_1.IncidentCommunication, communication => communication.incident, { cascade: true }),
    __metadata("design:type", Array)
], Incident.prototype, "communications", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => incident_timeline_entity_1.IncidentTimeline, timeline => timeline.incident, { cascade: true }),
    __metadata("design:type", Array)
], Incident.prototype, "timeline", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => response_plan_entity_1.ResponsePlan, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'response_plan_id' }),
    __metadata("design:type", typeof (_j = typeof response_plan_entity_1.ResponsePlan !== "undefined" && response_plan_entity_1.ResponsePlan) === "function" ? _j : Object)
], Incident.prototype, "responsePlan", void 0);
exports.Incident = Incident = __decorate([
    (0, typeorm_1.Entity)('incidents'),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['severity']),
    (0, typeorm_1.Index)(['priority']),
    (0, typeorm_1.Index)(['category']),
    (0, typeorm_1.Index)(['assignedTo']),
    (0, typeorm_1.Index)(['createdAt']),
    (0, typeorm_1.Index)(['detectedAt'])
], Incident);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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