{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\threat.specification.ts", "mappings": ";;;AAAA,6DAA8E;AAE9E,wEAA+D;AAE/D;;;;;GAKG;AACH,MAAsB,mBAAoB,SAAQ,iCAAyB;IACzE;;OAEG;IACO,kBAAkB,CAAC,MAAc,EAAE,UAA4B;QACvE,OAAO,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACO,kBAAkB,CAAC,MAAc,EAAE,UAAoB;QAC/D,OAAO,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACO,cAAc,CAAC,MAAc,EAAE,KAAe;QACtD,OAAO,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACO,SAAS,CAAC,MAAc,EAAE,IAAc;QAChD,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACO,UAAU,CAAC,MAAc,EAAE,IAAc;QACjD,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACO,gBAAgB,CAAC,MAAc,EAAE,WAAoB,EAAE,WAAoB;QACnF,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;QAEjC,IAAI,WAAW,KAAK,SAAS,IAAI,QAAQ,GAAG,WAAW,EAAE,CAAC;YACxD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,WAAW,KAAK,SAAS,IAAI,QAAQ,GAAG,WAAW,EAAE,CAAC;YACxD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACO,uBAAuB,CAAC,MAAc,EAAE,aAAsB,EAAE,aAAsB;QAC9F,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;QAErC,IAAI,aAAa,KAAK,SAAS,IAAI,UAAU,GAAG,aAAa,EAAE,CAAC;YAC9D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,aAAa,KAAK,SAAS,IAAI,UAAU,GAAG,aAAa,EAAE,CAAC;YAC9D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACO,sBAAsB,CAAC,MAAc,EAAE,QAAiB,EAAE,QAAiB;QACnF,MAAM,SAAS,GAAG,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC;QAElD,IAAI,QAAQ,KAAK,SAAS,IAAI,SAAS,GAAG,QAAQ,EAAE,CAAC;YACnD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,QAAQ,KAAK,SAAS,IAAI,SAAS,GAAG,QAAQ,EAAE,CAAC;YACnD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAtFD,kDAsFC;AAED;;;;GAIG;AACH,MAAa,+BAAgC,SAAQ,mBAAmB;IACtE,aAAa,CAAC,MAAc;QAC1B,OAAO,MAAM,CAAC,cAAc,EAAE,CAAC;IACjC,CAAC;IAED,cAAc;QACZ,OAAO,sCAAsC,CAAC;IAChD,CAAC;CACF;AARD,0EAQC;AAED;;;;GAIG;AACH,MAAa,2BAA4B,SAAQ,mBAAmB;IAClE,aAAa,CAAC,MAAc;QAC1B,OAAO,MAAM,CAAC,QAAQ,KAAK,qCAAc,CAAC,QAAQ,CAAC;IACrD,CAAC;IAED,cAAc;QACZ,OAAO,8BAA8B,CAAC;IACxC,CAAC;CACF;AARD,kEAQC;AAED;;;;GAIG;AACH,MAAa,yBAA0B,SAAQ,mBAAmB;IAChE,aAAa,CAAC,MAAc;QAC1B,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC;IAC3B,CAAC;IAED,cAAc;QACZ,OAAO,iCAAiC,CAAC;IAC3C,CAAC;CACF;AARD,8DAQC;AAED;;;;GAIG;AACH,MAAa,2BAA4B,SAAQ,mBAAmB;IAClE,aAAa,CAAC,MAAc;QAC1B,OAAO,MAAM,CAAC,UAAU,EAAE,CAAC;IAC7B,CAAC;IAED,cAAc;QACZ,OAAO,0BAA0B,CAAC;IACpC,CAAC;CACF;AARD,kEAQC;AAED;;;;GAIG;AACH,MAAa,2BAA4B,SAAQ,mBAAmB;IAClE,YAA6B,eAAuB,EAAE;QACpD,KAAK,EAAE,CAAC;QADmB,iBAAY,GAAZ,YAAY,CAAa;IAEtD,CAAC;IAED,aAAa,CAAC,MAAc;QAC1B,OAAO,MAAM,CAAC,cAAc,CAAC,SAAS,IAAI,IAAI,CAAC,YAAY,CAAC;IAC9D,CAAC;IAED,cAAc;QACZ,OAAO,kCAAkC,IAAI,CAAC,YAAY,GAAG,CAAC;IAChE,CAAC;CACF;AAZD,kEAYC;AAED;;;;GAIG;AACH,MAAa,iCAAkC,SAAQ,mBAAmB;IACxE,YAA6B,gBAAwB,EAAE;QACrD,KAAK,EAAE,CAAC;QADmB,kBAAa,GAAb,aAAa,CAAa;IAEvD,CAAC;IAED,aAAa,CAAC,MAAc;QAC1B,OAAO,MAAM,CAAC,UAAU,IAAI,IAAI,CAAC,aAAa,CAAC;IACjD,CAAC;IAED,cAAc;QACZ,OAAO,kCAAkC,IAAI,CAAC,aAAa,GAAG,CAAC;IACjE,CAAC;CACF;AAZD,8EAYC;AAED;;;;GAIG;AACH,MAAa,yBAA0B,SAAQ,mBAAmB;IAChE,YAA6B,cAAsB,EAAE;QACnD,KAAK,EAAE,CAAC;QADmB,gBAAW,GAAX,WAAW,CAAa;IAErD,CAAC;IAED,aAAa,CAAC,MAAc;QAC1B,OAAO,MAAM,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,WAAW,CAAC;IAC7C,CAAC;IAED,cAAc;QACZ,OAAO,8BAA8B,IAAI,CAAC,WAAW,QAAQ,CAAC;IAChE,CAAC;CACF;AAZD,8DAYC;AAED;;;;GAIG;AACH,MAAa,wBAAyB,SAAQ,mBAAmB;IAC/D,YAA6B,iBAAyB,GAAG;QACvD,KAAK,EAAE,CAAC;QADmB,mBAAc,GAAd,cAAc,CAAc;IAEzD,CAAC;IAED,aAAa,CAAC,MAAc;QAC1B,OAAO,MAAM,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC;IAC/C,CAAC;IAED,cAAc;QACZ,OAAO,wBAAwB,IAAI,CAAC,cAAc,QAAQ,CAAC;IAC7D,CAAC;CACF;AAZD,4DAYC;AAED;;;;GAIG;AACH,MAAa,2BAA4B,SAAQ,mBAAmB;IAClE,YAA6B,UAA4B;QACvD,KAAK,EAAE,CAAC;QADmB,eAAU,GAAV,UAAU,CAAkB;IAEzD,CAAC;IAED,aAAa,CAAC,MAAc;QAC1B,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IAC1D,CAAC;IAED,cAAc;QACZ,OAAO,8BAA8B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IACpE,CAAC;CACF;AAZD,kEAYC;AAED;;;;GAIG;AACH,MAAa,2BAA4B,SAAQ,mBAAmB;IAClE,YAA6B,UAAoB;QAC/C,KAAK,EAAE,CAAC;QADmB,eAAU,GAAV,UAAU,CAAU;IAEjD,CAAC;IAED,aAAa,CAAC,MAAc;QAC1B,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IAC1D,CAAC;IAED,cAAc;QACZ,OAAO,8BAA8B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IACpE,CAAC;CACF;AAZD,kEAYC;AAED;;;;GAIG;AACH,MAAa,uBAAwB,SAAQ,mBAAmB;IAC9D,YAA6B,KAAe;QAC1C,KAAK,EAAE,CAAC;QADmB,UAAK,GAAL,KAAK,CAAU;IAE5C,CAAC;IAED,aAAa,CAAC,MAAc;QAC1B,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IACjD,CAAC;IAED,cAAc;QACZ,OAAO,0BAA0B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAC3D,CAAC;CACF;AAZD,0DAYC;AAED;;;;GAIG;AACH,MAAa,sBAAuB,SAAQ,mBAAmB;IAC7D,YACmB,IAAc,EACd,aAAsB,KAAK;QAE5C,KAAK,EAAE,CAAC;QAHS,SAAI,GAAJ,IAAI,CAAU;QACd,eAAU,GAAV,UAAU,CAAiB;IAG9C,CAAC;IAED,aAAa,CAAC,MAAc;QAC1B,OAAO,IAAI,CAAC,UAAU;YACpB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC;YACpC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;IAED,cAAc;QACZ,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;QACjD,OAAO,cAAc,QAAQ,mBAAmB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IACzE,CAAC;CACF;AAlBD,wDAkBC;AAED;;;;GAIG;AACH,MAAa,iCAAkC,SAAQ,mBAAmB;IACxE,YACmB,QAAiB,EACjB,QAAiB;QAElC,KAAK,EAAE,CAAC;QAHS,aAAQ,GAAR,QAAQ,CAAS;QACjB,aAAQ,GAAR,QAAQ,CAAS;IAGpC,CAAC;IAED,aAAa,CAAC,MAAc;QAC1B,OAAO,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC3E,CAAC;IAED,cAAc;QACZ,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC/D,OAAO,gCAAgC,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9E,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,iCAAiC,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC1D,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,gCAAgC,IAAI,CAAC,QAAQ,EAAE,CAAC;QACzD,CAAC;QACD,OAAO,2BAA2B,CAAC;IACrC,CAAC;CACF;AAtBD,8EAsBC;AAED;;;;GAIG;AACH,MAAa,kCAAmC,SAAQ,mBAAmB;IACzE,YACmB,aAAsB,EACtB,aAAsB;QAEvC,KAAK,EAAE,CAAC;QAHS,kBAAa,GAAb,aAAa,CAAS;QACtB,kBAAa,GAAb,aAAa,CAAS;IAGzC,CAAC;IAED,aAAa,CAAC,MAAc;QAC1B,OAAO,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACtF,CAAC;IAED,cAAc;QACZ,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YACzE,OAAO,gCAAgC,IAAI,CAAC,aAAa,QAAQ,IAAI,CAAC,aAAa,EAAE,CAAC;QACxF,CAAC;aAAM,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YAC5C,OAAO,iCAAiC,IAAI,CAAC,aAAa,EAAE,CAAC;QAC/D,CAAC;aAAM,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YAC5C,OAAO,gCAAgC,IAAI,CAAC,aAAa,EAAE,CAAC;QAC9D,CAAC;QACD,OAAO,iCAAiC,CAAC;IAC3C,CAAC;CACF;AAtBD,gFAsBC;AAED;;;;GAIG;AACH,MAAa,2BAA4B,SAAQ,mBAAmB;IAClE,YACmB,WAAoB,EACpB,WAAoB;QAErC,KAAK,EAAE,CAAC;QAHS,gBAAW,GAAX,WAAW,CAAS;QACpB,gBAAW,GAAX,WAAW,CAAS;IAGvC,CAAC;IAED,aAAa,CAAC,MAAc;QAC1B,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAC3E,CAAC;IAED,cAAc;QACZ,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;YACrE,OAAO,yBAAyB,IAAI,CAAC,WAAW,QAAQ,IAAI,CAAC,WAAW,QAAQ,CAAC;QACnF,CAAC;aAAM,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;YAC1C,OAAO,sBAAsB,IAAI,CAAC,WAAW,YAAY,CAAC;QAC5D,CAAC;aAAM,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;YAC1C,OAAO,qBAAqB,IAAI,CAAC,WAAW,YAAY,CAAC;QAC3D,CAAC;QACD,OAAO,oBAAoB,CAAC;IAC9B,CAAC;CACF;AAtBD,kEAsBC;AAED;;;;GAIG;AACH,MAAa,8BAA+B,SAAQ,mBAAmB;IACrE,YACmB,KAAc,EACd,QAAiB,EACjB,iBAA0B,IAAI;QAE/C,KAAK,EAAE,CAAC;QAJS,UAAK,GAAL,KAAK,CAAS;QACd,aAAQ,GAAR,QAAQ,CAAS;QACjB,mBAAc,GAAd,cAAc,CAAgB;IAGjD,CAAC;IAED,aAAa,CAAC,MAAc;QAC1B,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;QAEvC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,OAAO,CAAC,WAAW,CAAC;QACtB,CAAC;QAED,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,IAAI,WAAW,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE,CAAC;YACnD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACpE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,cAAc;QACZ,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,OAAO,uCAAuC,CAAC;QACjD,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChC,OAAO,kCAAkC,IAAI,CAAC,KAAK,kBAAkB,IAAI,CAAC,QAAQ,GAAG,CAAC;QACxF,CAAC;aAAM,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACtB,OAAO,kCAAkC,IAAI,CAAC,KAAK,GAAG,CAAC;QACzD,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACzB,OAAO,+BAA+B,IAAI,CAAC,QAAQ,GAAG,CAAC;QACzD,CAAC;QAED,OAAO,oCAAoC,CAAC;IAC9C,CAAC;CACF;AA9CD,wEA8CC;AAED;;;;GAIG;AACH,MAAa,gCAAiC,SAAQ,mBAAmB;IACvE,YACmB,UAAmB,EACnB,mBAA4B,IAAI;QAEjD,KAAK,EAAE,CAAC;QAHS,eAAU,GAAV,UAAU,CAAS;QACnB,qBAAgB,GAAhB,gBAAgB,CAAgB;IAGnD,CAAC;IAED,aAAa,CAAC,MAAc;QAC1B,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;QAE3C,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,OAAO,CAAC,aAAa,CAAC;QACxB,CAAC;QAED,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,IAAI,aAAa,CAAC,IAAI,KAAK,IAAI,CAAC,UAAU,EAAE,CAAC;YAC9D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,cAAc;QACZ,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,OAAO,0CAA0C,CAAC;QACpD,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,OAAO,6CAA6C,IAAI,CAAC,UAAU,GAAG,CAAC;QACzE,CAAC;QAED,OAAO,uCAAuC,CAAC;IACjD,CAAC;CACF;AArCD,4EAqCC;AAED;;;;GAIG;AACH,MAAa,uCAAwC,SAAQ,mBAAmB;IAC9E,aAAa,CAAC,MAAc;QAC1B,OAAO,MAAM,CAAC,0BAA0B,EAAE,CAAC;IAC7C,CAAC;IAED,cAAc;QACZ,OAAO,+FAA+F,CAAC;IACzG,CAAC;CACF;AARD,0FAQC;AAED;;;;GAIG;AACH,MAAa,2BAA4B,SAAQ,mBAAmB;IAClE,YACmB,UAAqB,EACrB,aAAsB,EACtB,aAAsB;QAEvC,KAAK,EAAE,CAAC;QAJS,eAAU,GAAV,UAAU,CAAW;QACrB,kBAAa,GAAb,aAAa,CAAS;QACtB,kBAAa,GAAb,aAAa,CAAS;IAGzC,CAAC;IAED,aAAa,CAAC,MAAc;QAC1B,MAAM,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;QAE7C,0BAA0B;QAC1B,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,IAAI,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YACnF,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,IAAI,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YACnF,OAAO,KAAK,CAAC;QACf,CAAC;QAED,6BAA6B;QAC7B,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;QAC/E,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,cAAc;QACZ,MAAM,UAAU,GAAa,EAAE,CAAC;QAEhC,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YACzE,UAAU,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,SAAS,CAAC,CAAC;QAChF,CAAC;aAAM,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YAC5C,UAAU,CAAC,IAAI,CAAC,oBAAoB,IAAI,CAAC,aAAa,SAAS,CAAC,CAAC;QACnE,CAAC;aAAM,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YAC5C,UAAU,CAAC,IAAI,CAAC,mBAAmB,IAAI,CAAC,aAAa,SAAS,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClD,UAAU,CAAC,IAAI,CAAC,mBAAmB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACnE,CAAC;QAED,OAAO,UAAU,CAAC,MAAM,GAAG,CAAC;YAC1B,CAAC,CAAC,UAAU,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YACtC,CAAC,CAAC,qCAAqC,CAAC;IAC5C,CAAC;CACF;AAhDD,kEAgDC;AAED;;;;GAIG;AACH,MAAa,4BAA6B,SAAQ,mBAAmB;IACnE,YACmB,QAAiB,EACjB,QAAiB;QAElC,KAAK,EAAE,CAAC;QAHS,aAAQ,GAAR,QAAQ,CAAS;QACjB,aAAQ,GAAR,QAAQ,CAAS;IAGpC,CAAC;IAED,aAAa,CAAC,MAAc;QAC1B,MAAM,cAAc,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC;QAEhD,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,cAAc,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,cAAc,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,cAAc;QACZ,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC/D,OAAO,cAAc,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,aAAa,CAAC;QACnE,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,uBAAuB,IAAI,CAAC,QAAQ,aAAa,CAAC;QAC3D,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,sBAAsB,IAAI,CAAC,QAAQ,aAAa,CAAC;QAC1D,CAAC;QACD,OAAO,qCAAqC,CAAC;IAC/C,CAAC;CACF;AAhCD,oEAgCC;AAED;;;;GAIG;AACH,MAAa,0BAA0B;IAAvC;QACU,mBAAc,GAA0B,EAAE,CAAC;IAkMrD,CAAC;IAhMC;;OAEG;IACH,YAAY;QACV,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,+BAA+B,EAAE,CAAC,CAAC;QAChE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,2BAA2B,EAAE,CAAC,CAAC;QAC5D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,yBAAyB,EAAE,CAAC,CAAC;QAC1D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,2BAA2B,EAAE,CAAC,CAAC;QAC5D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,eAAuB,EAAE;QAChC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,2BAA2B,CAAC,YAAY,CAAC,CAAC,CAAC;QACxE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,gBAAwB,EAAE;QACvC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,iCAAiC,CAAC,aAAa,CAAC,CAAC,CAAC;QAC/E,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAsB,EAAE;QAC7B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,yBAAyB,CAAC,WAAW,CAAC,CAAC,CAAC;QACrE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,GAAG,UAA4B;QAC5C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,2BAA2B,CAAC,UAAU,CAAC,CAAC,CAAC;QACtE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,GAAG,UAAoB;QACpC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,2BAA2B,CAAC,UAAU,CAAC,CAAC,CAAC;QACtE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,GAAG,KAAe;QAC1B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,uBAAuB,CAAC,KAAK,CAAC,CAAC,CAAC;QAC7D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,IAAc,EAAE,aAAsB,KAAK;QAClD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,sBAAsB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;QACvE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,QAAiB,EAAE,QAAiB;QACjD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,iCAAiC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;QACpF,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,aAAsB,EAAE,aAAsB;QAC5D,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,kCAAkC,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC,CAAC;QAC/F,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,0BAA0B;QACxB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,uCAAuC,EAAE,CAAC,CAAC;QACxE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,KAAc,EAAE,QAAiB;QAC/C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,8BAA8B,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC;QACpF,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,UAAmB;QACnC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,gCAAgC,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC;QACjF,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,UAAqB,EAAE,QAAiB,EAAE,QAAiB;QACzE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,2BAA2B,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC1F,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,QAAiB,EAAE,QAAiB;QACrD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,4BAA4B,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC/E,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC;QAED,4CAA4C;QAC5C,IAAI,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpD,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAwB,CAAC;QACzE,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,WAAW;QACT,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC;QAED,2CAA2C;QAC3C,IAAI,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpD,QAAQ,GAAG,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAwB,CAAC;QACxE,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM;QACX,OAAO,IAAI,0BAA0B,EAAE,CAAC;IAC1C,CAAC;CACF;AAnMD,gEAmMC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\threat.specification.ts"], "sourcesContent": ["import { BaseSpecification, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { Threat } from '../entities/threat/threat.entity';\r\nimport { ThreatSeverity } from '../enums/threat-severity.enum';\r\n\r\n/**\r\n * Threat Specification Base Class\r\n * \r\n * Base class for all threat-related specifications.\r\n * Provides common functionality for threat filtering and validation.\r\n */\r\nexport abstract class ThreatSpecification extends BaseSpecification<Threat> {\r\n  /**\r\n   * Helper method to check if threat matches any of the provided severities\r\n   */\r\n  protected matchesAnySeverity(threat: Threat, severities: ThreatSeverity[]): boolean {\r\n    return severities.includes(threat.severity);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if threat matches any of the provided categories\r\n   */\r\n  protected matchesAnyCategory(threat: Threat, categories: string[]): boolean {\r\n    return categories.includes(threat.category);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if threat matches any of the provided types\r\n   */\r\n  protected matchesAnyType(threat: Threat, types: string[]): boolean {\r\n    return types.includes(threat.type);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if threat has any of the provided tags\r\n   */\r\n  protected hasAnyTag(threat: Threat, tags: string[]): boolean {\r\n    return threat.tags.some(tag => tags.includes(tag));\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if threat has all of the provided tags\r\n   */\r\n  protected hasAllTags(threat: Threat, tags: string[]): boolean {\r\n    return tags.every(tag => threat.tags.includes(tag));\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if threat age is within range\r\n   */\r\n  protected isAgeWithinRange(threat: Threat, minAgeHours?: number, maxAgeHours?: number): boolean {\r\n    const ageHours = threat.getAge();\r\n    \r\n    if (minAgeHours !== undefined && ageHours < minAgeHours) {\r\n      return false;\r\n    }\r\n    \r\n    if (maxAgeHours !== undefined && ageHours > maxAgeHours) {\r\n      return false;\r\n    }\r\n    \r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if confidence is within range\r\n   */\r\n  protected isConfidenceWithinRange(threat: Threat, minConfidence?: number, maxConfidence?: number): boolean {\r\n    const confidence = threat.confidence;\r\n    \r\n    if (minConfidence !== undefined && confidence < minConfidence) {\r\n      return false;\r\n    }\r\n    \r\n    if (maxConfidence !== undefined && confidence > maxConfidence) {\r\n      return false;\r\n    }\r\n    \r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if risk score is within range\r\n   */\r\n  protected isRiskScoreWithinRange(threat: Threat, minScore?: number, maxScore?: number): boolean {\r\n    const riskScore = threat.riskAssessment.riskScore;\r\n    \r\n    if (minScore !== undefined && riskScore < minScore) {\r\n      return false;\r\n    }\r\n    \r\n    if (maxScore !== undefined && riskScore > maxScore) {\r\n      return false;\r\n    }\r\n    \r\n    return true;\r\n  }\r\n}\r\n\r\n/**\r\n * High Severity Threat Specification\r\n * \r\n * Specification for threats with high or critical severity.\r\n */\r\nexport class HighSeverityThreatSpecification extends ThreatSpecification {\r\n  isSatisfiedBy(threat: Threat): boolean {\r\n    return threat.isHighSeverity();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Threat has high or critical severity';\r\n  }\r\n}\r\n\r\n/**\r\n * Critical Threat Specification\r\n * \r\n * Specification for threats with critical severity only.\r\n */\r\nexport class CriticalThreatSpecification extends ThreatSpecification {\r\n  isSatisfiedBy(threat: Threat): boolean {\r\n    return threat.severity === ThreatSeverity.CRITICAL;\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Threat has critical severity';\r\n  }\r\n}\r\n\r\n/**\r\n * Active Threat Specification\r\n * \r\n * Specification for threats that are currently active (not resolved).\r\n */\r\nexport class ActiveThreatSpecification extends ThreatSpecification {\r\n  isSatisfiedBy(threat: Threat): boolean {\r\n    return threat.isActive();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Threat is active (not resolved)';\r\n  }\r\n}\r\n\r\n/**\r\n * Resolved Threat Specification\r\n * \r\n * Specification for threats that have been resolved.\r\n */\r\nexport class ResolvedThreatSpecification extends ThreatSpecification {\r\n  isSatisfiedBy(threat: Threat): boolean {\r\n    return threat.isResolved();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Threat has been resolved';\r\n  }\r\n}\r\n\r\n/**\r\n * High Risk Threat Specification\r\n * \r\n * Specification for threats with high risk scores.\r\n */\r\nexport class HighRiskThreatSpecification extends ThreatSpecification {\r\n  constructor(private readonly minRiskScore: number = 70) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(threat: Threat): boolean {\r\n    return threat.riskAssessment.riskScore >= this.minRiskScore;\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Threat has high risk score (>= ${this.minRiskScore})`;\r\n  }\r\n}\r\n\r\n/**\r\n * High Confidence Threat Specification\r\n * \r\n * Specification for threats with high confidence levels.\r\n */\r\nexport class HighConfidenceThreatSpecification extends ThreatSpecification {\r\n  constructor(private readonly minConfidence: number = 80) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(threat: Threat): boolean {\r\n    return threat.confidence >= this.minConfidence;\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Threat has high confidence (>= ${this.minConfidence})`;\r\n  }\r\n}\r\n\r\n/**\r\n * Recent Threat Specification\r\n * \r\n * Specification for threats that were detected recently.\r\n */\r\nexport class RecentThreatSpecification extends ThreatSpecification {\r\n  constructor(private readonly withinHours: number = 24) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(threat: Threat): boolean {\r\n    return threat.getAge() <= this.withinHours;\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Threat was detected within ${this.withinHours} hours`;\r\n  }\r\n}\r\n\r\n/**\r\n * Stale Threat Specification\r\n * \r\n * Specification for threats that are considered stale.\r\n */\r\nexport class StaleThreatSpecification extends ThreatSpecification {\r\n  constructor(private readonly olderThanHours: number = 168) { // Default 7 days\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(threat: Threat): boolean {\r\n    return threat.getAge() > this.olderThanHours;\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Threat is older than ${this.olderThanHours} hours`;\r\n  }\r\n}\r\n\r\n/**\r\n * Threat Severity Specification\r\n * \r\n * Specification for threats of specific severities.\r\n */\r\nexport class ThreatSeveritySpecification extends ThreatSpecification {\r\n  constructor(private readonly severities: ThreatSeverity[]) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(threat: Threat): boolean {\r\n    return this.matchesAnySeverity(threat, this.severities);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Threat severity is one of: ${this.severities.join(', ')}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Threat Category Specification\r\n * \r\n * Specification for threats of specific categories.\r\n */\r\nexport class ThreatCategorySpecification extends ThreatSpecification {\r\n  constructor(private readonly categories: string[]) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(threat: Threat): boolean {\r\n    return this.matchesAnyCategory(threat, this.categories);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Threat category is one of: ${this.categories.join(', ')}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Threat Type Specification\r\n * \r\n * Specification for threats of specific types.\r\n */\r\nexport class ThreatTypeSpecification extends ThreatSpecification {\r\n  constructor(private readonly types: string[]) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(threat: Threat): boolean {\r\n    return this.matchesAnyType(threat, this.types);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Threat type is one of: ${this.types.join(', ')}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Threat Tag Specification\r\n * \r\n * Specification for threats with specific tags.\r\n */\r\nexport class ThreatTagSpecification extends ThreatSpecification {\r\n  constructor(\r\n    private readonly tags: string[],\r\n    private readonly requireAll: boolean = false\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(threat: Threat): boolean {\r\n    return this.requireAll \r\n      ? this.hasAllTags(threat, this.tags)\r\n      : this.hasAnyTag(threat, this.tags);\r\n  }\r\n\r\n  getDescription(): string {\r\n    const operator = this.requireAll ? 'all' : 'any';\r\n    return `Threat has ${operator} of these tags: ${this.tags.join(', ')}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Threat Risk Score Range Specification\r\n * \r\n * Specification for threats within a specific risk score range.\r\n */\r\nexport class ThreatRiskScoreRangeSpecification extends ThreatSpecification {\r\n  constructor(\r\n    private readonly minScore?: number,\r\n    private readonly maxScore?: number\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(threat: Threat): boolean {\r\n    return this.isRiskScoreWithinRange(threat, this.minScore, this.maxScore);\r\n  }\r\n\r\n  getDescription(): string {\r\n    if (this.minScore !== undefined && this.maxScore !== undefined) {\r\n      return `Threat risk score is between ${this.minScore} and ${this.maxScore}`;\r\n    } else if (this.minScore !== undefined) {\r\n      return `Threat risk score is at least ${this.minScore}`;\r\n    } else if (this.maxScore !== undefined) {\r\n      return `Threat risk score is at most ${this.maxScore}`;\r\n    }\r\n    return 'Threat has any risk score';\r\n  }\r\n}\r\n\r\n/**\r\n * Threat Confidence Range Specification\r\n * \r\n * Specification for threats within a specific confidence range.\r\n */\r\nexport class ThreatConfidenceRangeSpecification extends ThreatSpecification {\r\n  constructor(\r\n    private readonly minConfidence?: number,\r\n    private readonly maxConfidence?: number\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(threat: Threat): boolean {\r\n    return this.isConfidenceWithinRange(threat, this.minConfidence, this.maxConfidence);\r\n  }\r\n\r\n  getDescription(): string {\r\n    if (this.minConfidence !== undefined && this.maxConfidence !== undefined) {\r\n      return `Threat confidence is between ${this.minConfidence} and ${this.maxConfidence}`;\r\n    } else if (this.minConfidence !== undefined) {\r\n      return `Threat confidence is at least ${this.minConfidence}`;\r\n    } else if (this.maxConfidence !== undefined) {\r\n      return `Threat confidence is at most ${this.maxConfidence}`;\r\n    }\r\n    return 'Threat has any confidence level';\r\n  }\r\n}\r\n\r\n/**\r\n * Threat Age Range Specification\r\n * \r\n * Specification for threats within a specific age range.\r\n */\r\nexport class ThreatAgeRangeSpecification extends ThreatSpecification {\r\n  constructor(\r\n    private readonly minAgeHours?: number,\r\n    private readonly maxAgeHours?: number\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(threat: Threat): boolean {\r\n    return this.isAgeWithinRange(threat, this.minAgeHours, this.maxAgeHours);\r\n  }\r\n\r\n  getDescription(): string {\r\n    if (this.minAgeHours !== undefined && this.maxAgeHours !== undefined) {\r\n      return `Threat age is between ${this.minAgeHours} and ${this.maxAgeHours} hours`;\r\n    } else if (this.minAgeHours !== undefined) {\r\n      return `Threat is at least ${this.minAgeHours} hours old`;\r\n    } else if (this.maxAgeHours !== undefined) {\r\n      return `Threat is at most ${this.maxAgeHours} hours old`;\r\n    }\r\n    return 'Threat has any age';\r\n  }\r\n}\r\n\r\n/**\r\n * Threat Attribution Specification\r\n * \r\n * Specification for threats with specific attribution properties.\r\n */\r\nexport class ThreatAttributionSpecification extends ThreatSpecification {\r\n  constructor(\r\n    private readonly actor?: string,\r\n    private readonly campaign?: string,\r\n    private readonly hasAttribution: boolean = true\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(threat: Threat): boolean {\r\n    const attribution = threat.attribution;\r\n    \r\n    if (!this.hasAttribution) {\r\n      return !attribution;\r\n    }\r\n    \r\n    if (!attribution) {\r\n      return false;\r\n    }\r\n    \r\n    if (this.actor && attribution.actor !== this.actor) {\r\n      return false;\r\n    }\r\n    \r\n    if (this.campaign && !attribution.campaigns.includes(this.campaign)) {\r\n      return false;\r\n    }\r\n    \r\n    return true;\r\n  }\r\n\r\n  getDescription(): string {\r\n    if (!this.hasAttribution) {\r\n      return 'Threat has no attribution information';\r\n    }\r\n    \r\n    if (this.actor && this.campaign) {\r\n      return `Threat is attributed to actor \"${this.actor}\" in campaign \"${this.campaign}\"`;\r\n    } else if (this.actor) {\r\n      return `Threat is attributed to actor \"${this.actor}\"`;\r\n    } else if (this.campaign) {\r\n      return `Threat is part of campaign \"${this.campaign}\"`;\r\n    }\r\n    \r\n    return 'Threat has attribution information';\r\n  }\r\n}\r\n\r\n/**\r\n * Threat Malware Family Specification\r\n * \r\n * Specification for threats associated with specific malware families.\r\n */\r\nexport class ThreatMalwareFamilySpecification extends ThreatSpecification {\r\n  constructor(\r\n    private readonly familyName?: string,\r\n    private readonly hasMalwareFamily: boolean = true\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(threat: Threat): boolean {\r\n    const malwareFamily = threat.malwareFamily;\r\n    \r\n    if (!this.hasMalwareFamily) {\r\n      return !malwareFamily;\r\n    }\r\n    \r\n    if (!malwareFamily) {\r\n      return false;\r\n    }\r\n    \r\n    if (this.familyName && malwareFamily.name !== this.familyName) {\r\n      return false;\r\n    }\r\n    \r\n    return true;\r\n  }\r\n\r\n  getDescription(): string {\r\n    if (!this.hasMalwareFamily) {\r\n      return 'Threat has no malware family information';\r\n    }\r\n    \r\n    if (this.familyName) {\r\n      return `Threat is associated with malware family \"${this.familyName}\"`;\r\n    }\r\n    \r\n    return 'Threat has malware family information';\r\n  }\r\n}\r\n\r\n/**\r\n * Requires Immediate Attention Specification\r\n * \r\n * Specification for threats that require immediate attention.\r\n */\r\nexport class RequiresImmediateAttentionSpecification extends ThreatSpecification {\r\n  isSatisfiedBy(threat: Threat): boolean {\r\n    return threat.requiresImmediateAttention();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Threat requires immediate attention (critical severity or high severity with high confidence)';\r\n  }\r\n}\r\n\r\n/**\r\n * Affected Assets Specification\r\n * \r\n * Specification for threats affecting specific assets or asset counts.\r\n */\r\nexport class AffectedAssetsSpecification extends ThreatSpecification {\r\n  constructor(\r\n    private readonly assetNames?: string[],\r\n    private readonly minAssetCount?: number,\r\n    private readonly maxAssetCount?: number\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(threat: Threat): boolean {\r\n    const affectedAssets = threat.affectedAssets;\r\n    \r\n    // Check asset count range\r\n    if (this.minAssetCount !== undefined && affectedAssets.length < this.minAssetCount) {\r\n      return false;\r\n    }\r\n    \r\n    if (this.maxAssetCount !== undefined && affectedAssets.length > this.maxAssetCount) {\r\n      return false;\r\n    }\r\n    \r\n    // Check specific asset names\r\n    if (this.assetNames && this.assetNames.length > 0) {\r\n      return this.assetNames.some(assetName => affectedAssets.includes(assetName));\r\n    }\r\n    \r\n    return true;\r\n  }\r\n\r\n  getDescription(): string {\r\n    const conditions: string[] = [];\r\n    \r\n    if (this.minAssetCount !== undefined && this.maxAssetCount !== undefined) {\r\n      conditions.push(`affects ${this.minAssetCount}-${this.maxAssetCount} assets`);\r\n    } else if (this.minAssetCount !== undefined) {\r\n      conditions.push(`affects at least ${this.minAssetCount} assets`);\r\n    } else if (this.maxAssetCount !== undefined) {\r\n      conditions.push(`affects at most ${this.maxAssetCount} assets`);\r\n    }\r\n    \r\n    if (this.assetNames && this.assetNames.length > 0) {\r\n      conditions.push(`affects assets: ${this.assetNames.join(', ')}`);\r\n    }\r\n    \r\n    return conditions.length > 0 \r\n      ? `Threat ${conditions.join(' and ')}`\r\n      : 'Threat affects any number of assets';\r\n  }\r\n}\r\n\r\n/**\r\n * Indicators Count Specification\r\n * \r\n * Specification for threats with specific indicator counts.\r\n */\r\nexport class IndicatorsCountSpecification extends ThreatSpecification {\r\n  constructor(\r\n    private readonly minCount?: number,\r\n    private readonly maxCount?: number\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(threat: Threat): boolean {\r\n    const indicatorCount = threat.indicators.length;\r\n    \r\n    if (this.minCount !== undefined && indicatorCount < this.minCount) {\r\n      return false;\r\n    }\r\n    \r\n    if (this.maxCount !== undefined && indicatorCount > this.maxCount) {\r\n      return false;\r\n    }\r\n    \r\n    return true;\r\n  }\r\n\r\n  getDescription(): string {\r\n    if (this.minCount !== undefined && this.maxCount !== undefined) {\r\n      return `Threat has ${this.minCount}-${this.maxCount} indicators`;\r\n    } else if (this.minCount !== undefined) {\r\n      return `Threat has at least ${this.minCount} indicators`;\r\n    } else if (this.maxCount !== undefined) {\r\n      return `Threat has at most ${this.maxCount} indicators`;\r\n    }\r\n    return 'Threat has any number of indicators';\r\n  }\r\n}\r\n\r\n/**\r\n * Composite Threat Specification Builder\r\n * \r\n * Builder for creating complex threat specifications using fluent interface.\r\n */\r\nexport class ThreatSpecificationBuilder {\r\n  private specifications: ThreatSpecification[] = [];\r\n\r\n  /**\r\n   * Add high severity filter\r\n   */\r\n  highSeverity(): ThreatSpecificationBuilder {\r\n    this.specifications.push(new HighSeverityThreatSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add critical severity filter\r\n   */\r\n  critical(): ThreatSpecificationBuilder {\r\n    this.specifications.push(new CriticalThreatSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add active status filter\r\n   */\r\n  active(): ThreatSpecificationBuilder {\r\n    this.specifications.push(new ActiveThreatSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add resolved status filter\r\n   */\r\n  resolved(): ThreatSpecificationBuilder {\r\n    this.specifications.push(new ResolvedThreatSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add high risk filter\r\n   */\r\n  highRisk(minRiskScore: number = 70): ThreatSpecificationBuilder {\r\n    this.specifications.push(new HighRiskThreatSpecification(minRiskScore));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add high confidence filter\r\n   */\r\n  highConfidence(minConfidence: number = 80): ThreatSpecificationBuilder {\r\n    this.specifications.push(new HighConfidenceThreatSpecification(minConfidence));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add recent threats filter\r\n   */\r\n  recent(withinHours: number = 24): ThreatSpecificationBuilder {\r\n    this.specifications.push(new RecentThreatSpecification(withinHours));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add severity filter\r\n   */\r\n  withSeverities(...severities: ThreatSeverity[]): ThreatSpecificationBuilder {\r\n    this.specifications.push(new ThreatSeveritySpecification(severities));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add category filter\r\n   */\r\n  withCategories(...categories: string[]): ThreatSpecificationBuilder {\r\n    this.specifications.push(new ThreatCategorySpecification(categories));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add type filter\r\n   */\r\n  withTypes(...types: string[]): ThreatSpecificationBuilder {\r\n    this.specifications.push(new ThreatTypeSpecification(types));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add tag filter\r\n   */\r\n  withTags(tags: string[], requireAll: boolean = false): ThreatSpecificationBuilder {\r\n    this.specifications.push(new ThreatTagSpecification(tags, requireAll));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add risk score range filter\r\n   */\r\n  riskScoreRange(minScore?: number, maxScore?: number): ThreatSpecificationBuilder {\r\n    this.specifications.push(new ThreatRiskScoreRangeSpecification(minScore, maxScore));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add confidence range filter\r\n   */\r\n  confidenceRange(minConfidence?: number, maxConfidence?: number): ThreatSpecificationBuilder {\r\n    this.specifications.push(new ThreatConfidenceRangeSpecification(minConfidence, maxConfidence));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add requires immediate attention filter\r\n   */\r\n  requiresImmediateAttention(): ThreatSpecificationBuilder {\r\n    this.specifications.push(new RequiresImmediateAttentionSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add attribution filter\r\n   */\r\n  withAttribution(actor?: string, campaign?: string): ThreatSpecificationBuilder {\r\n    this.specifications.push(new ThreatAttributionSpecification(actor, campaign, true));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add malware family filter\r\n   */\r\n  withMalwareFamily(familyName?: string): ThreatSpecificationBuilder {\r\n    this.specifications.push(new ThreatMalwareFamilySpecification(familyName, true));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add affected assets filter\r\n   */\r\n  affectingAssets(assetNames?: string[], minCount?: number, maxCount?: number): ThreatSpecificationBuilder {\r\n    this.specifications.push(new AffectedAssetsSpecification(assetNames, minCount, maxCount));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add indicators count filter\r\n   */\r\n  withIndicatorCount(minCount?: number, maxCount?: number): ThreatSpecificationBuilder {\r\n    this.specifications.push(new IndicatorsCountSpecification(minCount, maxCount));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Build the final specification using AND logic\r\n   */\r\n  build(): ThreatSpecification {\r\n    if (this.specifications.length === 0) {\r\n      throw new Error('At least one specification must be added');\r\n    }\r\n\r\n    if (this.specifications.length === 1) {\r\n      return this.specifications[0];\r\n    }\r\n\r\n    // Combine all specifications with AND logic\r\n    let combined = this.specifications[0];\r\n    for (let i = 1; i < this.specifications.length; i++) {\r\n      combined = combined.and(this.specifications[i]) as ThreatSpecification;\r\n    }\r\n\r\n    return combined;\r\n  }\r\n\r\n  /**\r\n   * Build the final specification using OR logic\r\n   */\r\n  buildWithOr(): ThreatSpecification {\r\n    if (this.specifications.length === 0) {\r\n      throw new Error('At least one specification must be added');\r\n    }\r\n\r\n    if (this.specifications.length === 1) {\r\n      return this.specifications[0];\r\n    }\r\n\r\n    // Combine all specifications with OR logic\r\n    let combined = this.specifications[0];\r\n    for (let i = 1; i < this.specifications.length; i++) {\r\n      combined = combined.or(this.specifications[i]) as ThreatSpecification;\r\n    }\r\n\r\n    return combined;\r\n  }\r\n\r\n  /**\r\n   * Create a new builder instance\r\n   */\r\n  static create(): ThreatSpecificationBuilder {\r\n    return new ThreatSpecificationBuilder();\r\n  }\r\n}"], "version": 3}