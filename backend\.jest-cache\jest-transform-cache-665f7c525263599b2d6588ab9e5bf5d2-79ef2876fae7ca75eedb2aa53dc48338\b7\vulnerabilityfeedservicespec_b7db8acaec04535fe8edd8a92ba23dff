9d6472a88438790c143f374f2274a002
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const vulnerability_feed_service_1 = require("../vulnerability-feed.service");
const vulnerability_feed_entity_1 = require("../../../domain/entities/vulnerability-feed.entity");
const vulnerability_entity_1 = require("../../../domain/entities/vulnerability.entity");
const logger_service_1 = require("../../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../../infrastructure/logging/audit/audit.service");
const vulnerability_feed_integration_service_1 = require("../../../infrastructure/services/vulnerability-feed-integration.service");
describe('VulnerabilityFeedService', () => {
    let service;
    let feedRepository;
    let vulnerabilityRepository;
    let loggerService;
    let auditService;
    let feedIntegrationService;
    const mockFeed = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        name: 'Test Feed',
        description: 'Test feed description',
        feedType: 'nvd',
        provider: 'NIST',
        url: 'https://services.nvd.nist.gov/rest/json/cves/2.0',
        format: 'json',
        feedConfig: {
            syncInterval: 3600,
            batchSize: 1000,
        },
        priority: 'high',
        status: 'active',
        isActive: true,
        lastSyncAt: new Date('2023-12-01T10:00:00Z'),
        nextSyncAt: new Date('2023-12-01T11:00:00Z'),
        syncStats: {
            totalSyncs: 10,
            successfulSyncs: 9,
            failedSyncs: 1,
            lastSyncDuration: 300,
        },
        qualityMetrics: {
            completenessScore: 95,
            accuracyScore: 98,
            errorRate: 2,
        },
        getSummary: jest.fn(),
        exportForReporting: jest.fn(),
        activate: jest.fn(),
        deactivate: jest.fn(),
        pause: jest.fn(),
        resume: jest.fn(),
        updateSyncStats: jest.fn(),
        updateQualityMetrics: jest.fn(),
        isHealthy: true,
        syncSuccessRate: 90,
        hoursSinceLastSync: 1,
        isOverdue: false,
        isDueForSync: true,
    };
    const mockVulnerability = {
        id: 'vuln-123',
        identifier: 'CVE-2023-1234',
        title: 'Test Vulnerability',
        severity: 'high',
    };
    beforeEach(async () => {
        const mockRepository = {
            create: jest.fn(),
            save: jest.fn(),
            find: jest.fn(),
            findOne: jest.fn(),
            findOneBy: jest.fn(),
            count: jest.fn(),
            remove: jest.fn(),
            createQueryBuilder: jest.fn(() => ({
                leftJoinAndSelect: jest.fn().mockReturnThis(),
                where: jest.fn().mockReturnThis(),
                andWhere: jest.fn().mockReturnThis(),
                orderBy: jest.fn().mockReturnThis(),
                addOrderBy: jest.fn().mockReturnThis(),
                skip: jest.fn().mockReturnThis(),
                take: jest.fn().mockReturnThis(),
                getManyAndCount: jest.fn(),
                getMany: jest.fn(),
            })),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                vulnerability_feed_service_1.VulnerabilityFeedService,
                {
                    provide: (0, typeorm_1.getRepositoryToken)(vulnerability_feed_entity_1.VulnerabilityFeed),
                    useValue: mockRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(vulnerability_entity_1.Vulnerability),
                    useValue: mockRepository,
                },
                {
                    provide: logger_service_1.LoggerService,
                    useValue: {
                        debug: jest.fn(),
                        log: jest.fn(),
                        warn: jest.fn(),
                        error: jest.fn(),
                    },
                },
                {
                    provide: audit_service_1.AuditService,
                    useValue: {
                        logUserAction: jest.fn(),
                    },
                },
                {
                    provide: vulnerability_feed_integration_service_1.VulnerabilityFeedIntegrationService,
                    useValue: {
                        fetchNVDData: jest.fn(),
                        fetchMITREData: jest.fn(),
                        fetchVendorAdvisories: jest.fn(),
                        fetchThreatIntelligence: jest.fn(),
                        fetchExploitDatabase: jest.fn(),
                        validateFeedData: jest.fn(),
                    },
                },
            ],
        }).compile();
        service = module.get(vulnerability_feed_service_1.VulnerabilityFeedService);
        feedRepository = module.get((0, typeorm_1.getRepositoryToken)(vulnerability_feed_entity_1.VulnerabilityFeed));
        vulnerabilityRepository = module.get((0, typeorm_1.getRepositoryToken)(vulnerability_entity_1.Vulnerability));
        loggerService = module.get(logger_service_1.LoggerService);
        auditService = module.get(audit_service_1.AuditService);
        feedIntegrationService = module.get(vulnerability_feed_integration_service_1.VulnerabilityFeedIntegrationService);
    });
    it('should be defined', () => {
        expect(service).toBeDefined();
    });
    describe('createFeed', () => {
        it('should create a new feed', async () => {
            const feedData = {
                name: 'New Feed',
                description: 'New feed description',
                feedType: 'nvd',
                provider: 'NIST',
                url: 'https://services.nvd.nist.gov/rest/json/cves/2.0',
                format: 'json',
                feedConfig: {
                    syncInterval: 3600,
                    batchSize: 1000,
                },
            };
            feedRepository.create.mockReturnValue(mockFeed);
            feedRepository.save.mockResolvedValue(mockFeed);
            const result = await service.createFeed(feedData, 'user-123');
            expect(feedRepository.create).toHaveBeenCalledWith(expect.objectContaining({
                ...feedData,
                status: 'inactive',
                isActive: false,
            }));
            expect(feedRepository.save).toHaveBeenCalledWith(mockFeed);
            expect(auditService.logUserAction).toHaveBeenCalledWith('user-123', 'create', 'vulnerability_feed', mockFeed.id, expect.any(Object));
        });
        it('should throw error when feed with same name exists', async () => {
            const feedData = {
                name: 'Existing Feed',
                feedType: 'nvd',
                url: 'https://example.com',
                format: 'json',
                feedConfig: {},
            };
            feedRepository.findOne.mockResolvedValue(mockFeed);
            await expect(service.createFeed(feedData, 'user-123')).rejects.toThrow('Feed with this name already exists');
        });
    });
    describe('getFeedDetails', () => {
        it('should return feed details', async () => {
            feedRepository.findOne.mockResolvedValue(mockFeed);
            const result = await service.getFeedDetails('123e4567-e89b-12d3-a456-426614174000');
            expect(result).toEqual(mockFeed);
            expect(feedRepository.findOne).toHaveBeenCalledWith({
                where: { id: '123e4567-e89b-12d3-a456-426614174000' },
            });
        });
        it('should throw NotFoundException when feed not found', async () => {
            feedRepository.findOne.mockResolvedValue(null);
            await expect(service.getFeedDetails('non-existent-id')).rejects.toThrow('Feed not found');
        });
    });
    describe('searchFeeds', () => {
        it('should search feeds with filters', async () => {
            const mockQueryBuilder = feedRepository.createQueryBuilder();
            mockQueryBuilder.getManyAndCount.mockResolvedValue([[mockFeed], 1]);
            const searchCriteria = {
                page: 1,
                limit: 10,
                feedTypes: ['nvd'],
                statuses: ['active'],
                providers: ['NIST'],
            };
            const result = await service.searchFeeds(searchCriteria);
            expect(result).toHaveProperty('feeds');
            expect(result).toHaveProperty('total');
            expect(result).toHaveProperty('page');
            expect(result).toHaveProperty('totalPages');
            expect(result.feeds).toHaveLength(1);
            expect(result.total).toBe(1);
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('feed.feedType IN (:...feedTypes)', { feedTypes: ['nvd'] });
        });
        it('should handle boolean filters', async () => {
            const mockQueryBuilder = feedRepository.createQueryBuilder();
            mockQueryBuilder.getManyAndCount.mockResolvedValue([[], 0]);
            const searchCriteria = {
                page: 1,
                limit: 10,
                isActive: true,
                healthyOnly: true,
                dueForSync: true,
            };
            await service.searchFeeds(searchCriteria);
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('feed.isActive = :isActive', { isActive: true });
        });
    });
    describe('activateFeed', () => {
        it('should activate an inactive feed', async () => {
            const inactiveFeed = { ...mockFeed, status: 'inactive', isActive: false };
            feedRepository.findOne.mockResolvedValue(inactiveFeed);
            feedRepository.save.mockResolvedValue({
                ...inactiveFeed,
                status: 'active',
                isActive: true,
            });
            const result = await service.activateFeed('123e4567-e89b-12d3-a456-426614174000', 'user-123');
            expect(inactiveFeed.activate).toHaveBeenCalled();
            expect(feedRepository.save).toHaveBeenCalledWith(inactiveFeed);
            expect(auditService.logUserAction).toHaveBeenCalledWith('user-123', 'activate', 'vulnerability_feed', inactiveFeed.id, expect.any(Object));
        });
        it('should throw error when feed not found', async () => {
            feedRepository.findOne.mockResolvedValue(null);
            await expect(service.activateFeed('non-existent-id', 'user-123')).rejects.toThrow('Feed not found');
        });
    });
    describe('deactivateFeed', () => {
        it('should deactivate an active feed', async () => {
            feedRepository.findOne.mockResolvedValue(mockFeed);
            feedRepository.save.mockResolvedValue({
                ...mockFeed,
                status: 'inactive',
                isActive: false,
            });
            const result = await service.deactivateFeed('123e4567-e89b-12d3-a456-426614174000', 'user-123');
            expect(mockFeed.deactivate).toHaveBeenCalled();
            expect(feedRepository.save).toHaveBeenCalledWith(mockFeed);
            expect(auditService.logUserAction).toHaveBeenCalledWith('user-123', 'deactivate', 'vulnerability_feed', mockFeed.id, expect.any(Object));
        });
    });
    describe('syncFeed', () => {
        it('should sync an NVD feed', async () => {
            const nvdFeed = { ...mockFeed, feedType: 'nvd' };
            const mockFeedData = {
                vulnerabilities: [
                    {
                        identifier: 'CVE-2023-5678',
                        title: 'New Vulnerability',
                        severity: 'high',
                    },
                ],
                metadata: {
                    source: 'nvd',
                    fetchedAt: new Date(),
                    totalCount: 1,
                },
            };
            feedRepository.findOne.mockResolvedValue(nvdFeed);
            feedIntegrationService.fetchNVDData.mockResolvedValue(mockFeedData);
            feedIntegrationService.validateFeedData.mockResolvedValue({
                totalRecords: 1,
                validRecords: 1,
                invalidRecords: 0,
                qualityScore: 100,
                completenessScore: 100,
            });
            vulnerabilityRepository.findOne.mockResolvedValue(null);
            vulnerabilityRepository.create.mockReturnValue(mockVulnerability);
            vulnerabilityRepository.save.mockResolvedValue(mockVulnerability);
            feedRepository.save.mockResolvedValue(nvdFeed);
            const result = await service.syncFeed('123e4567-e89b-12d3-a456-426614174000', 'user-123');
            expect(feedIntegrationService.fetchNVDData).toHaveBeenCalledWith(expect.objectContaining({
                url: nvdFeed.url,
                feedConfig: nvdFeed.feedConfig,
            }));
            expect(feedIntegrationService.validateFeedData).toHaveBeenCalled();
            expect(nvdFeed.updateSyncStats).toHaveBeenCalled();
            expect(auditService.logUserAction).toHaveBeenCalledWith('user-123', 'sync', 'vulnerability_feed', nvdFeed.id, expect.any(Object));
        });
        it('should handle sync errors gracefully', async () => {
            const errorFeed = { ...mockFeed, status: 'active' };
            feedRepository.findOne.mockResolvedValue(errorFeed);
            feedIntegrationService.fetchNVDData.mockRejectedValue(new Error('Network error'));
            feedRepository.save.mockResolvedValue({
                ...errorFeed,
                status: 'error',
            });
            const result = await service.syncFeed('123e4567-e89b-12d3-a456-426614174000', 'user-123');
            expect(result.success).toBe(false);
            expect(result.error).toBe('Network error');
            expect(loggerService.error).toHaveBeenCalledWith('Failed to sync feed', expect.objectContaining({
                feedId: errorFeed.id,
                error: 'Network error',
            }));
        });
        it('should throw error when feed is not active', async () => {
            const inactiveFeed = { ...mockFeed, status: 'inactive', isActive: false };
            feedRepository.findOne.mockResolvedValue(inactiveFeed);
            await expect(service.syncFeed('123e4567-e89b-12d3-a456-426614174000', 'user-123')).rejects.toThrow('Feed is not active');
        });
    });
    describe('updateFeed', () => {
        it('should update an existing feed', async () => {
            const updates = {
                description: 'Updated description',
                priority: 'critical',
                feedConfig: {
                    syncInterval: 1800,
                    batchSize: 500,
                },
            };
            feedRepository.findOne.mockResolvedValue(mockFeed);
            feedRepository.save.mockResolvedValue({
                ...mockFeed,
                ...updates,
            });
            const result = await service.updateFeed('123e4567-e89b-12d3-a456-426614174000', updates, 'user-123');
            expect(feedRepository.save).toHaveBeenCalled();
            expect(auditService.logUserAction).toHaveBeenCalledWith('user-123', 'update', 'vulnerability_feed', '123e4567-e89b-12d3-a456-426614174000', expect.objectContaining({
                name: mockFeed.name,
                changes: expect.any(Object),
            }));
        });
    });
    describe('deleteFeed', () => {
        it('should delete an inactive feed', async () => {
            const inactiveFeed = { ...mockFeed, status: 'inactive', isActive: false };
            feedRepository.findOne.mockResolvedValue(inactiveFeed);
            feedRepository.remove.mockResolvedValue(inactiveFeed);
            await service.deleteFeed('123e4567-e89b-12d3-a456-426614174000', 'user-123');
            expect(feedRepository.remove).toHaveBeenCalledWith(inactiveFeed);
            expect(auditService.logUserAction).toHaveBeenCalledWith('user-123', 'delete', 'vulnerability_feed', inactiveFeed.id, expect.any(Object));
        });
        it('should throw error when trying to delete an active feed', async () => {
            feedRepository.findOne.mockResolvedValue(mockFeed);
            await expect(service.deleteFeed('123e4567-e89b-12d3-a456-426614174000', 'user-123')).rejects.toThrow('Cannot delete active feed');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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