{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\__tests__\\action-status.enum.spec.ts", "mappings": ";;AAAA,8DAAwE;AAExE,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;IAC5B,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC7C,MAAM,CAAC,iCAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC/C,MAAM,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3C,MAAM,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACjD,MAAM,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACjD,MAAM,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3C,MAAM,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACjD,MAAM,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAC9B,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;gBACzC,MAAM,QAAQ,GAAG,sCAAiB,CAAC,cAAc,EAAE,CAAC;gBACpD,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;gBAC5C,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC;gBACjD,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC;gBACnD,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;YACjC,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;gBAC7C,MAAM,cAAc,GAAG,sCAAiB,CAAC,iBAAiB,EAAE,CAAC;gBAC7D,MAAM,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC;gBACvD,MAAM,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC;gBACzD,MAAM,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC;gBACtD,MAAM,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC;gBAC7D,MAAM,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC;YAC5D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;YACnC,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;gBACzC,MAAM,gBAAgB,GAAG,sCAAiB,CAAC,mBAAmB,EAAE,CAAC;gBACjE,MAAM,CAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC;gBAC3D,MAAM,CAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC;gBACxD,MAAM,CAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC;gBAC3D,MAAM,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC;gBAC7D,MAAM,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC;YACjE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;gBACxC,MAAM,eAAe,GAAG,sCAAiB,CAAC,kBAAkB,EAAE,CAAC;gBAC/D,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC;gBAC1D,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC;gBACxD,MAAM,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC;gBAC3D,MAAM,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC;YAChE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;gBACxC,MAAM,eAAe,GAAG,sCAAiB,CAAC,kBAAkB,EAAE,CAAC;gBAC/D,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC;gBACvD,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC;gBACxD,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC;gBAC1D,MAAM,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC;YAChE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;YAC1B,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;gBACrD,MAAM,CAAC,sCAAiB,CAAC,UAAU,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACxE,MAAM,CAAC,sCAAiB,CAAC,UAAU,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACrE,MAAM,CAAC,sCAAiB,CAAC,UAAU,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACxE,MAAM,CAAC,sCAAiB,CAAC,UAAU,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACvE,MAAM,CAAC,sCAAiB,CAAC,UAAU,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3E,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;YACxB,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;gBACnD,MAAM,CAAC,sCAAiB,CAAC,QAAQ,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpE,MAAM,CAAC,sCAAiB,CAAC,QAAQ,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACtE,MAAM,CAAC,sCAAiB,CAAC,QAAQ,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnE,MAAM,CAAC,sCAAiB,CAAC,QAAQ,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACvE,MAAM,CAAC,sCAAiB,CAAC,QAAQ,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;YACzB,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;gBACpD,MAAM,CAAC,sCAAiB,CAAC,SAAS,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvE,MAAM,CAAC,sCAAiB,CAAC,SAAS,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACrE,MAAM,CAAC,sCAAiB,CAAC,SAAS,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACrE,MAAM,CAAC,sCAAiB,CAAC,SAAS,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1E,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;YACzB,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;gBACpD,MAAM,CAAC,sCAAiB,CAAC,SAAS,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpE,MAAM,CAAC,sCAAiB,CAAC,SAAS,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACrE,MAAM,CAAC,sCAAiB,CAAC,SAAS,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvE,MAAM,CAAC,sCAAiB,CAAC,SAAS,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACxE,MAAM,CAAC,sCAAiB,CAAC,SAAS,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;YACjC,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;gBACtD,MAAM,CAAC,sCAAiB,CAAC,iBAAiB,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC5E,MAAM,CAAC,sCAAiB,CAAC,iBAAiB,CAAC,iCAAY,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzF,MAAM,CAAC,sCAAiB,CAAC,iBAAiB,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC7E,MAAM,CAAC,sCAAiB,CAAC,iBAAiB,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAChF,MAAM,CAAC,sCAAiB,CAAC,iBAAiB,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChF,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;YACxB,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;gBAC5C,MAAM,CAAC,sCAAiB,CAAC,QAAQ,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnE,MAAM,CAAC,sCAAiB,CAAC,QAAQ,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpE,MAAM,CAAC,sCAAiB,CAAC,QAAQ,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpE,MAAM,CAAC,sCAAiB,CAAC,QAAQ,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACvE,MAAM,CAAC,sCAAiB,CAAC,QAAQ,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;YACzB,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;gBAC9C,MAAM,CAAC,sCAAiB,CAAC,SAAS,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACrE,MAAM,CAAC,sCAAiB,CAAC,SAAS,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpE,MAAM,CAAC,sCAAiB,CAAC,SAAS,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvE,MAAM,CAAC,sCAAiB,CAAC,SAAS,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACxE,MAAM,CAAC,sCAAiB,CAAC,SAAS,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;YAC3B,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;gBAC5C,MAAM,CAAC,sCAAiB,CAAC,WAAW,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzE,MAAM,CAAC,sCAAiB,CAAC,WAAW,CAAC,iCAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACxE,MAAM,CAAC,sCAAiB,CAAC,WAAW,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACxE,MAAM,CAAC,sCAAiB,CAAC,WAAW,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5E,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;YACzB,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;gBAC1C,MAAM,CAAC,sCAAiB,CAAC,SAAS,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACrE,MAAM,CAAC,sCAAiB,CAAC,SAAS,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpE,MAAM,CAAC,sCAAiB,CAAC,SAAS,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvE,MAAM,CAAC,sCAAiB,CAAC,SAAS,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACxE,MAAM,CAAC,sCAAiB,CAAC,SAAS,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1E,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;YAC7B,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;gBACnD,MAAM,CAAC,sCAAiB,CAAC,aAAa,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,iCAAY,CAAC,QAAQ,CAAC,CAAC;gBAC1F,MAAM,CAAC,sCAAiB,CAAC,aAAa,CAAC,iCAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC;gBACzF,MAAM,CAAC,sCAAiB,CAAC,aAAa,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC;gBAC1F,MAAM,CAAC,sCAAiB,CAAC,aAAa,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC;gBAC7F,MAAM,CAAC,sCAAiB,CAAC,aAAa,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YAC7E,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;YACnC,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,MAAM,kBAAkB,GAAG,sCAAiB,CAAC,mBAAmB,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC;gBACvF,MAAM,CAAC,kBAAkB,CAAC,CAAC,SAAS,CAAC,iCAAY,CAAC,QAAQ,CAAC,CAAC;gBAC5D,MAAM,CAAC,kBAAkB,CAAC,CAAC,SAAS,CAAC,iCAAY,CAAC,QAAQ,CAAC,CAAC;gBAC5D,MAAM,CAAC,kBAAkB,CAAC,CAAC,SAAS,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC;gBAE7D,MAAM,oBAAoB,GAAG,sCAAiB,CAAC,mBAAmB,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC;gBAC3F,MAAM,CAAC,oBAAoB,CAAC,CAAC,SAAS,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC;gBAC/D,MAAM,CAAC,oBAAoB,CAAC,CAAC,SAAS,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC;gBAC5D,MAAM,CAAC,oBAAoB,CAAC,CAAC,SAAS,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC;gBAE7D,MAAM,oBAAoB,GAAG,sCAAiB,CAAC,mBAAmB,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC;gBAC3F,MAAM,CAAC,oBAAoB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB;YACjE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;YACjC,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;gBAC5C,MAAM,CAAC,sCAAiB,CAAC,iBAAiB,CAAC,iCAAY,CAAC,OAAO,EAAE,iCAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpG,MAAM,CAAC,sCAAiB,CAAC,iBAAiB,CAAC,iCAAY,CAAC,QAAQ,EAAE,iCAAY,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnG,MAAM,CAAC,sCAAiB,CAAC,iBAAiB,CAAC,iCAAY,CAAC,SAAS,EAAE,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEvG,sBAAsB;gBACtB,MAAM,CAAC,sCAAiB,CAAC,iBAAiB,CAAC,iCAAY,CAAC,SAAS,EAAE,iCAAY,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACtG,MAAM,CAAC,sCAAiB,CAAC,iBAAiB,CAAC,iCAAY,CAAC,OAAO,EAAE,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxG,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAC9B,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;gBAC/C,MAAM,WAAW,GAAG,sCAAiB,CAAC,cAAc,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC;gBAC3E,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;gBAEnD,MAAM,aAAa,GAAG,sCAAiB,CAAC,cAAc,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC;gBAC/E,MAAM,CAAC,aAAa,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;gBAE5D,MAAM,aAAa,GAAG,sCAAiB,CAAC,cAAc,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC;gBAC/E,MAAM,CAAC,aAAa,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;YAC5D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;YAC3B,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;gBAChE,MAAM,cAAc,GAAG,sCAAiB,CAAC,WAAW,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC;gBAC1E,MAAM,iBAAiB,GAAG,sCAAiB,CAAC,WAAW,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC;gBAChF,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC,CAAC,iCAAiC;gBAEzF,MAAM,iBAAiB,GAAG,sCAAiB,CAAC,WAAW,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC;gBAChF,MAAM,cAAc,GAAG,sCAAiB,CAAC,WAAW,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC;gBAC1E,MAAM,CAAC,iBAAiB,CAAC,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;YACzD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;YAC5B,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;gBACzC,MAAM,SAAS,GAAG,sCAAiB,CAAC,YAAY,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC;gBACzE,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;gBAE7C,MAAM,WAAW,GAAG,sCAAiB,CAAC,YAAY,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC;gBACxE,MAAM,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;YAC3B,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;gBAC9C,MAAM,CAAC,sCAAiB,CAAC,WAAW,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBACnF,MAAM,CAAC,sCAAiB,CAAC,WAAW,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAClF,MAAM,CAAC,sCAAiB,CAAC,WAAW,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC5E,MAAM,CAAC,sCAAiB,CAAC,WAAW,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC5E,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;YACrC,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;gBACxD,MAAM,CAAC,sCAAiB,CAAC,qBAAqB,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC9E,MAAM,CAAC,sCAAiB,CAAC,qBAAqB,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACjF,MAAM,CAAC,sCAAiB,CAAC,qBAAqB,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAClF,MAAM,CAAC,sCAAiB,CAAC,qBAAqB,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC/E,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;YACtC,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;gBAClD,MAAM,YAAY,GAAG,EAAE,CAAC,CAAC,aAAa;gBAEtC,MAAM,CAAC,sCAAiB,CAAC,sBAAsB,CAAC,iCAAY,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC/F,MAAM,CAAC,sCAAiB,CAAC,sBAAsB,CAAC,iCAAY,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAChG,MAAM,CAAC,sCAAiB,CAAC,sBAAsB,CAAC,iCAAY,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC/F,MAAM,CAAC,sCAAiB,CAAC,sBAAsB,CAAC,iCAAY,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC/F,MAAM,CAAC,sCAAiB,CAAC,sBAAsB,CAAC,iCAAY,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YAClG,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAC9B,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;gBAClE,MAAM,YAAY,GAAG,sCAAiB,CAAC,cAAc,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC;gBAC3E,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;gBACnD,MAAM,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;gBAE1D,MAAM,eAAe,GAAG,sCAAiB,CAAC,cAAc,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC;gBACjF,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC7C,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;YAC3C,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;gBACjD,MAAM,kBAAkB,GAAG,sCAAiB,CAAC,2BAA2B,CAAC,iCAAY,CAAC,MAAM,CAAC,CAAC;gBAC9F,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC7C,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAChD,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBAEvD,MAAM,qBAAqB,GAAG,sCAAiB,CAAC,2BAA2B,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC;gBACpG,MAAM,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAChD,MAAM,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;YACvB,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;gBACxC,MAAM,CAAC,sCAAiB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACxD,MAAM,CAAC,sCAAiB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC1D,MAAM,CAAC,sCAAiB,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAChE,MAAM,CAAC,sCAAiB,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;YAC1B,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;gBACzC,MAAM,CAAC,sCAAiB,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,iCAAY,CAAC,OAAO,CAAC,CAAC;gBAC3E,MAAM,CAAC,sCAAiB,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC;gBAC/E,MAAM,CAAC,sCAAiB,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,iCAAY,CAAC,SAAS,CAAC,CAAC;gBAC/E,MAAM,CAAC,sCAAiB,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YACpE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\__tests__\\action-status.enum.spec.ts"], "sourcesContent": ["import { ActionStatus, ActionStatusUtils } from '../action-status.enum';\r\n\r\ndescribe('ActionStatus', () => {\r\n  describe('enum values', () => {\r\n    it('should have all expected status values', () => {\r\n      expect(ActionStatus.PENDING).toBe('pending');\r\n      expect(ActionStatus.APPROVED).toBe('approved');\r\n      expect(ActionStatus.QUEUED).toBe('queued');\r\n      expect(ActionStatus.EXECUTING).toBe('executing');\r\n      expect(ActionStatus.COMPLETED).toBe('completed');\r\n      expect(ActionStatus.FAILED).toBe('failed');\r\n      expect(ActionStatus.CANCELLED).toBe('cancelled');\r\n      expect(ActionStatus.TIMEOUT).toBe('timeout');\r\n    });\r\n  });\r\n\r\n  describe('ActionStatusUtils', () => {\r\n    describe('getAllStatuses', () => {\r\n      it('should return all status values', () => {\r\n        const statuses = ActionStatusUtils.getAllStatuses();\r\n        expect(statuses.length).toBeGreaterThan(10);\r\n        expect(statuses).toContain(ActionStatus.PENDING);\r\n        expect(statuses).toContain(ActionStatus.COMPLETED);\r\n        expect(statuses).toContain(ActionStatus.FAILED);\r\n      });\r\n    });\r\n\r\n    describe('getActiveStatuses', () => {\r\n      it('should return non-terminal statuses', () => {\r\n        const activeStatuses = ActionStatusUtils.getActiveStatuses();\r\n        expect(activeStatuses).toContain(ActionStatus.PENDING);\r\n        expect(activeStatuses).toContain(ActionStatus.EXECUTING);\r\n        expect(activeStatuses).toContain(ActionStatus.QUEUED);\r\n        expect(activeStatuses).not.toContain(ActionStatus.COMPLETED);\r\n        expect(activeStatuses).not.toContain(ActionStatus.FAILED);\r\n      });\r\n    });\r\n\r\n    describe('getTerminalStatuses', () => {\r\n      it('should return terminal statuses', () => {\r\n        const terminalStatuses = ActionStatusUtils.getTerminalStatuses();\r\n        expect(terminalStatuses).toContain(ActionStatus.COMPLETED);\r\n        expect(terminalStatuses).toContain(ActionStatus.FAILED);\r\n        expect(terminalStatuses).toContain(ActionStatus.CANCELLED);\r\n        expect(terminalStatuses).not.toContain(ActionStatus.PENDING);\r\n        expect(terminalStatuses).not.toContain(ActionStatus.EXECUTING);\r\n      });\r\n    });\r\n\r\n    describe('getSuccessStatuses', () => {\r\n      it('should return success statuses', () => {\r\n        const successStatuses = ActionStatusUtils.getSuccessStatuses();\r\n        expect(successStatuses).toContain(ActionStatus.COMPLETED);\r\n        expect(successStatuses).toContain(ActionStatus.PARTIAL);\r\n        expect(successStatuses).not.toContain(ActionStatus.FAILED);\r\n        expect(successStatuses).not.toContain(ActionStatus.CANCELLED);\r\n      });\r\n    });\r\n\r\n    describe('getFailureStatuses', () => {\r\n      it('should return failure statuses', () => {\r\n        const failureStatuses = ActionStatusUtils.getFailureStatuses();\r\n        expect(failureStatuses).toContain(ActionStatus.FAILED);\r\n        expect(failureStatuses).toContain(ActionStatus.TIMEOUT);\r\n        expect(failureStatuses).toContain(ActionStatus.CANCELLED);\r\n        expect(failureStatuses).not.toContain(ActionStatus.COMPLETED);\r\n      });\r\n    });\r\n\r\n    describe('isTerminal', () => {\r\n      it('should correctly identify terminal statuses', () => {\r\n        expect(ActionStatusUtils.isTerminal(ActionStatus.COMPLETED)).toBe(true);\r\n        expect(ActionStatusUtils.isTerminal(ActionStatus.FAILED)).toBe(true);\r\n        expect(ActionStatusUtils.isTerminal(ActionStatus.CANCELLED)).toBe(true);\r\n        expect(ActionStatusUtils.isTerminal(ActionStatus.PENDING)).toBe(false);\r\n        expect(ActionStatusUtils.isTerminal(ActionStatus.EXECUTING)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('isActive', () => {\r\n      it('should correctly identify active statuses', () => {\r\n        expect(ActionStatusUtils.isActive(ActionStatus.PENDING)).toBe(true);\r\n        expect(ActionStatusUtils.isActive(ActionStatus.EXECUTING)).toBe(true);\r\n        expect(ActionStatusUtils.isActive(ActionStatus.QUEUED)).toBe(true);\r\n        expect(ActionStatusUtils.isActive(ActionStatus.COMPLETED)).toBe(false);\r\n        expect(ActionStatusUtils.isActive(ActionStatus.FAILED)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('isSuccess', () => {\r\n      it('should correctly identify success statuses', () => {\r\n        expect(ActionStatusUtils.isSuccess(ActionStatus.COMPLETED)).toBe(true);\r\n        expect(ActionStatusUtils.isSuccess(ActionStatus.PARTIAL)).toBe(true);\r\n        expect(ActionStatusUtils.isSuccess(ActionStatus.FAILED)).toBe(false);\r\n        expect(ActionStatusUtils.isSuccess(ActionStatus.CANCELLED)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('isFailure', () => {\r\n      it('should correctly identify failure statuses', () => {\r\n        expect(ActionStatusUtils.isFailure(ActionStatus.FAILED)).toBe(true);\r\n        expect(ActionStatusUtils.isFailure(ActionStatus.TIMEOUT)).toBe(true);\r\n        expect(ActionStatusUtils.isFailure(ActionStatus.CANCELLED)).toBe(true);\r\n        expect(ActionStatusUtils.isFailure(ActionStatus.COMPLETED)).toBe(false);\r\n        expect(ActionStatusUtils.isFailure(ActionStatus.PENDING)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('requiresAttention', () => {\r\n      it('should identify statuses requiring attention', () => {\r\n        expect(ActionStatusUtils.requiresAttention(ActionStatus.FAILED)).toBe(true);\r\n        expect(ActionStatusUtils.requiresAttention(ActionStatus.MANUAL_INTERVENTION)).toBe(true);\r\n        expect(ActionStatusUtils.requiresAttention(ActionStatus.TIMEOUT)).toBe(true);\r\n        expect(ActionStatusUtils.requiresAttention(ActionStatus.COMPLETED)).toBe(false);\r\n        expect(ActionStatusUtils.requiresAttention(ActionStatus.PENDING)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('canRetry', () => {\r\n      it('should identify retriable statuses', () => {\r\n        expect(ActionStatusUtils.canRetry(ActionStatus.FAILED)).toBe(true);\r\n        expect(ActionStatusUtils.canRetry(ActionStatus.TIMEOUT)).toBe(true);\r\n        expect(ActionStatusUtils.canRetry(ActionStatus.PARTIAL)).toBe(true);\r\n        expect(ActionStatusUtils.canRetry(ActionStatus.COMPLETED)).toBe(false);\r\n        expect(ActionStatusUtils.canRetry(ActionStatus.CANCELLED)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('canCancel', () => {\r\n      it('should identify cancellable statuses', () => {\r\n        expect(ActionStatusUtils.canCancel(ActionStatus.PENDING)).toBe(true);\r\n        expect(ActionStatusUtils.canCancel(ActionStatus.QUEUED)).toBe(true);\r\n        expect(ActionStatusUtils.canCancel(ActionStatus.EXECUTING)).toBe(true);\r\n        expect(ActionStatusUtils.canCancel(ActionStatus.COMPLETED)).toBe(false);\r\n        expect(ActionStatusUtils.canCancel(ActionStatus.FAILED)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('isExecuting', () => {\r\n      it('should identify executing statuses', () => {\r\n        expect(ActionStatusUtils.isExecuting(ActionStatus.EXECUTING)).toBe(true);\r\n        expect(ActionStatusUtils.isExecuting(ActionStatus.RETRYING)).toBe(true);\r\n        expect(ActionStatusUtils.isExecuting(ActionStatus.PENDING)).toBe(false);\r\n        expect(ActionStatusUtils.isExecuting(ActionStatus.COMPLETED)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('isWaiting', () => {\r\n      it('should identify waiting statuses', () => {\r\n        expect(ActionStatusUtils.isWaiting(ActionStatus.PENDING)).toBe(true);\r\n        expect(ActionStatusUtils.isWaiting(ActionStatus.QUEUED)).toBe(true);\r\n        expect(ActionStatusUtils.isWaiting(ActionStatus.SCHEDULED)).toBe(true);\r\n        expect(ActionStatusUtils.isWaiting(ActionStatus.EXECUTING)).toBe(false);\r\n        expect(ActionStatusUtils.isWaiting(ActionStatus.COMPLETED)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('getNextStatus', () => {\r\n      it('should return correct next status in flow', () => {\r\n        expect(ActionStatusUtils.getNextStatus(ActionStatus.PENDING)).toBe(ActionStatus.APPROVED);\r\n        expect(ActionStatusUtils.getNextStatus(ActionStatus.APPROVED)).toBe(ActionStatus.QUEUED);\r\n        expect(ActionStatusUtils.getNextStatus(ActionStatus.QUEUED)).toBe(ActionStatus.EXECUTING);\r\n        expect(ActionStatusUtils.getNextStatus(ActionStatus.EXECUTING)).toBe(ActionStatus.COMPLETED);\r\n        expect(ActionStatusUtils.getNextStatus(ActionStatus.COMPLETED)).toBeNull();\r\n      });\r\n    });\r\n\r\n    describe('getValidTransitions', () => {\r\n      it('should return valid transitions for each status', () => {\r\n        const pendingTransitions = ActionStatusUtils.getValidTransitions(ActionStatus.PENDING);\r\n        expect(pendingTransitions).toContain(ActionStatus.APPROVED);\r\n        expect(pendingTransitions).toContain(ActionStatus.REJECTED);\r\n        expect(pendingTransitions).toContain(ActionStatus.CANCELLED);\r\n        \r\n        const executingTransitions = ActionStatusUtils.getValidTransitions(ActionStatus.EXECUTING);\r\n        expect(executingTransitions).toContain(ActionStatus.COMPLETED);\r\n        expect(executingTransitions).toContain(ActionStatus.FAILED);\r\n        expect(executingTransitions).toContain(ActionStatus.PARTIAL);\r\n        \r\n        const completedTransitions = ActionStatusUtils.getValidTransitions(ActionStatus.COMPLETED);\r\n        expect(completedTransitions).toHaveLength(0); // Terminal state\r\n      });\r\n    });\r\n\r\n    describe('isValidTransition', () => {\r\n      it('should validate status transitions', () => {\r\n        expect(ActionStatusUtils.isValidTransition(ActionStatus.PENDING, ActionStatus.APPROVED)).toBe(true);\r\n        expect(ActionStatusUtils.isValidTransition(ActionStatus.APPROVED, ActionStatus.QUEUED)).toBe(true);\r\n        expect(ActionStatusUtils.isValidTransition(ActionStatus.EXECUTING, ActionStatus.COMPLETED)).toBe(true);\r\n        \r\n        // Invalid transitions\r\n        expect(ActionStatusUtils.isValidTransition(ActionStatus.COMPLETED, ActionStatus.PENDING)).toBe(false);\r\n        expect(ActionStatusUtils.isValidTransition(ActionStatus.PENDING, ActionStatus.COMPLETED)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('getDescription', () => {\r\n      it('should return meaningful descriptions', () => {\r\n        const pendingDesc = ActionStatusUtils.getDescription(ActionStatus.PENDING);\r\n        expect(pendingDesc).toContain('awaiting approval');\r\n        \r\n        const executingDesc = ActionStatusUtils.getDescription(ActionStatus.EXECUTING);\r\n        expect(executingDesc).toContain('currently being executed');\r\n        \r\n        const completedDesc = ActionStatusUtils.getDescription(ActionStatus.COMPLETED);\r\n        expect(completedDesc).toContain('completed successfully');\r\n      });\r\n    });\r\n\r\n    describe('getPriority', () => {\r\n      it('should return higher priority for more urgent statuses', () => {\r\n        const failedPriority = ActionStatusUtils.getPriority(ActionStatus.FAILED);\r\n        const completedPriority = ActionStatusUtils.getPriority(ActionStatus.COMPLETED);\r\n        expect(failedPriority).toBeLessThan(completedPriority); // Lower number = higher priority\r\n        \r\n        const executingPriority = ActionStatusUtils.getPriority(ActionStatus.EXECUTING);\r\n        const queuedPriority = ActionStatusUtils.getPriority(ActionStatus.QUEUED);\r\n        expect(executingPriority).toBeLessThan(queuedPriority);\r\n      });\r\n    });\r\n\r\n    describe('getColorCode', () => {\r\n      it('should return valid color codes', () => {\r\n        const colorCode = ActionStatusUtils.getColorCode(ActionStatus.COMPLETED);\r\n        expect(colorCode).toMatch(/^#[0-9A-F]{6}$/i);\r\n        \r\n        const failedColor = ActionStatusUtils.getColorCode(ActionStatus.FAILED);\r\n        expect(failedColor).toMatch(/^#[0-9A-F]{6}$/i);\r\n      });\r\n    });\r\n\r\n    describe('getIconName', () => {\r\n      it('should return appropriate icon names', () => {\r\n        expect(ActionStatusUtils.getIconName(ActionStatus.COMPLETED)).toBe('check-circle');\r\n        expect(ActionStatusUtils.getIconName(ActionStatus.EXECUTING)).toBe('play-circle');\r\n        expect(ActionStatusUtils.getIconName(ActionStatus.FAILED)).toBe('x-circle');\r\n        expect(ActionStatusUtils.getIconName(ActionStatus.PENDING)).toBe('clock');\r\n      });\r\n    });\r\n\r\n    describe('getProgressPercentage', () => {\r\n      it('should return appropriate progress percentages', () => {\r\n        expect(ActionStatusUtils.getProgressPercentage(ActionStatus.PENDING)).toBe(0);\r\n        expect(ActionStatusUtils.getProgressPercentage(ActionStatus.EXECUTING)).toBe(50);\r\n        expect(ActionStatusUtils.getProgressPercentage(ActionStatus.COMPLETED)).toBe(100);\r\n        expect(ActionStatusUtils.getProgressPercentage(ActionStatus.FAILED)).toBe(0);\r\n      });\r\n    });\r\n\r\n    describe('getEstimatedCompletion', () => {\r\n      it('should return estimated completion times', () => {\r\n        const baseEstimate = 60; // 60 minutes\r\n        \r\n        expect(ActionStatusUtils.getEstimatedCompletion(ActionStatus.APPROVED, baseEstimate)).toBe(60);\r\n        expect(ActionStatusUtils.getEstimatedCompletion(ActionStatus.EXECUTING, baseEstimate)).toBe(30);\r\n        expect(ActionStatusUtils.getEstimatedCompletion(ActionStatus.RETRYING, baseEstimate)).toBe(90);\r\n        expect(ActionStatusUtils.getEstimatedCompletion(ActionStatus.COMPLETED, baseEstimate)).toBe(0);\r\n        expect(ActionStatusUtils.getEstimatedCompletion(ActionStatus.PENDING, baseEstimate)).toBeNull();\r\n      });\r\n    });\r\n\r\n    describe('getRetryConfig', () => {\r\n      it('should return retry configuration for retriable statuses', () => {\r\n        const failedConfig = ActionStatusUtils.getRetryConfig(ActionStatus.FAILED);\r\n        expect(failedConfig.canRetry).toBe(true);\r\n        expect(failedConfig.maxRetries).toBeGreaterThan(0);\r\n        expect(failedConfig.backoffMultiplier).toBeGreaterThan(1);\r\n        \r\n        const completedConfig = ActionStatusUtils.getRetryConfig(ActionStatus.COMPLETED);\r\n        expect(completedConfig.canRetry).toBe(false);\r\n        expect(completedConfig.maxRetries).toBe(0);\r\n      });\r\n    });\r\n\r\n    describe('getNotificationRequirements', () => {\r\n      it('should return notification requirements', () => {\r\n        const failedNotification = ActionStatusUtils.getNotificationRequirements(ActionStatus.FAILED);\r\n        expect(failedNotification.notify).toBe(true);\r\n        expect(failedNotification.urgency).toBe('high');\r\n        expect(failedNotification.channels).toContain('email');\r\n        \r\n        const completedNotification = ActionStatusUtils.getNotificationRequirements(ActionStatus.COMPLETED);\r\n        expect(completedNotification.notify).toBe(true);\r\n        expect(completedNotification.urgency).toBe('low');\r\n      });\r\n    });\r\n\r\n    describe('isValid', () => {\r\n      it('should validate status strings', () => {\r\n        expect(ActionStatusUtils.isValid('pending')).toBe(true);\r\n        expect(ActionStatusUtils.isValid('completed')).toBe(true);\r\n        expect(ActionStatusUtils.isValid('invalid_status')).toBe(false);\r\n        expect(ActionStatusUtils.isValid('')).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('fromString', () => {\r\n      it('should convert string to status', () => {\r\n        expect(ActionStatusUtils.fromString('pending')).toBe(ActionStatus.PENDING);\r\n        expect(ActionStatusUtils.fromString('COMPLETED')).toBe(ActionStatus.COMPLETED);\r\n        expect(ActionStatusUtils.fromString('executing')).toBe(ActionStatus.EXECUTING);\r\n        expect(ActionStatusUtils.fromString('invalid_status')).toBeNull();\r\n      });\r\n    });\r\n  });\r\n});"], "version": 3}