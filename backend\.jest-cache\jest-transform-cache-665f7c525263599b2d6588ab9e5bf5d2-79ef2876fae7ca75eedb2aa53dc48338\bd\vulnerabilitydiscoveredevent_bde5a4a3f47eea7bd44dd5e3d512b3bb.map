{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\vulnerability-discovered.event.ts", "mappings": ";;;AAAA,6DAA4E;AAC5E,wEAA+D;AAC/D,0EAAiE;AA2CjE;;;;;;;;;;;;;GAaG;AACH,MAAa,4BAA6B,SAAQ,+BAAiD;IACjG,YACE,WAA2B,EAC3B,SAA2C,EAC3C,OAMC;QAED,KAAK,CAAC,WAAW,EAAE,SAAS,EAAE;YAC5B,YAAY,EAAE,CAAC;YACf,GAAG,OAAO;YACV,QAAQ,EAAE;gBACR,SAAS,EAAE,yBAAyB;gBACpC,MAAM,EAAE,UAAU;gBAClB,aAAa,EAAE,eAAe;gBAC9B,GAAG,OAAO,EAAE,QAAQ;aACrB;SACF,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,KAAK,qCAAc,CAAC,QAAQ,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,KAAK,qCAAc,CAAC,IAAI;YAC/C,IAAI,CAAC,SAAS,CAAC,QAAQ,KAAK,qCAAc,CAAC,QAAQ,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,KAAK,uCAAe,CAAC,IAAI;YAClD,IAAI,CAAC,SAAS,CAAC,UAAU,KAAK,uCAAe,CAAC,SAAS;YACvD,IAAI,CAAC,SAAS,CAAC,UAAU,KAAK,uCAAe,CAAC,SAAS,CAAC;IACjE,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,EAAE,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,GAAG,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,IAAI,EAAE,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,KAAK,gBAAgB,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,0BAA0B;QACxB,OAAO,IAAI,CAAC,kBAAkB,EAAE;YACzB,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACzD,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,6BAA6B;QAC3B,OAAO,IAAI,CAAC,kBAAkB,EAAE;YACzB,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,2BAA2B;QACzB,OAAO,IAAI,CAAC,kBAAkB,EAAE;YACzB,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,IAAI,IAAI,CAAC,0BAA0B,EAAE,EAAE,CAAC;YACtC,OAAO,UAAU,CAAC;QACpB,CAAC;QACD,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;YACjC,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,KAAK,qCAAc,CAAC,MAAM,EAAE,CAAC;YACtD,OAAO,QAAQ,CAAC;QAClB,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,QAAQ,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YAChC,KAAK,qCAAc,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;YACvC,KAAK,qCAAc,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC;YACnC,KAAK,qCAAc,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,CAAC;YACtC,KAAK,qCAAc,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;YACnC,OAAO,CAAC,CAAC,OAAO,GAAG,CAAC;QACtB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,IAAI,IAAI,CAAC,0BAA0B,EAAE,EAAE,CAAC;YACtC,OAAO,UAAU,CAAC;QACpB,CAAC;QACD,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;YACjC,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,IAAI,IAAI,CAAC,gBAAgB,EAAE,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;YAC5D,OAAO,QAAQ,CAAC;QAClB,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,QAAQ,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YAChC,KAAK,qCAAc,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAG,UAAU;YACpD,KAAK,qCAAc,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAO,UAAU;YACpD,KAAK,qCAAc,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,CAAC,CAAI,WAAW;YACrD,KAAK,qCAAc,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC,CAAO,SAAS;YACnD,OAAO,CAAC,CAAC,OAAO,GAAG,CAAC,CAAsB,SAAS;QACrD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,uBAAuB;QACrB,MAAM,QAAQ,GAAa,CAAC,SAAS,CAAC,CAAC;QAEvC,IAAI,IAAI,CAAC,0BAA0B,EAAE,EAAE,CAAC;YACtC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QACzC,CAAC;aAAM,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;YACxC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAClC,CAAC;aAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC;YACnC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzB,CAAC;QAED,IAAI,IAAI,CAAC,6BAA6B,EAAE,EAAE,CAAC;YACzC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACvC,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,2BAA2B;QACzB,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,IAAI,CAAC,0BAA0B,EAAE,EAAE,CAAC;YACtC,OAAO,CAAC,IAAI,CAAC,6BAA6B,EAAE,yBAAyB,EAAE,kBAAkB,CAAC,CAAC;QAC7F,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAC;YAC7B,OAAO,CAAC,IAAI,CAAC,uBAAuB,EAAE,mBAAmB,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC;YAC5B,OAAO,CAAC,IAAI,CAAC,4BAA4B,EAAE,4BAA4B,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,IAAI,CAAC,2BAA2B,EAAE,EAAE,CAAC;YACvC,OAAO,CAAC,IAAI,CAAC,8BAA8B,EAAE,8BAA8B,CAAC,CAAC;QAC/E,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;YACzB,OAAO,CAAC,IAAI,CAAC,qBAAqB,EAAE,0BAA0B,CAAC,CAAC;QAClE,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO;YACL,UAAU,EAAE,0BAA0B;YACtC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ;YACjC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ;YACjC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;YACzB,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU;YACrC,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe;YAChD,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE;YAC5C,UAAU,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK;YACpD,oBAAoB,EAAE,IAAI,CAAC,sBAAsB,EAAE;YACnD,oBAAoB,EAAE,IAAI,CAAC,sBAAsB,EAAE;YACnD,4BAA4B,EAAE,IAAI,CAAC,0BAA0B,EAAE,CAAC,QAAQ,EAAE;YAC1E,+BAA+B,EAAE,IAAI,CAAC,6BAA6B,EAAE,CAAC,QAAQ,EAAE;YAChF,oBAAoB,EAAE,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,QAAQ,EAAE;YAClE,uBAAuB,EAAE,IAAI,CAAC,qBAAqB,EAAE,CAAC,QAAQ,EAAE;SACjE,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,gBAAgB;QAOd,OAAO;YACL,MAAM,EAAE,0BAA0B;YAClC,QAAQ,EAAE,eAAe;YACzB,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe;YAC1C,OAAO,EAAE;gBACP,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK;gBAC3B,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK;gBAC3B,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ;gBACjC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ;gBACjC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;gBACzB,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU;gBACrC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS;gBACnC,kBAAkB,EAAE,IAAI,CAAC,SAAS,CAAC,kBAAkB;gBACrD,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe;gBAC/C,0BAA0B,EAAE,IAAI,CAAC,0BAA0B,EAAE;gBAC7D,6BAA6B,EAAE,IAAI,CAAC,6BAA6B,EAAE;gBACnE,mBAAmB,EAAE,IAAI,CAAC,sBAAsB,EAAE;gBAClD,cAAc,EAAE,IAAI,CAAC,iBAAiB,EAAE;gBACxC,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,EAAE;aACjD;YACD,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;SACzC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,kBAAkB;QA0ChB,OAAO;YACL,SAAS,EAAE,yBAAyB;YACpC,OAAO,EAAE,KAAK;YACd,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;YACxC,IAAI,EAAE;gBACJ,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe;gBAC/C,aAAa,EAAE;oBACb,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK;oBAC3B,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK;oBAC3B,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ;oBACjC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ;oBACjC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;oBACzB,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU;oBACrC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS;iBACpC;gBACD,SAAS,EAAE;oBACT,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe;oBACtC,kBAAkB,EAAE,IAAI,CAAC,SAAS,CAAC,kBAAkB;oBACrD,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS;iBACpC;gBACD,QAAQ,EAAE;oBACR,QAAQ,EAAE,IAAI,CAAC,sBAAsB,EAAE;oBACvC,OAAO,EAAE,IAAI,CAAC,sBAAsB,EAAE;oBACtC,cAAc,EAAE,IAAI,CAAC,iBAAiB,EAAE;oBACxC,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,EAAE;oBAChD,gBAAgB,EAAE,IAAI,CAAC,2BAA2B,EAAE;iBACrD;gBACD,KAAK,EAAE;oBACL,0BAA0B,EAAE,IAAI,CAAC,0BAA0B,EAAE;oBAC7D,6BAA6B,EAAE,IAAI,CAAC,6BAA6B,EAAE;oBACnE,2BAA2B,EAAE,IAAI,CAAC,2BAA2B,EAAE;oBAC/D,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EAAE;oBACzC,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE;iBACpD;aACF;YACD,QAAQ,EAAE;gBACR,aAAa,EAAE,IAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,MAAM,EAAE,UAAU;gBAClB,aAAa,EAAE,eAAe;aAC/B;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,GAAG,KAAK,CAAC,MAAM,EAAE;YACjB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE;gBACR,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,EAAE;gBAC7C,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE;gBACnD,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE;gBAC3C,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EAAE;gBACzC,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE;gBACnD,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE;gBAC3C,0BAA0B,EAAE,IAAI,CAAC,0BAA0B,EAAE;gBAC7D,6BAA6B,EAAE,IAAI,CAAC,6BAA6B,EAAE;gBACnE,2BAA2B,EAAE,IAAI,CAAC,2BAA2B,EAAE;gBAC/D,mBAAmB,EAAE,IAAI,CAAC,sBAAsB,EAAE;gBAClD,cAAc,EAAE,IAAI,CAAC,iBAAiB,EAAE;gBACxC,mBAAmB,EAAE,IAAI,CAAC,sBAAsB,EAAE;gBAClD,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,EAAE;gBAChD,oBAAoB,EAAE,IAAI,CAAC,uBAAuB,EAAE;gBACpD,wBAAwB,EAAE,IAAI,CAAC,2BAA2B,EAAE;gBAC5D,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE;aACnC;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,IAAyB;QACvC,OAAO,IAAI,4BAA4B,CACrC,8BAAc,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,EAC3C,IAAI,CAAC,SAAS,EACd;YACE,OAAO,EAAE,8BAAc,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC;YAChD,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;YACrC,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;QAC3D,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QACnE,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QACzE,MAAM,SAAS,GAAG,IAAI,CAAC,qBAAqB,EAAE;YAC5C,CAAC,CAAC,cAAc,IAAI,CAAC,SAAS,CAAC,kBAAkB,SAAS;YAC1D,CAAC,CAAC,yBAAyB,CAAC;QAE9B,OAAO,GAAG,YAAY,4BAA4B,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,OAAO,oBAAoB,cAAc,cAAc,SAAS,EAAE,CAAC;IAC/I,CAAC;IAED;;OAEG;IACH,UAAU;QAaR,OAAO;YACL,SAAS,EAAE,yBAAyB;YACpC,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe;YAC/C,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK;YAC3B,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK;YAC3B,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ;YACjC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ;YACjC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS;YACnC,kBAAkB,EAAE,IAAI,CAAC,SAAS,CAAC,kBAAkB;YACrD,0BAA0B,EAAE,IAAI,CAAC,0BAA0B,EAAE;YAC7D,mBAAmB,EAAE,IAAI,CAAC,sBAAsB,EAAE;YAClD,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;SACzC,CAAC;IACJ,CAAC;CACF;AAxgBD,oEAwgBC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\vulnerability-discovered.event.ts"], "sourcesContent": ["import { BaseDomainEvent, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { ThreatSeverity } from '../enums/threat-severity.enum';\r\nimport { ConfidenceLevel } from '../enums/confidence-level.enum';\r\n\r\n/**\r\n * Vulnerability Discovered Event Data\r\n */\r\nexport interface VulnerabilityDiscoveredEventData {\r\n  /** ID of the discovered vulnerability */\r\n  vulnerabilityId: string;\r\n  /** CVE identifier if available */\r\n  cveId?: string;\r\n  /** Vulnerability title */\r\n  title: string;\r\n  /** Vulnerability severity */\r\n  severity: ThreatSeverity;\r\n  /** Vulnerability category */\r\n  category: string;\r\n  /** Vulnerability type */\r\n  type: string;\r\n  /** Discovery confidence */\r\n  confidence: ConfidenceLevel;\r\n  /** Risk score (0-100) */\r\n  riskScore: number;\r\n  /** Number of affected assets */\r\n  affectedAssetCount: number;\r\n  /** Discovery method */\r\n  discoveryMethod: string;\r\n  /** When the vulnerability was discovered */\r\n  timestamp: string;\r\n  /** Discovery metadata */\r\n  metadata?: {\r\n    discoverySource: string;\r\n    discoveredBy: string;\r\n    scannerInfo?: {\r\n      name: string;\r\n      version: string;\r\n      ruleId: string;\r\n    };\r\n    assetCriticality: string;\r\n    networkExposure: string;\r\n    complianceImpact: string[];\r\n  };\r\n}\r\n\r\n/**\r\n * Vulnerability Discovered Domain Event\r\n * \r\n * Published when a new security vulnerability is discovered in the environment.\r\n * This event triggers vulnerability management and remediation workflows.\r\n * \r\n * Key use cases:\r\n * - Initiate vulnerability assessment procedures\r\n * - Trigger remediation planning workflows\r\n * - Send vulnerability notifications\r\n * - Update vulnerability databases\r\n * - Begin compliance impact analysis\r\n * - Escalate critical vulnerabilities\r\n */\r\nexport class VulnerabilityDiscoveredEvent extends BaseDomainEvent<VulnerabilityDiscoveredEventData> {\r\n  constructor(\r\n    aggregateId: UniqueEntityId,\r\n    eventData: VulnerabilityDiscoveredEventData,\r\n    options?: {\r\n      eventId?: UniqueEntityId;\r\n      occurredOn?: Date;\r\n      correlationId?: string;\r\n      causationId?: string;\r\n      metadata?: Record<string, any>;\r\n    }\r\n  ) {\r\n    super(aggregateId, eventData, {\r\n      eventVersion: 1,\r\n      ...options,\r\n      metadata: {\r\n        eventType: 'VulnerabilityDiscovered',\r\n        domain: 'Security',\r\n        aggregateType: 'Vulnerability',\r\n        ...options?.metadata,\r\n      },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get the vulnerability ID\r\n   */\r\n  get vulnerabilityId(): string {\r\n    return this.eventData.vulnerabilityId;\r\n  }\r\n\r\n  /**\r\n   * Get the CVE ID\r\n   */\r\n  get cveId(): string | undefined {\r\n    return this.eventData.cveId;\r\n  }\r\n\r\n  /**\r\n   * Get the vulnerability title\r\n   */\r\n  get title(): string {\r\n    return this.eventData.title;\r\n  }\r\n\r\n  /**\r\n   * Get the vulnerability severity\r\n   */\r\n  get severity(): ThreatSeverity {\r\n    return this.eventData.severity;\r\n  }\r\n\r\n  /**\r\n   * Get the vulnerability category\r\n   */\r\n  get category(): string {\r\n    return this.eventData.category;\r\n  }\r\n\r\n  /**\r\n   * Get the vulnerability type\r\n   */\r\n  get vulnerabilityType(): string {\r\n    return this.eventData.type;\r\n  }\r\n\r\n  /**\r\n   * Get the discovery confidence\r\n   */\r\n  get confidence(): ConfidenceLevel {\r\n    return this.eventData.confidence;\r\n  }\r\n\r\n  /**\r\n   * Get the risk score\r\n   */\r\n  get riskScore(): number {\r\n    return this.eventData.riskScore;\r\n  }\r\n\r\n  /**\r\n   * Get the affected asset count\r\n   */\r\n  get affectedAssetCount(): number {\r\n    return this.eventData.affectedAssetCount;\r\n  }\r\n\r\n  /**\r\n   * Get the discovery method\r\n   */\r\n  get discoveryMethod(): string {\r\n    return this.eventData.discoveryMethod;\r\n  }\r\n\r\n  /**\r\n   * Get the discovery timestamp\r\n   */\r\n  get discoveryTimestamp(): string {\r\n    return this.eventData.timestamp;\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability is critical severity\r\n   */\r\n  isCriticalSeverity(): boolean {\r\n    return this.eventData.severity === ThreatSeverity.CRITICAL;\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability is high severity or above\r\n   */\r\n  isHighSeverityOrAbove(): boolean {\r\n    return this.eventData.severity === ThreatSeverity.HIGH || \r\n           this.eventData.severity === ThreatSeverity.CRITICAL;\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability has high confidence\r\n   */\r\n  hasHighConfidence(): boolean {\r\n    return this.eventData.confidence === ConfidenceLevel.HIGH ||\r\n           this.eventData.confidence === ConfidenceLevel.VERY_HIGH ||\r\n           this.eventData.confidence === ConfidenceLevel.CONFIRMED;\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability has high risk score\r\n   */\r\n  hasHighRiskScore(): boolean {\r\n    return this.eventData.riskScore >= 70;\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability affects multiple assets\r\n   */\r\n  affectsMultipleAssets(): boolean {\r\n    return this.eventData.affectedAssetCount > 1;\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability affects many assets\r\n   */\r\n  affectsManyAssets(): boolean {\r\n    return this.eventData.affectedAssetCount >= 10;\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability was discovered by automated scan\r\n   */\r\n  isAutomatedDiscovery(): boolean {\r\n    return this.eventData.discoveryMethod === 'automated_scan';\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability requires immediate attention\r\n   */\r\n  requiresImmediateAttention(): boolean {\r\n    return this.isCriticalSeverity() || \r\n           (this.isHighSeverityOrAbove() && this.hasHighRiskScore()) ||\r\n           (this.hasHighRiskScore() && this.affectsManyAssets());\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability requires executive notification\r\n   */\r\n  requiresExecutiveNotification(): boolean {\r\n    return this.isCriticalSeverity() && \r\n           (this.hasHighRiskScore() || this.affectsManyAssets());\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability requires compliance reporting\r\n   */\r\n  requiresComplianceReporting(): boolean {\r\n    return this.isCriticalSeverity() || \r\n           (this.isHighSeverityOrAbove() && this.hasHighRiskScore());\r\n  }\r\n\r\n  /**\r\n   * Get remediation priority\r\n   */\r\n  getRemediationPriority(): 'low' | 'medium' | 'high' | 'critical' {\r\n    if (this.requiresImmediateAttention()) {\r\n      return 'critical';\r\n    }\r\n    if (this.isHighSeverityOrAbove()) {\r\n      return 'high';\r\n    }\r\n    if (this.eventData.severity === ThreatSeverity.MEDIUM) {\r\n      return 'medium';\r\n    }\r\n    return 'low';\r\n  }\r\n\r\n  /**\r\n   * Get remediation SLA (in days)\r\n   */\r\n  getRemediationSLA(): number {\r\n    switch (this.eventData.severity) {\r\n      case ThreatSeverity.CRITICAL: return 1;\r\n      case ThreatSeverity.HIGH: return 7;\r\n      case ThreatSeverity.MEDIUM: return 30;\r\n      case ThreatSeverity.LOW: return 90;\r\n      default: return 180;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get notification urgency\r\n   */\r\n  getNotificationUrgency(): 'low' | 'normal' | 'high' | 'critical' {\r\n    if (this.requiresImmediateAttention()) {\r\n      return 'critical';\r\n    }\r\n    if (this.isHighSeverityOrAbove()) {\r\n      return 'high';\r\n    }\r\n    if (this.hasHighRiskScore() || this.affectsMultipleAssets()) {\r\n      return 'normal';\r\n    }\r\n    return 'low';\r\n  }\r\n\r\n  /**\r\n   * Get assessment timeline (hours)\r\n   */\r\n  getAssessmentTimeline(): number {\r\n    switch (this.eventData.severity) {\r\n      case ThreatSeverity.CRITICAL: return 2;   // 2 hours\r\n      case ThreatSeverity.HIGH: return 8;       // 8 hours\r\n      case ThreatSeverity.MEDIUM: return 24;    // 24 hours\r\n      case ThreatSeverity.LOW: return 72;       // 3 days\r\n      default: return 168;                      // 7 days\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get notification channels\r\n   */\r\n  getNotificationChannels(): string[] {\r\n    const channels: string[] = ['webhook'];\r\n\r\n    if (this.requiresImmediateAttention()) {\r\n      channels.push('email', 'slack', 'sms');\r\n    } else if (this.isHighSeverityOrAbove()) {\r\n      channels.push('email', 'slack');\r\n    } else if (this.hasHighRiskScore()) {\r\n      channels.push('email');\r\n    }\r\n\r\n    if (this.requiresExecutiveNotification()) {\r\n      channels.push('executive_dashboard');\r\n    }\r\n\r\n    return channels;\r\n  }\r\n\r\n  /**\r\n   * Get automated response actions\r\n   */\r\n  getAutomatedResponseActions(): string[] {\r\n    const actions: string[] = [];\r\n\r\n    if (this.requiresImmediateAttention()) {\r\n      actions.push('create_vulnerability_ticket', 'assign_security_analyst', 'begin_assessment');\r\n    }\r\n\r\n    if (this.affectsManyAssets()) {\r\n      actions.push('asset_inventory_check', 'impact_assessment');\r\n    }\r\n\r\n    if (this.hasHighRiskScore()) {\r\n      actions.push('threat_intelligence_lookup', 'exploit_availability_check');\r\n    }\r\n\r\n    if (this.requiresComplianceReporting()) {\r\n      actions.push('compliance_impact_assessment', 'regulatory_notification_prep');\r\n    }\r\n\r\n    if (this.eventData.cveId) {\r\n      actions.push('cve_database_lookup', 'patch_availability_check');\r\n    }\r\n\r\n    return actions;\r\n  }\r\n\r\n  /**\r\n   * Get metrics tags for this event\r\n   */\r\n  getMetricsTags(): Record<string, string> {\r\n    return {\r\n      event_type: 'vulnerability_discovered',\r\n      severity: this.eventData.severity,\r\n      category: this.eventData.category,\r\n      type: this.eventData.type,\r\n      confidence: this.eventData.confidence,\r\n      discovery_method: this.eventData.discoveryMethod,\r\n      has_cve: (!!this.eventData.cveId).toString(),\r\n      risk_level: this.hasHighRiskScore() ? 'high' : 'low',\r\n      remediation_priority: this.getRemediationPriority(),\r\n      notification_urgency: this.getNotificationUrgency(),\r\n      requires_immediate_attention: this.requiresImmediateAttention().toString(),\r\n      requires_executive_notification: this.requiresExecutiveNotification().toString(),\r\n      affected_asset_count: this.eventData.affectedAssetCount.toString(),\r\n      affects_multiple_assets: this.affectsMultipleAssets().toString(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get audit log entry\r\n   */\r\n  getAuditLogEntry(): {\r\n    action: string;\r\n    resource: string;\r\n    resourceId: string;\r\n    details: Record<string, any>;\r\n    timestamp: string;\r\n  } {\r\n    return {\r\n      action: 'vulnerability_discovered',\r\n      resource: 'Vulnerability',\r\n      resourceId: this.eventData.vulnerabilityId,\r\n      details: {\r\n        cveId: this.eventData.cveId,\r\n        title: this.eventData.title,\r\n        severity: this.eventData.severity,\r\n        category: this.eventData.category,\r\n        type: this.eventData.type,\r\n        confidence: this.eventData.confidence,\r\n        riskScore: this.eventData.riskScore,\r\n        affectedAssetCount: this.eventData.affectedAssetCount,\r\n        discoveryMethod: this.eventData.discoveryMethod,\r\n        requiresImmediateAttention: this.requiresImmediateAttention(),\r\n        requiresExecutiveNotification: this.requiresExecutiveNotification(),\r\n        remediationPriority: this.getRemediationPriority(),\r\n        remediationSLA: this.getRemediationSLA(),\r\n        assessmentTimeline: this.getAssessmentTimeline(),\r\n      },\r\n      timestamp: this.occurredOn.toISOString(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create integration event for external systems\r\n   */\r\n  toIntegrationEvent(): {\r\n    eventType: string;\r\n    version: string;\r\n    timestamp: string;\r\n    data: {\r\n      vulnerabilityId: string;\r\n      vulnerability: {\r\n        cveId?: string;\r\n        title: string;\r\n        severity: string;\r\n        category: string;\r\n        type: string;\r\n        confidence: string;\r\n        riskScore: number;\r\n      };\r\n      discovery: {\r\n        method: string;\r\n        affectedAssetCount: number;\r\n        timestamp: string;\r\n      };\r\n      response: {\r\n        priority: string;\r\n        urgency: string;\r\n        remediationSLA: number;\r\n        assessmentTimeline: number;\r\n        automatedActions: string[];\r\n      };\r\n      flags: {\r\n        requiresImmediateAttention: boolean;\r\n        requiresExecutiveNotification: boolean;\r\n        requiresComplianceReporting: boolean;\r\n        hasHighRiskScore: boolean;\r\n        affectsMultipleAssets: boolean;\r\n      };\r\n    };\r\n    metadata: {\r\n      correlationId?: string;\r\n      causationId?: string;\r\n      domain: string;\r\n      aggregateType: string;\r\n    };\r\n  } {\r\n    return {\r\n      eventType: 'VulnerabilityDiscovered',\r\n      version: '1.0',\r\n      timestamp: this.occurredOn.toISOString(),\r\n      data: {\r\n        vulnerabilityId: this.eventData.vulnerabilityId,\r\n        vulnerability: {\r\n          cveId: this.eventData.cveId,\r\n          title: this.eventData.title,\r\n          severity: this.eventData.severity,\r\n          category: this.eventData.category,\r\n          type: this.eventData.type,\r\n          confidence: this.eventData.confidence,\r\n          riskScore: this.eventData.riskScore,\r\n        },\r\n        discovery: {\r\n          method: this.eventData.discoveryMethod,\r\n          affectedAssetCount: this.eventData.affectedAssetCount,\r\n          timestamp: this.eventData.timestamp,\r\n        },\r\n        response: {\r\n          priority: this.getRemediationPriority(),\r\n          urgency: this.getNotificationUrgency(),\r\n          remediationSLA: this.getRemediationSLA(),\r\n          assessmentTimeline: this.getAssessmentTimeline(),\r\n          automatedActions: this.getAutomatedResponseActions(),\r\n        },\r\n        flags: {\r\n          requiresImmediateAttention: this.requiresImmediateAttention(),\r\n          requiresExecutiveNotification: this.requiresExecutiveNotification(),\r\n          requiresComplianceReporting: this.requiresComplianceReporting(),\r\n          hasHighRiskScore: this.hasHighRiskScore(),\r\n          affectsMultipleAssets: this.affectsMultipleAssets(),\r\n        },\r\n      },\r\n      metadata: {\r\n        correlationId: this.correlationId,\r\n        causationId: this.causationId,\r\n        domain: 'Security',\r\n        aggregateType: 'Vulnerability',\r\n      },\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      ...super.toJSON(),\r\n      eventData: this.eventData,\r\n      analysis: {\r\n        isCriticalSeverity: this.isCriticalSeverity(),\r\n        isHighSeverityOrAbove: this.isHighSeverityOrAbove(),\r\n        hasHighConfidence: this.hasHighConfidence(),\r\n        hasHighRiskScore: this.hasHighRiskScore(),\r\n        affectsMultipleAssets: this.affectsMultipleAssets(),\r\n        affectsManyAssets: this.affectsManyAssets(),\r\n        requiresImmediateAttention: this.requiresImmediateAttention(),\r\n        requiresExecutiveNotification: this.requiresExecutiveNotification(),\r\n        requiresComplianceReporting: this.requiresComplianceReporting(),\r\n        remediationPriority: this.getRemediationPriority(),\r\n        remediationSLA: this.getRemediationSLA(),\r\n        notificationUrgency: this.getNotificationUrgency(),\r\n        assessmentTimeline: this.getAssessmentTimeline(),\r\n        notificationChannels: this.getNotificationChannels(),\r\n        automatedResponseActions: this.getAutomatedResponseActions(),\r\n        metricsTags: this.getMetricsTags(),\r\n      },\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create from JSON representation\r\n   */\r\n  static fromJSON(json: Record<string, any>): VulnerabilityDiscoveredEvent {\r\n    return new VulnerabilityDiscoveredEvent(\r\n      UniqueEntityId.fromString(json.aggregateId),\r\n      json.eventData,\r\n      {\r\n        eventId: UniqueEntityId.fromString(json.eventId),\r\n        occurredOn: new Date(json.occurredOn),\r\n        correlationId: json.correlationId,\r\n        causationId: json.causationId,\r\n        metadata: json.metadata,\r\n      }\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get human-readable description\r\n   */\r\n  getDescription(): string {\r\n    const severityText = this.eventData.severity.toUpperCase();\r\n    const confidenceText = this.eventData.confidence.replace('_', ' ');\r\n    const cveText = this.eventData.cveId ? ` (${this.eventData.cveId})` : '';\r\n    const assetText = this.affectsMultipleAssets() \r\n      ? ` affecting ${this.eventData.affectedAssetCount} assets` \r\n      : ' affecting single asset';\r\n    \r\n    return `${severityText} severity vulnerability \"${this.eventData.title}\"${cveText} discovered with ${confidenceText} confidence${assetText}`;\r\n  }\r\n\r\n  /**\r\n   * Get event summary for logging\r\n   */\r\n  getSummary(): {\r\n    eventType: string;\r\n    vulnerabilityId: string;\r\n    cveId?: string;\r\n    title: string;\r\n    severity: string;\r\n    category: string;\r\n    riskScore: number;\r\n    affectedAssetCount: number;\r\n    requiresImmediateAttention: boolean;\r\n    remediationPriority: string;\r\n    timestamp: string;\r\n  } {\r\n    return {\r\n      eventType: 'VulnerabilityDiscovered',\r\n      vulnerabilityId: this.eventData.vulnerabilityId,\r\n      cveId: this.eventData.cveId,\r\n      title: this.eventData.title,\r\n      severity: this.eventData.severity,\r\n      category: this.eventData.category,\r\n      riskScore: this.eventData.riskScore,\r\n      affectedAssetCount: this.eventData.affectedAssetCount,\r\n      requiresImmediateAttention: this.requiresImmediateAttention(),\r\n      remediationPriority: this.getRemediationPriority(),\r\n      timestamp: this.occurredOn.toISOString(),\r\n    };\r\n  }\r\n}\r\n"], "version": 3}