{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting-analytics\\domain\\entities\\report-schedule.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCASiB;AACjB,mDAAyC;AAEzC;;;GAGG;AAKI,IAAM,cAAc,GAApB,MAAM,cAAc;IAgJzB;;OAEG;IACH,IAAI,KAAK;QACP,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO,KAAK,CAAC;QACpD,OAAO,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAClC,OAAO,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,MAAwC;QACrD,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;QACnB,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC5B,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAE5B,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YACzB,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC;QACzB,CAAC;aAAM,IAAI,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC/B,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC;QACzB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,yEAAyE;QACzE,gDAAgD;QAChD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QAEvB,+BAA+B;QAC/B,IAAI,IAAI,CAAC,cAAc,KAAK,WAAW,EAAE,CAAC,CAAC,oBAAoB;YAC7D,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;YAC9B,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;YACvC,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC7B,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC;QAC3B,CAAC;aAAM,IAAI,IAAI,CAAC,cAAc,KAAK,WAAW,EAAE,CAAC,CAAC,mBAAmB;YACnE,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;YAC9B,MAAM,eAAe,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACxD,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,eAAe,CAAC,CAAC;YACrD,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC7B,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC;QAC3B,CAAC;aAAM,IAAI,IAAI,CAAC,cAAc,KAAK,WAAW,EAAE,CAAC,CAAC,iBAAiB;YACjE,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;YAC9B,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;YAC5C,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC7B,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC;QAC3B,CAAC;aAAM,CAAC;YACN,sCAAsC;YACtC,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;YAC9B,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;YACvC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC;QAC3B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,yEAAyE;QACzE,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAE7C,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,mEAAmE;aAC3E,CAAC;QACJ,CAAC;QAED,4BAA4B;QAC5B,MAAM,aAAa,GAAG;YACpB,WAAW,EAAM,oBAAoB;YACrC,WAAW,EAAM,mBAAmB;YACpC,WAAW,EAAM,iBAAiB;YAClC,aAAa,EAAI,gBAAgB;YACjC,cAAc,EAAG,mBAAmB;SACrC,CAAC;QAEF,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC;YACjD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,qCAAqC;aAC7C,CAAC;QACJ,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC3B,CAAC;CACF,CAAA;AAhQY,wCAAc;AAEzB;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;0CACpB;AAMX;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;4CACX;AAMb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDACpB;AAMrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;gDAC3B;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,CAAC;;sDACb;AAMvB;IADC,IAAA,gBAAM,EAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;gDACV;AAMjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kDAWxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDAgBxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDAM9D;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACtE,IAAI,oBAAJ,IAAI;iDAAC;AAMjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACtE,IAAI,oBAAJ,IAAI;iDAAC;AAMjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDACH;AAMjD;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;gDAC1C;AAMjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;oDAC1C;AAMrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;oDAC1C;AAMrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;iDAC3B;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;iDAC1C;AAGnB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;iDAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;iDAAC;AAKhB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,sBAAM,CAAC;IACvB,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;kDAC1B,sBAAM,oBAAN,sBAAM;8CAAC;AAGf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;gDAC3B;yBA9IN,cAAc;IAJ1B,IAAA,gBAAM,EAAC,kBAAkB,CAAC;IAC1B,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,WAAW,CAAC,CAAC;GACR,cAAc,CAgQ1B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting-analytics\\domain\\entities\\report-schedule.entity.ts"], "sourcesContent": ["import {\r\n  <PERSON><PERSON><PERSON>,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  ManyToOne,\r\n  JoinColumn,\r\n} from 'typeorm';\r\nimport { Report } from './report.entity';\r\n\r\n/**\r\n * Report Schedule entity\r\n * Represents scheduled execution configurations for reports\r\n */\r\n@Entity('report_schedules')\r\n@Index(['reportId'])\r\n@Index(['isActive'])\r\n@Index(['nextRunAt'])\r\nexport class ReportSchedule {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * Schedule name\r\n   */\r\n  @Column({ length: 255 })\r\n  name: string;\r\n\r\n  /**\r\n   * Schedule description\r\n   */\r\n  @Column({ type: 'text', nullable: true })\r\n  description?: string;\r\n\r\n  /**\r\n   * Whether the schedule is active\r\n   */\r\n  @Column({ name: 'is_active', default: true })\r\n  isActive: boolean;\r\n\r\n  /**\r\n   * Cron expression for scheduling\r\n   */\r\n  @Column({ name: 'cron_expression' })\r\n  cronExpression: string;\r\n\r\n  /**\r\n   * Timezone for the schedule\r\n   */\r\n  @Column({ default: 'UTC' })\r\n  timezone: string;\r\n\r\n  /**\r\n   * Schedule parameters (overrides for report execution)\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  parameters?: {\r\n    timeRange?: {\r\n      type: 'relative' | 'absolute';\r\n      value?: string;\r\n      startDate?: string;\r\n      endDate?: string;\r\n    };\r\n    filters?: Record<string, any>;\r\n    exportFormat?: string;\r\n    customParameters?: Record<string, any>;\r\n  };\r\n\r\n  /**\r\n   * Notification settings\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  notifications?: {\r\n    onSuccess?: {\r\n      enabled: boolean;\r\n      recipients: string[];\r\n      includeReport: boolean;\r\n    };\r\n    onFailure?: {\r\n      enabled: boolean;\r\n      recipients: string[];\r\n      includeErrorDetails: boolean;\r\n    };\r\n    channels?: Array<{\r\n      type: 'email' | 'slack' | 'webhook';\r\n      configuration: Record<string, any>;\r\n    }>;\r\n  };\r\n\r\n  /**\r\n   * Retry configuration\r\n   */\r\n  @Column({ name: 'retry_config', type: 'jsonb', nullable: true })\r\n  retryConfig?: {\r\n    enabled: boolean;\r\n    maxRetries: number;\r\n    retryInterval: number; // minutes\r\n    backoffMultiplier: number;\r\n  };\r\n\r\n  /**\r\n   * Next scheduled run time\r\n   */\r\n  @Column({ name: 'next_run_at', type: 'timestamp with time zone', nullable: true })\r\n  nextRunAt?: Date;\r\n\r\n  /**\r\n   * Last run time\r\n   */\r\n  @Column({ name: 'last_run_at', type: 'timestamp with time zone', nullable: true })\r\n  lastRunAt?: Date;\r\n\r\n  /**\r\n   * Last run status\r\n   */\r\n  @Column({ name: 'last_run_status', nullable: true })\r\n  lastRunStatus?: 'success' | 'failed' | 'skipped';\r\n\r\n  /**\r\n   * Run count\r\n   */\r\n  @Column({ name: 'run_count', type: 'integer', default: 0 })\r\n  runCount: number;\r\n\r\n  /**\r\n   * Success count\r\n   */\r\n  @Column({ name: 'success_count', type: 'integer', default: 0 })\r\n  successCount: number;\r\n\r\n  /**\r\n   * Failure count\r\n   */\r\n  @Column({ name: 'failure_count', type: 'integer', default: 0 })\r\n  failureCount: number;\r\n\r\n  /**\r\n   * User who created the schedule\r\n   */\r\n  @Column({ name: 'created_by', type: 'uuid' })\r\n  createdBy: string;\r\n\r\n  /**\r\n   * User who last updated the schedule\r\n   */\r\n  @Column({ name: 'updated_by', type: 'uuid', nullable: true })\r\n  updatedBy?: string;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  // Relationships\r\n  @ManyToOne(() => Report)\r\n  @JoinColumn({ name: 'report_id' })\r\n  report: Report;\r\n\r\n  @Column({ name: 'report_id', type: 'uuid' })\r\n  reportId: string;\r\n\r\n  /**\r\n   * Check if schedule is due for execution\r\n   */\r\n  get isDue(): boolean {\r\n    if (!this.isActive || !this.nextRunAt) return false;\r\n    return new Date() >= this.nextRunAt;\r\n  }\r\n\r\n  /**\r\n   * Get success rate\r\n   */\r\n  get successRate(): number {\r\n    if (this.runCount === 0) return 0;\r\n    return (this.successCount / this.runCount) * 100;\r\n  }\r\n\r\n  /**\r\n   * Update run statistics\r\n   */\r\n  updateRunStats(status: 'success' | 'failed' | 'skipped'): void {\r\n    this.runCount += 1;\r\n    this.lastRunAt = new Date();\r\n    this.lastRunStatus = status;\r\n\r\n    if (status === 'success') {\r\n      this.successCount += 1;\r\n    } else if (status === 'failed') {\r\n      this.failureCount += 1;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate next run time based on cron expression\r\n   */\r\n  calculateNextRun(): void {\r\n    // This would use a cron parser library like 'node-cron' or 'cron-parser'\r\n    // For now, we'll implement a simple placeholder\r\n    const now = new Date();\r\n    \r\n    // Parse basic cron expressions\r\n    if (this.cronExpression === '0 0 * * *') { // Daily at midnight\r\n      const nextRun = new Date(now);\r\n      nextRun.setDate(nextRun.getDate() + 1);\r\n      nextRun.setHours(0, 0, 0, 0);\r\n      this.nextRunAt = nextRun;\r\n    } else if (this.cronExpression === '0 0 * * 0') { // Weekly on Sunday\r\n      const nextRun = new Date(now);\r\n      const daysUntilSunday = (7 - nextRun.getDay()) % 7 || 7;\r\n      nextRun.setDate(nextRun.getDate() + daysUntilSunday);\r\n      nextRun.setHours(0, 0, 0, 0);\r\n      this.nextRunAt = nextRun;\r\n    } else if (this.cronExpression === '0 0 1 * *') { // Monthly on 1st\r\n      const nextRun = new Date(now);\r\n      nextRun.setMonth(nextRun.getMonth() + 1, 1);\r\n      nextRun.setHours(0, 0, 0, 0);\r\n      this.nextRunAt = nextRun;\r\n    } else {\r\n      // Default to daily if unknown pattern\r\n      const nextRun = new Date(now);\r\n      nextRun.setDate(nextRun.getDate() + 1);\r\n      this.nextRunAt = nextRun;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Enable schedule\r\n   */\r\n  enable(): void {\r\n    this.isActive = true;\r\n    this.calculateNextRun();\r\n  }\r\n\r\n  /**\r\n   * Disable schedule\r\n   */\r\n  disable(): void {\r\n    this.isActive = false;\r\n    this.nextRunAt = null;\r\n  }\r\n\r\n  /**\r\n   * Validate cron expression\r\n   */\r\n  validateCronExpression(): { isValid: boolean; error?: string } {\r\n    // Basic validation - in a real implementation, use a cron parser library\r\n    const parts = this.cronExpression.split(' ');\r\n    \r\n    if (parts.length !== 5) {\r\n      return {\r\n        isValid: false,\r\n        error: 'Cron expression must have 5 parts (minute hour day month weekday)',\r\n      };\r\n    }\r\n\r\n    // Check for common patterns\r\n    const validPatterns = [\r\n      '0 0 * * *',     // Daily at midnight\r\n      '0 0 * * 0',     // Weekly on Sunday\r\n      '0 0 1 * *',     // Monthly on 1st\r\n      '0 */6 * * *',   // Every 6 hours\r\n      '*/30 * * * *',  // Every 30 minutes\r\n    ];\r\n\r\n    if (!validPatterns.includes(this.cronExpression)) {\r\n      return {\r\n        isValid: false,\r\n        error: 'Unsupported cron expression pattern',\r\n      };\r\n    }\r\n\r\n    return { isValid: true };\r\n  }\r\n}\r\n"], "version": 3}