c65c4e982afb292848ea9e54f1ee66ec
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const incident_management_service_1 = require("./incident-management.service");
const incident_entity_1 = require("../../domain/entities/incident.entity");
const incident_task_entity_1 = require("../../domain/entities/incident-task.entity");
const incident_evidence_entity_1 = require("../../domain/entities/incident-evidence.entity");
const incident_timeline_entity_1 = require("../../domain/entities/incident-timeline.entity");
const response_plan_entity_1 = require("../../domain/entities/response-plan.entity");
const logger_service_1 = require("../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../infrastructure/logging/audit/audit.service");
const notification_service_1 = require("./notification.service");
const response_orchestration_service_1 = require("./response-orchestration.service");
describe('IncidentManagementService', () => {
    let service;
    let incidentRepository;
    let taskRepository;
    let evidenceRepository;
    let timelineRepository;
    let responsePlanRepository;
    let loggerService;
    let auditService;
    let notificationService;
    let responseOrchestrationService;
    const mockIncidentRepository = {
        create: jest.fn(),
        save: jest.fn(),
        findOne: jest.fn(),
        find: jest.fn(),
        createQueryBuilder: jest.fn(),
        count: jest.fn(),
        remove: jest.fn(),
    };
    const mockTaskRepository = {
        create: jest.fn(),
        save: jest.fn(),
        findOne: jest.fn(),
        find: jest.fn(),
    };
    const mockEvidenceRepository = {
        create: jest.fn(),
        save: jest.fn(),
        findOne: jest.fn(),
        find: jest.fn(),
    };
    const mockTimelineRepository = {
        create: jest.fn(),
        save: jest.fn(),
        findOne: jest.fn(),
        find: jest.fn(),
    };
    const mockResponsePlanRepository = {
        find: jest.fn(),
        findOne: jest.fn(),
    };
    const mockLoggerService = {
        debug: jest.fn(),
        log: jest.fn(),
        warn: jest.fn(),
        error: jest.fn(),
    };
    const mockAuditService = {
        logUserAction: jest.fn(),
    };
    const mockNotificationService = {
        sendIncidentNotification: jest.fn(),
    };
    const mockResponseOrchestrationService = {
        executeResponsePlan: jest.fn(),
    };
    const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn(),
        getCount: jest.fn(),
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn(),
        getRawOne: jest.fn(),
    };
    const mockIncidentData = {
        title: 'Test Incident',
        description: 'Test incident description',
        severity: 'high',
        category: 'phishing',
        source: 'manual',
        detectedAt: new Date(),
        tags: ['test'],
        confidentiality: 'internal',
    };
    const mockIncident = {
        id: 'incident-123',
        title: 'Test Incident',
        description: 'Test incident description',
        status: 'new',
        severity: 'high',
        priority: 'high',
        category: 'phishing',
        source: 'manual',
        detectedAt: new Date(),
        tags: ['test'],
        confidentiality: 'internal',
        createdBy: 'user-123',
        isOpen: true,
        isCritical: true,
        isOverdue: false,
        ageInHours: 2,
        affectedAssetCount: 1,
        iocCount: 2,
        calculateRiskScore: jest.fn().mockReturnValue(8.5),
        acknowledge: jest.fn(),
        escalate: jest.fn(),
        resolve: jest.fn(),
        close: jest.fn(),
        addTeamMember: jest.fn(),
        tasks: [],
        evidence: [],
        communications: [],
        timeline: [],
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [
                incident_management_service_1.IncidentManagementService,
                {
                    provide: (0, typeorm_1.getRepositoryToken)(incident_entity_1.Incident),
                    useValue: mockIncidentRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(incident_task_entity_1.IncidentTask),
                    useValue: mockTaskRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(incident_evidence_entity_1.IncidentEvidence),
                    useValue: mockEvidenceRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(incident_timeline_entity_1.IncidentTimeline),
                    useValue: mockTimelineRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(response_plan_entity_1.ResponsePlan),
                    useValue: mockResponsePlanRepository,
                },
                {
                    provide: logger_service_1.LoggerService,
                    useValue: mockLoggerService,
                },
                {
                    provide: audit_service_1.AuditService,
                    useValue: mockAuditService,
                },
                {
                    provide: notification_service_1.NotificationService,
                    useValue: mockNotificationService,
                },
                {
                    provide: response_orchestration_service_1.ResponseOrchestrationService,
                    useValue: mockResponseOrchestrationService,
                },
            ],
        }).compile();
        service = module.get(incident_management_service_1.IncidentManagementService);
        incidentRepository = module.get((0, typeorm_1.getRepositoryToken)(incident_entity_1.Incident));
        taskRepository = module.get((0, typeorm_1.getRepositoryToken)(incident_task_entity_1.IncidentTask));
        evidenceRepository = module.get((0, typeorm_1.getRepositoryToken)(incident_evidence_entity_1.IncidentEvidence));
        timelineRepository = module.get((0, typeorm_1.getRepositoryToken)(incident_timeline_entity_1.IncidentTimeline));
        responsePlanRepository = module.get((0, typeorm_1.getRepositoryToken)(response_plan_entity_1.ResponsePlan));
        loggerService = module.get(logger_service_1.LoggerService);
        auditService = module.get(audit_service_1.AuditService);
        notificationService = module.get(notification_service_1.NotificationService);
        responseOrchestrationService = module.get(response_orchestration_service_1.ResponseOrchestrationService);
        // Setup query builder mock
        mockIncidentRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('createIncident', () => {
        it('should create incident successfully', async () => {
            const userId = 'user-123';
            const savedIncident = { ...mockIncident, id: 'incident-123' };
            mockIncidentRepository.create.mockReturnValue(mockIncident);
            mockIncidentRepository.save.mockResolvedValue(savedIncident);
            mockTimelineRepository.create.mockReturnValue({});
            mockTimelineRepository.save.mockResolvedValue({});
            mockResponsePlanRepository.find.mockResolvedValue([]);
            const result = await service.createIncident(mockIncidentData, userId);
            expect(mockIncidentRepository.create).toHaveBeenCalledWith({
                ...mockIncidentData,
                priority: 'high', // calculated from severity
                createdBy: userId,
            });
            expect(mockIncidentRepository.save).toHaveBeenCalledWith(mockIncident);
            expect(mockTimelineRepository.create).toHaveBeenCalled();
            expect(mockNotificationService.sendIncidentNotification).toHaveBeenCalledWith(savedIncident, 'created');
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(userId, 'create', 'incident', savedIncident.id, expect.objectContaining({
                title: mockIncidentData.title,
                severity: mockIncidentData.severity,
                category: mockIncidentData.category,
            }));
            expect(result).toEqual(savedIncident);
        });
        it('should apply response plan if applicable', async () => {
            const userId = 'user-123';
            const savedIncident = { ...mockIncident, id: 'incident-123' };
            const responsePlan = {
                id: 'plan-123',
                name: 'Phishing Response Plan',
                isApplicableToIncident: jest.fn().mockReturnValue(true),
            };
            mockIncidentRepository.create.mockReturnValue(mockIncident);
            mockIncidentRepository.save.mockResolvedValue(savedIncident);
            mockTimelineRepository.create.mockReturnValue({});
            mockTimelineRepository.save.mockResolvedValue({});
            mockResponsePlanRepository.find.mockResolvedValue([responsePlan]);
            await service.createIncident(mockIncidentData, userId);
            expect(responsePlan.isApplicableToIncident).toHaveBeenCalledWith(mockIncident);
            expect(mockResponseOrchestrationService.executeResponsePlan).toHaveBeenCalledWith(savedIncident.id, responsePlan.id);
        });
    });
    describe('findById', () => {
        it('should return incident when found', async () => {
            const incidentId = 'incident-123';
            mockIncidentRepository.findOne.mockResolvedValue(mockIncident);
            const result = await service.findById(incidentId);
            expect(mockIncidentRepository.findOne).toHaveBeenCalledWith({
                where: { id: incidentId },
                relations: ['tasks', 'evidence', 'communications', 'timeline', 'responsePlan'],
            });
            expect(result).toEqual(mockIncident);
        });
        it('should return null when incident not found', async () => {
            const incidentId = 'non-existent';
            mockIncidentRepository.findOne.mockResolvedValue(null);
            const result = await service.findById(incidentId);
            expect(result).toBeNull();
        });
    });
    describe('findMany', () => {
        it('should return paginated incidents with filters', async () => {
            const options = {
                page: 1,
                limit: 10,
                status: ['new', 'investigating'],
                severity: ['high', 'critical'],
                search: 'phishing',
            };
            const incidents = [mockIncident];
            const total = 1;
            mockQueryBuilder.getManyAndCount.mockResolvedValue([incidents, total]);
            const result = await service.findMany(options);
            expect(mockIncidentRepository.createQueryBuilder).toHaveBeenCalledWith('incident');
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('incident.status IN (:...status)', { status: options.status });
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('incident.severity IN (:...severity)', { severity: options.severity });
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('(incident.title ILIKE :search OR incident.description ILIKE :search)', { search: '%phishing%' });
            expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith('incident.createdAt', 'DESC');
            expect(mockQueryBuilder.skip).toHaveBeenCalledWith(0);
            expect(mockQueryBuilder.take).toHaveBeenCalledWith(10);
            expect(result).toEqual({
                incidents,
                total,
                page: 1,
                totalPages: 1,
            });
        });
    });
    describe('acknowledgeIncident', () => {
        it('should acknowledge incident successfully', async () => {
            const incidentId = 'incident-123';
            const userId = 'user-123';
            const incident = { ...mockIncident, status: 'new' };
            jest.spyOn(service, 'findById').mockResolvedValue(incident);
            mockIncidentRepository.save.mockResolvedValue(incident);
            mockTimelineRepository.create.mockReturnValue({});
            mockTimelineRepository.save.mockResolvedValue({});
            const result = await service.acknowledgeIncident(incidentId, userId);
            expect(incident.acknowledge).toHaveBeenCalledWith(userId);
            expect(mockIncidentRepository.save).toHaveBeenCalledWith(incident);
            expect(mockTimelineRepository.create).toHaveBeenCalled();
            expect(mockNotificationService.sendIncidentNotification).toHaveBeenCalledWith(incident, 'acknowledged');
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(userId, 'acknowledge', 'incident', incidentId, expect.any(Object));
            expect(result).toEqual(incident);
        });
        it('should throw error when incident not found', async () => {
            const incidentId = 'non-existent';
            const userId = 'user-123';
            jest.spyOn(service, 'findById').mockResolvedValue(null);
            await expect(service.acknowledgeIncident(incidentId, userId))
                .rejects.toThrow('Incident not found');
        });
        it('should throw error when incident already acknowledged', async () => {
            const incidentId = 'incident-123';
            const userId = 'user-123';
            const incident = { ...mockIncident, status: 'investigating' };
            jest.spyOn(service, 'findById').mockResolvedValue(incident);
            await expect(service.acknowledgeIncident(incidentId, userId))
                .rejects.toThrow('Incident has already been acknowledged');
        });
    });
    describe('escalateIncident', () => {
        it('should escalate incident successfully', async () => {
            const incidentId = 'incident-123';
            const userId = 'user-123';
            const reason = 'Critical data breach detected';
            const incident = { ...mockIncident };
            jest.spyOn(service, 'findById').mockResolvedValue(incident);
            mockIncidentRepository.save.mockResolvedValue(incident);
            mockTimelineRepository.create.mockReturnValue({});
            mockTimelineRepository.save.mockResolvedValue({});
            const result = await service.escalateIncident(incidentId, reason, userId);
            expect(incident.escalate).toHaveBeenCalledWith(reason, userId);
            expect(mockIncidentRepository.save).toHaveBeenCalledWith(incident);
            expect(mockTimelineRepository.create).toHaveBeenCalled();
            expect(mockNotificationService.sendIncidentNotification).toHaveBeenCalledWith(incident, 'escalated');
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(userId, 'escalate', 'incident', incidentId, expect.objectContaining({ reason }));
            expect(result).toEqual(incident);
        });
    });
    describe('resolveIncident', () => {
        it('should resolve incident successfully', async () => {
            const incidentId = 'incident-123';
            const userId = 'user-123';
            const resolution = 'Phishing emails blocked, credentials reset';
            const incident = { ...mockIncident };
            jest.spyOn(service, 'findById').mockResolvedValue(incident);
            mockIncidentRepository.save.mockResolvedValue(incident);
            mockTimelineRepository.create.mockReturnValue({});
            mockTimelineRepository.save.mockResolvedValue({});
            const result = await service.resolveIncident(incidentId, resolution, userId);
            expect(incident.resolve).toHaveBeenCalledWith(userId, resolution);
            expect(mockIncidentRepository.save).toHaveBeenCalledWith(incident);
            expect(mockTimelineRepository.create).toHaveBeenCalled();
            expect(mockNotificationService.sendIncidentNotification).toHaveBeenCalledWith(incident, 'resolved');
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(userId, 'resolve', 'incident', incidentId, expect.objectContaining({ resolution }));
            expect(result).toEqual(incident);
        });
    });
    describe('addTeamMember', () => {
        it('should add team member successfully', async () => {
            const incidentId = 'incident-123';
            const userId = 'user-456';
            const role = 'analyst';
            const permissions = ['view_evidence', 'update_tasks'];
            const addedBy = 'user-123';
            const incident = { ...mockIncident };
            jest.spyOn(service, 'findById').mockResolvedValue(incident);
            mockIncidentRepository.save.mockResolvedValue(incident);
            mockTimelineRepository.create.mockReturnValue({});
            mockTimelineRepository.save.mockResolvedValue({});
            const result = await service.addTeamMember(incidentId, userId, role, permissions, addedBy);
            expect(incident.addTeamMember).toHaveBeenCalledWith(userId, role, permissions);
            expect(mockIncidentRepository.save).toHaveBeenCalledWith(incident);
            expect(mockTimelineRepository.create).toHaveBeenCalled();
            expect(mockAuditService.logUserAction).toHaveBeenCalledWith(addedBy, 'add_team_member', 'incident', incidentId, expect.objectContaining({ userId, role, permissions }));
            expect(result).toEqual(incident);
        });
    });
    describe('getStatistics', () => {
        it('should return incident statistics', async () => {
            const mockStats = {
                totalIncidents: 100,
                openIncidents: 25,
                criticalIncidents: 5,
                overdueIncidents: 3,
                byStatus: { new: 10, investigating: 15 },
                bySeverity: { high: 20, critical: 5 },
                performance: {
                    averageResolutionTime: 240,
                    slaCompliance: 85.5,
                },
            };
            mockIncidentRepository.count.mockResolvedValueOnce(100); // totalIncidents
            mockIncidentRepository.count.mockResolvedValueOnce(25); // openIncidents
            mockIncidentRepository.count.mockResolvedValueOnce(5); // criticalIncidents
            // Mock helper methods
            jest.spyOn(service, 'getOverdueIncidentsCount').mockResolvedValue(3);
            jest.spyOn(service, 'getIncidentsByStatus').mockResolvedValue(mockStats.byStatus);
            jest.spyOn(service, 'getIncidentsBySeverity').mockResolvedValue(mockStats.bySeverity);
            jest.spyOn(service, 'getAverageResolutionTime').mockResolvedValue(240);
            jest.spyOn(service, 'getSlaCompliance').mockResolvedValue(85.5);
            const result = await service.getStatistics();
            expect(result).toMatchObject({
                totalIncidents: 100,
                openIncidents: 25,
                criticalIncidents: 5,
                overdueIncidents: 3,
                byStatus: mockStats.byStatus,
                bySeverity: mockStats.bySeverity,
                performance: mockStats.performance,
            });
            expect(result.timestamp).toBeDefined();
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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