{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\database.config.ts", "mappings": ";;;;;;;;;;;;;AAAA,2CAA4C;AAE5C,2CAA4C;AAC5C,2CAA+C;AAiG/C;;GAEG;AACH,MAAM,SAAS;IACb,MAAM,CAAC,MAAM,CAAC,GAAW,EAAE,YAAoB;QAC7C,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC;IAC1C,CAAC;IAED,MAAM,CAAC,MAAM,CAAC,GAAW,EAAE,YAAoB;QAC7C,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC/B,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;QAC3C,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC;IAC1D,CAAC;IAED,MAAM,CAAC,OAAO,CAAC,GAAW,EAAE,YAAY,GAAG,KAAK;QAC9C,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC/B,OAAO,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,KAAK,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC;IAC5E,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,GAAW,EAAE,YAA0B;QACzD,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAiB,CAAC;QAC/C,MAAM,UAAU,GAAG,IAAI,GAAG,CAAe,CAAC,UAAU,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC;QAC3E,OAAO,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC;IACtD,CAAC;IAED,MAAM,CAAC,cAAc,CAAC,GAAW;QAC/B,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,SAAS,CAAC;IACvC,CAAC;CACF;AAED;;GAEG;AACH,MAAM,gBAAgB,GAAG,GAAuB,EAAE,CAAC,CAAC;IAClD,GAAG,EAAE,SAAS,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC,CAAC;IAC7C,GAAG,EAAE,SAAS,CAAC,MAAM,CAAC,mBAAmB,EAAE,GAAG,CAAC;IAC/C,oBAAoB,EAAE,SAAS,CAAC,MAAM,CAAC,+BAA+B,EAAE,KAAK,CAAC;IAC9E,mBAAmB,EAAE,SAAS,CAAC,MAAM,CAAC,8BAA8B,EAAE,KAAK,CAAC;IAC5E,oBAAoB,EAAE,SAAS,CAAC,MAAM,CAAC,+BAA+B,EAAE,IAAI,CAAC;IAC7E,iBAAiB,EAAE,SAAS,CAAC,MAAM,CAAC,4BAA4B,EAAE,KAAK,CAAC;IACxE,kBAAkB,EAAE,SAAS,CAAC,MAAM,CAAC,6BAA6B,EAAE,IAAI,CAAC;IACzE,yBAAyB,EAAE,SAAS,CAAC,MAAM,CAAC,qCAAqC,EAAE,GAAG,CAAC;IACvF,oBAAoB,EAAE,SAAS,CAAC,OAAO,CAAC,sCAAsC,EAAE,KAAK,CAAC;CACvF,CAAC,CAAC;AAEH;;GAEG;AACH,MAAM,eAAe,GAAG,GAAsB,EAAE,CAAC,CAAC;IAChD,OAAO,EAAE,SAAS,CAAC,OAAO,CAAC,cAAc,EAAE,KAAK,CAAC;IACjD,kBAAkB,EAAE,SAAS,CAAC,OAAO,CAAC,kCAAkC,EAAE,KAAK,CAAC;IAChF,EAAE,EAAE,SAAS,CAAC,cAAc,CAAC,iBAAiB,CAAC;IAC/C,IAAI,EAAE,SAAS,CAAC,cAAc,CAAC,mBAAmB,CAAC;IACnD,GAAG,EAAE,SAAS,CAAC,cAAc,CAAC,kBAAkB,CAAC;CAClD,CAAC,CAAC;AAEH;;GAEG;AACH,MAAM,qBAAqB,GAAG,GAA4B,EAAE,CAAC,CAAC;IAC5D,GAAG,EAAE,SAAS,CAAC,OAAO,CAAC,yBAAyB,EAAE,IAAI,CAAC;IACvD,SAAS,EAAE,SAAS,CAAC,MAAM,CAAC,2BAA2B,EAAE,YAAY,CAAC;IACtE,SAAS,EAAE,SAAS,CAAC,MAAM,CAAC,yBAAyB,EAAE,yBAAyB,CAAC;IACjF,uBAAuB,EAAE,SAAS,CAAC,OAAO,CAAC,+CAA+C,EAAE,IAAI,CAAC;IACjG,mBAAmB,EAAE,SAAS,CAAC,OAAO,CAAC,0CAA0C,EAAE,KAAK,CAAC;CAC1F,CAAC,CAAC;AAEH;;GAEG;AACH,MAAM,mBAAmB,GAAG,GAA0B,EAAE;IACtD,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,aAAa,CAAC;IAEhE,OAAO;QACL,OAAO,EAAE,SAAS,CAAC,OAAO,CAAC,0BAA0B,EAAE,aAAa,CAAC;QACrE,UAAU,EAAE,SAAS,CAAC,OAAO,CAAC,sBAAsB,EAAE,aAAa,CAAC;QACpE,SAAS,EAAE,SAAS,CAAC,OAAO,CAAC,qBAAqB,EAAE,IAAI,CAAC;QACzD,cAAc,EAAE,SAAS,CAAC,OAAO,CAAC,2BAA2B,EAAE,KAAK,CAAC;QACrE,kBAAkB,EAAE,SAAS,CAAC,MAAM,CAAC,+BAA+B,EAAE,IAAI,CAAC;QAC3E,oBAAoB,EAAE,SAAS,CAAC,OAAO,CAAC,kCAAkC,EAAE,KAAK,CAAC;QAClF,qBAAqB,EAAE,SAAS,CAAC,MAAM,CAAC,mCAAmC,EAAE,KAAK,CAAC;KACpF,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,oBAAoB,GAAG,GAAmB,EAAE;IAChD,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,aAAa,CAAC;IAChE,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,MAAM,CAAC;IAElD,OAAO;QACL,IAAI,EAAE,SAAS,CAAC,YAAY,CAAC,eAAe,EAAE,UAAU,CAAC;QACzD,IAAI,EAAE,SAAS,CAAC,MAAM,CAAC,eAAe,EAAE,WAAW,CAAC;QACpD,IAAI,EAAE,SAAS,CAAC,MAAM,CAAC,eAAe,EAAE,IAAI,CAAC;QAC7C,QAAQ,EAAE,SAAS,CAAC,MAAM,CAAC,mBAAmB,EAAE,eAAe,CAAC;QAChE,QAAQ,EAAE,SAAS,CAAC,MAAM,CAAC,mBAAmB,EAAE,mBAAmB,CAAC;QACpE,QAAQ,EAAE,SAAS,CAAC,MAAM,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,aAAa,CAAC;QACrF,MAAM,EAAE,SAAS,CAAC,MAAM,CAAC,iBAAiB,EAAE,QAAQ,CAAC;QACrD,GAAG,EAAE,eAAe,EAAE;QACtB,IAAI,EAAE,gBAAgB,EAAE;QACxB,SAAS,EAAE,qBAAqB,EAAE;QAClC,OAAO,EAAE,mBAAmB,EAAE;QAC9B,WAAW,EAAE,SAAS,CAAC,OAAO,CAAC,sBAAsB,EAAE,aAAa,CAAC;QACrE,UAAU,EAAE,SAAS,CAAC,OAAO,CAAC,sBAAsB,EAAE,KAAK,CAAC;QAC5D,QAAQ,EAAE;YACR,GAAG,SAAS,6BAA6B;YACzC,GAAG,SAAS,wDAAwD;SACrE;QACD,UAAU,EAAE,CAAC,GAAG,SAAS,uCAAuC,CAAC;QACjE,WAAW,EAAE,CAAC,GAAG,SAAS,wCAAwC,CAAC;QACnE,QAAQ,EAAE,SAAS,CAAC,MAAM,CAAC,mBAAmB,EAAE,KAAK,CAAC;QACtD,OAAO,EAAE,SAAS,CAAC,MAAM,CAAC,kBAAkB,EAAE,SAAS,CAAC;QACxD,SAAS,EAAE,SAAS,CAAC,MAAM,CAAC,oBAAoB,EAAE,oBAAoB,CAAC;KACxE,CAAC;AACJ,CAAC,CAAC;AAEF;;;GAGG;AACU,QAAA,cAAc,GAAG,IAAA,mBAAU,EAAC,UAAU,EAAE,GAAyB,EAAE;IAC9E,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,aAAa,CAAC;IAChE,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,YAAY,CAAC;IAC9D,MAAM,QAAQ,GAAG,oBAAoB,EAAE,CAAC;IAExC,MAAM,MAAM,GAAyB;QACnC,IAAI,EAAE,QAAQ,CAAC,IAAI;QACnB,IAAI,EAAE,QAAQ,CAAC,IAAI;QACnB,IAAI,EAAE,QAAQ,CAAC,IAAI;QACnB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;QAC3B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;QAC3B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;QAC3B,MAAM,EAAE,QAAQ,CAAC,MAAM;QAEvB,GAAG,EAAE,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;YAC1B,kBAAkB,EAAE,QAAQ,CAAC,GAAG,CAAC,kBAAkB;YACnD,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC;YAC/C,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACrD,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;SACnD,CAAC,CAAC,CAAC,KAAK;QAET,QAAQ,EAAE,QAAQ,CAAC,QAAQ;QAC3B,UAAU,EAAE,QAAQ,CAAC,UAAU;QAC/B,WAAW,EAAE,QAAQ,CAAC,WAAW;QAEjC,KAAK,EAAE;YACL,GAAG,EAAE,QAAQ,CAAC,IAAI,CAAC,GAAG;YACtB,GAAG,EAAE,QAAQ,CAAC,IAAI,CAAC,GAAG;YACtB,oBAAoB,EAAE,QAAQ,CAAC,IAAI,CAAC,oBAAoB;YACxD,mBAAmB,EAAE,QAAQ,CAAC,IAAI,CAAC,mBAAmB;YACtD,oBAAoB,EAAE,QAAQ,CAAC,IAAI,CAAC,oBAAoB;YACxD,iBAAiB,EAAE,QAAQ,CAAC,IAAI,CAAC,iBAAiB;YAClD,kBAAkB,EAAE,QAAQ,CAAC,IAAI,CAAC,kBAAkB;YACpD,yBAAyB,EAAE,QAAQ,CAAC,IAAI,CAAC,yBAAyB;YAClE,oBAAoB,EAAE,QAAQ,CAAC,IAAI,CAAC,oBAAoB;YACxD,iBAAiB,EAAE,QAAQ,CAAC,OAAO,CAAC,qBAAqB;YACzD,aAAa,EAAE,QAAQ,CAAC,OAAO,CAAC,qBAAqB;SACtD;QAED,WAAW,EAAE,QAAQ,CAAC,WAAW;QACjC,UAAU,EAAE,QAAQ,CAAC,UAAU;QAC/B,aAAa,EAAE,QAAQ,CAAC,SAAS,CAAC,GAAG;QACrC,mBAAmB,EAAE,QAAQ,CAAC,SAAS,CAAC,SAAS;QACjD,yBAAyB,EAAE,QAAQ,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK;QAEtF,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACjC,CAAC,aAAa,CAAC,CAAC;gBACd,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;gBACxF,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAC/C,CAAC,CAAC,CAAC,KAAK;QAEX,MAAM,EAAE,kBAAkB;QAC1B,qBAAqB,EAAE,QAAQ,CAAC,OAAO,CAAC,kBAAkB;KAC3D,CAAC;IAEF,2CAA2C;IAC3C,IAAI,SAAS,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,EAAE,CAAC;QAC7C,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAEnD,MAAc,CAAC,KAAK,GAAG;YACtB,IAAI,EAAE,OAAO;YACb,OAAO,EAAE;gBACP,IAAI,EAAE,SAAS,CAAC,MAAM,CAAC,YAAY,EAAE,WAAW,CAAC;gBACjD,IAAI,EAAE,SAAS,CAAC,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC;gBAC1C,GAAG,CAAC,aAAa,IAAI,EAAE,QAAQ,EAAE,aAAa,EAAE,CAAC;gBACjD,EAAE,EAAE,SAAS,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,CAAC;aAC1C;YACD,QAAQ,EAAE,SAAS,CAAC,MAAM,CAAC,yBAAyB,EAAE,KAAK,CAAC;SAC7D,CAAC;IACJ,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC,CAAC;AAEH;;GAEG;AAEI,IAAM,4BAA4B,GAAlC,MAAM,4BAA4B;IAGvC,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QACvD,IAAI,CAAC,MAAM,GAAG,oBAAoB,EAAE,CAAC;IACvC,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IAC1B,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IAC1B,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IAC1B,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;IAC9B,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;IAC9B,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;IAC9B,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAC5B,CAAC;IAED,IAAI,GAAG;QACL,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;IACzB,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IAC1B,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;IAC/B,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;IAC7B,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;IACjC,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;IAChC,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;IAC9B,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;IAC7B,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAuB,UAAU,CAAE,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC;QACvE,OAAO,GAAG,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,QAAQ,EAAE,CAAC;IACzE,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;IACvE,CAAC;CACF,CAAA;AAjHY,oEAA4B;uCAA5B,4BAA4B;IADxC,IAAA,mBAAU,GAAE;yDAIiC,sBAAa,oBAAb,sBAAa;GAH9C,4BAA4B,CAiHxC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\database.config.ts"], "sourcesContent": ["import { registerAs } from '@nestjs/config';\r\nimport { TypeOrmModuleOptions } from '@nestjs/typeorm';\r\nimport { Injectable } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\n\r\n/**\r\n * Supported database types for TypeORM (matching TypeORM's actual supported types)\r\n */\r\nexport type DatabaseType = 'postgres' | 'mysql' | 'mariadb';\r\n\r\n/**\r\n * Database connection pool configuration\r\n */\r\nexport interface DatabasePoolConfig {\r\n  min: number;\r\n  max: number;\r\n  acquireTimeoutMillis: number;\r\n  createTimeoutMillis: number;\r\n  destroyTimeoutMillis: number;\r\n  idleTimeoutMillis: number;\r\n  reapIntervalMillis: number;\r\n  createRetryIntervalMillis: number;\r\n  propagateCreateError: boolean;\r\n}\r\n\r\n/**\r\n * Database SSL configuration\r\n */\r\nexport interface DatabaseSSLConfig {\r\n  enabled: boolean;\r\n  rejectUnauthorized: boolean;\r\n  ca?: string;\r\n  cert?: string;\r\n  key?: string;\r\n}\r\n\r\n/**\r\n * Database migration configuration\r\n */\r\nexport interface DatabaseMigrationConfig {\r\n  run: boolean;\r\n  tableName: string;\r\n  directory: string;\r\n  transactionPerMigration: boolean;\r\n  disableTransactions: boolean;\r\n}\r\n\r\n/**\r\n * Database logging configuration\r\n */\r\nexport interface DatabaseLoggingConfig {\r\n  enabled: boolean;\r\n  logQueries: boolean;\r\n  logErrors: boolean;\r\n  logSlowQueries: boolean;\r\n  slowQueryThreshold: number;\r\n  logOnlyFailedQueries: boolean;\r\n  maxQueryExecutionTime: number;\r\n}\r\n\r\n/**\r\n * Cache configuration type\r\n */\r\nexport interface CacheConfig {\r\n  type: 'redis';\r\n  options: {\r\n    host: string;\r\n    port: number;\r\n    password?: string | undefined;\r\n    db: number;\r\n  };\r\n  duration: number;\r\n}\r\n\r\n/**\r\n * Complete database configuration interface\r\n */\r\nexport interface DatabaseConfig {\r\n  type: DatabaseType;\r\n  host: string;\r\n  port: number;\r\n  username: string;\r\n  password: string;\r\n  database: string;\r\n  schema: string;\r\n  ssl: DatabaseSSLConfig;\r\n  pool: DatabasePoolConfig;\r\n  migration: DatabaseMigrationConfig;\r\n  logging: DatabaseLoggingConfig;\r\n  cache?: CacheConfig;\r\n  synchronize: boolean;\r\n  dropSchema: boolean;\r\n  entities: string[];\r\n  migrations: string[];\r\n  subscribers: string[];\r\n  timezone: string;\r\n  charset: string;\r\n  collation: string;\r\n}\r\n\r\n/**\r\n * Environment variable utilities with strict type safety\r\n */\r\nclass EnvParser {\r\n  static string(key: string, defaultValue: string): string {\r\n    return process.env[key] ?? defaultValue;\r\n  }\r\n\r\n  static number(key: string, defaultValue: number): number {\r\n    const value = process.env[key];\r\n    const parsed = value ? Number(value) : NaN;\r\n    return Number.isInteger(parsed) ? parsed : defaultValue;\r\n  }\r\n\r\n  static boolean(key: string, defaultValue = false): boolean {\r\n    const value = process.env[key];\r\n    return value === 'true' ? true : value === 'false' ? false : defaultValue;\r\n  }\r\n\r\n  static databaseType(key: string, defaultValue: DatabaseType): DatabaseType {\r\n    const value = process.env[key] as DatabaseType;\r\n    const validTypes = new Set<DatabaseType>(['postgres', 'mysql', 'mariadb']);\r\n    return validTypes.has(value) ? value : defaultValue;\r\n  }\r\n\r\n  static optionalString(key: string): string | undefined {\r\n    return process.env[key] || undefined;\r\n  }\r\n}\r\n\r\n/**\r\n * Create database pool configuration\r\n */\r\nconst createPoolConfig = (): DatabasePoolConfig => ({\r\n  min: EnvParser.number('DATABASE_POOL_MIN', 2),\r\n  max: EnvParser.number('DATABASE_POOL_MAX', 100),\r\n  acquireTimeoutMillis: EnvParser.number('DATABASE_POOL_ACQUIRE_TIMEOUT', 60000),\r\n  createTimeoutMillis: EnvParser.number('DATABASE_POOL_CREATE_TIMEOUT', 30000),\r\n  destroyTimeoutMillis: EnvParser.number('DATABASE_POOL_DESTROY_TIMEOUT', 5000),\r\n  idleTimeoutMillis: EnvParser.number('DATABASE_POOL_IDLE_TIMEOUT', 30000),\r\n  reapIntervalMillis: EnvParser.number('DATABASE_POOL_REAP_INTERVAL', 1000),\r\n  createRetryIntervalMillis: EnvParser.number('DATABASE_POOL_CREATE_RETRY_INTERVAL', 200),\r\n  propagateCreateError: EnvParser.boolean('DATABASE_POOL_PROPAGATE_CREATE_ERROR', false),\r\n});\r\n\r\n/**\r\n * Create database SSL configuration\r\n */\r\nconst createSSLConfig = (): DatabaseSSLConfig => ({\r\n  enabled: EnvParser.boolean('DATABASE_SSL', false),\r\n  rejectUnauthorized: EnvParser.boolean('DATABASE_SSL_REJECT_UNAUTHORIZED', false),\r\n  ca: EnvParser.optionalString('DATABASE_SSL_CA'),\r\n  cert: EnvParser.optionalString('DATABASE_SSL_CERT'),\r\n  key: EnvParser.optionalString('DATABASE_SSL_KEY'),\r\n});\r\n\r\n/**\r\n * Create database migration configuration\r\n */\r\nconst createMigrationConfig = (): DatabaseMigrationConfig => ({\r\n  run: EnvParser.boolean('DATABASE_MIGRATIONS_RUN', true),\r\n  tableName: EnvParser.string('DATABASE_MIGRATIONS_TABLE', 'migrations'),\r\n  directory: EnvParser.string('DATABASE_MIGRATIONS_DIR', 'src/database/migrations'),\r\n  transactionPerMigration: EnvParser.boolean('DATABASE_MIGRATIONS_TRANSACTION_PER_MIGRATION', true),\r\n  disableTransactions: EnvParser.boolean('DATABASE_MIGRATIONS_DISABLE_TRANSACTIONS', false),\r\n});\r\n\r\n/**\r\n * Create database logging configuration\r\n */\r\nconst createLoggingConfig = (): DatabaseLoggingConfig => {\r\n  const isDevelopment = process.env['NODE_ENV'] === 'development';\r\n  \r\n  return {\r\n    enabled: EnvParser.boolean('DATABASE_LOGGING_ENABLED', isDevelopment),\r\n    logQueries: EnvParser.boolean('DATABASE_LOG_QUERIES', isDevelopment),\r\n    logErrors: EnvParser.boolean('DATABASE_LOG_ERRORS', true),\r\n    logSlowQueries: EnvParser.boolean('DATABASE_LOG_SLOW_QUERIES', false),\r\n    slowQueryThreshold: EnvParser.number('DATABASE_SLOW_QUERY_THRESHOLD', 1000),\r\n    logOnlyFailedQueries: EnvParser.boolean('DATABASE_LOG_ONLY_FAILED_QUERIES', false),\r\n    maxQueryExecutionTime: EnvParser.number('DATABASE_MAX_QUERY_EXECUTION_TIME', 30000),\r\n  };\r\n};\r\n\r\n/**\r\n * Create complete database configuration\r\n */\r\nconst createDatabaseConfig = (): DatabaseConfig => {\r\n  const isDevelopment = process.env['NODE_ENV'] === 'development';\r\n  const isTest = process.env['NODE_ENV'] === 'test';\r\n  \r\n  return {\r\n    type: EnvParser.databaseType('DATABASE_TYPE', 'postgres'),\r\n    host: EnvParser.string('DATABASE_HOST', 'localhost'),\r\n    port: EnvParser.number('DATABASE_PORT', 5432),\r\n    username: EnvParser.string('DATABASE_USERNAME', 'sentinel_user'),\r\n    password: EnvParser.string('DATABASE_PASSWORD', 'sentinel_password'),\r\n    database: EnvParser.string('DATABASE_NAME', isTest ? 'sentinel_test' : 'sentinel_db'),\r\n    schema: EnvParser.string('DATABASE_SCHEMA', 'public'),\r\n    ssl: createSSLConfig(),\r\n    pool: createPoolConfig(),\r\n    migration: createMigrationConfig(),\r\n    logging: createLoggingConfig(),\r\n    synchronize: EnvParser.boolean('DATABASE_SYNCHRONIZE', isDevelopment),\r\n    dropSchema: EnvParser.boolean('DATABASE_DROP_SCHEMA', false),\r\n    entities: [\r\n      `${__dirname}/../../**/*.entity{.ts,.js}`,\r\n      `${__dirname}/../../modules/**/domain/entities/**/*.entity{.ts,.js}`,\r\n    ],\r\n    migrations: [`${__dirname}/../../database/migrations/*{.ts,.js}`],\r\n    subscribers: [`${__dirname}/../../database/subscribers/*{.ts,.js}`],\r\n    timezone: EnvParser.string('DATABASE_TIMEZONE', 'UTC'),\r\n    charset: EnvParser.string('DATABASE_CHARSET', 'utf8mb4'),\r\n    collation: EnvParser.string('DATABASE_COLLATION', 'utf8mb4_unicode_ci'),\r\n  };\r\n};\r\n\r\n/**\r\n * Database configuration factory for TypeORM\r\n * Provides environment-specific database connection settings with strict type safety\r\n */\r\nexport const databaseConfig = registerAs('database', (): TypeOrmModuleOptions => {\r\n  const isDevelopment = process.env['NODE_ENV'] === 'development';\r\n  const isProduction = process.env['NODE_ENV'] === 'production';\r\n  const dbConfig = createDatabaseConfig();\r\n\r\n  const config: TypeOrmModuleOptions = {\r\n    type: dbConfig.type,\r\n    host: dbConfig.host,\r\n    port: dbConfig.port,\r\n    username: dbConfig.username,\r\n    password: dbConfig.password,\r\n    database: dbConfig.database,\r\n    schema: dbConfig.schema,\r\n\r\n    ssl: dbConfig.ssl.enabled ? {\r\n      rejectUnauthorized: dbConfig.ssl.rejectUnauthorized,\r\n      ...(dbConfig.ssl.ca && { ca: dbConfig.ssl.ca }),\r\n      ...(dbConfig.ssl.cert && { cert: dbConfig.ssl.cert }),\r\n      ...(dbConfig.ssl.key && { key: dbConfig.ssl.key }),\r\n    } : false,\r\n\r\n    entities: dbConfig.entities,\r\n    migrations: dbConfig.migrations,\r\n    subscribers: dbConfig.subscribers,\r\n\r\n    extra: {\r\n      min: dbConfig.pool.min,\r\n      max: dbConfig.pool.max,\r\n      acquireTimeoutMillis: dbConfig.pool.acquireTimeoutMillis,\r\n      createTimeoutMillis: dbConfig.pool.createTimeoutMillis,\r\n      destroyTimeoutMillis: dbConfig.pool.destroyTimeoutMillis,\r\n      idleTimeoutMillis: dbConfig.pool.idleTimeoutMillis,\r\n      reapIntervalMillis: dbConfig.pool.reapIntervalMillis,\r\n      createRetryIntervalMillis: dbConfig.pool.createRetryIntervalMillis,\r\n      propagateCreateError: dbConfig.pool.propagateCreateError,\r\n      statement_timeout: dbConfig.logging.maxQueryExecutionTime,\r\n      query_timeout: dbConfig.logging.maxQueryExecutionTime,\r\n    },\r\n\r\n    synchronize: dbConfig.synchronize,\r\n    dropSchema: dbConfig.dropSchema,\r\n    migrationsRun: dbConfig.migration.run,\r\n    migrationsTableName: dbConfig.migration.tableName,\r\n    migrationsTransactionMode: dbConfig.migration.transactionPerMigration ? 'each' : 'all',\r\n\r\n    logging: dbConfig.logging.enabled ? \r\n      (isDevelopment ? \r\n        (dbConfig.logging.logQueries ? ['query', 'error', 'warn', 'info'] : ['error', 'warn']) :\r\n        (isProduction ? ['error'] : ['error', 'warn'])\r\n      ) : false,\r\n    \r\n    logger: 'advanced-console',\r\n    maxQueryExecutionTime: dbConfig.logging.slowQueryThreshold,\r\n  };\r\n\r\n  // Add Redis cache configuration if enabled\r\n  if (EnvParser.boolean('REDIS_ENABLED', true)) {\r\n    const redisPassword = process.env['REDIS_PASSWORD'];\r\n    \r\n    (config as any).cache = {\r\n      type: 'redis',\r\n      options: {\r\n        host: EnvParser.string('REDIS_HOST', 'localhost'),\r\n        port: EnvParser.number('REDIS_PORT', 6379),\r\n        ...(redisPassword && { password: redisPassword }),\r\n        db: EnvParser.number('REDIS_CACHE_DB', 1),\r\n      },\r\n      duration: EnvParser.number('DATABASE_CACHE_DURATION', 30000),\r\n    };\r\n  }\r\n\r\n  return config;\r\n});\r\n\r\n/**\r\n * Database configuration service for dependency injection\r\n */\r\n@Injectable()\r\nexport class DatabaseConfigurationService {\r\n  private readonly config: DatabaseConfig;\r\n\r\n  constructor(private readonly configService: ConfigService) {\r\n    this.config = createDatabaseConfig();\r\n  }\r\n\r\n  get type(): DatabaseType {\r\n    return this.config.type;\r\n  }\r\n\r\n  get host(): string {\r\n    return this.config.host;\r\n  }\r\n\r\n  get port(): number {\r\n    return this.config.port;\r\n  }\r\n\r\n  get username(): string {\r\n    return this.config.username;\r\n  }\r\n\r\n  get password(): string {\r\n    return this.config.password;\r\n  }\r\n\r\n  get database(): string {\r\n    return this.config.database;\r\n  }\r\n\r\n  get schema(): string {\r\n    return this.config.schema;\r\n  }\r\n\r\n  get ssl(): DatabaseSSLConfig {\r\n    return this.config.ssl;\r\n  }\r\n\r\n  get pool(): DatabasePoolConfig {\r\n    return this.config.pool;\r\n  }\r\n\r\n  get migration(): DatabaseMigrationConfig {\r\n    return this.config.migration;\r\n  }\r\n\r\n  get logging(): DatabaseLoggingConfig {\r\n    return this.config.logging;\r\n  }\r\n\r\n  get synchronize(): boolean {\r\n    return this.config.synchronize;\r\n  }\r\n\r\n  get dropSchema(): boolean {\r\n    return this.config.dropSchema;\r\n  }\r\n\r\n  get timezone(): string {\r\n    return this.config.timezone;\r\n  }\r\n\r\n  get charset(): string {\r\n    return this.config.charset;\r\n  }\r\n\r\n  get collation(): string {\r\n    return this.config.collation;\r\n  }\r\n\r\n  /**\r\n   * Get complete database configuration\r\n   */\r\n  getAll(): DatabaseConfig {\r\n    return this.config;\r\n  }\r\n\r\n  /**\r\n   * Get TypeORM configuration options\r\n   */\r\n  getTypeOrmOptions(): TypeOrmModuleOptions {\r\n    return this.configService.get<TypeOrmModuleOptions>('database')!;\r\n  }\r\n\r\n  /**\r\n   * Get connection string for the database\r\n   */\r\n  getConnectionString(): string {\r\n    const { type, username, password, host, port, database } = this.config;\r\n    return `${type}://${username}:${password}@${host}:${port}/${database}`;\r\n  }\r\n\r\n  /**\r\n   * Check if SSL is enabled\r\n   */\r\n  isSSLEnabled(): boolean {\r\n    return this.config.ssl.enabled;\r\n  }\r\n\r\n  /**\r\n   * Check if migrations should run automatically\r\n   */\r\n  shouldRunMigrations(): boolean {\r\n    return this.config.migration.run;\r\n  }\r\n\r\n  /**\r\n   * Check if query logging is enabled\r\n   */\r\n  isQueryLoggingEnabled(): boolean {\r\n    return this.config.logging.enabled && this.config.logging.logQueries;\r\n  }\r\n}"], "version": 3}