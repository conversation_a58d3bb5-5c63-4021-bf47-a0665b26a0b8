{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\correlated-event.specification.ts", "mappings": ";;;AAAA,6DAA8E;AAa9E;;;;;GAKG;AACH,MAAsB,4BAA6B,SAAQ,iCAAkC;IAC3F;;OAEG;IACO,cAAc,CAAC,KAAsB,EAAE,KAAkB;QACjE,OAAO,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACO,kBAAkB,CAAC,KAAsB,EAAE,UAA2B;QAC9E,OAAO,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACO,gBAAgB,CAAC,KAAsB,EAAE,QAAuB;QACxE,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACO,0BAA0B,CAAC,KAAsB,EAAE,QAAiC;QAC5F,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACO,2BAA2B,CAAC,KAAsB,EAAE,QAA6B;QACzF,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACO,yBAAyB,CAAC,KAAsB,EAAE,MAAyB;QACnF,OAAO,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACO,SAAS,CAAC,KAAsB,EAAE,IAAc;QACxD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACO,UAAU,CAAC,KAAsB,EAAE,IAAc;QACzD,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACO,oCAAoC,CAAC,KAAsB,EAAE,QAAiB,EAAE,QAAiB;QACzG,MAAM,YAAY,GAAG,KAAK,CAAC,uBAAuB,CAAC;QAEnD,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YAC/B,OAAO,QAAQ,KAAK,SAAS,CAAC,CAAC,oDAAoD;QACrF,CAAC;QAED,IAAI,QAAQ,KAAK,SAAS,IAAI,YAAY,GAAG,QAAQ,EAAE,CAAC;YACtD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,QAAQ,KAAK,SAAS,IAAI,YAAY,GAAG,QAAQ,EAAE,CAAC;YACtD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACO,sBAAsB,CAAC,KAAsB,EAAE,QAAiB,EAAE,QAAiB;QAC3F,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;QAElC,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;YAC5B,OAAO,QAAQ,KAAK,SAAS,CAAC,CAAC,oDAAoD;QACrF,CAAC;QAED,IAAI,QAAQ,KAAK,SAAS,IAAI,SAAS,GAAG,QAAQ,EAAE,CAAC;YACnD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,QAAQ,KAAK,SAAS,IAAI,SAAS,GAAG,QAAQ,EAAE,CAAC;YACnD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACO,cAAc,CAAC,KAAsB,EAAE,MAAc;QAC7D,OAAO,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACO,cAAc,CAAC,KAAsB,EAAE,MAAc;QAC7D,OAAO,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACO,cAAc,CAAC,KAAsB,EAAE,SAA+B;QAC9E,OAAO,KAAK,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACO,qBAAqB,CAAC,KAAsB,EAAE,OAAe;QACrE,OAAO,KAAK,CAAC,mBAAmB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IACrD,CAAC;CACF;AA9HD,oEA8HC;AAED;;;;GAIG;AACH,MAAa,iCAAkC,SAAQ,4BAA4B;IACjF,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,sBAAsB,EAAE,CAAC;IACxC,CAAC;IAED,cAAc;QACZ,OAAO,4CAA4C,CAAC;IACtD,CAAC;CACF;AARD,8EAQC;AAED;;;;GAIG;AACH,MAAa,8BAA+B,SAAQ,4BAA4B;IAC9E,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,mBAAmB,EAAE,CAAC;IACrC,CAAC;IAED,cAAc;QACZ,OAAO,yCAAyC,CAAC;IACnD,CAAC;CACF;AARD,wEAQC;AAED;;;;GAIG;AACH,MAAa,kCAAmC,SAAQ,4BAA4B;IAClF,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,uBAAuB,EAAE,CAAC;IACzC,CAAC;IAED,cAAc;QACZ,OAAO,gDAAgD,CAAC;IAC1D,CAAC;CACF;AARD,gFAQC;AAED;;;;GAIG;AACH,MAAa,+BAAgC,SAAQ,4BAA4B;IAC/E,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,oBAAoB,EAAE,CAAC;IACtC,CAAC;IAED,cAAc;QACZ,OAAO,kDAAkD,CAAC;IAC5D,CAAC;CACF;AARD,0EAQC;AAED;;;;GAIG;AACH,MAAa,mCAAoC,SAAQ,4BAA4B;IACnF,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,yBAAyB,EAAE,CAAC;IAC3C,CAAC;IAED,cAAc;QACZ,OAAO,uDAAuD,CAAC;IACjE,CAAC;CACF;AARD,kFAQC;AAED;;;;GAIG;AACH,MAAa,gCAAiC,SAAQ,4BAA4B;IAChF,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,mBAAmB,EAAE,CAAC;IACrC,CAAC;IAED,cAAc;QACZ,OAAO,wCAAwC,CAAC;IAClD,CAAC;CACF;AARD,4EAQC;AAED;;;;GAIG;AACH,MAAa,iCAAkC,SAAQ,4BAA4B;IACjF,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,oBAAoB,CAAC;IACpC,CAAC;IAED,cAAc;QACZ,OAAO,yCAAyC,CAAC;IACnD,CAAC;CACF;AARD,8EAQC;AAED;;;;GAIG;AACH,MAAa,8BAA+B,SAAQ,4BAA4B;IAC9E,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,mBAAmB,EAAE,CAAC;IACrC,CAAC;IAED,cAAc;QACZ,OAAO,qDAAqD,CAAC;IAC/D,CAAC;CACF;AARD,wEAQC;AAED;;;;GAIG;AACH,MAAa,sCAAuC,SAAQ,4BAA4B;IACtF,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,2BAA2B,EAAE,CAAC;IAC7C,CAAC;IAED,cAAc;QACZ,OAAO,kDAAkD,CAAC;IAC5D,CAAC;CACF;AARD,wFAQC;AAED;;;;GAIG;AACH,MAAa,2BAA4B,SAAQ,4BAA4B;IAC3E,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,cAAc,EAAE,CAAC;IAChC,CAAC;IAED,cAAc;QACZ,OAAO,sCAAsC,CAAC;IAChD,CAAC;CACF;AARD,kEAQC;AAED;;;;GAIG;AACH,MAAa,mCAAoC,SAAQ,4BAA4B;IACnF,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,sBAAsB,EAAE,CAAC;IACxC,CAAC;IAED,cAAc;QACZ,OAAO,gDAAgD,CAAC;IAC1D,CAAC;CACF;AARD,kFAQC;AAED;;;;GAIG;AACH,MAAa,kCAAmC,SAAQ,4BAA4B;IAClF,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,qBAAqB,EAAE,CAAC;IACvC,CAAC;IAED,cAAc;QACZ,OAAO,+CAA+C,CAAC;IACzD,CAAC;CACF;AARD,gFAQC;AAED;;;;GAIG;AACH,MAAa,qCAAsC,SAAQ,4BAA4B;IACrF,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,wBAAwB,EAAE,CAAC;IAC1C,CAAC;IAED,cAAc;QACZ,OAAO,kDAAkD,CAAC;IAC5D,CAAC;CACF;AARD,sFAQC;AAED;;;;GAIG;AACH,MAAa,8BAA+B,SAAQ,4BAA4B;IAC9E,YAA6B,QAA6B;QACxD,KAAK,EAAE,CAAC;QADmB,aAAQ,GAAR,QAAQ,CAAqB;IAE1D,CAAC;IAED,aAAa,CAAC,KAAsB;QAClC,OAAO,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAChE,CAAC;IAED,cAAc;QACZ,OAAO,sCAAsC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAC1E,CAAC;CACF;AAZD,wEAYC;AAED;;;;GAIG;AACH,MAAa,4BAA6B,SAAQ,4BAA4B;IAC5E,YAA6B,MAAyB;QACpD,KAAK,EAAE,CAAC;QADmB,WAAM,GAAN,MAAM,CAAmB;IAEtD,CAAC;IAED,aAAa,CAAC,KAAsB;QAClC,OAAO,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5D,CAAC;IAED,cAAc;QACZ,OAAO,gDAAgD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAClF,CAAC;CACF;AAZD,oEAYC;AAED;;;;GAIG;AACH,MAAa,yCAA0C,SAAQ,4BAA4B;IACzF,YACmB,QAAiB,EACjB,QAAiB;QAElC,KAAK,EAAE,CAAC;QAHS,aAAQ,GAAR,QAAQ,CAAS;QACjB,aAAQ,GAAR,QAAQ,CAAS;IAGpC,CAAC;IAED,aAAa,CAAC,KAAsB;QAClC,OAAO,IAAI,CAAC,oCAAoC,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACxF,CAAC;IAED,cAAc;QACZ,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC/D,OAAO,6CAA6C,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC3F,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,8CAA8C,IAAI,CAAC,QAAQ,EAAE,CAAC;QACvE,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,6CAA6C,IAAI,CAAC,QAAQ,EAAE,CAAC;QACtE,CAAC;QACD,OAAO,wCAAwC,CAAC;IAClD,CAAC;CACF;AAtBD,8FAsBC;AAED;;;;GAIG;AACH,MAAa,wBAAyB,SAAQ,4BAA4B;IACxE,YAA6B,MAAc;QACzC,KAAK,EAAE,CAAC;QADmB,WAAM,GAAN,MAAM,CAAQ;IAE3C,CAAC;IAED,aAAa,CAAC,KAAsB;QAClC,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IACjD,CAAC;IAED,cAAc;QACZ,OAAO,sCAAsC,IAAI,CAAC,MAAM,EAAE,CAAC;IAC7D,CAAC;CACF;AAZD,4DAYC;AAED;;;;GAIG;AACH,MAAa,0BAA2B,SAAQ,4BAA4B;IAC1E,YAA6B,eAA+B;QAC1D,KAAK,EAAE,CAAC;QADmB,oBAAe,GAAf,eAAe,CAAgB;IAE5D,CAAC;IAED,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAC5D,CAAC;IAED,cAAc;QACZ,OAAO,+CAA+C,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,EAAE,CAAC;IAC1F,CAAC;CACF;AAZD,gEAYC;AAED;;;;GAIG;AACH,MAAa,0BAA2B,SAAQ,4BAA4B;IAC1E,YAA6B,aAAqB;QAChD,KAAK,EAAE,CAAC;QADmB,kBAAa,GAAb,aAAa,CAAQ;IAElD,CAAC;IAED,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC;IACpD,CAAC;IAED,cAAc;QACZ,OAAO,wCAAwC,IAAI,CAAC,aAAa,EAAE,CAAC;IACtE,CAAC;CACF;AAZD,gEAYC;AAED;;;;GAIG;AACH,MAAa,6BAA8B,SAAQ,4BAA4B;IAC7E,YAA6B,WAAmB,CAAC;QAC/C,KAAK,EAAE,CAAC;QADmB,aAAQ,GAAR,QAAQ,CAAY;IAEjD,CAAC;IAED,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,eAAe,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC;IACvD,CAAC;IAED,cAAc;QACZ,OAAO,iCAAiC,IAAI,CAAC,QAAQ,iBAAiB,CAAC;IACzE,CAAC;CACF;AAZD,sEAYC;AAED;;;;GAIG;AACH,MAAa,2BAA4B,SAAQ,4BAA4B;IAC3E,YAA6B,WAAmB,CAAC;QAC/C,KAAK,EAAE,CAAC;QADmB,aAAQ,GAAR,QAAQ,CAAY;IAEjD,CAAC;IAED,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,aAAa,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC;IACrD,CAAC;IAED,cAAc;QACZ,OAAO,iCAAiC,IAAI,CAAC,QAAQ,eAAe,CAAC;IACvE,CAAC;CACF;AAZD,kEAYC;AAED;;;;GAIG;AACH,MAAa,+BAAgC,SAAQ,4BAA4B;IAC/E,YAA6B,QAAkB;QAC7C,KAAK,EAAE,CAAC;QADmB,aAAQ,GAAR,QAAQ,CAAU;IAE/C,CAAC;IAED,aAAa,CAAC,KAAsB;QAClC,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;IACnF,CAAC;IAED,cAAc;QACZ,OAAO,kCAAkC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IACtE,CAAC;CACF;AAZD,0EAYC;AAED;;;;GAIG;AACH,MAAa,sBAAuB,SAAQ,4BAA4B;IACtE,YAA6B,UAAkC;QAC7D,KAAK,EAAE,CAAC;QADmB,eAAU,GAAV,UAAU,CAAwB;IAE/D,CAAC;IAED,aAAa,CAAC,KAAsB;QAClC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;IACxE,CAAC;IAED,cAAc;QACZ,OAAO,0CAA0C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAChF,CAAC;CACF;AAZD,wDAYC;AAED;;;;GAIG;AACH,MAAa,gCAAiC,SAAQ,4BAA4B;IAChF,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,iCAAiC,EAAE,CAAC;IACnD,CAAC;IAED,cAAc;QACZ,OAAO,4DAA4D,CAAC;IACtE,CAAC;CACF;AARD,4EAQC;AAED;;;;GAIG;AACH,MAAa,0BAA2B,SAAQ,4BAA4B;IAC1E,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,UAAU,KAAK,SAAS,CAAC;IACxC,CAAC;IAED,cAAc;QACZ,OAAO,6CAA6C,CAAC;IACvD,CAAC;CACF;AARD,gEAQC;AAED;;;;GAIG;AACH,MAAa,0BAA2B,SAAQ,4BAA4B;IAC1E,aAAa,CAAC,KAAsB;QAClC,OAAO,KAAK,CAAC,oBAAoB,IAAI,KAAK,CAAC,UAAU,KAAK,SAAS,CAAC;IACtE,CAAC;IAED,cAAc;QACZ,OAAO,2CAA2C,CAAC;IACrD,CAAC;CACF;AARD,gEAQC;AAED;;;;GAIG;AACH,MAAa,qCAAsC,SAAQ,4BAA4B;IACrF,YACmB,aAAsB,EACtB,aAAsB;QAEvC,KAAK,EAAE,CAAC;QAHS,kBAAa,GAAb,aAAa,CAAS;QACtB,kBAAa,GAAb,aAAa,CAAS;IAGzC,CAAC;IAED,aAAa,CAAC,KAAsB;QAClC,MAAM,QAAQ,GAAG,KAAK,CAAC,sBAAsB,EAAE,CAAC;QAEhD,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;YACtB,OAAO,KAAK,CAAC,CAAC,wBAAwB;QACxC,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,IAAI,QAAQ,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YACtE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,IAAI,QAAQ,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YACtE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,cAAc;QACZ,MAAM,cAAc,GAAG,CAAC,EAAU,EAAE,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC;QAEjD,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YACzE,OAAO,wCAAwC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;QAChI,CAAC;aAAM,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YAC5C,OAAO,yCAAyC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;QACvF,CAAC;aAAM,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YAC5C,OAAO,wCAAwC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;QACtF,CAAC;QACD,OAAO,mCAAmC,CAAC;IAC7C,CAAC;CACF;AAtCD,sFAsCC;AAED;;;;GAIG;AACH,MAAa,wCAAyC,SAAQ,4BAA4B;IACxF,YACmB,aAAsB,EACtB,aAAsB;QAEvC,KAAK,EAAE,CAAC;QAHS,kBAAa,GAAb,aAAa,CAAS;QACtB,kBAAa,GAAb,aAAa,CAAS;IAGzC,CAAC;IAED,aAAa,CAAC,KAAsB;QAClC,MAAM,aAAa,GAAG,KAAK,CAAC,yBAAyB,EAAE,CAAC;QAExD,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC,aAAa,KAAK,SAAS,CAAC,CAAC,oDAAoD;QAC/F,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,IAAI,aAAa,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YAC3E,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,IAAI,aAAa,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YAC3E,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,cAAc;QACZ,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YACzE,OAAO,wDAAwD,IAAI,CAAC,aAAa,QAAQ,IAAI,CAAC,aAAa,EAAE,CAAC;QAChH,CAAC;aAAM,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YAC5C,OAAO,yDAAyD,IAAI,CAAC,aAAa,EAAE,CAAC;QACvF,CAAC;aAAM,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YAC5C,OAAO,wDAAwD,IAAI,CAAC,aAAa,EAAE,CAAC;QACtF,CAAC;QACD,OAAO,mDAAmD,CAAC;IAC7D,CAAC;CACF;AApCD,4FAoCC;AAED;;;;GAIG;AACH,MAAa,mCAAmC;IAAhD;QACU,mBAAc,GAAmC,EAAE,CAAC;IAkR9D,CAAC;IAhRC;;OAEG;IACH,oBAAoB;QAClB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,iCAAiC,EAAE,CAAC,CAAC;QAClE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,8BAA8B,EAAE,CAAC,CAAC;QAC/D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,kCAAkC,EAAE,CAAC,CAAC;QACnE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,+BAA+B,EAAE,CAAC,CAAC;QAChE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,mCAAmC,EAAE,CAAC,CAAC;QACpE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,gCAAgC,EAAE,CAAC,CAAC;QACjE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,iCAAiC,EAAE,CAAC,CAAC;QAClE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,8BAA8B,EAAE,CAAC,CAAC;QAC/D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,yBAAyB;QACvB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,sCAAsC,EAAE,CAAC,CAAC;QACvE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,2BAA2B,EAAE,CAAC,CAAC;QAC5D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,mCAAmC,EAAE,CAAC,CAAC;QACpE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,kCAAkC,EAAE,CAAC,CAAC;QACnE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,qCAAqC,EAAE,CAAC,CAAC;QACtE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAC,GAAG,QAA6B;QACpD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,8BAA8B,CAAC,QAAQ,CAAC,CAAC,CAAC;QACvE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,GAAG,MAAyB;QAC9C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,4BAA4B,CAAC,MAAM,CAAC,CAAC,CAAC;QACnE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,4BAA4B,CAAC,QAAiB,EAAE,QAAiB;QAC/D,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,yCAAyC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC5F,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,MAAc;QAC5B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,wBAAwB,CAAC,MAAM,CAAC,CAAC,CAAC;QAC/D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,eAA+B;QAC/C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,0BAA0B,CAAC,eAAe,CAAC,CAAC,CAAC;QAC1E,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,aAAqB;QACrC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,0BAA0B,CAAC,aAAa,CAAC,CAAC,CAAC;QACxE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,WAAmB,CAAC;QACnC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,6BAA6B,CAAC,QAAQ,CAAC,CAAC,CAAC;QACtE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,WAAmB,CAAC;QACjC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,2BAA2B,CAAC,QAAQ,CAAC,CAAC,CAAC;QACpE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,uBAAuB,CAAC,GAAG,QAAkB;QAC3C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,+BAA+B,CAAC,QAAQ,CAAC,CAAC,CAAC;QACxE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,GAAG,UAAkC;QAClD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,sBAAsB,CAAC,UAAU,CAAC,CAAC,CAAC;QACjE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,gCAAgC,EAAE,CAAC,CAAC;QACjE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,0BAA0B,EAAE,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,aAAa;QACX,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,0BAA0B,EAAE,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,wBAAwB,CAAC,aAAsB,EAAE,aAAsB;QACrE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,qCAAqC,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC,CAAC;QAClG,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,2BAA2B,CAAC,aAAsB,EAAE,aAAsB;QACxE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,wCAAwC,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC,CAAC;QACrG,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC;QAED,4CAA4C;QAC5C,IAAI,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpD,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAiC,CAAC;QAClF,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,WAAW;QACT,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC;QAED,2CAA2C;QAC3C,IAAI,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpD,QAAQ,GAAG,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAiC,CAAC;QACjF,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM;QACX,OAAO,IAAI,mCAAmC,EAAE,CAAC;IACnD,CAAC;CACF;AAnRD,kFAmRC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\correlated-event.specification.ts"], "sourcesContent": ["import { BaseSpecification, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { \r\n  CorrelatedEvent, \r\n  CorrelationStatus, \r\n  CorrelationRuleType, \r\n  CorrelationMatchType \r\n} from '../entities/correlated-event.entity';\r\nimport { EventType } from '../enums/event-type.enum';\r\nimport { EventSeverity } from '../enums/event-severity.enum';\r\nimport { EventStatus } from '../enums/event-status.enum';\r\nimport { EventProcessingStatus } from '../enums/event-processing-status.enum';\r\nimport { ConfidenceLevel } from '../enums/confidence-level.enum';\r\n\r\n/**\r\n * CorrelatedEvent Specification Base Class\r\n * \r\n * Base class for all correlated event-related specifications.\r\n * Provides common functionality for correlated event filtering and validation.\r\n */\r\nexport abstract class CorrelatedEventSpecification extends BaseSpecification<CorrelatedEvent> {\r\n  /**\r\n   * Helper method to check if correlated event matches any of the provided types\r\n   */\r\n  protected matchesAnyType(event: CorrelatedEvent, types: EventType[]): boolean {\r\n    return types.includes(event.type);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if correlated event matches any of the provided severities\r\n   */\r\n  protected matchesAnySeverity(event: CorrelatedEvent, severities: EventSeverity[]): boolean {\r\n    return severities.includes(event.severity);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if correlated event matches any of the provided statuses\r\n   */\r\n  protected matchesAnyStatus(event: CorrelatedEvent, statuses: EventStatus[]): boolean {\r\n    return statuses.includes(event.status);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if correlated event matches any of the provided processing statuses\r\n   */\r\n  protected matchesAnyProcessingStatus(event: CorrelatedEvent, statuses: EventProcessingStatus[]): boolean {\r\n    return statuses.includes(event.processingStatus);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if correlated event matches any of the provided correlation statuses\r\n   */\r\n  protected matchesAnyCorrelationStatus(event: CorrelatedEvent, statuses: CorrelationStatus[]): boolean {\r\n    return statuses.includes(event.correlationStatus);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if correlated event matches any of the provided confidence levels\r\n   */\r\n  protected matchesAnyConfidenceLevel(event: CorrelatedEvent, levels: ConfidenceLevel[]): boolean {\r\n    return levels.includes(event.confidenceLevel);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if correlated event has any of the provided tags\r\n   */\r\n  protected hasAnyTag(event: CorrelatedEvent, tags: string[]): boolean {\r\n    return event.tags.some(tag => tags.includes(tag));\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if correlated event has all of the provided tags\r\n   */\r\n  protected hasAllTags(event: CorrelatedEvent, tags: string[]): boolean {\r\n    return tags.every(tag => event.tags.includes(tag));\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if correlation quality score is within range\r\n   */\r\n  protected isCorrelationQualityScoreWithinRange(event: CorrelatedEvent, minScore?: number, maxScore?: number): boolean {\r\n    const qualityScore = event.correlationQualityScore;\r\n    \r\n    if (qualityScore === undefined) {\r\n      return minScore === undefined; // If no min score required, undefined is acceptable\r\n    }\r\n    \r\n    if (minScore !== undefined && qualityScore < minScore) {\r\n      return false;\r\n    }\r\n    \r\n    if (maxScore !== undefined && qualityScore > maxScore) {\r\n      return false;\r\n    }\r\n    \r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if risk score is within range\r\n   */\r\n  protected isRiskScoreWithinRange(event: CorrelatedEvent, minScore?: number, maxScore?: number): boolean {\r\n    const riskScore = event.riskScore;\r\n    \r\n    if (riskScore === undefined) {\r\n      return minScore === undefined; // If no min score required, undefined is acceptable\r\n    }\r\n    \r\n    if (minScore !== undefined && riskScore < minScore) {\r\n      return false;\r\n    }\r\n    \r\n    if (maxScore !== undefined && riskScore > maxScore) {\r\n      return false;\r\n    }\r\n    \r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if applied rule exists\r\n   */\r\n  protected hasAppliedRule(event: CorrelatedEvent, ruleId: string): boolean {\r\n    return event.hasAppliedRule(ruleId);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if correlation match by rule exists\r\n   */\r\n  protected hasMatchByRule(event: CorrelatedEvent, ruleId: string): boolean {\r\n    return event.getMatchesByRule(ruleId).length > 0;\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if correlation match by type exists\r\n   */\r\n  protected hasMatchByType(event: CorrelatedEvent, matchType: CorrelationMatchType): boolean {\r\n    return event.getMatchesByType(matchType).length > 0;\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if correlation pattern exists\r\n   */\r\n  protected hasCorrelationPattern(event: CorrelatedEvent, pattern: string): boolean {\r\n    return event.correlationPatterns.includes(pattern);\r\n  }\r\n}\r\n\r\n/**\r\n * Correlation Completed Specification\r\n * \r\n * Specification for correlated events that have completed correlation.\r\n */\r\nexport class CorrelationCompletedSpecification extends CorrelatedEventSpecification {\r\n  isSatisfiedBy(event: CorrelatedEvent): boolean {\r\n    return event.isCorrelationCompleted();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Correlated event has completed correlation';\r\n  }\r\n}\r\n\r\n/**\r\n * Correlation Failed Specification\r\n * \r\n * Specification for correlated events that have failed correlation.\r\n */\r\nexport class CorrelationFailedSpecification extends CorrelatedEventSpecification {\r\n  isSatisfiedBy(event: CorrelatedEvent): boolean {\r\n    return event.isCorrelationFailed();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Correlated event has failed correlation';\r\n  }\r\n}\r\n\r\n/**\r\n * Correlation In Progress Specification\r\n * \r\n * Specification for correlated events that are currently being correlated.\r\n */\r\nexport class CorrelationInProgressSpecification extends CorrelatedEventSpecification {\r\n  isSatisfiedBy(event: CorrelatedEvent): boolean {\r\n    return event.isCorrelationInProgress();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Correlated event is currently being correlated';\r\n  }\r\n}\r\n\r\n/**\r\n * Correlation Partial Specification\r\n * \r\n * Specification for correlated events that have partial correlation results.\r\n */\r\nexport class CorrelationPartialSpecification extends CorrelatedEventSpecification {\r\n  isSatisfiedBy(event: CorrelatedEvent): boolean {\r\n    return event.isCorrelationPartial();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Correlated event has partial correlation results';\r\n  }\r\n}\r\n\r\n/**\r\n * High Correlation Quality Specification\r\n * \r\n * Specification for correlated events with high correlation quality scores.\r\n */\r\nexport class HighCorrelationQualitySpecification extends CorrelatedEventSpecification {\r\n  isSatisfiedBy(event: CorrelatedEvent): boolean {\r\n    return event.hasHighCorrelationQuality();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Correlated event has high correlation quality (>= 70)';\r\n  }\r\n}\r\n\r\n/**\r\n * Has Validation Errors Specification\r\n * \r\n * Specification for correlated events that have validation errors.\r\n */\r\nexport class HasValidationErrorsSpecification extends CorrelatedEventSpecification {\r\n  isSatisfiedBy(event: CorrelatedEvent): boolean {\r\n    return event.hasValidationErrors();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Correlated event has validation errors';\r\n  }\r\n}\r\n\r\n/**\r\n * Requires Manual Review Specification\r\n * \r\n * Specification for correlated events that require manual review.\r\n */\r\nexport class RequiresManualReviewSpecification extends CorrelatedEventSpecification {\r\n  isSatisfiedBy(event: CorrelatedEvent): boolean {\r\n    return event.requiresManualReview;\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Correlated event requires manual review';\r\n  }\r\n}\r\n\r\n/**\r\n * Ready For Next Stage Specification\r\n * \r\n * Specification for correlated events that are ready for the next processing stage.\r\n */\r\nexport class ReadyForNextStageSpecification extends CorrelatedEventSpecification {\r\n  isSatisfiedBy(event: CorrelatedEvent): boolean {\r\n    return event.isReadyForNextStage();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Correlated event is ready for next processing stage';\r\n  }\r\n}\r\n\r\n/**\r\n * High Confidence Correlation Specification\r\n * \r\n * Specification for correlated events with high confidence correlation.\r\n */\r\nexport class HighConfidenceCorrelationSpecification extends CorrelatedEventSpecification {\r\n  isSatisfiedBy(event: CorrelatedEvent): boolean {\r\n    return event.isHighConfidenceCorrelation();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Correlated event has high confidence correlation';\r\n  }\r\n}\r\n\r\n/**\r\n * Has Attack Chain Specification\r\n * \r\n * Specification for correlated events that have an attack chain.\r\n */\r\nexport class HasAttackChainSpecification extends CorrelatedEventSpecification {\r\n  isSatisfiedBy(event: CorrelatedEvent): boolean {\r\n    return event.hasAttackChain();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Correlated event has an attack chain';\r\n  }\r\n}\r\n\r\n/**\r\n * Has Temporal Correlation Specification\r\n * \r\n * Specification for correlated events that have temporal correlation data.\r\n */\r\nexport class HasTemporalCorrelationSpecification extends CorrelatedEventSpecification {\r\n  isSatisfiedBy(event: CorrelatedEvent): boolean {\r\n    return event.hasTemporalCorrelation();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Correlated event has temporal correlation data';\r\n  }\r\n}\r\n\r\n/**\r\n * Has Spatial Correlation Specification\r\n * \r\n * Specification for correlated events that have spatial correlation data.\r\n */\r\nexport class HasSpatialCorrelationSpecification extends CorrelatedEventSpecification {\r\n  isSatisfiedBy(event: CorrelatedEvent): boolean {\r\n    return event.hasSpatialCorrelation();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Correlated event has spatial correlation data';\r\n  }\r\n}\r\n\r\n/**\r\n * Has Behavioral Correlation Specification\r\n * \r\n * Specification for correlated events that have behavioral correlation data.\r\n */\r\nexport class HasBehavioralCorrelationSpecification extends CorrelatedEventSpecification {\r\n  isSatisfiedBy(event: CorrelatedEvent): boolean {\r\n    return event.hasBehavioralCorrelation();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Correlated event has behavioral correlation data';\r\n  }\r\n}\r\n\r\n/**\r\n * Correlation Status Specification\r\n * \r\n * Specification for correlated events with specific correlation statuses.\r\n */\r\nexport class CorrelationStatusSpecification extends CorrelatedEventSpecification {\r\n  constructor(private readonly statuses: CorrelationStatus[]) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: CorrelatedEvent): boolean {\r\n    return this.matchesAnyCorrelationStatus(event, this.statuses);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Correlated event status is one of: ${this.statuses.join(', ')}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Confidence Level Specification\r\n * \r\n * Specification for correlated events with specific confidence levels.\r\n */\r\nexport class ConfidenceLevelSpecification extends CorrelatedEventSpecification {\r\n  constructor(private readonly levels: ConfidenceLevel[]) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: CorrelatedEvent): boolean {\r\n    return this.matchesAnyConfidenceLevel(event, this.levels);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Correlated event confidence level is one of: ${this.levels.join(', ')}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Correlation Quality Score Range Specification\r\n * \r\n * Specification for correlated events within a specific correlation quality score range.\r\n */\r\nexport class CorrelationQualityScoreRangeSpecification extends CorrelatedEventSpecification {\r\n  constructor(\r\n    private readonly minScore?: number,\r\n    private readonly maxScore?: number\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: CorrelatedEvent): boolean {\r\n    return this.isCorrelationQualityScoreWithinRange(event, this.minScore, this.maxScore);\r\n  }\r\n\r\n  getDescription(): string {\r\n    if (this.minScore !== undefined && this.maxScore !== undefined) {\r\n      return `Correlated event quality score is between ${this.minScore} and ${this.maxScore}`;\r\n    } else if (this.minScore !== undefined) {\r\n      return `Correlated event quality score is at least ${this.minScore}`;\r\n    } else if (this.maxScore !== undefined) {\r\n      return `Correlated event quality score is at most ${this.maxScore}`;\r\n    }\r\n    return 'Correlated event has any quality score';\r\n  }\r\n}\r\n\r\n/**\r\n * Applied Rule Specification\r\n * \r\n * Specification for correlated events that have specific rules applied.\r\n */\r\nexport class AppliedRuleSpecification extends CorrelatedEventSpecification {\r\n  constructor(private readonly ruleId: string) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: CorrelatedEvent): boolean {\r\n    return this.hasAppliedRule(event, this.ruleId);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Correlated event has applied rule: ${this.ruleId}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Enriched Event Specification\r\n * \r\n * Specification for correlated events that reference a specific enriched event.\r\n */\r\nexport class EnrichedEventSpecification extends CorrelatedEventSpecification {\r\n  constructor(private readonly enrichedEventId: UniqueEntityId) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: CorrelatedEvent): boolean {\r\n    return event.enrichedEventId.equals(this.enrichedEventId);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Correlated event references enriched event: ${this.enrichedEventId.toString()}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Correlation ID Specification\r\n * \r\n * Specification for correlated events with a specific correlation ID.\r\n */\r\nexport class CorrelationIdSpecification extends CorrelatedEventSpecification {\r\n  constructor(private readonly correlationId: string) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: CorrelatedEvent): boolean {\r\n    return event.correlationId === this.correlationId;\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Correlated event has correlation ID: ${this.correlationId}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Has Related Events Specification\r\n * \r\n * Specification for correlated events that have related events.\r\n */\r\nexport class HasRelatedEventsSpecification extends CorrelatedEventSpecification {\r\n  constructor(private readonly minCount: number = 1) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: CorrelatedEvent): boolean {\r\n    return event.relatedEventIds.length >= this.minCount;\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Correlated event has at least ${this.minCount} related events`;\r\n  }\r\n}\r\n\r\n/**\r\n * Has Child Events Specification\r\n * \r\n * Specification for correlated events that have child events.\r\n */\r\nexport class HasChildEventsSpecification extends CorrelatedEventSpecification {\r\n  constructor(private readonly minCount: number = 1) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: CorrelatedEvent): boolean {\r\n    return event.childEventIds.length >= this.minCount;\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Correlated event has at least ${this.minCount} child events`;\r\n  }\r\n}\r\n\r\n/**\r\n * Correlation Pattern Specification\r\n * \r\n * Specification for correlated events that have specific correlation patterns.\r\n */\r\nexport class CorrelationPatternSpecification extends CorrelatedEventSpecification {\r\n  constructor(private readonly patterns: string[]) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: CorrelatedEvent): boolean {\r\n    return this.patterns.some(pattern => this.hasCorrelationPattern(event, pattern));\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Correlated event has patterns: ${this.patterns.join(', ')}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Match Type Specification\r\n * \r\n * Specification for correlated events that have matches of specific types.\r\n */\r\nexport class MatchTypeSpecification extends CorrelatedEventSpecification {\r\n  constructor(private readonly matchTypes: CorrelationMatchType[]) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: CorrelatedEvent): boolean {\r\n    return this.matchTypes.some(type => this.hasMatchByType(event, type));\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Correlated event has matches of types: ${this.matchTypes.join(', ')}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Exceeded Max Attempts Specification\r\n * \r\n * Specification for correlated events that have exceeded maximum correlation attempts.\r\n */\r\nexport class ExceededMaxAttemptsSpecification extends CorrelatedEventSpecification {\r\n  isSatisfiedBy(event: CorrelatedEvent): boolean {\r\n    return event.hasExceededMaxCorrelationAttempts();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Correlated event has exceeded maximum correlation attempts';\r\n  }\r\n}\r\n\r\n/**\r\n * Reviewed Event Specification\r\n * \r\n * Specification for correlated events that have been manually reviewed.\r\n */\r\nexport class ReviewedEventSpecification extends CorrelatedEventSpecification {\r\n  isSatisfiedBy(event: CorrelatedEvent): boolean {\r\n    return event.reviewedAt !== undefined;\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Correlated event has been manually reviewed';\r\n  }\r\n}\r\n\r\n/**\r\n * Pending Review Specification\r\n * \r\n * Specification for correlated events that are pending manual review.\r\n */\r\nexport class PendingReviewSpecification extends CorrelatedEventSpecification {\r\n  isSatisfiedBy(event: CorrelatedEvent): boolean {\r\n    return event.requiresManualReview && event.reviewedAt === undefined;\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Correlated event is pending manual review';\r\n  }\r\n}\r\n\r\n/**\r\n * Correlation Duration Range Specification\r\n * \r\n * Specification for correlated events with correlation duration within a specific range.\r\n */\r\nexport class CorrelationDurationRangeSpecification extends CorrelatedEventSpecification {\r\n  constructor(\r\n    private readonly minDurationMs?: number,\r\n    private readonly maxDurationMs?: number\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: CorrelatedEvent): boolean {\r\n    const duration = event.getCorrelationDuration();\r\n    \r\n    if (duration === null) {\r\n      return false; // No duration available\r\n    }\r\n    \r\n    if (this.minDurationMs !== undefined && duration < this.minDurationMs) {\r\n      return false;\r\n    }\r\n    \r\n    if (this.maxDurationMs !== undefined && duration > this.maxDurationMs) {\r\n      return false;\r\n    }\r\n    \r\n    return true;\r\n  }\r\n\r\n  getDescription(): string {\r\n    const formatDuration = (ms: number) => `${ms}ms`;\r\n\r\n    if (this.minDurationMs !== undefined && this.maxDurationMs !== undefined) {\r\n      return `Correlated event duration is between ${formatDuration(this.minDurationMs)} and ${formatDuration(this.maxDurationMs)}`;\r\n    } else if (this.minDurationMs !== undefined) {\r\n      return `Correlated event duration is at least ${formatDuration(this.minDurationMs)}`;\r\n    } else if (this.maxDurationMs !== undefined) {\r\n      return `Correlated event duration is at most ${formatDuration(this.maxDurationMs)}`;\r\n    }\r\n    return 'Correlated event has any duration';\r\n  }\r\n}\r\n\r\n/**\r\n * Average Match Confidence Range Specification\r\n * \r\n * Specification for correlated events with average match confidence within a specific range.\r\n */\r\nexport class AverageMatchConfidenceRangeSpecification extends CorrelatedEventSpecification {\r\n  constructor(\r\n    private readonly minConfidence?: number,\r\n    private readonly maxConfidence?: number\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: CorrelatedEvent): boolean {\r\n    const avgConfidence = event.getAverageMatchConfidence();\r\n    \r\n    if (avgConfidence === null) {\r\n      return this.minConfidence === undefined; // If no min confidence required, null is acceptable\r\n    }\r\n    \r\n    if (this.minConfidence !== undefined && avgConfidence < this.minConfidence) {\r\n      return false;\r\n    }\r\n    \r\n    if (this.maxConfidence !== undefined && avgConfidence > this.maxConfidence) {\r\n      return false;\r\n    }\r\n    \r\n    return true;\r\n  }\r\n\r\n  getDescription(): string {\r\n    if (this.minConfidence !== undefined && this.maxConfidence !== undefined) {\r\n      return `Correlated event average match confidence is between ${this.minConfidence} and ${this.maxConfidence}`;\r\n    } else if (this.minConfidence !== undefined) {\r\n      return `Correlated event average match confidence is at least ${this.minConfidence}`;\r\n    } else if (this.maxConfidence !== undefined) {\r\n      return `Correlated event average match confidence is at most ${this.maxConfidence}`;\r\n    }\r\n    return 'Correlated event has any average match confidence';\r\n  }\r\n}\r\n\r\n/**\r\n * Composite CorrelatedEvent Specification Builder\r\n * \r\n * Builder for creating complex correlated event specifications using fluent interface.\r\n */\r\nexport class CorrelatedEventSpecificationBuilder {\r\n  private specifications: CorrelatedEventSpecification[] = [];\r\n\r\n  /**\r\n   * Add correlation completed filter\r\n   */\r\n  correlationCompleted(): CorrelatedEventSpecificationBuilder {\r\n    this.specifications.push(new CorrelationCompletedSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add correlation failed filter\r\n   */\r\n  correlationFailed(): CorrelatedEventSpecificationBuilder {\r\n    this.specifications.push(new CorrelationFailedSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add correlation in progress filter\r\n   */\r\n  correlationInProgress(): CorrelatedEventSpecificationBuilder {\r\n    this.specifications.push(new CorrelationInProgressSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add correlation partial filter\r\n   */\r\n  correlationPartial(): CorrelatedEventSpecificationBuilder {\r\n    this.specifications.push(new CorrelationPartialSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add high correlation quality filter\r\n   */\r\n  highCorrelationQuality(): CorrelatedEventSpecificationBuilder {\r\n    this.specifications.push(new HighCorrelationQualitySpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add has validation errors filter\r\n   */\r\n  hasValidationErrors(): CorrelatedEventSpecificationBuilder {\r\n    this.specifications.push(new HasValidationErrorsSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add requires manual review filter\r\n   */\r\n  requiresManualReview(): CorrelatedEventSpecificationBuilder {\r\n    this.specifications.push(new RequiresManualReviewSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add ready for next stage filter\r\n   */\r\n  readyForNextStage(): CorrelatedEventSpecificationBuilder {\r\n    this.specifications.push(new ReadyForNextStageSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add high confidence correlation filter\r\n   */\r\n  highConfidenceCorrelation(): CorrelatedEventSpecificationBuilder {\r\n    this.specifications.push(new HighConfidenceCorrelationSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add has attack chain filter\r\n   */\r\n  hasAttackChain(): CorrelatedEventSpecificationBuilder {\r\n    this.specifications.push(new HasAttackChainSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add has temporal correlation filter\r\n   */\r\n  hasTemporalCorrelation(): CorrelatedEventSpecificationBuilder {\r\n    this.specifications.push(new HasTemporalCorrelationSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add has spatial correlation filter\r\n   */\r\n  hasSpatialCorrelation(): CorrelatedEventSpecificationBuilder {\r\n    this.specifications.push(new HasSpatialCorrelationSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add has behavioral correlation filter\r\n   */\r\n  hasBehavioralCorrelation(): CorrelatedEventSpecificationBuilder {\r\n    this.specifications.push(new HasBehavioralCorrelationSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add correlation status filter\r\n   */\r\n  withCorrelationStatus(...statuses: CorrelationStatus[]): CorrelatedEventSpecificationBuilder {\r\n    this.specifications.push(new CorrelationStatusSpecification(statuses));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add confidence level filter\r\n   */\r\n  withConfidenceLevel(...levels: ConfidenceLevel[]): CorrelatedEventSpecificationBuilder {\r\n    this.specifications.push(new ConfidenceLevelSpecification(levels));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add correlation quality score range filter\r\n   */\r\n  correlationQualityScoreRange(minScore?: number, maxScore?: number): CorrelatedEventSpecificationBuilder {\r\n    this.specifications.push(new CorrelationQualityScoreRangeSpecification(minScore, maxScore));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add applied rule filter\r\n   */\r\n  withAppliedRule(ruleId: string): CorrelatedEventSpecificationBuilder {\r\n    this.specifications.push(new AppliedRuleSpecification(ruleId));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add enriched event filter\r\n   */\r\n  fromEnrichedEvent(enrichedEventId: UniqueEntityId): CorrelatedEventSpecificationBuilder {\r\n    this.specifications.push(new EnrichedEventSpecification(enrichedEventId));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add correlation ID filter\r\n   */\r\n  withCorrelationId(correlationId: string): CorrelatedEventSpecificationBuilder {\r\n    this.specifications.push(new CorrelationIdSpecification(correlationId));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add has related events filter\r\n   */\r\n  hasRelatedEvents(minCount: number = 1): CorrelatedEventSpecificationBuilder {\r\n    this.specifications.push(new HasRelatedEventsSpecification(minCount));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add has child events filter\r\n   */\r\n  hasChildEvents(minCount: number = 1): CorrelatedEventSpecificationBuilder {\r\n    this.specifications.push(new HasChildEventsSpecification(minCount));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add correlation pattern filter\r\n   */\r\n  withCorrelationPatterns(...patterns: string[]): CorrelatedEventSpecificationBuilder {\r\n    this.specifications.push(new CorrelationPatternSpecification(patterns));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add match type filter\r\n   */\r\n  withMatchTypes(...matchTypes: CorrelationMatchType[]): CorrelatedEventSpecificationBuilder {\r\n    this.specifications.push(new MatchTypeSpecification(matchTypes));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add exceeded max attempts filter\r\n   */\r\n  exceededMaxAttempts(): CorrelatedEventSpecificationBuilder {\r\n    this.specifications.push(new ExceededMaxAttemptsSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add reviewed filter\r\n   */\r\n  reviewed(): CorrelatedEventSpecificationBuilder {\r\n    this.specifications.push(new ReviewedEventSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add pending review filter\r\n   */\r\n  pendingReview(): CorrelatedEventSpecificationBuilder {\r\n    this.specifications.push(new PendingReviewSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add correlation duration range filter\r\n   */\r\n  correlationDurationRange(minDurationMs?: number, maxDurationMs?: number): CorrelatedEventSpecificationBuilder {\r\n    this.specifications.push(new CorrelationDurationRangeSpecification(minDurationMs, maxDurationMs));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add average match confidence range filter\r\n   */\r\n  averageMatchConfidenceRange(minConfidence?: number, maxConfidence?: number): CorrelatedEventSpecificationBuilder {\r\n    this.specifications.push(new AverageMatchConfidenceRangeSpecification(minConfidence, maxConfidence));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Build the final specification using AND logic\r\n   */\r\n  build(): CorrelatedEventSpecification {\r\n    if (this.specifications.length === 0) {\r\n      throw new Error('At least one specification must be added');\r\n    }\r\n\r\n    if (this.specifications.length === 1) {\r\n      return this.specifications[0];\r\n    }\r\n\r\n    // Combine all specifications with AND logic\r\n    let combined = this.specifications[0];\r\n    for (let i = 1; i < this.specifications.length; i++) {\r\n      combined = combined.and(this.specifications[i]) as CorrelatedEventSpecification;\r\n    }\r\n\r\n    return combined;\r\n  }\r\n\r\n  /**\r\n   * Build the final specification using OR logic\r\n   */\r\n  buildWithOr(): CorrelatedEventSpecification {\r\n    if (this.specifications.length === 0) {\r\n      throw new Error('At least one specification must be added');\r\n    }\r\n\r\n    if (this.specifications.length === 1) {\r\n      return this.specifications[0];\r\n    }\r\n\r\n    // Combine all specifications with OR logic\r\n    let combined = this.specifications[0];\r\n    for (let i = 1; i < this.specifications.length; i++) {\r\n      combined = combined.or(this.specifications[i]) as CorrelatedEventSpecification;\r\n    }\r\n\r\n    return combined;\r\n  }\r\n\r\n  /**\r\n   * Create a new builder instance\r\n   */\r\n  static create(): CorrelatedEventSpecificationBuilder {\r\n    return new CorrelatedEventSpecificationBuilder();\r\n  }\r\n}"], "version": 3}