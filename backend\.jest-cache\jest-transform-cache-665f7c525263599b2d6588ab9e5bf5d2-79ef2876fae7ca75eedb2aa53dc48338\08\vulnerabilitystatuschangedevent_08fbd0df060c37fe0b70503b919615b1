5856e1141d006c3280e8021649f3b298
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VulnerabilityStatusChangedEvent = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
const vulnerability_entity_1 = require("../entities/vulnerability/vulnerability.entity");
/**
 * Vulnerability Status Changed Domain Event
 *
 * Published when a vulnerability's status changes (e.g., from discovered to triaged,
 * from in_progress to remediated, etc.).
 *
 * This event enables:
 * - Workflow automation based on status transitions
 * - Audit trail for vulnerability lifecycle
 * - Notification triggers for stakeholders
 * - Metrics collection for remediation timelines
 * - Integration with external systems (ITSM, etc.)
 */
class VulnerabilityStatusChangedEvent extends shared_kernel_1.BaseDomainEvent {
    constructor(aggregateId, eventData, options) {
        super(aggregateId, eventData, options);
    }
    /**
     * Get the vulnerability ID
     */
    get vulnerabilityId() {
        return this.eventData.vulnerabilityId;
    }
    /**
     * Get the old status
     */
    get oldStatus() {
        return this.eventData.oldStatus;
    }
    /**
     * Get the new status
     */
    get newStatus() {
        return this.eventData.newStatus;
    }
    /**
     * Get the reason for the change
     */
    get reason() {
        return this.eventData.reason;
    }
    /**
     * Get who made the change
     */
    get changedBy() {
        return this.eventData.changedBy;
    }
    /**
     * Get additional context
     */
    get context() {
        return this.eventData.context;
    }
    /**
     * Check if this is a status progression (forward movement in lifecycle)
     */
    isStatusProgression() {
        const statusOrder = {
            [vulnerability_entity_1.VulnerabilityStatus.DISCOVERED]: 1,
            [vulnerability_entity_1.VulnerabilityStatus.CONFIRMED]: 2,
            [vulnerability_entity_1.VulnerabilityStatus.TRIAGED]: 3,
            [vulnerability_entity_1.VulnerabilityStatus.IN_PROGRESS]: 4,
            [vulnerability_entity_1.VulnerabilityStatus.REMEDIATED]: 5,
            [vulnerability_entity_1.VulnerabilityStatus.VERIFIED]: 6,
            [vulnerability_entity_1.VulnerabilityStatus.CLOSED]: 7,
            [vulnerability_entity_1.VulnerabilityStatus.FALSE_POSITIVE]: 0,
            [vulnerability_entity_1.VulnerabilityStatus.ACCEPTED_RISK]: 0,
        };
        return statusOrder[this.eventData.newStatus] > statusOrder[this.eventData.oldStatus];
    }
    /**
     * Check if this is a status regression (backward movement)
     */
    isStatusRegression() {
        const statusOrder = {
            [vulnerability_entity_1.VulnerabilityStatus.DISCOVERED]: 1,
            [vulnerability_entity_1.VulnerabilityStatus.CONFIRMED]: 2,
            [vulnerability_entity_1.VulnerabilityStatus.TRIAGED]: 3,
            [vulnerability_entity_1.VulnerabilityStatus.IN_PROGRESS]: 4,
            [vulnerability_entity_1.VulnerabilityStatus.REMEDIATED]: 5,
            [vulnerability_entity_1.VulnerabilityStatus.VERIFIED]: 6,
            [vulnerability_entity_1.VulnerabilityStatus.CLOSED]: 7,
            [vulnerability_entity_1.VulnerabilityStatus.FALSE_POSITIVE]: 0,
            [vulnerability_entity_1.VulnerabilityStatus.ACCEPTED_RISK]: 0,
        };
        return statusOrder[this.eventData.newStatus] < statusOrder[this.eventData.oldStatus] &&
            statusOrder[this.eventData.newStatus] > 0;
    }
    /**
     * Check if vulnerability was closed (remediated, verified, or closed)
     */
    wasVulnerabilityClosed() {
        return [
            vulnerability_entity_1.VulnerabilityStatus.REMEDIATED,
            vulnerability_entity_1.VulnerabilityStatus.VERIFIED,
            vulnerability_entity_1.VulnerabilityStatus.CLOSED,
        ].includes(this.eventData.newStatus);
    }
    /**
     * Check if vulnerability was dismissed (false positive or accepted risk)
     */
    wasVulnerabilityDismissed() {
        return [
            vulnerability_entity_1.VulnerabilityStatus.FALSE_POSITIVE,
            vulnerability_entity_1.VulnerabilityStatus.ACCEPTED_RISK,
        ].includes(this.eventData.newStatus);
    }
    /**
     * Check if vulnerability was reopened
     */
    wasVulnerabilityReopened() {
        const closedStatuses = [
            vulnerability_entity_1.VulnerabilityStatus.REMEDIATED,
            vulnerability_entity_1.VulnerabilityStatus.VERIFIED,
            vulnerability_entity_1.VulnerabilityStatus.CLOSED,
            vulnerability_entity_1.VulnerabilityStatus.FALSE_POSITIVE,
            vulnerability_entity_1.VulnerabilityStatus.ACCEPTED_RISK,
        ];
        const activeStatuses = [
            vulnerability_entity_1.VulnerabilityStatus.DISCOVERED,
            vulnerability_entity_1.VulnerabilityStatus.CONFIRMED,
            vulnerability_entity_1.VulnerabilityStatus.TRIAGED,
            vulnerability_entity_1.VulnerabilityStatus.IN_PROGRESS,
        ];
        return closedStatuses.includes(this.eventData.oldStatus) &&
            activeStatuses.includes(this.eventData.newStatus);
    }
    /**
     * Get status transition type
     */
    getTransitionType() {
        if (this.wasVulnerabilityClosed())
            return 'closure';
        if (this.wasVulnerabilityDismissed())
            return 'dismissal';
        if (this.wasVulnerabilityReopened())
            return 'reopening';
        if (this.isStatusProgression())
            return 'progression';
        if (this.isStatusRegression())
            return 'regression';
        return 'lateral';
    }
    /**
     * Get notification priority based on status change
     */
    getNotificationPriority() {
        const transitionType = this.getTransitionType();
        switch (transitionType) {
            case 'closure':
                return 'medium'; // Good news, but not urgent
            case 'dismissal':
                return 'low'; // Informational
            case 'reopening':
                return 'high'; // Requires attention
            case 'regression':
                return 'high'; // Problem that needs investigation
            case 'progression':
                return 'low'; // Normal workflow progression
            default:
                return 'low';
        }
    }
    /**
     * Get required notifications based on status change
     */
    getRequiredNotifications() {
        const transitionType = this.getTransitionType();
        const priority = this.getNotificationPriority();
        const baseChannels = ['email', 'webhook'];
        const baseRecipients = ['vulnerability_team'];
        switch (transitionType) {
            case 'closure':
                return {
                    channels: [...baseChannels, 'dashboard'],
                    recipients: [...baseRecipients, 'stakeholders'],
                    urgency: 'within_day',
                };
            case 'dismissal':
                return {
                    channels: baseChannels,
                    recipients: baseRecipients,
                    urgency: 'routine',
                };
            case 'reopening':
                return {
                    channels: [...baseChannels, 'slack', 'dashboard'],
                    recipients: [...baseRecipients, 'security_team', 'management'],
                    urgency: 'within_hour',
                };
            case 'regression':
                return {
                    channels: [...baseChannels, 'slack'],
                    recipients: [...baseRecipients, 'security_team'],
                    urgency: 'within_hour',
                };
            default:
                return {
                    channels: baseChannels,
                    recipients: baseRecipients,
                    urgency: 'routine',
                };
        }
    }
    /**
     * Get workflow actions triggered by this status change
     */
    getTriggeredWorkflowActions() {
        const actions = [];
        const transitionType = this.getTransitionType();
        switch (this.eventData.newStatus) {
            case vulnerability_entity_1.VulnerabilityStatus.CONFIRMED:
                actions.push('assign_to_security_team', 'create_risk_assessment');
                break;
            case vulnerability_entity_1.VulnerabilityStatus.TRIAGED:
                actions.push('assign_remediation_owner', 'set_remediation_timeline');
                break;
            case vulnerability_entity_1.VulnerabilityStatus.IN_PROGRESS:
                actions.push('start_remediation_tracking', 'notify_stakeholders');
                break;
            case vulnerability_entity_1.VulnerabilityStatus.REMEDIATED:
                actions.push('schedule_verification', 'update_asset_inventory');
                break;
            case vulnerability_entity_1.VulnerabilityStatus.VERIFIED:
                actions.push('close_vulnerability', 'update_metrics');
                break;
            case vulnerability_entity_1.VulnerabilityStatus.CLOSED:
                actions.push('archive_vulnerability', 'generate_closure_report');
                break;
            case vulnerability_entity_1.VulnerabilityStatus.FALSE_POSITIVE:
                actions.push('update_scanner_rules', 'document_false_positive');
                break;
            case vulnerability_entity_1.VulnerabilityStatus.ACCEPTED_RISK:
                actions.push('document_risk_acceptance', 'schedule_risk_review');
                break;
        }
        if (transitionType === 'reopening') {
            actions.push('investigate_reopening_cause', 'reassess_vulnerability');
        }
        return actions;
    }
    /**
     * Get metrics to update based on status change
     */
    getMetricsToUpdate() {
        const metrics = [];
        const baseTags = {
            old_status: this.eventData.oldStatus,
            new_status: this.eventData.newStatus,
            transition_type: this.getTransitionType(),
        };
        // Status transition counter
        metrics.push({
            metric: 'vulnerability_status_transitions',
            operation: 'increment',
            tags: baseTags,
        });
        // Status-specific metrics
        if (this.wasVulnerabilityClosed()) {
            metrics.push({
                metric: 'vulnerabilities_closed',
                operation: 'increment',
                tags: { status: this.eventData.newStatus },
            });
            // Calculate time to closure if we have discovery time
            metrics.push({
                metric: 'vulnerability_closure_time',
                operation: 'timing',
                tags: { status: this.eventData.newStatus },
            });
        }
        if (this.wasVulnerabilityDismissed()) {
            metrics.push({
                metric: 'vulnerabilities_dismissed',
                operation: 'increment',
                tags: { reason: this.eventData.newStatus },
            });
        }
        if (this.wasVulnerabilityReopened()) {
            metrics.push({
                metric: 'vulnerabilities_reopened',
                operation: 'increment',
                tags: { from_status: this.eventData.oldStatus },
            });
        }
        return metrics;
    }
    /**
     * Get compliance reporting requirements
     */
    getComplianceReportingRequirements() {
        const transitionType = this.getTransitionType();
        if (this.wasVulnerabilityClosed()) {
            return {
                required: true,
                frameworks: ['SOX', 'PCI_DSS', 'HIPAA', 'GDPR'],
                deadline: 'within_week',
                reportType: 'closure',
            };
        }
        if (this.wasVulnerabilityDismissed()) {
            return {
                required: true,
                frameworks: ['SOX', 'PCI_DSS'],
                deadline: 'within_week',
                reportType: 'exception',
            };
        }
        if (transitionType === 'reopening') {
            return {
                required: true,
                frameworks: ['SOX', 'PCI_DSS', 'HIPAA'],
                deadline: 'within_24h',
                reportType: 'exception',
            };
        }
        return {
            required: false,
            frameworks: [],
            deadline: 'next_cycle',
            reportType: 'none',
        };
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            eventType: 'VulnerabilityStatusChanged',
            eventId: this.eventId.toString(),
            aggregateId: this.aggregateId.toString(),
            occurredOn: this.occurredOn.toISOString(),
            version: this.version,
            isDispatched: this.isDispatched,
            timestamp: this.occurredOn.toISOString(),
            data: {
                vulnerabilityId: this.eventData.vulnerabilityId,
                oldStatus: this.eventData.oldStatus,
                newStatus: this.eventData.newStatus,
                reason: this.eventData.reason,
                changedBy: this.eventData.changedBy,
                context: this.eventData.context,
                timestamp: this.eventData.timestamp,
            },
            analysis: {
                transitionType: this.getTransitionType(),
                isProgression: this.isStatusProgression(),
                isRegression: this.isStatusRegression(),
                wasClosed: this.wasVulnerabilityClosed(),
                wasDismissed: this.wasVulnerabilityDismissed(),
                wasReopened: this.wasVulnerabilityReopened(),
                notificationPriority: this.getNotificationPriority(),
                requiredNotifications: this.getRequiredNotifications(),
                triggeredActions: this.getTriggeredWorkflowActions(),
                metricsToUpdate: this.getMetricsToUpdate(),
                complianceRequirements: this.getComplianceReportingRequirements(),
            },
        };
    }
    /**
     * Create from JSON representation
     */
    static fromJSON(json) {
        return new VulnerabilityStatusChangedEvent(shared_kernel_1.UniqueEntityId.fromString(json.aggregateId), json.data, {
            eventId: shared_kernel_1.UniqueEntityId.fromString(json.eventId),
            occurredOn: new Date(json.occurredOn),
            version: json.version,
        });
    }
    /**
     * Get human-readable description
     */
    getDescription() {
        const statusText = this.eventData.newStatus.replace(/_/g, ' ');
        const oldStatusText = this.eventData.oldStatus.replace(/_/g, ' ');
        return `Vulnerability status changed from "${oldStatusText}" to "${statusText}": ${this.eventData.reason}`;
    }
    /**
     * Get event summary for logging
     */
    getEventSummary() {
        return {
            eventType: 'VulnerabilityStatusChanged',
            vulnerabilityId: this.eventData.vulnerabilityId,
            statusTransition: `${this.eventData.oldStatus} -> ${this.eventData.newStatus}`,
            transitionType: this.getTransitionType(),
            reason: this.eventData.reason,
            timestamp: this.occurredOn.toISOString(),
        };
    }
}
exports.VulnerabilityStatusChangedEvent = VulnerabilityStatusChangedEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxjb3JlXFxzZWN1cml0eVxcZG9tYWluXFxldmVudHNcXHZ1bG5lcmFiaWxpdHktc3RhdHVzLWNoYW5nZWQuZXZlbnQudHMiLCJtYXBwaW5ncyI6Ijs7O0FBQUEsNkRBQTRFO0FBQzVFLHlGQUFxRjtBQXNCckY7Ozs7Ozs7Ozs7OztHQVlHO0FBQ0gsTUFBYSwrQkFBZ0MsU0FBUSwrQkFBb0Q7SUFDdkcsWUFDRSxXQUEyQixFQUMzQixTQUE4QyxFQUM5QyxPQUlDO1FBRUQsS0FBSyxDQUFDLFdBQVcsRUFBRSxTQUFTLEVBQUUsT0FBTyxDQUFDLENBQUM7SUFDekMsQ0FBQztJQUVEOztPQUVHO0lBQ0gsSUFBSSxlQUFlO1FBQ2pCLE9BQU8sSUFBSSxDQUFDLFNBQVMsQ0FBQyxlQUFlLENBQUM7SUFDeEMsQ0FBQztJQUVEOztPQUVHO0lBQ0gsSUFBSSxTQUFTO1FBQ1gsT0FBTyxJQUFJLENBQUMsU0FBUyxDQUFDLFNBQVMsQ0FBQztJQUNsQyxDQUFDO0lBRUQ7O09BRUc7SUFDSCxJQUFJLFNBQVM7UUFDWCxPQUFPLElBQUksQ0FBQyxTQUFTLENBQUMsU0FBUyxDQUFDO0lBQ2xDLENBQUM7SUFFRDs7T0FFRztJQUNILElBQUksTUFBTTtRQUNSLE9BQU8sSUFBSSxDQUFDLFNBQVMsQ0FBQyxNQUFNLENBQUM7SUFDL0IsQ0FBQztJQUVEOztPQUVHO0lBQ0gsSUFBSSxTQUFTO1FBQ1gsT0FBTyxJQUFJLENBQUMsU0FBUyxDQUFDLFNBQVMsQ0FBQztJQUNsQyxDQUFDO0lBRUQ7O09BRUc7SUFDSCxJQUFJLE9BQU87UUFDVCxPQUFPLElBQUksQ0FBQyxTQUFTLENBQUMsT0FBTyxDQUFDO0lBQ2hDLENBQUM7SUFFRDs7T0FFRztJQUNILG1CQUFtQjtRQUNqQixNQUFNLFdBQVcsR0FBd0M7WUFDdkQsQ0FBQywwQ0FBbUIsQ0FBQyxVQUFVLENBQUMsRUFBRSxDQUFDO1lBQ25DLENBQUMsMENBQW1CLENBQUMsU0FBUyxDQUFDLEVBQUUsQ0FBQztZQUNsQyxDQUFDLDBDQUFtQixDQUFDLE9BQU8sQ0FBQyxFQUFFLENBQUM7WUFDaEMsQ0FBQywwQ0FBbUIsQ0FBQyxXQUFXLENBQUMsRUFBRSxDQUFDO1lBQ3BDLENBQUMsMENBQW1CLENBQUMsVUFBVSxDQUFDLEVBQUUsQ0FBQztZQUNuQyxDQUFDLDBDQUFtQixDQUFDLFFBQVEsQ0FBQyxFQUFFLENBQUM7WUFDakMsQ0FBQywwQ0FBbUIsQ0FBQyxNQUFNLENBQUMsRUFBRSxDQUFDO1lBQy9CLENBQUMsMENBQW1CLENBQUMsY0FBYyxDQUFDLEVBQUUsQ0FBQztZQUN2QyxDQUFDLDBDQUFtQixDQUFDLGFBQWEsQ0FBQyxFQUFFLENBQUM7U0FDdkMsQ0FBQztRQUVGLE9BQU8sV0FBVyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsU0FBUyxDQUFDLEdBQUcsV0FBVyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsU0FBUyxDQUFDLENBQUM7SUFDdkYsQ0FBQztJQUVEOztPQUVHO0lBQ0gsa0JBQWtCO1FBQ2hCLE1BQU0sV0FBVyxHQUF3QztZQUN2RCxDQUFDLDBDQUFtQixDQUFDLFVBQVUsQ0FBQyxFQUFFLENBQUM7WUFDbkMsQ0FBQywwQ0FBbUIsQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDO1lBQ2xDLENBQUMsMENBQW1CLENBQUMsT0FBTyxDQUFDLEVBQUUsQ0FBQztZQUNoQyxDQUFDLDBDQUFtQixDQUFDLFdBQVcsQ0FBQyxFQUFFLENBQUM7WUFDcEMsQ0FBQywwQ0FBbUIsQ0FBQyxVQUFVLENBQUMsRUFBRSxDQUFDO1lBQ25DLENBQUMsMENBQW1CLENBQUMsUUFBUSxDQUFDLEVBQUUsQ0FBQztZQUNqQyxDQUFDLDBDQUFtQixDQUFDLE1BQU0sQ0FBQyxFQUFFLENBQUM7WUFDL0IsQ0FBQywwQ0FBbUIsQ0FBQyxjQUFjLENBQUMsRUFBRSxDQUFDO1lBQ3ZDLENBQUMsMENBQW1CLENBQUMsYUFBYSxDQUFDLEVBQUUsQ0FBQztTQUN2QyxDQUFDO1FBRUYsT0FBTyxXQUFXLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxTQUFTLENBQUMsR0FBRyxXQUFXLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxTQUFTLENBQUM7WUFDN0UsV0FBVyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsU0FBUyxDQUFDLEdBQUcsQ0FBQyxDQUFDO0lBQ25ELENBQUM7SUFFRDs7T0FFRztJQUNILHNCQUFzQjtRQUNwQixPQUFPO1lBQ0wsMENBQW1CLENBQUMsVUFBVTtZQUM5QiwwQ0FBbUIsQ0FBQyxRQUFRO1lBQzVCLDBDQUFtQixDQUFDLE1BQU07U0FDM0IsQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxTQUFTLENBQUMsQ0FBQztJQUN2QyxDQUFDO0lBRUQ7O09BRUc7SUFDSCx5QkFBeUI7UUFDdkIsT0FBTztZQUNMLDBDQUFtQixDQUFDLGNBQWM7WUFDbEMsMENBQW1CLENBQUMsYUFBYTtTQUNsQyxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLFNBQVMsQ0FBQyxDQUFDO0lBQ3ZDLENBQUM7SUFFRDs7T0FFRztJQUNILHdCQUF3QjtRQUN0QixNQUFNLGNBQWMsR0FBRztZQUNyQiwwQ0FBbUIsQ0FBQyxVQUFVO1lBQzlCLDBDQUFtQixDQUFDLFFBQVE7WUFDNUIsMENBQW1CLENBQUMsTUFBTTtZQUMxQiwwQ0FBbUIsQ0FBQyxjQUFjO1lBQ2xDLDBDQUFtQixDQUFDLGFBQWE7U0FDbEMsQ0FBQztRQUVGLE1BQU0sY0FBYyxHQUFHO1lBQ3JCLDBDQUFtQixDQUFDLFVBQVU7WUFDOUIsMENBQW1CLENBQUMsU0FBUztZQUM3QiwwQ0FBbUIsQ0FBQyxPQUFPO1lBQzNCLDBDQUFtQixDQUFDLFdBQVc7U0FDaEMsQ0FBQztRQUVGLE9BQU8sY0FBYyxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLFNBQVMsQ0FBQztZQUNqRCxjQUFjLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsU0FBUyxDQUFDLENBQUM7SUFDM0QsQ0FBQztJQUVEOztPQUVHO0lBQ0gsaUJBQWlCO1FBQ2YsSUFBSSxJQUFJLENBQUMsc0JBQXNCLEVBQUU7WUFBRSxPQUFPLFNBQVMsQ0FBQztRQUNwRCxJQUFJLElBQUksQ0FBQyx5QkFBeUIsRUFBRTtZQUFFLE9BQU8sV0FBVyxDQUFDO1FBQ3pELElBQUksSUFBSSxDQUFDLHdCQUF3QixFQUFFO1lBQUUsT0FBTyxXQUFXLENBQUM7UUFDeEQsSUFBSSxJQUFJLENBQUMsbUJBQW1CLEVBQUU7WUFBRSxPQUFPLGFBQWEsQ0FBQztRQUNyRCxJQUFJLElBQUksQ0FBQyxrQkFBa0IsRUFBRTtZQUFFLE9BQU8sWUFBWSxDQUFDO1FBQ25ELE9BQU8sU0FBUyxDQUFDO0lBQ25CLENBQUM7SUFFRDs7T0FFRztJQUNILHVCQUF1QjtRQUNyQixNQUFNLGNBQWMsR0FBRyxJQUFJLENBQUMsaUJBQWlCLEVBQUUsQ0FBQztRQUVoRCxRQUFRLGNBQWMsRUFBRSxDQUFDO1lBQ3ZCLEtBQUssU0FBUztnQkFDWixPQUFPLFFBQVEsQ0FBQyxDQUFDLDRCQUE0QjtZQUMvQyxLQUFLLFdBQVc7Z0JBQ2QsT0FBTyxLQUFLLENBQUMsQ0FBQyxnQkFBZ0I7WUFDaEMsS0FBSyxXQUFXO2dCQUNkLE9BQU8sTUFBTSxDQUFDLENBQUMscUJBQXFCO1lBQ3RDLEtBQUssWUFBWTtnQkFDZixPQUFPLE1BQU0sQ0FBQyxDQUFDLG1DQUFtQztZQUNwRCxLQUFLLGFBQWE7Z0JBQ2hCLE9BQU8sS0FBSyxDQUFDLENBQUMsOEJBQThCO1lBQzlDO2dCQUNFLE9BQU8sS0FBSyxDQUFDO1FBQ2pCLENBQUM7SUFDSCxDQUFDO0lBRUQ7O09BRUc7SUFDSCx3QkFBd0I7UUFLdEIsTUFBTSxjQUFjLEdBQUcsSUFBSSxDQUFDLGlCQUFpQixFQUFFLENBQUM7UUFDaEQsTUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLHVCQUF1QixFQUFFLENBQUM7UUFFaEQsTUFBTSxZQUFZLEdBQUcsQ0FBQyxPQUFPLEVBQUUsU0FBUyxDQUFDLENBQUM7UUFDMUMsTUFBTSxjQUFjLEdBQUcsQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDO1FBRTlDLFFBQVEsY0FBYyxFQUFFLENBQUM7WUFDdkIsS0FBSyxTQUFTO2dCQUNaLE9BQU87b0JBQ0wsUUFBUSxFQUFFLENBQUMsR0FBRyxZQUFZLEVBQUUsV0FBVyxDQUFDO29CQUN4QyxVQUFVLEVBQUUsQ0FBQyxHQUFHLGNBQWMsRUFBRSxjQUFjLENBQUM7b0JBQy9DLE9BQU8sRUFBRSxZQUFZO2lCQUN0QixDQUFDO1lBRUosS0FBSyxXQUFXO2dCQUNkLE9BQU87b0JBQ0wsUUFBUSxFQUFFLFlBQVk7b0JBQ3RCLFVBQVUsRUFBRSxjQUFjO29CQUMxQixPQUFPLEVBQUUsU0FBUztpQkFDbkIsQ0FBQztZQUVKLEtBQUssV0FBVztnQkFDZCxPQUFPO29CQUNMLFFBQVEsRUFBRSxDQUFDLEdBQUcsWUFBWSxFQUFFLE9BQU8sRUFBRSxXQUFXLENBQUM7b0JBQ2pELFVBQVUsRUFBRSxDQUFDLEdBQUcsY0FBYyxFQUFFLGVBQWUsRUFBRSxZQUFZLENBQUM7b0JBQzlELE9BQU8sRUFBRSxhQUFhO2lCQUN2QixDQUFDO1lBRUosS0FBSyxZQUFZO2dCQUNmLE9BQU87b0JBQ0wsUUFBUSxFQUFFLENBQUMsR0FBRyxZQUFZLEVBQUUsT0FBTyxDQUFDO29CQUNwQyxVQUFVLEVBQUUsQ0FBQyxHQUFHLGNBQWMsRUFBRSxlQUFlLENBQUM7b0JBQ2hELE9BQU8sRUFBRSxhQUFhO2lCQUN2QixDQUFDO1lBRUo7Z0JBQ0UsT0FBTztvQkFDTCxRQUFRLEVBQUUsWUFBWTtvQkFDdEIsVUFBVSxFQUFFLGNBQWM7b0JBQzFCLE9BQU8sRUFBRSxTQUFTO2lCQUNuQixDQUFDO1FBQ04sQ0FBQztJQUNILENBQUM7SUFFRDs7T0FFRztJQUNILDJCQUEyQjtRQUN6QixNQUFNLE9BQU8sR0FBYSxFQUFFLENBQUM7UUFDN0IsTUFBTSxjQUFjLEdBQUcsSUFBSSxDQUFDLGlCQUFpQixFQUFFLENBQUM7UUFFaEQsUUFBUSxJQUFJLENBQUMsU0FBUyxDQUFDLFNBQVMsRUFBRSxDQUFDO1lBQ2pDLEtBQUssMENBQW1CLENBQUMsU0FBUztnQkFDaEMsT0FBTyxDQUFDLElBQUksQ0FBQyx5QkFBeUIsRUFBRSx3QkFBd0IsQ0FBQyxDQUFDO2dCQUNsRSxNQUFNO1lBRVIsS0FBSywwQ0FBbUIsQ0FBQyxPQUFPO2dCQUM5QixPQUFPLENBQUMsSUFBSSxDQUFDLDBCQUEwQixFQUFFLDBCQUEwQixDQUFDLENBQUM7Z0JBQ3JFLE1BQU07WUFFUixLQUFLLDBDQUFtQixDQUFDLFdBQVc7Z0JBQ2xDLE9BQU8sQ0FBQyxJQUFJLENBQUMsNEJBQTRCLEVBQUUscUJBQXFCLENBQUMsQ0FBQztnQkFDbEUsTUFBTTtZQUVSLEtBQUssMENBQW1CLENBQUMsVUFBVTtnQkFDakMsT0FBTyxDQUFDLElBQUksQ0FBQyx1QkFBdUIsRUFBRSx3QkFBd0IsQ0FBQyxDQUFDO2dCQUNoRSxNQUFNO1lBRVIsS0FBSywwQ0FBbUIsQ0FBQyxRQUFRO2dCQUMvQixPQUFPLENBQUMsSUFBSSxDQUFDLHFCQUFxQixFQUFFLGdCQUFnQixDQUFDLENBQUM7Z0JBQ3RELE1BQU07WUFFUixLQUFLLDBDQUFtQixDQUFDLE1BQU07Z0JBQzdCLE9BQU8sQ0FBQyxJQUFJLENBQUMsdUJBQXVCLEVBQUUseUJBQXlCLENBQUMsQ0FBQztnQkFDakUsTUFBTTtZQUVSLEtBQUssMENBQW1CLENBQUMsY0FBYztnQkFDckMsT0FBTyxDQUFDLElBQUksQ0FBQyxzQkFBc0IsRUFBRSx5QkFBeUIsQ0FBQyxDQUFDO2dCQUNoRSxNQUFNO1lBRVIsS0FBSywwQ0FBbUIsQ0FBQyxhQUFhO2dCQUNwQyxPQUFPLENBQUMsSUFBSSxDQUFDLDBCQUEwQixFQUFFLHNCQUFzQixDQUFDLENBQUM7Z0JBQ2pFLE1BQU07UUFDVixDQUFDO1FBRUQsSUFBSSxjQUFjLEtBQUssV0FBVyxFQUFFLENBQUM7WUFDbkMsT0FBTyxDQUFDLElBQUksQ0FBQyw2QkFBNkIsRUFBRSx3QkFBd0IsQ0FBQyxDQUFDO1FBQ3hFLENBQUM7UUFFRCxPQUFPLE9BQU8sQ0FBQztJQUNqQixDQUFDO0lBRUQ7O09BRUc7SUFDSCxrQkFBa0I7UUFNaEIsTUFBTSxPQUFPLEdBS1IsRUFBRSxDQUFDO1FBRVIsTUFBTSxRQUFRLEdBQUc7WUFDZixVQUFVLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxTQUFTO1lBQ3BDLFVBQVUsRUFBRSxJQUFJLENBQUMsU0FBUyxDQUFDLFNBQVM7WUFDcEMsZUFBZSxFQUFFLElBQUksQ0FBQyxpQkFBaUIsRUFBRTtTQUMxQyxDQUFDO1FBRUYsNEJBQTRCO1FBQzVCLE9BQU8sQ0FBQyxJQUFJLENBQUM7WUFDWCxNQUFNLEVBQUUsa0NBQWtDO1lBQzFDLFNBQVMsRUFBRSxXQUFXO1lBQ3RCLElBQUksRUFBRSxRQUFRO1NBQ2YsQ0FBQyxDQUFDO1FBRUgsMEJBQTBCO1FBQzFCLElBQUksSUFBSSxDQUFDLHNCQUFzQixFQUFFLEVBQUUsQ0FBQztZQUNsQyxPQUFPLENBQUMsSUFBSSxDQUFDO2dCQUNYLE1BQU0sRUFBRSx3QkFBd0I7Z0JBQ2hDLFNBQVMsRUFBRSxXQUFXO2dCQUN0QixJQUFJLEVBQUUsRUFBRSxNQUFNLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxTQUFTLEVBQUU7YUFDM0MsQ0FBQyxDQUFDO1lBRUgsc0RBQXNEO1lBQ3RELE9BQU8sQ0FBQyxJQUFJLENBQUM7Z0JBQ1gsTUFBTSxFQUFFLDRCQUE0QjtnQkFDcEMsU0FBUyxFQUFFLFFBQVE7Z0JBQ25CLElBQUksRUFBRSxFQUFFLE1BQU0sRUFBRSxJQUFJLENBQUMsU0FBUyxDQUFDLFNBQVMsRUFBRTthQUMzQyxDQUFDLENBQUM7UUFDTCxDQUFDO1FBRUQsSUFBSSxJQUFJLENBQUMseUJBQXlCLEVBQUUsRUFBRSxDQUFDO1lBQ3JDLE9BQU8sQ0FBQyxJQUFJLENBQUM7Z0JBQ1gsTUFBTSxFQUFFLDJCQUEyQjtnQkFDbkMsU0FBUyxFQUFFLFdBQVc7Z0JBQ3RCLElBQUksRUFBRSxFQUFFLE1BQU0sRUFBRSxJQUFJLENBQUMsU0FBUyxDQUFDLFNBQVMsRUFBRTthQUMzQyxDQUFDLENBQUM7UUFDTCxDQUFDO1FBRUQsSUFBSSxJQUFJLENBQUMsd0JBQXdCLEVBQUUsRUFBRSxDQUFDO1lBQ3BDLE9BQU8sQ0FBQyxJQUFJLENBQUM7Z0JBQ1gsTUFBTSxFQUFFLDBCQUEwQjtnQkFDbEMsU0FBUyxFQUFFLFdBQVc7Z0JBQ3RCLElBQUksRUFBRSxFQUFFLFdBQVcsRUFBRSxJQUFJLENBQUMsU0FBUyxDQUFDLFNBQVMsRUFBRTthQUNoRCxDQUFDLENBQUM7UUFDTCxDQUFDO1FBRUQsT0FBTyxPQUFPLENBQUM7SUFDakIsQ0FBQztJQUVEOztPQUVHO0lBQ0gsa0NBQWtDO1FBTWhDLE1BQU0sY0FBYyxHQUFHLElBQUksQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1FBRWhELElBQUksSUFBSSxDQUFDLHNCQUFzQixFQUFFLEVBQUUsQ0FBQztZQUNsQyxPQUFPO2dCQUNMLFFBQVEsRUFBRSxJQUFJO2dCQUNkLFVBQVUsRUFBRSxDQUFDLEtBQUssRUFBRSxTQUFTLEVBQUUsT0FBTyxFQUFFLE1BQU0sQ0FBQztnQkFDL0MsUUFBUSxFQUFFLGFBQWE7Z0JBQ3ZCLFVBQVUsRUFBRSxTQUFTO2FBQ3RCLENBQUM7UUFDSixDQUFDO1FBRUQsSUFBSSxJQUFJLENBQUMseUJBQXlCLEVBQUUsRUFBRSxDQUFDO1lBQ3JDLE9BQU87Z0JBQ0wsUUFBUSxFQUFFLElBQUk7Z0JBQ2QsVUFBVSxFQUFFLENBQUMsS0FBSyxFQUFFLFNBQVMsQ0FBQztnQkFDOUIsUUFBUSxFQUFFLGFBQWE7Z0JBQ3ZCLFVBQVUsRUFBRSxXQUFXO2FBQ3hCLENBQUM7UUFDSixDQUFDO1FBRUQsSUFBSSxjQUFjLEtBQUssV0FBVyxFQUFFLENBQUM7WUFDbkMsT0FBTztnQkFDTCxRQUFRLEVBQUUsSUFBSTtnQkFDZCxVQUFVLEVBQUUsQ0FBQyxLQUFLLEVBQUUsU0FBUyxFQUFFLE9BQU8sQ0FBQztnQkFDdkMsUUFBUSxFQUFFLFlBQVk7Z0JBQ3RCLFVBQVUsRUFBRSxXQUFXO2FBQ3hCLENBQUM7UUFDSixDQUFDO1FBRUQsT0FBTztZQUNMLFFBQVEsRUFBRSxLQUFLO1lBQ2YsVUFBVSxFQUFFLEVBQUU7WUFDZCxRQUFRLEVBQUUsWUFBWTtZQUN0QixVQUFVLEVBQUUsTUFBTTtTQUNuQixDQUFDO0lBQ0osQ0FBQztJQUVEOztPQUVHO0lBQ0ksTUFBTTtRQUNYLE9BQU87WUFDTCxTQUFTLEVBQUUsNEJBQTRCO1lBQ3ZDLE9BQU8sRUFBRSxJQUFJLENBQUMsT0FBTyxDQUFDLFFBQVEsRUFBRTtZQUNoQyxXQUFXLEVBQUUsSUFBSSxDQUFDLFdBQVcsQ0FBQyxRQUFRLEVBQUU7WUFDeEMsVUFBVSxFQUFFLElBQUksQ0FBQyxVQUFVLENBQUMsV0FBVyxFQUFFO1lBQ3pDLE9BQU8sRUFBRSxJQUFJLENBQUMsT0FBTztZQUNyQixZQUFZLEVBQUUsSUFBSSxDQUFDLFlBQVk7WUFDL0IsU0FBUyxFQUFFLElBQUksQ0FBQyxVQUFVLENBQUMsV0FBVyxFQUFFO1lBQ3hDLElBQUksRUFBRTtnQkFDSixlQUFlLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxlQUFlO2dCQUMvQyxTQUFTLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxTQUFTO2dCQUNuQyxTQUFTLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxTQUFTO2dCQUNuQyxNQUFNLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxNQUFNO2dCQUM3QixTQUFTLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxTQUFTO2dCQUNuQyxPQUFPLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxPQUFPO2dCQUMvQixTQUFTLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxTQUFTO2FBQ3BDO1lBQ0QsUUFBUSxFQUFFO2dCQUNSLGNBQWMsRUFBRSxJQUFJLENBQUMsaUJBQWlCLEVBQUU7Z0JBQ3hDLGFBQWEsRUFBRSxJQUFJLENBQUMsbUJBQW1CLEVBQUU7Z0JBQ3pDLFlBQVksRUFBRSxJQUFJLENBQUMsa0JBQWtCLEVBQUU7Z0JBQ3ZDLFNBQVMsRUFBRSxJQUFJLENBQUMsc0JBQXNCLEVBQUU7Z0JBQ3hDLFlBQVksRUFBRSxJQUFJLENBQUMseUJBQXlCLEVBQUU7Z0JBQzlDLFdBQVcsRUFBRSxJQUFJLENBQUMsd0JBQXdCLEVBQUU7Z0JBQzVDLG9CQUFvQixFQUFFLElBQUksQ0FBQyx1QkFBdUIsRUFBRTtnQkFDcEQscUJBQXFCLEVBQUUsSUFBSSxDQUFDLHdCQUF3QixFQUFFO2dCQUN0RCxnQkFBZ0IsRUFBRSxJQUFJLENBQUMsMkJBQTJCLEVBQUU7Z0JBQ3BELGVBQWUsRUFBRSxJQUFJLENBQUMsa0JBQWtCLEVBQUU7Z0JBQzFDLHNCQUFzQixFQUFFLElBQUksQ0FBQyxrQ0FBa0MsRUFBRTthQUNsRTtTQUNGLENBQUM7SUFDSixDQUFDO0lBRUQ7O09BRUc7SUFDSCxNQUFNLENBQUMsUUFBUSxDQUFDLElBQXlCO1FBQ3ZDLE9BQU8sSUFBSSwrQkFBK0IsQ0FDeEMsOEJBQWMsQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxFQUMzQyxJQUFJLENBQUMsSUFBSSxFQUNUO1lBQ0UsT0FBTyxFQUFFLDhCQUFjLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUM7WUFDaEQsVUFBVSxFQUFFLElBQUksSUFBSSxDQUFDLElBQUksQ0FBQyxVQUFVLENBQUM7WUFDckMsT0FBTyxFQUFFLElBQUksQ0FBQyxPQUFPO1NBQ3RCLENBQ0YsQ0FBQztJQUNKLENBQUM7SUFFRDs7T0FFRztJQUNILGNBQWM7UUFDWixNQUFNLFVBQVUsR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLFNBQVMsQ0FBQyxPQUFPLENBQUMsSUFBSSxFQUFFLEdBQUcsQ0FBQyxDQUFDO1FBQy9ELE1BQU0sYUFBYSxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsU0FBUyxDQUFDLE9BQU8sQ0FBQyxJQUFJLEVBQUUsR0FBRyxDQUFDLENBQUM7UUFFbEUsT0FBTyxzQ0FBc0MsYUFBYSxTQUFTLFVBQVUsTUFBTSxJQUFJLENBQUMsU0FBUyxDQUFDLE1BQU0sRUFBRSxDQUFDO0lBQzdHLENBQUM7SUFFRDs7T0FFRztJQUNILGVBQWU7UUFRYixPQUFPO1lBQ0wsU0FBUyxFQUFFLDRCQUE0QjtZQUN2QyxlQUFlLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxlQUFlO1lBQy9DLGdCQUFnQixFQUFFLEdBQUcsSUFBSSxDQUFDLFNBQVMsQ0FBQyxTQUFTLE9BQU8sSUFBSSxDQUFDLFNBQVMsQ0FBQyxTQUFTLEVBQUU7WUFDOUUsY0FBYyxFQUFFLElBQUksQ0FBQyxpQkFBaUIsRUFBRTtZQUN4QyxNQUFNLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxNQUFNO1lBQzdCLFNBQVMsRUFBRSxJQUFJLENBQUMsVUFBVSxDQUFDLFdBQVcsRUFBRTtTQUN6QyxDQUFDO0lBQ0osQ0FBQztDQUNGO0FBaGRELDBFQWdkQyIsIm5hbWVzIjpbXSwic291cmNlcyI6WyJDOlxcVXNlcnNcXEx1a2FcXHNlbnRpbmVsXFxiYWNrZW5kXFxzcmNcXGNvcmVcXHNlY3VyaXR5XFxkb21haW5cXGV2ZW50c1xcdnVsbmVyYWJpbGl0eS1zdGF0dXMtY2hhbmdlZC5ldmVudC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBCYXNlRG9tYWluRXZlbnQsIFVuaXF1ZUVudGl0eUlkIH0gZnJvbSAnLi4vLi4vLi4vLi4vc2hhcmVkLWtlcm5lbCc7XHJcbmltcG9ydCB7IFZ1bG5lcmFiaWxpdHlTdGF0dXMgfSBmcm9tICcuLi9lbnRpdGllcy92dWxuZXJhYmlsaXR5L3Z1bG5lcmFiaWxpdHkuZW50aXR5JztcclxuXHJcbi8qKlxyXG4gKiBWdWxuZXJhYmlsaXR5IFN0YXR1cyBDaGFuZ2VkIEV2ZW50IERhdGFcclxuICovXHJcbmV4cG9ydCBpbnRlcmZhY2UgVnVsbmVyYWJpbGl0eVN0YXR1c0NoYW5nZWRFdmVudERhdGEge1xyXG4gIC8qKiBJRCBvZiB0aGUgdnVsbmVyYWJpbGl0eSAqL1xyXG4gIHZ1bG5lcmFiaWxpdHlJZDogc3RyaW5nO1xyXG4gIC8qKiBQcmV2aW91cyBzdGF0dXMgKi9cclxuICBvbGRTdGF0dXM6IFZ1bG5lcmFiaWxpdHlTdGF0dXM7XHJcbiAgLyoqIE5ldyBzdGF0dXMgKi9cclxuICBuZXdTdGF0dXM6IFZ1bG5lcmFiaWxpdHlTdGF0dXM7XHJcbiAgLyoqIFJlYXNvbiBmb3Igc3RhdHVzIGNoYW5nZSAqL1xyXG4gIHJlYXNvbjogc3RyaW5nO1xyXG4gIC8qKiBUaW1lc3RhbXAgb2YgdGhlIGNoYW5nZSAqL1xyXG4gIHRpbWVzdGFtcDogc3RyaW5nO1xyXG4gIC8qKiBVc2VyIHdobyBtYWRlIHRoZSBjaGFuZ2UgKG9wdGlvbmFsKSAqL1xyXG4gIGNoYW5nZWRCeT86IHN0cmluZztcclxuICAvKiogQWRkaXRpb25hbCBjb250ZXh0IChvcHRpb25hbCkgKi9cclxuICBjb250ZXh0PzogUmVjb3JkPHN0cmluZywgYW55PjtcclxufVxyXG5cclxuLyoqXHJcbiAqIFZ1bG5lcmFiaWxpdHkgU3RhdHVzIENoYW5nZWQgRG9tYWluIEV2ZW50XHJcbiAqIFxyXG4gKiBQdWJsaXNoZWQgd2hlbiBhIHZ1bG5lcmFiaWxpdHkncyBzdGF0dXMgY2hhbmdlcyAoZS5nLiwgZnJvbSBkaXNjb3ZlcmVkIHRvIHRyaWFnZWQsXHJcbiAqIGZyb20gaW5fcHJvZ3Jlc3MgdG8gcmVtZWRpYXRlZCwgZXRjLikuXHJcbiAqIFxyXG4gKiBUaGlzIGV2ZW50IGVuYWJsZXM6XHJcbiAqIC0gV29ya2Zsb3cgYXV0b21hdGlvbiBiYXNlZCBvbiBzdGF0dXMgdHJhbnNpdGlvbnNcclxuICogLSBBdWRpdCB0cmFpbCBmb3IgdnVsbmVyYWJpbGl0eSBsaWZlY3ljbGVcclxuICogLSBOb3RpZmljYXRpb24gdHJpZ2dlcnMgZm9yIHN0YWtlaG9sZGVyc1xyXG4gKiAtIE1ldHJpY3MgY29sbGVjdGlvbiBmb3IgcmVtZWRpYXRpb24gdGltZWxpbmVzXHJcbiAqIC0gSW50ZWdyYXRpb24gd2l0aCBleHRlcm5hbCBzeXN0ZW1zIChJVFNNLCBldGMuKVxyXG4gKi9cclxuZXhwb3J0IGNsYXNzIFZ1bG5lcmFiaWxpdHlTdGF0dXNDaGFuZ2VkRXZlbnQgZXh0ZW5kcyBCYXNlRG9tYWluRXZlbnQ8VnVsbmVyYWJpbGl0eVN0YXR1c0NoYW5nZWRFdmVudERhdGE+IHtcclxuICBjb25zdHJ1Y3RvcihcclxuICAgIGFnZ3JlZ2F0ZUlkOiBVbmlxdWVFbnRpdHlJZCxcclxuICAgIGV2ZW50RGF0YTogVnVsbmVyYWJpbGl0eVN0YXR1c0NoYW5nZWRFdmVudERhdGEsXHJcbiAgICBvcHRpb25zPzoge1xyXG4gICAgICBldmVudElkPzogVW5pcXVlRW50aXR5SWQ7XHJcbiAgICAgIG9jY3VycmVkT24/OiBEYXRlO1xyXG4gICAgICB2ZXJzaW9uPzogbnVtYmVyO1xyXG4gICAgfVxyXG4gICkge1xyXG4gICAgc3VwZXIoYWdncmVnYXRlSWQsIGV2ZW50RGF0YSwgb3B0aW9ucyk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgdGhlIHZ1bG5lcmFiaWxpdHkgSURcclxuICAgKi9cclxuICBnZXQgdnVsbmVyYWJpbGl0eUlkKCk6IHN0cmluZyB7XHJcbiAgICByZXR1cm4gdGhpcy5ldmVudERhdGEudnVsbmVyYWJpbGl0eUlkO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IHRoZSBvbGQgc3RhdHVzXHJcbiAgICovXHJcbiAgZ2V0IG9sZFN0YXR1cygpOiBWdWxuZXJhYmlsaXR5U3RhdHVzIHtcclxuICAgIHJldHVybiB0aGlzLmV2ZW50RGF0YS5vbGRTdGF0dXM7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgdGhlIG5ldyBzdGF0dXNcclxuICAgKi9cclxuICBnZXQgbmV3U3RhdHVzKCk6IFZ1bG5lcmFiaWxpdHlTdGF0dXMge1xyXG4gICAgcmV0dXJuIHRoaXMuZXZlbnREYXRhLm5ld1N0YXR1cztcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCB0aGUgcmVhc29uIGZvciB0aGUgY2hhbmdlXHJcbiAgICovXHJcbiAgZ2V0IHJlYXNvbigpOiBzdHJpbmcge1xyXG4gICAgcmV0dXJuIHRoaXMuZXZlbnREYXRhLnJlYXNvbjtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCB3aG8gbWFkZSB0aGUgY2hhbmdlXHJcbiAgICovXHJcbiAgZ2V0IGNoYW5nZWRCeSgpOiBzdHJpbmcgfCB1bmRlZmluZWQge1xyXG4gICAgcmV0dXJuIHRoaXMuZXZlbnREYXRhLmNoYW5nZWRCeTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCBhZGRpdGlvbmFsIGNvbnRleHRcclxuICAgKi9cclxuICBnZXQgY29udGV4dCgpOiBSZWNvcmQ8c3RyaW5nLCBhbnk+IHwgdW5kZWZpbmVkIHtcclxuICAgIHJldHVybiB0aGlzLmV2ZW50RGF0YS5jb250ZXh0O1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2hlY2sgaWYgdGhpcyBpcyBhIHN0YXR1cyBwcm9ncmVzc2lvbiAoZm9yd2FyZCBtb3ZlbWVudCBpbiBsaWZlY3ljbGUpXHJcbiAgICovXHJcbiAgaXNTdGF0dXNQcm9ncmVzc2lvbigpOiBib29sZWFuIHtcclxuICAgIGNvbnN0IHN0YXR1c09yZGVyOiBSZWNvcmQ8VnVsbmVyYWJpbGl0eVN0YXR1cywgbnVtYmVyPiA9IHtcclxuICAgICAgW1Z1bG5lcmFiaWxpdHlTdGF0dXMuRElTQ09WRVJFRF06IDEsXHJcbiAgICAgIFtWdWxuZXJhYmlsaXR5U3RhdHVzLkNPTkZJUk1FRF06IDIsXHJcbiAgICAgIFtWdWxuZXJhYmlsaXR5U3RhdHVzLlRSSUFHRURdOiAzLFxyXG4gICAgICBbVnVsbmVyYWJpbGl0eVN0YXR1cy5JTl9QUk9HUkVTU106IDQsXHJcbiAgICAgIFtWdWxuZXJhYmlsaXR5U3RhdHVzLlJFTUVESUFURURdOiA1LFxyXG4gICAgICBbVnVsbmVyYWJpbGl0eVN0YXR1cy5WRVJJRklFRF06IDYsXHJcbiAgICAgIFtWdWxuZXJhYmlsaXR5U3RhdHVzLkNMT1NFRF06IDcsXHJcbiAgICAgIFtWdWxuZXJhYmlsaXR5U3RhdHVzLkZBTFNFX1BPU0lUSVZFXTogMCxcclxuICAgICAgW1Z1bG5lcmFiaWxpdHlTdGF0dXMuQUNDRVBURURfUklTS106IDAsXHJcbiAgICB9O1xyXG5cclxuICAgIHJldHVybiBzdGF0dXNPcmRlclt0aGlzLmV2ZW50RGF0YS5uZXdTdGF0dXNdID4gc3RhdHVzT3JkZXJbdGhpcy5ldmVudERhdGEub2xkU3RhdHVzXTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENoZWNrIGlmIHRoaXMgaXMgYSBzdGF0dXMgcmVncmVzc2lvbiAoYmFja3dhcmQgbW92ZW1lbnQpXHJcbiAgICovXHJcbiAgaXNTdGF0dXNSZWdyZXNzaW9uKCk6IGJvb2xlYW4ge1xyXG4gICAgY29uc3Qgc3RhdHVzT3JkZXI6IFJlY29yZDxWdWxuZXJhYmlsaXR5U3RhdHVzLCBudW1iZXI+ID0ge1xyXG4gICAgICBbVnVsbmVyYWJpbGl0eVN0YXR1cy5ESVNDT1ZFUkVEXTogMSxcclxuICAgICAgW1Z1bG5lcmFiaWxpdHlTdGF0dXMuQ09ORklSTUVEXTogMixcclxuICAgICAgW1Z1bG5lcmFiaWxpdHlTdGF0dXMuVFJJQUdFRF06IDMsXHJcbiAgICAgIFtWdWxuZXJhYmlsaXR5U3RhdHVzLklOX1BST0dSRVNTXTogNCxcclxuICAgICAgW1Z1bG5lcmFiaWxpdHlTdGF0dXMuUkVNRURJQVRFRF06IDUsXHJcbiAgICAgIFtWdWxuZXJhYmlsaXR5U3RhdHVzLlZFUklGSUVEXTogNixcclxuICAgICAgW1Z1bG5lcmFiaWxpdHlTdGF0dXMuQ0xPU0VEXTogNyxcclxuICAgICAgW1Z1bG5lcmFiaWxpdHlTdGF0dXMuRkFMU0VfUE9TSVRJVkVdOiAwLFxyXG4gICAgICBbVnVsbmVyYWJpbGl0eVN0YXR1cy5BQ0NFUFRFRF9SSVNLXTogMCxcclxuICAgIH07XHJcblxyXG4gICAgcmV0dXJuIHN0YXR1c09yZGVyW3RoaXMuZXZlbnREYXRhLm5ld1N0YXR1c10gPCBzdGF0dXNPcmRlclt0aGlzLmV2ZW50RGF0YS5vbGRTdGF0dXNdICYmXHJcbiAgICAgICAgICAgc3RhdHVzT3JkZXJbdGhpcy5ldmVudERhdGEubmV3U3RhdHVzXSA+IDA7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDaGVjayBpZiB2dWxuZXJhYmlsaXR5IHdhcyBjbG9zZWQgKHJlbWVkaWF0ZWQsIHZlcmlmaWVkLCBvciBjbG9zZWQpXHJcbiAgICovXHJcbiAgd2FzVnVsbmVyYWJpbGl0eUNsb3NlZCgpOiBib29sZWFuIHtcclxuICAgIHJldHVybiBbXHJcbiAgICAgIFZ1bG5lcmFiaWxpdHlTdGF0dXMuUkVNRURJQVRFRCxcclxuICAgICAgVnVsbmVyYWJpbGl0eVN0YXR1cy5WRVJJRklFRCxcclxuICAgICAgVnVsbmVyYWJpbGl0eVN0YXR1cy5DTE9TRUQsXHJcbiAgICBdLmluY2x1ZGVzKHRoaXMuZXZlbnREYXRhLm5ld1N0YXR1cyk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDaGVjayBpZiB2dWxuZXJhYmlsaXR5IHdhcyBkaXNtaXNzZWQgKGZhbHNlIHBvc2l0aXZlIG9yIGFjY2VwdGVkIHJpc2spXHJcbiAgICovXHJcbiAgd2FzVnVsbmVyYWJpbGl0eURpc21pc3NlZCgpOiBib29sZWFuIHtcclxuICAgIHJldHVybiBbXHJcbiAgICAgIFZ1bG5lcmFiaWxpdHlTdGF0dXMuRkFMU0VfUE9TSVRJVkUsXHJcbiAgICAgIFZ1bG5lcmFiaWxpdHlTdGF0dXMuQUNDRVBURURfUklTSyxcclxuICAgIF0uaW5jbHVkZXModGhpcy5ldmVudERhdGEubmV3U3RhdHVzKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENoZWNrIGlmIHZ1bG5lcmFiaWxpdHkgd2FzIHJlb3BlbmVkXHJcbiAgICovXHJcbiAgd2FzVnVsbmVyYWJpbGl0eVJlb3BlbmVkKCk6IGJvb2xlYW4ge1xyXG4gICAgY29uc3QgY2xvc2VkU3RhdHVzZXMgPSBbXHJcbiAgICAgIFZ1bG5lcmFiaWxpdHlTdGF0dXMuUkVNRURJQVRFRCxcclxuICAgICAgVnVsbmVyYWJpbGl0eVN0YXR1cy5WRVJJRklFRCxcclxuICAgICAgVnVsbmVyYWJpbGl0eVN0YXR1cy5DTE9TRUQsXHJcbiAgICAgIFZ1bG5lcmFiaWxpdHlTdGF0dXMuRkFMU0VfUE9TSVRJVkUsXHJcbiAgICAgIFZ1bG5lcmFiaWxpdHlTdGF0dXMuQUNDRVBURURfUklTSyxcclxuICAgIF07XHJcblxyXG4gICAgY29uc3QgYWN0aXZlU3RhdHVzZXMgPSBbXHJcbiAgICAgIFZ1bG5lcmFiaWxpdHlTdGF0dXMuRElTQ09WRVJFRCxcclxuICAgICAgVnVsbmVyYWJpbGl0eVN0YXR1cy5DT05GSVJNRUQsXHJcbiAgICAgIFZ1bG5lcmFiaWxpdHlTdGF0dXMuVFJJQUdFRCxcclxuICAgICAgVnVsbmVyYWJpbGl0eVN0YXR1cy5JTl9QUk9HUkVTUyxcclxuICAgIF07XHJcblxyXG4gICAgcmV0dXJuIGNsb3NlZFN0YXR1c2VzLmluY2x1ZGVzKHRoaXMuZXZlbnREYXRhLm9sZFN0YXR1cykgJiZcclxuICAgICAgICAgICBhY3RpdmVTdGF0dXNlcy5pbmNsdWRlcyh0aGlzLmV2ZW50RGF0YS5uZXdTdGF0dXMpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IHN0YXR1cyB0cmFuc2l0aW9uIHR5cGVcclxuICAgKi9cclxuICBnZXRUcmFuc2l0aW9uVHlwZSgpOiAncHJvZ3Jlc3Npb24nIHwgJ3JlZ3Jlc3Npb24nIHwgJ2Nsb3N1cmUnIHwgJ2Rpc21pc3NhbCcgfCAncmVvcGVuaW5nJyB8ICdsYXRlcmFsJyB7XHJcbiAgICBpZiAodGhpcy53YXNWdWxuZXJhYmlsaXR5Q2xvc2VkKCkpIHJldHVybiAnY2xvc3VyZSc7XHJcbiAgICBpZiAodGhpcy53YXNWdWxuZXJhYmlsaXR5RGlzbWlzc2VkKCkpIHJldHVybiAnZGlzbWlzc2FsJztcclxuICAgIGlmICh0aGlzLndhc1Z1bG5lcmFiaWxpdHlSZW9wZW5lZCgpKSByZXR1cm4gJ3Jlb3BlbmluZyc7XHJcbiAgICBpZiAodGhpcy5pc1N0YXR1c1Byb2dyZXNzaW9uKCkpIHJldHVybiAncHJvZ3Jlc3Npb24nO1xyXG4gICAgaWYgKHRoaXMuaXNTdGF0dXNSZWdyZXNzaW9uKCkpIHJldHVybiAncmVncmVzc2lvbic7XHJcbiAgICByZXR1cm4gJ2xhdGVyYWwnO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IG5vdGlmaWNhdGlvbiBwcmlvcml0eSBiYXNlZCBvbiBzdGF0dXMgY2hhbmdlXHJcbiAgICovXHJcbiAgZ2V0Tm90aWZpY2F0aW9uUHJpb3JpdHkoKTogJ2xvdycgfCAnbWVkaXVtJyB8ICdoaWdoJyB8ICdjcml0aWNhbCcge1xyXG4gICAgY29uc3QgdHJhbnNpdGlvblR5cGUgPSB0aGlzLmdldFRyYW5zaXRpb25UeXBlKCk7XHJcbiAgICBcclxuICAgIHN3aXRjaCAodHJhbnNpdGlvblR5cGUpIHtcclxuICAgICAgY2FzZSAnY2xvc3VyZSc6XHJcbiAgICAgICAgcmV0dXJuICdtZWRpdW0nOyAvLyBHb29kIG5ld3MsIGJ1dCBub3QgdXJnZW50XHJcbiAgICAgIGNhc2UgJ2Rpc21pc3NhbCc6XHJcbiAgICAgICAgcmV0dXJuICdsb3cnOyAvLyBJbmZvcm1hdGlvbmFsXHJcbiAgICAgIGNhc2UgJ3Jlb3BlbmluZyc6XHJcbiAgICAgICAgcmV0dXJuICdoaWdoJzsgLy8gUmVxdWlyZXMgYXR0ZW50aW9uXHJcbiAgICAgIGNhc2UgJ3JlZ3Jlc3Npb24nOlxyXG4gICAgICAgIHJldHVybiAnaGlnaCc7IC8vIFByb2JsZW0gdGhhdCBuZWVkcyBpbnZlc3RpZ2F0aW9uXHJcbiAgICAgIGNhc2UgJ3Byb2dyZXNzaW9uJzpcclxuICAgICAgICByZXR1cm4gJ2xvdyc7IC8vIE5vcm1hbCB3b3JrZmxvdyBwcm9ncmVzc2lvblxyXG4gICAgICBkZWZhdWx0OlxyXG4gICAgICAgIHJldHVybiAnbG93JztcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCByZXF1aXJlZCBub3RpZmljYXRpb25zIGJhc2VkIG9uIHN0YXR1cyBjaGFuZ2VcclxuICAgKi9cclxuICBnZXRSZXF1aXJlZE5vdGlmaWNhdGlvbnMoKToge1xyXG4gICAgY2hhbm5lbHM6IHN0cmluZ1tdO1xyXG4gICAgcmVjaXBpZW50czogc3RyaW5nW107XHJcbiAgICB1cmdlbmN5OiAnaW1tZWRpYXRlJyB8ICd3aXRoaW5faG91cicgfCAnd2l0aGluX2RheScgfCAncm91dGluZSc7XHJcbiAgfSB7XHJcbiAgICBjb25zdCB0cmFuc2l0aW9uVHlwZSA9IHRoaXMuZ2V0VHJhbnNpdGlvblR5cGUoKTtcclxuICAgIGNvbnN0IHByaW9yaXR5ID0gdGhpcy5nZXROb3RpZmljYXRpb25Qcmlvcml0eSgpO1xyXG4gICAgXHJcbiAgICBjb25zdCBiYXNlQ2hhbm5lbHMgPSBbJ2VtYWlsJywgJ3dlYmhvb2snXTtcclxuICAgIGNvbnN0IGJhc2VSZWNpcGllbnRzID0gWyd2dWxuZXJhYmlsaXR5X3RlYW0nXTtcclxuICAgIFxyXG4gICAgc3dpdGNoICh0cmFuc2l0aW9uVHlwZSkge1xyXG4gICAgICBjYXNlICdjbG9zdXJlJzpcclxuICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgY2hhbm5lbHM6IFsuLi5iYXNlQ2hhbm5lbHMsICdkYXNoYm9hcmQnXSxcclxuICAgICAgICAgIHJlY2lwaWVudHM6IFsuLi5iYXNlUmVjaXBpZW50cywgJ3N0YWtlaG9sZGVycyddLFxyXG4gICAgICAgICAgdXJnZW5jeTogJ3dpdGhpbl9kYXknLFxyXG4gICAgICAgIH07XHJcbiAgICAgIFxyXG4gICAgICBjYXNlICdkaXNtaXNzYWwnOlxyXG4gICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICBjaGFubmVsczogYmFzZUNoYW5uZWxzLFxyXG4gICAgICAgICAgcmVjaXBpZW50czogYmFzZVJlY2lwaWVudHMsXHJcbiAgICAgICAgICB1cmdlbmN5OiAncm91dGluZScsXHJcbiAgICAgICAgfTtcclxuICAgICAgXHJcbiAgICAgIGNhc2UgJ3Jlb3BlbmluZyc6XHJcbiAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgIGNoYW5uZWxzOiBbLi4uYmFzZUNoYW5uZWxzLCAnc2xhY2snLCAnZGFzaGJvYXJkJ10sXHJcbiAgICAgICAgICByZWNpcGllbnRzOiBbLi4uYmFzZVJlY2lwaWVudHMsICdzZWN1cml0eV90ZWFtJywgJ21hbmFnZW1lbnQnXSxcclxuICAgICAgICAgIHVyZ2VuY3k6ICd3aXRoaW5faG91cicsXHJcbiAgICAgICAgfTtcclxuICAgICAgXHJcbiAgICAgIGNhc2UgJ3JlZ3Jlc3Npb24nOlxyXG4gICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICBjaGFubmVsczogWy4uLmJhc2VDaGFubmVscywgJ3NsYWNrJ10sXHJcbiAgICAgICAgICByZWNpcGllbnRzOiBbLi4uYmFzZVJlY2lwaWVudHMsICdzZWN1cml0eV90ZWFtJ10sXHJcbiAgICAgICAgICB1cmdlbmN5OiAnd2l0aGluX2hvdXInLFxyXG4gICAgICAgIH07XHJcbiAgICAgIFxyXG4gICAgICBkZWZhdWx0OlxyXG4gICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICBjaGFubmVsczogYmFzZUNoYW5uZWxzLFxyXG4gICAgICAgICAgcmVjaXBpZW50czogYmFzZVJlY2lwaWVudHMsXHJcbiAgICAgICAgICB1cmdlbmN5OiAncm91dGluZScsXHJcbiAgICAgICAgfTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCB3b3JrZmxvdyBhY3Rpb25zIHRyaWdnZXJlZCBieSB0aGlzIHN0YXR1cyBjaGFuZ2VcclxuICAgKi9cclxuICBnZXRUcmlnZ2VyZWRXb3JrZmxvd0FjdGlvbnMoKTogc3RyaW5nW10ge1xyXG4gICAgY29uc3QgYWN0aW9uczogc3RyaW5nW10gPSBbXTtcclxuICAgIGNvbnN0IHRyYW5zaXRpb25UeXBlID0gdGhpcy5nZXRUcmFuc2l0aW9uVHlwZSgpO1xyXG4gICAgXHJcbiAgICBzd2l0Y2ggKHRoaXMuZXZlbnREYXRhLm5ld1N0YXR1cykge1xyXG4gICAgICBjYXNlIFZ1bG5lcmFiaWxpdHlTdGF0dXMuQ09ORklSTUVEOlxyXG4gICAgICAgIGFjdGlvbnMucHVzaCgnYXNzaWduX3RvX3NlY3VyaXR5X3RlYW0nLCAnY3JlYXRlX3Jpc2tfYXNzZXNzbWVudCcpO1xyXG4gICAgICAgIGJyZWFrO1xyXG4gICAgICBcclxuICAgICAgY2FzZSBWdWxuZXJhYmlsaXR5U3RhdHVzLlRSSUFHRUQ6XHJcbiAgICAgICAgYWN0aW9ucy5wdXNoKCdhc3NpZ25fcmVtZWRpYXRpb25fb3duZXInLCAnc2V0X3JlbWVkaWF0aW9uX3RpbWVsaW5lJyk7XHJcbiAgICAgICAgYnJlYWs7XHJcbiAgICAgIFxyXG4gICAgICBjYXNlIFZ1bG5lcmFiaWxpdHlTdGF0dXMuSU5fUFJPR1JFU1M6XHJcbiAgICAgICAgYWN0aW9ucy5wdXNoKCdzdGFydF9yZW1lZGlhdGlvbl90cmFja2luZycsICdub3RpZnlfc3Rha2Vob2xkZXJzJyk7XHJcbiAgICAgICAgYnJlYWs7XHJcbiAgICAgIFxyXG4gICAgICBjYXNlIFZ1bG5lcmFiaWxpdHlTdGF0dXMuUkVNRURJQVRFRDpcclxuICAgICAgICBhY3Rpb25zLnB1c2goJ3NjaGVkdWxlX3ZlcmlmaWNhdGlvbicsICd1cGRhdGVfYXNzZXRfaW52ZW50b3J5Jyk7XHJcbiAgICAgICAgYnJlYWs7XHJcbiAgICAgIFxyXG4gICAgICBjYXNlIFZ1bG5lcmFiaWxpdHlTdGF0dXMuVkVSSUZJRUQ6XHJcbiAgICAgICAgYWN0aW9ucy5wdXNoKCdjbG9zZV92dWxuZXJhYmlsaXR5JywgJ3VwZGF0ZV9tZXRyaWNzJyk7XHJcbiAgICAgICAgYnJlYWs7XHJcbiAgICAgIFxyXG4gICAgICBjYXNlIFZ1bG5lcmFiaWxpdHlTdGF0dXMuQ0xPU0VEOlxyXG4gICAgICAgIGFjdGlvbnMucHVzaCgnYXJjaGl2ZV92dWxuZXJhYmlsaXR5JywgJ2dlbmVyYXRlX2Nsb3N1cmVfcmVwb3J0Jyk7XHJcbiAgICAgICAgYnJlYWs7XHJcbiAgICAgIFxyXG4gICAgICBjYXNlIFZ1bG5lcmFiaWxpdHlTdGF0dXMuRkFMU0VfUE9TSVRJVkU6XHJcbiAgICAgICAgYWN0aW9ucy5wdXNoKCd1cGRhdGVfc2Nhbm5lcl9ydWxlcycsICdkb2N1bWVudF9mYWxzZV9wb3NpdGl2ZScpO1xyXG4gICAgICAgIGJyZWFrO1xyXG4gICAgICBcclxuICAgICAgY2FzZSBWdWxuZXJhYmlsaXR5U3RhdHVzLkFDQ0VQVEVEX1JJU0s6XHJcbiAgICAgICAgYWN0aW9ucy5wdXNoKCdkb2N1bWVudF9yaXNrX2FjY2VwdGFuY2UnLCAnc2NoZWR1bGVfcmlza19yZXZpZXcnKTtcclxuICAgICAgICBicmVhaztcclxuICAgIH1cclxuICAgIFxyXG4gICAgaWYgKHRyYW5zaXRpb25UeXBlID09PSAncmVvcGVuaW5nJykge1xyXG4gICAgICBhY3Rpb25zLnB1c2goJ2ludmVzdGlnYXRlX3Jlb3BlbmluZ19jYXVzZScsICdyZWFzc2Vzc192dWxuZXJhYmlsaXR5Jyk7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIHJldHVybiBhY3Rpb25zO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IG1ldHJpY3MgdG8gdXBkYXRlIGJhc2VkIG9uIHN0YXR1cyBjaGFuZ2VcclxuICAgKi9cclxuICBnZXRNZXRyaWNzVG9VcGRhdGUoKTogQXJyYXk8e1xyXG4gICAgbWV0cmljOiBzdHJpbmc7XHJcbiAgICBvcGVyYXRpb246ICdpbmNyZW1lbnQnIHwgJ2RlY3JlbWVudCcgfCAnc2V0JyB8ICd0aW1pbmcnO1xyXG4gICAgdmFsdWU/OiBudW1iZXI7XHJcbiAgICB0YWdzPzogUmVjb3JkPHN0cmluZywgc3RyaW5nPjtcclxuICB9PiB7XHJcbiAgICBjb25zdCBtZXRyaWNzOiBBcnJheTx7XHJcbiAgICAgIG1ldHJpYzogc3RyaW5nO1xyXG4gICAgICBvcGVyYXRpb246ICdpbmNyZW1lbnQnIHwgJ2RlY3JlbWVudCcgfCAnc2V0JyB8ICd0aW1pbmcnO1xyXG4gICAgICB2YWx1ZT86IG51bWJlcjtcclxuICAgICAgdGFncz86IFJlY29yZDxzdHJpbmcsIHN0cmluZz47XHJcbiAgICB9PiA9IFtdO1xyXG4gICAgXHJcbiAgICBjb25zdCBiYXNlVGFncyA9IHtcclxuICAgICAgb2xkX3N0YXR1czogdGhpcy5ldmVudERhdGEub2xkU3RhdHVzLFxyXG4gICAgICBuZXdfc3RhdHVzOiB0aGlzLmV2ZW50RGF0YS5uZXdTdGF0dXMsXHJcbiAgICAgIHRyYW5zaXRpb25fdHlwZTogdGhpcy5nZXRUcmFuc2l0aW9uVHlwZSgpLFxyXG4gICAgfTtcclxuICAgIFxyXG4gICAgLy8gU3RhdHVzIHRyYW5zaXRpb24gY291bnRlclxyXG4gICAgbWV0cmljcy5wdXNoKHtcclxuICAgICAgbWV0cmljOiAndnVsbmVyYWJpbGl0eV9zdGF0dXNfdHJhbnNpdGlvbnMnLFxyXG4gICAgICBvcGVyYXRpb246ICdpbmNyZW1lbnQnLFxyXG4gICAgICB0YWdzOiBiYXNlVGFncyxcclxuICAgIH0pO1xyXG4gICAgXHJcbiAgICAvLyBTdGF0dXMtc3BlY2lmaWMgbWV0cmljc1xyXG4gICAgaWYgKHRoaXMud2FzVnVsbmVyYWJpbGl0eUNsb3NlZCgpKSB7XHJcbiAgICAgIG1ldHJpY3MucHVzaCh7XHJcbiAgICAgICAgbWV0cmljOiAndnVsbmVyYWJpbGl0aWVzX2Nsb3NlZCcsXHJcbiAgICAgICAgb3BlcmF0aW9uOiAnaW5jcmVtZW50JyxcclxuICAgICAgICB0YWdzOiB7IHN0YXR1czogdGhpcy5ldmVudERhdGEubmV3U3RhdHVzIH0sXHJcbiAgICAgIH0pO1xyXG4gICAgICBcclxuICAgICAgLy8gQ2FsY3VsYXRlIHRpbWUgdG8gY2xvc3VyZSBpZiB3ZSBoYXZlIGRpc2NvdmVyeSB0aW1lXHJcbiAgICAgIG1ldHJpY3MucHVzaCh7XHJcbiAgICAgICAgbWV0cmljOiAndnVsbmVyYWJpbGl0eV9jbG9zdXJlX3RpbWUnLFxyXG4gICAgICAgIG9wZXJhdGlvbjogJ3RpbWluZycsXHJcbiAgICAgICAgdGFnczogeyBzdGF0dXM6IHRoaXMuZXZlbnREYXRhLm5ld1N0YXR1cyB9LFxyXG4gICAgICB9KTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgaWYgKHRoaXMud2FzVnVsbmVyYWJpbGl0eURpc21pc3NlZCgpKSB7XHJcbiAgICAgIG1ldHJpY3MucHVzaCh7XHJcbiAgICAgICAgbWV0cmljOiAndnVsbmVyYWJpbGl0aWVzX2Rpc21pc3NlZCcsXHJcbiAgICAgICAgb3BlcmF0aW9uOiAnaW5jcmVtZW50JyxcclxuICAgICAgICB0YWdzOiB7IHJlYXNvbjogdGhpcy5ldmVudERhdGEubmV3U3RhdHVzIH0sXHJcbiAgICAgIH0pO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICBpZiAodGhpcy53YXNWdWxuZXJhYmlsaXR5UmVvcGVuZWQoKSkge1xyXG4gICAgICBtZXRyaWNzLnB1c2goe1xyXG4gICAgICAgIG1ldHJpYzogJ3Z1bG5lcmFiaWxpdGllc19yZW9wZW5lZCcsXHJcbiAgICAgICAgb3BlcmF0aW9uOiAnaW5jcmVtZW50JyxcclxuICAgICAgICB0YWdzOiB7IGZyb21fc3RhdHVzOiB0aGlzLmV2ZW50RGF0YS5vbGRTdGF0dXMgfSxcclxuICAgICAgfSk7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIHJldHVybiBtZXRyaWNzO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IGNvbXBsaWFuY2UgcmVwb3J0aW5nIHJlcXVpcmVtZW50c1xyXG4gICAqL1xyXG4gIGdldENvbXBsaWFuY2VSZXBvcnRpbmdSZXF1aXJlbWVudHMoKToge1xyXG4gICAgcmVxdWlyZWQ6IGJvb2xlYW47XHJcbiAgICBmcmFtZXdvcmtzOiBzdHJpbmdbXTtcclxuICAgIGRlYWRsaW5lOiAnaW1tZWRpYXRlJyB8ICd3aXRoaW5fMjRoJyB8ICd3aXRoaW5fd2VlaycgfCAnbmV4dF9jeWNsZSc7XHJcbiAgICByZXBvcnRUeXBlOiAnc3RhdHVzX2NoYW5nZScgfCAnY2xvc3VyZScgfCAnZXhjZXB0aW9uJyB8ICdub25lJztcclxuICB9IHtcclxuICAgIGNvbnN0IHRyYW5zaXRpb25UeXBlID0gdGhpcy5nZXRUcmFuc2l0aW9uVHlwZSgpO1xyXG4gICAgXHJcbiAgICBpZiAodGhpcy53YXNWdWxuZXJhYmlsaXR5Q2xvc2VkKCkpIHtcclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICByZXF1aXJlZDogdHJ1ZSxcclxuICAgICAgICBmcmFtZXdvcmtzOiBbJ1NPWCcsICdQQ0lfRFNTJywgJ0hJUEFBJywgJ0dEUFInXSxcclxuICAgICAgICBkZWFkbGluZTogJ3dpdGhpbl93ZWVrJyxcclxuICAgICAgICByZXBvcnRUeXBlOiAnY2xvc3VyZScsXHJcbiAgICAgIH07XHJcbiAgICB9XHJcbiAgICBcclxuICAgIGlmICh0aGlzLndhc1Z1bG5lcmFiaWxpdHlEaXNtaXNzZWQoKSkge1xyXG4gICAgICByZXR1cm4ge1xyXG4gICAgICAgIHJlcXVpcmVkOiB0cnVlLFxyXG4gICAgICAgIGZyYW1ld29ya3M6IFsnU09YJywgJ1BDSV9EU1MnXSxcclxuICAgICAgICBkZWFkbGluZTogJ3dpdGhpbl93ZWVrJyxcclxuICAgICAgICByZXBvcnRUeXBlOiAnZXhjZXB0aW9uJyxcclxuICAgICAgfTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgaWYgKHRyYW5zaXRpb25UeXBlID09PSAncmVvcGVuaW5nJykge1xyXG4gICAgICByZXR1cm4ge1xyXG4gICAgICAgIHJlcXVpcmVkOiB0cnVlLFxyXG4gICAgICAgIGZyYW1ld29ya3M6IFsnU09YJywgJ1BDSV9EU1MnLCAnSElQQUEnXSxcclxuICAgICAgICBkZWFkbGluZTogJ3dpdGhpbl8yNGgnLFxyXG4gICAgICAgIHJlcG9ydFR5cGU6ICdleGNlcHRpb24nLFxyXG4gICAgICB9O1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICByZXF1aXJlZDogZmFsc2UsXHJcbiAgICAgIGZyYW1ld29ya3M6IFtdLFxyXG4gICAgICBkZWFkbGluZTogJ25leHRfY3ljbGUnLFxyXG4gICAgICByZXBvcnRUeXBlOiAnbm9uZScsXHJcbiAgICB9O1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ29udmVydCB0byBKU09OIHJlcHJlc2VudGF0aW9uXHJcbiAgICovXHJcbiAgcHVibGljIHRvSlNPTigpOiBSZWNvcmQ8c3RyaW5nLCBhbnk+IHtcclxuICAgIHJldHVybiB7XHJcbiAgICAgIGV2ZW50VHlwZTogJ1Z1bG5lcmFiaWxpdHlTdGF0dXNDaGFuZ2VkJyxcclxuICAgICAgZXZlbnRJZDogdGhpcy5ldmVudElkLnRvU3RyaW5nKCksXHJcbiAgICAgIGFnZ3JlZ2F0ZUlkOiB0aGlzLmFnZ3JlZ2F0ZUlkLnRvU3RyaW5nKCksXHJcbiAgICAgIG9jY3VycmVkT246IHRoaXMub2NjdXJyZWRPbi50b0lTT1N0cmluZygpLFxyXG4gICAgICB2ZXJzaW9uOiB0aGlzLnZlcnNpb24sXHJcbiAgICAgIGlzRGlzcGF0Y2hlZDogdGhpcy5pc0Rpc3BhdGNoZWQsXHJcbiAgICAgIHRpbWVzdGFtcDogdGhpcy5vY2N1cnJlZE9uLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgIGRhdGE6IHtcclxuICAgICAgICB2dWxuZXJhYmlsaXR5SWQ6IHRoaXMuZXZlbnREYXRhLnZ1bG5lcmFiaWxpdHlJZCxcclxuICAgICAgICBvbGRTdGF0dXM6IHRoaXMuZXZlbnREYXRhLm9sZFN0YXR1cyxcclxuICAgICAgICBuZXdTdGF0dXM6IHRoaXMuZXZlbnREYXRhLm5ld1N0YXR1cyxcclxuICAgICAgICByZWFzb246IHRoaXMuZXZlbnREYXRhLnJlYXNvbixcclxuICAgICAgICBjaGFuZ2VkQnk6IHRoaXMuZXZlbnREYXRhLmNoYW5nZWRCeSxcclxuICAgICAgICBjb250ZXh0OiB0aGlzLmV2ZW50RGF0YS5jb250ZXh0LFxyXG4gICAgICAgIHRpbWVzdGFtcDogdGhpcy5ldmVudERhdGEudGltZXN0YW1wLFxyXG4gICAgICB9LFxyXG4gICAgICBhbmFseXNpczoge1xyXG4gICAgICAgIHRyYW5zaXRpb25UeXBlOiB0aGlzLmdldFRyYW5zaXRpb25UeXBlKCksXHJcbiAgICAgICAgaXNQcm9ncmVzc2lvbjogdGhpcy5pc1N0YXR1c1Byb2dyZXNzaW9uKCksXHJcbiAgICAgICAgaXNSZWdyZXNzaW9uOiB0aGlzLmlzU3RhdHVzUmVncmVzc2lvbigpLFxyXG4gICAgICAgIHdhc0Nsb3NlZDogdGhpcy53YXNWdWxuZXJhYmlsaXR5Q2xvc2VkKCksXHJcbiAgICAgICAgd2FzRGlzbWlzc2VkOiB0aGlzLndhc1Z1bG5lcmFiaWxpdHlEaXNtaXNzZWQoKSxcclxuICAgICAgICB3YXNSZW9wZW5lZDogdGhpcy53YXNWdWxuZXJhYmlsaXR5UmVvcGVuZWQoKSxcclxuICAgICAgICBub3RpZmljYXRpb25Qcmlvcml0eTogdGhpcy5nZXROb3RpZmljYXRpb25Qcmlvcml0eSgpLFxyXG4gICAgICAgIHJlcXVpcmVkTm90aWZpY2F0aW9uczogdGhpcy5nZXRSZXF1aXJlZE5vdGlmaWNhdGlvbnMoKSxcclxuICAgICAgICB0cmlnZ2VyZWRBY3Rpb25zOiB0aGlzLmdldFRyaWdnZXJlZFdvcmtmbG93QWN0aW9ucygpLFxyXG4gICAgICAgIG1ldHJpY3NUb1VwZGF0ZTogdGhpcy5nZXRNZXRyaWNzVG9VcGRhdGUoKSxcclxuICAgICAgICBjb21wbGlhbmNlUmVxdWlyZW1lbnRzOiB0aGlzLmdldENvbXBsaWFuY2VSZXBvcnRpbmdSZXF1aXJlbWVudHMoKSxcclxuICAgICAgfSxcclxuICAgIH07XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDcmVhdGUgZnJvbSBKU09OIHJlcHJlc2VudGF0aW9uXHJcbiAgICovXHJcbiAgc3RhdGljIGZyb21KU09OKGpzb246IFJlY29yZDxzdHJpbmcsIGFueT4pOiBWdWxuZXJhYmlsaXR5U3RhdHVzQ2hhbmdlZEV2ZW50IHtcclxuICAgIHJldHVybiBuZXcgVnVsbmVyYWJpbGl0eVN0YXR1c0NoYW5nZWRFdmVudChcclxuICAgICAgVW5pcXVlRW50aXR5SWQuZnJvbVN0cmluZyhqc29uLmFnZ3JlZ2F0ZUlkKSxcclxuICAgICAganNvbi5kYXRhLFxyXG4gICAgICB7XHJcbiAgICAgICAgZXZlbnRJZDogVW5pcXVlRW50aXR5SWQuZnJvbVN0cmluZyhqc29uLmV2ZW50SWQpLFxyXG4gICAgICAgIG9jY3VycmVkT246IG5ldyBEYXRlKGpzb24ub2NjdXJyZWRPbiksXHJcbiAgICAgICAgdmVyc2lvbjoganNvbi52ZXJzaW9uLFxyXG4gICAgICB9XHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IGh1bWFuLXJlYWRhYmxlIGRlc2NyaXB0aW9uXHJcbiAgICovXHJcbiAgZ2V0RGVzY3JpcHRpb24oKTogc3RyaW5nIHtcclxuICAgIGNvbnN0IHN0YXR1c1RleHQgPSB0aGlzLmV2ZW50RGF0YS5uZXdTdGF0dXMucmVwbGFjZSgvXy9nLCAnICcpO1xyXG4gICAgY29uc3Qgb2xkU3RhdHVzVGV4dCA9IHRoaXMuZXZlbnREYXRhLm9sZFN0YXR1cy5yZXBsYWNlKC9fL2csICcgJyk7XHJcbiAgICBcclxuICAgIHJldHVybiBgVnVsbmVyYWJpbGl0eSBzdGF0dXMgY2hhbmdlZCBmcm9tIFwiJHtvbGRTdGF0dXNUZXh0fVwiIHRvIFwiJHtzdGF0dXNUZXh0fVwiOiAke3RoaXMuZXZlbnREYXRhLnJlYXNvbn1gO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IGV2ZW50IHN1bW1hcnkgZm9yIGxvZ2dpbmdcclxuICAgKi9cclxuICBnZXRFdmVudFN1bW1hcnkoKToge1xyXG4gICAgZXZlbnRUeXBlOiBzdHJpbmc7XHJcbiAgICB2dWxuZXJhYmlsaXR5SWQ6IHN0cmluZztcclxuICAgIHN0YXR1c1RyYW5zaXRpb246IHN0cmluZztcclxuICAgIHRyYW5zaXRpb25UeXBlOiBzdHJpbmc7XHJcbiAgICByZWFzb246IHN0cmluZztcclxuICAgIHRpbWVzdGFtcDogc3RyaW5nO1xyXG4gIH0ge1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAgZXZlbnRUeXBlOiAnVnVsbmVyYWJpbGl0eVN0YXR1c0NoYW5nZWQnLFxyXG4gICAgICB2dWxuZXJhYmlsaXR5SWQ6IHRoaXMuZXZlbnREYXRhLnZ1bG5lcmFiaWxpdHlJZCxcclxuICAgICAgc3RhdHVzVHJhbnNpdGlvbjogYCR7dGhpcy5ldmVudERhdGEub2xkU3RhdHVzfSAtPiAke3RoaXMuZXZlbnREYXRhLm5ld1N0YXR1c31gLFxyXG4gICAgICB0cmFuc2l0aW9uVHlwZTogdGhpcy5nZXRUcmFuc2l0aW9uVHlwZSgpLFxyXG4gICAgICByZWFzb246IHRoaXMuZXZlbnREYXRhLnJlYXNvbixcclxuICAgICAgdGltZXN0YW1wOiB0aGlzLm9jY3VycmVkT24udG9JU09TdHJpbmcoKSxcclxuICAgIH07XHJcbiAgfVxyXG59Il0sInZlcnNpb24iOjN9