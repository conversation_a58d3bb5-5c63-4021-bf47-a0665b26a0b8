{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\headers\\__tests__\\hsts.config.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,2CAA+C;AAC/C,gDAA4C;AAE5C,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;IAC1B,IAAI,UAAsB,CAAC;IAC3B,IAAI,aAAyC,CAAC;IAE9C,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,iBAAiB,GAAG;YACxB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;SACf,CAAC;QAEF,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,wBAAU;gBACV;oBACE,OAAO,EAAE,sBAAa;oBACtB,QAAQ,EAAE,iBAAiB;iBAC5B;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,UAAU,GAAG,MAAM,CAAC,GAAG,CAAa,wBAAU,CAAC,CAAC;QAChD,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,sBAAa,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,YAAY,CAAC;gBAC5C,IAAI,GAAG,KAAK,sBAAsB;oBAAE,OAAO,QAAQ,CAAC;gBACpD,IAAI,GAAG,KAAK,iCAAiC;oBAAE,OAAO,IAAI,CAAC;gBAC3D,IAAI,GAAG,KAAK,uBAAuB;oBAAE,OAAO,IAAI,CAAC;gBACjD,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,UAAU,CAAC,kBAAkB,EAAE,CAAC;YAE7C,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,SAAS,CAAC;gBACzC,IAAI,GAAG,KAAK,sBAAsB;oBAAE,OAAO,OAAO,CAAC;gBACnD,IAAI,GAAG,KAAK,iCAAiC;oBAAE,OAAO,IAAI,CAAC;gBAC3D,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,UAAU,CAAC,kBAAkB,EAAE,CAAC;YAE7C,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;YACxD,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,aAAa,CAAC;gBAC7C,IAAI,GAAG,KAAK,sBAAsB;oBAAE,OAAO,IAAI,CAAC;gBAChD,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,UAAU,CAAC,kBAAkB,EAAE,CAAC;YAE7C,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAClC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAE1C,MAAM,IAAI,GAAG,UAAU,CAAC,kBAAkB,EAAE,CAAC;YAE7C,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,YAAY,CAAC;gBAC5C,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,UAAU,CAAC,kBAAkB,EAAE,CAAC;YAE7C,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;YAC5C,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,YAAY,CAAC;gBAC5C,IAAI,GAAG,KAAK,wBAAwB;oBAAE,OAAO,KAAK,CAAC;gBACnD,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,UAAU,CAAC,aAAa,EAAE,CAAC;YAE3C,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,SAAS,CAAC;gBACzC,IAAI,GAAG,KAAK,wBAAwB;oBAAE,OAAO,KAAK,CAAC;gBACnD,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,UAAU,CAAC,aAAa,EAAE,CAAC;YAE3C,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,aAAa,CAAC;gBAC7C,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,UAAU,CAAC,aAAa,EAAE,CAAC;YAE3C,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,YAAY,CAAC;gBAC5C,IAAI,GAAG,KAAK,wBAAwB;oBAAE,OAAO,IAAI,CAAC;gBAClD,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,UAAU,CAAC,aAAa,EAAE,CAAC;YAE3C,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,UAAU,CAAC,GAAG,EAAE;YACd,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,YAAY,CAAC;gBAC5C,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,IAAI,GAAG,UAAU,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;YAErD,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,IAAI,GAAG,UAAU,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;YAEvD,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,WAAW,GAAG,UAAU,CAAC,kBAAkB,EAAE,CAAC;YACpD,MAAM,UAAU,GAAG,UAAU,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;YAE9D,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,SAAS,CAAC;gBACzC,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,UAAU,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;YAErD,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YACzD,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,aAAa,CAAC;gBAC7C,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,UAAU,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;YAErD,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,YAAY,CAAC;gBAC5C,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,UAAU,CAAC,kBAAkB,EAAE,CAAC;YAEhD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;YAChD,IAAI,CAAC,KAAK,CAAC,UAAiB,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAEvE,MAAM,OAAO,GAAG,UAAU,CAAC,kBAAkB,EAAE,CAAC;YAEhD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;YAChD,IAAI,CAAC,KAAK,CAAC,UAAiB,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC,4BAA4B,CAAC,CAAC;YAEjG,MAAM,OAAO,GAAG,UAAU,CAAC,kBAAkB,EAAE,CAAC;YAEhD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;YAChD,IAAI,CAAC,KAAK,CAAC,UAAiB,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;YAEjF,MAAM,OAAO,GAAG,UAAU,CAAC,kBAAkB,EAAE,CAAC;YAEhD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;YAChD,IAAI,CAAC,KAAK,CAAC,UAAiB,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;YACnF,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,kBAAkB,EAAE,CAAC;YAEpE,MAAM,OAAO,GAAG,UAAU,CAAC,kBAAkB,EAAE,CAAC;YAEhD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3B,MAAM,CAAC,UAAU,CAAC,CAAC,oBAAoB,CAAC,uEAAuE,CAAC,CAAC;YAEjH,UAAU,CAAC,WAAW,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;YAChD,IAAI,CAAC,KAAK,CAAC,UAAiB,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC,qCAAqC,CAAC,CAAC;YAC1G,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,kBAAkB,EAAE,CAAC;YAEpE,MAAM,OAAO,GAAG,UAAU,CAAC,kBAAkB,EAAE,CAAC;YAEhD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3B,MAAM,CAAC,UAAU,CAAC,CAAC,oBAAoB,CAAC,wDAAwD,CAAC,CAAC;YAElG,UAAU,CAAC,WAAW,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,GAAG,EAAE;gBACxC,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,kBAAkB,EAAE,CAAC;YAErE,MAAM,OAAO,GAAG,UAAU,CAAC,kBAAkB,EAAE,CAAC;YAEhD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5B,MAAM,CAAC,UAAU,CAAC,CAAC,gBAAgB,EAAE,CAAC;YAEtC,UAAU,CAAC,WAAW,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,YAAY,CAAC;gBAC5C,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG,UAAU,CAAC,qBAAqB,EAAE,CAAC;YAEvD,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,qDAAqD,CAAC,CAAC;QACpG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,YAAY,CAAC;gBAC5C,IAAI,GAAG,KAAK,sBAAsB;oBAAE,OAAO,IAAI,CAAC;gBAChD,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG,UAAU,CAAC,qBAAqB,EAAE,CAAC;YAEvD,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,oDAAoD,CAAC,CAAC;QAC7F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,YAAY,CAAC;gBAC5C,IAAI,GAAG,KAAK,iCAAiC;oBAAE,OAAO,KAAK,CAAC;gBAC5D,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG,UAAU,CAAC,qBAAqB,EAAE,CAAC;YAEvD,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,yCAAyC,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,YAAY,CAAC;gBAC5C,IAAI,GAAG,KAAK,uBAAuB;oBAAE,OAAO,KAAK,CAAC;gBAClD,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG,UAAU,CAAC,qBAAqB,EAAE,CAAC;YAEvD,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;YAEjD,MAAM,WAAW,GAAG,UAAU,CAAC,qBAAqB,EAAE,CAAC;YAEvD,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,yDAAyD,CAAC,CAAC;QAClG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,YAAY,CAAC;gBAC5C,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,UAAU,CAAC,oBAAoB,EAAE,CAAC;YAElD,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACrC,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,UAAU;oBAAE,OAAO,aAAa,CAAC;gBAC7C,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,UAAU,CAAC,oBAAoB,EAAE,CAAC;YAElD,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,aAAa,GAAG,UAAU,CAAC,qBAAqB,EAAE,CAAC;YAEzD,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBACzD,IAAI,GAAG,KAAK,8BAA8B;oBAAE,OAAO,CAAC,aAAa,EAAE,iBAAiB,CAAC,CAAC;gBACtF,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,UAAU,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7D,MAAM,CAAC,UAAU,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjE,MAAM,CAAC,UAAU,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAEtC,MAAM,CAAC,UAAU,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\headers\\__tests__\\hsts.config.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { HstsConfig } from '../hsts.config';\r\n\r\ndescribe('HstsConfig', () => {\r\n  let hstsConfig: HstsConfig;\r\n  let configService: jest.Mocked<ConfigService>;\r\n\r\n  beforeEach(async () => {\r\n    const mockConfigService = {\r\n      get: jest.fn(),\r\n    };\r\n\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        HstsConfig,\r\n        {\r\n          provide: ConfigService,\r\n          useValue: mockConfigService,\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    hstsConfig = module.get<HstsConfig>(HstsConfig);\r\n    configService = module.get(ConfigService);\r\n  });\r\n\r\n  describe('generateHSTSHeader', () => {\r\n    it('should return production HSTS for production environment', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'production';\r\n        if (key === 'security.hsts.maxAge') return 31536000;\r\n        if (key === 'security.hsts.includeSubDomains') return true;\r\n        if (key === 'security.hsts.preload') return true;\r\n        return defaultValue;\r\n      });\r\n\r\n      const hsts = hstsConfig.generateHSTSHeader();\r\n\r\n      expect(hsts).toBe('max-age=31536000; includeSubDomains; preload');\r\n    });\r\n\r\n    it('should return staging HSTS for staging environment', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'staging';\r\n        if (key === 'security.hsts.maxAge') return 7776000;\r\n        if (key === 'security.hsts.includeSubDomains') return true;\r\n        return defaultValue;\r\n      });\r\n\r\n      const hsts = hstsConfig.generateHSTSHeader();\r\n\r\n      expect(hsts).toBe('max-age=7776000; includeSubDomains');\r\n      expect(hsts).not.toContain('preload');\r\n    });\r\n\r\n    it('should return development HSTS for development environment', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'development';\r\n        if (key === 'security.hsts.maxAge') return 3600;\r\n        return defaultValue;\r\n      });\r\n\r\n      const hsts = hstsConfig.generateHSTSHeader();\r\n\r\n      expect(hsts).toBe('max-age=3600');\r\n      expect(hsts).not.toContain('includeSubDomains');\r\n      expect(hsts).not.toContain('preload');\r\n    });\r\n\r\n    it('should return test HSTS for test environment', () => {\r\n      configService.get.mockReturnValue('test');\r\n\r\n      const hsts = hstsConfig.generateHSTSHeader();\r\n\r\n      expect(hsts).toBe('max-age=0');\r\n    });\r\n\r\n    it('should use default values when config is missing', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'production';\r\n        return defaultValue;\r\n      });\r\n\r\n      const hsts = hstsConfig.generateHSTSHeader();\r\n\r\n      expect(hsts).toContain('max-age=31536000');\r\n      expect(hsts).toContain('includeSubDomains');\r\n      expect(hsts).toContain('preload');\r\n    });\r\n  });\r\n\r\n  describe('isHSTSEnabled', () => {\r\n    it('should be enabled in production', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'production';\r\n        if (key === 'security.hsts.disabled') return false;\r\n        return defaultValue;\r\n      });\r\n\r\n      const enabled = hstsConfig.isHSTSEnabled();\r\n\r\n      expect(enabled).toBe(true);\r\n    });\r\n\r\n    it('should be enabled in staging', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'staging';\r\n        if (key === 'security.hsts.disabled') return false;\r\n        return defaultValue;\r\n      });\r\n\r\n      const enabled = hstsConfig.isHSTSEnabled();\r\n\r\n      expect(enabled).toBe(true);\r\n    });\r\n\r\n    it('should be disabled in development', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'development';\r\n        return defaultValue;\r\n      });\r\n\r\n      const enabled = hstsConfig.isHSTSEnabled();\r\n\r\n      expect(enabled).toBe(false);\r\n    });\r\n\r\n    it('should be disabled when force disabled', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'production';\r\n        if (key === 'security.hsts.disabled') return true;\r\n        return defaultValue;\r\n      });\r\n\r\n      const enabled = hstsConfig.isHSTSEnabled();\r\n\r\n      expect(enabled).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('getDomainSpecificHSTS', () => {\r\n    beforeEach(() => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'production';\r\n        return defaultValue;\r\n      });\r\n    });\r\n\r\n    it('should return API-specific HSTS for API domains', () => {\r\n      const hsts = hstsConfig.getDomainSpecificHSTS('api');\r\n\r\n      expect(hsts).toBe('max-age=63072000; includeSubDomains; preload');\r\n    });\r\n\r\n    it('should return admin-specific HSTS for admin domains', () => {\r\n      const hsts = hstsConfig.getDomainSpecificHSTS('admin');\r\n\r\n      expect(hsts).toBe('max-age=63072000; includeSubDomains; preload');\r\n    });\r\n\r\n    it('should return default HSTS for public domains', () => {\r\n      const defaultHsts = hstsConfig.generateHSTSHeader();\r\n      const publicHsts = hstsConfig.getDomainSpecificHSTS('public');\r\n\r\n      expect(publicHsts).toBe(defaultHsts);\r\n    });\r\n\r\n    it('should return staging API HSTS in staging environment', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'staging';\r\n        return defaultValue;\r\n      });\r\n\r\n      const hsts = hstsConfig.getDomainSpecificHSTS('api');\r\n\r\n      expect(hsts).toBe('max-age=15552000; includeSubDomains');\r\n      expect(hsts).not.toContain('preload');\r\n    });\r\n\r\n    it('should return development API HSTS in development environment', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'development';\r\n        return defaultValue;\r\n      });\r\n\r\n      const hsts = hstsConfig.getDomainSpecificHSTS('api');\r\n\r\n      expect(hsts).toBe('max-age=3600');\r\n    });\r\n  });\r\n\r\n  describe('validateHSTSConfig', () => {\r\n    it('should validate valid HSTS configuration', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'production';\r\n        return defaultValue;\r\n      });\r\n\r\n      const isValid = hstsConfig.validateHSTSConfig();\r\n\r\n      expect(isValid).toBe(true);\r\n    });\r\n\r\n    it('should fail validation for empty HSTS header', () => {\r\n      configService.get.mockReturnValue('production');\r\n      jest.spyOn(hstsConfig as any, 'getProductionHSTS').mockReturnValue('');\r\n\r\n      const isValid = hstsConfig.validateHSTSConfig();\r\n\r\n      expect(isValid).toBe(false);\r\n    });\r\n\r\n    it('should fail validation for missing max-age', () => {\r\n      configService.get.mockReturnValue('production');\r\n      jest.spyOn(hstsConfig as any, 'getProductionHSTS').mockReturnValue('includeSubDomains; preload');\r\n\r\n      const isValid = hstsConfig.validateHSTSConfig();\r\n\r\n      expect(isValid).toBe(false);\r\n    });\r\n\r\n    it('should fail validation for negative max-age', () => {\r\n      configService.get.mockReturnValue('production');\r\n      jest.spyOn(hstsConfig as any, 'getProductionHSTS').mockReturnValue('max-age=-1');\r\n\r\n      const isValid = hstsConfig.validateHSTSConfig();\r\n\r\n      expect(isValid).toBe(false);\r\n    });\r\n\r\n    it('should warn about short max-age in production', () => {\r\n      configService.get.mockReturnValue('production');\r\n      jest.spyOn(hstsConfig as any, 'getProductionHSTS').mockReturnValue('max-age=3600');\r\n      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();\r\n\r\n      const isValid = hstsConfig.validateHSTSConfig();\r\n\r\n      expect(isValid).toBe(true);\r\n      expect(consoleSpy).toHaveBeenCalledWith('HSTS: Production max-age should be at least 1 year (31536000 seconds)');\r\n      \r\n      consoleSpy.mockRestore();\r\n    });\r\n\r\n    it('should warn about missing preload in production', () => {\r\n      configService.get.mockReturnValue('production');\r\n      jest.spyOn(hstsConfig as any, 'getProductionHSTS').mockReturnValue('max-age=31536000; includeSubDomains');\r\n      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();\r\n\r\n      const isValid = hstsConfig.validateHSTSConfig();\r\n\r\n      expect(isValid).toBe(true);\r\n      expect(consoleSpy).toHaveBeenCalledWith('HSTS: Consider enabling preload for production domains');\r\n      \r\n      consoleSpy.mockRestore();\r\n    });\r\n\r\n    it('should handle validation errors gracefully', () => {\r\n      configService.get.mockImplementation(() => {\r\n        throw new Error('Configuration error');\r\n      });\r\n      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();\r\n\r\n      const isValid = hstsConfig.validateHSTSConfig();\r\n\r\n      expect(isValid).toBe(false);\r\n      expect(consoleSpy).toHaveBeenCalled();\r\n      \r\n      consoleSpy.mockRestore();\r\n    });\r\n  });\r\n\r\n  describe('getPreloadEligibility', () => {\r\n    it('should return eligible for proper production configuration', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'production';\r\n        return defaultValue;\r\n      });\r\n\r\n      const eligibility = hstsConfig.getPreloadEligibility();\r\n\r\n      expect(eligibility.eligible).toBe(true);\r\n      expect(eligibility.issues).toHaveLength(0);\r\n      expect(eligibility.requirements).toContain('Serve HSTS header with max-age >= 31536000 (1 year)');\r\n    });\r\n\r\n    it('should return not eligible for short max-age', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'production';\r\n        if (key === 'security.hsts.maxAge') return 3600;\r\n        return defaultValue;\r\n      });\r\n\r\n      const eligibility = hstsConfig.getPreloadEligibility();\r\n\r\n      expect(eligibility.eligible).toBe(false);\r\n      expect(eligibility.issues).toContain('max-age must be at least 31536000 seconds (1 year)');\r\n    });\r\n\r\n    it('should return not eligible for missing includeSubDomains', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'production';\r\n        if (key === 'security.hsts.includeSubDomains') return false;\r\n        return defaultValue;\r\n      });\r\n\r\n      const eligibility = hstsConfig.getPreloadEligibility();\r\n\r\n      expect(eligibility.eligible).toBe(false);\r\n      expect(eligibility.issues).toContain('includeSubDomains directive is required');\r\n    });\r\n\r\n    it('should return not eligible for missing preload', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'production';\r\n        if (key === 'security.hsts.preload') return false;\r\n        return defaultValue;\r\n      });\r\n\r\n      const eligibility = hstsConfig.getPreloadEligibility();\r\n\r\n      expect(eligibility.eligible).toBe(false);\r\n      expect(eligibility.issues).toContain('preload directive is required');\r\n    });\r\n\r\n    it('should return not eligible for non-production environment', () => {\r\n      configService.get.mockReturnValue('development');\r\n\r\n      const eligibility = hstsConfig.getPreloadEligibility();\r\n\r\n      expect(eligibility.eligible).toBe(false);\r\n      expect(eligibility.issues).toContain('Preload is only recommended for production environments');\r\n    });\r\n  });\r\n\r\n  describe('getHSTSConfigSummary', () => {\r\n    it('should return configuration summary', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'production';\r\n        return defaultValue;\r\n      });\r\n\r\n      const summary = hstsConfig.getHSTSConfigSummary();\r\n\r\n      expect(summary.environment).toBe('production');\r\n      expect(summary.enabled).toBe(true);\r\n      expect(summary.maxAge).toBe(31536000);\r\n      expect(summary.maxAgeDays).toBe(365);\r\n      expect(summary.includeSubDomains).toBe(true);\r\n      expect(summary.preload).toBe(true);\r\n      expect(summary.preloadEligible).toBe(true);\r\n      expect(typeof summary.headerLength).toBe('number');\r\n    });\r\n\r\n    it('should handle disabled HSTS', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'NODE_ENV') return 'development';\r\n        return defaultValue;\r\n      });\r\n\r\n      const summary = hstsConfig.getHSTSConfigSummary();\r\n\r\n      expect(summary.enabled).toBe(false);\r\n      expect(summary.maxAge).toBe(3600);\r\n      expect(summary.maxAgeDays).toBe(0);\r\n      expect(summary.includeSubDomains).toBe(false);\r\n      expect(summary.preload).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('generateRemovalHeader', () => {\r\n    it('should generate removal header', () => {\r\n      const removalHeader = hstsConfig.generateRemovalHeader();\r\n\r\n      expect(removalHeader).toBe('max-age=0');\r\n    });\r\n  });\r\n\r\n  describe('isInPreloadList', () => {\r\n    it('should check if domain is in preload list', () => {\r\n      configService.get.mockImplementation((key, defaultValue) => {\r\n        if (key === 'security.hsts.preloadDomains') return ['example.com', 'api.example.com'];\r\n        return defaultValue;\r\n      });\r\n\r\n      expect(hstsConfig.isInPreloadList('example.com')).toBe(true);\r\n      expect(hstsConfig.isInPreloadList('api.example.com')).toBe(true);\r\n      expect(hstsConfig.isInPreloadList('other.com')).toBe(false);\r\n    });\r\n\r\n    it('should return false when no preload domains configured', () => {\r\n      configService.get.mockReturnValue([]);\r\n\r\n      expect(hstsConfig.isInPreloadList('example.com')).toBe(false);\r\n    });\r\n  });\r\n});"], "version": 3}