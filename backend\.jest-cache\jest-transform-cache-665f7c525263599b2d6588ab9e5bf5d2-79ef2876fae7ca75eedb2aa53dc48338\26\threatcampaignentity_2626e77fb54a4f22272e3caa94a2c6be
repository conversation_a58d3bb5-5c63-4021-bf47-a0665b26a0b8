e0aa74e8d73c00e5a5841ef80ddd40b3
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThreatCampaign = exports.CampaignObjective = exports.CampaignSophistication = exports.CampaignStatus = void 0;
const typeorm_1 = require("typeorm");
const class_validator_1 = require("class-validator");
const threat_intelligence_entity_1 = require("./threat-intelligence.entity");
const threat_actor_entity_1 = require("./threat-actor.entity");
/**
 * Campaign status
 */
var CampaignStatus;
(function (CampaignStatus) {
    CampaignStatus["ACTIVE"] = "active";
    CampaignStatus["INACTIVE"] = "inactive";
    CampaignStatus["COMPLETED"] = "completed";
    CampaignStatus["SUSPECTED"] = "suspected";
    CampaignStatus["UNDER_INVESTIGATION"] = "under_investigation";
})(CampaignStatus || (exports.CampaignStatus = CampaignStatus = {}));
/**
 * Campaign sophistication levels
 */
var CampaignSophistication;
(function (CampaignSophistication) {
    CampaignSophistication["LOW"] = "low";
    CampaignSophistication["MEDIUM"] = "medium";
    CampaignSophistication["HIGH"] = "high";
    CampaignSophistication["ADVANCED"] = "advanced";
})(CampaignSophistication || (exports.CampaignSophistication = CampaignSophistication = {}));
/**
 * Campaign objectives
 */
var CampaignObjective;
(function (CampaignObjective) {
    CampaignObjective["DATA_THEFT"] = "data_theft";
    CampaignObjective["FINANCIAL_GAIN"] = "financial_gain";
    CampaignObjective["ESPIONAGE"] = "espionage";
    CampaignObjective["SABOTAGE"] = "sabotage";
    CampaignObjective["DISRUPTION"] = "disruption";
    CampaignObjective["RECONNAISSANCE"] = "reconnaissance";
    CampaignObjective["CREDENTIAL_HARVESTING"] = "credential_harvesting";
    CampaignObjective["RANSOMWARE"] = "ransomware";
    CampaignObjective["UNKNOWN"] = "unknown";
})(CampaignObjective || (exports.CampaignObjective = CampaignObjective = {}));
/**
 * Threat Campaign entity
 * Represents coordinated threat activities attributed to specific actors
 */
let ThreatCampaign = class ThreatCampaign {
    /**
     * Check if campaign is currently active
     */
    get isActive() {
        return this.status === CampaignStatus.ACTIVE;
    }
    /**
     * Check if campaign is ongoing (not completed)
     */
    get isOngoing() {
        return this.status === CampaignStatus.ACTIVE ||
            this.status === CampaignStatus.SUSPECTED ||
            this.status === CampaignStatus.UNDER_INVESTIGATION;
    }
    /**
     * Get campaign duration in days
     */
    get durationInDays() {
        const endDate = this.endDate || new Date();
        return Math.floor((endDate.getTime() - this.startDate.getTime()) / (1000 * 60 * 60 * 24));
    }
    /**
     * Get days since last activity
     */
    get daysSinceLastActivity() {
        const lastActivity = this.lastActivity || this.startDate;
        return Math.floor((Date.now() - lastActivity.getTime()) / (1000 * 60 * 60 * 24));
    }
    /**
     * Get campaign summary
     */
    getSummary() {
        return {
            id: this.id,
            name: this.name,
            aliases: this.aliases,
            status: this.status,
            sophistication: this.sophistication,
            objectives: this.objectives,
            isActive: this.isActive,
            isOngoing: this.isOngoing,
            durationInDays: this.durationInDays,
            daysSinceLastActivity: this.daysSinceLastActivity,
            victimCount: this.victimCount,
            indicatorCount: this.indicatorCount,
            impactScore: this.impactScore,
            confidenceScore: this.confidenceScore,
            threatActor: this.threatActor?.name,
            targetedSectors: this.targetedSectors,
            targetedCountries: this.targetedCountries,
            tags: this.tags,
        };
    }
    /**
     * Add victim to campaign
     */
    addVictim(victim) {
        if (!this.victims) {
            this.victims = [];
        }
        this.victims.push(victim);
        this.victimCount = this.victims.length;
        this.lastActivity = new Date();
    }
    /**
     * Add attack vector
     */
    addAttackVector(vector) {
        if (!this.attackVectors) {
            this.attackVectors = [];
        }
        // Remove existing vector with same name
        this.attackVectors = this.attackVectors.filter(v => v.vector !== vector.vector);
        this.attackVectors.push(vector);
    }
    /**
     * Add timeline phase
     */
    addTimelinePhase(phase) {
        if (!this.timeline) {
            this.timeline = [];
        }
        this.timeline.push(phase);
        // Sort by start date
        this.timeline.sort((a, b) => a.startDate.getTime() - b.startDate.getTime());
    }
    /**
     * Update campaign activity
     */
    recordActivity() {
        this.lastActivity = new Date();
        if (this.status === CampaignStatus.INACTIVE) {
            this.status = CampaignStatus.ACTIVE;
        }
    }
    /**
     * Calculate impact score based on various factors
     */
    calculateImpactScore() {
        let score = 0;
        // Base score from victim count
        if (this.victimCount > 100) {
            score += 9.0;
        }
        else if (this.victimCount > 50) {
            score += 7.0;
        }
        else if (this.victimCount > 10) {
            score += 5.0;
        }
        else if (this.victimCount > 0) {
            score += 3.0;
        }
        // Sophistication modifier
        const sophisticationModifiers = {
            [CampaignSophistication.LOW]: 0.7,
            [CampaignSophistication.MEDIUM]: 1.0,
            [CampaignSophistication.HIGH]: 1.3,
            [CampaignSophistication.ADVANCED]: 1.5,
        };
        score *= sophisticationModifiers[this.sophistication];
        // Objective severity
        const highImpactObjectives = [
            CampaignObjective.SABOTAGE,
            CampaignObjective.RANSOMWARE,
            CampaignObjective.DATA_THEFT,
            CampaignObjective.ESPIONAGE,
        ];
        const hasHighImpactObjective = this.objectives.some(obj => highImpactObjectives.includes(obj));
        if (hasHighImpactObjective) {
            score += 1.0;
        }
        // Critical sector targeting
        const criticalSectors = [
            'financial',
            'healthcare',
            'energy',
            'government',
            'defense',
            'telecommunications',
        ];
        const targetsCriticalSector = this.targetedSectors?.some(sector => criticalSectors.includes(sector.toLowerCase()));
        if (targetsCriticalSector) {
            score += 1.0;
        }
        // Duration factor
        if (this.durationInDays > 365) {
            score += 1.0; // Long-running campaign
        }
        else if (this.durationInDays > 90) {
            score += 0.5;
        }
        // Recent activity
        if (this.daysSinceLastActivity <= 30) {
            score += 0.5;
        }
        this.impactScore = Math.min(score, 10.0);
        return this.impactScore;
    }
    /**
     * Calculate confidence score based on attribution and evidence
     */
    calculateConfidenceScore() {
        let score = 5.0; // Base confidence
        // Attribution factor
        if (this.threatActor) {
            score += 2.0;
        }
        // Evidence quantity
        if (this.indicatorCount > 50) {
            score += 2.0;
        }
        else if (this.indicatorCount > 20) {
            score += 1.0;
        }
        else if (this.indicatorCount > 5) {
            score += 0.5;
        }
        // Victim confirmation
        const confirmedVictims = this.victims?.filter(v => v.confirmedDate) || [];
        if (confirmedVictims.length > 0) {
            score += 1.0;
        }
        // Reference quality
        if (this.references && this.references.length > 3) {
            score += 0.5;
        }
        // Timeline completeness
        if (this.timeline && this.timeline.length > 3) {
            score += 0.5;
        }
        this.confidenceScore = Math.min(score, 10.0);
        return this.confidenceScore;
    }
    /**
     * Check if campaign targets specific sector
     */
    targetsSector(sector) {
        return this.targetedSectors ?
            this.targetedSectors.some(s => s.toLowerCase() === sector.toLowerCase()) :
            false;
    }
    /**
     * Check if campaign targets specific country
     */
    targetsCountry(country) {
        return this.targetedCountries ?
            this.targetedCountries.some(c => c.toLowerCase() === country.toLowerCase()) :
            false;
    }
    /**
     * Get most impacted sectors
     */
    getMostImpactedSectors() {
        if (!this.victims)
            return [];
        const sectorCounts = new Map();
        this.victims.forEach(victim => {
            const count = sectorCounts.get(victim.sector) || 0;
            sectorCounts.set(victim.sector, count + 1);
        });
        return Array.from(sectorCounts.entries())
            .map(([sector, count]) => ({ sector, count }))
            .sort((a, b) => b.count - a.count);
    }
    /**
     * Export campaign for reporting
     */
    exportForReporting() {
        return {
            id: this.id,
            name: this.name,
            aliases: this.aliases,
            description: this.description,
            status: this.status,
            sophistication: this.sophistication,
            objectives: this.objectives,
            startDate: this.startDate,
            endDate: this.endDate,
            lastActivity: this.lastActivity,
            durationInDays: this.durationInDays,
            daysSinceLastActivity: this.daysSinceLastActivity,
            targetedSectors: this.targetedSectors,
            targetedCountries: this.targetedCountries,
            targetedTechnologies: this.targetedTechnologies,
            victimCount: this.victimCount,
            indicatorCount: this.indicatorCount,
            impactScore: this.impactScore,
            confidenceScore: this.confidenceScore,
            threatActor: this.threatActor?.name,
            threatActorId: this.threatActorId,
            malwareFamilies: this.malwareFamilies,
            toolsUsed: this.toolsUsed,
            mostImpactedSectors: this.getMostImpactedSectors(),
            attackVectors: this.attackVectors,
            timeline: this.timeline,
            infrastructure: this.infrastructure,
            tags: this.tags,
            references: this.references,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }
    /**
     * Complete campaign
     */
    complete() {
        this.status = CampaignStatus.COMPLETED;
        this.endDate = new Date();
    }
    /**
     * Activate campaign
     */
    activate() {
        this.status = CampaignStatus.ACTIVE;
        this.lastActivity = new Date();
    }
    /**
     * Deactivate campaign
     */
    deactivate() {
        this.status = CampaignStatus.INACTIVE;
    }
    /**
     * Mark as under investigation
     */
    markUnderInvestigation() {
        this.status = CampaignStatus.UNDER_INVESTIGATION;
    }
};
exports.ThreatCampaign = ThreatCampaign;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ThreatCampaign.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ThreatCampaign.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ThreatCampaign.prototype, "aliases", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ThreatCampaign.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: CampaignStatus,
        default: CampaignStatus.SUSPECTED,
    }),
    (0, class_validator_1.IsEnum)(CampaignStatus),
    __metadata("design:type", String)
], ThreatCampaign.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: CampaignSophistication,
        default: CampaignSophistication.MEDIUM,
    }),
    (0, class_validator_1.IsEnum)(CampaignSophistication),
    __metadata("design:type", String)
], ThreatCampaign.prototype, "sophistication", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: CampaignObjective,
        array: true,
    }),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ThreatCampaign.prototype, "objectives", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], ThreatCampaign.prototype, "startDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], ThreatCampaign.prototype, "endDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], ThreatCampaign.prototype, "lastActivity", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ThreatCampaign.prototype, "targetedSectors", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ThreatCampaign.prototype, "targetedCountries", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ThreatCampaign.prototype, "targetedTechnologies", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', array: true, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ThreatCampaign.prototype, "victims", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', array: true, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ThreatCampaign.prototype, "attackVectors", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', array: true, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ThreatCampaign.prototype, "timeline", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ThreatCampaign.prototype, "malwareFamilies", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ThreatCampaign.prototype, "toolsUsed", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ThreatCampaign.prototype, "infrastructure", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ThreatCampaign.prototype, "references", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ThreatCampaign.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'integer', default: 0 }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ThreatCampaign.prototype, "victimCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'integer', default: 0 }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ThreatCampaign.prototype, "indicatorCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 3, scale: 1, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ThreatCampaign.prototype, "impactScore", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 3, scale: 1, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ThreatCampaign.prototype, "confidenceScore", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", typeof (_d = typeof Record !== "undefined" && Record) === "function" ? _d : Object)
], ThreatCampaign.prototype, "customAttributes", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => threat_actor_entity_1.ThreatActor, (actor) => actor.campaigns, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'threat_actor_id' }),
    __metadata("design:type", typeof (_e = typeof threat_actor_entity_1.ThreatActor !== "undefined" && threat_actor_entity_1.ThreatActor) === "function" ? _e : Object)
], ThreatCampaign.prototype, "threatActor", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], ThreatCampaign.prototype, "threatActorId", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => threat_intelligence_entity_1.ThreatIntelligence, (threat) => threat.threatCampaign),
    __metadata("design:type", Array)
], ThreatCampaign.prototype, "threatIntelligence", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", typeof (_f = typeof Date !== "undefined" && Date) === "function" ? _f : Object)
], ThreatCampaign.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", typeof (_g = typeof Date !== "undefined" && Date) === "function" ? _g : Object)
], ThreatCampaign.prototype, "updatedAt", void 0);
exports.ThreatCampaign = ThreatCampaign = __decorate([
    (0, typeorm_1.Entity)('threat_campaigns'),
    (0, typeorm_1.Index)(['status', 'startDate']),
    (0, typeorm_1.Index)(['threatActorId']),
    (0, typeorm_1.Index)(['objectives'], { where: 'objectives IS NOT NULL' }),
    (0, typeorm_1.Index)(['targetedSectors'], { where: 'targeted_sectors IS NOT NULL' })
], ThreatCampaign);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxtb2R1bGVzXFx0aHJlYXQtaW50ZWxsaWdlbmNlXFxkb21haW5cXGVudGl0aWVzXFx0aHJlYXQtY2FtcGFpZ24uZW50aXR5LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBQSxxQ0FVaUI7QUFDakIscURBQStHO0FBRS9HLDZFQUFrRTtBQUNsRSwrREFBb0Q7QUFFcEQ7O0dBRUc7QUFDSCxJQUFZLGNBTVg7QUFORCxXQUFZLGNBQWM7SUFDeEIsbUNBQWlCLENBQUE7SUFDakIsdUNBQXFCLENBQUE7SUFDckIseUNBQXVCLENBQUE7SUFDdkIseUNBQXVCLENBQUE7SUFDdkIsNkRBQTJDLENBQUE7QUFDN0MsQ0FBQyxFQU5XLGNBQWMsOEJBQWQsY0FBYyxRQU16QjtBQUVEOztHQUVHO0FBQ0gsSUFBWSxzQkFLWDtBQUxELFdBQVksc0JBQXNCO0lBQ2hDLHFDQUFXLENBQUE7SUFDWCwyQ0FBaUIsQ0FBQTtJQUNqQix1Q0FBYSxDQUFBO0lBQ2IsK0NBQXFCLENBQUE7QUFDdkIsQ0FBQyxFQUxXLHNCQUFzQixzQ0FBdEIsc0JBQXNCLFFBS2pDO0FBRUQ7O0dBRUc7QUFDSCxJQUFZLGlCQVVYO0FBVkQsV0FBWSxpQkFBaUI7SUFDM0IsOENBQXlCLENBQUE7SUFDekIsc0RBQWlDLENBQUE7SUFDakMsNENBQXVCLENBQUE7SUFDdkIsMENBQXFCLENBQUE7SUFDckIsOENBQXlCLENBQUE7SUFDekIsc0RBQWlDLENBQUE7SUFDakMsb0VBQStDLENBQUE7SUFDL0MsOENBQXlCLENBQUE7SUFDekIsd0NBQW1CLENBQUE7QUFDckIsQ0FBQyxFQVZXLGlCQUFpQixpQ0FBakIsaUJBQWlCLFFBVTVCO0FBcUNEOzs7R0FHRztBQU1JLElBQU0sY0FBYyxHQUFwQixNQUFNLGNBQWM7SUEySnpCOztPQUVHO0lBQ0gsSUFBSSxRQUFRO1FBQ1YsT0FBTyxJQUFJLENBQUMsTUFBTSxLQUFLLGNBQWMsQ0FBQyxNQUFNLENBQUM7SUFDL0MsQ0FBQztJQUVEOztPQUVHO0lBQ0gsSUFBSSxTQUFTO1FBQ1gsT0FBTyxJQUFJLENBQUMsTUFBTSxLQUFLLGNBQWMsQ0FBQyxNQUFNO1lBQ3JDLElBQUksQ0FBQyxNQUFNLEtBQUssY0FBYyxDQUFDLFNBQVM7WUFDeEMsSUFBSSxDQUFDLE1BQU0sS0FBSyxjQUFjLENBQUMsbUJBQW1CLENBQUM7SUFDNUQsQ0FBQztJQUVEOztPQUVHO0lBQ0gsSUFBSSxjQUFjO1FBQ2hCLE1BQU0sT0FBTyxHQUFHLElBQUksQ0FBQyxPQUFPLElBQUksSUFBSSxJQUFJLEVBQUUsQ0FBQztRQUMzQyxPQUFPLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxPQUFPLENBQUMsT0FBTyxFQUFFLEdBQUcsSUFBSSxDQUFDLFNBQVMsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxHQUFHLENBQUMsSUFBSSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxDQUFDLENBQUMsQ0FBQztJQUM1RixDQUFDO0lBRUQ7O09BRUc7SUFDSCxJQUFJLHFCQUFxQjtRQUN2QixNQUFNLFlBQVksR0FBRyxJQUFJLENBQUMsWUFBWSxJQUFJLElBQUksQ0FBQyxTQUFTLENBQUM7UUFDekQsT0FBTyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsSUFBSSxDQUFDLEdBQUcsRUFBRSxHQUFHLFlBQVksQ0FBQyxPQUFPLEVBQUUsQ0FBQyxHQUFHLENBQUMsSUFBSSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxDQUFDLENBQUMsQ0FBQztJQUNuRixDQUFDO0lBRUQ7O09BRUc7SUFDSCxVQUFVO1FBQ1IsT0FBTztZQUNMLEVBQUUsRUFBRSxJQUFJLENBQUMsRUFBRTtZQUNYLElBQUksRUFBRSxJQUFJLENBQUMsSUFBSTtZQUNmLE9BQU8sRUFBRSxJQUFJLENBQUMsT0FBTztZQUNyQixNQUFNLEVBQUUsSUFBSSxDQUFDLE1BQU07WUFDbkIsY0FBYyxFQUFFLElBQUksQ0FBQyxjQUFjO1lBQ25DLFVBQVUsRUFBRSxJQUFJLENBQUMsVUFBVTtZQUMzQixRQUFRLEVBQUUsSUFBSSxDQUFDLFFBQVE7WUFDdkIsU0FBUyxFQUFFLElBQUksQ0FBQyxTQUFTO1lBQ3pCLGNBQWMsRUFBRSxJQUFJLENBQUMsY0FBYztZQUNuQyxxQkFBcUIsRUFBRSxJQUFJLENBQUMscUJBQXFCO1lBQ2pELFdBQVcsRUFBRSxJQUFJLENBQUMsV0FBVztZQUM3QixjQUFjLEVBQUUsSUFBSSxDQUFDLGNBQWM7WUFDbkMsV0FBVyxFQUFFLElBQUksQ0FBQyxXQUFXO1lBQzdCLGVBQWUsRUFBRSxJQUFJLENBQUMsZUFBZTtZQUNyQyxXQUFXLEVBQUUsSUFBSSxDQUFDLFdBQVcsRUFBRSxJQUFJO1lBQ25DLGVBQWUsRUFBRSxJQUFJLENBQUMsZUFBZTtZQUNyQyxpQkFBaUIsRUFBRSxJQUFJLENBQUMsaUJBQWlCO1lBQ3pDLElBQUksRUFBRSxJQUFJLENBQUMsSUFBSTtTQUNoQixDQUFDO0lBQ0osQ0FBQztJQUVEOztPQUVHO0lBQ0gsU0FBUyxDQUFDLE1BQWtCO1FBQzFCLElBQUksQ0FBQyxJQUFJLENBQUMsT0FBTyxFQUFFLENBQUM7WUFDbEIsSUFBSSxDQUFDLE9BQU8sR0FBRyxFQUFFLENBQUM7UUFDcEIsQ0FBQztRQUVELElBQUksQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBQzFCLElBQUksQ0FBQyxXQUFXLEdBQUcsSUFBSSxDQUFDLE9BQU8sQ0FBQyxNQUFNLENBQUM7UUFDdkMsSUFBSSxDQUFDLFlBQVksR0FBRyxJQUFJLElBQUksRUFBRSxDQUFDO0lBQ2pDLENBQUM7SUFFRDs7T0FFRztJQUNILGVBQWUsQ0FBQyxNQUFvQjtRQUNsQyxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsRUFBRSxDQUFDO1lBQ3hCLElBQUksQ0FBQyxhQUFhLEdBQUcsRUFBRSxDQUFDO1FBQzFCLENBQUM7UUFFRCx3Q0FBd0M7UUFDeEMsSUFBSSxDQUFDLGFBQWEsR0FBRyxJQUFJLENBQUMsYUFBYSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxNQUFNLEtBQUssTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBQ2hGLElBQUksQ0FBQyxhQUFhLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDO0lBQ2xDLENBQUM7SUFFRDs7T0FFRztJQUNILGdCQUFnQixDQUFDLEtBQXVCO1FBQ3RDLElBQUksQ0FBQyxJQUFJLENBQUMsUUFBUSxFQUFFLENBQUM7WUFDbkIsSUFBSSxDQUFDLFFBQVEsR0FBRyxFQUFFLENBQUM7UUFDckIsQ0FBQztRQUVELElBQUksQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDO1FBQzFCLHFCQUFxQjtRQUNyQixJQUFJLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFDLENBQUMsQ0FBQyxTQUFTLENBQUMsT0FBTyxFQUFFLEdBQUcsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxDQUFDO0lBQzlFLENBQUM7SUFFRDs7T0FFRztJQUNILGNBQWM7UUFDWixJQUFJLENBQUMsWUFBWSxHQUFHLElBQUksSUFBSSxFQUFFLENBQUM7UUFDL0IsSUFBSSxJQUFJLENBQUMsTUFBTSxLQUFLLGNBQWMsQ0FBQyxRQUFRLEVBQUUsQ0FBQztZQUM1QyxJQUFJLENBQUMsTUFBTSxHQUFHLGNBQWMsQ0FBQyxNQUFNLENBQUM7UUFDdEMsQ0FBQztJQUNILENBQUM7SUFFRDs7T0FFRztJQUNILG9CQUFvQjtRQUNsQixJQUFJLEtBQUssR0FBRyxDQUFDLENBQUM7UUFFZCwrQkFBK0I7UUFDL0IsSUFBSSxJQUFJLENBQUMsV0FBVyxHQUFHLEdBQUcsRUFBRSxDQUFDO1lBQzNCLEtBQUssSUFBSSxHQUFHLENBQUM7UUFDZixDQUFDO2FBQU0sSUFBSSxJQUFJLENBQUMsV0FBVyxHQUFHLEVBQUUsRUFBRSxDQUFDO1lBQ2pDLEtBQUssSUFBSSxHQUFHLENBQUM7UUFDZixDQUFDO2FBQU0sSUFBSSxJQUFJLENBQUMsV0FBVyxHQUFHLEVBQUUsRUFBRSxDQUFDO1lBQ2pDLEtBQUssSUFBSSxHQUFHLENBQUM7UUFDZixDQUFDO2FBQU0sSUFBSSxJQUFJLENBQUMsV0FBVyxHQUFHLENBQUMsRUFBRSxDQUFDO1lBQ2hDLEtBQUssSUFBSSxHQUFHLENBQUM7UUFDZixDQUFDO1FBRUQsMEJBQTBCO1FBQzFCLE1BQU0sdUJBQXVCLEdBQUc7WUFDOUIsQ0FBQyxzQkFBc0IsQ0FBQyxHQUFHLENBQUMsRUFBRSxHQUFHO1lBQ2pDLENBQUMsc0JBQXNCLENBQUMsTUFBTSxDQUFDLEVBQUUsR0FBRztZQUNwQyxDQUFDLHNCQUFzQixDQUFDLElBQUksQ0FBQyxFQUFFLEdBQUc7WUFDbEMsQ0FBQyxzQkFBc0IsQ0FBQyxRQUFRLENBQUMsRUFBRSxHQUFHO1NBQ3ZDLENBQUM7UUFDRixLQUFLLElBQUksdUJBQXVCLENBQUMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxDQUFDO1FBRXRELHFCQUFxQjtRQUNyQixNQUFNLG9CQUFvQixHQUFHO1lBQzNCLGlCQUFpQixDQUFDLFFBQVE7WUFDMUIsaUJBQWlCLENBQUMsVUFBVTtZQUM1QixpQkFBaUIsQ0FBQyxVQUFVO1lBQzVCLGlCQUFpQixDQUFDLFNBQVM7U0FDNUIsQ0FBQztRQUVGLE1BQU0sc0JBQXNCLEdBQUcsSUFBSSxDQUFDLFVBQVUsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FDeEQsb0JBQW9CLENBQUMsUUFBUSxDQUFDLEdBQUcsQ0FBQyxDQUNuQyxDQUFDO1FBRUYsSUFBSSxzQkFBc0IsRUFBRSxDQUFDO1lBQzNCLEtBQUssSUFBSSxHQUFHLENBQUM7UUFDZixDQUFDO1FBRUQsNEJBQTRCO1FBQzVCLE1BQU0sZUFBZSxHQUFHO1lBQ3RCLFdBQVc7WUFDWCxZQUFZO1lBQ1osUUFBUTtZQUNSLFlBQVk7WUFDWixTQUFTO1lBQ1Qsb0JBQW9CO1NBQ3JCLENBQUM7UUFFRixNQUFNLHFCQUFxQixHQUFHLElBQUksQ0FBQyxlQUFlLEVBQUUsSUFBSSxDQUFDLE1BQU0sQ0FBQyxFQUFFLENBQ2hFLGVBQWUsQ0FBQyxRQUFRLENBQUMsTUFBTSxDQUFDLFdBQVcsRUFBRSxDQUFDLENBQy9DLENBQUM7UUFFRixJQUFJLHFCQUFxQixFQUFFLENBQUM7WUFDMUIsS0FBSyxJQUFJLEdBQUcsQ0FBQztRQUNmLENBQUM7UUFFRCxrQkFBa0I7UUFDbEIsSUFBSSxJQUFJLENBQUMsY0FBYyxHQUFHLEdBQUcsRUFBRSxDQUFDO1lBQzlCLEtBQUssSUFBSSxHQUFHLENBQUMsQ0FBQyx3QkFBd0I7UUFDeEMsQ0FBQzthQUFNLElBQUksSUFBSSxDQUFDLGNBQWMsR0FBRyxFQUFFLEVBQUUsQ0FBQztZQUNwQyxLQUFLLElBQUksR0FBRyxDQUFDO1FBQ2YsQ0FBQztRQUVELGtCQUFrQjtRQUNsQixJQUFJLElBQUksQ0FBQyxxQkFBcUIsSUFBSSxFQUFFLEVBQUUsQ0FBQztZQUNyQyxLQUFLLElBQUksR0FBRyxDQUFDO1FBQ2YsQ0FBQztRQUVELElBQUksQ0FBQyxXQUFXLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxLQUFLLEVBQUUsSUFBSSxDQUFDLENBQUM7UUFDekMsT0FBTyxJQUFJLENBQUMsV0FBVyxDQUFDO0lBQzFCLENBQUM7SUFFRDs7T0FFRztJQUNILHdCQUF3QjtRQUN0QixJQUFJLEtBQUssR0FBRyxHQUFHLENBQUMsQ0FBQyxrQkFBa0I7UUFFbkMscUJBQXFCO1FBQ3JCLElBQUksSUFBSSxDQUFDLFdBQVcsRUFBRSxDQUFDO1lBQ3JCLEtBQUssSUFBSSxHQUFHLENBQUM7UUFDZixDQUFDO1FBRUQsb0JBQW9CO1FBQ3BCLElBQUksSUFBSSxDQUFDLGNBQWMsR0FBRyxFQUFFLEVBQUUsQ0FBQztZQUM3QixLQUFLLElBQUksR0FBRyxDQUFDO1FBQ2YsQ0FBQzthQUFNLElBQUksSUFBSSxDQUFDLGNBQWMsR0FBRyxFQUFFLEVBQUUsQ0FBQztZQUNwQyxLQUFLLElBQUksR0FBRyxDQUFDO1FBQ2YsQ0FBQzthQUFNLElBQUksSUFBSSxDQUFDLGNBQWMsR0FBRyxDQUFDLEVBQUUsQ0FBQztZQUNuQyxLQUFLLElBQUksR0FBRyxDQUFDO1FBQ2YsQ0FBQztRQUVELHNCQUFzQjtRQUN0QixNQUFNLGdCQUFnQixHQUFHLElBQUksQ0FBQyxPQUFPLEVBQUUsTUFBTSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLGFBQWEsQ0FBQyxJQUFJLEVBQUUsQ0FBQztRQUMxRSxJQUFJLGdCQUFnQixDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUUsQ0FBQztZQUNoQyxLQUFLLElBQUksR0FBRyxDQUFDO1FBQ2YsQ0FBQztRQUVELG9CQUFvQjtRQUNwQixJQUFJLElBQUksQ0FBQyxVQUFVLElBQUksSUFBSSxDQUFDLFVBQVUsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUM7WUFDbEQsS0FBSyxJQUFJLEdBQUcsQ0FBQztRQUNmLENBQUM7UUFFRCx3QkFBd0I7UUFDeEIsSUFBSSxJQUFJLENBQUMsUUFBUSxJQUFJLElBQUksQ0FBQyxRQUFRLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRSxDQUFDO1lBQzlDLEtBQUssSUFBSSxHQUFHLENBQUM7UUFDZixDQUFDO1FBRUQsSUFBSSxDQUFDLGVBQWUsR0FBRyxJQUFJLENBQUMsR0FBRyxDQUFDLEtBQUssRUFBRSxJQUFJLENBQUMsQ0FBQztRQUM3QyxPQUFPLElBQUksQ0FBQyxlQUFlLENBQUM7SUFDOUIsQ0FBQztJQUVEOztPQUVHO0lBQ0gsYUFBYSxDQUFDLE1BQWM7UUFDMUIsT0FBTyxJQUFJLENBQUMsZUFBZSxDQUFDLENBQUM7WUFDM0IsSUFBSSxDQUFDLGVBQWUsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsV0FBVyxFQUFFLEtBQUssTUFBTSxDQUFDLFdBQVcsRUFBRSxDQUFDLENBQUMsQ0FBQztZQUMxRSxLQUFLLENBQUM7SUFDVixDQUFDO0lBRUQ7O09BRUc7SUFDSCxjQUFjLENBQUMsT0FBZTtRQUM1QixPQUFPLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDO1lBQzdCLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsV0FBVyxFQUFFLEtBQUssT0FBTyxDQUFDLFdBQVcsRUFBRSxDQUFDLENBQUMsQ0FBQztZQUM3RSxLQUFLLENBQUM7SUFDVixDQUFDO0lBRUQ7O09BRUc7SUFDSCxzQkFBc0I7UUFDcEIsSUFBSSxDQUFDLElBQUksQ0FBQyxPQUFPO1lBQUUsT0FBTyxFQUFFLENBQUM7UUFFN0IsTUFBTSxZQUFZLEdBQUcsSUFBSSxHQUFHLEVBQWtCLENBQUM7UUFFL0MsSUFBSSxDQUFDLE9BQU8sQ0FBQyxPQUFPLENBQUMsTUFBTSxDQUFDLEVBQUU7WUFDNUIsTUFBTSxLQUFLLEdBQUcsWUFBWSxDQUFDLEdBQUcsQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQ25ELFlBQVksQ0FBQyxHQUFHLENBQUMsTUFBTSxDQUFDLE1BQU0sRUFBRSxLQUFLLEdBQUcsQ0FBQyxDQUFDLENBQUM7UUFDN0MsQ0FBQyxDQUFDLENBQUM7UUFFSCxPQUFPLEtBQUssQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLE9BQU8sRUFBRSxDQUFDO2FBQ3RDLEdBQUcsQ0FBQyxDQUFDLENBQUMsTUFBTSxFQUFFLEtBQUssQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDLEVBQUUsTUFBTSxFQUFFLEtBQUssRUFBRSxDQUFDLENBQUM7YUFDN0MsSUFBSSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxFQUFFLENBQUMsQ0FBQyxDQUFDLEtBQUssR0FBRyxDQUFDLENBQUMsS0FBSyxDQUFDLENBQUM7SUFDdkMsQ0FBQztJQUVEOztPQUVHO0lBQ0gsa0JBQWtCO1FBQ2hCLE9BQU87WUFDTCxFQUFFLEVBQUUsSUFBSSxDQUFDLEVBQUU7WUFDWCxJQUFJLEVBQUUsSUFBSSxDQUFDLElBQUk7WUFDZixPQUFPLEVBQUUsSUFBSSxDQUFDLE9BQU87WUFDckIsV0FBVyxFQUFFLElBQUksQ0FBQyxXQUFXO1lBQzdCLE1BQU0sRUFBRSxJQUFJLENBQUMsTUFBTTtZQUNuQixjQUFjLEVBQUUsSUFBSSxDQUFDLGNBQWM7WUFDbkMsVUFBVSxFQUFFLElBQUksQ0FBQyxVQUFVO1lBQzNCLFNBQVMsRUFBRSxJQUFJLENBQUMsU0FBUztZQUN6QixPQUFPLEVBQUUsSUFBSSxDQUFDLE9BQU87WUFDckIsWUFBWSxFQUFFLElBQUksQ0FBQyxZQUFZO1lBQy9CLGNBQWMsRUFBRSxJQUFJLENBQUMsY0FBYztZQUNuQyxxQkFBcUIsRUFBRSxJQUFJLENBQUMscUJBQXFCO1lBQ2pELGVBQWUsRUFBRSxJQUFJLENBQUMsZUFBZTtZQUNyQyxpQkFBaUIsRUFBRSxJQUFJLENBQUMsaUJBQWlCO1lBQ3pDLG9CQUFvQixFQUFFLElBQUksQ0FBQyxvQkFBb0I7WUFDL0MsV0FBVyxFQUFFLElBQUksQ0FBQyxXQUFXO1lBQzdCLGNBQWMsRUFBRSxJQUFJLENBQUMsY0FBYztZQUNuQyxXQUFXLEVBQUUsSUFBSSxDQUFDLFdBQVc7WUFDN0IsZUFBZSxFQUFFLElBQUksQ0FBQyxlQUFlO1lBQ3JDLFdBQVcsRUFBRSxJQUFJLENBQUMsV0FBVyxFQUFFLElBQUk7WUFDbkMsYUFBYSxFQUFFLElBQUksQ0FBQyxhQUFhO1lBQ2pDLGVBQWUsRUFBRSxJQUFJLENBQUMsZUFBZTtZQUNyQyxTQUFTLEVBQUUsSUFBSSxDQUFDLFNBQVM7WUFDekIsbUJBQW1CLEVBQUUsSUFBSSxDQUFDLHNCQUFzQixFQUFFO1lBQ2xELGFBQWEsRUFBRSxJQUFJLENBQUMsYUFBYTtZQUNqQyxRQUFRLEVBQUUsSUFBSSxDQUFDLFFBQVE7WUFDdkIsY0FBYyxFQUFFLElBQUksQ0FBQyxjQUFjO1lBQ25DLElBQUksRUFBRSxJQUFJLENBQUMsSUFBSTtZQUNmLFVBQVUsRUFBRSxJQUFJLENBQUMsVUFBVTtZQUMzQixTQUFTLEVBQUUsSUFBSSxDQUFDLFNBQVM7WUFDekIsU0FBUyxFQUFFLElBQUksQ0FBQyxTQUFTO1NBQzFCLENBQUM7SUFDSixDQUFDO0lBRUQ7O09BRUc7SUFDSCxRQUFRO1FBQ04sSUFBSSxDQUFDLE1BQU0sR0FBRyxjQUFjLENBQUMsU0FBUyxDQUFDO1FBQ3ZDLElBQUksQ0FBQyxPQUFPLEdBQUcsSUFBSSxJQUFJLEVBQUUsQ0FBQztJQUM1QixDQUFDO0lBRUQ7O09BRUc7SUFDSCxRQUFRO1FBQ04sSUFBSSxDQUFDLE1BQU0sR0FBRyxjQUFjLENBQUMsTUFBTSxDQUFDO1FBQ3BDLElBQUksQ0FBQyxZQUFZLEdBQUcsSUFBSSxJQUFJLEVBQUUsQ0FBQztJQUNqQyxDQUFDO0lBRUQ7O09BRUc7SUFDSCxVQUFVO1FBQ1IsSUFBSSxDQUFDLE1BQU0sR0FBRyxjQUFjLENBQUMsUUFBUSxDQUFDO0lBQ3hDLENBQUM7SUFFRDs7T0FFRztJQUNILHNCQUFzQjtRQUNwQixJQUFJLENBQUMsTUFBTSxHQUFHLGNBQWMsQ0FBQyxtQkFBbUIsQ0FBQztJQUNuRCxDQUFDO0NBQ0YsQ0FBQTtBQWxlWSx3Q0FBYztBQUV6QjtJQURDLElBQUEsZ0NBQXNCLEVBQUMsTUFBTSxDQUFDOzswQ0FDcEI7QUFJWDtJQUZDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxTQUFTLEVBQUUsTUFBTSxFQUFFLEdBQUcsRUFBRSxDQUFDO0lBQ3hDLElBQUEsMEJBQVEsR0FBRTs7NENBQ0U7QUFLYjtJQUhDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxNQUFNLEVBQUUsS0FBSyxFQUFFLElBQUksRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7SUFDckQsSUFBQSw0QkFBVSxHQUFFO0lBQ1osSUFBQSx5QkFBTyxHQUFFOzsrQ0FDUztBQUluQjtJQUZDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxNQUFNLEVBQUUsQ0FBQztJQUN4QixJQUFBLDBCQUFRLEdBQUU7O21EQUNTO0FBUXBCO0lBTkMsSUFBQSxnQkFBTSxFQUFDO1FBQ04sSUFBSSxFQUFFLE1BQU07UUFDWixJQUFJLEVBQUUsY0FBYztRQUNwQixPQUFPLEVBQUUsY0FBYyxDQUFDLFNBQVM7S0FDbEMsQ0FBQztJQUNELElBQUEsd0JBQU0sRUFBQyxjQUFjLENBQUM7OzhDQUNBO0FBUXZCO0lBTkMsSUFBQSxnQkFBTSxFQUFDO1FBQ04sSUFBSSxFQUFFLE1BQU07UUFDWixJQUFJLEVBQUUsc0JBQXNCO1FBQzVCLE9BQU8sRUFBRSxzQkFBc0IsQ0FBQyxNQUFNO0tBQ3ZDLENBQUM7SUFDRCxJQUFBLHdCQUFNLEVBQUMsc0JBQXNCLENBQUM7O3NEQUNRO0FBUXZDO0lBTkMsSUFBQSxnQkFBTSxFQUFDO1FBQ04sSUFBSSxFQUFFLE1BQU07UUFDWixJQUFJLEVBQUUsaUJBQWlCO1FBQ3ZCLEtBQUssRUFBRSxJQUFJO0tBQ1osQ0FBQztJQUNELElBQUEseUJBQU8sR0FBRTs7a0RBQ3NCO0FBSWhDO0lBRkMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLDBCQUEwQixFQUFFLENBQUM7SUFDNUMsSUFBQSx3QkFBTSxHQUFFO2tEQUNFLElBQUksb0JBQUosSUFBSTtpREFBQztBQUtoQjtJQUhDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSwwQkFBMEIsRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7SUFDNUQsSUFBQSw0QkFBVSxHQUFFO0lBQ1osSUFBQSx3QkFBTSxHQUFFO2tEQUNDLElBQUksb0JBQUosSUFBSTsrQ0FBQztBQUtmO0lBSEMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLDBCQUEwQixFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQztJQUM1RCxJQUFBLDRCQUFVLEdBQUU7SUFDWixJQUFBLHdCQUFNLEdBQUU7a0RBQ00sSUFBSSxvQkFBSixJQUFJO29EQUFDO0FBS3BCO0lBSEMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLE1BQU0sRUFBRSxLQUFLLEVBQUUsSUFBSSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQztJQUNyRCxJQUFBLDRCQUFVLEdBQUU7SUFDWixJQUFBLHlCQUFPLEdBQUU7O3VEQUNpQjtBQUszQjtJQUhDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxNQUFNLEVBQUUsS0FBSyxFQUFFLElBQUksRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7SUFDckQsSUFBQSw0QkFBVSxHQUFFO0lBQ1osSUFBQSx5QkFBTyxHQUFFOzt5REFDbUI7QUFLN0I7SUFIQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUsTUFBTSxFQUFFLEtBQUssRUFBRSxJQUFJLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDO0lBQ3JELElBQUEsNEJBQVUsR0FBRTtJQUNaLElBQUEseUJBQU8sR0FBRTs7NERBQ3NCO0FBS2hDO0lBSEMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLE9BQU8sRUFBRSxLQUFLLEVBQUUsSUFBSSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQztJQUN0RCxJQUFBLDRCQUFVLEdBQUU7SUFDWixJQUFBLHlCQUFPLEdBQUU7OytDQUNhO0FBS3ZCO0lBSEMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLE9BQU8sRUFBRSxLQUFLLEVBQUUsSUFBSSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQztJQUN0RCxJQUFBLDRCQUFVLEdBQUU7SUFDWixJQUFBLHlCQUFPLEdBQUU7O3FEQUNxQjtBQUsvQjtJQUhDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxPQUFPLEVBQUUsS0FBSyxFQUFFLElBQUksRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7SUFDdEQsSUFBQSw0QkFBVSxHQUFFO0lBQ1osSUFBQSx5QkFBTyxHQUFFOztnREFDb0I7QUFLOUI7SUFIQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUsTUFBTSxFQUFFLEtBQUssRUFBRSxJQUFJLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDO0lBQ3JELElBQUEsNEJBQVUsR0FBRTtJQUNaLElBQUEseUJBQU8sR0FBRTs7dURBQ2lCO0FBSzNCO0lBSEMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLE1BQU0sRUFBRSxLQUFLLEVBQUUsSUFBSSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQztJQUNyRCxJQUFBLDRCQUFVLEdBQUU7SUFDWixJQUFBLHlCQUFPLEdBQUU7O2lEQUNXO0FBS3JCO0lBSEMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLE9BQU8sRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7SUFDekMsSUFBQSw0QkFBVSxHQUFFO0lBQ1osSUFBQSwwQkFBUSxHQUFFOztzREFNVDtBQUtGO0lBSEMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLE1BQU0sRUFBRSxLQUFLLEVBQUUsSUFBSSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQztJQUNyRCxJQUFBLDRCQUFVLEdBQUU7SUFDWixJQUFBLHlCQUFPLEdBQUU7O2tEQUNZO0FBS3RCO0lBSEMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLE1BQU0sRUFBRSxLQUFLLEVBQUUsSUFBSSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQztJQUNyRCxJQUFBLDRCQUFVLEdBQUU7SUFDWixJQUFBLHlCQUFPLEdBQUU7OzRDQUNNO0FBSWhCO0lBRkMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLFNBQVMsRUFBRSxPQUFPLEVBQUUsQ0FBQyxFQUFFLENBQUM7SUFDdkMsSUFBQSwwQkFBUSxHQUFFOzttREFDUztBQUlwQjtJQUZDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxTQUFTLEVBQUUsT0FBTyxFQUFFLENBQUMsRUFBRSxDQUFDO0lBQ3ZDLElBQUEsMEJBQVEsR0FBRTs7c0RBQ1k7QUFLdkI7SUFIQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUsU0FBUyxFQUFFLFNBQVMsRUFBRSxDQUFDLEVBQUUsS0FBSyxFQUFFLENBQUMsRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7SUFDbkUsSUFBQSw0QkFBVSxHQUFFO0lBQ1osSUFBQSwwQkFBUSxHQUFFOzttREFDVTtBQUtyQjtJQUhDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxTQUFTLEVBQUUsU0FBUyxFQUFFLENBQUMsRUFBRSxLQUFLLEVBQUUsQ0FBQyxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQztJQUNuRSxJQUFBLDRCQUFVLEdBQUU7SUFDWixJQUFBLDBCQUFRLEdBQUU7O3VEQUNjO0FBS3pCO0lBSEMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLE9BQU8sRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7SUFDekMsSUFBQSw0QkFBVSxHQUFFO0lBQ1osSUFBQSwwQkFBUSxHQUFFO2tEQUNRLE1BQU0sb0JBQU4sTUFBTTt3REFBYztBQUt2QztJQUZDLElBQUEsbUJBQVMsRUFBQyxHQUFHLEVBQUUsQ0FBQyxpQ0FBVyxFQUFFLENBQUMsS0FBSyxFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsU0FBUyxFQUFFLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDO0lBQzVFLElBQUEsb0JBQVUsRUFBQyxFQUFFLElBQUksRUFBRSxpQkFBaUIsRUFBRSxDQUFDO2tEQUMxQixpQ0FBVyxvQkFBWCxpQ0FBVzttREFBQztBQUcxQjtJQURDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxNQUFNLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDOztxREFDbEI7QUFHdkI7SUFEQyxJQUFBLG1CQUFTLEVBQUMsR0FBRyxFQUFFLENBQUMsK0NBQWtCLEVBQUUsQ0FBQyxNQUFNLEVBQUUsRUFBRSxDQUFDLE1BQU0sQ0FBQyxjQUFjLENBQUM7OzBEQUM5QjtBQUd6QztJQURDLElBQUEsMEJBQWdCLEdBQUU7a0RBQ1IsSUFBSSxvQkFBSixJQUFJO2lEQUFDO0FBR2hCO0lBREMsSUFBQSwwQkFBZ0IsR0FBRTtrREFDUixJQUFJLG9CQUFKLElBQUk7aURBQUM7eUJBekpMLGNBQWM7SUFMMUIsSUFBQSxnQkFBTSxFQUFDLGtCQUFrQixDQUFDO0lBQzFCLElBQUEsZUFBSyxFQUFDLENBQUMsUUFBUSxFQUFFLFdBQVcsQ0FBQyxDQUFDO0lBQzlCLElBQUEsZUFBSyxFQUFDLENBQUMsZUFBZSxDQUFDLENBQUM7SUFDeEIsSUFBQSxlQUFLLEVBQUMsQ0FBQyxZQUFZLENBQUMsRUFBRSxFQUFFLEtBQUssRUFBRSx3QkFBd0IsRUFBRSxDQUFDO0lBQzFELElBQUEsZUFBSyxFQUFDLENBQUMsaUJBQWlCLENBQUMsRUFBRSxFQUFFLEtBQUssRUFBRSw4QkFBOEIsRUFBRSxDQUFDO0dBQ3pELGNBQWMsQ0FrZTFCIiwibmFtZXMiOltdLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTHVrYVxcc2VudGluZWxcXGJhY2tlbmRcXHNyY1xcbW9kdWxlc1xcdGhyZWF0LWludGVsbGlnZW5jZVxcZG9tYWluXFxlbnRpdGllc1xcdGhyZWF0LWNhbXBhaWduLmVudGl0eS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xyXG4gIEVudGl0eSxcclxuICBQcmltYXJ5R2VuZXJhdGVkQ29sdW1uLFxyXG4gIENvbHVtbixcclxuICBDcmVhdGVEYXRlQ29sdW1uLFxyXG4gIFVwZGF0ZURhdGVDb2x1bW4sXHJcbiAgSW5kZXgsXHJcbiAgT25lVG9NYW55LFxyXG4gIE1hbnlUb09uZSxcclxuICBKb2luQ29sdW1uLFxyXG59IGZyb20gJ3R5cGVvcm0nO1xyXG5pbXBvcnQgeyBJc0VudW0sIElzT3B0aW9uYWwsIElzU3RyaW5nLCBJc051bWJlciwgSXNCb29sZWFuLCBJc0FycmF5LCBJc09iamVjdCwgSXNEYXRlIH0gZnJvbSAnY2xhc3MtdmFsaWRhdG9yJztcclxuXHJcbmltcG9ydCB7IFRocmVhdEludGVsbGlnZW5jZSB9IGZyb20gJy4vdGhyZWF0LWludGVsbGlnZW5jZS5lbnRpdHknO1xyXG5pbXBvcnQgeyBUaHJlYXRBY3RvciB9IGZyb20gJy4vdGhyZWF0LWFjdG9yLmVudGl0eSc7XHJcblxyXG4vKipcclxuICogQ2FtcGFpZ24gc3RhdHVzXHJcbiAqL1xyXG5leHBvcnQgZW51bSBDYW1wYWlnblN0YXR1cyB7XHJcbiAgQUNUSVZFID0gJ2FjdGl2ZScsXHJcbiAgSU5BQ1RJVkUgPSAnaW5hY3RpdmUnLFxyXG4gIENPTVBMRVRFRCA9ICdjb21wbGV0ZWQnLFxyXG4gIFNVU1BFQ1RFRCA9ICdzdXNwZWN0ZWQnLFxyXG4gIFVOREVSX0lOVkVTVElHQVRJT04gPSAndW5kZXJfaW52ZXN0aWdhdGlvbicsXHJcbn1cclxuXHJcbi8qKlxyXG4gKiBDYW1wYWlnbiBzb3BoaXN0aWNhdGlvbiBsZXZlbHNcclxuICovXHJcbmV4cG9ydCBlbnVtIENhbXBhaWduU29waGlzdGljYXRpb24ge1xyXG4gIExPVyA9ICdsb3cnLFxyXG4gIE1FRElVTSA9ICdtZWRpdW0nLFxyXG4gIEhJR0ggPSAnaGlnaCcsXHJcbiAgQURWQU5DRUQgPSAnYWR2YW5jZWQnLFxyXG59XHJcblxyXG4vKipcclxuICogQ2FtcGFpZ24gb2JqZWN0aXZlc1xyXG4gKi9cclxuZXhwb3J0IGVudW0gQ2FtcGFpZ25PYmplY3RpdmUge1xyXG4gIERBVEFfVEhFRlQgPSAnZGF0YV90aGVmdCcsXHJcbiAgRklOQU5DSUFMX0dBSU4gPSAnZmluYW5jaWFsX2dhaW4nLFxyXG4gIEVTUElPTkFHRSA9ICdlc3Bpb25hZ2UnLFxyXG4gIFNBQk9UQUdFID0gJ3NhYm90YWdlJyxcclxuICBESVNSVVBUSU9OID0gJ2Rpc3J1cHRpb24nLFxyXG4gIFJFQ09OTkFJU1NBTkNFID0gJ3JlY29ubmFpc3NhbmNlJyxcclxuICBDUkVERU5USUFMX0hBUlZFU1RJTkcgPSAnY3JlZGVudGlhbF9oYXJ2ZXN0aW5nJyxcclxuICBSQU5TT01XQVJFID0gJ3JhbnNvbXdhcmUnLFxyXG4gIFVOS05PV04gPSAndW5rbm93bicsXHJcbn1cclxuXHJcbi8qKlxyXG4gKiBUaW1lbGluZSBpbmZvcm1hdGlvbiBmb3IgY2FtcGFpZ24gcGhhc2VzXHJcbiAqL1xyXG5leHBvcnQgaW50ZXJmYWNlIENhbXBhaWduVGltZWxpbmUge1xyXG4gIHBoYXNlOiBzdHJpbmc7XHJcbiAgc3RhcnREYXRlOiBEYXRlO1xyXG4gIGVuZERhdGU/OiBEYXRlO1xyXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XHJcbiAgaW5kaWNhdG9ycz86IHN0cmluZ1tdO1xyXG59XHJcblxyXG4vKipcclxuICogVmljdGltIGluZm9ybWF0aW9uXHJcbiAqL1xyXG5leHBvcnQgaW50ZXJmYWNlIFZpY3RpbUluZm8ge1xyXG4gIG9yZ2FuaXphdGlvbk5hbWU/OiBzdHJpbmc7XHJcbiAgc2VjdG9yOiBzdHJpbmc7XHJcbiAgY291bnRyeTogc3RyaW5nO1xyXG4gIHNpemU/OiAnc21hbGwnIHwgJ21lZGl1bScgfCAnbGFyZ2UnIHwgJ2VudGVycHJpc2UnO1xyXG4gIGltcGFjdExldmVsOiAnbG93JyB8ICdtZWRpdW0nIHwgJ2hpZ2gnIHwgJ2NyaXRpY2FsJztcclxuICBkaXNjb3ZlcmVkRGF0ZTogRGF0ZTtcclxuICBjb25maXJtZWREYXRlPzogRGF0ZTtcclxufVxyXG5cclxuLyoqXHJcbiAqIEF0dGFjayB2ZWN0b3IgaW5mb3JtYXRpb25cclxuICovXHJcbmV4cG9ydCBpbnRlcmZhY2UgQXR0YWNrVmVjdG9yIHtcclxuICB2ZWN0b3I6IHN0cmluZztcclxuICBkZXNjcmlwdGlvbjogc3RyaW5nO1xyXG4gIG1pdHJlSWQ/OiBzdHJpbmc7XHJcbiAgZnJlcXVlbmN5OiAnaGlnaCcgfCAnbWVkaXVtJyB8ICdsb3cnO1xyXG4gIHN1Y2Nlc3NSYXRlPzogbnVtYmVyO1xyXG59XHJcblxyXG4vKipcclxuICogVGhyZWF0IENhbXBhaWduIGVudGl0eVxyXG4gKiBSZXByZXNlbnRzIGNvb3JkaW5hdGVkIHRocmVhdCBhY3Rpdml0aWVzIGF0dHJpYnV0ZWQgdG8gc3BlY2lmaWMgYWN0b3JzXHJcbiAqL1xyXG5ARW50aXR5KCd0aHJlYXRfY2FtcGFpZ25zJylcclxuQEluZGV4KFsnc3RhdHVzJywgJ3N0YXJ0RGF0ZSddKVxyXG5ASW5kZXgoWyd0aHJlYXRBY3RvcklkJ10pXHJcbkBJbmRleChbJ29iamVjdGl2ZXMnXSwgeyB3aGVyZTogJ29iamVjdGl2ZXMgSVMgTk9UIE5VTEwnIH0pXHJcbkBJbmRleChbJ3RhcmdldGVkU2VjdG9ycyddLCB7IHdoZXJlOiAndGFyZ2V0ZWRfc2VjdG9ycyBJUyBOT1QgTlVMTCcgfSlcclxuZXhwb3J0IGNsYXNzIFRocmVhdENhbXBhaWduIHtcclxuICBAUHJpbWFyeUdlbmVyYXRlZENvbHVtbigndXVpZCcpXHJcbiAgaWQ6IHN0cmluZztcclxuXHJcbiAgQENvbHVtbih7IHR5cGU6ICd2YXJjaGFyJywgbGVuZ3RoOiAyNTUgfSlcclxuICBASXNTdHJpbmcoKVxyXG4gIG5hbWU6IHN0cmluZztcclxuXHJcbiAgQENvbHVtbih7IHR5cGU6ICd0ZXh0JywgYXJyYXk6IHRydWUsIG51bGxhYmxlOiB0cnVlIH0pXHJcbiAgQElzT3B0aW9uYWwoKVxyXG4gIEBJc0FycmF5KClcclxuICBhbGlhc2VzPzogc3RyaW5nW107XHJcblxyXG4gIEBDb2x1bW4oeyB0eXBlOiAndGV4dCcgfSlcclxuICBASXNTdHJpbmcoKVxyXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XHJcblxyXG4gIEBDb2x1bW4oe1xyXG4gICAgdHlwZTogJ2VudW0nLFxyXG4gICAgZW51bTogQ2FtcGFpZ25TdGF0dXMsXHJcbiAgICBkZWZhdWx0OiBDYW1wYWlnblN0YXR1cy5TVVNQRUNURUQsXHJcbiAgfSlcclxuICBASXNFbnVtKENhbXBhaWduU3RhdHVzKVxyXG4gIHN0YXR1czogQ2FtcGFpZ25TdGF0dXM7XHJcblxyXG4gIEBDb2x1bW4oe1xyXG4gICAgdHlwZTogJ2VudW0nLFxyXG4gICAgZW51bTogQ2FtcGFpZ25Tb3BoaXN0aWNhdGlvbixcclxuICAgIGRlZmF1bHQ6IENhbXBhaWduU29waGlzdGljYXRpb24uTUVESVVNLFxyXG4gIH0pXHJcbiAgQElzRW51bShDYW1wYWlnblNvcGhpc3RpY2F0aW9uKVxyXG4gIHNvcGhpc3RpY2F0aW9uOiBDYW1wYWlnblNvcGhpc3RpY2F0aW9uO1xyXG5cclxuICBAQ29sdW1uKHtcclxuICAgIHR5cGU6ICdlbnVtJyxcclxuICAgIGVudW06IENhbXBhaWduT2JqZWN0aXZlLFxyXG4gICAgYXJyYXk6IHRydWUsXHJcbiAgfSlcclxuICBASXNBcnJheSgpXHJcbiAgb2JqZWN0aXZlczogQ2FtcGFpZ25PYmplY3RpdmVbXTtcclxuXHJcbiAgQENvbHVtbih7IHR5cGU6ICd0aW1lc3RhbXAgd2l0aCB0aW1lIHpvbmUnIH0pXHJcbiAgQElzRGF0ZSgpXHJcbiAgc3RhcnREYXRlOiBEYXRlO1xyXG5cclxuICBAQ29sdW1uKHsgdHlwZTogJ3RpbWVzdGFtcCB3aXRoIHRpbWUgem9uZScsIG51bGxhYmxlOiB0cnVlIH0pXHJcbiAgQElzT3B0aW9uYWwoKVxyXG4gIEBJc0RhdGUoKVxyXG4gIGVuZERhdGU/OiBEYXRlO1xyXG5cclxuICBAQ29sdW1uKHsgdHlwZTogJ3RpbWVzdGFtcCB3aXRoIHRpbWUgem9uZScsIG51bGxhYmxlOiB0cnVlIH0pXHJcbiAgQElzT3B0aW9uYWwoKVxyXG4gIEBJc0RhdGUoKVxyXG4gIGxhc3RBY3Rpdml0eT86IERhdGU7XHJcblxyXG4gIEBDb2x1bW4oeyB0eXBlOiAndGV4dCcsIGFycmF5OiB0cnVlLCBudWxsYWJsZTogdHJ1ZSB9KVxyXG4gIEBJc09wdGlvbmFsKClcclxuICBASXNBcnJheSgpXHJcbiAgdGFyZ2V0ZWRTZWN0b3JzPzogc3RyaW5nW107XHJcblxyXG4gIEBDb2x1bW4oeyB0eXBlOiAndGV4dCcsIGFycmF5OiB0cnVlLCBudWxsYWJsZTogdHJ1ZSB9KVxyXG4gIEBJc09wdGlvbmFsKClcclxuICBASXNBcnJheSgpXHJcbiAgdGFyZ2V0ZWRDb3VudHJpZXM/OiBzdHJpbmdbXTtcclxuXHJcbiAgQENvbHVtbih7IHR5cGU6ICd0ZXh0JywgYXJyYXk6IHRydWUsIG51bGxhYmxlOiB0cnVlIH0pXHJcbiAgQElzT3B0aW9uYWwoKVxyXG4gIEBJc0FycmF5KClcclxuICB0YXJnZXRlZFRlY2hub2xvZ2llcz86IHN0cmluZ1tdO1xyXG5cclxuICBAQ29sdW1uKHsgdHlwZTogJ2pzb25iJywgYXJyYXk6IHRydWUsIG51bGxhYmxlOiB0cnVlIH0pXHJcbiAgQElzT3B0aW9uYWwoKVxyXG4gIEBJc0FycmF5KClcclxuICB2aWN0aW1zPzogVmljdGltSW5mb1tdO1xyXG5cclxuICBAQ29sdW1uKHsgdHlwZTogJ2pzb25iJywgYXJyYXk6IHRydWUsIG51bGxhYmxlOiB0cnVlIH0pXHJcbiAgQElzT3B0aW9uYWwoKVxyXG4gIEBJc0FycmF5KClcclxuICBhdHRhY2tWZWN0b3JzPzogQXR0YWNrVmVjdG9yW107XHJcblxyXG4gIEBDb2x1bW4oeyB0eXBlOiAnanNvbmInLCBhcnJheTogdHJ1ZSwgbnVsbGFibGU6IHRydWUgfSlcclxuICBASXNPcHRpb25hbCgpXHJcbiAgQElzQXJyYXkoKVxyXG4gIHRpbWVsaW5lPzogQ2FtcGFpZ25UaW1lbGluZVtdO1xyXG5cclxuICBAQ29sdW1uKHsgdHlwZTogJ3RleHQnLCBhcnJheTogdHJ1ZSwgbnVsbGFibGU6IHRydWUgfSlcclxuICBASXNPcHRpb25hbCgpXHJcbiAgQElzQXJyYXkoKVxyXG4gIG1hbHdhcmVGYW1pbGllcz86IHN0cmluZ1tdO1xyXG5cclxuICBAQ29sdW1uKHsgdHlwZTogJ3RleHQnLCBhcnJheTogdHJ1ZSwgbnVsbGFibGU6IHRydWUgfSlcclxuICBASXNPcHRpb25hbCgpXHJcbiAgQElzQXJyYXkoKVxyXG4gIHRvb2xzVXNlZD86IHN0cmluZ1tdO1xyXG5cclxuICBAQ29sdW1uKHsgdHlwZTogJ2pzb25iJywgbnVsbGFibGU6IHRydWUgfSlcclxuICBASXNPcHRpb25hbCgpXHJcbiAgQElzT2JqZWN0KClcclxuICBpbmZyYXN0cnVjdHVyZT86IHtcclxuICAgIGRvbWFpbnM/OiBzdHJpbmdbXTtcclxuICAgIGlwQWRkcmVzc2VzPzogc3RyaW5nW107XHJcbiAgICBjZXJ0aWZpY2F0ZXM/OiBzdHJpbmdbXTtcclxuICAgIGVtYWlscz86IHN0cmluZ1tdO1xyXG4gIH07XHJcblxyXG4gIEBDb2x1bW4oeyB0eXBlOiAndGV4dCcsIGFycmF5OiB0cnVlLCBudWxsYWJsZTogdHJ1ZSB9KVxyXG4gIEBJc09wdGlvbmFsKClcclxuICBASXNBcnJheSgpXHJcbiAgcmVmZXJlbmNlcz86IHN0cmluZ1tdO1xyXG5cclxuICBAQ29sdW1uKHsgdHlwZTogJ3RleHQnLCBhcnJheTogdHJ1ZSwgbnVsbGFibGU6IHRydWUgfSlcclxuICBASXNPcHRpb25hbCgpXHJcbiAgQElzQXJyYXkoKVxyXG4gIHRhZ3M/OiBzdHJpbmdbXTtcclxuXHJcbiAgQENvbHVtbih7IHR5cGU6ICdpbnRlZ2VyJywgZGVmYXVsdDogMCB9KVxyXG4gIEBJc051bWJlcigpXHJcbiAgdmljdGltQ291bnQ6IG51bWJlcjtcclxuXHJcbiAgQENvbHVtbih7IHR5cGU6ICdpbnRlZ2VyJywgZGVmYXVsdDogMCB9KVxyXG4gIEBJc051bWJlcigpXHJcbiAgaW5kaWNhdG9yQ291bnQ6IG51bWJlcjtcclxuXHJcbiAgQENvbHVtbih7IHR5cGU6ICdkZWNpbWFsJywgcHJlY2lzaW9uOiAzLCBzY2FsZTogMSwgbnVsbGFibGU6IHRydWUgfSlcclxuICBASXNPcHRpb25hbCgpXHJcbiAgQElzTnVtYmVyKClcclxuICBpbXBhY3RTY29yZT86IG51bWJlcjtcclxuXHJcbiAgQENvbHVtbih7IHR5cGU6ICdkZWNpbWFsJywgcHJlY2lzaW9uOiAzLCBzY2FsZTogMSwgbnVsbGFibGU6IHRydWUgfSlcclxuICBASXNPcHRpb25hbCgpXHJcbiAgQElzTnVtYmVyKClcclxuICBjb25maWRlbmNlU2NvcmU/OiBudW1iZXI7XHJcblxyXG4gIEBDb2x1bW4oeyB0eXBlOiAnanNvbmInLCBudWxsYWJsZTogdHJ1ZSB9KVxyXG4gIEBJc09wdGlvbmFsKClcclxuICBASXNPYmplY3QoKVxyXG4gIGN1c3RvbUF0dHJpYnV0ZXM/OiBSZWNvcmQ8c3RyaW5nLCBhbnk+O1xyXG5cclxuICAvLyBSZWxhdGlvbnNoaXBzXHJcbiAgQE1hbnlUb09uZSgoKSA9PiBUaHJlYXRBY3RvciwgKGFjdG9yKSA9PiBhY3Rvci5jYW1wYWlnbnMsIHsgbnVsbGFibGU6IHRydWUgfSlcclxuICBASm9pbkNvbHVtbih7IG5hbWU6ICd0aHJlYXRfYWN0b3JfaWQnIH0pXHJcbiAgdGhyZWF0QWN0b3I/OiBUaHJlYXRBY3RvcjtcclxuXHJcbiAgQENvbHVtbih7IHR5cGU6ICd1dWlkJywgbnVsbGFibGU6IHRydWUgfSlcclxuICB0aHJlYXRBY3RvcklkPzogc3RyaW5nO1xyXG5cclxuICBAT25lVG9NYW55KCgpID0+IFRocmVhdEludGVsbGlnZW5jZSwgKHRocmVhdCkgPT4gdGhyZWF0LnRocmVhdENhbXBhaWduKVxyXG4gIHRocmVhdEludGVsbGlnZW5jZTogVGhyZWF0SW50ZWxsaWdlbmNlW107XHJcblxyXG4gIEBDcmVhdGVEYXRlQ29sdW1uKClcclxuICBjcmVhdGVkQXQ6IERhdGU7XHJcblxyXG4gIEBVcGRhdGVEYXRlQ29sdW1uKClcclxuICB1cGRhdGVkQXQ6IERhdGU7XHJcblxyXG4gIC8qKlxyXG4gICAqIENoZWNrIGlmIGNhbXBhaWduIGlzIGN1cnJlbnRseSBhY3RpdmVcclxuICAgKi9cclxuICBnZXQgaXNBY3RpdmUoKTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gdGhpcy5zdGF0dXMgPT09IENhbXBhaWduU3RhdHVzLkFDVElWRTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENoZWNrIGlmIGNhbXBhaWduIGlzIG9uZ29pbmcgKG5vdCBjb21wbGV0ZWQpXHJcbiAgICovXHJcbiAgZ2V0IGlzT25nb2luZygpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLnN0YXR1cyA9PT0gQ2FtcGFpZ25TdGF0dXMuQUNUSVZFIHx8IFxyXG4gICAgICAgICAgIHRoaXMuc3RhdHVzID09PSBDYW1wYWlnblN0YXR1cy5TVVNQRUNURUQgfHxcclxuICAgICAgICAgICB0aGlzLnN0YXR1cyA9PT0gQ2FtcGFpZ25TdGF0dXMuVU5ERVJfSU5WRVNUSUdBVElPTjtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCBjYW1wYWlnbiBkdXJhdGlvbiBpbiBkYXlzXHJcbiAgICovXHJcbiAgZ2V0IGR1cmF0aW9uSW5EYXlzKCk6IG51bWJlciB7XHJcbiAgICBjb25zdCBlbmREYXRlID0gdGhpcy5lbmREYXRlIHx8IG5ldyBEYXRlKCk7XHJcbiAgICByZXR1cm4gTWF0aC5mbG9vcigoZW5kRGF0ZS5nZXRUaW1lKCkgLSB0aGlzLnN0YXJ0RGF0ZS5nZXRUaW1lKCkpIC8gKDEwMDAgKiA2MCAqIDYwICogMjQpKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCBkYXlzIHNpbmNlIGxhc3QgYWN0aXZpdHlcclxuICAgKi9cclxuICBnZXQgZGF5c1NpbmNlTGFzdEFjdGl2aXR5KCk6IG51bWJlciB7XHJcbiAgICBjb25zdCBsYXN0QWN0aXZpdHkgPSB0aGlzLmxhc3RBY3Rpdml0eSB8fCB0aGlzLnN0YXJ0RGF0ZTtcclxuICAgIHJldHVybiBNYXRoLmZsb29yKChEYXRlLm5vdygpIC0gbGFzdEFjdGl2aXR5LmdldFRpbWUoKSkgLyAoMTAwMCAqIDYwICogNjAgKiAyNCkpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IGNhbXBhaWduIHN1bW1hcnlcclxuICAgKi9cclxuICBnZXRTdW1tYXJ5KCk6IGFueSB7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBpZDogdGhpcy5pZCxcclxuICAgICAgbmFtZTogdGhpcy5uYW1lLFxyXG4gICAgICBhbGlhc2VzOiB0aGlzLmFsaWFzZXMsXHJcbiAgICAgIHN0YXR1czogdGhpcy5zdGF0dXMsXHJcbiAgICAgIHNvcGhpc3RpY2F0aW9uOiB0aGlzLnNvcGhpc3RpY2F0aW9uLFxyXG4gICAgICBvYmplY3RpdmVzOiB0aGlzLm9iamVjdGl2ZXMsXHJcbiAgICAgIGlzQWN0aXZlOiB0aGlzLmlzQWN0aXZlLFxyXG4gICAgICBpc09uZ29pbmc6IHRoaXMuaXNPbmdvaW5nLFxyXG4gICAgICBkdXJhdGlvbkluRGF5czogdGhpcy5kdXJhdGlvbkluRGF5cyxcclxuICAgICAgZGF5c1NpbmNlTGFzdEFjdGl2aXR5OiB0aGlzLmRheXNTaW5jZUxhc3RBY3Rpdml0eSxcclxuICAgICAgdmljdGltQ291bnQ6IHRoaXMudmljdGltQ291bnQsXHJcbiAgICAgIGluZGljYXRvckNvdW50OiB0aGlzLmluZGljYXRvckNvdW50LFxyXG4gICAgICBpbXBhY3RTY29yZTogdGhpcy5pbXBhY3RTY29yZSxcclxuICAgICAgY29uZmlkZW5jZVNjb3JlOiB0aGlzLmNvbmZpZGVuY2VTY29yZSxcclxuICAgICAgdGhyZWF0QWN0b3I6IHRoaXMudGhyZWF0QWN0b3I/Lm5hbWUsXHJcbiAgICAgIHRhcmdldGVkU2VjdG9yczogdGhpcy50YXJnZXRlZFNlY3RvcnMsXHJcbiAgICAgIHRhcmdldGVkQ291bnRyaWVzOiB0aGlzLnRhcmdldGVkQ291bnRyaWVzLFxyXG4gICAgICB0YWdzOiB0aGlzLnRhZ3MsXHJcbiAgICB9O1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQWRkIHZpY3RpbSB0byBjYW1wYWlnblxyXG4gICAqL1xyXG4gIGFkZFZpY3RpbSh2aWN0aW06IFZpY3RpbUluZm8pOiB2b2lkIHtcclxuICAgIGlmICghdGhpcy52aWN0aW1zKSB7XHJcbiAgICAgIHRoaXMudmljdGltcyA9IFtdO1xyXG4gICAgfVxyXG5cclxuICAgIHRoaXMudmljdGltcy5wdXNoKHZpY3RpbSk7XHJcbiAgICB0aGlzLnZpY3RpbUNvdW50ID0gdGhpcy52aWN0aW1zLmxlbmd0aDtcclxuICAgIHRoaXMubGFzdEFjdGl2aXR5ID0gbmV3IERhdGUoKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEFkZCBhdHRhY2sgdmVjdG9yXHJcbiAgICovXHJcbiAgYWRkQXR0YWNrVmVjdG9yKHZlY3RvcjogQXR0YWNrVmVjdG9yKTogdm9pZCB7XHJcbiAgICBpZiAoIXRoaXMuYXR0YWNrVmVjdG9ycykge1xyXG4gICAgICB0aGlzLmF0dGFja1ZlY3RvcnMgPSBbXTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBSZW1vdmUgZXhpc3RpbmcgdmVjdG9yIHdpdGggc2FtZSBuYW1lXHJcbiAgICB0aGlzLmF0dGFja1ZlY3RvcnMgPSB0aGlzLmF0dGFja1ZlY3RvcnMuZmlsdGVyKHYgPT4gdi52ZWN0b3IgIT09IHZlY3Rvci52ZWN0b3IpO1xyXG4gICAgdGhpcy5hdHRhY2tWZWN0b3JzLnB1c2godmVjdG9yKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEFkZCB0aW1lbGluZSBwaGFzZVxyXG4gICAqL1xyXG4gIGFkZFRpbWVsaW5lUGhhc2UocGhhc2U6IENhbXBhaWduVGltZWxpbmUpOiB2b2lkIHtcclxuICAgIGlmICghdGhpcy50aW1lbGluZSkge1xyXG4gICAgICB0aGlzLnRpbWVsaW5lID0gW107XHJcbiAgICB9XHJcblxyXG4gICAgdGhpcy50aW1lbGluZS5wdXNoKHBoYXNlKTtcclxuICAgIC8vIFNvcnQgYnkgc3RhcnQgZGF0ZVxyXG4gICAgdGhpcy50aW1lbGluZS5zb3J0KChhLCBiKSA9PiBhLnN0YXJ0RGF0ZS5nZXRUaW1lKCkgLSBiLnN0YXJ0RGF0ZS5nZXRUaW1lKCkpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogVXBkYXRlIGNhbXBhaWduIGFjdGl2aXR5XHJcbiAgICovXHJcbiAgcmVjb3JkQWN0aXZpdHkoKTogdm9pZCB7XHJcbiAgICB0aGlzLmxhc3RBY3Rpdml0eSA9IG5ldyBEYXRlKCk7XHJcbiAgICBpZiAodGhpcy5zdGF0dXMgPT09IENhbXBhaWduU3RhdHVzLklOQUNUSVZFKSB7XHJcbiAgICAgIHRoaXMuc3RhdHVzID0gQ2FtcGFpZ25TdGF0dXMuQUNUSVZFO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2FsY3VsYXRlIGltcGFjdCBzY29yZSBiYXNlZCBvbiB2YXJpb3VzIGZhY3RvcnNcclxuICAgKi9cclxuICBjYWxjdWxhdGVJbXBhY3RTY29yZSgpOiBudW1iZXIge1xyXG4gICAgbGV0IHNjb3JlID0gMDtcclxuXHJcbiAgICAvLyBCYXNlIHNjb3JlIGZyb20gdmljdGltIGNvdW50XHJcbiAgICBpZiAodGhpcy52aWN0aW1Db3VudCA+IDEwMCkge1xyXG4gICAgICBzY29yZSArPSA5LjA7XHJcbiAgICB9IGVsc2UgaWYgKHRoaXMudmljdGltQ291bnQgPiA1MCkge1xyXG4gICAgICBzY29yZSArPSA3LjA7XHJcbiAgICB9IGVsc2UgaWYgKHRoaXMudmljdGltQ291bnQgPiAxMCkge1xyXG4gICAgICBzY29yZSArPSA1LjA7XHJcbiAgICB9IGVsc2UgaWYgKHRoaXMudmljdGltQ291bnQgPiAwKSB7XHJcbiAgICAgIHNjb3JlICs9IDMuMDtcclxuICAgIH1cclxuXHJcbiAgICAvLyBTb3BoaXN0aWNhdGlvbiBtb2RpZmllclxyXG4gICAgY29uc3Qgc29waGlzdGljYXRpb25Nb2RpZmllcnMgPSB7XHJcbiAgICAgIFtDYW1wYWlnblNvcGhpc3RpY2F0aW9uLkxPV106IDAuNyxcclxuICAgICAgW0NhbXBhaWduU29waGlzdGljYXRpb24uTUVESVVNXTogMS4wLFxyXG4gICAgICBbQ2FtcGFpZ25Tb3BoaXN0aWNhdGlvbi5ISUdIXTogMS4zLFxyXG4gICAgICBbQ2FtcGFpZ25Tb3BoaXN0aWNhdGlvbi5BRFZBTkNFRF06IDEuNSxcclxuICAgIH07XHJcbiAgICBzY29yZSAqPSBzb3BoaXN0aWNhdGlvbk1vZGlmaWVyc1t0aGlzLnNvcGhpc3RpY2F0aW9uXTtcclxuXHJcbiAgICAvLyBPYmplY3RpdmUgc2V2ZXJpdHlcclxuICAgIGNvbnN0IGhpZ2hJbXBhY3RPYmplY3RpdmVzID0gW1xyXG4gICAgICBDYW1wYWlnbk9iamVjdGl2ZS5TQUJPVEFHRSxcclxuICAgICAgQ2FtcGFpZ25PYmplY3RpdmUuUkFOU09NV0FSRSxcclxuICAgICAgQ2FtcGFpZ25PYmplY3RpdmUuREFUQV9USEVGVCxcclxuICAgICAgQ2FtcGFpZ25PYmplY3RpdmUuRVNQSU9OQUdFLFxyXG4gICAgXTtcclxuICAgIFxyXG4gICAgY29uc3QgaGFzSGlnaEltcGFjdE9iamVjdGl2ZSA9IHRoaXMub2JqZWN0aXZlcy5zb21lKG9iaiA9PiBcclxuICAgICAgaGlnaEltcGFjdE9iamVjdGl2ZXMuaW5jbHVkZXMob2JqKVxyXG4gICAgKTtcclxuICAgIFxyXG4gICAgaWYgKGhhc0hpZ2hJbXBhY3RPYmplY3RpdmUpIHtcclxuICAgICAgc2NvcmUgKz0gMS4wO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIENyaXRpY2FsIHNlY3RvciB0YXJnZXRpbmdcclxuICAgIGNvbnN0IGNyaXRpY2FsU2VjdG9ycyA9IFtcclxuICAgICAgJ2ZpbmFuY2lhbCcsXHJcbiAgICAgICdoZWFsdGhjYXJlJyxcclxuICAgICAgJ2VuZXJneScsXHJcbiAgICAgICdnb3Zlcm5tZW50JyxcclxuICAgICAgJ2RlZmVuc2UnLFxyXG4gICAgICAndGVsZWNvbW11bmljYXRpb25zJyxcclxuICAgIF07XHJcbiAgICBcclxuICAgIGNvbnN0IHRhcmdldHNDcml0aWNhbFNlY3RvciA9IHRoaXMudGFyZ2V0ZWRTZWN0b3JzPy5zb21lKHNlY3RvciA9PiBcclxuICAgICAgY3JpdGljYWxTZWN0b3JzLmluY2x1ZGVzKHNlY3Rvci50b0xvd2VyQ2FzZSgpKVxyXG4gICAgKTtcclxuICAgIFxyXG4gICAgaWYgKHRhcmdldHNDcml0aWNhbFNlY3Rvcikge1xyXG4gICAgICBzY29yZSArPSAxLjA7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gRHVyYXRpb24gZmFjdG9yXHJcbiAgICBpZiAodGhpcy5kdXJhdGlvbkluRGF5cyA+IDM2NSkge1xyXG4gICAgICBzY29yZSArPSAxLjA7IC8vIExvbmctcnVubmluZyBjYW1wYWlnblxyXG4gICAgfSBlbHNlIGlmICh0aGlzLmR1cmF0aW9uSW5EYXlzID4gOTApIHtcclxuICAgICAgc2NvcmUgKz0gMC41O1xyXG4gICAgfVxyXG5cclxuICAgIC8vIFJlY2VudCBhY3Rpdml0eVxyXG4gICAgaWYgKHRoaXMuZGF5c1NpbmNlTGFzdEFjdGl2aXR5IDw9IDMwKSB7XHJcbiAgICAgIHNjb3JlICs9IDAuNTtcclxuICAgIH1cclxuXHJcbiAgICB0aGlzLmltcGFjdFNjb3JlID0gTWF0aC5taW4oc2NvcmUsIDEwLjApO1xyXG4gICAgcmV0dXJuIHRoaXMuaW1wYWN0U2NvcmU7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDYWxjdWxhdGUgY29uZmlkZW5jZSBzY29yZSBiYXNlZCBvbiBhdHRyaWJ1dGlvbiBhbmQgZXZpZGVuY2VcclxuICAgKi9cclxuICBjYWxjdWxhdGVDb25maWRlbmNlU2NvcmUoKTogbnVtYmVyIHtcclxuICAgIGxldCBzY29yZSA9IDUuMDsgLy8gQmFzZSBjb25maWRlbmNlXHJcblxyXG4gICAgLy8gQXR0cmlidXRpb24gZmFjdG9yXHJcbiAgICBpZiAodGhpcy50aHJlYXRBY3Rvcikge1xyXG4gICAgICBzY29yZSArPSAyLjA7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gRXZpZGVuY2UgcXVhbnRpdHlcclxuICAgIGlmICh0aGlzLmluZGljYXRvckNvdW50ID4gNTApIHtcclxuICAgICAgc2NvcmUgKz0gMi4wO1xyXG4gICAgfSBlbHNlIGlmICh0aGlzLmluZGljYXRvckNvdW50ID4gMjApIHtcclxuICAgICAgc2NvcmUgKz0gMS4wO1xyXG4gICAgfSBlbHNlIGlmICh0aGlzLmluZGljYXRvckNvdW50ID4gNSkge1xyXG4gICAgICBzY29yZSArPSAwLjU7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gVmljdGltIGNvbmZpcm1hdGlvblxyXG4gICAgY29uc3QgY29uZmlybWVkVmljdGltcyA9IHRoaXMudmljdGltcz8uZmlsdGVyKHYgPT4gdi5jb25maXJtZWREYXRlKSB8fCBbXTtcclxuICAgIGlmIChjb25maXJtZWRWaWN0aW1zLmxlbmd0aCA+IDApIHtcclxuICAgICAgc2NvcmUgKz0gMS4wO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIFJlZmVyZW5jZSBxdWFsaXR5XHJcbiAgICBpZiAodGhpcy5yZWZlcmVuY2VzICYmIHRoaXMucmVmZXJlbmNlcy5sZW5ndGggPiAzKSB7XHJcbiAgICAgIHNjb3JlICs9IDAuNTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBUaW1lbGluZSBjb21wbGV0ZW5lc3NcclxuICAgIGlmICh0aGlzLnRpbWVsaW5lICYmIHRoaXMudGltZWxpbmUubGVuZ3RoID4gMykge1xyXG4gICAgICBzY29yZSArPSAwLjU7XHJcbiAgICB9XHJcblxyXG4gICAgdGhpcy5jb25maWRlbmNlU2NvcmUgPSBNYXRoLm1pbihzY29yZSwgMTAuMCk7XHJcbiAgICByZXR1cm4gdGhpcy5jb25maWRlbmNlU2NvcmU7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDaGVjayBpZiBjYW1wYWlnbiB0YXJnZXRzIHNwZWNpZmljIHNlY3RvclxyXG4gICAqL1xyXG4gIHRhcmdldHNTZWN0b3Ioc2VjdG9yOiBzdHJpbmcpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLnRhcmdldGVkU2VjdG9ycyA/IFxyXG4gICAgICB0aGlzLnRhcmdldGVkU2VjdG9ycy5zb21lKHMgPT4gcy50b0xvd2VyQ2FzZSgpID09PSBzZWN0b3IudG9Mb3dlckNhc2UoKSkgOiBcclxuICAgICAgZmFsc2U7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDaGVjayBpZiBjYW1wYWlnbiB0YXJnZXRzIHNwZWNpZmljIGNvdW50cnlcclxuICAgKi9cclxuICB0YXJnZXRzQ291bnRyeShjb3VudHJ5OiBzdHJpbmcpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLnRhcmdldGVkQ291bnRyaWVzID8gXHJcbiAgICAgIHRoaXMudGFyZ2V0ZWRDb3VudHJpZXMuc29tZShjID0+IGMudG9Mb3dlckNhc2UoKSA9PT0gY291bnRyeS50b0xvd2VyQ2FzZSgpKSA6IFxyXG4gICAgICBmYWxzZTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCBtb3N0IGltcGFjdGVkIHNlY3RvcnNcclxuICAgKi9cclxuICBnZXRNb3N0SW1wYWN0ZWRTZWN0b3JzKCk6IEFycmF5PHsgc2VjdG9yOiBzdHJpbmc7IGNvdW50OiBudW1iZXIgfT4ge1xyXG4gICAgaWYgKCF0aGlzLnZpY3RpbXMpIHJldHVybiBbXTtcclxuXHJcbiAgICBjb25zdCBzZWN0b3JDb3VudHMgPSBuZXcgTWFwPHN0cmluZywgbnVtYmVyPigpO1xyXG4gICAgXHJcbiAgICB0aGlzLnZpY3RpbXMuZm9yRWFjaCh2aWN0aW0gPT4ge1xyXG4gICAgICBjb25zdCBjb3VudCA9IHNlY3RvckNvdW50cy5nZXQodmljdGltLnNlY3RvcikgfHwgMDtcclxuICAgICAgc2VjdG9yQ291bnRzLnNldCh2aWN0aW0uc2VjdG9yLCBjb3VudCArIDEpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgcmV0dXJuIEFycmF5LmZyb20oc2VjdG9yQ291bnRzLmVudHJpZXMoKSlcclxuICAgICAgLm1hcCgoW3NlY3RvciwgY291bnRdKSA9PiAoeyBzZWN0b3IsIGNvdW50IH0pKVxyXG4gICAgICAuc29ydCgoYSwgYikgPT4gYi5jb3VudCAtIGEuY291bnQpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogRXhwb3J0IGNhbXBhaWduIGZvciByZXBvcnRpbmdcclxuICAgKi9cclxuICBleHBvcnRGb3JSZXBvcnRpbmcoKTogYW55IHtcclxuICAgIHJldHVybiB7XHJcbiAgICAgIGlkOiB0aGlzLmlkLFxyXG4gICAgICBuYW1lOiB0aGlzLm5hbWUsXHJcbiAgICAgIGFsaWFzZXM6IHRoaXMuYWxpYXNlcyxcclxuICAgICAgZGVzY3JpcHRpb246IHRoaXMuZGVzY3JpcHRpb24sXHJcbiAgICAgIHN0YXR1czogdGhpcy5zdGF0dXMsXHJcbiAgICAgIHNvcGhpc3RpY2F0aW9uOiB0aGlzLnNvcGhpc3RpY2F0aW9uLFxyXG4gICAgICBvYmplY3RpdmVzOiB0aGlzLm9iamVjdGl2ZXMsXHJcbiAgICAgIHN0YXJ0RGF0ZTogdGhpcy5zdGFydERhdGUsXHJcbiAgICAgIGVuZERhdGU6IHRoaXMuZW5kRGF0ZSxcclxuICAgICAgbGFzdEFjdGl2aXR5OiB0aGlzLmxhc3RBY3Rpdml0eSxcclxuICAgICAgZHVyYXRpb25JbkRheXM6IHRoaXMuZHVyYXRpb25JbkRheXMsXHJcbiAgICAgIGRheXNTaW5jZUxhc3RBY3Rpdml0eTogdGhpcy5kYXlzU2luY2VMYXN0QWN0aXZpdHksXHJcbiAgICAgIHRhcmdldGVkU2VjdG9yczogdGhpcy50YXJnZXRlZFNlY3RvcnMsXHJcbiAgICAgIHRhcmdldGVkQ291bnRyaWVzOiB0aGlzLnRhcmdldGVkQ291bnRyaWVzLFxyXG4gICAgICB0YXJnZXRlZFRlY2hub2xvZ2llczogdGhpcy50YXJnZXRlZFRlY2hub2xvZ2llcyxcclxuICAgICAgdmljdGltQ291bnQ6IHRoaXMudmljdGltQ291bnQsXHJcbiAgICAgIGluZGljYXRvckNvdW50OiB0aGlzLmluZGljYXRvckNvdW50LFxyXG4gICAgICBpbXBhY3RTY29yZTogdGhpcy5pbXBhY3RTY29yZSxcclxuICAgICAgY29uZmlkZW5jZVNjb3JlOiB0aGlzLmNvbmZpZGVuY2VTY29yZSxcclxuICAgICAgdGhyZWF0QWN0b3I6IHRoaXMudGhyZWF0QWN0b3I/Lm5hbWUsXHJcbiAgICAgIHRocmVhdEFjdG9ySWQ6IHRoaXMudGhyZWF0QWN0b3JJZCxcclxuICAgICAgbWFsd2FyZUZhbWlsaWVzOiB0aGlzLm1hbHdhcmVGYW1pbGllcyxcclxuICAgICAgdG9vbHNVc2VkOiB0aGlzLnRvb2xzVXNlZCxcclxuICAgICAgbW9zdEltcGFjdGVkU2VjdG9yczogdGhpcy5nZXRNb3N0SW1wYWN0ZWRTZWN0b3JzKCksXHJcbiAgICAgIGF0dGFja1ZlY3RvcnM6IHRoaXMuYXR0YWNrVmVjdG9ycyxcclxuICAgICAgdGltZWxpbmU6IHRoaXMudGltZWxpbmUsXHJcbiAgICAgIGluZnJhc3RydWN0dXJlOiB0aGlzLmluZnJhc3RydWN0dXJlLFxyXG4gICAgICB0YWdzOiB0aGlzLnRhZ3MsXHJcbiAgICAgIHJlZmVyZW5jZXM6IHRoaXMucmVmZXJlbmNlcyxcclxuICAgICAgY3JlYXRlZEF0OiB0aGlzLmNyZWF0ZWRBdCxcclxuICAgICAgdXBkYXRlZEF0OiB0aGlzLnVwZGF0ZWRBdCxcclxuICAgIH07XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDb21wbGV0ZSBjYW1wYWlnblxyXG4gICAqL1xyXG4gIGNvbXBsZXRlKCk6IHZvaWQge1xyXG4gICAgdGhpcy5zdGF0dXMgPSBDYW1wYWlnblN0YXR1cy5DT01QTEVURUQ7XHJcbiAgICB0aGlzLmVuZERhdGUgPSBuZXcgRGF0ZSgpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQWN0aXZhdGUgY2FtcGFpZ25cclxuICAgKi9cclxuICBhY3RpdmF0ZSgpOiB2b2lkIHtcclxuICAgIHRoaXMuc3RhdHVzID0gQ2FtcGFpZ25TdGF0dXMuQUNUSVZFO1xyXG4gICAgdGhpcy5sYXN0QWN0aXZpdHkgPSBuZXcgRGF0ZSgpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogRGVhY3RpdmF0ZSBjYW1wYWlnblxyXG4gICAqL1xyXG4gIGRlYWN0aXZhdGUoKTogdm9pZCB7XHJcbiAgICB0aGlzLnN0YXR1cyA9IENhbXBhaWduU3RhdHVzLklOQUNUSVZFO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogTWFyayBhcyB1bmRlciBpbnZlc3RpZ2F0aW9uXHJcbiAgICovXHJcbiAgbWFya1VuZGVySW52ZXN0aWdhdGlvbigpOiB2b2lkIHtcclxuICAgIHRoaXMuc3RhdHVzID0gQ2FtcGFpZ25TdGF0dXMuVU5ERVJfSU5WRVNUSUdBVElPTjtcclxuICB9XHJcbn1cclxuIl0sInZlcnNpb24iOjN9