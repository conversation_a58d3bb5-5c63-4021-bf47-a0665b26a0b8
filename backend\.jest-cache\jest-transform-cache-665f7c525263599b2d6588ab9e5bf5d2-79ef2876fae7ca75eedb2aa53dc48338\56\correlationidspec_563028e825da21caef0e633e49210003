3e15f088d533e09ad079ce2c52b5c753
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const correlation_id_value_object_1 = require("../../value-objects/correlation-id.value-object");
const unique_entity_id_value_object_1 = require("../../value-objects/unique-entity-id.value-object");
describe('CorrelationId', () => {
    describe('creation', () => {
        it('should create a valid correlation ID from UUID string', () => {
            const uuid = '123e4567-e89b-42d3-a456-************';
            const correlationId = correlation_id_value_object_1.CorrelationId.fromString(uuid);
            expect(correlationId.value).toBe(uuid);
            expect(correlationId.isValid()).toBe(true);
        });
        it('should generate a new correlation ID', () => {
            const correlationId = correlation_id_value_object_1.CorrelationId.generate();
            expect(correlationId.value).toBeDefined();
            expect(correlationId.isValid()).toBe(true);
            expect(correlation_id_value_object_1.CorrelationId.isValid(correlationId.value)).toBe(true);
        });
        it('should create correlation ID from UniqueEntityId', () => {
            const uniqueId = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const correlationId = correlation_id_value_object_1.CorrelationId.fromUniqueEntityId(uniqueId);
            expect(correlationId.value).toBe(uniqueId.value);
            expect(correlationId.uniqueId.equals(uniqueId)).toBe(true);
        });
        it('should create correlation ID from existing ID', () => {
            const original = correlation_id_value_object_1.CorrelationId.generate();
            const copy = correlation_id_value_object_1.CorrelationId.fromId(original);
            expect(copy.value).toBe(original.value);
            expect(copy.equals(original)).toBe(true);
            expect(copy).not.toBe(original); // Different instances
        });
    });
    describe('validation', () => {
        it('should throw error for null value', () => {
            expect(() => new correlation_id_value_object_1.CorrelationId(null)).toThrow('CorrelationId cannot be null or undefined');
        });
        it('should throw error for undefined value', () => {
            expect(() => new correlation_id_value_object_1.CorrelationId(undefined)).toThrow('CorrelationId cannot be null or undefined');
        });
        it('should throw error for empty string', () => {
            expect(() => new correlation_id_value_object_1.CorrelationId('')).toThrow('CorrelationId cannot be empty');
        });
        it('should throw error for whitespace string', () => {
            expect(() => new correlation_id_value_object_1.CorrelationId('   ')).toThrow('CorrelationId cannot be empty');
        });
        it('should throw error for non-string value', () => {
            expect(() => new correlation_id_value_object_1.CorrelationId(123)).toThrow('CorrelationId must be a string');
        });
        it('should throw error for invalid UUID format', () => {
            expect(() => new correlation_id_value_object_1.CorrelationId('invalid-uuid')).toThrow('Invalid CorrelationId format');
        });
        it('should throw error for non-UUID v4 format', () => {
            expect(() => new correlation_id_value_object_1.CorrelationId('123e4567-e89b-22d3-a456-************')).toThrow('Invalid CorrelationId format');
        });
    });
    describe('static validation methods', () => {
        it('should validate correct UUID strings', () => {
            const validUuid = '123e4567-e89b-42d3-a456-************';
            expect(correlation_id_value_object_1.CorrelationId.isValid(validUuid)).toBe(true);
        });
        it('should reject invalid UUID strings', () => {
            expect(correlation_id_value_object_1.CorrelationId.isValid('invalid-uuid')).toBe(false);
            expect(correlation_id_value_object_1.CorrelationId.isValid('')).toBe(false);
            expect(correlation_id_value_object_1.CorrelationId.isValid(null)).toBe(false);
            expect(correlation_id_value_object_1.CorrelationId.isValid(undefined)).toBe(false);
        });
        it('should try parse valid UUID', () => {
            const validUuid = '123e4567-e89b-42d3-a456-************';
            const result = correlation_id_value_object_1.CorrelationId.tryParse(validUuid);
            expect(result).not.toBeNull();
            expect(result.value).toBe(validUuid);
        });
        it('should return null for invalid UUID in tryParse', () => {
            const result = correlation_id_value_object_1.CorrelationId.tryParse('invalid-uuid');
            expect(result).toBeNull();
        });
    });
    describe('utility methods', () => {
        let correlationId;
        beforeEach(() => {
            correlationId = correlation_id_value_object_1.CorrelationId.fromString('123e4567-e89b-42d3-a456-************');
        });
        it('should get version', () => {
            expect(correlationId.getVersion()).toBe(1);
        });
        it('should get variant', () => {
            expect(correlationId.getVariant()).toBe('RFC4122');
        });
        it('should get short string representation', () => {
            const shortString = correlationId.toShortString();
            expect(shortString).toBe('123e4567');
            expect(correlationId.toShortString(4)).toBe('123e');
        });
        it('should get compact string representation', () => {
            const compactString = correlationId.toCompactString();
            expect(compactString).toBe('123e4567e89b42d3a456************');
        });
        it('should convert to uppercase', () => {
            const upperCase = correlationId.toUpperCase();
            expect(upperCase).toBe('123e4567-e89b-42d3-a456-************');
        });
        it('should convert to lowercase', () => {
            const lowerCase = correlationId.toLowerCase();
            expect(lowerCase).toBe('123e4567-e89b-42d3-a456-************');
        });
    });
    describe('hierarchical correlation', () => {
        let parentCorrelation;
        beforeEach(() => {
            parentCorrelation = correlation_id_value_object_1.CorrelationId.generate();
        });
        it('should create child correlation ID', () => {
            const child = parentCorrelation.createChild();
            expect(child).not.toEqual(parentCorrelation);
            expect(child.isValid()).toBe(true);
        });
        it('should create child correlation ID with suffix', () => {
            const child = parentCorrelation.createChild('auth');
            expect(child).not.toEqual(parentCorrelation);
            expect(child.isValid()).toBe(true);
        });
        it('should create sibling correlation ID', () => {
            const sibling = parentCorrelation.createSibling();
            expect(sibling).not.toEqual(parentCorrelation);
            expect(sibling.isValid()).toBe(true);
        });
        it('should create sibling correlation ID with suffix', () => {
            const sibling = parentCorrelation.createSibling('parallel-task');
            expect(sibling).not.toEqual(parentCorrelation);
            expect(sibling.isValid()).toBe(true);
        });
        it('should create consistent child IDs from same parent', () => {
            const child1 = parentCorrelation.createChild('test');
            const child2 = parentCorrelation.createChild('test');
            expect(child1.equals(child2)).toBe(true);
        });
        it('should create different child IDs with different suffixes', () => {
            const child1 = parentCorrelation.createChild('test1');
            const child2 = parentCorrelation.createChild('test2');
            expect(child1.equals(child2)).toBe(false);
        });
    });
    describe('equality and comparison', () => {
        it('should be equal to itself', () => {
            const correlationId = correlation_id_value_object_1.CorrelationId.generate();
            expect(correlationId.equals(correlationId)).toBe(true);
            expect(correlationId.matches(correlationId)).toBe(true);
        });
        it('should be equal to correlation ID with same value', () => {
            const uuid = '123e4567-e89b-42d3-a456-************';
            const correlationId1 = correlation_id_value_object_1.CorrelationId.fromString(uuid);
            const correlationId2 = correlation_id_value_object_1.CorrelationId.fromString(uuid);
            expect(correlationId1.equals(correlationId2)).toBe(true);
            expect(correlationId1.matches(correlationId2)).toBe(true);
        });
        it('should not be equal to correlation ID with different value', () => {
            const correlationId1 = correlation_id_value_object_1.CorrelationId.generate();
            const correlationId2 = correlation_id_value_object_1.CorrelationId.generate();
            expect(correlationId1.equals(correlationId2)).toBe(false);
            expect(correlationId1.matches(correlationId2)).toBe(false);
        });
        it('should not be equal to null or undefined', () => {
            const correlationId = correlation_id_value_object_1.CorrelationId.generate();
            expect(correlationId.equals(null)).toBe(false);
            expect(correlationId.equals(undefined)).toBe(false);
        });
        it('should not be equal to non-CorrelationId object', () => {
            const correlationId = correlation_id_value_object_1.CorrelationId.generate();
            expect(correlationId.equals({})).toBe(false);
        });
    });
    describe('factory methods', () => {
        it('should create correlation ID for request', () => {
            const correlationId = correlation_id_value_object_1.CorrelationId.forRequest();
            expect(correlationId.isValid()).toBe(true);
        });
        it('should create correlation ID for request with ID', () => {
            const requestId = 'req-123';
            const correlationId = correlation_id_value_object_1.CorrelationId.forRequest(requestId);
            expect(correlationId.isValid()).toBe(true);
            // Should be deterministic
            const correlationId2 = correlation_id_value_object_1.CorrelationId.forRequest(requestId);
            expect(correlationId.equals(correlationId2)).toBe(true);
        });
        it('should create correlation ID for operation', () => {
            const correlationId = correlation_id_value_object_1.CorrelationId.forOperation('user-login');
            expect(correlationId.isValid()).toBe(true);
        });
        it('should create correlation ID for operation with ID', () => {
            const correlationId = correlation_id_value_object_1.CorrelationId.forOperation('user-login', 'op-456');
            expect(correlationId.isValid()).toBe(true);
            // Should be deterministic
            const correlationId2 = correlation_id_value_object_1.CorrelationId.forOperation('user-login', 'op-456');
            expect(correlationId.equals(correlationId2)).toBe(true);
        });
        it('should throw error for empty operation name', () => {
            expect(() => correlation_id_value_object_1.CorrelationId.forOperation('')).toThrow('Operation name cannot be empty');
            expect(() => correlation_id_value_object_1.CorrelationId.forOperation('   ')).toThrow('Operation name cannot be empty');
        });
        it('should create correlation ID for batch', () => {
            const correlationId = correlation_id_value_object_1.CorrelationId.forBatch('batch-789');
            expect(correlationId.isValid()).toBe(true);
        });
        it('should create correlation ID for batch item', () => {
            const correlationId = correlation_id_value_object_1.CorrelationId.forBatch('batch-789', 5);
            expect(correlationId.isValid()).toBe(true);
            // Should be deterministic
            const correlationId2 = correlation_id_value_object_1.CorrelationId.forBatch('batch-789', 5);
            expect(correlationId.equals(correlationId2)).toBe(true);
        });
        it('should throw error for empty batch ID', () => {
            expect(() => correlation_id_value_object_1.CorrelationId.forBatch('')).toThrow('Batch ID cannot be empty');
            expect(() => correlation_id_value_object_1.CorrelationId.forBatch('   ')).toThrow('Batch ID cannot be empty');
        });
        it('should create correlation ID for event', () => {
            const correlationId = correlation_id_value_object_1.CorrelationId.forEvent('user-created');
            expect(correlationId.isValid()).toBe(true);
        });
        it('should create correlation ID for event with ID', () => {
            const correlationId = correlation_id_value_object_1.CorrelationId.forEvent('user-created', 'evt-123');
            expect(correlationId.isValid()).toBe(true);
            // Should be deterministic
            const correlationId2 = correlation_id_value_object_1.CorrelationId.forEvent('user-created', 'evt-123');
            expect(correlationId.equals(correlationId2)).toBe(true);
        });
        it('should throw error for empty event type', () => {
            expect(() => correlation_id_value_object_1.CorrelationId.forEvent('')).toThrow('Event type cannot be empty');
            expect(() => correlation_id_value_object_1.CorrelationId.forEvent('   ')).toThrow('Event type cannot be empty');
        });
    });
    describe('tracing context', () => {
        it('should get tracing context', () => {
            const correlationId = correlation_id_value_object_1.CorrelationId.fromString('123e4567-e89b-42d3-a456-************');
            const context = correlationId.getTracingContext();
            expect(context.correlationId).toBe('123e4567-e89b-42d3-a456-************');
            expect(context.shortId).toBe('123e4567');
            expect(context.compactId).toBe('123e4567e89b42d3a456************');
            expect(context.version).toBe(1);
            expect(context.variant).toBe('RFC4122');
        });
        it('should create HTTP headers', () => {
            const correlationId = correlation_id_value_object_1.CorrelationId.fromString('123e4567-e89b-42d3-a456-************');
            const headers = correlationId.toHeaders();
            expect(headers['X-Correlation-ID']).toBe('123e4567-e89b-42d3-a456-************');
            expect(headers['X-Correlation-ID-Short']).toBe('123e4567');
        });
        it('should create HTTP headers with custom header name', () => {
            const correlationId = correlation_id_value_object_1.CorrelationId.fromString('123e4567-e89b-42d3-a456-************');
            const headers = correlationId.toHeaders('X-Request-ID');
            expect(headers['X-Request-ID']).toBe('123e4567-e89b-42d3-a456-************');
            expect(headers['X-Request-ID-Short']).toBe('123e4567');
        });
        it('should parse correlation ID from headers', () => {
            const headers = {
                'X-Correlation-ID': '123e4567-e89b-42d3-a456-************'
            };
            const correlationId = correlation_id_value_object_1.CorrelationId.fromHeaders(headers);
            expect(correlationId).not.toBeNull();
            expect(correlationId.value).toBe('123e4567-e89b-42d3-a456-************');
        });
        it('should parse correlation ID from headers with custom header name', () => {
            const headers = {
                'X-Request-ID': '123e4567-e89b-42d3-a456-************'
            };
            const correlationId = correlation_id_value_object_1.CorrelationId.fromHeaders(headers, 'X-Request-ID');
            expect(correlationId).not.toBeNull();
            expect(correlationId.value).toBe('123e4567-e89b-42d3-a456-************');
        });
        it('should handle case-insensitive header names', () => {
            const headers = {
                'x-correlation-id': '123e4567-e89b-42d3-a456-************'
            };
            const correlationId = correlation_id_value_object_1.CorrelationId.fromHeaders(headers);
            expect(correlationId).not.toBeNull();
            expect(correlationId.value).toBe('123e4567-e89b-42d3-a456-************');
        });
        it('should handle array header values', () => {
            const headers = {
                'X-Correlation-ID': ['123e4567-e89b-42d3-a456-************', 'other-value']
            };
            const correlationId = correlation_id_value_object_1.CorrelationId.fromHeaders(headers);
            expect(correlationId).not.toBeNull();
            expect(correlationId.value).toBe('123e4567-e89b-42d3-a456-************');
        });
        it('should return null when header not found', () => {
            const headers = {};
            const correlationId = correlation_id_value_object_1.CorrelationId.fromHeaders(headers);
            expect(correlationId).toBeNull();
        });
        it('should return null for invalid header value', () => {
            const headers = {
                'X-Correlation-ID': 'invalid-uuid'
            };
            const correlationId = correlation_id_value_object_1.CorrelationId.fromHeaders(headers);
            expect(correlationId).toBeNull();
        });
    });
    describe('bulk operations', () => {
        it('should generate multiple correlation IDs', () => {
            const correlationIds = correlation_id_value_object_1.CorrelationId.generateMany(5);
            expect(correlationIds).toHaveLength(5);
            expect(correlationIds.every(id => id.isValid())).toBe(true);
            // All should be unique
            const uniqueValues = new Set(correlationIds.map(id => id.value));
            expect(uniqueValues.size).toBe(5);
        });
        it('should handle zero count for generateMany', () => {
            const correlationIds = correlation_id_value_object_1.CorrelationId.generateMany(0);
            expect(correlationIds).toHaveLength(0);
        });
        it('should throw error for negative count', () => {
            expect(() => correlation_id_value_object_1.CorrelationId.generateMany(-1)).toThrow('Count must be non-negative');
        });
    });
    describe('serialization', () => {
        it('should convert to string', () => {
            const uuid = '123e4567-e89b-42d3-a456-************';
            const correlationId = correlation_id_value_object_1.CorrelationId.fromString(uuid);
            expect(correlationId.toString()).toBe(uuid);
        });
        it('should convert to JSON', () => {
            const uuid = '123e4567-e89b-42d3-a456-************';
            const correlationId = correlation_id_value_object_1.CorrelationId.fromString(uuid);
            const json = correlationId.toJSON();
            expect(json.value).toBe(uuid);
            expect(json.type).toBe('CorrelationId');
            expect(json.version).toBe(1);
            expect(json.variant).toBe('RFC4122');
            expect(json.shortString).toBe('123e4567');
            expect(json.compactString).toBe('123e4567e89b42d3a456************');
        });
        it('should create from JSON', () => {
            const uuid = '123e4567-e89b-42d3-a456-************';
            const json = { value: uuid };
            const correlationId = correlation_id_value_object_1.CorrelationId.fromJSON(json);
            expect(correlationId.value).toBe(uuid);
        });
    });
    describe('immutability', () => {
        it('should be immutable after creation', () => {
            const correlationId = correlation_id_value_object_1.CorrelationId.generate();
            const originalValue = correlationId.value;
            // Attempt to modify (should not work due to readonly)
            expect(() => {
                correlationId._value = 'modified';
            }).not.toThrow(); // TypeScript prevents this, but runtime doesn't throw
            // Value should remain unchanged
            expect(correlationId.value).toBe(originalValue);
        });
        it('should be frozen', () => {
            const correlationId = correlation_id_value_object_1.CorrelationId.generate();
            expect(Object.isFrozen(correlationId)).toBe(true);
        });
    });
    describe('edge cases', () => {
        it('should handle case-insensitive UUID comparison', () => {
            const uuid1 = '123e4567-e89b-42d3-a456-************';
            const uuid2 = '123e4567-e89b-42d3-a456-************';
            const correlationId1 = correlation_id_value_object_1.CorrelationId.fromString(uuid1);
            const correlationId2 = correlation_id_value_object_1.CorrelationId.fromString(uuid2);
            expect(correlationId1.equals(correlationId2)).toBe(true);
        });
        it('should maintain original case in value', () => {
            const uuid = '123e4567-e89b-42d3-a456-************';
            const correlationId = correlation_id_value_object_1.CorrelationId.fromString(uuid);
            expect(correlationId.value).toBe(uuid);
        });
        it('should create deterministic correlation ID from seed', () => {
            const correlation1 = correlation_id_value_object_1.CorrelationId.fromSeed('test');
            const correlation2 = correlation_id_value_object_1.CorrelationId.fromSeed('test');
            expect(correlation1.equals(correlation2)).toBe(true);
        });
        it('should create different correlation IDs from different seeds', () => {
            const correlation1 = correlation_id_value_object_1.CorrelationId.fromSeed('test1');
            const correlation2 = correlation_id_value_object_1.CorrelationId.fromSeed('test2');
            expect(correlation1.equals(correlation2)).toBe(false);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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