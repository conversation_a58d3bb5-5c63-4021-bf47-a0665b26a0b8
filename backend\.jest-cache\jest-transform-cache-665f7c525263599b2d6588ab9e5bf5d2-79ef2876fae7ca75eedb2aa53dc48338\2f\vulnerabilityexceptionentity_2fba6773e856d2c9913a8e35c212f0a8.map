{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\domain\\entities\\vulnerability-exception.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCASiB;AACjB,iEAAuD;AACvD,yFAA+E;AAE/E;;;GAGG;AAOI,IAAM,sBAAsB,GAA5B,MAAM,sBAAsB;IA4LjC;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,KAAK,UAAU,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,IAAI,CAAC,IAAI,CAAC,cAAc;YAAE,OAAO,KAAK,CAAC;QACvC,OAAO,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,cAAc,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,IAAI,mBAAmB;QACrB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC;QAC7D,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;QAC1D,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,mBAAmB,IAAI,EAAE,IAAI,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,UAAkB,EAAE,QAAiB;QAC3C,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAE7B,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;YACnG,IAAI,eAAe,EAAE,CAAC;gBACpB,eAAe,CAAC,QAAQ,GAAG,IAAI,CAAC;gBAChC,eAAe,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;gBACtD,eAAe,CAAC,QAAQ,GAAG,QAAQ,CAAC;YACtC,CAAC;QACH,CAAC;QAED,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAkB,EAAE,MAAc;QACvC,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC;QAEjC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC7B,CAAC;QACD,IAAI,CAAC,gBAAgB,CAAC,eAAe,GAAG,MAAM,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAiB,EAAE,MAAc;QACtC,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;QACxB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC;QAEjC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC7B,CAAC;QACD,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,GAAG,MAAM,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAuB,EAAE,UAAkB,EAAE,MAAc;QAChE,IAAI,CAAC,cAAc,GAAG,iBAAiB,CAAC;QACxC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC;QAEjC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;YACtC,IAAI,CAAC,gBAAgB,CAAC,UAAU,GAAG,EAAE,CAAC;QACxC,CAAC;QAED,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC;YACpC,UAAU;YACV,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACpC,iBAAiB,EAAE,iBAAiB,CAAC,WAAW,EAAE;YAClD,MAAM;SACP,CAAC,CAAC;QAEH,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAkB,EAAE,KAAc;QACvC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC;QAEjC,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC3B,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;YAC7B,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC;gBACvC,IAAI,CAAC,gBAAgB,CAAC,WAAW,GAAG,EAAE,CAAC;YACzC,CAAC;YAED,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,IAAI,CAAC;gBACrC,UAAU;gBACV,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACpC,KAAK;aACN,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,GAAW;QAChB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,GAAW;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACK,iBAAiB;QACvB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,eAAe;YAAE,OAAO;QAE9C,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC;QAElD,QAAQ,SAAS,EAAE,CAAC;YAClB,KAAK,SAAS;gBACZ,IAAI,CAAC,cAAc,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;gBACzE,MAAM;YACR,KAAK,WAAW;gBACd,IAAI,CAAC,cAAc,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;gBACzE,MAAM;YACR,KAAK,eAAe;gBAClB,IAAI,CAAC,cAAc,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;gBAC1E,MAAM;YACR,KAAK,UAAU;gBACb,IAAI,CAAC,cAAc,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;gBAC1E,MAAM;QACV,CAAC;IACH,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;YAC7C,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE;YAC5B,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACrC,CAAC;IACJ,CAAC;CACF,CAAA;AA1aY,wDAAsB;AAEjC;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;kDACpB;AASX;IAJC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,EAAE,sBAAsB,EAAE,wBAAwB,EAAE,sBAAsB,CAAC;KACtH,CAAC;;oDACsH;AAUxH;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC;QAC/D,OAAO,EAAE,SAAS;KACnB,CAAC;;sDACkE;AAMpE;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;qDACV;AAMd;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;6DACH;AAMtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;8DAC1C;AAUxB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC;KAC5C,CAAC;;yDACgD;AAMlD;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,uBAAuB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAClD,KAAK,oBAAL,KAAK;oEAQzB;AAMH;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAC5B,KAAK,oBAAL,KAAK;2DAOhB;AAMH;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;0DAOxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;gEAYnE;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC;kDACtD,IAAI,oBAAJ,IAAI;8DAAC;AAMrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC;kDACtD,IAAI,oBAAJ,IAAI;2DAAC;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACrE,IAAI,oBAAJ,IAAI;0DAAC;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACtE,IAAI,oBAAJ,IAAI;8DAAC;AAMtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACtE,IAAI,oBAAJ,IAAI;8DAAC;AAMtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;2DAC3B;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;0DAC1C;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;0DAC1C;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;oDACtC;AAMf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAClD,MAAM,oBAAN,MAAM;gEAAc;AAGvC;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;yDAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;yDAAC;AAKhB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,oCAAa,EAAE,aAAa,CAAC,EAAE,CAAC,aAAa,CAAC,UAAU,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;IAClG,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,CAAC;kDAC1B,oCAAa,oBAAb,oCAAa;6DAAC;AAG7B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;+DAC3B;AAIxB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,oBAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;IAC/D,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;kDACzB,oBAAK,oBAAL,oBAAK;qDAAC;AAGd;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uDAC1C;iCA1LN,sBAAsB;IANlC,IAAA,gBAAM,EAAC,0BAA0B,CAAC;IAClC,IAAA,eAAK,EAAC,CAAC,iBAAiB,CAAC,CAAC;IAC1B,IAAA,eAAK,EAAC,CAAC,SAAS,CAAC,CAAC;IAClB,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,gBAAgB,CAAC,CAAC;IACzB,IAAA,eAAK,EAAC,CAAC,WAAW,CAAC,CAAC;GACR,sBAAsB,CA0alC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\domain\\entities\\vulnerability-exception.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  ManyToOne,\r\n  JoinColumn,\r\n} from 'typeorm';\r\nimport { Vulnerability } from './vulnerability.entity';\r\nimport { Asset } from '../../../asset-management/domain/entities/asset.entity';\r\n\r\n/**\r\n * Vulnerability Exception entity\r\n * Represents approved exceptions for vulnerabilities that won't be remediated\r\n */\r\n@Entity('vulnerability_exceptions')\r\n@Index(['vulnerabilityId'])\r\n@Index(['assetId'])\r\n@Index(['status'])\r\n@Index(['expirationDate'])\r\n@Index(['riskLevel'])\r\nexport class VulnerabilityException {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * Exception type\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['risk_acceptance', 'false_positive', 'compensating_control', 'business_justification', 'technical_limitation'],\r\n  })\r\n  type: 'risk_acceptance' | 'false_positive' | 'compensating_control' | 'business_justification' | 'technical_limitation';\r\n\r\n  /**\r\n   * Exception status\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['pending', 'approved', 'rejected', 'expired', 'revoked'],\r\n    default: 'pending',\r\n  })\r\n  status: 'pending' | 'approved' | 'rejected' | 'expired' | 'revoked';\r\n\r\n  /**\r\n   * Exception title/summary\r\n   */\r\n  @Column({ length: 255 })\r\n  title: string;\r\n\r\n  /**\r\n   * Detailed justification for the exception\r\n   */\r\n  @Column({ type: 'text' })\r\n  justification: string;\r\n\r\n  /**\r\n   * Business impact if vulnerability is not remediated\r\n   */\r\n  @Column({ name: 'business_impact', type: 'text', nullable: true })\r\n  businessImpact?: string;\r\n\r\n  /**\r\n   * Risk level after accepting the exception\r\n   */\r\n  @Column({\r\n    name: 'risk_level',\r\n    type: 'enum',\r\n    enum: ['low', 'medium', 'high', 'critical'],\r\n  })\r\n  riskLevel: 'low' | 'medium' | 'high' | 'critical';\r\n\r\n  /**\r\n   * Compensating controls in place\r\n   */\r\n  @Column({ name: 'compensating_controls', type: 'jsonb', nullable: true })\r\n  compensatingControls?: Array<{\r\n    type: 'technical' | 'administrative' | 'physical';\r\n    description: string;\r\n    effectiveness: 'low' | 'medium' | 'high';\r\n    implementationDate: string;\r\n    responsible: string;\r\n    verificationMethod: string;\r\n    lastVerified?: string;\r\n  }>;\r\n\r\n  /**\r\n   * Mitigation measures\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  mitigations?: Array<{\r\n    type: 'configuration' | 'network' | 'access_control' | 'monitoring' | 'process';\r\n    description: string;\r\n    effectiveness: 'low' | 'medium' | 'high';\r\n    implementation: string;\r\n    responsible: string;\r\n    status: 'planned' | 'implemented' | 'verified';\r\n  }>;\r\n\r\n  /**\r\n   * Exception conditions and requirements\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  conditions?: {\r\n    requirements?: string[];\r\n    restrictions?: string[];\r\n    monitoringRequirements?: string[];\r\n    reviewFrequency?: 'monthly' | 'quarterly' | 'semi_annually' | 'annually';\r\n    escalationCriteria?: string[];\r\n  };\r\n\r\n  /**\r\n   * Approval workflow information\r\n   */\r\n  @Column({ name: 'approval_workflow', type: 'jsonb', nullable: true })\r\n  approvalWorkflow?: {\r\n    requiredApprovers: Array<{\r\n      role: string;\r\n      userId?: string;\r\n      approved: boolean;\r\n      approvedAt?: string;\r\n      comments?: string;\r\n    }>;\r\n    currentStep: number;\r\n    totalSteps: number;\r\n    workflowType: 'standard' | 'expedited' | 'emergency';\r\n  };\r\n\r\n  /**\r\n   * Exception expiration date\r\n   */\r\n  @Column({ name: 'expiration_date', type: 'timestamp with time zone' })\r\n  expirationDate: Date;\r\n\r\n  /**\r\n   * When exception was requested\r\n   */\r\n  @Column({ name: 'requested_at', type: 'timestamp with time zone' })\r\n  requestedAt: Date;\r\n\r\n  /**\r\n   * When exception was approved\r\n   */\r\n  @Column({ name: 'approved_at', type: 'timestamp with time zone', nullable: true })\r\n  approvedAt?: Date;\r\n\r\n  /**\r\n   * When exception was last reviewed\r\n   */\r\n  @Column({ name: 'last_reviewed_at', type: 'timestamp with time zone', nullable: true })\r\n  lastReviewedAt?: Date;\r\n\r\n  /**\r\n   * Next review date\r\n   */\r\n  @Column({ name: 'next_review_date', type: 'timestamp with time zone', nullable: true })\r\n  nextReviewDate?: Date;\r\n\r\n  /**\r\n   * User who requested the exception\r\n   */\r\n  @Column({ name: 'requested_by', type: 'uuid' })\r\n  requestedBy: string;\r\n\r\n  /**\r\n   * User who approved the exception\r\n   */\r\n  @Column({ name: 'approved_by', type: 'uuid', nullable: true })\r\n  approvedBy?: string;\r\n\r\n  /**\r\n   * User who last reviewed the exception\r\n   */\r\n  @Column({ name: 'reviewed_by', type: 'uuid', nullable: true })\r\n  reviewedBy?: string;\r\n\r\n  /**\r\n   * Exception tags for categorization\r\n   */\r\n  @Column({ type: 'text', array: true, default: '{}' })\r\n  tags: string[];\r\n\r\n  /**\r\n   * Additional metadata\r\n   */\r\n  @Column({ name: 'custom_attributes', type: 'jsonb', nullable: true })\r\n  customAttributes?: Record<string, any>;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  // Relationships\r\n  @ManyToOne(() => Vulnerability, vulnerability => vulnerability.exceptions, { onDelete: 'CASCADE' })\r\n  @JoinColumn({ name: 'vulnerability_id' })\r\n  vulnerability: Vulnerability;\r\n\r\n  @Column({ name: 'vulnerability_id', type: 'uuid' })\r\n  vulnerabilityId: string;\r\n\r\n  @ManyToOne(() => Asset, { nullable: true, onDelete: 'CASCADE' })\r\n  @JoinColumn({ name: 'asset_id' })\r\n  asset?: Asset;\r\n\r\n  @Column({ name: 'asset_id', type: 'uuid', nullable: true })\r\n  assetId?: string;\r\n\r\n  /**\r\n   * Check if exception is active\r\n   */\r\n  get isActive(): boolean {\r\n    return this.status === 'approved' && !this.isExpired;\r\n  }\r\n\r\n  /**\r\n   * Check if exception is expired\r\n   */\r\n  get isExpired(): boolean {\r\n    return new Date() > this.expirationDate;\r\n  }\r\n\r\n  /**\r\n   * Check if exception is pending approval\r\n   */\r\n  get isPending(): boolean {\r\n    return this.status === 'pending';\r\n  }\r\n\r\n  /**\r\n   * Check if exception needs review\r\n   */\r\n  get needsReview(): boolean {\r\n    if (!this.nextReviewDate) return false;\r\n    return new Date() >= this.nextReviewDate;\r\n  }\r\n\r\n  /**\r\n   * Get days until expiration\r\n   */\r\n  get daysUntilExpiration(): number {\r\n    const now = new Date();\r\n    const diffMs = this.expirationDate.getTime() - now.getTime();\r\n    return Math.ceil(diffMs / (1000 * 60 * 60 * 24));\r\n  }\r\n\r\n  /**\r\n   * Get exception age in days\r\n   */\r\n  get ageInDays(): number {\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - this.requestedAt.getTime();\r\n    return Math.floor(diffMs / (1000 * 60 * 60 * 24));\r\n  }\r\n\r\n  /**\r\n   * Check if exception is expiring soon (within 30 days)\r\n   */\r\n  get isExpiringSoon(): boolean {\r\n    return this.daysUntilExpiration <= 30 && this.daysUntilExpiration > 0;\r\n  }\r\n\r\n  /**\r\n   * Approve exception\r\n   */\r\n  approve(approvedBy: string, comments?: string): void {\r\n    this.status = 'approved';\r\n    this.approvedBy = approvedBy;\r\n    this.approvedAt = new Date();\r\n    \r\n    if (this.approvalWorkflow) {\r\n      const currentApprover = this.approvalWorkflow.requiredApprovers[this.approvalWorkflow.currentStep];\r\n      if (currentApprover) {\r\n        currentApprover.approved = true;\r\n        currentApprover.approvedAt = new Date().toISOString();\r\n        currentApprover.comments = comments;\r\n      }\r\n    }\r\n\r\n    this.setNextReviewDate();\r\n  }\r\n\r\n  /**\r\n   * Reject exception\r\n   */\r\n  reject(rejectedBy: string, reason: string): void {\r\n    this.status = 'rejected';\r\n    this.reviewedBy = rejectedBy;\r\n    this.lastReviewedAt = new Date();\r\n    \r\n    if (!this.customAttributes) {\r\n      this.customAttributes = {};\r\n    }\r\n    this.customAttributes.rejectionReason = reason;\r\n  }\r\n\r\n  /**\r\n   * Revoke exception\r\n   */\r\n  revoke(revokedBy: string, reason: string): void {\r\n    this.status = 'revoked';\r\n    this.reviewedBy = revokedBy;\r\n    this.lastReviewedAt = new Date();\r\n    \r\n    if (!this.customAttributes) {\r\n      this.customAttributes = {};\r\n    }\r\n    this.customAttributes.revocationReason = reason;\r\n  }\r\n\r\n  /**\r\n   * Extend exception expiration\r\n   */\r\n  extend(newExpirationDate: Date, extendedBy: string, reason: string): void {\r\n    this.expirationDate = newExpirationDate;\r\n    this.reviewedBy = extendedBy;\r\n    this.lastReviewedAt = new Date();\r\n    \r\n    if (!this.customAttributes) {\r\n      this.customAttributes = {};\r\n    }\r\n    \r\n    if (!this.customAttributes.extensions) {\r\n      this.customAttributes.extensions = [];\r\n    }\r\n    \r\n    this.customAttributes.extensions.push({\r\n      extendedBy,\r\n      extendedAt: new Date().toISOString(),\r\n      newExpirationDate: newExpirationDate.toISOString(),\r\n      reason,\r\n    });\r\n\r\n    this.setNextReviewDate();\r\n  }\r\n\r\n  /**\r\n   * Review exception\r\n   */\r\n  review(reviewedBy: string, notes?: string): void {\r\n    this.reviewedBy = reviewedBy;\r\n    this.lastReviewedAt = new Date();\r\n    \r\n    if (notes) {\r\n      if (!this.customAttributes) {\r\n        this.customAttributes = {};\r\n      }\r\n      \r\n      if (!this.customAttributes.reviewNotes) {\r\n        this.customAttributes.reviewNotes = [];\r\n      }\r\n      \r\n      this.customAttributes.reviewNotes.push({\r\n        reviewedBy,\r\n        reviewedAt: new Date().toISOString(),\r\n        notes,\r\n      });\r\n    }\r\n\r\n    this.setNextReviewDate();\r\n  }\r\n\r\n  /**\r\n   * Add tag to exception\r\n   */\r\n  addTag(tag: string): void {\r\n    if (!this.tags.includes(tag)) {\r\n      this.tags.push(tag);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove tag from exception\r\n   */\r\n  removeTag(tag: string): void {\r\n    this.tags = this.tags.filter(t => t !== tag);\r\n  }\r\n\r\n  /**\r\n   * Set next review date based on conditions\r\n   */\r\n  private setNextReviewDate(): void {\r\n    if (!this.conditions?.reviewFrequency) return;\r\n\r\n    const now = new Date();\r\n    const frequency = this.conditions.reviewFrequency;\r\n    \r\n    switch (frequency) {\r\n      case 'monthly':\r\n        this.nextReviewDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);\r\n        break;\r\n      case 'quarterly':\r\n        this.nextReviewDate = new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000);\r\n        break;\r\n      case 'semi_annually':\r\n        this.nextReviewDate = new Date(now.getTime() + 180 * 24 * 60 * 60 * 1000);\r\n        break;\r\n      case 'annually':\r\n        this.nextReviewDate = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000);\r\n        break;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get exception summary\r\n   */\r\n  getSummary(): any {\r\n    return {\r\n      id: this.id,\r\n      type: this.type,\r\n      status: this.status,\r\n      title: this.title,\r\n      riskLevel: this.riskLevel,\r\n      vulnerabilityId: this.vulnerabilityId,\r\n      assetId: this.assetId,\r\n      isActive: this.isActive,\r\n      isExpired: this.isExpired,\r\n      isPending: this.isPending,\r\n      needsReview: this.needsReview,\r\n      isExpiringSoon: this.isExpiringSoon,\r\n      daysUntilExpiration: this.daysUntilExpiration,\r\n      ageInDays: this.ageInDays,\r\n      requestedAt: this.requestedAt,\r\n      approvedAt: this.approvedAt,\r\n      expirationDate: this.expirationDate,\r\n      nextReviewDate: this.nextReviewDate,\r\n      tags: this.tags,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Export exception for reporting\r\n   */\r\n  exportForReporting(): any {\r\n    return {\r\n      exception: this.getSummary(),\r\n      justification: this.justification,\r\n      businessImpact: this.businessImpact,\r\n      compensatingControls: this.compensatingControls,\r\n      mitigations: this.mitigations,\r\n      conditions: this.conditions,\r\n      approvalWorkflow: this.approvalWorkflow,\r\n      customAttributes: this.customAttributes,\r\n      exportedAt: new Date().toISOString(),\r\n    };\r\n  }\r\n}\r\n"], "version": 3}