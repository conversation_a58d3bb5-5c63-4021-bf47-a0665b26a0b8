ac44eb4718e8cdb9ea1d9221b4634a9a
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Threat = void 0;
const base_aggregate_root_1 = require("../../../../shared-kernel/domain/base-aggregate-root");
const threat_severity_enum_1 = require("../../enums/threat-severity.enum");
const threat_detected_event_1 = require("../../events/threat-detected.event");
const threat_severity_changed_event_1 = require("../../events/threat-severity-changed.event");
/**
 * Threat Entity
 *
 * Represents a security threat with comprehensive tracking and management capabilities.
 * Serves as the aggregate root for threat response workflows.
 *
 * Key responsibilities:
 * - Threat lifecycle management
 * - Severity and risk assessment
 * - Mitigation tracking
 * - Attribution and intelligence
 * - Timeline and status management
 *
 * Business Rules:
 * - Severity changes must be justified and tracked
 * - Mitigation actions must be recorded with timestamps
 * - High-severity threats require immediate assignment
 * - Risk scores must be recalculated when indicators change
 */
class Threat extends base_aggregate_root_1.BaseAggregateRoot {
    constructor(props, id) {
        super(props, id);
    }
    validate() {
        if (!this.props.name || this.props.name.trim().length === 0) {
            throw new Error('Threat must have a name');
        }
        if (!this.props.description || this.props.description.trim().length === 0) {
            throw new Error('Threat must have a description');
        }
        if (!Object.values(threat_severity_enum_1.ThreatSeverity).includes(this.props.severity)) {
            throw new Error(`Invalid threat severity: ${this.props.severity}`);
        }
        if (!this.props.category || this.props.category.trim().length === 0) {
            throw new Error('Threat must have a category');
        }
        if (!this.props.type || this.props.type.trim().length === 0) {
            throw new Error('Threat must have a type');
        }
        if (this.props.confidence < Threat.MIN_CONFIDENCE || this.props.confidence > Threat.MAX_CONFIDENCE) {
            throw new Error(`Threat confidence must be between ${Threat.MIN_CONFIDENCE} and ${Threat.MAX_CONFIDENCE}`);
        }
        if (this.props.riskAssessment.riskScore < Threat.MIN_RISK_SCORE ||
            this.props.riskAssessment.riskScore > Threat.MAX_RISK_SCORE) {
            throw new Error(`Risk score must be between ${Threat.MIN_RISK_SCORE} and ${Threat.MAX_RISK_SCORE}`);
        }
        if (!this.props.timeline.firstDetected) {
            throw new Error('Threat must have a first detected timestamp');
        }
        if (!this.props.mitigationStatus.status) {
            throw new Error('Threat must have a mitigation status');
        }
        // Validate timeline consistency
        if (this.props.timeline.lastSeen < this.props.timeline.firstDetected) {
            throw new Error('Last seen cannot be before first detected');
        }
    }
    /**
     * Create a new threat
     */
    static create(name, description, severity, category, type, confidence, options) {
        const now = new Date();
        const props = {
            name: name.trim(),
            description: description.trim(),
            severity,
            category: category.trim(),
            subcategory: options?.subcategory?.trim(),
            type: type.trim(),
            confidence,
            indicators: options?.indicators || [],
            cvssScores: options?.cvssScores || [],
            attribution: options?.attribution,
            malwareFamily: options?.malwareFamily,
            techniques: options?.techniques || [],
            affectedAssets: options?.affectedAssets || [],
            timeline: {
                firstDetected: now,
                lastSeen: now,
            },
            mitigationStatus: {
                status: 'detected',
                actionsTaken: [],
                responseTeam: [],
                nextActions: [],
            },
            riskAssessment: Threat.calculateInitialRiskAssessment(severity, confidence),
            tags: options?.tags || [],
            attributes: options?.attributes || {},
        };
        const threat = new Threat(props);
        // Publish domain event
        threat.addDomainEvent(new threat_detected_event_1.ThreatDetectedEvent(threat.id, {
            threatId: threat.id.toString(),
            name: threat.props.name,
            severity: threat.props.severity,
            category: threat.props.category,
            type: threat.props.type,
            confidence: threat.props.confidence,
            riskScore: threat.props.riskAssessment.riskScore,
            indicatorCount: threat.props.indicators.length,
            affectedAssetCount: threat.props.affectedAssets.length,
            timestamp: now.toISOString(),
        }));
        return threat;
    }
    static calculateInitialRiskAssessment(severity, confidence) {
        // Base risk score from severity
        let riskScore = 0;
        switch (severity) {
            case threat_severity_enum_1.ThreatSeverity.CRITICAL:
                riskScore = 90;
                break;
            case threat_severity_enum_1.ThreatSeverity.HIGH:
                riskScore = 70;
                break;
            case threat_severity_enum_1.ThreatSeverity.MEDIUM:
                riskScore = 50;
                break;
            case threat_severity_enum_1.ThreatSeverity.LOW:
                riskScore = 30;
                break;
            case threat_severity_enum_1.ThreatSeverity.UNKNOWN:
                riskScore = 20;
                break;
        }
        // Adjust by confidence
        riskScore = Math.round(riskScore * (confidence / 100));
        return {
            riskScore: Math.max(0, Math.min(100, riskScore)),
            impact: {
                confidentiality: severity === threat_severity_enum_1.ThreatSeverity.CRITICAL ? 'high' : 'medium',
                integrity: severity === threat_severity_enum_1.ThreatSeverity.CRITICAL ? 'high' : 'medium',
                availability: severity === threat_severity_enum_1.ThreatSeverity.CRITICAL ? 'high' : 'medium',
                financial: 0, // To be assessed
                reputational: severity === threat_severity_enum_1.ThreatSeverity.CRITICAL ? 'high' : 'low',
            },
            likelihood: {
                exploitability: 'medium',
                weaponization: 'medium',
                spread: 'low',
            },
            businessImpact: [],
            complianceImplications: [],
        };
    }
    /**
     * Get threat name
     */
    get name() {
        return this.props.name;
    }
    /**
     * Get threat description
     */
    get description() {
        return this.props.description;
    }
    /**
     * Get threat severity
     */
    get severity() {
        return this.props.severity;
    }
    /**
     * Get threat category
     */
    get category() {
        return this.props.category;
    }
    /**
     * Get threat type
     */
    get type() {
        return this.props.type;
    }
    /**
     * Get confidence score
     */
    get confidence() {
        return this.props.confidence;
    }
    /**
     * Get indicators
     */
    get indicators() {
        return [...this.props.indicators];
    }
    /**
     * Get CVSS scores
     */
    get cvssScores() {
        return [...this.props.cvssScores];
    }
    /**
     * Get attribution
     */
    get attribution() {
        return this.props.attribution;
    }
    /**
     * Get malware family
     */
    get malwareFamily() {
        return this.props.malwareFamily;
    }
    /**
     * Get attack techniques
     */
    get techniques() {
        return [...this.props.techniques];
    }
    /**
     * Get affected assets
     */
    get affectedAssets() {
        return [...this.props.affectedAssets];
    }
    /**
     * Get timeline
     */
    get timeline() {
        return this.props.timeline;
    }
    /**
     * Get mitigation status
     */
    get mitigationStatus() {
        return this.props.mitigationStatus;
    }
    /**
     * Get risk assessment
     */
    get riskAssessment() {
        return this.props.riskAssessment;
    }
    /**
     * Get tags
     */
    get tags() {
        return [...this.props.tags];
    }
    /**
     * Get attributes
     */
    get attributes() {
        return { ...this.props.attributes };
    }
    /**
     * Change threat severity
     */
    changeSeverity(newSeverity, reason) {
        if (newSeverity === this.props.severity) {
            return; // No change needed
        }
        const oldSeverity = this.props.severity;
        this.props.severity = newSeverity;
        // Recalculate risk assessment
        this.recalculateRiskAssessment();
        // Publish domain event
        this.addDomainEvent(new threat_severity_changed_event_1.ThreatSeverityChangedEvent(this.id, {
            threatId: this.id.toString(),
            oldSeverity,
            newSeverity,
            reason,
            newRiskScore: this.props.riskAssessment.riskScore,
            timestamp: new Date().toISOString(),
        }));
    }
    /**
     * Add indicators
     */
    addIndicators(indicators) {
        const newIndicators = indicators.filter(newIoc => !this.props.indicators.some(existingIoc => existingIoc.equals(newIoc)));
        this.props.indicators.push(...newIndicators);
        this.recalculateRiskAssessment();
    }
    /**
     * Add mitigation action
     */
    addMitigationAction(action) {
        const mitigationAction = {
            ...action,
            timestamp: new Date(),
        };
        this.props.mitigationStatus.actionsTaken.push(mitigationAction);
    }
    /**
     * Update mitigation status
     */
    updateMitigationStatus(status, options) {
        this.props.mitigationStatus.status = status;
        if (options?.assignedAnalyst) {
            this.props.mitigationStatus.assignedAnalyst = options.assignedAnalyst;
        }
        if (options?.incidentCommander) {
            this.props.mitigationStatus.incidentCommander = options.incidentCommander;
        }
        if (options?.responseTeam) {
            this.props.mitigationStatus.responseTeam = options.responseTeam;
        }
        if (options?.nextActions) {
            this.props.mitigationStatus.nextActions = options.nextActions;
        }
        // Update timeline based on status
        this.updateTimelineForStatus(status);
    }
    updateTimelineForStatus(status) {
        const now = new Date();
        switch (status) {
            case 'containing':
                if (!this.props.timeline.containmentStarted) {
                    this.props.timeline.containmentStarted = now;
                }
                break;
            case 'contained':
                if (!this.props.timeline.contained) {
                    this.props.timeline.contained = now;
                }
                break;
            case 'eradicating':
                if (!this.props.timeline.eradicationStarted) {
                    this.props.timeline.eradicationStarted = now;
                }
                break;
            case 'eradicated':
                if (!this.props.timeline.eradicated) {
                    this.props.timeline.eradicated = now;
                }
                break;
            case 'recovering':
                if (!this.props.timeline.recoveryStarted) {
                    this.props.timeline.recoveryStarted = now;
                }
                break;
            case 'recovered':
                if (!this.props.timeline.recoveryCompleted) {
                    this.props.timeline.recoveryCompleted = now;
                }
                break;
        }
    }
    /**
     * Check if threat is active
     */
    isActive() {
        const activeStatuses = ['detected', 'investigating', 'containing', 'eradicating', 'recovering'];
        return activeStatuses.includes(this.props.mitigationStatus.status);
    }
    /**
     * Check if threat is resolved
     */
    isResolved() {
        return this.props.mitigationStatus.status === 'recovered';
    }
    /**
     * Check if threat is high severity
     */
    isHighSeverity() {
        return this.props.severity === threat_severity_enum_1.ThreatSeverity.HIGH ||
            this.props.severity === threat_severity_enum_1.ThreatSeverity.CRITICAL;
    }
    /**
     * Check if threat requires immediate attention
     */
    requiresImmediateAttention() {
        return this.props.severity === threat_severity_enum_1.ThreatSeverity.CRITICAL ||
            (this.props.severity === threat_severity_enum_1.ThreatSeverity.HIGH && this.props.confidence >= 80);
    }
    /**
     * Get threat age in hours
     */
    getAge() {
        return (Date.now() - this.props.timeline.firstDetected.getTime()) / (1000 * 60 * 60);
    }
    /**
     * Recalculate risk assessment
     */
    recalculateRiskAssessment() {
        let riskScore = this.props.riskAssessment.riskScore;
        // Adjust for indicators
        const highSeverityIndicators = this.props.indicators.filter(ioc => ioc.isHighSeverity());
        riskScore += highSeverityIndicators.length * 5;
        // Adjust for CVSS scores
        const highCVSSScores = this.props.cvssScores.filter(score => score.isHighOrCritical());
        riskScore += highCVSSScores.length * 10;
        // Adjust for affected assets
        riskScore += Math.min(this.props.affectedAssets.length * 2, 20);
        this.props.riskAssessment.riskScore = Math.max(0, Math.min(100, riskScore));
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            id: this.id.toString(),
            name: this.props.name,
            description: this.props.description,
            severity: this.props.severity,
            category: this.props.category,
            subcategory: this.props.subcategory,
            type: this.props.type,
            confidence: this.props.confidence,
            indicators: this.props.indicators.map(ioc => ioc.toJSON()),
            cvssScores: this.props.cvssScores.map(score => score.toJSON()),
            attribution: this.props.attribution,
            malwareFamily: this.props.malwareFamily,
            techniques: this.props.techniques,
            affectedAssets: this.props.affectedAssets,
            timeline: this.props.timeline,
            mitigationStatus: this.props.mitigationStatus,
            riskAssessment: this.props.riskAssessment,
            tags: this.props.tags,
            attributes: this.props.attributes,
            analysis: {
                isActive: this.isActive(),
                isResolved: this.isResolved(),
                isHighSeverity: this.isHighSeverity(),
                requiresImmediateAttention: this.requiresImmediateAttention(),
                age: this.getAge(),
            },
            createdAt: this.createdAt?.toISOString(),
            updatedAt: this.updatedAt?.toISOString(),
        };
    }
}
exports.Threat = Threat;
Threat.MIN_CONFIDENCE = 0;
Threat.MAX_CONFIDENCE = 100;
Threat.MIN_RISK_SCORE = 0;
Threat.MAX_RISK_SCORE = 100;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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