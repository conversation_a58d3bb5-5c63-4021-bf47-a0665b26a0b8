{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\normalized-event.factory.ts", "mappings": ";;;AAAA,iFAAoI;AAEpI,6DAA2D;AAC3D,8DAAqD;AACrD,sEAA6D;AAC7D,kEAAyD;AACzD,wFAA8E;AAoF9E;;;;;;;;;;;;;GAaG;AACH,MAAa,sBAAsB;IAMjC;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,OAAqC;QACjD,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QAE5C,oCAAoC;QACpC,MAAM,oBAAoB,GAAyB;YACjD,eAAe,EAAE,aAAa,CAAC,EAAE;YACjC,QAAQ,EAAE,aAAa,CAAC,QAAQ;YAChC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,aAAa,CAAC,IAAI;YACxC,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,aAAa,CAAC,QAAQ;YACpD,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,aAAa,CAAC,MAAM;YAC9C,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,IAAI,oDAAqB,CAAC,UAAU;YAC9E,mBAAmB,EAAE,OAAO,CAAC,mBAAmB,IAAI,6CAAmB,CAAC,OAAO;YAC/E,YAAY,EAAE,aAAa,CAAC,OAAO;YACnC,cAAc,EAAE,OAAO,CAAC,cAAc;YACtC,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,aAAa,CAAC,KAAK;YAC3C,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,aAAa,CAAC,WAAW;YAC7D,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,aAAa,CAAC,IAAI;YACxC,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,aAAa,CAAC,SAAS;YACvD,eAAe,EAAE,OAAO,CAAC,eAAe,IAAI,aAAa,CAAC,eAAe;YACzE,UAAU,EAAE;gBACV,GAAG,aAAa,CAAC,UAAU;gBAC3B,GAAG,OAAO,CAAC,UAAU;aACtB;YACD,aAAa,EAAE,aAAa,CAAC,aAAa;YAC1C,aAAa,EAAE,aAAa,CAAC,aAAa;YAC1C,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,EAAE;YACxC,aAAa,EAAE,OAAO,CAAC,aAAa;YACpC,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;YAC1C,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;YAC1C,oBAAoB,EAAE,OAAO,CAAC,iBAAiB;YAC/C,qBAAqB,EAAE,CAAC;SACzB,CAAC;QAEF,OAAO,yCAAe,CAAC,MAAM,CAAC,oBAAoB,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,uBAAuB,CAC5B,aAAoB,EACpB,SAAuC,EAAE;QAEzC,MAAM,UAAU,GAAG,sBAAsB,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAEnE,4BAA4B;QAC5B,MAAM,mBAAmB,GAAG,sBAAsB,CAAC,uBAAuB,CACxE,aAAa,CAAC,OAAO,EACrB,UAAU,CAAC,cAAc,CAC1B,CAAC;QAEF,+BAA+B;QAC/B,MAAM,gBAAgB,GAAG,sBAAsB,CAAC,yBAAyB,CACvE,mBAAmB,EACnB,UAAU,CACX,CAAC;QAEF,yCAAyC;QACzC,MAAM,oBAAoB,GAAG,sBAAsB,CAAC,gCAAgC,CAClF,aAAa,EACb,gBAAgB,EAChB,mBAAmB,CAAC,gBAAgB,EACpC,UAAU,CACX,CAAC;QAEF,OAAO,sBAAsB,CAAC,MAAM,CAAC;YACnC,aAAa;YACb,cAAc,EAAE,mBAAmB,CAAC,cAAc;YAClD,aAAa,EAAE,UAAU,CAAC,oBAAoB;YAC9C,YAAY,EAAE,mBAAmB,CAAC,YAAY;YAC9C,mBAAmB,EAAE,mBAAmB,CAAC,OAAO;gBAC9C,CAAC,CAAC,6CAAmB,CAAC,SAAS;gBAC/B,CAAC,CAAC,6CAAmB,CAAC,MAAM;YAC9B,gBAAgB;YAChB,gBAAgB,EAAE,mBAAmB,CAAC,gBAAgB;YACtD,iBAAiB,EAAE,oBAAoB;SACxC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAChB,OAA4B,EAC5B,eAA+B,EAC/B,aAAqB,EACrB,SAAuC,EAAE;QAEzC,MAAM,UAAU,GAAG,sBAAsB,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAEnE,wCAAwC;QACxC,MAAM,mBAAmB,GAAG,sBAAsB,CAAC,uBAAuB,CACxE,OAAO,EACP,UAAU,CAAC,cAAc,CAC1B,CAAC;QAEF,uDAAuD;QACvD,MAAM,SAAS,GAAG,sBAAsB,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC;QAE9F,+BAA+B;QAC/B,MAAM,gBAAgB,GAAG,sBAAsB,CAAC,yBAAyB,CACvE,mBAAmB,EACnB,UAAU,CACX,CAAC;QAEF,8EAA8E;QAC9E,MAAM,QAAQ,GAAG,sBAAsB,CAAC,qBAAqB,EAAE,CAAC;QAEhE,MAAM,oBAAoB,GAAyB;YACjD,eAAe;YACf,QAAQ;YACR,IAAI,EAAE,SAAS,CAAC,IAAI;YACpB,QAAQ,EAAE,SAAS,CAAC,QAAQ;YAC5B,MAAM,EAAE,+BAAW,CAAC,MAAM;YAC1B,gBAAgB,EAAE,oDAAqB,CAAC,UAAU;YAClD,mBAAmB,EAAE,mBAAmB,CAAC,OAAO;gBAC9C,CAAC,CAAC,6CAAmB,CAAC,SAAS;gBAC/B,CAAC,CAAC,6CAAmB,CAAC,MAAM;YAC9B,YAAY,EAAE,OAAO;YACrB,cAAc,EAAE,mBAAmB,CAAC,cAAc;YAClD,KAAK,EAAE,SAAS,CAAC,KAAK;YACtB,WAAW,EAAE,SAAS,CAAC,WAAW;YAClC,IAAI,EAAE,SAAS,CAAC,IAAI;YACpB,SAAS,EAAE,SAAS,CAAC,SAAS;YAC9B,eAAe,EAAE,SAAS,CAAC,eAAe;YAC1C,UAAU,EAAE,SAAS,CAAC,UAAU;YAChC,YAAY,EAAE,mBAAmB,CAAC,YAAY;YAC9C,aAAa;YACb,gBAAgB;YAChB,gBAAgB,EAAE,mBAAmB,CAAC,gBAAgB;YACtD,qBAAqB,EAAE,CAAC;SACzB,CAAC;QAEF,OAAO,yCAAe,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,OAAkC;QAUnD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,UAAU,GAAsB,EAAE,CAAC;QACzC,MAAM,MAAM,GAAsC,EAAE,CAAC;QAErD,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnC,IAAI,CAAC;gBACH,MAAM,eAAe,GAAG,sBAAsB,CAAC,MAAM,CAAC;oBACpD,aAAa,EAAE,KAAK;oBACpB,cAAc,EAAE,sBAAsB,CAAC,kBAAkB,CAAC,KAAK,CAAC,OAAO,CAAC;oBACxE,aAAa,EAAE,OAAO,CAAC,aAAa;oBACpC,YAAY,EAAE,OAAO,CAAC,KAAK;iBAC5B,CAAC,CAAC;gBAEH,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACnC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;gBAC9E,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC,CAAC;gBAE5C,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;oBAC1B,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAEhD,OAAO;YACL,UAAU;YACV,MAAM;YACN,OAAO,EAAE;gBACP,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM;gBAC5B,UAAU,EAAE,UAAU,CAAC,MAAM;gBAC7B,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,gBAAgB;aACjB;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CACrB,YAAmD,EAAE;QAErD,+CAA+C;QAC/C,MAAM,iBAAiB,GAAG,SAAS,CAAC,aAAa,IAAI,sBAAsB,CAAC,eAAe,EAAE,CAAC;QAE9F,MAAM,cAAc,GAAiC;YACnD,aAAa,EAAE,iBAAiB;YAChC,cAAc,EAAE;gBACd,UAAU,EAAE,gBAAgB;gBAC5B,QAAQ,EAAE,QAAQ;gBAClB,KAAK,EAAE,qBAAqB;gBAC5B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,MAAM,EAAE,aAAa;gBACrB,UAAU,EAAE,IAAI;aACjB;YACD,aAAa,EAAE,sBAAsB,CAAC,sBAAsB;YAC5D,YAAY,EAAE,EAAE;YAChB,mBAAmB,EAAE,6CAAmB,CAAC,SAAS;YAClD,gBAAgB,EAAE,EAAE;SACrB,CAAC;QAEF,OAAO,sBAAsB,CAAC,MAAM,CAAC;YACnC,GAAG,cAAc;YACjB,GAAG,SAAS;SACb,CAAC,CAAC;IACL,CAAC;IAED,yBAAyB;IAEjB,MAAM,CAAC,gBAAgB,CAAC,MAAoC;QAClE,OAAO;YACL,oBAAoB,EAAE,sBAAsB,CAAC,sBAAsB;YACnE,cAAc,EAAE,EAAE;YAClB,uBAAuB,EAAE,sBAAsB,CAAC,wBAAwB;YACxE,8BAA8B,EAAE,IAAI;YACpC,8BAA8B,EAAE,IAAI;YACpC,mBAAmB,EAAE,sBAAsB,CAAC,6BAA6B;YACzE,sBAAsB,EAAE,sBAAsB,CAAC,6BAA6B;YAC5E,GAAG,MAAM;SACV,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,uBAAuB,CACpC,OAA4B,EAC5B,KAA0B;QAO1B,MAAM,cAAc,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC;QACtC,MAAM,YAAY,GAAwB,EAAE,CAAC;QAC7C,MAAM,gBAAgB,GAAa,EAAE,CAAC;QAEtC,iDAAiD;QACjD,MAAM,WAAW,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;QAEvE,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;YAC/B,IAAI,CAAC;gBACH,4EAA4E;gBAC5E,MAAM,UAAU,GAAG,sBAAsB,CAAC,SAAS,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;gBAE1E,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;oBACvB,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACxB,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;gBACjD,CAAC;qBAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACzB,gBAAgB,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,aAAa,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC;gBACpF,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;gBAC9E,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAClB,gBAAgB,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,kBAAkB,YAAY,EAAE,CAAC,CAAC;gBACrF,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,gBAAgB,CAAC,MAAM,KAAK,CAAC;YACtC,cAAc;YACd,YAAY;YACZ,gBAAgB;SACjB,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,SAAS,CACtB,IAAyB,EACzB,IAAuB;QAEvB,sFAAsF;QACtF,QAAQ,IAAI,CAAC,EAAE,EAAE,CAAC;YAChB,KAAK,yBAAyB;gBAC5B,OAAO,sBAAsB,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YACzD,KAAK,wBAAwB;gBAC3B,OAAO,sBAAsB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YACxD,KAAK,eAAe;gBAClB,OAAO,sBAAsB,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YAC7D;gBACE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACnC,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,kBAAkB,CAAC,IAAyB;QACzD,MAAM,eAAe,GAAG,CAAC,WAAW,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;QAEhF,KAAK,MAAM,KAAK,IAAI,eAAe,EAAE,CAAC;YACpC,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBAChB,IAAI,CAAC;oBACH,MAAM,mBAAmB,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;oBAChE,OAAO;wBACL,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE,EAAE,GAAG,IAAI,EAAE,oBAAoB,EAAE,mBAAmB,EAAE;qBAC7D,CAAC;gBACJ,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,qCAAqC,KAAK,EAAE,EAAE,CAAC;gBACjF,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,gCAAgC,EAAE,CAAC;IACrE,CAAC;IAEO,MAAM,CAAC,iBAAiB,CAAC,IAAyB;QACxD,MAAM,cAAc,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QAEzD,KAAK,MAAM,KAAK,IAAI,cAAc,EAAE,CAAC;YACnC,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBAChB,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;gBACnD,IAAI,kBAA0B,CAAC;gBAE/B,IAAI,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;oBAChE,kBAAkB,GAAG,UAAU,CAAC;gBAClC,CAAC;qBAAM,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;oBACnE,kBAAkB,GAAG,MAAM,CAAC;gBAC9B,CAAC;qBAAM,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;oBACpE,kBAAkB,GAAG,QAAQ,CAAC;gBAChC,CAAC;qBAAM,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;oBACjE,kBAAkB,GAAG,KAAK,CAAC;gBAC7B,CAAC;qBAAM,CAAC;oBACN,kBAAkB,GAAG,QAAQ,CAAC,CAAC,UAAU;gBAC3C,CAAC;gBAED,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,IAAI,EAAE,EAAE,GAAG,IAAI,EAAE,mBAAmB,EAAE,kBAAkB,EAAE;iBAC3D,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC;IAC9D,CAAC;IAEO,MAAM,CAAC,SAAS,CAAC,IAAyB,EAAE,MAA4B;QAC9E,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;YACrC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACjC,CAAC;QAED,MAAM,UAAU,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;QAE/B,KAAK,MAAM,CAAC,WAAW,EAAE,WAAW,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC;YAC9E,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,SAAS,EAAE,CAAC;gBACpC,UAAU,CAAC,WAAqB,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;YACxD,CAAC;QACH,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;IAC7C,CAAC;IAEO,MAAM,CAAC,yBAAyB,CACtC,MAA2F,EAC3F,MAA2B;QAE3B,IAAI,KAAK,GAAG,GAAG,CAAC;QAEhB,qCAAqC;QACrC,KAAK,IAAI,MAAM,CAAC,gBAAgB,CAAC,MAAM,GAAG,EAAE,CAAC;QAE7C,uCAAuC;QACvC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACpB,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,+CAA+C;QAC/C,MAAM,aAAa,GAAG,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1E,MAAM,oBAAoB,GAAG,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/E,MAAM,oBAAoB,GAAG,aAAa,CAAC,MAAM,GAAG,oBAAoB,CAAC,MAAM,CAAC;QAChF,KAAK,IAAI,oBAAoB,GAAG,EAAE,CAAC;QAEnC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;IAC3C,CAAC;IAEO,MAAM,CAAC,gCAAgC,CAC7C,aAAoB,EACpB,gBAAwB,EACxB,gBAA0B,EAC1B,MAA2B;QAE3B,yCAAyC;QACzC,IAAI,MAAM,CAAC,8BAA8B,IAAI,aAAa,CAAC,UAAU,EAAE,EAAE,CAAC;YACxE,OAAO,IAAI,CAAC;QACd,CAAC;QAED,wCAAwC;QACxC,IAAI,MAAM,CAAC,8BAA8B,IAAI,aAAa,CAAC,UAAU,EAAE,EAAE,CAAC;YACxE,OAAO,IAAI,CAAC;QACd,CAAC;QAED,0CAA0C;QAC1C,IAAI,gBAAgB,GAAG,MAAM,CAAC,uBAAuB,EAAE,CAAC;YACtD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,mDAAmD;QACnD,IAAI,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC,mBAAmB,EAAE,CAAC;YACzD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,MAAM,CAAC,gBAAgB,CAAC,cAAmC;QAUjE,OAAO;YACL,IAAI,EAAE,sBAAsB,CAAC,YAAY,CAAC,cAAc,CAAC,UAAU,IAAI,cAAc,CAAC,IAAI,CAAC;YAC3F,QAAQ,EAAE,sBAAsB,CAAC,gBAAgB,CAAC,cAAc,CAAC,QAAQ,IAAI,cAAc,CAAC,KAAK,CAAC;YAClG,KAAK,EAAE,cAAc,CAAC,KAAK,IAAI,cAAc,CAAC,OAAO,IAAI,2BAA2B;YACpF,WAAW,EAAE,cAAc,CAAC,WAAW,IAAI,cAAc,CAAC,OAAO;YACjE,IAAI,EAAE,cAAc,CAAC,IAAI,IAAI,cAAc,CAAC,MAAM;YAClD,SAAS,EAAE,cAAc,CAAC,UAAU,IAAI,cAAc,CAAC,SAAS;YAChE,eAAe,EAAE,cAAc,CAAC,UAAU,IAAI,cAAc,CAAC,eAAe;YAC5E,UAAU,EAAE,cAAc,CAAC,UAAU,IAAI,EAAE;SAC5C,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,YAAY,CAAC,IAAY;QACtC,IAAI,CAAC,IAAI;YAAE,OAAO,2BAAS,CAAC,MAAM,CAAC;QAEnC,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACnC,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,2BAAS,CAAC,eAAe,CAAC;QACjE,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,2BAAS,CAAC,gBAAgB,CAAC;QACnE,IAAI,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC;YAAE,OAAO,2BAAS,CAAC,sBAAsB,CAAC;QAC/E,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,2BAAS,CAAC,aAAa,CAAC;QAC9D,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC;YAAE,OAAO,2BAAS,CAAC,sBAAsB,CAAC;QAE5E,OAAO,2BAAS,CAAC,MAAM,CAAC;IAC1B,CAAC;IAEO,MAAM,CAAC,gBAAgB,CAAC,QAAgB;QAC9C,IAAI,CAAC,QAAQ;YAAE,OAAO,mCAAa,CAAC,MAAM,CAAC;QAE3C,MAAM,GAAG,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;QACnC,IAAI,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC;YAAE,OAAO,mCAAa,CAAC,QAAQ,CAAC;QAC5D,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,mCAAa,CAAC,IAAI,CAAC;QACpD,IAAI,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,mCAAa,CAAC,MAAM,CAAC;QACxD,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE,OAAO,mCAAa,CAAC,GAAG,CAAC;QAElD,OAAO,mCAAa,CAAC,MAAM,CAAC;IAC9B,CAAC;IAEO,MAAM,CAAC,kBAAkB,CAAC,OAA4B;QAC5D,qEAAqE;QACrE,OAAO;YACL,GAAG,OAAO;YACV,aAAa,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACvC,cAAc,EAAE,sBAAsB,CAAC,sBAAsB;SAC9D,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,qBAAqB;QAClC,wEAAwE;QACxE,OAAO;YACL,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,MAAM,EAAE,0BAA0B;YAClC,YAAY,EAAE,8BAAc,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;SACjD,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,eAAe;QAC5B,6DAA6D;QAC7D,kEAAkE;QAClE,OAAO,EAAW,CAAC;IACrB,CAAC;;AAzeH,wDA0eC;AAzeyB,6CAAsB,GAAG,OAAO,CAAC;AACjC,+CAAwB,GAAG,EAAE,CAAC;AAC9B,oDAA6B,GAAG,EAAE,CAAC;AACnC,oDAA6B,GAAG,KAAK,CAAC,CAAC,aAAa", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\normalized-event.factory.ts"], "sourcesContent": ["import { NormalizedEvent, NormalizedEventProps, NormalizationStatus, NormalizationRule } from '../entities/normalized-event.entity';\r\nimport { Event } from '../entities/event.entity';\r\nimport { UniqueEntityId } from '../../../../shared-kernel';\r\nimport { EventType } from '../enums/event-type.enum';\r\nimport { EventSeverity } from '../enums/event-severity.enum';\r\nimport { EventStatus } from '../enums/event-status.enum';\r\nimport { EventProcessingStatus } from '../enums/event-processing-status.enum';\r\n\r\n/**\r\n * Normalized Event Creation Options\r\n */\r\nexport interface CreateNormalizedEventOptions {\r\n  /** Normalized event ID (optional, will be generated if not provided) */\r\n  id?: UniqueEntityId;\r\n  /** Original event to normalize */\r\n  originalEvent: Event;\r\n  /** Normalized data in standard format */\r\n  normalizedData: Record<string, any>;\r\n  /** Schema version used for normalization */\r\n  schemaVersion: string;\r\n  /** Applied normalization rules */\r\n  appliedRules?: NormalizationRule[];\r\n  /** Initial normalization status (optional, defaults to PENDING) */\r\n  normalizationStatus?: NormalizationStatus;\r\n  /** Override event type (optional, uses original event type if not provided) */\r\n  type?: EventType;\r\n  /** Override event severity (optional, uses original event severity if not provided) */\r\n  severity?: EventSeverity;\r\n  /** Override event status (optional, uses original event status if not provided) */\r\n  status?: EventStatus;\r\n  /** Override processing status (optional, uses NORMALIZED if not provided) */\r\n  processingStatus?: EventProcessingStatus;\r\n  /** Override title (optional, uses original event title if not provided) */\r\n  title?: string;\r\n  /** Override description (optional, uses original event description if not provided) */\r\n  description?: string;\r\n  /** Override tags (optional, uses original event tags if not provided) */\r\n  tags?: string[];\r\n  /** Override risk score (optional, uses original event risk score if not provided) */\r\n  riskScore?: number;\r\n  /** Override confidence level (optional, uses original event confidence level if not provided) */\r\n  confidenceLevel?: number;\r\n  /** Additional attributes */\r\n  attributes?: Record<string, any>;\r\n  /** Force manual review requirement */\r\n  forceManualReview?: boolean;\r\n  /** Initial data quality score */\r\n  dataQualityScore?: number;\r\n  /** Initial validation errors */\r\n  validationErrors?: string[];\r\n}\r\n\r\n/**\r\n * Normalization Configuration\r\n */\r\nexport interface NormalizationConfig {\r\n  /** Default schema version */\r\n  defaultSchemaVersion: string;\r\n  /** Available normalization rules */\r\n  availableRules: NormalizationRule[];\r\n  /** Minimum data quality threshold */\r\n  minDataQualityThreshold: number;\r\n  /** Whether to require manual review for high-risk events */\r\n  requireManualReviewForHighRisk: boolean;\r\n  /** Whether to require manual review for critical events */\r\n  requireManualReviewForCritical: boolean;\r\n  /** Maximum allowed validation errors */\r\n  maxValidationErrors: number;\r\n  /** Default normalization timeout in milliseconds */\r\n  normalizationTimeoutMs: number;\r\n}\r\n\r\n/**\r\n * Batch Normalization Options\r\n */\r\nexport interface BatchNormalizationOptions {\r\n  /** Events to normalize */\r\n  events: Event[];\r\n  /** Schema version to use for all events */\r\n  schemaVersion: string;\r\n  /** Rules to apply to all events */\r\n  rules: NormalizationRule[];\r\n  /** Whether to stop on first failure */\r\n  stopOnFailure?: boolean;\r\n  /** Maximum concurrent normalizations */\r\n  maxConcurrency?: number;\r\n  /** Batch processing timeout */\r\n  batchTimeoutMs?: number;\r\n}\r\n\r\n/**\r\n * NormalizedEvent Factory\r\n * \r\n * Factory class for creating NormalizedEvent entities with proper validation and defaults.\r\n * Handles complex normalization scenarios and ensures all business rules are applied.\r\n * \r\n * Key responsibilities:\r\n * - Create normalized events from original events\r\n * - Apply normalization rules and validation\r\n * - Calculate data quality scores\r\n * - Determine manual review requirements\r\n * - Handle batch normalization operations\r\n * - Manage schema versioning and compatibility\r\n */\r\nexport class NormalizedEventFactory {\r\n  private static readonly DEFAULT_SCHEMA_VERSION = '1.0.0';\r\n  private static readonly DEFAULT_MIN_DATA_QUALITY = 60;\r\n  private static readonly DEFAULT_MAX_VALIDATION_ERRORS = 10;\r\n  private static readonly DEFAULT_NORMALIZATION_TIMEOUT = 30000; // 30 seconds\r\n\r\n  /**\r\n   * Create a new NormalizedEvent from an original Event\r\n   */\r\n  static create(options: CreateNormalizedEventOptions): NormalizedEvent {\r\n    const originalEvent = options.originalEvent;\r\n\r\n    // Build normalized event properties\r\n    const normalizedEventProps: NormalizedEventProps = {\r\n      originalEventId: originalEvent.id,\r\n      metadata: originalEvent.metadata,\r\n      type: options.type || originalEvent.type,\r\n      severity: options.severity || originalEvent.severity,\r\n      status: options.status || originalEvent.status,\r\n      processingStatus: options.processingStatus || EventProcessingStatus.NORMALIZED,\r\n      normalizationStatus: options.normalizationStatus || NormalizationStatus.PENDING,\r\n      originalData: originalEvent.rawData,\r\n      normalizedData: options.normalizedData,\r\n      title: options.title || originalEvent.title,\r\n      description: options.description || originalEvent.description,\r\n      tags: options.tags || originalEvent.tags,\r\n      riskScore: options.riskScore || originalEvent.riskScore,\r\n      confidenceLevel: options.confidenceLevel || originalEvent.confidenceLevel,\r\n      attributes: {\r\n        ...originalEvent.attributes,\r\n        ...options.attributes,\r\n      },\r\n      correlationId: originalEvent.correlationId,\r\n      parentEventId: originalEvent.parentEventId,\r\n      appliedRules: options.appliedRules || [],\r\n      schemaVersion: options.schemaVersion,\r\n      dataQualityScore: options.dataQualityScore,\r\n      validationErrors: options.validationErrors,\r\n      requiresManualReview: options.forceManualReview,\r\n      normalizationAttempts: 0,\r\n    };\r\n\r\n    return NormalizedEvent.create(normalizedEventProps, options.id);\r\n  }\r\n\r\n  /**\r\n   * Create a NormalizedEvent with automatic normalization\r\n   */\r\n  static createWithNormalization(\r\n    originalEvent: Event,\r\n    config: Partial<NormalizationConfig> = {}\r\n  ): NormalizedEvent {\r\n    const fullConfig = NormalizedEventFactory.getDefaultConfig(config);\r\n    \r\n    // Apply normalization rules\r\n    const normalizationResult = NormalizedEventFactory.applyNormalizationRules(\r\n      originalEvent.rawData,\r\n      fullConfig.availableRules\r\n    );\r\n\r\n    // Calculate data quality score\r\n    const dataQualityScore = NormalizedEventFactory.calculateDataQualityScore(\r\n      normalizationResult,\r\n      fullConfig\r\n    );\r\n\r\n    // Determine if manual review is required\r\n    const requiresManualReview = NormalizedEventFactory.determineManualReviewRequirement(\r\n      originalEvent,\r\n      dataQualityScore,\r\n      normalizationResult.validationErrors,\r\n      fullConfig\r\n    );\r\n\r\n    return NormalizedEventFactory.create({\r\n      originalEvent,\r\n      normalizedData: normalizationResult.normalizedData,\r\n      schemaVersion: fullConfig.defaultSchemaVersion,\r\n      appliedRules: normalizationResult.appliedRules,\r\n      normalizationStatus: normalizationResult.success \r\n        ? NormalizationStatus.COMPLETED \r\n        : NormalizationStatus.FAILED,\r\n      dataQualityScore,\r\n      validationErrors: normalizationResult.validationErrors,\r\n      forceManualReview: requiresManualReview,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a NormalizedEvent from raw event data\r\n   */\r\n  static fromRawData(\r\n    rawData: Record<string, any>,\r\n    originalEventId: UniqueEntityId,\r\n    schemaVersion: string,\r\n    config: Partial<NormalizationConfig> = {}\r\n  ): NormalizedEvent {\r\n    const fullConfig = NormalizedEventFactory.getDefaultConfig(config);\r\n\r\n    // Apply normalization rules to raw data\r\n    const normalizationResult = NormalizedEventFactory.applyNormalizationRules(\r\n      rawData,\r\n      fullConfig.availableRules\r\n    );\r\n\r\n    // Extract basic event information from normalized data\r\n    const eventInfo = NormalizedEventFactory.extractEventInfo(normalizationResult.normalizedData);\r\n\r\n    // Calculate data quality score\r\n    const dataQualityScore = NormalizedEventFactory.calculateDataQualityScore(\r\n      normalizationResult,\r\n      fullConfig\r\n    );\r\n\r\n    // Create minimal metadata (this would typically come from the original event)\r\n    const metadata = NormalizedEventFactory.createMinimalMetadata();\r\n\r\n    const normalizedEventProps: NormalizedEventProps = {\r\n      originalEventId,\r\n      metadata,\r\n      type: eventInfo.type,\r\n      severity: eventInfo.severity,\r\n      status: EventStatus.ACTIVE,\r\n      processingStatus: EventProcessingStatus.NORMALIZED,\r\n      normalizationStatus: normalizationResult.success \r\n        ? NormalizationStatus.COMPLETED \r\n        : NormalizationStatus.FAILED,\r\n      originalData: rawData,\r\n      normalizedData: normalizationResult.normalizedData,\r\n      title: eventInfo.title,\r\n      description: eventInfo.description,\r\n      tags: eventInfo.tags,\r\n      riskScore: eventInfo.riskScore,\r\n      confidenceLevel: eventInfo.confidenceLevel,\r\n      attributes: eventInfo.attributes,\r\n      appliedRules: normalizationResult.appliedRules,\r\n      schemaVersion,\r\n      dataQualityScore,\r\n      validationErrors: normalizationResult.validationErrors,\r\n      normalizationAttempts: 0,\r\n    };\r\n\r\n    return NormalizedEvent.create(normalizedEventProps);\r\n  }\r\n\r\n  /**\r\n   * Create multiple NormalizedEvents in batch\r\n   */\r\n  static createBatch(options: BatchNormalizationOptions): {\r\n    successful: NormalizedEvent[];\r\n    failed: { event: Event; error: string }[];\r\n    summary: {\r\n      total: number;\r\n      successful: number;\r\n      failed: number;\r\n      processingTimeMs: number;\r\n    };\r\n  } {\r\n    const startTime = Date.now();\r\n    const successful: NormalizedEvent[] = [];\r\n    const failed: { event: Event; error: string }[] = [];\r\n\r\n    for (const event of options.events) {\r\n      try {\r\n        const normalizedEvent = NormalizedEventFactory.create({\r\n          originalEvent: event,\r\n          normalizedData: NormalizedEventFactory.normalizeEventData(event.rawData),\r\n          schemaVersion: options.schemaVersion,\r\n          appliedRules: options.rules,\r\n        });\r\n\r\n        successful.push(normalizedEvent);\r\n      } catch (error) {\r\n        const errorMessage = error instanceof Error ? error.message : 'Unknown error';\r\n        failed.push({ event, error: errorMessage });\r\n\r\n        if (options.stopOnFailure) {\r\n          break;\r\n        }\r\n      }\r\n    }\r\n\r\n    const processingTimeMs = Date.now() - startTime;\r\n\r\n    return {\r\n      successful,\r\n      failed,\r\n      summary: {\r\n        total: options.events.length,\r\n        successful: successful.length,\r\n        failed: failed.length,\r\n        processingTimeMs,\r\n      },\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create a NormalizedEvent for testing purposes\r\n   */\r\n  static createForTesting(\r\n    overrides: Partial<CreateNormalizedEventOptions> = {}\r\n  ): NormalizedEvent {\r\n    // Create a mock original event if not provided\r\n    const mockOriginalEvent = overrides.originalEvent || NormalizedEventFactory.createMockEvent();\r\n\r\n    const defaultOptions: CreateNormalizedEventOptions = {\r\n      originalEvent: mockOriginalEvent,\r\n      normalizedData: {\r\n        event_type: 'security_alert',\r\n        severity: 'medium',\r\n        title: 'Test Security Event',\r\n        timestamp: new Date().toISOString(),\r\n        source: 'test-source',\r\n        normalized: true,\r\n      },\r\n      schemaVersion: NormalizedEventFactory.DEFAULT_SCHEMA_VERSION,\r\n      appliedRules: [],\r\n      normalizationStatus: NormalizationStatus.COMPLETED,\r\n      dataQualityScore: 85,\r\n    };\r\n\r\n    return NormalizedEventFactory.create({\r\n      ...defaultOptions,\r\n      ...overrides,\r\n    });\r\n  }\r\n\r\n  // Private helper methods\r\n\r\n  private static getDefaultConfig(config: Partial<NormalizationConfig>): NormalizationConfig {\r\n    return {\r\n      defaultSchemaVersion: NormalizedEventFactory.DEFAULT_SCHEMA_VERSION,\r\n      availableRules: [],\r\n      minDataQualityThreshold: NormalizedEventFactory.DEFAULT_MIN_DATA_QUALITY,\r\n      requireManualReviewForHighRisk: true,\r\n      requireManualReviewForCritical: true,\r\n      maxValidationErrors: NormalizedEventFactory.DEFAULT_MAX_VALIDATION_ERRORS,\r\n      normalizationTimeoutMs: NormalizedEventFactory.DEFAULT_NORMALIZATION_TIMEOUT,\r\n      ...config,\r\n    };\r\n  }\r\n\r\n  private static applyNormalizationRules(\r\n    rawData: Record<string, any>,\r\n    rules: NormalizationRule[]\r\n  ): {\r\n    success: boolean;\r\n    normalizedData: Record<string, any>;\r\n    appliedRules: NormalizationRule[];\r\n    validationErrors: string[];\r\n  } {\r\n    const normalizedData = { ...rawData };\r\n    const appliedRules: NormalizationRule[] = [];\r\n    const validationErrors: string[] = [];\r\n\r\n    // Sort rules by priority (higher priority first)\r\n    const sortedRules = [...rules].sort((a, b) => b.priority - a.priority);\r\n\r\n    for (const rule of sortedRules) {\r\n      try {\r\n        // Apply rule logic (this would be implemented based on specific rule types)\r\n        const ruleResult = NormalizedEventFactory.applyRule(normalizedData, rule);\r\n        \r\n        if (ruleResult.success) {\r\n          appliedRules.push(rule);\r\n          Object.assign(normalizedData, ruleResult.data);\r\n        } else if (rule.required) {\r\n          validationErrors.push(`Required rule '${rule.name}' failed: ${ruleResult.error}`);\r\n        }\r\n      } catch (error) {\r\n        const errorMessage = error instanceof Error ? error.message : 'Unknown error';\r\n        if (rule.required) {\r\n          validationErrors.push(`Required rule '${rule.name}' threw error: ${errorMessage}`);\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: validationErrors.length === 0,\r\n      normalizedData,\r\n      appliedRules,\r\n      validationErrors,\r\n    };\r\n  }\r\n\r\n  private static applyRule(\r\n    data: Record<string, any>,\r\n    rule: NormalizationRule\r\n  ): { success: boolean; data?: Record<string, any>; error?: string } {\r\n    // This is a simplified implementation - in practice, this would be more sophisticated\r\n    switch (rule.id) {\r\n      case 'timestamp_normalization':\r\n        return NormalizedEventFactory.normalizeTimestamp(data);\r\n      case 'severity_normalization':\r\n        return NormalizedEventFactory.normalizeSeverity(data);\r\n      case 'field_mapping':\r\n        return NormalizedEventFactory.mapFields(data, rule.config);\r\n      default:\r\n        return { success: true, data };\r\n    }\r\n  }\r\n\r\n  private static normalizeTimestamp(data: Record<string, any>): { success: boolean; data?: Record<string, any>; error?: string } {\r\n    const timestampFields = ['timestamp', '@timestamp', 'event_time', 'created_at'];\r\n    \r\n    for (const field of timestampFields) {\r\n      if (data[field]) {\r\n        try {\r\n          const normalizedTimestamp = new Date(data[field]).toISOString();\r\n          return {\r\n            success: true,\r\n            data: { ...data, normalized_timestamp: normalizedTimestamp }\r\n          };\r\n        } catch (error) {\r\n          return { success: false, error: `Invalid timestamp format in field ${field}` };\r\n        }\r\n      }\r\n    }\r\n\r\n    return { success: false, error: 'No valid timestamp field found' };\r\n  }\r\n\r\n  private static normalizeSeverity(data: Record<string, any>): { success: boolean; data?: Record<string, any>; error?: string } {\r\n    const severityFields = ['severity', 'level', 'priority'];\r\n    \r\n    for (const field of severityFields) {\r\n      if (data[field]) {\r\n        const severity = String(data[field]).toLowerCase();\r\n        let normalizedSeverity: string;\r\n\r\n        if (severity.includes('critical') || severity.includes('fatal')) {\r\n          normalizedSeverity = 'critical';\r\n        } else if (severity.includes('high') || severity.includes('error')) {\r\n          normalizedSeverity = 'high';\r\n        } else if (severity.includes('medium') || severity.includes('warn')) {\r\n          normalizedSeverity = 'medium';\r\n        } else if (severity.includes('low') || severity.includes('info')) {\r\n          normalizedSeverity = 'low';\r\n        } else {\r\n          normalizedSeverity = 'medium'; // default\r\n        }\r\n\r\n        return {\r\n          success: true,\r\n          data: { ...data, normalized_severity: normalizedSeverity }\r\n        };\r\n      }\r\n    }\r\n\r\n    return { success: false, error: 'No severity field found' };\r\n  }\r\n\r\n  private static mapFields(data: Record<string, any>, config?: Record<string, any>): { success: boolean; data?: Record<string, any>; error?: string } {\r\n    if (!config || !config.fieldMappings) {\r\n      return { success: true, data };\r\n    }\r\n\r\n    const mappedData = { ...data };\r\n    \r\n    for (const [sourceField, targetField] of Object.entries(config.fieldMappings)) {\r\n      if (data[sourceField] !== undefined) {\r\n        mappedData[targetField as string] = data[sourceField];\r\n      }\r\n    }\r\n\r\n    return { success: true, data: mappedData };\r\n  }\r\n\r\n  private static calculateDataQualityScore(\r\n    result: { success: boolean; appliedRules: NormalizationRule[]; validationErrors: string[] },\r\n    config: NormalizationConfig\r\n  ): number {\r\n    let score = 100;\r\n\r\n    // Reduce score for validation errors\r\n    score -= result.validationErrors.length * 10;\r\n\r\n    // Reduce score if normalization failed\r\n    if (!result.success) {\r\n      score -= 20;\r\n    }\r\n\r\n    // Reduce score based on missing required rules\r\n    const requiredRules = config.availableRules.filter(rule => rule.required);\r\n    const appliedRequiredRules = result.appliedRules.filter(rule => rule.required);\r\n    const missingRequiredRules = requiredRules.length - appliedRequiredRules.length;\r\n    score -= missingRequiredRules * 15;\r\n\r\n    return Math.max(0, Math.min(100, score));\r\n  }\r\n\r\n  private static determineManualReviewRequirement(\r\n    originalEvent: Event,\r\n    dataQualityScore: number,\r\n    validationErrors: string[],\r\n    config: NormalizationConfig\r\n  ): boolean {\r\n    // High-risk events require manual review\r\n    if (config.requireManualReviewForHighRisk && originalEvent.isHighRisk()) {\r\n      return true;\r\n    }\r\n\r\n    // Critical events require manual review\r\n    if (config.requireManualReviewForCritical && originalEvent.isCritical()) {\r\n      return true;\r\n    }\r\n\r\n    // Low data quality requires manual review\r\n    if (dataQualityScore < config.minDataQualityThreshold) {\r\n      return true;\r\n    }\r\n\r\n    // Too many validation errors require manual review\r\n    if (validationErrors.length > config.maxValidationErrors) {\r\n      return true;\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  private static extractEventInfo(normalizedData: Record<string, any>): {\r\n    type: EventType;\r\n    severity: EventSeverity;\r\n    title: string;\r\n    description?: string;\r\n    tags?: string[];\r\n    riskScore?: number;\r\n    confidenceLevel?: number;\r\n    attributes?: Record<string, any>;\r\n  } {\r\n    return {\r\n      type: NormalizedEventFactory.mapEventType(normalizedData.event_type || normalizedData.type),\r\n      severity: NormalizedEventFactory.mapEventSeverity(normalizedData.severity || normalizedData.level),\r\n      title: normalizedData.title || normalizedData.message || 'Normalized Security Event',\r\n      description: normalizedData.description || normalizedData.details,\r\n      tags: normalizedData.tags || normalizedData.labels,\r\n      riskScore: normalizedData.risk_score || normalizedData.riskScore,\r\n      confidenceLevel: normalizedData.confidence || normalizedData.confidenceLevel,\r\n      attributes: normalizedData.attributes || {},\r\n    };\r\n  }\r\n\r\n  private static mapEventType(type: string): EventType {\r\n    if (!type) return EventType.CUSTOM;\r\n    \r\n    const typeStr = type.toLowerCase();\r\n    if (typeStr.includes('threat')) return EventType.THREAT_DETECTED;\r\n    if (typeStr.includes('malware')) return EventType.MALWARE_DETECTED;\r\n    if (typeStr.includes('vulnerability')) return EventType.VULNERABILITY_DETECTED;\r\n    if (typeStr.includes('login')) return EventType.LOGIN_FAILURE;\r\n    if (typeStr.includes('connection')) return EventType.CONNECTION_ESTABLISHED;\r\n    \r\n    return EventType.CUSTOM;\r\n  }\r\n\r\n  private static mapEventSeverity(severity: string): EventSeverity {\r\n    if (!severity) return EventSeverity.MEDIUM;\r\n    \r\n    const sev = severity.toLowerCase();\r\n    if (sev.includes('critical')) return EventSeverity.CRITICAL;\r\n    if (sev.includes('high')) return EventSeverity.HIGH;\r\n    if (sev.includes('medium')) return EventSeverity.MEDIUM;\r\n    if (sev.includes('low')) return EventSeverity.LOW;\r\n    \r\n    return EventSeverity.MEDIUM;\r\n  }\r\n\r\n  private static normalizeEventData(rawData: Record<string, any>): Record<string, any> {\r\n    // Basic normalization - in practice this would be more sophisticated\r\n    return {\r\n      ...rawData,\r\n      normalized_at: new Date().toISOString(),\r\n      schema_version: NormalizedEventFactory.DEFAULT_SCHEMA_VERSION,\r\n    };\r\n  }\r\n\r\n  private static createMinimalMetadata(): any {\r\n    // This would typically create proper EventMetadata - simplified for now\r\n    return {\r\n      timestamp: new Date(),\r\n      source: 'normalized-event-factory',\r\n      processingId: UniqueEntityId.create().toString(),\r\n    };\r\n  }\r\n\r\n  private static createMockEvent(): Event {\r\n    // This would create a proper mock Event - simplified for now\r\n    // In practice, you'd use the EventFactory to create a proper mock\r\n    return {} as Event;\r\n  }\r\n}"], "version": 3}