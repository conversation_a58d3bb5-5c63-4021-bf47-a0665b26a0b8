1314848273e6c84a7554a9d77da702ec
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const policy_audit_trail_1 = require("../policy-audit-trail");
const security_policy_1 = require("../security-policy");
const unique_entity_id_value_object_1 = require("../../../../../shared-kernel/value-objects/unique-entity-id.value-object");
const tenant_id_value_object_1 = require("../../../../../shared-kernel/value-objects/tenant-id.value-object");
const user_id_value_object_1 = require("../../../../../shared-kernel/value-objects/user-id.value-object");
const timestamp_value_object_1 = require("../../../../../shared-kernel/value-objects/timestamp.value-object");
const validation_exception_1 = require("../../../../../shared-kernel/exceptions/validation.exception");
describe('PolicyAuditTrail', () => {
    let tenantId;
    let userId;
    let policyId;
    beforeEach(() => {
        tenantId = tenant_id_value_object_1.TenantId.create('tenant-123');
        userId = user_id_value_object_1.UserId.create('user-123');
        policyId = unique_entity_id_value_object_1.UniqueEntityId.generate();
    });
    describe('AuditEntry', () => {
        it('should create a valid audit entry', () => {
            const entry = policy_audit_trail_1.AuditEntry.create({
                tenantId,
                eventType: policy_audit_trail_1.AuditEventType.POLICY_CREATED,
                severity: policy_audit_trail_1.AuditSeverity.MEDIUM,
                userId,
                policyId,
                description: 'Policy was created',
                details: { policyName: 'Test Policy' },
                context: {
                    source: policy_audit_trail_1.AuditSource.WEB_UI,
                    ipAddress: '***********',
                    userAgent: 'Mozilla/5.0'
                }
            });
            expect(entry).toBeDefined();
            expect(entry.tenantId).toBe(tenantId);
            expect(entry.eventType).toBe(policy_audit_trail_1.AuditEventType.POLICY_CREATED);
            expect(entry.severity).toBe(policy_audit_trail_1.AuditSeverity.MEDIUM);
            expect(entry.userId).toBe(userId);
            expect(entry.policyId).toBe(policyId);
            expect(entry.description).toBe('Policy was created');
            expect(entry.details.policyName).toBe('Test Policy');
            expect(entry.context.source).toBe(policy_audit_trail_1.AuditSource.WEB_UI);
        });
        it('should throw validation exception for missing required fields', () => {
            expect(() => {
                policy_audit_trail_1.AuditEntry.create({
                    tenantId,
                    eventType: policy_audit_trail_1.AuditEventType.POLICY_CREATED,
                    severity: policy_audit_trail_1.AuditSeverity.MEDIUM,
                    description: '',
                    details: {},
                    context: { source: policy_audit_trail_1.AuditSource.WEB_UI }
                });
            }).toThrow(validation_exception_1.ValidationException);
        });
        it('should manage tags correctly', () => {
            const entry = policy_audit_trail_1.AuditEntry.create({
                tenantId,
                eventType: policy_audit_trail_1.AuditEventType.POLICY_CREATED,
                severity: policy_audit_trail_1.AuditSeverity.MEDIUM,
                description: 'Test entry',
                details: {},
                context: { source: policy_audit_trail_1.AuditSource.WEB_UI },
                tags: ['initial-tag']
            });
            expect(entry.tags).toContain('initial-tag');
            expect(entry.hasTag('initial-tag')).toBe(true);
            entry.addTag('new-tag');
            expect(entry.tags).toContain('new-tag');
            expect(entry.hasTag('new-tag')).toBe(true);
            entry.removeTag('initial-tag');
            expect(entry.tags).not.toContain('initial-tag');
            expect(entry.hasTag('initial-tag')).toBe(false);
        });
        it('should generate searchable text', () => {
            const entry = policy_audit_trail_1.AuditEntry.create({
                tenantId,
                eventType: policy_audit_trail_1.AuditEventType.POLICY_CREATED,
                severity: policy_audit_trail_1.AuditSeverity.MEDIUM,
                description: 'Policy was created',
                details: { policyName: 'Test Policy' },
                context: { source: policy_audit_trail_1.AuditSource.WEB_UI },
                tags: ['policy', 'creation']
            });
            const searchableText = entry.toSearchableText();
            expect(searchableText).toContain('policy was created');
            expect(searchableText).toContain('policy_created');
            expect(searchableText).toContain('medium');
            expect(searchableText).toContain('policy');
            expect(searchableText).toContain('creation');
        });
    });
    describe('PolicyAuditTrail creation', () => {
        it('should create a valid audit trail', () => {
            const auditTrail = policy_audit_trail_1.PolicyAuditTrail.create({
                tenantId,
                retentionDays: 365,
                createdBy: userId
            });
            expect(auditTrail).toBeDefined();
            expect(auditTrail.tenantId).toBe(tenantId);
            expect(auditTrail.retentionDays).toBe(365);
            expect(auditTrail.createdBy).toBe(userId);
            expect(auditTrail.entryCount).toBe(0);
        });
        it('should throw validation exception for invalid retention days', () => {
            expect(() => {
                policy_audit_trail_1.PolicyAuditTrail.create({
                    tenantId,
                    retentionDays: -1,
                    createdBy: userId
                });
            }).toThrow(validation_exception_1.ValidationException);
        });
    });
    describe('audit entry management', () => {
        let auditTrail;
        beforeEach(() => {
            auditTrail = policy_audit_trail_1.PolicyAuditTrail.create({
                tenantId,
                retentionDays: 365,
                createdBy: userId
            });
        });
        it('should add audit entries', () => {
            const entry = policy_audit_trail_1.AuditEntry.create({
                tenantId,
                eventType: policy_audit_trail_1.AuditEventType.POLICY_CREATED,
                severity: policy_audit_trail_1.AuditSeverity.MEDIUM,
                description: 'Test entry',
                details: {},
                context: { source: policy_audit_trail_1.AuditSource.WEB_UI }
            });
            auditTrail.addEntry(entry);
            expect(auditTrail.entryCount).toBe(1);
            expect(auditTrail.entries[0]).toBe(entry);
        });
        it('should reject entries with different tenant ID', () => {
            const differentTenantId = tenant_id_value_object_1.TenantId.create('different-tenant');
            const entry = policy_audit_trail_1.AuditEntry.create({
                tenantId: differentTenantId,
                eventType: policy_audit_trail_1.AuditEventType.POLICY_CREATED,
                severity: policy_audit_trail_1.AuditSeverity.MEDIUM,
                description: 'Test entry',
                details: {},
                context: { source: policy_audit_trail_1.AuditSource.WEB_UI }
            });
            expect(() => {
                auditTrail.addEntry(entry);
            }).toThrow(validation_exception_1.ValidationException);
        });
    });
    describe('specialized audit entry methods', () => {
        let auditTrail;
        beforeEach(() => {
            auditTrail = policy_audit_trail_1.PolicyAuditTrail.create({
                tenantId,
                retentionDays: 365,
                createdBy: userId
            });
        });
        it('should add policy created entry', () => {
            auditTrail.addPolicyCreatedEntry(policyId, 'Test Policy', userId, { source: policy_audit_trail_1.AuditSource.WEB_UI });
            expect(auditTrail.entryCount).toBe(1);
            const entry = auditTrail.entries[0];
            expect(entry.eventType).toBe(policy_audit_trail_1.AuditEventType.POLICY_CREATED);
            expect(entry.policyId).toBe(policyId);
            expect(entry.description).toContain('Test Policy');
            expect(entry.hasTag('policy')).toBe(true);
            expect(entry.hasTag('creation')).toBe(true);
        });
        it('should add rule evaluated entry', () => {
            const evaluationResult = {
                policyId,
                ruleId: 'rule-1',
                matched: true,
                reason: 'Rule matched condition',
                confidence: 0.85,
                evaluatedAt: timestamp_value_object_1.Timestamp.now(),
                context: {
                    eventData: { type: 'login_failed' }
                }
            };
            auditTrail.addRuleEvaluatedEntry(policyId, 'rule-1', evaluationResult, { source: policy_audit_trail_1.AuditSource.SYSTEM });
            expect(auditTrail.entryCount).toBe(1);
            const entry = auditTrail.entries[0];
            expect(entry.eventType).toBe(policy_audit_trail_1.AuditEventType.RULE_EVALUATED);
            expect(entry.ruleId).toBe('rule-1');
            expect(entry.severity).toBe(policy_audit_trail_1.AuditSeverity.HIGH); // Because rule matched
            expect(entry.hasTag('matched')).toBe(true);
        });
        it('should add action executed entry', () => {
            auditTrail.addActionExecutedEntry(policyId, 'rule-1', 'BLOCK_IP', security_policy_1.PolicyExecutionStatus.EXECUTED, userId, { source: policy_audit_trail_1.AuditSource.SYSTEM }, { targetIp: '***********00' });
            expect(auditTrail.entryCount).toBe(1);
            const entry = auditTrail.entries[0];
            expect(entry.eventType).toBe(policy_audit_trail_1.AuditEventType.ACTION_EXECUTED);
            expect(entry.details.actionType).toBe('BLOCK_IP');
            expect(entry.details.targetIp).toBe('***********00');
            expect(entry.hasTag('executed')).toBe(true);
        });
        it('should add compliance assessed entry', () => {
            const findings = [
                { id: 'finding-1', severity: 'HIGH', description: 'Test finding' }
            ];
            auditTrail.addComplianceAssessedEntry(policyId, 'control-1', 'COMPLIANT', userId, { source: policy_audit_trail_1.AuditSource.WEB_UI }, findings);
            expect(auditTrail.entryCount).toBe(1);
            const entry = auditTrail.entries[0];
            expect(entry.eventType).toBe(policy_audit_trail_1.AuditEventType.COMPLIANCE_ASSESSED);
            expect(entry.ruleId).toBe('control-1');
            expect(entry.details.status).toBe('COMPLIANT');
            expect(entry.details.findingsCount).toBe(1);
            expect(entry.hasTag('compliance')).toBe(true);
        });
    });
    describe('audit entry search', () => {
        let auditTrail;
        beforeEach(() => {
            auditTrail = policy_audit_trail_1.PolicyAuditTrail.create({
                tenantId,
                retentionDays: 365,
                createdBy: userId
            });
            // Add various entries for testing
            auditTrail.addPolicyCreatedEntry(policyId, 'Policy 1', userId, { source: policy_audit_trail_1.AuditSource.WEB_UI });
            auditTrail.addActionExecutedEntry(policyId, 'rule-1', 'BLOCK_IP', security_policy_1.PolicyExecutionStatus.EXECUTED, userId, { source: policy_audit_trail_1.AuditSource.SYSTEM });
            auditTrail.addComplianceAssessedEntry(policyId, 'control-1', 'COMPLIANT', userId, { source: policy_audit_trail_1.AuditSource.API });
        });
        it('should search entries by event type', () => {
            const criteria = {
                eventTypes: [policy_audit_trail_1.AuditEventType.POLICY_CREATED]
            };
            const results = auditTrail.searchEntries(criteria);
            expect(results).toHaveLength(1);
            expect(results[0].eventType).toBe(policy_audit_trail_1.AuditEventType.POLICY_CREATED);
        });
        it('should search entries by user ID', () => {
            const criteria = {
                userId
            };
            const results = auditTrail.searchEntries(criteria);
            expect(results).toHaveLength(3); // All entries are by the same user
            expect(results.every(r => r.userId?.equals(userId))).toBe(true);
        });
        it('should search entries by severity', () => {
            const criteria = {
                severities: [policy_audit_trail_1.AuditSeverity.MEDIUM]
            };
            const results = auditTrail.searchEntries(criteria);
            expect(results.length).toBeGreaterThan(0);
            expect(results.every(r => r.severity === policy_audit_trail_1.AuditSeverity.MEDIUM)).toBe(true);
        });
        it('should search entries by policy ID', () => {
            const criteria = {
                policyId
            };
            const results = auditTrail.searchEntries(criteria);
            expect(results).toHaveLength(3);
            expect(results.every(r => r.policyId?.equals(policyId))).toBe(true);
        });
        it('should search entries by source', () => {
            const criteria = {
                source: policy_audit_trail_1.AuditSource.WEB_UI
            };
            const results = auditTrail.searchEntries(criteria);
            expect(results).toHaveLength(1);
            expect(results[0].context.source).toBe(policy_audit_trail_1.AuditSource.WEB_UI);
        });
        it('should apply pagination', () => {
            const criteria = {
                limit: 2,
                offset: 1
            };
            const results = auditTrail.searchEntries(criteria);
            expect(results).toHaveLength(2);
        });
        it('should sort results by timestamp (most recent first)', () => {
            const results = auditTrail.searchEntries({});
            expect(results).toHaveLength(3);
            for (let i = 1; i < results.length; i++) {
                expect(results[i - 1].timestamp.value.getTime()).toBeGreaterThanOrEqual(results[i].timestamp.value.getTime());
            }
        });
    });
    describe('audit summary generation', () => {
        let auditTrail;
        beforeEach(() => {
            auditTrail = policy_audit_trail_1.PolicyAuditTrail.create({
                tenantId,
                retentionDays: 365,
                createdBy: userId
            });
            // Add entries with different characteristics
            auditTrail.addPolicyCreatedEntry(policyId, 'Policy 1', userId, { source: policy_audit_trail_1.AuditSource.WEB_UI });
            auditTrail.addActionExecutedEntry(policyId, 'rule-1', 'BLOCK_IP', security_policy_1.PolicyExecutionStatus.EXECUTED, userId, { source: policy_audit_trail_1.AuditSource.SYSTEM });
            auditTrail.addComplianceAssessedEntry(policyId, 'control-1', 'COMPLIANT', userId, { source: policy_audit_trail_1.AuditSource.API });
        });
        it('should generate audit summary', () => {
            const summary = auditTrail.generateSummary(30);
            expect(summary).toBeDefined();
            expect(summary.totalEvents).toBe(3);
            expect(summary.eventsByType).toBeDefined();
            expect(summary.eventsBySeverity).toBeDefined();
            expect(summary.eventsBySource).toBeDefined();
            expect(Array.isArray(summary.topUsers)).toBe(true);
            expect(Array.isArray(summary.recentActivity)).toBe(true);
        });
        it('should count events by type correctly', () => {
            const summary = auditTrail.generateSummary(30);
            expect(summary.eventsByType[policy_audit_trail_1.AuditEventType.POLICY_CREATED]).toBe(1);
            expect(summary.eventsByType[policy_audit_trail_1.AuditEventType.ACTION_EXECUTED]).toBe(1);
            expect(summary.eventsByType[policy_audit_trail_1.AuditEventType.COMPLIANCE_ASSESSED]).toBe(1);
        });
        it('should identify top users', () => {
            const summary = auditTrail.generateSummary(30);
            expect(summary.topUsers).toHaveLength(1);
            expect(summary.topUsers[0].userId.equals(userId)).toBe(true);
            expect(summary.topUsers[0].eventCount).toBe(3);
        });
        it('should limit recent activity', () => {
            const summary = auditTrail.generateSummary(30);
            expect(summary.recentActivity.length).toBeLessThanOrEqual(10);
        });
    });
    describe('audit export', () => {
        let auditTrail;
        beforeEach(() => {
            auditTrail = policy_audit_trail_1.PolicyAuditTrail.create({
                tenantId,
                retentionDays: 365,
                createdBy: userId
            });
            auditTrail.addPolicyCreatedEntry(policyId, 'Policy 1', userId, { source: policy_audit_trail_1.AuditSource.WEB_UI });
        });
        it('should export entries as JSON', () => {
            const exported = auditTrail.exportEntries({}, 'json');
            expect(typeof exported).toBe('string');
            const parsed = JSON.parse(exported);
            expect(Array.isArray(parsed)).toBe(true);
            expect(parsed).toHaveLength(1);
            expect(parsed[0].eventType).toBe(policy_audit_trail_1.AuditEventType.POLICY_CREATED);
        });
        it('should export entries as CSV', () => {
            const exported = auditTrail.exportEntries({}, 'csv');
            expect(typeof exported).toBe('string');
            const lines = exported.split('\n');
            expect(lines.length).toBeGreaterThan(1); // Header + data
            expect(lines[0]).toContain('ID,Timestamp,Event Type');
            expect(lines[1]).toContain('POLICY_CREATED');
        });
        it('should handle CSV escaping', () => {
            // Add entry with special characters
            const entry = policy_audit_trail_1.AuditEntry.create({
                tenantId,
                eventType: policy_audit_trail_1.AuditEventType.POLICY_CREATED,
                severity: policy_audit_trail_1.AuditSeverity.MEDIUM,
                description: 'Policy "Test Policy" was created',
                details: {},
                context: { source: policy_audit_trail_1.AuditSource.WEB_UI }
            });
            auditTrail.addEntry(entry);
            const exported = auditTrail.exportEntries({}, 'csv');
            expect(exported).toContain('""Test Policy""'); // Escaped quotes
        });
    });
    describe('retention and cleanup', () => {
        let auditTrail;
        beforeEach(() => {
            auditTrail = policy_audit_trail_1.PolicyAuditTrail.create({
                tenantId,
                retentionDays: 30, // Short retention for testing
                createdBy: userId
            });
        });
        it('should clean up expired entries when adding new ones', () => {
            // Create an old entry (simulate by creating entry with past timestamp)
            const oldEntry = new policy_audit_trail_1.AuditEntry({
                tenantId,
                eventType: policy_audit_trail_1.AuditEventType.POLICY_CREATED,
                severity: policy_audit_trail_1.AuditSeverity.MEDIUM,
                description: 'Old entry',
                details: {},
                context: { source: policy_audit_trail_1.AuditSource.WEB_UI },
                timestamp: timestamp_value_object_1.Timestamp.create(new Date(Date.now() - 35 * 24 * 60 * 60 * 1000)) // 35 days ago
            });
            // Manually add to bypass validation for testing
            auditTrail._entries.push(oldEntry);
            expect(auditTrail.entryCount).toBe(1);
            // Add new entry, which should trigger cleanup
            auditTrail.addPolicyCreatedEntry(policyId, 'New Policy', userId, { source: policy_audit_trail_1.AuditSource.WEB_UI });
            // Old entry should be removed
            expect(auditTrail.entryCount).toBe(1);
            expect(auditTrail.entries[0].description).toBe("Policy 'New Policy' was created");
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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