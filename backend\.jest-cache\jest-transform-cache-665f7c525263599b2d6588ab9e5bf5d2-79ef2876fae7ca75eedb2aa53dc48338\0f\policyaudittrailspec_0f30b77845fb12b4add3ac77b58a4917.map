{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\policies\\__tests__\\policy-audit-trail.spec.ts", "mappings": ";;AAAA,8DAO+B;AAC/B,wDAAmF;AACnF,4HAA0G;AAC1G,8GAA6F;AAC7F,0GAAyF;AACzF,8GAA8F;AAC9F,uGAAmG;AAEnG,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;IAChC,IAAI,QAAkB,CAAC;IACvB,IAAI,MAAc,CAAC;IACnB,IAAI,QAAwB,CAAC;IAE7B,UAAU,CAAC,GAAG,EAAE;QACd,QAAQ,GAAG,iCAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QACzC,MAAM,GAAG,6BAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACnC,QAAQ,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;IACvC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,KAAK,GAAG,+BAAU,CAAC,MAAM,CAAC;gBAC9B,QAAQ;gBACR,SAAS,EAAE,mCAAc,CAAC,cAAc;gBACxC,QAAQ,EAAE,kCAAa,CAAC,MAAM;gBAC9B,MAAM;gBACN,QAAQ;gBACR,WAAW,EAAE,oBAAoB;gBACjC,OAAO,EAAE,EAAE,UAAU,EAAE,aAAa,EAAE;gBACtC,OAAO,EAAE;oBACP,MAAM,EAAE,gCAAW,CAAC,MAAM;oBAC1B,SAAS,EAAE,aAAa;oBACxB,SAAS,EAAE,aAAa;iBACzB;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;YAC5B,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,mCAAc,CAAC,cAAc,CAAC,CAAC;YAC5D,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,kCAAa,CAAC,MAAM,CAAC,CAAC;YAClD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAClC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACrD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACrD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,gCAAW,CAAC,MAAM,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,MAAM,CAAC,GAAG,EAAE;gBACV,+BAAU,CAAC,MAAM,CAAC;oBAChB,QAAQ;oBACR,SAAS,EAAE,mCAAc,CAAC,cAAc;oBACxC,QAAQ,EAAE,kCAAa,CAAC,MAAM;oBAC9B,WAAW,EAAE,EAAE;oBACf,OAAO,EAAE,EAAE;oBACX,OAAO,EAAE,EAAE,MAAM,EAAE,gCAAW,CAAC,MAAM,EAAE;iBACxC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,KAAK,GAAG,+BAAU,CAAC,MAAM,CAAC;gBAC9B,QAAQ;gBACR,SAAS,EAAE,mCAAc,CAAC,cAAc;gBACxC,QAAQ,EAAE,kCAAa,CAAC,MAAM;gBAC9B,WAAW,EAAE,YAAY;gBACzB,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,EAAE,MAAM,EAAE,gCAAW,CAAC,MAAM,EAAE;gBACvC,IAAI,EAAE,CAAC,aAAa,CAAC;aACtB,CAAC,CAAC;YAEH,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YAC5C,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE/C,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACxB,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE3C,KAAK,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YAC/B,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,KAAK,GAAG,+BAAU,CAAC,MAAM,CAAC;gBAC9B,QAAQ;gBACR,SAAS,EAAE,mCAAc,CAAC,cAAc;gBACxC,QAAQ,EAAE,kCAAa,CAAC,MAAM;gBAC9B,WAAW,EAAE,oBAAoB;gBACjC,OAAO,EAAE,EAAE,UAAU,EAAE,aAAa,EAAE;gBACtC,OAAO,EAAE,EAAE,MAAM,EAAE,gCAAW,CAAC,MAAM,EAAE;gBACvC,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;aAC7B,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,KAAK,CAAC,gBAAgB,EAAE,CAAC;YAChD,MAAM,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;YACvD,MAAM,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;YACnD,MAAM,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAC3C,MAAM,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAC3C,MAAM,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,UAAU,GAAG,qCAAgB,CAAC,MAAM,CAAC;gBACzC,QAAQ;gBACR,aAAa,EAAE,GAAG;gBAClB,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;YAEH,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACjC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3C,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC3C,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1C,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;YACtE,MAAM,CAAC,GAAG,EAAE;gBACV,qCAAgB,CAAC,MAAM,CAAC;oBACtB,QAAQ;oBACR,aAAa,EAAE,CAAC,CAAC;oBACjB,SAAS,EAAE,MAAM;iBAClB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,IAAI,UAA4B,CAAC;QAEjC,UAAU,CAAC,GAAG,EAAE;YACd,UAAU,GAAG,qCAAgB,CAAC,MAAM,CAAC;gBACnC,QAAQ;gBACR,aAAa,EAAE,GAAG;gBAClB,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,KAAK,GAAG,+BAAU,CAAC,MAAM,CAAC;gBAC9B,QAAQ;gBACR,SAAS,EAAE,mCAAc,CAAC,cAAc;gBACxC,QAAQ,EAAE,kCAAa,CAAC,MAAM;gBAC9B,WAAW,EAAE,YAAY;gBACzB,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,EAAE,MAAM,EAAE,gCAAW,CAAC,MAAM,EAAE;aACxC,CAAC,CAAC;YAEH,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAE3B,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,iBAAiB,GAAG,iCAAQ,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;YAC9D,MAAM,KAAK,GAAG,+BAAU,CAAC,MAAM,CAAC;gBAC9B,QAAQ,EAAE,iBAAiB;gBAC3B,SAAS,EAAE,mCAAc,CAAC,cAAc;gBACxC,QAAQ,EAAE,kCAAa,CAAC,MAAM;gBAC9B,WAAW,EAAE,YAAY;gBACzB,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,EAAE,MAAM,EAAE,gCAAW,CAAC,MAAM,EAAE;aACxC,CAAC,CAAC;YAEH,MAAM,CAAC,GAAG,EAAE;gBACV,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC/C,IAAI,UAA4B,CAAC;QAEjC,UAAU,CAAC,GAAG,EAAE;YACd,UAAU,GAAG,qCAAgB,CAAC,MAAM,CAAC;gBACnC,QAAQ;gBACR,aAAa,EAAE,GAAG;gBAClB,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,UAAU,CAAC,qBAAqB,CAC9B,QAAQ,EACR,aAAa,EACb,MAAM,EACN,EAAE,MAAM,EAAE,gCAAW,CAAC,MAAM,EAAE,CAC/B,CAAC;YAEF,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,mCAAc,CAAC,cAAc,CAAC,CAAC;YAC5D,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,gBAAgB,GAA2B;gBAC/C,QAAQ;gBACR,MAAM,EAAE,QAAQ;gBAChB,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,wBAAwB;gBAChC,UAAU,EAAE,IAAI;gBAChB,WAAW,EAAE,kCAAS,CAAC,GAAG,EAAE;gBAC5B,OAAO,EAAE;oBACP,SAAS,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE;iBACpC;aACF,CAAC;YAEF,UAAU,CAAC,qBAAqB,CAC9B,QAAQ,EACR,QAAQ,EACR,gBAAgB,EAChB,EAAE,MAAM,EAAE,gCAAW,CAAC,MAAM,EAAE,CAC/B,CAAC;YAEF,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,mCAAc,CAAC,cAAc,CAAC,CAAC;YAC5D,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACpC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,kCAAa,CAAC,IAAI,CAAC,CAAC,CAAC,uBAAuB;YACxE,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,UAAU,CAAC,sBAAsB,CAC/B,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,uCAAqB,CAAC,QAAQ,EAC9B,MAAM,EACN,EAAE,MAAM,EAAE,gCAAW,CAAC,MAAM,EAAE,EAC9B,EAAE,QAAQ,EAAE,eAAe,EAAE,CAC9B,CAAC;YAEF,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,mCAAc,CAAC,eAAe,CAAC,CAAC;YAC7D,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAClD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACrD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,QAAQ,GAAG;gBACf,EAAE,EAAE,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE;aACnE,CAAC;YAEF,UAAU,CAAC,0BAA0B,CACnC,QAAQ,EACR,WAAW,EACX,WAAW,EACX,MAAM,EACN,EAAE,MAAM,EAAE,gCAAW,CAAC,MAAM,EAAE,EAC9B,QAAQ,CACT,CAAC;YAEF,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,mCAAc,CAAC,mBAAmB,CAAC,CAAC;YACjE,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACvC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,IAAI,UAA4B,CAAC;QAEjC,UAAU,CAAC,GAAG,EAAE;YACd,UAAU,GAAG,qCAAgB,CAAC,MAAM,CAAC;gBACnC,QAAQ;gBACR,aAAa,EAAE,GAAG;gBAClB,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;YAEH,kCAAkC;YAClC,UAAU,CAAC,qBAAqB,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,gCAAW,CAAC,MAAM,EAAE,CAAC,CAAC;YAC/F,UAAU,CAAC,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,uCAAqB,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,gCAAW,CAAC,MAAM,EAAE,CAAC,CAAC;YAC1I,UAAU,CAAC,0BAA0B,CAAC,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,gCAAW,CAAC,GAAG,EAAE,CAAC,CAAC;QACjH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,QAAQ,GAAwB;gBACpC,UAAU,EAAE,CAAC,mCAAc,CAAC,cAAc,CAAC;aAC5C,CAAC;YAEF,MAAM,OAAO,GAAG,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAEnD,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,mCAAc,CAAC,cAAc,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,QAAQ,GAAwB;gBACpC,MAAM;aACP,CAAC;YAEF,MAAM,OAAO,GAAG,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAEnD,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,mCAAmC;YACpE,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,QAAQ,GAAwB;gBACpC,UAAU,EAAE,CAAC,kCAAa,CAAC,MAAM,CAAC;aACnC,CAAC;YAEF,MAAM,OAAO,GAAG,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAEnD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,kCAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,QAAQ,GAAwB;gBACpC,QAAQ;aACT,CAAC;YAEF,MAAM,OAAO,GAAG,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAEnD,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,QAAQ,GAAwB;gBACpC,MAAM,EAAE,gCAAW,CAAC,MAAM;aAC3B,CAAC;YAEF,MAAM,OAAO,GAAG,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAEnD,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,gCAAW,CAAC,MAAM,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACjC,MAAM,QAAQ,GAAwB;gBACpC,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,CAAC;aACV,CAAC;YAEF,MAAM,OAAO,GAAG,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAEnD,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,OAAO,GAAG,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;YAE7C,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACxC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,sBAAsB,CACrE,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,CACrC,CAAC;YACJ,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,IAAI,UAA4B,CAAC;QAEjC,UAAU,CAAC,GAAG,EAAE;YACd,UAAU,GAAG,qCAAgB,CAAC,MAAM,CAAC;gBACnC,QAAQ;gBACR,aAAa,EAAE,GAAG;gBAClB,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;YAEH,6CAA6C;YAC7C,UAAU,CAAC,qBAAqB,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,gCAAW,CAAC,MAAM,EAAE,CAAC,CAAC;YAC/F,UAAU,CAAC,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,uCAAqB,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,gCAAW,CAAC,MAAM,EAAE,CAAC,CAAC;YAC1I,UAAU,CAAC,0BAA0B,CAAC,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,gCAAW,CAAC,GAAG,EAAE,CAAC,CAAC;QACjH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,OAAO,GAAG,UAAU,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAE/C,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9B,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,OAAO,GAAG,UAAU,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAE/C,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,mCAAc,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpE,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,mCAAc,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACrE,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,mCAAc,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,OAAO,GAAG,UAAU,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAE/C,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7D,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,OAAO,GAAG,UAAU,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAE/C,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,IAAI,UAA4B,CAAC;QAEjC,UAAU,CAAC,GAAG,EAAE;YACd,UAAU,GAAG,qCAAgB,CAAC,MAAM,CAAC;gBACnC,QAAQ;gBACR,aAAa,EAAE,GAAG;gBAClB,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;YAEH,UAAU,CAAC,qBAAqB,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,gCAAW,CAAC,MAAM,EAAE,CAAC,CAAC;QACjG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,QAAQ,GAAG,UAAU,CAAC,aAAa,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;YAEtD,MAAM,CAAC,OAAO,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACpC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,mCAAc,CAAC,cAAc,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,QAAQ,GAAG,UAAU,CAAC,aAAa,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YAErD,MAAM,CAAC,OAAO,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACnC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB;YACzD,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;YACtD,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,oCAAoC;YACpC,MAAM,KAAK,GAAG,+BAAU,CAAC,MAAM,CAAC;gBAC9B,QAAQ;gBACR,SAAS,EAAE,mCAAc,CAAC,cAAc;gBACxC,QAAQ,EAAE,kCAAa,CAAC,MAAM;gBAC9B,WAAW,EAAE,kCAAkC;gBAC/C,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,EAAE,MAAM,EAAE,gCAAW,CAAC,MAAM,EAAE;aACxC,CAAC,CAAC;YAEH,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAE3B,MAAM,QAAQ,GAAG,UAAU,CAAC,aAAa,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,iBAAiB;QAClE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,IAAI,UAA4B,CAAC;QAEjC,UAAU,CAAC,GAAG,EAAE;YACd,UAAU,GAAG,qCAAgB,CAAC,MAAM,CAAC;gBACnC,QAAQ;gBACR,aAAa,EAAE,EAAE,EAAE,8BAA8B;gBACjD,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,uEAAuE;YACvE,MAAM,QAAQ,GAAG,IAAK,+BAAkB,CAAC;gBACvC,QAAQ;gBACR,SAAS,EAAE,mCAAc,CAAC,cAAc;gBACxC,QAAQ,EAAE,kCAAa,CAAC,MAAM;gBAC9B,WAAW,EAAE,WAAW;gBACxB,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,EAAE,MAAM,EAAE,gCAAW,CAAC,MAAM,EAAE;gBACvC,SAAS,EAAE,kCAAS,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,cAAc;aAC5F,CAAC,CAAC;YAEH,gDAAgD;YAC/C,UAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC5C,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAEtC,8CAA8C;YAC9C,UAAU,CAAC,qBAAqB,CAAC,QAAQ,EAAE,YAAY,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,gCAAW,CAAC,MAAM,EAAE,CAAC,CAAC;YAEjG,8BAA8B;YAC9B,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\policies\\__tests__\\policy-audit-trail.spec.ts"], "sourcesContent": ["import { \r\n  PolicyAuditTrail, \r\n  AuditEntry, \r\n  AuditEventType, \r\n  AuditSeverity, \r\n  AuditSource,\r\n  AuditSearchCriteria\r\n} from '../policy-audit-trail';\r\nimport { PolicyEvaluationResult, PolicyExecutionStatus } from '../security-policy';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { TenantId } from '../../../../../shared-kernel/value-objects/tenant-id.value-object';\r\nimport { UserId } from '../../../../../shared-kernel/value-objects/user-id.value-object';\r\nimport { Timestamp } from '../../../../../shared-kernel/value-objects/timestamp.value-object';\r\nimport { ValidationException } from '../../../../../shared-kernel/exceptions/validation.exception';\r\n\r\ndescribe('PolicyAuditTrail', () => {\r\n  let tenantId: TenantId;\r\n  let userId: UserId;\r\n  let policyId: UniqueEntityId;\r\n\r\n  beforeEach(() => {\r\n    tenantId = TenantId.create('tenant-123');\r\n    userId = UserId.create('user-123');\r\n    policyId = UniqueEntityId.generate();\r\n  });\r\n\r\n  describe('AuditEntry', () => {\r\n    it('should create a valid audit entry', () => {\r\n      const entry = AuditEntry.create({\r\n        tenantId,\r\n        eventType: AuditEventType.POLICY_CREATED,\r\n        severity: AuditSeverity.MEDIUM,\r\n        userId,\r\n        policyId,\r\n        description: 'Policy was created',\r\n        details: { policyName: 'Test Policy' },\r\n        context: {\r\n          source: AuditSource.WEB_UI,\r\n          ipAddress: '***********',\r\n          userAgent: 'Mozilla/5.0'\r\n        }\r\n      });\r\n\r\n      expect(entry).toBeDefined();\r\n      expect(entry.tenantId).toBe(tenantId);\r\n      expect(entry.eventType).toBe(AuditEventType.POLICY_CREATED);\r\n      expect(entry.severity).toBe(AuditSeverity.MEDIUM);\r\n      expect(entry.userId).toBe(userId);\r\n      expect(entry.policyId).toBe(policyId);\r\n      expect(entry.description).toBe('Policy was created');\r\n      expect(entry.details.policyName).toBe('Test Policy');\r\n      expect(entry.context.source).toBe(AuditSource.WEB_UI);\r\n    });\r\n\r\n    it('should throw validation exception for missing required fields', () => {\r\n      expect(() => {\r\n        AuditEntry.create({\r\n          tenantId,\r\n          eventType: AuditEventType.POLICY_CREATED,\r\n          severity: AuditSeverity.MEDIUM,\r\n          description: '',\r\n          details: {},\r\n          context: { source: AuditSource.WEB_UI }\r\n        });\r\n      }).toThrow(ValidationException);\r\n    });\r\n\r\n    it('should manage tags correctly', () => {\r\n      const entry = AuditEntry.create({\r\n        tenantId,\r\n        eventType: AuditEventType.POLICY_CREATED,\r\n        severity: AuditSeverity.MEDIUM,\r\n        description: 'Test entry',\r\n        details: {},\r\n        context: { source: AuditSource.WEB_UI },\r\n        tags: ['initial-tag']\r\n      });\r\n\r\n      expect(entry.tags).toContain('initial-tag');\r\n      expect(entry.hasTag('initial-tag')).toBe(true);\r\n\r\n      entry.addTag('new-tag');\r\n      expect(entry.tags).toContain('new-tag');\r\n      expect(entry.hasTag('new-tag')).toBe(true);\r\n\r\n      entry.removeTag('initial-tag');\r\n      expect(entry.tags).not.toContain('initial-tag');\r\n      expect(entry.hasTag('initial-tag')).toBe(false);\r\n    });\r\n\r\n    it('should generate searchable text', () => {\r\n      const entry = AuditEntry.create({\r\n        tenantId,\r\n        eventType: AuditEventType.POLICY_CREATED,\r\n        severity: AuditSeverity.MEDIUM,\r\n        description: 'Policy was created',\r\n        details: { policyName: 'Test Policy' },\r\n        context: { source: AuditSource.WEB_UI },\r\n        tags: ['policy', 'creation']\r\n      });\r\n\r\n      const searchableText = entry.toSearchableText();\r\n      expect(searchableText).toContain('policy was created');\r\n      expect(searchableText).toContain('policy_created');\r\n      expect(searchableText).toContain('medium');\r\n      expect(searchableText).toContain('policy');\r\n      expect(searchableText).toContain('creation');\r\n    });\r\n  });\r\n\r\n  describe('PolicyAuditTrail creation', () => {\r\n    it('should create a valid audit trail', () => {\r\n      const auditTrail = PolicyAuditTrail.create({\r\n        tenantId,\r\n        retentionDays: 365,\r\n        createdBy: userId\r\n      });\r\n\r\n      expect(auditTrail).toBeDefined();\r\n      expect(auditTrail.tenantId).toBe(tenantId);\r\n      expect(auditTrail.retentionDays).toBe(365);\r\n      expect(auditTrail.createdBy).toBe(userId);\r\n      expect(auditTrail.entryCount).toBe(0);\r\n    });\r\n\r\n    it('should throw validation exception for invalid retention days', () => {\r\n      expect(() => {\r\n        PolicyAuditTrail.create({\r\n          tenantId,\r\n          retentionDays: -1,\r\n          createdBy: userId\r\n        });\r\n      }).toThrow(ValidationException);\r\n    });\r\n  });\r\n\r\n  describe('audit entry management', () => {\r\n    let auditTrail: PolicyAuditTrail;\r\n\r\n    beforeEach(() => {\r\n      auditTrail = PolicyAuditTrail.create({\r\n        tenantId,\r\n        retentionDays: 365,\r\n        createdBy: userId\r\n      });\r\n    });\r\n\r\n    it('should add audit entries', () => {\r\n      const entry = AuditEntry.create({\r\n        tenantId,\r\n        eventType: AuditEventType.POLICY_CREATED,\r\n        severity: AuditSeverity.MEDIUM,\r\n        description: 'Test entry',\r\n        details: {},\r\n        context: { source: AuditSource.WEB_UI }\r\n      });\r\n\r\n      auditTrail.addEntry(entry);\r\n\r\n      expect(auditTrail.entryCount).toBe(1);\r\n      expect(auditTrail.entries[0]).toBe(entry);\r\n    });\r\n\r\n    it('should reject entries with different tenant ID', () => {\r\n      const differentTenantId = TenantId.create('different-tenant');\r\n      const entry = AuditEntry.create({\r\n        tenantId: differentTenantId,\r\n        eventType: AuditEventType.POLICY_CREATED,\r\n        severity: AuditSeverity.MEDIUM,\r\n        description: 'Test entry',\r\n        details: {},\r\n        context: { source: AuditSource.WEB_UI }\r\n      });\r\n\r\n      expect(() => {\r\n        auditTrail.addEntry(entry);\r\n      }).toThrow(ValidationException);\r\n    });\r\n  });\r\n\r\n  describe('specialized audit entry methods', () => {\r\n    let auditTrail: PolicyAuditTrail;\r\n\r\n    beforeEach(() => {\r\n      auditTrail = PolicyAuditTrail.create({\r\n        tenantId,\r\n        retentionDays: 365,\r\n        createdBy: userId\r\n      });\r\n    });\r\n\r\n    it('should add policy created entry', () => {\r\n      auditTrail.addPolicyCreatedEntry(\r\n        policyId,\r\n        'Test Policy',\r\n        userId,\r\n        { source: AuditSource.WEB_UI }\r\n      );\r\n\r\n      expect(auditTrail.entryCount).toBe(1);\r\n      const entry = auditTrail.entries[0];\r\n      expect(entry.eventType).toBe(AuditEventType.POLICY_CREATED);\r\n      expect(entry.policyId).toBe(policyId);\r\n      expect(entry.description).toContain('Test Policy');\r\n      expect(entry.hasTag('policy')).toBe(true);\r\n      expect(entry.hasTag('creation')).toBe(true);\r\n    });\r\n\r\n    it('should add rule evaluated entry', () => {\r\n      const evaluationResult: PolicyEvaluationResult = {\r\n        policyId,\r\n        ruleId: 'rule-1',\r\n        matched: true,\r\n        reason: 'Rule matched condition',\r\n        confidence: 0.85,\r\n        evaluatedAt: Timestamp.now(),\r\n        context: {\r\n          eventData: { type: 'login_failed' }\r\n        }\r\n      };\r\n\r\n      auditTrail.addRuleEvaluatedEntry(\r\n        policyId,\r\n        'rule-1',\r\n        evaluationResult,\r\n        { source: AuditSource.SYSTEM }\r\n      );\r\n\r\n      expect(auditTrail.entryCount).toBe(1);\r\n      const entry = auditTrail.entries[0];\r\n      expect(entry.eventType).toBe(AuditEventType.RULE_EVALUATED);\r\n      expect(entry.ruleId).toBe('rule-1');\r\n      expect(entry.severity).toBe(AuditSeverity.HIGH); // Because rule matched\r\n      expect(entry.hasTag('matched')).toBe(true);\r\n    });\r\n\r\n    it('should add action executed entry', () => {\r\n      auditTrail.addActionExecutedEntry(\r\n        policyId,\r\n        'rule-1',\r\n        'BLOCK_IP',\r\n        PolicyExecutionStatus.EXECUTED,\r\n        userId,\r\n        { source: AuditSource.SYSTEM },\r\n        { targetIp: '***********00' }\r\n      );\r\n\r\n      expect(auditTrail.entryCount).toBe(1);\r\n      const entry = auditTrail.entries[0];\r\n      expect(entry.eventType).toBe(AuditEventType.ACTION_EXECUTED);\r\n      expect(entry.details.actionType).toBe('BLOCK_IP');\r\n      expect(entry.details.targetIp).toBe('***********00');\r\n      expect(entry.hasTag('executed')).toBe(true);\r\n    });\r\n\r\n    it('should add compliance assessed entry', () => {\r\n      const findings = [\r\n        { id: 'finding-1', severity: 'HIGH', description: 'Test finding' }\r\n      ];\r\n\r\n      auditTrail.addComplianceAssessedEntry(\r\n        policyId,\r\n        'control-1',\r\n        'COMPLIANT',\r\n        userId,\r\n        { source: AuditSource.WEB_UI },\r\n        findings\r\n      );\r\n\r\n      expect(auditTrail.entryCount).toBe(1);\r\n      const entry = auditTrail.entries[0];\r\n      expect(entry.eventType).toBe(AuditEventType.COMPLIANCE_ASSESSED);\r\n      expect(entry.ruleId).toBe('control-1');\r\n      expect(entry.details.status).toBe('COMPLIANT');\r\n      expect(entry.details.findingsCount).toBe(1);\r\n      expect(entry.hasTag('compliance')).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('audit entry search', () => {\r\n    let auditTrail: PolicyAuditTrail;\r\n\r\n    beforeEach(() => {\r\n      auditTrail = PolicyAuditTrail.create({\r\n        tenantId,\r\n        retentionDays: 365,\r\n        createdBy: userId\r\n      });\r\n\r\n      // Add various entries for testing\r\n      auditTrail.addPolicyCreatedEntry(policyId, 'Policy 1', userId, { source: AuditSource.WEB_UI });\r\n      auditTrail.addActionExecutedEntry(policyId, 'rule-1', 'BLOCK_IP', PolicyExecutionStatus.EXECUTED, userId, { source: AuditSource.SYSTEM });\r\n      auditTrail.addComplianceAssessedEntry(policyId, 'control-1', 'COMPLIANT', userId, { source: AuditSource.API });\r\n    });\r\n\r\n    it('should search entries by event type', () => {\r\n      const criteria: AuditSearchCriteria = {\r\n        eventTypes: [AuditEventType.POLICY_CREATED]\r\n      };\r\n\r\n      const results = auditTrail.searchEntries(criteria);\r\n\r\n      expect(results).toHaveLength(1);\r\n      expect(results[0].eventType).toBe(AuditEventType.POLICY_CREATED);\r\n    });\r\n\r\n    it('should search entries by user ID', () => {\r\n      const criteria: AuditSearchCriteria = {\r\n        userId\r\n      };\r\n\r\n      const results = auditTrail.searchEntries(criteria);\r\n\r\n      expect(results).toHaveLength(3); // All entries are by the same user\r\n      expect(results.every(r => r.userId?.equals(userId))).toBe(true);\r\n    });\r\n\r\n    it('should search entries by severity', () => {\r\n      const criteria: AuditSearchCriteria = {\r\n        severities: [AuditSeverity.MEDIUM]\r\n      };\r\n\r\n      const results = auditTrail.searchEntries(criteria);\r\n\r\n      expect(results.length).toBeGreaterThan(0);\r\n      expect(results.every(r => r.severity === AuditSeverity.MEDIUM)).toBe(true);\r\n    });\r\n\r\n    it('should search entries by policy ID', () => {\r\n      const criteria: AuditSearchCriteria = {\r\n        policyId\r\n      };\r\n\r\n      const results = auditTrail.searchEntries(criteria);\r\n\r\n      expect(results).toHaveLength(3);\r\n      expect(results.every(r => r.policyId?.equals(policyId))).toBe(true);\r\n    });\r\n\r\n    it('should search entries by source', () => {\r\n      const criteria: AuditSearchCriteria = {\r\n        source: AuditSource.WEB_UI\r\n      };\r\n\r\n      const results = auditTrail.searchEntries(criteria);\r\n\r\n      expect(results).toHaveLength(1);\r\n      expect(results[0].context.source).toBe(AuditSource.WEB_UI);\r\n    });\r\n\r\n    it('should apply pagination', () => {\r\n      const criteria: AuditSearchCriteria = {\r\n        limit: 2,\r\n        offset: 1\r\n      };\r\n\r\n      const results = auditTrail.searchEntries(criteria);\r\n\r\n      expect(results).toHaveLength(2);\r\n    });\r\n\r\n    it('should sort results by timestamp (most recent first)', () => {\r\n      const results = auditTrail.searchEntries({});\r\n\r\n      expect(results).toHaveLength(3);\r\n      for (let i = 1; i < results.length; i++) {\r\n        expect(results[i - 1].timestamp.value.getTime()).toBeGreaterThanOrEqual(\r\n          results[i].timestamp.value.getTime()\r\n        );\r\n      }\r\n    });\r\n  });\r\n\r\n  describe('audit summary generation', () => {\r\n    let auditTrail: PolicyAuditTrail;\r\n\r\n    beforeEach(() => {\r\n      auditTrail = PolicyAuditTrail.create({\r\n        tenantId,\r\n        retentionDays: 365,\r\n        createdBy: userId\r\n      });\r\n\r\n      // Add entries with different characteristics\r\n      auditTrail.addPolicyCreatedEntry(policyId, 'Policy 1', userId, { source: AuditSource.WEB_UI });\r\n      auditTrail.addActionExecutedEntry(policyId, 'rule-1', 'BLOCK_IP', PolicyExecutionStatus.EXECUTED, userId, { source: AuditSource.SYSTEM });\r\n      auditTrail.addComplianceAssessedEntry(policyId, 'control-1', 'COMPLIANT', userId, { source: AuditSource.API });\r\n    });\r\n\r\n    it('should generate audit summary', () => {\r\n      const summary = auditTrail.generateSummary(30);\r\n\r\n      expect(summary).toBeDefined();\r\n      expect(summary.totalEvents).toBe(3);\r\n      expect(summary.eventsByType).toBeDefined();\r\n      expect(summary.eventsBySeverity).toBeDefined();\r\n      expect(summary.eventsBySource).toBeDefined();\r\n      expect(Array.isArray(summary.topUsers)).toBe(true);\r\n      expect(Array.isArray(summary.recentActivity)).toBe(true);\r\n    });\r\n\r\n    it('should count events by type correctly', () => {\r\n      const summary = auditTrail.generateSummary(30);\r\n\r\n      expect(summary.eventsByType[AuditEventType.POLICY_CREATED]).toBe(1);\r\n      expect(summary.eventsByType[AuditEventType.ACTION_EXECUTED]).toBe(1);\r\n      expect(summary.eventsByType[AuditEventType.COMPLIANCE_ASSESSED]).toBe(1);\r\n    });\r\n\r\n    it('should identify top users', () => {\r\n      const summary = auditTrail.generateSummary(30);\r\n\r\n      expect(summary.topUsers).toHaveLength(1);\r\n      expect(summary.topUsers[0].userId.equals(userId)).toBe(true);\r\n      expect(summary.topUsers[0].eventCount).toBe(3);\r\n    });\r\n\r\n    it('should limit recent activity', () => {\r\n      const summary = auditTrail.generateSummary(30);\r\n\r\n      expect(summary.recentActivity.length).toBeLessThanOrEqual(10);\r\n    });\r\n  });\r\n\r\n  describe('audit export', () => {\r\n    let auditTrail: PolicyAuditTrail;\r\n\r\n    beforeEach(() => {\r\n      auditTrail = PolicyAuditTrail.create({\r\n        tenantId,\r\n        retentionDays: 365,\r\n        createdBy: userId\r\n      });\r\n\r\n      auditTrail.addPolicyCreatedEntry(policyId, 'Policy 1', userId, { source: AuditSource.WEB_UI });\r\n    });\r\n\r\n    it('should export entries as JSON', () => {\r\n      const exported = auditTrail.exportEntries({}, 'json');\r\n\r\n      expect(typeof exported).toBe('string');\r\n      const parsed = JSON.parse(exported);\r\n      expect(Array.isArray(parsed)).toBe(true);\r\n      expect(parsed).toHaveLength(1);\r\n      expect(parsed[0].eventType).toBe(AuditEventType.POLICY_CREATED);\r\n    });\r\n\r\n    it('should export entries as CSV', () => {\r\n      const exported = auditTrail.exportEntries({}, 'csv');\r\n\r\n      expect(typeof exported).toBe('string');\r\n      const lines = exported.split('\\n');\r\n      expect(lines.length).toBeGreaterThan(1); // Header + data\r\n      expect(lines[0]).toContain('ID,Timestamp,Event Type');\r\n      expect(lines[1]).toContain('POLICY_CREATED');\r\n    });\r\n\r\n    it('should handle CSV escaping', () => {\r\n      // Add entry with special characters\r\n      const entry = AuditEntry.create({\r\n        tenantId,\r\n        eventType: AuditEventType.POLICY_CREATED,\r\n        severity: AuditSeverity.MEDIUM,\r\n        description: 'Policy \"Test Policy\" was created',\r\n        details: {},\r\n        context: { source: AuditSource.WEB_UI }\r\n      });\r\n\r\n      auditTrail.addEntry(entry);\r\n\r\n      const exported = auditTrail.exportEntries({}, 'csv');\r\n      expect(exported).toContain('\"\"Test Policy\"\"'); // Escaped quotes\r\n    });\r\n  });\r\n\r\n  describe('retention and cleanup', () => {\r\n    let auditTrail: PolicyAuditTrail;\r\n\r\n    beforeEach(() => {\r\n      auditTrail = PolicyAuditTrail.create({\r\n        tenantId,\r\n        retentionDays: 30, // Short retention for testing\r\n        createdBy: userId\r\n      });\r\n    });\r\n\r\n    it('should clean up expired entries when adding new ones', () => {\r\n      // Create an old entry (simulate by creating entry with past timestamp)\r\n      const oldEntry = new (AuditEntry as any)({\r\n        tenantId,\r\n        eventType: AuditEventType.POLICY_CREATED,\r\n        severity: AuditSeverity.MEDIUM,\r\n        description: 'Old entry',\r\n        details: {},\r\n        context: { source: AuditSource.WEB_UI },\r\n        timestamp: Timestamp.create(new Date(Date.now() - 35 * 24 * 60 * 60 * 1000)) // 35 days ago\r\n      });\r\n\r\n      // Manually add to bypass validation for testing\r\n      (auditTrail as any)._entries.push(oldEntry);\r\n      expect(auditTrail.entryCount).toBe(1);\r\n\r\n      // Add new entry, which should trigger cleanup\r\n      auditTrail.addPolicyCreatedEntry(policyId, 'New Policy', userId, { source: AuditSource.WEB_UI });\r\n\r\n      // Old entry should be removed\r\n      expect(auditTrail.entryCount).toBe(1);\r\n      expect(auditTrail.entries[0].description).toBe(\"Policy 'New Policy' was created\");\r\n    });\r\n  });\r\n});"], "version": 3}