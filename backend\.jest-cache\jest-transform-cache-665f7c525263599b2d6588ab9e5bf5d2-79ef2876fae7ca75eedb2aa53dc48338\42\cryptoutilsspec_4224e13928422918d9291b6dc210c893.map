{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\utils\\crypto.utils.spec.ts", "mappings": ";AAAA;;GAEG;;AAEH,2DAAyE;AACzE,+DAAiE;AAEjE,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;IAC3B,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,GAAG,GAAG,0BAAW,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAExC,MAAM,CAAC,GAAG,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACnC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,IAAI,GAAG,0BAAW,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YACzC,MAAM,IAAI,GAAG,0BAAW,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAEzC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,IAAI,GAAG,0BAAW,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YAE1C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,KAAK,GAAG,0BAAW,CAAC,YAAY,EAAE,CAAC;YACzC,MAAM,KAAK,GAAG,0BAAW,CAAC,YAAY,EAAE,CAAC;YAEzC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,EAAE,GAAG,0BAAW,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YAEtC,MAAM,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAClC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,GAAG,GAAG,0BAAW,CAAC,UAAU,EAAE,CAAC;YACrC,MAAM,GAAG,GAAG,0BAAW,CAAC,UAAU,EAAE,CAAC;YAErC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,QAAQ,GAAG,eAAe,CAAC;YACjC,MAAM,IAAI,GAAG,0BAAW,CAAC,YAAY,EAAE,CAAC;YAExC,MAAM,GAAG,GAAG,MAAM,0BAAW,CAAC,eAAe,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;YAExE,MAAM,CAAC,GAAG,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACnC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,QAAQ,GAAG,eAAe,CAAC;YACjC,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;YAExD,MAAM,IAAI,GAAG,MAAM,0BAAW,CAAC,eAAe,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;YACzE,MAAM,IAAI,GAAG,MAAM,0BAAW,CAAC,eAAe,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;YAEzE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;YACpE,MAAM,IAAI,GAAG,0BAAW,CAAC,YAAY,EAAE,CAAC;YAExC,MAAM,IAAI,GAAG,MAAM,0BAAW,CAAC,eAAe,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;YAC5E,MAAM,IAAI,GAAG,MAAM,0BAAW,CAAC,eAAe,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;YAE5E,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;YAClD,MAAM,GAAG,GAAG,0BAAW,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAExC,MAAM,SAAS,GAAG,MAAM,0BAAW,CAAC,gBAAgB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAChE,MAAM,SAAS,GAAG,MAAM,0BAAW,CAAC,gBAAgB,CAClD,SAAS,CAAC,SAAS,EACnB,GAAG,EACH,SAAS,CAAC,EAAE,EACZ,SAAS,CAAC,GAAI,CACf,CAAC;YAEF,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wBAAwB,EAAE,KAAK,IAAI,EAAE;YACtC,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAC9C,MAAM,GAAG,GAAG,0BAAW,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YACxC,MAAM,EAAE,GAAG,0BAAW,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YAEtC,MAAM,SAAS,GAAG,MAAM,0BAAW,CAAC,gBAAgB,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;YAEpE,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAC9C,MAAM,GAAG,GAAG,0BAAW,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAExC,MAAM,SAAS,GAAG,MAAM,0BAAW,CAAC,gBAAgB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAEhE,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,CAAC,SAAS,CAAC,GAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAC9C,MAAM,IAAI,GAAG,0BAAW,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YACzC,MAAM,IAAI,GAAG,0BAAW,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAEzC,MAAM,SAAS,GAAG,MAAM,0BAAW,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAEjE,MAAM,MAAM,CACV,0BAAW,CAAC,gBAAgB,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,GAAI,CAAC,CACtF,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;YAClD,MAAM,GAAG,GAAG,0BAAW,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAExC,MAAM,SAAS,GAAG,MAAM,0BAAW,CAAC,gBAAgB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAChE,MAAM,SAAS,GAAG,MAAM,0BAAW,CAAC,gBAAgB,CAClD,SAAS,CAAC,SAAS,EACnB,GAAG,EACH,SAAS,CAAC,EAAE,CACb,CAAC;YAEF,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uBAAuB,EAAE,KAAK,IAAI,EAAE;YACrC,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAC9C,MAAM,GAAG,GAAG,0BAAW,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAExC,MAAM,SAAS,GAAG,MAAM,0BAAW,CAAC,gBAAgB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAEhE,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAE9C,MAAM,IAAI,GAAG,MAAM,0BAAW,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAEhD,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,gCAAgC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAE9C,MAAM,IAAI,GAAG,MAAM,0BAAW,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAEhD,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,gCAAgC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAE9C,MAAM,KAAK,GAAG,MAAM,0BAAW,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,KAAK,GAAG,MAAM,0BAAW,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAEjD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,MAAM,EAAE,GAAG,EAAE;QACpB,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAC9C,MAAM,GAAG,GAAG,0BAAW,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAExC,MAAM,IAAI,GAAG,MAAM,0BAAW,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YACrD,MAAM,OAAO,GAAG,MAAM,0BAAW,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;YAE9D,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAC9C,MAAM,IAAI,GAAG,0BAAW,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YACzC,MAAM,IAAI,GAAG,0BAAW,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAEzC,MAAM,IAAI,GAAG,MAAM,0BAAW,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACtD,MAAM,OAAO,GAAG,MAAM,0BAAW,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAE/D,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAC/C,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;YACnD,MAAM,GAAG,GAAG,0BAAW,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAExC,MAAM,IAAI,GAAG,MAAM,0BAAW,CAAC,UAAU,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YACtD,MAAM,OAAO,GAAG,MAAM,0BAAW,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;YAE/D,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,MAAM,OAAO,GAAG,MAAM,0BAAW,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAE3D,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;YAChD,MAAM,OAAO,GAAG,MAAM,0BAAW,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAE3D,MAAM,SAAS,GAAG,MAAM,0BAAW,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;YACxE,MAAM,SAAS,GAAG,MAAM,0BAAW,CAAC,UAAU,CAAC,SAAS,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;YAE9E,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,OAAO,GAAG,MAAM,0BAAW,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAEhE,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;YACjD,MAAM,OAAO,GAAG,MAAM,0BAAW,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAEhE,MAAM,SAAS,GAAG,MAAM,0BAAW,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YACjF,MAAM,OAAO,GAAG,MAAM,0BAAW,CAAC,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAE3F,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;YACjD,MAAM,QAAQ,GAAG,MAAM,0BAAW,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YACjE,MAAM,QAAQ,GAAG,MAAM,0BAAW,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAEjE,MAAM,SAAS,GAAG,MAAM,0BAAW,CAAC,SAAS,CAAC,IAAI,EAAE,QAAQ,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAClF,MAAM,OAAO,GAAG,MAAM,0BAAW,CAAC,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAE5F,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,KAAK,GAAG,0BAAW,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;YAElD,MAAM,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACrC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,GAAG,GAAG,0BAAW,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;YAEjD,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAClC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,GAAG,GAAG,0BAAW,CAAC,oBAAoB,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC;YAE/D,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC5B,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sBAAsB,EAAE,GAAG,EAAE;YAC9B,MAAM,IAAI,GAAG,0BAAW,CAAC,YAAY,EAAE,CAAC;YAExC,MAAM,CAAC,OAAO,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnC,MAAM,CAAC,wEAAwE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,CAAC,0BAAW,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrE,MAAM,CAAC,0BAAW,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtE,MAAM,CAAC,0BAAW,CAAC,mBAAmB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAC1C,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAC1C,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAE1C,MAAM,CAAC,0BAAW,CAAC,0BAA0B,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtE,MAAM,CAAC,0BAAW,CAAC,0BAA0B,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;YAElD,MAAM,OAAO,GAAG,0BAAW,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,OAAO,GAAG,0BAAW,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YAElD,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;YAElD,MAAM,OAAO,GAAG,0BAAW,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,OAAO,GAAG,0BAAW,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAErD,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACnC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACnC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;YAElD,MAAM,OAAO,GAAG,0BAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAC5C,MAAM,OAAO,GAAG,0BAAW,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YAE/C,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACvD,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;YAElD,MAAM,QAAQ,GAAG,0BAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAErD,MAAM,CAAC,OAAO,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvC,MAAM,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAE9C,MAAM,SAAS,GAAG,0BAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,SAAS,GAAG,0BAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAEtD,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;YACnD,MAAM,QAAQ,GAAG,iBAAiB,CAAC;YAEnC,MAAM,SAAS,GAAG,MAAM,0BAAW,CAAC,mBAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YACxE,MAAM,SAAS,GAAG,MAAM,0BAAW,CAAC,mBAAmB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YAE7E,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;YACnD,MAAM,SAAS,GAAG,WAAW,CAAC;YAC9B,MAAM,SAAS,GAAG,WAAW,CAAC;YAE9B,MAAM,SAAS,GAAG,MAAM,0BAAW,CAAC,mBAAmB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YAEzE,MAAM,MAAM,CACV,0BAAW,CAAC,mBAAmB,CAAC,SAAS,EAAE,SAAS,CAAC,CACtD,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAC9C,MAAM,QAAQ,GAAG,UAAU,CAAC;YAE5B,MAAM,SAAS,GAAG,MAAM,0BAAW,CAAC,mBAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAExE,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;YACrC,MAAM,CAAC,SAAS,CAAC,IAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,QAAQ,GAAG,eAAe,CAAC;YAEjC,MAAM,UAAU,GAAG,MAAM,0BAAW,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAC5D,MAAM,OAAO,GAAG,MAAM,0BAAW,CAAC,cAAc,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAEvE,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,SAAS,GAAG,WAAW,CAAC;YAC9B,MAAM,SAAS,GAAG,WAAW,CAAC;YAE9B,MAAM,UAAU,GAAG,MAAM,0BAAW,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAC7D,MAAM,OAAO,GAAG,MAAM,0BAAW,CAAC,cAAc,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;YAExE,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,KAAK,IAAI,EAAE;YACxC,MAAM,QAAQ,GAAG,eAAe,CAAC;YACjC,MAAM,IAAI,GAAG,0BAAW,CAAC,YAAY,EAAE,CAAC;YAExC,MAAM,UAAU,GAAG,MAAM,0BAAW,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAElE,MAAM,CAAC,UAAU,CAAC,IAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,gBAAgB,GAAqB;gBACzC,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC;gBAChD,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE,MAAM,CAAC;gBAChD,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC;gBACpC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC;gBACvC,SAAS,EAAE,oCAAmB,CAAC,WAAW;aAC3C,CAAC;YAEF,MAAM,UAAU,GAAG,0BAAW,CAAC,yBAAyB,CAAC,gBAAgB,CAAC,CAAC;YAC3E,MAAM,YAAY,GAAG,0BAAW,CAAC,2BAA2B,CAAC,UAAU,CAAC,CAAC;YAEzE,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7E,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/D,MAAM,CAAC,YAAY,CAAC,GAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnE,MAAM,CAAC,YAAY,CAAC,IAAK,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrE,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\utils\\crypto.utils.spec.ts"], "sourcesContent": ["/**\r\n * Crypto Utils Tests\r\n */\r\n\r\nimport { CryptoUtils, EncryptionResult } from '../../utils/crypto.utils';\r\nimport { EncryptionAlgorithm } from '../../types/security.types';\r\n\r\ndescribe('CryptoUtils', () => {\r\n  describe('generateKey', () => {\r\n    it('should generate key of specified size', () => {\r\n      const key = CryptoUtils.generateKey(32);\r\n      \r\n      expect(key).toBeInstanceOf(Buffer);\r\n      expect(key.length).toBe(32);\r\n    });\r\n\r\n    it('should generate different keys', () => {\r\n      const key1 = CryptoUtils.generateKey(16);\r\n      const key2 = CryptoUtils.generateKey(16);\r\n      \r\n      expect(key1.equals(key2)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('generateSalt', () => {\r\n    it('should generate salt of specified size', () => {\r\n      const salt = CryptoUtils.generateSalt(16);\r\n      \r\n      expect(salt).toBeInstanceOf(<PERSON>uffer);\r\n      expect(salt.length).toBe(16);\r\n    });\r\n\r\n    it('should generate different salts', () => {\r\n      const salt1 = CryptoUtils.generateSalt();\r\n      const salt2 = CryptoUtils.generateSalt();\r\n      \r\n      expect(salt1.equals(salt2)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('generateIV', () => {\r\n    it('should generate IV of specified size', () => {\r\n      const iv = CryptoUtils.generateIV(12);\r\n      \r\n      expect(iv).toBeInstanceOf(Buffer);\r\n      expect(iv.length).toBe(12);\r\n    });\r\n\r\n    it('should generate different IVs', () => {\r\n      const iv1 = CryptoUtils.generateIV();\r\n      const iv2 = CryptoUtils.generateIV();\r\n      \r\n      expect(iv1.equals(iv2)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('deriveKeyPBKDF2', () => {\r\n    it('should derive key from password', async () => {\r\n      const password = 'test-password';\r\n      const salt = CryptoUtils.generateSalt();\r\n      \r\n      const key = await CryptoUtils.deriveKeyPBKDF2(password, salt, 1000, 32);\r\n      \r\n      expect(key).toBeInstanceOf(Buffer);\r\n      expect(key.length).toBe(32);\r\n    });\r\n\r\n    it('should derive same key for same inputs', async () => {\r\n      const password = 'test-password';\r\n      const salt = Buffer.from('fixed-salt-16-bytes', 'utf8');\r\n      \r\n      const key1 = await CryptoUtils.deriveKeyPBKDF2(password, salt, 1000, 32);\r\n      const key2 = await CryptoUtils.deriveKeyPBKDF2(password, salt, 1000, 32);\r\n      \r\n      expect(key1.equals(key2)).toBe(true);\r\n    });\r\n\r\n    it('should derive different keys for different passwords', async () => {\r\n      const salt = CryptoUtils.generateSalt();\r\n      \r\n      const key1 = await CryptoUtils.deriveKeyPBKDF2('password1', salt, 1000, 32);\r\n      const key2 = await CryptoUtils.deriveKeyPBKDF2('password2', salt, 1000, 32);\r\n      \r\n      expect(key1.equals(key2)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('AES-256-GCM encryption', () => {\r\n    it('should encrypt and decrypt data', async () => {\r\n      const data = Buffer.from('Hello, World!', 'utf8');\r\n      const key = CryptoUtils.generateKey(32);\r\n      \r\n      const encrypted = await CryptoUtils.encryptAES256GCM(data, key);\r\n      const decrypted = await CryptoUtils.decryptAES256GCM(\r\n        encrypted.encrypted,\r\n        key,\r\n        encrypted.iv,\r\n        encrypted.tag!\r\n      );\r\n      \r\n      expect(decrypted.toString('utf8')).toBe('Hello, World!');\r\n    });\r\n\r\n    it('should use provided IV', async () => {\r\n      const data = Buffer.from('test data', 'utf8');\r\n      const key = CryptoUtils.generateKey(32);\r\n      const iv = CryptoUtils.generateIV(12);\r\n      \r\n      const encrypted = await CryptoUtils.encryptAES256GCM(data, key, iv);\r\n      \r\n      expect(encrypted.iv.equals(iv)).toBe(true);\r\n    });\r\n\r\n    it('should include authentication tag', async () => {\r\n      const data = Buffer.from('test data', 'utf8');\r\n      const key = CryptoUtils.generateKey(32);\r\n      \r\n      const encrypted = await CryptoUtils.encryptAES256GCM(data, key);\r\n      \r\n      expect(encrypted.tag).toBeDefined();\r\n      expect(encrypted.tag!.length).toBe(16);\r\n    });\r\n\r\n    it('should fail decryption with wrong key', async () => {\r\n      const data = Buffer.from('test data', 'utf8');\r\n      const key1 = CryptoUtils.generateKey(32);\r\n      const key2 = CryptoUtils.generateKey(32);\r\n      \r\n      const encrypted = await CryptoUtils.encryptAES256GCM(data, key1);\r\n      \r\n      await expect(\r\n        CryptoUtils.decryptAES256GCM(encrypted.encrypted, key2, encrypted.iv, encrypted.tag!)\r\n      ).rejects.toThrow();\r\n    });\r\n  });\r\n\r\n  describe('AES-256-CBC encryption', () => {\r\n    it('should encrypt and decrypt data', async () => {\r\n      const data = Buffer.from('Hello, World!', 'utf8');\r\n      const key = CryptoUtils.generateKey(32);\r\n      \r\n      const encrypted = await CryptoUtils.encryptAES256CBC(data, key);\r\n      const decrypted = await CryptoUtils.decryptAES256CBC(\r\n        encrypted.encrypted,\r\n        key,\r\n        encrypted.iv\r\n      );\r\n      \r\n      expect(decrypted.toString('utf8')).toBe('Hello, World!');\r\n    });\r\n\r\n    it('should use 16-byte IV', async () => {\r\n      const data = Buffer.from('test data', 'utf8');\r\n      const key = CryptoUtils.generateKey(32);\r\n      \r\n      const encrypted = await CryptoUtils.encryptAES256CBC(data, key);\r\n      \r\n      expect(encrypted.iv.length).toBe(16);\r\n    });\r\n  });\r\n\r\n  describe('SHA hashing', () => {\r\n    it('should hash data with SHA-256', async () => {\r\n      const data = Buffer.from('test data', 'utf8');\r\n      \r\n      const hash = await CryptoUtils.hashSHA256(data);\r\n      \r\n      expect(hash).toBeInstanceOf(Buffer);\r\n      expect(hash.length).toBe(32); // SHA-256 produces 32-byte hash\r\n    });\r\n\r\n    it('should hash data with SHA-512', async () => {\r\n      const data = Buffer.from('test data', 'utf8');\r\n      \r\n      const hash = await CryptoUtils.hashSHA512(data);\r\n      \r\n      expect(hash).toBeInstanceOf(Buffer);\r\n      expect(hash.length).toBe(64); // SHA-512 produces 64-byte hash\r\n    });\r\n\r\n    it('should produce same hash for same data', async () => {\r\n      const data = Buffer.from('test data', 'utf8');\r\n      \r\n      const hash1 = await CryptoUtils.hashSHA256(data);\r\n      const hash2 = await CryptoUtils.hashSHA256(data);\r\n      \r\n      expect(hash1.equals(hash2)).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('HMAC', () => {\r\n    it('should create and verify HMAC', async () => {\r\n      const data = Buffer.from('test data', 'utf8');\r\n      const key = CryptoUtils.generateKey(32);\r\n      \r\n      const hmac = await CryptoUtils.createHMAC(data, key);\r\n      const isValid = await CryptoUtils.verifyHMAC(data, hmac, key);\r\n      \r\n      expect(isValid).toBe(true);\r\n    });\r\n\r\n    it('should fail verification with wrong key', async () => {\r\n      const data = Buffer.from('test data', 'utf8');\r\n      const key1 = CryptoUtils.generateKey(32);\r\n      const key2 = CryptoUtils.generateKey(32);\r\n      \r\n      const hmac = await CryptoUtils.createHMAC(data, key1);\r\n      const isValid = await CryptoUtils.verifyHMAC(data, hmac, key2);\r\n      \r\n      expect(isValid).toBe(false);\r\n    });\r\n\r\n    it('should fail verification with modified data', async () => {\r\n      const data1 = Buffer.from('test data', 'utf8');\r\n      const data2 = Buffer.from('modified data', 'utf8');\r\n      const key = CryptoUtils.generateKey(32);\r\n      \r\n      const hmac = await CryptoUtils.createHMAC(data1, key);\r\n      const isValid = await CryptoUtils.verifyHMAC(data2, hmac, key);\r\n      \r\n      expect(isValid).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('RSA encryption', () => {\r\n    it('should generate RSA key pair', async () => {\r\n      const keyPair = await CryptoUtils.generateRSAKeyPair(2048);\r\n      \r\n      expect(keyPair.publicKey).toBeInstanceOf(Buffer);\r\n      expect(keyPair.privateKey).toBeInstanceOf(Buffer);\r\n      expect(keyPair.algorithm).toBe('RSA-OAEP');\r\n      expect(keyPair.keySize).toBe(2048);\r\n    });\r\n\r\n    it('should encrypt and decrypt with RSA', async () => {\r\n      const data = Buffer.from('Hello, RSA!', 'utf8');\r\n      const keyPair = await CryptoUtils.generateRSAKeyPair(2048);\r\n      \r\n      const encrypted = await CryptoUtils.encryptRSA(data, keyPair.publicKey);\r\n      const decrypted = await CryptoUtils.decryptRSA(encrypted, keyPair.privateKey);\r\n      \r\n      expect(decrypted.toString('utf8')).toBe('Hello, RSA!');\r\n    });\r\n  });\r\n\r\n  describe('ECDSA signing', () => {\r\n    it('should generate ECDSA key pair', async () => {\r\n      const keyPair = await CryptoUtils.generateECDSAKeyPair('P-256');\r\n      \r\n      expect(keyPair.publicKey).toBeInstanceOf(Buffer);\r\n      expect(keyPair.privateKey).toBeInstanceOf(Buffer);\r\n      expect(keyPair.algorithm).toBe('ECDSA-P-256');\r\n      expect(keyPair.keySize).toBe(256);\r\n    });\r\n\r\n    it('should sign and verify with ECDSA', async () => {\r\n      const data = Buffer.from('test message', 'utf8');\r\n      const keyPair = await CryptoUtils.generateECDSAKeyPair('P-256');\r\n      \r\n      const signature = await CryptoUtils.signECDSA(data, keyPair.privateKey, 'P-256');\r\n      const isValid = await CryptoUtils.verifyECDSA(data, signature, keyPair.publicKey, 'P-256');\r\n      \r\n      expect(isValid).toBe(true);\r\n    });\r\n\r\n    it('should fail verification with wrong public key', async () => {\r\n      const data = Buffer.from('test message', 'utf8');\r\n      const keyPair1 = await CryptoUtils.generateECDSAKeyPair('P-256');\r\n      const keyPair2 = await CryptoUtils.generateECDSAKeyPair('P-256');\r\n      \r\n      const signature = await CryptoUtils.signECDSA(data, keyPair1.privateKey, 'P-256');\r\n      const isValid = await CryptoUtils.verifyECDSA(data, signature, keyPair2.publicKey, 'P-256');\r\n      \r\n      expect(isValid).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('random generation', () => {\r\n    it('should generate random bytes', () => {\r\n      const bytes = CryptoUtils.generateRandomBytes(16);\r\n      \r\n      expect(bytes).toBeInstanceOf(Buffer);\r\n      expect(bytes.length).toBe(16);\r\n    });\r\n\r\n    it('should generate random string', () => {\r\n      const str = CryptoUtils.generateRandomString(20);\r\n      \r\n      expect(typeof str).toBe('string');\r\n      expect(str.length).toBe(20);\r\n    });\r\n\r\n    it('should generate random string with custom charset', () => {\r\n      const str = CryptoUtils.generateRandomString(10, '0123456789');\r\n      \r\n      expect(str.length).toBe(10);\r\n      expect(/^[0-9]+$/.test(str)).toBe(true);\r\n    });\r\n\r\n    it('should generate UUID', () => {\r\n      const uuid = CryptoUtils.generateUUID();\r\n      \r\n      expect(typeof uuid).toBe('string');\r\n      expect(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(uuid)).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('constant time comparison', () => {\r\n    it('should compare strings in constant time', () => {\r\n      expect(CryptoUtils.constantTimeCompare('hello', 'hello')).toBe(true);\r\n      expect(CryptoUtils.constantTimeCompare('hello', 'world')).toBe(false);\r\n      expect(CryptoUtils.constantTimeCompare('hello', 'hell')).toBe(false);\r\n    });\r\n\r\n    it('should compare buffers in constant time', () => {\r\n      const buf1 = Buffer.from('hello', 'utf8');\r\n      const buf2 = Buffer.from('hello', 'utf8');\r\n      const buf3 = Buffer.from('world', 'utf8');\r\n      \r\n      expect(CryptoUtils.constantTimeCompareBuffers(buf1, buf2)).toBe(true);\r\n      expect(CryptoUtils.constantTimeCompareBuffers(buf1, buf3)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('encoding', () => {\r\n    it('should encode and decode Base64', () => {\r\n      const data = Buffer.from('Hello, World!', 'utf8');\r\n      \r\n      const encoded = CryptoUtils.encodeBase64(data);\r\n      const decoded = CryptoUtils.decodeBase64(encoded);\r\n      \r\n      expect(decoded.toString('utf8')).toBe('Hello, World!');\r\n    });\r\n\r\n    it('should encode and decode Base64URL', () => {\r\n      const data = Buffer.from('Hello, World!', 'utf8');\r\n      \r\n      const encoded = CryptoUtils.encodeBase64URL(data);\r\n      const decoded = CryptoUtils.decodeBase64URL(encoded);\r\n      \r\n      expect(decoded.toString('utf8')).toBe('Hello, World!');\r\n      expect(encoded).not.toContain('+');\r\n      expect(encoded).not.toContain('/');\r\n      expect(encoded).not.toContain('=');\r\n    });\r\n\r\n    it('should encode and decode hex', () => {\r\n      const data = Buffer.from('Hello, World!', 'utf8');\r\n      \r\n      const encoded = CryptoUtils.encodeHex(data);\r\n      const decoded = CryptoUtils.decodeHex(encoded);\r\n      \r\n      expect(decoded.toString('utf8')).toBe('Hello, World!');\r\n      expect(/^[0-9a-f]+$/.test(encoded)).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('checksum', () => {\r\n    it('should calculate CRC32 checksum', () => {\r\n      const data = Buffer.from('Hello, World!', 'utf8');\r\n      \r\n      const checksum = CryptoUtils.calculateChecksum(data);\r\n      \r\n      expect(typeof checksum).toBe('number');\r\n      expect(checksum).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should produce same checksum for same data', () => {\r\n      const data = Buffer.from('test data', 'utf8');\r\n      \r\n      const checksum1 = CryptoUtils.calculateChecksum(data);\r\n      const checksum2 = CryptoUtils.calculateChecksum(data);\r\n      \r\n      expect(checksum1).toBe(checksum2);\r\n    });\r\n  });\r\n\r\n  describe('password encryption', () => {\r\n    it('should encrypt and decrypt with password', async () => {\r\n      const data = Buffer.from('sensitive data', 'utf8');\r\n      const password = 'strong-password';\r\n      \r\n      const encrypted = await CryptoUtils.encryptWithPassword(data, password);\r\n      const decrypted = await CryptoUtils.decryptWithPassword(encrypted, password);\r\n      \r\n      expect(decrypted.toString('utf8')).toBe('sensitive data');\r\n    });\r\n\r\n    it('should fail decryption with wrong password', async () => {\r\n      const data = Buffer.from('sensitive data', 'utf8');\r\n      const password1 = 'password1';\r\n      const password2 = 'password2';\r\n      \r\n      const encrypted = await CryptoUtils.encryptWithPassword(data, password1);\r\n      \r\n      await expect(\r\n        CryptoUtils.decryptWithPassword(encrypted, password2)\r\n      ).rejects.toThrow();\r\n    });\r\n\r\n    it('should include salt in result', async () => {\r\n      const data = Buffer.from('test data', 'utf8');\r\n      const password = 'password';\r\n      \r\n      const encrypted = await CryptoUtils.encryptWithPassword(data, password);\r\n      \r\n      expect(encrypted.salt).toBeDefined();\r\n      expect(encrypted.salt!.length).toBe(16);\r\n    });\r\n  });\r\n\r\n  describe('password hashing', () => {\r\n    it('should hash and verify password', async () => {\r\n      const password = 'test-password';\r\n      \r\n      const hashResult = await CryptoUtils.hashPassword(password);\r\n      const isValid = await CryptoUtils.verifyPassword(password, hashResult);\r\n      \r\n      expect(isValid).toBe(true);\r\n    });\r\n\r\n    it('should fail verification with wrong password', async () => {\r\n      const password1 = 'password1';\r\n      const password2 = 'password2';\r\n      \r\n      const hashResult = await CryptoUtils.hashPassword(password1);\r\n      const isValid = await CryptoUtils.verifyPassword(password2, hashResult);\r\n      \r\n      expect(isValid).toBe(false);\r\n    });\r\n\r\n    it('should use provided salt', async () => {\r\n      const password = 'test-password';\r\n      const salt = CryptoUtils.generateSalt();\r\n      \r\n      const hashResult = await CryptoUtils.hashPassword(password, salt);\r\n      \r\n      expect(hashResult.salt!.equals(salt)).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('serialization', () => {\r\n    it('should serialize and deserialize encryption result', () => {\r\n      const encryptionResult: EncryptionResult = {\r\n        encrypted: Buffer.from('encrypted-data', 'utf8'),\r\n        iv: Buffer.from('initialization-vector', 'utf8'),\r\n        tag: Buffer.from('auth-tag', 'utf8'),\r\n        salt: Buffer.from('salt-value', 'utf8'),\r\n        algorithm: EncryptionAlgorithm.AES_256_GCM,\r\n      };\r\n      \r\n      const serialized = CryptoUtils.serializeEncryptionResult(encryptionResult);\r\n      const deserialized = CryptoUtils.deserializeEncryptionResult(serialized);\r\n      \r\n      expect(deserialized.encrypted.equals(encryptionResult.encrypted)).toBe(true);\r\n      expect(deserialized.iv.equals(encryptionResult.iv)).toBe(true);\r\n      expect(deserialized.tag!.equals(encryptionResult.tag!)).toBe(true);\r\n      expect(deserialized.salt!.equals(encryptionResult.salt!)).toBe(true);\r\n      expect(deserialized.algorithm).toBe(encryptionResult.algorithm);\r\n    });\r\n  });\r\n});"], "version": 3}