c3ac47c15d8e594d5d1718d86b6a3c2f
"use strict";
/**
 * Validation Utils Tests
 */
Object.defineProperty(exports, "__esModule", { value: true });
const validation_utils_1 = require("../../utils/validation.utils");
describe('ValidationUtils', () => {
    describe('validateValue', () => {
        it('should validate value against rules', async () => {
            const rules = [
                validation_utils_1.ValidationUtils.Rules.required(),
                validation_utils_1.ValidationUtils.Rules.minLength(3),
            ];
            const result = await validation_utils_1.ValidationUtils.validateValue('hello', rules, 'testField');
            expect(result.isValid).toBe(true);
            expect(result.errors).toHaveLength(0);
        });
        it('should fail validation when rules fail', async () => {
            const rules = [
                validation_utils_1.ValidationUtils.Rules.required(),
                validation_utils_1.ValidationUtils.Rules.minLength(10),
            ];
            const result = await validation_utils_1.ValidationUtils.validateValue('hello', rules, 'testField');
            expect(result.isValid).toBe(false);
            expect(result.errors).toHaveLength(1);
            expect(result.errors[0].field).toBe('testField');
            expect(result.errors[0].code).toBe('MIN_LENGTH');
        });
        it('should stop validation on required rule failure', async () => {
            const rules = [
                validation_utils_1.ValidationUtils.Rules.required(),
                validation_utils_1.ValidationUtils.Rules.minLength(3),
            ];
            const result = await validation_utils_1.ValidationUtils.validateValue('', rules, 'testField');
            expect(result.isValid).toBe(false);
            expect(result.errors).toHaveLength(1);
            expect(result.errors[0].code).toBe('REQUIRED');
        });
    });
    describe('validateObject', () => {
        it('should validate object against schema', async () => {
            const schema = {
                fields: [
                    {
                        field: 'name',
                        required: true,
                        type: 'string',
                        rules: [validation_utils_1.ValidationUtils.Rules.minLength(2)],
                    },
                    {
                        field: 'age',
                        required: true,
                        type: 'number',
                        rules: [validation_utils_1.ValidationUtils.Rules.min(0)],
                    },
                ],
            };
            const obj = { name: 'John', age: 25 };
            const result = await validation_utils_1.ValidationUtils.validateObject(obj, schema);
            expect(result.isValid).toBe(true);
            expect(result.errors).toHaveLength(0);
        });
        it('should fail validation for missing required fields', async () => {
            const schema = {
                fields: [
                    {
                        field: 'name',
                        required: true,
                        type: 'string',
                    },
                ],
            };
            const obj = {};
            const result = await validation_utils_1.ValidationUtils.validateObject(obj, schema);
            expect(result.isValid).toBe(false);
            expect(result.errors).toHaveLength(1);
            expect(result.errors[0].code).toBe('REQUIRED_FIELD');
        });
        it('should fail validation for wrong types', async () => {
            const schema = {
                fields: [
                    {
                        field: 'age',
                        required: true,
                        type: 'number',
                    },
                ],
            };
            const obj = { age: 'not a number' };
            const result = await validation_utils_1.ValidationUtils.validateObject(obj, schema);
            expect(result.isValid).toBe(false);
            expect(result.errors).toHaveLength(1);
            expect(result.errors[0].code).toBe('INVALID_TYPE');
        });
        it('should apply default values', async () => {
            const schema = {
                fields: [
                    {
                        field: 'status',
                        required: false,
                        type: 'string',
                        defaultValue: 'active',
                    },
                ],
            };
            const obj = {};
            const result = await validation_utils_1.ValidationUtils.validateObject(obj, schema);
            expect(result.isValid).toBe(true);
        });
        it('should handle unknown fields when not allowed', async () => {
            const schema = {
                fields: [
                    {
                        field: 'name',
                        required: true,
                        type: 'string',
                    },
                ],
                allowUnknown: false,
            };
            const obj = { name: 'John', extra: 'field' };
            const result = await validation_utils_1.ValidationUtils.validateObject(obj, schema);
            expect(result.isValid).toBe(false);
            expect(result.errors).toHaveLength(1);
            expect(result.errors[0].code).toBe('UNKNOWN_FIELD');
        });
        it('should strip unknown fields when configured', async () => {
            const schema = {
                fields: [
                    {
                        field: 'name',
                        required: true,
                        type: 'string',
                    },
                ],
                allowUnknown: false,
                stripUnknown: true,
            };
            const obj = { name: 'John', extra: 'field' };
            const result = await validation_utils_1.ValidationUtils.validateObject(obj, schema);
            expect(result.isValid).toBe(true);
            expect(result.warnings).toHaveLength(1);
            expect(result.warnings[0].code).toBe('UNKNOWN_FIELD_STRIPPED');
        });
    });
    describe('validateType', () => {
        it('should validate string type', () => {
            const result = validation_utils_1.ValidationUtils.validateType('hello', 'string');
            expect(result.isValid).toBe(true);
            const result2 = validation_utils_1.ValidationUtils.validateType(123, 'string');
            expect(result2.isValid).toBe(false);
        });
        it('should validate number type', () => {
            const result = validation_utils_1.ValidationUtils.validateType(123, 'number');
            expect(result.isValid).toBe(true);
            const result2 = validation_utils_1.ValidationUtils.validateType('123', 'number');
            expect(result2.isValid).toBe(false);
            const result3 = validation_utils_1.ValidationUtils.validateType(NaN, 'number');
            expect(result3.isValid).toBe(false);
        });
        it('should validate boolean type', () => {
            const result = validation_utils_1.ValidationUtils.validateType(true, 'boolean');
            expect(result.isValid).toBe(true);
            const result2 = validation_utils_1.ValidationUtils.validateType('true', 'boolean');
            expect(result2.isValid).toBe(false);
        });
        it('should validate array type', () => {
            const result = validation_utils_1.ValidationUtils.validateType([1, 2, 3], 'array');
            expect(result.isValid).toBe(true);
            const result2 = validation_utils_1.ValidationUtils.validateType('not array', 'array');
            expect(result2.isValid).toBe(false);
        });
        it('should validate object type', () => {
            const result = validation_utils_1.ValidationUtils.validateType({ key: 'value' }, 'object');
            expect(result.isValid).toBe(true);
            const result2 = validation_utils_1.ValidationUtils.validateType([1, 2, 3], 'object');
            expect(result2.isValid).toBe(false);
            const result3 = validation_utils_1.ValidationUtils.validateType(null, 'object');
            expect(result3.isValid).toBe(false);
        });
        it('should validate date type', () => {
            const result = validation_utils_1.ValidationUtils.validateType(new Date(), 'date');
            expect(result.isValid).toBe(true);
            const result2 = validation_utils_1.ValidationUtils.validateType('2023-01-01', 'date');
            expect(result2.isValid).toBe(true);
            const result3 = validation_utils_1.ValidationUtils.validateType('invalid date', 'date');
            expect(result3.isValid).toBe(false);
        });
        it('should validate email type', () => {
            const result = validation_utils_1.ValidationUtils.validateType('<EMAIL>', 'email');
            expect(result.isValid).toBe(true);
            const result2 = validation_utils_1.ValidationUtils.validateType('invalid-email', 'email');
            expect(result2.isValid).toBe(false);
        });
        it('should validate URL type', () => {
            const result = validation_utils_1.ValidationUtils.validateType('https://example.com', 'url');
            expect(result.isValid).toBe(true);
            const result2 = validation_utils_1.ValidationUtils.validateType('invalid-url', 'url');
            expect(result2.isValid).toBe(false);
        });
        it('should validate UUID type', () => {
            const result = validation_utils_1.ValidationUtils.validateType('123e4567-e89b-12d3-a456-************', 'uuid');
            expect(result.isValid).toBe(true);
            const result2 = validation_utils_1.ValidationUtils.validateType('invalid-uuid', 'uuid');
            expect(result2.isValid).toBe(false);
        });
    });
    describe('validation rules', () => {
        describe('required', () => {
            it('should validate required values', () => {
                const rule = validation_utils_1.ValidationUtils.Rules.required();
                expect(rule.validate('value')).toBe(true);
                expect(rule.validate('')).toBe(false);
                expect(rule.validate(null)).toBe(false);
                expect(rule.validate(undefined)).toBe(false);
            });
        });
        describe('minLength', () => {
            it('should validate minimum length for strings', () => {
                const rule = validation_utils_1.ValidationUtils.Rules.minLength(5);
                expect(rule.validate('hello')).toBe(true);
                expect(rule.validate('hello world')).toBe(true);
                expect(rule.validate('hi')).toBe(false);
            });
            it('should validate minimum length for arrays', () => {
                const rule = validation_utils_1.ValidationUtils.Rules.minLength(3);
                expect(rule.validate([1, 2, 3])).toBe(true);
                expect(rule.validate([1, 2, 3, 4])).toBe(true);
                expect(rule.validate([1, 2])).toBe(false);
            });
        });
        describe('maxLength', () => {
            it('should validate maximum length for strings', () => {
                const rule = validation_utils_1.ValidationUtils.Rules.maxLength(5);
                expect(rule.validate('hello')).toBe(true);
                expect(rule.validate('hi')).toBe(true);
                expect(rule.validate('hello world')).toBe(false);
            });
        });
        describe('min', () => {
            it('should validate minimum value', () => {
                const rule = validation_utils_1.ValidationUtils.Rules.min(10);
                expect(rule.validate(10)).toBe(true);
                expect(rule.validate(15)).toBe(true);
                expect(rule.validate(5)).toBe(false);
            });
        });
        describe('max', () => {
            it('should validate maximum value', () => {
                const rule = validation_utils_1.ValidationUtils.Rules.max(10);
                expect(rule.validate(10)).toBe(true);
                expect(rule.validate(5)).toBe(true);
                expect(rule.validate(15)).toBe(false);
            });
        });
        describe('pattern', () => {
            it('should validate regex pattern', () => {
                const rule = validation_utils_1.ValidationUtils.Rules.pattern(/^\d{3}-\d{3}-\d{4}$/);
                expect(rule.validate('************')).toBe(true);
                expect(rule.validate('invalid-phone')).toBe(false);
            });
        });
        describe('email', () => {
            it('should validate email addresses', () => {
                const rule = validation_utils_1.ValidationUtils.Rules.email();
                expect(rule.validate('<EMAIL>')).toBe(true);
                expect(rule.validate('<EMAIL>')).toBe(true);
                expect(rule.validate('invalid-email')).toBe(false);
                expect(rule.validate('@example.com')).toBe(false);
            });
        });
        describe('url', () => {
            it('should validate URLs', () => {
                const rule = validation_utils_1.ValidationUtils.Rules.url();
                expect(rule.validate('https://example.com')).toBe(true);
                expect(rule.validate('http://localhost:3000')).toBe(true);
                expect(rule.validate('ftp://files.example.com')).toBe(true);
                expect(rule.validate('invalid-url')).toBe(false);
            });
        });
        describe('uuid', () => {
            it('should validate UUIDs', () => {
                const rule = validation_utils_1.ValidationUtils.Rules.uuid();
                expect(rule.validate('123e4567-e89b-12d3-a456-************')).toBe(true);
                expect(rule.validate('550e8400-e29b-41d4-a716-************')).toBe(true);
                expect(rule.validate('invalid-uuid')).toBe(false);
                expect(rule.validate('123e4567-e89b-12d3-a456')).toBe(false);
            });
        });
        describe('in', () => {
            it('should validate value is in allowed list', () => {
                const rule = validation_utils_1.ValidationUtils.Rules.in(['red', 'green', 'blue']);
                expect(rule.validate('red')).toBe(true);
                expect(rule.validate('green')).toBe(true);
                expect(rule.validate('yellow')).toBe(false);
            });
        });
        describe('notIn', () => {
            it('should validate value is not in forbidden list', () => {
                const rule = validation_utils_1.ValidationUtils.Rules.notIn(['admin', 'root']);
                expect(rule.validate('user')).toBe(true);
                expect(rule.validate('guest')).toBe(true);
                expect(rule.validate('admin')).toBe(false);
                expect(rule.validate('root')).toBe(false);
            });
        });
        describe('custom', () => {
            it('should validate with custom function', () => {
                const rule = validation_utils_1.ValidationUtils.Rules.custom((value) => value % 2 === 0, 'Value must be even', 'EVEN_NUMBER');
                expect(rule.validate(4)).toBe(true);
                expect(rule.validate(6)).toBe(true);
                expect(rule.validate(3)).toBe(false);
                expect(rule.validate(5)).toBe(false);
            });
        });
    });
    describe('utility functions', () => {
        describe('isValidEmail', () => {
            it('should validate email addresses', () => {
                expect(validation_utils_1.ValidationUtils.isValidEmail('<EMAIL>')).toBe(true);
                expect(validation_utils_1.ValidationUtils.isValidEmail('<EMAIL>')).toBe(true);
                expect(validation_utils_1.ValidationUtils.isValidEmail('invalid-email')).toBe(false);
                expect(validation_utils_1.ValidationUtils.isValidEmail('@example.com')).toBe(false);
                expect(validation_utils_1.ValidationUtils.isValidEmail(123)).toBe(false);
            });
        });
        describe('isValidUrl', () => {
            it('should validate URLs', () => {
                expect(validation_utils_1.ValidationUtils.isValidUrl('https://example.com')).toBe(true);
                expect(validation_utils_1.ValidationUtils.isValidUrl('http://localhost:3000')).toBe(true);
                expect(validation_utils_1.ValidationUtils.isValidUrl('invalid-url')).toBe(false);
                expect(validation_utils_1.ValidationUtils.isValidUrl(123)).toBe(false);
            });
        });
        describe('isValidUUID', () => {
            it('should validate UUIDs', () => {
                expect(validation_utils_1.ValidationUtils.isValidUUID('123e4567-e89b-12d3-a456-************')).toBe(true);
                expect(validation_utils_1.ValidationUtils.isValidUUID('550e8400-e29b-41d4-a716-************')).toBe(true);
                expect(validation_utils_1.ValidationUtils.isValidUUID('invalid-uuid')).toBe(false);
                expect(validation_utils_1.ValidationUtils.isValidUUID(123)).toBe(false);
            });
        });
        describe('isValidDateString', () => {
            it('should validate date strings', () => {
                expect(validation_utils_1.ValidationUtils.isValidDateString('2023-01-01')).toBe(true);
                expect(validation_utils_1.ValidationUtils.isValidDateString('2023-01-01T12:00:00Z')).toBe(true);
                expect(validation_utils_1.ValidationUtils.isValidDateString('invalid-date')).toBe(false);
                expect(validation_utils_1.ValidationUtils.isValidDateString(123)).toBe(false);
            });
        });
        describe('isValidIpAddress', () => {
            it('should validate IP addresses', () => {
                expect(validation_utils_1.ValidationUtils.isValidIpAddress('***********')).toBe(true);
                expect(validation_utils_1.ValidationUtils.isValidIpAddress('********')).toBe(true);
                expect(validation_utils_1.ValidationUtils.isValidIpAddress('2001:0db8:85a3:0000:0000:8a2e:0370:7334')).toBe(true);
                expect(validation_utils_1.ValidationUtils.isValidIpAddress('256.1.1.1')).toBe(false);
                expect(validation_utils_1.ValidationUtils.isValidIpAddress('invalid-ip')).toBe(false);
                expect(validation_utils_1.ValidationUtils.isValidIpAddress(123)).toBe(false);
            });
        });
        describe('isValidPhoneNumber', () => {
            it('should validate phone numbers', () => {
                expect(validation_utils_1.ValidationUtils.isValidPhoneNumber('+1234567890')).toBe(true);
                expect(validation_utils_1.ValidationUtils.isValidPhoneNumber('1234567890')).toBe(true);
                expect(validation_utils_1.ValidationUtils.isValidPhoneNumber('+****************')).toBe(true);
                expect(validation_utils_1.ValidationUtils.isValidPhoneNumber('invalid-phone')).toBe(false);
                expect(validation_utils_1.ValidationUtils.isValidPhoneNumber(123)).toBe(false);
            });
        });
        describe('isValidCreditCard', () => {
            it('should validate credit card numbers using Luhn algorithm', () => {
                expect(validation_utils_1.ValidationUtils.isValidCreditCard('****************')).toBe(true); // Valid Visa
                expect(validation_utils_1.ValidationUtils.isValidCreditCard('4532 0151 1283 0366')).toBe(true); // With spaces
                expect(validation_utils_1.ValidationUtils.isValidCreditCard('1234567890123456')).toBe(false); // Invalid
                expect(validation_utils_1.ValidationUtils.isValidCreditCard('invalid-card')).toBe(false);
                expect(validation_utils_1.ValidationUtils.isValidCreditCard(123)).toBe(false);
            });
        });
        describe('sanitizeString', () => {
            it('should sanitize string input', () => {
                expect(validation_utils_1.ValidationUtils.sanitizeString('  hello   world  ')).toBe('hello world');
                expect(validation_utils_1.ValidationUtils.sanitizeString('test<script>alert()</script>')).toBe('testscriptalert()/script');
                expect(validation_utils_1.ValidationUtils.sanitizeString(123)).toBe('');
            });
        });
        describe('normalizeEmail', () => {
            it('should normalize email addresses', () => {
                expect(validation_utils_1.ValidationUtils.normalizeEmail('  <EMAIL>  ')).toBe('<EMAIL>');
                expect(validation_utils_1.ValidationUtils.normalizeEmail('<EMAIL>')).toBe('<EMAIL>');
                expect(validation_utils_1.ValidationUtils.normalizeEmail('invalid-email')).toBe('invalid-email');
            });
        });
        describe('validatePasswordStrength', () => {
            it('should validate strong passwords', () => {
                const result = validation_utils_1.ValidationUtils.validatePasswordStrength('StrongP@ssw0rd123');
                expect(result.isValid).toBe(true);
                expect(result.score).toBeGreaterThan(4);
                expect(result.feedback).toHaveLength(0);
            });
            it('should reject weak passwords', () => {
                const result = validation_utils_1.ValidationUtils.validatePasswordStrength('weak');
                expect(result.isValid).toBe(false);
                expect(result.score).toBeLessThan(4);
                expect(result.feedback.length).toBeGreaterThan(0);
            });
            it('should handle non-string input', () => {
                const result = validation_utils_1.ValidationUtils.validatePasswordStrength(123);
                expect(result.isValid).toBe(false);
                expect(result.score).toBe(0);
                expect(result.feedback).toContain('Password must be a string');
            });
        });
    });
    describe('SchemaBuilder', () => {
        it('should build schema using fluent interface', () => {
            const schema = validation_utils_1.ValidationUtils.schema()
                .field('name')
                .required()
                .type('string')
                .rule(validation_utils_1.ValidationUtils.Rules.minLength(2))
                .end()
                .field('age')
                .required()
                .type('number')
                .rule(validation_utils_1.ValidationUtils.Rules.min(0))
                .end()
                .allowUnknownFields(false)
                .build();
            expect(schema.fields).toHaveLength(2);
            expect(schema.fields[0].field).toBe('name');
            expect(schema.fields[0].required).toBe(true);
            expect(schema.fields[0].type).toBe('string');
            expect(schema.fields[1].field).toBe('age');
            expect(schema.allowUnknown).toBe(false);
        });
        it('should build schema with default values and descriptions', () => {
            const schema = validation_utils_1.ValidationUtils.schema()
                .field('status')
                .type('string')
                .default('active')
                .description('User status')
                .end()
                .build();
            expect(schema.fields[0].defaultValue).toBe('active');
            expect(schema.fields[0].description).toBe('User status');
        });
        it('should build schema with custom validators', () => {
            const customValidator = async (value) => ({
                isValid: value === 'valid',
                errors: [],
                warnings: [],
            });
            const schema = validation_utils_1.ValidationUtils.schema()
                .field('custom')
                .validator(customValidator)
                .end()
                .build();
            expect(schema.fields[0].validator).toBe(customValidator);
        });
        it('should build schema with schema-level rules', () => {
            const schemaRule = validation_utils_1.ValidationUtils.Rules.custom((obj) => obj.password === obj.confirmPassword, 'Passwords must match', 'PASSWORD_MISMATCH');
            const schema = validation_utils_1.ValidationUtils.schema()
                .field('password').type('string').end()
                .field('confirmPassword').type('string').end()
                .rule(schemaRule)
                .build();
            expect(schema.rules).toHaveLength(1);
            expect(schema.rules[0]).toBe(schemaRule);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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