{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\domain\\base-service.spec.ts", "mappings": ";;AAAA,4DAAuF;AACvF,wEAAoE;AACpE,0EAAqE;AACrE,sEAAiE;AAQjE,MAAM,WAAY,SAAQ,0BAA+B;IACvD;QACE,KAAK,CAAC,aAAa,CAAC,CAAC;IACvB,CAAC;IAED,uCAAuC;IAChC,iBAAiB,CAAC,UAAuC,EAAE;QAChE,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC;IAEM,KAAK,CAAC,oBAAoB,CAC/B,SAA2B,EAC3B,OAA2B,EAC3B,aAAqB;QAErB,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;IAClE,CAAC;IAEM,yBAAyB,CAC9B,MAAS,EACT,cAAsC;QAEtC,OAAO,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;IAC5D,CAAC;IAEM,uBAAuB,CAAC,UAAoC;QACjE,OAAO,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;IAC9C,CAAC;IAEM,qBAAqB,CAAC,UAAoC;QAC/D,OAAO,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;IAC5C,CAAC;IAEM,WAAW,CAAI,IAAQ,EAAE,QAA8B;QAC5D,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IACtC,CAAC;IAEM,WAAW,CAChB,KAAc,EACd,MAAiB,EACjB,QAA8B;QAE9B,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAEM,KAAK,CAAC,kBAAkB,CAC7B,SAA2B,EAC3B,aAAqB,CAAC,EACtB,YAAoB,IAAI;QAExB,OAAO,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;IAC/D,CAAC;IAEM,KAAK,CAAC,mBAAmB,CAC9B,UAAgC,EAChC,WAAoB,KAAK;QAEzB,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IACpD,CAAC;IAEM,yBAAyB;QAC9B,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACtC,CAAC;IAEM,aAAa,CAClB,UAAkB,EAClB,KAAa,EACb,IAA6B;QAE7B,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;IACjD,CAAC;IAED,0BAA0B;IAC1B,KAAK,CAAC,wBAAwB,CAAC,KAAa;QAC1C,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC,CAAC;QAE5D,OAAO,IAAI,CAAC,gBAAgB,CAC1B,KAAK,IAAI,EAAE;YACT,IAAI,KAAK,KAAK,OAAO,EAAE,CAAC;gBACtB,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;YAChC,CAAC;YACD,OAAO,cAAc,KAAK,EAAE,CAAC;QAC/B,CAAC,EACD,OAAO,EACP,0BAA0B,CAC3B,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YACd,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,OAAO,MAAM,CAAC,IAAK,CAAC;YACtB,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,KAAM,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AASD,MAAM,uBAAwB,SAAQ,sCAA6B;IACjE,YAA6B,UAAmB;QAC9C,KAAK,EAAE,CAAC;QADmB,eAAU,GAAV,UAAU,CAAS;IAEhD,CAAC;IAED,aAAa,CAAC,MAAkB;QAC9B,OAAO,IAAI,CAAC,UAAU,IAAI,MAAM,CAAC,OAAO,CAAC;IAC3C,CAAC;IAED,cAAc;QACZ,OAAO,iBAAiB,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,aAAa,CAAC;IACzE,CAAC;CACF;AAOD,MAAM,aAAc,SAAQ,uCAAqC;IAC/D,YAAY,KAAyB,EAAE,EAAmB;QACxD,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IACnB,CAAC;IAED,YAAY,CAAC,OAAe;QAC1B,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;QACvD,IAAY,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;CACF;AAED,MAAM,eAAgB,SAAQ,mCAAoC;IAChE,YAAY,WAA2B,EAAE,IAAyB;QAChE,KAAK,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IAC3B,CAAC;CACF;AAED,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;IAC3B,IAAI,OAAoB,CAAC;IAEzB,UAAU,CAAC,GAAG,EAAE;QACd,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,OAAO,GAAG,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAE5C,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,OAAO,GAAgC;gBAC3C,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,YAAY;gBACtB,aAAa,EAAE,iBAAiB;gBAChC,WAAW,EAAE,cAAc;aAC5B,CAAC;YAEF,MAAM,OAAO,GAAG,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAEnD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACtD,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,OAAO,GAAG,OAAO,CAAC,iBAAiB,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC;YAElE,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,OAAO,GAAG,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAC5C,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEzD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,oBAAoB,CAAC,SAAS,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;YAEhF,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACpC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;YAClE,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,OAAO,GAAG,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAC5C,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;YACtC,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAErD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,oBAAoB,CAAC,SAAS,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;YAEhF,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,CAAC;YACpC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,MAAM,OAAO,GAAG,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAC5C,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAClD,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,CAC9D,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,oBAAoB,CAAC,SAAS,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;YAEhF,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,QAAS,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,OAAO,GAAG,OAAO,CAAC,iBAAiB,CAAC,EAAE,aAAa,EAAE,kBAAkB,EAAE,CAAC,CAAC;YACjF,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEzD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,oBAAoB,CAAC,SAAS,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;YAEhF,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,qEAAqE;QACvE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;YACtE,MAAM,MAAM,GAAe,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;YACpE,MAAM,cAAc,GAAG;gBACrB,IAAI,uBAAuB,CAAC,IAAI,CAAC;gBACjC,IAAI,uBAAuB,CAAC,IAAI,CAAC;aAClC,CAAC;YAEF,MAAM,MAAM,GAAG,OAAO,CAAC,yBAAyB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;YAEzE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;YACtE,MAAM,MAAM,GAAe,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;YACrE,MAAM,cAAc,GAAG;gBACrB,IAAI,uBAAuB,CAAC,IAAI,CAAC;gBACjC,IAAI,uBAAuB,CAAC,KAAK,CAAC;aACnC,CAAC;YAEF,MAAM,MAAM,GAAG,OAAO,CAAC,yBAAyB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;YAEzE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAC;YACjE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,MAAM,GAAe,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;YACpE,MAAM,cAAc,GAAoC,EAAE,CAAC;YAE3D,MAAM,MAAM,GAAG,OAAO,CAAC,yBAAyB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;YAEzE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,UAAU,GAAG,IAAI,aAAa,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;YAC9D,MAAM,UAAU,GAAG,IAAI,aAAa,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;YAE9D,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YACnC,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YACnC,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAEnC,MAAM,MAAM,GAAG,OAAO,CAAC,uBAAuB,CAAC,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC;YAEzE,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,SAAS,GAAG,IAAI,aAAa,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;YAE3D,MAAM,MAAM,GAAG,OAAO,CAAC,uBAAuB,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YAE5D,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,UAAU,GAAG,IAAI,aAAa,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;YAC9D,MAAM,UAAU,GAAG,IAAI,aAAa,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;YAE9D,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YACnC,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAEnC,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChD,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEhD,OAAO,CAAC,qBAAqB,CAAC,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC;YAExD,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChD,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC;YAElE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACjC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,MAAM,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;YAErC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,cAAc,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;YAEnF,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;YACtC,MAAM,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAEtD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEzD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,kBAAkB,CAAC,SAAS,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;YAEnE,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC/B,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE;iBACxB,qBAAqB,CAAC,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC;iBAC7C,qBAAqB,CAAC,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC;iBAC7C,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEhC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,kBAAkB,CAAC,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YAElE,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC/B,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;YAC5C,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAErD,MAAM,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;YAC/F,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,sBAAsB;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;YAC1C,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;YAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAErD,MAAM,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;YAC9F,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;YACpE,MAAM,UAAU,GAAG;gBACjB,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;gBACtC,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;gBACtC,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;aACvC,CAAC;YAEF,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;YAEpE,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,UAAU,GAAG;gBACjB,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;gBACtC,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC;gBACjD,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;aACvC,CAAC;YAEF,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;YAEpE,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,MAAM,UAAU,GAAG;gBACjB,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;gBACtC,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC;gBACjD,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;aACvC,CAAC;YAEF,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YAErE,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YAE7D,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,aAAa,GAAG,OAAO,CAAC,yBAAyB,EAAE,CAAC;YAE1D,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,CAAC,OAAO,aAAa,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC5C,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,GAAG,GAAG,OAAO,CAAC,yBAAyB,EAAE,CAAC;YAChD,MAAM,GAAG,GAAG,OAAO,CAAC,yBAAyB,EAAE,CAAC;YAEhD,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAC5B,8CAA8C;YAC9C,MAAM,CAAC,GAAG,EAAE;gBACV,OAAO,CAAC,aAAa,CAAC,aAAa,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;YAC/D,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;YACtE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;YAEpE,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,MAAM,CAAC,OAAO,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,MAAM,eAAgB,SAAQ,0BAAW;YACvC;gBACE,KAAK,CAAC,iBAAiB,CAAC,CAAC;YAC3B,CAAC;YAED,KAAK,CAAC,iBAAiB;gBACrB,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,EAAE,MAAM,EAAE,eAAe,EAAE,CAAC,CAAC;gBAEhE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CACxC,KAAK,IAAI,EAAE,CAAC,iBAAiB,EAC7B,OAAO,EACP,mBAAmB,CACpB,CAAC;gBAEF,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,IAAK,CAAC,CAAC,CAAC,QAAQ,CAAC;YAClD,CAAC;SACF;QAED,EAAE,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;YAC1C,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;YAC9C,MAAM,MAAM,GAAG,MAAM,eAAe,CAAC,iBAAiB,EAAE,CAAC;YAEzD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,OAAO,GAAG,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAC5C,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YAE9D,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,oBAAoB,CAAC,SAAS,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;YAEhF,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,OAAO,GAAG,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAC5C,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAEpD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,oBAAoB,CAAC,SAAS,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;YAEhF,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACtD,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,CAAC,CAC1C,CAAC;YAEF,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YACrE,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE3B,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;YAClC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,OAAO,GAAG,SAAS,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,wCAAwC;QAC1F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACrE,MAAM,UAAU,GAAG;gBACjB,GAAG,EAAE,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;gBACnE,GAAG,EAAE,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC;gBACrE,GAAG,EAAE,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC;aACrE,CAAC;YAEF,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YAErE,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\domain\\base-service.spec.ts"], "sourcesContent": ["import { BaseService, ServiceContext, ServiceResult } from '../../domain/base-service';\r\nimport { BaseSpecification } from '../../domain/base-specification';\r\nimport { BaseAggregateRoot } from '../../domain/base-aggregate-root';\r\nimport { BaseDomainEvent } from '../../domain/base-domain-event';\r\nimport { UniqueEntityId } from '../../value-objects/unique-entity-id.value-object';\r\n\r\n// Test implementations\r\ninterface TestServiceContext extends ServiceContext {\r\n  customField?: string;\r\n}\r\n\r\nclass TestService extends BaseService<TestServiceContext> {\r\n  constructor() {\r\n    super('TestService');\r\n  }\r\n\r\n  // Expose protected methods for testing\r\n  public testCreateContext(options: Partial<TestServiceContext> = {}): TestServiceContext {\r\n    return this.createContext(options);\r\n  }\r\n\r\n  public async testExecuteOperation<T>(\r\n    operation: () => Promise<T>,\r\n    context: TestServiceContext,\r\n    operationName: string\r\n  ): Promise<ServiceResult<T>> {\r\n    return this.executeOperation(operation, context, operationName);\r\n  }\r\n\r\n  public testValidateBusinessRules<T>(\r\n    entity: T,\r\n    specifications: BaseSpecification<T>[]\r\n  ): ServiceResult<void> {\r\n    return this.validateBusinessRules(entity, specifications);\r\n  }\r\n\r\n  public testCollectDomainEvents(aggregates: BaseAggregateRoot<any>[]): BaseDomainEvent[] {\r\n    return this.collectDomainEvents(aggregates);\r\n  }\r\n\r\n  public testClearDomainEvents(aggregates: BaseAggregateRoot<any>[]): void {\r\n    return this.clearDomainEvents(aggregates);\r\n  }\r\n\r\n  public testSuccess<T>(data?: T, metadata?: Record<string, any>): ServiceResult<T> {\r\n    return this.success(data, metadata);\r\n  }\r\n\r\n  public testFailure(\r\n    error?: string,\r\n    errors?: string[],\r\n    metadata?: Record<string, any>\r\n  ): ServiceResult<never> {\r\n    return this.failure(error, errors, metadata);\r\n  }\r\n\r\n  public async testRetryOperation<T>(\r\n    operation: () => Promise<T>,\r\n    maxRetries: number = 3,\r\n    baseDelay: number = 1000\r\n  ): Promise<T> {\r\n    return this.retryOperation(operation, maxRetries, baseDelay);\r\n  }\r\n\r\n  public async testExecuteParallel<T>(\r\n    operations: (() => Promise<T>)[],\r\n    failFast: boolean = false\r\n  ): Promise<ServiceResult<T>[]> {\r\n    return this.executeParallel(operations, failFast);\r\n  }\r\n\r\n  public testGenerateCorrelationId(): string {\r\n    return this.generateCorrelationId();\r\n  }\r\n\r\n  public testLogMetric(\r\n    metricName: string,\r\n    value: number,\r\n    tags?: Record<string, string>\r\n  ): void {\r\n    return this.logMetric(metricName, value, tags);\r\n  }\r\n\r\n  // Test business operation\r\n  async performBusinessOperation(input: string): Promise<string> {\r\n    const context = this.createContext({ customField: 'test' });\r\n    \r\n    return this.executeOperation(\r\n      async () => {\r\n        if (input === 'error') {\r\n          throw new Error('Test error');\r\n        }\r\n        return `Processed: ${input}`;\r\n      },\r\n      context,\r\n      'performBusinessOperation'\r\n    ).then(result => {\r\n      if (result.success) {\r\n        return result.data!;\r\n      }\r\n      throw new Error(result.error!);\r\n    });\r\n  }\r\n}\r\n\r\n// Test entity and specifications\r\ninterface TestEntity {\r\n  id: string;\r\n  name: string;\r\n  isValid: boolean;\r\n}\r\n\r\nclass TestEntitySpecification extends BaseSpecification<TestEntity> {\r\n  constructor(private readonly shouldPass: boolean) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(entity: TestEntity): boolean {\r\n    return this.shouldPass && entity.isValid;\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Entity should ${this.shouldPass ? 'pass' : 'fail'} validation`;\r\n  }\r\n}\r\n\r\n// Test aggregate for domain events\r\ninterface TestAggregateProps {\r\n  name: string;\r\n}\r\n\r\nclass TestAggregate extends BaseAggregateRoot<TestAggregateProps> {\r\n  constructor(props: TestAggregateProps, id?: UniqueEntityId) {\r\n    super(props, id);\r\n  }\r\n\r\n  addTestEvent(message: string): void {\r\n    const event = new TestDomainEvent(this.id, { message });\r\n    (this as any).addDomainEvent(event);\r\n  }\r\n}\r\n\r\nclass TestDomainEvent extends BaseDomainEvent<{ message: string }> {\r\n  constructor(aggregateId: UniqueEntityId, data: { message: string }) {\r\n    super(aggregateId, data);\r\n  }\r\n}\r\n\r\ndescribe('BaseService', () => {\r\n  let service: TestService;\r\n\r\n  beforeEach(() => {\r\n    service = new TestService();\r\n  });\r\n\r\n  describe('context creation', () => {\r\n    it('should create context with default timestamp', () => {\r\n      const context = service.testCreateContext();\r\n\r\n      expect(context.timestamp).toBeInstanceOf(Date);\r\n      expect(context.timestamp.getTime()).toBeLessThanOrEqual(Date.now());\r\n    });\r\n\r\n    it('should create context with provided options', () => {\r\n      const options: Partial<TestServiceContext> = {\r\n        userId: 'user-123',\r\n        tenantId: 'tenant-456',\r\n        correlationId: 'correlation-789',\r\n        customField: 'custom-value'\r\n      };\r\n\r\n      const context = service.testCreateContext(options);\r\n\r\n      expect(context.userId).toBe('user-123');\r\n      expect(context.tenantId).toBe('tenant-456');\r\n      expect(context.correlationId).toBe('correlation-789');\r\n      expect(context.customField).toBe('custom-value');\r\n      expect(context.timestamp).toBeInstanceOf(Date);\r\n    });\r\n\r\n    it('should merge options with defaults', () => {\r\n      const context = service.testCreateContext({ userId: 'user-123' });\r\n\r\n      expect(context.userId).toBe('user-123');\r\n      expect(context.timestamp).toBeInstanceOf(Date);\r\n      expect(context.tenantId).toBeUndefined();\r\n    });\r\n  });\r\n\r\n  describe('operation execution', () => {\r\n    it('should execute successful operation', async () => {\r\n      const context = service.testCreateContext();\r\n      const operation = jest.fn().mockResolvedValue('success');\r\n\r\n      const result = await service.testExecuteOperation(operation, context, 'testOp');\r\n\r\n      expect(result.success).toBe(true);\r\n      expect(result.data).toBe('success');\r\n      expect(result.error).toBeUndefined();\r\n      expect(result.metadata).toHaveProperty('duration');\r\n      expect(result.metadata).toHaveProperty('operationName', 'testOp');\r\n      expect(operation).toHaveBeenCalledTimes(1);\r\n    });\r\n\r\n    it('should handle operation failure', async () => {\r\n      const context = service.testCreateContext();\r\n      const error = new Error('Test error');\r\n      const operation = jest.fn().mockRejectedValue(error);\r\n\r\n      const result = await service.testExecuteOperation(operation, context, 'testOp');\r\n\r\n      expect(result.success).toBe(false);\r\n      expect(result.data).toBeUndefined();\r\n      expect(result.error).toBe('Test error');\r\n      expect(result.metadata).toHaveProperty('duration');\r\n      expect(result.metadata).toHaveProperty('errorType', 'Error');\r\n    });\r\n\r\n    it('should measure execution time', async () => {\r\n      const context = service.testCreateContext();\r\n      const operation = jest.fn().mockImplementation(() => \r\n        new Promise(resolve => setTimeout(() => resolve('done'), 10))\r\n      );\r\n\r\n      const result = await service.testExecuteOperation(operation, context, 'testOp');\r\n\r\n      expect(result.success).toBe(true);\r\n      expect(result.metadata!.duration).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should include correlation ID in logs', async () => {\r\n      const context = service.testCreateContext({ correlationId: 'test-correlation' });\r\n      const operation = jest.fn().mockResolvedValue('success');\r\n\r\n      const result = await service.testExecuteOperation(operation, context, 'testOp');\r\n\r\n      expect(result.success).toBe(true);\r\n      // Correlation ID should be used in logging (tested through behavior)\r\n    });\r\n  });\r\n\r\n  describe('business rule validation', () => {\r\n    it('should pass validation when all specifications are satisfied', () => {\r\n      const entity: TestEntity = { id: '1', name: 'Test', isValid: true };\r\n      const specifications = [\r\n        new TestEntitySpecification(true),\r\n        new TestEntitySpecification(true)\r\n      ];\r\n\r\n      const result = service.testValidateBusinessRules(entity, specifications);\r\n\r\n      expect(result.success).toBe(true);\r\n      expect(result.errors).toBeUndefined();\r\n    });\r\n\r\n    it('should fail validation when specifications are not satisfied', () => {\r\n      const entity: TestEntity = { id: '1', name: 'Test', isValid: false };\r\n      const specifications = [\r\n        new TestEntitySpecification(true),\r\n        new TestEntitySpecification(false)\r\n      ];\r\n\r\n      const result = service.testValidateBusinessRules(entity, specifications);\r\n\r\n      expect(result.success).toBe(false);\r\n      expect(result.errors).toHaveLength(2);\r\n      expect(result.errors).toContain('Entity should pass validation');\r\n      expect(result.errors).toContain('Entity should fail validation');\r\n    });\r\n\r\n    it('should handle empty specifications array', () => {\r\n      const entity: TestEntity = { id: '1', name: 'Test', isValid: true };\r\n      const specifications: BaseSpecification<TestEntity>[] = [];\r\n\r\n      const result = service.testValidateBusinessRules(entity, specifications);\r\n\r\n      expect(result.success).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('domain event management', () => {\r\n    it('should collect domain events from aggregates', () => {\r\n      const aggregate1 = new TestAggregate({ name: 'Aggregate 1' });\r\n      const aggregate2 = new TestAggregate({ name: 'Aggregate 2' });\r\n\r\n      aggregate1.addTestEvent('Event 1');\r\n      aggregate2.addTestEvent('Event 2');\r\n      aggregate2.addTestEvent('Event 3');\r\n\r\n      const events = service.testCollectDomainEvents([aggregate1, aggregate2]);\r\n\r\n      expect(events).toHaveLength(3);\r\n      expect(events[0]).toBeInstanceOf(TestDomainEvent);\r\n      expect(events[1]).toBeInstanceOf(TestDomainEvent);\r\n      expect(events[2]).toBeInstanceOf(TestDomainEvent);\r\n    });\r\n\r\n    it('should handle aggregates with no events', () => {\r\n      const aggregate = new TestAggregate({ name: 'Aggregate' });\r\n\r\n      const events = service.testCollectDomainEvents([aggregate]);\r\n\r\n      expect(events).toHaveLength(0);\r\n    });\r\n\r\n    it('should clear domain events from aggregates', () => {\r\n      const aggregate1 = new TestAggregate({ name: 'Aggregate 1' });\r\n      const aggregate2 = new TestAggregate({ name: 'Aggregate 2' });\r\n\r\n      aggregate1.addTestEvent('Event 1');\r\n      aggregate2.addTestEvent('Event 2');\r\n\r\n      expect(aggregate1.domainEvents).toHaveLength(1);\r\n      expect(aggregate2.domainEvents).toHaveLength(1);\r\n\r\n      service.testClearDomainEvents([aggregate1, aggregate2]);\r\n\r\n      expect(aggregate1.domainEvents).toHaveLength(0);\r\n      expect(aggregate2.domainEvents).toHaveLength(0);\r\n    });\r\n  });\r\n\r\n  describe('result creation helpers', () => {\r\n    it('should create success result', () => {\r\n      const result = service.testSuccess('data', { extra: 'metadata' });\r\n\r\n      expect(result.success).toBe(true);\r\n      expect(result.data).toBe('data');\r\n      expect(result.error).toBeUndefined();\r\n      expect(result.metadata).toEqual({ extra: 'metadata' });\r\n    });\r\n\r\n    it('should create success result without data', () => {\r\n      const result = service.testSuccess();\r\n\r\n      expect(result.success).toBe(true);\r\n      expect(result.data).toBeUndefined();\r\n    });\r\n\r\n    it('should create failure result with single error', () => {\r\n      const result = service.testFailure('Single error', undefined, { context: 'test' });\r\n\r\n      expect(result.success).toBe(false);\r\n      expect(result.error).toBe('Single error');\r\n      expect(result.errors).toBeUndefined();\r\n      expect(result.metadata).toEqual({ context: 'test' });\r\n    });\r\n\r\n    it('should create failure result with multiple errors', () => {\r\n      const errors = ['Error 1', 'Error 2'];\r\n      const result = service.testFailure(undefined, errors);\r\n\r\n      expect(result.success).toBe(false);\r\n      expect(result.error).toBeUndefined();\r\n      expect(result.errors).toEqual(errors);\r\n    });\r\n  });\r\n\r\n  describe('retry mechanism', () => {\r\n    it('should succeed on first attempt', async () => {\r\n      const operation = jest.fn().mockResolvedValue('success');\r\n\r\n      const result = await service.testRetryOperation(operation, 3, 100);\r\n\r\n      expect(result).toBe('success');\r\n      expect(operation).toHaveBeenCalledTimes(1);\r\n    });\r\n\r\n    it('should retry on failure and eventually succeed', async () => {\r\n      const operation = jest.fn()\r\n        .mockRejectedValueOnce(new Error('Attempt 1'))\r\n        .mockRejectedValueOnce(new Error('Attempt 2'))\r\n        .mockResolvedValue('success');\r\n\r\n      const result = await service.testRetryOperation(operation, 3, 10);\r\n\r\n      expect(result).toBe('success');\r\n      expect(operation).toHaveBeenCalledTimes(3);\r\n    });\r\n\r\n    it('should fail after max retries', async () => {\r\n      const error = new Error('Persistent error');\r\n      const operation = jest.fn().mockRejectedValue(error);\r\n\r\n      await expect(service.testRetryOperation(operation, 2, 10)).rejects.toThrow('Persistent error');\r\n      expect(operation).toHaveBeenCalledTimes(3); // Initial + 2 retries\r\n    });\r\n\r\n    it('should handle zero retries', async () => {\r\n      const error = new Error('Immediate error');\r\n      const operation = jest.fn().mockRejectedValue(error);\r\n\r\n      await expect(service.testRetryOperation(operation, 0, 10)).rejects.toThrow('Immediate error');\r\n      expect(operation).toHaveBeenCalledTimes(1);\r\n    });\r\n  });\r\n\r\n  describe('parallel execution', () => {\r\n    it('should execute operations in parallel with fail fast', async () => {\r\n      const operations = [\r\n        jest.fn().mockResolvedValue('result1'),\r\n        jest.fn().mockResolvedValue('result2'),\r\n        jest.fn().mockResolvedValue('result3')\r\n      ];\r\n\r\n      const results = await service.testExecuteParallel(operations, true);\r\n\r\n      expect(results).toHaveLength(3);\r\n      expect(results.every(r => r.success)).toBe(true);\r\n      expect(results[0].data).toBe('result1');\r\n      expect(results[1].data).toBe('result2');\r\n      expect(results[2].data).toBe('result3');\r\n    });\r\n\r\n    it('should fail fast on first error', async () => {\r\n      const operations = [\r\n        jest.fn().mockResolvedValue('result1'),\r\n        jest.fn().mockRejectedValue(new Error('Error 2')),\r\n        jest.fn().mockResolvedValue('result3')\r\n      ];\r\n\r\n      const results = await service.testExecuteParallel(operations, true);\r\n\r\n      expect(results).toHaveLength(1);\r\n      expect(results[0].success).toBe(false);\r\n      expect(results[0].error).toBe('Error 2');\r\n    });\r\n\r\n    it('should execute all operations without fail fast', async () => {\r\n      const operations = [\r\n        jest.fn().mockResolvedValue('result1'),\r\n        jest.fn().mockRejectedValue(new Error('Error 2')),\r\n        jest.fn().mockResolvedValue('result3')\r\n      ];\r\n\r\n      const results = await service.testExecuteParallel(operations, false);\r\n\r\n      expect(results).toHaveLength(3);\r\n      expect(results[0].success).toBe(true);\r\n      expect(results[0].data).toBe('result1');\r\n      expect(results[1].success).toBe(false);\r\n      expect(results[1].error).toBe('Error 2');\r\n      expect(results[2].success).toBe(true);\r\n      expect(results[2].data).toBe('result3');\r\n    });\r\n\r\n    it('should handle empty operations array', async () => {\r\n      const results = await service.testExecuteParallel([], false);\r\n\r\n      expect(results).toHaveLength(0);\r\n    });\r\n  });\r\n\r\n  describe('utility methods', () => {\r\n    it('should generate correlation ID', () => {\r\n      const correlationId = service.testGenerateCorrelationId();\r\n\r\n      expect(correlationId).toBeDefined();\r\n      expect(typeof correlationId).toBe('string');\r\n      expect(correlationId.length).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should generate unique correlation IDs', () => {\r\n      const id1 = service.testGenerateCorrelationId();\r\n      const id2 = service.testGenerateCorrelationId();\r\n\r\n      expect(id1).not.toBe(id2);\r\n    });\r\n\r\n    it('should log metrics', () => {\r\n      // This test verifies the method doesn't throw\r\n      expect(() => {\r\n        service.testLogMetric('test.metric', 42, { tag1: 'value1' });\r\n      }).not.toThrow();\r\n    });\r\n  });\r\n\r\n  describe('integration scenarios', () => {\r\n    it('should handle complete business operation successfully', async () => {\r\n      const result = await service.performBusinessOperation('test input');\r\n\r\n      expect(result).toBe('Processed: test input');\r\n    });\r\n\r\n    it('should handle business operation failure', async () => {\r\n      await expect(service.performBusinessOperation('error')).rejects.toThrow('Test error');\r\n    });\r\n  });\r\n\r\n  describe('inheritance', () => {\r\n    class ExtendedService extends BaseService {\r\n      constructor() {\r\n        super('ExtendedService');\r\n      }\r\n\r\n      async extendedOperation(): Promise<string> {\r\n        const context = this.createContext({ userId: 'extended-user' });\r\n        \r\n        const result = await this.executeOperation(\r\n          async () => 'extended result',\r\n          context,\r\n          'extendedOperation'\r\n        );\r\n\r\n        return result.success ? result.data! : 'failed';\r\n      }\r\n    }\r\n\r\n    it('should support inheritance', async () => {\r\n      const extendedService = new ExtendedService();\r\n      const result = await extendedService.extendedOperation();\r\n\r\n      expect(result).toBe('extended result');\r\n    });\r\n  });\r\n\r\n  describe('error handling edge cases', () => {\r\n    it('should handle non-Error objects thrown', async () => {\r\n      const context = service.testCreateContext();\r\n      const operation = jest.fn().mockRejectedValue('string error');\r\n\r\n      const result = await service.testExecuteOperation(operation, context, 'testOp');\r\n\r\n      expect(result.success).toBe(false);\r\n      expect(result.error).toBe('string error');\r\n    });\r\n\r\n    it('should handle null/undefined errors', async () => {\r\n      const context = service.testCreateContext();\r\n      const operation = jest.fn().mockRejectedValue(null);\r\n\r\n      const result = await service.testExecuteOperation(operation, context, 'testOp');\r\n\r\n      expect(result.success).toBe(false);\r\n      expect(result.error).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('performance considerations', () => {\r\n    it('should handle large number of parallel operations', async () => {\r\n      const operations = Array.from({ length: 100 }, (_, i) => \r\n        jest.fn().mockResolvedValue(`result${i}`)\r\n      );\r\n\r\n      const startTime = Date.now();\r\n      const results = await service.testExecuteParallel(operations, false);\r\n      const endTime = Date.now();\r\n\r\n      expect(results).toHaveLength(100);\r\n      expect(results.every(r => r.success)).toBe(true);\r\n      expect(endTime - startTime).toBeLessThan(1000); // Should be fast due to parallelization\r\n    });\r\n\r\n    it('should handle operations with varying execution times', async () => {\r\n      const operations = [\r\n        () => new Promise(resolve => setTimeout(() => resolve('fast'), 10)),\r\n        () => new Promise(resolve => setTimeout(() => resolve('medium'), 50)),\r\n        () => new Promise(resolve => setTimeout(() => resolve('slow'), 100))\r\n      ];\r\n\r\n      const results = await service.testExecuteParallel(operations, false);\r\n\r\n      expect(results).toHaveLength(3);\r\n      expect(results.every(r => r.success)).toBe(true);\r\n    });\r\n  });\r\n});"], "version": 3}