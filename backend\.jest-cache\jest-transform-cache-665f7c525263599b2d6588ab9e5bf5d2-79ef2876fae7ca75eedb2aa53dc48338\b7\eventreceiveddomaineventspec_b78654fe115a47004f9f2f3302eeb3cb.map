{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\event-received.domain-event.spec.ts", "mappings": ";;AAAA,gFAAkG;AAClG,gEAA8D;AAC9D,iEAAwD;AACxD,yEAAgE;AAChE,4GAA2F;AAC3F,kHAAiG;AAEjG,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;IACxC,IAAI,WAA2B,CAAC;IAChC,IAAI,SAAiC,CAAC;IAEtC,UAAU,CAAC,GAAG,EAAE;QACd,WAAW,GAAG,8BAAc,CAAC,QAAQ,EAAE,CAAC;QACxC,SAAS,GAAG;YACV,SAAS,EAAE,2BAAS,CAAC,gBAAgB;YACrC,QAAQ,EAAE,mCAAa,CAAC,IAAI;YAC5B,MAAM,EAAE,uCAAW,CAAC,MAAM,CAAC;gBACzB,IAAI,EAAE,oBAAoB;gBAC1B,UAAU,EAAE,gBAAgB;gBAC5B,IAAI,EAAE,gBAAgB;gBACtB,OAAO,EAAE,OAAO;aACjB,CAAC;YACF,UAAU,EAAE,6CAAc,CAAC,GAAG,EAAE;YAChC,iBAAiB,EAAE,6CAAc,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YACvE,OAAO,EAAE;gBACP,YAAY,EAAE,mBAAmB;gBACjC,SAAS,EAAE,yBAAyB;gBACpC,UAAU,EAAE,IAAI;gBAChB,IAAI,EAAE,QAAQ;aACf;YACD,KAAK,EAAE,8BAA8B;YACrC,QAAQ,EAAE,IAAI;YACd,QAAQ,EAAE,MAAM;SACjB,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAEnE,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,gBAAgB,CAAC,CAAC;YACzD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YACzD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACpC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAEnE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACvD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,aAAa,GAAG,qBAAqB,CAAC;YAC5C,MAAM,cAAc,GAAG,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC;YAEtD,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,SAAS,EAAE;gBACjE,aAAa;gBACb,QAAQ,EAAE,cAAc;aACzB,CAAC,CAAC;YAEH,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACnE,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,iBAAiB,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,UAAmB,EAAE,CAAC;YAC1E,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;YAE3E,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,eAAe,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,QAAiB,EAAE,CAAC;YACtE,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;YAEzE,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3C,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACnE,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,iBAAiB,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,mCAAa,CAAC,QAAQ,EAAE,CAAC;YAC7E,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;YAE3E,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,eAAe,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,mCAAa,CAAC,MAAM,EAAE,CAAC;YACzE,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;YAEzE,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3C,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,cAAc,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC,CAAC,MAAM;YAC1E,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;YAExE,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACnE,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,cAAc,GAAG;gBACrB,GAAG,SAAS;gBACZ,iBAAiB,EAAE,6CAAc,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,iBAAiB;aACpG,CAAC;YACF,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;YAExE,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACnE,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACnE,MAAM,OAAO,GAAG,KAAK,CAAC,oBAAoB,EAAE,CAAC;YAE7C,MAAM,CAAC,OAAO,CAAC,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,qBAAqB;YACnE,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,uBAAuB;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACnE,MAAM,cAAc,GAAG,KAAK,CAAC,2BAA2B,EAAE,CAAC;YAE3D,MAAM,CAAC,cAAc,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YACjD,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,iBAAiB,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,UAAmB,EAAE,CAAC;YAC1E,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;YAE3E,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACnE,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,eAAe,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,QAAiB,EAAE,CAAC;YACtE,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;YAEzE,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,iBAAiB,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,UAAmB,EAAE,CAAC;YAC1E,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;YAE3E,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACnE,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,qBAAqB,GAAG;gBAC5B,GAAG,SAAS;gBACZ,QAAQ,EAAE,QAAiB;gBAC3B,QAAQ,EAAE,mCAAa,CAAC,IAAI;aAC7B,CAAC;YACF,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,qBAAqB,CAAC,CAAC;YAE/E,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,eAAe,GAAG;gBACtB,GAAG,SAAS;gBACZ,QAAQ,EAAE,QAAiB;gBAC3B,QAAQ,EAAE,mCAAa,CAAC,MAAM;aAC/B,CAAC;YACF,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;YAEzE,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,iBAAiB,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,UAAmB,EAAE,CAAC;YAC1E,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;YAC3E,MAAM,SAAS,GAAG,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAEjD,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,cAAc,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC,CAAC,MAAM;YAC1E,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;YACxE,MAAM,SAAS,GAAG,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAEjD,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACnE,MAAM,OAAO,GAAG,KAAK,CAAC,oBAAoB,EAAE,CAAC;YAE7C,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,mBAAmB,GAAG;gBAC1B,GAAG,SAAS;gBACZ,OAAO,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;aAC9B,CAAC;YACF,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC;YAC7E,MAAM,OAAO,GAAG,KAAK,CAAC,oBAAoB,EAAE,CAAC;YAE7C,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACnE,MAAM,KAAK,GAAG,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAE7C,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;YACpD,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;YAClD,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACnE,MAAM,KAAK,GAAG,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAE7C,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,cAAc,GAAG;gBACrB,GAAG,SAAS;gBACZ,iBAAiB,EAAE,6CAAc,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;aAClF,CAAC;YACF,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;YACxE,MAAM,KAAK,GAAG,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAE7C,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,cAAc,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC;YACnE,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;YACxE,MAAM,KAAK,GAAG,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAE7C,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACnE,MAAM,gBAAgB,GAAG,KAAK,CAAC,kBAAkB,EAAE,CAAC;YAEpD,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACzD,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7C,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;YACnE,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,gBAAgB,CAAC,CAAC;YAC1E,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,IAAI,CAAC,CAAC;YACtE,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC/D,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACnE,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;YAE5B,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACnE,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;YAC5B,MAAM,iBAAiB,GAAG,sDAAwB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAElE,MAAM,CAAC,iBAAiB,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3E,MAAM,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACxD,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,kEAAkE,EAAE,GAAG,EAAE;YAC1E,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACnE,MAAM,WAAW,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;YAE3C,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAC/C,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;YAClD,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAC/C,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,cAAc,GAAG;gBACrB,GAAG,SAAS;gBACZ,iBAAiB,EAAE,6CAAc,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;aAClF,CAAC;YACF,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;YACxE,MAAM,WAAW,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;YAE3C,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,cAAc,GAAG,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC;YACnE,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;YACxE,MAAM,WAAW,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;YAE3C,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACnE,MAAM,OAAO,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;YAEnC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,mBAAmB,GAAG,EAAE,GAAG,SAAS,EAAE,CAAC;YAC7C,OAAQ,mBAA2B,CAAC,QAAQ,CAAC;YAE7C,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC;YAE7E,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;YACvC,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3C,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,yBAAyB;QAC5F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,eAAe,GAAG,EAAE,GAAG,SAAS,EAAE,CAAC;YACzC,OAAQ,eAAuB,CAAC,QAAQ,CAAC;YAEzC,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;YAEzE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;YACvC,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,kBAAkB,GAAG,EAAE,GAAG,SAAS,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;YACzD,MAAM,KAAK,GAAG,IAAI,sDAAwB,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;YAC5E,MAAM,OAAO,GAAG,KAAK,CAAC,oBAAoB,EAAE,CAAC;YAE7C,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\__tests__\\event-received.domain-event.spec.ts"], "sourcesContent": ["import { EventReceivedDomainEvent, EventReceivedEventData } from '../event-received.domain-event';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel';\r\nimport { EventType } from '../../enums/event-type.enum';\r\nimport { EventSeverity } from '../../enums/event-severity.enum';\r\nimport { EventSource } from '../../value-objects/event-metadata/event-source.value-object';\r\nimport { EventTimestamp } from '../../value-objects/event-metadata/event-timestamp.value-object';\r\n\r\ndescribe('EventReceivedDomainEvent', () => {\r\n  let aggregateId: UniqueEntityId;\r\n  let eventData: EventReceivedEventData;\r\n\r\n  beforeEach(() => {\r\n    aggregateId = UniqueEntityId.generate();\r\n    eventData = {\r\n      eventType: EventType.MALWARE_DETECTED,\r\n      severity: EventSeverity.HIGH,\r\n      source: EventSource.create({\r\n        type: 'endpoint_detection',\r\n        identifier: 'edr-sensor-001',\r\n        name: 'EDR Sensor 001',\r\n        version: '2.1.0'\r\n      }),\r\n      receivedAt: EventTimestamp.now(),\r\n      originalTimestamp: EventTimestamp.fromDate(new Date(Date.now() - 5000)),\r\n      rawData: {\r\n        malware_name: 'Trojan.Win32.Test',\r\n        file_path: 'C:\\\\temp\\\\malicious.exe',\r\n        process_id: 1234,\r\n        user: 'SYSTEM'\r\n      },\r\n      title: 'Malware detected on endpoint',\r\n      dataSize: 2048,\r\n      priority: 'high'\r\n    };\r\n  });\r\n\r\n  describe('constructor', () => {\r\n    it('should create event with valid data', () => {\r\n      const event = new EventReceivedDomainEvent(aggregateId, eventData);\r\n\r\n      expect(event.aggregateId).toEqual(aggregateId);\r\n      expect(event.eventType).toBe(EventType.MALWARE_DETECTED);\r\n      expect(event.severity).toBe(EventSeverity.HIGH);\r\n      expect(event.title).toBe('Malware detected on endpoint');\r\n      expect(event.priority).toBe('high');\r\n      expect(event.dataSize).toBe(2048);\r\n    });\r\n\r\n    it('should set correct metadata', () => {\r\n      const event = new EventReceivedDomainEvent(aggregateId, eventData);\r\n\r\n      expect(event.metadata.eventType).toBe('EventReceived');\r\n      expect(event.metadata.domain).toBe('Security');\r\n      expect(event.metadata.aggregateType).toBe('Event');\r\n      expect(event.metadata.processingStage).toBe('intake');\r\n    });\r\n\r\n    it('should accept custom options', () => {\r\n      const correlationId = 'test-correlation-id';\r\n      const customMetadata = { customField: 'customValue' };\r\n\r\n      const event = new EventReceivedDomainEvent(aggregateId, eventData, {\r\n        correlationId,\r\n        metadata: customMetadata\r\n      });\r\n\r\n      expect(event.correlationId).toBe(correlationId);\r\n      expect(event.metadata.customField).toBe('customValue');\r\n    });\r\n  });\r\n\r\n  describe('priority checks', () => {\r\n    it('should identify high priority events', () => {\r\n      const event = new EventReceivedDomainEvent(aggregateId, eventData);\r\n      expect(event.isHighPriority()).toBe(true);\r\n    });\r\n\r\n    it('should identify critical priority events', () => {\r\n      const criticalEventData = { ...eventData, priority: 'critical' as const };\r\n      const event = new EventReceivedDomainEvent(aggregateId, criticalEventData);\r\n      \r\n      expect(event.isCriticalPriority()).toBe(true);\r\n      expect(event.isHighPriority()).toBe(true);\r\n    });\r\n\r\n    it('should identify normal priority events', () => {\r\n      const normalEventData = { ...eventData, priority: 'normal' as const };\r\n      const event = new EventReceivedDomainEvent(aggregateId, normalEventData);\r\n      \r\n      expect(event.isHighPriority()).toBe(false);\r\n      expect(event.isCriticalPriority()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('severity checks', () => {\r\n    it('should identify high severity events', () => {\r\n      const event = new EventReceivedDomainEvent(aggregateId, eventData);\r\n      expect(event.isHighSeverity()).toBe(true);\r\n    });\r\n\r\n    it('should identify critical severity events', () => {\r\n      const criticalEventData = { ...eventData, severity: EventSeverity.CRITICAL };\r\n      const event = new EventReceivedDomainEvent(aggregateId, criticalEventData);\r\n      \r\n      expect(event.isCriticalSeverity()).toBe(true);\r\n      expect(event.isHighSeverity()).toBe(true);\r\n    });\r\n\r\n    it('should identify medium severity events', () => {\r\n      const mediumEventData = { ...eventData, severity: EventSeverity.MEDIUM };\r\n      const event = new EventReceivedDomainEvent(aggregateId, mediumEventData);\r\n      \r\n      expect(event.isHighSeverity()).toBe(false);\r\n      expect(event.isCriticalSeverity()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('data size checks', () => {\r\n    it('should identify large events', () => {\r\n      const largeEventData = { ...eventData, dataSize: 2 * 1024 * 1024 }; // 2MB\r\n      const event = new EventReceivedDomainEvent(aggregateId, largeEventData);\r\n      \r\n      expect(event.isLargeEvent()).toBe(true);\r\n    });\r\n\r\n    it('should identify normal size events', () => {\r\n      const event = new EventReceivedDomainEvent(aggregateId, eventData);\r\n      expect(event.isLargeEvent()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('staleness checks', () => {\r\n    it('should identify stale events', () => {\r\n      const staleEventData = {\r\n        ...eventData,\r\n        originalTimestamp: EventTimestamp.fromDate(new Date(Date.now() - 10 * 60 * 1000)) // 10 minutes ago\r\n      };\r\n      const event = new EventReceivedDomainEvent(aggregateId, staleEventData);\r\n      \r\n      expect(event.isStaleEvent()).toBe(true);\r\n    });\r\n\r\n    it('should identify fresh events', () => {\r\n      const event = new EventReceivedDomainEvent(aggregateId, eventData);\r\n      expect(event.isStaleEvent()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('processing latency', () => {\r\n    it('should calculate processing latency correctly', () => {\r\n      const event = new EventReceivedDomainEvent(aggregateId, eventData);\r\n      const latency = event.getProcessingLatency();\r\n      \r\n      expect(latency).toBeGreaterThanOrEqual(5000); // At least 5 seconds\r\n      expect(latency).toBeLessThan(10000); // Less than 10 seconds\r\n    });\r\n\r\n    it('should calculate processing latency in seconds', () => {\r\n      const event = new EventReceivedDomainEvent(aggregateId, eventData);\r\n      const latencySeconds = event.getProcessingLatencySeconds();\r\n      \r\n      expect(latencySeconds).toBeGreaterThanOrEqual(5);\r\n      expect(latencySeconds).toBeLessThan(10);\r\n    });\r\n  });\r\n\r\n  describe('timeout recommendations', () => {\r\n    it('should recommend short timeout for critical priority', () => {\r\n      const criticalEventData = { ...eventData, priority: 'critical' as const };\r\n      const event = new EventReceivedDomainEvent(aggregateId, criticalEventData);\r\n      \r\n      expect(event.getRecommendedTimeout()).toBe(1);\r\n    });\r\n\r\n    it('should recommend medium timeout for high priority', () => {\r\n      const event = new EventReceivedDomainEvent(aggregateId, eventData);\r\n      expect(event.getRecommendedTimeout()).toBe(5);\r\n    });\r\n\r\n    it('should recommend long timeout for normal priority', () => {\r\n      const normalEventData = { ...eventData, priority: 'normal' as const };\r\n      const event = new EventReceivedDomainEvent(aggregateId, normalEventData);\r\n      \r\n      expect(event.getRecommendedTimeout()).toBe(30);\r\n    });\r\n  });\r\n\r\n  describe('processing queue assignment', () => {\r\n    it('should assign critical events to critical queue', () => {\r\n      const criticalEventData = { ...eventData, priority: 'critical' as const };\r\n      const event = new EventReceivedDomainEvent(aggregateId, criticalEventData);\r\n      \r\n      expect(event.getProcessingQueue()).toBe('critical-events');\r\n    });\r\n\r\n    it('should assign high priority events to high priority queue', () => {\r\n      const event = new EventReceivedDomainEvent(aggregateId, eventData);\r\n      expect(event.getProcessingQueue()).toBe('high-priority-events');\r\n    });\r\n\r\n    it('should assign high severity events to high severity queue', () => {\r\n      const highSeverityEventData = { \r\n        ...eventData, \r\n        priority: 'normal' as const,\r\n        severity: EventSeverity.HIGH \r\n      };\r\n      const event = new EventReceivedDomainEvent(aggregateId, highSeverityEventData);\r\n      \r\n      expect(event.getProcessingQueue()).toBe('high-severity-events');\r\n    });\r\n\r\n    it('should assign normal events to standard queue', () => {\r\n      const normalEventData = { \r\n        ...eventData, \r\n        priority: 'normal' as const,\r\n        severity: EventSeverity.MEDIUM \r\n      };\r\n      const event = new EventReceivedDomainEvent(aggregateId, normalEventData);\r\n      \r\n      expect(event.getProcessingQueue()).toBe('standard-events');\r\n    });\r\n  });\r\n\r\n  describe('resource requirements', () => {\r\n    it('should require high resources for critical priority events', () => {\r\n      const criticalEventData = { ...eventData, priority: 'critical' as const };\r\n      const event = new EventReceivedDomainEvent(aggregateId, criticalEventData);\r\n      const resources = event.getProcessingResources();\r\n      \r\n      expect(resources.cpu).toBe('high');\r\n    });\r\n\r\n    it('should require high memory for large events', () => {\r\n      const largeEventData = { ...eventData, dataSize: 2 * 1024 * 1024 }; // 2MB\r\n      const event = new EventReceivedDomainEvent(aggregateId, largeEventData);\r\n      const resources = event.getProcessingResources();\r\n      \r\n      expect(resources.memory).toBe('high');\r\n      expect(resources.storage).toBe('high');\r\n    });\r\n  });\r\n\r\n  describe('quality indicators', () => {\r\n    it('should assess good quality for complete events', () => {\r\n      const event = new EventReceivedDomainEvent(aggregateId, eventData);\r\n      const quality = event.getQualityIndicators();\r\n      \r\n      expect(quality.hasRequiredFields).toBe(true);\r\n      expect(quality.hasValidTimestamp).toBe(true);\r\n      expect(quality.hasValidSource).toBe(true);\r\n      expect(quality.dataIntegrity).toBe('good');\r\n      expect(quality.completeness).toBeGreaterThan(70);\r\n    });\r\n\r\n    it('should assess poor quality for incomplete events', () => {\r\n      const incompleteEventData = {\r\n        ...eventData,\r\n        rawData: { incomplete: null }\r\n      };\r\n      const event = new EventReceivedDomainEvent(aggregateId, incompleteEventData);\r\n      const quality = event.getQualityIndicators();\r\n      \r\n      expect(quality.completeness).toBeLessThan(70);\r\n    });\r\n  });\r\n\r\n  describe('next processing steps', () => {\r\n    it('should include validation for all events', () => {\r\n      const event = new EventReceivedDomainEvent(aggregateId, eventData);\r\n      const steps = event.getNextProcessingSteps();\r\n      \r\n      expect(steps).toContain('validate_event_structure');\r\n      expect(steps).toContain('normalize_event_format');\r\n      expect(steps).toContain('route_to_processing_pipeline');\r\n    });\r\n\r\n    it('should include priority assessment for high severity events', () => {\r\n      const event = new EventReceivedDomainEvent(aggregateId, eventData);\r\n      const steps = event.getNextProcessingSteps();\r\n      \r\n      expect(steps).toContain('priority_threat_assessment');\r\n    });\r\n\r\n    it('should include stale event handling for old events', () => {\r\n      const staleEventData = {\r\n        ...eventData,\r\n        originalTimestamp: EventTimestamp.fromDate(new Date(Date.now() - 10 * 60 * 1000))\r\n      };\r\n      const event = new EventReceivedDomainEvent(aggregateId, staleEventData);\r\n      const steps = event.getNextProcessingSteps();\r\n      \r\n      expect(steps).toContain('stale_event_handling');\r\n    });\r\n\r\n    it('should include large event processing for big events', () => {\r\n      const largeEventData = { ...eventData, dataSize: 2 * 1024 * 1024 };\r\n      const event = new EventReceivedDomainEvent(aggregateId, largeEventData);\r\n      const steps = event.getNextProcessingSteps();\r\n      \r\n      expect(steps).toContain('large_event_processing');\r\n    });\r\n  });\r\n\r\n  describe('integration event conversion', () => {\r\n    it('should convert to integration event format', () => {\r\n      const event = new EventReceivedDomainEvent(aggregateId, eventData);\r\n      const integrationEvent = event.toIntegrationEvent();\r\n      \r\n      expect(integrationEvent.eventType).toBe('EventReceived');\r\n      expect(integrationEvent.version).toBe('1.0');\r\n      expect(integrationEvent.data.eventId).toBe(aggregateId.toString());\r\n      expect(integrationEvent.data.event.type).toBe(EventType.MALWARE_DETECTED);\r\n      expect(integrationEvent.data.event.severity).toBe(EventSeverity.HIGH);\r\n      expect(integrationEvent.data.processing.priority).toBe('high');\r\n      expect(integrationEvent.data.processing.queue).toBe('high-priority-events');\r\n    });\r\n  });\r\n\r\n  describe('JSON serialization', () => {\r\n    it('should serialize to JSON correctly', () => {\r\n      const event = new EventReceivedDomainEvent(aggregateId, eventData);\r\n      const json = event.toJSON();\r\n      \r\n      expect(json.eventData).toEqual(eventData);\r\n      expect(json.analysis.isHighPriority).toBe(true);\r\n      expect(json.analysis.isHighSeverity).toBe(true);\r\n      expect(json.analysis.processingQueue).toBe('high-priority-events');\r\n    });\r\n\r\n    it('should deserialize from JSON correctly', () => {\r\n      const event = new EventReceivedDomainEvent(aggregateId, eventData);\r\n      const json = event.toJSON();\r\n      const deserializedEvent = EventReceivedDomainEvent.fromJSON(json);\r\n      \r\n      expect(deserializedEvent.aggregateId.equals(event.aggregateId)).toBe(true);\r\n      expect(deserializedEvent.eventType).toBe(event.eventType);\r\n      expect(deserializedEvent.severity).toBe(event.severity);\r\n      expect(deserializedEvent.title).toBe(event.title);\r\n    });\r\n  });\r\n\r\n  describe('human-readable description', () => {\r\n    it('should generate appropriate description for high priority events', () => {\r\n      const event = new EventReceivedDomainEvent(aggregateId, eventData);\r\n      const description = event.getDescription();\r\n      \r\n      expect(description).toContain('high severity');\r\n      expect(description).toContain('malware_detected');\r\n      expect(description).toContain('high priority');\r\n      expect(description).toContain('edr-sensor-001');\r\n    });\r\n\r\n    it('should include stale indicator for old events', () => {\r\n      const staleEventData = {\r\n        ...eventData,\r\n        originalTimestamp: EventTimestamp.fromDate(new Date(Date.now() - 10 * 60 * 1000))\r\n      };\r\n      const event = new EventReceivedDomainEvent(aggregateId, staleEventData);\r\n      const description = event.getDescription();\r\n      \r\n      expect(description).toContain('[STALE]');\r\n    });\r\n\r\n    it('should include large indicator for big events', () => {\r\n      const largeEventData = { ...eventData, dataSize: 2 * 1024 * 1024 };\r\n      const event = new EventReceivedDomainEvent(aggregateId, largeEventData);\r\n      const description = event.getDescription();\r\n      \r\n      expect(description).toContain('[LARGE]');\r\n    });\r\n  });\r\n\r\n  describe('event summary', () => {\r\n    it('should generate comprehensive summary', () => {\r\n      const event = new EventReceivedDomainEvent(aggregateId, eventData);\r\n      const summary = event.getSummary();\r\n      \r\n      expect(summary.eventType).toBe('EventReceived');\r\n      expect(summary.eventId).toBe(aggregateId.toString());\r\n      expect(summary.severity).toBe(EventSeverity.HIGH);\r\n      expect(summary.source).toBe('edr-sensor-001');\r\n      expect(summary.priority).toBe('high');\r\n      expect(summary.processingLatency).toBeGreaterThan(0);\r\n      expect(summary.qualityScore).toBeGreaterThan(0);\r\n    });\r\n  });\r\n\r\n  describe('edge cases', () => {\r\n    it('should handle events without priority', () => {\r\n      const noPriorityEventData = { ...eventData };\r\n      delete (noPriorityEventData as any).priority;\r\n      \r\n      const event = new EventReceivedDomainEvent(aggregateId, noPriorityEventData);\r\n      \r\n      expect(event.priority).toBeUndefined();\r\n      expect(event.isHighPriority()).toBe(false);\r\n      expect(event.getProcessingQueue()).toBe('high-severity-events'); // Falls back to severity\r\n    });\r\n\r\n    it('should handle events without data size', () => {\r\n      const noSizeEventData = { ...eventData };\r\n      delete (noSizeEventData as any).dataSize;\r\n      \r\n      const event = new EventReceivedDomainEvent(aggregateId, noSizeEventData);\r\n      \r\n      expect(event.dataSize).toBeUndefined();\r\n      expect(event.isLargeEvent()).toBe(false);\r\n    });\r\n\r\n    it('should handle events with empty raw data', () => {\r\n      const emptyDataEventData = { ...eventData, rawData: {} };\r\n      const event = new EventReceivedDomainEvent(aggregateId, emptyDataEventData);\r\n      const quality = event.getQualityIndicators();\r\n      \r\n      expect(quality.completeness).toBe(0);\r\n      expect(quality.dataIntegrity).toBe('poor');\r\n    });\r\n  });\r\n});"], "version": 3}