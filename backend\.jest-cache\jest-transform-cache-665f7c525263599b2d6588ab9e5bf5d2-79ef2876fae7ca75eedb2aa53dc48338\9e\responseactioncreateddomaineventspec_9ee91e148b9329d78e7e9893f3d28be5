e83e63435fe03134877d8a432d1753b1
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const response_action_created_domain_event_1 = require("../response-action-created.domain-event");
const shared_kernel_1 = require("../../../../../shared-kernel");
const action_type_enum_1 = require("../../enums/action-type.enum");
const action_status_enum_1 = require("../../enums/action-status.enum");
describe('ResponseActionCreatedDomainEvent', () => {
    let aggregateId;
    let eventData;
    let event;
    beforeEach(() => {
        aggregateId = shared_kernel_1.UniqueEntityId.generate();
        eventData = {
            actionType: action_type_enum_1.ActionType.BLOCK_IP,
            status: action_status_enum_1.ActionStatus.PENDING,
            title: 'Block malicious IP address',
            priority: 'high',
            isAutomated: true,
            approvalRequired: false,
            target: {
                type: 'system',
                id: 'firewall-001',
                name: 'Main Firewall',
            },
            relatedEventId: shared_kernel_1.UniqueEntityId.generate().toString(),
            relatedThreatId: shared_kernel_1.UniqueEntityId.generate().toString(),
        };
        event = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, eventData);
    });
    describe('creation', () => {
        it('should create event with required data', () => {
            expect(event).toBeInstanceOf(response_action_created_domain_event_1.ResponseActionCreatedDomainEvent);
            expect(event.aggregateId).toBe(aggregateId);
            expect(event.eventData).toBe(eventData);
            expect(event.occurredOn).toBeInstanceOf(Date);
        });
        it('should create event with custom options', () => {
            const customOptions = {
                eventId: shared_kernel_1.UniqueEntityId.generate(),
                occurredOn: new Date('2023-01-01'),
                eventVersion: 2,
                correlationId: 'corr-123',
                causationId: 'cause-456',
                metadata: { source: 'test' },
            };
            const customEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, eventData, customOptions);
            expect(customEvent.eventId).toBe(customOptions.eventId);
            expect(customEvent.occurredOn).toBe(customOptions.occurredOn);
            expect(customEvent.eventVersion).toBe(customOptions.eventVersion);
            expect(customEvent.correlationId).toBe(customOptions.correlationId);
            expect(customEvent.causationId).toBe(customOptions.causationId);
            expect(customEvent.metadata).toEqual(customOptions.metadata);
        });
    });
    describe('getters', () => {
        it('should provide access to event data properties', () => {
            expect(event.actionType).toBe(action_type_enum_1.ActionType.BLOCK_IP);
            expect(event.status).toBe(action_status_enum_1.ActionStatus.PENDING);
            expect(event.title).toBe('Block malicious IP address');
            expect(event.priority).toBe('high');
            expect(event.isAutomated).toBe(true);
            expect(event.approvalRequired).toBe(false);
            expect(event.target).toEqual(eventData.target);
            expect(event.relatedEventId).toBe(eventData.relatedEventId);
            expect(event.relatedThreatId).toBe(eventData.relatedThreatId);
            expect(event.relatedVulnerabilityId).toBeUndefined();
        });
    });
    describe('classification methods', () => {
        it('should identify high priority actions', () => {
            expect(event.isHighPriority()).toBe(true);
            const normalEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                priority: 'normal',
            });
            expect(normalEvent.isHighPriority()).toBe(false);
        });
        it('should identify critical actions', () => {
            const criticalEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                priority: 'critical',
            });
            expect(criticalEvent.isCritical()).toBe(true);
            expect(event.isCritical()).toBe(false);
        });
        it('should identify security actions', () => {
            expect(event.isSecurityAction()).toBe(true);
            const notificationEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.SEND_EMAIL,
            });
            expect(notificationEvent.isSecurityAction()).toBe(false);
        });
        it('should identify containment actions', () => {
            expect(event.isContainmentAction()).toBe(true);
            const isolateEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.ISOLATE_SYSTEM,
            });
            expect(isolateEvent.isContainmentAction()).toBe(true);
            const emailEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.SEND_EMAIL,
            });
            expect(emailEvent.isContainmentAction()).toBe(false);
        });
        it('should identify notification actions', () => {
            const emailEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.SEND_EMAIL,
            });
            expect(emailEvent.isNotificationAction()).toBe(true);
            expect(event.isNotificationAction()).toBe(false);
        });
    });
    describe('target and relationship methods', () => {
        it('should check target entity type', () => {
            expect(event.targetsEntityType('system')).toBe(true);
            expect(event.targetsEntityType('user')).toBe(false);
        });
        it('should check related entities', () => {
            expect(event.isEventRelated()).toBe(true);
            expect(event.isThreatRelated()).toBe(true);
            expect(event.isVulnerabilityRelated()).toBe(false);
            const unrelatedEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                relatedEventId: undefined,
                relatedThreatId: undefined,
            });
            expect(unrelatedEvent.isEventRelated()).toBe(false);
            expect(unrelatedEvent.isThreatRelated()).toBe(false);
        });
    });
    describe('recommendation methods', () => {
        it('should provide next action recommendations for approval required actions', () => {
            const approvalEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                approvalRequired: true,
                isAutomated: false,
            });
            const recommendations = approvalEvent.getRecommendedNextActions();
            expect(recommendations).toContain('Request approval from appropriate authority');
            expect(recommendations).toContain('Notify approval workflow system');
        });
        it('should provide next action recommendations for automated actions', () => {
            const automatedEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                isAutomated: true,
                approvalRequired: false,
            });
            const recommendations = automatedEvent.getRecommendedNextActions();
            expect(recommendations).toContain('Queue action for automated execution');
            expect(recommendations).toContain('Validate execution prerequisites');
        });
        it('should provide next action recommendations for high priority actions', () => {
            const recommendations = event.getRecommendedNextActions();
            expect(recommendations).toContain('Prioritize in execution queue');
            expect(recommendations).toContain('Notify high-priority action handlers');
        });
        it('should provide next action recommendations for critical actions', () => {
            const criticalEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                priority: 'critical',
            });
            const recommendations = criticalEvent.getRecommendedNextActions();
            expect(recommendations).toContain('Escalate to incident response team');
            expect(recommendations).toContain('Activate emergency response procedures');
        });
        it('should provide next action recommendations for containment actions', () => {
            const recommendations = event.getRecommendedNextActions();
            expect(recommendations).toContain('Prepare containment procedures');
            expect(recommendations).toContain('Validate containment scope and impact');
        });
        it('should provide next action recommendations for notification actions', () => {
            const notificationEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.SEND_EMAIL,
            });
            const recommendations = notificationEvent.getRecommendedNextActions();
            expect(recommendations).toContain('Prepare notification templates');
            expect(recommendations).toContain('Validate recipient lists');
        });
    });
    describe('notification targets', () => {
        it('should identify notification targets for approval required actions', () => {
            const approvalEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                approvalRequired: true,
            });
            const targets = approvalEvent.getNotificationTargets();
            expect(targets).toContain('approval-managers');
        });
        it('should identify notification targets for high priority actions', () => {
            const targets = event.getNotificationTargets();
            expect(targets).toContain('security-team');
            expect(targets).toContain('incident-response-team');
        });
        it('should identify notification targets for critical actions', () => {
            const criticalEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                priority: 'critical',
            });
            const targets = criticalEvent.getNotificationTargets();
            expect(targets).toContain('security-managers');
            expect(targets).toContain('on-call-engineers');
        });
        it('should identify notification targets for security actions', () => {
            const targets = event.getNotificationTargets();
            expect(targets).toContain('security-analysts');
        });
        it('should identify notification targets for containment actions', () => {
            const targets = event.getNotificationTargets();
            expect(targets).toContain('containment-specialists');
        });
    });
    describe('categorization methods', () => {
        it('should categorize containment actions', () => {
            expect(event.getActionCategory()).toBe('Containment');
        });
        it('should categorize notification actions', () => {
            const emailEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.SEND_EMAIL,
            });
            expect(emailEvent.getActionCategory()).toBe('Notification');
        });
        it('should categorize detection actions', () => {
            const scanEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.VULNERABILITY_SCAN,
            });
            expect(scanEvent.getActionCategory()).toBe('Detection');
        });
        it('should categorize eradication actions', () => {
            const malwareEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.REMOVE_MALWARE,
            });
            expect(malwareEvent.getActionCategory()).toBe('Eradication');
        });
        it('should categorize recovery actions', () => {
            const backupEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.RESTORE_BACKUP,
            });
            expect(backupEvent.getActionCategory()).toBe('Recovery');
        });
        it('should categorize other actions', () => {
            const customEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.EXECUTE_SCRIPT,
            });
            expect(customEvent.getActionCategory()).toBe('Other');
        });
    });
    describe('urgency and timing methods', () => {
        it('should determine execution urgency for critical actions', () => {
            const criticalEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                priority: 'critical',
            });
            expect(criticalEvent.getExecutionUrgency()).toBe('immediate');
        });
        it('should determine execution urgency for high priority actions', () => {
            expect(event.getExecutionUrgency()).toBe('urgent');
        });
        it('should determine execution urgency for containment actions', () => {
            const isolateEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.ISOLATE_SYSTEM,
                priority: 'normal',
            });
            expect(isolateEvent.getExecutionUrgency()).toBe('urgent');
        });
        it('should determine execution urgency for normal actions', () => {
            const normalEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                priority: 'normal',
                actionType: action_type_enum_1.ActionType.SEND_EMAIL,
            });
            expect(normalEvent.getExecutionUrgency()).toBe('normal');
        });
        it('should determine execution urgency for low priority actions', () => {
            const lowEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                priority: 'low',
                actionType: action_type_enum_1.ActionType.SEND_EMAIL,
            });
            expect(lowEvent.getExecutionUrgency()).toBe('low');
        });
        it('should estimate execution time for different action types', () => {
            expect(event.getEstimatedExecutionTime()).toBe(1); // BLOCK_IP
            const scanEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.VULNERABILITY_SCAN,
            });
            expect(scanEvent.getEstimatedExecutionTime()).toBe(15);
            const unknownEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.EXECUTE_SCRIPT,
            });
            expect(unknownEvent.getEstimatedExecutionTime()).toBe(30); // Default
        });
    });
    describe('risk assessment methods', () => {
        it('should assess risk level for high-risk actions', () => {
            const shutdownEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.SHUTDOWN_SYSTEM,
            });
            expect(shutdownEvent.getRiskLevel()).toBe('high');
        });
        it('should assess risk level for medium-risk actions', () => {
            const patchEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.PATCH_VULNERABILITY,
            });
            expect(patchEvent.getRiskLevel()).toBe('medium');
        });
        it('should assess risk level for low-risk actions', () => {
            expect(event.getRiskLevel()).toBe('low');
        });
    });
    describe('compliance methods', () => {
        it('should provide compliance considerations for containment actions', () => {
            const considerations = event.getComplianceConsiderations();
            expect(considerations).toContain('Document containment rationale for compliance');
            expect(considerations).toContain('Ensure containment actions are proportionate');
        });
        it('should provide compliance considerations for file deletion', () => {
            const deleteEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.DELETE_FILE,
            });
            const considerations = deleteEvent.getComplianceConsiderations();
            expect(considerations).toContain('Ensure data retention policies are followed');
            expect(considerations).toContain('Create forensic copies before deletion');
        });
        it('should provide compliance considerations for account disabling', () => {
            const disableEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.DISABLE_ACCOUNT,
            });
            const considerations = disableEvent.getComplianceConsiderations();
            expect(considerations).toContain('Follow user access management procedures');
            expect(considerations).toContain('Document account disabling rationale');
        });
        it('should provide compliance considerations for high-risk actions', () => {
            const shutdownEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.SHUTDOWN_SYSTEM,
            });
            const considerations = shutdownEvent.getComplianceConsiderations();
            expect(considerations).toContain('Obtain appropriate approvals for high-risk actions');
            expect(considerations).toContain('Document risk assessment and mitigation');
        });
    });
    describe('integration event conversion', () => {
        it('should convert to integration event format', () => {
            const integrationEvent = event.toIntegrationEvent();
            expect(integrationEvent.eventType).toBe('ResponseActionCreated');
            expect(integrationEvent.action).toBe('response_action_created');
            expect(integrationEvent.resource).toBe('ResponseAction');
            expect(integrationEvent.resourceId).toBe(aggregateId.toString());
            expect(integrationEvent.data).toBe(eventData);
            expect(integrationEvent.metadata).toEqual({
                priority: 'high',
                category: 'Containment',
                urgency: 'urgent',
                riskLevel: 'low',
                estimatedExecutionTime: 1,
                isAutomated: true,
                approvalRequired: false,
            });
        });
    });
    describe('edge cases', () => {
        it('should handle events without target', () => {
            const noTargetEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                target: undefined,
            });
            expect(noTargetEvent.target).toBeUndefined();
            expect(noTargetEvent.targetsEntityType('system')).toBe(false);
        });
        it('should handle events without related entities', () => {
            const unrelatedEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                relatedEventId: undefined,
                relatedThreatId: undefined,
                relatedVulnerabilityId: undefined,
            });
            expect(unrelatedEvent.isEventRelated()).toBe(false);
            expect(unrelatedEvent.isThreatRelated()).toBe(false);
            expect(unrelatedEvent.isVulnerabilityRelated()).toBe(false);
        });
        it('should handle unknown action types gracefully', () => {
            const unknownEvent = new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(aggregateId, {
                ...eventData,
                actionType: 'UNKNOWN_ACTION',
            });
            expect(unknownEvent.getActionCategory()).toBe('Other');
            expect(unknownEvent.getEstimatedExecutionTime()).toBe(30);
            expect(unknownEvent.getRiskLevel()).toBe('low');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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