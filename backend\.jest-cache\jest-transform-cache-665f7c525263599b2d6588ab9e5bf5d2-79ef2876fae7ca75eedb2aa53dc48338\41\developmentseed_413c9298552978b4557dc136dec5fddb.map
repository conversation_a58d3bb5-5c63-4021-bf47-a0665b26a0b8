{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\database\\seeds\\development.seed.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,2CAAwC;AACxC,+CAAiC;AAEjC;;;GAGG;AACH,MAAa,iBAAiB;IAG5B,YAA6B,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;QAFlC,WAAM,GAAG,IAAI,eAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAEP,CAAC;IAEvD;;OAEG;IACH,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;QAE5D,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;YACvB,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;YACvB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxB,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACjC,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAChC,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAEnC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE;gBACvD,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,SAAS;QACrB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;QAEtC,MAAM,KAAK,GAAG;YACZ;gBACE,IAAI,EAAE,OAAO;gBACb,WAAW,EAAE,uCAAuC;gBACpD,WAAW,EAAE;oBACX,YAAY,EAAE,aAAa,EAAE,cAAc;oBAC3C,YAAY,EAAE,aAAa,EAAE,cAAc;oBAC3C,sBAAsB,EAAE,uBAAuB,EAAE,wBAAwB;oBACzE,sBAAsB,EAAE,uBAAuB,EAAE,wBAAwB;oBACzE,aAAa,EAAE,cAAc,EAAE,eAAe;oBAC9C,YAAY,EAAE,aAAa,EAAE,cAAc;oBAC3C,cAAc,EAAE,eAAe;oBAC/B,cAAc;iBACf;gBACD,cAAc,EAAE,IAAI;aACrB;YACD;gBACE,IAAI,EAAE,kBAAkB;gBACxB,WAAW,EAAE,0DAA0D;gBACvE,WAAW,EAAE;oBACX,sBAAsB,EAAE,uBAAuB;oBAC/C,sBAAsB,EAAE,uBAAuB;oBAC/C,aAAa,EAAE,cAAc;oBAC7B,YAAY,EAAE,aAAa;oBAC3B,cAAc;iBACf;gBACD,cAAc,EAAE,IAAI;aACrB;YACD;gBACE,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,mCAAmC;gBAChD,WAAW,EAAE;oBACX,sBAAsB;oBACtB,sBAAsB;oBACtB,aAAa;oBACb,YAAY;oBACZ,cAAc;iBACf;gBACD,cAAc,EAAE,IAAI;aACrB;YACD;gBACE,IAAI,EAAE,oBAAoB;gBAC1B,WAAW,EAAE,+BAA+B;gBAC5C,WAAW,EAAE;oBACX,sBAAsB,EAAE,uBAAuB;oBAC/C,sBAAsB;oBACtB,aAAa;oBACb,cAAc;iBACf;gBACD,cAAc,EAAE,IAAI;aACrB;SACF,CAAC;QAEF,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;;;OAQ3B,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;QAC3F,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,KAAK,CAAC,MAAM,QAAQ,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,SAAS;QACrB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;QAEtC,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QAExD,MAAM,KAAK,GAAG;YACZ;gBACE,KAAK,EAAE,oBAAoB;gBAC3B,aAAa,EAAE,YAAY;gBAC3B,UAAU,EAAE,QAAQ;gBACpB,SAAS,EAAE,eAAe;gBAC1B,IAAI,EAAE,OAAO;gBACb,SAAS,EAAE,IAAI;gBACf,cAAc,EAAE,IAAI;aACrB;YACD;gBACE,KAAK,EAAE,sBAAsB;gBAC7B,aAAa,EAAE,YAAY;gBAC3B,UAAU,EAAE,UAAU;gBACtB,SAAS,EAAE,SAAS;gBACpB,IAAI,EAAE,kBAAkB;gBACxB,SAAS,EAAE,IAAI;gBACf,cAAc,EAAE,IAAI;aACrB;YACD;gBACE,KAAK,EAAE,qBAAqB;gBAC5B,aAAa,EAAE,YAAY;gBAC3B,UAAU,EAAE,MAAM;gBAClB,SAAS,EAAE,MAAM;gBACjB,IAAI,EAAE,QAAQ;gBACd,SAAS,EAAE,IAAI;gBACf,cAAc,EAAE,IAAI;aACrB;YACD;gBACE,KAAK,EAAE,wBAAwB;gBAC/B,aAAa,EAAE,YAAY;gBAC3B,UAAU,EAAE,UAAU;gBACtB,SAAS,EAAE,WAAW;gBACtB,IAAI,EAAE,oBAAoB;gBAC1B,SAAS,EAAE,IAAI;gBACf,cAAc,EAAE,IAAI;aACrB;SACF,CAAC;QAEF,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;;;;;;OAW3B,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;QACxH,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,KAAK,CAAC,MAAM,QAAQ,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,UAAU;QACtB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;QAEvC,MAAM,MAAM,GAAG;YACb;gBACE,IAAI,EAAE,eAAe;gBACrB,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE,cAAc;gBAC1B,QAAQ,EAAE,gBAAgB;gBAC1B,gBAAgB,EAAE,QAAQ;gBAC1B,UAAU,EAAE,WAAW;gBACvB,QAAQ,EAAE,eAAe;gBACzB,KAAK,EAAE,eAAe;gBACtB,WAAW,EAAE,MAAM;gBACnB,MAAM,EAAE,QAAQ;gBAChB,QAAQ,EAAE;oBACR,QAAQ,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC;oBACvC,WAAW,EAAE,YAAY;oBACzB,eAAe,EAAE,OAAO;iBACzB;aACF;YACD;gBACE,IAAI,EAAE,iBAAiB;gBACvB,IAAI,EAAE,UAAU;gBAChB,UAAU,EAAE,cAAc;gBAC1B,QAAQ,EAAE,eAAe;gBACzB,gBAAgB,EAAE,QAAQ;gBAC1B,UAAU,EAAE,KAAK;gBACjB,QAAQ,EAAE,eAAe;gBACzB,KAAK,EAAE,eAAe;gBACtB,WAAW,EAAE,UAAU;gBACvB,MAAM,EAAE,QAAQ;gBAChB,QAAQ,EAAE;oBACR,aAAa,EAAE,YAAY;oBAC3B,OAAO,EAAE,MAAM;oBACf,eAAe,EAAE,QAAQ;iBAC1B;aACF;YACD;gBACE,IAAI,EAAE,yBAAyB;gBAC/B,IAAI,EAAE,aAAa;gBACnB,UAAU,EAAE,cAAc;gBAC1B,QAAQ,EAAE,WAAW;gBACrB,gBAAgB,EAAE,SAAS;gBAC3B,UAAU,EAAE,QAAQ;gBACpB,QAAQ,EAAE,gBAAgB;gBAC1B,KAAK,EAAE,kBAAkB;gBACzB,WAAW,EAAE,QAAQ;gBACrB,MAAM,EAAE,QAAQ;gBAChB,QAAQ,EAAE;oBACR,IAAI,EAAE,WAAW;oBACjB,UAAU,EAAE,aAAa;iBAC1B;aACF;YACD;gBACE,IAAI,EAAE,aAAa;gBACnB,IAAI,EAAE,gBAAgB;gBACtB,UAAU,EAAE,aAAa;gBACzB,QAAQ,EAAE,gBAAgB;gBAC1B,gBAAgB,EAAE,WAAW;gBAC7B,UAAU,EAAE,MAAM;gBAClB,QAAQ,EAAE,gBAAgB;gBAC1B,KAAK,EAAE,cAAc;gBACrB,WAAW,EAAE,UAAU;gBACvB,MAAM,EAAE,QAAQ;gBAChB,QAAQ,EAAE;oBACR,WAAW,EAAE,QAAQ;oBACrB,KAAK,EAAE,gBAAgB;iBACxB;aACF;SACF,CAAC;QAEF,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;OAe3B,EAAE;gBACD,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,QAAQ;gBACxD,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK;gBACrE,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC;aAChE,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,MAAM,CAAC,MAAM,SAAS,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB;QAC/B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAEhD,MAAM,eAAe,GAAG;YACtB;gBACE,MAAM,EAAE,eAAe;gBACvB,KAAK,EAAE,wCAAwC;gBAC/C,WAAW,EAAE,oFAAoF;gBACjG,QAAQ,EAAE,UAAU;gBACpB,UAAU,EAAE,GAAG;gBACf,WAAW,EAAE,8CAA8C;gBAC3D,MAAM,EAAE,QAAQ;gBAChB,iBAAiB,EAAE;oBACjB,EAAE,IAAI,EAAE,cAAc,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE;iBAChE;gBACD,UAAU,EAAE;oBACV,EAAE,GAAG,EAAE,gDAAgD,EAAE,IAAI,EAAE,UAAU,EAAE;iBAC5E;gBACD,iBAAiB,EAAE,IAAI;gBACvB,eAAe,EAAE,IAAI;gBACrB,cAAc,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACtC,aAAa,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;aACtC;YACD;gBACE,MAAM,EAAE,eAAe;gBACvB,KAAK,EAAE,qCAAqC;gBAC5C,WAAW,EAAE,0DAA0D;gBACvE,QAAQ,EAAE,MAAM;gBAChB,UAAU,EAAE,GAAG;gBACf,WAAW,EAAE,8CAA8C;gBAC3D,MAAM,EAAE,QAAQ;gBAChB,iBAAiB,EAAE;oBACjB,EAAE,IAAI,EAAE,mBAAmB,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE;iBAC5D;gBACD,UAAU,EAAE;oBACV,EAAE,GAAG,EAAE,gDAAgD,EAAE,IAAI,EAAE,UAAU,EAAE;iBAC5E;gBACD,iBAAiB,EAAE,KAAK;gBACxB,eAAe,EAAE,IAAI;gBACrB,cAAc,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACtC,aAAa,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;aACtC;YACD;gBACE,MAAM,EAAE,eAAe;gBACvB,KAAK,EAAE,qCAAqC;gBAC5C,WAAW,EAAE,uDAAuD;gBACpE,QAAQ,EAAE,QAAQ;gBAClB,UAAU,EAAE,GAAG;gBACf,WAAW,EAAE,8CAA8C;gBAC3D,MAAM,EAAE,QAAQ;gBAChB,iBAAiB,EAAE;oBACjB,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE;iBAC9D;gBACD,UAAU,EAAE;oBACV,EAAE,GAAG,EAAE,gDAAgD,EAAE,IAAI,EAAE,UAAU,EAAE;iBAC5E;gBACD,iBAAiB,EAAE,KAAK;gBACxB,eAAe,EAAE,KAAK;gBACtB,cAAc,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACtC,aAAa,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;aACtC;SACF,CAAC;QAEF,KAAK,MAAM,IAAI,IAAI,eAAe,EAAE,CAAC;YACnC,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;OAiB3B,EAAE;gBACD,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ;gBACxD,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM;gBAC9C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;gBACvE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,eAAe;gBAC5C,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa;aACxC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,eAAe,CAAC,MAAM,kBAAkB,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB;QAC9B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAEhD,MAAM,MAAM,GAAG;YACb;gBACE,UAAU,EAAE,mBAAmB;gBAC/B,QAAQ,EAAE,MAAM;gBAChB,KAAK,EAAE,gCAAgC;gBACvC,WAAW,EAAE,kEAAkE;gBAC/E,SAAS,EAAE,cAAc;gBACzB,SAAS,EAAE,cAAc;gBACzB,WAAW,EAAE,KAAK;gBAClB,WAAW,EAAE,EAAE;gBACf,QAAQ,EAAE,KAAK;gBACf,OAAO,EAAE;oBACP,iBAAiB,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC;oBAC5C,aAAa,EAAE,EAAE;iBAClB;gBACD,QAAQ,EAAE;oBACR,WAAW,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE;oBACpD,mBAAmB,EAAE,EAAE,UAAU,EAAE,WAAW,EAAE;iBACjD;gBACD,MAAM,EAAE,MAAM;gBACd,aAAa,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,EAAE,aAAa;gBAC5D,YAAY,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,EAAE,iBAAiB;gBAC/D,gBAAgB,EAAE,EAAE;gBACpB,UAAU,EAAE,IAAI;aACjB;YACD;gBACE,UAAU,EAAE,mBAAmB;gBAC/B,QAAQ,EAAE,UAAU;gBACpB,KAAK,EAAE,iCAAiC;gBACxC,WAAW,EAAE,0CAA0C;gBACvD,SAAS,EAAE,cAAc;gBACzB,SAAS,EAAE,IAAI;gBACf,OAAO,EAAE;oBACP,SAAS,EAAE,0CAA0C;oBACrD,YAAY,EAAE,gBAAgB;oBAC9B,OAAO,EAAE,kBAAkB;iBAC5B;gBACD,QAAQ,EAAE;oBACR,iBAAiB,EAAE,aAAa;oBAChC,WAAW,EAAE,oBAAoB;iBAClC;gBACD,MAAM,EAAE,eAAe;gBACvB,aAAa,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,EAAE,cAAc;gBAC7D,YAAY,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC;gBAC5C,gBAAgB,EAAE,CAAC;gBACnB,UAAU,EAAE,IAAI;aACjB;YACD;gBACE,UAAU,EAAE,iBAAiB;gBAC7B,QAAQ,EAAE,QAAQ;gBAClB,KAAK,EAAE,iCAAiC;gBACxC,WAAW,EAAE,wCAAwC;gBACrD,SAAS,EAAE,cAAc;gBACzB,SAAS,EAAE,cAAc;gBACzB,WAAW,EAAE,IAAI;gBACjB,WAAW,EAAE,GAAG;gBAChB,QAAQ,EAAE,KAAK;gBACf,OAAO,EAAE;oBACP,iBAAiB,EAAE,UAAU,EAAE,MAAM;oBACrC,gBAAgB,EAAE,GAAG;oBACrB,eAAe,EAAE,SAAS,CAAC,QAAQ;iBACpC;gBACD,QAAQ,EAAE;oBACR,kBAAkB,EAAE,IAAI;oBACxB,WAAW,EAAE,WAAW;iBACzB;gBACD,MAAM,EAAE,UAAU;gBAClB,aAAa,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,EAAE,eAAe;gBAC/D,YAAY,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC;gBAC7C,gBAAgB,EAAE,CAAC;gBACnB,UAAU,EAAE,IAAI;aACjB;SACF,CAAC;QAEF,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;OAG3B,EAAE;gBACD,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,WAAW;gBAChE,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,WAAW;gBACtE,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC;gBAC7E,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,YAAY;gBACrD,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,UAAU;aACzC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,MAAM,CAAC,MAAM,kBAAkB,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB;QACjC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;QAEpD,MAAM,IAAI,GAAG;YACX,EAAE,IAAI,EAAE,aAAa,EAAE,WAAW,EAAE,6BAA6B,EAAE,KAAK,EAAE,SAAS,EAAE;YACrF,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,wBAAwB,EAAE,KAAK,EAAE,SAAS,EAAE;YAC5E,EAAE,IAAI,EAAE,iBAAiB,EAAE,WAAW,EAAE,2BAA2B,EAAE,KAAK,EAAE,SAAS,EAAE;YACvF,EAAE,IAAI,EAAE,gBAAgB,EAAE,WAAW,EAAE,iCAAiC,EAAE,KAAK,EAAE,SAAS,EAAE;YAC5F,EAAE,IAAI,EAAE,gBAAgB,EAAE,WAAW,EAAE,kCAAkC,EAAE,KAAK,EAAE,SAAS,EAAE;YAC7F,EAAE,IAAI,EAAE,iBAAiB,EAAE,WAAW,EAAE,+BAA+B,EAAE,KAAK,EAAE,SAAS,EAAE;YAC3F,EAAE,IAAI,EAAE,gBAAgB,EAAE,WAAW,EAAE,2BAA2B,EAAE,KAAK,EAAE,SAAS,EAAE;YACtF,EAAE,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,2BAA2B,EAAE,KAAK,EAAE,SAAS,EAAE;SAClF,CAAC;QAEF,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;OAM3B,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,MAAM,sBAAsB,CAAC,CAAC;IACjE,CAAC;CACF;AA1eD,8CA0eC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\database\\seeds\\development.seed.ts"], "sourcesContent": ["import { DataSource } from 'typeorm';\r\nimport { Logger } from '@nestjs/common';\r\nimport * as bcrypt from 'bcrypt';\r\n\r\n/**\r\n * Development environment database seeder\r\n * Creates sample data for development and testing purposes\r\n */\r\nexport class DevelopmentSeeder {\r\n  private readonly logger = new Logger(DevelopmentSeeder.name);\r\n\r\n  constructor(private readonly dataSource: DataSource) {}\r\n\r\n  /**\r\n   * Execute all development seeds\r\n   */\r\n  async seed(): Promise<void> {\r\n    this.logger.log('Starting development database seeding...');\r\n\r\n    try {\r\n      await this.seedRoles();\r\n      await this.seedUsers();\r\n      await this.seedAssets();\r\n      await this.seedVulnerabilities();\r\n      await this.seedSecurityEvents();\r\n      await this.seedSecurityEventTags();\r\n\r\n      this.logger.log('Development database seeding completed successfully');\r\n    } catch (error) {\r\n      this.logger.error('Development database seeding failed', {\r\n        error: error.message,\r\n        stack: error.stack,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Seed system roles\r\n   */\r\n  private async seedRoles(): Promise<void> {\r\n    this.logger.debug('Seeding roles...');\r\n\r\n    const roles = [\r\n      {\r\n        name: 'admin',\r\n        description: 'System administrator with full access',\r\n        permissions: [\r\n          'users:read', 'users:write', 'users:delete',\r\n          'roles:read', 'roles:write', 'roles:delete',\r\n          'security-events:read', 'security-events:write', 'security-events:delete',\r\n          'vulnerabilities:read', 'vulnerabilities:write', 'vulnerabilities:delete',\r\n          'assets:read', 'assets:write', 'assets:delete',\r\n          'scans:read', 'scans:write', 'scans:delete',\r\n          'reports:read', 'reports:write',\r\n          'system:admin'\r\n        ],\r\n        is_system_role: true,\r\n      },\r\n      {\r\n        name: 'security_analyst',\r\n        description: 'Security analyst with read/write access to security data',\r\n        permissions: [\r\n          'security-events:read', 'security-events:write',\r\n          'vulnerabilities:read', 'vulnerabilities:write',\r\n          'assets:read', 'assets:write',\r\n          'scans:read', 'scans:write',\r\n          'reports:read'\r\n        ],\r\n        is_system_role: true,\r\n      },\r\n      {\r\n        name: 'viewer',\r\n        description: 'Read-only access to security data',\r\n        permissions: [\r\n          'security-events:read',\r\n          'vulnerabilities:read',\r\n          'assets:read',\r\n          'scans:read',\r\n          'reports:read'\r\n        ],\r\n        is_system_role: true,\r\n      },\r\n      {\r\n        name: 'incident_responder',\r\n        description: 'Incident response team member',\r\n        permissions: [\r\n          'security-events:read', 'security-events:write',\r\n          'vulnerabilities:read',\r\n          'assets:read',\r\n          'reports:read'\r\n        ],\r\n        is_system_role: true,\r\n      },\r\n    ];\r\n\r\n    for (const role of roles) {\r\n      await this.dataSource.query(`\r\n        INSERT INTO roles (name, description, permissions, is_system_role)\r\n        VALUES ($1, $2, $3, $4)\r\n        ON CONFLICT (name) DO UPDATE SET\r\n          description = EXCLUDED.description,\r\n          permissions = EXCLUDED.permissions,\r\n          is_system_role = EXCLUDED.is_system_role,\r\n          updated_at = CURRENT_TIMESTAMP\r\n      `, [role.name, role.description, JSON.stringify(role.permissions), role.is_system_role]);\r\n    }\r\n\r\n    this.logger.debug(`Seeded ${roles.length} roles`);\r\n  }\r\n\r\n  /**\r\n   * Seed development users\r\n   */\r\n  private async seedUsers(): Promise<void> {\r\n    this.logger.debug('Seeding users...');\r\n\r\n    const passwordHash = await bcrypt.hash('dev123456', 10);\r\n\r\n    const users = [\r\n      {\r\n        email: '<EMAIL>',\r\n        password_hash: passwordHash,\r\n        first_name: 'System',\r\n        last_name: 'Administrator',\r\n        role: 'admin',\r\n        is_active: true,\r\n        email_verified: true,\r\n      },\r\n      {\r\n        email: '<EMAIL>',\r\n        password_hash: passwordHash,\r\n        first_name: 'Security',\r\n        last_name: 'Analyst',\r\n        role: 'security_analyst',\r\n        is_active: true,\r\n        email_verified: true,\r\n      },\r\n      {\r\n        email: '<EMAIL>',\r\n        password_hash: passwordHash,\r\n        first_name: 'Read',\r\n        last_name: 'Only',\r\n        role: 'viewer',\r\n        is_active: true,\r\n        email_verified: true,\r\n      },\r\n      {\r\n        email: '<EMAIL>',\r\n        password_hash: passwordHash,\r\n        first_name: 'Incident',\r\n        last_name: 'Responder',\r\n        role: 'incident_responder',\r\n        is_active: true,\r\n        email_verified: true,\r\n      },\r\n    ];\r\n\r\n    for (const user of users) {\r\n      await this.dataSource.query(`\r\n        INSERT INTO users (email, password_hash, first_name, last_name, role, is_active, email_verified)\r\n        VALUES ($1, $2, $3, $4, $5, $6, $7)\r\n        ON CONFLICT (email) DO UPDATE SET\r\n          password_hash = EXCLUDED.password_hash,\r\n          first_name = EXCLUDED.first_name,\r\n          last_name = EXCLUDED.last_name,\r\n          role = EXCLUDED.role,\r\n          is_active = EXCLUDED.is_active,\r\n          email_verified = EXCLUDED.email_verified,\r\n          updated_at = CURRENT_TIMESTAMP\r\n      `, [user.email, user.password_hash, user.first_name, user.last_name, user.role, user.is_active, user.email_verified]);\r\n    }\r\n\r\n    this.logger.debug(`Seeded ${users.length} users`);\r\n  }\r\n\r\n  /**\r\n   * Seed sample assets\r\n   */\r\n  private async seedAssets(): Promise<void> {\r\n    this.logger.debug('Seeding assets...');\r\n\r\n    const assets = [\r\n      {\r\n        name: 'Web Server 01',\r\n        type: 'SERVER',\r\n        ip_address: '***********0',\r\n        hostname: 'web01.internal',\r\n        operating_system: 'Ubuntu',\r\n        os_version: '22.04 LTS',\r\n        location: 'Data Center A',\r\n        owner: 'IT Operations',\r\n        criticality: 'HIGH',\r\n        status: 'ACTIVE',\r\n        metadata: {\r\n          services: ['nginx', 'php-fpm', 'mysql'],\r\n          environment: 'production',\r\n          backup_schedule: 'daily'\r\n        }\r\n      },\r\n      {\r\n        name: 'Database Server',\r\n        type: 'DATABASE',\r\n        ip_address: '************',\r\n        hostname: 'db01.internal',\r\n        operating_system: 'CentOS',\r\n        os_version: '8.5',\r\n        location: 'Data Center A',\r\n        owner: 'Database Team',\r\n        criticality: 'CRITICAL',\r\n        status: 'ACTIVE',\r\n        metadata: {\r\n          database_type: 'PostgreSQL',\r\n          version: '14.2',\r\n          backup_schedule: 'hourly'\r\n        }\r\n      },\r\n      {\r\n        name: 'Development Workstation',\r\n        type: 'WORKSTATION',\r\n        ip_address: '************',\r\n        hostname: 'dev-ws-01',\r\n        operating_system: 'Windows',\r\n        os_version: '11 Pro',\r\n        location: 'Office Floor 2',\r\n        owner: 'Development Team',\r\n        criticality: 'MEDIUM',\r\n        status: 'ACTIVE',\r\n        metadata: {\r\n          user: 'developer',\r\n          department: 'Engineering'\r\n        }\r\n      },\r\n      {\r\n        name: 'Core Router',\r\n        type: 'NETWORK_DEVICE',\r\n        ip_address: '***********',\r\n        hostname: 'router-core-01',\r\n        operating_system: 'Cisco IOS',\r\n        os_version: '15.7',\r\n        location: 'Network Closet',\r\n        owner: 'Network Team',\r\n        criticality: 'CRITICAL',\r\n        status: 'ACTIVE',\r\n        metadata: {\r\n          device_type: 'router',\r\n          model: 'Cisco ISR 4331'\r\n        }\r\n      },\r\n    ];\r\n\r\n    for (const asset of assets) {\r\n      await this.dataSource.query(`\r\n        INSERT INTO assets (name, type, ip_address, hostname, operating_system, os_version, location, owner, criticality, status, metadata)\r\n        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)\r\n        ON CONFLICT (name) DO UPDATE SET\r\n          type = EXCLUDED.type,\r\n          ip_address = EXCLUDED.ip_address,\r\n          hostname = EXCLUDED.hostname,\r\n          operating_system = EXCLUDED.operating_system,\r\n          os_version = EXCLUDED.os_version,\r\n          location = EXCLUDED.location,\r\n          owner = EXCLUDED.owner,\r\n          criticality = EXCLUDED.criticality,\r\n          status = EXCLUDED.status,\r\n          metadata = EXCLUDED.metadata,\r\n          updated_at = CURRENT_TIMESTAMP\r\n      `, [\r\n        asset.name, asset.type, asset.ip_address, asset.hostname,\r\n        asset.operating_system, asset.os_version, asset.location, asset.owner,\r\n        asset.criticality, asset.status, JSON.stringify(asset.metadata)\r\n      ]);\r\n    }\r\n\r\n    this.logger.debug(`Seeded ${assets.length} assets`);\r\n  }\r\n\r\n  /**\r\n   * Seed sample vulnerabilities\r\n   */\r\n  private async seedVulnerabilities(): Promise<void> {\r\n    this.logger.debug('Seeding vulnerabilities...');\r\n\r\n    const vulnerabilities = [\r\n      {\r\n        cve_id: 'CVE-2023-0001',\r\n        title: 'Remote Code Execution in Web Framework',\r\n        description: 'A critical vulnerability allowing remote code execution through unsanitized input.',\r\n        severity: 'CRITICAL',\r\n        cvss_score: 9.8,\r\n        cvss_vector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H',\r\n        cwe_id: 'CWE-78',\r\n        affected_software: [\r\n          { name: 'WebFramework', versions: ['1.0.0', '1.1.0', '1.2.0'] }\r\n        ],\r\n        references: [\r\n          { url: 'https://nvd.nist.gov/vuln/detail/CVE-2023-0001', type: 'advisory' }\r\n        ],\r\n        exploit_available: true,\r\n        patch_available: true,\r\n        published_date: new Date('2023-01-15'),\r\n        modified_date: new Date('2023-01-20')\r\n      },\r\n      {\r\n        cve_id: 'CVE-2023-0002',\r\n        title: 'SQL Injection in Database Interface',\r\n        description: 'SQL injection vulnerability in database query interface.',\r\n        severity: 'HIGH',\r\n        cvss_score: 8.1,\r\n        cvss_vector: 'CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:N',\r\n        cwe_id: 'CWE-89',\r\n        affected_software: [\r\n          { name: 'DatabaseInterface', versions: ['2.0.0', '2.1.0'] }\r\n        ],\r\n        references: [\r\n          { url: 'https://nvd.nist.gov/vuln/detail/CVE-2023-0002', type: 'advisory' }\r\n        ],\r\n        exploit_available: false,\r\n        patch_available: true,\r\n        published_date: new Date('2023-02-01'),\r\n        modified_date: new Date('2023-02-05')\r\n      },\r\n      {\r\n        cve_id: 'CVE-2023-0003',\r\n        title: 'Cross-Site Scripting in Admin Panel',\r\n        description: 'Stored XSS vulnerability in administrative interface.',\r\n        severity: 'MEDIUM',\r\n        cvss_score: 6.1,\r\n        cvss_vector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N',\r\n        cwe_id: 'CWE-79',\r\n        affected_software: [\r\n          { name: 'AdminPanel', versions: ['3.0.0', '3.1.0', '3.2.0'] }\r\n        ],\r\n        references: [\r\n          { url: 'https://nvd.nist.gov/vuln/detail/CVE-2023-0003', type: 'advisory' }\r\n        ],\r\n        exploit_available: false,\r\n        patch_available: false,\r\n        published_date: new Date('2023-03-01'),\r\n        modified_date: new Date('2023-03-01')\r\n      },\r\n    ];\r\n\r\n    for (const vuln of vulnerabilities) {\r\n      await this.dataSource.query(`\r\n        INSERT INTO vulnerabilities (cve_id, title, description, severity, cvss_score, cvss_vector, cwe_id, affected_software, references, exploit_available, patch_available, published_date, modified_date)\r\n        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)\r\n        ON CONFLICT (cve_id) DO UPDATE SET\r\n          title = EXCLUDED.title,\r\n          description = EXCLUDED.description,\r\n          severity = EXCLUDED.severity,\r\n          cvss_score = EXCLUDED.cvss_score,\r\n          cvss_vector = EXCLUDED.cvss_vector,\r\n          cwe_id = EXCLUDED.cwe_id,\r\n          affected_software = EXCLUDED.affected_software,\r\n          references = EXCLUDED.references,\r\n          exploit_available = EXCLUDED.exploit_available,\r\n          patch_available = EXCLUDED.patch_available,\r\n          published_date = EXCLUDED.published_date,\r\n          modified_date = EXCLUDED.modified_date,\r\n          updated_at = CURRENT_TIMESTAMP\r\n      `, [\r\n        vuln.cve_id, vuln.title, vuln.description, vuln.severity,\r\n        vuln.cvss_score, vuln.cvss_vector, vuln.cwe_id,\r\n        JSON.stringify(vuln.affected_software), JSON.stringify(vuln.references),\r\n        vuln.exploit_available, vuln.patch_available,\r\n        vuln.published_date, vuln.modified_date\r\n      ]);\r\n    }\r\n\r\n    this.logger.debug(`Seeded ${vulnerabilities.length} vulnerabilities`);\r\n  }\r\n\r\n  /**\r\n   * Seed sample security events\r\n   */\r\n  private async seedSecurityEvents(): Promise<void> {\r\n    this.logger.debug('Seeding security events...');\r\n\r\n    const events = [\r\n      {\r\n        event_type: 'INTRUSION_ATTEMPT',\r\n        severity: 'HIGH',\r\n        title: 'Multiple Failed Login Attempts',\r\n        description: 'Multiple failed login attempts detected from external IP address',\r\n        source_ip: '************',\r\n        target_ip: '***********0',\r\n        source_port: 45123,\r\n        target_port: 22,\r\n        protocol: 'TCP',\r\n        payload: {\r\n          username_attempts: ['admin', 'root', 'user'],\r\n          attempt_count: 15\r\n        },\r\n        metadata: {\r\n          geolocation: { country: 'Unknown', city: 'Unknown' },\r\n          threat_intelligence: { reputation: 'malicious' }\r\n        },\r\n        status: 'OPEN',\r\n        first_seen_at: new Date(Date.now() - 3600000), // 1 hour ago\r\n        last_seen_at: new Date(Date.now() - 1800000), // 30 minutes ago\r\n        occurrence_count: 15,\r\n        risk_score: 85.5\r\n      },\r\n      {\r\n        event_type: 'MALWARE_DETECTION',\r\n        severity: 'CRITICAL',\r\n        title: 'Malware Detected on Workstation',\r\n        description: 'Trojan horse detected in downloaded file',\r\n        source_ip: '************',\r\n        target_ip: null,\r\n        payload: {\r\n          file_path: 'C:\\\\Users\\\\<USER>\\\\Downloads\\\\document.exe',\r\n          malware_type: 'Trojan.Generic',\r\n          scanner: 'Windows Defender'\r\n        },\r\n        metadata: {\r\n          quarantine_status: 'quarantined',\r\n          scan_engine: 'Microsoft Defender'\r\n        },\r\n        status: 'INVESTIGATING',\r\n        first_seen_at: new Date(Date.now() - 7200000), // 2 hours ago\r\n        last_seen_at: new Date(Date.now() - 7200000),\r\n        occurrence_count: 1,\r\n        risk_score: 95.0\r\n      },\r\n      {\r\n        event_type: 'NETWORK_ANOMALY',\r\n        severity: 'MEDIUM',\r\n        title: 'Unusual Network Traffic Pattern',\r\n        description: 'Abnormal data transfer volume detected',\r\n        source_ip: '************',\r\n        target_ip: '************',\r\n        source_port: 3306,\r\n        target_port: 443,\r\n        protocol: 'TCP',\r\n        payload: {\r\n          bytes_transferred: 1073741824, // 1GB\r\n          duration_seconds: 300,\r\n          normal_baseline: 104857600 // 100MB\r\n        },\r\n        metadata: {\r\n          baseline_deviation: 10.0,\r\n          time_of_day: 'off_hours'\r\n        },\r\n        status: 'RESOLVED',\r\n        first_seen_at: new Date(Date.now() - 86400000), // 24 hours ago\r\n        last_seen_at: new Date(Date.now() - 86400000),\r\n        occurrence_count: 1,\r\n        risk_score: 65.0\r\n      },\r\n    ];\r\n\r\n    for (const event of events) {\r\n      await this.dataSource.query(`\r\n        INSERT INTO security_events (event_type, severity, title, description, source_ip, target_ip, source_port, target_port, protocol, payload, metadata, status, first_seen_at, last_seen_at, occurrence_count, risk_score)\r\n        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)\r\n      `, [\r\n        event.event_type, event.severity, event.title, event.description,\r\n        event.source_ip, event.target_ip, event.source_port, event.target_port,\r\n        event.protocol, JSON.stringify(event.payload), JSON.stringify(event.metadata),\r\n        event.status, event.first_seen_at, event.last_seen_at,\r\n        event.occurrence_count, event.risk_score\r\n      ]);\r\n    }\r\n\r\n    this.logger.debug(`Seeded ${events.length} security events`);\r\n  }\r\n\r\n  /**\r\n   * Seed security event tags\r\n   */\r\n  private async seedSecurityEventTags(): Promise<void> {\r\n    this.logger.debug('Seeding security event tags...');\r\n\r\n    const tags = [\r\n      { name: 'brute-force', description: 'Brute force attack attempts', color: '#ff4444' },\r\n      { name: 'malware', description: 'Malware related events', color: '#cc0000' },\r\n      { name: 'network-anomaly', description: 'Network traffic anomalies', color: '#ff8800' },\r\n      { name: 'false-positive', description: 'Confirmed false positive events', color: '#888888' },\r\n      { name: 'critical-asset', description: 'Events affecting critical assets', color: '#ff0000' },\r\n      { name: 'external-threat', description: 'Threats from external sources', color: '#9900cc' },\r\n      { name: 'insider-threat', description: 'Potential insider threats', color: '#cc6600' },\r\n      { name: 'automated', description: 'Automated attack patterns', color: '#0066cc' },\r\n    ];\r\n\r\n    for (const tag of tags) {\r\n      await this.dataSource.query(`\r\n        INSERT INTO security_event_tags (name, description, color)\r\n        VALUES ($1, $2, $3)\r\n        ON CONFLICT (name) DO UPDATE SET\r\n          description = EXCLUDED.description,\r\n          color = EXCLUDED.color\r\n      `, [tag.name, tag.description, tag.color]);\r\n    }\r\n\r\n    this.logger.debug(`Seeded ${tags.length} security event tags`);\r\n  }\r\n}"], "version": 3}