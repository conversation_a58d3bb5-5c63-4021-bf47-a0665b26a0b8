39d250874e5fab67d1feaa4d842b9854
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Vulnerability = exports.VulnerabilityStatus = void 0;
const shared_kernel_1 = require("../../../../../shared-kernel");
const threat_severity_enum_1 = require("../../enums/threat-severity.enum");
const confidence_level_enum_1 = require("../../enums/confidence-level.enum");
const vulnerability_discovered_event_1 = require("../../events/vulnerability-discovered.event");
const vulnerability_status_changed_event_1 = require("../../events/vulnerability-status-changed.event");
/**
 * Vulnerability Status
 */
var VulnerabilityStatus;
(function (VulnerabilityStatus) {
    VulnerabilityStatus["DISCOVERED"] = "discovered";
    VulnerabilityStatus["CONFIRMED"] = "confirmed";
    VulnerabilityStatus["TRIAGED"] = "triaged";
    VulnerabilityStatus["IN_PROGRESS"] = "in_progress";
    VulnerabilityStatus["REMEDIATED"] = "remediated";
    VulnerabilityStatus["VERIFIED"] = "verified";
    VulnerabilityStatus["CLOSED"] = "closed";
    VulnerabilityStatus["FALSE_POSITIVE"] = "false_positive";
    VulnerabilityStatus["ACCEPTED_RISK"] = "accepted_risk";
})(VulnerabilityStatus || (exports.VulnerabilityStatus = VulnerabilityStatus = {}));
/**
 * Vulnerability Entity
 *
 * Represents a security vulnerability with comprehensive tracking and management.
 * Handles the complete vulnerability lifecycle from discovery to remediation.
 *
 * Key responsibilities:
 * - Vulnerability lifecycle management
 * - Risk assessment and prioritization
 * - Remediation tracking and verification
 * - Compliance impact assessment
 * - Asset impact analysis
 *
 * Business Rules:
 * - Critical vulnerabilities require immediate triage
 * - Remediation timelines based on severity and exposure
 * - Compliance deadlines override standard timelines
 * - Risk scores must be recalculated when context changes
 */
class Vulnerability extends shared_kernel_1.BaseAggregateRoot {
    constructor(props, id) {
        super(props, id);
    }
    validate() {
        if (!this.props.title || this.props.title.trim().length === 0) {
            throw new Error('Vulnerability must have a title');
        }
        if (!this.props.description || this.props.description.trim().length === 0) {
            throw new Error('Vulnerability must have a description');
        }
        if (!Object.values(threat_severity_enum_1.ThreatSeverity).includes(this.props.severity)) {
            throw new Error(`Invalid vulnerability severity: ${this.props.severity}`);
        }
        if (!Object.values(confidence_level_enum_1.ConfidenceLevel).includes(this.props.confidence)) {
            throw new Error(`Invalid confidence level: ${this.props.confidence}`);
        }
        if (!Object.values(VulnerabilityStatus).includes(this.props.status)) {
            throw new Error(`Invalid vulnerability status: ${this.props.status}`);
        }
        if (!this.props.category || this.props.category.trim().length === 0) {
            throw new Error('Vulnerability must have a category');
        }
        if (!this.props.type || this.props.type.trim().length === 0) {
            throw new Error('Vulnerability must have a type');
        }
        if (this.props.affectedAssets.length === 0) {
            throw new Error('Vulnerability must affect at least one asset');
        }
        if (this.props.riskAssessment.riskScore < Vulnerability.MIN_RISK_SCORE ||
            this.props.riskAssessment.riskScore > Vulnerability.MAX_RISK_SCORE) {
            throw new Error(`Risk score must be between ${Vulnerability.MIN_RISK_SCORE} and ${Vulnerability.MAX_RISK_SCORE}`);
        }
        if (!this.props.discovery.discoveredAt) {
            throw new Error('Vulnerability must have a discovery date');
        }
    }
    /**
     * Create a new vulnerability
     */
    static create(title, description, severity, category, type, affectedAssets, discovery, options) {
        const riskAssessment = Vulnerability.calculateInitialRiskAssessment(severity, affectedAssets, options?.cvssScores || []);
        const props = {
            cveId: options?.cveId,
            title: title.trim(),
            description: description.trim(),
            severity,
            cvssScores: options?.cvssScores || [],
            category: category.trim(),
            type: type.trim(),
            confidence: options?.confidence || confidence_level_enum_1.ConfidenceLevel.MEDIUM,
            affectedAssets,
            status: VulnerabilityStatus.DISCOVERED,
            discovery,
            exploitation: options?.exploitation,
            remediation: {
                status: 'not_started',
                priority: Vulnerability.calculateRemediationPriority(severity, riskAssessment.riskScore),
                actions: [],
                workarounds: [],
                timeline: {},
            },
            riskAssessment,
            complianceImpact: [],
            tags: options?.tags || [],
            attributes: options?.attributes || {},
        };
        const vulnerability = new Vulnerability(props);
        // Publish domain event
        vulnerability.addDomainEvent(new vulnerability_discovered_event_1.VulnerabilityDiscoveredEvent(vulnerability.id, {
            vulnerabilityId: vulnerability.id.toString(),
            cveId: vulnerability.props.cveId,
            title: vulnerability.props.title,
            severity: vulnerability.props.severity,
            category: vulnerability.props.category,
            type: vulnerability.props.type,
            confidence: vulnerability.props.confidence,
            riskScore: vulnerability.props.riskAssessment.riskScore,
            affectedAssetCount: vulnerability.props.affectedAssets.length,
            discoveryMethod: vulnerability.props.discovery.method,
            timestamp: new Date().toISOString(),
        }));
        return vulnerability;
    }
    static calculateInitialRiskAssessment(severity, affectedAssets, cvssScores) {
        // Base risk from severity
        let riskScore = 0;
        switch (severity) {
            case threat_severity_enum_1.ThreatSeverity.CRITICAL:
                riskScore = 90;
                break;
            case threat_severity_enum_1.ThreatSeverity.HIGH:
                riskScore = 70;
                break;
            case threat_severity_enum_1.ThreatSeverity.MEDIUM:
                riskScore = 50;
                break;
            case threat_severity_enum_1.ThreatSeverity.LOW:
                riskScore = 30;
                break;
            case threat_severity_enum_1.ThreatSeverity.UNKNOWN:
                riskScore = 20;
                break;
        }
        // Adjust for CVSS scores
        if (cvssScores.length > 0) {
            const maxCVSS = Math.max(...cvssScores.map(score => score.baseScore));
            riskScore = Math.max(riskScore, maxCVSS * 10);
        }
        // Adjust for asset criticality
        const criticalAssets = affectedAssets.filter(asset => asset.criticality === 'critical');
        const highCriticalityAssets = affectedAssets.filter(asset => asset.criticality === 'high');
        if (criticalAssets.length > 0) {
            riskScore += 15;
        }
        else if (highCriticalityAssets.length > 0) {
            riskScore += 10;
        }
        // Adjust for exposure
        const externalAssets = affectedAssets.filter(asset => asset.exposure === 'external');
        if (externalAssets.length > 0) {
            riskScore += 10;
        }
        const finalRiskScore = Math.min(100, riskScore);
        return {
            riskScore: finalRiskScore,
            exploitabilityScore: 50, // Default, to be updated with exploitation data
            impactScore: finalRiskScore,
            environmentalFactors: {
                assetCriticality: criticalAssets.length > 0 ? 100 : 50,
                networkExposure: externalAssets.length > 0 ? 100 : 30,
                dataClassification: 50, // Default
                businessImpact: 50, // Default
            },
            riskFactors: [],
            riskLevel: Vulnerability.getRiskLevel(finalRiskScore),
            businessRisk: {
                financialImpact: 0,
                reputationalImpact: severity === threat_severity_enum_1.ThreatSeverity.CRITICAL ? 'high' : 'medium',
                operationalImpact: criticalAssets.length > 0 ? 'high' : 'medium',
                complianceImpact: 'medium',
            },
        };
    }
    static getRiskLevel(riskScore) {
        if (riskScore >= Vulnerability.CRITICAL_RISK_THRESHOLD)
            return 'critical';
        if (riskScore >= Vulnerability.HIGH_RISK_THRESHOLD)
            return 'high';
        if (riskScore >= 30)
            return 'medium';
        return 'low';
    }
    static calculateRemediationPriority(severity, riskScore) {
        if (severity === threat_severity_enum_1.ThreatSeverity.CRITICAL || riskScore >= Vulnerability.CRITICAL_RISK_THRESHOLD) {
            return 'critical';
        }
        if (severity === threat_severity_enum_1.ThreatSeverity.HIGH || riskScore >= Vulnerability.HIGH_RISK_THRESHOLD) {
            return 'high';
        }
        if (severity === threat_severity_enum_1.ThreatSeverity.MEDIUM) {
            return 'medium';
        }
        return 'low';
    }
    /**
     * Get vulnerability ID
     */
    get cveId() {
        return this.props.cveId;
    }
    /**
     * Get vulnerability title
     */
    get title() {
        return this.props.title;
    }
    /**
     * Get vulnerability description
     */
    get description() {
        return this.props.description;
    }
    /**
     * Get vulnerability severity
     */
    get severity() {
        return this.props.severity;
    }
    /**
     * Get CVSS scores
     */
    get cvssScores() {
        return [...this.props.cvssScores];
    }
    /**
     * Get vulnerability category
     */
    get category() {
        return this.props.category;
    }
    /**
     * Get vulnerability type
     */
    get type() {
        return this.props.type;
    }
    /**
     * Get confidence level
     */
    get confidence() {
        return this.props.confidence;
    }
    /**
     * Get affected assets
     */
    get affectedAssets() {
        return [...this.props.affectedAssets];
    }
    /**
     * Get vulnerability status
     */
    get status() {
        return this.props.status;
    }
    /**
     * Get discovery information
     */
    get discovery() {
        return this.props.discovery;
    }
    /**
     * Get exploitation information
     */
    get exploitation() {
        return this.props.exploitation;
    }
    /**
     * Get remediation information
     */
    get remediation() {
        return this.props.remediation;
    }
    /**
     * Get risk assessment
     */
    get riskAssessment() {
        return this.props.riskAssessment;
    }
    /**
     * Get compliance impact
     */
    get complianceImpact() {
        return [...this.props.complianceImpact];
    }
    /**
     * Get tags
     */
    get tags() {
        return [...this.props.tags];
    }
    /**
     * Get attributes
     */
    get attributes() {
        return { ...this.props.attributes };
    }
    /**
     * Change vulnerability status
     */
    changeStatus(newStatus, reason) {
        if (newStatus === this.props.status) {
            return; // No change needed
        }
        const oldStatus = this.props.status;
        this.props.status = newStatus;
        // Update remediation timeline based on status
        this.updateRemediationTimeline(newStatus);
        // Publish domain event
        this.addDomainEvent(new vulnerability_status_changed_event_1.VulnerabilityStatusChangedEvent(this.id, {
            vulnerabilityId: this.id.toString(),
            oldStatus,
            newStatus,
            reason,
            timestamp: new Date().toISOString(),
        }));
    }
    updateRemediationTimeline(status) {
        const now = new Date();
        switch (status) {
            case VulnerabilityStatus.TRIAGED:
                if (!this.props.remediation.timeline.plannedStart) {
                    this.props.remediation.timeline.plannedStart = now;
                }
                break;
            case VulnerabilityStatus.IN_PROGRESS:
                if (!this.props.remediation.timeline.actualStart) {
                    this.props.remediation.timeline.actualStart = now;
                }
                this.props.remediation.status = 'in_progress';
                break;
            case VulnerabilityStatus.REMEDIATED:
                if (!this.props.remediation.timeline.actualCompletion) {
                    this.props.remediation.timeline.actualCompletion = now;
                }
                this.props.remediation.status = 'completed';
                break;
            case VulnerabilityStatus.VERIFIED:
                if (!this.props.remediation.timeline.verificationDate) {
                    this.props.remediation.timeline.verificationDate = now;
                }
                this.props.remediation.status = 'verified';
                break;
        }
    }
    /**
     * Check if vulnerability is critical
     */
    isCritical() {
        return this.props.severity === threat_severity_enum_1.ThreatSeverity.CRITICAL ||
            this.props.riskAssessment.riskScore >= Vulnerability.CRITICAL_RISK_THRESHOLD;
    }
    /**
     * Check if vulnerability is high risk
     */
    isHighRisk() {
        return this.props.riskAssessment.riskLevel === 'high' ||
            this.props.riskAssessment.riskLevel === 'critical';
    }
    /**
     * Check if vulnerability is actively exploited
     */
    isActivelyExploited() {
        return this.props.exploitation?.status === 'active_exploitation';
    }
    /**
     * Check if vulnerability affects critical assets
     */
    affectsCriticalAssets() {
        return this.props.affectedAssets.some(asset => asset.criticality === 'critical');
    }
    /**
     * Check if vulnerability is externally exposed
     */
    isExternallyExposed() {
        return this.props.affectedAssets.some(asset => asset.exposure === 'external');
    }
    /**
     * Check if vulnerability requires immediate attention
     */
    requiresImmediateAttention() {
        return this.isCritical() ||
            this.isActivelyExploited() ||
            (this.isHighRisk() && this.affectsCriticalAssets());
    }
    /**
     * Get remediation SLA (in days)
     */
    getRemediationSLA() {
        if (this.isCritical())
            return 1;
        if (this.isHighRisk())
            return 7;
        if (this.props.riskAssessment.riskLevel === 'medium')
            return 30;
        return 90;
    }
    /**
     * Check if remediation is overdue
     */
    isRemediationOverdue() {
        const slaDate = new Date(this.props.discovery.discoveredAt);
        slaDate.setDate(slaDate.getDate() + this.getRemediationSLA());
        return new Date() > slaDate && !this.isRemediated();
    }
    /**
     * Check if vulnerability is remediated
     */
    isRemediated() {
        return [
            VulnerabilityStatus.REMEDIATED,
            VulnerabilityStatus.VERIFIED,
            VulnerabilityStatus.CLOSED,
        ].includes(this.props.status);
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            id: this.id.toString(),
            cveId: this.props.cveId,
            title: this.props.title,
            description: this.props.description,
            severity: this.props.severity,
            cvssScores: this.props.cvssScores.map(score => score.toJSON()),
            category: this.props.category,
            type: this.props.type,
            confidence: this.props.confidence,
            affectedAssets: this.props.affectedAssets,
            status: this.props.status,
            discovery: this.props.discovery,
            exploitation: this.props.exploitation,
            remediation: this.props.remediation,
            riskAssessment: this.props.riskAssessment,
            complianceImpact: this.props.complianceImpact,
            tags: this.props.tags,
            attributes: this.props.attributes,
            analysis: {
                isCritical: this.isCritical(),
                isHighRisk: this.isHighRisk(),
                isActivelyExploited: this.isActivelyExploited(),
                affectsCriticalAssets: this.affectsCriticalAssets(),
                isExternallyExposed: this.isExternallyExposed(),
                requiresImmediateAttention: this.requiresImmediateAttention(),
                remediationSLA: this.getRemediationSLA(),
                isRemediationOverdue: this.isRemediationOverdue(),
                isRemediated: this.isRemediated(),
            },
            createdAt: this.createdAt?.toISOString(),
            updatedAt: this.updatedAt?.toISOString(),
        };
    }
}
exports.Vulnerability = Vulnerability;
Vulnerability.MIN_RISK_SCORE = 0;
Vulnerability.MAX_RISK_SCORE = 100;
Vulnerability.CRITICAL_RISK_THRESHOLD = 80;
Vulnerability.HIGH_RISK_THRESHOLD = 60;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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