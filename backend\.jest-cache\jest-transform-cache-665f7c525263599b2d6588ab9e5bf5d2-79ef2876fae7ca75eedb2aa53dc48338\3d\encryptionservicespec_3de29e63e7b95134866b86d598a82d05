179907d74d9904221c256647ed57f952
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const encryption_service_1 = require("../encryption/encryption.service");
const aes_encryption_1 = require("../encryption/aes.encryption");
const rsa_encryption_1 = require("../encryption/rsa.encryption");
const hash_service_1 = require("../encryption/hash.service");
describe('EncryptionService', () => {
    let service;
    let configService;
    let aesEncryption;
    let rsaEncryption;
    let hashService;
    const mockAESResult = {
        encryptedData: 'encrypted-data',
        iv: 'initialization-vector',
        tag: 'auth-tag',
        algorithm: 'aes-256-gcm',
    };
    const mockRSAKeyPair = {
        publicKey: '-----BEGIN PUBLIC KEY-----\nMOCK_PUBLIC_KEY\n-----END PUBLIC KEY-----',
        privateKey: '-----BEGIN PRIVATE KEY-----\nMOCK_PRIVATE_KEY\n-----END PRIVATE KEY-----',
    };
    beforeEach(async () => {
        // Mock ConfigService
        configService = {
            get: jest.fn(),
        };
        // Mock AESEncryption
        aesEncryption = {
            encrypt: jest.fn().mockResolvedValue(mockAESResult),
            decrypt: jest.fn().mockResolvedValue('decrypted-data'),
            generateKey: jest.fn().mockReturnValue('generated-aes-key'),
            validateKey: jest.fn().mockReturnValue(true),
        };
        // Mock RSAEncryption
        rsaEncryption = {
            generateKeyPair: jest.fn().mockResolvedValue(mockRSAKeyPair),
            encrypt: jest.fn().mockResolvedValue('rsa-encrypted-data'),
            decrypt: jest.fn().mockResolvedValue('rsa-decrypted-data'),
            sign: jest.fn().mockResolvedValue('rsa-signature'),
            verify: jest.fn().mockResolvedValue(true),
            encryptLargeData: jest.fn().mockResolvedValue(['chunk1', 'chunk2']),
            decryptLargeData: jest.fn().mockResolvedValue('large-decrypted-data'),
            validatePrivateKey: jest.fn().mockReturnValue(true),
            validatePublicKey: jest.fn().mockReturnValue(true),
        };
        // Mock HashService
        hashService = {
            hash: jest.fn().mockReturnValue('hashed-data'),
            sha256: jest.fn().mockReturnValue('sha256-hash'),
            sha512: jest.fn().mockReturnValue('sha512-hash'),
            hmac: jest.fn().mockReturnValue('hmac-result'),
            hashPassword: jest.fn().mockResolvedValue('bcrypt-hash'),
            verifyPassword: jest.fn().mockResolvedValue(true),
            pbkdf2: jest.fn().mockResolvedValue('pbkdf2-hash'),
            generateRandomSalt: jest.fn().mockReturnValue('random-salt'),
            generateChecksum: jest.fn().mockReturnValue('checksum'),
            verifyChecksum: jest.fn().mockReturnValue(true),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                encryption_service_1.EncryptionService,
                {
                    provide: config_1.ConfigService,
                    useValue: configService,
                },
                {
                    provide: aes_encryption_1.AESEncryption,
                    useValue: aesEncryption,
                },
                {
                    provide: rsa_encryption_1.RSAEncryption,
                    useValue: rsaEncryption,
                },
                {
                    provide: hash_service_1.HashService,
                    useValue: hashService,
                },
            ],
        }).compile();
        service = module.get(encryption_service_1.EncryptionService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('AES Encryption Methods', () => {
        describe('encryptAES', () => {
            it('should encrypt data using AES', async () => {
                const result = await service.encryptAES('test-data', 'test-key');
                expect(result).toEqual({
                    encryptedData: 'encrypted-data',
                    iv: 'initialization-vector',
                    tag: 'auth-tag',
                    algorithm: 'aes-256-gcm',
                });
                expect(aesEncryption.encrypt).toHaveBeenCalledWith('test-data', 'test-key');
            });
            it('should encrypt data without providing key', async () => {
                const result = await service.encryptAES('test-data');
                expect(result).toEqual({
                    encryptedData: 'encrypted-data',
                    iv: 'initialization-vector',
                    tag: 'auth-tag',
                    algorithm: 'aes-256-gcm',
                });
                expect(aesEncryption.encrypt).toHaveBeenCalledWith('test-data', undefined);
            });
        });
        describe('decryptAES', () => {
            it('should decrypt data using AES', async () => {
                const result = await service.decryptAES('encrypted-data', 'iv', 'tag', 'key');
                expect(result).toEqual({
                    decryptedData: 'decrypted-data',
                    algorithm: 'aes-256-gcm',
                });
                expect(aesEncryption.decrypt).toHaveBeenCalledWith({
                    encryptedData: 'encrypted-data',
                    iv: 'iv',
                    tag: 'tag',
                    key: 'key',
                });
            });
            it('should decrypt data without providing key', async () => {
                const result = await service.decryptAES('encrypted-data', 'iv', 'tag');
                expect(result).toEqual({
                    decryptedData: 'decrypted-data',
                    algorithm: 'aes-256-gcm',
                });
                expect(aesEncryption.decrypt).toHaveBeenCalledWith({
                    encryptedData: 'encrypted-data',
                    iv: 'iv',
                    tag: 'tag',
                    key: undefined,
                });
            });
        });
        describe('generateAESKey', () => {
            it('should generate AES key', () => {
                const result = service.generateAESKey();
                expect(result).toBe('generated-aes-key');
                expect(aesEncryption.generateKey).toHaveBeenCalled();
            });
        });
    });
    describe('RSA Encryption Methods', () => {
        describe('generateRSAKeyPair', () => {
            it('should generate RSA key pair', async () => {
                const result = await service.generateRSAKeyPair();
                expect(result).toEqual(mockRSAKeyPair);
                expect(rsaEncryption.generateKeyPair).toHaveBeenCalledWith(undefined);
            });
            it('should generate RSA key pair with custom key size', async () => {
                const result = await service.generateRSAKeyPair(4096);
                expect(result).toEqual(mockRSAKeyPair);
                expect(rsaEncryption.generateKeyPair).toHaveBeenCalledWith(4096);
            });
        });
        describe('encryptRSA', () => {
            it('should encrypt data using RSA', async () => {
                const result = await service.encryptRSA('test-data', 'public-key');
                expect(result).toBe('rsa-encrypted-data');
                expect(rsaEncryption.encrypt).toHaveBeenCalledWith('test-data', 'public-key', undefined);
            });
            it('should encrypt data using RSA with options', async () => {
                const options = { padding: 'OAEP' };
                const result = await service.encryptRSA('test-data', 'public-key', options);
                expect(result).toBe('rsa-encrypted-data');
                expect(rsaEncryption.encrypt).toHaveBeenCalledWith('test-data', 'public-key', options);
            });
        });
        describe('decryptRSA', () => {
            it('should decrypt data using RSA', async () => {
                const result = await service.decryptRSA('encrypted-data', 'private-key');
                expect(result).toBe('rsa-decrypted-data');
                expect(rsaEncryption.decrypt).toHaveBeenCalledWith('encrypted-data', 'private-key', undefined);
            });
            it('should decrypt data using RSA with options', async () => {
                const options = { padding: 'OAEP' };
                const result = await service.decryptRSA('encrypted-data', 'private-key', options);
                expect(result).toBe('rsa-decrypted-data');
                expect(rsaEncryption.decrypt).toHaveBeenCalledWith('encrypted-data', 'private-key', options);
            });
        });
        describe('signRSA', () => {
            it('should sign data using RSA', async () => {
                const result = await service.signRSA('test-data', 'private-key');
                expect(result).toBe('rsa-signature');
                expect(rsaEncryption.sign).toHaveBeenCalledWith('test-data', 'private-key', undefined);
            });
            it('should sign data using RSA with custom algorithm', async () => {
                const result = await service.signRSA('test-data', 'private-key', 'sha512');
                expect(result).toBe('rsa-signature');
                expect(rsaEncryption.sign).toHaveBeenCalledWith('test-data', 'private-key', 'sha512');
            });
        });
        describe('verifyRSA', () => {
            it('should verify RSA signature', async () => {
                const result = await service.verifyRSA('test-data', 'signature', 'public-key');
                expect(result).toBe(true);
                expect(rsaEncryption.verify).toHaveBeenCalledWith('test-data', 'signature', 'public-key', undefined);
            });
            it('should verify RSA signature with custom algorithm', async () => {
                const result = await service.verifyRSA('test-data', 'signature', 'public-key', 'sha512');
                expect(result).toBe(true);
                expect(rsaEncryption.verify).toHaveBeenCalledWith('test-data', 'signature', 'public-key', 'sha512');
            });
        });
        describe('encryptRSALarge', () => {
            it('should encrypt large data using RSA', async () => {
                const result = await service.encryptRSALarge('large-data', 'public-key');
                expect(result).toEqual(['chunk1', 'chunk2']);
                expect(rsaEncryption.encryptLargeData).toHaveBeenCalledWith('large-data', 'public-key');
            });
        });
        describe('decryptRSALarge', () => {
            it('should decrypt large data using RSA', async () => {
                const result = await service.decryptRSALarge(['chunk1', 'chunk2'], 'private-key');
                expect(result).toBe('large-decrypted-data');
                expect(rsaEncryption.decryptLargeData).toHaveBeenCalledWith(['chunk1', 'chunk2'], 'private-key');
            });
        });
    });
    describe('Hash Methods', () => {
        describe('hash', () => {
            it('should hash data with default algorithm', () => {
                const result = service.hash('test-data');
                expect(result).toBe('hashed-data');
                expect(hashService.hash).toHaveBeenCalledWith('test-data', { algorithm: encryption_service_1.HashAlgorithm.SHA256 });
            });
            it('should hash data with specified algorithm', () => {
                const result = service.hash('test-data', encryption_service_1.HashAlgorithm.SHA512);
                expect(result).toBe('hashed-data');
                expect(hashService.hash).toHaveBeenCalledWith('test-data', { algorithm: encryption_service_1.HashAlgorithm.SHA512 });
            });
        });
        describe('hashSHA256', () => {
            it('should hash data using SHA-256', () => {
                const result = service.hashSHA256('test-data');
                expect(result).toBe('sha256-hash');
                expect(hashService.sha256).toHaveBeenCalledWith('test-data');
            });
        });
        describe('hashSHA512', () => {
            it('should hash data using SHA-512', () => {
                const result = service.hashSHA512('test-data');
                expect(result).toBe('sha512-hash');
                expect(hashService.sha512).toHaveBeenCalledWith('test-data');
            });
        });
        describe('hmac', () => {
            it('should create HMAC with default algorithm', () => {
                const result = service.hmac('test-data', 'secret-key');
                expect(result).toBe('hmac-result');
                expect(hashService.hmac).toHaveBeenCalledWith('test-data', 'secret-key', { algorithm: encryption_service_1.HashAlgorithm.SHA256 });
            });
            it('should create HMAC with specified algorithm', () => {
                const result = service.hmac('test-data', 'secret-key', encryption_service_1.HashAlgorithm.SHA512);
                expect(result).toBe('hmac-result');
                expect(hashService.hmac).toHaveBeenCalledWith('test-data', 'secret-key', { algorithm: encryption_service_1.HashAlgorithm.SHA512 });
            });
        });
        describe('hashPassword', () => {
            it('should hash password using bcrypt', async () => {
                const result = await service.hashPassword('password');
                expect(result).toBe('bcrypt-hash');
                expect(hashService.hashPassword).toHaveBeenCalledWith('password', undefined);
            });
            it('should hash password with options', async () => {
                const options = { rounds: 12 };
                const result = await service.hashPassword('password', options);
                expect(result).toBe('bcrypt-hash');
                expect(hashService.hashPassword).toHaveBeenCalledWith('password', options);
            });
        });
        describe('verifyPassword', () => {
            it('should verify password against hash', async () => {
                const result = await service.verifyPassword('password', 'hash');
                expect(result).toBe(true);
                expect(hashService.verifyPassword).toHaveBeenCalledWith('password', 'hash');
            });
        });
        describe('pbkdf2', () => {
            it('should generate PBKDF2 hash', async () => {
                const result = await service.pbkdf2('password', 'salt');
                expect(result).toBe('pbkdf2-hash');
                expect(hashService.pbkdf2).toHaveBeenCalledWith('password', 'salt', undefined, undefined);
            });
            it('should generate PBKDF2 hash with custom parameters', async () => {
                const result = await service.pbkdf2('password', 'salt', 10000, 64);
                expect(result).toBe('pbkdf2-hash');
                expect(hashService.pbkdf2).toHaveBeenCalledWith('password', 'salt', 10000, 64);
            });
        });
    });
    describe('Utility Methods', () => {
        describe('generateSecureRandom', () => {
            it('should generate secure random string with default length', () => {
                const result = service.generateSecureRandom();
                expect(result).toBeDefined();
                expect(typeof result).toBe('string');
                expect(result.length).toBe(64); // 32 bytes * 2 (hex)
            });
            it('should generate secure random string with custom length', () => {
                const result = service.generateSecureRandom(16);
                expect(result).toBeDefined();
                expect(typeof result).toBe('string');
                expect(result.length).toBe(32); // 16 bytes * 2 (hex)
            });
        });
        describe('generateSalt', () => {
            it('should generate salt', () => {
                const result = service.generateSalt();
                expect(result).toBe('random-salt');
                expect(hashService.generateRandomSalt).toHaveBeenCalledWith(32);
            });
            it('should generate salt with custom length', () => {
                const result = service.generateSalt(16);
                expect(result).toBe('random-salt');
                expect(hashService.generateRandomSalt).toHaveBeenCalledWith(16);
            });
        });
        describe('validateAESKey', () => {
            it('should validate AES key', () => {
                const result = service.validateAESKey('test-key');
                expect(result).toBe(true);
                expect(aesEncryption.validateKey).toHaveBeenCalledWith('test-key');
            });
        });
        describe('validateRSAPrivateKey', () => {
            it('should validate RSA private key', () => {
                const result = service.validateRSAPrivateKey('private-key');
                expect(result).toBe(true);
                expect(rsaEncryption.validatePrivateKey).toHaveBeenCalledWith('private-key');
            });
        });
        describe('validateRSAPublicKey', () => {
            it('should validate RSA public key', () => {
                const result = service.validateRSAPublicKey('public-key');
                expect(result).toBe(true);
                expect(rsaEncryption.validatePublicKey).toHaveBeenCalledWith('public-key');
            });
        });
        describe('generateChecksum', () => {
            it('should generate checksum with default algorithm', () => {
                const result = service.generateChecksum('test-data');
                expect(result).toBe('checksum');
                expect(hashService.generateChecksum).toHaveBeenCalledWith('test-data', encryption_service_1.HashAlgorithm.SHA256);
            });
            it('should generate checksum with specified algorithm', () => {
                const result = service.generateChecksum('test-data', encryption_service_1.HashAlgorithm.SHA512);
                expect(result).toBe('checksum');
                expect(hashService.generateChecksum).toHaveBeenCalledWith('test-data', encryption_service_1.HashAlgorithm.SHA512);
            });
        });
        describe('verifyChecksum', () => {
            it('should verify checksum with default algorithm', () => {
                const result = service.verifyChecksum('test-data', 'expected-checksum');
                expect(result).toBe(true);
                expect(hashService.verifyChecksum).toHaveBeenCalledWith('test-data', 'expected-checksum', encryption_service_1.HashAlgorithm.SHA256);
            });
            it('should verify checksum with specified algorithm', () => {
                const result = service.verifyChecksum('test-data', 'expected-checksum', encryption_service_1.HashAlgorithm.SHA512);
                expect(result).toBe(true);
                expect(hashService.verifyChecksum).toHaveBeenCalledWith('test-data', 'expected-checksum', encryption_service_1.HashAlgorithm.SHA512);
            });
        });
    });
    describe('Error Handling', () => {
        it('should handle AES encryption errors', async () => {
            aesEncryption.encrypt.mockRejectedValue(new Error('AES encryption failed'));
            await expect(service.encryptAES('test-data')).rejects.toThrow('AES encryption failed');
        });
        it('should handle RSA encryption errors', async () => {
            rsaEncryption.encrypt.mockRejectedValue(new Error('RSA encryption failed'));
            await expect(service.encryptRSA('test-data', 'public-key')).rejects.toThrow('RSA encryption failed');
        });
        it('should handle password hashing errors', async () => {
            hashService.hashPassword.mockRejectedValue(new Error('Password hashing failed'));
            await expect(service.hashPassword('password')).rejects.toThrow('Password hashing failed');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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