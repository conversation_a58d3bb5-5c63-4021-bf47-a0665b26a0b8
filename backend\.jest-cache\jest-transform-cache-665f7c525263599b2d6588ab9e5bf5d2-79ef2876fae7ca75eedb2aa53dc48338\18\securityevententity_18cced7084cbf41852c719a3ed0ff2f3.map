{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\event\\security-event.entity.ts", "mappings": ";;;AAAA,8FAAyF;AAGzF,2FAAiF;AACjF,4FAAsF;AACtF,0GAAmG;AAkCnG;;;;;;;;;;;;;;;;;;GAkBG;AACH,MAAa,aAAc,SAAQ,uCAAqC;IAKtE,YAAY,KAAyB,EAAE,EAAmB;QACxD,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IACnB,CAAC;IAES,QAAQ;QAChB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,oDAAqB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;YACtE,MAAM,IAAI,KAAK,CAAC,oCAAoC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxE,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9D,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,aAAa,CAAC,gBAAgB,EAAE,CAAC;YAC7D,MAAM,IAAI,KAAK,CAAC,6BAA6B,aAAa,CAAC,gBAAgB,aAAa,CAAC,CAAC;QAC5F,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,aAAa,CAAC,sBAAsB,EAAE,CAAC;YACnG,MAAM,IAAI,KAAK,CAAC,mCAAmC,aAAa,CAAC,sBAAsB,aAAa,CAAC,CAAC;QACxG,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,CAAC,EAAE,CAAC;YACtC,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,aAAa,CAAC,uBAAuB,EAAE,CAAC;YAC1E,MAAM,IAAI,KAAK,CAAC,qCAAqC,aAAa,CAAC,uBAAuB,EAAE,CAAC,CAAC;QAChG,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,CAAC,EAAE,CAAC;YACrF,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM,CACX,QAAuB,EACvB,OAA4B,EAC5B,KAAa,EACb,OAMC;QAED,MAAM,KAAK,GAAuB;YAChC,QAAQ;YACR,MAAM,EAAE,oDAAqB,CAAC,GAAG;YACjC,OAAO;YACP,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE;YACnB,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE;YACzC,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE;YACnC,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE;YACzC,kBAAkB,EAAE,CAAC;YACrB,IAAI,EAAE,OAAO,EAAE,IAAI,IAAI,EAAE;YACzB,UAAU,EAAE,OAAO,EAAE,UAAU,IAAI,EAAE;SACtC,CAAC;QAEF,MAAM,KAAK,GAAG,IAAI,aAAa,CAAC,KAAK,CAAC,CAAC;QAEvC,uBAAuB;QACvB,KAAK,CAAC,cAAc,CAAC,IAAI,wDAAyB,CAChD,KAAK,CAAC,EAAE,EACR;YACE,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE;YAC5B,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK;YACxB,QAAQ,EAAE,KAAK,CAAC,KAAK,CAAC,QAAQ;YAC9B,UAAU,EAAE,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI;YAC5C,gBAAgB,EAAE,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU;YACxD,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,WAAW,EAAE;SACxD,CACF,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAI,OAAO;QACT,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,IAAI,IAAI;QACN,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,UAAU;QACR,MAAM,gBAAgB,GAAG;YACvB,oDAAqB,CAAC,QAAQ;YAC9B,oDAAqB,CAAC,QAAQ;YAC9B,oDAAqB,CAAC,MAAM;YAC5B,oDAAqB,CAAC,SAAS;YAC/B,oDAAqB,CAAC,OAAO;YAC7B,oDAAqB,CAAC,OAAO;SAC9B,CAAC;QACF,OAAO,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,YAAY;QACV,MAAM,kBAAkB,GAAG;YACzB,oDAAqB,CAAC,WAAW;YACjC,oDAAqB,CAAC,SAAS;YAC/B,oDAAqB,CAAC,WAAW;YACjC,oDAAqB,CAAC,SAAS;YAC/B,oDAAqB,CAAC,aAAa;YACnC,oDAAqB,CAAC,YAAY;SACnC,CAAC;QACF,OAAO,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,MAAM,iBAAiB,GAAG;YACxB,oDAAqB,CAAC,cAAc;YACpC,oDAAqB,CAAC,aAAa;YACnC,oDAAqB,CAAC,MAAM;YAC5B,oDAAqB,CAAC,OAAO;YAC7B,oDAAqB,CAAC,OAAO;SAC9B,CAAC;QACF,OAAO,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,oDAAqB,CAAC,MAAM;YAClD,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,aAAa,CAAC,uBAAuB,CAAC;IAC/E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,GAAW;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,YAAY,CAAU,GAAW;QAC/B,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAM,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,YAAY,CACV,SAAgC,EAChC,OAGC;QAED,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,6CAA6C,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;QACpF,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,EAAE,CAAC;YAC7C,MAAM,IAAI,KAAK,CAAC,kCAAkC,IAAI,CAAC,KAAK,CAAC,MAAM,OAAO,SAAS,EAAE,CAAC,CAAC;QACzF,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QACpC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC;QAC9B,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,IAAI,IAAI,EAAE,CAAC;QAExC,IAAI,OAAO,EAAE,YAAY,EAAE,CAAC;YAC1B,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;QACjD,CAAC;QAED,IAAI,OAAO,EAAE,kBAAkB,KAAK,SAAS,EAAE,CAAC;YAC9C,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;QAC7D,CAAC;QAED,qDAAqD;QACrD,IAAI,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,EAAE,CAAC;YAC5C,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC;QAClC,CAAC;QAED,8BAA8B;QAC9B,IAAI,CAAC,cAAc,CAAC,IAAI,qEAA+B,CACrD,IAAI,CAAC,EAAE,EACP;YACE,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE;YAC3B,SAAS;YACT,SAAS;YACT,kBAAkB,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB;YACjD,YAAY,EAAE,OAAO,EAAE,YAAY;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CACF,CAAC,CAAC;IACL,CAAC;IAEO,uBAAuB,CAAC,SAAgC;QAC9D,MAAM,gBAAgB,GAA2D;YAC/E,CAAC,oDAAqB,CAAC,GAAG,CAAC,EAAE;gBAC3B,oDAAqB,CAAC,WAAW;gBACjC,oDAAqB,CAAC,OAAO;gBAC7B,oDAAqB,CAAC,MAAM;aAC7B;YACD,CAAC,oDAAqB,CAAC,WAAW,CAAC,EAAE;gBACnC,oDAAqB,CAAC,UAAU;gBAChC,oDAAqB,CAAC,MAAM;gBAC5B,oDAAqB,CAAC,OAAO;aAC9B;YACD,CAAC,oDAAqB,CAAC,UAAU,CAAC,EAAE;gBAClC,oDAAqB,CAAC,SAAS;gBAC/B,oDAAqB,CAAC,OAAO;gBAC7B,oDAAqB,CAAC,MAAM;aAC7B;YACD,CAAC,oDAAqB,CAAC,SAAS,CAAC,EAAE;gBACjC,oDAAqB,CAAC,QAAQ;gBAC9B,oDAAqB,CAAC,MAAM;gBAC5B,oDAAqB,CAAC,OAAO;aAC9B;YACD,CAAC,oDAAqB,CAAC,QAAQ,CAAC,EAAE;gBAChC,oDAAqB,CAAC,WAAW;gBACjC,oDAAqB,CAAC,SAAS;gBAC/B,oDAAqB,CAAC,MAAM;aAC7B;YACD,CAAC,oDAAqB,CAAC,WAAW,CAAC,EAAE;gBACnC,oDAAqB,CAAC,UAAU;gBAChC,oDAAqB,CAAC,MAAM;gBAC5B,oDAAqB,CAAC,OAAO;aAC9B;YACD,CAAC,oDAAqB,CAAC,UAAU,CAAC,EAAE;gBAClC,oDAAqB,CAAC,SAAS;gBAC/B,oDAAqB,CAAC,MAAM;aAC7B;YACD,CAAC,oDAAqB,CAAC,SAAS,CAAC,EAAE;gBACjC,oDAAqB,CAAC,QAAQ;gBAC9B,oDAAqB,CAAC,MAAM;gBAC5B,oDAAqB,CAAC,OAAO;aAC9B;YACD,CAAC,oDAAqB,CAAC,QAAQ,CAAC,EAAE;gBAChC,oDAAqB,CAAC,cAAc;gBACpC,oDAAqB,CAAC,QAAQ;gBAC9B,oDAAqB,CAAC,SAAS;aAChC;YACD,CAAC,oDAAqB,CAAC,cAAc,CAAC,EAAE;gBACtC,oDAAqB,CAAC,aAAa;gBACnC,oDAAqB,CAAC,SAAS;gBAC/B,oDAAqB,CAAC,OAAO;aAC9B;YACD,CAAC,oDAAqB,CAAC,aAAa,CAAC,EAAE;gBACrC,oDAAqB,CAAC,QAAQ;gBAC9B,oDAAqB,CAAC,OAAO;gBAC7B,oDAAqB,CAAC,cAAc;aACrC;YACD,CAAC,oDAAqB,CAAC,OAAO,CAAC,EAAE;gBAC/B,oDAAqB,CAAC,aAAa;gBACnC,oDAAqB,CAAC,cAAc;gBACpC,oDAAqB,CAAC,SAAS;aAChC;YACD,CAAC,oDAAqB,CAAC,MAAM,CAAC,EAAE;gBAC9B,oDAAqB,CAAC,YAAY;gBAClC,oDAAqB,CAAC,SAAS;aAChC;YACD,CAAC,oDAAqB,CAAC,OAAO,CAAC,EAAE;gBAC/B,oDAAqB,CAAC,YAAY;gBAClC,oDAAqB,CAAC,SAAS;aAChC;YACD,CAAC,oDAAqB,CAAC,YAAY,CAAC,EAAE;gBACpC,oDAAqB,CAAC,UAAU;gBAChC,oDAAqB,CAAC,MAAM;aAC7B;YACD,CAAC,oDAAqB,CAAC,QAAQ,CAAC,EAAE;gBAChC,oDAAqB,CAAC,QAAQ;gBAC9B,oDAAqB,CAAC,YAAY;aACnC;YACD,8CAA8C;YAC9C,CAAC,oDAAqB,CAAC,QAAQ,CAAC,EAAE,EAAE;YACpC,CAAC,oDAAqB,CAAC,SAAS,CAAC,EAAE,EAAE;YACrC,CAAC,oDAAqB,CAAC,OAAO,CAAC,EAAE,EAAE;SACpC,CAAC;QAEF,OAAO,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC;IAC3E,CAAC;IAEO,uBAAuB,CAAC,MAA6B;QAC3D,OAAO;YACL,oDAAqB,CAAC,WAAW;YACjC,oDAAqB,CAAC,SAAS;YAC/B,oDAAqB,CAAC,WAAW;YACjC,oDAAqB,CAAC,SAAS;YAC/B,oDAAqB,CAAC,YAAY;SACnC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,IAAc;QACpB,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;QACnE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,IAAc;QACvB,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,GAAW,EAAE,KAAU;QAClC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,GAAW;QACzB,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,WAAmB;QACnC,IAAI,WAAW,CAAC,MAAM,GAAG,aAAa,CAAC,sBAAsB,EAAE,CAAC;YAC9D,MAAM,IAAI,KAAK,CAAC,6BAA6B,aAAa,CAAC,sBAAsB,aAAa,CAAC,CAAC;QAClG,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,YAAoB,EAAE,kBAA2B;QAC5D,IAAI,CAAC,YAAY,CAAC,oDAAqB,CAAC,MAAM,EAAE;YAC9C,YAAY;YACZ,kBAAkB;SACnB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,kBAA2B;QACxC,IAAI,CAAC,YAAY,CAAC,oDAAqB,CAAC,QAAQ,EAAE;YAChD,kBAAkB;SACnB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,oBAAoB;QAYlB,OAAO;YACL,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;YACzB,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB;YACvC,WAAW,EAAE,aAAa,CAAC,uBAAuB;YAClD,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE;YACzB,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE;YAC7B,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE;YACjC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE;YAC3C,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,WAAW,EAAE;YAC1D,kBAAkB,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB;YACjD,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY;SACtC,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE;YACtB,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE;YACtC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;YACzB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK;YACvB,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;YACnC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC7B,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;YACnC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU;YACjC,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,EAAE;YAC9C,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE;YAClB,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE;YACxC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE;SACzC,CAAC;IACJ,CAAC;;AAhgBH,sCAigBC;AAhgByB,qCAAuB,GAAG,CAAC,CAAC;AAC5B,8BAAgB,GAAG,GAAG,CAAC;AACvB,oCAAsB,GAAG,IAAI,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\event\\security-event.entity.ts"], "sourcesContent": ["import { BaseAggregateRoot } from '../../../../shared-kernel/domain/base-aggregate-root';\r\nimport { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { EventMetadata } from '../../value-objects/event-metadata/event-metadata.value-object';\r\nimport { EventProcessingStatus } from '../../enums/event-processing-status.enum';\r\nimport { SecurityEventCreatedEvent } from '../../events/security-event-created.event';\r\nimport { SecurityEventStatusChangedEvent } from '../../events/security-event-status-changed.event';\r\n\r\n/**\r\n * Security Event Properties\r\n */\r\nexport interface SecurityEventProps {\r\n  /** Event metadata (timestamp, source, etc.) */\r\n  metadata: EventMetadata;\r\n  /** Current processing status */\r\n  status: EventProcessingStatus;\r\n  /** Raw event data as received */\r\n  rawData: Record<string, any>;\r\n  /** Event title/summary */\r\n  title: string;\r\n  /** Detailed event description */\r\n  description?: string;\r\n  /** Event category */\r\n  category?: string;\r\n  /** Event subcategory */\r\n  subcategory?: string;\r\n  /** Processing error message if failed */\r\n  errorMessage?: string;\r\n  /** Number of processing attempts */\r\n  processingAttempts: number;\r\n  /** When processing was last attempted */\r\n  lastProcessedAt?: Date;\r\n  /** Processing duration in milliseconds */\r\n  processingDuration?: number;\r\n  /** Event tags for classification */\r\n  tags: string[];\r\n  /** Custom attributes */\r\n  attributes: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Security Event Entity\r\n * \r\n * Represents a security event in the system with full lifecycle tracking.\r\n * Serves as the aggregate root for event processing workflows.\r\n * \r\n * Key responsibilities:\r\n * - Event lifecycle management\r\n * - Status transitions and validation\r\n * - Processing attempt tracking\r\n * - Domain event publishing\r\n * - Business rule enforcement\r\n * \r\n * Business Rules:\r\n * - Events can only transition to valid next statuses\r\n * - Failed events can be retried with limits\r\n * - Terminal statuses cannot be changed\r\n * - Processing attempts are tracked and limited\r\n */\r\nexport class SecurityEvent extends BaseAggregateRoot<SecurityEventProps> {\r\n  private static readonly MAX_PROCESSING_ATTEMPTS = 3;\r\n  private static readonly MAX_TITLE_LENGTH = 255;\r\n  private static readonly MAX_DESCRIPTION_LENGTH = 2000;\r\n\r\n  constructor(props: SecurityEventProps, id?: UniqueEntityId) {\r\n    super(props, id);\r\n  }\r\n\r\n  protected validate(): void {\r\n    if (!this.props.metadata) {\r\n      throw new Error('Security event must have metadata');\r\n    }\r\n\r\n    if (!this.props.status) {\r\n      throw new Error('Security event must have a status');\r\n    }\r\n\r\n    if (!Object.values(EventProcessingStatus).includes(this.props.status)) {\r\n      throw new Error(`Invalid event processing status: ${this.props.status}`);\r\n    }\r\n\r\n    if (!this.props.rawData || Object.keys(this.props.rawData).length === 0) {\r\n      throw new Error('Security event must have raw data');\r\n    }\r\n\r\n    if (!this.props.title || this.props.title.trim().length === 0) {\r\n      throw new Error('Security event must have a title');\r\n    }\r\n\r\n    if (this.props.title.length > SecurityEvent.MAX_TITLE_LENGTH) {\r\n      throw new Error(`Event title cannot exceed ${SecurityEvent.MAX_TITLE_LENGTH} characters`);\r\n    }\r\n\r\n    if (this.props.description && this.props.description.length > SecurityEvent.MAX_DESCRIPTION_LENGTH) {\r\n      throw new Error(`Event description cannot exceed ${SecurityEvent.MAX_DESCRIPTION_LENGTH} characters`);\r\n    }\r\n\r\n    if (this.props.processingAttempts < 0) {\r\n      throw new Error('Processing attempts cannot be negative');\r\n    }\r\n\r\n    if (this.props.processingAttempts > SecurityEvent.MAX_PROCESSING_ATTEMPTS) {\r\n      throw new Error(`Processing attempts cannot exceed ${SecurityEvent.MAX_PROCESSING_ATTEMPTS}`);\r\n    }\r\n\r\n    if (this.props.processingDuration !== undefined && this.props.processingDuration < 0) {\r\n      throw new Error('Processing duration cannot be negative');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create a new security event\r\n   */\r\n  static create(\r\n    metadata: EventMetadata,\r\n    rawData: Record<string, any>,\r\n    title: string,\r\n    options?: {\r\n      description?: string;\r\n      category?: string;\r\n      subcategory?: string;\r\n      tags?: string[];\r\n      attributes?: Record<string, any>;\r\n    }\r\n  ): SecurityEvent {\r\n    const props: SecurityEventProps = {\r\n      metadata,\r\n      status: EventProcessingStatus.RAW,\r\n      rawData,\r\n      title: title.trim(),\r\n      description: options?.description?.trim(),\r\n      category: options?.category?.trim(),\r\n      subcategory: options?.subcategory?.trim(),\r\n      processingAttempts: 0,\r\n      tags: options?.tags || [],\r\n      attributes: options?.attributes || {},\r\n    };\r\n\r\n    const event = new SecurityEvent(props);\r\n    \r\n    // Publish domain event\r\n    event.addDomainEvent(new SecurityEventCreatedEvent(\r\n      event.id,\r\n      {\r\n        eventId: event.id.toString(),\r\n        title: event.props.title,\r\n        category: event.props.category,\r\n        sourceType: event.props.metadata.source.type,\r\n        sourceIdentifier: event.props.metadata.source.identifier,\r\n        timestamp: event.props.metadata.timestamp.toISOString(),\r\n      }\r\n    ));\r\n\r\n    return event;\r\n  }\r\n\r\n  /**\r\n   * Get event metadata\r\n   */\r\n  get metadata(): EventMetadata {\r\n    return this.props.metadata;\r\n  }\r\n\r\n  /**\r\n   * Get current status\r\n   */\r\n  get status(): EventProcessingStatus {\r\n    return this.props.status;\r\n  }\r\n\r\n  /**\r\n   * Get raw event data\r\n   */\r\n  get rawData(): Record<string, any> {\r\n    return { ...this.props.rawData };\r\n  }\r\n\r\n  /**\r\n   * Get event title\r\n   */\r\n  get title(): string {\r\n    return this.props.title;\r\n  }\r\n\r\n  /**\r\n   * Get event description\r\n   */\r\n  get description(): string | undefined {\r\n    return this.props.description;\r\n  }\r\n\r\n  /**\r\n   * Get event category\r\n   */\r\n  get category(): string | undefined {\r\n    return this.props.category;\r\n  }\r\n\r\n  /**\r\n   * Get event subcategory\r\n   */\r\n  get subcategory(): string | undefined {\r\n    return this.props.subcategory;\r\n  }\r\n\r\n  /**\r\n   * Get error message\r\n   */\r\n  get errorMessage(): string | undefined {\r\n    return this.props.errorMessage;\r\n  }\r\n\r\n  /**\r\n   * Get processing attempts count\r\n   */\r\n  get processingAttempts(): number {\r\n    return this.props.processingAttempts;\r\n  }\r\n\r\n  /**\r\n   * Get last processed timestamp\r\n   */\r\n  get lastProcessedAt(): Date | undefined {\r\n    return this.props.lastProcessedAt;\r\n  }\r\n\r\n  /**\r\n   * Get processing duration\r\n   */\r\n  get processingDuration(): number | undefined {\r\n    return this.props.processingDuration;\r\n  }\r\n\r\n  /**\r\n   * Get event tags\r\n   */\r\n  get tags(): string[] {\r\n    return [...this.props.tags];\r\n  }\r\n\r\n  /**\r\n   * Get event attributes\r\n   */\r\n  get attributes(): Record<string, any> {\r\n    return { ...this.props.attributes };\r\n  }\r\n\r\n  /**\r\n   * Check if event is in terminal status\r\n   */\r\n  isTerminal(): boolean {\r\n    const terminalStatuses = [\r\n      EventProcessingStatus.RESOLVED,\r\n      EventProcessingStatus.ARCHIVED,\r\n      EventProcessingStatus.FAILED,\r\n      EventProcessingStatus.DISCARDED,\r\n      EventProcessingStatus.SKIPPED,\r\n      EventProcessingStatus.TIMEOUT,\r\n    ];\r\n    return terminalStatuses.includes(this.props.status);\r\n  }\r\n\r\n  /**\r\n   * Check if event is in progress\r\n   */\r\n  isInProgress(): boolean {\r\n    const inProgressStatuses = [\r\n      EventProcessingStatus.NORMALIZING,\r\n      EventProcessingStatus.ENRICHING,\r\n      EventProcessingStatus.CORRELATING,\r\n      EventProcessingStatus.ANALYZING,\r\n      EventProcessingStatus.INVESTIGATING,\r\n      EventProcessingStatus.REPROCESSING,\r\n    ];\r\n    return inProgressStatuses.includes(this.props.status);\r\n  }\r\n\r\n  /**\r\n   * Check if event requires attention\r\n   */\r\n  requiresAttention(): boolean {\r\n    const attentionStatuses = [\r\n      EventProcessingStatus.PENDING_REVIEW,\r\n      EventProcessingStatus.INVESTIGATING,\r\n      EventProcessingStatus.FAILED,\r\n      EventProcessingStatus.ON_HOLD,\r\n      EventProcessingStatus.TIMEOUT,\r\n    ];\r\n    return attentionStatuses.includes(this.props.status);\r\n  }\r\n\r\n  /**\r\n   * Check if event can be retried\r\n   */\r\n  canRetry(): boolean {\r\n    return this.props.status === EventProcessingStatus.FAILED &&\r\n           this.props.processingAttempts < SecurityEvent.MAX_PROCESSING_ATTEMPTS;\r\n  }\r\n\r\n  /**\r\n   * Check if event has specific tag\r\n   */\r\n  hasTag(tag: string): boolean {\r\n    return this.props.tags.includes(tag);\r\n  }\r\n\r\n  /**\r\n   * Get attribute value\r\n   */\r\n  getAttribute<T = any>(key: string): T | undefined {\r\n    return this.props.attributes[key] as T;\r\n  }\r\n\r\n  /**\r\n   * Change event status\r\n   */\r\n  changeStatus(\r\n    newStatus: EventProcessingStatus,\r\n    options?: {\r\n      errorMessage?: string;\r\n      processingDuration?: number;\r\n    }\r\n  ): void {\r\n    if (this.isTerminal()) {\r\n      throw new Error(`Cannot change status from terminal state: ${this.props.status}`);\r\n    }\r\n\r\n    if (!this.isValidStatusTransition(newStatus)) {\r\n      throw new Error(`Invalid status transition from ${this.props.status} to ${newStatus}`);\r\n    }\r\n\r\n    const oldStatus = this.props.status;\r\n    this.props.status = newStatus;\r\n    this.props.lastProcessedAt = new Date();\r\n\r\n    if (options?.errorMessage) {\r\n      this.props.errorMessage = options.errorMessage;\r\n    }\r\n\r\n    if (options?.processingDuration !== undefined) {\r\n      this.props.processingDuration = options.processingDuration;\r\n    }\r\n\r\n    // Increment processing attempts for certain statuses\r\n    if (this.shouldIncrementAttempts(newStatus)) {\r\n      this.props.processingAttempts++;\r\n    }\r\n\r\n    // Publish status change event\r\n    this.addDomainEvent(new SecurityEventStatusChangedEvent(\r\n      this.id,\r\n      {\r\n        eventId: this.id.toString(),\r\n        oldStatus,\r\n        newStatus,\r\n        processingAttempts: this.props.processingAttempts,\r\n        errorMessage: options?.errorMessage,\r\n        timestamp: new Date().toISOString(),\r\n      }\r\n    ));\r\n  }\r\n\r\n  private isValidStatusTransition(newStatus: EventProcessingStatus): boolean {\r\n    const validTransitions: Record<EventProcessingStatus, EventProcessingStatus[]> = {\r\n      [EventProcessingStatus.RAW]: [\r\n        EventProcessingStatus.NORMALIZING,\r\n        EventProcessingStatus.SKIPPED,\r\n        EventProcessingStatus.FAILED,\r\n      ],\r\n      [EventProcessingStatus.NORMALIZING]: [\r\n        EventProcessingStatus.NORMALIZED,\r\n        EventProcessingStatus.FAILED,\r\n        EventProcessingStatus.TIMEOUT,\r\n      ],\r\n      [EventProcessingStatus.NORMALIZED]: [\r\n        EventProcessingStatus.ENRICHING,\r\n        EventProcessingStatus.SKIPPED,\r\n        EventProcessingStatus.FAILED,\r\n      ],\r\n      [EventProcessingStatus.ENRICHING]: [\r\n        EventProcessingStatus.ENRICHED,\r\n        EventProcessingStatus.FAILED,\r\n        EventProcessingStatus.TIMEOUT,\r\n      ],\r\n      [EventProcessingStatus.ENRICHED]: [\r\n        EventProcessingStatus.CORRELATING,\r\n        EventProcessingStatus.ANALYZING,\r\n        EventProcessingStatus.FAILED,\r\n      ],\r\n      [EventProcessingStatus.CORRELATING]: [\r\n        EventProcessingStatus.CORRELATED,\r\n        EventProcessingStatus.FAILED,\r\n        EventProcessingStatus.TIMEOUT,\r\n      ],\r\n      [EventProcessingStatus.CORRELATED]: [\r\n        EventProcessingStatus.ANALYZING,\r\n        EventProcessingStatus.FAILED,\r\n      ],\r\n      [EventProcessingStatus.ANALYZING]: [\r\n        EventProcessingStatus.ANALYZED,\r\n        EventProcessingStatus.FAILED,\r\n        EventProcessingStatus.TIMEOUT,\r\n      ],\r\n      [EventProcessingStatus.ANALYZED]: [\r\n        EventProcessingStatus.PENDING_REVIEW,\r\n        EventProcessingStatus.RESOLVED,\r\n        EventProcessingStatus.DISCARDED,\r\n      ],\r\n      [EventProcessingStatus.PENDING_REVIEW]: [\r\n        EventProcessingStatus.INVESTIGATING,\r\n        EventProcessingStatus.DISCARDED,\r\n        EventProcessingStatus.ON_HOLD,\r\n      ],\r\n      [EventProcessingStatus.INVESTIGATING]: [\r\n        EventProcessingStatus.RESOLVED,\r\n        EventProcessingStatus.ON_HOLD,\r\n        EventProcessingStatus.PENDING_REVIEW,\r\n      ],\r\n      [EventProcessingStatus.ON_HOLD]: [\r\n        EventProcessingStatus.INVESTIGATING,\r\n        EventProcessingStatus.PENDING_REVIEW,\r\n        EventProcessingStatus.DISCARDED,\r\n      ],\r\n      [EventProcessingStatus.FAILED]: [\r\n        EventProcessingStatus.REPROCESSING,\r\n        EventProcessingStatus.DISCARDED,\r\n      ],\r\n      [EventProcessingStatus.TIMEOUT]: [\r\n        EventProcessingStatus.REPROCESSING,\r\n        EventProcessingStatus.DISCARDED,\r\n      ],\r\n      [EventProcessingStatus.REPROCESSING]: [\r\n        EventProcessingStatus.NORMALIZED,\r\n        EventProcessingStatus.FAILED,\r\n      ],\r\n      [EventProcessingStatus.RESOLVED]: [\r\n        EventProcessingStatus.ARCHIVED,\r\n        EventProcessingStatus.REPROCESSING,\r\n      ],\r\n      // Terminal statuses have no valid transitions\r\n      [EventProcessingStatus.ARCHIVED]: [],\r\n      [EventProcessingStatus.DISCARDED]: [],\r\n      [EventProcessingStatus.SKIPPED]: [],\r\n    };\r\n\r\n    return validTransitions[this.props.status]?.includes(newStatus) || false;\r\n  }\r\n\r\n  private shouldIncrementAttempts(status: EventProcessingStatus): boolean {\r\n    return [\r\n      EventProcessingStatus.NORMALIZING,\r\n      EventProcessingStatus.ENRICHING,\r\n      EventProcessingStatus.CORRELATING,\r\n      EventProcessingStatus.ANALYZING,\r\n      EventProcessingStatus.REPROCESSING,\r\n    ].includes(status);\r\n  }\r\n\r\n  /**\r\n   * Add tags to the event\r\n   */\r\n  addTags(tags: string[]): void {\r\n    const newTags = tags.filter(tag => !this.props.tags.includes(tag));\r\n    this.props.tags.push(...newTags);\r\n  }\r\n\r\n  /**\r\n   * Remove tags from the event\r\n   */\r\n  removeTags(tags: string[]): void {\r\n    this.props.tags = this.props.tags.filter(tag => !tags.includes(tag));\r\n  }\r\n\r\n  /**\r\n   * Set attribute value\r\n   */\r\n  setAttribute(key: string, value: any): void {\r\n    this.props.attributes[key] = value;\r\n  }\r\n\r\n  /**\r\n   * Remove attribute\r\n   */\r\n  removeAttribute(key: string): void {\r\n    delete this.props.attributes[key];\r\n  }\r\n\r\n  /**\r\n   * Update event description\r\n   */\r\n  updateDescription(description: string): void {\r\n    if (description.length > SecurityEvent.MAX_DESCRIPTION_LENGTH) {\r\n      throw new Error(`Description cannot exceed ${SecurityEvent.MAX_DESCRIPTION_LENGTH} characters`);\r\n    }\r\n    this.props.description = description.trim();\r\n  }\r\n\r\n  /**\r\n   * Mark event as failed\r\n   */\r\n  markAsFailed(errorMessage: string, processingDuration?: number): void {\r\n    this.changeStatus(EventProcessingStatus.FAILED, {\r\n      errorMessage,\r\n      processingDuration,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Mark event as resolved\r\n   */\r\n  markAsResolved(processingDuration?: number): void {\r\n    this.changeStatus(EventProcessingStatus.RESOLVED, {\r\n      processingDuration,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get event age in milliseconds\r\n   */\r\n  getAge(): number {\r\n    return this.props.metadata.timestamp.getAge();\r\n  }\r\n\r\n  /**\r\n   * Get processing summary\r\n   */\r\n  getProcessingSummary(): {\r\n    status: string;\r\n    attempts: number;\r\n    maxAttempts: number;\r\n    canRetry: boolean;\r\n    isTerminal: boolean;\r\n    isInProgress: boolean;\r\n    requiresAttention: boolean;\r\n    lastProcessedAt?: string;\r\n    processingDuration?: number;\r\n    errorMessage?: string;\r\n  } {\r\n    return {\r\n      status: this.props.status,\r\n      attempts: this.props.processingAttempts,\r\n      maxAttempts: SecurityEvent.MAX_PROCESSING_ATTEMPTS,\r\n      canRetry: this.canRetry(),\r\n      isTerminal: this.isTerminal(),\r\n      isInProgress: this.isInProgress(),\r\n      requiresAttention: this.requiresAttention(),\r\n      lastProcessedAt: this.props.lastProcessedAt?.toISOString(),\r\n      processingDuration: this.props.processingDuration,\r\n      errorMessage: this.props.errorMessage,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      id: this.id.toString(),\r\n      metadata: this.props.metadata.toJSON(),\r\n      status: this.props.status,\r\n      title: this.props.title,\r\n      description: this.props.description,\r\n      category: this.props.category,\r\n      subcategory: this.props.subcategory,\r\n      tags: this.props.tags,\r\n      attributes: this.props.attributes,\r\n      processingSummary: this.getProcessingSummary(),\r\n      age: this.getAge(),\r\n      createdAt: this.createdAt?.toISOString(),\r\n      updatedAt: this.updatedAt?.toISOString(),\r\n    };\r\n  }\r\n}\r\n"], "version": 3}