{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\auth\\auth.service.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,2CAAgG;AAChG,2CAA+C;AAC/C,6CAAmD;AACnD,qCAAqC;AACrC,kEAA8D;AAC9D,4DAAwD;AACxD,sDAAkD;AAElD,gDAAgD;AAChD,2FAAiF;AACjF,2FAAiF;AACjF,6GAAkG;AAElG;;;GAGG;AAEI,IAAM,WAAW,mBAAjB,MAAM,WAAW;IAGtB,YAEE,cAAiD,EAEjD,cAAiD,EAEjD,sBAAiE,EAChD,eAAgC,EAChC,YAA0B,EAC1B,WAAwB,EACxB,aAA4B;QAR5B,mBAAc,GAAd,cAAc,CAAkB;QAEhC,mBAAc,GAAd,cAAc,CAAkB;QAEhC,2BAAsB,GAAtB,sBAAsB,CAA0B;QAChD,oBAAe,GAAf,eAAe,CAAiB;QAChC,iBAAY,GAAZ,YAAY,CAAc;QAC1B,gBAAW,GAAX,WAAW,CAAa;QACxB,kBAAa,GAAb,aAAa,CAAe;QAZ9B,WAAM,GAAG,IAAI,eAAM,CAAC,aAAW,CAAC,IAAI,CAAC,CAAC;IAapD,CAAC;IAEJ;;;;;OAKG;IACH,KAAK,CAAC,YAAY,CAAC,KAAa,EAAE,QAAgB;QAChD,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAE5D,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBAC7C,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,WAAW,EAAE,EAAE;gBACrC,SAAS,EAAE,CAAC,OAAO,CAAC;aACrB,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;gBAC9C,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACnB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;gBACzE,MAAM,IAAI,8BAAqB,CAAC,qBAAqB,CAAC,CAAC;YACzD,CAAC;YAED,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,EAAE,CAAC;gBACtD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE;oBACzC,KAAK;oBACL,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,WAAW,EAAE,IAAI,CAAC,WAAW;iBAC9B,CAAC,CAAC;gBACH,MAAM,IAAI,8BAAqB,CAAC,+BAA+B,CAAC,CAAC;YACnE,CAAC;YAED,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,gBAAgB,CACjE,QAAQ,EACR,IAAI,CAAC,YAAY,CAClB,CAAC;YAEF,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;gBACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;gBACzE,OAAO,IAAI,CAAC;YACd,CAAC;YAED,uDAAuD;YACvD,IAAI,IAAI,CAAC,mBAAmB,GAAG,CAAC,EAAE,CAAC;gBACjC,MAAM,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;YAC5C,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,yCAAyC,EAAE;gBACzD,KAAK;gBACL,MAAM,EAAE,IAAI,CAAC,EAAE;aAChB,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE;gBACrD,KAAK;gBACL,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,KAAK,CACT,IAAU,EACV,SAAkB,EAClB,SAAkB;QAOlB,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE;gBACpD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,KAAK,EAAE,IAAI,CAAC,KAAK;aAClB,CAAC,CAAC;YAEH,uBAAuB;YACvB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAEvE,wBAAwB;YACxB,MAAM,OAAO,GAAG;gBACd,GAAG,EAAE,IAAI,CAAC,EAAE;gBACZ,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;gBAC/C,WAAW;aACZ,CAAC;YAEF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;YACzE,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAE3E,oBAAoB;YACpB,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;YAEvD,iCAAiC;YACjC,MAAM,EAAE,YAAY,EAAE,GAAG,YAAY,EAAE,GAAG,IAAI,CAAC;YAE/C,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAClD,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAErE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iCAAiC,EAAE;gBACjD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,SAAS;aACV,CAAC,CAAC;YAEH,OAAO;gBACL,WAAW;gBACX,YAAY;gBACZ,IAAI,EAAE,YAAY;gBAClB,SAAS;aACV,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE;gBACpD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,aAAa,CAAC,YAAoB;QAKtC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;YAEtD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;gBAC5D,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE;gBAC3E,SAAS,EAAE,CAAC,MAAM,EAAE,YAAY,CAAC;aAClC,CAAC,CAAC;YAEH,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,SAAS,IAAI,WAAW,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,EAAE,CAAC;gBAChF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;gBACrD,MAAM,IAAI,8BAAqB,CAAC,uBAAuB,CAAC,CAAC;YAC3D,CAAC;YAED,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;YAE9B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACnB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;gBAClE,MAAM,IAAI,8BAAqB,CAAC,qBAAqB,CAAC,CAAC;YACzD,CAAC;YAED,2BAA2B;YAC3B,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAE9C,sBAAsB;YACtB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAEvE,MAAM,OAAO,GAAG;gBACd,GAAG,EAAE,IAAI,CAAC,EAAE;gBACZ,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;gBAC/C,WAAW;aACZ,CAAC;YAEF,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;YAC5E,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAE9E,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAClD,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAErE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;YAEtE,OAAO;gBACL,WAAW,EAAE,cAAc;gBAC3B,YAAY,EAAE,eAAe;gBAC7B,SAAS;aACV,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE;gBAC3C,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,MAAM,CAAC,YAAoB;QAC/B,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;YAEtC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;gBAC5D,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE;aAC5E,CAAC,CAAC;YAEH,IAAI,WAAW,EAAE,CAAC;gBAChB,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;gBAC9C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,8BAA8B,EAAE;oBAC9C,OAAO,EAAE,WAAW,CAAC,EAAE;iBACxB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE;gBACvC,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,kBAAkB,CAAC,OAAY;QACnC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBAC7C,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,GAAG,EAAE;gBAC1B,SAAS,EAAE,CAAC,OAAO,CAAC;aACrB,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC5B,MAAM,IAAI,8BAAqB,CAAC,4BAA4B,CAAC,CAAC;YAChE,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;gBAChD,OAAO;gBACP,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,IAAI,8BAAqB,CAAC,eAAe,CAAC,CAAC;QACnD,CAAC;IACH,CAAC;IAED;;;;OAIG;IACK,KAAK,CAAC,iBAAiB,CAAC,IAAU;QACxC,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAClD,MAAM,WAAW,GAAG,UAAU,CAAC,QAAQ,CAAC,gBAAgB,CAAC;QACzD,MAAM,eAAe,GAAG,UAAU,CAAC,QAAQ,CAAC,eAAe,CAAC;QAE5D,IAAI,CAAC,mBAAmB,IAAI,CAAC,CAAC;QAE9B,IAAI,IAAI,CAAC,mBAAmB,IAAI,WAAW,EAAE,CAAC;YAC5C,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,eAAe,CAAC,CAAC;YAC1D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kDAAkD,EAAE;gBACnE,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,QAAQ,EAAE,IAAI,CAAC,mBAAmB;gBAClC,WAAW,EAAE,IAAI,CAAC,WAAW;aAC9B,CAAC,CAAC;QACL,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAED;;;;OAIG;IACK,KAAK,CAAC,wBAAwB,CAAC,IAAU;QAC/C,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAED;;;;;;OAMG;IACK,KAAK,CAAC,eAAe,CAC3B,IAAU,EACV,SAAkB,EAClB,SAAkB;QAElB,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QAC9B,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAErC,4BAA4B;QAC5B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qBAAqB,EAAE;YACrC,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,SAAS;YACT,SAAS;YACT,SAAS,EAAE,IAAI,CAAC,WAAW;SAC5B,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACK,KAAK,CAAC,kBAAkB,CAAC,OAAe;QAC9C,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IACzE,CAAC;IAED;;;;OAIG;IACK,mBAAmB,CAAC,SAAiB;QAC3C,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;QACjD,IAAI,CAAC,KAAK;YAAE,OAAO,IAAI,CAAC,CAAC,oBAAoB;QAE7C,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAEtB,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,GAAG,CAAC,CAAC,OAAO,KAAK,CAAC;YACvB,KAAK,GAAG,CAAC,CAAC,OAAO,KAAK,GAAG,EAAE,CAAC;YAC5B,KAAK,GAAG,CAAC,CAAC,OAAO,KAAK,GAAG,IAAI,CAAC;YAC9B,KAAK,GAAG,CAAC,CAAC,OAAO,KAAK,GAAG,KAAK,CAAC;YAC/B,OAAO,CAAC,CAAC,OAAO,IAAI,CAAC;QACvB,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,qBAAqB,CAAC,SAQ3B;QACC,IAAI,CAAC;YACH,8BAA8B;YAC9B,IAAI,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBAC3C,KAAK,EAAE,EAAE,KAAK,EAAE,SAAS,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE;gBAC/C,SAAS,EAAE,CAAC,OAAO,CAAC;aACrB,CAAC,CAAC;YAEH,IAAI,IAAI,EAAE,CAAC;gBACT,6BAA6B;gBAC7B,IAAI,CAAC,QAAQ,GAAG;oBACd,GAAG,IAAI,CAAC,QAAQ;oBAChB,KAAK,EAAE;wBACL,GAAG,IAAI,CAAC,QAAQ,EAAE,KAAK;wBACvB,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE;4BACpB,UAAU,EAAE,SAAS,CAAC,UAAU;4BAChC,SAAS,EAAE,IAAI,IAAI,EAAE;4BACrB,YAAY,EAAE,SAAS,CAAC,YAAY;yBACrC;qBACF;iBACF,CAAC;gBAEF,sDAAsD;gBACtD,IAAI,SAAS,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;oBAC3C,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;gBACvC,CAAC;gBAED,yCAAyC;gBACzC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;oBACxB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;oBAC1B,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;gBACzB,CAAC;gBAED,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACrC,OAAO,IAAI,CAAC;YACd,CAAC;YAED,4BAA4B;YAC5B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;YAEhD,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;gBAChC,KAAK,EAAE,SAAS,CAAC,KAAK,CAAC,WAAW,EAAE;gBACpC,SAAS,EAAE,SAAS,CAAC,SAAS;gBAC9B,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,SAAS,EAAE,SAAS,CAAC,SAAS;gBAC9B,aAAa,EAAE,IAAI;gBACnB,MAAM,EAAE,QAAQ;gBAChB,YAAY,EAAE,EAAE,EAAE,mCAAmC;gBACrD,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;gBACvC,QAAQ,EAAE;oBACR,KAAK,EAAE;wBACL,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE;4BACpB,UAAU,EAAE,SAAS,CAAC,UAAU;4BAChC,SAAS,EAAE,IAAI,IAAI,EAAE;4BACrB,YAAY,EAAE,SAAS,CAAC,YAAY;yBACrC;qBACF;iBACF;aACF,CAAC,CAAC;YAEH,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE;gBACxD,KAAK,EAAE,SAAS,CAAC,KAAK;gBACtB,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,oBAAoB,CAAC,QAQ1B;QACC,IAAI,CAAC;YACH,8BAA8B;YAC9B,IAAI,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBAC3C,KAAK,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE;gBAC9C,SAAS,EAAE,CAAC,OAAO,CAAC;aACrB,CAAC,CAAC;YAEH,IAAI,IAAI,EAAE,CAAC;gBACT,4BAA4B;gBAC5B,IAAI,CAAC,QAAQ,GAAG;oBACd,GAAG,IAAI,CAAC,QAAQ;oBAChB,IAAI,EAAE;wBACJ,GAAG,IAAI,CAAC,QAAQ,EAAE,IAAI;wBACtB,UAAU,EAAE,QAAQ,CAAC,UAAU;wBAC/B,SAAS,EAAE,IAAI,IAAI,EAAE;wBACrB,YAAY,EAAE,QAAQ,CAAC,YAAY;qBACpC;iBACF,CAAC;gBAEF,wCAAwC;gBACxC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;oBACxB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;oBAC1B,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;gBACzB,CAAC;gBAED,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACrC,OAAO,IAAI,CAAC;YACd,CAAC;YAED,2BAA2B;YAC3B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;YAEhD,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;gBAChC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE;gBACnC,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,aAAa,EAAE,IAAI;gBACnB,MAAM,EAAE,QAAQ;gBAChB,YAAY,EAAE,EAAE,EAAE,kCAAkC;gBACpD,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;gBACvC,QAAQ,EAAE;oBACR,IAAI,EAAE;wBACJ,UAAU,EAAE,QAAQ,CAAC,UAAU;wBAC/B,SAAS,EAAE,IAAI,IAAI,EAAE;wBACrB,YAAY,EAAE,QAAQ,CAAC,YAAY;qBACpC;iBACF;aACF,CAAC,CAAC;YAEH,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE;gBACvD,KAAK,EAAE,QAAQ,CAAC,KAAK;gBACrB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,oBAAoB,CAAC,MAAc;QACvC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc;iBACpC,kBAAkB,CAAC,MAAM,CAAC;iBAC1B,KAAK,CAAC,iDAAiD,EAAE,EAAE,MAAM,EAAE,CAAC;iBACpE,OAAO,EAAE,CAAC;YAEb,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE;gBACrD,MAAM;gBACN,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,mBAAmB,CAAC,MAAc,EAAE,MAAe;QACvD,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,CACrD,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,EAC5B;gBACE,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,aAAa,EAAE,MAAM,IAAI,mBAAmB;aAC7C,CACF,CAAC;YAEF,OAAO,MAAM,CAAC,QAAQ,IAAI,CAAC,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;gBAClD,MAAM;gBACN,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,OAAO,CAAC,CAAC;QACX,CAAC;IACH,CAAC;IAED;;;OAGG;IACK,KAAK,CAAC,cAAc;QAC1B,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBACvC,KAAK,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE;aAC3C,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;gBAC9C,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AApkBY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,kBAAI,CAAC,CAAA;IAEtB,WAAA,IAAA,0BAAgB,EAAC,kBAAI,CAAC,CAAA;IAEtB,WAAA,IAAA,0BAAgB,EAAC,mCAAY,CAAC,CAAA;yDAHE,oBAAU,oBAAV,oBAAU,oDAEV,oBAAU,oBAAV,oBAAU,oDAEF,oBAAU,oBAAV,oBAAU,oDACjB,kCAAe,oBAAf,kCAAe,oDAClB,4BAAY,oBAAZ,4BAAY,oDACb,0BAAW,oBAAX,0BAAW,oDACT,sBAAa,oBAAb,sBAAa;GAbpC,WAAW,CAokBvB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\auth\\auth.service.ts"], "sourcesContent": ["import { Injectable, UnauthorizedException, Logger, BadRequestException } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\nimport { PasswordService } from './services/password.service';\r\nimport { TokenService } from './services/token.service';\r\nimport { RbacService } from './rbac/rbac.service';\r\n\r\n// Import entities (these will be created later)\r\nimport { User } from '../../modules/user-management/domain/entities/user.entity';\r\nimport { Role } from '../../modules/user-management/domain/entities/role.entity';\r\nimport { RefreshToken } from '../../modules/user-management/domain/entities/refresh-token.entity';\r\n\r\n/**\r\n * Authentication service that handles user authentication and authorization\r\n * Provides methods for login, logout, token management, and user validation\r\n */\r\n@Injectable()\r\nexport class AuthService {\r\n  private readonly logger = new Logger(AuthService.name);\r\n\r\n  constructor(\r\n    @InjectRepository(User)\r\n    private readonly userRepository: Repository<User>,\r\n    @InjectRepository(Role)\r\n    private readonly roleRepository: Repository<Role>,\r\n    @InjectRepository(RefreshToken)\r\n    private readonly refreshTokenRepository: Repository<RefreshToken>,\r\n    private readonly passwordService: PasswordService,\r\n    private readonly tokenService: TokenService,\r\n    private readonly rbacService: RbacService,\r\n    private readonly configService: ConfigService,\r\n  ) {}\r\n\r\n  /**\r\n   * Validate user credentials for local authentication\r\n   * @param email User email\r\n   * @param password Plain text password\r\n   * @returns Promise<User | null> User object if valid, null otherwise\r\n   */\r\n  async validateUser(email: string, password: string): Promise<User | null> {\r\n    try {\r\n      this.logger.debug('Validating user credentials', { email });\r\n\r\n      const user = await this.userRepository.findOne({\r\n        where: { email: email.toLowerCase() },\r\n        relations: ['roles'],\r\n      });\r\n\r\n      if (!user) {\r\n        this.logger.warn('User not found', { email });\r\n        return null;\r\n      }\r\n\r\n      if (!user.isActive) {\r\n        this.logger.warn('User account is inactive', { email, userId: user.id });\r\n        throw new UnauthorizedException('Account is inactive');\r\n      }\r\n\r\n      if (user.lockedUntil && user.lockedUntil > new Date()) {\r\n        this.logger.warn('User account is locked', { \r\n          email, \r\n          userId: user.id, \r\n          lockedUntil: user.lockedUntil \r\n        });\r\n        throw new UnauthorizedException('Account is temporarily locked');\r\n      }\r\n\r\n      const isPasswordValid = await this.passwordService.validatePassword(\r\n        password,\r\n        user.passwordHash,\r\n      );\r\n\r\n      if (!isPasswordValid) {\r\n        await this.handleFailedLogin(user);\r\n        this.logger.warn('Invalid password attempt', { email, userId: user.id });\r\n        return null;\r\n      }\r\n\r\n      // Reset failed login attempts on successful validation\r\n      if (user.failedLoginAttempts > 0) {\r\n        await this.resetFailedLoginAttempts(user);\r\n      }\r\n\r\n      this.logger.log('User credentials validated successfully', { \r\n        email, \r\n        userId: user.id \r\n      });\r\n\r\n      return user;\r\n    } catch (error) {\r\n      this.logger.error('Error validating user credentials', {\r\n        email,\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Authenticate user and generate tokens\r\n   * @param user User object\r\n   * @param ipAddress Client IP address\r\n   * @param userAgent Client user agent\r\n   * @returns Promise<AuthResult> Authentication result with tokens\r\n   */\r\n  async login(\r\n    user: User,\r\n    ipAddress?: string,\r\n    userAgent?: string,\r\n  ): Promise<{\r\n    accessToken: string;\r\n    refreshToken: string;\r\n    user: Partial<User>;\r\n    expiresIn: number;\r\n  }> {\r\n    try {\r\n      this.logger.debug('Generating authentication tokens', { \r\n        userId: user.id,\r\n        email: user.email \r\n      });\r\n\r\n      // Get user permissions\r\n      const permissions = await this.rbacService.getUserPermissions(user.id);\r\n\r\n      // Generate access token\r\n      const payload = {\r\n        sub: user.id,\r\n        email: user.email,\r\n        roles: user.roles?.map(role => role.name) || [],\r\n        permissions,\r\n      };\r\n\r\n      const accessToken = await this.tokenService.generateAccessToken(payload);\r\n      const refreshToken = await this.tokenService.generateRefreshToken(user.id);\r\n\r\n      // Update last login\r\n      await this.updateLastLogin(user, ipAddress, userAgent);\r\n\r\n      // Clean user object for response\r\n      const { passwordHash, ...userResponse } = user;\r\n\r\n      const authConfig = this.configService.get('auth');\r\n      const expiresIn = this.parseExpirationTime(authConfig.jwt.expiresIn);\r\n\r\n      this.logger.log('User authenticated successfully', {\r\n        userId: user.id,\r\n        email: user.email,\r\n        ipAddress,\r\n      });\r\n\r\n      return {\r\n        accessToken,\r\n        refreshToken,\r\n        user: userResponse,\r\n        expiresIn,\r\n      };\r\n    } catch (error) {\r\n      this.logger.error('Error during user authentication', {\r\n        userId: user.id,\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Refresh access token using refresh token\r\n   * @param refreshToken Refresh token string\r\n   * @returns Promise<AuthResult> New authentication tokens\r\n   */\r\n  async refreshTokens(refreshToken: string): Promise<{\r\n    accessToken: string;\r\n    refreshToken: string;\r\n    expiresIn: number;\r\n  }> {\r\n    try {\r\n      this.logger.debug('Refreshing authentication tokens');\r\n\r\n      const tokenRecord = await this.refreshTokenRepository.findOne({\r\n        where: { tokenHash: await this.passwordService.hashPassword(refreshToken) },\r\n        relations: ['user', 'user.roles'],\r\n      });\r\n\r\n      if (!tokenRecord || tokenRecord.isRevoked || tokenRecord.expiresAt < new Date()) {\r\n        this.logger.warn('Invalid or expired refresh token');\r\n        throw new UnauthorizedException('Invalid refresh token');\r\n      }\r\n\r\n      const user = tokenRecord.user;\r\n\r\n      if (!user.isActive) {\r\n        this.logger.warn('User account is inactive', { userId: user.id });\r\n        throw new UnauthorizedException('Account is inactive');\r\n      }\r\n\r\n      // Revoke old refresh token\r\n      await this.revokeRefreshToken(tokenRecord.id);\r\n\r\n      // Generate new tokens\r\n      const permissions = await this.rbacService.getUserPermissions(user.id);\r\n      \r\n      const payload = {\r\n        sub: user.id,\r\n        email: user.email,\r\n        roles: user.roles?.map(role => role.name) || [],\r\n        permissions,\r\n      };\r\n\r\n      const newAccessToken = await this.tokenService.generateAccessToken(payload);\r\n      const newRefreshToken = await this.tokenService.generateRefreshToken(user.id);\r\n\r\n      const authConfig = this.configService.get('auth');\r\n      const expiresIn = this.parseExpirationTime(authConfig.jwt.expiresIn);\r\n\r\n      this.logger.log('Tokens refreshed successfully', { userId: user.id });\r\n\r\n      return {\r\n        accessToken: newAccessToken,\r\n        refreshToken: newRefreshToken,\r\n        expiresIn,\r\n      };\r\n    } catch (error) {\r\n      this.logger.error('Error refreshing tokens', {\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Logout user and revoke refresh token\r\n   * @param refreshToken Refresh token to revoke\r\n   * @returns Promise<void>\r\n   */\r\n  async logout(refreshToken: string): Promise<void> {\r\n    try {\r\n      this.logger.debug('Logging out user');\r\n\r\n      const tokenRecord = await this.refreshTokenRepository.findOne({\r\n        where: { tokenHash: await this.passwordService.hashPassword(refreshToken) },\r\n      });\r\n\r\n      if (tokenRecord) {\r\n        await this.revokeRefreshToken(tokenRecord.id);\r\n        this.logger.log('User logged out successfully', { \r\n          tokenId: tokenRecord.id \r\n        });\r\n      }\r\n    } catch (error) {\r\n      this.logger.error('Error during logout', {\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate JWT payload and return user\r\n   * @param payload JWT payload\r\n   * @returns Promise<User> User object\r\n   */\r\n  async validateJwtPayload(payload: any): Promise<User> {\r\n    try {\r\n      const user = await this.userRepository.findOne({\r\n        where: { id: payload.sub },\r\n        relations: ['roles'],\r\n      });\r\n\r\n      if (!user || !user.isActive) {\r\n        throw new UnauthorizedException('User not found or inactive');\r\n      }\r\n\r\n      return user;\r\n    } catch (error) {\r\n      this.logger.error('Error validating JWT payload', {\r\n        payload,\r\n        error: error.message,\r\n      });\r\n      throw new UnauthorizedException('Invalid token');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handle failed login attempt\r\n   * @param user User object\r\n   * @returns Promise<void>\r\n   */\r\n  private async handleFailedLogin(user: User): Promise<void> {\r\n    const authConfig = this.configService.get('auth');\r\n    const maxAttempts = authConfig.security.maxLoginAttempts;\r\n    const lockoutDuration = authConfig.security.lockoutDuration;\r\n\r\n    user.failedLoginAttempts += 1;\r\n\r\n    if (user.failedLoginAttempts >= maxAttempts) {\r\n      user.lockedUntil = new Date(Date.now() + lockoutDuration);\r\n      this.logger.warn('User account locked due to failed login attempts', {\r\n        userId: user.id,\r\n        attempts: user.failedLoginAttempts,\r\n        lockedUntil: user.lockedUntil,\r\n      });\r\n    }\r\n\r\n    await this.userRepository.save(user);\r\n  }\r\n\r\n  /**\r\n   * Reset failed login attempts\r\n   * @param user User object\r\n   * @returns Promise<void>\r\n   */\r\n  private async resetFailedLoginAttempts(user: User): Promise<void> {\r\n    user.failedLoginAttempts = 0;\r\n    user.lockedUntil = null;\r\n    await this.userRepository.save(user);\r\n  }\r\n\r\n  /**\r\n   * Update user's last login timestamp\r\n   * @param user User object\r\n   * @param ipAddress Client IP address\r\n   * @param userAgent Client user agent\r\n   * @returns Promise<void>\r\n   */\r\n  private async updateLastLogin(\r\n    user: User,\r\n    ipAddress?: string,\r\n    userAgent?: string,\r\n  ): Promise<void> {\r\n    user.lastLoginAt = new Date();\r\n    await this.userRepository.save(user);\r\n\r\n    // Log login event for audit\r\n    this.logger.log('User login recorded', {\r\n      userId: user.id,\r\n      email: user.email,\r\n      ipAddress,\r\n      userAgent,\r\n      timestamp: user.lastLoginAt,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Revoke refresh token\r\n   * @param tokenId Token ID to revoke\r\n   * @returns Promise<void>\r\n   */\r\n  private async revokeRefreshToken(tokenId: string): Promise<void> {\r\n    await this.refreshTokenRepository.update(tokenId, { isRevoked: true });\r\n  }\r\n\r\n  /**\r\n   * Parse expiration time string to seconds\r\n   * @param expiresIn Expiration time string (e.g., '1h', '30m')\r\n   * @returns number Expiration time in seconds\r\n   */\r\n  private parseExpirationTime(expiresIn: string): number {\r\n    const match = expiresIn.match(/^(\\d+)([smhd])$/);\r\n    if (!match) return 3600; // Default to 1 hour\r\n\r\n    const value = parseInt(match[1]);\r\n    const unit = match[2];\r\n\r\n    switch (unit) {\r\n      case 's': return value;\r\n      case 'm': return value * 60;\r\n      case 'h': return value * 3600;\r\n      case 'd': return value * 86400;\r\n      default: return 3600;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Find or create OAuth user\r\n   * @param oauthData OAuth user data\r\n   * @returns User entity\r\n   */\r\n  async findOrCreateOAuthUser(oauthData: {\r\n    email: string;\r\n    firstName: string;\r\n    lastName: string;\r\n    avatarUrl?: string;\r\n    provider: string;\r\n    providerId: string;\r\n    providerData: any;\r\n  }): Promise<User | null> {\r\n    try {\r\n      // Find existing user by email\r\n      let user = await this.userRepository.findOne({\r\n        where: { email: oauthData.email.toLowerCase() },\r\n        relations: ['roles'],\r\n      });\r\n\r\n      if (user) {\r\n        // Update OAuth provider data\r\n        user.metadata = {\r\n          ...user.metadata,\r\n          oauth: {\r\n            ...user.metadata?.oauth,\r\n            [oauthData.provider]: {\r\n              providerId: oauthData.providerId,\r\n              lastLogin: new Date(),\r\n              providerData: oauthData.providerData,\r\n            },\r\n          },\r\n        };\r\n\r\n        // Update avatar if provided and user doesn't have one\r\n        if (oauthData.avatarUrl && !user.avatarUrl) {\r\n          user.avatarUrl = oauthData.avatarUrl;\r\n        }\r\n\r\n        // Mark email as verified for OAuth users\r\n        if (!user.emailVerified) {\r\n          user.emailVerified = true;\r\n          user.status = 'active';\r\n        }\r\n\r\n        await this.userRepository.save(user);\r\n        return user;\r\n      }\r\n\r\n      // Create new user for OAuth\r\n      const defaultRole = await this.getDefaultRole();\r\n\r\n      user = this.userRepository.create({\r\n        email: oauthData.email.toLowerCase(),\r\n        firstName: oauthData.firstName,\r\n        lastName: oauthData.lastName,\r\n        avatarUrl: oauthData.avatarUrl,\r\n        emailVerified: true,\r\n        status: 'active',\r\n        passwordHash: '', // OAuth users don't have passwords\r\n        roles: defaultRole ? [defaultRole] : [],\r\n        metadata: {\r\n          oauth: {\r\n            [oauthData.provider]: {\r\n              providerId: oauthData.providerId,\r\n              createdAt: new Date(),\r\n              providerData: oauthData.providerData,\r\n            },\r\n          },\r\n        },\r\n      });\r\n\r\n      return await this.userRepository.save(user);\r\n\r\n    } catch (error) {\r\n      this.logger.error('Error finding or creating OAuth user', {\r\n        email: oauthData.email,\r\n        provider: oauthData.provider,\r\n        error: error.message,\r\n      });\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Find or create SAML user\r\n   * @param samlData SAML user data\r\n   * @returns User entity\r\n   */\r\n  async findOrCreateSamlUser(samlData: {\r\n    email: string;\r\n    firstName: string;\r\n    lastName: string;\r\n    displayName?: string;\r\n    provider: string;\r\n    providerId: string;\r\n    providerData: any;\r\n  }): Promise<User | null> {\r\n    try {\r\n      // Find existing user by email\r\n      let user = await this.userRepository.findOne({\r\n        where: { email: samlData.email.toLowerCase() },\r\n        relations: ['roles'],\r\n      });\r\n\r\n      if (user) {\r\n        // Update SAML provider data\r\n        user.metadata = {\r\n          ...user.metadata,\r\n          saml: {\r\n            ...user.metadata?.saml,\r\n            providerId: samlData.providerId,\r\n            lastLogin: new Date(),\r\n            providerData: samlData.providerData,\r\n          },\r\n        };\r\n\r\n        // Mark email as verified for SAML users\r\n        if (!user.emailVerified) {\r\n          user.emailVerified = true;\r\n          user.status = 'active';\r\n        }\r\n\r\n        await this.userRepository.save(user);\r\n        return user;\r\n      }\r\n\r\n      // Create new user for SAML\r\n      const defaultRole = await this.getDefaultRole();\r\n\r\n      user = this.userRepository.create({\r\n        email: samlData.email.toLowerCase(),\r\n        firstName: samlData.firstName,\r\n        lastName: samlData.lastName,\r\n        emailVerified: true,\r\n        status: 'active',\r\n        passwordHash: '', // SAML users don't have passwords\r\n        roles: defaultRole ? [defaultRole] : [],\r\n        metadata: {\r\n          saml: {\r\n            providerId: samlData.providerId,\r\n            createdAt: new Date(),\r\n            providerData: samlData.providerData,\r\n          },\r\n        },\r\n      });\r\n\r\n      return await this.userRepository.save(user);\r\n\r\n    } catch (error) {\r\n      this.logger.error('Error finding or creating SAML user', {\r\n        email: samlData.email,\r\n        provider: samlData.provider,\r\n        error: error.message,\r\n      });\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Find user by SAML nameID\r\n   * @param nameId SAML nameID\r\n   * @returns User entity or null\r\n   */\r\n  async findUserBySamlNameId(nameId: string): Promise<User | null> {\r\n    try {\r\n      const users = await this.userRepository\r\n        .createQueryBuilder('user')\r\n        .where(\"user.metadata->>'saml'->>'providerId' = :nameId\", { nameId })\r\n        .getMany();\r\n\r\n      return users.length > 0 ? users[0] : null;\r\n    } catch (error) {\r\n      this.logger.error('Error finding user by SAML nameID', {\r\n        nameId,\r\n        error: error.message,\r\n      });\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Revoke all user tokens\r\n   * @param userId User ID\r\n   * @param reason Revocation reason\r\n   * @returns Number of tokens revoked\r\n   */\r\n  async revokeAllUserTokens(userId: string, reason?: string): Promise<number> {\r\n    try {\r\n      const result = await this.refreshTokenRepository.update(\r\n        { userId, isRevoked: false },\r\n        {\r\n          isRevoked: true,\r\n          revokedAt: new Date(),\r\n          revokedReason: reason || 'manual_revocation',\r\n        }\r\n      );\r\n\r\n      return result.affected || 0;\r\n    } catch (error) {\r\n      this.logger.error('Error revoking all user tokens', {\r\n        userId,\r\n        error: error.message,\r\n      });\r\n      return 0;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get default role for new users\r\n   * @returns Default role or null\r\n   */\r\n  private async getDefaultRole(): Promise<Role | null> {\r\n    try {\r\n      return await this.roleRepository.findOne({\r\n        where: { isDefault: true, isActive: true },\r\n      });\r\n    } catch (error) {\r\n      this.logger.error('Error getting default role', {\r\n        error: error.message,\r\n      });\r\n      return null;\r\n    }\r\n  }\r\n}\r\n"], "version": 3}