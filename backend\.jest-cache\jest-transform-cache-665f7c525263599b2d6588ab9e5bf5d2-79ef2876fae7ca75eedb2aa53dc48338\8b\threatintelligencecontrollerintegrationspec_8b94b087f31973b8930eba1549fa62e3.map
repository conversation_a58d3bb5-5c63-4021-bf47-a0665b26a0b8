{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\api\\controllers\\__tests__\\threat-intelligence.controller.integration.spec.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6CAAsD;AAEtD,6CAAgD;AAChD,2CAA6D;AAC7D,mDAAqC;AAErC,sFAAiF;AACjF,2GAAsG;AACtG,oGAAqJ;AACrJ,4GAAgG;AAChG,sFAA2E;AAC3E,4FAAiF;AACjF,yFAAqF;AACrF,6FAAyF;AACzF,0GAAsG;AACtG,6FAAwF;AACxF,uFAAmF;AAEnF,QAAQ,CAAC,4CAA4C,EAAE,GAAG,EAAE;IAC1D,IAAI,GAAqB,CAAC;IAC1B,IAAI,yBAAoD,CAAC;IACzD,IAAI,aAA4B,CAAC;IAEjC,MAAM,QAAQ,GAAG;QACf,EAAE,EAAE,SAAS;QACb,KAAK,EAAE,kBAAkB;QACzB,KAAK,EAAE,CAAC,gBAAgB,CAAC;KAC1B,CAAC;IAEF,MAAM,sBAAsB,GAAG;QAC7B,EAAE,EAAE,sCAAsC;QAC1C,KAAK,EAAE,mBAAmB;QAC1B,WAAW,EAAE,wDAAwD;QACrE,UAAU,EAAE,uCAAU,CAAC,GAAG;QAC1B,QAAQ,EAAE,2CAAc,CAAC,IAAI;QAC7B,UAAU,EAAE,6CAAgB,CAAC,IAAI;QACjC,MAAM,EAAE,yCAAY,CAAC,MAAM;QAC3B,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QACjC,QAAQ,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QAChC,IAAI,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,UAAU,CAAC;QACzC,UAAU,EAAE;YACV,IAAI,EAAE,0BAA0B;YAChC,IAAI,EAAE,YAAY;YAClB,WAAW,EAAE,GAAG;YAChB,GAAG,EAAE,sCAAsC;YAC3C,WAAW,EAAE,IAAI,IAAI,EAAE;YACvB,UAAU,EAAE,6CAAgB,CAAC,IAAI;SAClC;QACD,WAAW,EAAE;YACX,OAAO,EAAE,CAAC,gBAAgB,EAAE,aAAa,CAAC;YAC1C,UAAU,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;SACvC;QACD,eAAe,EAAE,CAAC,YAAY,EAAE,WAAW,CAAC;QAC5C,iBAAiB,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;QAC/B,SAAS,EAAE,GAAG;QACd,gBAAgB,EAAE,CAAC;QACnB,KAAK,EAAE,KAAK;QACZ,YAAY,EAAE,IAAI;QAClB,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QACjC,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;KAClC,CAAC;IAEF,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,aAAa,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAClE,OAAO,EAAE;gBACP,qBAAY,CAAC,OAAO,CAAC;oBACnB,QAAQ,EAAE,IAAI;oBACd,WAAW,EAAE,WAAW;iBACzB,CAAC;gBACF,uBAAa,CAAC,YAAY,CAAC;oBACzB,OAAO,EAAE,CAAC,qBAAY,CAAC;oBACvB,UAAU,EAAE,CAAC,aAA4B,EAAE,EAAE,CAAC,CAAC;wBAC7C,IAAI,EAAE,UAAU;wBAChB,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,WAAW,CAAC;wBAC/C,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC;wBACxC,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,aAAa,EAAE,MAAM,CAAC;wBAClD,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,aAAa,EAAE,MAAM,CAAC;wBAClD,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,aAAa,EAAE,eAAe,CAAC;wBAC3D,QAAQ,EAAE,CAAC,+CAAkB,EAAE,sDAAqB,EAAE,iCAAW,EAAE,uCAAc,CAAC;wBAClF,WAAW,EAAE,IAAI;wBACjB,UAAU,EAAE,IAAI;qBACjB,CAAC;oBACF,MAAM,EAAE,CAAC,sBAAa,CAAC;iBACxB,CAAC;gBACF,uBAAa,CAAC,UAAU,CAAC,CAAC,+CAAkB,EAAE,sDAAqB,EAAE,iCAAW,EAAE,uCAAc,CAAC,CAAC;aACnG;YACD,WAAW,EAAE,CAAC,6DAA4B,CAAC;YAC3C,SAAS,EAAE;gBACT,uDAAyB;gBACzB;oBACE,OAAO,EAAE,8BAAa;oBACtB,QAAQ,EAAE;wBACR,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;wBAChB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;wBACf,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;wBACd,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;qBACjB;iBACF;gBACD;oBACE,OAAO,EAAE,4BAAY;oBACrB,QAAQ,EAAE;wBACR,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;wBACxB,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE;qBAC5B;iBACF;gBACD;oBACE,OAAO,EAAE,0CAAmB;oBAC5B,QAAQ,EAAE;wBACR,uBAAuB,EAAE,IAAI,CAAC,EAAE,EAAE;wBAClC,oBAAoB,EAAE,IAAI,CAAC,EAAE,EAAE;wBAC/B,oBAAoB,EAAE,IAAI,CAAC,EAAE,EAAE;qBAChC;iBACF;aACF;SACF,CAAC;aACC,aAAa,CAAC,6BAAY,CAAC;aAC3B,QAAQ,CAAC;YACR,WAAW,EAAE,CAAC,OAAO,EAAE,EAAE;gBACvB,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;gBACpD,OAAO,CAAC,IAAI,GAAG,QAAQ,CAAC;gBACxB,OAAO,IAAI,CAAC;YACd,CAAC;SACF,CAAC;aACD,aAAa,CAAC,wBAAU,CAAC;aACzB,QAAQ,CAAC;YACR,WAAW,EAAE,GAAG,EAAE,CAAC,IAAI;SACxB,CAAC;aACD,OAAO,EAAE,CAAC;QAEb,GAAG,GAAG,aAAa,CAAC,qBAAqB,EAAE,CAAC;QAC5C,yBAAyB,GAAG,aAAa,CAAC,GAAG,CAA4B,uDAAyB,CAAC,CAAC;QACpG,aAAa,GAAG,aAAa,CAAC,GAAG,CAAgB,sBAAa,CAAC,CAAC;QAEhE,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC;IACnB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,qCAAqC;QACrC,MAAM,gBAAgB,GAAG,yBAAyB,CAAC,8BAA8B,CAAC,CAAC;QACnF,MAAM,gBAAgB,CAAC,KAAK,EAAE,CAAC;IACjC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,MAAM,SAAS,GAAG;gBAChB,KAAK,EAAE,kBAAkB;gBACzB,WAAW,EAAE,gCAAgC;gBAC7C,UAAU,EAAE,uCAAU,CAAC,GAAG;gBAC1B,QAAQ,EAAE,2CAAc,CAAC,IAAI;gBAC7B,UAAU,EAAE,6CAAgB,CAAC,MAAM;gBACnC,SAAS,EAAE,sBAAsB;gBACjC,UAAU,EAAE;oBACV,IAAI,EAAE,aAAa;oBACnB,IAAI,EAAE,YAAY;oBAClB,WAAW,EAAE,GAAG;oBAChB,GAAG,EAAE,kBAAkB;oBACvB,WAAW,EAAE,sBAAsB;oBACnC,UAAU,EAAE,MAAM;iBACnB;gBACD,IAAI,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;gBACrB,eAAe,EAAE,CAAC,YAAY,CAAC;aAChC,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,0BAA0B,CAAC;iBAChC,IAAI,CAAC,SAAS,CAAC;iBACf,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC;gBAClC,KAAK,EAAE,SAAS,CAAC,KAAK;gBACtB,WAAW,EAAE,SAAS,CAAC,WAAW;gBAClC,UAAU,EAAE,SAAS,CAAC,UAAU;gBAChC,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,UAAU,EAAE,SAAS,CAAC,UAAU;gBAChC,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,eAAe,EAAE,SAAS,CAAC,eAAe;aAC3C,CAAC,CAAC;YACH,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;YACvC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,UAAU,GAAG;gBACjB,KAAK,EAAE,EAAE,EAAE,uBAAuB;gBAClC,WAAW,EAAE,aAAa;gBAC1B,UAAU,EAAE,cAAc,EAAE,qBAAqB;gBACjD,SAAS,EAAE,cAAc,EAAE,sBAAsB;aAClD,CAAC;YAEF,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,IAAI,CAAC,0BAA0B,CAAC;iBAChC,IAAI,CAAC,UAAU,CAAC;iBAChB,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,MAAM,SAAS,GAAG;gBAChB,KAAK,EAAE,kBAAkB;gBACzB,WAAW,EAAE,aAAa;gBAC1B,UAAU,EAAE,uCAAU,CAAC,OAAO;gBAC9B,SAAS,EAAE,sBAAsB;gBACjC,UAAU,EAAE;oBACV,IAAI,EAAE,aAAa;oBACnB,IAAI,EAAE,YAAY;oBAClB,WAAW,EAAE,GAAG;oBAChB,GAAG,EAAE,kBAAkB;oBACvB,WAAW,EAAE,sBAAsB;oBACnC,UAAU,EAAE,MAAM;iBACnB;aACF,CAAC;YAEF,sBAAsB;YACtB,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,IAAI,CAAC,0BAA0B,CAAC;iBAChC,IAAI,CAAC,SAAS,CAAC;iBACf,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,0BAA0B;YAC1B,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,IAAI,CAAC,0BAA0B,CAAC;iBAChC,IAAI,CAAC,SAAS,CAAC;iBACf,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAChD,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,mCAAmC;YACnC,MAAM,OAAO,GAAG,MAAM,yBAAyB,CAAC,wBAAwB,CAAC;gBACvE,KAAK,EAAE,aAAa;gBACpB,WAAW,EAAE,kBAAkB;gBAC/B,UAAU,EAAE,uCAAU,CAAC,GAAG;gBAC1B,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,UAAU,EAAE,sBAAsB,CAAC,UAAU;aAC9C,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;YAEhB,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,4BAA4B,OAAO,CAAC,EAAE,EAAE,CAAC;iBAC7C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC;gBAClC,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,KAAK,EAAE,aAAa;gBACpB,WAAW,EAAE,kBAAkB;gBAC/B,UAAU,EAAE,uCAAU,CAAC,GAAG;aAC3B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;YACtE,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,+DAA+D,CAAC;iBACpE,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,uCAAuC,CAAC;iBAC5C,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qCAAqC,EAAE,GAAG,EAAE;QACnD,UAAU,CAAC,KAAK,IAAI,EAAE;YACpB,mBAAmB;YACnB,MAAM,yBAAyB,CAAC,wBAAwB,CAAC;gBACvD,KAAK,EAAE,gBAAgB;gBACvB,WAAW,EAAE,4BAA4B;gBACzC,UAAU,EAAE,uCAAU,CAAC,GAAG;gBAC1B,QAAQ,EAAE,2CAAc,CAAC,IAAI;gBAC7B,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACjC,IAAI,EAAE,CAAC,OAAO,EAAE,YAAY,CAAC;gBAC7B,UAAU,EAAE,sBAAsB,CAAC,UAAU;aAC9C,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;YAEhB,MAAM,yBAAyB,CAAC,wBAAwB,CAAC;gBACvD,KAAK,EAAE,kBAAkB;gBACzB,WAAW,EAAE,iBAAiB;gBAC9B,UAAU,EAAE,uCAAU,CAAC,OAAO;gBAC9B,QAAQ,EAAE,2CAAc,CAAC,MAAM;gBAC/B,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACjC,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;gBAC5B,UAAU,EAAE,sBAAsB,CAAC,UAAU;aAC9C,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,iCAAiC,CAAC;iBACtC,KAAK,CAAC;gBACL,WAAW,EAAE,CAAC,uCAAU,CAAC,GAAG,CAAC;gBAC7B,UAAU,EAAE,CAAC,2CAAc,CAAC,IAAI,CAAC;gBACjC,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,EAAE;aACV,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC9C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,uCAAU,CAAC,GAAG,CAAC,CAAC;YACjE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,2CAAc,CAAC,IAAI,CAAC,CAAC;YACpE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,iCAAiC,CAAC;iBACtC,KAAK,CAAC;gBACL,UAAU,EAAE,YAAY;gBACxB,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,EAAE;aACV,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC9C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,KAAK,IAAI,EAAE;YACxC,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,iCAAiC,CAAC;iBACtC,KAAK,CAAC;gBACL,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,CAAC;aACT,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC9C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,iCAAiC,CAAC;iBACtC,KAAK,CAAC;gBACL,UAAU,EAAE,aAAa;aAC1B,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC9C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAChD,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,mCAAmC;YACnC,MAAM,OAAO,GAAG,MAAM,yBAAyB,CAAC,wBAAwB,CAAC;gBACvE,KAAK,EAAE,gBAAgB;gBACvB,WAAW,EAAE,sBAAsB;gBACnC,UAAU,EAAE,uCAAU,CAAC,OAAO;gBAC9B,QAAQ,EAAE,2CAAc,CAAC,MAAM;gBAC/B,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,UAAU,EAAE,sBAAsB,CAAC,UAAU;aAC9C,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;YAEhB,MAAM,SAAS,GAAG;gBAChB,KAAK,EAAE,eAAe;gBACtB,QAAQ,EAAE,2CAAc,CAAC,IAAI;gBAC7B,IAAI,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;aAC1B,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,4BAA4B,OAAO,CAAC,EAAE,EAAE,CAAC;iBAC7C,IAAI,CAAC,SAAS,CAAC;iBACf,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC;gBAClC,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,KAAK,EAAE,eAAe;gBACtB,QAAQ,EAAE,2CAAc,CAAC,IAAI;gBAC7B,IAAI,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;aAC1B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;YACtE,MAAM,SAAS,GAAG;gBAChB,KAAK,EAAE,eAAe;aACvB,CAAC;YAEF,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,+DAA+D,CAAC;iBACpE,IAAI,CAAC,SAAS,CAAC;iBACf,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qCAAqC,EAAE,GAAG,EAAE;QACnD,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,mCAAmC;YACnC,MAAM,OAAO,GAAG,MAAM,yBAAyB,CAAC,wBAAwB,CAAC;gBACvE,KAAK,EAAE,WAAW;gBAClB,WAAW,EAAE,iBAAiB;gBAC9B,UAAU,EAAE,uCAAU,CAAC,OAAO;gBAC9B,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,UAAU,EAAE,sBAAsB,CAAC,UAAU;aAC9C,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;YAEhB,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,MAAM,CAAC,4BAA4B,OAAO,CAAC,EAAE,EAAE,CAAC;iBAChD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,sBAAsB;YACtB,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,4BAA4B,OAAO,CAAC,EAAE,EAAE,CAAC;iBAC7C,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;YACtE,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,MAAM,CAAC,+DAA+D,CAAC;iBACvE,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2CAA2C,EAAE,GAAG,EAAE;QACzD,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,mCAAmC;YACnC,MAAM,OAAO,GAAG,MAAM,yBAAyB,CAAC,wBAAwB,CAAC;gBACvE,KAAK,EAAE,mBAAmB;gBAC1B,WAAW,EAAE,aAAa;gBAC1B,UAAU,EAAE,uCAAU,CAAC,OAAO;gBAC9B,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,UAAU,EAAE,sBAAsB,CAAC,UAAU;aAC9C,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;YAEhB,MAAM,kBAAkB,GAAG;gBACzB,MAAM,EAAE,aAAa;gBACrB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,OAAO,EAAE,kBAAkB;aAC5B,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,4BAA4B,OAAO,CAAC,EAAE,UAAU,CAAC;iBACtD,IAAI,CAAC,EAAE,OAAO,EAAE,kBAAkB,EAAE,CAAC;iBACrC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC1D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wCAAwC,EAAE,GAAG,EAAE;QACtD,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,wBAAwB;YACxB,MAAM,yBAAyB,CAAC,wBAAwB,CAAC;gBACvD,KAAK,EAAE,kBAAkB;gBACzB,WAAW,EAAE,MAAM;gBACnB,UAAU,EAAE,uCAAU,CAAC,GAAG;gBAC1B,QAAQ,EAAE,2CAAc,CAAC,QAAQ;gBACjC,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,UAAU,EAAE,sBAAsB,CAAC,UAAU;aAC9C,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;YAEhB,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,oCAAoC,CAAC;iBACzC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAChD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YACrD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAClD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACtD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACvD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YACzD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sCAAsC,EAAE,GAAG,EAAE;QACpD,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,mBAAmB;YACnB,MAAM,yBAAyB,CAAC,wBAAwB,CAAC;gBACvD,KAAK,EAAE,aAAa;gBACpB,WAAW,EAAE,aAAa;gBAC1B,UAAU,EAAE,uCAAU,CAAC,OAAO;gBAC9B,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,UAAU,EAAE,sBAAsB,CAAC,UAAU;aAC9C,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;YAEhB,MAAM,aAAa,GAAG;gBACpB,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE;oBACR,WAAW,EAAE,CAAC,uCAAU,CAAC,OAAO,CAAC;iBAClC;aACF,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,iCAAiC,CAAC;iBACvC,IAAI,CAAC,aAAa,CAAC;iBACnB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACvD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAC7C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YACnD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\api\\controllers\\__tests__\\threat-intelligence.controller.integration.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { INestApplication } from '@nestjs/common';\r\nimport { TypeOrmModule } from '@nestjs/typeorm';\r\nimport { ConfigModule, ConfigService } from '@nestjs/config';\r\nimport * as request from 'supertest';\r\n\r\nimport { ThreatIntelligenceController } from '../threat-intelligence.controller';\r\nimport { ThreatIntelligenceService } from '../../../application/services/threat-intelligence.service';\r\nimport { ThreatIntelligence, ThreatType, ThreatSeverity, ThreatStatus, ThreatConfidence } from '../../../domain/entities/threat-intelligence.entity';\r\nimport { IndicatorOfCompromise } from '../../../domain/entities/indicator-of-compromise.entity';\r\nimport { ThreatActor } from '../../../domain/entities/threat-actor.entity';\r\nimport { ThreatCampaign } from '../../../domain/entities/threat-campaign.entity';\r\nimport { LoggerService } from '../../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../../infrastructure/logging/audit/audit.service';\r\nimport { NotificationService } from '../../../../../infrastructure/notification/notification.service';\r\nimport { JwtAuthGuard } from '../../../../../infrastructure/auth/guards/jwt-auth.guard';\r\nimport { RolesGuard } from '../../../../../infrastructure/auth/guards/roles.guard';\r\n\r\ndescribe('ThreatIntelligenceController (Integration)', () => {\r\n  let app: INestApplication;\r\n  let threatIntelligenceService: ThreatIntelligenceService;\r\n  let configService: ConfigService;\r\n\r\n  const mockUser = {\r\n    id: 'user123',\r\n    email: '<EMAIL>',\r\n    roles: ['threat_analyst'],\r\n  };\r\n\r\n  const mockThreatIntelligence = {\r\n    id: '123e4567-e89b-12d3-a456-426614174000',\r\n    title: 'Test APT Campaign',\r\n    description: 'Advanced persistent threat targeting healthcare sector',\r\n    threatType: ThreatType.APT,\r\n    severity: ThreatSeverity.HIGH,\r\n    confidence: ThreatConfidence.HIGH,\r\n    status: ThreatStatus.ACTIVE,\r\n    firstSeen: new Date('2023-01-01'),\r\n    lastSeen: new Date('2023-01-15'),\r\n    tags: ['apt29', 'healthcare', 'phishing'],\r\n    dataSource: {\r\n      name: 'CrowdStrike Intelligence',\r\n      type: 'commercial',\r\n      reliability: 'A',\r\n      url: 'https://intelligence.crowdstrike.com',\r\n      lastUpdated: new Date(),\r\n      confidence: ThreatConfidence.HIGH,\r\n    },\r\n    mitreAttack: {\r\n      tactics: ['initial-access', 'persistence'],\r\n      techniques: ['T1566.001', 'T1053.005'],\r\n    },\r\n    targetedSectors: ['healthcare', 'financial'],\r\n    targetedCountries: ['US', 'UK'],\r\n    riskScore: 8.5,\r\n    observationCount: 5,\r\n    isIoc: false,\r\n    isAttributed: true,\r\n    createdAt: new Date('2023-01-01'),\r\n    updatedAt: new Date('2023-01-15'),\r\n  };\r\n\r\n  beforeAll(async () => {\r\n    const moduleFixture: TestingModule = await Test.createTestingModule({\r\n      imports: [\r\n        ConfigModule.forRoot({\r\n          isGlobal: true,\r\n          envFilePath: '.env.test',\r\n        }),\r\n        TypeOrmModule.forRootAsync({\r\n          imports: [ConfigModule],\r\n          useFactory: (configService: ConfigService) => ({\r\n            type: 'postgres',\r\n            host: configService.get('DB_HOST', 'localhost'),\r\n            port: configService.get('DB_PORT', 5432),\r\n            username: configService.get('DB_USERNAME', 'test'),\r\n            password: configService.get('DB_PASSWORD', 'test'),\r\n            database: configService.get('DB_DATABASE', 'sentinel_test'),\r\n            entities: [ThreatIntelligence, IndicatorOfCompromise, ThreatActor, ThreatCampaign],\r\n            synchronize: true,\r\n            dropSchema: true,\r\n          }),\r\n          inject: [ConfigService],\r\n        }),\r\n        TypeOrmModule.forFeature([ThreatIntelligence, IndicatorOfCompromise, ThreatActor, ThreatCampaign]),\r\n      ],\r\n      controllers: [ThreatIntelligenceController],\r\n      providers: [\r\n        ThreatIntelligenceService,\r\n        {\r\n          provide: LoggerService,\r\n          useValue: {\r\n            error: jest.fn(),\r\n            warn: jest.fn(),\r\n            log: jest.fn(),\r\n            debug: jest.fn(),\r\n          },\r\n        },\r\n        {\r\n          provide: AuditService,\r\n          useValue: {\r\n            logUserAction: jest.fn(),\r\n            logSecurityEvent: jest.fn(),\r\n          },\r\n        },\r\n        {\r\n          provide: NotificationService,\r\n          useValue: {\r\n            sendCriticalThreatAlert: jest.fn(),\r\n            sendUserNotification: jest.fn(),\r\n            sendRoleNotification: jest.fn(),\r\n          },\r\n        },\r\n      ],\r\n    })\r\n      .overrideGuard(JwtAuthGuard)\r\n      .useValue({\r\n        canActivate: (context) => {\r\n          const request = context.switchToHttp().getRequest();\r\n          request.user = mockUser;\r\n          return true;\r\n        },\r\n      })\r\n      .overrideGuard(RolesGuard)\r\n      .useValue({\r\n        canActivate: () => true,\r\n      })\r\n      .compile();\r\n\r\n    app = moduleFixture.createNestApplication();\r\n    threatIntelligenceService = moduleFixture.get<ThreatIntelligenceService>(ThreatIntelligenceService);\r\n    configService = moduleFixture.get<ConfigService>(ConfigService);\r\n\r\n    await app.init();\r\n  });\r\n\r\n  afterAll(async () => {\r\n    await app.close();\r\n  });\r\n\r\n  beforeEach(async () => {\r\n    // Clean up database before each test\r\n    const threatRepository = threatIntelligenceService['threatIntelligenceRepository'];\r\n    await threatRepository.clear();\r\n  });\r\n\r\n  describe('POST /api/threat-intelligence', () => {\r\n    it('should create threat intelligence successfully', async () => {\r\n      const createDto = {\r\n        title: 'New APT Campaign',\r\n        description: 'New advanced persistent threat',\r\n        threatType: ThreatType.APT,\r\n        severity: ThreatSeverity.HIGH,\r\n        confidence: ThreatConfidence.MEDIUM,\r\n        firstSeen: '2023-01-01T00:00:00Z',\r\n        dataSource: {\r\n          name: 'Test Source',\r\n          type: 'commercial',\r\n          reliability: 'A',\r\n          url: 'https://test.com',\r\n          lastUpdated: '2023-01-01T00:00:00Z',\r\n          confidence: 'high',\r\n        },\r\n        tags: ['test', 'apt'],\r\n        targetedSectors: ['healthcare'],\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/threat-intelligence')\r\n        .send(createDto)\r\n        .expect(201);\r\n\r\n      expect(response.body).toMatchObject({\r\n        title: createDto.title,\r\n        description: createDto.description,\r\n        threatType: createDto.threatType,\r\n        severity: createDto.severity,\r\n        confidence: createDto.confidence,\r\n        tags: createDto.tags,\r\n        targetedSectors: createDto.targetedSectors,\r\n      });\r\n      expect(response.body.id).toBeDefined();\r\n      expect(response.body.createdAt).toBeDefined();\r\n      expect(response.body.updatedAt).toBeDefined();\r\n    });\r\n\r\n    it('should return 400 for invalid data', async () => {\r\n      const invalidDto = {\r\n        title: '', // Invalid: empty title\r\n        description: 'Description',\r\n        threatType: 'invalid_type', // Invalid enum value\r\n        firstSeen: 'invalid_date', // Invalid date format\r\n      };\r\n\r\n      await request(app.getHttpServer())\r\n        .post('/api/threat-intelligence')\r\n        .send(invalidDto)\r\n        .expect(400);\r\n    });\r\n\r\n    it('should return 409 for duplicate threat intelligence', async () => {\r\n      const createDto = {\r\n        title: 'Duplicate Threat',\r\n        description: 'Description',\r\n        threatType: ThreatType.MALWARE,\r\n        firstSeen: '2023-01-01T00:00:00Z',\r\n        dataSource: {\r\n          name: 'Test Source',\r\n          type: 'commercial',\r\n          reliability: 'A',\r\n          url: 'https://test.com',\r\n          lastUpdated: '2023-01-01T00:00:00Z',\r\n          confidence: 'high',\r\n        },\r\n      };\r\n\r\n      // Create first threat\r\n      await request(app.getHttpServer())\r\n        .post('/api/threat-intelligence')\r\n        .send(createDto)\r\n        .expect(201);\r\n\r\n      // Try to create duplicate\r\n      await request(app.getHttpServer())\r\n        .post('/api/threat-intelligence')\r\n        .send(createDto)\r\n        .expect(409);\r\n    });\r\n  });\r\n\r\n  describe('GET /api/threat-intelligence/:id', () => {\r\n    it('should return threat intelligence by ID', async () => {\r\n      // Create threat intelligence first\r\n      const created = await threatIntelligenceService.createThreatIntelligence({\r\n        title: 'Test Threat',\r\n        description: 'Test description',\r\n        threatType: ThreatType.APT,\r\n        firstSeen: new Date(),\r\n        dataSource: mockThreatIntelligence.dataSource,\r\n      }, mockUser.id);\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .get(`/api/threat-intelligence/${created.id}`)\r\n        .expect(200);\r\n\r\n      expect(response.body).toMatchObject({\r\n        id: created.id,\r\n        title: 'Test Threat',\r\n        description: 'Test description',\r\n        threatType: ThreatType.APT,\r\n      });\r\n    });\r\n\r\n    it('should return 404 for non-existent threat intelligence', async () => {\r\n      await request(app.getHttpServer())\r\n        .get('/api/threat-intelligence/123e4567-e89b-12d3-a456-************')\r\n        .expect(404);\r\n    });\r\n\r\n    it('should return 400 for invalid UUID format', async () => {\r\n      await request(app.getHttpServer())\r\n        .get('/api/threat-intelligence/invalid-uuid')\r\n        .expect(400);\r\n    });\r\n  });\r\n\r\n  describe('GET /api/threat-intelligence/search', () => {\r\n    beforeEach(async () => {\r\n      // Create test data\r\n      await threatIntelligenceService.createThreatIntelligence({\r\n        title: 'APT29 Campaign',\r\n        description: 'APT29 targeting healthcare',\r\n        threatType: ThreatType.APT,\r\n        severity: ThreatSeverity.HIGH,\r\n        firstSeen: new Date('2023-01-01'),\r\n        tags: ['apt29', 'healthcare'],\r\n        dataSource: mockThreatIntelligence.dataSource,\r\n      }, mockUser.id);\r\n\r\n      await threatIntelligenceService.createThreatIntelligence({\r\n        title: 'Malware Campaign',\r\n        description: 'Banking malware',\r\n        threatType: ThreatType.MALWARE,\r\n        severity: ThreatSeverity.MEDIUM,\r\n        firstSeen: new Date('2023-01-02'),\r\n        tags: ['banking', 'malware'],\r\n        dataSource: mockThreatIntelligence.dataSource,\r\n      }, mockUser.id);\r\n    });\r\n\r\n    it('should search threat intelligence with filters', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/threat-intelligence/search')\r\n        .query({\r\n          threatTypes: [ThreatType.APT],\r\n          severities: [ThreatSeverity.HIGH],\r\n          page: 1,\r\n          limit: 10,\r\n        })\r\n        .expect(200);\r\n\r\n      expect(response.body.threats).toHaveLength(1);\r\n      expect(response.body.threats[0].threatType).toBe(ThreatType.APT);\r\n      expect(response.body.threats[0].severity).toBe(ThreatSeverity.HIGH);\r\n      expect(response.body.total).toBe(1);\r\n      expect(response.body.page).toBe(1);\r\n      expect(response.body.totalPages).toBe(1);\r\n    });\r\n\r\n    it('should search with text query', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/threat-intelligence/search')\r\n        .query({\r\n          searchText: 'healthcare',\r\n          page: 1,\r\n          limit: 10,\r\n        })\r\n        .expect(200);\r\n\r\n      expect(response.body.threats).toHaveLength(1);\r\n      expect(response.body.threats[0].title).toContain('APT29');\r\n    });\r\n\r\n    it('should handle pagination', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/threat-intelligence/search')\r\n        .query({\r\n          page: 1,\r\n          limit: 1,\r\n        })\r\n        .expect(200);\r\n\r\n      expect(response.body.threats).toHaveLength(1);\r\n      expect(response.body.total).toBe(2);\r\n      expect(response.body.totalPages).toBe(2);\r\n    });\r\n\r\n    it('should return empty results for no matches', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/threat-intelligence/search')\r\n        .query({\r\n          searchText: 'nonexistent',\r\n        })\r\n        .expect(200);\r\n\r\n      expect(response.body.threats).toHaveLength(0);\r\n      expect(response.body.total).toBe(0);\r\n    });\r\n  });\r\n\r\n  describe('PUT /api/threat-intelligence/:id', () => {\r\n    it('should update threat intelligence successfully', async () => {\r\n      // Create threat intelligence first\r\n      const created = await threatIntelligenceService.createThreatIntelligence({\r\n        title: 'Original Title',\r\n        description: 'Original description',\r\n        threatType: ThreatType.MALWARE,\r\n        severity: ThreatSeverity.MEDIUM,\r\n        firstSeen: new Date(),\r\n        dataSource: mockThreatIntelligence.dataSource,\r\n      }, mockUser.id);\r\n\r\n      const updateDto = {\r\n        title: 'Updated Title',\r\n        severity: ThreatSeverity.HIGH,\r\n        tags: ['updated', 'test'],\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .put(`/api/threat-intelligence/${created.id}`)\r\n        .send(updateDto)\r\n        .expect(200);\r\n\r\n      expect(response.body).toMatchObject({\r\n        id: created.id,\r\n        title: 'Updated Title',\r\n        severity: ThreatSeverity.HIGH,\r\n        tags: ['updated', 'test'],\r\n      });\r\n    });\r\n\r\n    it('should return 404 for non-existent threat intelligence', async () => {\r\n      const updateDto = {\r\n        title: 'Updated Title',\r\n      };\r\n\r\n      await request(app.getHttpServer())\r\n        .put('/api/threat-intelligence/123e4567-e89b-12d3-a456-************')\r\n        .send(updateDto)\r\n        .expect(404);\r\n    });\r\n  });\r\n\r\n  describe('DELETE /api/threat-intelligence/:id', () => {\r\n    it('should delete threat intelligence successfully', async () => {\r\n      // Create threat intelligence first\r\n      const created = await threatIntelligenceService.createThreatIntelligence({\r\n        title: 'To Delete',\r\n        description: 'Will be deleted',\r\n        threatType: ThreatType.MALWARE,\r\n        firstSeen: new Date(),\r\n        dataSource: mockThreatIntelligence.dataSource,\r\n      }, mockUser.id);\r\n\r\n      await request(app.getHttpServer())\r\n        .delete(`/api/threat-intelligence/${created.id}`)\r\n        .expect(204);\r\n\r\n      // Verify it's deleted\r\n      await request(app.getHttpServer())\r\n        .get(`/api/threat-intelligence/${created.id}`)\r\n        .expect(404);\r\n    });\r\n\r\n    it('should return 404 for non-existent threat intelligence', async () => {\r\n      await request(app.getHttpServer())\r\n        .delete('/api/threat-intelligence/123e4567-e89b-12d3-a456-************')\r\n        .expect(404);\r\n    });\r\n  });\r\n\r\n  describe('POST /api/threat-intelligence/:id/observe', () => {\r\n    it('should record observation successfully', async () => {\r\n      // Create threat intelligence first\r\n      const created = await threatIntelligenceService.createThreatIntelligence({\r\n        title: 'Observable Threat',\r\n        description: 'Test threat',\r\n        threatType: ThreatType.MALWARE,\r\n        firstSeen: new Date(),\r\n        dataSource: mockThreatIntelligence.dataSource,\r\n      }, mockUser.id);\r\n\r\n      const observationContext = {\r\n        source: 'test_system',\r\n        timestamp: new Date(),\r\n        details: 'Test observation',\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post(`/api/threat-intelligence/${created.id}/observe`)\r\n        .send({ context: observationContext })\r\n        .expect(200);\r\n\r\n      expect(response.body.observationCount).toBeGreaterThan(0);\r\n      expect(response.body.lastObserved).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('GET /api/threat-intelligence/dashboard', () => {\r\n    it('should return dashboard data', async () => {\r\n      // Create some test data\r\n      await threatIntelligenceService.createThreatIntelligence({\r\n        title: 'Dashboard Test 1',\r\n        description: 'Test',\r\n        threatType: ThreatType.APT,\r\n        severity: ThreatSeverity.CRITICAL,\r\n        firstSeen: new Date(),\r\n        dataSource: mockThreatIntelligence.dataSource,\r\n      }, mockUser.id);\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/threat-intelligence/dashboard')\r\n        .expect(200);\r\n\r\n      expect(response.body).toHaveProperty('summary');\r\n      expect(response.body).toHaveProperty('distribution');\r\n      expect(response.body).toHaveProperty('timestamp');\r\n      expect(response.body.summary).toHaveProperty('total');\r\n      expect(response.body.summary).toHaveProperty('active');\r\n      expect(response.body.summary).toHaveProperty('critical');\r\n      expect(response.body.summary).toHaveProperty('recent');\r\n    });\r\n  });\r\n\r\n  describe('POST /api/threat-intelligence/export', () => {\r\n    it('should export threat intelligence in JSON format', async () => {\r\n      // Create test data\r\n      await threatIntelligenceService.createThreatIntelligence({\r\n        title: 'Export Test',\r\n        description: 'Test export',\r\n        threatType: ThreatType.MALWARE,\r\n        firstSeen: new Date(),\r\n        dataSource: mockThreatIntelligence.dataSource,\r\n      }, mockUser.id);\r\n\r\n      const exportRequest = {\r\n        format: 'json',\r\n        criteria: {\r\n          threatTypes: [ThreatType.MALWARE],\r\n        },\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/threat-intelligence/export')\r\n        .send(exportRequest)\r\n        .expect(200);\r\n\r\n      expect(response.body).toHaveProperty('format', 'json');\r\n      expect(response.body).toHaveProperty('data');\r\n      expect(response.body).toHaveProperty('exportedAt');\r\n      expect(response.body).toHaveProperty('count');\r\n      expect(Array.isArray(response.body.data)).toBe(true);\r\n    });\r\n  });\r\n});\r\n"], "version": 3}