{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\value-objects\\timestamp.spec.ts", "mappings": ";;AAAA,uFAAuE;AAEvE,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;IACzB,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAClD,MAAM,SAAS,GAAG,kCAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAE3C,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;YACtD,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACjD,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,YAAY,GAAG,aAAa,CAAC,CAAC,2BAA2B;YAC/D,MAAM,SAAS,GAAG,kCAAS,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;YAE3D,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACtD,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,OAAO,GAAG,UAAU,CAAC,CAAC,2BAA2B;YACvD,MAAM,SAAS,GAAG,kCAAS,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAEjD,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC5C,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,SAAS,GAAG,0BAA0B,CAAC;YAC7C,MAAM,SAAS,GAAG,kCAAS,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;YAErD,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC1B,MAAM,SAAS,GAAG,kCAAS,CAAC,GAAG,EAAE,CAAC;YAClC,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAEzB,MAAM,WAAW,GAAG,SAAS,CAAC,cAAc,EAAE,CAAC;YAC/C,MAAM,CAAC,WAAW,CAAC,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;YACnD,MAAM,CAAC,WAAW,CAAC,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;YACxB,MAAM,SAAS,GAAG,IAAI,kCAAS,CAAC,IAAI,EAAE,cAAc,EAAE,kBAAkB,CAAC,CAAC;YAE1E,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACjD,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,kCAAS,CAAC,IAAW,CAAC,CAAC,CAAC,OAAO,CAAC,uCAAuC,CAAC,CAAC;QAC5F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,kCAAS,CAAC,SAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,uCAAuC,CAAC,CAAC;QACjG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,kCAAS,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,kCAAS,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;QACjG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,kCAAS,CAAC,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,yCAAyC,CAAC,CAAC;QACvH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,kCAAS,CAAC,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,yCAAyC,CAAC,CAAC;QACvH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,kCAAS,CAAC,IAAI,IAAI,EAAE,EAAE,SAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,2DAA2D,CAAC,CAAC;QACjI,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,kCAAS,CAAC,IAAI,IAAI,EAAE,EAAE,cAAc,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,qCAAqC,CAAC,CAAC;QAC7G,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,MAAM,GAAG,kCAAS,CAAC,QAAQ,CAAC,0BAA0B,CAAC,CAAC;YAC9D,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;YAC9B,MAAM,CAAC,MAAO,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,MAAM,GAAG,kCAAS,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAClD,MAAM,MAAM,GAAG,kCAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;YAC9B,MAAM,CAAC,MAAO,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACjC,MAAM,YAAY,GAAG,aAAa,CAAC;YACnC,MAAM,MAAM,GAAG,kCAAS,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;YAC9B,MAAM,CAAC,MAAO,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,IAAI,aAAwB,CAAC;QAE7B,UAAU,CAAC,GAAG,EAAE;YACd,aAAa,GAAG,kCAAS,CAAC,aAAa,CAAC,0BAA0B,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACjC,MAAM,MAAM,GAAG,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAC5B,MAAM,MAAM,GAAG,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAC5B,MAAM,MAAM,GAAG,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kBAAkB,EAAE,GAAG,EAAE;YAC1B,MAAM,MAAM,GAAG,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iBAAiB,EAAE,GAAG,EAAE;YACzB,MAAM,MAAM,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,MAAM,GAAG,aAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACjC,MAAM,MAAM,GAAG,aAAa,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACjC,MAAM,MAAM,GAAG,aAAa,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uBAAuB,EAAE,GAAG,EAAE;YAC/B,MAAM,MAAM,GAAG,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sBAAsB,EAAE,GAAG,EAAE;YAC9B,MAAM,MAAM,GAAG,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,IAAI,UAAqB,CAAC;QAC1B,IAAI,UAAqB,CAAC;QAE1B,UAAU,CAAC,GAAG,EAAE;YACd,UAAU,GAAG,kCAAS,CAAC,aAAa,CAAC,0BAA0B,CAAC,CAAC;YACjE,UAAU,GAAG,kCAAS,CAAC,aAAa,CAAC,0BAA0B,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,IAAI,GAAG,UAAU,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;YAC7D,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,2BAA2B;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,IAAI,GAAG,UAAU,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;YACxD,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,yBAAyB;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,IAAI,GAAG,UAAU,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;YACxD,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,YAAY;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,IAAI,GAAG,UAAU,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,UAAU,GAAG,kCAAS,CAAC,aAAa,CAAC,0BAA0B,CAAC,CAAC;YACvE,MAAM,IAAI,GAAG,UAAU,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;YACrD,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,IAAI,GAAG,UAAU,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;YAC7D,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,IAAI,UAAqB,CAAC;QAC1B,IAAI,UAAqB,CAAC;QAC1B,IAAI,UAAqB,CAAC;QAE1B,UAAU,CAAC,GAAG,EAAE;YACd,UAAU,GAAG,kCAAS,CAAC,aAAa,CAAC,0BAA0B,CAAC,CAAC;YACjE,UAAU,GAAG,kCAAS,CAAC,aAAa,CAAC,0BAA0B,CAAC,CAAC;YACjE,UAAU,GAAG,kCAAS,CAAC,aAAa,CAAC,0BAA0B,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wBAAwB,EAAE,GAAG,EAAE;YAChC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpD,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uBAAuB,EAAE,GAAG,EAAE;YAC/B,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sBAAsB,EAAE,GAAG,EAAE;YAC9B,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,MAAM,GAAG,kCAAS,CAAC,aAAa,CAAC,0BAA0B,CAAC,CAAC;YACnE,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClE,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtE,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,MAAM,GAAG,kCAAS,CAAC,aAAa,CAAC,0BAA0B,CAAC,CAAC;YACnE,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnE,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxE,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACjC,MAAM,SAAS,GAAG,kCAAS,CAAC,aAAa,CAAC,0BAA0B,CAAC,CAAC;YACtE,MAAM,UAAU,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;YAC1C,MAAM,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uBAAuB,EAAE,GAAG,EAAE;YAC/B,MAAM,SAAS,GAAG,kCAAS,CAAC,aAAa,CAAC,0BAA0B,CAAC,CAAC;YACtE,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;YACtC,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,SAAS,GAAG,kCAAS,CAAC,GAAG,EAAE,CAAC;YAClC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAClD,MAAM,UAAU,GAAG,IAAI,kCAAS,CAAC,IAAI,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;YAC9D,MAAM,UAAU,GAAG,IAAI,kCAAS,CAAC,IAAI,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;YAE9D,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,UAAU,GAAG,kCAAS,CAAC,aAAa,CAAC,0BAA0B,CAAC,CAAC;YACvE,MAAM,UAAU,GAAG,kCAAS,CAAC,aAAa,CAAC,0BAA0B,CAAC,CAAC;YAEvE,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAClD,MAAM,UAAU,GAAG,IAAI,kCAAS,CAAC,IAAI,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;YAC9D,MAAM,UAAU,GAAG,IAAI,kCAAS,CAAC,IAAI,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;YAE9D,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAClD,MAAM,UAAU,GAAG,IAAI,kCAAS,CAAC,IAAI,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;YAC9D,MAAM,UAAU,GAAG,IAAI,kCAAS,CAAC,IAAI,EAAE,cAAc,EAAE,kBAAkB,CAAC,CAAC;YAE3E,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,SAAS,GAAG,kCAAS,CAAC,GAAG,EAAE,CAAC;YAClC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,IAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClD,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,SAAS,GAAG,kCAAS,CAAC,GAAG,EAAE,CAAC;YAClC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,IAAI,SAAoB,CAAC;QAEzB,UAAU,CAAC,GAAG,EAAE;YACd,SAAS,GAAG,kCAAS,CAAC,aAAa,CAAC,0BAA0B,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,SAAS,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;YAC1C,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,YAAY,GAAG,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACvD,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,wBAAwB,EAAE,GAAG,EAAE;YAChC,MAAM,SAAS,GAAG,kCAAS,CAAC,aAAa,CAAC,0BAA0B,CAAC,CAAC;YACtE,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC;YAEhC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACpD,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACtC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC5C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACjC,MAAM,IAAI,GAAG;gBACX,KAAK,EAAE,0BAA0B;gBACjC,SAAS,EAAE,cAAc;gBACzB,QAAQ,EAAE,KAAK;aAChB,CAAC;YACF,MAAM,SAAS,GAAG,kCAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAE3C,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACjE,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACjD,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,IAAI,GAAG;gBACX,YAAY,EAAE,aAAa;gBAC3B,SAAS,EAAE,cAAc;gBACzB,QAAQ,EAAE,kBAAkB;aAC7B,CAAC;YACF,MAAM,SAAS,GAAG,kCAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAE3C,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACvD,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACjD,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,SAAS,GAAG,kCAAS,CAAC,GAAG,EAAE,CAAC;YAClC,MAAM,aAAa,GAAG,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAE/C,sDAAsD;YACtD,MAAM,CAAC,GAAG,EAAE;gBACT,SAAiB,CAAC,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACzC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,sDAAsD;YAExE,gCAAgC;YAChC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kBAAkB,EAAE,GAAG,EAAE;YAC1B,MAAM,SAAS,GAAG,kCAAS,CAAC,GAAG,EAAE,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,QAAQ,GAAG,kCAAS,CAAC,GAAG,EAAE,CAAC;YACjC,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAEtC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACpC,MAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,QAAQ,GAAG,kCAAS,CAAC,aAAa,CAAC,0BAA0B,CAAC,CAAC;YACrE,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,0DAA0D;YAC1D,MAAM,SAAS,GAAG,IAAI,kCAAS,CAAC,0BAA0B,EAAE,cAAc,EAAE,kBAAkB,CAAC,CAAC;YAChG,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,SAAS,GAAG,IAAI,kCAAS,CAAC,IAAI,IAAI,EAAE,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;YACnE,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,GAAG,GAAG,IAAI,kCAAS,CAAC,IAAI,IAAI,EAAE,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;YAC7D,MAAM,EAAE,GAAG,IAAI,kCAAS,CAAC,IAAI,IAAI,EAAE,EAAE,cAAc,EAAE,kBAAkB,CAAC,CAAC;YAEzE,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjC,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\value-objects\\timestamp.spec.ts"], "sourcesContent": ["import { Timestamp } from '../../value-objects/timestamp.value-object';\r\n\r\ndescribe('Timestamp', () => {\r\n  describe('creation', () => {\r\n    it('should create timestamp from Date object', () => {\r\n      const date = new Date('2023-01-01T12:00:00.000Z');\r\n      const timestamp = Timestamp.fromDate(date);\r\n\r\n      expect(timestamp.date.getTime()).toBe(date.getTime());\r\n      expect(timestamp.precision).toBe('milliseconds');\r\n      expect(timestamp.timezone).toBe('UTC');\r\n    });\r\n\r\n    it('should create timestamp from milliseconds', () => {\r\n      const milliseconds = 1672574400000; // 2023-01-01T12:00:00.000Z\r\n      const timestamp = Timestamp.fromMilliseconds(milliseconds);\r\n\r\n      expect(timestamp.toMilliseconds()).toBe(milliseconds);\r\n      expect(timestamp.precision).toBe('milliseconds');\r\n    });\r\n\r\n    it('should create timestamp from seconds', () => {\r\n      const seconds = 1672574400; // 2023-01-01T12:00:00.000Z\r\n      const timestamp = Timestamp.fromSeconds(seconds);\r\n\r\n      expect(timestamp.toSeconds()).toBe(seconds);\r\n      expect(timestamp.toMilliseconds()).toBe(seconds * 1000);\r\n    });\r\n\r\n    it('should create timestamp from ISO string', () => {\r\n      const isoString = '2023-01-01T12:00:00.000Z';\r\n      const timestamp = Timestamp.fromISOString(isoString);\r\n\r\n      expect(timestamp.toISOString()).toBe(isoString);\r\n    });\r\n\r\n    it('should create current timestamp', () => {\r\n      const before = Date.now();\r\n      const timestamp = Timestamp.now();\r\n      const after = Date.now();\r\n\r\n      const timestampMs = timestamp.toMilliseconds();\r\n      expect(timestampMs).toBeGreaterThanOrEqual(before);\r\n      expect(timestampMs).toBeLessThanOrEqual(after);\r\n    });\r\n\r\n    it('should create timestamp with custom precision and timezone', () => {\r\n      const date = new Date();\r\n      const timestamp = new Timestamp(date, 'microseconds', 'America/New_York');\r\n\r\n      expect(timestamp.precision).toBe('microseconds');\r\n      expect(timestamp.timezone).toBe('America/New_York');\r\n    });\r\n  });\r\n\r\n  describe('validation', () => {\r\n    it('should throw error for null value', () => {\r\n      expect(() => new Timestamp(null as any)).toThrow('Timestamp cannot be null or undefined');\r\n    });\r\n\r\n    it('should throw error for undefined value', () => {\r\n      expect(() => new Timestamp(undefined as any)).toThrow('Timestamp cannot be null or undefined');\r\n    });\r\n\r\n    it('should throw error for invalid date string', () => {\r\n      expect(() => new Timestamp('invalid-date')).toThrow('Invalid timestamp string');\r\n    });\r\n\r\n    it('should throw error for invalid Date object', () => {\r\n      expect(() => new Timestamp(new Date('invalid'))).toThrow('Timestamp contains an invalid date');\r\n    });\r\n\r\n    it('should throw error for date before 1970', () => {\r\n      expect(() => new Timestamp(new Date('1969-12-31T23:59:59.999Z'))).toThrow('Timestamp must be between 1970 and 2100');\r\n    });\r\n\r\n    it('should throw error for date after 2100', () => {\r\n      expect(() => new Timestamp(new Date('2101-01-01T00:00:00.000Z'))).toThrow('Timestamp must be between 1970 and 2100');\r\n    });\r\n\r\n    it('should throw error for invalid precision', () => {\r\n      expect(() => new Timestamp(new Date(), 'invalid' as any)).toThrow('Precision must be either \"milliseconds\" or \"microseconds\"');\r\n    });\r\n\r\n    it('should throw error for empty timezone', () => {\r\n      expect(() => new Timestamp(new Date(), 'milliseconds', '')).toThrow('Timezone must be a non-empty string');\r\n    });\r\n  });\r\n\r\n  describe('static validation methods', () => {\r\n    it('should try parse valid date string', () => {\r\n      const result = Timestamp.tryParse('2023-01-01T12:00:00.000Z');\r\n      expect(result).not.toBeNull();\r\n      expect(result!.toISOString()).toBe('2023-01-01T12:00:00.000Z');\r\n    });\r\n\r\n    it('should return null for invalid date in tryParse', () => {\r\n      const result = Timestamp.tryParse('invalid-date');\r\n      expect(result).toBeNull();\r\n    });\r\n\r\n    it('should try parse Date object', () => {\r\n      const date = new Date('2023-01-01T12:00:00.000Z');\r\n      const result = Timestamp.tryParse(date);\r\n      expect(result).not.toBeNull();\r\n      expect(result!.date.getTime()).toBe(date.getTime());\r\n    });\r\n\r\n    it('should try parse number', () => {\r\n      const milliseconds = 1672574400000;\r\n      const result = Timestamp.tryParse(milliseconds);\r\n      expect(result).not.toBeNull();\r\n      expect(result!.toMilliseconds()).toBe(milliseconds);\r\n    });\r\n  });\r\n\r\n  describe('arithmetic operations', () => {\r\n    let baseTimestamp: Timestamp;\r\n\r\n    beforeEach(() => {\r\n      baseTimestamp = Timestamp.fromISOString('2023-01-01T12:00:00.000Z');\r\n    });\r\n\r\n    it('should add milliseconds', () => {\r\n      const result = baseTimestamp.addMilliseconds(1000);\r\n      expect(result.toISOString()).toBe('2023-01-01T12:00:01.000Z');\r\n    });\r\n\r\n    it('should add seconds', () => {\r\n      const result = baseTimestamp.addSeconds(60);\r\n      expect(result.toISOString()).toBe('2023-01-01T12:01:00.000Z');\r\n    });\r\n\r\n    it('should add minutes', () => {\r\n      const result = baseTimestamp.addMinutes(30);\r\n      expect(result.toISOString()).toBe('2023-01-01T12:30:00.000Z');\r\n    });\r\n\r\n    it('should add hours', () => {\r\n      const result = baseTimestamp.addHours(2);\r\n      expect(result.toISOString()).toBe('2023-01-01T14:00:00.000Z');\r\n    });\r\n\r\n    it('should add days', () => {\r\n      const result = baseTimestamp.addDays(1);\r\n      expect(result.toISOString()).toBe('2023-01-02T12:00:00.000Z');\r\n    });\r\n\r\n    it('should subtract milliseconds', () => {\r\n      const result = baseTimestamp.subtractMilliseconds(1000);\r\n      expect(result.toISOString()).toBe('2023-01-01T11:59:59.000Z');\r\n    });\r\n\r\n    it('should subtract seconds', () => {\r\n      const result = baseTimestamp.subtractSeconds(60);\r\n      expect(result.toISOString()).toBe('2023-01-01T11:59:00.000Z');\r\n    });\r\n\r\n    it('should subtract minutes', () => {\r\n      const result = baseTimestamp.subtractMinutes(30);\r\n      expect(result.toISOString()).toBe('2023-01-01T11:30:00.000Z');\r\n    });\r\n\r\n    it('should subtract hours', () => {\r\n      const result = baseTimestamp.subtractHours(2);\r\n      expect(result.toISOString()).toBe('2023-01-01T10:00:00.000Z');\r\n    });\r\n\r\n    it('should subtract days', () => {\r\n      const result = baseTimestamp.subtractDays(1);\r\n      expect(result.toISOString()).toBe('2022-12-31T12:00:00.000Z');\r\n    });\r\n  });\r\n\r\n  describe('difference calculations', () => {\r\n    let timestamp1: Timestamp;\r\n    let timestamp2: Timestamp;\r\n\r\n    beforeEach(() => {\r\n      timestamp1 = Timestamp.fromISOString('2023-01-01T12:00:00.000Z');\r\n      timestamp2 = Timestamp.fromISOString('2023-01-01T14:30:45.500Z');\r\n    });\r\n\r\n    it('should calculate difference in milliseconds', () => {\r\n      const diff = timestamp2.differenceInMilliseconds(timestamp1);\r\n      expect(diff).toBe(9045500); // 2.5 hours + 45.5 seconds\r\n    });\r\n\r\n    it('should calculate difference in seconds', () => {\r\n      const diff = timestamp2.differenceInSeconds(timestamp1);\r\n      expect(diff).toBe(9045); // 2.5 hours + 45 seconds\r\n    });\r\n\r\n    it('should calculate difference in minutes', () => {\r\n      const diff = timestamp2.differenceInMinutes(timestamp1);\r\n      expect(diff).toBe(150); // 2.5 hours\r\n    });\r\n\r\n    it('should calculate difference in hours', () => {\r\n      const diff = timestamp2.differenceInHours(timestamp1);\r\n      expect(diff).toBe(2);\r\n    });\r\n\r\n    it('should calculate difference in days', () => {\r\n      const timestamp3 = Timestamp.fromISOString('2023-01-03T12:00:00.000Z');\r\n      const diff = timestamp3.differenceInDays(timestamp1);\r\n      expect(diff).toBe(2);\r\n    });\r\n\r\n    it('should handle negative differences', () => {\r\n      const diff = timestamp1.differenceInMilliseconds(timestamp2);\r\n      expect(diff).toBe(-9045500);\r\n    });\r\n  });\r\n\r\n  describe('comparison operations', () => {\r\n    let timestamp1: Timestamp;\r\n    let timestamp2: Timestamp;\r\n    let timestamp3: Timestamp;\r\n\r\n    beforeEach(() => {\r\n      timestamp1 = Timestamp.fromISOString('2023-01-01T12:00:00.000Z');\r\n      timestamp2 = Timestamp.fromISOString('2023-01-01T14:00:00.000Z');\r\n      timestamp3 = Timestamp.fromISOString('2023-01-01T12:00:00.000Z');\r\n    });\r\n\r\n    it('should check if before', () => {\r\n      expect(timestamp1.isBefore(timestamp2)).toBe(true);\r\n      expect(timestamp2.isBefore(timestamp1)).toBe(false);\r\n      expect(timestamp1.isBefore(timestamp3)).toBe(false);\r\n    });\r\n\r\n    it('should check if after', () => {\r\n      expect(timestamp2.isAfter(timestamp1)).toBe(true);\r\n      expect(timestamp1.isAfter(timestamp2)).toBe(false);\r\n      expect(timestamp1.isAfter(timestamp3)).toBe(false);\r\n    });\r\n\r\n    it('should check if same', () => {\r\n      expect(timestamp1.isSame(timestamp3)).toBe(true);\r\n      expect(timestamp1.isSame(timestamp2)).toBe(false);\r\n    });\r\n\r\n    it('should check if between (inclusive)', () => {\r\n      const middle = Timestamp.fromISOString('2023-01-01T13:00:00.000Z');\r\n      expect(middle.isBetween(timestamp1, timestamp2, true)).toBe(true);\r\n      expect(timestamp1.isBetween(timestamp1, timestamp2, true)).toBe(true);\r\n      expect(timestamp2.isBetween(timestamp1, timestamp2, true)).toBe(true);\r\n    });\r\n\r\n    it('should check if between (exclusive)', () => {\r\n      const middle = Timestamp.fromISOString('2023-01-01T13:00:00.000Z');\r\n      expect(middle.isBetween(timestamp1, timestamp2, false)).toBe(true);\r\n      expect(timestamp1.isBetween(timestamp1, timestamp2, false)).toBe(false);\r\n      expect(timestamp2.isBetween(timestamp1, timestamp2, false)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('day boundaries', () => {\r\n    it('should get start of day', () => {\r\n      const timestamp = Timestamp.fromISOString('2023-01-01T14:30:45.123Z');\r\n      const startOfDay = timestamp.startOfDay();\r\n      expect(startOfDay.toISOString()).toBe('2023-01-01T00:00:00.000Z');\r\n    });\r\n\r\n    it('should get end of day', () => {\r\n      const timestamp = Timestamp.fromISOString('2023-01-01T14:30:45.123Z');\r\n      const endOfDay = timestamp.endOfDay();\r\n      expect(endOfDay.toISOString()).toBe('2023-01-01T23:59:59.999Z');\r\n    });\r\n  });\r\n\r\n  describe('equality and comparison', () => {\r\n    it('should be equal to itself', () => {\r\n      const timestamp = Timestamp.now();\r\n      expect(timestamp.equals(timestamp)).toBe(true);\r\n    });\r\n\r\n    it('should be equal to timestamp with same value and properties', () => {\r\n      const date = new Date('2023-01-01T12:00:00.000Z');\r\n      const timestamp1 = new Timestamp(date, 'milliseconds', 'UTC');\r\n      const timestamp2 = new Timestamp(date, 'milliseconds', 'UTC');\r\n\r\n      expect(timestamp1.equals(timestamp2)).toBe(true);\r\n    });\r\n\r\n    it('should not be equal to timestamp with different value', () => {\r\n      const timestamp1 = Timestamp.fromISOString('2023-01-01T12:00:00.000Z');\r\n      const timestamp2 = Timestamp.fromISOString('2023-01-01T13:00:00.000Z');\r\n\r\n      expect(timestamp1.equals(timestamp2)).toBe(false);\r\n    });\r\n\r\n    it('should not be equal to timestamp with different precision', () => {\r\n      const date = new Date('2023-01-01T12:00:00.000Z');\r\n      const timestamp1 = new Timestamp(date, 'milliseconds', 'UTC');\r\n      const timestamp2 = new Timestamp(date, 'microseconds', 'UTC');\r\n\r\n      expect(timestamp1.equals(timestamp2)).toBe(false);\r\n    });\r\n\r\n    it('should not be equal to timestamp with different timezone', () => {\r\n      const date = new Date('2023-01-01T12:00:00.000Z');\r\n      const timestamp1 = new Timestamp(date, 'milliseconds', 'UTC');\r\n      const timestamp2 = new Timestamp(date, 'milliseconds', 'America/New_York');\r\n\r\n      expect(timestamp1.equals(timestamp2)).toBe(false);\r\n    });\r\n\r\n    it('should not be equal to null or undefined', () => {\r\n      const timestamp = Timestamp.now();\r\n      expect(timestamp.equals(null as any)).toBe(false);\r\n      expect(timestamp.equals(undefined as any)).toBe(false);\r\n    });\r\n\r\n    it('should not be equal to non-Timestamp object', () => {\r\n      const timestamp = Timestamp.now();\r\n      expect(timestamp.equals({} as any)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('string representations', () => {\r\n    let timestamp: Timestamp;\r\n\r\n    beforeEach(() => {\r\n      timestamp = Timestamp.fromISOString('2023-01-01T12:00:00.000Z');\r\n    });\r\n\r\n    it('should convert to ISO string', () => {\r\n      expect(timestamp.toISOString()).toBe('2023-01-01T12:00:00.000Z');\r\n    });\r\n\r\n    it('should convert to UTC string', () => {\r\n      const utcString = timestamp.toUTCString();\r\n      expect(utcString).toBe('Sun, 01 Jan 2023 12:00:00 GMT');\r\n    });\r\n\r\n    it('should convert to locale string', () => {\r\n      const localeString = timestamp.toLocaleString('en-US');\r\n      expect(localeString).toContain('2023');\r\n    });\r\n\r\n    it('should convert to string (ISO format)', () => {\r\n      expect(timestamp.toString()).toBe('2023-01-01T12:00:00.000Z');\r\n    });\r\n  });\r\n\r\n  describe('serialization', () => {\r\n    it('should convert to JSON', () => {\r\n      const timestamp = Timestamp.fromISOString('2023-01-01T12:00:00.000Z');\r\n      const json = timestamp.toJSON();\r\n\r\n      expect(json.value).toBe('2023-01-01T12:00:00.000Z');\r\n      expect(json.milliseconds).toBe(1672574400000);\r\n      expect(json.seconds).toBe(1672574400);\r\n      expect(json.precision).toBe('milliseconds');\r\n      expect(json.timezone).toBe('UTC');\r\n      expect(json.type).toBe('Timestamp');\r\n    });\r\n\r\n    it('should create from JSON', () => {\r\n      const json = {\r\n        value: '2023-01-01T12:00:00.000Z',\r\n        precision: 'milliseconds',\r\n        timezone: 'UTC'\r\n      };\r\n      const timestamp = Timestamp.fromJSON(json);\r\n\r\n      expect(timestamp.toISOString()).toBe('2023-01-01T12:00:00.000Z');\r\n      expect(timestamp.precision).toBe('milliseconds');\r\n      expect(timestamp.timezone).toBe('UTC');\r\n    });\r\n\r\n    it('should create from JSON with milliseconds', () => {\r\n      const json = {\r\n        milliseconds: 1672574400000,\r\n        precision: 'microseconds',\r\n        timezone: 'America/New_York'\r\n      };\r\n      const timestamp = Timestamp.fromJSON(json);\r\n\r\n      expect(timestamp.toMilliseconds()).toBe(1672574400000);\r\n      expect(timestamp.precision).toBe('microseconds');\r\n      expect(timestamp.timezone).toBe('America/New_York');\r\n    });\r\n  });\r\n\r\n  describe('immutability', () => {\r\n    it('should be immutable after creation', () => {\r\n      const timestamp = Timestamp.now();\r\n      const originalValue = timestamp.date.getTime();\r\n\r\n      // Attempt to modify (should not work due to readonly)\r\n      expect(() => {\r\n        (timestamp as any)._value = new Date();\r\n      }).not.toThrow(); // TypeScript prevents this, but runtime doesn't throw\r\n\r\n      // Value should remain unchanged\r\n      expect(timestamp.date.getTime()).toBe(originalValue);\r\n    });\r\n\r\n    it('should be frozen', () => {\r\n      const timestamp = Timestamp.now();\r\n      expect(Object.isFrozen(timestamp)).toBe(true);\r\n    });\r\n\r\n    it('should return new instances for arithmetic operations', () => {\r\n      const original = Timestamp.now();\r\n      const modified = original.addHours(1);\r\n\r\n      expect(modified).not.toBe(original);\r\n      expect(modified.differenceInHours(original)).toBe(1);\r\n    });\r\n  });\r\n\r\n  describe('edge cases', () => {\r\n    it('should handle leap year dates', () => {\r\n      const leapYear = Timestamp.fromISOString('2020-02-29T12:00:00.000Z');\r\n      expect(leapYear.isValid()).toBe(true);\r\n    });\r\n\r\n    it('should handle daylight saving time transitions', () => {\r\n      // This test assumes the system can handle DST transitions\r\n      const timestamp = new Timestamp('2023-03-12T07:00:00.000Z', 'milliseconds', 'America/New_York');\r\n      expect(timestamp.isValid()).toBe(true);\r\n    });\r\n\r\n    it('should handle microsecond precision', () => {\r\n      const timestamp = new Timestamp(new Date(), 'microseconds', 'UTC');\r\n      expect(timestamp.precision).toBe('microseconds');\r\n    });\r\n\r\n    it('should handle different timezones', () => {\r\n      const utc = new Timestamp(new Date(), 'milliseconds', 'UTC');\r\n      const ny = new Timestamp(new Date(), 'milliseconds', 'America/New_York');\r\n      \r\n      expect(utc.timezone).toBe('UTC');\r\n      expect(ny.timezone).toBe('America/New_York');\r\n    });\r\n  });\r\n});"], "version": 3}