{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\types\\audit.types.spec.ts", "mappings": ";AAAA;;GAEG;;AAEH,yDAQiC;AAEjC,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;IAC1B,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,KAAK,GAAG,wBAAU,CAAC,WAAW,CAClC,yBAAW,CAAC,MAAM,EAClB,yBAAW,CAAC,OAAO,EACnB,2BAA2B,CAC5B,CAAC;YAEF,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;YAC/B,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,yBAAW,CAAC,MAAM,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,yBAAW,CAAC,OAAO,CAAC,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAC5D,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,KAAK,GAAG,wBAAU,CAAC,WAAW,CAClC,yBAAW,CAAC,MAAM,EAClB,yBAAW,CAAC,OAAO,EACnB,uBAAuB,EACvB;gBACE,QAAQ,EAAE,2BAAa,CAAC,QAAQ;gBAChC,QAAQ,EAAE,2BAAa,CAAC,iBAAiB;gBACzC,IAAI,EAAE;oBACJ,MAAM,EAAE,SAAS;oBACjB,QAAQ,EAAE,UAAU;iBACrB;gBACD,QAAQ,EAAE;oBACR,YAAY,EAAE,MAAM;oBACpB,UAAU,EAAE,KAAK;iBAClB;aACF,CACF,CAAC;YAEF,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,2BAAa,CAAC,QAAQ,CAAC,CAAC;YACpD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,2BAAa,CAAC,iBAAiB,CAAC,CAAC;YAC7D,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC3C,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,QAAQ,GAAG,wBAAU,CAAC,aAAa,CAAC,yBAAW,CAAC,MAAM,EAAE,yBAAW,CAAC,OAAO,CAAC,CAAC;YACnF,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,2BAAa,CAAC,IAAI,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,QAAQ,GAAG,wBAAU,CAAC,aAAa,CAAC,yBAAW,CAAC,KAAK,EAAE,yBAAW,CAAC,OAAO,CAAC,CAAC;YAClF,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,2BAAa,CAAC,QAAQ,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,QAAQ,GAAG,wBAAU,CAAC,aAAa,CAAC,yBAAW,CAAC,MAAM,EAAE,yBAAW,CAAC,OAAO,CAAC,CAAC;YACnF,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,2BAAa,CAAC,MAAM,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,QAAQ,GAAG,wBAAU,CAAC,aAAa,CAAC,yBAAW,CAAC,IAAI,EAAE,yBAAW,CAAC,OAAO,CAAC,CAAC;YACjF,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,2BAAa,CAAC,GAAG,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,CAAC,wBAAU,CAAC,aAAa,CAAC,yBAAW,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,2BAAa,CAAC,cAAc,CAAC,CAAC;YACvF,MAAM,CAAC,wBAAU,CAAC,aAAa,CAAC,yBAAW,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,2BAAa,CAAC,cAAc,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,CAAC,wBAAU,CAAC,aAAa,CAAC,yBAAW,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,2BAAa,CAAC,aAAa,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,CAAC,wBAAU,CAAC,aAAa,CAAC,yBAAW,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,2BAAa,CAAC,WAAW,CAAC,CAAC;YACnF,MAAM,CAAC,wBAAU,CAAC,aAAa,CAAC,yBAAW,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,2BAAa,CAAC,WAAW,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,CAAC,wBAAU,CAAC,aAAa,CAAC,yBAAW,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,2BAAa,CAAC,iBAAiB,CAAC,CAAC;YAC3F,MAAM,CAAC,wBAAU,CAAC,aAAa,CAAC,yBAAW,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,2BAAa,CAAC,iBAAiB,CAAC,CAAC;YAC3F,MAAM,CAAC,wBAAU,CAAC,aAAa,CAAC,yBAAW,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,2BAAa,CAAC,iBAAiB,CAAC,CAAC;QAC7F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,MAAM,CAAC,wBAAU,CAAC,aAAa,CAAC,yBAAW,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,2BAAa,CAAC,oBAAoB,CAAC,CAAC;YACjG,MAAM,CAAC,wBAAU,CAAC,aAAa,CAAC,yBAAW,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,2BAAa,CAAC,oBAAoB,CAAC,CAAC;QAChG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,KAAK,GAAwB;gBACjC,QAAQ,EAAE,2BAAa,CAAC,QAAQ;gBAChC,MAAM,EAAE,yBAAW,CAAC,IAAI;gBACxB,MAAM,EAAE,yBAAW,CAAC,OAAO;aAC5B,CAAC;YAEF,MAAM,KAAK,GAAG,wBAAU,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,+BAA+B;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,KAAK,GAAwB;gBACjC,QAAQ,EAAE,2BAAa,CAAC,MAAM;gBAC9B,MAAM,EAAE,yBAAW,CAAC,MAAM;gBAC1B,MAAM,EAAE,yBAAW,CAAC,OAAO;aAC5B,CAAC;YAEF,MAAM,KAAK,GAAG,wBAAU,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,iCAAiC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,KAAK,GAAwB;gBACjC,QAAQ,EAAE,2BAAa,CAAC,GAAG;gBAC3B,MAAM,EAAE,yBAAW,CAAC,IAAI;gBACxB,MAAM,EAAE,yBAAW,CAAC,OAAO;aAC5B,CAAC;YAEF,MAAM,KAAK,GAAG,wBAAU,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,6BAA6B;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,KAAK,GAAwB;gBACjC,QAAQ,EAAE,2BAAa,CAAC,GAAG;gBAC3B,MAAM,EAAE,yBAAW,CAAC,IAAI;gBACxB,MAAM,EAAE,yBAAW,CAAC,OAAO;gBAC3B,IAAI,EAAE;oBACJ,MAAM,EAAE,OAAO;oBACf,KAAK,EAAE,CAAC,OAAO,CAAC;iBACjB;aACF,CAAC;YAEF,MAAM,KAAK,GAAG,wBAAU,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,2BAA2B;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;YAChC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO;YAEjC,MAAM,KAAK,GAAwB;gBACjC,QAAQ,EAAE,2BAAa,CAAC,GAAG;gBAC3B,MAAM,EAAE,yBAAW,CAAC,IAAI;gBACxB,MAAM,EAAE,yBAAW,CAAC,OAAO;gBAC3B,SAAS,EAAE,YAAY;aACxB,CAAC;YAEF,MAAM,KAAK,GAAG,wBAAU,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,+BAA+B;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,KAAK,GAAwB;gBACjC,QAAQ,EAAE,2BAAa,CAAC,QAAQ;gBAChC,MAAM,EAAE,yBAAW,CAAC,MAAM;gBAC1B,MAAM,EAAE,yBAAW,CAAC,OAAO;gBAC3B,IAAI,EAAE;oBACJ,MAAM,EAAE,OAAO;oBACf,KAAK,EAAE,CAAC,OAAO,CAAC;oBAChB,SAAS,EAAE,aAAa;oBACxB,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;iBAC5B;gBACD,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;aAC5C,CAAC;YAEF,MAAM,KAAK,GAAG,wBAAU,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,mEAAmE,EAAE,GAAG,EAAE;YAC3E,MAAM,KAAK,GAAe;gBACxB,EAAE,EAAE,SAAS;gBACb,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,yBAAW,CAAC,KAAK;gBACzB,MAAM,EAAE,yBAAW,CAAC,OAAO;gBAC3B,QAAQ,EAAE,2BAAa,CAAC,GAAG;gBAC3B,QAAQ,EAAE,2BAAa,CAAC,cAAc;gBACtC,WAAW,EAAE,YAAY;gBACzB,MAAM,EAAE,EAAE,WAAW,EAAE,MAAM,EAAE;gBAC/B,IAAI,EAAE;oBACJ,MAAM,EAAE,SAAS;oBACjB,KAAK,EAAE,kBAAkB;oBACzB,SAAS,EAAE,eAAe;iBAC3B;gBACD,OAAO,EAAE;oBACP,MAAM,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE;oBAC/B,KAAK,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE;iBAC/B;gBACD,QAAQ,EAAE;oBACR,KAAK,EAAE,cAAc;oBACrB,UAAU,EAAE,WAAW;iBACxB;aACF,CAAC;YAEF,MAAM,MAAM,GAAG;gBACb,GAAG,wBAAU,CAAC,cAAc;gBAC5B,oBAAoB,EAAE,KAAK;gBAC3B,kBAAkB,EAAE,IAAI;aACzB,CAAC;YAEF,MAAM,SAAS,GAAG,wBAAU,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAE1D,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;YAC/D,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;YACtE,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACnE,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAClE,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACzD,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,KAAK,GAAe;gBACxB,EAAE,EAAE,SAAS;gBACb,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,yBAAW,CAAC,MAAM;gBAC1B,MAAM,EAAE,yBAAW,CAAC,OAAO;gBAC3B,QAAQ,EAAE,2BAAa,CAAC,MAAM;gBAC9B,QAAQ,EAAE,2BAAa,CAAC,iBAAiB;gBACzC,WAAW,EAAE,cAAc;gBAC3B,MAAM,EAAE,EAAE,WAAW,EAAE,MAAM,EAAE;gBAC/B,OAAO,EAAE;oBACP,MAAM,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE;oBAC5B,KAAK,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE;iBAC5B;aACF,CAAC;YAEF,MAAM,MAAM,GAAG;gBACb,GAAG,wBAAU,CAAC,cAAc;gBAC5B,kBAAkB,EAAE,KAAK;aAC1B,CAAC;YAEF,MAAM,SAAS,GAAG,wBAAU,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAE1D,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,KAAK,GAAwB;gBACjC,MAAM,EAAE,yBAAW,CAAC,IAAI;aACzB,CAAC;YAEF,MAAM,MAAM,GAAG;gBACb,GAAG,wBAAU,CAAC,cAAc;gBAC5B,OAAO,EAAE,KAAK;aACf,CAAC;YAEF,MAAM,MAAM,GAAG,wBAAU,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,KAAK,GAAwB;gBACjC,MAAM,EAAE,yBAAW,CAAC,IAAI;aACzB,CAAC;YAEF,MAAM,MAAM,GAAG;gBACb,GAAG,wBAAU,CAAC,cAAc;gBAC5B,cAAc,EAAE,CAAC,yBAAW,CAAC,MAAM,EAAE,yBAAW,CAAC,MAAM,CAAC;aACzD,CAAC;YAEF,MAAM,MAAM,GAAG,wBAAU,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,KAAK,GAAwB;gBACjC,MAAM,EAAE,yBAAW,CAAC,IAAI;gBACxB,QAAQ,EAAE,2BAAa,CAAC,WAAW;aACpC,CAAC;YAEF,MAAM,MAAM,GAAG;gBACb,GAAG,wBAAU,CAAC,cAAc;gBAC5B,iBAAiB,EAAE,CAAC,2BAAa,CAAC,cAAc,CAAC;aAClD,CAAC;YAEF,MAAM,MAAM,GAAG,wBAAU,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,KAAK,GAAwB;gBACjC,MAAM,EAAE,yBAAW,CAAC,IAAI;gBACxB,QAAQ,EAAE,2BAAa,CAAC,GAAG;aAC5B,CAAC;YAEF,MAAM,MAAM,GAAG;gBACb,GAAG,wBAAU,CAAC,cAAc;gBAC5B,eAAe,EAAE,2BAAa,CAAC,MAAM;aACtC,CAAC;YAEF,MAAM,MAAM,GAAG,wBAAU,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,KAAK,GAAwB;gBACjC,MAAM,EAAE,yBAAW,CAAC,MAAM;gBAC1B,QAAQ,EAAE,2BAAa,CAAC,iBAAiB;gBACzC,QAAQ,EAAE,2BAAa,CAAC,MAAM;aAC/B,CAAC;YAEF,MAAM,MAAM,GAAG,wBAAU,CAAC,WAAW,CAAC,KAAK,EAAE,wBAAU,CAAC,cAAc,CAAC,CAAC;YACxE,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,KAAK,GAAe;gBACxB,EAAE,EAAE,SAAS;gBACb,SAAS,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;gBAC3C,MAAM,EAAE,yBAAW,CAAC,MAAM;gBAC1B,MAAM,EAAE,yBAAW,CAAC,OAAO;gBAC3B,QAAQ,EAAE,2BAAa,CAAC,MAAM;gBAC9B,QAAQ,EAAE,2BAAa,CAAC,iBAAiB;gBACzC,WAAW,EAAE,cAAc;gBAC3B,MAAM,EAAE,EAAE,WAAW,EAAE,MAAM,EAAE;gBAC/B,IAAI,EAAE;oBACJ,MAAM,EAAE,SAAS;oBACjB,QAAQ,EAAE,UAAU;iBACrB;gBACD,QAAQ,EAAE;oBACR,YAAY,EAAE,MAAM;oBACpB,UAAU,EAAE,KAAK;iBAClB;aACF,CAAC;YAEF,MAAM,SAAS,GAAG,wBAAU,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAEhD,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;YACxD,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YACxC,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YACtC,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YACxC,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YACvC,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,KAAK,GAAe;gBACxB,EAAE,EAAE,SAAS;gBACb,SAAS,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;gBAC3C,MAAM,EAAE,yBAAW,CAAC,MAAM;gBAC1B,MAAM,EAAE,yBAAW,CAAC,OAAO;gBAC3B,QAAQ,EAAE,2BAAa,CAAC,MAAM;gBAC9B,QAAQ,EAAE,2BAAa,CAAC,iBAAiB;gBACzC,WAAW,EAAE,eAAe;gBAC5B,MAAM,EAAE,EAAE,WAAW,EAAE,MAAM,EAAE;aAChC,CAAC;YAEF,MAAM,SAAS,GAAG,wBAAU,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAEhD,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YACtC,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,MAAM,GAAqB;gBAC/B,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACjC,OAAO,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBAC/B,MAAM,EAAE,SAAS;gBACjB,OAAO,EAAE,CAAC,yBAAW,CAAC,MAAM,EAAE,yBAAW,CAAC,MAAM,CAAC;gBACjD,OAAO,EAAE,CAAC,yBAAW,CAAC,OAAO,CAAC;gBAC9B,UAAU,EAAE,CAAC,2BAAa,CAAC,IAAI,CAAC;gBAChC,UAAU,EAAE,CAAC,2BAAa,CAAC,iBAAiB,CAAC;gBAC7C,aAAa,EAAE,CAAC,MAAM,CAAC;gBACvB,WAAW,EAAE,CAAC,KAAK,CAAC;gBACpB,MAAM,EAAE,aAAa;gBACrB,YAAY,EAAE,EAAE;gBAChB,YAAY,EAAE,EAAE;gBAChB,cAAc,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;gBAC/B,QAAQ,EAAE,WAAW;gBACrB,YAAY,EAAE,CAAC,MAAM,CAAC;gBACtB,YAAY,EAAE,CAAC,MAAM,CAAC;aACvB,CAAC;YAEF,MAAM,KAAK,GAAG,wBAAU,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAE7C,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACvD,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACrD,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACjD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACjD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACjD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACtD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACtD,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YACzE,MAAM,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YACrE,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC;YAChC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YACvD,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YACvD,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAChE,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACvD,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YACrE,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,MAAM,GAAqB,EAAE,CAAC;YACpC,MAAM,KAAK,GAAG,wBAAU,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAE7C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,MAAM,GAAqB;gBAC/B,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;aAClC,CAAC;YAEF,MAAM,KAAK,GAAG,wBAAU,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAE7C,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACvD,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\types\\audit.types.spec.ts"], "sourcesContent": ["/**\r\n * Audit Types Tests\r\n */\r\n\r\nimport {\r\n  AuditAction,\r\n  AuditResult,\r\n  AuditSeverity,\r\n  AuditCategory,\r\n  AuditUtils,\r\n  AuditEntry,\r\n  AuditQueryParams,\r\n} from '../../types/audit.types';\r\n\r\ndescribe('AuditUtils', () => {\r\n  describe('createEntry', () => {\r\n    it('should create a basic audit entry', () => {\r\n      const entry = AuditUtils.createEntry(\r\n        AuditAction.CREATE,\r\n        AuditResult.SUCCESS,\r\n        'User created successfully'\r\n      );\r\n\r\n      expect(entry.id).toBeDefined();\r\n      expect(entry.timestamp).toBeInstanceOf(Date);\r\n      expect(entry.action).toBe(AuditAction.CREATE);\r\n      expect(entry.result).toBe(AuditResult.SUCCESS);\r\n      expect(entry.description).toBe('User created successfully');\r\n      expect(entry.system.application).toBe('sentinel');\r\n    });\r\n\r\n    it('should create an audit entry with custom options', () => {\r\n      const entry = AuditUtils.createEntry(\r\n        AuditAction.DELETE,\r\n        AuditResult.FAILURE,\r\n        'Failed to delete user',\r\n        {\r\n          severity: AuditSeverity.CRITICAL,\r\n          category: AuditCategory.DATA_MODIFICATION,\r\n          user: {\r\n            userId: 'user123',\r\n            username: 'testuser',\r\n          },\r\n          resource: {\r\n            resourceType: 'User',\r\n            resourceId: '456',\r\n          },\r\n        }\r\n      );\r\n\r\n      expect(entry.severity).toBe(AuditSeverity.CRITICAL);\r\n      expect(entry.category).toBe(AuditCategory.DATA_MODIFICATION);\r\n      expect(entry.user?.userId).toBe('user123');\r\n      expect(entry.resource?.resourceType).toBe('User');\r\n    });\r\n  });\r\n\r\n  describe('inferSeverity', () => {\r\n    it('should infer high severity for critical actions', () => {\r\n      const severity = AuditUtils.inferSeverity(AuditAction.DELETE, AuditResult.SUCCESS);\r\n      expect(severity).toBe(AuditSeverity.HIGH);\r\n    });\r\n\r\n    it('should infer critical severity for failed critical actions', () => {\r\n      const severity = AuditUtils.inferSeverity(AuditAction.PURGE, AuditResult.FAILURE);\r\n      expect(severity).toBe(AuditSeverity.CRITICAL);\r\n    });\r\n\r\n    it('should infer medium severity for moderate actions', () => {\r\n      const severity = AuditUtils.inferSeverity(AuditAction.UPDATE, AuditResult.SUCCESS);\r\n      expect(severity).toBe(AuditSeverity.MEDIUM);\r\n    });\r\n\r\n    it('should infer low severity for read actions', () => {\r\n      const severity = AuditUtils.inferSeverity(AuditAction.READ, AuditResult.SUCCESS);\r\n      expect(severity).toBe(AuditSeverity.LOW);\r\n    });\r\n  });\r\n\r\n  describe('inferCategory', () => {\r\n    it('should infer authentication category for login/logout', () => {\r\n      expect(AuditUtils.inferCategory(AuditAction.LOGIN)).toBe(AuditCategory.AUTHENTICATION);\r\n      expect(AuditUtils.inferCategory(AuditAction.LOGOUT)).toBe(AuditCategory.AUTHENTICATION);\r\n    });\r\n\r\n    it('should infer authorization category for access', () => {\r\n      expect(AuditUtils.inferCategory(AuditAction.ACCESS)).toBe(AuditCategory.AUTHORIZATION);\r\n    });\r\n\r\n    it('should infer data access category for read/export', () => {\r\n      expect(AuditUtils.inferCategory(AuditAction.READ)).toBe(AuditCategory.DATA_ACCESS);\r\n      expect(AuditUtils.inferCategory(AuditAction.EXPORT)).toBe(AuditCategory.DATA_ACCESS);\r\n    });\r\n\r\n    it('should infer data modification category for CRUD operations', () => {\r\n      expect(AuditUtils.inferCategory(AuditAction.CREATE)).toBe(AuditCategory.DATA_MODIFICATION);\r\n      expect(AuditUtils.inferCategory(AuditAction.UPDATE)).toBe(AuditCategory.DATA_MODIFICATION);\r\n      expect(AuditUtils.inferCategory(AuditAction.DELETE)).toBe(AuditCategory.DATA_MODIFICATION);\r\n    });\r\n\r\n    it('should infer system configuration category for config actions', () => {\r\n      expect(AuditUtils.inferCategory(AuditAction.CONFIGURE)).toBe(AuditCategory.SYSTEM_CONFIGURATION);\r\n      expect(AuditUtils.inferCategory(AuditAction.BACKUP)).toBe(AuditCategory.SYSTEM_CONFIGURATION);\r\n    });\r\n  });\r\n\r\n  describe('calculateRiskScore', () => {\r\n    it('should calculate risk score based on severity', () => {\r\n      const entry: Partial<AuditEntry> = {\r\n        severity: AuditSeverity.CRITICAL,\r\n        action: AuditAction.READ,\r\n        result: AuditResult.SUCCESS,\r\n      };\r\n\r\n      const score = AuditUtils.calculateRiskScore(entry);\r\n      expect(score).toBeGreaterThan(40); // Base critical severity score\r\n    });\r\n\r\n    it('should increase risk score for high-risk actions', () => {\r\n      const entry: Partial<AuditEntry> = {\r\n        severity: AuditSeverity.MEDIUM,\r\n        action: AuditAction.DELETE,\r\n        result: AuditResult.SUCCESS,\r\n      };\r\n\r\n      const score = AuditUtils.calculateRiskScore(entry);\r\n      expect(score).toBeGreaterThan(20); // Base medium + high-risk action\r\n    });\r\n\r\n    it('should increase risk score for failures', () => {\r\n      const entry: Partial<AuditEntry> = {\r\n        severity: AuditSeverity.LOW,\r\n        action: AuditAction.READ,\r\n        result: AuditResult.FAILURE,\r\n      };\r\n\r\n      const score = AuditUtils.calculateRiskScore(entry);\r\n      expect(score).toBeGreaterThan(10); // Base low + failure penalty\r\n    });\r\n\r\n    it('should increase risk score for admin users', () => {\r\n      const entry: Partial<AuditEntry> = {\r\n        severity: AuditSeverity.LOW,\r\n        action: AuditAction.READ,\r\n        result: AuditResult.SUCCESS,\r\n        user: {\r\n          userId: 'admin',\r\n          roles: ['admin'],\r\n        },\r\n      };\r\n\r\n      const score = AuditUtils.calculateRiskScore(entry);\r\n      expect(score).toBeGreaterThan(10); // Base low + admin penalty\r\n    });\r\n\r\n    it('should increase risk score for off-hours access', () => {\r\n      const offHoursDate = new Date();\r\n      offHoursDate.setHours(2); // 2 AM\r\n\r\n      const entry: Partial<AuditEntry> = {\r\n        severity: AuditSeverity.LOW,\r\n        action: AuditAction.READ,\r\n        result: AuditResult.SUCCESS,\r\n        timestamp: offHoursDate,\r\n      };\r\n\r\n      const score = AuditUtils.calculateRiskScore(entry);\r\n      expect(score).toBeGreaterThan(10); // Base low + off-hours penalty\r\n    });\r\n\r\n    it('should cap risk score at 100', () => {\r\n      const entry: Partial<AuditEntry> = {\r\n        severity: AuditSeverity.CRITICAL,\r\n        action: AuditAction.DELETE,\r\n        result: AuditResult.FAILURE,\r\n        user: {\r\n          userId: 'admin',\r\n          roles: ['admin'],\r\n          ipAddress: '***********',\r\n          location: { country: 'US' },\r\n        },\r\n        timestamp: new Date(new Date().setHours(2)),\r\n      };\r\n\r\n      const score = AuditUtils.calculateRiskScore(entry);\r\n      expect(score).toBeLessThanOrEqual(100);\r\n    });\r\n  });\r\n\r\n  describe('sanitizeEntry', () => {\r\n    it('should sanitize sensitive data when includeSensitiveData is false', () => {\r\n      const entry: AuditEntry = {\r\n        id: 'audit-1',\r\n        timestamp: new Date(),\r\n        action: AuditAction.LOGIN,\r\n        result: AuditResult.SUCCESS,\r\n        severity: AuditSeverity.LOW,\r\n        category: AuditCategory.AUTHENTICATION,\r\n        description: 'User login',\r\n        system: { application: 'test' },\r\n        user: {\r\n          userId: 'user123',\r\n          email: '<EMAIL>',\r\n          ipAddress: '*************',\r\n        },\r\n        changes: {\r\n          before: { password: 'oldpass' },\r\n          after: { password: 'newpass' },\r\n        },\r\n        metadata: {\r\n          token: 'secret-token',\r\n          publicInfo: 'safe-data',\r\n        },\r\n      };\r\n\r\n      const config = {\r\n        ...AuditUtils.DEFAULT_CONFIG,\r\n        includeSensitiveData: false,\r\n        includeDataChanges: true,\r\n      };\r\n\r\n      const sanitized = AuditUtils.sanitizeEntry(entry, config);\r\n\r\n      expect(sanitized.user?.email).toMatch(/us\\*\\*\\*@example\\.com/);\r\n      expect(sanitized.user?.ipAddress).toMatch(/192\\.168\\.\\*\\*\\*\\.\\*\\*\\*/);\r\n      expect(sanitized.changes?.before?.password).toBe('***REDACTED***');\r\n      expect(sanitized.changes?.after?.password).toBe('***REDACTED***');\r\n      expect(sanitized.metadata?.token).toBe('***REDACTED***');\r\n      expect(sanitized.metadata?.publicInfo).toBe('safe-data');\r\n    });\r\n\r\n    it('should remove data changes when includeDataChanges is false', () => {\r\n      const entry: AuditEntry = {\r\n        id: 'audit-1',\r\n        timestamp: new Date(),\r\n        action: AuditAction.UPDATE,\r\n        result: AuditResult.SUCCESS,\r\n        severity: AuditSeverity.MEDIUM,\r\n        category: AuditCategory.DATA_MODIFICATION,\r\n        description: 'User updated',\r\n        system: { application: 'test' },\r\n        changes: {\r\n          before: { name: 'Old Name' },\r\n          after: { name: 'New Name' },\r\n        },\r\n      };\r\n\r\n      const config = {\r\n        ...AuditUtils.DEFAULT_CONFIG,\r\n        includeDataChanges: false,\r\n      };\r\n\r\n      const sanitized = AuditUtils.sanitizeEntry(entry, config);\r\n\r\n      expect(sanitized.changes).toBeUndefined();\r\n    });\r\n  });\r\n\r\n  describe('shouldAudit', () => {\r\n    it('should return false when auditing is disabled', () => {\r\n      const entry: Partial<AuditEntry> = {\r\n        action: AuditAction.READ,\r\n      };\r\n\r\n      const config = {\r\n        ...AuditUtils.DEFAULT_CONFIG,\r\n        enabled: false,\r\n      };\r\n\r\n      const result = AuditUtils.shouldAudit(entry, config);\r\n      expect(result).toBe(false);\r\n    });\r\n\r\n    it('should return false for non-audited actions', () => {\r\n      const entry: Partial<AuditEntry> = {\r\n        action: AuditAction.READ,\r\n      };\r\n\r\n      const config = {\r\n        ...AuditUtils.DEFAULT_CONFIG,\r\n        auditedActions: [AuditAction.CREATE, AuditAction.UPDATE],\r\n      };\r\n\r\n      const result = AuditUtils.shouldAudit(entry, config);\r\n      expect(result).toBe(false);\r\n    });\r\n\r\n    it('should return false for non-audited categories', () => {\r\n      const entry: Partial<AuditEntry> = {\r\n        action: AuditAction.READ,\r\n        category: AuditCategory.DATA_ACCESS,\r\n      };\r\n\r\n      const config = {\r\n        ...AuditUtils.DEFAULT_CONFIG,\r\n        auditedCategories: [AuditCategory.AUTHENTICATION],\r\n      };\r\n\r\n      const result = AuditUtils.shouldAudit(entry, config);\r\n      expect(result).toBe(false);\r\n    });\r\n\r\n    it('should return false for severity below minimum', () => {\r\n      const entry: Partial<AuditEntry> = {\r\n        action: AuditAction.READ,\r\n        severity: AuditSeverity.LOW,\r\n      };\r\n\r\n      const config = {\r\n        ...AuditUtils.DEFAULT_CONFIG,\r\n        minimumSeverity: AuditSeverity.MEDIUM,\r\n      };\r\n\r\n      const result = AuditUtils.shouldAudit(entry, config);\r\n      expect(result).toBe(false);\r\n    });\r\n\r\n    it('should return true for valid audit entry', () => {\r\n      const entry: Partial<AuditEntry> = {\r\n        action: AuditAction.CREATE,\r\n        category: AuditCategory.DATA_MODIFICATION,\r\n        severity: AuditSeverity.MEDIUM,\r\n      };\r\n\r\n      const result = AuditUtils.shouldAudit(entry, AuditUtils.DEFAULT_CONFIG);\r\n      expect(result).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('formatEntry', () => {\r\n    it('should format audit entry for display', () => {\r\n      const entry: AuditEntry = {\r\n        id: 'audit-1',\r\n        timestamp: new Date('2023-01-01T12:00:00Z'),\r\n        action: AuditAction.CREATE,\r\n        result: AuditResult.SUCCESS,\r\n        severity: AuditSeverity.MEDIUM,\r\n        category: AuditCategory.DATA_MODIFICATION,\r\n        description: 'User created',\r\n        system: { application: 'test' },\r\n        user: {\r\n          userId: 'user123',\r\n          username: 'testuser',\r\n        },\r\n        resource: {\r\n          resourceType: 'User',\r\n          resourceId: '456',\r\n        },\r\n      };\r\n\r\n      const formatted = AuditUtils.formatEntry(entry);\r\n\r\n      expect(formatted).toContain('2023-01-01T12:00:00.000Z');\r\n      expect(formatted).toContain('testuser');\r\n      expect(formatted).toContain('create');\r\n      expect(formatted).toContain('User:456');\r\n      expect(formatted).toContain('success');\r\n      expect(formatted).toContain('medium');\r\n    });\r\n\r\n    it('should handle missing user information', () => {\r\n      const entry: AuditEntry = {\r\n        id: 'audit-1',\r\n        timestamp: new Date('2023-01-01T12:00:00Z'),\r\n        action: AuditAction.CREATE,\r\n        result: AuditResult.SUCCESS,\r\n        severity: AuditSeverity.MEDIUM,\r\n        category: AuditCategory.DATA_MODIFICATION,\r\n        description: 'System action',\r\n        system: { application: 'test' },\r\n      };\r\n\r\n      const formatted = AuditUtils.formatEntry(entry);\r\n\r\n      expect(formatted).toContain('system');\r\n      expect(formatted).toContain('N/A');\r\n    });\r\n  });\r\n\r\n  describe('createQuery', () => {\r\n    it('should create query from parameters', () => {\r\n      const params: AuditQueryParams = {\r\n        startDate: new Date('2023-01-01'),\r\n        endDate: new Date('2023-12-31'),\r\n        userId: 'user123',\r\n        actions: [AuditAction.CREATE, AuditAction.UPDATE],\r\n        results: [AuditResult.SUCCESS],\r\n        severities: [AuditSeverity.HIGH],\r\n        categories: [AuditCategory.DATA_MODIFICATION],\r\n        resourceTypes: ['User'],\r\n        resourceIds: ['456'],\r\n        search: 'test search',\r\n        minRiskScore: 50,\r\n        maxRiskScore: 90,\r\n        complianceTags: ['SOX', 'GDPR'],\r\n        tenantId: 'tenant123',\r\n        applications: ['app1'],\r\n        environments: ['prod'],\r\n      };\r\n\r\n      const query = AuditUtils.createQuery(params);\r\n\r\n      expect(query.timestamp.$gte).toEqual(params.startDate);\r\n      expect(query.timestamp.$lte).toEqual(params.endDate);\r\n      expect(query['user.userId']).toBe(params.userId);\r\n      expect(query.action.$in).toEqual(params.actions);\r\n      expect(query.result.$in).toEqual(params.results);\r\n      expect(query.severity.$in).toEqual(params.severities);\r\n      expect(query.category.$in).toEqual(params.categories);\r\n      expect(query['resource.resourceType'].$in).toEqual(params.resourceTypes);\r\n      expect(query['resource.resourceId'].$in).toEqual(params.resourceIds);\r\n      expect(query.$or).toBeDefined();\r\n      expect(query.riskScore.$gte).toBe(params.minRiskScore);\r\n      expect(query.riskScore.$lte).toBe(params.maxRiskScore);\r\n      expect(query.complianceTags.$in).toEqual(params.complianceTags);\r\n      expect(query['system.tenantId']).toBe(params.tenantId);\r\n      expect(query['system.application'].$in).toEqual(params.applications);\r\n      expect(query['system.environment'].$in).toEqual(params.environments);\r\n    });\r\n\r\n    it('should create empty query for empty parameters', () => {\r\n      const params: AuditQueryParams = {};\r\n      const query = AuditUtils.createQuery(params);\r\n\r\n      expect(Object.keys(query)).toHaveLength(0);\r\n    });\r\n\r\n    it('should handle single date parameter', () => {\r\n      const params: AuditQueryParams = {\r\n        startDate: new Date('2023-01-01'),\r\n      };\r\n\r\n      const query = AuditUtils.createQuery(params);\r\n\r\n      expect(query.timestamp.$gte).toEqual(params.startDate);\r\n      expect(query.timestamp.$lte).toBeUndefined();\r\n    });\r\n  });\r\n});"], "version": 3}