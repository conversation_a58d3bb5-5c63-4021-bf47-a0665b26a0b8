{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\repositories\\vulnerability.repository.ts", "mappings": ";;;AAIA;;GAEG;AACH,IAAY,mBAWX;AAXD,WAAY,mBAAmB;IAC7B,gDAAyB,CAAA;IACzB,8CAAuB,CAAA;IACvB,0CAAmB,CAAA;IACnB,kDAA2B,CAAA;IAC3B,gDAAyB,CAAA;IACzB,4CAAqB,CAAA;IACrB,wCAAiB,CAAA;IACjB,wDAAiC,CAAA;IACjC,sDAA+B,CAAA;IAC/B,4CAAqB,CAAA;AACvB,CAAC,EAXW,mBAAmB,mCAAnB,mBAAmB,QAW9B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\repositories\\vulnerability.repository.ts"], "sourcesContent": ["import { AggregateRepository, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { ThreatSeverity } from '../enums/threat-severity.enum';\r\nimport { VulnerabilitySeverity } from '../enums/vulnerability-severity.enum';\r\n\r\n/**\r\n * Vulnerability Status Enum (placeholder until actual enum is implemented)\r\n */\r\nexport enum VulnerabilityStatus {\r\n  DISCOVERED = 'DISCOVERED',\r\n  CONFIRMED = 'CONFIRMED',\r\n  TRIAGED = 'TRIAGED',\r\n  IN_PROGRESS = 'IN_PROGRESS',\r\n  REMEDIATED = 'REMEDIATED',\r\n  VERIFIED = 'VERIFIED',\r\n  CLOSED = 'CLOSED',\r\n  FALSE_POSITIVE = 'FALSE_POSITIVE',\r\n  ACCEPTED_RISK = 'ACCEPTED_RISK',\r\n  DEFERRED = 'DEFERRED',\r\n}\r\n\r\n/**\r\n * Vulnerability Entity Interface (placeholder until actual entity is implemented)\r\n * This interface defines the expected structure of the Vulnerability entity\r\n */\r\nexport interface Vulnerability {\r\n  id: UniqueEntityId;\r\n  cveId?: string;\r\n  title: string;\r\n  description?: string;\r\n  severity: VulnerabilitySeverity;\r\n  status: VulnerabilityStatus;\r\n  cvssScore?: number;\r\n  riskScore: number;\r\n  category: string;\r\n  type: string;\r\n  tags: string[];\r\n  affectedAssets: Array<{\r\n    id: string;\r\n    name: string;\r\n    type: string;\r\n    criticality: 'low' | 'medium' | 'high' | 'critical';\r\n    exposure: 'internal' | 'external' | 'dmz';\r\n  }>;\r\n  exploitation?: {\r\n    status: 'no_exploitation' | 'proof_of_concept' | 'active_exploitation';\r\n    difficulty: 'low' | 'medium' | 'high';\r\n    requirements: string[];\r\n  };\r\n  remediation?: {\r\n    effort: 'low' | 'medium' | 'high';\r\n    timeline: string;\r\n    steps: string[];\r\n    priority: number;\r\n  };\r\n  discoveredAt: Date;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n  remediatedAt?: Date;\r\n  verifiedAt?: Date;\r\n}\r\n\r\n/**\r\n * Vulnerability Repository Interface\r\n * \r\n * Provides specialized data access methods for Vulnerability aggregates.\r\n * Focuses on vulnerability management, assessment, and remediation tracking.\r\n * \r\n * Key responsibilities:\r\n * - Vulnerability persistence and retrieval\r\n * - Risk-based vulnerability prioritization\r\n * - Asset-based vulnerability analysis\r\n * - Remediation tracking and metrics\r\n * - Compliance and reporting support\r\n */\r\nexport interface VulnerabilityRepository extends AggregateRepository<Vulnerability, UniqueEntityId> {\r\n  /**\r\n   * Find vulnerabilities by severity level\r\n   * Critical for priority-based vulnerability management\r\n   * \r\n   * @param severity The severity level to filter by\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to vulnerabilities with the specified severity\r\n   */\r\n  findBySeverity(severity: VulnerabilitySeverity, limit?: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find vulnerabilities by multiple severity levels\r\n   * Useful for filtering high-priority vulnerabilities\r\n   * \r\n   * @param severities Array of severity levels to include\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to vulnerabilities with any of the specified severities\r\n   */\r\n  findBySeverities(severities: VulnerabilitySeverity[], limit?: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find critical vulnerabilities\r\n   * Most urgent vulnerabilities requiring immediate attention\r\n   * \r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to critical vulnerabilities\r\n   */\r\n  findCriticalVulnerabilities(limit?: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find high severity vulnerabilities (HIGH and CRITICAL)\r\n   * High-priority vulnerabilities for focused remediation\r\n   * \r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to high severity vulnerabilities\r\n   */\r\n  findHighSeverityVulnerabilities(limit?: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find vulnerabilities by status\r\n   * Essential for workflow management and remediation tracking\r\n   * \r\n   * @param status The vulnerability status to filter by\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to vulnerabilities with the specified status\r\n   */\r\n  findByStatus(status: VulnerabilityStatus, limit?: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find active vulnerabilities (not remediated or closed)\r\n   * Current vulnerabilities requiring attention\r\n   * \r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to active vulnerabilities\r\n   */\r\n  findActiveVulnerabilities(limit?: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find remediated vulnerabilities\r\n   * Historical vulnerabilities for analysis and reporting\r\n   * \r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to remediated vulnerabilities\r\n   */\r\n  findRemediatedVulnerabilities(limit?: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find vulnerabilities with high risk scores\r\n   * Risk-based vulnerability prioritization\r\n   * \r\n   * @param minRiskScore Minimum risk score threshold (default: 70)\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to high-risk vulnerabilities\r\n   */\r\n  findHighRiskVulnerabilities(minRiskScore?: number, limit?: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find vulnerabilities by CVE ID\r\n   * CVE-specific vulnerability lookup\r\n   * \r\n   * @param cveId The CVE identifier\r\n   * @returns Promise resolving to vulnerability with the specified CVE ID\r\n   */\r\n  findByCveId(cveId: string): Promise<Vulnerability | null>;\r\n\r\n  /**\r\n   * Find vulnerabilities with CVE identifiers\r\n   * Known vulnerabilities with official CVE assignments\r\n   * \r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to vulnerabilities with CVE IDs\r\n   */\r\n  findWithCveIds(limit?: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find zero-day vulnerabilities (no CVE)\r\n   * Unknown or newly discovered vulnerabilities\r\n   * \r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to zero-day vulnerabilities\r\n   */\r\n  findZeroDayVulnerabilities(limit?: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find vulnerabilities by CVSS score range\r\n   * CVSS-based vulnerability filtering\r\n   * \r\n   * @param minScore Minimum CVSS score\r\n   * @param maxScore Maximum CVSS score\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to vulnerabilities within the CVSS score range\r\n   */\r\n  findByCvssScoreRange(minScore?: number, maxScore?: number, limit?: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find vulnerabilities with high CVSS scores\r\n   * CVSS-based high-priority vulnerability identification\r\n   * \r\n   * @param minScore Minimum CVSS score (default: 7.0)\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to high CVSS score vulnerabilities\r\n   */\r\n  findHighCvssVulnerabilities(minScore?: number, limit?: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find vulnerabilities by category\r\n   * Categorical vulnerability analysis\r\n   * \r\n   * @param categories Array of vulnerability categories\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to vulnerabilities in the specified categories\r\n   */\r\n  findByCategories(categories: string[], limit?: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find vulnerabilities by type\r\n   * Type-specific vulnerability analysis\r\n   * \r\n   * @param types Array of vulnerability types\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to vulnerabilities of the specified types\r\n   */\r\n  findByTypes(types: string[], limit?: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find vulnerabilities by tags\r\n   * Tag-based vulnerability filtering and categorization\r\n   * \r\n   * @param tags Array of tags to search for\r\n   * @param matchAll Whether to match all tags (AND) or any tag (OR)\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to vulnerabilities with the specified tags\r\n   */\r\n  findByTags(tags: string[], matchAll?: boolean, limit?: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find vulnerabilities affecting specific assets\r\n   * Asset-based vulnerability analysis\r\n   * \r\n   * @param assetIds Array of asset IDs\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to vulnerabilities affecting the specified assets\r\n   */\r\n  findByAffectedAssets(assetIds: string[], limit?: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find vulnerabilities affecting critical assets\r\n   * Critical asset vulnerability prioritization\r\n   * \r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to vulnerabilities affecting critical assets\r\n   */\r\n  findAffectingCriticalAssets(limit?: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find vulnerabilities affecting externally exposed assets\r\n   * External exposure vulnerability prioritization\r\n   * \r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to vulnerabilities affecting externally exposed assets\r\n   */\r\n  findAffectingExternalAssets(limit?: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find actively exploited vulnerabilities\r\n   * Immediate threat vulnerability identification\r\n   * \r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to actively exploited vulnerabilities\r\n   */\r\n  findActivelyExploited(limit?: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find recent vulnerabilities within a time window\r\n   * Recently discovered vulnerability monitoring\r\n   * \r\n   * @param withinDays Time window in days (default: 7)\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to recent vulnerabilities\r\n   */\r\n  findRecentVulnerabilities(withinDays?: number, limit?: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find stale vulnerabilities that haven't been updated\r\n   * Identifies vulnerabilities that may need attention\r\n   * \r\n   * @param olderThanDays Age threshold in days (default: 90)\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to stale vulnerabilities\r\n   */\r\n  findStaleVulnerabilities(olderThanDays?: number, limit?: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find vulnerabilities by age range\r\n   * Age-based vulnerability analysis\r\n   * \r\n   * @param minAgeDays Minimum age in days\r\n   * @param maxAgeDays Maximum age in days\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to vulnerabilities within the age range\r\n   */\r\n  findByAgeRange(minAgeDays?: number, maxAgeDays?: number, limit?: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Count vulnerabilities by severity\r\n   * Severity distribution analysis\r\n   * \r\n   * @returns Promise resolving to severity counts\r\n   */\r\n  countBySeverity(): Promise<Record<VulnerabilitySeverity, number>>;\r\n\r\n  /**\r\n   * Count vulnerabilities by status\r\n   * Status distribution analysis\r\n   * \r\n   * @returns Promise resolving to status counts\r\n   */\r\n  countByStatus(): Promise<Record<VulnerabilityStatus, number>>;\r\n\r\n  /**\r\n   * Count vulnerabilities by category\r\n   * Category distribution analysis\r\n   * \r\n   * @returns Promise resolving to category counts\r\n   */\r\n  countByCategory(): Promise<Record<string, number>>;\r\n\r\n  /**\r\n   * Get vulnerability statistics for a time period\r\n   * Comprehensive vulnerability metrics and analysis\r\n   * \r\n   * @param startTime Start of the time period\r\n   * @param endTime End of the time period\r\n   * @returns Promise resolving to vulnerability statistics\r\n   */\r\n  getVulnerabilityStatistics(startTime: Date, endTime: Date): Promise<{\r\n    totalVulnerabilities: number;\r\n    activeVulnerabilities: number;\r\n    remediatedVulnerabilities: number;\r\n    vulnerabilitiesBySeverity: Record<VulnerabilitySeverity, number>;\r\n    vulnerabilitiesByStatus: Record<VulnerabilityStatus, number>;\r\n    vulnerabilitiesByCategory: Record<string, number>;\r\n    averageRiskScore: number;\r\n    averageCvssScore: number;\r\n    highRiskVulnerabilityCount: number;\r\n    criticalVulnerabilityCount: number;\r\n    zeroDayCount: number;\r\n    activelyExploitedCount: number;\r\n    averageRemediationTime: number;\r\n    remediationMetrics: {\r\n      totalRemediated: number;\r\n      averageTimeToRemediation: number;\r\n      remediationByPriority: Record<number, number>;\r\n    };\r\n    assetImpact: {\r\n      criticalAssetsAffected: number;\r\n      externalAssetsAffected: number;\r\n      totalAssetsAffected: number;\r\n    };\r\n  }>;\r\n\r\n  /**\r\n   * Find vulnerabilities for prioritization\r\n   * Risk-based vulnerability prioritization algorithm\r\n   * \r\n   * @param criteria Prioritization criteria\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to prioritized vulnerabilities\r\n   */\r\n  findForPrioritization(criteria?: {\r\n    minSeverity?: VulnerabilitySeverity;\r\n    minRiskScore?: number;\r\n    minCvssScore?: number;\r\n    includeActivelyExploited?: boolean;\r\n    includeCriticalAssets?: boolean;\r\n    includeExternalAssets?: boolean;\r\n    maxAge?: number;\r\n  }, limit?: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find vulnerabilities by remediation effort\r\n   * Effort-based remediation planning\r\n   * \r\n   * @param effort Remediation effort level\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to vulnerabilities with the specified remediation effort\r\n   */\r\n  findByRemediationEffort(effort: 'low' | 'medium' | 'high', limit?: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find vulnerabilities by remediation priority\r\n   * Priority-based remediation planning\r\n   * \r\n   * @param minPriority Minimum remediation priority\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to vulnerabilities with the specified priority or higher\r\n   */\r\n  findByRemediationPriority(minPriority: number, limit?: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Find overdue vulnerabilities\r\n   * Identifies vulnerabilities past their remediation timeline\r\n   * \r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to overdue vulnerabilities\r\n   */\r\n  findOverdueVulnerabilities(limit?: number): Promise<Vulnerability[]>;\r\n\r\n  /**\r\n   * Archive old remediated vulnerabilities\r\n   * Moves old remediated vulnerabilities to archive storage\r\n   * \r\n   * @param olderThanDays Age threshold in days\r\n   * @param batchSize Number of vulnerabilities to archive per batch\r\n   * @returns Promise resolving to the number of archived vulnerabilities\r\n   */\r\n  archiveOldVulnerabilities(olderThanDays: number, batchSize?: number): Promise<number>;\r\n\r\n  /**\r\n   * Bulk update vulnerability status\r\n   * Efficient batch operation for vulnerability management\r\n   * \r\n   * @param vulnerabilityIds Array of vulnerability IDs to update\r\n   * @param newStatus New vulnerability status\r\n   * @param updatedBy Who is performing the update\r\n   * @returns Promise resolving to the number of updated vulnerabilities\r\n   */\r\n  bulkUpdateStatus(\r\n    vulnerabilityIds: UniqueEntityId[],\r\n    newStatus: VulnerabilityStatus,\r\n    updatedBy?: string\r\n  ): Promise<number>;\r\n\r\n  /**\r\n   * Find vulnerability trends\r\n   * Analyzes vulnerability patterns over time\r\n   * \r\n   * @param timeWindow Time window for trend analysis in days\r\n   * @returns Promise resolving to vulnerability trend data\r\n   */\r\n  findVulnerabilityTrends(timeWindow?: number): Promise<Array<{\r\n    date: Date;\r\n    vulnerabilityCount: number;\r\n    severityDistribution: Record<VulnerabilitySeverity, number>;\r\n    topCategories: Array<{ category: string; count: number }>;\r\n    remediationRate: number;\r\n    averageRiskScore: number;\r\n  }>>;\r\n}"], "version": 3}