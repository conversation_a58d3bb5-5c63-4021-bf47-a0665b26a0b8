{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\domain\\entities\\vulnerability-feed.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCAOiB;AAEjB;;;GAGG;AAMI,IAAM,iBAAiB,GAAvB,MAAM,iBAAiB;IA8N5B;;OAEG;IACH,IAAI,YAAY;QACd,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS;YAAE,OAAO,KAAK,CAAC;QAC9D,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAO,IAAI,CAAC;QAClC,OAAO,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,UAAU,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO,IAAI,CAAC;QAEjC,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC;YAC7C,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,GAAG;YAChE,CAAC,CAAC,CAAC,CAAC;QAEN,OAAO,SAAS,GAAG,EAAE,CAAC,CAAC,2BAA2B;IACpD,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QACjE,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC;IAC5E,CAAC;IAED;;OAEG;IACH,IAAI,kBAAkB;QACpB,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAO,IAAI,CAAC;QAClC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QACzD,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC;QACtC,IAAI,KAAK,KAAK,IAAI;YAAE,OAAO,IAAI,CAAC;QAEhC,qDAAqD;QACrD,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,GAAG,EAAE,CAAC;QACxD,OAAO,KAAK,GAAG,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,SAAS;QACP,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;QACxB,IAAI,CAAC,iBAAiB,GAAG,IAAI,IAAI,EAAE,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,KAAU;QACrB,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAE7B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC,SAAS,GAAG;gBACf,UAAU,EAAE,CAAC;gBACb,eAAe,EAAE,CAAC;gBAClB,WAAW,EAAE,CAAC;gBACd,qBAAqB,EAAE,CAAC;gBACxB,iBAAiB,EAAE,CAAC;gBACpB,mBAAmB,EAAE,CAAC;gBACtB,mBAAmB,EAAE,CAAC;gBACtB,WAAW,EAAE,CAAC;aACf,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;QAC5B,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC;QACjC,IAAI,CAAC,SAAS,CAAC,gBAAgB,GAAG,KAAK,CAAC,QAAQ,CAAC;QACjD,IAAI,CAAC,SAAS,CAAC,qBAAqB,IAAI,KAAK,CAAC,gBAAgB,IAAI,CAAC,CAAC;QACpE,IAAI,CAAC,SAAS,CAAC,iBAAiB,IAAI,KAAK,CAAC,YAAY,IAAI,CAAC,CAAC;QAC5D,IAAI,CAAC,SAAS,CAAC,mBAAmB,IAAI,KAAK,CAAC,cAAc,IAAI,CAAC,CAAC;QAChE,IAAI,CAAC,SAAS,CAAC,mBAAmB,IAAI,KAAK,CAAC,cAAc,IAAI,CAAC,CAAC;QAEhE,kCAAkC;QAClC,IAAI,IAAI,CAAC,SAAS,CAAC,mBAAmB,EAAE,CAAC;YACvC,IAAI,CAAC,SAAS,CAAC,mBAAmB;gBAChC,CAAC,IAAI,CAAC,SAAS,CAAC,mBAAmB,GAAG,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC9D,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,SAAS,CAAC,mBAAmB,GAAG,KAAK,CAAC,QAAQ,CAAC;QACtD,CAAC;QAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,KAAa;QACpB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC;QAEtB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC,SAAS,GAAG;gBACf,UAAU,EAAE,CAAC;gBACb,eAAe,EAAE,CAAC;gBAClB,WAAW,EAAE,CAAC;gBACd,qBAAqB,EAAE,CAAC;gBACxB,iBAAiB,EAAE,CAAC;gBACpB,mBAAmB,EAAE,CAAC;gBACtB,mBAAmB,EAAE,CAAC;gBACtB,WAAW,EAAE,CAAC;aACf,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;QAC5B,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;QAC7B,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;QAC7B,IAAI,CAAC,SAAS,CAAC,gBAAgB,GAAG,KAAK,CAAC;QACxC,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAEtD,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;QACvB,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;QACvB,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,UAAU;QACR,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,gBAAgB;QACtB,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO;QAE3B,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,GAAG,EAAE,GAAG,IAAI,CAAC;QAC5D,IAAI,CAAC,UAAU,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,UAAU,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,GAAW;QAChB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,GAAW;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE;YACvB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACrC,CAAC;IACJ,CAAC;CACF,CAAA;AAlcY,8CAAiB;AAE5B;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;6CACpB;AAMX;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;+CACX;AAMb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sDACpB;AAUrB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,WAAW;QACjB,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,qBAAqB,EAAE,YAAY,EAAE,iBAAiB,EAAE,QAAQ,CAAC;KACjH,CAAC;;mDACwH;AAM1H;IADC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDACT;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;8CACb;AAUZ;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC;QAC5D,OAAO,EAAE,MAAM;KAChB,CAAC;;iDACiE;AAMnE;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDAW7D;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;;qDAkC7C;AAUF;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC;QAC1D,OAAO,EAAE,UAAU;KACpB,CAAC;;iDAC6D;AAM/D;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;mDAC3B;AAUlB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC;QAC3C,OAAO,EAAE,QAAQ;KAClB,CAAC;;mDAC+C;AAMjD;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;oDAc5D;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;yDAUjE;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;0DAkBlE;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACtE,IAAI,oBAAJ,IAAI;qDAAC;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,sBAAsB,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACvE,IAAI,oBAAJ,IAAI;4DAAC;AAMzB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACtE,IAAI,oBAAJ,IAAI;qDAAC;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;+CACtC;AAMf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAClD,MAAM,oBAAN,MAAM;2DAAc;AAGvC;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;oDAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;oDAAC;4BA5NL,iBAAiB;IAL7B,IAAA,gBAAM,EAAC,qBAAqB,CAAC;IAC7B,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,YAAY,CAAC,CAAC;IACrB,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;GACP,iBAAiB,CAkc7B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\domain\\entities\\vulnerability-feed.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n} from 'typeorm';\r\n\r\n/**\r\n * Vulnerability Feed entity\r\n * Represents external vulnerability data sources and feeds\r\n */\r\n@Entity('vulnerability_feeds')\r\n@Index(['feedType'])\r\n@Index(['status'])\r\n@Index(['lastSyncAt'])\r\n@Index(['isActive'])\r\nexport class VulnerabilityFeed {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * Feed name\r\n   */\r\n  @Column({ length: 255 })\r\n  name: string;\r\n\r\n  /**\r\n   * Feed description\r\n   */\r\n  @Column({ type: 'text', nullable: true })\r\n  description?: string;\r\n\r\n  /**\r\n   * Type of vulnerability feed\r\n   */\r\n  @Column({\r\n    name: 'feed_type',\r\n    type: 'enum',\r\n    enum: ['nvd', 'mitre', 'vendor', 'commercial', 'threat_intelligence', 'exploit_db', 'github_advisory', 'custom'],\r\n  })\r\n  feedType: 'nvd' | 'mitre' | 'vendor' | 'commercial' | 'threat_intelligence' | 'exploit_db' | 'github_advisory' | 'custom';\r\n\r\n  /**\r\n   * Feed provider/vendor\r\n   */\r\n  @Column({ nullable: true })\r\n  provider?: string;\r\n\r\n  /**\r\n   * Feed URL or endpoint\r\n   */\r\n  @Column({ type: 'text' })\r\n  url: string;\r\n\r\n  /**\r\n   * Feed format\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['json', 'xml', 'csv', 'rss', 'atom', 'api', 'custom'],\r\n    default: 'json',\r\n  })\r\n  format: 'json' | 'xml' | 'csv' | 'rss' | 'atom' | 'api' | 'custom';\r\n\r\n  /**\r\n   * Authentication configuration\r\n   */\r\n  @Column({ name: 'auth_config', type: 'jsonb', nullable: true })\r\n  authConfig?: {\r\n    type: 'none' | 'api_key' | 'basic' | 'oauth' | 'certificate';\r\n    apiKey?: string;\r\n    username?: string;\r\n    password?: string;\r\n    token?: string;\r\n    clientId?: string;\r\n    clientSecret?: string;\r\n    certificatePath?: string;\r\n    headers?: Record<string, string>;\r\n  };\r\n\r\n  /**\r\n   * Feed configuration\r\n   */\r\n  @Column({ name: 'feed_config', type: 'jsonb' })\r\n  feedConfig: {\r\n    syncInterval: number; // minutes\r\n    batchSize?: number;\r\n    timeout?: number; // seconds\r\n    retryAttempts?: number;\r\n    retryDelay?: number; // seconds\r\n    filters?: {\r\n      severities?: string[];\r\n      products?: string[];\r\n      vendors?: string[];\r\n      dateRange?: {\r\n        from?: string;\r\n        to?: string;\r\n      };\r\n      keywords?: string[];\r\n      excludeKeywords?: string[];\r\n    };\r\n    mapping?: {\r\n      idField: string;\r\n      titleField: string;\r\n      descriptionField: string;\r\n      severityField: string;\r\n      cvssField?: string;\r\n      publishedDateField: string;\r\n      modifiedDateField?: string;\r\n      referencesField?: string;\r\n      affectedProductsField?: string;\r\n    };\r\n    transformation?: {\r\n      severityMapping?: Record<string, string>;\r\n      dateFormat?: string;\r\n      customScript?: string;\r\n    };\r\n  };\r\n\r\n  /**\r\n   * Feed status\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['active', 'inactive', 'error', 'syncing', 'paused'],\r\n    default: 'inactive',\r\n  })\r\n  status: 'active' | 'inactive' | 'error' | 'syncing' | 'paused';\r\n\r\n  /**\r\n   * Whether feed is active\r\n   */\r\n  @Column({ name: 'is_active', default: true })\r\n  isActive: boolean;\r\n\r\n  /**\r\n   * Feed priority for processing\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['low', 'medium', 'high', 'critical'],\r\n    default: 'medium',\r\n  })\r\n  priority: 'low' | 'medium' | 'high' | 'critical';\r\n\r\n  /**\r\n   * Sync statistics\r\n   */\r\n  @Column({ name: 'sync_stats', type: 'jsonb', nullable: true })\r\n  syncStats?: {\r\n    totalSyncs: number;\r\n    successfulSyncs: number;\r\n    failedSyncs: number;\r\n    lastSyncDuration?: number; // seconds\r\n    averageSyncDuration?: number; // seconds\r\n    totalRecordsProcessed: number;\r\n    totalRecordsAdded: number;\r\n    totalRecordsUpdated: number;\r\n    totalRecordsSkipped: number;\r\n    totalErrors: number;\r\n    lastErrorMessage?: string;\r\n    lastErrorAt?: string;\r\n  };\r\n\r\n  /**\r\n   * Data quality metrics\r\n   */\r\n  @Column({ name: 'quality_metrics', type: 'jsonb', nullable: true })\r\n  qualityMetrics?: {\r\n    completenessScore: number; // percentage\r\n    accuracyScore: number; // percentage\r\n    timelinessScore: number; // percentage\r\n    consistencyScore: number; // percentage\r\n    duplicateRate: number; // percentage\r\n    errorRate: number; // percentage\r\n    lastQualityCheck?: string;\r\n    qualityTrend: 'improving' | 'stable' | 'degrading';\r\n  };\r\n\r\n  /**\r\n   * Feed validation rules\r\n   */\r\n  @Column({ name: 'validation_rules', type: 'jsonb', nullable: true })\r\n  validationRules?: {\r\n    requiredFields: string[];\r\n    fieldValidation: Record<string, {\r\n      type: 'string' | 'number' | 'date' | 'email' | 'url' | 'regex';\r\n      pattern?: string;\r\n      minLength?: number;\r\n      maxLength?: number;\r\n      min?: number;\r\n      max?: number;\r\n      allowNull?: boolean;\r\n    }>;\r\n    businessRules?: Array<{\r\n      name: string;\r\n      description: string;\r\n      condition: string;\r\n      action: 'skip' | 'warn' | 'error';\r\n    }>;\r\n  };\r\n\r\n  /**\r\n   * When feed was last synchronized\r\n   */\r\n  @Column({ name: 'last_sync_at', type: 'timestamp with time zone', nullable: true })\r\n  lastSyncAt?: Date;\r\n\r\n  /**\r\n   * When feed sync was last attempted\r\n   */\r\n  @Column({ name: 'last_sync_attempt_at', type: 'timestamp with time zone', nullable: true })\r\n  lastSyncAttemptAt?: Date;\r\n\r\n  /**\r\n   * When next sync is scheduled\r\n   */\r\n  @Column({ name: 'next_sync_at', type: 'timestamp with time zone', nullable: true })\r\n  nextSyncAt?: Date;\r\n\r\n  /**\r\n   * Feed tags for categorization\r\n   */\r\n  @Column({ type: 'text', array: true, default: '{}' })\r\n  tags: string[];\r\n\r\n  /**\r\n   * Custom attributes\r\n   */\r\n  @Column({ name: 'custom_attributes', type: 'jsonb', nullable: true })\r\n  customAttributes?: Record<string, any>;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  /**\r\n   * Check if feed is due for sync\r\n   */\r\n  get isDueForSync(): boolean {\r\n    if (!this.isActive || this.status === 'syncing') return false;\r\n    if (!this.nextSyncAt) return true;\r\n    return new Date() >= this.nextSyncAt;\r\n  }\r\n\r\n  /**\r\n   * Check if feed is healthy\r\n   */\r\n  get isHealthy(): boolean {\r\n    if (!this.syncStats) return true;\r\n    \r\n    const errorRate = this.syncStats.totalSyncs > 0 \r\n      ? (this.syncStats.failedSyncs / this.syncStats.totalSyncs) * 100 \r\n      : 0;\r\n    \r\n    return errorRate < 10; // Less than 10% error rate\r\n  }\r\n\r\n  /**\r\n   * Get sync success rate\r\n   */\r\n  get syncSuccessRate(): number {\r\n    if (!this.syncStats || this.syncStats.totalSyncs === 0) return 0;\r\n    return (this.syncStats.successfulSyncs / this.syncStats.totalSyncs) * 100;\r\n  }\r\n\r\n  /**\r\n   * Get time since last sync in hours\r\n   */\r\n  get hoursSinceLastSync(): number | null {\r\n    if (!this.lastSyncAt) return null;\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - this.lastSyncAt.getTime();\r\n    return Math.floor(diffMs / (1000 * 60 * 60));\r\n  }\r\n\r\n  /**\r\n   * Check if feed is overdue for sync\r\n   */\r\n  get isOverdue(): boolean {\r\n    const hours = this.hoursSinceLastSync;\r\n    if (hours === null) return true;\r\n    \r\n    // Consider overdue if more than 2x the sync interval\r\n    const intervalHours = this.feedConfig.syncInterval / 60;\r\n    return hours > (intervalHours * 2);\r\n  }\r\n\r\n  /**\r\n   * Start sync\r\n   */\r\n  startSync(): void {\r\n    this.status = 'syncing';\r\n    this.lastSyncAttemptAt = new Date();\r\n  }\r\n\r\n  /**\r\n   * Complete sync successfully\r\n   */\r\n  completeSync(stats: any): void {\r\n    this.status = 'active';\r\n    this.lastSyncAt = new Date();\r\n    \r\n    if (!this.syncStats) {\r\n      this.syncStats = {\r\n        totalSyncs: 0,\r\n        successfulSyncs: 0,\r\n        failedSyncs: 0,\r\n        totalRecordsProcessed: 0,\r\n        totalRecordsAdded: 0,\r\n        totalRecordsUpdated: 0,\r\n        totalRecordsSkipped: 0,\r\n        totalErrors: 0,\r\n      };\r\n    }\r\n    \r\n    this.syncStats.totalSyncs++;\r\n    this.syncStats.successfulSyncs++;\r\n    this.syncStats.lastSyncDuration = stats.duration;\r\n    this.syncStats.totalRecordsProcessed += stats.recordsProcessed || 0;\r\n    this.syncStats.totalRecordsAdded += stats.recordsAdded || 0;\r\n    this.syncStats.totalRecordsUpdated += stats.recordsUpdated || 0;\r\n    this.syncStats.totalRecordsSkipped += stats.recordsSkipped || 0;\r\n    \r\n    // Calculate average sync duration\r\n    if (this.syncStats.averageSyncDuration) {\r\n      this.syncStats.averageSyncDuration = \r\n        (this.syncStats.averageSyncDuration + stats.duration) / 2;\r\n    } else {\r\n      this.syncStats.averageSyncDuration = stats.duration;\r\n    }\r\n    \r\n    this.scheduleNextSync();\r\n  }\r\n\r\n  /**\r\n   * Fail sync\r\n   */\r\n  failSync(error: string): void {\r\n    this.status = 'error';\r\n    \r\n    if (!this.syncStats) {\r\n      this.syncStats = {\r\n        totalSyncs: 0,\r\n        successfulSyncs: 0,\r\n        failedSyncs: 0,\r\n        totalRecordsProcessed: 0,\r\n        totalRecordsAdded: 0,\r\n        totalRecordsUpdated: 0,\r\n        totalRecordsSkipped: 0,\r\n        totalErrors: 0,\r\n      };\r\n    }\r\n    \r\n    this.syncStats.totalSyncs++;\r\n    this.syncStats.failedSyncs++;\r\n    this.syncStats.totalErrors++;\r\n    this.syncStats.lastErrorMessage = error;\r\n    this.syncStats.lastErrorAt = new Date().toISOString();\r\n    \r\n    this.scheduleNextSync();\r\n  }\r\n\r\n  /**\r\n   * Pause feed\r\n   */\r\n  pause(): void {\r\n    this.status = 'paused';\r\n    this.nextSyncAt = null;\r\n  }\r\n\r\n  /**\r\n   * Resume feed\r\n   */\r\n  resume(): void {\r\n    this.status = 'active';\r\n    this.scheduleNextSync();\r\n  }\r\n\r\n  /**\r\n   * Activate feed\r\n   */\r\n  activate(): void {\r\n    this.isActive = true;\r\n    this.status = 'active';\r\n    this.scheduleNextSync();\r\n  }\r\n\r\n  /**\r\n   * Deactivate feed\r\n   */\r\n  deactivate(): void {\r\n    this.isActive = false;\r\n    this.status = 'inactive';\r\n    this.nextSyncAt = null;\r\n  }\r\n\r\n  /**\r\n   * Schedule next sync\r\n   */\r\n  private scheduleNextSync(): void {\r\n    if (!this.isActive) return;\r\n    \r\n    const now = new Date();\r\n    const intervalMs = this.feedConfig.syncInterval * 60 * 1000;\r\n    this.nextSyncAt = new Date(now.getTime() + intervalMs);\r\n  }\r\n\r\n  /**\r\n   * Add tag to feed\r\n   */\r\n  addTag(tag: string): void {\r\n    if (!this.tags.includes(tag)) {\r\n      this.tags.push(tag);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove tag from feed\r\n   */\r\n  removeTag(tag: string): void {\r\n    this.tags = this.tags.filter(t => t !== tag);\r\n  }\r\n\r\n  /**\r\n   * Get feed summary\r\n   */\r\n  getSummary(): any {\r\n    return {\r\n      id: this.id,\r\n      name: this.name,\r\n      feedType: this.feedType,\r\n      provider: this.provider,\r\n      status: this.status,\r\n      isActive: this.isActive,\r\n      priority: this.priority,\r\n      isDueForSync: this.isDueForSync,\r\n      isHealthy: this.isHealthy,\r\n      isOverdue: this.isOverdue,\r\n      syncSuccessRate: this.syncSuccessRate,\r\n      hoursSinceLastSync: this.hoursSinceLastSync,\r\n      lastSyncAt: this.lastSyncAt,\r\n      nextSyncAt: this.nextSyncAt,\r\n      tags: this.tags,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Export feed for reporting\r\n   */\r\n  exportForReporting(): any {\r\n    return {\r\n      feed: this.getSummary(),\r\n      description: this.description,\r\n      url: this.url,\r\n      format: this.format,\r\n      feedConfig: this.feedConfig,\r\n      syncStats: this.syncStats,\r\n      qualityMetrics: this.qualityMetrics,\r\n      validationRules: this.validationRules,\r\n      customAttributes: this.customAttributes,\r\n      exportedAt: new Date().toISOString(),\r\n    };\r\n  }\r\n}\r\n"], "version": 3}