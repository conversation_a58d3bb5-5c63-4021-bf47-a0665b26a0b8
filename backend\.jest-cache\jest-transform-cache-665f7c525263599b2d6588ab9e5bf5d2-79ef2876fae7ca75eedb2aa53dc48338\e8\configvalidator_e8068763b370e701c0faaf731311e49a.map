{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\validators\\config.validator.ts", "mappings": ";;;;;;;;;;;;;AAAA,yDAAkE;AAClE,qDAYyB;AACzB,2CAA4C;AAC5C,2CAA+C;AAE/C,0CAA0C;AAC1C,MAAM,gBAAgB,GAAG,GAAG,EAAE,CAAC,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,IAAI,CAAC,CAAC;AAC5F,MAAM,eAAe,GAAG,GAAG,EAAE,CAAC,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;AAE5E,mBAAmB;AACnB,IAAK,WAIJ;AAJD,WAAK,WAAW;IACd,0CAA2B,CAAA;IAC3B,kCAAmB,CAAA;IACnB,wCAAyB,CAAA;AAC3B,CAAC,EAJI,WAAW,KAAX,WAAW,QAIf;AAED,iBAAiB;AACjB,IAAK,QAMJ;AAND,WAAK,QAAQ;IACX,2BAAe,CAAA;IACf,yBAAa,CAAA;IACb,yBAAa,CAAA;IACb,2BAAe,CAAA;IACf,+BAAmB,CAAA;AACrB,CAAC,EANI,QAAQ,KAAR,QAAQ,QAMZ;AAED,MAAM,cAAc;CAoCnB;AAlCU;IADR,IAAA,0BAAQ,GAAE;;4CACY;AAGd;IADR,IAAA,0BAAQ,GAAE;;4CACY;AAId;IAFR,IAAA,wBAAM,GAAE;IACR,eAAe,EAAE;;4CACK;AAGd;IADR,IAAA,0BAAQ,GAAE;;gDACgB;AAGlB;IADR,IAAA,0BAAQ,GAAE;;gDACgB;AAGlB;IADR,IAAA,0BAAQ,GAAE;;gDACgB;AAIlB;IAFR,IAAA,2BAAS,GAAE;IACX,gBAAgB,EAAE;;mDACY;AAItB;IAFR,IAAA,2BAAS,GAAE;IACX,gBAAgB,EAAE;;+CACQ;AAIlB;IAFR,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;2CACW;AAKb;IAHR,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,eAAe,EAAE;;sDACe;AAGnC,MAAM,WAAW;CAqBhB;AAnBU;IADR,IAAA,0BAAQ,GAAE;;yCACY;AAId;IAFR,IAAA,wBAAM,GAAE;IACR,eAAe,EAAE;;yCACK;AAId;IAFR,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;6CACgB;AAKlB;IAHR,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,eAAe,EAAE;;uCACG;AAKZ;IAHR,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,eAAe,EAAE;;wCACI;AAGxB,MAAM,mBAAmB;CAaxB;AAXU;IADR,IAAA,0BAAQ,GAAE;;qDACgB;AAGlB;IADR,IAAA,0BAAQ,GAAE;;yDACoB;AAGtB;IADR,IAAA,uBAAK,GAAE;;wDACsB;AAIrB;IAFR,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;mDACc;AAG3B,MAAM,SAAS;CAkBd;AAhBU;IADR,IAAA,0BAAQ,GAAE;;yCACc;AAGhB;IADR,IAAA,0BAAQ,GAAE;;4CACiB;AAGnB;IADR,IAAA,0BAAQ,GAAE;;gDACqB;AAGvB;IADR,IAAA,0BAAQ,GAAE;;mDACwB;AAG1B;IADR,IAAA,0BAAQ,GAAE;;yCACc;AAGhB;IADR,IAAA,0BAAQ,GAAE;;2CACgB;AAG7B,MAAM,WAAW;CAQhB;AALU;IAFR,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,mBAAmB,CAAC;8BACd,mBAAmB;2CAAC;AAI7B;IAFR,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,mBAAmB,CAAC;8BACX,mBAAmB;8CAAC;AAG3C,MAAM,UAAU;CAiCf;AA/BU;IADR,IAAA,uBAAK,GAAE;;8CACqB;AAGpB;IADR,IAAA,0BAAQ,GAAE;;0CACc;AAGhB;IADR,IAAA,uBAAK,GAAE;;+CACsB;AAGrB;IADR,IAAA,0BAAQ,GAAE;;wCACY;AAId;IAFR,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;+CACmB;AAIrB;IAFR,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;oDACwB;AAI1B;IAFR,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;sDAC0B;AAI5B;IAFR,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;mDACuB;AAKzB;IAHR,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,eAAe,EAAE;;uDACoB;AAGxC,MAAM,WAAW;CAQhB;AALU;IAFR,IAAA,0BAAQ,GAAE;IACV,eAAe,EAAE;;uDACmB;AAI5B;IAFR,IAAA,0BAAQ,GAAE;IACV,eAAe,EAAE;;qDACiB;AAGrC,MAAM,uBAAuB;CAQ5B;AALU;IAFR,IAAA,0BAAQ,GAAE;IACV,eAAe,EAAE;;iEACiB;AAI1B;IAFR,IAAA,0BAAQ,GAAE;IACV,eAAe,EAAE;;iEACiB;AAGrC,MAAM,aAAa;CAQlB;AALU;IAFR,IAAA,0BAAQ,GAAE;IACV,eAAe,EAAE;;4DACsB;AAI/B;IAFR,IAAA,0BAAQ,GAAE;IACV,eAAe,EAAE;;+DACyB;AAG7C,MAAM,UAAU;CAwBf;AArBU;IAFR,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,SAAS,CAAC;8BACP,SAAS;uCAAC;AAIhB;IAFR,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,WAAW,CAAC;8BACP,WAAW;yCAAC;AAIpB;IAFR,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,UAAU,CAAC;8BACP,UAAU;wCAAC;AAIlB;IAFR,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,WAAW,CAAC;8BACC,WAAW;iDAAC;AAI5B;IAFR,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,uBAAuB,CAAC;8BACP,uBAAuB;qDAAC;AAI5C;IAFR,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,aAAa,CAAC;8BACP,aAAa;2CAAC;AAGnC,MAAM,UAAU;CAaf;AAXU;IADR,IAAA,0BAAQ,GAAE;;0CACyB;AAI3B;IAFR,IAAA,2BAAS,GAAE;IACX,gBAAgB,EAAE;;+CACY;AAGtB;IADR,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;2CACG;AAGnB;IADR,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;kDACU;AAGrC,MAAM,eAAe;CAepB;AAZU;IAFR,IAAA,2BAAS,GAAE;IACX,gBAAgB,EAAE;;gDACQ;AAIlB;IAFR,IAAA,0BAAQ,GAAE;IACV,eAAe,EAAE;;iDACS;AAIlB;IAFR,IAAA,0BAAQ,GAAE;IACV,eAAe,EAAE;;4CACI;AAGb;IADR,IAAA,0BAAQ,GAAE;;gDACe;AAG5B,MAAM,cAAc;CAUnB;AAPU;IAFR,IAAA,2BAAS,GAAE;IACX,gBAAgB,EAAE;;+CACQ;AAGlB;IADR,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;iDACK;AAGrB;IADR,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;iDACK;AAGhC,MAAM,qBAAqB;CAkB1B;AAfU;IAFR,IAAA,2BAAS,GAAE;IACX,gBAAgB,EAAE;;mDACK;AAIf;IAFR,IAAA,2BAAS,GAAE;IACX,gBAAgB,EAAE;;sDACQ;AAIlB;IAFR,IAAA,2BAAS,GAAE;IACX,gBAAgB,EAAE;;4DACc;AAGxB;IADR,IAAA,0BAAQ,GAAE;;6DACsB;AAGxB;IADR,IAAA,0BAAQ,GAAE;;oEAC6B;AAG1C,MAAM,cAAc;CAoBnB;AAjBU;IAFR,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,UAAU,CAAC;8BACP,UAAU;4CAAC;AAIlB;IAFR,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,eAAe,CAAC;8BACP,eAAe;iDAAC;AAI5B;IAFR,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,eAAe,CAAC;8BACJ,eAAe;oDAAC;AAI/B;IAFR,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,cAAc,CAAC;8BACP,cAAc;gDAAC;AAI1B;IAFR,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,qBAAqB,CAAC;8BACf,qBAAqB;+CAAC;AAG3C,MAAM,eAAe;CAsBpB;AAnBU;IAFR,IAAA,2BAAS,GAAE;IACX,gBAAgB,EAAE;;gDACQ;AAGlB;IADR,IAAA,0BAAQ,GAAE;;iDACgB;AAGlB;IADR,IAAA,0BAAQ,GAAE;;+CACc;AAIhB;IAFR,IAAA,4BAAU,GAAE;IACZ,IAAA,uBAAK,GAAE;;gDACkB;AAIjB;IAFR,IAAA,0BAAQ,GAAE;IACV,eAAe,EAAE;;gDACQ;AAIjB;IAFR,IAAA,0BAAQ,GAAE;IACV,eAAe,EAAE;;gDACQ;AAG5B,MAAM,gBAAgB;CAgBrB;AAbU;IAFR,IAAA,2BAAS,GAAE;IACX,gBAAgB,EAAE;;+DACsB;AAIhC;IAFR,IAAA,2BAAS,GAAE;IACX,gBAAgB,EAAE;;4DACmB;AAI7B;IAFR,IAAA,2BAAS,GAAE;IACX,gBAAgB,EAAE;;wDACe;AAIzB;IAFR,IAAA,2BAAS,GAAE;IACX,gBAAgB,EAAE;;0DACiB;AAGtC,MAAM,cAAc;CAYnB;AATU;IAFR,IAAA,0BAAQ,GAAE;IACV,eAAe,EAAE;;yDACkB;AAI3B;IAFR,IAAA,0BAAQ,GAAE;IACV,eAAe,EAAE;;iDACU;AAInB;IAFR,IAAA,0BAAQ,GAAE;IACV,eAAe,EAAE;;mDACY;AAGhC,MAAM,QAAQ;CAYb;AATU;IAFR,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,eAAe,CAAC;8BACT,eAAe;yCAAC;AAI1B;IAFR,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,gBAAgB,CAAC;8BACT,gBAAgB;0CAAC;AAI5B;IAFR,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,cAAc,CAAC;8BACT,cAAc;wCAAC;AAGnC,MAAM,gBAAgB;CAWrB;AARU;IAFR,IAAA,2BAAS,GAAE;IACX,gBAAgB,EAAE;;iDACQ;AAGlB;IADR,IAAA,0BAAQ,GAAE;;8CACY;AAId;IAFR,IAAA,0BAAQ,GAAE;IACV,eAAe,EAAE;;mDACU;AAG9B,MAAM,WAAW;CAQhB;AALU;IAFR,IAAA,2BAAS,GAAE;IACX,gBAAgB,EAAE;;4CACQ;AAIlB;IAFR,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,gBAAgB,CAAC;8BACV,gBAAgB;4CAAC;AAGtC,MAAM,aAAa;CA2BlB;AAzBU;IADR,IAAA,wBAAM,EAAC,QAAQ,CAAC;;4CACS;AAIjB;IAFR,IAAA,2BAAS,GAAE;IACX,gBAAgB,EAAE;;8CACQ;AAIlB;IAFR,IAAA,2BAAS,GAAE;IACX,gBAAgB,EAAE;;2CACK;AAIf;IAFR,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;+CACgB;AAIlB;IAFR,IAAA,2BAAS,GAAE;IACX,gBAAgB,EAAE;;2CACK;AAIf;IAFR,IAAA,2BAAS,GAAE;IACX,gBAAgB,EAAE;;gDACU;AAIpB;IAFR,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,WAAW,CAAC;8BACP,WAAW;4CAAC;AAG/B,MAAM,oBAAoB;CASzB;AAPU;IADR,IAAA,0BAAQ,GAAE;;2DACqB;AAGvB;IADR,IAAA,0BAAQ,GAAE;;+DACyB;AAG3B;IADR,IAAA,0BAAQ,GAAE;;0DACoB;AAGjC,MAAM,WAAW;CA4BhB;AA1BU;IADR,IAAA,0BAAQ,GAAE;;yCACY;AAId;IAFR,IAAA,wBAAM,GAAE;IACR,eAAe,EAAE;;yCACK;AAId;IAFR,IAAA,2BAAS,GAAE;IACX,gBAAgB,EAAE;;2CACO;AAGjB;IADR,IAAA,0BAAQ,GAAE;;6CACgB;AAGlB;IADR,IAAA,0BAAQ,GAAE;;6CACgB;AAGlB;IADR,IAAA,yBAAO,GAAE;;yCACa;AAId;IAFR,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;;4CACgB;AAIjB;IAFR,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,oBAAoB,CAAC;8BACZ,oBAAoB;8CAAC;AAG5C,MAAa,SAAS;CAiErB;AAjED,8BAiEC;AA/DU;IADR,IAAA,wBAAM,EAAC,WAAW,CAAC;;2CACY;AAIvB;IAFR,IAAA,wBAAM,GAAE;IACR,eAAe,EAAE;;uCACK;AAGd;IADR,IAAA,0BAAQ,GAAE;;2CACgB;AAGlB;IADR,IAAA,0BAAQ,GAAE;;8CACmB;AAGrB;IADR,IAAA,uBAAK,GAAE;;0CACkB;AAGjB;IADR,IAAA,uBAAK,GAAE;;0CACkB;AAIjB;IAFR,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;6CACkB;AAIpB;IAFR,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,cAAc,CAAC;8BACP,cAAc;2CAAC;AAI1B;IAFR,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,WAAW,CAAC;8BACP,WAAW;wCAAC;AAIpB;IAFR,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,UAAU,CAAC;8BACP,UAAU;uCAAC;AAIlB;IAFR,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,cAAc,CAAC;8BACP,cAAc;2CAAC;AAI1B;IAFR,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,QAAQ,CAAC;8BACP,QAAQ;qCAAC;AAId;IAFR,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,aAAa,CAAC;8BACP,aAAa;0CAAC;AAIxB;IAFR,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,WAAW,CAAC;8BACP,WAAW;wCAAC;AAIpB;IAFR,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;6CACkB;AAKpB;IAHR,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;IACX,gBAAgB,EAAE;;iDACe;AAKzB;IAHR,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;IACX,gBAAgB,EAAE;;iDACe;AAI7B,IAAM,uBAAuB,GAA7B,MAAM,uBAAuB;IAClC,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;IAAG,CAAC;IAE7D,QAAQ,CAAC,MAA+B;QACtC,MAAM,eAAe,GAAG,IAAA,gCAAY,EAAC,SAAS,EAAE,MAAM,EAAE;YACtD,wBAAwB,EAAE,IAAI;YAC9B,uBAAuB,EAAE,IAAI;SAC9B,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,IAAA,8BAAY,EAAC,eAAe,EAAE;YAC3C,qBAAqB,EAAE,KAAK;YAC5B,SAAS,EAAE,IAAI;YACf,oBAAoB,EAAE,IAAI;SAC3B,CAAC,CAAC;QAEH,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,oCAAoC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACnF,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAEO,YAAY,CAAC,MAAyB;QAC5C,OAAO,MAAM;aACV,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;aACrC,MAAM,CAAC,OAAO,CAAC;aACf,IAAI,CAAC,IAAI,CAAC,CAAC;IAChB,CAAC;IAEO,WAAW,CAAC,KAAsB,EAAE,IAAI,GAAG,EAAE;QACnD,MAAM,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC;QAExE,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;YACtB,OAAO,GAAG,WAAW,KAAK,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QAC1E,CAAC;QAED,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,EAAE,CAAC;YAC3B,OAAO,KAAK,CAAC,QAAQ;iBAClB,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;iBAClD,MAAM,CAAC,OAAO,CAAC;iBACf,IAAI,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC;QAED,OAAO,EAAE,CAAC;IACZ,CAAC;CACF,CAAA;AA7CY,0DAAuB;kCAAvB,uBAAuB;IADnC,IAAA,mBAAU,GAAE;yDAEiC,sBAAa,oBAAb,sBAAa;GAD9C,uBAAuB,CA6CnC;AAEM,MAAM,cAAc,GAAG,CAAC,MAA+B,EAAa,EAAE;IAC3E,MAAM,eAAe,GAAG,IAAA,gCAAY,EAAC,SAAS,EAAE,MAAM,EAAE;QACtD,wBAAwB,EAAE,IAAI;QAC9B,uBAAuB,EAAE,IAAI;KAC9B,CAAC,CAAC;IAEH,MAAM,MAAM,GAAG,IAAA,8BAAY,EAAC,eAAe,EAAE;QAC3C,qBAAqB,EAAE,KAAK;QAC5B,SAAS,EAAE,IAAI;QACf,oBAAoB,EAAE,IAAI;KAC3B,CAAC,CAAC;IAEH,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACtB,MAAM,aAAa,GAAG,MAAM;aACzB,GAAG,CAAC,KAAK,CAAC,EAAE;YACX,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;YACtC,OAAO,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,0BAA0B,CAAC;QAC1F,CAAC,CAAC;aACD,MAAM,CAAC,OAAO,CAAC,CAAC;QAEnB,MAAM,IAAI,KAAK,CAAC,oCAAoC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAClF,CAAC;IAED,OAAO,eAAe,CAAC;AACzB,CAAC,CAAC;AAxBW,QAAA,cAAc,kBAwBzB;AAEK,MAAM,gBAAgB,GAAG,GAAuB,EAAE,CAAC,CAAC;IACzD,QAAQ,EAAE,WAAW,CAAC,WAAW;IACjC,IAAI,EAAE,IAAI;IACV,QAAQ,EAAE,UAAU;IACpB,WAAW,EAAE,OAAO;IACpB,UAAU,EAAE,QAAQ;IACpB,cAAc,EAAE,IAAI;IACpB,cAAc,EAAE,IAAI;CACrB,CAAC,CAAC;AARU,QAAA,gBAAgB,oBAQ1B;AAEH,+BAA+B;AACxB,MAAM,aAAa,GAAG,GAAc,EAAE;IAC3C,OAAO,IAAA,sBAAc,EAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACrC,CAAC,CAAC;AAFW,QAAA,aAAa,iBAExB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\validators\\config.validator.ts"], "sourcesContent": ["import { plainToClass, Transform, Type } from 'class-transformer';\r\nimport {\r\n  IsString,\r\n  IsNumber,\r\n  IsBoolean,\r\n  IsOptional,\r\n  IsEnum,\r\n  IsUrl,\r\n  IsPort,\r\n  validateSync,\r\n  IsEmail,\r\n  ValidateNested,\r\n  ValidationError,\r\n} from 'class-validator';\r\nimport { Injectable } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\n\r\n// Utility type for boolean transformation\r\nconst BooleanTransform = () => Transform(({ value }) => value === 'true' || value === true);\r\nconst NumberTransform = () => Transform(({ value }) => parseInt(value, 10));\r\n\r\n// Environment enum\r\nenum Environment {\r\n  DEVELOPMENT = 'development',\r\n  STAGING = 'staging',\r\n  PRODUCTION = 'production',\r\n}\r\n\r\n// Log level enum\r\nenum LogLevel {\r\n  ERROR = 'error',\r\n  WARN = 'warn',\r\n  INFO = 'info',\r\n  DEBUG = 'debug',\r\n  VERBOSE = 'verbose',\r\n}\r\n\r\nclass DatabaseConfig {\r\n  @IsString()\r\n  readonly type!: string;\r\n\r\n  @IsString()\r\n  readonly host!: string;\r\n\r\n  @IsPort()\r\n  @NumberTransform()\r\n  readonly port!: number;\r\n\r\n  @IsString()\r\n  readonly username!: string;\r\n\r\n  @IsString()\r\n  readonly password!: string;\r\n\r\n  @IsString()\r\n  readonly database!: string;\r\n\r\n  @IsBoolean()\r\n  @BooleanTransform()\r\n  readonly synchronize!: boolean;\r\n\r\n  @IsBoolean()\r\n  @BooleanTransform()\r\n  readonly logging!: boolean;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  readonly ssl?: string;\r\n\r\n  @IsOptional()\r\n  @IsNumber()\r\n  @NumberTransform()\r\n  readonly maxConnections?: number;\r\n}\r\n\r\nclass RedisConfig {\r\n  @IsString()\r\n  readonly host!: string;\r\n\r\n  @IsPort()\r\n  @NumberTransform()\r\n  readonly port!: number;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  readonly password?: string;\r\n\r\n  @IsOptional()\r\n  @IsNumber()\r\n  @NumberTransform()\r\n  readonly db?: number;\r\n\r\n  @IsOptional()\r\n  @IsNumber()\r\n  @NumberTransform()\r\n  readonly ttl?: number;\r\n}\r\n\r\nclass OAuthProviderConfig {\r\n  @IsString()\r\n  readonly clientId!: string;\r\n\r\n  @IsString()\r\n  readonly clientSecret!: string;\r\n\r\n  @IsUrl()\r\n  readonly callbackUrl!: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  readonly tenant?: string;\r\n}\r\n\r\nclass JwtConfig {\r\n  @IsString()\r\n  readonly secret!: string;\r\n\r\n  @IsString()\r\n  readonly expiresIn!: string;\r\n\r\n  @IsString()\r\n  readonly refreshSecret!: string;\r\n\r\n  @IsString()\r\n  readonly refreshExpiresIn!: string;\r\n\r\n  @IsString()\r\n  readonly issuer!: string;\r\n\r\n  @IsString()\r\n  readonly audience!: string;\r\n}\r\n\r\nclass OAuthConfig {\r\n  @ValidateNested()\r\n  @Type(() => OAuthProviderConfig)\r\n  readonly google!: OAuthProviderConfig;\r\n\r\n  @ValidateNested()\r\n  @Type(() => OAuthProviderConfig)\r\n  readonly microsoft!: OAuthProviderConfig;\r\n}\r\n\r\nclass SamlConfig {\r\n  @IsUrl()\r\n  readonly entryPoint!: string;\r\n\r\n  @IsString()\r\n  readonly issuer!: string;\r\n\r\n  @IsUrl()\r\n  readonly callbackUrl!: string;\r\n\r\n  @IsString()\r\n  readonly cert!: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  readonly privateCert?: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  readonly identifierFormat?: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  readonly signatureAlgorithm?: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  readonly digestAlgorithm?: string;\r\n\r\n  @IsOptional()\r\n  @IsNumber()\r\n  @NumberTransform()\r\n  readonly acceptedClockSkewMs?: number;\r\n}\r\n\r\nclass TokenConfig {\r\n  @IsNumber()\r\n  @NumberTransform()\r\n  readonly tokenExpiryMinutes!: number;\r\n\r\n  @IsNumber()\r\n  @NumberTransform()\r\n  readonly rateLimitMinutes!: number;\r\n}\r\n\r\nclass EmailVerificationConfig {\r\n  @IsNumber()\r\n  @NumberTransform()\r\n  readonly tokenExpiryHours!: number;\r\n\r\n  @IsNumber()\r\n  @NumberTransform()\r\n  readonly rateLimitMinutes!: number;\r\n}\r\n\r\nclass SessionConfig {\r\n  @IsNumber()\r\n  @NumberTransform()\r\n  readonly maxConcurrentSessions!: number;\r\n\r\n  @IsNumber()\r\n  @NumberTransform()\r\n  readonly inactivityTimeoutMinutes!: number;\r\n}\r\n\r\nclass AuthConfig {\r\n  @ValidateNested()\r\n  @Type(() => JwtConfig)\r\n  readonly jwt!: JwtConfig;\r\n\r\n  @ValidateNested()\r\n  @Type(() => OAuthConfig)\r\n  readonly oauth!: OAuthConfig;\r\n\r\n  @ValidateNested()\r\n  @Type(() => SamlConfig)\r\n  readonly saml!: SamlConfig;\r\n\r\n  @ValidateNested()\r\n  @Type(() => TokenConfig)\r\n  readonly passwordReset!: TokenConfig;\r\n\r\n  @ValidateNested()\r\n  @Type(() => EmailVerificationConfig)\r\n  readonly emailVerification!: EmailVerificationConfig;\r\n\r\n  @ValidateNested()\r\n  @Type(() => SessionConfig)\r\n  readonly session!: SessionConfig;\r\n}\r\n\r\nclass CorsConfig {\r\n  @IsString()\r\n  readonly origin!: string | string[];\r\n\r\n  @IsBoolean()\r\n  @BooleanTransform()\r\n  readonly credentials!: boolean;\r\n\r\n  @IsString({ each: true })\r\n  readonly methods!: string[];\r\n\r\n  @IsString({ each: true })\r\n  readonly allowedHeaders!: string[];\r\n}\r\n\r\nclass RateLimitConfig {\r\n  @IsBoolean()\r\n  @BooleanTransform()\r\n  readonly enabled!: boolean;\r\n\r\n  @IsNumber()\r\n  @NumberTransform()\r\n  readonly windowMs!: number;\r\n\r\n  @IsNumber()\r\n  @NumberTransform()\r\n  readonly max!: number;\r\n\r\n  @IsString()\r\n  readonly message!: string;\r\n}\r\n\r\nclass IpFilterConfig {\r\n  @IsBoolean()\r\n  @BooleanTransform()\r\n  readonly enabled!: boolean;\r\n\r\n  @IsString({ each: true })\r\n  readonly whitelist!: string[];\r\n\r\n  @IsString({ each: true })\r\n  readonly blacklist!: string[];\r\n}\r\n\r\nclass SecurityHeadersConfig {\r\n  @IsBoolean()\r\n  @BooleanTransform()\r\n  readonly hsts!: boolean;\r\n\r\n  @IsBoolean()\r\n  @BooleanTransform()\r\n  readonly noSniff!: boolean;\r\n\r\n  @IsBoolean()\r\n  @BooleanTransform()\r\n  readonly xssProtection!: boolean;\r\n\r\n  @IsString()\r\n  readonly referrerPolicy!: string;\r\n\r\n  @IsString()\r\n  readonly contentSecurityPolicy!: string;\r\n}\r\n\r\nclass SecurityConfig {\r\n  @ValidateNested()\r\n  @Type(() => CorsConfig)\r\n  readonly cors!: CorsConfig;\r\n\r\n  @ValidateNested()\r\n  @Type(() => RateLimitConfig)\r\n  readonly rateLimit!: RateLimitConfig;\r\n\r\n  @ValidateNested()\r\n  @Type(() => RateLimitConfig)\r\n  readonly apiRateLimit!: RateLimitConfig;\r\n\r\n  @ValidateNested()\r\n  @Type(() => IpFilterConfig)\r\n  readonly ipFilter!: IpFilterConfig;\r\n\r\n  @ValidateNested()\r\n  @Type(() => SecurityHeadersConfig)\r\n  readonly headers!: SecurityHeadersConfig;\r\n}\r\n\r\nclass AIServiceConfig {\r\n  @IsBoolean()\r\n  @BooleanTransform()\r\n  readonly enabled!: boolean;\r\n\r\n  @IsString()\r\n  readonly provider!: string;\r\n\r\n  @IsString()\r\n  readonly apiKey!: string;\r\n\r\n  @IsOptional()\r\n  @IsUrl()\r\n  readonly baseUrl?: string;\r\n\r\n  @IsNumber()\r\n  @NumberTransform()\r\n  readonly timeout!: number;\r\n\r\n  @IsNumber()\r\n  @NumberTransform()\r\n  readonly retries!: number;\r\n}\r\n\r\nclass AIFeaturesConfig {\r\n  @IsBoolean()\r\n  @BooleanTransform()\r\n  readonly vulnerabilityAnalysis!: boolean;\r\n\r\n  @IsBoolean()\r\n  @BooleanTransform()\r\n  readonly threatIntelligence!: boolean;\r\n\r\n  @IsBoolean()\r\n  @BooleanTransform()\r\n  readonly riskAssessment!: boolean;\r\n\r\n  @IsBoolean()\r\n  @BooleanTransform()\r\n  readonly reportGeneration!: boolean;\r\n}\r\n\r\nclass AILimitsConfig {\r\n  @IsNumber()\r\n  @NumberTransform()\r\n  readonly requestsPerMinute!: number;\r\n\r\n  @IsNumber()\r\n  @NumberTransform()\r\n  readonly maxTokens!: number;\r\n\r\n  @IsNumber()\r\n  @NumberTransform()\r\n  readonly maxFileSize!: number;\r\n}\r\n\r\nclass AIConfig {\r\n  @ValidateNested()\r\n  @Type(() => AIServiceConfig)\r\n  readonly service!: AIServiceConfig;\r\n\r\n  @ValidateNested()\r\n  @Type(() => AIFeaturesConfig)\r\n  readonly features!: AIFeaturesConfig;\r\n\r\n  @ValidateNested()\r\n  @Type(() => AILimitsConfig)\r\n  readonly limits!: AILimitsConfig;\r\n}\r\n\r\nclass LogStorageConfig {\r\n  @IsBoolean()\r\n  @BooleanTransform()\r\n  readonly enabled!: boolean;\r\n\r\n  @IsString()\r\n  readonly type!: string;\r\n\r\n  @IsNumber()\r\n  @NumberTransform()\r\n  readonly retention!: number;\r\n}\r\n\r\nclass AuditConfig {\r\n  @IsBoolean()\r\n  @BooleanTransform()\r\n  readonly enabled!: boolean;\r\n\r\n  @ValidateNested()\r\n  @Type(() => LogStorageConfig)\r\n  readonly storage!: LogStorageConfig;\r\n}\r\n\r\nclass LoggingConfig {\r\n  @IsEnum(LogLevel)\r\n  readonly level!: LogLevel;\r\n\r\n  @IsBoolean()\r\n  @BooleanTransform()\r\n  readonly console!: boolean;\r\n\r\n  @IsBoolean()\r\n  @BooleanTransform()\r\n  readonly file!: boolean;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  readonly filePath?: string;\r\n\r\n  @IsBoolean()\r\n  @BooleanTransform()\r\n  readonly json!: boolean;\r\n\r\n  @IsBoolean()\r\n  @BooleanTransform()\r\n  readonly timestamp!: boolean;\r\n\r\n  @ValidateNested()\r\n  @Type(() => AuditConfig)\r\n  readonly audit!: AuditConfig;\r\n}\r\n\r\nclass EmailTemplatesConfig {\r\n  @IsString()\r\n  readonly passwordReset!: string;\r\n\r\n  @IsString()\r\n  readonly emailVerification!: string;\r\n\r\n  @IsString()\r\n  readonly welcomeEmail!: string;\r\n}\r\n\r\nclass EmailConfig {\r\n  @IsString()\r\n  readonly host!: string;\r\n\r\n  @IsPort()\r\n  @NumberTransform()\r\n  readonly port!: number;\r\n\r\n  @IsBoolean()\r\n  @BooleanTransform()\r\n  readonly secure!: boolean;\r\n\r\n  @IsString()\r\n  readonly username!: string;\r\n\r\n  @IsString()\r\n  readonly password!: string;\r\n\r\n  @IsEmail()\r\n  readonly from!: string;\r\n\r\n  @IsOptional()\r\n  @IsEmail()\r\n  readonly replyTo?: string;\r\n\r\n  @ValidateNested()\r\n  @Type(() => EmailTemplatesConfig)\r\n  readonly templates!: EmailTemplatesConfig;\r\n}\r\n\r\nexport class AppConfig {\r\n  @IsEnum(Environment)\r\n  readonly NODE_ENV!: Environment;\r\n\r\n  @IsPort()\r\n  @NumberTransform()\r\n  readonly PORT!: number;\r\n\r\n  @IsString()\r\n  readonly APP_NAME!: string;\r\n\r\n  @IsString()\r\n  readonly APP_VERSION!: string;\r\n\r\n  @IsUrl()\r\n  readonly APP_URL!: string;\r\n\r\n  @IsUrl()\r\n  readonly API_URL!: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  readonly API_PREFIX?: string;\r\n\r\n  @ValidateNested()\r\n  @Type(() => DatabaseConfig)\r\n  readonly database!: DatabaseConfig;\r\n\r\n  @ValidateNested()\r\n  @Type(() => RedisConfig)\r\n  readonly redis!: RedisConfig;\r\n\r\n  @ValidateNested()\r\n  @Type(() => AuthConfig)\r\n  readonly auth!: AuthConfig;\r\n\r\n  @ValidateNested()\r\n  @Type(() => SecurityConfig)\r\n  readonly security!: SecurityConfig;\r\n\r\n  @ValidateNested()\r\n  @Type(() => AIConfig)\r\n  readonly ai!: AIConfig;\r\n\r\n  @ValidateNested()\r\n  @Type(() => LoggingConfig)\r\n  readonly logging!: LoggingConfig;\r\n\r\n  @ValidateNested()\r\n  @Type(() => EmailConfig)\r\n  readonly email!: EmailConfig;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  readonly SENTRY_DSN?: string;\r\n\r\n  @IsOptional()\r\n  @IsBoolean()\r\n  @BooleanTransform()\r\n  readonly ENABLE_SWAGGER?: boolean;\r\n\r\n  @IsOptional()\r\n  @IsBoolean()\r\n  @BooleanTransform()\r\n  readonly ENABLE_METRICS?: boolean;\r\n}\r\n\r\n@Injectable()\r\nexport class ConfigValidationService {\r\n  constructor(private readonly configService: ConfigService) {}\r\n\r\n  validate(config: Record<string, unknown>): AppConfig {\r\n    const validatedConfig = plainToClass(AppConfig, config, {\r\n      enableImplicitConversion: true,\r\n      excludeExtraneousValues: true,\r\n    });\r\n\r\n    const errors = validateSync(validatedConfig, {\r\n      skipMissingProperties: false,\r\n      whitelist: true,\r\n      forbidNonWhitelisted: true,\r\n    });\r\n\r\n    if (errors.length > 0) {\r\n      throw new Error(`Configuration validation failed: ${this.formatErrors(errors)}`);\r\n    }\r\n\r\n    return validatedConfig;\r\n  }\r\n\r\n  private formatErrors(errors: ValidationError[]): string {\r\n    return errors\r\n      .map(error => this.formatError(error))\r\n      .filter(Boolean)\r\n      .join('; ');\r\n  }\r\n\r\n  private formatError(error: ValidationError, path = ''): string {\r\n    const currentPath = path ? `${path}.${error.property}` : error.property;\r\n    \r\n    if (error.constraints) {\r\n      return `${currentPath}: ${Object.values(error.constraints).join(', ')}`;\r\n    }\r\n    \r\n    if (error.children?.length) {\r\n      return error.children\r\n        .map(child => this.formatError(child, currentPath))\r\n        .filter(Boolean)\r\n        .join('; ');\r\n    }\r\n    \r\n    return '';\r\n  }\r\n}\r\n\r\nexport const validateConfig = (config: Record<string, unknown>): AppConfig => {\r\n  const validatedConfig = plainToClass(AppConfig, config, {\r\n    enableImplicitConversion: true,\r\n    excludeExtraneousValues: true,\r\n  });\r\n\r\n  const errors = validateSync(validatedConfig, {\r\n    skipMissingProperties: false,\r\n    whitelist: true,\r\n    forbidNonWhitelisted: true,\r\n  });\r\n\r\n  if (errors.length > 0) {\r\n    const errorMessages = errors\r\n      .map(error => {\r\n        const constraints = error.constraints;\r\n        return constraints ? Object.values(constraints).join(', ') : 'Unknown validation error';\r\n      })\r\n      .filter(Boolean);\r\n\r\n    throw new Error(`Configuration validation failed: ${errorMessages.join('; ')}`);\r\n  }\r\n\r\n  return validatedConfig;\r\n};\r\n\r\nexport const getDefaultConfig = (): Partial<AppConfig> => ({\r\n  NODE_ENV: Environment.DEVELOPMENT,\r\n  PORT: 3000,\r\n  APP_NAME: 'Sentinel',\r\n  APP_VERSION: '1.0.0',\r\n  API_PREFIX: 'api/v1',\r\n  ENABLE_SWAGGER: true,\r\n  ENABLE_METRICS: true,\r\n});\r\n\r\n// NestJS Configuration Factory\r\nexport const configFactory = (): AppConfig => {\r\n  return validateConfig(process.env);\r\n};"], "version": 3}