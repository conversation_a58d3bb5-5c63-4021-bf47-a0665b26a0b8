{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\validation\\validators\\is-cve-id.validator.ts", "mappings": ";;;;;;;;;AA+DA,0BAUC;AAQD,oCAuBC;AAxGD,qDAMyB;AAEzB;;;GAGG;AAEI,IAAM,iBAAiB,GAAvB,MAAM,iBAAiB;IAC5B;;;;;OAKG;IACH,QAAQ,CAAC,KAAU,EAAE,IAAyB;QAC5C,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,uEAAuE;QACvE,2DAA2D;QAC3D,MAAM,UAAU,GAAG,oBAAoB,CAAC;QAExC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,4CAA4C;QAC5C,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QAC9C,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACxC,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;YAE7C,yEAAyE;YACzE,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,WAAW,GAAG,CAAC,EAAE,CAAC;gBAC1C,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,cAAc,CAAC,IAAyB;QACtC,OAAO,GAAG,IAAI,CAAC,QAAQ,yDAAyD,CAAC;IACnF,CAAC;CACF,CAAA;AA3CY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,qCAAmB,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;GAC1C,iBAAiB,CA2C7B;AAED;;;;GAIG;AACH,SAAgB,OAAO,CAAC,iBAAqC;IAC3D,OAAO,UAAU,MAAc,EAAE,YAAoB;QACnD,IAAA,mCAAiB,EAAC;YAChB,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,OAAO,EAAE,iBAAiB;YAC1B,WAAW,EAAE,EAAE;YACf,SAAS,EAAE,iBAAiB;SAC7B,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AACH,SAAgB,YAAY,CAAC,iBAAqC;IAChE,OAAO,UAAU,MAAc,EAAE,YAAoB;QACnD,IAAA,mCAAiB,EAAC;YAChB,IAAI,EAAE,cAAc;YACpB,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,OAAO,EAAE,iBAAiB;YAC1B,WAAW,EAAE,EAAE;YACf,SAAS,EAAE;gBACT,QAAQ,CAAC,KAAU,EAAE,IAAyB;oBAC5C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;wBAC1B,OAAO,KAAK,CAAC;oBACf,CAAC;oBAED,MAAM,UAAU,GAAG,IAAI,iBAAiB,EAAE,CAAC;oBAC3C,OAAO,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;gBAC9D,CAAC;gBACD,cAAc,CAAC,IAAyB;oBACtC,OAAO,GAAG,IAAI,CAAC,QAAQ,4CAA4C,CAAC;gBACtE,CAAC;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAa,UAAU;IACrB;;;;OAIG;IACH,MAAM,CAAC,WAAW,CAAC,KAAa;QAC9B,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAClD,OAAO,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC/C,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,qBAAqB,CAAC,KAAa;QACxC,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAClD,OAAO,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC/C,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,aAAa,CAAC,IAAY,EAAE,cAAsB;QACvD,MAAM,cAAc,GAAG,cAAc,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAClE,OAAO,OAAO,IAAI,IAAI,cAAc,EAAE,CAAC;IACzC,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,OAAO,CAAC,KAAa;QAC1B,MAAM,UAAU,GAAG,IAAI,iBAAiB,EAAE,CAAC;QAC3C,OAAO,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAyB,CAAC,CAAC;IAC/D,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,SAAS,CAAC,KAAa;QAC5B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QACxC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;CACF;AA3DD,gCA2DC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\validation\\validators\\is-cve-id.validator.ts"], "sourcesContent": ["import {\r\n  registerDecorator,\r\n  ValidationOptions,\r\n  ValidatorConstraint,\r\n  ValidatorConstraintInterface,\r\n  ValidationArguments,\r\n} from 'class-validator';\r\n\r\n/**\r\n * CVE ID format validator constraint\r\n * Validates CVE identifiers according to MITRE CVE format\r\n */\r\n@ValidatorConstraint({ name: 'isCveId', async: false })\r\nexport class IsCveIdConstraint implements ValidatorConstraintInterface {\r\n  /**\r\n   * Validate CVE ID format\r\n   * @param value Value to validate\r\n   * @param args Validation arguments\r\n   * @returns boolean indicating if value is valid CVE ID\r\n   */\r\n  validate(value: any, args: ValidationArguments): boolean {\r\n    if (typeof value !== 'string') {\r\n      return false;\r\n    }\r\n\r\n    // CVE ID format: CVE-YYYY-NNNN or CVE-YYYY-NNNNN+ (where N is a digit)\r\n    // Examples: CVE-2021-1234, CVE-2023-12345, CVE-2024-123456\r\n    const cvePattern = /^CVE-\\d{4}-\\d{4,}$/;\r\n    \r\n    if (!cvePattern.test(value)) {\r\n      return false;\r\n    }\r\n\r\n    // Extract year and validate it's reasonable\r\n    const yearMatch = value.match(/CVE-(\\d{4})-/);\r\n    if (yearMatch) {\r\n      const year = parseInt(yearMatch[1], 10);\r\n      const currentYear = new Date().getFullYear();\r\n      \r\n      // CVE years should be between 1999 (first CVE year) and current year + 1\r\n      if (year < 1999 || year > currentYear + 1) {\r\n        return false;\r\n      }\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Default error message for CVE ID validation\r\n   * @param args Validation arguments\r\n   * @returns Error message\r\n   */\r\n  defaultMessage(args: ValidationArguments): string {\r\n    return `${args.property} must be a valid CVE identifier (format: CVE-YYYY-NNNN)`;\r\n  }\r\n}\r\n\r\n/**\r\n * CVE ID validation decorator\r\n * @param validationOptions Validation options\r\n * @returns Property decorator\r\n */\r\nexport function IsCveId(validationOptions?: ValidationOptions) {\r\n  return function (object: Object, propertyName: string) {\r\n    registerDecorator({\r\n      target: object.constructor,\r\n      propertyName: propertyName,\r\n      options: validationOptions,\r\n      constraints: [],\r\n      validator: IsCveIdConstraint,\r\n    });\r\n  };\r\n}\r\n\r\n/**\r\n * CVE ID array validation decorator\r\n * Validates an array of CVE IDs\r\n * @param validationOptions Validation options\r\n * @returns Property decorator\r\n */\r\nexport function IsCveIdArray(validationOptions?: ValidationOptions) {\r\n  return function (object: Object, propertyName: string) {\r\n    registerDecorator({\r\n      name: 'isCveIdArray',\r\n      target: object.constructor,\r\n      propertyName: propertyName,\r\n      options: validationOptions,\r\n      constraints: [],\r\n      validator: {\r\n        validate(value: any, args: ValidationArguments) {\r\n          if (!Array.isArray(value)) {\r\n            return false;\r\n          }\r\n\r\n          const constraint = new IsCveIdConstraint();\r\n          return value.every(item => constraint.validate(item, args));\r\n        },\r\n        defaultMessage(args: ValidationArguments) {\r\n          return `${args.property} must be an array of valid CVE identifiers`;\r\n        },\r\n      },\r\n    });\r\n  };\r\n}\r\n\r\n/**\r\n * Utility functions for CVE ID handling\r\n */\r\nexport class CveIdUtils {\r\n  /**\r\n   * Extract year from CVE ID\r\n   * @param cveId CVE identifier\r\n   * @returns Year or null if invalid\r\n   */\r\n  static extractYear(cveId: string): number | null {\r\n    const match = cveId.match(/^CVE-(\\d{4})-\\d{4,}$/);\r\n    return match ? parseInt(match[1], 10) : null;\r\n  }\r\n\r\n  /**\r\n   * Extract sequence number from CVE ID\r\n   * @param cveId CVE identifier\r\n   * @returns Sequence number or null if invalid\r\n   */\r\n  static extractSequenceNumber(cveId: string): number | null {\r\n    const match = cveId.match(/^CVE-\\d{4}-(\\d{4,})$/);\r\n    return match ? parseInt(match[1], 10) : null;\r\n  }\r\n\r\n  /**\r\n   * Generate CVE ID from components\r\n   * @param year Year\r\n   * @param sequenceNumber Sequence number\r\n   * @returns CVE ID string\r\n   */\r\n  static generateCveId(year: number, sequenceNumber: number): string {\r\n    const paddedSequence = sequenceNumber.toString().padStart(4, '0');\r\n    return `CVE-${year}-${paddedSequence}`;\r\n  }\r\n\r\n  /**\r\n   * Validate CVE ID format without decorator\r\n   * @param cveId CVE identifier to validate\r\n   * @returns boolean indicating validity\r\n   */\r\n  static isValid(cveId: string): boolean {\r\n    const constraint = new IsCveIdConstraint();\r\n    return constraint.validate(cveId, {} as ValidationArguments);\r\n  }\r\n\r\n  /**\r\n   * Normalize CVE ID format\r\n   * @param cveId CVE identifier\r\n   * @returns Normalized CVE ID or null if invalid\r\n   */\r\n  static normalize(cveId: string): string | null {\r\n    if (typeof cveId !== 'string') {\r\n      return null;\r\n    }\r\n\r\n    const upperCaseId = cveId.toUpperCase();\r\n    if (!this.isValid(upperCaseId)) {\r\n      return null;\r\n    }\r\n\r\n    return upperCaseId;\r\n  }\r\n}"], "version": 3}