{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\critical-vulnerability.specification.ts", "mappings": ";;;AAAA,yFAAqF;AAErF,wEAA+D;AAC/D,0EAAiE;AAEjE;;;;;;;;;;;;;;GAcG;AACH,MAAa,kCAAmC,SAAQ,sCAAgC;IAyBtF;;OAEG;IACI,aAAa,CAAC,aAA4B;QAC/C,OAAO,IAAI,CAAC,gCAAgC,CAAC,aAAa,CAAC;YACpD,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC;YACvC,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC;YACzC,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC;YACpC,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC;YACvC,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC;YACnC,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC;YACvC,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC;YACrC,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACK,gCAAgC,CAAC,aAA4B;QACnE,OAAO,aAAa,CAAC,QAAQ,KAAK,qCAAc,CAAC,QAAQ;YAClD,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,aAA4B;QACtD,MAAM,YAAY,GAAG,aAAa,CAAC,YAAY,CAAC;QAChD,OAAO,YAAY,EAAE,MAAM,KAAK,qBAAqB;YAC9C,YAAY,EAAE,MAAM,KAAK,YAAY,CAAC;IAC/C,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,aAA4B;QACxD,OAAO,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAC/C,KAAK,CAAC,WAAW,KAAK,UAAU,CACjC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,aAA4B;QACnD,OAAO,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAC3C,KAAK,CAAC,SAAS,IAAI,kCAAkC,CAAC,uBAAuB,CAC9E,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,aAA4B;QACtD,OAAO,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAC/C,KAAK,CAAC,QAAQ,KAAK,UAAU,IAAI,KAAK,CAAC,QAAQ,KAAK,OAAO,CAC5D,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,aAA4B;QAClD,MAAM,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;QAC9C,OAAO,kCAAkC,CAAC,4BAA4B,CAAC,IAAI,CACzE,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAC5C,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,aAA4B;QACtD,MAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;QACtD,OAAO,kCAAkC,CAAC,mBAAmB,CAAC,IAAI,CAChE,gBAAgB,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CACxD,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,aAA4B;QACpD,MAAM,YAAY,GAAG,aAAa,CAAC,YAAY,CAAC;QAChD,OAAO,YAAY,EAAE,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CACpD,OAAO,CAAC,IAAI,KAAK,QAAQ,IAAI,OAAO,CAAC,WAAW,IAAI,EAAE,CACvD,IAAI,KAAK,CAAC;IACb,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,aAA4B;QACnD,OAAO,aAAa,CAAC,cAAc,CAAC,SAAS,IAAI,kCAAkC,CAAC,uBAAuB,CAAC;IAC9G,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,UAA2B;QAClD,OAAO,UAAU,KAAK,uCAAe,CAAC,IAAI;YACnC,UAAU,KAAK,uCAAe,CAAC,SAAS;YACxC,UAAU,KAAK,uCAAe,CAAC,SAAS,CAAC;IAClD,CAAC;IAED;;OAEG;IACI,cAAc;QACnB,OAAO,yJAAyJ,CAAC;IACnK,CAAC;IAED;;OAEG;IACI,WAAW;QAChB,OAAO;YACL,wCAAwC;YACxC,sCAAsC;YACtC,yBAAyB;YACzB,iBAAiB,kCAAkC,CAAC,uBAAuB,EAAE;YAC7E,4BAA4B;YAC5B,gEAAgE;YAChE,8DAA8D;YAC9D,2BAA2B;YAC3B,iBAAiB,kCAAkC,CAAC,uBAAuB,EAAE;SAC9E,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,qBAAqB,CAAC,aAA4B;QACvD,IAAI,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC;YACvC,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,EAAE,CAAC;YAC1C,OAAO,WAAW,CAAC;QACrB,CAAC;QAED,IAAI,IAAI,CAAC,gCAAgC,CAAC,aAAa,CAAC;YACpD,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,EAAE,CAAC;YACzC,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,aAA4B;QACrD,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;QAE1D,QAAQ,OAAO,EAAE,CAAC;YAChB,KAAK,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,CAAG,UAAU;YACxC,KAAK,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAM,UAAU;YACxC,KAAK,MAAM,CAAC,CAAC,OAAO,EAAE,CAAC,CAAO,WAAW;YACzC,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC,CAAW,WAAW;QAC3C,CAAC;IACH,CAAC;IAED;;OAEG;IACI,yBAAyB,CAAC,aAA4B;QAO3D,MAAM,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;QACpE,MAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;QAClE,MAAM,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;QACpE,MAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;QAEhE,OAAO;YACL,qBAAqB,EAAE,mBAAmB;gBACrB,CAAC,eAAe,IAAI,mBAAmB,CAAC;YAC7D,iBAAiB,EAAE,mBAAmB,IAAI,eAAe;YACzD,oBAAoB,EAAE,mBAAmB;gBACrB,CAAC,iBAAiB,IAAI,eAAe,CAAC;YAC1D,mBAAmB,EAAE,eAAe,IAAI,mBAAmB;YAC3D,gBAAgB,EAAE,mBAAmB,IAAI,mBAAmB;SAC7D,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,qBAAqB,CAAC,aAA4B;QACvD,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,EAAE,CAAC;YAC5C,OAAO,CAAC,IAAI,CAAC,qBAAqB,EAAE,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;QAC9E,CAAC;QAED,IAAI,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,EAAE,CAAC;YAC5C,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,oBAAoB,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,EAAE,CAAC;YAC9C,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,sBAAsB,EAAE,wBAAwB,CAAC,CAAC;QACpF,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,EAAE,CAAC;YAC1C,OAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE,qBAAqB,EAAE,gBAAgB,CAAC,CAAC;QAChF,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,uBAAuB,CAAC,aAA4B;QACzD,MAAM,QAAQ,GAAa,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAChD,MAAM,UAAU,GAAG,IAAI,CAAC,yBAAyB,CAAC,aAAa,CAAC,CAAC;QAEjE,IAAI,UAAU,CAAC,iBAAiB,EAAE,CAAC;YACjC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,mBAAmB,CAAC,CAAC;QAC9D,CAAC;aAAM,IAAI,UAAU,CAAC,qBAAqB,EAAE,CAAC;YAC5C,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,UAAU,CAAC,mBAAmB,EAAE,CAAC;YACnC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACrC,CAAC;QAED,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;QAE7C,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACI,yBAAyB,CAAC,aAA4B;QAQ3D,MAAM,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;QACpE,MAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;QAChE,MAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;QAElE,OAAO;YACL,oBAAoB,EAAE,mBAAmB,IAAI,iBAAiB;YAC9D,aAAa,EAAE,mBAAmB,IAAI,CAAC,iBAAiB,IAAI,eAAe,CAAC;YAC5E,gBAAgB,EAAE,mBAAmB;YACrC,iBAAiB,EAAE,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC;YAC1D,kBAAkB,EAAE,eAAe;YACnC,WAAW,EAAE,IAAI,EAAE,+CAA+C;SACnE,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,yBAAyB,CAAC,aAA4B;QAO3D,MAAM,UAAU,GAAG,IAAI,CAAC,yBAAyB,CAAC,aAAa,CAAC,CAAC;QACjE,MAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;QAElE,MAAM,kBAAkB,GAAa,EAAE,CAAC;QACxC,MAAM,kBAAkB,GAAa,EAAE,CAAC;QAExC,IAAI,UAAU,CAAC,mBAAmB,EAAE,CAAC;YACnC,IAAI,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC5C,kBAAkB,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;YACrD,CAAC;YAED,IAAI,eAAe,EAAE,CAAC;gBACpB,kBAAkB,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;QAED,OAAO;YACL,kBAAkB;YAClB,kBAAkB;YAClB,qBAAqB,EAAE,IAAI;YAC3B,kBAAkB,EAAE,IAAI;YACxB,sBAAsB,EAAE,eAAe;SACxC,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,2BAA2B,CAAC,aAA4B;QAK7D,MAAM,SAAS,GAAa,EAAE,CAAC;QAC/B,MAAM,SAAS,GAAa,EAAE,CAAC;QAC/B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,IAAI,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,EAAE,CAAC;YAC5C,SAAS,CAAC,IAAI,CAAC,0BAA0B,EAAE,sBAAsB,EAAE,4BAA4B,CAAC,CAAC;QACnG,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,EAAE,CAAC;YAC1C,SAAS,CAAC,IAAI,CAAC,6BAA6B,EAAE,oBAAoB,CAAC,CAAC;YACpE,SAAS,CAAC,IAAI,CAAC,yBAAyB,EAAE,uBAAuB,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,EAAE,CAAC;YAC5C,SAAS,CAAC,IAAI,CAAC,0BAA0B,EAAE,kBAAkB,CAAC,CAAC;YAC/D,SAAS,CAAC,IAAI,CAAC,6BAA6B,EAAE,sBAAsB,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,EAAE,CAAC;YAC9C,SAAS,CAAC,IAAI,CAAC,iBAAiB,EAAE,kBAAkB,EAAE,qBAAqB,CAAC,CAAC;YAC7E,QAAQ,CAAC,IAAI,CAAC,qBAAqB,EAAE,+BAA+B,CAAC,CAAC;QACxE,CAAC;QAED,QAAQ,CAAC,IAAI,CAAC,sCAAsC,EAAE,6BAA6B,CAAC,CAAC;QAErF,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC;IAC5C,CAAC;;AAhWH,gFAiWC;AAhWyB,0DAAuB,GAAG,GAAG,CAAC;AAC9B,sDAAmB,GAAG,GAAG,CAAC;AAC1B,0DAAuB,GAAG,EAAE,CAAC;AAC7B,sDAAmB,GAAG,EAAE,CAAC;AAEzB,+DAA4B,GAAG;IACrD,uBAAuB;IACvB,sBAAsB;IACtB,uBAAuB;IACvB,eAAe;IACf,iBAAiB;IACjB,UAAU;IACV,UAAU;CACX,CAAC;AAEsB,sDAAmB,GAAG;IAC5C,uBAAuB;IACvB,sBAAsB;IACtB,gBAAgB;IAChB,WAAW;IACX,eAAe;IACf,mBAAmB;CACpB,CAAC;AA4UJ;;;;GAIG;AACH,MAAa,kCAAmC,SAAQ,sCAAgC;IAItF;;OAEG;IACI,aAAa,CAAC,aAA4B;QAC/C,OAAO,IAAI,CAAC,4BAA4B,CAAC,aAAa,CAAC;YAChD,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC;YACpC,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC;YACpC,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC;YACvC,IAAI,CAAC,4BAA4B,CAAC,aAAa,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACK,4BAA4B,CAAC,aAA4B;QAC/D,OAAO,aAAa,CAAC,QAAQ,KAAK,qCAAc,CAAC,IAAI;YAC9C,CAAC,aAAa,CAAC,UAAU,KAAK,uCAAe,CAAC,IAAI;gBACjD,aAAa,CAAC,UAAU,KAAK,uCAAe,CAAC,SAAS;gBACtD,aAAa,CAAC,UAAU,KAAK,uCAAe,CAAC,SAAS,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,aAA4B;QACnD,OAAO,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAC3C,KAAK,CAAC,SAAS,IAAI,kCAAkC,CAAC,mBAAmB,CAC1E,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,aAA4B;QACnD,OAAO,aAAa,CAAC,cAAc,CAAC,SAAS,IAAI,kCAAkC,CAAC,mBAAmB,CAAC;IAC1G,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,aAA4B;QACtD,OAAO,aAAa,CAAC,YAAY,EAAE,iBAAiB,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC;IAC3E,CAAC;IAED;;OAEG;IACK,4BAA4B,CAAC,aAA4B;QAC/D,OAAO,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAC/C,KAAK,CAAC,WAAW,KAAK,MAAM,IAAI,KAAK,CAAC,WAAW,KAAK,UAAU,CACjE,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,cAAc;QACnB,OAAO,gHAAgH,CAAC;IAC1H,CAAC;IAED;;OAEG;IACI,WAAW;QAChB,OAAO;YACL,oCAAoC;YACpC,iBAAiB,kCAAkC,CAAC,mBAAmB,EAAE;YACzE,iBAAiB,kCAAkC,CAAC,mBAAmB,EAAE;YACzE,oBAAoB;YACpB,iCAAiC;SAClC,CAAC;IACJ,CAAC;;AA3EH,gFA4EC;AA3EyB,sDAAmB,GAAG,GAAG,CAAC;AAC1B,sDAAmB,GAAG,EAAE,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\critical-vulnerability.specification.ts"], "sourcesContent": ["import { BaseSpecification } from '../../../shared-kernel/domain/base-specification';\r\nimport { Vulnerability } from '../entities/vulnerability/vulnerability.entity';\r\nimport { ThreatSeverity } from '../enums/threat-severity.enum';\r\nimport { ConfidenceLevel } from '../enums/confidence-level.enum';\r\n\r\n/**\r\n * Critical Vulnerability Specification\r\n * \r\n * Defines business rules for identifying critical vulnerabilities that require\r\n * immediate attention and emergency response procedures.\r\n * \r\n * Critical vulnerability criteria:\r\n * - Critical or high severity with high confidence\r\n * - Actively exploited vulnerabilities\r\n * - Vulnerabilities affecting critical assets\r\n * - High CVSS scores (9.0+)\r\n * - Externally exposed vulnerabilities\r\n * - Zero-day vulnerabilities\r\n * - Vulnerabilities with public exploits\r\n */\r\nexport class CriticalVulnerabilitySpecification extends BaseSpecification<Vulnerability> {\r\n  private static readonly CRITICAL_CVSS_THRESHOLD = 9.0;\r\n  private static readonly HIGH_CVSS_THRESHOLD = 7.0;\r\n  private static readonly CRITICAL_RISK_THRESHOLD = 80;\r\n  private static readonly HIGH_RISK_THRESHOLD = 70;\r\n\r\n  private static readonly CRITICAL_VULNERABILITY_TYPES = [\r\n    'remote_code_execution',\r\n    'privilege_escalation',\r\n    'authentication_bypass',\r\n    'sql_injection',\r\n    'buffer_overflow',\r\n    'zero_day',\r\n    'wormable',\r\n  ];\r\n\r\n  private static readonly CRITICAL_CATEGORIES = [\r\n    'remote_code_execution',\r\n    'privilege_escalation',\r\n    'authentication',\r\n    'injection',\r\n    'cryptographic',\r\n    'memory_corruption',\r\n  ];\r\n\r\n  /**\r\n   * Check if the vulnerability meets critical criteria\r\n   */\r\n  public isSatisfiedBy(vulnerability: Vulnerability): boolean {\r\n    return this.hasCriticalSeverityAndConfidence(vulnerability) ||\r\n           this.isActivelyExploited(vulnerability) ||\r\n           this.affectsCriticalAssets(vulnerability) ||\r\n           this.hasHighCVSSScore(vulnerability) ||\r\n           this.isExternallyExposed(vulnerability) ||\r\n           this.hasCriticalType(vulnerability) ||\r\n           this.hasCriticalCategory(vulnerability) ||\r\n           this.hasPublicExploits(vulnerability) ||\r\n           this.hasHighRiskScore(vulnerability);\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability has critical severity and high confidence\r\n   */\r\n  private hasCriticalSeverityAndConfidence(vulnerability: Vulnerability): boolean {\r\n    return vulnerability.severity === ThreatSeverity.CRITICAL &&\r\n           this.isHighConfidence(vulnerability.confidence);\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability is actively exploited\r\n   */\r\n  private isActivelyExploited(vulnerability: Vulnerability): boolean {\r\n    const exploitation = vulnerability.exploitation;\r\n    return exploitation?.status === 'active_exploitation' ||\r\n           exploitation?.status === 'weaponized';\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability affects critical assets\r\n   */\r\n  private affectsCriticalAssets(vulnerability: Vulnerability): boolean {\r\n    return vulnerability.affectedAssets.some(asset => \r\n      asset.criticality === 'critical'\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability has high CVSS score\r\n   */\r\n  private hasHighCVSSScore(vulnerability: Vulnerability): boolean {\r\n    return vulnerability.cvssScores.some(score => \r\n      score.baseScore >= CriticalVulnerabilitySpecification.CRITICAL_CVSS_THRESHOLD\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability is externally exposed\r\n   */\r\n  private isExternallyExposed(vulnerability: Vulnerability): boolean {\r\n    return vulnerability.affectedAssets.some(asset => \r\n      asset.exposure === 'external' || asset.exposure === 'cloud'\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability has critical type\r\n   */\r\n  private hasCriticalType(vulnerability: Vulnerability): boolean {\r\n    const type = vulnerability.type.toLowerCase();\r\n    return CriticalVulnerabilitySpecification.CRITICAL_VULNERABILITY_TYPES.some(\r\n      criticalType => type.includes(criticalType)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability has critical category\r\n   */\r\n  private hasCriticalCategory(vulnerability: Vulnerability): boolean {\r\n    const category = vulnerability.category.toLowerCase();\r\n    return CriticalVulnerabilitySpecification.CRITICAL_CATEGORIES.some(\r\n      criticalCategory => category.includes(criticalCategory)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability has public exploits\r\n   */\r\n  private hasPublicExploits(vulnerability: Vulnerability): boolean {\r\n    const exploitation = vulnerability.exploitation;\r\n    return exploitation?.availableExploits.some(exploit => \r\n      exploit.type === 'public' && exploit.reliability >= 70\r\n    ) || false;\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability has high risk score\r\n   */\r\n  private hasHighRiskScore(vulnerability: Vulnerability): boolean {\r\n    return vulnerability.riskAssessment.riskScore >= CriticalVulnerabilitySpecification.CRITICAL_RISK_THRESHOLD;\r\n  }\r\n\r\n  /**\r\n   * Check if confidence level is high\r\n   */\r\n  private isHighConfidence(confidence: ConfidenceLevel): boolean {\r\n    return confidence === ConfidenceLevel.HIGH ||\r\n           confidence === ConfidenceLevel.VERY_HIGH ||\r\n           confidence === ConfidenceLevel.CONFIRMED;\r\n  }\r\n\r\n  /**\r\n   * Get specification description\r\n   */\r\n  public getDescription(): string {\r\n    return 'Identifies critical vulnerabilities requiring immediate attention based on severity, exploitation status, asset criticality, CVSS scores, and exposure.';\r\n  }\r\n\r\n  /**\r\n   * Get specification criteria\r\n   */\r\n  public getCriteria(): string[] {\r\n    return [\r\n      'Critical severity with high confidence',\r\n      'Active exploitation or weaponization',\r\n      'Affects critical assets',\r\n      `CVSS score >= ${CriticalVulnerabilitySpecification.CRITICAL_CVSS_THRESHOLD}`,\r\n      'External or cloud exposure',\r\n      'Critical vulnerability types (RCE, privilege escalation, etc.)',\r\n      'Critical categories (injection, authentication bypass, etc.)',\r\n      'Public exploits available',\r\n      `Risk score >= ${CriticalVulnerabilitySpecification.CRITICAL_RISK_THRESHOLD}`,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Get remediation urgency for critical vulnerabilities\r\n   */\r\n  public getRemediationUrgency(vulnerability: Vulnerability): 'immediate' | 'urgent' | 'high' {\r\n    if (this.isActivelyExploited(vulnerability) || \r\n        this.hasPublicExploits(vulnerability)) {\r\n      return 'immediate';\r\n    }\r\n\r\n    if (this.hasCriticalSeverityAndConfidence(vulnerability) ||\r\n        this.hasHighCVSSScore(vulnerability)) {\r\n      return 'urgent';\r\n    }\r\n\r\n    return 'high';\r\n  }\r\n\r\n  /**\r\n   * Get response timeline in hours\r\n   */\r\n  public getResponseTimeline(vulnerability: Vulnerability): number {\r\n    const urgency = this.getRemediationUrgency(vulnerability);\r\n    \r\n    switch (urgency) {\r\n      case 'immediate': return 2;   // 2 hours\r\n      case 'urgent': return 8;      // 8 hours\r\n      case 'high': return 24;       // 24 hours\r\n      default: return 72;           // 72 hours\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get escalation requirements\r\n   */\r\n  public getEscalationRequirements(vulnerability: Vulnerability): {\r\n    executiveNotification: boolean;\r\n    emergencyResponse: boolean;\r\n    externalConsultation: boolean;\r\n    complianceReporting: boolean;\r\n    publicDisclosure: boolean;\r\n  } {\r\n    const isActivelyExploited = this.isActivelyExploited(vulnerability);\r\n    const affectsCritical = this.affectsCriticalAssets(vulnerability);\r\n    const isExternallyExposed = this.isExternallyExposed(vulnerability);\r\n    const hasPublicExploits = this.hasPublicExploits(vulnerability);\r\n\r\n    return {\r\n      executiveNotification: isActivelyExploited || \r\n                           (affectsCritical && isExternallyExposed),\r\n      emergencyResponse: isActivelyExploited && affectsCritical,\r\n      externalConsultation: isActivelyExploited || \r\n                          (hasPublicExploits && affectsCritical),\r\n      complianceReporting: affectsCritical || isExternallyExposed,\r\n      publicDisclosure: isActivelyExploited && isExternallyExposed,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get containment actions\r\n   */\r\n  public getContainmentActions(vulnerability: Vulnerability): string[] {\r\n    const actions: string[] = [];\r\n\r\n    if (this.isActivelyExploited(vulnerability)) {\r\n      actions.push('immediate_isolation', 'traffic_blocking', 'service_shutdown');\r\n    }\r\n\r\n    if (this.isExternallyExposed(vulnerability)) {\r\n      actions.push('firewall_rules', 'waf_protection', 'access_restriction');\r\n    }\r\n\r\n    if (this.affectsCriticalAssets(vulnerability)) {\r\n      actions.push('asset_isolation', 'network_segmentation', 'monitoring_enhancement');\r\n    }\r\n\r\n    if (this.hasPublicExploits(vulnerability)) {\r\n      actions.push('signature_deployment', 'behavior_monitoring', 'threat_hunting');\r\n    }\r\n\r\n    return actions;\r\n  }\r\n\r\n  /**\r\n   * Get notification channels\r\n   */\r\n  public getNotificationChannels(vulnerability: Vulnerability): string[] {\r\n    const channels: string[] = ['email', 'webhook'];\r\n    const escalation = this.getEscalationRequirements(vulnerability);\r\n\r\n    if (escalation.emergencyResponse) {\r\n      channels.push('sms', 'pager', 'phone', 'emergency_hotline');\r\n    } else if (escalation.executiveNotification) {\r\n      channels.push('sms', 'executive_dashboard');\r\n    }\r\n\r\n    if (escalation.complianceReporting) {\r\n      channels.push('compliance_portal');\r\n    }\r\n\r\n    channels.push('slack', 'security_dashboard');\r\n\r\n    return channels;\r\n  }\r\n\r\n  /**\r\n   * Get monitoring requirements\r\n   */\r\n  public getMonitoringRequirements(vulnerability: Vulnerability): {\r\n    continuousMonitoring: boolean;\r\n    threatHunting: boolean;\r\n    behaviorAnalysis: boolean;\r\n    networkMonitoring: boolean;\r\n    endpointMonitoring: boolean;\r\n    logAnalysis: boolean;\r\n  } {\r\n    const isActivelyExploited = this.isActivelyExploited(vulnerability);\r\n    const hasPublicExploits = this.hasPublicExploits(vulnerability);\r\n    const affectsCritical = this.affectsCriticalAssets(vulnerability);\r\n\r\n    return {\r\n      continuousMonitoring: isActivelyExploited || hasPublicExploits,\r\n      threatHunting: isActivelyExploited || (hasPublicExploits && affectsCritical),\r\n      behaviorAnalysis: isActivelyExploited,\r\n      networkMonitoring: this.isExternallyExposed(vulnerability),\r\n      endpointMonitoring: affectsCritical,\r\n      logAnalysis: true, // Always required for critical vulnerabilities\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get compliance requirements\r\n   */\r\n  public getComplianceRequirements(vulnerability: Vulnerability): {\r\n    immediateReporting: string[];\r\n    scheduledReporting: string[];\r\n    documentationRequired: boolean;\r\n    auditTrailRequired: boolean;\r\n    riskAssessmentRequired: boolean;\r\n  } {\r\n    const escalation = this.getEscalationRequirements(vulnerability);\r\n    const affectsCritical = this.affectsCriticalAssets(vulnerability);\r\n\r\n    const immediateReporting: string[] = [];\r\n    const scheduledReporting: string[] = [];\r\n\r\n    if (escalation.complianceReporting) {\r\n      if (this.isActivelyExploited(vulnerability)) {\r\n        immediateReporting.push('CISA', 'SEC', 'GDPR_DPA');\r\n      }\r\n      \r\n      if (affectsCritical) {\r\n        scheduledReporting.push('SOX', 'PCI_DSS', 'HIPAA');\r\n      }\r\n    }\r\n\r\n    return {\r\n      immediateReporting,\r\n      scheduledReporting,\r\n      documentationRequired: true,\r\n      auditTrailRequired: true,\r\n      riskAssessmentRequired: affectsCritical,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get risk mitigation strategies\r\n   */\r\n  public getRiskMitigationStrategies(vulnerability: Vulnerability): {\r\n    immediate: string[];\r\n    shortTerm: string[];\r\n    longTerm: string[];\r\n  } {\r\n    const immediate: string[] = [];\r\n    const shortTerm: string[] = [];\r\n    const longTerm: string[] = [];\r\n\r\n    if (this.isActivelyExploited(vulnerability)) {\r\n      immediate.push('isolate_affected_systems', 'block_attack_vectors', 'activate_incident_response');\r\n    }\r\n\r\n    if (this.hasPublicExploits(vulnerability)) {\r\n      immediate.push('deploy_detection_signatures', 'enhance_monitoring');\r\n      shortTerm.push('apply_emergency_patches', 'implement_workarounds');\r\n    }\r\n\r\n    if (this.isExternallyExposed(vulnerability)) {\r\n      immediate.push('restrict_external_access', 'deploy_waf_rules');\r\n      shortTerm.push('review_network_architecture', 'implement_zero_trust');\r\n    }\r\n\r\n    if (this.affectsCriticalAssets(vulnerability)) {\r\n      shortTerm.push('asset_hardening', 'privilege_review', 'backup_verification');\r\n      longTerm.push('architecture_review', 'security_controls_enhancement');\r\n    }\r\n\r\n    longTerm.push('vulnerability_management_improvement', 'security_awareness_training');\r\n\r\n    return { immediate, shortTerm, longTerm };\r\n  }\r\n}\r\n\r\n/**\r\n * High Risk Vulnerability Specification\r\n * \r\n * Extended specification for vulnerabilities that are high risk but not necessarily critical.\r\n */\r\nexport class HighRiskVulnerabilitySpecification extends BaseSpecification<Vulnerability> {\r\n  private static readonly HIGH_CVSS_THRESHOLD = 7.0;\r\n  private static readonly HIGH_RISK_THRESHOLD = 70;\r\n\r\n  /**\r\n   * Check if the vulnerability meets high risk criteria\r\n   */\r\n  public isSatisfiedBy(vulnerability: Vulnerability): boolean {\r\n    return this.hasHighSeverityAndConfidence(vulnerability) ||\r\n           this.hasHighCVSSScore(vulnerability) ||\r\n           this.hasHighRiskScore(vulnerability) ||\r\n           this.hasExploitAvailable(vulnerability) ||\r\n           this.affectsHighCriticalityAssets(vulnerability);\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability has high severity and confidence\r\n   */\r\n  private hasHighSeverityAndConfidence(vulnerability: Vulnerability): boolean {\r\n    return vulnerability.severity === ThreatSeverity.HIGH &&\r\n           (vulnerability.confidence === ConfidenceLevel.HIGH ||\r\n            vulnerability.confidence === ConfidenceLevel.VERY_HIGH ||\r\n            vulnerability.confidence === ConfidenceLevel.CONFIRMED);\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability has high CVSS score\r\n   */\r\n  private hasHighCVSSScore(vulnerability: Vulnerability): boolean {\r\n    return vulnerability.cvssScores.some(score => \r\n      score.baseScore >= HighRiskVulnerabilitySpecification.HIGH_CVSS_THRESHOLD\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability has high risk score\r\n   */\r\n  private hasHighRiskScore(vulnerability: Vulnerability): boolean {\r\n    return vulnerability.riskAssessment.riskScore >= HighRiskVulnerabilitySpecification.HIGH_RISK_THRESHOLD;\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability has available exploits\r\n   */\r\n  private hasExploitAvailable(vulnerability: Vulnerability): boolean {\r\n    return vulnerability.exploitation?.availableExploits.length > 0 || false;\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability affects high criticality assets\r\n   */\r\n  private affectsHighCriticalityAssets(vulnerability: Vulnerability): boolean {\r\n    return vulnerability.affectedAssets.some(asset => \r\n      asset.criticality === 'high' || asset.criticality === 'critical'\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get specification description\r\n   */\r\n  public getDescription(): string {\r\n    return 'Identifies high-risk vulnerabilities that require prioritized attention and accelerated remediation timelines.';\r\n  }\r\n\r\n  /**\r\n   * Get specification criteria\r\n   */\r\n  public getCriteria(): string[] {\r\n    return [\r\n      'High severity with high confidence',\r\n      `CVSS score >= ${HighRiskVulnerabilitySpecification.HIGH_CVSS_THRESHOLD}`,\r\n      `Risk score >= ${HighRiskVulnerabilitySpecification.HIGH_RISK_THRESHOLD}`,\r\n      'Exploits available',\r\n      'Affects high or critical assets',\r\n    ];\r\n  }\r\n}\r\n"], "version": 3}