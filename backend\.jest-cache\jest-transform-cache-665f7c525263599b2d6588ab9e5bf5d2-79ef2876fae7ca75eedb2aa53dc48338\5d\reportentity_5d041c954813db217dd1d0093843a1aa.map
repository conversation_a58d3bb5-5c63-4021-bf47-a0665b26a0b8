{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting-analytics\\domain\\entities\\report.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCAUiB;AACjB,uEAA4D;AAC5D,qEAA0D;AAE1D;;;GAGG;AAOI,IAAM,MAAM,GAAZ,MAAM,MAAM;IAiRjB;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAI,kBAAkB;QACpB,IAAI,CAAC,IAAI,CAAC,cAAc;YAAE,OAAO,KAAK,CAAC;QACvC,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAC7D,OAAO,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,sBAAsB;QACxB,OAAO,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,QAAQ,EAAE,sBAAsB,IAAI,KAAK,CAAC,CAAC,qBAAqB;IACvG,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,aAAqB,EAAE,SAAiB,EAAE,MAAc;QAC3E,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC;QACjC,IAAI,CAAC,mBAAmB,GAAG,MAAa,CAAC;QACzC,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;QAE/B,gCAAgC;QAChC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAChC,CAAC,IAAI,CAAC,gBAAgB,GAAG,CAAC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,GAAG,aAAa,CAAC,GAAG,IAAI,CAAC,cAAc,CAC1F,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,gBAAgB,GAAG,aAAa,CAAC;QACxC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,wBAAwB;QACxB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACnF,MAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;QAC5D,CAAC;QAED,sBAAsB;QACtB,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;YACjC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;YACzE,IAAI,IAAI,KAAK,UAAU,IAAI,CAAC,KAAK,EAAE,CAAC;gBAClC,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YACvD,CAAC;YACD,IAAI,IAAI,KAAK,UAAU,IAAI,CAAC,CAAC,SAAS,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBACpD,MAAM,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;YACvE,CAAC;QACH,CAAC;QAED,0BAA0B;QAC1B,IAAI,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;gBACvD,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC;oBAC/C,MAAM,CAAC,IAAI,CAAC,iBAAiB,KAAK,GAAG,CAAC,6BAA6B,CAAC,CAAC;gBACvE,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACP,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAY,EAAE,aAAsB,KAAK;QAC7C,OAAO;YACL,IAAI;YACJ,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,MAAM,EAAE,OAAO;YACf,UAAU;YACV,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;YAC5C,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC7D,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACzE,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YAC9F,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YAChH,QAAQ,EAAE;gBACR,GAAG,IAAI,CAAC,QAAQ;gBAChB,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,SAAS,EAAE,yBAAyB;aAC7C;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,IAAI,EAAE,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,UAAkB;QAC/B,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,yBAAyB;QACvB,OAAO,IAAI,CAAC,aAAa,CAAC,aAAa,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,OAAiB;QAC7B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACrB,CAAC;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,OAAO,CAAC;QACxD,MAAM,YAAY,GAAG,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC3D,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,0BAA0B;QAChD,MAAM,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAE1C,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,UAAU,CAAC;QAEnC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;YAC7B,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,EAAE,CAAC;QAC/B,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC;YAC3B,OAAO,EAAE,UAAU;YACnB,IAAI,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YAC9B,OAAO;SACR,CAAC,CAAC;IACL,CAAC;CACF,CAAA;AA7aY,wBAAM;AAEjB;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;kCACpB;AAMX;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;oCACX;AAMb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;2CACpB;AAsBrB;IAjBC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE;YACJ,uBAAuB;YACvB,iBAAiB;YACjB,mBAAmB;YACnB,qBAAqB;YACrB,qBAAqB;YACrB,iBAAiB;YACjB,cAAc;YACd,sBAAsB;YACtB,gBAAgB;YAChB,qBAAqB;YACrB,cAAc;YACd,cAAc;SACf;KACF,CAAC;;oCACW;AAUb;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,WAAW,EAAE,WAAW,CAAC;QACzE,OAAO,EAAE,UAAU;KACpB,CAAC;;wCAC8E;AAUhF;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,CAAC;QACnD,OAAO,EAAE,OAAO;KACjB,CAAC;;sCACqD;AAMvD;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;0CAC5B;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;0CAC1C;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;;6CAmDxB;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sCAmCxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;6CAYhE;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,sBAAsB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDAQtE;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;wCAaxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACtE,IAAI,oBAAJ,IAAI;8CAAC;AAMtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,uBAAuB,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDACW;AAMrE;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;8CAC1C;AAMvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;gDAC9C;AAM1B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;6CAC7C;AAMvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;yCAC3B;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;yCAC1C;AAGnB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;yCAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;yCAAC;AAIhB;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,yCAAe,EAAE,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC;;0CAClC;AAG9B;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,uCAAc,EAAE,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC;;yCACjC;AAI5B;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC3C,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;8BACzB,MAAM;wCAAC;iBA/QP,MAAM;IANlB,IAAA,gBAAM,EAAC,SAAS,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,MAAM,CAAC,CAAC;IACf,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,WAAW,CAAC,CAAC;IACpB,IAAA,eAAK,EAAC,CAAC,YAAY,CAAC,CAAC;GACT,MAAM,CA6alB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting-analytics\\domain\\entities\\report.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  OneToMany,\r\n  ManyToOne,\r\n  JoinColumn,\r\n} from 'typeorm';\r\nimport { ReportExecution } from './report-execution.entity';\r\nimport { ReportSchedule } from './report-schedule.entity';\r\n\r\n/**\r\n * Report entity\r\n * Represents report templates and configurations for generating various types of reports\r\n */\r\n@Entity('reports')\r\n@Index(['type'])\r\n@Index(['category'])\r\n@Index(['status'])\r\n@Index(['createdBy'])\r\n@Index(['isTemplate'])\r\nexport class Report {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * Report name\r\n   */\r\n  @Column({ length: 255 })\r\n  name: string;\r\n\r\n  /**\r\n   * Report description\r\n   */\r\n  @Column({ type: 'text', nullable: true })\r\n  description?: string;\r\n\r\n  /**\r\n   * Report type\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: [\r\n      'vulnerability_summary',\r\n      'risk_assessment',\r\n      'compliance_report',\r\n      'executive_dashboard',\r\n      'threat_intelligence',\r\n      'asset_inventory',\r\n      'scan_results',\r\n      'remediation_tracking',\r\n      'trend_analysis',\r\n      'performance_metrics',\r\n      'audit_report',\r\n      'custom_query',\r\n    ],\r\n  })\r\n  type: string;\r\n\r\n  /**\r\n   * Report category\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['security', 'compliance', 'operational', 'executive', 'technical'],\r\n    default: 'security',\r\n  })\r\n  category: 'security' | 'compliance' | 'operational' | 'executive' | 'technical';\r\n\r\n  /**\r\n   * Report status\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['draft', 'active', 'archived', 'deprecated'],\r\n    default: 'draft',\r\n  })\r\n  status: 'draft' | 'active' | 'archived' | 'deprecated';\r\n\r\n  /**\r\n   * Whether this is a template report\r\n   */\r\n  @Column({ name: 'is_template', default: false })\r\n  isTemplate: boolean;\r\n\r\n  /**\r\n   * Template ID if this report is based on a template\r\n   */\r\n  @Column({ name: 'template_id', type: 'uuid', nullable: true })\r\n  templateId?: string;\r\n\r\n  /**\r\n   * Report configuration and parameters\r\n   */\r\n  @Column({ type: 'jsonb' })\r\n  configuration: {\r\n    // Data sources\r\n    dataSources: string[];\r\n    \r\n    // Time range settings\r\n    timeRange?: {\r\n      type: 'relative' | 'absolute';\r\n      value: string; // e.g., '30d', '1y' for relative, or ISO dates for absolute\r\n      startDate?: string;\r\n      endDate?: string;\r\n    };\r\n    \r\n    // Filters\r\n    filters?: {\r\n      assetTypes?: string[];\r\n      severities?: string[];\r\n      statuses?: string[];\r\n      tags?: string[];\r\n      assignees?: string[];\r\n      customFilters?: Record<string, any>;\r\n    };\r\n    \r\n    // Grouping and aggregation\r\n    groupBy?: string[];\r\n    aggregations?: Array<{\r\n      field: string;\r\n      function: 'count' | 'sum' | 'avg' | 'min' | 'max';\r\n      alias?: string;\r\n    }>;\r\n    \r\n    // Sorting\r\n    sortBy?: Array<{\r\n      field: string;\r\n      direction: 'asc' | 'desc';\r\n    }>;\r\n    \r\n    // Visualization settings\r\n    visualizations?: Array<{\r\n      type: 'table' | 'chart' | 'metric' | 'trend';\r\n      chartType?: 'bar' | 'line' | 'pie' | 'donut' | 'area' | 'scatter';\r\n      title: string;\r\n      dataSource: string;\r\n      configuration: Record<string, any>;\r\n    }>;\r\n    \r\n    // Export settings\r\n    exportFormats?: string[];\r\n    \r\n    // Custom SQL or query\r\n    customQuery?: string;\r\n  };\r\n\r\n  /**\r\n   * Report layout and styling\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  layout?: {\r\n    pageSize?: 'A4' | 'A3' | 'Letter' | 'Legal';\r\n    orientation?: 'portrait' | 'landscape';\r\n    margins?: {\r\n      top: number;\r\n      right: number;\r\n      bottom: number;\r\n      left: number;\r\n    };\r\n    header?: {\r\n      enabled: boolean;\r\n      content: string;\r\n      height?: number;\r\n    };\r\n    footer?: {\r\n      enabled: boolean;\r\n      content: string;\r\n      height?: number;\r\n    };\r\n    branding?: {\r\n      logo?: string;\r\n      colors?: {\r\n        primary: string;\r\n        secondary: string;\r\n        accent: string;\r\n      };\r\n    };\r\n    sections?: Array<{\r\n      id: string;\r\n      type: 'text' | 'chart' | 'table' | 'metric';\r\n      title?: string;\r\n      content: any;\r\n      styling?: Record<string, any>;\r\n    }>;\r\n  };\r\n\r\n  /**\r\n   * Access control settings\r\n   */\r\n  @Column({ name: 'access_control', type: 'jsonb', nullable: true })\r\n  accessControl?: {\r\n    visibility: 'private' | 'shared' | 'public';\r\n    allowedRoles?: string[];\r\n    allowedUsers?: string[];\r\n    permissions?: {\r\n      view: boolean;\r\n      edit: boolean;\r\n      execute: boolean;\r\n      schedule: boolean;\r\n      share: boolean;\r\n    };\r\n  };\r\n\r\n  /**\r\n   * Performance and caching settings\r\n   */\r\n  @Column({ name: 'performance_settings', type: 'jsonb', nullable: true })\r\n  performanceSettings?: {\r\n    cacheEnabled?: boolean;\r\n    cacheTtl?: number; // seconds\r\n    maxExecutionTime?: number; // seconds\r\n    maxRows?: number;\r\n    enablePagination?: boolean;\r\n    pageSize?: number;\r\n  };\r\n\r\n  /**\r\n   * Report metadata\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  metadata?: {\r\n    version?: string;\r\n    author?: string;\r\n    tags?: string[];\r\n    estimatedExecutionTime?: number;\r\n    dataFreshness?: string;\r\n    dependencies?: string[];\r\n    changelog?: Array<{\r\n      version: string;\r\n      date: string;\r\n      changes: string[];\r\n    }>;\r\n  };\r\n\r\n  /**\r\n   * Last execution information\r\n   */\r\n  @Column({ name: 'last_executed_at', type: 'timestamp with time zone', nullable: true })\r\n  lastExecutedAt?: Date;\r\n\r\n  /**\r\n   * Last execution status\r\n   */\r\n  @Column({ name: 'last_execution_status', nullable: true })\r\n  lastExecutionStatus?: 'success' | 'failed' | 'timeout' | 'cancelled';\r\n\r\n  /**\r\n   * Execution count\r\n   */\r\n  @Column({ name: 'execution_count', type: 'integer', default: 0 })\r\n  executionCount: number;\r\n\r\n  /**\r\n   * Average execution time in milliseconds\r\n   */\r\n  @Column({ name: 'avg_execution_time', type: 'integer', nullable: true })\r\n  avgExecutionTime?: number;\r\n\r\n  /**\r\n   * Report size in bytes (last execution)\r\n   */\r\n  @Column({ name: 'last_size_bytes', type: 'bigint', nullable: true })\r\n  lastSizeBytes?: number;\r\n\r\n  /**\r\n   * User who created the report\r\n   */\r\n  @Column({ name: 'created_by', type: 'uuid' })\r\n  createdBy: string;\r\n\r\n  /**\r\n   * User who last updated the report\r\n   */\r\n  @Column({ name: 'updated_by', type: 'uuid', nullable: true })\r\n  updatedBy?: string;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  // Relationships\r\n  @OneToMany(() => ReportExecution, execution => execution.report)\r\n  executions: ReportExecution[];\r\n\r\n  @OneToMany(() => ReportSchedule, schedule => schedule.report)\r\n  schedules: ReportSchedule[];\r\n\r\n  @ManyToOne(() => Report, { nullable: true })\r\n  @JoinColumn({ name: 'template_id' })\r\n  template?: Report;\r\n\r\n  /**\r\n   * Check if report is active\r\n   */\r\n  get isActive(): boolean {\r\n    return this.status === 'active';\r\n  }\r\n\r\n  /**\r\n   * Check if report is a template\r\n   */\r\n  get isReportTemplate(): boolean {\r\n    return this.isTemplate;\r\n  }\r\n\r\n  /**\r\n   * Check if report has been executed recently\r\n   */\r\n  get hasRecentExecution(): boolean {\r\n    if (!this.lastExecutedAt) return false;\r\n    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);\r\n    return this.lastExecutedAt > oneDayAgo;\r\n  }\r\n\r\n  /**\r\n   * Get estimated execution time\r\n   */\r\n  get estimatedExecutionTime(): number {\r\n    return this.avgExecutionTime || this.metadata?.estimatedExecutionTime || 30000; // 30 seconds default\r\n  }\r\n\r\n  /**\r\n   * Update execution statistics\r\n   */\r\n  updateExecutionStats(executionTime: number, sizeBytes: number, status: string): void {\r\n    this.executionCount += 1;\r\n    this.lastExecutedAt = new Date();\r\n    this.lastExecutionStatus = status as any;\r\n    this.lastSizeBytes = sizeBytes;\r\n\r\n    // Update average execution time\r\n    if (this.avgExecutionTime) {\r\n      this.avgExecutionTime = Math.round(\r\n        (this.avgExecutionTime * (this.executionCount - 1) + executionTime) / this.executionCount\r\n      );\r\n    } else {\r\n      this.avgExecutionTime = executionTime;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate report configuration\r\n   */\r\n  validateConfiguration(): { isValid: boolean; errors: string[] } {\r\n    const errors: string[] = [];\r\n\r\n    // Check required fields\r\n    if (!this.configuration.dataSources || this.configuration.dataSources.length === 0) {\r\n      errors.push('At least one data source must be specified');\r\n    }\r\n\r\n    // Validate time range\r\n    if (this.configuration.timeRange) {\r\n      const { type, value, startDate, endDate } = this.configuration.timeRange;\r\n      if (type === 'relative' && !value) {\r\n        errors.push('Relative time range value is required');\r\n      }\r\n      if (type === 'absolute' && (!startDate || !endDate)) {\r\n        errors.push('Absolute time range requires both start and end dates');\r\n      }\r\n    }\r\n\r\n    // Validate visualizations\r\n    if (this.configuration.visualizations) {\r\n      this.configuration.visualizations.forEach((viz, index) => {\r\n        if (!viz.type || !viz.title || !viz.dataSource) {\r\n          errors.push(`Visualization ${index + 1} is missing required fields`);\r\n        }\r\n      });\r\n    }\r\n\r\n    return {\r\n      isValid: errors.length === 0,\r\n      errors,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Clone report as new template or instance\r\n   */\r\n  clone(name: string, isTemplate: boolean = false): Partial<Report> {\r\n    return {\r\n      name,\r\n      description: this.description,\r\n      type: this.type,\r\n      category: this.category,\r\n      status: 'draft',\r\n      isTemplate,\r\n      templateId: isTemplate ? undefined : this.id,\r\n      configuration: JSON.parse(JSON.stringify(this.configuration)),\r\n      layout: this.layout ? JSON.parse(JSON.stringify(this.layout)) : undefined,\r\n      accessControl: this.accessControl ? JSON.parse(JSON.stringify(this.accessControl)) : undefined,\r\n      performanceSettings: this.performanceSettings ? JSON.parse(JSON.stringify(this.performanceSettings)) : undefined,\r\n      metadata: {\r\n        ...this.metadata,\r\n        version: '1.0.0',\r\n        author: undefined, // Will be set by creator\r\n      },\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get data sources used by this report\r\n   */\r\n  getDataSources(): string[] {\r\n    return this.configuration.dataSources || [];\r\n  }\r\n\r\n  /**\r\n   * Check if report uses specific data source\r\n   */\r\n  usesDataSource(dataSource: string): boolean {\r\n    return this.getDataSources().includes(dataSource);\r\n  }\r\n\r\n  /**\r\n   * Get export formats supported by this report\r\n   */\r\n  getSupportedExportFormats(): string[] {\r\n    return this.configuration.exportFormats || ['pdf', 'excel', 'csv'];\r\n  }\r\n\r\n  /**\r\n   * Update report version\r\n   */\r\n  updateVersion(changes: string[]): void {\r\n    if (!this.metadata) {\r\n      this.metadata = {};\r\n    }\r\n\r\n    const currentVersion = this.metadata.version || '1.0.0';\r\n    const versionParts = currentVersion.split('.').map(Number);\r\n    versionParts[2] += 1; // Increment patch version\r\n    const newVersion = versionParts.join('.');\r\n\r\n    this.metadata.version = newVersion;\r\n\r\n    if (!this.metadata.changelog) {\r\n      this.metadata.changelog = [];\r\n    }\r\n\r\n    this.metadata.changelog.push({\r\n      version: newVersion,\r\n      date: new Date().toISOString(),\r\n      changes,\r\n    });\r\n  }\r\n}\r\n"], "version": 3}