e8b1f7e7d89926f1c18013eb0334ff1f
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const enriched_event_entity_1 = require("../enriched-event.entity");
const shared_kernel_1 = require("../../../../../shared-kernel");
const event_metadata_value_object_1 = require("../../value-objects/event-metadata/event-metadata.value-object");
const event_timestamp_value_object_1 = require("../../value-objects/event-metadata/event-timestamp.value-object");
const event_source_value_object_1 = require("../../value-objects/event-metadata/event-source.value-object");
const event_type_enum_1 = require("../../enums/event-type.enum");
const event_severity_enum_1 = require("../../enums/event-severity.enum");
const event_status_enum_1 = require("../../enums/event-status.enum");
const event_processing_status_enum_1 = require("../../enums/event-processing-status.enum");
const event_source_type_enum_1 = require("../../enums/event-source-type.enum");
const enrichment_source_enum_1 = require("../../enums/enrichment-source.enum");
describe('EnrichedEvent Entity', () => {
    let validProps;
    let mockMetadata;
    let mockEnrichmentRule;
    let mockEnrichmentData;
    beforeEach(() => {
        // Create mock metadata
        const eventSource = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.SIEM, 'test-siem-001');
        const eventTimestamp = event_timestamp_value_object_1.EventTimestamp.create();
        mockMetadata = event_metadata_value_object_1.EventMetadata.create(eventTimestamp, eventSource);
        // Create mock enrichment rule
        mockEnrichmentRule = {
            id: 'test-rule-001',
            name: 'Test Enrichment Rule',
            description: 'A test enrichment rule',
            priority: 100,
            required: false,
            sources: [enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION],
            config: { timeout: 5000 },
        };
        // Create mock enrichment data
        mockEnrichmentData = {
            source: enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION,
            type: 'reputation',
            data: { score: 75, category: 'clean' },
            confidence: 85,
            timestamp: new Date(),
        };
        // Create valid props
        validProps = {
            normalizedEventId: shared_kernel_1.UniqueEntityId.create(),
            metadata: mockMetadata,
            type: event_type_enum_1.EventType.THREAT_DETECTED,
            severity: event_severity_enum_1.EventSeverity.MEDIUM,
            status: event_status_enum_1.EventStatus.ACTIVE,
            processingStatus: event_processing_status_enum_1.EventProcessingStatus.ENRICHED,
            enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.COMPLETED,
            normalizedData: { event_type: 'threat', normalized: true },
            enrichedData: { event_type: 'threat', enriched: true, threat_score: 45 },
            title: 'Test Enriched Event',
            description: 'A test enriched security event',
            tags: ['test', 'enriched'],
            riskScore: 65,
            confidenceLevel: 80,
            attributes: { test: true },
            appliedRules: [mockEnrichmentRule],
            enrichmentData: [mockEnrichmentData],
            enrichmentQualityScore: 85,
            threatIntelScore: 45,
        };
    });
    describe('creation', () => {
        it('should create a valid enriched event with required properties', () => {
            const enrichedEvent = enriched_event_entity_1.EnrichedEvent.create(validProps);
            expect(enrichedEvent).toBeInstanceOf(enriched_event_entity_1.EnrichedEvent);
            expect(enrichedEvent.normalizedEventId).toEqual(validProps.normalizedEventId);
            expect(enrichedEvent.type).toBe(event_type_enum_1.EventType.THREAT_DETECTED);
            expect(enrichedEvent.severity).toBe(event_severity_enum_1.EventSeverity.MEDIUM);
            expect(enrichedEvent.enrichmentStatus).toBe(enriched_event_entity_1.EnrichmentStatus.COMPLETED);
            expect(enrichedEvent.title).toBe('Test Enriched Event');
            expect(enrichedEvent.enrichmentQualityScore).toBe(85);
            expect(enrichedEvent.threatIntelScore).toBe(45);
        });
        it('should generate domain event on creation', () => {
            const enrichedEvent = enriched_event_entity_1.EnrichedEvent.create(validProps);
            const domainEvents = enrichedEvent.getUncommittedEvents();
            expect(domainEvents).toHaveLength(1);
            expect(domainEvents[0].constructor.name).toBe('EnrichedEventCreatedDomainEvent');
        });
        it('should assign unique ID if not provided', () => {
            const enrichedEvent = enriched_event_entity_1.EnrichedEvent.create(validProps);
            expect(enrichedEvent.id).toBeDefined();
            expect(enrichedEvent.id).toBeInstanceOf(shared_kernel_1.UniqueEntityId);
        });
        it('should use provided ID if given', () => {
            const customId = shared_kernel_1.UniqueEntityId.create();
            const enrichedEvent = enriched_event_entity_1.EnrichedEvent.create(validProps, customId);
            expect(enrichedEvent.id).toEqual(customId);
        });
    });
    describe('validation', () => {
        it('should throw error if normalized event ID is missing', () => {
            const invalidProps = { ...validProps };
            delete invalidProps.normalizedEventId;
            expect(() => enriched_event_entity_1.EnrichedEvent.create(invalidProps)).toThrow('EnrichedEvent must reference a normalized event');
        });
        it('should throw error if metadata is missing', () => {
            const invalidProps = { ...validProps };
            delete invalidProps.metadata;
            expect(() => enriched_event_entity_1.EnrichedEvent.create(invalidProps)).toThrow('EnrichedEvent must have metadata');
        });
        it('should throw error if enrichment status is missing', () => {
            const invalidProps = { ...validProps };
            delete invalidProps.enrichmentStatus;
            expect(() => enriched_event_entity_1.EnrichedEvent.create(invalidProps)).toThrow('EnrichedEvent must have an enrichment status');
        });
        it('should throw error if normalized data is missing', () => {
            const invalidProps = { ...validProps };
            delete invalidProps.normalizedData;
            expect(() => enriched_event_entity_1.EnrichedEvent.create(invalidProps)).toThrow('EnrichedEvent must have normalized data');
        });
        it('should throw error if enriched data is missing', () => {
            const invalidProps = { ...validProps };
            delete invalidProps.enrichedData;
            expect(() => enriched_event_entity_1.EnrichedEvent.create(invalidProps)).toThrow('EnrichedEvent must have enriched data');
        });
        it('should throw error if title is empty', () => {
            const invalidProps = { ...validProps, title: '' };
            expect(() => enriched_event_entity_1.EnrichedEvent.create(invalidProps)).toThrow('EnrichedEvent must have a non-empty title');
        });
        it('should throw error if enrichment quality score is out of range', () => {
            const invalidProps = { ...validProps, enrichmentQualityScore: 150 };
            expect(() => enriched_event_entity_1.EnrichedEvent.create(invalidProps)).toThrow('Enrichment quality score must be between 0 and 100');
        });
        it('should throw error if threat intelligence score is out of range', () => {
            const invalidProps = { ...validProps, threatIntelScore: -10 };
            expect(() => enriched_event_entity_1.EnrichedEvent.create(invalidProps)).toThrow('Threat intelligence score must be between 0 and 100');
        });
        it('should throw error if too many enrichment data sources', () => {
            const manyEnrichmentData = Array.from({ length: 51 }, (_, i) => ({
                ...mockEnrichmentData,
                source: enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION,
                type: `type-${i}`,
            }));
            const invalidProps = { ...validProps, enrichmentData: manyEnrichmentData };
            expect(() => enriched_event_entity_1.EnrichedEvent.create(invalidProps)).toThrow('EnrichedEvent cannot have more than 50 enrichment data sources');
        });
    });
    describe('enrichment lifecycle', () => {
        let enrichedEvent;
        beforeEach(() => {
            const pendingProps = { ...validProps, enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.PENDING };
            enrichedEvent = enriched_event_entity_1.EnrichedEvent.create(pendingProps);
            enrichedEvent.markEventsAsCommitted(); // Clear creation event
        });
        it('should start enrichment process', () => {
            enrichedEvent.startEnrichment();
            expect(enrichedEvent.enrichmentStatus).toBe(enriched_event_entity_1.EnrichmentStatus.IN_PROGRESS);
            expect(enrichedEvent.enrichmentStartedAt).toBeDefined();
            expect(enrichedEvent.enrichmentAttempts).toBe(1);
        });
        it('should complete enrichment process', () => {
            enrichedEvent.startEnrichment();
            const result = {
                success: true,
                appliedRules: ['rule1', 'rule2'],
                failedRules: [],
                warnings: [],
                errors: [],
                processingDurationMs: 1500,
                confidenceScore: 85,
                sourcesUsed: 3,
                dataPointsEnriched: 10,
            };
            enrichedEvent.completeEnrichment(result);
            expect(enrichedEvent.enrichmentStatus).toBe(enriched_event_entity_1.EnrichmentStatus.COMPLETED);
            expect(enrichedEvent.enrichmentCompletedAt).toBeDefined();
            expect(enrichedEvent.enrichmentResult).toEqual(result);
            expect(enrichedEvent.lastEnrichmentError).toBeUndefined();
            const domainEvents = enrichedEvent.getUncommittedEvents();
            expect(domainEvents).toHaveLength(1);
            expect(domainEvents[0].constructor.name).toBe('EnrichedEventStatusChangedDomainEvent');
        });
        it('should fail enrichment process', () => {
            enrichedEvent.startEnrichment();
            const error = 'Network timeout during enrichment';
            enrichedEvent.failEnrichment(error);
            expect(enrichedEvent.enrichmentStatus).toBe(enriched_event_entity_1.EnrichmentStatus.FAILED);
            expect(enrichedEvent.lastEnrichmentError).toBe(error);
            const domainEvents = enrichedEvent.getUncommittedEvents();
            expect(domainEvents).toHaveLength(1);
            expect(domainEvents[0].constructor.name).toBe('EnrichedEventEnrichmentFailedDomainEvent');
        });
        it('should skip enrichment process', () => {
            const reason = 'Skipped due to low priority';
            enrichedEvent.skipEnrichment(reason);
            expect(enrichedEvent.enrichmentStatus).toBe(enriched_event_entity_1.EnrichmentStatus.SKIPPED);
            expect(enrichedEvent.reviewNotes).toBe(reason);
            expect(enrichedEvent.lastEnrichmentError).toBeUndefined();
        });
        it('should reset enrichment for retry', () => {
            enrichedEvent.startEnrichment();
            enrichedEvent.failEnrichment('Test error');
            enrichedEvent.resetEnrichment();
            expect(enrichedEvent.enrichmentStatus).toBe(enriched_event_entity_1.EnrichmentStatus.PENDING);
            expect(enrichedEvent.enrichmentStartedAt).toBeUndefined();
            expect(enrichedEvent.enrichmentCompletedAt).toBeUndefined();
            expect(enrichedEvent.lastEnrichmentError).toBeUndefined();
            expect(enrichedEvent.enrichmentResult).toBeUndefined();
        });
        it('should throw error when starting enrichment if not pending', () => {
            enrichedEvent.startEnrichment();
            expect(() => enrichedEvent.startEnrichment()).toThrow('Can only start enrichment for pending events');
        });
        it('should throw error when completing enrichment if not in progress', () => {
            const result = {
                success: true,
                appliedRules: [],
                failedRules: [],
                warnings: [],
                errors: [],
                processingDurationMs: 1000,
                confidenceScore: 80,
                sourcesUsed: 1,
                dataPointsEnriched: 5,
            };
            expect(() => enrichedEvent.completeEnrichment(result)).toThrow('Can only complete enrichment for in-progress events');
        });
    });
    describe('enrichment data management', () => {
        let enrichedEvent;
        beforeEach(() => {
            enrichedEvent = enriched_event_entity_1.EnrichedEvent.create(validProps);
        });
        it('should add enrichment data', () => {
            const newEnrichmentData = {
                source: enrichment_source_enum_1.EnrichmentSource.IP_GEOLOCATION,
                type: 'geolocation',
                data: { country: 'US', city: 'New York' },
                confidence: 95,
                timestamp: new Date(),
            };
            enrichedEvent.addEnrichmentData(newEnrichmentData);
            const enrichmentData = enrichedEvent.enrichmentData;
            expect(enrichmentData).toHaveLength(2);
            expect(enrichmentData[1]).toEqual(newEnrichmentData);
        });
        it('should update existing enrichment data with same source and type', () => {
            const updatedData = {
                source: enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION,
                type: 'reputation',
                data: { score: 90, category: 'trusted' },
                confidence: 95,
                timestamp: new Date(),
            };
            enrichedEvent.addEnrichmentData(updatedData);
            const enrichmentData = enrichedEvent.enrichmentData;
            expect(enrichmentData).toHaveLength(1);
            expect(enrichmentData[0].data.score).toBe(90);
            expect(enrichmentData[0].data.category).toBe('trusted');
        });
        it('should update enriched data', () => {
            const additionalData = { new_field: 'new_value', updated_field: 'updated' };
            enrichedEvent.updateEnrichedData(additionalData);
            const enrichedData = enrichedEvent.enrichedData;
            expect(enrichedData.new_field).toBe('new_value');
            expect(enrichedData.updated_field).toBe('updated');
        });
        it('should add applied enrichment rule', () => {
            const newRule = {
                id: 'new-rule-001',
                name: 'New Rule',
                description: 'A new enrichment rule',
                priority: 50,
                required: true,
                sources: [enrichment_source_enum_1.EnrichmentSource.THREAT_INTELLIGENCE],
            };
            enrichedEvent.addAppliedRule(newRule);
            const appliedRules = enrichedEvent.appliedRules;
            expect(appliedRules).toHaveLength(2);
            expect(appliedRules[1]).toEqual(newRule);
        });
        it('should not add duplicate applied rule', () => {
            enrichedEvent.addAppliedRule(mockEnrichmentRule);
            const appliedRules = enrichedEvent.appliedRules;
            expect(appliedRules).toHaveLength(1);
        });
    });
    describe('context management', () => {
        let enrichedEvent;
        beforeEach(() => {
            enrichedEvent = enriched_event_entity_1.EnrichedEvent.create(validProps);
        });
        it('should update asset context', () => {
            const assetContext = { owner: 'IT Team', criticality: 'high' };
            enrichedEvent.updateAssetContext(assetContext);
            expect(enrichedEvent.assetContext).toEqual(assetContext);
        });
        it('should update user context', () => {
            const userContext = { username: 'john.doe', department: 'Engineering' };
            enrichedEvent.updateUserContext(userContext);
            expect(enrichedEvent.userContext).toEqual(userContext);
        });
        it('should update network context', () => {
            const networkContext = { subnet: '192.168.1.0/24', vlan: 'DMZ' };
            enrichedEvent.updateNetworkContext(networkContext);
            expect(enrichedEvent.networkContext).toEqual(networkContext);
        });
        it('should update geolocation context', () => {
            const geoContext = { country: 'US', city: 'San Francisco' };
            enrichedEvent.updateGeolocationContext(geoContext);
            expect(enrichedEvent.geolocationContext).toEqual(geoContext);
        });
        it('should add reputation score', () => {
            enrichedEvent.addReputationScore('virustotal', 85);
            enrichedEvent.addReputationScore('shodan', 70);
            const reputationScores = enrichedEvent.reputationScores;
            expect(reputationScores.virustotal).toBe(85);
            expect(reputationScores.shodan).toBe(70);
        });
        it('should throw error for invalid reputation score', () => {
            expect(() => enrichedEvent.addReputationScore('test', 150)).toThrow('Reputation score must be between 0 and 100');
        });
    });
    describe('scoring and quality', () => {
        let enrichedEvent;
        beforeEach(() => {
            enrichedEvent = enriched_event_entity_1.EnrichedEvent.create(validProps);
        });
        it('should update threat intelligence score', () => {
            enrichedEvent.updateThreatIntelScore(75);
            expect(enrichedEvent.threatIntelScore).toBe(75);
        });
        it('should throw error for invalid threat intelligence score', () => {
            expect(() => enrichedEvent.updateThreatIntelScore(-5)).toThrow('Threat intelligence score must be between 0 and 100');
        });
        it('should update enrichment quality score', () => {
            enrichedEvent.updateEnrichmentQualityScore(90);
            expect(enrichedEvent.enrichmentQualityScore).toBe(90);
        });
        it('should throw error for invalid enrichment quality score', () => {
            expect(() => enrichedEvent.updateEnrichmentQualityScore(105)).toThrow('Enrichment quality score must be between 0 and 100');
        });
    });
    describe('manual review', () => {
        let enrichedEvent;
        beforeEach(() => {
            enrichedEvent = enriched_event_entity_1.EnrichedEvent.create(validProps);
        });
        it('should mark for manual review', () => {
            const reason = 'High threat intelligence score detected';
            enrichedEvent.markForManualReview(reason);
            expect(enrichedEvent.requiresManualReview).toBe(true);
            expect(enrichedEvent.reviewNotes).toBe(reason);
        });
        it('should complete manual review', () => {
            enrichedEvent.markForManualReview('Test reason');
            const reviewer = '<EMAIL>';
            const notes = 'Reviewed and approved';
            enrichedEvent.completeManualReview(reviewer, notes);
            expect(enrichedEvent.reviewedBy).toBe(reviewer);
            expect(enrichedEvent.reviewedAt).toBeDefined();
            expect(enrichedEvent.reviewNotes).toBe(notes);
        });
        it('should throw error when completing review if not marked for review', () => {
            expect(() => enrichedEvent.completeManualReview('<EMAIL>')).toThrow('Event is not marked for manual review');
        });
    });
    describe('validation errors', () => {
        let enrichedEvent;
        beforeEach(() => {
            enrichedEvent = enriched_event_entity_1.EnrichedEvent.create(validProps);
        });
        it('should add validation errors', () => {
            const errors = ['Error 1', 'Error 2'];
            enrichedEvent.addValidationErrors(errors);
            expect(enrichedEvent.validationErrors).toEqual(errors);
            expect(enrichedEvent.hasValidationErrors()).toBe(true);
        });
        it('should clear validation errors', () => {
            enrichedEvent.addValidationErrors(['Error 1']);
            enrichedEvent.clearValidationErrors();
            expect(enrichedEvent.validationErrors).toEqual([]);
            expect(enrichedEvent.hasValidationErrors()).toBe(false);
        });
        it('should throw error for too many validation errors', () => {
            const manyErrors = Array.from({ length: 11 }, (_, i) => `Error ${i + 1}`);
            expect(() => enrichedEvent.addValidationErrors(manyErrors)).toThrow('Cannot have more than 10 validation errors');
        });
    });
    describe('query methods', () => {
        let enrichedEvent;
        beforeEach(() => {
            enrichedEvent = enriched_event_entity_1.EnrichedEvent.create(validProps);
        });
        it('should check enrichment completion status', () => {
            expect(enrichedEvent.isEnrichmentCompleted()).toBe(true);
            expect(enrichedEvent.isEnrichmentFailed()).toBe(false);
            expect(enrichedEvent.isEnrichmentInProgress()).toBe(false);
            expect(enrichedEvent.isEnrichmentPartial()).toBe(false);
            expect(enrichedEvent.isEnrichmentSkipped()).toBe(false);
        });
        it('should check enrichment quality', () => {
            expect(enrichedEvent.hasHighEnrichmentQuality()).toBe(true);
        });
        it('should check threat intelligence risk', () => {
            expect(enrichedEvent.isHighThreatRisk()).toBe(false);
            enrichedEvent.updateThreatIntelScore(90);
            expect(enrichedEvent.isHighThreatRisk()).toBe(true);
        });
        it('should check data availability', () => {
            expect(enrichedEvent.hasThreatIntelligence()).toBe(false);
            expect(enrichedEvent.hasReputationData()).toBe(false);
            expect(enrichedEvent.hasGeolocationData()).toBe(false);
            enrichedEvent.addReputationScore('test', 75);
            expect(enrichedEvent.hasReputationData()).toBe(true);
        });
        it('should check readiness for next stage', () => {
            expect(enrichedEvent.isReadyForNextStage()).toBe(true);
            enrichedEvent.addValidationErrors(['Test error']);
            expect(enrichedEvent.isReadyForNextStage()).toBe(false);
        });
        it('should get enrichment duration', () => {
            const propsWithTiming = {
                ...validProps,
                enrichmentStartedAt: new Date(Date.now() - 5000),
                enrichmentCompletedAt: new Date(),
            };
            const timedEvent = enriched_event_entity_1.EnrichedEvent.create(propsWithTiming);
            const duration = timedEvent.getEnrichmentDuration();
            expect(duration).toBeGreaterThan(4000);
            expect(duration).toBeLessThan(6000);
        });
        it('should get applied rule names', () => {
            const ruleNames = enrichedEvent.getAppliedRuleNames();
            expect(ruleNames).toEqual(['Test Enrichment Rule']);
        });
        it('should check if specific rule was applied', () => {
            expect(enrichedEvent.hasAppliedRule('test-rule-001')).toBe(true);
            expect(enrichedEvent.hasAppliedRule('non-existent-rule')).toBe(false);
        });
        it('should get enrichment data by source', () => {
            const ipReputationData = enrichedEvent.getEnrichmentDataBySource(enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION);
            expect(ipReputationData).toHaveLength(1);
            expect(ipReputationData[0].type).toBe('reputation');
            const geoData = enrichedEvent.getEnrichmentDataBySource(enrichment_source_enum_1.EnrichmentSource.IP_GEOLOCATION);
            expect(geoData).toHaveLength(0);
        });
        it('should get enrichment data by type', () => {
            const reputationData = enrichedEvent.getEnrichmentDataByType('reputation');
            expect(reputationData).toHaveLength(1);
            expect(reputationData[0].source).toBe(enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION);
            const geoData = enrichedEvent.getEnrichmentDataByType('geolocation');
            expect(geoData).toHaveLength(0);
        });
        it('should get average reputation score', () => {
            expect(enrichedEvent.getAverageReputationScore()).toBeNull();
            enrichedEvent.addReputationScore('source1', 80);
            enrichedEvent.addReputationScore('source2', 60);
            expect(enrichedEvent.getAverageReputationScore()).toBe(70);
        });
        it('should check max attempts exceeded', () => {
            expect(enrichedEvent.hasExceededMaxEnrichmentAttempts()).toBe(false);
            // Simulate multiple failed attempts
            const propsWithAttempts = { ...validProps, enrichmentAttempts: 3 };
            const eventWithAttempts = enriched_event_entity_1.EnrichedEvent.create(propsWithAttempts);
            expect(eventWithAttempts.hasExceededMaxEnrichmentAttempts()).toBe(true);
        });
    });
    describe('summary and serialization', () => {
        let enrichedEvent;
        beforeEach(() => {
            enrichedEvent = enriched_event_entity_1.EnrichedEvent.create(validProps);
        });
        it('should generate event summary', () => {
            const summary = enrichedEvent.getSummary();
            expect(summary).toMatchObject({
                id: enrichedEvent.id.toString(),
                normalizedEventId: validProps.normalizedEventId.toString(),
                title: 'Test Enriched Event',
                type: event_type_enum_1.EventType.THREAT_DETECTED,
                severity: event_severity_enum_1.EventSeverity.MEDIUM,
                status: event_status_enum_1.EventStatus.ACTIVE,
                enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.COMPLETED,
                enrichmentQualityScore: 85,
                threatIntelScore: 45,
                appliedRulesCount: 1,
                enrichmentDataCount: 1,
                hasValidationErrors: false,
                requiresManualReview: false,
                isReadyForNextStage: true,
                hasThreatIntelligence: false,
                hasReputationData: false,
            });
        });
        it('should serialize to JSON', () => {
            const json = enrichedEvent.toJSON();
            expect(json).toHaveProperty('id');
            expect(json).toHaveProperty('normalizedEventId');
            expect(json).toHaveProperty('metadata');
            expect(json).toHaveProperty('enrichedData');
            expect(json).toHaveProperty('appliedRules');
            expect(json).toHaveProperty('enrichmentData');
            expect(json).toHaveProperty('summary');
            expect(json.type).toBe(event_type_enum_1.EventType.THREAT_DETECTED);
            expect(json.enrichmentStatus).toBe(enriched_event_entity_1.EnrichmentStatus.COMPLETED);
        });
    });
    describe('immutability', () => {
        let enrichedEvent;
        beforeEach(() => {
            enrichedEvent = enriched_event_entity_1.EnrichedEvent.create(validProps);
        });
        it('should return copies of mutable properties', () => {
            const normalizedData = enrichedEvent.normalizedData;
            const enrichedData = enrichedEvent.enrichedData;
            const tags = enrichedEvent.tags;
            const attributes = enrichedEvent.attributes;
            const appliedRules = enrichedEvent.appliedRules;
            const enrichmentData = enrichedEvent.enrichmentData;
            // Modify returned objects
            normalizedData.modified = true;
            enrichedData.modified = true;
            tags.push('modified');
            attributes.modified = true;
            appliedRules.push({});
            enrichmentData.push({});
            // Original should be unchanged
            expect(enrichedEvent.normalizedData.modified).toBeUndefined();
            expect(enrichedEvent.enrichedData.modified).toBeUndefined();
            expect(enrichedEvent.tags).not.toContain('modified');
            expect(enrichedEvent.attributes.modified).toBeUndefined();
            expect(enrichedEvent.appliedRules).toHaveLength(1);
            expect(enrichedEvent.enrichmentData).toHaveLength(1);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************