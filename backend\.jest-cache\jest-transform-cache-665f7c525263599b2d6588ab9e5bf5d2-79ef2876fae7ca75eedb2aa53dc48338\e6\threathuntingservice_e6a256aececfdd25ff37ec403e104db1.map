{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\application\\services\\threat-hunting.service.ts", "mappings": ";;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,kIAAmH;AACnH,gIAAkH;AAClH,sHAAwG;AACxG,4HAA8G;AAC9G,kFAAyE;AACzE,oFAA2E;AAC3E,iGAA4F;AAwK5F;;;;;;;;;;;;;;GAcG;AAEI,IAAM,oBAAoB,GAA1B,MAAM,oBAAoB;IAC/B,YACmB,uBAAgD,EAChD,uBAAgD,EAChD,kBAAsC,EACtC,qBAA4C,EAC5C,cAAoC;QAJpC,4BAAuB,GAAvB,uBAAuB,CAAyB;QAChD,4BAAuB,GAAvB,uBAAuB,CAAyB;QAChD,uBAAkB,GAAlB,kBAAkB,CAAoB;QACtC,0BAAqB,GAArB,qBAAqB,CAAuB;QAC5C,mBAAc,GAAd,cAAc,CAAsB;IACpD,CAAC;IAEJ;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,KAAyB;QACzC,MAAM,WAAW,GAAG,QAAQ,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QACzC,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,8BAA8B;YAC9B,MAAM,MAAM,GAAwB;gBAClC,KAAK;gBACL,SAAS,EAAE;oBACT,WAAW;oBACX,SAAS;oBACT,OAAO,EAAE,IAAI,IAAI,EAAE;oBACnB,QAAQ,EAAE,CAAC;oBACX,MAAM,EAAE,SAAS;oBACjB,QAAQ,EAAE,CAAC;iBACZ;gBACD,OAAO,EAAE;oBACP,YAAY,EAAE,CAAC;oBACf,qBAAqB,EAAE,CAAC;oBACxB,oBAAoB,EAAE,CAAC;oBACvB,gBAAgB,EAAE,KAAK,CAAC,UAAU,CAAC,SAAS;oBAC5C,iBAAiB,EAAE,EAAE;oBACrB,sBAAsB,EAAE,EAAE;iBAC3B;gBACD,QAAQ,EAAE,EAAE;gBACZ,QAAQ,EAAE;oBACR,gBAAgB,EAAE;wBAChB,kBAAkB,EAAE,KAAK;wBACzB,sBAAsB,EAAE,EAAE;wBAC1B,mBAAmB,EAAE,EAAE;qBACxB;oBACD,cAAc,EAAE,EAAE;oBAClB,iBAAiB,EAAE;wBACjB,MAAM,EAAE,EAAE;wBACV,WAAW,EAAE,CAAC;qBACf;oBACD,eAAe,EAAE;wBACf,SAAS,EAAE,EAAE;wBACb,SAAS,EAAE,EAAE;wBACb,QAAQ,EAAE,EAAE;wBACZ,UAAU,EAAE,EAAE;qBACf;iBACF;gBACD,OAAO,EAAE;oBACP,YAAY,EAAE,CAAC;oBACf,kBAAkB,EAAE,CAAC;oBACrB,gBAAgB,EAAE,CAAC;oBACnB,uBAAuB,EAAE,CAAC;iBAC3B;aACF,CAAC;YAEF,mCAAmC;YACnC,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;gBACnB,KAAK,YAAY;oBACf,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;oBACzC,MAAM;gBACR,KAAK,qBAAqB;oBACxB,MAAM,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;oBAChD,MAAM;gBACR,KAAK,mBAAmB;oBACtB,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;oBAC7C,MAAM;gBACR,KAAK,kBAAkB;oBACrB,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;oBAC7C,MAAM;gBACR,KAAK,mBAAmB;oBACtB,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;oBAC9C,MAAM;gBACR;oBACE,MAAM,IAAI,KAAK,CAAC,0BAA0B,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;YAC5D,CAAC;YAED,mBAAmB;YACnB,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;YAEzC,4BAA4B;YAC5B,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;YAErC,qBAAqB;YACrB,MAAM,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;YAC3B,MAAM,CAAC,SAAS,CAAC,OAAO,GAAG,OAAO,CAAC;YACnC,MAAM,CAAC,SAAS,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;YACpE,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,WAAW,CAAC;YACtC,MAAM,CAAC,SAAS,CAAC,QAAQ,GAAG,GAAG,CAAC;YAEhC,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;YAC3B,OAAO;gBACL,GAAG,MAAM;gBACT,SAAS,EAAE;oBACT,WAAW;oBACX,SAAS;oBACT,OAAO;oBACP,QAAQ,EAAE,OAAO,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE;oBACjD,MAAM,EAAE,QAAQ;oBAChB,QAAQ,EAAE,CAAC;iBACZ;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc,CAAC,KAAyB,EAAE,MAA2B;QACjF,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,IAAI,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjE,OAAO;QACT,CAAC;QAED,KAAK,MAAM,GAAG,IAAI,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;YACxC,oCAAoC;YACpC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;YAEjF,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBAC3B,MAAM,OAAO,GAAG;oBACd,EAAE,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;oBACxC,IAAI,EAAE,WAAoB;oBAC1B,UAAU,EAAE,GAAG,CAAC,UAAU;oBAC1B,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,KAAK,CAAC;oBAC5C,WAAW,EAAE,oBAAoB,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,KAAK,EAAE;oBAC1D,QAAQ,EAAE,CAAC;4BACT,IAAI,EAAE,OAAgB;4BACtB,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI;4BACzB,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK;4BAChC,IAAI,EAAE;gCACJ,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE;gCAC5B,OAAO,EAAE,GAAG,CAAC,IAAI;gCACjB,QAAQ,EAAE,GAAG,CAAC,KAAK;gCACnB,SAAS,EAAE,KAAK,CAAC,OAAO;6BACzB;yBACF,CAAC;oBACF,cAAc,EAAE,KAAK,CAAC,cAAc,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE;oBACvE,aAAa,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC;oBACpC,kBAAkB,EAAE,EAAE;oBACtB,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,CAAC,GAAG,EAAE,KAAK,CAAC;iBAC3D,CAAC;gBAEF,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC9B,MAAM,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;gBAE9B,IAAI,GAAG,CAAC,UAAU,KAAK,uCAAe,CAAC,IAAI,IAAI,GAAG,CAAC,UAAU,KAAK,uCAAe,CAAC,SAAS,EAAE,CAAC;oBAC5F,MAAM,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC;gBACzC,CAAC;YACH,CAAC;QACH,CAAC;QAED,kBAAkB;QAClB,MAAM,CAAC,SAAS,CAAC,QAAQ,GAAG,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CAAC,KAAyB,EAAE,MAA2B;QACxF,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,IAAI,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzE,OAAO;QACT,CAAC;QAED,iDAAiD;QACjD,uCAAuC;QACvC,MAAM,iBAAiB,GAAG,IAAI,CAAC,mCAAmC,CAAC,KAAK,CAAC,CAAC;QAC1E,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,CAAC;QAC3C,MAAM,CAAC,OAAO,CAAC,YAAY,IAAI,iBAAiB,CAAC,MAAM,CAAC;QACxD,MAAM,CAAC,SAAS,CAAC,QAAQ,GAAG,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,KAAyB,EAAE,MAA2B;QACrF,+CAA+C;QAC/C,uCAAuC;QACvC,MAAM,iBAAiB,GAAG,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAC;QACvE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,CAAC;QAC3C,MAAM,CAAC,OAAO,CAAC,YAAY,IAAI,iBAAiB,CAAC,MAAM,CAAC;QACxD,MAAM,CAAC,SAAS,CAAC,QAAQ,GAAG,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,KAAyB,EAAE,MAA2B;QACrF,8CAA8C;QAC9C,MAAM,iBAAiB,GAAG,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAC;QACvE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,CAAC;QAC3C,MAAM,CAAC,OAAO,CAAC,YAAY,IAAI,iBAAiB,CAAC,MAAM,CAAC;QACxD,MAAM,CAAC,SAAS,CAAC,QAAQ,GAAG,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,KAAyB,EAAE,MAA2B;QACtF,+CAA+C;QAC/C,MAAM,iBAAiB,GAAG,IAAI,CAAC,iCAAiC,CAAC,KAAK,CAAC,CAAC;QACxE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,CAAC;QAC3C,MAAM,CAAC,OAAO,CAAC,YAAY,IAAI,iBAAiB,CAAC,MAAM,CAAC;QACxD,MAAM,CAAC,SAAS,CAAC,QAAQ,GAAG,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CAAC,MAA2B;QAC7D,8BAA8B;QAC9B,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,GAAG,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAErG,wBAAwB;QACxB,MAAM,CAAC,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAE3E,iCAAiC;QACjC,MAAM,CAAC,QAAQ,CAAC,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAE3E,2BAA2B;QAC3B,MAAM,CAAC,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;QAEvE,mCAAmC;QACnC,MAAM,YAAY,GAAG,IAAI,GAAG,EAAU,CAAC;QACvC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAChC,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QACH,MAAM,CAAC,OAAO,CAAC,oBAAoB,GAAG,YAAY,CAAC,IAAI,CAAC;QAExD,oCAAoC;QACpC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAChC,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,OAAO,CAAC,UAAU,CAAC;gBACvD,CAAC,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,MAA2B;QACzD,uCAAuC;QACvC,MAAM,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,OAAO,CAAC,YAAY,GAAG,EAAE,CAAC,CAAC;QAE9E,gCAAgC;QAChC,MAAM,CAAC,OAAO,CAAC,kBAAkB,GAAG,EAAE,CAAC,CAAC,sDAAsD;QAE9F,8BAA8B;QAC9B,MAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC;YAC9C,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE;gBACtC,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;gBACpE,OAAO,GAAG,GAAG,eAAe,CAAC;YAC/B,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM;YAChC,CAAC,CAAC,CAAC,CAAC;QACN,MAAM,CAAC,OAAO,CAAC,gBAAgB,GAAG,aAAa,CAAC;QAEhD,sCAAsC;QACtC,MAAM,CAAC,OAAO,CAAC,uBAAuB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,aAAa,CAAC,CAAC;IAC3E,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,GAAQ,EAAE,KAAU;QAC5C,iCAAiC;QACjC,IAAI,GAAG,CAAC,UAAU,KAAK,uCAAe,CAAC,SAAS,EAAE,CAAC;YACjD,OAAO,qCAAc,CAAC,IAAI,CAAC;QAC7B,CAAC;QACD,IAAI,GAAG,CAAC,UAAU,KAAK,uCAAe,CAAC,IAAI,EAAE,CAAC;YAC5C,OAAO,qCAAc,CAAC,MAAM,CAAC;QAC/B,CAAC;QACD,OAAO,qCAAc,CAAC,GAAG,CAAC;IAC5B,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,GAAQ,EAAE,KAAU;QAChD,OAAO;YACL,6BAA6B;YAC7B,4BAA4B;YAC5B,wBAAwB;YACxB,wBAAwB;SACzB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,mCAAmC,CAAC,KAAyB;QACnE,OAAO;YACL;gBACE,EAAE,EAAE,cAAc,IAAI,CAAC,GAAG,EAAE,EAAE;gBAC9B,IAAI,EAAE,oBAAoB;gBAC1B,UAAU,EAAE,uCAAe,CAAC,MAAM;gBAClC,QAAQ,EAAE,qCAAc,CAAC,MAAM;gBAC/B,WAAW,EAAE,gCAAgC;gBAC7C,QAAQ,EAAE,EAAE;gBACZ,cAAc,EAAE,CAAC,SAAS,CAAC;gBAC3B,aAAa,EAAE,EAAE;gBACjB,kBAAkB,EAAE,EAAE;gBACtB,kBAAkB,EAAE,CAAC,2BAA2B,CAAC;aAClD;SACF,CAAC;IACJ,CAAC;IAEO,gCAAgC,CAAC,KAAyB;QAChE,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,gCAAgC,CAAC,KAAyB;QAChE,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,iCAAiC,CAAC,KAAyB;QACjE,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACK,wBAAwB,CAAC,QAAe;QAC9C,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,qCAAc,CAAC,QAAQ,CAAC;YAAE,OAAO,UAAU,CAAC;QAClF,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,qCAAc,CAAC,IAAI,CAAC;YAAE,OAAO,MAAM,CAAC;QAC1E,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,qCAAc,CAAC,MAAM,CAAC;YAAE,OAAO,QAAQ,CAAC;QAC9E,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,QAAe;QACzC,OAAO,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAChC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAChC,SAAS,EAAE,QAAQ,CAAC,SAAS;YAC7B,KAAK,EAAE,OAAO,CAAC,WAAW;YAC1B,SAAS,EAAE,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,aAAa,IAAI,SAAS;YACpE,KAAK,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,SAAS;YAC7C,UAAU,EAAE,OAAO,CAAC,UAAU;SAC/B,CAAC,CAAC,CACJ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,QAAe;QACtC,OAAO;YACL,MAAM,EAAE,EAAE;YACV,WAAW,EAAE,CAAC;SACf,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,MAA2B;QACzD,MAAM,eAAe,GAAG;YACtB,SAAS,EAAE,EAAE;YACb,SAAS,EAAE,EAAE;YACb,QAAQ,EAAE,EAAE;YACZ,UAAU,EAAE,EAAE;SACf,CAAC;QAEF,IAAI,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,KAAK,UAAU,EAAE,CAAC;YACvE,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QACpE,CAAC;QAED,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QACpE,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QACrE,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAEnE,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,UAA2B;QACpD,MAAM,MAAM,GAAG;YACb,CAAC,uCAAe,CAAC,OAAO,CAAC,EAAE,CAAC;YAC5B,CAAC,uCAAe,CAAC,GAAG,CAAC,EAAE,EAAE;YACzB,CAAC,uCAAe,CAAC,MAAM,CAAC,EAAE,EAAE;YAC5B,CAAC,uCAAe,CAAC,IAAI,CAAC,EAAE,EAAE;YAC1B,CAAC,uCAAe,CAAC,SAAS,CAAC,EAAE,EAAE;YAC/B,CAAC,uCAAe,CAAC,SAAS,CAAC,EAAE,GAAG;SACjC,CAAC;QACF,OAAO,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB;QACxB,OAAO;YACL;gBACE,IAAI,EAAE,gCAAgC;gBACtC,WAAW,EAAE,mDAAmD;gBAChE,IAAI,EAAE,kBAAkB;gBACxB,UAAU,EAAE;oBACV,SAAS,EAAE;wBACT,IAAI,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;wBACpD,EAAE,EAAE,IAAI,IAAI,EAAE;qBACf;oBACD,QAAQ,EAAE;wBACR,WAAW,EAAE,gBAAgB;wBAC7B,mBAAmB,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC;qBACrD;iBACF;gBACD,QAAQ,EAAE,MAAM;gBAChB,eAAe,EAAE;oBACf,gBAAgB,EAAE,EAAE;oBACpB,mBAAmB,EAAE,uCAAe,CAAC,MAAM;oBAC3C,iBAAiB,EAAE,GAAG;iBACvB;aACF;YACD;gBACE,IAAI,EAAE,4BAA4B;gBAClC,WAAW,EAAE,8CAA8C;gBAC3D,IAAI,EAAE,qBAAqB;gBAC3B,UAAU,EAAE;oBACV,SAAS,EAAE;wBACT,IAAI,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;wBAChD,EAAE,EAAE,IAAI,IAAI,EAAE;qBACf;oBACD,QAAQ,EAAE;wBACR,aAAa,EAAE,CAAC,gBAAgB,EAAE,eAAe,CAAC;wBAClD,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,cAAc,CAAC;qBAC1D;iBACF;gBACD,QAAQ,EAAE,UAAU;gBACpB,eAAe,EAAE;oBACf,gBAAgB,EAAE,EAAE;oBACpB,mBAAmB,EAAE,uCAAe,CAAC,IAAI;oBACzC,iBAAiB,EAAE,GAAG;iBACvB;aACF;SACF,CAAC;IACJ,CAAC;CACF,CAAA;AA7bY,oDAAoB;+BAApB,oBAAoB;IADhC,IAAA,mBAAU,GAAE;yDAGiC,6DAAuB,oBAAvB,6DAAuB,oDACvB,4DAAuB,oBAAvB,4DAAuB,oDAC5B,kDAAkB,oBAAlB,kDAAkB,oDACf,wDAAqB,oBAArB,wDAAqB,oDAC5B,6CAAoB,oBAApB,6CAAoB;GAN5C,oBAAoB,CA6bhC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\application\\services\\threat-hunting.service.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { SecurityEventRepository } from '../../domain/interfaces/repositories/security-event.repository.interface';\r\nimport { VulnerabilityRepository } from '../../domain/interfaces/repositories/vulnerability.repository.interface';\r\nimport { IncidentRepository } from '../../domain/interfaces/repositories/incident.repository.interface';\r\nimport { CorrelationRepository } from '../../domain/interfaces/repositories/correlation.repository.interface';\r\nimport { ThreatSeverity } from '../../domain/enums/threat-severity.enum';\r\nimport { ConfidenceLevel } from '../../domain/enums/confidence-level.enum';\r\nimport { DomainEventPublisher } from '../../../shared-kernel/domain/domain-event-publisher';\r\n\r\n/**\r\n * Threat Hunting Query\r\n */\r\nexport interface ThreatHuntingQuery {\r\n  /** Query name */\r\n  name: string;\r\n  /** Query description */\r\n  description: string;\r\n  /** Query type */\r\n  type: 'ioc_search' | 'behavioral_analysis' | 'anomaly_detection' | 'pattern_matching' | 'timeline_analysis';\r\n  /** Search parameters */\r\n  parameters: {\r\n    /** Time range */\r\n    timeRange: {\r\n      from: Date;\r\n      to: Date;\r\n    };\r\n    /** Search criteria */\r\n    criteria: Record<string, any>;\r\n    /** IOCs to hunt for */\r\n    iocs?: Array<{\r\n      type: string;\r\n      value: string;\r\n      confidence: ConfidenceLevel;\r\n    }>;\r\n    /** Behavioral patterns */\r\n    patterns?: Array<{\r\n      pattern: string;\r\n      threshold: number;\r\n      timeWindow: number;\r\n    }>;\r\n    /** MITRE ATT&CK techniques */\r\n    mitreAttackTechniques?: string[];\r\n    /** Asset scope */\r\n    assetScope?: string[];\r\n  };\r\n  /** Query priority */\r\n  priority: 'low' | 'medium' | 'high' | 'critical';\r\n  /** Expected results */\r\n  expectedResults: {\r\n    estimatedMatches: number;\r\n    confidenceThreshold: ConfidenceLevel;\r\n    falsePositiveRate: number;\r\n  };\r\n}\r\n\r\n/**\r\n * Threat Hunting Result\r\n */\r\nexport interface ThreatHuntingResult {\r\n  /** Query information */\r\n  query: ThreatHuntingQuery;\r\n  /** Execution metadata */\r\n  execution: {\r\n    executionId: string;\r\n    startTime: Date;\r\n    endTime: Date;\r\n    duration: number;\r\n    status: 'running' | 'completed' | 'failed' | 'cancelled';\r\n    progress: number;\r\n  };\r\n  /** Search results */\r\n  results: {\r\n    /** Total matches found */\r\n    totalMatches: number;\r\n    /** High confidence matches */\r\n    highConfidenceMatches: number;\r\n    /** Unique assets affected */\r\n    uniqueAssetsAffected: number;\r\n    /** Time range covered */\r\n    timeRangeCovered: {\r\n      from: Date;\r\n      to: Date;\r\n    };\r\n    /** Matches by category */\r\n    matchesByCategory: Record<string, number>;\r\n    /** Confidence distribution */\r\n    confidenceDistribution: Record<ConfidenceLevel, number>;\r\n  };\r\n  /** Detailed findings */\r\n  findings: Array<{\r\n    /** Finding ID */\r\n    id: string;\r\n    /** Finding type */\r\n    type: 'ioc_match' | 'behavioral_anomaly' | 'pattern_match' | 'correlation_hit';\r\n    /** Confidence level */\r\n    confidence: ConfidenceLevel;\r\n    /** Severity assessment */\r\n    severity: ThreatSeverity;\r\n    /** Description */\r\n    description: string;\r\n    /** Evidence */\r\n    evidence: Array<{\r\n      type: 'event' | 'log' | 'network_flow' | 'file' | 'registry';\r\n      source: string;\r\n      timestamp: Date;\r\n      data: Record<string, any>;\r\n    }>;\r\n    /** Affected assets */\r\n    affectedAssets: string[];\r\n    /** Related events */\r\n    relatedEvents: string[];\r\n    /** MITRE ATT&CK mapping */\r\n    mitreAttackMapping: Array<{\r\n      techniqueId: string;\r\n      techniqueName: string;\r\n      tactic: string;\r\n    }>;\r\n    /** Recommended actions */\r\n    recommendedActions: string[];\r\n  }>;\r\n  /** Analysis summary */\r\n  analysis: {\r\n    /** Threat assessment */\r\n    threatAssessment: {\r\n      overallThreatLevel: 'low' | 'medium' | 'high' | 'critical';\r\n      threatActorAttribution: Array<{\r\n        actor: string;\r\n        confidence: ConfidenceLevel;\r\n        evidence: string[];\r\n      }>;\r\n      campaignAttribution: Array<{\r\n        campaign: string;\r\n        confidence: ConfidenceLevel;\r\n        evidence: string[];\r\n      }>;\r\n    };\r\n    /** Attack timeline */\r\n    attackTimeline: Array<{\r\n      timestamp: Date;\r\n      event: string;\r\n      technique: string;\r\n      asset: string;\r\n      confidence: ConfidenceLevel;\r\n    }>;\r\n    /** Kill chain analysis */\r\n    killChainAnalysis: {\r\n      phases: Array<{\r\n        phase: string;\r\n        techniques: string[];\r\n        evidence: string[];\r\n        completeness: number;\r\n      }>;\r\n      progression: number;\r\n    };\r\n    /** Recommendations */\r\n    recommendations: {\r\n      immediate: string[];\r\n      shortTerm: string[];\r\n      longTerm: string[];\r\n      preventive: string[];\r\n    };\r\n  };\r\n  /** Quality metrics */\r\n  quality: {\r\n    /** Data coverage */\r\n    dataCoverage: number;\r\n    /** Search completeness */\r\n    searchCompleteness: number;\r\n    /** Result confidence */\r\n    resultConfidence: number;\r\n    /** False positive likelihood */\r\n    falsePositiveLikelihood: number;\r\n  };\r\n}\r\n\r\n/**\r\n * Threat Hunting Service\r\n * \r\n * Provides advanced threat hunting capabilities for proactive threat detection\r\n * and investigation. Supports multiple hunting methodologies and techniques.\r\n * \r\n * Key capabilities:\r\n * - IOC-based hunting\r\n * - Behavioral analysis\r\n * - Anomaly detection\r\n * - Pattern matching\r\n * - Timeline analysis\r\n * - MITRE ATT&CK mapping\r\n * - Threat actor attribution\r\n */\r\n@Injectable()\r\nexport class ThreatHuntingService {\r\n  constructor(\r\n    private readonly securityEventRepository: SecurityEventRepository,\r\n    private readonly vulnerabilityRepository: VulnerabilityRepository,\r\n    private readonly incidentRepository: IncidentRepository,\r\n    private readonly correlationRepository: CorrelationRepository,\r\n    private readonly eventPublisher: DomainEventPublisher\r\n  ) {}\r\n\r\n  /**\r\n   * Execute threat hunting query\r\n   */\r\n  async executeHunt(query: ThreatHuntingQuery): Promise<ThreatHuntingResult> {\r\n    const executionId = `hunt-${Date.now()}`;\r\n    const startTime = new Date();\r\n\r\n    try {\r\n      // Initialize result structure\r\n      const result: ThreatHuntingResult = {\r\n        query,\r\n        execution: {\r\n          executionId,\r\n          startTime,\r\n          endTime: new Date(),\r\n          duration: 0,\r\n          status: 'running',\r\n          progress: 0,\r\n        },\r\n        results: {\r\n          totalMatches: 0,\r\n          highConfidenceMatches: 0,\r\n          uniqueAssetsAffected: 0,\r\n          timeRangeCovered: query.parameters.timeRange,\r\n          matchesByCategory: {},\r\n          confidenceDistribution: {},\r\n        },\r\n        findings: [],\r\n        analysis: {\r\n          threatAssessment: {\r\n            overallThreatLevel: 'low',\r\n            threatActorAttribution: [],\r\n            campaignAttribution: [],\r\n          },\r\n          attackTimeline: [],\r\n          killChainAnalysis: {\r\n            phases: [],\r\n            progression: 0,\r\n          },\r\n          recommendations: {\r\n            immediate: [],\r\n            shortTerm: [],\r\n            longTerm: [],\r\n            preventive: [],\r\n          },\r\n        },\r\n        quality: {\r\n          dataCoverage: 0,\r\n          searchCompleteness: 0,\r\n          resultConfidence: 0,\r\n          falsePositiveLikelihood: 0,\r\n        },\r\n      };\r\n\r\n      // Execute hunt based on query type\r\n      switch (query.type) {\r\n        case 'ioc_search':\r\n          await this.executeIOCHunt(query, result);\r\n          break;\r\n        case 'behavioral_analysis':\r\n          await this.executeBehavioralHunt(query, result);\r\n          break;\r\n        case 'anomaly_detection':\r\n          await this.executeAnomalyHunt(query, result);\r\n          break;\r\n        case 'pattern_matching':\r\n          await this.executePatternHunt(query, result);\r\n          break;\r\n        case 'timeline_analysis':\r\n          await this.executeTimelineHunt(query, result);\r\n          break;\r\n        default:\r\n          throw new Error(`Unsupported hunt type: ${query.type}`);\r\n      }\r\n\r\n      // Perform analysis\r\n      await this.performThreatAnalysis(result);\r\n\r\n      // Calculate quality metrics\r\n      this.calculateQualityMetrics(result);\r\n\r\n      // Finalize execution\r\n      const endTime = new Date();\r\n      result.execution.endTime = endTime;\r\n      result.execution.duration = endTime.getTime() - startTime.getTime();\r\n      result.execution.status = 'completed';\r\n      result.execution.progress = 100;\r\n\r\n      return result;\r\n    } catch (error) {\r\n      const endTime = new Date();\r\n      return {\r\n        ...result,\r\n        execution: {\r\n          executionId,\r\n          startTime,\r\n          endTime,\r\n          duration: endTime.getTime() - startTime.getTime(),\r\n          status: 'failed',\r\n          progress: 0,\r\n        },\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Execute IOC-based hunt\r\n   */\r\n  private async executeIOCHunt(query: ThreatHuntingQuery, result: ThreatHuntingResult): Promise<void> {\r\n    if (!query.parameters.iocs || query.parameters.iocs.length === 0) {\r\n      return;\r\n    }\r\n\r\n    for (const ioc of query.parameters.iocs) {\r\n      // Search for IOC in security events\r\n      const events = await this.securityEventRepository.findByIOC(ioc.value, ioc.type);\r\n      \r\n      for (const event of events) {\r\n        const finding = {\r\n          id: `ioc-${Date.now()}-${Math.random()}`,\r\n          type: 'ioc_match' as const,\r\n          confidence: ioc.confidence,\r\n          severity: this.assessIOCSeverity(ioc, event),\r\n          description: `IOC match found: ${ioc.type} = ${ioc.value}`,\r\n          evidence: [{\r\n            type: 'event' as const,\r\n            source: event.source.name,\r\n            timestamp: event.timestamp.value,\r\n            data: {\r\n              eventId: event.id.toString(),\r\n              iocType: ioc.type,\r\n              iocValue: ioc.value,\r\n              eventData: event.rawData,\r\n            },\r\n          }],\r\n          affectedAssets: event.affectedAssets?.map(asset => asset.assetId) || [],\r\n          relatedEvents: [event.id.toString()],\r\n          mitreAttackMapping: [],\r\n          recommendedActions: this.getIOCRecommendations(ioc, event),\r\n        };\r\n\r\n        result.findings.push(finding);\r\n        result.results.totalMatches++;\r\n\r\n        if (ioc.confidence === ConfidenceLevel.HIGH || ioc.confidence === ConfidenceLevel.VERY_HIGH) {\r\n          result.results.highConfidenceMatches++;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Update progress\r\n    result.execution.progress = 30;\r\n  }\r\n\r\n  /**\r\n   * Execute behavioral analysis hunt\r\n   */\r\n  private async executeBehavioralHunt(query: ThreatHuntingQuery, result: ThreatHuntingResult): Promise<void> {\r\n    if (!query.parameters.patterns || query.parameters.patterns.length === 0) {\r\n      return;\r\n    }\r\n\r\n    // This would implement behavioral analysis logic\r\n    // For now, we'll simulate some results\r\n    const simulatedFindings = this.generateSimulatedBehavioralFindings(query);\r\n    result.findings.push(...simulatedFindings);\r\n    result.results.totalMatches += simulatedFindings.length;\r\n    result.execution.progress = 60;\r\n  }\r\n\r\n  /**\r\n   * Execute anomaly detection hunt\r\n   */\r\n  private async executeAnomalyHunt(query: ThreatHuntingQuery, result: ThreatHuntingResult): Promise<void> {\r\n    // This would implement anomaly detection logic\r\n    // For now, we'll simulate some results\r\n    const simulatedFindings = this.generateSimulatedAnomalyFindings(query);\r\n    result.findings.push(...simulatedFindings);\r\n    result.results.totalMatches += simulatedFindings.length;\r\n    result.execution.progress = 80;\r\n  }\r\n\r\n  /**\r\n   * Execute pattern matching hunt\r\n   */\r\n  private async executePatternHunt(query: ThreatHuntingQuery, result: ThreatHuntingResult): Promise<void> {\r\n    // This would implement pattern matching logic\r\n    const simulatedFindings = this.generateSimulatedPatternFindings(query);\r\n    result.findings.push(...simulatedFindings);\r\n    result.results.totalMatches += simulatedFindings.length;\r\n    result.execution.progress = 90;\r\n  }\r\n\r\n  /**\r\n   * Execute timeline analysis hunt\r\n   */\r\n  private async executeTimelineHunt(query: ThreatHuntingQuery, result: ThreatHuntingResult): Promise<void> {\r\n    // This would implement timeline analysis logic\r\n    const simulatedFindings = this.generateSimulatedTimelineFindings(query);\r\n    result.findings.push(...simulatedFindings);\r\n    result.results.totalMatches += simulatedFindings.length;\r\n    result.execution.progress = 95;\r\n  }\r\n\r\n  /**\r\n   * Perform threat analysis on findings\r\n   */\r\n  private async performThreatAnalysis(result: ThreatHuntingResult): Promise<void> {\r\n    // Assess overall threat level\r\n    result.analysis.threatAssessment.overallThreatLevel = this.assessOverallThreatLevel(result.findings);\r\n\r\n    // Build attack timeline\r\n    result.analysis.attackTimeline = this.buildAttackTimeline(result.findings);\r\n\r\n    // Analyze kill chain progression\r\n    result.analysis.killChainAnalysis = this.analyzeKillChain(result.findings);\r\n\r\n    // Generate recommendations\r\n    result.analysis.recommendations = this.generateRecommendations(result);\r\n\r\n    // Calculate unique assets affected\r\n    const uniqueAssets = new Set<string>();\r\n    result.findings.forEach(finding => {\r\n      finding.affectedAssets.forEach(asset => uniqueAssets.add(asset));\r\n    });\r\n    result.results.uniqueAssetsAffected = uniqueAssets.size;\r\n\r\n    // Calculate confidence distribution\r\n    result.findings.forEach(finding => {\r\n      result.results.confidenceDistribution[finding.confidence] = \r\n        (result.results.confidenceDistribution[finding.confidence] || 0) + 1;\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Calculate quality metrics\r\n   */\r\n  private calculateQualityMetrics(result: ThreatHuntingResult): void {\r\n    // Calculate data coverage (simplified)\r\n    result.quality.dataCoverage = Math.min(100, result.results.totalMatches * 10);\r\n\r\n    // Calculate search completeness\r\n    result.quality.searchCompleteness = 95; // Would be calculated based on actual search coverage\r\n\r\n    // Calculate result confidence\r\n    const avgConfidence = result.findings.length > 0 \r\n      ? result.findings.reduce((sum, finding) => {\r\n          const confidenceScore = this.getConfidenceScore(finding.confidence);\r\n          return sum + confidenceScore;\r\n        }, 0) / result.findings.length\r\n      : 0;\r\n    result.quality.resultConfidence = avgConfidence;\r\n\r\n    // Calculate false positive likelihood\r\n    result.quality.falsePositiveLikelihood = Math.max(0, 30 - avgConfidence);\r\n  }\r\n\r\n  /**\r\n   * Assess IOC severity\r\n   */\r\n  private assessIOCSeverity(ioc: any, event: any): ThreatSeverity {\r\n    // Simplified severity assessment\r\n    if (ioc.confidence === ConfidenceLevel.CONFIRMED) {\r\n      return ThreatSeverity.HIGH;\r\n    }\r\n    if (ioc.confidence === ConfidenceLevel.HIGH) {\r\n      return ThreatSeverity.MEDIUM;\r\n    }\r\n    return ThreatSeverity.LOW;\r\n  }\r\n\r\n  /**\r\n   * Get IOC recommendations\r\n   */\r\n  private getIOCRecommendations(ioc: any, event: any): string[] {\r\n    return [\r\n      'Investigate affected assets',\r\n      'Check for lateral movement',\r\n      'Review network traffic',\r\n      'Update detection rules',\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Generate simulated findings for demonstration\r\n   */\r\n  private generateSimulatedBehavioralFindings(query: ThreatHuntingQuery): any[] {\r\n    return [\r\n      {\r\n        id: `behavioral-${Date.now()}`,\r\n        type: 'behavioral_anomaly',\r\n        confidence: ConfidenceLevel.MEDIUM,\r\n        severity: ThreatSeverity.MEDIUM,\r\n        description: 'Unusual login pattern detected',\r\n        evidence: [],\r\n        affectedAssets: ['asset-1'],\r\n        relatedEvents: [],\r\n        mitreAttackMapping: [],\r\n        recommendedActions: ['Investigate user behavior'],\r\n      },\r\n    ];\r\n  }\r\n\r\n  private generateSimulatedAnomalyFindings(query: ThreatHuntingQuery): any[] {\r\n    return [];\r\n  }\r\n\r\n  private generateSimulatedPatternFindings(query: ThreatHuntingQuery): any[] {\r\n    return [];\r\n  }\r\n\r\n  private generateSimulatedTimelineFindings(query: ThreatHuntingQuery): any[] {\r\n    return [];\r\n  }\r\n\r\n  /**\r\n   * Assess overall threat level\r\n   */\r\n  private assessOverallThreatLevel(findings: any[]): 'low' | 'medium' | 'high' | 'critical' {\r\n    if (findings.some(f => f.severity === ThreatSeverity.CRITICAL)) return 'critical';\r\n    if (findings.some(f => f.severity === ThreatSeverity.HIGH)) return 'high';\r\n    if (findings.some(f => f.severity === ThreatSeverity.MEDIUM)) return 'medium';\r\n    return 'low';\r\n  }\r\n\r\n  /**\r\n   * Build attack timeline\r\n   */\r\n  private buildAttackTimeline(findings: any[]): any[] {\r\n    return findings.flatMap(finding => \r\n      finding.evidence.map(evidence => ({\r\n        timestamp: evidence.timestamp,\r\n        event: finding.description,\r\n        technique: finding.mitreAttackMapping[0]?.techniqueName || 'Unknown',\r\n        asset: finding.affectedAssets[0] || 'Unknown',\r\n        confidence: finding.confidence,\r\n      }))\r\n    ).sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\r\n  }\r\n\r\n  /**\r\n   * Analyze kill chain progression\r\n   */\r\n  private analyzeKillChain(findings: any[]): any {\r\n    return {\r\n      phases: [],\r\n      progression: 0,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate recommendations\r\n   */\r\n  private generateRecommendations(result: ThreatHuntingResult): any {\r\n    const recommendations = {\r\n      immediate: [],\r\n      shortTerm: [],\r\n      longTerm: [],\r\n      preventive: [],\r\n    };\r\n\r\n    if (result.analysis.threatAssessment.overallThreatLevel === 'critical') {\r\n      recommendations.immediate.push('Activate incident response team');\r\n    }\r\n\r\n    recommendations.shortTerm.push('Review and update detection rules');\r\n    recommendations.longTerm.push('Enhance threat hunting capabilities');\r\n    recommendations.preventive.push('Implement additional monitoring');\r\n\r\n    return recommendations;\r\n  }\r\n\r\n  /**\r\n   * Get confidence score\r\n   */\r\n  private getConfidenceScore(confidence: ConfidenceLevel): number {\r\n    const scores = {\r\n      [ConfidenceLevel.UNKNOWN]: 0,\r\n      [ConfidenceLevel.LOW]: 20,\r\n      [ConfidenceLevel.MEDIUM]: 50,\r\n      [ConfidenceLevel.HIGH]: 80,\r\n      [ConfidenceLevel.VERY_HIGH]: 95,\r\n      [ConfidenceLevel.CONFIRMED]: 100,\r\n    };\r\n    return scores[confidence] || 0;\r\n  }\r\n\r\n  /**\r\n   * Get predefined hunting queries\r\n   */\r\n  async getPredefinedQueries(): Promise<ThreatHuntingQuery[]> {\r\n    return [\r\n      {\r\n        name: 'Suspicious PowerShell Activity',\r\n        description: 'Hunt for suspicious PowerShell execution patterns',\r\n        type: 'pattern_matching',\r\n        parameters: {\r\n          timeRange: {\r\n            from: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),\r\n            to: new Date(),\r\n          },\r\n          criteria: {\r\n            processName: 'powershell.exe',\r\n            commandLinePatterns: ['encoded', 'bypass', 'hidden'],\r\n          },\r\n        },\r\n        priority: 'high',\r\n        expectedResults: {\r\n          estimatedMatches: 50,\r\n          confidenceThreshold: ConfidenceLevel.MEDIUM,\r\n          falsePositiveRate: 0.2,\r\n        },\r\n      },\r\n      {\r\n        name: 'Lateral Movement Detection',\r\n        description: 'Detect potential lateral movement activities',\r\n        type: 'behavioral_analysis',\r\n        parameters: {\r\n          timeRange: {\r\n            from: new Date(Date.now() - 24 * 60 * 60 * 1000),\r\n            to: new Date(),\r\n          },\r\n          criteria: {\r\n            loginPatterns: ['multiple_hosts', 'unusual_times'],\r\n            networkConnections: ['internal_scanning', 'admin_shares'],\r\n          },\r\n        },\r\n        priority: 'critical',\r\n        expectedResults: {\r\n          estimatedMatches: 10,\r\n          confidenceThreshold: ConfidenceLevel.HIGH,\r\n          falsePositiveRate: 0.1,\r\n        },\r\n      },\r\n    ];\r\n  }\r\n}\r\n"], "version": 3}