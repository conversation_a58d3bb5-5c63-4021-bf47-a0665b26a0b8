{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\domain\\entities\\threat-actor.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCAQiB;AACjB,6CAAmC;AAEnC;;;GAGG;AAQI,IAAM,WAAW,GAAjB,MAAM,WAAW;IAgQtB;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACxD,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,IAAI,iBAAiB;QACnB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;QACvD,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,MAAM,qBAAqB,GAAG;YAC5B,OAAO,EAAE,CAAC;YACV,YAAY,EAAE,CAAC;YACf,QAAQ,EAAE,CAAC;YACX,MAAM,EAAE,CAAC;YACT,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE,CAAC;SACb,CAAC;QAEF,MAAM,kBAAkB,GAAG;YACzB,GAAG,EAAE,CAAC;YACN,MAAM,EAAE,CAAC;YACT,IAAI,EAAE,CAAC;YACP,QAAQ,EAAE,CAAC;SACZ,CAAC;QAEF,MAAM,oBAAoB,GAAG,qBAAqB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACxE,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC/D,MAAM,gBAAgB,GAAG,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;QAC/C,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAEjD,MAAM,SAAS,GAAG,CAAC,oBAAoB,GAAG,iBAAiB,CAAC,GAAG,gBAAgB,GAAG,cAAc,CAAC;QACjG,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,MAAc;QACzB,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,OAAe;QAC5B,OAAO,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IACnF,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,SAAiB;QAC7B,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IACjF,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,aAAqB;QAC/B,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IACrF,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,IAAI,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,QAMX;QACC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACtB,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,SAAiB;QACjC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC1B,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC5B,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAC9C,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,MAAc;QAC9B,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC1B,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC5B,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3C,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,aAAqB;QACpC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC1B,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC5B,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;YAClD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;CACF,CAAA;AA5YY,kCAAW;AAEtB;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;uCACpB;AAMX;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;yCACzB;AAMb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4CACvB;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;gDACpB;AAmBrB;IAdC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE;YACJ,KAAK;YACL,eAAe;YACf,YAAY;YACZ,cAAc;YACd,gBAAgB;YAChB,eAAe;YACf,WAAW;YACX,SAAS;SACV;QACD,OAAO,EAAE,SAAS;KACnB,CAAC;;yCACW;AAUb;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,SAAS,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE,WAAW,CAAC;QACjF,OAAO,EAAE,cAAc;KACxB,CAAC;;mDAC6F;AAU/F;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,CAAC;QAC/D,OAAO,EAAE,QAAQ;KAClB,CAAC;;2CACkE;AAMpE;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;gDACnB;AAUvB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,cAAc,EAAE,YAAY,CAAC;QAC7E,QAAQ,EAAE,IAAI;KACf,CAAC;;kDACqB;AAMvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;0CACzB;AAMjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;iDAClB;AAMxB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;oDACzC;AAM3B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sDACzC;AAM7B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sDAC5B;AAM3B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;oDACzC;AAM3B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;iDACzC;AAMxB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;oDACzC;AAM3B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;0CACzB;AAMjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDAYjE;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;8CAOtC;AAMJ;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC;kDACtD,IAAI,oBAAJ,IAAI;8CAAC;AAMhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC;kDACtD,IAAI,oBAAJ,IAAI;6CAAC;AAMf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;+CACtB;AAWnB;IANC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,cAAc;QACpB,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC;QAC3C,OAAO,EAAE,QAAQ;KAClB,CAAC;;gDACkD;AAMpD;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;+CAMtC;AAMJ;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4CACvB;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;yCAC1B;AAUhB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC;QACxC,OAAO,EAAE,OAAO;KACjB,CAAC;;wCACuC;AAMzC;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;0CAC1B;AAMf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAC/B,MAAM,oBAAN,MAAM;6CAAc;AAM/B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;8CAC1C;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;8CAC1C;AAGnB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;8CAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;8CAAC;AAIhB;IADC,IAAA,oBAAU,EAAC,GAAG,EAAE,CAAC,gBAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC;;yCACnC;sBA9PD,WAAW;IAPvB,IAAA,gBAAM,EAAC,eAAe,CAAC;IACvB,IAAA,eAAK,EAAC,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;IACjC,IAAA,eAAK,EAAC,CAAC,MAAM,CAAC,CAAC;IACf,IAAA,eAAK,EAAC,CAAC,gBAAgB,CAAC,CAAC;IACzB,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,WAAW,CAAC,CAAC;IACpB,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;GACP,WAAW,CA4YvB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\domain\\entities\\threat-actor.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  ManyToMany,\r\n} from 'typeorm';\r\nimport { IOC } from './ioc.entity';\r\n\r\n/**\r\n * Threat Actor entity\r\n * Represents known threat actors, APT groups, and malicious entities\r\n */\r\n@Entity('threat_actors')\r\n@Index(['name'], { unique: true })\r\n@Index(['type'])\r\n@Index(['sophistication'])\r\n@Index(['status'])\r\n@Index(['firstSeen'])\r\n@Index(['lastSeen'])\r\nexport class ThreatActor {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * Threat actor name/identifier\r\n   */\r\n  @Column({ unique: true, length: 255 })\r\n  name: string;\r\n\r\n  /**\r\n   * Alternative names and aliases\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  aliases?: string[];\r\n\r\n  /**\r\n   * Threat actor description\r\n   */\r\n  @Column({ type: 'text', nullable: true })\r\n  description?: string;\r\n\r\n  /**\r\n   * Threat actor type\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: [\r\n      'apt',\r\n      'cybercriminal',\r\n      'hacktivist',\r\n      'nation_state',\r\n      'insider_threat',\r\n      'script_kiddie',\r\n      'terrorist',\r\n      'unknown',\r\n    ],\r\n    default: 'unknown',\r\n  })\r\n  type: string;\r\n\r\n  /**\r\n   * Sophistication level\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['minimal', 'intermediate', 'advanced', 'expert', 'innovator', 'strategic'],\r\n    default: 'intermediate',\r\n  })\r\n  sophistication: 'minimal' | 'intermediate' | 'advanced' | 'expert' | 'innovator' | 'strategic';\r\n\r\n  /**\r\n   * Threat actor status\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['active', 'inactive', 'dormant', 'disbanded', 'unknown'],\r\n    default: 'active',\r\n  })\r\n  status: 'active' | 'inactive' | 'dormant' | 'disbanded' | 'unknown';\r\n\r\n  /**\r\n   * Primary motivations\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  motivations?: string[];\r\n\r\n  /**\r\n   * Resource level\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['individual', 'club', 'contest', 'team', 'organization', 'government'],\r\n    nullable: true,\r\n  })\r\n  resourceLevel?: string;\r\n\r\n  /**\r\n   * Primary goals\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  goals?: string[];\r\n\r\n  /**\r\n   * Known capabilities\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  capabilities?: string[];\r\n\r\n  /**\r\n   * Targeted sectors\r\n   */\r\n  @Column({ name: 'targeted_sectors', type: 'jsonb', nullable: true })\r\n  targetedSectors?: string[];\r\n\r\n  /**\r\n   * Targeted countries/regions\r\n   */\r\n  @Column({ name: 'targeted_countries', type: 'jsonb', nullable: true })\r\n  targetedCountries?: string[];\r\n\r\n  /**\r\n   * Attributed country/origin\r\n   */\r\n  @Column({ name: 'attributed_country', nullable: true })\r\n  attributedCountry?: string;\r\n\r\n  /**\r\n   * MITRE ATT&CK techniques used\r\n   */\r\n  @Column({ name: 'mitre_techniques', type: 'jsonb', nullable: true })\r\n  mitreTechniques?: string[];\r\n\r\n  /**\r\n   * MITRE ATT&CK tactics used\r\n   */\r\n  @Column({ name: 'mitre_tactics', type: 'jsonb', nullable: true })\r\n  mitreTactics?: string[];\r\n\r\n  /**\r\n   * Known malware families used\r\n   */\r\n  @Column({ name: 'malware_families', type: 'jsonb', nullable: true })\r\n  malwareFamilies?: string[];\r\n\r\n  /**\r\n   * Known tools used\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  tools?: string[];\r\n\r\n  /**\r\n   * Attack patterns and TTPs\r\n   */\r\n  @Column({ name: 'attack_patterns', type: 'jsonb', nullable: true })\r\n  attackPatterns?: {\r\n    initialAccess?: string[];\r\n    persistence?: string[];\r\n    privilegeEscalation?: string[];\r\n    defenseEvasion?: string[];\r\n    credentialAccess?: string[];\r\n    discovery?: string[];\r\n    lateralMovement?: string[];\r\n    collection?: string[];\r\n    exfiltration?: string[];\r\n    impact?: string[];\r\n  };\r\n\r\n  /**\r\n   * Known campaigns\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  campaigns?: {\r\n    name: string;\r\n    startDate?: string;\r\n    endDate?: string;\r\n    description?: string;\r\n    targets?: string[];\r\n  }[];\r\n\r\n  /**\r\n   * First time this actor was observed\r\n   */\r\n  @Column({ name: 'first_seen', type: 'timestamp with time zone' })\r\n  firstSeen: Date;\r\n\r\n  /**\r\n   * Last time this actor was observed\r\n   */\r\n  @Column({ name: 'last_seen', type: 'timestamp with time zone' })\r\n  lastSeen: Date;\r\n\r\n  /**\r\n   * Confidence level in attribution (0-100)\r\n   */\r\n  @Column({ type: 'integer', default: 50 })\r\n  confidence: number;\r\n\r\n  /**\r\n   * Threat level assessment\r\n   */\r\n  @Column({\r\n    name: 'threat_level',\r\n    type: 'enum',\r\n    enum: ['low', 'medium', 'high', 'critical'],\r\n    default: 'medium',\r\n  })\r\n  threatLevel: 'low' | 'medium' | 'high' | 'critical';\r\n\r\n  /**\r\n   * External references and sources\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  references?: {\r\n    url: string;\r\n    source: string;\r\n    title?: string;\r\n    date?: string;\r\n  }[];\r\n\r\n  /**\r\n   * Intelligence sources\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  sources?: string[];\r\n\r\n  /**\r\n   * Tags for categorization\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  tags?: string[];\r\n\r\n  /**\r\n   * TLP (Traffic Light Protocol) classification\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['white', 'green', 'amber', 'red'],\r\n    default: 'white',\r\n  })\r\n  tlp: 'white' | 'green' | 'amber' | 'red';\r\n\r\n  /**\r\n   * Additional notes\r\n   */\r\n  @Column({ type: 'text', nullable: true })\r\n  notes?: string;\r\n\r\n  /**\r\n   * Additional metadata\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  metadata?: Record<string, any>;\r\n\r\n  /**\r\n   * User who created this threat actor\r\n   */\r\n  @Column({ name: 'created_by', type: 'uuid', nullable: true })\r\n  createdBy?: string;\r\n\r\n  /**\r\n   * User who last updated this threat actor\r\n   */\r\n  @Column({ name: 'updated_by', type: 'uuid', nullable: true })\r\n  updatedBy?: string;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  // Relationships\r\n  @ManyToMany(() => IOC, ioc => ioc.threatActors)\r\n  iocs: IOC[];\r\n\r\n  /**\r\n   * Check if threat actor is currently active\r\n   */\r\n  get isActive(): boolean {\r\n    return this.status === 'active';\r\n  }\r\n\r\n  /**\r\n   * Get age of threat actor in days\r\n   */\r\n  get ageInDays(): number {\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - this.firstSeen.getTime();\r\n    return Math.floor(diffMs / (1000 * 60 * 60 * 24));\r\n  }\r\n\r\n  /**\r\n   * Get days since last activity\r\n   */\r\n  get daysSinceLastSeen(): number {\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - this.lastSeen.getTime();\r\n    return Math.floor(diffMs / (1000 * 60 * 60 * 24));\r\n  }\r\n\r\n  /**\r\n   * Get risk score based on sophistication, threat level, and activity\r\n   */\r\n  get riskScore(): number {\r\n    const sophisticationWeights = {\r\n      minimal: 1,\r\n      intermediate: 2,\r\n      advanced: 3,\r\n      expert: 4,\r\n      innovator: 5,\r\n      strategic: 6,\r\n    };\r\n\r\n    const threatLevelWeights = {\r\n      low: 1,\r\n      medium: 2,\r\n      high: 3,\r\n      critical: 4,\r\n    };\r\n\r\n    const sophisticationWeight = sophisticationWeights[this.sophistication];\r\n    const threatLevelWeight = threatLevelWeights[this.threatLevel];\r\n    const confidenceWeight = this.confidence / 100;\r\n    const activityWeight = this.isActive ? 1.2 : 0.8;\r\n\r\n    const baseScore = (sophisticationWeight + threatLevelWeight) * confidenceWeight * activityWeight;\r\n    return Math.round(baseScore * 10) / 10;\r\n  }\r\n\r\n  /**\r\n   * Check if actor targets specific sector\r\n   */\r\n  targetsSecor(sector: string): boolean {\r\n    return this.targetedSectors ? this.targetedSectors.includes(sector) : false;\r\n  }\r\n\r\n  /**\r\n   * Check if actor targets specific country\r\n   */\r\n  targetsCountry(country: string): boolean {\r\n    return this.targetedCountries ? this.targetedCountries.includes(country) : false;\r\n  }\r\n\r\n  /**\r\n   * Check if actor uses specific MITRE technique\r\n   */\r\n  usesTechnique(technique: string): boolean {\r\n    return this.mitreTechniques ? this.mitreTechniques.includes(technique) : false;\r\n  }\r\n\r\n  /**\r\n   * Check if actor uses specific malware family\r\n   */\r\n  usesMalware(malwareFamily: string): boolean {\r\n    return this.malwareFamilies ? this.malwareFamilies.includes(malwareFamily) : false;\r\n  }\r\n\r\n  /**\r\n   * Update last seen timestamp\r\n   */\r\n  updateLastSeen(): void {\r\n    this.lastSeen = new Date();\r\n  }\r\n\r\n  /**\r\n   * Add new campaign\r\n   */\r\n  addCampaign(campaign: {\r\n    name: string;\r\n    startDate?: string;\r\n    endDate?: string;\r\n    description?: string;\r\n    targets?: string[];\r\n  }): void {\r\n    if (!this.campaigns) {\r\n      this.campaigns = [];\r\n    }\r\n    this.campaigns.push(campaign);\r\n  }\r\n\r\n  /**\r\n   * Add MITRE technique\r\n   */\r\n  addMitreTechnique(technique: string): void {\r\n    if (!this.mitreTechniques) {\r\n      this.mitreTechniques = [];\r\n    }\r\n    if (!this.mitreTechniques.includes(technique)) {\r\n      this.mitreTechniques.push(technique);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Add targeted sector\r\n   */\r\n  addTargetedSector(sector: string): void {\r\n    if (!this.targetedSectors) {\r\n      this.targetedSectors = [];\r\n    }\r\n    if (!this.targetedSectors.includes(sector)) {\r\n      this.targetedSectors.push(sector);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Add malware family\r\n   */\r\n  addMalwareFamily(malwareFamily: string): void {\r\n    if (!this.malwareFamilies) {\r\n      this.malwareFamilies = [];\r\n    }\r\n    if (!this.malwareFamilies.includes(malwareFamily)) {\r\n      this.malwareFamilies.push(malwareFamily);\r\n    }\r\n  }\r\n}\r\n"], "version": 3}