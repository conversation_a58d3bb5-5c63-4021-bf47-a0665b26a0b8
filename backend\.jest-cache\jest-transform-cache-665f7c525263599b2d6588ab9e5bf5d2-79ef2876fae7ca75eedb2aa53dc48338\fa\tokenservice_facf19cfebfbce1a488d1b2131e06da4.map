{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\auth\\services\\token.service.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,qCAAyC;AACzC,2CAA+C;AAC/C,6CAAmD;AACnD,qCAAqC;AACrC,yDAAqD;AACrD,wDAAqE;AACrE,+CAAiC;AAEjC,gDAAgD;AAChD,gHAAqG;AAErG;;;GAGG;AAEI,IAAM,YAAY,oBAAlB,MAAM,YAAY;IAGvB,YACmB,UAAsB,EACtB,aAA4B,EAC5B,eAAgC,EAEjD,sBAAiE;QAJhD,eAAU,GAAV,UAAU,CAAY;QACtB,kBAAa,GAAb,aAAa,CAAe;QAC5B,oBAAe,GAAf,eAAe,CAAiB;QAEhC,2BAAsB,GAAtB,sBAAsB,CAA0B;QAPlD,WAAM,GAAG,IAAI,eAAM,CAAC,cAAY,CAAC,IAAI,CAAC,CAAC;IAQrD,CAAC;IAEJ;;;;OAIG;IACH,KAAK,CAAC,mBAAmB,CAAC,OAAY;QACpC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE;gBAC3C,MAAM,EAAE,OAAO,CAAC,GAAG;gBACnB,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,SAAS,EAAE,OAAO,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC;gBACrC,eAAe,EAAE,OAAO,CAAC,WAAW,EAAE,MAAM,IAAI,CAAC;aAClD,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAY,KAAK,CAAC,CAAC;YAE3D,MAAM,YAAY,GAAG;gBACnB,GAAG,OAAO;gBACV,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;gBAClC,IAAI,EAAE,QAAQ;aACf,CAAC;YAEF,MAAM,WAAW,GAAG,4BAAe,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAC9D,MAAM,WAAW,GAAG,4BAAe,CAAC,cAAc,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YAEpE,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,EAAE;gBAC/C,GAAG,WAAW;gBACd,MAAM,EAAE,WAAW;aACpB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE;gBACvD,MAAM,EAAE,OAAO,CAAC,GAAG;gBACnB,WAAW,EAAE,KAAK,CAAC,MAAM;gBACzB,SAAS,EAAE,SAAS,CAAC,SAAS;aAC/B,CAAC,CAAC;YAEH,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;gBACjD,MAAM,EAAE,OAAO,CAAC,GAAG;gBACnB,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,oBAAoB,CAAC,MAAc;QACvC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YAE1D,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAY,KAAK,CAAC,CAAC;YAE3D,wBAAwB;YACxB,MAAM,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAE1D,6BAA6B;YAC7B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YAEtE,4BAA4B;YAC5B,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAC7B,MAAM,cAAc,GAAG,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;YAC5E,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,cAAc,GAAG,IAAI,CAAC,CAAC;YAE/D,iCAAiC;YACjC,MAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC;gBACtD,MAAM;gBACN,SAAS;gBACT,SAAS;gBACT,SAAS,EAAE,KAAK;aACjB,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAErD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE;gBACxD,MAAM;gBACN,OAAO,EAAE,YAAY,CAAC,EAAE;gBACxB,SAAS;aACV,CAAC,CAAC;YAEH,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;gBAClD,MAAM;gBACN,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,iBAAiB,CAAC,KAAa;QACnC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;gBAC1C,WAAW,EAAE,KAAK,CAAC,MAAM;aAC1B,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAY,KAAK,CAAC,CAAC;YAC3D,MAAM,aAAa,GAAG,4BAAe,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YAClE,MAAM,WAAW,GAAG,4BAAe,CAAC,cAAc,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YAErE,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE;gBAC5C,GAAG,aAAa;gBAChB,MAAM,EAAE,WAAW;aACpB,CAAC,CAAC;YAEH,sBAAsB;YACtB,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC9B,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;YACxC,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE;gBACtD,MAAM,EAAE,OAAO,CAAC,GAAG;gBACnB,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,SAAS,EAAE,SAAS,CAAC,SAAS;aAC/B,CAAC,CAAC;YAEH,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;gBAChD,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,WAAW,EAAE,KAAK,CAAC,MAAM;aAC1B,CAAC,CAAC;YACH,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,kBAAkB,CAAC,KAAa;QACpC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE;gBAC3C,WAAW,EAAE,KAAK,CAAC,MAAM;aAC1B,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAEjE,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;gBAC7D,KAAK,EAAE,EAAE,SAAS,EAAE;gBACpB,SAAS,EAAE,CAAC,MAAM,CAAC;aACpB,CAAC,CAAC;YAEH,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;gBAC5C,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IAAI,YAAY,CAAC,SAAS,EAAE,CAAC;gBAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE;oBAC3C,OAAO,EAAE,YAAY,CAAC,EAAE;iBACzB,CAAC,CAAC;gBACH,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IAAI,YAAY,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,EAAE,CAAC;gBACxC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE;oBAC5C,OAAO,EAAE,YAAY,CAAC,EAAE;oBACxB,SAAS,EAAE,YAAY,CAAC,SAAS;iBAClC,CAAC,CAAC;gBACH,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE;gBACvD,OAAO,EAAE,YAAY,CAAC,EAAE;gBACxB,MAAM,EAAE,YAAY,CAAC,MAAM;aAC5B,CAAC,CAAC;YAEH,OAAO,YAAY,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;gBACjD,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,WAAW,EAAE,KAAK,CAAC,MAAM;aAC1B,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,kBAAkB,CAAC,OAAe;QACtC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;YAEzD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,CACrD,EAAE,EAAE,EAAE,OAAO,EAAE,EACf,EAAE,SAAS,EAAE,IAAI,EAAE,CACpB,CAAC;YAEF,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC;YAEpC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE;gBACnD,OAAO;gBACP,OAAO;aACR,CAAC,CAAC;YAEH,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;gBAChD,OAAO;gBACP,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,mBAAmB,CAAC,MAAc;QACtC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YAE1D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,CACrD,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,EAC5B,EAAE,SAAS,EAAE,IAAI,EAAE,CACpB,CAAC;YAEF,MAAM,YAAY,GAAG,MAAM,CAAC,QAAQ,IAAI,CAAC,CAAC;YAE1C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,yBAAyB,EAAE;gBACzC,MAAM;gBACN,YAAY;aACb,CAAC,CAAC;YAEH,OAAO,YAAY,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;gBAClD,MAAM;gBACN,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,OAAO,CAAC,CAAC;QACX,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,oBAAoB;QACxB,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAC;YAExD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC;gBACtD,SAAS,EAAE,EAAE,GAAG,EAAE,IAAI,IAAI,EAAE,EAAS;aACtC,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,MAAM,CAAC,QAAQ,IAAI,CAAC,CAAC;YAE1C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2BAA2B,EAAE;gBAC3C,YAAY;aACb,CAAC,CAAC;YAEH,OAAO,YAAY,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE;gBACpD,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,OAAO,CAAC,CAAC;QACX,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,iBAAiB,CAAC,MAAc;QAMpC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC;gBACpD,KAAK,EAAE,EAAE,MAAM,EAAE;aAClB,CAAC,CAAC;YAEH,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,KAAK,GAAG;gBACZ,KAAK,EAAE,MAAM,CAAC,MAAM;gBACpB,MAAM,EAAE,CAAC;gBACT,OAAO,EAAE,CAAC;gBACV,OAAO,EAAE,CAAC;aACX,CAAC;YAEF,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACrB,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;oBACpB,KAAK,CAAC,OAAO,EAAE,CAAC;gBAClB,CAAC;qBAAM,IAAI,KAAK,CAAC,SAAS,GAAG,GAAG,EAAE,CAAC;oBACjC,KAAK,CAAC,OAAO,EAAE,CAAC;gBAClB,CAAC;qBAAM,CAAC;oBACN,KAAK,CAAC,MAAM,EAAE,CAAC;gBACjB,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE;gBACzC,MAAM;gBACN,GAAG,KAAK;aACT,CAAC,CAAC;YAEH,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE;gBACvD,MAAM;gBACN,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;QACzD,CAAC;IACH,CAAC;IAED;;;;OAIG;IACK,mBAAmB,CAAC,SAAiB;QAC3C,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;QACjD,IAAI,CAAC,KAAK;YAAE,OAAO,IAAI,CAAC,CAAC,oBAAoB;QAE7C,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAEtB,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,GAAG,CAAC,CAAC,OAAO,KAAK,CAAC;YACvB,KAAK,GAAG,CAAC,CAAC,OAAO,KAAK,GAAG,EAAE,CAAC;YAC5B,KAAK,GAAG,CAAC,CAAC,OAAO,KAAK,GAAG,IAAI,CAAC;YAC9B,KAAK,GAAG,CAAC,CAAC,OAAO,KAAK,GAAG,KAAK,CAAC;YAC/B,OAAO,CAAC,CAAC,OAAO,IAAI,CAAC;QACvB,CAAC;IACH,CAAC;CACF,CAAA;AAtWY,oCAAY;uBAAZ,YAAY;IADxB,IAAA,mBAAU,GAAE;IAQR,WAAA,IAAA,0BAAgB,EAAC,mCAAY,CAAC,CAAA;yDAHF,gBAAU,oBAAV,gBAAU,oDACP,sBAAa,oBAAb,sBAAa,oDACX,kCAAe,oBAAf,kCAAe,oDAER,oBAAU,oBAAV,oBAAU;GAR1C,YAAY,CAsWxB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\auth\\services\\token.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { JwtService } from '@nestjs/jwt';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\nimport { PasswordService } from './password.service';\r\nimport { JwtConfig, JwtConfigHelper } from '../../config/jwt.config';\r\nimport * as crypto from 'crypto';\r\n\r\n// Import entities (these will be created later)\r\nimport { RefreshToken } from '../../../modules/user-management/domain/entities/refresh-token.entity';\r\n\r\n/**\r\n * Token service for generating and managing JWT and refresh tokens\r\n * Handles token creation, validation, and refresh token lifecycle\r\n */\r\n@Injectable()\r\nexport class TokenService {\r\n  private readonly logger = new Logger(TokenService.name);\r\n\r\n  constructor(\r\n    private readonly jwtService: JwtService,\r\n    private readonly configService: ConfigService,\r\n    private readonly passwordService: PasswordService,\r\n    @InjectRepository(RefreshToken)\r\n    private readonly refreshTokenRepository: Repository<RefreshToken>,\r\n  ) {}\r\n\r\n  /**\r\n   * Generate JWT access token\r\n   * @param payload Token payload\r\n   * @returns Promise<string> JWT token\r\n   */\r\n  async generateAccessToken(payload: any): Promise<string> {\r\n    try {\r\n      this.logger.debug('Generating access token', {\r\n        userId: payload.sub,\r\n        email: payload.email,\r\n        roleCount: payload.roles?.length || 0,\r\n        permissionCount: payload.permissions?.length || 0,\r\n      });\r\n\r\n      const jwtConfig = this.configService.get<JwtConfig>('jwt');\r\n      \r\n      const tokenPayload = {\r\n        ...payload,\r\n        iat: Math.floor(Date.now() / 1000),\r\n        type: 'access',\r\n      };\r\n\r\n      const signOptions = JwtConfigHelper.getSignOptions(jwtConfig);\r\n      const secretOrKey = JwtConfigHelper.getSecretOrKey(jwtConfig, true);\r\n\r\n      const token = this.jwtService.sign(tokenPayload, {\r\n        ...signOptions,\r\n        secret: secretOrKey,\r\n      });\r\n\r\n      this.logger.debug('Access token generated successfully', {\r\n        userId: payload.sub,\r\n        tokenLength: token.length,\r\n        algorithm: jwtConfig.algorithm,\r\n      });\r\n\r\n      return token;\r\n    } catch (error) {\r\n      this.logger.error('Error generating access token', {\r\n        userId: payload.sub,\r\n        error: error.message,\r\n      });\r\n      throw new Error('Failed to generate access token');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Generate refresh token\r\n   * @param userId User ID\r\n   * @returns Promise<string> Refresh token\r\n   */\r\n  async generateRefreshToken(userId: string): Promise<string> {\r\n    try {\r\n      this.logger.debug('Generating refresh token', { userId });\r\n\r\n      const jwtConfig = this.configService.get<JwtConfig>('jwt');\r\n      \r\n      // Generate random token\r\n      const tokenValue = crypto.randomBytes(32).toString('hex');\r\n      \r\n      // Hash the token for storage\r\n      const tokenHash = await this.passwordService.hashPassword(tokenValue);\r\n      \r\n      // Calculate expiration date\r\n      const expiresAt = new Date();\r\n      const expirationTime = this.parseExpirationTime(jwtConfig.refreshExpiresIn);\r\n      expiresAt.setTime(expiresAt.getTime() + expirationTime * 1000);\r\n\r\n      // Save refresh token to database\r\n      const refreshToken = this.refreshTokenRepository.create({\r\n        userId,\r\n        tokenHash,\r\n        expiresAt,\r\n        isRevoked: false,\r\n      });\r\n\r\n      await this.refreshTokenRepository.save(refreshToken);\r\n\r\n      this.logger.debug('Refresh token generated successfully', {\r\n        userId,\r\n        tokenId: refreshToken.id,\r\n        expiresAt,\r\n      });\r\n\r\n      return tokenValue;\r\n    } catch (error) {\r\n      this.logger.error('Error generating refresh token', {\r\n        userId,\r\n        error: error.message,\r\n      });\r\n      throw new Error('Failed to generate refresh token');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Verify JWT token\r\n   * @param token JWT token\r\n   * @returns Promise<any> Decoded token payload\r\n   */\r\n  async verifyAccessToken(token: string): Promise<any> {\r\n    try {\r\n      this.logger.debug('Verifying access token', {\r\n        tokenLength: token.length,\r\n      });\r\n\r\n      const jwtConfig = this.configService.get<JwtConfig>('jwt');\r\n      const verifyOptions = JwtConfigHelper.getVerifyOptions(jwtConfig);\r\n      const secretOrKey = JwtConfigHelper.getSecretOrKey(jwtConfig, false);\r\n      \r\n      const payload = this.jwtService.verify(token, {\r\n        ...verifyOptions,\r\n        secret: secretOrKey,\r\n      });\r\n\r\n      // Validate token type\r\n      if (payload.type !== 'access') {\r\n        throw new Error('Invalid token type');\r\n      }\r\n\r\n      this.logger.debug('Access token verified successfully', {\r\n        userId: payload.sub,\r\n        email: payload.email,\r\n        exp: payload.exp,\r\n        algorithm: jwtConfig.algorithm,\r\n      });\r\n\r\n      return payload;\r\n    } catch (error) {\r\n      this.logger.error('Error verifying access token', {\r\n        error: error.message,\r\n        tokenLength: token.length,\r\n      });\r\n      throw new Error('Invalid or expired token');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Verify refresh token\r\n   * @param token Refresh token\r\n   * @returns Promise<RefreshToken | null> Refresh token entity or null\r\n   */\r\n  async verifyRefreshToken(token: string): Promise<RefreshToken | null> {\r\n    try {\r\n      this.logger.debug('Verifying refresh token', {\r\n        tokenLength: token.length,\r\n      });\r\n\r\n      const tokenHash = await this.passwordService.hashPassword(token);\r\n      \r\n      const refreshToken = await this.refreshTokenRepository.findOne({\r\n        where: { tokenHash },\r\n        relations: ['user'],\r\n      });\r\n\r\n      if (!refreshToken) {\r\n        this.logger.warn('Refresh token not found');\r\n        return null;\r\n      }\r\n\r\n      if (refreshToken.isRevoked) {\r\n        this.logger.warn('Refresh token is revoked', {\r\n          tokenId: refreshToken.id,\r\n        });\r\n        return null;\r\n      }\r\n\r\n      if (refreshToken.expiresAt < new Date()) {\r\n        this.logger.warn('Refresh token has expired', {\r\n          tokenId: refreshToken.id,\r\n          expiresAt: refreshToken.expiresAt,\r\n        });\r\n        return null;\r\n      }\r\n\r\n      this.logger.debug('Refresh token verified successfully', {\r\n        tokenId: refreshToken.id,\r\n        userId: refreshToken.userId,\r\n      });\r\n\r\n      return refreshToken;\r\n    } catch (error) {\r\n      this.logger.error('Error verifying refresh token', {\r\n        error: error.message,\r\n        tokenLength: token.length,\r\n      });\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Revoke refresh token\r\n   * @param tokenId Token ID\r\n   * @returns Promise<boolean>\r\n   */\r\n  async revokeRefreshToken(tokenId: string): Promise<boolean> {\r\n    try {\r\n      this.logger.debug('Revoking refresh token', { tokenId });\r\n\r\n      const result = await this.refreshTokenRepository.update(\r\n        { id: tokenId },\r\n        { isRevoked: true }\r\n      );\r\n\r\n      const success = result.affected > 0;\r\n\r\n      this.logger.debug('Refresh token revocation result', {\r\n        tokenId,\r\n        success,\r\n      });\r\n\r\n      return success;\r\n    } catch (error) {\r\n      this.logger.error('Error revoking refresh token', {\r\n        tokenId,\r\n        error: error.message,\r\n      });\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Revoke all refresh tokens for a user\r\n   * @param userId User ID\r\n   * @returns Promise<number> Number of tokens revoked\r\n   */\r\n  async revokeAllUserTokens(userId: string): Promise<number> {\r\n    try {\r\n      this.logger.debug('Revoking all user tokens', { userId });\r\n\r\n      const result = await this.refreshTokenRepository.update(\r\n        { userId, isRevoked: false },\r\n        { isRevoked: true }\r\n      );\r\n\r\n      const revokedCount = result.affected || 0;\r\n\r\n      this.logger.log('All user tokens revoked', {\r\n        userId,\r\n        revokedCount,\r\n      });\r\n\r\n      return revokedCount;\r\n    } catch (error) {\r\n      this.logger.error('Error revoking all user tokens', {\r\n        userId,\r\n        error: error.message,\r\n      });\r\n      return 0;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Clean up expired refresh tokens\r\n   * @returns Promise<number> Number of tokens cleaned up\r\n   */\r\n  async cleanupExpiredTokens(): Promise<number> {\r\n    try {\r\n      this.logger.debug('Cleaning up expired refresh tokens');\r\n\r\n      const result = await this.refreshTokenRepository.delete({\r\n        expiresAt: { $lt: new Date() } as any,\r\n      });\r\n\r\n      const deletedCount = result.affected || 0;\r\n\r\n      this.logger.log('Expired tokens cleaned up', {\r\n        deletedCount,\r\n      });\r\n\r\n      return deletedCount;\r\n    } catch (error) {\r\n      this.logger.error('Error cleaning up expired tokens', {\r\n        error: error.message,\r\n      });\r\n      return 0;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get token statistics for a user\r\n   * @param userId User ID\r\n   * @returns Promise<Object> Token statistics\r\n   */\r\n  async getUserTokenStats(userId: string): Promise<{\r\n    total: number;\r\n    active: number;\r\n    expired: number;\r\n    revoked: number;\r\n  }> {\r\n    try {\r\n      const tokens = await this.refreshTokenRepository.find({\r\n        where: { userId },\r\n      });\r\n\r\n      const now = new Date();\r\n      const stats = {\r\n        total: tokens.length,\r\n        active: 0,\r\n        expired: 0,\r\n        revoked: 0,\r\n      };\r\n\r\n      tokens.forEach(token => {\r\n        if (token.isRevoked) {\r\n          stats.revoked++;\r\n        } else if (token.expiresAt < now) {\r\n          stats.expired++;\r\n        } else {\r\n          stats.active++;\r\n        }\r\n      });\r\n\r\n      this.logger.debug('User token statistics', {\r\n        userId,\r\n        ...stats,\r\n      });\r\n\r\n      return stats;\r\n    } catch (error) {\r\n      this.logger.error('Error getting user token statistics', {\r\n        userId,\r\n        error: error.message,\r\n      });\r\n      return { total: 0, active: 0, expired: 0, revoked: 0 };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Parse expiration time string to seconds\r\n   * @param expiresIn Expiration time string (e.g., '1h', '30m')\r\n   * @returns number Expiration time in seconds\r\n   */\r\n  private parseExpirationTime(expiresIn: string): number {\r\n    const match = expiresIn.match(/^(\\d+)([smhd])$/);\r\n    if (!match) return 3600; // Default to 1 hour\r\n\r\n    const value = parseInt(match[1]);\r\n    const unit = match[2];\r\n\r\n    switch (unit) {\r\n      case 's': return value;\r\n      case 'm': return value * 60;\r\n      case 'h': return value * 3600;\r\n      case 'd': return value * 86400;\r\n      default: return 3600;\r\n    }\r\n  }\r\n}\r\n"], "version": 3}