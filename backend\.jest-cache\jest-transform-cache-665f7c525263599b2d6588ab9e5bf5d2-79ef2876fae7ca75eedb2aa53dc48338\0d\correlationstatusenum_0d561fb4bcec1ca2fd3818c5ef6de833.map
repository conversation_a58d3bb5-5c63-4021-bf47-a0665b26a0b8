{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\correlation-status.enum.ts", "mappings": ";;;AAAA;;;;;;GAMG;AACH,IAAY,iBAwDX;AAxDD,WAAY,iBAAiB;IAC3B;;;;;OAKG;IACH,wCAAmB,CAAA;IAEnB;;;;;OAKG;IACH,gDAA2B,CAAA;IAE3B;;;;;OAKG;IACH,4CAAuB,CAAA;IAEvB;;;;;OAKG;IACH,sCAAiB,CAAA;IAEjB;;;;;OAKG;IACH,wCAAmB,CAAA;IAEnB;;;;;OAKG;IACH,wCAAmB,CAAA;IAEnB;;;;;OAKG;IACH,wCAAmB,CAAA;AACrB,CAAC,EAxDW,iBAAiB,iCAAjB,iBAAiB,QAwD5B;AAED;;GAEG;AACH,MAAa,sBAAsB;IACjC;;OAEG;IACH,MAAM,CAAC,cAAc;QACnB,OAAO,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB;QACtB,OAAO;YACL,iBAAiB,CAAC,OAAO;YACzB,iBAAiB,CAAC,WAAW;SAC9B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB;QACzB,OAAO;YACL,iBAAiB,CAAC,SAAS;YAC3B,iBAAiB,CAAC,OAAO;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB;QACtB,OAAO;YACL,iBAAiB,CAAC,MAAM;YACxB,iBAAiB,CAAC,OAAO;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB;QACxB,OAAO;YACL,iBAAiB,CAAC,SAAS;YAC3B,iBAAiB,CAAC,MAAM;YACxB,iBAAiB,CAAC,OAAO;YACzB,iBAAiB,CAAC,OAAO;YACzB,iBAAiB,CAAC,OAAO;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,MAAyB;QACvC,OAAO,sBAAsB,CAAC,iBAAiB,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,MAAyB;QAC1C,OAAO,sBAAsB,CAAC,oBAAoB,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,MAAyB;QACvC,OAAO,sBAAsB,CAAC,iBAAiB,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,MAAyB;QACzC,OAAO,sBAAsB,CAAC,mBAAmB,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,MAAyB;QAC1C,OAAO;YACL,iBAAiB,CAAC,MAAM;YACxB,iBAAiB,CAAC,OAAO;SAC1B,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CAAC,aAAgC;QAC1D,MAAM,WAAW,GAAmD;YAClE,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE;gBAC3B,iBAAiB,CAAC,WAAW;gBAC7B,iBAAiB,CAAC,OAAO;gBACzB,iBAAiB,CAAC,MAAM;aACzB;YACD,CAAC,iBAAiB,CAAC,WAAW,CAAC,EAAE;gBAC/B,iBAAiB,CAAC,SAAS;gBAC3B,iBAAiB,CAAC,OAAO;gBACzB,iBAAiB,CAAC,MAAM;gBACxB,iBAAiB,CAAC,OAAO;aAC1B;YACD,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,WAAW;YAC9C,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE;gBAC1B,iBAAiB,CAAC,OAAO,EAAE,YAAY;aACxC;YACD,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE;gBAC3B,iBAAiB,CAAC,WAAW,EAAE,4BAA4B;aAC5D;YACD,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,WAAW;YAC5C,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE;gBAC3B,iBAAiB,CAAC,OAAO,EAAE,YAAY;aACxC;SACF,CAAC;QAEF,OAAO,WAAW,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,IAAuB,EAAE,EAAqB;QACrE,MAAM,iBAAiB,GAAG,sBAAsB,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAC5E,OAAO,iBAAiB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,MAAyB;QAC7C,MAAM,YAAY,GAAsC;YACtD,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,0CAA0C;YACvE,CAAC,iBAAiB,CAAC,WAAW,CAAC,EAAE,2CAA2C;YAC5E,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,6CAA6C;YAC5E,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,yCAAyC;YACrE,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,qDAAqD;YAClF,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,iDAAiD;YAC9E,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,2CAA2C;SACzE,CAAC;QACF,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,MAAyB;QAC3C,MAAM,MAAM,GAAsC;YAChD,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,SAAS,EAAQ,QAAQ;YACtD,CAAC,iBAAiB,CAAC,WAAW,CAAC,EAAE,SAAS,EAAI,OAAO;YACrD,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,SAAS,EAAM,QAAQ;YACtD,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,SAAS,EAAS,MAAM;YACpD,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,SAAS,EAAQ,SAAS;YACvD,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,SAAS,EAAQ,OAAO;YACrD,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,SAAS,EAAQ,YAAY;SAC3D,CAAC;QACF,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,MAAyB;QAC1C,MAAM,KAAK,GAAsC;YAC/C,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,OAAO;YACpC,CAAC,iBAAiB,CAAC,WAAW,CAAC,EAAE,SAAS;YAC1C,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,cAAc;YAC7C,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,UAAU;YACtC,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,gBAAgB;YAC7C,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,cAAc;YAC3C,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,SAAS;SACvC,CAAC;QACF,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,MAAyB;QAC/C,MAAM,UAAU,GAAsC;YACpD,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC,EAAS,6BAA6B;YACnE,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,CAAC,EAAQ,0BAA0B;YAChE,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,CAAC,EAAQ,kBAAkB;YACxD,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,CAAC,EAAQ,gCAAgC;YACtE,CAAC,iBAAiB,CAAC,WAAW,CAAC,EAAE,CAAC,EAAI,uCAAuC;YAC7E,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,CAAC,EAAM,qBAAqB;YAC3D,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,CAAC,EAAQ,qBAAqB;SAC5D,CAAC;QACF,OAAO,UAAU,CAAC,MAAM,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,CAAC,MAAc;QAC3B,OAAO,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,QAAQ,CAAC,MAA2B,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,KAAa;QAC7B,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;QAC9C,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;QAClD,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,KAAK,UAAU,CAAC,IAAI,IAAI,CAAC;IAChE,CAAC;CACF;AAhND,wDAgNC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\correlation-status.enum.ts"], "sourcesContent": ["/**\r\n * Correlation Status Enum\r\n * \r\n * Represents the status of event correlation processing.\r\n * Used to track the lifecycle of correlation analysis and\r\n * indicate the current state of correlation operations.\r\n */\r\nexport enum CorrelationStatus {\r\n  /**\r\n   * Pending Correlation\r\n   * - Event is queued for correlation analysis\r\n   * - Correlation rules have not been applied yet\r\n   * - Waiting for correlation engine processing\r\n   */\r\n  PENDING = 'PENDING',\r\n\r\n  /**\r\n   * Correlation In Progress\r\n   * - Correlation analysis is currently running\r\n   * - Rules are being applied and patterns analyzed\r\n   * - Temporal and spatial analysis in progress\r\n   */\r\n  IN_PROGRESS = 'IN_PROGRESS',\r\n\r\n  /**\r\n   * Correlation Completed\r\n   * - Correlation analysis has finished successfully\r\n   * - All applicable rules have been processed\r\n   * - Correlation results are available\r\n   */\r\n  COMPLETED = 'COMPLETED',\r\n\r\n  /**\r\n   * Correlation Failed\r\n   * - Correlation analysis encountered errors\r\n   * - Processing could not be completed\r\n   * - Manual intervention may be required\r\n   */\r\n  FAILED = 'FAILED',\r\n\r\n  /**\r\n   * Partial Correlation\r\n   * - Some correlation rules succeeded, others failed\r\n   * - Partial results are available\r\n   * - May require additional processing\r\n   */\r\n  PARTIAL = 'PARTIAL',\r\n\r\n  /**\r\n   * Correlation Skipped\r\n   * - Correlation was intentionally bypassed\r\n   * - Event does not meet correlation criteria\r\n   * - No correlation analysis performed\r\n   */\r\n  SKIPPED = 'SKIPPED',\r\n\r\n  /**\r\n   * Correlation Timeout\r\n   * - Correlation analysis exceeded time limits\r\n   * - Processing was terminated due to timeout\r\n   * - May be retried with different parameters\r\n   */\r\n  TIMEOUT = 'TIMEOUT',\r\n}\r\n\r\n/**\r\n * Correlation Status Utilities\r\n */\r\nexport class CorrelationStatusUtils {\r\n  /**\r\n   * Get all correlation statuses\r\n   */\r\n  static getAllStatuses(): CorrelationStatus[] {\r\n    return Object.values(CorrelationStatus);\r\n  }\r\n\r\n  /**\r\n   * Get active correlation statuses (in progress)\r\n   */\r\n  static getActiveStatuses(): CorrelationStatus[] {\r\n    return [\r\n      CorrelationStatus.PENDING,\r\n      CorrelationStatus.IN_PROGRESS,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Get completed correlation statuses\r\n   */\r\n  static getCompletedStatuses(): CorrelationStatus[] {\r\n    return [\r\n      CorrelationStatus.COMPLETED,\r\n      CorrelationStatus.PARTIAL,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Get failed correlation statuses\r\n   */\r\n  static getFailedStatuses(): CorrelationStatus[] {\r\n    return [\r\n      CorrelationStatus.FAILED,\r\n      CorrelationStatus.TIMEOUT,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Get terminal correlation statuses (no further processing)\r\n   */\r\n  static getTerminalStatuses(): CorrelationStatus[] {\r\n    return [\r\n      CorrelationStatus.COMPLETED,\r\n      CorrelationStatus.FAILED,\r\n      CorrelationStatus.PARTIAL,\r\n      CorrelationStatus.SKIPPED,\r\n      CorrelationStatus.TIMEOUT,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Check if status is active\r\n   */\r\n  static isActive(status: CorrelationStatus): boolean {\r\n    return CorrelationStatusUtils.getActiveStatuses().includes(status);\r\n  }\r\n\r\n  /**\r\n   * Check if status is completed\r\n   */\r\n  static isCompleted(status: CorrelationStatus): boolean {\r\n    return CorrelationStatusUtils.getCompletedStatuses().includes(status);\r\n  }\r\n\r\n  /**\r\n   * Check if status is failed\r\n   */\r\n  static isFailed(status: CorrelationStatus): boolean {\r\n    return CorrelationStatusUtils.getFailedStatuses().includes(status);\r\n  }\r\n\r\n  /**\r\n   * Check if status is terminal\r\n   */\r\n  static isTerminal(status: CorrelationStatus): boolean {\r\n    return CorrelationStatusUtils.getTerminalStatuses().includes(status);\r\n  }\r\n\r\n  /**\r\n   * Check if status allows retry\r\n   */\r\n  static allowsRetry(status: CorrelationStatus): boolean {\r\n    return [\r\n      CorrelationStatus.FAILED,\r\n      CorrelationStatus.TIMEOUT,\r\n    ].includes(status);\r\n  }\r\n\r\n  /**\r\n   * Get next valid statuses from current status\r\n   */\r\n  static getNextValidStatuses(currentStatus: CorrelationStatus): CorrelationStatus[] {\r\n    const transitions: Record<CorrelationStatus, CorrelationStatus[]> = {\r\n      [CorrelationStatus.PENDING]: [\r\n        CorrelationStatus.IN_PROGRESS,\r\n        CorrelationStatus.SKIPPED,\r\n        CorrelationStatus.FAILED,\r\n      ],\r\n      [CorrelationStatus.IN_PROGRESS]: [\r\n        CorrelationStatus.COMPLETED,\r\n        CorrelationStatus.PARTIAL,\r\n        CorrelationStatus.FAILED,\r\n        CorrelationStatus.TIMEOUT,\r\n      ],\r\n      [CorrelationStatus.COMPLETED]: [], // Terminal\r\n      [CorrelationStatus.FAILED]: [\r\n        CorrelationStatus.PENDING, // For retry\r\n      ],\r\n      [CorrelationStatus.PARTIAL]: [\r\n        CorrelationStatus.IN_PROGRESS, // For additional processing\r\n      ],\r\n      [CorrelationStatus.SKIPPED]: [], // Terminal\r\n      [CorrelationStatus.TIMEOUT]: [\r\n        CorrelationStatus.PENDING, // For retry\r\n      ],\r\n    };\r\n\r\n    return transitions[currentStatus] || [];\r\n  }\r\n\r\n  /**\r\n   * Check if status transition is valid\r\n   */\r\n  static isValidTransition(from: CorrelationStatus, to: CorrelationStatus): boolean {\r\n    const validNextStatuses = CorrelationStatusUtils.getNextValidStatuses(from);\r\n    return validNextStatuses.includes(to);\r\n  }\r\n\r\n  /**\r\n   * Get human-readable description\r\n   */\r\n  static getDescription(status: CorrelationStatus): string {\r\n    const descriptions: Record<CorrelationStatus, string> = {\r\n      [CorrelationStatus.PENDING]: 'Event is queued for correlation analysis',\r\n      [CorrelationStatus.IN_PROGRESS]: 'Correlation analysis is currently running',\r\n      [CorrelationStatus.COMPLETED]: 'Correlation analysis completed successfully',\r\n      [CorrelationStatus.FAILED]: 'Correlation analysis failed with errors',\r\n      [CorrelationStatus.PARTIAL]: 'Correlation analysis completed with partial results',\r\n      [CorrelationStatus.SKIPPED]: 'Correlation analysis was intentionally bypassed',\r\n      [CorrelationStatus.TIMEOUT]: 'Correlation analysis exceeded time limits',\r\n    };\r\n    return descriptions[status];\r\n  }\r\n\r\n  /**\r\n   * Get color code for UI display\r\n   */\r\n  static getColorCode(status: CorrelationStatus): string {\r\n    const colors: Record<CorrelationStatus, string> = {\r\n      [CorrelationStatus.PENDING]: '#F59E0B',       // Amber\r\n      [CorrelationStatus.IN_PROGRESS]: '#3B82F6',   // Blue\r\n      [CorrelationStatus.COMPLETED]: '#059669',     // Green\r\n      [CorrelationStatus.FAILED]: '#DC2626',        // Red\r\n      [CorrelationStatus.PARTIAL]: '#D97706',       // Orange\r\n      [CorrelationStatus.SKIPPED]: '#6B7280',       // Gray\r\n      [CorrelationStatus.TIMEOUT]: '#EF4444',       // Light Red\r\n    };\r\n    return colors[status];\r\n  }\r\n\r\n  /**\r\n   * Get icon name for UI display\r\n   */\r\n  static getIconName(status: CorrelationStatus): string {\r\n    const icons: Record<CorrelationStatus, string> = {\r\n      [CorrelationStatus.PENDING]: 'clock',\r\n      [CorrelationStatus.IN_PROGRESS]: 'refresh',\r\n      [CorrelationStatus.COMPLETED]: 'check-circle',\r\n      [CorrelationStatus.FAILED]: 'x-circle',\r\n      [CorrelationStatus.PARTIAL]: 'alert-triangle',\r\n      [CorrelationStatus.SKIPPED]: 'skip-forward',\r\n      [CorrelationStatus.TIMEOUT]: 'clock-x',\r\n    };\r\n    return icons[status];\r\n  }\r\n\r\n  /**\r\n   * Get priority level for processing\r\n   */\r\n  static getPriorityLevel(status: CorrelationStatus): number {\r\n    const priorities: Record<CorrelationStatus, number> = {\r\n      [CorrelationStatus.FAILED]: 5,        // Highest priority for retry\r\n      [CorrelationStatus.TIMEOUT]: 4,       // High priority for retry\r\n      [CorrelationStatus.PENDING]: 3,       // Normal priority\r\n      [CorrelationStatus.PARTIAL]: 2,       // Lower priority for completion\r\n      [CorrelationStatus.IN_PROGRESS]: 1,   // Lowest priority (already processing)\r\n      [CorrelationStatus.COMPLETED]: 0,     // No priority (done)\r\n      [CorrelationStatus.SKIPPED]: 0,       // No priority (done)\r\n    };\r\n    return priorities[status];\r\n  }\r\n\r\n  /**\r\n   * Validate correlation status\r\n   */\r\n  static isValid(status: string): boolean {\r\n    return Object.values(CorrelationStatus).includes(status as CorrelationStatus);\r\n  }\r\n\r\n  /**\r\n   * Get correlation status from string (case-insensitive)\r\n   */\r\n  static fromString(value: string): CorrelationStatus | null {\r\n    const normalized = value.toUpperCase().trim();\r\n    const statuses = Object.values(CorrelationStatus);\r\n    return statuses.find(status => status === normalized) || null;\r\n  }\r\n}"], "version": 3}