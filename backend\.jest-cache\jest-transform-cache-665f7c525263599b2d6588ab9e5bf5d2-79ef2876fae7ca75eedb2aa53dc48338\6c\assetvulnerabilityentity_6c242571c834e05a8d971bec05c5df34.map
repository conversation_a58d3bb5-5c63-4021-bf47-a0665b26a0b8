{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\asset-management\\domain\\entities\\asset-vulnerability.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCASiB;AACjB,iDAAuC;AAEvC;;;GAGG;AASI,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IA6Q7B;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,CAAC,MAAM,EAAE,WAAW,EAAE,eAAe,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,QAAQ,KAAK,UAAU,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,QAAQ,KAAK,UAAU;YAC5B,CAAC,IAAI,CAAC,QAAQ,KAAK,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC;YAC9C,IAAI,CAAC,UAAU;YACf,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;QAC3D,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,IAAI,oBAAoB;QACtB,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAO,IAAI,CAAC;QAClC,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;QACvE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE,OAAO,KAAK,CAAC;QAE/B,+BAA+B;QAC/B,MAAM,SAAS,GAAG;YAChB,QAAQ,EAAE,CAAC,EAAI,SAAS;YACxB,IAAI,EAAE,EAAE,EAAO,UAAU;YACzB,MAAM,EAAE,EAAE,EAAK,UAAU;YACzB,GAAG,EAAE,GAAG,EAAO,WAAW;YAC1B,IAAI,EAAE,GAAG,EAAM,WAAW;SAC3B,CAAC;QAEF,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACzC,OAAO,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,uBAAuB;QACvB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,4BAA4B;YAC5B,MAAM,cAAc,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;YAC5E,KAAK,GAAG,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACxC,CAAC;QAED,yBAAyB;QACzB,IAAI,IAAI,CAAC,WAAW;YAAE,KAAK,IAAI,CAAC,CAAC;QACjC,IAAI,IAAI,CAAC,UAAU;YAAE,KAAK,IAAI,CAAC,CAAC;QAChC,IAAI,IAAI,CAAC,SAAS;YAAE,KAAK,IAAI,CAAC,CAAC;QAE/B,yBAAyB;QACzB,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC,cAAc,EAAE,gBAAgB,CAAC;QACpF,IAAI,gBAAgB,KAAK,UAAU;YAAE,KAAK,IAAI,CAAC,CAAC;aAC3C,IAAI,gBAAgB,KAAK,MAAM;YAAE,KAAK,IAAI,CAAC,CAAC;QAEjD,eAAe;QACf,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,KAAK,UAAU;gBAAE,KAAK,IAAI,CAAC,CAAC;YAC9D,IAAI,IAAI,CAAC,WAAW,CAAC,cAAc,KAAK,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,cAAc,KAAK,MAAM;gBAAE,KAAK,IAAI,CAAC,CAAC;YACzG,IAAI,IAAI,CAAC,WAAW,CAAC,eAAe,KAAK,SAAS,IAAI,IAAI,CAAC,WAAW,CAAC,eAAe,KAAK,MAAM;gBAAE,KAAK,IAAI,CAAC,CAAC;YAC9G,IAAI,IAAI,CAAC,WAAW,CAAC,eAAe;gBAAE,KAAK,IAAI,GAAG,CAAC;QACrD,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,MAAc,EAAE,SAAiB,EAAE,MAAe;QAC7D,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAE3B,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,gBAAgB,EAAE,eAAe,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACjF,IAAI,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAC/B,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,CAAC;YAC3C,IAAI,CAAC,oBAAoB,CAAC,WAAW,GAAG,EAAE,CAAC;QAC7C,CAAC;QAED,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,kBAAkB,GAAG,MAAM,CAAC;QACpE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAc,EAAE,UAAkB;QACvC,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;QACzB,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,GAAW;QAChB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,GAAW;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,WAAgB;QAChC,IAAI,CAAC,WAAW,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,WAAW,EAAE,CAAC;QAC3D,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,WAAgB;QAC7B,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,CAAC;YAC3C,IAAI,CAAC,oBAAoB,CAAC,WAAW,GAAG,EAAE,CAAC;QAC7C,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,eAAe,EAAE,CAAC;YAC3D,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,eAAe,GAAG,EAAE,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,eAAuB,EAAE,aAAqB;QAChE,IAAI,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,WAAW,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC;YAC1E,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC;YACtF,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC,eAAe,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;YAC9G,IAAI,CAAC,SAAS,GAAG,aAAa,CAAC;QACjC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,uBAAuB,EAAE,IAAI,CAAC,uBAAuB;YACrD,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,SAAS,EAAE,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACtD,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO;YACL,aAAa,EAAE,IAAI,CAAC,UAAU,EAAE;YAChC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACrC,CAAC;IACJ,CAAC;CACF,CAAA;AAteY,gDAAkB;AAE7B;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;8CACpB;AAMX;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;2DAC3B;AAMxB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC;;mEACb;AAMhC;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;iDACV;AAMd;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;uDACL;AASpB;IAJC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC;KACpD,CAAC;;oDACwD;AAM1D;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDACrE;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sDAC5B;AAUpB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,gBAAgB,EAAE,WAAW,EAAE,SAAS,EAAE,eAAe,EAAE,eAAe,CAAC;QACvG,OAAO,EAAE,MAAM;KAChB,CAAC;;kDACa;AAMf;IADC,IAAA,gBAAM,EAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;uDACN;AAMrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;sDAC5B;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;qDAC7B;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;;gEA8GxB;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDACrE;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uDAU9D;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;gDACtC;AAMf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC;kDACtD,IAAI,oBAAJ,IAAI;wDAAC;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACtE,IAAI,oBAAJ,IAAI;0DAAC;AAMtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACrE,IAAI,oBAAJ,IAAI;sDAAC;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;wDAC3B;AAMrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sDAC1C;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDAC1C;AAGnB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;qDAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;qDAAC;AAKhB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,oBAAK,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,eAAe,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;IAC/E,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;kDAC1B,oBAAK,oBAAL,oBAAK;iDAAC;AAGb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;mDAC3B;6BA3QL,kBAAkB;IAR9B,IAAA,gBAAM,EAAC,uBAAuB,CAAC;IAC/B,IAAA,eAAK,EAAC,CAAC,SAAS,CAAC,CAAC;IAClB,IAAA,eAAK,EAAC,CAAC,iBAAiB,CAAC,CAAC;IAC1B,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,cAAc,CAAC,CAAC;IACvB,IAAA,eAAK,EAAC,CAAC,aAAa,CAAC,CAAC;IACtB,IAAA,eAAK,EAAC,CAAC,YAAY,CAAC,CAAC;GACT,kBAAkB,CAse9B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\asset-management\\domain\\entities\\asset-vulnerability.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  ManyToOne,\r\n  JoinColumn,\r\n} from 'typeorm';\r\nimport { Asset } from './asset.entity';\r\n\r\n/**\r\n * Asset Vulnerability entity\r\n * Represents vulnerabilities associated with specific assets\r\n */\r\n@Entity('asset_vulnerabilities')\r\n@Index(['assetId'])\r\n@Index(['vulnerabilityId'])\r\n@Index(['severity'])\r\n@Index(['status'])\r\n@Index(['discoveredAt'])\r\n@Index(['exploitable'])\r\n@Index(['hasExploit'])\r\nexport class AssetVulnerability {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * Reference to vulnerability from vulnerability management module\r\n   */\r\n  @Column({ name: 'vulnerability_id', type: 'uuid' })\r\n  vulnerabilityId: string;\r\n\r\n  /**\r\n   * Vulnerability identifier (CVE, etc.)\r\n   */\r\n  @Column({ name: 'vulnerability_identifier' })\r\n  vulnerabilityIdentifier: string;\r\n\r\n  /**\r\n   * Vulnerability title\r\n   */\r\n  @Column({ length: 500 })\r\n  title: string;\r\n\r\n  /**\r\n   * Vulnerability description\r\n   */\r\n  @Column({ type: 'text' })\r\n  description: string;\r\n\r\n  /**\r\n   * Vulnerability severity\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['info', 'low', 'medium', 'high', 'critical'],\r\n  })\r\n  severity: 'info' | 'low' | 'medium' | 'high' | 'critical';\r\n\r\n  /**\r\n   * CVSS score\r\n   */\r\n  @Column({ name: 'cvss_score', type: 'decimal', precision: 3, scale: 1, nullable: true })\r\n  cvssScore?: number;\r\n\r\n  /**\r\n   * CVSS vector\r\n   */\r\n  @Column({ name: 'cvss_vector', nullable: true })\r\n  cvssVector?: string;\r\n\r\n  /**\r\n   * Vulnerability status on this asset\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['open', 'confirmed', 'false_positive', 'mitigated', 'patched', 'accepted_risk', 'investigating'],\r\n    default: 'open',\r\n  })\r\n  status: string;\r\n\r\n  /**\r\n   * Whether vulnerability is exploitable\r\n   */\r\n  @Column({ default: false })\r\n  exploitable: boolean;\r\n\r\n  /**\r\n   * Whether exploit code is available\r\n   */\r\n  @Column({ name: 'has_exploit', default: false })\r\n  hasExploit: boolean;\r\n\r\n  /**\r\n   * Whether vulnerability is being exploited in the wild\r\n   */\r\n  @Column({ name: 'in_the_wild', default: false })\r\n  inTheWild: boolean;\r\n\r\n  /**\r\n   * Asset-specific vulnerability details\r\n   */\r\n  @Column({ type: 'jsonb' })\r\n  assetSpecificDetails: {\r\n    // Affected components on this asset\r\n    affectedComponents?: Array<{\r\n      type: 'software' | 'service' | 'configuration' | 'hardware';\r\n      name: string;\r\n      version?: string;\r\n      path?: string;\r\n      port?: number;\r\n      process?: string;\r\n    }>;\r\n    \r\n    // Detection details\r\n    detection?: {\r\n      method: 'scanner' | 'agent' | 'manual' | 'threat_intelligence';\r\n      source: string;\r\n      confidence: 'low' | 'medium' | 'high';\r\n      evidence?: Array<{\r\n        type: 'file' | 'registry' | 'process' | 'network' | 'configuration';\r\n        location: string;\r\n        value?: string;\r\n        description?: string;\r\n      }>;\r\n    };\r\n    \r\n    // Exploitation details\r\n    exploitation?: {\r\n      difficulty: 'low' | 'medium' | 'high';\r\n      prerequisites?: string[];\r\n      impact?: {\r\n        confidentiality: 'none' | 'partial' | 'complete';\r\n        integrity: 'none' | 'partial' | 'complete';\r\n        availability: 'none' | 'partial' | 'complete';\r\n      };\r\n      exploitScenarios?: Array<{\r\n        scenario: string;\r\n        likelihood: 'low' | 'medium' | 'high';\r\n        impact: string;\r\n      }>;\r\n    };\r\n    \r\n    // Remediation specific to this asset\r\n    remediation?: {\r\n      recommendations?: Array<{\r\n        action: string;\r\n        priority: 'low' | 'medium' | 'high' | 'critical';\r\n        effort: 'low' | 'medium' | 'high';\r\n        cost?: 'low' | 'medium' | 'high';\r\n        timeline?: string;\r\n        dependencies?: string[];\r\n      }>;\r\n      patches?: Array<{\r\n        id: string;\r\n        title: string;\r\n        vendor: string;\r\n        releaseDate: string;\r\n        downloadUrl?: string;\r\n        prerequisites?: string[];\r\n        rebootRequired?: boolean;\r\n      }>;\r\n      workarounds?: Array<{\r\n        description: string;\r\n        effectiveness: 'low' | 'medium' | 'high';\r\n        temporaryFix: boolean;\r\n        sideEffects?: string[];\r\n      }>;\r\n      mitigations?: Array<{\r\n        type: 'configuration' | 'network' | 'access_control' | 'monitoring';\r\n        description: string;\r\n        effectiveness: 'low' | 'medium' | 'high';\r\n        implemented: boolean;\r\n        implementedDate?: string;\r\n      }>;\r\n    };\r\n    \r\n    // Business impact on this asset\r\n    businessImpact?: {\r\n      assetCriticality: 'low' | 'medium' | 'high' | 'critical';\r\n      businessFunction?: string;\r\n      dataClassification?: 'public' | 'internal' | 'confidential' | 'restricted';\r\n      userImpact?: number; // number of affected users\r\n      financialImpact?: {\r\n        estimatedCost: number;\r\n        currency: string;\r\n        impactType: 'direct' | 'indirect' | 'opportunity_cost';\r\n      };\r\n      complianceImpact?: Array<{\r\n        framework: string;\r\n        requirement: string;\r\n        severity: 'low' | 'medium' | 'high' | 'critical';\r\n      }>;\r\n    };\r\n    \r\n    // Threat context\r\n    threatContext?: {\r\n      threatActors?: string[];\r\n      attackVectors?: string[];\r\n      campaigns?: string[];\r\n      iocs?: Array<{\r\n        type: string;\r\n        value: string;\r\n        confidence: 'low' | 'medium' | 'high';\r\n      }>;\r\n      threatIntelligence?: Array<{\r\n        source: string;\r\n        intelligence: string;\r\n        confidence: 'low' | 'medium' | 'high';\r\n        date: string;\r\n      }>;\r\n    };\r\n  };\r\n\r\n  /**\r\n   * Risk score calculated for this asset\r\n   */\r\n  @Column({ name: 'risk_score', type: 'decimal', precision: 4, scale: 2, nullable: true })\r\n  riskScore?: number;\r\n\r\n  /**\r\n   * Risk factors specific to this asset\r\n   */\r\n  @Column({ name: 'risk_factors', type: 'jsonb', nullable: true })\r\n  riskFactors?: {\r\n    assetExposure: 'internal' | 'external' | 'dmz' | 'cloud';\r\n    networkSegmentation: 'isolated' | 'segmented' | 'flat';\r\n    accessControls: 'strong' | 'moderate' | 'weak' | 'none';\r\n    monitoringCoverage: 'comprehensive' | 'partial' | 'minimal' | 'none';\r\n    patchingCadence: 'immediate' | 'regular' | 'delayed' | 'none';\r\n    backupStatus: 'current' | 'outdated' | 'none';\r\n    incidentHistory: boolean;\r\n    complianceRequirements: string[];\r\n  };\r\n\r\n  /**\r\n   * Vulnerability tags\r\n   */\r\n  @Column({ type: 'text', array: true, default: '{}' })\r\n  tags: string[];\r\n\r\n  /**\r\n   * When vulnerability was discovered on this asset\r\n   */\r\n  @Column({ name: 'discovered_at', type: 'timestamp with time zone' })\r\n  discoveredAt: Date;\r\n\r\n  /**\r\n   * When vulnerability was last verified on this asset\r\n   */\r\n  @Column({ name: 'last_verified_at', type: 'timestamp with time zone', nullable: true })\r\n  lastVerifiedAt?: Date;\r\n\r\n  /**\r\n   * When vulnerability was resolved on this asset\r\n   */\r\n  @Column({ name: 'resolved_at', type: 'timestamp with time zone', nullable: true })\r\n  resolvedAt?: Date;\r\n\r\n  /**\r\n   * User who discovered the vulnerability\r\n   */\r\n  @Column({ name: 'discovered_by', type: 'uuid' })\r\n  discoveredBy: string;\r\n\r\n  /**\r\n   * User assigned to handle the vulnerability\r\n   */\r\n  @Column({ name: 'assigned_to', type: 'uuid', nullable: true })\r\n  assignedTo?: string;\r\n\r\n  /**\r\n   * User who last updated the vulnerability\r\n   */\r\n  @Column({ name: 'updated_by', type: 'uuid', nullable: true })\r\n  updatedBy?: string;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  // Relationships\r\n  @ManyToOne(() => Asset, asset => asset.vulnerabilities, { onDelete: 'CASCADE' })\r\n  @JoinColumn({ name: 'asset_id' })\r\n  asset: Asset;\r\n\r\n  @Column({ name: 'asset_id', type: 'uuid' })\r\n  assetId: string;\r\n\r\n  /**\r\n   * Check if vulnerability is open\r\n   */\r\n  get isOpen(): boolean {\r\n    return ['open', 'confirmed', 'investigating'].includes(this.status);\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability is critical\r\n   */\r\n  get isCritical(): boolean {\r\n    return this.severity === 'critical';\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability is high risk\r\n   */\r\n  get isHighRisk(): boolean {\r\n    return this.severity === 'critical' || \r\n           (this.severity === 'high' && this.exploitable) ||\r\n           this.hasExploit ||\r\n           this.inTheWild;\r\n  }\r\n\r\n  /**\r\n   * Get vulnerability age in days\r\n   */\r\n  get ageInDays(): number {\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - this.discoveredAt.getTime();\r\n    return Math.floor(diffMs / (1000 * 60 * 60 * 24));\r\n  }\r\n\r\n  /**\r\n   * Get time to resolution in days\r\n   */\r\n  get timeToResolutionDays(): number | null {\r\n    if (!this.resolvedAt) return null;\r\n    const diffMs = this.resolvedAt.getTime() - this.discoveredAt.getTime();\r\n    return Math.floor(diffMs / (1000 * 60 * 60 * 24));\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability is overdue for patching\r\n   */\r\n  get isOverdue(): boolean {\r\n    if (!this.isOpen) return false;\r\n    \r\n    // Define SLA based on severity\r\n    const slaMatrix = {\r\n      critical: 7,   // 7 days\r\n      high: 30,      // 30 days\r\n      medium: 90,    // 90 days\r\n      low: 180,      // 180 days\r\n      info: 365,     // 365 days\r\n    };\r\n    \r\n    const slaDays = slaMatrix[this.severity];\r\n    return this.ageInDays > slaDays;\r\n  }\r\n\r\n  /**\r\n   * Calculate risk score for this asset\r\n   */\r\n  calculateRiskScore(): number {\r\n    let score = 0;\r\n\r\n    // Base score from CVSS\r\n    if (this.cvssScore) {\r\n      score = this.cvssScore;\r\n    } else {\r\n      // Fallback severity scoring\r\n      const severityScores = { info: 1, low: 3, medium: 5, high: 7, critical: 9 };\r\n      score = severityScores[this.severity];\r\n    }\r\n\r\n    // Exploitability factors\r\n    if (this.exploitable) score += 1;\r\n    if (this.hasExploit) score += 1;\r\n    if (this.inTheWild) score += 2;\r\n\r\n    // Asset-specific factors\r\n    const assetCriticality = this.assetSpecificDetails.businessImpact?.assetCriticality;\r\n    if (assetCriticality === 'critical') score += 2;\r\n    else if (assetCriticality === 'high') score += 1;\r\n\r\n    // Risk factors\r\n    if (this.riskFactors) {\r\n      if (this.riskFactors.assetExposure === 'external') score += 1;\r\n      if (this.riskFactors.accessControls === 'weak' || this.riskFactors.accessControls === 'none') score += 1;\r\n      if (this.riskFactors.patchingCadence === 'delayed' || this.riskFactors.patchingCadence === 'none') score += 1;\r\n      if (this.riskFactors.incidentHistory) score += 0.5;\r\n    }\r\n\r\n    return Math.min(10, score);\r\n  }\r\n\r\n  /**\r\n   * Update vulnerability status\r\n   */\r\n  updateStatus(status: string, updatedBy: string, reason?: string): void {\r\n    this.status = status;\r\n    this.updatedBy = updatedBy;\r\n    \r\n    if (['patched', 'mitigated', 'false_positive', 'accepted_risk'].includes(status)) {\r\n      this.resolvedAt = new Date();\r\n    }\r\n    \r\n    if (!this.assetSpecificDetails.remediation) {\r\n      this.assetSpecificDetails.remediation = {};\r\n    }\r\n    \r\n    if (reason) {\r\n      this.assetSpecificDetails.remediation.statusChangeReason = reason;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Assign vulnerability to user\r\n   */\r\n  assign(userId: string, assignedBy: string): void {\r\n    this.assignedTo = userId;\r\n    this.updatedBy = assignedBy;\r\n  }\r\n\r\n  /**\r\n   * Add tag to vulnerability\r\n   */\r\n  addTag(tag: string): void {\r\n    if (!this.tags.includes(tag)) {\r\n      this.tags.push(tag);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove tag from vulnerability\r\n   */\r\n  removeTag(tag: string): void {\r\n    this.tags = this.tags.filter(t => t !== tag);\r\n  }\r\n\r\n  /**\r\n   * Update risk factors\r\n   */\r\n  updateRiskFactors(riskFactors: any): void {\r\n    this.riskFactors = { ...this.riskFactors, ...riskFactors };\r\n    this.riskScore = this.calculateRiskScore();\r\n  }\r\n\r\n  /**\r\n   * Add remediation recommendation\r\n   */\r\n  addRemediation(remediation: any): void {\r\n    if (!this.assetSpecificDetails.remediation) {\r\n      this.assetSpecificDetails.remediation = {};\r\n    }\r\n    \r\n    if (!this.assetSpecificDetails.remediation.recommendations) {\r\n      this.assetSpecificDetails.remediation.recommendations = [];\r\n    }\r\n    \r\n    this.assetSpecificDetails.remediation.recommendations.push(remediation);\r\n  }\r\n\r\n  /**\r\n   * Mark mitigation as implemented\r\n   */\r\n  implementMitigation(mitigationIndex: number, implementedBy: string): void {\r\n    if (this.assetSpecificDetails.remediation?.mitigations?.[mitigationIndex]) {\r\n      this.assetSpecificDetails.remediation.mitigations[mitigationIndex].implemented = true;\r\n      this.assetSpecificDetails.remediation.mitigations[mitigationIndex].implementedDate = new Date().toISOString();\r\n      this.updatedBy = implementedBy;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get vulnerability summary\r\n   */\r\n  getSummary(): any {\r\n    return {\r\n      id: this.id,\r\n      vulnerabilityId: this.vulnerabilityId,\r\n      vulnerabilityIdentifier: this.vulnerabilityIdentifier,\r\n      title: this.title,\r\n      severity: this.severity,\r\n      cvssScore: this.cvssScore,\r\n      status: this.status,\r\n      assetId: this.assetId,\r\n      exploitable: this.exploitable,\r\n      hasExploit: this.hasExploit,\r\n      inTheWild: this.inTheWild,\r\n      isOpen: this.isOpen,\r\n      isCritical: this.isCritical,\r\n      isHighRisk: this.isHighRisk,\r\n      isOverdue: this.isOverdue,\r\n      ageInDays: this.ageInDays,\r\n      timeToResolutionDays: this.timeToResolutionDays,\r\n      riskScore: this.riskScore || this.calculateRiskScore(),\r\n      discoveredAt: this.discoveredAt,\r\n      lastVerifiedAt: this.lastVerifiedAt,\r\n      resolvedAt: this.resolvedAt,\r\n      assignedTo: this.assignedTo,\r\n      tags: this.tags,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Export vulnerability for reporting\r\n   */\r\n  exportForReporting(): any {\r\n    return {\r\n      vulnerability: this.getSummary(),\r\n      assetSpecificDetails: this.assetSpecificDetails,\r\n      riskFactors: this.riskFactors,\r\n      exportedAt: new Date().toISOString(),\r\n    };\r\n  }\r\n}\r\n"], "version": 3}