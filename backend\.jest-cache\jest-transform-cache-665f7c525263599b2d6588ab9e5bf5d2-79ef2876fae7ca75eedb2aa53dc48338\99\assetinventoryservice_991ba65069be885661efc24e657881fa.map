{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\asset-management\\application\\services\\asset-inventory.service.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,2CAAuE;AACvE,6CAAmD;AACnD,qCAAmE;AACnE,+CAAwD;AACxD,qEAA2D;AAC3D,iFAAsE;AACtE,iGAAsF;AACtF,iGAAsF;AACtF,+FAAoF;AACpF,sFAAkF;AAClF,0FAAsF;AACtF,uGAAmG;AACnG,uEAAkE;AAClE,iFAA4E;AAE5E;;;GAGG;AAEI,IAAM,qBAAqB,6BAA3B,MAAM,qBAAqB;IAGhC,YAEE,eAAmD,EAEnD,oBAA6D,EAE7D,uBAAwE,EAExE,uBAAwE,EAExE,sBAAsE,EACrD,aAA4B,EAC5B,YAA0B,EAC1B,mBAAwC,EACxC,qBAA4C,EAC5C,0BAAsD;QAbtD,oBAAe,GAAf,eAAe,CAAmB;QAElC,yBAAoB,GAApB,oBAAoB,CAAwB;QAE5C,4BAAuB,GAAvB,uBAAuB,CAAgC;QAEvD,4BAAuB,GAAvB,uBAAuB,CAAgC;QAEvD,2BAAsB,GAAtB,sBAAsB,CAA+B;QACrD,kBAAa,GAAb,aAAa,CAAe;QAC5B,iBAAY,GAAZ,YAAY,CAAc;QAC1B,wBAAmB,GAAnB,mBAAmB,CAAqB;QACxC,0BAAqB,GAArB,qBAAqB,CAAuB;QAC5C,+BAA0B,GAA1B,0BAA0B,CAA4B;QAjBxD,WAAM,GAAG,IAAI,eAAM,CAAC,uBAAqB,CAAC,IAAI,CAAC,CAAC;IAkB9D,CAAC;IAEJ;;;OAGG;IACH,KAAK,CAAC,qBAAqB;QACzB,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;YAE1D,MAAM,CACJ,WAAW,EACX,YAAY,EACZ,cAAc,EACd,mBAAmB,EACnB,mBAAmB,EACnB,YAAY,EACZ,gBAAgB,EAChB,gBAAgB,EAChB,kBAAkB,EAClB,eAAe,EAChB,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACpB,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,IAAI,CAAC,eAAe,EAAE;gBACtB,IAAI,CAAC,iBAAiB,EAAE;gBACxB,IAAI,CAAC,sBAAsB,EAAE;gBAC7B,IAAI,CAAC,sBAAsB,EAAE;gBAC7B,IAAI,CAAC,oBAAoB,EAAE;gBAC3B,IAAI,CAAC,wBAAwB,EAAE;gBAC/B,IAAI,CAAC,wBAAwB,EAAE;gBAC/B,IAAI,CAAC,2BAA2B,CAAC,CAAC,CAAC,EAAE,cAAc;gBACnD,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,EAAE,eAAe;aAC7C,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG;gBAChB,OAAO,EAAE;oBACP,WAAW;oBACX,YAAY;oBACZ,aAAa,EAAE,WAAW,GAAG,YAAY;oBACzC,gBAAgB;oBAChB,gBAAgB;oBAChB,aAAa,EAAE,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,gBAAgB,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;oBACvF,iBAAiB,EAAE,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,gBAAgB,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC5F;gBACD,SAAS,EAAE;oBACT,MAAM,EAAE,YAAY;oBACpB,QAAQ,EAAE,cAAc;oBACxB,aAAa,EAAE,mBAAmB;oBAClC,aAAa,EAAE,mBAAmB;iBACnC;gBACD,cAAc,EAAE;oBACd,kBAAkB;oBAClB,MAAM,EAAE,eAAe;iBACxB;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kDAAkD,EAAE;gBAClE,WAAW;gBACX,YAAY;gBACZ,gBAAgB;aACjB,CAAC,CAAC;YAEH,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8CAA8C,EAAE;gBAChE,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,YAAY,CAAC,QAgBlB;QACC,IAAI,CAAC;YACH,MAAM,EACJ,IAAI,GAAG,CAAC,EACR,KAAK,GAAG,EAAE,EACV,KAAK,EACL,QAAQ,EACR,aAAa,EACb,YAAY,EACZ,SAAS,EACT,QAAQ,EACR,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,kBAAkB,EAClB,UAAU,EACV,MAAM,GAAG,MAAM,EACf,SAAS,GAAG,KAAK,GAClB,GAAG,QAAQ,CAAC;YAEb,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,OAAO,CAAC;iBAClE,iBAAiB,CAAC,aAAa,EAAE,OAAO,CAAC;iBACzC,iBAAiB,CAAC,uBAAuB,EAAE,iBAAiB,CAAC,CAAC;YAEjE,gBAAgB;YAChB,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9B,YAAY,CAAC,QAAQ,CAAC,2BAA2B,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAChE,CAAC;YAED,IAAI,QAAQ,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpC,YAAY,CAAC,QAAQ,CAAC,gCAAgC,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YACxE,CAAC;YAED,IAAI,aAAa,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9C,YAAY,CAAC,QAAQ,CAAC,0CAA0C,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;YACvF,CAAC;YAED,IAAI,YAAY,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5C,YAAY,CAAC,QAAQ,CAAC,yCAAyC,EAAE,EAAE,YAAY,EAAE,CAAC,CAAC;YACrF,CAAC;YAED,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtC,YAAY,CAAC,QAAQ,CAAC,mCAAmC,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;YAC5E,CAAC;YAED,IAAI,QAAQ,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpC,YAAY,CAAC,QAAQ,CAAC,iCAAiC,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YACzE,CAAC;YAED,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5B,YAAY,CAAC,QAAQ,CAAC,qBAAqB,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;YACzD,CAAC;YAED,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;gBAC3B,IAAI,QAAQ,EAAE,CAAC;oBACb,YAAY,CAAC,QAAQ,CAAC,6CAA6C,CAAC,CAAC;gBACvE,CAAC;qBAAM,CAAC;oBACN,YAAY,CAAC,QAAQ,CAAC,8FAA8F,CAAC,CAAC;gBACxH,CAAC;YACH,CAAC;YAED,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;gBAC3B,MAAM,cAAc,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;gBAC5D,IAAI,QAAQ,EAAE,CAAC;oBACb,YAAY,CAAC,QAAQ,CAAC,kCAAkC,EAAE,EAAE,cAAc,EAAE,CAAC,CAAC;gBAChF,CAAC;qBAAM,CAAC;oBACN,YAAY,CAAC,QAAQ,CAAC,+DAA+D,EAAE,EAAE,cAAc,EAAE,CAAC,CAAC;gBAC7G,CAAC;YACH,CAAC;YAED,IAAI,kBAAkB,KAAK,SAAS,EAAE,CAAC;gBACrC,IAAI,kBAAkB,EAAE,CAAC;oBACvB,YAAY,CAAC,QAAQ,CAAC,gCAAgC,CAAC,CAAC;gBAC1D,CAAC;qBAAM,CAAC;oBACN,YAAY,CAAC,QAAQ,CAAC,4BAA4B,CAAC,CAAC;gBACtD,CAAC;YACH,CAAC;YAED,IAAI,UAAU,EAAE,CAAC;gBACf,YAAY,CAAC,QAAQ,CACnB,gJAAgJ,EAChJ,EAAE,UAAU,EAAE,IAAI,UAAU,GAAG,EAAE,CAClC,CAAC;YACJ,CAAC;YAED,gBAAgB;YAChB,YAAY,CAAC,OAAO,CAAC,SAAS,MAAM,EAAE,EAAE,SAAS,CAAC,CAAC;YAEnD,mBAAmB;YACnB,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAClC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEtC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM,YAAY,CAAC,eAAe,EAAE,CAAC;YAC7D,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;YAE5C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;gBAChD,KAAK;gBACL,IAAI;gBACJ,KAAK;gBACL,UAAU;gBACV,OAAO,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAE;aAC9E,CAAC,CAAC;YAEH,OAAO;gBACL,MAAM;gBACN,KAAK;gBACL,IAAI;gBACJ,UAAU;aACX,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE;gBAC3C,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,QAAQ;aACT,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,WAAW,CAAC,SAmBjB,EAAE,MAAc;QACf,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE;gBACtC,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,MAAM;aACP,CAAC,CAAC;YAEH,8BAA8B;YAC9B,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;gBACtB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;oBACpD,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE;iBACjD,CAAC,CAAC;gBACH,IAAI,CAAC,KAAK,EAAE,CAAC;oBACX,MAAM,IAAI,0BAAiB,CAAC,mCAAmC,CAAC,CAAC;gBACnE,CAAC;YACH,CAAC;YAED,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;gBACxC,GAAG,SAAS;gBACZ,MAAM,EAAE,SAAS;gBACjB,YAAY,EAAE,IAAI,IAAI,EAAE;gBACxB,QAAQ,EAAE,IAAI,IAAI,EAAE;gBACpB,SAAS,EAAE,MAAM;gBACjB,WAAW,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;gBAC7D,IAAI,EAAE,SAAS,CAAC,IAAI,IAAI,EAAE;aAC3B,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE1D,sBAAsB;YACtB,MAAM,IAAI,CAAC,0BAA0B,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YAEnE,uCAAuC;YACvC,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YAE1C,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,OAAO,EACP,UAAU,CAAC,EAAE,EACb;gBACE,SAAS,EAAE,SAAS,CAAC,IAAI;gBACzB,SAAS,EAAE,SAAS,CAAC,IAAI;gBACzB,WAAW,EAAE,SAAS,CAAC,WAAW;gBAClC,WAAW,EAAE,SAAS,CAAC,WAAW;aACnC,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,EAAE;gBAC5C,OAAO,EAAE,UAAU,CAAC,EAAE;gBACtB,SAAS,EAAE,SAAS,CAAC,IAAI;gBACzB,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;gBAC1C,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,SAAS;gBACT,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,WAAW,CAAC,OAAe,EAAE,OAAY,EAAE,MAAc;QAC7D,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;gBAC/C,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE;gBACtB,SAAS,EAAE,CAAC,OAAO,CAAC;aACrB,CAAC,CAAC;YAEH,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,MAAM,IAAI,0BAAiB,CAAC,iBAAiB,CAAC,CAAC;YACjD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE;gBAClC,OAAO;gBACP,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;gBAC7B,MAAM;aACP,CAAC,CAAC;YAEH,kCAAkC;YAClC,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC;gBACzD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;oBACpD,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE;iBAC/C,CAAC,CAAC;gBACH,IAAI,CAAC,KAAK,EAAE,CAAC;oBACX,MAAM,IAAI,0BAAiB,CAAC,mCAAmC,CAAC,CAAC;gBACnE,CAAC;YACH,CAAC;YAED,0BAA0B;YAC1B,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAEvD,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAC9B,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC;YAEzB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE5D,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,OAAO,EACP,OAAO,EACP;gBACE,SAAS,EAAE,KAAK,CAAC,IAAI;gBACrB,OAAO;aACR,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,EAAE;gBAC5C,OAAO;gBACP,MAAM;gBACN,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM;aAC1C,CAAC,CAAC;YAEH,OAAO,YAAY,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;gBAC1C,OAAO;gBACP,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,WAAW,CAAC,OAAe,EAAE,MAAc;QAC/C,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;gBAC/C,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE;aACvB,CAAC,CAAC;YAEH,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,MAAM,IAAI,0BAAiB,CAAC,iBAAiB,CAAC,CAAC;YACjD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE;gBAClC,OAAO;gBACP,SAAS,EAAE,KAAK,CAAC,IAAI;gBACrB,MAAM;aACP,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAEzC,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,OAAO,EACP,OAAO,EACP;gBACE,SAAS,EAAE,KAAK,CAAC,IAAI;gBACrB,SAAS,EAAE,KAAK,CAAC,IAAI;aACtB,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,EAAE;gBAC5C,OAAO;gBACP,SAAS,EAAE,KAAK,CAAC,IAAI;gBACrB,MAAM;aACP,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;gBAC1C,OAAO;gBACP,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,eAAe,CAAC,OAAe;QACnC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;gBAC/C,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE;gBACtB,SAAS,EAAE;oBACT,OAAO;oBACP,gBAAgB;oBAChB,iBAAiB;oBACjB,qBAAqB;oBACrB,qBAAqB;oBACrB,cAAc;oBACd,eAAe;iBAChB;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,MAAM,IAAI,0BAAiB,CAAC,iBAAiB,CAAC,CAAC;YACjD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE;gBAC3C,OAAO;gBACP,SAAS,EAAE,KAAK,CAAC,IAAI;aACtB,CAAC,CAAC;YAEH,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;gBAC/C,OAAO;gBACP,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,cAAc,CAAC,OAAe;QAClC,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,EAAE;gBACzC,QAAQ,EAAE,IAAI,IAAI,EAAE;aACrB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;QAC5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE;gBACpD,OAAO;gBACP,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,gBAAgB,CACpB,QAAkB,EAClB,MAA4E,EAC5E,MAAc;QAEd,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;gBAChD,UAAU,EAAE,QAAQ,CAAC,MAAM;gBAC3B,MAAM;gBACN,MAAM;aACP,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAC/B,EAAE,EAAE,EAAE,IAAA,YAAE,EAAC,QAAQ,CAAC,EAAE,EACpB,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,CAC9B,CAAC;YAEF,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,aAAa,EACb,OAAO,EACP,IAAI,EACJ;gBACE,QAAQ;gBACR,MAAM;gBACN,UAAU,EAAE,QAAQ,CAAC,MAAM;aAC5B,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qCAAqC,EAAE;gBACrD,UAAU,EAAE,QAAQ,CAAC,MAAM;gBAC3B,MAAM;gBACN,MAAM;aACP,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE;gBACxD,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,QAAQ;gBACR,MAAM;gBACN,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IAEG,AAAN,KAAK,CAAC,gBAAgB;QACpB,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;YAEnD,2BAA2B;YAC3B,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAEhC,kCAAkC;YAClC,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAEtC,gCAAgC;YAChC,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAErC,8BAA8B;YAC9B,MAAM,IAAI,CAAC,yBAAyB,EAAE,CAAC;YAEvC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE;gBAC3D,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,yBAAyB;IAEjB,KAAK,CAAC,mBAAmB;QAC/B,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;IAC5C,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe;aACtC,kBAAkB,CAAC,OAAO,CAAC;aAC3B,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC;aAC5B,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC;aAC9B,OAAO,CAAC,YAAY,CAAC;aACrB,UAAU,EAAE,CAAC;QAEhB,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YACjC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtC,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe;aACtC,kBAAkB,CAAC,OAAO,CAAC;aAC3B,MAAM,CAAC,cAAc,EAAE,QAAQ,CAAC;aAChC,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC;aAC9B,OAAO,CAAC,cAAc,CAAC;aACvB,UAAU,EAAE,CAAC;QAEhB,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YACjC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAEO,KAAK,CAAC,sBAAsB;QAClC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe;aACtC,kBAAkB,CAAC,OAAO,CAAC;aAC3B,MAAM,CAAC,mBAAmB,EAAE,aAAa,CAAC;aAC1C,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC;aAC9B,OAAO,CAAC,mBAAmB,CAAC;aAC5B,UAAU,EAAE,CAAC;QAEhB,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YACjC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7C,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAEO,KAAK,CAAC,sBAAsB;QAClC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe;aACtC,kBAAkB,CAAC,OAAO,CAAC;aAC3B,MAAM,CAAC,mBAAmB,EAAE,aAAa,CAAC;aAC1C,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC;aAC9B,OAAO,CAAC,mBAAmB,CAAC;aAC5B,UAAU,EAAE,CAAC;QAEhB,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YACjC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7C,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAEO,KAAK,CAAC,oBAAoB;QAChC,MAAM,cAAc,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAC5D,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;YACtC,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAA,iBAAO,EAAC,cAAc,EAAE,IAAI,IAAI,EAAE,CAAC,EAAE;SACzD,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,wBAAwB;QACpC,OAAO,MAAM,IAAI,CAAC,eAAe;aAC9B,kBAAkB,CAAC,OAAO,CAAC;aAC3B,KAAK,CAAC,6CAA6C,CAAC;aACpD,QAAQ,EAAE,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,wBAAwB;QACpC,OAAO,MAAM,IAAI,CAAC,eAAe;aAC9B,kBAAkB,CAAC,OAAO,CAAC;aAC3B,SAAS,CAAC,uBAAuB,EAAE,eAAe,CAAC;aACnD,KAAK,CAAC,wCAAwC,EAAE,EAAE,QAAQ,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE,CAAC;aACpF,QAAQ,EAAE,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,2BAA2B,CAAC,IAAY;QACpD,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;QAE9C,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YACrC,KAAK,EAAE,EAAE,YAAY,EAAE,IAAA,iBAAO,EAAC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,EAAE;YACvD,KAAK,EAAE,EAAE,YAAY,EAAE,MAAM,EAAE;YAC/B,IAAI,EAAE,EAAE;SACT,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,IAAY;QAC3C,sCAAsC;QACtC,mEAAmE;QACnE,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,KAAY;QAC3C,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;gBAClD,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;aAC1B,CAAC,CAAC;YAEH,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBAC3B,IAAI,KAAK,CAAC,0BAA0B,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC5C,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC;oBACzB,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACvC,MAAM,CAAC,iCAAiC;gBAC1C,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE;gBACzD,OAAO,EAAE,KAAK,CAAC,EAAE;gBACjB,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,iBAAiB,CAAC,QAAe,EAAE,OAAY;QACrD,MAAM,OAAO,GAAwB,EAAE,CAAC;QAExC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACjC,IAAI,QAAQ,CAAC,GAAG,CAAC,KAAK,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;gBACnC,OAAO,CAAC,GAAG,CAAC,GAAG;oBACb,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC;oBACnB,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC;iBACjB,CAAC;YACJ,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC9B,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QACzD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YACpD,KAAK,EAAE;gBACL,QAAQ,EAAE,IAAA,iBAAO,EAAC,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,UAAU,CAAC;gBACrD,MAAM,EAAE,QAAQ;aACjB;SACF,CAAC,CAAC;QAEH,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,MAAM,IAAI,CAAC,mBAAmB,CAAC,4BAA4B,CAAC,aAAa,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,wBAAwB;QACpC,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,eAAe;aACnD,kBAAkB,CAAC,OAAO,CAAC;aAC3B,KAAK,CAAC,8FAA8F,CAAC;aACrG,QAAQ,CAAC,wBAAwB,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;aACxD,OAAO,EAAE,CAAC;QAEb,8BAA8B;QAC9B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;YAClD,KAAK,EAAE,mBAAmB,CAAC,MAAM;SAClC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,uBAAuB;QACnC,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YACzD,KAAK,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;SACzB,CAAC,CAAC;QAEH,KAAK,MAAM,KAAK,IAAI,kBAAkB,EAAE,CAAC;YACvC,MAAM,IAAI,CAAC,0BAA0B,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,yBAAyB;QACrC,sCAAsC;QACtC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;QAEtD,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACjE,KAAK,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;YACnC,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,OAAe;QACpD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YAC7C,KAAK,EAAE,EAAE,OAAO,EAAE;SACnB,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG;YACjB,WAAW,EAAE,MAAM,CAAC,MAAM;YAC1B,YAAY,EAAE,EAAE;YAChB,cAAc,EAAE,EAAE;YAClB,mBAAmB,EAAE,EAAE;SACxB,CAAC;QAEF,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACrB,gBAAgB;YAChB,UAAU,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAErF,kBAAkB;YAClB,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAE7F,uBAAuB;YACvB,UAAU,CAAC,mBAAmB,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,mBAAmB,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACnH,CAAC,CAAC,CAAC;QAEH,OAAO,UAAU,CAAC;IACpB,CAAC;CACF,CAAA;AAnxBY,sDAAqB;AAyiB1B;IADL,IAAA,eAAI,EAAC,yBAAc,CAAC,UAAU,CAAC;;;wDACN,OAAO,oBAAP,OAAO;6DAsBhC;gCA/jBU,qBAAqB;IADjC,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,oBAAK,CAAC,CAAA;IAEvB,WAAA,IAAA,0BAAgB,EAAC,+BAAU,CAAC,CAAA;IAE5B,WAAA,IAAA,0BAAgB,EAAC,+CAAkB,CAAC,CAAA;IAEpC,WAAA,IAAA,0BAAgB,EAAC,+CAAkB,CAAC,CAAA;IAEpC,WAAA,IAAA,0BAAgB,EAAC,6CAAiB,CAAC,CAAA;yDAPF,oBAAU,oBAAV,oBAAU,oDAEL,oBAAU,oBAAV,oBAAU,oDAEP,oBAAU,oBAAV,oBAAU,oDAEV,oBAAU,oBAAV,oBAAU,oDAEX,oBAAU,oBAAV,oBAAU,oDACnB,8BAAa,oBAAb,8BAAa,oDACd,4BAAY,oBAAZ,4BAAY,oDACL,0CAAmB,oBAAnB,0CAAmB,oDACjB,+CAAqB,oBAArB,+CAAqB,oDAChB,yDAA0B,oBAA1B,yDAA0B;GAlB9D,qBAAqB,CAmxBjC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\asset-management\\application\\services\\asset-inventory.service.ts"], "sourcesContent": ["import { Injectable, Logger, NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository, FindManyOptions, In, Between } from 'typeorm';\r\nimport { <PERSON>ron, CronExpression } from '@nestjs/schedule';\r\nimport { Asset } from '../../domain/entities/asset.entity';\r\nimport { AssetGroup } from '../../domain/entities/asset-group.entity';\r\nimport { AssetConfiguration } from '../../domain/entities/asset-configuration.entity';\r\nimport { AssetVulnerability } from '../../domain/entities/asset-vulnerability.entity';\r\nimport { AssetRelationship } from '../../domain/entities/asset-relationship.entity';\r\nimport { LoggerService } from '../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../infrastructure/logging/audit/audit.service';\r\nimport { NotificationService } from '../../../../infrastructure/notification/notification.service';\r\nimport { AssetDiscoveryService } from './asset-discovery.service';\r\nimport { AssetClassificationService } from './asset-classification.service';\r\n\r\n/**\r\n * Asset Inventory service\r\n * Handles comprehensive asset inventory management, tracking, and lifecycle operations\r\n */\r\n@Injectable()\r\nexport class AssetInventoryService {\r\n  private readonly logger = new Logger(AssetInventoryService.name);\r\n\r\n  constructor(\r\n    @InjectRepository(Asset)\r\n    private readonly assetRepository: Repository<Asset>,\r\n    @InjectRepository(AssetGroup)\r\n    private readonly assetGroupRepository: Repository<AssetGroup>,\r\n    @InjectRepository(AssetConfiguration)\r\n    private readonly configurationRepository: Repository<AssetConfiguration>,\r\n    @InjectRepository(AssetVulnerability)\r\n    private readonly vulnerabilityRepository: Repository<AssetVulnerability>,\r\n    @InjectRepository(AssetRelationship)\r\n    private readonly relationshipRepository: Repository<AssetRelationship>,\r\n    private readonly loggerService: LoggerService,\r\n    private readonly auditService: AuditService,\r\n    private readonly notificationService: NotificationService,\r\n    private readonly assetDiscoveryService: AssetDiscoveryService,\r\n    private readonly assetClassificationService: AssetClassificationService,\r\n  ) {}\r\n\r\n  /**\r\n   * Get asset inventory dashboard\r\n   * @returns Comprehensive inventory dashboard data\r\n   */\r\n  async getInventoryDashboard(): Promise<any> {\r\n    try {\r\n      this.logger.debug('Generating asset inventory dashboard');\r\n\r\n      const [\r\n        totalAssets,\r\n        assetsByType,\r\n        assetsByStatus,\r\n        assetsByCriticality,\r\n        assetsByEnvironment,\r\n        onlineAssets,\r\n        assetsWithAgents,\r\n        vulnerableAssets,\r\n        recentlyDiscovered,\r\n        inventoryTrends,\r\n      ] = await Promise.all([\r\n        this.getTotalAssetsCount(),\r\n        this.getAssetsByType(),\r\n        this.getAssetsByStatus(),\r\n        this.getAssetsByCriticality(),\r\n        this.getAssetsByEnvironment(),\r\n        this.getOnlineAssetsCount(),\r\n        this.getAssetsWithAgentsCount(),\r\n        this.getVulnerableAssetsCount(),\r\n        this.getRecentlyDiscoveredAssets(7), // Last 7 days\r\n        this.getInventoryTrends(30), // Last 30 days\r\n      ]);\r\n\r\n      const dashboard = {\r\n        summary: {\r\n          totalAssets,\r\n          onlineAssets,\r\n          offlineAssets: totalAssets - onlineAssets,\r\n          assetsWithAgents,\r\n          vulnerableAssets,\r\n          agentCoverage: totalAssets > 0 ? Math.round((assetsWithAgents / totalAssets) * 100) : 0,\r\n          vulnerabilityRate: totalAssets > 0 ? Math.round((vulnerableAssets / totalAssets) * 100) : 0,\r\n        },\r\n        breakdown: {\r\n          byType: assetsByType,\r\n          byStatus: assetsByStatus,\r\n          byCriticality: assetsByCriticality,\r\n          byEnvironment: assetsByEnvironment,\r\n        },\r\n        recentActivity: {\r\n          recentlyDiscovered,\r\n          trends: inventoryTrends,\r\n        },\r\n        timestamp: new Date().toISOString(),\r\n      };\r\n\r\n      this.logger.log('Asset inventory dashboard generated successfully', {\r\n        totalAssets,\r\n        onlineAssets,\r\n        vulnerableAssets,\r\n      });\r\n\r\n      return dashboard;\r\n    } catch (error) {\r\n      this.logger.error('Failed to generate asset inventory dashboard', {\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Search assets with advanced filtering\r\n   * @param criteria Search criteria\r\n   * @returns Paginated asset results\r\n   */\r\n  async searchAssets(criteria: {\r\n    page?: number;\r\n    limit?: number;\r\n    types?: string[];\r\n    statuses?: string[];\r\n    criticalities?: string[];\r\n    environments?: string[];\r\n    locations?: string[];\r\n    groupIds?: string[];\r\n    tags?: string[];\r\n    hasAgent?: boolean;\r\n    isOnline?: boolean;\r\n    hasVulnerabilities?: boolean;\r\n    searchText?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n  }): Promise<{ assets: Asset[]; total: number; page: number; totalPages: number }> {\r\n    try {\r\n      const {\r\n        page = 1,\r\n        limit = 50,\r\n        types,\r\n        statuses,\r\n        criticalities,\r\n        environments,\r\n        locations,\r\n        groupIds,\r\n        tags,\r\n        hasAgent,\r\n        isOnline,\r\n        hasVulnerabilities,\r\n        searchText,\r\n        sortBy = 'name',\r\n        sortOrder = 'ASC',\r\n      } = criteria;\r\n\r\n      const queryBuilder = this.assetRepository.createQueryBuilder('asset')\r\n        .leftJoinAndSelect('asset.group', 'group')\r\n        .leftJoinAndSelect('asset.vulnerabilities', 'vulnerabilities');\r\n\r\n      // Apply filters\r\n      if (types && types.length > 0) {\r\n        queryBuilder.andWhere('asset.type IN (:...types)', { types });\r\n      }\r\n\r\n      if (statuses && statuses.length > 0) {\r\n        queryBuilder.andWhere('asset.status IN (:...statuses)', { statuses });\r\n      }\r\n\r\n      if (criticalities && criticalities.length > 0) {\r\n        queryBuilder.andWhere('asset.criticality IN (:...criticalities)', { criticalities });\r\n      }\r\n\r\n      if (environments && environments.length > 0) {\r\n        queryBuilder.andWhere('asset.environment IN (:...environments)', { environments });\r\n      }\r\n\r\n      if (locations && locations.length > 0) {\r\n        queryBuilder.andWhere('asset.location IN (:...locations)', { locations });\r\n      }\r\n\r\n      if (groupIds && groupIds.length > 0) {\r\n        queryBuilder.andWhere('asset.groupId IN (:...groupIds)', { groupIds });\r\n      }\r\n\r\n      if (tags && tags.length > 0) {\r\n        queryBuilder.andWhere('asset.tags && :tags', { tags });\r\n      }\r\n\r\n      if (hasAgent !== undefined) {\r\n        if (hasAgent) {\r\n          queryBuilder.andWhere(\"asset.discovery->>'agentInstalled' = 'true'\");\r\n        } else {\r\n          queryBuilder.andWhere(\"(asset.discovery->>'agentInstalled' IS NULL OR asset.discovery->>'agentInstalled' = 'false')\");\r\n        }\r\n      }\r\n\r\n      if (isOnline !== undefined) {\r\n        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);\r\n        if (isOnline) {\r\n          queryBuilder.andWhere('asset.lastSeen > :fiveMinutesAgo', { fiveMinutesAgo });\r\n        } else {\r\n          queryBuilder.andWhere('(asset.lastSeen IS NULL OR asset.lastSeen <= :fiveMinutesAgo)', { fiveMinutesAgo });\r\n        }\r\n      }\r\n\r\n      if (hasVulnerabilities !== undefined) {\r\n        if (hasVulnerabilities) {\r\n          queryBuilder.andWhere('vulnerabilities.id IS NOT NULL');\r\n        } else {\r\n          queryBuilder.andWhere('vulnerabilities.id IS NULL');\r\n        }\r\n      }\r\n\r\n      if (searchText) {\r\n        queryBuilder.andWhere(\r\n          '(asset.name ILIKE :searchText OR asset.hostname ILIKE :searchText OR asset.ipAddress ILIKE :searchText OR asset.description ILIKE :searchText)',\r\n          { searchText: `%${searchText}%` },\r\n        );\r\n      }\r\n\r\n      // Apply sorting\r\n      queryBuilder.orderBy(`asset.${sortBy}`, sortOrder);\r\n\r\n      // Apply pagination\r\n      const offset = (page - 1) * limit;\r\n      queryBuilder.skip(offset).take(limit);\r\n\r\n      const [assets, total] = await queryBuilder.getManyAndCount();\r\n      const totalPages = Math.ceil(total / limit);\r\n\r\n      this.logger.debug('Assets searched successfully', {\r\n        total,\r\n        page,\r\n        limit,\r\n        totalPages,\r\n        filters: { types, statuses, criticalities, environments, hasAgent, isOnline },\r\n      });\r\n\r\n      return {\r\n        assets,\r\n        total,\r\n        page,\r\n        totalPages,\r\n      };\r\n    } catch (error) {\r\n      this.logger.error('Failed to search assets', {\r\n        error: error.message,\r\n        criteria,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create new asset\r\n   * @param assetData Asset data\r\n   * @param userId User creating the asset\r\n   * @returns Created asset\r\n   */\r\n  async createAsset(assetData: {\r\n    name: string;\r\n    description?: string;\r\n    type: string;\r\n    subType?: string;\r\n    criticality?: 'low' | 'medium' | 'high' | 'critical';\r\n    environment?: 'production' | 'staging' | 'development' | 'testing' | 'sandbox';\r\n    location?: string;\r\n    ipAddress?: string;\r\n    hostname?: string;\r\n    groupId?: string;\r\n    operatingSystem?: any;\r\n    hardware?: any;\r\n    software?: any;\r\n    cloudMetadata?: any;\r\n    networkInfo?: any;\r\n    ownership?: any;\r\n    tags?: string[];\r\n    customAttributes?: Record<string, any>;\r\n  }, userId: string): Promise<Asset> {\r\n    try {\r\n      this.logger.debug('Creating new asset', {\r\n        name: assetData.name,\r\n        type: assetData.type,\r\n        userId,\r\n      });\r\n\r\n      // Validate group if specified\r\n      if (assetData.groupId) {\r\n        const group = await this.assetGroupRepository.findOne({\r\n          where: { id: assetData.groupId, isActive: true },\r\n        });\r\n        if (!group) {\r\n          throw new NotFoundException('Asset group not found or inactive');\r\n        }\r\n      }\r\n\r\n      const asset = this.assetRepository.create({\r\n        ...assetData,\r\n        status: 'unknown',\r\n        discoveredAt: new Date(),\r\n        lastSeen: new Date(),\r\n        createdBy: userId,\r\n        ipAddresses: assetData.ipAddress ? [assetData.ipAddress] : [],\r\n        tags: assetData.tags || [],\r\n      });\r\n\r\n      const savedAsset = await this.assetRepository.save(asset);\r\n\r\n      // Auto-classify asset\r\n      await this.assetClassificationService.classifyAsset(savedAsset.id);\r\n\r\n      // Auto-assign to groups based on rules\r\n      await this.autoAssignToGroups(savedAsset);\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'create',\r\n        'asset',\r\n        savedAsset.id,\r\n        {\r\n          assetName: assetData.name,\r\n          assetType: assetData.type,\r\n          environment: assetData.environment,\r\n          criticality: assetData.criticality,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Asset created successfully', {\r\n        assetId: savedAsset.id,\r\n        assetName: assetData.name,\r\n        userId,\r\n      });\r\n\r\n      return savedAsset;\r\n    } catch (error) {\r\n      this.logger.error('Failed to create asset', {\r\n        error: error.message,\r\n        assetData,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update asset\r\n   * @param assetId Asset ID\r\n   * @param updates Asset updates\r\n   * @param userId User updating the asset\r\n   * @returns Updated asset\r\n   */\r\n  async updateAsset(assetId: string, updates: any, userId: string): Promise<Asset> {\r\n    try {\r\n      const asset = await this.assetRepository.findOne({\r\n        where: { id: assetId },\r\n        relations: ['group'],\r\n      });\r\n\r\n      if (!asset) {\r\n        throw new NotFoundException('Asset not found');\r\n      }\r\n\r\n      this.logger.debug('Updating asset', {\r\n        assetId,\r\n        updates: Object.keys(updates),\r\n        userId,\r\n      });\r\n\r\n      // Validate group if being updated\r\n      if (updates.groupId && updates.groupId !== asset.groupId) {\r\n        const group = await this.assetGroupRepository.findOne({\r\n          where: { id: updates.groupId, isActive: true },\r\n        });\r\n        if (!group) {\r\n          throw new NotFoundException('Asset group not found or inactive');\r\n        }\r\n      }\r\n\r\n      // Track changes for audit\r\n      const changes = this.trackAssetChanges(asset, updates);\r\n\r\n      Object.assign(asset, updates);\r\n      asset.updatedBy = userId;\r\n\r\n      const updatedAsset = await this.assetRepository.save(asset);\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'update',\r\n        'asset',\r\n        assetId,\r\n        {\r\n          assetName: asset.name,\r\n          changes,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Asset updated successfully', {\r\n        assetId,\r\n        userId,\r\n        changesCount: Object.keys(changes).length,\r\n      });\r\n\r\n      return updatedAsset;\r\n    } catch (error) {\r\n      this.logger.error('Failed to update asset', {\r\n        assetId,\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Delete asset\r\n   * @param assetId Asset ID\r\n   * @param userId User deleting the asset\r\n   */\r\n  async deleteAsset(assetId: string, userId: string): Promise<void> {\r\n    try {\r\n      const asset = await this.assetRepository.findOne({\r\n        where: { id: assetId },\r\n      });\r\n\r\n      if (!asset) {\r\n        throw new NotFoundException('Asset not found');\r\n      }\r\n\r\n      this.logger.debug('Deleting asset', {\r\n        assetId,\r\n        assetName: asset.name,\r\n        userId,\r\n      });\r\n\r\n      await this.assetRepository.remove(asset);\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'delete',\r\n        'asset',\r\n        assetId,\r\n        {\r\n          assetName: asset.name,\r\n          assetType: asset.type,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Asset deleted successfully', {\r\n        assetId,\r\n        assetName: asset.name,\r\n        userId,\r\n      });\r\n    } catch (error) {\r\n      this.logger.error('Failed to delete asset', {\r\n        assetId,\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get asset details with relationships\r\n   * @param assetId Asset ID\r\n   * @returns Asset with full details\r\n   */\r\n  async getAssetDetails(assetId: string): Promise<Asset> {\r\n    try {\r\n      const asset = await this.assetRepository.findOne({\r\n        where: { id: assetId },\r\n        relations: [\r\n          'group',\r\n          'configurations',\r\n          'vulnerabilities',\r\n          'sourceRelationships',\r\n          'targetRelationships',\r\n          'dependencies',\r\n          'relatedAssets',\r\n        ],\r\n      });\r\n\r\n      if (!asset) {\r\n        throw new NotFoundException('Asset not found');\r\n      }\r\n\r\n      this.logger.debug('Asset details retrieved', {\r\n        assetId,\r\n        assetName: asset.name,\r\n      });\r\n\r\n      return asset;\r\n    } catch (error) {\r\n      this.logger.error('Failed to get asset details', {\r\n        assetId,\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update asset last seen timestamp\r\n   * @param assetId Asset ID\r\n   */\r\n  async updateLastSeen(assetId: string): Promise<void> {\r\n    try {\r\n      await this.assetRepository.update(assetId, {\r\n        lastSeen: new Date(),\r\n      });\r\n\r\n      this.logger.debug('Asset last seen updated', { assetId });\r\n    } catch (error) {\r\n      this.logger.error('Failed to update asset last seen', {\r\n        assetId,\r\n        error: error.message,\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Bulk update asset statuses\r\n   * @param assetIds Asset IDs\r\n   * @param status New status\r\n   * @param userId User performing the update\r\n   */\r\n  async bulkUpdateStatus(\r\n    assetIds: string[],\r\n    status: 'active' | 'inactive' | 'maintenance' | 'decommissioned' | 'unknown',\r\n    userId: string,\r\n  ): Promise<void> {\r\n    try {\r\n      this.logger.debug('Bulk updating asset statuses', {\r\n        assetCount: assetIds.length,\r\n        status,\r\n        userId,\r\n      });\r\n\r\n      await this.assetRepository.update(\r\n        { id: In(assetIds) },\r\n        { status, updatedBy: userId },\r\n      );\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'bulk_update',\r\n        'asset',\r\n        null,\r\n        {\r\n          assetIds,\r\n          status,\r\n          assetCount: assetIds.length,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Asset statuses updated successfully', {\r\n        assetCount: assetIds.length,\r\n        status,\r\n        userId,\r\n      });\r\n    } catch (error) {\r\n      this.logger.error('Failed to bulk update asset statuses', {\r\n        error: error.message,\r\n        assetIds,\r\n        status,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Monitor asset inventory for changes and issues\r\n   */\r\n  @Cron(CronExpression.EVERY_HOUR)\r\n  async monitorInventory(): Promise<void> {\r\n    try {\r\n      this.logger.debug('Starting inventory monitoring');\r\n\r\n      // Check for offline assets\r\n      await this.checkOfflineAssets();\r\n\r\n      // Check for assets without agents\r\n      await this.checkAssetsWithoutAgents();\r\n\r\n      // Check for unclassified assets\r\n      await this.checkUnclassifiedAssets();\r\n\r\n      // Update inventory statistics\r\n      await this.updateInventoryStatistics();\r\n\r\n      this.logger.log('Inventory monitoring completed');\r\n    } catch (error) {\r\n      this.logger.error('Failed to complete inventory monitoring', {\r\n        error: error.message,\r\n      });\r\n    }\r\n  }\r\n\r\n  // Private helper methods\r\n\r\n  private async getTotalAssetsCount(): Promise<number> {\r\n    return await this.assetRepository.count();\r\n  }\r\n\r\n  private async getAssetsByType(): Promise<Record<string, number>> {\r\n    const result = await this.assetRepository\r\n      .createQueryBuilder('asset')\r\n      .select('asset.type', 'type')\r\n      .addSelect('COUNT(*)', 'count')\r\n      .groupBy('asset.type')\r\n      .getRawMany();\r\n\r\n    return result.reduce((acc, item) => {\r\n      acc[item.type] = parseInt(item.count);\r\n      return acc;\r\n    }, {});\r\n  }\r\n\r\n  private async getAssetsByStatus(): Promise<Record<string, number>> {\r\n    const result = await this.assetRepository\r\n      .createQueryBuilder('asset')\r\n      .select('asset.status', 'status')\r\n      .addSelect('COUNT(*)', 'count')\r\n      .groupBy('asset.status')\r\n      .getRawMany();\r\n\r\n    return result.reduce((acc, item) => {\r\n      acc[item.status] = parseInt(item.count);\r\n      return acc;\r\n    }, {});\r\n  }\r\n\r\n  private async getAssetsByCriticality(): Promise<Record<string, number>> {\r\n    const result = await this.assetRepository\r\n      .createQueryBuilder('asset')\r\n      .select('asset.criticality', 'criticality')\r\n      .addSelect('COUNT(*)', 'count')\r\n      .groupBy('asset.criticality')\r\n      .getRawMany();\r\n\r\n    return result.reduce((acc, item) => {\r\n      acc[item.criticality] = parseInt(item.count);\r\n      return acc;\r\n    }, {});\r\n  }\r\n\r\n  private async getAssetsByEnvironment(): Promise<Record<string, number>> {\r\n    const result = await this.assetRepository\r\n      .createQueryBuilder('asset')\r\n      .select('asset.environment', 'environment')\r\n      .addSelect('COUNT(*)', 'count')\r\n      .groupBy('asset.environment')\r\n      .getRawMany();\r\n\r\n    return result.reduce((acc, item) => {\r\n      acc[item.environment] = parseInt(item.count);\r\n      return acc;\r\n    }, {});\r\n  }\r\n\r\n  private async getOnlineAssetsCount(): Promise<number> {\r\n    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);\r\n    return await this.assetRepository.count({\r\n      where: { lastSeen: Between(fiveMinutesAgo, new Date()) },\r\n    });\r\n  }\r\n\r\n  private async getAssetsWithAgentsCount(): Promise<number> {\r\n    return await this.assetRepository\r\n      .createQueryBuilder('asset')\r\n      .where(\"asset.discovery->>'agentInstalled' = 'true'\")\r\n      .getCount();\r\n  }\r\n\r\n  private async getVulnerableAssetsCount(): Promise<number> {\r\n    return await this.assetRepository\r\n      .createQueryBuilder('asset')\r\n      .innerJoin('asset.vulnerabilities', 'vulnerability')\r\n      .where('vulnerability.status IN (:...statuses)', { statuses: ['open', 'confirmed'] })\r\n      .getCount();\r\n  }\r\n\r\n  private async getRecentlyDiscoveredAssets(days: number): Promise<Asset[]> {\r\n    const startDate = new Date();\r\n    startDate.setDate(startDate.getDate() - days);\r\n\r\n    return await this.assetRepository.find({\r\n      where: { discoveredAt: Between(startDate, new Date()) },\r\n      order: { discoveredAt: 'DESC' },\r\n      take: 10,\r\n    });\r\n  }\r\n\r\n  private async getInventoryTrends(days: number): Promise<any[]> {\r\n    // Implementation for inventory trends\r\n    // This would track asset discovery, status changes, etc. over time\r\n    return [];\r\n  }\r\n\r\n  private async autoAssignToGroups(asset: Asset): Promise<void> {\r\n    try {\r\n      const groups = await this.assetGroupRepository.find({\r\n        where: { isActive: true },\r\n      });\r\n\r\n      for (const group of groups) {\r\n        if (group.matchesAutoAssignmentRules(asset)) {\r\n          asset.groupId = group.id;\r\n          await this.assetRepository.save(asset);\r\n          break; // Assign to first matching group\r\n        }\r\n      }\r\n    } catch (error) {\r\n      this.logger.error('Failed to auto-assign asset to groups', {\r\n        assetId: asset.id,\r\n        error: error.message,\r\n      });\r\n    }\r\n  }\r\n\r\n  private trackAssetChanges(original: Asset, updates: any): Record<string, any> {\r\n    const changes: Record<string, any> = {};\r\n\r\n    Object.keys(updates).forEach(key => {\r\n      if (original[key] !== updates[key]) {\r\n        changes[key] = {\r\n          from: original[key],\r\n          to: updates[key],\r\n        };\r\n      }\r\n    });\r\n\r\n    return changes;\r\n  }\r\n\r\n  private async checkOfflineAssets(): Promise<void> {\r\n    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);\r\n    const offlineAssets = await this.assetRepository.find({\r\n      where: {\r\n        lastSeen: Between(new Date('1900-01-01'), oneHourAgo),\r\n        status: 'active',\r\n      },\r\n    });\r\n\r\n    if (offlineAssets.length > 0) {\r\n      await this.notificationService.sendAssetOfflineNotification(offlineAssets);\r\n    }\r\n  }\r\n\r\n  private async checkAssetsWithoutAgents(): Promise<void> {\r\n    const assetsWithoutAgents = await this.assetRepository\r\n      .createQueryBuilder('asset')\r\n      .where(\"(asset.discovery->>'agentInstalled' IS NULL OR asset.discovery->>'agentInstalled' = 'false')\")\r\n      .andWhere('asset.status = :status', { status: 'active' })\r\n      .getMany();\r\n\r\n    // Log for monitoring purposes\r\n    this.logger.debug('Assets without agents detected', {\r\n      count: assetsWithoutAgents.length,\r\n    });\r\n  }\r\n\r\n  private async checkUnclassifiedAssets(): Promise<void> {\r\n    const unclassifiedAssets = await this.assetRepository.find({\r\n      where: { type: 'other' },\r\n    });\r\n\r\n    for (const asset of unclassifiedAssets) {\r\n      await this.assetClassificationService.classifyAsset(asset.id);\r\n    }\r\n  }\r\n\r\n  private async updateInventoryStatistics(): Promise<void> {\r\n    // Update cached statistics for groups\r\n    const groups = await this.assetGroupRepository.find();\r\n\r\n    for (const group of groups) {\r\n      const statistics = await this.calculateGroupStatistics(group.id);\r\n      group.updateStatistics(statistics);\r\n      await this.assetGroupRepository.save(group);\r\n    }\r\n  }\r\n\r\n  private async calculateGroupStatistics(groupId: string): Promise<any> {\r\n    const assets = await this.assetRepository.find({\r\n      where: { groupId },\r\n    });\r\n\r\n    const statistics = {\r\n      totalAssets: assets.length,\r\n      assetsByType: {},\r\n      assetsByStatus: {},\r\n      assetsByCriticality: {},\r\n    };\r\n\r\n    assets.forEach(asset => {\r\n      // Count by type\r\n      statistics.assetsByType[asset.type] = (statistics.assetsByType[asset.type] || 0) + 1;\r\n      \r\n      // Count by status\r\n      statistics.assetsByStatus[asset.status] = (statistics.assetsByStatus[asset.status] || 0) + 1;\r\n      \r\n      // Count by criticality\r\n      statistics.assetsByCriticality[asset.criticality] = (statistics.assetsByCriticality[asset.criticality] || 0) + 1;\r\n    });\r\n\r\n    return statistics;\r\n  }\r\n}\r\n"], "version": 3}