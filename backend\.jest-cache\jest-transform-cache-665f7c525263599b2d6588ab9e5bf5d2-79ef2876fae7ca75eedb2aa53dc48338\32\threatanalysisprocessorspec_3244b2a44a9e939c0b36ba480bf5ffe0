c67d447d6e5d2386bcc08ffa52384be8
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const threat_analysis_processor_1 = require("../threat-analysis.processor");
const threat_intelligence_entity_1 = require("../../../domain/entities/threat-intelligence.entity");
const threat_actor_entity_1 = require("../../../domain/entities/threat-actor.entity");
const indicator_of_compromise_entity_1 = require("../../../domain/entities/indicator-of-compromise.entity");
const threat_intelligence_service_1 = require("../../../application/services/threat-intelligence.service");
const logger_service_1 = require("../../../../../infrastructure/logging/logger.service");
const notification_service_1 = require("../../../../../infrastructure/notification/notification.service");
describe('ThreatAnalysisProcessor', () => {
    let processor;
    let threatRepository;
    let actorRepository;
    let iocRepository;
    let threatIntelligenceService;
    let loggerService;
    let notificationService;
    const mockThreatIntelligence = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        title: 'Test APT Campaign',
        description: 'Test threat for attribution analysis',
        threatType: threat_intelligence_entity_1.ThreatType.APT,
        severity: threat_intelligence_entity_1.ThreatSeverity.HIGH,
        confidence: threat_intelligence_entity_1.ThreatConfidence.HIGH,
        firstSeen: new Date('2023-01-01'),
        lastSeen: new Date('2023-01-15'),
        targetedSectors: ['healthcare', 'financial'],
        targetedCountries: ['US', 'UK'],
        mitreAttack: {
            tactics: ['initial-access', 'persistence'],
            techniques: ['T1566.001', 'T1053.005'],
        },
        technicalDetails: {
            malwareFamily: 'Cobalt Strike',
            infrastructure: ['*************'],
        },
        observationCount: 5,
        riskScore: 7.5,
        isAttributed: false,
        indicators: [],
    };
    const mockThreatActor = {
        id: 'actor-123',
        name: 'APT29',
        aliases: ['Cozy Bear', 'The Dukes'],
        actorType: 'apt',
        sophisticationLevel: 'advanced',
        isActive: true,
        targetedSectors: ['healthcare', 'government'],
        targetedCountries: ['US', 'UK', 'DE'],
        attackPatterns: [
            { mitreId: 'T1566.001', name: 'Spearphishing Attachment' },
            { mitreId: 'T1053.005', name: 'Scheduled Task' },
        ],
        tools: [
            { name: 'Cobalt Strike', type: 'backdoor' },
        ],
    };
    beforeEach(async () => {
        const mockRepository = {
            findOne: jest.fn(),
            find: jest.fn(),
            save: jest.fn(),
            create: jest.fn(),
            remove: jest.fn(),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                threat_analysis_processor_1.ThreatAnalysisProcessor,
                {
                    provide: (0, typeorm_1.getRepositoryToken)(threat_intelligence_entity_1.ThreatIntelligence),
                    useValue: mockRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(threat_actor_entity_1.ThreatActor),
                    useValue: mockRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(indicator_of_compromise_entity_1.IndicatorOfCompromise),
                    useValue: mockRepository,
                },
                {
                    provide: threat_intelligence_service_1.ThreatIntelligenceService,
                    useValue: {
                        findRelatedThreats: jest.fn(),
                    },
                },
                {
                    provide: logger_service_1.LoggerService,
                    useValue: {
                        error: jest.fn(),
                        warn: jest.fn(),
                        log: jest.fn(),
                        debug: jest.fn(),
                    },
                },
                {
                    provide: notification_service_1.NotificationService,
                    useValue: {
                        sendUserNotification: jest.fn(),
                        sendRoleNotification: jest.fn(),
                    },
                },
            ],
        }).compile();
        processor = module.get(threat_analysis_processor_1.ThreatAnalysisProcessor);
        threatRepository = module.get((0, typeorm_1.getRepositoryToken)(threat_intelligence_entity_1.ThreatIntelligence));
        actorRepository = module.get((0, typeorm_1.getRepositoryToken)(threat_actor_entity_1.ThreatActor));
        iocRepository = module.get((0, typeorm_1.getRepositoryToken)(indicator_of_compromise_entity_1.IndicatorOfCompromise));
        threatIntelligenceService = module.get(threat_intelligence_service_1.ThreatIntelligenceService);
        loggerService = module.get(logger_service_1.LoggerService);
        notificationService = module.get(notification_service_1.NotificationService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('handleAttributionAnalysis', () => {
        it('should perform attribution analysis successfully', async () => {
            const jobData = {
                threatId: mockThreatIntelligence.id,
                analysisType: 'attribution',
                userId: 'user123',
            };
            const mockJob = {
                id: 'job123',
                data: jobData,
                progress: jest.fn(),
            };
            threatRepository.findOne.mockResolvedValue({
                ...mockThreatIntelligence,
                indicators: [],
            });
            actorRepository.find.mockResolvedValue([mockThreatActor]);
            const result = await processor.handleAttributionAnalysis(mockJob);
            expect(threatRepository.findOne).toHaveBeenCalledWith({
                where: { id: mockThreatIntelligence.id },
                relations: ['indicators', 'threatActor'],
            });
            expect(actorRepository.find).toHaveBeenCalledWith({
                where: { isActive: true },
            });
            expect(result).toEqual({
                threatId: mockThreatIntelligence.id,
                suggestedActors: expect.arrayContaining([
                    expect.objectContaining({
                        actorId: mockThreatActor.id,
                        actorName: mockThreatActor.name,
                        confidence: expect.any(Number),
                        reasons: expect.any(Array),
                    }),
                ]),
                confidence: expect.any(Number),
                analysisDate: expect.any(Date),
            });
            expect(mockJob.progress).toHaveBeenCalledWith(100);
        });
        it('should throw error when threat not found', async () => {
            const jobData = {
                threatId: 'nonexistent',
                analysisType: 'attribution',
                userId: 'user123',
            };
            const mockJob = {
                id: 'job123',
                data: jobData,
                progress: jest.fn(),
            };
            threatRepository.findOne.mockResolvedValue(null);
            await expect(processor.handleAttributionAnalysis(mockJob))
                .rejects.toThrow('Threat intelligence not found: nonexistent');
        });
        it('should update threat with attribution when confidence is high', async () => {
            const jobData = {
                threatId: mockThreatIntelligence.id,
                analysisType: 'attribution',
                userId: 'user123',
            };
            const mockJob = {
                id: 'job123',
                data: jobData,
                progress: jest.fn(),
            };
            const threat = {
                ...mockThreatIntelligence,
                indicators: [],
                threatActorId: null,
            };
            threatRepository.findOne.mockResolvedValue(threat);
            actorRepository.find.mockResolvedValue([mockThreatActor]);
            // Mock high confidence match
            jest.spyOn(processor, 'calculateActorMatch').mockReturnValue({
                confidence: 0.9,
                reasons: ['Targets similar sectors', 'Uses similar techniques'],
            });
            await processor.handleAttributionAnalysis(mockJob);
            expect(threatRepository.save).toHaveBeenCalledWith(expect.objectContaining({
                threatActorId: mockThreatActor.id,
                isAttributed: true,
                customAttributes: expect.objectContaining({
                    attributionAnalysis: expect.any(Object),
                    attributionDate: expect.any(Date),
                }),
            }));
        });
    });
    describe('handleThreatScoring', () => {
        it('should calculate and update threat score', async () => {
            const jobData = {
                threatId: mockThreatIntelligence.id,
                analysisType: 'scoring',
                userId: 'user123',
            };
            const mockJob = {
                id: 'job123',
                data: jobData,
                progress: jest.fn(),
            };
            const threat = {
                ...mockThreatIntelligence,
                riskScore: 6.0,
                indicators: [],
            };
            threatRepository.findOne.mockResolvedValue(threat);
            const result = await processor.handleThreatScoring(mockJob);
            expect(result).toEqual({
                threatId: mockThreatIntelligence.id,
                oldScore: 6.0,
                newScore: expect.any(Number),
                factors: expect.arrayContaining([
                    expect.objectContaining({
                        factor: expect.any(String),
                        weight: expect.any(Number),
                        value: expect.any(Number),
                        impact: expect.any(Number),
                    }),
                ]),
                analysisDate: expect.any(Date),
            });
            expect(threatRepository.save).toHaveBeenCalledWith(expect.objectContaining({
                riskScore: expect.any(Number),
            }));
            expect(mockJob.progress).toHaveBeenCalledWith(100);
        });
        it('should send notification for significant score changes', async () => {
            const jobData = {
                threatId: mockThreatIntelligence.id,
                analysisType: 'scoring',
                userId: 'user123',
            };
            const mockJob = {
                id: 'job123',
                data: jobData,
                progress: jest.fn(),
            };
            const threat = {
                ...mockThreatIntelligence,
                riskScore: 5.0, // Old score
                indicators: [],
            };
            threatRepository.findOne.mockResolvedValue(threat);
            // Mock scoring to return significantly higher score
            jest.spyOn(processor, 'calculateWeightedScore').mockReturnValue(8.5);
            await processor.handleThreatScoring(mockJob);
            expect(notificationService.sendUserNotification).toHaveBeenCalledWith({
                userId: 'user123',
                title: 'Threat Score Updated',
                message: expect.stringContaining('increased from 5.0 to 8.5'),
                type: 'warning',
                data: expect.objectContaining({
                    threatId: mockThreatIntelligence.id,
                    oldScore: 5.0,
                    newScore: 8.5,
                    change: 3.5,
                }),
            });
        });
    });
    describe('handleIOCEnrichment', () => {
        it('should enrich multiple IOCs successfully', async () => {
            const iocIds = ['ioc1', 'ioc2', 'ioc3'];
            const jobData = {
                iocIds,
                analysisType: 'enrichment',
                userId: 'user123',
            };
            const mockJob = {
                id: 'job123',
                data: jobData,
                progress: jest.fn(),
            };
            const mockIOC = {
                id: 'ioc1',
                iocType: 'ip_address',
                value: '*************',
                addEnrichment: jest.fn(),
            };
            iocRepository.findOne.mockResolvedValue(mockIOC);
            await processor.handleIOCEnrichment(mockJob);
            expect(iocRepository.findOne).toHaveBeenCalledTimes(3);
            expect(mockJob.progress).toHaveBeenCalledWith(33); // First IOC
            expect(mockJob.progress).toHaveBeenCalledWith(67); // Second IOC
            expect(mockJob.progress).toHaveBeenCalledWith(100); // Third IOC
        });
        it('should handle IOC enrichment errors gracefully', async () => {
            const iocIds = ['ioc1', 'ioc2'];
            const jobData = {
                iocIds,
                analysisType: 'enrichment',
                userId: 'user123',
            };
            const mockJob = {
                id: 'job123',
                data: jobData,
                progress: jest.fn(),
            };
            iocRepository.findOne
                .mockResolvedValueOnce({ id: 'ioc1' })
                .mockRejectedValueOnce(new Error('Database error'));
            await processor.handleIOCEnrichment(mockJob);
            expect(loggerService.warn).toHaveBeenCalledWith('Failed to enrich IOC', expect.objectContaining({
                iocId: 'ioc2',
                error: 'Database error',
            }));
            expect(mockJob.progress).toHaveBeenCalledWith(100);
        });
    });
    describe('handleThreatCorrelation', () => {
        it('should perform threat correlation analysis', async () => {
            const jobData = {
                threatId: mockThreatIntelligence.id,
                analysisType: 'correlation',
                userId: 'user123',
            };
            const mockJob = {
                id: 'job123',
                data: jobData,
                progress: jest.fn(),
            };
            const mockCorrelations = [
                {
                    relatedThreats: [mockThreatIntelligence],
                    correlationScore: 0.8,
                    correlationType: 'actor',
                },
            ];
            threatIntelligenceService.findRelatedThreats.mockResolvedValue(mockCorrelations);
            await processor.handleThreatCorrelation(mockJob);
            expect(threatIntelligenceService.findRelatedThreats).toHaveBeenCalledWith({
                threatIntelligenceId: mockThreatIntelligence.id,
                correlationTypes: ['actor', 'campaign', 'indicators', 'techniques'],
                timeWindow: 90,
                confidenceThreshold: 0.6,
            });
            expect(mockJob.progress).toHaveBeenCalledWith(100);
        });
    });
    describe('error handling', () => {
        it('should handle job failures gracefully', async () => {
            const jobData = {
                threatId: 'invalid-id',
                analysisType: 'attribution',
                userId: 'user123',
            };
            const mockJob = {
                id: 'job123',
                data: jobData,
                progress: jest.fn(),
            };
            threatRepository.findOne.mockRejectedValue(new Error('Database connection failed'));
            await expect(processor.handleAttributionAnalysis(mockJob))
                .rejects.toThrow('Database connection failed');
            expect(loggerService.error).toHaveBeenCalledWith('Attribution analysis failed', expect.objectContaining({
                jobId: 'job123',
                threatId: 'invalid-id',
                error: 'Database connection failed',
            }));
        });
    });
    describe('private helper methods', () => {
        it('should calculate actor match correctly', async () => {
            const factors = {
                targetedSectors: ['healthcare', 'financial'],
                techniques: ['T1566.001', 'T1053.005'],
                targetedCountries: ['US', 'UK'],
                malwareFamily: 'Cobalt Strike',
            };
            const match = processor.calculateActorMatch(mockThreatActor, factors);
            expect(match).toEqual({
                confidence: expect.any(Number),
                reasons: expect.arrayContaining([
                    expect.stringContaining('similar sectors'),
                    expect.stringContaining('similar techniques'),
                    expect.stringContaining('similar countries'),
                    expect.stringContaining('similar malware'),
                ]),
            });
            expect(match.confidence).toBeGreaterThan(0);
            expect(match.confidence).toBeLessThanOrEqual(1);
        });
        it('should calculate scoring factors correctly', async () => {
            const threat = {
                ...mockThreatIntelligence,
                severity: threat_intelligence_entity_1.ThreatSeverity.CRITICAL,
                confidence: threat_intelligence_entity_1.ThreatConfidence.HIGH,
                isAttributed: true,
                observationCount: 10,
                lastSeen: new Date(),
                indicators: [{ id: 'ioc1' }, { id: 'ioc2' }],
                targetedSectors: ['healthcare'],
            };
            const factors = await processor.calculateScoringFactors(threat);
            expect(factors).toEqual(expect.arrayContaining([
                expect.objectContaining({
                    factor: 'severity',
                    weight: 0.25,
                    value: 10, // Critical = 10
                    impact: 2.5, // 10 * 0.25
                }),
                expect.objectContaining({
                    factor: 'confidence',
                    weight: 0.2,
                    value: 10, // High = 10
                    impact: 2.0, // 10 * 0.2
                }),
                expect.objectContaining({
                    factor: 'attribution',
                    weight: 0.15,
                    value: 8, // Attributed = 8
                    impact: 1.2, // 8 * 0.15
                }),
            ]));
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************