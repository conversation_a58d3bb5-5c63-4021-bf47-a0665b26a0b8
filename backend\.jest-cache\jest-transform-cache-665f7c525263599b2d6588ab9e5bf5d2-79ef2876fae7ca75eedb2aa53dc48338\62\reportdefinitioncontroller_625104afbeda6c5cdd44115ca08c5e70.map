{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\controllers\\report-definition.controller.ts", "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAewB;AACxB,6CAQyB;AACzB,uFAAkF;AAClF,iFAA6E;AAC7E,6FAAgF;AAChF,2GAA6F;AAC7F,8FAA0F;AAC1F,8FAA0F;AAC1F,qFAAgF;AAChF,sFAAgF;AAChF,sFAAgF;AAChF,0FAAoF;AACpF,oGAA6F;AAC7F,8FAAyF;AACzF,oFAA8E;AAE9E;;;;;;;;;;GAUG;AAMI,IAAM,0BAA0B,GAAhC,MAAM,0BAA0B;IACrC,YACmB,uBAAgD;QAAhD,4BAAuB,GAAvB,uBAAuB,CAAyB;IAChE,CAAC;IAEJ;;OAEG;IA+HG,AAAN,KAAK,CAAC,sBAAsB,CACJ,SAAoC,EAC3C,IAAS;QAExB,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,sBAAsB,CAChF,SAAS,EACT,IAAI,CAAC,EAAE,CACR,CAAC;QAEF,OAAO,IAAI,4DAA2B,CAAC,gBAAgB,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IA8EG,AAAN,KAAK,CAAC,oBAAoB,CACT,IAAa,EACZ,KAAc,EACT,UAAmB,EACrB,QAAiB,EAClB,OAAgB,EACb,UAAoB,EAC1B,IAAe,EACb,MAAe,EACjB,IAAS;QAExB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,oBAAoB,CACpE;YACE,IAAI;YACJ,KAAK;YACL,UAAU;YACV,QAAQ;YACR,OAAO;YACP,UAAU;YACV,IAAI;YACJ,MAAM;SACP,EACD,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,KAAK,CACX,CAAC;QAEF,OAAO,IAAI,qEAA+B,CAAC,MAAM,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IA4BG,AAAN,KAAK,CAAC,uBAAuB,CACC,EAAU,EACvB,IAAS;QAExB,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,uBAAuB,CACjF,EAAE,EACF,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,KAAK,CACX,CAAC;QAEF,OAAO,IAAI,4DAA2B,CAAC,gBAAgB,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IAkCG,AAAN,KAAK,CAAC,sBAAsB,CACE,EAAU,EAChB,SAAoC,EAC3C,IAAS;QAExB,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,sBAAsB,CAChF,EAAE,EACF,SAAS,EACT,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,KAAK,CACX,CAAC;QAEF,OAAO,IAAI,4DAA2B,CAAC,gBAAgB,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IA8BG,AAAN,KAAK,CAAC,sBAAsB,CACE,EAAU,EACvB,IAAS;QAExB,MAAM,IAAI,CAAC,uBAAuB,CAAC,sBAAsB,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IACrF,CAAC;IAED;;OAEG;IAkDG,AAAN,KAAK,CAAC,qBAAqB,CACW,UAAkB,EAChC,QAAkC,EACzC,IAAS;QAExB,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,qBAAqB,CAC/E,UAAU,EACV,QAAQ,CAAC,IAAI,EACb,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,KAAK,CACX,CAAC;QAEF,OAAO,IAAI,4DAA2B,CAAC,gBAAgB,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IA2BG,AAAN,KAAK,CAAC,8BAA8B,CACN,EAAU,EACvB,IAAS;QAExB,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,8BAA8B,CACvF,EAAE,EACF,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,KAAK,CACX,CAAC;QAEF,OAAO,IAAI,iEAA8B,CAAC,eAAe,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IAyBG,AAAN,KAAK,CAAC,kBAAkB,CACH,QAAiB,EACf,UAAmB,EACzB,IAAS;QAExB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,oBAAoB,CACpE;YACE,UAAU,EAAE,IAAI;YAChB,QAAQ;YACR,UAAU;YACV,IAAI,EAAE,CAAC;YACP,KAAK,EAAE,GAAG;SACX,EACD,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,KAAK,CACX,CAAC;QAEF,OAAO,IAAI,qEAA+B,CAAC,MAAM,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IAiDG,AAAN,KAAK,CAAC,2BAA2B,CACT,SAAoC;QAY1D,uDAAuD;QACvD,2CAA2C;QAC3C,OAAO;YACL,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,EAAE;YACZ,eAAe,EAAE;gBACf,WAAW,EAAE,CAAC,uCAAuC,CAAC;gBACtD,QAAQ,EAAE,CAAC,sCAAsC,CAAC;gBAClD,SAAS,EAAE,CAAC,6BAA6B,CAAC;gBAC1C,UAAU,EAAE,CAAC,mDAAmD,CAAC;aAClE;SACF,CAAC;IACJ,CAAC;CACF,CAAA;AA9mBY,gEAA0B;AAsI/B;IA9HL,IAAA,aAAI,GAAE;IACN,IAAA,uBAAK,EAAC,kBAAkB,EAAE,kBAAkB,EAAE,SAAS,EAAE,gBAAgB,CAAC;IAC1E,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,gCAAgC;QACzC,WAAW,EAAE,+IAA+I;KAC7J,CAAC;IACD,IAAA,iBAAO,EAAC;QACP,IAAI,EAAE,wDAAyB;QAC/B,WAAW,EAAE,iCAAiC;QAC9C,QAAQ,EAAE;YACR,gBAAgB,EAAE;gBAChB,OAAO,EAAE,6BAA6B;gBACtC,KAAK,EAAE;oBACL,IAAI,EAAE,0BAA0B;oBAChC,WAAW,EAAE,wEAAwE;oBACrF,UAAU,EAAE,sBAAsB;oBAClC,QAAQ,EAAE,YAAY;oBACtB,QAAQ,EAAE,MAAM;oBAChB,gBAAgB,EAAE;wBAChB,OAAO,EAAE;4BACP;gCACE,IAAI,EAAE,YAAY;gCAClB,KAAK,EAAE,YAAY;gCACnB,OAAO,EAAE,EAAE,aAAa,EAAE,WAAW,EAAE;6BACxC;yBACF;qBACF;oBACD,mBAAmB,EAAE;wBACnB,SAAS,EAAE,KAAK;wBAChB,MAAM,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE;wBACrD,OAAO,EAAE;4BACP,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;4BACzC,KAAK,EAAE,OAAO;4BACd,QAAQ,EAAE,EAAE;4BACZ,UAAU,EAAE,OAAO;yBACpB;wBACD,MAAM,EAAE;4BACN;gCACE,IAAI,EAAE,kBAAkB;gCACxB,SAAS,EAAE,OAAO;gCAClB,WAAW,EAAE,KAAK;6BACnB;yBACF;wBACD,aAAa,EAAE;4BACb,SAAS,EAAE,IAAI;4BACf,SAAS,EAAE,IAAI;4BACf,OAAO,EAAE,IAAI;4BACb,MAAM,EAAE,IAAI;yBACb;qBACF;oBACD,YAAY,EAAE;wBACZ,OAAO,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC;wBAChC,aAAa,EAAE,KAAK;wBACpB,eAAe,EAAE;4BACf,KAAK,EAAE,IAAI;4BACX,QAAQ,EAAE,IAAI;4BACd,OAAO,EAAE,KAAK;4BACd,OAAO,EAAE,IAAI;yBACd;qBACF;oBACD,aAAa,EAAE;wBACb,UAAU,EAAE,QAAQ;wBACpB,YAAY,EAAE,CAAC,kBAAkB,EAAE,SAAS,CAAC;wBAC7C,YAAY,EAAE,EAAE;wBAChB,WAAW,EAAE;4BACX,IAAI,EAAE,IAAI;4BACV,IAAI,EAAE,IAAI;4BACV,MAAM,EAAE,KAAK;4BACb,MAAM,EAAE,IAAI;4BACZ,KAAK,EAAE,IAAI;yBACZ;qBACF;oBACD,iBAAiB,EAAE;wBACjB,OAAO,EAAE;4BACP,OAAO,EAAE,IAAI;4BACb,QAAQ,EAAE,OAAO;4BACjB,GAAG,EAAE,IAAI;4BACT,iBAAiB,EAAE,CAAC,yBAAyB,CAAC;yBAC/C;wBACD,YAAY,EAAE;4BACZ,SAAS,EAAE,KAAK;4BAChB,YAAY,EAAE,KAAK;4BACnB,kBAAkB,EAAE,IAAI;4BACxB,UAAU,EAAE,CAAC,cAAc,EAAE,YAAY,CAAC;yBAC3C;wBACD,UAAU,EAAE;4BACV,kBAAkB,EAAE,IAAI;4BACxB,eAAe,EAAE,IAAI;4BACrB,eAAe,EAAE;gCACf,aAAa,EAAE,KAAK;gCACpB,UAAU,EAAE,KAAK;gCACjB,SAAS,EAAE,CAAC;6BACb;yBACF;qBACF;oBACD,IAAI,EAAE,CAAC,KAAK,EAAE,YAAY,EAAE,WAAW,CAAC;oBACxC,oBAAoB,EAAE,CAAC,KAAK,CAAC;oBAC7B,kBAAkB,EAAE,cAAc;oBAClC,aAAa,EAAE,IAAI;iBACpB;aACF;SACF;KACF,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,OAAO;QAC1B,WAAW,EAAE,wCAAwC;QACrD,IAAI,EAAE,4DAA2B;KAClC,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,WAAW;QAC9B,WAAW,EAAE,yDAAyD;QACtE,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,UAAU,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE;gBAC5C,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,8EAA8E,EAAE;gBACpH,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,aAAa,EAAE;gBACjD,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE;gBAClD,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,+BAA+B,EAAE;aACnE;SACF;KACF,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,SAAS;QAC5B,WAAW,EAAE,uDAAuD;KACrE,CAAC;IAEC,WAAA,IAAA,aAAI,EAAC,uBAAc,CAAC,CAAA;IACpB,WAAA,IAAA,oCAAW,GAAE,CAAA;;yDADmB,wDAAyB,oBAAzB,wDAAyB;wDAEzD,OAAO,oBAAP,OAAO;wEAOT;AAkFK;IA7EL,IAAA,YAAG,GAAE;IACL,IAAA,uBAAK,EAAC,kBAAkB,EAAE,kBAAkB,EAAE,SAAS,EAAE,eAAe,CAAC;IACzE,IAAA,wBAAe,EAAC,oCAAgB,CAAC;IACjC,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,wBAAwB;QACjC,WAAW,EAAE,sHAAsH;KACpI,CAAC;IACD,IAAA,kBAAQ,EAAC;QACR,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE,uBAAuB;QACpC,OAAO,EAAE,CAAC;KACX,CAAC;IACD,IAAA,kBAAQ,EAAC;QACR,IAAI,EAAE,OAAO;QACb,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE,oCAAoC;QACjD,OAAO,EAAE,EAAE;KACZ,CAAC;IACD,IAAA,kBAAQ,EAAC;QACR,IAAI,EAAE,YAAY;QAClB,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE,uBAAuB;QACpC,IAAI,EAAE;YACJ,sBAAsB;YACtB,mBAAmB;YACnB,eAAe;YACf,eAAe;YACf,iBAAiB;YACjB,kBAAkB;YAClB,uBAAuB;YACvB,oBAAoB;YACpB,gBAAgB;YAChB,mBAAmB;YACnB,mBAAmB;YACnB,eAAe;SAChB;KACF,CAAC;IACD,IAAA,kBAAQ,EAAC;QACR,IAAI,EAAE,UAAU;QAChB,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE,oBAAoB;QACjC,IAAI,EAAE,CAAC,YAAY,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;KACzG,CAAC;IACD,IAAA,kBAAQ,EAAC;QACR,IAAI,EAAE,SAAS;QACf,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE,oBAAoB;KAClC,CAAC;IACD,IAAA,kBAAQ,EAAC;QACR,IAAI,EAAE,YAAY;QAClB,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,OAAO;QACb,WAAW,EAAE,2BAA2B;KACzC,CAAC;IACD,IAAA,kBAAQ,EAAC;QACR,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,CAAC,MAAM,CAAC;QACd,WAAW,EAAE,gBAAgB;KAC9B,CAAC;IACD,IAAA,kBAAQ,EAAC;QACR,IAAI,EAAE,QAAQ;QACd,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE,gCAAgC;KAC9C,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,2CAA2C;QACxD,IAAI,EAAE,qEAA+B;KACtC,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,MAAM,CAAC,CAAA;IACb,WAAA,IAAA,cAAK,EAAC,OAAO,CAAC,CAAA;IACd,WAAA,IAAA,cAAK,EAAC,YAAY,CAAC,CAAA;IACnB,WAAA,IAAA,cAAK,EAAC,UAAU,CAAC,CAAA;IACjB,WAAA,IAAA,cAAK,EAAC,SAAS,CAAC,CAAA;IAChB,WAAA,IAAA,cAAK,EAAC,YAAY,CAAC,CAAA;IACnB,WAAA,IAAA,cAAK,EAAC,MAAM,CAAC,CAAA;IACb,WAAA,IAAA,cAAK,EAAC,QAAQ,CAAC,CAAA;IACf,WAAA,IAAA,oCAAW,GAAE,CAAA;;;wDACb,OAAO,oBAAP,OAAO;sEAiBT;AAgCK;IA3BL,IAAA,YAAG,EAAC,KAAK,CAAC;IACV,IAAA,uBAAK,EAAC,kBAAkB,EAAE,kBAAkB,EAAE,SAAS,EAAE,eAAe,CAAC;IACzE,IAAA,wBAAe,EAAC,oCAAgB,CAAC;IACjC,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,6BAA6B;QACtC,WAAW,EAAE,kFAAkF;KAChG,CAAC;IACD,IAAA,kBAAQ,EAAC;QACR,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,MAAM;QACZ,MAAM,EAAE,MAAM;QACd,WAAW,EAAE,sBAAsB;QACnC,OAAO,EAAE,sCAAsC;KAChD,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,0CAA0C;QACvD,IAAI,EAAE,4DAA2B;KAClC,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,SAAS;QAC5B,WAAW,EAAE,6BAA6B;KAC3C,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,SAAS;QAC5B,WAAW,EAAE,yDAAyD;KACvE,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;IAC1B,WAAA,IAAA,oCAAW,GAAE,CAAA;;;wDACb,OAAO,oBAAP,OAAO;yEAQT;AAsCK;IAjCL,IAAA,YAAG,EAAC,KAAK,CAAC;IACV,IAAA,uBAAK,EAAC,kBAAkB,EAAE,kBAAkB,EAAE,SAAS,EAAE,gBAAgB,CAAC;IAC1E,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,0BAA0B;QACnC,WAAW,EAAE,uFAAuF;KACrG,CAAC;IACD,IAAA,kBAAQ,EAAC;QACR,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,MAAM;QACZ,MAAM,EAAE,MAAM;QACd,WAAW,EAAE,sBAAsB;KACpC,CAAC;IACD,IAAA,iBAAO,EAAC;QACP,IAAI,EAAE,wDAAyB;QAC/B,WAAW,EAAE,yCAAyC;KACvD,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,wCAAwC;QACrD,IAAI,EAAE,4DAA2B;KAClC,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,SAAS;QAC5B,WAAW,EAAE,6BAA6B;KAC3C,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,SAAS;QAC5B,WAAW,EAAE,2DAA2D;KACzE,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,WAAW;QAC9B,WAAW,EAAE,wDAAwD;KACtE,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;IAC1B,WAAA,IAAA,aAAI,EAAC,uBAAc,CAAC,CAAA;IACpB,WAAA,IAAA,oCAAW,GAAE,CAAA;;iEADmB,wDAAyB,oBAAzB,wDAAyB;wDAEzD,OAAO,oBAAP,OAAO;wEAST;AAkCK;IA7BL,IAAA,eAAM,EAAC,KAAK,CAAC;IACb,IAAA,uBAAK,EAAC,kBAAkB,EAAE,kBAAkB,EAAE,gBAAgB,CAAC;IAC/D,IAAA,iBAAQ,EAAC,mBAAU,CAAC,UAAU,CAAC;IAC/B,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,0BAA0B;QACnC,WAAW,EAAE,+GAA+G;KAC7H,CAAC;IACD,IAAA,kBAAQ,EAAC;QACR,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,MAAM;QACZ,MAAM,EAAE,MAAM;QACd,WAAW,EAAE,sBAAsB;KACpC,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,UAAU;QAC7B,WAAW,EAAE,wCAAwC;KACtD,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,SAAS;QAC5B,WAAW,EAAE,6BAA6B;KAC3C,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,SAAS;QAC5B,WAAW,EAAE,2DAA2D;KACzE,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,WAAW;QAC9B,WAAW,EAAE,6CAA6C;KAC3D,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;IAC1B,WAAA,IAAA,oCAAW,GAAE,CAAA;;;wDACb,OAAO,oBAAP,OAAO;wEAET;AAsDK;IAjDL,IAAA,aAAI,EAAC,mBAAmB,CAAC;IACzB,IAAA,uBAAK,EAAC,kBAAkB,EAAE,kBAAkB,EAAE,SAAS,EAAE,gBAAgB,CAAC;IAC1E,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,uCAAuC;QAChD,WAAW,EAAE,sGAAsG;KACpH,CAAC;IACD,IAAA,kBAAQ,EAAC;QACR,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE,MAAM;QACZ,MAAM,EAAE,MAAM;QACd,WAAW,EAAE,+BAA+B;KAC7C,CAAC;IACD,IAAA,iBAAO,EAAC;QACP,IAAI,EAAE,sDAAwB;QAC9B,WAAW,EAAE,qBAAqB;QAClC,QAAQ,EAAE;YACR,YAAY,EAAE;gBACZ,OAAO,EAAE,oBAAoB;gBAC7B,KAAK,EAAE;oBACL,IAAI,EAAE,0BAA0B;oBAChC,WAAW,EAAE,6CAA6C;oBAC1D,cAAc,EAAE;wBACd,gBAAgB,EAAE;4BAChB,OAAO,EAAE;gCACP;oCACE,IAAI,EAAE,YAAY;oCAClB,KAAK,EAAE,YAAY;oCACnB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;iCACvC;6BACF;yBACF;qBACF;iBACF;aACF;SACF;KACF,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,OAAO;QAC1B,WAAW,EAAE,uCAAuC;QACpD,IAAI,EAAE,4DAA2B;KAClC,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,SAAS;QAC5B,WAAW,EAAE,oBAAoB;KAClC,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,WAAW;QAC9B,WAAW,EAAE,wDAAwD;KACtE,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,YAAY,EAAE,sBAAa,CAAC,CAAA;IAClC,WAAA,IAAA,aAAI,EAAC,uBAAc,CAAC,CAAA;IACpB,WAAA,IAAA,oCAAW,GAAE,CAAA;;iEADkB,sDAAwB,oBAAxB,sDAAwB;wDAEvD,OAAO,oBAAP,OAAO;uEAST;AA+BK;IA1BL,IAAA,YAAG,EAAC,qBAAqB,CAAC;IAC1B,IAAA,uBAAK,EAAC,kBAAkB,EAAE,kBAAkB,EAAE,SAAS,EAAE,gBAAgB,CAAC;IAC1E,IAAA,wBAAe,EAAC,oCAAgB,CAAC;IACjC,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,kCAAkC;QAC3C,WAAW,EAAE,gIAAgI;KAC9I,CAAC;IACD,IAAA,kBAAQ,EAAC;QACR,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,MAAM;QACZ,MAAM,EAAE,MAAM;QACd,WAAW,EAAE,sBAAsB;KACpC,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,qDAAqD;QAClE,IAAI,EAAE,iEAA8B;KACrC,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,SAAS;QAC5B,WAAW,EAAE,6BAA6B;KAC3C,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,SAAS;QAC5B,WAAW,EAAE,kEAAkE;KAChF,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;IAC1B,WAAA,IAAA,oCAAW,GAAE,CAAA;;;wDACb,OAAO,oBAAP,OAAO;gFAQT;AA6BK;IAxBL,IAAA,YAAG,EAAC,gBAAgB,CAAC;IACrB,IAAA,uBAAK,EAAC,kBAAkB,EAAE,kBAAkB,EAAE,SAAS,EAAE,gBAAgB,CAAC;IAC1E,IAAA,wBAAe,EAAC,oCAAgB,CAAC;IACjC,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,gCAAgC;QACzC,WAAW,EAAE,wFAAwF;KACtG,CAAC;IACD,IAAA,kBAAQ,EAAC;QACR,IAAI,EAAE,UAAU;QAChB,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE,8BAA8B;KAC5C,CAAC;IACD,IAAA,kBAAQ,EAAC;QACR,IAAI,EAAE,YAAY;QAClB,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE,iCAAiC;KAC/C,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,yCAAyC;QACtD,IAAI,EAAE,qEAA+B;KACtC,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,UAAU,CAAC,CAAA;IACjB,WAAA,IAAA,cAAK,EAAC,YAAY,CAAC,CAAA;IACnB,WAAA,IAAA,oCAAW,GAAE,CAAA;;;wDACb,OAAO,oBAAP,OAAO;oEAcT;AAqDK;IAhDL,IAAA,aAAI,EAAC,UAAU,CAAC;IAChB,IAAA,uBAAK,EAAC,kBAAkB,EAAE,kBAAkB,EAAE,SAAS,EAAE,gBAAgB,CAAC;IAC1E,IAAA,iBAAQ,EAAC,mBAAU,CAAC,EAAE,CAAC;IACvB,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,+BAA+B;QACxC,WAAW,EAAE,qHAAqH;KACnI,CAAC;IACD,IAAA,iBAAO,EAAC;QACP,IAAI,EAAE,wDAAyB;QAC/B,WAAW,EAAE,kCAAkC;KAChD,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,wBAAwB;QACrC,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,OAAO,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE;gBAC3C,MAAM,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;gBACjE,QAAQ,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;gBACnE,eAAe,EAAE;oBACf,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,WAAW,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;wBACzD,QAAQ,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;wBACtD,SAAS,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;wBACvD,UAAU,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;qBACzD;iBACF;aACF;SACF;KACF,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,WAAW;QAC9B,WAAW,EAAE,iCAAiC;QAC9C,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,OAAO,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE;gBAC5C,MAAM,EAAE;oBACN,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;oBACzB,OAAO,EAAE,CAAC,6CAA6C,EAAE,8BAA8B,CAAC;iBACzF;gBACD,QAAQ,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;aACvD;SACF;KACF,CAAC;IAEC,WAAA,IAAA,aAAI,EAAC,uBAAc,CAAC,CAAA;;yDAAY,wDAAyB,oBAAzB,wDAAyB;wDACzD,OAAO,oBAAP,OAAO;6EAwBT;qCA7mBU,0BAA0B;IALtC,IAAA,iBAAO,EAAC,oBAAoB,CAAC;IAC7B,IAAA,mBAAU,EAAC,8BAA8B,CAAC;IAC1C,IAAA,kBAAS,EAAC,6BAAY,EAAE,wBAAU,CAAC;IACnC,IAAA,wBAAe,EAAC,oCAAgB,CAAC;IACjC,IAAA,uBAAa,GAAE;yDAG8B,mDAAuB,oBAAvB,mDAAuB;GAFxD,0BAA0B,CA8mBtC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\controllers\\report-definition.controller.ts"], "sourcesContent": ["import {\r\n  Controller,\r\n  Get,\r\n  Post,\r\n  Put,\r\n  Delete,\r\n  Body,\r\n  Param,\r\n  Query,\r\n  UseGuards,\r\n  UseInterceptors,\r\n  HttpStatus,\r\n  ParseUUIDPipe,\r\n  ValidationPipe,\r\n  HttpCode,\r\n} from '@nestjs/common';\r\nimport {\r\n  ApiTags,\r\n  ApiOperation,\r\n  ApiResponse,\r\n  ApiParam,\r\n  ApiQuery,\r\n  ApiBearerAuth,\r\n  ApiBody,\r\n} from '@nestjs/swagger';\r\nimport { JwtAuthGuard } from '../../../infrastructure/auth/guards/jwt-auth.guard';\r\nimport { RolesGuard } from '../../../infrastructure/auth/guards/roles.guard';\r\nimport { Roles } from '../../../infrastructure/auth/decorators/roles.decorator';\r\nimport { CurrentUser } from '../../../infrastructure/auth/decorators/current-user.decorator';\r\nimport { AuditInterceptor } from '../../../infrastructure/interceptors/audit.interceptor';\r\nimport { CacheInterceptor } from '../../../infrastructure/interceptors/cache.interceptor';\r\nimport { ReportDefinitionService } from '../services/report-definition.service';\r\nimport { CreateReportDefinitionDto } from '../dto/create-report-definition.dto';\r\nimport { UpdateReportDefinitionDto } from '../dto/update-report-definition.dto';\r\nimport { ReportDefinitionResponseDto } from '../dto/report-definition-response.dto';\r\nimport { ReportDefinitionListResponseDto } from '../dto/report-definition-list-response.dto';\r\nimport { OptimizationRecommendationsDto } from '../dto/optimization-recommendations.dto';\r\nimport { CloneReportDefinitionDto } from '../dto/clone-report-definition.dto';\r\n\r\n/**\r\n * Report Definition Controller\r\n * \r\n * Provides REST endpoints for report definition management including:\r\n * - CRUD operations with comprehensive validation and error handling\r\n * - Template management and cloning capabilities\r\n * - Access control validation and user permission checking\r\n * - Configuration validation and optimization recommendations\r\n * - Usage statistics and analytics with filtering and pagination\r\n * - Integration with audit logging and security measures\r\n */\r\n@ApiTags('Report Definitions')\r\n@Controller('api/v1/reporting/definitions')\r\n@UseGuards(JwtAuthGuard, RolesGuard)\r\n@UseInterceptors(AuditInterceptor)\r\n@ApiBearerAuth()\r\nexport class ReportDefinitionController {\r\n  constructor(\r\n    private readonly reportDefinitionService: ReportDefinitionService,\r\n  ) {}\r\n\r\n  /**\r\n   * Create a new report definition\r\n   */\r\n  @Post()\r\n  @Roles('compliance_admin', 'security_analyst', 'auditor', 'report_creator')\r\n  @ApiOperation({\r\n    summary: 'Create a new report definition',\r\n    description: 'Creates a new report definition with comprehensive configuration including data sources, visualizations, export settings, and access control.',\r\n  })\r\n  @ApiBody({\r\n    type: CreateReportDefinitionDto,\r\n    description: 'Report definition configuration',\r\n    examples: {\r\n      complianceReport: {\r\n        summary: 'Compliance Dashboard Report',\r\n        value: {\r\n          name: 'SOX Compliance Dashboard',\r\n          description: 'Comprehensive SOX compliance status report with key metrics and trends',\r\n          reportType: 'compliance_dashboard',\r\n          category: 'compliance',\r\n          priority: 'high',\r\n          dataSourceConfig: {\r\n            sources: [\r\n              {\r\n                type: 'compliance',\r\n                query: 'frameworks',\r\n                filters: { frameworkType: 'financial' },\r\n              },\r\n            ],\r\n          },\r\n          visualizationConfig: {\r\n            chartType: 'bar',\r\n            layout: { width: 800, height: 600, responsive: true },\r\n            styling: {\r\n              colors: ['#1f77b4', '#ff7f0e', '#2ca02c'],\r\n              theme: 'light',\r\n              fontSize: 12,\r\n              fontFamily: 'Arial',\r\n            },\r\n            series: [\r\n              {\r\n                name: 'Compliance Score',\r\n                dataField: 'score',\r\n                aggregation: 'avg',\r\n              },\r\n            ],\r\n            interactivity: {\r\n              drillDown: true,\r\n              filtering: true,\r\n              sorting: true,\r\n              export: true,\r\n            },\r\n          },\r\n          exportConfig: {\r\n            formats: ['pdf', 'excel', 'csv'],\r\n            defaultFormat: 'pdf',\r\n            deliveryOptions: {\r\n              email: true,\r\n              download: true,\r\n              webhook: false,\r\n              storage: true,\r\n            },\r\n          },\r\n          accessControl: {\r\n            visibility: 'shared',\r\n            allowedRoles: ['compliance_admin', 'auditor'],\r\n            allowedUsers: [],\r\n            permissions: {\r\n              view: true,\r\n              edit: true,\r\n              delete: false,\r\n              export: true,\r\n              share: true,\r\n            },\r\n          },\r\n          performanceConfig: {\r\n            caching: {\r\n              enabled: true,\r\n              strategy: 'redis',\r\n              ttl: 3600,\r\n              invalidationRules: ['compliance_data_updated'],\r\n            },\r\n            optimization: {\r\n              dataLimit: 10000,\r\n              queryTimeout: 30000,\r\n              parallelProcessing: true,\r\n              indexHints: ['framework_id', 'created_at'],\r\n            },\r\n            monitoring: {\r\n              trackExecutionTime: true,\r\n              trackDataVolume: true,\r\n              alertThresholds: {\r\n                executionTime: 60000,\r\n                dataVolume: 50000,\r\n                errorRate: 5,\r\n              },\r\n            },\r\n          },\r\n          tags: ['sox', 'compliance', 'financial'],\r\n          complianceFrameworks: ['sox'],\r\n          dataClassification: 'confidential',\r\n          auditRequired: true,\r\n        },\r\n      },\r\n    },\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.CREATED,\r\n    description: 'Report definition created successfully',\r\n    type: ReportDefinitionResponseDto,\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.BAD_REQUEST,\r\n    description: 'Invalid request data or configuration validation failed',\r\n    schema: {\r\n      type: 'object',\r\n      properties: {\r\n        statusCode: { type: 'number', example: 400 },\r\n        message: { type: 'string', example: 'Configuration validation failed: At least one data source must be configured' },\r\n        error: { type: 'string', example: 'Bad Request' },\r\n        timestamp: { type: 'string', format: 'date-time' },\r\n        path: { type: 'string', example: '/api/v1/reporting/definitions' },\r\n      },\r\n    },\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.FORBIDDEN,\r\n    description: 'Insufficient permissions to create report definitions',\r\n  })\r\n  async createReportDefinition(\r\n    @Body(ValidationPipe) createDto: CreateReportDefinitionDto,\r\n    @CurrentUser() user: any,\r\n  ): Promise<ReportDefinitionResponseDto> {\r\n    const reportDefinition = await this.reportDefinitionService.createReportDefinition(\r\n      createDto,\r\n      user.id\r\n    );\r\n\r\n    return new ReportDefinitionResponseDto(reportDefinition);\r\n  }\r\n\r\n  /**\r\n   * Get all report definitions with filtering and pagination\r\n   */\r\n  @Get()\r\n  @Roles('compliance_admin', 'security_analyst', 'auditor', 'report_viewer')\r\n  @UseInterceptors(CacheInterceptor)\r\n  @ApiOperation({\r\n    summary: 'Get report definitions',\r\n    description: 'Retrieves a paginated list of report definitions with optional filtering by type, category, owner, and search terms.',\r\n  })\r\n  @ApiQuery({\r\n    name: 'page',\r\n    required: false,\r\n    type: Number,\r\n    description: 'Page number (1-based)',\r\n    example: 1,\r\n  })\r\n  @ApiQuery({\r\n    name: 'limit',\r\n    required: false,\r\n    type: Number,\r\n    description: 'Number of items per page (max 100)',\r\n    example: 20,\r\n  })\r\n  @ApiQuery({\r\n    name: 'reportType',\r\n    required: false,\r\n    type: String,\r\n    description: 'Filter by report type',\r\n    enum: [\r\n      'compliance_dashboard',\r\n      'assessment_report',\r\n      'audit_summary',\r\n      'risk_analysis',\r\n      'evidence_report',\r\n      'framework_status',\r\n      'control_effectiveness',\r\n      'violation_analysis',\r\n      'trend_analysis',\r\n      'executive_summary',\r\n      'regulatory_report',\r\n      'custom_report',\r\n    ],\r\n  })\r\n  @ApiQuery({\r\n    name: 'category',\r\n    required: false,\r\n    type: String,\r\n    description: 'Filter by category',\r\n    enum: ['compliance', 'security', 'risk', 'audit', 'operational', 'financial', 'executive', 'regulatory'],\r\n  })\r\n  @ApiQuery({\r\n    name: 'ownerId',\r\n    required: false,\r\n    type: String,\r\n    description: 'Filter by owner ID',\r\n  })\r\n  @ApiQuery({\r\n    name: 'isTemplate',\r\n    required: false,\r\n    type: Boolean,\r\n    description: 'Filter by template status',\r\n  })\r\n  @ApiQuery({\r\n    name: 'tags',\r\n    required: false,\r\n    type: [String],\r\n    description: 'Filter by tags',\r\n  })\r\n  @ApiQuery({\r\n    name: 'search',\r\n    required: false,\r\n    type: String,\r\n    description: 'Search in name and description',\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Report definitions retrieved successfully',\r\n    type: ReportDefinitionListResponseDto,\r\n  })\r\n  async getReportDefinitions(\r\n    @Query('page') page?: number,\r\n    @Query('limit') limit?: number,\r\n    @Query('reportType') reportType?: string,\r\n    @Query('category') category?: string,\r\n    @Query('ownerId') ownerId?: string,\r\n    @Query('isTemplate') isTemplate?: boolean,\r\n    @Query('tags') tags?: string[],\r\n    @Query('search') search?: string,\r\n    @CurrentUser() user: any,\r\n  ): Promise<ReportDefinitionListResponseDto> {\r\n    const result = await this.reportDefinitionService.getReportDefinitions(\r\n      {\r\n        page,\r\n        limit,\r\n        reportType,\r\n        category,\r\n        ownerId,\r\n        isTemplate,\r\n        tags,\r\n        search,\r\n      },\r\n      user.id,\r\n      user.roles\r\n    );\r\n\r\n    return new ReportDefinitionListResponseDto(result);\r\n  }\r\n\r\n  /**\r\n   * Get report definition by ID\r\n   */\r\n  @Get(':id')\r\n  @Roles('compliance_admin', 'security_analyst', 'auditor', 'report_viewer')\r\n  @UseInterceptors(CacheInterceptor)\r\n  @ApiOperation({\r\n    summary: 'Get report definition by ID',\r\n    description: 'Retrieves a specific report definition by its ID with access control validation.',\r\n  })\r\n  @ApiParam({\r\n    name: 'id',\r\n    type: String,\r\n    format: 'uuid',\r\n    description: 'Report definition ID',\r\n    example: '123e4567-e89b-12d3-a456-************',\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Report definition retrieved successfully',\r\n    type: ReportDefinitionResponseDto,\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.NOT_FOUND,\r\n    description: 'Report definition not found',\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.FORBIDDEN,\r\n    description: 'Insufficient permissions to view this report definition',\r\n  })\r\n  async getReportDefinitionById(\r\n    @Param('id', ParseUUIDPipe) id: string,\r\n    @CurrentUser() user: any,\r\n  ): Promise<ReportDefinitionResponseDto> {\r\n    const reportDefinition = await this.reportDefinitionService.getReportDefinitionById(\r\n      id,\r\n      user.id,\r\n      user.roles\r\n    );\r\n\r\n    return new ReportDefinitionResponseDto(reportDefinition);\r\n  }\r\n\r\n  /**\r\n   * Update report definition\r\n   */\r\n  @Put(':id')\r\n  @Roles('compliance_admin', 'security_analyst', 'auditor', 'report_creator')\r\n  @ApiOperation({\r\n    summary: 'Update report definition',\r\n    description: 'Updates an existing report definition with partial or complete configuration changes.',\r\n  })\r\n  @ApiParam({\r\n    name: 'id',\r\n    type: String,\r\n    format: 'uuid',\r\n    description: 'Report definition ID',\r\n  })\r\n  @ApiBody({\r\n    type: UpdateReportDefinitionDto,\r\n    description: 'Updated report definition configuration',\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Report definition updated successfully',\r\n    type: ReportDefinitionResponseDto,\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.NOT_FOUND,\r\n    description: 'Report definition not found',\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.FORBIDDEN,\r\n    description: 'Insufficient permissions to update this report definition',\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.BAD_REQUEST,\r\n    description: 'Invalid update data or configuration validation failed',\r\n  })\r\n  async updateReportDefinition(\r\n    @Param('id', ParseUUIDPipe) id: string,\r\n    @Body(ValidationPipe) updateDto: UpdateReportDefinitionDto,\r\n    @CurrentUser() user: any,\r\n  ): Promise<ReportDefinitionResponseDto> {\r\n    const reportDefinition = await this.reportDefinitionService.updateReportDefinition(\r\n      id,\r\n      updateDto,\r\n      user.id,\r\n      user.roles\r\n    );\r\n\r\n    return new ReportDefinitionResponseDto(reportDefinition);\r\n  }\r\n\r\n  /**\r\n   * Delete report definition\r\n   */\r\n  @Delete(':id')\r\n  @Roles('compliance_admin', 'security_analyst', 'report_creator')\r\n  @HttpCode(HttpStatus.NO_CONTENT)\r\n  @ApiOperation({\r\n    summary: 'Delete report definition',\r\n    description: 'Soft deletes a report definition. The definition will be marked as inactive but preserved for audit purposes.',\r\n  })\r\n  @ApiParam({\r\n    name: 'id',\r\n    type: String,\r\n    format: 'uuid',\r\n    description: 'Report definition ID',\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.NO_CONTENT,\r\n    description: 'Report definition deleted successfully',\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.NOT_FOUND,\r\n    description: 'Report definition not found',\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.FORBIDDEN,\r\n    description: 'Insufficient permissions to delete this report definition',\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.BAD_REQUEST,\r\n    description: 'Cannot delete report with active executions',\r\n  })\r\n  async deleteReportDefinition(\r\n    @Param('id', ParseUUIDPipe) id: string,\r\n    @CurrentUser() user: any,\r\n  ): Promise<void> {\r\n    await this.reportDefinitionService.deleteReportDefinition(id, user.id, user.roles);\r\n  }\r\n\r\n  /**\r\n   * Clone report definition from template\r\n   */\r\n  @Post(':templateId/clone')\r\n  @Roles('compliance_admin', 'security_analyst', 'auditor', 'report_creator')\r\n  @ApiOperation({\r\n    summary: 'Clone report definition from template',\r\n    description: 'Creates a new report definition by cloning an existing template with customizable name and settings.',\r\n  })\r\n  @ApiParam({\r\n    name: 'templateId',\r\n    type: String,\r\n    format: 'uuid',\r\n    description: 'Template report definition ID',\r\n  })\r\n  @ApiBody({\r\n    type: CloneReportDefinitionDto,\r\n    description: 'Clone configuration',\r\n    examples: {\r\n      cloneExample: {\r\n        summary: 'Clone SOX template',\r\n        value: {\r\n          name: 'Q4 SOX Compliance Report',\r\n          description: 'Quarterly SOX compliance report for Q4 2024',\r\n          customizations: {\r\n            dataSourceConfig: {\r\n              sources: [\r\n                {\r\n                  type: 'compliance',\r\n                  query: 'frameworks',\r\n                  filters: { quarter: 'Q4', year: 2024 },\r\n                },\r\n              ],\r\n            },\r\n          },\r\n        },\r\n      },\r\n    },\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.CREATED,\r\n    description: 'Report definition cloned successfully',\r\n    type: ReportDefinitionResponseDto,\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.NOT_FOUND,\r\n    description: 'Template not found',\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.BAD_REQUEST,\r\n    description: 'Source report is not a template or name already exists',\r\n  })\r\n  async cloneReportDefinition(\r\n    @Param('templateId', ParseUUIDPipe) templateId: string,\r\n    @Body(ValidationPipe) cloneDto: CloneReportDefinitionDto,\r\n    @CurrentUser() user: any,\r\n  ): Promise<ReportDefinitionResponseDto> {\r\n    const reportDefinition = await this.reportDefinitionService.cloneReportDefinition(\r\n      templateId,\r\n      cloneDto.name,\r\n      user.id,\r\n      user.roles\r\n    );\r\n\r\n    return new ReportDefinitionResponseDto(reportDefinition);\r\n  }\r\n\r\n  /**\r\n   * Get optimization recommendations\r\n   */\r\n  @Get(':id/recommendations')\r\n  @Roles('compliance_admin', 'security_analyst', 'auditor', 'report_creator')\r\n  @UseInterceptors(CacheInterceptor)\r\n  @ApiOperation({\r\n    summary: 'Get optimization recommendations',\r\n    description: 'Analyzes the report definition and provides recommendations for performance, security, usability, and compliance improvements.',\r\n  })\r\n  @ApiParam({\r\n    name: 'id',\r\n    type: String,\r\n    format: 'uuid',\r\n    description: 'Report definition ID',\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Optimization recommendations retrieved successfully',\r\n    type: OptimizationRecommendationsDto,\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.NOT_FOUND,\r\n    description: 'Report definition not found',\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.FORBIDDEN,\r\n    description: 'Insufficient permissions to view recommendations for this report',\r\n  })\r\n  async getOptimizationRecommendations(\r\n    @Param('id', ParseUUIDPipe) id: string,\r\n    @CurrentUser() user: any,\r\n  ): Promise<OptimizationRecommendationsDto> {\r\n    const recommendations = await this.reportDefinitionService.getOptimizationRecommendations(\r\n      id,\r\n      user.id,\r\n      user.roles\r\n    );\r\n\r\n    return new OptimizationRecommendationsDto(recommendations);\r\n  }\r\n\r\n  /**\r\n   * Get report templates\r\n   */\r\n  @Get('templates/list')\r\n  @Roles('compliance_admin', 'security_analyst', 'auditor', 'report_creator')\r\n  @UseInterceptors(CacheInterceptor)\r\n  @ApiOperation({\r\n    summary: 'Get available report templates',\r\n    description: 'Retrieves a list of available report templates that can be used to create new reports.',\r\n  })\r\n  @ApiQuery({\r\n    name: 'category',\r\n    required: false,\r\n    type: String,\r\n    description: 'Filter templates by category',\r\n  })\r\n  @ApiQuery({\r\n    name: 'reportType',\r\n    required: false,\r\n    type: String,\r\n    description: 'Filter templates by report type',\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Report templates retrieved successfully',\r\n    type: ReportDefinitionListResponseDto,\r\n  })\r\n  async getReportTemplates(\r\n    @Query('category') category?: string,\r\n    @Query('reportType') reportType?: string,\r\n    @CurrentUser() user: any,\r\n  ): Promise<ReportDefinitionListResponseDto> {\r\n    const result = await this.reportDefinitionService.getReportDefinitions(\r\n      {\r\n        isTemplate: true,\r\n        category,\r\n        reportType,\r\n        page: 1,\r\n        limit: 100,\r\n      },\r\n      user.id,\r\n      user.roles\r\n    );\r\n\r\n    return new ReportDefinitionListResponseDto(result);\r\n  }\r\n\r\n  /**\r\n   * Validate report configuration\r\n   */\r\n  @Post('validate')\r\n  @Roles('compliance_admin', 'security_analyst', 'auditor', 'report_creator')\r\n  @HttpCode(HttpStatus.OK)\r\n  @ApiOperation({\r\n    summary: 'Validate report configuration',\r\n    description: 'Validates a report configuration without creating the report. Useful for form validation and configuration testing.',\r\n  })\r\n  @ApiBody({\r\n    type: CreateReportDefinitionDto,\r\n    description: 'Report configuration to validate',\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Configuration is valid',\r\n    schema: {\r\n      type: 'object',\r\n      properties: {\r\n        isValid: { type: 'boolean', example: true },\r\n        errors: { type: 'array', items: { type: 'string' }, example: [] },\r\n        warnings: { type: 'array', items: { type: 'string' }, example: [] },\r\n        recommendations: {\r\n          type: 'object',\r\n          properties: {\r\n            performance: { type: 'array', items: { type: 'string' } },\r\n            security: { type: 'array', items: { type: 'string' } },\r\n            usability: { type: 'array', items: { type: 'string' } },\r\n            compliance: { type: 'array', items: { type: 'string' } },\r\n          },\r\n        },\r\n      },\r\n    },\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.BAD_REQUEST,\r\n    description: 'Configuration validation failed',\r\n    schema: {\r\n      type: 'object',\r\n      properties: {\r\n        isValid: { type: 'boolean', example: false },\r\n        errors: {\r\n          type: 'array',\r\n          items: { type: 'string' },\r\n          example: ['At least one data source must be configured', 'Chart type must be specified'],\r\n        },\r\n        warnings: { type: 'array', items: { type: 'string' } },\r\n      },\r\n    },\r\n  })\r\n  async validateReportConfiguration(\r\n    @Body(ValidationPipe) configDto: CreateReportDefinitionDto,\r\n  ): Promise<{\r\n    isValid: boolean;\r\n    errors: string[];\r\n    warnings: string[];\r\n    recommendations?: {\r\n      performance: string[];\r\n      security: string[];\r\n      usability: string[];\r\n      compliance: string[];\r\n    };\r\n  }> {\r\n    // This would use the validation logic from the service\r\n    // For now, return a mock validation result\r\n    return {\r\n      isValid: true,\r\n      errors: [],\r\n      warnings: [],\r\n      recommendations: {\r\n        performance: ['Enable caching for better performance'],\r\n        security: ['Consider enabling audit requirements'],\r\n        usability: ['Enable export functionality'],\r\n        compliance: ['Extend retention period for compliance frameworks'],\r\n      },\r\n    };\r\n  }\r\n}\r\n"], "version": 3}