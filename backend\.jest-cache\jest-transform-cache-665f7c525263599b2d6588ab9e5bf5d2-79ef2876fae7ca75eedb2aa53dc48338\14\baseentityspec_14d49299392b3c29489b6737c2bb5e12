1003204291bd6267eaded1e530f6374d
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const base_entity_1 = require("../../domain/base-entity");
const unique_entity_id_value_object_1 = require("../../value-objects/unique-entity-id.value-object");
class TestEntity extends base_entity_1.BaseEntity {
    constructor(props, id) {
        super(props, id);
    }
    get name() {
        return this.props.name;
    }
    get email() {
        return this.props.email;
    }
    get age() {
        return this.props.age;
    }
    // Method to update props for testing
    updateName(name) {
        this.props.name = name;
    }
}
describe('BaseEntity', () => {
    describe('construction', () => {
        it('should create entity with provided props', () => {
            const props = {
                name: '<PERSON>',
                email: '<EMAIL>',
                age: 30
            };
            const entity = new TestEntity(props);
            expect(entity.name).toBe('<PERSON>');
            expect(entity.email).toBe('<EMAIL>');
            expect(entity.age).toBe(30);
        });
        it('should create entity with provided ID', () => {
            const props = {
                name: '<PERSON>e',
                email: '<EMAIL>'
            };
            const id = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const entity = new TestEntity(props, id);
            expect(entity.id.equals(id)).toBe(true);
        });
        it('should generate ID if not provided', () => {
            const props = {
                name: 'John Doe',
                email: '<EMAIL>'
            };
            const entity = new TestEntity(props);
            expect(entity.id).toBeInstanceOf(unique_entity_id_value_object_1.UniqueEntityId);
            expect(entity.id.value).toBeDefined();
        });
        it('should handle optional properties', () => {
            const props = {
                name: 'John Doe',
                email: '<EMAIL>'
                // age is optional and not provided
            };
            const entity = new TestEntity(props);
            expect(entity.name).toBe('John Doe');
            expect(entity.email).toBe('<EMAIL>');
            expect(entity.age).toBeUndefined();
        });
    });
    describe('identity', () => {
        it('should have unique identity', () => {
            const props = {
                name: 'John Doe',
                email: '<EMAIL>'
            };
            const entity1 = new TestEntity(props);
            const entity2 = new TestEntity(props);
            expect(entity1.id.equals(entity2.id)).toBe(false);
        });
        it('should maintain identity across property changes', () => {
            const props = {
                name: 'John Doe',
                email: '<EMAIL>'
            };
            const entity = new TestEntity(props);
            const originalId = entity.id;
            entity.updateName('Jane Doe');
            expect(entity.id.equals(originalId)).toBe(true);
        });
        it('should expose ID as readonly', () => {
            const entity = new TestEntity({
                name: 'John Doe',
                email: '<EMAIL>'
            });
            const id = entity.id;
            // Attempt to modify ID should not be possible
            expect(() => {
                entity.id = unique_entity_id_value_object_1.UniqueEntityId.generate();
            }).toThrow();
        });
    });
    describe('equality comparison', () => {
        it('should be equal to itself', () => {
            const entity = new TestEntity({
                name: 'John Doe',
                email: '<EMAIL>'
            });
            expect(entity.equals(entity)).toBe(true);
        });
        it('should be equal to entity with same ID', () => {
            const id = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const props1 = {
                name: 'John Doe',
                email: '<EMAIL>'
            };
            const props2 = {
                name: 'Jane Doe', // Different props
                email: '<EMAIL>'
            };
            const entity1 = new TestEntity(props1, id);
            const entity2 = new TestEntity(props2, id);
            expect(entity1.equals(entity2)).toBe(true);
        });
        it('should not be equal to entity with different ID', () => {
            const props = {
                name: 'John Doe',
                email: '<EMAIL>'
            };
            const entity1 = new TestEntity(props);
            const entity2 = new TestEntity(props);
            expect(entity1.equals(entity2)).toBe(false);
        });
        it('should not be equal to null', () => {
            const entity = new TestEntity({
                name: 'John Doe',
                email: '<EMAIL>'
            });
            expect(entity.equals(null)).toBe(false);
        });
        it('should not be equal to undefined', () => {
            const entity = new TestEntity({
                name: 'John Doe',
                email: '<EMAIL>'
            });
            expect(entity.equals(undefined)).toBe(false);
        });
    });
    describe('JSON serialization', () => {
        it('should serialize to JSON with ID and props', () => {
            const props = {
                name: 'John Doe',
                email: '<EMAIL>',
                age: 30
            };
            const entity = new TestEntity(props);
            const json = entity.toJSON();
            expect(json).toHaveProperty('id', entity.id.toString());
            expect(json).toHaveProperty('name', 'John Doe');
            expect(json).toHaveProperty('email', '<EMAIL>');
            expect(json).toHaveProperty('age', 30);
        });
        it('should handle undefined optional properties in JSON', () => {
            const props = {
                name: 'John Doe',
                email: '<EMAIL>'
                // age is undefined
            };
            const entity = new TestEntity(props);
            const json = entity.toJSON();
            expect(json).toHaveProperty('id');
            expect(json).toHaveProperty('name', 'John Doe');
            expect(json).toHaveProperty('email', '<EMAIL>');
            // age property should not be present in JSON when undefined
            expect(json.age).toBeUndefined();
        });
        it('should include all props in JSON', () => {
            const props = {
                name: 'John Doe',
                email: '<EMAIL>',
                age: 30
            };
            const entity = new TestEntity(props);
            const json = entity.toJSON();
            // Should include ID plus all props
            expect(Object.keys(json)).toEqual(['id', 'name', 'email', 'age']);
        });
    });
    describe('props access', () => {
        it('should provide access to props through getters', () => {
            const props = {
                name: 'John Doe',
                email: '<EMAIL>',
                age: 30
            };
            const entity = new TestEntity(props);
            expect(entity.name).toBe(props.name);
            expect(entity.email).toBe(props.email);
            expect(entity.age).toBe(props.age);
        });
        it('should maintain props reference', () => {
            const props = {
                name: 'John Doe',
                email: '<EMAIL>',
                age: 30
            };
            const entity = new TestEntity(props);
            // Props should be the same reference (for performance)
            expect(entity.props).toBe(props);
        });
        it('should allow props modification through entity methods', () => {
            const props = {
                name: 'John Doe',
                email: '<EMAIL>'
            };
            const entity = new TestEntity(props);
            expect(entity.name).toBe('John Doe');
            entity.updateName('Jane Doe');
            expect(entity.name).toBe('Jane Doe');
        });
    });
    describe('type safety', () => {
        it('should enforce props type at compile time', () => {
            // This test ensures TypeScript compilation succeeds with correct types
            const validProps = {
                name: 'John Doe',
                email: '<EMAIL>',
                age: 30
            };
            const entity = new TestEntity(validProps);
            expect(entity).toBeInstanceOf(TestEntity);
        });
        it('should maintain type safety for ID', () => {
            const entity = new TestEntity({
                name: 'John Doe',
                email: '<EMAIL>'
            });
            expect(entity.id).toBeInstanceOf(unique_entity_id_value_object_1.UniqueEntityId);
        });
    });
    describe('inheritance', () => {
        class ExtendedEntity extends TestEntity {
            constructor(props, id) {
                super(props, id);
            }
            get department() {
                return this.props.department;
            }
        }
        it('should support inheritance', () => {
            const props = {
                name: 'John Doe',
                email: '<EMAIL>',
                age: 30,
                department: 'Engineering'
            };
            const entity = new ExtendedEntity(props);
            expect(entity).toBeInstanceOf(ExtendedEntity);
            expect(entity).toBeInstanceOf(TestEntity);
            expect(entity).toBeInstanceOf(base_entity_1.BaseEntity);
            expect(entity.department).toBe('Engineering');
        });
        it('should maintain base functionality in derived classes', () => {
            const props = {
                name: 'John Doe',
                email: '<EMAIL>',
                department: 'Engineering'
            };
            const entity = new ExtendedEntity(props);
            expect(entity.id).toBeInstanceOf(unique_entity_id_value_object_1.UniqueEntityId);
            expect(entity.name).toBe('John Doe');
            expect(entity.email).toBe('<EMAIL>');
            expect(entity.equals(entity)).toBe(true);
        });
    });
    describe('edge cases', () => {
        it('should handle empty string properties', () => {
            const props = {
                name: '',
                email: ''
            };
            const entity = new TestEntity(props);
            expect(entity.name).toBe('');
            expect(entity.email).toBe('');
        });
        it('should handle zero age', () => {
            const props = {
                name: 'Baby Doe',
                email: '<EMAIL>',
                age: 0
            };
            const entity = new TestEntity(props);
            expect(entity.age).toBe(0);
        });
        it('should handle negative age', () => {
            const props = {
                name: 'Time Traveler',
                email: '<EMAIL>',
                age: -1
            };
            const entity = new TestEntity(props);
            expect(entity.age).toBe(-1);
        });
        it('should handle special characters in properties', () => {
            const props = {
                name: 'José María Aznar-López',
                email: 'josé.marí*************'
            };
            const entity = new TestEntity(props);
            expect(entity.name).toBe('José María Aznar-López');
            expect(entity.email).toBe('josé.marí*************');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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