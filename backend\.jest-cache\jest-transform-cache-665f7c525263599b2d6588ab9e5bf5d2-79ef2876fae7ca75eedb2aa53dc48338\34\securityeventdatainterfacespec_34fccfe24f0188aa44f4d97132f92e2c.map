{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\data\\__tests__\\security-event-data.interface.spec.ts", "mappings": ";;AAeA,oEAA2D;AAC3D,4EAAmE;AACnE,wEAA+D;AAC/D,8FAAoF;AACpF,kFAAwE;AAExE,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;IAC3C,IAAI,qBAAwC,CAAC;IAC7C,IAAI,kBAA+C,CAAC;IACpD,IAAI,eAAyC,CAAC;IAC9C,IAAI,aAAqC,CAAC;IAC1C,IAAI,YAAmC,CAAC;IACxC,IAAI,eAAyC,CAAC;IAC9C,IAAI,oBAAmD,CAAC;IACxD,IAAI,kBAA+C,CAAC;IACpD,IAAI,iBAA6C,CAAC;IAElD,UAAU,CAAC,GAAG,EAAE;QACd,kBAAkB,GAAG;YACnB,UAAU,EAAE,WAAW;YACvB,aAAa,EAAE,OAAO;YACtB,KAAK,EAAE,YAAY;YACnB,QAAQ,EAAE,YAAY;YACtB,UAAU,EAAE,MAAM;YAClB,QAAQ,EAAE,KAAK;YACf,WAAW,EAAE,sBAAsB;YACnC,WAAW,EAAE,sBAAsB;YACnC,WAAW,EAAE,EAAE;YACf,SAAS,EAAE,EAAE,aAAa,EAAE,OAAO,EAAE;SACtC,CAAC;QAEF,eAAe,GAAG;YAChB,QAAQ,EAAE,eAAe;YACzB,aAAa,EAAE,UAAU;YACzB,UAAU,EAAE,GAAG;YACf,eAAe,EAAE,EAAE;YACnB,QAAQ,EAAE,KAAK;YACf,cAAc,EAAE,gBAAgB;YAChC,YAAY,EAAE,CAAC,mBAAmB,CAAC;YACnC,OAAO,EAAE,CAAC,aAAa,CAAC;YACxB,IAAI,EAAE,CAAC,0BAA0B,CAAC;YAClC,aAAa,EAAE,UAAU;YACzB,gBAAgB,EAAE,IAAI;YACtB,YAAY,EAAE,EAAE;YAChB,kBAAkB,EAAE,EAAE;SACvB,CAAC;QAEF,aAAa,GAAG;YACd,OAAO,EAAE,WAAW;YACpB,SAAS,EAAE,eAAe;YAC1B,SAAS,EAAE,QAAQ;YACnB,UAAU,EAAE,SAAS;YACrB,WAAW,EAAE,MAAM;YACnB,QAAQ,EAAE,eAAe;YACzB,WAAW,EAAE,YAAY;YACzB,eAAe,EAAE,cAAc;YAC/B,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;YACjC,SAAS,EAAE,CAAC,KAAK,EAAE,eAAe,CAAC;SACpC,CAAC;QAEF,YAAY,GAAG;YACb,MAAM,EAAE,UAAU;YAClB,QAAQ,EAAE,UAAU;YACpB,KAAK,EAAE,sBAAsB;YAC7B,KAAK,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;YACxB,MAAM,EAAE,CAAC,gBAAgB,CAAC;YAC1B,UAAU,EAAE,IAAI;YAChB,QAAQ,EAAE,UAAU;YACpB,UAAU,EAAE,MAAM;YAClB,SAAS,EAAE,aAAa;YACxB,SAAS,EAAE,gBAAgB;SAC5B,CAAC;QAEF,eAAe,GAAG;YAChB,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,IAAI;YACZ,IAAI,EAAE,UAAU;YAChB,QAAQ,EAAE,OAAO;YACjB,SAAS,EAAE,CAAC,OAAO;YACnB,GAAG,EAAE,aAAa;YAClB,YAAY,EAAE,cAAc;YAC5B,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE,EAAE;SACnB,CAAC;QAEF,oBAAoB,GAAG;YACrB,IAAI,EAAE,CAAC,eAAe,EAAE,eAAe,CAAC;YACxC,aAAa,EAAE,CAAC,cAAc,CAAC;YAC/B,iBAAiB,EAAE,CAAC,eAAe,CAAC;YACpC,aAAa,EAAE,CAAC,eAAe,CAAC;YAChC,UAAU,EAAE,CAAC,kBAAkB,CAAC;YAChC,qBAAqB,EAAE,CAAC,OAAO,CAAC;YAChC,WAAW,EAAE,OAAO;YACpB,aAAa,EAAE,eAAe;YAC9B,UAAU,EAAE,cAAc;SAC3B,CAAC;QAEF,kBAAkB,GAAG;YACnB,UAAU,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC;YAC9B,YAAY,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;YACnD,kBAAkB,EAAE,cAAc;YAClC,aAAa,EAAE,MAAM;YACrB,uBAAuB,EAAE,CAAC,KAAK,EAAE,aAAa,CAAC;YAC/C,iBAAiB,EAAE,CAAC,eAAe,EAAE,mBAAmB,CAAC;SAC1D,CAAC;QAEF,iBAAiB,GAAG;YAClB,aAAa,EAAE,IAAI,EAAE,UAAU;YAC/B,SAAS,EAAE,KAAK;YAChB,gBAAgB,EAAE,IAAI;YACtB,YAAY,EAAE,sBAAsB;YACpC,QAAQ,EAAE,sBAAsB;YAChC,aAAa,EAAE,mCAAmC;SACnD,CAAC;QAEF,qBAAqB,GAAG;YACtB,OAAO,EAAE,OAAO;YAChB,UAAU,EAAE,eAAe;YAC3B,YAAY,EAAE,WAAW;YACzB,UAAU,EAAE,wCAAe,CAAC,IAAI;YAChC,SAAS,EAAE,sBAAsB;YACjC,IAAI,EAAE,2BAAS,CAAC,aAAa;YAC7B,QAAQ,EAAE,mCAAa,CAAC,IAAI;YAC5B,MAAM,EAAE,+BAAW,CAAC,IAAI;YACxB,gBAAgB,EAAE,oDAAqB,CAAC,UAAU;YAClD,KAAK,EAAE,sBAAsB;YAC7B,WAAW,EAAE,yCAAyC;YACtD,OAAO,EAAE,EAAE,QAAQ,EAAE,gBAAgB,EAAE;YACvC,cAAc,EAAE,EAAE,UAAU,EAAE,gBAAgB,EAAE;YAChD,IAAI,EAAE,CAAC,gBAAgB,EAAE,UAAU,CAAC;YACpC,SAAS,EAAE,EAAE;YACb,eAAe,EAAE,EAAE;YACnB,UAAU,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE;YACnC,aAAa,EAAE,UAAU;YACzB,aAAa,EAAE,YAAY;YAC3B,cAAc,EAAE,kBAAkB;YAClC,WAAW,EAAE,eAAe;YAC5B,SAAS,EAAE,aAAa;YACxB,QAAQ,EAAE,YAAY;YACtB,WAAW,EAAE,eAAe;YAC5B,gBAAgB,EAAE,oBAAoB;YACtC,cAAc,EAAE,kBAAkB;YAClC,aAAa,EAAE,iBAAiB;SACjC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YACpD,MAAM,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACvD,MAAM,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;YACzD,MAAM,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACvD,MAAM,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YACtD,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;YACjD,MAAM,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YACrD,MAAM,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;YAClD,MAAM,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YACpD,MAAM,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,qBAAqB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;YACzD,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACjC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAE7C,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;YACzD,MAAM,MAAM,GAAsB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAEzD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;YACjE,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;YACrE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;YAC7D,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QACjG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACxD,MAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACnD,MAAM,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,kBAAkB,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACvD,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACvD,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAChD,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACtD,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,CAAC,CAAC,YAAY,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;QAChG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC7C,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC/C,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7E,MAAM,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChE,MAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,OAAO,iBAAiB,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC9D,MAAM,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,CAAC,OAAO,iBAAiB,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,gCAAgC;YAChC,MAAM,aAAa,GAA+B;gBAChD,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC;oBAClC,OAAO,EAAE,IAAI;oBACb,MAAM,EAAE,EAAE;oBACV,QAAQ,EAAE,EAAE;oBACZ,aAAa,EAAE,qBAAqB;iBACrC,CAAC;gBACF,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;gBAChD,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,qBAAqB,CAAC;aAC3D,CAAC;YAEF,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7C,MAAM,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;YACpD,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,iCAAiC;YACjC,MAAM,cAAc,GAAgC;gBAClD,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;gBACvC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,qBAAqB,CAAC;gBAC1D,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACxD,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,qBAAqB,CAAC;gBAC5D,oBAAoB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,CAAC;aAC3D,CAAC;YAEF,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAC5C,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9C,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9C,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YAChD,MAAM,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC,WAAW,EAAE,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,kCAAkC;YAClC,MAAM,eAAe,GAAiC;gBACpD,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,qBAAqB,CAAC;gBAC3D,mBAAmB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC;gBAClD,uBAAuB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,wCAAe,CAAC,IAAI,CAAC,CAAC;gBAC1E,yBAAyB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;aACtE,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAChD,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1D,MAAM,CAAC,eAAe,CAAC,uBAAuB,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9D,MAAM,CAAC,eAAe,CAAC,yBAAyB,CAAC,CAAC,WAAW,EAAE,CAAC;QAClE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,QAAQ,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YAC7C,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACzB,MAAM,aAAa,GAAG,EAAE,GAAG,qBAAqB,EAAE,OAAO,EAAE,CAAC;gBAC5D,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,UAAU,GAAG;gBACjB,GAAG,qBAAqB;gBACxB,OAAO,EAAE,OAAO;aACjB,CAAC;YAEF,oDAAoD;YACpD,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YACzC,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YAC5C,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,wCAAe,CAAC,CAAC;YACnD,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;gBAC/B,MAAM,SAAS,GAAG,EAAE,GAAG,qBAAqB,EAAE,UAAU,EAAE,CAAC;gBAC3D,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,gBAAgB,GAAG;gBACvB,YAAY,EAAE,QAAQ;gBACtB,YAAY,EAAE,GAAG;gBACjB,YAAY,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE;aACnC,CAAC;YAEF,MAAM,eAAe,GAAG;gBACtB,GAAG,qBAAqB;gBACxB,UAAU,EAAE,gBAAgB;aAC7B,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,gBAAgB,GAAG;gBACvB,GAAG,kBAAkB;gBACrB,SAAS,EAAE;oBACT,GAAG,kBAAkB,CAAC,SAAS;oBAC/B,WAAW,EAAE,YAAY;oBACzB,gBAAgB,EAAE,UAAU;iBAC7B;aACF,CAAC;YAEF,MAAM,iBAAiB,GAAG;gBACxB,GAAG,qBAAqB;gBACxB,cAAc,EAAE,gBAAgB;aACjC,CAAC;YAEF,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAC/E,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;QACtF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\data\\__tests__\\security-event-data.interface.spec.ts"], "sourcesContent": ["import {\r\n  SecurityEventData,\r\n  SecurityEventDataValidator,\r\n  SecurityEventDataSerializer,\r\n  SecurityEventDataTransformer,\r\n  SecurityEventDataValidationResult,\r\n  SecurityEventSourceMetadata,\r\n  SecurityEventNetworkInfo,\r\n  SecurityEventAssetInfo,\r\n  SecurityEventUserInfo,\r\n  SecurityEventGeoLocation,\r\n  SecurityEventThreatIndicators,\r\n  SecurityEventComplianceInfo,\r\n  SecurityEventRetentionInfo,\r\n} from '../security-event-data.interface';\r\nimport { EventType } from '../../../enums/event-type.enum';\r\nimport { EventSeverity } from '../../../enums/event-severity.enum';\r\nimport { EventStatus } from '../../../enums/event-status.enum';\r\nimport { EventProcessingStatus } from '../../../enums/event-processing-status.enum';\r\nimport { EventSourceType } from '../../../enums/event-source-type.enum';\r\n\r\ndescribe('SecurityEventData Interface', () => {\r\n  let mockSecurityEventData: SecurityEventData;\r\n  let mockSourceMetadata: SecurityEventSourceMetadata;\r\n  let mockNetworkInfo: SecurityEventNetworkInfo;\r\n  let mockAssetInfo: SecurityEventAssetInfo;\r\n  let mockUserInfo: SecurityEventUserInfo;\r\n  let mockGeoLocation: SecurityEventGeoLocation;\r\n  let mockThreatIndicators: SecurityEventThreatIndicators;\r\n  let mockComplianceInfo: SecurityEventComplianceInfo;\r\n  let mockRetentionInfo: SecurityEventRetentionInfo;\r\n\r\n  beforeEach(() => {\r\n    mockSourceMetadata = {\r\n      systemName: 'Test SIEM',\r\n      systemVersion: '1.0.0',\r\n      agent: 'test-agent',\r\n      configId: 'config-123',\r\n      dataFormat: 'json',\r\n      timezone: 'UTC',\r\n      collectedAt: '2024-01-01T00:00:00Z',\r\n      processedAt: '2024-01-01T00:01:00Z',\r\n      reliability: 95,\r\n      sourceIds: { 'internal_id': '12345' },\r\n    };\r\n\r\n    mockNetworkInfo = {\r\n      sourceIp: '*************',\r\n      destinationIp: '********',\r\n      sourcePort: 443,\r\n      destinationPort: 80,\r\n      protocol: 'TCP',\r\n      networkSegment: '***********/24',\r\n      macAddresses: ['00:11:22:33:44:55'],\r\n      domains: ['example.com'],\r\n      urls: ['https://example.com/path'],\r\n      flowDirection: 'outbound',\r\n      bytesTransferred: 1024,\r\n      packetsCount: 10,\r\n      connectionDuration: 30,\r\n    };\r\n\r\n    mockAssetInfo = {\r\n      assetId: 'asset-123',\r\n      assetName: 'Web Server 01',\r\n      assetType: 'server',\r\n      assetOwner: 'IT Team',\r\n      criticality: 'high',\r\n      location: 'Data Center A',\r\n      environment: 'production',\r\n      operatingSystem: 'Ubuntu 20.04',\r\n      applications: ['nginx', 'nodejs'],\r\n      assetTags: ['web', 'public-facing'],\r\n    };\r\n\r\n    mockUserInfo = {\r\n      userId: 'user-123',\r\n      username: 'john.doe',\r\n      email: '<EMAIL>',\r\n      roles: ['admin', 'user'],\r\n      groups: ['administrators'],\r\n      department: 'IT',\r\n      location: 'New York',\r\n      authMethod: 'SAML',\r\n      sessionId: 'session-456',\r\n      userAgent: 'Mozilla/5.0...',\r\n    };\r\n\r\n    mockGeoLocation = {\r\n      country: 'US',\r\n      region: 'NY',\r\n      city: 'New York',\r\n      latitude: 40.7128,\r\n      longitude: -74.0060,\r\n      isp: 'Example ISP',\r\n      organization: 'Example Corp',\r\n      timezone: 'America/New_York',\r\n      accuracyRadius: 10,\r\n    };\r\n\r\n    mockThreatIndicators = {\r\n      iocs: ['*************', 'malicious.com'],\r\n      malwareHashes: ['abc123def456'],\r\n      suspiciousDomains: ['malicious.com'],\r\n      suspiciousIps: ['*************'],\r\n      signatures: ['MALWARE_DETECTED'],\r\n      mitreAttackTechniques: ['T1055'],\r\n      threatActor: 'APT29',\r\n      malwareFamily: 'Cobalt Strike',\r\n      campaignId: 'campaign-123',\r\n    };\r\n\r\n    mockComplianceInfo = {\r\n      frameworks: ['SOX', 'PCI-DSS'],\r\n      requirements: ['Data Protection', 'Access Control'],\r\n      dataClassification: 'Confidential',\r\n      privacyImpact: 'high',\r\n      regulatoryNotifications: ['SEC', 'PCI Council'],\r\n      auditRequirements: ['Log Retention', 'Access Monitoring'],\r\n    };\r\n\r\n    mockRetentionInfo = {\r\n      retentionDays: 2555, // 7 years\r\n      legalHold: false,\r\n      archivalRequired: true,\r\n      deletionDate: '2031-01-01T00:00:00Z',\r\n      policyId: 'retention-policy-001',\r\n      justification: 'Regulatory compliance requirement',\r\n    };\r\n\r\n    mockSecurityEventData = {\r\n      version: '1.0.0',\r\n      externalId: 'ext-event-123',\r\n      sourceSystem: 'Test SIEM',\r\n      sourceType: EventSourceType.SIEM,\r\n      timestamp: '2024-01-01T00:00:00Z',\r\n      type: EventType.LOGIN_FAILURE,\r\n      severity: EventSeverity.HIGH,\r\n      status: EventStatus.OPEN,\r\n      processingStatus: EventProcessingStatus.NORMALIZED,\r\n      title: 'Failed Login Attempt',\r\n      description: 'Multiple failed login attempts detected',\r\n      rawData: { original: 'raw event data' },\r\n      normalizedData: { normalized: 'processed data' },\r\n      tags: ['authentication', 'security'],\r\n      riskScore: 85,\r\n      confidenceLevel: 90,\r\n      attributes: { custom: 'attribute' },\r\n      correlationId: 'corr-123',\r\n      parentEventId: 'parent-456',\r\n      sourceMetadata: mockSourceMetadata,\r\n      networkInfo: mockNetworkInfo,\r\n      assetInfo: mockAssetInfo,\r\n      userInfo: mockUserInfo,\r\n      geoLocation: mockGeoLocation,\r\n      threatIndicators: mockThreatIndicators,\r\n      complianceInfo: mockComplianceInfo,\r\n      retentionInfo: mockRetentionInfo,\r\n    };\r\n  });\r\n\r\n  describe('SecurityEventData Structure', () => {\r\n    it('should have all required fields', () => {\r\n      expect(mockSecurityEventData.version).toBeDefined();\r\n      expect(mockSecurityEventData.externalId).toBeDefined();\r\n      expect(mockSecurityEventData.sourceSystem).toBeDefined();\r\n      expect(mockSecurityEventData.sourceType).toBeDefined();\r\n      expect(mockSecurityEventData.timestamp).toBeDefined();\r\n      expect(mockSecurityEventData.type).toBeDefined();\r\n      expect(mockSecurityEventData.severity).toBeDefined();\r\n      expect(mockSecurityEventData.title).toBeDefined();\r\n      expect(mockSecurityEventData.rawData).toBeDefined();\r\n      expect(mockSecurityEventData.sourceMetadata).toBeDefined();\r\n    });\r\n\r\n    it('should support versioning', () => {\r\n      expect(mockSecurityEventData.version).toBe('1.0.0');\r\n      expect(typeof mockSecurityEventData.version).toBe('string');\r\n    });\r\n\r\n    it('should be serializable to JSON', () => {\r\n      const jsonString = JSON.stringify(mockSecurityEventData);\r\n      expect(jsonString).toBeDefined();\r\n      expect(jsonString.length).toBeGreaterThan(0);\r\n\r\n      const parsed = JSON.parse(jsonString);\r\n      expect(parsed.version).toBe(mockSecurityEventData.version);\r\n      expect(parsed.externalId).toBe(mockSecurityEventData.externalId);\r\n    });\r\n\r\n    it('should maintain data integrity after serialization', () => {\r\n      const jsonString = JSON.stringify(mockSecurityEventData);\r\n      const parsed: SecurityEventData = JSON.parse(jsonString);\r\n\r\n      expect(parsed.version).toBe(mockSecurityEventData.version);\r\n      expect(parsed.externalId).toBe(mockSecurityEventData.externalId);\r\n      expect(parsed.sourceSystem).toBe(mockSecurityEventData.sourceSystem);\r\n      expect(parsed.type).toBe(mockSecurityEventData.type);\r\n      expect(parsed.severity).toBe(mockSecurityEventData.severity);\r\n      expect(parsed.sourceMetadata.systemName).toBe(mockSecurityEventData.sourceMetadata.systemName);\r\n    });\r\n  });\r\n\r\n  describe('SecurityEventSourceMetadata', () => {\r\n    it('should contain required source information', () => {\r\n      expect(mockSourceMetadata.systemName).toBe('Test SIEM');\r\n      expect(mockSourceMetadata.dataFormat).toBe('json');\r\n      expect(mockSourceMetadata.collectedAt).toBeDefined();\r\n    });\r\n\r\n    it('should support reliability scoring', () => {\r\n      expect(mockSourceMetadata.reliability).toBe(95);\r\n      expect(typeof mockSourceMetadata.reliability).toBe('number');\r\n    });\r\n  });\r\n\r\n  describe('SecurityEventNetworkInfo', () => {\r\n    it('should contain network-related information', () => {\r\n      expect(mockNetworkInfo.sourceIp).toBe('*************');\r\n      expect(mockNetworkInfo.destinationIp).toBe('********');\r\n      expect(mockNetworkInfo.protocol).toBe('TCP');\r\n    });\r\n\r\n    it('should support flow direction classification', () => {\r\n      expect(['inbound', 'outbound', 'internal']).toContain(mockNetworkInfo.flowDirection);\r\n    });\r\n  });\r\n\r\n  describe('SecurityEventAssetInfo', () => {\r\n    it('should contain asset identification', () => {\r\n      expect(mockAssetInfo.assetId).toBe('asset-123');\r\n      expect(mockAssetInfo.assetName).toBe('Web Server 01');\r\n      expect(mockAssetInfo.assetType).toBe('server');\r\n    });\r\n\r\n    it('should support criticality levels', () => {\r\n      expect(['low', 'medium', 'high', 'critical']).toContain(mockAssetInfo.criticality);\r\n    });\r\n\r\n    it('should support environment classification', () => {\r\n      expect(['production', 'staging', 'development', 'test']).toContain(mockAssetInfo.environment);\r\n    });\r\n  });\r\n\r\n  describe('SecurityEventUserInfo', () => {\r\n    it('should contain user identification', () => {\r\n      expect(mockUserInfo.userId).toBe('user-123');\r\n      expect(mockUserInfo.username).toBe('john.doe');\r\n      expect(mockUserInfo.email).toBe('<EMAIL>');\r\n    });\r\n\r\n    it('should support role-based information', () => {\r\n      expect(Array.isArray(mockUserInfo.roles)).toBe(true);\r\n      expect(mockUserInfo.roles).toContain('admin');\r\n    });\r\n  });\r\n\r\n  describe('SecurityEventThreatIndicators', () => {\r\n    it('should contain threat intelligence data', () => {\r\n      expect(Array.isArray(mockThreatIndicators.iocs)).toBe(true);\r\n      expect(mockThreatIndicators.iocs).toContain('*************');\r\n    });\r\n\r\n    it('should support MITRE ATT&CK mapping', () => {\r\n      expect(Array.isArray(mockThreatIndicators.mitreAttackTechniques)).toBe(true);\r\n      expect(mockThreatIndicators.mitreAttackTechniques).toContain('T1055');\r\n    });\r\n  });\r\n\r\n  describe('SecurityEventComplianceInfo', () => {\r\n    it('should contain compliance framework information', () => {\r\n      expect(Array.isArray(mockComplianceInfo.frameworks)).toBe(true);\r\n      expect(mockComplianceInfo.frameworks).toContain('SOX');\r\n    });\r\n\r\n    it('should support privacy impact assessment', () => {\r\n      expect(['none', 'low', 'medium', 'high']).toContain(mockComplianceInfo.privacyImpact);\r\n    });\r\n  });\r\n\r\n  describe('SecurityEventRetentionInfo', () => {\r\n    it('should contain retention requirements', () => {\r\n      expect(typeof mockRetentionInfo.retentionDays).toBe('number');\r\n      expect(mockRetentionInfo.retentionDays).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should support legal hold status', () => {\r\n      expect(typeof mockRetentionInfo.legalHold).toBe('boolean');\r\n    });\r\n  });\r\n\r\n  describe('Data Validation Interface', () => {\r\n    it('should define validation methods', () => {\r\n      // Mock validator implementation\r\n      const mockValidator: SecurityEventDataValidator = {\r\n        validate: jest.fn().mockReturnValue({\r\n          isValid: true,\r\n          errors: [],\r\n          warnings: [],\r\n          sanitizedData: mockSecurityEventData,\r\n        }),\r\n        validateVersion: jest.fn().mockReturnValue(true),\r\n        sanitize: jest.fn().mockReturnValue(mockSecurityEventData),\r\n      };\r\n\r\n      expect(mockValidator.validate).toBeDefined();\r\n      expect(mockValidator.validateVersion).toBeDefined();\r\n      expect(mockValidator.sanitize).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('Data Serialization Interface', () => {\r\n    it('should define serialization methods', () => {\r\n      // Mock serializer implementation\r\n      const mockSerializer: SecurityEventDataSerializer = {\r\n        toJson: jest.fn().mockReturnValue('{}'),\r\n        fromJson: jest.fn().mockReturnValue(mockSecurityEventData),\r\n        toBinary: jest.fn().mockReturnValue(Buffer.from('test')),\r\n        fromBinary: jest.fn().mockReturnValue(mockSecurityEventData),\r\n        getSupportedVersions: jest.fn().mockReturnValue(['1.0.0']),\r\n      };\r\n\r\n      expect(mockSerializer.toJson).toBeDefined();\r\n      expect(mockSerializer.fromJson).toBeDefined();\r\n      expect(mockSerializer.toBinary).toBeDefined();\r\n      expect(mockSerializer.fromBinary).toBeDefined();\r\n      expect(mockSerializer.getSupportedVersions).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('Data Transformation Interface', () => {\r\n    it('should define transformation methods', () => {\r\n      // Mock transformer implementation\r\n      const mockTransformer: SecurityEventDataTransformer = {\r\n        transform: jest.fn().mockReturnValue(mockSecurityEventData),\r\n        transformToExternal: jest.fn().mockReturnValue({}),\r\n        getSupportedSourceTypes: jest.fn().mockReturnValue([EventSourceType.SIEM]),\r\n        getSupportedTargetFormats: jest.fn().mockReturnValue(['json', 'xml']),\r\n      };\r\n\r\n      expect(mockTransformer.transform).toBeDefined();\r\n      expect(mockTransformer.transformToExternal).toBeDefined();\r\n      expect(mockTransformer.getSupportedSourceTypes).toBeDefined();\r\n      expect(mockTransformer.getSupportedTargetFormats).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('Version Compatibility', () => {\r\n    it('should support version checking', () => {\r\n      const versions = ['1.0.0', '1.1.0', '2.0.0'];\r\n      versions.forEach(version => {\r\n        const versionedData = { ...mockSecurityEventData, version };\r\n        expect(versionedData.version).toBe(version);\r\n      });\r\n    });\r\n\r\n    it('should maintain backward compatibility', () => {\r\n      const legacyData = {\r\n        ...mockSecurityEventData,\r\n        version: '0.9.0',\r\n      };\r\n\r\n      // Should still be valid SecurityEventData structure\r\n      expect(legacyData.version).toBeDefined();\r\n      expect(legacyData.externalId).toBeDefined();\r\n      expect(legacyData.sourceSystem).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('External Integration Support', () => {\r\n    it('should support multiple source types', () => {\r\n      const sourceTypes = Object.values(EventSourceType);\r\n      sourceTypes.forEach(sourceType => {\r\n        const eventData = { ...mockSecurityEventData, sourceType };\r\n        expect(eventData.sourceType).toBe(sourceType);\r\n      });\r\n    });\r\n\r\n    it('should support custom attributes', () => {\r\n      const customAttributes = {\r\n        customField1: 'value1',\r\n        customField2: 123,\r\n        customField3: { nested: 'object' },\r\n      };\r\n\r\n      const eventWithCustom = {\r\n        ...mockSecurityEventData,\r\n        attributes: customAttributes,\r\n      };\r\n\r\n      expect(eventWithCustom.attributes).toEqual(customAttributes);\r\n    });\r\n\r\n    it('should support extensible metadata', () => {\r\n      const extendedMetadata = {\r\n        ...mockSourceMetadata,\r\n        sourceIds: {\r\n          ...mockSourceMetadata.sourceIds,\r\n          'vendor_id': 'vendor-123',\r\n          'correlation_id': 'corr-456',\r\n        },\r\n      };\r\n\r\n      const eventWithExtended = {\r\n        ...mockSecurityEventData,\r\n        sourceMetadata: extendedMetadata,\r\n      };\r\n\r\n      expect(eventWithExtended.sourceMetadata.sourceIds).toHaveProperty('vendor_id');\r\n      expect(eventWithExtended.sourceMetadata.sourceIds).toHaveProperty('correlation_id');\r\n    });\r\n  });\r\n});"], "version": 3}