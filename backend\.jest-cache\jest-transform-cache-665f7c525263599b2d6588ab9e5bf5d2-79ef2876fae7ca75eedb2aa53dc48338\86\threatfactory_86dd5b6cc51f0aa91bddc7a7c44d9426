7b12ad2d64a6097ca2d84600ce44c968
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThreatFactory = void 0;
const threat_entity_1 = require("../entities/threat/threat.entity");
const threat_severity_enum_1 = require("../enums/threat-severity.enum");
const ioc_value_object_1 = require("../value-objects/threat-indicators/ioc.value-object");
/**
 * Threat Factory
 *
 * Factory class for creating Threat entities with proper validation and defaults.
 * Handles complex threat creation scenarios and ensures all business rules are applied.
 *
 * Key responsibilities:
 * - Create threats from various input formats
 * - Apply default values and business rules
 * - Validate threat data consistency
 * - Generate proper risk assessments
 * - Handle threat relationships and correlations
 */
class ThreatFactory {
    /**
     * Create a new Threat with the provided options
     */
    static create(options) {
        return threat_entity_1.Threat.create(options.name, options.description, options.severity, options.category, options.type, options.confidence, {
            subcategory: options.subcategory,
            indicators: options.indicators,
            cvssScores: options.cvssScores,
            attribution: options.attribution,
            malwareFamily: options.malwareFamily,
            techniques: options.techniques,
            affectedAssets: options.affectedAssets,
            tags: options.tags,
            attributes: options.attributes,
        });
    }
    /**
     * Create a Threat from threat intelligence data
     */
    static fromThreatIntelligence(threatData, options) {
        const severity = ThreatFactory.mapThreatSeverity(threatData.severity);
        const confidence = threatData.confidence || ThreatFactory.inferConfidence(threatData);
        // Create IOC from indicator
        const indicators = [];
        if (threatData.indicator) {
            const iocType = ThreatFactory.inferIOCType(threatData.indicatorType);
            const iocSeverity = ThreatFactory.mapIOCSeverity(severity);
            const iocConfidence = ThreatFactory.mapIOCConfidence(confidence);
            indicators.push(ioc_value_object_1.IOC.create(iocType, threatData.indicator, iocSeverity, iocConfidence));
        }
        // Build attribution if available
        const attribution = threatData.attribution ? {
            actor: threatData.attribution.actor || 'Unknown',
            confidence: confidence,
            aliases: [],
            motivation: [],
            capabilities: [],
            campaigns: threatData.attribution.campaign ? [threatData.attribution.campaign] : [],
        } : undefined;
        // Build malware family if available
        const malwareFamily = threatData.attribution?.malwareFamily ? {
            name: threatData.attribution.malwareFamily,
            confidence: confidence,
            capabilities: [],
            protocols: [],
            persistence: [],
        } : undefined;
        const name = `Threat Intelligence: ${threatData.indicator || 'Unknown Threat'}`;
        const description = threatData.description || `Threat detected from intelligence source: ${threatData.source || 'Unknown'}`;
        return ThreatFactory.create({
            name,
            description,
            severity,
            category: 'threat-intelligence',
            type: threatData.indicatorType || 'unknown',
            confidence,
            indicators,
            attribution,
            malwareFamily,
            tags: ['threat-intelligence', 'external'],
            attributes: {
                source: threatData.source,
                originalData: threatData.metadata,
            },
            ...options,
        });
    }
    /**
     * Create a Threat from malware analysis data
     */
    static fromMalwareAnalysis(malwareData, options) {
        const severity = ThreatFactory.inferMalwareSeverity(malwareData);
        const confidence = malwareData.confidence;
        // Create hash IOC
        const indicators = [];
        const iocType = ioc_value_object_1.IOCType.FILE_HASH; // All hashes use FILE_HASH type
        indicators.push(ioc_value_object_1.IOC.create(iocType, malwareData.hash, ThreatFactory.mapIOCSeverity(severity), ThreatFactory.mapIOCConfidence(confidence)));
        // Add network indicators
        if (malwareData.networkIndicators) {
            malwareData.networkIndicators.forEach(indicator => {
                const iocType = ThreatFactory.inferIOCTypeFromValue(indicator);
                indicators.push(ioc_value_object_1.IOC.create(iocType, indicator, ThreatFactory.mapIOCSeverity(severity), ThreatFactory.mapIOCConfidence(confidence)));
            });
        }
        // Build malware family
        const malwareFamily = malwareData.family ? {
            name: malwareData.family,
            variant: malwareData.variant,
            confidence: confidence,
            capabilities: malwareData.capabilities || [],
            protocols: malwareData.protocols || [],
            persistence: malwareData.persistence || [],
        } : undefined;
        const name = `Malware: ${malwareData.family || malwareData.hash.substring(0, 8)}`;
        const description = `Malware detected with hash ${malwareData.hash}`;
        return ThreatFactory.create({
            name,
            description,
            severity,
            category: 'malware',
            type: malwareData.family || 'unknown-malware',
            confidence,
            indicators,
            malwareFamily,
            affectedAssets: malwareData.affectedFiles,
            tags: ['malware', 'analysis'],
            attributes: {
                hash: malwareData.hash,
                hashType: malwareData.hashType,
                analysisData: malwareData,
            },
            ...options,
        });
    }
    /**
     * Create a Threat from attack pattern data
     */
    static fromAttackPattern(patternData, options) {
        const severity = ThreatFactory.inferAttackPatternSeverity(patternData);
        const confidence = patternData.confidence;
        // Build attack technique
        const techniques = [{
                id: patternData.techniqueId || 'UNKNOWN',
                name: patternData.name,
                tactic: patternData.tactic,
                description: patternData.description,
                confidence: confidence,
                evidence: patternData.evidence,
            }];
        const name = `Attack Pattern: ${patternData.name}`;
        const description = patternData.description;
        return ThreatFactory.create({
            name,
            description,
            severity,
            category: 'attack-pattern',
            type: patternData.tactic.toLowerCase().replace(/\s+/g, '-'),
            confidence,
            techniques,
            affectedAssets: patternData.affectedAssets,
            tags: ['attack-pattern', 'mitre-attack'],
            attributes: {
                techniqueId: patternData.techniqueId,
                tactic: patternData.tactic,
                evidence: patternData.evidence,
            },
            ...options,
        });
    }
    /**
     * Create a high-severity threat alert
     */
    static createHighSeverityAlert(name, description, category, type, options) {
        return ThreatFactory.create({
            name,
            description,
            severity: threat_severity_enum_1.ThreatSeverity.HIGH,
            category,
            type,
            confidence: 85, // Default high confidence for alerts
            tags: ['high-severity', 'alert'],
            ...options,
        });
    }
    /**
     * Create a critical threat alert
     */
    static createCriticalAlert(name, description, category, type, options) {
        return ThreatFactory.create({
            name,
            description,
            severity: threat_severity_enum_1.ThreatSeverity.CRITICAL,
            category,
            type,
            confidence: 95, // Default critical confidence
            tags: ['critical', 'alert', 'immediate-response'],
            ...options,
        });
    }
    /**
     * Create a threat from security event correlation
     */
    static fromEventCorrelation(correlatedEvents, // Would be Event[] in real implementation
    name, description, options) {
        // Infer severity from correlated events
        const severity = ThreatFactory.inferSeverityFromEvents(correlatedEvents);
        const confidence = ThreatFactory.calculateCorrelationConfidence(correlatedEvents);
        // Extract affected assets from events
        const affectedAssets = ThreatFactory.extractAffectedAssets(correlatedEvents);
        // Generate techniques from event patterns
        const techniques = ThreatFactory.generateTechniquesFromEvents(correlatedEvents);
        return ThreatFactory.create({
            name,
            description,
            severity,
            category: 'correlated-threat',
            type: 'multi-stage-attack',
            confidence,
            techniques,
            affectedAssets,
            tags: ['correlated', 'multi-stage'],
            attributes: {
                correlatedEventCount: correlatedEvents.length,
                correlationTimestamp: new Date().toISOString(),
            },
            ...options,
        });
    }
    /**
     * Create a manual threat (created by analyst)
     */
    static createManualThreat(name, description, severity, category, type, confidence, createdBy, options) {
        return ThreatFactory.create({
            name,
            description,
            severity,
            category,
            type,
            confidence,
            tags: ['manual', 'analyst-created'],
            attributes: {
                createdBy,
                createdAt: new Date().toISOString(),
                source: 'manual-analysis',
            },
            ...options,
        });
    }
    // Helper methods for data mapping and inference
    static mapThreatSeverity(severity) {
        if (typeof severity === 'string') {
            const sev = severity.toLowerCase();
            if (sev.includes('critical') || sev.includes('fatal'))
                return threat_severity_enum_1.ThreatSeverity.CRITICAL;
            if (sev.includes('high') || sev.includes('severe'))
                return threat_severity_enum_1.ThreatSeverity.HIGH;
            if (sev.includes('medium') || sev.includes('moderate'))
                return threat_severity_enum_1.ThreatSeverity.MEDIUM;
            if (sev.includes('low') || sev.includes('minor'))
                return threat_severity_enum_1.ThreatSeverity.LOW;
        }
        if (typeof severity === 'number') {
            if (severity >= 9)
                return threat_severity_enum_1.ThreatSeverity.CRITICAL;
            if (severity >= 7)
                return threat_severity_enum_1.ThreatSeverity.HIGH;
            if (severity >= 4)
                return threat_severity_enum_1.ThreatSeverity.MEDIUM;
            if (severity >= 1)
                return threat_severity_enum_1.ThreatSeverity.LOW;
        }
        return threat_severity_enum_1.ThreatSeverity.UNKNOWN;
    }
    static inferConfidence(threatData) {
        let confidence = 50; // Base confidence
        // Increase confidence based on data quality
        if (threatData.source)
            confidence += 10;
        if (threatData.attribution?.actor)
            confidence += 15;
        if (threatData.attribution?.campaign)
            confidence += 10;
        if (threatData.description && threatData.description.length > 50)
            confidence += 10;
        return Math.min(100, confidence);
    }
    static inferIOCType(indicatorType) {
        const type = indicatorType.toLowerCase();
        if (type.includes('ip') || type.includes('address'))
            return ioc_value_object_1.IOCType.IP_ADDRESS;
        if (type.includes('domain') || type.includes('hostname'))
            return ioc_value_object_1.IOCType.DOMAIN;
        if (type.includes('url') || type.includes('uri'))
            return ioc_value_object_1.IOCType.URL;
        if (type.includes('email'))
            return ioc_value_object_1.IOCType.EMAIL;
        if (type.includes('hash') || type.includes('md5') || type.includes('sha'))
            return ioc_value_object_1.IOCType.FILE_HASH;
        if (type.includes('file') || type.includes('filename'))
            return ioc_value_object_1.IOCType.FILE_PATH;
        return ioc_value_object_1.IOCType.IP_ADDRESS; // Default fallback
    }
    static inferIOCTypeFromValue(value) {
        // IP address pattern
        if (/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/.test(value)) {
            return ioc_value_object_1.IOCType.IP_ADDRESS;
        }
        // Domain pattern
        if (/^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/.test(value)) {
            return ioc_value_object_1.IOCType.DOMAIN;
        }
        // URL pattern
        if (/^https?:\/\//.test(value)) {
            return ioc_value_object_1.IOCType.URL;
        }
        // Email pattern
        if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
            return ioc_value_object_1.IOCType.EMAIL;
        }
        // Hash patterns - all use FILE_HASH
        if (/^[a-fA-F0-9]{32}$/.test(value))
            return ioc_value_object_1.IOCType.FILE_HASH;
        if (/^[a-fA-F0-9]{40}$/.test(value))
            return ioc_value_object_1.IOCType.FILE_HASH;
        if (/^[a-fA-F0-9]{64}$/.test(value))
            return ioc_value_object_1.IOCType.FILE_HASH;
        return ioc_value_object_1.IOCType.IP_ADDRESS; // Default fallback
    }
    static mapIOCSeverity(threatSeverity) {
        switch (threatSeverity) {
            case threat_severity_enum_1.ThreatSeverity.CRITICAL: return ioc_value_object_1.IOCSeverity.CRITICAL;
            case threat_severity_enum_1.ThreatSeverity.HIGH: return ioc_value_object_1.IOCSeverity.HIGH;
            case threat_severity_enum_1.ThreatSeverity.MEDIUM: return ioc_value_object_1.IOCSeverity.MEDIUM;
            case threat_severity_enum_1.ThreatSeverity.LOW: return ioc_value_object_1.IOCSeverity.LOW;
            default: return ioc_value_object_1.IOCSeverity.LOW;
        }
    }
    static mapIOCConfidence(confidence) {
        if (confidence >= 90)
            return ioc_value_object_1.IOCConfidence.CONFIRMED;
        if (confidence >= 70)
            return ioc_value_object_1.IOCConfidence.HIGH;
        if (confidence >= 50)
            return ioc_value_object_1.IOCConfidence.MEDIUM;
        return ioc_value_object_1.IOCConfidence.LOW;
    }
    static inferMalwareSeverity(malwareData) {
        let score = 0;
        // Base score from capabilities
        if (malwareData.capabilities) {
            score += malwareData.capabilities.length * 10;
        }
        // Increase score for persistence mechanisms
        if (malwareData.persistence && malwareData.persistence.length > 0) {
            score += 20;
        }
        // Increase score for network communication
        if (malwareData.protocols && malwareData.protocols.length > 0) {
            score += 15;
        }
        // Increase score for multiple affected files
        if (malwareData.affectedFiles && malwareData.affectedFiles.length > 5) {
            score += 15;
        }
        if (score >= 60)
            return threat_severity_enum_1.ThreatSeverity.CRITICAL;
        if (score >= 40)
            return threat_severity_enum_1.ThreatSeverity.HIGH;
        if (score >= 20)
            return threat_severity_enum_1.ThreatSeverity.MEDIUM;
        return threat_severity_enum_1.ThreatSeverity.LOW;
    }
    static inferAttackPatternSeverity(patternData) {
        const tactic = patternData.tactic.toLowerCase();
        // High-impact tactics
        if (tactic.includes('impact') || tactic.includes('exfiltration')) {
            return threat_severity_enum_1.ThreatSeverity.CRITICAL;
        }
        // Medium-high impact tactics
        if (tactic.includes('lateral') || tactic.includes('privilege') || tactic.includes('persistence')) {
            return threat_severity_enum_1.ThreatSeverity.HIGH;
        }
        // Medium impact tactics
        if (tactic.includes('execution') || tactic.includes('defense')) {
            return threat_severity_enum_1.ThreatSeverity.MEDIUM;
        }
        // Lower impact tactics
        return threat_severity_enum_1.ThreatSeverity.LOW;
    }
    static inferSeverityFromEvents(events) {
        // This would analyze actual Event entities in real implementation
        if (events.length >= 10)
            return threat_severity_enum_1.ThreatSeverity.CRITICAL;
        if (events.length >= 5)
            return threat_severity_enum_1.ThreatSeverity.HIGH;
        if (events.length >= 2)
            return threat_severity_enum_1.ThreatSeverity.MEDIUM;
        return threat_severity_enum_1.ThreatSeverity.LOW;
    }
    static calculateCorrelationConfidence(events) {
        // Base confidence from number of events
        let confidence = Math.min(events.length * 10, 70);
        // Increase confidence for time proximity
        confidence += 10;
        // Increase confidence for asset overlap
        confidence += 10;
        return Math.min(100, confidence);
    }
    static extractAffectedAssets(events) {
        // This would extract assets from actual Event entities
        return [`asset-${events.length}`];
    }
    static generateTechniquesFromEvents(events) {
        // This would analyze event patterns to infer techniques
        return [{
                id: 'T1000',
                name: 'Multi-stage Attack',
                tactic: 'Multiple',
                description: `Correlated attack involving ${events.length} events`,
                confidence: 80,
                evidence: [`${events.length} correlated events`],
            }];
    }
}
exports.ThreatFactory = ThreatFactory;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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