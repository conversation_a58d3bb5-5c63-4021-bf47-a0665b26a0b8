import {
  Injectable,
  CanActivate,
  ExecutionContext,
  BadRequestException,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request, Response } from 'express';

import { ApiVersioningService } from './api-versioning.service';
import { API_VERSION_KEY, ApiVersionConfig } from './api-version.decorator';

/**
 * Guard to validate and enforce API versioning
 * Checks version compatibility, deprecation status, and version requirements
 */
@Injectable()
export class ApiVersionGuard implements CanActivate {
  private readonly logger = new Logger(ApiVersionGuard.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly versioningService: ApiVersioningService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();

    try {
      // Extract version from request
      const requestedVersion = this.versioningService.extractVersion(request);
      
      // Get version metadata from controller/method
      const versionConfig = this.getVersionConfig(context);
      
      // Validate version
      await this.validateVersion(requestedVersion, versionConfig, request, response);
      
      // Store version in request for later use
      (request as any).apiVersion = requestedVersion;
      
      // Log version usage
      await this.versioningService.logVersionUsage(
        requestedVersion,
        request.path,
        (request as any).user?.id,
      );

      return true;

    } catch (error) {
      this.logger.error('API version validation failed', {
        path: request.path,
        method: request.method,
        error: error instanceof Error ? error.message : String(error),
        headers: request.headers,
      });

      if (error instanceof HttpException) {
        throw error;
      }

      throw new BadRequestException('Invalid API version');
    }
  }

  /**
   * Get version configuration from metadata
   */
  private getVersionConfig(context: ExecutionContext): ApiVersionConfig | null {
    // Check method-level metadata first
    const methodConfig = this.reflector.get<ApiVersionConfig>(
      API_VERSION_KEY,
      context.getHandler(),
    );

    if (methodConfig) {
      return methodConfig;
    }

    // Check class-level metadata
    const classConfig = this.reflector.get<ApiVersionConfig>(
      API_VERSION_KEY,
      context.getClass(),
    );

    return classConfig || null;
  }

  /**
   * Validate API version against requirements
   */
  private async validateVersion(
    requestedVersion: string,
    versionConfig: ApiVersionConfig | null,
    request: Request,
    response: Response,
  ): Promise<void> {
    // Check if version is supported
    if (!this.versioningService.validateVersion(requestedVersion)) {
      const supportedVersions = this.versioningService.getSupportedVersions()
        .map(v => v.version)
        .join(', ');

      throw new BadRequestException(
        `Unsupported API version: ${requestedVersion}. Supported versions: ${supportedVersions}`,
      );
    }

    // Get version information
    const versionInfo = this.versioningService.getVersionInfo(requestedVersion);
    
    if (!versionInfo) {
      throw new BadRequestException(`Invalid API version: ${requestedVersion}`);
    }

    // Check if version is sunset
    if (versionInfo.status === 'sunset') {
      throw new HttpException(
        {
          statusCode: HttpStatus.GONE,
          message: `API version ${requestedVersion} is no longer supported`,
          error: 'Version Sunset',
          sunsetDate: versionInfo.sunsetDate,
          replacement: this.getReplacementVersion(requestedVersion),
        },
        HttpStatus.GONE,
      );
    }

    // Handle deprecated versions
    if (versionInfo.status === 'deprecated') {
      this.addDeprecationHeaders(response, requestedVersion, versionInfo);
    }

    // Check version-specific requirements
    if (versionConfig) {
      await this.validateVersionConfig(requestedVersion, versionConfig, request, response);
    }

    // Check min/max version requirements
    await this.validateVersionRange(requestedVersion, request, response);

    // Check experimental features
    await this.validateExperimentalFeatures(requestedVersion, request, response);
  }

  /**
   * Validate version against specific configuration
   */
  private async validateVersionConfig(
    requestedVersion: string,
    config: ApiVersionConfig,
    request: Request,
    response: Response,
  ): Promise<void> {
    // Check if specific version is required
    if (config.version && config.version !== 'deprecated' && config.version !== requestedVersion) {
      // Allow compatible versions (same major version)
      const compatibility = this.versioningService.checkCompatibility(
        requestedVersion,
        config.version,
      );

      if (!compatibility.compatible) {
        throw new BadRequestException(
          `This endpoint requires API version ${config.version}. ` +
          `Version ${requestedVersion} is not compatible.`,
        );
      }

      // Add compatibility warning headers
      if (compatibility.migrationRequired) {
        response.setHeader('X-API-Compatibility-Warning', 
          `Migration required from v${requestedVersion} to v${config.version}`);
        response.setHeader('X-API-Breaking-Changes', 
          compatibility.breakingChanges.join(', '));
      }
    }

    // Handle deprecated endpoint
    if (config.deprecated) {
      this.addDeprecationHeaders(response, requestedVersion, config);
      
      // Log deprecated endpoint usage
      this.logger.warn('Deprecated endpoint accessed', {
        version: requestedVersion,
        endpoint: request.path,
        deprecationDate: config.deprecationDate,
        sunsetDate: config.sunsetDate,
        replacement: config.replacement,
      });
    }
  }

  /**
   * Validate version range requirements
   */
  private async validateVersionRange(
    requestedVersion: string,
    request: Request,
    response: Response,
  ): Promise<void> {
    const context = this.getCurrentExecutionContext(request);
    
    if (!context) return;

    // Check minimum version requirement
    const minVersion = this.reflector.get<string>('min_version', context.getHandler()) ||
                      this.reflector.get<string>('min_version', context.getClass());

    if (minVersion && this.compareVersions(requestedVersion, minVersion) < 0) {
      throw new BadRequestException(
        `This endpoint requires minimum API version ${minVersion}. ` +
        `Current version: ${requestedVersion}`,
      );
    }

    // Check maximum version requirement
    const maxVersion = this.reflector.get<string>('max_version', context.getHandler()) ||
                      this.reflector.get<string>('max_version', context.getClass());

    if (maxVersion && this.compareVersions(requestedVersion, maxVersion) > 0) {
      throw new BadRequestException(
        `This endpoint supports maximum API version ${maxVersion}. ` +
        `Current version: ${requestedVersion}`,
      );
    }
  }

  /**
   * Validate experimental features
   */
  private async validateExperimentalFeatures(
    requestedVersion: string,
    request: Request,
    response: Response,
  ): Promise<void> {
    const context = this.getCurrentExecutionContext(request);
    
    if (!context) return;

    const experimental = this.reflector.get<any>('experimental', context.getHandler()) ||
                        this.reflector.get<any>('experimental', context.getClass());

    if (experimental) {
      // Add experimental headers
      response.setHeader('X-API-Experimental', 'true');
      if (experimental.stabilityLevel) {
        response.setHeader('X-API-Stability-Level', experimental.stabilityLevel);
      }
      if (experimental.warning) {
        response.setHeader('X-API-Experimental-Warning', experimental.warning);
      }

      if (experimental.feedback) {
        response.setHeader('X-API-Feedback', experimental.feedback);
      }

      // Log experimental feature usage
      this.logger.log('Experimental feature accessed', {
        version: requestedVersion,
        endpoint: request.path,
        stabilityLevel: experimental.stabilityLevel || 'unknown',
      });
    }
  }

  /**
   * Add deprecation headers to response
   */
  private addDeprecationHeaders(
    response: Response,
    version: string,
    config: ApiVersionConfig,
  ): void {
    response.setHeader('X-API-Deprecated', 'true');
    response.setHeader('X-API-Deprecated-Version', version);
    
    if (config.deprecationDate) {
      response.setHeader('X-API-Deprecation-Date', config.deprecationDate.toISOString());
    }
    
    if (config.sunsetDate) {
      response.setHeader('X-API-Sunset-Date', config.sunsetDate.toISOString());
    }
    
    if (config.replacement) {
      response.setHeader('X-API-Replacement', config.replacement);
    }
    
    if (config.migrationGuide) {
      response.setHeader('X-API-Migration-Guide', config.migrationGuide);
    }

    // Add deprecation warnings
    const warnings = this.versioningService.getDeprecationWarnings(version);
    if (warnings.length > 0) {
      const warningMessages = warnings.map(w => 
        `${w.severity}: ${w.feature || 'Version'} deprecated on ${w.deprecationDate.toISOString()}`
      );
      response.setHeader('X-API-Deprecation-Warnings', warningMessages.join('; '));
    }
  }

  /**
   * Get replacement version for sunset version
   */
  private getReplacementVersion(sunsetVersion: string): string {
    // Logic to determine replacement version
    const supportedVersions = this.versioningService.getSupportedVersions()
      .filter(v => v.status === 'active')
      .sort((a, b) => this.compareVersions(b.version, a.version));

    return supportedVersions[0]?.version || 'latest';
  }

  /**
   * Compare two version strings
   */
  private compareVersions(a: string, b: string): number {
    const aParts = a.split('.').map(Number);
    const bParts = b.split('.').map(Number);
    
    for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {
      const aPart = aParts[i] || 0;
      const bPart = bParts[i] || 0;
      
      if (aPart > bPart) return 1;
      if (aPart < bPart) return -1;
    }
    
    return 0;
  }

  /**
   * Get current execution context (helper method)
   */
  private getCurrentExecutionContext(request: Request): ExecutionContext | null {
    // This is a simplified approach - in a real implementation,
    // you might need to store the context in the request or use a different approach
    return (request as any).executionContext || null;
  }
}