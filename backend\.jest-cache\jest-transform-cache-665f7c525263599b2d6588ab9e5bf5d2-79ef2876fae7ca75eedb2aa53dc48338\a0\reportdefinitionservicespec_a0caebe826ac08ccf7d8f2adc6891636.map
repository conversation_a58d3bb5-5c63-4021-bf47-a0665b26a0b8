{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\tests\\unit\\services\\report-definition.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,6CAAqD;AAErD,kFAA8E;AAC9E,wFAAoF;AACpF,2FAAsF;AACtF,yFAA8E;AAI9E;;;;;;;;;GASG;AACH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;IACvC,IAAI,OAAgC,CAAC;IACrC,IAAI,UAAqD,CAAC;IAC1D,IAAI,YAAuC,CAAC;IAC5C,IAAI,cAA2C,CAAC;IAEhD,MAAM,oBAAoB,GAAqB;QAC7C,EAAE,EAAE,gBAAgB;QACpB,IAAI,EAAE,aAAa;QACnB,WAAW,EAAE,yBAAyB;QACtC,QAAQ,EAAE,YAAY;QACtB,IAAI,EAAE,SAAS;QACf,UAAU,EAAE,YAAY;QACxB,KAAK,EAAE,+BAA+B;QACtC,UAAU,EAAE;YACV;gBACE,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE,MAAM;gBACZ,QAAQ,EAAE,IAAI;gBACd,YAAY,EAAE,IAAI;aACnB;SACF;QACD,aAAa,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC;QAC/B,QAAQ,EAAE;YACR,OAAO,EAAE,IAAI;YACb,cAAc,EAAE,WAAW;YAC3B,QAAQ,EAAE,KAAK;SAChB;QACD,QAAQ,EAAE,IAAI;QACd,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QACjC,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QACjC,SAAS,EAAE,WAAW;QACtB,SAAS,EAAE,WAAW;KACH,CAAC;IAEtB,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,cAAc,GAAG;YACrB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;YACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;YAClB,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;YACpB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;YACf,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;YACjB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;YACjB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;YACjB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;YAChB,kBAAkB,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;gBACjC,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACjC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACpC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBACnC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBAChC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;gBAChC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;gBAClB,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;aACpB,CAAC,CAAC;SACJ,CAAC;QAEF,MAAM,gBAAgB,GAAG;YACvB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;YACd,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;YACd,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;YACd,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;SACtB,CAAC;QAEF,MAAM,kBAAkB,GAAG;YACzB,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;YACxB,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE;YAC1B,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;SACvB,CAAC;QAEF,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,mDAAuB;gBACvB;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,2CAAgB,CAAC;oBAC7C,QAAQ,EAAE,cAAc;iBACzB;gBACD;oBACE,OAAO,EAAE,4BAAY;oBACrB,QAAQ,EAAE,gBAAgB;iBAC3B;gBACD;oBACE,OAAO,EAAE,gCAAc;oBACvB,QAAQ,EAAE,kBAAkB;iBAC7B;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAA0B,mDAAuB,CAAC,CAAC;QACvE,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC,IAAA,4BAAkB,EAAC,2CAAgB,CAAC,CAAC,CAAC;QAC9D,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,4BAAY,CAAC,CAAC;QACxC,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC,gCAAc,CAAC,CAAC;IAC9C,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,MAAM,SAAS,GAA8B;YAC3C,IAAI,EAAE,YAAY;YAClB,WAAW,EAAE,wBAAwB;YACrC,QAAQ,EAAE,YAAY;YACtB,IAAI,EAAE,SAAS;YACf,UAAU,EAAE,YAAY;YACxB,KAAK,EAAE,+BAA+B;YACtC,UAAU,EAAE,EAAE;YACd,aAAa,EAAE,CAAC,KAAK,CAAC;SACvB,CAAC;QAEF,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,MAAM,WAAW,GAAG,EAAE,GAAG,oBAAoB,EAAE,GAAG,SAAS,EAAE,CAAC;YAC9D,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,WAAkB,CAAC,CAAC;YACtD,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;YAE/C,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,sBAAsB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;YAE5E,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC;gBAC7C,GAAG,SAAS;gBACZ,SAAS,EAAE,WAAW;gBACtB,SAAS,EAAE,WAAW;gBACtB,QAAQ,EAAE,IAAI;aACf,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;YAC1D,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,CAAC;YAC7E,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,oBAAoB,CAAC,2BAA2B,EAAE;gBACrF,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,IAAI,EAAE,SAAS,CAAC,IAAI;aACrB,CAAC,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,UAAU,CAAC,SAAS,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;YAE7D,MAAM,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;iBACjE,OAAO,CAAC,OAAO,CAAC,iDAAiD,CAAC,CAAC;YAEtE,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,UAAU,GAAG,EAAE,GAAG,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;YAE9C,MAAM,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;iBAClE,OAAO,CAAC,OAAO,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,YAAY,CAAC,GAAG,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;YAEzD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;YAEvE,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAAC,kCAAkC,CAAC,CAAC;YAClF,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;YACpE,YAAY,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YACzC,UAAU,CAAC,SAAS,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;YAE7D,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;YAEvE,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAAC,kCAAkC,CAAC,CAAC;YAClF,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,oBAAoB,CAAC,EAAE,EAAE,EAAE,gBAAgB,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;YAC5F,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAC3C,kCAAkC,EAClC,oBAAoB,EACpB,IAAI,CACL,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACrE,YAAY,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YACzC,UAAU,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE7C,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,CAAC;YAExE,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;YAC1B,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,MAAM,SAAS,GAA8B;YAC3C,IAAI,EAAE,gBAAgB;YACtB,WAAW,EAAE,qBAAqB;SACnC,CAAC;QAEF,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,aAAa,GAAG,EAAE,GAAG,oBAAoB,EAAE,GAAG,SAAS,EAAE,CAAC;YAChE,UAAU,CAAC,SAAS,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;YAC7D,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAEjD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,sBAAsB,CAAC,gBAAgB,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;YAE9F,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,oBAAoB,CAAC,EAAE,EAAE,EAAE,gBAAgB,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;YAC5F,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC;gBAC3C,GAAG,oBAAoB;gBACvB,GAAG,SAAS;gBACZ,SAAS,EAAE,WAAW;gBACtB,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;aAC5B,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAAC,kCAAkC,CAAC,CAAC;YAClF,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,CAAC;YAC7E,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACrE,UAAU,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE7C,MAAM,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,iBAAiB,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;iBACpF,OAAO,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;YAElD,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,UAAU,CAAC,SAAS,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;YAC7D,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,GAAG,oBAAoB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC;YAEhF,MAAM,OAAO,CAAC,sBAAsB,CAAC,gBAAgB,EAAE,WAAW,CAAC,CAAC;YAEpE,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,oBAAoB,CAAC,EAAE,EAAE,EAAE,gBAAgB,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;YAC5F,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC;gBAC3C,GAAG,oBAAoB;gBACvB,QAAQ,EAAE,KAAK;gBACf,SAAS,EAAE,WAAW;gBACtB,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;aAC5B,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAAC,kCAAkC,CAAC,CAAC;YAClF,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACrE,UAAU,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE7C,MAAM,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC;iBACzE,OAAO,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;YAElD,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,MAAM,gBAAgB,GAAG;YACvB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;YACjC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;YACpC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;YACnC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;YAChC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;YAChC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;YAClB,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;SACpB,CAAC;QAEF,UAAU,CAAC,GAAG,EAAE;YACd,UAAU,CAAC,kBAAkB,CAAC,eAAe,CAAC,gBAAuB,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,KAAK,IAAI,EAAE;YACvE,MAAM,OAAO,GAAG;gBACd,QAAQ,EAAE,YAAY;gBACtB,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,MAAM;gBACd,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,EAAE;aACV,CAAC;YAEF,gBAAgB,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC;YACnE,gBAAgB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAE/C,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAE3D,MAAM,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YACrE,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,oBAAoB,CAAC,6BAA6B,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;YACvG,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,6BAA6B,EAAE,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC,CAAC;YAClH,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,qBAAqB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;YACnG,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CACpD,iEAAiE,EACjE,EAAE,MAAM,EAAE,QAAQ,EAAE,CACrB,CAAC;YACF,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;YAClF,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;YACtD,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;YAEvD,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,iBAAiB,EAAE,CAAC,oBAAoB,CAAC;gBACzC,KAAK,EAAE,CAAC;gBACR,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,EAAE;gBACT,UAAU,EAAE,CAAC;aACd,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;YACpE,gBAAgB,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC;YACnE,gBAAgB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAE/C,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;YAEtD,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,oBAAoB,CAAC,6BAA6B,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;YACvG,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;YACzD,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,MAAM,eAAe,GAAG;gBACtB,IAAI,EAAE,cAAc;gBACpB,UAAU,EAAE,YAAY;gBACxB,KAAK,EAAE,+BAA+B;gBACtC,UAAU,EAAE,EAAE;aACf,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,wBAAwB,CAAC,eAAe,CAAC,CAAC;YAEvE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;YACtE,MAAM,iBAAiB,GAAG;gBACxB,IAAI,EAAE,EAAE;gBACR,UAAU,EAAE,gBAAgB;gBAC5B,KAAK,EAAE,EAAE;gBACT,UAAU,EAAE,EAAE;aACf,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,CAAC;YAEzE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,UAAU,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC,CAAC;YAEhF,MAAM,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;iBACrD,OAAO,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAC;YAEjD,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,oBAAoB,CAAC,yBAAyB,EAAE;gBACnF,SAAS,EAAE,KAAK;gBAChB,KAAK,EAAE,4BAA4B;aACpC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,YAAY,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC;YACnE,UAAU,CAAC,SAAS,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;YAE7D,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;YAEhE,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;YAC7C,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,gBAAgB,EAAE,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,UAAU,CAAC,SAAS,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;YAE7D,MAAM,OAAO,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;YAEjD,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,oBAAoB,CAAC,4BAA4B,EAAE;gBACtF,EAAE,EAAE,SAAS;gBACb,MAAM,EAAE,KAAK;aACd,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,YAAY,CAAC,GAAG,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;YAEzD,MAAM,OAAO,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;YAEjD,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,oBAAoB,CAAC,4BAA4B,EAAE;gBACtF,EAAE,EAAE,SAAS;gBACb,MAAM,EAAE,IAAI;aACb,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\tests\\unit\\services\\report-definition.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { getRepositoryToken } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\nimport { CacheService } from '../../../../infrastructure/cache/cache.service';\r\nimport { MetricsService } from '../../../../infrastructure/metrics/metrics.service';\r\nimport { ReportDefinitionService } from '../../../services/report-definition.service';\r\nimport { ReportDefinition } from '../../../entities/report-definition.entity';\r\nimport { CreateReportDefinitionDto } from '../../../dto/create-report-definition.dto';\r\nimport { UpdateReportDefinitionDto } from '../../../dto/update-report-definition.dto';\r\n\r\n/**\r\n * Unit Tests for Report Definition Service\r\n * \r\n * Tests all service methods including:\r\n * - CRUD operations with validation\r\n * - Business logic and error handling\r\n * - Cache integration and invalidation\r\n * - Metrics collection and monitoring\r\n * - Data transformation and validation\r\n */\r\ndescribe('ReportDefinitionService', () => {\r\n  let service: ReportDefinitionService;\r\n  let repository: jest.Mocked<Repository<ReportDefinition>>;\r\n  let cacheService: jest.Mocked<CacheService>;\r\n  let metricsService: jest.Mocked<MetricsService>;\r\n\r\n  const mockReportDefinition: ReportDefinition = {\r\n    id: 'test-report-id',\r\n    name: 'Test Report',\r\n    description: 'Test report description',\r\n    category: 'compliance',\r\n    type: 'tabular',\r\n    dataSource: 'compliance',\r\n    query: 'SELECT * FROM compliance_data',\r\n    parameters: [\r\n      {\r\n        name: 'startDate',\r\n        type: 'date',\r\n        required: true,\r\n        defaultValue: null,\r\n      },\r\n    ],\r\n    outputFormats: ['pdf', 'excel'],\r\n    schedule: {\r\n      enabled: true,\r\n      cronExpression: '0 9 * * 1',\r\n      timezone: 'UTC',\r\n    },\r\n    isActive: true,\r\n    createdAt: new Date('2024-01-01'),\r\n    updatedAt: new Date('2024-01-01'),\r\n    createdBy: 'test-user',\r\n    updatedBy: 'test-user',\r\n  } as ReportDefinition;\r\n\r\n  beforeEach(async () => {\r\n    const mockRepository = {\r\n      find: jest.fn(),\r\n      findOne: jest.fn(),\r\n      findOneBy: jest.fn(),\r\n      save: jest.fn(),\r\n      update: jest.fn(),\r\n      delete: jest.fn(),\r\n      create: jest.fn(),\r\n      count: jest.fn(),\r\n      createQueryBuilder: jest.fn(() => ({\r\n        where: jest.fn().mockReturnThis(),\r\n        andWhere: jest.fn().mockReturnThis(),\r\n        orderBy: jest.fn().mockReturnThis(),\r\n        skip: jest.fn().mockReturnThis(),\r\n        take: jest.fn().mockReturnThis(),\r\n        getMany: jest.fn(),\r\n        getCount: jest.fn(),\r\n      })),\r\n    };\r\n\r\n    const mockCacheService = {\r\n      get: jest.fn(),\r\n      set: jest.fn(),\r\n      del: jest.fn(),\r\n      delPattern: jest.fn(),\r\n    };\r\n\r\n    const mockMetricsService = {\r\n      recordCounter: jest.fn(),\r\n      recordHistogram: jest.fn(),\r\n      recordGauge: jest.fn(),\r\n    };\r\n\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        ReportDefinitionService,\r\n        {\r\n          provide: getRepositoryToken(ReportDefinition),\r\n          useValue: mockRepository,\r\n        },\r\n        {\r\n          provide: CacheService,\r\n          useValue: mockCacheService,\r\n        },\r\n        {\r\n          provide: MetricsService,\r\n          useValue: mockMetricsService,\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    service = module.get<ReportDefinitionService>(ReportDefinitionService);\r\n    repository = module.get(getRepositoryToken(ReportDefinition));\r\n    cacheService = module.get(CacheService);\r\n    metricsService = module.get(MetricsService);\r\n  });\r\n\r\n  afterEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('createReportDefinition', () => {\r\n    const createDto: CreateReportDefinitionDto = {\r\n      name: 'New Report',\r\n      description: 'New report description',\r\n      category: 'compliance',\r\n      type: 'tabular',\r\n      dataSource: 'compliance',\r\n      query: 'SELECT * FROM compliance_data',\r\n      parameters: [],\r\n      outputFormats: ['pdf'],\r\n    };\r\n\r\n    it('should create a new report definition successfully', async () => {\r\n      const savedReport = { ...mockReportDefinition, ...createDto };\r\n      repository.create.mockReturnValue(savedReport as any);\r\n      repository.save.mockResolvedValue(savedReport);\r\n\r\n      const result = await service.createReportDefinition(createDto, 'test-user');\r\n\r\n      expect(repository.create).toHaveBeenCalledWith({\r\n        ...createDto,\r\n        createdBy: 'test-user',\r\n        updatedBy: 'test-user',\r\n        isActive: true,\r\n      });\r\n      expect(repository.save).toHaveBeenCalledWith(savedReport);\r\n      expect(cacheService.delPattern).toHaveBeenCalledWith('report_definitions_*');\r\n      expect(metricsService.recordCounter).toHaveBeenCalledWith('report_definition_created', {\r\n        category: createDto.category,\r\n        type: createDto.type,\r\n      });\r\n      expect(result).toEqual(savedReport);\r\n    });\r\n\r\n    it('should throw error for duplicate report name', async () => {\r\n      repository.findOneBy.mockResolvedValue(mockReportDefinition);\r\n\r\n      await expect(service.createReportDefinition(createDto, 'test-user'))\r\n        .rejects.toThrow('Report definition with this name already exists');\r\n\r\n      expect(repository.save).not.toHaveBeenCalled();\r\n    });\r\n\r\n    it('should validate required parameters', async () => {\r\n      const invalidDto = { ...createDto, name: '' };\r\n\r\n      await expect(service.createReportDefinition(invalidDto, 'test-user'))\r\n        .rejects.toThrow();\r\n    });\r\n  });\r\n\r\n  describe('getReportDefinitionById', () => {\r\n    it('should return cached report definition if available', async () => {\r\n      cacheService.get.mockResolvedValue(mockReportDefinition);\r\n\r\n      const result = await service.getReportDefinitionById('test-report-id');\r\n\r\n      expect(cacheService.get).toHaveBeenCalledWith('report_definition_test-report-id');\r\n      expect(repository.findOneBy).not.toHaveBeenCalled();\r\n      expect(result).toEqual(mockReportDefinition);\r\n    });\r\n\r\n    it('should fetch from database and cache if not in cache', async () => {\r\n      cacheService.get.mockResolvedValue(null);\r\n      repository.findOneBy.mockResolvedValue(mockReportDefinition);\r\n\r\n      const result = await service.getReportDefinitionById('test-report-id');\r\n\r\n      expect(cacheService.get).toHaveBeenCalledWith('report_definition_test-report-id');\r\n      expect(repository.findOneBy).toHaveBeenCalledWith({ id: 'test-report-id', isActive: true });\r\n      expect(cacheService.set).toHaveBeenCalledWith(\r\n        'report_definition_test-report-id',\r\n        mockReportDefinition,\r\n        3600\r\n      );\r\n      expect(result).toEqual(mockReportDefinition);\r\n    });\r\n\r\n    it('should return null for non-existent report definition', async () => {\r\n      cacheService.get.mockResolvedValue(null);\r\n      repository.findOneBy.mockResolvedValue(null);\r\n\r\n      const result = await service.getReportDefinitionById('non-existent-id');\r\n\r\n      expect(result).toBeNull();\r\n      expect(cacheService.set).not.toHaveBeenCalled();\r\n    });\r\n  });\r\n\r\n  describe('updateReportDefinition', () => {\r\n    const updateDto: UpdateReportDefinitionDto = {\r\n      name: 'Updated Report',\r\n      description: 'Updated description',\r\n    };\r\n\r\n    it('should update report definition successfully', async () => {\r\n      const updatedReport = { ...mockReportDefinition, ...updateDto };\r\n      repository.findOneBy.mockResolvedValue(mockReportDefinition);\r\n      repository.save.mockResolvedValue(updatedReport);\r\n\r\n      const result = await service.updateReportDefinition('test-report-id', updateDto, 'test-user');\r\n\r\n      expect(repository.findOneBy).toHaveBeenCalledWith({ id: 'test-report-id', isActive: true });\r\n      expect(repository.save).toHaveBeenCalledWith({\r\n        ...mockReportDefinition,\r\n        ...updateDto,\r\n        updatedBy: 'test-user',\r\n        updatedAt: expect.any(Date),\r\n      });\r\n      expect(cacheService.del).toHaveBeenCalledWith('report_definition_test-report-id');\r\n      expect(cacheService.delPattern).toHaveBeenCalledWith('report_definitions_*');\r\n      expect(result).toEqual(updatedReport);\r\n    });\r\n\r\n    it('should throw error for non-existent report definition', async () => {\r\n      repository.findOneBy.mockResolvedValue(null);\r\n\r\n      await expect(service.updateReportDefinition('non-existent-id', updateDto, 'test-user'))\r\n        .rejects.toThrow('Report definition not found');\r\n\r\n      expect(repository.save).not.toHaveBeenCalled();\r\n    });\r\n  });\r\n\r\n  describe('deleteReportDefinition', () => {\r\n    it('should soft delete report definition successfully', async () => {\r\n      repository.findOneBy.mockResolvedValue(mockReportDefinition);\r\n      repository.save.mockResolvedValue({ ...mockReportDefinition, isActive: false });\r\n\r\n      await service.deleteReportDefinition('test-report-id', 'test-user');\r\n\r\n      expect(repository.findOneBy).toHaveBeenCalledWith({ id: 'test-report-id', isActive: true });\r\n      expect(repository.save).toHaveBeenCalledWith({\r\n        ...mockReportDefinition,\r\n        isActive: false,\r\n        updatedBy: 'test-user',\r\n        updatedAt: expect.any(Date),\r\n      });\r\n      expect(cacheService.del).toHaveBeenCalledWith('report_definition_test-report-id');\r\n      expect(cacheService.delPattern).toHaveBeenCalledWith('report_definitions_*');\r\n    });\r\n\r\n    it('should throw error for non-existent report definition', async () => {\r\n      repository.findOneBy.mockResolvedValue(null);\r\n\r\n      await expect(service.deleteReportDefinition('non-existent-id', 'test-user'))\r\n        .rejects.toThrow('Report definition not found');\r\n\r\n      expect(repository.save).not.toHaveBeenCalled();\r\n    });\r\n  });\r\n\r\n  describe('getReportDefinitions', () => {\r\n    const mockQueryBuilder = {\r\n      where: jest.fn().mockReturnThis(),\r\n      andWhere: jest.fn().mockReturnThis(),\r\n      orderBy: jest.fn().mockReturnThis(),\r\n      skip: jest.fn().mockReturnThis(),\r\n      take: jest.fn().mockReturnThis(),\r\n      getMany: jest.fn(),\r\n      getCount: jest.fn(),\r\n    };\r\n\r\n    beforeEach(() => {\r\n      repository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);\r\n    });\r\n\r\n    it('should return paginated report definitions with filters', async () => {\r\n      const filters = {\r\n        category: 'compliance',\r\n        type: 'tabular',\r\n        search: 'test',\r\n        page: 1,\r\n        limit: 10,\r\n      };\r\n\r\n      mockQueryBuilder.getMany.mockResolvedValue([mockReportDefinition]);\r\n      mockQueryBuilder.getCount.mockResolvedValue(1);\r\n\r\n      const result = await service.getReportDefinitions(filters);\r\n\r\n      expect(repository.createQueryBuilder).toHaveBeenCalledWith('report');\r\n      expect(mockQueryBuilder.where).toHaveBeenCalledWith('report.isActive = :isActive', { isActive: true });\r\n      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('report.category = :category', { category: 'compliance' });\r\n      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('report.type = :type', { type: 'tabular' });\r\n      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(\r\n        '(report.name ILIKE :search OR report.description ILIKE :search)',\r\n        { search: '%test%' }\r\n      );\r\n      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith('report.createdAt', 'DESC');\r\n      expect(mockQueryBuilder.skip).toHaveBeenCalledWith(0);\r\n      expect(mockQueryBuilder.take).toHaveBeenCalledWith(10);\r\n\r\n      expect(result).toEqual({\r\n        reportDefinitions: [mockReportDefinition],\r\n        total: 1,\r\n        page: 1,\r\n        limit: 10,\r\n        totalPages: 1,\r\n      });\r\n    });\r\n\r\n    it('should return all report definitions without filters', async () => {\r\n      mockQueryBuilder.getMany.mockResolvedValue([mockReportDefinition]);\r\n      mockQueryBuilder.getCount.mockResolvedValue(1);\r\n\r\n      const result = await service.getReportDefinitions({});\r\n\r\n      expect(mockQueryBuilder.where).toHaveBeenCalledWith('report.isActive = :isActive', { isActive: true });\r\n      expect(mockQueryBuilder.andWhere).not.toHaveBeenCalled();\r\n      expect(result.reportDefinitions).toEqual([mockReportDefinition]);\r\n    });\r\n  });\r\n\r\n  describe('validateReportDefinition', () => {\r\n    it('should validate report definition successfully', async () => {\r\n      const validDefinition = {\r\n        name: 'Valid Report',\r\n        dataSource: 'compliance',\r\n        query: 'SELECT * FROM compliance_data',\r\n        parameters: [],\r\n      };\r\n\r\n      const result = await service.validateReportDefinition(validDefinition);\r\n\r\n      expect(result.isValid).toBe(true);\r\n      expect(result.errors).toEqual([]);\r\n    });\r\n\r\n    it('should return validation errors for invalid definition', async () => {\r\n      const invalidDefinition = {\r\n        name: '',\r\n        dataSource: 'invalid_source',\r\n        query: '',\r\n        parameters: [],\r\n      };\r\n\r\n      const result = await service.validateReportDefinition(invalidDefinition);\r\n\r\n      expect(result.isValid).toBe(false);\r\n      expect(result.errors).toContain('Report name is required');\r\n      expect(result.errors).toContain('Invalid data source');\r\n      expect(result.errors).toContain('Query is required');\r\n    });\r\n  });\r\n\r\n  describe('error handling', () => {\r\n    it('should handle database errors gracefully', async () => {\r\n      repository.findOneBy.mockRejectedValue(new Error('Database connection failed'));\r\n\r\n      await expect(service.getReportDefinitionById('test-id'))\r\n        .rejects.toThrow('Database connection failed');\r\n\r\n      expect(metricsService.recordCounter).toHaveBeenCalledWith('report_definition_error', {\r\n        operation: 'get',\r\n        error: 'Database connection failed',\r\n      });\r\n    });\r\n\r\n    it('should handle cache errors gracefully', async () => {\r\n      cacheService.get.mockRejectedValue(new Error('Cache unavailable'));\r\n      repository.findOneBy.mockResolvedValue(mockReportDefinition);\r\n\r\n      const result = await service.getReportDefinitionById('test-id');\r\n\r\n      expect(result).toEqual(mockReportDefinition);\r\n      expect(repository.findOneBy).toHaveBeenCalled();\r\n    });\r\n  });\r\n\r\n  describe('metrics collection', () => {\r\n    it('should record metrics for successful operations', async () => {\r\n      repository.findOneBy.mockResolvedValue(mockReportDefinition);\r\n\r\n      await service.getReportDefinitionById('test-id');\r\n\r\n      expect(metricsService.recordCounter).toHaveBeenCalledWith('report_definition_accessed', {\r\n        id: 'test-id',\r\n        cached: false,\r\n      });\r\n    });\r\n\r\n    it('should record metrics for cache hits', async () => {\r\n      cacheService.get.mockResolvedValue(mockReportDefinition);\r\n\r\n      await service.getReportDefinitionById('test-id');\r\n\r\n      expect(metricsService.recordCounter).toHaveBeenCalledWith('report_definition_accessed', {\r\n        id: 'test-id',\r\n        cached: true,\r\n      });\r\n    });\r\n  });\r\n});\r\n"], "version": 3}