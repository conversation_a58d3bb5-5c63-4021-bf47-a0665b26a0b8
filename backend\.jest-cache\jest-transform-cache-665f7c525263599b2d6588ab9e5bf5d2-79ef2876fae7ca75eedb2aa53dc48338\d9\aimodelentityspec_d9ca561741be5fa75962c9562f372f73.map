{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\entities\\__tests__\\ai-model.entity.spec.ts", "mappings": ";;AAAA,4HAA0G;AAC1G,wDAS4B;AAC5B,8FAAiF;AACjF,4GAA8F;AAC9F,sHAAwG;AACxG,0HAA4G;AAE5G,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;IAC9B,IAAI,UAAgE,CAAC;IACrE,IAAI,iBAAqC,CAAC;IAC1C,IAAI,eAAiC,CAAC;IACtC,IAAI,gBAAmC,CAAC;IACxC,IAAI,wBAA8C,CAAC;IAEnD,UAAU,CAAC,GAAG,EAAE;QACd,iBAAiB,GAAG;YAClB,QAAQ,EAAE,yBAAyB;YACnC,MAAM,EAAE,UAAU;YAClB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,CAAC;YACV,SAAS,EAAE,EAAE;YACb,cAAc,EAAE,EAAE;SACnB,CAAC;QAEF,eAAe,GAAG;YAChB,aAAa,EAAE,GAAG;YAClB,kBAAkB,EAAE,EAAE;YACtB,cAAc,EAAE,CAAC;YACjB,cAAc,EAAE,GAAG;YACnB,UAAU,EAAE,GAAG;YACf,UAAU,EAAE,GAAG;YACf,QAAQ,EAAE,IAAI;YACd,SAAS,EAAE,IAAI;YACf,MAAM,EAAE,IAAI;YACZ,OAAO,EAAE,IAAI;YACb,UAAU,EAAE,EAAE;YACd,WAAW,EAAE,IAAI,IAAI,EAAE;SACxB,CAAC;QAEF,gBAAgB,GAAG;YACjB,cAAc,EAAE,IAAI;YACpB,eAAe,EAAE,IAAI;YACrB,aAAa,EAAE,IAAI;YACnB,iBAAiB,EAAE,KAAK;YACxB,kBAAkB,EAAE,IAAI;YACxB,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;YACvB,UAAU,EAAE,CAAC,MAAM,CAAC;SACrB,CAAC;QAEF,wBAAwB,GAAG;YACzB,GAAG,EAAE,CAAC;YACN,MAAM,EAAE,IAAI;YACZ,GAAG,EAAE,CAAC;YACN,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,GAAG;SACf,CAAC;QAEF,UAAU,GAAG;YACX,IAAI,EAAE,YAAY;YAClB,OAAO,EAAE,OAAO;YAChB,QAAQ,EAAE,4BAAU,CAAC,MAAM;YAC3B,SAAS,EAAE,2BAAS,CAAC,cAAc;YACnC,aAAa,EAAE,iBAAiB;YAChC,WAAW,EAAE,eAAe;YAC5B,MAAM,EAAE,6BAAW,CAAC,MAAM;YAC1B,YAAY,EAAE,gBAAgB;YAC9B,oBAAoB,EAAE,wBAAwB;YAC9C,kBAAkB,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;YACzD,IAAI,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;YACrB,QAAQ,EAAE,GAAG;YACb,MAAM,EAAE,GAAG;YACX,qBAAqB,EAAE,GAAG;YAC1B,eAAe,EAAE,IAAI,IAAI,EAAE;YAC3B,QAAQ,EAAE,IAAI,IAAI,EAAE;YACpB,UAAU,EAAE,IAAI,IAAI,EAAE;YACtB,QAAQ,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE;SAC7B,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,KAAK,GAAG,yBAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAEzC,MAAM,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,yBAAO,CAAC,CAAC;YACtC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YACjD,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YAC7C,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,KAAK,GAAG,yBAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACzC,MAAM,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC;YAElC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,mDAAmB,CAAC,CAAC;YACtD,MAAM,CAAE,MAAM,CAAC,CAAC,CAAyB,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,QAAQ,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YAC3C,MAAM,KAAK,GAAG,yBAAO,CAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAEnD,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;YAEjD,MAAM,CAAC,GAAG,EAAE,CAAC,yBAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;YAEpD,MAAM,CAAC,GAAG,EAAE,CAAC,yBAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;YAErD,MAAM,CAAC,GAAG,EAAE,CAAC,yBAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;YAEnD,MAAM,CAAC,GAAG,EAAE,CAAC,yBAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,qBAAqB,EAAE,CAAC,EAAE,CAAC;YAEjE,MAAM,CAAC,GAAG,EAAE,CAAC,yBAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,gDAAgD,CAAC,CAAC;QACvG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,kBAAkB,EAAE,EAAE,EAAE,CAAC;YAE/D,MAAM,CAAC,GAAG,EAAE,CAAC,yBAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,2CAA2C,CAAC,CAAC;QAClG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,YAAY,GAAG;gBACnB,GAAG,UAAU;gBACb,aAAa,EAAE,EAAE,GAAG,iBAAiB,EAAE,OAAO,EAAE,CAAC,EAAE;aACpD,CAAC;YAEF,MAAM,CAAC,GAAG,EAAE,CAAC,yBAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,8CAA8C,CAAC,CAAC;QACrG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,EAAE,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,KAAK,GAAG;gBACZ,GAAG,UAAU;gBACb,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,GAAG;gBACd,SAAS,EAAE,GAAG;aACf,CAAC;YAEF,MAAM,KAAK,GAAG,yBAAO,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YAE9C,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,IAAI,KAAc,CAAC;QAEnB,UAAU,CAAC,GAAG,EAAE;YACd,KAAK,GAAG,yBAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACnC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,uBAAuB;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uBAAuB,EAAE,GAAG,EAAE;YAC/B,KAAK,CAAC,UAAU,EAAE,CAAC;YACnB,KAAK,CAAC,WAAW,EAAE,CAAC;YAEpB,KAAK,CAAC,QAAQ,EAAE,CAAC;YAEjB,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,6BAAW,CAAC,MAAM,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,gEAAyB,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACjC,KAAK,CAAC,UAAU,EAAE,CAAC;YAEnB,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,6BAAW,CAAC,QAAQ,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,gEAAyB,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sBAAsB,EAAE,GAAG,EAAE;YAC9B,KAAK,CAAC,OAAO,EAAE,CAAC;YAEhB,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,6BAAW,CAAC,QAAQ,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,gEAAyB,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,KAAK,CAAC,OAAO,EAAE,CAAC;YAChB,KAAK,CAAC,WAAW,EAAE,CAAC;YAEpB,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,IAAI,KAAc,CAAC;QAEnB,UAAU,CAAC,GAAG,EAAE;YACd,KAAK,GAAG,yBAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YAErB,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACnC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;YAErB,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,+CAA+C,CAAC,CAAC;QAC/F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE5C,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YACtB,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE7C,KAAK,CAAC,UAAU,EAAE,CAAC;YACnB,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;YACtB,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YAErB,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YAErB,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,IAAI,KAAc,CAAC;QAEnB,UAAU,CAAC,GAAG,EAAE;YACd,KAAK,GAAG,yBAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACnC,KAAK,CAAC,WAAW,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,UAAU,GAAG;gBACjB,QAAQ,EAAE,IAAI;gBACd,SAAS,EAAE,IAAI;gBACf,aAAa,EAAE,GAAG;aACnB,CAAC;YAEF,KAAK,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;YAE3C,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClD,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,0EAA8B,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,YAAY,GAAG,KAAK,CAAC,eAAe,EAAE,CAAC;YAE7C,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,WAAW,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;YAE3C,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,KAAK,GAAG,KAAK,CAAC,yBAAyB,EAAE,CAAC;YAEhD,MAAM,CAAC,KAAK,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACjC,MAAM,CAAC,KAAK,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,IAAI,KAAc,CAAC;QAEnB,UAAU,CAAC,GAAG,EAAE;YACd,KAAK,GAAG,yBAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACnC,KAAK,CAAC,WAAW,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,SAAS,GAAG;gBAChB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,CAAC;aACX,CAAC;YAEF,KAAK,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;YAErC,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,8EAAgC,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,IAAI,KAAc,CAAC;QAEnB,UAAU,CAAC,GAAG,EAAE;YACd,KAAK,GAAG,yBAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gBAAgB,EAAE,GAAG,EAAE;YACxB,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAExB,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC;YACxC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,iBAAiB;YAEvC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mBAAmB,EAAE,GAAG,EAAE;YAC3B,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAExB,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,IAAI,KAAc,CAAC;QAEnB,UAAU,CAAC,GAAG,EAAE;YACd,KAAK,GAAG,yBAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7D,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,IAAI,KAAc,CAAC;QAEnB,UAAU,CAAC,GAAG,EAAE;YACd,KAAK,GAAG,yBAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;YAC9B,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAC1B,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAE7B,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,eAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,sBAAsB,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;YACtF,MAAM,CAAC,KAAK,CAAC,eAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,mBAAmB,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACjC,KAAK,CAAC,cAAc,EAAE,CAAC;YAEvB,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,6BAAW,CAAC,MAAM,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wBAAwB,EAAE,GAAG,EAAE;YAChC,MAAM,WAAW,GAAG,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;YAC3C,KAAK,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAElC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,8BAA8B;QAC5E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,IAAI,KAAc,CAAC;QAEnB,UAAU,CAAC,GAAG,EAAE;YACd,KAAK,GAAG,yBAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;YACxB,MAAM,kBAAkB,GAAG,KAAK,CAAC,kBAAkB,CAAC;YAEpD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACtB,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEpC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YAC7C,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;YAChC,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC;YAEzB,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\entities\\__tests__\\ai-model.entity.spec.ts"], "sourcesContent": ["import { UniqueEntityId } from '../../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { \r\n  AIModel, \r\n  AIProvider, \r\n  ModelType, \r\n  ModelStatus,\r\n  ModelConfiguration,\r\n  ModelPerformance,\r\n  ModelCapabilities,\r\n  ResourceRequirements\r\n} from '../ai-model.entity';\r\nimport { AIModelCreatedEvent } from '../../events/ai-model-created.domain-event';\r\nimport { AIModelStatusChangedEvent } from '../../events/ai-model-status-changed.domain-event';\r\nimport { AIModelPerformanceUpdatedEvent } from '../../events/ai-model-performance-updated.domain-event';\r\nimport { AIModelConfigurationUpdatedEvent } from '../../events/ai-model-configuration-updated.domain-event';\r\n\r\ndescribe('AIModel Entity', () => {\r\n  let validProps: Omit<any, 'createdAt' | 'updatedAt' | 'currentLoad'>;\r\n  let mockConfiguration: ModelConfiguration;\r\n  let mockPerformance: ModelPerformance;\r\n  let mockCapabilities: ModelCapabilities;\r\n  let mockResourceRequirements: ResourceRequirements;\r\n\r\n  beforeEach(() => {\r\n    mockConfiguration = {\r\n      endpoint: 'https://api.example.com',\r\n      apiKey: 'test-key',\r\n      timeout: 30000,\r\n      retries: 3,\r\n      batchSize: 10,\r\n      customSettings: {},\r\n    };\r\n\r\n    mockPerformance = {\r\n      totalRequests: 100,\r\n      successfulRequests: 95,\r\n      failedRequests: 5,\r\n      averageLatency: 200,\r\n      p95Latency: 300,\r\n      p99Latency: 500,\r\n      accuracy: 0.95,\r\n      precision: 0.92,\r\n      recall: 0.88,\r\n      f1Score: 0.90,\r\n      throughput: 50,\r\n      lastUpdated: new Date(),\r\n    };\r\n\r\n    mockCapabilities = {\r\n      maxInputLength: 4096,\r\n      maxOutputLength: 2048,\r\n      supportsBatch: true,\r\n      supportsStreaming: false,\r\n      supportsFineTuning: true,\r\n      languages: ['en', 'es'],\r\n      modalities: ['text'],\r\n    };\r\n\r\n    mockResourceRequirements = {\r\n      cpu: 2,\r\n      memory: 2048,\r\n      gpu: 1,\r\n      storage: 1024,\r\n      bandwidth: 100,\r\n    };\r\n\r\n    validProps = {\r\n      name: 'Test Model',\r\n      version: '1.0.0',\r\n      provider: AIProvider.OPENAI,\r\n      modelType: ModelType.LANGUAGE_MODEL,\r\n      configuration: mockConfiguration,\r\n      performance: mockPerformance,\r\n      status: ModelStatus.ACTIVE,\r\n      capabilities: mockCapabilities,\r\n      resourceRequirements: mockResourceRequirements,\r\n      supportedTaskTypes: ['text-generation', 'classification'],\r\n      tags: ['test', 'nlp'],\r\n      priority: 5.0,\r\n      weight: 2.0,\r\n      maxConcurrentRequests: 100,\r\n      lastHealthCheck: new Date(),\r\n      lastUsed: new Date(),\r\n      deployedAt: new Date(),\r\n      metadata: { version: '1.0' },\r\n    };\r\n  });\r\n\r\n  describe('Creation', () => {\r\n    it('should create a valid AI model', () => {\r\n      const model = AIModel.create(validProps);\r\n\r\n      expect(model).toBeInstanceOf(AIModel);\r\n      expect(model.name).toBe(validProps.name);\r\n      expect(model.version).toBe(validProps.version);\r\n      expect(model.provider).toBe(validProps.provider);\r\n      expect(model.modelType).toBe(validProps.modelType);\r\n      expect(model.status).toBe(validProps.status);\r\n      expect(model.currentLoad).toBe(0);\r\n      expect(model.createdAt).toBeInstanceOf(Date);\r\n      expect(model.updatedAt).toBeInstanceOf(Date);\r\n    });\r\n\r\n    it('should generate a domain event when created', () => {\r\n      const model = AIModel.create(validProps);\r\n      const events = model.domainEvents;\r\n\r\n      expect(events).toHaveLength(1);\r\n      expect(events[0]).toBeInstanceOf(AIModelCreatedEvent);\r\n      expect((events[0] as AIModelCreatedEvent).modelName).toBe(validProps.name);\r\n    });\r\n\r\n    it('should create with custom ID', () => {\r\n      const customId = UniqueEntityId.generate();\r\n      const model = AIModel.create(validProps, customId);\r\n\r\n      expect(model.id.equals(customId)).toBe(true);\r\n    });\r\n\r\n    it('should throw error for invalid name', () => {\r\n      const invalidProps = { ...validProps, name: '' };\r\n\r\n      expect(() => AIModel.create(invalidProps)).toThrow('AI Model name is required');\r\n    });\r\n\r\n    it('should throw error for invalid version', () => {\r\n      const invalidProps = { ...validProps, version: '' };\r\n\r\n      expect(() => AIModel.create(invalidProps)).toThrow('AI Model version is required');\r\n    });\r\n\r\n    it('should throw error for invalid priority', () => {\r\n      const invalidProps = { ...validProps, priority: 15 };\r\n\r\n      expect(() => AIModel.create(invalidProps)).toThrow('Priority must be between 0 and 10');\r\n    });\r\n\r\n    it('should throw error for invalid weight', () => {\r\n      const invalidProps = { ...validProps, weight: -1 };\r\n\r\n      expect(() => AIModel.create(invalidProps)).toThrow('Weight must be between 0 and 10');\r\n    });\r\n\r\n    it('should throw error for invalid max concurrent requests', () => {\r\n      const invalidProps = { ...validProps, maxConcurrentRequests: 0 };\r\n\r\n      expect(() => AIModel.create(invalidProps)).toThrow('Max concurrent requests must be greater than 0');\r\n    });\r\n\r\n    it('should throw error for empty supported task types', () => {\r\n      const invalidProps = { ...validProps, supportedTaskTypes: [] };\r\n\r\n      expect(() => AIModel.create(invalidProps)).toThrow('Model must support at least one task type');\r\n    });\r\n\r\n    it('should throw error for invalid configuration timeout', () => {\r\n      const invalidProps = { \r\n        ...validProps, \r\n        configuration: { ...mockConfiguration, timeout: 0 }\r\n      };\r\n\r\n      expect(() => AIModel.create(invalidProps)).toThrow('Configuration timeout must be greater than 0');\r\n    });\r\n  });\r\n\r\n  describe('Reconstitution', () => {\r\n    it('should reconstitute from valid props', () => {\r\n      const id = UniqueEntityId.generate();\r\n      const now = new Date();\r\n      const props = {\r\n        ...validProps,\r\n        currentLoad: 5,\r\n        createdAt: now,\r\n        updatedAt: now,\r\n      };\r\n\r\n      const model = AIModel.reconstitute(props, id);\r\n\r\n      expect(model.id.equals(id)).toBe(true);\r\n      expect(model.name).toBe(props.name);\r\n      expect(model.currentLoad).toBe(5);\r\n      expect(model.createdAt).toBe(now);\r\n      expect(model.updatedAt).toBe(now);\r\n    });\r\n  });\r\n\r\n  describe('Status Management', () => {\r\n    let model: AIModel;\r\n\r\n    beforeEach(() => {\r\n      model = AIModel.create(validProps);\r\n      model.clearEvents(); // Clear creation event\r\n    });\r\n\r\n    it('should activate model', () => {\r\n      model.deactivate();\r\n      model.clearEvents();\r\n\r\n      model.activate();\r\n\r\n      expect(model.status).toBe(ModelStatus.ACTIVE);\r\n      expect(model.domainEvents).toHaveLength(1);\r\n      expect(model.domainEvents[0]).toBeInstanceOf(AIModelStatusChangedEvent);\r\n    });\r\n\r\n    it('should deactivate model', () => {\r\n      model.deactivate();\r\n\r\n      expect(model.status).toBe(ModelStatus.INACTIVE);\r\n      expect(model.domainEvents).toHaveLength(1);\r\n      expect(model.domainEvents[0]).toBeInstanceOf(AIModelStatusChangedEvent);\r\n    });\r\n\r\n    it('should archive model', () => {\r\n      model.archive();\r\n\r\n      expect(model.status).toBe(ModelStatus.ARCHIVED);\r\n      expect(model.domainEvents).toHaveLength(1);\r\n      expect(model.domainEvents[0]).toBeInstanceOf(AIModelStatusChangedEvent);\r\n    });\r\n\r\n    it('should not activate archived model', () => {\r\n      model.archive();\r\n      model.clearEvents();\r\n\r\n      expect(() => model.activate()).toThrow('Cannot activate archived model');\r\n    });\r\n  });\r\n\r\n  describe('Load Management', () => {\r\n    let model: AIModel;\r\n\r\n    beforeEach(() => {\r\n      model = AIModel.create(validProps);\r\n    });\r\n\r\n    it('should update load correctly', () => {\r\n      model.updateLoad(10);\r\n\r\n      expect(model.currentLoad).toBe(10);\r\n      expect(model.lastUsed).toBeInstanceOf(Date);\r\n    });\r\n\r\n    it('should not allow negative load', () => {\r\n      model.updateLoad(-5);\r\n\r\n      expect(model.currentLoad).toBe(0);\r\n    });\r\n\r\n    it('should throw error when exceeding max concurrent requests', () => {\r\n      expect(() => model.updateLoad(150)).toThrow('Load exceeds maximum concurrent requests: 100');\r\n    });\r\n\r\n    it('should check if can handle request', () => {\r\n      expect(model.canHandleRequest()).toBe(true);\r\n\r\n      model.updateLoad(100);\r\n      expect(model.canHandleRequest()).toBe(false);\r\n\r\n      model.deactivate();\r\n      model.updateLoad(-50);\r\n      expect(model.canHandleRequest()).toBe(false);\r\n    });\r\n\r\n    it('should calculate utilization correctly', () => {\r\n      model.updateLoad(50);\r\n\r\n      expect(model.getUtilization()).toBe(0.5);\r\n    });\r\n\r\n    it('should detect overloaded state', () => {\r\n      model.updateLoad(85);\r\n\r\n      expect(model.isOverloaded()).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('Performance Management', () => {\r\n    let model: AIModel;\r\n\r\n    beforeEach(() => {\r\n      model = AIModel.create(validProps);\r\n      model.clearEvents();\r\n    });\r\n\r\n    it('should update performance metrics', () => {\r\n      const newMetrics = {\r\n        accuracy: 0.98,\r\n        precision: 0.95,\r\n        totalRequests: 200,\r\n      };\r\n\r\n      model.updatePerformanceMetrics(newMetrics);\r\n\r\n      expect(model.performance.accuracy).toBe(0.98);\r\n      expect(model.performance.precision).toBe(0.95);\r\n      expect(model.performance.totalRequests).toBe(200);\r\n      expect(model.domainEvents).toHaveLength(1);\r\n      expect(model.domainEvents[0]).toBeInstanceOf(AIModelPerformanceUpdatedEvent);\r\n    });\r\n\r\n    it('should calculate availability correctly', () => {\r\n      const availability = model.getAvailability();\r\n\r\n      expect(availability).toBe(0.95); // 95/100\r\n    });\r\n\r\n    it('should calculate success rate correctly', () => {\r\n      const successRate = model.getSuccessRate();\r\n\r\n      expect(successRate).toBe(0.95); // 95/100\r\n    });\r\n\r\n    it('should calculate performance score', () => {\r\n      const score = model.calculatePerformanceScore();\r\n\r\n      expect(score).toBeGreaterThan(0);\r\n      expect(score).toBeLessThanOrEqual(1);\r\n    });\r\n  });\r\n\r\n  describe('Configuration Management', () => {\r\n    let model: AIModel;\r\n\r\n    beforeEach(() => {\r\n      model = AIModel.create(validProps);\r\n      model.clearEvents();\r\n    });\r\n\r\n    it('should update configuration', () => {\r\n      const newConfig = {\r\n        timeout: 60000,\r\n        retries: 5,\r\n      };\r\n\r\n      model.updateConfiguration(newConfig);\r\n\r\n      expect(model.configuration.timeout).toBe(60000);\r\n      expect(model.configuration.retries).toBe(5);\r\n      expect(model.domainEvents).toHaveLength(1);\r\n      expect(model.domainEvents[0]).toBeInstanceOf(AIModelConfigurationUpdatedEvent);\r\n    });\r\n  });\r\n\r\n  describe('Tag Management', () => {\r\n    let model: AIModel;\r\n\r\n    beforeEach(() => {\r\n      model = AIModel.create(validProps);\r\n    });\r\n\r\n    it('should add tag', () => {\r\n      model.addTag('new-tag');\r\n\r\n      expect(model.tags).toContain('new-tag');\r\n    });\r\n\r\n    it('should not add duplicate tag', () => {\r\n      const initialLength = model.tags.length;\r\n      model.addTag('test'); // Already exists\r\n\r\n      expect(model.tags).toHaveLength(initialLength);\r\n    });\r\n\r\n    it('should remove tag', () => {\r\n      model.removeTag('test');\r\n\r\n      expect(model.tags).not.toContain('test');\r\n    });\r\n  });\r\n\r\n  describe('Task Type Support', () => {\r\n    let model: AIModel;\r\n\r\n    beforeEach(() => {\r\n      model = AIModel.create(validProps);\r\n    });\r\n\r\n    it('should check task type support', () => {\r\n      expect(model.supportsTaskType('text-generation')).toBe(true);\r\n      expect(model.supportsTaskType('image-generation')).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('Utility Methods', () => {\r\n    let model: AIModel;\r\n\r\n    beforeEach(() => {\r\n      model = AIModel.create(validProps);\r\n    });\r\n\r\n    it('should record health check', () => {\r\n      const beforeTime = new Date();\r\n      model.recordHealthCheck();\r\n      const afterTime = new Date();\r\n\r\n      expect(model.lastHealthCheck).toBeInstanceOf(Date);\r\n      expect(model.lastHealthCheck!.getTime()).toBeGreaterThanOrEqual(beforeTime.getTime());\r\n      expect(model.lastHealthCheck!.getTime()).toBeLessThanOrEqual(afterTime.getTime());\r\n    });\r\n\r\n    it('should mark as deployed', () => {\r\n      model.markAsDeployed();\r\n\r\n      expect(model.deployedAt).toBeInstanceOf(Date);\r\n      expect(model.status).toBe(ModelStatus.ACTIVE);\r\n    });\r\n\r\n    it('should update metadata', () => {\r\n      const newMetadata = { newKey: 'newValue' };\r\n      model.updateMetadata(newMetadata);\r\n\r\n      expect(model.metadata.newKey).toBe('newValue');\r\n      expect(model.metadata.version).toBe('1.0'); // Original metadata preserved\r\n    });\r\n  });\r\n\r\n  describe('Immutability', () => {\r\n    let model: AIModel;\r\n\r\n    beforeEach(() => {\r\n      model = AIModel.create(validProps);\r\n    });\r\n\r\n    it('should return copies of arrays', () => {\r\n      const tags = model.tags;\r\n      const supportedTaskTypes = model.supportedTaskTypes;\r\n\r\n      tags.push('modified');\r\n      supportedTaskTypes.push('modified');\r\n\r\n      expect(model.tags).not.toContain('modified');\r\n      expect(model.supportedTaskTypes).not.toContain('modified');\r\n    });\r\n\r\n    it('should return copy of metadata', () => {\r\n      const metadata = model.metadata;\r\n      metadata.modified = true;\r\n\r\n      expect(model.metadata.modified).toBeUndefined();\r\n    });\r\n  });\r\n});"], "version": 3}