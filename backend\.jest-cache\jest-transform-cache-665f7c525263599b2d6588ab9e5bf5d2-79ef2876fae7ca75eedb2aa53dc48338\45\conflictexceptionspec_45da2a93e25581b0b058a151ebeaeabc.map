{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\exceptions\\conflict.exception.spec.ts", "mappings": ";;AAAA,4EAAwE;AAExE,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;IACjC,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,SAAS,GAAG,IAAI,sCAAiB,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC;YAE1E,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,sCAAiB,CAAC,CAAC;YACpD,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAChD,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACxC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC1C,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC5C,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,SAAS,GAAG,IAAI,sCAAiB,CAAC,eAAe,EAAE,oBAAoB,EAAE;gBAC7E,YAAY,EAAE,MAAM;gBACpB,UAAU,EAAE,UAAU;gBACtB,gBAAgB,EAAE,kBAAkB;gBACpC,aAAa,EAAE,sBAAsB;gBACrC,aAAa,EAAE,OAAO;gBACtB,aAAa,EAAE,qBAAqB;aACrC,CAAC,CAAC;YAEH,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAChD,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAC1D,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC5C,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC9C,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC5D,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAC7D,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9C,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;YACjC,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;gBACxD,MAAM,SAAS,GAAG,sCAAiB,CAAC,iBAAiB,CACnD,MAAM,EACN,OAAO,EACP,kBAAkB,EAClB;oBACE,UAAU,EAAE,UAAU;oBACtB,kBAAkB,EAAE,UAAU;iBAC/B,CACF,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;gBACpF,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBAC1D,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC5C,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC9C,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBAC5D,MAAM,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC3D,MAAM,CAAC,SAAS,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACzC,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;gBACjE,MAAM,SAAS,GAAG,sCAAiB,CAAC,yBAAyB,CAC3D,SAAS,EACT,YAAY,EACZ,SAAS,CACV,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;gBACtF,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;gBACnE,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC/C,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACnD,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACnD,MAAM,CAAC,SAAS,CAAC,2BAA2B,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;YAC/B,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;gBACtD,MAAM,SAAS,GAAG,sCAAiB,CAAC,eAAe,CACjD,UAAU,EACV,SAAS,EACT,CAAC,EACD,CAAC,CACF,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;gBAC7F,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBACxD,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAChD,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC7C,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAChD,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC3C,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACxC,MAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEjD,MAAM,WAAW,GAAG,SAAS,CAAC,cAAc,EAAE,CAAC;gBAC/C,MAAM,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;YAC1D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;YAC7B,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;gBACpD,MAAM,SAAS,GAAG,sCAAiB,CAAC,aAAa,CAC/C,OAAO,EACP,WAAW,EACX,SAAS,EACT,SAAS,EACT,QAAQ,EACR;oBACE,aAAa,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC;iBACzC,CACF,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAC5B,+EAA+E,CAChF,CAAC;gBACF,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBACtD,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC7C,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAC/C,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC9C,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACnD,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAChD,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAE/C,MAAM,SAAS,GAAG,SAAS,CAAC,YAAY,EAAE,CAAC;gBAC3C,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC;oBACxB,OAAO,EAAE,SAAS;oBAClB,QAAQ,EAAE,SAAS;oBACnB,SAAS,EAAE,QAAQ;oBACnB,aAAa,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC;iBACzC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;YACpC,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;gBAC5D,MAAM,SAAS,GAAG,sCAAiB,CAAC,oBAAoB,CACtD,yBAAyB,EACzB,MAAM,EACN,UAAU,EACV,kDAAkD,EAClD;oBACE,gBAAgB,EAAE,CAAC;iBACpB,CACF,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAC5B,wHAAwH,CACzH,CAAC;gBACF,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;gBAC9D,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC5C,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC9C,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC3C,MAAM,CAAC,SAAS,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEtD,MAAM,QAAQ,GAAG,SAAS,CAAC,mBAAmB,EAAE,CAAC;gBACjD,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;oBACvB,QAAQ,EAAE,yBAAyB;oBACnC,eAAe,EAAE,kDAAkD;iBACpE,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;YACtC,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;gBAC7D,MAAM,cAAc,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBACxD,MAAM,SAAS,GAAG,sCAAiB,CAAC,sBAAsB,CACxD,UAAU,EACV,SAAS,EACT,QAAQ,EACR;oBACE,oBAAoB,EAAE,QAAQ;oBAC9B,cAAc,EAAE,UAAU;oBAC1B,cAAc;iBACf,CACF,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAC5B,uEAAuE,CACxE,CAAC;gBACF,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;gBAC/D,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAChD,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC7C,MAAM,CAAC,SAAS,CAAC,wBAAwB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAExD,MAAM,OAAO,GAAG,SAAS,CAAC,6BAA6B,EAAE,CAAC;gBAC1D,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC;oBACtB,SAAS,EAAE,QAAQ;oBACnB,oBAAoB,EAAE,QAAQ;oBAC9B,cAAc,EAAE,UAAU;oBAC1B,cAAc;iBACf,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,MAAM,YAAY,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;gBACvD,MAAM,SAAS,GAAG,sCAAiB,CAAC,kBAAkB,CACpD,UAAU,EACV,SAAS,EACT,SAAS,EACT,YAAY,EACZ,QAAQ,CACT,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAC5B,sFAAsF,CACvF,CAAC;gBACF,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBAC3D,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAChD,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC7C,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEpD,MAAM,OAAO,GAAG,SAAS,CAAC,iBAAiB,EAAE,CAAC;gBAC9C,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC;oBACtB,qBAAqB,EAAE,SAAS;oBAChC,oBAAoB,EAAE,YAAY;oBAClC,cAAc,EAAE,CAAC;oBACjB,SAAS,EAAE,QAAQ;iBACpB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;gBAC/C,MAAM,YAAY,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;gBAC7E,MAAM,SAAS,GAAG,sCAAiB,CAAC,kBAAkB,CACpD,UAAU,EACV,SAAS,EACT,SAAS,EACT,YAAY,EACZ,QAAQ,CACT,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAC5B,iGAAiG,CAClG,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAC9B,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;gBAC5D,MAAM,cAAc,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBACxD,MAAM,aAAa,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBACvD,MAAM,SAAS,GAAG,sCAAiB,CAAC,cAAc,CAChD,UAAU,EACV,SAAS,EACT,UAAU,EACV,cAAc,EACd,MAAM,EACN;oBACE,QAAQ,EAAE,WAAW;oBACrB,aAAa;iBACd,CACF,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAC5B,wEAAwE,CACzE,CAAC;gBACF,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBACvD,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAChD,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC7C,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEhD,MAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;gBACzC,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;oBACvB,QAAQ,EAAE,UAAU;oBACpB,cAAc;oBACd,QAAQ,EAAE,WAAW;oBACrB,aAAa;oBACb,SAAS,EAAE,MAAM;iBAClB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,kBAAkB,GAAG,sCAAiB,CAAC,iBAAiB,CAAC,MAAM,EAAE,OAAO,EAAE,kBAAkB,CAAC,CAAC;YACpG,MAAM,gBAAgB,GAAG,sCAAiB,CAAC,eAAe,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACjF,MAAM,cAAc,GAAG,sCAAiB,CAAC,aAAa,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;YAE3G,MAAM,CAAC,kBAAkB,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,CAAC,kBAAkB,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEzD,MAAM,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,gBAAgB,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEvD,MAAM,CAAC,cAAc,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzD,MAAM,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvD,MAAM,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,SAAS,GAAG,sCAAiB,CAAC,iBAAiB,CAAC,MAAM,EAAE,OAAO,EAAE,kBAAkB,CAAC,CAAC;YAC3F,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qEAAqE,EAAE,GAAG,EAAE;YAC7E,MAAM,SAAS,GAAG,sCAAiB,CAAC,yBAAyB,CAAC,SAAS,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;YAC3F,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;QAC5F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,SAAS,GAAG,sCAAiB,CAAC,eAAe,CAAC,UAAU,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC/E,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,+EAA+E,CAAC,CAAC;QAC3H,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,SAAS,GAAG,sCAAiB,CAAC,aAAa,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;YACtG,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,6EAA6E,CAAC,CAAC;QACzH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;YACxE,MAAM,SAAS,GAAG,sCAAiB,CAAC,oBAAoB,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC;YACpG,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,iEAAiE,CAAC,CAAC;QAC7G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iEAAiE,EAAE,GAAG,EAAE;YACzE,MAAM,SAAS,GAAG,sCAAiB,CAAC,sBAAsB,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;YACrF,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,yEAAyE,CAAC,CAAC;QACrH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,SAAS,GAAG,sCAAiB,CAAC,kBAAkB,CAAC,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAC;YACxG,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;QAC3G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,SAAS,GAAG,sCAAiB,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC;YACjG,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;QACtG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,SAAS,GAAG,IAAI,sCAAiB,CAAC,eAAe,EAAE,kBAAkB,CAAC,CAAC;YAC7E,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;QAC/F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,SAAS,GAAG,sCAAiB,CAAC,iBAAiB,CAAC,MAAM,EAAE,OAAO,EAAE,kBAAkB,CAAC,CAAC;YAC3F,MAAM,WAAW,GAAG,SAAS,CAAC,wBAAwB,EAAE,CAAC;YAEzD,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,iDAAiD,CAAC,CAAC;YACjF,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,4DAA4D,CAAC,CAAC;QAC9F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,SAAS,GAAG,sCAAiB,CAAC,eAAe,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC1E,MAAM,WAAW,GAAG,SAAS,CAAC,wBAAwB,EAAE,CAAC;YAEzD,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,8CAA8C,CAAC,CAAC;YAC9E,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,4CAA4C,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mEAAmE,EAAE,GAAG,EAAE;YAC3E,MAAM,SAAS,GAAG,sCAAiB,CAAC,aAAa,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE;gBACpG,aAAa,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC;aACzC,CAAC,CAAC;YACH,MAAM,WAAW,GAAG,SAAS,CAAC,wBAAwB,EAAE,CAAC;YAEzD,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,0DAA0D,CAAC,CAAC;YAC1F,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,mDAAmD,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;YACxE,MAAM,aAAa,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,kBAAkB;YACxE,MAAM,SAAS,GAAG,sCAAiB,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,IAAI,EAAE,EAAE,MAAM,EAAE;gBAC/F,aAAa;aACd,CAAC,CAAC;YACH,MAAM,WAAW,GAAG,SAAS,CAAC,wBAAwB,EAAE,CAAC;YAEzD,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,kCAAkC,aAAa,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;YAC/F,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,mCAAmC,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,SAAS,GAAG,sCAAiB,CAAC,iBAAiB,CAAC,MAAM,EAAE,OAAO,EAAE,kBAAkB,CAAC,CAAC;YAE3F,MAAM,QAAQ,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;YAE3C,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;gBACvB,KAAK,EAAE,0BAA0B;gBACjC,IAAI,EAAE,UAAU;gBAChB,OAAO,EAAE;oBACP,YAAY,EAAE,oBAAoB;oBAClC,YAAY,EAAE,MAAM;oBACpB,UAAU,EAAE,SAAS;oBACrB,aAAa,EAAE,OAAO;oBACtB,qBAAqB,EAAE;wBACrB,iDAAiD;wBACjD,4DAA4D;qBAC7D;iBACF;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,SAAS,GAAG,sCAAiB,CAAC,eAAe,CAAC,UAAU,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAEjF,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC;YAEhC,MAAM,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC;gBACzB,IAAI,EAAE,mBAAmB;gBACzB,IAAI,EAAE,UAAU;gBAChB,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,UAAU;gBACpB,YAAY,EAAE,kBAAkB;gBAChC,YAAY,EAAE,UAAU;gBACxB,UAAU,EAAE,SAAS;gBACrB,gBAAgB,EAAE,CAAC;gBACnB,aAAa,EAAE,CAAC;gBAChB,aAAa,EAAE,SAAS;gBACxB,mBAAmB,EAAE,KAAK;gBAC1B,iBAAiB,EAAE,IAAI;gBACvB,eAAe,EAAE,KAAK;gBACtB,WAAW,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;gBACvC,SAAS,EAAE,IAAI;gBACf,gBAAgB,EAAE,IAAI;gBACtB,cAAc,EAAE,IAAI;gBACpB,QAAQ,EAAE,IAAI;aACf,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,SAAS,GAAG,IAAI,sCAAiB,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC;YAE1E,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACxC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACjD,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,SAAS,GAAG,IAAI,sCAAiB,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC;YAE1E,MAAM,CAAC,SAAS,YAAY,sCAAiB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1D,MAAM,CAAC,SAAS,YAAY,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\exceptions\\conflict.exception.spec.ts"], "sourcesContent": ["import { ConflictException } from '../../exceptions/conflict.exception';\r\n\r\ndescribe('ConflictException', () => {\r\n  describe('constructor', () => {\r\n    it('should create exception with required parameters', () => {\r\n      const exception = new ConflictException('Test conflict', 'test_conflict');\r\n\r\n      expect(exception).toBeInstanceOf(ConflictException);\r\n      expect(exception.message).toBe('Test conflict');\r\n      expect(exception.code).toBe('CONFLICT');\r\n      expect(exception.severity).toBe('medium');\r\n      expect(exception.category).toBe('conflict');\r\n      expect(exception.conflictType).toBe('test_conflict');\r\n    });\r\n\r\n    it('should create exception with all options', () => {\r\n      const exception = new ConflictException('Test conflict', 'duplicate_resource', {\r\n        resourceType: 'User',\r\n        resourceId: 'user-123',\r\n        conflictingValue: '<EMAIL>',\r\n        existingValue: '<EMAIL>',\r\n        conflictField: 'email',\r\n        correlationId: 'test-correlation-id',\r\n      });\r\n\r\n      expect(exception.message).toBe('Test conflict');\r\n      expect(exception.conflictType).toBe('duplicate_resource');\r\n      expect(exception.resourceType).toBe('User');\r\n      expect(exception.resourceId).toBe('user-123');\r\n      expect(exception.conflictingValue).toBe('<EMAIL>');\r\n      expect(exception.existingValue).toBe('<EMAIL>');\r\n      expect(exception.conflictField).toBe('email');\r\n      expect(exception.correlationId).toBe('test-correlation-id');\r\n    });\r\n  });\r\n\r\n  describe('static factory methods', () => {\r\n    describe('duplicateResource', () => {\r\n      it('should create exception for duplicate resource', () => {\r\n        const exception = ConflictException.duplicateResource(\r\n          'User',\r\n          'email',\r\n          '<EMAIL>',\r\n          {\r\n            resourceId: 'user-123',\r\n            existingResourceId: 'user-456',\r\n          }\r\n        );\r\n\r\n        expect(exception.message).toBe(\"User with email '<EMAIL>' already exists\");\r\n        expect(exception.conflictType).toBe('duplicate_resource');\r\n        expect(exception.resourceType).toBe('User');\r\n        expect(exception.conflictField).toBe('email');\r\n        expect(exception.conflictingValue).toBe('<EMAIL>');\r\n        expect(exception.getExistingResourceId()).toBe('user-456');\r\n        expect(exception.isDuplicateResource()).toBe(true);\r\n      });\r\n    });\r\n\r\n    describe('uniqueConstraintViolation', () => {\r\n      it('should create exception for unique constraint violation', () => {\r\n        const exception = ConflictException.uniqueConstraintViolation(\r\n          'Product',\r\n          'unique_sku',\r\n          'SKU-123'\r\n        );\r\n\r\n        expect(exception.message).toBe(\"Unique constraint 'unique_sku' violated for Product\");\r\n        expect(exception.conflictType).toBe('unique_constraint_violation');\r\n        expect(exception.resourceType).toBe('Product');\r\n        expect(exception.conflictField).toBe('unique_sku');\r\n        expect(exception.conflictingValue).toBe('SKU-123');\r\n        expect(exception.isUniqueConstraintViolation()).toBe(true);\r\n      });\r\n    });\r\n\r\n    describe('versionConflict', () => {\r\n      it('should create exception for version conflict', () => {\r\n        const exception = ConflictException.versionConflict(\r\n          'Document',\r\n          'doc-123',\r\n          5,\r\n          7\r\n        );\r\n\r\n        expect(exception.message).toBe(\"Version conflict for Document 'doc-123': expected 5, got 7\");\r\n        expect(exception.conflictType).toBe('version_conflict');\r\n        expect(exception.resourceType).toBe('Document');\r\n        expect(exception.resourceId).toBe('doc-123');\r\n        expect(exception.conflictField).toBe('version');\r\n        expect(exception.conflictingValue).toBe(5);\r\n        expect(exception.existingValue).toBe(7);\r\n        expect(exception.isVersionConflict()).toBe(true);\r\n\r\n        const versionInfo = exception.getVersionInfo();\r\n        expect(versionInfo).toEqual({ expected: 5, actual: 7 });\r\n      });\r\n    });\r\n\r\n    describe('stateConflict', () => {\r\n      it('should create exception for state conflict', () => {\r\n        const exception = ConflictException.stateConflict(\r\n          'Order',\r\n          'order-123',\r\n          'shipped',\r\n          'pending',\r\n          'cancel',\r\n          {\r\n            allowedStates: ['pending', 'processing'],\r\n          }\r\n        );\r\n\r\n        expect(exception.message).toBe(\r\n          \"Cannot cancel Order 'order-123' in state 'shipped'. Required state: 'pending'\"\r\n        );\r\n        expect(exception.conflictType).toBe('state_conflict');\r\n        expect(exception.resourceType).toBe('Order');\r\n        expect(exception.resourceId).toBe('order-123');\r\n        expect(exception.conflictField).toBe('state');\r\n        expect(exception.conflictingValue).toBe('pending');\r\n        expect(exception.existingValue).toBe('shipped');\r\n        expect(exception.isStateConflict()).toBe(true);\r\n\r\n        const stateInfo = exception.getStateInfo();\r\n        expect(stateInfo).toEqual({\r\n          current: 'shipped',\r\n          required: 'pending',\r\n          operation: 'cancel',\r\n          allowedStates: ['pending', 'processing'],\r\n        });\r\n      });\r\n    });\r\n\r\n    describe('businessRuleConflict', () => {\r\n      it('should create exception for business rule conflict', () => {\r\n        const exception = ConflictException.businessRuleConflict(\r\n          'max_concurrent_sessions',\r\n          'User',\r\n          'user-123',\r\n          'User cannot have more than 3 concurrent sessions',\r\n          {\r\n            conflictingValue: 4,\r\n          }\r\n        );\r\n\r\n        expect(exception.message).toBe(\r\n          \"Business rule 'max_concurrent_sessions' conflict for User 'user-123': User cannot have more than 3 concurrent sessions\"\r\n        );\r\n        expect(exception.conflictType).toBe('business_rule_conflict');\r\n        expect(exception.resourceType).toBe('User');\r\n        expect(exception.resourceId).toBe('user-123');\r\n        expect(exception.conflictingValue).toBe(4);\r\n        expect(exception.isBusinessRuleConflict()).toBe(true);\r\n\r\n        const ruleInfo = exception.getBusinessRuleInfo();\r\n        expect(ruleInfo).toEqual({\r\n          ruleName: 'max_concurrent_sessions',\r\n          ruleDescription: 'User cannot have more than 3 concurrent sessions',\r\n        });\r\n      });\r\n    });\r\n\r\n    describe('concurrentModification', () => {\r\n      it('should create exception for concurrent modification', () => {\r\n        const lastModifiedAt = new Date('2023-01-01T12:00:00Z');\r\n        const exception = ConflictException.concurrentModification(\r\n          'Document',\r\n          'doc-123',\r\n          'update',\r\n          {\r\n            conflictingOperation: 'delete',\r\n            lastModifiedBy: 'user-456',\r\n            lastModifiedAt,\r\n          }\r\n        );\r\n\r\n        expect(exception.message).toBe(\r\n          \"Concurrent modification detected for Document 'doc-123' during update\"\r\n        );\r\n        expect(exception.conflictType).toBe('concurrent_modification');\r\n        expect(exception.resourceType).toBe('Document');\r\n        expect(exception.resourceId).toBe('doc-123');\r\n        expect(exception.isConcurrentModification()).toBe(true);\r\n\r\n        const modInfo = exception.getConcurrentModificationInfo();\r\n        expect(modInfo).toEqual({\r\n          operation: 'update',\r\n          conflictingOperation: 'delete',\r\n          lastModifiedBy: 'user-456',\r\n          lastModifiedAt,\r\n        });\r\n      });\r\n    });\r\n\r\n    describe('dependencyConflict', () => {\r\n      it('should create exception for dependency conflict', () => {\r\n        const dependentIds = ['child-1', 'child-2', 'child-3'];\r\n        const exception = ConflictException.dependencyConflict(\r\n          'Category',\r\n          'cat-123',\r\n          'Product',\r\n          dependentIds,\r\n          'delete'\r\n        );\r\n\r\n        expect(exception.message).toBe(\r\n          \"Cannot delete Category 'cat-123' due to dependent Product: child-1, child-2, child-3\"\r\n        );\r\n        expect(exception.conflictType).toBe('dependency_conflict');\r\n        expect(exception.resourceType).toBe('Category');\r\n        expect(exception.resourceId).toBe('cat-123');\r\n        expect(exception.isDependencyConflict()).toBe(true);\r\n\r\n        const depInfo = exception.getDependencyInfo();\r\n        expect(depInfo).toEqual({\r\n          dependentResourceType: 'Product',\r\n          dependentResourceIds: dependentIds,\r\n          dependentCount: 3,\r\n          operation: 'delete',\r\n        });\r\n      });\r\n\r\n      it('should truncate long dependency lists', () => {\r\n        const dependentIds = ['child-1', 'child-2', 'child-3', 'child-4', 'child-5'];\r\n        const exception = ConflictException.dependencyConflict(\r\n          'Category',\r\n          'cat-123',\r\n          'Product',\r\n          dependentIds,\r\n          'delete'\r\n        );\r\n\r\n        expect(exception.message).toBe(\r\n          \"Cannot delete Category 'cat-123' due to dependent Product: child-1, child-2, child-3 and 2 more\"\r\n        );\r\n      });\r\n    });\r\n\r\n    describe('resourceLocked', () => {\r\n      it('should create exception for resource lock conflict', () => {\r\n        const lockAcquiredAt = new Date('2023-01-01T12:00:00Z');\r\n        const lockExpiresAt = new Date('2023-01-01T13:00:00Z');\r\n        const exception = ConflictException.resourceLocked(\r\n          'Document',\r\n          'doc-123',\r\n          'user-456',\r\n          lockAcquiredAt,\r\n          'edit',\r\n          {\r\n            lockType: 'exclusive',\r\n            lockExpiresAt,\r\n          }\r\n        );\r\n\r\n        expect(exception.message).toBe(\r\n          \"Document 'doc-123' is locked by 'user-456' and cannot be used for edit\"\r\n        );\r\n        expect(exception.conflictType).toBe('resource_locked');\r\n        expect(exception.resourceType).toBe('Document');\r\n        expect(exception.resourceId).toBe('doc-123');\r\n        expect(exception.isResourceLocked()).toBe(true);\r\n\r\n        const lockInfo = exception.getLockInfo();\r\n        expect(lockInfo).toEqual({\r\n          lockedBy: 'user-456',\r\n          lockAcquiredAt,\r\n          lockType: 'exclusive',\r\n          lockExpiresAt,\r\n          operation: 'edit',\r\n        });\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('type checking methods', () => {\r\n    it('should correctly identify conflict types', () => {\r\n      const duplicateException = ConflictException.duplicateResource('User', 'email', '<EMAIL>');\r\n      const versionException = ConflictException.versionConflict('Doc', 'doc-1', 1, 2);\r\n      const stateException = ConflictException.stateConflict('Order', 'order-1', 'shipped', 'pending', 'cancel');\r\n\r\n      expect(duplicateException.isDuplicateResource()).toBe(true);\r\n      expect(duplicateException.isVersionConflict()).toBe(false);\r\n      expect(duplicateException.isStateConflict()).toBe(false);\r\n\r\n      expect(versionException.isDuplicateResource()).toBe(false);\r\n      expect(versionException.isVersionConflict()).toBe(true);\r\n      expect(versionException.isStateConflict()).toBe(false);\r\n\r\n      expect(stateException.isDuplicateResource()).toBe(false);\r\n      expect(stateException.isVersionConflict()).toBe(false);\r\n      expect(stateException.isStateConflict()).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('getUserMessage', () => {\r\n    it('should return user-friendly message for duplicate resource', () => {\r\n      const exception = ConflictException.duplicateResource('User', 'email', '<EMAIL>');\r\n      expect(exception.getUserMessage()).toBe('This user already exists');\r\n    });\r\n\r\n    it('should return user-friendly message for unique constraint violation', () => {\r\n      const exception = ConflictException.uniqueConstraintViolation('Product', 'sku', 'SKU-123');\r\n      expect(exception.getUserMessage()).toBe('A product with this information already exists');\r\n    });\r\n\r\n    it('should return user-friendly message for version conflict', () => {\r\n      const exception = ConflictException.versionConflict('Document', 'doc-1', 1, 2);\r\n      expect(exception.getUserMessage()).toBe('This resource has been modified by another user. Please refresh and try again');\r\n    });\r\n\r\n    it('should return user-friendly message for state conflict', () => {\r\n      const exception = ConflictException.stateConflict('Order', 'order-1', 'shipped', 'pending', 'cancel');\r\n      expect(exception.getUserMessage()).toBe('This operation cannot be performed due to the current state of the resource');\r\n    });\r\n\r\n    it('should return user-friendly message for business rule conflict', () => {\r\n      const exception = ConflictException.businessRuleConflict('rule', 'User', 'user-1', 'Rule violated');\r\n      expect(exception.getUserMessage()).toBe('This operation violates a business rule and cannot be completed');\r\n    });\r\n\r\n    it('should return user-friendly message for concurrent modification', () => {\r\n      const exception = ConflictException.concurrentModification('Doc', 'doc-1', 'update');\r\n      expect(exception.getUserMessage()).toBe('This resource is being modified by another user. Please try again later');\r\n    });\r\n\r\n    it('should return user-friendly message for dependency conflict', () => {\r\n      const exception = ConflictException.dependencyConflict('Cat', 'cat-1', 'Product', ['prod-1'], 'delete');\r\n      expect(exception.getUserMessage()).toBe('This operation cannot be completed due to dependent resources');\r\n    });\r\n\r\n    it('should return user-friendly message for resource locked', () => {\r\n      const exception = ConflictException.resourceLocked('Doc', 'doc-1', 'user-1', new Date(), 'edit');\r\n      expect(exception.getUserMessage()).toBe('This resource is currently locked and cannot be modified');\r\n    });\r\n\r\n    it('should return default message for unknown conflict type', () => {\r\n      const exception = new ConflictException('Test conflict', 'unknown_conflict');\r\n      expect(exception.getUserMessage()).toBe('A conflict occurred while processing your request');\r\n    });\r\n  });\r\n\r\n  describe('getResolutionSuggestions', () => {\r\n    it('should provide suggestions for duplicate resource', () => {\r\n      const exception = ConflictException.duplicateResource('User', 'email', '<EMAIL>');\r\n      const suggestions = exception.getResolutionSuggestions();\r\n\r\n      expect(suggestions).toContain('Use a different value for the conflicting field');\r\n      expect(suggestions).toContain('Update the existing resource instead of creating a new one');\r\n    });\r\n\r\n    it('should provide suggestions for version conflict', () => {\r\n      const exception = ConflictException.versionConflict('Doc', 'doc-1', 1, 2);\r\n      const suggestions = exception.getResolutionSuggestions();\r\n\r\n      expect(suggestions).toContain('Refresh the resource and retry the operation');\r\n      expect(suggestions).toContain('Merge your changes with the latest version');\r\n    });\r\n\r\n    it('should provide suggestions for state conflict with allowed states', () => {\r\n      const exception = ConflictException.stateConflict('Order', 'order-1', 'shipped', 'pending', 'cancel', {\r\n        allowedStates: ['pending', 'processing'],\r\n      });\r\n      const suggestions = exception.getResolutionSuggestions();\r\n\r\n      expect(suggestions).toContain('Change the resource state to one of: pending, processing');\r\n      expect(suggestions).toContain('Wait for the resource to reach the required state');\r\n    });\r\n\r\n    it('should provide suggestions for resource locked with expiration', () => {\r\n      const lockExpiresAt = new Date(Date.now() + 3600000); // 1 hour from now\r\n      const exception = ConflictException.resourceLocked('Doc', 'doc-1', 'user-1', new Date(), 'edit', {\r\n        lockExpiresAt,\r\n      });\r\n      const suggestions = exception.getResolutionSuggestions();\r\n\r\n      expect(suggestions).toContain(`Wait until the lock expires at ${lockExpiresAt.toISOString()}`);\r\n      expect(suggestions).toContain('Contact the user who has the lock');\r\n    });\r\n  });\r\n\r\n  describe('toApiResponse', () => {\r\n    it('should convert to API response format', () => {\r\n      const exception = ConflictException.duplicateResource('User', 'email', '<EMAIL>');\r\n\r\n      const response = exception.toApiResponse();\r\n\r\n      expect(response).toEqual({\r\n        error: 'This user already exists',\r\n        code: 'CONFLICT',\r\n        details: {\r\n          conflictType: 'duplicate_resource',\r\n          resourceType: 'User',\r\n          resourceId: undefined,\r\n          conflictField: 'email',\r\n          resolutionSuggestions: [\r\n            'Use a different value for the conflicting field',\r\n            'Update the existing resource instead of creating a new one',\r\n          ],\r\n        },\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('toJSON', () => {\r\n    it('should convert to JSON with detailed information', () => {\r\n      const exception = ConflictException.versionConflict('Document', 'doc-123', 5, 7);\r\n\r\n      const json = exception.toJSON();\r\n\r\n      expect(json).toMatchObject({\r\n        name: 'ConflictException',\r\n        code: 'CONFLICT',\r\n        severity: 'medium',\r\n        category: 'conflict',\r\n        conflictType: 'version_conflict',\r\n        resourceType: 'Document',\r\n        resourceId: 'doc-123',\r\n        conflictingValue: 5,\r\n        existingValue: 7,\r\n        conflictField: 'version',\r\n        isDuplicateResource: false,\r\n        isVersionConflict: true,\r\n        isStateConflict: false,\r\n        versionInfo: { expected: 5, actual: 7 },\r\n        stateInfo: null,\r\n        businessRuleInfo: null,\r\n        dependencyInfo: null,\r\n        lockInfo: null,\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('inheritance', () => {\r\n    it('should be instance of Error and DomainException', () => {\r\n      const exception = new ConflictException('Test conflict', 'test_conflict');\r\n\r\n      expect(exception).toBeInstanceOf(Error);\r\n      expect(exception.name).toBe('ConflictException');\r\n      expect(exception.code).toBe('CONFLICT');\r\n    });\r\n\r\n    it('should maintain proper prototype chain', () => {\r\n      const exception = new ConflictException('Test conflict', 'test_conflict');\r\n\r\n      expect(exception instanceof ConflictException).toBe(true);\r\n      expect(exception instanceof Error).toBe(true);\r\n    });\r\n  });\r\n});"], "version": 3}