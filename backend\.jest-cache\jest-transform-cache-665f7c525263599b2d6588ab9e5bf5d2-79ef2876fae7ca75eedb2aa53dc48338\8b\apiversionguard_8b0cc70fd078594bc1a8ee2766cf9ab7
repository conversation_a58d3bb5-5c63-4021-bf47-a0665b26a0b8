d9cfec57932d4828154af4da925a80ba
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ApiVersionGuard_1;
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiVersionGuard = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const api_versioning_service_1 = require("./api-versioning.service");
const api_version_decorator_1 = require("./api-version.decorator");
/**
 * Guard to validate and enforce API versioning
 * Checks version compatibility, deprecation status, and version requirements
 */
let ApiVersionGuard = ApiVersionGuard_1 = class ApiVersionGuard {
    constructor(reflector, versioningService) {
        this.reflector = reflector;
        this.versioningService = versioningService;
        this.logger = new common_1.Logger(ApiVersionGuard_1.name);
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const response = context.switchToHttp().getResponse();
        try {
            // Extract version from request
            const requestedVersion = this.versioningService.extractVersion(request);
            // Get version metadata from controller/method
            const versionConfig = this.getVersionConfig(context);
            // Validate version
            await this.validateVersion(requestedVersion, versionConfig, request, response);
            // Store version in request for later use
            request.apiVersion = requestedVersion;
            // Log version usage
            await this.versioningService.logVersionUsage(requestedVersion, request.path, request.user?.id);
            return true;
        }
        catch (error) {
            this.logger.error('API version validation failed', {
                path: request.path,
                method: request.method,
                error: error.message,
                headers: request.headers,
            });
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.BadRequestException('Invalid API version');
        }
    }
    /**
     * Get version configuration from metadata
     */
    getVersionConfig(context) {
        // Check method-level metadata first
        const methodConfig = this.reflector.get(api_version_decorator_1.API_VERSION_KEY, context.getHandler());
        if (methodConfig) {
            return methodConfig;
        }
        // Check class-level metadata
        const classConfig = this.reflector.get(api_version_decorator_1.API_VERSION_KEY, context.getClass());
        return classConfig || null;
    }
    /**
     * Validate API version against requirements
     */
    async validateVersion(requestedVersion, versionConfig, request, response) {
        // Check if version is supported
        if (!this.versioningService.validateVersion(requestedVersion)) {
            const supportedVersions = this.versioningService.getSupportedVersions()
                .map(v => v.version)
                .join(', ');
            throw new common_1.BadRequestException(`Unsupported API version: ${requestedVersion}. Supported versions: ${supportedVersions}`);
        }
        // Get version information
        const versionInfo = this.versioningService.getVersionInfo(requestedVersion);
        if (!versionInfo) {
            throw new common_1.BadRequestException(`Invalid API version: ${requestedVersion}`);
        }
        // Check if version is sunset
        if (versionInfo.status === 'sunset') {
            throw new common_1.HttpException({
                statusCode: common_1.HttpStatus.GONE,
                message: `API version ${requestedVersion} is no longer supported`,
                error: 'Version Sunset',
                sunsetDate: versionInfo.sunsetDate,
                replacement: this.getReplacementVersion(requestedVersion),
            }, common_1.HttpStatus.GONE);
        }
        // Handle deprecated versions
        if (versionInfo.status === 'deprecated') {
            this.addDeprecationHeaders(response, requestedVersion, versionInfo);
        }
        // Check version-specific requirements
        if (versionConfig) {
            await this.validateVersionConfig(requestedVersion, versionConfig, request, response);
        }
        // Check min/max version requirements
        await this.validateVersionRange(requestedVersion, request, response);
        // Check experimental features
        await this.validateExperimentalFeatures(requestedVersion, request, response);
    }
    /**
     * Validate version against specific configuration
     */
    async validateVersionConfig(requestedVersion, config, request, response) {
        // Check if specific version is required
        if (config.version && config.version !== 'deprecated' && config.version !== requestedVersion) {
            // Allow compatible versions (same major version)
            const compatibility = this.versioningService.checkCompatibility(requestedVersion, config.version);
            if (!compatibility.compatible) {
                throw new common_1.BadRequestException(`This endpoint requires API version ${config.version}. ` +
                    `Version ${requestedVersion} is not compatible.`);
            }
            // Add compatibility warning headers
            if (compatibility.migrationRequired) {
                response.setHeader('X-API-Compatibility-Warning', `Migration required from v${requestedVersion} to v${config.version}`);
                response.setHeader('X-API-Breaking-Changes', compatibility.breakingChanges.join(', '));
            }
        }
        // Handle deprecated endpoint
        if (config.deprecated) {
            this.addDeprecationHeaders(response, requestedVersion, config);
            // Log deprecated endpoint usage
            this.logger.warn('Deprecated endpoint accessed', {
                version: requestedVersion,
                endpoint: request.path,
                deprecationDate: config.deprecationDate,
                sunsetDate: config.sunsetDate,
                replacement: config.replacement,
            });
        }
    }
    /**
     * Validate version range requirements
     */
    async validateVersionRange(requestedVersion, request, response) {
        const context = this.getCurrentExecutionContext(request);
        if (!context)
            return;
        // Check minimum version requirement
        const minVersion = this.reflector.get('min_version', context.getHandler()) ||
            this.reflector.get('min_version', context.getClass());
        if (minVersion && this.compareVersions(requestedVersion, minVersion) < 0) {
            throw new common_1.BadRequestException(`This endpoint requires minimum API version ${minVersion}. ` +
                `Current version: ${requestedVersion}`);
        }
        // Check maximum version requirement
        const maxVersion = this.reflector.get('max_version', context.getHandler()) ||
            this.reflector.get('max_version', context.getClass());
        if (maxVersion && this.compareVersions(requestedVersion, maxVersion) > 0) {
            throw new common_1.BadRequestException(`This endpoint supports maximum API version ${maxVersion}. ` +
                `Current version: ${requestedVersion}`);
        }
    }
    /**
     * Validate experimental features
     */
    async validateExperimentalFeatures(requestedVersion, request, response) {
        const context = this.getCurrentExecutionContext(request);
        if (!context)
            return;
        const experimental = this.reflector.get('experimental', context.getHandler()) ||
            this.reflector.get('experimental', context.getClass());
        if (experimental) {
            // Add experimental headers
            response.setHeader('X-API-Experimental', 'true');
            response.setHeader('X-API-Stability-Level', experimental.stabilityLevel);
            response.setHeader('X-API-Experimental-Warning', experimental.warning);
            if (experimental.feedback) {
                response.setHeader('X-API-Feedback', experimental.feedback);
            }
            // Log experimental feature usage
            this.logger.log('Experimental feature accessed', {
                version: requestedVersion,
                endpoint: request.path,
                stabilityLevel: experimental.stabilityLevel,
            });
        }
    }
    /**
     * Add deprecation headers to response
     */
    addDeprecationHeaders(response, version, config) {
        response.setHeader('X-API-Deprecated', 'true');
        response.setHeader('X-API-Deprecated-Version', version);
        if (config.deprecationDate) {
            response.setHeader('X-API-Deprecation-Date', config.deprecationDate.toISOString());
        }
        if (config.sunsetDate) {
            response.setHeader('X-API-Sunset-Date', config.sunsetDate.toISOString());
        }
        if (config.replacement) {
            response.setHeader('X-API-Replacement', config.replacement);
        }
        if (config.migrationGuide) {
            response.setHeader('X-API-Migration-Guide', config.migrationGuide);
        }
        // Add deprecation warnings
        const warnings = this.versioningService.getDeprecationWarnings(version);
        if (warnings.length > 0) {
            const warningMessages = warnings.map(w => `${w.severity}: ${w.feature || 'Version'} deprecated on ${w.deprecationDate.toISOString()}`);
            response.setHeader('X-API-Deprecation-Warnings', warningMessages.join('; '));
        }
    }
    /**
     * Get replacement version for sunset version
     */
    getReplacementVersion(sunsetVersion) {
        // Logic to determine replacement version
        const supportedVersions = this.versioningService.getSupportedVersions()
            .filter(v => v.status === 'active')
            .sort((a, b) => this.compareVersions(b.version, a.version));
        return supportedVersions[0]?.version || 'latest';
    }
    /**
     * Compare two version strings
     */
    compareVersions(a, b) {
        const aParts = a.split('.').map(Number);
        const bParts = b.split('.').map(Number);
        for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {
            const aPart = aParts[i] || 0;
            const bPart = bParts[i] || 0;
            if (aPart > bPart)
                return 1;
            if (aPart < bPart)
                return -1;
        }
        return 0;
    }
    /**
     * Get current execution context (helper method)
     */
    getCurrentExecutionContext(request) {
        // This is a simplified approach - in a real implementation,
        // you might need to store the context in the request or use a different approach
        return request.executionContext || null;
    }
};
exports.ApiVersionGuard = ApiVersionGuard;
exports.ApiVersionGuard = ApiVersionGuard = ApiVersionGuard_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof core_1.Reflector !== "undefined" && core_1.Reflector) === "function" ? _a : Object, typeof (_b = typeof api_versioning_service_1.ApiVersioningService !== "undefined" && api_versioning_service_1.ApiVersioningService) === "function" ? _b : Object])
], ApiVersionGuard);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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