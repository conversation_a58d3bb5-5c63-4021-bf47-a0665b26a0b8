{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\api\\controllers\\vulnerability-assessment.controller.ts", "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAawB;AACxB,6CAQyB;AACzB,0FAAqF;AACrF,oFAAgF;AAChF,gGAAmF;AACnF,8GAAgG;AAChG,kHAA6G;AAC7G,wEAAmE;AACnE,wEAAmE;AACnE,0EAAqE;AAErE;;;GAGG;AAKI,IAAM,iCAAiC,GAAvC,MAAM,iCAAiC;IAC5C,YAA6B,iBAAiD;QAAjD,sBAAiB,GAAjB,iBAAiB,CAAgC;IAAG,CAAC;IAElF;;OAEG;IAoBG,AAAN,KAAK,CAAC,iBAAiB,CAAwB,KAA2B;QACxE,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IAaG,AAAN,KAAK,CAAC,oBAAoB,CAA6B,EAAU;QAC/D,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IAaG,AAAN,KAAK,CAAC,gBAAgB,CACE,mBAAwC,EAC/C,IAAS;QAExB,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;IACrF,CAAC;IAED;;OAEG;IAcG,AAAN,KAAK,CAAC,gBAAgB,CACQ,EAAU,EAChB,mBAAwC,EAC/C,IAAS;QAExB,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,EAAE,EAAE,mBAAmB,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;IACzF,CAAC;IAED;;OAEG;IAaG,AAAN,KAAK,CAAC,kBAAkB,CACM,EAAU,EACvB,IAAS;QAExB,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IAuBG,AAAN,KAAK,CAAC,gBAAgB,CACQ,EAAU,EAC9B,UAGP,EACc,IAAS;QAExB,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IAaG,AAAN,KAAK,CAAC,gBAAgB,CACQ,EAAU,EACvB,IAAS;QAExB,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IASG,AAAN,KAAK,CAAC,oBAAoB,CAA6B,EAAU;QAC/D,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;QACzE,OAAO,UAAU,CAAC,UAAU,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IASG,AAAN,KAAK,CAAC,gBAAgB,CAA6B,EAAU;QAC3D,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;QACzE,OAAO,UAAU,CAAC,kBAAkB,EAAE,CAAC;IACzC,CAAC;IAED;;OAEG;IAkBG,AAAN,KAAK,CAAC,mBAAmB,CACK,EAAU,EAC9B,IAA+B,EACxB,IAAS;QAExB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;QACzE,UAAU,CAAC,mBAAmB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACnD,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IAkBG,AAAN,KAAK,CAAC,UAAU,CACc,EAAU,EAC9B,IAA+B,EACxB,IAAS;QAExB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;QACzE,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC1C,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IASG,AAAN,KAAK,CAAC,6BAA6B,CAA0C,eAAuB;QAClG,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC;YACpD,gBAAgB,EAAE,CAAC,eAAe,CAAC;SACpC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IASG,AAAN,KAAK,CAAC,qBAAqB,CAAkC,OAAe;QAC1E,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC;YACpD,QAAQ,EAAE,CAAC,OAAO,CAAC;SACpB,CAAC,CAAC;IACL,CAAC;CACF,CAAA;AA3SY,8EAAiC;AAyBtC;IAnBL,IAAA,YAAG,GAAE;IACL,IAAA,uBAAK,EAAC,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,QAAQ,CAAC;IAChE,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,gEAAgE,EAAE,CAAC;IAC3F,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC;IACrF,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,gBAAgB,EAAE,CAAC;IACzF,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,WAAW,EAAE,6BAA6B,EAAE,CAAC;IACnH,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,WAAW,EAAE,qBAAqB,EAAE,CAAC;IACnG,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,WAAW,EAAE,oBAAoB,EAAE,CAAC;IAClG,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,WAAW,EAAE,4BAA4B,EAAE,CAAC;IACjH,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,WAAW,EAAE,oBAAoB,EAAE,CAAC;IACpG,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,WAAW,EAAE,+BAA+B,EAAE,CAAC;IAC/G,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,iCAAiC,EAAE,CAAC;IACrH,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,gCAAgC,EAAE,CAAC;IACnH,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,CAAC;IACtF,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,WAAW,EAAE,YAAY,EAAE,CAAC;IAClG,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,oCAAoC;KAClD,CAAC;IACuB,WAAA,IAAA,cAAK,EAAC,uBAAc,CAAC,CAAA;;yDAAQ,6CAAoB,oBAApB,6CAAoB;;0EAEzE;AAiBK;IAZL,IAAA,YAAG,EAAC,KAAK,CAAC;IACV,IAAA,uBAAK,EAAC,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,QAAQ,CAAC;IAChE,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,8BAA8B,EAAE,CAAC;IACzD,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,eAAe,EAAE,CAAC;IACtD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,2CAA2C;KACzD,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,SAAS;QAC5B,WAAW,EAAE,sBAAsB;KACpC,CAAC;IAC0B,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;;;;6EAErD;AAiBK;IAZL,IAAA,aAAI,GAAE;IACN,IAAA,uBAAK,EAAC,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,CAAC;IACtD,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,uCAAuC,EAAE,CAAC;IAClE,IAAA,iBAAO,EAAC,EAAE,IAAI,EAAE,2CAAmB,EAAE,CAAC;IACtC,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,OAAO;QAC1B,WAAW,EAAE,iCAAiC;KAC/C,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,WAAW;QAC9B,WAAW,EAAE,yBAAyB;KACvC,CAAC;IAEC,WAAA,IAAA,aAAI,EAAC,uBAAc,CAAC,CAAA;IACpB,WAAA,IAAA,oCAAW,GAAE,CAAA;;yDAD6B,2CAAmB,oBAAnB,2CAAmB;;yEAI/D;AAkBK;IAbL,IAAA,YAAG,EAAC,KAAK,CAAC;IACV,IAAA,uBAAK,EAAC,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,CAAC;IACtD,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,mBAAmB,EAAE,CAAC;IAC9C,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,eAAe,EAAE,CAAC;IACtD,IAAA,iBAAO,EAAC,EAAE,IAAI,EAAE,2CAAmB,EAAE,CAAC;IACtC,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,iCAAiC;KAC/C,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,SAAS;QAC5B,WAAW,EAAE,sBAAsB;KACpC,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;IAC1B,WAAA,IAAA,aAAI,EAAC,uBAAc,CAAC,CAAA;IACpB,WAAA,IAAA,oCAAW,GAAE,CAAA;;iEAD6B,2CAAmB,oBAAnB,2CAAmB;;yEAI/D;AAiBK;IAZL,IAAA,YAAG,EAAC,cAAc,CAAC;IACnB,IAAA,uBAAK,EAAC,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,CAAC;IACtD,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,qBAAqB,EAAE,CAAC;IAChD,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,eAAe,EAAE,CAAC;IACtD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,mCAAmC;KACjD,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,SAAS;QAC5B,WAAW,EAAE,sBAAsB;KACpC,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;IAC1B,WAAA,IAAA,oCAAW,GAAE,CAAA;;;;2EAGf;AA2BK;IAtBL,IAAA,YAAG,EAAC,YAAY,CAAC;IACjB,IAAA,uBAAK,EAAC,OAAO,EAAE,kBAAkB,CAAC;IAClC,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,mBAAmB,EAAE,CAAC;IAC9C,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,eAAe,EAAE,CAAC;IACtD,IAAA,iBAAO,EAAC;QACP,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,QAAQ,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;gBAC7B,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;aAC7B;YACD,QAAQ,EAAE,CAAC,UAAU,CAAC;SACvB;KACF,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,kCAAkC;KAChD,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,SAAS;QAC5B,WAAW,EAAE,sBAAsB;KACpC,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;IAC1B,WAAA,IAAA,aAAI,GAAE,CAAA;IAIN,WAAA,IAAA,oCAAW,GAAE,CAAA;;;;yEAGf;AAiBK;IAZL,IAAA,eAAM,EAAC,KAAK,CAAC;IACb,IAAA,uBAAK,EAAC,OAAO,EAAE,kBAAkB,CAAC;IAClC,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,mBAAmB,EAAE,CAAC;IAC9C,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,eAAe,EAAE,CAAC;IACtD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,UAAU;QAC7B,WAAW,EAAE,iCAAiC;KAC/C,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,SAAS;QAC5B,WAAW,EAAE,sBAAsB;KACpC,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;IAC1B,WAAA,IAAA,oCAAW,GAAE,CAAA;;;;yEAGf;AAaK;IARL,IAAA,YAAG,EAAC,aAAa,CAAC;IAClB,IAAA,uBAAK,EAAC,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,QAAQ,CAAC;IAChE,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC;IACnD,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,eAAe,EAAE,CAAC;IACtD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,2CAA2C;KACzD,CAAC;IAC0B,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;;;;6EAGrD;AAaK;IARL,IAAA,YAAG,EAAC,YAAY,CAAC;IACjB,IAAA,uBAAK,EAAC,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,CAAC;IACtD,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,sCAAsC,EAAE,CAAC;IACjE,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,eAAe,EAAE,CAAC;IACtD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,uCAAuC;KACrD,CAAC;IACsB,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;;;;yEAGjD;AAsBK;IAjBL,IAAA,YAAG,EAAC,oBAAoB,CAAC;IACzB,IAAA,uBAAK,EAAC,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,CAAC;IACtD,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,mCAAmC,EAAE,CAAC;IAC9D,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,eAAe,EAAE,CAAC;IACtD,IAAA,iBAAO,EAAC;QACP,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,aAAa,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;aAClC;YACD,QAAQ,EAAE,CAAC,eAAe,CAAC;SAC5B;KACF,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,kDAAkD;KAChE,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;IAC1B,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,oCAAW,GAAE,CAAA;;;;4EAKf;AAsBK;IAjBL,IAAA,YAAG,EAAC,iBAAiB,CAAC;IACtB,IAAA,uBAAK,EAAC,OAAO,EAAE,kBAAkB,CAAC;IAClC,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,4BAA4B,EAAE,CAAC;IACvD,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,eAAe,EAAE,CAAC;IACtD,IAAA,iBAAO,EAAC;QACP,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,aAAa,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;aAClC;YACD,QAAQ,EAAE,CAAC,eAAe,CAAC;SAC5B;KACF,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,4BAA4B;KAC1C,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;IAC1B,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,oCAAW,GAAE,CAAA;;;;mEAKf;AAaK;IARL,IAAA,YAAG,EAAC,gCAAgC,CAAC;IACrC,IAAA,uBAAK,EAAC,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,QAAQ,CAAC;IAChE,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,8CAA8C,EAAE,CAAC;IACzE,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IACtE,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,oCAAoC;KAClD,CAAC;IACmC,WAAA,IAAA,cAAK,EAAC,iBAAiB,EAAE,sBAAa,CAAC,CAAA;;;;sFAI3E;AAaK;IARL,IAAA,YAAG,EAAC,gBAAgB,CAAC;IACrB,IAAA,uBAAK,EAAC,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,QAAQ,CAAC;IAChE,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,sCAAsC,EAAE,CAAC;IACjE,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,CAAC;IACtD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,oCAAoC;KAClD,CAAC;IAC2B,WAAA,IAAA,cAAK,EAAC,SAAS,EAAE,sBAAa,CAAC,CAAA;;;;8EAI3D;4CA1SU,iCAAiC;IAJ7C,IAAA,iBAAO,EAAC,2BAA2B,CAAC;IACpC,IAAA,uBAAa,GAAE;IACf,IAAA,kBAAS,EAAC,6BAAY,EAAE,wBAAU,CAAC;IACnC,IAAA,mBAAU,EAAC,+BAA+B,CAAC;yDAEM,iEAA8B,oBAA9B,iEAA8B;GADnE,iCAAiC,CA2S7C", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\api\\controllers\\vulnerability-assessment.controller.ts"], "sourcesContent": ["import {\r\n  Controller,\r\n  Get,\r\n  Post,\r\n  Put,\r\n  Delete,\r\n  Body,\r\n  Param,\r\n  Query,\r\n  UseGuards,\r\n  HttpStatus,\r\n  ParseUUIDPipe,\r\n  ValidationPipe,\r\n} from '@nestjs/common';\r\nimport {\r\n  ApiTags,\r\n  ApiOperation,\r\n  ApiResponse,\r\n  ApiParam,\r\n  ApiQuery,\r\n  ApiBearerAuth,\r\n  ApiBody,\r\n} from '@nestjs/swagger';\r\nimport { JwtAuthGuard } from '../../../../infrastructure/auth/guards/jwt-auth.guard';\r\nimport { RolesGuard } from '../../../../infrastructure/auth/guards/roles.guard';\r\nimport { Roles } from '../../../../infrastructure/auth/decorators/roles.decorator';\r\nimport { CurrentUser } from '../../../../infrastructure/auth/decorators/current-user.decorator';\r\nimport { VulnerabilityAssessmentService } from '../../application/services/vulnerability-assessment.service';\r\nimport { CreateAssessmentDto } from '../dto/create-assessment.dto';\r\nimport { UpdateAssessmentDto } from '../dto/update-assessment.dto';\r\nimport { SearchAssessmentsDto } from '../dto/search-assessments.dto';\r\n\r\n/**\r\n * Vulnerability Assessment controller\r\n * Handles vulnerability assessment API endpoints\r\n */\r\n@ApiTags('Vulnerability Assessments')\r\n@ApiBearerAuth()\r\n@UseGuards(JwtAuthGuard, RolesGuard)\r\n@Controller('api/vulnerability-assessments')\r\nexport class VulnerabilityAssessmentController {\r\n  constructor(private readonly assessmentService: VulnerabilityAssessmentService) {}\r\n\r\n  /**\r\n   * Search assessments\r\n   */\r\n  @Get()\r\n  @Roles('admin', 'security_analyst', 'security_manager', 'viewer')\r\n  @ApiOperation({ summary: 'Search vulnerability assessments with filtering and pagination' })\r\n  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })\r\n  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })\r\n  @ApiQuery({ name: 'vulnerabilityIds', required: false, type: [String], description: 'Filter by vulnerability IDs' })\r\n  @ApiQuery({ name: 'assetIds', required: false, type: [String], description: 'Filter by asset IDs' })\r\n  @ApiQuery({ name: 'statuses', required: false, type: [String], description: 'Filter by statuses' })\r\n  @ApiQuery({ name: 'assessmentTypes', required: false, type: [String], description: 'Filter by assessment types' })\r\n  @ApiQuery({ name: 'assessedBy', required: false, type: [String], description: 'Filter by assessor' })\r\n  @ApiQuery({ name: 'severities', required: false, type: [String], description: 'Filter by assessed severities' })\r\n  @ApiQuery({ name: 'isFalsePositive', required: false, type: Boolean, description: 'Filter by false positive status' })\r\n  @ApiQuery({ name: 'isAcceptedRisk', required: false, type: Boolean, description: 'Filter by accepted risk status' })\r\n  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Sort field' })\r\n  @ApiQuery({ name: 'sortOrder', required: false, enum: ['ASC', 'DESC'], description: 'Sort order' })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Assessments retrieved successfully',\r\n  })\r\n  async searchAssessments(@Query(ValidationPipe) query: SearchAssessmentsDto) {\r\n    return await this.assessmentService.searchAssessments(query);\r\n  }\r\n\r\n  /**\r\n   * Get assessment details\r\n   */\r\n  @Get(':id')\r\n  @Roles('admin', 'security_analyst', 'security_manager', 'viewer')\r\n  @ApiOperation({ summary: 'Get assessment details by ID' })\r\n  @ApiParam({ name: 'id', description: 'Assessment ID' })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Assessment details retrieved successfully',\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.NOT_FOUND,\r\n    description: 'Assessment not found',\r\n  })\r\n  async getAssessmentDetails(@Param('id', ParseUUIDPipe) id: string) {\r\n    return await this.assessmentService.getAssessmentDetails(id);\r\n  }\r\n\r\n  /**\r\n   * Create assessment\r\n   */\r\n  @Post()\r\n  @Roles('admin', 'security_analyst', 'security_manager')\r\n  @ApiOperation({ summary: 'Create a new vulnerability assessment' })\r\n  @ApiBody({ type: CreateAssessmentDto })\r\n  @ApiResponse({\r\n    status: HttpStatus.CREATED,\r\n    description: 'Assessment created successfully',\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.BAD_REQUEST,\r\n    description: 'Invalid assessment data',\r\n  })\r\n  async createAssessment(\r\n    @Body(ValidationPipe) createAssessmentDto: CreateAssessmentDto,\r\n    @CurrentUser() user: any,\r\n  ) {\r\n    return await this.assessmentService.createAssessment(createAssessmentDto, user.id);\r\n  }\r\n\r\n  /**\r\n   * Update assessment\r\n   */\r\n  @Put(':id')\r\n  @Roles('admin', 'security_analyst', 'security_manager')\r\n  @ApiOperation({ summary: 'Update assessment' })\r\n  @ApiParam({ name: 'id', description: 'Assessment ID' })\r\n  @ApiBody({ type: UpdateAssessmentDto })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Assessment updated successfully',\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.NOT_FOUND,\r\n    description: 'Assessment not found',\r\n  })\r\n  async updateAssessment(\r\n    @Param('id', ParseUUIDPipe) id: string,\r\n    @Body(ValidationPipe) updateAssessmentDto: UpdateAssessmentDto,\r\n    @CurrentUser() user: any,\r\n  ) {\r\n    return await this.assessmentService.updateAssessment(id, updateAssessmentDto, user.id);\r\n  }\r\n\r\n  /**\r\n   * Complete assessment\r\n   */\r\n  @Put(':id/complete')\r\n  @Roles('admin', 'security_analyst', 'security_manager')\r\n  @ApiOperation({ summary: 'Complete assessment' })\r\n  @ApiParam({ name: 'id', description: 'Assessment ID' })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Assessment completed successfully',\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.NOT_FOUND,\r\n    description: 'Assessment not found',\r\n  })\r\n  async completeAssessment(\r\n    @Param('id', ParseUUIDPipe) id: string,\r\n    @CurrentUser() user: any,\r\n  ) {\r\n    return await this.assessmentService.completeAssessment(id, user.id);\r\n  }\r\n\r\n  /**\r\n   * Review assessment\r\n   */\r\n  @Put(':id/review')\r\n  @Roles('admin', 'security_manager')\r\n  @ApiOperation({ summary: 'Review assessment' })\r\n  @ApiParam({ name: 'id', description: 'Assessment ID' })\r\n  @ApiBody({\r\n    schema: {\r\n      type: 'object',\r\n      properties: {\r\n        approved: { type: 'boolean' },\r\n        comments: { type: 'string' },\r\n      },\r\n      required: ['approved'],\r\n    },\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Assessment reviewed successfully',\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.NOT_FOUND,\r\n    description: 'Assessment not found',\r\n  })\r\n  async reviewAssessment(\r\n    @Param('id', ParseUUIDPipe) id: string,\r\n    @Body() reviewData: {\r\n      approved: boolean;\r\n      comments?: string;\r\n    },\r\n    @CurrentUser() user: any,\r\n  ) {\r\n    return await this.assessmentService.reviewAssessment(id, reviewData, user.id);\r\n  }\r\n\r\n  /**\r\n   * Delete assessment\r\n   */\r\n  @Delete(':id')\r\n  @Roles('admin', 'security_manager')\r\n  @ApiOperation({ summary: 'Delete assessment' })\r\n  @ApiParam({ name: 'id', description: 'Assessment ID' })\r\n  @ApiResponse({\r\n    status: HttpStatus.NO_CONTENT,\r\n    description: 'Assessment deleted successfully',\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.NOT_FOUND,\r\n    description: 'Assessment not found',\r\n  })\r\n  async deleteAssessment(\r\n    @Param('id', ParseUUIDPipe) id: string,\r\n    @CurrentUser() user: any,\r\n  ) {\r\n    await this.assessmentService.deleteAssessment(id, user.id);\r\n  }\r\n\r\n  /**\r\n   * Get assessment summary\r\n   */\r\n  @Get(':id/summary')\r\n  @Roles('admin', 'security_analyst', 'security_manager', 'viewer')\r\n  @ApiOperation({ summary: 'Get assessment summary' })\r\n  @ApiParam({ name: 'id', description: 'Assessment ID' })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Assessment summary retrieved successfully',\r\n  })\r\n  async getAssessmentSummary(@Param('id', ParseUUIDPipe) id: string) {\r\n    const assessment = await this.assessmentService.getAssessmentDetails(id);\r\n    return assessment.getSummary();\r\n  }\r\n\r\n  /**\r\n   * Export assessment for reporting\r\n   */\r\n  @Get(':id/export')\r\n  @Roles('admin', 'security_analyst', 'security_manager')\r\n  @ApiOperation({ summary: 'Export assessment data for reporting' })\r\n  @ApiParam({ name: 'id', description: 'Assessment ID' })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Assessment data exported successfully',\r\n  })\r\n  async exportAssessment(@Param('id', ParseUUIDPipe) id: string) {\r\n    const assessment = await this.assessmentService.getAssessmentDetails(id);\r\n    return assessment.exportForReporting();\r\n  }\r\n\r\n  /**\r\n   * Mark as false positive\r\n   */\r\n  @Put(':id/false-positive')\r\n  @Roles('admin', 'security_analyst', 'security_manager')\r\n  @ApiOperation({ summary: 'Mark assessment as false positive' })\r\n  @ApiParam({ name: 'id', description: 'Assessment ID' })\r\n  @ApiBody({\r\n    schema: {\r\n      type: 'object',\r\n      properties: {\r\n        justification: { type: 'string' },\r\n      },\r\n      required: ['justification'],\r\n    },\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Assessment marked as false positive successfully',\r\n  })\r\n  async markAsFalsePositive(\r\n    @Param('id', ParseUUIDPipe) id: string,\r\n    @Body() data: { justification: string },\r\n    @CurrentUser() user: any,\r\n  ) {\r\n    const assessment = await this.assessmentService.getAssessmentDetails(id);\r\n    assessment.markAsFalsePositive(data.justification);\r\n    return await this.assessmentService.updateAssessment(id, assessment, user.id);\r\n  }\r\n\r\n  /**\r\n   * Accept risk\r\n   */\r\n  @Put(':id/accept-risk')\r\n  @Roles('admin', 'security_manager')\r\n  @ApiOperation({ summary: 'Accept risk for assessment' })\r\n  @ApiParam({ name: 'id', description: 'Assessment ID' })\r\n  @ApiBody({\r\n    schema: {\r\n      type: 'object',\r\n      properties: {\r\n        justification: { type: 'string' },\r\n      },\r\n      required: ['justification'],\r\n    },\r\n  })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Risk accepted successfully',\r\n  })\r\n  async acceptRisk(\r\n    @Param('id', ParseUUIDPipe) id: string,\r\n    @Body() data: { justification: string },\r\n    @CurrentUser() user: any,\r\n  ) {\r\n    const assessment = await this.assessmentService.getAssessmentDetails(id);\r\n    assessment.acceptRisk(data.justification);\r\n    return await this.assessmentService.updateAssessment(id, assessment, user.id);\r\n  }\r\n\r\n  /**\r\n   * Get assessments by vulnerability\r\n   */\r\n  @Get('vulnerability/:vulnerabilityId')\r\n  @Roles('admin', 'security_analyst', 'security_manager', 'viewer')\r\n  @ApiOperation({ summary: 'Get assessments for a specific vulnerability' })\r\n  @ApiParam({ name: 'vulnerabilityId', description: 'Vulnerability ID' })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Assessments retrieved successfully',\r\n  })\r\n  async getAssessmentsByVulnerability(@Param('vulnerabilityId', ParseUUIDPipe) vulnerabilityId: string) {\r\n    return await this.assessmentService.searchAssessments({\r\n      vulnerabilityIds: [vulnerabilityId],\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get assessments by asset\r\n   */\r\n  @Get('asset/:assetId')\r\n  @Roles('admin', 'security_analyst', 'security_manager', 'viewer')\r\n  @ApiOperation({ summary: 'Get assessments for a specific asset' })\r\n  @ApiParam({ name: 'assetId', description: 'Asset ID' })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Assessments retrieved successfully',\r\n  })\r\n  async getAssessmentsByAsset(@Param('assetId', ParseUUIDPipe) assetId: string) {\r\n    return await this.assessmentService.searchAssessments({\r\n      assetIds: [assetId],\r\n    });\r\n  }\r\n}\r\n"], "version": 3}