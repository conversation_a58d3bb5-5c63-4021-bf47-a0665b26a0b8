fa7be3a1dd3c04d08e66cd4cb31fddd3
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const compliance_policy_1 = require("../compliance-policy");
const tenant_id_value_object_1 = require("../../../../../shared-kernel/value-objects/tenant-id.value-object");
const user_id_value_object_1 = require("../../../../../shared-kernel/value-objects/user-id.value-object");
const timestamp_value_object_1 = require("../../../../../shared-kernel/value-objects/timestamp.value-object");
const validation_exception_1 = require("../../../../../shared-kernel/exceptions/validation.exception");
describe('CompliancePolicy', () => {
    let tenantId;
    let userId;
    let validControl;
    beforeEach(() => {
        tenantId = tenant_id_value_object_1.TenantId.create('tenant-123');
        userId = user_id_value_object_1.UserId.create('user-123');
        validControl = {
            id: 'control-1',
            name: 'Access Control',
            description: 'Ensure proper access controls are in place',
            framework: compliance_policy_1.ComplianceFramework.SOC2,
            controlId: 'CC6.1',
            type: compliance_policy_1.ComplianceControlType.PREVENTIVE,
            requirements: ['Multi-factor authentication', 'Role-based access'],
            testProcedures: ['Review user access logs', 'Test MFA implementation'],
            evidence: [],
            status: compliance_policy_1.ComplianceStatus.NOT_ASSESSED,
            responsible: userId,
            priority: compliance_policy_1.CompliancePriority.HIGH,
            automatedCheck: true,
            checkFrequency: compliance_policy_1.ComplianceCheckFrequency.MONTHLY
        };
    });
    describe('creation', () => {
        it('should create a valid compliance policy', () => {
            const policy = compliance_policy_1.CompliancePolicy.create({
                name: 'SOC2 Policy',
                description: 'SOC2 compliance policy',
                framework: compliance_policy_1.ComplianceFramework.SOC2,
                version: '1.0.0',
                tenantId,
                controls: [validControl],
                enabled: true,
                createdBy: userId
            });
            expect(policy).toBeDefined();
            expect(policy.name).toBe('SOC2 Policy');
            expect(policy.framework).toBe(compliance_policy_1.ComplianceFramework.SOC2);
            expect(policy.controls).toHaveLength(1);
            expect(policy.enabled).toBe(true);
        });
        it('should throw validation exception for missing name', () => {
            expect(() => {
                compliance_policy_1.CompliancePolicy.create({
                    name: '',
                    description: 'SOC2 compliance policy',
                    framework: compliance_policy_1.ComplianceFramework.SOC2,
                    version: '1.0.0',
                    tenantId,
                    controls: [],
                    enabled: true,
                    createdBy: userId
                });
            }).toThrow(validation_exception_1.ValidationException);
        });
        it('should throw validation exception for invalid framework', () => {
            expect(() => {
                compliance_policy_1.CompliancePolicy.create({
                    name: 'Test Policy',
                    description: 'Test policy',
                    framework: 'INVALID',
                    version: '1.0.0',
                    tenantId,
                    controls: [],
                    enabled: true,
                    createdBy: userId
                });
            }).toThrow(validation_exception_1.ValidationException);
        });
        it('should throw validation exception for invalid control', () => {
            const invalidControl = { ...validControl, controlId: '' };
            expect(() => {
                compliance_policy_1.CompliancePolicy.create({
                    name: 'Test Policy',
                    description: 'Test policy',
                    framework: compliance_policy_1.ComplianceFramework.SOC2,
                    version: '1.0.0',
                    tenantId,
                    controls: [invalidControl],
                    enabled: true,
                    createdBy: userId
                });
            }).toThrow(validation_exception_1.ValidationException);
        });
    });
    describe('compliance assessment', () => {
        let policy;
        beforeEach(() => {
            policy = compliance_policy_1.CompliancePolicy.create({
                name: 'SOC2 Policy',
                description: 'SOC2 compliance policy',
                framework: compliance_policy_1.ComplianceFramework.SOC2,
                version: '1.0.0',
                tenantId,
                controls: [validControl],
                enabled: true,
                createdBy: userId
            });
        });
        it('should assess compliance for a control', () => {
            const assessment = policy.assessCompliance('control-1', userId);
            expect(assessment).toBeDefined();
            expect(assessment.controlId).toBe('control-1');
            expect(assessment.assessedBy).toBe(userId);
            expect(assessment.status).toBeDefined();
            expect(assessment.nextAssessmentDue).toBeDefined();
        });
        it('should throw error when assessing non-existent control', () => {
            expect(() => {
                policy.assessCompliance('non-existent', userId);
            }).toThrow(validation_exception_1.ValidationException);
        });
        it('should update control status after assessment', () => {
            const originalStatus = policy.controls[0].status;
            policy.assessCompliance('control-1', userId);
            const updatedControl = policy.controls.find(c => c.id === 'control-1');
            expect(updatedControl?.lastAssessed).toBeDefined();
            expect(updatedControl?.nextAssessment).toBeDefined();
        });
        it('should calculate next assessment date based on frequency', () => {
            const monthlyControl = { ...validControl, id: 'control-monthly', checkFrequency: compliance_policy_1.ComplianceCheckFrequency.MONTHLY };
            policy.addControl(monthlyControl, userId);
            const assessment = policy.assessCompliance('control-monthly', userId);
            const nextAssessment = assessment.nextAssessmentDue.value;
            const now = new Date();
            const expectedNext = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());
            expect(nextAssessment.getMonth()).toBe(expectedNext.getMonth());
        });
    });
    describe('compliance reporting', () => {
        let policy;
        beforeEach(() => {
            policy = compliance_policy_1.CompliancePolicy.create({
                name: 'SOC2 Policy',
                description: 'SOC2 compliance policy',
                framework: compliance_policy_1.ComplianceFramework.SOC2,
                version: '1.0.0',
                tenantId,
                controls: [validControl],
                enabled: true,
                createdBy: userId
            });
        });
        it('should generate compliance report', () => {
            const startDate = timestamp_value_object_1.Timestamp.create(new Date('2024-01-01'));
            const endDate = timestamp_value_object_1.Timestamp.create(new Date('2024-12-31'));
            const report = policy.generateComplianceReport(compliance_policy_1.ComplianceFramework.SOC2, { startDate, endDate }, userId);
            expect(report).toBeDefined();
            expect(report.framework).toBe(compliance_policy_1.ComplianceFramework.SOC2);
            expect(report.generatedBy).toBe(userId);
            expect(report.controlsSummary).toBeDefined();
            expect(report.overallStatus).toBeDefined();
            expect(report.executiveSummary).toBeDefined();
        });
        it('should calculate controls summary correctly', () => {
            // Add controls with different statuses
            const compliantControl = {
                ...validControl,
                id: 'control-2',
                status: compliance_policy_1.ComplianceStatus.COMPLIANT
            };
            const nonCompliantControl = {
                ...validControl,
                id: 'control-3',
                status: compliance_policy_1.ComplianceStatus.NON_COMPLIANT
            };
            policy.addControl(compliantControl, userId);
            policy.addControl(nonCompliantControl, userId);
            const startDate = timestamp_value_object_1.Timestamp.create(new Date('2024-01-01'));
            const endDate = timestamp_value_object_1.Timestamp.create(new Date('2024-12-31'));
            const report = policy.generateComplianceReport(compliance_policy_1.ComplianceFramework.SOC2, { startDate, endDate }, userId);
            expect(report.controlsSummary.total).toBe(3);
            expect(report.controlsSummary.compliant).toBe(1);
            expect(report.controlsSummary.nonCompliant).toBe(1);
            expect(report.controlsSummary.notAssessed).toBe(1);
        });
        it('should determine overall status correctly', () => {
            const nonCompliantControl = {
                ...validControl,
                id: 'control-2',
                status: compliance_policy_1.ComplianceStatus.NON_COMPLIANT
            };
            policy.addControl(nonCompliantControl, userId);
            const startDate = timestamp_value_object_1.Timestamp.create(new Date('2024-01-01'));
            const endDate = timestamp_value_object_1.Timestamp.create(new Date('2024-12-31'));
            const report = policy.generateComplianceReport(compliance_policy_1.ComplianceFramework.SOC2, { startDate, endDate }, userId);
            expect(report.overallStatus).toBe(compliance_policy_1.ComplianceStatus.NON_COMPLIANT);
        });
    });
    describe('control management', () => {
        let policy;
        beforeEach(() => {
            policy = compliance_policy_1.CompliancePolicy.create({
                name: 'SOC2 Policy',
                description: 'SOC2 compliance policy',
                framework: compliance_policy_1.ComplianceFramework.SOC2,
                version: '1.0.0',
                tenantId,
                controls: [validControl],
                enabled: true,
                createdBy: userId
            });
        });
        it('should add a new control', () => {
            const newControl = { ...validControl, id: 'control-2', name: 'New Control' };
            policy.addControl(newControl, userId);
            expect(policy.controls).toHaveLength(2);
            expect(policy.controls.find(c => c.id === 'control-2')).toBeDefined();
        });
        it('should throw error when adding duplicate control ID', () => {
            expect(() => {
                policy.addControl(validControl, userId);
            }).toThrow(validation_exception_1.ValidationException);
        });
        it('should update an existing control', () => {
            policy.updateControl('control-1', { name: 'Updated Control' }, userId);
            const updatedControl = policy.controls.find(c => c.id === 'control-1');
            expect(updatedControl?.name).toBe('Updated Control');
        });
        it('should throw error when updating non-existent control', () => {
            expect(() => {
                policy.updateControl('non-existent', { name: 'Updated' }, userId);
            }).toThrow(validation_exception_1.ValidationException);
        });
        it('should remove a control', () => {
            policy.removeControl('control-1', userId);
            expect(policy.controls).toHaveLength(0);
        });
        it('should throw error when removing non-existent control', () => {
            expect(() => {
                policy.removeControl('non-existent', userId);
            }).toThrow(validation_exception_1.ValidationException);
        });
    });
    describe('evidence management', () => {
        let policy;
        beforeEach(() => {
            policy = compliance_policy_1.CompliancePolicy.create({
                name: 'SOC2 Policy',
                description: 'SOC2 compliance policy',
                framework: compliance_policy_1.ComplianceFramework.SOC2,
                version: '1.0.0',
                tenantId,
                controls: [validControl],
                enabled: true,
                createdBy: userId
            });
        });
        it('should add evidence to a control', () => {
            const evidence = {
                id: 'evidence-1',
                type: compliance_policy_1.ComplianceEvidenceType.DOCUMENT,
                description: 'Access control policy document',
                source: 'Policy Management System',
                collectedAt: timestamp_value_object_1.Timestamp.now(),
                collectedBy: userId,
                metadata: { documentId: 'doc-123' }
            };
            policy.addEvidence('control-1', evidence, userId);
            const control = policy.controls.find(c => c.id === 'control-1');
            expect(control?.evidence).toHaveLength(1);
            expect(control?.evidence[0].id).toBe('evidence-1');
        });
        it('should throw error when adding evidence to non-existent control', () => {
            const evidence = {
                id: 'evidence-1',
                type: compliance_policy_1.ComplianceEvidenceType.DOCUMENT,
                description: 'Test evidence',
                source: 'Test',
                collectedAt: timestamp_value_object_1.Timestamp.now(),
                collectedBy: userId,
                metadata: {}
            };
            expect(() => {
                policy.addEvidence('non-existent', evidence, userId);
            }).toThrow(validation_exception_1.ValidationException);
        });
    });
    describe('compliance status summary', () => {
        let policy;
        beforeEach(() => {
            policy = compliance_policy_1.CompliancePolicy.create({
                name: 'Multi-Framework Policy',
                description: 'Policy covering multiple frameworks',
                framework: compliance_policy_1.ComplianceFramework.SOC2,
                version: '1.0.0',
                tenantId,
                controls: [],
                enabled: true,
                createdBy: userId
            });
        });
        it('should return status summary for all frameworks', () => {
            // Add controls for different frameworks
            const soc2Control = { ...validControl, framework: compliance_policy_1.ComplianceFramework.SOC2, status: compliance_policy_1.ComplianceStatus.COMPLIANT };
            const gdprControl = { ...validControl, id: 'control-2', framework: compliance_policy_1.ComplianceFramework.GDPR, status: compliance_policy_1.ComplianceStatus.NON_COMPLIANT };
            policy.addControl(soc2Control, userId);
            policy.addControl(gdprControl, userId);
            const summary = policy.getComplianceStatusSummary();
            expect(summary[compliance_policy_1.ComplianceFramework.SOC2]).toBe(compliance_policy_1.ComplianceStatus.COMPLIANT);
            expect(summary[compliance_policy_1.ComplianceFramework.GDPR]).toBe(compliance_policy_1.ComplianceStatus.NON_COMPLIANT);
            expect(summary[compliance_policy_1.ComplianceFramework.HIPAA]).toBe(compliance_policy_1.ComplianceStatus.NOT_ASSESSED);
        });
        it('should aggregate status correctly for multiple controls', () => {
            const control1 = { ...validControl, framework: compliance_policy_1.ComplianceFramework.SOC2, status: compliance_policy_1.ComplianceStatus.COMPLIANT };
            const control2 = { ...validControl, id: 'control-2', framework: compliance_policy_1.ComplianceFramework.SOC2, status: compliance_policy_1.ComplianceStatus.PARTIALLY_COMPLIANT };
            policy.addControl(control1, userId);
            policy.addControl(control2, userId);
            const summary = policy.getComplianceStatusSummary();
            // Should be partially compliant due to mixed statuses
            expect(summary[compliance_policy_1.ComplianceFramework.SOC2]).toBe(compliance_policy_1.ComplianceStatus.PARTIALLY_COMPLIANT);
        });
    });
    describe('controls requiring assessment', () => {
        let policy;
        beforeEach(() => {
            policy = compliance_policy_1.CompliancePolicy.create({
                name: 'SOC2 Policy',
                description: 'SOC2 compliance policy',
                framework: compliance_policy_1.ComplianceFramework.SOC2,
                version: '1.0.0',
                tenantId,
                controls: [],
                enabled: true,
                createdBy: userId
            });
        });
        it('should return controls that need assessment', () => {
            const pastDue = new Date();
            pastDue.setDate(pastDue.getDate() - 1);
            const overdueControl = {
                ...validControl,
                nextAssessment: timestamp_value_object_1.Timestamp.create(pastDue)
            };
            const futureControl = {
                ...validControl,
                id: 'control-2',
                nextAssessment: timestamp_value_object_1.Timestamp.create(new Date(Date.now() + 86400000)) // Tomorrow
            };
            policy.addControl(overdueControl, userId);
            policy.addControl(futureControl, userId);
            const controlsRequiringAssessment = policy.getControlsRequiringAssessment();
            expect(controlsRequiringAssessment).toHaveLength(1);
            expect(controlsRequiringAssessment[0].id).toBe('control-1');
        });
        it('should return controls with no next assessment date', () => {
            const controlWithoutDate = { ...validControl };
            delete controlWithoutDate.nextAssessment;
            policy.addControl(controlWithoutDate, userId);
            const controlsRequiringAssessment = policy.getControlsRequiringAssessment();
            expect(controlsRequiringAssessment).toHaveLength(1);
            expect(controlsRequiringAssessment[0].id).toBe('control-1');
        });
    });
    describe('validation', () => {
        it('should validate control properties', () => {
            const invalidControl = { ...validControl, type: 'INVALID' };
            expect(() => {
                compliance_policy_1.CompliancePolicy.create({
                    name: 'Test Policy',
                    description: 'Test policy',
                    framework: compliance_policy_1.ComplianceFramework.SOC2,
                    version: '1.0.0',
                    tenantId,
                    controls: [invalidControl],
                    enabled: true,
                    createdBy: userId
                });
            }).toThrow(validation_exception_1.ValidationException);
        });
        it('should validate required control fields', () => {
            const controlMissingName = { ...validControl, name: '' };
            expect(() => {
                compliance_policy_1.CompliancePolicy.create({
                    name: 'Test Policy',
                    description: 'Test policy',
                    framework: compliance_policy_1.ComplianceFramework.SOC2,
                    version: '1.0.0',
                    tenantId,
                    controls: [controlMissingName],
                    enabled: true,
                    createdBy: userId
                });
            }).toThrow(validation_exception_1.ValidationException);
        });
        it('should validate control arrays', () => {
            const controlWithInvalidRequirements = {
                ...validControl,
                requirements: 'not an array'
            };
            expect(() => {
                compliance_policy_1.CompliancePolicy.create({
                    name: 'Test Policy',
                    description: 'Test policy',
                    framework: compliance_policy_1.ComplianceFramework.SOC2,
                    version: '1.0.0',
                    tenantId,
                    controls: [controlWithInvalidRequirements],
                    enabled: true,
                    createdBy: userId
                });
            }).toThrow(validation_exception_1.ValidationException);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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