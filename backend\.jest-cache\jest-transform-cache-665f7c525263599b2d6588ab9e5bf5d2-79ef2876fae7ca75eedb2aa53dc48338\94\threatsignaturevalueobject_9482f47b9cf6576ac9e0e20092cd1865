1d7f0231ab10309a001bcb3f147a45b4
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThreatSignature = exports.SignatureSeverity = exports.SignatureCategory = exports.SignatureType = void 0;
const base_value_object_1 = require("../../../../../shared-kernel/value-objects/base-value-object");
const confidence_level_enum_1 = require("../../enums/confidence-level.enum");
/**
 * Signature Type Enumeration
 */
var SignatureType;
(function (SignatureType) {
    SignatureType["YARA"] = "yara";
    SignatureType["SNORT"] = "snort";
    SignatureType["SURICATA"] = "suricata";
    SignatureType["SIGMA"] = "sigma";
    SignatureType["CUSTOM"] = "custom";
    SignatureType["REGEX"] = "regex";
    SignatureType["HASH"] = "hash";
    SignatureType["BEHAVIORAL"] = "behavioral";
    SignatureType["NETWORK"] = "network";
    SignatureType["ENDPOINT"] = "endpoint";
})(SignatureType || (exports.SignatureType = SignatureType = {}));
/**
 * Signature Category Enumeration
 */
var SignatureCategory;
(function (SignatureCategory) {
    SignatureCategory["MALWARE"] = "malware";
    SignatureCategory["EXPLOIT"] = "exploit";
    SignatureCategory["COMMAND_CONTROL"] = "command_control";
    SignatureCategory["EXFILTRATION"] = "exfiltration";
    SignatureCategory["LATERAL_MOVEMENT"] = "lateral_movement";
    SignatureCategory["PERSISTENCE"] = "persistence";
    SignatureCategory["PRIVILEGE_ESCALATION"] = "privilege_escalation";
    SignatureCategory["DEFENSE_EVASION"] = "defense_evasion";
    SignatureCategory["RECONNAISSANCE"] = "reconnaissance";
    SignatureCategory["INITIAL_ACCESS"] = "initial_access";
})(SignatureCategory || (exports.SignatureCategory = SignatureCategory = {}));
/**
 * Signature Severity Enumeration
 */
var SignatureSeverity;
(function (SignatureSeverity) {
    SignatureSeverity["INFO"] = "info";
    SignatureSeverity["LOW"] = "low";
    SignatureSeverity["MEDIUM"] = "medium";
    SignatureSeverity["HIGH"] = "high";
    SignatureSeverity["CRITICAL"] = "critical";
})(SignatureSeverity || (exports.SignatureSeverity = SignatureSeverity = {}));
/**
 * Threat Signature Value Object
 *
 * Represents a threat detection signature with comprehensive metadata,
 * performance metrics, and validation information.
 *
 * Key features:
 * - Multiple signature types (YARA, Snort, Sigma, etc.)
 * - Performance and accuracy metrics
 * - MITRE ATT&CK technique mapping
 * - Validation and quality assurance
 * - Usage statistics and effectiveness tracking
 *
 * Business Rules:
 * - Signature content must be valid for its type
 * - Confidence must match validation status
 * - Performance metrics must be realistic
 * - MITRE techniques must be valid
 */
class ThreatSignature extends base_value_object_1.BaseValueObject {
    constructor(props) {
        super(props);
    }
    validate() {
        if (!this._value.id || this._value.id.trim().length === 0) {
            throw new Error('Threat signature must have an ID');
        }
        if (!this._value.name || this._value.name.trim().length === 0) {
            throw new Error('Threat signature must have a name');
        }
        if (!Object.values(SignatureType).includes(this._value.type)) {
            throw new Error(`Invalid signature type: ${this._value.type}`);
        }
        if (!Object.values(SignatureCategory).includes(this._value.category)) {
            throw new Error(`Invalid signature category: ${this._value.category}`);
        }
        if (!Object.values(SignatureSeverity).includes(this._value.severity)) {
            throw new Error(`Invalid signature severity: ${this._value.severity}`);
        }
        if (!this._value.content || this._value.content.trim().length === 0) {
            throw new Error('Threat signature must have content');
        }
        if (this._value.content.length > ThreatSignature.MAX_CONTENT_LENGTH) {
            throw new Error(`Signature content exceeds maximum length of ${ThreatSignature.MAX_CONTENT_LENGTH} characters`);
        }
        if (!Object.values(confidence_level_enum_1.ConfidenceLevel).includes(this._value.confidence)) {
            throw new Error(`Invalid confidence level: ${this._value.confidence}`);
        }
        if (!this._value.version || this._value.version.trim().length === 0) {
            throw new Error('Threat signature must have a version');
        }
        if (!this._value.author || this._value.author.trim().length === 0) {
            throw new Error('Threat signature must have an author');
        }
        if (this._value.detectionLogic.falsePositiveRate < 0 || this._value.detectionLogic.falsePositiveRate > 1) {
            throw new Error('False positive rate must be between 0 and 1');
        }
        if (this._value.detectionLogic.truePositiveRate < 0 || this._value.detectionLogic.truePositiveRate > 1) {
            throw new Error('True positive rate must be between 0 and 1');
        }
        if (this._value.performance.processingTime < 0) {
            throw new Error('Processing time cannot be negative');
        }
        if (this._value.performance.memoryUsage < 0) {
            throw new Error('Memory usage cannot be negative');
        }
        if (this._value.performance.cpuUsage < 0 || this._value.performance.cpuUsage > 100) {
            throw new Error('CPU usage must be between 0 and 100');
        }
        if (this._value.usage.totalDetections < 0) {
            throw new Error('Total detections cannot be negative');
        }
        if (this._value.usage.truePositives < 0 || this._value.usage.truePositives > this._value.usage.totalDetections) {
            throw new Error('True positives must be between 0 and total detections');
        }
        if (this._value.usage.falsePositives < 0) {
            throw new Error('False positives cannot be negative');
        }
    }
    /**
     * Create a new threat signature
     */
    static create(id, name, type, category, severity, content, description, author, options) {
        const now = new Date();
        const props = {
            id: id.trim(),
            name: name.trim(),
            type,
            category,
            severity,
            content: content.trim(),
            description: description.trim(),
            confidence: options?.confidence || confidence_level_enum_1.ConfidenceLevel.MEDIUM,
            version: options?.version || '1.0',
            author: author.trim(),
            createdAt: now,
            modifiedAt: now,
            tags: options?.tags || [],
            mitreAttackTechniques: options?.mitreAttackTechniques || [],
            targetPlatforms: options?.targetPlatforms || [],
            detectionLogic: {
                method: options?.detectionMethod || 'pattern_match',
                parameters: options?.detectionParameters || {},
                falsePositiveRate: 0.05, // Default 5%
                truePositiveRate: 0.85, // Default 85%
            },
            performance: {
                processingTime: 0,
                memoryUsage: 0,
                cpuUsage: 0,
                throughput: 0,
            },
            validation: {
                isValidated: false,
            },
            usage: {
                totalDetections: 0,
                truePositives: 0,
                falsePositives: 0,
                detectionRate: 0,
            },
            metadata: {
                source: options?.source || 'custom',
                references: options?.references || [],
                relatedSignatures: [],
            },
        };
        return new ThreatSignature(props);
    }
    /**
     * Get signature ID
     */
    get id() {
        return this._value.id;
    }
    /**
     * Get signature name
     */
    get name() {
        return this._value.name;
    }
    /**
     * Get signature type
     */
    get type() {
        return this._value.type;
    }
    /**
     * Get signature category
     */
    get category() {
        return this._value.category;
    }
    /**
     * Get signature severity
     */
    get severity() {
        return this._value.severity;
    }
    /**
     * Get signature content
     */
    get content() {
        return this._value.content;
    }
    /**
     * Get signature description
     */
    get description() {
        return this._value.description;
    }
    /**
     * Get signature confidence
     */
    get confidence() {
        return this._value.confidence;
    }
    /**
     * Get signature version
     */
    get version() {
        return this._value.version;
    }
    /**
     * Get signature author
     */
    get author() {
        return this._value.author;
    }
    /**
     * Get creation date
     */
    get createdAt() {
        return this._value.createdAt;
    }
    /**
     * Get modification date
     */
    get modifiedAt() {
        return this._value.modifiedAt;
    }
    /**
     * Get signature tags
     */
    get tags() {
        return [...this._value.tags];
    }
    /**
     * Get MITRE ATT&CK techniques
     */
    get mitreAttackTechniques() {
        return [...this._value.mitreAttackTechniques];
    }
    /**
     * Get target platforms
     */
    get targetPlatforms() {
        return [...this._value.targetPlatforms];
    }
    /**
     * Get detection logic
     */
    get detectionLogic() {
        return { ...this._value.detectionLogic };
    }
    /**
     * Get performance metrics
     */
    get performance() {
        return { ...this._value.performance };
    }
    /**
     * Get validation status
     */
    get validation() {
        return { ...this._value.validation };
    }
    /**
     * Get usage statistics
     */
    get usage() {
        return { ...this._value.usage };
    }
    /**
     * Get metadata
     */
    get metadata() {
        return { ...this._value.metadata };
    }
    /**
     * Check if signature is validated
     */
    isValidated() {
        return this._value.validation.isValidated;
    }
    /**
     * Check if signature is production ready
     */
    isProductionReady() {
        const confidenceValues = {
            [confidence_level_enum_1.ConfidenceLevel.UNKNOWN]: 0,
            [confidence_level_enum_1.ConfidenceLevel.VERY_LOW]: 1,
            [confidence_level_enum_1.ConfidenceLevel.LOW]: 2,
            [confidence_level_enum_1.ConfidenceLevel.MEDIUM]: 3,
            [confidence_level_enum_1.ConfidenceLevel.HIGH]: 4,
            [confidence_level_enum_1.ConfidenceLevel.VERY_HIGH]: 5,
            [confidence_level_enum_1.ConfidenceLevel.CONFIRMED]: 6,
        };
        const minConfidenceValue = confidenceValues[ThreatSignature.MIN_CONFIDENCE_FOR_PRODUCTION];
        const currentConfidenceValue = confidenceValues[this._value.confidence];
        return this.isValidated() &&
            currentConfidenceValue >= minConfidenceValue &&
            this._value.detectionLogic.falsePositiveRate <= ThreatSignature.MAX_FALSE_POSITIVE_RATE;
    }
    /**
     * Check if signature is high performance
     */
    isHighPerformance() {
        return this._value.performance.processingTime < 100 && // Less than 100ms
            this._value.performance.cpuUsage < 10 && // Less than 10% CPU
            this._value.performance.throughput > 1000; // More than 1000 events/sec
    }
    /**
     * Check if signature is effective
     */
    isEffective() {
        if (this._value.usage.totalDetections === 0) {
            return false; // No data to determine effectiveness
        }
        const accuracy = this._value.usage.truePositives / this._value.usage.totalDetections;
        return accuracy >= 0.8; // 80% accuracy threshold
    }
    /**
     * Get signature effectiveness score (0-100)
     */
    getEffectivenessScore() {
        if (this._value.usage.totalDetections === 0) {
            return 0;
        }
        const accuracy = this._value.usage.truePositives / this._value.usage.totalDetections;
        const confidenceScore = this.getConfidenceScore() / 100; // Convert to 0-1 scale
        const performanceScore = this.getPerformanceScore() / 100; // Convert to 0-1 scale
        return Math.round((accuracy * 50) + (confidenceScore * 25) + (performanceScore * 25));
    }
    /**
     * Get confidence score (0-100)
     */
    getConfidenceScore() {
        const confidenceValues = {
            [confidence_level_enum_1.ConfidenceLevel.UNKNOWN]: 0,
            [confidence_level_enum_1.ConfidenceLevel.VERY_LOW]: 20,
            [confidence_level_enum_1.ConfidenceLevel.LOW]: 20,
            [confidence_level_enum_1.ConfidenceLevel.MEDIUM]: 50,
            [confidence_level_enum_1.ConfidenceLevel.HIGH]: 80,
            [confidence_level_enum_1.ConfidenceLevel.VERY_HIGH]: 95,
            [confidence_level_enum_1.ConfidenceLevel.CONFIRMED]: 100,
        };
        return confidenceValues[this._value.confidence] || 0;
    }
    /**
     * Get performance score (0-100)
     */
    getPerformanceScore() {
        // If no performance data is available, return a neutral score
        if (this._value.performance.processingTime === 0 &&
            this._value.performance.cpuUsage === 0 &&
            this._value.performance.throughput === 0) {
            return 50; // Neutral score when no performance data
        }
        let score = 100;
        // Penalize high processing time
        if (this._value.performance.processingTime > 1000) {
            score -= 30;
        }
        else if (this._value.performance.processingTime > 500) {
            score -= 15;
        }
        // Penalize high CPU usage
        if (this._value.performance.cpuUsage > 50) {
            score -= 30;
        }
        else if (this._value.performance.cpuUsage > 25) {
            score -= 15;
        }
        // Reward high throughput
        if (this._value.performance.throughput > 10000) {
            score += 10;
        }
        else if (this._value.performance.throughput > 5000) {
            score += 5;
        }
        return Math.max(0, Math.min(100, score));
    }
    /**
     * Get signature quality rating
     */
    getQualityRating() {
        const effectivenessScore = this.getEffectivenessScore();
        if (effectivenessScore >= 90)
            return 'excellent';
        if (effectivenessScore >= 70)
            return 'good';
        if (effectivenessScore >= 50)
            return 'fair';
        return 'poor';
    }
    /**
     * Get signature age in days
     */
    getAge() {
        return Math.floor((Date.now() - this._value.createdAt.getTime()) / (1000 * 60 * 60 * 24));
    }
    /**
     * Check if signature needs update
     */
    needsUpdate() {
        const age = this.getAge();
        const effectiveness = this.getEffectivenessScore();
        // Needs update if old and ineffective
        return (age > 365 && effectiveness < 50) || // Older than 1 year and poor effectiveness
            (age > 180 && effectiveness < 30) || // Older than 6 months and very poor effectiveness
            this._value.detectionLogic.falsePositiveRate > ThreatSignature.MAX_FALSE_POSITIVE_RATE;
    }
    /**
     * Get recommended actions
     */
    getRecommendedActions() {
        const actions = [];
        if (!this.isValidated()) {
            actions.push('Validate signature');
        }
        if (!this.isProductionReady()) {
            actions.push('Improve signature quality for production use');
        }
        if (!this.isHighPerformance()) {
            actions.push('Optimize signature performance');
        }
        if (!this.isEffective() && this._value.usage.totalDetections > 10) {
            actions.push('Review and improve signature accuracy');
        }
        if (this.needsUpdate()) {
            actions.push('Update signature content and logic');
        }
        if (this._value.detectionLogic.falsePositiveRate > 0.05) {
            actions.push('Reduce false positive rate');
        }
        return actions;
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            id: this._value.id,
            name: this._value.name,
            type: this._value.type,
            category: this._value.category,
            severity: this._value.severity,
            content: this._value.content,
            description: this._value.description,
            confidence: this._value.confidence,
            version: this._value.version,
            author: this._value.author,
            createdAt: this._value.createdAt.toISOString(),
            modifiedAt: this._value.modifiedAt.toISOString(),
            tags: this._value.tags,
            mitreAttackTechniques: this._value.mitreAttackTechniques,
            targetPlatforms: this._value.targetPlatforms,
            detectionLogic: this._value.detectionLogic,
            performance: this._value.performance,
            validation: this._value.validation,
            usage: this._value.usage,
            metadata: this._value.metadata,
            analysis: {
                isValidated: this.isValidated(),
                isProductionReady: this.isProductionReady(),
                isHighPerformance: this.isHighPerformance(),
                isEffective: this.isEffective(),
                effectivenessScore: this.getEffectivenessScore(),
                confidenceScore: this.getConfidenceScore(),
                performanceScore: this.getPerformanceScore(),
                qualityRating: this.getQualityRating(),
                age: this.getAge(),
                needsUpdate: this.needsUpdate(),
                recommendedActions: this.getRecommendedActions(),
            },
        };
    }
    /**
     * Create from JSON representation
     */
    static fromJSON(json) {
        const props = {
            id: json.id,
            name: json.name,
            type: json.type,
            category: json.category,
            severity: json.severity,
            content: json.content,
            description: json.description,
            confidence: json.confidence,
            version: json.version,
            author: json.author,
            createdAt: new Date(json.createdAt),
            modifiedAt: new Date(json.modifiedAt),
            tags: json.tags,
            mitreAttackTechniques: json.mitreAttackTechniques,
            targetPlatforms: json.targetPlatforms,
            detectionLogic: json.detectionLogic,
            performance: json.performance,
            validation: json.validation,
            usage: json.usage,
            metadata: json.metadata,
        };
        return new ThreatSignature(props);
    }
}
exports.ThreatSignature = ThreatSignature;
ThreatSignature.MAX_CONTENT_LENGTH = 100000; // 100KB
ThreatSignature.MIN_CONFIDENCE_FOR_PRODUCTION = confidence_level_enum_1.ConfidenceLevel.MEDIUM;
ThreatSignature.MAX_FALSE_POSITIVE_RATE = 0.1; // 10%
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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