{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\value-objects\\base-value-object.spec.ts", "mappings": ";;AAAA,6EAAwE;AAExE,yCAAyC;AACzC,MAAM,eAAgB,SAAQ,mCAAuB;IACnD,YAAY,KAAa;QACvB,KAAK,CAAC,KAAK,CAAC,CAAC;IACf,CAAC;IAES,QAAQ;QAChB,KAAK,CAAC,QAAQ,EAAE,CAAC;QACjB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;CACF;AAED,4CAA4C;AAC5C,MAAM,kBAAmB,SAAQ,mCAA8C;IAC7E,YAAY,KAAoC;QAC9C,KAAK,CAAC,KAAK,CAAC,CAAC;IACf,CAAC;IAES,QAAQ;QAChB,KAAK,CAAC,QAAQ,EAAE,CAAC;QACjB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC;YAC7C,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAES,qBAAqB;QAC7B,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAC7C,CAAC;CACF;AAED,kCAAkC;AAClC,MAAM,iBAAkB,SAAQ,mCAAyE;IACvG,YAAY,MAAuB,EAAE,OAA2B;QAC9D,KAAK,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC;IAC7B,CAAC;IAES,qBAAqB;QAC7B,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACnD,CAAC;CACF;AAED,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;IAC/B,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,EAAE,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC;YACvC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,eAAe,CAAC,IAAW,CAAC,CAAC,CAAC,OAAO,CAAC,6CAA6C,CAAC,CAAC;QACxG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,eAAe,CAAC,SAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,6CAA6C,CAAC,CAAC;QAC7G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,0CAA0C,CAAC,CAAC;QAC9F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qBAAqB,EAAE,GAAG,EAAE;YAC7B,MAAM,EAAE,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,EAAE,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC;YACvC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,GAAG,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC;YACxC,MAAM,GAAG,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC;YACxC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,GAAG,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;YACzC,MAAM,GAAG,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;YACzC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,EAAE,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC;YACvC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,GAAG,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC;YACxC,MAAM,GAAG,GAAG,IAAI,kBAAkB,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;YAC9D,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,GAAG,GAAG,IAAI,kBAAkB,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;YAC9D,MAAM,GAAG,GAAG,IAAI,kBAAkB,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;YAC9D,MAAM,GAAG,GAAG,IAAI,kBAAkB,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;YAE9D,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC;YAC5C,MAAM,QAAQ,GAAG,IAAI,kBAAkB,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;YACnE,MAAM,OAAO,GAAG,IAAI,iBAAiB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YAEzD,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC;YAC5C,MAAM,QAAQ,GAAG,IAAI,kBAAkB,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;YACnE,MAAM,OAAO,GAAG,IAAI,iBAAiB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YAEzD,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,WAAW,CAAC,CAAC;YACjD,MAAM,OAAO,GAAG,IAAI,iBAAiB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YAEzD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,EAAE,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC;YACvC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,EAAE,GAAG,IAAI,kBAAkB,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;YAC7D,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,EAAE,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC;YACvC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,EAAE,GAAG,IAAI,kBAAkB,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;YAC7D,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC;YAC3C,MAAM,OAAO,GAAG,IAAI,kBAAkB,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;YAClE,MAAM,MAAM,GAAG,IAAI,iBAAiB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAEtD,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC7B,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBACnB,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE;aACnC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;QACzB,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,GAAG,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC;YACxC,MAAM,GAAG,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC;YAExC,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,GAAG,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;YACzC,MAAM,GAAG,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;YAEzC,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,GAAG,GAAG,IAAI,kBAAkB,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;YAC9D,MAAM,GAAG,GAAG,IAAI,kBAAkB,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;YAC9D,MAAM,GAAG,GAAG,IAAI,kBAAkB,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;YAE9D,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC;YAClD,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;QACvB,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,EAAE,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC;YACvC,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;YAEzB,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC3B,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,EAAE,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC;YACvC,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;YAEzB,MAAM,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,EAAE,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC;YACvC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,EAAE,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC;YACvC,MAAM,CAAC,EAAE,CAAC,kBAAkB,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,6DAA6D;YAC7D,MAAM,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YACpD,EAAE,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,gBAAgB;YAElC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjC,MAAM,CAAC,EAAE,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,MAAM,gBAAiB,SAAQ,mCAAyB;YAC5C,qBAAqB;gBAC7B,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvB,CAAC;SACF;QAED,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,GAAG,GAAG,IAAI,gBAAgB,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;YAClD,MAAM,GAAG,GAAG,IAAI,gBAAgB,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;YAClD,MAAM,GAAG,GAAG,IAAI,gBAAgB,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;YAElD,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,GAAG,GAAG,IAAI,gBAAgB,CAAC,EAAE,CAAC,CAAC;YACrC,MAAM,GAAG,GAAG,IAAI,gBAAgB,CAAC,EAAE,CAAC,CAAC;YAErC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,GAAG,GAAG,IAAI,gBAAgB,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;YAC7C,MAAM,GAAG,GAAG,IAAI,gBAAgB,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;YAElD,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,sBAAuB,SAAQ,mCAAuB;gBAChD,QAAQ;oBAChB,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;wBACtD,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;oBACvD,CAAC;gBACH,CAAC;aACF;YAED,MAAM,EAAE,GAAG,IAAI,sBAAsB,CAAC,EAAE,CAAC,CAAC;YAC1C,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC1B,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,kBAAmB,SAAQ,mCAAuB;gBAC5C,QAAQ;oBAChB,KAAK,CAAC,QAAQ,EAAE,CAAC;oBACjB,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;wBAC1D,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;oBAClD,CAAC;gBACH,CAAC;aACF;YAED,MAAM,EAAE,GAAG,IAAI,kBAAkB,CAAC,EAAE,CAAC,CAAC;YACtC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC1B,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,kBAAmB,SAAQ,mCAAwB;gBAC7C,QAAQ;oBAChB,KAAK,CAAC,QAAQ,EAAE,CAAC;oBACjB,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;wBACrC,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;oBAC7C,CAAC;gBACH,CAAC;aACF;YAED,MAAM,EAAE,GAAG,IAAI,kBAAkB,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5B,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\value-objects\\base-value-object.spec.ts"], "sourcesContent": ["import { BaseValueObject } from '../../value-objects/base-value-object';\r\n\r\n// Test implementation of BaseValueObject\r\nclass TestValueObject extends BaseValueObject<string> {\r\n  constructor(value: string) {\r\n    super(value);\r\n  }\r\n\r\n  protected validate(): void {\r\n    super.validate();\r\n    if (this._value.length < 3) {\r\n      throw new Error('Value must be at least 3 characters long');\r\n    }\r\n  }\r\n}\r\n\r\n// Complex value object for testing equality\r\nclass ComplexValueObject extends BaseValueObject<{ name: string; age: number }> {\r\n  constructor(value: { name: string; age: number }) {\r\n    super(value);\r\n  }\r\n\r\n  protected validate(): void {\r\n    super.validate();\r\n    if (!this._value.name || this._value.age < 0) {\r\n      throw new Error('Invalid complex value');\r\n    }\r\n  }\r\n\r\n  protected getEqualityComponents(): any[] {\r\n    return [this._value.name, this._value.age];\r\n  }\r\n}\r\n\r\n// Nested value object for testing\r\nclass NestedValueObject extends BaseValueObject<{ simple: TestValueObject; complex: ComplexValueObject }> {\r\n  constructor(simple: TestValueObject, complex: ComplexValueObject) {\r\n    super({ simple, complex });\r\n  }\r\n\r\n  protected getEqualityComponents(): any[] {\r\n    return [this._value.simple, this._value.complex];\r\n  }\r\n}\r\n\r\ndescribe('BaseValueObject', () => {\r\n  describe('construction and validation', () => {\r\n    it('should create a valid value object', () => {\r\n      const vo = new TestValueObject('test');\r\n      expect(vo.value).toBe('test');\r\n    });\r\n\r\n    it('should throw error for null value', () => {\r\n      expect(() => new TestValueObject(null as any)).toThrow('TestValueObject cannot be null or undefined');\r\n    });\r\n\r\n    it('should throw error for undefined value', () => {\r\n      expect(() => new TestValueObject(undefined as any)).toThrow('TestValueObject cannot be null or undefined');\r\n    });\r\n\r\n    it('should throw error for invalid value', () => {\r\n      expect(() => new TestValueObject('ab')).toThrow('Value must be at least 3 characters long');\r\n    });\r\n\r\n    it('should be immutable', () => {\r\n      const vo = new TestValueObject('test');\r\n      expect(Object.isFrozen(vo)).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('equality comparison', () => {\r\n    it('should be equal to itself', () => {\r\n      const vo = new TestValueObject('test');\r\n      expect(vo.equals(vo)).toBe(true);\r\n    });\r\n\r\n    it('should be equal to another value object with same value', () => {\r\n      const vo1 = new TestValueObject('test');\r\n      const vo2 = new TestValueObject('test');\r\n      expect(vo1.equals(vo2)).toBe(true);\r\n    });\r\n\r\n    it('should not be equal to value object with different value', () => {\r\n      const vo1 = new TestValueObject('test1');\r\n      const vo2 = new TestValueObject('test2');\r\n      expect(vo1.equals(vo2)).toBe(false);\r\n    });\r\n\r\n    it('should not be equal to null or undefined', () => {\r\n      const vo = new TestValueObject('test');\r\n      expect(vo.equals(null)).toBe(false);\r\n      expect(vo.equals(undefined)).toBe(false);\r\n    });\r\n\r\n    it('should not be equal to different type', () => {\r\n      const vo1 = new TestValueObject('test');\r\n      const vo2 = new ComplexValueObject({ name: 'test', age: 25 });\r\n      expect(vo1.equals(vo2 as any)).toBe(false);\r\n    });\r\n\r\n    it('should handle complex object equality', () => {\r\n      const vo1 = new ComplexValueObject({ name: 'John', age: 25 });\r\n      const vo2 = new ComplexValueObject({ name: 'John', age: 25 });\r\n      const vo3 = new ComplexValueObject({ name: 'Jane', age: 25 });\r\n      \r\n      expect(vo1.equals(vo2)).toBe(true);\r\n      expect(vo1.equals(vo3)).toBe(false);\r\n    });\r\n\r\n    it('should handle nested value object equality', () => {\r\n      const simple1 = new TestValueObject('test');\r\n      const complex1 = new ComplexValueObject({ name: 'John', age: 25 });\r\n      const nested1 = new NestedValueObject(simple1, complex1);\r\n\r\n      const simple2 = new TestValueObject('test');\r\n      const complex2 = new ComplexValueObject({ name: 'John', age: 25 });\r\n      const nested2 = new NestedValueObject(simple2, complex2);\r\n\r\n      const simple3 = new TestValueObject('different');\r\n      const nested3 = new NestedValueObject(simple3, complex2);\r\n\r\n      expect(nested1.equals(nested2)).toBe(true);\r\n      expect(nested1.equals(nested3)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('string representation', () => {\r\n    it('should convert simple value to string', () => {\r\n      const vo = new TestValueObject('test');\r\n      expect(vo.toString()).toBe('test');\r\n    });\r\n\r\n    it('should convert complex value to JSON string', () => {\r\n      const vo = new ComplexValueObject({ name: 'John', age: 25 });\r\n      expect(vo.toString()).toBe('{\"name\":\"John\",\"age\":25}');\r\n    });\r\n  });\r\n\r\n  describe('JSON serialization', () => {\r\n    it('should serialize simple value', () => {\r\n      const vo = new TestValueObject('test');\r\n      expect(vo.toJSON()).toBe('test');\r\n    });\r\n\r\n    it('should serialize complex value', () => {\r\n      const vo = new ComplexValueObject({ name: 'John', age: 25 });\r\n      expect(vo.toJSON()).toEqual({ name: 'John', age: 25 });\r\n    });\r\n\r\n    it('should serialize nested value objects', () => {\r\n      const simple = new TestValueObject('test');\r\n      const complex = new ComplexValueObject({ name: 'John', age: 25 });\r\n      const nested = new NestedValueObject(simple, complex);\r\n      \r\n      const json = nested.toJSON();\r\n      expect(json).toEqual({\r\n        simple: 'test',\r\n        complex: { name: 'John', age: 25 }\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('hash code', () => {\r\n    it('should generate consistent hash codes', () => {\r\n      const vo1 = new TestValueObject('test');\r\n      const vo2 = new TestValueObject('test');\r\n      \r\n      expect(vo1.getHashCode()).toBe(vo2.getHashCode());\r\n    });\r\n\r\n    it('should generate different hash codes for different values', () => {\r\n      const vo1 = new TestValueObject('test1');\r\n      const vo2 = new TestValueObject('test2');\r\n      \r\n      expect(vo1.getHashCode()).not.toBe(vo2.getHashCode());\r\n    });\r\n\r\n    it('should handle complex objects', () => {\r\n      const vo1 = new ComplexValueObject({ name: 'John', age: 25 });\r\n      const vo2 = new ComplexValueObject({ name: 'John', age: 25 });\r\n      const vo3 = new ComplexValueObject({ name: 'Jane', age: 25 });\r\n      \r\n      expect(vo1.getHashCode()).toBe(vo2.getHashCode());\r\n      expect(vo1.getHashCode()).not.toBe(vo3.getHashCode());\r\n    });\r\n  });\r\n\r\n  describe('cloning', () => {\r\n    it('should create a clone with same value', () => {\r\n      const vo = new TestValueObject('test');\r\n      const clone = vo.clone();\r\n      \r\n      expect(clone).not.toBe(vo);\r\n      expect(clone.equals(vo)).toBe(true);\r\n      expect(clone.value).toBe(vo.value);\r\n    });\r\n\r\n    it('should create independent clones', () => {\r\n      const vo = new TestValueObject('test');\r\n      const clone = vo.clone();\r\n      \r\n      expect(clone).toBeInstanceOf(TestValueObject);\r\n      expect(clone.equals(vo)).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('validation utilities', () => {\r\n    it('should return true for valid value object', () => {\r\n      const vo = new TestValueObject('test');\r\n      expect(vo.isValid()).toBe(true);\r\n    });\r\n\r\n    it('should return null for valid value object validation error', () => {\r\n      const vo = new TestValueObject('test');\r\n      expect(vo.getValidationError()).toBeNull();\r\n    });\r\n\r\n    it('should handle validation errors gracefully', () => {\r\n      // Create a value object that bypasses constructor validation\r\n      const vo = Object.create(TestValueObject.prototype);\r\n      vo._value = 'ab'; // Invalid value\r\n      \r\n      expect(vo.isValid()).toBe(false);\r\n      expect(vo.getValidationError()).toBe('Value must be at least 3 characters long');\r\n    });\r\n  });\r\n\r\n  describe('array equality', () => {\r\n    class ArrayValueObject extends BaseValueObject<string[]> {\r\n      protected getEqualityComponents(): any[] {\r\n        return [this._value];\r\n      }\r\n    }\r\n\r\n    it('should handle array equality correctly', () => {\r\n      const vo1 = new ArrayValueObject(['a', 'b', 'c']);\r\n      const vo2 = new ArrayValueObject(['a', 'b', 'c']);\r\n      const vo3 = new ArrayValueObject(['a', 'b', 'd']);\r\n      \r\n      expect(vo1.equals(vo2)).toBe(true);\r\n      expect(vo1.equals(vo3)).toBe(false);\r\n    });\r\n\r\n    it('should handle empty arrays', () => {\r\n      const vo1 = new ArrayValueObject([]);\r\n      const vo2 = new ArrayValueObject([]);\r\n      \r\n      expect(vo1.equals(vo2)).toBe(true);\r\n    });\r\n\r\n    it('should handle arrays of different lengths', () => {\r\n      const vo1 = new ArrayValueObject(['a', 'b']);\r\n      const vo2 = new ArrayValueObject(['a', 'b', 'c']);\r\n      \r\n      expect(vo1.equals(vo2)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('edge cases', () => {\r\n    it('should handle empty string values', () => {\r\n      class EmptyStringValueObject extends BaseValueObject<string> {\r\n        protected validate(): void {\r\n          if (this._value === null || this._value === undefined) {\r\n            throw new Error('Value cannot be null or undefined');\r\n          }\r\n        }\r\n      }\r\n\r\n      const vo = new EmptyStringValueObject('');\r\n      expect(vo.value).toBe('');\r\n      expect(vo.toString()).toBe('');\r\n    });\r\n\r\n    it('should handle numeric values', () => {\r\n      class NumericValueObject extends BaseValueObject<number> {\r\n        protected validate(): void {\r\n          super.validate();\r\n          if (typeof this._value !== 'number' || isNaN(this._value)) {\r\n            throw new Error('Value must be a valid number');\r\n          }\r\n        }\r\n      }\r\n\r\n      const vo = new NumericValueObject(42);\r\n      expect(vo.value).toBe(42);\r\n      expect(vo.toString()).toBe('42');\r\n    });\r\n\r\n    it('should handle boolean values', () => {\r\n      class BooleanValueObject extends BaseValueObject<boolean> {\r\n        protected validate(): void {\r\n          super.validate();\r\n          if (typeof this._value !== 'boolean') {\r\n            throw new Error('Value must be a boolean');\r\n          }\r\n        }\r\n      }\r\n\r\n      const vo = new BooleanValueObject(true);\r\n      expect(vo.value).toBe(true);\r\n      expect(vo.toString()).toBe('true');\r\n    });\r\n  });\r\n});"], "version": 3}