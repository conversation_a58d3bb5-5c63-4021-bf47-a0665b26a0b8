{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\database\\connection.factory.ts", "mappings": ";;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAC/C,qCAAwD;AAExD,sDAAsD;AACtD,MAAM,YAAY,GAAG,CAAC,KAAc,EAAE,EAAE,CAAC,CAAC;IACxC,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;IAC/D,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;CACxD,CAAC,CAAC;AAEH;;;GAGG;AAEI,IAAM,iBAAiB,yBAAvB,MAAM,iBAAiB;IAI5B,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAHxC,WAAM,GAAG,IAAI,eAAM,CAAC,mBAAiB,CAAC,IAAI,CAAC,CAAC;QAC5C,gBAAW,GAAG,IAAI,GAAG,EAAsB,CAAC;IAED,CAAC;IAE7D;;;;;OAKG;IACH,KAAK,CAAC,gBAAgB,CACpB,OAAe,SAAS,EACxB,OAAoC;QAEpC,IAAI,CAAC;YACH,qCAAqC;YACrC,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC/B,MAAM,kBAAkB,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACtD,IAAI,kBAAkB,EAAE,aAAa,EAAE,CAAC;oBACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,IAAI,EAAE,CAAC,CAAC;oBAC1D,OAAO,kBAAkB,CAAC;gBAC5B,CAAC;YACH,CAAC;YAED,4BAA4B;YAC5B,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAEzD,8BAA8B;YAC9B,MAAM,iBAAiB,GAAsB;gBAC3C,GAAG,aAAa;gBAChB,GAAG,OAAO;gBACV,IAAI;aACL,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,IAAI,EAAE,EAAE;gBAC7D,IAAI,EAAG,iBAAyB,CAAC,IAAI;gBACrC,QAAQ,EAAE,iBAAiB,CAAC,QAAQ;gBACpC,IAAI,EAAE,iBAAiB,CAAC,IAAI;aAC7B,CAAC,CAAC;YAEH,mCAAmC;YACnC,MAAM,UAAU,GAAG,IAAI,oBAAU,CAAC,iBAAiB,CAAC,CAAC;YACrD,MAAM,UAAU,CAAC,UAAU,EAAE,CAAC;YAE9B,6BAA6B;YAC7B,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;YAEvC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,IAAI,wBAAwB,CAAC,CAAC;YACtE,OAAO,UAAU,CAAC;QAEpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yCAAyC,IAAI,GAAG,EAAE;gBAClE,KAAK,EAAE,SAAS,CAAC,OAAO;gBACxB,KAAK,EAAE,SAAS,CAAC,KAAK;aACvB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,aAAa,CAAC,OAAe,SAAS;QACpC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,eAAe,CAAC,OAAe,SAAS;QAC5C,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAE9C,IAAI,UAAU,IAAI,UAAU,CAAC,aAAa,EAAE,CAAC;gBAC3C,MAAM,UAAU,CAAC,OAAO,EAAE,CAAC;gBAC3B,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAC9B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,IAAI,uBAAuB,CAAC,CAAC;YACvE,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,IAAI,+BAA+B,CAAC,CAAC;YACvE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,IAAI,GAAG,EAAE;gBACjE,KAAK,EAAE,SAAS,CAAC,OAAO;aACzB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,mBAAmB;QACvB,MAAM,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;QAE5D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,eAAe,CAAC,MAAM,0BAA0B,CAAC,CAAC;QAE7E,MAAM,aAAa,GAAG,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAC/C,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACvC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;QACnE,CAAC,CAAC,CACH,CAAC;QAEF,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QACjC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;IACrD,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,wBAAwB;QAC5B,MAAM,cAAc,GAAG;YACrB,KAAK,EAAE;gBACL,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,gBAAgB,CAAC;gBAC3C,qCAAqC;gBACrC,GAAG,EAAE,EAAE,EAAE,mCAAmC;gBAC5C,iBAAiB,EAAE,KAAK,EAAE,oBAAoB;gBAC9C,aAAa,EAAE,KAAK;aACrB;YACD,mDAAmD;YACnD,aAAa,EAAE,KAAK;YACpB,WAAW,EAAE,KAAK;YAClB,OAAO,EAAE,CAAC,OAAO,CAAQ,EAAE,sCAAsC;SAClE,CAAC;QAEF,OAAO,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;IAC3D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,sBAAsB;QAC1B,MAAM,YAAY,GAAG;YACnB,KAAK,EAAE;gBACL,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,gBAAgB,CAAC;gBAC3C,sCAAsC;gBACtC,GAAG,EAAE,EAAE,EAAE,2BAA2B;gBACpC,GAAG,EAAE,CAAC;gBACN,IAAI,EAAE,KAAK,EAAE,sBAAsB;gBACnC,OAAO,EAAE,KAAK,EAAE,yBAAyB;aAC1C;YACD,2BAA2B;YAC3B,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM,CAAQ;YACjC,KAAK,EAAE,KAAK,EAAE,8BAA8B;SAC7C,CAAC;QAEF,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;IACvD,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,cAAc,CAClB,OAAe,SAAS,EACxB,aAAqB,CAAC,EACtB,aAAqB,IAAI;QAEzB,IAAI,SAAgB,CAAC;QAErB,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,UAAU,EAAE,OAAO,EAAE,EAAE,CAAC;YACvD,IAAI,CAAC;gBACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,IAAI,cAAc,OAAO,IAAI,UAAU,GAAG,CAAC,CAAC;gBAE9F,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;gBAC5C,IAAI,CAAC,UAAU,EAAE,CAAC;oBAChB,MAAM,IAAI,KAAK,CAAC,eAAe,IAAI,aAAa,CAAC,CAAC;gBACpD,CAAC;gBAED,2BAA2B;gBAC3B,MAAM,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;gBAEnC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,IAAI,mBAAmB,CAAC,CAAC;gBACjE,OAAO,IAAI,CAAC;YAEd,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;gBACtE,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;gBACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4CAA4C,OAAO,IAAI,UAAU,GAAG,EAAE;oBACrF,KAAK,EAAE,SAAS,CAAC,OAAO;oBACxB,IAAI;iBACL,CAAC,CAAC;gBAEH,IAAI,OAAO,GAAG,UAAU,EAAE,CAAC;oBACzB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,UAAU,OAAO,CAAC,CAAC;oBACpD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC;gBAChE,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yCAAyC,UAAU,WAAW,EAAE;YAChF,IAAI;YACJ,KAAK,EAAE,SAAS,CAAC,OAAO;SACzB,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,oBAAoB;QACxB,MAAM,MAAM,GAAwB,EAAE,CAAC;QAEvC,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC;QACvD,KAAK,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC,IAAI,OAAO,EAAE,CAAC;YACzC,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBACxD,MAAM,CAAC,IAAI,CAAC,GAAG;oBACb,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW;oBAC3C,aAAa,EAAE,UAAU,CAAC,aAAa;oBACvC,QAAQ,EAAE,UAAU,CAAC,OAAO,CAAC,QAAQ;oBACrC,IAAI,EAAG,UAAU,CAAC,OAAe,CAAC,IAAI;oBACtC,IAAI,EAAE,UAAU,CAAC,OAAO,CAAC,IAAI;oBAC7B,WAAW,EAAE,UAAU,CAAC,eAAe,CAAC,MAAM;iBAC/C,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;gBACtC,MAAM,CAAC,IAAI,CAAC,GAAG;oBACb,MAAM,EAAE,OAAO;oBACf,KAAK,EAAE,SAAS,CAAC,OAAO;oBACxB,aAAa,EAAE,UAAU,CAAC,aAAa;iBACxC,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;OAGG;IACH,kBAAkB;QAChB,MAAM,KAAK,GAAG;YACZ,gBAAgB,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI;YACvC,iBAAiB,EAAE,CAAC;YACpB,WAAW,EAAE,EAAyB;SACvC,CAAC;QAEF,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC;QACvD,KAAK,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC,IAAI,OAAO,EAAE,CAAC;YACzC,MAAM,QAAQ,GAAG,UAAU,CAAC,aAAa,CAAC;YAC1C,IAAI,QAAQ,EAAE,CAAC;gBACb,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAC5B,CAAC;YAED,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG;gBACxB,QAAQ;gBACR,QAAQ,EAAE,UAAU,CAAC,OAAO,CAAC,QAAQ;gBACrC,IAAI,EAAE,UAAU,CAAC,OAAO,CAAC,IAAI;gBAC7B,WAAW,EAAE,UAAU,CAAC,eAAe,CAAC,MAAM;gBAC9C,cAAc,EAAE,UAAU,CAAC,UAAU,CAAC,MAAM;aAC7C,CAAC;QACJ,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;CACF,CAAA;AAhRY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;yDAKiC,sBAAa,oBAAb,sBAAa;GAJ9C,iBAAiB,CAgR7B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\database\\connection.factory.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { DataSource, DataSourceOptions } from 'typeorm';\r\n\r\n// Helper function to safely extract error information\r\nconst getErrorInfo = (error: unknown) => ({\r\n  message: error instanceof Error ? error.message : String(error),\r\n  stack: error instanceof Error ? error.stack : undefined,\r\n});\r\n\r\n/**\r\n * Connection factory for creating and managing database connections\r\n * Supports multiple database types and connection configurations\r\n */\r\n@Injectable()\r\nexport class ConnectionFactory {\r\n  private readonly logger = new Logger(ConnectionFactory.name);\r\n  private readonly connections = new Map<string, DataSource>();\r\n\r\n  constructor(private readonly configService: ConfigService) {}\r\n\r\n  /**\r\n   * Create a new database connection\r\n   * @param name Connection name\r\n   * @param options Connection options (optional, uses default config if not provided)\r\n   * @returns Promise<DataSource> Database connection\r\n   */\r\n  async createConnection(\r\n    name: string = 'default',\r\n    options?: Partial<DataSourceOptions>\r\n  ): Promise<DataSource> {\r\n    try {\r\n      // Check if connection already exists\r\n      if (this.connections.has(name)) {\r\n        const existingConnection = this.connections.get(name);\r\n        if (existingConnection?.isInitialized) {\r\n          this.logger.debug(`Reusing existing connection: ${name}`);\r\n          return existingConnection;\r\n        }\r\n      }\r\n\r\n      // Get default configuration\r\n      const defaultConfig = this.configService.get('database');\r\n      \r\n      // Merge with provided options\r\n      const connectionOptions: DataSourceOptions = {\r\n        ...defaultConfig,\r\n        ...options,\r\n        name,\r\n      };\r\n\r\n      this.logger.debug(`Creating new database connection: ${name}`, {\r\n        host: (connectionOptions as any).host,\r\n        database: connectionOptions.database,\r\n        type: connectionOptions.type,\r\n      });\r\n\r\n      // Create and initialize connection\r\n      const connection = new DataSource(connectionOptions);\r\n      await connection.initialize();\r\n\r\n      // Store connection for reuse\r\n      this.connections.set(name, connection);\r\n\r\n      this.logger.log(`Database connection '${name}' created successfully`);\r\n      return connection;\r\n\r\n    } catch (error) {\r\n      const errorInfo = getErrorInfo(error);\r\n      this.logger.error(`Failed to create database connection '${name}'`, {\r\n        error: errorInfo.message,\r\n        stack: errorInfo.stack,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get an existing connection by name\r\n   * @param name Connection name\r\n   * @returns DataSource | undefined\r\n   */\r\n  getConnection(name: string = 'default'): DataSource | undefined {\r\n    return this.connections.get(name);\r\n  }\r\n\r\n  /**\r\n   * Close a specific connection\r\n   * @param name Connection name\r\n   * @returns Promise<void>\r\n   */\r\n  async closeConnection(name: string = 'default'): Promise<void> {\r\n    try {\r\n      const connection = this.connections.get(name);\r\n      \r\n      if (connection && connection.isInitialized) {\r\n        await connection.destroy();\r\n        this.connections.delete(name);\r\n        this.logger.log(`Database connection '${name}' closed successfully`);\r\n      } else {\r\n        this.logger.warn(`Connection '${name}' not found or already closed`);\r\n      }\r\n    } catch (error) {\r\n      const errorInfo = getErrorInfo(error);\r\n      this.logger.error(`Failed to close database connection '${name}'`, {\r\n        error: errorInfo.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Close all connections\r\n   * @returns Promise<void>\r\n   */\r\n  async closeAllConnections(): Promise<void> {\r\n    const connectionNames = Array.from(this.connections.keys());\r\n    \r\n    this.logger.log(`Closing ${connectionNames.length} database connections...`);\r\n\r\n    const closePromises = connectionNames.map(name => \r\n      this.closeConnection(name).catch(error => {\r\n        this.logger.error(`Failed to close connection '${name}'`, error);\r\n      })\r\n    );\r\n\r\n    await Promise.all(closePromises);\r\n    this.logger.log('All database connections closed');\r\n  }\r\n\r\n  /**\r\n   * Create a read-only connection for reporting/analytics\r\n   * @returns Promise<DataSource>\r\n   */\r\n  async createReadOnlyConnection(): Promise<DataSource> {\r\n    const readOnlyConfig = {\r\n      extra: {\r\n        ...this.configService.get('database.extra'),\r\n        // Configure for read-only operations\r\n        max: 20, // Smaller pool for read operations\r\n        statement_timeout: 30000, // 30 second timeout\r\n        query_timeout: 30000,\r\n      },\r\n      // Disable migrations and schema sync for read-only\r\n      migrationsRun: false,\r\n      synchronize: false,\r\n      logging: ['error'] as any, // Minimal logging for read operations\r\n    };\r\n\r\n    return this.createConnection('readonly', readOnlyConfig);\r\n  }\r\n\r\n  /**\r\n   * Create a connection for background jobs/workers\r\n   * @returns Promise<DataSource>\r\n   */\r\n  async createWorkerConnection(): Promise<DataSource> {\r\n    const workerConfig = {\r\n      extra: {\r\n        ...this.configService.get('database.extra'),\r\n        // Configure for background processing\r\n        max: 10, // Smaller pool for workers\r\n        min: 2,\r\n        idle: 30000, // Longer idle timeout\r\n        acquire: 60000, // Longer acquire timeout\r\n      },\r\n      // Worker-specific settings\r\n      logging: ['error', 'warn'] as any,\r\n      cache: false, // Disable caching for workers\r\n    };\r\n\r\n    return this.createConnection('worker', workerConfig);\r\n  }\r\n\r\n  /**\r\n   * Test connection with retry logic\r\n   * @param name Connection name\r\n   * @param maxRetries Maximum number of retry attempts\r\n   * @param retryDelay Delay between retries in milliseconds\r\n   * @returns Promise<boolean>\r\n   */\r\n  async testConnection(\r\n    name: string = 'default',\r\n    maxRetries: number = 3,\r\n    retryDelay: number = 1000\r\n  ): Promise<boolean> {\r\n    let lastError: Error;\r\n\r\n    for (let attempt = 1; attempt <= maxRetries; attempt++) {\r\n      try {\r\n        this.logger.debug(`Testing database connection '${name}' (attempt ${attempt}/${maxRetries})`);\r\n        \r\n        const connection = this.getConnection(name);\r\n        if (!connection) {\r\n          throw new Error(`Connection '${name}' not found`);\r\n        }\r\n\r\n        // Test with a simple query\r\n        await connection.query('SELECT 1');\r\n        \r\n        this.logger.log(`Database connection '${name}' test successful`);\r\n        return true;\r\n\r\n      } catch (error) {\r\n        lastError = error instanceof Error ? error : new Error(String(error));\r\n        const errorInfo = getErrorInfo(error);\r\n        this.logger.warn(`Database connection test failed (attempt ${attempt}/${maxRetries})`, {\r\n          error: errorInfo.message,\r\n          name,\r\n        });\r\n\r\n        if (attempt < maxRetries) {\r\n          this.logger.debug(`Retrying in ${retryDelay}ms...`);\r\n          await new Promise(resolve => setTimeout(resolve, retryDelay));\r\n        }\r\n      }\r\n    }\r\n\r\n    this.logger.error(`Database connection test failed after ${maxRetries} attempts`, {\r\n      name,\r\n      error: lastError.message,\r\n    });\r\n\r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Get connection health status for all connections\r\n   * @returns Promise<Record<string, any>>\r\n   */\r\n  async getConnectionsHealth(): Promise<Record<string, any>> {\r\n    const health: Record<string, any> = {};\r\n\r\n    const entries = Array.from(this.connections.entries());\r\n    for (const [name, connection] of entries) {\r\n      try {\r\n        const isHealthy = await this.testConnection(name, 1, 0);\r\n        health[name] = {\r\n          status: isHealthy ? 'healthy' : 'unhealthy',\r\n          isInitialized: connection.isInitialized,\r\n          database: connection.options.database,\r\n          host: (connection.options as any).host,\r\n          type: connection.options.type,\r\n          entityCount: connection.entityMetadatas.length,\r\n        };\r\n      } catch (error) {\r\n        const errorInfo = getErrorInfo(error);\r\n        health[name] = {\r\n          status: 'error',\r\n          error: errorInfo.message,\r\n          isInitialized: connection.isInitialized,\r\n        };\r\n      }\r\n    }\r\n\r\n    return health;\r\n  }\r\n\r\n  /**\r\n   * Get connection statistics\r\n   * @returns Record<string, any>\r\n   */\r\n  getConnectionStats(): Record<string, any> {\r\n    const stats = {\r\n      totalConnections: this.connections.size,\r\n      activeConnections: 0,\r\n      connections: {} as Record<string, any>,\r\n    };\r\n\r\n    const entries = Array.from(this.connections.entries());\r\n    for (const [name, connection] of entries) {\r\n      const isActive = connection.isInitialized;\r\n      if (isActive) {\r\n        stats.activeConnections++;\r\n      }\r\n\r\n      stats.connections[name] = {\r\n        isActive,\r\n        database: connection.options.database,\r\n        type: connection.options.type,\r\n        entityCount: connection.entityMetadatas.length,\r\n        migrationCount: connection.migrations.length,\r\n      };\r\n    }\r\n\r\n    return stats;\r\n  }\r\n}\r\n"], "version": 3}