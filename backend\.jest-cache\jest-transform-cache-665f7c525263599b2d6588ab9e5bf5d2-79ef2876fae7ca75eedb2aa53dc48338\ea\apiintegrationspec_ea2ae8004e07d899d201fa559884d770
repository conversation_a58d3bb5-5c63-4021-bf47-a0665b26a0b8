3aa38f30afc478241aa79ec4a2e817ec
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const request = __importStar(require("supertest"));
const base_api_controller_1 = require("../common/base-api.controller");
const api_version_guard_1 = require("../versioning/api-version.guard");
const api_versioning_service_1 = require("../versioning/api-versioning.service");
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const api_version_decorator_1 = require("../versioning/api-version.decorator");
// Test controller extending BaseApiController
let TestController = class TestController extends base_api_controller_1.BaseApiController {
    constructor() {
        super('TestController');
    }
    getSuccess() {
        return this.createSuccessResponse({ message: 'Test successful' });
    }
    getError() {
        return this.createErrorResponse('Test error', 'TEST_ERROR');
    }
    getPaginated(query) {
        const data = [{ id: 1, name: 'Item 1' }, { id: 2, name: 'Item 2' }];
        const pagination = {
            page: 1,
            limit: 10,
            total: 2,
            totalPages: 1,
            hasNext: false,
            hasPrev: false,
            offset: 0,
        };
        return this.createPaginatedResponse(data, pagination);
    }
    postValidate(body) {
        this.validateRequiredParams(body, ['name', 'email']);
        return this.createSuccessResponse({ validated: true });
    }
    getSanitize(input) {
        const sanitized = this.sanitizeInput(input);
        return this.createSuccessResponse({ original: input, sanitized });
    }
};
__decorate([
    (0, common_1.Get)('success'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], TestController.prototype, "getSuccess", null);
__decorate([
    (0, common_1.Get)('error'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], TestController.prototype, "getError", null);
__decorate([
    (0, common_1.Get)('paginated'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], TestController.prototype, "getPaginated", null);
__decorate([
    (0, common_1.Post)('validate'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], TestController.prototype, "postValidate", null);
__decorate([
    (0, common_1.Get)('sanitize/:input'),
    __param(0, (0, common_1.Param)('input')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], TestController.prototype, "getSanitize", null);
TestController = __decorate([
    (0, common_1.Controller)('test'),
    (0, api_version_decorator_1.ApiVersion)('1.0'),
    __metadata("design:paramtypes", [])
], TestController);
// Test controller for versioning
let VersionedController = class VersionedController {
    getV1() {
        return { version: '1.0', message: 'Version 1.0 endpoint' };
    }
    getV2() {
        return { version: '2.0', message: 'Version 2.0 endpoint' };
    }
    getDeprecated() {
        return { version: '1.0', message: 'Deprecated endpoint' };
    }
};
__decorate([
    (0, common_1.Get)('v1'),
    (0, api_version_decorator_1.ApiVersion)('1.0'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], VersionedController.prototype, "getV1", null);
__decorate([
    (0, common_1.Get)('v2'),
    (0, api_version_decorator_1.ApiVersion)('2.0'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], VersionedController.prototype, "getV2", null);
__decorate([
    (0, common_1.Get)('deprecated'),
    (0, api_version_decorator_1.ApiVersion)('1.0', { deprecated: true, replacement: 'v2' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], VersionedController.prototype, "getDeprecated", null);
VersionedController = __decorate([
    (0, common_1.Controller)('versioned'),
    (0, common_1.UseGuards)(api_version_guard_1.ApiVersionGuard)
], VersionedController);
describe('API Integration Tests', () => {
    let app;
    let versioningService;
    beforeAll(async () => {
        // Mock ApiVersioningService
        versioningService = {
            extractVersion: jest.fn(),
            validateVersion: jest.fn(),
            getVersionInfo: jest.fn(),
            getSupportedVersions: jest.fn(),
            checkCompatibility: jest.fn(),
            logVersionUsage: jest.fn(),
            getDeprecationWarnings: jest.fn(),
        };
        const moduleFixture = await testing_1.Test.createTestingModule({
            imports: [
                config_1.ConfigModule.forRoot({
                    isGlobal: true,
                }),
            ],
            controllers: [TestController, VersionedController],
            providers: [
                {
                    provide: api_versioning_service_1.ApiVersioningService,
                    useValue: versioningService,
                },
                core_1.Reflector,
                api_version_guard_1.ApiVersionGuard,
            ],
        }).compile();
        app = moduleFixture.createNestApplication();
        await app.init();
    });
    afterAll(async () => {
        await app.close();
    });
    beforeEach(() => {
        jest.clearAllMocks();
    });
    describe('BaseApiController', () => {
        describe('Success Response', () => {
            it('should return standardized success response', async () => {
                const response = await request(app.getHttpServer())
                    .get('/test/success')
                    .expect(200);
                expect(response.body).toEqual({
                    success: true,
                    data: { message: 'Test successful' },
                    message: 'Operation completed successfully',
                    timestamp: expect.any(String),
                    metadata: {},
                });
            });
        });
        describe('Error Response', () => {
            it('should return standardized error response', async () => {
                const response = await request(app.getHttpServer())
                    .get('/test/error')
                    .expect(200);
                expect(response.body).toEqual({
                    success: false,
                    error: {
                        code: 'TEST_ERROR',
                        message: 'Test error',
                        details: {},
                        timestamp: expect.any(String),
                    },
                });
            });
        });
        describe('Paginated Response', () => {
            it('should return standardized paginated response', async () => {
                const response = await request(app.getHttpServer())
                    .get('/test/paginated')
                    .expect(200);
                expect(response.body).toEqual({
                    success: true,
                    data: [
                        { id: 1, name: 'Item 1' },
                        { id: 2, name: 'Item 2' },
                    ],
                    message: 'Data retrieved successfully',
                    timestamp: expect.any(String),
                    pagination: {
                        page: 1,
                        limit: 10,
                        total: 2,
                        totalPages: 1,
                        hasNext: false,
                        hasPrev: false,
                        offset: 0,
                    },
                    metadata: {
                        totalItems: 2,
                        itemsPerPage: 10,
                        currentPage: 1,
                        totalPages: 1,
                    },
                });
            });
        });
        describe('Validation', () => {
            it('should validate required parameters successfully', async () => {
                const response = await request(app.getHttpServer())
                    .post('/test/validate')
                    .send({ name: 'John', email: '<EMAIL>' })
                    .expect(201);
                expect(response.body).toEqual({
                    success: true,
                    data: { validated: true },
                    message: 'Operation completed successfully',
                    timestamp: expect.any(String),
                    metadata: {},
                });
            });
            it('should fail validation for missing required parameters', async () => {
                await request(app.getHttpServer())
                    .post('/test/validate')
                    .send({ name: 'John' }) // Missing email
                    .expect(500); // This would be handled by exception filter in real app
            });
        });
        describe('Input Sanitization', () => {
            it('should sanitize XSS attempts', async () => {
                const maliciousInput = '<script>alert("xss")</script>Hello';
                const response = await request(app.getHttpServer())
                    .get(`/test/sanitize/${encodeURIComponent(maliciousInput)}`)
                    .expect(200);
                expect(response.body.data.sanitized).toBe('Hello');
                expect(response.body.data.original).toBe(maliciousInput);
            });
            it('should preserve safe input', async () => {
                const safeInput = 'Hello World';
                const response = await request(app.getHttpServer())
                    .get(`/test/sanitize/${encodeURIComponent(safeInput)}`)
                    .expect(200);
                expect(response.body.data.sanitized).toBe(safeInput);
                expect(response.body.data.original).toBe(safeInput);
            });
        });
    });
    describe('API Versioning', () => {
        beforeEach(() => {
            // Setup default mocks for versioning service
            versioningService.extractVersion.mockReturnValue('1.0');
            versioningService.validateVersion.mockReturnValue(true);
            versioningService.getVersionInfo.mockReturnValue({
                version: '1.0',
                status: 'active',
                releaseDate: new Date(),
            });
            versioningService.getSupportedVersions.mockReturnValue([
                { version: '1.0', status: 'active', releaseDate: new Date() },
                { version: '2.0', status: 'active', releaseDate: new Date() },
            ]);
            versioningService.checkCompatibility.mockReturnValue({
                compatible: true,
                migrationRequired: false,
                breakingChanges: [],
            });
            versioningService.logVersionUsage.mockResolvedValue(undefined);
            versioningService.getDeprecationWarnings.mockReturnValue([]);
        });
        describe('Version Extraction', () => {
            it('should extract version from Accept header', async () => {
                await request(app.getHttpServer())
                    .get('/versioned/v1')
                    .set('Accept', 'application/vnd.api+json;version=1.0')
                    .expect(200);
                expect(versioningService.extractVersion).toHaveBeenCalled();
            });
            it('should extract version from custom header', async () => {
                await request(app.getHttpServer())
                    .get('/versioned/v1')
                    .set('X-API-Version', '1.0')
                    .expect(200);
                expect(versioningService.extractVersion).toHaveBeenCalled();
            });
            it('should extract version from query parameter', async () => {
                await request(app.getHttpServer())
                    .get('/versioned/v1?version=1.0')
                    .expect(200);
                expect(versioningService.extractVersion).toHaveBeenCalled();
            });
        });
        describe('Version Validation', () => {
            it('should allow valid version', async () => {
                versioningService.extractVersion.mockReturnValue('1.0');
                versioningService.validateVersion.mockReturnValue(true);
                await request(app.getHttpServer())
                    .get('/versioned/v1')
                    .set('X-API-Version', '1.0')
                    .expect(200);
                expect(versioningService.validateVersion).toHaveBeenCalledWith('1.0');
            });
            it('should reject invalid version', async () => {
                versioningService.extractVersion.mockReturnValue('999.0');
                versioningService.validateVersion.mockReturnValue(false);
                versioningService.getSupportedVersions.mockReturnValue([
                    { version: '1.0', status: 'active', releaseDate: new Date() },
                    { version: '2.0', status: 'active', releaseDate: new Date() },
                ]);
                await request(app.getHttpServer())
                    .get('/versioned/v1')
                    .set('X-API-Version', '999.0')
                    .expect(400);
            });
            it('should reject sunset version', async () => {
                versioningService.extractVersion.mockReturnValue('0.5');
                versioningService.validateVersion.mockReturnValue(true);
                versioningService.getVersionInfo.mockReturnValue({
                    version: '0.5',
                    status: 'sunset',
                    releaseDate: new Date(),
                    sunsetDate: new Date(),
                });
                await request(app.getHttpServer())
                    .get('/versioned/v1')
                    .set('X-API-Version', '0.5')
                    .expect(410); // Gone
            });
        });
        describe('Deprecation Handling', () => {
            it('should add deprecation headers for deprecated endpoints', async () => {
                versioningService.extractVersion.mockReturnValue('1.0');
                versioningService.validateVersion.mockReturnValue(true);
                versioningService.getVersionInfo.mockReturnValue({
                    version: '1.0',
                    status: 'deprecated',
                    releaseDate: new Date(),
                    deprecationDate: new Date(),
                });
                const response = await request(app.getHttpServer())
                    .get('/versioned/deprecated')
                    .set('X-API-Version', '1.0')
                    .expect(200);
                expect(response.headers['x-api-deprecated']).toBe('true');
                expect(response.headers['x-api-deprecated-version']).toBe('1.0');
            });
            it('should include replacement information in deprecation headers', async () => {
                versioningService.extractVersion.mockReturnValue('1.0');
                versioningService.validateVersion.mockReturnValue(true);
                versioningService.getVersionInfo.mockReturnValue({
                    version: '1.0',
                    status: 'deprecated',
                    releaseDate: new Date(),
                    deprecationDate: new Date(),
                });
                const response = await request(app.getHttpServer())
                    .get('/versioned/deprecated')
                    .set('X-API-Version', '1.0')
                    .expect(200);
                expect(response.headers['x-api-replacement']).toBe('v2');
            });
        });
        describe('Version Compatibility', () => {
            it('should handle compatible versions', async () => {
                versioningService.extractVersion.mockReturnValue('1.1');
                versioningService.validateVersion.mockReturnValue(true);
                versioningService.getVersionInfo.mockReturnValue({
                    version: '1.1',
                    status: 'active',
                    releaseDate: new Date(),
                });
                versioningService.checkCompatibility.mockReturnValue({
                    compatible: true,
                    migrationRequired: false,
                    breakingChanges: [],
                });
                await request(app.getHttpServer())
                    .get('/versioned/v1')
                    .set('X-API-Version', '1.1')
                    .expect(200);
            });
            it('should warn about migration requirements', async () => {
                versioningService.extractVersion.mockReturnValue('1.5');
                versioningService.validateVersion.mockReturnValue(true);
                versioningService.getVersionInfo.mockReturnValue({
                    version: '1.5',
                    status: 'active',
                    releaseDate: new Date(),
                });
                versioningService.checkCompatibility.mockReturnValue({
                    compatible: true,
                    migrationRequired: true,
                    breakingChanges: ['Field renamed: oldField -> newField'],
                });
                const response = await request(app.getHttpServer())
                    .get('/versioned/v1')
                    .set('X-API-Version', '1.5')
                    .expect(200);
                expect(response.headers['x-api-compatibility-warning']).toContain('Migration required');
                expect(response.headers['x-api-breaking-changes']).toContain('Field renamed');
            });
        });
        describe('Version Logging', () => {
            it('should log version usage', async () => {
                versioningService.extractVersion.mockReturnValue('1.0');
                versioningService.validateVersion.mockReturnValue(true);
                versioningService.getVersionInfo.mockReturnValue({
                    version: '1.0',
                    status: 'active',
                    releaseDate: new Date(),
                });
                await request(app.getHttpServer())
                    .get('/versioned/v1')
                    .set('X-API-Version', '1.0')
                    .expect(200);
                expect(versioningService.logVersionUsage).toHaveBeenCalledWith('1.0', '/versioned/v1', undefined);
            });
        });
    });
    describe('Error Handling', () => {
        it('should handle version guard errors gracefully', async () => {
            versioningService.extractVersion.mockImplementation(() => {
                throw new Error('Version extraction failed');
            });
            await request(app.getHttpServer())
                .get('/versioned/v1')
                .set('X-API-Version', 'invalid')
                .expect(400);
        });
        it('should provide meaningful error messages', async () => {
            versioningService.extractVersion.mockReturnValue('999.0');
            versioningService.validateVersion.mockReturnValue(false);
            versioningService.getSupportedVersions.mockReturnValue([
                { version: '1.0', status: 'active', releaseDate: new Date() },
                { version: '2.0', status: 'active', releaseDate: new Date() },
            ]);
            const response = await request(app.getHttpServer())
                .get('/versioned/v1')
                .set('X-API-Version', '999.0')
                .expect(400);
            expect(response.body.message).toContain('Unsupported API version: 999.0');
            expect(response.body.message).toContain('Supported versions: 1.0, 2.0');
        });
    });
    describe('Request/Response Transformation', () => {
        it('should handle request transformation', async () => {
            const response = await request(app.getHttpServer())
                .get('/test/success')
                .expect(200);
            expect(response.body.timestamp).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/);
        });
        it('should include correlation ID in responses when provided', async () => {
            const correlationId = 'test-correlation-123';
            await request(app.getHttpServer())
                .get('/test/success')
                .set('X-Correlation-ID', correlationId)
                .expect(200);
            // In a real implementation, the correlation ID would be included in the response
            // This test verifies the header is processed
        });
    });
    describe('Rate Limiting Integration', () => {
        // These tests would require actual rate limiting middleware
        it('should respect rate limits', async () => {
            // Multiple requests to test rate limiting
            const requests = Array(5).fill(null).map(() => request(app.getHttpServer()).get('/test/success'));
            const responses = await Promise.all(requests);
            // All should succeed in this test environment
            responses.forEach(response => {
                expect(response.status).toBe(200);
            });
        });
    });
    describe('Content Negotiation', () => {
        it('should handle JSON content type', async () => {
            const response = await request(app.getHttpServer())
                .get('/test/success')
                .set('Accept', 'application/json')
                .expect(200);
            expect(response.headers['content-type']).toMatch(/application\/json/);
        });
        it('should handle different API versions in Accept header', async () => {
            versioningService.extractVersion.mockReturnValue('2.0');
            versioningService.validateVersion.mockReturnValue(true);
            versioningService.getVersionInfo.mockReturnValue({
                version: '2.0',
                status: 'active',
                releaseDate: new Date(),
            });
            await request(app.getHttpServer())
                .get('/versioned/v2')
                .set('Accept', 'application/vnd.api+json;version=2.0')
                .expect(200);
            expect(versioningService.extractVersion).toHaveBeenCalled();
        });
    });
    describe('Security Headers', () => {
        it('should include security headers in responses', async () => {
            const response = await request(app.getHttpServer())
                .get('/test/success')
                .expect(200);
            // In a real implementation, security headers would be added by middleware
            // This test structure shows how to verify their presence
            expect(response.headers).toBeDefined();
        });
    });
    describe('Pagination Integration', () => {
        it('should handle pagination parameters correctly', async () => {
            const response = await request(app.getHttpServer())
                .get('/test/paginated?page=1&limit=10')
                .expect(200);
            expect(response.body.pagination).toEqual({
                page: 1,
                limit: 10,
                total: 2,
                totalPages: 1,
                hasNext: false,
                hasPrev: false,
                offset: 0,
            });
        });
        it('should handle invalid pagination parameters', async () => {
            const response = await request(app.getHttpServer())
                .get('/test/paginated?page=-1&limit=1000')
                .expect(200);
            // BaseApiController should normalize invalid parameters
            expect(response.body.pagination.page).toBeGreaterThan(0);
            expect(response.body.pagination.limit).toBeLessThanOrEqual(100);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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