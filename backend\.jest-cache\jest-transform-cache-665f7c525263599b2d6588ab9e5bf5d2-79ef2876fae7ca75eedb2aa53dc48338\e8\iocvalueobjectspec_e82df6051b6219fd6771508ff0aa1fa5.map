{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\threat-indicators\\__tests__\\ioc.value-object.spec.ts", "mappings": ";;AAAA,0DAA+E;AAE/E,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;IAChC,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,gBAAgB;YAChB,MAAM,GAAG,GAAG,sBAAG,CAAC,MAAM,CACpB,0BAAO,CAAC,UAAU,EAClB,aAAa,EACb,gCAAa,CAAC,IAAI,EAClB,8BAAW,CAAC,MAAM,CACnB,CAAC;YAEF,SAAS;YACT,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,0BAAO,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACtC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,gCAAa,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,8BAAW,CAAC,MAAM,CAAC,CAAC;YAC9C,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,gBAAgB;YAChB,MAAM,GAAG,GAAG,sBAAG,CAAC,MAAM,CACpB,0BAAO,CAAC,MAAM,EACd,uBAAuB,EACvB,gCAAa,CAAC,SAAS,EACvB,8BAAW,CAAC,QAAQ,EACpB;gBACE,WAAW,EAAE,wBAAwB;gBACrC,MAAM,EAAE,mBAAmB;gBAC3B,IAAI,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC;gBACvB,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,EAAE,WAAW;aACxD,CACF,CAAC;YAEF,SAAS;YACT,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YACvD,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAC7C,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,gBAAgB;YAChB,MAAM,GAAG,GAAG,sBAAG,CAAC,eAAe,CAC7B,UAAU,EACV,gCAAa,CAAC,MAAM,EACpB,8BAAW,CAAC,IAAI,CACjB,CAAC;YAEF,SAAS;YACT,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,0BAAO,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,gBAAgB;YAChB,MAAM,GAAG,GAAG,sBAAG,CAAC,YAAY,CAC1B,gBAAgB,EAChB,gCAAa,CAAC,GAAG,EACjB,8BAAW,CAAC,MAAM,CACnB,CAAC;YAEF,SAAS;YACT,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,0BAAO,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,gBAAgB;YAChB,MAAM,GAAG,GAAG,sBAAG,CAAC,cAAc,CAC5B,0CAA0C,EAC1C,gCAAa,CAAC,IAAI,EAClB,8BAAW,CAAC,QAAQ,CACrB,CAAC;YAEF,SAAS;YACT,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,0BAAO,CAAC,SAAS,CAAC,CAAC;YACzC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,yBAAyB;YACzB,MAAM,CAAC,GAAG,EAAE,CAAC,sBAAG,CAAC,eAAe,CAAC,YAAY,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;iBACpF,OAAO,CAAC,uCAAuC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,gBAAgB;YAChB,MAAM,GAAG,GAAG,sBAAG,CAAC,eAAe,CAAC,eAAe,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;YAEzF,SAAS;YACT,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,gBAAgB;YAChB,MAAM,GAAG,GAAG,sBAAG,CAAC,eAAe,CAAC,KAAK,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;YAE/E,SAAS;YACT,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,yBAAyB;YACzB,MAAM,CAAC,GAAG,EAAE,CAAC,sBAAG,CAAC,YAAY,CAAC,iBAAiB,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;iBACtF,OAAO,CAAC,wCAAwC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM;YACN,MAAM,OAAO,GAAG,kCAAkC,CAAC;YACnD,MAAM,CAAC,GAAG,EAAE,CAAC,sBAAG,CAAC,cAAc,CAAC,OAAO,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;iBAC9E,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,OAAO;YACP,MAAM,QAAQ,GAAG,0CAA0C,CAAC;YAC5D,MAAM,CAAC,GAAG,EAAE,CAAC,sBAAG,CAAC,cAAc,CAAC,QAAQ,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;iBAC/E,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,SAAS;YACT,MAAM,UAAU,GAAG,kEAAkE,CAAC;YACtF,MAAM,CAAC,GAAG,EAAE,CAAC,sBAAG,CAAC,cAAc,CAAC,UAAU,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;iBACjF,GAAG,CAAC,OAAO,EAAE,CAAC;YAEjB,eAAe;YACf,MAAM,CAAC,GAAG,EAAE,CAAC,sBAAG,CAAC,cAAc,CAAC,cAAc,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;iBACrF,OAAO,CAAC,wCAAwC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,YAAY;YACZ,MAAM,GAAG,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,GAAG,EAAE,4BAA4B,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;YAC1G,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAErD,cAAc;YACd,MAAM,CAAC,GAAG,EAAE,CAAC,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,GAAG,EAAE,WAAW,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;iBACvF,OAAO,CAAC,+BAA+B,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,cAAc;YACd,MAAM,GAAG,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,KAAK,EAAE,wBAAwB,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;YACxG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAEjD,gBAAgB;YAChB,MAAM,CAAC,GAAG,EAAE,CAAC,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,KAAK,EAAE,eAAe,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;iBAC7F,OAAO,CAAC,qCAAqC,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,YAAY;YACZ,MAAM,GAAG,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,GAAG,EAAE,eAAe,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;YAC7F,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAExC,cAAc;YACd,MAAM,CAAC,GAAG,EAAE,CAAC,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,GAAG,EAAE,aAAa,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;iBACzF,OAAO,CAAC,iCAAiC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC;YACzC,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,oBAAoB;YAE7D,MAAM,CAAC,GAAG,EAAE,CAAC,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,aAAa,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,EAAE;gBACjG,SAAS;gBACT,QAAQ;aACT,CAAC,CAAC,CAAC,OAAO,CAAC,wCAAwC,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,CAAC,GAAG,EAAE,CAAC,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,EAAE,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;iBACrF,OAAO,CAAC,iCAAiC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,sBAAG,CAAC;gBACnB,IAAI,EAAE,SAAoB;gBAC1B,KAAK,EAAE,aAAa;gBACpB,UAAU,EAAE,gCAAa,CAAC,IAAI;gBAC9B,QAAQ,EAAE,8BAAW,CAAC,MAAM;aAC7B,CAAC,CAAC,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,sBAAG,CAAC;gBACnB,IAAI,EAAE,0BAAO,CAAC,UAAU;gBACxB,KAAK,EAAE,aAAa;gBACpB,UAAU,EAAE,SAA0B;gBACtC,QAAQ,EAAE,8BAAW,CAAC,MAAM;aAC7B,CAAC,CAAC,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,sBAAG,CAAC;gBACnB,IAAI,EAAE,0BAAO,CAAC,UAAU;gBACxB,KAAK,EAAE,aAAa;gBACpB,UAAU,EAAE,gCAAa,CAAC,IAAI;gBAC9B,QAAQ,EAAE,SAAwB;aACnC,CAAC,CAAC,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,UAAU,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,aAAa,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,EAAE;gBACvG,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,eAAe;aACxD,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,aAAa,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,EAAE;gBACtG,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,EAAE,oBAAoB;aACjE,CAAC,CAAC;YAEH,MAAM,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,iBAAiB,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,aAAa,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;YAChH,MAAM,gBAAgB,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,aAAa,EAAE,gCAAa,CAAC,GAAG,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;YAC9G,MAAM,YAAY,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,aAAa,EAAE,gCAAa,CAAC,SAAS,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;YAEhH,MAAM,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxD,MAAM,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,eAAe,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,aAAa,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,IAAI,CAAC,CAAC;YAC5G,MAAM,WAAW,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,aAAa,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,QAAQ,CAAC,CAAC;YAC5G,MAAM,cAAc,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,aAAa,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,GAAG,CAAC,CAAC;YAE1G,MAAM,CAAC,eAAe,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5C,MAAM,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpD,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uBAAuB,EAAE,GAAG,EAAE;YAC/B,MAAM,GAAG,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,aAAa,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,EAAE;gBAChG,IAAI,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC;aAClC,CAAC,CAAC;YAEH,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,aAAa;YAChF,MAAM,GAAG,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,aAAa,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,EAAE;gBAChG,SAAS,EAAE,QAAQ;aACpB,CAAC,CAAC;YAEH,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,kBAAkB;YACvF,MAAM,GAAG,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,aAAa,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,EAAE;gBAChG,SAAS,EAAE,UAAU;aACtB,CAAC,CAAC;YAEH,MAAM,CAAC,GAAG,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,WAAW,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,aAAa,EAAE,gCAAa,CAAC,SAAS,EAAE,8BAAW,CAAC,QAAQ,CAAC,CAAC;YACjH,MAAM,MAAM,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,aAAa,EAAE,gCAAa,CAAC,GAAG,EAAE,8BAAW,CAAC,IAAI,CAAC,CAAC;YAElG,MAAM,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC;YAC9E,MAAM,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YACzD,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,MAAM,GAAG,sBAAG,CAAC,cAAc,CAAC,kCAAkC,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;YAC9G,MAAM,OAAO,GAAG,sBAAG,CAAC,cAAc,CAAC,0CAA0C,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;YACvH,MAAM,SAAS,GAAG,sBAAG,CAAC,cAAc,CAAC,kEAAkE,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;YACjJ,MAAM,KAAK,GAAG,sBAAG,CAAC,eAAe,CAAC,aAAa,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;YAEzF,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC3C,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,WAAW,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,aAAa,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;YAC1G,MAAM,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;YAC3B,MAAM,UAAU,GAAG,WAAW,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAEvD,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACzC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAC7C,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,WAAW,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,aAAa,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,EAAE;gBACxG,IAAI,EAAE,CAAC,SAAS,CAAC;aAClB,CAAC,CAAC;YACH,MAAM,UAAU,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC;YAE1D,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACzC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC;YAC7D,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,WAAW,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,aAAa,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;YAC1G,MAAM,OAAO,GAAG,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,gBAAgB,EAAE,CAAC;YACjE,MAAM,UAAU,GAAG,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAEpD,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACzC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAC5C,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,WAAW,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,aAAa,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;YAC1G,MAAM,cAAc,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,CAAC;YACvD,MAAM,UAAU,GAAG,WAAW,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAE9D,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACzC,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YACrD,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,aAAa,EAAE,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,IAAI,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,aAAa,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;YACnG,MAAM,IAAI,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,aAAa,EAAE,gCAAa,CAAC,GAAG,EAAE,8BAAW,CAAC,IAAI,CAAC,CAAC;YAChG,MAAM,IAAI,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,aAAa,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;YACnG,MAAM,IAAI,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,MAAM,EAAE,aAAa,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;YAE/F,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,sBAAsB;YAC5D,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,kBAAkB;YACzD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAiB;YACxD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,GAAG,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,aAAa,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,CAAC,CAAC;YAClG,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,GAAG,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,aAAa,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,MAAM,EAAE;gBAChG,WAAW,EAAE,UAAU;gBACvB,IAAI,EAAE,CAAC,MAAM,CAAC;aACf,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;YAE1B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,0BAAO,CAAC,UAAU,CAAC,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACvC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,gCAAa,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,8BAAW,CAAC,MAAM,CAAC,CAAC;YAC/C,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,WAAW,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,MAAM,EAAE,eAAe,EAAE,gCAAa,CAAC,SAAS,EAAE,8BAAW,CAAC,QAAQ,CAAC,CAAC;YAC/G,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC;YAClC,MAAM,eAAe,GAAG,sBAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAE3C,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAChE,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,CAAC,sBAAG,CAAC,OAAO,CAAC,0BAAO,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClE,MAAM,CAAC,sBAAG,CAAC,OAAO,CAAC,0BAAO,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClE,MAAM,CAAC,sBAAG,CAAC,OAAO,CAAC,0BAAO,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9D,MAAM,CAAC,sBAAG,CAAC,OAAO,CAAC,0BAAO,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,GAAG,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,aAAa,EAAE,gCAAa,CAAC,IAAI,EAAE,8BAAW,CAAC,QAAQ,EAAE;gBAClG,IAAI,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC;aAC5B,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,GAAG,CAAC,UAAU,EAAE,CAAC;YAEjC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,0BAAO,CAAC,UAAU,CAAC,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC1C,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,gCAAa,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,8BAAW,CAAC,QAAQ,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACjC,MAAM,CAAC,OAAO,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\threat-indicators\\__tests__\\ioc.value-object.spec.ts"], "sourcesContent": ["import { IOC, IOCType, IOCConfidence, IOCSeverity } from '../ioc.value-object';\r\n\r\ndescribe('IOC Value Object', () => {\r\n  describe('creation', () => {\r\n    it('should create valid IOC with required properties', () => {\r\n      // Arrange & Act\r\n      const ioc = IOC.create(\r\n        IOCType.IP_ADDRESS,\r\n        '***********',\r\n        IOCConfidence.HIGH,\r\n        IOCSeverity.MEDIUM\r\n      );\r\n\r\n      // Assert\r\n      expect(ioc.type).toBe(IOCType.IP_ADDRESS);\r\n      expect(ioc.value).toBe('***********');\r\n      expect(ioc.confidence).toBe(IOCConfidence.HIGH);\r\n      expect(ioc.severity).toBe(IOCSeverity.MEDIUM);\r\n      expect(ioc.firstSeen).toBeInstanceOf(Date);\r\n    });\r\n\r\n    it('should create IOC with optional properties', () => {\r\n      // Arrange & Act\r\n      const ioc = IOC.create(\r\n        IOCType.DOMAIN,\r\n        'malicious.example.com',\r\n        IOCConfidence.CONFIRMED,\r\n        IOCSeverity.CRITICAL,\r\n        {\r\n          description: 'Known malicious domain',\r\n          source: 'threat-intel-feed',\r\n          tags: ['malware', 'c2'],\r\n          expiresAt: new Date(Date.now() + 86400000), // 24 hours\r\n        }\r\n      );\r\n\r\n      // Assert\r\n      expect(ioc.description).toBe('Known malicious domain');\r\n      expect(ioc.source).toBe('threat-intel-feed');\r\n      expect(ioc.tags).toEqual(['malware', 'c2']);\r\n      expect(ioc.expiresAt).toBeInstanceOf(Date);\r\n    });\r\n\r\n    it('should create IP address IOC using factory method', () => {\r\n      // Arrange & Act\r\n      const ioc = IOC.createIPAddress(\r\n        '********',\r\n        IOCConfidence.MEDIUM,\r\n        IOCSeverity.HIGH\r\n      );\r\n\r\n      // Assert\r\n      expect(ioc.type).toBe(IOCType.IP_ADDRESS);\r\n      expect(ioc.value).toBe('********');\r\n    });\r\n\r\n    it('should create domain IOC using factory method', () => {\r\n      // Arrange & Act\r\n      const ioc = IOC.createDomain(\r\n        'suspicious.com',\r\n        IOCConfidence.LOW,\r\n        IOCSeverity.MEDIUM\r\n      );\r\n\r\n      // Assert\r\n      expect(ioc.type).toBe(IOCType.DOMAIN);\r\n      expect(ioc.value).toBe('suspicious.com');\r\n    });\r\n\r\n    it('should create file hash IOC using factory method', () => {\r\n      // Arrange & Act\r\n      const ioc = IOC.createFileHash(\r\n        'a1b2c3d4e5f6789012345678901234567890abcd',\r\n        IOCConfidence.HIGH,\r\n        IOCSeverity.CRITICAL\r\n      );\r\n\r\n      // Assert\r\n      expect(ioc.type).toBe(IOCType.FILE_HASH);\r\n      expect(ioc.value).toBe('a1b2c3d4e5f6789012345678901234567890abcd');\r\n    });\r\n  });\r\n\r\n  describe('validation', () => {\r\n    it('should validate IP address format', () => {\r\n      // Arrange & Act & Assert\r\n      expect(() => IOC.createIPAddress('invalid-ip', IOCConfidence.HIGH, IOCSeverity.MEDIUM))\r\n        .toThrow('Invalid IP address format: invalid-ip');\r\n    });\r\n\r\n    it('should validate IPv4 addresses', () => {\r\n      // Arrange & Act\r\n      const ioc = IOC.createIPAddress('***********00', IOCConfidence.HIGH, IOCSeverity.MEDIUM);\r\n\r\n      // Assert\r\n      expect(ioc.value).toBe('***********00');\r\n    });\r\n\r\n    it('should validate IPv6 addresses', () => {\r\n      // Arrange & Act\r\n      const ioc = IOC.createIPAddress('::1', IOCConfidence.HIGH, IOCSeverity.MEDIUM);\r\n\r\n      // Assert\r\n      expect(ioc.value).toBe('::1');\r\n    });\r\n\r\n    it('should validate domain format', () => {\r\n      // Arrange & Act & Assert\r\n      expect(() => IOC.createDomain('invalid..domain', IOCConfidence.HIGH, IOCSeverity.MEDIUM))\r\n        .toThrow('Invalid domain format: invalid..domain');\r\n    });\r\n\r\n    it('should validate file hash formats', () => {\r\n      // MD5\r\n      const md5Hash = 'a1b2c3d4e5f67890123456789012345a';\r\n      expect(() => IOC.createFileHash(md5Hash, IOCConfidence.HIGH, IOCSeverity.MEDIUM))\r\n        .not.toThrow();\r\n\r\n      // SHA1\r\n      const sha1Hash = 'a1b2c3d4e5f6789012345678901234567890abcd';\r\n      expect(() => IOC.createFileHash(sha1Hash, IOCConfidence.HIGH, IOCSeverity.MEDIUM))\r\n        .not.toThrow();\r\n\r\n      // SHA256\r\n      const sha256Hash = 'a1b2c3d4e5f67890123456789012345678901234567890123456789012345678';\r\n      expect(() => IOC.createFileHash(sha256Hash, IOCConfidence.HIGH, IOCSeverity.MEDIUM))\r\n        .not.toThrow();\r\n\r\n      // Invalid hash\r\n      expect(() => IOC.createFileHash('invalid-hash', IOCConfidence.HIGH, IOCSeverity.MEDIUM))\r\n        .toThrow('Invalid file hash format: invalid-hash');\r\n    });\r\n\r\n    it('should validate URL format', () => {\r\n      // Valid URL\r\n      const ioc = IOC.create(IOCType.URL, 'https://malicious.com/path', IOCConfidence.HIGH, IOCSeverity.MEDIUM);\r\n      expect(ioc.value).toBe('https://malicious.com/path');\r\n\r\n      // Invalid URL\r\n      expect(() => IOC.create(IOCType.URL, 'not-a-url', IOCConfidence.HIGH, IOCSeverity.MEDIUM))\r\n        .toThrow('Invalid URL format: not-a-url');\r\n    });\r\n\r\n    it('should validate email format', () => {\r\n      // Valid email\r\n      const ioc = IOC.create(IOCType.EMAIL, '<EMAIL>', IOCConfidence.HIGH, IOCSeverity.MEDIUM);\r\n      expect(ioc.value).toBe('<EMAIL>');\r\n\r\n      // Invalid email\r\n      expect(() => IOC.create(IOCType.EMAIL, 'invalid-email', IOCConfidence.HIGH, IOCSeverity.MEDIUM))\r\n        .toThrow('Invalid email format: invalid-email');\r\n    });\r\n\r\n    it('should validate CVE format', () => {\r\n      // Valid CVE\r\n      const ioc = IOC.create(IOCType.CVE, 'CVE-2023-1234', IOCConfidence.HIGH, IOCSeverity.MEDIUM);\r\n      expect(ioc.value).toBe('CVE-2023-1234');\r\n\r\n      // Invalid CVE\r\n      expect(() => IOC.create(IOCType.CVE, 'invalid-cve', IOCConfidence.HIGH, IOCSeverity.MEDIUM))\r\n        .toThrow('Invalid CVE format: invalid-cve');\r\n    });\r\n\r\n    it('should validate temporal relationships', () => {\r\n      const firstSeen = new Date('2023-01-01');\r\n      const lastSeen = new Date('2022-12-31'); // Before first seen\r\n\r\n      expect(() => IOC.create(IOCType.IP_ADDRESS, '***********', IOCConfidence.HIGH, IOCSeverity.MEDIUM, {\r\n        firstSeen,\r\n        lastSeen,\r\n      })).toThrow('IOC firstSeen cannot be after lastSeen');\r\n    });\r\n\r\n    it('should require non-empty value', () => {\r\n      expect(() => IOC.create(IOCType.IP_ADDRESS, '', IOCConfidence.HIGH, IOCSeverity.MEDIUM))\r\n        .toThrow('IOC must have a non-empty value');\r\n    });\r\n\r\n    it('should require valid IOC type', () => {\r\n      expect(() => new IOC({\r\n        type: 'invalid' as IOCType,\r\n        value: '***********',\r\n        confidence: IOCConfidence.HIGH,\r\n        severity: IOCSeverity.MEDIUM,\r\n      })).toThrow('Invalid IOC type: invalid');\r\n    });\r\n\r\n    it('should require valid confidence level', () => {\r\n      expect(() => new IOC({\r\n        type: IOCType.IP_ADDRESS,\r\n        value: '***********',\r\n        confidence: 'invalid' as IOCConfidence,\r\n        severity: IOCSeverity.MEDIUM,\r\n      })).toThrow('Invalid IOC confidence: invalid');\r\n    });\r\n\r\n    it('should require valid severity level', () => {\r\n      expect(() => new IOC({\r\n        type: IOCType.IP_ADDRESS,\r\n        value: '***********',\r\n        confidence: IOCConfidence.HIGH,\r\n        severity: 'invalid' as IOCSeverity,\r\n      })).toThrow('Invalid IOC severity: invalid');\r\n    });\r\n  });\r\n\r\n  describe('business logic', () => {\r\n    it('should check if IOC is expired', () => {\r\n      const expiredIOC = IOC.create(IOCType.IP_ADDRESS, '***********', IOCConfidence.HIGH, IOCSeverity.MEDIUM, {\r\n        expiresAt: new Date(Date.now() - 1000), // 1 second ago\r\n      });\r\n\r\n      const activeIOC = IOC.create(IOCType.IP_ADDRESS, '***********', IOCConfidence.HIGH, IOCSeverity.MEDIUM, {\r\n        expiresAt: new Date(Date.now() + 86400000), // 24 hours from now\r\n      });\r\n\r\n      expect(expiredIOC.isExpired()).toBe(true);\r\n      expect(expiredIOC.isActive()).toBe(false);\r\n      expect(activeIOC.isExpired()).toBe(false);\r\n      expect(activeIOC.isActive()).toBe(true);\r\n    });\r\n\r\n    it('should check confidence levels', () => {\r\n      const highConfidenceIOC = IOC.create(IOCType.IP_ADDRESS, '***********', IOCConfidence.HIGH, IOCSeverity.MEDIUM);\r\n      const lowConfidenceIOC = IOC.create(IOCType.IP_ADDRESS, '***********', IOCConfidence.LOW, IOCSeverity.MEDIUM);\r\n      const confirmedIOC = IOC.create(IOCType.IP_ADDRESS, '***********', IOCConfidence.CONFIRMED, IOCSeverity.MEDIUM);\r\n\r\n      expect(highConfidenceIOC.isHighConfidence()).toBe(true);\r\n      expect(lowConfidenceIOC.isHighConfidence()).toBe(false);\r\n      expect(confirmedIOC.isHighConfidence()).toBe(true);\r\n    });\r\n\r\n    it('should check severity levels', () => {\r\n      const highSeverityIOC = IOC.create(IOCType.IP_ADDRESS, '***********', IOCConfidence.HIGH, IOCSeverity.HIGH);\r\n      const criticalIOC = IOC.create(IOCType.IP_ADDRESS, '***********', IOCConfidence.HIGH, IOCSeverity.CRITICAL);\r\n      const lowSeverityIOC = IOC.create(IOCType.IP_ADDRESS, '***********', IOCConfidence.HIGH, IOCSeverity.LOW);\r\n\r\n      expect(highSeverityIOC.isHighSeverity()).toBe(true);\r\n      expect(criticalIOC.isHighSeverity()).toBe(true);\r\n      expect(criticalIOC.isCritical()).toBe(true);\r\n      expect(lowSeverityIOC.isHighSeverity()).toBe(false);\r\n      expect(lowSeverityIOC.isCritical()).toBe(false);\r\n    });\r\n\r\n    it('should check for tags', () => {\r\n      const ioc = IOC.create(IOCType.IP_ADDRESS, '***********', IOCConfidence.HIGH, IOCSeverity.MEDIUM, {\r\n        tags: ['malware', 'botnet', 'c2'],\r\n      });\r\n\r\n      expect(ioc.hasTag('malware')).toBe(true);\r\n      expect(ioc.hasTag('phishing')).toBe(false);\r\n    });\r\n\r\n    it('should calculate age correctly', () => {\r\n      const pastDate = new Date(Date.now() - (5 * 24 * 60 * 60 * 1000)); // 5 days ago\r\n      const ioc = IOC.create(IOCType.IP_ADDRESS, '***********', IOCConfidence.HIGH, IOCSeverity.MEDIUM, {\r\n        firstSeen: pastDate,\r\n      });\r\n\r\n      expect(ioc.getAge()).toBe(5);\r\n    });\r\n\r\n    it('should calculate days until expiration', () => {\r\n      const futureDate = new Date(Date.now() + (3 * 24 * 60 * 60 * 1000)); // 3 days from now\r\n      const ioc = IOC.create(IOCType.IP_ADDRESS, '***********', IOCConfidence.HIGH, IOCSeverity.MEDIUM, {\r\n        expiresAt: futureDate,\r\n      });\r\n\r\n      expect(ioc.getDaysUntilExpiration()).toBe(3);\r\n    });\r\n\r\n    it('should calculate threat score', () => {\r\n      const criticalIOC = IOC.create(IOCType.IP_ADDRESS, '***********', IOCConfidence.CONFIRMED, IOCSeverity.CRITICAL);\r\n      const lowIOC = IOC.create(IOCType.IP_ADDRESS, '***********', IOCConfidence.LOW, IOCSeverity.INFO);\r\n\r\n      expect(criticalIOC.getThreatScore()).toBeGreaterThan(lowIOC.getThreatScore());\r\n      expect(criticalIOC.getThreatScore()).toBeGreaterThan(50);\r\n      expect(lowIOC.getThreatScore()).toBeLessThan(20);\r\n    });\r\n\r\n    it('should identify hash types for file hashes', () => {\r\n      const md5IOC = IOC.createFileHash('a1b2c3d4e5f67890123456789012345a', IOCConfidence.HIGH, IOCSeverity.MEDIUM);\r\n      const sha1IOC = IOC.createFileHash('a1b2c3d4e5f6789012345678901234567890abcd', IOCConfidence.HIGH, IOCSeverity.MEDIUM);\r\n      const sha256IOC = IOC.createFileHash('a1b2c3d4e5f67890123456789012345678901234567890123456789012345678', IOCConfidence.HIGH, IOCSeverity.MEDIUM);\r\n      const ipIOC = IOC.createIPAddress('***********', IOCConfidence.HIGH, IOCSeverity.MEDIUM);\r\n\r\n      expect(md5IOC.getHashType()).toBe('md5');\r\n      expect(sha1IOC.getHashType()).toBe('sha1');\r\n      expect(sha256IOC.getHashType()).toBe('sha256');\r\n      expect(ipIOC.getHashType()).toBeNull();\r\n    });\r\n  });\r\n\r\n  describe('immutability and updates', () => {\r\n    it('should create new IOC when updating last seen', () => {\r\n      const originalIOC = IOC.create(IOCType.IP_ADDRESS, '***********', IOCConfidence.HIGH, IOCSeverity.MEDIUM);\r\n      const newDate = new Date();\r\n      const updatedIOC = originalIOC.updateLastSeen(newDate);\r\n\r\n      expect(updatedIOC).not.toBe(originalIOC);\r\n      expect(updatedIOC.lastSeen).toEqual(newDate);\r\n      expect(originalIOC.lastSeen).toBeUndefined();\r\n    });\r\n\r\n    it('should create new IOC when adding tags', () => {\r\n      const originalIOC = IOC.create(IOCType.IP_ADDRESS, '***********', IOCConfidence.HIGH, IOCSeverity.MEDIUM, {\r\n        tags: ['malware'],\r\n      });\r\n      const updatedIOC = originalIOC.withTags(['botnet', 'c2']);\r\n\r\n      expect(updatedIOC).not.toBe(originalIOC);\r\n      expect(updatedIOC.tags).toEqual(['malware', 'botnet', 'c2']);\r\n      expect(originalIOC.tags).toEqual(['malware']);\r\n    });\r\n\r\n    it('should create new IOC when adding context', () => {\r\n      const originalIOC = IOC.create(IOCType.IP_ADDRESS, '***********', IOCConfidence.HIGH, IOCSeverity.MEDIUM);\r\n      const context = { campaign: 'APT-123', actor: 'threat-group-x' };\r\n      const updatedIOC = originalIOC.withContext(context);\r\n\r\n      expect(updatedIOC).not.toBe(originalIOC);\r\n      expect(updatedIOC.context).toEqual(context);\r\n      expect(originalIOC.context).toEqual({});\r\n    });\r\n\r\n    it('should create new IOC when setting expiration', () => {\r\n      const originalIOC = IOC.create(IOCType.IP_ADDRESS, '***********', IOCConfidence.HIGH, IOCSeverity.MEDIUM);\r\n      const expirationDate = new Date(Date.now() + 86400000);\r\n      const updatedIOC = originalIOC.withExpiration(expirationDate);\r\n\r\n      expect(updatedIOC).not.toBe(originalIOC);\r\n      expect(updatedIOC.expiresAt).toEqual(expirationDate);\r\n      expect(originalIOC.expiresAt).toBeUndefined();\r\n    });\r\n  });\r\n\r\n  describe('equality and comparison', () => {\r\n    it('should compare IOCs for equality based on type and value', () => {\r\n      const ioc1 = IOC.create(IOCType.IP_ADDRESS, '***********', IOCConfidence.HIGH, IOCSeverity.MEDIUM);\r\n      const ioc2 = IOC.create(IOCType.IP_ADDRESS, '***********', IOCConfidence.LOW, IOCSeverity.HIGH);\r\n      const ioc3 = IOC.create(IOCType.IP_ADDRESS, '***********', IOCConfidence.HIGH, IOCSeverity.MEDIUM);\r\n      const ioc4 = IOC.create(IOCType.DOMAIN, '***********', IOCConfidence.HIGH, IOCSeverity.MEDIUM);\r\n\r\n      expect(ioc1.equals(ioc2)).toBe(true); // Same type and value\r\n      expect(ioc1.equals(ioc3)).toBe(false); // Different value\r\n      expect(ioc1.equals(ioc4)).toBe(false); // Different type\r\n      expect(ioc1.equals(undefined)).toBe(false);\r\n    });\r\n\r\n    it('should have consistent string representation', () => {\r\n      const ioc = IOC.create(IOCType.IP_ADDRESS, '***********', IOCConfidence.HIGH, IOCSeverity.MEDIUM);\r\n      expect(ioc.toString()).toBe('ip_address:***********');\r\n    });\r\n  });\r\n\r\n  describe('serialization', () => {\r\n    it('should serialize to JSON correctly', () => {\r\n      const ioc = IOC.create(IOCType.IP_ADDRESS, '***********', IOCConfidence.HIGH, IOCSeverity.MEDIUM, {\r\n        description: 'Test IOC',\r\n        tags: ['test'],\r\n      });\r\n\r\n      const json = ioc.toJSON();\r\n\r\n      expect(json.type).toBe(IOCType.IP_ADDRESS);\r\n      expect(json.value).toBe('***********');\r\n      expect(json.confidence).toBe(IOCConfidence.HIGH);\r\n      expect(json.severity).toBe(IOCSeverity.MEDIUM);\r\n      expect(json.description).toBe('Test IOC');\r\n      expect(json.tags).toEqual(['test']);\r\n      expect(json.summary).toBeDefined();\r\n      expect(json.hashType).toBeNull();\r\n    });\r\n\r\n    it('should deserialize from JSON correctly', () => {\r\n      const originalIOC = IOC.create(IOCType.DOMAIN, 'malicious.com', IOCConfidence.CONFIRMED, IOCSeverity.CRITICAL);\r\n      const json = originalIOC.toJSON();\r\n      const deserializedIOC = IOC.fromJSON(json);\r\n\r\n      expect(deserializedIOC.equals(originalIOC)).toBe(true);\r\n      expect(deserializedIOC.type).toBe(originalIOC.type);\r\n      expect(deserializedIOC.value).toBe(originalIOC.value);\r\n      expect(deserializedIOC.confidence).toBe(originalIOC.confidence);\r\n      expect(deserializedIOC.severity).toBe(originalIOC.severity);\r\n    });\r\n  });\r\n\r\n  describe('validation utility', () => {\r\n    it('should validate IOC format without creating instance', () => {\r\n      expect(IOC.isValid(IOCType.IP_ADDRESS, '***********')).toBe(true);\r\n      expect(IOC.isValid(IOCType.IP_ADDRESS, 'invalid-ip')).toBe(false);\r\n      expect(IOC.isValid(IOCType.DOMAIN, 'example.com')).toBe(true);\r\n      expect(IOC.isValid(IOCType.DOMAIN, 'invalid..domain')).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('summary and analysis', () => {\r\n    it('should provide comprehensive summary', () => {\r\n      const ioc = IOC.create(IOCType.IP_ADDRESS, '***********', IOCConfidence.HIGH, IOCSeverity.CRITICAL, {\r\n        tags: ['malware', 'botnet'],\r\n      });\r\n\r\n      const summary = ioc.getSummary();\r\n\r\n      expect(summary.type).toBe(IOCType.IP_ADDRESS);\r\n      expect(summary.value).toBe('***********');\r\n      expect(summary.confidence).toBe(IOCConfidence.HIGH);\r\n      expect(summary.severity).toBe(IOCSeverity.CRITICAL);\r\n      expect(summary.isHighConfidence).toBe(true);\r\n      expect(summary.isHighSeverity).toBe(true);\r\n      expect(summary.tagCount).toBe(2);\r\n      expect(typeof summary.threatScore).toBe('number');\r\n    });\r\n  });\r\n});"], "version": 3}