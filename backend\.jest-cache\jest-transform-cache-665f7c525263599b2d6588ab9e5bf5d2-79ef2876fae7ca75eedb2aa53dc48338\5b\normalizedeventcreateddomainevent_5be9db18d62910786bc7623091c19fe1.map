{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\normalized-event-created.domain-event.ts", "mappings": ";;;AAAA,6DAA4E;AAE5E,sEAA6D;AAC7D,iFAA0E;AAwB1E;;;;;;;;;;GAUG;AACH,MAAa,iCAAkC,SAAQ,+BAAgD;IACrG,YACE,WAA2B,EAC3B,SAA0C,EAC1C,OAOC;QAED,KAAK,CAAC,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,IAAI,oBAAoB;QACtB,OAAO,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,CAAC,mCAAa,CAAC,IAAI,EAAE,mCAAa,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,QAAQ,KAAK,mCAAa,CAAC,QAAQ,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,OAAO,IAAI,CAAC,mBAAmB,KAAK,6CAAmB,CAAC,SAAS,CAAC;IACpE,CAAC;IAED;;OAEG;IACH,eAAe;QAeb,OAAO;YACL,iBAAiB,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;YAC9C,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE;YAChD,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;YAC7C,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE;YACrC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE;YAC7B,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC7C,wBAAwB,EAAE,IAAI,CAAC,wBAAwB,EAAE;SAC1D,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,GAAG,KAAK,CAAC,MAAM,EAAE;YACjB,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE;SACrC,CAAC;IACJ,CAAC;CACF;AAhJD,8EAgJC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\normalized-event-created.domain-event.ts"], "sourcesContent": ["import { BaseDomainEvent, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { EventType } from '../enums/event-type.enum';\r\nimport { EventSeverity } from '../enums/event-severity.enum';\r\nimport { NormalizationStatus } from '../entities/normalized-event.entity';\r\n\r\n/**\r\n * Normalized Event Created Domain Event Data\r\n */\r\nexport interface NormalizedEventCreatedEventData {\r\n  /** Original event ID that was normalized */\r\n  originalEventId: UniqueEntityId;\r\n  /** Type of the normalized event */\r\n  eventType: EventType;\r\n  /** Severity of the normalized event */\r\n  severity: EventSeverity;\r\n  /** Current normalization status */\r\n  normalizationStatus: NormalizationStatus;\r\n  /** Schema version used for normalization */\r\n  schemaVersion: string;\r\n  /** Data quality score */\r\n  dataQualityScore?: number;\r\n  /** Number of applied normalization rules */\r\n  appliedRulesCount: number;\r\n  /** Whether the event requires manual review */\r\n  requiresManualReview: boolean;\r\n}\r\n\r\n/**\r\n * Normalized Event Created Domain Event\r\n * \r\n * Raised when a new normalized security event is created in the system.\r\n * This event triggers various downstream processes including:\r\n * - Enrichment pipeline initiation\r\n * - Data quality monitoring\r\n * - Manual review queue management\r\n * - Metrics collection\r\n * - Audit logging\r\n */\r\nexport class NormalizedEventCreatedDomainEvent extends BaseDomainEvent<NormalizedEventCreatedEventData> {\r\n  constructor(\r\n    aggregateId: UniqueEntityId,\r\n    eventData: NormalizedEventCreatedEventData,\r\n    options?: {\r\n      eventId?: UniqueEntityId;\r\n      occurredOn?: Date;\r\n      eventVersion?: number;\r\n      correlationId?: string;\r\n      causationId?: string;\r\n      metadata?: Record<string, any>;\r\n    }\r\n  ) {\r\n    super(aggregateId, eventData, options);\r\n  }\r\n\r\n  /**\r\n   * Get the original event ID\r\n   */\r\n  get originalEventId(): UniqueEntityId {\r\n    return this.eventData.originalEventId;\r\n  }\r\n\r\n  /**\r\n   * Get the type of the normalized event\r\n   */\r\n  get eventType(): EventType {\r\n    return this.eventData.eventType;\r\n  }\r\n\r\n  /**\r\n   * Get the severity of the normalized event\r\n   */\r\n  get severity(): EventSeverity {\r\n    return this.eventData.severity;\r\n  }\r\n\r\n  /**\r\n   * Get the normalization status\r\n   */\r\n  get normalizationStatus(): NormalizationStatus {\r\n    return this.eventData.normalizationStatus;\r\n  }\r\n\r\n  /**\r\n   * Get the schema version\r\n   */\r\n  get schemaVersion(): string {\r\n    return this.eventData.schemaVersion;\r\n  }\r\n\r\n  /**\r\n   * Get the data quality score\r\n   */\r\n  get dataQualityScore(): number | undefined {\r\n    return this.eventData.dataQualityScore;\r\n  }\r\n\r\n  /**\r\n   * Get the number of applied rules\r\n   */\r\n  get appliedRulesCount(): number {\r\n    return this.eventData.appliedRulesCount;\r\n  }\r\n\r\n  /**\r\n   * Check if the event requires manual review\r\n   */\r\n  get requiresManualReview(): boolean {\r\n    return this.eventData.requiresManualReview;\r\n  }\r\n\r\n  /**\r\n   * Check if the normalized event is high severity\r\n   */\r\n  isHighSeverity(): boolean {\r\n    return [EventSeverity.HIGH, EventSeverity.CRITICAL].includes(this.severity);\r\n  }\r\n\r\n  /**\r\n   * Check if the normalized event is critical\r\n   */\r\n  isCritical(): boolean {\r\n    return this.severity === EventSeverity.CRITICAL;\r\n  }\r\n\r\n  /**\r\n   * Check if the event has high data quality\r\n   */\r\n  hasHighDataQuality(): boolean {\r\n    return (this.dataQualityScore || 0) >= 60;\r\n  }\r\n\r\n  /**\r\n   * Check if normalization is completed\r\n   */\r\n  isNormalizationCompleted(): boolean {\r\n    return this.normalizationStatus === NormalizationStatus.COMPLETED;\r\n  }\r\n\r\n  /**\r\n   * Get event summary for handlers\r\n   */\r\n  getEventSummary(): {\r\n    normalizedEventId: string;\r\n    originalEventId: string;\r\n    eventType: EventType;\r\n    severity: EventSeverity;\r\n    normalizationStatus: NormalizationStatus;\r\n    schemaVersion: string;\r\n    dataQualityScore?: number;\r\n    appliedRulesCount: number;\r\n    requiresManualReview: boolean;\r\n    isHighSeverity: boolean;\r\n    isCritical: boolean;\r\n    hasHighDataQuality: boolean;\r\n    isNormalizationCompleted: boolean;\r\n  } {\r\n    return {\r\n      normalizedEventId: this.aggregateId.toString(),\r\n      originalEventId: this.originalEventId.toString(),\r\n      eventType: this.eventType,\r\n      severity: this.severity,\r\n      normalizationStatus: this.normalizationStatus,\r\n      schemaVersion: this.schemaVersion,\r\n      dataQualityScore: this.dataQualityScore,\r\n      appliedRulesCount: this.appliedRulesCount,\r\n      requiresManualReview: this.requiresManualReview,\r\n      isHighSeverity: this.isHighSeverity(),\r\n      isCritical: this.isCritical(),\r\n      hasHighDataQuality: this.hasHighDataQuality(),\r\n      isNormalizationCompleted: this.isNormalizationCompleted(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      ...super.toJSON(),\r\n      eventSummary: this.getEventSummary(),\r\n    };\r\n  }\r\n}"], "version": 3}