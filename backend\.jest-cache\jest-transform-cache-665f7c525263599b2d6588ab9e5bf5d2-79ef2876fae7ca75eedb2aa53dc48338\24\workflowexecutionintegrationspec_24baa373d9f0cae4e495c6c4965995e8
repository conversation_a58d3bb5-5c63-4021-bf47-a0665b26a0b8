5fd3b3ad490d77add9642f92e668bca6
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const event_emitter_1 = require("@nestjs/event-emitter");
const bull_1 = require("@nestjs/bull");
const typeorm_2 = require("@nestjs/typeorm");
const notification_workflow_service_1 = require("../../services/notification-workflow.service");
const workflow_engine_service_1 = require("../../services/workflow-engine.service");
const workflow_rule_engine_service_1 = require("../../services/workflow-rule-engine.service");
const workflow_scheduler_service_1 = require("../../services/workflow-scheduler.service");
const notification_queue_management_service_1 = require("../../services/notification-queue-management.service");
const notification_template_management_service_1 = require("../../services/notification-template-management.service");
const notification_workflow_entity_1 = require("../../entities/notification-workflow.entity");
const workflow_execution_entity_1 = require("../../entities/workflow-execution.entity");
const workflow_execution_context_entity_1 = require("../../entities/workflow-execution-context.entity");
const workflow_schedule_entity_1 = require("../../entities/workflow-schedule.entity");
const notification_template_entity_1 = require("../../entities/notification-template.entity");
/**
 * Workflow Execution Integration Tests
 *
 * Comprehensive integration tests for workflow execution including:
 * - End-to-end workflow execution scenarios with real components
 * - Multi-step workflow validation with conditional logic
 * - Error handling and recovery mechanism testing
 * - Performance and timing validation under load
 * - Integration with notification providers and templates
 * - Real-time monitoring and analytics validation
 */
describe('Workflow Execution Integration Tests', () => {
    let app;
    let workflowService;
    let workflowEngine;
    let ruleEngine;
    let schedulerService;
    let queueService;
    let templateService;
    let workflowRepository;
    let executionRepository;
    let contextRepository;
    let scheduleRepository;
    let templateRepository;
    const testUser = {
        id: 'test-user-123',
        email: '<EMAIL>',
        role: 'admin',
        team: 'testing',
    };
    beforeAll(async () => {
        const moduleFixture = await testing_1.Test.createTestingModule({
            imports: [
                config_1.ConfigModule.forRoot({
                    isGlobal: true,
                    envFilePath: '.env.test',
                }),
                typeorm_1.TypeOrmModule.forRoot({
                    type: 'postgres',
                    host: process.env.TEST_DB_HOST || 'localhost',
                    port: parseInt(process.env.TEST_DB_PORT) || 5433,
                    username: process.env.TEST_DB_USERNAME || 'test',
                    password: process.env.TEST_DB_PASSWORD || 'test',
                    database: process.env.TEST_DB_NAME || 'sentinel_test',
                    entities: [
                        notification_workflow_entity_1.NotificationWorkflow,
                        workflow_execution_entity_1.WorkflowExecution,
                        workflow_execution_context_entity_1.WorkflowExecutionContext,
                        workflow_schedule_entity_1.WorkflowSchedule,
                        notification_template_entity_1.NotificationTemplate,
                    ],
                    synchronize: true,
                    dropSchema: true,
                }),
                typeorm_1.TypeOrmModule.forFeature([
                    notification_workflow_entity_1.NotificationWorkflow,
                    workflow_execution_entity_1.WorkflowExecution,
                    workflow_execution_context_entity_1.WorkflowExecutionContext,
                    workflow_schedule_entity_1.WorkflowSchedule,
                    notification_template_entity_1.NotificationTemplate,
                ]),
                event_emitter_1.EventEmitterModule.forRoot(),
                bull_1.BullModule.forRoot({
                    redis: {
                        host: process.env.TEST_REDIS_HOST || 'localhost',
                        port: parseInt(process.env.TEST_REDIS_PORT) || 6380,
                        db: 1, // Use different DB for tests
                    },
                }),
                bull_1.BullModule.registerQueue({ name: 'notification-high' }, { name: 'notification-medium' }, { name: 'notification-low' }),
            ],
            providers: [
                notification_workflow_service_1.NotificationWorkflowService,
                workflow_engine_service_1.WorkflowEngineService,
                workflow_rule_engine_service_1.WorkflowRuleEngine,
                workflow_scheduler_service_1.WorkflowSchedulerService,
                notification_queue_management_service_1.NotificationQueueManagementService,
                notification_template_management_service_1.NotificationTemplateManagementService,
                // Mock providers for external dependencies
                {
                    provide: 'EmailNotificationProvider',
                    useValue: {
                        sendNotification: jest.fn().mockResolvedValue({ success: true, messageId: 'test-123' }),
                        validateConfiguration: jest.fn().mockReturnValue(true),
                        getHealthStatus: jest.fn().mockResolvedValue({ healthy: true }),
                    },
                },
                {
                    provide: 'SlackNotificationProvider',
                    useValue: {
                        sendNotification: jest.fn().mockResolvedValue({ success: true, messageId: 'slack-123' }),
                        validateConfiguration: jest.fn().mockReturnValue(true),
                        getHealthStatus: jest.fn().mockResolvedValue({ healthy: true }),
                    },
                },
            ],
        }).compile();
        app = moduleFixture.createNestApplication();
        await app.init();
        // Get service instances
        workflowService = moduleFixture.get(notification_workflow_service_1.NotificationWorkflowService);
        workflowEngine = moduleFixture.get(workflow_engine_service_1.WorkflowEngineService);
        ruleEngine = moduleFixture.get(workflow_rule_engine_service_1.WorkflowRuleEngine);
        schedulerService = moduleFixture.get(workflow_scheduler_service_1.WorkflowSchedulerService);
        queueService = moduleFixture.get(notification_queue_management_service_1.NotificationQueueManagementService);
        templateService = moduleFixture.get(notification_template_management_service_1.NotificationTemplateManagementService);
        // Get repository instances
        workflowRepository = moduleFixture.get((0, typeorm_2.getRepositoryToken)(notification_workflow_entity_1.NotificationWorkflow));
        executionRepository = moduleFixture.get((0, typeorm_2.getRepositoryToken)(workflow_execution_entity_1.WorkflowExecution));
        contextRepository = moduleFixture.get((0, typeorm_2.getRepositoryToken)(workflow_execution_context_entity_1.WorkflowExecutionContext));
        scheduleRepository = moduleFixture.get((0, typeorm_2.getRepositoryToken)(workflow_schedule_entity_1.WorkflowSchedule));
        templateRepository = moduleFixture.get((0, typeorm_2.getRepositoryToken)(notification_template_entity_1.NotificationTemplate));
    });
    afterAll(async () => {
        await app.close();
    });
    beforeEach(async () => {
        // Clean up database before each test
        await contextRepository.delete({});
        await executionRepository.delete({});
        await scheduleRepository.delete({});
        await workflowRepository.delete({});
        await templateRepository.delete({});
    });
    describe('Simple Workflow Execution', () => {
        it('should execute a simple notification workflow end-to-end', async () => {
            // Create a notification template
            const template = await templateRepository.save({
                name: 'Test Alert Template',
                type: 'email',
                subject: 'Test Alert: {{alert.message}}',
                content: 'Alert Details: {{alert.message}}\nSeverity: {{alert.severity}}',
                variables: ['alert.message', 'alert.severity'],
                isActive: true,
                createdBy: testUser.id,
                updatedBy: testUser.id,
            });
            // Create a simple workflow
            const workflowData = {
                name: 'Simple Alert Notification',
                description: 'Send email notification for alerts',
                definition: {
                    startStep: 'send_notification',
                    steps: [
                        {
                            id: 'send_notification',
                            type: 'notification',
                            config: {
                                channel: 'email',
                                templateId: template.id,
                                recipients: ['<EMAIL>'],
                                priority: 'high',
                            },
                        },
                    ],
                },
                status: 'active',
                category: 'incident_response',
            };
            const workflow = await workflowService.createWorkflow(workflowData, testUser);
            expect(workflow).toBeDefined();
            expect(workflow.id).toBeDefined();
            // Execute the workflow
            const executionData = {
                input: {
                    alert: {
                        id: 'alert-123',
                        message: 'Database connection failure',
                        severity: 'critical',
                        priority: 95,
                    },
                    user: testUser,
                },
                context: {
                    environment: 'test',
                    escalationLevel: 1,
                },
            };
            const execution = await workflowService.executeWorkflow(workflow.id, executionData, testUser);
            expect(execution).toBeDefined();
            expect(execution.executionId).toBeDefined();
            expect(execution.status).toBe('running');
            // Wait for execution to complete
            await new Promise(resolve => setTimeout(resolve, 2000));
            // Verify execution completed successfully
            const completedExecution = await executionRepository.findOne({
                where: { id: execution.executionId },
                relations: ['contexts'],
            });
            expect(completedExecution).toBeDefined();
            expect(completedExecution.status).toBe('completed');
            expect(completedExecution.stepsCompleted).toBe(1);
            expect(completedExecution.totalSteps).toBe(1);
            // Verify step context was created
            expect(completedExecution.contexts).toHaveLength(1);
            const stepContext = completedExecution.contexts[0];
            expect(stepContext.stepId).toBe('send_notification');
            expect(stepContext.status).toBe('completed');
            expect(stepContext.result).toBeDefined();
        });
        it('should handle workflow execution with invalid template', async () => {
            // Create workflow with non-existent template
            const workflowData = {
                name: 'Invalid Template Workflow',
                description: 'Workflow with invalid template reference',
                definition: {
                    startStep: 'send_notification',
                    steps: [
                        {
                            id: 'send_notification',
                            type: 'notification',
                            config: {
                                channel: 'email',
                                templateId: 'non-existent-template',
                                recipients: ['<EMAIL>'],
                            },
                        },
                    ],
                },
                status: 'active',
            };
            const workflow = await workflowService.createWorkflow(workflowData, testUser);
            // Execute the workflow
            const executionData = {
                input: {
                    alert: { message: 'Test alert', severity: 'low' },
                    user: testUser,
                },
            };
            const execution = await workflowService.executeWorkflow(workflow.id, executionData, testUser);
            // Wait for execution to complete
            await new Promise(resolve => setTimeout(resolve, 2000));
            // Verify execution failed
            const failedExecution = await executionRepository.findOne({
                where: { id: execution.executionId },
                relations: ['contexts'],
            });
            expect(failedExecution.status).toBe('failed');
            expect(failedExecution.error).toContain('template');
        });
    });
    describe('Multi-Step Workflow Execution', () => {
        it('should execute a multi-step workflow with conditional logic', async () => {
            // Create workflow with conditional steps
            const workflowData = {
                name: 'Conditional Escalation Workflow',
                description: 'Escalate based on alert severity',
                definition: {
                    startStep: 'check_severity',
                    steps: [
                        {
                            id: 'check_severity',
                            type: 'condition',
                            config: {
                                condition: {
                                    field: 'input.alert.severity',
                                    operator: 'eq',
                                    value: 'critical',
                                },
                            },
                            nextSteps: [
                                {
                                    stepId: 'send_critical_alert',
                                    condition: {
                                        field: 'stepResult.result',
                                        operator: 'eq',
                                        value: true,
                                    },
                                },
                                {
                                    stepId: 'send_normal_alert',
                                    condition: {
                                        field: 'stepResult.result',
                                        operator: 'eq',
                                        value: false,
                                    },
                                },
                            ],
                        },
                        {
                            id: 'send_critical_alert',
                            type: 'notification',
                            config: {
                                channel: 'email',
                                message: 'CRITICAL: {{input.alert.message}}',
                                recipients: ['<EMAIL>', '<EMAIL>'],
                                priority: 'critical',
                            },
                        },
                        {
                            id: 'send_normal_alert',
                            type: 'notification',
                            config: {
                                channel: 'email',
                                message: 'Alert: {{input.alert.message}}',
                                recipients: ['<EMAIL>'],
                                priority: 'medium',
                            },
                        },
                    ],
                },
                status: 'active',
            };
            const workflow = await workflowService.createWorkflow(workflowData, testUser);
            // Test critical alert path
            const criticalExecutionData = {
                input: {
                    alert: {
                        message: 'Database down',
                        severity: 'critical',
                    },
                    user: testUser,
                },
            };
            const criticalExecution = await workflowService.executeWorkflow(workflow.id, criticalExecutionData, testUser);
            // Wait for execution
            await new Promise(resolve => setTimeout(resolve, 3000));
            const completedCriticalExecution = await executionRepository.findOne({
                where: { id: criticalExecution.executionId },
                relations: ['contexts'],
            });
            expect(completedCriticalExecution.status).toBe('completed');
            expect(completedCriticalExecution.stepsCompleted).toBe(2); // condition + critical notification
            // Verify critical alert step was executed
            const criticalStepContext = completedCriticalExecution.contexts.find(ctx => ctx.stepId === 'send_critical_alert');
            expect(criticalStepContext).toBeDefined();
            expect(criticalStepContext.status).toBe('completed');
            // Test normal alert path
            const normalExecutionData = {
                input: {
                    alert: {
                        message: 'Minor issue',
                        severity: 'low',
                    },
                    user: testUser,
                },
            };
            const normalExecution = await workflowService.executeWorkflow(workflow.id, normalExecutionData, testUser);
            // Wait for execution
            await new Promise(resolve => setTimeout(resolve, 3000));
            const completedNormalExecution = await executionRepository.findOne({
                where: { id: normalExecution.executionId },
                relations: ['contexts'],
            });
            expect(completedNormalExecution.status).toBe('completed');
            expect(completedNormalExecution.stepsCompleted).toBe(2); // condition + normal notification
            // Verify normal alert step was executed
            const normalStepContext = completedNormalExecution.contexts.find(ctx => ctx.stepId === 'send_normal_alert');
            expect(normalStepContext).toBeDefined();
            expect(normalStepContext.status).toBe('completed');
        });
        it('should execute workflow with delay and variable steps', async () => {
            const workflowData = {
                name: 'Delayed Notification Workflow',
                description: 'Wait before sending notification',
                definition: {
                    startStep: 'set_variables',
                    steps: [
                        {
                            id: 'set_variables',
                            type: 'variable',
                            config: {
                                variables: {
                                    delayTime: 1000, // 1 second for testing
                                    notificationMessage: 'Delayed alert: {{input.alert.message}}',
                                },
                            },
                            nextStep: 'wait_step',
                        },
                        {
                            id: 'wait_step',
                            type: 'delay',
                            config: {
                                delay: '{{variables.delayTime}}',
                            },
                            nextStep: 'send_notification',
                        },
                        {
                            id: 'send_notification',
                            type: 'notification',
                            config: {
                                channel: 'email',
                                message: '{{variables.notificationMessage}}',
                                recipients: ['<EMAIL>'],
                            },
                        },
                    ],
                },
                status: 'active',
            };
            const workflow = await workflowService.createWorkflow(workflowData, testUser);
            const executionData = {
                input: {
                    alert: {
                        message: 'System maintenance required',
                        severity: 'medium',
                    },
                    user: testUser,
                },
            };
            const startTime = Date.now();
            const execution = await workflowService.executeWorkflow(workflow.id, executionData, testUser);
            // Wait for execution to complete
            await new Promise(resolve => setTimeout(resolve, 4000));
            const completedExecution = await executionRepository.findOne({
                where: { id: execution.executionId },
                relations: ['contexts'],
            });
            const endTime = Date.now();
            const executionTime = endTime - startTime;
            expect(completedExecution.status).toBe('completed');
            expect(completedExecution.stepsCompleted).toBe(3);
            expect(executionTime).toBeGreaterThan(1000); // Should take at least 1 second due to delay
            // Verify all steps were executed in order
            const stepContexts = completedExecution.contexts.sort((a, b) => a.startedAt.getTime() - b.startedAt.getTime());
            expect(stepContexts[0].stepId).toBe('set_variables');
            expect(stepContexts[1].stepId).toBe('wait_step');
            expect(stepContexts[2].stepId).toBe('send_notification');
            // Verify variables were set correctly
            const variableStep = stepContexts[0];
            expect(variableStep.result.variables).toEqual({
                delayTime: 1000,
                notificationMessage: 'Delayed alert: System maintenance required',
            });
        });
    });
    describe('Error Handling and Recovery', () => {
        it('should handle step failures with retry mechanism', async () => {
            // Mock a failing notification provider
            const mockProvider = {
                sendNotification: jest.fn()
                    .mockRejectedValueOnce(new Error('Network timeout'))
                    .mockRejectedValueOnce(new Error('Service unavailable'))
                    .mockResolvedValueOnce({ success: true, messageId: 'retry-success' }),
                validateConfiguration: jest.fn().mockReturnValue(true),
                getHealthStatus: jest.fn().mockResolvedValue({ healthy: false }),
            };
            // Create workflow with retry configuration
            const workflowData = {
                name: 'Retry Test Workflow',
                description: 'Test retry mechanism',
                definition: {
                    startStep: 'send_notification',
                    steps: [
                        {
                            id: 'send_notification',
                            type: 'notification',
                            config: {
                                channel: 'email',
                                message: 'Test retry message',
                                recipients: ['<EMAIL>'],
                            },
                            errorHandling: {
                                retry: {
                                    maxAttempts: 3,
                                    delay: 500,
                                },
                            },
                        },
                    ],
                },
                status: 'active',
            };
            const workflow = await workflowService.createWorkflow(workflowData, testUser);
            const executionData = {
                input: {
                    alert: { message: 'Retry test', severity: 'medium' },
                    user: testUser,
                },
            };
            const execution = await workflowService.executeWorkflow(workflow.id, executionData, testUser);
            // Wait for execution with retries
            await new Promise(resolve => setTimeout(resolve, 5000));
            const completedExecution = await executionRepository.findOne({
                where: { id: execution.executionId },
                relations: ['contexts'],
            });
            expect(completedExecution.status).toBe('completed');
            expect(completedExecution.stepsCompleted).toBe(1);
            // Verify retry attempts were made
            const stepContext = completedExecution.contexts[0];
            expect(stepContext.retryCount).toBe(2); // Failed twice, succeeded on third attempt
            expect(stepContext.status).toBe('completed');
        });
        it('should execute fallback step when main step fails', async () => {
            const workflowData = {
                name: 'Fallback Test Workflow',
                description: 'Test fallback mechanism',
                definition: {
                    startStep: 'primary_notification',
                    steps: [
                        {
                            id: 'primary_notification',
                            type: 'notification',
                            config: {
                                channel: 'invalid_channel', // This will fail
                                message: 'Primary notification',
                                recipients: ['<EMAIL>'],
                            },
                            errorHandling: {
                                fallbackStep: 'fallback_notification',
                            },
                        },
                        {
                            id: 'fallback_notification',
                            type: 'notification',
                            config: {
                                channel: 'email',
                                message: 'Fallback notification: {{input.alert.message}}',
                                recipients: ['<EMAIL>'],
                            },
                        },
                    ],
                },
                status: 'active',
            };
            const workflow = await workflowService.createWorkflow(workflowData, testUser);
            const executionData = {
                input: {
                    alert: { message: 'Fallback test', severity: 'medium' },
                    user: testUser,
                },
            };
            const execution = await workflowService.executeWorkflow(workflow.id, executionData, testUser);
            // Wait for execution
            await new Promise(resolve => setTimeout(resolve, 3000));
            const completedExecution = await executionRepository.findOne({
                where: { id: execution.executionId },
                relations: ['contexts'],
            });
            expect(completedExecution.status).toBe('completed');
            expect(completedExecution.stepsCompleted).toBe(2); // Primary failed, fallback succeeded
            // Verify primary step failed
            const primaryContext = completedExecution.contexts.find(ctx => ctx.stepId === 'primary_notification');
            expect(primaryContext.status).toBe('failed');
            // Verify fallback step succeeded
            const fallbackContext = completedExecution.contexts.find(ctx => ctx.stepId === 'fallback_notification');
            expect(fallbackContext.status).toBe('completed');
        });
    });
    describe('Performance and Load Testing', () => {
        it('should handle concurrent workflow executions', async () => {
            const workflowData = {
                name: 'Concurrent Test Workflow',
                description: 'Test concurrent execution',
                definition: {
                    startStep: 'send_notification',
                    steps: [
                        {
                            id: 'send_notification',
                            type: 'notification',
                            config: {
                                channel: 'email',
                                message: 'Concurrent test: {{input.alert.id}}',
                                recipients: ['<EMAIL>'],
                            },
                        },
                    ],
                },
                status: 'active',
            };
            const workflow = await workflowService.createWorkflow(workflowData, testUser);
            // Execute multiple workflows concurrently
            const concurrentExecutions = 10;
            const executionPromises = [];
            for (let i = 0; i < concurrentExecutions; i++) {
                const executionData = {
                    input: {
                        alert: { id: `alert-${i}`, message: `Concurrent test ${i}`, severity: 'medium' },
                        user: testUser,
                    },
                };
                executionPromises.push(workflowService.executeWorkflow(workflow.id, executionData, testUser));
            }
            const executions = await Promise.all(executionPromises);
            expect(executions).toHaveLength(concurrentExecutions);
            // Wait for all executions to complete
            await new Promise(resolve => setTimeout(resolve, 5000));
            // Verify all executions completed successfully
            const completedExecutions = await executionRepository.find({
                where: { workflowId: workflow.id },
            });
            expect(completedExecutions).toHaveLength(concurrentExecutions);
            completedExecutions.forEach(execution => {
                expect(execution.status).toBe('completed');
                expect(execution.stepsCompleted).toBe(1);
            });
        });
        it('should measure workflow execution performance', async () => {
            const workflowData = {
                name: 'Performance Test Workflow',
                description: 'Measure execution performance',
                definition: {
                    startStep: 'step1',
                    steps: [
                        { id: 'step1', type: 'variable', config: { variables: { test: 'value1' } }, nextStep: 'step2' },
                        { id: 'step2', type: 'condition', config: { condition: { field: 'variables.test', operator: 'eq', value: 'value1' } }, nextStep: 'step3' },
                        { id: 'step3', type: 'notification', config: { channel: 'email', message: 'Performance test', recipients: ['<EMAIL>'] } },
                    ],
                },
                status: 'active',
            };
            const workflow = await workflowService.createWorkflow(workflowData, testUser);
            const executionData = {
                input: {
                    alert: { message: 'Performance test', severity: 'medium' },
                    user: testUser,
                },
            };
            const startTime = Date.now();
            const execution = await workflowService.executeWorkflow(workflow.id, executionData, testUser);
            // Wait for execution
            await new Promise(resolve => setTimeout(resolve, 3000));
            const completedExecution = await executionRepository.findOne({
                where: { id: execution.executionId },
                relations: ['contexts'],
            });
            const totalExecutionTime = Date.now() - startTime;
            expect(completedExecution.status).toBe('completed');
            expect(completedExecution.duration).toBeDefined();
            expect(completedExecution.duration).toBeGreaterThan(0);
            expect(totalExecutionTime).toBeLessThan(10000); // Should complete within 10 seconds
            // Verify step-level performance metrics
            completedExecution.contexts.forEach(context => {
                expect(context.duration).toBeDefined();
                expect(context.duration).toBeGreaterThan(0);
                expect(context.startedAt).toBeDefined();
                expect(context.completedAt).toBeDefined();
            });
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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