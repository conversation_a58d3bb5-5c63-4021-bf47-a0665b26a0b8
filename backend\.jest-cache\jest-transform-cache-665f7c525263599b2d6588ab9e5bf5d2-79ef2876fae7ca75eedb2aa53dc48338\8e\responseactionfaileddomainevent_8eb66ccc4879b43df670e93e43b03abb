b66cf4b071235dd352d5f873a2c5eeee
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseActionFailedDomainEvent = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
const action_type_enum_1 = require("../enums/action-type.enum");
/**
 * Response Action Failed Domain Event
 *
 * Raised when a response action execution fails.
 * This event indicates that the action could not be completed successfully
 * and may require intervention, retry, or alternative approaches.
 *
 * Key information:
 * - Action type and failure details
 * - Error information and context
 * - Retry capabilities and count
 * - Failure timestamp and executor
 *
 * Use cases:
 * - Trigger failure handling workflows
 * - Notify stakeholders of action failures
 * - Initiate retry mechanisms
 * - Escalate critical action failures
 * - Update monitoring and alerting systems
 * - Generate failure reports and analytics
 */
class ResponseActionFailedDomainEvent extends shared_kernel_1.BaseDomainEvent {
    constructor(aggregateId, eventData, options) {
        super(aggregateId, eventData, options);
    }
    /**
     * Get the action type that failed
     */
    get actionType() {
        return this.eventData.actionType;
    }
    /**
     * Get the failure error message
     */
    get error() {
        return this.eventData.error;
    }
    /**
     * Get who was executing the action
     */
    get executedBy() {
        return this.eventData.executedBy;
    }
    /**
     * Get when the action failed
     */
    get failedAt() {
        return this.eventData.failedAt;
    }
    /**
     * Get the current retry count
     */
    get retryCount() {
        return this.eventData.retryCount;
    }
    /**
     * Check if the action can be retried
     */
    get canRetry() {
        return this.eventData.canRetry;
    }
    /**
     * Check if this is a first-time failure
     */
    isFirstFailure() {
        return this.eventData.retryCount === 0;
    }
    /**
     * Check if this is a repeated failure
     */
    isRepeatedFailure() {
        return this.eventData.retryCount > 0;
    }
    /**
     * Check if this is a final failure (no more retries)
     */
    isFinalFailure() {
        return !this.eventData.canRetry;
    }
    /**
     * Check if this was an automated execution failure
     */
    isAutomatedFailure() {
        return this.eventData.executedBy?.includes('system') ||
            this.eventData.executedBy?.includes('automation') ||
            this.eventData.executedBy?.includes('bot') ||
            false;
    }
    /**
     * Check if this was a manual execution failure
     */
    isManualFailure() {
        return this.eventData.executedBy !== undefined && !this.isAutomatedFailure();
    }
    /**
     * Check if this is a security-critical action failure
     */
    isSecurityCriticalFailure() {
        const criticalActions = [
            action_type_enum_1.ActionType.ISOLATE_SYSTEM,
            action_type_enum_1.ActionType.SHUTDOWN_SYSTEM,
            action_type_enum_1.ActionType.DELETE_FILE,
            action_type_enum_1.ActionType.REMOVE_MALWARE,
            action_type_enum_1.ActionType.DISABLE_ACCOUNT,
            action_type_enum_1.ActionType.BLOCK_IP,
            action_type_enum_1.ActionType.QUARANTINE_FILE,
            action_type_enum_1.ActionType.UPDATE_FIREWALL,
            action_type_enum_1.ActionType.PATCH_VULNERABILITY,
        ];
        return criticalActions.includes(this.eventData.actionType);
    }
    /**
     * Check if this is a containment action failure
     */
    isContainmentFailure() {
        const containmentActions = [
            action_type_enum_1.ActionType.ISOLATE_SYSTEM,
            action_type_enum_1.ActionType.QUARANTINE_FILE,
            action_type_enum_1.ActionType.BLOCK_IP,
            action_type_enum_1.ActionType.BLOCK_DOMAIN,
            action_type_enum_1.ActionType.BLOCK_URL,
            action_type_enum_1.ActionType.DISABLE_ACCOUNT,
            action_type_enum_1.ActionType.REVOKE_TOKEN,
            action_type_enum_1.ActionType.TERMINATE_CONNECTION,
            action_type_enum_1.ActionType.SHUTDOWN_SYSTEM,
        ];
        return containmentActions.includes(this.eventData.actionType);
    }
    /**
     * Check if this is a recovery action failure
     */
    isRecoveryFailure() {
        const recoveryActions = [
            action_type_enum_1.ActionType.RESTORE_BACKUP,
            action_type_enum_1.ActionType.REBUILD_SYSTEM,
            action_type_enum_1.ActionType.RESTORE_NETWORK,
            action_type_enum_1.ActionType.ENABLE_SERVICE,
            action_type_enum_1.ActionType.RESET_PASSWORD,
            action_type_enum_1.ActionType.REGENERATE_CERTIFICATE,
        ];
        return recoveryActions.includes(this.eventData.actionType);
    }
    /**
     * Check if this is a notification action failure
     */
    isNotificationFailure() {
        const notificationActions = [
            action_type_enum_1.ActionType.SEND_EMAIL,
            action_type_enum_1.ActionType.SEND_SMS,
            action_type_enum_1.ActionType.SEND_SLACK,
            action_type_enum_1.ActionType.TRIGGER_WEBHOOK,
            action_type_enum_1.ActionType.PAGE_ONCALL,
            action_type_enum_1.ActionType.CREATE_TICKET,
            action_type_enum_1.ActionType.UPDATE_INCIDENT,
        ];
        return notificationActions.includes(this.eventData.actionType);
    }
    /**
     * Get failure severity level
     */
    getFailureSeverity() {
        if (this.isSecurityCriticalFailure()) {
            return this.isFinalFailure() ? 'critical' : 'high';
        }
        if (this.isContainmentFailure()) {
            return this.isFinalFailure() ? 'high' : 'medium';
        }
        if (this.isRecoveryFailure()) {
            return this.isFinalFailure() ? 'high' : 'medium';
        }
        if (this.isNotificationFailure()) {
            return 'medium';
        }
        return 'low';
    }
    /**
     * Get failure category
     */
    getFailureCategory() {
        const errorLower = this.eventData.error.toLowerCase();
        if (errorLower.includes('permission') || errorLower.includes('unauthorized') || errorLower.includes('forbidden')) {
            return 'permission';
        }
        if (errorLower.includes('timeout') || errorLower.includes('timed out')) {
            return 'timeout';
        }
        if (errorLower.includes('resource') || errorLower.includes('memory') || errorLower.includes('disk')) {
            return 'resource';
        }
        if (errorLower.includes('config') || errorLower.includes('setting') || errorLower.includes('parameter')) {
            return 'configuration';
        }
        if (errorLower.includes('network') || errorLower.includes('connection') || errorLower.includes('dns')) {
            return 'network';
        }
        if (errorLower.includes('exception') || errorLower.includes('error') || errorLower.includes('failed')) {
            return 'technical';
        }
        return 'unknown';
    }
    /**
     * Get recommended immediate actions
     */
    getImmediateActions() {
        const actions = [];
        if (this.isSecurityCriticalFailure()) {
            actions.push('Escalate to incident response team');
            actions.push('Assess security impact of failure');
            actions.push('Consider manual fallback procedures');
        }
        if (this.isContainmentFailure()) {
            actions.push('Implement alternative containment measures');
            actions.push('Monitor for threat spread');
            actions.push('Assess containment gap impact');
        }
        if (this.isRecoveryFailure()) {
            actions.push('Assess service impact');
            actions.push('Consider alternative recovery methods');
            actions.push('Notify affected stakeholders');
        }
        if (this.canRetry) {
            actions.push('Analyze failure cause before retry');
            actions.push('Adjust retry parameters if needed');
        }
        else {
            actions.push('Investigate root cause of failure');
            actions.push('Consider alternative action approaches');
        }
        // Category-specific actions
        switch (this.getFailureCategory()) {
            case 'permission':
                actions.push('Verify action permissions and credentials');
                actions.push('Check role-based access controls');
                break;
            case 'timeout':
                actions.push('Check system performance and load');
                actions.push('Consider increasing timeout limits');
                break;
            case 'resource':
                actions.push('Check system resource availability');
                actions.push('Free up resources if possible');
                break;
            case 'configuration':
                actions.push('Verify action configuration parameters');
                actions.push('Check system configuration settings');
                break;
            case 'network':
                actions.push('Check network connectivity');
                actions.push('Verify network configuration');
                break;
        }
        return actions;
    }
    /**
     * Get stakeholders to notify
     */
    getNotificationTargets() {
        const targets = [];
        // Always notify the action requestor
        targets.push('action-requestor');
        if (this.isSecurityCriticalFailure()) {
            targets.push('security-team');
            targets.push('incident-response-team');
            if (this.isFinalFailure()) {
                targets.push('security-managers');
                targets.push('on-call-engineers');
            }
        }
        if (this.isContainmentFailure()) {
            targets.push('containment-specialists');
            targets.push('security-analysts');
        }
        if (this.isRecoveryFailure()) {
            targets.push('recovery-team');
            targets.push('service-owners');
        }
        if (this.isNotificationFailure()) {
            targets.push('communication-team');
        }
        if (this.isRepeatedFailure()) {
            targets.push('technical-support');
        }
        if (this.isFinalFailure()) {
            targets.push('escalation-team');
        }
        return targets;
    }
    /**
     * Get escalation requirements
     */
    getEscalationRequirements() {
        if (this.isSecurityCriticalFailure() && this.isFinalFailure()) {
            return {
                immediate: true,
                level: 'executive',
                reason: 'Critical security action failed with no retry options'
            };
        }
        if (this.isSecurityCriticalFailure()) {
            return {
                immediate: true,
                level: 'management',
                reason: 'Critical security action failed'
            };
        }
        if (this.isContainmentFailure() && this.isFinalFailure()) {
            return {
                immediate: true,
                level: 'management',
                reason: 'Containment action failed - threat may spread'
            };
        }
        if (this.isRepeatedFailure() && this.getFailureSeverity() === 'high') {
            return {
                immediate: false,
                level: 'technical',
                reason: 'Repeated high-severity action failure'
            };
        }
        return {
            immediate: false,
            level: 'none',
            reason: 'Standard failure handling applies'
        };
    }
    /**
     * Get retry recommendations
     */
    getRetryRecommendations() {
        if (!this.canRetry) {
            return {
                shouldRetry: false,
                delayMinutes: 0,
                maxRetries: 0,
                conditions: ['Action cannot be retried']
            };
        }
        const category = this.getFailureCategory();
        const conditions = [];
        let delayMinutes = 5;
        let maxRetries = 3;
        switch (category) {
            case 'timeout':
                delayMinutes = 10;
                maxRetries = 2;
                conditions.push('Verify system performance before retry');
                break;
            case 'resource':
                delayMinutes = 15;
                maxRetries = 2;
                conditions.push('Ensure sufficient resources available');
                break;
            case 'network':
                delayMinutes = 5;
                maxRetries = 3;
                conditions.push('Verify network connectivity');
                break;
            case 'permission':
                delayMinutes = 0;
                maxRetries = 1;
                conditions.push('Fix permission issues before retry');
                break;
            case 'configuration':
                delayMinutes = 0;
                maxRetries = 1;
                conditions.push('Correct configuration before retry');
                break;
            default:
                conditions.push('Analyze failure cause before retry');
        }
        if (this.isSecurityCriticalFailure()) {
            conditions.push('Get approval for critical action retry');
        }
        return {
            shouldRetry: true,
            delayMinutes,
            maxRetries,
            conditions
        };
    }
    /**
     * Get failure metrics
     */
    getFailureMetrics() {
        return {
            actionType: this.eventData.actionType,
            failureCategory: this.getFailureCategory(),
            failureSeverity: this.getFailureSeverity(),
            retryCount: this.eventData.retryCount,
            canRetry: this.eventData.canRetry,
            isSecurityCritical: this.isSecurityCriticalFailure(),
            isAutomated: this.isAutomatedFailure(),
            isFinalFailure: this.isFinalFailure(),
        };
    }
    /**
     * Convert to integration event format
     */
    toIntegrationEvent() {
        const escalation = this.getEscalationRequirements();
        return {
            eventType: 'ResponseActionFailed',
            action: 'response_action_failed',
            resource: 'ResponseAction',
            resourceId: this.aggregateId.toString(),
            data: this.eventData,
            metadata: {
                failureSeverity: this.getFailureSeverity(),
                failureCategory: this.getFailureCategory(),
                isSecurityCritical: this.isSecurityCriticalFailure(),
                isFinalFailure: this.isFinalFailure(),
                escalationRequired: escalation.immediate,
                canRetry: this.eventData.canRetry,
            },
        };
    }
}
exports.ResponseActionFailedDomainEvent = ResponseActionFailedDomainEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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