{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\services\\report-definition.service.ts", "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAwG;AACxG,6CAAmD;AACnD,qCAAgE;AAChE,yDAAsD;AACtD,mFAA+E;AAC/E,uGAAkG;AAClG,mFAAwE;AAGxE,yFAAoF;AAEpF;;;;;;;;;;;GAWG;AAEI,IAAM,uBAAuB,GAA7B,MAAM,uBAAuB;IAGlC,YAEE,0BAAyE,EACxD,YAA2B,EAC3B,YAAqC,EACrC,eAAgC;QAHhC,+BAA0B,GAA1B,0BAA0B,CAA8B;QACxD,iBAAY,GAAZ,YAAY,CAAe;QAC3B,iBAAY,GAAZ,YAAY,CAAyB;QACrC,oBAAe,GAAf,eAAe,CAAiB;QAPlC,WAAM,GAAG,IAAI,8BAAa,CAAC,yBAAyB,CAAC,CAAC;IAQpE,CAAC;IAEJ;;OAEG;IACH,KAAK,CAAC,sBAAsB,CAC1B,SAAoC,EACpC,MAAc;QAEd,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gCAAgC,EAAE;gBAChD,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,UAAU,EAAE,SAAS,CAAC,UAAU;gBAChC,MAAM;aACP,CAAC,CAAC;YAEH,+CAA+C;YAC/C,MAAM,IAAI,CAAC,4BAA4B,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YAEhE,yBAAyB;YACzB,MAAM,gBAAgB,GAAG,IAAI,CAAC,2BAA2B,CAAC,SAAS,CAAC,CAAC;YACrE,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;gBAC9B,MAAM,IAAI,4BAAmB,CAAC,oCAAoC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC1G,CAAC;YAED,kCAAkC;YAClC,MAAM,gBAAgB,GAAG,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC;gBAC9D,GAAG,SAAS;gBACZ,OAAO,EAAE,MAAM;gBACf,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE;oBACR,IAAI,EAAE,SAAS,CAAC,IAAI,IAAI,EAAE;oBAC1B,OAAO,EAAE,KAAK;oBACd,cAAc,EAAE,MAAM;oBACtB,SAAS,EAAE,CAAC;4BACV,OAAO,EAAE,KAAK;4BACd,OAAO,EAAE,CAAC,kBAAkB,CAAC;4BAC7B,UAAU,EAAE,MAAM;4BAClB,UAAU,EAAE,IAAI,IAAI,EAAE;yBACvB,CAAC;oBACF,KAAK,EAAE;wBACL,cAAc,EAAE,CAAC;wBACjB,YAAY,EAAE,IAAI;wBAClB,oBAAoB,EAAE,CAAC;wBACvB,eAAe,EAAE,CAAC;qBACnB;oBACD,UAAU,EAAE;wBACV,UAAU,EAAE,SAAS,CAAC,oBAAoB,IAAI,EAAE;wBAChD,kBAAkB,EAAE,SAAS,CAAC,kBAAkB,IAAI,UAAU;wBAC9D,eAAe,EAAE,SAAS,CAAC,eAAe,IAAI,GAAG;wBACjD,aAAa,EAAE,SAAS,CAAC,aAAa,IAAI,KAAK;qBAChD;iBACF;aACF,CAAC,CAAC;YAEH,mBAAmB;YACnB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAEjF,8BAA8B;YAC9B,MAAM,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAC;YAE9C,kBAAkB;YAClB,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;gBACxC,SAAS,EAAE,oBAAoB;gBAC/B,QAAQ,EAAE,WAAW,CAAC,EAAE;gBACxB,MAAM,EAAE,2BAA2B;gBACnC,MAAM;gBACN,SAAS,EAAE;oBACT,IAAI,EAAE,WAAW,CAAC,IAAI;oBACtB,UAAU,EAAE,WAAW,CAAC,UAAU;oBAClC,QAAQ,EAAE,WAAW,CAAC,QAAQ;oBAC9B,UAAU,EAAE,WAAW,CAAC,UAAU;iBACnC;gBACD,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,KAAK;gBAChB,oBAAoB,EAAE,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU;gBAChE,qBAAqB,EAAE,4BAA4B;aACpD,CAAC,CAAC;YAEH,sBAAsB;YACtB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,8BAA8B,EAAE;gBACrD,QAAQ,EAAE,WAAW,CAAC,EAAE;gBACxB,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,UAAU,EAAE,WAAW,CAAC,UAAU;gBAClC,MAAM;gBACN,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wCAAwC,EAAE;gBACxD,QAAQ,EAAE,WAAW,CAAC,EAAE;gBACxB,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,WAAW,CAAC;QAErB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE;gBACtD,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAC3B,EAAU,EACV,MAAc,EACd,YAAsB,EAAE;QAExB,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YAE5E,kBAAkB;YAClB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,EAAE,CAAC,CAAC;YAC9D,IAAI,YAAY,EAAE,CAAC;gBACjB,kBAAkB;gBAClB,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,EAAE,CAAC;oBAC3D,MAAM,IAAI,2BAAkB,CAAC,8CAA8C,CAAC,CAAC;gBAC/E,CAAC;gBACD,OAAO,YAAY,CAAC;YACtB,CAAC;YAED,sBAAsB;YACtB,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC;gBACrE,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;gBAC7B,SAAS,EAAE,CAAC,YAAY,CAAC;aAC1B,CAAC,CAAC;YAEH,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACtB,MAAM,IAAI,0BAAiB,CAAC,6BAA6B,EAAE,YAAY,CAAC,CAAC;YAC3E,CAAC;YAED,kBAAkB;YAClB,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,EAAE,CAAC;gBAC/D,MAAM,IAAI,2BAAkB,CAAC,8CAA8C,CAAC,CAAC;YAC/E,CAAC;YAED,mBAAmB;YACnB,MAAM,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,CAAC;YAEnD,OAAO,gBAAgB,CAAC;QAE1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE;gBACxD,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,QAAQ,EAAE,EAAE;gBACZ,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACxB,OASC,EACD,MAAc,EACd,YAAsB,EAAE;QAQxB,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4CAA4C,EAAE;gBAC9D,OAAO;gBACP,MAAM;aACP,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC;YAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,mBAAmB;YACrE,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAElC,cAAc;YACd,MAAM,YAAY,GAAG,IAAI,CAAC,0BAA0B,CAAC,kBAAkB,CAAC,QAAQ,CAAC;iBAC9E,KAAK,CAAC,6BAA6B,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;YAE5D,gBAAgB;YAChB,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;gBACvB,YAAY,CAAC,QAAQ,CAAC,iCAAiC,EAAE,EAAE,UAAU,EAAE,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;YAC/F,CAAC;YAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACrB,YAAY,CAAC,QAAQ,CAAC,6BAA6B,EAAE,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;YACvF,CAAC;YAED,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;gBACpB,YAAY,CAAC,QAAQ,CAAC,2BAA2B,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;YACnF,CAAC;YAED,IAAI,OAAO,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;gBACrC,YAAY,CAAC,QAAQ,CAAC,iCAAiC,EAAE,EAAE,UAAU,EAAE,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;YAC/F,CAAC;YAED,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5C,YAAY,CAAC,QAAQ,CAAC,0BAA0B,EAAE;oBAChD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC;iBAC7C,CAAC,CAAC;YACL,CAAC;YAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;gBACnB,YAAY,CAAC,QAAQ,CACnB,iEAAiE,EACjE,EAAE,MAAM,EAAE,IAAI,OAAO,CAAC,MAAM,GAAG,EAAE,CAClC,CAAC;YACJ,CAAC;YAED,yDAAyD;YACzD,YAAY,CAAC,QAAQ,CACnB,+BAA+B;gBAC/B,2CAA2C;gBAC3C,yCAAyC;gBACzC,sCAAsC,EACtC;gBACE,MAAM;gBACN,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC;gBACtD,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,YAAY,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC;gBACtD,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,YAAY,EAAE,SAAS,EAAE,CAAC;aACxD,CACF,CAAC;YAEF,wCAAwC;YACxC,YAAY,CAAC,OAAO,CAAC,mDAAmD,EAAE,MAAM,CAAC;iBAC9E,UAAU,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;YAE1C,kBAAkB;YAClB,MAAM,KAAK,GAAG,MAAM,YAAY,CAAC,QAAQ,EAAE,CAAC;YAE5C,mBAAmB;YACnB,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAEzC,gBAAgB;YAChB,MAAM,OAAO,GAAG,MAAM,YAAY,CAAC,OAAO,EAAE,CAAC;YAE7C,6EAA6E;YAC7E,MAAM,iBAAiB,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAChD,MAAM,CAAC,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,CAChD,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;gBAChD,KAAK;gBACL,QAAQ,EAAE,iBAAiB,CAAC,MAAM;gBAClC,IAAI;gBACJ,MAAM;aACP,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,iBAAiB;gBAC1B,KAAK;gBACL,IAAI;gBACJ,KAAK;gBACL,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;aACrC,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE;gBACzD,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,OAAO;gBACP,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CAC1B,EAAU,EACV,SAAoC,EACpC,MAAc,EACd,YAAsB,EAAE;QAExB,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YAExE,sBAAsB;YACtB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;YAEjF,wBAAwB;YACxB,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,EAAE,CAAC;gBAC7D,MAAM,IAAI,2BAAkB,CAAC,8CAA8C,CAAC,CAAC;YAC/E,CAAC;YAED,oDAAoD;YACpD,IAAI,SAAS,CAAC,IAAI,IAAI,SAAS,CAAC,IAAI,KAAK,cAAc,CAAC,IAAI,EAAE,CAAC;gBAC7D,MAAM,IAAI,CAAC,4BAA4B,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;YACtE,CAAC;YAED,qCAAqC;YACrC,IAAI,SAAS,CAAC,gBAAgB,IAAI,SAAS,CAAC,mBAAmB,IAAI,SAAS,CAAC,YAAY,EAAE,CAAC;gBAC1F,MAAM,gBAAgB,GAAG;oBACvB,GAAG,cAAc;oBACjB,GAAG,SAAS;iBACb,CAAC;gBACF,MAAM,gBAAgB,GAAG,IAAI,CAAC,2BAA2B,CAAC,gBAAgB,CAAC,CAAC;gBAC5E,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;oBAC9B,MAAM,IAAI,4BAAmB,CAAC,oCAAoC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC1G,CAAC;YACH,CAAC;YAED,kBAAkB;YAClB,MAAM,eAAe,GAAG;gBACtB,GAAG,cAAc,CAAC,QAAQ;gBAC1B,cAAc,EAAE,MAAM;gBACtB,SAAS,EAAE;oBACT,GAAG,cAAc,CAAC,QAAQ,CAAC,SAAS;oBACpC;wBACE,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC;wBAC/D,OAAO,EAAE,IAAI,CAAC,yBAAyB,CAAC,cAAc,EAAE,SAAS,CAAC;wBAClE,UAAU,EAAE,MAAM;wBAClB,UAAU,EAAE,IAAI,IAAI,EAAE;qBACvB;iBACF;aACF,CAAC;YAEF,gBAAgB;YAChB,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC;gBAC/D,GAAG,cAAc;gBACjB,GAAG,SAAS;gBACZ,QAAQ,EAAE,eAAe;gBACzB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,eAAe;YACf,MAAM,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;YAEhD,kBAAkB;YAClB,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;gBACxC,SAAS,EAAE,oBAAoB;gBAC/B,QAAQ,EAAE,EAAE;gBACZ,MAAM,EAAE,2BAA2B;gBACnC,MAAM;gBACN,SAAS,EAAE,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC;gBACtD,SAAS,EAAE,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC;gBACrD,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,KAAK;gBAChB,oBAAoB,EAAE,aAAa,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU;gBAClE,qBAAqB,EAAE,0BAA0B;aAClD,CAAC,CAAC;YAEH,oBAAoB;YACpB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,8BAA8B,EAAE;gBACrD,QAAQ,EAAE,EAAE;gBACZ,IAAI,EAAE,aAAa,CAAC,IAAI;gBACxB,OAAO,EAAE,eAAe,CAAC,SAAS,CAAC,eAAe,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,OAAO;gBAChF,MAAM;gBACN,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wCAAwC,EAAE;gBACxD,QAAQ,EAAE,EAAE;gBACZ,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,aAAa,CAAC;QAEvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE;gBACtD,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,QAAQ,EAAE,EAAE;gBACZ,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CAC1B,EAAU,EACV,MAAc,EACd,YAAsB,EAAE;QAExB,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YAExE,sBAAsB;YACtB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;YAEjF,0BAA0B;YAC1B,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE,CAAC;gBAC/D,MAAM,IAAI,2BAAkB,CAAC,gDAAgD,CAAC,CAAC;YACjF,CAAC;YAED,wCAAwC;YACxC,MAAM,gBAAgB,GAAG,cAAc,CAAC,UAAU,EAAE,MAAM,CACxD,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,KAAK,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,SAAS,CAC9E,CAAC;YAEF,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpD,MAAM,IAAI,4BAAmB,CAAC,6CAA6C,CAAC,CAAC;YAC/E,CAAC;YAED,cAAc;YACd,MAAM,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,EAAE,EAAE;gBAC/C,QAAQ,EAAE,KAAK;gBACf,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,oBAAoB;YACpB,MAAM,IAAI,CAAC,4BAA4B,CAAC,EAAE,CAAC,CAAC;YAE5C,kBAAkB;YAClB,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;gBACxC,SAAS,EAAE,oBAAoB;gBAC/B,QAAQ,EAAE,EAAE;gBACZ,MAAM,EAAE,2BAA2B;gBACnC,MAAM;gBACN,SAAS,EAAE,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC;gBACtD,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,QAAQ;gBACnB,oBAAoB,EAAE,cAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU;gBACnE,qBAAqB,EAAE,4BAA4B;aACpD,CAAC,CAAC;YAEH,sBAAsB;YACtB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,8BAA8B,EAAE;gBACrD,QAAQ,EAAE,EAAE;gBACZ,IAAI,EAAE,cAAc,CAAC,IAAI;gBACzB,MAAM;gBACN,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wCAAwC,EAAE;gBACxD,QAAQ,EAAE,EAAE;gBACZ,MAAM;aACP,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE;gBACtD,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,QAAQ,EAAE,EAAE;gBACZ,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACzB,UAAkB,EAClB,OAAe,EACf,MAAc,EACd,YAAsB,EAAE;QAExB,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,yCAAyC,EAAE;gBACzD,UAAU;gBACV,OAAO;gBACP,MAAM;aACP,CAAC,CAAC;YAEH,eAAe;YACf,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;YAEnF,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;gBACzB,MAAM,IAAI,4BAAmB,CAAC,iCAAiC,CAAC,CAAC;YACnE,CAAC;YAED,+BAA+B;YAC/B,MAAM,IAAI,CAAC,4BAA4B,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAEzD,qBAAqB;YACrB,MAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAEnD,oBAAoB;YACpB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAE5E,uBAAuB;YACvB,MAAM,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;YAE/C,kBAAkB;YAClB,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;gBACxC,SAAS,EAAE,oBAAoB;gBAC/B,QAAQ,EAAE,YAAY,CAAC,EAAE;gBACzB,MAAM,EAAE,0BAA0B;gBAClC,MAAM;gBACN,SAAS,EAAE;oBACT,IAAI,EAAE,YAAY,CAAC,IAAI;oBACvB,UAAU;oBACV,UAAU,EAAE,QAAQ,CAAC,IAAI;iBAC1B;gBACD,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,KAAK;gBAChB,oBAAoB,EAAE,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU;gBACjE,qBAAqB,EAAE,yCAAyC;aACjE,CAAC,CAAC;YAEH,qBAAqB;YACrB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,6BAA6B,EAAE;gBACpD,QAAQ,EAAE,YAAY,CAAC,EAAE;gBACzB,UAAU;gBACV,IAAI,EAAE,YAAY,CAAC,IAAI;gBACvB,MAAM;gBACN,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uCAAuC,EAAE;gBACvD,QAAQ,EAAE,YAAY,CAAC,EAAE;gBACzB,UAAU;gBACV,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,YAAY,CAAC;QAEtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE;gBACrD,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,UAAU;gBACV,OAAO;gBACP,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,8BAA8B,CAClC,EAAU,EACV,MAAc,EACd,YAAsB,EAAE;QAOxB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;YAEzE,MAAM,eAAe,GAAG;gBACtB,WAAW,EAAE,EAAE;gBACf,QAAQ,EAAE,EAAE;gBACZ,SAAS,EAAE,EAAE;gBACb,UAAU,EAAE,EAAE;aACf,CAAC;YAEF,8BAA8B;YAC9B,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;gBAC9C,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;YAC9F,CAAC;YAED,IAAI,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC/C,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,qEAAqE,CAAC,CAAC;YAC1G,CAAC;YAED,IAAI,MAAM,CAAC,iBAAiB,CAAC,YAAY,CAAC,SAAS,GAAG,KAAK,EAAE,CAAC;gBAC5D,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;YAC9F,CAAC;YAED,2BAA2B;YAC3B,IAAI,MAAM,CAAC,aAAa,CAAC,UAAU,KAAK,QAAQ;gBAC5C,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,kBAAkB,KAAK,QAAQ,EAAE,CAAC;gBAC/D,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;YAC/F,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,aAAa;gBACzC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrD,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC;YAClG,CAAC;YAED,4BAA4B;YAC5B,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;gBACrD,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;YAC3F,CAAC;YAED,IAAI,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACjD,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;YAC5F,CAAC;YAED,6BAA6B;YAC7B,IAAI,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC;gBAChD,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,eAAe,GAAG,IAAI,EAAE,CAAC;gBACtD,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;YACnG,CAAC;YAED,OAAO,eAAe,CAAC;QAEzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4CAA4C,EAAE;gBAC9D,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,QAAQ,EAAE,EAAE;gBACZ,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,yBAAyB;IAEjB,KAAK,CAAC,4BAA4B,CACxC,IAAY,EACZ,MAAc,EACd,SAAkB;QAElB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC;YACnE,KAAK,EAAE;gBACL,IAAI;gBACJ,OAAO,EAAE,MAAM;gBACf,QAAQ,EAAE,IAAI;gBACd,GAAG,CAAC,SAAS,IAAI,EAAE,EAAE,EAAE,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;aACzC;SACF,CAAC,CAAC;QAEH,IAAI,cAAc,EAAE,CAAC;YACnB,MAAM,IAAI,4BAAmB,CAAC,qBAAqB,IAAI,kBAAkB,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;IAEO,2BAA2B,CAAC,MAAW;QAC7C,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,wBAAwB;QACxB,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,OAAO,IAAI,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtF,MAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QAC7D,CAAC;QAED,yBAAyB;QACzB,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,SAAS,EAAE,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,MAAM,IAAI,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1F,MAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QAC7D,CAAC;QAED,gCAAgC;QAChC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,OAAO,IAAI,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9E,MAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QAC9D,CAAC;QAED,0BAA0B;QAC1B,IAAI,MAAM,CAAC,aAAa,EAAE,UAAU,KAAK,QAAQ;YAC7C,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,YAAY,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,YAAY,EAAE,MAAM,CAAC,EAAE,CAAC;YAC/F,MAAM,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QACpE,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACP,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,MAAwB;QAC1D,MAAM,QAAQ,GAAG,qBAAqB,MAAM,CAAC,EAAE,EAAE,CAAC;QAClD,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS;IACzE,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,EAAU;QAChD,MAAM,QAAQ,GAAG,qBAAqB,EAAE,EAAE,CAAC;QAC3C,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACzC,CAAC;IAEO,KAAK,CAAC,4BAA4B,CAAC,EAAU;QACnD,MAAM,QAAQ,GAAG,qBAAqB,EAAE,EAAE,CAAC;QAC3C,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;IAEO,gBAAgB,CAAC,cAAsB;QAC7C,MAAM,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACxC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QAC5C,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE,CAAC;IAC5C,CAAC;IAEO,yBAAyB,CAAC,SAA2B,EAAE,SAAc;QAC3E,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,SAAS,CAAC,IAAI,IAAI,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;YACxD,OAAO,CAAC,IAAI,CAAC,sBAAsB,SAAS,CAAC,IAAI,SAAS,SAAS,CAAC,IAAI,GAAG,CAAC,CAAC;QAC/E,CAAC;QAED,IAAI,SAAS,CAAC,WAAW,KAAK,SAAS,IAAI,SAAS,CAAC,WAAW,KAAK,SAAS,CAAC,WAAW,EAAE,CAAC;YAC3F,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,SAAS,CAAC,gBAAgB,EAAE,CAAC;YAC/B,OAAO,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,SAAS,CAAC,mBAAmB,EAAE,CAAC;YAClC,OAAO,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,SAAS,CAAC,YAAY,EAAE,CAAC;YAC3B,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,SAAS,CAAC,aAAa,EAAE,CAAC;YAC5B,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,SAAS,CAAC,gBAAgB,EAAE,CAAC;YAC/B,OAAO,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,SAAS,CAAC,iBAAiB,EAAE,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QACpD,CAAC;QAED,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;IAC1D,CAAC;IAEO,sBAAsB,CAAC,MAAwB;QACrD,OAAO;YACL,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,UAAU,EAAE,MAAM,CAAC,UAAU;YAC7B,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,UAAU,EAAE,MAAM,CAAC,UAAU;YAC7B,UAAU,EAAE,MAAM,CAAC,aAAa,CAAC,UAAU;YAC3C,kBAAkB,EAAE,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,kBAAkB;SAClE,CAAC;IACJ,CAAC;CACF,CAAA;AA1uBY,0DAAuB;kCAAvB,uBAAuB;IADnC,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,2CAAgB,CAAC,CAAA;yDACU,oBAAU,oBAAV,oBAAU,oDACxB,6BAAa,oBAAb,6BAAa,oDACb,mDAAuB,oBAAvB,mDAAuB,oDACpB,mCAAe,oBAAf,mCAAe;GARxC,uBAAuB,CA0uBnC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\services\\report-definition.service.ts"], "sourcesContent": ["import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository, FindManyOptions, Like, In } from 'typeorm';\r\nimport { EventEmitter2 } from '@nestjs/event-emitter';\r\nimport { LoggerService } from '../../../infrastructure/logging/logger.service';\r\nimport { DistributedCacheService } from '../../../infrastructure/cache/distributed-cache.service';\r\nimport { ReportDefinition } from '../entities/report-definition.entity';\r\nimport { CreateReportDefinitionDto } from '../dto/create-report-definition.dto';\r\nimport { UpdateReportDefinitionDto } from '../dto/update-report-definition.dto';\r\nimport { AuditLogService } from '../../compliance-audit/services/audit-log.service';\r\n\r\n/**\r\n * Report Definition Service\r\n * \r\n * Provides comprehensive report definition management including:\r\n * - CRUD operations with validation and access control\r\n * - Template management and cloning capabilities\r\n * - Configuration validation and optimization recommendations\r\n * - Usage statistics tracking and popularity scoring\r\n * - Integration with compliance and audit systems\r\n * - Performance optimization with caching strategies\r\n * - User permission validation and data filtering\r\n */\r\n@Injectable()\r\nexport class ReportDefinitionService {\r\n  private readonly logger = new LoggerService('ReportDefinitionService');\r\n\r\n  constructor(\r\n    @InjectRepository(ReportDefinition)\r\n    private readonly reportDefinitionRepository: Repository<ReportDefinition>,\r\n    private readonly eventEmitter: EventEmitter2,\r\n    private readonly cacheService: DistributedCacheService,\r\n    private readonly auditLogService: AuditLogService,\r\n  ) {}\r\n\r\n  /**\r\n   * Create a new report definition\r\n   */\r\n  async createReportDefinition(\r\n    createDto: CreateReportDefinitionDto,\r\n    userId: string\r\n  ): Promise<ReportDefinition> {\r\n    try {\r\n      this.logger.log('Creating new report definition', {\r\n        name: createDto.name,\r\n        reportType: createDto.reportType,\r\n        userId,\r\n      });\r\n\r\n      // Validate report name uniqueness for the user\r\n      await this.validateReportNameUniqueness(createDto.name, userId);\r\n\r\n      // Validate configuration\r\n      const validationResult = this.validateReportConfiguration(createDto);\r\n      if (!validationResult.isValid) {\r\n        throw new BadRequestException(`Configuration validation failed: ${validationResult.errors.join(', ')}`);\r\n      }\r\n\r\n      // Create report definition entity\r\n      const reportDefinition = this.reportDefinitionRepository.create({\r\n        ...createDto,\r\n        ownerId: userId,\r\n        isActive: true,\r\n        metadata: {\r\n          tags: createDto.tags || [],\r\n          version: '1.0',\r\n          lastModifiedBy: userId,\r\n          changeLog: [{\r\n            version: '1.0',\r\n            changes: ['Initial creation'],\r\n            modifiedBy: userId,\r\n            modifiedAt: new Date(),\r\n          }],\r\n          usage: {\r\n            executionCount: 0,\r\n            lastExecuted: null,\r\n            averageExecutionTime: 0,\r\n            popularityScore: 0,\r\n          },\r\n          compliance: {\r\n            frameworks: createDto.complianceFrameworks || [],\r\n            dataClassification: createDto.dataClassification || 'internal',\r\n            retentionPeriod: createDto.retentionPeriod || 365,\r\n            auditRequired: createDto.auditRequired || false,\r\n          },\r\n        },\r\n      });\r\n\r\n      // Save to database\r\n      const savedReport = await this.reportDefinitionRepository.save(reportDefinition);\r\n\r\n      // Cache the report definition\r\n      await this.cacheReportDefinition(savedReport);\r\n\r\n      // Log audit trail\r\n      await this.auditLogService.createAuditLog({\r\n        tableName: 'report_definitions',\r\n        recordId: savedReport.id,\r\n        action: 'REPORT_DEFINITION_CREATED',\r\n        userId,\r\n        newValues: {\r\n          name: savedReport.name,\r\n          reportType: savedReport.reportType,\r\n          category: savedReport.category,\r\n          isTemplate: savedReport.isTemplate,\r\n        },\r\n        success: true,\r\n        riskLevel: 'low',\r\n        complianceFrameworks: savedReport.metadata.compliance.frameworks,\r\n        businessJustification: 'Report definition creation',\r\n      });\r\n\r\n      // Emit creation event\r\n      this.eventEmitter.emit('reporting.definition.created', {\r\n        reportId: savedReport.id,\r\n        name: savedReport.name,\r\n        reportType: savedReport.reportType,\r\n        userId,\r\n        timestamp: new Date(),\r\n      });\r\n\r\n      this.logger.log('Report definition created successfully', {\r\n        reportId: savedReport.id,\r\n        name: savedReport.name,\r\n        userId,\r\n      });\r\n\r\n      return savedReport;\r\n\r\n    } catch (error) {\r\n      this.logger.error('Failed to create report definition', {\r\n        error: error.message,\r\n        name: createDto.name,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get report definition by ID with access control\r\n   */\r\n  async getReportDefinitionById(\r\n    id: string,\r\n    userId: string,\r\n    userRoles: string[] = []\r\n  ): Promise<ReportDefinition> {\r\n    try {\r\n      this.logger.debug('Retrieving report definition', { reportId: id, userId });\r\n\r\n      // Try cache first\r\n      const cachedReport = await this.getCachedReportDefinition(id);\r\n      if (cachedReport) {\r\n        // Validate access\r\n        if (!cachedReport.hasUserAccess(userId, userRoles, 'view')) {\r\n          throw new ForbiddenException('Insufficient permissions to view this report');\r\n        }\r\n        return cachedReport;\r\n      }\r\n\r\n      // Fetch from database\r\n      const reportDefinition = await this.reportDefinitionRepository.findOne({\r\n        where: { id, isActive: true },\r\n        relations: ['executions'],\r\n      });\r\n\r\n      if (!reportDefinition) {\r\n        throw new NotFoundException(`Report definition with ID ${id} not found`);\r\n      }\r\n\r\n      // Validate access\r\n      if (!reportDefinition.hasUserAccess(userId, userRoles, 'view')) {\r\n        throw new ForbiddenException('Insufficient permissions to view this report');\r\n      }\r\n\r\n      // Cache the report\r\n      await this.cacheReportDefinition(reportDefinition);\r\n\r\n      return reportDefinition;\r\n\r\n    } catch (error) {\r\n      this.logger.error('Failed to retrieve report definition', {\r\n        error: error.message,\r\n        reportId: id,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get report definitions with filtering and pagination\r\n   */\r\n  async getReportDefinitions(\r\n    filters: {\r\n      reportType?: string;\r\n      category?: string;\r\n      ownerId?: string;\r\n      isTemplate?: boolean;\r\n      tags?: string[];\r\n      search?: string;\r\n      page?: number;\r\n      limit?: number;\r\n    },\r\n    userId: string,\r\n    userRoles: string[] = []\r\n  ): Promise<{\r\n    reports: ReportDefinition[];\r\n    total: number;\r\n    page: number;\r\n    limit: number;\r\n    totalPages: number;\r\n  }> {\r\n    try {\r\n      this.logger.debug('Retrieving report definitions with filters', {\r\n        filters,\r\n        userId,\r\n      });\r\n\r\n      const page = filters.page || 1;\r\n      const limit = Math.min(filters.limit || 20, 100); // Max 100 per page\r\n      const offset = (page - 1) * limit;\r\n\r\n      // Build query\r\n      const queryBuilder = this.reportDefinitionRepository.createQueryBuilder('report')\r\n        .where('report.isActive = :isActive', { isActive: true });\r\n\r\n      // Apply filters\r\n      if (filters.reportType) {\r\n        queryBuilder.andWhere('report.reportType = :reportType', { reportType: filters.reportType });\r\n      }\r\n\r\n      if (filters.category) {\r\n        queryBuilder.andWhere('report.category = :category', { category: filters.category });\r\n      }\r\n\r\n      if (filters.ownerId) {\r\n        queryBuilder.andWhere('report.ownerId = :ownerId', { ownerId: filters.ownerId });\r\n      }\r\n\r\n      if (filters.isTemplate !== undefined) {\r\n        queryBuilder.andWhere('report.isTemplate = :isTemplate', { isTemplate: filters.isTemplate });\r\n      }\r\n\r\n      if (filters.tags && filters.tags.length > 0) {\r\n        queryBuilder.andWhere('report.metadata @> :tags', {\r\n          tags: JSON.stringify({ tags: filters.tags }),\r\n        });\r\n      }\r\n\r\n      if (filters.search) {\r\n        queryBuilder.andWhere(\r\n          '(report.name ILIKE :search OR report.description ILIKE :search)',\r\n          { search: `%${filters.search}%` }\r\n        );\r\n      }\r\n\r\n      // Apply access control - only show reports user can view\r\n      queryBuilder.andWhere(\r\n        '(report.ownerId = :userId OR ' +\r\n        'report.accessControl @> :publicAccess OR ' +\r\n        'report.accessControl @> :userAccess OR ' +\r\n        'report.accessControl @> :roleAccess)',\r\n        {\r\n          userId,\r\n          publicAccess: JSON.stringify({ visibility: 'public' }),\r\n          userAccess: JSON.stringify({ allowedUsers: [userId] }),\r\n          roleAccess: JSON.stringify({ allowedRoles: userRoles }),\r\n        }\r\n      );\r\n\r\n      // Order by popularity and creation date\r\n      queryBuilder.orderBy('report.metadata->>\\'usage\\'->>\\'popularityScore\\'', 'DESC')\r\n        .addOrderBy('report.createdAt', 'DESC');\r\n\r\n      // Get total count\r\n      const total = await queryBuilder.getCount();\r\n\r\n      // Apply pagination\r\n      queryBuilder.limit(limit).offset(offset);\r\n\r\n      // Execute query\r\n      const reports = await queryBuilder.getMany();\r\n\r\n      // Filter out reports user doesn't have access to (additional security layer)\r\n      const accessibleReports = reports.filter(report =>\r\n        report.hasUserAccess(userId, userRoles, 'view')\r\n      );\r\n\r\n      this.logger.debug('Retrieved report definitions', {\r\n        total,\r\n        returned: accessibleReports.length,\r\n        page,\r\n        userId,\r\n      });\r\n\r\n      return {\r\n        reports: accessibleReports,\r\n        total,\r\n        page,\r\n        limit,\r\n        totalPages: Math.ceil(total / limit),\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error('Failed to retrieve report definitions', {\r\n        error: error.message,\r\n        filters,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update report definition\r\n   */\r\n  async updateReportDefinition(\r\n    id: string,\r\n    updateDto: UpdateReportDefinitionDto,\r\n    userId: string,\r\n    userRoles: string[] = []\r\n  ): Promise<ReportDefinition> {\r\n    try {\r\n      this.logger.log('Updating report definition', { reportId: id, userId });\r\n\r\n      // Get existing report\r\n      const existingReport = await this.getReportDefinitionById(id, userId, userRoles);\r\n\r\n      // Check edit permission\r\n      if (!existingReport.hasUserAccess(userId, userRoles, 'edit')) {\r\n        throw new ForbiddenException('Insufficient permissions to edit this report');\r\n      }\r\n\r\n      // Validate name uniqueness if name is being changed\r\n      if (updateDto.name && updateDto.name !== existingReport.name) {\r\n        await this.validateReportNameUniqueness(updateDto.name, userId, id);\r\n      }\r\n\r\n      // Validate configuration if provided\r\n      if (updateDto.dataSourceConfig || updateDto.visualizationConfig || updateDto.exportConfig) {\r\n        const configToValidate = {\r\n          ...existingReport,\r\n          ...updateDto,\r\n        };\r\n        const validationResult = this.validateReportConfiguration(configToValidate);\r\n        if (!validationResult.isValid) {\r\n          throw new BadRequestException(`Configuration validation failed: ${validationResult.errors.join(', ')}`);\r\n        }\r\n      }\r\n\r\n      // Update metadata\r\n      const updatedMetadata = {\r\n        ...existingReport.metadata,\r\n        lastModifiedBy: userId,\r\n        changeLog: [\r\n          ...existingReport.metadata.changeLog,\r\n          {\r\n            version: this.incrementVersion(existingReport.metadata.version),\r\n            changes: this.generateChangeDescription(existingReport, updateDto),\r\n            modifiedBy: userId,\r\n            modifiedAt: new Date(),\r\n          },\r\n        ],\r\n      };\r\n\r\n      // Apply updates\r\n      const updatedReport = await this.reportDefinitionRepository.save({\r\n        ...existingReport,\r\n        ...updateDto,\r\n        metadata: updatedMetadata,\r\n        updatedAt: new Date(),\r\n      });\r\n\r\n      // Update cache\r\n      await this.cacheReportDefinition(updatedReport);\r\n\r\n      // Log audit trail\r\n      await this.auditLogService.createAuditLog({\r\n        tableName: 'report_definitions',\r\n        recordId: id,\r\n        action: 'REPORT_DEFINITION_UPDATED',\r\n        userId,\r\n        oldValues: this.extractAuditableFields(existingReport),\r\n        newValues: this.extractAuditableFields(updatedReport),\r\n        success: true,\r\n        riskLevel: 'low',\r\n        complianceFrameworks: updatedReport.metadata.compliance.frameworks,\r\n        businessJustification: 'Report definition update',\r\n      });\r\n\r\n      // Emit update event\r\n      this.eventEmitter.emit('reporting.definition.updated', {\r\n        reportId: id,\r\n        name: updatedReport.name,\r\n        changes: updatedMetadata.changeLog[updatedMetadata.changeLog.length - 1].changes,\r\n        userId,\r\n        timestamp: new Date(),\r\n      });\r\n\r\n      this.logger.log('Report definition updated successfully', {\r\n        reportId: id,\r\n        userId,\r\n      });\r\n\r\n      return updatedReport;\r\n\r\n    } catch (error) {\r\n      this.logger.error('Failed to update report definition', {\r\n        error: error.message,\r\n        reportId: id,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Delete report definition (soft delete)\r\n   */\r\n  async deleteReportDefinition(\r\n    id: string,\r\n    userId: string,\r\n    userRoles: string[] = []\r\n  ): Promise<void> {\r\n    try {\r\n      this.logger.log('Deleting report definition', { reportId: id, userId });\r\n\r\n      // Get existing report\r\n      const existingReport = await this.getReportDefinitionById(id, userId, userRoles);\r\n\r\n      // Check delete permission\r\n      if (!existingReport.hasUserAccess(userId, userRoles, 'delete')) {\r\n        throw new ForbiddenException('Insufficient permissions to delete this report');\r\n      }\r\n\r\n      // Check if report has active executions\r\n      const activeExecutions = existingReport.executions?.filter(\r\n        execution => execution.status === 'running' || execution.status === 'pending'\r\n      );\r\n\r\n      if (activeExecutions && activeExecutions.length > 0) {\r\n        throw new BadRequestException('Cannot delete report with active executions');\r\n      }\r\n\r\n      // Soft delete\r\n      await this.reportDefinitionRepository.update(id, {\r\n        isActive: false,\r\n        updatedAt: new Date(),\r\n      });\r\n\r\n      // Remove from cache\r\n      await this.removeCachedReportDefinition(id);\r\n\r\n      // Log audit trail\r\n      await this.auditLogService.createAuditLog({\r\n        tableName: 'report_definitions',\r\n        recordId: id,\r\n        action: 'REPORT_DEFINITION_DELETED',\r\n        userId,\r\n        oldValues: this.extractAuditableFields(existingReport),\r\n        success: true,\r\n        riskLevel: 'medium',\r\n        complianceFrameworks: existingReport.metadata.compliance.frameworks,\r\n        businessJustification: 'Report definition deletion',\r\n      });\r\n\r\n      // Emit deletion event\r\n      this.eventEmitter.emit('reporting.definition.deleted', {\r\n        reportId: id,\r\n        name: existingReport.name,\r\n        userId,\r\n        timestamp: new Date(),\r\n      });\r\n\r\n      this.logger.log('Report definition deleted successfully', {\r\n        reportId: id,\r\n        userId,\r\n      });\r\n\r\n    } catch (error) {\r\n      this.logger.error('Failed to delete report definition', {\r\n        error: error.message,\r\n        reportId: id,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Clone report definition from template\r\n   */\r\n  async cloneReportDefinition(\r\n    templateId: string,\r\n    newName: string,\r\n    userId: string,\r\n    userRoles: string[] = []\r\n  ): Promise<ReportDefinition> {\r\n    try {\r\n      this.logger.log('Cloning report definition from template', {\r\n        templateId,\r\n        newName,\r\n        userId,\r\n      });\r\n\r\n      // Get template\r\n      const template = await this.getReportDefinitionById(templateId, userId, userRoles);\r\n\r\n      if (!template.isTemplate) {\r\n        throw new BadRequestException('Source report is not a template');\r\n      }\r\n\r\n      // Validate new name uniqueness\r\n      await this.validateReportNameUniqueness(newName, userId);\r\n\r\n      // Clone the template\r\n      const clonedData = template.clone(newName, userId);\r\n\r\n      // Create new report\r\n      const clonedReport = await this.reportDefinitionRepository.save(clonedData);\r\n\r\n      // Cache the new report\r\n      await this.cacheReportDefinition(clonedReport);\r\n\r\n      // Log audit trail\r\n      await this.auditLogService.createAuditLog({\r\n        tableName: 'report_definitions',\r\n        recordId: clonedReport.id,\r\n        action: 'REPORT_DEFINITION_CLONED',\r\n        userId,\r\n        newValues: {\r\n          name: clonedReport.name,\r\n          templateId,\r\n          clonedFrom: template.name,\r\n        },\r\n        success: true,\r\n        riskLevel: 'low',\r\n        complianceFrameworks: clonedReport.metadata.compliance.frameworks,\r\n        businessJustification: 'Report definition cloning from template',\r\n      });\r\n\r\n      // Emit cloning event\r\n      this.eventEmitter.emit('reporting.definition.cloned', {\r\n        reportId: clonedReport.id,\r\n        templateId,\r\n        name: clonedReport.name,\r\n        userId,\r\n        timestamp: new Date(),\r\n      });\r\n\r\n      this.logger.log('Report definition cloned successfully', {\r\n        reportId: clonedReport.id,\r\n        templateId,\r\n        userId,\r\n      });\r\n\r\n      return clonedReport;\r\n\r\n    } catch (error) {\r\n      this.logger.error('Failed to clone report definition', {\r\n        error: error.message,\r\n        templateId,\r\n        newName,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get optimization recommendations for a report\r\n   */\r\n  async getOptimizationRecommendations(\r\n    id: string,\r\n    userId: string,\r\n    userRoles: string[] = []\r\n  ): Promise<{\r\n    performance: string[];\r\n    security: string[];\r\n    usability: string[];\r\n    compliance: string[];\r\n  }> {\r\n    try {\r\n      const report = await this.getReportDefinitionById(id, userId, userRoles);\r\n\r\n      const recommendations = {\r\n        performance: [],\r\n        security: [],\r\n        usability: [],\r\n        compliance: [],\r\n      };\r\n\r\n      // Performance recommendations\r\n      if (!report.performanceConfig.caching.enabled) {\r\n        recommendations.performance.push('Enable caching to improve report generation performance');\r\n      }\r\n\r\n      if (report.dataSourceConfig.sources.length > 3) {\r\n        recommendations.performance.push('Consider reducing the number of data sources for better performance');\r\n      }\r\n\r\n      if (report.performanceConfig.optimization.dataLimit > 10000) {\r\n        recommendations.performance.push('Consider reducing data limit or implementing pagination');\r\n      }\r\n\r\n      // Security recommendations\r\n      if (report.accessControl.visibility === 'public' && \r\n          report.metadata.compliance.dataClassification !== 'public') {\r\n        recommendations.security.push('Review public visibility for non-public data classification');\r\n      }\r\n\r\n      if (!report.metadata.compliance.auditRequired && \r\n          report.metadata.compliance.frameworks.length > 0) {\r\n        recommendations.security.push('Consider enabling audit requirements for compliance frameworks');\r\n      }\r\n\r\n      // Usability recommendations\r\n      if (!report.visualizationConfig.interactivity.export) {\r\n        recommendations.usability.push('Enable export functionality for better user experience');\r\n      }\r\n\r\n      if (report.visualizationConfig.series.length > 5) {\r\n        recommendations.usability.push('Consider reducing the number of data series for clarity');\r\n      }\r\n\r\n      // Compliance recommendations\r\n      if (report.metadata.compliance.frameworks.length > 0 && \r\n          report.metadata.compliance.retentionPeriod < 2555) {\r\n        recommendations.compliance.push('Consider extending retention period for compliance frameworks');\r\n      }\r\n\r\n      return recommendations;\r\n\r\n    } catch (error) {\r\n      this.logger.error('Failed to get optimization recommendations', {\r\n        error: error.message,\r\n        reportId: id,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Private helper methods\r\n\r\n  private async validateReportNameUniqueness(\r\n    name: string,\r\n    userId: string,\r\n    excludeId?: string\r\n  ): Promise<void> {\r\n    const existingReport = await this.reportDefinitionRepository.findOne({\r\n      where: {\r\n        name,\r\n        ownerId: userId,\r\n        isActive: true,\r\n        ...(excludeId && { id: Not(excludeId) }),\r\n      },\r\n    });\r\n\r\n    if (existingReport) {\r\n      throw new BadRequestException(`Report with name \"${name}\" already exists`);\r\n    }\r\n  }\r\n\r\n  private validateReportConfiguration(config: any): { isValid: boolean; errors: string[] } {\r\n    const errors: string[] = [];\r\n\r\n    // Validate data sources\r\n    if (!config.dataSourceConfig?.sources || config.dataSourceConfig.sources.length === 0) {\r\n      errors.push('At least one data source must be configured');\r\n    }\r\n\r\n    // Validate visualization\r\n    if (!config.visualizationConfig?.chartType) {\r\n      errors.push('Chart type must be specified');\r\n    }\r\n\r\n    if (!config.visualizationConfig?.series || config.visualizationConfig.series.length === 0) {\r\n      errors.push('At least one data series must be configured');\r\n    }\r\n\r\n    // Validate export configuration\r\n    if (!config.exportConfig?.formats || config.exportConfig.formats.length === 0) {\r\n      errors.push('At least one export format must be specified');\r\n    }\r\n\r\n    // Validate access control\r\n    if (config.accessControl?.visibility === 'shared' && \r\n        (!config.accessControl.allowedUsers?.length && !config.accessControl.allowedRoles?.length)) {\r\n      errors.push('Shared reports must specify allowed users or roles');\r\n    }\r\n\r\n    return {\r\n      isValid: errors.length === 0,\r\n      errors,\r\n    };\r\n  }\r\n\r\n  private async cacheReportDefinition(report: ReportDefinition): Promise<void> {\r\n    const cacheKey = `report_definition:${report.id}`;\r\n    await this.cacheService.set(cacheKey, report, { ttl: 3600 }); // 1 hour\r\n  }\r\n\r\n  private async getCachedReportDefinition(id: string): Promise<ReportDefinition | null> {\r\n    const cacheKey = `report_definition:${id}`;\r\n    return this.cacheService.get(cacheKey);\r\n  }\r\n\r\n  private async removeCachedReportDefinition(id: string): Promise<void> {\r\n    const cacheKey = `report_definition:${id}`;\r\n    await this.cacheService.del(cacheKey);\r\n  }\r\n\r\n  private incrementVersion(currentVersion: string): string {\r\n    const parts = currentVersion.split('.');\r\n    const patch = parseInt(parts[2] || '0') + 1;\r\n    return `${parts[0]}.${parts[1]}.${patch}`;\r\n  }\r\n\r\n  private generateChangeDescription(oldReport: ReportDefinition, updateDto: any): string[] {\r\n    const changes: string[] = [];\r\n\r\n    if (updateDto.name && updateDto.name !== oldReport.name) {\r\n      changes.push(`Name changed from \"${oldReport.name}\" to \"${updateDto.name}\"`);\r\n    }\r\n\r\n    if (updateDto.description !== undefined && updateDto.description !== oldReport.description) {\r\n      changes.push('Description updated');\r\n    }\r\n\r\n    if (updateDto.dataSourceConfig) {\r\n      changes.push('Data source configuration updated');\r\n    }\r\n\r\n    if (updateDto.visualizationConfig) {\r\n      changes.push('Visualization configuration updated');\r\n    }\r\n\r\n    if (updateDto.exportConfig) {\r\n      changes.push('Export configuration updated');\r\n    }\r\n\r\n    if (updateDto.accessControl) {\r\n      changes.push('Access control updated');\r\n    }\r\n\r\n    if (updateDto.schedulingConfig) {\r\n      changes.push('Scheduling configuration updated');\r\n    }\r\n\r\n    if (updateDto.performanceConfig) {\r\n      changes.push('Performance configuration updated');\r\n    }\r\n\r\n    return changes.length > 0 ? changes : ['Minor updates'];\r\n  }\r\n\r\n  private extractAuditableFields(report: ReportDefinition): any {\r\n    return {\r\n      name: report.name,\r\n      reportType: report.reportType,\r\n      category: report.category,\r\n      priority: report.priority,\r\n      isActive: report.isActive,\r\n      isTemplate: report.isTemplate,\r\n      visibility: report.accessControl.visibility,\r\n      dataClassification: report.metadata.compliance.dataClassification,\r\n    };\r\n  }\r\n}\r\n"], "version": 3}