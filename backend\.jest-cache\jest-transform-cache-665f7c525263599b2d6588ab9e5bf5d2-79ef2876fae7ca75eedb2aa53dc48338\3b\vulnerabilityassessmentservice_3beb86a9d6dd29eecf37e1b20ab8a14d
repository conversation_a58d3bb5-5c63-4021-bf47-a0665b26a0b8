5200cbb20fab3f60028483c54741ee1c
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var VulnerabilityAssessmentService_1;
var _a, _b, _c, _d, _e, _f, _g;
Object.defineProperty(exports, "__esModule", { value: true });
exports.VulnerabilityAssessmentService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const vulnerability_assessment_entity_1 = require("../../domain/entities/vulnerability-assessment.entity");
const vulnerability_entity_1 = require("../../domain/entities/vulnerability.entity");
const asset_entity_1 = require("../../../asset-management/domain/entities/asset.entity");
const vulnerability_scan_entity_1 = require("../../domain/entities/vulnerability-scan.entity");
const logger_service_1 = require("../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../infrastructure/logging/audit/audit.service");
const notification_service_1 = require("../../../../infrastructure/notification/notification.service");
/**
 * Vulnerability Assessment service
 * Handles vulnerability assessment operations and workflows
 */
let VulnerabilityAssessmentService = VulnerabilityAssessmentService_1 = class VulnerabilityAssessmentService {
    constructor(assessmentRepository, vulnerabilityRepository, assetRepository, scanRepository, loggerService, auditService, notificationService) {
        this.assessmentRepository = assessmentRepository;
        this.vulnerabilityRepository = vulnerabilityRepository;
        this.assetRepository = assetRepository;
        this.scanRepository = scanRepository;
        this.loggerService = loggerService;
        this.auditService = auditService;
        this.notificationService = notificationService;
        this.logger = new common_1.Logger(VulnerabilityAssessmentService_1.name);
    }
    /**
     * Create vulnerability assessment
     */
    async createAssessment(assessmentData, userId) {
        try {
            this.logger.debug('Creating vulnerability assessment', {
                vulnerabilityId: assessmentData.vulnerabilityId,
                assetId: assessmentData.assetId,
                assessmentType: assessmentData.assessmentType,
                userId,
            });
            // Validate vulnerability exists
            const vulnerability = await this.vulnerabilityRepository.findOne({
                where: { id: assessmentData.vulnerabilityId },
            });
            if (!vulnerability) {
                throw new common_1.NotFoundException('Vulnerability not found');
            }
            // Validate asset if provided
            if (assessmentData.assetId) {
                const asset = await this.assetRepository.findOne({
                    where: { id: assessmentData.assetId },
                });
                if (!asset) {
                    throw new common_1.NotFoundException('Asset not found');
                }
            }
            // Validate scan if provided
            if (assessmentData.scanId) {
                const scan = await this.scanRepository.findOne({
                    where: { id: assessmentData.scanId },
                });
                if (!scan) {
                    throw new common_1.NotFoundException('Scan not found');
                }
            }
            const assessment = this.assessmentRepository.create({
                ...assessmentData,
                status: 'pending',
                assessedBy: userId,
                assessedAt: new Date(),
            });
            const savedAssessment = await this.assessmentRepository.save(assessment);
            await this.auditService.logUserAction(userId, 'create', 'vulnerability_assessment', savedAssessment.id, {
                vulnerabilityId: assessmentData.vulnerabilityId,
                assetId: assessmentData.assetId,
                assessmentType: assessmentData.assessmentType,
            });
            this.logger.log('Vulnerability assessment created successfully', {
                assessmentId: savedAssessment.id,
                vulnerabilityId: assessmentData.vulnerabilityId,
                userId,
            });
            return savedAssessment;
        }
        catch (error) {
            this.logger.error('Failed to create vulnerability assessment', {
                error: error.message,
                assessmentData,
                userId,
            });
            throw error;
        }
    }
    /**
     * Get assessment details
     */
    async getAssessmentDetails(id) {
        try {
            const assessment = await this.assessmentRepository.findOne({
                where: { id },
                relations: ['vulnerability', 'asset', 'scan'],
            });
            if (!assessment) {
                throw new common_1.NotFoundException('Assessment not found');
            }
            this.logger.debug('Assessment details retrieved', {
                assessmentId: id,
                vulnerabilityId: assessment.vulnerabilityId,
                status: assessment.status,
            });
            return assessment;
        }
        catch (error) {
            this.logger.error('Failed to get assessment details', {
                assessmentId: id,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Search assessments with filtering
     */
    async searchAssessments(criteria) {
        try {
            const page = criteria.page || 1;
            const limit = Math.min(criteria.limit || 50, 1000);
            const offset = (page - 1) * limit;
            const queryBuilder = this.assessmentRepository
                .createQueryBuilder('assessment')
                .leftJoinAndSelect('assessment.vulnerability', 'vulnerability')
                .leftJoinAndSelect('assessment.asset', 'asset')
                .leftJoinAndSelect('assessment.scan', 'scan');
            // Apply filters
            if (criteria.vulnerabilityIds?.length) {
                queryBuilder.andWhere('assessment.vulnerabilityId IN (:...vulnerabilityIds)', {
                    vulnerabilityIds: criteria.vulnerabilityIds
                });
            }
            if (criteria.assetIds?.length) {
                queryBuilder.andWhere('assessment.assetId IN (:...assetIds)', {
                    assetIds: criteria.assetIds
                });
            }
            if (criteria.scanIds?.length) {
                queryBuilder.andWhere('assessment.scanId IN (:...scanIds)', {
                    scanIds: criteria.scanIds
                });
            }
            if (criteria.statuses?.length) {
                queryBuilder.andWhere('assessment.status IN (:...statuses)', {
                    statuses: criteria.statuses
                });
            }
            if (criteria.assessmentTypes?.length) {
                queryBuilder.andWhere('assessment.assessmentType IN (:...assessmentTypes)', {
                    assessmentTypes: criteria.assessmentTypes
                });
            }
            if (criteria.assessedBy?.length) {
                queryBuilder.andWhere('assessment.assessedBy IN (:...assessedBy)', {
                    assessedBy: criteria.assessedBy
                });
            }
            if (criteria.assessedAfter) {
                queryBuilder.andWhere('assessment.assessedAt >= :assessedAfter', {
                    assessedAfter: criteria.assessedAfter
                });
            }
            if (criteria.assessedBefore) {
                queryBuilder.andWhere('assessment.assessedAt <= :assessedBefore', {
                    assessedBefore: criteria.assessedBefore
                });
            }
            if (criteria.severities?.length) {
                queryBuilder.andWhere('assessment.assessedSeverity IN (:...severities)', {
                    severities: criteria.severities
                });
            }
            if (criteria.isFalsePositive !== undefined) {
                queryBuilder.andWhere('assessment.isFalsePositive = :isFalsePositive', {
                    isFalsePositive: criteria.isFalsePositive
                });
            }
            if (criteria.isAcceptedRisk !== undefined) {
                queryBuilder.andWhere('assessment.isAcceptedRisk = :isAcceptedRisk', {
                    isAcceptedRisk: criteria.isAcceptedRisk
                });
            }
            // Apply sorting
            const sortBy = criteria.sortBy || 'assessedAt';
            const sortOrder = criteria.sortOrder || 'DESC';
            queryBuilder.orderBy(`assessment.${sortBy}`, sortOrder);
            // Apply pagination
            queryBuilder.skip(offset).take(limit);
            const [assessments, total] = await queryBuilder.getManyAndCount();
            this.logger.debug('Assessment search completed', {
                total,
                page,
                limit,
                criteriaCount: Object.keys(criteria).length,
            });
            return {
                assessments,
                total,
                page,
                totalPages: Math.ceil(total / limit),
            };
        }
        catch (error) {
            this.logger.error('Failed to search assessments', {
                error: error.message,
                criteria,
            });
            throw error;
        }
    }
    /**
     * Update assessment
     */
    async updateAssessment(id, updates, userId) {
        try {
            const assessment = await this.assessmentRepository.findOne({
                where: { id },
            });
            if (!assessment) {
                throw new common_1.NotFoundException('Assessment not found');
            }
            this.logger.debug('Updating assessment', {
                assessmentId: id,
                userId,
            });
            // Track changes for audit
            const changes = {};
            Object.keys(updates).forEach(key => {
                if (assessment[key] !== updates[key]) {
                    changes[key] = {
                        from: assessment[key],
                        to: updates[key],
                    };
                }
            });
            Object.assign(assessment, updates);
            const savedAssessment = await this.assessmentRepository.save(assessment);
            await this.auditService.logUserAction(userId, 'update', 'vulnerability_assessment', id, {
                vulnerabilityId: assessment.vulnerabilityId,
                changes,
            });
            this.logger.log('Assessment updated successfully', {
                assessmentId: id,
                changesCount: Object.keys(changes).length,
                userId,
            });
            return savedAssessment;
        }
        catch (error) {
            this.logger.error('Failed to update assessment', {
                assessmentId: id,
                error: error.message,
                userId,
            });
            throw error;
        }
    }
    /**
     * Complete assessment
     */
    async completeAssessment(id, userId) {
        try {
            const assessment = await this.assessmentRepository.findOne({
                where: { id },
                relations: ['vulnerability'],
            });
            if (!assessment) {
                throw new common_1.NotFoundException('Assessment not found');
            }
            assessment.complete();
            const savedAssessment = await this.assessmentRepository.save(assessment);
            await this.auditService.logUserAction(userId, 'complete', 'vulnerability_assessment', id, {
                vulnerabilityId: assessment.vulnerabilityId,
            });
            this.logger.log('Assessment completed successfully', {
                assessmentId: id,
                vulnerabilityId: assessment.vulnerabilityId,
                userId,
            });
            return savedAssessment;
        }
        catch (error) {
            this.logger.error('Failed to complete assessment', {
                assessmentId: id,
                error: error.message,
                userId,
            });
            throw error;
        }
    }
    /**
     * Review assessment
     */
    async reviewAssessment(id, reviewData, userId) {
        try {
            const assessment = await this.assessmentRepository.findOne({
                where: { id },
            });
            if (!assessment) {
                throw new common_1.NotFoundException('Assessment not found');
            }
            assessment.review(userId, reviewData.comments);
            if (!reviewData.approved) {
                assessment.reject(reviewData.comments);
            }
            const savedAssessment = await this.assessmentRepository.save(assessment);
            await this.auditService.logUserAction(userId, 'review', 'vulnerability_assessment', id, {
                vulnerabilityId: assessment.vulnerabilityId,
                approved: reviewData.approved,
                comments: reviewData.comments,
            });
            this.logger.log('Assessment reviewed successfully', {
                assessmentId: id,
                approved: reviewData.approved,
                userId,
            });
            return savedAssessment;
        }
        catch (error) {
            this.logger.error('Failed to review assessment', {
                assessmentId: id,
                error: error.message,
                userId,
            });
            throw error;
        }
    }
    /**
     * Delete assessment
     */
    async deleteAssessment(id, userId) {
        try {
            const assessment = await this.assessmentRepository.findOne({
                where: { id },
            });
            if (!assessment) {
                throw new common_1.NotFoundException('Assessment not found');
            }
            this.logger.debug('Deleting assessment', {
                assessmentId: id,
                vulnerabilityId: assessment.vulnerabilityId,
                userId,
            });
            await this.assessmentRepository.remove(assessment);
            await this.auditService.logUserAction(userId, 'delete', 'vulnerability_assessment', id, {
                vulnerabilityId: assessment.vulnerabilityId,
                assessmentType: assessment.assessmentType,
            });
            this.logger.log('Assessment deleted successfully', {
                assessmentId: id,
                userId,
            });
        }
        catch (error) {
            this.logger.error('Failed to delete assessment', {
                assessmentId: id,
                error: error.message,
                userId,
            });
            throw error;
        }
    }
};
exports.VulnerabilityAssessmentService = VulnerabilityAssessmentService;
exports.VulnerabilityAssessmentService = VulnerabilityAssessmentService = VulnerabilityAssessmentService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(vulnerability_assessment_entity_1.VulnerabilityAssessment)),
    __param(1, (0, typeorm_1.InjectRepository)(vulnerability_entity_1.Vulnerability)),
    __param(2, (0, typeorm_1.InjectRepository)(asset_entity_1.Asset)),
    __param(3, (0, typeorm_1.InjectRepository)(vulnerability_scan_entity_1.VulnerabilityScan)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _c : Object, typeof (_d = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _d : Object, typeof (_e = typeof logger_service_1.LoggerService !== "undefined" && logger_service_1.LoggerService) === "function" ? _e : Object, typeof (_f = typeof audit_service_1.AuditService !== "undefined" && audit_service_1.AuditService) === "function" ? _f : Object, typeof (_g = typeof notification_service_1.NotificationService !== "undefined" && notification_service_1.NotificationService) === "function" ? _g : Object])
], VulnerabilityAssessmentService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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