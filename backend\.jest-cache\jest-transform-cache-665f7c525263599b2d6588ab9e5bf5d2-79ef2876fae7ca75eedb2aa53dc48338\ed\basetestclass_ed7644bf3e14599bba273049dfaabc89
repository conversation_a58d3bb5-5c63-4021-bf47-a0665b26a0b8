12ebb30df5fe6b04c31058e3c7d39fea
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseTestClass = void 0;
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const jwt_1 = require("@nestjs/jwt");
const passport_1 = require("@nestjs/passport");
const request = __importStar(require("supertest"));
const test_database_util_1 = require("../utils/test-database.util");
const test_fixtures_util_1 = require("../fixtures/test-fixtures.util");
/**
 * Base Test Class
 *
 * Comprehensive base class for all test suites providing:
 * - NestJS testing module setup with proper dependency injection
 * - Database transaction management with automatic rollback
 * - Authentication and authorization testing utilities
 * - Mock service creation and provider overrides
 * - Performance testing and benchmarking utilities
 * - Event emission testing and validation
 * - HTTP request testing with supertest integration
 * - Test data management and cleanup utilities
 */
class BaseTestClass {
    /**
     * Setup test environment before all tests
     */
    async setupTestEnvironment(moduleMetadata, entities = [], customProviders = []) {
        // Initialize database utility
        this.databaseUtil = test_database_util_1.TestDatabaseUtil.getInstance();
        await this.databaseUtil.initializeTestDatabase(entities);
        // Create testing module
        this.testingModule = await testing_1.Test.createTestingModule({
            imports: [
                config_1.ConfigModule.forRoot({
                    isGlobal: true,
                    envFilePath: ['.env.test', '.env'],
                }),
                typeorm_1.TypeOrmModule.forRootAsync({
                    imports: [config_1.ConfigModule],
                    useFactory: async (configService) => ({
                        type: 'postgres',
                        host: configService.get('TEST_DB_HOST', 'localhost'),
                        port: configService.get('TEST_DB_PORT', 5433),
                        username: configService.get('TEST_DB_USERNAME', 'test'),
                        password: configService.get('TEST_DB_PASSWORD', 'test'),
                        database: configService.get('TEST_DB_NAME', 'sentinel_test'),
                        entities,
                        synchronize: true,
                        dropSchema: false,
                        logging: false,
                    }),
                    inject: [config_1.ConfigService],
                }),
                typeorm_1.TypeOrmModule.forFeature(entities),
                event_emitter_1.EventEmitterModule.forRoot(),
                passport_1.PassportModule.register({ defaultStrategy: 'jwt' }),
                jwt_1.JwtModule.registerAsync({
                    imports: [config_1.ConfigModule],
                    useFactory: async (configService) => ({
                        secret: configService.get('JWT_SECRET', 'test-secret'),
                        signOptions: { expiresIn: '1h' },
                    }),
                    inject: [config_1.ConfigService],
                }),
                ...moduleMetadata.imports || [],
            ],
            controllers: moduleMetadata.controllers || [],
            providers: [
                ...moduleMetadata.providers || [],
                ...customProviders,
            ],
        }).compile();
        // Create application instance
        this.app = this.testingModule.createNestApplication();
        // Setup global pipes, guards, and interceptors
        await this.setupGlobalMiddleware();
        // Initialize application
        await this.app.init();
        this.httpServer = this.app.getHttpServer();
        // Setup test user and authentication
        await this.setupTestAuthentication();
    }
    /**
     * Cleanup test environment after all tests
     */
    async cleanupTestEnvironment() {
        if (this.app) {
            await this.app.close();
        }
        if (this.databaseUtil) {
            await this.databaseUtil.closeConnection();
        }
    }
    /**
     * Setup test data before each test
     */
    async setupTestData() {
        await this.databaseUtil.startTransaction();
        // Generate and seed test data
        const testScenario = test_fixtures_util_1.TestFixturesUtil.generateTestScenario('default');
        await this.seedTestData(testScenario);
    }
    /**
     * Cleanup test data after each test
     */
    async cleanupTestData() {
        await this.databaseUtil.rollbackTransaction();
    }
    /**
     * Get service instance from testing module
     */
    getService(serviceClass) {
        return this.testingModule.get(serviceClass);
    }
    /**
     * Get repository instance from testing module
     */
    getRepository(entity) {
        return this.databaseUtil.getRepository(entity);
    }
    /**
     * Create mock provider
     */
    createMockProvider(token, mockImplementation) {
        return {
            provide: token,
            useValue: mockImplementation,
        };
    }
    /**
     * Create spy provider
     */
    createSpyProvider(token, originalService) {
        const spy = jest.createMockFromModule(originalService);
        return {
            provide: token,
            useValue: spy,
        };
    }
    /**
     * Make authenticated HTTP request
     */
    async makeAuthenticatedRequest(method, path, data, headers) {
        const req = request(this.httpServer)[method](path);
        if (this.authToken) {
            req.set('Authorization', `Bearer ${this.authToken}`);
        }
        if (headers) {
            Object.entries(headers).forEach(([key, value]) => {
                req.set(key, value);
            });
        }
        if (data && ['post', 'put', 'patch'].includes(method)) {
            req.send(data);
        }
        return req;
    }
    /**
     * Make unauthenticated HTTP request
     */
    async makeUnauthenticatedRequest(method, path, data, headers) {
        const req = request(this.httpServer)[method](path);
        if (headers) {
            Object.entries(headers).forEach(([key, value]) => {
                req.set(key, value);
            });
        }
        if (data && ['post', 'put', 'patch'].includes(method)) {
            req.send(data);
        }
        return req;
    }
    /**
     * Wait for event emission
     */
    async waitForEvent(eventName, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const timer = setTimeout(() => {
                reject(new Error(`Event ${eventName} not emitted within ${timeout}ms`));
            }, timeout);
            const eventEmitter = this.testingModule.get('EventEmitter2');
            eventEmitter.once(eventName, (data) => {
                clearTimeout(timer);
                resolve(data);
            });
        });
    }
    /**
     * Measure execution time
     */
    async measureExecutionTime(operation) {
        const startTime = Date.now();
        const result = await operation();
        const executionTime = Date.now() - startTime;
        return { result, executionTime };
    }
    /**
     * Run performance benchmark
     */
    async runPerformanceBenchmark(operation, iterations = 100) {
        const times = [];
        for (let i = 0; i < iterations; i++) {
            const { executionTime } = await this.measureExecutionTime(operation);
            times.push(executionTime);
        }
        const totalTime = times.reduce((sum, time) => sum + time, 0);
        const averageTime = totalTime / iterations;
        const minTime = Math.min(...times);
        const maxTime = Math.max(...times);
        return {
            averageTime,
            minTime,
            maxTime,
            totalTime,
            iterations,
        };
    }
    /**
     * Assert response structure
     */
    assertResponseStructure(response, expectedStructure) {
        for (const [key, expectedType] of Object.entries(expectedStructure)) {
            expect(response).toHaveProperty(key);
            if (typeof expectedType === 'string') {
                expect(typeof response[key]).toBe(expectedType);
            }
            else if (Array.isArray(expectedType)) {
                expect(Array.isArray(response[key])).toBe(true);
                if (expectedType.length > 0 && response[key].length > 0) {
                    this.assertResponseStructure(response[key][0], expectedType[0]);
                }
            }
            else if (typeof expectedType === 'object') {
                this.assertResponseStructure(response[key], expectedType);
            }
        }
    }
    /**
     * Assert database state
     */
    async assertDatabaseState(entity, conditions, expectedCount) {
        const repository = this.getRepository(entity);
        const actualCount = await repository.count({ where: conditions });
        expect(actualCount).toBe(expectedCount);
    }
    /**
     * Assert event was emitted
     */
    async assertEventEmitted(eventName, expectedData, timeout = 1000) {
        try {
            const eventData = await this.waitForEvent(eventName, timeout);
            if (expectedData) {
                expect(eventData).toMatchObject(expectedData);
            }
        }
        catch (error) {
            throw new Error(`Expected event ${eventName} was not emitted`);
        }
    }
    /**
     * Create test data factory
     */
    createTestDataFactory(entity) {
        return this.databaseUtil.createTestDataFactory(entity);
    }
    /**
     * Private helper methods
     */
    async setupGlobalMiddleware() {
        // Setup global validation pipe
        const { ValidationPipe } = await Promise.resolve().then(() => __importStar(require('@nestjs/common')));
        this.app.useGlobalPipes(new ValidationPipe({
            transform: true,
            whitelist: true,
            forbidNonWhitelisted: true,
        }));
        // Setup global exception filter
        const { HttpExceptionFilter } = await Promise.resolve().then(() => __importStar(require('../../../common/filters/http-exception.filter')));
        this.app.useGlobalFilters(new HttpExceptionFilter());
        // Setup global logging interceptor
        const { LoggingInterceptor } = await Promise.resolve().then(() => __importStar(require('../../../common/interceptors/logging.interceptor')));
        this.app.useGlobalInterceptors(new LoggingInterceptor());
    }
    async setupTestAuthentication() {
        // Create test user
        this.testUser = test_fixtures_util_1.TestFixturesUtil.generateUser({
            email: '<EMAIL>',
            username: 'testuser',
            role: 'admin',
            isActive: true,
        });
        // Generate JWT token
        const jwtService = this.testingModule.get('JwtService');
        this.authToken = await jwtService.signAsync({
            sub: this.testUser.id,
            email: this.testUser.email,
            role: this.testUser.role,
        });
    }
    async seedTestData(testScenario) {
        // Seed users
        if (testScenario.users) {
            const userRepository = this.getRepository(Object);
            // Note: This would need to be adapted based on actual entity classes
            // await userRepository.save(testScenario.users);
        }
        // Seed other entities as needed
        // This would be implemented based on the actual entity structure
    }
    /**
     * Utility methods for common test scenarios
     */
    async createTestUser(overrides = {}) {
        const userData = test_fixtures_util_1.TestFixturesUtil.generateUser(overrides);
        // Save to database and return
        return userData;
    }
    async createTestReportDefinition(overrides = {}) {
        const reportData = test_fixtures_util_1.TestFixturesUtil.generateReportDefinition(overrides);
        // Save to database and return
        return reportData;
    }
    async createTestWorkflowTemplate(overrides = {}) {
        const workflowData = test_fixtures_util_1.TestFixturesUtil.generateWorkflowTemplate(overrides);
        // Save to database and return
        return workflowData;
    }
    async createTestJobExecution(overrides = {}) {
        const jobData = test_fixtures_util_1.TestFixturesUtil.generateJobExecution(overrides);
        // Save to database and return
        return jobData;
    }
    async createTestMetric(overrides = {}) {
        const metricData = test_fixtures_util_1.TestFixturesUtil.generateMetric(overrides);
        // Save to database and return
        return metricData;
    }
    async createTestHealthCheck(overrides = {}) {
        const healthCheckData = test_fixtures_util_1.TestFixturesUtil.generateHealthCheck(overrides);
        // Save to database and return
        return healthCheckData;
    }
    async createTestAlert(overrides = {}) {
        const alertData = test_fixtures_util_1.TestFixturesUtil.generateAlert(overrides);
        // Save to database and return
        return alertData;
    }
}
exports.BaseTestClass = BaseTestClass;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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