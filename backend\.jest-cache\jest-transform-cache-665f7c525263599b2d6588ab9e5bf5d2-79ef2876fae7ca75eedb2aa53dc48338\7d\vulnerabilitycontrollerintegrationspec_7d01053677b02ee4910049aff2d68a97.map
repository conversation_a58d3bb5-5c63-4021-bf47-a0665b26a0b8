{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\api\\controllers\\__tests__\\vulnerability.controller.integration.spec.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6CAAsD;AAEtD,6CAAgD;AAChD,2CAA8C;AAC9C,mDAAqC;AAErC,6CAAqD;AAErD,0EAAsE;AACtE,+FAA2F;AAC3F,wFAA8E;AAC9E,8GAAmG;AACnG,4GAAiG;AACjG,4FAAkF;AAClF,yFAAqF;AACrF,6FAAyF;AACzF,0GAAsG;AACtG,6FAAwF;AACxF,uFAAmF;AAEnF,QAAQ,CAAC,uCAAuC,EAAE,GAAG,EAAE;IACrD,IAAI,GAAqB,CAAC;IAC1B,IAAI,uBAAkD,CAAC;IACvD,IAAI,oBAAyD,CAAC;IAC9D,IAAI,mBAAuD,CAAC;IAC5D,IAAI,eAAkC,CAAC;IAEvC,MAAM,QAAQ,GAAG;QACf,EAAE,EAAE,UAAU;QACd,KAAK,EAAE,kBAAkB;QACzB,KAAK,EAAE,CAAC,kBAAkB,CAAC;KAC5B,CAAC;IAEF,MAAM,iBAAiB,GAAG;QACxB,UAAU,EAAE,eAAe;QAC3B,KAAK,EAAE,kCAAkC;QACzC,WAAW,EAAE,iEAAiE;QAC9E,QAAQ,EAAE,MAAM;QAChB,SAAS,EAAE,GAAG;QACd,gBAAgB,EAAE;YAChB;gBACE,MAAM,EAAE,WAAW;gBACnB,OAAO,EAAE,iBAAiB;gBAC1B,OAAO,EAAE,OAAO;aACjB;SACF;QACD,aAAa,EAAE,sBAAsB;QACrC,UAAU,EAAE;YACV,IAAI,EAAE,cAAc;YACpB,IAAI,EAAE,UAAU;YAChB,UAAU,EAAE,MAAM;YAClB,WAAW,EAAE,sBAAsB;SACpC;KACF,CAAC;IAEF,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,aAAa,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAClE,OAAO,EAAE;gBACP,qBAAY,CAAC,OAAO,CAAC;oBACnB,QAAQ,EAAE,IAAI;oBACd,WAAW,EAAE,WAAW;iBACzB,CAAC;gBACF,uBAAa,CAAC,OAAO,CAAC;oBACpB,IAAI,EAAE,QAAQ;oBACd,QAAQ,EAAE,UAAU;oBACpB,QAAQ,EAAE,CAAC,oCAAa,EAAE,yDAAuB,EAAE,uDAAsB,EAAE,oBAAK,CAAC;oBACjF,WAAW,EAAE,IAAI;oBACjB,OAAO,EAAE,KAAK;iBACf,CAAC;gBACF,uBAAa,CAAC,UAAU,CAAC;oBACvB,oCAAa;oBACb,yDAAuB;oBACvB,uDAAsB;oBACtB,oBAAK;iBACN,CAAC;aACH;YACD,WAAW,EAAE,CAAC,kDAAuB,CAAC;YACtC,SAAS,EAAE;gBACT,4CAAoB;gBACpB;oBACE,OAAO,EAAE,8BAAa;oBACtB,QAAQ,EAAE;wBACR,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;wBAChB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;wBACd,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;wBACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;qBACjB;iBACF;gBACD;oBACE,OAAO,EAAE,4BAAY;oBACrB,QAAQ,EAAE;wBACR,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;qBACzB;iBACF;gBACD;oBACE,OAAO,EAAE,0CAAmB;oBAC5B,QAAQ,EAAE;wBACR,mCAAmC,EAAE,IAAI,CAAC,EAAE,EAAE;qBAC/C;iBACF;aACF;SACF,CAAC;aACC,aAAa,CAAC,6BAAY,CAAC;aAC3B,QAAQ,CAAC;YACR,WAAW,EAAE,CAAC,OAAO,EAAE,EAAE;gBACvB,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;gBACpD,OAAO,CAAC,IAAI,GAAG,QAAQ,CAAC;gBACxB,OAAO,IAAI,CAAC;YACd,CAAC;SACF,CAAC;aACD,aAAa,CAAC,wBAAU,CAAC;aACzB,QAAQ,CAAC;YACR,WAAW,EAAE,GAAG,EAAE,CAAC,IAAI;SACxB,CAAC;aACD,OAAO,EAAE,CAAC;QAEb,GAAG,GAAG,aAAa,CAAC,qBAAqB,EAAE,CAAC;QAC5C,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC;QAEjB,uBAAuB,GAAG,aAAa,CAAC,GAAG,CACzC,IAAA,4BAAkB,EAAC,oCAAa,CAAC,CAClC,CAAC;QACF,oBAAoB,GAAG,aAAa,CAAC,GAAG,CACtC,IAAA,4BAAkB,EAAC,yDAAuB,CAAC,CAC5C,CAAC;QACF,mBAAmB,GAAG,aAAa,CAAC,GAAG,CACrC,IAAA,4BAAkB,EAAC,uDAAsB,CAAC,CAC3C,CAAC;QACF,eAAe,GAAG,aAAa,CAAC,GAAG,CACjC,IAAA,4BAAkB,EAAC,oBAAK,CAAC,CAC1B,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,qCAAqC;QACrC,MAAM,uBAAuB,CAAC,KAAK,EAAE,CAAC;QACtC,MAAM,oBAAoB,CAAC,KAAK,EAAE,CAAC;QACnC,MAAM,mBAAmB,CAAC,KAAK,EAAE,CAAC;QAClC,MAAM,eAAe,CAAC,KAAK,EAAE,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE;QAClD,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,8BAA8B;YAC9B,MAAM,KAAK,GAAG,uBAAuB,CAAC,MAAM,CAAC;gBAC3C,GAAG,iBAAiB;gBACpB,UAAU,EAAE,eAAe;gBAC3B,QAAQ,EAAE,UAAU;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,GAAG,uBAAuB,CAAC,MAAM,CAAC;gBAC3C,GAAG,iBAAiB;gBACpB,UAAU,EAAE,eAAe;gBAC3B,QAAQ,EAAE,MAAM;aACjB,CAAC,CAAC;YACH,MAAM,uBAAuB,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;YAEnD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,gCAAgC,CAAC;iBACrC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAChD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAClD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAClD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,8BAA8B;YAC9B,MAAM,eAAe,GAAG,EAAE,CAAC;YAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC7B,eAAe,CAAC,IAAI,CAClB,uBAAuB,CAAC,MAAM,CAAC;oBAC7B,GAAG,iBAAiB;oBACpB,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;oBACvD,KAAK,EAAE,sBAAsB,CAAC,EAAE;iBACjC,CAAC,CACH,CAAC;YACJ,CAAC;YACD,MAAM,uBAAuB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAEpD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,KAAK,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;iBAC7B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;YACxD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAC9C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAC7C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YACnD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YACvD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACrC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,KAAK,GAAG,uBAAuB,CAAC,MAAM,CAAC;gBAC3C,GAAG,iBAAiB;gBACpB,UAAU,EAAE,eAAe;gBAC3B,QAAQ,EAAE,UAAU;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,GAAG,uBAAuB,CAAC,MAAM,CAAC;gBAC3C,GAAG,iBAAiB;gBACpB,UAAU,EAAE,eAAe;gBAC3B,QAAQ,EAAE,QAAQ;aACnB,CAAC,CAAC;YACH,MAAM,uBAAuB,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;YAEnD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,KAAK,CAAC,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC;iBACjC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,KAAK,GAAG,uBAAuB,CAAC,MAAM,CAAC;gBAC3C,GAAG,iBAAiB;gBACpB,UAAU,EAAE,eAAe;gBAC3B,KAAK,EAAE,6BAA6B;aACrC,CAAC,CAAC;YACH,MAAM,KAAK,GAAG,uBAAuB,CAAC,MAAM,CAAC;gBAC3C,GAAG,iBAAiB;gBACpB,UAAU,EAAE,eAAe;gBAC3B,KAAK,EAAE,kCAAkC;aAC1C,CAAC,CAAC;YACH,MAAM,uBAAuB,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;YAEnD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,KAAK,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC;iBAC5B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,aAAa,GAAG,uBAAuB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YACxE,MAAM,SAAS,GAAG,MAAM,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAEpE,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,wBAAwB,SAAS,CAAC,EAAE,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACpE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,2DAA2D,CAAC;iBAChE,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,sBAAsB,CAAC;iBAC5B,IAAI,CAAC,iBAAiB,CAAC;iBACvB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACpE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAE1D,kCAAkC;YAClC,MAAM,SAAS,GAAG,MAAM,uBAAuB,CAAC,OAAO,CAAC;gBACtD,KAAK,EAAE,EAAE,UAAU,EAAE,iBAAiB,CAAC,UAAU,EAAE;aACpD,CAAC,CAAC;YACH,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAChC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,oBAAoB,GAAG;gBAC3B,GAAG,iBAAiB;gBACpB,UAAU,EAAE,EAAE,EAAE,4BAA4B;gBAC5C,QAAQ,EAAE,SAAS,EAAE,uBAAuB;aAC7C,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,sBAAsB,CAAC;iBAC5B,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,KAAK,IAAI,EAAE;YACxE,6BAA6B;YAC7B,MAAM,aAAa,GAAG,uBAAuB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YACxE,MAAM,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAElD,0BAA0B;YAC1B,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,sBAAsB,CAAC;iBAC5B,IAAI,CAAC,iBAAiB,CAAC;iBACvB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,aAAa,GAAG,uBAAuB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YACxE,MAAM,SAAS,GAAG,MAAM,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAEpE,MAAM,OAAO,GAAG;gBACd,KAAK,EAAE,6BAA6B;gBACpC,QAAQ,EAAE,UAAU;gBACpB,SAAS,EAAE,GAAG;aACf,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,wBAAwB,SAAS,CAAC,EAAE,EAAE,CAAC;iBAC3C,IAAI,CAAC,OAAO,CAAC;iBACb,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACtD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAExD,8BAA8B;YAC9B,MAAM,WAAW,GAAG,MAAM,uBAAuB,CAAC,OAAO,CAAC;gBACxD,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE;aAC5B,CAAC,CAAC;YACH,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,2DAA2D,CAAC;iBAChE,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC;iBAChC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC/C,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,aAAa,GAAG,uBAAuB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YACxE,MAAM,SAAS,GAAG,MAAM,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAEpE,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,MAAM,CAAC,wBAAwB,SAAS,CAAC,EAAE,EAAE,CAAC;iBAC9C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,sCAAsC;YACtC,MAAM,WAAW,GAAG,MAAM,uBAAuB,CAAC,OAAO,CAAC;gBACxD,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE;aAC5B,CAAC,CAAC;YACH,MAAM,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,MAAM,CAAC,2DAA2D,CAAC;iBACnE,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sCAAsC,EAAE,GAAG,EAAE;QACpD,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,aAAa,GAAG,uBAAuB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YACxE,MAAM,SAAS,GAAG,MAAM,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAEpE,MAAM,cAAc,GAAG;gBACrB,cAAc,EAAE,QAAQ;gBACxB,QAAQ,EAAE,gDAAgD;gBAC1D,gBAAgB,EAAE,MAAM;gBACxB,eAAe,EAAE,EAAE;aACpB,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,wBAAwB,SAAS,CAAC,EAAE,SAAS,CAAC;iBACnD,IAAI,CAAC,cAAc,CAAC;iBACpB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YACzD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YAE7D,8BAA8B;YAC9B,MAAM,UAAU,GAAG,MAAM,oBAAoB,CAAC,OAAO,CAAC;gBACpD,KAAK,EAAE,EAAE,eAAe,EAAE,SAAS,CAAC,EAAE,EAAE;aACzC,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACjC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0CAA0C,EAAE,GAAG,EAAE;QACxD,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,aAAa,GAAG,uBAAuB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YACxE,MAAM,SAAS,GAAG,MAAM,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAEpE,MAAM,aAAa,GAAG;gBACpB,aAAa,EAAE,gBAAgB;gBAC/B,aAAa,EAAE,2DAA2D;gBAC1E,qBAAqB,EAAE,uCAAuC;aAC/D,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,wBAAwB,SAAS,CAAC,EAAE,aAAa,CAAC;iBACvD,IAAI,CAAC,aAAa,CAAC;iBACnB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YACzD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;YAEtE,6BAA6B;YAC7B,MAAM,SAAS,GAAG,MAAM,mBAAmB,CAAC,OAAO,CAAC;gBAClD,KAAK,EAAE,EAAE,eAAe,EAAE,SAAS,CAAC,EAAE,EAAE;aACzC,CAAC,CAAC;YACH,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAChC,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\api\\controllers\\__tests__\\vulnerability.controller.integration.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { INestApplication } from '@nestjs/common';\r\nimport { TypeOrmModule } from '@nestjs/typeorm';\r\nimport { ConfigModule } from '@nestjs/config';\r\nimport * as request from 'supertest';\r\nimport { Repository } from 'typeorm';\r\nimport { getRepositoryToken } from '@nestjs/typeorm';\r\n\r\nimport { VulnerabilityController } from '../vulnerability.controller';\r\nimport { VulnerabilityService } from '../../../application/services/vulnerability.service';\r\nimport { Vulnerability } from '../../../domain/entities/vulnerability.entity';\r\nimport { VulnerabilityAssessment } from '../../../domain/entities/vulnerability-assessment.entity';\r\nimport { VulnerabilityException } from '../../../domain/entities/vulnerability-exception.entity';\r\nimport { Asset } from '../../../../asset-management/domain/entities/asset.entity';\r\nimport { LoggerService } from '../../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../../infrastructure/logging/audit/audit.service';\r\nimport { NotificationService } from '../../../../../infrastructure/notification/notification.service';\r\nimport { JwtAuthGuard } from '../../../../../infrastructure/auth/guards/jwt-auth.guard';\r\nimport { RolesGuard } from '../../../../../infrastructure/auth/guards/roles.guard';\r\n\r\ndescribe('VulnerabilityController (Integration)', () => {\r\n  let app: INestApplication;\r\n  let vulnerabilityRepository: Repository<Vulnerability>;\r\n  let assessmentRepository: Repository<VulnerabilityAssessment>;\r\n  let exceptionRepository: Repository<VulnerabilityException>;\r\n  let assetRepository: Repository<Asset>;\r\n\r\n  const mockUser = {\r\n    id: 'user-123',\r\n    email: '<EMAIL>',\r\n    roles: ['security_analyst'],\r\n  };\r\n\r\n  const mockVulnerability = {\r\n    identifier: 'CVE-2023-1234',\r\n    title: 'Test SQL Injection Vulnerability',\r\n    description: 'A SQL injection vulnerability in the user authentication module',\r\n    severity: 'high',\r\n    cvssScore: 7.5,\r\n    affectedProducts: [\r\n      {\r\n        vendor: 'Test Corp',\r\n        product: 'Web Application',\r\n        version: '1.0.0',\r\n      },\r\n    ],\r\n    publishedDate: '2023-01-01T00:00:00Z',\r\n    dataSource: {\r\n      name: 'Manual Entry',\r\n      type: 'internal',\r\n      confidence: 'high',\r\n      lastUpdated: '2023-01-01T00:00:00Z',\r\n    },\r\n  };\r\n\r\n  beforeAll(async () => {\r\n    const moduleFixture: TestingModule = await Test.createTestingModule({\r\n      imports: [\r\n        ConfigModule.forRoot({\r\n          isGlobal: true,\r\n          envFilePath: '.env.test',\r\n        }),\r\n        TypeOrmModule.forRoot({\r\n          type: 'sqlite',\r\n          database: ':memory:',\r\n          entities: [Vulnerability, VulnerabilityAssessment, VulnerabilityException, Asset],\r\n          synchronize: true,\r\n          logging: false,\r\n        }),\r\n        TypeOrmModule.forFeature([\r\n          Vulnerability,\r\n          VulnerabilityAssessment,\r\n          VulnerabilityException,\r\n          Asset,\r\n        ]),\r\n      ],\r\n      controllers: [VulnerabilityController],\r\n      providers: [\r\n        VulnerabilityService,\r\n        {\r\n          provide: LoggerService,\r\n          useValue: {\r\n            debug: jest.fn(),\r\n            log: jest.fn(),\r\n            warn: jest.fn(),\r\n            error: jest.fn(),\r\n          },\r\n        },\r\n        {\r\n          provide: AuditService,\r\n          useValue: {\r\n            logUserAction: jest.fn(),\r\n          },\r\n        },\r\n        {\r\n          provide: NotificationService,\r\n          useValue: {\r\n            sendNewCriticalVulnerabilitiesAlert: jest.fn(),\r\n          },\r\n        },\r\n      ],\r\n    })\r\n      .overrideGuard(JwtAuthGuard)\r\n      .useValue({\r\n        canActivate: (context) => {\r\n          const request = context.switchToHttp().getRequest();\r\n          request.user = mockUser;\r\n          return true;\r\n        },\r\n      })\r\n      .overrideGuard(RolesGuard)\r\n      .useValue({\r\n        canActivate: () => true,\r\n      })\r\n      .compile();\r\n\r\n    app = moduleFixture.createNestApplication();\r\n    await app.init();\r\n\r\n    vulnerabilityRepository = moduleFixture.get<Repository<Vulnerability>>(\r\n      getRepositoryToken(Vulnerability),\r\n    );\r\n    assessmentRepository = moduleFixture.get<Repository<VulnerabilityAssessment>>(\r\n      getRepositoryToken(VulnerabilityAssessment),\r\n    );\r\n    exceptionRepository = moduleFixture.get<Repository<VulnerabilityException>>(\r\n      getRepositoryToken(VulnerabilityException),\r\n    );\r\n    assetRepository = moduleFixture.get<Repository<Asset>>(\r\n      getRepositoryToken(Asset),\r\n    );\r\n  });\r\n\r\n  afterAll(async () => {\r\n    await app.close();\r\n  });\r\n\r\n  beforeEach(async () => {\r\n    // Clean up database before each test\r\n    await vulnerabilityRepository.clear();\r\n    await assessmentRepository.clear();\r\n    await exceptionRepository.clear();\r\n    await assetRepository.clear();\r\n  });\r\n\r\n  describe('GET /api/vulnerabilities/dashboard', () => {\r\n    it('should return vulnerability dashboard data', async () => {\r\n      // Create test vulnerabilities\r\n      const vuln1 = vulnerabilityRepository.create({\r\n        ...mockVulnerability,\r\n        identifier: 'CVE-2023-0001',\r\n        severity: 'critical',\r\n      });\r\n      const vuln2 = vulnerabilityRepository.create({\r\n        ...mockVulnerability,\r\n        identifier: 'CVE-2023-0002',\r\n        severity: 'high',\r\n      });\r\n      await vulnerabilityRepository.save([vuln1, vuln2]);\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/vulnerabilities/dashboard')\r\n        .expect(200);\r\n\r\n      expect(response.body).toHaveProperty('summary');\r\n      expect(response.body).toHaveProperty('breakdown');\r\n      expect(response.body).toHaveProperty('timestamp');\r\n      expect(response.body.summary.total).toBe(2);\r\n      expect(response.body.summary.critical).toBe(1);\r\n      expect(response.body.summary.high).toBe(1);\r\n    });\r\n  });\r\n\r\n  describe('GET /api/vulnerabilities', () => {\r\n    it('should return paginated vulnerabilities', async () => {\r\n      // Create test vulnerabilities\r\n      const vulnerabilities = [];\r\n      for (let i = 1; i <= 15; i++) {\r\n        vulnerabilities.push(\r\n          vulnerabilityRepository.create({\r\n            ...mockVulnerability,\r\n            identifier: `CVE-2023-${i.toString().padStart(4, '0')}`,\r\n            title: `Test Vulnerability ${i}`,\r\n          }),\r\n        );\r\n      }\r\n      await vulnerabilityRepository.save(vulnerabilities);\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/vulnerabilities')\r\n        .query({ page: 1, limit: 10 })\r\n        .expect(200);\r\n\r\n      expect(response.body).toHaveProperty('vulnerabilities');\r\n      expect(response.body).toHaveProperty('total');\r\n      expect(response.body).toHaveProperty('page');\r\n      expect(response.body).toHaveProperty('totalPages');\r\n      expect(response.body.vulnerabilities).toHaveLength(10);\r\n      expect(response.body.total).toBe(15);\r\n      expect(response.body.totalPages).toBe(2);\r\n    });\r\n\r\n    it('should filter vulnerabilities by severity', async () => {\r\n      const vuln1 = vulnerabilityRepository.create({\r\n        ...mockVulnerability,\r\n        identifier: 'CVE-2023-0001',\r\n        severity: 'critical',\r\n      });\r\n      const vuln2 = vulnerabilityRepository.create({\r\n        ...mockVulnerability,\r\n        identifier: 'CVE-2023-0002',\r\n        severity: 'medium',\r\n      });\r\n      await vulnerabilityRepository.save([vuln1, vuln2]);\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/vulnerabilities')\r\n        .query({ severities: 'critical' })\r\n        .expect(200);\r\n\r\n      expect(response.body.vulnerabilities).toHaveLength(1);\r\n      expect(response.body.vulnerabilities[0].severity).toBe('critical');\r\n    });\r\n\r\n    it('should search vulnerabilities by text', async () => {\r\n      const vuln1 = vulnerabilityRepository.create({\r\n        ...mockVulnerability,\r\n        identifier: 'CVE-2023-0001',\r\n        title: 'SQL Injection in Login Form',\r\n      });\r\n      const vuln2 = vulnerabilityRepository.create({\r\n        ...mockVulnerability,\r\n        identifier: 'CVE-2023-0002',\r\n        title: 'Cross-Site Scripting in Comments',\r\n      });\r\n      await vulnerabilityRepository.save([vuln1, vuln2]);\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/vulnerabilities')\r\n        .query({ searchText: 'SQL' })\r\n        .expect(200);\r\n\r\n      expect(response.body.vulnerabilities).toHaveLength(1);\r\n      expect(response.body.vulnerabilities[0].title).toContain('SQL');\r\n    });\r\n  });\r\n\r\n  describe('GET /api/vulnerabilities/:id', () => {\r\n    it('should return vulnerability details', async () => {\r\n      const vulnerability = vulnerabilityRepository.create(mockVulnerability);\r\n      const savedVuln = await vulnerabilityRepository.save(vulnerability);\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .get(`/api/vulnerabilities/${savedVuln.id}`)\r\n        .expect(200);\r\n\r\n      expect(response.body).toHaveProperty('id');\r\n      expect(response.body.identifier).toBe(mockVulnerability.identifier);\r\n      expect(response.body.title).toBe(mockVulnerability.title);\r\n    });\r\n\r\n    it('should return 404 for non-existent vulnerability', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/vulnerabilities/123e4567-e89b-12d3-a456-426614174000')\r\n        .expect(404);\r\n\r\n      expect(response.body.message).toContain('Vulnerability not found');\r\n    });\r\n  });\r\n\r\n  describe('POST /api/vulnerabilities', () => {\r\n    it('should create a new vulnerability', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/vulnerabilities')\r\n        .send(mockVulnerability)\r\n        .expect(201);\r\n\r\n      expect(response.body).toHaveProperty('id');\r\n      expect(response.body.identifier).toBe(mockVulnerability.identifier);\r\n      expect(response.body.title).toBe(mockVulnerability.title);\r\n\r\n      // Verify it was saved to database\r\n      const savedVuln = await vulnerabilityRepository.findOne({\r\n        where: { identifier: mockVulnerability.identifier },\r\n      });\r\n      expect(savedVuln).toBeDefined();\r\n      expect(savedVuln.title).toBe(mockVulnerability.title);\r\n    });\r\n\r\n    it('should return 400 for invalid vulnerability data', async () => {\r\n      const invalidVulnerability = {\r\n        ...mockVulnerability,\r\n        identifier: '', // Invalid: empty identifier\r\n        severity: 'invalid', // Invalid: not in enum\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/vulnerabilities')\r\n        .send(invalidVulnerability)\r\n        .expect(400);\r\n\r\n      expect(response.body.message).toContain('validation failed');\r\n    });\r\n\r\n    it('should return 409 for duplicate vulnerability identifier', async () => {\r\n      // Create first vulnerability\r\n      const vulnerability = vulnerabilityRepository.create(mockVulnerability);\r\n      await vulnerabilityRepository.save(vulnerability);\r\n\r\n      // Try to create duplicate\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/vulnerabilities')\r\n        .send(mockVulnerability)\r\n        .expect(409);\r\n\r\n      expect(response.body.message).toContain('already exists');\r\n    });\r\n  });\r\n\r\n  describe('PUT /api/vulnerabilities/:id', () => {\r\n    it('should update an existing vulnerability', async () => {\r\n      const vulnerability = vulnerabilityRepository.create(mockVulnerability);\r\n      const savedVuln = await vulnerabilityRepository.save(vulnerability);\r\n\r\n      const updates = {\r\n        title: 'Updated Vulnerability Title',\r\n        severity: 'critical',\r\n        cvssScore: 9.0,\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .put(`/api/vulnerabilities/${savedVuln.id}`)\r\n        .send(updates)\r\n        .expect(200);\r\n\r\n      expect(response.body.title).toBe(updates.title);\r\n      expect(response.body.severity).toBe(updates.severity);\r\n      expect(response.body.cvssScore).toBe(updates.cvssScore);\r\n\r\n      // Verify database was updated\r\n      const updatedVuln = await vulnerabilityRepository.findOne({\r\n        where: { id: savedVuln.id },\r\n      });\r\n      expect(updatedVuln.title).toBe(updates.title);\r\n    });\r\n\r\n    it('should return 404 for non-existent vulnerability', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .put('/api/vulnerabilities/123e4567-e89b-12d3-a456-426614174000')\r\n        .send({ title: 'Updated Title' })\r\n        .expect(404);\r\n\r\n      expect(response.body.message).toContain('Vulnerability not found');\r\n    });\r\n  });\r\n\r\n  describe('DELETE /api/vulnerabilities/:id', () => {\r\n    it('should delete an existing vulnerability', async () => {\r\n      const vulnerability = vulnerabilityRepository.create(mockVulnerability);\r\n      const savedVuln = await vulnerabilityRepository.save(vulnerability);\r\n\r\n      await request(app.getHttpServer())\r\n        .delete(`/api/vulnerabilities/${savedVuln.id}`)\r\n        .expect(204);\r\n\r\n      // Verify it was deleted from database\r\n      const deletedVuln = await vulnerabilityRepository.findOne({\r\n        where: { id: savedVuln.id },\r\n      });\r\n      expect(deletedVuln).toBeNull();\r\n    });\r\n\r\n    it('should return 404 for non-existent vulnerability', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .delete('/api/vulnerabilities/123e4567-e89b-12d3-a456-426614174000')\r\n        .expect(404);\r\n\r\n      expect(response.body.message).toContain('Vulnerability not found');\r\n    });\r\n  });\r\n\r\n  describe('POST /api/vulnerabilities/:id/assess', () => {\r\n    it('should create a vulnerability assessment', async () => {\r\n      const vulnerability = vulnerabilityRepository.create(mockVulnerability);\r\n      const savedVuln = await vulnerabilityRepository.save(vulnerability);\r\n\r\n      const assessmentData = {\r\n        assessmentType: 'manual',\r\n        findings: 'Confirmed vulnerability through manual testing',\r\n        assessedSeverity: 'high',\r\n        confidenceLevel: 90,\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post(`/api/vulnerabilities/${savedVuln.id}/assess`)\r\n        .send(assessmentData)\r\n        .expect(201);\r\n\r\n      expect(response.body).toHaveProperty('id');\r\n      expect(response.body.vulnerabilityId).toBe(savedVuln.id);\r\n      expect(response.body.findings).toBe(assessmentData.findings);\r\n\r\n      // Verify assessment was saved\r\n      const assessment = await assessmentRepository.findOne({\r\n        where: { vulnerabilityId: savedVuln.id },\r\n      });\r\n      expect(assessment).toBeDefined();\r\n      expect(assessment.findings).toBe(assessmentData.findings);\r\n    });\r\n  });\r\n\r\n  describe('POST /api/vulnerabilities/:id/exceptions', () => {\r\n    it('should create a vulnerability exception', async () => {\r\n      const vulnerability = vulnerabilityRepository.create(mockVulnerability);\r\n      const savedVuln = await vulnerabilityRepository.save(vulnerability);\r\n\r\n      const exceptionData = {\r\n        exceptionType: 'false_positive',\r\n        justification: 'This is a false positive due to configuration differences',\r\n        businessJustification: 'No business impact in our environment',\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post(`/api/vulnerabilities/${savedVuln.id}/exceptions`)\r\n        .send(exceptionData)\r\n        .expect(201);\r\n\r\n      expect(response.body).toHaveProperty('id');\r\n      expect(response.body.vulnerabilityId).toBe(savedVuln.id);\r\n      expect(response.body.exceptionType).toBe(exceptionData.exceptionType);\r\n\r\n      // Verify exception was saved\r\n      const exception = await exceptionRepository.findOne({\r\n        where: { vulnerabilityId: savedVuln.id },\r\n      });\r\n      expect(exception).toBeDefined();\r\n      expect(exception.justification).toBe(exceptionData.justification);\r\n    });\r\n  });\r\n});\r\n"], "version": 3}