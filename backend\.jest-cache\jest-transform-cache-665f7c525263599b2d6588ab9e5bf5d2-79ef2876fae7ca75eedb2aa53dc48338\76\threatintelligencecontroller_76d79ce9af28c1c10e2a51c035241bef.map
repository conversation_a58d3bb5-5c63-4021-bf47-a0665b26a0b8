{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\api\\controllers\\threat-intelligence.controller.ts", "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAgBwB;AACxB,6CAQyB;AAEzB,wGAA0J;AAE1J,0FAAqF;AACrF,oFAAgF;AAChF,gGAAmF;AACnF,0FAAoF;AACpF,0FAAoF;AACpF,0FAAoF;AACpF,0EAAqE;AACrE,8FAAwF;AAExF;;;GAGG;AAKI,IAAM,4BAA4B,GAAlC,MAAM,4BAA4B;IACvC,YACmB,yBAAoD;QAApD,8BAAyB,GAAzB,yBAAyB,CAA2B;IACpE,CAAC;IAEJ;;OAEG;IASG,AAAN,KAAK,CAAC,YAAY;QAChB,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,gBAAgB,EAAE,CAAC;IACjE,CAAC;IAED;;OAEG;IAcG,AAAN,KAAK,CAAC,wBAAwB,CACnB,SAAsC;QAO/C,MAAM,QAAQ,GAAqC;YACjD,IAAI,EAAE,SAAS,CAAC,IAAI;YACpB,KAAK,EAAE,SAAS,CAAC,KAAK;YACtB,WAAW,EAAE,SAAS,CAAC,WAAW;YAClC,UAAU,EAAE,SAAS,CAAC,UAAU;YAChC,QAAQ,EAAE,SAAS,CAAC,QAAQ;YAC5B,gBAAgB,EAAE,SAAS,CAAC,gBAAgB;YAC5C,IAAI,EAAE,SAAS,CAAC,IAAI;YACpB,UAAU,EAAE,SAAS,CAAC,UAAU;YAChC,SAAS,EAAE,SAAS,CAAC,SAAS,IAAI,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;gBACpD,SAAS,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;gBACxC,OAAO,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;aACrC,CAAC,CAAC,CAAC,SAAS;YACb,YAAY,EAAE,SAAS,CAAC,YAAY;YACpC,YAAY,EAAE,SAAS,CAAC,YAAY;YACpC,aAAa,EAAE,SAAS,CAAC,aAAa;YACtC,UAAU,EAAE,SAAS,CAAC,UAAU;YAChC,UAAU,EAAE,SAAS,CAAC,UAAU;YAChC,cAAc,EAAE,SAAS,CAAC,cAAc;YACxC,MAAM,EAAE,SAAS,CAAC,MAAM;YACxB,SAAS,EAAE,SAAS,CAAC,SAAS;SAC/B,CAAC;QAEF,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC;IACjF,CAAC;IAED;;OAEG;IAWG,AAAN,KAAK,CAAC,wBAAwB,CACb,OAAe,CAAC,EACf,QAAgB,EAAE;QAOlC,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,wBAAwB,CAAC;YACnE,IAAI;YACJ,KAAK;SACN,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IAWG,AAAN,KAAK,CAAC,yBAAyB,CACD,EAAU;QAEtC,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,yBAAyB,CAAC,EAAE,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IAcG,AAAN,KAAK,CAAC,wBAAwB,CACpB,SAAsC,EACnC,GAAQ;QAEnB,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3B,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,wBAAwB,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;IAC1F,CAAC;IAED;;OAEG;IAaG,AAAN,KAAK,CAAC,wBAAwB,CACA,EAAU,EAC9B,SAAsC,EACnC,GAAQ;QAEnB,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3B,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,wBAAwB,CAAC,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;IAC9F,CAAC;IAED;;OAEG;IAQG,AAAN,KAAK,CAAC,wBAAwB,CACA,EAAU,EAC3B,GAAQ;QAEnB,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3B,MAAM,IAAI,CAAC,yBAAyB,CAAC,wBAAwB,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IAqBG,AAAN,KAAK,CAAC,iBAAiB,CACO,EAAU,EAC9B,IAAuB;QAE/B,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,iBAAiB,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IAClF,CAAC;IAED;;OAEG;IAYG,AAAN,KAAK,CAAC,kBAAkB,CACM,EAAU,EAC9B,cAAoC;QAM5C,MAAM,QAAQ,GAAwB;YACpC,oBAAoB,EAAE,EAAE;YACxB,gBAAgB,EAAE,cAAc,CAAC,gBAAgB;YACjD,UAAU,EAAE,cAAc,CAAC,UAAU;YACrC,mBAAmB,EAAE,cAAc,CAAC,mBAAmB;SACxD,CAAC;QAEF,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;IAC3E,CAAC;IAED;;OAEG;IA0BG,AAAN,KAAK,CAAC,wBAAwB,CACpB,IAGP;QAED,MAAM,EAAE,MAAM,EAAE,QAAQ,GAAG,EAAE,EAAE,GAAG,IAAI,CAAC;QACvC,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,wBAAwB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IACzF,CAAC;IAED;;OAEG;IASG,AAAN,KAAK,CAAC,aAAa;QACjB,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,gBAAgB,EAAE,CAAC;IACjE,CAAC;IAED;;OAEG;IASG,AAAN,KAAK,CAAC,yBAAyB;QAC7B,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,gBAAgB,EAAE,CAAC;QAC9E,OAAO,aAAa,CAAC,YAAY,CAAC,WAAW,CAAC;IAChD,CAAC;IAED;;OAEG;IASG,AAAN,KAAK,CAAC,kBAAkB;QACtB,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,gBAAgB,EAAE,CAAC;QAC9E,OAAO,aAAa,CAAC,YAAY,CAAC,SAAS,CAAC;IAC9C,CAAC;CACF,CAAA;AAzUY,oEAA4B;AAgBjC;IARL,IAAA,YAAG,EAAC,WAAW,CAAC;IAChB,IAAA,uBAAK,EAAC,kBAAkB,EAAE,gBAAgB,EAAE,OAAO,CAAC;IACpD,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,wCAAwC,EAAE,CAAC;IACnE,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,uCAAuC;QACpD,IAAI,EAAE,MAAM;KACb,CAAC;;;wDACoB,OAAO,oBAAP,OAAO;gEAE5B;AAkBK;IAbL,IAAA,YAAG,EAAC,QAAQ,CAAC;IACb,IAAA,uBAAK,EAAC,kBAAkB,EAAE,gBAAgB,EAAE,OAAO,CAAC;IACpD,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,oDAAoD,EAAE,CAAC;IAC/E,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IACzD,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IAC1D,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC;IAClE,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC;IACjE,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IAC/D,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,oCAAoC;QACjD,IAAI,EAAE,MAAM;KACb,CAAC;IAEC,WAAA,IAAA,cAAK,GAAE,CAAA;;yDAAY,4DAA2B,oBAA3B,4DAA2B;wDAC9C,OAAO,oBAAP,OAAO;4EA8BT;AAeK;IAVL,IAAA,YAAG,GAAE;IACL,IAAA,uBAAK,EAAC,kBAAkB,EAAE,gBAAgB,EAAE,OAAO,CAAC;IACpD,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,6CAA6C,EAAE,CAAC;IACxE,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IACzD,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IAC1D,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,iDAAiD;QAC9D,IAAI,EAAE,CAAC,gEAA6B,CAAC;KACtC,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,MAAM,CAAC,CAAA;IACb,WAAA,IAAA,cAAK,EAAC,OAAO,CAAC,CAAA;;;wDACd,OAAO,oBAAP,OAAO;4EAUT;AAeK;IAVL,IAAA,YAAG,EAAC,KAAK,CAAC;IACV,IAAA,uBAAK,EAAC,kBAAkB,EAAE,gBAAgB,EAAE,OAAO,CAAC;IACpD,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,+BAA+B,EAAE,CAAC;IAC1D,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,wBAAwB,EAAE,CAAC;IAC/D,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,4CAA4C;QACzD,IAAI,EAAE,gEAA6B;KACpC,CAAC;IACD,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,+BAA+B,EAAE,CAAC;IAExE,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;;;wDAC1B,OAAO,oBAAP,OAAO;6EAET;AAkBK;IAbL,IAAA,aAAI,GAAE;IACN,IAAA,uBAAK,EAAC,gBAAgB,EAAE,OAAO,CAAC;IAChC,IAAA,iBAAQ,EAAC,mBAAU,CAAC,OAAO,CAAC;IAC5B,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC;IAC3D,IAAA,iBAAO,EAAC,EAAE,IAAI,EAAE,4DAA2B,EAAE,CAAC;IAC9C,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,0CAA0C;QACvD,IAAI,EAAE,gEAA6B;KACpC,CAAC;IACD,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,oBAAoB,EAAE,CAAC;IAC/D,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,oCAAoC,EAAE,CAAC;IAC/E,IAAA,iBAAQ,EAAC,IAAI,uBAAc,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IAE/C,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,gBAAO,GAAE,CAAA;;yDADS,4DAA2B,oBAA3B,4DAA2B;wDAE7C,OAAO,oBAAP,OAAO;4EAGT;AAiBK;IAZL,IAAA,YAAG,EAAC,KAAK,CAAC;IACV,IAAA,uBAAK,EAAC,gBAAgB,EAAE,OAAO,CAAC;IAChC,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,4BAA4B,EAAE,CAAC;IACvD,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,wBAAwB,EAAE,CAAC;IAC/D,IAAA,iBAAO,EAAC,EAAE,IAAI,EAAE,4DAA2B,EAAE,CAAC;IAC9C,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,0CAA0C;QACvD,IAAI,EAAE,gEAA6B;KACpC,CAAC;IACD,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,+BAA+B,EAAE,CAAC;IAC1E,IAAA,iBAAQ,EAAC,IAAI,uBAAc,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IAE/C,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;IAC1B,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,gBAAO,GAAE,CAAA;;iEADS,4DAA2B,oBAA3B,4DAA2B;wDAE7C,OAAO,oBAAP,OAAO;4EAGT;AAYK;IAPL,IAAA,eAAM,EAAC,KAAK,CAAC;IACb,IAAA,uBAAK,EAAC,gBAAgB,EAAE,OAAO,CAAC;IAChC,IAAA,iBAAQ,EAAC,mBAAU,CAAC,UAAU,CAAC;IAC/B,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,4BAA4B,EAAE,CAAC;IACvD,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,wBAAwB,EAAE,CAAC;IAC/D,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,0CAA0C,EAAE,CAAC;IACrF,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,+BAA+B,EAAE,CAAC;IAExE,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;IAC1B,WAAA,IAAA,gBAAO,GAAE,CAAA;;;wDACT,OAAO,oBAAP,OAAO;4EAGT;AAyBK;IApBL,IAAA,aAAI,EAAC,aAAa,CAAC;IACnB,IAAA,uBAAK,EAAC,kBAAkB,EAAE,gBAAgB,EAAE,OAAO,CAAC;IACpD,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,2CAA2C,EAAE,CAAC;IACtE,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,wBAAwB,EAAE,CAAC;IAC/D,IAAA,iBAAO,EAAC;QACP,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,OAAO,EAAE;oBACP,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,qBAAqB;iBACnC;aACF;SACF;KACF,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,mCAAmC;QAChD,IAAI,EAAE,gEAA6B;KACpC,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;IAC1B,WAAA,IAAA,aAAI,GAAE,CAAA;;;wDACN,OAAO,oBAAP,OAAO;qEAET;AAgBK;IAXL,IAAA,aAAI,EAAC,eAAe,CAAC;IACrB,IAAA,uBAAK,EAAC,kBAAkB,EAAE,gBAAgB,EAAE,OAAO,CAAC;IACpD,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,kCAAkC,EAAE,CAAC;IAC7D,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,wBAAwB,EAAE,CAAC;IAC/D,IAAA,iBAAO,EAAC,EAAE,IAAI,EAAE,6CAAoB,EAAE,CAAC;IACvC,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,oCAAoC;QACjD,IAAI,EAAE,MAAM;KACb,CAAC;IACD,IAAA,iBAAQ,EAAC,IAAI,uBAAc,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IAE/C,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;IAC1B,WAAA,IAAA,aAAI,GAAE,CAAA;;iEAAiB,6CAAoB,oBAApB,6CAAoB;wDAC3C,OAAO,oBAAP,OAAO;sEAaT;AA8BK;IAzBL,IAAA,aAAI,EAAC,QAAQ,CAAC;IACd,IAAA,uBAAK,EAAC,kBAAkB,EAAE,gBAAgB,EAAE,OAAO,CAAC;IACpD,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,iCAAiC,EAAE,CAAC;IAC5D,IAAA,iBAAO,EAAC;QACP,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC;oBAC7B,WAAW,EAAE,eAAe;iBAC7B;gBACD,QAAQ,EAAE;oBACR,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,4BAA4B;iBAC1C;aACF;YACD,QAAQ,EAAE,CAAC,QAAQ,CAAC;SACrB;KACF,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,4BAA4B;QACzC,IAAI,EAAE,MAAM;KACb,CAAC;IAEC,WAAA,IAAA,aAAI,GAAE,CAAA;;;wDAIN,OAAO,oBAAP,OAAO;4EAGT;AAaK;IARL,IAAA,YAAG,EAAC,gBAAgB,CAAC;IACrB,IAAA,uBAAK,EAAC,kBAAkB,EAAE,gBAAgB,EAAE,OAAO,CAAC;IACpD,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,oCAAoC,EAAE,CAAC;IAC/D,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,mCAAmC;QAChD,IAAI,EAAE,MAAM;KACb,CAAC;;;wDACqB,OAAO,oBAAP,OAAO;iEAE7B;AAaK;IARL,IAAA,YAAG,EAAC,oBAAoB,CAAC;IACzB,IAAA,uBAAK,EAAC,kBAAkB,EAAE,gBAAgB,EAAE,OAAO,CAAC;IACpD,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,8BAA8B,EAAE,CAAC;IACzD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,iDAAiD;QAC9D,IAAI,EAAE,MAAM;KACb,CAAC;;;wDACiC,OAAO,oBAAP,OAAO;6EAGzC;AAaK;IARL,IAAA,YAAG,EAAC,kBAAkB,CAAC;IACvB,IAAA,uBAAK,EAAC,kBAAkB,EAAE,gBAAgB,EAAE,OAAO,CAAC;IACpD,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC;IAClD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,0CAA0C;QACvD,IAAI,EAAE,MAAM;KACb,CAAC;;;wDAC0B,OAAO,oBAAP,OAAO;sEAGlC;uCAxUU,4BAA4B;IAJxC,IAAA,iBAAO,EAAC,qBAAqB,CAAC;IAC9B,IAAA,mBAAU,EAAC,yBAAyB,CAAC;IACrC,IAAA,kBAAS,EAAC,6BAAY,EAAE,wBAAU,CAAC;IACnC,IAAA,uBAAa,GAAE;yDAGgC,uDAAyB,oBAAzB,uDAAyB;GAF5D,4BAA4B,CAyUxC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\api\\controllers\\threat-intelligence.controller.ts"], "sourcesContent": ["import {\r\n  Controller,\r\n  Get,\r\n  Post,\r\n  Put,\r\n  Delete,\r\n  Body,\r\n  Param,\r\n  Query,\r\n  UseGuards,\r\n  HttpStatus,\r\n  HttpCode,\r\n  ParseUUIDPipe,\r\n  ValidationPipe,\r\n  UsePipes,\r\n  Request,\r\n} from '@nestjs/common';\r\nimport {\r\n  ApiTags,\r\n  ApiOperation,\r\n  ApiResponse,\r\n  ApiParam,\r\n  ApiQuery,\r\n  ApiBearerAuth,\r\n  ApiBody,\r\n} from '@nestjs/swagger';\r\n\r\nimport { ThreatIntelligenceService, ThreatIntelligenceSearchCriteria, CorrelationCriteria } from '../../application/services/threat-intelligence.service';\r\nimport { ThreatIntelligence } from '../../domain/entities/threat-intelligence.entity';\r\nimport { JwtAuthGuard } from '../../../../infrastructure/auth/guards/jwt-auth.guard';\r\nimport { RolesGuard } from '../../../../infrastructure/auth/guards/roles.guard';\r\nimport { Roles } from '../../../../infrastructure/auth/decorators/roles.decorator';\r\nimport { CreateThreatIntelligenceDto } from '../dto/create-threat-intelligence.dto';\r\nimport { UpdateThreatIntelligenceDto } from '../dto/update-threat-intelligence.dto';\r\nimport { ThreatIntelligenceSearchDto } from '../dto/threat-intelligence-search.dto';\r\nimport { ThreatCorrelationDto } from '../dto/threat-correlation.dto';\r\nimport { ThreatIntelligenceResponseDto } from '../dto/threat-intelligence-response.dto';\r\n\r\n/**\r\n * Controller for threat intelligence management\r\n * Provides RESTful endpoints for threat intelligence operations\r\n */\r\n@ApiTags('Threat Intelligence')\r\n@Controller('api/threat-intelligence')\r\n@UseGuards(JwtAuthGuard, RolesGuard)\r\n@ApiBearerAuth()\r\nexport class ThreatIntelligenceController {\r\n  constructor(\r\n    private readonly threatIntelligenceService: ThreatIntelligenceService,\r\n  ) {}\r\n\r\n  /**\r\n   * Get threat intelligence dashboard data\r\n   */\r\n  @Get('dashboard')\r\n  @Roles('security_analyst', 'threat_analyst', 'admin')\r\n  @ApiOperation({ summary: 'Get threat intelligence dashboard data' })\r\n  @ApiResponse({\r\n    status: 200,\r\n    description: 'Dashboard data retrieved successfully',\r\n    type: Object,\r\n  })\r\n  async getDashboard(): Promise<any> {\r\n    return await this.threatIntelligenceService.getDashboardData();\r\n  }\r\n\r\n  /**\r\n   * Search threat intelligence with advanced filtering\r\n   */\r\n  @Get('search')\r\n  @Roles('security_analyst', 'threat_analyst', 'admin')\r\n  @ApiOperation({ summary: 'Search threat intelligence with advanced filtering' })\r\n  @ApiQuery({ name: 'page', required: false, type: Number })\r\n  @ApiQuery({ name: 'limit', required: false, type: Number })\r\n  @ApiQuery({ name: 'threatTypes', required: false, type: [String] })\r\n  @ApiQuery({ name: 'severities', required: false, type: [String] })\r\n  @ApiQuery({ name: 'searchText', required: false, type: String })\r\n  @ApiResponse({\r\n    status: 200,\r\n    description: 'Threat intelligence search results',\r\n    type: Object,\r\n  })\r\n  async searchThreatIntelligence(\r\n    @Query() searchDto: ThreatIntelligenceSearchDto,\r\n  ): Promise<{\r\n    threats: ThreatIntelligence[];\r\n    total: number;\r\n    page: number;\r\n    totalPages: number;\r\n  }> {\r\n    const criteria: ThreatIntelligenceSearchCriteria = {\r\n      page: searchDto.page,\r\n      limit: searchDto.limit,\r\n      threatTypes: searchDto.threatTypes,\r\n      severities: searchDto.severities,\r\n      statuses: searchDto.statuses,\r\n      confidenceLevels: searchDto.confidenceLevels,\r\n      tags: searchDto.tags,\r\n      searchText: searchDto.searchText,\r\n      dateRange: searchDto.startDate && searchDto.endDate ? {\r\n        startDate: new Date(searchDto.startDate),\r\n        endDate: new Date(searchDto.endDate),\r\n      } : undefined,\r\n      riskScoreMin: searchDto.riskScoreMin,\r\n      riskScoreMax: searchDto.riskScoreMax,\r\n      threatActorId: searchDto.threatActorId,\r\n      campaignId: searchDto.campaignId,\r\n      dataSource: searchDto.dataSource,\r\n      includeExpired: searchDto.includeExpired,\r\n      sortBy: searchDto.sortBy,\r\n      sortOrder: searchDto.sortOrder,\r\n    };\r\n\r\n    return await this.threatIntelligenceService.searchThreatIntelligence(criteria);\r\n  }\r\n\r\n  /**\r\n   * Get all threat intelligence (paginated)\r\n   */\r\n  @Get()\r\n  @Roles('security_analyst', 'threat_analyst', 'admin')\r\n  @ApiOperation({ summary: 'Get all threat intelligence with pagination' })\r\n  @ApiQuery({ name: 'page', required: false, type: Number })\r\n  @ApiQuery({ name: 'limit', required: false, type: Number })\r\n  @ApiResponse({\r\n    status: 200,\r\n    description: 'Threat intelligence list retrieved successfully',\r\n    type: [ThreatIntelligenceResponseDto],\r\n  })\r\n  async getAllThreatIntelligence(\r\n    @Query('page') page: number = 1,\r\n    @Query('limit') limit: number = 50,\r\n  ): Promise<{\r\n    threats: ThreatIntelligence[];\r\n    total: number;\r\n    page: number;\r\n    totalPages: number;\r\n  }> {\r\n    return await this.threatIntelligenceService.searchThreatIntelligence({\r\n      page,\r\n      limit,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get threat intelligence by ID\r\n   */\r\n  @Get(':id')\r\n  @Roles('security_analyst', 'threat_analyst', 'admin')\r\n  @ApiOperation({ summary: 'Get threat intelligence by ID' })\r\n  @ApiParam({ name: 'id', description: 'Threat intelligence ID' })\r\n  @ApiResponse({\r\n    status: 200,\r\n    description: 'Threat intelligence retrieved successfully',\r\n    type: ThreatIntelligenceResponseDto,\r\n  })\r\n  @ApiResponse({ status: 404, description: 'Threat intelligence not found' })\r\n  async getThreatIntelligenceById(\r\n    @Param('id', ParseUUIDPipe) id: string,\r\n  ): Promise<ThreatIntelligence> {\r\n    return await this.threatIntelligenceService.getThreatIntelligenceById(id);\r\n  }\r\n\r\n  /**\r\n   * Create new threat intelligence\r\n   */\r\n  @Post()\r\n  @Roles('threat_analyst', 'admin')\r\n  @HttpCode(HttpStatus.CREATED)\r\n  @ApiOperation({ summary: 'Create new threat intelligence' })\r\n  @ApiBody({ type: CreateThreatIntelligenceDto })\r\n  @ApiResponse({\r\n    status: 201,\r\n    description: 'Threat intelligence created successfully',\r\n    type: ThreatIntelligenceResponseDto,\r\n  })\r\n  @ApiResponse({ status: 400, description: 'Invalid input data' })\r\n  @ApiResponse({ status: 409, description: 'Threat intelligence already exists' })\r\n  @UsePipes(new ValidationPipe({ transform: true }))\r\n  async createThreatIntelligence(\r\n    @Body() createDto: CreateThreatIntelligenceDto,\r\n    @Request() req: any,\r\n  ): Promise<ThreatIntelligence> {\r\n    const userId = req.user.id;\r\n    return await this.threatIntelligenceService.createThreatIntelligence(createDto, userId);\r\n  }\r\n\r\n  /**\r\n   * Update threat intelligence\r\n   */\r\n  @Put(':id')\r\n  @Roles('threat_analyst', 'admin')\r\n  @ApiOperation({ summary: 'Update threat intelligence' })\r\n  @ApiParam({ name: 'id', description: 'Threat intelligence ID' })\r\n  @ApiBody({ type: UpdateThreatIntelligenceDto })\r\n  @ApiResponse({\r\n    status: 200,\r\n    description: 'Threat intelligence updated successfully',\r\n    type: ThreatIntelligenceResponseDto,\r\n  })\r\n  @ApiResponse({ status: 404, description: 'Threat intelligence not found' })\r\n  @UsePipes(new ValidationPipe({ transform: true }))\r\n  async updateThreatIntelligence(\r\n    @Param('id', ParseUUIDPipe) id: string,\r\n    @Body() updateDto: UpdateThreatIntelligenceDto,\r\n    @Request() req: any,\r\n  ): Promise<ThreatIntelligence> {\r\n    const userId = req.user.id;\r\n    return await this.threatIntelligenceService.updateThreatIntelligence(id, updateDto, userId);\r\n  }\r\n\r\n  /**\r\n   * Delete threat intelligence\r\n   */\r\n  @Delete(':id')\r\n  @Roles('threat_analyst', 'admin')\r\n  @HttpCode(HttpStatus.NO_CONTENT)\r\n  @ApiOperation({ summary: 'Delete threat intelligence' })\r\n  @ApiParam({ name: 'id', description: 'Threat intelligence ID' })\r\n  @ApiResponse({ status: 204, description: 'Threat intelligence deleted successfully' })\r\n  @ApiResponse({ status: 404, description: 'Threat intelligence not found' })\r\n  async deleteThreatIntelligence(\r\n    @Param('id', ParseUUIDPipe) id: string,\r\n    @Request() req: any,\r\n  ): Promise<void> {\r\n    const userId = req.user.id;\r\n    await this.threatIntelligenceService.deleteThreatIntelligence(id, userId);\r\n  }\r\n\r\n  /**\r\n   * Record observation of threat intelligence\r\n   */\r\n  @Post(':id/observe')\r\n  @Roles('security_analyst', 'threat_analyst', 'admin')\r\n  @ApiOperation({ summary: 'Record observation of threat intelligence' })\r\n  @ApiParam({ name: 'id', description: 'Threat intelligence ID' })\r\n  @ApiBody({\r\n    schema: {\r\n      type: 'object',\r\n      properties: {\r\n        context: {\r\n          type: 'object',\r\n          description: 'Observation context',\r\n        },\r\n      },\r\n    },\r\n  })\r\n  @ApiResponse({\r\n    status: 200,\r\n    description: 'Observation recorded successfully',\r\n    type: ThreatIntelligenceResponseDto,\r\n  })\r\n  async recordObservation(\r\n    @Param('id', ParseUUIDPipe) id: string,\r\n    @Body() body: { context?: any },\r\n  ): Promise<ThreatIntelligence> {\r\n    return await this.threatIntelligenceService.recordObservation(id, body.context);\r\n  }\r\n\r\n  /**\r\n   * Find related threat intelligence\r\n   */\r\n  @Post(':id/correlate')\r\n  @Roles('security_analyst', 'threat_analyst', 'admin')\r\n  @ApiOperation({ summary: 'Find related threat intelligence' })\r\n  @ApiParam({ name: 'id', description: 'Threat intelligence ID' })\r\n  @ApiBody({ type: ThreatCorrelationDto })\r\n  @ApiResponse({\r\n    status: 200,\r\n    description: 'Related threats found successfully',\r\n    type: Object,\r\n  })\r\n  @UsePipes(new ValidationPipe({ transform: true }))\r\n  async findRelatedThreats(\r\n    @Param('id', ParseUUIDPipe) id: string,\r\n    @Body() correlationDto: ThreatCorrelationDto,\r\n  ): Promise<{\r\n    relatedThreats: ThreatIntelligence[];\r\n    correlationScore: number;\r\n    correlationType: string;\r\n  }[]> {\r\n    const criteria: CorrelationCriteria = {\r\n      threatIntelligenceId: id,\r\n      correlationTypes: correlationDto.correlationTypes,\r\n      timeWindow: correlationDto.timeWindow,\r\n      confidenceThreshold: correlationDto.confidenceThreshold,\r\n    };\r\n\r\n    return await this.threatIntelligenceService.findRelatedThreats(criteria);\r\n  }\r\n\r\n  /**\r\n   * Export threat intelligence\r\n   */\r\n  @Post('export')\r\n  @Roles('security_analyst', 'threat_analyst', 'admin')\r\n  @ApiOperation({ summary: 'Export threat intelligence data' })\r\n  @ApiBody({\r\n    schema: {\r\n      type: 'object',\r\n      properties: {\r\n        format: {\r\n          type: 'string',\r\n          enum: ['json', 'csv', 'stix'],\r\n          description: 'Export format',\r\n        },\r\n        criteria: {\r\n          type: 'object',\r\n          description: 'Search criteria for export',\r\n        },\r\n      },\r\n      required: ['format'],\r\n    },\r\n  })\r\n  @ApiResponse({\r\n    status: 200,\r\n    description: 'Data exported successfully',\r\n    type: Object,\r\n  })\r\n  async exportThreatIntelligence(\r\n    @Body() body: {\r\n      format: 'json' | 'csv' | 'stix';\r\n      criteria?: ThreatIntelligenceSearchCriteria;\r\n    },\r\n  ): Promise<any> {\r\n    const { format, criteria = {} } = body;\r\n    return await this.threatIntelligenceService.exportThreatIntelligence(criteria, format);\r\n  }\r\n\r\n  /**\r\n   * Get threat intelligence statistics\r\n   */\r\n  @Get('stats/overview')\r\n  @Roles('security_analyst', 'threat_analyst', 'admin')\r\n  @ApiOperation({ summary: 'Get threat intelligence statistics' })\r\n  @ApiResponse({\r\n    status: 200,\r\n    description: 'Statistics retrieved successfully',\r\n    type: Object,\r\n  })\r\n  async getStatistics(): Promise<any> {\r\n    return await this.threatIntelligenceService.getDashboardData();\r\n  }\r\n\r\n  /**\r\n   * Get threat type distribution\r\n   */\r\n  @Get('stats/threat-types')\r\n  @Roles('security_analyst', 'threat_analyst', 'admin')\r\n  @ApiOperation({ summary: 'Get threat type distribution' })\r\n  @ApiResponse({\r\n    status: 200,\r\n    description: 'Threat type distribution retrieved successfully',\r\n    type: Object,\r\n  })\r\n  async getThreatTypeDistribution(): Promise<any> {\r\n    const dashboardData = await this.threatIntelligenceService.getDashboardData();\r\n    return dashboardData.distribution.threatTypes;\r\n  }\r\n\r\n  /**\r\n   * Get top threat actors\r\n   */\r\n  @Get('stats/top-actors')\r\n  @Roles('security_analyst', 'threat_analyst', 'admin')\r\n  @ApiOperation({ summary: 'Get top threat actors' })\r\n  @ApiResponse({\r\n    status: 200,\r\n    description: 'Top threat actors retrieved successfully',\r\n    type: Object,\r\n  })\r\n  async getTopThreatActors(): Promise<any> {\r\n    const dashboardData = await this.threatIntelligenceService.getDashboardData();\r\n    return dashboardData.distribution.topActors;\r\n  }\r\n}\r\n"], "version": 3}