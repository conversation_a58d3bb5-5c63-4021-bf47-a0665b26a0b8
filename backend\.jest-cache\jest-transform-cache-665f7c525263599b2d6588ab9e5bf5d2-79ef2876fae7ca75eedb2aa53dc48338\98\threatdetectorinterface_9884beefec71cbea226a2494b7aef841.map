{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\services\\threat-detector.interface.ts", "mappings": ";;;AAwEA;;GAEG;AACH,IAAY,UAiBX;AAjBD,WAAY,UAAU;IACpB,iCAAmB,CAAA;IACnB,mCAAqB,CAAA;IACrB,uCAAyB,CAAA;IACzB,yBAAW,CAAA;IACX,+CAAiC,CAAA;IACjC,2BAAa,CAAA;IACb,yCAA2B,CAAA;IAC3B,6CAA+B,CAAA;IAC/B,yBAAW,CAAA;IACX,2DAA6C,CAAA;IAC7C,qDAAuC,CAAA;IACvC,mDAAqC,CAAA;IACrC,yCAA2B,CAAA;IAC3B,+CAAiC,CAAA;IACjC,yDAA2C,CAAA;IAC3C,iCAAmB,CAAA;AACrB,CAAC,EAjBW,UAAU,0BAAV,UAAU,QAiBrB;AAED;;GAEG;AACH,IAAY,cAaX;AAbD,WAAY,cAAc;IACxB,qCAAmB,CAAA;IACnB,uCAAqB,CAAA;IACrB,6CAA2B,CAAA;IAC3B,+BAAa,CAAA;IACb,uCAAqB,CAAA;IACrB,mDAAiC,CAAA;IACjC,iCAAe,CAAA;IACf,6BAAW,CAAA;IACX,mCAAiB,CAAA;IACjB,iCAAe,CAAA;IACf,6BAAW,CAAA;IACX,uCAAqB,CAAA;AACvB,CAAC,EAbW,cAAc,8BAAd,cAAc,QAazB;AAED;;GAEG;AACH,IAAY,oBAUX;AAVD,WAAY,oBAAoB;IAC9B,2DAAmC,CAAA;IACnC,mEAA2C,CAAA;IAC3C,6DAAqC,CAAA;IACrC,iEAAyC,CAAA;IACzC,mEAA2C,CAAA;IAC3C,+DAAuC,CAAA;IACvC,6DAAqC,CAAA;IACrC,qEAA6C,CAAA;IAC7C,yCAAiB,CAAA;AACnB,CAAC,EAVW,oBAAoB,oCAApB,oBAAoB,QAU/B;AAED;;GAEG;AACH,IAAY,qBAWX;AAXD,WAAY,qBAAqB;IAC/B,gEAAuC,CAAA;IACvC,oEAA2C,CAAA;IAC3C,8DAAqC,CAAA;IACrC,kEAAyC,CAAA;IACzC,oEAA2C,CAAA;IAC3C,4DAAmC,CAAA;IACnC,8DAAqC,CAAA;IACrC,8DAAqC,CAAA;IACrC,wCAAe,CAAA;IACf,8DAAqC,CAAA;AACvC,CAAC,EAXW,qBAAqB,qCAArB,qBAAqB,QAWhC;AAwBD;;GAEG;AACH,IAAY,aAcX;AAdD,WAAY,aAAa;IACvB,0CAAyB,CAAA;IACzB,kCAAiB,CAAA;IACjB,4BAAW,CAAA;IACX,wCAAuB,CAAA;IACvB,gCAAe,CAAA;IACf,0CAAyB,CAAA;IACzB,8CAA6B,CAAA;IAC7B,wCAAuB,CAAA;IACvB,8CAA6B,CAAA;IAC7B,4CAA2B,CAAA;IAC3B,gCAAe,CAAA;IACf,wCAAuB,CAAA;IACvB,kCAAiB,CAAA;AACnB,CAAC,EAdW,aAAa,6BAAb,aAAa,QAcxB;AA0BD;;GAEG;AACH,IAAY,eAOX;AAPD,WAAY,eAAe;IACzB,gDAA6B,CAAA;IAC7B,kDAA+B,CAAA;IAC/B,4CAAyB,CAAA;IACzB,sCAAmB,CAAA;IACnB,0CAAuB,CAAA;IACvB,sCAAmB,CAAA;AACrB,CAAC,EAPW,eAAe,+BAAf,eAAe,QAO1B;AAED;;GAEG;AACH,IAAY,mBAOX;AAPD,WAAY,mBAAmB;IAC7B,0CAAmB,CAAA;IACnB,oDAA6B,CAAA;IAC7B,4CAAqB,CAAA;IACrB,wCAAiB,CAAA;IACjB,8CAAuB,CAAA;IACvB,8CAAuB,CAAA;AACzB,CAAC,EAPW,mBAAmB,mCAAnB,mBAAmB,QAO9B;AAED;;GAEG;AACH,IAAY,cAQX;AARD,WAAY,cAAc;IACxB,mDAAiC,CAAA;IACjC,iDAA+B,CAAA;IAC/B,uCAAqB,CAAA;IACrB,+CAA6B,CAAA;IAC7B,+CAA6B,CAAA;IAC7B,6DAA2C,CAAA;IAC3C,iEAA+C,CAAA;AACjD,CAAC,EARW,cAAc,8BAAd,cAAc,QAQzB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\services\\threat-detector.interface.ts"], "sourcesContent": ["import { Event } from '../../entities/event.entity';\r\nimport { NormalizedEvent } from '../../entities/normalized-event.entity';\r\nimport { EnrichedEvent } from '../../entities/enriched-event.entity';\r\nimport { CorrelatedEvent } from '../../entities/correlated-event.entity';\r\nimport { EventSeverity } from '../../enums/event-severity.enum';\r\nimport { EventType } from '../../enums/event-type.enum';\r\nimport { ConfidenceLevel } from '../../enums/confidence-level.enum';\r\n\r\n/**\r\n * Threat Analysis Result\r\n */\r\nexport interface ThreatAnalysis {\r\n  /** Detected threats */\r\n  threats: DetectedThreat[];\r\n  /** Overall analysis confidence (0-100) */\r\n  confidence: number;\r\n  /** Analysis timestamp */\r\n  analysisTime: Date;\r\n  /** Analysis duration in milliseconds */\r\n  analysisDurationMs: number;\r\n  /** Analysis method used */\r\n  analysisMethod: ThreatAnalysisMethod;\r\n  /** Risk score (0-100) */\r\n  riskScore: number;\r\n  /** Threat indicators found */\r\n  indicators: ThreatIndicator[];\r\n  /** Analysis metadata */\r\n  metadata?: Record<string, any>;\r\n  /** Recommendations */\r\n  recommendations: string[];\r\n  /** False positive probability (0-100) */\r\n  falsePositiveProbability: number;\r\n}\r\n\r\n/**\r\n * Detected Threat Interface\r\n */\r\nexport interface DetectedThreat {\r\n  /** Threat identifier */\r\n  id: string;\r\n  /** Threat type */\r\n  type: ThreatType;\r\n  /** Threat category */\r\n  category: ThreatCategory;\r\n  /** Threat severity */\r\n  severity: EventSeverity;\r\n  /** Threat name/title */\r\n  name: string;\r\n  /** Threat description */\r\n  description: string;\r\n  /** Threat indicators */\r\n  indicators: ThreatIndicator[];\r\n  /** Detection confidence (0-100) */\r\n  confidence: number;\r\n  /** Detection source */\r\n  source: ThreatDetectionSource;\r\n  /** Detection timestamp */\r\n  timestamp: Date;\r\n  /** MITRE ATT&CK tactics */\r\n  tactics?: string[];\r\n  /** MITRE ATT&CK techniques */\r\n  techniques?: string[];\r\n  /** CVE identifiers if applicable */\r\n  cveIds?: string[];\r\n  /** Threat actor information */\r\n  threatActor?: ThreatActor;\r\n  /** Kill chain stage */\r\n  killChainStage?: KillChainStage;\r\n  /** Threat metadata */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Threat Type Enum\r\n */\r\nexport enum ThreatType {\r\n  MALWARE = 'MALWARE',\r\n  PHISHING = 'PHISHING',\r\n  RANSOMWARE = 'RANSOMWARE',\r\n  APT = 'APT',\r\n  INSIDER_THREAT = 'INSIDER_THREAT',\r\n  DDoS = 'DDOS',\r\n  BRUTE_FORCE = 'BRUTE_FORCE',\r\n  SQL_INJECTION = 'SQL_INJECTION',\r\n  XSS = 'XSS',\r\n  PRIVILEGE_ESCALATION = 'PRIVILEGE_ESCALATION',\r\n  DATA_EXFILTRATION = 'DATA_EXFILTRATION',\r\n  LATERAL_MOVEMENT = 'LATERAL_MOVEMENT',\r\n  PERSISTENCE = 'PERSISTENCE',\r\n  RECONNAISSANCE = 'RECONNAISSANCE',\r\n  COMMAND_AND_CONTROL = 'COMMAND_AND_CONTROL',\r\n  UNKNOWN = 'UNKNOWN',\r\n}\r\n\r\n/**\r\n * Threat Category Enum\r\n */\r\nexport enum ThreatCategory {\r\n  NETWORK = 'NETWORK',\r\n  ENDPOINT = 'ENDPOINT',\r\n  APPLICATION = 'APPLICATION',\r\n  DATA = 'DATA',\r\n  IDENTITY = 'IDENTITY',\r\n  INFRASTRUCTURE = 'INFRASTRUCTURE',\r\n  CLOUD = 'CLOUD',\r\n  IOT = 'IOT',\r\n  MOBILE = 'MOBILE',\r\n  EMAIL = 'EMAIL',\r\n  WEB = 'WEB',\r\n  PHYSICAL = 'PHYSICAL',\r\n}\r\n\r\n/**\r\n * Threat Analysis Method Enum\r\n */\r\nexport enum ThreatAnalysisMethod {\r\n  SIGNATURE_BASED = 'SIGNATURE_BASED',\r\n  BEHAVIORAL_ANALYSIS = 'BEHAVIORAL_ANALYSIS',\r\n  MACHINE_LEARNING = 'MACHINE_LEARNING',\r\n  HEURISTIC_ANALYSIS = 'HEURISTIC_ANALYSIS',\r\n  THREAT_INTELLIGENCE = 'THREAT_INTELLIGENCE',\r\n  ANOMALY_DETECTION = 'ANOMALY_DETECTION',\r\n  PATTERN_MATCHING = 'PATTERN_MATCHING',\r\n  STATISTICAL_ANALYSIS = 'STATISTICAL_ANALYSIS',\r\n  HYBRID = 'HYBRID',\r\n}\r\n\r\n/**\r\n * Threat Detection Source Enum\r\n */\r\nexport enum ThreatDetectionSource {\r\n  INTERNAL_ANALYSIS = 'INTERNAL_ANALYSIS',\r\n  THREAT_INTELLIGENCE = 'THREAT_INTELLIGENCE',\r\n  MACHINE_LEARNING = 'MACHINE_LEARNING',\r\n  SIGNATURE_DATABASE = 'SIGNATURE_DATABASE',\r\n  BEHAVIORAL_ANALYSIS = 'BEHAVIORAL_ANALYSIS',\r\n  COMMUNITY_FEEDS = 'COMMUNITY_FEEDS',\r\n  COMMERCIAL_FEEDS = 'COMMERCIAL_FEEDS',\r\n  GOVERNMENT_FEEDS = 'GOVERNMENT_FEEDS',\r\n  OSINT = 'OSINT',\r\n  SANDBOX_ANALYSIS = 'SANDBOX_ANALYSIS',\r\n}\r\n\r\n/**\r\n * Threat Indicator Interface\r\n */\r\nexport interface ThreatIndicator {\r\n  /** Indicator type */\r\n  type: IndicatorType;\r\n  /** Indicator value */\r\n  value: string;\r\n  /** Indicator confidence (0-100) */\r\n  confidence: number;\r\n  /** Indicator source */\r\n  source: string;\r\n  /** First seen timestamp */\r\n  firstSeen: Date;\r\n  /** Last seen timestamp */\r\n  lastSeen: Date;\r\n  /** Indicator context */\r\n  context?: string;\r\n  /** Indicator metadata */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Indicator Type Enum\r\n */\r\nexport enum IndicatorType {\r\n  IP_ADDRESS = 'IP_ADDRESS',\r\n  DOMAIN = 'DOMAIN',\r\n  URL = 'URL',\r\n  FILE_HASH = 'FILE_HASH',\r\n  EMAIL = 'EMAIL',\r\n  USER_AGENT = 'USER_AGENT',\r\n  REGISTRY_KEY = 'REGISTRY_KEY',\r\n  FILE_PATH = 'FILE_PATH',\r\n  PROCESS_NAME = 'PROCESS_NAME',\r\n  CERTIFICATE = 'CERTIFICATE',\r\n  MUTEX = 'MUTEX',\r\n  YARA_RULE = 'YARA_RULE',\r\n  CUSTOM = 'CUSTOM',\r\n}\r\n\r\n/**\r\n * Threat Actor Interface\r\n */\r\nexport interface ThreatActor {\r\n  /** Actor identifier */\r\n  id: string;\r\n  /** Actor name */\r\n  name: string;\r\n  /** Actor aliases */\r\n  aliases: string[];\r\n  /** Actor type */\r\n  type: ThreatActorType;\r\n  /** Actor motivation */\r\n  motivation: string[];\r\n  /** Actor sophistication level */\r\n  sophistication: SophisticationLevel;\r\n  /** Actor origin country */\r\n  originCountry?: string;\r\n  /** Actor first seen */\r\n  firstSeen?: Date;\r\n  /** Actor last seen */\r\n  lastSeen?: Date;\r\n}\r\n\r\n/**\r\n * Threat Actor Type Enum\r\n */\r\nexport enum ThreatActorType {\r\n  NATION_STATE = 'NATION_STATE',\r\n  CYBERCRIMINAL = 'CYBERCRIMINAL',\r\n  HACKTIVIST = 'HACKTIVIST',\r\n  INSIDER = 'INSIDER',\r\n  TERRORIST = 'TERRORIST',\r\n  UNKNOWN = 'UNKNOWN',\r\n}\r\n\r\n/**\r\n * Sophistication Level Enum\r\n */\r\nexport enum SophisticationLevel {\r\n  MINIMAL = 'MINIMAL',\r\n  INTERMEDIATE = 'INTERMEDIATE',\r\n  ADVANCED = 'ADVANCED',\r\n  EXPERT = 'EXPERT',\r\n  INNOVATOR = 'INNOVATOR',\r\n  STRATEGIC = 'STRATEGIC',\r\n}\r\n\r\n/**\r\n * Kill Chain Stage Enum\r\n */\r\nexport enum KillChainStage {\r\n  RECONNAISSANCE = 'RECONNAISSANCE',\r\n  WEAPONIZATION = 'WEAPONIZATION',\r\n  DELIVERY = 'DELIVERY',\r\n  EXPLOITATION = 'EXPLOITATION',\r\n  INSTALLATION = 'INSTALLATION',\r\n  COMMAND_AND_CONTROL = 'COMMAND_AND_CONTROL',\r\n  ACTIONS_ON_OBJECTIVES = 'ACTIONS_ON_OBJECTIVES',\r\n}\r\n\r\n/**\r\n * Threat Detection Configuration\r\n */\r\nexport interface ThreatDetectionConfig {\r\n  /** Enable real-time detection */\r\n  enableRealTimeDetection?: boolean;\r\n  /** Detection sensitivity (0-100) */\r\n  sensitivity?: number;\r\n  /** Minimum confidence threshold */\r\n  minConfidenceThreshold?: number;\r\n  /** Maximum analysis time in milliseconds */\r\n  maxAnalysisTimeMs?: number;\r\n  /** Enable machine learning models */\r\n  enableMLModels?: boolean;\r\n  /** Enable behavioral analysis */\r\n  enableBehavioralAnalysis?: boolean;\r\n  /** Enable threat intelligence feeds */\r\n  enableThreatIntel?: boolean;\r\n  /** Custom detection rules */\r\n  customRules?: ThreatDetectionRule[];\r\n  /** Whitelist patterns */\r\n  whitelistPatterns?: string[];\r\n  /** Blacklist patterns */\r\n  blacklistPatterns?: string[];\r\n}\r\n\r\n/**\r\n * Threat Detection Rule Interface\r\n */\r\nexport interface ThreatDetectionRule {\r\n  /** Rule identifier */\r\n  id: string;\r\n  /** Rule name */\r\n  name: string;\r\n  /** Rule description */\r\n  description: string;\r\n  /** Rule pattern */\r\n  pattern: string;\r\n  /** Rule type */\r\n  type: ThreatType;\r\n  /** Rule severity */\r\n  severity: EventSeverity;\r\n  /** Rule confidence */\r\n  confidence: number;\r\n  /** Rule enabled status */\r\n  enabled: boolean;\r\n  /** Rule metadata */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Threat Detection Context\r\n */\r\nexport interface ThreatDetectionContext {\r\n  /** Detection request ID */\r\n  requestId: string;\r\n  /** Detection configuration */\r\n  config: ThreatDetectionConfig;\r\n  /** User context */\r\n  userContext?: {\r\n    userId: string;\r\n    tenantId: string;\r\n  };\r\n  /** Detection metadata */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Threat Detector Interface\r\n * \r\n * Defines the contract for threat detection and analysis capabilities.\r\n * Supports multiple detection methods and provides comprehensive threat intelligence.\r\n */\r\nexport interface ThreatDetector {\r\n  /**\r\n   * Analyze a single event for threats\r\n   * @param event - Event to analyze\r\n   * @param context - Detection context\r\n   * @returns Threat analysis result\r\n   */\r\n  analyzeEvent(event: Event, context?: ThreatDetectionContext): Promise<ThreatAnalysis>;\r\n\r\n  /**\r\n   * Analyze normalized event for threats\r\n   * @param normalizedEvent - Normalized event to analyze\r\n   * @param context - Detection context\r\n   * @returns Threat analysis result\r\n   */\r\n  analyzeNormalizedEvent(normalizedEvent: NormalizedEvent, context?: ThreatDetectionContext): Promise<ThreatAnalysis>;\r\n\r\n  /**\r\n   * Analyze enriched event for threats\r\n   * @param enrichedEvent - Enriched event to analyze\r\n   * @param context - Detection context\r\n   * @returns Threat analysis result\r\n   */\r\n  analyzeEnrichedEvent(enrichedEvent: EnrichedEvent, context?: ThreatDetectionContext): Promise<ThreatAnalysis>;\r\n\r\n  /**\r\n   * Analyze correlated event for threats\r\n   * @param correlatedEvent - Correlated event to analyze\r\n   * @param context - Detection context\r\n   * @returns Threat analysis result\r\n   */\r\n  analyzeCorrelatedEvent(correlatedEvent: CorrelatedEvent, context?: ThreatDetectionContext): Promise<ThreatAnalysis>;\r\n\r\n  /**\r\n   * Analyze multiple events for threats\r\n   * @param events - Events to analyze\r\n   * @param context - Detection context\r\n   * @returns Array of threat analysis results\r\n   */\r\n  analyzeEvents(events: Event[], context?: ThreatDetectionContext): Promise<ThreatAnalysis[]>;\r\n\r\n  /**\r\n   * Detect threat patterns across multiple events\r\n   * @param events - Events to analyze for patterns\r\n   * @param context - Detection context\r\n   * @returns Detected threat patterns\r\n   */\r\n  detectPatterns(events: Event[], context?: ThreatDetectionContext): Promise<DetectedThreat[]>;\r\n\r\n  /**\r\n   * Validate a detected threat\r\n   * @param threat - Threat to validate\r\n   * @param context - Detection context\r\n   * @returns Validation result\r\n   */\r\n  validateThreat(threat: DetectedThreat, context?: ThreatDetectionContext): Promise<{\r\n    isValid: boolean;\r\n    confidence: number;\r\n    validationReasons: string[];\r\n  }>;\r\n\r\n  /**\r\n   * Enrich threat with additional intelligence\r\n   * @param threat - Threat to enrich\r\n   * @param context - Detection context\r\n   * @returns Enriched threat\r\n   */\r\n  enrichThreat(threat: DetectedThreat, context?: ThreatDetectionContext): Promise<DetectedThreat>;\r\n\r\n  /**\r\n   * Get threat intelligence for indicators\r\n   * @param indicators - Threat indicators to lookup\r\n   * @param context - Detection context\r\n   * @returns Threat intelligence data\r\n   */\r\n  getThreatIntelligence(indicators: ThreatIndicator[], context?: ThreatDetectionContext): Promise<{\r\n    indicators: ThreatIndicator[];\r\n    threats: DetectedThreat[];\r\n    confidence: number;\r\n    sources: string[];\r\n  }>;\r\n\r\n  /**\r\n   * Update threat detection rules\r\n   * @param rules - New or updated detection rules\r\n   * @param context - Detection context\r\n   * @returns Update result\r\n   */\r\n  updateDetectionRules(rules: ThreatDetectionRule[], context?: ThreatDetectionContext): Promise<{\r\n    updated: number;\r\n    failed: number;\r\n    errors: string[];\r\n  }>;\r\n\r\n  /**\r\n   * Get current threat detection configuration\r\n   * @returns Current detection configuration\r\n   */\r\n  getDetectionConfig(): Promise<ThreatDetectionConfig>;\r\n\r\n  /**\r\n   * Update threat detection configuration\r\n   * @param config - New detection configuration\r\n   * @returns Configuration update result\r\n   */\r\n  updateDetectionConfig(config: Partial<ThreatDetectionConfig>): Promise<{\r\n    updated: boolean;\r\n    config: ThreatDetectionConfig;\r\n    timestamp: Date;\r\n  }>;\r\n\r\n  /**\r\n   * Get threat detection health status\r\n   * @returns Detection system health\r\n   */\r\n  getDetectionHealth(): Promise<{\r\n    status: 'healthy' | 'degraded' | 'unhealthy';\r\n    detectionEngines: Record<string, {\r\n      status: 'healthy' | 'degraded' | 'unhealthy';\r\n      latency: number;\r\n      accuracy: number;\r\n      lastUpdate: Date;\r\n    }>;\r\n    threatIntelFeeds: Record<string, {\r\n      status: 'healthy' | 'degraded' | 'unhealthy';\r\n      lastUpdate: Date;\r\n      recordCount: number;\r\n    }>;\r\n    lastHealthCheck: Date;\r\n  }>;\r\n} "], "version": 3}