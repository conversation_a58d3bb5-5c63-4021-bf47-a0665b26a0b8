{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\threat-indicators\\threat-signature.value-object.ts", "mappings": ";;;AAAA,oGAA+F;AAC/F,6EAAoE;AAEpE;;GAEG;AACH,IAAY,aAWX;AAXD,WAAY,aAAa;IACvB,8BAAa,CAAA;IACb,gCAAe,CAAA;IACf,sCAAqB,CAAA;IACrB,gCAAe,CAAA;IACf,kCAAiB,CAAA;IACjB,gCAAe,CAAA;IACf,8BAAa,CAAA;IACb,0CAAyB,CAAA;IACzB,oCAAmB,CAAA;IACnB,sCAAqB,CAAA;AACvB,CAAC,EAXW,aAAa,6BAAb,aAAa,QAWxB;AAED;;GAEG;AACH,IAAY,iBAWX;AAXD,WAAY,iBAAiB;IAC3B,wCAAmB,CAAA;IACnB,wCAAmB,CAAA;IACnB,wDAAmC,CAAA;IACnC,kDAA6B,CAAA;IAC7B,0DAAqC,CAAA;IACrC,gDAA2B,CAAA;IAC3B,kEAA6C,CAAA;IAC7C,wDAAmC,CAAA;IACnC,sDAAiC,CAAA;IACjC,sDAAiC,CAAA;AACnC,CAAC,EAXW,iBAAiB,iCAAjB,iBAAiB,QAW5B;AAED;;GAEG;AACH,IAAY,iBAMX;AAND,WAAY,iBAAiB;IAC3B,kCAAa,CAAA;IACb,gCAAW,CAAA;IACX,sCAAiB,CAAA;IACjB,kCAAa,CAAA;IACb,0CAAqB,CAAA;AACvB,CAAC,EANW,iBAAiB,iCAAjB,iBAAiB,QAM5B;AAsGD;;;;;;;;;;;;;;;;;;GAkBG;AACH,MAAa,eAAgB,SAAQ,mCAAqC;IAKxE,YAAY,KAA2B;QACrC,KAAK,CAAC,KAAK,CAAC,CAAC;IACf,CAAC;IAES,QAAQ;QAChB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1D,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9D,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7D,MAAM,IAAI,KAAK,CAAC,2BAA2B,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;QACjE,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrE,MAAM,IAAI,KAAK,CAAC,+BAA+B,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrE,MAAM,IAAI,KAAK,CAAC,+BAA+B,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpE,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,eAAe,CAAC,kBAAkB,EAAE,CAAC;YACpE,MAAM,IAAI,KAAK,CAAC,+CAA+C,eAAe,CAAC,kBAAkB,aAAa,CAAC,CAAC;QAClH,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,uCAAe,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC;YACrE,MAAM,IAAI,KAAK,CAAC,6BAA6B,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpE,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClE,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,iBAAiB,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,iBAAiB,GAAG,CAAC,EAAE,CAAC;YACzG,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACjE,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,gBAAgB,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;YACvG,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC;YAC/C,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;YAC5C,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,GAAG,GAAG,EAAE,CAAC;YACnF,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC;YAC1C,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;YAC/G,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC;YACzC,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM,CACX,EAAU,EACV,IAAY,EACZ,IAAmB,EACnB,QAA2B,EAC3B,QAA2B,EAC3B,OAAe,EACf,WAAmB,EACnB,MAAc,EACd,OAUC;QAED,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QAEvB,MAAM,KAAK,GAAyB;YAClC,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE;YACb,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE;YACjB,IAAI;YACJ,QAAQ;YACR,QAAQ;YACR,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE;YACvB,WAAW,EAAE,WAAW,CAAC,IAAI,EAAE;YAC/B,UAAU,EAAE,OAAO,EAAE,UAAU,IAAI,uCAAe,CAAC,MAAM;YACzD,OAAO,EAAE,OAAO,EAAE,OAAO,IAAI,KAAK;YAClC,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE;YACrB,SAAS,EAAE,GAAG;YACd,UAAU,EAAE,GAAG;YACf,IAAI,EAAE,OAAO,EAAE,IAAI,IAAI,EAAE;YACzB,qBAAqB,EAAE,OAAO,EAAE,qBAAqB,IAAI,EAAE;YAC3D,eAAe,EAAE,OAAO,EAAE,eAAe,IAAI,EAAE;YAC/C,cAAc,EAAE;gBACd,MAAM,EAAE,OAAO,EAAE,eAAe,IAAI,eAAe;gBACnD,UAAU,EAAE,OAAO,EAAE,mBAAmB,IAAI,EAAE;gBAC9C,iBAAiB,EAAE,IAAI,EAAE,aAAa;gBACtC,gBAAgB,EAAE,IAAI,EAAE,cAAc;aACvC;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,CAAC;gBACjB,WAAW,EAAE,CAAC;gBACd,QAAQ,EAAE,CAAC;gBACX,UAAU,EAAE,CAAC;aACd;YACD,UAAU,EAAE;gBACV,WAAW,EAAE,KAAK;aACnB;YACD,KAAK,EAAE;gBACL,eAAe,EAAE,CAAC;gBAClB,aAAa,EAAE,CAAC;gBAChB,cAAc,EAAE,CAAC;gBACjB,aAAa,EAAE,CAAC;aACjB;YACD,QAAQ,EAAE;gBACR,MAAM,EAAE,OAAO,EAAE,MAAM,IAAI,QAAQ;gBACnC,UAAU,EAAE,OAAO,EAAE,UAAU,IAAI,EAAE;gBACrC,iBAAiB,EAAE,EAAE;aACtB;SACF,CAAC;QAEF,OAAO,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAI,EAAE;QACJ,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAI,IAAI;QACN,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAI,qBAAqB;QACvB,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,IAAI,cAAc;QAChB,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,MAAM,gBAAgB,GAAG;YACvB,CAAC,uCAAe,CAAC,OAAO,CAAC,EAAE,CAAC;YAC5B,CAAC,uCAAe,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7B,CAAC,uCAAe,CAAC,GAAG,CAAC,EAAE,CAAC;YACxB,CAAC,uCAAe,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3B,CAAC,uCAAe,CAAC,IAAI,CAAC,EAAE,CAAC;YACzB,CAAC,uCAAe,CAAC,SAAS,CAAC,EAAE,CAAC;YAC9B,CAAC,uCAAe,CAAC,SAAS,CAAC,EAAE,CAAC;SAC/B,CAAC;QAEF,MAAM,kBAAkB,GAAG,gBAAgB,CAAC,eAAe,CAAC,6BAA6B,CAAC,CAAC;QAC3F,MAAM,sBAAsB,GAAG,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAExE,OAAO,IAAI,CAAC,WAAW,EAAE;YAClB,sBAAsB,IAAI,kBAAkB;YAC5C,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,iBAAiB,IAAI,eAAe,CAAC,uBAAuB,CAAC;IACjG,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,cAAc,GAAG,GAAG,IAAI,kBAAkB;YAClE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,GAAG,EAAE,IAAI,oBAAoB;YAC7D,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC,4BAA4B;IAChF,CAAC;IAED;;OAEG;IACH,WAAW;QACT,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,KAAK,CAAC,EAAE,CAAC;YAC5C,OAAO,KAAK,CAAC,CAAC,qCAAqC;QACrD,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC;QACrF,OAAO,QAAQ,IAAI,GAAG,CAAC,CAAC,yBAAyB;IACnD,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,KAAK,CAAC,EAAE,CAAC;YAC5C,OAAO,CAAC,CAAC;QACX,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC;QACrF,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,GAAG,CAAC,CAAC,uBAAuB;QAChF,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,EAAE,GAAG,GAAG,CAAC,CAAC,uBAAuB;QAElF,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,GAAG,EAAE,CAAC,GAAG,CAAC,eAAe,GAAG,EAAE,CAAC,GAAG,CAAC,gBAAgB,GAAG,EAAE,CAAC,CAAC,CAAC;IACxF,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,MAAM,gBAAgB,GAAG;YACvB,CAAC,uCAAe,CAAC,OAAO,CAAC,EAAE,CAAC;YAC5B,CAAC,uCAAe,CAAC,QAAQ,CAAC,EAAE,EAAE;YAC9B,CAAC,uCAAe,CAAC,GAAG,CAAC,EAAE,EAAE;YACzB,CAAC,uCAAe,CAAC,MAAM,CAAC,EAAE,EAAE;YAC5B,CAAC,uCAAe,CAAC,IAAI,CAAC,EAAE,EAAE;YAC1B,CAAC,uCAAe,CAAC,SAAS,CAAC,EAAE,EAAE;YAC/B,CAAC,uCAAe,CAAC,SAAS,CAAC,EAAE,GAAG;SACjC,CAAC;QAEF,OAAO,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,8DAA8D;QAC9D,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,cAAc,KAAK,CAAC;YAC5C,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,KAAK,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC;YAC7C,OAAO,EAAE,CAAC,CAAC,yCAAyC;QACtD,CAAC;QAED,IAAI,KAAK,GAAG,GAAG,CAAC;QAEhB,gCAAgC;QAChC,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,cAAc,GAAG,IAAI,EAAE,CAAC;YAClD,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;aAAM,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,cAAc,GAAG,GAAG,EAAE,CAAC;YACxD,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,0BAA0B;QAC1B,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,GAAG,EAAE,EAAE,CAAC;YAC1C,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;aAAM,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,GAAG,EAAE,EAAE,CAAC;YACjD,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,yBAAyB;QACzB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,GAAG,KAAK,EAAE,CAAC;YAC/C,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;aAAM,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,GAAG,IAAI,EAAE,CAAC;YACrD,KAAK,IAAI,CAAC,CAAC;QACb,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,MAAM,kBAAkB,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAExD,IAAI,kBAAkB,IAAI,EAAE;YAAE,OAAO,WAAW,CAAC;QACjD,IAAI,kBAAkB,IAAI,EAAE;YAAE,OAAO,MAAM,CAAC;QAC5C,IAAI,kBAAkB,IAAI,EAAE;YAAE,OAAO,MAAM,CAAC;QAC5C,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAC5F,CAAC;IAED;;OAEG;IACH,WAAW;QACT,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAC1B,MAAM,aAAa,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAEnD,sCAAsC;QACtC,OAAO,CAAC,GAAG,GAAG,GAAG,IAAI,aAAa,GAAG,EAAE,CAAC,IAAI,2CAA2C;YAChF,CAAC,GAAG,GAAG,GAAG,IAAI,aAAa,GAAG,EAAE,CAAC,IAAI,kDAAkD;YACvF,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,iBAAiB,GAAG,eAAe,CAAC,uBAAuB,CAAC;IAChG,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;YACxB,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACrC,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAC;YAC9B,OAAO,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAC;YAC9B,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,GAAG,EAAE,EAAE,CAAC;YAClE,OAAO,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;YACvB,OAAO,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,iBAAiB,GAAG,IAAI,EAAE,CAAC;YACxD,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QAC7C,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;YAClB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;YACtB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;YACtB,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;YAC9B,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;YAC9B,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;YAC5B,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;YACpC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;YAClC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;YAC5B,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;YAC1B,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE;YAC9C,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,WAAW,EAAE;YAChD,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;YACtB,qBAAqB,EAAE,IAAI,CAAC,MAAM,CAAC,qBAAqB;YACxD,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe;YAC5C,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc;YAC1C,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;YACpC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;YAClC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;YACxB,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;YAC9B,QAAQ,EAAE;gBACR,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE;gBAC/B,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE;gBAC3C,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE;gBAC3C,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE;gBAC/B,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,EAAE;gBAChD,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;gBAC1C,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,EAAE;gBAC5C,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE;gBACtC,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE;gBAClB,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE;gBAC/B,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,EAAE;aACjD;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,IAAyB;QACvC,MAAM,KAAK,GAAyB;YAClC,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;YACnC,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;YACrC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,qBAAqB,EAAE,IAAI,CAAC,qBAAqB;YACjD,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC;QAEF,OAAO,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC;IACpC,CAAC;;AAriBH,0CAsiBC;AAriByB,kCAAkB,GAAG,MAAM,CAAC,CAAC,QAAQ;AACrC,6CAA6B,GAAG,uCAAe,CAAC,MAAM,CAAC;AACvD,uCAAuB,GAAG,GAAG,CAAC,CAAC,MAAM", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\threat-indicators\\threat-signature.value-object.ts"], "sourcesContent": ["import { BaseValueObject } from '../../../../../shared-kernel/value-objects/base-value-object';\r\nimport { ConfidenceLevel } from '../../enums/confidence-level.enum';\r\n\r\n/**\r\n * Signature Type Enumeration\r\n */\r\nexport enum SignatureType {\r\n  YARA = 'yara',\r\n  SNORT = 'snort',\r\n  SURICATA = 'suricata',\r\n  SIGMA = 'sigma',\r\n  CUSTOM = 'custom',\r\n  REGEX = 'regex',\r\n  HASH = 'hash',\r\n  BEHAVIORAL = 'behavioral',\r\n  NETWORK = 'network',\r\n  ENDPOINT = 'endpoint',\r\n}\r\n\r\n/**\r\n * Signature Category Enumeration\r\n */\r\nexport enum SignatureCategory {\r\n  MALWARE = 'malware',\r\n  EXPLOIT = 'exploit',\r\n  COMMAND_CONTROL = 'command_control',\r\n  EXFILTRATION = 'exfiltration',\r\n  LATERAL_MOVEMENT = 'lateral_movement',\r\n  PERSISTENCE = 'persistence',\r\n  PRIVILEGE_ESCALATION = 'privilege_escalation',\r\n  DEFENSE_EVASION = 'defense_evasion',\r\n  RECONNAISSANCE = 'reconnaissance',\r\n  INITIAL_ACCESS = 'initial_access',\r\n}\r\n\r\n/**\r\n * Signature Severity Enumeration\r\n */\r\nexport enum SignatureSeverity {\r\n  INFO = 'info',\r\n  LOW = 'low',\r\n  MEDIUM = 'medium',\r\n  HIGH = 'high',\r\n  CRITICAL = 'critical',\r\n}\r\n\r\n/**\r\n * Threat Signature Properties\r\n */\r\nexport interface ThreatSignatureProps {\r\n  /** Signature identifier */\r\n  id: string;\r\n  /** Signature name */\r\n  name: string;\r\n  /** Signature type */\r\n  type: SignatureType;\r\n  /** Signature category */\r\n  category: SignatureCategory;\r\n  /** Signature severity */\r\n  severity: SignatureSeverity;\r\n  /** Signature content/rule */\r\n  content: string;\r\n  /** Signature description */\r\n  description: string;\r\n  /** Signature confidence */\r\n  confidence: ConfidenceLevel;\r\n  /** Signature version */\r\n  version: string;\r\n  /** Author information */\r\n  author: string;\r\n  /** Creation date */\r\n  createdAt: Date;\r\n  /** Last modified date */\r\n  modifiedAt: Date;\r\n  /** Signature tags */\r\n  tags: string[];\r\n  /** MITRE ATT&CK techniques */\r\n  mitreAttackTechniques: string[];\r\n  /** Target platforms */\r\n  targetPlatforms: string[];\r\n  /** Detection logic */\r\n  detectionLogic: {\r\n    /** Detection method */\r\n    method: 'pattern_match' | 'statistical' | 'behavioral' | 'heuristic' | 'machine_learning';\r\n    /** Detection parameters */\r\n    parameters: Record<string, any>;\r\n    /** False positive rate */\r\n    falsePositiveRate: number;\r\n    /** True positive rate */\r\n    truePositiveRate: number;\r\n  };\r\n  /** Performance metrics */\r\n  performance: {\r\n    /** Processing time in milliseconds */\r\n    processingTime: number;\r\n    /** Memory usage in bytes */\r\n    memoryUsage: number;\r\n    /** CPU usage percentage */\r\n    cpuUsage: number;\r\n    /** Throughput (events per second) */\r\n    throughput: number;\r\n  };\r\n  /** Validation status */\r\n  validation: {\r\n    /** Is signature validated */\r\n    isValidated: boolean;\r\n    /** Validation date */\r\n    validatedAt?: Date;\r\n    /** Validated by */\r\n    validatedBy?: string;\r\n    /** Validation notes */\r\n    validationNotes?: string;\r\n  };\r\n  /** Usage statistics */\r\n  usage: {\r\n    /** Total detections */\r\n    totalDetections: number;\r\n    /** True positives */\r\n    truePositives: number;\r\n    /** False positives */\r\n    falsePositives: number;\r\n    /** Last detection */\r\n    lastDetection?: Date;\r\n    /** Detection rate (per day) */\r\n    detectionRate: number;\r\n  };\r\n  /** Signature metadata */\r\n  metadata: {\r\n    /** Source of signature */\r\n    source: string;\r\n    /** License information */\r\n    license?: string;\r\n    /** References */\r\n    references: string[];\r\n    /** Related signatures */\r\n    relatedSignatures: string[];\r\n    /** Threat intelligence context */\r\n    threatIntelligence?: {\r\n      threatActor?: string;\r\n      campaign?: string;\r\n      malwareFamily?: string;\r\n      cveReferences?: string[];\r\n    };\r\n  };\r\n}\r\n\r\n/**\r\n * Threat Signature Value Object\r\n * \r\n * Represents a threat detection signature with comprehensive metadata,\r\n * performance metrics, and validation information.\r\n * \r\n * Key features:\r\n * - Multiple signature types (YARA, Snort, Sigma, etc.)\r\n * - Performance and accuracy metrics\r\n * - MITRE ATT&CK technique mapping\r\n * - Validation and quality assurance\r\n * - Usage statistics and effectiveness tracking\r\n * \r\n * Business Rules:\r\n * - Signature content must be valid for its type\r\n * - Confidence must match validation status\r\n * - Performance metrics must be realistic\r\n * - MITRE techniques must be valid\r\n */\r\nexport class ThreatSignature extends BaseValueObject<ThreatSignatureProps> {\r\n  private static readonly MAX_CONTENT_LENGTH = 100000; // 100KB\r\n  private static readonly MIN_CONFIDENCE_FOR_PRODUCTION = ConfidenceLevel.MEDIUM;\r\n  private static readonly MAX_FALSE_POSITIVE_RATE = 0.1; // 10%\r\n\r\n  constructor(props: ThreatSignatureProps) {\r\n    super(props);\r\n  }\r\n\r\n  protected validate(): void {\r\n    if (!this._value.id || this._value.id.trim().length === 0) {\r\n      throw new Error('Threat signature must have an ID');\r\n    }\r\n\r\n    if (!this._value.name || this._value.name.trim().length === 0) {\r\n      throw new Error('Threat signature must have a name');\r\n    }\r\n\r\n    if (!Object.values(SignatureType).includes(this._value.type)) {\r\n      throw new Error(`Invalid signature type: ${this._value.type}`);\r\n    }\r\n\r\n    if (!Object.values(SignatureCategory).includes(this._value.category)) {\r\n      throw new Error(`Invalid signature category: ${this._value.category}`);\r\n    }\r\n\r\n    if (!Object.values(SignatureSeverity).includes(this._value.severity)) {\r\n      throw new Error(`Invalid signature severity: ${this._value.severity}`);\r\n    }\r\n\r\n    if (!this._value.content || this._value.content.trim().length === 0) {\r\n      throw new Error('Threat signature must have content');\r\n    }\r\n\r\n    if (this._value.content.length > ThreatSignature.MAX_CONTENT_LENGTH) {\r\n      throw new Error(`Signature content exceeds maximum length of ${ThreatSignature.MAX_CONTENT_LENGTH} characters`);\r\n    }\r\n\r\n    if (!Object.values(ConfidenceLevel).includes(this._value.confidence)) {\r\n      throw new Error(`Invalid confidence level: ${this._value.confidence}`);\r\n    }\r\n\r\n    if (!this._value.version || this._value.version.trim().length === 0) {\r\n      throw new Error('Threat signature must have a version');\r\n    }\r\n\r\n    if (!this._value.author || this._value.author.trim().length === 0) {\r\n      throw new Error('Threat signature must have an author');\r\n    }\r\n\r\n    if (this._value.detectionLogic.falsePositiveRate < 0 || this._value.detectionLogic.falsePositiveRate > 1) {\r\n      throw new Error('False positive rate must be between 0 and 1');\r\n    }\r\n\r\n    if (this._value.detectionLogic.truePositiveRate < 0 || this._value.detectionLogic.truePositiveRate > 1) {\r\n      throw new Error('True positive rate must be between 0 and 1');\r\n    }\r\n\r\n    if (this._value.performance.processingTime < 0) {\r\n      throw new Error('Processing time cannot be negative');\r\n    }\r\n\r\n    if (this._value.performance.memoryUsage < 0) {\r\n      throw new Error('Memory usage cannot be negative');\r\n    }\r\n\r\n    if (this._value.performance.cpuUsage < 0 || this._value.performance.cpuUsage > 100) {\r\n      throw new Error('CPU usage must be between 0 and 100');\r\n    }\r\n\r\n    if (this._value.usage.totalDetections < 0) {\r\n      throw new Error('Total detections cannot be negative');\r\n    }\r\n\r\n    if (this._value.usage.truePositives < 0 || this._value.usage.truePositives > this._value.usage.totalDetections) {\r\n      throw new Error('True positives must be between 0 and total detections');\r\n    }\r\n\r\n    if (this._value.usage.falsePositives < 0) {\r\n      throw new Error('False positives cannot be negative');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create a new threat signature\r\n   */\r\n  static create(\r\n    id: string,\r\n    name: string,\r\n    type: SignatureType,\r\n    category: SignatureCategory,\r\n    severity: SignatureSeverity,\r\n    content: string,\r\n    description: string,\r\n    author: string,\r\n    options?: {\r\n      confidence?: ConfidenceLevel;\r\n      version?: string;\r\n      tags?: string[];\r\n      mitreAttackTechniques?: string[];\r\n      targetPlatforms?: string[];\r\n      detectionMethod?: 'pattern_match' | 'statistical' | 'behavioral' | 'heuristic' | 'machine_learning';\r\n      detectionParameters?: Record<string, any>;\r\n      source?: string;\r\n      references?: string[];\r\n    }\r\n  ): ThreatSignature {\r\n    const now = new Date();\r\n\r\n    const props: ThreatSignatureProps = {\r\n      id: id.trim(),\r\n      name: name.trim(),\r\n      type,\r\n      category,\r\n      severity,\r\n      content: content.trim(),\r\n      description: description.trim(),\r\n      confidence: options?.confidence || ConfidenceLevel.MEDIUM,\r\n      version: options?.version || '1.0',\r\n      author: author.trim(),\r\n      createdAt: now,\r\n      modifiedAt: now,\r\n      tags: options?.tags || [],\r\n      mitreAttackTechniques: options?.mitreAttackTechniques || [],\r\n      targetPlatforms: options?.targetPlatforms || [],\r\n      detectionLogic: {\r\n        method: options?.detectionMethod || 'pattern_match',\r\n        parameters: options?.detectionParameters || {},\r\n        falsePositiveRate: 0.05, // Default 5%\r\n        truePositiveRate: 0.85, // Default 85%\r\n      },\r\n      performance: {\r\n        processingTime: 0,\r\n        memoryUsage: 0,\r\n        cpuUsage: 0,\r\n        throughput: 0,\r\n      },\r\n      validation: {\r\n        isValidated: false,\r\n      },\r\n      usage: {\r\n        totalDetections: 0,\r\n        truePositives: 0,\r\n        falsePositives: 0,\r\n        detectionRate: 0,\r\n      },\r\n      metadata: {\r\n        source: options?.source || 'custom',\r\n        references: options?.references || [],\r\n        relatedSignatures: [],\r\n      },\r\n    };\r\n\r\n    return new ThreatSignature(props);\r\n  }\r\n\r\n  /**\r\n   * Get signature ID\r\n   */\r\n  get id(): string {\r\n    return this._value.id;\r\n  }\r\n\r\n  /**\r\n   * Get signature name\r\n   */\r\n  get name(): string {\r\n    return this._value.name;\r\n  }\r\n\r\n  /**\r\n   * Get signature type\r\n   */\r\n  get type(): SignatureType {\r\n    return this._value.type;\r\n  }\r\n\r\n  /**\r\n   * Get signature category\r\n   */\r\n  get category(): SignatureCategory {\r\n    return this._value.category;\r\n  }\r\n\r\n  /**\r\n   * Get signature severity\r\n   */\r\n  get severity(): SignatureSeverity {\r\n    return this._value.severity;\r\n  }\r\n\r\n  /**\r\n   * Get signature content\r\n   */\r\n  get content(): string {\r\n    return this._value.content;\r\n  }\r\n\r\n  /**\r\n   * Get signature description\r\n   */\r\n  get description(): string {\r\n    return this._value.description;\r\n  }\r\n\r\n  /**\r\n   * Get signature confidence\r\n   */\r\n  get confidence(): ConfidenceLevel {\r\n    return this._value.confidence;\r\n  }\r\n\r\n  /**\r\n   * Get signature version\r\n   */\r\n  get version(): string {\r\n    return this._value.version;\r\n  }\r\n\r\n  /**\r\n   * Get signature author\r\n   */\r\n  get author(): string {\r\n    return this._value.author;\r\n  }\r\n\r\n  /**\r\n   * Get creation date\r\n   */\r\n  get createdAt(): Date {\r\n    return this._value.createdAt;\r\n  }\r\n\r\n  /**\r\n   * Get modification date\r\n   */\r\n  get modifiedAt(): Date {\r\n    return this._value.modifiedAt;\r\n  }\r\n\r\n  /**\r\n   * Get signature tags\r\n   */\r\n  get tags(): string[] {\r\n    return [...this._value.tags];\r\n  }\r\n\r\n  /**\r\n   * Get MITRE ATT&CK techniques\r\n   */\r\n  get mitreAttackTechniques(): string[] {\r\n    return [...this._value.mitreAttackTechniques];\r\n  }\r\n\r\n  /**\r\n   * Get target platforms\r\n   */\r\n  get targetPlatforms(): string[] {\r\n    return [...this._value.targetPlatforms];\r\n  }\r\n\r\n  /**\r\n   * Get detection logic\r\n   */\r\n  get detectionLogic(): ThreatSignatureProps['detectionLogic'] {\r\n    return { ...this._value.detectionLogic };\r\n  }\r\n\r\n  /**\r\n   * Get performance metrics\r\n   */\r\n  get performance(): ThreatSignatureProps['performance'] {\r\n    return { ...this._value.performance };\r\n  }\r\n\r\n  /**\r\n   * Get validation status\r\n   */\r\n  get validation(): ThreatSignatureProps['validation'] {\r\n    return { ...this._value.validation };\r\n  }\r\n\r\n  /**\r\n   * Get usage statistics\r\n   */\r\n  get usage(): ThreatSignatureProps['usage'] {\r\n    return { ...this._value.usage };\r\n  }\r\n\r\n  /**\r\n   * Get metadata\r\n   */\r\n  get metadata(): ThreatSignatureProps['metadata'] {\r\n    return { ...this._value.metadata };\r\n  }\r\n\r\n  /**\r\n   * Check if signature is validated\r\n   */\r\n  isValidated(): boolean {\r\n    return this._value.validation.isValidated;\r\n  }\r\n\r\n  /**\r\n   * Check if signature is production ready\r\n   */\r\n  isProductionReady(): boolean {\r\n    const confidenceValues = {\r\n      [ConfidenceLevel.UNKNOWN]: 0,\r\n      [ConfidenceLevel.VERY_LOW]: 1,\r\n      [ConfidenceLevel.LOW]: 2,\r\n      [ConfidenceLevel.MEDIUM]: 3,\r\n      [ConfidenceLevel.HIGH]: 4,\r\n      [ConfidenceLevel.VERY_HIGH]: 5,\r\n      [ConfidenceLevel.CONFIRMED]: 6,\r\n    };\r\n    \r\n    const minConfidenceValue = confidenceValues[ThreatSignature.MIN_CONFIDENCE_FOR_PRODUCTION];\r\n    const currentConfidenceValue = confidenceValues[this._value.confidence];\r\n    \r\n    return this.isValidated() &&\r\n           currentConfidenceValue >= minConfidenceValue &&\r\n           this._value.detectionLogic.falsePositiveRate <= ThreatSignature.MAX_FALSE_POSITIVE_RATE;\r\n  }\r\n\r\n  /**\r\n   * Check if signature is high performance\r\n   */\r\n  isHighPerformance(): boolean {\r\n    return this._value.performance.processingTime < 100 && // Less than 100ms\r\n           this._value.performance.cpuUsage < 10 && // Less than 10% CPU\r\n           this._value.performance.throughput > 1000; // More than 1000 events/sec\r\n  }\r\n\r\n  /**\r\n   * Check if signature is effective\r\n   */\r\n  isEffective(): boolean {\r\n    if (this._value.usage.totalDetections === 0) {\r\n      return false; // No data to determine effectiveness\r\n    }\r\n\r\n    const accuracy = this._value.usage.truePositives / this._value.usage.totalDetections;\r\n    return accuracy >= 0.8; // 80% accuracy threshold\r\n  }\r\n\r\n  /**\r\n   * Get signature effectiveness score (0-100)\r\n   */\r\n  getEffectivenessScore(): number {\r\n    if (this._value.usage.totalDetections === 0) {\r\n      return 0;\r\n    }\r\n\r\n    const accuracy = this._value.usage.truePositives / this._value.usage.totalDetections;\r\n    const confidenceScore = this.getConfidenceScore() / 100; // Convert to 0-1 scale\r\n    const performanceScore = this.getPerformanceScore() / 100; // Convert to 0-1 scale\r\n\r\n    return Math.round((accuracy * 50) + (confidenceScore * 25) + (performanceScore * 25));\r\n  }\r\n\r\n  /**\r\n   * Get confidence score (0-100)\r\n   */\r\n  getConfidenceScore(): number {\r\n    const confidenceValues = {\r\n      [ConfidenceLevel.UNKNOWN]: 0,\r\n      [ConfidenceLevel.VERY_LOW]: 20,\r\n      [ConfidenceLevel.LOW]: 20,\r\n      [ConfidenceLevel.MEDIUM]: 50,\r\n      [ConfidenceLevel.HIGH]: 80,\r\n      [ConfidenceLevel.VERY_HIGH]: 95,\r\n      [ConfidenceLevel.CONFIRMED]: 100,\r\n    };\r\n\r\n    return confidenceValues[this._value.confidence] || 0;\r\n  }\r\n\r\n  /**\r\n   * Get performance score (0-100)\r\n   */\r\n  getPerformanceScore(): number {\r\n    // If no performance data is available, return a neutral score\r\n    if (this._value.performance.processingTime === 0 && \r\n        this._value.performance.cpuUsage === 0 && \r\n        this._value.performance.throughput === 0) {\r\n      return 50; // Neutral score when no performance data\r\n    }\r\n\r\n    let score = 100;\r\n\r\n    // Penalize high processing time\r\n    if (this._value.performance.processingTime > 1000) {\r\n      score -= 30;\r\n    } else if (this._value.performance.processingTime > 500) {\r\n      score -= 15;\r\n    }\r\n\r\n    // Penalize high CPU usage\r\n    if (this._value.performance.cpuUsage > 50) {\r\n      score -= 30;\r\n    } else if (this._value.performance.cpuUsage > 25) {\r\n      score -= 15;\r\n    }\r\n\r\n    // Reward high throughput\r\n    if (this._value.performance.throughput > 10000) {\r\n      score += 10;\r\n    } else if (this._value.performance.throughput > 5000) {\r\n      score += 5;\r\n    }\r\n\r\n    return Math.max(0, Math.min(100, score));\r\n  }\r\n\r\n  /**\r\n   * Get signature quality rating\r\n   */\r\n  getQualityRating(): 'poor' | 'fair' | 'good' | 'excellent' {\r\n    const effectivenessScore = this.getEffectivenessScore();\r\n\r\n    if (effectivenessScore >= 90) return 'excellent';\r\n    if (effectivenessScore >= 70) return 'good';\r\n    if (effectivenessScore >= 50) return 'fair';\r\n    return 'poor';\r\n  }\r\n\r\n  /**\r\n   * Get signature age in days\r\n   */\r\n  getAge(): number {\r\n    return Math.floor((Date.now() - this._value.createdAt.getTime()) / (1000 * 60 * 60 * 24));\r\n  }\r\n\r\n  /**\r\n   * Check if signature needs update\r\n   */\r\n  needsUpdate(): boolean {\r\n    const age = this.getAge();\r\n    const effectiveness = this.getEffectivenessScore();\r\n\r\n    // Needs update if old and ineffective\r\n    return (age > 365 && effectiveness < 50) || // Older than 1 year and poor effectiveness\r\n           (age > 180 && effectiveness < 30) || // Older than 6 months and very poor effectiveness\r\n           this._value.detectionLogic.falsePositiveRate > ThreatSignature.MAX_FALSE_POSITIVE_RATE;\r\n  }\r\n\r\n  /**\r\n   * Get recommended actions\r\n   */\r\n  getRecommendedActions(): string[] {\r\n    const actions: string[] = [];\r\n\r\n    if (!this.isValidated()) {\r\n      actions.push('Validate signature');\r\n    }\r\n\r\n    if (!this.isProductionReady()) {\r\n      actions.push('Improve signature quality for production use');\r\n    }\r\n\r\n    if (!this.isHighPerformance()) {\r\n      actions.push('Optimize signature performance');\r\n    }\r\n\r\n    if (!this.isEffective() && this._value.usage.totalDetections > 10) {\r\n      actions.push('Review and improve signature accuracy');\r\n    }\r\n\r\n    if (this.needsUpdate()) {\r\n      actions.push('Update signature content and logic');\r\n    }\r\n\r\n    if (this._value.detectionLogic.falsePositiveRate > 0.05) {\r\n      actions.push('Reduce false positive rate');\r\n    }\r\n\r\n    return actions;\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      id: this._value.id,\r\n      name: this._value.name,\r\n      type: this._value.type,\r\n      category: this._value.category,\r\n      severity: this._value.severity,\r\n      content: this._value.content,\r\n      description: this._value.description,\r\n      confidence: this._value.confidence,\r\n      version: this._value.version,\r\n      author: this._value.author,\r\n      createdAt: this._value.createdAt.toISOString(),\r\n      modifiedAt: this._value.modifiedAt.toISOString(),\r\n      tags: this._value.tags,\r\n      mitreAttackTechniques: this._value.mitreAttackTechniques,\r\n      targetPlatforms: this._value.targetPlatforms,\r\n      detectionLogic: this._value.detectionLogic,\r\n      performance: this._value.performance,\r\n      validation: this._value.validation,\r\n      usage: this._value.usage,\r\n      metadata: this._value.metadata,\r\n      analysis: {\r\n        isValidated: this.isValidated(),\r\n        isProductionReady: this.isProductionReady(),\r\n        isHighPerformance: this.isHighPerformance(),\r\n        isEffective: this.isEffective(),\r\n        effectivenessScore: this.getEffectivenessScore(),\r\n        confidenceScore: this.getConfidenceScore(),\r\n        performanceScore: this.getPerformanceScore(),\r\n        qualityRating: this.getQualityRating(),\r\n        age: this.getAge(),\r\n        needsUpdate: this.needsUpdate(),\r\n        recommendedActions: this.getRecommendedActions(),\r\n      },\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create from JSON representation\r\n   */\r\n  static fromJSON(json: Record<string, any>): ThreatSignature {\r\n    const props: ThreatSignatureProps = {\r\n      id: json.id,\r\n      name: json.name,\r\n      type: json.type,\r\n      category: json.category,\r\n      severity: json.severity,\r\n      content: json.content,\r\n      description: json.description,\r\n      confidence: json.confidence,\r\n      version: json.version,\r\n      author: json.author,\r\n      createdAt: new Date(json.createdAt),\r\n      modifiedAt: new Date(json.modifiedAt),\r\n      tags: json.tags,\r\n      mitreAttackTechniques: json.mitreAttackTechniques,\r\n      targetPlatforms: json.targetPlatforms,\r\n      detectionLogic: json.detectionLogic,\r\n      performance: json.performance,\r\n      validation: json.validation,\r\n      usage: json.usage,\r\n      metadata: json.metadata,\r\n    };\r\n\r\n    return new ThreatSignature(props);\r\n  }\r\n}\r\n"], "version": 3}