{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\__tests__\\e2e\\authentication-workflow.e2e-spec.ts", "mappings": ";;;;;AAAA,6CAAsD;AAEtD,2CAA8C;AAC9C,0DAAgC;AAChC,iDAA6C;AAE7C,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;IAC7C,IAAI,GAAqB,CAAC;IAC1B,IAAI,SAAiB,CAAC;IACtB,IAAI,YAAoB,CAAC;IACzB,IAAI,MAAc,CAAC;IAEnB,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,aAAa,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAClE,OAAO,EAAE;gBACP,qBAAY,CAAC,OAAO,CAAC;oBACnB,QAAQ,EAAE,IAAI;oBACd,WAAW,EAAE,WAAW;iBACzB,CAAC;gBACF,sBAAS;aACV;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,GAAG,GAAG,aAAa,CAAC,qBAAqB,EAAE,CAAC;QAC5C,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC;IACnB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,MAAM,QAAQ,GAAG;YACf,KAAK,EAAE,kBAAkB;YACzB,QAAQ,EAAE,oBAAoB;YAC9B,IAAI,EAAE,WAAW;SAClB,CAAC;QAEF,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,uBAAuB,CAAC;iBAC7B,IAAI,CAAC,QAAQ,CAAC;iBACd,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,IAAI,EAAE;wBACJ,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBACtB,KAAK,EAAE,QAAQ,CAAC,KAAK;wBACrB,IAAI,EAAE,QAAQ,CAAC,IAAI;wBACnB,eAAe,EAAE,KAAK;wBACtB,KAAK,EAAE,CAAC,MAAM,CAAC;wBACf,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;qBAC9B;oBACD,MAAM,EAAE;wBACN,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC/B,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAChC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;qBAC9B;iBACF;gBACD,OAAO,EAAE,8BAA8B;gBACvC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC9B,CAAC,CAAC;YAEH,gDAAgD;YAChD,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;YAClD,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;YACtD,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,IAAI,CAAC,uBAAuB,CAAC;iBAC7B,IAAI,CAAC,QAAQ,CAAC;iBACd,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,WAAW,GAAG;gBAClB,KAAK,EAAE,eAAe;gBACtB,QAAQ,EAAE,KAAK,EAAE,YAAY;gBAC7B,IAAI,EAAE,EAAE,EAAE,aAAa;aACxB,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,uBAAuB,CAAC;iBAC7B,IAAI,CAAC,WAAW,CAAC;iBACjB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;YAClE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;YACxE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,SAAS,GAAG;gBAChB,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,oBAAoB;aAC/B,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC,SAAS,CAAC;iBACf,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,IAAI,EAAE;wBACJ,EAAE,EAAE,MAAM;wBACV,KAAK,EAAE,SAAS,CAAC,KAAK;wBACtB,IAAI,EAAE,WAAW;wBACjB,KAAK,EAAE,CAAC,MAAM,CAAC;wBACf,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;qBAC/B;oBACD,MAAM,EAAE;wBACN,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC/B,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAChC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;qBAC9B;oBACD,OAAO,EAAE;wBACP,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBACtB,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;qBAC9B;iBACF;gBACD,OAAO,EAAE,kBAAkB;gBAC3B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC9B,CAAC,CAAC;YAEH,gBAAgB;YAChB,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;YAClD,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,YAAY,GAAG;gBACnB,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,eAAe;aAC1B,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC,YAAY,CAAC;iBAClB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,gBAAgB,GAAG;gBACvB,KAAK,EAAE,yBAAyB;gBAChC,QAAQ,EAAE,kBAAkB;aAC7B,CAAC;YAEF,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC,gBAAgB,CAAC;iBACtB,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBACtC,EAAE,EAAE,MAAM;gBACV,KAAK,EAAE,kBAAkB;gBACzB,IAAI,EAAE,WAAW;gBACjB,KAAK,EAAE,CAAC,MAAM,CAAC;gBACf,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;gBAC9B,eAAe,EAAE,KAAK;gBACtB,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC9B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,GAAG,CAAC,eAAe,EAAE,sBAAsB,CAAC;iBAC5C,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,YAAY,GAAG,+IAA+I,CAAC;YAErK,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,YAAY,EAAE,CAAC;iBAC9C,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,sBAAsB,CAAC;iBAC5B,IAAI,CAAC,EAAE,YAAY,EAAE,CAAC;iBACtB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,MAAM,EAAE;wBACN,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC/B,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAChC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;qBAC9B;iBACF;gBACD,OAAO,EAAE,+BAA+B;gBACxC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC9B,CAAC,CAAC;YAEH,gBAAgB;YAChB,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;YAClD,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,IAAI,CAAC,sBAAsB,CAAC;iBAC5B,IAAI,CAAC,EAAE,YAAY,EAAE,uBAAuB,EAAE,CAAC;iBAC/C,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,MAAM,kBAAkB,GAAG;gBACzB,eAAe,EAAE,oBAAoB;gBACrC,WAAW,EAAE,uBAAuB;aACrC,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,8BAA8B,CAAC;iBACnC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC,kBAAkB,CAAC;iBACxB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,SAAS,GAAG;gBAChB,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,uBAAuB;aAClC,CAAC;YAEF,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC,SAAS,CAAC;iBACf,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,SAAS,GAAG;gBAChB,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,oBAAoB,EAAE,eAAe;aAChD,CAAC;YAEF,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC,SAAS,CAAC;iBACf,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,KAAK,IAAI,EAAE;YACzE,MAAM,kBAAkB,GAAG;gBACzB,eAAe,EAAE,sBAAsB;gBACvC,WAAW,EAAE,wBAAwB;aACtC,CAAC;YAEF,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,8BAA8B,CAAC;iBACnC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC,kBAAkB,CAAC;iBACxB,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;YAC3C,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,uBAAuB,CAAC;iBAC5B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CACzC,MAAM,CAAC,eAAe,CAAC;gBACrB,MAAM,CAAC,gBAAgB,CAAC;oBACtB,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACtB,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC9B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC7B,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAClC,gBAAgB,EAAE,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC;iBACtC,CAAC;aACH,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,2CAA2C;YAC3C,MAAM,gBAAgB,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACxD,GAAG,CAAC,uBAAuB,CAAC;iBAC5B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,SAAS,GAAG,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAE5D,qBAAqB;YACrB,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,MAAM,CAAC,yBAAyB,SAAS,EAAE,CAAC;iBAC5C,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,MAAM,CAAC,8BAA8B,CAAC;iBACtC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;YAC1C,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,qBAAqB,CAAC;iBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,IAAI,CAAC,sBAAsB,CAAC;iBAC5B,IAAI,CAAC,EAAE,YAAY,EAAE,CAAC;iBACtB,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,8BAA8B,CAAC;iBACpC,IAAI,CAAC,EAAE,KAAK,EAAE,kBAAkB,EAAE,CAAC;iBACnC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,8BAA8B,CAAC;iBACpC,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC;iBAC1C,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,yCAAyC;YAEzD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,uEAAuE;QACvE,gEAAgE;IAClE,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,mCAAmC;YACnC,MAAM,aAAa,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACrD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC;gBACJ,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,uBAAuB;aAClC,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,YAAY,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;YAEhE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,kCAAkC,CAAC;iBACxC,GAAG,CAAC,eAAe,EAAE,UAAU,YAAY,EAAE,CAAC;iBAC9C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,8EAA8E;QAC9E,kCAAkC;IACpC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,2BAA2B;YAC3B,MAAM,aAAa,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACrD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC;gBACJ,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,uBAAuB;aAClC,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;YAE7D,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;YACpE,2BAA2B;YAC3B,MAAM,aAAa,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACrD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC;gBACJ,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,uBAAuB;aAClC,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;YAE7D,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,qBAAqB,CAAC;iBAC1B,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC;gBACJ,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,uBAAuB;aAClC,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,gEAAgE;YAChE,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,SAAS,GAAG;gBAChB,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,uBAAuB;aAClC,CAAC;YAEF,0CAA0C;YAC1C,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAC5C,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACzB,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC,SAAS,CAAC,CACnB,CAAC;YAEF,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAE9C,0CAA0C;YAC1C,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAC3B,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,eAAe,GAAG;gBACtB,iBAAiB;gBACjB,gBAAgB,EAAE,oBAAoB;gBACtC,gCAAgC,EAAE,iBAAiB;gBACnD,EAAE,EAAE,cAAc;aACnB,CAAC;YAEF,KAAK,MAAM,KAAK,IAAI,eAAe,EAAE,CAAC;gBACpC,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAC/B,GAAG,CAAC,sBAAsB,CAAC;qBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;qBACvC,MAAM,CAAC,GAAG,CAAC,CAAC;YACjB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,+CAA+C;YAC/C,gEAAgE;YAEhE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC;gBACJ,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,uBAAuB;aAClC,CAAC,CAAC;YAEL,oDAAoD;YACpD,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC;gBACJ,KAAK,EAAE,eAAe;gBACtB,QAAQ,EAAE,OAAO;aAClB,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YACnD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAChD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC;gBACJ,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,uBAAuB;aAClC,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC3B,MAAM,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAC;YAErC,wDAAwD;YACxD,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\__tests__\\e2e\\authentication-workflow.e2e-spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { INestApplication } from '@nestjs/common';\r\nimport { ConfigModule } from '@nestjs/config';\r\nimport request from 'supertest';\r\nimport { AppModule } from '../../app.module';\r\n\r\ndescribe('Authentication Workflow (e2e)', () => {\r\n  let app: INestApplication;\r\n  let authToken: string;\r\n  let refreshToken: string;\r\n  let userId: string;\r\n\r\n  beforeAll(async () => {\r\n    const moduleFixture: TestingModule = await Test.createTestingModule({\r\n      imports: [\r\n        ConfigModule.forRoot({\r\n          isGlobal: true,\r\n          envFilePath: '.env.test',\r\n        }),\r\n        AppModule,\r\n      ],\r\n    }).compile();\r\n\r\n    app = moduleFixture.createNestApplication();\r\n    await app.init();\r\n  });\r\n\r\n  afterAll(async () => {\r\n    await app.close();\r\n  });\r\n\r\n  describe('User Registration Flow', () => {\r\n    const testUser = {\r\n      email: '<EMAIL>',\r\n      password: 'SecurePassword123!',\r\n      name: 'Test User',\r\n    };\r\n\r\n    it('should register a new user successfully', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/auth/register')\r\n        .send(testUser)\r\n        .expect(201);\r\n\r\n      expect(response.body).toEqual({\r\n        success: true,\r\n        data: {\r\n          user: {\r\n            id: expect.any(String),\r\n            email: testUser.email,\r\n            name: testUser.name,\r\n            isEmailVerified: false,\r\n            roles: ['user'],\r\n            createdAt: expect.any(String),\r\n          },\r\n          tokens: {\r\n            accessToken: expect.any(String),\r\n            refreshToken: expect.any(String),\r\n            expiresIn: expect.any(Number),\r\n          },\r\n        },\r\n        message: 'User registered successfully',\r\n        timestamp: expect.any(String),\r\n      });\r\n\r\n      // Store tokens and user ID for subsequent tests\r\n      authToken = response.body.data.tokens.accessToken;\r\n      refreshToken = response.body.data.tokens.refreshToken;\r\n      userId = response.body.data.user.id;\r\n    });\r\n\r\n    it('should reject duplicate email registration', async () => {\r\n      await request(app.getHttpServer())\r\n        .post('/api/v1/auth/register')\r\n        .send(testUser)\r\n        .expect(409);\r\n    });\r\n\r\n    it('should validate registration input', async () => {\r\n      const invalidUser = {\r\n        email: 'invalid-email',\r\n        password: '123', // Too short\r\n        name: '', // Empty name\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/auth/register')\r\n        .send(invalidUser)\r\n        .expect(400);\r\n\r\n      expect(response.body.message).toContain('email must be an email');\r\n      expect(response.body.message).toContain('password must be longer than');\r\n      expect(response.body.message).toContain('name should not be empty');\r\n    });\r\n  });\r\n\r\n  describe('User Login Flow', () => {\r\n    it('should login with valid credentials', async () => {\r\n      const loginData = {\r\n        email: '<EMAIL>',\r\n        password: 'SecurePassword123!',\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/auth/login')\r\n        .send(loginData)\r\n        .expect(200);\r\n\r\n      expect(response.body).toEqual({\r\n        success: true,\r\n        data: {\r\n          user: {\r\n            id: userId,\r\n            email: loginData.email,\r\n            name: 'Test User',\r\n            roles: ['user'],\r\n            permissions: expect.any(Array),\r\n          },\r\n          tokens: {\r\n            accessToken: expect.any(String),\r\n            refreshToken: expect.any(String),\r\n            expiresIn: expect.any(Number),\r\n          },\r\n          session: {\r\n            id: expect.any(String),\r\n            expiresAt: expect.any(String),\r\n          },\r\n        },\r\n        message: 'Login successful',\r\n        timestamp: expect.any(String),\r\n      });\r\n\r\n      // Update tokens\r\n      authToken = response.body.data.tokens.accessToken;\r\n      refreshToken = response.body.data.tokens.refreshToken;\r\n    });\r\n\r\n    it('should reject invalid credentials', async () => {\r\n      const invalidLogin = {\r\n        email: '<EMAIL>',\r\n        password: 'WrongPassword',\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/auth/login')\r\n        .send(invalidLogin)\r\n        .expect(401);\r\n\r\n      expect(response.body.message).toBe('Invalid credentials');\r\n    });\r\n\r\n    it('should reject login for non-existent user', async () => {\r\n      const nonExistentLogin = {\r\n        email: '<EMAIL>',\r\n        password: 'SomePassword123!',\r\n      };\r\n\r\n      await request(app.getHttpServer())\r\n        .post('/api/v1/auth/login')\r\n        .send(nonExistentLogin)\r\n        .expect(401);\r\n    });\r\n  });\r\n\r\n  describe('Protected Route Access', () => {\r\n    it('should access protected route with valid token', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/auth/profile')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .expect(200);\r\n\r\n      expect(response.body.data.user).toEqual({\r\n        id: userId,\r\n        email: '<EMAIL>',\r\n        name: 'Test User',\r\n        roles: ['user'],\r\n        permissions: expect.any(Array),\r\n        isEmailVerified: false,\r\n        createdAt: expect.any(String),\r\n        updatedAt: expect.any(String),\r\n      });\r\n    });\r\n\r\n    it('should reject access without token', async () => {\r\n      await request(app.getHttpServer())\r\n        .get('/api/v1/auth/profile')\r\n        .expect(401);\r\n    });\r\n\r\n    it('should reject access with invalid token', async () => {\r\n      await request(app.getHttpServer())\r\n        .get('/api/v1/auth/profile')\r\n        .set('Authorization', 'Bearer invalid-token')\r\n        .expect(401);\r\n    });\r\n\r\n    it('should reject access with expired token', async () => {\r\n      const expiredToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.invalid';\r\n      \r\n      await request(app.getHttpServer())\r\n        .get('/api/v1/auth/profile')\r\n        .set('Authorization', `Bearer ${expiredToken}`)\r\n        .expect(401);\r\n    });\r\n  });\r\n\r\n  describe('Token Refresh Flow', () => {\r\n    it('should refresh tokens with valid refresh token', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/auth/refresh')\r\n        .send({ refreshToken })\r\n        .expect(200);\r\n\r\n      expect(response.body).toEqual({\r\n        success: true,\r\n        data: {\r\n          tokens: {\r\n            accessToken: expect.any(String),\r\n            refreshToken: expect.any(String),\r\n            expiresIn: expect.any(Number),\r\n          },\r\n        },\r\n        message: 'Tokens refreshed successfully',\r\n        timestamp: expect.any(String),\r\n      });\r\n\r\n      // Update tokens\r\n      authToken = response.body.data.tokens.accessToken;\r\n      refreshToken = response.body.data.tokens.refreshToken;\r\n    });\r\n\r\n    it('should reject refresh with invalid refresh token', async () => {\r\n      await request(app.getHttpServer())\r\n        .post('/api/v1/auth/refresh')\r\n        .send({ refreshToken: 'invalid-refresh-token' })\r\n        .expect(401);\r\n    });\r\n\r\n    it('should work with new access token after refresh', async () => {\r\n      await request(app.getHttpServer())\r\n        .get('/api/v1/auth/profile')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .expect(200);\r\n    });\r\n  });\r\n\r\n  describe('Password Change Flow', () => {\r\n    it('should change password with valid current password', async () => {\r\n      const passwordChangeData = {\r\n        currentPassword: 'SecurePassword123!',\r\n        newPassword: 'NewSecurePassword456!',\r\n      };\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .put('/api/v1/auth/change-password')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send(passwordChangeData)\r\n        .expect(200);\r\n\r\n      expect(response.body.message).toBe('Password changed successfully');\r\n    });\r\n\r\n    it('should login with new password', async () => {\r\n      const loginData = {\r\n        email: '<EMAIL>',\r\n        password: 'NewSecurePassword456!',\r\n      };\r\n\r\n      await request(app.getHttpServer())\r\n        .post('/api/v1/auth/login')\r\n        .send(loginData)\r\n        .expect(200);\r\n    });\r\n\r\n    it('should reject old password after change', async () => {\r\n      const loginData = {\r\n        email: '<EMAIL>',\r\n        password: 'SecurePassword123!', // Old password\r\n      };\r\n\r\n      await request(app.getHttpServer())\r\n        .post('/api/v1/auth/login')\r\n        .send(loginData)\r\n        .expect(401);\r\n    });\r\n\r\n    it('should reject password change with wrong current password', async () => {\r\n      const passwordChangeData = {\r\n        currentPassword: 'WrongCurrentPassword',\r\n        newPassword: 'AnotherNewPassword789!',\r\n      };\r\n\r\n      await request(app.getHttpServer())\r\n        .put('/api/v1/auth/change-password')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send(passwordChangeData)\r\n        .expect(400);\r\n    });\r\n  });\r\n\r\n  describe('Session Management', () => {\r\n    it('should list active sessions', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/api/v1/auth/sessions')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .expect(200);\r\n\r\n      expect(response.body.data.sessions).toEqual(\r\n        expect.arrayContaining([\r\n          expect.objectContaining({\r\n            id: expect.any(String),\r\n            deviceInfo: expect.any(Object),\r\n            createdAt: expect.any(String),\r\n            lastAccessedAt: expect.any(String),\r\n            isCurrentSession: expect.any(Boolean),\r\n          }),\r\n        ])\r\n      );\r\n    });\r\n\r\n    it('should revoke specific session', async () => {\r\n      // First, get sessions to find a session ID\r\n      const sessionsResponse = await request(app.getHttpServer())\r\n        .get('/api/v1/auth/sessions')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .expect(200);\r\n\r\n      const sessionId = sessionsResponse.body.data.sessions[0].id;\r\n\r\n      // Revoke the session\r\n      await request(app.getHttpServer())\r\n        .delete(`/api/v1/auth/sessions/${sessionId}`)\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .expect(200);\r\n    });\r\n\r\n    it('should revoke all other sessions', async () => {\r\n      await request(app.getHttpServer())\r\n        .delete('/api/v1/auth/sessions/others')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .expect(200);\r\n    });\r\n  });\r\n\r\n  describe('Logout Flow', () => {\r\n    it('should logout successfully', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/auth/logout')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .expect(200);\r\n\r\n      expect(response.body.message).toBe('Logout successful');\r\n    });\r\n\r\n    it('should not access protected routes after logout', async () => {\r\n      await request(app.getHttpServer())\r\n        .get('/api/v1/auth/profile')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .expect(401);\r\n    });\r\n\r\n    it('should not refresh tokens after logout', async () => {\r\n      await request(app.getHttpServer())\r\n        .post('/api/v1/auth/refresh')\r\n        .send({ refreshToken })\r\n        .expect(401);\r\n    });\r\n  });\r\n\r\n  describe('Password Reset Flow', () => {\r\n    it('should initiate password reset', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/auth/forgot-password')\r\n        .send({ email: '<EMAIL>' })\r\n        .expect(200);\r\n\r\n      expect(response.body.message).toBe('Password reset email sent');\r\n    });\r\n\r\n    it('should handle non-existent email for password reset', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/auth/forgot-password')\r\n        .send({ email: '<EMAIL>' })\r\n        .expect(200); // Should return 200 for security reasons\r\n\r\n      expect(response.body.message).toBe('Password reset email sent');\r\n    });\r\n\r\n    // Note: In a real implementation, you would test the actual reset flow\r\n    // with a valid reset token, but that requires email integration\r\n  });\r\n\r\n  describe('Email Verification Flow', () => {\r\n    it('should resend verification email', async () => {\r\n      // Login first to get a valid token\r\n      const loginResponse = await request(app.getHttpServer())\r\n        .post('/api/v1/auth/login')\r\n        .send({\r\n          email: '<EMAIL>',\r\n          password: 'NewSecurePassword456!',\r\n        })\r\n        .expect(200);\r\n\r\n      const newAuthToken = loginResponse.body.data.tokens.accessToken;\r\n\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/auth/resend-verification')\r\n        .set('Authorization', `Bearer ${newAuthToken}`)\r\n        .expect(200);\r\n\r\n      expect(response.body.message).toBe('Verification email sent');\r\n    });\r\n\r\n    // Note: In a real implementation, you would test the actual verification flow\r\n    // with a valid verification token\r\n  });\r\n\r\n  describe('Role-Based Access Control', () => {\r\n    it('should access user-level endpoints', async () => {\r\n      // Login to get fresh token\r\n      const loginResponse = await request(app.getHttpServer())\r\n        .post('/api/v1/auth/login')\r\n        .send({\r\n          email: '<EMAIL>',\r\n          password: 'NewSecurePassword456!',\r\n        })\r\n        .expect(200);\r\n\r\n      const userToken = loginResponse.body.data.tokens.accessToken;\r\n\r\n      await request(app.getHttpServer())\r\n        .get('/api/v1/auth/profile')\r\n        .set('Authorization', `Bearer ${userToken}`)\r\n        .expect(200);\r\n    });\r\n\r\n    it('should reject admin-level endpoints for regular user', async () => {\r\n      // Login to get fresh token\r\n      const loginResponse = await request(app.getHttpServer())\r\n        .post('/api/v1/auth/login')\r\n        .send({\r\n          email: '<EMAIL>',\r\n          password: 'NewSecurePassword456!',\r\n        })\r\n        .expect(200);\r\n\r\n      const userToken = loginResponse.body.data.tokens.accessToken;\r\n\r\n      await request(app.getHttpServer())\r\n        .get('/api/v1/admin/users')\r\n        .set('Authorization', `Bearer ${userToken}`)\r\n        .expect(403);\r\n    });\r\n  });\r\n\r\n  describe('Security Features', () => {\r\n    it('should include security headers in responses', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/auth/login')\r\n        .send({\r\n          email: '<EMAIL>',\r\n          password: 'NewSecurePassword456!',\r\n        })\r\n        .expect(200);\r\n\r\n      // Check for security headers (these would be set by middleware)\r\n      expect(response.headers).toBeDefined();\r\n    });\r\n\r\n    it('should handle concurrent login attempts', async () => {\r\n      const loginData = {\r\n        email: '<EMAIL>',\r\n        password: 'NewSecurePassword456!',\r\n      };\r\n\r\n      // Make multiple concurrent login requests\r\n      const requests = Array(5).fill(null).map(() =>\r\n        request(app.getHttpServer())\r\n          .post('/api/v1/auth/login')\r\n          .send(loginData)\r\n      );\r\n\r\n      const responses = await Promise.all(requests);\r\n      \r\n      // All should succeed (no race conditions)\r\n      responses.forEach(response => {\r\n        expect(response.status).toBe(200);\r\n        expect(response.body.data.tokens.accessToken).toBeDefined();\r\n      });\r\n    });\r\n\r\n    it('should validate JWT token format', async () => {\r\n      const malformedTokens = [\r\n        'not-a-jwt-token',\r\n        'header.payload', // Missing signature\r\n        'header.payload.signature.extra', // Too many parts\r\n        '', // Empty token\r\n      ];\r\n\r\n      for (const token of malformedTokens) {\r\n        await request(app.getHttpServer())\r\n          .get('/api/v1/auth/profile')\r\n          .set('Authorization', `Bearer ${token}`)\r\n          .expect(401);\r\n      }\r\n    });\r\n  });\r\n\r\n  describe('Error Handling', () => {\r\n    it('should handle database connection errors gracefully', async () => {\r\n      // This would require mocking database failures\r\n      // In a real test, you might temporarily disconnect the database\r\n      \r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/auth/login')\r\n        .send({\r\n          email: '<EMAIL>',\r\n          password: 'NewSecurePassword456!',\r\n        });\r\n\r\n      // Should either succeed or return appropriate error\r\n      expect([200, 503]).toContain(response.status);\r\n    });\r\n\r\n    it('should maintain consistent error format', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/auth/login')\r\n        .send({\r\n          email: 'invalid-email',\r\n          password: 'short',\r\n        })\r\n        .expect(400);\r\n\r\n      expect(response.body).toHaveProperty('statusCode');\r\n      expect(response.body).toHaveProperty('message');\r\n      expect(response.body).toHaveProperty('timestamp');\r\n    });\r\n  });\r\n\r\n  describe('Performance', () => {\r\n    it('should handle authentication requests efficiently', async () => {\r\n      const startTime = Date.now();\r\n      \r\n      await request(app.getHttpServer())\r\n        .post('/api/v1/auth/login')\r\n        .send({\r\n          email: '<EMAIL>',\r\n          password: 'NewSecurePassword456!',\r\n        })\r\n        .expect(200);\r\n      \r\n      const endTime = Date.now();\r\n      const duration = endTime - startTime;\r\n\r\n      // Authentication should complete within reasonable time\r\n      expect(duration).toBeLessThan(2000); // 2 seconds\r\n    });\r\n  });\r\n});"], "version": 3}