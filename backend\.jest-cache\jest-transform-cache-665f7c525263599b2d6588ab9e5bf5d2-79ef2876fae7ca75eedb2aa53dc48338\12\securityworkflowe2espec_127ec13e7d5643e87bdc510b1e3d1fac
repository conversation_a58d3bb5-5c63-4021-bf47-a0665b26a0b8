2773ad7eb439b5ee9e423be07403f8ae
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const supertest_1 = __importDefault(require("supertest"));
const app_module_1 = require("../../app.module");
describe('Security Workflow (e2e)', () => {
    let app;
    let authToken;
    let userId;
    beforeAll(async () => {
        const moduleFixture = await testing_1.Test.createTestingModule({
            imports: [
                config_1.ConfigModule.forRoot({
                    isGlobal: true,
                    envFilePath: '.env.test',
                }),
                app_module_1.AppModule,
            ],
        }).compile();
        app = moduleFixture.createNestApplication();
        await app.init();
        // Setup authentication for protected endpoints
        const authResponse = await (0, supertest_1.default)(app.getHttpServer())
            .post('/api/v1/auth/register')
            .send({
            email: '<EMAIL>',
            password: 'SecurePassword123!',
            name: 'Security Test User',
        });
        authToken = authResponse.body.data.tokens.accessToken;
        userId = authResponse.body.data.user.id;
    });
    afterAll(async () => {
        await app.close();
    });
    describe('Input Validation and Sanitization', () => {
        it('should sanitize XSS attempts in input', async () => {
            const maliciousInput = {
                name: '<script>alert("xss")</script>Clean Name',
                description: '<img src="x" onerror="alert(1)">Description',
                category: 'test',
            };
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/items')
                .set('Authorization', `Bearer ${authToken}`)
                .send(maliciousInput)
                .expect(201);
            // XSS should be sanitized
            expect(response.body.data.name).toBe('Clean Name');
            expect(response.body.data.name).not.toContain('<script>');
            expect(response.body.data.description).toBe('Description');
            expect(response.body.data.description).not.toContain('<img');
        });
        it('should prevent SQL injection attempts', async () => {
            const sqlInjectionAttempts = [
                "'; DROP TABLE users; --",
                "1' OR '1'='1",
                "admin'--",
                "' UNION SELECT * FROM users --",
            ];
            for (const injection of sqlInjectionAttempts) {
                const response = await (0, supertest_1.default)(app.getHttpServer())
                    .get('/api/v1/test/items')
                    .query({ search: injection })
                    .set('Authorization', `Bearer ${authToken}`)
                    .expect(200);
                // Should return normal results, not execute SQL
                expect(response.body.success).toBe(true);
                expect(response.body.data).toEqual(expect.any(Array));
            }
        });
        it('should validate and reject malformed JSON', async () => {
            await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/items')
                .set('Authorization', `Bearer ${authToken}`)
                .set('Content-Type', 'application/json')
                .send('{"name": "test", "invalid": json}') // Malformed JSON
                .expect(400);
        });
        it('should enforce maximum request size limits', async () => {
            const largePayload = {
                name: 'Large Item',
                description: 'A'.repeat(10000), // Very large description
                category: 'test',
                data: Array.from({ length: 1000 }, (_, i) => ({
                    key: `key${i}`,
                    value: 'x'.repeat(1000),
                })),
            };
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/items')
                .set('Authorization', `Bearer ${authToken}`)
                .send(largePayload);
            // Should either accept (if within limits) or reject (if too large)
            expect([201, 413]).toContain(response.status);
        });
        it('should validate file upload security', async () => {
            // Test file upload with malicious file types
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/upload')
                .set('Authorization', `Bearer ${authToken}`)
                .attach('file', Buffer.from('<?php echo "malicious"; ?>'), {
                filename: 'malicious.php',
                contentType: 'application/x-php',
            });
            // Should reject dangerous file types
            expect([400, 415]).toContain(response.status);
        });
    });
    describe('Authentication Security', () => {
        it('should enforce strong password requirements', async () => {
            const weakPasswords = [
                '123456',
                'password',
                'qwerty',
                'abc123',
                'Password', // Missing special character and number
                'password123', // Missing uppercase and special character
            ];
            for (const weakPassword of weakPasswords) {
                const response = await (0, supertest_1.default)(app.getHttpServer())
                    .post('/api/v1/auth/register')
                    .send({
                    email: `weak-${Date.now()}@example.com`,
                    password: weakPassword,
                    name: 'Weak Password User',
                })
                    .expect(400);
                expect(response.body.message).toContain('password');
            }
        });
        it('should prevent brute force attacks with rate limiting', async () => {
            const loginAttempts = Array.from({ length: 10 }, () => (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/login')
                .send({
                email: '<EMAIL>',
                password: 'WrongPassword',
            }));
            const responses = await Promise.all(loginAttempts.map(req => req.then(res => ({ status: res.status }))
                .catch(err => ({ status: err.response?.status || 500 }))));
            // Some requests should be rate limited
            const rateLimitedCount = responses.filter(r => r.status === 429).length;
            expect(rateLimitedCount).toBeGreaterThan(0);
        });
        it('should invalidate tokens on password change', async () => {
            // Login to get a token
            const loginResponse = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/login')
                .send({
                email: '<EMAIL>',
                password: 'SecurePassword123!',
            })
                .expect(200);
            const oldToken = loginResponse.body.data.tokens.accessToken;
            // Change password
            await (0, supertest_1.default)(app.getHttpServer())
                .put('/api/v1/auth/change-password')
                .set('Authorization', `Bearer ${oldToken}`)
                .send({
                currentPassword: 'SecurePassword123!',
                newPassword: 'NewSecurePassword456!',
            })
                .expect(200);
            // Old token should be invalid
            await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/auth/profile')
                .set('Authorization', `Bearer ${oldToken}`)
                .expect(401);
        });
        it('should prevent session fixation attacks', async () => {
            // Login with one session
            const session1 = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/login')
                .send({
                email: '<EMAIL>',
                password: 'NewSecurePassword456!',
            })
                .expect(200);
            // Login with another session
            const session2 = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/login')
                .send({
                email: '<EMAIL>',
                password: 'NewSecurePassword456!',
            })
                .expect(200);
            // Both sessions should have different tokens
            expect(session1.body.data.tokens.accessToken).not.toBe(session2.body.data.tokens.accessToken);
        });
    });
    describe('Authorization Security', () => {
        it('should enforce role-based access control', async () => {
            // Regular user should not access admin endpoints
            await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/admin/users')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(403);
            await (0, supertest_1.default)(app.getHttpServer())
                .delete('/api/v1/admin/users/some-id')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(403);
        });
        it('should prevent privilege escalation', async () => {
            // User should not be able to modify their own roles
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .put('/api/v1/auth/profile')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                name: 'Updated Name',
                roles: ['admin'], // Attempting to escalate privileges
            })
                .expect(200);
            // Roles should not be updated
            expect(response.body.data.user.roles).toEqual(['user']);
        });
        it('should validate resource ownership', async () => {
            // Create an item as the current user
            const createResponse = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/items')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                name: 'User Item',
                description: 'Item owned by user',
                category: 'test',
            })
                .expect(201);
            const itemId = createResponse.body.data.id;
            // Create another user
            const otherUserResponse = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/register')
                .send({
                email: '<EMAIL>',
                password: 'OtherPassword123!',
                name: 'Other User',
            })
                .expect(201);
            const otherUserToken = otherUserResponse.body.data.tokens.accessToken;
            // Other user should not be able to modify the item
            await (0, supertest_1.default)(app.getHttpServer())
                .put(`/api/v1/test/items/${itemId}`)
                .set('Authorization', `Bearer ${otherUserToken}`)
                .send({
                name: 'Modified by other user',
            })
                .expect(403);
        });
    });
    describe('Data Protection', () => {
        it('should not expose sensitive data in responses', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/auth/profile')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            // Password should never be in response
            expect(response.body.data.user).not.toHaveProperty('password');
            expect(response.body.data.user).not.toHaveProperty('passwordHash');
            // Internal fields should not be exposed
            expect(response.body.data.user).not.toHaveProperty('__v');
            expect(response.body.data.user).not.toHaveProperty('_id');
        });
        it('should encrypt sensitive data in transit', async () => {
            // All API responses should use HTTPS in production
            // This test verifies that security headers are present
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/health')
                .expect(200);
            // In a real implementation, check for security headers
            expect(response.headers).toBeDefined();
        });
        it('should handle PII data appropriately', async () => {
            const piiData = {
                name: 'John Doe',
                email: '<EMAIL>',
                phone: '+1-************',
                ssn: '***********', // Should be rejected or masked
            };
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .put('/api/v1/auth/profile')
                .set('Authorization', `Bearer ${authToken}`)
                .send(piiData);
            // Should either reject SSN or mask it
            if (response.status === 200) {
                expect(response.body.data.user).not.toHaveProperty('ssn');
            }
            else {
                expect(response.status).toBe(400);
            }
        });
    });
    describe('Security Headers', () => {
        it('should include security headers in all responses', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/health')
                .expect(200);
            // Check for common security headers
            const headers = response.headers;
            // These would be set by security middleware in a real implementation
            expect(headers).toBeDefined();
            // In a real test, you would check for:
            // expect(headers['x-frame-options']).toBe('DENY');
            // expect(headers['x-content-type-options']).toBe('nosniff');
            // expect(headers['x-xss-protection']).toBe('1; mode=block');
            // expect(headers['strict-transport-security']).toBeDefined();
            // expect(headers['content-security-policy']).toBeDefined();
        });
        it('should handle CORS properly', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .options('/api/v1/health')
                .set('Origin', 'https://trusted-domain.com')
                .set('Access-Control-Request-Method', 'GET')
                .expect(200);
            expect(response.headers['access-control-allow-origin']).toBeDefined();
        });
        it('should reject requests from untrusted origins', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/health')
                .set('Origin', 'https://malicious-domain.com');
            // Should either block or allow based on CORS configuration
            expect([200, 403]).toContain(response.status);
        });
    });
    describe('Error Handling Security', () => {
        it('should not expose stack traces in production', async () => {
            // Trigger an error
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/nonexistent-endpoint')
                .expect(404);
            // Should not expose internal details
            expect(response.body).not.toHaveProperty('stack');
            expect(response.body.message).not.toContain('at ');
            expect(response.body.message).not.toContain('node_modules');
        });
        it('should provide consistent error responses', async () => {
            const responses = await Promise.all([
                (0, supertest_1.default)(app.getHttpServer()).get('/api/v1/nonexistent'),
                (0, supertest_1.default)(app.getHttpServer()).post('/api/v1/nonexistent'),
                (0, supertest_1.default)(app.getHttpServer()).put('/api/v1/nonexistent'),
                (0, supertest_1.default)(app.getHttpServer()).delete('/api/v1/nonexistent'),
            ]);
            responses.forEach(response => {
                expect(response.status).toBe(404);
                expect(response.body).toHaveProperty('statusCode');
                expect(response.body).toHaveProperty('message');
                expect(response.body).toHaveProperty('timestamp');
            });
        });
        it('should handle timeout errors gracefully', async () => {
            // This would require mocking slow operations
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/health')
                .timeout(100); // Very short timeout
            // Should handle timeout gracefully
            expect([200, 408, 503]).toContain(response.status);
        });
    });
    describe('Audit and Logging Security', () => {
        it('should log security events', async () => {
            // Failed login attempt
            await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/login')
                .send({
                email: '<EMAIL>',
                password: 'WrongPassword',
            })
                .expect(401);
            // In a real implementation, you would verify that this is logged
            // This test serves as documentation of the requirement
        });
        it('should log privilege escalation attempts', async () => {
            // Attempt to access admin endpoint
            await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/admin/users')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(403);
            // In a real implementation, you would verify that this is logged
        });
        it('should not log sensitive data', async () => {
            // Login with password
            await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/login')
                .send({
                email: '<EMAIL>',
                password: 'NewSecurePassword456!',
            })
                .expect(200);
            // In a real implementation, you would verify that passwords are not logged
        });
    });
    describe('Cryptographic Security', () => {
        it('should use secure random values', async () => {
            // Generate multiple tokens and verify they are different
            const tokens = [];
            for (let i = 0; i < 5; i++) {
                const response = await (0, supertest_1.default)(app.getHttpServer())
                    .post('/api/v1/auth/login')
                    .send({
                    email: '<EMAIL>',
                    password: 'NewSecurePassword456!',
                })
                    .expect(200);
                tokens.push(response.body.data.tokens.accessToken);
            }
            // All tokens should be unique
            const uniqueTokens = new Set(tokens);
            expect(uniqueTokens.size).toBe(tokens.length);
        });
        it('should handle encryption/decryption securely', async () => {
            // Test encrypted data storage and retrieval
            const sensitiveData = {
                name: 'Sensitive Item',
                description: 'This contains sensitive information',
                category: 'sensitive',
                encryptedField: 'secret-data-123',
            };
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/secure-items')
                .set('Authorization', `Bearer ${authToken}`)
                .send(sensitiveData)
                .expect(201);
            // Encrypted fields should not be returned in plain text
            expect(response.body.data.encryptedField).not.toBe(sensitiveData.encryptedField);
        });
    });
    describe('Session Security', () => {
        it('should invalidate sessions on logout', async () => {
            // Login to get a session
            const loginResponse = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/login')
                .send({
                email: '<EMAIL>',
                password: 'NewSecurePassword456!',
            })
                .expect(200);
            const sessionToken = loginResponse.body.data.tokens.accessToken;
            // Logout
            await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/logout')
                .set('Authorization', `Bearer ${sessionToken}`)
                .expect(200);
            // Session should be invalid
            await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/auth/profile')
                .set('Authorization', `Bearer ${sessionToken}`)
                .expect(401);
        });
        it('should handle concurrent sessions securely', async () => {
            // Create multiple sessions
            const sessions = await Promise.all([
                (0, supertest_1.default)(app.getHttpServer())
                    .post('/api/v1/auth/login')
                    .send({
                    email: '<EMAIL>',
                    password: 'NewSecurePassword456!',
                }),
                (0, supertest_1.default)(app.getHttpServer())
                    .post('/api/v1/auth/login')
                    .send({
                    email: '<EMAIL>',
                    password: 'NewSecurePassword456!',
                }),
            ]);
            // Both sessions should be valid
            for (const session of sessions) {
                expect(session.status).toBe(200);
                await (0, supertest_1.default)(app.getHttpServer())
                    .get('/api/v1/auth/profile')
                    .set('Authorization', `Bearer ${session.body.data.tokens.accessToken}`)
                    .expect(200);
            }
        });
        it('should enforce session timeout', async () => {
            // This would require mocking time or waiting for actual timeout
            // In a real implementation, you would test session expiration
            const loginResponse = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/login')
                .send({
                email: '<EMAIL>',
                password: 'NewSecurePassword456!',
            })
                .expect(200);
            const token = loginResponse.body.data.tokens.accessToken;
            // Verify token is currently valid
            await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/auth/profile')
                .set('Authorization', `Bearer ${token}`)
                .expect(200);
            // In a real test, you would wait for expiration or mock time
        });
    });
    describe('API Security', () => {
        it('should validate API version security', async () => {
            // Test deprecated API versions
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v0/health') // Hypothetical deprecated version
                .expect(404);
            expect(response.body.message).toContain('not found');
        });
        it('should handle malformed requests securely', async () => {
            const malformedRequests = [
                // Extremely long URL
                '/api/v1/' + 'a'.repeat(10000),
                // Special characters in URL
                '/api/v1/test/../../../etc/passwd',
                // Null bytes
                '/api/v1/test\x00items',
            ];
            for (const url of malformedRequests) {
                const response = await (0, supertest_1.default)(app.getHttpServer())
                    .get(url)
                    .set('Authorization', `Bearer ${authToken}`);
                // Should handle gracefully without crashing
                expect([400, 404, 500]).toContain(response.status);
            }
        });
        it('should prevent parameter pollution', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/test/items')
                .query('limit=10&limit=1000&limit=5') // Multiple limit parameters
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            // Should handle parameter pollution gracefully
            expect(response.body.pagination.limit).toBeLessThanOrEqual(100); // Assuming max limit
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************