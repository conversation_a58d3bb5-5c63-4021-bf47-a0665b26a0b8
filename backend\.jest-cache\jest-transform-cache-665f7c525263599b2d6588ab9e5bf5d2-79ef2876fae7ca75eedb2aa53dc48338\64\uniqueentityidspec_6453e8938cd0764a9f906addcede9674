6e4140db831d7899ddc3cb5fe71a1f0b
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const unique_entity_id_value_object_1 = require("../../value-objects/unique-entity-id.value-object");
const uuid_1 = require("uuid");
describe('UniqueEntityId', () => {
    describe('construction and validation', () => {
        it('should create a valid UniqueEntityId with valid UUID', () => {
            const uuid = (0, uuid_1.v4)();
            const id = new unique_entity_id_value_object_1.UniqueEntityId(uuid);
            expect(id.value).toBe(uuid);
        });
        it('should throw error for null value', () => {
            expect(() => new unique_entity_id_value_object_1.UniqueEntityId(null)).toThrow('UniqueEntityId cannot be null or undefined');
        });
        it('should throw error for undefined value', () => {
            expect(() => new unique_entity_id_value_object_1.UniqueEntityId(undefined)).toThrow('UniqueEntityId cannot be null or undefined');
        });
        it('should throw error for empty string', () => {
            expect(() => new unique_entity_id_value_object_1.UniqueEntityId('')).toThrow('UniqueEntityId cannot be empty');
        });
        it('should throw error for whitespace string', () => {
            expect(() => new unique_entity_id_value_object_1.UniqueEntityId('   ')).toThrow('UniqueEntityId cannot be empty');
        });
        it('should throw error for non-string value', () => {
            expect(() => new unique_entity_id_value_object_1.UniqueEntityId(123)).toThrow('UniqueEntityId must be a string');
        });
        it('should throw error for invalid UUID format', () => {
            expect(() => new unique_entity_id_value_object_1.UniqueEntityId('invalid-uuid')).toThrow('UniqueEntityId must be a valid UUID');
        });
        it('should throw error for non-v4 UUID', () => {
            const v1Uuid = '550e8400-e29b-11d4-a716-************'; // UUID v1 format
            expect(() => new unique_entity_id_value_object_1.UniqueEntityId(v1Uuid)).toThrow('UniqueEntityId must be a valid UUID v4 format');
        });
        it('should accept valid UUID v4', () => {
            const validUuid = '550e8400-e29b-41d4-a716-************'; // UUID v4 format
            expect(() => new unique_entity_id_value_object_1.UniqueEntityId(validUuid)).not.toThrow();
        });
    });
    describe('factory methods', () => {
        it('should generate a new unique ID', () => {
            const id = unique_entity_id_value_object_1.UniqueEntityId.generate();
            expect(id).toBeInstanceOf(unique_entity_id_value_object_1.UniqueEntityId);
            expect((0, uuid_1.validate)(id.value)).toBe(true);
        });
        it('should generate different IDs each time', () => {
            const id1 = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const id2 = unique_entity_id_value_object_1.UniqueEntityId.generate();
            expect(id1.equals(id2)).toBe(false);
        });
        it('should create from string', () => {
            const uuid = (0, uuid_1.v4)();
            const id = unique_entity_id_value_object_1.UniqueEntityId.fromString(uuid);
            expect(id.value).toBe(uuid);
        });
        it('should create from existing ID', () => {
            const original = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const copy = unique_entity_id_value_object_1.UniqueEntityId.fromId(original);
            expect(copy.equals(original)).toBe(true);
            expect(copy).not.toBe(original); // Different instances
        });
        it('should generate multiple IDs', () => {
            const ids = unique_entity_id_value_object_1.UniqueEntityId.generateMany(5);
            expect(ids).toHaveLength(5);
            expect(ids.every(id => id instanceof unique_entity_id_value_object_1.UniqueEntityId)).toBe(true);
            // All should be unique
            const values = ids.map(id => id.value);
            const uniqueValues = new Set(values);
            expect(uniqueValues.size).toBe(5);
        });
        it('should handle zero count for generateMany', () => {
            const ids = unique_entity_id_value_object_1.UniqueEntityId.generateMany(0);
            expect(ids).toHaveLength(0);
        });
        it('should throw error for negative count', () => {
            expect(() => unique_entity_id_value_object_1.UniqueEntityId.generateMany(-1)).toThrow('Count must be non-negative');
        });
        it('should create deterministic ID from seed', () => {
            const id1 = unique_entity_id_value_object_1.UniqueEntityId.fromSeed('test-seed');
            const id2 = unique_entity_id_value_object_1.UniqueEntityId.fromSeed('test-seed');
            const id3 = unique_entity_id_value_object_1.UniqueEntityId.fromSeed('different-seed');
            expect(id1.equals(id2)).toBe(true);
            expect(id1.equals(id3)).toBe(false);
        });
    });
    describe('validation utilities', () => {
        it('should validate valid UUID string', () => {
            const uuid = (0, uuid_1.v4)();
            expect(unique_entity_id_value_object_1.UniqueEntityId.isValid(uuid)).toBe(true);
        });
        it('should reject invalid UUID string', () => {
            expect(unique_entity_id_value_object_1.UniqueEntityId.isValid('invalid')).toBe(false);
            expect(unique_entity_id_value_object_1.UniqueEntityId.isValid('')).toBe(false);
            expect(unique_entity_id_value_object_1.UniqueEntityId.isValid(null)).toBe(false);
            expect(unique_entity_id_value_object_1.UniqueEntityId.isValid(undefined)).toBe(false);
        });
        it('should try parse valid UUID', () => {
            const uuid = (0, uuid_1.v4)();
            const id = unique_entity_id_value_object_1.UniqueEntityId.tryParse(uuid);
            expect(id).toBeInstanceOf(unique_entity_id_value_object_1.UniqueEntityId);
            expect(id.value).toBe(uuid);
        });
        it('should return null for invalid UUID in tryParse', () => {
            const id = unique_entity_id_value_object_1.UniqueEntityId.tryParse('invalid');
            expect(id).toBeNull();
        });
    });
    describe('UUID information', () => {
        it('should get UUID version', () => {
            const id = unique_entity_id_value_object_1.UniqueEntityId.generate();
            expect(id.getVersion()).toBe(4);
        });
        it('should get UUID variant', () => {
            const id = unique_entity_id_value_object_1.UniqueEntityId.generate();
            expect(id.getVariant()).toBe('RFC4122');
        });
        it('should return null timestamp for v4 UUID', () => {
            const id = unique_entity_id_value_object_1.UniqueEntityId.generate();
            expect(id.getTimestamp()).toBeNull();
        });
    });
    describe('string representations', () => {
        it('should convert to string', () => {
            const uuid = (0, uuid_1.v4)();
            const id = new unique_entity_id_value_object_1.UniqueEntityId(uuid);
            expect(id.toString()).toBe(uuid);
        });
        it('should create short string representation', () => {
            const id = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const shortString = id.toShortString();
            expect(shortString).toHaveLength(8);
            expect(id.value.startsWith(shortString)).toBe(true);
        });
        it('should create short string with custom length', () => {
            const id = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const shortString = id.toShortString(12);
            expect(shortString).toHaveLength(12);
        });
        it('should create compact string without hyphens', () => {
            const uuid = '550e8400-e29b-41d4-a716-************';
            const id = new unique_entity_id_value_object_1.UniqueEntityId(uuid);
            expect(id.toCompactString()).toBe('550e8400e29b41d4a716************');
        });
        it('should convert to uppercase', () => {
            const uuid = '550e8400-e29b-41d4-a716-************';
            const id = new unique_entity_id_value_object_1.UniqueEntityId(uuid);
            expect(id.toUpperCase()).toBe('550E8400-E29B-41D4-A716-************');
        });
        it('should convert to lowercase', () => {
            const uuid = '550E8400-E29B-41D4-A716-************';
            const id = new unique_entity_id_value_object_1.UniqueEntityId(uuid);
            expect(id.toLowerCase()).toBe('550e8400-e29b-41d4-a716-************');
        });
    });
    describe('equality comparison', () => {
        it('should be equal to itself', () => {
            const id = unique_entity_id_value_object_1.UniqueEntityId.generate();
            expect(id.equals(id)).toBe(true);
        });
        it('should be equal to another ID with same value', () => {
            const uuid = (0, uuid_1.v4)();
            const id1 = new unique_entity_id_value_object_1.UniqueEntityId(uuid);
            const id2 = new unique_entity_id_value_object_1.UniqueEntityId(uuid);
            expect(id1.equals(id2)).toBe(true);
        });
        it('should not be equal to ID with different value', () => {
            const id1 = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const id2 = unique_entity_id_value_object_1.UniqueEntityId.generate();
            expect(id1.equals(id2)).toBe(false);
        });
        it('should not be equal to null or undefined', () => {
            const id = unique_entity_id_value_object_1.UniqueEntityId.generate();
            expect(id.equals(null)).toBe(false);
            expect(id.equals(undefined)).toBe(false);
        });
        it('should handle case-insensitive comparison', () => {
            const uuid = '550e8400-e29b-41d4-a716-************';
            const id1 = new unique_entity_id_value_object_1.UniqueEntityId(uuid.toLowerCase());
            const id2 = new unique_entity_id_value_object_1.UniqueEntityId(uuid.toUpperCase());
            expect(id1.equals(id2)).toBe(true);
        });
        it('should not be equal to non-UniqueEntityId object', () => {
            const id = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const other = { value: id.value };
            expect(id.equals(other)).toBe(false);
        });
    });
    describe('JSON serialization', () => {
        it('should serialize to JSON', () => {
            const id = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const json = id.toJSON();
            expect(json).toHaveProperty('value', id.value);
            expect(json).toHaveProperty('version', 4);
            expect(json).toHaveProperty('variant', 'RFC4122');
            expect(json).toHaveProperty('shortString');
            expect(json).toHaveProperty('compactString');
        });
        it('should deserialize from JSON', () => {
            const original = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const json = original.toJSON();
            const deserialized = unique_entity_id_value_object_1.UniqueEntityId.fromJSON(json);
            expect(deserialized.equals(original)).toBe(true);
        });
    });
    describe('immutability', () => {
        it('should be immutable', () => {
            const id = unique_entity_id_value_object_1.UniqueEntityId.generate();
            expect(Object.isFrozen(id)).toBe(true);
        });
        it('should not allow value modification', () => {
            const id = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const originalValue = id.value;
            // Attempt to modify (should fail silently or throw in strict mode)
            try {
                id._value = 'modified';
            }
            catch {
                // Expected in strict mode
            }
            expect(id.value).toBe(originalValue);
        });
    });
    describe('hash code', () => {
        it('should generate consistent hash codes', () => {
            const uuid = (0, uuid_1.v4)();
            const id1 = new unique_entity_id_value_object_1.UniqueEntityId(uuid);
            const id2 = new unique_entity_id_value_object_1.UniqueEntityId(uuid);
            expect(id1.getHashCode()).toBe(id2.getHashCode());
        });
        it('should generate different hash codes for different values', () => {
            const id1 = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const id2 = unique_entity_id_value_object_1.UniqueEntityId.generate();
            expect(id1.getHashCode()).not.toBe(id2.getHashCode());
        });
    });
    describe('cloning', () => {
        it('should create a clone with same value', () => {
            const id = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const clone = id.clone();
            expect(clone).not.toBe(id);
            expect(clone.equals(id)).toBe(true);
            expect(clone.value).toBe(id.value);
        });
    });
    describe('edge cases', () => {
        it('should handle UUID with different casing in constructor', () => {
            const uuid = '550e8400-e29b-41d4-a716-************';
            const upperCaseId = new unique_entity_id_value_object_1.UniqueEntityId(uuid.toUpperCase());
            const lowerCaseId = new unique_entity_id_value_object_1.UniqueEntityId(uuid.toLowerCase());
            expect(upperCaseId.equals(lowerCaseId)).toBe(true);
        });
        it('should maintain original casing in value', () => {
            const upperUuid = '550E8400-E29B-41D4-A716-************';
            const id = new unique_entity_id_value_object_1.UniqueEntityId(upperUuid);
            expect(id.value).toBe(upperUuid);
        });
        it('should handle very long seed strings', () => {
            const longSeed = 'a'.repeat(1000);
            const id = unique_entity_id_value_object_1.UniqueEntityId.fromSeed(longSeed);
            expect(id).toBeInstanceOf(unique_entity_id_value_object_1.UniqueEntityId);
            expect((0, uuid_1.validate)(id.value)).toBe(true);
        });
        it('should handle empty seed string', () => {
            const id = unique_entity_id_value_object_1.UniqueEntityId.fromSeed('');
            expect(id).toBeInstanceOf(unique_entity_id_value_object_1.UniqueEntityId);
            expect((0, uuid_1.validate)(id.value)).toBe(true);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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