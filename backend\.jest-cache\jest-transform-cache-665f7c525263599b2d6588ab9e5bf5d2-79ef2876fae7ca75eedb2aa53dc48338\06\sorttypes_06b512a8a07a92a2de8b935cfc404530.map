{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\types\\sort.types.ts", "mappings": ";AAAA;;;;;GAKG;;;AAEH;;;GAGG;AACH,IAAY,aAGX;AAHD,WAAY,aAAa;IACvB,4BAAW,CAAA;IACX,8BAAa,CAAA;AACf,CAAC,EAHW,aAAa,6BAAb,aAAa,QAGxB;AA6DD;;GAEG;AACH,MAAa,SAAS;IAYpB;;OAEG;IACH,MAAM,CAAC,YAAY,CACjB,UAAsB,EACtB,SAAqB,SAAS,CAAC,cAAc;QAE7C,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,4BAA4B;QAC5B,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC;YAClD,MAAM,CAAC,IAAI,CAAC,0BAA0B,UAAU,CAAC,IAAI,CAAC,MAAM,8BAA8B,MAAM,CAAC,aAAa,GAAG,CAAC,CAAC;QACrH,CAAC;QAED,2BAA2B;QAC3B,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,KAAK,EAAE,EAAE;YAC3C,6BAA6B;YAC7B,IAAI,MAAM,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzF,MAAM,CAAC,IAAI,CAAC,UAAU,SAAS,CAAC,KAAK,mBAAmB,CAAC,CAAC;YAC5D,CAAC;YAED,8BAA8B;YAC9B,IAAI,SAAS,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;gBACpD,MAAM,CAAC,IAAI,CAAC,oDAAoD,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC;YACtF,CAAC;YAED,yBAAyB;YACzB,IAAI,SAAS,CAAC,aAAa,KAAK,SAAS,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;gBACxE,QAAQ,CAAC,IAAI,CAAC,+CAA+C,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC;YACnF,CAAC;YAED,6BAA6B;YAC7B,MAAM,cAAc,GAAG,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,UAAU,EAAE,EAAE,CACrE,UAAU,KAAK,KAAK,IAAI,KAAK,CAAC,KAAK,KAAK,SAAS,CAAC,KAAK,CACxD,CAAC;YACF,IAAI,cAAc,KAAK,CAAC,CAAC,EAAE,CAAC;gBAC1B,MAAM,CAAC,IAAI,CAAC,yBAAyB,SAAS,CAAC,KAAK,SAAS,CAAC,CAAC;YACjE,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;YACN,QAAQ;SACT,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,GAAG,CAAC,KAAa,EAAE,QAAiB;QACzC,OAAO;YACL,KAAK;YACL,SAAS,EAAE,aAAa,CAAC,GAAG;YAC5B,QAAQ;SACT,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,IAAI,CAAC,KAAa,EAAE,QAAiB;QAC1C,OAAO;YACL,KAAK;YACL,SAAS,EAAE,aAAa,CAAC,IAAI;YAC7B,QAAQ;SACT,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,GAAG,UAAuB;QACtC,gCAAgC;QAChC,MAAM,MAAM,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3C,MAAM,SAAS,GAAG,CAAC,CAAC,QAAQ,IAAI,MAAM,CAAC,gBAAgB,CAAC;YACxD,MAAM,SAAS,GAAG,CAAC,CAAC,QAAQ,IAAI,MAAM,CAAC,gBAAgB,CAAC;YACxD,OAAO,SAAS,GAAG,SAAS,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,IAAI,EAAE,MAAM;YACZ,mBAAmB,EAAE,IAAI;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS,CACd,KAA0B,EAC1B,SAAqB,SAAS,CAAC,cAAc;QAE7C,MAAM,UAAU,GAAgB,EAAE,CAAC;QAEnC,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;YACf,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC;YACjF,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAExC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAY,EAAE,KAAa,EAAE,EAAE;gBAChD,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;gBAC5B,IAAI,CAAC,OAAO;oBAAE,OAAO;gBAErB,IAAI,KAAa,CAAC;gBAClB,IAAI,SAAS,GAAkB,MAAM,CAAC,gBAAgB,CAAC;gBAEvD,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;oBAC5B,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oBAC7B,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC;gBACjC,CAAC;qBAAM,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;oBACnC,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oBAC7B,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC;gBAChC,CAAC;qBAAM,CAAC;oBACN,KAAK,GAAG,OAAO,CAAC;gBAClB,CAAC;gBAED,UAAU,CAAC,IAAI,CAAC;oBACd,KAAK;oBACL,SAAS;oBACT,QAAQ,EAAE,KAAK;iBAChB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,IAAI,EAAE,UAAU;YAChB,mBAAmB,EAAE,IAAI;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,UAAsB;QACzC,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjC,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,UAAU,GAAG,UAAU,CAAC,IAAI;aAC/B,GAAG,CAAC,KAAK,CAAC,EAAE;YACX,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,KAAK,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;YACjE,OAAO,GAAG,MAAM,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;QACnC,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,CAAC,CAAC;QAEb,OAAO,QAAQ,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS,CAAI,KAAU,EAAE,UAAsB;QACpD,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,CAAC,GAAG,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC9B,KAAK,MAAM,SAAS,IAAI,UAAU,CAAC,IAAI,EAAE,CAAC;gBACxC,IAAI,MAAc,CAAC;gBAEnB,IAAI,SAAS,CAAC,UAAU,EAAE,CAAC;oBACzB,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACtC,CAAC;qBAAM,CAAC;oBACN,MAAM,MAAM,GAAG,SAAS,CAAC,cAAc,CAAC,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;oBAC5D,MAAM,MAAM,GAAG,SAAS,CAAC,cAAc,CAAC,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;oBAC5D,MAAM,GAAG,SAAS,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC,aAAa,CAAC,CAAC;gBAC5E,CAAC;gBAED,IAAI,SAAS,CAAC,SAAS,KAAK,aAAa,CAAC,IAAI,EAAE,CAAC;oBAC/C,MAAM,GAAG,CAAC,MAAM,CAAC;gBACnB,CAAC;gBAED,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC;oBACjB,OAAO,MAAM,CAAC;gBAChB,CAAC;YACH,CAAC;YAED,OAAO,CAAC,CAAC;QACX,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,cAAc,CAAC,GAAQ,EAAE,IAAY;QAClD,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE;YAC7C,OAAO,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAC1E,CAAC,EAAE,GAAG,CAAC,CAAC;IACV,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,aAAa,CAAC,CAAM,EAAE,CAAM,EAAE,gBAAyB,IAAI;QACxE,+BAA+B;QAC/B,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI;YAAE,OAAO,CAAC,CAAC;QACrC,IAAI,CAAC,IAAI,IAAI;YAAE,OAAO,CAAC,CAAC,CAAC;QACzB,IAAI,CAAC,IAAI,IAAI;YAAE,OAAO,CAAC,CAAC;QAExB,2BAA2B;QAC3B,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC;YACnD,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC;gBACpB,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC;YACtB,CAAC;YACD,OAAO,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC;QAED,2BAA2B;QAC3B,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC;YACnD,OAAO,CAAC,GAAG,CAAC,CAAC;QACf,CAAC;QAED,yBAAyB;QACzB,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,IAAI,EAAE,CAAC;YAC3C,OAAO,CAAC,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;QACnC,CAAC;QAED,4BAA4B;QAC5B,IAAI,OAAO,CAAC,KAAK,SAAS,IAAI,OAAO,CAAC,KAAK,SAAS,EAAE,CAAC;YACrD,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC;QAED,mCAAmC;QACnC,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACvB,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAEvB,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;QAC9D,CAAC;QAED,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,SAAwB;QAC9C,OAAO,SAAS,KAAK,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC;IAClF,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,CAAC,UAAsB;QACnC,OAAO,UAAU,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CAAC,UAAsB;QAC/C,OAAO,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,GAAG,UAAwB;QACtC,MAAM,SAAS,GAAgB,EAAE,CAAC;QAClC,MAAM,UAAU,GAAG,IAAI,GAAG,EAAU,CAAC;QAErC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC1B,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAC1B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;oBACjC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACtB,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAC9B,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,IAAI,EAAE,SAAS;YACf,mBAAmB,EAAE,IAAI;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,UAAsB,EAAE,UAAmB;QAC7D,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjC,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,YAAY,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YAC/C,MAAM,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC;YAC5E,OAAO,GAAG,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,WAAW,EAAE,EAAE,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,OAAO,YAAY,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,UAAsB;QACvC,MAAM,SAAS,GAA2B,EAAE,CAAC;QAE7C,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC9B,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,SAAS,KAAK,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;QAEH,OAAO,SAAS,CAAC;IACnB,CAAC;;AA7TH,8BA8TC;AA7TC;;GAEG;AACa,wBAAc,GAAe;IAC3C,aAAa,EAAE,CAAC;IAChB,cAAc,EAAE,EAAE;IAClB,gBAAgB,EAAE,aAAa,CAAC,GAAG;IACnC,kBAAkB,EAAE,IAAI;IACxB,eAAe,EAAE,KAAK;CACvB,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\types\\sort.types.ts"], "sourcesContent": ["/**\r\n * Sort Types\r\n * \r\n * Common types and interfaces for sorting data across the application.\r\n * Provides consistent sorting patterns and utilities.\r\n */\r\n\r\n/**\r\n * Sort Direction\r\n * Supported sort directions\r\n */\r\nexport enum SortDirection {\r\n  ASC = 'asc',\r\n  DESC = 'desc',\r\n}\r\n\r\n/**\r\n * Sort Field\r\n * Represents a single sort field with direction\r\n */\r\nexport interface SortField {\r\n  /** Field name to sort by */\r\n  field: string;\r\n  /** Sort direction */\r\n  direction: SortDirection;\r\n  /** Priority/order of this sort field (lower numbers have higher priority) */\r\n  priority?: number;\r\n  /** Whether to use case-sensitive sorting for strings */\r\n  caseSensitive?: boolean;\r\n  /** Custom sort function for complex sorting logic */\r\n  customSort?: (a: any, b: any) => number;\r\n}\r\n\r\n/**\r\n * Sort Parameters\r\n * Complete sort specification for queries\r\n */\r\nexport interface SortParams {\r\n  /** Array of sort fields in order of priority */\r\n  sort: SortField[];\r\n  /** Default sort field if none specified */\r\n  defaultSort?: SortField;\r\n  /** Whether to allow multiple sort fields */\r\n  allowMultipleFields?: boolean;\r\n}\r\n\r\n/**\r\n * Sort Configuration\r\n * Configuration for sort behavior\r\n */\r\nexport interface SortConfig {\r\n  /** Maximum number of sort fields allowed */\r\n  maxSortFields: number;\r\n  /** Fields that support sorting */\r\n  sortableFields: string[];\r\n  /** Default sort direction */\r\n  defaultDirection: SortDirection;\r\n  /** Whether to allow case-sensitive sorting */\r\n  allowCaseSensitive: boolean;\r\n  /** Whether to allow custom sort functions */\r\n  allowCustomSort: boolean;\r\n}\r\n\r\n/**\r\n * Sort Validation Result\r\n */\r\nexport interface SortValidationResult {\r\n  /** Whether the sort is valid */\r\n  isValid: boolean;\r\n  /** Validation errors */\r\n  errors: string[];\r\n  /** Warnings (non-blocking issues) */\r\n  warnings: string[];\r\n}\r\n\r\n/**\r\n * Sort Utilities\r\n */\r\nexport class SortUtils {\r\n  /**\r\n   * Default sort configuration\r\n   */\r\n  static readonly DEFAULT_CONFIG: SortConfig = {\r\n    maxSortFields: 5,\r\n    sortableFields: [],\r\n    defaultDirection: SortDirection.ASC,\r\n    allowCaseSensitive: true,\r\n    allowCustomSort: false,\r\n  };\r\n\r\n  /**\r\n   * Validate sort parameters\r\n   */\r\n  static validateSort(\r\n    sortParams: SortParams,\r\n    config: SortConfig = SortUtils.DEFAULT_CONFIG\r\n  ): SortValidationResult {\r\n    const errors: string[] = [];\r\n    const warnings: string[] = [];\r\n\r\n    // Check maximum sort fields\r\n    if (sortParams.sort.length > config.maxSortFields) {\r\n      errors.push(`Number of sort fields (${sortParams.sort.length}) exceeds maximum allowed (${config.maxSortFields})`);\r\n    }\r\n\r\n    // Validate each sort field\r\n    sortParams.sort.forEach((sortField, index) => {\r\n      // Check if field is sortable\r\n      if (config.sortableFields.length > 0 && !config.sortableFields.includes(sortField.field)) {\r\n        errors.push(`Field '${sortField.field}' is not sortable`);\r\n      }\r\n\r\n      // Check custom sort functions\r\n      if (sortField.customSort && !config.allowCustomSort) {\r\n        errors.push(`Custom sort functions are not allowed for field '${sortField.field}'`);\r\n      }\r\n\r\n      // Check case sensitivity\r\n      if (sortField.caseSensitive !== undefined && !config.allowCaseSensitive) {\r\n        warnings.push(`Case sensitivity setting ignored for field '${sortField.field}'`);\r\n      }\r\n\r\n      // Check for duplicate fields\r\n      const duplicateIndex = sortParams.sort.findIndex((other, otherIndex) => \r\n        otherIndex !== index && other.field === sortField.field\r\n      );\r\n      if (duplicateIndex !== -1) {\r\n        errors.push(`Duplicate sort field '${sortField.field}' found`);\r\n      }\r\n    });\r\n\r\n    return {\r\n      isValid: errors.length === 0,\r\n      errors,\r\n      warnings,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create a simple ascending sort\r\n   */\r\n  static asc(field: string, priority?: number): SortField {\r\n    return {\r\n      field,\r\n      direction: SortDirection.ASC,\r\n      priority,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create a simple descending sort\r\n   */\r\n  static desc(field: string, priority?: number): SortField {\r\n    return {\r\n      field,\r\n      direction: SortDirection.DESC,\r\n      priority,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create sort parameters from multiple fields\r\n   */\r\n  static create(...sortFields: SortField[]): SortParams {\r\n    // Sort by priority if specified\r\n    const sorted = [...sortFields].sort((a, b) => {\r\n      const aPriority = a.priority ?? Number.MAX_SAFE_INTEGER;\r\n      const bPriority = b.priority ?? Number.MAX_SAFE_INTEGER;\r\n      return aPriority - bPriority;\r\n    });\r\n\r\n    return {\r\n      sort: sorted,\r\n      allowMultipleFields: true,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Parse sort from query parameters\r\n   */\r\n  static fromQuery(\r\n    query: Record<string, any>,\r\n    config: SortConfig = SortUtils.DEFAULT_CONFIG\r\n  ): SortParams {\r\n    const sortFields: SortField[] = [];\r\n\r\n    if (query.sort) {\r\n      const sortString = Array.isArray(query.sort) ? query.sort.join(',') : query.sort;\r\n      const sortParts = sortString.split(',');\r\n\r\n      sortParts.forEach((part: string, index: number) => {\r\n        const trimmed = part.trim();\r\n        if (!trimmed) return;\r\n\r\n        let field: string;\r\n        let direction: SortDirection = config.defaultDirection;\r\n\r\n        if (trimmed.startsWith('-')) {\r\n          field = trimmed.substring(1);\r\n          direction = SortDirection.DESC;\r\n        } else if (trimmed.startsWith('+')) {\r\n          field = trimmed.substring(1);\r\n          direction = SortDirection.ASC;\r\n        } else {\r\n          field = trimmed;\r\n        }\r\n\r\n        sortFields.push({\r\n          field,\r\n          direction,\r\n          priority: index,\r\n        });\r\n      });\r\n    }\r\n\r\n    return {\r\n      sort: sortFields,\r\n      allowMultipleFields: true,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert sort parameters to query string\r\n   */\r\n  static toQueryString(sortParams: SortParams): string {\r\n    if (sortParams.sort.length === 0) {\r\n      return '';\r\n    }\r\n\r\n    const sortString = sortParams.sort\r\n      .map(field => {\r\n        const prefix = field.direction === SortDirection.DESC ? '-' : '';\r\n        return `${prefix}${field.field}`;\r\n      })\r\n      .join(',');\r\n\r\n    return `sort=${encodeURIComponent(sortString)}`;\r\n  }\r\n\r\n  /**\r\n   * Apply sort to an array of objects\r\n   */\r\n  static applySort<T>(items: T[], sortParams: SortParams): T[] {\r\n    if (sortParams.sort.length === 0) {\r\n      return items;\r\n    }\r\n\r\n    return [...items].sort((a, b) => {\r\n      for (const sortField of sortParams.sort) {\r\n        let result: number;\r\n\r\n        if (sortField.customSort) {\r\n          result = sortField.customSort(a, b);\r\n        } else {\r\n          const aValue = SortUtils.getNestedValue(a, sortField.field);\r\n          const bValue = SortUtils.getNestedValue(b, sortField.field);\r\n          result = SortUtils.compareValues(aValue, bValue, sortField.caseSensitive);\r\n        }\r\n\r\n        if (sortField.direction === SortDirection.DESC) {\r\n          result = -result;\r\n        }\r\n\r\n        if (result !== 0) {\r\n          return result;\r\n        }\r\n      }\r\n\r\n      return 0;\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get nested property value from object\r\n   */\r\n  private static getNestedValue(obj: any, path: string): any {\r\n    return path.split('.').reduce((current, key) => {\r\n      return current && current[key] !== undefined ? current[key] : undefined;\r\n    }, obj);\r\n  }\r\n\r\n  /**\r\n   * Compare two values for sorting\r\n   */\r\n  private static compareValues(a: any, b: any, caseSensitive: boolean = true): number {\r\n    // Handle null/undefined values\r\n    if (a == null && b == null) return 0;\r\n    if (a == null) return -1;\r\n    if (b == null) return 1;\r\n\r\n    // Handle string comparison\r\n    if (typeof a === 'string' && typeof b === 'string') {\r\n      if (!caseSensitive) {\r\n        a = a.toLowerCase();\r\n        b = b.toLowerCase();\r\n      }\r\n      return a.localeCompare(b);\r\n    }\r\n\r\n    // Handle number comparison\r\n    if (typeof a === 'number' && typeof b === 'number') {\r\n      return a - b;\r\n    }\r\n\r\n    // Handle date comparison\r\n    if (a instanceof Date && b instanceof Date) {\r\n      return a.getTime() - b.getTime();\r\n    }\r\n\r\n    // Handle boolean comparison\r\n    if (typeof a === 'boolean' && typeof b === 'boolean') {\r\n      return a === b ? 0 : a ? 1 : -1;\r\n    }\r\n\r\n    // Convert to string for comparison\r\n    const aStr = String(a);\r\n    const bStr = String(b);\r\n    \r\n    if (!caseSensitive) {\r\n      return aStr.toLowerCase().localeCompare(bStr.toLowerCase());\r\n    }\r\n    \r\n    return aStr.localeCompare(bStr);\r\n  }\r\n\r\n  /**\r\n   * Reverse sort direction\r\n   */\r\n  static reverseDirection(direction: SortDirection): SortDirection {\r\n    return direction === SortDirection.ASC ? SortDirection.DESC : SortDirection.ASC;\r\n  }\r\n\r\n  /**\r\n   * Check if sort parameters are empty\r\n   */\r\n  static isEmpty(sortParams: SortParams): boolean {\r\n    return sortParams.sort.length === 0;\r\n  }\r\n\r\n  /**\r\n   * Get all fields referenced in sort parameters\r\n   */\r\n  static getReferencedFields(sortParams: SortParams): string[] {\r\n    return sortParams.sort.map(field => field.field);\r\n  }\r\n\r\n  /**\r\n   * Merge multiple sort parameters\r\n   */\r\n  static merge(...sortParams: SortParams[]): SortParams {\r\n    const allFields: SortField[] = [];\r\n    const seenFields = new Set<string>();\r\n\r\n    sortParams.forEach(params => {\r\n      params.sort.forEach(field => {\r\n        if (!seenFields.has(field.field)) {\r\n          allFields.push(field);\r\n          seenFields.add(field.field);\r\n        }\r\n      });\r\n    });\r\n\r\n    return {\r\n      sort: allFields,\r\n      allowMultipleFields: true,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create SQL ORDER BY clause from sort parameters\r\n   */\r\n  static toSqlOrderBy(sortParams: SortParams, tableAlias?: string): string {\r\n    if (sortParams.sort.length === 0) {\r\n      return '';\r\n    }\r\n\r\n    const orderClauses = sortParams.sort.map(field => {\r\n      const fieldName = tableAlias ? `${tableAlias}.${field.field}` : field.field;\r\n      return `${fieldName} ${field.direction.toUpperCase()}`;\r\n    });\r\n\r\n    return `ORDER BY ${orderClauses.join(', ')}`;\r\n  }\r\n\r\n  /**\r\n   * Create MongoDB sort object from sort parameters\r\n   */\r\n  static toMongoSort(sortParams: SortParams): Record<string, 1 | -1> {\r\n    const mongoSort: Record<string, 1 | -1> = {};\r\n\r\n    sortParams.sort.forEach(field => {\r\n      mongoSort[field.field] = field.direction === SortDirection.ASC ? 1 : -1;\r\n    });\r\n\r\n    return mongoSort;\r\n  }\r\n}"], "version": 3}