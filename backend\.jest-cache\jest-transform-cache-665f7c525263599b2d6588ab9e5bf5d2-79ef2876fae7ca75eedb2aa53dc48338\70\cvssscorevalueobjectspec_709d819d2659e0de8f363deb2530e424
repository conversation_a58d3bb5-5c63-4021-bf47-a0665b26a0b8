3a614e5ccdd634b896d70074e08fd0d6
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const cvss_score_value_object_1 = require("../cvss-score.value-object");
describe('CVSSScore Value Object', () => {
    describe('creation', () => {
        it('should create valid CVSS score with required properties', () => {
            // Arrange & Act
            const cvssScore = cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');
            // Assert
            expect(cvssScore.baseScore).toBe(7.5);
            expect(cvssScore.version).toBe(cvss_score_value_object_1.CVSSVersion.V3_1);
            expect(cvssScore.vectorString).toBe('CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');
            expect(cvssScore.calculatedAt).toBeInstanceOf(Date);
        });
        it('should create CVSS v3.1 score using factory method', () => {
            // Arrange & Act
            const cvssScore = cvss_score_value_object_1.CVSSScore.createV3_1(8.8, 'CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H');
            // Assert
            expect(cvssScore.baseScore).toBe(8.8);
            expect(cvssScore.version).toBe(cvss_score_value_object_1.CVSSVersion.V3_1);
        });
        it('should create CVSS v4.0 score using factory method', () => {
            // Arrange & Act
            const cvssScore = cvss_score_value_object_1.CVSSScore.createV4_0(9.1, 'CVSS:4.0/AV:N/AC:L/AT:N/PR:N/UI:N/VC:H/VI:H/VA:H/SC:N/SI:N/SA:N');
            // Assert
            expect(cvssScore.baseScore).toBe(9.1);
            expect(cvssScore.version).toBe(cvss_score_value_object_1.CVSSVersion.V4_0);
        });
        it('should create CVSS score with optional properties', () => {
            // Arrange & Act
            const cvssScore = cvss_score_value_object_1.CVSSScore.create(6.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:N/A:N', {
                temporalScore: 6.0,
                environmentalScore: 5.8,
                exploitabilityScore: 3.9,
                impactScore: 3.6,
                source: 'NVD',
            });
            // Assert
            expect(cvssScore.temporalScore).toBe(6.0);
            expect(cvssScore.environmentalScore).toBe(5.8);
            expect(cvssScore.exploitabilityScore).toBe(3.9);
            expect(cvssScore.impactScore).toBe(3.6);
            expect(cvssScore.source).toBe('NVD');
        });
    });
    describe('validation', () => {
        it('should validate base score range', () => {
            // Valid scores
            expect(() => cvss_score_value_object_1.CVSSScore.create(0.0, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:N'))
                .not.toThrow();
            expect(() => cvss_score_value_object_1.CVSSScore.create(10.0, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H'))
                .not.toThrow();
            // Invalid scores
            expect(() => cvss_score_value_object_1.CVSSScore.create(-0.1, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:N'))
                .toThrow('CVSS base score must be between 0 and 10');
            expect(() => cvss_score_value_object_1.CVSSScore.create(10.1, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H'))
                .toThrow('CVSS base score must be between 0 and 10');
        });
        it('should validate temporal score range', () => {
            // Valid temporal score
            expect(() => cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H', {
                temporalScore: 7.0,
            })).not.toThrow();
            // Invalid temporal score
            expect(() => cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H', {
                temporalScore: 10.5,
            })).toThrow('CVSS temporal score must be between 0 and 10');
        });
        it('should validate environmental score range', () => {
            // Valid environmental score
            expect(() => cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H', {
                environmentalScore: 6.8,
            })).not.toThrow();
            // Invalid environmental score
            expect(() => cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H', {
                environmentalScore: -1.0,
            })).toThrow('CVSS environmental score must be between 0 and 10');
        });
        it('should validate CVSS version', () => {
            // Valid versions
            expect(() => cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V2, '(AV:N/AC:M/Au:N/C:P/I:P/A:P)'))
                .not.toThrow();
            expect(() => cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_0, 'CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H'))
                .not.toThrow();
            // Invalid version
            expect(() => cvss_score_value_object_1.CVSSScore.create(7.5, 'invalid', 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H'))
                .toThrow('Invalid CVSS version: invalid');
        });
        it('should require vector string', () => {
            expect(() => cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_1, ''))
                .toThrow('CVSS vector string is required');
        });
        it('should validate CVSS v2 vector string format', () => {
            // Valid v2 vector
            expect(() => cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V2, '(AV:N/AC:M/Au:N/C:P/I:P/A:P)'))
                .not.toThrow();
            // Invalid v2 vector
            expect(() => cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V2, 'invalid-vector'))
                .toThrow('Invalid CVSS v2 vector string format');
        });
        it('should validate CVSS v3 vector string format', () => {
            // Valid v3.1 vector
            expect(() => cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H'))
                .not.toThrow();
            // Valid v3.0 vector
            expect(() => cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_0, 'CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H'))
                .not.toThrow();
            // Invalid v3 vector
            expect(() => cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'invalid-vector'))
                .toThrow('Invalid CVSS v3 vector string format');
        });
        it('should validate CVSS v4 vector string format', () => {
            // Valid v4.0 vector
            expect(() => cvss_score_value_object_1.CVSSScore.create(9.1, cvss_score_value_object_1.CVSSVersion.V4_0, 'CVSS:4.0/AV:N/AC:L/AT:N/PR:N/UI:N/VC:H/VI:H/VA:H/SC:N/SI:N/SA:N'))
                .not.toThrow();
            // Invalid v4 vector
            expect(() => cvss_score_value_object_1.CVSSScore.create(9.1, cvss_score_value_object_1.CVSSVersion.V4_0, 'invalid-vector'))
                .toThrow('Invalid CVSS v4 vector string format');
        });
        it('should validate subscore ranges', () => {
            // Valid subscores
            expect(() => cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H', {
                exploitabilityScore: 3.9,
                impactScore: 5.9,
            })).not.toThrow();
            // Invalid exploitability score
            expect(() => cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H', {
                exploitabilityScore: 11.0,
            })).toThrow('CVSS exploitability score must be between 0 and 10');
            // Invalid impact score
            expect(() => cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H', {
                impactScore: -1.0,
            })).toThrow('CVSS impact score must be between 0 and 10');
        });
    });
    describe('severity classification', () => {
        it('should classify severity correctly', () => {
            const noneScore = cvss_score_value_object_1.CVSSScore.create(0.0, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:N');
            const lowScore = cvss_score_value_object_1.CVSSScore.create(2.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:L/AC:H/PR:H/UI:R/S:U/C:L/I:N/A:N');
            const mediumScore = cvss_score_value_object_1.CVSSScore.create(5.0, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:N');
            const highScore = cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N');
            const criticalScore = cvss_score_value_object_1.CVSSScore.create(9.8, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');
            expect(noneScore.getSeverity()).toBe(cvss_score_value_object_1.CVSSSeverity.NONE);
            expect(lowScore.getSeverity()).toBe(cvss_score_value_object_1.CVSSSeverity.LOW);
            expect(mediumScore.getSeverity()).toBe(cvss_score_value_object_1.CVSSSeverity.MEDIUM);
            expect(highScore.getSeverity()).toBe(cvss_score_value_object_1.CVSSSeverity.HIGH);
            expect(criticalScore.getSeverity()).toBe(cvss_score_value_object_1.CVSSSeverity.CRITICAL);
        });
        it('should identify critical scores', () => {
            const criticalScore = cvss_score_value_object_1.CVSSScore.create(9.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');
            const highScore = cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N');
            expect(criticalScore.isCritical()).toBe(true);
            expect(highScore.isCritical()).toBe(false);
        });
        it('should identify high or critical scores', () => {
            const criticalScore = cvss_score_value_object_1.CVSSScore.create(9.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');
            const highScore = cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N');
            const mediumScore = cvss_score_value_object_1.CVSSScore.create(5.0, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:N');
            expect(criticalScore.isHighOrCritical()).toBe(true);
            expect(highScore.isHighOrCritical()).toBe(true);
            expect(mediumScore.isHighOrCritical()).toBe(false);
        });
        it('should identify scores requiring immediate attention', () => {
            const urgentScore = cvss_score_value_object_1.CVSSScore.create(8.0, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:N');
            const normalScore = cvss_score_value_object_1.CVSSScore.create(6.0, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N');
            expect(urgentScore.requiresImmediateAttention()).toBe(true);
            expect(normalScore.requiresImmediateAttention()).toBe(false);
        });
    });
    describe('effective score calculation', () => {
        it('should use environmental score when available', () => {
            const cvssScore = cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H', {
                temporalScore: 7.0,
                environmentalScore: 6.5,
            });
            expect(cvssScore.getEffectiveScore()).toBe(6.5);
        });
        it('should use temporal score when environmental is not available', () => {
            const cvssScore = cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H', {
                temporalScore: 7.0,
            });
            expect(cvssScore.getEffectiveScore()).toBe(7.0);
        });
        it('should use base score when others are not available', () => {
            const cvssScore = cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');
            expect(cvssScore.getEffectiveScore()).toBe(7.5);
        });
    });
    describe('risk assessment', () => {
        it('should provide risk level assessment', () => {
            const criticalScore = cvss_score_value_object_1.CVSSScore.create(9.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');
            const highScore = cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N');
            const mediumScore = cvss_score_value_object_1.CVSSScore.create(5.0, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:N');
            const lowScore = cvss_score_value_object_1.CVSSScore.create(2.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:L/AC:H/PR:H/UI:R/S:U/C:L/I:N/A:N');
            expect(criticalScore.getRiskLevel()).toBe('critical');
            expect(highScore.getRiskLevel()).toBe('high');
            expect(mediumScore.getRiskLevel()).toBe('medium');
            expect(lowScore.getRiskLevel()).toBe('low');
        });
        it('should provide priority score for remediation', () => {
            const highScore = cvss_score_value_object_1.CVSSScore.create(7.8, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N');
            const lowScore = cvss_score_value_object_1.CVSSScore.create(2.3, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:L/AC:H/PR:H/UI:R/S:U/C:L/I:N/A:N');
            expect(highScore.getPriorityScore()).toBe(8);
            expect(lowScore.getPriorityScore()).toBe(3);
        });
        it('should provide remediation timeline', () => {
            const criticalScore = cvss_score_value_object_1.CVSSScore.create(9.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');
            const highScore = cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N');
            const mediumScore = cvss_score_value_object_1.CVSSScore.create(5.0, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:N');
            const criticalTimeline = criticalScore.getRemediationTimeline();
            const highTimeline = highScore.getRemediationTimeline();
            const mediumTimeline = mediumScore.getRemediationTimeline();
            expect(criticalTimeline.immediate).toBe(true);
            expect(criticalTimeline.days).toBe(1);
            expect(highTimeline.immediate).toBe(false);
            expect(highTimeline.days).toBe(7);
            expect(mediumTimeline.days).toBe(30);
        });
        it('should provide compliance requirements', () => {
            const criticalScore = cvss_score_value_object_1.CVSSScore.create(9.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');
            const lowScore = cvss_score_value_object_1.CVSSScore.create(2.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:L/AC:H/PR:H/UI:R/S:U/C:L/I:N/A:N');
            const criticalCompliance = criticalScore.getComplianceRequirements();
            const lowCompliance = lowScore.getComplianceRequirements();
            expect(criticalCompliance.pciDss).toBe(true);
            expect(criticalCompliance.sox).toBe(true);
            expect(criticalCompliance.reporting).toBe(true);
            expect(lowCompliance.pciDss).toBe(false);
            expect(lowCompliance.sox).toBe(false);
            expect(lowCompliance.reporting).toBe(false);
        });
    });
    describe('immutability and updates', () => {
        it('should create new score when adding temporal score', () => {
            const originalScore = cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');
            const updatedScore = originalScore.withTemporalScore(7.0);
            expect(updatedScore).not.toBe(originalScore);
            expect(updatedScore.temporalScore).toBe(7.0);
            expect(originalScore.temporalScore).toBeUndefined();
        });
        it('should create new score when adding environmental score', () => {
            const originalScore = cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');
            const updatedScore = originalScore.withEnvironmentalScore(6.8);
            expect(updatedScore).not.toBe(originalScore);
            expect(updatedScore.environmentalScore).toBe(6.8);
            expect(originalScore.environmentalScore).toBeUndefined();
        });
        it('should create new score when adding subscores', () => {
            const originalScore = cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');
            const updatedScore = originalScore.withSubscores(3.9, 5.9);
            expect(updatedScore).not.toBe(originalScore);
            expect(updatedScore.exploitabilityScore).toBe(3.9);
            expect(updatedScore.impactScore).toBe(5.9);
            expect(originalScore.exploitabilityScore).toBeUndefined();
            expect(originalScore.impactScore).toBeUndefined();
        });
    });
    describe('equality and comparison', () => {
        it('should compare CVSS scores for equality', () => {
            const score1 = cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');
            const score2 = cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');
            const score3 = cvss_score_value_object_1.CVSSScore.create(8.0, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');
            const score4 = cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_0, 'CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');
            expect(score1.equals(score2)).toBe(true);
            expect(score1.equals(score3)).toBe(false); // Different base score
            expect(score1.equals(score4)).toBe(false); // Different version
            expect(score1.equals(undefined)).toBe(false);
        });
        it('should have consistent string representation', () => {
            const score = cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');
            expect(score.toString()).toBe('CVSS 3.1: 7.5 (high)');
        });
    });
    describe('serialization', () => {
        it('should serialize to JSON correctly', () => {
            const score = cvss_score_value_object_1.CVSSScore.create(7.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H', {
                temporalScore: 7.0,
                source: 'NVD',
            });
            const json = score.toJSON();
            expect(json.baseScore).toBe(7.5);
            expect(json.temporalScore).toBe(7.0);
            expect(json.version).toBe(cvss_score_value_object_1.CVSSVersion.V3_1);
            expect(json.vectorString).toBe('CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');
            expect(json.source).toBe('NVD');
            expect(json.summary).toBeDefined();
            expect(json.compliance).toBeDefined();
            expect(json.remediation).toBeDefined();
        });
        it('should deserialize from JSON correctly', () => {
            const originalScore = cvss_score_value_object_1.CVSSScore.create(8.8, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H');
            const json = originalScore.toJSON();
            const deserializedScore = cvss_score_value_object_1.CVSSScore.fromJSON(json);
            expect(deserializedScore.equals(originalScore)).toBe(true);
            expect(deserializedScore.baseScore).toBe(originalScore.baseScore);
            expect(deserializedScore.version).toBe(originalScore.version);
            expect(deserializedScore.vectorString).toBe(originalScore.vectorString);
        });
    });
    describe('validation utility', () => {
        it('should validate CVSS score format without creating instance', () => {
            expect(cvss_score_value_object_1.CVSSScore.isValid(7.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H')).toBe(true);
            expect(cvss_score_value_object_1.CVSSScore.isValid(10.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H')).toBe(false);
            expect(cvss_score_value_object_1.CVSSScore.isValid(7.5, cvss_score_value_object_1.CVSSVersion.V3_1, 'invalid-vector')).toBe(false);
        });
    });
    describe('score summary', () => {
        it('should provide comprehensive score summary', () => {
            const score = cvss_score_value_object_1.CVSSScore.create(8.8, cvss_score_value_object_1.CVSSVersion.V3_1, 'CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H', {
                temporalScore: 8.5,
                environmentalScore: 8.0,
            });
            const summary = score.getScoreSummary();
            expect(summary.baseScore).toBe(8.8);
            expect(summary.temporalScore).toBe(8.5);
            expect(summary.environmentalScore).toBe(8.0);
            expect(summary.effectiveScore).toBe(8.0);
            expect(summary.severity).toBe(cvss_score_value_object_1.CVSSSeverity.HIGH);
            expect(summary.riskLevel).toBe('high');
            expect(summary.requiresImmediateAttention).toBe(true);
            expect(typeof summary.priorityScore).toBe('number');
            expect(typeof summary.remediationDays).toBe('number');
        });
    });
    describe('vector string parsing', () => {
        it('should parse CVSS score from vector string', () => {
            const vectorString = 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H';
            const score = cvss_score_value_object_1.CVSSScore.fromVectorString(vectorString, 'test-source');
            expect(score.vectorString).toBe(vectorString);
            expect(score.version).toBe(cvss_score_value_object_1.CVSSVersion.V3_1);
            expect(score.source).toBe('test-source');
            expect(typeof score.baseScore).toBe('number');
        });
        it('should detect version from vector string', () => {
            // This test would need the actual implementation of version detection
            // For now, we'll test that it doesn't throw
            expect(() => cvss_score_value_object_1.CVSSScore.fromVectorString('CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H'))
                .not.toThrow();
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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