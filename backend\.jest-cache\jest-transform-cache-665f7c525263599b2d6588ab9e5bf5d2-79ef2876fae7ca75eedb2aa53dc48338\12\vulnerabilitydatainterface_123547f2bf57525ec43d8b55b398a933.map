{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\data\\vulnerability-data.interface.ts", "mappings": ";;;AAiIA;;GAEG;AACH,IAAY,8BAaX;AAbD,WAAY,8BAA8B;IACxC,iCAAiC;IACjC,yCAAO,CAAA;IACP,8BAA8B;IAC9B,yCAAO,CAAA;IACP,6BAA6B;IAC7B,yCAAO,CAAA;IACP,kCAAkC;IAClC,yCAAO,CAAA;IACP,wBAAwB;IACxB,yCAAO,CAAA;IACP,uBAAuB;IACvB,yCAAO,CAAA;AACT,CAAC,EAbW,8BAA8B,8CAA9B,8BAA8B,QAazC;AAED;;GAEG;AACH,IAAY,qBAgCX;AAhCD,WAAY,qBAAqB;IAC/B,0DAAiC,CAAA;IACjC,wDAA+B,CAAA;IAC/B,4DAAmC,CAAA;IACnC,0DAAiC,CAAA;IACjC,gEAAuC,CAAA;IACvC,sEAA6C,CAAA;IAC7C,kFAAyD,CAAA;IACzD,wDAA+B,CAAA;IAC/B,wDAA+B,CAAA;IAC/B,gEAAuC,CAAA;IACvC,4DAAmC,CAAA;IACnC,oEAA2C,CAAA;IAC3C,0DAAiC,CAAA;IACjC,0EAAiD,CAAA;IACjD,8DAAqC,CAAA;IACrC,0EAAiD,CAAA;IACjD,8DAAqC,CAAA;IACrC,0DAAiC,CAAA;IACjC,gEAAuC,CAAA;IACvC,0EAAiD,CAAA;IACjD,wEAA+C,CAAA;IAC/C,kEAAyC,CAAA;IACzC,0DAAiC,CAAA;IACjC,sEAA6C,CAAA;IAC7C,0DAAiC,CAAA;IACjC,wEAA+C,CAAA;IAC/C,kEAAyC,CAAA;IACzC,wDAA+B,CAAA;IAC/B,gEAAuC,CAAA;IACvC,wDAA+B,CAAA;IAC/B,wCAAe,CAAA;AACjB,CAAC,EAhCW,qBAAqB,qCAArB,qBAAqB,QAgChC;AAED;;GAEG;AACH,IAAY,iBAWX;AAXD,WAAY,iBAAiB;IAC3B,gDAA2B,CAAA;IAC3B,8DAAyC,CAAA;IACzC,gEAA2C,CAAA;IAC3C,oDAA+B,CAAA;IAC/B,0DAAqC,CAAA;IACrC,0DAAqC,CAAA;IACrC,gDAA2B,CAAA;IAC3B,sDAAiC,CAAA;IACjC,4EAAuD,CAAA;IACvD,oCAAe,CAAA;AACjB,CAAC,EAXW,iBAAiB,iCAAjB,iBAAiB,QAW5B;AAED;;GAEG;AACH,IAAY,mBAUX;AAVD,WAAY,mBAAmB;IAC7B,8CAAuB,CAAA;IACvB,4CAAqB,CAAA;IACrB,4CAAqB,CAAA;IACrB,8DAAuC,CAAA;IACvC,kEAA2C,CAAA;IAC3C,4CAAqB,CAAA;IACrB,4CAAqB,CAAA;IACrB,4CAAqB,CAAA;IACrB,8CAAuB,CAAA;AACzB,CAAC,EAVW,mBAAmB,mCAAnB,mBAAmB,QAU9B;AA6ID;;GAEG;AACH,IAAY,0BAYX;AAZD,WAAY,0BAA0B;IACpC,mDAAqB,CAAA;IACrB,iDAAmB,CAAA;IACnB,iDAAmB,CAAA;IACnB,6CAAe,CAAA;IACf,2DAA6B,CAAA;IAC7B,6CAAe,CAAA;IACf,+CAAiB,CAAA;IACjB,2CAAa,CAAA;IACb,+CAAiB,CAAA;IACjB,yCAAW,CAAA;IACX,6CAAe,CAAA;AACjB,CAAC,EAZW,0BAA0B,0CAA1B,0BAA0B,QAYrC;AA+BD;;GAEG;AACH,IAAY,+BAQX;AARD,WAAY,+BAA+B;IACzC,sEAAmC,CAAA;IACnC,8DAA2B,CAAA;IAC3B,wEAAqC,CAAA;IACrC,4DAAyB,CAAA;IACzB,gDAAa,CAAA;IACb,oDAAiB,CAAA;IACjB,sDAAmB,CAAA;AACrB,CAAC,EARW,+BAA+B,+CAA/B,+BAA+B,QAQ1C;AAED;;GAEG;AACH,IAAY,mCAKX;AALD,WAAY,mCAAmC;IAC7C,kDAAW,CAAA;IACX,wDAAiB,CAAA;IACjB,oDAAa,CAAA;IACb,0DAAmB,CAAA;AACrB,CAAC,EALW,mCAAmC,mDAAnC,mCAAmC,QAK9C;AAyBD;;GAEG;AACH,IAAY,4BAMX;AAND,WAAY,4BAA4B;IACtC,qDAAqB,CAAA;IACrB,qEAAqC,CAAA;IACrC,yDAAyB,CAAA;IACzB,6CAAa,CAAA;IACb,2DAA2B,CAAA;AAC7B,CAAC,EANW,4BAA4B,4CAA5B,4BAA4B,QAMvC;AA8DD;;GAEG;AACH,IAAY,8BAOX;AAPD,WAAY,8BAA8B;IACxC,yDAAuB,CAAA;IACvB,qDAAmB,CAAA;IACnB,qDAAmB,CAAA;IACnB,6DAA2B,CAAA;IAC3B,+DAA6B,CAAA;IAC7B,qDAAmB,CAAA;AACrB,CAAC,EAPW,8BAA8B,8CAA9B,8BAA8B,QAOzC;AAyBD;;GAEG;AACH,IAAY,oBAOX;AAPD,WAAY,oBAAoB;IAC9B,uCAAe,CAAA;IACf,yCAAiB,CAAA;IACjB,2CAAmB,CAAA;IACnB,uDAA+B,CAAA;IAC/B,mDAA2B,CAAA;IAC3B,uCAAe,CAAA;AACjB,CAAC,EAPW,oBAAoB,oCAApB,oBAAoB,QAO/B;AAuJD;;GAEG;AACH,IAAY,qBAMX;AAND,WAAY,qBAAqB;IAC/B,8CAAqB,CAAA;IACrB,sCAAa,CAAA;IACb,0CAAiB,CAAA;IACjB,oCAAW,CAAA;IACX,wDAA+B,CAAA;AACjC,CAAC,EANW,qBAAqB,qCAArB,qBAAqB,QAMhC;AA4GD;;GAEG;AACH,IAAY,4BAUX;AAVD,WAAY,4BAA4B;IACtC,mEAAmC,CAAA;IACnC,qEAAqC,CAAA;IACrC,6EAA6C,CAAA;IAC7C,+DAA+B,CAAA;IAC/B,2EAA2C,CAAA;IAC3C,yEAAyC,CAAA;IACzC,mEAAmC,CAAA;IACnC,2EAA2C,CAAA;IAC3C,+CAAe,CAAA;AACjB,CAAC,EAVW,4BAA4B,4CAA5B,4BAA4B,QAUvC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\data\\vulnerability-data.interface.ts"], "sourcesContent": ["import { VulnerabilitySeverity } from '../../enums/vulnerability-severity.enum';\r\nimport { ConfidenceLevel } from '../../enums/confidence-level.enum';\r\n\r\n/**\r\n * Vulnerability Data Interface for External Integration\r\n * \r\n * This interface defines the structure for vulnerability data received from\r\n * external vulnerability databases, security scanners, threat intelligence feeds,\r\n * and vulnerability management platforms. It supports standardized vulnerability\r\n * data exchange, serialization, and versioning for vulnerability management integration.\r\n */\r\nexport interface VulnerabilityData {\r\n  /** Data format version for backward compatibility */\r\n  readonly version: string;\r\n  \r\n  /** Unique identifier for the vulnerability from external source */\r\n  readonly externalId: string;\r\n  \r\n  /** CVE identifier if available */\r\n  readonly cveId?: string;\r\n  \r\n  /** Other vulnerability identifiers (e.g., vendor-specific) */\r\n  readonly alternativeIds?: VulnerabilityAlternativeId[];\r\n  \r\n  /** Vulnerability source identifier */\r\n  readonly sourceId: string;\r\n  \r\n  /** Source organization or vendor */\r\n  readonly sourceOrganization: string;\r\n  \r\n  /** Source reliability rating */\r\n  readonly sourceReliability: VulnerabilitySourceReliability;\r\n  \r\n  /** Vulnerability title */\r\n  readonly title: string;\r\n  \r\n  /** Detailed vulnerability description */\r\n  readonly description: string;\r\n  \r\n  /** Vulnerability severity level */\r\n  readonly severity: VulnerabilitySeverity;\r\n  \r\n  /** Vulnerability category */\r\n  readonly category: VulnerabilityCategory;\r\n  \r\n  /** Vulnerability type classification */\r\n  readonly type: VulnerabilityType;\r\n  \r\n  /** Confidence level in vulnerability assessment */\r\n  readonly confidence: ConfidenceLevel;\r\n  \r\n  /** Vulnerability status */\r\n  readonly status: VulnerabilityStatus;\r\n  \r\n  /** Publication date */\r\n  readonly publishedAt: string;\r\n  \r\n  /** Last modified date */\r\n  readonly modifiedAt: string;\r\n  \r\n  /** Discovery date */\r\n  readonly discoveredAt?: string;\r\n  \r\n  /** Disclosure date */\r\n  readonly disclosedAt?: string;\r\n  \r\n  /** CVSS scores */\r\n  readonly cvssScores: VulnerabilityCVSSData[];\r\n  \r\n  /** Affected products and versions */\r\n  readonly affectedProducts: VulnerabilityAffectedProduct[];\r\n  \r\n  /** Vulnerability references */\r\n  readonly references: VulnerabilityReference[];\r\n  \r\n  /** Exploitation information */\r\n  readonly exploitation?: VulnerabilityExploitationData;\r\n  \r\n  /** Patch and remediation information */\r\n  readonly remediation?: VulnerabilityRemediationData;\r\n  \r\n  /** Vulnerability assessment details */\r\n  readonly assessment: VulnerabilityAssessmentData;\r\n  \r\n  /** Compliance and regulatory impact */\r\n  readonly complianceImpact?: VulnerabilityComplianceImpact;\r\n  \r\n  /** Business impact assessment */\r\n  readonly businessImpact?: VulnerabilityBusinessImpact;\r\n  \r\n  /** Technical details */\r\n  readonly technicalDetails?: VulnerabilityTechnicalDetails;\r\n  \r\n  /** Detection and scanning information */\r\n  readonly detectionInfo?: VulnerabilityDetectionInfo;\r\n  \r\n  /** Threat intelligence context */\r\n  readonly threatContext?: VulnerabilityThreatContext;\r\n  \r\n  /** Asset context if applicable */\r\n  readonly assetContext?: VulnerabilityAssetContext[];\r\n  \r\n  /** Vulnerability sharing information */\r\n  readonly sharingInfo: VulnerabilitySharingInfo;\r\n  \r\n  /** Custom attributes from source */\r\n  readonly attributes?: Record<string, any>;\r\n  \r\n  /** Vulnerability tags for categorization */\r\n  readonly tags?: string[];\r\n  \r\n  /** Data quality metrics */\r\n  readonly qualityMetrics?: VulnerabilityDataQualityMetrics;\r\n}\r\n\r\n/**\r\n * Vulnerability Alternative Identifiers\r\n */\r\nexport interface VulnerabilityAlternativeId {\r\n  /** Identifier type */\r\n  readonly type: string;\r\n  \r\n  /** Identifier value */\r\n  readonly value: string;\r\n  \r\n  /** Identifier source */\r\n  readonly source: string;\r\n}\r\n\r\n/**\r\n * Vulnerability Source Reliability Levels\r\n */\r\nexport enum VulnerabilitySourceReliability {\r\n  /** Completely reliable source */\r\n  A = 'A',\r\n  /** Usually reliable source */\r\n  B = 'B',\r\n  /** Fairly reliable source */\r\n  C = 'C',\r\n  /** Not usually reliable source */\r\n  D = 'D',\r\n  /** Unreliable source */\r\n  E = 'E',\r\n  /** Cannot be judged */\r\n  F = 'F',\r\n}\r\n\r\n/**\r\n * Vulnerability Categories\r\n */\r\nexport enum VulnerabilityCategory {\r\n  AUTHENTICATION = 'authentication',\r\n  AUTHORIZATION = 'authorization',\r\n  BUFFER_OVERFLOW = 'buffer_overflow',\r\n  CODE_INJECTION = 'code_injection',\r\n  COMMAND_INJECTION = 'command_injection',\r\n  CROSS_SITE_SCRIPTING = 'cross_site_scripting',\r\n  CROSS_SITE_REQUEST_FORGERY = 'cross_site_request_forgery',\r\n  CRYPTOGRAPHIC = 'cryptographic',\r\n  DATA_EXPOSURE = 'data_exposure',\r\n  DENIAL_OF_SERVICE = 'denial_of_service',\r\n  DESERIALIZATION = 'deserialization',\r\n  DIRECTORY_TRAVERSAL = 'directory_traversal',\r\n  FILE_INCLUSION = 'file_inclusion',\r\n  INFORMATION_DISCLOSURE = 'information_disclosure',\r\n  INPUT_VALIDATION = 'input_validation',\r\n  INSECURE_CONFIGURATION = 'insecure_configuration',\r\n  INSECURE_STORAGE = 'insecure_storage',\r\n  LDAP_INJECTION = 'ldap_injection',\r\n  MEMORY_CORRUPTION = 'memory_corruption',\r\n  MISSING_AUTHENTICATION = 'missing_authentication',\r\n  MISSING_AUTHORIZATION = 'missing_authorization',\r\n  MISSING_ENCRYPTION = 'missing_encryption',\r\n  PATH_TRAVERSAL = 'path_traversal',\r\n  PRIVILEGE_ESCALATION = 'privilege_escalation',\r\n  RACE_CONDITION = 'race_condition',\r\n  REMOTE_CODE_EXECUTION = 'remote_code_execution',\r\n  SESSION_MANAGEMENT = 'session_management',\r\n  SQL_INJECTION = 'sql_injection',\r\n  WEAK_CRYPTOGRAPHY = 'weak_cryptography',\r\n  XML_INJECTION = 'xml_injection',\r\n  OTHER = 'other',\r\n}\r\n\r\n/**\r\n * Vulnerability Types\r\n */\r\nexport enum VulnerabilityType {\r\n  DESIGN_FLAW = 'design_flaw',\r\n  IMPLEMENTATION_BUG = 'implementation_bug',\r\n  CONFIGURATION_ERROR = 'configuration_error',\r\n  MISSING_PATCH = 'missing_patch',\r\n  WEAK_CREDENTIALS = 'weak_credentials',\r\n  INSECURE_DEFAULT = 'insecure_default',\r\n  LOGIC_ERROR = 'logic_error',\r\n  RACE_CONDITION = 'race_condition',\r\n  TIME_OF_CHECK_TIME_OF_USE = 'time_of_check_time_of_use',\r\n  OTHER = 'other',\r\n}\r\n\r\n/**\r\n * Vulnerability Status\r\n */\r\nexport enum VulnerabilityStatus {\r\n  PUBLISHED = 'published',\r\n  MODIFIED = 'modified',\r\n  ANALYZED = 'analyzed',\r\n  AWAITING_ANALYSIS = 'awaiting_analysis',\r\n  UNDERGOING_ANALYSIS = 'undergoing_analysis',\r\n  REJECTED = 'rejected',\r\n  DISPUTED = 'disputed',\r\n  RESERVED = 'reserved',\r\n  WITHDRAWN = 'withdrawn',\r\n}\r\n\r\n/**\r\n * Vulnerability CVSS Data\r\n */\r\nexport interface VulnerabilityCVSSData {\r\n  /** CVSS version */\r\n  readonly version: string;\r\n  \r\n  /** Base score */\r\n  readonly baseScore: number;\r\n  \r\n  /** Temporal score */\r\n  readonly temporalScore?: number;\r\n  \r\n  /** Environmental score */\r\n  readonly environmentalScore?: number;\r\n  \r\n  /** Base vector string */\r\n  readonly baseVector: string;\r\n  \r\n  /** Temporal vector string */\r\n  readonly temporalVector?: string;\r\n  \r\n  /** Environmental vector string */\r\n  readonly environmentalVector?: string;\r\n  \r\n  /** Base severity */\r\n  readonly baseSeverity: string;\r\n  \r\n  /** Temporal severity */\r\n  readonly temporalSeverity?: string;\r\n  \r\n  /** Environmental severity */\r\n  readonly environmentalSeverity?: string;\r\n  \r\n  /** Exploitability score */\r\n  readonly exploitabilityScore?: number;\r\n  \r\n  /** Impact score */\r\n  readonly impactScore?: number;\r\n  \r\n  /** CVSS source */\r\n  readonly source: string;\r\n  \r\n  /** Assessment date */\r\n  readonly assessedAt: string;\r\n}\r\n\r\n/**\r\n * Vulnerability Affected Product\r\n */\r\nexport interface VulnerabilityAffectedProduct {\r\n  /** Vendor name */\r\n  readonly vendor: string;\r\n  \r\n  /** Product name */\r\n  readonly product: string;\r\n  \r\n  /** Product version */\r\n  readonly version?: string;\r\n  \r\n  /** Version range */\r\n  readonly versionRange?: VulnerabilityVersionRange;\r\n  \r\n  /** Platform information */\r\n  readonly platform?: string;\r\n  \r\n  /** Product configuration */\r\n  readonly configuration?: string;\r\n  \r\n  /** CPE (Common Platform Enumeration) identifier */\r\n  readonly cpe?: string;\r\n  \r\n  /** SWID (Software Identification) tag */\r\n  readonly swid?: string;\r\n  \r\n  /** Package information */\r\n  readonly packageInfo?: VulnerabilityPackageInfo;\r\n}\r\n\r\n/**\r\n * Vulnerability Version Range\r\n */\r\nexport interface VulnerabilityVersionRange {\r\n  /** Starting version (inclusive) */\r\n  readonly startVersion?: string;\r\n  \r\n  /** Ending version (exclusive) */\r\n  readonly endVersion?: string;\r\n  \r\n  /** Starting version inclusion */\r\n  readonly startInclusive?: boolean;\r\n  \r\n  /** Ending version inclusion */\r\n  readonly endInclusive?: boolean;\r\n  \r\n  /** Version criteria */\r\n  readonly criteria?: string;\r\n}\r\n\r\n/**\r\n * Vulnerability Package Information\r\n */\r\nexport interface VulnerabilityPackageInfo {\r\n  /** Package manager type */\r\n  readonly type: string;\r\n  \r\n  /** Package name */\r\n  readonly name: string;\r\n  \r\n  /** Package version */\r\n  readonly version?: string;\r\n  \r\n  /** Package ecosystem */\r\n  readonly ecosystem?: string;\r\n  \r\n  /** Package URL (PURL) */\r\n  readonly purl?: string;\r\n}\r\n\r\n/**\r\n * Vulnerability Reference\r\n */\r\nexport interface VulnerabilityReference {\r\n  /** Reference URL */\r\n  readonly url: string;\r\n  \r\n  /** Reference name or title */\r\n  readonly name?: string;\r\n  \r\n  /** Reference source */\r\n  readonly source?: string;\r\n  \r\n  /** Reference type */\r\n  readonly type: VulnerabilityReferenceType;\r\n  \r\n  /** Reference tags */\r\n  readonly tags?: string[];\r\n}\r\n\r\n/**\r\n * Vulnerability Reference Types\r\n */\r\nexport enum VulnerabilityReferenceType {\r\n  ADVISORY = 'advisory',\r\n  ARTICLE = 'article',\r\n  EXPLOIT = 'exploit',\r\n  ISSUE = 'issue',\r\n  MAILING_LIST = 'mailing_list',\r\n  PATCH = 'patch',\r\n  REPORT = 'report',\r\n  TOOL = 'tool',\r\n  VENDOR = 'vendor',\r\n  WEB = 'web',\r\n  OTHER = 'other',\r\n}\r\n\r\n/**\r\n * Vulnerability Exploitation Data\r\n */\r\nexport interface VulnerabilityExploitationData {\r\n  /** Exploitation status */\r\n  readonly status: VulnerabilityExploitationStatus;\r\n  \r\n  /** Exploitation complexity */\r\n  readonly complexity: VulnerabilityExploitationComplexity;\r\n  \r\n  /** Known exploits */\r\n  readonly knownExploits?: VulnerabilityKnownExploit[];\r\n  \r\n  /** Exploitation timeline */\r\n  readonly timeline?: VulnerabilityExploitationTimeline;\r\n  \r\n  /** Exploitation context */\r\n  readonly context?: VulnerabilityExploitationContext;\r\n  \r\n  /** Proof of concept availability */\r\n  readonly pocAvailable?: boolean;\r\n  \r\n  /** Weaponization status */\r\n  readonly weaponized?: boolean;\r\n  \r\n  /** In-the-wild exploitation */\r\n  readonly inTheWild?: boolean;\r\n}\r\n\r\n/**\r\n * Vulnerability Exploitation Status\r\n */\r\nexport enum VulnerabilityExploitationStatus {\r\n  NOT_EXPLOITABLE = 'not_exploitable',\r\n  THEORETICAL = 'theoretical',\r\n  PROOF_OF_CONCEPT = 'proof_of_concept',\r\n  FUNCTIONAL = 'functional',\r\n  HIGH = 'high',\r\n  ACTIVE = 'active',\r\n  UNKNOWN = 'unknown',\r\n}\r\n\r\n/**\r\n * Vulnerability Exploitation Complexity\r\n */\r\nexport enum VulnerabilityExploitationComplexity {\r\n  LOW = 'low',\r\n  MEDIUM = 'medium',\r\n  HIGH = 'high',\r\n  UNKNOWN = 'unknown',\r\n}\r\n\r\n/**\r\n * Vulnerability Known Exploit\r\n */\r\nexport interface VulnerabilityKnownExploit {\r\n  /** Exploit name */\r\n  readonly name: string;\r\n  \r\n  /** Exploit type */\r\n  readonly type: string;\r\n  \r\n  /** Exploit source */\r\n  readonly source?: string;\r\n  \r\n  /** Exploit URL */\r\n  readonly url?: string;\r\n  \r\n  /** Exploit date */\r\n  readonly date?: string;\r\n  \r\n  /** Exploit maturity */\r\n  readonly maturity: VulnerabilityExploitMaturity;\r\n}\r\n\r\n/**\r\n * Vulnerability Exploit Maturity\r\n */\r\nexport enum VulnerabilityExploitMaturity {\r\n  UNPROVEN = 'unproven',\r\n  PROOF_OF_CONCEPT = 'proof_of_concept',\r\n  FUNCTIONAL = 'functional',\r\n  HIGH = 'high',\r\n  NOT_DEFINED = 'not_defined',\r\n}\r\n\r\n/**\r\n * Vulnerability Exploitation Timeline\r\n */\r\nexport interface VulnerabilityExploitationTimeline {\r\n  /** First exploit observed */\r\n  readonly firstExploitSeen?: string;\r\n  \r\n  /** Last exploit observed */\r\n  readonly lastExploitSeen?: string;\r\n  \r\n  /** Exploitation trend */\r\n  readonly trend?: 'increasing' | 'decreasing' | 'stable';\r\n  \r\n  /** Exploitation frequency */\r\n  readonly frequency?: 'rare' | 'occasional' | 'frequent' | 'constant';\r\n}\r\n\r\n/**\r\n * Vulnerability Exploitation Context\r\n */\r\nexport interface VulnerabilityExploitationContext {\r\n  /** Attack vector */\r\n  readonly attackVector: 'network' | 'adjacent' | 'local' | 'physical';\r\n  \r\n  /** Attack complexity */\r\n  readonly attackComplexity: 'low' | 'high';\r\n  \r\n  /** Privileges required */\r\n  readonly privilegesRequired: 'none' | 'low' | 'high';\r\n  \r\n  /** User interaction */\r\n  readonly userInteraction: 'none' | 'required';\r\n  \r\n  /** Scope */\r\n  readonly scope: 'unchanged' | 'changed';\r\n}\r\n\r\n/**\r\n * Vulnerability Remediation Data\r\n */\r\nexport interface VulnerabilityRemediationData {\r\n  /** Remediation status */\r\n  readonly status: VulnerabilityRemediationStatus;\r\n  \r\n  /** Available fixes */\r\n  readonly fixes?: VulnerabilityFix[];\r\n  \r\n  /** Workarounds */\r\n  readonly workarounds?: VulnerabilityWorkaround[];\r\n  \r\n  /** Mitigation strategies */\r\n  readonly mitigations?: VulnerabilityMitigation[];\r\n  \r\n  /** Remediation timeline */\r\n  readonly timeline?: VulnerabilityRemediationTimeline;\r\n  \r\n  /** Remediation effort */\r\n  readonly effort?: VulnerabilityRemediationEffort;\r\n}\r\n\r\n/**\r\n * Vulnerability Remediation Status\r\n */\r\nexport enum VulnerabilityRemediationStatus {\r\n  AVAILABLE = 'available',\r\n  PARTIAL = 'partial',\r\n  PLANNED = 'planned',\r\n  UNAVAILABLE = 'unavailable',\r\n  WILL_NOT_FIX = 'will_not_fix',\r\n  UNKNOWN = 'unknown',\r\n}\r\n\r\n/**\r\n * Vulnerability Fix\r\n */\r\nexport interface VulnerabilityFix {\r\n  /** Fix type */\r\n  readonly type: VulnerabilityFixType;\r\n  \r\n  /** Fix description */\r\n  readonly description: string;\r\n  \r\n  /** Fix version */\r\n  readonly version?: string;\r\n  \r\n  /** Fix URL */\r\n  readonly url?: string;\r\n  \r\n  /** Fix date */\r\n  readonly date?: string;\r\n  \r\n  /** Fix effectiveness */\r\n  readonly effectiveness: 'complete' | 'partial' | 'unknown';\r\n}\r\n\r\n/**\r\n * Vulnerability Fix Types\r\n */\r\nexport enum VulnerabilityFixType {\r\n  PATCH = 'patch',\r\n  UPDATE = 'update',\r\n  UPGRADE = 'upgrade',\r\n  CONFIGURATION = 'configuration',\r\n  REPLACEMENT = 'replacement',\r\n  OTHER = 'other',\r\n}\r\n\r\n/**\r\n * Vulnerability Workaround\r\n */\r\nexport interface VulnerabilityWorkaround {\r\n  /** Workaround description */\r\n  readonly description: string;\r\n  \r\n  /** Implementation steps */\r\n  readonly steps?: string[];\r\n  \r\n  /** Effectiveness */\r\n  readonly effectiveness: 'high' | 'medium' | 'low';\r\n  \r\n  /** Impact on functionality */\r\n  readonly functionalImpact?: 'none' | 'low' | 'medium' | 'high';\r\n  \r\n  /** Implementation complexity */\r\n  readonly complexity: 'low' | 'medium' | 'high';\r\n}\r\n\r\n/**\r\n * Vulnerability Mitigation\r\n */\r\nexport interface VulnerabilityMitigation {\r\n  /** Mitigation type */\r\n  readonly type: 'preventive' | 'detective' | 'corrective';\r\n  \r\n  /** Mitigation description */\r\n  readonly description: string;\r\n  \r\n  /** Implementation guidance */\r\n  readonly guidance?: string;\r\n  \r\n  /** Effectiveness */\r\n  readonly effectiveness: 'high' | 'medium' | 'low';\r\n  \r\n  /** Required controls */\r\n  readonly controls?: string[];\r\n}\r\n\r\n/**\r\n * Vulnerability Remediation Timeline\r\n */\r\nexport interface VulnerabilityRemediationTimeline {\r\n  /** Vendor acknowledgment date */\r\n  readonly vendorAcknowledged?: string;\r\n  \r\n  /** Fix development started */\r\n  readonly fixDevelopmentStarted?: string;\r\n  \r\n  /** Fix available date */\r\n  readonly fixAvailable?: string;\r\n  \r\n  /** Fix deployed date */\r\n  readonly fixDeployed?: string;\r\n  \r\n  /** Expected fix date */\r\n  readonly expectedFixDate?: string;\r\n}\r\n\r\n/**\r\n * Vulnerability Remediation Effort\r\n */\r\nexport interface VulnerabilityRemediationEffort {\r\n  /** Time to fix in hours */\r\n  readonly timeToFix?: number;\r\n  \r\n  /** Implementation complexity */\r\n  readonly complexity: 'low' | 'medium' | 'high';\r\n  \r\n  /** Required resources */\r\n  readonly resources?: string[];\r\n  \r\n  /** Dependencies */\r\n  readonly dependencies?: string[];\r\n  \r\n  /** Risk of remediation */\r\n  readonly remediationRisk: 'low' | 'medium' | 'high';\r\n}\r\n\r\n/**\r\n * Vulnerability Assessment Data\r\n */\r\nexport interface VulnerabilityAssessmentData {\r\n  /** Risk score (0-100) */\r\n  readonly riskScore: number;\r\n  \r\n  /** Impact assessment */\r\n  readonly impact: VulnerabilityImpactAssessment;\r\n  \r\n  /** Likelihood assessment */\r\n  readonly likelihood: VulnerabilityLikelihoodAssessment;\r\n  \r\n  /** Priority rating */\r\n  readonly priority: VulnerabilityPriority;\r\n  \r\n  /** Assessment date */\r\n  readonly assessedAt: string;\r\n  \r\n  /** Assessment source */\r\n  readonly assessedBy: string;\r\n  \r\n  /** Assessment methodology */\r\n  readonly methodology?: string;\r\n  \r\n  /** Assessment notes */\r\n  readonly notes?: string;\r\n}\r\n\r\n/**\r\n * Vulnerability Impact Assessment\r\n */\r\nexport interface VulnerabilityImpactAssessment {\r\n  /** Confidentiality impact */\r\n  readonly confidentiality: 'none' | 'partial' | 'complete';\r\n  \r\n  /** Integrity impact */\r\n  readonly integrity: 'none' | 'partial' | 'complete';\r\n  \r\n  /** Availability impact */\r\n  readonly availability: 'none' | 'partial' | 'complete';\r\n  \r\n  /** Business impact */\r\n  readonly business: 'low' | 'medium' | 'high' | 'critical';\r\n  \r\n  /** Financial impact */\r\n  readonly financial?: 'low' | 'medium' | 'high' | 'critical';\r\n  \r\n  /** Reputational impact */\r\n  readonly reputational?: 'low' | 'medium' | 'high' | 'critical';\r\n}\r\n\r\n/**\r\n * Vulnerability Likelihood Assessment\r\n */\r\nexport interface VulnerabilityLikelihoodAssessment {\r\n  /** Exploitation likelihood */\r\n  readonly exploitation: 'low' | 'medium' | 'high';\r\n  \r\n  /** Attack vector accessibility */\r\n  readonly vectorAccessibility: 'low' | 'medium' | 'high';\r\n  \r\n  /** Attacker motivation */\r\n  readonly attackerMotivation: 'low' | 'medium' | 'high';\r\n  \r\n  /** Target attractiveness */\r\n  readonly targetAttractiveness: 'low' | 'medium' | 'high';\r\n}\r\n\r\n/**\r\n * Vulnerability Priority\r\n */\r\nexport enum VulnerabilityPriority {\r\n  CRITICAL = 'critical',\r\n  HIGH = 'high',\r\n  MEDIUM = 'medium',\r\n  LOW = 'low',\r\n  INFORMATIONAL = 'informational',\r\n}\r\n\r\n/**\r\n * Vulnerability Compliance Impact\r\n */\r\nexport interface VulnerabilityComplianceImpact {\r\n  /** Affected compliance frameworks */\r\n  readonly frameworks: string[];\r\n  \r\n  /** Compliance requirements */\r\n  readonly requirements: string[];\r\n  \r\n  /** Violation severity */\r\n  readonly violationSeverity: 'low' | 'medium' | 'high' | 'critical';\r\n  \r\n  /** Regulatory notifications required */\r\n  readonly notificationsRequired: boolean;\r\n  \r\n  /** Compliance deadline */\r\n  readonly deadline?: string;\r\n  \r\n  /** Penalties */\r\n  readonly penalties?: string[];\r\n}\r\n\r\n/**\r\n * Vulnerability Business Impact\r\n */\r\nexport interface VulnerabilityBusinessImpact {\r\n  /** Affected business processes */\r\n  readonly affectedProcesses: string[];\r\n  \r\n  /** Service disruption potential */\r\n  readonly serviceDisruption: 'none' | 'minimal' | 'moderate' | 'severe';\r\n  \r\n  /** Revenue impact */\r\n  readonly revenueImpact: 'none' | 'low' | 'medium' | 'high';\r\n  \r\n  /** Customer impact */\r\n  readonly customerImpact: 'none' | 'low' | 'medium' | 'high';\r\n  \r\n  /** Operational impact */\r\n  readonly operationalImpact: 'none' | 'low' | 'medium' | 'high';\r\n  \r\n  /** Strategic impact */\r\n  readonly strategicImpact: 'none' | 'low' | 'medium' | 'high';\r\n}\r\n\r\n/**\r\n * Vulnerability Technical Details\r\n */\r\nexport interface VulnerabilityTechnicalDetails {\r\n  /** Root cause analysis */\r\n  readonly rootCause?: string;\r\n  \r\n  /** Attack vectors */\r\n  readonly attackVectors: string[];\r\n  \r\n  /** Prerequisites */\r\n  readonly prerequisites?: string[];\r\n  \r\n  /** Technical impact */\r\n  readonly technicalImpact: string[];\r\n  \r\n  /** Affected components */\r\n  readonly affectedComponents: string[];\r\n  \r\n  /** Code examples */\r\n  readonly codeExamples?: VulnerabilityCodeExample[];\r\n}\r\n\r\n/**\r\n * Vulnerability Code Example\r\n */\r\nexport interface VulnerabilityCodeExample {\r\n  /** Programming language */\r\n  readonly language: string;\r\n  \r\n  /** Code snippet */\r\n  readonly code: string;\r\n  \r\n  /** Example type */\r\n  readonly type: 'vulnerable' | 'exploit' | 'fix';\r\n  \r\n  /** Description */\r\n  readonly description?: string;\r\n}\r\n\r\n/**\r\n * Vulnerability Detection Information\r\n */\r\nexport interface VulnerabilityDetectionInfo {\r\n  /** Detection methods */\r\n  readonly methods: VulnerabilityDetectionMethod[];\r\n  \r\n  /** Scanner information */\r\n  readonly scannerInfo?: VulnerabilityScannerInfo;\r\n  \r\n  /** Detection rules */\r\n  readonly rules?: VulnerabilityDetectionRule[];\r\n  \r\n  /** False positive rate */\r\n  readonly falsePositiveRate?: 'low' | 'medium' | 'high';\r\n  \r\n  /** Detection confidence */\r\n  readonly confidence: ConfidenceLevel;\r\n}\r\n\r\n/**\r\n * Vulnerability Detection Methods\r\n */\r\nexport enum VulnerabilityDetectionMethod {\r\n  STATIC_ANALYSIS = 'static_analysis',\r\n  DYNAMIC_ANALYSIS = 'dynamic_analysis',\r\n  INTERACTIVE_ANALYSIS = 'interactive_analysis',\r\n  MANUAL_REVIEW = 'manual_review',\r\n  PENETRATION_TESTING = 'penetration_testing',\r\n  AUTOMATED_SCANNING = 'automated_scanning',\r\n  SIGNATURE_BASED = 'signature_based',\r\n  BEHAVIORAL_ANALYSIS = 'behavioral_analysis',\r\n  OTHER = 'other',\r\n}\r\n\r\n/**\r\n * Vulnerability Scanner Information\r\n */\r\nexport interface VulnerabilityScannerInfo {\r\n  /** Scanner name */\r\n  readonly name: string;\r\n  \r\n  /** Scanner version */\r\n  readonly version: string;\r\n  \r\n  /** Scanner vendor */\r\n  readonly vendor: string;\r\n  \r\n  /** Scan date */\r\n  readonly scanDate: string;\r\n  \r\n  /** Scan configuration */\r\n  readonly configuration?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Vulnerability Detection Rule\r\n */\r\nexport interface VulnerabilityDetectionRule {\r\n  /** Rule identifier */\r\n  readonly id: string;\r\n  \r\n  /** Rule name */\r\n  readonly name: string;\r\n  \r\n  /** Rule type */\r\n  readonly type: string;\r\n  \r\n  /** Rule content */\r\n  readonly content?: string;\r\n  \r\n  /** Rule confidence */\r\n  readonly confidence: ConfidenceLevel;\r\n}\r\n\r\n/**\r\n * Vulnerability Threat Context\r\n */\r\nexport interface VulnerabilityThreatContext {\r\n  /** Associated threats */\r\n  readonly threats: string[];\r\n  \r\n  /** Threat actors */\r\n  readonly threatActors?: string[];\r\n  \r\n  /** Attack campaigns */\r\n  readonly campaigns?: string[];\r\n  \r\n  /** Threat intelligence */\r\n  readonly intelligence?: VulnerabilityThreatIntelligence[];\r\n}\r\n\r\n/**\r\n * Vulnerability Threat Intelligence\r\n */\r\nexport interface VulnerabilityThreatIntelligence {\r\n  /** Intelligence source */\r\n  readonly source: string;\r\n  \r\n  /** Intelligence type */\r\n  readonly type: string;\r\n  \r\n  /** Intelligence content */\r\n  readonly content: string;\r\n  \r\n  /** Intelligence confidence */\r\n  readonly confidence: ConfidenceLevel;\r\n  \r\n  /** Intelligence date */\r\n  readonly date: string;\r\n}\r\n\r\n/**\r\n * Vulnerability Asset Context\r\n */\r\nexport interface VulnerabilityAssetContext {\r\n  /** Asset identifier */\r\n  readonly assetId: string;\r\n  \r\n  /** Asset name */\r\n  readonly assetName: string;\r\n  \r\n  /** Asset type */\r\n  readonly assetType: string;\r\n  \r\n  /** Asset criticality */\r\n  readonly criticality: 'low' | 'medium' | 'high' | 'critical';\r\n  \r\n  /** Asset exposure */\r\n  readonly exposure: 'internal' | 'external' | 'both';\r\n  \r\n  /** Asset environment */\r\n  readonly environment: 'production' | 'staging' | 'development' | 'test';\r\n  \r\n  /** Vulnerability instance details */\r\n  readonly instanceDetails?: VulnerabilityInstanceDetails;\r\n}\r\n\r\n/**\r\n * Vulnerability Instance Details\r\n */\r\nexport interface VulnerabilityInstanceDetails {\r\n  /** Instance identifier */\r\n  readonly instanceId: string;\r\n  \r\n  /** Discovery date */\r\n  readonly discoveredAt: string;\r\n  \r\n  /** Last verified date */\r\n  readonly lastVerified: string;\r\n  \r\n  /** Instance status */\r\n  readonly status: 'open' | 'fixed' | 'accepted' | 'false_positive';\r\n  \r\n  /** Instance notes */\r\n  readonly notes?: string;\r\n}\r\n\r\n/**\r\n * Vulnerability Sharing Information\r\n */\r\nexport interface VulnerabilitySharingInfo {\r\n  /** Traffic Light Protocol (TLP) marking */\r\n  readonly tlp: 'red' | 'amber' | 'green' | 'white';\r\n  \r\n  /** Sharing restrictions */\r\n  readonly restrictions?: string[];\r\n  \r\n  /** Permitted actions */\r\n  readonly permittedActions?: string[];\r\n  \r\n  /** Sharing agreement reference */\r\n  readonly agreementRef?: string;\r\n  \r\n  /** Copyright information */\r\n  readonly copyright?: string;\r\n  \r\n  /** License information */\r\n  readonly license?: string;\r\n}\r\n\r\n/**\r\n * Vulnerability Data Quality Metrics\r\n */\r\nexport interface VulnerabilityDataQualityMetrics {\r\n  /** Completeness score (0-100) */\r\n  readonly completeness: number;\r\n  \r\n  /** Accuracy score (0-100) */\r\n  readonly accuracy: number;\r\n  \r\n  /** Timeliness score (0-100) */\r\n  readonly timeliness: number;\r\n  \r\n  /** Relevance score (0-100) */\r\n  readonly relevance: number;\r\n  \r\n  /** Overall quality score (0-100) */\r\n  readonly overallScore: number;\r\n  \r\n  /** Quality assessment timestamp */\r\n  readonly assessedAt: string;\r\n  \r\n  /** Quality issues identified */\r\n  readonly issues?: string[];\r\n}\r\n\r\n/**\r\n * Vulnerability Data Validator Interface\r\n */\r\nexport interface VulnerabilityDataValidator {\r\n  /**\r\n   * Validate vulnerability data structure and content\r\n   */\r\n  validate(data: VulnerabilityData): VulnerabilityDataValidationResult;\r\n  \r\n  /**\r\n   * Validate data format version compatibility\r\n   */\r\n  validateVersion(version: string): boolean;\r\n  \r\n  /**\r\n   * Sanitize and normalize vulnerability data\r\n   */\r\n  sanitize(data: VulnerabilityData): VulnerabilityData;\r\n  \r\n  /**\r\n   * Validate CVSS scores\r\n   */\r\n  validateCVSS(cvssData: VulnerabilityCVSSData[]): VulnerabilityCVSSValidationResult[];\r\n}\r\n\r\n/**\r\n * Vulnerability Data Validation Result\r\n */\r\nexport interface VulnerabilityDataValidationResult {\r\n  /** Validation success status */\r\n  readonly isValid: boolean;\r\n  \r\n  /** Validation errors */\r\n  readonly errors: VulnerabilityDataValidationError[];\r\n  \r\n  /** Validation warnings */\r\n  readonly warnings: VulnerabilityDataValidationWarning[];\r\n  \r\n  /** Sanitized data if validation passed */\r\n  readonly sanitizedData?: VulnerabilityData;\r\n  \r\n  /** Quality assessment */\r\n  readonly qualityAssessment?: VulnerabilityDataQualityMetrics;\r\n}\r\n\r\n/**\r\n * Vulnerability Data Validation Error\r\n */\r\nexport interface VulnerabilityDataValidationError {\r\n  /** Error code */\r\n  readonly code: string;\r\n  \r\n  /** Error message */\r\n  readonly message: string;\r\n  \r\n  /** Field path where error occurred */\r\n  readonly field?: string;\r\n  \r\n  /** Invalid value */\r\n  readonly value?: any;\r\n  \r\n  /** Error severity */\r\n  readonly severity: 'error' | 'warning';\r\n  \r\n  /** Suggested fix */\r\n  readonly suggestion?: string;\r\n}\r\n\r\n/**\r\n * Vulnerability Data Validation Warning\r\n */\r\nexport interface VulnerabilityDataValidationWarning {\r\n  /** Warning code */\r\n  readonly code: string;\r\n  \r\n  /** Warning message */\r\n  readonly message: string;\r\n  \r\n  /** Field path where warning occurred */\r\n  readonly field?: string;\r\n  \r\n  /** Warning value */\r\n  readonly value?: any;\r\n  \r\n  /** Suggested action */\r\n  readonly suggestion?: string;\r\n}\r\n\r\n/**\r\n * Vulnerability CVSS Validation Result\r\n */\r\nexport interface VulnerabilityCVSSValidationResult {\r\n  /** CVSS data being validated */\r\n  readonly cvssData: VulnerabilityCVSSData;\r\n  \r\n  /** Validation success status */\r\n  readonly isValid: boolean;\r\n  \r\n  /** Validation errors */\r\n  readonly errors: string[];\r\n  \r\n  /** Validation warnings */\r\n  readonly warnings: string[];\r\n  \r\n  /** Calculated scores validation */\r\n  readonly scoresValid: boolean;\r\n}\r\n\r\n/**\r\n * Vulnerability Data Serializer Interface\r\n */\r\nexport interface VulnerabilityDataSerializer {\r\n  /**\r\n   * Serialize vulnerability data to JSON\r\n   */\r\n  toJson(data: VulnerabilityData): string;\r\n  \r\n  /**\r\n   * Deserialize JSON to vulnerability data\r\n   */\r\n  fromJson(json: string): VulnerabilityData;\r\n  \r\n  /**\r\n   * Serialize to CVE JSON format\r\n   */\r\n  toCveJson(data: VulnerabilityData): string;\r\n  \r\n  /**\r\n   * Deserialize from CVE JSON format\r\n   */\r\n  fromCveJson(json: string): VulnerabilityData;\r\n  \r\n  /**\r\n   * Serialize to binary format for efficient storage\r\n   */\r\n  toBinary(data: VulnerabilityData): Buffer;\r\n  \r\n  /**\r\n   * Deserialize from binary format\r\n   */\r\n  fromBinary(buffer: Buffer): VulnerabilityData;\r\n  \r\n  /**\r\n   * Get supported format versions\r\n   */\r\n  getSupportedVersions(): string[];\r\n}\r\n\r\n/**\r\n * Vulnerability Data Transformer Interface\r\n */\r\nexport interface VulnerabilityDataTransformer {\r\n  /**\r\n   * Transform external vulnerability data to internal format\r\n   */\r\n  transform(externalData: any, sourceFormat: string): VulnerabilityData;\r\n  \r\n  /**\r\n   * Transform internal vulnerability data to external format\r\n   */\r\n  transformToExternal(internalData: VulnerabilityData, targetFormat: string): any;\r\n  \r\n  /**\r\n   * Get supported source formats\r\n   */\r\n  getSupportedSourceFormats(): string[];\r\n  \r\n  /**\r\n   * Get supported target formats\r\n   */\r\n  getSupportedTargetFormats(): string[];\r\n  \r\n  /**\r\n   * Enrich vulnerability data with additional context\r\n   */\r\n  enrich(data: VulnerabilityData, enrichmentSources: string[]): Promise<VulnerabilityData>;\r\n  \r\n  /**\r\n   * Normalize vulnerability data across different sources\r\n   */\r\n  normalize(data: VulnerabilityData[]): VulnerabilityData[];\r\n}"], "version": 3}