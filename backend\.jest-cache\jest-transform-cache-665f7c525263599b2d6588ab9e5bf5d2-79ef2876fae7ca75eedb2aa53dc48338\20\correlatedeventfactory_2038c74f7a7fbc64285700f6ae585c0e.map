{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\correlated-event.factory.ts", "mappings": ";;;AAAA,iFAU6C;AAE7C,6DAA2D;AAE3D,sEAA6D;AAE7D,wFAA8E;AAC9E,0EAAuF;AAgIvF;;;;;;;;;;;;;;GAcG;AACH,MAAa,sBAAsB;IAQjC;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,OAAqC;QACjD,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QAE5C,0CAA0C;QAC1C,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,sBAAsB,CAAC,qBAAqB,EAAE,CAAC;QAE9F,6BAA6B;QAC7B,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe;YAC7C,sBAAsB,CAAC,wBAAwB,CAAC,OAAO,CAAC,kBAAkB,IAAI,EAAE,CAAC,CAAC;QAEpF,oCAAoC;QACpC,MAAM,oBAAoB,GAAyB;YACjD,eAAe,EAAE,aAAa,CAAC,EAAE;YACjC,QAAQ,EAAE,aAAa,CAAC,QAAQ;YAChC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,aAAa,CAAC,IAAI;YACxC,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,aAAa,CAAC,QAAQ;YACpD,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,aAAa,CAAC,MAAM;YAC9C,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,IAAI,oDAAqB,CAAC,UAAU;YAC9E,iBAAiB,EAAE,OAAO,CAAC,iBAAiB,IAAI,2CAAiB,CAAC,OAAO;YACzE,YAAY,EAAE,aAAa,CAAC,YAAY;YACxC,cAAc,EAAE,OAAO,CAAC,cAAc;YACtC,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,aAAa,CAAC,KAAK;YAC3C,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,aAAa,CAAC,WAAW;YAC7D,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,aAAa,CAAC,IAAI;YACxC,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,aAAa,CAAC,SAAS;YACvD,eAAe;YACf,UAAU,EAAE;gBACV,GAAG,aAAa,CAAC,UAAU;gBAC3B,GAAG,OAAO,CAAC,UAAU;aACtB;YACD,aAAa;YACb,aAAa,EAAE,OAAO,CAAC,aAAa,IAAI,aAAa,CAAC,aAAa;YACnE,aAAa,EAAE,OAAO,CAAC,aAAa,IAAI,EAAE;YAC1C,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,EAAE;YACxC,kBAAkB,EAAE,OAAO,CAAC,kBAAkB,IAAI,EAAE;YACpD,eAAe,EAAE,OAAO,CAAC,eAAe,IAAI,EAAE;YAC9C,mBAAmB,EAAE,OAAO,CAAC,mBAAmB,IAAI,EAAE;YACtD,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,mBAAmB,EAAE,OAAO,CAAC,mBAAmB;YAChD,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;YAC9C,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;YACpD,uBAAuB,EAAE,OAAO,CAAC,uBAAuB;YACxD,oBAAoB,EAAE,OAAO,CAAC,iBAAiB;YAC/C,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;YAC1C,mBAAmB,EAAE,CAAC;SACvB,CAAC;QAEF,OAAO,yCAAe,CAAC,MAAM,CAAC,oBAAoB,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB,CAC1B,aAA4B,EAC5B,SAAqC,EAAE;QAEvC,MAAM,UAAU,GAAG,sBAAsB,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAEnE,0BAA0B;QAC1B,MAAM,iBAAiB,GAAG,sBAAsB,CAAC,qBAAqB,CACpE,aAAa,CAAC,YAAY,EAC1B,UAAU,CAAC,cAAc,EACzB,UAAU,CAAC,gBAAgB,CAC5B,CAAC;QAEF,sCAAsC;QACtC,MAAM,uBAAuB,GAAG,sBAAsB,CAAC,gCAAgC,CACrF,iBAAiB,EACjB,UAAU,CACX,CAAC;QAEF,6BAA6B;QAC7B,MAAM,eAAe,GAAG,sBAAsB,CAAC,wBAAwB,CACrE,iBAAiB,CAAC,kBAAkB,CACrC,CAAC;QAEF,yCAAyC;QACzC,MAAM,oBAAoB,GAAG,sBAAsB,CAAC,gCAAgC,CAClF,aAAa,EACb,uBAAuB,EACvB,eAAe,EACf,iBAAiB,CAAC,gBAAgB,EAClC,UAAU,CACX,CAAC;QAEF,OAAO,sBAAsB,CAAC,MAAM,CAAC;YACnC,aAAa;YACb,cAAc,EAAE,iBAAiB,CAAC,cAAc;YAChD,YAAY,EAAE,iBAAiB,CAAC,YAAY;YAC5C,kBAAkB,EAAE,iBAAiB,CAAC,kBAAkB;YACxD,iBAAiB,EAAE,iBAAiB,CAAC,OAAO;gBAC1C,CAAC,CAAC,2CAAiB,CAAC,SAAS;gBAC7B,CAAC,CAAC,2CAAiB,CAAC,MAAM;YAC5B,eAAe;YACf,uBAAuB;YACvB,eAAe,EAAE,iBAAiB,CAAC,eAAe;YAClD,mBAAmB,EAAE,iBAAiB,CAAC,mBAAmB;YAC1D,mBAAmB,EAAE,iBAAiB,CAAC,mBAAmB;YAC1D,kBAAkB,EAAE,iBAAiB,CAAC,kBAAkB;YACxD,qBAAqB,EAAE,iBAAiB,CAAC,qBAAqB;YAC9D,gBAAgB,EAAE,iBAAiB,CAAC,gBAAgB;YACpD,iBAAiB,EAAE,oBAAoB;SACxC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,8BAA8B,CACnC,aAA4B,EAC5B,kBAA+C;QAE/C,sBAAsB;QACtB,MAAM,iBAAiB,GAAG,sBAAsB,CAAC,iBAAiB,CAChE,aAAa,EACb,kBAAkB,CACnB,CAAC;QAEF,kDAAkD;QAClD,MAAM,cAAc,GAAG;YACrB,GAAG,aAAa,CAAC,YAAY;YAC7B,GAAG,iBAAiB,CAAC,cAAc;YACnC,YAAY,EAAE,iBAAiB,CAAC,WAAW,CAAC,CAAC,CAAC;gBAC5C,EAAE,EAAE,iBAAiB,CAAC,WAAW,CAAC,EAAE;gBACpC,IAAI,EAAE,iBAAiB,CAAC,WAAW,CAAC,IAAI;gBACxC,MAAM,EAAE,iBAAiB,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM;gBACnD,UAAU,EAAE,iBAAiB,CAAC,WAAW,CAAC,UAAU;gBACpD,QAAQ,EAAE,iBAAiB,CAAC,WAAW,CAAC,QAAQ;aACjD,CAAC,CAAC,CAAC,IAAI;SACT,CAAC;QAEF,OAAO,sBAAsB,CAAC,MAAM,CAAC;YACnC,aAAa;YACb,cAAc;YACd,YAAY,EAAE,iBAAiB,CAAC,YAAY;YAC5C,kBAAkB,EAAE,iBAAiB,CAAC,kBAAkB;YACxD,iBAAiB,EAAE,2CAAiB,CAAC,SAAS;YAC9C,eAAe,EAAE,iBAAiB,CAAC,WAAW,EAAE,UAAU,IAAI,uCAAe,CAAC,MAAM;YACpF,WAAW,EAAE,iBAAiB,CAAC,WAAW;YAC1C,eAAe,EAAE,iBAAiB,CAAC,eAAe;YAClD,mBAAmB,EAAE,iBAAiB,CAAC,mBAAmB;YAC1D,uBAAuB,EAAE,iBAAiB,CAAC,YAAY;SACxD,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,OAAgC;QAUjD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,UAAU,GAAsB,EAAE,CAAC;QACzC,MAAM,MAAM,GAAsD,EAAE,CAAC;QAErE,KAAK,MAAM,aAAa,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;YACnD,IAAI,CAAC;gBACH,MAAM,eAAe,GAAG,sBAAsB,CAAC,MAAM,CAAC;oBACpD,aAAa;oBACb,cAAc,EAAE,sBAAsB,CAAC,kBAAkB,CACvD,aAAa,CAAC,YAAY,EAC1B,OAAO,CAAC,SAAS,CAClB;oBACD,YAAY,EAAE,OAAO,CAAC,KAAK;iBAC5B,CAAC,CAAC;gBAEH,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACnC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;gBAC9E,MAAM,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC,CAAC;gBAEpD,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;oBAC1B,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAEhD,OAAO;YACL,UAAU;YACV,MAAM;YACN,OAAO,EAAE;gBACP,KAAK,EAAE,OAAO,CAAC,cAAc,CAAC,MAAM;gBACpC,UAAU,EAAE,UAAU,CAAC,MAAM;gBAC7B,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,gBAAgB;aACjB;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CACrB,YAAmD,EAAE;QAErD,+CAA+C;QAC/C,MAAM,iBAAiB,GAAG,SAAS,CAAC,aAAa;YAC/C,sBAAsB,CAAC,uBAAuB,EAAE,CAAC;QAEnD,MAAM,cAAc,GAAiC;YACnD,aAAa,EAAE,iBAAiB;YAChC,cAAc,EAAE;gBACd,GAAG,iBAAiB,CAAC,YAAY;gBACjC,UAAU,EAAE,IAAI;gBAChB,qBAAqB,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBAC/C,oBAAoB,EAAE,CAAC,mBAAmB,EAAE,eAAe,CAAC;gBAC5D,YAAY,EAAE;oBACZ,QAAQ,EAAE,IAAI;oBACd,MAAM,EAAE,CAAC;oBACT,UAAU,EAAE,MAAM;iBACnB;gBACD,cAAc,EAAE,CAAC;aAClB;YACD,YAAY,EAAE;gBACZ,sBAAsB,CAAC,yBAAyB,CAAC,sBAAsB,CAAC;gBACxE,sBAAsB,CAAC,yBAAyB,CAAC,qBAAqB,CAAC;aACxE;YACD,kBAAkB,EAAE;gBAClB,sBAAsB,CAAC,0BAA0B,EAAE;gBACnD,sBAAsB,CAAC,0BAA0B,EAAE;aACpD;YACD,iBAAiB,EAAE,2CAAiB,CAAC,SAAS;YAC9C,eAAe,EAAE,uCAAe,CAAC,IAAI;YACrC,uBAAuB,EAAE,EAAE;YAC3B,eAAe,EAAE,CAAC,IAAI,8BAAc,EAAE,EAAE,IAAI,8BAAc,EAAE,CAAC;YAC7D,mBAAmB,EAAE,CAAC,mBAAmB,EAAE,eAAe,CAAC;SAC5D,CAAC;QAEF,OAAO,sBAAsB,CAAC,MAAM,CAAC;YACnC,GAAG,cAAc;YACjB,GAAG,SAAS;SACb,CAAC,CAAC;IACL,CAAC;IAED,yBAAyB;IAEjB,MAAM,CAAC,gBAAgB,CAAC,MAAkC;QAChE,OAAO;YACL,cAAc,EAAE,EAAE;YAClB,8BAA8B,EAAE,sBAAsB,CAAC,+BAA+B;YACtF,oCAAoC,EAAE,IAAI;YAC1C,kCAAkC,EAAE,IAAI;YACxC,mBAAmB,EAAE,sBAAsB,CAAC,6BAA6B;YACzE,oBAAoB,EAAE,sBAAsB,CAAC,2BAA2B;YACxE,gBAAgB,EAAE,MAAM,CAAC,MAAM,CAAC,6CAAmB,CAAC;YACpD,kBAAkB,EAAE;gBAClB,CAAC,6CAAmB,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACjC,CAAC,6CAAmB,CAAC,OAAO,CAAC,EAAE,CAAC;gBAChC,CAAC,6CAAmB,CAAC,OAAO,CAAC,EAAE,CAAC;gBAChC,CAAC,6CAAmB,CAAC,UAAU,CAAC,EAAE,CAAC;gBACnC,CAAC,6CAAmB,CAAC,SAAS,CAAC,EAAE,CAAC;gBAClC,CAAC,6CAAmB,CAAC,WAAW,CAAC,EAAE,CAAC;gBACpC,CAAC,6CAAmB,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACjC,CAAC,6CAAmB,CAAC,MAAM,CAAC,EAAE,CAAC;aAChC;YACD,qBAAqB,EAAE,sBAAsB,CAAC,+BAA+B;YAC7E,gBAAgB,EAAE,sBAAsB,CAAC,uBAAuB;YAChE,oBAAoB,EAAE,sBAAsB,CAAC,+BAA+B;YAC5E,GAAG,MAAM;SACV,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,qBAAqB,CAClC,YAAiC,EACjC,KAAwB,EACxB,gBAAuC;QAavC,MAAM,cAAc,GAAG,EAAE,GAAG,YAAY,EAAE,CAAC;QAC3C,MAAM,YAAY,GAAsB,EAAE,CAAC;QAC3C,MAAM,kBAAkB,GAAuB,EAAE,CAAC;QAClD,MAAM,gBAAgB,GAAa,EAAE,CAAC;QACtC,MAAM,eAAe,GAAqB,EAAE,CAAC;QAC7C,MAAM,mBAAmB,GAAa,EAAE,CAAC;QAEzC,iDAAiD;QACjD,MAAM,WAAW,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;QAEvE,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;YAC/B,IAAI,CAAC;gBACH,gCAAgC;gBAChC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC1C,SAAS;gBACX,CAAC;gBAED,mBAAmB;gBACnB,MAAM,UAAU,GAAG,sBAAsB,CAAC,SAAS,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;gBAE1E,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;oBACvB,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACxB,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;oBAE/C,IAAI,UAAU,CAAC,kBAAkB,EAAE,CAAC;wBAClC,kBAAkB,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,kBAAkB,CAAC,CAAC;oBAC5D,CAAC;oBAED,IAAI,UAAU,CAAC,eAAe,EAAE,CAAC;wBAC/B,eAAe,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,eAAe,CAAC,CAAC;oBACtD,CAAC;oBAED,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;wBACxB,mBAAmB,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC;oBACnD,CAAC;gBACH,CAAC;qBAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACzB,gBAAgB,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,aAAa,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC;gBACpF,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;gBAC9E,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAClB,gBAAgB,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,kBAAkB,YAAY,EAAE,CAAC,CAAC;gBACrF,CAAC;YACH,CAAC;QACH,CAAC;QAED,+BAA+B;QAC/B,MAAM,QAAQ,GAAG,sBAAsB,CAAC,0BAA0B,CAAC,kBAAkB,CAAC,CAAC;QAEvF,OAAO;YACL,OAAO,EAAE,gBAAgB,CAAC,MAAM,KAAK,CAAC;YACtC,cAAc;YACd,YAAY;YACZ,kBAAkB;YAClB,gBAAgB;YAChB,eAAe;YACf,mBAAmB;YACnB,GAAG,QAAQ;SACZ,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,SAAS,CACtB,IAAyB,EACzB,IAAqB;QASrB,4EAA4E;QAC5E,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,6CAAmB,CAAC,QAAQ;gBAC/B,OAAO,sBAAsB,CAAC,wBAAwB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACrE,KAAK,6CAAmB,CAAC,OAAO;gBAC9B,OAAO,sBAAsB,CAAC,uBAAuB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACpE,KAAK,6CAAmB,CAAC,OAAO;gBAC9B,OAAO,sBAAsB,CAAC,uBAAuB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACpE,KAAK,6CAAmB,CAAC,UAAU;gBACjC,OAAO,sBAAsB,CAAC,0BAA0B,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACvE;gBACE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACnC,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,wBAAwB,CACrC,IAAyB,EACzB,IAAqB;QAQrB,4BAA4B;QAC5B,MAAM,OAAO,GAAuB,EAAE,CAAC;QACvC,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,qCAAqC;QACrC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,OAAO,CAAC,IAAI,CAAC;gBACX,OAAO,EAAE,IAAI,8BAAc,EAAE;gBAC7B,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,8CAAoB,CAAC,QAAQ;gBACxC,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,OAAO,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE;gBAC/D,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,GAAG;aACZ,CAAC,CAAC;YAEH,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACrC,CAAC;QAED,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,GAAG,IAAI;gBACP,oBAAoB,EAAE;oBACpB,cAAc,EAAE,QAAQ,CAAC,MAAM;oBAC/B,aAAa,EAAE,OAAO,CAAC,MAAM;oBAC7B,UAAU,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;iBACxC;aACF;YACD,kBAAkB,EAAE,OAAO;YAC3B,QAAQ;SACT,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,uBAAuB,CACpC,IAAyB,EACzB,IAAqB;QAQrB,2BAA2B;QAC3B,MAAM,OAAO,GAAuB,EAAE,CAAC;QACvC,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,oCAAoC;QACpC,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YAC1C,OAAO,CAAC,IAAI,CAAC;gBACX,OAAO,EAAE,IAAI,8BAAc,EAAE;gBAC7B,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,8CAAoB,CAAC,OAAO;gBACvC,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,cAAc,EAAE,gBAAgB,EAAE;gBAC9D,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,GAAG;aACZ,CAAC,CAAC;YAEH,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACjC,CAAC;QAED,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,GAAG,IAAI;gBACP,mBAAmB,EAAE;oBACnB,cAAc,EAAE,QAAQ,CAAC,MAAM;oBAC/B,aAAa,EAAE,OAAO,CAAC,MAAM;oBAC7B,UAAU,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;iBACxC;aACF;YACD,kBAAkB,EAAE,OAAO;YAC3B,QAAQ;SACT,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,uBAAuB,CACpC,IAAyB,EACzB,IAAqB;QAQrB,2BAA2B;QAC3B,MAAM,OAAO,GAAuB,EAAE,CAAC;QACvC,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,sCAAsC;QACtC,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACrC,OAAO,CAAC,IAAI,CAAC;gBACX,OAAO,EAAE,IAAI,8BAAc,EAAE;gBAC7B,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,8CAAoB,CAAC,OAAO;gBACvC,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,OAAO,EAAE,EAAE,cAAc,EAAE,IAAI,EAAE,OAAO,EAAE,kBAAkB,EAAE;gBAC9D,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,GAAG;aACZ,CAAC,CAAC;YAEH,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACpC,CAAC;QAED,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,GAAG,IAAI;gBACP,mBAAmB,EAAE;oBACnB,cAAc,EAAE,QAAQ,CAAC,MAAM;oBAC/B,aAAa,EAAE,OAAO,CAAC,MAAM;oBAC7B,UAAU,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;iBACxC;aACF;YACD,kBAAkB,EAAE,OAAO;YAC3B,QAAQ;SACT,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,0BAA0B,CACvC,IAAyB,EACzB,IAAqB;QAQrB,8BAA8B;QAC9B,MAAM,OAAO,GAAuB,EAAE,CAAC;QACvC,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,uCAAuC;QACvC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClC,OAAO,CAAC,IAAI,CAAC;gBACX,OAAO,EAAE,IAAI,8BAAc,EAAE;gBAC7B,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,8CAAoB,CAAC,OAAO;gBACvC,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,OAAO,EAAE,EAAE,iBAAiB,EAAE,IAAI,EAAE,cAAc,EAAE,EAAE,EAAE;gBACxD,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,GAAG;aACZ,CAAC,CAAC;YAEH,QAAQ,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACtC,CAAC;QAED,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,GAAG,IAAI;gBACP,sBAAsB,EAAE;oBACtB,cAAc,EAAE,QAAQ,CAAC,MAAM;oBAC/B,aAAa,EAAE,OAAO,CAAC,MAAM;oBAC7B,UAAU,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBACvC,aAAa,EAAE,EAAE;iBAClB;aACF;YACD,kBAAkB,EAAE,OAAO;YAC3B,QAAQ;SACT,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,iBAAiB,CAC9B,aAA4B,EAC5B,OAAoC;QAUpC,oCAAoC;QACpC,MAAM,cAAc,GAAG,EAAE,GAAG,aAAa,CAAC,YAAY,EAAE,CAAC;QACzD,MAAM,YAAY,GAAsB,OAAO,CAAC,WAAW,IAAI,EAAE,CAAC;QAClE,MAAM,kBAAkB,GAAuB,EAAE,CAAC;QAClD,MAAM,eAAe,GAAqB,EAAE,CAAC;QAC7C,MAAM,mBAAmB,GAAa,EAAE,CAAC;QAEzC,8BAA8B;QAC9B,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,6BAA6B;QAEzE,IAAI,cAAc,EAAE,CAAC;YACnB,MAAM,WAAW,GAAgB;gBAC/B,EAAE,EAAE,gBAAgB,IAAI,CAAC,GAAG,EAAE,EAAE;gBAChC,IAAI,EAAE,6BAA6B;gBACnC,WAAW,EAAE,yCAAyC;gBACtD,MAAM,EAAE;oBACN;wBACE,EAAE,EAAE,SAAS;wBACb,IAAI,EAAE,gBAAgB;wBACtB,WAAW,EAAE,oBAAoB;wBACjC,QAAQ,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC;wBAC5B,KAAK,EAAE,CAAC;wBACR,UAAU,EAAE,uCAAe,CAAC,IAAI;wBAChC,SAAS,EAAE,IAAI,IAAI,EAAE;wBACrB,MAAM,EAAE,gBAAgB;wBACxB,UAAU,EAAE,CAAC,WAAW,CAAC;qBAC1B;iBACF;gBACD,UAAU,EAAE,OAAO,CAAC,aAAa;gBACjC,QAAQ,EAAE,aAAa,CAAC,QAAQ;gBAChC,QAAQ,EAAE;oBACR,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,EAAE,aAAa;oBACxD,OAAO,EAAE,IAAI,IAAI,EAAE;oBACnB,QAAQ,EAAE,OAAO;iBAClB;aACF,CAAC;YAEF,mBAAmB,CAAC,IAAI,CAAC,cAAc,EAAE,oBAAoB,CAAC,CAAC;YAE/D,OAAO;gBACL,cAAc,EAAE;oBACd,GAAG,cAAc;oBACjB,qBAAqB,EAAE,IAAI;oBAC3B,eAAe,EAAE,WAAW,CAAC,EAAE;iBAChC;gBACD,YAAY;gBACZ,kBAAkB;gBAClB,WAAW;gBACX,eAAe;gBACf,mBAAmB;gBACnB,YAAY,EAAE,EAAE;aACjB,CAAC;QACJ,CAAC;QAED,OAAO;YACL,cAAc;YACd,YAAY;YACZ,kBAAkB;YAClB,eAAe;YACf,mBAAmB;YACnB,YAAY,EAAE,EAAE;SACjB,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,gCAAgC,CAC7C,MAAyF,EACzF,MAAyB;QAEzB,IAAI,KAAK,GAAG,GAAG,CAAC;QAEhB,qCAAqC;QACrC,KAAK,IAAI,MAAM,CAAC,gBAAgB,CAAC,MAAM,GAAG,EAAE,CAAC;QAE7C,qCAAqC;QACrC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACpB,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,+CAA+C;QAC/C,MAAM,aAAa,GAAG,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1E,MAAM,oBAAoB,GAAG,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/E,MAAM,oBAAoB,GAAG,aAAa,CAAC,MAAM,GAAG,oBAAoB,CAAC,MAAM,CAAC;QAChF,KAAK,IAAI,oBAAoB,GAAG,EAAE,CAAC;QAEnC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;IAC3C,CAAC;IAEO,MAAM,CAAC,wBAAwB,CAAC,OAA2B;QACjE,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,uCAAe,CAAC,GAAG,CAAC;QAErD,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;QACjG,OAAO,4CAAoB,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;IAC9D,CAAC;IAEO,MAAM,CAAC,gCAAgC,CAC7C,aAA4B,EAC5B,uBAA+B,EAC/B,eAAgC,EAChC,gBAA0B,EAC1B,MAAyB;QAEzB,qDAAqD;QACrD,IAAI,MAAM,CAAC,oCAAoC;YAC3C,4CAAoB,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC;YACjD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,wCAAwC;QACxC,IAAI,aAAa,CAAC,QAAQ,KAAK,mCAAa,CAAC,QAAQ,EAAE,CAAC;YACtD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,iDAAiD;QACjD,IAAI,uBAAuB,GAAG,MAAM,CAAC,8BAA8B,EAAE,CAAC;YACpE,OAAO,IAAI,CAAC;QACd,CAAC;QAED,mDAAmD;QACnD,IAAI,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC,mBAAmB,EAAE,CAAC;YACzD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,MAAM,CAAC,0BAA0B,CAAC,OAA2B;QAKnE,MAAM,QAAQ,GAAQ,EAAE,CAAC;QAEzB,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,KAAK,8CAAoB,CAAC,QAAQ,CAAC,CAAC;QAC3F,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,QAAQ,CAAC,mBAAmB,GAAG;gBAC7B,UAAU,EAAE,OAAO,EAAE,SAAS;gBAC9B,aAAa,EAAE,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;gBAClD,WAAW,EAAE,UAAU;gBACvB,UAAU,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,eAAe,CAAC,MAAM;aAC/F,CAAC;QACJ,CAAC;QAED,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,KAAK,8CAAoB,CAAC,OAAO,CAAC,CAAC;QACzF,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,QAAQ,CAAC,kBAAkB,GAAG;gBAC5B,SAAS,EAAE,CAAC,eAAe,CAAC;gBAC5B,SAAS,EAAE,CAAC,WAAW,CAAC;gBACxB,eAAe,EAAE,CAAC,gBAAgB,CAAC;gBACnC,iBAAiB,EAAE,CAAC,SAAS,CAAC;gBAC9B,UAAU,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,cAAc,CAAC,MAAM;aAC7F,CAAC;QACJ,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,MAAM,CAAC,kBAAkB,CAC/B,YAAiC,EACjC,SAAiC;QAEjC,mEAAmE;QACnE,OAAO;YACL,GAAG,YAAY;YACf,aAAa,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACvC,sBAAsB,EAAE,SAAS,IAAI,EAAE;SACxC,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,qBAAqB;QAClC,OAAO,QAAQ,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACzE,CAAC;IAEO,MAAM,CAAC,uBAAuB;QACpC,qEAAqE;QACrE,0EAA0E;QAC1E,OAAO,EAAmB,CAAC;IAC7B,CAAC;IAEO,MAAM,CAAC,yBAAyB,CAAC,IAAY;QACnD,OAAO;YACL,EAAE,EAAE,QAAQ,IAAI,EAAE;YAClB,IAAI,EAAE,GAAG,IAAI,OAAO;YACpB,WAAW,EAAE,QAAQ,IAAI,mBAAmB;YAC5C,IAAI,EAAE,6CAAmB,CAAC,QAAQ;YAClC,QAAQ,EAAE,GAAG;YACb,QAAQ,EAAE,KAAK;YACf,YAAY,EAAE,OAAO;YACrB,aAAa,EAAE,EAAE;SAClB,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,0BAA0B;QACvC,OAAO;YACL,OAAO,EAAE,IAAI,8BAAc,EAAE;YAC7B,UAAU,EAAE,EAAE;YACd,SAAS,EAAE,8CAAoB,CAAC,OAAO;YACvC,MAAM,EAAE,WAAW;YACnB,OAAO,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;YACvB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,MAAM,EAAE,GAAG;SACZ,CAAC;IACJ,CAAC;;AAxwBH,wDAywBC;AAxwByB,sDAA+B,GAAG,EAAE,CAAC;AACrC,oDAA6B,GAAG,EAAE,CAAC;AACnC,kDAA2B,GAAG,MAAM,CAAC,CAAC,YAAY;AAClD,sDAA+B,GAAG,CAAC,CAAC;AACpC,8CAAuB,GAAG,OAAO,CAAC,CAAC,SAAS;AAC5C,sDAA+B,GAAG,GAAG,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\correlated-event.factory.ts"], "sourcesContent": ["import { \r\n  CorrelatedEvent, \r\n  CorrelatedEventProps, \r\n  CorrelationStatus, \r\n  CorrelationRule, \r\n  CorrelationMatch, \r\n  CorrelationResult,\r\n  AttackChain,\r\n  CorrelationRuleType,\r\n  CorrelationMatchType\r\n} from '../entities/correlated-event.entity';\r\nimport { EnrichedEvent } from '../entities/enriched-event.entity';\r\nimport { UniqueEntityId } from '../../../../shared-kernel';\r\nimport { EventType } from '../enums/event-type.enum';\r\nimport { EventSeverity } from '../enums/event-severity.enum';\r\nimport { EventStatus } from '../enums/event-status.enum';\r\nimport { EventProcessingStatus } from '../enums/event-processing-status.enum';\r\nimport { ConfidenceLevel, ConfidenceLevelUtils } from '../enums/confidence-level.enum';\r\n\r\n/**\r\n * Correlated Event Creation Options\r\n */\r\nexport interface CreateCorrelatedEventOptions {\r\n  /** Correlated event ID (optional, will be generated if not provided) */\r\n  id?: UniqueEntityId;\r\n  /** Enriched event to correlate */\r\n  enrichedEvent: EnrichedEvent;\r\n  /** Correlated data with correlation context */\r\n  correlatedData: Record<string, any>;\r\n  /** Applied correlation rules */\r\n  appliedRules?: CorrelationRule[];\r\n  /** Correlation matches found */\r\n  correlationMatches?: CorrelationMatch[];\r\n  /** Initial correlation status (optional, defaults to PENDING) */\r\n  correlationStatus?: CorrelationStatus;\r\n  /** Override event type (optional, uses enriched event type if not provided) */\r\n  type?: EventType;\r\n  /** Override event severity (optional, uses enriched event severity if not provided) */\r\n  severity?: EventSeverity;\r\n  /** Override event status (optional, uses enriched event status if not provided) */\r\n  status?: EventStatus;\r\n  /** Override processing status (optional, uses CORRELATED if not provided) */\r\n  processingStatus?: EventProcessingStatus;\r\n  /** Override title (optional, uses enriched event title if not provided) */\r\n  title?: string;\r\n  /** Override description (optional, uses enriched event description if not provided) */\r\n  description?: string;\r\n  /** Override tags (optional, uses enriched event tags if not provided) */\r\n  tags?: string[];\r\n  /** Override risk score (optional, uses enriched event risk score if not provided) */\r\n  riskScore?: number;\r\n  /** Confidence level of correlation */\r\n  confidenceLevel?: ConfidenceLevel;\r\n  /** Additional attributes */\r\n  attributes?: Record<string, any>;\r\n  /** Correlation ID for grouping related events */\r\n  correlationId?: string;\r\n  /** Parent event ID if this is a child event */\r\n  parentEventId?: UniqueEntityId;\r\n  /** Child event IDs if this is a parent event */\r\n  childEventIds?: UniqueEntityId[];\r\n  /** Related event IDs (correlated events) */\r\n  relatedEventIds?: UniqueEntityId[];\r\n  /** Correlation patterns identified */\r\n  correlationPatterns?: string[];\r\n  /** Attack chain information */\r\n  attackChain?: AttackChain;\r\n  /** Force manual review requirement */\r\n  forceManualReview?: boolean;\r\n  /** Initial correlation quality score */\r\n  correlationQualityScore?: number;\r\n  /** Temporal correlation data */\r\n  temporalCorrelation?: CorrelatedEventProps['temporalCorrelation'];\r\n  /** Spatial correlation data */\r\n  spatialCorrelation?: CorrelatedEventProps['spatialCorrelation'];\r\n  /** Behavioral correlation data */\r\n  behavioralCorrelation?: CorrelatedEventProps['behavioralCorrelation'];\r\n  /** Initial validation errors */\r\n  validationErrors?: string[];\r\n}\r\n\r\n/**\r\n * Correlation Configuration\r\n */\r\nexport interface CorrelationConfig {\r\n  /** Available correlation rules */\r\n  availableRules: CorrelationRule[];\r\n  /** Minimum correlation quality threshold */\r\n  minCorrelationQualityThreshold: number;\r\n  /** Whether to require manual review for high-confidence correlations */\r\n  requireManualReviewForHighConfidence: boolean;\r\n  /** Whether to require manual review for attack chains */\r\n  requireManualReviewForAttackChains: boolean;\r\n  /** Maximum allowed validation errors */\r\n  maxValidationErrors: number;\r\n  /** Default correlation timeout in milliseconds */\r\n  correlationTimeoutMs: number;\r\n  /** Enabled correlation rule types */\r\n  enabledRuleTypes: CorrelationRuleType[];\r\n  /** Rule type priority mapping */\r\n  ruleTypePriorities: Record<CorrelationRuleType, number>;\r\n  /** Maximum concurrent correlation requests */\r\n  maxConcurrentRequests: number;\r\n  /** Time window for temporal correlation in milliseconds */\r\n  temporalWindowMs: number;\r\n  /** Maximum events to correlate in a single operation */\r\n  maxEventsToCorrelate: number;\r\n}\r\n\r\n/**\r\n * Batch Correlation Options\r\n */\r\nexport interface BatchCorrelationOptions {\r\n  /** Enriched events to correlate */\r\n  enrichedEvents: EnrichedEvent[];\r\n  /** Rules to apply to all events */\r\n  rules: CorrelationRule[];\r\n  /** Whether to stop on first failure */\r\n  stopOnFailure?: boolean;\r\n  /** Maximum concurrent correlations */\r\n  maxConcurrency?: number;\r\n  /** Batch processing timeout */\r\n  batchTimeoutMs?: number;\r\n  /** Correlation rule types to use */\r\n  ruleTypes?: CorrelationRuleType[];\r\n}\r\n\r\n/**\r\n * Attack Chain Detection Options\r\n */\r\nexport interface AttackChainDetectionOptions {\r\n  /** Events to analyze for attack chains */\r\n  events: EnrichedEvent[];\r\n  /** Time window for attack chain detection in milliseconds */\r\n  timeWindowMs: number;\r\n  /** Minimum confidence threshold for attack chain */\r\n  minConfidence: ConfidenceLevel;\r\n  /** Maximum stages in an attack chain */\r\n  maxStages: number;\r\n  /** Whether to include behavioral analysis */\r\n  includeBehavioralAnalysis: boolean;\r\n  /** Custom attack chain rules */\r\n  customRules?: CorrelationRule[];\r\n}\r\n\r\n/**\r\n * CorrelatedEvent Factory\r\n * \r\n * Factory class for creating CorrelatedEvent entities with proper validation and defaults.\r\n * Handles complex correlation scenarios and ensures all business rules are applied.\r\n * \r\n * Key responsibilities:\r\n * - Create correlated events from enriched events\r\n * - Apply correlation rules and validation\r\n * - Calculate correlation quality scores\r\n * - Determine manual review requirements\r\n * - Handle batch correlation operations\r\n * - Support attack chain detection and analysis\r\n * - Manage temporal, spatial, and behavioral correlations\r\n */\r\nexport class CorrelatedEventFactory {\r\n  private static readonly DEFAULT_MIN_CORRELATION_QUALITY = 70;\r\n  private static readonly DEFAULT_MAX_VALIDATION_ERRORS = 10;\r\n  private static readonly DEFAULT_CORRELATION_TIMEOUT = 120000; // 2 minutes\r\n  private static readonly DEFAULT_MAX_CONCURRENT_REQUESTS = 5;\r\n  private static readonly DEFAULT_TEMPORAL_WINDOW = 3600000; // 1 hour\r\n  private static readonly DEFAULT_MAX_EVENTS_TO_CORRELATE = 100;\r\n\r\n  /**\r\n   * Create a new CorrelatedEvent from an EnrichedEvent\r\n   */\r\n  static create(options: CreateCorrelatedEventOptions): CorrelatedEvent {\r\n    const enrichedEvent = options.enrichedEvent;\r\n\r\n    // Generate correlation ID if not provided\r\n    const correlationId = options.correlationId || CorrelatedEventFactory.generateCorrelationId();\r\n\r\n    // Determine confidence level\r\n    const confidenceLevel = options.confidenceLevel || \r\n      CorrelatedEventFactory.calculateConfidenceLevel(options.correlationMatches || []);\r\n\r\n    // Build correlated event properties\r\n    const correlatedEventProps: CorrelatedEventProps = {\r\n      enrichedEventId: enrichedEvent.id,\r\n      metadata: enrichedEvent.metadata,\r\n      type: options.type || enrichedEvent.type,\r\n      severity: options.severity || enrichedEvent.severity,\r\n      status: options.status || enrichedEvent.status,\r\n      processingStatus: options.processingStatus || EventProcessingStatus.CORRELATED,\r\n      correlationStatus: options.correlationStatus || CorrelationStatus.PENDING,\r\n      enrichedData: enrichedEvent.enrichedData,\r\n      correlatedData: options.correlatedData,\r\n      title: options.title || enrichedEvent.title,\r\n      description: options.description || enrichedEvent.description,\r\n      tags: options.tags || enrichedEvent.tags,\r\n      riskScore: options.riskScore || enrichedEvent.riskScore,\r\n      confidenceLevel,\r\n      attributes: {\r\n        ...enrichedEvent.attributes,\r\n        ...options.attributes,\r\n      },\r\n      correlationId,\r\n      parentEventId: options.parentEventId || enrichedEvent.parentEventId,\r\n      childEventIds: options.childEventIds || [],\r\n      appliedRules: options.appliedRules || [],\r\n      correlationMatches: options.correlationMatches || [],\r\n      relatedEventIds: options.relatedEventIds || [],\r\n      correlationPatterns: options.correlationPatterns || [],\r\n      attackChain: options.attackChain,\r\n      temporalCorrelation: options.temporalCorrelation,\r\n      spatialCorrelation: options.spatialCorrelation,\r\n      behavioralCorrelation: options.behavioralCorrelation,\r\n      correlationQualityScore: options.correlationQualityScore,\r\n      requiresManualReview: options.forceManualReview,\r\n      validationErrors: options.validationErrors,\r\n      correlationAttempts: 0,\r\n    };\r\n\r\n    return CorrelatedEvent.create(correlatedEventProps, options.id);\r\n  }\r\n\r\n  /**\r\n   * Create a CorrelatedEvent with automatic correlation\r\n   */\r\n  static createWithCorrelation(\r\n    enrichedEvent: EnrichedEvent,\r\n    config: Partial<CorrelationConfig> = {}\r\n  ): CorrelatedEvent {\r\n    const fullConfig = CorrelatedEventFactory.getDefaultConfig(config);\r\n    \r\n    // Apply correlation rules\r\n    const correlationResult = CorrelatedEventFactory.applyCorrelationRules(\r\n      enrichedEvent.enrichedData,\r\n      fullConfig.availableRules,\r\n      fullConfig.enabledRuleTypes\r\n    );\r\n\r\n    // Calculate correlation quality score\r\n    const correlationQualityScore = CorrelatedEventFactory.calculateCorrelationQualityScore(\r\n      correlationResult,\r\n      fullConfig\r\n    );\r\n\r\n    // Calculate confidence level\r\n    const confidenceLevel = CorrelatedEventFactory.calculateConfidenceLevel(\r\n      correlationResult.correlationMatches\r\n    );\r\n\r\n    // Determine if manual review is required\r\n    const requiresManualReview = CorrelatedEventFactory.determineManualReviewRequirement(\r\n      enrichedEvent,\r\n      correlationQualityScore,\r\n      confidenceLevel,\r\n      correlationResult.validationErrors,\r\n      fullConfig\r\n    );\r\n\r\n    return CorrelatedEventFactory.create({\r\n      enrichedEvent,\r\n      correlatedData: correlationResult.correlatedData,\r\n      appliedRules: correlationResult.appliedRules,\r\n      correlationMatches: correlationResult.correlationMatches,\r\n      correlationStatus: correlationResult.success \r\n        ? CorrelationStatus.COMPLETED \r\n        : CorrelationStatus.FAILED,\r\n      confidenceLevel,\r\n      correlationQualityScore,\r\n      relatedEventIds: correlationResult.relatedEventIds,\r\n      correlationPatterns: correlationResult.correlationPatterns,\r\n      temporalCorrelation: correlationResult.temporalCorrelation,\r\n      spatialCorrelation: correlationResult.spatialCorrelation,\r\n      behavioralCorrelation: correlationResult.behavioralCorrelation,\r\n      validationErrors: correlationResult.validationErrors,\r\n      forceManualReview: requiresManualReview,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a CorrelatedEvent with attack chain detection\r\n   */\r\n  static createWithAttackChainDetection(\r\n    enrichedEvent: EnrichedEvent,\r\n    attackChainOptions: AttackChainDetectionOptions\r\n  ): CorrelatedEvent {\r\n    // Detect attack chain\r\n    const attackChainResult = CorrelatedEventFactory.detectAttackChain(\r\n      enrichedEvent,\r\n      attackChainOptions\r\n    );\r\n\r\n    // Build correlated data with attack chain context\r\n    const correlatedData = {\r\n      ...enrichedEvent.enrichedData,\r\n      ...attackChainResult.correlatedData,\r\n      attack_chain: attackChainResult.attackChain ? {\r\n        id: attackChainResult.attackChain.id,\r\n        name: attackChainResult.attackChain.name,\r\n        stages: attackChainResult.attackChain.stages.length,\r\n        confidence: attackChainResult.attackChain.confidence,\r\n        severity: attackChainResult.attackChain.severity,\r\n      } : null,\r\n    };\r\n\r\n    return CorrelatedEventFactory.create({\r\n      enrichedEvent,\r\n      correlatedData,\r\n      appliedRules: attackChainResult.appliedRules,\r\n      correlationMatches: attackChainResult.correlationMatches,\r\n      correlationStatus: CorrelationStatus.COMPLETED,\r\n      confidenceLevel: attackChainResult.attackChain?.confidence || ConfidenceLevel.MEDIUM,\r\n      attackChain: attackChainResult.attackChain,\r\n      relatedEventIds: attackChainResult.relatedEventIds,\r\n      correlationPatterns: attackChainResult.correlationPatterns,\r\n      correlationQualityScore: attackChainResult.qualityScore,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create multiple CorrelatedEvents in batch\r\n   */\r\n  static createBatch(options: BatchCorrelationOptions): {\r\n    successful: CorrelatedEvent[];\r\n    failed: { enrichedEvent: EnrichedEvent; error: string }[];\r\n    summary: {\r\n      total: number;\r\n      successful: number;\r\n      failed: number;\r\n      processingTimeMs: number;\r\n    };\r\n  } {\r\n    const startTime = Date.now();\r\n    const successful: CorrelatedEvent[] = [];\r\n    const failed: { enrichedEvent: EnrichedEvent; error: string }[] = [];\r\n\r\n    for (const enrichedEvent of options.enrichedEvents) {\r\n      try {\r\n        const correlatedEvent = CorrelatedEventFactory.create({\r\n          enrichedEvent,\r\n          correlatedData: CorrelatedEventFactory.correlateEventData(\r\n            enrichedEvent.enrichedData,\r\n            options.ruleTypes\r\n          ),\r\n          appliedRules: options.rules,\r\n        });\r\n\r\n        successful.push(correlatedEvent);\r\n      } catch (error) {\r\n        const errorMessage = error instanceof Error ? error.message : 'Unknown error';\r\n        failed.push({ enrichedEvent, error: errorMessage });\r\n\r\n        if (options.stopOnFailure) {\r\n          break;\r\n        }\r\n      }\r\n    }\r\n\r\n    const processingTimeMs = Date.now() - startTime;\r\n\r\n    return {\r\n      successful,\r\n      failed,\r\n      summary: {\r\n        total: options.enrichedEvents.length,\r\n        successful: successful.length,\r\n        failed: failed.length,\r\n        processingTimeMs,\r\n      },\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create a CorrelatedEvent for testing purposes\r\n   */\r\n  static createForTesting(\r\n    overrides: Partial<CreateCorrelatedEventOptions> = {}\r\n  ): CorrelatedEvent {\r\n    // Create a mock enriched event if not provided\r\n    const mockEnrichedEvent = overrides.enrichedEvent || \r\n      CorrelatedEventFactory.createMockEnrichedEvent();\r\n\r\n    const defaultOptions: CreateCorrelatedEventOptions = {\r\n      enrichedEvent: mockEnrichedEvent,\r\n      correlatedData: {\r\n        ...mockEnrichedEvent.enrichedData,\r\n        correlated: true,\r\n        correlation_timestamp: new Date().toISOString(),\r\n        correlation_patterns: ['temporal_sequence', 'ip_clustering'],\r\n        attack_chain: {\r\n          detected: true,\r\n          stages: 3,\r\n          confidence: 'high',\r\n        },\r\n        related_events: 5,\r\n      },\r\n      appliedRules: [\r\n        CorrelatedEventFactory.createMockCorrelationRule('temporal_correlation'),\r\n        CorrelatedEventFactory.createMockCorrelationRule('spatial_correlation'),\r\n      ],\r\n      correlationMatches: [\r\n        CorrelatedEventFactory.createMockCorrelationMatch(),\r\n        CorrelatedEventFactory.createMockCorrelationMatch(),\r\n      ],\r\n      correlationStatus: CorrelationStatus.COMPLETED,\r\n      confidenceLevel: ConfidenceLevel.HIGH,\r\n      correlationQualityScore: 85,\r\n      relatedEventIds: [new UniqueEntityId(), new UniqueEntityId()],\r\n      correlationPatterns: ['temporal_sequence', 'ip_clustering'],\r\n    };\r\n\r\n    return CorrelatedEventFactory.create({\r\n      ...defaultOptions,\r\n      ...overrides,\r\n    });\r\n  }\r\n\r\n  // Private helper methods\r\n\r\n  private static getDefaultConfig(config: Partial<CorrelationConfig>): CorrelationConfig {\r\n    return {\r\n      availableRules: [],\r\n      minCorrelationQualityThreshold: CorrelatedEventFactory.DEFAULT_MIN_CORRELATION_QUALITY,\r\n      requireManualReviewForHighConfidence: true,\r\n      requireManualReviewForAttackChains: true,\r\n      maxValidationErrors: CorrelatedEventFactory.DEFAULT_MAX_VALIDATION_ERRORS,\r\n      correlationTimeoutMs: CorrelatedEventFactory.DEFAULT_CORRELATION_TIMEOUT,\r\n      enabledRuleTypes: Object.values(CorrelationRuleType),\r\n      ruleTypePriorities: {\r\n        [CorrelationRuleType.TEMPORAL]: 1,\r\n        [CorrelationRuleType.SPATIAL]: 2,\r\n        [CorrelationRuleType.PATTERN]: 3,\r\n        [CorrelationRuleType.BEHAVIORAL]: 4,\r\n        [CorrelationRuleType.SIGNATURE]: 5,\r\n        [CorrelationRuleType.STATISTICAL]: 6,\r\n        [CorrelationRuleType.SEMANTIC]: 7,\r\n        [CorrelationRuleType.CAUSAL]: 8,\r\n      },\r\n      maxConcurrentRequests: CorrelatedEventFactory.DEFAULT_MAX_CONCURRENT_REQUESTS,\r\n      temporalWindowMs: CorrelatedEventFactory.DEFAULT_TEMPORAL_WINDOW,\r\n      maxEventsToCorrelate: CorrelatedEventFactory.DEFAULT_MAX_EVENTS_TO_CORRELATE,\r\n      ...config,\r\n    };\r\n  }\r\n\r\n  private static applyCorrelationRules(\r\n    enrichedData: Record<string, any>,\r\n    rules: CorrelationRule[],\r\n    enabledRuleTypes: CorrelationRuleType[]\r\n  ): {\r\n    success: boolean;\r\n    correlatedData: Record<string, any>;\r\n    appliedRules: CorrelationRule[];\r\n    correlationMatches: CorrelationMatch[];\r\n    validationErrors: string[];\r\n    relatedEventIds: UniqueEntityId[];\r\n    correlationPatterns: string[];\r\n    temporalCorrelation?: CorrelatedEventProps['temporalCorrelation'];\r\n    spatialCorrelation?: CorrelatedEventProps['spatialCorrelation'];\r\n    behavioralCorrelation?: CorrelatedEventProps['behavioralCorrelation'];\r\n  } {\r\n    const correlatedData = { ...enrichedData };\r\n    const appliedRules: CorrelationRule[] = [];\r\n    const correlationMatches: CorrelationMatch[] = [];\r\n    const validationErrors: string[] = [];\r\n    const relatedEventIds: UniqueEntityId[] = [];\r\n    const correlationPatterns: string[] = [];\r\n\r\n    // Sort rules by priority (higher priority first)\r\n    const sortedRules = [...rules].sort((a, b) => b.priority - a.priority);\r\n\r\n    for (const rule of sortedRules) {\r\n      try {\r\n        // Check if rule type is enabled\r\n        if (!enabledRuleTypes.includes(rule.type)) {\r\n          continue;\r\n        }\r\n\r\n        // Apply rule logic\r\n        const ruleResult = CorrelatedEventFactory.applyRule(correlatedData, rule);\r\n        \r\n        if (ruleResult.success) {\r\n          appliedRules.push(rule);\r\n          Object.assign(correlatedData, ruleResult.data);\r\n          \r\n          if (ruleResult.correlationMatches) {\r\n            correlationMatches.push(...ruleResult.correlationMatches);\r\n          }\r\n\r\n          if (ruleResult.relatedEventIds) {\r\n            relatedEventIds.push(...ruleResult.relatedEventIds);\r\n          }\r\n\r\n          if (ruleResult.patterns) {\r\n            correlationPatterns.push(...ruleResult.patterns);\r\n          }\r\n        } else if (rule.required) {\r\n          validationErrors.push(`Required rule '${rule.name}' failed: ${ruleResult.error}`);\r\n        }\r\n      } catch (error) {\r\n        const errorMessage = error instanceof Error ? error.message : 'Unknown error';\r\n        if (rule.required) {\r\n          validationErrors.push(`Required rule '${rule.name}' threw error: ${errorMessage}`);\r\n        }\r\n      }\r\n    }\r\n\r\n    // Extract correlation contexts\r\n    const contexts = CorrelatedEventFactory.extractCorrelationContexts(correlationMatches);\r\n\r\n    return {\r\n      success: validationErrors.length === 0,\r\n      correlatedData,\r\n      appliedRules,\r\n      correlationMatches,\r\n      validationErrors,\r\n      relatedEventIds,\r\n      correlationPatterns,\r\n      ...contexts,\r\n    };\r\n  }\r\n\r\n  private static applyRule(\r\n    data: Record<string, any>,\r\n    rule: CorrelationRule\r\n  ): { \r\n    success: boolean; \r\n    data?: Record<string, any>; \r\n    error?: string;\r\n    correlationMatches?: CorrelationMatch[];\r\n    relatedEventIds?: UniqueEntityId[];\r\n    patterns?: string[];\r\n  } {\r\n    // Simplified implementation - in practice, this would be more sophisticated\r\n    switch (rule.type) {\r\n      case CorrelationRuleType.TEMPORAL:\r\n        return CorrelatedEventFactory.applyTemporalCorrelation(data, rule);\r\n      case CorrelationRuleType.SPATIAL:\r\n        return CorrelatedEventFactory.applySpatialCorrelation(data, rule);\r\n      case CorrelationRuleType.PATTERN:\r\n        return CorrelatedEventFactory.applyPatternCorrelation(data, rule);\r\n      case CorrelationRuleType.BEHAVIORAL:\r\n        return CorrelatedEventFactory.applyBehavioralCorrelation(data, rule);\r\n      default:\r\n        return { success: true, data };\r\n    }\r\n  }\r\n\r\n  private static applyTemporalCorrelation(\r\n    data: Record<string, any>,\r\n    rule: CorrelationRule\r\n  ): {\r\n    success: boolean;\r\n    data?: Record<string, any>;\r\n    error?: string;\r\n    correlationMatches?: CorrelationMatch[];\r\n    patterns?: string[];\r\n  } {\r\n    // Mock temporal correlation\r\n    const matches: CorrelationMatch[] = [];\r\n    const patterns: string[] = [];\r\n\r\n    // Simulate finding temporal patterns\r\n    if (data.timestamp) {\r\n      matches.push({\r\n        eventId: new UniqueEntityId(),\r\n        confidence: 75,\r\n        matchType: CorrelationMatchType.TEMPORAL,\r\n        ruleId: rule.id,\r\n        details: { timeWindow: rule.timeWindowMs, pattern: 'sequence' },\r\n        timestamp: new Date(),\r\n        weight: 0.8,\r\n      });\r\n\r\n      patterns.push('temporal_sequence');\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: {\r\n        ...data,\r\n        temporal_correlation: {\r\n          patterns_found: patterns.length,\r\n          matches_found: matches.length,\r\n          confidence: matches.length > 0 ? 75 : 0,\r\n        },\r\n      },\r\n      correlationMatches: matches,\r\n      patterns,\r\n    };\r\n  }\r\n\r\n  private static applySpatialCorrelation(\r\n    data: Record<string, any>,\r\n    rule: CorrelationRule\r\n  ): {\r\n    success: boolean;\r\n    data?: Record<string, any>;\r\n    error?: string;\r\n    correlationMatches?: CorrelationMatch[];\r\n    patterns?: string[];\r\n  } {\r\n    // Mock spatial correlation\r\n    const matches: CorrelationMatch[] = [];\r\n    const patterns: string[] = [];\r\n\r\n    // Simulate finding spatial patterns\r\n    if (data.source_ip || data.destination_ip) {\r\n      matches.push({\r\n        eventId: new UniqueEntityId(),\r\n        confidence: 80,\r\n        matchType: CorrelationMatchType.SPATIAL,\r\n        ruleId: rule.id,\r\n        details: { ipCluster: true, networkSegment: '***********/24' },\r\n        timestamp: new Date(),\r\n        weight: 0.9,\r\n      });\r\n\r\n      patterns.push('ip_clustering');\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: {\r\n        ...data,\r\n        spatial_correlation: {\r\n          patterns_found: patterns.length,\r\n          matches_found: matches.length,\r\n          confidence: matches.length > 0 ? 80 : 0,\r\n        },\r\n      },\r\n      correlationMatches: matches,\r\n      patterns,\r\n    };\r\n  }\r\n\r\n  private static applyPatternCorrelation(\r\n    data: Record<string, any>,\r\n    rule: CorrelationRule\r\n  ): {\r\n    success: boolean;\r\n    data?: Record<string, any>;\r\n    error?: string;\r\n    correlationMatches?: CorrelationMatch[];\r\n    patterns?: string[];\r\n  } {\r\n    // Mock pattern correlation\r\n    const matches: CorrelationMatch[] = [];\r\n    const patterns: string[] = [];\r\n\r\n    // Simulate finding signature patterns\r\n    if (data.event_type && data.severity) {\r\n      matches.push({\r\n        eventId: new UniqueEntityId(),\r\n        confidence: 70,\r\n        matchType: CorrelationMatchType.PATTERN,\r\n        ruleId: rule.id,\r\n        details: { signatureMatch: true, pattern: 'attack_signature' },\r\n        timestamp: new Date(),\r\n        weight: 0.7,\r\n      });\r\n\r\n      patterns.push('attack_signature');\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: {\r\n        ...data,\r\n        pattern_correlation: {\r\n          patterns_found: patterns.length,\r\n          matches_found: matches.length,\r\n          confidence: matches.length > 0 ? 70 : 0,\r\n        },\r\n      },\r\n      correlationMatches: matches,\r\n      patterns,\r\n    };\r\n  }\r\n\r\n  private static applyBehavioralCorrelation(\r\n    data: Record<string, any>,\r\n    rule: CorrelationRule\r\n  ): {\r\n    success: boolean;\r\n    data?: Record<string, any>;\r\n    error?: string;\r\n    correlationMatches?: CorrelationMatch[];\r\n    patterns?: string[];\r\n  } {\r\n    // Mock behavioral correlation\r\n    const matches: CorrelationMatch[] = [];\r\n    const patterns: string[] = [];\r\n\r\n    // Simulate finding behavioral patterns\r\n    if (data.user_id || data.asset_id) {\r\n      matches.push({\r\n        eventId: new UniqueEntityId(),\r\n        confidence: 65,\r\n        matchType: CorrelationMatchType.PATTERN,\r\n        ruleId: rule.id,\r\n        details: { behavioralAnomaly: true, deviationScore: 85 },\r\n        timestamp: new Date(),\r\n        weight: 0.6,\r\n      });\r\n\r\n      patterns.push('behavioral_anomaly');\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: {\r\n        ...data,\r\n        behavioral_correlation: {\r\n          patterns_found: patterns.length,\r\n          matches_found: matches.length,\r\n          confidence: matches.length > 0 ? 65 : 0,\r\n          anomaly_score: 85,\r\n        },\r\n      },\r\n      correlationMatches: matches,\r\n      patterns,\r\n    };\r\n  }\r\n\r\n  private static detectAttackChain(\r\n    enrichedEvent: EnrichedEvent,\r\n    options: AttackChainDetectionOptions\r\n  ): {\r\n    correlatedData: Record<string, any>;\r\n    appliedRules: CorrelationRule[];\r\n    correlationMatches: CorrelationMatch[];\r\n    attackChain?: AttackChain;\r\n    relatedEventIds: UniqueEntityId[];\r\n    correlationPatterns: string[];\r\n    qualityScore: number;\r\n  } {\r\n    // Simplified attack chain detection\r\n    const correlatedData = { ...enrichedEvent.enrichedData };\r\n    const appliedRules: CorrelationRule[] = options.customRules || [];\r\n    const correlationMatches: CorrelationMatch[] = [];\r\n    const relatedEventIds: UniqueEntityId[] = [];\r\n    const correlationPatterns: string[] = [];\r\n\r\n    // Mock attack chain detection\r\n    const hasAttackChain = Math.random() > 0.7; // 30% chance of attack chain\r\n\r\n    if (hasAttackChain) {\r\n      const attackChain: AttackChain = {\r\n        id: `attack_chain_${Date.now()}`,\r\n        name: 'Multi-stage Attack Campaign',\r\n        description: 'Coordinated attack with multiple stages',\r\n        stages: [\r\n          {\r\n            id: 'stage_1',\r\n            name: 'Initial Access',\r\n            description: 'Initial compromise',\r\n            eventIds: [enrichedEvent.id],\r\n            order: 1,\r\n            confidence: ConfidenceLevel.HIGH,\r\n            timestamp: new Date(),\r\n            tactic: 'Initial Access',\r\n            techniques: ['T1566.001'],\r\n          },\r\n        ],\r\n        confidence: options.minConfidence,\r\n        severity: enrichedEvent.severity,\r\n        timeline: {\r\n          startTime: new Date(Date.now() - 3600000), // 1 hour ago\r\n          endTime: new Date(),\r\n          duration: 3600000,\r\n        },\r\n      };\r\n\r\n      correlationPatterns.push('attack_chain', 'multi_stage_attack');\r\n      \r\n      return {\r\n        correlatedData: {\r\n          ...correlatedData,\r\n          attack_chain_detected: true,\r\n          attack_chain_id: attackChain.id,\r\n        },\r\n        appliedRules,\r\n        correlationMatches,\r\n        attackChain,\r\n        relatedEventIds,\r\n        correlationPatterns,\r\n        qualityScore: 90,\r\n      };\r\n    }\r\n\r\n    return {\r\n      correlatedData,\r\n      appliedRules,\r\n      correlationMatches,\r\n      relatedEventIds,\r\n      correlationPatterns,\r\n      qualityScore: 70,\r\n    };\r\n  }\r\n\r\n  private static calculateCorrelationQualityScore(\r\n    result: { success: boolean; appliedRules: CorrelationRule[]; validationErrors: string[] },\r\n    config: CorrelationConfig\r\n  ): number {\r\n    let score = 100;\r\n\r\n    // Reduce score for validation errors\r\n    score -= result.validationErrors.length * 15;\r\n\r\n    // Reduce score if correlation failed\r\n    if (!result.success) {\r\n      score -= 25;\r\n    }\r\n\r\n    // Reduce score based on missing required rules\r\n    const requiredRules = config.availableRules.filter(rule => rule.required);\r\n    const appliedRequiredRules = result.appliedRules.filter(rule => rule.required);\r\n    const missingRequiredRules = requiredRules.length - appliedRequiredRules.length;\r\n    score -= missingRequiredRules * 20;\r\n\r\n    return Math.max(0, Math.min(100, score));\r\n  }\r\n\r\n  private static calculateConfidenceLevel(matches: CorrelationMatch[]): ConfidenceLevel {\r\n    if (matches.length === 0) return ConfidenceLevel.LOW;\r\n\r\n    const avgConfidence = matches.reduce((sum, match) => sum + match.confidence, 0) / matches.length;\r\n    return ConfidenceLevelUtils.fromNumericValue(avgConfidence);\r\n  }\r\n\r\n  private static determineManualReviewRequirement(\r\n    enrichedEvent: EnrichedEvent,\r\n    correlationQualityScore: number,\r\n    confidenceLevel: ConfidenceLevel,\r\n    validationErrors: string[],\r\n    config: CorrelationConfig\r\n  ): boolean {\r\n    // High confidence correlations require manual review\r\n    if (config.requireManualReviewForHighConfidence && \r\n        ConfidenceLevelUtils.isHigh(confidenceLevel)) {\r\n      return true;\r\n    }\r\n\r\n    // Critical events require manual review\r\n    if (enrichedEvent.severity === EventSeverity.CRITICAL) {\r\n      return true;\r\n    }\r\n\r\n    // Low correlation quality requires manual review\r\n    if (correlationQualityScore < config.minCorrelationQualityThreshold) {\r\n      return true;\r\n    }\r\n\r\n    // Too many validation errors require manual review\r\n    if (validationErrors.length > config.maxValidationErrors) {\r\n      return true;\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  private static extractCorrelationContexts(matches: CorrelationMatch[]): {\r\n    temporalCorrelation?: CorrelatedEventProps['temporalCorrelation'];\r\n    spatialCorrelation?: CorrelatedEventProps['spatialCorrelation'];\r\n    behavioralCorrelation?: CorrelatedEventProps['behavioralCorrelation'];\r\n  } {\r\n    const contexts: any = {};\r\n\r\n    const temporalMatches = matches.filter(m => m.matchType === CorrelationMatchType.TEMPORAL);\r\n    if (temporalMatches.length > 0) {\r\n      contexts.temporalCorrelation = {\r\n        timeWindow: 3600000, // 1 hour\r\n        eventSequence: temporalMatches.map(m => m.eventId),\r\n        patternType: 'sequence',\r\n        confidence: temporalMatches.reduce((sum, m) => sum + m.confidence, 0) / temporalMatches.length,\r\n      };\r\n    }\r\n\r\n    const spatialMatches = matches.filter(m => m.matchType === CorrelationMatchType.SPATIAL);\r\n    if (spatialMatches.length > 0) {\r\n      contexts.spatialCorrelation = {\r\n        sourceIps: ['*************'],\r\n        targetIps: ['*********'],\r\n        networkSegments: ['***********/24'],\r\n        geographicRegions: ['US-East'],\r\n        confidence: spatialMatches.reduce((sum, m) => sum + m.confidence, 0) / spatialMatches.length,\r\n      };\r\n    }\r\n\r\n    return contexts;\r\n  }\r\n\r\n  private static correlateEventData(\r\n    enrichedData: Record<string, any>,\r\n    ruleTypes?: CorrelationRuleType[]\r\n  ): Record<string, any> {\r\n    // Basic correlation - in practice this would be more sophisticated\r\n    return {\r\n      ...enrichedData,\r\n      correlated_at: new Date().toISOString(),\r\n      correlation_rule_types: ruleTypes || [],\r\n    };\r\n  }\r\n\r\n  private static generateCorrelationId(): string {\r\n    return `corr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n  }\r\n\r\n  private static createMockEnrichedEvent(): EnrichedEvent {\r\n    // This would create a proper mock EnrichedEvent - simplified for now\r\n    // In practice, you'd use the EnrichedEventFactory to create a proper mock\r\n    return {} as EnrichedEvent;\r\n  }\r\n\r\n  private static createMockCorrelationRule(type: string): CorrelationRule {\r\n    return {\r\n      id: `rule_${type}`,\r\n      name: `${type} Rule`,\r\n      description: `Mock ${type} correlation rule`,\r\n      type: CorrelationRuleType.TEMPORAL,\r\n      priority: 100,\r\n      required: false,\r\n      timeWindowMs: 3600000,\r\n      minConfidence: 70,\r\n    };\r\n  }\r\n\r\n  private static createMockCorrelationMatch(): CorrelationMatch {\r\n    return {\r\n      eventId: new UniqueEntityId(),\r\n      confidence: 80,\r\n      matchType: CorrelationMatchType.PATTERN,\r\n      ruleId: 'mock_rule',\r\n      details: { mock: true },\r\n      timestamp: new Date(),\r\n      weight: 0.8,\r\n    };\r\n  }\r\n}"], "version": 3}