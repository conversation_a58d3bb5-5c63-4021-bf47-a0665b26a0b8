{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\value-objects\\correlation-id.spec.ts", "mappings": ";;AAAA,iGAAgF;AAChF,qGAAmF;AAEnF,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;IAC7B,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,IAAI,GAAG,sCAAsC,CAAC;YACpD,MAAM,aAAa,GAAG,2CAAa,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAErD,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,aAAa,GAAG,2CAAa,CAAC,QAAQ,EAAE,CAAC;YAE/C,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1C,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,2CAAa,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,QAAQ,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YAC3C,MAAM,aAAa,GAAG,2CAAa,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YAEjE,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACjD,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,QAAQ,GAAG,2CAAa,CAAC,QAAQ,EAAE,CAAC;YAC1C,MAAM,IAAI,GAAG,2CAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAE5C,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,sBAAsB;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,2CAAa,CAAC,IAAW,CAAC,CAAC,CAAC,OAAO,CAAC,2CAA2C,CAAC,CAAC;QACpG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,2CAAa,CAAC,SAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,2CAA2C,CAAC,CAAC;QACzG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,2CAAa,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,2CAAa,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,2CAAa,CAAC,GAAU,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,2CAAa,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,2CAAa,CAAC,sCAAsC,CAAC,CAAC,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;QAClH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,SAAS,GAAG,sCAAsC,CAAC;YACzD,MAAM,CAAC,2CAAa,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,CAAC,2CAAa,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1D,MAAM,CAAC,2CAAa,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,2CAAa,CAAC,OAAO,CAAC,IAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvD,MAAM,CAAC,2CAAa,CAAC,OAAO,CAAC,SAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,SAAS,GAAG,sCAAsC,CAAC;YACzD,MAAM,MAAM,GAAG,2CAAa,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YAEjD,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;YAC9B,MAAM,CAAC,MAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,MAAM,GAAG,2CAAa,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,IAAI,aAA4B,CAAC;QAEjC,UAAU,CAAC,GAAG,EAAE;YACd,aAAa,GAAG,2CAAa,CAAC,UAAU,CAAC,sCAAsC,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAC5B,MAAM,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAC5B,MAAM,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,WAAW,GAAG,aAAa,CAAC,aAAa,EAAE,CAAC;YAClD,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACrC,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,aAAa,GAAG,aAAa,CAAC,eAAe,EAAE,CAAC;YACtD,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,SAAS,GAAG,aAAa,CAAC,WAAW,EAAE,CAAC;YAC9C,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,SAAS,GAAG,aAAa,CAAC,WAAW,EAAE,CAAC;YAC9C,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,IAAI,iBAAgC,CAAC;QAErC,UAAU,CAAC,GAAG,EAAE;YACd,iBAAiB,GAAG,2CAAa,CAAC,QAAQ,EAAE,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,KAAK,GAAG,iBAAiB,CAAC,WAAW,EAAE,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;YAC7C,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,KAAK,GAAG,iBAAiB,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YACpD,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;YAC7C,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,OAAO,GAAG,iBAAiB,CAAC,aAAa,EAAE,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,OAAO,GAAG,iBAAiB,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;YACjE,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,MAAM,GAAG,iBAAiB,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YACrD,MAAM,MAAM,GAAG,iBAAiB,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,MAAM,GAAG,iBAAiB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YACtD,MAAM,MAAM,GAAG,iBAAiB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,aAAa,GAAG,2CAAa,CAAC,QAAQ,EAAE,CAAC;YAC/C,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,IAAI,GAAG,sCAAsC,CAAC;YACpD,MAAM,cAAc,GAAG,2CAAa,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,cAAc,GAAG,2CAAa,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAEtD,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzD,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,cAAc,GAAG,2CAAa,CAAC,QAAQ,EAAE,CAAC;YAChD,MAAM,cAAc,GAAG,2CAAa,CAAC,QAAQ,EAAE,CAAC;YAEhD,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1D,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,aAAa,GAAG,2CAAa,CAAC,QAAQ,EAAE,CAAC;YAC/C,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,IAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,SAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,aAAa,GAAG,2CAAa,CAAC,QAAQ,EAAE,CAAC;YAC/C,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,aAAa,GAAG,2CAAa,CAAC,UAAU,EAAE,CAAC;YACjD,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,SAAS,GAAG,SAAS,CAAC;YAC5B,MAAM,aAAa,GAAG,2CAAa,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE3C,0BAA0B;YAC1B,MAAM,cAAc,GAAG,2CAAa,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YAC3D,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,aAAa,GAAG,2CAAa,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YAC/D,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,aAAa,GAAG,2CAAa,CAAC,YAAY,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YACzE,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE3C,0BAA0B;YAC1B,MAAM,cAAc,GAAG,2CAAa,CAAC,YAAY,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAC1E,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,CAAC,GAAG,EAAE,CAAC,2CAAa,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;YACvF,MAAM,CAAC,GAAG,EAAE,CAAC,2CAAa,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;QAC5F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,aAAa,GAAG,2CAAa,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YAC1D,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,aAAa,GAAG,2CAAa,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;YAC7D,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE3C,0BAA0B;YAC1B,MAAM,cAAc,GAAG,2CAAa,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;YAC9D,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,GAAG,EAAE,CAAC,2CAAa,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;YAC7E,MAAM,CAAC,GAAG,EAAE,CAAC,2CAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,aAAa,GAAG,2CAAa,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;YAC7D,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,aAAa,GAAG,2CAAa,CAAC,QAAQ,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;YACxE,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE3C,0BAA0B;YAC1B,MAAM,cAAc,GAAG,2CAAa,CAAC,QAAQ,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;YACzE,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,CAAC,GAAG,EAAE,CAAC,2CAAa,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAC;YAC/E,MAAM,CAAC,GAAG,EAAE,CAAC,2CAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,aAAa,GAAG,2CAAa,CAAC,UAAU,CAAC,sCAAsC,CAAC,CAAC;YACvF,MAAM,OAAO,GAAG,aAAa,CAAC,iBAAiB,EAAE,CAAC;YAElD,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YAC3E,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YACnE,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,aAAa,GAAG,2CAAa,CAAC,UAAU,CAAC,sCAAsC,CAAC,CAAC;YACvF,MAAM,OAAO,GAAG,aAAa,CAAC,SAAS,EAAE,CAAC;YAE1C,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YACjF,MAAM,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,aAAa,GAAG,2CAAa,CAAC,UAAU,CAAC,sCAAsC,CAAC,CAAC;YACvF,MAAM,OAAO,GAAG,aAAa,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YAExD,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YAC7E,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,OAAO,GAAG;gBACd,kBAAkB,EAAE,sCAAsC;aAC3D,CAAC;YACF,MAAM,aAAa,GAAG,2CAAa,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAEzD,MAAM,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,CAAC,aAAc,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kEAAkE,EAAE,GAAG,EAAE;YAC1E,MAAM,OAAO,GAAG;gBACd,cAAc,EAAE,sCAAsC;aACvD,CAAC;YACF,MAAM,aAAa,GAAG,2CAAa,CAAC,WAAW,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;YAEzE,MAAM,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,CAAC,aAAc,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,OAAO,GAAG;gBACd,kBAAkB,EAAE,sCAAsC;aAC3D,CAAC;YACF,MAAM,aAAa,GAAG,2CAAa,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAEzD,MAAM,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,CAAC,aAAc,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,OAAO,GAAG;gBACd,kBAAkB,EAAE,CAAC,sCAAsC,EAAE,aAAa,CAAC;aAC5E,CAAC;YACF,MAAM,aAAa,GAAG,2CAAa,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAEzD,MAAM,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,CAAC,aAAc,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,OAAO,GAAG,EAAE,CAAC;YACnB,MAAM,aAAa,GAAG,2CAAa,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YACzD,MAAM,CAAC,aAAa,CAAC,CAAC,QAAQ,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,OAAO,GAAG;gBACd,kBAAkB,EAAE,cAAc;aACnC,CAAC;YACF,MAAM,aAAa,GAAG,2CAAa,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YACzD,MAAM,CAAC,aAAa,CAAC,CAAC,QAAQ,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,cAAc,GAAG,2CAAa,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAErD,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE5D,uBAAuB;YACvB,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;YACjE,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,cAAc,GAAG,2CAAa,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrD,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,GAAG,EAAE,CAAC,2CAAa,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,IAAI,GAAG,sCAAsC,CAAC;YACpD,MAAM,aAAa,GAAG,2CAAa,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wBAAwB,EAAE,GAAG,EAAE;YAChC,MAAM,IAAI,GAAG,sCAAsC,CAAC;YACpD,MAAM,aAAa,GAAG,2CAAa,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,IAAI,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC;YAEpC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACjC,MAAM,IAAI,GAAG,sCAAsC,CAAC;YACpD,MAAM,IAAI,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;YAC7B,MAAM,aAAa,GAAG,2CAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEnD,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,aAAa,GAAG,2CAAa,CAAC,QAAQ,EAAE,CAAC;YAC/C,MAAM,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC;YAE1C,sDAAsD;YACtD,MAAM,CAAC,GAAG,EAAE;gBACT,aAAqB,CAAC,MAAM,GAAG,UAAU,CAAC;YAC7C,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,sDAAsD;YAExE,gCAAgC;YAChC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kBAAkB,EAAE,GAAG,EAAE;YAC1B,MAAM,aAAa,GAAG,2CAAa,CAAC,QAAQ,EAAE,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,KAAK,GAAG,sCAAsC,CAAC;YACrD,MAAM,KAAK,GAAG,sCAAsC,CAAC;YAErD,MAAM,cAAc,GAAG,2CAAa,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YACvD,MAAM,cAAc,GAAG,2CAAa,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAEvD,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,IAAI,GAAG,sCAAsC,CAAC;YACpD,MAAM,aAAa,GAAG,2CAAa,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,YAAY,GAAG,2CAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACpD,MAAM,YAAY,GAAG,2CAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACpD,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;YACtE,MAAM,YAAY,GAAG,2CAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACrD,MAAM,YAAY,GAAG,2CAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACrD,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\value-objects\\correlation-id.spec.ts"], "sourcesContent": ["import { CorrelationId } from '../../value-objects/correlation-id.value-object';\r\nimport { UniqueEntityId } from '../../value-objects/unique-entity-id.value-object';\r\n\r\ndescribe('CorrelationId', () => {\r\n  describe('creation', () => {\r\n    it('should create a valid correlation ID from UUID string', () => {\r\n      const uuid = '123e4567-e89b-42d3-a456-************';\r\n      const correlationId = CorrelationId.fromString(uuid);\r\n\r\n      expect(correlationId.value).toBe(uuid);\r\n      expect(correlationId.isValid()).toBe(true);\r\n    });\r\n\r\n    it('should generate a new correlation ID', () => {\r\n      const correlationId = CorrelationId.generate();\r\n\r\n      expect(correlationId.value).toBeDefined();\r\n      expect(correlationId.isValid()).toBe(true);\r\n      expect(CorrelationId.isValid(correlationId.value)).toBe(true);\r\n    });\r\n\r\n    it('should create correlation ID from UniqueEntityId', () => {\r\n      const uniqueId = UniqueEntityId.generate();\r\n      const correlationId = CorrelationId.fromUniqueEntityId(uniqueId);\r\n\r\n      expect(correlationId.value).toBe(uniqueId.value);\r\n      expect(correlationId.uniqueId.equals(uniqueId)).toBe(true);\r\n    });\r\n\r\n    it('should create correlation ID from existing ID', () => {\r\n      const original = CorrelationId.generate();\r\n      const copy = CorrelationId.fromId(original);\r\n\r\n      expect(copy.value).toBe(original.value);\r\n      expect(copy.equals(original)).toBe(true);\r\n      expect(copy).not.toBe(original); // Different instances\r\n    });\r\n  });\r\n\r\n  describe('validation', () => {\r\n    it('should throw error for null value', () => {\r\n      expect(() => new CorrelationId(null as any)).toThrow('CorrelationId cannot be null or undefined');\r\n    });\r\n\r\n    it('should throw error for undefined value', () => {\r\n      expect(() => new CorrelationId(undefined as any)).toThrow('CorrelationId cannot be null or undefined');\r\n    });\r\n\r\n    it('should throw error for empty string', () => {\r\n      expect(() => new CorrelationId('')).toThrow('CorrelationId cannot be empty');\r\n    });\r\n\r\n    it('should throw error for whitespace string', () => {\r\n      expect(() => new CorrelationId('   ')).toThrow('CorrelationId cannot be empty');\r\n    });\r\n\r\n    it('should throw error for non-string value', () => {\r\n      expect(() => new CorrelationId(123 as any)).toThrow('CorrelationId must be a string');\r\n    });\r\n\r\n    it('should throw error for invalid UUID format', () => {\r\n      expect(() => new CorrelationId('invalid-uuid')).toThrow('Invalid CorrelationId format');\r\n    });\r\n\r\n    it('should throw error for non-UUID v4 format', () => {\r\n      expect(() => new CorrelationId('123e4567-e89b-22d3-a456-************')).toThrow('Invalid CorrelationId format');\r\n    });\r\n  });\r\n\r\n  describe('static validation methods', () => {\r\n    it('should validate correct UUID strings', () => {\r\n      const validUuid = '123e4567-e89b-42d3-a456-************';\r\n      expect(CorrelationId.isValid(validUuid)).toBe(true);\r\n    });\r\n\r\n    it('should reject invalid UUID strings', () => {\r\n      expect(CorrelationId.isValid('invalid-uuid')).toBe(false);\r\n      expect(CorrelationId.isValid('')).toBe(false);\r\n      expect(CorrelationId.isValid(null as any)).toBe(false);\r\n      expect(CorrelationId.isValid(undefined as any)).toBe(false);\r\n    });\r\n\r\n    it('should try parse valid UUID', () => {\r\n      const validUuid = '123e4567-e89b-42d3-a456-************';\r\n      const result = CorrelationId.tryParse(validUuid);\r\n\r\n      expect(result).not.toBeNull();\r\n      expect(result!.value).toBe(validUuid);\r\n    });\r\n\r\n    it('should return null for invalid UUID in tryParse', () => {\r\n      const result = CorrelationId.tryParse('invalid-uuid');\r\n      expect(result).toBeNull();\r\n    });\r\n  });\r\n\r\n  describe('utility methods', () => {\r\n    let correlationId: CorrelationId;\r\n\r\n    beforeEach(() => {\r\n      correlationId = CorrelationId.fromString('123e4567-e89b-42d3-a456-************');\r\n    });\r\n\r\n    it('should get version', () => {\r\n      expect(correlationId.getVersion()).toBe(1);\r\n    });\r\n\r\n    it('should get variant', () => {\r\n      expect(correlationId.getVariant()).toBe('RFC4122');\r\n    });\r\n\r\n    it('should get short string representation', () => {\r\n      const shortString = correlationId.toShortString();\r\n      expect(shortString).toBe('123e4567');\r\n      expect(correlationId.toShortString(4)).toBe('123e');\r\n    });\r\n\r\n    it('should get compact string representation', () => {\r\n      const compactString = correlationId.toCompactString();\r\n      expect(compactString).toBe('123e4567e89b42d3a456************');\r\n    });\r\n\r\n    it('should convert to uppercase', () => {\r\n      const upperCase = correlationId.toUpperCase();\r\n      expect(upperCase).toBe('123e4567-e89b-42d3-a456-************');\r\n    });\r\n\r\n    it('should convert to lowercase', () => {\r\n      const lowerCase = correlationId.toLowerCase();\r\n      expect(lowerCase).toBe('123e4567-e89b-42d3-a456-************');\r\n    });\r\n  });\r\n\r\n  describe('hierarchical correlation', () => {\r\n    let parentCorrelation: CorrelationId;\r\n\r\n    beforeEach(() => {\r\n      parentCorrelation = CorrelationId.generate();\r\n    });\r\n\r\n    it('should create child correlation ID', () => {\r\n      const child = parentCorrelation.createChild();\r\n      expect(child).not.toEqual(parentCorrelation);\r\n      expect(child.isValid()).toBe(true);\r\n    });\r\n\r\n    it('should create child correlation ID with suffix', () => {\r\n      const child = parentCorrelation.createChild('auth');\r\n      expect(child).not.toEqual(parentCorrelation);\r\n      expect(child.isValid()).toBe(true);\r\n    });\r\n\r\n    it('should create sibling correlation ID', () => {\r\n      const sibling = parentCorrelation.createSibling();\r\n      expect(sibling).not.toEqual(parentCorrelation);\r\n      expect(sibling.isValid()).toBe(true);\r\n    });\r\n\r\n    it('should create sibling correlation ID with suffix', () => {\r\n      const sibling = parentCorrelation.createSibling('parallel-task');\r\n      expect(sibling).not.toEqual(parentCorrelation);\r\n      expect(sibling.isValid()).toBe(true);\r\n    });\r\n\r\n    it('should create consistent child IDs from same parent', () => {\r\n      const child1 = parentCorrelation.createChild('test');\r\n      const child2 = parentCorrelation.createChild('test');\r\n      expect(child1.equals(child2)).toBe(true);\r\n    });\r\n\r\n    it('should create different child IDs with different suffixes', () => {\r\n      const child1 = parentCorrelation.createChild('test1');\r\n      const child2 = parentCorrelation.createChild('test2');\r\n      expect(child1.equals(child2)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('equality and comparison', () => {\r\n    it('should be equal to itself', () => {\r\n      const correlationId = CorrelationId.generate();\r\n      expect(correlationId.equals(correlationId)).toBe(true);\r\n      expect(correlationId.matches(correlationId)).toBe(true);\r\n    });\r\n\r\n    it('should be equal to correlation ID with same value', () => {\r\n      const uuid = '123e4567-e89b-42d3-a456-************';\r\n      const correlationId1 = CorrelationId.fromString(uuid);\r\n      const correlationId2 = CorrelationId.fromString(uuid);\r\n\r\n      expect(correlationId1.equals(correlationId2)).toBe(true);\r\n      expect(correlationId1.matches(correlationId2)).toBe(true);\r\n    });\r\n\r\n    it('should not be equal to correlation ID with different value', () => {\r\n      const correlationId1 = CorrelationId.generate();\r\n      const correlationId2 = CorrelationId.generate();\r\n\r\n      expect(correlationId1.equals(correlationId2)).toBe(false);\r\n      expect(correlationId1.matches(correlationId2)).toBe(false);\r\n    });\r\n\r\n    it('should not be equal to null or undefined', () => {\r\n      const correlationId = CorrelationId.generate();\r\n      expect(correlationId.equals(null as any)).toBe(false);\r\n      expect(correlationId.equals(undefined as any)).toBe(false);\r\n    });\r\n\r\n    it('should not be equal to non-CorrelationId object', () => {\r\n      const correlationId = CorrelationId.generate();\r\n      expect(correlationId.equals({} as any)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('factory methods', () => {\r\n    it('should create correlation ID for request', () => {\r\n      const correlationId = CorrelationId.forRequest();\r\n      expect(correlationId.isValid()).toBe(true);\r\n    });\r\n\r\n    it('should create correlation ID for request with ID', () => {\r\n      const requestId = 'req-123';\r\n      const correlationId = CorrelationId.forRequest(requestId);\r\n      expect(correlationId.isValid()).toBe(true);\r\n      \r\n      // Should be deterministic\r\n      const correlationId2 = CorrelationId.forRequest(requestId);\r\n      expect(correlationId.equals(correlationId2)).toBe(true);\r\n    });\r\n\r\n    it('should create correlation ID for operation', () => {\r\n      const correlationId = CorrelationId.forOperation('user-login');\r\n      expect(correlationId.isValid()).toBe(true);\r\n    });\r\n\r\n    it('should create correlation ID for operation with ID', () => {\r\n      const correlationId = CorrelationId.forOperation('user-login', 'op-456');\r\n      expect(correlationId.isValid()).toBe(true);\r\n      \r\n      // Should be deterministic\r\n      const correlationId2 = CorrelationId.forOperation('user-login', 'op-456');\r\n      expect(correlationId.equals(correlationId2)).toBe(true);\r\n    });\r\n\r\n    it('should throw error for empty operation name', () => {\r\n      expect(() => CorrelationId.forOperation('')).toThrow('Operation name cannot be empty');\r\n      expect(() => CorrelationId.forOperation('   ')).toThrow('Operation name cannot be empty');\r\n    });\r\n\r\n    it('should create correlation ID for batch', () => {\r\n      const correlationId = CorrelationId.forBatch('batch-789');\r\n      expect(correlationId.isValid()).toBe(true);\r\n    });\r\n\r\n    it('should create correlation ID for batch item', () => {\r\n      const correlationId = CorrelationId.forBatch('batch-789', 5);\r\n      expect(correlationId.isValid()).toBe(true);\r\n      \r\n      // Should be deterministic\r\n      const correlationId2 = CorrelationId.forBatch('batch-789', 5);\r\n      expect(correlationId.equals(correlationId2)).toBe(true);\r\n    });\r\n\r\n    it('should throw error for empty batch ID', () => {\r\n      expect(() => CorrelationId.forBatch('')).toThrow('Batch ID cannot be empty');\r\n      expect(() => CorrelationId.forBatch('   ')).toThrow('Batch ID cannot be empty');\r\n    });\r\n\r\n    it('should create correlation ID for event', () => {\r\n      const correlationId = CorrelationId.forEvent('user-created');\r\n      expect(correlationId.isValid()).toBe(true);\r\n    });\r\n\r\n    it('should create correlation ID for event with ID', () => {\r\n      const correlationId = CorrelationId.forEvent('user-created', 'evt-123');\r\n      expect(correlationId.isValid()).toBe(true);\r\n      \r\n      // Should be deterministic\r\n      const correlationId2 = CorrelationId.forEvent('user-created', 'evt-123');\r\n      expect(correlationId.equals(correlationId2)).toBe(true);\r\n    });\r\n\r\n    it('should throw error for empty event type', () => {\r\n      expect(() => CorrelationId.forEvent('')).toThrow('Event type cannot be empty');\r\n      expect(() => CorrelationId.forEvent('   ')).toThrow('Event type cannot be empty');\r\n    });\r\n  });\r\n\r\n  describe('tracing context', () => {\r\n    it('should get tracing context', () => {\r\n      const correlationId = CorrelationId.fromString('123e4567-e89b-42d3-a456-************');\r\n      const context = correlationId.getTracingContext();\r\n\r\n      expect(context.correlationId).toBe('123e4567-e89b-42d3-a456-************');\r\n      expect(context.shortId).toBe('123e4567');\r\n      expect(context.compactId).toBe('123e4567e89b42d3a456************');\r\n      expect(context.version).toBe(1);\r\n      expect(context.variant).toBe('RFC4122');\r\n    });\r\n\r\n    it('should create HTTP headers', () => {\r\n      const correlationId = CorrelationId.fromString('123e4567-e89b-42d3-a456-************');\r\n      const headers = correlationId.toHeaders();\r\n\r\n      expect(headers['X-Correlation-ID']).toBe('123e4567-e89b-42d3-a456-************');\r\n      expect(headers['X-Correlation-ID-Short']).toBe('123e4567');\r\n    });\r\n\r\n    it('should create HTTP headers with custom header name', () => {\r\n      const correlationId = CorrelationId.fromString('123e4567-e89b-42d3-a456-************');\r\n      const headers = correlationId.toHeaders('X-Request-ID');\r\n\r\n      expect(headers['X-Request-ID']).toBe('123e4567-e89b-42d3-a456-************');\r\n      expect(headers['X-Request-ID-Short']).toBe('123e4567');\r\n    });\r\n\r\n    it('should parse correlation ID from headers', () => {\r\n      const headers = {\r\n        'X-Correlation-ID': '123e4567-e89b-42d3-a456-************'\r\n      };\r\n      const correlationId = CorrelationId.fromHeaders(headers);\r\n\r\n      expect(correlationId).not.toBeNull();\r\n      expect(correlationId!.value).toBe('123e4567-e89b-42d3-a456-************');\r\n    });\r\n\r\n    it('should parse correlation ID from headers with custom header name', () => {\r\n      const headers = {\r\n        'X-Request-ID': '123e4567-e89b-42d3-a456-************'\r\n      };\r\n      const correlationId = CorrelationId.fromHeaders(headers, 'X-Request-ID');\r\n\r\n      expect(correlationId).not.toBeNull();\r\n      expect(correlationId!.value).toBe('123e4567-e89b-42d3-a456-************');\r\n    });\r\n\r\n    it('should handle case-insensitive header names', () => {\r\n      const headers = {\r\n        'x-correlation-id': '123e4567-e89b-42d3-a456-************'\r\n      };\r\n      const correlationId = CorrelationId.fromHeaders(headers);\r\n\r\n      expect(correlationId).not.toBeNull();\r\n      expect(correlationId!.value).toBe('123e4567-e89b-42d3-a456-************');\r\n    });\r\n\r\n    it('should handle array header values', () => {\r\n      const headers = {\r\n        'X-Correlation-ID': ['123e4567-e89b-42d3-a456-************', 'other-value']\r\n      };\r\n      const correlationId = CorrelationId.fromHeaders(headers);\r\n\r\n      expect(correlationId).not.toBeNull();\r\n      expect(correlationId!.value).toBe('123e4567-e89b-42d3-a456-************');\r\n    });\r\n\r\n    it('should return null when header not found', () => {\r\n      const headers = {};\r\n      const correlationId = CorrelationId.fromHeaders(headers);\r\n      expect(correlationId).toBeNull();\r\n    });\r\n\r\n    it('should return null for invalid header value', () => {\r\n      const headers = {\r\n        'X-Correlation-ID': 'invalid-uuid'\r\n      };\r\n      const correlationId = CorrelationId.fromHeaders(headers);\r\n      expect(correlationId).toBeNull();\r\n    });\r\n  });\r\n\r\n  describe('bulk operations', () => {\r\n    it('should generate multiple correlation IDs', () => {\r\n      const correlationIds = CorrelationId.generateMany(5);\r\n\r\n      expect(correlationIds).toHaveLength(5);\r\n      expect(correlationIds.every(id => id.isValid())).toBe(true);\r\n      \r\n      // All should be unique\r\n      const uniqueValues = new Set(correlationIds.map(id => id.value));\r\n      expect(uniqueValues.size).toBe(5);\r\n    });\r\n\r\n    it('should handle zero count for generateMany', () => {\r\n      const correlationIds = CorrelationId.generateMany(0);\r\n      expect(correlationIds).toHaveLength(0);\r\n    });\r\n\r\n    it('should throw error for negative count', () => {\r\n      expect(() => CorrelationId.generateMany(-1)).toThrow('Count must be non-negative');\r\n    });\r\n  });\r\n\r\n  describe('serialization', () => {\r\n    it('should convert to string', () => {\r\n      const uuid = '123e4567-e89b-42d3-a456-************';\r\n      const correlationId = CorrelationId.fromString(uuid);\r\n      expect(correlationId.toString()).toBe(uuid);\r\n    });\r\n\r\n    it('should convert to JSON', () => {\r\n      const uuid = '123e4567-e89b-42d3-a456-************';\r\n      const correlationId = CorrelationId.fromString(uuid);\r\n      const json = correlationId.toJSON();\r\n\r\n      expect(json.value).toBe(uuid);\r\n      expect(json.type).toBe('CorrelationId');\r\n      expect(json.version).toBe(1);\r\n      expect(json.variant).toBe('RFC4122');\r\n      expect(json.shortString).toBe('123e4567');\r\n      expect(json.compactString).toBe('123e4567e89b42d3a456************');\r\n    });\r\n\r\n    it('should create from JSON', () => {\r\n      const uuid = '123e4567-e89b-42d3-a456-************';\r\n      const json = { value: uuid };\r\n      const correlationId = CorrelationId.fromJSON(json);\r\n\r\n      expect(correlationId.value).toBe(uuid);\r\n    });\r\n  });\r\n\r\n  describe('immutability', () => {\r\n    it('should be immutable after creation', () => {\r\n      const correlationId = CorrelationId.generate();\r\n      const originalValue = correlationId.value;\r\n\r\n      // Attempt to modify (should not work due to readonly)\r\n      expect(() => {\r\n        (correlationId as any)._value = 'modified';\r\n      }).not.toThrow(); // TypeScript prevents this, but runtime doesn't throw\r\n\r\n      // Value should remain unchanged\r\n      expect(correlationId.value).toBe(originalValue);\r\n    });\r\n\r\n    it('should be frozen', () => {\r\n      const correlationId = CorrelationId.generate();\r\n      expect(Object.isFrozen(correlationId)).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('edge cases', () => {\r\n    it('should handle case-insensitive UUID comparison', () => {\r\n      const uuid1 = '123e4567-e89b-42d3-a456-************';\r\n      const uuid2 = '123e4567-e89b-42d3-a456-************';\r\n      \r\n      const correlationId1 = CorrelationId.fromString(uuid1);\r\n      const correlationId2 = CorrelationId.fromString(uuid2);\r\n      \r\n      expect(correlationId1.equals(correlationId2)).toBe(true);\r\n    });\r\n\r\n    it('should maintain original case in value', () => {\r\n      const uuid = '123e4567-e89b-42d3-a456-************';\r\n      const correlationId = CorrelationId.fromString(uuid);\r\n      expect(correlationId.value).toBe(uuid);\r\n    });\r\n\r\n    it('should create deterministic correlation ID from seed', () => {\r\n      const correlation1 = CorrelationId.fromSeed('test');\r\n      const correlation2 = CorrelationId.fromSeed('test');\r\n      expect(correlation1.equals(correlation2)).toBe(true);\r\n    });\r\n\r\n    it('should create different correlation IDs from different seeds', () => {\r\n      const correlation1 = CorrelationId.fromSeed('test1');\r\n      const correlation2 = CorrelationId.fromSeed('test2');\r\n      expect(correlation1.equals(correlation2)).toBe(false);\r\n    });\r\n  });\r\n});\r\n"], "version": 3}