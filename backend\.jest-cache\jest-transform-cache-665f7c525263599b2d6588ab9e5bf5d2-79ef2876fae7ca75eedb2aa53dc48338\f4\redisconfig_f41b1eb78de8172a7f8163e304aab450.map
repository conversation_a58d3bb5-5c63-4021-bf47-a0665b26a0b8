{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\redis.config.ts", "mappings": ";;;AAAA,2CAA4C;AA2G5C;;GAEG;AACH,MAAM,QAAQ,GAAG;IACf,GAAG,EAAE,CAAC,KAAyB,EAAE,YAAoB,EAAU,EAAE,CAC/D,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,YAAY;IAE5D,IAAI,EAAE,CAAC,KAAyB,EAAW,EAAE,CAAC,KAAK,KAAK,MAAM;IAE9D,MAAM,EAAE,CAAC,KAAyB,EAAE,YAAoB,EAAU,EAAE,CAClE,KAAK,IAAI,YAAY;IAEvB,cAAc,EAAE,CAAC,KAAyB,EAAsB,EAAE,CAChE,KAAK,IAAI,SAAS;IAEpB,UAAU,EAAE,CAAC,KAAyB,EAAsB,EAAE,CAC5D,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM;CAClC,CAAC;AAEX;;GAEG;AACH,MAAM,UAAU,GAAG,CAAC,QAA4B,EAAe,EAAE;IAC/D,IAAI,CAAC,QAAQ;QAAE,OAAO,EAAE,CAAC;IAEzB,OAAO,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC;SACvB,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;SACxB,MAAM,CAAC,OAAO,CAAC;SACf,GAAG,CAAC,IAAI,CAAC,EAAE;QACV,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACxC,IAAI,CAAC,IAAI;YAAE,MAAM,IAAI,KAAK,CAAC,8BAA8B,IAAI,EAAE,CAAC,CAAC;QAEjE,OAAO;YACL,IAAI;YACJ,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC;SAClC,CAAC;IACJ,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,cAAc,GAAG;IACrB,IAAI,EAAE,IAAI;IACV,EAAE,EAAE,CAAC;IACL,WAAW,EAAE,CAAC;IACd,WAAW,EAAE,IAAI;IACjB,eAAe,EAAE,KAAK;IACtB,eAAe,EAAE,IAAI;IACrB,UAAU,EAAE,KAAK;IACjB,GAAG,EAAE,IAAI;IACT,eAAe,EAAE,KAAK;IACtB,WAAW,EAAE,KAAK;IAClB,mBAAmB,EAAE,IAAI;IACzB,iBAAiB,EAAE,KAAK;IACxB,cAAc,EAAE,GAAG;IACnB,qBAAqB,EAAE,GAAG;IAC1B,iBAAiB,EAAE,EAAE;IACrB,cAAc,EAAE,CAAC;IACjB,mBAAmB,EAAE,IAAI;IACzB,oBAAoB,EAAE,GAAG;IACzB,qBAAqB,EAAE,KAAK;IAC5B,oBAAoB,EAAE,IAAI;IAC1B,kBAAkB,EAAE,KAAK;CACjB,CAAC;AAEX;;;GAGG;AACU,QAAA,WAAW,GAAG,IAAA,mBAAU,EAAC,OAAO,EAAE,GAAgB,EAAE;IAC/D,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;IAExB,OAAO;QACL,OAAO,EAAE;YACP,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,WAAW,CAAC;YACrD,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,cAAc,CAAC,IAAI,CAAC;YAC1D,QAAQ,EAAE,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;YACxD,EAAE,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,cAAc,CAAC,EAAE,CAAC;YACpD,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB,CAAC,EAAE,WAAW,CAAC;SACjE;QAED,UAAU,EAAE;YACV,oBAAoB,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE,cAAc,CAAC,WAAW,CAAC;YACxF,oBAAoB,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE,cAAc,CAAC,WAAW,CAAC;YACxF,cAAc,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,uBAAuB,CAAC,EAAE,cAAc,CAAC,eAAe,CAAC;YAC1F,cAAc,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,uBAAuB,CAAC,EAAE,cAAc,CAAC,eAAe,CAAC;YAC1F,WAAW,EAAE,IAAI;YACjB,SAAS,EAAE,cAAc,CAAC,UAAU;YACpC,MAAM,EAAE,CAAU;SACnB;QAED,KAAK,EAAE;YACL,GAAG,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,cAAc,CAAC,GAAG,CAAC;YACvD,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,uBAAuB,CAAC,EAAE,cAAc,CAAC,eAAe,CAAC;YACpF,WAAW,EAAE,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;YACpD,UAAU,EAAE,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;SACzD;QAED,OAAO,EAAE;YACP,EAAE,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC;YAC5C,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,EAAE,OAAO,CAAC;YAChE,GAAG,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE,cAAc,CAAC,WAAW,CAAC;YACvE,UAAU,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,2BAA2B,CAAC,EAAE,cAAc,CAAC,mBAAmB,CAAC;SAC/F;QAED,SAAS,EAAE;YACT,EAAE,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC;YAC/C,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,yBAAyB,CAAC,EAAE,KAAK,CAAC;YACjE,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,yBAAyB,CAAC,EAAE,cAAc,CAAC,iBAAiB,CAAC;YACxF,GAAG,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,sBAAsB,CAAC,EAAE,cAAc,CAAC,cAAc,CAAC;SAC9E;QAED,KAAK,EAAE;YACL,EAAE,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;YAC1C,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC,EAAE,OAAO,CAAC;YAC9D,iBAAiB,EAAE;gBACjB,gBAAgB,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,gCAAgC,CAAC,EAAE,cAAc,CAAC,qBAAqB,CAAC;gBAC3G,YAAY,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,4BAA4B,CAAC,EAAE,cAAc,CAAC,iBAAiB,CAAC;gBAC/F,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,sBAAsB,CAAC,EAAE,cAAc,CAAC,cAAc,CAAC;gBAClF,OAAO,EAAE;oBACP,IAAI,EAAE,aAAsB;oBAC5B,KAAK,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,2BAA2B,CAAC,EAAE,cAAc,CAAC,mBAAmB,CAAC;iBAC1F;aACF;SACF;QAED,MAAM,EAAE;YACN,EAAE,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YAC3C,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,qBAAqB,CAAC,EAAE,KAAK,CAAC;YAC7D,YAAY,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,4BAA4B,CAAC,EAAE,cAAc,CAAC,oBAAoB,CAAC;SACnG;QAED,OAAO,EAAE;YACP,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;YACpD,KAAK,EAAE,UAAU,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;YAC7C,OAAO,EAAE;gBACP,gBAAgB,EAAE,IAAI;gBACtB,YAAY,EAAE;oBACZ,QAAQ,EAAE,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;iBACzD;gBACD,oBAAoB,EAAE,cAAc,CAAC,WAAW;gBAChD,oBAAoB,EAAE,cAAc,CAAC,WAAW;aACjD;SACF;QAED,QAAQ,EAAE;YACR,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;YACrD,SAAS,EAAE,UAAU,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YAC7C,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,qBAAqB,CAAC,EAAE,UAAU,CAAC;YAC7D,QAAQ,EAAE,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;SAClE;QAED,WAAW,EAAE;YACX,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;YACzD,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,6BAA6B,CAAC,EAAE,cAAc,CAAC,qBAAqB,CAAC;YAChG,OAAO,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,4BAA4B,CAAC,EAAE,cAAc,CAAC,oBAAoB,CAAC;SAC9F;QAED,UAAU,EAAE;YACV,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;YACvD,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;YAC5D,gBAAgB,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,0BAA0B,CAAC,EAAE,cAAc,CAAC,kBAAkB,CAAC;YAClG,mBAAmB,EAAE,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;SACvE;KACF,CAAC;AACJ,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\redis.config.ts"], "sourcesContent": ["import { registerAs } from '@nestjs/config';\r\n\r\n/**\r\n * Redis node configuration for cluster/sentinel setup\r\n */\r\ninterface RedisNode {\r\n  readonly host: string;\r\n  readonly port: number;\r\n}\r\n\r\n/**\r\n * Job backoff configuration for queue processing\r\n */\r\ninterface JobBackoffConfig {\r\n  readonly type: 'exponential' | 'fixed';\r\n  readonly delay: number;\r\n}\r\n\r\n/**\r\n * Default job options for Redis queues\r\n */\r\ninterface DefaultJobOptions {\r\n  readonly removeOnComplete: number;\r\n  readonly removeOnFail: number;\r\n  readonly attempts: number;\r\n  readonly backoff: JobBackoffConfig;\r\n}\r\n\r\n/**\r\n * Comprehensive Redis configuration interface\r\n */\r\nexport interface RedisConfig {\r\n  readonly primary: {\r\n    readonly host: string;\r\n    readonly port: number;\r\n    readonly password?: string | undefined;\r\n    readonly db: number;\r\n    readonly keyPrefix: string;\r\n  };\r\n  readonly connection: {\r\n    readonly maxRetriesPerRequest: number;\r\n    readonly retryDelayOnFailover: number;\r\n    readonly connectTimeout: number;\r\n    readonly commandTimeout: number;\r\n    readonly lazyConnect: boolean;\r\n    readonly keepAlive: number;\r\n    readonly family: 4 | 6;\r\n  };\r\n  readonly cache: {\r\n    readonly ttl: number;\r\n    readonly maxItems: number;\r\n    readonly compression: boolean;\r\n    readonly serializer: 'json' | 'msgpack';\r\n  };\r\n  readonly session: {\r\n    readonly db: number;\r\n    readonly keyPrefix: string;\r\n    readonly ttl: number;\r\n    readonly touchAfter: number;\r\n  };\r\n  readonly rateLimit: {\r\n    readonly db: number;\r\n    readonly keyPrefix: string;\r\n    readonly windowMs: number;\r\n    readonly max: number;\r\n  };\r\n  readonly queue: {\r\n    readonly db: number;\r\n    readonly keyPrefix: string;\r\n    readonly defaultJobOptions: DefaultJobOptions;\r\n  };\r\n  readonly pubsub: {\r\n    readonly db: number;\r\n    readonly keyPrefix: string;\r\n    readonly maxListeners: number;\r\n  };\r\n  readonly cluster: {\r\n    readonly enabled: boolean;\r\n    readonly nodes: ReadonlyArray<RedisNode>;\r\n    readonly options: {\r\n      readonly enableReadyCheck: boolean;\r\n      readonly redisOptions: {\r\n        readonly password?: string | undefined;\r\n      };\r\n      readonly maxRetriesPerRequest: number;\r\n      readonly retryDelayOnFailover: number;\r\n    };\r\n  };\r\n  readonly sentinel: {\r\n    readonly enabled: boolean;\r\n    readonly sentinels: ReadonlyArray<RedisNode>;\r\n    readonly name: string;\r\n    readonly password?: string | undefined;\r\n  };\r\n  readonly healthCheck: {\r\n    readonly enabled: boolean;\r\n    readonly interval: number;\r\n    readonly timeout: number;\r\n  };\r\n  readonly monitoring: {\r\n    readonly enabled: boolean;\r\n    readonly slowLogEnabled: boolean;\r\n    readonly slowLogThreshold: number;\r\n    readonly commandStatsEnabled: boolean;\r\n  };\r\n}\r\n\r\n/**\r\n * Environment variable parsing utilities with improved type safety\r\n */\r\nconst parseEnv = {\r\n  int: (value: string | undefined, defaultValue: number): number => \r\n    value ? parseInt(value, 10) || defaultValue : defaultValue,\r\n  \r\n  bool: (value: string | undefined): boolean => value === 'true',\r\n  \r\n  string: (value: string | undefined, defaultValue: string): string => \r\n    value || defaultValue,\r\n  \r\n  optionalString: (value: string | undefined): string | undefined => \r\n    value || undefined,\r\n  \r\n  serializer: (value: string | undefined): 'json' | 'msgpack' =>\r\n    value === 'msgpack' ? 'msgpack' : 'json',\r\n} as const;\r\n\r\n/**\r\n * Parse Redis nodes from environment string\r\n */\r\nconst parseNodes = (envValue: string | undefined): RedisNode[] => {\r\n  if (!envValue) return [];\r\n  \r\n  return envValue.split(',')\r\n    .map(node => node.trim())\r\n    .filter(Boolean)\r\n    .map(node => {\r\n      const [host, portStr] = node.split(':');\r\n      if (!host) throw new Error(`Invalid Redis node format: ${node}`);\r\n      \r\n      return {\r\n        host,\r\n        port: parseEnv.int(portStr, 6379),\r\n      };\r\n    });\r\n};\r\n\r\n/**\r\n * Redis configuration constants\r\n */\r\nconst REDIS_DEFAULTS = {\r\n  PORT: 6379,\r\n  DB: 0,\r\n  MAX_RETRIES: 3,\r\n  RETRY_DELAY: 1000,\r\n  CONNECT_TIMEOUT: 10000,\r\n  COMMAND_TIMEOUT: 5000,\r\n  KEEP_ALIVE: 30000,\r\n  TTL: 3600,\r\n  CACHE_MAX_ITEMS: 10000,\r\n  SESSION_TTL: 86400,\r\n  SESSION_TOUCH_AFTER: 3600,\r\n  RATE_LIMIT_WINDOW: 60000,\r\n  RATE_LIMIT_MAX: 100,\r\n  QUEUE_REMOVE_COMPLETE: 100,\r\n  QUEUE_REMOVE_FAIL: 50,\r\n  QUEUE_ATTEMPTS: 3,\r\n  QUEUE_BACKOFF_DELAY: 2000,\r\n  PUBSUB_MAX_LISTENERS: 100,\r\n  HEALTH_CHECK_INTERVAL: 30000,\r\n  HEALTH_CHECK_TIMEOUT: 5000,\r\n  SLOW_LOG_THRESHOLD: 10000,\r\n} as const;\r\n\r\n/**\r\n * Redis configuration factory with comprehensive type safety\r\n * Supports single instance, cluster, and sentinel configurations\r\n */\r\nexport const redisConfig = registerAs('redis', (): RedisConfig => {\r\n  const env = process.env;\r\n\r\n  return {\r\n    primary: {\r\n      host: parseEnv.string(env['REDIS_HOST'], 'localhost'),\r\n      port: parseEnv.int(env['REDIS_PORT'], REDIS_DEFAULTS.PORT),\r\n      password: parseEnv.optionalString(env['REDIS_PASSWORD']),\r\n      db: parseEnv.int(env['REDIS_DB'], REDIS_DEFAULTS.DB),\r\n      keyPrefix: parseEnv.string(env['REDIS_KEY_PREFIX'], 'sentinel:'),\r\n    },\r\n\r\n    connection: {\r\n      maxRetriesPerRequest: parseEnv.int(env['REDIS_MAX_RETRIES'], REDIS_DEFAULTS.MAX_RETRIES),\r\n      retryDelayOnFailover: parseEnv.int(env['REDIS_RETRY_DELAY'], REDIS_DEFAULTS.RETRY_DELAY),\r\n      connectTimeout: parseEnv.int(env['REDIS_CONNECT_TIMEOUT'], REDIS_DEFAULTS.CONNECT_TIMEOUT),\r\n      commandTimeout: parseEnv.int(env['REDIS_COMMAND_TIMEOUT'], REDIS_DEFAULTS.COMMAND_TIMEOUT),\r\n      lazyConnect: true,\r\n      keepAlive: REDIS_DEFAULTS.KEEP_ALIVE,\r\n      family: 4 as const,\r\n    },\r\n\r\n    cache: {\r\n      ttl: parseEnv.int(env['REDIS_TTL'], REDIS_DEFAULTS.TTL),\r\n      maxItems: parseEnv.int(env['REDIS_CACHE_MAX_ITEMS'], REDIS_DEFAULTS.CACHE_MAX_ITEMS),\r\n      compression: parseEnv.bool(env['REDIS_COMPRESSION']),\r\n      serializer: parseEnv.serializer(env['REDIS_SERIALIZER']),\r\n    },\r\n\r\n    session: {\r\n      db: parseEnv.int(env['REDIS_SESSION_DB'], 1),\r\n      keyPrefix: parseEnv.string(env['REDIS_SESSION_PREFIX'], 'sess:'),\r\n      ttl: parseEnv.int(env['REDIS_SESSION_TTL'], REDIS_DEFAULTS.SESSION_TTL),\r\n      touchAfter: parseEnv.int(env['REDIS_SESSION_TOUCH_AFTER'], REDIS_DEFAULTS.SESSION_TOUCH_AFTER),\r\n    },\r\n\r\n    rateLimit: {\r\n      db: parseEnv.int(env['REDIS_RATE_LIMIT_DB'], 2),\r\n      keyPrefix: parseEnv.string(env['REDIS_RATE_LIMIT_PREFIX'], 'rl:'),\r\n      windowMs: parseEnv.int(env['REDIS_RATE_LIMIT_WINDOW'], REDIS_DEFAULTS.RATE_LIMIT_WINDOW),\r\n      max: parseEnv.int(env['REDIS_RATE_LIMIT_MAX'], REDIS_DEFAULTS.RATE_LIMIT_MAX),\r\n    },\r\n\r\n    queue: {\r\n      db: parseEnv.int(env['REDIS_QUEUE_DB'], 3),\r\n      keyPrefix: parseEnv.string(env['REDIS_QUEUE_PREFIX'], 'bull:'),\r\n      defaultJobOptions: {\r\n        removeOnComplete: parseEnv.int(env['REDIS_QUEUE_REMOVE_ON_COMPLETE'], REDIS_DEFAULTS.QUEUE_REMOVE_COMPLETE),\r\n        removeOnFail: parseEnv.int(env['REDIS_QUEUE_REMOVE_ON_FAIL'], REDIS_DEFAULTS.QUEUE_REMOVE_FAIL),\r\n        attempts: parseEnv.int(env['REDIS_QUEUE_ATTEMPTS'], REDIS_DEFAULTS.QUEUE_ATTEMPTS),\r\n        backoff: {\r\n          type: 'exponential' as const,\r\n          delay: parseEnv.int(env['REDIS_QUEUE_BACKOFF_DELAY'], REDIS_DEFAULTS.QUEUE_BACKOFF_DELAY),\r\n        },\r\n      },\r\n    },\r\n\r\n    pubsub: {\r\n      db: parseEnv.int(env['REDIS_PUBSUB_DB'], 4),\r\n      keyPrefix: parseEnv.string(env['REDIS_PUBSUB_PREFIX'], 'ps:'),\r\n      maxListeners: parseEnv.int(env['REDIS_PUBSUB_MAX_LISTENERS'], REDIS_DEFAULTS.PUBSUB_MAX_LISTENERS),\r\n    },\r\n\r\n    cluster: {\r\n      enabled: parseEnv.bool(env['REDIS_CLUSTER_ENABLED']),\r\n      nodes: parseNodes(env['REDIS_CLUSTER_NODES']),\r\n      options: {\r\n        enableReadyCheck: true,\r\n        redisOptions: {\r\n          password: parseEnv.optionalString(env['REDIS_PASSWORD']),\r\n        },\r\n        maxRetriesPerRequest: REDIS_DEFAULTS.MAX_RETRIES,\r\n        retryDelayOnFailover: REDIS_DEFAULTS.RETRY_DELAY,\r\n      },\r\n    },\r\n\r\n    sentinel: {\r\n      enabled: parseEnv.bool(env['REDIS_SENTINEL_ENABLED']),\r\n      sentinels: parseNodes(env['REDIS_SENTINELS']),\r\n      name: parseEnv.string(env['REDIS_SENTINEL_NAME'], 'mymaster'),\r\n      password: parseEnv.optionalString(env['REDIS_SENTINEL_PASSWORD']),\r\n    },\r\n\r\n    healthCheck: {\r\n      enabled: parseEnv.bool(env['REDIS_HEALTH_CHECK_ENABLED']),\r\n      interval: parseEnv.int(env['REDIS_HEALTH_CHECK_INTERVAL'], REDIS_DEFAULTS.HEALTH_CHECK_INTERVAL),\r\n      timeout: parseEnv.int(env['REDIS_HEALTH_CHECK_TIMEOUT'], REDIS_DEFAULTS.HEALTH_CHECK_TIMEOUT),\r\n    },\r\n\r\n    monitoring: {\r\n      enabled: parseEnv.bool(env['REDIS_MONITORING_ENABLED']),\r\n      slowLogEnabled: parseEnv.bool(env['REDIS_SLOW_LOG_ENABLED']),\r\n      slowLogThreshold: parseEnv.int(env['REDIS_SLOW_LOG_THRESHOLD'], REDIS_DEFAULTS.SLOW_LOG_THRESHOLD),\r\n      commandStatsEnabled: parseEnv.bool(env['REDIS_COMMAND_STATS_ENABLED']),\r\n    },\r\n  };\r\n});"], "version": 3}