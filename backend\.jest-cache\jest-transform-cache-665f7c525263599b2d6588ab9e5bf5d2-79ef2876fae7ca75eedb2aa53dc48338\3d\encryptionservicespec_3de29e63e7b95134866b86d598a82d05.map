{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\__tests__\\encryption.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,2CAA+C;AAC/C,yEAAoF;AACpF,iEAA6D;AAC7D,iEAA6D;AAC7D,6DAAyD;AAEzD,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;IACjC,IAAI,OAA0B,CAAC;IAC/B,IAAI,aAAyC,CAAC;IAC9C,IAAI,aAAyC,CAAC;IAC9C,IAAI,aAAyC,CAAC;IAC9C,IAAI,WAAqC,CAAC;IAE1C,MAAM,aAAa,GAAG;QACpB,aAAa,EAAE,gBAAgB;QAC/B,EAAE,EAAE,uBAAuB;QAC3B,GAAG,EAAE,UAAU;QACf,SAAS,EAAE,aAAa;KACzB,CAAC;IAEF,MAAM,cAAc,GAAG;QACrB,SAAS,EAAE,uEAAuE;QAClF,UAAU,EAAE,0EAA0E;KACvF,CAAC;IAEF,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,qBAAqB;QACrB,aAAa,GAAG;YACd,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;SACR,CAAC;QAET,qBAAqB;QACrB,aAAa,GAAG;YACd,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,aAAa,CAAC;YACnD,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,gBAAgB,CAAC;YACtD,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,mBAAmB,CAAC;YAC3D,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;SACtC,CAAC;QAET,qBAAqB;QACrB,aAAa,GAAG;YACd,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,cAAc,CAAC;YAC5D,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,oBAAoB,CAAC;YAC1D,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,oBAAoB,CAAC;YAC1D,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,eAAe,CAAC;YAClD,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC;YACzC,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACnE,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,sBAAsB,CAAC;YACrE,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;YACnD,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;SAC5C,CAAC;QAET,mBAAmB;QACnB,WAAW,GAAG;YACZ,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,aAAa,CAAC;YAC9C,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,aAAa,CAAC;YAChD,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,aAAa,CAAC;YAChD,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,aAAa,CAAC;YAC9C,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,aAAa,CAAC;YACxD,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC;YACjD,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,aAAa,CAAC;YAClD,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,aAAa,CAAC;YAC5D,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,UAAU,CAAC;YACvD,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;SACzC,CAAC;QAET,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,sCAAiB;gBACjB;oBACE,OAAO,EAAE,sBAAa;oBACtB,QAAQ,EAAE,aAAa;iBACxB;gBACD;oBACE,OAAO,EAAE,8BAAa;oBACtB,QAAQ,EAAE,aAAa;iBACxB;gBACD;oBACE,OAAO,EAAE,8BAAa;oBACtB,QAAQ,EAAE,aAAa;iBACxB;gBACD;oBACE,OAAO,EAAE,0BAAW;oBACpB,QAAQ,EAAE,WAAW;iBACtB;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAAoB,sCAAiB,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;YAC1B,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;gBAC7C,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;gBAEjE,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;oBACrB,aAAa,EAAE,gBAAgB;oBAC/B,EAAE,EAAE,uBAAuB;oBAC3B,GAAG,EAAE,UAAU;oBACf,SAAS,EAAE,aAAa;iBACzB,CAAC,CAAC;gBACH,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;YAC9E,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;gBACzD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;gBAErD,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;oBACrB,aAAa,EAAE,gBAAgB;oBAC/B,EAAE,EAAE,uBAAuB;oBAC3B,GAAG,EAAE,UAAU;oBACf,SAAS,EAAE,aAAa;iBACzB,CAAC,CAAC;gBACH,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC7E,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;YAC1B,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;gBAC7C,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;gBAE9E,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;oBACrB,aAAa,EAAE,gBAAgB;oBAC/B,SAAS,EAAE,aAAa;iBACzB,CAAC,CAAC;gBACH,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;oBACjD,aAAa,EAAE,gBAAgB;oBAC/B,EAAE,EAAE,IAAI;oBACR,GAAG,EAAE,KAAK;oBACV,GAAG,EAAE,KAAK;iBACX,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;gBACzD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;gBAEvE,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;oBACrB,aAAa,EAAE,gBAAgB;oBAC/B,SAAS,EAAE,aAAa;iBACzB,CAAC,CAAC;gBACH,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;oBACjD,aAAa,EAAE,gBAAgB;oBAC/B,EAAE,EAAE,IAAI;oBACR,GAAG,EAAE,KAAK;oBACV,GAAG,EAAE,SAAS;iBACf,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAC9B,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;gBACjC,MAAM,MAAM,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;gBAExC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBACzC,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACvD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;gBAC5C,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,kBAAkB,EAAE,CAAC;gBAElD,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;gBACvC,MAAM,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;YACxE,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;gBACjE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBAEtD,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;gBACvC,MAAM,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;YACnE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;YAC1B,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;gBAC7C,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;gBAEnE,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBAC1C,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC,WAAW,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;YAC3F,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;gBAC1D,MAAM,OAAO,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;gBACpC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,WAAW,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;gBAE5E,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBAC1C,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC,WAAW,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;YACzF,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;YAC1B,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;gBAC7C,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;gBAEzE,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBAC1C,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,aAAa,EAAE,SAAS,CAAC,CAAC;YACjG,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;gBAC1D,MAAM,OAAO,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;gBACpC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,gBAAgB,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;gBAElF,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBAC1C,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;YAC/F,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;YACvB,EAAE,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;gBAC1C,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;gBAEjE,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;gBACrC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,WAAW,EAAE,aAAa,EAAE,SAAS,CAAC,CAAC;YACzF,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;gBAChE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;gBAE3E,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;gBACrC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,WAAW,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;YACxF,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;YACzB,EAAE,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;gBAC3C,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,SAAS,CAAC,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;gBAE/E,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC1B,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;YACvG,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;gBACjE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,SAAS,CAAC,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;gBAEzF,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC1B,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;YACtG,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;YAC/B,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;gBACnD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;gBAEzE,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;gBAC7C,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,oBAAoB,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;YAC1F,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;YAC/B,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;gBACnD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,aAAa,CAAC,CAAC;gBAElF,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAC5C,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,oBAAoB,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,aAAa,CAAC,CAAC;YACnG,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,QAAQ,CAAC,MAAM,EAAE,GAAG,EAAE;YACpB,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;gBACjD,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAEzC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACnC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,kCAAa,CAAC,MAAM,EAAE,CAAC,CAAC;YAClG,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;gBACnD,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,kCAAa,CAAC,MAAM,CAAC,CAAC;gBAE/D,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACnC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,kCAAa,CAAC,MAAM,EAAE,CAAC,CAAC;YAClG,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;YAC1B,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;gBACxC,MAAM,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;gBAE/C,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACnC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;YAC/D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;YAC1B,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;gBACxC,MAAM,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;gBAE/C,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACnC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;YAC/D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,MAAM,EAAE,GAAG,EAAE;YACpB,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;gBACnD,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;gBAEvD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACnC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,WAAW,EAAE,YAAY,EAAE,EAAE,SAAS,EAAE,kCAAa,CAAC,MAAM,EAAE,CAAC,CAAC;YAChH,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;gBACrD,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,YAAY,EAAE,kCAAa,CAAC,MAAM,CAAC,CAAC;gBAE7E,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACnC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,WAAW,EAAE,YAAY,EAAE,EAAE,SAAS,EAAE,kCAAa,CAAC,MAAM,EAAE,CAAC,CAAC;YAChH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;YAC5B,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;gBACjD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;gBAEtD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACnC,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,oBAAoB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YAC/E,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;gBACjD,MAAM,OAAO,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;gBAC/B,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;gBAE/D,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACnC,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,oBAAoB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAC7E,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAC9B,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;gBACnD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;gBAEhE,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC1B,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,oBAAoB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YAC9E,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;YACtB,EAAE,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;gBAC3C,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;gBAExD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACnC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;YAC5F,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;gBAClE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;gBAEnE,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACnC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;YACjF,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;YACpC,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;gBAClE,MAAM,MAAM,GAAG,OAAO,CAAC,oBAAoB,EAAE,CAAC;gBAE9C,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC7B,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACrC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,qBAAqB;YACvD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;gBACjE,MAAM,MAAM,GAAG,OAAO,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;gBAEhD,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC7B,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACrC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,qBAAqB;YACvD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;YAC5B,EAAE,CAAC,sBAAsB,EAAE,GAAG,EAAE;gBAC9B,MAAM,MAAM,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;gBAEtC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACnC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;YAClE,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;gBACjD,MAAM,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;gBAExC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACnC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;YAClE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAC9B,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;gBACjC,MAAM,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;gBAElD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC1B,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;YACrE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;YACrC,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;gBACzC,MAAM,MAAM,GAAG,OAAO,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;gBAE5D,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC1B,MAAM,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;YAC/E,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;YACpC,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;gBACxC,MAAM,MAAM,GAAG,OAAO,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;gBAE1D,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC1B,MAAM,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;YAC7E,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;YAChC,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,MAAM,MAAM,GAAG,OAAO,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;gBAErD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAChC,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC,oBAAoB,CAAC,WAAW,EAAE,kCAAa,CAAC,MAAM,CAAC,CAAC;YAC/F,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;gBAC3D,MAAM,MAAM,GAAG,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,kCAAa,CAAC,MAAM,CAAC,CAAC;gBAE3E,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAChC,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC,oBAAoB,CAAC,WAAW,EAAE,kCAAa,CAAC,MAAM,CAAC,CAAC;YAC/F,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAC9B,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;gBACvD,MAAM,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC;gBAExE,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC1B,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,oBAAoB,CAAC,WAAW,EAAE,mBAAmB,EAAE,kCAAa,CAAC,MAAM,CAAC,CAAC;YAClH,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,MAAM,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC,WAAW,EAAE,mBAAmB,EAAE,kCAAa,CAAC,MAAM,CAAC,CAAC;gBAE9F,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC1B,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,oBAAoB,CAAC,WAAW,EAAE,mBAAmB,EAAE,kCAAa,CAAC,MAAM,CAAC,CAAC;YAClH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,aAAa,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC,CAAC;YAE5E,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,aAAa,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC,CAAC;YAE5E,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;QACvG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,WAAW,CAAC,YAAY,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC,CAAC;YAEjF,MAAM,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;QAC5F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\__tests__\\encryption.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { EncryptionService, HashAlgorithm } from '../encryption/encryption.service';\r\nimport { AESEncryption } from '../encryption/aes.encryption';\r\nimport { RSAEncryption } from '../encryption/rsa.encryption';\r\nimport { HashService } from '../encryption/hash.service';\r\n\r\ndescribe('EncryptionService', () => {\r\n  let service: EncryptionService;\r\n  let configService: jest.Mocked<ConfigService>;\r\n  let aesEncryption: jest.Mocked<AESEncryption>;\r\n  let rsaEncryption: jest.Mocked<RSAEncryption>;\r\n  let hashService: jest.Mocked<HashService>;\r\n\r\n  const mockAESResult = {\r\n    encryptedData: 'encrypted-data',\r\n    iv: 'initialization-vector',\r\n    tag: 'auth-tag',\r\n    algorithm: 'aes-256-gcm',\r\n  };\r\n\r\n  const mockRSAKeyPair = {\r\n    publicKey: '-----BEGIN PUBLIC KEY-----\\nMOCK_PUBLIC_KEY\\n-----END PUBLIC KEY-----',\r\n    privateKey: '-----BEGIN PRIVATE KEY-----\\nMOCK_PRIVATE_KEY\\n-----END PRIVATE KEY-----',\r\n  };\r\n\r\n  beforeEach(async () => {\r\n    // Mock ConfigService\r\n    configService = {\r\n      get: jest.fn(),\r\n    } as any;\r\n\r\n    // Mock AESEncryption\r\n    aesEncryption = {\r\n      encrypt: jest.fn().mockResolvedValue(mockAESResult),\r\n      decrypt: jest.fn().mockResolvedValue('decrypted-data'),\r\n      generateKey: jest.fn().mockReturnValue('generated-aes-key'),\r\n      validateKey: jest.fn().mockReturnValue(true),\r\n    } as any;\r\n\r\n    // Mock RSAEncryption\r\n    rsaEncryption = {\r\n      generateKeyPair: jest.fn().mockResolvedValue(mockRSAKeyPair),\r\n      encrypt: jest.fn().mockResolvedValue('rsa-encrypted-data'),\r\n      decrypt: jest.fn().mockResolvedValue('rsa-decrypted-data'),\r\n      sign: jest.fn().mockResolvedValue('rsa-signature'),\r\n      verify: jest.fn().mockResolvedValue(true),\r\n      encryptLargeData: jest.fn().mockResolvedValue(['chunk1', 'chunk2']),\r\n      decryptLargeData: jest.fn().mockResolvedValue('large-decrypted-data'),\r\n      validatePrivateKey: jest.fn().mockReturnValue(true),\r\n      validatePublicKey: jest.fn().mockReturnValue(true),\r\n    } as any;\r\n\r\n    // Mock HashService\r\n    hashService = {\r\n      hash: jest.fn().mockReturnValue('hashed-data'),\r\n      sha256: jest.fn().mockReturnValue('sha256-hash'),\r\n      sha512: jest.fn().mockReturnValue('sha512-hash'),\r\n      hmac: jest.fn().mockReturnValue('hmac-result'),\r\n      hashPassword: jest.fn().mockResolvedValue('bcrypt-hash'),\r\n      verifyPassword: jest.fn().mockResolvedValue(true),\r\n      pbkdf2: jest.fn().mockResolvedValue('pbkdf2-hash'),\r\n      generateRandomSalt: jest.fn().mockReturnValue('random-salt'),\r\n      generateChecksum: jest.fn().mockReturnValue('checksum'),\r\n      verifyChecksum: jest.fn().mockReturnValue(true),\r\n    } as any;\r\n\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        EncryptionService,\r\n        {\r\n          provide: ConfigService,\r\n          useValue: configService,\r\n        },\r\n        {\r\n          provide: AESEncryption,\r\n          useValue: aesEncryption,\r\n        },\r\n        {\r\n          provide: RSAEncryption,\r\n          useValue: rsaEncryption,\r\n        },\r\n        {\r\n          provide: HashService,\r\n          useValue: hashService,\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    service = module.get<EncryptionService>(EncryptionService);\r\n  });\r\n\r\n  afterEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('AES Encryption Methods', () => {\r\n    describe('encryptAES', () => {\r\n      it('should encrypt data using AES', async () => {\r\n        const result = await service.encryptAES('test-data', 'test-key');\r\n\r\n        expect(result).toEqual({\r\n          encryptedData: 'encrypted-data',\r\n          iv: 'initialization-vector',\r\n          tag: 'auth-tag',\r\n          algorithm: 'aes-256-gcm',\r\n        });\r\n        expect(aesEncryption.encrypt).toHaveBeenCalledWith('test-data', 'test-key');\r\n      });\r\n\r\n      it('should encrypt data without providing key', async () => {\r\n        const result = await service.encryptAES('test-data');\r\n\r\n        expect(result).toEqual({\r\n          encryptedData: 'encrypted-data',\r\n          iv: 'initialization-vector',\r\n          tag: 'auth-tag',\r\n          algorithm: 'aes-256-gcm',\r\n        });\r\n        expect(aesEncryption.encrypt).toHaveBeenCalledWith('test-data', undefined);\r\n      });\r\n    });\r\n\r\n    describe('decryptAES', () => {\r\n      it('should decrypt data using AES', async () => {\r\n        const result = await service.decryptAES('encrypted-data', 'iv', 'tag', 'key');\r\n\r\n        expect(result).toEqual({\r\n          decryptedData: 'decrypted-data',\r\n          algorithm: 'aes-256-gcm',\r\n        });\r\n        expect(aesEncryption.decrypt).toHaveBeenCalledWith({\r\n          encryptedData: 'encrypted-data',\r\n          iv: 'iv',\r\n          tag: 'tag',\r\n          key: 'key',\r\n        });\r\n      });\r\n\r\n      it('should decrypt data without providing key', async () => {\r\n        const result = await service.decryptAES('encrypted-data', 'iv', 'tag');\r\n\r\n        expect(result).toEqual({\r\n          decryptedData: 'decrypted-data',\r\n          algorithm: 'aes-256-gcm',\r\n        });\r\n        expect(aesEncryption.decrypt).toHaveBeenCalledWith({\r\n          encryptedData: 'encrypted-data',\r\n          iv: 'iv',\r\n          tag: 'tag',\r\n          key: undefined,\r\n        });\r\n      });\r\n    });\r\n\r\n    describe('generateAESKey', () => {\r\n      it('should generate AES key', () => {\r\n        const result = service.generateAESKey();\r\n\r\n        expect(result).toBe('generated-aes-key');\r\n        expect(aesEncryption.generateKey).toHaveBeenCalled();\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('RSA Encryption Methods', () => {\r\n    describe('generateRSAKeyPair', () => {\r\n      it('should generate RSA key pair', async () => {\r\n        const result = await service.generateRSAKeyPair();\r\n\r\n        expect(result).toEqual(mockRSAKeyPair);\r\n        expect(rsaEncryption.generateKeyPair).toHaveBeenCalledWith(undefined);\r\n      });\r\n\r\n      it('should generate RSA key pair with custom key size', async () => {\r\n        const result = await service.generateRSAKeyPair(4096);\r\n\r\n        expect(result).toEqual(mockRSAKeyPair);\r\n        expect(rsaEncryption.generateKeyPair).toHaveBeenCalledWith(4096);\r\n      });\r\n    });\r\n\r\n    describe('encryptRSA', () => {\r\n      it('should encrypt data using RSA', async () => {\r\n        const result = await service.encryptRSA('test-data', 'public-key');\r\n\r\n        expect(result).toBe('rsa-encrypted-data');\r\n        expect(rsaEncryption.encrypt).toHaveBeenCalledWith('test-data', 'public-key', undefined);\r\n      });\r\n\r\n      it('should encrypt data using RSA with options', async () => {\r\n        const options = { padding: 'OAEP' };\r\n        const result = await service.encryptRSA('test-data', 'public-key', options);\r\n\r\n        expect(result).toBe('rsa-encrypted-data');\r\n        expect(rsaEncryption.encrypt).toHaveBeenCalledWith('test-data', 'public-key', options);\r\n      });\r\n    });\r\n\r\n    describe('decryptRSA', () => {\r\n      it('should decrypt data using RSA', async () => {\r\n        const result = await service.decryptRSA('encrypted-data', 'private-key');\r\n\r\n        expect(result).toBe('rsa-decrypted-data');\r\n        expect(rsaEncryption.decrypt).toHaveBeenCalledWith('encrypted-data', 'private-key', undefined);\r\n      });\r\n\r\n      it('should decrypt data using RSA with options', async () => {\r\n        const options = { padding: 'OAEP' };\r\n        const result = await service.decryptRSA('encrypted-data', 'private-key', options);\r\n\r\n        expect(result).toBe('rsa-decrypted-data');\r\n        expect(rsaEncryption.decrypt).toHaveBeenCalledWith('encrypted-data', 'private-key', options);\r\n      });\r\n    });\r\n\r\n    describe('signRSA', () => {\r\n      it('should sign data using RSA', async () => {\r\n        const result = await service.signRSA('test-data', 'private-key');\r\n\r\n        expect(result).toBe('rsa-signature');\r\n        expect(rsaEncryption.sign).toHaveBeenCalledWith('test-data', 'private-key', undefined);\r\n      });\r\n\r\n      it('should sign data using RSA with custom algorithm', async () => {\r\n        const result = await service.signRSA('test-data', 'private-key', 'sha512');\r\n\r\n        expect(result).toBe('rsa-signature');\r\n        expect(rsaEncryption.sign).toHaveBeenCalledWith('test-data', 'private-key', 'sha512');\r\n      });\r\n    });\r\n\r\n    describe('verifyRSA', () => {\r\n      it('should verify RSA signature', async () => {\r\n        const result = await service.verifyRSA('test-data', 'signature', 'public-key');\r\n\r\n        expect(result).toBe(true);\r\n        expect(rsaEncryption.verify).toHaveBeenCalledWith('test-data', 'signature', 'public-key', undefined);\r\n      });\r\n\r\n      it('should verify RSA signature with custom algorithm', async () => {\r\n        const result = await service.verifyRSA('test-data', 'signature', 'public-key', 'sha512');\r\n\r\n        expect(result).toBe(true);\r\n        expect(rsaEncryption.verify).toHaveBeenCalledWith('test-data', 'signature', 'public-key', 'sha512');\r\n      });\r\n    });\r\n\r\n    describe('encryptRSALarge', () => {\r\n      it('should encrypt large data using RSA', async () => {\r\n        const result = await service.encryptRSALarge('large-data', 'public-key');\r\n\r\n        expect(result).toEqual(['chunk1', 'chunk2']);\r\n        expect(rsaEncryption.encryptLargeData).toHaveBeenCalledWith('large-data', 'public-key');\r\n      });\r\n    });\r\n\r\n    describe('decryptRSALarge', () => {\r\n      it('should decrypt large data using RSA', async () => {\r\n        const result = await service.decryptRSALarge(['chunk1', 'chunk2'], 'private-key');\r\n\r\n        expect(result).toBe('large-decrypted-data');\r\n        expect(rsaEncryption.decryptLargeData).toHaveBeenCalledWith(['chunk1', 'chunk2'], 'private-key');\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Hash Methods', () => {\r\n    describe('hash', () => {\r\n      it('should hash data with default algorithm', () => {\r\n        const result = service.hash('test-data');\r\n\r\n        expect(result).toBe('hashed-data');\r\n        expect(hashService.hash).toHaveBeenCalledWith('test-data', { algorithm: HashAlgorithm.SHA256 });\r\n      });\r\n\r\n      it('should hash data with specified algorithm', () => {\r\n        const result = service.hash('test-data', HashAlgorithm.SHA512);\r\n\r\n        expect(result).toBe('hashed-data');\r\n        expect(hashService.hash).toHaveBeenCalledWith('test-data', { algorithm: HashAlgorithm.SHA512 });\r\n      });\r\n    });\r\n\r\n    describe('hashSHA256', () => {\r\n      it('should hash data using SHA-256', () => {\r\n        const result = service.hashSHA256('test-data');\r\n\r\n        expect(result).toBe('sha256-hash');\r\n        expect(hashService.sha256).toHaveBeenCalledWith('test-data');\r\n      });\r\n    });\r\n\r\n    describe('hashSHA512', () => {\r\n      it('should hash data using SHA-512', () => {\r\n        const result = service.hashSHA512('test-data');\r\n\r\n        expect(result).toBe('sha512-hash');\r\n        expect(hashService.sha512).toHaveBeenCalledWith('test-data');\r\n      });\r\n    });\r\n\r\n    describe('hmac', () => {\r\n      it('should create HMAC with default algorithm', () => {\r\n        const result = service.hmac('test-data', 'secret-key');\r\n\r\n        expect(result).toBe('hmac-result');\r\n        expect(hashService.hmac).toHaveBeenCalledWith('test-data', 'secret-key', { algorithm: HashAlgorithm.SHA256 });\r\n      });\r\n\r\n      it('should create HMAC with specified algorithm', () => {\r\n        const result = service.hmac('test-data', 'secret-key', HashAlgorithm.SHA512);\r\n\r\n        expect(result).toBe('hmac-result');\r\n        expect(hashService.hmac).toHaveBeenCalledWith('test-data', 'secret-key', { algorithm: HashAlgorithm.SHA512 });\r\n      });\r\n    });\r\n\r\n    describe('hashPassword', () => {\r\n      it('should hash password using bcrypt', async () => {\r\n        const result = await service.hashPassword('password');\r\n\r\n        expect(result).toBe('bcrypt-hash');\r\n        expect(hashService.hashPassword).toHaveBeenCalledWith('password', undefined);\r\n      });\r\n\r\n      it('should hash password with options', async () => {\r\n        const options = { rounds: 12 };\r\n        const result = await service.hashPassword('password', options);\r\n\r\n        expect(result).toBe('bcrypt-hash');\r\n        expect(hashService.hashPassword).toHaveBeenCalledWith('password', options);\r\n      });\r\n    });\r\n\r\n    describe('verifyPassword', () => {\r\n      it('should verify password against hash', async () => {\r\n        const result = await service.verifyPassword('password', 'hash');\r\n\r\n        expect(result).toBe(true);\r\n        expect(hashService.verifyPassword).toHaveBeenCalledWith('password', 'hash');\r\n      });\r\n    });\r\n\r\n    describe('pbkdf2', () => {\r\n      it('should generate PBKDF2 hash', async () => {\r\n        const result = await service.pbkdf2('password', 'salt');\r\n\r\n        expect(result).toBe('pbkdf2-hash');\r\n        expect(hashService.pbkdf2).toHaveBeenCalledWith('password', 'salt', undefined, undefined);\r\n      });\r\n\r\n      it('should generate PBKDF2 hash with custom parameters', async () => {\r\n        const result = await service.pbkdf2('password', 'salt', 10000, 64);\r\n\r\n        expect(result).toBe('pbkdf2-hash');\r\n        expect(hashService.pbkdf2).toHaveBeenCalledWith('password', 'salt', 10000, 64);\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Utility Methods', () => {\r\n    describe('generateSecureRandom', () => {\r\n      it('should generate secure random string with default length', () => {\r\n        const result = service.generateSecureRandom();\r\n\r\n        expect(result).toBeDefined();\r\n        expect(typeof result).toBe('string');\r\n        expect(result.length).toBe(64); // 32 bytes * 2 (hex)\r\n      });\r\n\r\n      it('should generate secure random string with custom length', () => {\r\n        const result = service.generateSecureRandom(16);\r\n\r\n        expect(result).toBeDefined();\r\n        expect(typeof result).toBe('string');\r\n        expect(result.length).toBe(32); // 16 bytes * 2 (hex)\r\n      });\r\n    });\r\n\r\n    describe('generateSalt', () => {\r\n      it('should generate salt', () => {\r\n        const result = service.generateSalt();\r\n\r\n        expect(result).toBe('random-salt');\r\n        expect(hashService.generateRandomSalt).toHaveBeenCalledWith(32);\r\n      });\r\n\r\n      it('should generate salt with custom length', () => {\r\n        const result = service.generateSalt(16);\r\n\r\n        expect(result).toBe('random-salt');\r\n        expect(hashService.generateRandomSalt).toHaveBeenCalledWith(16);\r\n      });\r\n    });\r\n\r\n    describe('validateAESKey', () => {\r\n      it('should validate AES key', () => {\r\n        const result = service.validateAESKey('test-key');\r\n\r\n        expect(result).toBe(true);\r\n        expect(aesEncryption.validateKey).toHaveBeenCalledWith('test-key');\r\n      });\r\n    });\r\n\r\n    describe('validateRSAPrivateKey', () => {\r\n      it('should validate RSA private key', () => {\r\n        const result = service.validateRSAPrivateKey('private-key');\r\n\r\n        expect(result).toBe(true);\r\n        expect(rsaEncryption.validatePrivateKey).toHaveBeenCalledWith('private-key');\r\n      });\r\n    });\r\n\r\n    describe('validateRSAPublicKey', () => {\r\n      it('should validate RSA public key', () => {\r\n        const result = service.validateRSAPublicKey('public-key');\r\n\r\n        expect(result).toBe(true);\r\n        expect(rsaEncryption.validatePublicKey).toHaveBeenCalledWith('public-key');\r\n      });\r\n    });\r\n\r\n    describe('generateChecksum', () => {\r\n      it('should generate checksum with default algorithm', () => {\r\n        const result = service.generateChecksum('test-data');\r\n\r\n        expect(result).toBe('checksum');\r\n        expect(hashService.generateChecksum).toHaveBeenCalledWith('test-data', HashAlgorithm.SHA256);\r\n      });\r\n\r\n      it('should generate checksum with specified algorithm', () => {\r\n        const result = service.generateChecksum('test-data', HashAlgorithm.SHA512);\r\n\r\n        expect(result).toBe('checksum');\r\n        expect(hashService.generateChecksum).toHaveBeenCalledWith('test-data', HashAlgorithm.SHA512);\r\n      });\r\n    });\r\n\r\n    describe('verifyChecksum', () => {\r\n      it('should verify checksum with default algorithm', () => {\r\n        const result = service.verifyChecksum('test-data', 'expected-checksum');\r\n\r\n        expect(result).toBe(true);\r\n        expect(hashService.verifyChecksum).toHaveBeenCalledWith('test-data', 'expected-checksum', HashAlgorithm.SHA256);\r\n      });\r\n\r\n      it('should verify checksum with specified algorithm', () => {\r\n        const result = service.verifyChecksum('test-data', 'expected-checksum', HashAlgorithm.SHA512);\r\n\r\n        expect(result).toBe(true);\r\n        expect(hashService.verifyChecksum).toHaveBeenCalledWith('test-data', 'expected-checksum', HashAlgorithm.SHA512);\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Error Handling', () => {\r\n    it('should handle AES encryption errors', async () => {\r\n      aesEncryption.encrypt.mockRejectedValue(new Error('AES encryption failed'));\r\n\r\n      await expect(service.encryptAES('test-data')).rejects.toThrow('AES encryption failed');\r\n    });\r\n\r\n    it('should handle RSA encryption errors', async () => {\r\n      rsaEncryption.encrypt.mockRejectedValue(new Error('RSA encryption failed'));\r\n\r\n      await expect(service.encryptRSA('test-data', 'public-key')).rejects.toThrow('RSA encryption failed');\r\n    });\r\n\r\n    it('should handle password hashing errors', async () => {\r\n      hashService.hashPassword.mockRejectedValue(new Error('Password hashing failed'));\r\n\r\n      await expect(service.hashPassword('password')).rejects.toThrow('Password hashing failed');\r\n    });\r\n  });\r\n});"], "version": 3}