74ea8139eb2f9d8bddcb70082f2b8b4a
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const request = __importStar(require("supertest"));
const reporting_module_1 = require("../../reporting.module");
const report_definition_entity_1 = require("../../entities/report-definition.entity");
const auth_guard_1 = require("../../../auth/guards/auth.guard");
const roles_guard_1 = require("../../../auth/guards/roles.guard");
/**
 * Performance and Load Testing
 *
 * Tests system performance under various load conditions including:
 * - Report generation performance under high load
 * - Data processing throughput and latency benchmarks
 * - Cache performance and optimization validation
 * - Database query performance and optimization
 * - API endpoint performance and scalability testing
 */
describe('Performance and Load Testing', () => {
    let app;
    let moduleRef;
    const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        roles: ['compliance_admin'],
    };
    const mockAuthGuard = {
        canActivate: jest.fn(() => true),
    };
    const mockRolesGuard = {
        canActivate: jest.fn(() => true),
    };
    beforeAll(async () => {
        moduleRef = await testing_1.Test.createTestingModule({
            imports: [
                config_1.ConfigModule.forRoot({
                    isGlobal: true,
                    envFilePath: '.env.test',
                }),
                typeorm_1.TypeOrmModule.forRoot({
                    type: 'postgres',
                    host: process.env.TEST_DB_HOST || 'localhost',
                    port: parseInt(process.env.TEST_DB_PORT || '5433'),
                    username: process.env.TEST_DB_USERNAME || 'test',
                    password: process.env.TEST_DB_PASSWORD || 'test',
                    database: process.env.TEST_DB_NAME || 'sentinel_perf_test',
                    entities: [report_definition_entity_1.ReportDefinition],
                    synchronize: true,
                    dropSchema: true,
                    logging: false,
                    // Performance optimizations for testing
                    extra: {
                        max: 20, // Maximum number of connections
                        connectionTimeoutMillis: 2000,
                        idleTimeoutMillis: 30000,
                    },
                }),
                reporting_module_1.ReportingModule,
            ],
        })
            .overrideGuard(auth_guard_1.AuthGuard)
            .useValue(mockAuthGuard)
            .overrideGuard(roles_guard_1.RolesGuard)
            .useValue(mockRolesGuard)
            .compile();
        app = moduleRef.createNestApplication();
        // Add request context middleware
        app.use((req, res, next) => {
            req.user = mockUser;
            next();
        });
        await app.init();
        // Create test data for performance testing
        await createTestData();
    });
    afterAll(async () => {
        await app.close();
        await moduleRef.close();
    });
    async function createTestData() {
        // Create multiple report definitions for testing
        const reportDefinitions = Array.from({ length: 100 }, (_, i) => ({
            name: `Performance Test Report ${i}`,
            description: `Performance test report ${i} for load testing`,
            category: i % 2 === 0 ? 'compliance' : 'audit',
            type: i % 3 === 0 ? 'tabular' : 'chart',
            dataSource: i % 2 === 0 ? 'compliance' : 'audit',
            query: `SELECT * FROM ${i % 2 === 0 ? 'compliance' : 'audit'}_data WHERE id > ${i}`,
            parameters: [
                {
                    name: 'startDate',
                    type: 'date',
                    required: true,
                    defaultValue: null,
                },
            ],
            outputFormats: ['pdf', 'excel'],
        }));
        for (const reportDef of reportDefinitions) {
            await request(app.getHttpServer())
                .post('/api/v1/reporting/report-definitions')
                .send(reportDef);
        }
    }
    describe('API Endpoint Performance', () => {
        it('should handle high-frequency GET requests efficiently', async () => {
            const concurrentRequests = 50;
            const requestsPerBatch = 10;
            const batches = Math.ceil(concurrentRequests / requestsPerBatch);
            const allResponseTimes = [];
            for (let batch = 0; batch < batches; batch++) {
                const batchPromises = Array.from({ length: requestsPerBatch }, async () => {
                    const startTime = Date.now();
                    const response = await request(app.getHttpServer())
                        .get('/api/v1/reporting/report-definitions')
                        .query({ page: 1, limit: 20 });
                    const responseTime = Date.now() - startTime;
                    allResponseTimes.push(responseTime);
                    expect(response.status).toBe(200);
                    return responseTime;
                });
                await Promise.all(batchPromises);
            }
            // Performance assertions
            const averageResponseTime = allResponseTimes.reduce((sum, time) => sum + time, 0) / allResponseTimes.length;
            const maxResponseTime = Math.max(...allResponseTimes);
            const p95ResponseTime = allResponseTimes.sort((a, b) => a - b)[Math.floor(allResponseTimes.length * 0.95)];
            console.log(`Performance Metrics for GET /report-definitions:`);
            console.log(`  Average Response Time: ${averageResponseTime.toFixed(2)}ms`);
            console.log(`  Max Response Time: ${maxResponseTime}ms`);
            console.log(`  95th Percentile: ${p95ResponseTime}ms`);
            expect(averageResponseTime).toBeLessThan(1000); // Average should be under 1 second
            expect(p95ResponseTime).toBeLessThan(2000); // 95th percentile should be under 2 seconds
            expect(maxResponseTime).toBeLessThan(5000); // Max should be under 5 seconds
        });
        it('should handle concurrent POST requests efficiently', async () => {
            const concurrentRequests = 20;
            const responseTimes = [];
            const createPromises = Array.from({ length: concurrentRequests }, async (_, i) => {
                const reportDto = {
                    name: `Concurrent Test Report ${i}_${Date.now()}`,
                    description: `Concurrent test report ${i}`,
                    category: 'compliance',
                    type: 'tabular',
                    dataSource: 'compliance',
                    query: `SELECT * FROM compliance_data WHERE id > ${i}`,
                    parameters: [],
                    outputFormats: ['pdf'],
                };
                const startTime = Date.now();
                const response = await request(app.getHttpServer())
                    .post('/api/v1/reporting/report-definitions')
                    .send(reportDto);
                const responseTime = Date.now() - startTime;
                responseTimes.push(responseTime);
                expect(response.status).toBe(201);
                return responseTime;
            });
            await Promise.all(createPromises);
            const averageResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
            const maxResponseTime = Math.max(...responseTimes);
            console.log(`Performance Metrics for POST /report-definitions:`);
            console.log(`  Average Response Time: ${averageResponseTime.toFixed(2)}ms`);
            console.log(`  Max Response Time: ${maxResponseTime}ms`);
            expect(averageResponseTime).toBeLessThan(2000); // Average should be under 2 seconds
            expect(maxResponseTime).toBeLessThan(5000); // Max should be under 5 seconds
        });
        it('should handle large pagination requests efficiently', async () => {
            const pageSizes = [10, 50, 100];
            const performanceResults = {};
            for (const pageSize of pageSizes) {
                const startTime = Date.now();
                const response = await request(app.getHttpServer())
                    .get('/api/v1/reporting/report-definitions')
                    .query({ page: 1, limit: pageSize });
                const responseTime = Date.now() - startTime;
                performanceResults[pageSize] = responseTime;
                expect(response.status).toBe(200);
                expect(response.body.reportDefinitions.length).toBeLessThanOrEqual(pageSize);
            }
            console.log('Pagination Performance:');
            Object.entries(performanceResults).forEach(([size, time]) => {
                console.log(`  Page Size ${size}: ${time}ms`);
            });
            // Performance should scale reasonably with page size
            expect(performanceResults[100]).toBeLessThan(performanceResults[10] * 5);
        });
    });
    describe('Database Query Performance', () => {
        it('should perform efficient search queries', async () => {
            const searchTerms = ['Performance', 'Test', 'Report', 'Compliance', 'Audit'];
            const searchResults = {};
            for (const term of searchTerms) {
                const startTime = Date.now();
                const response = await request(app.getHttpServer())
                    .get('/api/v1/reporting/report-definitions')
                    .query({
                    search: term,
                    page: 1,
                    limit: 50,
                });
                const queryTime = Date.now() - startTime;
                searchResults[term] = queryTime;
                expect(response.status).toBe(200);
                expect(response.body.reportDefinitions.length).toBeGreaterThan(0);
            }
            console.log('Search Query Performance:');
            Object.entries(searchResults).forEach(([term, time]) => {
                console.log(`  Search "${term}": ${time}ms`);
            });
            // All search queries should complete within reasonable time
            Object.values(searchResults).forEach(time => {
                expect(time).toBeLessThan(1500);
            });
        });
        it('should handle complex filtering efficiently', async () => {
            const filterCombinations = [
                { category: 'compliance' },
                { type: 'tabular' },
                { category: 'compliance', type: 'tabular' },
                { category: 'audit', type: 'chart' },
                { search: 'Performance', category: 'compliance' },
            ];
            const filterResults = {};
            for (const [index, filters] of filterCombinations.entries()) {
                const startTime = Date.now();
                const response = await request(app.getHttpServer())
                    .get('/api/v1/reporting/report-definitions')
                    .query({
                    ...filters,
                    page: 1,
                    limit: 20,
                });
                const queryTime = Date.now() - startTime;
                filterResults[`Filter ${index + 1}`] = queryTime;
                expect(response.status).toBe(200);
            }
            console.log('Filter Query Performance:');
            Object.entries(filterResults).forEach(([filter, time]) => {
                console.log(`  ${filter}: ${time}ms`);
            });
            // Complex filters should still perform well
            Object.values(filterResults).forEach(time => {
                expect(time).toBeLessThan(2000);
            });
        });
    });
    describe('Report Generation Performance', () => {
        let reportDefinitionId;
        beforeAll(async () => {
            // Create a report definition for generation testing
            const response = await request(app.getHttpServer())
                .post('/api/v1/reporting/report-definitions')
                .send({
                name: 'Performance Generation Test Report',
                description: 'Report for testing generation performance',
                category: 'compliance',
                type: 'tabular',
                dataSource: 'compliance',
                query: 'SELECT * FROM compliance_data LIMIT 1000',
                parameters: [],
                outputFormats: ['pdf', 'excel', 'csv'],
            });
            reportDefinitionId = response.body.id;
        });
        it('should queue multiple report generation requests efficiently', async () => {
            const concurrentGenerations = 10;
            const generationTimes = [];
            const generationPromises = Array.from({ length: concurrentGenerations }, async (_, i) => {
                const generateDto = {
                    reportDefinitionId,
                    parameters: {},
                    formats: ['pdf'],
                    priority: 'medium',
                };
                const startTime = Date.now();
                const response = await request(app.getHttpServer())
                    .post('/api/v1/reporting/report-generation/generate')
                    .send(generateDto);
                const queueTime = Date.now() - startTime;
                generationTimes.push(queueTime);
                expect(response.status).toBe(202);
                expect(response.body.status).toBe('queued');
                return response.body.reportId;
            });
            const reportIds = await Promise.all(generationPromises);
            const averageQueueTime = generationTimes.reduce((sum, time) => sum + time, 0) / generationTimes.length;
            const maxQueueTime = Math.max(...generationTimes);
            console.log(`Report Generation Queue Performance:`);
            console.log(`  Average Queue Time: ${averageQueueTime.toFixed(2)}ms`);
            console.log(`  Max Queue Time: ${maxQueueTime}ms`);
            console.log(`  Reports Queued: ${reportIds.length}`);
            expect(averageQueueTime).toBeLessThan(1000); // Should queue quickly
            expect(maxQueueTime).toBeLessThan(3000);
        });
        it('should handle status check requests efficiently', async () => {
            // Generate a report first
            const generateResponse = await request(app.getHttpServer())
                .post('/api/v1/reporting/report-generation/generate')
                .send({
                reportDefinitionId,
                parameters: {},
                formats: ['pdf'],
                priority: 'high',
            });
            const reportId = generateResponse.body.reportId;
            // Test multiple status checks
            const statusCheckTimes = [];
            const numberOfChecks = 20;
            for (let i = 0; i < numberOfChecks; i++) {
                const startTime = Date.now();
                const response = await request(app.getHttpServer())
                    .get(`/api/v1/reporting/report-generation/status/${reportId}`);
                const checkTime = Date.now() - startTime;
                statusCheckTimes.push(checkTime);
                expect(response.status).toBe(200);
                // Small delay between checks
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            const averageCheckTime = statusCheckTimes.reduce((sum, time) => sum + time, 0) / statusCheckTimes.length;
            const maxCheckTime = Math.max(...statusCheckTimes);
            console.log(`Status Check Performance:`);
            console.log(`  Average Check Time: ${averageCheckTime.toFixed(2)}ms`);
            console.log(`  Max Check Time: ${maxCheckTime}ms`);
            expect(averageCheckTime).toBeLessThan(500); // Status checks should be fast
            expect(maxCheckTime).toBeLessThan(1000);
        });
    });
    describe('Data Aggregation Performance', () => {
        it('should handle compliance metrics aggregation efficiently', async () => {
            const aggregationSizes = [
                { frameworks: ['sox'], timeRange: '7d' },
                { frameworks: ['sox', 'gdpr'], timeRange: '30d' },
                { frameworks: ['sox', 'gdpr', 'iso27001'], timeRange: '90d' },
            ];
            const aggregationResults = {};
            for (const [index, config] of aggregationSizes.entries()) {
                const startTime = Date.now();
                const response = await request(app.getHttpServer())
                    .get('/api/v1/reporting/data-aggregation/compliance/metrics')
                    .query({
                    frameworks: config.frameworks.join(','),
                    timeRange: config.timeRange,
                    includeHistorical: true,
                });
                const aggregationTime = Date.now() - startTime;
                aggregationResults[`Config ${index + 1}`] = aggregationTime;
                expect(response.status).toBe(200);
                expect(response.body.frameworkMetrics).toBeDefined();
            }
            console.log('Compliance Metrics Aggregation Performance:');
            Object.entries(aggregationResults).forEach(([config, time]) => {
                console.log(`  ${config}: ${time}ms`);
            });
            // Aggregation should complete within reasonable time
            Object.values(aggregationResults).forEach(time => {
                expect(time).toBeLessThan(5000);
            });
        });
        it('should handle audit analytics processing efficiently', async () => {
            const analyticsConfigs = [
                { timeRange: '1d', includeAnomalyDetection: false },
                { timeRange: '7d', includeAnomalyDetection: true },
                { timeRange: '30d', includeAnomalyDetection: true, includePatternRecognition: true },
            ];
            const analyticsResults = {};
            for (const [index, config] of analyticsConfigs.entries()) {
                const startTime = Date.now();
                const response = await request(app.getHttpServer())
                    .get('/api/v1/reporting/data-aggregation/audit/analytics')
                    .query(config);
                const analyticsTime = Date.now() - startTime;
                analyticsResults[`Analytics ${index + 1}`] = analyticsTime;
                expect(response.status).toBe(200);
                expect(response.body.summary).toBeDefined();
            }
            console.log('Audit Analytics Performance:');
            Object.entries(analyticsResults).forEach(([config, time]) => {
                console.log(`  ${config}: ${time}ms`);
            });
            // Analytics should complete within reasonable time
            Object.values(analyticsResults).forEach(time => {
                expect(time).toBeLessThan(10000);
            });
        });
    });
    describe('Memory and Resource Usage', () => {
        it('should maintain stable memory usage under load', async () => {
            const initialMemory = process.memoryUsage();
            // Perform intensive operations
            const operations = Array.from({ length: 100 }, async (_, i) => {
                return request(app.getHttpServer())
                    .get('/api/v1/reporting/report-definitions')
                    .query({ page: Math.floor(i / 10) + 1, limit: 20 });
            });
            await Promise.all(operations);
            // Force garbage collection if available
            if (global.gc) {
                global.gc();
            }
            const finalMemory = process.memoryUsage();
            const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
            const memoryIncreasePercent = (memoryIncrease / initialMemory.heapUsed) * 100;
            console.log('Memory Usage:');
            console.log(`  Initial Heap: ${(initialMemory.heapUsed / 1024 / 1024).toFixed(2)} MB`);
            console.log(`  Final Heap: ${(finalMemory.heapUsed / 1024 / 1024).toFixed(2)} MB`);
            console.log(`  Increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)} MB (${memoryIncreasePercent.toFixed(2)}%)`);
            // Memory increase should be reasonable
            expect(memoryIncreasePercent).toBeLessThan(50); // Less than 50% increase
        });
        it('should handle connection pool efficiently', async () => {
            const concurrentConnections = 30; // More than typical pool size
            const connectionPromises = Array.from({ length: concurrentConnections }, async (_, i) => {
                return request(app.getHttpServer())
                    .get('/api/v1/reporting/report-definitions')
                    .query({ page: 1, limit: 5 });
            });
            const startTime = Date.now();
            const responses = await Promise.all(connectionPromises);
            const totalTime = Date.now() - startTime;
            console.log(`Connection Pool Performance:`);
            console.log(`  Concurrent Connections: ${concurrentConnections}`);
            console.log(`  Total Time: ${totalTime}ms`);
            console.log(`  Average Time per Request: ${(totalTime / concurrentConnections).toFixed(2)}ms`);
            // All requests should succeed
            responses.forEach(response => {
                expect(response.status).toBe(200);
            });
            // Should handle concurrent connections efficiently
            expect(totalTime).toBeLessThan(10000); // Should complete within 10 seconds
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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