b56a48039b738b9f4f62a2356f6c761c
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const base_domain_event_1 = require("../../domain/base-domain-event");
const unique_entity_id_value_object_1 = require("../../value-objects/unique-entity-id.value-object");
class TestDomainEvent extends base_domain_event_1.BaseDomainEvent {
    constructor(aggregateId, data, options) {
        super(aggregateId, data, options);
    }
    get message() {
        return this.eventData.message;
    }
    get value() {
        return this.eventData.value;
    }
}
class AnotherTestEvent extends base_domain_event_1.BaseDomainEvent {
    constructor(aggregateId, status) {
        super(aggregateId, { status });
    }
    get status() {
        return this.eventData.status;
    }
}
describe('BaseDomainEvent', () => {
    let aggregateId;
    let eventData;
    beforeEach(() => {
        aggregateId = unique_entity_id_value_object_1.UniqueEntityId.generate();
        eventData = { message: 'Test message', value: 42 };
    });
    describe('construction', () => {
        it('should create event with required parameters', () => {
            const event = new TestDomainEvent(aggregateId, eventData);
            expect(event.aggregateId.equals(aggregateId)).toBe(true);
            expect(event.eventData).toEqual(eventData);
            expect(event.message).toBe('Test message');
            expect(event.value).toBe(42);
        });
        it('should generate event ID if not provided', () => {
            const event = new TestDomainEvent(aggregateId, eventData);
            expect(event.eventId).toBeInstanceOf(unique_entity_id_value_object_1.UniqueEntityId);
            expect(event.eventId.value).toBeDefined();
        });
        it('should use provided event ID', () => {
            const eventId = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const event = new TestDomainEvent(aggregateId, eventData, { eventId });
            expect(event.eventId.equals(eventId)).toBe(true);
        });
        it('should set occurred time to now if not provided', () => {
            const beforeCreation = new Date();
            const event = new TestDomainEvent(aggregateId, eventData);
            const afterCreation = new Date();
            expect(event.occurredOn.getTime()).toBeGreaterThanOrEqual(beforeCreation.getTime());
            expect(event.occurredOn.getTime()).toBeLessThanOrEqual(afterCreation.getTime());
        });
        it('should use provided occurred time', () => {
            const occurredOn = new Date('2023-01-01T00:00:00Z');
            const event = new TestDomainEvent(aggregateId, eventData, { occurredOn });
            expect(event.occurredOn).toEqual(occurredOn);
        });
        it('should default event version to 1', () => {
            const event = new TestDomainEvent(aggregateId, eventData);
            expect(event.eventVersion).toBe(1);
        });
        it('should use provided event version', () => {
            const event = new TestDomainEvent(aggregateId, eventData, { eventVersion: 2 });
            expect(event.eventVersion).toBe(2);
        });
        it('should set correlation and causation IDs if provided', () => {
            const correlationId = 'correlation-123';
            const causationId = 'causation-456';
            const event = new TestDomainEvent(aggregateId, eventData, {
                correlationId,
                causationId
            });
            expect(event.correlationId).toBe(correlationId);
            expect(event.causationId).toBe(causationId);
        });
        it('should set metadata if provided', () => {
            const metadata = { source: 'test', priority: 'high' };
            const event = new TestDomainEvent(aggregateId, eventData, { metadata });
            expect(event.metadata).toEqual(metadata);
        });
        it('should freeze event data for immutability', () => {
            const event = new TestDomainEvent(aggregateId, eventData);
            expect(Object.isFrozen(event.eventData)).toBe(true);
        });
        it('should freeze metadata for immutability', () => {
            const metadata = { source: 'test' };
            const event = new TestDomainEvent(aggregateId, eventData, { metadata });
            // The internal metadata should be frozen (we can't test it directly)
            // But we can test that the metadata getter returns a copy that doesn't affect the original
            const metadataCopy = event.metadata;
            metadataCopy.source = 'modified';
            // The original event's metadata should remain unchanged
            expect(event.metadata.source).toBe('test');
        });
    });
    describe('event properties', () => {
        it('should provide event type name', () => {
            const event = new TestDomainEvent(aggregateId, eventData);
            expect(event.eventType).toBe('TestDomainEvent');
        });
        it('should start as not dispatched', () => {
            const event = new TestDomainEvent(aggregateId, eventData);
            expect(event.isDispatched).toBe(false);
            expect(event.dispatchedAt).toBeUndefined();
        });
        it('should provide empty metadata by default', () => {
            const event = new TestDomainEvent(aggregateId, eventData);
            expect(event.metadata).toEqual({});
        });
        it('should return copy of metadata', () => {
            const metadata = { source: 'test' };
            const event = new TestDomainEvent(aggregateId, eventData, { metadata });
            const retrievedMetadata = event.metadata;
            expect(retrievedMetadata).toEqual(metadata);
            expect(retrievedMetadata).not.toBe(metadata); // Different reference
        });
    });
    describe('dispatch management', () => {
        it('should mark event as dispatched', () => {
            const event = new TestDomainEvent(aggregateId, eventData);
            expect(event.isDispatched).toBe(false);
            const beforeDispatch = new Date();
            event.markAsDispatched();
            const afterDispatch = new Date();
            expect(event.isDispatched).toBe(true);
            expect(event.dispatchedAt).toBeDefined();
            expect(event.dispatchedAt.getTime()).toBeGreaterThanOrEqual(beforeDispatch.getTime());
            expect(event.dispatchedAt.getTime()).toBeLessThanOrEqual(afterDispatch.getTime());
        });
        it('should throw error when marking already dispatched event', () => {
            const event = new TestDomainEvent(aggregateId, eventData);
            event.markAsDispatched();
            expect(() => event.markAsDispatched()).toThrow(`Event ${event.eventId.toString()} has already been dispatched`);
        });
    });
    describe('age calculations', () => {
        it('should calculate age in milliseconds', () => {
            const pastTime = new Date(Date.now() - 5000); // 5 seconds ago
            const event = new TestDomainEvent(aggregateId, eventData, { occurredOn: pastTime });
            const age = event.getAge();
            expect(age).toBeGreaterThanOrEqual(4900); // Allow some tolerance
            expect(age).toBeLessThanOrEqual(5100);
        });
        it('should calculate age in seconds', () => {
            const pastTime = new Date(Date.now() - 5000); // 5 seconds ago
            const event = new TestDomainEvent(aggregateId, eventData, { occurredOn: pastTime });
            const ageInSeconds = event.getAgeInSeconds();
            expect(ageInSeconds).toBeGreaterThanOrEqual(4);
            expect(ageInSeconds).toBeLessThanOrEqual(5);
        });
        it('should calculate age in minutes', () => {
            const pastTime = new Date(Date.now() - 150000); // 2.5 minutes ago
            const event = new TestDomainEvent(aggregateId, eventData, { occurredOn: pastTime });
            const ageInMinutes = event.getAgeInMinutes();
            expect(ageInMinutes).toBe(2);
        });
        it('should check if event is stale', () => {
            const recentTime = new Date(Date.now() - 1000); // 1 second ago
            const oldTime = new Date(Date.now() - 10000); // 10 seconds ago
            const recentEvent = new TestDomainEvent(aggregateId, eventData, { occurredOn: recentTime });
            const oldEvent = new TestDomainEvent(aggregateId, eventData, { occurredOn: oldTime });
            expect(recentEvent.isStale(5000)).toBe(false); // Not stale (< 5 seconds)
            expect(oldEvent.isStale(5000)).toBe(true); // Stale (> 5 seconds)
        });
    });
    describe('event modification', () => {
        it('should create event with additional metadata', () => {
            const event = new TestDomainEvent(aggregateId, eventData, {
                metadata: { source: 'original' }
            });
            const modifiedEvent = event.withMetadata({ priority: 'high', source: 'modified' });
            expect(modifiedEvent).not.toBe(event);
            expect(modifiedEvent.metadata).toEqual({
                source: 'modified', // Should override
                priority: 'high'
            });
            expect(modifiedEvent.eventId.equals(event.eventId)).toBe(true);
            expect(modifiedEvent.aggregateId.equals(event.aggregateId)).toBe(true);
        });
        it('should create event with correlation ID', () => {
            const event = new TestDomainEvent(aggregateId, eventData);
            const correlationId = 'new-correlation-id';
            const modifiedEvent = event.withCorrelationId(correlationId);
            expect(modifiedEvent).not.toBe(event);
            expect(modifiedEvent.correlationId).toBe(correlationId);
            expect(modifiedEvent.eventId.equals(event.eventId)).toBe(true);
        });
        it('should create event with causation ID', () => {
            const event = new TestDomainEvent(aggregateId, eventData);
            const causationId = 'new-causation-id';
            const modifiedEvent = event.withCausationId(causationId);
            expect(modifiedEvent).not.toBe(event);
            expect(modifiedEvent.causationId).toBe(causationId);
            expect(modifiedEvent.eventId.equals(event.eventId)).toBe(true);
        });
    });
    describe('equality comparison', () => {
        it('should be equal to itself', () => {
            const event = new TestDomainEvent(aggregateId, eventData);
            expect(event.equals(event)).toBe(true);
        });
        it('should be equal to event with same event ID', () => {
            const eventId = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const event1 = new TestDomainEvent(aggregateId, eventData, { eventId });
            const event2 = new TestDomainEvent(aggregateId, eventData, { eventId });
            expect(event1.equals(event2)).toBe(true);
        });
        it('should not be equal to event with different event ID', () => {
            const event1 = new TestDomainEvent(aggregateId, eventData);
            const event2 = new TestDomainEvent(aggregateId, eventData);
            expect(event1.equals(event2)).toBe(false);
        });
        it('should not be equal to null or undefined', () => {
            const event = new TestDomainEvent(aggregateId, eventData);
            expect(event.equals(null)).toBe(false);
            expect(event.equals(undefined)).toBe(false);
        });
    });
    describe('string representation', () => {
        it('should provide string representation', () => {
            const event = new TestDomainEvent(aggregateId, eventData);
            const stringRep = event.toString();
            expect(stringRep).toBe(`TestDomainEvent(${event.eventId.toString()})`);
        });
    });
    describe('JSON serialization', () => {
        it('should serialize to JSON', () => {
            const occurredOn = new Date('2023-01-01T00:00:00Z');
            const correlationId = 'correlation-123';
            const causationId = 'causation-456';
            const metadata = { source: 'test' };
            const event = new TestDomainEvent(aggregateId, eventData, {
                occurredOn,
                correlationId,
                causationId,
                metadata,
                eventVersion: 2
            });
            const json = event.toJSON();
            expect(json).toEqual({
                eventId: event.eventId.toString(),
                eventType: 'TestDomainEvent',
                occurredOn: occurredOn.toISOString(),
                aggregateId: aggregateId.toString(),
                eventVersion: 2,
                eventData: eventData,
                isDispatched: false,
                dispatchedAt: undefined,
                correlationId,
                causationId,
                metadata
            });
        });
        it('should include dispatch information in JSON', () => {
            const event = new TestDomainEvent(aggregateId, eventData);
            event.markAsDispatched();
            const json = event.toJSON();
            expect(json.isDispatched).toBe(true);
            expect(json.dispatchedAt).toBeDefined();
        });
        it('should deserialize from JSON', () => {
            const originalEvent = new TestDomainEvent(aggregateId, eventData, {
                correlationId: 'correlation-123',
                metadata: { source: 'test' }
            });
            const json = originalEvent.toJSON();
            const deserializedEvent = base_domain_event_1.BaseDomainEvent.fromJSON(json, TestDomainEvent);
            expect(deserializedEvent.eventId.equals(originalEvent.eventId)).toBe(true);
            expect(deserializedEvent.aggregateId.equals(originalEvent.aggregateId)).toBe(true);
            expect(deserializedEvent.eventData).toEqual(originalEvent.eventData);
            expect(deserializedEvent.correlationId).toBe(originalEvent.correlationId);
            expect(deserializedEvent.metadata).toEqual(originalEvent.metadata);
        });
    });
    describe('immutability', () => {
        it('should not allow modification of event data', () => {
            const event = new TestDomainEvent(aggregateId, eventData);
            expect(() => {
                event.eventData.message = 'modified';
            }).toThrow();
        });
        it('should not allow modification of metadata', () => {
            const metadata = { source: 'test' };
            const event = new TestDomainEvent(aggregateId, eventData, { metadata });
            // The metadata getter returns a copy, so modifying it won't affect the original
            const metadataCopy = event.metadata;
            expect(() => {
                metadataCopy.source = 'modified';
            }).not.toThrow();
            // But the original event's metadata should remain unchanged
            expect(event.metadata.source).toBe('test');
        });
        it('should not allow modification of core properties', () => {
            const event = new TestDomainEvent(aggregateId, eventData);
            expect(() => {
                event.eventId = unique_entity_id_value_object_1.UniqueEntityId.generate();
            }).toThrow();
            expect(() => {
                event.aggregateId = unique_entity_id_value_object_1.UniqueEntityId.generate();
            }).toThrow();
            expect(() => {
                event.occurredOn = new Date();
            }).toThrow();
        });
    });
    describe('inheritance', () => {
        it('should support inheritance with different event data types', () => {
            const testEvent = new TestDomainEvent(aggregateId, eventData);
            const anotherEvent = new AnotherTestEvent(aggregateId, 'active');
            expect(testEvent).toBeInstanceOf(TestDomainEvent);
            expect(testEvent).toBeInstanceOf(base_domain_event_1.BaseDomainEvent);
            expect(anotherEvent).toBeInstanceOf(AnotherTestEvent);
            expect(anotherEvent).toBeInstanceOf(base_domain_event_1.BaseDomainEvent);
            expect(testEvent.eventType).toBe('TestDomainEvent');
            expect(anotherEvent.eventType).toBe('AnotherTestEvent');
        });
        it('should maintain type safety for event data', () => {
            const testEvent = new TestDomainEvent(aggregateId, eventData);
            const anotherEvent = new AnotherTestEvent(aggregateId, 'active');
            expect(testEvent.message).toBe('Test message');
            expect(testEvent.value).toBe(42);
            expect(anotherEvent.status).toBe('active');
        });
    });
    describe('edge cases', () => {
        it('should handle empty event data', () => {
            class EmptyDataEvent extends base_domain_event_1.BaseDomainEvent {
                constructor(aggregateId) {
                    super(aggregateId, {});
                }
            }
            const event = new EmptyDataEvent(aggregateId);
            expect(event.eventData).toEqual({});
            expect(event.eventType).toBe('EmptyDataEvent');
        });
        it('should handle null values in event data', () => {
            class NullableEvent extends base_domain_event_1.BaseDomainEvent {
                constructor(aggregateId, data) {
                    super(aggregateId, data);
                }
            }
            const event = new NullableEvent(aggregateId, { value: null });
            expect(event.eventData.value).toBeNull();
            expect(event.eventData.optional).toBeUndefined();
        });
        it('should handle complex nested event data', () => {
            class ComplexEvent extends base_domain_event_1.BaseDomainEvent {
                constructor(aggregateId, data) {
                    super(aggregateId, data);
                }
            }
            const complexData = {
                user: {
                    id: 'user-123',
                    profile: {
                        name: 'John Doe',
                        settings: { theme: 'dark', notifications: true }
                    }
                },
                actions: ['login', 'view_dashboard']
            };
            const event = new ComplexEvent(aggregateId, complexData);
            expect(event.eventData).toEqual(complexData);
            expect(Object.isFrozen(event.eventData)).toBe(true);
        });
        it('should handle very old events', () => {
            const veryOldTime = new Date('1970-01-01T00:00:00Z');
            const event = new TestDomainEvent(aggregateId, eventData, { occurredOn: veryOldTime });
            expect(event.getAge()).toBeGreaterThan(1000000000); // Very large number
            expect(event.isStale(1000)).toBe(true);
        });
        it('should handle future events (clock skew)', () => {
            const futureTime = new Date(Date.now() + 1000); // 1 second in future
            const event = new TestDomainEvent(aggregateId, eventData, { occurredOn: futureTime });
            expect(event.getAge()).toBeLessThan(0); // Negative age
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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