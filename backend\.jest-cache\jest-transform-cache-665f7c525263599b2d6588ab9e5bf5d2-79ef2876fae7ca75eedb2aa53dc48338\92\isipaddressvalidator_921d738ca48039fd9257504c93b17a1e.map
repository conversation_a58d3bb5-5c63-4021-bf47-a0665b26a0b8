{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\validation\\validators\\is-ip-address.validator.ts", "mappings": ";;;;;;;;;AAsNA,kCAaC;AAQD,sCAQC;AAQD,sCAQC;AAOD,8CAYC;AAQD,4CA0BC;AAxTD,qDAMyB;AACzB,6BAA2B;AAoB3B;;;GAGG;AAEI,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IAChC;;;;;OAKG;IACH,QAAQ,CAAC,KAAU,EAAE,IAAyB;QAC5C,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,cAAc,GAA+B;YACjD,SAAS,EAAE,IAAI;YACf,SAAS,EAAE,IAAI;YACf,YAAY,EAAE,IAAI;YAClB,aAAa,EAAE,IAAI;YACnB,cAAc,EAAE,KAAK;YACrB,aAAa,EAAE,KAAK;SACrB,CAAC;QAEF,MAAM,OAAO,GAA+B;YAC1C,GAAG,cAAc;YACjB,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;SAC/B,CAAC;QAEF,mCAAmC;QACnC,MAAM,SAAS,GAAG,IAAA,UAAI,EAAC,KAAK,CAAC,CAAC;QAC9B,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC;YACpB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,+BAA+B;QAC/B,IAAI,SAAS,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YAC1C,OAAO,KAAK,CAAC;QACf,CAAC;QACD,IAAI,SAAS,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YAC1C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,0CAA0C;QAC1C,IAAI,CAAC,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;YACrD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;YACvD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;YACzD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;YACvD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACK,WAAW,CAAC,EAAU;QAC5B,MAAM,SAAS,GAAG,IAAA,UAAI,EAAC,EAAE,CAAC,CAAC;QAE3B,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC;YACpB,sBAAsB;YACtB,MAAM,aAAa,GAAG;gBACpB,OAAO,EAAqB,aAAa;gBACzC,+BAA+B,EAAE,gBAAgB;gBACjD,aAAa,EAAe,iBAAiB;aAC9C,CAAC;YAEF,OAAO,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QACrD,CAAC;aAAM,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC;YAC3B,sBAAsB;YACtB,MAAM,aAAa,GAAG;gBACpB,SAAS,EAAG,oCAAoC;gBAChD,SAAS,EAAG,oCAAoC;gBAChD,SAAS,EAAG,yBAAyB;aACtC,CAAC;YAEF,OAAO,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QACrD,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;OAIG;IACK,YAAY,CAAC,EAAU;QAC7B,MAAM,SAAS,GAAG,IAAA,UAAI,EAAC,EAAE,CAAC,CAAC;QAE3B,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC;YACpB,OAAO,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC3B,CAAC;aAAM,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC;YAC3B,OAAO,EAAE,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC;QACpC,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;OAIG;IACK,aAAa,CAAC,EAAU;QAC9B,MAAM,SAAS,GAAG,IAAA,UAAI,EAAC,EAAE,CAAC,CAAC;QAE3B,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC;YACpB,oCAAoC;YACpC,OAAO,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACxC,CAAC;aAAM,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC;YAC3B,iCAAiC;YACjC,OAAO,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACrC,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;OAIG;IACK,YAAY,CAAC,EAAU;QAC7B,MAAM,SAAS,GAAG,IAAA,UAAI,EAAC,EAAE,CAAC,CAAC;QAE3B,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC;YACpB,MAAM,cAAc,GAAG;gBACrB,MAAM,EAAY,2BAA2B;gBAC7C,aAAa,EAAK,8BAA8B;gBAChD,cAAc,EAAI,2CAA2C;gBAC7D,cAAc,EAAI,4BAA4B;gBAC9C,iBAAiB,EAAE,+BAA+B;gBAClD,gBAAgB,EAAG,8BAA8B;gBACjD,QAAQ,EAAU,wCAAwC;aAC3D,CAAC;YAEF,OAAO,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QACtD,CAAC;aAAM,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC;YAC3B,MAAM,cAAc,GAAG;gBACrB,MAAM,EAAY,wCAAwC;gBAC1D,aAAa,EAAK,gCAAgC;gBAClD,YAAY,EAAM,wBAAwB;aAC3C,CAAC;YAEF,OAAO,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QACtD,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;OAIG;IACH,cAAc,CAAC,IAAyB;QACtC,MAAM,OAAO,GAA+B,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACtE,MAAM,YAAY,GAAG,EAAE,CAAC;QAExB,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK;YAAE,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3D,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK;YAAE,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAE3D,OAAO,GAAG,IAAI,CAAC,QAAQ,oBAAoB,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;IACjF,CAAC;CACF,CAAA;AA9KY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,qCAAmB,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;GAC9C,qBAAqB,CA8KjC;AAED;;;;;GAKG;AACH,SAAgB,WAAW,CACzB,OAAoC,EACpC,iBAAqC;IAErC,OAAO,UAAU,MAAc,EAAE,YAAoB;QACnD,IAAA,mCAAiB,EAAC;YAChB,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,OAAO,EAAE,iBAAiB;YAC1B,WAAW,EAAE,CAAC,OAAO,CAAC;YACtB,SAAS,EAAE,qBAAqB;SACjC,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AACH,SAAgB,aAAa,CAC3B,OAAqE,EACrE,iBAAqC;IAErC,OAAO,WAAW,CAChB,EAAE,GAAG,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,EACjD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AACH,SAAgB,aAAa,CAC3B,OAAqE,EACrE,iBAAqC;IAErC,OAAO,WAAW,CAChB,EAAE,GAAG,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,EACjD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACH,SAAgB,iBAAiB,CAAC,iBAAqC;IACrE,OAAO,WAAW,CAChB;QACE,SAAS,EAAE,IAAI;QACf,SAAS,EAAE,IAAI;QACf,YAAY,EAAE,KAAK;QACnB,aAAa,EAAE,KAAK;QACpB,cAAc,EAAE,KAAK;QACrB,aAAa,EAAE,KAAK;KACrB,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AACH,SAAgB,gBAAgB,CAC9B,OAAoC,EACpC,iBAAqC;IAErC,OAAO,UAAU,MAAc,EAAE,YAAoB;QACnD,IAAA,mCAAiB,EAAC;YAChB,IAAI,EAAE,kBAAkB;YACxB,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,OAAO,EAAE,iBAAiB;YAC1B,WAAW,EAAE,CAAC,OAAO,CAAC;YACtB,SAAS,EAAE;gBACT,QAAQ,CAAC,KAAU,EAAE,IAAyB;oBAC5C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;wBAC1B,OAAO,KAAK,CAAC;oBACf,CAAC;oBAED,MAAM,UAAU,GAAG,IAAI,qBAAqB,EAAE,CAAC;oBAC/C,OAAO,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;gBAC9D,CAAC;gBACD,cAAc,CAAC,IAAyB;oBACtC,OAAO,GAAG,IAAI,CAAC,QAAQ,yCAAyC,CAAC;gBACnE,CAAC;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAa,cAAc;IACzB;;;;OAIG;IACH,MAAM,CAAC,UAAU,CAAC,EAAU;QAC1B,OAAO,IAAA,UAAI,EAAC,EAAE,CAAC,CAAC;IAClB,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,OAAO,CAAC,EAAU,EAAE,OAAoC;QAC7D,MAAM,UAAU,GAAG,IAAI,qBAAqB,EAAE,CAAC;QAC/C,OAAO,UAAU,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,WAAW,EAAE,CAAC,OAAO,CAAC,EAAyB,CAAC,CAAC;IACpF,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,SAAS,CAAC,EAAU;QACzB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,OAAO,GAAG,IAAA,UAAI,EAAC,EAAE,CAAC,CAAC;QACzB,IAAI,OAAO,KAAK,CAAC,EAAE,CAAC;YAClB,sDAAsD;YACtD,IAAI,CAAC;gBACH,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAC5B,MAAM,aAAa,GAAG,EAAE,CAAC;gBACzB,IAAI,gBAAgB,GAAG,CAAC,CAAC,CAAC;gBAE1B,6BAA6B;gBAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBACtC,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,gBAAgB,KAAK,CAAC,CAAC,EAAE,CAAC;wBAC/C,gBAAgB,GAAG,CAAC,CAAC;oBACvB,CAAC;gBACH,CAAC;gBAED,IAAI,gBAAgB,KAAK,CAAC,CAAC,EAAE,CAAC;oBAC5B,sBAAsB;oBACtB,MAAM,iBAAiB,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC;oBAC3D,MAAM,gBAAgB,GAAG,KAAK,CAAC,KAAK,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;oBACjF,MAAM,YAAY,GAAG,CAAC,GAAG,iBAAiB,CAAC,MAAM,GAAG,gBAAgB,CAAC,MAAM,CAAC;oBAE5E,aAAa,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,CAAC;oBACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC;wBACtC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBAC7B,CAAC;oBACD,aAAa,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,CAAC;gBAC1C,CAAC;qBAAM,CAAC;oBACN,aAAa,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;gBAC/B,CAAC;gBAED,gCAAgC;gBAChC,OAAO,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC;YAClF,CAAC;YAAC,MAAM,CAAC;gBACP,OAAO,EAAE,CAAC,WAAW,EAAE,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,aAAa,CAAC,EAAU,EAAE,IAAY;QAC3C,MAAM,CAAC,OAAO,EAAE,YAAY,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAChD,MAAM,MAAM,GAAG,QAAQ,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;QAE1C,IAAI,IAAA,UAAI,EAAC,EAAE,CAAC,KAAK,IAAA,UAAI,EAAC,OAAO,CAAC,EAAE,CAAC;YAC/B,OAAO,KAAK,CAAC,CAAC,wBAAwB;QACxC,CAAC;QAED,IAAI,IAAA,UAAI,EAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QACrD,CAAC;aAAM,IAAI,IAAA,UAAI,EAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QACrD,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;;OAMG;IACK,MAAM,CAAC,iBAAiB,CAAC,EAAU,EAAE,OAAe,EAAE,MAAc;QAC1E,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QACjC,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC3C,MAAM,IAAI,GAAG,CAAC,UAAU,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC;QAEjD,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC;IAChD,CAAC;IAED;;;;;;OAMG;IACK,MAAM,CAAC,iBAAiB,CAAC,EAAU,EAAE,OAAe,EAAE,MAAc;QAC1E,wEAAwE;QACxE,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QACxC,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAElD,IAAI,CAAC,YAAY,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACxC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,KAAK,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QAC7C,MAAM,UAAU,GAAG,iBAAiB,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QAEvD,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAE/C,OAAO,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,eAAe,CAAC,KAAK,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC;IAC1F,CAAC;IAED;;;;OAIG;IACK,MAAM,CAAC,SAAS,CAAC,EAAU;QACjC,OAAO,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;IACzF,CAAC;CACF;AA7ID,wCA6IC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\validation\\validators\\is-ip-address.validator.ts"], "sourcesContent": ["import {\r\n  registerDecorator,\r\n  ValidationOptions,\r\n  ValidatorConstraint,\r\n  ValidatorConstraintInterface,\r\n  ValidationArguments,\r\n} from 'class-validator';\r\nimport { isIP } from 'net';\r\n\r\n/**\r\n * IP address validation options\r\n */\r\nexport interface IpAddressValidationOptions {\r\n  /** Allow IPv4 addresses */\r\n  allowIPv4?: boolean;\r\n  /** Allow IPv6 addresses */\r\n  allowIPv6?: boolean;\r\n  /** Allow private IP addresses */\r\n  allowPrivate?: boolean;\r\n  /** Allow loopback addresses */\r\n  allowLoopback?: boolean;\r\n  /** Allow multicast addresses */\r\n  allowMulticast?: boolean;\r\n  /** Allow reserved addresses */\r\n  allowReserved?: boolean;\r\n}\r\n\r\n/**\r\n * IP address format validator constraint\r\n * Validates IPv4 and IPv6 addresses with configurable options\r\n */\r\n@ValidatorConstraint({ name: 'isIpAddress', async: false })\r\nexport class IsIpAddressConstraint implements ValidatorConstraintInterface {\r\n  /**\r\n   * Validate IP address format and type\r\n   * @param value Value to validate\r\n   * @param args Validation arguments\r\n   * @returns boolean indicating if value is valid IP address\r\n   */\r\n  validate(value: any, args: ValidationArguments): boolean {\r\n    if (typeof value !== 'string') {\r\n      return false;\r\n    }\r\n\r\n    const defaultOptions: IpAddressValidationOptions = {\r\n      allowIPv4: true,\r\n      allowIPv6: true,\r\n      allowPrivate: true,\r\n      allowLoopback: true,\r\n      allowMulticast: false,\r\n      allowReserved: false,\r\n    };\r\n\r\n    const options: IpAddressValidationOptions = {\r\n      ...defaultOptions,\r\n      ...(args.constraints[0] || {}),\r\n    };\r\n\r\n    // Check if it's a valid IP address\r\n    const ipVersion = isIP(value);\r\n    if (ipVersion === 0) {\r\n      return false;\r\n    }\r\n\r\n    // Check IP version constraints\r\n    if (ipVersion === 4 && !options.allowIPv4) {\r\n      return false;\r\n    }\r\n    if (ipVersion === 6 && !options.allowIPv6) {\r\n      return false;\r\n    }\r\n\r\n    // Additional validations based on options\r\n    if (!options.allowPrivate && this.isPrivateIP(value)) {\r\n      return false;\r\n    }\r\n\r\n    if (!options.allowLoopback && this.isLoopbackIP(value)) {\r\n      return false;\r\n    }\r\n\r\n    if (!options.allowMulticast && this.isMulticastIP(value)) {\r\n      return false;\r\n    }\r\n\r\n    if (!options.allowReserved && this.isReservedIP(value)) {\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Check if IP address is private\r\n   * @param ip IP address string\r\n   * @returns boolean indicating if IP is private\r\n   */\r\n  private isPrivateIP(ip: string): boolean {\r\n    const ipVersion = isIP(ip);\r\n    \r\n    if (ipVersion === 4) {\r\n      // IPv4 private ranges\r\n      const privateRanges = [\r\n        /^10\\./,                    // 10.0.0.0/8\r\n        /^172\\.(1[6-9]|2[0-9]|3[01])\\./, // **********/12\r\n        /^192\\.168\\./,              // ***********/16\r\n      ];\r\n      \r\n      return privateRanges.some(range => range.test(ip));\r\n    } else if (ipVersion === 6) {\r\n      // IPv6 private ranges\r\n      const privateRanges = [\r\n        /^fc00:/i,  // fc00::/7 (Unique Local Addresses)\r\n        /^fd00:/i,  // fd00::/8 (Unique Local Addresses)\r\n        /^fe80:/i,  // fe80::/10 (Link-Local)\r\n      ];\r\n      \r\n      return privateRanges.some(range => range.test(ip));\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Check if IP address is loopback\r\n   * @param ip IP address string\r\n   * @returns boolean indicating if IP is loopback\r\n   */\r\n  private isLoopbackIP(ip: string): boolean {\r\n    const ipVersion = isIP(ip);\r\n    \r\n    if (ipVersion === 4) {\r\n      return /^127\\./.test(ip);\r\n    } else if (ipVersion === 6) {\r\n      return ip.toLowerCase() === '::1';\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Check if IP address is multicast\r\n   * @param ip IP address string\r\n   * @returns boolean indicating if IP is multicast\r\n   */\r\n  private isMulticastIP(ip: string): boolean {\r\n    const ipVersion = isIP(ip);\r\n    \r\n    if (ipVersion === 4) {\r\n      // IPv4 multicast range: *********/4\r\n      return /^2(2[4-9]|3[0-9])\\./.test(ip);\r\n    } else if (ipVersion === 6) {\r\n      // IPv6 multicast range: ff00::/8\r\n      return /^ff[0-9a-f]{2}:/i.test(ip);\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Check if IP address is reserved\r\n   * @param ip IP address string\r\n   * @returns boolean indicating if IP is reserved\r\n   */\r\n  private isReservedIP(ip: string): boolean {\r\n    const ipVersion = isIP(ip);\r\n    \r\n    if (ipVersion === 4) {\r\n      const reservedRanges = [\r\n        /^0\\./,           // 0.0.0.0/8 (This network)\r\n        /^169\\.254\\./,    // ***********/16 (Link-local)\r\n        /^192\\.0\\.0\\./,   // *********/24 (IETF Protocol Assignments)\r\n        /^192\\.0\\.2\\./,   // *********/24 (TEST-NET-1)\r\n        /^198\\.51\\.100\\./, // ************/24 (TEST-NET-2)\r\n        /^203\\.0\\.113\\./,  // ***********/24 (TEST-NET-3)\r\n        /^240\\./,         // 240.0.0.0/4 (Reserved for future use)\r\n      ];\r\n      \r\n      return reservedRanges.some(range => range.test(ip));\r\n    } else if (ipVersion === 6) {\r\n      const reservedRanges = [\r\n        /^::$/,           // :: (Unspecified address, but not ::1)\r\n        /^2001:db8:/i,    // 2001:db8::/32 (Documentation)\r\n        /^2001:10:/i,     // 2001:10::/28 (ORCHID)\r\n      ];\r\n      \r\n      return reservedRanges.some(range => range.test(ip));\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Default error message for IP address validation\r\n   * @param args Validation arguments\r\n   * @returns Error message\r\n   */\r\n  defaultMessage(args: ValidationArguments): string {\r\n    const options: IpAddressValidationOptions = args.constraints[0] || {};\r\n    const allowedTypes = [];\r\n    \r\n    if (options.allowIPv4 !== false) allowedTypes.push('IPv4');\r\n    if (options.allowIPv6 !== false) allowedTypes.push('IPv6');\r\n    \r\n    return `${args.property} must be a valid ${allowedTypes.join(' or ')} address`;\r\n  }\r\n}\r\n\r\n/**\r\n * IP address validation decorator\r\n * @param options IP address validation options\r\n * @param validationOptions Validation options\r\n * @returns Property decorator\r\n */\r\nexport function IsIpAddress(\r\n  options?: IpAddressValidationOptions,\r\n  validationOptions?: ValidationOptions,\r\n) {\r\n  return function (object: Object, propertyName: string) {\r\n    registerDecorator({\r\n      target: object.constructor,\r\n      propertyName: propertyName,\r\n      options: validationOptions,\r\n      constraints: [options],\r\n      validator: IsIpAddressConstraint,\r\n    });\r\n  };\r\n}\r\n\r\n/**\r\n * IPv4 address validation decorator\r\n * @param options IP address validation options\r\n * @param validationOptions Validation options\r\n * @returns Property decorator\r\n */\r\nexport function IsIPv4Address(\r\n  options?: Omit<IpAddressValidationOptions, 'allowIPv4' | 'allowIPv6'>,\r\n  validationOptions?: ValidationOptions,\r\n) {\r\n  return IsIpAddress(\r\n    { ...options, allowIPv4: true, allowIPv6: false },\r\n    validationOptions,\r\n  );\r\n}\r\n\r\n/**\r\n * IPv6 address validation decorator\r\n * @param options IP address validation options\r\n * @param validationOptions Validation options\r\n * @returns Property decorator\r\n */\r\nexport function IsIPv6Address(\r\n  options?: Omit<IpAddressValidationOptions, 'allowIPv4' | 'allowIPv6'>,\r\n  validationOptions?: ValidationOptions,\r\n) {\r\n  return IsIpAddress(\r\n    { ...options, allowIPv4: false, allowIPv6: true },\r\n    validationOptions,\r\n  );\r\n}\r\n\r\n/**\r\n * Public IP address validation decorator\r\n * @param validationOptions Validation options\r\n * @returns Property decorator\r\n */\r\nexport function IsPublicIpAddress(validationOptions?: ValidationOptions) {\r\n  return IsIpAddress(\r\n    {\r\n      allowIPv4: true,\r\n      allowIPv6: true,\r\n      allowPrivate: false,\r\n      allowLoopback: false,\r\n      allowMulticast: false,\r\n      allowReserved: false,\r\n    },\r\n    validationOptions,\r\n  );\r\n}\r\n\r\n/**\r\n * IP address array validation decorator\r\n * @param options IP address validation options\r\n * @param validationOptions Validation options\r\n * @returns Property decorator\r\n */\r\nexport function IsIpAddressArray(\r\n  options?: IpAddressValidationOptions,\r\n  validationOptions?: ValidationOptions,\r\n) {\r\n  return function (object: Object, propertyName: string) {\r\n    registerDecorator({\r\n      name: 'isIpAddressArray',\r\n      target: object.constructor,\r\n      propertyName: propertyName,\r\n      options: validationOptions,\r\n      constraints: [options],\r\n      validator: {\r\n        validate(value: any, args: ValidationArguments) {\r\n          if (!Array.isArray(value)) {\r\n            return false;\r\n          }\r\n\r\n          const constraint = new IsIpAddressConstraint();\r\n          return value.every(item => constraint.validate(item, args));\r\n        },\r\n        defaultMessage(args: ValidationArguments) {\r\n          return `${args.property} must be an array of valid IP addresses`;\r\n        },\r\n      },\r\n    });\r\n  };\r\n}\r\n\r\n/**\r\n * Utility functions for IP address handling\r\n */\r\nexport class IpAddressUtils {\r\n  /**\r\n   * Get IP address version\r\n   * @param ip IP address string\r\n   * @returns IP version (4, 6, or 0 if invalid)\r\n   */\r\n  static getVersion(ip: string): number {\r\n    return isIP(ip);\r\n  }\r\n\r\n  /**\r\n   * Check if IP address is valid\r\n   * @param ip IP address string\r\n   * @param options Validation options\r\n   * @returns boolean indicating validity\r\n   */\r\n  static isValid(ip: string, options?: IpAddressValidationOptions): boolean {\r\n    const constraint = new IsIpAddressConstraint();\r\n    return constraint.validate(ip, { constraints: [options] } as ValidationArguments);\r\n  }\r\n\r\n  /**\r\n   * Normalize IP address format\r\n   * @param ip IP address string\r\n   * @returns Normalized IP address or null if invalid\r\n   */\r\n  static normalize(ip: string): string | null {\r\n    if (!this.isValid(ip)) {\r\n      return null;\r\n    }\r\n\r\n    const version = isIP(ip);\r\n    if (version === 6) {\r\n      // Normalize IPv6 address (expand compressed notation)\r\n      try {\r\n        const parts = ip.split(':');\r\n        const expandedParts = [];\r\n        let doubleColonIndex = -1;\r\n\r\n        // Find double colon position\r\n        for (let i = 0; i < parts.length; i++) {\r\n          if (parts[i] === '' && doubleColonIndex === -1) {\r\n            doubleColonIndex = i;\r\n          }\r\n        }\r\n\r\n        if (doubleColonIndex !== -1) {\r\n          // Expand double colon\r\n          const beforeDoubleColon = parts.slice(0, doubleColonIndex);\r\n          const afterDoubleColon = parts.slice(doubleColonIndex + 1).filter(p => p !== '');\r\n          const missingParts = 8 - beforeDoubleColon.length - afterDoubleColon.length;\r\n\r\n          expandedParts.push(...beforeDoubleColon);\r\n          for (let i = 0; i < missingParts; i++) {\r\n            expandedParts.push('0000');\r\n          }\r\n          expandedParts.push(...afterDoubleColon);\r\n        } else {\r\n          expandedParts.push(...parts);\r\n        }\r\n\r\n        // Pad each part to 4 characters\r\n        return expandedParts.map(part => part.padStart(4, '0')).join(':').toLowerCase();\r\n      } catch {\r\n        return ip.toLowerCase();\r\n      }\r\n    }\r\n\r\n    return ip;\r\n  }\r\n\r\n  /**\r\n   * Check if IP is in CIDR range\r\n   * @param ip IP address to check\r\n   * @param cidr CIDR notation (e.g., \"***********/24\")\r\n   * @returns boolean indicating if IP is in range\r\n   */\r\n  static isInCidrRange(ip: string, cidr: string): boolean {\r\n    const [network, prefixLength] = cidr.split('/');\r\n    const prefix = parseInt(prefixLength, 10);\r\n\r\n    if (isIP(ip) !== isIP(network)) {\r\n      return false; // Different IP versions\r\n    }\r\n\r\n    if (isIP(ip) === 4) {\r\n      return this.isIPv4InCidrRange(ip, network, prefix);\r\n    } else if (isIP(ip) === 6) {\r\n      return this.isIPv6InCidrRange(ip, network, prefix);\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Check if IPv4 is in CIDR range\r\n   * @param ip IPv4 address\r\n   * @param network Network address\r\n   * @param prefix Prefix length\r\n   * @returns boolean indicating if IP is in range\r\n   */\r\n  private static isIPv4InCidrRange(ip: string, network: string, prefix: number): boolean {\r\n    const ipInt = this.ipv4ToInt(ip);\r\n    const networkInt = this.ipv4ToInt(network);\r\n    const mask = (0xffffffff << (32 - prefix)) >>> 0;\r\n\r\n    return (ipInt & mask) === (networkInt & mask);\r\n  }\r\n\r\n  /**\r\n   * Check if IPv6 is in CIDR range\r\n   * @param ip IPv6 address\r\n   * @param network Network address\r\n   * @param prefix Prefix length\r\n   * @returns boolean indicating if IP is in range\r\n   */\r\n  private static isIPv6InCidrRange(ip: string, network: string, prefix: number): boolean {\r\n    // Simplified IPv6 CIDR check - in production, use a proper IPv6 library\r\n    const normalizedIp = this.normalize(ip);\r\n    const normalizedNetwork = this.normalize(network);\r\n    \r\n    if (!normalizedIp || !normalizedNetwork) {\r\n      return false;\r\n    }\r\n\r\n    const ipHex = normalizedIp.replace(/:/g, '');\r\n    const networkHex = normalizedNetwork.replace(/:/g, '');\r\n    \r\n    const prefixHexLength = Math.floor(prefix / 4);\r\n    \r\n    return ipHex.substring(0, prefixHexLength) === networkHex.substring(0, prefixHexLength);\r\n  }\r\n\r\n  /**\r\n   * Convert IPv4 address to integer\r\n   * @param ip IPv4 address string\r\n   * @returns Integer representation\r\n   */\r\n  private static ipv4ToInt(ip: string): number {\r\n    return ip.split('.').reduce((acc, octet) => (acc << 8) + parseInt(octet, 10), 0) >>> 0;\r\n  }\r\n}"], "version": 3}