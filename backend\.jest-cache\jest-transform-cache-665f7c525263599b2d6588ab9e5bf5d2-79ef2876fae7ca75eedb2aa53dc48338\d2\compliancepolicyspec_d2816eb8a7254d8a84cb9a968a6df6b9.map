{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\policies\\__tests__\\compliance-policy.spec.ts", "mappings": ";;AAAA,4DAS8B;AAE9B,8GAA6F;AAC7F,0GAAyF;AACzF,8GAA8F;AAC9F,uGAAmG;AAEnG,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;IAChC,IAAI,QAAkB,CAAC;IACvB,IAAI,MAAc,CAAC;IACnB,IAAI,YAA+B,CAAC;IAEpC,UAAU,CAAC,GAAG,EAAE;QACd,QAAQ,GAAG,iCAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QACzC,MAAM,GAAG,6BAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAEnC,YAAY,GAAG;YACb,EAAE,EAAE,WAAW;YACf,IAAI,EAAE,gBAAgB;YACtB,WAAW,EAAE,4CAA4C;YACzD,SAAS,EAAE,uCAAmB,CAAC,IAAI;YACnC,SAAS,EAAE,OAAO;YAClB,IAAI,EAAE,yCAAqB,CAAC,UAAU;YACtC,YAAY,EAAE,CAAC,6BAA6B,EAAE,mBAAmB,CAAC;YAClE,cAAc,EAAE,CAAC,yBAAyB,EAAE,yBAAyB,CAAC;YACtE,QAAQ,EAAE,EAAE;YACZ,MAAM,EAAE,oCAAgB,CAAC,YAAY;YACrC,WAAW,EAAE,MAAM;YACnB,QAAQ,EAAE,sCAAkB,CAAC,IAAI;YACjC,cAAc,EAAE,IAAI;YACpB,cAAc,EAAE,4CAAwB,CAAC,OAAO;SACjD,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,MAAM,GAAG,oCAAgB,CAAC,MAAM,CAAC;gBACrC,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,wBAAwB;gBACrC,SAAS,EAAE,uCAAmB,CAAC,IAAI;gBACnC,OAAO,EAAE,OAAO;gBAChB,QAAQ;gBACR,QAAQ,EAAE,CAAC,YAAY,CAAC;gBACxB,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,uCAAmB,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,CAAC,GAAG,EAAE;gBACV,oCAAgB,CAAC,MAAM,CAAC;oBACtB,IAAI,EAAE,EAAE;oBACR,WAAW,EAAE,wBAAwB;oBACrC,SAAS,EAAE,uCAAmB,CAAC,IAAI;oBACnC,OAAO,EAAE,OAAO;oBAChB,QAAQ;oBACR,QAAQ,EAAE,EAAE;oBACZ,OAAO,EAAE,IAAI;oBACb,SAAS,EAAE,MAAM;iBAClB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,CAAC,GAAG,EAAE;gBACV,oCAAgB,CAAC,MAAM,CAAC;oBACtB,IAAI,EAAE,aAAa;oBACnB,WAAW,EAAE,aAAa;oBAC1B,SAAS,EAAE,SAAgC;oBAC3C,OAAO,EAAE,OAAO;oBAChB,QAAQ;oBACR,QAAQ,EAAE,EAAE;oBACZ,OAAO,EAAE,IAAI;oBACb,SAAS,EAAE,MAAM;iBAClB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,cAAc,GAAG,EAAE,GAAG,YAAY,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC;YAE1D,MAAM,CAAC,GAAG,EAAE;gBACV,oCAAgB,CAAC,MAAM,CAAC;oBACtB,IAAI,EAAE,aAAa;oBACnB,WAAW,EAAE,aAAa;oBAC1B,SAAS,EAAE,uCAAmB,CAAC,IAAI;oBACnC,OAAO,EAAE,OAAO;oBAChB,QAAQ;oBACR,QAAQ,EAAE,CAAC,cAAc,CAAC;oBAC1B,OAAO,EAAE,IAAI;oBACb,SAAS,EAAE,MAAM;iBAClB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,IAAI,MAAwB,CAAC;QAE7B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,oCAAgB,CAAC,MAAM,CAAC;gBAC/B,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,wBAAwB;gBACrC,SAAS,EAAE,uCAAmB,CAAC,IAAI;gBACnC,OAAO,EAAE,OAAO;gBAChB,QAAQ;gBACR,QAAQ,EAAE,CAAC,YAAY,CAAC;gBACxB,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,UAAU,GAAG,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAEhE,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACjC,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC/C,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC3C,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YACxC,MAAM,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC,WAAW,EAAE,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,CAAC,GAAG,EAAE;gBACV,MAAM,CAAC,gBAAgB,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YACjD,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAE7C,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,WAAW,CAAC,CAAC;YACvE,MAAM,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;YACnD,MAAM,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC,WAAW,EAAE,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,cAAc,GAAG,EAAE,GAAG,YAAY,EAAE,EAAE,EAAE,iBAAiB,EAAE,cAAc,EAAE,4CAAwB,CAAC,OAAO,EAAE,CAAC;YACpH,MAAM,CAAC,UAAU,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;YAE1C,MAAM,UAAU,GAAG,MAAM,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;YACtE,MAAM,cAAc,GAAG,UAAU,CAAC,iBAAiB,CAAC,KAAK,CAAC;YAC1D,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,YAAY,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;YAEpF,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,IAAI,MAAwB,CAAC;QAE7B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,oCAAgB,CAAC,MAAM,CAAC;gBAC/B,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,wBAAwB;gBACrC,SAAS,EAAE,uCAAmB,CAAC,IAAI;gBACnC,OAAO,EAAE,OAAO;gBAChB,QAAQ;gBACR,QAAQ,EAAE,CAAC,YAAY,CAAC;gBACxB,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,SAAS,GAAG,kCAAS,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;YAC3D,MAAM,OAAO,GAAG,kCAAS,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;YAEzD,MAAM,MAAM,GAAG,MAAM,CAAC,wBAAwB,CAC5C,uCAAmB,CAAC,IAAI,EACxB,EAAE,SAAS,EAAE,OAAO,EAAE,EACtB,MAAM,CACP,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7B,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,uCAAmB,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,uCAAuC;YACvC,MAAM,gBAAgB,GAAG;gBACvB,GAAG,YAAY;gBACf,EAAE,EAAE,WAAW;gBACf,MAAM,EAAE,oCAAgB,CAAC,SAAS;aACnC,CAAC;YACF,MAAM,mBAAmB,GAAG;gBAC1B,GAAG,YAAY;gBACf,EAAE,EAAE,WAAW;gBACf,MAAM,EAAE,oCAAgB,CAAC,aAAa;aACvC,CAAC;YAEF,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;YAC5C,MAAM,CAAC,UAAU,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;YAE/C,MAAM,SAAS,GAAG,kCAAS,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;YAC3D,MAAM,OAAO,GAAG,kCAAS,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;YAEzD,MAAM,MAAM,GAAG,MAAM,CAAC,wBAAwB,CAC5C,uCAAmB,CAAC,IAAI,EACxB,EAAE,SAAS,EAAE,OAAO,EAAE,EACtB,MAAM,CACP,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,mBAAmB,GAAG;gBAC1B,GAAG,YAAY;gBACf,EAAE,EAAE,WAAW;gBACf,MAAM,EAAE,oCAAgB,CAAC,aAAa;aACvC,CAAC;YACF,MAAM,CAAC,UAAU,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;YAE/C,MAAM,SAAS,GAAG,kCAAS,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;YAC3D,MAAM,OAAO,GAAG,kCAAS,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;YAEzD,MAAM,MAAM,GAAG,MAAM,CAAC,wBAAwB,CAC5C,uCAAmB,CAAC,IAAI,EACxB,EAAE,SAAS,EAAE,OAAO,EAAE,EACtB,MAAM,CACP,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,oCAAgB,CAAC,aAAa,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,IAAI,MAAwB,CAAC;QAE7B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,oCAAgB,CAAC,MAAM,CAAC;gBAC/B,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,wBAAwB;gBACrC,SAAS,EAAE,uCAAmB,CAAC,IAAI;gBACnC,OAAO,EAAE,OAAO;gBAChB,QAAQ;gBACR,QAAQ,EAAE,CAAC,YAAY,CAAC;gBACxB,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,UAAU,GAAG,EAAE,GAAG,YAAY,EAAE,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;YAC7E,MAAM,CAAC,UAAU,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YAEtC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,WAAW,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,CAAC,GAAG,EAAE;gBACV,MAAM,CAAC,UAAU,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,CAAC,aAAa,CAAC,WAAW,EAAE,EAAE,IAAI,EAAE,iBAAiB,EAAE,EAAE,MAAM,CAAC,CAAC;YAEvE,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,WAAW,CAAC,CAAC;YACvE,MAAM,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,CAAC,GAAG,EAAE;gBACV,MAAM,CAAC,aAAa,CAAC,cAAc,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC,CAAC;YACpE,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACjC,MAAM,CAAC,aAAa,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAE1C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,CAAC,GAAG,EAAE;gBACV,MAAM,CAAC,aAAa,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,IAAI,MAAwB,CAAC;QAE7B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,oCAAgB,CAAC,MAAM,CAAC;gBAC/B,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,wBAAwB;gBACrC,SAAS,EAAE,uCAAmB,CAAC,IAAI;gBACnC,OAAO,EAAE,OAAO;gBAChB,QAAQ;gBACR,QAAQ,EAAE,CAAC,YAAY,CAAC;gBACxB,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,QAAQ,GAAG;gBACf,EAAE,EAAE,YAAY;gBAChB,IAAI,EAAE,0CAAsB,CAAC,QAAQ;gBACrC,WAAW,EAAE,gCAAgC;gBAC7C,MAAM,EAAE,0BAA0B;gBAClC,WAAW,EAAE,kCAAS,CAAC,GAAG,EAAE;gBAC5B,WAAW,EAAE,MAAM;gBACnB,QAAQ,EAAE,EAAE,UAAU,EAAE,SAAS,EAAE;aACpC,CAAC;YAEF,MAAM,CAAC,WAAW,CAAC,WAAW,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;YAElD,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,WAAW,CAAC,CAAC;YAChE,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iEAAiE,EAAE,GAAG,EAAE;YACzE,MAAM,QAAQ,GAAG;gBACf,EAAE,EAAE,YAAY;gBAChB,IAAI,EAAE,0CAAsB,CAAC,QAAQ;gBACrC,WAAW,EAAE,eAAe;gBAC5B,MAAM,EAAE,MAAM;gBACd,WAAW,EAAE,kCAAS,CAAC,GAAG,EAAE;gBAC5B,WAAW,EAAE,MAAM;gBACnB,QAAQ,EAAE,EAAE;aACb,CAAC;YAEF,MAAM,CAAC,GAAG,EAAE;gBACV,MAAM,CAAC,WAAW,CAAC,cAAc,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,IAAI,MAAwB,CAAC;QAE7B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,oCAAgB,CAAC,MAAM,CAAC;gBAC/B,IAAI,EAAE,wBAAwB;gBAC9B,WAAW,EAAE,qCAAqC;gBAClD,SAAS,EAAE,uCAAmB,CAAC,IAAI;gBACnC,OAAO,EAAE,OAAO;gBAChB,QAAQ;gBACR,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,wCAAwC;YACxC,MAAM,WAAW,GAAG,EAAE,GAAG,YAAY,EAAE,SAAS,EAAE,uCAAmB,CAAC,IAAI,EAAE,MAAM,EAAE,oCAAgB,CAAC,SAAS,EAAE,CAAC;YACjH,MAAM,WAAW,GAAG,EAAE,GAAG,YAAY,EAAE,EAAE,EAAE,WAAW,EAAE,SAAS,EAAE,uCAAmB,CAAC,IAAI,EAAE,MAAM,EAAE,oCAAgB,CAAC,aAAa,EAAE,CAAC;YAEtI,MAAM,CAAC,UAAU,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YACvC,MAAM,CAAC,UAAU,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAEvC,MAAM,OAAO,GAAG,MAAM,CAAC,0BAA0B,EAAE,CAAC;YAEpD,MAAM,CAAC,OAAO,CAAC,uCAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,oCAAgB,CAAC,SAAS,CAAC,CAAC;YAC3E,MAAM,CAAC,OAAO,CAAC,uCAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,oCAAgB,CAAC,aAAa,CAAC,CAAC;YAC/E,MAAM,CAAC,OAAO,CAAC,uCAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,oCAAgB,CAAC,YAAY,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,QAAQ,GAAG,EAAE,GAAG,YAAY,EAAE,SAAS,EAAE,uCAAmB,CAAC,IAAI,EAAE,MAAM,EAAE,oCAAgB,CAAC,SAAS,EAAE,CAAC;YAC9G,MAAM,QAAQ,GAAG,EAAE,GAAG,YAAY,EAAE,EAAE,EAAE,WAAW,EAAE,SAAS,EAAE,uCAAmB,CAAC,IAAI,EAAE,MAAM,EAAE,oCAAgB,CAAC,mBAAmB,EAAE,CAAC;YAEzI,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACpC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAEpC,MAAM,OAAO,GAAG,MAAM,CAAC,0BAA0B,EAAE,CAAC;YAEpD,sDAAsD;YACtD,MAAM,CAAC,OAAO,CAAC,uCAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,oCAAgB,CAAC,mBAAmB,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,IAAI,MAAwB,CAAC;QAE7B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,oCAAgB,CAAC,MAAM,CAAC;gBAC/B,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,wBAAwB;gBACrC,SAAS,EAAE,uCAAmB,CAAC,IAAI;gBACnC,OAAO,EAAE,OAAO;gBAChB,QAAQ;gBACR,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;YAC3B,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;YAEvC,MAAM,cAAc,GAAG;gBACrB,GAAG,YAAY;gBACf,cAAc,EAAE,kCAAS,CAAC,MAAM,CAAC,OAAO,CAAC;aAC1C,CAAC;YAEF,MAAM,aAAa,GAAG;gBACpB,GAAG,YAAY;gBACf,EAAE,EAAE,WAAW;gBACf,cAAc,EAAE,kCAAS,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,WAAW;aAC9E,CAAC;YAEF,MAAM,CAAC,UAAU,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;YAC1C,MAAM,CAAC,UAAU,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;YAEzC,MAAM,2BAA2B,GAAG,MAAM,CAAC,8BAA8B,EAAE,CAAC;YAE5E,MAAM,CAAC,2BAA2B,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACpD,MAAM,CAAC,2BAA2B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,kBAAkB,GAAG,EAAE,GAAG,YAAY,EAAE,CAAC;YAC/C,OAAO,kBAAkB,CAAC,cAAc,CAAC;YAEzC,MAAM,CAAC,UAAU,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;YAE9C,MAAM,2BAA2B,GAAG,MAAM,CAAC,8BAA8B,EAAE,CAAC;YAE5E,MAAM,CAAC,2BAA2B,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACpD,MAAM,CAAC,2BAA2B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,cAAc,GAAG,EAAE,GAAG,YAAY,EAAE,IAAI,EAAE,SAAkC,EAAE,CAAC;YAErF,MAAM,CAAC,GAAG,EAAE;gBACV,oCAAgB,CAAC,MAAM,CAAC;oBACtB,IAAI,EAAE,aAAa;oBACnB,WAAW,EAAE,aAAa;oBAC1B,SAAS,EAAE,uCAAmB,CAAC,IAAI;oBACnC,OAAO,EAAE,OAAO;oBAChB,QAAQ;oBACR,QAAQ,EAAE,CAAC,cAAc,CAAC;oBAC1B,OAAO,EAAE,IAAI;oBACb,SAAS,EAAE,MAAM;iBAClB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,kBAAkB,GAAG,EAAE,GAAG,YAAY,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;YAEzD,MAAM,CAAC,GAAG,EAAE;gBACV,oCAAgB,CAAC,MAAM,CAAC;oBACtB,IAAI,EAAE,aAAa;oBACnB,WAAW,EAAE,aAAa;oBAC1B,SAAS,EAAE,uCAAmB,CAAC,IAAI;oBACnC,OAAO,EAAE,OAAO;oBAChB,QAAQ;oBACR,QAAQ,EAAE,CAAC,kBAAkB,CAAC;oBAC9B,OAAO,EAAE,IAAI;oBACb,SAAS,EAAE,MAAM;iBAClB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,8BAA8B,GAAG;gBACrC,GAAG,YAAY;gBACf,YAAY,EAAE,cAAqB;aACpC,CAAC;YAEF,MAAM,CAAC,GAAG,EAAE;gBACV,oCAAgB,CAAC,MAAM,CAAC;oBACtB,IAAI,EAAE,aAAa;oBACnB,WAAW,EAAE,aAAa;oBAC1B,SAAS,EAAE,uCAAmB,CAAC,IAAI;oBACnC,OAAO,EAAE,OAAO;oBAChB,QAAQ;oBACR,QAAQ,EAAE,CAAC,8BAA8B,CAAC;oBAC1C,OAAO,EAAE,IAAI;oBACb,SAAS,EAAE,MAAM;iBAClB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,0CAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\policies\\__tests__\\compliance-policy.spec.ts"], "sourcesContent": ["import { \r\n  CompliancePolicy, \r\n  ComplianceFramework, \r\n  ComplianceStatus, \r\n  ComplianceControlType,\r\n  ComplianceControl,\r\n  CompliancePriority,\r\n  ComplianceCheckFrequency,\r\n  ComplianceEvidenceType\r\n} from '../compliance-policy';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { TenantId } from '../../../../../shared-kernel/value-objects/tenant-id.value-object';\r\nimport { UserId } from '../../../../../shared-kernel/value-objects/user-id.value-object';\r\nimport { Timestamp } from '../../../../../shared-kernel/value-objects/timestamp.value-object';\r\nimport { ValidationException } from '../../../../../shared-kernel/exceptions/validation.exception';\r\n\r\ndescribe('CompliancePolicy', () => {\r\n  let tenantId: TenantId;\r\n  let userId: UserId;\r\n  let validControl: ComplianceControl;\r\n\r\n  beforeEach(() => {\r\n    tenantId = TenantId.create('tenant-123');\r\n    userId = UserId.create('user-123');\r\n    \r\n    validControl = {\r\n      id: 'control-1',\r\n      name: 'Access Control',\r\n      description: 'Ensure proper access controls are in place',\r\n      framework: ComplianceFramework.SOC2,\r\n      controlId: 'CC6.1',\r\n      type: ComplianceControlType.PREVENTIVE,\r\n      requirements: ['Multi-factor authentication', 'Role-based access'],\r\n      testProcedures: ['Review user access logs', 'Test MFA implementation'],\r\n      evidence: [],\r\n      status: ComplianceStatus.NOT_ASSESSED,\r\n      responsible: userId,\r\n      priority: CompliancePriority.HIGH,\r\n      automatedCheck: true,\r\n      checkFrequency: ComplianceCheckFrequency.MONTHLY\r\n    };\r\n  });\r\n\r\n  describe('creation', () => {\r\n    it('should create a valid compliance policy', () => {\r\n      const policy = CompliancePolicy.create({\r\n        name: 'SOC2 Policy',\r\n        description: 'SOC2 compliance policy',\r\n        framework: ComplianceFramework.SOC2,\r\n        version: '1.0.0',\r\n        tenantId,\r\n        controls: [validControl],\r\n        enabled: true,\r\n        createdBy: userId\r\n      });\r\n\r\n      expect(policy).toBeDefined();\r\n      expect(policy.name).toBe('SOC2 Policy');\r\n      expect(policy.framework).toBe(ComplianceFramework.SOC2);\r\n      expect(policy.controls).toHaveLength(1);\r\n      expect(policy.enabled).toBe(true);\r\n    });\r\n\r\n    it('should throw validation exception for missing name', () => {\r\n      expect(() => {\r\n        CompliancePolicy.create({\r\n          name: '',\r\n          description: 'SOC2 compliance policy',\r\n          framework: ComplianceFramework.SOC2,\r\n          version: '1.0.0',\r\n          tenantId,\r\n          controls: [],\r\n          enabled: true,\r\n          createdBy: userId\r\n        });\r\n      }).toThrow(ValidationException);\r\n    });\r\n\r\n    it('should throw validation exception for invalid framework', () => {\r\n      expect(() => {\r\n        CompliancePolicy.create({\r\n          name: 'Test Policy',\r\n          description: 'Test policy',\r\n          framework: 'INVALID' as ComplianceFramework,\r\n          version: '1.0.0',\r\n          tenantId,\r\n          controls: [],\r\n          enabled: true,\r\n          createdBy: userId\r\n        });\r\n      }).toThrow(ValidationException);\r\n    });\r\n\r\n    it('should throw validation exception for invalid control', () => {\r\n      const invalidControl = { ...validControl, controlId: '' };\r\n      \r\n      expect(() => {\r\n        CompliancePolicy.create({\r\n          name: 'Test Policy',\r\n          description: 'Test policy',\r\n          framework: ComplianceFramework.SOC2,\r\n          version: '1.0.0',\r\n          tenantId,\r\n          controls: [invalidControl],\r\n          enabled: true,\r\n          createdBy: userId\r\n        });\r\n      }).toThrow(ValidationException);\r\n    });\r\n  });\r\n\r\n  describe('compliance assessment', () => {\r\n    let policy: CompliancePolicy;\r\n\r\n    beforeEach(() => {\r\n      policy = CompliancePolicy.create({\r\n        name: 'SOC2 Policy',\r\n        description: 'SOC2 compliance policy',\r\n        framework: ComplianceFramework.SOC2,\r\n        version: '1.0.0',\r\n        tenantId,\r\n        controls: [validControl],\r\n        enabled: true,\r\n        createdBy: userId\r\n      });\r\n    });\r\n\r\n    it('should assess compliance for a control', () => {\r\n      const assessment = policy.assessCompliance('control-1', userId);\r\n\r\n      expect(assessment).toBeDefined();\r\n      expect(assessment.controlId).toBe('control-1');\r\n      expect(assessment.assessedBy).toBe(userId);\r\n      expect(assessment.status).toBeDefined();\r\n      expect(assessment.nextAssessmentDue).toBeDefined();\r\n    });\r\n\r\n    it('should throw error when assessing non-existent control', () => {\r\n      expect(() => {\r\n        policy.assessCompliance('non-existent', userId);\r\n      }).toThrow(ValidationException);\r\n    });\r\n\r\n    it('should update control status after assessment', () => {\r\n      const originalStatus = policy.controls[0].status;\r\n      policy.assessCompliance('control-1', userId);\r\n\r\n      const updatedControl = policy.controls.find(c => c.id === 'control-1');\r\n      expect(updatedControl?.lastAssessed).toBeDefined();\r\n      expect(updatedControl?.nextAssessment).toBeDefined();\r\n    });\r\n\r\n    it('should calculate next assessment date based on frequency', () => {\r\n      const monthlyControl = { ...validControl, id: 'control-monthly', checkFrequency: ComplianceCheckFrequency.MONTHLY };\r\n      policy.addControl(monthlyControl, userId);\r\n\r\n      const assessment = policy.assessCompliance('control-monthly', userId);\r\n      const nextAssessment = assessment.nextAssessmentDue.value;\r\n      const now = new Date();\r\n      const expectedNext = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());\r\n\r\n      expect(nextAssessment.getMonth()).toBe(expectedNext.getMonth());\r\n    });\r\n  });\r\n\r\n  describe('compliance reporting', () => {\r\n    let policy: CompliancePolicy;\r\n\r\n    beforeEach(() => {\r\n      policy = CompliancePolicy.create({\r\n        name: 'SOC2 Policy',\r\n        description: 'SOC2 compliance policy',\r\n        framework: ComplianceFramework.SOC2,\r\n        version: '1.0.0',\r\n        tenantId,\r\n        controls: [validControl],\r\n        enabled: true,\r\n        createdBy: userId\r\n      });\r\n    });\r\n\r\n    it('should generate compliance report', () => {\r\n      const startDate = Timestamp.create(new Date('2024-01-01'));\r\n      const endDate = Timestamp.create(new Date('2024-12-31'));\r\n\r\n      const report = policy.generateComplianceReport(\r\n        ComplianceFramework.SOC2,\r\n        { startDate, endDate },\r\n        userId\r\n      );\r\n\r\n      expect(report).toBeDefined();\r\n      expect(report.framework).toBe(ComplianceFramework.SOC2);\r\n      expect(report.generatedBy).toBe(userId);\r\n      expect(report.controlsSummary).toBeDefined();\r\n      expect(report.overallStatus).toBeDefined();\r\n      expect(report.executiveSummary).toBeDefined();\r\n    });\r\n\r\n    it('should calculate controls summary correctly', () => {\r\n      // Add controls with different statuses\r\n      const compliantControl = { \r\n        ...validControl, \r\n        id: 'control-2', \r\n        status: ComplianceStatus.COMPLIANT \r\n      };\r\n      const nonCompliantControl = { \r\n        ...validControl, \r\n        id: 'control-3', \r\n        status: ComplianceStatus.NON_COMPLIANT \r\n      };\r\n\r\n      policy.addControl(compliantControl, userId);\r\n      policy.addControl(nonCompliantControl, userId);\r\n\r\n      const startDate = Timestamp.create(new Date('2024-01-01'));\r\n      const endDate = Timestamp.create(new Date('2024-12-31'));\r\n\r\n      const report = policy.generateComplianceReport(\r\n        ComplianceFramework.SOC2,\r\n        { startDate, endDate },\r\n        userId\r\n      );\r\n\r\n      expect(report.controlsSummary.total).toBe(3);\r\n      expect(report.controlsSummary.compliant).toBe(1);\r\n      expect(report.controlsSummary.nonCompliant).toBe(1);\r\n      expect(report.controlsSummary.notAssessed).toBe(1);\r\n    });\r\n\r\n    it('should determine overall status correctly', () => {\r\n      const nonCompliantControl = { \r\n        ...validControl, \r\n        id: 'control-2', \r\n        status: ComplianceStatus.NON_COMPLIANT \r\n      };\r\n      policy.addControl(nonCompliantControl, userId);\r\n\r\n      const startDate = Timestamp.create(new Date('2024-01-01'));\r\n      const endDate = Timestamp.create(new Date('2024-12-31'));\r\n\r\n      const report = policy.generateComplianceReport(\r\n        ComplianceFramework.SOC2,\r\n        { startDate, endDate },\r\n        userId\r\n      );\r\n\r\n      expect(report.overallStatus).toBe(ComplianceStatus.NON_COMPLIANT);\r\n    });\r\n  });\r\n\r\n  describe('control management', () => {\r\n    let policy: CompliancePolicy;\r\n\r\n    beforeEach(() => {\r\n      policy = CompliancePolicy.create({\r\n        name: 'SOC2 Policy',\r\n        description: 'SOC2 compliance policy',\r\n        framework: ComplianceFramework.SOC2,\r\n        version: '1.0.0',\r\n        tenantId,\r\n        controls: [validControl],\r\n        enabled: true,\r\n        createdBy: userId\r\n      });\r\n    });\r\n\r\n    it('should add a new control', () => {\r\n      const newControl = { ...validControl, id: 'control-2', name: 'New Control' };\r\n      policy.addControl(newControl, userId);\r\n\r\n      expect(policy.controls).toHaveLength(2);\r\n      expect(policy.controls.find(c => c.id === 'control-2')).toBeDefined();\r\n    });\r\n\r\n    it('should throw error when adding duplicate control ID', () => {\r\n      expect(() => {\r\n        policy.addControl(validControl, userId);\r\n      }).toThrow(ValidationException);\r\n    });\r\n\r\n    it('should update an existing control', () => {\r\n      policy.updateControl('control-1', { name: 'Updated Control' }, userId);\r\n\r\n      const updatedControl = policy.controls.find(c => c.id === 'control-1');\r\n      expect(updatedControl?.name).toBe('Updated Control');\r\n    });\r\n\r\n    it('should throw error when updating non-existent control', () => {\r\n      expect(() => {\r\n        policy.updateControl('non-existent', { name: 'Updated' }, userId);\r\n      }).toThrow(ValidationException);\r\n    });\r\n\r\n    it('should remove a control', () => {\r\n      policy.removeControl('control-1', userId);\r\n\r\n      expect(policy.controls).toHaveLength(0);\r\n    });\r\n\r\n    it('should throw error when removing non-existent control', () => {\r\n      expect(() => {\r\n        policy.removeControl('non-existent', userId);\r\n      }).toThrow(ValidationException);\r\n    });\r\n  });\r\n\r\n  describe('evidence management', () => {\r\n    let policy: CompliancePolicy;\r\n\r\n    beforeEach(() => {\r\n      policy = CompliancePolicy.create({\r\n        name: 'SOC2 Policy',\r\n        description: 'SOC2 compliance policy',\r\n        framework: ComplianceFramework.SOC2,\r\n        version: '1.0.0',\r\n        tenantId,\r\n        controls: [validControl],\r\n        enabled: true,\r\n        createdBy: userId\r\n      });\r\n    });\r\n\r\n    it('should add evidence to a control', () => {\r\n      const evidence = {\r\n        id: 'evidence-1',\r\n        type: ComplianceEvidenceType.DOCUMENT,\r\n        description: 'Access control policy document',\r\n        source: 'Policy Management System',\r\n        collectedAt: Timestamp.now(),\r\n        collectedBy: userId,\r\n        metadata: { documentId: 'doc-123' }\r\n      };\r\n\r\n      policy.addEvidence('control-1', evidence, userId);\r\n\r\n      const control = policy.controls.find(c => c.id === 'control-1');\r\n      expect(control?.evidence).toHaveLength(1);\r\n      expect(control?.evidence[0].id).toBe('evidence-1');\r\n    });\r\n\r\n    it('should throw error when adding evidence to non-existent control', () => {\r\n      const evidence = {\r\n        id: 'evidence-1',\r\n        type: ComplianceEvidenceType.DOCUMENT,\r\n        description: 'Test evidence',\r\n        source: 'Test',\r\n        collectedAt: Timestamp.now(),\r\n        collectedBy: userId,\r\n        metadata: {}\r\n      };\r\n\r\n      expect(() => {\r\n        policy.addEvidence('non-existent', evidence, userId);\r\n      }).toThrow(ValidationException);\r\n    });\r\n  });\r\n\r\n  describe('compliance status summary', () => {\r\n    let policy: CompliancePolicy;\r\n\r\n    beforeEach(() => {\r\n      policy = CompliancePolicy.create({\r\n        name: 'Multi-Framework Policy',\r\n        description: 'Policy covering multiple frameworks',\r\n        framework: ComplianceFramework.SOC2,\r\n        version: '1.0.0',\r\n        tenantId,\r\n        controls: [],\r\n        enabled: true,\r\n        createdBy: userId\r\n      });\r\n    });\r\n\r\n    it('should return status summary for all frameworks', () => {\r\n      // Add controls for different frameworks\r\n      const soc2Control = { ...validControl, framework: ComplianceFramework.SOC2, status: ComplianceStatus.COMPLIANT };\r\n      const gdprControl = { ...validControl, id: 'control-2', framework: ComplianceFramework.GDPR, status: ComplianceStatus.NON_COMPLIANT };\r\n      \r\n      policy.addControl(soc2Control, userId);\r\n      policy.addControl(gdprControl, userId);\r\n\r\n      const summary = policy.getComplianceStatusSummary();\r\n\r\n      expect(summary[ComplianceFramework.SOC2]).toBe(ComplianceStatus.COMPLIANT);\r\n      expect(summary[ComplianceFramework.GDPR]).toBe(ComplianceStatus.NON_COMPLIANT);\r\n      expect(summary[ComplianceFramework.HIPAA]).toBe(ComplianceStatus.NOT_ASSESSED);\r\n    });\r\n\r\n    it('should aggregate status correctly for multiple controls', () => {\r\n      const control1 = { ...validControl, framework: ComplianceFramework.SOC2, status: ComplianceStatus.COMPLIANT };\r\n      const control2 = { ...validControl, id: 'control-2', framework: ComplianceFramework.SOC2, status: ComplianceStatus.PARTIALLY_COMPLIANT };\r\n      \r\n      policy.addControl(control1, userId);\r\n      policy.addControl(control2, userId);\r\n\r\n      const summary = policy.getComplianceStatusSummary();\r\n\r\n      // Should be partially compliant due to mixed statuses\r\n      expect(summary[ComplianceFramework.SOC2]).toBe(ComplianceStatus.PARTIALLY_COMPLIANT);\r\n    });\r\n  });\r\n\r\n  describe('controls requiring assessment', () => {\r\n    let policy: CompliancePolicy;\r\n\r\n    beforeEach(() => {\r\n      policy = CompliancePolicy.create({\r\n        name: 'SOC2 Policy',\r\n        description: 'SOC2 compliance policy',\r\n        framework: ComplianceFramework.SOC2,\r\n        version: '1.0.0',\r\n        tenantId,\r\n        controls: [],\r\n        enabled: true,\r\n        createdBy: userId\r\n      });\r\n    });\r\n\r\n    it('should return controls that need assessment', () => {\r\n      const pastDue = new Date();\r\n      pastDue.setDate(pastDue.getDate() - 1);\r\n\r\n      const overdueControl = { \r\n        ...validControl, \r\n        nextAssessment: Timestamp.create(pastDue)\r\n      };\r\n      \r\n      const futureControl = { \r\n        ...validControl, \r\n        id: 'control-2',\r\n        nextAssessment: Timestamp.create(new Date(Date.now() + 86400000)) // Tomorrow\r\n      };\r\n\r\n      policy.addControl(overdueControl, userId);\r\n      policy.addControl(futureControl, userId);\r\n\r\n      const controlsRequiringAssessment = policy.getControlsRequiringAssessment();\r\n\r\n      expect(controlsRequiringAssessment).toHaveLength(1);\r\n      expect(controlsRequiringAssessment[0].id).toBe('control-1');\r\n    });\r\n\r\n    it('should return controls with no next assessment date', () => {\r\n      const controlWithoutDate = { ...validControl };\r\n      delete controlWithoutDate.nextAssessment;\r\n\r\n      policy.addControl(controlWithoutDate, userId);\r\n\r\n      const controlsRequiringAssessment = policy.getControlsRequiringAssessment();\r\n\r\n      expect(controlsRequiringAssessment).toHaveLength(1);\r\n      expect(controlsRequiringAssessment[0].id).toBe('control-1');\r\n    });\r\n  });\r\n\r\n  describe('validation', () => {\r\n    it('should validate control properties', () => {\r\n      const invalidControl = { ...validControl, type: 'INVALID' as ComplianceControlType };\r\n      \r\n      expect(() => {\r\n        CompliancePolicy.create({\r\n          name: 'Test Policy',\r\n          description: 'Test policy',\r\n          framework: ComplianceFramework.SOC2,\r\n          version: '1.0.0',\r\n          tenantId,\r\n          controls: [invalidControl],\r\n          enabled: true,\r\n          createdBy: userId\r\n        });\r\n      }).toThrow(ValidationException);\r\n    });\r\n\r\n    it('should validate required control fields', () => {\r\n      const controlMissingName = { ...validControl, name: '' };\r\n      \r\n      expect(() => {\r\n        CompliancePolicy.create({\r\n          name: 'Test Policy',\r\n          description: 'Test policy',\r\n          framework: ComplianceFramework.SOC2,\r\n          version: '1.0.0',\r\n          tenantId,\r\n          controls: [controlMissingName],\r\n          enabled: true,\r\n          createdBy: userId\r\n        });\r\n      }).toThrow(ValidationException);\r\n    });\r\n\r\n    it('should validate control arrays', () => {\r\n      const controlWithInvalidRequirements = { \r\n        ...validControl, \r\n        requirements: 'not an array' as any \r\n      };\r\n      \r\n      expect(() => {\r\n        CompliancePolicy.create({\r\n          name: 'Test Policy',\r\n          description: 'Test policy',\r\n          framework: ComplianceFramework.SOC2,\r\n          version: '1.0.0',\r\n          tenantId,\r\n          controls: [controlWithInvalidRequirements],\r\n          enabled: true,\r\n          createdBy: userId\r\n        });\r\n      }).toThrow(ValidationException);\r\n    });\r\n  });\r\n});"], "version": 3}