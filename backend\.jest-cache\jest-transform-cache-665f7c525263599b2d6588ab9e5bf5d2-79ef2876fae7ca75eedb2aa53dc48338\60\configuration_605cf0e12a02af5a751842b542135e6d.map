{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\configuration.ts", "mappings": ";;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,2CAA+C;AAC/C,2CAA4C;AA2M5C;;GAEG;AACI,MAAM,uBAAuB,GAAG,GAAsB,EAAE;IAC7D,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,aAAa,CAAgB,CAAC;IAEtE,OAAO;QACL,WAAW,EAAE,GAAG;QAChB,aAAa,EAAE,GAAG,KAAK,aAAa;QACpC,YAAY,EAAE,GAAG,KAAK,YAAY;QAClC,MAAM,EAAE,GAAG,KAAK,MAAM;QACtB,SAAS,EAAE,GAAG,KAAK,SAAS;QAC5B,KAAK,EAAE,GAAG,KAAK,aAAa,IAAI,GAAG,KAAK,MAAM;QAC9C,SAAS,EAAE,GAAG,KAAK,aAAa;KACjC,CAAC;AACJ,CAAC,CAAC;AAZW,QAAA,uBAAuB,2BAYlC;AAEF;;GAEG;AACI,MAAM,uBAAuB,GAAG,GAAsB,EAAE,CAAC,CAAC;IAC/D,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,kBAAkB;IACnD,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,OAAO;IAC9C,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,oDAAoD;IACnG,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,MAAM,EAAE,EAAE,CAAC;IACjD,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,KAAK;IAChD,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,uBAAuB,CAAC;IAC/E,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,KAAK,MAAM;IACjD,eAAe,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,OAAO,EAAE,EAAE,CAAC;CAC1E,CAAC,CAAC;AATU,QAAA,uBAAuB,2BASjC;AAEH;;GAEG;AACI,MAAM,oBAAoB,GAAG,GAA6B,EAAE,CAAC,CAAC;IACnE,IAAI,EAAG,OAAO,CAAC,GAAG,CAAC,eAAe,CAAS,IAAI,UAAU;IACzD,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,WAAW;IACjD,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,MAAM,EAAE,EAAE,CAAC;IAC1D,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,UAAU;IACxD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,UAAU;IACxD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,UAAU;IACpD,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,QAAQ;IAClD,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,MAAM;IAC3C,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,KAAK,OAAO;IACpD,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,KAAK,MAAM;IAC3D,aAAa,EAAE,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,KAAK,OAAO;IACjE,cAAc,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,IAAI,KAAK,EAAE,EAAE,CAAC;IAC9E,iBAAiB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,IAAI,OAAO,EAAE,EAAE,CAAC;IACtF,cAAc,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,IAAI,OAAO,EAAE,EAAE,CAAC;IAChF,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,IAAI,OAAO,EAAE,EAAE,CAAC;IAC1E,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,IAAI,MAAM,EAAE,EAAE,CAAC;IAC3E,gBAAgB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,IAAI,OAAO,EAAE,EAAE,CAAC;IACrF,aAAa,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC;CAC3E,CAAC,CAAC;AAnBU,QAAA,oBAAoB,wBAmB9B;AAEH;;GAEG;AACI,MAAM,iBAAiB,GAAG,GAA0B,EAAE,CAAC,CAAC;IAC7D,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,WAAW;IAC9C,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,MAAM,EAAE,EAAE,CAAC;IACvD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,SAAS;IACpD,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC;IAChD,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,MAAM,EAAE,EAAE,CAAC;IACrD,UAAU,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC;IACjE,UAAU,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,MAAM,EAAE,EAAE,CAAC;IACpE,cAAc,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,IAAI,OAAO,EAAE,EAAE,CAAC;IAC7E,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,KAAK,OAAO;IAC1D,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,OAAO,EAAE,EAAE,CAAC;IACnE,MAAM,EAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,GAAG,EAAE,EAAE,CAAW;CACpE,CAAC,CAAC;AAZU,QAAA,iBAAiB,qBAY3B;AAEH;;GAEG;AACI,MAAM,oBAAoB,GAAG,GAA0B,EAAE,CAAC,CAAC;IAChE,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,gDAAgD;IACxF,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,IAAI;IACnD,gBAAgB,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,IAAI,oDAAoD;IAC3G,mBAAmB,EAAE,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,IAAI,IAAI;IAClE,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC;IAChE,aAAa,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,oDAAoD;IACpG,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC;IACjE,cAAc,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,KAAK,EAAE,EAAE,CAAC;IACtE,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,OAAO;IACpD,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,KAAK,OAAO;IAC5D,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,KAAK,MAAM;IAC5D,UAAU,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,IAAI,UAAU,EAAE,EAAE,CAAC;IAC5E,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,KAAK,MAAM;IAC1D,YAAY,EAAG,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAS,IAAI,MAAM;IACtE,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,KAAK,OAAO;CAC7E,CAAC,CAAC;AAhBU,QAAA,oBAAoB,wBAgB9B;AAEH;;GAEG;AACI,MAAM,2BAA2B,GAAG,GAA0B,EAAE,CAAC,CAAC;IACvE,WAAW,EAAE,IAAA,+BAAuB,GAAE;IACtC,WAAW,EAAE,IAAA,+BAAuB,GAAE;IACtC,QAAQ,EAAE,IAAA,4BAAoB,GAAE;IAChC,KAAK,EAAE,IAAA,yBAAiB,GAAE;IAC1B,QAAQ,EAAE,IAAA,4BAAoB,GAAE;IAChC,OAAO,EAAE;QACP,KAAK,EAAG,OAAO,CAAC,GAAG,CAAC,WAAW,CAAS,IAAI,MAAM;QAClD,MAAM,EAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAS,IAAI,MAAM;QACpD,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,KAAK,MAAM;QACvD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,cAAc;QACxD,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,KAAK;QACtD,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC;QACpE,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,KAAK,OAAO;QAC9D,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,KAAK,MAAM;QACzD,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,gBAAgB;QAC5D,oBAAoB,EAAE,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,KAAK,OAAO;QAC3E,iBAAiB,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,KAAK,OAAO;KAC7D;IACD,UAAU,EAAE;QACV,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,KAAK,OAAO;QACnE,0BAA0B,EAAE,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,KAAK,OAAO;QACpF,uBAAuB,EAAE,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,KAAK,OAAO;QAC9E,mBAAmB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,IAAI,KAAK,EAAE,EAAE,CAAC;QAC7F,kBAAkB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,IAAI,KAAK,EAAE,EAAE,CAAC;QAC3F,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,MAAM;QACzD,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,MAAM,EAAE,EAAE,CAAC;QAChE,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,UAAU;QACtD,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,MAAM;QACzD,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,IAAI,kBAAkB;QAC7E,qBAAqB,EAAE,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC;KAC9D;IACD,SAAS,EAAE;QACT,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,KAAK,MAAM;QACrD,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC;QAClC,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC;QACzC,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,IAAI,OAAO,EAAE,EAAE,CAAC;QACnE,aAAa,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC;QAC5E,UAAU,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,IAAI,MAAM,EAAE,EAAE,CAAC;QACzE,qBAAqB,EAAE,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,KAAK,MAAM;QACnF,uBAAuB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC;QACjG,qBAAqB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,IAAI,OAAO,EAAE,EAAE,CAAC;QACjG,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC;QAC/C,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS;QAChH,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;KACnH;IACD,gBAAgB,EAAE;QAChB,iBAAiB,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC;QACtD,0BAA0B,EAAE,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC;QACxE,sBAAsB,EAAE,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC;QAC/D,yBAAyB,EAAE,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC;QACtE,mBAAmB,EAAE,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,KAAK,MAAM;QACpE,gBAAgB,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC;QACnD,gBAAgB,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS;QACjH,gBAAgB,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC;QACnD,oBAAoB,EAAE,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC;KAC5D;IACD,KAAK,EAAE;QACL,UAAU,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,KAAK,EAAE,EAAE,CAAC;QACnE,kBAAkB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,IAAI,MAAM,EAAE,EAAE,CAAC;QACpF,UAAU,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,KAAK,EAAE,EAAE,CAAC;QACnE,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,IAAI,MAAM,EAAE,EAAE,CAAC;QACzE,QAAQ,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,OAAO,EAAE,EAAE,CAAC;QACjE,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,KAAK,MAAM;QACvE,mBAAmB,EAAG,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAS,IAAI,MAAM;KAClF;IACD,UAAU,EAAE;QACV,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,KAAK,OAAO;QAC1D,oBAAoB,EAAE,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,KAAK,OAAO;QAClF,qBAAqB,EAAE,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,KAAK,MAAM;QACnF,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,KAAK,OAAO;QAC1D,oBAAoB,EAAE,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,KAAK,MAAM;QACjF,gBAAgB,EAAE,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,KAAK,MAAM;KAC3E;IACD,UAAU,EAAE;QACV,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,IAAI,UAAU,EAAE,EAAE,CAAC,EAAE,OAAO;QACrF,YAAY,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,IAAI,2CAA2C,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC;QAC7G,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,IAAI,WAAW;QAC7D,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,IAAI,QAAQ;QACnE,eAAe,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,IAAI,SAAS,EAAE,EAAE,CAAC,EAAE,SAAS;QAC7F,gBAAgB,EAAE,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,KAAK,MAAM;KACtE;IACD,UAAU,EAAE;QACV,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC;QAC3E,QAAQ,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,IAAI,KAAK,EAAE,EAAE,CAAC;QACpE,aAAa,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC;QAC5E,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,IAAI,OAAO,EAAE,EAAE,CAAC;KACzE;IACD,WAAW,EAAE;QACX,gBAAgB,EAAE,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,KAAK,MAAM;QACjE,mBAAmB,EAAE,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,KAAK,MAAM;QACvE,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,KAAK,MAAM;QACtE,oBAAoB,EAAE,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,KAAK,MAAM;QAC1E,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,KAAK,MAAM;QACzD,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,KAAK,OAAO;QACjE,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,KAAK,MAAM;KAC9D;CACF,CAAC,CAAC;AAjGU,QAAA,2BAA2B,+BAiGrC;AAEH;;GAEG;AACH,kBAAe,IAAA,mBAAU,EAAC,UAAU,EAAE,mCAA2B,CAAC,CAAC;AAEnE;;GAEG;AAEI,IAAM,4BAA4B,GAAlC,MAAM,4BAA4B;IAGvC,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QACvD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAwB,UAAU,CAAE,CAAC;IAC3E,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;IACjC,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;IACjC,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;IAC9B,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;IAC3B,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;IAC9B,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;IAC7B,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;IAChC,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;IAC/B,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC;IACtC,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;IAC3B,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;IAChC,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;IAChC,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;IAChC,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,aAAa;QACX,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC;IAC3C,CAAC;CACF,CAAA;AAjGY,oEAA4B;uCAA5B,4BAA4B;IADxC,IAAA,mBAAU,GAAE;yDAIiC,sBAAa,oBAAb,sBAAa;GAH9C,4BAA4B,CAiGxC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\configuration.ts"], "sourcesContent": ["import { registerAs } from '@nestjs/config';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { Injectable } from '@nestjs/common';\r\n\r\n/**\r\n * Environment-specific configuration settings for the Sentinel platform\r\n * Provides centralized configuration management with type safety and validation\r\n */\r\n\r\nexport type Environment = 'development' | 'production' | 'test' | 'staging';\r\n\r\nexport interface EnvironmentConfig {\r\n  environment: Environment;\r\n  isDevelopment: boolean;\r\n  isProduction: boolean;\r\n  isTest: boolean;\r\n  isStaging: boolean;\r\n  debug: boolean;\r\n  hotReload: boolean;\r\n}\r\n\r\nexport interface ApplicationConfig {\r\n  name: string;\r\n  version: string;\r\n  description: string;\r\n  port: number;\r\n  globalPrefix: string;\r\n  corsOrigin: string | string[];\r\n  trustProxy: boolean;\r\n  shutdownTimeout: number;\r\n}\r\n\r\nexport interface DatabaseConnectionConfig {\r\n  type: 'postgres' | 'mysql' | 'mariadb';\r\n  host: string;\r\n  port: number;\r\n  username: string;\r\n  password: string;\r\n  database: string;\r\n  schema: string;\r\n  ssl: boolean;\r\n  logging: boolean;\r\n  synchronize: boolean;\r\n  migrationsRun: boolean;\r\n  maxConnections: number;\r\n  connectionTimeout: number;\r\n  acquireTimeout: number;\r\n  idleTimeout: number;\r\n  reapInterval: number;\r\n  createRetryDelay: number;\r\n  retryAttempts: number;\r\n}\r\n\r\nexport interface RedisConnectionConfig {\r\n  host: string;\r\n  port: number;\r\n  password?: string;\r\n  db: number;\r\n  ttl: number;\r\n  maxRetries: number;\r\n  retryDelay: number;\r\n  connectTimeout: number;\r\n  lazyConnect: boolean;\r\n  keepAlive: number;\r\n  family: 4 | 6;\r\n}\r\n\r\nexport interface SecurityConfiguration {\r\n  jwtSecret: string;\r\n  jwtExpiresIn: string;\r\n  jwtRefreshSecret: string;\r\n  jwtRefreshExpiresIn: string;\r\n  bcryptRounds: number;\r\n  sessionSecret: string;\r\n  rateLimitTtl: number;\r\n  rateLimitLimit: number;\r\n  corsEnabled: boolean;\r\n  corsCredentials: boolean;\r\n  hstsEnabled: boolean;\r\n  hstsMaxAge: number;\r\n  cspEnabled: boolean;\r\n  frameOptions: 'DENY' | 'SAMEORIGIN' | 'ALLOW-FROM';\r\n  contentTypeOptions: boolean;\r\n}\r\n\r\nexport interface LoggingConfiguration {\r\n  level: 'error' | 'warn' | 'info' | 'http' | 'verbose' | 'debug' | 'silly';\r\n  format: 'json' | 'simple' | 'combined';\r\n  fileEnabled: boolean;\r\n  filePath: string;\r\n  fileMaxSize: string;\r\n  fileMaxFiles: number;\r\n  consoleEnabled: boolean;\r\n  auditEnabled: boolean;\r\n  auditPath: string;\r\n  correlationIdEnabled: boolean;\r\n  structuredLogging: boolean;\r\n}\r\n\r\nexport interface MonitoringConfiguration {\r\n  healthCheckEnabled: boolean;\r\n  healthCheckDatabaseEnabled: boolean;\r\n  healthCheckRedisEnabled: boolean;\r\n  memoryHeapThreshold: number;\r\n  memoryRssThreshold: number;\r\n  metricsEnabled: boolean;\r\n  metricsPort: number;\r\n  metricsPath: string;\r\n  tracingEnabled: boolean;\r\n  tracingServiceName: string;\r\n  tracingJaegerEndpoint?: string;\r\n}\r\n\r\nexport interface AIServiceConfiguration {\r\n  enabled: boolean;\r\n  url?: string;\r\n  apiKey?: string;\r\n  timeout: number;\r\n  retryAttempts: number;\r\n  retryDelay: number;\r\n  circuitBreakerEnabled: boolean;\r\n  circuitBreakerThreshold: number;\r\n  circuitBreakerTimeout: number;\r\n  modelName?: string;\r\n  maxTokens?: number;\r\n  temperature?: number;\r\n}\r\n\r\nexport interface ExternalServicesConfiguration {\r\n  threatIntelApiKey?: string;\r\n  vulnerabilityScannerApiKey?: string;\r\n  notificationServiceUrl?: string;\r\n  notificationServiceApiKey?: string;\r\n  emailServiceEnabled: boolean;\r\n  emailServiceHost?: string;\r\n  emailServicePort?: number;\r\n  emailServiceUser?: string;\r\n  emailServicePassword?: string;\r\n}\r\n\r\nexport interface CacheConfiguration {\r\n  ttlDefault: number;\r\n  ttlVulnerabilities: number;\r\n  ttlThreats: number;\r\n  ttlAiResults: number;\r\n  maxItems: number;\r\n  compressionEnabled: boolean;\r\n  serializationFormat: 'json' | 'msgpack';\r\n}\r\n\r\nexport interface ValidationConfiguration {\r\n  whitelist: boolean;\r\n  forbidNonWhitelisted: boolean;\r\n  skipMissingProperties: boolean;\r\n  transform: boolean;\r\n  disableErrorMessages: boolean;\r\n  stopAtFirstError: boolean;\r\n}\r\n\r\nexport interface FileUploadConfiguration {\r\n  maxFileSize: number;\r\n  allowedTypes: string[];\r\n  destination: string;\r\n  tempDestination: string;\r\n  cleanupInterval: number;\r\n  virusScanEnabled: boolean;\r\n}\r\n\r\nexport interface PaginationConfiguration {\r\n  defaultLimit: number;\r\n  maxLimit: number;\r\n  defaultOffset: number;\r\n  maxOffset: number;\r\n}\r\n\r\nexport interface DevelopmentConfiguration {\r\n  enablePlayground: boolean;\r\n  enableIntrospection: boolean;\r\n  enableDebugLogging: boolean;\r\n  mockExternalServices: boolean;\r\n  seedDatabase: boolean;\r\n  enableHotReload: boolean;\r\n  enableProfiler: boolean;\r\n}\r\n\r\n/**\r\n * Complete application configuration interface\r\n */\r\nexport interface SentinelConfiguration {\r\n  environment: EnvironmentConfig;\r\n  application: ApplicationConfig;\r\n  database: DatabaseConnectionConfig;\r\n  redis: RedisConnectionConfig;\r\n  security: SecurityConfiguration;\r\n  logging: LoggingConfiguration;\r\n  monitoring: MonitoringConfiguration;\r\n  aiService: AIServiceConfiguration;\r\n  externalServices: ExternalServicesConfiguration;\r\n  cache: CacheConfiguration;\r\n  validation: ValidationConfiguration;\r\n  fileUpload: FileUploadConfiguration;\r\n  pagination: PaginationConfiguration;\r\n  development: DevelopmentConfiguration;\r\n}\r\n\r\n/**\r\n * Environment-specific configuration factory\r\n */\r\nexport const createEnvironmentConfig = (): EnvironmentConfig => {\r\n  const env = (process.env['NODE_ENV'] || 'development') as Environment;\r\n  \r\n  return {\r\n    environment: env,\r\n    isDevelopment: env === 'development',\r\n    isProduction: env === 'production',\r\n    isTest: env === 'test',\r\n    isStaging: env === 'staging',\r\n    debug: env === 'development' || env === 'test',\r\n    hotReload: env === 'development',\r\n  };\r\n};\r\n\r\n/**\r\n * Application configuration factory\r\n */\r\nexport const createApplicationConfig = (): ApplicationConfig => ({\r\n  name: process.env['APP_NAME'] || 'Sentinel Backend',\r\n  version: process.env['APP_VERSION'] || '1.0.0',\r\n  description: process.env['APP_DESCRIPTION'] || 'Sentinel Vulnerability Assessment Platform Backend',\r\n  port: parseInt(process.env['PORT'] || '3000', 10),\r\n  globalPrefix: process.env['API_PREFIX'] || 'api',\r\n  corsOrigin: process.env['CORS_ORIGIN']?.split(',') || ['http://localhost:3001'],\r\n  trustProxy: process.env['TRUST_PROXY'] === 'true',\r\n  shutdownTimeout: parseInt(process.env['SHUTDOWN_TIMEOUT'] || '10000', 10),\r\n});\r\n\r\n/**\r\n * Database configuration factory\r\n */\r\nexport const createDatabaseConfig = (): DatabaseConnectionConfig => ({\r\n  type: (process.env['DATABASE_TYPE'] as any) || 'postgres',\r\n  host: process.env['DATABASE_HOST'] || 'localhost',\r\n  port: parseInt(process.env['DATABASE_PORT'] || '5432', 10),\r\n  username: process.env['DATABASE_USERNAME'] || 'sentinel',\r\n  password: process.env['DATABASE_PASSWORD'] || 'password',\r\n  database: process.env['DATABASE_NAME'] || 'sentinel',\r\n  schema: process.env['DATABASE_SCHEMA'] || 'public',\r\n  ssl: process.env['DATABASE_SSL'] === 'true',\r\n  logging: process.env['DATABASE_LOGGING'] !== 'false',\r\n  synchronize: process.env['DATABASE_SYNCHRONIZE'] === 'true',\r\n  migrationsRun: process.env['DATABASE_MIGRATIONS_RUN'] !== 'false',\r\n  maxConnections: parseInt(process.env['DATABASE_MAX_CONNECTIONS'] || '100', 10),\r\n  connectionTimeout: parseInt(process.env['DATABASE_CONNECTION_TIMEOUT'] || '60000', 10),\r\n  acquireTimeout: parseInt(process.env['DATABASE_ACQUIRE_TIMEOUT'] || '60000', 10),\r\n  idleTimeout: parseInt(process.env['DATABASE_IDLE_TIMEOUT'] || '10000', 10),\r\n  reapInterval: parseInt(process.env['DATABASE_REAP_INTERVAL'] || '1000', 10),\r\n  createRetryDelay: parseInt(process.env['DATABASE_CREATE_RETRY_DELAY'] || '30000', 10),\r\n  retryAttempts: parseInt(process.env['DATABASE_RETRY_ATTEMPTS'] || '3', 10),\r\n});\r\n\r\n/**\r\n * Redis configuration factory\r\n */\r\nexport const createRedisConfig = (): RedisConnectionConfig => ({\r\n  host: process.env['REDIS_HOST'] || 'localhost',\r\n  port: parseInt(process.env['REDIS_PORT'] || '6379', 10),\r\n  password: process.env['REDIS_PASSWORD'] || undefined,\r\n  db: parseInt(process.env['REDIS_DB'] || '0', 10),\r\n  ttl: parseInt(process.env['REDIS_TTL'] || '3600', 10),\r\n  maxRetries: parseInt(process.env['REDIS_MAX_RETRIES'] || '3', 10),\r\n  retryDelay: parseInt(process.env['REDIS_RETRY_DELAY'] || '1000', 10),\r\n  connectTimeout: parseInt(process.env['REDIS_CONNECT_TIMEOUT'] || '10000', 10),\r\n  lazyConnect: process.env['REDIS_LAZY_CONNECT'] !== 'false',\r\n  keepAlive: parseInt(process.env['REDIS_KEEP_ALIVE'] || '30000', 10),\r\n  family: (parseInt(process.env['REDIS_FAMILY'] || '4', 10) as 4 | 6),\r\n});\r\n\r\n/**\r\n * Security configuration factory\r\n */\r\nexport const createSecurityConfig = (): SecurityConfiguration => ({\r\n  jwtSecret: process.env['JWT_SECRET'] || 'your-super-secret-jwt-key-change-in-production',\r\n  jwtExpiresIn: process.env['JWT_EXPIRES_IN'] || '1h',\r\n  jwtRefreshSecret: process.env['JWT_REFRESH_SECRET'] || 'your-super-secret-refresh-key-change-in-production',\r\n  jwtRefreshExpiresIn: process.env['JWT_REFRESH_EXPIRES_IN'] || '7d',\r\n  bcryptRounds: parseInt(process.env['BCRYPT_ROUNDS'] || '12', 10),\r\n  sessionSecret: process.env['SESSION_SECRET'] || 'your-super-secret-session-key-change-in-production',\r\n  rateLimitTtl: parseInt(process.env['RATE_LIMIT_TTL'] || '60', 10),\r\n  rateLimitLimit: parseInt(process.env['RATE_LIMIT_LIMIT'] || '100', 10),\r\n  corsEnabled: process.env['CORS_ENABLED'] !== 'false',\r\n  corsCredentials: process.env['CORS_CREDENTIALS'] !== 'false',\r\n  hstsEnabled: process.env['SECURITY_HSTS_ENABLED'] === 'true',\r\n  hstsMaxAge: parseInt(process.env['SECURITY_HSTS_MAX_AGE'] || '31536000', 10),\r\n  cspEnabled: process.env['SECURITY_CSP_ENABLED'] === 'true',\r\n  frameOptions: (process.env['SECURITY_FRAME_OPTIONS'] as any) || 'DENY',\r\n  contentTypeOptions: process.env['SECURITY_CONTENT_TYPE_OPTIONS'] !== 'false',\r\n});\r\n\r\n/**\r\n * Complete configuration factory\r\n */\r\nexport const createSentinelConfiguration = (): SentinelConfiguration => ({\r\n  environment: createEnvironmentConfig(),\r\n  application: createApplicationConfig(),\r\n  database: createDatabaseConfig(),\r\n  redis: createRedisConfig(),\r\n  security: createSecurityConfig(),\r\n  logging: {\r\n    level: (process.env['LOG_LEVEL'] as any) || 'info',\r\n    format: (process.env['LOG_FORMAT'] as any) || 'json',\r\n    fileEnabled: process.env['LOG_FILE_ENABLED'] === 'true',\r\n    filePath: process.env['LOG_FILE_PATH'] || 'logs/app.log',\r\n    fileMaxSize: process.env['LOG_FILE_MAX_SIZE'] || '10m',\r\n    fileMaxFiles: parseInt(process.env['LOG_FILE_MAX_FILES'] || '5', 10),\r\n    consoleEnabled: process.env['LOG_CONSOLE_ENABLED'] !== 'false',\r\n    auditEnabled: process.env['LOG_AUDIT_ENABLED'] === 'true',\r\n    auditPath: process.env['LOG_AUDIT_PATH'] || 'logs/audit.log',\r\n    correlationIdEnabled: process.env['LOG_CORRELATION_ID_ENABLED'] !== 'false',\r\n    structuredLogging: process.env['LOG_STRUCTURED'] !== 'false',\r\n  },\r\n  monitoring: {\r\n    healthCheckEnabled: process.env['HEALTH_CHECK_ENABLED'] !== 'false',\r\n    healthCheckDatabaseEnabled: process.env['HEALTH_CHECK_DATABASE_ENABLED'] !== 'false',\r\n    healthCheckRedisEnabled: process.env['HEALTH_CHECK_REDIS_ENABLED'] !== 'false',\r\n    memoryHeapThreshold: parseInt(process.env['HEALTH_CHECK_MEMORY_HEAP_THRESHOLD'] || '150', 10),\r\n    memoryRssThreshold: parseInt(process.env['HEALTH_CHECK_MEMORY_RSS_THRESHOLD'] || '150', 10),\r\n    metricsEnabled: process.env['METRICS_ENABLED'] === 'true',\r\n    metricsPort: parseInt(process.env['METRICS_PORT'] || '9090', 10),\r\n    metricsPath: process.env['METRICS_PATH'] || '/metrics',\r\n    tracingEnabled: process.env['TRACING_ENABLED'] === 'true',\r\n    tracingServiceName: process.env['TRACING_SERVICE_NAME'] || 'sentinel-backend',\r\n    tracingJaegerEndpoint: process.env['TRACING_JAEGER_ENDPOINT'],\r\n  },\r\n  aiService: {\r\n    enabled: process.env['AI_SERVICE_ENABLED'] === 'true',\r\n    url: process.env['AI_SERVICE_URL'],\r\n    apiKey: process.env['AI_SERVICE_API_KEY'],\r\n    timeout: parseInt(process.env['AI_SERVICE_TIMEOUT'] || '30000', 10),\r\n    retryAttempts: parseInt(process.env['AI_SERVICE_RETRY_ATTEMPTS'] || '3', 10),\r\n    retryDelay: parseInt(process.env['AI_SERVICE_RETRY_DELAY'] || '1000', 10),\r\n    circuitBreakerEnabled: process.env['AI_SERVICE_CIRCUIT_BREAKER_ENABLED'] === 'true',\r\n    circuitBreakerThreshold: parseInt(process.env['AI_SERVICE_CIRCUIT_BREAKER_THRESHOLD'] || '5', 10),\r\n    circuitBreakerTimeout: parseInt(process.env['AI_SERVICE_CIRCUIT_BREAKER_TIMEOUT'] || '60000', 10),\r\n    modelName: process.env['AI_SERVICE_MODEL_NAME'],\r\n    maxTokens: process.env['AI_SERVICE_MAX_TOKENS'] ? parseInt(process.env['AI_SERVICE_MAX_TOKENS'], 10) : undefined,\r\n    temperature: process.env['AI_SERVICE_TEMPERATURE'] ? parseFloat(process.env['AI_SERVICE_TEMPERATURE']) : undefined,\r\n  },\r\n  externalServices: {\r\n    threatIntelApiKey: process.env['THREAT_INTEL_API_KEY'],\r\n    vulnerabilityScannerApiKey: process.env['VULNERABILITY_SCANNER_API_KEY'],\r\n    notificationServiceUrl: process.env['NOTIFICATION_SERVICE_URL'],\r\n    notificationServiceApiKey: process.env['NOTIFICATION_SERVICE_API_KEY'],\r\n    emailServiceEnabled: process.env['EMAIL_SERVICE_ENABLED'] === 'true',\r\n    emailServiceHost: process.env['EMAIL_SERVICE_HOST'],\r\n    emailServicePort: process.env['EMAIL_SERVICE_PORT'] ? parseInt(process.env['EMAIL_SERVICE_PORT'], 10) : undefined,\r\n    emailServiceUser: process.env['EMAIL_SERVICE_USER'],\r\n    emailServicePassword: process.env['EMAIL_SERVICE_PASSWORD'],\r\n  },\r\n  cache: {\r\n    ttlDefault: parseInt(process.env['CACHE_TTL_DEFAULT'] || '300', 10),\r\n    ttlVulnerabilities: parseInt(process.env['CACHE_TTL_VULNERABILITIES'] || '1800', 10),\r\n    ttlThreats: parseInt(process.env['CACHE_TTL_THREATS'] || '900', 10),\r\n    ttlAiResults: parseInt(process.env['CACHE_TTL_AI_RESULTS'] || '3600', 10),\r\n    maxItems: parseInt(process.env['CACHE_MAX_ITEMS'] || '10000', 10),\r\n    compressionEnabled: process.env['CACHE_COMPRESSION_ENABLED'] === 'true',\r\n    serializationFormat: (process.env['CACHE_SERIALIZATION_FORMAT'] as any) || 'json',\r\n  },\r\n  validation: {\r\n    whitelist: process.env['VALIDATION_WHITELIST'] !== 'false',\r\n    forbidNonWhitelisted: process.env['VALIDATION_FORBID_NON_WHITELISTED'] !== 'false',\r\n    skipMissingProperties: process.env['VALIDATION_SKIP_MISSING_PROPERTIES'] === 'true',\r\n    transform: process.env['VALIDATION_TRANSFORM'] !== 'false',\r\n    disableErrorMessages: process.env['VALIDATION_DISABLE_ERROR_MESSAGES'] === 'true',\r\n    stopAtFirstError: process.env['VALIDATION_STOP_AT_FIRST_ERROR'] === 'true',\r\n  },\r\n  fileUpload: {\r\n    maxFileSize: parseInt(process.env['UPLOAD_MAX_FILE_SIZE'] || '10485760', 10), // 10MB\r\n    allowedTypes: (process.env['UPLOAD_ALLOWED_TYPES'] || 'application/json,text/csv,application/xml').split(','),\r\n    destination: process.env['UPLOAD_DESTINATION'] || './uploads',\r\n    tempDestination: process.env['UPLOAD_TEMP_DESTINATION'] || './temp',\r\n    cleanupInterval: parseInt(process.env['UPLOAD_CLEANUP_INTERVAL'] || '3600000', 10), // 1 hour\r\n    virusScanEnabled: process.env['UPLOAD_VIRUS_SCAN_ENABLED'] === 'true',\r\n  },\r\n  pagination: {\r\n    defaultLimit: parseInt(process.env['PAGINATION_DEFAULT_LIMIT'] || '20', 10),\r\n    maxLimit: parseInt(process.env['PAGINATION_MAX_LIMIT'] || '100', 10),\r\n    defaultOffset: parseInt(process.env['PAGINATION_DEFAULT_OFFSET'] || '0', 10),\r\n    maxOffset: parseInt(process.env['PAGINATION_MAX_OFFSET'] || '10000', 10),\r\n  },\r\n  development: {\r\n    enablePlayground: process.env['DEV_ENABLE_PLAYGROUND'] === 'true',\r\n    enableIntrospection: process.env['DEV_ENABLE_INTROSPECTION'] === 'true',\r\n    enableDebugLogging: process.env['DEV_ENABLE_DEBUG_LOGGING'] === 'true',\r\n    mockExternalServices: process.env['DEV_MOCK_EXTERNAL_SERVICES'] === 'true',\r\n    seedDatabase: process.env['DEV_SEED_DATABASE'] === 'true',\r\n    enableHotReload: process.env['DEV_ENABLE_HOT_RELOAD'] !== 'false',\r\n    enableProfiler: process.env['DEV_ENABLE_PROFILER'] === 'true',\r\n  },\r\n});\r\n\r\n/**\r\n * NestJS configuration registration\r\n */\r\nexport default registerAs('sentinel', createSentinelConfiguration);\r\n\r\n/**\r\n * Configuration service for dependency injection\r\n */\r\n@Injectable()\r\nexport class SentinelConfigurationService {\r\n  private readonly config: SentinelConfiguration;\r\n\r\n  constructor(private readonly configService: ConfigService) {\r\n    this.config = this.configService.get<SentinelConfiguration>('sentinel')!;\r\n  }\r\n\r\n  get environment(): EnvironmentConfig {\r\n    return this.config.environment;\r\n  }\r\n\r\n  get application(): ApplicationConfig {\r\n    return this.config.application;\r\n  }\r\n\r\n  get database(): DatabaseConnectionConfig {\r\n    return this.config.database;\r\n  }\r\n\r\n  get redis(): RedisConnectionConfig {\r\n    return this.config.redis;\r\n  }\r\n\r\n  get security(): SecurityConfiguration {\r\n    return this.config.security;\r\n  }\r\n\r\n  get logging(): LoggingConfiguration {\r\n    return this.config.logging;\r\n  }\r\n\r\n  get monitoring(): MonitoringConfiguration {\r\n    return this.config.monitoring;\r\n  }\r\n\r\n  get aiService(): AIServiceConfiguration {\r\n    return this.config.aiService;\r\n  }\r\n\r\n  get externalServices(): ExternalServicesConfiguration {\r\n    return this.config.externalServices;\r\n  }\r\n\r\n  get cache(): CacheConfiguration {\r\n    return this.config.cache;\r\n  }\r\n\r\n  get validation(): ValidationConfiguration {\r\n    return this.config.validation;\r\n  }\r\n\r\n  get fileUpload(): FileUploadConfiguration {\r\n    return this.config.fileUpload;\r\n  }\r\n\r\n  get pagination(): PaginationConfiguration {\r\n    return this.config.pagination;\r\n  }\r\n\r\n  get development(): DevelopmentConfiguration {\r\n    return this.config.development;\r\n  }\r\n\r\n  /**\r\n   * Get the complete configuration object\r\n   */\r\n  getAll(): SentinelConfiguration {\r\n    return this.config;\r\n  }\r\n\r\n  /**\r\n   * Check if running in development mode\r\n   */\r\n  isDevelopment(): boolean {\r\n    return this.config.environment.isDevelopment;\r\n  }\r\n\r\n  /**\r\n   * Check if running in production mode\r\n   */\r\n  isProduction(): boolean {\r\n    return this.config.environment.isProduction;\r\n  }\r\n\r\n  /**\r\n   * Check if running in test mode\r\n   */\r\n  isTest(): boolean {\r\n    return this.config.environment.isTest;\r\n  }\r\n\r\n  /**\r\n   * Check if running in staging mode\r\n   */\r\n  isStaging(): boolean {\r\n    return this.config.environment.isStaging;\r\n  }\r\n}"], "version": 3}