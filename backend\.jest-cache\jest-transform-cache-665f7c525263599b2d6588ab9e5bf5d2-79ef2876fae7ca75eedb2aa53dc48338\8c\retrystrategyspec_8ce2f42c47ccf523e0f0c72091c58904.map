{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\patterns\\retry-strategy.spec.ts", "mappings": ";;AAAA,kEAA+E;AAE/E,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;IAC9B,UAAU,CAAC,GAAG,EAAE;QACd,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;YACpE,MAAM,QAAQ,GAAG,IAAI,8BAAa,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC;YACvD,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,MAAM,mBAAmB,GAAG,KAAK,IAAI,EAAE;gBACrC,SAAS,EAAE,CAAC;gBACZ,OAAO,WAAW,SAAS,EAAE,CAAC;YAChC,CAAC,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACjC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,QAAQ,GAAG,IAAI,8BAAa,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;YACtE,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,MAAM,cAAc,GAAG,KAAK,IAAI,EAAE;gBAChC,SAAS,EAAE,CAAC;gBACZ,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;oBAClB,MAAM,IAAI,KAAK,CAAC,WAAW,SAAS,SAAS,CAAC,CAAC;gBACjD,CAAC;gBACD,OAAO,kBAAkB,CAAC;YAC5B,CAAC,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACxC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,MAAM,QAAQ,GAAG,IAAI,8BAAa,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;YACtE,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,MAAM,sBAAsB,GAAG,KAAK,IAAI,EAAE;gBACxC,SAAS,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,WAAW,SAAS,SAAS,CAAC,CAAC;YACjD,CAAC,CAAC;YAEF,MAAM,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;YAC3F,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,MAAM,GAAa,EAAE,CAAC;YAC5B,MAAM,kBAAkB,GAAG,MAAM,CAAC,UAAU,CAAC;YAE7C,oCAAoC;YACpC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;gBACnE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACnB,OAAO,kBAAkB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,+BAA+B;YACzE,CAAC,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,IAAI,8BAAa,CAAC;gBACjC,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,GAAG;gBACd,iBAAiB,EAAE,CAAC;gBACpB,MAAM,EAAE,KAAK,EAAE,wCAAwC;aACxD,CAAC,CAAC;YAEH,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,MAAM,gBAAgB,GAAG,KAAK,IAAI,EAAE;gBAClC,SAAS,EAAE,CAAC;gBACZ,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;oBAClB,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;gBAClC,CAAC;gBACD,OAAO,SAAS,CAAC;YACnB,CAAC,CAAC;YAEF,MAAM,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YAEzC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc;YAC9C,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,yBAAyB;YACtD,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,8BAA8B;YAE3D,8BAA8B;YAC9B,MAAM,CAAC,UAAU,GAAG,kBAAkB,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,MAAM,GAAa,EAAE,CAAC;YAC5B,MAAM,kBAAkB,GAAG,MAAM,CAAC,UAAU,CAAC;YAE7C,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;gBACnE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACnB,OAAO,kBAAkB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,IAAI,8BAAa,CAAC;gBACjC,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,GAAG;gBACd,QAAQ,EAAE,GAAG;gBACb,iBAAiB,EAAE,CAAC;gBACpB,MAAM,EAAE,KAAK;aACd,CAAC,CAAC;YAEH,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,MAAM,gBAAgB,GAAG,KAAK,IAAI,EAAE;gBAClC,SAAS,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;YAClC,CAAC,CAAC;YAEF,MAAM,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YAEnE,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,yBAAyB;YACtD,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,mCAAmC;YAChE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,kCAAkC;YAE/D,MAAM,CAAC,UAAU,GAAG,kBAAkB,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,MAAM,MAAM,GAAa,EAAE,CAAC;YAC5B,MAAM,kBAAkB,GAAG,MAAM,CAAC,UAAU,CAAC;YAC7C,MAAM,kBAAkB,GAAG,IAAI,CAAC,MAAM,CAAC;YAEvC,gDAAgD;YAChD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,EAAE;iBACpB,mBAAmB,CAAC,GAAG,CAAC,CAAC,0BAA0B;iBACnD,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,2BAA2B;YAExD,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;gBACnE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACnB,OAAO,kBAAkB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,IAAI,8BAAa,CAAC;gBACjC,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,GAAG;gBACd,iBAAiB,EAAE,CAAC;gBACpB,MAAM,EAAE,IAAI;aACb,CAAC,CAAC;YAEH,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,MAAM,gBAAgB,GAAG,KAAK,IAAI,EAAE;gBAClC,SAAS,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;YAClC,CAAC,CAAC;YAEF,MAAM,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YAEnE,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/B,wDAAwD;YACxD,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,+BAA+B;YAC3D,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,gCAAgC;YAE7D,IAAI,CAAC,MAAM,GAAG,kBAAkB,CAAC;YACjC,MAAM,CAAC,UAAU,GAAG,kBAAkB,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,YAAa,SAAQ,KAAK;gBAC9B,YAAY,OAAe;oBACzB,KAAK,CAAC,OAAO,CAAC,CAAC;oBACf,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC;gBAC7B,CAAC;aACF;YAED,MAAM,QAAQ,GAAG,IAAI,8BAAa,CAAC;gBACjC,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,EAAE;gBACb,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,YAAY,YAAY;aAClD,CAAC,CAAC;YAEH,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,8CAA8C;YAC9C,MAAM,qBAAqB,GAAG,KAAK,IAAI,EAAE;gBACvC,SAAS,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;YACtC,CAAC,CAAC;YAEF,MAAM,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;YAC1F,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE1B,mBAAmB;YACnB,SAAS,GAAG,CAAC,CAAC;YAEd,oCAAoC;YACpC,MAAM,kBAAkB,GAAG,KAAK,IAAI,EAAE;gBACpC,SAAS,EAAE,CAAC;gBACZ,MAAM,IAAI,YAAY,CAAC,eAAe,CAAC,CAAC;YAC1C,CAAC,CAAC;YAEF,MAAM,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;YACpF,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;YAC1B,MAAM,QAAQ,GAAG,IAAI,8BAAa,CAAC;gBACjC,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,EAAE;gBACb,OAAO;aACR,CAAC,CAAC;YAEH,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,MAAM,gBAAgB,GAAG,KAAK,IAAI,EAAE;gBAClC,SAAS,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,WAAW,SAAS,SAAS,CAAC,CAAC;YACjD,CAAC,CAAC;YAEF,MAAM,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YAEnE,MAAM,CAAC,OAAO,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,wBAAwB;YAClE,MAAM,CAAC,OAAO,CAAC,CAAC,uBAAuB,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;YACjE,MAAM,CAAC,OAAO,CAAC,CAAC,uBAAuB,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,QAAQ,GAAG,IAAI,8BAAa,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;YACtE,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,MAAM,cAAc,GAAG,KAAK,IAAI,EAAE;gBAChC,SAAS,EAAE,CAAC;gBACZ,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;oBAClB,MAAM,IAAI,KAAK,CAAC,WAAW,SAAS,SAAS,CAAC,CAAC;gBACjD,CAAC;gBACD,OAAO,uBAAuB,CAAC;YACjC,CAAC,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;YAEjE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,KAAK,IAAI,EAAE;YACvE,MAAM,QAAQ,GAAG,IAAI,8BAAa,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC;YAEvD,MAAM,mBAAmB,GAAG,KAAK,IAAI,EAAE,CAAC,mBAAmB,CAAC;YAE5D,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,CAAC;YAEtE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC/C,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,QAAQ,GAAG,gCAAe,CAAC,kBAAkB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAC3D,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,MAAM,cAAc,GAAG,KAAK,IAAI,EAAE;gBAChC,SAAS,EAAE,CAAC;gBACZ,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;oBAClB,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;gBAClC,CAAC;gBACD,OAAO,qBAAqB,CAAC;YAC/B,CAAC,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAC3C,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,MAAM,GAAa,EAAE,CAAC;YAC5B,MAAM,kBAAkB,GAAG,MAAM,CAAC,UAAU,CAAC;YAE7C,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;gBACnE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACnB,OAAO,kBAAkB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,gCAAe,CAAC,aAAa,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;YACvD,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,MAAM,gBAAgB,GAAG,KAAK,IAAI,EAAE;gBAClC,SAAS,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;YAClC,CAAC,CAAC;YAEF,MAAM,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YAEnE,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,+BAA+B;YAC5D,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAE5B,MAAM,CAAC,UAAU,GAAG,kBAAkB,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,MAAM,GAAa,EAAE,CAAC;YAC5B,MAAM,kBAAkB,GAAG,MAAM,CAAC,UAAU,CAAC;YAE7C,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;gBACnE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACnB,OAAO,kBAAkB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,gCAAe,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;YACpD,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,MAAM,gBAAgB,GAAG,KAAK,IAAI,EAAE;gBAClC,SAAS,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;YAClC,CAAC,CAAC;YAEF,MAAM,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YAEnE,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC5B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAE5B,MAAM,CAAC,UAAU,GAAG,kBAAkB,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,MAAM,GAAa,EAAE,CAAC;YAC5B,MAAM,kBAAkB,GAAG,MAAM,CAAC,UAAU,CAAC;YAE7C,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;gBACnE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACnB,OAAO,kBAAkB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,gCAAe,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9C,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,MAAM,gBAAgB,GAAG,KAAK,IAAI,EAAE;gBAClC,SAAS,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;YAClC,CAAC,CAAC;YAEF,MAAM,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YAEnE,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW;YACtC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE1B,MAAM,CAAC,UAAU,GAAG,kBAAkB,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,YAAa,SAAQ,KAAK;gBAC9B,YAAY,OAAe;oBACzB,KAAK,CAAC,OAAO,CAAC,CAAC;oBACf,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC;gBAC7B,CAAC;aACF;YAED,MAAM,YAAa,SAAQ,KAAK;gBAC9B,YAAY,OAAe;oBACzB,KAAK,CAAC,OAAO,CAAC,CAAC;oBACf,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC;gBAC7B,CAAC;aACF;YAED,MAAM,QAAQ,GAAG,gCAAe,CAAC,YAAY,CAAC,CAAC,YAAY,EAAE,YAAY,CAAC,EAAE;gBAC1E,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,EAAE;aACd,CAAC,CAAC;YAEH,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,iCAAiC;YACjC,MAAM,uBAAuB,GAAG,KAAK,IAAI,EAAE;gBACzC,SAAS,EAAE,CAAC;gBACZ,MAAM,IAAI,YAAY,CAAC,gBAAgB,CAAC,CAAC;YAC3C,CAAC,CAAC;YAEF,MAAM,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YAC1F,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE1B,mBAAmB;YACnB,SAAS,GAAG,CAAC,CAAC;YAEd,iCAAiC;YACjC,MAAM,uBAAuB,GAAG,KAAK,IAAI,EAAE;gBACzC,SAAS,EAAE,CAAC;gBACZ,MAAM,IAAI,YAAY,CAAC,kBAAkB,CAAC,CAAC;YAC7C,CAAC,CAAC;YAEF,MAAM,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;YAC5F,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE1B,mBAAmB;YACnB,SAAS,GAAG,CAAC,CAAC;YAEd,qCAAqC;YACrC,MAAM,qBAAqB,GAAG,KAAK,IAAI,EAAE;gBACvC,SAAS,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;YACjC,CAAC,CAAC;YAEF,MAAM,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YACrF,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,QAAQ,GAAG,IAAI,8BAAa,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC;YAEvD,MAAM,SAAS,GAAG,KAAK,IAAI,EAAE,CAAC,oBAAoB,CAAC;YAEnD,iDAAiD;YACjD,wEAAwE;YACxE,MAAM,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,QAAQ,GAAG,IAAI,8BAAa,CAAC;gBACjC,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,CAAC,GAAG,EAAE,iBAAiB;aACnC,CAAC,CAAC;YAEH,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,MAAM,gBAAgB,GAAG,KAAK,IAAI,EAAE;gBAClC,SAAS,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;YAClC,CAAC,CAAC;YAEF,MAAM,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACnE,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,4CAA4C;QACzE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,QAAQ,GAAG,IAAI,8BAAa,CAAC;gBACjC,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,MAAM,CAAC,gBAAgB;gBAClC,QAAQ,EAAE,GAAG,EAAE,uBAAuB;gBACtC,MAAM,EAAE,KAAK,EAAE,yCAAyC;aACzD,CAAC,CAAC;YAEH,MAAM,MAAM,GAAa,EAAE,CAAC;YAC5B,MAAM,kBAAkB,GAAG,MAAM,CAAC,UAAU,CAAC;YAE7C,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;gBACnE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACnB,OAAO,kBAAkB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;YAEH,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,MAAM,gBAAgB,GAAG,KAAK,IAAI,EAAE;gBAClC,SAAS,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;YAClC,CAAC,CAAC;YAEF,MAAM,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YAEnE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,+BAA+B;YAE5D,MAAM,CAAC,UAAU,GAAG,kBAAkB,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,QAAQ,GAAG,IAAI,8BAAa,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;YAEtE,MAAM,WAAW,GAAG,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;YAC9C,WAAW,CAAC,IAAI,GAAG,aAAa,CAAC;YAChC,WAAmB,CAAC,cAAc,GAAG,cAAc,CAAC;YAErD,MAAM,gBAAgB,GAAG,KAAK,IAAI,EAAE;gBAClC,MAAM,WAAW,CAAC;YACpB,CAAC,CAAC;YAEF,IAAI,CAAC;gBACH,MAAM,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;gBACzC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACnC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,oCAAoC;gBACrE,MAAM,CAAE,KAAa,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,MAAM,QAAQ,GAAG,IAAI,8BAAa,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;YAEtE,MAAM,qBAAqB,GAAG,KAAK,IAAI,EAAE;gBACvC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;gBACtD,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;YACjC,CAAC,CAAC;YAEF,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,MAAM,iBAAiB,GAAG,KAAK,IAAI,EAAE;gBACnC,SAAS,EAAE,CAAC;gBACZ,OAAO,MAAM,qBAAqB,EAAE,CAAC;YACvC,CAAC,CAAC;YAEF,MAAM,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YACjF,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\patterns\\retry-strategy.spec.ts"], "sourcesContent": ["import { RetryStrategy, RetryStrategies } from '../../patterns/retry-strategy';\r\n\r\ndescribe('Retry Strategy', () => {\r\n  beforeEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('RetryStrategy class', () => {\r\n    it('should succeed on first attempt when no error occurs', async () => {\r\n      const strategy = new RetryStrategy({ maxAttempts: 3 });\r\n      let callCount = 0;\r\n\r\n      const successfulOperation = async () => {\r\n        callCount++;\r\n        return `success-${callCount}`;\r\n      };\r\n\r\n      const result = await strategy.execute(successfulOperation);\r\n      expect(result).toBe('success-1');\r\n      expect(callCount).toBe(1);\r\n    });\r\n\r\n    it('should retry failed operations up to max attempts', async () => {\r\n      const strategy = new RetryStrategy({ maxAttempts: 3, baseDelay: 10 });\r\n      let callCount = 0;\r\n\r\n      const flakyOperation = async () => {\r\n        callCount++;\r\n        if (callCount < 3) {\r\n          throw new Error(`Attempt ${callCount} failed`);\r\n        }\r\n        return 'eventual success';\r\n      };\r\n\r\n      const result = await strategy.execute(flakyOperation);\r\n      expect(result).toBe('eventual success');\r\n      expect(callCount).toBe(3);\r\n    });\r\n\r\n    it('should throw last error after max attempts exceeded', async () => {\r\n      const strategy = new RetryStrategy({ maxAttempts: 2, baseDelay: 10 });\r\n      let callCount = 0;\r\n\r\n      const alwaysFailingOperation = async () => {\r\n        callCount++;\r\n        throw new Error(`Attempt ${callCount} failed`);\r\n      };\r\n\r\n      await expect(strategy.execute(alwaysFailingOperation)).rejects.toThrow('Attempt 2 failed');\r\n      expect(callCount).toBe(2);\r\n    });\r\n\r\n    it('should apply exponential backoff delays', async () => {\r\n      const delays: number[] = [];\r\n      const originalSetTimeout = global.setTimeout;\r\n\r\n      // Mock setTimeout to capture delays\r\n      global.setTimeout = jest.fn().mockImplementation((callback, delay) => {\r\n        delays.push(delay);\r\n        return originalSetTimeout(callback, 0); // Execute immediately for test\r\n      });\r\n\r\n      const strategy = new RetryStrategy({\r\n        maxAttempts: 3,\r\n        baseDelay: 100,\r\n        backoffMultiplier: 2,\r\n        jitter: false, // Disable jitter for predictable delays\r\n      });\r\n\r\n      let callCount = 0;\r\n      const failingOperation = async () => {\r\n        callCount++;\r\n        if (callCount < 3) {\r\n          throw new Error('Retry needed');\r\n        }\r\n        return 'success';\r\n      };\r\n\r\n      await strategy.execute(failingOperation);\r\n\r\n      expect(delays).toHaveLength(2); // Two retries\r\n      expect(delays[0]).toBe(100); // First retry: baseDelay\r\n      expect(delays[1]).toBe(200); // Second retry: baseDelay * 2\r\n\r\n      // Restore original setTimeout\r\n      global.setTimeout = originalSetTimeout;\r\n    });\r\n\r\n    it('should respect maximum delay limit', async () => {\r\n      const delays: number[] = [];\r\n      const originalSetTimeout = global.setTimeout;\r\n\r\n      global.setTimeout = jest.fn().mockImplementation((callback, delay) => {\r\n        delays.push(delay);\r\n        return originalSetTimeout(callback, 0);\r\n      });\r\n\r\n      const strategy = new RetryStrategy({\r\n        maxAttempts: 4,\r\n        baseDelay: 100,\r\n        maxDelay: 150,\r\n        backoffMultiplier: 2,\r\n        jitter: false,\r\n      });\r\n\r\n      let callCount = 0;\r\n      const failingOperation = async () => {\r\n        callCount++;\r\n        throw new Error('Always fails');\r\n      };\r\n\r\n      await expect(strategy.execute(failingOperation)).rejects.toThrow();\r\n\r\n      expect(delays).toHaveLength(3);\r\n      expect(delays[0]).toBe(100); // First retry: baseDelay\r\n      expect(delays[1]).toBe(150); // Second retry: capped at maxDelay\r\n      expect(delays[2]).toBe(150); // Third retry: capped at maxDelay\r\n\r\n      global.setTimeout = originalSetTimeout;\r\n    });\r\n\r\n    it('should apply jitter to delays', async () => {\r\n      const delays: number[] = [];\r\n      const originalSetTimeout = global.setTimeout;\r\n      const originalMathRandom = Math.random;\r\n\r\n      // Mock Math.random to return predictable values\r\n      Math.random = jest.fn()\r\n        .mockReturnValueOnce(0.5) // First retry: 0.5 jitter\r\n        .mockReturnValueOnce(0.8); // Second retry: 0.8 jitter\r\n\r\n      global.setTimeout = jest.fn().mockImplementation((callback, delay) => {\r\n        delays.push(delay);\r\n        return originalSetTimeout(callback, 0);\r\n      });\r\n\r\n      const strategy = new RetryStrategy({\r\n        maxAttempts: 3,\r\n        baseDelay: 100,\r\n        backoffMultiplier: 2,\r\n        jitter: true,\r\n      });\r\n\r\n      let callCount = 0;\r\n      const failingOperation = async () => {\r\n        callCount++;\r\n        throw new Error('Always fails');\r\n      };\r\n\r\n      await expect(strategy.execute(failingOperation)).rejects.toThrow();\r\n\r\n      expect(delays).toHaveLength(2);\r\n      // With jitter, delay = baseDelay * (0.5 + random * 0.5)\r\n      expect(delays[0]).toBe(75); // 100 * (0.5 + 0.5 * 0.5) = 75\r\n      expect(delays[1]).toBe(180); // 200 * (0.5 + 0.8 * 0.5) = 180\r\n\r\n      Math.random = originalMathRandom;\r\n      global.setTimeout = originalSetTimeout;\r\n    });\r\n\r\n    it('should respect retryOn condition', async () => {\r\n      class NetworkError extends Error {\r\n        constructor(message: string) {\r\n          super(message);\r\n          this.name = 'NetworkError';\r\n        }\r\n      }\r\n\r\n      const strategy = new RetryStrategy({\r\n        maxAttempts: 3,\r\n        baseDelay: 10,\r\n        retryOn: (error) => error instanceof NetworkError,\r\n      });\r\n\r\n      let callCount = 0;\r\n\r\n      // Non-retryable error should fail immediately\r\n      const nonRetryableOperation = async () => {\r\n        callCount++;\r\n        throw new Error('Validation error');\r\n      };\r\n\r\n      await expect(strategy.execute(nonRetryableOperation)).rejects.toThrow('Validation error');\r\n      expect(callCount).toBe(1);\r\n\r\n      // Reset call count\r\n      callCount = 0;\r\n\r\n      // Retryable error should be retried\r\n      const retryableOperation = async () => {\r\n        callCount++;\r\n        throw new NetworkError('Network error');\r\n      };\r\n\r\n      await expect(strategy.execute(retryableOperation)).rejects.toThrow('Network error');\r\n      expect(callCount).toBe(3);\r\n    });\r\n\r\n    it('should call onRetry callback', async () => {\r\n      const onRetry = jest.fn();\r\n      const strategy = new RetryStrategy({\r\n        maxAttempts: 3,\r\n        baseDelay: 10,\r\n        onRetry,\r\n      });\r\n\r\n      let callCount = 0;\r\n      const failingOperation = async () => {\r\n        callCount++;\r\n        throw new Error(`Attempt ${callCount} failed`);\r\n      };\r\n\r\n      await expect(strategy.execute(failingOperation)).rejects.toThrow();\r\n\r\n      expect(onRetry).toHaveBeenCalledTimes(2); // Called for each retry\r\n      expect(onRetry).toHaveBeenNthCalledWith(1, expect.any(Error), 1);\r\n      expect(onRetry).toHaveBeenNthCalledWith(2, expect.any(Error), 2);\r\n    });\r\n\r\n    it('should provide detailed execution results', async () => {\r\n      const strategy = new RetryStrategy({ maxAttempts: 3, baseDelay: 10 });\r\n      let callCount = 0;\r\n\r\n      const flakyOperation = async () => {\r\n        callCount++;\r\n        if (callCount < 3) {\r\n          throw new Error(`Attempt ${callCount} failed`);\r\n        }\r\n        return 'success after retries';\r\n      };\r\n\r\n      const result = await strategy.executeWithDetails(flakyOperation);\r\n\r\n      expect(result.result).toBe('success after retries');\r\n      expect(result.attempts).toBe(3);\r\n      expect(result.totalDelay).toBeGreaterThan(0);\r\n      expect(result.errors).toHaveLength(2);\r\n      expect(result.errors[0].message).toBe('Attempt 1 failed');\r\n      expect(result.errors[1].message).toBe('Attempt 2 failed');\r\n    });\r\n\r\n    it('should handle immediate success with executeWithDetails', async () => {\r\n      const strategy = new RetryStrategy({ maxAttempts: 3 });\r\n\r\n      const successfulOperation = async () => 'immediate success';\r\n\r\n      const result = await strategy.executeWithDetails(successfulOperation);\r\n\r\n      expect(result.result).toBe('immediate success');\r\n      expect(result.attempts).toBe(1);\r\n      expect(result.totalDelay).toBe(0);\r\n      expect(result.errors).toHaveLength(0);\r\n    });\r\n  });\r\n\r\n  describe('RetryStrategies factory methods', () => {\r\n    it('should create exponential backoff strategy', async () => {\r\n      const strategy = RetryStrategies.exponentialBackoff(3, 50);\r\n      let callCount = 0;\r\n\r\n      const flakyOperation = async () => {\r\n        callCount++;\r\n        if (callCount < 3) {\r\n          throw new Error('Retry needed');\r\n        }\r\n        return 'exponential success';\r\n      };\r\n\r\n      const result = await strategy.execute(flakyOperation);\r\n      expect(result).toBe('exponential success');\r\n      expect(callCount).toBe(3);\r\n    });\r\n\r\n    it('should create linear backoff strategy', async () => {\r\n      const delays: number[] = [];\r\n      const originalSetTimeout = global.setTimeout;\r\n\r\n      global.setTimeout = jest.fn().mockImplementation((callback, delay) => {\r\n        delays.push(delay);\r\n        return originalSetTimeout(callback, 0);\r\n      });\r\n\r\n      const strategy = RetryStrategies.linearBackoff(3, 100);\r\n      let callCount = 0;\r\n\r\n      const failingOperation = async () => {\r\n        callCount++;\r\n        throw new Error('Always fails');\r\n      };\r\n\r\n      await expect(strategy.execute(failingOperation)).rejects.toThrow();\r\n\r\n      expect(delays).toHaveLength(2);\r\n      expect(delays[0]).toBe(100); // Linear: same delay each time\r\n      expect(delays[1]).toBe(100);\r\n\r\n      global.setTimeout = originalSetTimeout;\r\n    });\r\n\r\n    it('should create fixed delay strategy', async () => {\r\n      const delays: number[] = [];\r\n      const originalSetTimeout = global.setTimeout;\r\n\r\n      global.setTimeout = jest.fn().mockImplementation((callback, delay) => {\r\n        delays.push(delay);\r\n        return originalSetTimeout(callback, 0);\r\n      });\r\n\r\n      const strategy = RetryStrategies.fixedDelay(3, 200);\r\n      let callCount = 0;\r\n\r\n      const failingOperation = async () => {\r\n        callCount++;\r\n        throw new Error('Always fails');\r\n      };\r\n\r\n      await expect(strategy.execute(failingOperation)).rejects.toThrow();\r\n\r\n      expect(delays).toHaveLength(2);\r\n      expect(delays[0]).toBe(200);\r\n      expect(delays[1]).toBe(200);\r\n\r\n      global.setTimeout = originalSetTimeout;\r\n    });\r\n\r\n    it('should create immediate retry strategy', async () => {\r\n      const delays: number[] = [];\r\n      const originalSetTimeout = global.setTimeout;\r\n\r\n      global.setTimeout = jest.fn().mockImplementation((callback, delay) => {\r\n        delays.push(delay);\r\n        return originalSetTimeout(callback, 0);\r\n      });\r\n\r\n      const strategy = RetryStrategies.immediate(3);\r\n      let callCount = 0;\r\n\r\n      const failingOperation = async () => {\r\n        callCount++;\r\n        throw new Error('Always fails');\r\n      };\r\n\r\n      await expect(strategy.execute(failingOperation)).rejects.toThrow();\r\n\r\n      expect(delays).toHaveLength(2);\r\n      expect(delays[0]).toBe(0); // No delay\r\n      expect(delays[1]).toBe(0);\r\n\r\n      global.setTimeout = originalSetTimeout;\r\n    });\r\n\r\n    it('should create error-specific retry strategy', async () => {\r\n      class NetworkError extends Error {\r\n        constructor(message: string) {\r\n          super(message);\r\n          this.name = 'NetworkError';\r\n        }\r\n      }\r\n\r\n      class TimeoutError extends Error {\r\n        constructor(message: string) {\r\n          super(message);\r\n          this.name = 'TimeoutError';\r\n        }\r\n      }\r\n\r\n      const strategy = RetryStrategies.onErrorTypes([NetworkError, TimeoutError], {\r\n        maxAttempts: 3,\r\n        baseDelay: 10,\r\n      });\r\n\r\n      let callCount = 0;\r\n\r\n      // NetworkError should be retried\r\n      const networkFailingOperation = async () => {\r\n        callCount++;\r\n        throw new NetworkError('Network failed');\r\n      };\r\n\r\n      await expect(strategy.execute(networkFailingOperation)).rejects.toThrow('Network failed');\r\n      expect(callCount).toBe(3);\r\n\r\n      // Reset call count\r\n      callCount = 0;\r\n\r\n      // TimeoutError should be retried\r\n      const timeoutFailingOperation = async () => {\r\n        callCount++;\r\n        throw new TimeoutError('Timeout occurred');\r\n      };\r\n\r\n      await expect(strategy.execute(timeoutFailingOperation)).rejects.toThrow('Timeout occurred');\r\n      expect(callCount).toBe(3);\r\n\r\n      // Reset call count\r\n      callCount = 0;\r\n\r\n      // Other errors should not be retried\r\n      const otherFailingOperation = async () => {\r\n        callCount++;\r\n        throw new Error('Other error');\r\n      };\r\n\r\n      await expect(strategy.execute(otherFailingOperation)).rejects.toThrow('Other error');\r\n      expect(callCount).toBe(1);\r\n    });\r\n  });\r\n\r\n  describe('edge cases and error handling', () => {\r\n    it('should handle zero max attempts', async () => {\r\n      const strategy = new RetryStrategy({ maxAttempts: 0 });\r\n\r\n      const operation = async () => 'should not execute';\r\n\r\n      // With 0 max attempts, should not execute at all\r\n      // This is an edge case - the implementation should handle it gracefully\r\n      await expect(strategy.execute(operation)).rejects.toThrow();\r\n    });\r\n\r\n    it('should handle negative delays gracefully', async () => {\r\n      const strategy = new RetryStrategy({\r\n        maxAttempts: 2,\r\n        baseDelay: -100, // Negative delay\r\n      });\r\n\r\n      let callCount = 0;\r\n      const failingOperation = async () => {\r\n        callCount++;\r\n        throw new Error('Always fails');\r\n      };\r\n\r\n      await expect(strategy.execute(failingOperation)).rejects.toThrow();\r\n      expect(callCount).toBe(2); // Should still retry despite negative delay\r\n    });\r\n\r\n    it('should handle very large delays', async () => {\r\n      const strategy = new RetryStrategy({\r\n        maxAttempts: 2,\r\n        baseDelay: Number.MAX_SAFE_INTEGER,\r\n        maxDelay: 100, // Should cap the delay\r\n        jitter: false, // Disable jitter for predictable testing\r\n      });\r\n\r\n      const delays: number[] = [];\r\n      const originalSetTimeout = global.setTimeout;\r\n\r\n      global.setTimeout = jest.fn().mockImplementation((callback, delay) => {\r\n        delays.push(delay);\r\n        return originalSetTimeout(callback, 0);\r\n      });\r\n\r\n      let callCount = 0;\r\n      const failingOperation = async () => {\r\n        callCount++;\r\n        throw new Error('Always fails');\r\n      };\r\n\r\n      await expect(strategy.execute(failingOperation)).rejects.toThrow();\r\n\r\n      expect(delays[0]).toBe(100); // Should be capped at maxDelay\r\n\r\n      global.setTimeout = originalSetTimeout;\r\n    });\r\n\r\n    it('should preserve error properties', async () => {\r\n      const strategy = new RetryStrategy({ maxAttempts: 2, baseDelay: 10 });\r\n\r\n      const customError = new Error('Custom error');\r\n      customError.name = 'CustomError';\r\n      (customError as any).customProperty = 'custom value';\r\n\r\n      const failingOperation = async () => {\r\n        throw customError;\r\n      };\r\n\r\n      try {\r\n        await strategy.execute(failingOperation);\r\n        fail('Should have thrown error');\r\n      } catch (error) {\r\n        expect(error).toBe(customError); // Should be the same error instance\r\n        expect((error as any).customProperty).toBe('custom value');\r\n      }\r\n    });\r\n\r\n    it('should handle async errors correctly', async () => {\r\n      const strategy = new RetryStrategy({ maxAttempts: 2, baseDelay: 10 });\r\n\r\n      const asyncFailingOperation = async () => {\r\n        await new Promise(resolve => setTimeout(resolve, 10));\r\n        throw new Error('Async error');\r\n      };\r\n\r\n      let callCount = 0;\r\n      const countingOperation = async () => {\r\n        callCount++;\r\n        return await asyncFailingOperation();\r\n      };\r\n\r\n      await expect(strategy.execute(countingOperation)).rejects.toThrow('Async error');\r\n      expect(callCount).toBe(2);\r\n    });\r\n  });\r\n});"], "version": 3}