{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\asset-management.service.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,2CAA4F;AAC5F,6CAAmD;AACnD,qCAAyE;AACzE,qEAA2D;AAC3D,qFAA2E;AAC3E,sFAAkF;AAClF,0FAAsF;AAEtF;;;GAGG;AAEI,IAAM,sBAAsB,8BAA5B,MAAM,sBAAsB;IAGjC,YAEE,eAAmD,EAEnD,uBAAmE,EAClD,aAA4B,EAC5B,YAA0B;QAJ1B,oBAAe,GAAf,eAAe,CAAmB;QAElC,4BAAuB,GAAvB,uBAAuB,CAA2B;QAClD,kBAAa,GAAb,aAAa,CAAe;QAC5B,iBAAY,GAAZ,YAAY,CAAc;QAR5B,WAAM,GAAG,IAAI,eAAM,CAAC,wBAAsB,CAAC,IAAI,CAAC,CAAC;IAS/D,CAAC;IAEJ;;;;;OAKG;IACH,KAAK,CAAC,WAAW,CACf,SAYC,EACD,MAAc;QAEd,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE;gBACtC,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,MAAM;aACP,CAAC,CAAC;YAEH,6BAA6B;YAC7B,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;gBACxB,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;oBACvD,KAAK,EAAE,EAAE,SAAS,EAAE,SAAS,CAAC,SAAS,EAAE;iBAC1C,CAAC,CAAC;gBACH,IAAI,aAAa,EAAE,CAAC;oBAClB,MAAM,IAAI,4BAAmB,CAAC,yBAAyB,SAAS,CAAC,SAAS,iBAAiB,CAAC,CAAC;gBAC/F,CAAC;YACH,CAAC;YAED,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;gBACxC,GAAG,SAAS;gBACZ,MAAM,EAAE,QAAQ;gBAChB,eAAe,EAAE,IAAI,IAAI,EAAE;gBAC3B,QAAQ,EAAE,IAAI,IAAI,EAAE;gBACpB,SAAS,EAAE,MAAM;gBACjB,eAAe,EAAE,QAAQ;aAC1B,CAAC,CAAC;YAEH,+BAA+B;YAC/B,KAAK,CAAC,kBAAkB,EAAE,CAAC;YAE3B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE1D,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,OAAO,EACP,UAAU,CAAC,EAAE,EACb;gBACE,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,SAAS,EAAE,SAAS,CAAC,SAAS;aAC/B,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,EAAE;gBAC5C,OAAO,EAAE,UAAU,CAAC,EAAE;gBACtB,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;gBAC1C,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,SAAS;gBACT,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,QAAQ,CAAC,EAAU;QACvB,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;gBACxC,KAAK,EAAE,EAAE,EAAE,EAAE;gBACb,SAAS,EAAE,CAAC,iBAAiB,EAAE,aAAa,CAAC;aAC9C,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;gBAC9C,EAAE;gBACF,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,QAAQ,CAAC,OAad;QACC,IAAI,CAAC;YACH,MAAM,EACJ,IAAI,GAAG,CAAC,EACR,KAAK,GAAG,EAAE,EACV,IAAI,EACJ,MAAM,EACN,WAAW,EACX,WAAW,EACX,MAAM,EACN,IAAI,EACJ,SAAS,EACT,QAAQ,EACR,MAAM,GAAG,WAAW,EACpB,SAAS,GAAG,MAAM,GACnB,GAAG,OAAO,CAAC;YAEZ,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAEtE,gBAAgB;YAChB,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5B,YAAY,CAAC,QAAQ,CAAC,0BAA0B,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;YAC9D,CAAC;YAED,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChC,YAAY,CAAC,QAAQ,CAAC,8BAA8B,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YACpE,CAAC;YAED,IAAI,WAAW,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC1C,YAAY,CAAC,QAAQ,CAAC,wCAAwC,EAAE,EAAE,WAAW,EAAE,CAAC,CAAC;YACnF,CAAC;YAED,IAAI,WAAW,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC1C,YAAY,CAAC,QAAQ,CAAC,wCAAwC,EAAE,EAAE,WAAW,EAAE,CAAC,CAAC;YACnF,CAAC;YAED,IAAI,MAAM,EAAE,CAAC;gBACX,YAAY,CAAC,QAAQ,CACnB,+FAA+F,EAC/F,EAAE,MAAM,EAAE,IAAI,MAAM,GAAG,EAAE,CAC1B,CAAC;YACJ,CAAC;YAED,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5B,YAAY,CAAC,QAAQ,CAAC,qBAAqB,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;YACzD,CAAC;YAED,IAAI,SAAS,EAAE,CAAC;gBACd,YAAY,CAAC,QAAQ,CAAC,8BAA8B,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;YACvE,CAAC;YAED,IAAI,QAAQ,EAAE,CAAC;gBACb,YAAY,CAAC,QAAQ,CAAC,gCAAgC,EAAE,EAAE,QAAQ,EAAE,IAAI,QAAQ,GAAG,EAAE,CAAC,CAAC;YACzF,CAAC;YAED,gBAAgB;YAChB,YAAY,CAAC,OAAO,CAAC,SAAS,MAAM,EAAE,EAAE,SAAS,CAAC,CAAC;YAEnD,mBAAmB;YACnB,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAClC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEtC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM,YAAY,CAAC,eAAe,EAAE,CAAC;YAC7D,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;YAE5C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE;gBACpC,KAAK;gBACL,IAAI;gBACJ,KAAK;gBACL,UAAU;gBACV,OAAO,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE;aAClE,CAAC,CAAC;YAEH,OAAO;gBACL,MAAM;gBACN,KAAK;gBACL,IAAI;gBACJ,UAAU;aACX,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE;gBACzC,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,OAAO;aACR,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,WAAW,CACf,EAAU,EACV,UAA0B,EAC1B,MAAc;QAEd,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACtC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,MAAM,IAAI,0BAAiB,CAAC,iBAAiB,CAAC,CAAC;YACjD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE;gBAClC,OAAO,EAAE,EAAE;gBACX,UAAU;gBACV,MAAM;aACP,CAAC,CAAC;YAEH,0BAA0B;YAC1B,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,UAAU,EAAE;gBAC/B,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;YAEH,oDAAoD;YACpD,IAAI,UAAU,CAAC,WAAW,IAAI,UAAU,CAAC,mBAAmB,EAAE,CAAC;gBAC7D,KAAK,CAAC,kBAAkB,EAAE,CAAC;YAC7B,CAAC;YAED,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE5D,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,OAAO,EACP,EAAE,EACF;gBACE,OAAO,EAAE,UAAU;gBACnB,cAAc,EAAE;oBACd,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,MAAM,EAAE,KAAK,CAAC,MAAM;oBACpB,WAAW,EAAE,KAAK,CAAC,WAAW;iBAC/B;aACF,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,EAAE;gBAC5C,OAAO,EAAE,EAAE;gBACX,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,YAAY,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;gBAC1C,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,WAAW,CAAC,EAAU,EAAE,MAAc;QAC1C,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACtC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,MAAM,IAAI,0BAAiB,CAAC,iBAAiB,CAAC,CAAC;YACjD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE;gBAClC,OAAO,EAAE,EAAE;gBACX,MAAM;aACP,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAEzC,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,OAAO,EACP,EAAE,EACF;gBACE,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,SAAS,EAAE,KAAK,CAAC,SAAS;aAC3B,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,EAAE;gBAC5C,OAAO,EAAE,EAAE;gBACX,MAAM;aACP,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;gBAC1C,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,cAAc,CAClB,aAOE,EACF,MAAc;QAEd,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE;gBACtC,KAAK,EAAE,aAAa,CAAC,MAAM;gBAC3B,MAAM;aACP,CAAC,CAAC;YAEH,MAAM,gBAAgB,GAAY,EAAE,CAAC;YAErC,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE,CAAC;gBACjC,gCAAgC;gBAChC,IAAI,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;oBAC7C,KAAK,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE;iBACrC,CAAC,CAAC;gBAEH,IAAI,KAAK,EAAE,CAAC;oBACV,wBAAwB;oBACxB,KAAK,CAAC,cAAc,EAAE,CAAC;oBACvB,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC;oBACjD,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,KAAK,CAAC,UAAU,CAAC;oBACvD,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,IAAI,KAAK,CAAC,eAAe,CAAC;oBACtE,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC;oBACjD,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;oBAC7C,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC;gBAC3B,CAAC;qBAAM,CAAC;oBACN,mBAAmB;oBACnB,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;wBAClC,IAAI,EAAE,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS;wBACrC,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;wBACnC,SAAS,EAAE,IAAI,CAAC,SAAS;wBACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;wBACvB,UAAU,EAAE,IAAI,CAAC,UAAU;wBAC3B,eAAe,EAAE,IAAI,CAAC,eAAe;wBACrC,QAAQ,EAAE,IAAI,CAAC,QAAQ;wBACvB,MAAM,EAAE,QAAQ;wBAChB,WAAW,EAAE,QAAQ;wBACrB,WAAW,EAAE,YAAY;wBACzB,eAAe,EAAE,IAAI,CAAC,eAAe;wBACrC,eAAe,EAAE,IAAI,IAAI,EAAE;wBAC3B,QAAQ,EAAE,IAAI,IAAI,EAAE;wBACpB,SAAS,EAAE,MAAM;qBAClB,CAAC,CAAC;oBAEH,KAAK,CAAC,kBAAkB,EAAE,CAAC;gBAC7B,CAAC;gBAED,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC1D,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACpC,CAAC;YAED,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,UAAU,EACV,OAAO,EACP,IAAI,EACJ;gBACE,eAAe,EAAE,gBAAgB,CAAC,MAAM;gBACxC,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,eAAe;aAC1C,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2BAA2B,EAAE;gBAC3C,eAAe,EAAE,gBAAgB,CAAC,MAAM;gBACxC,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,gBAAgB,CAAC;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;gBAC7C,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,yBAAyB,CAAC,OAAe;QAC7C,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAC3C,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,MAAM,IAAI,0BAAiB,CAAC,iBAAiB,CAAC,CAAC;YACjD,CAAC;YAED,oCAAoC;YACpC,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,uBAAuB;iBAC3D,kBAAkB,CAAC,MAAM,CAAC;iBAC1B,MAAM,CAAC,eAAe,EAAE,UAAU,CAAC;iBACnC,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC;iBAC9B,KAAK,CAAC,yBAAyB,EAAE,EAAE,OAAO,EAAE,CAAC;iBAC7C,QAAQ,CAAC,+BAA+B,EAAE,EAAE,QAAQ,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE,CAAC;iBAC9E,OAAO,CAAC,eAAe,CAAC;iBACxB,UAAU,EAAE,CAAC;YAEhB,MAAM,MAAM,GAAG;gBACb,QAAQ,EAAE,CAAC;gBACX,IAAI,EAAE,CAAC;gBACP,MAAM,EAAE,CAAC;gBACT,GAAG,EAAE,CAAC;gBACN,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,CAAC;aACT,CAAC;YAEF,mBAAmB,CAAC,OAAO,CAAC,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,EAAE;gBAClD,MAAM,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACnC,MAAM,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC;YAEH,KAAK,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;YACxC,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE;gBACzD,OAAO;gBACP,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,uBAAuB;QAC3B,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,eAAe;iBAC9B,kBAAkB,CAAC,OAAO,CAAC;iBAC3B,KAAK,CAAC,gCAAgC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;iBAC5D,QAAQ,CAAC,wBAAwB,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;iBACxD,QAAQ,CACP,yGAAyG,CAC1G;iBACA,OAAO,CAAC,mBAAmB,EAAE,MAAM,CAAC;iBACpC,UAAU,CAAC,oBAAoB,EAAE,KAAK,CAAC;iBACvC,OAAO,EAAE,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE;gBACzD,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,aAAa;QACjB,IAAI,CAAC;YACH,MAAM,CACJ,WAAW,EACX,YAAY,EACZ,cAAc,EACd,YAAY,EACZ,mBAAmB,EACnB,yBAAyB,EAC1B,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACpB,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE;gBAC5B,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,CAAC;gBAC3D,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,WAAW,EAAE,UAAU,EAAE,EAAE,CAAC;gBAClE,IAAI,CAAC,oBAAoB,EAAE;gBAC3B,IAAI,CAAC,2BAA2B,EAAE;gBAClC,IAAI,CAAC,4BAA4B,EAAE;aACpC,CAAC,CAAC;YAEH,OAAO;gBACL,KAAK,EAAE,WAAW;gBAClB,MAAM,EAAE,YAAY;gBACpB,QAAQ,EAAE,cAAc;gBACxB,MAAM,EAAE,YAAY;gBACpB,aAAa,EAAE,mBAAmB;gBAClC,mBAAmB,EAAE,yBAAyB;gBAC9C,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;gBAClD,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACK,kBAAkB,CAAC,IAAS;QAClC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;YAElE,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBACpE,OAAO,iBAAiB,CAAC;YAC3B,CAAC;YACD,IAAI,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBACjE,OAAO,QAAQ,CAAC;YAClB,CAAC;YACD,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBAClC,OAAO,gBAAgB,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;YAC1D,IAAI,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC1E,OAAO,QAAQ,CAAC;YAClB,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC,CAAC,eAAe;IAClC,CAAC;IAED;;;OAGG;IACK,KAAK,CAAC,oBAAoB;QAChC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe;aACtC,kBAAkB,CAAC,OAAO,CAAC;aAC3B,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC;aAC5B,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC;aAC9B,OAAO,CAAC,YAAY,CAAC;aACrB,UAAU,EAAE,CAAC;QAEhB,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YACjC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtC,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAED;;;OAGG;IACK,KAAK,CAAC,2BAA2B;QACvC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe;aACtC,kBAAkB,CAAC,OAAO,CAAC;aAC3B,MAAM,CAAC,mBAAmB,EAAE,aAAa,CAAC;aAC1C,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC;aAC9B,OAAO,CAAC,mBAAmB,CAAC;aAC5B,UAAU,EAAE,CAAC;QAEhB,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YACjC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7C,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAED;;;OAGG;IACK,KAAK,CAAC,4BAA4B;QACxC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe;aACtC,kBAAkB,CAAC,OAAO,CAAC;aAC3B,SAAS,CAAC,uBAAuB,EAAE,MAAM,CAAC;aAC1C,KAAK,CAAC,+BAA+B,EAAE,EAAE,QAAQ,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE,CAAC;aAC3E,QAAQ,EAAE,CAAC;QAEd,OAAO,MAAM,CAAC;IAChB,CAAC;CACF,CAAA;AArmBY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,oBAAK,CAAC,CAAA;IAEvB,WAAA,IAAA,0BAAgB,EAAC,oCAAa,CAAC,CAAA;yDADE,oBAAU,oBAAV,oBAAU,oDAEF,oBAAU,oBAAV,oBAAU,oDACpB,8BAAa,oBAAb,8BAAa,oDACd,4BAAY,oBAAZ,4BAAY;GATlC,sBAAsB,CAqmBlC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\asset-management.service.ts"], "sourcesContent": ["import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository, FindManyOptions, Like, In, Between } from 'typeorm';\r\nimport { Asset } from '../../domain/entities/asset.entity';\r\nimport { Vulnerability } from '../../domain/entities/vulnerability.entity';\r\nimport { LoggerService } from '../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../infrastructure/logging/audit/audit.service';\r\n\r\n/**\r\n * Asset Management service\r\n * Handles asset discovery, inventory, and lifecycle management\r\n */\r\n@Injectable()\r\nexport class AssetManagementService {\r\n  private readonly logger = new Logger(AssetManagementService.name);\r\n\r\n  constructor(\r\n    @InjectRepository(Asset)\r\n    private readonly assetRepository: Repository<Asset>,\r\n    @InjectRepository(Vulnerability)\r\n    private readonly vulnerabilityRepository: Repository<Vulnerability>,\r\n    private readonly loggerService: LoggerService,\r\n    private readonly auditService: AuditService,\r\n  ) {}\r\n\r\n  /**\r\n   * Create a new asset\r\n   * @param assetData Asset creation data\r\n   * @param userId User creating the asset\r\n   * @returns Created asset\r\n   */\r\n  async createAsset(\r\n    assetData: {\r\n      name: string;\r\n      type: string;\r\n      description?: string;\r\n      ipAddress?: string;\r\n      hostname?: string;\r\n      criticality?: 'low' | 'medium' | 'high' | 'critical';\r\n      environment?: 'production' | 'staging' | 'development' | 'testing' | 'sandbox';\r\n      owner?: any;\r\n      location?: any;\r\n      tags?: string[];\r\n      metadata?: Record<string, any>;\r\n    },\r\n    userId: string,\r\n  ): Promise<Asset> {\r\n    try {\r\n      this.logger.debug('Creating new asset', {\r\n        name: assetData.name,\r\n        type: assetData.type,\r\n        userId,\r\n      });\r\n\r\n      // Check for duplicate assets\r\n      if (assetData.ipAddress) {\r\n        const existingAsset = await this.assetRepository.findOne({\r\n          where: { ipAddress: assetData.ipAddress },\r\n        });\r\n        if (existingAsset) {\r\n          throw new BadRequestException(`Asset with IP address ${assetData.ipAddress} already exists`);\r\n        }\r\n      }\r\n\r\n      const asset = this.assetRepository.create({\r\n        ...assetData,\r\n        status: 'active',\r\n        firstDiscovered: new Date(),\r\n        lastSeen: new Date(),\r\n        createdBy: userId,\r\n        discoveryMethod: 'manual',\r\n      });\r\n\r\n      // Calculate initial risk score\r\n      asset.calculateRiskScore();\r\n\r\n      const savedAsset = await this.assetRepository.save(asset);\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'create',\r\n        'asset',\r\n        savedAsset.id,\r\n        {\r\n          name: assetData.name,\r\n          type: assetData.type,\r\n          ipAddress: assetData.ipAddress,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Asset created successfully', {\r\n        assetId: savedAsset.id,\r\n        name: assetData.name,\r\n        userId,\r\n      });\r\n\r\n      return savedAsset;\r\n    } catch (error) {\r\n      this.logger.error('Failed to create asset', {\r\n        error: error.message,\r\n        assetData,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Find asset by ID\r\n   * @param id Asset ID\r\n   * @returns Asset or null\r\n   */\r\n  async findById(id: string): Promise<Asset | null> {\r\n    try {\r\n      return await this.assetRepository.findOne({\r\n        where: { id },\r\n        relations: ['vulnerabilities', 'scanResults'],\r\n      });\r\n    } catch (error) {\r\n      this.logger.error('Failed to find asset by ID', {\r\n        id,\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Find assets with filtering and pagination\r\n   * @param options Query options\r\n   * @returns Paginated assets\r\n   */\r\n  async findMany(options: {\r\n    page?: number;\r\n    limit?: number;\r\n    type?: string[];\r\n    status?: string[];\r\n    criticality?: string[];\r\n    environment?: string[];\r\n    search?: string;\r\n    tags?: string[];\r\n    ipAddress?: string;\r\n    hostname?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n  }): Promise<{ assets: Asset[]; total: number; page: number; totalPages: number }> {\r\n    try {\r\n      const {\r\n        page = 1,\r\n        limit = 20,\r\n        type,\r\n        status,\r\n        criticality,\r\n        environment,\r\n        search,\r\n        tags,\r\n        ipAddress,\r\n        hostname,\r\n        sortBy = 'createdAt',\r\n        sortOrder = 'DESC',\r\n      } = options;\r\n\r\n      const queryBuilder = this.assetRepository.createQueryBuilder('asset');\r\n\r\n      // Apply filters\r\n      if (type && type.length > 0) {\r\n        queryBuilder.andWhere('asset.type IN (:...type)', { type });\r\n      }\r\n\r\n      if (status && status.length > 0) {\r\n        queryBuilder.andWhere('asset.status IN (:...status)', { status });\r\n      }\r\n\r\n      if (criticality && criticality.length > 0) {\r\n        queryBuilder.andWhere('asset.criticality IN (:...criticality)', { criticality });\r\n      }\r\n\r\n      if (environment && environment.length > 0) {\r\n        queryBuilder.andWhere('asset.environment IN (:...environment)', { environment });\r\n      }\r\n\r\n      if (search) {\r\n        queryBuilder.andWhere(\r\n          '(asset.name ILIKE :search OR asset.description ILIKE :search OR asset.hostname ILIKE :search)',\r\n          { search: `%${search}%` },\r\n        );\r\n      }\r\n\r\n      if (tags && tags.length > 0) {\r\n        queryBuilder.andWhere('asset.tags ?& :tags', { tags });\r\n      }\r\n\r\n      if (ipAddress) {\r\n        queryBuilder.andWhere('asset.ipAddress = :ipAddress', { ipAddress });\r\n      }\r\n\r\n      if (hostname) {\r\n        queryBuilder.andWhere('asset.hostname ILIKE :hostname', { hostname: `%${hostname}%` });\r\n      }\r\n\r\n      // Apply sorting\r\n      queryBuilder.orderBy(`asset.${sortBy}`, sortOrder);\r\n\r\n      // Apply pagination\r\n      const offset = (page - 1) * limit;\r\n      queryBuilder.skip(offset).take(limit);\r\n\r\n      const [assets, total] = await queryBuilder.getManyAndCount();\r\n      const totalPages = Math.ceil(total / limit);\r\n\r\n      this.logger.debug('Assets retrieved', {\r\n        total,\r\n        page,\r\n        limit,\r\n        totalPages,\r\n        filters: { type, status, criticality, environment, search, tags },\r\n      });\r\n\r\n      return {\r\n        assets,\r\n        total,\r\n        page,\r\n        totalPages,\r\n      };\r\n    } catch (error) {\r\n      this.logger.error('Failed to find assets', {\r\n        error: error.message,\r\n        options,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update asset\r\n   * @param id Asset ID\r\n   * @param updateData Update data\r\n   * @param userId User updating the asset\r\n   * @returns Updated asset\r\n   */\r\n  async updateAsset(\r\n    id: string,\r\n    updateData: Partial<Asset>,\r\n    userId: string,\r\n  ): Promise<Asset> {\r\n    try {\r\n      const asset = await this.findById(id);\r\n      if (!asset) {\r\n        throw new NotFoundException('Asset not found');\r\n      }\r\n\r\n      this.logger.debug('Updating asset', {\r\n        assetId: id,\r\n        updateData,\r\n        userId,\r\n      });\r\n\r\n      // Update asset properties\r\n      Object.assign(asset, updateData, {\r\n        updatedBy: userId,\r\n      });\r\n\r\n      // Recalculate risk score if relevant fields changed\r\n      if (updateData.criticality || updateData.vulnerabilityCounts) {\r\n        asset.calculateRiskScore();\r\n      }\r\n\r\n      const updatedAsset = await this.assetRepository.save(asset);\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'update',\r\n        'asset',\r\n        id,\r\n        {\r\n          changes: updateData,\r\n          previousValues: {\r\n            name: asset.name,\r\n            status: asset.status,\r\n            criticality: asset.criticality,\r\n          },\r\n        },\r\n      );\r\n\r\n      this.logger.log('Asset updated successfully', {\r\n        assetId: id,\r\n        userId,\r\n      });\r\n\r\n      return updatedAsset;\r\n    } catch (error) {\r\n      this.logger.error('Failed to update asset', {\r\n        assetId: id,\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Delete asset\r\n   * @param id Asset ID\r\n   * @param userId User deleting the asset\r\n   */\r\n  async deleteAsset(id: string, userId: string): Promise<void> {\r\n    try {\r\n      const asset = await this.findById(id);\r\n      if (!asset) {\r\n        throw new NotFoundException('Asset not found');\r\n      }\r\n\r\n      this.logger.debug('Deleting asset', {\r\n        assetId: id,\r\n        userId,\r\n      });\r\n\r\n      await this.assetRepository.remove(asset);\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'delete',\r\n        'asset',\r\n        id,\r\n        {\r\n          name: asset.name,\r\n          type: asset.type,\r\n          ipAddress: asset.ipAddress,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Asset deleted successfully', {\r\n        assetId: id,\r\n        userId,\r\n      });\r\n    } catch (error) {\r\n      this.logger.error('Failed to delete asset', {\r\n        assetId: id,\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Discover assets from network scan\r\n   * @param discoveryData Discovery data from network scan\r\n   * @param userId User initiating discovery\r\n   * @returns Discovered assets\r\n   */\r\n  async discoverAssets(\r\n    discoveryData: Array<{\r\n      ipAddress: string;\r\n      hostname?: string;\r\n      macAddress?: string;\r\n      operatingSystem?: any;\r\n      services?: any[];\r\n      discoveryMethod: string;\r\n    }>,\r\n    userId: string,\r\n  ): Promise<Asset[]> {\r\n    try {\r\n      this.logger.debug('Discovering assets', {\r\n        count: discoveryData.length,\r\n        userId,\r\n      });\r\n\r\n      const discoveredAssets: Asset[] = [];\r\n\r\n      for (const data of discoveryData) {\r\n        // Check if asset already exists\r\n        let asset = await this.assetRepository.findOne({\r\n          where: { ipAddress: data.ipAddress },\r\n        });\r\n\r\n        if (asset) {\r\n          // Update existing asset\r\n          asset.updateLastSeen();\r\n          asset.hostname = data.hostname || asset.hostname;\r\n          asset.macAddress = data.macAddress || asset.macAddress;\r\n          asset.operatingSystem = data.operatingSystem || asset.operatingSystem;\r\n          asset.services = data.services || asset.services;\r\n          asset.discoveryMethod = data.discoveryMethod;\r\n          asset.updatedBy = userId;\r\n        } else {\r\n          // Create new asset\r\n          asset = this.assetRepository.create({\r\n            name: data.hostname || data.ipAddress,\r\n            type: this.determineAssetType(data),\r\n            ipAddress: data.ipAddress,\r\n            hostname: data.hostname,\r\n            macAddress: data.macAddress,\r\n            operatingSystem: data.operatingSystem,\r\n            services: data.services,\r\n            status: 'active',\r\n            criticality: 'medium',\r\n            environment: 'production',\r\n            discoveryMethod: data.discoveryMethod,\r\n            firstDiscovered: new Date(),\r\n            lastSeen: new Date(),\r\n            createdBy: userId,\r\n          });\r\n\r\n          asset.calculateRiskScore();\r\n        }\r\n\r\n        const savedAsset = await this.assetRepository.save(asset);\r\n        discoveredAssets.push(savedAsset);\r\n      }\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'discover',\r\n        'asset',\r\n        null,\r\n        {\r\n          discoveredCount: discoveredAssets.length,\r\n          method: discoveryData[0]?.discoveryMethod,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Asset discovery completed', {\r\n        discoveredCount: discoveredAssets.length,\r\n        userId,\r\n      });\r\n\r\n      return discoveredAssets;\r\n    } catch (error) {\r\n      this.logger.error('Failed to discover assets', {\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update asset vulnerability counts\r\n   * @param assetId Asset ID\r\n   * @returns Updated asset\r\n   */\r\n  async updateVulnerabilityCounts(assetId: string): Promise<Asset> {\r\n    try {\r\n      const asset = await this.findById(assetId);\r\n      if (!asset) {\r\n        throw new NotFoundException('Asset not found');\r\n      }\r\n\r\n      // Count vulnerabilities by severity\r\n      const vulnerabilityCounts = await this.vulnerabilityRepository\r\n        .createQueryBuilder('vuln')\r\n        .select('vuln.severity', 'severity')\r\n        .addSelect('COUNT(*)', 'count')\r\n        .where('vuln.assetId = :assetId', { assetId })\r\n        .andWhere('vuln.status IN (:...statuses)', { statuses: ['open', 'confirmed'] })\r\n        .groupBy('vuln.severity')\r\n        .getRawMany();\r\n\r\n      const counts = {\r\n        critical: 0,\r\n        high: 0,\r\n        medium: 0,\r\n        low: 0,\r\n        info: 0,\r\n        total: 0,\r\n      };\r\n\r\n      vulnerabilityCounts.forEach(({ severity, count }) => {\r\n        counts[severity] = parseInt(count);\r\n        counts.total += parseInt(count);\r\n      });\r\n\r\n      asset.updateVulnerabilityCounts(counts);\r\n      return await this.assetRepository.save(asset);\r\n    } catch (error) {\r\n      this.logger.error('Failed to update vulnerability counts', {\r\n        assetId,\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get assets due for scanning\r\n   * @returns Assets due for scanning\r\n   */\r\n  async getAssetsDueForScanning(): Promise<Asset[]> {\r\n    try {\r\n      return await this.assetRepository\r\n        .createQueryBuilder('asset')\r\n        .where('asset.isScannable = :scannable', { scannable: true })\r\n        .andWhere('asset.status = :status', { status: 'active' })\r\n        .andWhere(\r\n          '(asset.lastScanDate IS NULL OR asset.lastScanDate + INTERVAL \\'1 hour\\' * asset.scanFrequency <= NOW())',\r\n        )\r\n        .orderBy('asset.criticality', 'DESC')\r\n        .addOrderBy('asset.lastScanDate', 'ASC')\r\n        .getMany();\r\n    } catch (error) {\r\n      this.logger.error('Failed to get assets due for scanning', {\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get asset statistics\r\n   * @returns Asset statistics\r\n   */\r\n  async getStatistics(): Promise<any> {\r\n    try {\r\n      const [\r\n        totalAssets,\r\n        activeAssets,\r\n        criticalAssets,\r\n        assetsByType,\r\n        assetsByEnvironment,\r\n        assetsWithVulnerabilities,\r\n      ] = await Promise.all([\r\n        this.assetRepository.count(),\r\n        this.assetRepository.count({ where: { status: 'active' } }),\r\n        this.assetRepository.count({ where: { criticality: 'critical' } }),\r\n        this.getAssetCountsByType(),\r\n        this.getAssetCountsByEnvironment(),\r\n        this.getAssetsWithVulnerabilities(),\r\n      ]);\r\n\r\n      return {\r\n        total: totalAssets,\r\n        active: activeAssets,\r\n        critical: criticalAssets,\r\n        byType: assetsByType,\r\n        byEnvironment: assetsByEnvironment,\r\n        withVulnerabilities: assetsWithVulnerabilities,\r\n        timestamp: new Date().toISOString(),\r\n      };\r\n    } catch (error) {\r\n      this.logger.error('Failed to get asset statistics', {\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Determine asset type based on discovery data\r\n   * @param data Discovery data\r\n   * @returns Asset type\r\n   */\r\n  private determineAssetType(data: any): string {\r\n    if (data.services) {\r\n      const serviceNames = data.services.map(s => s.name.toLowerCase());\r\n      \r\n      if (serviceNames.includes('http') || serviceNames.includes('https')) {\r\n        return 'web_application';\r\n      }\r\n      if (serviceNames.includes('ssh') || serviceNames.includes('rdp')) {\r\n        return 'server';\r\n      }\r\n      if (serviceNames.includes('snmp')) {\r\n        return 'network_device';\r\n      }\r\n    }\r\n\r\n    if (data.operatingSystem) {\r\n      const os = data.operatingSystem.name?.toLowerCase() || '';\r\n      if (os.includes('windows') || os.includes('linux') || os.includes('unix')) {\r\n        return 'server';\r\n      }\r\n    }\r\n\r\n    return 'server'; // Default type\r\n  }\r\n\r\n  /**\r\n   * Get asset counts by type\r\n   * @returns Asset counts by type\r\n   */\r\n  private async getAssetCountsByType(): Promise<Record<string, number>> {\r\n    const result = await this.assetRepository\r\n      .createQueryBuilder('asset')\r\n      .select('asset.type', 'type')\r\n      .addSelect('COUNT(*)', 'count')\r\n      .groupBy('asset.type')\r\n      .getRawMany();\r\n\r\n    return result.reduce((acc, item) => {\r\n      acc[item.type] = parseInt(item.count);\r\n      return acc;\r\n    }, {});\r\n  }\r\n\r\n  /**\r\n   * Get asset counts by environment\r\n   * @returns Asset counts by environment\r\n   */\r\n  private async getAssetCountsByEnvironment(): Promise<Record<string, number>> {\r\n    const result = await this.assetRepository\r\n      .createQueryBuilder('asset')\r\n      .select('asset.environment', 'environment')\r\n      .addSelect('COUNT(*)', 'count')\r\n      .groupBy('asset.environment')\r\n      .getRawMany();\r\n\r\n    return result.reduce((acc, item) => {\r\n      acc[item.environment] = parseInt(item.count);\r\n      return acc;\r\n    }, {});\r\n  }\r\n\r\n  /**\r\n   * Get count of assets with vulnerabilities\r\n   * @returns Count of assets with vulnerabilities\r\n   */\r\n  private async getAssetsWithVulnerabilities(): Promise<number> {\r\n    const result = await this.assetRepository\r\n      .createQueryBuilder('asset')\r\n      .innerJoin('asset.vulnerabilities', 'vuln')\r\n      .where('vuln.status IN (:...statuses)', { statuses: ['open', 'confirmed'] })\r\n      .getCount();\r\n\r\n    return result;\r\n  }\r\n}\r\n"], "version": 3}