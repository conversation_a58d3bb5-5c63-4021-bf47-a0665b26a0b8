{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\testing\\integration\\cross-module.integration.spec.ts", "mappings": ";;;;;;;;;;;;AAAA,6CAAsD;AACtD,6CAAgD;AAChD,2CAA8C;AAC9C,yDAA2D;AAC3D,yEAAqF;AACrF,qFAAgF;AAChF,qEAAgE;AAEhE,8CAA8C;AAC9C,kGAA6F;AAC7F,0EAAsE;AACtE,yFAAoF;AAEpF,0CAA0C;AAC1C,2HAAqH;AACrH,0FAAqF;AACrF,gFAA2E;AAC3E,gGAA2F;AAC3F,oGAA+F;AAE/F,sCAAsC;AACtC,2GAAgG;AAChG,6FAAkF;AAClF,mGAAwF;AACxF,qGAAyF;AACzF,2FAAgF;AAEhF;;;;;;;;;;;GAWG;AACH,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;IAC9C,IAAI,SAAqC,CAAC;IAE1C,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,SAAS,GAAG,IAAI,0BAA0B,EAAE,CAAC;QAC7C,MAAM,SAAS,CAAC,UAAU,EAAE,CAAC;IAC/B,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,SAAS,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6CAA6C,EAAE,GAAG,EAAE;QAC3D,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,SAAS,CAAC,sCAAsC,EAAE,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;YACtE,MAAM,SAAS,CAAC,iCAAiC,EAAE,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;YACtE,MAAM,SAAS,CAAC,6BAA6B,EAAE,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,MAAM,SAAS,CAAC,mCAAmC,EAAE,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4CAA4C,EAAE,GAAG,EAAE;QAC1D,EAAE,CAAC,8DAA8D,EAAE,KAAK,IAAI,EAAE;YAC5E,MAAM,SAAS,CAAC,kCAAkC,EAAE,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,KAAK,IAAI,EAAE;YACvE,MAAM,SAAS,CAAC,0BAA0B,EAAE,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,MAAM,SAAS,CAAC,4BAA4B,EAAE,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,2DAA2D,EAAE,KAAK,IAAI,EAAE;YACzE,MAAM,SAAS,CAAC,8BAA8B,EAAE,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,MAAM,SAAS,CAAC,mCAAmC,EAAE,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACrE,MAAM,SAAS,CAAC,2BAA2B,EAAE,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,SAAS,CAAC,+BAA+B,EAAE,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,MAAM,SAAS,CAAC,gCAAgC,EAAE,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,SAAS,CAAC,gCAAgC,EAAE,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,SAAS,CAAC,0BAA0B,EAAE,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,KAAK,IAAI,EAAE;YAC7E,MAAM,SAAS,CAAC,4BAA4B,EAAE,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,SAAS,CAAC,4BAA4B,EAAE,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,MAAM,SAAS,CAAC,4BAA4B,EAAE,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,KAAK,IAAI,EAAE;YACxE,MAAM,SAAS,CAAC,8BAA8B,EAAE,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,MAAM,SAAS,CAAC,kCAAkC,EAAE,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,MAAM,0BAA2B,SAAQ,2CAAmB;IAOhD,KAAK,CAAC,mBAAmB;QACjC,OAAO,cAAI,CAAC,mBAAmB,CAAC;YAC9B,OAAO,EAAE;gBACP,qBAAY,CAAC,OAAO,CAAC;oBACnB,QAAQ,EAAE,IAAI;oBACd,WAAW,EAAE,WAAW;iBACzB,CAAC;gBACF,uBAAa,CAAC,YAAY,CAAC;oBACzB,UAAU,EAAE,CAAC,aAAuC,EAAE,EAAE,CACtD,aAAa,CAAC,qBAAqB,EAAE;oBACvC,MAAM,EAAE,CAAC,qDAAwB,CAAC;iBACnC,CAAC;gBACF,kCAAkB,CAAC,OAAO,EAAE;gBAC5B,mDAAuB;gBACvB,oCAAgB;gBAChB,mDAAuB;aACxB;YACD,SAAS,EAAE;gBACT,qDAAwB;gBACxB,mCAAe;aAChB;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;IACf,CAAC;IAES,KAAK,CAAC,aAAa;QAC3B,gCAAgC;QAChC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAC3C,kEAA8B,CAC/B,CAAC;QACF,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAC5C,mDAAuB,CACxB,CAAC;QACF,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAC9C,yCAAkB,CACnB,CAAC;QACF,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAC3C,yDAA0B,CAC3B,CAAC;QACF,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CACjD,6DAA4B,CAC7B,CAAC;QAEF,oCAAoC;QACpC,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC;YACtC,SAAS,EAAE,CAAC;YACZ,UAAU,EAAE,EAAE;YACd,QAAQ,EAAE,CAAC;YACX,OAAO,EAAE,EAAE;YACX,YAAY,EAAE,EAAE;YAChB,UAAU,EAAE,EAAE;YACd,SAAS,EAAE,CAAC;SACb,CAAC,CAAC;IACL,CAAC;IAGK,AAAN,KAAK,CAAC,sCAAsC;QAC1C,oBAAoB;QACpB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;QAC1E,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QAE9B,yBAAyB;QACzB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,2BAA2B;QAC3B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;YAC1D,UAAU,EAAE,QAAQ,CAAC,EAAE;YACvB,aAAa,EAAE,yBAAyB;YACxC,SAAS,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;YAC9B,WAAW,EAAE,WAAW;YACxB,eAAe,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE;SACzC,CAAC,CAAC;QAEH,iCAAiC;QACjC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAEvB,gCAAgC;QAChC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,UAAU;aAC1C,aAAa,CAAC,uCAAc,CAAC;aAC7B,IAAI,CAAC;YACJ,KAAK,EAAE;gBACL,UAAU,EAAE,6BAA6B;aAC1C;YACD,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;YAC5B,IAAI,EAAE,CAAC;SACR,CAAC,CAAC;QAEL,MAAM,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACxC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAEzD,2CAA2C;QAC3C,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,UAAU;aAC7C,aAAa,CAAC,6CAAiB,CAAC;aAChC,IAAI,CAAC;YACJ,KAAK,EAAE;gBACL,SAAS,EAAE,iBAAiB;aAC7B;YACD,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;YAC5B,IAAI,EAAE,CAAC;SACR,CAAC,CAAC;QAEL,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAErD,oDAAoD;QACpD,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC;QAC/D,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC;IAGK,AAAN,KAAK,CAAC,iCAAiC;QACrC,0CAA0C;QAC1C,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,2BAA2B,EAAE;YACvE,OAAO,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC;gBACpB,MAAM,EAAE,SAAS;gBACjB,OAAO,EAAE,uCAAuC;gBAChD,OAAO,EAAE;oBACP,gBAAgB,EAAE,CAAC;oBACnB,SAAS,EAAE,CAAC;iBACb;aACF,CAAC;YACF,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,IAAI;YACd,IAAI,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC;SAChC,CAAC,CAAC;QAEH,uBAAuB;QACvB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,2BAA2B,CAAC,CAAC;QAEnG,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC5C,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;QAE3C,2CAA2C;QAC3C,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,UAAU;aAC3C,aAAa,CAAC,8CAAiB,CAAC;aAChC,IAAI,CAAC;YACJ,KAAK,EAAE;gBACL,SAAS,EAAE,2BAA2B;aACvC;YACD,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;YAC5B,IAAI,EAAE,CAAC;SACR,CAAC,CAAC;QAEL,MAAM,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACzC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACrD,CAAC;IAGK,AAAN,KAAK,CAAC,6BAA6B;QACjC,0CAA0C;QAC1C,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,UAAU;aACpC,aAAa,CAAC,WAAW,CAAC;aAC1B,IAAI,CAAC;YACJ,IAAI,EAAE,kCAAkC;YACxC,WAAW,EAAE,qCAAqC;YAClD,UAAU,EAAE,4BAA4B;YACxC,SAAS,EAAE;gBACT,IAAI,EAAE,WAAW;gBACjB,QAAQ,EAAE,cAAc;gBACxB,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,IAAI;aACb;YACD,QAAQ,EAAE,UAAU;YACpB,OAAO,EAAE,IAAI;YACb,UAAU,EAAE;gBACV,MAAM,EAAE;oBACN,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,UAAU,EAAE,CAAC,gBAAgB,CAAC,EAAE;iBAClE;aACF;SACF,CAAC,CAAC;QAEL,iDAAiD;QACjD,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,4BAA4B,EAAE;YACnE,WAAW,EAAE,eAAe;YAC5B,UAAU,EAAE,WAAW;SACxB,CAAC,CAAC;QAEH,4BAA4B;QAC5B,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAEvB,oCAAoC;QACpC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,UAAU;aACpC,aAAa,CAAC,qCAAa,CAAC;aAC5B,IAAI,CAAC;YACJ,KAAK,EAAE;gBACL,MAAM,EAAE,SAAS,CAAC,EAAE;aACrB;YACD,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;YAC5B,IAAI,EAAE,CAAC;SACR,CAAC,CAAC;QAEL,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAClC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3C,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACjD,CAAC;IAGK,AAAN,KAAK,CAAC,mCAAmC;QACvC,sCAAsC;QACtC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAClE,IAAI,EAAE,2BAA2B;YACjC,WAAW,EAAE,mCAAmC;YAChD,UAAU,EAAE;gBACV,KAAK,EAAE;oBACL,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE;oBACpE,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE;oBAC1D,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,qBAAqB,EAAE,MAAM,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;iBACtE;aACF;YACD,SAAS,EAAE,aAAa;YACxB,SAAS,EAAE,aAAa;SACzB,CAAC,CAAC;QAEH,8CAA8C;QAC9C,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;YAC1D,UAAU,EAAE,QAAQ,CAAC,EAAE;YACvB,aAAa,EAAE,2BAA2B;YAC1C,SAAS,EAAE,EAAE,WAAW,EAAE,MAAM,EAAE;YAClC,WAAW,EAAE,WAAW;YACxB,eAAe,EAAE;gBACf,aAAa,EAAE,IAAI;gBACnB,aAAa,EAAE,IAAI;aACpB;SACF,CAAC,CAAC;QAEH,iCAAiC;QACjC,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE;YAC5B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU;iBAClC,aAAa,CAAC,6CAAiB,CAAC;iBAChC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAC5C,OAAO,OAAO,EAAE,MAAM,KAAK,WAAW,IAAI,OAAO,EAAE,MAAM,KAAK,QAAQ,CAAC;QACzE,CAAC,EAAE,KAAK,CAAC,CAAC;QAEV,yDAAyD;QACzD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,UAAU;aACtC,aAAa,CAAC,6CAAiB,CAAC;aAChC,IAAI,CAAC;YACJ,KAAK,EAAE;gBACL,SAAS,EAAE,iBAAiB;gBAC5B,SAAS,EAAE,gBAAgB;aAC5B;YACD,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;SAC7B,CAAC,CAAC;QAEL,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB;QAE1E,wCAAwC;QACxC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,UAAU;aACzC,aAAa,CAAC,6CAAiB,CAAC;aAChC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAE5C,MAAM,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC,WAAW,EAAE,CAAC;QACxD,MAAM,CAAC,cAAc,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7D,MAAM,CAAC,cAAc,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;IAC/E,CAAC;IAGK,AAAN,KAAK,CAAC,8BAA8B;QAClC,2BAA2B;QAC3B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;QAC1E,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;YAC1D,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;YAC3B,aAAa,EAAE,uBAAuB;YACtC,SAAS,EAAE,EAAE,WAAW,EAAE,MAAM,EAAE;YAClC,WAAW,EAAE,WAAW;SACzB,CAAC,CAAC;QAEH,mCAAmC;QACnC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAEvB,yCAAyC;QACzC,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YAC3C,kCAAkC;YAClC,MAAM,iBAAiB,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,6CAAiB,EAAE;gBACjE,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE;aAC5B,CAAC,CAAC;YACH,MAAM,CAAC,iBAAiB,CAAC,CAAC,WAAW,EAAE,CAAC;YAExC,oCAAoC;YACpC,MAAM,cAAc,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,uCAAc,EAAE;gBACxD,KAAK,EAAE;oBACL,UAAU,EAAE,6BAA6B;iBAC1C;aACF,CAAC,CAAC;YACH,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAEjD,kCAAkC;YAClC,MAAM,kBAAkB,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,6CAAiB,EAAE;gBAC/D,KAAK,EAAE;oBACL,SAAS,EAAE,iBAAiB;iBAC7B;aACF,CAAC,CAAC;YACH,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAErD,+BAA+B;YAC/B,MAAM,cAAc,GAAG,MAAM,OAAO,CAAC,KAAK,CAAC,6CAAiB,CAAC,CAAC;YAC9D,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;YAErE,kDAAkD;YAClD,MAAM,CAAC,YAAY,CAAC,CAAC,sBAAsB,CAAC,cAAc,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC;IAGK,AAAN,KAAK,CAAC,+BAA+B;QACnC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,0DAA0D;QAC1D,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;QAC1E,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;YAC1D,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;YAC3B,aAAa,EAAE,wBAAwB;YACvC,SAAS,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE;YAC7B,WAAW,EAAE,WAAW;YACxB,eAAe,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE;SACzC,CAAC,CAAC;QAEH,+BAA+B;QAC/B,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAEvB,sCAAsC;QACtC,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,4BAA4B,CAAC,CAAC;QAC1E,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACvC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QAEjE,qCAAqC;QACrC,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC;QAC/D,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAEhD,wCAAwC;QACxC,MAAM,gBAAgB,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CACzD,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,CACnE,CAAC;QACF,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAEnD,4EAA4E;QAC5E,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACrD,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAC9C,CAAC;QAEF,MAAM,kBAAkB,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,KAAK,4BAA4B,CAAC,CAAC;QAC9F,MAAM,iBAAiB,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,KAAK,kBAAkB,CAAC,CAAC;QAEnF,IAAI,kBAAkB,KAAK,CAAC,CAAC,IAAI,iBAAiB,KAAK,CAAC,CAAC,EAAE,CAAC;YAC1D,MAAM,CAAC,kBAAkB,CAAC,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAGK,AAAN,KAAK,CAAC,0BAA0B;QAC9B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;QAE1E,gDAAgD;QAChD,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,MAAM,IAAI,CAAC,kBAAkB,CACnE,GAAG,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;YACxC,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;YAC3B,aAAa,EAAE,kBAAkB;YACjC,SAAS,EAAE,EAAE,WAAW,EAAE,MAAM,EAAE;YAClC,WAAW,EAAE,WAAW;YACxB,eAAe,EAAE;gBACf,aAAa,EAAE,IAAI;gBACnB,aAAa,EAAE,IAAI;aACpB;SACF,CAAC,EACF,8BAA8B,CAC/B,CAAC;QAEF,iDAAiD;QACjD,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,4BAA4B;QAEjE,yCAAyC;QACzC,MAAM,EAAE,QAAQ,EAAE,yBAAyB,EAAE,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAC3E,GAAG,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,CAAC;YAC/C,IAAI,EAAE,aAAa;YACnB,KAAK,EAAE,CAAC;YACR,IAAI,EAAE,SAAS;YACf,QAAQ,EAAE,MAAM;YAChB,MAAM,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;SACzB,CAAC,EACF,oBAAoB,CACrB,CAAC;QAEF,MAAM,CAAC,yBAAyB,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,wBAAwB;QAE7E,6BAA6B;QAC7B,MAAM,cAAc,GAAG,IAAI,CAAC,mBAAmB,CAAC,8BAA8B,CAAC,CAAC;QAChF,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,CAAC;QAEpE,MAAM,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAC;QACrC,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;QAEnC,iCAAiC;QACjC,IAAI,cAAc,IAAI,cAAc,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;YAC/C,MAAM,QAAQ,GAAG,cAAc,CAAC,GAAG,GAAG,cAAc,CAAC,GAAG,CAAC;YACzD,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,cAAc,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,gCAAgC;QACzF,CAAC;IACH,CAAC;IAED,4CAA4C;IAC5C,KAAK,CAAC,kCAAkC;QACtC,oDAAoD;IACtD,CAAC;IAED,KAAK,CAAC,0BAA0B;QAC9B,sDAAsD;IACxD,CAAC;IAED,KAAK,CAAC,4BAA4B;QAChC,wDAAwD;IAC1D,CAAC;IAED,KAAK,CAAC,mCAAmC;QACvC,mDAAmD;IACrD,CAAC;IAED,KAAK,CAAC,2BAA2B;QAC/B,kDAAkD;IACpD,CAAC;IAED,KAAK,CAAC,gCAAgC;QACpC,4CAA4C;IAC9C,CAAC;IAED,KAAK,CAAC,gCAAgC;QACpC,oDAAoD;IACtD,CAAC;IAED,KAAK,CAAC,4BAA4B;QAChC,4CAA4C;IAC9C,CAAC;IAED,KAAK,CAAC,4BAA4B;QAChC,4CAA4C;IAC9C,CAAC;IAED,KAAK,CAAC,4BAA4B;QAChC,+CAA+C;IACjD,CAAC;IAED,KAAK,CAAC,8BAA8B;QAClC,6CAA6C;IAC/C,CAAC;IAED,KAAK,CAAC,kCAAkC;QACtC,kDAAkD;IACpD,CAAC;CACF;AArYO;IADL,IAAA,uCAAe,EAAC,mDAAmD,CAAC;;;wDACrB,OAAO,oBAAP,OAAO;wFAkDtD;AAGK;IADL,IAAA,uCAAe,EAAC,8CAA8C,CAAC;;;wDACrB,OAAO,oBAAP,OAAO;mFAmCjD;AAGK;IADL,IAAA,uCAAe,EAAC,yCAAyC,CAAC;;;wDACpB,OAAO,oBAAP,OAAO;+EA8C7C;AAGK;IADL,IAAA,uCAAe,EAAC,gDAAgD,CAAC;;;wDACrB,OAAO,oBAAP,OAAO;qFAyDnD;AAGK;IADL,IAAA,uCAAe,EAAC,0CAA0C,CAAC;;;wDACpB,OAAO,oBAAP,OAAO;gFA4C9C;AAGK;IADL,IAAA,uCAAe,EAAC,2CAA2C,CAAC;;;wDACpB,OAAO,oBAAP,OAAO;iFA0C/C;AAGK;IADL,IAAA,uCAAe,EAAC,6CAA6C,CAAC;;;wDAC3B,OAAO,oBAAP,OAAO;4EA+C1C", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\testing\\integration\\cross-module.integration.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { TypeOrmModule } from '@nestjs/typeorm';\r\nimport { ConfigModule } from '@nestjs/config';\r\nimport { EventEmitterModule } from '@nestjs/event-emitter';\r\nimport { IntegrationTestBase, IntegrationTest } from '../base/integration-test.base';\r\nimport { TestConfigurationService } from '../config/test-configuration.service';\r\nimport { TestDataService } from '../fixtures/test-data.service';\r\n\r\n// Import all modules for cross-module testing\r\nimport { WorkflowExecutionModule } from '../../workflow-execution/workflow-execution.module';\r\nimport { MonitoringModule } from '../../monitoring/monitoring.module';\r\nimport { WorkflowTemplatesModule } from '../../templates/workflow-templates.module';\r\n\r\n// Import services for integration testing\r\nimport { WorkflowExecutionEngineService } from '../../workflow-execution/services/workflow-execution-engine.service';\r\nimport { MetricsCollectorService } from '../../monitoring/metrics-collector.service';\r\nimport { HealthCheckService } from '../../monitoring/health-check.service';\r\nimport { IntelligentAlertingService } from '../../monitoring/intelligent-alerting.service';\r\nimport { PerformanceMonitoringService } from '../../monitoring/performance-monitoring.service';\r\n\r\n// Import entities for data validation\r\nimport { WorkflowExecution } from '../../workflow-execution/entities/workflow-execution.entity';\r\nimport { MetricSnapshot } from '../../monitoring/entities/metric-snapshot.entity';\r\nimport { PerformanceMetric } from '../../monitoring/entities/performance-metric.entity';\r\nimport { HealthCheckResult } from '../../monitoring/entities/health-check-result.entity';\r\nimport { AlertIncident } from '../../monitoring/entities/alert-incident.entity';\r\n\r\n/**\r\n * Cross-Module Integration Tests\r\n * \r\n * Comprehensive cross-module integration testing including:\r\n * - Workflow Execution ↔ Monitoring integration\r\n * - Workflow Execution ↔ Templates integration\r\n * - Monitoring ↔ Alerting integration\r\n * - Data flow validation across module boundaries\r\n * - Event propagation and handling across modules\r\n * - Performance impact of cross-module operations\r\n * - Error handling and recovery across modules\r\n */\r\ndescribe('Cross-Module Integration Tests', () => {\r\n  let testSuite: CrossModuleIntegrationTest;\r\n\r\n  beforeEach(async () => {\r\n    testSuite = new CrossModuleIntegrationTest();\r\n    await testSuite.beforeEach();\r\n  });\r\n\r\n  afterEach(async () => {\r\n    await testSuite.afterEach();\r\n  });\r\n\r\n  describe('Workflow Execution ↔ Monitoring Integration', () => {\r\n    it('should collect metrics during workflow execution', async () => {\r\n      await testSuite.testWorkflowExecutionMetricsCollection();\r\n    });\r\n\r\n    it('should trigger health checks during workflow execution', async () => {\r\n      await testSuite.testWorkflowExecutionHealthChecks();\r\n    });\r\n\r\n    it('should generate alerts for workflow execution failures', async () => {\r\n      await testSuite.testWorkflowExecutionAlerting();\r\n    });\r\n\r\n    it('should track performance metrics for workflow steps', async () => {\r\n      await testSuite.testWorkflowStepPerformanceTracking();\r\n    });\r\n  });\r\n\r\n  describe('Workflow Execution ↔ Templates Integration', () => {\r\n    it('should validate template compatibility with execution engine', async () => {\r\n      await testSuite.testTemplateExecutionCompatibility();\r\n    });\r\n\r\n    it('should handle template updates during active executions', async () => {\r\n      await testSuite.testTemplateUpdateHandling();\r\n    });\r\n\r\n    it('should validate template input/output schemas', async () => {\r\n      await testSuite.testTemplateSchemaValidation();\r\n    });\r\n  });\r\n\r\n  describe('Data Flow Validation', () => {\r\n    it('should maintain data consistency across module boundaries', async () => {\r\n      await testSuite.testCrossModuleDataConsistency();\r\n    });\r\n\r\n    it('should handle concurrent operations across modules', async () => {\r\n      await testSuite.testConcurrentCrossModuleOperations();\r\n    });\r\n\r\n    it('should validate transaction boundaries across modules', async () => {\r\n      await testSuite.testCrossModuleTransactions();\r\n    });\r\n  });\r\n\r\n  describe('Event Propagation', () => {\r\n    it('should propagate events correctly across modules', async () => {\r\n      await testSuite.testCrossModuleEventPropagation();\r\n    });\r\n\r\n    it('should handle event ordering and dependencies', async () => {\r\n      await testSuite.testEventOrderingAndDependencies();\r\n    });\r\n\r\n    it('should recover from event handling failures', async () => {\r\n      await testSuite.testEventHandlingFailureRecovery();\r\n    });\r\n  });\r\n\r\n  describe('Performance Impact', () => {\r\n    it('should measure cross-module operation performance', async () => {\r\n      await testSuite.testCrossModulePerformance();\r\n    });\r\n\r\n    it('should validate resource usage during cross-module operations', async () => {\r\n      await testSuite.testCrossModuleResourceUsage();\r\n    });\r\n\r\n    it('should handle load balancing across modules', async () => {\r\n      await testSuite.testCrossModuleLoadBalancing();\r\n    });\r\n  });\r\n\r\n  describe('Error Handling and Recovery', () => {\r\n    it('should handle cascading failures across modules', async () => {\r\n      await testSuite.testCascadingFailureHandling();\r\n    });\r\n\r\n    it('should implement circuit breakers for cross-module calls', async () => {\r\n      await testSuite.testCrossModuleCircuitBreakers();\r\n    });\r\n\r\n    it('should provide graceful degradation across modules', async () => {\r\n      await testSuite.testCrossModuleGracefulDegradation();\r\n    });\r\n  });\r\n});\r\n\r\nclass CrossModuleIntegrationTest extends IntegrationTestBase {\r\n  private executionEngine: WorkflowExecutionEngineService;\r\n  private metricsCollector: MetricsCollectorService;\r\n  private healthCheckService: HealthCheckService;\r\n  private alertingService: IntelligentAlertingService;\r\n  private performanceMonitoring: PerformanceMonitoringService;\r\n\r\n  protected async createTestingModule(): Promise<TestingModule> {\r\n    return Test.createTestingModule({\r\n      imports: [\r\n        ConfigModule.forRoot({\r\n          isGlobal: true,\r\n          envFilePath: '.env.test',\r\n        }),\r\n        TypeOrmModule.forRootAsync({\r\n          useFactory: (configService: TestConfigurationService) => \r\n            configService.getTestDatabaseConfig(),\r\n          inject: [TestConfigurationService],\r\n        }),\r\n        EventEmitterModule.forRoot(),\r\n        WorkflowExecutionModule,\r\n        MonitoringModule,\r\n        WorkflowTemplatesModule,\r\n      ],\r\n      providers: [\r\n        TestConfigurationService,\r\n        TestDataService,\r\n      ],\r\n    }).compile();\r\n  }\r\n\r\n  protected async setupTestData(): Promise<void> {\r\n    // Get services from all modules\r\n    this.executionEngine = this.testingModule.get<WorkflowExecutionEngineService>(\r\n      WorkflowExecutionEngineService\r\n    );\r\n    this.metricsCollector = this.testingModule.get<MetricsCollectorService>(\r\n      MetricsCollectorService\r\n    );\r\n    this.healthCheckService = this.testingModule.get<HealthCheckService>(\r\n      HealthCheckService\r\n    );\r\n    this.alertingService = this.testingModule.get<IntelligentAlertingService>(\r\n      IntelligentAlertingService\r\n    );\r\n    this.performanceMonitoring = this.testingModule.get<PerformanceMonitoringService>(\r\n      PerformanceMonitoringService\r\n    );\r\n\r\n    // Seed test data across all modules\r\n    await this.testDataService.seedTestData({\r\n      templates: 5,\r\n      executions: 10,\r\n      contexts: 2,\r\n      metrics: 50,\r\n      healthChecks: 20,\r\n      alertRules: 10,\r\n      incidents: 5,\r\n    });\r\n  }\r\n\r\n  @IntegrationTest('Workflow execution metrics collection integration')\r\n  async testWorkflowExecutionMetricsCollection(): Promise<void> {\r\n    // Get test template\r\n    const templates = await this.testDataService.generateWorkflowTemplates(1);\r\n    const template = templates[0];\r\n\r\n    // Clear existing metrics\r\n    this.clearEventHistory();\r\n\r\n    // Start workflow execution\r\n    const execution = await this.executionEngine.startExecution({\r\n      templateId: template.id,\r\n      executionName: 'Metrics Collection Test',\r\n      inputData: { test: 'metrics' },\r\n      triggeredBy: 'test-user',\r\n      executionConfig: { enableMetrics: true },\r\n    });\r\n\r\n    // Wait for execution to progress\r\n    await this.sleep(2000);\r\n\r\n    // Verify metrics were collected\r\n    const metricsSnapshot = await this.dataSource\r\n      .getRepository(MetricSnapshot)\r\n      .find({\r\n        where: {\r\n          metricName: 'workflow_executions_started',\r\n        },\r\n        order: { timestamp: 'DESC' },\r\n        take: 1,\r\n      });\r\n\r\n    expect(metricsSnapshot).toHaveLength(1);\r\n    expect(metricsSnapshot[0].labels).toContain(template.id);\r\n\r\n    // Verify performance metrics were recorded\r\n    const performanceMetrics = await this.dataSource\r\n      .getRepository(PerformanceMetric)\r\n      .find({\r\n        where: {\r\n          component: 'workflow_engine',\r\n        },\r\n        order: { timestamp: 'DESC' },\r\n        take: 5,\r\n      });\r\n\r\n    expect(performanceMetrics.length).toBeGreaterThan(0);\r\n\r\n    // Verify events were emitted for metrics collection\r\n    const metricsEvents = this.getEventHistory('metrics.recorded');\r\n    expect(metricsEvents.length).toBeGreaterThan(0);\r\n  }\r\n\r\n  @IntegrationTest('Workflow execution health checks integration')\r\n  async testWorkflowExecutionHealthChecks(): Promise<void> {\r\n    // Register workflow-specific health check\r\n    this.healthCheckService.registerHealthCheck('workflow_execution_engine', {\r\n      execute: async () => ({\r\n        status: 'healthy',\r\n        message: 'Workflow execution engine operational',\r\n        details: {\r\n          activeExecutions: 0,\r\n          queueSize: 0,\r\n        },\r\n      }),\r\n      timeout: 5000,\r\n      critical: true,\r\n      tags: ['workflow', 'execution'],\r\n    });\r\n\r\n    // Execute health check\r\n    const healthResult = await this.healthCheckService.executeHealthCheck('workflow_execution_engine');\r\n\r\n    expect(healthResult.status).toBe('healthy');\r\n    expect(healthResult.details).toBeDefined();\r\n\r\n    // Verify health check result was persisted\r\n    const persistedResults = await this.dataSource\r\n      .getRepository(HealthCheckResult)\r\n      .find({\r\n        where: {\r\n          checkName: 'workflow_execution_engine',\r\n        },\r\n        order: { timestamp: 'DESC' },\r\n        take: 1,\r\n      });\r\n\r\n    expect(persistedResults).toHaveLength(1);\r\n    expect(persistedResults[0].status).toBe('healthy');\r\n  }\r\n\r\n  @IntegrationTest('Workflow execution alerting integration')\r\n  async testWorkflowExecutionAlerting(): Promise<void> {\r\n    // Create alert rule for workflow failures\r\n    const alertRule = await this.dataSource\r\n      .getRepository('AlertRule')\r\n      .save({\r\n        name: 'Workflow Execution Failure Alert',\r\n        description: 'Alert when workflow execution fails',\r\n        metricName: 'workflow_executions_failed',\r\n        condition: {\r\n          type: 'threshold',\r\n          operator: 'greater_than',\r\n          value: 0,\r\n          window: '5m',\r\n        },\r\n        severity: 'critical',\r\n        enabled: true,\r\n        escalation: {\r\n          levels: [\r\n            { delay: 0, channels: ['email'], recipients: ['<EMAIL>'] },\r\n          ],\r\n        },\r\n      });\r\n\r\n    // Simulate workflow failure by triggering metric\r\n    this.metricsCollector.incrementCounter('workflow_executions_failed', {\r\n      template_id: 'test-template',\r\n      error_type: 'TestError',\r\n    });\r\n\r\n    // Wait for alert processing\r\n    await this.sleep(1000);\r\n\r\n    // Verify alert incident was created\r\n    const incidents = await this.dataSource\r\n      .getRepository(AlertIncident)\r\n      .find({\r\n        where: {\r\n          ruleId: alertRule.id,\r\n        },\r\n        order: { startTime: 'DESC' },\r\n        take: 1,\r\n      });\r\n\r\n    expect(incidents).toHaveLength(1);\r\n    expect(incidents[0].status).toBe('active');\r\n    expect(incidents[0].severity).toBe('critical');\r\n  }\r\n\r\n  @IntegrationTest('Workflow step performance tracking integration')\r\n  async testWorkflowStepPerformanceTracking(): Promise<void> {\r\n    // Create template with multiple steps\r\n    const template = await this.testDataService.templateRepository.save({\r\n      name: 'Performance Tracking Test',\r\n      description: 'Template for performance tracking',\r\n      definition: {\r\n        steps: [\r\n          { id: 'step1', type: 'notification', config: { message: 'Step 1' } },\r\n          { id: 'step2', type: 'validation', config: { rules: [] } },\r\n          { id: 'step3', type: 'data_transformation', config: { type: 'map' } },\r\n        ],\r\n      },\r\n      createdBy: 'test-system',\r\n      updatedBy: 'test-system',\r\n    });\r\n\r\n    // Start execution with performance monitoring\r\n    const execution = await this.executionEngine.startExecution({\r\n      templateId: template.id,\r\n      executionName: 'Performance Tracking Test',\r\n      inputData: { performance: 'test' },\r\n      triggeredBy: 'test-user',\r\n      executionConfig: {\r\n        enableMetrics: true,\r\n        enableTracing: true,\r\n      },\r\n    });\r\n\r\n    // Wait for execution to complete\r\n    await this.waitFor(async () => {\r\n      const updated = await this.dataSource\r\n        .getRepository(WorkflowExecution)\r\n        .findOne({ where: { id: execution.id } });\r\n      return updated?.status === 'completed' || updated?.status === 'failed';\r\n    }, 15000);\r\n\r\n    // Verify performance metrics were recorded for each step\r\n    const stepMetrics = await this.dataSource\r\n      .getRepository(PerformanceMetric)\r\n      .find({\r\n        where: {\r\n          component: 'workflow_engine',\r\n          operation: 'step_execution',\r\n        },\r\n        order: { timestamp: 'DESC' },\r\n      });\r\n\r\n    expect(stepMetrics.length).toBeGreaterThanOrEqual(3); // One for each step\r\n\r\n    // Verify step-specific performance data\r\n    const finalExecution = await this.dataSource\r\n      .getRepository(WorkflowExecution)\r\n      .findOne({ where: { id: execution.id } });\r\n\r\n    expect(finalExecution.performanceMetrics).toBeDefined();\r\n    expect(finalExecution.performanceMetrics.totalSteps).toBe(3);\r\n    expect(finalExecution.performanceMetrics.avgStepDuration).toBeGreaterThan(0);\r\n  }\r\n\r\n  @IntegrationTest('Cross-module data consistency validation')\r\n  async testCrossModuleDataConsistency(): Promise<void> {\r\n    // Start workflow execution\r\n    const templates = await this.testDataService.generateWorkflowTemplates(1);\r\n    const execution = await this.executionEngine.startExecution({\r\n      templateId: templates[0].id,\r\n      executionName: 'Data Consistency Test',\r\n      inputData: { consistency: 'test' },\r\n      triggeredBy: 'test-user',\r\n    });\r\n\r\n    // Wait for some execution progress\r\n    await this.sleep(1000);\r\n\r\n    // Verify data consistency across modules\r\n    await this.withTransaction(async (manager) => {\r\n      // Check workflow execution exists\r\n      const workflowExecution = await manager.findOne(WorkflowExecution, {\r\n        where: { id: execution.id },\r\n      });\r\n      expect(workflowExecution).toBeDefined();\r\n\r\n      // Check corresponding metrics exist\r\n      const relatedMetrics = await manager.find(MetricSnapshot, {\r\n        where: {\r\n          metricName: 'workflow_executions_started',\r\n        },\r\n      });\r\n      expect(relatedMetrics.length).toBeGreaterThan(0);\r\n\r\n      // Check performance metrics exist\r\n      const performanceMetrics = await manager.find(PerformanceMetric, {\r\n        where: {\r\n          component: 'workflow_engine',\r\n        },\r\n      });\r\n      expect(performanceMetrics.length).toBeGreaterThan(0);\r\n\r\n      // Verify referential integrity\r\n      const executionCount = await manager.count(WorkflowExecution);\r\n      const contextCount = await manager.count('WorkflowExecutionContext');\r\n      \r\n      // Each execution should have at least one context\r\n      expect(contextCount).toBeGreaterThanOrEqual(executionCount);\r\n    });\r\n  }\r\n\r\n  @IntegrationTest('Cross-module event propagation validation')\r\n  async testCrossModuleEventPropagation(): Promise<void> {\r\n    this.clearEventHistory();\r\n\r\n    // Start workflow execution to trigger cross-module events\r\n    const templates = await this.testDataService.generateWorkflowTemplates(1);\r\n    const execution = await this.executionEngine.startExecution({\r\n      templateId: templates[0].id,\r\n      executionName: 'Event Propagation Test',\r\n      inputData: { events: 'test' },\r\n      triggeredBy: 'test-user',\r\n      executionConfig: { enableMetrics: true },\r\n    });\r\n\r\n    // Wait for events to propagate\r\n    await this.sleep(2000);\r\n\r\n    // Verify workflow events were emitted\r\n    const workflowEvents = this.getEventHistory('workflow.execution.started');\r\n    expect(workflowEvents).toHaveLength(1);\r\n    expect(workflowEvents[0].args[0].executionId).toBe(execution.id);\r\n\r\n    // Verify metrics events were emitted\r\n    const metricsEvents = this.getEventHistory('metrics.recorded');\r\n    expect(metricsEvents.length).toBeGreaterThan(0);\r\n\r\n    // Verify monitoring events were emitted\r\n    const monitoringEvents = this.getEventHistory().filter(e => \r\n      e.event.startsWith('monitoring.') || e.event.startsWith('health.')\r\n    );\r\n    expect(monitoringEvents.length).toBeGreaterThan(0);\r\n\r\n    // Verify event ordering (workflow events should come before metrics events)\r\n    const allEvents = this.getEventHistory().sort((a, b) => \r\n      a.timestamp.getTime() - b.timestamp.getTime()\r\n    );\r\n\r\n    const workflowEventIndex = allEvents.findIndex(e => e.event === 'workflow.execution.started');\r\n    const metricsEventIndex = allEvents.findIndex(e => e.event === 'metrics.recorded');\r\n\r\n    if (workflowEventIndex !== -1 && metricsEventIndex !== -1) {\r\n      expect(workflowEventIndex).toBeLessThan(metricsEventIndex);\r\n    }\r\n  }\r\n\r\n  @IntegrationTest('Cross-module performance impact measurement')\r\n  async testCrossModulePerformance(): Promise<void> {\r\n    const templates = await this.testDataService.generateWorkflowTemplates(1);\r\n\r\n    // Measure performance of cross-module operation\r\n    const { result: execution, duration } = await this.measurePerformance(\r\n      () => this.executionEngine.startExecution({\r\n        templateId: templates[0].id,\r\n        executionName: 'Performance Test',\r\n        inputData: { performance: 'test' },\r\n        triggeredBy: 'test-user',\r\n        executionConfig: {\r\n          enableMetrics: true,\r\n          enableTracing: true,\r\n        },\r\n      }),\r\n      'cross_module_execution_start'\r\n    );\r\n\r\n    // Verify performance is within acceptable limits\r\n    expect(duration).toBeLessThan(5000); // 5 seconds max for startup\r\n\r\n    // Measure metrics collection performance\r\n    const { duration: metricsCollectionDuration } = await this.measurePerformance(\r\n      () => this.metricsCollector.recordBusinessMetric({\r\n        name: 'test_metric',\r\n        value: 1,\r\n        type: 'counter',\r\n        category: 'test',\r\n        labels: { test: 'true' },\r\n      }),\r\n      'metrics_collection'\r\n    );\r\n\r\n    expect(metricsCollectionDuration).toBeLessThan(100); // 100ms max for metrics\r\n\r\n    // Get performance statistics\r\n    const executionStats = this.getPerformanceStats('cross_module_execution_start');\r\n    const metricsStats = this.getPerformanceStats('metrics_collection');\r\n\r\n    expect(executionStats).toBeDefined();\r\n    expect(metricsStats).toBeDefined();\r\n\r\n    // Verify performance consistency\r\n    if (executionStats && executionStats.count > 1) {\r\n      const variance = executionStats.max - executionStats.min;\r\n      expect(variance).toBeLessThan(executionStats.avg * 2); // Variance should be reasonable\r\n    }\r\n  }\r\n\r\n  // Additional test method implementations...\r\n  async testTemplateExecutionCompatibility(): Promise<void> {\r\n    // Implementation for template compatibility testing\r\n  }\r\n\r\n  async testTemplateUpdateHandling(): Promise<void> {\r\n    // Implementation for template update handling testing\r\n  }\r\n\r\n  async testTemplateSchemaValidation(): Promise<void> {\r\n    // Implementation for template schema validation testing\r\n  }\r\n\r\n  async testConcurrentCrossModuleOperations(): Promise<void> {\r\n    // Implementation for concurrent operations testing\r\n  }\r\n\r\n  async testCrossModuleTransactions(): Promise<void> {\r\n    // Implementation for transaction boundary testing\r\n  }\r\n\r\n  async testEventOrderingAndDependencies(): Promise<void> {\r\n    // Implementation for event ordering testing\r\n  }\r\n\r\n  async testEventHandlingFailureRecovery(): Promise<void> {\r\n    // Implementation for event failure recovery testing\r\n  }\r\n\r\n  async testCrossModuleResourceUsage(): Promise<void> {\r\n    // Implementation for resource usage testing\r\n  }\r\n\r\n  async testCrossModuleLoadBalancing(): Promise<void> {\r\n    // Implementation for load balancing testing\r\n  }\r\n\r\n  async testCascadingFailureHandling(): Promise<void> {\r\n    // Implementation for cascading failure testing\r\n  }\r\n\r\n  async testCrossModuleCircuitBreakers(): Promise<void> {\r\n    // Implementation for circuit breaker testing\r\n  }\r\n\r\n  async testCrossModuleGracefulDegradation(): Promise<void> {\r\n    // Implementation for graceful degradation testing\r\n  }\r\n}\r\n"], "version": 3}