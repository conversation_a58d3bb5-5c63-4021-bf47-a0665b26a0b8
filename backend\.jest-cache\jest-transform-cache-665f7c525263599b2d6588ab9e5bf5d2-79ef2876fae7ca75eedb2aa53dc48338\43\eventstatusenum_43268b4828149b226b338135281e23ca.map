{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\event-status.enum.ts", "mappings": ";;;AAAA,IAAY,WAQX;AARD,WAAY,WAAW;IACrB,gCAAiB,CAAA;IACjB,8CAA+B,CAAA;IAC/B,sCAAuB,CAAA;IACvB,oCAAqB,CAAA;IACrB,gCAAiB,CAAA;IACjB,gDAAiC,CAAA;IACjC,kCAAmB,CAAA;AACrB,CAAC,EARW,WAAW,2BAAX,WAAW,QAQtB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\event-status.enum.ts"], "sourcesContent": ["export enum EventStatus {\r\n  ACTIVE = 'ACTIVE',\r\n  INVESTIGATING = 'INVESTIGATING',\r\n  MITIGATED = 'MITIGATED',\r\n  RESOLVED = 'RESOLVED',\r\n  CLOSED = 'CLOSED',\r\n  FALSE_POSITIVE = 'FALSE_POSITIVE',\r\n  IGNORED = 'IGNORED',\r\n} "], "version": 3}