45be1a07f00f882ad210cd0b6587dc79
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnrichedEventEnrichmentFailedDomainEvent = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
/**
 * Enriched Event Enrichment Failed Domain Event
 *
 * Raised when enrichment processing fails for a security event.
 * This event triggers various downstream processes including:
 * - Error handling and recovery workflows
 * - Retry logic and backoff strategies
 * - Alert generation for operational issues
 * - Metrics collection for failure analysis
 * - Fallback processing mechanisms
 */
class EnrichedEventEnrichmentFailedDomainEvent extends shared_kernel_1.BaseDomainEvent {
    constructor(aggregateId, eventData, options) {
        super(aggregateId, eventData, options);
    }
    /**
     * Get the normalized event ID
     */
    get normalizedEventId() {
        return this.eventData.normalizedEventId;
    }
    /**
     * Get the error message
     */
    get error() {
        return this.eventData.error;
    }
    /**
     * Get the current attempt number
     */
    get attempt() {
        return this.eventData.attempt;
    }
    /**
     * Check if maximum attempts have been exceeded
     */
    get maxAttemptsExceeded() {
        return this.eventData.maxAttemptsExceeded;
    }
    /**
     * Get the timestamp of the failure
     */
    get timestamp() {
        return this.eventData.timestamp || this.occurredOn;
    }
    /**
     * Get the error code
     */
    get errorCode() {
        return this.eventData.errorCode;
    }
    /**
     * Get the error category
     */
    get errorCategory() {
        return this.eventData.errorCategory;
    }
    /**
     * Get the failed enrichment sources
     */
    get failedSources() {
        return this.eventData.failedSources || [];
    }
    /**
     * Get partial results that were obtained
     */
    get partialResults() {
        return this.eventData.partialResults || {};
    }
    /**
     * Get additional error context
     */
    get errorContext() {
        return this.eventData.errorContext || {};
    }
    /**
     * Check if this is the first attempt
     */
    isFirstAttempt() {
        return this.attempt === 1;
    }
    /**
     * Check if retry is possible
     */
    canRetry() {
        return !this.maxAttemptsExceeded;
    }
    /**
     * Check if partial results are available
     */
    hasPartialResults() {
        return Object.keys(this.partialResults).length > 0;
    }
    /**
     * Check if multiple sources failed
     */
    hasMultipleSourceFailures() {
        return this.failedSources.length > 1;
    }
    /**
     * Check if this is a network-related error
     */
    isNetworkError() {
        return this.errorCategory === 'network' ||
            this.error.toLowerCase().includes('network') ||
            this.error.toLowerCase().includes('connection') ||
            this.error.toLowerCase().includes('timeout');
    }
    /**
     * Check if this is a validation error
     */
    isValidationError() {
        return this.errorCategory === 'validation' ||
            this.error.toLowerCase().includes('validation') ||
            this.error.toLowerCase().includes('invalid');
    }
    /**
     * Check if this is a rate limiting error
     */
    isRateLimitError() {
        return this.errorCategory === 'rate_limit' ||
            this.error.toLowerCase().includes('rate limit') ||
            this.error.toLowerCase().includes('quota') ||
            this.error.toLowerCase().includes('throttle');
    }
    /**
     * Check if this is an authentication error
     */
    isAuthenticationError() {
        return this.errorCategory === 'authentication' ||
            this.error.toLowerCase().includes('auth') ||
            this.error.toLowerCase().includes('unauthorized') ||
            this.error.toLowerCase().includes('forbidden');
    }
    /**
     * Check if this is a configuration error
     */
    isConfigurationError() {
        return this.errorCategory === 'configuration' ||
            this.error.toLowerCase().includes('config') ||
            this.error.toLowerCase().includes('setting');
    }
    /**
     * Check if this is a data format error
     */
    isDataFormatError() {
        return this.errorCategory === 'data_format' ||
            this.error.toLowerCase().includes('format') ||
            this.error.toLowerCase().includes('parse') ||
            this.error.toLowerCase().includes('json') ||
            this.error.toLowerCase().includes('xml');
    }
    /**
     * Get error severity level
     */
    getErrorSeverity() {
        if (this.maxAttemptsExceeded) {
            return 'critical';
        }
        if (this.isAuthenticationError() || this.isConfigurationError()) {
            return 'high';
        }
        if (this.isNetworkError() || this.isRateLimitError()) {
            return 'medium';
        }
        if (this.isValidationError() || this.isDataFormatError()) {
            return 'low';
        }
        return 'medium';
    }
    /**
     * Get recommended retry delay in milliseconds
     */
    getRecommendedRetryDelay() {
        if (this.maxAttemptsExceeded) {
            return 0; // No retry
        }
        // Exponential backoff based on attempt number
        const baseDelay = 1000; // 1 second
        const exponentialDelay = baseDelay * Math.pow(2, this.attempt - 1);
        // Add jitter to prevent thundering herd
        const jitter = Math.random() * 1000;
        // Different delays based on error type
        if (this.isRateLimitError()) {
            return exponentialDelay * 2 + jitter; // Longer delay for rate limits
        }
        if (this.isNetworkError()) {
            return exponentialDelay + jitter;
        }
        if (this.isValidationError() || this.isDataFormatError()) {
            return baseDelay + jitter; // Short delay for data issues
        }
        return exponentialDelay + jitter;
    }
    /**
     * Check if error is recoverable
     */
    isRecoverable() {
        // Non-recoverable errors
        if (this.isAuthenticationError() ||
            this.isConfigurationError() ||
            this.isValidationError() ||
            this.isDataFormatError()) {
            return false;
        }
        // Recoverable errors
        if (this.isNetworkError() || this.isRateLimitError()) {
            return true;
        }
        // Default to recoverable for unknown errors
        return true;
    }
    /**
     * Get failure impact assessment
     */
    getFailureImpact() {
        const severity = this.getErrorSeverity();
        const recoverable = this.isRecoverable();
        const canRetry = this.canRetry();
        const hasPartialResults = this.hasPartialResults();
        let recommendedAction = 'Monitor and retry';
        if (this.maxAttemptsExceeded) {
            recommendedAction = 'Manual intervention required';
        }
        else if (!recoverable) {
            recommendedAction = 'Fix configuration or data format';
        }
        else if (this.isRateLimitError()) {
            recommendedAction = 'Wait and retry with backoff';
        }
        else if (this.isNetworkError()) {
            recommendedAction = 'Check network connectivity and retry';
        }
        return {
            severity,
            recoverable,
            canRetry,
            hasPartialResults,
            recommendedAction,
        };
    }
    /**
     * Get event summary for handlers
     */
    getEventSummary() {
        return {
            enrichedEventId: this.aggregateId.toString(),
            normalizedEventId: this.normalizedEventId.toString(),
            error: this.error,
            errorCode: this.errorCode,
            errorCategory: this.errorCategory,
            attempt: this.attempt,
            maxAttemptsExceeded: this.maxAttemptsExceeded,
            failedSourcesCount: this.failedSources.length,
            isFirstAttempt: this.isFirstAttempt(),
            canRetry: this.canRetry(),
            hasPartialResults: this.hasPartialResults(),
            isNetworkError: this.isNetworkError(),
            isValidationError: this.isValidationError(),
            isRateLimitError: this.isRateLimitError(),
            isAuthenticationError: this.isAuthenticationError(),
            errorSeverity: this.getErrorSeverity(),
            recommendedRetryDelay: this.getRecommendedRetryDelay(),
            isRecoverable: this.isRecoverable(),
            failureImpact: this.getFailureImpact(),
        };
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            ...super.toJSON(),
            eventSummary: this.getEventSummary(),
        };
    }
}
exports.EnrichedEventEnrichmentFailedDomainEvent = EnrichedEventEnrichmentFailedDomainEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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