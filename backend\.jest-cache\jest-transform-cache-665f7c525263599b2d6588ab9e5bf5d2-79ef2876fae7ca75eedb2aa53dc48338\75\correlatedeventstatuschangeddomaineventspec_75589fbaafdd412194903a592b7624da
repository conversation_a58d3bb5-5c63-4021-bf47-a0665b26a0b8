1686fb3017cc8a31e1f8c6d69598f605
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const correlated_event_status_changed_domain_event_1 = require("../correlated-event-status-changed.domain-event");
const correlation_status_enum_1 = require("../../enums/correlation-status.enum");
const shared_kernel_1 = require("../../../../../shared-kernel");
describe('CorrelatedEventStatusChangedDomainEvent', () => {
    let eventData;
    let aggregateId;
    let mockResult;
    beforeEach(() => {
        aggregateId = shared_kernel_1.UniqueEntityId.create();
        mockResult = {
            success: true,
            appliedRules: ['rule1', 'rule2'],
            failedRules: [],
            warnings: ['Warning 1'],
            errors: [],
            processingDurationMs: 1500,
            confidenceScore: 85,
            rulesUsed: 2,
            matchesFound: 5,
            patternsIdentified: ['temporal_sequence', 'ip_clustering']
        };
        eventData = {
            oldStatus: correlation_status_enum_1.CorrelationStatus.IN_PROGRESS,
            newStatus: correlation_status_enum_1.CorrelationStatus.COMPLETED,
            result: mockResult,
            correlationQualityScore: 85,
            requiresManualReview: false,
            changedAt: new Date(),
            reason: 'Correlation completed successfully'
        };
    });
    describe('creation', () => {
        it('should create domain event with required data', () => {
            const domainEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, eventData);
            expect(domainEvent.aggregateId).toEqual(aggregateId);
            expect(domainEvent.eventData).toEqual(eventData);
            expect(domainEvent.eventName).toBe('CorrelatedEventStatusChangedDomainEvent');
            expect(domainEvent.occurredOn).toBeInstanceOf(Date);
            expect(domainEvent.eventId).toBeDefined();
        });
        it('should create domain event with custom options', () => {
            const customEventId = shared_kernel_1.UniqueEntityId.create();
            const customOccurredOn = new Date('2023-01-01T00:00:00Z');
            const correlationId = 'corr_123';
            const domainEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, eventData, {
                eventId: customEventId,
                occurredOn: customOccurredOn,
                correlationId,
                eventVersion: 2,
                metadata: { custom: 'data' }
            });
            expect(domainEvent.eventId).toEqual(customEventId);
            expect(domainEvent.occurredOn).toEqual(customOccurredOn);
            expect(domainEvent.correlationId).toBe(correlationId);
            expect(domainEvent.eventVersion).toBe(2);
            expect(domainEvent.metadata).toEqual({ custom: 'data' });
        });
    });
    describe('property getters', () => {
        let domainEvent;
        beforeEach(() => {
            domainEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, eventData);
        });
        it('should return old status', () => {
            expect(domainEvent.oldStatus).toBe(eventData.oldStatus);
        });
        it('should return new status', () => {
            expect(domainEvent.newStatus).toBe(eventData.newStatus);
        });
        it('should return correlation result', () => {
            expect(domainEvent.result).toEqual(eventData.result);
        });
        it('should return correlation quality score', () => {
            expect(domainEvent.correlationQualityScore).toBe(eventData.correlationQualityScore);
        });
        it('should return requires manual review flag', () => {
            expect(domainEvent.requiresManualReview).toBe(eventData.requiresManualReview);
        });
        it('should return changed at timestamp', () => {
            expect(domainEvent.changedAt).toEqual(eventData.changedAt);
        });
        it('should return reason', () => {
            expect(domainEvent.reason).toBe(eventData.reason);
        });
        it('should return change metadata', () => {
            const eventWithMetadata = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                changeMetadata: { custom: 'metadata' }
            });
            expect(eventWithMetadata.changeMetadata).toEqual({ custom: 'metadata' });
        });
        it('should return empty object for change metadata when not provided', () => {
            expect(domainEvent.changeMetadata).toEqual({});
        });
        it('should use occurred on when changed at is not provided', () => {
            const eventWithoutChangedAt = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                changedAt: undefined
            });
            expect(eventWithoutChangedAt.changedAt).toEqual(eventWithoutChangedAt.occurredOn);
        });
    });
    describe('status transition detection', () => {
        it('should detect completed transition', () => {
            const completedEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                newStatus: correlation_status_enum_1.CorrelationStatus.COMPLETED
            });
            expect(completedEvent.isCompletedTransition()).toBe(true);
            expect(completedEvent.isFailedTransition()).toBe(false);
            expect(completedEvent.isInProgressTransition()).toBe(false);
        });
        it('should detect failed transition', () => {
            const failedEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                newStatus: correlation_status_enum_1.CorrelationStatus.FAILED
            });
            expect(failedEvent.isFailedTransition()).toBe(true);
            expect(failedEvent.isCompletedTransition()).toBe(false);
        });
        it('should detect in progress transition', () => {
            const inProgressEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: correlation_status_enum_1.CorrelationStatus.PENDING,
                newStatus: correlation_status_enum_1.CorrelationStatus.IN_PROGRESS
            });
            expect(inProgressEvent.isInProgressTransition()).toBe(true);
            expect(inProgressEvent.isCompletedTransition()).toBe(false);
        });
        it('should detect partial transition', () => {
            const partialEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                newStatus: correlation_status_enum_1.CorrelationStatus.PARTIAL
            });
            expect(partialEvent.isPartialTransition()).toBe(true);
            expect(partialEvent.isCompletedTransition()).toBe(false);
        });
        it('should detect skipped transition', () => {
            const skippedEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                newStatus: correlation_status_enum_1.CorrelationStatus.SKIPPED
            });
            expect(skippedEvent.isSkippedTransition()).toBe(true);
            expect(skippedEvent.isCompletedTransition()).toBe(false);
        });
        it('should detect timeout transition', () => {
            const timeoutEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                newStatus: correlation_status_enum_1.CorrelationStatus.TIMEOUT
            });
            expect(timeoutEvent.isTimeoutTransition()).toBe(true);
            expect(timeoutEvent.isFailedTransition()).toBe(false); // timeout is not considered failed
        });
    });
    describe('success and failure detection', () => {
        it('should detect successful completion', () => {
            const successfulEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                newStatus: correlation_status_enum_1.CorrelationStatus.COMPLETED,
                result: { ...mockResult, success: true }
            });
            expect(successfulEvent.isSuccessfulCompletion()).toBe(true);
        });
        it('should not detect successful completion for failed result', () => {
            const failedResultEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                newStatus: correlation_status_enum_1.CorrelationStatus.COMPLETED,
                result: { ...mockResult, success: false }
            });
            expect(failedResultEvent.isSuccessfulCompletion()).toBe(false);
        });
        it('should detect failure transitions', () => {
            const failedEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                newStatus: correlation_status_enum_1.CorrelationStatus.FAILED
            });
            const timeoutEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                newStatus: correlation_status_enum_1.CorrelationStatus.TIMEOUT
            });
            const completedFailedEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                newStatus: correlation_status_enum_1.CorrelationStatus.COMPLETED,
                result: { ...mockResult, success: false }
            });
            expect(failedEvent.isFailureTransition()).toBe(true);
            expect(timeoutEvent.isFailureTransition()).toBe(true);
            expect(completedFailedEvent.isFailureTransition()).toBe(true);
        });
    });
    describe('quality and progress assessment', () => {
        it('should detect quality improvement', () => {
            const qualityEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                correlationQualityScore: 85
            });
            const lowQualityEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                correlationQualityScore: 50
            });
            const noQualityEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                correlationQualityScore: undefined
            });
            expect(qualityEvent.hasQualityImprovement()).toBe(true);
            expect(lowQualityEvent.hasQualityImprovement()).toBe(false);
            expect(noQualityEvent.hasQualityImprovement()).toBe(false);
        });
        it('should detect progress transitions', () => {
            const progressEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: correlation_status_enum_1.CorrelationStatus.PENDING,
                newStatus: correlation_status_enum_1.CorrelationStatus.IN_PROGRESS
            });
            const completionEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: correlation_status_enum_1.CorrelationStatus.IN_PROGRESS,
                newStatus: correlation_status_enum_1.CorrelationStatus.COMPLETED
            });
            const failureEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: correlation_status_enum_1.CorrelationStatus.IN_PROGRESS,
                newStatus: correlation_status_enum_1.CorrelationStatus.FAILED
            });
            expect(progressEvent.isProgressTransition()).toBe(true);
            expect(completionEvent.isProgressTransition()).toBe(true);
            expect(failureEvent.isProgressTransition()).toBe(false);
        });
        it('should detect regression transitions', () => {
            const regressionEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: correlation_status_enum_1.CorrelationStatus.COMPLETED,
                newStatus: correlation_status_enum_1.CorrelationStatus.PENDING
            });
            const progressEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: correlation_status_enum_1.CorrelationStatus.PENDING,
                newStatus: correlation_status_enum_1.CorrelationStatus.IN_PROGRESS
            });
            expect(regressionEvent.isRegressionTransition()).toBe(true);
            expect(progressEvent.isRegressionTransition()).toBe(false);
        });
    });
    describe('transition type classification', () => {
        it('should classify transition types correctly', () => {
            const completionEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                newStatus: correlation_status_enum_1.CorrelationStatus.COMPLETED,
                result: { ...mockResult, success: true }
            });
            const failureEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                newStatus: correlation_status_enum_1.CorrelationStatus.FAILED
            });
            const progressEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: correlation_status_enum_1.CorrelationStatus.PENDING,
                newStatus: correlation_status_enum_1.CorrelationStatus.IN_PROGRESS
            });
            const regressionEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: correlation_status_enum_1.CorrelationStatus.COMPLETED,
                newStatus: correlation_status_enum_1.CorrelationStatus.PENDING
            });
            const neutralEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: correlation_status_enum_1.CorrelationStatus.SKIPPED,
                newStatus: correlation_status_enum_1.CorrelationStatus.SKIPPED
            });
            expect(completionEvent.getTransitionType()).toBe('completion');
            expect(failureEvent.getTransitionType()).toBe('failure');
            expect(progressEvent.getTransitionType()).toBe('progress');
            expect(regressionEvent.getTransitionType()).toBe('regression');
            expect(neutralEvent.getTransitionType()).toBe('neutral');
        });
    });
    describe('notification priority', () => {
        it('should calculate high priority for successful completion with quality', () => {
            const highPriorityEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                newStatus: correlation_status_enum_1.CorrelationStatus.COMPLETED,
                result: { ...mockResult, success: true },
                correlationQualityScore: 85
            });
            expect(highPriorityEvent.getNotificationPriority()).toBe('high');
        });
        it('should calculate high priority for failure transitions', () => {
            const failureEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                newStatus: correlation_status_enum_1.CorrelationStatus.FAILED
            });
            expect(failureEvent.getNotificationPriority()).toBe('high');
        });
        it('should calculate medium priority for manual review requirements', () => {
            const reviewEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                requiresManualReview: true
            });
            expect(reviewEvent.getNotificationPriority()).toBe('medium');
        });
        it('should calculate low priority for progress transitions', () => {
            const progressEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: correlation_status_enum_1.CorrelationStatus.PENDING,
                newStatus: correlation_status_enum_1.CorrelationStatus.IN_PROGRESS
            });
            expect(progressEvent.getNotificationPriority()).toBe('low');
        });
    });
    describe('recommended actions', () => {
        it('should recommend actions for successful completion', () => {
            const successEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                newStatus: correlation_status_enum_1.CorrelationStatus.COMPLETED,
                result: { ...mockResult, success: true },
                requiresManualReview: true
            });
            const actions = successEvent.getRecommendedActions();
            expect(actions).toContain('Review correlation results');
            expect(actions).toContain('Validate identified patterns');
            expect(actions).toContain('Proceed to next processing stage');
            expect(actions).toContain('Schedule manual review');
        });
        it('should recommend actions for failed completion', () => {
            const failedEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                newStatus: correlation_status_enum_1.CorrelationStatus.COMPLETED,
                result: { ...mockResult, success: false }
            });
            const actions = failedEvent.getRecommendedActions();
            expect(actions).toContain('Investigate correlation issues');
            expect(actions).toContain('Review failed rules and errors');
        });
        it('should recommend actions for correlation failure', () => {
            const failureEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                newStatus: correlation_status_enum_1.CorrelationStatus.FAILED
            });
            const actions = failureEvent.getRecommendedActions();
            expect(actions).toContain('Investigate correlation failure');
            expect(actions).toContain('Review error logs and diagnostics');
            expect(actions).toContain('Consider retry with adjusted parameters');
            expect(actions).toContain('Escalate to correlation engine team');
        });
        it('should recommend actions for partial correlation', () => {
            const partialEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                newStatus: correlation_status_enum_1.CorrelationStatus.PARTIAL
            });
            const actions = partialEvent.getRecommendedActions();
            expect(actions).toContain('Review partial correlation results');
            expect(actions).toContain('Identify missing correlation data');
            expect(actions).toContain('Consider additional correlation rules');
            expect(actions).toContain('Evaluate if results are sufficient');
        });
        it('should recommend actions for timeout', () => {
            const timeoutEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                newStatus: correlation_status_enum_1.CorrelationStatus.TIMEOUT
            });
            const actions = timeoutEvent.getRecommendedActions();
            expect(actions).toContain('Investigate correlation performance');
            expect(actions).toContain('Review correlation rule complexity');
            expect(actions).toContain('Consider increasing timeout limits');
            expect(actions).toContain('Retry with optimized parameters');
        });
        it('should recommend actions for in progress status', () => {
            const inProgressEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                newStatus: correlation_status_enum_1.CorrelationStatus.IN_PROGRESS
            });
            const actions = inProgressEvent.getRecommendedActions();
            expect(actions).toContain('Monitor correlation progress');
            expect(actions).toContain('Track processing metrics');
        });
        it('should recommend actions for skipped status', () => {
            const skippedEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                newStatus: correlation_status_enum_1.CorrelationStatus.SKIPPED
            });
            const actions = skippedEvent.getRecommendedActions();
            expect(actions).toContain('Review skip reason');
            expect(actions).toContain('Validate skip criteria');
        });
    });
    describe('metrics calculation', () => {
        it('should calculate metrics for status transitions', () => {
            const domainEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, eventData);
            const metrics = domainEvent.getMetricsToUpdate();
            const transitionMetric = metrics.find(m => m.metric === 'correlation_status_transitions');
            expect(transitionMetric).toBeDefined();
            expect(transitionMetric.value).toBe(1);
            expect(transitionMetric.tags).toEqual({
                from_status: correlation_status_enum_1.CorrelationStatus.IN_PROGRESS,
                to_status: correlation_status_enum_1.CorrelationStatus.COMPLETED,
                transition_type: 'completion'
            });
        });
        it('should calculate quality score metrics', () => {
            const domainEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, eventData);
            const metrics = domainEvent.getMetricsToUpdate();
            const qualityMetric = metrics.find(m => m.metric === 'correlation_quality_score');
            expect(qualityMetric).toBeDefined();
            expect(qualityMetric.value).toBe(85);
            expect(qualityMetric.tags).toEqual({
                status: correlation_status_enum_1.CorrelationStatus.COMPLETED
            });
        });
        it('should calculate success metrics for successful completion', () => {
            const successEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                result: { ...mockResult, success: true }
            });
            const metrics = successEvent.getMetricsToUpdate();
            const successMetric = metrics.find(m => m.metric === 'correlation_completions_successful');
            expect(successMetric).toBeDefined();
            expect(successMetric.value).toBe(1);
        });
        it('should calculate failure metrics for failed transitions', () => {
            const failureEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                newStatus: correlation_status_enum_1.CorrelationStatus.FAILED
            });
            const metrics = failureEvent.getMetricsToUpdate();
            const failureMetric = metrics.find(m => m.metric === 'correlation_completions_failed');
            expect(failureMetric).toBeDefined();
            expect(failureMetric.value).toBe(1);
            expect(failureMetric.tags).toEqual({
                failure_type: correlation_status_enum_1.CorrelationStatus.FAILED
            });
        });
        it('should calculate processing duration metrics', () => {
            const domainEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, eventData);
            const metrics = domainEvent.getMetricsToUpdate();
            const durationMetric = metrics.find(m => m.metric === 'correlation_processing_duration_ms');
            expect(durationMetric).toBeDefined();
            expect(durationMetric.value).toBe(1500);
            expect(durationMetric.tags).toEqual({
                status: correlation_status_enum_1.CorrelationStatus.COMPLETED
            });
        });
        it('should calculate rules and matches metrics', () => {
            const domainEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, eventData);
            const metrics = domainEvent.getMetricsToUpdate();
            const rulesMetric = metrics.find(m => m.metric === 'correlation_rules_applied');
            const matchesMetric = metrics.find(m => m.metric === 'correlation_matches_found');
            const patternsMetric = metrics.find(m => m.metric === 'correlation_patterns_identified');
            expect(rulesMetric).toBeDefined();
            expect(rulesMetric.value).toBe(2);
            expect(matchesMetric).toBeDefined();
            expect(matchesMetric.value).toBe(5);
            expect(patternsMetric).toBeDefined();
            expect(patternsMetric.value).toBe(2);
        });
    });
    describe('event summary', () => {
        it('should provide comprehensive event summary', () => {
            const domainEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, eventData);
            const summary = domainEvent.getEventSummary();
            expect(summary.correlatedEventId).toBe(aggregateId.toString());
            expect(summary.oldStatus).toBe(eventData.oldStatus);
            expect(summary.newStatus).toBe(eventData.newStatus);
            expect(summary.correlationQualityScore).toBe(eventData.correlationQualityScore);
            expect(summary.requiresManualReview).toBe(eventData.requiresManualReview);
            expect(summary.changedAt).toEqual(eventData.changedAt);
            expect(summary.reason).toBe(eventData.reason);
            expect(summary.isCompletedTransition).toBe(true);
            expect(summary.isFailedTransition).toBe(false);
            expect(summary.isSuccessfulCompletion).toBe(true);
            expect(summary.isFailureTransition).toBe(false);
            expect(summary.hasQualityImprovement).toBe(true);
            expect(summary.isProgressTransition).toBe(true);
            expect(summary.isRegressionTransition).toBe(false);
            expect(summary.transitionType).toBe('completion');
            expect(summary.notificationPriority).toBe('high');
            expect(summary.recommendedActions).toBeInstanceOf(Array);
            expect(summary.metricsToUpdate).toBeInstanceOf(Array);
            expect(summary.result).toEqual(eventData.result);
        });
    });
    describe('JSON serialization', () => {
        it('should serialize to JSON with event summary', () => {
            const domainEvent = new correlated_event_status_changed_domain_event_1.CorrelatedEventStatusChangedDomainEvent(aggregateId, eventData);
            const json = domainEvent.toJSON();
            expect(json.eventName).toBe('CorrelatedEventStatusChangedDomainEvent');
            expect(json.aggregateId).toBe(aggregateId.toString());
            expect(json.eventData).toEqual(eventData);
            expect(json.eventSummary).toBeDefined();
            expect(json.occurredOn).toBeInstanceOf(Date);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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