{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\application\\services\\vulnerability-assessment.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,yFAAoF;AACpF,mGAA8G;AAC9G,kFAAyE;AACzE,oFAA2E;AAC3E,kHAAiG;AACjG,iGAA4F;AAG5F,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;IAC9C,IAAI,OAAuC,CAAC;IAC5C,IAAI,kBAAqD,CAAC;IAE1D,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,kBAAkB,GAAG;YACnB,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;SACf,CAAC;QAET,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,iEAA8B;gBAC9B;oBACE,OAAO,EAAE,6CAAoB;oBAC7B,QAAQ,EAAE,kBAAkB;iBAC7B;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAAiC,iEAA8B,CAAC,CAAC;IACvF,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,MAAM,aAAa,GAAG,uBAAuB,CAAC;gBAC5C,QAAQ,EAAE,qCAAc,CAAC,QAAQ;gBACjC,UAAU,EAAE,uCAAe,CAAC,IAAI;gBAChC,SAAS,EAAE,EAAE;gBACb,SAAS,EAAE,GAAG;gBACd,WAAW,EAAE,IAAI;gBACjB,qBAAqB,EAAE,IAAI;aAC5B,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC;gBAC/C,aAAa;gBACb,OAAO,EAAE;oBACP,sBAAsB,EAAE,IAAI;oBAC5B,yBAAyB,EAAE,IAAI;oBAC/B,qBAAqB,EAAE,IAAI;oBAC3B,uBAAuB,EAAE,IAAI;iBAC9B;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,WAAW,EAAE,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,MAAM,aAAa,GAAG,uBAAuB,CAAC;gBAC5C,QAAQ,EAAE,qCAAc,CAAC,MAAM;gBAC/B,UAAU,EAAE,uCAAe,CAAC,MAAM;gBAClC,SAAS,EAAE,EAAE;gBACb,SAAS,EAAE,GAAG;gBACd,WAAW,EAAE,KAAK;gBAClB,qBAAqB,EAAE,KAAK;aAC7B,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC;gBAC/C,aAAa;gBACb,OAAO,EAAE;oBACP,sBAAsB,EAAE,IAAI;iBAC7B;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;YACpE,MAAM,aAAa,GAAG,uBAAuB,CAAC;gBAC5C,QAAQ,EAAE,qCAAc,CAAC,IAAI;gBAC7B,UAAU,EAAE,uCAAe,CAAC,SAAS;gBACrC,SAAS,EAAE,EAAE;gBACb,SAAS,EAAE,GAAG;gBACd,WAAW,EAAE,IAAI;gBACjB,kBAAkB,EAAE,IAAI;gBACxB,qBAAqB,EAAE,IAAI;aAC5B,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC;gBAC/C,aAAa;gBACb,OAAO,EAAE;oBACP,sBAAsB,EAAE,IAAI;oBAC5B,qBAAqB,EAAE,IAAI;iBAC5B;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAChE,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC9D,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,MAAM,aAAa,GAAG,uBAAuB,CAAC;gBAC5C,QAAQ,EAAE,qCAAc,CAAC,IAAI;gBAC7B,UAAU,EAAE,uCAAe,CAAC,IAAI;gBAChC,SAAS,EAAE,EAAE;gBACb,mBAAmB,EAAE,IAAI;aAC1B,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG;gBACd,sBAAsB,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,KAAK,CAAC;gBAClD,aAAa,EAAE,KAAc;aAC9B,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC;gBAC/C,aAAa;gBACb,OAAO;gBACP,OAAO,EAAE;oBACP,uBAAuB,EAAE,IAAI;iBAC9B;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;YACzF,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE,iBAAiB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,oBAAoB,GAAG,IAAW,CAAC;YAEzC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC;gBAC/C,aAAa,EAAE,oBAAoB;aACpC,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,aAAa,GAAG,uBAAuB,CAAC;gBAC5C,QAAQ,EAAE,qCAAc,CAAC,IAAI;gBAC7B,UAAU,EAAE,uCAAe,CAAC,IAAI;gBAChC,SAAS,EAAE,EAAE;gBACb,SAAS,EAAE,GAAG;gBACd,qBAAqB,EAAE,IAAI;gBAC3B,mBAAmB,EAAE,IAAI;aAC1B,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC;gBAC/C,aAAa;aACd,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAC9D,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAC7D,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,aAAa,GAAG,uBAAuB,CAAC;gBAC5C,QAAQ,EAAE,qCAAc,CAAC,QAAQ;gBACjC,UAAU,EAAE,uCAAe,CAAC,IAAI;gBAChC,SAAS,EAAE,EAAE;gBACb,WAAW,EAAE,IAAI;gBACjB,qBAAqB,EAAE,IAAI;gBAC3B,KAAK,EAAE,gBAAgB;aACxB,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC;gBAC/C,aAAa;aACd,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACnE,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;YAC3E,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,MAAM,eAAe,GAAG;gBACtB,uBAAuB,CAAC;oBACtB,QAAQ,EAAE,qCAAc,CAAC,QAAQ;oBACjC,SAAS,EAAE,EAAE;iBACd,CAAC;gBACF,uBAAuB,CAAC;oBACtB,QAAQ,EAAE,qCAAc,CAAC,IAAI;oBAC7B,SAAS,EAAE,EAAE;iBACd,CAAC;gBACF,uBAAuB,CAAC;oBACtB,QAAQ,EAAE,qCAAc,CAAC,MAAM;oBAC/B,SAAS,EAAE,EAAE;iBACd,CAAC;aACH,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,yBAAyB,CAAC;gBACrD,eAAe;gBACf,MAAM,EAAE;oBACN,SAAS,EAAE,CAAC;oBACZ,uBAAuB,EAAE,IAAI;iBAC9B;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,KAAK,IAAI,EAAE;YAC1E,MAAM,eAAe,GAAG;gBACtB,uBAAuB,CAAC;oBACtB,EAAE,EAAE,QAAQ;oBACZ,QAAQ,EAAE,qCAAc,CAAC,MAAM;oBAC/B,SAAS,EAAE,EAAE;iBACd,CAAC;gBACF,uBAAuB,CAAC;oBACtB,EAAE,EAAE,QAAQ;oBACZ,QAAQ,EAAE,qCAAc,CAAC,QAAQ;oBACjC,SAAS,EAAE,EAAE;iBACd,CAAC;gBACF,uBAAuB,CAAC;oBACtB,EAAE,EAAE,QAAQ;oBACZ,QAAQ,EAAE,qCAAc,CAAC,IAAI;oBAC7B,SAAS,EAAE,EAAE;iBACd,CAAC;aACH,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,yBAAyB,CAAC;gBACrD,eAAe;gBACf,MAAM,EAAE;oBACN,kBAAkB,EAAE,IAAI;iBACzB;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,2BAA2B;YACrF,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,wBAAwB;YAClF,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,wBAAwB;QACpF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,eAAe,GAAG;gBACtB,uBAAuB,CAAC;oBACtB,QAAQ,EAAE,qCAAc,CAAC,QAAQ;oBACjC,SAAS,EAAE,EAAE;iBACd,CAAC;gBACF,IAAW,EAAE,2BAA2B;gBACxC,uBAAuB,CAAC;oBACtB,QAAQ,EAAE,qCAAc,CAAC,IAAI;oBAC7B,SAAS,EAAE,EAAE;iBACd,CAAC;aACH,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,yBAAyB,CAAC;gBACrD,eAAe,EAAE,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC;aACjD,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,MAAM,eAAe,GAAG;gBACtB,uBAAuB,CAAC;oBACtB,QAAQ,EAAE,qCAAc,CAAC,QAAQ;oBACjC,SAAS,EAAE,EAAE;iBACd,CAAC;gBACF,uBAAuB,CAAC;oBACtB,QAAQ,EAAE,qCAAc,CAAC,QAAQ;oBACjC,SAAS,EAAE,EAAE;iBACd,CAAC;gBACF,uBAAuB,CAAC;oBACtB,QAAQ,EAAE,qCAAc,CAAC,IAAI;oBAC7B,SAAS,EAAE,EAAE;iBACd,CAAC;gBACF,uBAAuB,CAAC;oBACtB,QAAQ,EAAE,qCAAc,CAAC,MAAM;oBAC/B,SAAS,EAAE,EAAE;iBACd,CAAC;gBACF,uBAAuB,CAAC;oBACtB,QAAQ,EAAE,qCAAc,CAAC,GAAG;oBAC5B,SAAS,EAAE,EAAE;iBACd,CAAC;aACH,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,yBAAyB,CAAC;gBACrD,eAAe;aAChB,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACzD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,WAAW,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,0BAA0B;YACtF,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAC1D,uBAAuB,CAAC;gBACtB,EAAE,EAAE,QAAQ,CAAC,EAAE;gBACf,QAAQ,EAAE,qCAAc,CAAC,MAAM;gBAC/B,SAAS,EAAE,EAAE;aACd,CAAC,CACH,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,yBAAyB,CAAC;gBACrD,eAAe;gBACf,MAAM,EAAE;oBACN,SAAS,EAAE,CAAC;iBACb;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,aAAa,GAAG,uBAAuB,CAAC;gBAC5C,QAAQ,EAAE,qCAAc,CAAC,IAAI;gBAC7B,SAAS,EAAE,EAAE;aACd,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC;gBAC/C,aAAa;gBACb,OAAO,EAAE;oBACP,sBAAsB,EAAE,IAAI;oBAC5B,yBAAyB,EAAE,IAAI;oBAC/B,qBAAqB,EAAE,IAAI;oBAC3B,uBAAuB,EAAE,IAAI;iBAC9B;aACF,CAAC,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAExC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,kCAAkC;YACvE,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAC1D,uBAAuB,CAAC;gBACtB,EAAE,EAAE,QAAQ,CAAC,EAAE;gBACf,QAAQ,EAAE,qCAAc,CAAC,MAAM;gBAC/B,SAAS,EAAE,EAAE,GAAG,CAAC;aAClB,CAAC,CACH,CAAC;YAEF,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,yBAAyB,CAAC;gBACrD,eAAe;gBACf,MAAM,EAAE;oBACN,SAAS,EAAE,EAAE;iBACd;aACF,CAAC,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAExC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACtC,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,mCAAmC;QAC1E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,iDAAiD;IACjD,SAAS,uBAAuB,CAAC,UAY7B,EAAE;QACJ,MAAM,EACJ,EAAE,GAAG,aAAa,EAClB,QAAQ,GAAG,qCAAc,CAAC,MAAM,EAChC,UAAU,GAAG,uCAAe,CAAC,MAAM,EACnC,SAAS,GAAG,EAAE,EACd,SAAS,GAAG,GAAG,EACf,WAAW,GAAG,KAAK,EACnB,kBAAkB,GAAG,KAAK,EAC1B,qBAAqB,GAAG,KAAK,EAC7B,mBAAmB,GAAG,KAAK,EAC3B,mBAAmB,GAAG,KAAK,EAC3B,KAAK,GACN,GAAG,OAAO,CAAC;QAEZ,MAAM,iBAAiB,GAAG;YACxB,EAAE,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE;YAC1B,KAAK;YACL,KAAK,EAAE,sBAAsB,EAAE,EAAE;YACjC,WAAW,EAAE,gCAAgC;YAC7C,QAAQ;YACR,UAAU;YACV,QAAQ,EAAE,eAAe;YACzB,IAAI,EAAE,WAAW;YACjB,MAAM,EAAE,0CAAmB,CAAC,IAAI;YAChC,cAAc,EAAE;gBACd,SAAS;gBACT,YAAY,EAAE;oBACZ,eAAe,EAAE,qBAAqB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK;oBACvD,kBAAkB,EAAE,qBAAqB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ;oBAC7D,iBAAiB,EAAE,qBAAqB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK;iBAC1D;aACF;YACD,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC;gBACtB,mCAAS,CAAC,MAAM,CACd,SAAS,EACT,8CAA8C,EAC9C,KAAK,CACN;aACF,CAAC,CAAC,CAAC,EAAE;YACN,YAAY,EAAE,WAAW,CAAC,CAAC,CAAC;gBAC1B,MAAM,EAAE,kBAAkB,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,kBAAkB;gBACvE,iBAAiB,EAAE;oBACjB;wBACE,IAAI,EAAE,QAAQ;wBACd,WAAW,EAAE,EAAE;wBACf,UAAU,EAAE,QAAQ;wBACpB,MAAM,EAAE,YAAY;qBACrB;iBACF;gBACD,UAAU,EAAE,QAAiB;gBAC7B,aAAa,EAAE,CAAC,SAAS,CAAC;aAC3B,CAAC,CAAC,CAAC,SAAS;YACb,cAAc,EAAE;gBACd;oBACE,OAAO,EAAE,SAAS;oBAClB,SAAS,EAAE,YAAY;oBACvB,SAAS,EAAE,QAAQ;oBACnB,WAAW,EAAE,qBAAqB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ;oBAC1D,QAAQ,EAAE,mBAAmB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU;oBACvD,MAAM,EAAE,QAAQ;oBAChB,MAAM,EAAE,aAAa;oBACrB,cAAc,EAAE,aAAa;oBAC7B,gBAAgB,EAAE,EAAE;iBACrB;aACF;YACD,gBAAgB,EAAE,mBAAmB,CAAC,CAAC,CAAC;gBACtC;oBACE,UAAU,EAAE,SAAS;oBACrB,WAAW,EAAE,KAAK;oBAClB,MAAM,EAAE,MAAM;oBACd,MAAM,EAAE,eAAe;iBACxB;aACF,CAAC,CAAC,CAAC,EAAE;YACN,WAAW,EAAE;gBACX,OAAO,EAAE,CAAC,aAAa,CAAC;gBACxB,QAAQ,EAAE;oBACR,YAAY,EAAE,IAAI,IAAI,EAAE;oBACxB,iBAAiB,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;iBAClE;gBACD,IAAI,EAAE;oBACJ,SAAS,EAAE,IAAI;iBAChB;aACF;YACD,IAAI,EAAE,EAAE;YACR,UAAU,EAAE,EAAE;YACd,UAAU,EAAE,GAAG,EAAE,CAAC,QAAQ,KAAK,qCAAc,CAAC,QAAQ;YACtD,qBAAqB,EAAE,GAAG,EAAE,CAAC,qBAAqB;YAClD,mBAAmB,EAAE,GAAG,EAAE,CAAC,mBAAmB;YAC9C,eAAe,EAAE,GAAG,EAAE,CAAC,EAAE;YACzB,iBAAiB,EAAE,GAAG,EAAE,GAAE,CAAC;SACrB,CAAC;QAET,OAAO,iBAAiB,CAAC;IAC3B,CAAC;AACH,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\application\\services\\vulnerability-assessment.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { VulnerabilityAssessmentService } from './vulnerability-assessment.service';\r\nimport { Vulnerability, VulnerabilityStatus } from '../../domain/entities/vulnerability/vulnerability.entity';\r\nimport { ThreatSeverity } from '../../domain/enums/threat-severity.enum';\r\nimport { ConfidenceLevel } from '../../domain/enums/confidence-level.enum';\r\nimport { CVSSScore } from '../../domain/value-objects/threat-indicators/cvss-score.value-object';\r\nimport { DomainEventPublisher } from '../../../shared-kernel/domain/domain-event-publisher';\r\nimport { UniqueEntityId } from '../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\n\r\ndescribe('VulnerabilityAssessmentService', () => {\r\n  let service: VulnerabilityAssessmentService;\r\n  let mockEventPublisher: jest.Mocked<DomainEventPublisher>;\r\n\r\n  beforeEach(async () => {\r\n    mockEventPublisher = {\r\n      publishAll: jest.fn(),\r\n    } as any;\r\n\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        VulnerabilityAssessmentService,\r\n        {\r\n          provide: DomainEventPublisher,\r\n          useValue: mockEventPublisher,\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    service = module.get<VulnerabilityAssessmentService>(VulnerabilityAssessmentService);\r\n  });\r\n\r\n  afterEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('assessVulnerability', () => {\r\n    it('should assess a critical vulnerability successfully', async () => {\r\n      const vulnerability = createMockVulnerability({\r\n        severity: ThreatSeverity.CRITICAL,\r\n        confidence: ConfidenceLevel.HIGH,\r\n        riskScore: 95,\r\n        cvssScore: 9.8,\r\n        hasExploits: true,\r\n        affectsCriticalAssets: true,\r\n      });\r\n\r\n      const result = await service.assessVulnerability({\r\n        vulnerability,\r\n        options: {\r\n          includeExploitAnalysis: true,\r\n          includeThreatIntelligence: true,\r\n          includeBusinessImpact: true,\r\n          includeComplianceImpact: true,\r\n        },\r\n      });\r\n\r\n      expect(result.success).toBe(true);\r\n      expect(result.summary.overallRisk).toBe('critical');\r\n      expect(result.summary.priority).toBe('critical');\r\n      expect(result.summary.urgency).toBe('immediate');\r\n      expect(result.exploitAnalysis).toBeDefined();\r\n      expect(result.threatIntelligence).toBeDefined();\r\n      expect(result.businessImpact).toBeDefined();\r\n      expect(result.complianceImpact).toBeDefined();\r\n      expect(result.recommendations.immediate).toContain('isolate_affected_systems');\r\n    });\r\n\r\n    it('should assess a medium severity vulnerability', async () => {\r\n      const vulnerability = createMockVulnerability({\r\n        severity: ThreatSeverity.MEDIUM,\r\n        confidence: ConfidenceLevel.MEDIUM,\r\n        riskScore: 55,\r\n        cvssScore: 5.5,\r\n        hasExploits: false,\r\n        affectsCriticalAssets: false,\r\n      });\r\n\r\n      const result = await service.assessVulnerability({\r\n        vulnerability,\r\n        options: {\r\n          includeExploitAnalysis: true,\r\n        },\r\n      });\r\n\r\n      expect(result.success).toBe(true);\r\n      expect(result.summary.overallRisk).toBe('medium');\r\n      expect(result.summary.priority).toBe('medium');\r\n      expect(result.summary.urgency).toBe('medium');\r\n      expect(result.exploitAnalysis?.exploitability).toBe('low');\r\n      expect(result.recommendations.immediate.length).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should handle vulnerability with active exploitation', async () => {\r\n      const vulnerability = createMockVulnerability({\r\n        severity: ThreatSeverity.HIGH,\r\n        confidence: ConfidenceLevel.CONFIRMED,\r\n        riskScore: 85,\r\n        cvssScore: 8.2,\r\n        hasExploits: true,\r\n        activeExploitation: true,\r\n        affectsCriticalAssets: true,\r\n      });\r\n\r\n      const result = await service.assessVulnerability({\r\n        vulnerability,\r\n        options: {\r\n          includeExploitAnalysis: true,\r\n          includeBusinessImpact: true,\r\n        },\r\n      });\r\n\r\n      expect(result.success).toBe(true);\r\n      expect(result.summary.overallRisk).toBe('critical');\r\n      expect(result.exploitAnalysis?.exploitability).toBe('critical');\r\n      expect(result.businessImpact?.operationalImpact).toBe('high');\r\n      expect(result.recommendations.immediate).toContain('immediate_isolation');\r\n    });\r\n\r\n    it('should assess vulnerability with compliance impact', async () => {\r\n      const vulnerability = createMockVulnerability({\r\n        severity: ThreatSeverity.HIGH,\r\n        confidence: ConfidenceLevel.HIGH,\r\n        riskScore: 75,\r\n        hasComplianceImpact: true,\r\n      });\r\n\r\n      const context = {\r\n        complianceRequirements: ['PCI_DSS', 'GDPR', 'SOX'],\r\n        riskTolerance: 'low' as const,\r\n      };\r\n\r\n      const result = await service.assessVulnerability({\r\n        vulnerability,\r\n        context,\r\n        options: {\r\n          includeComplianceImpact: true,\r\n        },\r\n      });\r\n\r\n      expect(result.success).toBe(true);\r\n      expect(result.complianceImpact).toBeDefined();\r\n      expect(result.complianceImpact?.regulationsAffected).toEqual(['PCI_DSS', 'GDPR', 'SOX']);\r\n      expect(result.complianceImpact?.reportingRequired).toBe(true);\r\n    });\r\n\r\n    it('should handle assessment errors gracefully', async () => {\r\n      const invalidVulnerability = null as any;\r\n\r\n      const result = await service.assessVulnerability({\r\n        vulnerability: invalidVulnerability,\r\n      });\r\n\r\n      expect(result.success).toBe(false);\r\n      expect(result.summary.overallRisk).toBe('medium');\r\n      expect(result.recommendations.immediate).toContain('Retry assessment');\r\n    });\r\n\r\n    it('should calculate risk analysis correctly', async () => {\r\n      const vulnerability = createMockVulnerability({\r\n        severity: ThreatSeverity.HIGH,\r\n        confidence: ConfidenceLevel.HIGH,\r\n        riskScore: 80,\r\n        cvssScore: 7.5,\r\n        affectsCriticalAssets: true,\r\n        isExternallyExposed: true,\r\n      });\r\n\r\n      const result = await service.assessVulnerability({\r\n        vulnerability,\r\n      });\r\n\r\n      expect(result.success).toBe(true);\r\n      expect(result.riskAnalysis.technicalRisk).toBeGreaterThan(70);\r\n      expect(result.riskAnalysis.businessRisk).toBeGreaterThan(50);\r\n      expect(result.riskAnalysis.combinedRisk).toBeGreaterThan(60);\r\n    });\r\n\r\n    it('should generate appropriate recommendations', async () => {\r\n      const vulnerability = createMockVulnerability({\r\n        severity: ThreatSeverity.CRITICAL,\r\n        confidence: ConfidenceLevel.HIGH,\r\n        riskScore: 95,\r\n        hasExploits: true,\r\n        affectsCriticalAssets: true,\r\n        cveId: 'CVE-2023-12345',\r\n      });\r\n\r\n      const result = await service.assessVulnerability({\r\n        vulnerability,\r\n      });\r\n\r\n      expect(result.success).toBe(true);\r\n      expect(result.recommendations.immediate.length).toBeGreaterThan(0);\r\n      expect(result.recommendations.shortTerm).toContain('Apply vendor patches');\r\n      expect(result.recommendations.preventive).toContain('Implement vulnerability scanning');\r\n    });\r\n  });\r\n\r\n  describe('assessVulnerabilitiesBulk', () => {\r\n    it('should assess multiple vulnerabilities successfully', async () => {\r\n      const vulnerabilities = [\r\n        createMockVulnerability({\r\n          severity: ThreatSeverity.CRITICAL,\r\n          riskScore: 95,\r\n        }),\r\n        createMockVulnerability({\r\n          severity: ThreatSeverity.HIGH,\r\n          riskScore: 75,\r\n        }),\r\n        createMockVulnerability({\r\n          severity: ThreatSeverity.MEDIUM,\r\n          riskScore: 55,\r\n        }),\r\n      ];\r\n\r\n      const result = await service.assessVulnerabilitiesBulk({\r\n        vulnerabilities,\r\n        config: {\r\n          batchSize: 2,\r\n          includeDetailedAnalysis: true,\r\n        },\r\n      });\r\n\r\n      expect(result.success).toBe(true);\r\n      expect(result.totalAssessed).toBe(3);\r\n      expect(result.successfulAssessments).toBe(3);\r\n      expect(result.failedAssessments).toBe(0);\r\n      expect(result.results).toHaveLength(3);\r\n      expect(result.summary.criticalVulnerabilities).toBe(1);\r\n      expect(result.summary.highRiskVulnerabilities).toBe(1);\r\n      expect(result.summary.mediumRiskVulnerabilities).toBe(1);\r\n    });\r\n\r\n    it('should prioritize critical vulnerabilities when configured', async () => {\r\n      const vulnerabilities = [\r\n        createMockVulnerability({\r\n          id: 'vuln-1',\r\n          severity: ThreatSeverity.MEDIUM,\r\n          riskScore: 55,\r\n        }),\r\n        createMockVulnerability({\r\n          id: 'vuln-2',\r\n          severity: ThreatSeverity.CRITICAL,\r\n          riskScore: 95,\r\n        }),\r\n        createMockVulnerability({\r\n          id: 'vuln-3',\r\n          severity: ThreatSeverity.HIGH,\r\n          riskScore: 75,\r\n        }),\r\n      ];\r\n\r\n      const result = await service.assessVulnerabilitiesBulk({\r\n        vulnerabilities,\r\n        config: {\r\n          prioritizeCritical: true,\r\n        },\r\n      });\r\n\r\n      expect(result.success).toBe(true);\r\n      expect(result.results[0].vulnerabilityId).toBe('vuln-2'); // Critical should be first\r\n      expect(result.results[1].vulnerabilityId).toBe('vuln-3'); // High should be second\r\n      expect(result.results[2].vulnerabilityId).toBe('vuln-1'); // Medium should be last\r\n    });\r\n\r\n    it('should handle partial failures in bulk assessment', async () => {\r\n      const vulnerabilities = [\r\n        createMockVulnerability({\r\n          severity: ThreatSeverity.CRITICAL,\r\n          riskScore: 95,\r\n        }),\r\n        null as any, // This will cause an error\r\n        createMockVulnerability({\r\n          severity: ThreatSeverity.HIGH,\r\n          riskScore: 75,\r\n        }),\r\n      ];\r\n\r\n      const result = await service.assessVulnerabilitiesBulk({\r\n        vulnerabilities: vulnerabilities.filter(Boolean),\r\n      });\r\n\r\n      expect(result.success).toBe(true);\r\n      expect(result.successfulAssessments).toBe(2);\r\n      expect(result.failedAssessments).toBe(0);\r\n    });\r\n\r\n    it('should calculate bulk summary statistics correctly', async () => {\r\n      const vulnerabilities = [\r\n        createMockVulnerability({\r\n          severity: ThreatSeverity.CRITICAL,\r\n          riskScore: 95,\r\n        }),\r\n        createMockVulnerability({\r\n          severity: ThreatSeverity.CRITICAL,\r\n          riskScore: 90,\r\n        }),\r\n        createMockVulnerability({\r\n          severity: ThreatSeverity.HIGH,\r\n          riskScore: 75,\r\n        }),\r\n        createMockVulnerability({\r\n          severity: ThreatSeverity.MEDIUM,\r\n          riskScore: 55,\r\n        }),\r\n        createMockVulnerability({\r\n          severity: ThreatSeverity.LOW,\r\n          riskScore: 25,\r\n        }),\r\n      ];\r\n\r\n      const result = await service.assessVulnerabilitiesBulk({\r\n        vulnerabilities,\r\n      });\r\n\r\n      expect(result.success).toBe(true);\r\n      expect(result.summary.criticalVulnerabilities).toBe(2);\r\n      expect(result.summary.highRiskVulnerabilities).toBe(1);\r\n      expect(result.summary.mediumRiskVulnerabilities).toBe(1);\r\n      expect(result.summary.lowRiskVulnerabilities).toBe(1);\r\n      expect(result.summary.averageRiskScore).toBeCloseTo(68, 0); // (95+90+75+55+25)/5 = 68\r\n      expect(result.summary.topRecommendations.length).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should respect batch size configuration', async () => {\r\n      const vulnerabilities = Array.from({ length: 15 }, (_, i) =>\r\n        createMockVulnerability({\r\n          id: `vuln-${i}`,\r\n          severity: ThreatSeverity.MEDIUM,\r\n          riskScore: 50,\r\n        })\r\n      );\r\n\r\n      const result = await service.assessVulnerabilitiesBulk({\r\n        vulnerabilities,\r\n        config: {\r\n          batchSize: 5,\r\n        },\r\n      });\r\n\r\n      expect(result.success).toBe(true);\r\n      expect(result.totalAssessed).toBe(15);\r\n      expect(result.successfulAssessments).toBe(15);\r\n    });\r\n  });\r\n\r\n  describe('performance', () => {\r\n    it('should complete assessment within reasonable time', async () => {\r\n      const vulnerability = createMockVulnerability({\r\n        severity: ThreatSeverity.HIGH,\r\n        riskScore: 75,\r\n      });\r\n\r\n      const startTime = Date.now();\r\n      const result = await service.assessVulnerability({\r\n        vulnerability,\r\n        options: {\r\n          includeExploitAnalysis: true,\r\n          includeThreatIntelligence: true,\r\n          includeBusinessImpact: true,\r\n          includeComplianceImpact: true,\r\n        },\r\n      });\r\n      const duration = Date.now() - startTime;\r\n\r\n      expect(result.success).toBe(true);\r\n      expect(duration).toBeLessThan(1000); // Should complete within 1 second\r\n      expect(result.metadata.assessmentDuration).toBeLessThan(1000);\r\n    });\r\n\r\n    it('should handle large bulk assessments efficiently', async () => {\r\n      const vulnerabilities = Array.from({ length: 50 }, (_, i) =>\r\n        createMockVulnerability({\r\n          id: `vuln-${i}`,\r\n          severity: ThreatSeverity.MEDIUM,\r\n          riskScore: 50 + i,\r\n        })\r\n      );\r\n\r\n      const startTime = Date.now();\r\n      const result = await service.assessVulnerabilitiesBulk({\r\n        vulnerabilities,\r\n        config: {\r\n          batchSize: 10,\r\n        },\r\n      });\r\n      const duration = Date.now() - startTime;\r\n\r\n      expect(result.success).toBe(true);\r\n      expect(result.totalAssessed).toBe(50);\r\n      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds\r\n    });\r\n  });\r\n\r\n  // Helper function to create mock vulnerabilities\r\n  function createMockVulnerability(options: {\r\n    id?: string;\r\n    severity?: ThreatSeverity;\r\n    confidence?: ConfidenceLevel;\r\n    riskScore?: number;\r\n    cvssScore?: number;\r\n    hasExploits?: boolean;\r\n    activeExploitation?: boolean;\r\n    affectsCriticalAssets?: boolean;\r\n    isExternallyExposed?: boolean;\r\n    hasComplianceImpact?: boolean;\r\n    cveId?: string;\r\n  } = {}): Vulnerability {\r\n    const {\r\n      id = 'test-vuln-1',\r\n      severity = ThreatSeverity.MEDIUM,\r\n      confidence = ConfidenceLevel.MEDIUM,\r\n      riskScore = 50,\r\n      cvssScore = 5.0,\r\n      hasExploits = false,\r\n      activeExploitation = false,\r\n      affectsCriticalAssets = false,\r\n      isExternallyExposed = false,\r\n      hasComplianceImpact = false,\r\n      cveId,\r\n    } = options;\r\n\r\n    const mockVulnerability = {\r\n      id: { toString: () => id },\r\n      cveId,\r\n      title: `Test Vulnerability ${id}`,\r\n      description: 'Test vulnerability description',\r\n      severity,\r\n      confidence,\r\n      category: 'test_category',\r\n      type: 'test_type',\r\n      status: VulnerabilityStatus.OPEN,\r\n      riskAssessment: {\r\n        riskScore,\r\n        businessRisk: {\r\n          financialImpact: affectsCriticalAssets ? 100000 : 10000,\r\n          reputationalImpact: affectsCriticalAssets ? 'high' : 'medium',\r\n          operationalImpact: affectsCriticalAssets ? 'high' : 'low',\r\n        },\r\n      },\r\n      cvssScores: cvssScore ? [\r\n        CVSSScore.create(\r\n          cvssScore,\r\n          'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H',\r\n          '3.1'\r\n        )\r\n      ] : [],\r\n      exploitation: hasExploits ? {\r\n        status: activeExploitation ? 'active_exploitation' : 'proof_of_concept',\r\n        availableExploits: [\r\n          {\r\n            type: 'public',\r\n            reliability: 80,\r\n            complexity: 'medium',\r\n            source: 'exploit-db',\r\n          }\r\n        ],\r\n        difficulty: 'medium' as const,\r\n        attackVectors: ['network'],\r\n      } : undefined,\r\n      affectedAssets: [\r\n        {\r\n          assetId: 'asset-1',\r\n          assetName: 'Test Asset',\r\n          assetType: 'server',\r\n          criticality: affectsCriticalAssets ? 'critical' : 'medium',\r\n          exposure: isExternallyExposed ? 'external' : 'internal',\r\n          impact: 'medium',\r\n          status: 'operational',\r\n          recoveryStatus: 'not_started',\r\n          affectedServices: [],\r\n        }\r\n      ],\r\n      complianceImpact: hasComplianceImpact ? [\r\n        {\r\n          regulation: 'PCI_DSS',\r\n          requirement: '6.1',\r\n          impact: 'high',\r\n          status: 'non_compliant',\r\n        }\r\n      ] : [],\r\n      remediation: {\r\n        actions: ['apply_patch'],\r\n        timeline: {\r\n          plannedStart: new Date(),\r\n          plannedCompletion: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),\r\n        },\r\n        cost: {\r\n          estimated: 5000,\r\n        },\r\n      },\r\n      tags: [],\r\n      attributes: {},\r\n      isCritical: () => severity === ThreatSeverity.CRITICAL,\r\n      affectsCriticalAssets: () => affectsCriticalAssets,\r\n      isExternallyExposed: () => isExternallyExposed,\r\n      getDomainEvents: () => [],\r\n      clearDomainEvents: () => {},\r\n    } as any;\r\n\r\n    return mockVulnerability;\r\n  }\r\n});\r\n"], "version": 3}