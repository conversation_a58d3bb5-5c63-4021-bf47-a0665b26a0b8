7985648b2dbda2b3de7bc2a1c08b5dde
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", { value: true });
exports.IncidentTask = void 0;
const typeorm_1 = require("typeorm");
const incident_entity_1 = require("./incident.entity");
/**
 * Incident Task entity
 * Represents individual tasks within incident response workflows
 */
let IncidentTask = class IncidentTask {
    /**
     * Check if task is overdue
     */
    get isOverdue() {
        if (!this.dueDate || this.status === 'completed')
            return false;
        return new Date() > this.dueDate;
    }
    /**
     * Check if task is blocked
     */
    get isBlocked() {
        return this.status === 'blocked';
    }
    /**
     * Check if task requires approval
     */
    get requiresApproval() {
        return this.configuration?.approval?.required || false;
    }
    /**
     * Check if task is automated
     */
    get isAutomated() {
        return this.type === 'automated' && !!this.configuration?.automation;
    }
    /**
     * Get task progress percentage
     */
    get progressPercentage() {
        switch (this.status) {
            case 'pending':
                return 0;
            case 'in_progress':
                return 50;
            case 'completed':
                return 100;
            case 'failed':
            case 'cancelled':
            case 'blocked':
                return 0;
            default:
                return 0;
        }
    }
    /**
     * Start task execution
     */
    start(userId) {
        this.status = 'in_progress';
        this.assignedTo = userId;
        this.updatedBy = userId;
        if (!this.execution) {
            this.execution = {};
        }
        this.execution.startedAt = new Date().toISOString();
        this.execution.attempts = (this.execution.attempts || 0) + 1;
    }
    /**
     * Complete task
     */
    complete(userId, result, quality) {
        this.status = 'completed';
        this.updatedBy = userId;
        if (!this.execution) {
            this.execution = {};
        }
        this.execution.completedAt = new Date().toISOString();
        this.execution.result = 'success';
        if (result) {
            this.execution.output = result;
        }
        if (quality) {
            this.execution.quality = quality;
        }
        // Calculate duration
        if (this.execution.startedAt) {
            const startTime = new Date(this.execution.startedAt);
            const endTime = new Date(this.execution.completedAt);
            this.execution.duration = Math.round((endTime.getTime() - startTime.getTime()) / 1000);
        }
    }
    /**
     * Fail task
     */
    fail(userId, errorMessage) {
        this.status = 'failed';
        this.updatedBy = userId;
        if (!this.execution) {
            this.execution = {};
        }
        this.execution.completedAt = new Date().toISOString();
        this.execution.result = 'failure';
        this.execution.errorMessage = errorMessage;
        // Calculate duration
        if (this.execution.startedAt) {
            const startTime = new Date(this.execution.startedAt);
            const endTime = new Date(this.execution.completedAt);
            this.execution.duration = Math.round((endTime.getTime() - startTime.getTime()) / 1000);
        }
    }
    /**
     * Block task
     */
    block(userId, reason) {
        this.status = 'blocked';
        this.updatedBy = userId;
        this.notes = `${this.notes || ''}\nBlocked: ${reason}`.trim();
    }
    /**
     * Cancel task
     */
    cancel(userId, reason) {
        this.status = 'cancelled';
        this.updatedBy = userId;
        if (reason) {
            this.notes = `${this.notes || ''}\nCancelled: ${reason}`.trim();
        }
    }
    /**
     * Add approval
     */
    addApproval(approverId, decision, comments) {
        if (!this.execution) {
            this.execution = {};
        }
        if (!this.execution.approvals) {
            this.execution.approvals = [];
        }
        // Remove existing approval from this user
        this.execution.approvals = this.execution.approvals.filter(a => a.approverId !== approverId);
        // Add new approval
        this.execution.approvals.push({
            approverId,
            decision,
            timestamp: new Date().toISOString(),
            comments,
        });
        // Check if all required approvals are received
        this.checkApprovalStatus();
    }
    /**
     * Check approval status and update task accordingly
     */
    checkApprovalStatus() {
        if (!this.requiresApproval || !this.execution?.approvals)
            return;
        const approvalConfig = this.configuration?.approval;
        if (!approvalConfig)
            return;
        const approvals = this.execution.approvals;
        const approvedCount = approvals.filter(a => a.decision === 'approved').length;
        const rejectedCount = approvals.filter(a => a.decision === 'rejected').length;
        const totalApprovers = approvalConfig.approvers.length;
        switch (approvalConfig.approvalType) {
            case 'any':
                if (approvedCount > 0) {
                    this.status = 'pending'; // Ready to proceed
                }
                else if (rejectedCount > 0) {
                    this.status = 'blocked';
                }
                break;
            case 'all':
                if (approvedCount === totalApprovers) {
                    this.status = 'pending'; // Ready to proceed
                }
                else if (rejectedCount > 0) {
                    this.status = 'blocked';
                }
                break;
            case 'majority':
                const majority = Math.ceil(totalApprovers / 2);
                if (approvedCount >= majority) {
                    this.status = 'pending'; // Ready to proceed
                }
                else if (rejectedCount >= majority) {
                    this.status = 'blocked';
                }
                break;
        }
    }
    /**
     * Add automation log
     */
    addAutomationLog(level, message, data) {
        if (!this.execution) {
            this.execution = {};
        }
        if (!this.execution.automationLogs) {
            this.execution.automationLogs = [];
        }
        this.execution.automationLogs.push({
            timestamp: new Date().toISOString(),
            level,
            message,
            data,
        });
    }
    /**
     * Add evidence reference
     */
    addEvidence(evidenceId) {
        if (!this.execution) {
            this.execution = {};
        }
        if (!this.execution.evidenceIds) {
            this.execution.evidenceIds = [];
        }
        if (!this.execution.evidenceIds.includes(evidenceId)) {
            this.execution.evidenceIds.push(evidenceId);
        }
    }
    /**
     * Validate task completion
     */
    validateCompletion() {
        const errors = [];
        // Check if evidence collection is required
        if (this.configuration?.evidenceCollection?.required) {
            if (!this.execution?.evidenceIds || this.execution.evidenceIds.length === 0) {
                errors.push('Evidence collection is required but no evidence was collected');
            }
        }
        // Check validation criteria
        if (this.configuration?.validation?.required && this.configuration.validation.criteria) {
            for (const criterion of this.configuration.validation.criteria) {
                if (!this.evaluateValidationCriterion(criterion)) {
                    errors.push(criterion.message || `Validation failed for ${criterion.field}`);
                }
            }
        }
        // Check approval requirements
        if (this.requiresApproval) {
            const hasRequiredApprovals = this.hasRequiredApprovals();
            if (!hasRequiredApprovals) {
                errors.push('Required approvals not received');
            }
        }
        return {
            isValid: errors.length === 0,
            errors,
        };
    }
    /**
     * Check if task has required approvals
     */
    hasRequiredApprovals() {
        if (!this.requiresApproval)
            return true;
        const approvalConfig = this.configuration?.approval;
        const approvals = this.execution?.approvals || [];
        if (!approvalConfig)
            return false;
        const approvedCount = approvals.filter(a => a.decision === 'approved').length;
        const totalApprovers = approvalConfig.approvers.length;
        switch (approvalConfig.approvalType) {
            case 'any':
                return approvedCount > 0;
            case 'all':
                return approvedCount === totalApprovers;
            case 'majority':
                return approvedCount >= Math.ceil(totalApprovers / 2);
            default:
                return false;
        }
    }
    /**
     * Evaluate validation criterion
     */
    evaluateValidationCriterion(criterion) {
        const value = this.getFieldValue(criterion.field);
        switch (criterion.operator) {
            case 'exists':
                return value !== null && value !== undefined;
            case 'not_empty':
                return value !== null && value !== undefined && value !== '';
            case 'equals':
                return value === criterion.value;
            case 'greater_than':
                return Number(value) > Number(criterion.value);
            case 'less_than':
                return Number(value) < Number(criterion.value);
            default:
                return false;
        }
    }
    /**
     * Get field value for validation
     */
    getFieldValue(field) {
        const parts = field.split('.');
        let value = this;
        for (const part of parts) {
            value = value?.[part];
        }
        return value;
    }
};
exports.IncidentTask = IncidentTask;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], IncidentTask.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], IncidentTask.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], IncidentTask.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['manual', 'automated', 'approval', 'investigation', 'containment', 'communication'],
    }),
    __metadata("design:type", String)
], IncidentTask.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['pending', 'in_progress', 'completed', 'failed', 'cancelled', 'blocked'],
        default: 'pending',
    }),
    __metadata("design:type", String)
], IncidentTask.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['low', 'medium', 'high', 'urgent'],
        default: 'medium',
    }),
    __metadata("design:type", String)
], IncidentTask.prototype, "priority", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'response_phase', nullable: true }),
    __metadata("design:type", String)
], IncidentTask.prototype, "responsePhase", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'task_order', type: 'integer', default: 0 }),
    __metadata("design:type", Number)
], IncidentTask.prototype, "taskOrder", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], IncidentTask.prototype, "configuration", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], IncidentTask.prototype, "execution", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'assigned_to', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], IncidentTask.prototype, "assignedTo", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'assigned_role', nullable: true }),
    __metadata("design:type", String)
], IncidentTask.prototype, "assignedRole", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'due_date', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], IncidentTask.prototype, "dueDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'estimated_duration', type: 'integer', nullable: true }),
    __metadata("design:type", Number)
], IncidentTask.prototype, "estimatedDuration", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], IncidentTask.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, default: '{}' }),
    __metadata("design:type", Array)
], IncidentTask.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', type: 'uuid' }),
    __metadata("design:type", String)
], IncidentTask.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], IncidentTask.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], IncidentTask.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], IncidentTask.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => incident_entity_1.Incident, incident => incident.tasks, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'incident_id' }),
    __metadata("design:type", typeof (_d = typeof incident_entity_1.Incident !== "undefined" && incident_entity_1.Incident) === "function" ? _d : Object)
], IncidentTask.prototype, "incident", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'incident_id', type: 'uuid' }),
    __metadata("design:type", String)
], IncidentTask.prototype, "incidentId", void 0);
exports.IncidentTask = IncidentTask = __decorate([
    (0, typeorm_1.Entity)('incident_tasks'),
    (0, typeorm_1.Index)(['incidentId']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['priority']),
    (0, typeorm_1.Index)(['assignedTo']),
    (0, typeorm_1.Index)(['dueDate'])
], IncidentTask);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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