{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\auth.config.ts", "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,qDAA4F;AAC5F,yDAAoD;AAEpD,yDAAyD;AACzD,MAAM,WAAW,GAAG,CAAC,KAAyB,EAAE,QAAgB,EAAU,EAAE;IAC1E,IAAI,CAAC,KAAK;QAAE,OAAO,QAAQ,CAAC;IAC5B,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IAC7B,OAAO,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC;AAClD,CAAC,CAAC;AAEF,MAAM,YAAY,GAAG,CAAC,KAAyB,EAAE,QAAQ,GAAG,KAAK,EAAW,EAAE,CAC5E,KAAK,EAAE,WAAW,EAAE,KAAK,MAAM,IAAI,QAAQ,CAAC;AAE9C,wDAAwD;AACxD,IAAU,IAAI,CAIb;AAJD,WAAU,IAAI;IACC,YAAO,GAAG,OAAS,CAAC;IACpB,WAAM,GAAG,QAAU,CAAC;IACpB,YAAO,GAAG,CAAC,CAAS,EAAU,EAAE,CAAC,CAAC,GAAG,KAAM,CAAC;AAC3D,CAAC,EAJS,IAAI,KAAJ,IAAI,QAIb;AA0ED,kEAAkE;AAClE,MAAa,oBAAoB;CA2EhC;AA3ED,oDA2EC;AAxEC;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,IAAI,qDAAqD,CAAC;;uDACtE;AAInB;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,IAAI,yDAAyD,CAAC;;8DACnE;AAI1B;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,IAAI,yDAAyD,CAAC;;2DACtE;AAOvB;IALC,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,EAAE,CAAC;IACP,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;;0DAC3B;AAOtB;IALC,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,EAAE,CAAC;IACP,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;;8DACtB;AAM1B;IAJC,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;;4DACzB;AAOxB;IALC,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,GAAG,CAAC;IACR,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;;+DACrB;AAI3B;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;;6DAC3B;AAI1B;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;;iEACvB;AAI9B;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;;2DAC7B;AAIxB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,sBAAI,EAAC,CAAC,MAAM,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;;yDAChB;AAIvB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,sBAAI,EAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;;sDACH;AAI7B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;4DACa;AAIxB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;gEACiB;AAI5B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;+DACgB;AAI3B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;mEACoB;AAGjC,+CAA+C;AAC/C,MAAM,QAAQ,GAAG;IACf,UAAU,EAAE,qDAAqD;IACjE,kBAAkB,EAAE,yDAAyD;IAC7E,UAAU,EAAE,mBAAmB;IAC/B,YAAY,EAAE,gBAAgB;IAC9B,cAAc,EAAE,IAAI;IACpB,sBAAsB,EAAE,IAAI;IAC5B,cAAc,EAAE,yDAAyD;IACzE,YAAY,EAAE,MAAkB;IAChC,UAAU,EAAE,OAAmB;IAC/B,gBAAgB,EAAE,aAAyB;IAC3C,kBAAkB,EAAE,CAAC;IACrB,eAAe,EAAE,EAAE;IACnB,mBAAmB,EAAE,CAAC;IACtB,uBAAuB,EAAE,EAAE;IAC3B,cAAc,EAAE,WAAW;IAC3B,uBAAuB,EAAE,GAAG;IAC5B,aAAa,EAAE,EAAE;CACT,CAAC;AAEX,yDAAyD;AACzD,MAAM,GAAG,GAAG;IACV,GAAG,EAAE,CAAa,GAAW,EAAE,QAAY,EAA0B,EAAE,CACrE,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,QAAQ;IAE9B,SAAS,EAAE,CAAC,GAAW,EAAE,QAAQ,GAAG,EAAE,EAAU,EAAE,CAChD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,QAAQ;IAE9B,SAAS,EAAE,CAAC,GAAW,EAAE,QAAQ,GAAG,CAAC,EAAU,EAAE,CAC/C,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC;IAEzC,UAAU,EAAE,CAAC,GAAW,EAAE,QAAQ,GAAG,KAAK,EAAW,EAAE,CACrD,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC;IAE1C,YAAY,EAAE,GAAY,EAAE,CAC1B,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,YAAY;CAClC,CAAC;AAEX,qCAAqC;AACrC,MAAM,mBAAmB,GAAG,CAAC,QAAgC,EAAiB,EAAE;IAC9E,MAAM,MAAM,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;IACtC,MAAM,MAAM,GAAkB,EAAE,CAAC;IAEjC,MAAM,QAAQ,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,YAAY,CAAC,CAAC;IAChD,MAAM,YAAY,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,gBAAgB,CAAC,CAAC;IACxD,MAAM,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,eAAe,CAAC,CAAC;IAEtD,IAAI,QAAQ;QAAE,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACzC,IAAI,YAAY;QAAE,MAAM,CAAC,YAAY,GAAG,YAAY,CAAC;IACrD,IAAI,WAAW;QAAE,MAAM,CAAC,WAAW,GAAG,WAAW,CAAC;IAElD,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAEF,qDAAqD;AACxC,QAAA,UAAU,GAAG,IAAA,mBAAU,EAAC,MAAM,EAAE,GAAe,EAAE,CAAC,CAAC;IAC9D,GAAG,EAAE;QACH,MAAM,EAAE,GAAG,CAAC,SAAS,CAAC,YAAY,EAAE,QAAQ,CAAC,UAAU,CAAC;QACxD,SAAS,EAAE,GAAG,CAAC,SAAS,CAAC,gBAAgB,EAAE,QAAQ,CAAC,cAAc,CAAC;QACnE,aAAa,EAAE,GAAG,CAAC,SAAS,CAAC,oBAAoB,EAAE,QAAQ,CAAC,kBAAkB,CAAC;QAC/E,gBAAgB,EAAE,GAAG,CAAC,SAAS,CAAC,wBAAwB,EAAE,QAAQ,CAAC,sBAAsB,CAAC;QAC1F,MAAM,EAAE,GAAG,CAAC,SAAS,CAAC,YAAY,EAAE,QAAQ,CAAC,UAAU,CAAC;QACxD,QAAQ,EAAE,GAAG,CAAC,SAAS,CAAC,cAAc,EAAE,QAAQ,CAAC,YAAY,CAAC;KAC/D;IAED,MAAM,EAAE;QACN,MAAM,EAAE,GAAG,CAAC,SAAS,CAAC,eAAe,EAAE,QAAQ,CAAC,aAAa,CAAC;KAC/D;IAED,OAAO,EAAE;QACP,MAAM,EAAE,GAAG,CAAC,SAAS,CAAC,gBAAgB,EAAE,QAAQ,CAAC,cAAc,CAAC;QAChE,MAAM,EAAE,KAAK;QACb,iBAAiB,EAAE,KAAK;QACxB,MAAM,EAAE;YACN,MAAM,EAAE,GAAG,CAAC,YAAY,EAAE;YAC1B,QAAQ,EAAE,IAAI;YACd,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,QAAQ,EAAG,GAAG,CAAC,GAAG,CAAC,kBAAkB,CAAuB,IAAI,QAAQ;SACzE;KACF;IAED,KAAK,EAAE;QACL,MAAM,EAAE,mBAAmB,CAAC,QAAQ,CAAC;QACrC,SAAS,EAAE,mBAAmB,CAAC,WAAW,CAAC;KAC5C;IAED,IAAI,EAAE;QACJ,WAAW,EAAG,GAAG,CAAC,GAAG,CAAC,mBAAmB,CAAc,IAAI,QAAQ,CAAC,YAAY;QAChF,SAAS,EAAG,GAAG,CAAC,GAAG,CAAC,YAAY,CAAc,IAAI,QAAQ,CAAC,UAAU;QACrE,cAAc,EAAG,GAAG,CAAC,GAAG,CAAC,kBAAkB,CAAc,IAAI,QAAQ,CAAC,gBAAgB;QACtF,mBAAmB,EAAE,GAAG,CAAC,UAAU,CAAC,uBAAuB,CAAC;KAC7D;IAED,QAAQ,EAAE;QACR,gBAAgB,EAAE,GAAG,CAAC,SAAS,CAAC,oBAAoB,EAAE,QAAQ,CAAC,kBAAkB,CAAC;QAClF,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,iBAAiB,EAAE,QAAQ,CAAC,eAAe,CAAC,CAAC;QACzF,iBAAiB,EAAE,GAAG,CAAC,SAAS,CAAC,qBAAqB,EAAE,QAAQ,CAAC,mBAAmB,CAAC;QACrF,wBAAwB,EAAE,GAAG,CAAC,UAAU,CAAC,4BAA4B,CAAC;QACtE,wBAAwB,EAAE,GAAG,CAAC,UAAU,CAAC,4BAA4B,CAAC;QACtE,sBAAsB,EAAE,GAAG,CAAC,UAAU,CAAC,0BAA0B,CAAC;QAClE,sBAAsB,EAAE,GAAG,CAAC,UAAU,CAAC,0BAA0B,CAAC;QAClE,eAAe,EAAE,GAAG,CAAC,UAAU,CAAC,mBAAmB,CAAC;QACpD,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,yBAAyB,EAAE,QAAQ,CAAC,uBAAuB,CAAC,CAAC;KACzG;IAED,MAAM,EAAE;QACN,UAAU,EAAE,GAAG,CAAC,SAAS,CAAC,gBAAgB,EAAE,QAAQ,CAAC,cAAc,CAAC;QACpE,OAAO,EAAE,GAAG,CAAC,UAAU,CAAC,iBAAiB,CAAC;QAC1C,iBAAiB,EAAE,GAAG,CAAC,SAAS,CAAC,yBAAyB,EAAE,QAAQ,CAAC,uBAAuB,CAAC,GAAG,IAAI,CAAC,MAAM;KAC5G;CACF,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\auth.config.ts"], "sourcesContent": ["import { registerAs } from '@nestjs/config';\r\nimport { IsBoolean, IsN<PERSON>ber, IsString, IsO<PERSON>al, IsIn, Min, Max } from 'class-validator';\r\nimport { Transform, Type } from 'class-transformer';\r\n\r\n// Consolidated utility functions with better type safety\r\nconst parseNumber = (value: string | undefined, fallback: number): number => {\r\n  if (!value) return fallback;\r\n  const parsed = Number(value);\r\n  return Number.isNaN(parsed) ? fallback : parsed;\r\n};\r\n\r\nconst parseBoolean = (value: string | undefined, fallback = false): boolean => \r\n  value?.toLowerCase() === 'true' || fallback;\r\n\r\n// Time utilities as a namespace for better organization\r\nnamespace Time {\r\n  export const HOUR_MS = 3_600_000;\r\n  export const DAY_MS = 86_400_000;\r\n  export const minutes = (n: number): number => n * 60_000;\r\n}\r\n\r\n// Stricter type definitions\r\nexport type SameSiteAttribute = 'strict' | 'lax' | 'none';\r\nexport type UserRole = 'user' | 'admin' | 'super_admin';\r\nexport type NodeEnv = 'development' | 'production' | 'test';\r\n\r\n// Configuration interface\r\nexport interface OAuthProvider {\r\n  clientId?: string;\r\n  clientSecret?: string;\r\n  callbackUrl?: string;\r\n}\r\n\r\n// Configuration interfaces with readonly modifiers\r\nexport interface JwtConfig {\r\n  readonly secret: string;\r\n  readonly expiresIn: string;\r\n  readonly refreshSecret: string;\r\n  readonly refreshExpiresIn: string;\r\n  readonly issuer: string;\r\n  readonly audience: string;\r\n}\r\n\r\nexport interface SessionConfig {\r\n  readonly secret: string;\r\n  readonly resave: boolean;\r\n  readonly saveUninitialized: boolean;\r\n  readonly cookie: {\r\n    readonly secure: boolean;\r\n    readonly httpOnly: boolean;\r\n    readonly maxAge: number;\r\n    readonly sameSite: SameSiteAttribute;\r\n  };\r\n}\r\n\r\nexport interface SecurityConfig {\r\n  readonly maxLoginAttempts: number;\r\n  readonly lockoutDuration: number;\r\n  readonly passwordMinLength: number;\r\n  readonly passwordRequireUppercase: boolean;\r\n  readonly passwordRequireLowercase: boolean;\r\n  readonly passwordRequireNumbers: boolean;\r\n  readonly passwordRequireSymbols: boolean;\r\n  readonly enableTwoFactor: boolean;\r\n  readonly sessionTimeout: number;\r\n}\r\n\r\nexport interface RbacConfig {\r\n  readonly defaultRole: UserRole;\r\n  readonly adminRole: UserRole;\r\n  readonly superAdminRole: UserRole;\r\n  readonly enableRoleHierarchy: boolean;\r\n}\r\n\r\nexport interface ApiKeyConfig {\r\n  readonly headerName: string;\r\n  readonly enabled: boolean;\r\n  readonly defaultExpiration: number;\r\n}\r\n\r\nexport interface AuthConfig {\r\n  readonly jwt: JwtConfig;\r\n  readonly bcrypt: { readonly rounds: number };\r\n  readonly session: SessionConfig;\r\n  readonly oauth: {\r\n    readonly google: OAuthProvider;\r\n    readonly microsoft: OAuthProvider;\r\n  };\r\n  readonly rbac: RbacConfig;\r\n  readonly security: SecurityConfig;\r\n  readonly apiKey: ApiKeyConfig;\r\n}\r\n\r\n// Validation class with comprehensive rules and better decorators\r\nexport class AuthConfigValidation {\r\n  @IsString()\r\n  @Transform(({ value }) => value || 'your-super-secret-jwt-key-change-this-in-production')\r\n  jwtSecret!: string;\r\n\r\n  @IsString()\r\n  @Transform(({ value }) => value || 'your-super-secret-refresh-key-change-this-in-production')\r\n  jwtRefreshSecret!: string;\r\n\r\n  @IsString()\r\n  @Transform(({ value }) => value || 'your-super-secret-session-key-change-this-in-production')\r\n  sessionSecret!: string;\r\n\r\n  @IsNumber()\r\n  @Min(4)\r\n  @Max(15)\r\n  @Type(() => Number)\r\n  @Transform(({ value }) => parseNumber(value, 12))\r\n  bcryptRounds!: number;\r\n\r\n  @IsNumber()\r\n  @Min(1)\r\n  @Max(20)\r\n  @Type(() => Number)\r\n  @Transform(({ value }) => parseNumber(value, 5))\r\n  maxLoginAttempts!: number;\r\n\r\n  @IsNumber()\r\n  @Min(1)\r\n  @Type(() => Number)\r\n  @Transform(({ value }) => parseNumber(value, 15))\r\n  lockoutMinutes!: number;\r\n\r\n  @IsNumber()\r\n  @Min(6)\r\n  @Max(128)\r\n  @Type(() => Number)\r\n  @Transform(({ value }) => parseNumber(value, 8))\r\n  passwordMinLength!: number;\r\n\r\n  @IsBoolean()\r\n  @Transform(({ value }) => parseBoolean(value, false))\r\n  enableTwoFactor!: boolean;\r\n\r\n  @IsBoolean()\r\n  @Transform(({ value }) => parseBoolean(value, false))\r\n  enableRoleHierarchy!: boolean;\r\n\r\n  @IsBoolean()\r\n  @Transform(({ value }) => parseBoolean(value, false))\r\n  apiKeyEnabled!: boolean;\r\n\r\n  @IsOptional()\r\n  @IsIn(['user', 'admin', 'super_admin'])\r\n  defaultRole?: UserRole;\r\n\r\n  @IsOptional()\r\n  @IsIn(['strict', 'lax', 'none'])\r\n  sameSite?: SameSiteAttribute;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  googleClientId?: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  googleClientSecret?: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  microsoftClientId?: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  microsoftClientSecret?: string;\r\n}\r\n\r\n// Configuration defaults with const assertions\r\nconst DEFAULTS = {\r\n  JWT_SECRET: 'your-super-secret-jwt-key-change-this-in-production',\r\n  JWT_REFRESH_SECRET: 'your-super-secret-refresh-key-change-this-in-production',\r\n  JWT_ISSUER: 'sentinel-platform',\r\n  JWT_AUDIENCE: 'sentinel-users',\r\n  JWT_EXPIRES_IN: '1h',\r\n  JWT_REFRESH_EXPIRES_IN: '7d',\r\n  SESSION_SECRET: 'your-super-secret-session-key-change-this-in-production',\r\n  DEFAULT_ROLE: 'user' as UserRole,\r\n  ADMIN_ROLE: 'admin' as UserRole,\r\n  SUPER_ADMIN_ROLE: 'super_admin' as UserRole,\r\n  MAX_LOGIN_ATTEMPTS: 5,\r\n  LOCKOUT_MINUTES: 15,\r\n  PASSWORD_MIN_LENGTH: 8,\r\n  SESSION_TIMEOUT_MINUTES: 30,\r\n  API_KEY_HEADER: 'X-API-Key',\r\n  API_KEY_EXPIRATION_DAYS: 365,\r\n  BCRYPT_ROUNDS: 12,\r\n} as const;\r\n\r\n// Optimized environment variable access with type safety\r\nconst env = {\r\n  get: <T = string>(key: string, fallback?: T): T | string | undefined => \r\n    process.env[key] ?? fallback,\r\n  \r\n  getString: (key: string, fallback = ''): string => \r\n    process.env[key] ?? fallback,\r\n  \r\n  getNumber: (key: string, fallback = 0): number => \r\n    parseNumber(process.env[key], fallback),\r\n  \r\n  getBoolean: (key: string, fallback = false): boolean => \r\n    parseBoolean(process.env[key], fallback),\r\n  \r\n  isProduction: (): boolean => \r\n    process.env['NODE_ENV'] === 'production',\r\n} as const;\r\n\r\n// Streamlined OAuth provider builder\r\nconst createOAuthProvider = (provider: 'google' | 'microsoft'): OAuthProvider => {\r\n  const prefix = provider.toUpperCase();\r\n  const config: OAuthProvider = {};\r\n  \r\n  const clientId = env.get(`${prefix}_CLIENT_ID`);\r\n  const clientSecret = env.get(`${prefix}_CLIENT_SECRET`);\r\n  const callbackUrl = env.get(`${prefix}_CALLBACK_URL`);\r\n  \r\n  if (clientId) config.clientId = clientId;\r\n  if (clientSecret) config.clientSecret = clientSecret;\r\n  if (callbackUrl) config.callbackUrl = callbackUrl;\r\n  \r\n  return config;\r\n};\r\n\r\n// Main configuration factory with enhanced structure\r\nexport const authConfig = registerAs('auth', (): AuthConfig => ({\r\n  jwt: {\r\n    secret: env.getString('JWT_SECRET', DEFAULTS.JWT_SECRET),\r\n    expiresIn: env.getString('JWT_EXPIRES_IN', DEFAULTS.JWT_EXPIRES_IN),\r\n    refreshSecret: env.getString('JWT_REFRESH_SECRET', DEFAULTS.JWT_REFRESH_SECRET),\r\n    refreshExpiresIn: env.getString('JWT_REFRESH_EXPIRES_IN', DEFAULTS.JWT_REFRESH_EXPIRES_IN),\r\n    issuer: env.getString('JWT_ISSUER', DEFAULTS.JWT_ISSUER),\r\n    audience: env.getString('JWT_AUDIENCE', DEFAULTS.JWT_AUDIENCE),\r\n  },\r\n\r\n  bcrypt: {\r\n    rounds: env.getNumber('BCRYPT_ROUNDS', DEFAULTS.BCRYPT_ROUNDS),\r\n  },\r\n\r\n  session: {\r\n    secret: env.getString('SESSION_SECRET', DEFAULTS.SESSION_SECRET),\r\n    resave: false,\r\n    saveUninitialized: false,\r\n    cookie: {\r\n      secure: env.isProduction(),\r\n      httpOnly: true,\r\n      maxAge: Time.DAY_MS,\r\n      sameSite: (env.get('COOKIE_SAME_SITE') as SameSiteAttribute) ?? 'strict',\r\n    },\r\n  },\r\n\r\n  oauth: {\r\n    google: createOAuthProvider('google'),\r\n    microsoft: createOAuthProvider('microsoft'),\r\n  },\r\n\r\n  rbac: {\r\n    defaultRole: (env.get('DEFAULT_USER_ROLE') as UserRole) ?? DEFAULTS.DEFAULT_ROLE,\r\n    adminRole: (env.get('ADMIN_ROLE') as UserRole) ?? DEFAULTS.ADMIN_ROLE,\r\n    superAdminRole: (env.get('SUPER_ADMIN_ROLE') as UserRole) ?? DEFAULTS.SUPER_ADMIN_ROLE,\r\n    enableRoleHierarchy: env.getBoolean('ENABLE_ROLE_HIERARCHY'),\r\n  },\r\n\r\n  security: {\r\n    maxLoginAttempts: env.getNumber('MAX_LOGIN_ATTEMPTS', DEFAULTS.MAX_LOGIN_ATTEMPTS),\r\n    lockoutDuration: Time.minutes(env.getNumber('LOCKOUT_MINUTES', DEFAULTS.LOCKOUT_MINUTES)),\r\n    passwordMinLength: env.getNumber('PASSWORD_MIN_LENGTH', DEFAULTS.PASSWORD_MIN_LENGTH),\r\n    passwordRequireUppercase: env.getBoolean('PASSWORD_REQUIRE_UPPERCASE'),\r\n    passwordRequireLowercase: env.getBoolean('PASSWORD_REQUIRE_LOWERCASE'),\r\n    passwordRequireNumbers: env.getBoolean('PASSWORD_REQUIRE_NUMBERS'),\r\n    passwordRequireSymbols: env.getBoolean('PASSWORD_REQUIRE_SYMBOLS'),\r\n    enableTwoFactor: env.getBoolean('ENABLE_TWO_FACTOR'),\r\n    sessionTimeout: Time.minutes(env.getNumber('SESSION_TIMEOUT_MINUTES', DEFAULTS.SESSION_TIMEOUT_MINUTES)),\r\n  },\r\n\r\n  apiKey: {\r\n    headerName: env.getString('API_KEY_HEADER', DEFAULTS.API_KEY_HEADER),\r\n    enabled: env.getBoolean('API_KEY_ENABLED'),\r\n    defaultExpiration: env.getNumber('API_KEY_EXPIRATION_DAYS', DEFAULTS.API_KEY_EXPIRATION_DAYS) * Time.DAY_MS,\r\n  },\r\n}));"], "version": 3}