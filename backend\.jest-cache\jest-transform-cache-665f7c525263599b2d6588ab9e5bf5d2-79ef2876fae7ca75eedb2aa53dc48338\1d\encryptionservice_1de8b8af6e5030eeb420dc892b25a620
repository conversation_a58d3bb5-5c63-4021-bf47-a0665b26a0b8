1fddd0ebd768e7d642eb17fc8f53f4e4
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EncryptionService = exports.HashAlgorithm = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const aes_encryption_1 = require("./aes.encryption");
const rsa_encryption_1 = require("./rsa.encryption");
const hash_service_1 = require("./hash.service");
const crypto = __importStar(require("crypto"));
var HashAlgorithm;
(function (HashAlgorithm) {
    HashAlgorithm["SHA256"] = "sha256";
    HashAlgorithm["SHA512"] = "sha512";
    HashAlgorithm["SHA1"] = "sha1";
    HashAlgorithm["MD5"] = "md5";
})(HashAlgorithm || (exports.HashAlgorithm = HashAlgorithm = {}));
let EncryptionService = class EncryptionService {
    constructor(configService, aesEncryption, rsaEncryption, hashService) {
        this.configService = configService;
        this.aesEncryption = aesEncryption;
        this.rsaEncryption = rsaEncryption;
        this.hashService = hashService;
    }
    // AES Encryption Methods
    /**
     * Encrypt data using AES-256-GCM
     */
    async encryptAES(data, key) {
        const result = await this.aesEncryption.encrypt(data, key);
        return {
            encryptedData: result.encryptedData,
            iv: result.iv,
            tag: result.tag,
            algorithm: result.algorithm,
        };
    }
    /**
     * Decrypt data using AES-256-GCM
     */
    async decryptAES(encryptedData, iv, tag, key) {
        const decryptedData = await this.aesEncryption.decrypt({
            encryptedData,
            iv,
            tag,
            key,
        });
        return {
            decryptedData,
            algorithm: 'aes-256-gcm',
        };
    }
    /**
     * Generate AES encryption key
     */
    generateAESKey() {
        return this.aesEncryption.generateKey();
    }
    // RSA Encryption Methods
    /**
     * Generate RSA key pair
     */
    async generateRSAKeyPair(keySize) {
        return this.rsaEncryption.generateKeyPair(keySize);
    }
    /**
     * Encrypt data using RSA
     */
    async encryptRSA(data, publicKey, options) {
        return this.rsaEncryption.encrypt(data, publicKey, options);
    }
    /**
     * Decrypt data using RSA
     */
    async decryptRSA(encryptedData, privateKey, options) {
        return this.rsaEncryption.decrypt(encryptedData, privateKey, options);
    }
    /**
     * Sign data using RSA
     */
    async signRSA(data, privateKey, algorithm) {
        return this.rsaEncryption.sign(data, privateKey, algorithm);
    }
    /**
     * Verify RSA signature
     */
    async verifyRSA(data, signature, publicKey, algorithm) {
        return this.rsaEncryption.verify(data, signature, publicKey, algorithm);
    }
    /**
     * Encrypt large data using RSA (chunked)
     */
    async encryptRSALarge(data, publicKey) {
        return this.rsaEncryption.encryptLargeData(data, publicKey);
    }
    /**
     * Decrypt large data using RSA (chunked)
     */
    async decryptRSALarge(encryptedChunks, privateKey) {
        return this.rsaEncryption.decryptLargeData(encryptedChunks, privateKey);
    }
    // Hash Methods
    /**
     * Hash data using specified algorithm
     */
    hash(data, algorithm = HashAlgorithm.SHA256) {
        return this.hashService.hash(data, { algorithm });
    }
    /**
     * Hash data using SHA-256
     */
    hashSHA256(data) {
        return this.hashService.sha256(data);
    }
    /**
     * Hash data using SHA-512
     */
    hashSHA512(data) {
        return this.hashService.sha512(data);
    }
    /**
     * Create HMAC
     */
    hmac(data, key, algorithm = HashAlgorithm.SHA256) {
        return this.hashService.hmac(data, key, { algorithm });
    }
    /**
     * Hash password using bcrypt
     */
    async hashPassword(password, options) {
        return this.hashService.hashPassword(password, options);
    }
    /**
     * Verify password against hash
     */
    async verifyPassword(password, hash) {
        return this.hashService.verifyPassword(password, hash);
    }
    /**
     * Generate PBKDF2 hash
     */
    async pbkdf2(password, salt, iterations, keyLength) {
        return this.hashService.pbkdf2(password, salt, iterations, keyLength);
    }
    // Utility Methods
    /**
     * Generate a secure random string
     */
    generateSecureRandom(length = 32) {
        return crypto.randomBytes(length).toString('hex');
    }
    /**
     * Generate cryptographically secure salt
     */
    generateSalt(length = 32) {
        return this.hashService.generateRandomSalt(length);
    }
    /**
     * Validate encryption keys
     */
    validateAESKey(key) {
        return this.aesEncryption.validateKey(key);
    }
    validateRSAPrivateKey(privateKey) {
        return this.rsaEncryption.validatePrivateKey(privateKey);
    }
    validateRSAPublicKey(publicKey) {
        return this.rsaEncryption.validatePublicKey(publicKey);
    }
    /**
     * Generate checksum for data integrity
     */
    generateChecksum(data, algorithm = HashAlgorithm.SHA256) {
        return this.hashService.generateChecksum(data, algorithm);
    }
    /**
     * Verify data integrity using checksum
     */
    verifyChecksum(data, expectedChecksum, algorithm = HashAlgorithm.SHA256) {
        return this.hashService.verifyChecksum(data, expectedChecksum, algorithm);
    }
};
exports.EncryptionService = EncryptionService;
exports.EncryptionService = EncryptionService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object, typeof (_b = typeof aes_encryption_1.AESEncryption !== "undefined" && aes_encryption_1.AESEncryption) === "function" ? _b : Object, typeof (_c = typeof rsa_encryption_1.RSAEncryption !== "undefined" && rsa_encryption_1.RSAEncryption) === "function" ? _c : Object, typeof (_d = typeof hash_service_1.HashService !== "undefined" && hash_service_1.HashService) === "function" ? _d : Object])
], EncryptionService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxpbmZyYXN0cnVjdHVyZVxcc2VjdXJpdHlcXGVuY3J5cHRpb25cXGVuY3J5cHRpb24uc2VydmljZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsMkNBQTRDO0FBQzVDLDJDQUErQztBQUMvQyxxREFBNEY7QUFDNUYscURBQW1GO0FBQ25GLGlEQUE0RjtBQUM1RiwrQ0FBaUM7QUFjakMsSUFBWSxhQUtYO0FBTEQsV0FBWSxhQUFhO0lBQ3ZCLGtDQUFpQixDQUFBO0lBQ2pCLGtDQUFpQixDQUFBO0lBQ2pCLDhCQUFhLENBQUE7SUFDYiw0QkFBVyxDQUFBO0FBQ2IsQ0FBQyxFQUxXLGFBQWEsNkJBQWIsYUFBYSxRQUt4QjtBQUdNLElBQU0saUJBQWlCLEdBQXZCLE1BQU0saUJBQWlCO0lBQzVCLFlBQ21CLGFBQTRCLEVBQzVCLGFBQTRCLEVBQzVCLGFBQTRCLEVBQzVCLFdBQXdCO1FBSHhCLGtCQUFhLEdBQWIsYUFBYSxDQUFlO1FBQzVCLGtCQUFhLEdBQWIsYUFBYSxDQUFlO1FBQzVCLGtCQUFhLEdBQWIsYUFBYSxDQUFlO1FBQzVCLGdCQUFXLEdBQVgsV0FBVyxDQUFhO0lBQ3hDLENBQUM7SUFFSix5QkFBeUI7SUFDekI7O09BRUc7SUFDSCxLQUFLLENBQUMsVUFBVSxDQUFDLElBQVksRUFBRSxHQUFZO1FBQ3pDLE1BQU0sTUFBTSxHQUFHLE1BQU0sSUFBSSxDQUFDLGFBQWEsQ0FBQyxPQUFPLENBQUMsSUFBSSxFQUFFLEdBQUcsQ0FBQyxDQUFDO1FBQzNELE9BQU87WUFDTCxhQUFhLEVBQUUsTUFBTSxDQUFDLGFBQWE7WUFDbkMsRUFBRSxFQUFFLE1BQU0sQ0FBQyxFQUFFO1lBQ2IsR0FBRyxFQUFFLE1BQU0sQ0FBQyxHQUFHO1lBQ2YsU0FBUyxFQUFFLE1BQU0sQ0FBQyxTQUFTO1NBQzVCLENBQUM7SUFDSixDQUFDO0lBRUQ7O09BRUc7SUFDSCxLQUFLLENBQUMsVUFBVSxDQUFDLGFBQXFCLEVBQUUsRUFBVSxFQUFFLEdBQVcsRUFBRSxHQUFZO1FBQzNFLE1BQU0sYUFBYSxHQUFHLE1BQU0sSUFBSSxDQUFDLGFBQWEsQ0FBQyxPQUFPLENBQUM7WUFDckQsYUFBYTtZQUNiLEVBQUU7WUFDRixHQUFHO1lBQ0gsR0FBRztTQUNKLENBQUMsQ0FBQztRQUVILE9BQU87WUFDTCxhQUFhO1lBQ2IsU0FBUyxFQUFFLGFBQWE7U0FDekIsQ0FBQztJQUNKLENBQUM7SUFFRDs7T0FFRztJQUNILGNBQWM7UUFDWixPQUFPLElBQUksQ0FBQyxhQUFhLENBQUMsV0FBVyxFQUFFLENBQUM7SUFDMUMsQ0FBQztJQUVELHlCQUF5QjtJQUN6Qjs7T0FFRztJQUNILEtBQUssQ0FBQyxrQkFBa0IsQ0FBQyxPQUFnQjtRQUN2QyxPQUFPLElBQUksQ0FBQyxhQUFhLENBQUMsZUFBZSxDQUFDLE9BQU8sQ0FBQyxDQUFDO0lBQ3JELENBQUM7SUFFRDs7T0FFRztJQUNILEtBQUssQ0FBQyxVQUFVLENBQUMsSUFBWSxFQUFFLFNBQWlCLEVBQUUsT0FBOEI7UUFDOUUsT0FBTyxJQUFJLENBQUMsYUFBYSxDQUFDLE9BQU8sQ0FBQyxJQUFJLEVBQUUsU0FBUyxFQUFFLE9BQU8sQ0FBQyxDQUFDO0lBQzlELENBQUM7SUFFRDs7T0FFRztJQUNILEtBQUssQ0FBQyxVQUFVLENBQUMsYUFBcUIsRUFBRSxVQUFrQixFQUFFLE9BQThCO1FBQ3hGLE9BQU8sSUFBSSxDQUFDLGFBQWEsQ0FBQyxPQUFPLENBQUMsYUFBYSxFQUFFLFVBQVUsRUFBRSxPQUFPLENBQUMsQ0FBQztJQUN4RSxDQUFDO0lBRUQ7O09BRUc7SUFDSCxLQUFLLENBQUMsT0FBTyxDQUFDLElBQVksRUFBRSxVQUFrQixFQUFFLFNBQWtCO1FBQ2hFLE9BQU8sSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsSUFBSSxFQUFFLFVBQVUsRUFBRSxTQUFTLENBQUMsQ0FBQztJQUM5RCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxLQUFLLENBQUMsU0FBUyxDQUFDLElBQVksRUFBRSxTQUFpQixFQUFFLFNBQWlCLEVBQUUsU0FBa0I7UUFDcEYsT0FBTyxJQUFJLENBQUMsYUFBYSxDQUFDLE1BQU0sQ0FBQyxJQUFJLEVBQUUsU0FBUyxFQUFFLFNBQVMsRUFBRSxTQUFTLENBQUMsQ0FBQztJQUMxRSxDQUFDO0lBRUQ7O09BRUc7SUFDSCxLQUFLLENBQUMsZUFBZSxDQUFDLElBQVksRUFBRSxTQUFpQjtRQUNuRCxPQUFPLElBQUksQ0FBQyxhQUFhLENBQUMsZ0JBQWdCLENBQUMsSUFBSSxFQUFFLFNBQVMsQ0FBQyxDQUFDO0lBQzlELENBQUM7SUFFRDs7T0FFRztJQUNILEtBQUssQ0FBQyxlQUFlLENBQUMsZUFBeUIsRUFBRSxVQUFrQjtRQUNqRSxPQUFPLElBQUksQ0FBQyxhQUFhLENBQUMsZ0JBQWdCLENBQUMsZUFBZSxFQUFFLFVBQVUsQ0FBQyxDQUFDO0lBQzFFLENBQUM7SUFFRCxlQUFlO0lBQ2Y7O09BRUc7SUFDSCxJQUFJLENBQUMsSUFBWSxFQUFFLFlBQTJCLGFBQWEsQ0FBQyxNQUFNO1FBQ2hFLE9BQU8sSUFBSSxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsSUFBSSxFQUFFLEVBQUUsU0FBUyxFQUFFLENBQUMsQ0FBQztJQUNwRCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxVQUFVLENBQUMsSUFBWTtRQUNyQixPQUFPLElBQUksQ0FBQyxXQUFXLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFDO0lBQ3ZDLENBQUM7SUFFRDs7T0FFRztJQUNILFVBQVUsQ0FBQyxJQUFZO1FBQ3JCLE9BQU8sSUFBSSxDQUFDLFdBQVcsQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLENBQUM7SUFDdkMsQ0FBQztJQUVEOztPQUVHO0lBQ0gsSUFBSSxDQUFDLElBQVksRUFBRSxHQUFXLEVBQUUsWUFBMkIsYUFBYSxDQUFDLE1BQU07UUFDN0UsT0FBTyxJQUFJLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxJQUFJLEVBQUUsR0FBRyxFQUFFLEVBQUUsU0FBUyxFQUFFLENBQUMsQ0FBQztJQUN6RCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxLQUFLLENBQUMsWUFBWSxDQUFDLFFBQWdCLEVBQUUsT0FBNkI7UUFDaEUsT0FBTyxJQUFJLENBQUMsV0FBVyxDQUFDLFlBQVksQ0FBQyxRQUFRLEVBQUUsT0FBTyxDQUFDLENBQUM7SUFDMUQsQ0FBQztJQUVEOztPQUVHO0lBQ0gsS0FBSyxDQUFDLGNBQWMsQ0FBQyxRQUFnQixFQUFFLElBQVk7UUFDakQsT0FBTyxJQUFJLENBQUMsV0FBVyxDQUFDLGNBQWMsQ0FBQyxRQUFRLEVBQUUsSUFBSSxDQUFDLENBQUM7SUFDekQsQ0FBQztJQUVEOztPQUVHO0lBQ0gsS0FBSyxDQUFDLE1BQU0sQ0FBQyxRQUFnQixFQUFFLElBQVksRUFBRSxVQUFtQixFQUFFLFNBQWtCO1FBQ2xGLE9BQU8sSUFBSSxDQUFDLFdBQVcsQ0FBQyxNQUFNLENBQUMsUUFBUSxFQUFFLElBQUksRUFBRSxVQUFVLEVBQUUsU0FBUyxDQUFDLENBQUM7SUFDeEUsQ0FBQztJQUVELGtCQUFrQjtJQUNsQjs7T0FFRztJQUNILG9CQUFvQixDQUFDLFNBQWlCLEVBQUU7UUFDdEMsT0FBTyxNQUFNLENBQUMsV0FBVyxDQUFDLE1BQU0sQ0FBQyxDQUFDLFFBQVEsQ0FBQyxLQUFLLENBQUMsQ0FBQztJQUNwRCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxZQUFZLENBQUMsU0FBaUIsRUFBRTtRQUM5QixPQUFPLElBQUksQ0FBQyxXQUFXLENBQUMsa0JBQWtCLENBQUMsTUFBTSxDQUFDLENBQUM7SUFDckQsQ0FBQztJQUVEOztPQUVHO0lBQ0gsY0FBYyxDQUFDLEdBQVc7UUFDeEIsT0FBTyxJQUFJLENBQUMsYUFBYSxDQUFDLFdBQVcsQ0FBQyxHQUFHLENBQUMsQ0FBQztJQUM3QyxDQUFDO0lBRUQscUJBQXFCLENBQUMsVUFBa0I7UUFDdEMsT0FBTyxJQUFJLENBQUMsYUFBYSxDQUFDLGtCQUFrQixDQUFDLFVBQVUsQ0FBQyxDQUFDO0lBQzNELENBQUM7SUFFRCxvQkFBb0IsQ0FBQyxTQUFpQjtRQUNwQyxPQUFPLElBQUksQ0FBQyxhQUFhLENBQUMsaUJBQWlCLENBQUMsU0FBUyxDQUFDLENBQUM7SUFDekQsQ0FBQztJQUVEOztPQUVHO0lBQ0gsZ0JBQWdCLENBQUMsSUFBWSxFQUFFLFlBQTJCLGFBQWEsQ0FBQyxNQUFNO1FBQzVFLE9BQU8sSUFBSSxDQUFDLFdBQVcsQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLEVBQUUsU0FBUyxDQUFDLENBQUM7SUFDNUQsQ0FBQztJQUVEOztPQUVHO0lBQ0gsY0FBYyxDQUFDLElBQVksRUFBRSxnQkFBd0IsRUFBRSxZQUEyQixhQUFhLENBQUMsTUFBTTtRQUNwRyxPQUFPLElBQUksQ0FBQyxXQUFXLENBQUMsY0FBYyxDQUFDLElBQUksRUFBRSxnQkFBZ0IsRUFBRSxTQUFTLENBQUMsQ0FBQztJQUM1RSxDQUFDO0NBQ0YsQ0FBQTtBQTdMWSw4Q0FBaUI7NEJBQWpCLGlCQUFpQjtJQUQ3QixJQUFBLG1CQUFVLEdBQUU7eURBR3VCLHNCQUFhLG9CQUFiLHNCQUFhLG9EQUNiLDhCQUFhLG9CQUFiLDhCQUFhLG9EQUNiLDhCQUFhLG9CQUFiLDhCQUFhLG9EQUNmLDBCQUFXLG9CQUFYLDBCQUFXO0dBTGhDLGlCQUFpQixDQTZMN0IiLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxpbmZyYXN0cnVjdHVyZVxcc2VjdXJpdHlcXGVuY3J5cHRpb25cXGVuY3J5cHRpb24uc2VydmljZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBJbmplY3RhYmxlIH0gZnJvbSAnQG5lc3Rqcy9jb21tb24nO1xyXG5pbXBvcnQgeyBDb25maWdTZXJ2aWNlIH0gZnJvbSAnQG5lc3Rqcy9jb25maWcnO1xyXG5pbXBvcnQgeyBBRVNFbmNyeXB0aW9uLCBBRVNFbmNyeXB0aW9uUmVzdWx0LCBBRVNEZWNyeXB0aW9uT3B0aW9ucyB9IGZyb20gJy4vYWVzLmVuY3J5cHRpb24nO1xyXG5pbXBvcnQgeyBSU0FFbmNyeXB0aW9uLCBSU0FLZXlQYWlyLCBSU0FFbmNyeXB0aW9uT3B0aW9ucyB9IGZyb20gJy4vcnNhLmVuY3J5cHRpb24nO1xyXG5pbXBvcnQgeyBIYXNoU2VydmljZSwgSGFzaE9wdGlvbnMsIFBhc3N3b3JkSGFzaE9wdGlvbnMsIEhNQUNPcHRpb25zIH0gZnJvbSAnLi9oYXNoLnNlcnZpY2UnO1xyXG5pbXBvcnQgKiBhcyBjcnlwdG8gZnJvbSAnY3J5cHRvJztcclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgRW5jcnlwdGlvblJlc3VsdCB7XHJcbiAgZW5jcnlwdGVkRGF0YTogc3RyaW5nO1xyXG4gIGl2Pzogc3RyaW5nO1xyXG4gIHRhZz86IHN0cmluZztcclxuICBhbGdvcml0aG06IHN0cmluZztcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBEZWNyeXB0aW9uUmVzdWx0IHtcclxuICBkZWNyeXB0ZWREYXRhOiBzdHJpbmc7XHJcbiAgYWxnb3JpdGhtOiBzdHJpbmc7XHJcbn1cclxuXHJcbmV4cG9ydCBlbnVtIEhhc2hBbGdvcml0aG0ge1xyXG4gIFNIQTI1NiA9ICdzaGEyNTYnLFxyXG4gIFNIQTUxMiA9ICdzaGE1MTInLFxyXG4gIFNIQTEgPSAnc2hhMScsXHJcbiAgTUQ1ID0gJ21kNScsXHJcbn1cclxuXHJcbkBJbmplY3RhYmxlKClcclxuZXhwb3J0IGNsYXNzIEVuY3J5cHRpb25TZXJ2aWNlIHtcclxuICBjb25zdHJ1Y3RvcihcclxuICAgIHByaXZhdGUgcmVhZG9ubHkgY29uZmlnU2VydmljZTogQ29uZmlnU2VydmljZSxcclxuICAgIHByaXZhdGUgcmVhZG9ubHkgYWVzRW5jcnlwdGlvbjogQUVTRW5jcnlwdGlvbixcclxuICAgIHByaXZhdGUgcmVhZG9ubHkgcnNhRW5jcnlwdGlvbjogUlNBRW5jcnlwdGlvbixcclxuICAgIHByaXZhdGUgcmVhZG9ubHkgaGFzaFNlcnZpY2U6IEhhc2hTZXJ2aWNlLFxyXG4gICkge31cclxuXHJcbiAgLy8gQUVTIEVuY3J5cHRpb24gTWV0aG9kc1xyXG4gIC8qKlxyXG4gICAqIEVuY3J5cHQgZGF0YSB1c2luZyBBRVMtMjU2LUdDTVxyXG4gICAqL1xyXG4gIGFzeW5jIGVuY3J5cHRBRVMoZGF0YTogc3RyaW5nLCBrZXk/OiBzdHJpbmcpOiBQcm9taXNlPEVuY3J5cHRpb25SZXN1bHQ+IHtcclxuICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHRoaXMuYWVzRW5jcnlwdGlvbi5lbmNyeXB0KGRhdGEsIGtleSk7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBlbmNyeXB0ZWREYXRhOiByZXN1bHQuZW5jcnlwdGVkRGF0YSxcclxuICAgICAgaXY6IHJlc3VsdC5pdixcclxuICAgICAgdGFnOiByZXN1bHQudGFnLFxyXG4gICAgICBhbGdvcml0aG06IHJlc3VsdC5hbGdvcml0aG0sXHJcbiAgICB9O1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogRGVjcnlwdCBkYXRhIHVzaW5nIEFFUy0yNTYtR0NNXHJcbiAgICovXHJcbiAgYXN5bmMgZGVjcnlwdEFFUyhlbmNyeXB0ZWREYXRhOiBzdHJpbmcsIGl2OiBzdHJpbmcsIHRhZzogc3RyaW5nLCBrZXk/OiBzdHJpbmcpOiBQcm9taXNlPERlY3J5cHRpb25SZXN1bHQ+IHtcclxuICAgIGNvbnN0IGRlY3J5cHRlZERhdGEgPSBhd2FpdCB0aGlzLmFlc0VuY3J5cHRpb24uZGVjcnlwdCh7XHJcbiAgICAgIGVuY3J5cHRlZERhdGEsXHJcbiAgICAgIGl2LFxyXG4gICAgICB0YWcsXHJcbiAgICAgIGtleSxcclxuICAgIH0pO1xyXG4gICAgXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBkZWNyeXB0ZWREYXRhLFxyXG4gICAgICBhbGdvcml0aG06ICdhZXMtMjU2LWdjbScsXHJcbiAgICB9O1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2VuZXJhdGUgQUVTIGVuY3J5cHRpb24ga2V5XHJcbiAgICovXHJcbiAgZ2VuZXJhdGVBRVNLZXkoKTogc3RyaW5nIHtcclxuICAgIHJldHVybiB0aGlzLmFlc0VuY3J5cHRpb24uZ2VuZXJhdGVLZXkoKTtcclxuICB9XHJcblxyXG4gIC8vIFJTQSBFbmNyeXB0aW9uIE1ldGhvZHNcclxuICAvKipcclxuICAgKiBHZW5lcmF0ZSBSU0Ega2V5IHBhaXJcclxuICAgKi9cclxuICBhc3luYyBnZW5lcmF0ZVJTQUtleVBhaXIoa2V5U2l6ZT86IG51bWJlcik6IFByb21pc2U8UlNBS2V5UGFpcj4ge1xyXG4gICAgcmV0dXJuIHRoaXMucnNhRW5jcnlwdGlvbi5nZW5lcmF0ZUtleVBhaXIoa2V5U2l6ZSk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBFbmNyeXB0IGRhdGEgdXNpbmcgUlNBXHJcbiAgICovXHJcbiAgYXN5bmMgZW5jcnlwdFJTQShkYXRhOiBzdHJpbmcsIHB1YmxpY0tleTogc3RyaW5nLCBvcHRpb25zPzogUlNBRW5jcnlwdGlvbk9wdGlvbnMpOiBQcm9taXNlPHN0cmluZz4ge1xyXG4gICAgcmV0dXJuIHRoaXMucnNhRW5jcnlwdGlvbi5lbmNyeXB0KGRhdGEsIHB1YmxpY0tleSwgb3B0aW9ucyk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBEZWNyeXB0IGRhdGEgdXNpbmcgUlNBXHJcbiAgICovXHJcbiAgYXN5bmMgZGVjcnlwdFJTQShlbmNyeXB0ZWREYXRhOiBzdHJpbmcsIHByaXZhdGVLZXk6IHN0cmluZywgb3B0aW9ucz86IFJTQUVuY3J5cHRpb25PcHRpb25zKTogUHJvbWlzZTxzdHJpbmc+IHtcclxuICAgIHJldHVybiB0aGlzLnJzYUVuY3J5cHRpb24uZGVjcnlwdChlbmNyeXB0ZWREYXRhLCBwcml2YXRlS2V5LCBvcHRpb25zKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIFNpZ24gZGF0YSB1c2luZyBSU0FcclxuICAgKi9cclxuICBhc3luYyBzaWduUlNBKGRhdGE6IHN0cmluZywgcHJpdmF0ZUtleTogc3RyaW5nLCBhbGdvcml0aG0/OiBzdHJpbmcpOiBQcm9taXNlPHN0cmluZz4ge1xyXG4gICAgcmV0dXJuIHRoaXMucnNhRW5jcnlwdGlvbi5zaWduKGRhdGEsIHByaXZhdGVLZXksIGFsZ29yaXRobSk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBWZXJpZnkgUlNBIHNpZ25hdHVyZVxyXG4gICAqL1xyXG4gIGFzeW5jIHZlcmlmeVJTQShkYXRhOiBzdHJpbmcsIHNpZ25hdHVyZTogc3RyaW5nLCBwdWJsaWNLZXk6IHN0cmluZywgYWxnb3JpdGhtPzogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiB7XHJcbiAgICByZXR1cm4gdGhpcy5yc2FFbmNyeXB0aW9uLnZlcmlmeShkYXRhLCBzaWduYXR1cmUsIHB1YmxpY0tleSwgYWxnb3JpdGhtKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEVuY3J5cHQgbGFyZ2UgZGF0YSB1c2luZyBSU0EgKGNodW5rZWQpXHJcbiAgICovXHJcbiAgYXN5bmMgZW5jcnlwdFJTQUxhcmdlKGRhdGE6IHN0cmluZywgcHVibGljS2V5OiBzdHJpbmcpOiBQcm9taXNlPHN0cmluZ1tdPiB7XHJcbiAgICByZXR1cm4gdGhpcy5yc2FFbmNyeXB0aW9uLmVuY3J5cHRMYXJnZURhdGEoZGF0YSwgcHVibGljS2V5KTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIERlY3J5cHQgbGFyZ2UgZGF0YSB1c2luZyBSU0EgKGNodW5rZWQpXHJcbiAgICovXHJcbiAgYXN5bmMgZGVjcnlwdFJTQUxhcmdlKGVuY3J5cHRlZENodW5rczogc3RyaW5nW10sIHByaXZhdGVLZXk6IHN0cmluZyk6IFByb21pc2U8c3RyaW5nPiB7XHJcbiAgICByZXR1cm4gdGhpcy5yc2FFbmNyeXB0aW9uLmRlY3J5cHRMYXJnZURhdGEoZW5jcnlwdGVkQ2h1bmtzLCBwcml2YXRlS2V5KTtcclxuICB9XHJcblxyXG4gIC8vIEhhc2ggTWV0aG9kc1xyXG4gIC8qKlxyXG4gICAqIEhhc2ggZGF0YSB1c2luZyBzcGVjaWZpZWQgYWxnb3JpdGhtXHJcbiAgICovXHJcbiAgaGFzaChkYXRhOiBzdHJpbmcsIGFsZ29yaXRobTogSGFzaEFsZ29yaXRobSA9IEhhc2hBbGdvcml0aG0uU0hBMjU2KTogc3RyaW5nIHtcclxuICAgIHJldHVybiB0aGlzLmhhc2hTZXJ2aWNlLmhhc2goZGF0YSwgeyBhbGdvcml0aG0gfSk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBIYXNoIGRhdGEgdXNpbmcgU0hBLTI1NlxyXG4gICAqL1xyXG4gIGhhc2hTSEEyNTYoZGF0YTogc3RyaW5nKTogc3RyaW5nIHtcclxuICAgIHJldHVybiB0aGlzLmhhc2hTZXJ2aWNlLnNoYTI1NihkYXRhKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEhhc2ggZGF0YSB1c2luZyBTSEEtNTEyXHJcbiAgICovXHJcbiAgaGFzaFNIQTUxMihkYXRhOiBzdHJpbmcpOiBzdHJpbmcge1xyXG4gICAgcmV0dXJuIHRoaXMuaGFzaFNlcnZpY2Uuc2hhNTEyKGRhdGEpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ3JlYXRlIEhNQUNcclxuICAgKi9cclxuICBobWFjKGRhdGE6IHN0cmluZywga2V5OiBzdHJpbmcsIGFsZ29yaXRobTogSGFzaEFsZ29yaXRobSA9IEhhc2hBbGdvcml0aG0uU0hBMjU2KTogc3RyaW5nIHtcclxuICAgIHJldHVybiB0aGlzLmhhc2hTZXJ2aWNlLmhtYWMoZGF0YSwga2V5LCB7IGFsZ29yaXRobSB9KTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEhhc2ggcGFzc3dvcmQgdXNpbmcgYmNyeXB0XHJcbiAgICovXHJcbiAgYXN5bmMgaGFzaFBhc3N3b3JkKHBhc3N3b3JkOiBzdHJpbmcsIG9wdGlvbnM/OiBQYXNzd29yZEhhc2hPcHRpb25zKTogUHJvbWlzZTxzdHJpbmc+IHtcclxuICAgIHJldHVybiB0aGlzLmhhc2hTZXJ2aWNlLmhhc2hQYXNzd29yZChwYXNzd29yZCwgb3B0aW9ucyk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBWZXJpZnkgcGFzc3dvcmQgYWdhaW5zdCBoYXNoXHJcbiAgICovXHJcbiAgYXN5bmMgdmVyaWZ5UGFzc3dvcmQocGFzc3dvcmQ6IHN0cmluZywgaGFzaDogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiB7XHJcbiAgICByZXR1cm4gdGhpcy5oYXNoU2VydmljZS52ZXJpZnlQYXNzd29yZChwYXNzd29yZCwgaGFzaCk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZW5lcmF0ZSBQQktERjIgaGFzaFxyXG4gICAqL1xyXG4gIGFzeW5jIHBia2RmMihwYXNzd29yZDogc3RyaW5nLCBzYWx0OiBzdHJpbmcsIGl0ZXJhdGlvbnM/OiBudW1iZXIsIGtleUxlbmd0aD86IG51bWJlcik6IFByb21pc2U8c3RyaW5nPiB7XHJcbiAgICByZXR1cm4gdGhpcy5oYXNoU2VydmljZS5wYmtkZjIocGFzc3dvcmQsIHNhbHQsIGl0ZXJhdGlvbnMsIGtleUxlbmd0aCk7XHJcbiAgfVxyXG5cclxuICAvLyBVdGlsaXR5IE1ldGhvZHNcclxuICAvKipcclxuICAgKiBHZW5lcmF0ZSBhIHNlY3VyZSByYW5kb20gc3RyaW5nXHJcbiAgICovXHJcbiAgZ2VuZXJhdGVTZWN1cmVSYW5kb20obGVuZ3RoOiBudW1iZXIgPSAzMik6IHN0cmluZyB7XHJcbiAgICByZXR1cm4gY3J5cHRvLnJhbmRvbUJ5dGVzKGxlbmd0aCkudG9TdHJpbmcoJ2hleCcpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2VuZXJhdGUgY3J5cHRvZ3JhcGhpY2FsbHkgc2VjdXJlIHNhbHRcclxuICAgKi9cclxuICBnZW5lcmF0ZVNhbHQobGVuZ3RoOiBudW1iZXIgPSAzMik6IHN0cmluZyB7XHJcbiAgICByZXR1cm4gdGhpcy5oYXNoU2VydmljZS5nZW5lcmF0ZVJhbmRvbVNhbHQobGVuZ3RoKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIFZhbGlkYXRlIGVuY3J5cHRpb24ga2V5c1xyXG4gICAqL1xyXG4gIHZhbGlkYXRlQUVTS2V5KGtleTogc3RyaW5nKTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gdGhpcy5hZXNFbmNyeXB0aW9uLnZhbGlkYXRlS2V5KGtleSk7XHJcbiAgfVxyXG5cclxuICB2YWxpZGF0ZVJTQVByaXZhdGVLZXkocHJpdmF0ZUtleTogc3RyaW5nKTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gdGhpcy5yc2FFbmNyeXB0aW9uLnZhbGlkYXRlUHJpdmF0ZUtleShwcml2YXRlS2V5KTtcclxuICB9XHJcblxyXG4gIHZhbGlkYXRlUlNBUHVibGljS2V5KHB1YmxpY0tleTogc3RyaW5nKTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gdGhpcy5yc2FFbmNyeXB0aW9uLnZhbGlkYXRlUHVibGljS2V5KHB1YmxpY0tleSk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZW5lcmF0ZSBjaGVja3N1bSBmb3IgZGF0YSBpbnRlZ3JpdHlcclxuICAgKi9cclxuICBnZW5lcmF0ZUNoZWNrc3VtKGRhdGE6IHN0cmluZywgYWxnb3JpdGhtOiBIYXNoQWxnb3JpdGhtID0gSGFzaEFsZ29yaXRobS5TSEEyNTYpOiBzdHJpbmcge1xyXG4gICAgcmV0dXJuIHRoaXMuaGFzaFNlcnZpY2UuZ2VuZXJhdGVDaGVja3N1bShkYXRhLCBhbGdvcml0aG0pO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogVmVyaWZ5IGRhdGEgaW50ZWdyaXR5IHVzaW5nIGNoZWNrc3VtXHJcbiAgICovXHJcbiAgdmVyaWZ5Q2hlY2tzdW0oZGF0YTogc3RyaW5nLCBleHBlY3RlZENoZWNrc3VtOiBzdHJpbmcsIGFsZ29yaXRobTogSGFzaEFsZ29yaXRobSA9IEhhc2hBbGdvcml0aG0uU0hBMjU2KTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gdGhpcy5oYXNoU2VydmljZS52ZXJpZnlDaGVja3N1bShkYXRhLCBleHBlY3RlZENoZWNrc3VtLCBhbGdvcml0aG0pO1xyXG4gIH1cclxufSAiXSwidmVyc2lvbiI6M30=