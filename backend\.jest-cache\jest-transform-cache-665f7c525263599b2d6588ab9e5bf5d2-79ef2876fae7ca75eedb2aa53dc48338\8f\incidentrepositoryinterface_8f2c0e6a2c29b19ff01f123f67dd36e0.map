{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\repositories\\incident.repository.interface.ts", "mappings": "", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\repositories\\incident.repository.interface.ts"], "sourcesContent": ["import { Incident, IncidentStatus, IncidentPriority } from '../../entities/incident/incident.entity';\r\nimport { ThreatSeverity } from '../../enums/threat-severity.enum';\r\nimport { BaseRepository } from '../../../../shared-kernel/domain/base-repository.interface';\r\n\r\n/**\r\n * Incident Search Criteria\r\n */\r\nexport interface IncidentSearchCriteria {\r\n  /** Incident statuses */\r\n  statuses?: IncidentStatus[];\r\n  /** Priority levels */\r\n  priorities?: IncidentPriority[];\r\n  /** Severity levels */\r\n  severities?: ThreatSeverity[];\r\n  /** Categories */\r\n  categories?: string[];\r\n  /** Types */\r\n  types?: string[];\r\n  /** Detection date range */\r\n  detectionDateRange?: {\r\n    from: Date;\r\n    to: Date;\r\n  };\r\n  /** Resolution date range */\r\n  resolutionDateRange?: {\r\n    from: Date;\r\n    to: Date;\r\n  };\r\n  /** Incident commander */\r\n  incidentCommander?: string;\r\n  /** Lead investigator */\r\n  leadInvestigator?: string;\r\n  /** Team member */\r\n  teamMember?: string;\r\n  /** Affected asset IDs */\r\n  affectedAssetIds?: string[];\r\n  /** Related event IDs */\r\n  relatedEventIds?: string[];\r\n  /** Related threat IDs */\r\n  relatedThreatIds?: string[];\r\n  /** Related vulnerability IDs */\r\n  relatedVulnerabilityIds?: string[];\r\n  /** Tags */\r\n  tags?: string[];\r\n  /** Text search in title/description */\r\n  searchText?: string;\r\n  /** SLA status */\r\n  slaStatus?: 'compliant' | 'at_risk' | 'breached';\r\n  /** Has evidence */\r\n  hasEvidence?: boolean;\r\n  /** Compliance requirements */\r\n  complianceRequirements?: string[];\r\n  /** Business impact level */\r\n  businessImpactLevel?: ('low' | 'medium' | 'high' | 'critical')[];\r\n  /** Pagination */\r\n  pagination?: {\r\n    page: number;\r\n    limit: number;\r\n    sortBy?: string;\r\n    sortOrder?: 'asc' | 'desc';\r\n  };\r\n}\r\n\r\n/**\r\n * Incident Statistics\r\n */\r\nexport interface IncidentStatistics {\r\n  /** Total incidents */\r\n  total: number;\r\n  /** Status distribution */\r\n  statusDistribution: Record<IncidentStatus, number>;\r\n  /** Priority distribution */\r\n  priorityDistribution: Record<IncidentPriority, number>;\r\n  /** Severity distribution */\r\n  severityDistribution: Record<ThreatSeverity, number>;\r\n  /** Response metrics */\r\n  responseMetrics: {\r\n    averageResponseTime: number;\r\n    averageContainmentTime: number;\r\n    averageResolutionTime: number;\r\n    slaComplianceRate: number;\r\n  };\r\n  /** Business impact */\r\n  businessImpact: {\r\n    totalFinancialImpact: number;\r\n    averageDowntime: number;\r\n    customersAffected: number;\r\n    reputationImpact: number;\r\n  };\r\n  /** Team performance */\r\n  teamPerformance: {\r\n    averageTeamSize: number;\r\n    commanderAssignmentRate: number;\r\n    escalationRate: number;\r\n    lessonsLearnedRate: number;\r\n  };\r\n  /** Trends */\r\n  trends: {\r\n    daily: Array<{ date: string; count: number; avgSeverity: number }>;\r\n    weekly: Array<{ week: string; count: number; avgSeverity: number }>;\r\n    monthly: Array<{ month: string; count: number; avgSeverity: number }>;\r\n  };\r\n}\r\n\r\n/**\r\n * Incident Performance Metrics\r\n */\r\nexport interface IncidentPerformanceMetrics {\r\n  /** Response time metrics */\r\n  responseTime: {\r\n    average: number;\r\n    median: number;\r\n    p95: number;\r\n    p99: number;\r\n    byPriority: Record<IncidentPriority, number>;\r\n    bySeverity: Record<ThreatSeverity, number>;\r\n  };\r\n  /** Containment time metrics */\r\n  containmentTime: {\r\n    average: number;\r\n    median: number;\r\n    p95: number;\r\n    p99: number;\r\n    byPriority: Record<IncidentPriority, number>;\r\n    bySeverity: Record<ThreatSeverity, number>;\r\n  };\r\n  /** Resolution time metrics */\r\n  resolutionTime: {\r\n    average: number;\r\n    median: number;\r\n    p95: number;\r\n    p99: number;\r\n    byPriority: Record<IncidentPriority, number>;\r\n    bySeverity: Record<ThreatSeverity, number>;\r\n  };\r\n  /** SLA compliance */\r\n  slaCompliance: {\r\n    overall: number;\r\n    byPriority: Record<IncidentPriority, number>;\r\n    bySeverity: Record<ThreatSeverity, number>;\r\n    breaches: number;\r\n    atRisk: number;\r\n  };\r\n}\r\n\r\n/**\r\n * Incident Repository Interface\r\n * \r\n * Defines the contract for incident data persistence and retrieval.\r\n * Supports complex querying, performance analytics, and compliance reporting.\r\n * \r\n * Key responsibilities:\r\n * - Incident CRUD operations\r\n * - Complex search and filtering\r\n * - Performance metrics and analytics\r\n * - SLA monitoring and compliance\r\n * - Team performance tracking\r\n * - Business impact analysis\r\n */\r\nexport interface IncidentRepository extends BaseRepository<Incident> {\r\n  /**\r\n   * Find incidents by search criteria\r\n   */\r\n  findByCriteria(criteria: IncidentSearchCriteria): Promise<{\r\n    incidents: Incident[];\r\n    total: number;\r\n    page: number;\r\n    limit: number;\r\n  }>;\r\n\r\n  /**\r\n   * Find incidents by status\r\n   */\r\n  findByStatus(status: IncidentStatus): Promise<Incident[]>;\r\n\r\n  /**\r\n   * Find incidents by priority\r\n   */\r\n  findByPriority(priority: IncidentPriority): Promise<Incident[]>;\r\n\r\n  /**\r\n   * Find incidents by severity\r\n   */\r\n  findBySeverity(severity: ThreatSeverity): Promise<Incident[]>;\r\n\r\n  /**\r\n   * Find active incidents\r\n   */\r\n  findActiveIncidents(): Promise<Incident[]>;\r\n\r\n  /**\r\n   * Find critical incidents\r\n   */\r\n  findCriticalIncidents(): Promise<Incident[]>;\r\n\r\n  /**\r\n   * Find incidents by incident commander\r\n   */\r\n  findByIncidentCommander(commanderId: string): Promise<Incident[]>;\r\n\r\n  /**\r\n   * Find incidents by team member\r\n   */\r\n  findByTeamMember(memberId: string): Promise<Incident[]>;\r\n\r\n  /**\r\n   * Find incidents affecting asset\r\n   */\r\n  findByAffectedAsset(assetId: string): Promise<Incident[]>;\r\n\r\n  /**\r\n   * Find incidents related to event\r\n   */\r\n  findByRelatedEvent(eventId: string): Promise<Incident[]>;\r\n\r\n  /**\r\n   * Find incidents related to threat\r\n   */\r\n  findByRelatedThreat(threatId: string): Promise<Incident[]>;\r\n\r\n  /**\r\n   * Find incidents related to vulnerability\r\n   */\r\n  findByRelatedVulnerability(vulnerabilityId: string): Promise<Incident[]>;\r\n\r\n  /**\r\n   * Find overdue incidents\r\n   */\r\n  findOverdueIncidents(): Promise<Incident[]>;\r\n\r\n  /**\r\n   * Find incidents with SLA breaches\r\n   */\r\n  findSLABreaches(): Promise<Incident[]>;\r\n\r\n  /**\r\n   * Find incidents at risk of SLA breach\r\n   */\r\n  findSLAAtRisk(hoursUntilBreach: number): Promise<Incident[]>;\r\n\r\n  /**\r\n   * Find incidents by detection date range\r\n   */\r\n  findByDetectionDateRange(from: Date, to: Date): Promise<Incident[]>;\r\n\r\n  /**\r\n   * Find incidents by tags\r\n   */\r\n  findByTags(tags: string[]): Promise<Incident[]>;\r\n\r\n  /**\r\n   * Get incident statistics\r\n   */\r\n  getStatistics(dateRange?: { from: Date; to: Date }): Promise<IncidentStatistics>;\r\n\r\n  /**\r\n   * Get incident performance metrics\r\n   */\r\n  getPerformanceMetrics(dateRange?: { from: Date; to: Date }): Promise<IncidentPerformanceMetrics>;\r\n\r\n  /**\r\n   * Get incident trends\r\n   */\r\n  getTrends(\r\n    period: 'daily' | 'weekly' | 'monthly',\r\n    dateRange: { from: Date; to: Date }\r\n  ): Promise<Array<{\r\n    date: string;\r\n    created: number;\r\n    resolved: number;\r\n    active: number;\r\n    averageSeverity: number;\r\n    averagePriority: number;\r\n  }>>;\r\n\r\n  /**\r\n   * Get team performance metrics\r\n   */\r\n  getTeamPerformanceMetrics(\r\n    teamMemberId?: string,\r\n    dateRange?: { from: Date; to: Date }\r\n  ): Promise<{\r\n    incidentsHandled: number;\r\n    averageResponseTime: number;\r\n    averageResolutionTime: number;\r\n    slaComplianceRate: number;\r\n    escalationRate: number;\r\n    commanderAssignments: number;\r\n    lessonsLearnedCompleted: number;\r\n    performanceRating: 'excellent' | 'good' | 'average' | 'needs_improvement';\r\n  }>;\r\n\r\n  /**\r\n   * Get business impact analysis\r\n   */\r\n  getBusinessImpactAnalysis(dateRange?: { from: Date; to: Date }): Promise<{\r\n    totalFinancialImpact: number;\r\n    averageFinancialImpact: number;\r\n    totalDowntime: number;\r\n    averageDowntime: number;\r\n    customersAffected: number;\r\n    servicesAffected: string[];\r\n    reputationImpact: 'low' | 'medium' | 'high' | 'critical';\r\n    impactByCategory: Record<string, number>;\r\n    impactBySeverity: Record<ThreatSeverity, number>;\r\n  }>;\r\n\r\n  /**\r\n   * Get compliance report\r\n   */\r\n  getComplianceReport(\r\n    regulation: string,\r\n    dateRange?: { from: Date; to: Date }\r\n  ): Promise<{\r\n    totalIncidents: number;\r\n    reportableIncidents: number;\r\n    reportingCompliance: number;\r\n    averageReportingTime: number;\r\n    breaches: number;\r\n    potentialFines: number;\r\n    complianceGaps: string[];\r\n  }>;\r\n\r\n  /**\r\n   * Get incident commander workload\r\n   */\r\n  getCommanderWorkload(commanderId?: string): Promise<{\r\n    activeIncidents: number;\r\n    totalIncidents: number;\r\n    averageIncidentsPerMonth: number;\r\n    currentWorkload: 'light' | 'moderate' | 'heavy' | 'overloaded';\r\n    performanceMetrics: {\r\n      averageResponseTime: number;\r\n      averageResolutionTime: number;\r\n      slaComplianceRate: number;\r\n      escalationRate: number;\r\n    };\r\n  }>;\r\n\r\n  /**\r\n   * Get incident escalation analysis\r\n   */\r\n  getEscalationAnalysis(dateRange?: { from: Date; to: Date }): Promise<{\r\n    totalEscalations: number;\r\n    escalationRate: number;\r\n    escalationReasons: Record<string, number>;\r\n    escalationsByPriority: Record<IncidentPriority, number>;\r\n    escalationsBySeverity: Record<ThreatSeverity, number>;\r\n    averageEscalationTime: number;\r\n    executiveEscalations: number;\r\n  }>;\r\n\r\n  /**\r\n   * Get evidence collection metrics\r\n   */\r\n  getEvidenceMetrics(dateRange?: { from: Date; to: Date }): Promise<{\r\n    incidentsWithEvidence: number;\r\n    averageEvidenceItems: number;\r\n    evidenceTypes: Record<string, number>;\r\n    chainOfCustodyCompliance: number;\r\n    forensicAnalysisRate: number;\r\n    evidenceRetentionCompliance: number;\r\n  }>;\r\n\r\n  /**\r\n   * Get lessons learned analysis\r\n   */\r\n  getLessonsLearnedAnalysis(dateRange?: { from: Date; to: Date }): Promise<{\r\n    completionRate: number;\r\n    averageTimeToCompletion: number;\r\n    actionItemsGenerated: number;\r\n    actionItemsCompleted: number;\r\n    processImprovements: number;\r\n    technologyImprovements: number;\r\n    trainingNeeds: string[];\r\n    topRecommendations: string[];\r\n  }>;\r\n\r\n  /**\r\n   * Search incidents with full-text search\r\n   */\r\n  searchFullText(\r\n    query: string,\r\n    filters?: Partial<IncidentSearchCriteria>\r\n  ): Promise<{\r\n    incidents: Incident[];\r\n    total: number;\r\n    highlights: Record<string, string[]>;\r\n  }>;\r\n\r\n  /**\r\n   * Get similar incidents\r\n   */\r\n  findSimilarIncidents(\r\n    incident: Incident,\r\n    similarity: 'category' | 'assets' | 'techniques' | 'indicators'\r\n  ): Promise<Incident[]>;\r\n\r\n  /**\r\n   * Bulk update incident status\r\n   */\r\n  bulkUpdateStatus(\r\n    incidentIds: string[],\r\n    status: IncidentStatus,\r\n    reason: string,\r\n    updatedBy: string\r\n  ): Promise<number>;\r\n\r\n  /**\r\n   * Bulk assign incident commander\r\n   */\r\n  bulkAssignCommander(\r\n    incidentIds: string[],\r\n    commanderId: string,\r\n    assignedBy: string\r\n  ): Promise<number>;\r\n\r\n  /**\r\n   * Bulk add tags to incidents\r\n   */\r\n  bulkAddTags(\r\n    incidentIds: string[],\r\n    tags: string[],\r\n    addedBy: string\r\n  ): Promise<number>;\r\n\r\n  /**\r\n   * Get incident timeline\r\n   */\r\n  getIncidentTimeline(incidentId: string): Promise<Array<{\r\n    timestamp: Date;\r\n    event: string;\r\n    description: string;\r\n    performedBy: string;\r\n    category: 'status_change' | 'assignment' | 'evidence' | 'communication' | 'escalation';\r\n    metadata?: Record<string, any>;\r\n  }>>;\r\n\r\n  /**\r\n   * Get incident dependencies\r\n   */\r\n  getIncidentDependencies(incidentId: string): Promise<{\r\n    relatedIncidents: Incident[];\r\n    relatedEvents: string[];\r\n    relatedThreats: string[];\r\n    relatedVulnerabilities: string[];\r\n    affectedAssets: string[];\r\n    involvedPersonnel: string[];\r\n  }>;\r\n\r\n  /**\r\n   * Archive old incidents\r\n   */\r\n  archiveOldIncidents(\r\n    olderThan: Date,\r\n    statuses: IncidentStatus[]\r\n  ): Promise<number>;\r\n\r\n  /**\r\n   * Get incident health metrics\r\n   */\r\n  getHealthMetrics(): Promise<{\r\n    activeIncidentCount: number;\r\n    overdueIncidentCount: number;\r\n    slaBreachRate: number;\r\n    averageResponseTime: number;\r\n    commanderUtilization: number;\r\n    escalationRate: number;\r\n    backlogSize: number;\r\n    criticalBacklog: number;\r\n  }>;\r\n\r\n  /**\r\n   * Export incidents for reporting\r\n   */\r\n  exportIncidents(\r\n    criteria: IncidentSearchCriteria,\r\n    format: 'csv' | 'json' | 'pdf'\r\n  ): Promise<{\r\n    data: Buffer | string;\r\n    filename: string;\r\n    contentType: string;\r\n  }>;\r\n\r\n  /**\r\n   * Get incident forecast\r\n   */\r\n  getForecast(\r\n    period: 'weekly' | 'monthly' | 'quarterly'\r\n  ): Promise<{\r\n    expectedIncidents: number;\r\n    expectedCriticalIncidents: number;\r\n    resourceRequirements: {\r\n      commanders: number;\r\n      analysts: number;\r\n      specialists: number;\r\n    };\r\n    riskTrend: 'increasing' | 'stable' | 'decreasing';\r\n    recommendations: string[];\r\n  }>;\r\n\r\n  /**\r\n   * Validate incident data integrity\r\n   */\r\n  validateDataIntegrity(): Promise<{\r\n    isValid: boolean;\r\n    issues: Array<{\r\n      type: string;\r\n      description: string;\r\n      affectedRecords: number;\r\n      severity: 'low' | 'medium' | 'high' | 'critical';\r\n    }>;\r\n  }>;\r\n}\r\n"], "version": 3}