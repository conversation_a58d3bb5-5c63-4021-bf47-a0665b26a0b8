{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\network\\port.value-object.spec.ts", "mappings": ";;AAAA,2DAAmF;AAEnF,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;IACjC,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,IAAI,GAAG,wBAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAE1B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC7B,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,gCAAY,CAAC,GAAG,CAAC,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,IAAI,GAAG,wBAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAE1B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC7B,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,gCAAY,CAAC,GAAG,CAAC,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,IAAI,GAAG,wBAAI,CAAC,MAAM,CAAC,GAAG,EAAE,gCAAY,CAAC,GAAG,CAAC,CAAC;YAEhD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC9B,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,gCAAY,CAAC,GAAG,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,IAAI,GAAG,wBAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAEvC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC7B,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,gCAAY,CAAC,GAAG,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,CAAC,GAAG,EAAE,CAAC,wBAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,yCAAyC,CAAC,CAAC;YAC9E,MAAM,CAAC,GAAG,EAAE,CAAC,wBAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,yCAAyC,CAAC,CAAC;YACjF,MAAM,CAAC,GAAG,EAAE,CAAC,wBAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,CAAC,GAAG,EAAE,CAAC,wBAAI,CAAC,MAAM,CAAC,EAAE,EAAE,SAAyB,CAAC,CAAC,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;QAChG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,KAAK,GAAG,wBAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,SAAS,GAAG,wBAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAElC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,cAAc,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;YAE1C,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBAC/B,MAAM,IAAI,GAAG,wBAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAC/B,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,6BAAS,CAAC,UAAU,CAAC,CAAC;gBAC5D,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACtC,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACxC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,eAAe,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAE5C,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBAChC,MAAM,IAAI,GAAG,wBAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAC/B,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,6BAAS,CAAC,UAAU,CAAC,CAAC;gBAC5D,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACvC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,YAAY,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YAE3C,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBAC7B,MAAM,IAAI,GAAG,wBAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAC/B,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,6BAAS,CAAC,OAAO,CAAC,CAAC;gBACzD,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACvC,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,QAAQ,GAAG;gBACf,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE;gBAC5B,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE;gBAC7B,EAAE,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE;gBAC/B,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE;gBAC7B,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,aAAa,EAAE;aACrC,CAAC;YAEF,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE;gBAC9C,MAAM,IAAI,GAAG,wBAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAC/B,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,QAAQ,GAAG;gBACf,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE;gBAC5B,EAAE,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE;gBAC7B,EAAE,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE;aAC/B,CAAC;YAEF,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE;gBAC9C,MAAM,IAAI,GAAG,wBAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAC/B,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,IAAI,GAAG,wBAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAC7B,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,aAAa,GAAG;gBACpB,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,gCAAY,CAAC,GAAG,EAAE,EAAE,SAAS;gBACnD,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,gCAAY,CAAC,GAAG,EAAE,EAAE,MAAM;gBAChD,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,gCAAY,CAAC,GAAG,EAAE,EAAE,MAAM;aAClD,CAAC;YAEF,aAAa,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE;gBACpD,MAAM,IAAI,GAAG,wBAAI,CAAC,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;gBAC5C,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,iCAAa,CAAC,QAAQ,CAAC,CAAC;gBACzD,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,aAAa,GAAG;gBACpB,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,gCAAY,CAAC,GAAG,EAAE,EAAE,MAAM;gBAChD,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,gCAAY,CAAC,GAAG,EAAE,EAAE,MAAM;gBAClD,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,gCAAY,CAAC,GAAG,EAAE,EAAE,aAAa;aAC1D,CAAC;YAEF,aAAa,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE;gBACpD,MAAM,IAAI,GAAG,wBAAI,CAAC,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;gBAC5C,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,iCAAa,CAAC,IAAI,CAAC,CAAC;gBACrD,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,eAAe,GAAG;gBACtB,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,gCAAY,CAAC,GAAG,EAAE,EAAE,OAAO;gBACjD,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,gCAAY,CAAC,GAAG,EAAE,EAAE,OAAO;gBACjD,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,gCAAY,CAAC,GAAG,EAAE,EAAE,WAAW;aACxD,CAAC;YAEF,eAAe,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE;gBACtD,MAAM,IAAI,GAAG,wBAAI,CAAC,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;gBAC5C,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,iCAAa,CAAC,MAAM,CAAC,CAAC;gBACvD,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,IAAI,GAAG,wBAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAC7B,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,iCAAa,CAAC,GAAG,CAAC,CAAC;YACpD,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,aAAa,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAEhD,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBAC9B,MAAM,IAAI,GAAG,wBAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAC/B,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,cAAc,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,kCAAkC;YAEhF,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBAC/B,MAAM,IAAI,GAAG,wBAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAC/B,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,IAAI,GAAG,wBAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC1B,MAAM,eAAe,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAE1D,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;YAClE,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;YACxD,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,iCAAiC,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,IAAI,GAAG,wBAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC1B,MAAM,eAAe,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAE1D,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,2CAA2C,CAAC,CAAC;YAC/E,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,iDAAiD,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,IAAI,GAAG,wBAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC1B,MAAM,eAAe,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAE1D,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,IAAI,GAAG,wBAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAC5B,MAAM,eAAe,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAE1D,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,qCAAqC,CAAC,CAAC;YACzE,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oEAAoE,EAAE,GAAG,EAAE;YAC5E,MAAM,IAAI,GAAG,wBAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC1B,MAAM,eAAe,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAE1D,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,0CAA0C,CAAC,CAAC;YAC9E,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,sCAAsC,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,cAAc,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAErD,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBAC/B,MAAM,IAAI,GAAG,wBAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAC/B,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,gBAAgB,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAEpD,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACjC,MAAM,IAAI,GAAG,wBAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAC/B,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,SAAS,GAAG;gBAChB,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,EAAE,gBAAgB,EAAE,gCAAY,CAAC,GAAG,EAAE;gBACzE,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,EAAE,gBAAgB,EAAE,gCAAY,CAAC,GAAG,EAAE;gBACzE,EAAE,KAAK,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,EAAE,gBAAgB,EAAE,gCAAY,CAAC,GAAG,EAAE;aAC5E,CAAC;YAEF,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,gBAAgB,EAAE,EAAE,EAAE;gBAC9D,MAAM,IAAI,GAAG,wBAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACpC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACvC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,CAAC,GAAG,EAAE,CAAC,wBAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,+CAA+C,CAAC,CAAC;YAClG,MAAM,CAAC,GAAG,EAAE,CAAC,wBAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;YACvD,MAAM,CAAC,GAAG,EAAE,CAAC,wBAAI,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,IAAI,GAAG,wBAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC9B,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC9B,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,gCAAY,CAAC,GAAG,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,CAAC,wBAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YACzC,MAAM,CAAC,wBAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,IAAI,GAAG,wBAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC1B,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,IAAI,GAAG,wBAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC1B,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YAEhC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC7B,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,6BAAS,CAAC,UAAU,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,iCAAa,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,CAAC,wBAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACxD,MAAM,CAAC,wBAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAClE,MAAM,CAAC,wBAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAClE,MAAM,CAAC,wBAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,KAAK,GAAG,wBAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC3B,MAAM,KAAK,GAAG,wBAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC3B,MAAM,KAAK,GAAG,wBAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC5B,MAAM,KAAK,GAAG,wBAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAE3B,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,IAAI,GAAG,wBAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC1B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,IAAI,GAAG,wBAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC1B,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YAE3B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC7B,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,iCAAa,CAAC,IAAI,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,IAAI,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,gCAAY,CAAC,GAAG,EAAE,CAAC;YACzD,MAAM,IAAI,GAAG,wBAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEjC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC9B,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,gCAAY,CAAC,GAAG,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,CAAC,wBAAI,CAAC,OAAO,CAAC,EAAE,EAAE,gCAAY,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,wBAAI,CAAC,OAAO,CAAC,KAAK,EAAE,gCAAY,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1D,MAAM,CAAC,wBAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,gCAAY,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,sBAAsB,EAAE,GAAG,EAAE;YAC9B,MAAM,IAAI,GAAG,wBAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACzB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC5B,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,IAAI,GAAG,wBAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAC7B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,SAAS,GAAG,CAAC,gCAAY,CAAC,GAAG,EAAE,gCAAY,CAAC,GAAG,EAAE,gCAAY,CAAC,IAAI,EAAE,gCAAY,CAAC,IAAI,CAAC,CAAC;YAE7F,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAC3B,MAAM,IAAI,GAAG,wBAAI,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;gBACvC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,IAAI,GAAG,wBAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YACvC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,gCAAY,CAAC,GAAG,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\network\\port.value-object.spec.ts"], "sourcesContent": ["import { Port, PortProtocol, PortClass, PortRiskLevel } from './port.value-object';\r\n\r\ndescribe('Port Value Object', () => {\r\n  describe('Creation and Validation', () => {\r\n    it('should create valid TCP port', () => {\r\n      const port = Port.tcp(80);\r\n      \r\n      expect(port.number).toBe(80);\r\n      expect(port.protocol).toBe(PortProtocol.TCP);\r\n      expect(port.isTCP()).toBe(true);\r\n      expect(port.isUDP()).toBe(false);\r\n    });\r\n\r\n    it('should create valid UDP port', () => {\r\n      const port = Port.udp(53);\r\n      \r\n      expect(port.number).toBe(53);\r\n      expect(port.protocol).toBe(PortProtocol.UDP);\r\n      expect(port.isUDP()).toBe(true);\r\n      expect(port.isTCP()).toBe(false);\r\n    });\r\n\r\n    it('should create port with create method', () => {\r\n      const port = Port.create(443, PortProtocol.TCP);\r\n      \r\n      expect(port.number).toBe(443);\r\n      expect(port.protocol).toBe(PortProtocol.TCP);\r\n    });\r\n\r\n    it('should create port from string format', () => {\r\n      const port = Port.fromString('tcp/80');\r\n      \r\n      expect(port.number).toBe(80);\r\n      expect(port.protocol).toBe(PortProtocol.TCP);\r\n    });\r\n\r\n    it('should validate port number range', () => {\r\n      expect(() => Port.tcp(-1)).toThrow('Port number must be between 0 and 65535');\r\n      expect(() => Port.tcp(65536)).toThrow('Port number must be between 0 and 65535');\r\n      expect(() => Port.tcp(1.5)).toThrow('Port number must be an integer');\r\n    });\r\n\r\n    it('should validate protocol', () => {\r\n      expect(() => Port.create(80, 'invalid' as PortProtocol)).toThrow('Invalid protocol: invalid');\r\n    });\r\n\r\n    it('should handle edge case port numbers', () => {\r\n      const port0 = Port.tcp(0);\r\n      const port65535 = Port.tcp(65535);\r\n      \r\n      expect(port0.number).toBe(0);\r\n      expect(port65535.number).toBe(65535);\r\n    });\r\n  });\r\n\r\n  describe('Port Classification', () => {\r\n    it('should classify well-known ports', () => {\r\n      const wellKnownPorts = [0, 80, 443, 1023];\r\n      \r\n      wellKnownPorts.forEach(portNum => {\r\n        const port = Port.tcp(portNum);\r\n        expect(port.getClassification()).toBe(PortClass.WELL_KNOWN);\r\n        expect(port.isWellKnown()).toBe(true);\r\n        expect(port.isRegistered()).toBe(false);\r\n        expect(port.isDynamic()).toBe(false);\r\n      });\r\n    });\r\n\r\n    it('should classify registered ports', () => {\r\n      const registeredPorts = [1024, 8080, 49151];\r\n      \r\n      registeredPorts.forEach(portNum => {\r\n        const port = Port.tcp(portNum);\r\n        expect(port.getClassification()).toBe(PortClass.REGISTERED);\r\n        expect(port.isRegistered()).toBe(true);\r\n        expect(port.isWellKnown()).toBe(false);\r\n        expect(port.isDynamic()).toBe(false);\r\n      });\r\n    });\r\n\r\n    it('should classify dynamic ports', () => {\r\n      const dynamicPorts = [49152, 60000, 65535];\r\n      \r\n      dynamicPorts.forEach(portNum => {\r\n        const port = Port.tcp(portNum);\r\n        expect(port.getClassification()).toBe(PortClass.DYNAMIC);\r\n        expect(port.isDynamic()).toBe(true);\r\n        expect(port.isWellKnown()).toBe(false);\r\n        expect(port.isRegistered()).toBe(false);\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Service Identification', () => {\r\n    it('should identify common TCP services', () => {\r\n      const services = [\r\n        { port: 22, service: 'SSH' },\r\n        { port: 80, service: 'HTTP' },\r\n        { port: 443, service: 'HTTPS' },\r\n        { port: 25, service: 'SMTP' },\r\n        { port: 21, service: 'FTP Control' },\r\n      ];\r\n\r\n      services.forEach(({ port: portNum, service }) => {\r\n        const port = Port.tcp(portNum);\r\n        expect(port.getServiceName()).toBe(service);\r\n      });\r\n    });\r\n\r\n    it('should identify common UDP services', () => {\r\n      const services = [\r\n        { port: 53, service: 'DNS' },\r\n        { port: 123, service: 'NTP' },\r\n        { port: 161, service: 'SNMP' },\r\n      ];\r\n\r\n      services.forEach(({ port: portNum, service }) => {\r\n        const port = Port.udp(portNum);\r\n        expect(port.getServiceName()).toBe(service);\r\n      });\r\n    });\r\n\r\n    it('should return null for unknown services', () => {\r\n      const port = Port.tcp(12345);\r\n      expect(port.getServiceName()).toBeNull();\r\n    });\r\n  });\r\n\r\n  describe('Security Risk Assessment', () => {\r\n    it('should identify critical risk ports', () => {\r\n      const criticalPorts = [\r\n        { port: 23, protocol: PortProtocol.TCP }, // Telnet\r\n        { port: 21, protocol: PortProtocol.TCP }, // FTP\r\n        { port: 135, protocol: PortProtocol.TCP }, // RPC\r\n      ];\r\n\r\n      criticalPorts.forEach(({ port: portNum, protocol }) => {\r\n        const port = Port.create(portNum, protocol);\r\n        expect(port.getRiskLevel()).toBe(PortRiskLevel.CRITICAL);\r\n        expect(port.isHighRisk()).toBe(true);\r\n      });\r\n    });\r\n\r\n    it('should identify high risk ports', () => {\r\n      const highRiskPorts = [\r\n        { port: 22, protocol: PortProtocol.TCP }, // SSH\r\n        { port: 3389, protocol: PortProtocol.TCP }, // RDP\r\n        { port: 1433, protocol: PortProtocol.TCP }, // SQL Server\r\n      ];\r\n\r\n      highRiskPorts.forEach(({ port: portNum, protocol }) => {\r\n        const port = Port.create(portNum, protocol);\r\n        expect(port.getRiskLevel()).toBe(PortRiskLevel.HIGH);\r\n        expect(port.isHighRisk()).toBe(true);\r\n      });\r\n    });\r\n\r\n    it('should identify medium risk ports', () => {\r\n      const mediumRiskPorts = [\r\n        { port: 25, protocol: PortProtocol.TCP }, // SMTP\r\n        { port: 80, protocol: PortProtocol.TCP }, // HTTP\r\n        { port: 8080, protocol: PortProtocol.TCP }, // HTTP Alt\r\n      ];\r\n\r\n      mediumRiskPorts.forEach(({ port: portNum, protocol }) => {\r\n        const port = Port.create(portNum, protocol);\r\n        expect(port.getRiskLevel()).toBe(PortRiskLevel.MEDIUM);\r\n        expect(port.isHighRisk()).toBe(false);\r\n      });\r\n    });\r\n\r\n    it('should identify low risk ports', () => {\r\n      const port = Port.tcp(12345);\r\n      expect(port.getRiskLevel()).toBe(PortRiskLevel.LOW);\r\n      expect(port.isHighRisk()).toBe(false);\r\n    });\r\n\r\n    it('should identify commonly attacked ports', () => {\r\n      const commonTargets = [22, 80, 443, 3389, 1433];\r\n      \r\n      commonTargets.forEach(portNum => {\r\n        const port = Port.tcp(portNum);\r\n        expect(port.isCommonlyAttacked()).toBe(true);\r\n      });\r\n    });\r\n\r\n    it('should identify ports that should be monitored', () => {\r\n      const monitoredPorts = [22, 80, 443, 23, 21]; // Mix of high-risk and well-known\r\n      \r\n      monitoredPorts.forEach(portNum => {\r\n        const port = Port.tcp(portNum);\r\n        expect(port.shouldMonitor()).toBe(true);\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Security Recommendations', () => {\r\n    it('should provide SSH security recommendations', () => {\r\n      const port = Port.tcp(22);\r\n      const recommendations = port.getSecurityRecommendations();\r\n      \r\n      expect(recommendations).toContain('Use key-based authentication');\r\n      expect(recommendations).toContain('Disable root login');\r\n      expect(recommendations).toContain('Change default port if possible');\r\n    });\r\n\r\n    it('should provide Telnet security recommendations', () => {\r\n      const port = Port.tcp(23);\r\n      const recommendations = port.getSecurityRecommendations();\r\n      \r\n      expect(recommendations).toContain('Replace with SSH for secure remote access');\r\n      expect(recommendations).toContain('Consider disabling this service if not required');\r\n    });\r\n\r\n    it('should provide HTTP security recommendations', () => {\r\n      const port = Port.tcp(80);\r\n      const recommendations = port.getSecurityRecommendations();\r\n      \r\n      expect(recommendations).toContain('Redirect to HTTPS');\r\n    });\r\n\r\n    it('should provide RDP security recommendations', () => {\r\n      const port = Port.tcp(3389);\r\n      const recommendations = port.getSecurityRecommendations();\r\n      \r\n      expect(recommendations).toContain('Enable Network Level Authentication');\r\n      expect(recommendations).toContain('Use VPN for remote access');\r\n    });\r\n\r\n    it('should provide general recommendations for commonly attacked ports', () => {\r\n      const port = Port.tcp(22);\r\n      const recommendations = port.getSecurityRecommendations();\r\n      \r\n      expect(recommendations).toContain('Implement fail2ban or similar protection');\r\n      expect(recommendations).toContain('Use non-standard ports when possible');\r\n    });\r\n  });\r\n\r\n  describe('Encryption Detection', () => {\r\n    it('should identify encrypted ports', () => {\r\n      const encryptedPorts = [22, 443, 465, 587, 993, 995];\r\n      \r\n      encryptedPorts.forEach(portNum => {\r\n        const port = Port.tcp(portNum);\r\n        expect(port.isEncrypted()).toBe(true);\r\n      });\r\n    });\r\n\r\n    it('should identify unencrypted ports', () => {\r\n      const unencryptedPorts = [21, 23, 25, 80, 110, 143];\r\n      \r\n      unencryptedPorts.forEach(portNum => {\r\n        const port = Port.tcp(portNum);\r\n        expect(port.isEncrypted()).toBe(false);\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('String Parsing and Formatting', () => {\r\n    it('should parse protocol/port format', () => {\r\n      const testCases = [\r\n        { input: 'tcp/80', expectedPort: 80, expectedProtocol: PortProtocol.TCP },\r\n        { input: 'udp/53', expectedPort: 53, expectedProtocol: PortProtocol.UDP },\r\n        { input: 'TCP/443', expectedPort: 443, expectedProtocol: PortProtocol.TCP },\r\n      ];\r\n\r\n      testCases.forEach(({ input, expectedPort, expectedProtocol }) => {\r\n        const port = Port.fromString(input);\r\n        expect(port.number).toBe(expectedPort);\r\n        expect(port.protocol).toBe(expectedProtocol);\r\n      });\r\n    });\r\n\r\n    it('should handle invalid string formats', () => {\r\n      expect(() => Port.fromString('invalid')).toThrow('Port string must be in format \"protocol/port\"');\r\n      expect(() => Port.fromString('tcp/invalid')).toThrow();\r\n      expect(() => Port.fromString('invalid/80')).toThrow('Invalid protocol: invalid');\r\n    });\r\n\r\n    it('should parse port numbers with fallback to TCP', () => {\r\n      const port = Port.parse('80');\r\n      expect(port?.number).toBe(80);\r\n      expect(port?.protocol).toBe(PortProtocol.TCP);\r\n    });\r\n\r\n    it('should return null for invalid parse input', () => {\r\n      expect(Port.parse('invalid')).toBeNull();\r\n      expect(Port.parse('')).toBeNull();\r\n    });\r\n\r\n    it('should format port as string', () => {\r\n      const port = Port.tcp(80);\r\n      expect(port.toString()).toBe('tcp/80');\r\n    });\r\n  });\r\n\r\n  describe('Port Information', () => {\r\n    it('should provide comprehensive port information', () => {\r\n      const port = Port.tcp(22);\r\n      const info = port.getPortInfo();\r\n      \r\n      expect(info.number).toBe(22);\r\n      expect(info.protocol).toBe('tcp');\r\n      expect(info.classification).toBe(PortClass.WELL_KNOWN);\r\n      expect(info.serviceName).toBe('SSH');\r\n      expect(info.riskLevel).toBe(PortRiskLevel.HIGH);\r\n      expect(info.isEncrypted).toBe(true);\r\n      expect(info.isCommonlyAttacked).toBe(true);\r\n      expect(info.recommendations).toBeInstanceOf(Array);\r\n      expect(info.recommendations.length).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should get range category', () => {\r\n      expect(Port.tcp(0).getRangeCategory()).toBe('Reserved');\r\n      expect(Port.tcp(80).getRangeCategory()).toBe('System/Well-Known');\r\n      expect(Port.tcp(8080).getRangeCategory()).toBe('User/Registered');\r\n      expect(Port.tcp(60000).getRangeCategory()).toBe('Dynamic/Private');\r\n    });\r\n  });\r\n\r\n  describe('Equality and Comparison', () => {\r\n    it('should compare ports for equality', () => {\r\n      const port1 = Port.tcp(80);\r\n      const port2 = Port.tcp(80);\r\n      const port3 = Port.tcp(443);\r\n      const port4 = Port.udp(80);\r\n      \r\n      expect(port1.equals(port2)).toBe(true);\r\n      expect(port1.equals(port3)).toBe(false);\r\n      expect(port1.equals(port4)).toBe(false);\r\n      expect(port1.equals(undefined)).toBe(false);\r\n    });\r\n\r\n    it('should handle same instance comparison', () => {\r\n      const port = Port.tcp(80);\r\n      expect(port.equals(port)).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('JSON Serialization', () => {\r\n    it('should serialize to JSON', () => {\r\n      const port = Port.tcp(22);\r\n      const json = port.toJSON();\r\n      \r\n      expect(json.number).toBe(22);\r\n      expect(json.protocol).toBe('tcp');\r\n      expect(json.string).toBe('tcp/22');\r\n      expect(json.serviceName).toBe('SSH');\r\n      expect(json.riskLevel).toBe(PortRiskLevel.HIGH);\r\n    });\r\n\r\n    it('should deserialize from JSON', () => {\r\n      const json = { number: 443, protocol: PortProtocol.TCP };\r\n      const port = Port.fromJSON(json);\r\n      \r\n      expect(port.number).toBe(443);\r\n      expect(port.protocol).toBe(PortProtocol.TCP);\r\n    });\r\n  });\r\n\r\n  describe('Validation Methods', () => {\r\n    it('should validate port without creating instance', () => {\r\n      expect(Port.isValid(80, PortProtocol.TCP)).toBe(true);\r\n      expect(Port.isValid(65536, PortProtocol.TCP)).toBe(false);\r\n      expect(Port.isValid(-1, PortProtocol.TCP)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('Edge Cases', () => {\r\n    it('should handle port 0', () => {\r\n      const port = Port.tcp(0);\r\n      expect(port.number).toBe(0);\r\n      expect(port.getRangeCategory()).toBe('Reserved');\r\n    });\r\n\r\n    it('should handle maximum port number', () => {\r\n      const port = Port.tcp(65535);\r\n      expect(port.number).toBe(65535);\r\n      expect(port.isDynamic()).toBe(true);\r\n    });\r\n\r\n    it('should handle all protocol types', () => {\r\n      const protocols = [PortProtocol.TCP, PortProtocol.UDP, PortProtocol.SCTP, PortProtocol.DCCP];\r\n      \r\n      protocols.forEach(protocol => {\r\n        const port = Port.create(80, protocol);\r\n        expect(port.protocol).toBe(protocol);\r\n      });\r\n    });\r\n\r\n    it('should handle case-insensitive protocol parsing', () => {\r\n      const port = Port.fromString('TCP/80');\r\n      expect(port.protocol).toBe(PortProtocol.TCP);\r\n    });\r\n  });\r\n});"], "version": 3}