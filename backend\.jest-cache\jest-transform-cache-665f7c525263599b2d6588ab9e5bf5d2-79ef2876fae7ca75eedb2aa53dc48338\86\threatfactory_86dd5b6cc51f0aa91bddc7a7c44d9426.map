{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\threat.factory.ts", "mappings": ";;;AAAA,oEAA0H;AAE1H,wEAA+D;AAC/D,0FAA+G;AAmH/G;;;;;;;;;;;;GAYG;AACH,MAAa,aAAa;IACxB;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,OAA4B;QACxC,OAAO,sBAAM,CAAC,MAAM,CAClB,OAAO,CAAC,IAAI,EACZ,OAAO,CAAC,WAAW,EACnB,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,IAAI,EACZ,OAAO,CAAC,UAAU,EAClB;YACE,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,aAAa,EAAE,OAAO,CAAC,aAAa;YACpC,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,cAAc,EAAE,OAAO,CAAC,cAAc;YACtC,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,UAAU,EAAE,OAAO,CAAC,UAAU;SAC/B,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAC3B,UAAkC,EAClC,OAAsC;QAEtC,MAAM,QAAQ,GAAG,aAAa,CAAC,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QACtE,MAAM,UAAU,GAAG,UAAU,CAAC,UAAU,IAAI,aAAa,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;QAEtF,4BAA4B;QAC5B,MAAM,UAAU,GAAU,EAAE,CAAC;QAC7B,IAAI,UAAU,CAAC,SAAS,EAAE,CAAC;YACzB,MAAM,OAAO,GAAG,aAAa,CAAC,YAAY,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;YACrE,MAAM,WAAW,GAAG,aAAa,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YAC3D,MAAM,aAAa,GAAG,aAAa,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;YAEjE,UAAU,CAAC,IAAI,CAAC,sBAAG,CAAC,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,SAAS,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC;QACzF,CAAC;QAED,iCAAiC;QACjC,MAAM,WAAW,GAAG,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC;YAC3C,KAAK,EAAE,UAAU,CAAC,WAAW,CAAC,KAAK,IAAI,SAAS;YAChD,UAAU,EAAE,UAAU;YACtB,OAAO,EAAE,EAAE;YACX,UAAU,EAAE,EAAE;YACd,YAAY,EAAE,EAAE;YAChB,SAAS,EAAE,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;SACpF,CAAC,CAAC,CAAC,SAAS,CAAC;QAEd,oCAAoC;QACpC,MAAM,aAAa,GAAG,UAAU,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC;YAC5D,IAAI,EAAE,UAAU,CAAC,WAAW,CAAC,aAAa;YAC1C,UAAU,EAAE,UAAU;YACtB,YAAY,EAAE,EAAE;YAChB,SAAS,EAAE,EAAE;YACb,WAAW,EAAE,EAAE;SAChB,CAAC,CAAC,CAAC,SAAS,CAAC;QAEd,MAAM,IAAI,GAAG,wBAAwB,UAAU,CAAC,SAAS,IAAI,gBAAgB,EAAE,CAAC;QAChF,MAAM,WAAW,GAAG,UAAU,CAAC,WAAW,IAAI,6CAA6C,UAAU,CAAC,MAAM,IAAI,SAAS,EAAE,CAAC;QAE5H,OAAO,aAAa,CAAC,MAAM,CAAC;YAC1B,IAAI;YACJ,WAAW;YACX,QAAQ;YACR,QAAQ,EAAE,qBAAqB;YAC/B,IAAI,EAAE,UAAU,CAAC,aAAa,IAAI,SAAS;YAC3C,UAAU;YACV,UAAU;YACV,WAAW;YACX,aAAa;YACb,IAAI,EAAE,CAAC,qBAAqB,EAAE,UAAU,CAAC;YACzC,UAAU,EAAE;gBACV,MAAM,EAAE,UAAU,CAAC,MAAM;gBACzB,YAAY,EAAE,UAAU,CAAC,QAAQ;aAClC;YACD,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CACxB,WAAgC,EAChC,OAAsC;QAEtC,MAAM,QAAQ,GAAG,aAAa,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QACjE,MAAM,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC;QAE1C,kBAAkB;QAClB,MAAM,UAAU,GAAU,EAAE,CAAC;QAC7B,MAAM,OAAO,GAAG,0BAAO,CAAC,SAAS,CAAC,CAAC,gCAAgC;QAEnE,UAAU,CAAC,IAAI,CAAC,sBAAG,CAAC,MAAM,CACxB,OAAO,EACP,WAAW,CAAC,IAAI,EAChB,aAAa,CAAC,cAAc,CAAC,QAAQ,CAAC,EACtC,aAAa,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAC3C,CAAC,CAAC;QAEH,yBAAyB;QACzB,IAAI,WAAW,CAAC,iBAAiB,EAAE,CAAC;YAClC,WAAW,CAAC,iBAAiB,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBAChD,MAAM,OAAO,GAAG,aAAa,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;gBAC/D,UAAU,CAAC,IAAI,CAAC,sBAAG,CAAC,MAAM,CACxB,OAAO,EACP,SAAS,EACT,aAAa,CAAC,cAAc,CAAC,QAAQ,CAAC,EACtC,aAAa,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAC3C,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC;QAED,uBAAuB;QACvB,MAAM,aAAa,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;YACzC,IAAI,EAAE,WAAW,CAAC,MAAM;YACxB,OAAO,EAAE,WAAW,CAAC,OAAO;YAC5B,UAAU,EAAE,UAAU;YACtB,YAAY,EAAE,WAAW,CAAC,YAAY,IAAI,EAAE;YAC5C,SAAS,EAAE,WAAW,CAAC,SAAS,IAAI,EAAE;YACtC,WAAW,EAAE,WAAW,CAAC,WAAW,IAAI,EAAE;SAC3C,CAAC,CAAC,CAAC,SAAS,CAAC;QAEd,MAAM,IAAI,GAAG,YAAY,WAAW,CAAC,MAAM,IAAI,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAClF,MAAM,WAAW,GAAG,8BAA8B,WAAW,CAAC,IAAI,EAAE,CAAC;QAErE,OAAO,aAAa,CAAC,MAAM,CAAC;YAC1B,IAAI;YACJ,WAAW;YACX,QAAQ;YACR,QAAQ,EAAE,SAAS;YACnB,IAAI,EAAE,WAAW,CAAC,MAAM,IAAI,iBAAiB;YAC7C,UAAU;YACV,UAAU;YACV,aAAa;YACb,cAAc,EAAE,WAAW,CAAC,aAAa;YACzC,IAAI,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC;YAC7B,UAAU,EAAE;gBACV,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,QAAQ,EAAE,WAAW,CAAC,QAAQ;gBAC9B,YAAY,EAAE,WAAW;aAC1B;YACD,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CACtB,WAA8B,EAC9B,OAAsC;QAEtC,MAAM,QAAQ,GAAG,aAAa,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAC;QACvE,MAAM,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC;QAE1C,yBAAyB;QACzB,MAAM,UAAU,GAAsB,CAAC;gBACrC,EAAE,EAAE,WAAW,CAAC,WAAW,IAAI,SAAS;gBACxC,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,MAAM,EAAE,WAAW,CAAC,MAAM;gBAC1B,WAAW,EAAE,WAAW,CAAC,WAAW;gBACpC,UAAU,EAAE,UAAU;gBACtB,QAAQ,EAAE,WAAW,CAAC,QAAQ;aAC/B,CAAC,CAAC;QAEH,MAAM,IAAI,GAAG,mBAAmB,WAAW,CAAC,IAAI,EAAE,CAAC;QACnD,MAAM,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;QAE5C,OAAO,aAAa,CAAC,MAAM,CAAC;YAC1B,IAAI;YACJ,WAAW;YACX,QAAQ;YACR,QAAQ,EAAE,gBAAgB;YAC1B,IAAI,EAAE,WAAW,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;YAC3D,UAAU;YACV,UAAU;YACV,cAAc,EAAE,WAAW,CAAC,cAAc;YAC1C,IAAI,EAAE,CAAC,gBAAgB,EAAE,cAAc,CAAC;YACxC,UAAU,EAAE;gBACV,WAAW,EAAE,WAAW,CAAC,WAAW;gBACpC,MAAM,EAAE,WAAW,CAAC,MAAM;gBAC1B,QAAQ,EAAE,WAAW,CAAC,QAAQ;aAC/B;YACD,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,uBAAuB,CAC5B,IAAY,EACZ,WAAmB,EACnB,QAAgB,EAChB,IAAY,EACZ,OAAsC;QAEtC,OAAO,aAAa,CAAC,MAAM,CAAC;YAC1B,IAAI;YACJ,WAAW;YACX,QAAQ,EAAE,qCAAc,CAAC,IAAI;YAC7B,QAAQ;YACR,IAAI;YACJ,UAAU,EAAE,EAAE,EAAE,qCAAqC;YACrD,IAAI,EAAE,CAAC,eAAe,EAAE,OAAO,CAAC;YAChC,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CACxB,IAAY,EACZ,WAAmB,EACnB,QAAgB,EAChB,IAAY,EACZ,OAAsC;QAEtC,OAAO,aAAa,CAAC,MAAM,CAAC;YAC1B,IAAI;YACJ,WAAW;YACX,QAAQ,EAAE,qCAAc,CAAC,QAAQ;YACjC,QAAQ;YACR,IAAI;YACJ,UAAU,EAAE,EAAE,EAAE,8BAA8B;YAC9C,IAAI,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,oBAAoB,CAAC;YACjD,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CACzB,gBAAuB,EAAE,0CAA0C;IACnE,IAAY,EACZ,WAAmB,EACnB,OAAsC;QAEtC,wCAAwC;QACxC,MAAM,QAAQ,GAAG,aAAa,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;QACzE,MAAM,UAAU,GAAG,aAAa,CAAC,8BAA8B,CAAC,gBAAgB,CAAC,CAAC;QAElF,sCAAsC;QACtC,MAAM,cAAc,GAAG,aAAa,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,CAAC;QAE7E,0CAA0C;QAC1C,MAAM,UAAU,GAAG,aAAa,CAAC,4BAA4B,CAAC,gBAAgB,CAAC,CAAC;QAEhF,OAAO,aAAa,CAAC,MAAM,CAAC;YAC1B,IAAI;YACJ,WAAW;YACX,QAAQ;YACR,QAAQ,EAAE,mBAAmB;YAC7B,IAAI,EAAE,oBAAoB;YAC1B,UAAU;YACV,UAAU;YACV,cAAc;YACd,IAAI,EAAE,CAAC,YAAY,EAAE,aAAa,CAAC;YACnC,UAAU,EAAE;gBACV,oBAAoB,EAAE,gBAAgB,CAAC,MAAM;gBAC7C,oBAAoB,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aAC/C;YACD,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB,CACvB,IAAY,EACZ,WAAmB,EACnB,QAAwB,EACxB,QAAgB,EAChB,IAAY,EACZ,UAAkB,EAClB,SAAiB,EACjB,OAAsC;QAEtC,OAAO,aAAa,CAAC,MAAM,CAAC;YAC1B,IAAI;YACJ,WAAW;YACX,QAAQ;YACR,QAAQ;YACR,IAAI;YACJ,UAAU;YACV,IAAI,EAAE,CAAC,QAAQ,EAAE,iBAAiB,CAAC;YACnC,UAAU,EAAE;gBACV,SAAS;gBACT,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,MAAM,EAAE,iBAAiB;aAC1B;YACD,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED,gDAAgD;IAExC,MAAM,CAAC,iBAAiB,CAAC,QAAyB;QACxD,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACjC,MAAM,GAAG,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;YACnC,IAAI,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC;gBAAE,OAAO,qCAAc,CAAC,QAAQ,CAAC;YACtF,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAAE,OAAO,qCAAc,CAAC,IAAI,CAAC;YAC/E,IAAI,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC;gBAAE,OAAO,qCAAc,CAAC,MAAM,CAAC;YACrF,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC;gBAAE,OAAO,qCAAc,CAAC,GAAG,CAAC;QAC9E,CAAC;QAED,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACjC,IAAI,QAAQ,IAAI,CAAC;gBAAE,OAAO,qCAAc,CAAC,QAAQ,CAAC;YAClD,IAAI,QAAQ,IAAI,CAAC;gBAAE,OAAO,qCAAc,CAAC,IAAI,CAAC;YAC9C,IAAI,QAAQ,IAAI,CAAC;gBAAE,OAAO,qCAAc,CAAC,MAAM,CAAC;YAChD,IAAI,QAAQ,IAAI,CAAC;gBAAE,OAAO,qCAAc,CAAC,GAAG,CAAC;QAC/C,CAAC;QAED,OAAO,qCAAc,CAAC,OAAO,CAAC;IAChC,CAAC;IAEO,MAAM,CAAC,eAAe,CAAC,UAAkC;QAC/D,IAAI,UAAU,GAAG,EAAE,CAAC,CAAC,kBAAkB;QAEvC,4CAA4C;QAC5C,IAAI,UAAU,CAAC,MAAM;YAAE,UAAU,IAAI,EAAE,CAAC;QACxC,IAAI,UAAU,CAAC,WAAW,EAAE,KAAK;YAAE,UAAU,IAAI,EAAE,CAAC;QACpD,IAAI,UAAU,CAAC,WAAW,EAAE,QAAQ;YAAE,UAAU,IAAI,EAAE,CAAC;QACvD,IAAI,UAAU,CAAC,WAAW,IAAI,UAAU,CAAC,WAAW,CAAC,MAAM,GAAG,EAAE;YAAE,UAAU,IAAI,EAAE,CAAC;QAEnF,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;IACnC,CAAC;IAEO,MAAM,CAAC,YAAY,CAAC,aAAqB;QAC/C,MAAM,IAAI,GAAG,aAAa,CAAC,WAAW,EAAE,CAAC;QAEzC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,0BAAO,CAAC,UAAU,CAAC;QAC/E,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;YAAE,OAAO,0BAAO,CAAC,MAAM,CAAC;QAChF,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE,OAAO,0BAAO,CAAC,GAAG,CAAC;QACrE,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,0BAAO,CAAC,KAAK,CAAC;QACjD,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE,OAAO,0BAAO,CAAC,SAAS,CAAC;QACpG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;YAAE,OAAO,0BAAO,CAAC,SAAS,CAAC;QAEjF,OAAO,0BAAO,CAAC,UAAU,CAAC,CAAC,mBAAmB;IAChD,CAAC;IAEO,MAAM,CAAC,qBAAqB,CAAC,KAAa;QAChD,qBAAqB;QACrB,IAAI,sCAAsC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACvD,OAAO,0BAAO,CAAC,UAAU,CAAC;QAC5B,CAAC;QAED,iBAAiB;QACjB,IAAI,0DAA0D,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAC3E,OAAO,0BAAO,CAAC,MAAM,CAAC;QACxB,CAAC;QAED,cAAc;QACd,IAAI,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAC/B,OAAO,0BAAO,CAAC,GAAG,CAAC;QACrB,CAAC;QAED,gBAAgB;QAChB,IAAI,4BAA4B,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAC7C,OAAO,0BAAO,CAAC,KAAK,CAAC;QACvB,CAAC;QAED,oCAAoC;QACpC,IAAI,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC;YAAE,OAAO,0BAAO,CAAC,SAAS,CAAC;QAC9D,IAAI,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC;YAAE,OAAO,0BAAO,CAAC,SAAS,CAAC;QAC9D,IAAI,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC;YAAE,OAAO,0BAAO,CAAC,SAAS,CAAC;QAE9D,OAAO,0BAAO,CAAC,UAAU,CAAC,CAAC,mBAAmB;IAChD,CAAC;IAEO,MAAM,CAAC,cAAc,CAAC,cAA8B;QAC1D,QAAQ,cAAc,EAAE,CAAC;YACvB,KAAK,qCAAc,CAAC,QAAQ,CAAC,CAAC,OAAO,8BAAW,CAAC,QAAQ,CAAC;YAC1D,KAAK,qCAAc,CAAC,IAAI,CAAC,CAAC,OAAO,8BAAW,CAAC,IAAI,CAAC;YAClD,KAAK,qCAAc,CAAC,MAAM,CAAC,CAAC,OAAO,8BAAW,CAAC,MAAM,CAAC;YACtD,KAAK,qCAAc,CAAC,GAAG,CAAC,CAAC,OAAO,8BAAW,CAAC,GAAG,CAAC;YAChD,OAAO,CAAC,CAAC,OAAO,8BAAW,CAAC,GAAG,CAAC;QAClC,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,gBAAgB,CAAC,UAAkB;QAChD,IAAI,UAAU,IAAI,EAAE;YAAE,OAAO,gCAAa,CAAC,SAAS,CAAC;QACrD,IAAI,UAAU,IAAI,EAAE;YAAE,OAAO,gCAAa,CAAC,IAAI,CAAC;QAChD,IAAI,UAAU,IAAI,EAAE;YAAE,OAAO,gCAAa,CAAC,MAAM,CAAC;QAClD,OAAO,gCAAa,CAAC,GAAG,CAAC;IAC3B,CAAC;IAEO,MAAM,CAAC,oBAAoB,CAAC,WAAgC;QAClE,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,+BAA+B;QAC/B,IAAI,WAAW,CAAC,YAAY,EAAE,CAAC;YAC7B,KAAK,IAAI,WAAW,CAAC,YAAY,CAAC,MAAM,GAAG,EAAE,CAAC;QAChD,CAAC;QAED,4CAA4C;QAC5C,IAAI,WAAW,CAAC,WAAW,IAAI,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClE,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,2CAA2C;QAC3C,IAAI,WAAW,CAAC,SAAS,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9D,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,6CAA6C;QAC7C,IAAI,WAAW,CAAC,aAAa,IAAI,WAAW,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtE,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,qCAAc,CAAC,QAAQ,CAAC;QAChD,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,qCAAc,CAAC,IAAI,CAAC;QAC5C,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,qCAAc,CAAC,MAAM,CAAC;QAC9C,OAAO,qCAAc,CAAC,GAAG,CAAC;IAC5B,CAAC;IAEO,MAAM,CAAC,0BAA0B,CAAC,WAA8B;QACtE,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QAEhD,sBAAsB;QACtB,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YACjE,OAAO,qCAAc,CAAC,QAAQ,CAAC;QACjC,CAAC;QAED,6BAA6B;QAC7B,IAAI,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;YACjG,OAAO,qCAAc,CAAC,IAAI,CAAC;QAC7B,CAAC;QAED,wBAAwB;QACxB,IAAI,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAC/D,OAAO,qCAAc,CAAC,MAAM,CAAC;QAC/B,CAAC;QAED,uBAAuB;QACvB,OAAO,qCAAc,CAAC,GAAG,CAAC;IAC5B,CAAC;IAEO,MAAM,CAAC,uBAAuB,CAAC,MAAa;QAClD,kEAAkE;QAClE,IAAI,MAAM,CAAC,MAAM,IAAI,EAAE;YAAE,OAAO,qCAAc,CAAC,QAAQ,CAAC;QACxD,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC;YAAE,OAAO,qCAAc,CAAC,IAAI,CAAC;QACnD,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC;YAAE,OAAO,qCAAc,CAAC,MAAM,CAAC;QACrD,OAAO,qCAAc,CAAC,GAAG,CAAC;IAC5B,CAAC;IAEO,MAAM,CAAC,8BAA8B,CAAC,MAAa;QACzD,wCAAwC;QACxC,IAAI,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;QAElD,yCAAyC;QACzC,UAAU,IAAI,EAAE,CAAC;QAEjB,wCAAwC;QACxC,UAAU,IAAI,EAAE,CAAC;QAEjB,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;IACnC,CAAC;IAEO,MAAM,CAAC,qBAAqB,CAAC,MAAa;QAChD,uDAAuD;QACvD,OAAO,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;IACpC,CAAC;IAEO,MAAM,CAAC,4BAA4B,CAAC,MAAa;QACvD,wDAAwD;QACxD,OAAO,CAAC;gBACN,EAAE,EAAE,OAAO;gBACX,IAAI,EAAE,oBAAoB;gBAC1B,MAAM,EAAE,UAAU;gBAClB,WAAW,EAAE,+BAA+B,MAAM,CAAC,MAAM,SAAS;gBAClE,UAAU,EAAE,EAAE;gBACd,QAAQ,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,oBAAoB,CAAC;aACjD,CAAC,CAAC;IACL,CAAC;CACF;AAveD,sCAueC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\threat.factory.ts"], "sourcesContent": ["import { Threat, ThreatProps, ThreatAttribution, MalwareFamily, AttackTechnique } from '../entities/threat/threat.entity';\r\nimport { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { ThreatSeverity } from '../enums/threat-severity.enum';\r\nimport { IOC, IOCType, IOCSeverity, IOCConfidence } from '../value-objects/threat-indicators/ioc.value-object';\r\nimport { CVSSScore } from '../value-objects/threat-indicators/cvss-score.value-object';\r\nimport { EventType } from '../enums/event-type.enum';\r\nimport { EventSeverity } from '../enums/event-severity.enum';\r\n\r\n/**\r\n * Threat Creation Options\r\n */\r\nexport interface CreateThreatOptions {\r\n  /** Threat ID (optional, will be generated if not provided) */\r\n  id?: UniqueEntityId;\r\n  /** Threat name/title */\r\n  name: string;\r\n  /** Threat description */\r\n  description: string;\r\n  /** Threat severity level */\r\n  severity: ThreatSeverity;\r\n  /** Threat category */\r\n  category: string;\r\n  /** Threat subcategory (optional) */\r\n  subcategory?: string;\r\n  /** Threat type */\r\n  type: string;\r\n  /** Threat confidence score (0-100) */\r\n  confidence: number;\r\n  /** Associated indicators of compromise */\r\n  indicators?: IOC[];\r\n  /** CVSS scores if applicable */\r\n  cvssScores?: CVSSScore[];\r\n  /** Threat actor attribution */\r\n  attribution?: ThreatAttribution;\r\n  /** Malware family information */\r\n  malwareFamily?: MalwareFamily;\r\n  /** Attack techniques used */\r\n  techniques?: AttackTechnique[];\r\n  /** Affected assets */\r\n  affectedAssets?: string[];\r\n  /** Threat tags */\r\n  tags?: string[];\r\n  /** Custom attributes */\r\n  attributes?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Threat Intelligence Data\r\n */\r\nexport interface ThreatIntelligenceData {\r\n  /** Threat indicator */\r\n  indicator: string;\r\n  /** Indicator type */\r\n  indicatorType: string;\r\n  /** Threat severity */\r\n  severity: string | number;\r\n  /** Threat description */\r\n  description?: string;\r\n  /** Confidence level */\r\n  confidence?: number;\r\n  /** Threat source */\r\n  source?: string;\r\n  /** Attribution information */\r\n  attribution?: {\r\n    actor?: string;\r\n    campaign?: string;\r\n    malwareFamily?: string;\r\n  };\r\n  /** Additional metadata */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Malware Analysis Data\r\n */\r\nexport interface MalwareAnalysisData {\r\n  /** Malware hash */\r\n  hash: string;\r\n  /** Hash type (MD5, SHA1, SHA256) */\r\n  hashType: string;\r\n  /** Malware family */\r\n  family?: string;\r\n  /** Malware variant */\r\n  variant?: string;\r\n  /** Analysis confidence */\r\n  confidence: number;\r\n  /** Capabilities detected */\r\n  capabilities?: string[];\r\n  /** Communication protocols */\r\n  protocols?: string[];\r\n  /** Persistence mechanisms */\r\n  persistence?: string[];\r\n  /** Affected files */\r\n  affectedFiles?: string[];\r\n  /** Network indicators */\r\n  networkIndicators?: string[];\r\n}\r\n\r\n/**\r\n * Attack Pattern Data\r\n */\r\nexport interface AttackPatternData {\r\n  /** Pattern name */\r\n  name: string;\r\n  /** MITRE ATT&CK technique ID */\r\n  techniqueId?: string;\r\n  /** Tactic */\r\n  tactic: string;\r\n  /** Pattern description */\r\n  description: string;\r\n  /** Detection confidence */\r\n  confidence: number;\r\n  /** Evidence supporting this pattern */\r\n  evidence: string[];\r\n  /** Affected assets */\r\n  affectedAssets?: string[];\r\n}\r\n\r\n/**\r\n * Threat Factory\r\n * \r\n * Factory class for creating Threat entities with proper validation and defaults.\r\n * Handles complex threat creation scenarios and ensures all business rules are applied.\r\n * \r\n * Key responsibilities:\r\n * - Create threats from various input formats\r\n * - Apply default values and business rules\r\n * - Validate threat data consistency\r\n * - Generate proper risk assessments\r\n * - Handle threat relationships and correlations\r\n */\r\nexport class ThreatFactory {\r\n  /**\r\n   * Create a new Threat with the provided options\r\n   */\r\n  static create(options: CreateThreatOptions): Threat {\r\n    return Threat.create(\r\n      options.name,\r\n      options.description,\r\n      options.severity,\r\n      options.category,\r\n      options.type,\r\n      options.confidence,\r\n      {\r\n        subcategory: options.subcategory,\r\n        indicators: options.indicators,\r\n        cvssScores: options.cvssScores,\r\n        attribution: options.attribution,\r\n        malwareFamily: options.malwareFamily,\r\n        techniques: options.techniques,\r\n        affectedAssets: options.affectedAssets,\r\n        tags: options.tags,\r\n        attributes: options.attributes,\r\n      }\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Create a Threat from threat intelligence data\r\n   */\r\n  static fromThreatIntelligence(\r\n    threatData: ThreatIntelligenceData,\r\n    options?: Partial<CreateThreatOptions>\r\n  ): Threat {\r\n    const severity = ThreatFactory.mapThreatSeverity(threatData.severity);\r\n    const confidence = threatData.confidence || ThreatFactory.inferConfidence(threatData);\r\n    \r\n    // Create IOC from indicator\r\n    const indicators: IOC[] = [];\r\n    if (threatData.indicator) {\r\n      const iocType = ThreatFactory.inferIOCType(threatData.indicatorType);\r\n      const iocSeverity = ThreatFactory.mapIOCSeverity(severity);\r\n      const iocConfidence = ThreatFactory.mapIOCConfidence(confidence);\r\n      \r\n      indicators.push(IOC.create(iocType, threatData.indicator, iocSeverity, iocConfidence));\r\n    }\r\n\r\n    // Build attribution if available\r\n    const attribution = threatData.attribution ? {\r\n      actor: threatData.attribution.actor || 'Unknown',\r\n      confidence: confidence,\r\n      aliases: [],\r\n      motivation: [],\r\n      capabilities: [],\r\n      campaigns: threatData.attribution.campaign ? [threatData.attribution.campaign] : [],\r\n    } : undefined;\r\n\r\n    // Build malware family if available\r\n    const malwareFamily = threatData.attribution?.malwareFamily ? {\r\n      name: threatData.attribution.malwareFamily,\r\n      confidence: confidence,\r\n      capabilities: [],\r\n      protocols: [],\r\n      persistence: [],\r\n    } : undefined;\r\n\r\n    const name = `Threat Intelligence: ${threatData.indicator || 'Unknown Threat'}`;\r\n    const description = threatData.description || `Threat detected from intelligence source: ${threatData.source || 'Unknown'}`;\r\n\r\n    return ThreatFactory.create({\r\n      name,\r\n      description,\r\n      severity,\r\n      category: 'threat-intelligence',\r\n      type: threatData.indicatorType || 'unknown',\r\n      confidence,\r\n      indicators,\r\n      attribution,\r\n      malwareFamily,\r\n      tags: ['threat-intelligence', 'external'],\r\n      attributes: {\r\n        source: threatData.source,\r\n        originalData: threatData.metadata,\r\n      },\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a Threat from malware analysis data\r\n   */\r\n  static fromMalwareAnalysis(\r\n    malwareData: MalwareAnalysisData,\r\n    options?: Partial<CreateThreatOptions>\r\n  ): Threat {\r\n    const severity = ThreatFactory.inferMalwareSeverity(malwareData);\r\n    const confidence = malwareData.confidence;\r\n\r\n    // Create hash IOC\r\n    const indicators: IOC[] = [];\r\n    const iocType = IOCType.FILE_HASH; // All hashes use FILE_HASH type\r\n    \r\n    indicators.push(IOC.create(\r\n      iocType, \r\n      malwareData.hash, \r\n      ThreatFactory.mapIOCSeverity(severity),\r\n      ThreatFactory.mapIOCConfidence(confidence)\r\n    ));\r\n\r\n    // Add network indicators\r\n    if (malwareData.networkIndicators) {\r\n      malwareData.networkIndicators.forEach(indicator => {\r\n        const iocType = ThreatFactory.inferIOCTypeFromValue(indicator);\r\n        indicators.push(IOC.create(\r\n          iocType,\r\n          indicator,\r\n          ThreatFactory.mapIOCSeverity(severity),\r\n          ThreatFactory.mapIOCConfidence(confidence)\r\n        ));\r\n      });\r\n    }\r\n\r\n    // Build malware family\r\n    const malwareFamily = malwareData.family ? {\r\n      name: malwareData.family,\r\n      variant: malwareData.variant,\r\n      confidence: confidence,\r\n      capabilities: malwareData.capabilities || [],\r\n      protocols: malwareData.protocols || [],\r\n      persistence: malwareData.persistence || [],\r\n    } : undefined;\r\n\r\n    const name = `Malware: ${malwareData.family || malwareData.hash.substring(0, 8)}`;\r\n    const description = `Malware detected with hash ${malwareData.hash}`;\r\n\r\n    return ThreatFactory.create({\r\n      name,\r\n      description,\r\n      severity,\r\n      category: 'malware',\r\n      type: malwareData.family || 'unknown-malware',\r\n      confidence,\r\n      indicators,\r\n      malwareFamily,\r\n      affectedAssets: malwareData.affectedFiles,\r\n      tags: ['malware', 'analysis'],\r\n      attributes: {\r\n        hash: malwareData.hash,\r\n        hashType: malwareData.hashType,\r\n        analysisData: malwareData,\r\n      },\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a Threat from attack pattern data\r\n   */\r\n  static fromAttackPattern(\r\n    patternData: AttackPatternData,\r\n    options?: Partial<CreateThreatOptions>\r\n  ): Threat {\r\n    const severity = ThreatFactory.inferAttackPatternSeverity(patternData);\r\n    const confidence = patternData.confidence;\r\n\r\n    // Build attack technique\r\n    const techniques: AttackTechnique[] = [{\r\n      id: patternData.techniqueId || 'UNKNOWN',\r\n      name: patternData.name,\r\n      tactic: patternData.tactic,\r\n      description: patternData.description,\r\n      confidence: confidence,\r\n      evidence: patternData.evidence,\r\n    }];\r\n\r\n    const name = `Attack Pattern: ${patternData.name}`;\r\n    const description = patternData.description;\r\n\r\n    return ThreatFactory.create({\r\n      name,\r\n      description,\r\n      severity,\r\n      category: 'attack-pattern',\r\n      type: patternData.tactic.toLowerCase().replace(/\\s+/g, '-'),\r\n      confidence,\r\n      techniques,\r\n      affectedAssets: patternData.affectedAssets,\r\n      tags: ['attack-pattern', 'mitre-attack'],\r\n      attributes: {\r\n        techniqueId: patternData.techniqueId,\r\n        tactic: patternData.tactic,\r\n        evidence: patternData.evidence,\r\n      },\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a high-severity threat alert\r\n   */\r\n  static createHighSeverityAlert(\r\n    name: string,\r\n    description: string,\r\n    category: string,\r\n    type: string,\r\n    options?: Partial<CreateThreatOptions>\r\n  ): Threat {\r\n    return ThreatFactory.create({\r\n      name,\r\n      description,\r\n      severity: ThreatSeverity.HIGH,\r\n      category,\r\n      type,\r\n      confidence: 85, // Default high confidence for alerts\r\n      tags: ['high-severity', 'alert'],\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a critical threat alert\r\n   */\r\n  static createCriticalAlert(\r\n    name: string,\r\n    description: string,\r\n    category: string,\r\n    type: string,\r\n    options?: Partial<CreateThreatOptions>\r\n  ): Threat {\r\n    return ThreatFactory.create({\r\n      name,\r\n      description,\r\n      severity: ThreatSeverity.CRITICAL,\r\n      category,\r\n      type,\r\n      confidence: 95, // Default critical confidence\r\n      tags: ['critical', 'alert', 'immediate-response'],\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a threat from security event correlation\r\n   */\r\n  static fromEventCorrelation(\r\n    correlatedEvents: any[], // Would be Event[] in real implementation\r\n    name: string,\r\n    description: string,\r\n    options?: Partial<CreateThreatOptions>\r\n  ): Threat {\r\n    // Infer severity from correlated events\r\n    const severity = ThreatFactory.inferSeverityFromEvents(correlatedEvents);\r\n    const confidence = ThreatFactory.calculateCorrelationConfidence(correlatedEvents);\r\n\r\n    // Extract affected assets from events\r\n    const affectedAssets = ThreatFactory.extractAffectedAssets(correlatedEvents);\r\n\r\n    // Generate techniques from event patterns\r\n    const techniques = ThreatFactory.generateTechniquesFromEvents(correlatedEvents);\r\n\r\n    return ThreatFactory.create({\r\n      name,\r\n      description,\r\n      severity,\r\n      category: 'correlated-threat',\r\n      type: 'multi-stage-attack',\r\n      confidence,\r\n      techniques,\r\n      affectedAssets,\r\n      tags: ['correlated', 'multi-stage'],\r\n      attributes: {\r\n        correlatedEventCount: correlatedEvents.length,\r\n        correlationTimestamp: new Date().toISOString(),\r\n      },\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a manual threat (created by analyst)\r\n   */\r\n  static createManualThreat(\r\n    name: string,\r\n    description: string,\r\n    severity: ThreatSeverity,\r\n    category: string,\r\n    type: string,\r\n    confidence: number,\r\n    createdBy: string,\r\n    options?: Partial<CreateThreatOptions>\r\n  ): Threat {\r\n    return ThreatFactory.create({\r\n      name,\r\n      description,\r\n      severity,\r\n      category,\r\n      type,\r\n      confidence,\r\n      tags: ['manual', 'analyst-created'],\r\n      attributes: {\r\n        createdBy,\r\n        createdAt: new Date().toISOString(),\r\n        source: 'manual-analysis',\r\n      },\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  // Helper methods for data mapping and inference\r\n\r\n  private static mapThreatSeverity(severity: string | number): ThreatSeverity {\r\n    if (typeof severity === 'string') {\r\n      const sev = severity.toLowerCase();\r\n      if (sev.includes('critical') || sev.includes('fatal')) return ThreatSeverity.CRITICAL;\r\n      if (sev.includes('high') || sev.includes('severe')) return ThreatSeverity.HIGH;\r\n      if (sev.includes('medium') || sev.includes('moderate')) return ThreatSeverity.MEDIUM;\r\n      if (sev.includes('low') || sev.includes('minor')) return ThreatSeverity.LOW;\r\n    }\r\n    \r\n    if (typeof severity === 'number') {\r\n      if (severity >= 9) return ThreatSeverity.CRITICAL;\r\n      if (severity >= 7) return ThreatSeverity.HIGH;\r\n      if (severity >= 4) return ThreatSeverity.MEDIUM;\r\n      if (severity >= 1) return ThreatSeverity.LOW;\r\n    }\r\n    \r\n    return ThreatSeverity.UNKNOWN;\r\n  }\r\n\r\n  private static inferConfidence(threatData: ThreatIntelligenceData): number {\r\n    let confidence = 50; // Base confidence\r\n    \r\n    // Increase confidence based on data quality\r\n    if (threatData.source) confidence += 10;\r\n    if (threatData.attribution?.actor) confidence += 15;\r\n    if (threatData.attribution?.campaign) confidence += 10;\r\n    if (threatData.description && threatData.description.length > 50) confidence += 10;\r\n    \r\n    return Math.min(100, confidence);\r\n  }\r\n\r\n  private static inferIOCType(indicatorType: string): IOCType {\r\n    const type = indicatorType.toLowerCase();\r\n    \r\n    if (type.includes('ip') || type.includes('address')) return IOCType.IP_ADDRESS;\r\n    if (type.includes('domain') || type.includes('hostname')) return IOCType.DOMAIN;\r\n    if (type.includes('url') || type.includes('uri')) return IOCType.URL;\r\n    if (type.includes('email')) return IOCType.EMAIL;\r\n    if (type.includes('hash') || type.includes('md5') || type.includes('sha')) return IOCType.FILE_HASH;\r\n    if (type.includes('file') || type.includes('filename')) return IOCType.FILE_PATH;\r\n    \r\n    return IOCType.IP_ADDRESS; // Default fallback\r\n  }\r\n\r\n  private static inferIOCTypeFromValue(value: string): IOCType {\r\n    // IP address pattern\r\n    if (/^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}$/.test(value)) {\r\n      return IOCType.IP_ADDRESS;\r\n    }\r\n    \r\n    // Domain pattern\r\n    if (/^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\\.[a-zA-Z]{2,}$/.test(value)) {\r\n      return IOCType.DOMAIN;\r\n    }\r\n    \r\n    // URL pattern\r\n    if (/^https?:\\/\\//.test(value)) {\r\n      return IOCType.URL;\r\n    }\r\n    \r\n    // Email pattern\r\n    if (/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(value)) {\r\n      return IOCType.EMAIL;\r\n    }\r\n    \r\n    // Hash patterns - all use FILE_HASH\r\n    if (/^[a-fA-F0-9]{32}$/.test(value)) return IOCType.FILE_HASH;\r\n    if (/^[a-fA-F0-9]{40}$/.test(value)) return IOCType.FILE_HASH;\r\n    if (/^[a-fA-F0-9]{64}$/.test(value)) return IOCType.FILE_HASH;\r\n    \r\n    return IOCType.IP_ADDRESS; // Default fallback\r\n  }\r\n\r\n  private static mapIOCSeverity(threatSeverity: ThreatSeverity): IOCSeverity {\r\n    switch (threatSeverity) {\r\n      case ThreatSeverity.CRITICAL: return IOCSeverity.CRITICAL;\r\n      case ThreatSeverity.HIGH: return IOCSeverity.HIGH;\r\n      case ThreatSeverity.MEDIUM: return IOCSeverity.MEDIUM;\r\n      case ThreatSeverity.LOW: return IOCSeverity.LOW;\r\n      default: return IOCSeverity.LOW;\r\n    }\r\n  }\r\n\r\n  private static mapIOCConfidence(confidence: number): IOCConfidence {\r\n    if (confidence >= 90) return IOCConfidence.CONFIRMED;\r\n    if (confidence >= 70) return IOCConfidence.HIGH;\r\n    if (confidence >= 50) return IOCConfidence.MEDIUM;\r\n    return IOCConfidence.LOW;\r\n  }\r\n\r\n  private static inferMalwareSeverity(malwareData: MalwareAnalysisData): ThreatSeverity {\r\n    let score = 0;\r\n    \r\n    // Base score from capabilities\r\n    if (malwareData.capabilities) {\r\n      score += malwareData.capabilities.length * 10;\r\n    }\r\n    \r\n    // Increase score for persistence mechanisms\r\n    if (malwareData.persistence && malwareData.persistence.length > 0) {\r\n      score += 20;\r\n    }\r\n    \r\n    // Increase score for network communication\r\n    if (malwareData.protocols && malwareData.protocols.length > 0) {\r\n      score += 15;\r\n    }\r\n    \r\n    // Increase score for multiple affected files\r\n    if (malwareData.affectedFiles && malwareData.affectedFiles.length > 5) {\r\n      score += 15;\r\n    }\r\n    \r\n    if (score >= 60) return ThreatSeverity.CRITICAL;\r\n    if (score >= 40) return ThreatSeverity.HIGH;\r\n    if (score >= 20) return ThreatSeverity.MEDIUM;\r\n    return ThreatSeverity.LOW;\r\n  }\r\n\r\n  private static inferAttackPatternSeverity(patternData: AttackPatternData): ThreatSeverity {\r\n    const tactic = patternData.tactic.toLowerCase();\r\n    \r\n    // High-impact tactics\r\n    if (tactic.includes('impact') || tactic.includes('exfiltration')) {\r\n      return ThreatSeverity.CRITICAL;\r\n    }\r\n    \r\n    // Medium-high impact tactics\r\n    if (tactic.includes('lateral') || tactic.includes('privilege') || tactic.includes('persistence')) {\r\n      return ThreatSeverity.HIGH;\r\n    }\r\n    \r\n    // Medium impact tactics\r\n    if (tactic.includes('execution') || tactic.includes('defense')) {\r\n      return ThreatSeverity.MEDIUM;\r\n    }\r\n    \r\n    // Lower impact tactics\r\n    return ThreatSeverity.LOW;\r\n  }\r\n\r\n  private static inferSeverityFromEvents(events: any[]): ThreatSeverity {\r\n    // This would analyze actual Event entities in real implementation\r\n    if (events.length >= 10) return ThreatSeverity.CRITICAL;\r\n    if (events.length >= 5) return ThreatSeverity.HIGH;\r\n    if (events.length >= 2) return ThreatSeverity.MEDIUM;\r\n    return ThreatSeverity.LOW;\r\n  }\r\n\r\n  private static calculateCorrelationConfidence(events: any[]): number {\r\n    // Base confidence from number of events\r\n    let confidence = Math.min(events.length * 10, 70);\r\n    \r\n    // Increase confidence for time proximity\r\n    confidence += 10;\r\n    \r\n    // Increase confidence for asset overlap\r\n    confidence += 10;\r\n    \r\n    return Math.min(100, confidence);\r\n  }\r\n\r\n  private static extractAffectedAssets(events: any[]): string[] {\r\n    // This would extract assets from actual Event entities\r\n    return [`asset-${events.length}`];\r\n  }\r\n\r\n  private static generateTechniquesFromEvents(events: any[]): AttackTechnique[] {\r\n    // This would analyze event patterns to infer techniques\r\n    return [{\r\n      id: 'T1000',\r\n      name: 'Multi-stage Attack',\r\n      tactic: 'Multiple',\r\n      description: `Correlated attack involving ${events.length} events`,\r\n      confidence: 80,\r\n      evidence: [`${events.length} correlated events`],\r\n    }];\r\n  }\r\n}"], "version": 3}