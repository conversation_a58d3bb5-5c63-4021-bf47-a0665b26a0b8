{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\recommendation-generated.domain-event.ts", "mappings": ";;;AAAA,6DAA4E;AAmH5E;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAa,kCAAmC,SAAQ,+BAAiD;IACvG,YACE,WAA2B,EAC3B,SAA2C,EAC3C,OAOC;QAED,KAAK,CAAC,WAAW,EAAE,SAAS,EAAE;YAC5B,YAAY,EAAE,CAAC;YACf,GAAG,OAAO;YACV,QAAQ,EAAE;gBACR,SAAS,EAAE,yBAAyB;gBACpC,MAAM,EAAE,UAAU;gBAClB,aAAa,EAAE,gBAAgB;gBAC/B,eAAe,EAAE,YAAY;gBAC7B,GAAG,OAAO,EAAE,QAAQ;aACrB;SACF,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAI,sBAAsB;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,IAAI,oBAAoB;QACtB,OAAO,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,IAAI,2BAA2B;QAC7B,OAAO,IAAI,CAAC,SAAS,CAAC,2BAA2B,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAI,oBAAoB;QACtB,OAAO,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,IAAI,qBAAqB;QACvB,OAAO,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,IAAI,sBAAsB;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,QAAQ,KAAK,UAAU,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,IAAI,CAAC,eAAe,IAAI,EAAE,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,OAAO,IAAI,CAAC,sBAAsB,IAAI,EAAE,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,IAAI,CAAC,oBAAoB,KAAK,KAAK,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO,IAAI,CAAC,oBAAoB,KAAK,MAAM,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,2BAA2B,GAAG,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,OAAO,IAAI,CAAC,2BAA2B,GAAG,EAAE,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO,IAAI,CAAC,YAAY,KAAK,SAAS,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,YAAY,EAAE,QAAQ,KAAK,MAAM,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,0BAA0B;QACxB,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC1C,IAAI,CAAC,UAAU,KAAK,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,CACrD,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO,IAAI,CAAC,YAAY,KAAK,SAAS,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,aAAa;QACX,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,aAAa,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,iBAAiB,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,0BAA0B;QACxB,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,qBAAqB,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,mBAAmB,CAAC;IAC1D,CAAC;IAED;;OAEG;IACH,aAAa;QACX,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,wCAAwC;QACxC,KAAK,IAAI,IAAI,CAAC,sBAAsB,GAAG,GAAG,CAAC;QAE3C,gCAAgC;QAChC,KAAK,IAAI,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC;QAEpC,+CAA+C;QAC/C,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,KAAK,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAC5C,IAAI,CAAC,oBAAoB,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACpE,KAAK,IAAI,WAAW,GAAG,GAAG,CAAC;QAE3B,wBAAwB;QACxB,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACrC,IAAI,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBAC/B,IAAI,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAC1D,KAAK,IAAI,aAAa,GAAG,GAAG,CAAC;QAE7B,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;YACtB,KAAK,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,CAAI,QAAQ;YACtC,KAAK,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAQ,SAAS;YACvC,KAAK,QAAQ,CAAC,CAAC,OAAO,EAAE,CAAC,CAAK,UAAU;YACxC,KAAK,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC,CAAQ,WAAW;YACzC,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC;QACrB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,uBAAuB;QAMrB,IAAI,aAAa,GAAuD,WAAW,CAAC;QACpF,MAAM,iBAAiB,GAAa,EAAE,CAAC;QACvC,MAAM,gBAAgB,GAAa,EAAE,CAAC;QAEtC,IAAI,IAAI,CAAC,kBAAkB,EAAE,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;YACnD,aAAa,GAAG,WAAW,CAAC;YAC5B,iBAAiB,CAAC,IAAI,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;YACpD,gBAAgB,CAAC,IAAI,CAAC,oEAAoE,CAAC,CAAC;QAC9F,CAAC;aAAM,IAAI,IAAI,CAAC,qBAAqB,EAAE,IAAI,IAAI,CAAC,0BAA0B,EAAE,EAAE,CAAC;YAC7E,aAAa,GAAG,UAAU,CAAC;YAC3B,iBAAiB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAC5C,gBAAgB,CAAC,IAAI,CAAC,wEAAwE,CAAC,CAAC;QAClG,CAAC;aAAM,IAAI,IAAI,CAAC,oBAAoB,KAAK,MAAM,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC;YAC1E,aAAa,GAAG,SAAS,CAAC;YAC1B,iBAAiB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC3C,gBAAgB,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAC;QACrF,CAAC;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;YAC/B,iBAAiB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAC7C,gBAAgB,CAAC,IAAI,CAAC,6EAA6E,CAAC,CAAC;QACvG,CAAC;QAED,OAAO;YACL,gBAAgB,EAAE,iBAAiB,CAAC,MAAM,GAAG,CAAC;YAC9C,aAAa;YACb,iBAAiB;YACjB,gBAAgB;SACjB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,MAAM,OAAO,GAAa,CAAC,eAAe,CAAC,CAAC;QAE5C,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;YAC9B,OAAO,CAAC,IAAI,CAAC,qBAAqB,EAAE,wBAAwB,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;YACjC,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;YAC/B,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAClC,CAAC;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;YAC/D,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAClC,CAAC;QAED,IAAI,IAAI,CAAC,0BAA0B,EAAE,EAAE,CAAC;YACtC,OAAO,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAChD,CAAC;QAED,mBAAmB;QACnB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;YACtC,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC;gBACzB,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACjC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,oBAAoB;IACpD,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,IAAI,IAAI,CAAC,oBAAoB,KAAK,MAAM;YAAE,UAAU,IAAI,CAAC,CAAC;aACrD,IAAI,IAAI,CAAC,oBAAoB,KAAK,QAAQ;YAAE,UAAU,IAAI,CAAC,CAAC;QAEjE,IAAI,IAAI,CAAC,eAAe,EAAE;YAAE,UAAU,IAAI,CAAC,CAAC;QAC5C,IAAI,IAAI,CAAC,0BAA0B,EAAE;YAAE,UAAU,IAAI,CAAC,CAAC;QACvD,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC;YAAE,UAAU,IAAI,CAAC,CAAC;QACxD,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC;YAAE,UAAU,IAAI,CAAC,CAAC;QAElD,IAAI,UAAU,IAAI,CAAC;YAAE,OAAO,QAAQ,CAAC;QACrC,IAAI,UAAU,IAAI,CAAC;YAAE,OAAO,UAAU,CAAC;QACvC,IAAI,UAAU,IAAI,CAAC;YAAE,OAAO,SAAS,CAAC;QACtC,OAAO,cAAc,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,wBAAwB;QAgBtB,MAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAEhD,OAAO;YACL,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,IAAI,EAAE,IAAI,CAAC,kBAAkB;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,sBAAsB,EAAE,IAAI,CAAC,sBAAsB;YACnD,UAAU,EAAE,IAAI,CAAC,aAAa,EAAE;YAChC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,cAAc,EAAE,IAAI,CAAC,2BAA2B;YAChD,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC1C,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE;YACvC,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE;YACvC,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC/C,gBAAgB,EAAE,QAAQ,CAAC,gBAAgB;YAC3C,qBAAqB,EAAE,IAAI,CAAC,wBAAwB,EAAE;SACvD,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,kBAAkB;QA4DhB,MAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAChD,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,MAAM,CAAC;QAC9F,MAAM,WAAW,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAEjF,OAAO;YACL,SAAS,EAAE,yBAAyB;YACpC,OAAO,EAAE,KAAK;YACd,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;YACxC,IAAI,EAAE;gBACJ,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;gBACvC,cAAc,EAAE;oBACd,IAAI,EAAE,IAAI,CAAC,kBAAkB;oBAC7B,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,WAAW,EAAE,IAAI,CAAC,WAAW;oBAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,eAAe,EAAE,IAAI,CAAC,eAAe;oBACrC,sBAAsB,EAAE,IAAI,CAAC,sBAAsB;oBACnD,UAAU,EAAE,IAAI,CAAC,aAAa,EAAE;iBACjC;gBACD,cAAc,EAAE;oBACd,MAAM,EAAE,IAAI,CAAC,oBAAoB;oBACjC,cAAc,EAAE,IAAI,CAAC,2BAA2B;oBAChD,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;oBAC1C,WAAW,EAAE,IAAI,CAAC,wBAAwB,EAAE;oBAC5C,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE;oBACvC,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE;iBACxC;gBACD,MAAM,EAAE;oBACN,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;oBACtB,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;oBAClC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;iBAC7B;gBACD,OAAO,EAAE;oBACP,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW;oBACrC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU;oBACnC,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa;oBACzC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;iBAC9C;gBACD,QAAQ,EAAE;oBACR,cAAc,EAAE,IAAI,CAAC,qBAAqB,CAAC,cAAc;oBACzD,aAAa,EAAE,IAAI,CAAC,qBAAqB,CAAC,aAAa;oBACvD,WAAW,EAAE,IAAI,CAAC,qBAAqB,CAAC,WAAW;oBACnD,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,EAAE;oBAC/C,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;iBAChD;gBACD,QAAQ,EAAE;oBACR,gBAAgB,EAAE,QAAQ,CAAC,gBAAgB;oBAC3C,aAAa,EAAE,QAAQ,CAAC,aAAa;oBACrC,iBAAiB,EAAE,QAAQ,CAAC,iBAAiB;iBAC9C;gBACD,OAAO,EAAE;oBACP,KAAK,EAAE,IAAI,CAAC,kBAAkB,CAAC,MAAM;oBACrC,eAAe;oBACf,WAAW;iBACZ;aACF;YACD,QAAQ,EAAE;gBACR,aAAa,EAAE,IAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,MAAM,EAAE,UAAU;gBAClB,aAAa,EAAE,gBAAgB;gBAC/B,eAAe,EAAE,YAAY;aAC9B;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,GAAG,KAAK,CAAC,MAAM,EAAE;YACjB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE;gBACR,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,EAAE;gBAC7C,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE;gBACnD,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE;gBAC3C,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EAAE;gBACzC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB,EAAE;gBACjD,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE;gBAC/B,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE;gBACjC,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE;gBACnD,oBAAoB,EAAE,IAAI,CAAC,oBAAoB,EAAE;gBACjD,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE;gBACvC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE;gBAC7B,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,EAAE;gBAC/C,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE;gBACvC,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE;gBACvC,0BAA0B,EAAE,IAAI,CAAC,0BAA0B,EAAE;gBAC7D,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE;gBACvC,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE;gBACnC,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,EAAE;gBAC/C,0BAA0B,EAAE,IAAI,CAAC,0BAA0B,EAAE;gBAC7D,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE;gBACnD,UAAU,EAAE,IAAI,CAAC,aAAa,EAAE;gBAChC,qBAAqB,EAAE,IAAI,CAAC,wBAAwB,EAAE;gBACtD,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;gBAC1C,oBAAoB,EAAE,IAAI,CAAC,uBAAuB,EAAE;gBACpD,mBAAmB,EAAE,IAAI,CAAC,sBAAsB,EAAE;gBAClD,qBAAqB,EAAE,IAAI,CAAC,wBAAwB,EAAE;aACvD;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,IAAyB;QACvC,OAAO,IAAI,kCAAkC,CAC3C,8BAAc,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,EAC3C,IAAI,CAAC,SAAS,EACd;YACE,OAAO,EAAE,8BAAc,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC;YAChD,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;YACrC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;QACjD,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,qBAAqB,CAAC;QAC5F,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAC7C,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QAE/D,OAAO,GAAG,YAAY,aAAa,IAAI,CAAC,kBAAkB,oBAAoB,IAAI,CAAC,KAAK,oBAAoB,cAAc,KAAK,UAAU,yBAAyB,WAAW,EAAE,CAAC;IAClL,CAAC;IAED;;OAEG;IACH,UAAU;QAcR,MAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAEhD,OAAO;YACL,SAAS,EAAE,yBAAyB;YACpC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,IAAI,EAAE,IAAI,CAAC,kBAAkB;YAC7B,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,sBAAsB,EAAE,IAAI,CAAC,sBAAsB;YACnD,UAAU,EAAE,IAAI,CAAC,aAAa,EAAE;YAChC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW;YACrC,gBAAgB,EAAE,QAAQ,CAAC,gBAAgB;YAC3C,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;SACzC,CAAC;IACJ,CAAC;CACF;AAruBD,gFAquBC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\recommendation-generated.domain-event.ts"], "sourcesContent": ["import { BaseDomainEvent, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { ActionType } from '../enums/action-type.enum';\r\n\r\n/**\r\n * Recommendation Generated Domain Event Data\r\n */\r\nexport interface RecommendationGeneratedEventData {\r\n  /** ID of the recommendation */\r\n  recommendationId: string;\r\n  /** Type of recommendation */\r\n  recommendationType: 'security_action' | 'policy_update' | 'configuration_change' | 'process_improvement' | 'training' | 'compliance' | 'threat_hunting';\r\n  /** Recommendation title */\r\n  title: string;\r\n  /** Detailed recommendation description */\r\n  description: string;\r\n  /** Priority level of the recommendation */\r\n  priority: 'low' | 'medium' | 'high' | 'critical';\r\n  /** Confidence score in the recommendation (0-100) */\r\n  confidenceScore: number;\r\n  /** Risk reduction potential (0-100) */\r\n  riskReductionPotential: number;\r\n  /** Implementation effort required */\r\n  implementationEffort: 'low' | 'medium' | 'high';\r\n  /** Estimated implementation time in hours */\r\n  estimatedImplementationTime: number;\r\n  /** Cost estimate for implementation */\r\n  costEstimate?: {\r\n    amount: number;\r\n    currency: string;\r\n    category: 'low' | 'medium' | 'high';\r\n  };\r\n  /** Source that generated the recommendation */\r\n  source: {\r\n    type: 'ai_analysis' | 'threat_intelligence' | 'compliance_scan' | 'vulnerability_assessment' | 'incident_analysis' | 'manual_review';\r\n    identifier: string;\r\n    version?: string;\r\n  };\r\n  /** Context that triggered the recommendation */\r\n  context: {\r\n    triggerType: 'threat_detected' | 'vulnerability_found' | 'incident_occurred' | 'compliance_gap' | 'policy_violation' | 'scheduled_review';\r\n    triggerIds: string[];\r\n    relatedAssets?: string[];\r\n    affectedSystems?: string[];\r\n  };\r\n  /** Recommended actions */\r\n  recommendedActions: Array<{\r\n    actionType: ActionType;\r\n    description: string;\r\n    priority: 'low' | 'medium' | 'high' | 'critical';\r\n    estimatedDuration: number; // minutes\r\n    prerequisites?: string[];\r\n    risks?: string[];\r\n  }>;\r\n  /** Success criteria for the recommendation */\r\n  successCriteria: string[];\r\n  /** Key performance indicators to measure success */\r\n  kpis: Array<{\r\n    name: string;\r\n    description: string;\r\n    targetValue: string;\r\n    measurementMethod: string;\r\n  }>;\r\n  /** Compliance frameworks this recommendation addresses */\r\n  complianceFrameworks?: string[];\r\n  /** Business justification */\r\n  businessJustification: {\r\n    riskMitigation: string;\r\n    businessValue: string;\r\n    costBenefit: string;\r\n  };\r\n  /** Implementation timeline */\r\n  timeline: {\r\n    recommendedStartDate: Date;\r\n    recommendedCompletionDate: Date;\r\n    milestones: Array<{\r\n      name: string;\r\n      description: string;\r\n      targetDate: Date;\r\n      dependencies?: string[];\r\n    }>;\r\n  };\r\n  /** Stakeholders involved */\r\n  stakeholders: Array<{\r\n    role: string;\r\n    responsibility: string;\r\n    required: boolean;\r\n  }>;\r\n  /** Alternative approaches */\r\n  alternatives?: Array<{\r\n    title: string;\r\n    description: string;\r\n    pros: string[];\r\n    cons: string[];\r\n    effort: 'low' | 'medium' | 'high';\r\n    riskReduction: number; // 0-100\r\n  }>;\r\n  /** Dependencies and prerequisites */\r\n  dependencies: string[];\r\n  /** Potential risks of implementation */\r\n  implementationRisks: Array<{\r\n    risk: string;\r\n    likelihood: 'low' | 'medium' | 'high';\r\n    impact: 'low' | 'medium' | 'high';\r\n    mitigation: string;\r\n  }>;\r\n  /** Validation and testing requirements */\r\n  validationRequirements: string[];\r\n  /** Rollback plan */\r\n  rollbackPlan?: {\r\n    description: string;\r\n    steps: string[];\r\n    estimatedTime: number; // minutes\r\n  };\r\n}\r\n\r\n/**\r\n * Recommendation Generated Domain Event\r\n * \r\n * Raised when the system generates a new security recommendation based on\r\n * threat analysis, vulnerability assessment, compliance gaps, or other security\r\n * insights. This event triggers recommendation review and implementation workflows.\r\n * \r\n * Key characteristics:\r\n * - Represents AI or system-generated security recommendations\r\n * - Contains comprehensive implementation guidance\r\n * - Includes business justification and risk analysis\r\n * - Triggers approval and implementation workflows\r\n * \r\n * Downstream processes triggered:\r\n * - Recommendation review and approval\r\n * - Implementation planning and scheduling\r\n * - Stakeholder notification and assignment\r\n * - Progress tracking and monitoring\r\n * - Success measurement and validation\r\n */\r\nexport class RecommendationGeneratedDomainEvent extends BaseDomainEvent<RecommendationGeneratedEventData> {\r\n  constructor(\r\n    aggregateId: UniqueEntityId,\r\n    eventData: RecommendationGeneratedEventData,\r\n    options?: {\r\n      eventId?: UniqueEntityId;\r\n      occurredOn?: Date;\r\n      eventVersion?: number;\r\n      correlationId?: string;\r\n      causationId?: string;\r\n      metadata?: Record<string, any>;\r\n    }\r\n  ) {\r\n    super(aggregateId, eventData, {\r\n      eventVersion: 1,\r\n      ...options,\r\n      metadata: {\r\n        eventType: 'RecommendationGenerated',\r\n        domain: 'Security',\r\n        aggregateType: 'Recommendation',\r\n        processingStage: 'generation',\r\n        ...options?.metadata,\r\n      },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get the recommendation ID\r\n   */\r\n  get recommendationId(): string {\r\n    return this.eventData.recommendationId;\r\n  }\r\n\r\n  /**\r\n   * Get the recommendation type\r\n   */\r\n  get recommendationType(): RecommendationGeneratedEventData['recommendationType'] {\r\n    return this.eventData.recommendationType;\r\n  }\r\n\r\n  /**\r\n   * Get the recommendation title\r\n   */\r\n  get title(): string {\r\n    return this.eventData.title;\r\n  }\r\n\r\n  /**\r\n   * Get the recommendation description\r\n   */\r\n  get description(): string {\r\n    return this.eventData.description;\r\n  }\r\n\r\n  /**\r\n   * Get the priority level\r\n   */\r\n  get priority(): RecommendationGeneratedEventData['priority'] {\r\n    return this.eventData.priority;\r\n  }\r\n\r\n  /**\r\n   * Get the confidence score\r\n   */\r\n  get confidenceScore(): number {\r\n    return this.eventData.confidenceScore;\r\n  }\r\n\r\n  /**\r\n   * Get the risk reduction potential\r\n   */\r\n  get riskReductionPotential(): number {\r\n    return this.eventData.riskReductionPotential;\r\n  }\r\n\r\n  /**\r\n   * Get the implementation effort\r\n   */\r\n  get implementationEffort(): RecommendationGeneratedEventData['implementationEffort'] {\r\n    return this.eventData.implementationEffort;\r\n  }\r\n\r\n  /**\r\n   * Get the estimated implementation time\r\n   */\r\n  get estimatedImplementationTime(): number {\r\n    return this.eventData.estimatedImplementationTime;\r\n  }\r\n\r\n  /**\r\n   * Get the cost estimate\r\n   */\r\n  get costEstimate(): RecommendationGeneratedEventData['costEstimate'] {\r\n    return this.eventData.costEstimate;\r\n  }\r\n\r\n  /**\r\n   * Get the recommendation source\r\n   */\r\n  get source(): RecommendationGeneratedEventData['source'] {\r\n    return this.eventData.source;\r\n  }\r\n\r\n  /**\r\n   * Get the context that triggered the recommendation\r\n   */\r\n  get context(): RecommendationGeneratedEventData['context'] {\r\n    return this.eventData.context;\r\n  }\r\n\r\n  /**\r\n   * Get the recommended actions\r\n   */\r\n  get recommendedActions(): RecommendationGeneratedEventData['recommendedActions'] {\r\n    return this.eventData.recommendedActions;\r\n  }\r\n\r\n  /**\r\n   * Get the success criteria\r\n   */\r\n  get successCriteria(): string[] {\r\n    return this.eventData.successCriteria;\r\n  }\r\n\r\n  /**\r\n   * Get the KPIs\r\n   */\r\n  get kpis(): RecommendationGeneratedEventData['kpis'] {\r\n    return this.eventData.kpis;\r\n  }\r\n\r\n  /**\r\n   * Get the compliance frameworks\r\n   */\r\n  get complianceFrameworks(): string[] | undefined {\r\n    return this.eventData.complianceFrameworks;\r\n  }\r\n\r\n  /**\r\n   * Get the business justification\r\n   */\r\n  get businessJustification(): RecommendationGeneratedEventData['businessJustification'] {\r\n    return this.eventData.businessJustification;\r\n  }\r\n\r\n  /**\r\n   * Get the implementation timeline\r\n   */\r\n  get timeline(): RecommendationGeneratedEventData['timeline'] {\r\n    return this.eventData.timeline;\r\n  }\r\n\r\n  /**\r\n   * Get the stakeholders\r\n   */\r\n  get stakeholders(): RecommendationGeneratedEventData['stakeholders'] {\r\n    return this.eventData.stakeholders;\r\n  }\r\n\r\n  /**\r\n   * Get the alternatives\r\n   */\r\n  get alternatives(): RecommendationGeneratedEventData['alternatives'] {\r\n    return this.eventData.alternatives;\r\n  }\r\n\r\n  /**\r\n   * Get the dependencies\r\n   */\r\n  get dependencies(): string[] {\r\n    return this.eventData.dependencies;\r\n  }\r\n\r\n  /**\r\n   * Get the implementation risks\r\n   */\r\n  get implementationRisks(): RecommendationGeneratedEventData['implementationRisks'] {\r\n    return this.eventData.implementationRisks;\r\n  }\r\n\r\n  /**\r\n   * Get the validation requirements\r\n   */\r\n  get validationRequirements(): string[] {\r\n    return this.eventData.validationRequirements;\r\n  }\r\n\r\n  /**\r\n   * Get the rollback plan\r\n   */\r\n  get rollbackPlan(): RecommendationGeneratedEventData['rollbackPlan'] {\r\n    return this.eventData.rollbackPlan;\r\n  }\r\n\r\n  /**\r\n   * Check if recommendation is critical priority\r\n   */\r\n  isCriticalPriority(): boolean {\r\n    return this.priority === 'critical';\r\n  }\r\n\r\n  /**\r\n   * Check if recommendation is high priority or above\r\n   */\r\n  isHighPriorityOrAbove(): boolean {\r\n    return ['high', 'critical'].includes(this.priority);\r\n  }\r\n\r\n  /**\r\n   * Check if recommendation has high confidence\r\n   */\r\n  hasHighConfidence(): boolean {\r\n    return this.confidenceScore >= 80;\r\n  }\r\n\r\n  /**\r\n   * Check if recommendation has low confidence\r\n   */\r\n  hasLowConfidence(): boolean {\r\n    return this.confidenceScore < 60;\r\n  }\r\n\r\n  /**\r\n   * Check if recommendation has high risk reduction potential\r\n   */\r\n  hasHighRiskReduction(): boolean {\r\n    return this.riskReductionPotential >= 70;\r\n  }\r\n\r\n  /**\r\n   * Check if recommendation is low effort\r\n   */\r\n  isLowEffort(): boolean {\r\n    return this.implementationEffort === 'low';\r\n  }\r\n\r\n  /**\r\n   * Check if recommendation is high effort\r\n   */\r\n  isHighEffort(): boolean {\r\n    return this.implementationEffort === 'high';\r\n  }\r\n\r\n  /**\r\n   * Check if recommendation is quick to implement (under 8 hours)\r\n   */\r\n  isQuickImplementation(): boolean {\r\n    return this.estimatedImplementationTime < 8;\r\n  }\r\n\r\n  /**\r\n   * Check if recommendation is long implementation (over 40 hours)\r\n   */\r\n  isLongImplementation(): boolean {\r\n    return this.estimatedImplementationTime > 40;\r\n  }\r\n\r\n  /**\r\n   * Check if recommendation has cost estimate\r\n   */\r\n  hasCostEstimate(): boolean {\r\n    return this.costEstimate !== undefined;\r\n  }\r\n\r\n  /**\r\n   * Check if recommendation is high cost\r\n   */\r\n  isHighCost(): boolean {\r\n    return this.costEstimate?.category === 'high';\r\n  }\r\n\r\n  /**\r\n   * Check if recommendation addresses compliance\r\n   */\r\n  addressesCompliance(): boolean {\r\n    return (this.complianceFrameworks?.length || 0) > 0;\r\n  }\r\n\r\n  /**\r\n   * Check if recommendation has alternatives\r\n   */\r\n  hasAlternatives(): boolean {\r\n    return (this.alternatives?.length || 0) > 0;\r\n  }\r\n\r\n  /**\r\n   * Check if recommendation has dependencies\r\n   */\r\n  hasDependencies(): boolean {\r\n    return this.dependencies.length > 0;\r\n  }\r\n\r\n  /**\r\n   * Check if recommendation has high implementation risks\r\n   */\r\n  hasHighImplementationRisks(): boolean {\r\n    return this.implementationRisks.some(risk => \r\n      risk.likelihood === 'high' && risk.impact === 'high'\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Check if recommendation has rollback plan\r\n   */\r\n  hasRollbackPlan(): boolean {\r\n    return this.rollbackPlan !== undefined;\r\n  }\r\n\r\n  /**\r\n   * Check if recommendation is AI-generated\r\n   */\r\n  isAIGenerated(): boolean {\r\n    return this.source.type === 'ai_analysis';\r\n  }\r\n\r\n  /**\r\n   * Check if recommendation is triggered by threat\r\n   */\r\n  isTriggeredByThreat(): boolean {\r\n    return this.context.triggerType === 'threat_detected';\r\n  }\r\n\r\n  /**\r\n   * Check if recommendation is triggered by vulnerability\r\n   */\r\n  isTriggeredByVulnerability(): boolean {\r\n    return this.context.triggerType === 'vulnerability_found';\r\n  }\r\n\r\n  /**\r\n   * Check if recommendation is triggered by incident\r\n   */\r\n  isTriggeredByIncident(): boolean {\r\n    return this.context.triggerType === 'incident_occurred';\r\n  }\r\n\r\n  /**\r\n   * Get recommendation value score (0-100)\r\n   */\r\n  getValueScore(): number {\r\n    let score = 0;\r\n    \r\n    // Risk reduction potential (40% weight)\r\n    score += this.riskReductionPotential * 0.4;\r\n    \r\n    // Confidence score (30% weight)\r\n    score += this.confidenceScore * 0.3;\r\n    \r\n    // Implementation effort (20% weight - inverse)\r\n    const effortScore = this.implementationEffort === 'low' ? 100 : \r\n                       this.implementationEffort === 'medium' ? 60 : 30;\r\n    score += effortScore * 0.2;\r\n    \r\n    // Priority (10% weight)\r\n    const priorityScore = this.priority === 'critical' ? 100 :\r\n                         this.priority === 'high' ? 80 :\r\n                         this.priority === 'medium' ? 60 : 40;\r\n    score += priorityScore * 0.1;\r\n    \r\n    return Math.round(score);\r\n  }\r\n\r\n  /**\r\n   * Get implementation urgency in days\r\n   */\r\n  getImplementationUrgency(): number {\r\n    switch (this.priority) {\r\n      case 'critical': return 1;    // 1 day\r\n      case 'high': return 7;        // 1 week\r\n      case 'medium': return 30;     // 1 month\r\n      case 'low': return 90;        // 3 months\r\n      default: return 90;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get approval requirements\r\n   */\r\n  getApprovalRequirements(): {\r\n    requiresApproval: boolean;\r\n    approvalLevel: 'team_lead' | 'manager' | 'director' | 'executive';\r\n    requiredApprovers: string[];\r\n    approvalCriteria: string[];\r\n  } {\r\n    let approvalLevel: 'team_lead' | 'manager' | 'director' | 'executive' = 'team_lead';\r\n    const requiredApprovers: string[] = [];\r\n    const approvalCriteria: string[] = [];\r\n    \r\n    if (this.isCriticalPriority() || this.isHighCost()) {\r\n      approvalLevel = 'executive';\r\n      requiredApprovers.push('security_director', 'ciso');\r\n      approvalCriteria.push('Executive approval required for critical/high-cost recommendations');\r\n    } else if (this.isHighPriorityOrAbove() || this.hasHighImplementationRisks()) {\r\n      approvalLevel = 'director';\r\n      requiredApprovers.push('security_director');\r\n      approvalCriteria.push('Director approval required for high-priority/high-risk recommendations');\r\n    } else if (this.implementationEffort === 'high' || this.hasDependencies()) {\r\n      approvalLevel = 'manager';\r\n      requiredApprovers.push('security_manager');\r\n      approvalCriteria.push('Manager approval required for high-effort recommendations');\r\n    }\r\n    \r\n    if (this.addressesCompliance()) {\r\n      requiredApprovers.push('compliance_officer');\r\n      approvalCriteria.push('Compliance officer approval required for compliance-related recommendations');\r\n    }\r\n    \r\n    return {\r\n      requiresApproval: requiredApprovers.length > 0,\r\n      approvalLevel,\r\n      requiredApprovers,\r\n      approvalCriteria,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get notification targets\r\n   */\r\n  getNotificationTargets(): string[] {\r\n    const targets: string[] = ['security_team'];\r\n    \r\n    if (this.isCriticalPriority()) {\r\n      targets.push('security_leadership', 'incident_response_team');\r\n    }\r\n    \r\n    if (this.isHighPriorityOrAbove()) {\r\n      targets.push('security_managers');\r\n    }\r\n    \r\n    if (this.addressesCompliance()) {\r\n      targets.push('compliance_team');\r\n    }\r\n    \r\n    if (this.isTriggeredByThreat() || this.isTriggeredByIncident()) {\r\n      targets.push('threat_analysts');\r\n    }\r\n    \r\n    if (this.isTriggeredByVulnerability()) {\r\n      targets.push('vulnerability_management_team');\r\n    }\r\n    \r\n    // Add stakeholders\r\n    this.stakeholders.forEach(stakeholder => {\r\n      if (stakeholder.required) {\r\n        targets.push(stakeholder.role);\r\n      }\r\n    });\r\n    \r\n    return [...new Set(targets)]; // Remove duplicates\r\n  }\r\n\r\n  /**\r\n   * Get implementation complexity level\r\n   */\r\n  getComplexityLevel(): 'simple' | 'moderate' | 'complex' | 'very_complex' {\r\n    let complexity = 0;\r\n    \r\n    if (this.implementationEffort === 'high') complexity += 2;\r\n    else if (this.implementationEffort === 'medium') complexity += 1;\r\n    \r\n    if (this.hasDependencies()) complexity += 1;\r\n    if (this.hasHighImplementationRisks()) complexity += 1;\r\n    if (this.recommendedActions.length > 5) complexity += 1;\r\n    if (this.stakeholders.length > 3) complexity += 1;\r\n    \r\n    if (complexity <= 1) return 'simple';\r\n    if (complexity <= 3) return 'moderate';\r\n    if (complexity <= 5) return 'complex';\r\n    return 'very_complex';\r\n  }\r\n\r\n  /**\r\n   * Get recommendation metrics\r\n   */\r\n  getRecommendationMetrics(): {\r\n    recommendationId: string;\r\n    type: string;\r\n    priority: string;\r\n    confidenceScore: number;\r\n    riskReductionPotential: number;\r\n    valueScore: number;\r\n    implementationEffort: string;\r\n    estimatedHours: number;\r\n    complexityLevel: string;\r\n    hasAlternatives: boolean;\r\n    hasDependencies: boolean;\r\n    addressesCompliance: boolean;\r\n    requiresApproval: boolean;\r\n    implementationUrgency: number;\r\n  } {\r\n    const approval = this.getApprovalRequirements();\r\n    \r\n    return {\r\n      recommendationId: this.recommendationId,\r\n      type: this.recommendationType,\r\n      priority: this.priority,\r\n      confidenceScore: this.confidenceScore,\r\n      riskReductionPotential: this.riskReductionPotential,\r\n      valueScore: this.getValueScore(),\r\n      implementationEffort: this.implementationEffort,\r\n      estimatedHours: this.estimatedImplementationTime,\r\n      complexityLevel: this.getComplexityLevel(),\r\n      hasAlternatives: this.hasAlternatives(),\r\n      hasDependencies: this.hasDependencies(),\r\n      addressesCompliance: this.addressesCompliance(),\r\n      requiresApproval: approval.requiresApproval,\r\n      implementationUrgency: this.getImplementationUrgency(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert to integration event for external systems\r\n   */\r\n  toIntegrationEvent(): {\r\n    eventType: string;\r\n    version: string;\r\n    timestamp: string;\r\n    data: {\r\n      recommendationId: string;\r\n      recommendation: {\r\n        type: string;\r\n        title: string;\r\n        description: string;\r\n        priority: string;\r\n        confidenceScore: number;\r\n        riskReductionPotential: number;\r\n        valueScore: number;\r\n      };\r\n      implementation: {\r\n        effort: string;\r\n        estimatedHours: number;\r\n        complexityLevel: string;\r\n        urgencyDays: number;\r\n        hasDependencies: boolean;\r\n        hasRollbackPlan: boolean;\r\n      };\r\n      source: {\r\n        type: string;\r\n        identifier: string;\r\n        version?: string;\r\n      };\r\n      context: {\r\n        triggerType: string;\r\n        triggerIds: string[];\r\n        relatedAssets?: string[];\r\n        affectedSystems?: string[];\r\n      };\r\n      business: {\r\n        riskMitigation: string;\r\n        businessValue: string;\r\n        costBenefit: string;\r\n        addressesCompliance: boolean;\r\n        complianceFrameworks?: string[];\r\n      };\r\n      approval: {\r\n        requiresApproval: boolean;\r\n        approvalLevel: string;\r\n        requiredApprovers: string[];\r\n      };\r\n      actions: {\r\n        count: number;\r\n        criticalActions: number;\r\n        actionTypes: ActionType[];\r\n      };\r\n    };\r\n    metadata: {\r\n      correlationId?: string;\r\n      causationId?: string;\r\n      domain: string;\r\n      aggregateType: string;\r\n      processingStage: string;\r\n    };\r\n  } {\r\n    const approval = this.getApprovalRequirements();\r\n    const criticalActions = this.recommendedActions.filter(a => a.priority === 'critical').length;\r\n    const actionTypes = [...new Set(this.recommendedActions.map(a => a.actionType))];\r\n    \r\n    return {\r\n      eventType: 'RecommendationGenerated',\r\n      version: '1.0',\r\n      timestamp: this.occurredOn.toISOString(),\r\n      data: {\r\n        recommendationId: this.recommendationId,\r\n        recommendation: {\r\n          type: this.recommendationType,\r\n          title: this.title,\r\n          description: this.description,\r\n          priority: this.priority,\r\n          confidenceScore: this.confidenceScore,\r\n          riskReductionPotential: this.riskReductionPotential,\r\n          valueScore: this.getValueScore(),\r\n        },\r\n        implementation: {\r\n          effort: this.implementationEffort,\r\n          estimatedHours: this.estimatedImplementationTime,\r\n          complexityLevel: this.getComplexityLevel(),\r\n          urgencyDays: this.getImplementationUrgency(),\r\n          hasDependencies: this.hasDependencies(),\r\n          hasRollbackPlan: this.hasRollbackPlan(),\r\n        },\r\n        source: {\r\n          type: this.source.type,\r\n          identifier: this.source.identifier,\r\n          version: this.source.version,\r\n        },\r\n        context: {\r\n          triggerType: this.context.triggerType,\r\n          triggerIds: this.context.triggerIds,\r\n          relatedAssets: this.context.relatedAssets,\r\n          affectedSystems: this.context.affectedSystems,\r\n        },\r\n        business: {\r\n          riskMitigation: this.businessJustification.riskMitigation,\r\n          businessValue: this.businessJustification.businessValue,\r\n          costBenefit: this.businessJustification.costBenefit,\r\n          addressesCompliance: this.addressesCompliance(),\r\n          complianceFrameworks: this.complianceFrameworks,\r\n        },\r\n        approval: {\r\n          requiresApproval: approval.requiresApproval,\r\n          approvalLevel: approval.approvalLevel,\r\n          requiredApprovers: approval.requiredApprovers,\r\n        },\r\n        actions: {\r\n          count: this.recommendedActions.length,\r\n          criticalActions,\r\n          actionTypes,\r\n        },\r\n      },\r\n      metadata: {\r\n        correlationId: this.correlationId,\r\n        causationId: this.causationId,\r\n        domain: 'Security',\r\n        aggregateType: 'Recommendation',\r\n        processingStage: 'generation',\r\n      },\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      ...super.toJSON(),\r\n      eventData: this.eventData,\r\n      analysis: {\r\n        isCriticalPriority: this.isCriticalPriority(),\r\n        isHighPriorityOrAbove: this.isHighPriorityOrAbove(),\r\n        hasHighConfidence: this.hasHighConfidence(),\r\n        hasLowConfidence: this.hasLowConfidence(),\r\n        hasHighRiskReduction: this.hasHighRiskReduction(),\r\n        isLowEffort: this.isLowEffort(),\r\n        isHighEffort: this.isHighEffort(),\r\n        isQuickImplementation: this.isQuickImplementation(),\r\n        isLongImplementation: this.isLongImplementation(),\r\n        hasCostEstimate: this.hasCostEstimate(),\r\n        isHighCost: this.isHighCost(),\r\n        addressesCompliance: this.addressesCompliance(),\r\n        hasAlternatives: this.hasAlternatives(),\r\n        hasDependencies: this.hasDependencies(),\r\n        hasHighImplementationRisks: this.hasHighImplementationRisks(),\r\n        hasRollbackPlan: this.hasRollbackPlan(),\r\n        isAIGenerated: this.isAIGenerated(),\r\n        isTriggeredByThreat: this.isTriggeredByThreat(),\r\n        isTriggeredByVulnerability: this.isTriggeredByVulnerability(),\r\n        isTriggeredByIncident: this.isTriggeredByIncident(),\r\n        valueScore: this.getValueScore(),\r\n        implementationUrgency: this.getImplementationUrgency(),\r\n        complexityLevel: this.getComplexityLevel(),\r\n        approvalRequirements: this.getApprovalRequirements(),\r\n        notificationTargets: this.getNotificationTargets(),\r\n        recommendationMetrics: this.getRecommendationMetrics(),\r\n      },\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create from JSON representation\r\n   */\r\n  static fromJSON(json: Record<string, any>): RecommendationGeneratedDomainEvent {\r\n    return new RecommendationGeneratedDomainEvent(\r\n      UniqueEntityId.fromString(json.aggregateId),\r\n      json.eventData,\r\n      {\r\n        eventId: UniqueEntityId.fromString(json.eventId),\r\n        occurredOn: new Date(json.occurredOn),\r\n        eventVersion: json.eventVersion,\r\n        correlationId: json.correlationId,\r\n        causationId: json.causationId,\r\n        metadata: json.metadata,\r\n      }\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get human-readable description\r\n   */\r\n  getDescription(): string {\r\n    const priorityText = this.priority.toUpperCase();\r\n    const confidenceText = this.hasHighConfidence() ? 'high confidence' : 'moderate confidence';\r\n    const effortText = this.implementationEffort;\r\n    const triggerText = this.context.triggerType.replace('_', ' ');\r\n    \r\n    return `${priorityText} priority ${this.recommendationType} recommendation \"${this.title}\" generated with ${confidenceText} (${effortText} effort) triggered by ${triggerText}`;\r\n  }\r\n\r\n  /**\r\n   * Get event summary for logging\r\n   */\r\n  getSummary(): {\r\n    eventType: string;\r\n    recommendationId: string;\r\n    type: string;\r\n    title: string;\r\n    priority: string;\r\n    confidenceScore: number;\r\n    riskReductionPotential: number;\r\n    valueScore: number;\r\n    implementationEffort: string;\r\n    triggerType: string;\r\n    requiresApproval: boolean;\r\n    timestamp: string;\r\n  } {\r\n    const approval = this.getApprovalRequirements();\r\n    \r\n    return {\r\n      eventType: 'RecommendationGenerated',\r\n      recommendationId: this.recommendationId,\r\n      type: this.recommendationType,\r\n      title: this.title,\r\n      priority: this.priority,\r\n      confidenceScore: this.confidenceScore,\r\n      riskReductionPotential: this.riskReductionPotential,\r\n      valueScore: this.getValueScore(),\r\n      implementationEffort: this.implementationEffort,\r\n      triggerType: this.context.triggerType,\r\n      requiresApproval: approval.requiresApproval,\r\n      timestamp: this.occurredOn.toISOString(),\r\n    };\r\n  }\r\n}"], "version": 3}