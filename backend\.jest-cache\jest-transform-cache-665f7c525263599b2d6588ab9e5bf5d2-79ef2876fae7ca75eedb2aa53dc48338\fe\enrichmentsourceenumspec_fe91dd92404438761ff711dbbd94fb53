102c3c7f8c6fad4b72e7405e12aeeac1
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const enrichment_source_enum_1 = require("../enrichment-source.enum");
describe('EnrichmentSource', () => {
    describe('enum values', () => {
        it('should have all expected enrichment sources', () => {
            expect(enrichment_source_enum_1.EnrichmentSource.COMMERCIAL_THREAT_INTEL).toBe('commercial_threat_intel');
            expect(enrichment_source_enum_1.EnrichmentSource.OSINT).toBe('osint');
            expect(enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION).toBe('ip_reputation');
            expect(enrichment_source_enum_1.EnrichmentSource.VIRUSTOTAL).toBe('virustotal');
            expect(enrichment_source_enum_1.EnrichmentSource.NVD).toBe('nvd');
            expect(enrichment_source_enum_1.EnrichmentSource.UNKNOWN).toBe('unknown');
        });
    });
    describe('EnrichmentSourceUtils', () => {
        describe('getAllSources', () => {
            it('should return all enrichment sources', () => {
                const sources = enrichment_source_enum_1.EnrichmentSourceUtils.getAllSources();
                expect(sources.length).toBeGreaterThan(50); // We have many sources
                expect(sources).toContain(enrichment_source_enum_1.EnrichmentSource.COMMERCIAL_THREAT_INTEL);
                expect(sources).toContain(enrichment_source_enum_1.EnrichmentSource.VIRUSTOTAL);
                expect(sources).toContain(enrichment_source_enum_1.EnrichmentSource.NVD);
            });
        });
        describe('getThreatIntelSources', () => {
            it('should return threat intelligence sources', () => {
                const threatIntelSources = enrichment_source_enum_1.EnrichmentSourceUtils.getThreatIntelSources();
                expect(threatIntelSources).toContain(enrichment_source_enum_1.EnrichmentSource.COMMERCIAL_THREAT_INTEL);
                expect(threatIntelSources).toContain(enrichment_source_enum_1.EnrichmentSource.OSINT);
                expect(threatIntelSources).toContain(enrichment_source_enum_1.EnrichmentSource.GOVERNMENT_INTEL);
                expect(threatIntelSources).toContain(enrichment_source_enum_1.EnrichmentSource.MISP);
                expect(threatIntelSources).not.toContain(enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION);
            });
        });
        describe('getReputationSources', () => {
            it('should return reputation service sources', () => {
                const reputationSources = enrichment_source_enum_1.EnrichmentSourceUtils.getReputationSources();
                expect(reputationSources).toContain(enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION);
                expect(reputationSources).toContain(enrichment_source_enum_1.EnrichmentSource.DOMAIN_REPUTATION);
                expect(reputationSources).toContain(enrichment_source_enum_1.EnrichmentSource.URL_REPUTATION);
                expect(reputationSources).toContain(enrichment_source_enum_1.EnrichmentSource.FILE_REPUTATION);
                expect(reputationSources).not.toContain(enrichment_source_enum_1.EnrichmentSource.VIRUSTOTAL);
            });
        });
        describe('getExternalAPISources', () => {
            it('should return external API sources', () => {
                const externalAPISources = enrichment_source_enum_1.EnrichmentSourceUtils.getExternalAPISources();
                expect(externalAPISources).toContain(enrichment_source_enum_1.EnrichmentSource.VIRUSTOTAL);
                expect(externalAPISources).toContain(enrichment_source_enum_1.EnrichmentSource.SHODAN);
                expect(externalAPISources).toContain(enrichment_source_enum_1.EnrichmentSource.CENSYS);
                expect(externalAPISources).toContain(enrichment_source_enum_1.EnrichmentSource.PASSIVETOTAL);
                expect(externalAPISources).not.toContain(enrichment_source_enum_1.EnrichmentSource.INTERNAL_SECURITY_TOOLS);
            });
        });
        describe('getInternalSources', () => {
            it('should return internal sources', () => {
                const internalSources = enrichment_source_enum_1.EnrichmentSourceUtils.getInternalSources();
                expect(internalSources).toContain(enrichment_source_enum_1.EnrichmentSource.INTERNAL_SECURITY_TOOLS);
                expect(internalSources).toContain(enrichment_source_enum_1.EnrichmentSource.CUSTOM_RULES);
                expect(internalSources).toContain(enrichment_source_enum_1.EnrichmentSource.MANUAL_ANALYSIS);
                expect(internalSources).toContain(enrichment_source_enum_1.EnrichmentSource.AUTOMATED_CORRELATION);
                expect(internalSources).not.toContain(enrichment_source_enum_1.EnrichmentSource.VIRUSTOTAL);
            });
        });
        describe('getRealTimeSources', () => {
            it('should return real-time sources', () => {
                const realTimeSources = enrichment_source_enum_1.EnrichmentSourceUtils.getRealTimeSources();
                expect(realTimeSources).toContain(enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION);
                expect(realTimeSources).toContain(enrichment_source_enum_1.EnrichmentSource.DNS_RESOLUTION);
                expect(realTimeSources).toContain(enrichment_source_enum_1.EnrichmentSource.VIRUSTOTAL);
                expect(realTimeSources).toContain(enrichment_source_enum_1.EnrichmentSource.ML_INFERENCE);
                expect(realTimeSources).not.toContain(enrichment_source_enum_1.EnrichmentSource.NVD);
            });
        });
        describe('getBatchSources', () => {
            it('should return batch processing sources', () => {
                const batchSources = enrichment_source_enum_1.EnrichmentSourceUtils.getBatchSources();
                expect(batchSources).toContain(enrichment_source_enum_1.EnrichmentSource.COMMERCIAL_THREAT_INTEL);
                expect(batchSources).toContain(enrichment_source_enum_1.EnrichmentSource.NVD);
                expect(batchSources).toContain(enrichment_source_enum_1.EnrichmentSource.CVE_DATABASE);
                expect(batchSources).toContain(enrichment_source_enum_1.EnrichmentSource.CMDB);
                expect(batchSources).not.toContain(enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION);
            });
        });
        describe('isExternal', () => {
            it('should correctly identify external sources', () => {
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.isExternal(enrichment_source_enum_1.EnrichmentSource.VIRUSTOTAL)).toBe(true);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.isExternal(enrichment_source_enum_1.EnrichmentSource.SHODAN)).toBe(true);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.isExternal(enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION)).toBe(true);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.isExternal(enrichment_source_enum_1.EnrichmentSource.COMMERCIAL_THREAT_INTEL)).toBe(true);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.isExternal(enrichment_source_enum_1.EnrichmentSource.INTERNAL_SECURITY_TOOLS)).toBe(false);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.isExternal(enrichment_source_enum_1.EnrichmentSource.MANUAL_ANALYSIS)).toBe(false);
            });
        });
        describe('isInternal', () => {
            it('should correctly identify internal sources', () => {
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.isInternal(enrichment_source_enum_1.EnrichmentSource.INTERNAL_SECURITY_TOOLS)).toBe(true);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.isInternal(enrichment_source_enum_1.EnrichmentSource.CUSTOM_RULES)).toBe(true);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.isInternal(enrichment_source_enum_1.EnrichmentSource.MANUAL_ANALYSIS)).toBe(true);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.isInternal(enrichment_source_enum_1.EnrichmentSource.VIRUSTOTAL)).toBe(false);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.isInternal(enrichment_source_enum_1.EnrichmentSource.SHODAN)).toBe(false);
            });
        });
        describe('isRealTime', () => {
            it('should correctly identify real-time sources', () => {
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.isRealTime(enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION)).toBe(true);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.isRealTime(enrichment_source_enum_1.EnrichmentSource.DNS_RESOLUTION)).toBe(true);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.isRealTime(enrichment_source_enum_1.EnrichmentSource.VIRUSTOTAL)).toBe(true);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.isRealTime(enrichment_source_enum_1.EnrichmentSource.NVD)).toBe(false);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.isRealTime(enrichment_source_enum_1.EnrichmentSource.CMDB)).toBe(false);
            });
        });
        describe('isBatchProcessed', () => {
            it('should correctly identify batch processed sources', () => {
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.isBatchProcessed(enrichment_source_enum_1.EnrichmentSource.NVD)).toBe(true);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.isBatchProcessed(enrichment_source_enum_1.EnrichmentSource.CVE_DATABASE)).toBe(true);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.isBatchProcessed(enrichment_source_enum_1.EnrichmentSource.CMDB)).toBe(true);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.isBatchProcessed(enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION)).toBe(false);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.isBatchProcessed(enrichment_source_enum_1.EnrichmentSource.DNS_RESOLUTION)).toBe(false);
            });
        });
        describe('getReliabilityScore', () => {
            it('should return appropriate reliability scores', () => {
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getReliabilityScore(enrichment_source_enum_1.EnrichmentSource.GOVERNMENT_INTEL)).toBe(95);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getReliabilityScore(enrichment_source_enum_1.EnrichmentSource.NVD)).toBe(95);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getReliabilityScore(enrichment_source_enum_1.EnrichmentSource.COMMERCIAL_THREAT_INTEL)).toBe(90);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getReliabilityScore(enrichment_source_enum_1.EnrichmentSource.VIRUSTOTAL)).toBe(85);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getReliabilityScore(enrichment_source_enum_1.EnrichmentSource.OSINT)).toBe(70);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getReliabilityScore(enrichment_source_enum_1.EnrichmentSource.UNKNOWN)).toBe(30);
                // Should return default for unmapped sources
                const defaultScore = enrichment_source_enum_1.EnrichmentSourceUtils.getReliabilityScore(enrichment_source_enum_1.EnrichmentSource.OTHER);
                expect(defaultScore).toBe(50);
            });
        });
        describe('getFreshnessRequirement', () => {
            it('should return appropriate freshness requirements in hours', () => {
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getFreshnessRequirement(enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION)).toBe(0.25); // 15 minutes
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getFreshnessRequirement(enrichment_source_enum_1.EnrichmentSource.DNS_RESOLUTION)).toBe(0.5); // 30 minutes
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getFreshnessRequirement(enrichment_source_enum_1.EnrichmentSource.COMMERCIAL_THREAT_INTEL)).toBe(1); // 1 hour
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getFreshnessRequirement(enrichment_source_enum_1.EnrichmentSource.NVD)).toBe(24); // Daily
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getFreshnessRequirement(enrichment_source_enum_1.EnrichmentSource.CMDB)).toBe(168); // Weekly
                // Should return default for unmapped sources
                const defaultFreshness = enrichment_source_enum_1.EnrichmentSourceUtils.getFreshnessRequirement(enrichment_source_enum_1.EnrichmentSource.OTHER);
                expect(defaultFreshness).toBe(24);
            });
        });
        describe('getCostCategory', () => {
            it('should return appropriate cost categories', () => {
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getCostCategory(enrichment_source_enum_1.EnrichmentSource.OSINT)).toBe('free');
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getCostCategory(enrichment_source_enum_1.EnrichmentSource.NVD)).toBe('free');
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getCostCategory(enrichment_source_enum_1.EnrichmentSource.VIRUSTOTAL)).toBe('low');
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getCostCategory(enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION)).toBe('medium');
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getCostCategory(enrichment_source_enum_1.EnrichmentSource.COMMERCIAL_THREAT_INTEL)).toBe('high');
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getCostCategory(enrichment_source_enum_1.EnrichmentSource.GOVERNMENT_INTEL)).toBe('enterprise');
                // Should return default for unmapped sources
                const defaultCost = enrichment_source_enum_1.EnrichmentSourceUtils.getCostCategory(enrichment_source_enum_1.EnrichmentSource.OTHER);
                expect(defaultCost).toBe('medium');
            });
        });
        describe('getRateLimit', () => {
            it('should return appropriate rate limits', () => {
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getRateLimit(enrichment_source_enum_1.EnrichmentSource.VIRUSTOTAL)).toBe(4);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getRateLimit(enrichment_source_enum_1.EnrichmentSource.SHODAN)).toBe(1);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getRateLimit(enrichment_source_enum_1.EnrichmentSource.DNS_RESOLUTION)).toBe(1000);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getRateLimit(enrichment_source_enum_1.EnrichmentSource.COMMERCIAL_THREAT_INTEL)).toBe(1000);
                // Should return default for unmapped sources
                const defaultRate = enrichment_source_enum_1.EnrichmentSourceUtils.getRateLimit(enrichment_source_enum_1.EnrichmentSource.OTHER);
                expect(defaultRate).toBe(100);
            });
        });
        describe('getSupportedDataTypes', () => {
            it('should return supported data types for sources', () => {
                const ipReputationTypes = enrichment_source_enum_1.EnrichmentSourceUtils.getSupportedDataTypes(enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION);
                expect(ipReputationTypes).toContain('ip_address');
                expect(ipReputationTypes).toContain('reputation_score');
                const virusTotalTypes = enrichment_source_enum_1.EnrichmentSourceUtils.getSupportedDataTypes(enrichment_source_enum_1.EnrichmentSource.VIRUSTOTAL);
                expect(virusTotalTypes).toContain('file_hash');
                expect(virusTotalTypes).toContain('url');
                expect(virusTotalTypes).toContain('domain');
                const cveTypes = enrichment_source_enum_1.EnrichmentSourceUtils.getSupportedDataTypes(enrichment_source_enum_1.EnrichmentSource.CVE_DATABASE);
                expect(cveTypes).toContain('cve_id');
                expect(cveTypes).toContain('cvss_score');
                // Should return default for unmapped sources
                const defaultTypes = enrichment_source_enum_1.EnrichmentSourceUtils.getSupportedDataTypes(enrichment_source_enum_1.EnrichmentSource.OTHER);
                expect(defaultTypes).toContain('generic_data');
            });
        });
        describe('getDescription', () => {
            it('should return meaningful descriptions', () => {
                const virusTotalDesc = enrichment_source_enum_1.EnrichmentSourceUtils.getDescription(enrichment_source_enum_1.EnrichmentSource.VIRUSTOTAL);
                expect(virusTotalDesc).toContain('VirusTotal');
                const osintDesc = enrichment_source_enum_1.EnrichmentSourceUtils.getDescription(enrichment_source_enum_1.EnrichmentSource.OSINT);
                expect(osintDesc).toContain('Open source intelligence');
                const nvdDesc = enrichment_source_enum_1.EnrichmentSourceUtils.getDescription(enrichment_source_enum_1.EnrichmentSource.NVD);
                expect(nvdDesc).toContain('National Vulnerability Database');
                // Should return formatted description for unmapped sources
                const otherDesc = enrichment_source_enum_1.EnrichmentSourceUtils.getDescription(enrichment_source_enum_1.EnrichmentSource.OTHER);
                expect(typeof otherDesc).toBe('string');
                expect(otherDesc.length).toBeGreaterThan(0);
            });
        });
        describe('getCategory', () => {
            it('should return correct categories', () => {
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getCategory(enrichment_source_enum_1.EnrichmentSource.COMMERCIAL_THREAT_INTEL)).toBe('Threat Intelligence');
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getCategory(enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION)).toBe('Reputation Services');
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getCategory(enrichment_source_enum_1.EnrichmentSource.VIRUSTOTAL)).toBe('External APIs');
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getCategory(enrichment_source_enum_1.EnrichmentSource.NVD)).toBe('Vulnerability');
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getCategory(enrichment_source_enum_1.EnrichmentSource.CMDB)).toBe('Asset Management');
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getCategory(enrichment_source_enum_1.EnrichmentSource.ACTIVE_DIRECTORY)).toBe('Identity');
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getCategory(enrichment_source_enum_1.EnrichmentSource.DNS_RESOLUTION)).toBe('Network');
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getCategory(enrichment_source_enum_1.EnrichmentSource.ML_MODELS)).toBe('Analytics');
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getCategory(enrichment_source_enum_1.EnrichmentSource.AWS_SECURITY)).toBe('Cloud');
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getCategory(enrichment_source_enum_1.EnrichmentSource.SANDBOX)).toBe('Analysis');
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.getCategory(enrichment_source_enum_1.EnrichmentSource.INTERNAL_SECURITY_TOOLS)).toBe('Internal');
            });
        });
        describe('isValid', () => {
            it('should validate enrichment source strings', () => {
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.isValid('virustotal')).toBe(true);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.isValid('ip_reputation')).toBe(true);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.isValid('invalid_source')).toBe(false);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.isValid('')).toBe(false);
            });
        });
        describe('fromString', () => {
            it('should convert string to enrichment source', () => {
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.fromString('virustotal')).toBe(enrichment_source_enum_1.EnrichmentSource.VIRUSTOTAL);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.fromString('ip_reputation')).toBe(enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.fromString('OSINT')).toBe(enrichment_source_enum_1.EnrichmentSource.OSINT);
                expect(enrichment_source_enum_1.EnrichmentSourceUtils.fromString('invalid_source')).toBeNull();
            });
        });
        describe('getEnrichmentPriority', () => {
            it('should calculate enrichment priority correctly', () => {
                const highReliabilityPriority = enrichment_source_enum_1.EnrichmentSourceUtils.getEnrichmentPriority(enrichment_source_enum_1.EnrichmentSource.GOVERNMENT_INTEL, 'threat_intel');
                const lowReliabilityPriority = enrichment_source_enum_1.EnrichmentSourceUtils.getEnrichmentPriority(enrichment_source_enum_1.EnrichmentSource.UNKNOWN, 'generic');
                expect(highReliabilityPriority).toBeGreaterThan(lowReliabilityPriority);
                expect(highReliabilityPriority).toBeGreaterThanOrEqual(0);
                expect(highReliabilityPriority).toBeLessThanOrEqual(100);
            });
            it('should boost priority for real-time sources', () => {
                const realTimePriority = enrichment_source_enum_1.EnrichmentSourceUtils.getEnrichmentPriority(enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION, 'ip_address');
                const batchPriority = enrichment_source_enum_1.EnrichmentSourceUtils.getEnrichmentPriority(enrichment_source_enum_1.EnrichmentSource.NVD, 'vulnerability');
                // Real-time sources should get priority boost
                expect(realTimePriority).toBeGreaterThan(batchPriority);
            });
        });
        describe('getRefreshInterval', () => {
            it('should return refresh interval in minutes', () => {
                const ipReputationInterval = enrichment_source_enum_1.EnrichmentSourceUtils.getRefreshInterval(enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION);
                expect(ipReputationInterval).toBe(15); // 0.25 hours * 60 minutes
                const nvdInterval = enrichment_source_enum_1.EnrichmentSourceUtils.getRefreshInterval(enrichment_source_enum_1.EnrichmentSource.NVD);
                expect(ipReputationInterval).toBeLessThan(nvdInterval); // Real-time should refresh more frequently
                // Should have minimum of 1 minute
                const allIntervals = [
                    enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION,
                    enrichment_source_enum_1.EnrichmentSource.DNS_RESOLUTION,
                    enrichment_source_enum_1.EnrichmentSource.ML_INFERENCE
                ].map(source => enrichment_source_enum_1.EnrichmentSourceUtils.getRefreshInterval(source));
                allIntervals.forEach(interval => {
                    expect(interval).toBeGreaterThanOrEqual(1);
                });
            });
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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