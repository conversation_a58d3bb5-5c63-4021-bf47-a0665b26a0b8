101b4efb3ca6a87a9e969265664ef067
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const supertest_1 = __importDefault(require("supertest"));
const app_module_1 = require("../../app.module");
describe('Error Scenarios (e2e)', () => {
    let app;
    let authToken;
    beforeAll(async () => {
        const moduleFixture = await testing_1.Test.createTestingModule({
            imports: [
                config_1.ConfigModule.forRoot({
                    isGlobal: true,
                    envFilePath: '.env.test',
                }),
                app_module_1.AppModule,
            ],
        }).compile();
        app = moduleFixture.createNestApplication();
        await app.init();
        // Setup authentication for protected endpoints
        const authResponse = await (0, supertest_1.default)(app.getHttpServer())
            .post('/api/v1/auth/register')
            .send({
            email: '<EMAIL>',
            password: 'SecurePassword123!',
            name: 'Error Test User',
        });
        authToken = authResponse.body.data.tokens.accessToken;
    });
    afterAll(async () => {
        await app.close();
    });
    describe('HTTP Error Codes', () => {
        it('should return 400 for bad request', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/items')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                // Missing required fields
                description: 'Missing name field',
            })
                .expect(400);
            expect(response.body).toEqual({
                statusCode: 400,
                message: expect.any(String),
                error: 'Bad Request',
                timestamp: expect.any(String),
                path: '/api/v1/test/items',
                method: 'POST',
                requestId: expect.any(String),
            });
        });
        it('should return 401 for unauthorized access', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/auth/profile')
                .expect(401);
            expect(response.body).toEqual({
                statusCode: 401,
                message: 'Unauthorized',
                error: 'Unauthorized',
                timestamp: expect.any(String),
                path: '/api/v1/auth/profile',
                method: 'GET',
                requestId: expect.any(String),
            });
        });
        it('should return 403 for forbidden access', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/admin/users')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(403);
            expect(response.body).toEqual({
                statusCode: 403,
                message: 'Forbidden resource',
                error: 'Forbidden',
                timestamp: expect.any(String),
                path: '/api/v1/admin/users',
                method: 'GET',
                requestId: expect.any(String),
            });
        });
        it('should return 404 for not found resources', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/test/items/non-existent-id')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(404);
            expect(response.body).toEqual({
                statusCode: 404,
                message: 'Resource not found',
                error: 'Not Found',
                timestamp: expect.any(String),
                path: '/api/v1/test/items/non-existent-id',
                method: 'GET',
                requestId: expect.any(String),
            });
        });
        it('should return 405 for method not allowed', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .patch('/api/v1/health') // Assuming PATCH is not allowed on health endpoint
                .expect(405);
            expect(response.body.statusCode).toBe(405);
            expect(response.body.error).toBe('Method Not Allowed');
        });
        it('should return 409 for conflict errors', async () => {
            // First registration
            await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/register')
                .send({
                email: '<EMAIL>',
                password: 'SecurePassword123!',
                name: 'Conflict Test User',
            })
                .expect(201);
            // Duplicate registration
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/register')
                .send({
                email: '<EMAIL>',
                password: 'AnotherPassword123!',
                name: 'Another User',
            })
                .expect(409);
            expect(response.body).toEqual({
                statusCode: 409,
                message: 'Email already exists',
                error: 'Conflict',
                timestamp: expect.any(String),
                path: '/api/v1/auth/register',
                method: 'POST',
                requestId: expect.any(String),
            });
        });
        it('should return 413 for payload too large', async () => {
            const largePayload = {
                name: 'Large Item',
                description: 'A'.repeat(100000), // Very large description
                category: 'test',
                data: Array.from({ length: 10000 }, (_, i) => ({
                    key: `key${i}`,
                    value: 'x'.repeat(1000),
                })),
            };
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/items')
                .set('Authorization', `Bearer ${authToken}`)
                .send(largePayload);
            // Should either accept or reject based on size limits
            if (response.status === 413) {
                expect(response.body.statusCode).toBe(413);
                expect(response.body.error).toBe('Payload Too Large');
            }
        });
        it('should return 415 for unsupported media type', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/items')
                .set('Authorization', `Bearer ${authToken}`)
                .set('Content-Type', 'application/xml')
                .send('<item><name>XML Item</name></item>')
                .expect(415);
            expect(response.body.statusCode).toBe(415);
            expect(response.body.error).toBe('Unsupported Media Type');
        });
        it('should return 422 for unprocessable entity', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/items')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                name: 'Valid Name',
                description: 'Valid Description',
                category: 'invalid-category-that-does-not-exist',
            })
                .expect(422);
            expect(response.body.statusCode).toBe(422);
            expect(response.body.error).toBe('Unprocessable Entity');
        });
        it('should return 429 for rate limiting', async () => {
            // Make multiple rapid requests to trigger rate limiting
            const requests = Array.from({ length: 50 }, () => (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/health'));
            const responses = await Promise.all(requests.map(req => req.then(res => ({ status: res.status, headers: res.headers, body: res.body }))
                .catch(err => ({
                status: err.response?.status || 500,
                headers: err.response?.headers || {},
                body: err.response?.body || {}
            }))));
            // Some requests should be rate limited
            const rateLimitedResponses = responses.filter(r => r.status === 429);
            if (rateLimitedResponses.length > 0) {
                const rateLimitedResponse = rateLimitedResponses[0];
                expect(rateLimitedResponse.body.statusCode).toBe(429);
                expect(rateLimitedResponse.body.error).toBe('Too Many Requests');
                expect(rateLimitedResponse.headers['x-ratelimit-limit']).toBeDefined();
                expect(rateLimitedResponse.headers['x-ratelimit-remaining']).toBeDefined();
                expect(rateLimitedResponse.headers['retry-after']).toBeDefined();
            }
        });
    });
    describe('Validation Errors', () => {
        it('should handle missing required fields', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/items')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                description: 'Missing name field',
                category: 'test',
            })
                .expect(400);
            expect(response.body.message).toContain('name');
            expect(response.body.details).toEqual(expect.arrayContaining([
                expect.objectContaining({
                    field: 'name',
                    message: expect.any(String),
                }),
            ]));
        });
        it('should handle invalid field types', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/items')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                name: 123, // Should be string
                description: true, // Should be string
                category: ['invalid'], // Should be string
            })
                .expect(400);
            expect(response.body.details).toEqual(expect.arrayContaining([
                expect.objectContaining({
                    field: 'name',
                    message: expect.stringContaining('string'),
                }),
                expect.objectContaining({
                    field: 'description',
                    message: expect.stringContaining('string'),
                }),
                expect.objectContaining({
                    field: 'category',
                    message: expect.stringContaining('string'),
                }),
            ]));
        });
        it('should handle field length validation', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/items')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                name: 'a'.repeat(256), // Too long
                description: 'b'.repeat(1001), // Too long
                category: '', // Too short
            })
                .expect(400);
            expect(response.body.details).toEqual(expect.arrayContaining([
                expect.objectContaining({
                    field: 'name',
                    message: expect.stringContaining('length'),
                }),
                expect.objectContaining({
                    field: 'description',
                    message: expect.stringContaining('length'),
                }),
                expect.objectContaining({
                    field: 'category',
                    message: expect.stringContaining('empty'),
                }),
            ]));
        });
        it('should handle custom validation rules', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/security-events')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                eventType: 'vulnerability_detected',
                severity: 'invalid-severity', // Should be low, medium, high, critical
                ipAddress: '999.999.999.999', // Invalid IP
                cveId: 'INVALID-CVE-FORMAT', // Should be CVE-YYYY-NNNN format
            })
                .expect(400);
            expect(response.body.details).toEqual(expect.arrayContaining([
                expect.objectContaining({
                    field: 'severity',
                    message: expect.stringContaining('severity'),
                }),
                expect.objectContaining({
                    field: 'ipAddress',
                    message: expect.stringContaining('IP address'),
                }),
                expect.objectContaining({
                    field: 'cveId',
                    message: expect.stringContaining('CVE'),
                }),
            ]));
        });
        it('should handle nested object validation', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/complex-items')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                name: 'Complex Item',
                metadata: {
                    tags: 'should-be-array', // Should be array
                    priority: 'invalid-priority', // Should be number
                    settings: {
                        enabled: 'not-boolean', // Should be boolean
                        threshold: 'not-number', // Should be number
                    },
                },
            })
                .expect(400);
            expect(response.body.details).toEqual(expect.arrayContaining([
                expect.objectContaining({
                    field: 'metadata.tags',
                    message: expect.stringContaining('array'),
                }),
                expect.objectContaining({
                    field: 'metadata.priority',
                    message: expect.stringContaining('number'),
                }),
                expect.objectContaining({
                    field: 'metadata.settings.enabled',
                    message: expect.stringContaining('boolean'),
                }),
                expect.objectContaining({
                    field: 'metadata.settings.threshold',
                    message: expect.stringContaining('number'),
                }),
            ]));
        });
    });
    describe('Database Errors', () => {
        it('should handle database connection errors gracefully', async () => {
            // This would require mocking database failures
            // In a real test, you might temporarily disconnect the database
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/test/items')
                .set('Authorization', `Bearer ${authToken}`);
            // Should either succeed or return appropriate error
            if (response.status === 503) {
                expect(response.body).toEqual({
                    statusCode: 503,
                    message: 'Service temporarily unavailable',
                    error: 'Service Unavailable',
                    timestamp: expect.any(String),
                    path: '/api/v1/test/items',
                    method: 'GET',
                    requestId: expect.any(String),
                });
            }
            else {
                expect(response.status).toBe(200);
            }
        });
        it('should handle database constraint violations', async () => {
            // Create an item with unique constraint
            await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/unique-items')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                uniqueCode: 'UNIQUE-001',
                name: 'Unique Item',
            })
                .expect(201);
            // Try to create another item with same unique code
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/unique-items')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                uniqueCode: 'UNIQUE-001',
                name: 'Another Unique Item',
            })
                .expect(409);
            expect(response.body.message).toContain('already exists');
        });
        it('should handle database timeout errors', async () => {
            // This would require mocking slow database operations
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/test/slow-query')
                .set('Authorization', `Bearer ${authToken}`)
                .timeout(1000);
            // Should handle timeout gracefully
            if (response.status === 408) {
                expect(response.body.statusCode).toBe(408);
                expect(response.body.error).toBe('Request Timeout');
            }
        });
    });
    describe('External Service Errors', () => {
        it('should handle external API failures', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/threat-intelligence/external-data')
                .set('Authorization', `Bearer ${authToken}`);
            // Should handle external service failures gracefully
            if (response.status === 503) {
                expect(response.body).toEqual({
                    statusCode: 503,
                    message: 'External service unavailable',
                    error: 'Service Unavailable',
                    timestamp: expect.any(String),
                    path: '/api/v1/threat-intelligence/external-data',
                    method: 'GET',
                    requestId: expect.any(String),
                });
            }
        });
        it('should handle external API timeout', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/vulnerability-management/scan-external')
                .set('Authorization', `Bearer ${authToken}`)
                .timeout(5000);
            // Should handle external API timeouts
            if (response.status === 504) {
                expect(response.body.statusCode).toBe(504);
                expect(response.body.error).toBe('Gateway Timeout');
            }
        });
        it('should handle malformed external API responses', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/analytics/external-metrics')
                .set('Authorization', `Bearer ${authToken}`);
            // Should handle malformed responses gracefully
            expect([200, 502, 503]).toContain(response.status);
            if (response.status === 502) {
                expect(response.body.error).toBe('Bad Gateway');
            }
        });
    });
    describe('Authentication Errors', () => {
        it('should handle expired tokens', async () => {
            const expiredToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.invalid';
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/auth/profile')
                .set('Authorization', `Bearer ${expiredToken}`)
                .expect(401);
            expect(response.body.message).toContain('expired');
        });
        it('should handle malformed tokens', async () => {
            const malformedTokens = [
                'not-a-jwt-token',
                'header.payload', // Missing signature
                'header.payload.signature.extra', // Too many parts
                '', // Empty token
            ];
            for (const token of malformedTokens) {
                const response = await (0, supertest_1.default)(app.getHttpServer())
                    .get('/api/v1/auth/profile')
                    .set('Authorization', `Bearer ${token}`)
                    .expect(401);
                expect(response.body.message).toContain('Invalid token');
            }
        });
        it('should handle revoked tokens', async () => {
            // Login to get a token
            const loginResponse = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/login')
                .send({
                email: '<EMAIL>',
                password: 'SecurePassword123!',
            })
                .expect(200);
            const token = loginResponse.body.data.tokens.accessToken;
            // Logout to revoke the token
            await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/logout')
                .set('Authorization', `Bearer ${token}`)
                .expect(200);
            // Try to use the revoked token
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/auth/profile')
                .set('Authorization', `Bearer ${token}`)
                .expect(401);
            expect(response.body.message).toContain('Invalid token');
        });
    });
    describe('File Upload Errors', () => {
        it('should handle unsupported file types', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/upload')
                .set('Authorization', `Bearer ${authToken}`)
                .attach('file', Buffer.from('<?php echo "malicious"; ?>'), {
                filename: 'malicious.php',
                contentType: 'application/x-php',
            })
                .expect(415);
            expect(response.body.message).toContain('file type not supported');
        });
        it('should handle file size limits', async () => {
            const largeFile = Buffer.alloc(10 * 1024 * 1024); // 10MB file
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/upload')
                .set('Authorization', `Bearer ${authToken}`)
                .attach('file', largeFile, {
                filename: 'large-file.txt',
                contentType: 'text/plain',
            });
            // Should either accept or reject based on size limits
            if (response.status === 413) {
                expect(response.body.message).toContain('file too large');
            }
        });
        it('should handle corrupted files', async () => {
            const corruptedFile = Buffer.from([0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10]); // Corrupted JPEG header
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/upload')
                .set('Authorization', `Bearer ${authToken}`)
                .attach('file', corruptedFile, {
                filename: 'corrupted.jpg',
                contentType: 'image/jpeg',
            });
            // Should handle corrupted files gracefully
            expect([400, 422]).toContain(response.status);
        });
    });
    describe('Concurrent Request Errors', () => {
        it('should handle race conditions gracefully', async () => {
            // Create multiple concurrent requests that might cause race conditions
            const requests = Array.from({ length: 10 }, () => (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/counter-items')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                name: 'Counter Item',
                increment: 1,
            }));
            const responses = await Promise.all(requests.map(req => req.then(res => ({ status: res.status, body: res.body }))
                .catch(err => ({
                status: err.response?.status || 500,
                body: err.response?.body || {}
            }))));
            // All requests should either succeed or fail gracefully
            responses.forEach(response => {
                expect([200, 201, 409, 500]).toContain(response.status);
            });
        });
        it('should handle deadlock situations', async () => {
            // This would require specific database operations that could cause deadlocks
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/complex-transaction')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                operations: [
                    { type: 'create', data: { name: 'Item 1' } },
                    { type: 'update', id: 'existing-id', data: { name: 'Updated' } },
                    { type: 'delete', id: 'another-id' },
                ],
            });
            // Should handle deadlocks gracefully
            expect([200, 409, 500]).toContain(response.status);
        });
    });
    describe('Memory and Resource Errors', () => {
        it('should handle memory exhaustion gracefully', async () => {
            // Try to create a request that might cause memory issues
            const memoryIntensivePayload = {
                name: 'Memory Test',
                data: Array.from({ length: 100000 }, (_, i) => ({
                    id: i,
                    value: 'x'.repeat(1000),
                    nested: {
                        deep: {
                            data: Array.from({ length: 100 }, (_, j) => `item-${j}`),
                        },
                    },
                })),
            };
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/memory-intensive')
                .set('Authorization', `Bearer ${authToken}`)
                .send(memoryIntensivePayload);
            // Should either process or reject gracefully
            expect([201, 413, 500]).toContain(response.status);
        });
        it('should handle CPU intensive operations', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/cpu-intensive')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                iterations: 1000000,
                complexity: 'high',
            })
                .timeout(10000);
            // Should handle CPU intensive operations within reasonable time
            expect([200, 408, 503]).toContain(response.status);
        });
    });
    describe('Error Response Consistency', () => {
        it('should maintain consistent error format across all endpoints', async () => {
            const errorEndpoints = [
                { method: 'get', path: '/api/v1/nonexistent', expectedStatus: 404 },
                { method: 'post', path: '/api/v1/test/items', body: {}, expectedStatus: 400 },
                { method: 'get', path: '/api/v1/auth/profile', expectedStatus: 401 },
                { method: 'get', path: '/api/v1/admin/users', expectedStatus: 403 },
            ];
            for (const endpoint of errorEndpoints) {
                let req = (0, supertest_1.default)(app.getHttpServer())[endpoint.method](endpoint.path);
                if (endpoint.path.includes('admin') || endpoint.path.includes('auth/profile')) {
                    // Don't set auth for these to test 401/403
                }
                else if (endpoint.body) {
                    req = req.set('Authorization', `Bearer ${authToken}`).send(endpoint.body);
                }
                else {
                    req = req.set('Authorization', `Bearer ${authToken}`);
                }
                const response = await req.expect(endpoint.expectedStatus);
                // All error responses should have consistent structure
                expect(response.body).toEqual({
                    statusCode: endpoint.expectedStatus,
                    message: expect.any(String),
                    error: expect.any(String),
                    timestamp: expect.any(String),
                    path: endpoint.path,
                    method: endpoint.method.toUpperCase(),
                    requestId: expect.any(String),
                });
            }
        });
        it('should include correlation IDs in error responses', async () => {
            const correlationId = 'test-correlation-123';
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/nonexistent')
                .set('X-Correlation-ID', correlationId)
                .expect(404);
            expect(response.body.requestId).toBeDefined();
            // In a real implementation, correlation ID would be included
        });
        it('should not expose sensitive information in errors', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/test/database-error')
                .set('Authorization', `Bearer ${authToken}`);
            // Should not expose database connection strings, internal paths, etc.
            expect(response.body.message).not.toContain('password');
            expect(response.body.message).not.toContain('localhost');
            expect(response.body.message).not.toContain('/home/');
            expect(response.body.message).not.toContain('node_modules');
            expect(response.body).not.toHaveProperty('stack');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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