e3f339000afd56661992f1cf6f9d60cf
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const cors_config_1 = require("../cors.config");
describe('CorsConfig', () => {
    let corsConfig;
    let configService;
    beforeEach(async () => {
        const mockConfigService = {
            get: jest.fn(),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                cors_config_1.CorsConfig,
                {
                    provide: config_1.ConfigService,
                    useValue: mockConfigService,
                },
            ],
        }).compile();
        corsConfig = module.get(cors_config_1.CorsConfig);
        configService = module.get(config_1.ConfigService);
    });
    describe('getCorsOptions', () => {
        it('should return production CORS options for production environment', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'production';
                if (key === 'cors.allowedOrigins')
                    return ['https://app.sentinel.com'];
                return defaultValue;
            });
            const corsOptions = corsConfig.getCorsOptions();
            expect(corsOptions.credentials).toBe(true);
            expect(corsOptions.maxAge).toBe(86400);
            expect(corsOptions.methods).toEqual(['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS']);
            expect(typeof corsOptions.origin).toBe('function');
        });
        it('should return staging CORS options for staging environment', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'staging';
                if (key === 'cors.allowedOrigins')
                    return ['https://staging.sentinel.com'];
                return defaultValue;
            });
            const corsOptions = corsConfig.getCorsOptions();
            expect(corsOptions.credentials).toBe(true);
            expect(corsOptions.maxAge).toBe(3600);
            expect(corsOptions.methods).toContain('HEAD');
        });
        it('should return development CORS options for development environment', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'development';
                return defaultValue;
            });
            const corsOptions = corsConfig.getCorsOptions();
            expect(corsOptions.origin).toBe(true);
            expect(corsOptions.credentials).toBe(true);
            expect(corsOptions.maxAge).toBe(300);
        });
        it('should return test CORS options for test environment', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'test';
                return defaultValue;
            });
            const corsOptions = corsConfig.getCorsOptions();
            expect(corsOptions.origin).toBe(true);
            expect(corsOptions.allowedHeaders).toBe('*');
            expect(corsOptions.exposedHeaders).toBe('*');
            expect(corsOptions.maxAge).toBe(0);
        });
    });
    describe('production CORS origin validation', () => {
        beforeEach(() => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'production';
                if (key === 'cors.allowedOrigins')
                    return ['https://app.sentinel.com', 'https://admin.sentinel.com'];
                return defaultValue;
            });
        });
        it('should allow requests from whitelisted origins', (done) => {
            const corsOptions = corsConfig.getCorsOptions();
            const originCallback = corsOptions.origin;
            originCallback('https://app.sentinel.com', (error, allowed) => {
                expect(error).toBeNull();
                expect(allowed).toBe(true);
                done();
            });
        });
        it('should allow requests with no origin (mobile apps, Postman)', (done) => {
            const corsOptions = corsConfig.getCorsOptions();
            const originCallback = corsOptions.origin;
            originCallback(undefined, (error, allowed) => {
                expect(error).toBeNull();
                expect(allowed).toBe(true);
                done();
            });
        });
        it('should reject requests from non-whitelisted origins', (done) => {
            const corsOptions = corsConfig.getCorsOptions();
            const originCallback = corsOptions.origin;
            originCallback('https://malicious.com', (error, allowed) => {
                expect(error).toBeInstanceOf(Error);
                expect(error?.message).toBe('Not allowed by CORS');
                expect(allowed).toBe(false);
                done();
            });
        });
    });
    describe('validateCorsConfig', () => {
        it('should validate valid CORS configuration', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'development';
                return defaultValue;
            });
            const isValid = corsConfig.validateCorsConfig();
            expect(isValid).toBe(true);
        });
        it('should fail validation for production with wildcard origin', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'production';
                return defaultValue;
            });
            // Mock getCorsOptions to return invalid config
            jest.spyOn(corsConfig, 'getProductionCorsOptions').mockReturnValue({
                origin: true,
                methods: ['GET', 'POST'],
                allowedHeaders: ['Content-Type'],
                credentials: true,
            });
            const isValid = corsConfig.validateCorsConfig();
            expect(isValid).toBe(false);
        });
        it('should handle validation errors gracefully', () => {
            configService.get.mockImplementation(() => {
                throw new Error('Configuration error');
            });
            const isValid = corsConfig.validateCorsConfig();
            expect(isValid).toBe(false);
        });
    });
    describe('getCorsConfigSummary', () => {
        it('should return configuration summary for development', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'development';
                return defaultValue;
            });
            const summary = corsConfig.getCorsConfigSummary();
            expect(summary.environment).toBe('development');
            expect(summary.allowsCredentials).toBe(true);
            expect(summary.originPolicy).toBe('allow-all');
            expect(typeof summary.maxAge).toBe('number');
        });
        it('should return configuration summary for production', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'production';
                if (key === 'cors.allowedOrigins')
                    return ['https://app.sentinel.com'];
                return defaultValue;
            });
            const summary = corsConfig.getCorsConfigSummary();
            expect(summary.environment).toBe('production');
            expect(summary.originPolicy).toBe('whitelist');
            expect(summary.maxAge).toBe(86400);
        });
        it('should handle array methods and headers', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'staging';
                return defaultValue;
            });
            const summary = corsConfig.getCorsConfigSummary();
            expect(typeof summary.methodsCount).toBe('number');
            expect(typeof summary.headersCount).toBe('number');
        });
    });
    describe('environment-specific configurations', () => {
        it('should include debug headers in staging', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'staging';
                return defaultValue;
            });
            const corsOptions = corsConfig.getCorsOptions();
            expect(corsOptions.allowedHeaders).toContain('X-Debug-Mode');
            expect(corsOptions.exposedHeaders).toContain('X-Debug-Info');
        });
        it('should include test headers in development', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'development';
                return defaultValue;
            });
            const corsOptions = corsConfig.getCorsOptions();
            expect(corsOptions.allowedHeaders).toContain('X-Test-Mode');
            expect(corsOptions.exposedHeaders).toContain('X-Performance-Metrics');
        });
        it('should have strict headers in production', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'production';
                if (key === 'cors.allowedOrigins')
                    return ['https://app.sentinel.com'];
                return defaultValue;
            });
            const corsOptions = corsConfig.getCorsOptions();
            expect(corsOptions.allowedHeaders).not.toContain('X-Debug-Mode');
            expect(corsOptions.allowedHeaders).not.toContain('X-Test-Mode');
            expect(corsOptions.maxAge).toBe(86400);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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