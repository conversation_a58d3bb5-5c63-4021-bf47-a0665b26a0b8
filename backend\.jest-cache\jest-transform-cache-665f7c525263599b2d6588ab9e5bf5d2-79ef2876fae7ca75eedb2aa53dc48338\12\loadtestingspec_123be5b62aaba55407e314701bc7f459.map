{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\tests\\performance\\load-testing.spec.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6CAAsD;AAEtD,6CAAgD;AAChD,2CAA8C;AAC9C,mDAAqC;AACrC,6DAAyD;AACzD,sFAA2E;AAC3E,gEAA4D;AAC5D,kEAA8D;AAE9D;;;;;;;;;GASG;AACH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;IAC5C,IAAI,GAAqB,CAAC;IAC1B,IAAI,SAAwB,CAAC;IAE7B,MAAM,QAAQ,GAAG;QACf,EAAE,EAAE,UAAU;QACd,KAAK,EAAE,kBAAkB;QACzB,KAAK,EAAE,CAAC,kBAAkB,CAAC;KAC5B,CAAC;IAEF,MAAM,aAAa,GAAG;QACpB,WAAW,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC;KACjC,CAAC;IAEF,MAAM,cAAc,GAAG;QACrB,WAAW,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC;KACjC,CAAC;IAEF,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,SAAS,GAAG,MAAM,cAAI,CAAC,mBAAmB,CAAC;YACzC,OAAO,EAAE;gBACP,qBAAY,CAAC,OAAO,CAAC;oBACnB,QAAQ,EAAE,IAAI;oBACd,WAAW,EAAE,WAAW;iBACzB,CAAC;gBACF,uBAAa,CAAC,OAAO,CAAC;oBACpB,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,WAAW;oBAC7C,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,MAAM,CAAC;oBAClD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,MAAM;oBAChD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,MAAM;oBAChD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,oBAAoB;oBAC1D,QAAQ,EAAE,CAAC,2CAAgB,CAAC;oBAC5B,WAAW,EAAE,IAAI;oBACjB,UAAU,EAAE,IAAI;oBAChB,OAAO,EAAE,KAAK;oBACd,wCAAwC;oBACxC,KAAK,EAAE;wBACL,GAAG,EAAE,EAAE,EAAE,gCAAgC;wBACzC,uBAAuB,EAAE,IAAI;wBAC7B,iBAAiB,EAAE,KAAK;qBACzB;iBACF,CAAC;gBACF,kCAAe;aAChB;SACF,CAAC;aACC,aAAa,CAAC,sBAAS,CAAC;aACxB,QAAQ,CAAC,aAAa,CAAC;aACvB,aAAa,CAAC,wBAAU,CAAC;aACzB,QAAQ,CAAC,cAAc,CAAC;aACxB,OAAO,EAAE,CAAC;QAEb,GAAG,GAAG,SAAS,CAAC,qBAAqB,EAAE,CAAC;QAExC,iCAAiC;QACjC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;YACzB,GAAG,CAAC,IAAI,GAAG,QAAQ,CAAC;YACpB,IAAI,EAAE,CAAC;QACT,CAAC,CAAC,CAAC;QAEH,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC;QAEjB,2CAA2C;QAC3C,MAAM,cAAc,EAAE,CAAC;IACzB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;QAClB,MAAM,SAAS,CAAC,KAAK,EAAE,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,KAAK,UAAU,cAAc;QAC3B,iDAAiD;QACjD,MAAM,iBAAiB,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YAC/D,IAAI,EAAE,2BAA2B,CAAC,EAAE;YACpC,WAAW,EAAE,2BAA2B,CAAC,mBAAmB;YAC5D,QAAQ,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO;YAC9C,IAAI,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO;YACvC,UAAU,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO;YAChD,KAAK,EAAE,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,oBAAoB,CAAC,EAAE;YACnF,UAAU,EAAE;gBACV;oBACE,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,MAAM;oBACZ,QAAQ,EAAE,IAAI;oBACd,YAAY,EAAE,IAAI;iBACnB;aACF;YACD,aAAa,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC;SAChC,CAAC,CAAC,CAAC;QAEJ,KAAK,MAAM,SAAS,IAAI,iBAAiB,EAAE,CAAC;YAC1C,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,IAAI,CAAC,sCAAsC,CAAC;iBAC5C,IAAI,CAAC,SAAS,CAAC,CAAC;QACrB,CAAC;IACH,CAAC;IAED,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACrE,MAAM,kBAAkB,GAAG,EAAE,CAAC;YAC9B,MAAM,gBAAgB,GAAG,EAAE,CAAC;YAC5B,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,kBAAkB,GAAG,gBAAgB,CAAC,CAAC;YAEjE,MAAM,gBAAgB,GAAa,EAAE,CAAC;YAEtC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC;gBAC7C,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,gBAAgB,EAAE,EAAE,KAAK,IAAI,EAAE;oBACxE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;oBAE7B,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;yBAChD,GAAG,CAAC,sCAAsC,CAAC;yBAC3C,KAAK,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;oBAEjC,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;oBAC5C,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBAEpC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBAClC,OAAO,YAAY,CAAC;gBACtB,CAAC,CAAC,CAAC;gBAEH,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YACnC,CAAC;YAED,yBAAyB;YACzB,MAAM,mBAAmB,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,gBAAgB,CAAC,MAAM,CAAC;YAC5G,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,CAAC;YACtD,MAAM,eAAe,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;YAE3G,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;YAChE,OAAO,CAAC,GAAG,CAAC,4BAA4B,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAC5E,OAAO,CAAC,GAAG,CAAC,wBAAwB,eAAe,IAAI,CAAC,CAAC;YACzD,OAAO,CAAC,GAAG,CAAC,sBAAsB,eAAe,IAAI,CAAC,CAAC;YAEvD,MAAM,CAAC,mBAAmB,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,mCAAmC;YACnF,MAAM,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,4CAA4C;YACxF,MAAM,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,gCAAgC;QAC9E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,MAAM,kBAAkB,GAAG,EAAE,CAAC;YAC9B,MAAM,aAAa,GAAa,EAAE,CAAC;YAEnC,MAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,kBAAkB,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC/E,MAAM,SAAS,GAAG;oBAChB,IAAI,EAAE,0BAA0B,CAAC,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;oBACjD,WAAW,EAAE,0BAA0B,CAAC,EAAE;oBAC1C,QAAQ,EAAE,YAAY;oBACtB,IAAI,EAAE,SAAS;oBACf,UAAU,EAAE,YAAY;oBACxB,KAAK,EAAE,4CAA4C,CAAC,EAAE;oBACtD,UAAU,EAAE,EAAE;oBACd,aAAa,EAAE,CAAC,KAAK,CAAC;iBACvB,CAAC;gBAEF,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAE7B,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,IAAI,CAAC,sCAAsC,CAAC;qBAC5C,IAAI,CAAC,SAAS,CAAC,CAAC;gBAEnB,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAC5C,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAEjC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAClC,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YAElC,MAAM,mBAAmB,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,CAAC;YACtG,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC;YAEnD,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;YACjE,OAAO,CAAC,GAAG,CAAC,4BAA4B,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAC5E,OAAO,CAAC,GAAG,CAAC,wBAAwB,eAAe,IAAI,CAAC,CAAC;YAEzD,MAAM,CAAC,mBAAmB,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,oCAAoC;YACpF,MAAM,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,gCAAgC;QAC9E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,MAAM,SAAS,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;YAChC,MAAM,kBAAkB,GAA2B,EAAE,CAAC;YAEtD,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBACjC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAE7B,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,sCAAsC,CAAC;qBAC3C,KAAK,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;gBAEvC,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAC5C,kBAAkB,CAAC,QAAQ,CAAC,GAAG,YAAY,CAAC;gBAE5C,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;YAC/E,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;YACvC,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;gBAC1D,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC;YAEH,qDAAqD;YACrD,MAAM,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC,kBAAkB,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,WAAW,GAAG,CAAC,aAAa,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;YAC7E,MAAM,aAAa,GAA2B,EAAE,CAAC;YAEjD,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;gBAC/B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAE7B,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,sCAAsC,CAAC;qBAC3C,KAAK,CAAC;oBACL,MAAM,EAAE,IAAI;oBACZ,IAAI,EAAE,CAAC;oBACP,KAAK,EAAE,EAAE;iBACV,CAAC,CAAC;gBAEL,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBACzC,aAAa,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC;gBAEhC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACpE,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;gBACrD,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,MAAM,IAAI,IAAI,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;YAEH,4DAA4D;YAC5D,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBAC1C,MAAM,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,kBAAkB,GAAG;gBACzB,EAAE,QAAQ,EAAE,YAAY,EAAE;gBAC1B,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,QAAQ,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE;gBAC3C,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE;gBACpC,EAAE,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY,EAAE;aAClD,CAAC;YAEF,MAAM,aAAa,GAA2B,EAAE,CAAC;YAEjD,KAAK,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,kBAAkB,CAAC,OAAO,EAAE,EAAE,CAAC;gBAC5D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAE7B,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,sCAAsC,CAAC;qBAC3C,KAAK,CAAC;oBACL,GAAG,OAAO;oBACV,IAAI,EAAE,CAAC;oBACP,KAAK,EAAE,EAAE;iBACV,CAAC,CAAC;gBAEL,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBACzC,aAAa,CAAC,UAAU,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC;gBAEjD,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpC,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;gBACvD,OAAO,CAAC,GAAG,CAAC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;YAEH,4CAA4C;YAC5C,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBAC1C,MAAM,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,IAAI,kBAA0B,CAAC;QAE/B,SAAS,CAAC,KAAK,IAAI,EAAE;YACnB,oDAAoD;YACpD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,sCAAsC,CAAC;iBAC5C,IAAI,CAAC;gBACJ,IAAI,EAAE,oCAAoC;gBAC1C,WAAW,EAAE,2CAA2C;gBACxD,QAAQ,EAAE,YAAY;gBACtB,IAAI,EAAE,SAAS;gBACf,UAAU,EAAE,YAAY;gBACxB,KAAK,EAAE,0CAA0C;gBACjD,UAAU,EAAE,EAAE;gBACd,aAAa,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC;aACvC,CAAC,CAAC;YAEL,kBAAkB,GAAG,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,KAAK,IAAI,EAAE;YAC5E,MAAM,qBAAqB,GAAG,EAAE,CAAC;YACjC,MAAM,eAAe,GAAa,EAAE,CAAC;YAErC,MAAM,kBAAkB,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,qBAAqB,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;gBACtF,MAAM,WAAW,GAAG;oBAClB,kBAAkB;oBAClB,UAAU,EAAE,EAAE;oBACd,OAAO,EAAE,CAAC,KAAK,CAAC;oBAChB,QAAQ,EAAE,QAAQ;iBACnB,CAAC;gBAEF,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAE7B,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,IAAI,CAAC,8CAA8C,CAAC;qBACpD,IAAI,CAAC,WAAW,CAAC,CAAC;gBAErB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBACzC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAEhC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAE5C,OAAO,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC;YAChC,CAAC,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;YAExD,MAAM,gBAAgB,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,eAAe,CAAC,MAAM,CAAC;YACvG,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,CAAC;YAElD,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;YACpD,OAAO,CAAC,GAAG,CAAC,yBAAyB,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YACtE,OAAO,CAAC,GAAG,CAAC,qBAAqB,YAAY,IAAI,CAAC,CAAC;YACnD,OAAO,CAAC,GAAG,CAAC,qBAAqB,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;YAErD,MAAM,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,uBAAuB;YACpE,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,0BAA0B;YAC1B,MAAM,gBAAgB,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACxD,IAAI,CAAC,8CAA8C,CAAC;iBACpD,IAAI,CAAC;gBACJ,kBAAkB;gBAClB,UAAU,EAAE,EAAE;gBACd,OAAO,EAAE,CAAC,KAAK,CAAC;gBAChB,QAAQ,EAAE,MAAM;aACjB,CAAC,CAAC;YAEL,MAAM,QAAQ,GAAG,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC;YAEhD,8BAA8B;YAC9B,MAAM,gBAAgB,GAAa,EAAE,CAAC;YACtC,MAAM,cAAc,GAAG,EAAE,CAAC;YAE1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,CAAC,EAAE,EAAE,CAAC;gBACxC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAE7B,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,8CAA8C,QAAQ,EAAE,CAAC,CAAC;gBAEjE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBACzC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAEjC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAElC,6BAA6B;gBAC7B,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;YACzD,CAAC;YAED,MAAM,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,gBAAgB,CAAC,MAAM,CAAC;YACzG,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,CAAC;YAEnD,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;YACzC,OAAO,CAAC,GAAG,CAAC,yBAAyB,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YACtE,OAAO,CAAC,GAAG,CAAC,qBAAqB,YAAY,IAAI,CAAC,CAAC;YAEnD,MAAM,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,+BAA+B;YAC3E,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,0DAA0D,EAAE,KAAK,IAAI,EAAE;YACxE,MAAM,gBAAgB,GAAG;gBACvB,EAAE,UAAU,EAAE,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE;gBACxC,EAAE,UAAU,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE;gBACjD,EAAE,UAAU,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE;aAC9D,CAAC;YAEF,MAAM,kBAAkB,GAA2B,EAAE,CAAC;YAEtD,KAAK,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,gBAAgB,CAAC,OAAO,EAAE,EAAE,CAAC;gBACzD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAE7B,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,uDAAuD,CAAC;qBAC5D,KAAK,CAAC;oBACL,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC;oBACvC,SAAS,EAAE,MAAM,CAAC,SAAS;oBAC3B,iBAAiB,EAAE,IAAI;iBACxB,CAAC,CAAC;gBAEL,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAC/C,kBAAkB,CAAC,UAAU,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC;gBAE5D,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE,CAAC;YACvD,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;YAC3D,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;gBAC5D,OAAO,CAAC,GAAG,CAAC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;YAEH,qDAAqD;YACrD,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBAC/C,MAAM,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;YACpE,MAAM,gBAAgB,GAAG;gBACvB,EAAE,SAAS,EAAE,IAAI,EAAE,uBAAuB,EAAE,KAAK,EAAE;gBACnD,EAAE,SAAS,EAAE,IAAI,EAAE,uBAAuB,EAAE,IAAI,EAAE;gBAClD,EAAE,SAAS,EAAE,KAAK,EAAE,uBAAuB,EAAE,IAAI,EAAE,yBAAyB,EAAE,IAAI,EAAE;aACrF,CAAC;YAEF,MAAM,gBAAgB,GAA2B,EAAE,CAAC;YAEpD,KAAK,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,gBAAgB,CAAC,OAAO,EAAE,EAAE,CAAC;gBACzD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAE7B,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChD,GAAG,CAAC,oDAAoD,CAAC;qBACzD,KAAK,CAAC,MAAM,CAAC,CAAC;gBAEjB,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAC7C,gBAAgB,CAAC,aAAa,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC;gBAE3D,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9C,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;gBAC1D,OAAO,CAAC,GAAG,CAAC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;YAEH,mDAAmD;YACnD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBAC7C,MAAM,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,MAAM,aAAa,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;YAE5C,+BAA+B;YAC/B,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC5D,OAAO,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChC,GAAG,CAAC,sCAAsC,CAAC;qBAC3C,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;YAEH,MAAM,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAE9B,wCAAwC;YACxC,IAAI,MAAM,CAAC,EAAE,EAAE,CAAC;gBACd,MAAM,CAAC,EAAE,EAAE,CAAC;YACd,CAAC;YAED,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;YAC1C,MAAM,cAAc,GAAG,WAAW,CAAC,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;YACrE,MAAM,qBAAqB,GAAG,CAAC,cAAc,GAAG,aAAa,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC;YAE9E,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;YAC7B,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,aAAa,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YACvF,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YACnF,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,cAAc,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,qBAAqB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAElH,uCAAuC;YACvC,MAAM,CAAC,qBAAqB,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,yBAAyB;QAC3E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,qBAAqB,GAAG,EAAE,CAAC,CAAC,8BAA8B;YAEhE,MAAM,kBAAkB,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,qBAAqB,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;gBACtF,OAAO,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAChC,GAAG,CAAC,sCAAsC,CAAC;qBAC3C,KAAK,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;YACxD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAEzC,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;YAC5C,OAAO,CAAC,GAAG,CAAC,6BAA6B,qBAAqB,EAAE,CAAC,CAAC;YAClE,OAAO,CAAC,GAAG,CAAC,iBAAiB,SAAS,IAAI,CAAC,CAAC;YAC5C,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,SAAS,GAAG,qBAAqB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAE/F,8BAA8B;YAC9B,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAC3B,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;YAEH,mDAAmD;YACnD,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,oCAAoC;QAC7E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\tests\\performance\\load-testing.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { INestApplication } from '@nestjs/common';\r\nimport { TypeOrmModule } from '@nestjs/typeorm';\r\nimport { ConfigModule } from '@nestjs/config';\r\nimport * as request from 'supertest';\r\nimport { ReportingModule } from '../../reporting.module';\r\nimport { ReportDefinition } from '../../entities/report-definition.entity';\r\nimport { AuthGuard } from '../../../auth/guards/auth.guard';\r\nimport { RolesGuard } from '../../../auth/guards/roles.guard';\r\n\r\n/**\r\n * Performance and Load Testing\r\n * \r\n * Tests system performance under various load conditions including:\r\n * - Report generation performance under high load\r\n * - Data processing throughput and latency benchmarks\r\n * - Cache performance and optimization validation\r\n * - Database query performance and optimization\r\n * - API endpoint performance and scalability testing\r\n */\r\ndescribe('Performance and Load Testing', () => {\r\n  let app: INestApplication;\r\n  let moduleRef: TestingModule;\r\n\r\n  const mockUser = {\r\n    id: 'user-123',\r\n    email: '<EMAIL>',\r\n    roles: ['compliance_admin'],\r\n  };\r\n\r\n  const mockAuthGuard = {\r\n    canActivate: jest.fn(() => true),\r\n  };\r\n\r\n  const mockRolesGuard = {\r\n    canActivate: jest.fn(() => true),\r\n  };\r\n\r\n  beforeAll(async () => {\r\n    moduleRef = await Test.createTestingModule({\r\n      imports: [\r\n        ConfigModule.forRoot({\r\n          isGlobal: true,\r\n          envFilePath: '.env.test',\r\n        }),\r\n        TypeOrmModule.forRoot({\r\n          type: 'postgres',\r\n          host: process.env.TEST_DB_HOST || 'localhost',\r\n          port: parseInt(process.env.TEST_DB_PORT || '5433'),\r\n          username: process.env.TEST_DB_USERNAME || 'test',\r\n          password: process.env.TEST_DB_PASSWORD || 'test',\r\n          database: process.env.TEST_DB_NAME || 'sentinel_perf_test',\r\n          entities: [ReportDefinition],\r\n          synchronize: true,\r\n          dropSchema: true,\r\n          logging: false,\r\n          // Performance optimizations for testing\r\n          extra: {\r\n            max: 20, // Maximum number of connections\r\n            connectionTimeoutMillis: 2000,\r\n            idleTimeoutMillis: 30000,\r\n          },\r\n        }),\r\n        ReportingModule,\r\n      ],\r\n    })\r\n      .overrideGuard(AuthGuard)\r\n      .useValue(mockAuthGuard)\r\n      .overrideGuard(RolesGuard)\r\n      .useValue(mockRolesGuard)\r\n      .compile();\r\n\r\n    app = moduleRef.createNestApplication();\r\n\r\n    // Add request context middleware\r\n    app.use((req, res, next) => {\r\n      req.user = mockUser;\r\n      next();\r\n    });\r\n\r\n    await app.init();\r\n\r\n    // Create test data for performance testing\r\n    await createTestData();\r\n  });\r\n\r\n  afterAll(async () => {\r\n    await app.close();\r\n    await moduleRef.close();\r\n  });\r\n\r\n  async function createTestData(): Promise<void> {\r\n    // Create multiple report definitions for testing\r\n    const reportDefinitions = Array.from({ length: 100 }, (_, i) => ({\r\n      name: `Performance Test Report ${i}`,\r\n      description: `Performance test report ${i} for load testing`,\r\n      category: i % 2 === 0 ? 'compliance' : 'audit',\r\n      type: i % 3 === 0 ? 'tabular' : 'chart',\r\n      dataSource: i % 2 === 0 ? 'compliance' : 'audit',\r\n      query: `SELECT * FROM ${i % 2 === 0 ? 'compliance' : 'audit'}_data WHERE id > ${i}`,\r\n      parameters: [\r\n        {\r\n          name: 'startDate',\r\n          type: 'date',\r\n          required: true,\r\n          defaultValue: null,\r\n        },\r\n      ],\r\n      outputFormats: ['pdf', 'excel'],\r\n    }));\r\n\r\n    for (const reportDef of reportDefinitions) {\r\n      await request(app.getHttpServer())\r\n        .post('/api/v1/reporting/report-definitions')\r\n        .send(reportDef);\r\n    }\r\n  }\r\n\r\n  describe('API Endpoint Performance', () => {\r\n    it('should handle high-frequency GET requests efficiently', async () => {\r\n      const concurrentRequests = 50;\r\n      const requestsPerBatch = 10;\r\n      const batches = Math.ceil(concurrentRequests / requestsPerBatch);\r\n\r\n      const allResponseTimes: number[] = [];\r\n\r\n      for (let batch = 0; batch < batches; batch++) {\r\n        const batchPromises = Array.from({ length: requestsPerBatch }, async () => {\r\n          const startTime = Date.now();\r\n          \r\n          const response = await request(app.getHttpServer())\r\n            .get('/api/v1/reporting/report-definitions')\r\n            .query({ page: 1, limit: 20 });\r\n\r\n          const responseTime = Date.now() - startTime;\r\n          allResponseTimes.push(responseTime);\r\n\r\n          expect(response.status).toBe(200);\r\n          return responseTime;\r\n        });\r\n\r\n        await Promise.all(batchPromises);\r\n      }\r\n\r\n      // Performance assertions\r\n      const averageResponseTime = allResponseTimes.reduce((sum, time) => sum + time, 0) / allResponseTimes.length;\r\n      const maxResponseTime = Math.max(...allResponseTimes);\r\n      const p95ResponseTime = allResponseTimes.sort((a, b) => a - b)[Math.floor(allResponseTimes.length * 0.95)];\r\n\r\n      console.log(`Performance Metrics for GET /report-definitions:`);\r\n      console.log(`  Average Response Time: ${averageResponseTime.toFixed(2)}ms`);\r\n      console.log(`  Max Response Time: ${maxResponseTime}ms`);\r\n      console.log(`  95th Percentile: ${p95ResponseTime}ms`);\r\n\r\n      expect(averageResponseTime).toBeLessThan(1000); // Average should be under 1 second\r\n      expect(p95ResponseTime).toBeLessThan(2000); // 95th percentile should be under 2 seconds\r\n      expect(maxResponseTime).toBeLessThan(5000); // Max should be under 5 seconds\r\n    });\r\n\r\n    it('should handle concurrent POST requests efficiently', async () => {\r\n      const concurrentRequests = 20;\r\n      const responseTimes: number[] = [];\r\n\r\n      const createPromises = Array.from({ length: concurrentRequests }, async (_, i) => {\r\n        const reportDto = {\r\n          name: `Concurrent Test Report ${i}_${Date.now()}`,\r\n          description: `Concurrent test report ${i}`,\r\n          category: 'compliance',\r\n          type: 'tabular',\r\n          dataSource: 'compliance',\r\n          query: `SELECT * FROM compliance_data WHERE id > ${i}`,\r\n          parameters: [],\r\n          outputFormats: ['pdf'],\r\n        };\r\n\r\n        const startTime = Date.now();\r\n        \r\n        const response = await request(app.getHttpServer())\r\n          .post('/api/v1/reporting/report-definitions')\r\n          .send(reportDto);\r\n\r\n        const responseTime = Date.now() - startTime;\r\n        responseTimes.push(responseTime);\r\n\r\n        expect(response.status).toBe(201);\r\n        return responseTime;\r\n      });\r\n\r\n      await Promise.all(createPromises);\r\n\r\n      const averageResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;\r\n      const maxResponseTime = Math.max(...responseTimes);\r\n\r\n      console.log(`Performance Metrics for POST /report-definitions:`);\r\n      console.log(`  Average Response Time: ${averageResponseTime.toFixed(2)}ms`);\r\n      console.log(`  Max Response Time: ${maxResponseTime}ms`);\r\n\r\n      expect(averageResponseTime).toBeLessThan(2000); // Average should be under 2 seconds\r\n      expect(maxResponseTime).toBeLessThan(5000); // Max should be under 5 seconds\r\n    });\r\n\r\n    it('should handle large pagination requests efficiently', async () => {\r\n      const pageSizes = [10, 50, 100];\r\n      const performanceResults: Record<number, number> = {};\r\n\r\n      for (const pageSize of pageSizes) {\r\n        const startTime = Date.now();\r\n\r\n        const response = await request(app.getHttpServer())\r\n          .get('/api/v1/reporting/report-definitions')\r\n          .query({ page: 1, limit: pageSize });\r\n\r\n        const responseTime = Date.now() - startTime;\r\n        performanceResults[pageSize] = responseTime;\r\n\r\n        expect(response.status).toBe(200);\r\n        expect(response.body.reportDefinitions.length).toBeLessThanOrEqual(pageSize);\r\n      }\r\n\r\n      console.log('Pagination Performance:');\r\n      Object.entries(performanceResults).forEach(([size, time]) => {\r\n        console.log(`  Page Size ${size}: ${time}ms`);\r\n      });\r\n\r\n      // Performance should scale reasonably with page size\r\n      expect(performanceResults[100]).toBeLessThan(performanceResults[10] * 5);\r\n    });\r\n  });\r\n\r\n  describe('Database Query Performance', () => {\r\n    it('should perform efficient search queries', async () => {\r\n      const searchTerms = ['Performance', 'Test', 'Report', 'Compliance', 'Audit'];\r\n      const searchResults: Record<string, number> = {};\r\n\r\n      for (const term of searchTerms) {\r\n        const startTime = Date.now();\r\n\r\n        const response = await request(app.getHttpServer())\r\n          .get('/api/v1/reporting/report-definitions')\r\n          .query({\r\n            search: term,\r\n            page: 1,\r\n            limit: 50,\r\n          });\r\n\r\n        const queryTime = Date.now() - startTime;\r\n        searchResults[term] = queryTime;\r\n\r\n        expect(response.status).toBe(200);\r\n        expect(response.body.reportDefinitions.length).toBeGreaterThan(0);\r\n      }\r\n\r\n      console.log('Search Query Performance:');\r\n      Object.entries(searchResults).forEach(([term, time]) => {\r\n        console.log(`  Search \"${term}\": ${time}ms`);\r\n      });\r\n\r\n      // All search queries should complete within reasonable time\r\n      Object.values(searchResults).forEach(time => {\r\n        expect(time).toBeLessThan(1500);\r\n      });\r\n    });\r\n\r\n    it('should handle complex filtering efficiently', async () => {\r\n      const filterCombinations = [\r\n        { category: 'compliance' },\r\n        { type: 'tabular' },\r\n        { category: 'compliance', type: 'tabular' },\r\n        { category: 'audit', type: 'chart' },\r\n        { search: 'Performance', category: 'compliance' },\r\n      ];\r\n\r\n      const filterResults: Record<string, number> = {};\r\n\r\n      for (const [index, filters] of filterCombinations.entries()) {\r\n        const startTime = Date.now();\r\n\r\n        const response = await request(app.getHttpServer())\r\n          .get('/api/v1/reporting/report-definitions')\r\n          .query({\r\n            ...filters,\r\n            page: 1,\r\n            limit: 20,\r\n          });\r\n\r\n        const queryTime = Date.now() - startTime;\r\n        filterResults[`Filter ${index + 1}`] = queryTime;\r\n\r\n        expect(response.status).toBe(200);\r\n      }\r\n\r\n      console.log('Filter Query Performance:');\r\n      Object.entries(filterResults).forEach(([filter, time]) => {\r\n        console.log(`  ${filter}: ${time}ms`);\r\n      });\r\n\r\n      // Complex filters should still perform well\r\n      Object.values(filterResults).forEach(time => {\r\n        expect(time).toBeLessThan(2000);\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Report Generation Performance', () => {\r\n    let reportDefinitionId: string;\r\n\r\n    beforeAll(async () => {\r\n      // Create a report definition for generation testing\r\n      const response = await request(app.getHttpServer())\r\n        .post('/api/v1/reporting/report-definitions')\r\n        .send({\r\n          name: 'Performance Generation Test Report',\r\n          description: 'Report for testing generation performance',\r\n          category: 'compliance',\r\n          type: 'tabular',\r\n          dataSource: 'compliance',\r\n          query: 'SELECT * FROM compliance_data LIMIT 1000',\r\n          parameters: [],\r\n          outputFormats: ['pdf', 'excel', 'csv'],\r\n        });\r\n\r\n      reportDefinitionId = response.body.id;\r\n    });\r\n\r\n    it('should queue multiple report generation requests efficiently', async () => {\r\n      const concurrentGenerations = 10;\r\n      const generationTimes: number[] = [];\r\n\r\n      const generationPromises = Array.from({ length: concurrentGenerations }, async (_, i) => {\r\n        const generateDto = {\r\n          reportDefinitionId,\r\n          parameters: {},\r\n          formats: ['pdf'],\r\n          priority: 'medium',\r\n        };\r\n\r\n        const startTime = Date.now();\r\n\r\n        const response = await request(app.getHttpServer())\r\n          .post('/api/v1/reporting/report-generation/generate')\r\n          .send(generateDto);\r\n\r\n        const queueTime = Date.now() - startTime;\r\n        generationTimes.push(queueTime);\r\n\r\n        expect(response.status).toBe(202);\r\n        expect(response.body.status).toBe('queued');\r\n\r\n        return response.body.reportId;\r\n      });\r\n\r\n      const reportIds = await Promise.all(generationPromises);\r\n\r\n      const averageQueueTime = generationTimes.reduce((sum, time) => sum + time, 0) / generationTimes.length;\r\n      const maxQueueTime = Math.max(...generationTimes);\r\n\r\n      console.log(`Report Generation Queue Performance:`);\r\n      console.log(`  Average Queue Time: ${averageQueueTime.toFixed(2)}ms`);\r\n      console.log(`  Max Queue Time: ${maxQueueTime}ms`);\r\n      console.log(`  Reports Queued: ${reportIds.length}`);\r\n\r\n      expect(averageQueueTime).toBeLessThan(1000); // Should queue quickly\r\n      expect(maxQueueTime).toBeLessThan(3000);\r\n    });\r\n\r\n    it('should handle status check requests efficiently', async () => {\r\n      // Generate a report first\r\n      const generateResponse = await request(app.getHttpServer())\r\n        .post('/api/v1/reporting/report-generation/generate')\r\n        .send({\r\n          reportDefinitionId,\r\n          parameters: {},\r\n          formats: ['pdf'],\r\n          priority: 'high',\r\n        });\r\n\r\n      const reportId = generateResponse.body.reportId;\r\n\r\n      // Test multiple status checks\r\n      const statusCheckTimes: number[] = [];\r\n      const numberOfChecks = 20;\r\n\r\n      for (let i = 0; i < numberOfChecks; i++) {\r\n        const startTime = Date.now();\r\n\r\n        const response = await request(app.getHttpServer())\r\n          .get(`/api/v1/reporting/report-generation/status/${reportId}`);\r\n\r\n        const checkTime = Date.now() - startTime;\r\n        statusCheckTimes.push(checkTime);\r\n\r\n        expect(response.status).toBe(200);\r\n\r\n        // Small delay between checks\r\n        await new Promise(resolve => setTimeout(resolve, 100));\r\n      }\r\n\r\n      const averageCheckTime = statusCheckTimes.reduce((sum, time) => sum + time, 0) / statusCheckTimes.length;\r\n      const maxCheckTime = Math.max(...statusCheckTimes);\r\n\r\n      console.log(`Status Check Performance:`);\r\n      console.log(`  Average Check Time: ${averageCheckTime.toFixed(2)}ms`);\r\n      console.log(`  Max Check Time: ${maxCheckTime}ms`);\r\n\r\n      expect(averageCheckTime).toBeLessThan(500); // Status checks should be fast\r\n      expect(maxCheckTime).toBeLessThan(1000);\r\n    });\r\n  });\r\n\r\n  describe('Data Aggregation Performance', () => {\r\n    it('should handle compliance metrics aggregation efficiently', async () => {\r\n      const aggregationSizes = [\r\n        { frameworks: ['sox'], timeRange: '7d' },\r\n        { frameworks: ['sox', 'gdpr'], timeRange: '30d' },\r\n        { frameworks: ['sox', 'gdpr', 'iso27001'], timeRange: '90d' },\r\n      ];\r\n\r\n      const aggregationResults: Record<string, number> = {};\r\n\r\n      for (const [index, config] of aggregationSizes.entries()) {\r\n        const startTime = Date.now();\r\n\r\n        const response = await request(app.getHttpServer())\r\n          .get('/api/v1/reporting/data-aggregation/compliance/metrics')\r\n          .query({\r\n            frameworks: config.frameworks.join(','),\r\n            timeRange: config.timeRange,\r\n            includeHistorical: true,\r\n          });\r\n\r\n        const aggregationTime = Date.now() - startTime;\r\n        aggregationResults[`Config ${index + 1}`] = aggregationTime;\r\n\r\n        expect(response.status).toBe(200);\r\n        expect(response.body.frameworkMetrics).toBeDefined();\r\n      }\r\n\r\n      console.log('Compliance Metrics Aggregation Performance:');\r\n      Object.entries(aggregationResults).forEach(([config, time]) => {\r\n        console.log(`  ${config}: ${time}ms`);\r\n      });\r\n\r\n      // Aggregation should complete within reasonable time\r\n      Object.values(aggregationResults).forEach(time => {\r\n        expect(time).toBeLessThan(5000);\r\n      });\r\n    });\r\n\r\n    it('should handle audit analytics processing efficiently', async () => {\r\n      const analyticsConfigs = [\r\n        { timeRange: '1d', includeAnomalyDetection: false },\r\n        { timeRange: '7d', includeAnomalyDetection: true },\r\n        { timeRange: '30d', includeAnomalyDetection: true, includePatternRecognition: true },\r\n      ];\r\n\r\n      const analyticsResults: Record<string, number> = {};\r\n\r\n      for (const [index, config] of analyticsConfigs.entries()) {\r\n        const startTime = Date.now();\r\n\r\n        const response = await request(app.getHttpServer())\r\n          .get('/api/v1/reporting/data-aggregation/audit/analytics')\r\n          .query(config);\r\n\r\n        const analyticsTime = Date.now() - startTime;\r\n        analyticsResults[`Analytics ${index + 1}`] = analyticsTime;\r\n\r\n        expect(response.status).toBe(200);\r\n        expect(response.body.summary).toBeDefined();\r\n      }\r\n\r\n      console.log('Audit Analytics Performance:');\r\n      Object.entries(analyticsResults).forEach(([config, time]) => {\r\n        console.log(`  ${config}: ${time}ms`);\r\n      });\r\n\r\n      // Analytics should complete within reasonable time\r\n      Object.values(analyticsResults).forEach(time => {\r\n        expect(time).toBeLessThan(10000);\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Memory and Resource Usage', () => {\r\n    it('should maintain stable memory usage under load', async () => {\r\n      const initialMemory = process.memoryUsage();\r\n\r\n      // Perform intensive operations\r\n      const operations = Array.from({ length: 100 }, async (_, i) => {\r\n        return request(app.getHttpServer())\r\n          .get('/api/v1/reporting/report-definitions')\r\n          .query({ page: Math.floor(i / 10) + 1, limit: 20 });\r\n      });\r\n\r\n      await Promise.all(operations);\r\n\r\n      // Force garbage collection if available\r\n      if (global.gc) {\r\n        global.gc();\r\n      }\r\n\r\n      const finalMemory = process.memoryUsage();\r\n      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;\r\n      const memoryIncreasePercent = (memoryIncrease / initialMemory.heapUsed) * 100;\r\n\r\n      console.log('Memory Usage:');\r\n      console.log(`  Initial Heap: ${(initialMemory.heapUsed / 1024 / 1024).toFixed(2)} MB`);\r\n      console.log(`  Final Heap: ${(finalMemory.heapUsed / 1024 / 1024).toFixed(2)} MB`);\r\n      console.log(`  Increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)} MB (${memoryIncreasePercent.toFixed(2)}%)`);\r\n\r\n      // Memory increase should be reasonable\r\n      expect(memoryIncreasePercent).toBeLessThan(50); // Less than 50% increase\r\n    });\r\n\r\n    it('should handle connection pool efficiently', async () => {\r\n      const concurrentConnections = 30; // More than typical pool size\r\n\r\n      const connectionPromises = Array.from({ length: concurrentConnections }, async (_, i) => {\r\n        return request(app.getHttpServer())\r\n          .get('/api/v1/reporting/report-definitions')\r\n          .query({ page: 1, limit: 5 });\r\n      });\r\n\r\n      const startTime = Date.now();\r\n      const responses = await Promise.all(connectionPromises);\r\n      const totalTime = Date.now() - startTime;\r\n\r\n      console.log(`Connection Pool Performance:`);\r\n      console.log(`  Concurrent Connections: ${concurrentConnections}`);\r\n      console.log(`  Total Time: ${totalTime}ms`);\r\n      console.log(`  Average Time per Request: ${(totalTime / concurrentConnections).toFixed(2)}ms`);\r\n\r\n      // All requests should succeed\r\n      responses.forEach(response => {\r\n        expect(response.status).toBe(200);\r\n      });\r\n\r\n      // Should handle concurrent connections efficiently\r\n      expect(totalTime).toBeLessThan(10000); // Should complete within 10 seconds\r\n    });\r\n  });\r\n});\r\n"], "version": 3}