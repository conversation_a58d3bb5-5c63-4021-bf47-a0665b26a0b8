af8984515bc6132cf42d8630d20ed7db
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventSource = void 0;
const base_value_object_1 = require("../../../../shared-kernel/value-objects/base-value-object");
const event_source_type_enum_1 = require("../../enums/event-source-type.enum");
/**
 * Event Source Value Object
 *
 * Represents the source system that generated a security event.
 * Provides identification, classification, and metadata about event origins.
 *
 * Key features:
 * - Source type classification and validation
 * - Unique source identification
 * - Vendor and version tracking
 * - Location and zone information
 * - Extensible metadata support
 * - Reliability and trust scoring
 */
class EventSource extends base_value_object_1.BaseValueObject {
    constructor(props) {
        super(props);
    }
    validate() {
        if (!this._value.type) {
            throw new Error('Event source must have a type');
        }
        if (!Object.values(event_source_type_enum_1.EventSourceType).includes(this._value.type)) {
            throw new Error(`Invalid event source type: ${this._value.type}`);
        }
        if (!this._value.identifier || this._value.identifier.trim().length === 0) {
            throw new Error('Event source must have a non-empty identifier');
        }
        // Validate identifier format (alphanumeric, hyphens, underscores, dots)
        if (!/^[a-zA-Z0-9._-]+$/.test(this._value.identifier)) {
            throw new Error('Event source identifier must contain only alphanumeric characters, dots, hyphens, and underscores');
        }
        // Validate version format if provided
        if (this._value.version && !/^\d+\.\d+(\.\d+)?(-[\w.-]+)?$/.test(this._value.version)) {
            throw new Error('Event source version must be in semantic version format');
        }
        // Validate location format if provided
        if (this._value.location && this._value.location.length > 100) {
            throw new Error('Event source location must be 100 characters or less');
        }
    }
    /**
     * Create an event source with type and identifier
     */
    static create(type, identifier, options) {
        return new EventSource({
            type,
            identifier,
            ...options,
        });
    }
    /**
     * Create an event source from hostname
     */
    static fromHostname(type, hostname, options) {
        return EventSource.create(type, hostname, {
            name: hostname,
            ...options,
        });
    }
    /**
     * Create an event source from IP address
     */
    static fromIPAddress(type, ipAddress, options) {
        return EventSource.create(type, ipAddress, {
            name: `${type}-${ipAddress}`,
            ...options,
        });
    }
    /**
     * Create a manual event source
     */
    static manual(identifier, options) {
        return EventSource.create(event_source_type_enum_1.EventSourceType.MANUAL, identifier, {
            name: 'Manual Event Creation',
            ...options,
        });
    }
    /**
     * Create an unknown event source
     */
    static unknown(identifier, options) {
        return EventSource.create(event_source_type_enum_1.EventSourceType.UNKNOWN, identifier, {
            name: 'Unknown Source',
            ...options,
        });
    }
    /**
     * Get the source type
     */
    get type() {
        return this._value.type;
    }
    /**
     * Get the source identifier
     */
    get identifier() {
        return this._value.identifier;
    }
    /**
     * Get the source name
     */
    get name() {
        return this._value.name || this._value.identifier;
    }
    /**
     * Get the source version
     */
    get version() {
        return this._value.version;
    }
    /**
     * Get the source vendor
     */
    get vendor() {
        return this._value.vendor;
    }
    /**
     * Get the source location
     */
    get location() {
        return this._value.location;
    }
    /**
     * Get source metadata
     */
    get metadata() {
        return this._value.metadata || {};
    }
    /**
     * Check if source has specific metadata
     */
    hasMetadata(key) {
        return key in this.metadata;
    }
    /**
     * Get metadata value
     */
    getMetadata(key) {
        return this.metadata[key];
    }
    /**
     * Get source category
     */
    getCategory() {
        if (this.isNetworkSource())
            return 'Network';
        if (this.isEndpointSource())
            return 'Endpoint';
        if (this.isApplicationSource())
            return 'Application';
        if (this.isCloudSource())
            return 'Cloud';
        if (this.isIdentitySource())
            return 'Identity';
        if (this.isSecurityTool())
            return 'Security Tool';
        if (this.isExternalSource())
            return 'External';
        return 'Other';
    }
    /**
     * Check if this is a network source
     */
    isNetworkSource() {
        const networkTypes = [
            event_source_type_enum_1.EventSourceType.FIREWALL,
            event_source_type_enum_1.EventSourceType.IDS_IPS,
            event_source_type_enum_1.EventSourceType.NETWORK_DEVICE,
            event_source_type_enum_1.EventSourceType.LOAD_BALANCER,
            event_source_type_enum_1.EventSourceType.VPN_GATEWAY,
            event_source_type_enum_1.EventSourceType.NAC,
            event_source_type_enum_1.EventSourceType.DNS_SERVER,
            event_source_type_enum_1.EventSourceType.DHCP_SERVER,
        ];
        return networkTypes.includes(this._value.type);
    }
    /**
     * Check if this is an endpoint source
     */
    isEndpointSource() {
        const endpointTypes = [
            event_source_type_enum_1.EventSourceType.EDR,
            event_source_type_enum_1.EventSourceType.ANTIVIRUS,
            event_source_type_enum_1.EventSourceType.HIDS,
            event_source_type_enum_1.EventSourceType.OPERATING_SYSTEM,
            event_source_type_enum_1.EventSourceType.MDM,
            event_source_type_enum_1.EventSourceType.USB_CONTROL,
        ];
        return endpointTypes.includes(this._value.type);
    }
    /**
     * Check if this is an application source
     */
    isApplicationSource() {
        const appTypes = [
            event_source_type_enum_1.EventSourceType.WAF,
            event_source_type_enum_1.EventSourceType.APM,
            event_source_type_enum_1.EventSourceType.DATABASE,
            event_source_type_enum_1.EventSourceType.WEB_SERVER,
            event_source_type_enum_1.EventSourceType.APPLICATION_SERVER,
            event_source_type_enum_1.EventSourceType.CUSTOM_APPLICATION,
            event_source_type_enum_1.EventSourceType.API_GATEWAY,
        ];
        return appTypes.includes(this._value.type);
    }
    /**
     * Check if this is a cloud source
     */
    isCloudSource() {
        const cloudTypes = [
            event_source_type_enum_1.EventSourceType.AWS,
            event_source_type_enum_1.EventSourceType.AZURE,
            event_source_type_enum_1.EventSourceType.GCP,
            event_source_type_enum_1.EventSourceType.CASB,
            event_source_type_enum_1.EventSourceType.CWPP,
            event_source_type_enum_1.EventSourceType.CONTAINER_SECURITY,
            event_source_type_enum_1.EventSourceType.KUBERNETES,
        ];
        return cloudTypes.includes(this._value.type);
    }
    /**
     * Check if this is an identity source
     */
    isIdentitySource() {
        const identityTypes = [
            event_source_type_enum_1.EventSourceType.DIRECTORY_SERVICE,
            event_source_type_enum_1.EventSourceType.SSO,
            event_source_type_enum_1.EventSourceType.MFA,
            event_source_type_enum_1.EventSourceType.PAM,
            event_source_type_enum_1.EventSourceType.IGA,
        ];
        return identityTypes.includes(this._value.type);
    }
    /**
     * Check if this is a security tool
     */
    isSecurityTool() {
        const securityTypes = [
            event_source_type_enum_1.EventSourceType.SIEM,
            event_source_type_enum_1.EventSourceType.SOAR,
            event_source_type_enum_1.EventSourceType.VULNERABILITY_SCANNER,
            event_source_type_enum_1.EventSourceType.THREAT_INTELLIGENCE,
            event_source_type_enum_1.EventSourceType.DECEPTION,
            event_source_type_enum_1.EventSourceType.SANDBOX,
            event_source_type_enum_1.EventSourceType.FORENSICS,
        ];
        return securityTypes.includes(this._value.type);
    }
    /**
     * Check if this is an external source
     */
    isExternalSource() {
        const externalTypes = [
            event_source_type_enum_1.EventSourceType.EXTERNAL_FEED,
            event_source_type_enum_1.EventSourceType.THIRD_PARTY,
            event_source_type_enum_1.EventSourceType.GOVERNMENT,
            event_source_type_enum_1.EventSourceType.INDUSTRY_SHARING,
        ];
        return externalTypes.includes(this._value.type);
    }
    /**
     * Check if this is a trusted source
     */
    isTrusted() {
        // Internal security tools and known systems are generally trusted
        return this.isSecurityTool() ||
            this.isIdentitySource() ||
            this._value.type === event_source_type_enum_1.EventSourceType.INTERNAL_AUTOMATION;
    }
    /**
     * Get reliability score (0-100)
     */
    getReliabilityScore() {
        const baseScore = this.getBaseReliabilityScore();
        // Adjust based on metadata
        let adjustedScore = baseScore;
        // Boost score for known vendors
        if (this.hasKnownVendor()) {
            adjustedScore += 5;
        }
        // Boost score for versioned sources
        if (this._value.version) {
            adjustedScore += 3;
        }
        // Boost score for sources with location info
        if (this._value.location) {
            adjustedScore += 2;
        }
        return Math.min(100, adjustedScore);
    }
    getBaseReliabilityScore() {
        const scores = {
            [event_source_type_enum_1.EventSourceType.SIEM]: 95,
            [event_source_type_enum_1.EventSourceType.EDR]: 90,
            [event_source_type_enum_1.EventSourceType.IDS_IPS]: 90,
            [event_source_type_enum_1.EventSourceType.FIREWALL]: 85,
            [event_source_type_enum_1.EventSourceType.OPERATING_SYSTEM]: 85,
            [event_source_type_enum_1.EventSourceType.ANTIVIRUS]: 80,
            [event_source_type_enum_1.EventSourceType.WAF]: 80,
            [event_source_type_enum_1.EventSourceType.VULNERABILITY_SCANNER]: 80,
            [event_source_type_enum_1.EventSourceType.DIRECTORY_SERVICE]: 80,
            [event_source_type_enum_1.EventSourceType.EXTERNAL_FEED]: 60,
            [event_source_type_enum_1.EventSourceType.THIRD_PARTY]: 60,
            [event_source_type_enum_1.EventSourceType.IOT_DEVICE]: 50,
            [event_source_type_enum_1.EventSourceType.MANUAL]: 70,
            [event_source_type_enum_1.EventSourceType.UNKNOWN]: 30,
        };
        return scores[this._value.type] || 50;
    }
    hasKnownVendor() {
        if (!this._value.vendor)
            return false;
        const knownVendors = [
            'microsoft', 'cisco', 'palo alto', 'fortinet', 'checkpoint',
            'splunk', 'elastic', 'crowdstrike', 'sentinelone', 'symantec',
            'mcafee', 'trend micro', 'kaspersky', 'f5', 'juniper',
            'amazon', 'google', 'azure', 'vmware', 'citrix'
        ];
        const vendor = this._value.vendor.toLowerCase();
        return knownVendors.some(known => vendor.includes(known));
    }
    /**
     * Get processing priority (1-10, higher = more priority)
     */
    getProcessingPriority() {
        const priorities = {
            [event_source_type_enum_1.EventSourceType.EDR]: 10,
            [event_source_type_enum_1.EventSourceType.IDS_IPS]: 9,
            [event_source_type_enum_1.EventSourceType.DECEPTION]: 9,
            [event_source_type_enum_1.EventSourceType.THREAT_INTELLIGENCE]: 8,
            [event_source_type_enum_1.EventSourceType.FIREWALL]: 7,
            [event_source_type_enum_1.EventSourceType.WAF]: 7,
            [event_source_type_enum_1.EventSourceType.ANTIVIRUS]: 7,
            [event_source_type_enum_1.EventSourceType.PAM]: 8,
            [event_source_type_enum_1.EventSourceType.OPERATING_SYSTEM]: 6,
            [event_source_type_enum_1.EventSourceType.DIRECTORY_SERVICE]: 6,
            [event_source_type_enum_1.EventSourceType.NETWORK_DEVICE]: 5,
            [event_source_type_enum_1.EventSourceType.WEB_SERVER]: 5,
            [event_source_type_enum_1.EventSourceType.IOT_DEVICE]: 4,
            [event_source_type_enum_1.EventSourceType.ENVIRONMENTAL]: 3,
            [event_source_type_enum_1.EventSourceType.MANUAL]: 6,
            [event_source_type_enum_1.EventSourceType.UNKNOWN]: 2,
        };
        return priorities[this._value.type] || 5;
    }
    /**
     * Check if source typically generates high volume
     */
    isHighVolumeSource() {
        const highVolumeTypes = [
            event_source_type_enum_1.EventSourceType.FIREWALL,
            event_source_type_enum_1.EventSourceType.NETWORK_DEVICE,
            event_source_type_enum_1.EventSourceType.WEB_SERVER,
            event_source_type_enum_1.EventSourceType.DNS_SERVER,
            event_source_type_enum_1.EventSourceType.OPERATING_SYSTEM,
            event_source_type_enum_1.EventSourceType.DATABASE,
        ];
        return highVolumeTypes.includes(this._value.type);
    }
    /**
     * Get source description
     */
    getDescription() {
        const descriptions = {
            [event_source_type_enum_1.EventSourceType.FIREWALL]: 'Network firewall and security appliance',
            [event_source_type_enum_1.EventSourceType.IDS_IPS]: 'Intrusion Detection/Prevention System',
            [event_source_type_enum_1.EventSourceType.EDR]: 'Endpoint Detection and Response system',
            [event_source_type_enum_1.EventSourceType.SIEM]: 'Security Information and Event Management',
            [event_source_type_enum_1.EventSourceType.MANUAL]: 'Manual event creation by security analyst',
            [event_source_type_enum_1.EventSourceType.UNKNOWN]: 'Unknown or unidentified source',
        };
        return descriptions[this._value.type] || `${this._value.type} source`;
    }
    /**
     * Create a new source with updated metadata
     */
    withMetadata(metadata) {
        return new EventSource({
            ...this._value,
            metadata: { ...this.metadata, ...metadata },
        });
    }
    /**
     * Create a new source with updated location
     */
    withLocation(location) {
        return new EventSource({
            ...this._value,
            location,
        });
    }
    /**
     * Create a new source with updated version
     */
    withVersion(version) {
        return new EventSource({
            ...this._value,
            version,
        });
    }
    /**
     * Get source summary for logging
     */
    getSummary() {
        return {
            type: this._value.type,
            identifier: this._value.identifier,
            name: this.name,
            category: this.getCategory(),
            reliability: this.getReliabilityScore(),
            priority: this.getProcessingPriority(),
            isTrusted: this.isTrusted(),
            isHighVolume: this.isHighVolumeSource(),
        };
    }
    /**
     * Compare sources for equality
     */
    equals(other) {
        if (!other) {
            return false;
        }
        if (this === other) {
            return true;
        }
        return this._value.type === other._value.type &&
            this._value.identifier === other._value.identifier;
    }
    /**
     * Get string representation
     */
    toString() {
        return `${this._value.type}:${this._value.identifier}`;
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            type: this._value.type,
            identifier: this._value.identifier,
            name: this._value.name,
            version: this._value.version,
            vendor: this._value.vendor,
            location: this._value.location,
            metadata: this._value.metadata,
            summary: this.getSummary(),
            description: this.getDescription(),
        };
    }
    /**
     * Create EventSource from JSON
     */
    static fromJSON(json) {
        return new EventSource({
            type: json.type,
            identifier: json.identifier,
            name: json.name,
            version: json.version,
            vendor: json.vendor,
            location: json.location,
            metadata: json.metadata,
        });
    }
    /**
     * Validate source format without creating instance
     */
    static isValid(type, identifier) {
        try {
            new EventSource({ type, identifier });
            return true;
        }
        catch {
            return false;
        }
    }
}
exports.EventSource = EventSource;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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