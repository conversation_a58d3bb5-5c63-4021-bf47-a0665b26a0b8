bef54875e6a0fd21f993345f3e7bea76
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseAction = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
const action_status_enum_1 = require("../enums/action-status.enum");
const response_action_created_domain_event_1 = require("../events/response-action-created.domain-event");
const response_action_status_changed_domain_event_1 = require("../events/response-action-status-changed.domain-event");
const response_action_executed_domain_event_1 = require("../events/response-action-executed.domain-event");
const response_action_failed_domain_event_1 = require("../events/response-action-failed.domain-event");
const response_action_rolled_back_domain_event_1 = require("../events/response-action-rolled-back.domain-event");
/**
 * Response Action Entity
 *
 * Represents an automated or manual response action that can be executed
 * in response to security events, threats, and vulnerabilities.
 *
 * Key responsibilities:
 * - Maintain action state and lifecycle
 * - Enforce business rules and execution constraints
 * - Generate domain events for state changes
 * - Support action chaining and correlation
 * - Handle retry logic and rollback capabilities
 * - Track execution metrics and results
 *
 * Business Rules:
 * - Actions must have valid type and parameters
 * - Status transitions must follow defined workflows
 * - Approval required for high-risk actions
 * - Retry limits must be respected
 * - Rollback only allowed for reversible actions
 * - Timeout handling for long-running actions
 */
class ResponseAction extends shared_kernel_1.BaseAggregateRoot {
    constructor(props, id) {
        super(props, id);
        this._createdAt = new Date();
        this.validateInvariants();
    }
    /**
     * Create a new ResponseAction
     */
    static create(props, id) {
        const action = new ResponseAction(props, id);
        // Add domain event for action creation
        action.addDomainEvent(new response_action_created_domain_event_1.ResponseActionCreatedDomainEvent(action.id, {
            actionType: props.actionType,
            status: props.status,
            title: props.title,
            priority: props.priority,
            isAutomated: props.isAutomated,
            approvalRequired: props.approvalRequired,
            target: props.target,
            relatedEventId: props.relatedEventId?.toString(),
            relatedThreatId: props.relatedThreatId?.toString(),
            relatedVulnerabilityId: props.relatedVulnerabilityId?.toString(),
        }));
        return action;
    }
    validateInvariants() {
        super.validateInvariants();
        if (!this.props.actionType) {
            throw new Error('ResponseAction must have an action type');
        }
        if (!this.props.status) {
            throw new Error('ResponseAction must have a status');
        }
        if (!this.props.title || this.props.title.trim().length === 0) {
            throw new Error('ResponseAction must have a non-empty title');
        }
        if (this.props.title.length > ResponseAction.MAX_TITLE_LENGTH) {
            throw new Error(`ResponseAction title cannot exceed ${ResponseAction.MAX_TITLE_LENGTH} characters`);
        }
        if (this.props.description && this.props.description.length > ResponseAction.MAX_DESCRIPTION_LENGTH) {
            throw new Error(`ResponseAction description cannot exceed ${ResponseAction.MAX_DESCRIPTION_LENGTH} characters`);
        }
        if (!this.props.parameters) {
            throw new Error('ResponseAction must have parameters');
        }
        if (!this.props.priority) {
            throw new Error('ResponseAction must have a priority');
        }
        if (this.props.isAutomated === undefined) {
            throw new Error('ResponseAction must specify if it is automated');
        }
        if (this.props.isReversible === undefined) {
            throw new Error('ResponseAction must specify if it is reversible');
        }
        if (!Array.isArray(this.props.requiredPermissions)) {
            throw new Error('ResponseAction must have required permissions array');
        }
        if (this.props.approvalRequired === undefined) {
            throw new Error('ResponseAction must specify if approval is required');
        }
        if (!Array.isArray(this.props.successCriteria)) {
            throw new Error('ResponseAction must have success criteria array');
        }
        if (this.props.retryCount < 0) {
            throw new Error('Retry count cannot be negative');
        }
        if (this.props.maxRetries < 0) {
            throw new Error('Max retries cannot be negative');
        }
        if (this.props.retryDelayMinutes < 0) {
            throw new Error('Retry delay cannot be negative');
        }
        if (!Array.isArray(this.props.tags)) {
            throw new Error('ResponseAction must have tags array');
        }
        if (this.props.tags.length > ResponseAction.MAX_TAGS) {
            throw new Error(`ResponseAction cannot have more than ${ResponseAction.MAX_TAGS} tags`);
        }
        if (!this.props.metadata) {
            throw new Error('ResponseAction must have metadata object');
        }
        if (!Array.isArray(this.props.childActionIds)) {
            throw new Error('ResponseAction must have child action IDs array');
        }
        if (this.props.timeoutMinutes <= 0) {
            throw new Error('Timeout minutes must be positive');
        }
        if (this.props.timedOut === undefined) {
            throw new Error('ResponseAction must specify if it timed out');
        }
        if (this.props.rolledBack === undefined) {
            throw new Error('ResponseAction must specify if it was rolled back');
        }
        // Validate status-specific constraints
        this.validateStatusConstraints();
    }
    validateStatusConstraints() {
        // If action is approved, it should have approval information
        if (this.props.status === action_status_enum_1.ActionStatus.APPROVED) {
            if (this.props.approvalRequired && !this.props.approvedBy) {
                throw new Error('Approved actions must have approver information');
            }
        }
        // If action is executed, it should have execution information
        if (this.props.status === action_status_enum_1.ActionStatus.COMPLETED || this.props.status === action_status_enum_1.ActionStatus.FAILED) {
            if (!this.props.executedAt) {
                throw new Error('Executed actions must have execution timestamp');
            }
        }
        // If action is rolled back, it must be reversible
        if (this.props.rolledBack && !this.props.isReversible) {
            throw new Error('Only reversible actions can be rolled back');
        }
        // If action has rollback details, it must be marked as rolled back
        if (this.props.rollbackDetails && !this.props.rolledBack) {
            throw new Error('Actions with rollback details must be marked as rolled back');
        }
    }
    // Getters
    get actionType() {
        return this.props.actionType;
    }
    get status() {
        return this.props.status;
    }
    get title() {
        return this.props.title;
    }
    get description() {
        return this.props.description;
    }
    get parameters() {
        return { ...this.props.parameters };
    }
    get target() {
        return this.props.target ? { ...this.props.target } : undefined;
    }
    get priority() {
        return this.props.priority;
    }
    get isAutomated() {
        return this.props.isAutomated;
    }
    get isReversible() {
        return this.props.isReversible;
    }
    get estimatedDurationMinutes() {
        return this.props.estimatedDurationMinutes;
    }
    get actualDurationMinutes() {
        return this.props.actualDurationMinutes;
    }
    get requiredPermissions() {
        return [...this.props.requiredPermissions];
    }
    get approvalRequired() {
        return this.props.approvalRequired;
    }
    get approvalLevel() {
        return this.props.approvalLevel;
    }
    get approvedBy() {
        return this.props.approvedBy;
    }
    get approvedAt() {
        return this.props.approvedAt;
    }
    get scheduledBy() {
        return this.props.scheduledBy;
    }
    get scheduledAt() {
        return this.props.scheduledAt;
    }
    get executedBy() {
        return this.props.executedBy;
    }
    get executedAt() {
        return this.props.executedAt;
    }
    get executionResults() {
        return this.props.executionResults ? { ...this.props.executionResults } : undefined;
    }
    get successCriteria() {
        return [...this.props.successCriteria];
    }
    get successCriteriaMet() {
        return this.props.successCriteriaMet;
    }
    get executionError() {
        return this.props.executionError;
    }
    get retryCount() {
        return this.props.retryCount;
    }
    get maxRetries() {
        return this.props.maxRetries;
    }
    get retryDelayMinutes() {
        return this.props.retryDelayMinutes;
    }
    get nextRetryAt() {
        return this.props.nextRetryAt;
    }
    get rollbackInfo() {
        return this.props.rollbackInfo ? { ...this.props.rollbackInfo } : undefined;
    }
    get rolledBack() {
        return this.props.rolledBack;
    }
    get rollbackDetails() {
        return this.props.rollbackDetails ? { ...this.props.rollbackDetails } : undefined;
    }
    get tags() {
        return [...this.props.tags];
    }
    get metadata() {
        return { ...this.props.metadata };
    }
    get relatedEventId() {
        return this.props.relatedEventId;
    }
    get relatedThreatId() {
        return this.props.relatedThreatId;
    }
    get relatedVulnerabilityId() {
        return this.props.relatedVulnerabilityId;
    }
    get parentActionId() {
        return this.props.parentActionId;
    }
    get childActionIds() {
        return [...this.props.childActionIds];
    }
    get correlationId() {
        return this.props.correlationId;
    }
    get timeoutMinutes() {
        return this.props.timeoutMinutes;
    }
    get timedOut() {
        return this.props.timedOut;
    }
    get timedOutAt() {
        return this.props.timedOutAt;
    }
    get createdAt() {
        return this._createdAt;
    }
    // Business methods
    /**
     * Change action status
     */
    changeStatus(newStatus, changedBy, notes) {
        if (this.props.status === newStatus) {
            return; // No change needed
        }
        // Validate status transition
        if (!this.isValidStatusTransition(this.props.status, newStatus)) {
            throw new Error(`Invalid status transition from ${this.props.status} to ${newStatus}`);
        }
        const oldStatus = this.props.status;
        this.props.status = newStatus;
        // Handle status-specific logic
        this.handleStatusChange(newStatus, changedBy);
        this.addDomainEvent(new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(this.id, {
            oldStatus,
            newStatus,
            changedBy,
            notes,
            timestamp: new Date(),
        }));
        this.validateInvariants();
    }
    handleStatusChange(newStatus, changedBy) {
        const now = new Date();
        switch (newStatus) {
            case action_status_enum_1.ActionStatus.APPROVED:
                this.props.approvedBy = changedBy;
                this.props.approvedAt = now;
                break;
            case action_status_enum_1.ActionStatus.EXECUTING:
                this.props.executedBy = changedBy;
                this.props.executedAt = now;
                break;
            case action_status_enum_1.ActionStatus.SCHEDULED:
                this.props.scheduledBy = changedBy;
                break;
            case action_status_enum_1.ActionStatus.TIMEOUT:
                this.props.timedOut = true;
                this.props.timedOutAt = now;
                break;
        }
    }
    isValidStatusTransition(from, to) {
        const validTransitions = {
            [action_status_enum_1.ActionStatus.PENDING]: [action_status_enum_1.ActionStatus.APPROVED, action_status_enum_1.ActionStatus.REJECTED, action_status_enum_1.ActionStatus.CANCELLED],
            [action_status_enum_1.ActionStatus.APPROVED]: [action_status_enum_1.ActionStatus.QUEUED, action_status_enum_1.ActionStatus.SCHEDULED, action_status_enum_1.ActionStatus.CANCELLED],
            [action_status_enum_1.ActionStatus.QUEUED]: [action_status_enum_1.ActionStatus.EXECUTING, action_status_enum_1.ActionStatus.CANCELLED, action_status_enum_1.ActionStatus.ON_HOLD],
            [action_status_enum_1.ActionStatus.EXECUTING]: [
                action_status_enum_1.ActionStatus.COMPLETED, action_status_enum_1.ActionStatus.FAILED, action_status_enum_1.ActionStatus.PARTIAL,
                action_status_enum_1.ActionStatus.PAUSED, action_status_enum_1.ActionStatus.CANCELLED, action_status_enum_1.ActionStatus.TIMEOUT,
                action_status_enum_1.ActionStatus.MANUAL_INTERVENTION
            ],
            [action_status_enum_1.ActionStatus.PAUSED]: [action_status_enum_1.ActionStatus.EXECUTING, action_status_enum_1.ActionStatus.CANCELLED, action_status_enum_1.ActionStatus.ON_HOLD],
            [action_status_enum_1.ActionStatus.RETRYING]: [action_status_enum_1.ActionStatus.EXECUTING, action_status_enum_1.ActionStatus.FAILED, action_status_enum_1.ActionStatus.CANCELLED, action_status_enum_1.ActionStatus.TIMEOUT],
            [action_status_enum_1.ActionStatus.MANUAL_INTERVENTION]: [action_status_enum_1.ActionStatus.EXECUTING, action_status_enum_1.ActionStatus.CANCELLED, action_status_enum_1.ActionStatus.ON_HOLD],
            [action_status_enum_1.ActionStatus.PARTIAL]: [action_status_enum_1.ActionStatus.EXECUTING, action_status_enum_1.ActionStatus.COMPLETED, action_status_enum_1.ActionStatus.FAILED, action_status_enum_1.ActionStatus.CANCELLED],
            [action_status_enum_1.ActionStatus.SCHEDULED]: [action_status_enum_1.ActionStatus.QUEUED, action_status_enum_1.ActionStatus.CANCELLED, action_status_enum_1.ActionStatus.ON_HOLD],
            [action_status_enum_1.ActionStatus.ON_HOLD]: [action_status_enum_1.ActionStatus.QUEUED, action_status_enum_1.ActionStatus.CANCELLED, action_status_enum_1.ActionStatus.REJECTED],
            [action_status_enum_1.ActionStatus.FAILED]: [action_status_enum_1.ActionStatus.RETRYING, action_status_enum_1.ActionStatus.CANCELLED],
            [action_status_enum_1.ActionStatus.TIMEOUT]: [action_status_enum_1.ActionStatus.RETRYING, action_status_enum_1.ActionStatus.CANCELLED],
            [action_status_enum_1.ActionStatus.COMPLETED]: [], // Terminal
            [action_status_enum_1.ActionStatus.CANCELLED]: [], // Terminal
            [action_status_enum_1.ActionStatus.SKIPPED]: [], // Terminal
            [action_status_enum_1.ActionStatus.REJECTED]: [], // Terminal
            [action_status_enum_1.ActionStatus.UNKNOWN]: [action_status_enum_1.ActionStatus.PENDING, action_status_enum_1.ActionStatus.CANCELLED],
        };
        return validTransitions[from]?.includes(to) || false;
    }
    /**
     * Execute the action
     */
    execute(executedBy, executionResults) {
        if (this.props.status !== action_status_enum_1.ActionStatus.EXECUTING) {
            throw new Error('Action must be in executing status to be executed');
        }
        this.props.executionResults = executionResults;
        this.props.executedBy = executedBy;
        this.props.executedAt = new Date();
        // Calculate actual duration
        if (this.props.executedAt) {
            const startTime = this.props.scheduledAt || this.props.approvedAt || this.createdAt;
            if (startTime) {
                this.props.actualDurationMinutes = Math.round((this.props.executedAt.getTime() - startTime.getTime()) / (1000 * 60));
            }
        }
        // Check success criteria
        this.evaluateSuccessCriteria();
        const newStatus = this.props.successCriteriaMet ? action_status_enum_1.ActionStatus.COMPLETED : action_status_enum_1.ActionStatus.FAILED;
        this.props.status = newStatus;
        this.addDomainEvent(new response_action_executed_domain_event_1.ResponseActionExecutedDomainEvent(this.id, {
            actionType: this.props.actionType,
            executedBy,
            executedAt: this.props.executedAt,
            executionResults,
            successCriteriaMet: this.props.successCriteriaMet,
            actualDurationMinutes: this.props.actualDurationMinutes,
        }));
        this.validateInvariants();
    }
    /**
     * Fail the action execution
     */
    fail(error, executedBy) {
        if (this.props.status !== action_status_enum_1.ActionStatus.EXECUTING && this.props.status !== action_status_enum_1.ActionStatus.RETRYING) {
            throw new Error('Action must be in executing or retrying status to fail');
        }
        this.props.executionError = error;
        this.props.status = action_status_enum_1.ActionStatus.FAILED;
        this.props.successCriteriaMet = false;
        if (executedBy) {
            this.props.executedBy = executedBy;
        }
        if (!this.props.executedAt) {
            this.props.executedAt = new Date();
        }
        this.addDomainEvent(new response_action_failed_domain_event_1.ResponseActionFailedDomainEvent(this.id, {
            actionType: this.props.actionType,
            error,
            executedBy,
            failedAt: new Date(),
            retryCount: this.props.retryCount,
            canRetry: this.canRetry(),
        }));
        this.validateInvariants();
    }
    /**
     * Retry the action
     */
    retry(retryBy) {
        if (!this.canRetry()) {
            throw new Error('Action cannot be retried');
        }
        this.props.retryCount += 1;
        this.props.status = action_status_enum_1.ActionStatus.RETRYING;
        this.props.executionError = undefined;
        this.props.nextRetryAt = new Date(Date.now() + this.props.retryDelayMinutes * 60 * 1000);
        this.addDomainEvent(new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(this.id, {
            oldStatus: action_status_enum_1.ActionStatus.FAILED,
            newStatus: action_status_enum_1.ActionStatus.RETRYING,
            changedBy: retryBy,
            notes: `Retry attempt ${this.props.retryCount}`,
            timestamp: new Date(),
        }));
        this.validateInvariants();
    }
    /**
     * Rollback the action
     */
    rollback(rolledBackBy, rollbackResults) {
        if (!this.canRollback()) {
            throw new Error('Action cannot be rolled back');
        }
        this.props.rolledBack = true;
        this.props.rollbackDetails = {
            rolledBackBy,
            rolledBackAt: new Date(),
            rollbackResults,
        };
        this.addDomainEvent(new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(this.id, {
            actionType: this.props.actionType,
            rolledBackBy,
            rolledBackAt: this.props.rollbackDetails.rolledBackAt,
            rollbackResults,
            originalExecutionResults: this.props.executionResults,
        }));
        this.validateInvariants();
    }
    /**
     * Set rollback information
     */
    setRollbackInfo(rollbackInfo) {
        this.props.rollbackInfo = rollbackInfo;
    }
    /**
     * Add child action
     */
    addChildAction(childActionId) {
        if (!this.props.childActionIds.some(id => id.equals(childActionId))) {
            this.props.childActionIds.push(childActionId);
        }
    }
    /**
     * Remove child action
     */
    removeChildAction(childActionId) {
        this.props.childActionIds = this.props.childActionIds.filter(id => !id.equals(childActionId));
    }
    /**
     * Set correlation ID
     */
    setCorrelationId(correlationId) {
        this.props.correlationId = correlationId;
    }
    /**
     * Add tags
     */
    addTags(tags) {
        const newTags = [...new Set([...this.props.tags, ...tags])]; // Remove duplicates
        if (newTags.length > ResponseAction.MAX_TAGS) {
            throw new Error(`ResponseAction cannot have more than ${ResponseAction.MAX_TAGS} tags`);
        }
        this.props.tags = newTags;
    }
    /**
     * Remove tags
     */
    removeTags(tags) {
        this.props.tags = this.props.tags.filter(tag => !tags.includes(tag));
    }
    /**
     * Update metadata
     */
    updateMetadata(metadata) {
        this.props.metadata = { ...this.props.metadata, ...metadata };
    }
    /**
     * Remove metadata key
     */
    removeMetadata(key) {
        delete this.props.metadata[key];
    }
    // Query methods
    /**
     * Check if action is terminal (completed, failed, cancelled, etc.)
     */
    isTerminal() {
        return [
            action_status_enum_1.ActionStatus.COMPLETED,
            action_status_enum_1.ActionStatus.FAILED,
            action_status_enum_1.ActionStatus.CANCELLED,
            action_status_enum_1.ActionStatus.TIMEOUT,
            action_status_enum_1.ActionStatus.SKIPPED,
            action_status_enum_1.ActionStatus.REJECTED,
        ].includes(this.props.status);
    }
    /**
     * Check if action is active (not terminal)
     */
    isActive() {
        return !this.isTerminal();
    }
    /**
     * Check if action is successful
     */
    isSuccessful() {
        return [action_status_enum_1.ActionStatus.COMPLETED, action_status_enum_1.ActionStatus.PARTIAL].includes(this.props.status);
    }
    /**
     * Check if action has failed
     */
    hasFailed() {
        return [action_status_enum_1.ActionStatus.FAILED, action_status_enum_1.ActionStatus.TIMEOUT].includes(this.props.status);
    }
    /**
     * Check if action can be retried
     */
    canRetry() {
        return this.hasFailed() &&
            this.props.retryCount < this.props.maxRetries &&
            !this.props.timedOut;
    }
    /**
     * Check if action can be rolled back
     */
    canRollback() {
        return this.props.isReversible &&
            this.isSuccessful() &&
            !this.props.rolledBack &&
            this.props.rollbackInfo?.canRollback === true;
    }
    /**
     * Check if action requires approval
     */
    requiresApproval() {
        return this.props.approvalRequired && this.props.status === action_status_enum_1.ActionStatus.PENDING;
    }
    /**
     * Check if action is high priority
     */
    isHighPriority() {
        return ['high', 'critical'].includes(this.props.priority);
    }
    /**
     * Check if action is overdue
     */
    isOverdue() {
        if (!this.props.scheduledAt) {
            return false;
        }
        return new Date() > this.props.scheduledAt && this.isActive();
    }
    /**
     * Check if action has timed out
     */
    hasTimedOut() {
        if (this.props.timedOut) {
            return true;
        }
        if (this.props.status === action_status_enum_1.ActionStatus.EXECUTING && this.props.executedAt) {
            const elapsedMinutes = (Date.now() - this.props.executedAt.getTime()) / (1000 * 60);
            return elapsedMinutes > this.props.timeoutMinutes;
        }
        return false;
    }
    /**
     * Check if action has specific tag
     */
    hasTag(tag) {
        return this.props.tags.includes(tag);
    }
    /**
     * Check if action has any of the specified tags
     */
    hasAnyTag(tags) {
        return tags.some(tag => this.hasTag(tag));
    }
    /**
     * Check if action has all of the specified tags
     */
    hasAllTags(tags) {
        return tags.every(tag => this.hasTag(tag));
    }
    /**
     * Get action age in milliseconds
     */
    getAge() {
        return Date.now() - this._createdAt.getTime();
    }
    /**
     * Get estimated completion time
     */
    getEstimatedCompletionTime() {
        if (this.isTerminal()) {
            return null;
        }
        const baseTime = this.props.scheduledAt || new Date();
        const estimatedMinutes = this.props.estimatedDurationMinutes || 30;
        return new Date(baseTime.getTime() + estimatedMinutes * 60 * 1000);
    }
    /**
     * Evaluate success criteria
     */
    evaluateSuccessCriteria() {
        // This is a simplified implementation
        // In a real system, this would involve more complex validation
        this.props.successCriteriaMet = this.props.executionResults !== undefined &&
            this.props.executionError === undefined;
    }
    /**
     * Get action summary for display
     */
    getSummary() {
        return {
            id: this.id.toString(),
            title: this.props.title,
            actionType: this.props.actionType,
            status: this.props.status,
            priority: this.props.priority,
            isAutomated: this.props.isAutomated,
            approvalRequired: this.props.approvalRequired,
            isReversible: this.props.isReversible,
            age: this.getAge(),
            isActive: this.isActive(),
            isSuccessful: this.isSuccessful(),
            canRetry: this.canRetry(),
            canRollback: this.canRollback(),
        };
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            ...super.toJSON(),
            actionType: this.props.actionType,
            status: this.props.status,
            title: this.props.title,
            description: this.props.description,
            parameters: this.props.parameters,
            target: this.props.target,
            priority: this.props.priority,
            isAutomated: this.props.isAutomated,
            isReversible: this.props.isReversible,
            estimatedDurationMinutes: this.props.estimatedDurationMinutes,
            actualDurationMinutes: this.props.actualDurationMinutes,
            requiredPermissions: this.props.requiredPermissions,
            approvalRequired: this.props.approvalRequired,
            approvalLevel: this.props.approvalLevel,
            approvedBy: this.props.approvedBy,
            approvedAt: this.props.approvedAt?.toISOString(),
            scheduledBy: this.props.scheduledBy,
            scheduledAt: this.props.scheduledAt?.toISOString(),
            executedBy: this.props.executedBy,
            executedAt: this.props.executedAt?.toISOString(),
            executionResults: this.props.executionResults,
            successCriteria: this.props.successCriteria,
            successCriteriaMet: this.props.successCriteriaMet,
            executionError: this.props.executionError,
            retryCount: this.props.retryCount,
            maxRetries: this.props.maxRetries,
            retryDelayMinutes: this.props.retryDelayMinutes,
            nextRetryAt: this.props.nextRetryAt?.toISOString(),
            rollbackInfo: this.props.rollbackInfo,
            rolledBack: this.props.rolledBack,
            rollbackDetails: this.props.rollbackDetails ? {
                ...this.props.rollbackDetails,
                rolledBackAt: this.props.rollbackDetails.rolledBackAt.toISOString(),
            } : undefined,
            tags: this.props.tags,
            metadata: this.props.metadata,
            relatedEventId: this.props.relatedEventId?.toString(),
            relatedThreatId: this.props.relatedThreatId?.toString(),
            relatedVulnerabilityId: this.props.relatedVulnerabilityId?.toString(),
            parentActionId: this.props.parentActionId?.toString(),
            childActionIds: this.props.childActionIds.map(id => id.toString()),
            correlationId: this.props.correlationId,
            timeoutMinutes: this.props.timeoutMinutes,
            timedOut: this.props.timedOut,
            timedOutAt: this.props.timedOutAt?.toISOString(),
            summary: this.getSummary(),
        };
    }
}
exports.ResponseAction = ResponseAction;
ResponseAction.MAX_TITLE_LENGTH = 200;
ResponseAction.MAX_DESCRIPTION_LENGTH = 2000;
ResponseAction.MAX_TAGS = 20;
ResponseAction.DEFAULT_TIMEOUT_MINUTES = 60;
ResponseAction.DEFAULT_MAX_RETRIES = 3;
ResponseAction.DEFAULT_RETRY_DELAY_MINUTES = 5;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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