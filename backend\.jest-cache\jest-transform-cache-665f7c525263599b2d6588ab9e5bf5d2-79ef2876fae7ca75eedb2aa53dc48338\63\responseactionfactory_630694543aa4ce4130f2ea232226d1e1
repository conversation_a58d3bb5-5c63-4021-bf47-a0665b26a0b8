b0293b4cd02603b56b7a90b1b4e11c61
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseActionFactory = void 0;
const response_action_entity_1 = require("../entities/response-action.entity");
const action_type_enum_1 = require("../enums/action-type.enum");
const action_status_enum_1 = require("../enums/action-status.enum");
/**
 * Response Action Factory
 *
 * Factory class for creating ResponseAction entities with proper validation and defaults.
 * Handles complex action creation scenarios and ensures all business rules are applied.
 *
 * Key responsibilities:
 * - Create actions from various input formats
 * - Apply default values based on action type
 * - Validate action configuration
 * - Set up proper permissions and approval requirements
 * - Configure retry and rollback settings
 * - Generate appropriate success criteria
 */
class ResponseActionFactory {
    /**
     * Create a new ResponseAction with the provided options
     */
    static create(options) {
        // Validate the action configuration
        ResponseActionFactory.validateActionConfiguration(options);
        // Apply defaults based on action type
        const defaults = ResponseActionFactory.getActionDefaults(options.actionType);
        // Build action properties
        const actionProps = {
            actionType: options.actionType,
            status: options.status || action_status_enum_1.ActionStatus.PENDING,
            title: options.title,
            description: options.description,
            parameters: options.parameters,
            target: options.target,
            priority: options.priority || defaults.priority,
            isAutomated: options.isAutomated ?? defaults.isAutomated,
            isReversible: options.isReversible ?? defaults.isReversible,
            estimatedDurationMinutes: options.estimatedDurationMinutes || defaults.estimatedDurationMinutes,
            actualDurationMinutes: undefined,
            requiredPermissions: options.requiredPermissions || defaults.requiredPermissions,
            approvalRequired: options.approvalRequired ?? defaults.approvalRequired,
            approvalLevel: options.approvalLevel || defaults.approvalLevel,
            approvedBy: undefined,
            approvedAt: undefined,
            scheduledBy: undefined,
            scheduledAt: options.scheduledAt,
            executedBy: undefined,
            executedAt: undefined,
            executionResults: undefined,
            successCriteria: options.successCriteria || defaults.successCriteria,
            successCriteriaMet: undefined,
            executionError: undefined,
            retryCount: 0,
            maxRetries: options.maxRetries || defaults.maxRetries,
            retryDelayMinutes: options.retryDelayMinutes || defaults.retryDelayMinutes,
            nextRetryAt: undefined,
            rollbackInfo: options.rollbackInfo || defaults.rollbackInfo,
            rolledBack: false,
            rollbackDetails: undefined,
            tags: options.tags || defaults.tags,
            metadata: options.metadata || {},
            relatedEventId: options.relatedEventId,
            relatedThreatId: options.relatedThreatId,
            relatedVulnerabilityId: options.relatedVulnerabilityId,
            parentActionId: options.parentActionId,
            childActionIds: [],
            correlationId: options.correlationId,
            timeoutMinutes: options.timeoutMinutes || defaults.timeoutMinutes,
            timedOut: false,
            timedOutAt: undefined,
        };
        return response_action_entity_1.ResponseAction.create(actionProps, options.id);
    }
    /**
     * Create a containment action
     */
    static createContainmentAction(actionType, title, target, parameters, options) {
        if (!action_type_enum_1.ActionTypeUtils.getContainmentActionTypes().includes(actionType)) {
            throw new Error(`${actionType} is not a containment action type`);
        }
        return ResponseActionFactory.create({
            actionType,
            title,
            target,
            parameters,
            priority: 'high',
            isAutomated: action_type_enum_1.ActionTypeUtils.isAutomated(actionType),
            approvalRequired: action_type_enum_1.ActionTypeUtils.isHighRisk(actionType),
            tags: ['containment', 'security'],
            ...options,
        });
    }
    /**
     * Create an eradication action
     */
    static createEradicationAction(actionType, title, target, parameters, options) {
        if (!action_type_enum_1.ActionTypeUtils.getEradicationActionTypes().includes(actionType)) {
            throw new Error(`${actionType} is not an eradication action type`);
        }
        return ResponseActionFactory.create({
            actionType,
            title,
            target,
            parameters,
            priority: 'high',
            isAutomated: action_type_enum_1.ActionTypeUtils.isAutomated(actionType),
            approvalRequired: true, // Eradication actions typically require approval
            tags: ['eradication', 'security'],
            ...options,
        });
    }
    /**
     * Create a recovery action
     */
    static createRecoveryAction(actionType, title, target, parameters, options) {
        if (!action_type_enum_1.ActionTypeUtils.getRecoveryActionTypes().includes(actionType)) {
            throw new Error(`${actionType} is not a recovery action type`);
        }
        return ResponseActionFactory.create({
            actionType,
            title,
            target,
            parameters,
            priority: 'normal',
            isAutomated: action_type_enum_1.ActionTypeUtils.isAutomated(actionType),
            approvalRequired: action_type_enum_1.ActionTypeUtils.isHighRisk(actionType),
            tags: ['recovery', 'restoration'],
            ...options,
        });
    }
    /**
     * Create a notification action
     */
    static createNotificationAction(actionType, title, parameters, options) {
        if (!action_type_enum_1.ActionTypeUtils.getNotificationActionTypes().includes(actionType)) {
            throw new Error(`${actionType} is not a notification action type`);
        }
        return ResponseActionFactory.create({
            actionType,
            title,
            parameters,
            priority: 'normal',
            isAutomated: true,
            approvalRequired: false,
            tags: ['notification', 'communication'],
            ...options,
        });
    }
    /**
     * Create an automated action
     */
    static createAutomatedAction(actionType, title, parameters, options) {
        if (!action_type_enum_1.ActionTypeUtils.isAutomated(actionType)) {
            throw new Error(`${actionType} is not an automated action type`);
        }
        return ResponseActionFactory.create({
            actionType,
            title,
            parameters,
            isAutomated: true,
            approvalRequired: action_type_enum_1.ActionTypeUtils.isHighRisk(actionType),
            status: action_status_enum_1.ActionStatus.APPROVED, // Auto-approve if no approval required
            ...options,
        });
    }
    /**
     * Create a manual action
     */
    static createManualAction(actionType, title, parameters, options) {
        if (!action_type_enum_1.ActionTypeUtils.isManual(actionType)) {
            throw new Error(`${actionType} is not a manual action type`);
        }
        return ResponseActionFactory.create({
            actionType,
            title,
            parameters,
            isAutomated: false,
            approvalRequired: true,
            tags: ['manual', 'human-required'],
            ...options,
        });
    }
    /**
     * Create a high-priority action
     */
    static createHighPriorityAction(actionType, title, parameters, options) {
        return ResponseActionFactory.create({
            actionType,
            title,
            parameters,
            priority: 'high',
            tags: ['high-priority', 'urgent'],
            ...options,
        });
    }
    /**
     * Create a critical action
     */
    static createCriticalAction(actionType, title, parameters, options) {
        return ResponseActionFactory.create({
            actionType,
            title,
            parameters,
            priority: 'critical',
            approvalRequired: true,
            approvalLevel: 'manager',
            tags: ['critical', 'emergency'],
            ...options,
        });
    }
    /**
     * Create a chained action (child of another action)
     */
    static createChainedAction(parentAction, actionType, title, parameters, options) {
        const childAction = ResponseActionFactory.create({
            actionType,
            title,
            parameters,
            parentActionId: parentAction.id,
            correlationId: parentAction.correlationId || parentAction.id.toString(),
            priority: parentAction.priority, // Inherit priority from parent
            tags: [...parentAction.tags, 'chained'],
            ...options,
        });
        // Add child to parent
        parentAction.addChildAction(childAction.id);
        return childAction;
    }
    /**
     * Create an action from event response
     */
    static fromEventResponse(eventId, actionType, title, parameters, options) {
        return ResponseActionFactory.create({
            actionType,
            title,
            parameters,
            relatedEventId: eventId,
            target: {
                type: 'event',
                id: eventId.toString(),
            },
            tags: ['event-response', 'automated-response'],
            ...options,
        });
    }
    /**
     * Create an action from threat response
     */
    static fromThreatResponse(threatId, actionType, title, parameters, options) {
        return ResponseActionFactory.create({
            actionType,
            title,
            parameters,
            relatedThreatId: threatId,
            target: {
                type: 'threat',
                id: threatId.toString(),
            },
            priority: 'high', // Threat responses are typically high priority
            tags: ['threat-response', 'security'],
            ...options,
        });
    }
    /**
     * Create an action from vulnerability response
     */
    static fromVulnerabilityResponse(vulnerabilityId, actionType, title, parameters, options) {
        return ResponseActionFactory.create({
            actionType,
            title,
            parameters,
            relatedVulnerabilityId: vulnerabilityId,
            target: {
                type: 'vulnerability',
                id: vulnerabilityId.toString(),
            },
            tags: ['vulnerability-response', 'remediation'],
            ...options,
        });
    }
    /**
     * Get default values for an action type
     */
    static getActionDefaults(actionType) {
        const isAutomated = action_type_enum_1.ActionTypeUtils.isAutomated(actionType);
        const isHighRisk = action_type_enum_1.ActionTypeUtils.isHighRisk(actionType);
        const isReversible = action_type_enum_1.ActionTypeUtils.isReversible(actionType);
        const estimatedTime = action_type_enum_1.ActionTypeUtils.getExecutionTimeEstimate(actionType);
        const requiredPermissions = action_type_enum_1.ActionTypeUtils.getRequiredPermissions(actionType);
        const approvalReqs = action_type_enum_1.ActionTypeUtils.getApprovalRequirements(actionType);
        const successCriteria = action_type_enum_1.ActionTypeUtils.getSuccessCriteria(actionType);
        // Determine priority based on action characteristics
        let priority = 'normal';
        if (action_type_enum_1.ActionTypeUtils.getContainmentActionTypes().includes(actionType)) {
            priority = 'high';
        }
        else if (action_type_enum_1.ActionTypeUtils.getEradicationActionTypes().includes(actionType)) {
            priority = 'high';
        }
        else if (isHighRisk) {
            priority = 'high';
        }
        // Set up rollback info for reversible actions
        let rollbackInfo = undefined;
        if (isReversible) {
            rollbackInfo = {
                canRollback: true,
                rollbackSteps: ResponseActionFactory.generateRollbackSteps(actionType),
            };
        }
        // Generate tags based on action characteristics
        const tags = [];
        if (isAutomated)
            tags.push('automated');
        if (!isAutomated)
            tags.push('manual');
        if (isHighRisk)
            tags.push('high-risk');
        if (isReversible)
            tags.push('reversible');
        if (action_type_enum_1.ActionTypeUtils.getContainmentActionTypes().includes(actionType))
            tags.push('containment');
        if (action_type_enum_1.ActionTypeUtils.getEradicationActionTypes().includes(actionType))
            tags.push('eradication');
        if (action_type_enum_1.ActionTypeUtils.getRecoveryActionTypes().includes(actionType))
            tags.push('recovery');
        if (action_type_enum_1.ActionTypeUtils.getNotificationActionTypes().includes(actionType))
            tags.push('notification');
        return {
            priority,
            isAutomated,
            isReversible,
            estimatedDurationMinutes: estimatedTime,
            requiredPermissions,
            approvalRequired: approvalReqs.required,
            approvalLevel: approvalReqs.level !== 'none' ? approvalReqs.level : undefined,
            successCriteria,
            maxRetries: isAutomated ? 3 : 1,
            retryDelayMinutes: 5,
            rollbackInfo,
            tags,
            timeoutMinutes: Math.max(estimatedTime * 2, 30), // Timeout is 2x estimated time, minimum 30 minutes
        };
    }
    /**
     * Generate rollback steps for an action type
     */
    static generateRollbackSteps(actionType) {
        const rollbackSteps = {
            [action_type_enum_1.ActionType.BLOCK_IP]: [
                'Remove IP from block list',
                'Update firewall rules',
                'Verify IP traffic is allowed',
            ],
            [action_type_enum_1.ActionType.BLOCK_DOMAIN]: [
                'Remove domain from block list',
                'Update DNS filtering rules',
                'Verify domain access is restored',
            ],
            [action_type_enum_1.ActionType.DISABLE_ACCOUNT]: [
                'Re-enable user account',
                'Restore account permissions',
                'Notify user of account restoration',
            ],
            [action_type_enum_1.ActionType.QUARANTINE_FILE]: [
                'Remove file from quarantine',
                'Restore file to original location',
                'Update file access permissions',
            ],
            [action_type_enum_1.ActionType.ISOLATE_SYSTEM]: [
                'Remove network isolation rules',
                'Restore system network connectivity',
                'Verify system communication',
            ],
            [action_type_enum_1.ActionType.UPDATE_FIREWALL]: [
                'Restore previous firewall configuration',
                'Apply rollback rules',
                'Verify network connectivity',
            ],
            [action_type_enum_1.ActionType.CHANGE_PERMISSIONS]: [
                'Restore previous permission settings',
                'Update access control lists',
                'Verify permission changes',
            ],
        };
        return rollbackSteps[actionType] || [
            'Identify changes made by original action',
            'Reverse the changes systematically',
            'Verify rollback completion',
        ];
    }
    /**
     * Validate action configuration
     */
    static validateActionConfiguration(options) {
        if (!options.actionType) {
            throw new Error('Action type is required');
        }
        if (!options.title || options.title.trim().length === 0) {
            throw new Error('Action title is required');
        }
        if (!options.parameters) {
            throw new Error('Action parameters are required');
        }
        // Validate action type specific requirements
        ResponseActionFactory.validateActionTypeRequirements(options.actionType, options.parameters);
    }
    /**
     * Validate action type specific requirements
     */
    static validateActionTypeRequirements(actionType, parameters) {
        switch (actionType) {
            case action_type_enum_1.ActionType.BLOCK_IP:
                if (!parameters.ipAddress) {
                    throw new Error('IP address parameter is required for BLOCK_IP action');
                }
                break;
            case action_type_enum_1.ActionType.BLOCK_DOMAIN:
                if (!parameters.domain) {
                    throw new Error('Domain parameter is required for BLOCK_DOMAIN action');
                }
                break;
            case action_type_enum_1.ActionType.DISABLE_ACCOUNT:
                if (!parameters.accountId && !parameters.username) {
                    throw new Error('Account ID or username parameter is required for DISABLE_ACCOUNT action');
                }
                break;
            case action_type_enum_1.ActionType.QUARANTINE_FILE:
                if (!parameters.filePath && !parameters.fileHash) {
                    throw new Error('File path or hash parameter is required for QUARANTINE_FILE action');
                }
                break;
            case action_type_enum_1.ActionType.SEND_EMAIL:
                if (!parameters.recipients || !parameters.subject) {
                    throw new Error('Recipients and subject parameters are required for SEND_EMAIL action');
                }
                break;
            // Add more validations as needed
        }
    }
}
exports.ResponseActionFactory = ResponseActionFactory;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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