0edbc4f5e02d33bd5462d6912a2d001b
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const unique_entity_id_value_object_1 = require("../../../../../shared-kernel/value-objects/unique-entity-id.value-object");
const ai_model_entity_1 = require("../../entities/ai-model.entity");
const ai_model_factory_1 = require("../ai-model.factory");
describe('AIModelFactory', () => {
    let validCreateRequest;
    let validReconstitutionData;
    beforeEach(() => {
        validCreateRequest = {
            name: 'Test Model',
            version: '1.0.0',
            provider: ai_model_entity_1.AIProvider.OPENAI,
            modelType: ai_model_entity_1.ModelType.LANGUAGE_MODEL,
            supportedTaskTypes: ['text-generation', 'classification'],
            tags: ['test', 'nlp'],
            priority: 5.0,
            weight: 2.0,
            maxConcurrentRequests: 100,
            metadata: { version: '1.0' },
        };
        const now = new Date();
        validReconstitutionData = {
            id: unique_entity_id_value_object_1.UniqueEntityId.generate().toString(),
            name: 'Test Model',
            version: '1.0.0',
            provider: ai_model_entity_1.AIProvider.OPENAI,
            modelType: ai_model_entity_1.ModelType.LANGUAGE_MODEL,
            configuration: {
                endpoint: 'https://api.example.com',
                apiKey: 'test-key',
                timeout: 30000,
                retries: 3,
                batchSize: 10,
                customSettings: {},
            },
            performance: {
                totalRequests: 100,
                successfulRequests: 95,
                failedRequests: 5,
                averageLatency: 200,
                p95Latency: 300,
                p99Latency: 500,
                accuracy: 0.95,
                precision: 0.92,
                recall: 0.88,
                f1Score: 0.90,
                throughput: 50,
                lastUpdated: now,
            },
            status: ai_model_entity_1.ModelStatus.ACTIVE,
            capabilities: {
                maxInputLength: 4096,
                maxOutputLength: 2048,
                supportsBatch: true,
                supportsStreaming: false,
                supportsFineTuning: true,
                languages: ['en', 'es'],
                modalities: ['text'],
            },
            resourceRequirements: {
                cpu: 2,
                memory: 2048,
                gpu: 1,
                storage: 1024,
                bandwidth: 100,
            },
            supportedTaskTypes: ['text-generation', 'classification'],
            tags: ['test', 'nlp'],
            priority: 5.0,
            weight: 2.0,
            maxConcurrentRequests: 100,
            currentLoad: 10,
            lastHealthCheck: now,
            lastUsed: now,
            deployedAt: now,
            metadata: { version: '1.0' },
            createdAt: now,
            updatedAt: now,
        };
    });
    describe('create', () => {
        it('should create a valid AI model', () => {
            const model = ai_model_factory_1.AIModelFactory.create(validCreateRequest);
            expect(model.name).toBe(validCreateRequest.name);
            expect(model.version).toBe(validCreateRequest.version);
            expect(model.provider).toBe(validCreateRequest.provider);
            expect(model.modelType).toBe(validCreateRequest.modelType);
            expect(model.status).toBe(ai_model_entity_1.ModelStatus.INACTIVE); // New models start inactive
            expect(model.supportedTaskTypes).toEqual(validCreateRequest.supportedTaskTypes);
            expect(model.tags).toEqual(validCreateRequest.tags);
            expect(model.priority).toBe(validCreateRequest.priority);
            expect(model.weight).toBe(validCreateRequest.weight);
            expect(model.maxConcurrentRequests).toBe(validCreateRequest.maxConcurrentRequests);
            expect(model.currentLoad).toBe(0);
            expect(model.createdAt).toBeInstanceOf(Date);
            expect(model.updatedAt).toBeInstanceOf(Date);
        });
        it('should create with custom ID', () => {
            const customId = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const model = ai_model_factory_1.AIModelFactory.create(validCreateRequest, customId);
            expect(model.id.equals(customId)).toBe(true);
        });
        it('should create with default values when optional fields are omitted', () => {
            const minimalRequest = {
                name: 'Minimal Model',
                version: '1.0.0',
                provider: ai_model_entity_1.AIProvider.PYTHON_AI,
                modelType: ai_model_entity_1.ModelType.CLASSIFICATION,
                supportedTaskTypes: ['classification'],
            };
            const model = ai_model_factory_1.AIModelFactory.create(minimalRequest);
            expect(model.tags).toEqual([]);
            expect(model.priority).toBe(1.0);
            expect(model.weight).toBe(1.0);
            expect(model.maxConcurrentRequests).toBe(100);
            expect(model.metadata).toEqual({});
            expect(model.configuration.timeout).toBe(30000);
            expect(model.configuration.retries).toBe(3);
            expect(model.configuration.batchSize).toBe(10);
            expect(model.capabilities.maxInputLength).toBe(4096);
            expect(model.capabilities.languages).toEqual(['en']);
        });
        it('should create with partial configuration', () => {
            const requestWithConfig = {
                ...validCreateRequest,
                configuration: {
                    endpoint: 'https://custom.api.com',
                    timeout: 60000,
                },
            };
            const model = ai_model_factory_1.AIModelFactory.create(requestWithConfig);
            expect(model.configuration.endpoint).toBe('https://custom.api.com');
            expect(model.configuration.timeout).toBe(60000);
            expect(model.configuration.retries).toBe(3); // Default value
            expect(model.configuration.batchSize).toBe(10); // Default value
        });
        it('should trim whitespace from name and version', () => {
            const requestWithWhitespace = {
                ...validCreateRequest,
                name: '  Test Model  ',
                version: '  1.0.0  ',
            };
            const model = ai_model_factory_1.AIModelFactory.create(requestWithWhitespace);
            expect(model.name).toBe('Test Model');
            expect(model.version).toBe('1.0.0');
        });
        describe('validation', () => {
            it('should throw error for empty name', () => {
                const invalidRequest = { ...validCreateRequest, name: '' };
                expect(() => ai_model_factory_1.AIModelFactory.create(invalidRequest)).toThrow('Model name is required');
            });
            it('should throw error for whitespace-only name', () => {
                const invalidRequest = { ...validCreateRequest, name: '   ' };
                expect(() => ai_model_factory_1.AIModelFactory.create(invalidRequest)).toThrow('Model name is required');
            });
            it('should throw error for name too long', () => {
                const invalidRequest = { ...validCreateRequest, name: 'a'.repeat(256) };
                expect(() => ai_model_factory_1.AIModelFactory.create(invalidRequest)).toThrow('Model name cannot exceed 255 characters');
            });
            it('should throw error for empty version', () => {
                const invalidRequest = { ...validCreateRequest, version: '' };
                expect(() => ai_model_factory_1.AIModelFactory.create(invalidRequest)).toThrow('Model version is required');
            });
            it('should throw error for version too long', () => {
                const invalidRequest = { ...validCreateRequest, version: 'a'.repeat(51) };
                expect(() => ai_model_factory_1.AIModelFactory.create(invalidRequest)).toThrow('Model version cannot exceed 50 characters');
            });
            it('should throw error for invalid provider', () => {
                const invalidRequest = { ...validCreateRequest, provider: 'invalid' };
                expect(() => ai_model_factory_1.AIModelFactory.create(invalidRequest)).toThrow('Invalid AI provider');
            });
            it('should throw error for invalid model type', () => {
                const invalidRequest = { ...validCreateRequest, modelType: 'invalid' };
                expect(() => ai_model_factory_1.AIModelFactory.create(invalidRequest)).toThrow('Invalid model type');
            });
            it('should throw error for empty supported task types', () => {
                const invalidRequest = { ...validCreateRequest, supportedTaskTypes: [] };
                expect(() => ai_model_factory_1.AIModelFactory.create(invalidRequest)).toThrow('At least one supported task type is required');
            });
            it('should throw error for empty task type in array', () => {
                const invalidRequest = { ...validCreateRequest, supportedTaskTypes: ['valid', ''] };
                expect(() => ai_model_factory_1.AIModelFactory.create(invalidRequest)).toThrow('All supported task types must be non-empty strings');
            });
            it('should throw error for invalid priority', () => {
                const invalidRequest = { ...validCreateRequest, priority: 15 };
                expect(() => ai_model_factory_1.AIModelFactory.create(invalidRequest)).toThrow('Priority must be between 0 and 10');
            });
            it('should throw error for negative priority', () => {
                const invalidRequest = { ...validCreateRequest, priority: -1 };
                expect(() => ai_model_factory_1.AIModelFactory.create(invalidRequest)).toThrow('Priority must be between 0 and 10');
            });
            it('should throw error for invalid weight', () => {
                const invalidRequest = { ...validCreateRequest, weight: 15 };
                expect(() => ai_model_factory_1.AIModelFactory.create(invalidRequest)).toThrow('Weight must be between 0 and 10');
            });
            it('should throw error for zero max concurrent requests', () => {
                const invalidRequest = { ...validCreateRequest, maxConcurrentRequests: 0 };
                expect(() => ai_model_factory_1.AIModelFactory.create(invalidRequest)).toThrow('Max concurrent requests must be greater than 0');
            });
            it('should throw error for negative max concurrent requests', () => {
                const invalidRequest = { ...validCreateRequest, maxConcurrentRequests: -1 };
                expect(() => ai_model_factory_1.AIModelFactory.create(invalidRequest)).toThrow('Max concurrent requests must be greater than 0');
            });
            it('should throw error for invalid configuration timeout', () => {
                const invalidRequest = {
                    ...validCreateRequest,
                    configuration: { timeout: 0 },
                };
                expect(() => ai_model_factory_1.AIModelFactory.create(invalidRequest)).toThrow('Configuration timeout must be greater than 0');
            });
            it('should throw error for negative configuration retries', () => {
                const invalidRequest = {
                    ...validCreateRequest,
                    configuration: { retries: -1 },
                };
                expect(() => ai_model_factory_1.AIModelFactory.create(invalidRequest)).toThrow('Configuration retries cannot be negative');
            });
            it('should throw error for invalid configuration batch size', () => {
                const invalidRequest = {
                    ...validCreateRequest,
                    configuration: { batchSize: 0 },
                };
                expect(() => ai_model_factory_1.AIModelFactory.create(invalidRequest)).toThrow('Configuration batch size must be greater than 0');
            });
            it('should throw error for empty tag in array', () => {
                const invalidRequest = { ...validCreateRequest, tags: ['valid', ''] };
                expect(() => ai_model_factory_1.AIModelFactory.create(invalidRequest)).toThrow('All tags must be non-empty strings');
            });
        });
    });
    describe('reconstitute', () => {
        it('should reconstitute a valid AI model', () => {
            const model = ai_model_factory_1.AIModelFactory.reconstitute(validReconstitutionData);
            expect(model.id.toString()).toBe(validReconstitutionData.id);
            expect(model.name).toBe(validReconstitutionData.name);
            expect(model.version).toBe(validReconstitutionData.version);
            expect(model.provider).toBe(validReconstitutionData.provider);
            expect(model.modelType).toBe(validReconstitutionData.modelType);
            expect(model.status).toBe(validReconstitutionData.status);
            expect(model.currentLoad).toBe(validReconstitutionData.currentLoad);
            expect(model.createdAt).toBe(validReconstitutionData.createdAt);
            expect(model.updatedAt).toBe(validReconstitutionData.updatedAt);
        });
        describe('validation', () => {
            it('should throw error for invalid ID', () => {
                const invalidData = { ...validReconstitutionData, id: 'invalid-id' };
                expect(() => ai_model_factory_1.AIModelFactory.reconstitute(invalidData)).toThrow('Valid ID is required for reconstitution');
            });
            it('should throw error for empty name', () => {
                const invalidData = { ...validReconstitutionData, name: '' };
                expect(() => ai_model_factory_1.AIModelFactory.reconstitute(invalidData)).toThrow('Model name is required');
            });
            it('should throw error for empty version', () => {
                const invalidData = { ...validReconstitutionData, version: '' };
                expect(() => ai_model_factory_1.AIModelFactory.reconstitute(invalidData)).toThrow('Model version is required');
            });
            it('should throw error for invalid provider', () => {
                const invalidData = { ...validReconstitutionData, provider: 'invalid' };
                expect(() => ai_model_factory_1.AIModelFactory.reconstitute(invalidData)).toThrow('Invalid AI provider');
            });
            it('should throw error for invalid model type', () => {
                const invalidData = { ...validReconstitutionData, modelType: 'invalid' };
                expect(() => ai_model_factory_1.AIModelFactory.reconstitute(invalidData)).toThrow('Invalid model type');
            });
            it('should throw error for invalid status', () => {
                const invalidData = { ...validReconstitutionData, status: 'invalid' };
                expect(() => ai_model_factory_1.AIModelFactory.reconstitute(invalidData)).toThrow('Invalid model status');
            });
            it('should throw error for empty supported task types', () => {
                const invalidData = { ...validReconstitutionData, supportedTaskTypes: [] };
                expect(() => ai_model_factory_1.AIModelFactory.reconstitute(invalidData)).toThrow('At least one supported task type is required');
            });
            it('should throw error for invalid priority', () => {
                const invalidData = { ...validReconstitutionData, priority: 15 };
                expect(() => ai_model_factory_1.AIModelFactory.reconstitute(invalidData)).toThrow('Priority must be between 0 and 10');
            });
            it('should throw error for invalid weight', () => {
                const invalidData = { ...validReconstitutionData, weight: -1 };
                expect(() => ai_model_factory_1.AIModelFactory.reconstitute(invalidData)).toThrow('Weight must be between 0 and 10');
            });
            it('should throw error for invalid max concurrent requests', () => {
                const invalidData = { ...validReconstitutionData, maxConcurrentRequests: 0 };
                expect(() => ai_model_factory_1.AIModelFactory.reconstitute(invalidData)).toThrow('Max concurrent requests must be greater than 0');
            });
            it('should throw error for negative current load', () => {
                const invalidData = { ...validReconstitutionData, currentLoad: -1 };
                expect(() => ai_model_factory_1.AIModelFactory.reconstitute(invalidData)).toThrow('Current load cannot be negative');
            });
            it('should throw error for invalid creation date', () => {
                const invalidData = { ...validReconstitutionData, createdAt: 'invalid' };
                expect(() => ai_model_factory_1.AIModelFactory.reconstitute(invalidData)).toThrow('Valid creation date is required');
            });
            it('should throw error for invalid update date', () => {
                const invalidData = { ...validReconstitutionData, updatedAt: 'invalid' };
                expect(() => ai_model_factory_1.AIModelFactory.reconstitute(invalidData)).toThrow('Valid update date is required');
            });
        });
    });
    describe('createForTesting', () => {
        it('should create a test model with default values', () => {
            const model = ai_model_factory_1.AIModelFactory.createForTesting();
            expect(model.name).toBe('Test Model');
            expect(model.version).toBe('1.0.0');
            expect(model.provider).toBe(ai_model_entity_1.AIProvider.PYTHON_AI);
            expect(model.modelType).toBe(ai_model_entity_1.ModelType.CLASSIFICATION);
            expect(model.supportedTaskTypes).toEqual(['classification', 'analysis']);
            expect(model.tags).toEqual(['test']);
            expect(model.priority).toBe(1.0);
            expect(model.weight).toBe(1.0);
            expect(model.maxConcurrentRequests).toBe(10);
            expect(model.metadata.test).toBe(true);
        });
        it('should create a test model with overrides', () => {
            const overrides = {
                name: 'Custom Test Model',
                provider: ai_model_entity_1.AIProvider.OPENAI,
                maxConcurrentRequests: 50,
            };
            const model = ai_model_factory_1.AIModelFactory.createForTesting(overrides);
            expect(model.name).toBe('Custom Test Model');
            expect(model.provider).toBe(ai_model_entity_1.AIProvider.OPENAI);
            expect(model.maxConcurrentRequests).toBe(50);
            expect(model.version).toBe('1.0.0'); // Default value preserved
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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