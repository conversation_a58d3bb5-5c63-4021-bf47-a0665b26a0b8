f29979c3531d62a0c1840fc12957b588
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const response_action_status_changed_domain_event_1 = require("../response-action-status-changed.domain-event");
const shared_kernel_1 = require("../../../../../shared-kernel");
const action_status_enum_1 = require("../../enums/action-status.enum");
describe('ResponseActionStatusChangedDomainEvent', () => {
    let aggregateId;
    let eventData;
    let event;
    beforeEach(() => {
        aggregateId = shared_kernel_1.UniqueEntityId.generate();
        eventData = {
            oldStatus: action_status_enum_1.ActionStatus.PENDING,
            newStatus: action_status_enum_1.ActionStatus.APPROVED,
            changedBy: '<EMAIL>',
            notes: 'Approved for execution',
            timestamp: new Date(),
        };
        event = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, eventData);
    });
    describe('creation', () => {
        it('should create event with required data', () => {
            expect(event).toBeInstanceOf(response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent);
            expect(event.aggregateId).toBe(aggregateId);
            expect(event.eventData).toBe(eventData);
            expect(event.occurredOn).toBeInstanceOf(Date);
        });
        it('should create event with custom options', () => {
            const customOptions = {
                eventId: shared_kernel_1.UniqueEntityId.generate(),
                occurredOn: new Date('2023-01-01'),
                eventVersion: 2,
                correlationId: 'corr-123',
                causationId: 'cause-456',
                metadata: { source: 'test' },
            };
            const customEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, eventData, customOptions);
            expect(customEvent.eventId).toBe(customOptions.eventId);
            expect(customEvent.occurredOn).toBe(customOptions.occurredOn);
            expect(customEvent.eventVersion).toBe(customOptions.eventVersion);
            expect(customEvent.correlationId).toBe(customOptions.correlationId);
            expect(customEvent.causationId).toBe(customOptions.causationId);
            expect(customEvent.metadata).toEqual(customOptions.metadata);
        });
    });
    describe('getters', () => {
        it('should provide access to event data properties', () => {
            expect(event.oldStatus).toBe(action_status_enum_1.ActionStatus.PENDING);
            expect(event.newStatus).toBe(action_status_enum_1.ActionStatus.APPROVED);
            expect(event.changedBy).toBe('<EMAIL>');
            expect(event.notes).toBe('Approved for execution');
            expect(event.changeTimestamp).toBe(eventData.timestamp);
        });
    });
    describe('status transition analysis', () => {
        it('should identify progression transitions', () => {
            expect(event.isProgression()).toBe(true);
            const regressionEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.PAUSED,
            });
            expect(regressionEvent.isProgression()).toBe(false);
        });
        it('should identify regression transitions', () => {
            const regressionEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.PAUSED,
            });
            expect(regressionEvent.isRegression()).toBe(true);
            expect(event.isRegression()).toBe(false);
        });
        it('should identify terminal transitions', () => {
            const terminalEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.COMPLETED,
            });
            expect(terminalEvent.isTerminalTransition()).toBe(true);
            expect(event.isTerminalTransition()).toBe(false);
        });
        it('should identify success transitions', () => {
            const successEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.COMPLETED,
            });
            expect(successEvent.isSuccessTransition()).toBe(true);
            expect(event.isSuccessTransition()).toBe(false);
            const partialEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.PARTIAL,
            });
            expect(partialEvent.isSuccessTransition()).toBe(true);
        });
        it('should identify failure transitions', () => {
            const failureEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.FAILED,
            });
            expect(failureEvent.isFailureTransition()).toBe(true);
            expect(event.isFailureTransition()).toBe(false);
            const timeoutEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.TIMEOUT,
            });
            expect(timeoutEvent.isFailureTransition()).toBe(true);
            const cancelledEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.CANCELLED,
            });
            expect(cancelledEvent.isFailureTransition()).toBe(true);
            const rejectedEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.PENDING,
                newStatus: action_status_enum_1.ActionStatus.REJECTED,
            });
            expect(rejectedEvent.isFailureTransition()).toBe(true);
        });
        it('should identify actions requiring attention', () => {
            const failedEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.FAILED,
            });
            expect(failedEvent.requiresAttention()).toBe(true);
            const interventionEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.MANUAL_INTERVENTION,
            });
            expect(interventionEvent.requiresAttention()).toBe(true);
            const partialEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.PARTIAL,
            });
            expect(partialEvent.requiresAttention()).toBe(true);
            const timeoutEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.TIMEOUT,
            });
            expect(timeoutEvent.requiresAttention()).toBe(true);
            const holdEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.QUEUED,
                newStatus: action_status_enum_1.ActionStatus.ON_HOLD,
            });
            expect(holdEvent.requiresAttention()).toBe(true);
            expect(event.requiresAttention()).toBe(false);
        });
    });
    describe('specific transition types', () => {
        it('should identify execution start', () => {
            const executionStartEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.QUEUED,
                newStatus: action_status_enum_1.ActionStatus.EXECUTING,
            });
            expect(executionStartEvent.isExecutionStart()).toBe(true);
            expect(event.isExecutionStart()).toBe(false);
        });
        it('should identify execution end', () => {
            const executionEndEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.COMPLETED,
            });
            expect(executionEndEvent.isExecutionEnd()).toBe(true);
            expect(event.isExecutionEnd()).toBe(false);
        });
        it('should identify approval transition', () => {
            expect(event.isApprovalTransition()).toBe(true);
            const nonApprovalEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.QUEUED,
                newStatus: action_status_enum_1.ActionStatus.EXECUTING,
            });
            expect(nonApprovalEvent.isApprovalTransition()).toBe(false);
        });
        it('should identify rejection transition', () => {
            const rejectionEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.PENDING,
                newStatus: action_status_enum_1.ActionStatus.REJECTED,
            });
            expect(rejectionEvent.isRejectionTransition()).toBe(true);
            expect(event.isRejectionTransition()).toBe(false);
        });
        it('should identify retry transition', () => {
            const retryEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.FAILED,
                newStatus: action_status_enum_1.ActionStatus.RETRYING,
            });
            expect(retryEvent.isRetryTransition()).toBe(true);
            expect(event.isRetryTransition()).toBe(false);
        });
        it('should identify pause transition', () => {
            const pauseEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.PAUSED,
            });
            expect(pauseEvent.isPauseTransition()).toBe(true);
            expect(event.isPauseTransition()).toBe(false);
        });
        it('should identify resume transition', () => {
            const resumeEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.PAUSED,
                newStatus: action_status_enum_1.ActionStatus.EXECUTING,
            });
            expect(resumeEvent.isResumeTransition()).toBe(true);
            expect(event.isResumeTransition()).toBe(false);
        });
    });
    describe('change categorization', () => {
        it('should categorize approval changes', () => {
            expect(event.getChangeCategory()).toBe('approval');
        });
        it('should categorize execution changes', () => {
            const executionStartEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.QUEUED,
                newStatus: action_status_enum_1.ActionStatus.EXECUTING,
            });
            expect(executionStartEvent.getChangeCategory()).toBe('execution');
            const executionEndEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.COMPLETED,
            });
            expect(executionEndEvent.getChangeCategory()).toBe('execution');
        });
        it('should categorize completion changes', () => {
            const completionEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.QUEUED,
                newStatus: action_status_enum_1.ActionStatus.COMPLETED,
            });
            expect(completionEvent.getChangeCategory()).toBe('completion');
        });
        it('should categorize failure changes', () => {
            const failureEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.QUEUED,
                newStatus: action_status_enum_1.ActionStatus.FAILED,
            });
            expect(failureEvent.getChangeCategory()).toBe('failure');
        });
        it('should categorize intervention changes', () => {
            const interventionEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.MANUAL_INTERVENTION,
            });
            expect(interventionEvent.getChangeCategory()).toBe('intervention');
        });
        it('should categorize other changes', () => {
            const queueEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.APPROVED,
                newStatus: action_status_enum_1.ActionStatus.QUEUED,
            });
            expect(queueEvent.getChangeCategory()).toBe('other');
        });
    });
    describe('recommended actions', () => {
        it('should recommend actions for approved status', () => {
            const actions = event.getRecommendedActions();
            expect(actions).toContain('Queue action for execution');
            expect(actions).toContain('Validate execution prerequisites');
        });
        it('should recommend actions for executing status', () => {
            const executingEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.QUEUED,
                newStatus: action_status_enum_1.ActionStatus.EXECUTING,
            });
            const actions = executingEvent.getRecommendedActions();
            expect(actions).toContain('Monitor action progress');
            expect(actions).toContain('Set up timeout monitoring');
        });
        it('should recommend actions for completed status', () => {
            const completedEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.COMPLETED,
            });
            const actions = completedEvent.getRecommendedActions();
            expect(actions).toContain('Validate success criteria');
            expect(actions).toContain('Update related entities');
            expect(actions).toContain('Generate completion report');
        });
        it('should recommend actions for failed status', () => {
            const failedEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.FAILED,
            });
            const actions = failedEvent.getRecommendedActions();
            expect(actions).toContain('Analyze failure cause');
            expect(actions).toContain('Determine if retry is appropriate');
            expect(actions).toContain('Notify failure to stakeholders');
        });
        it('should recommend actions for manual intervention status', () => {
            const interventionEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.MANUAL_INTERVENTION,
            });
            const actions = interventionEvent.getRecommendedActions();
            expect(actions).toContain('Notify human operators');
            expect(actions).toContain('Provide intervention guidance');
            expect(actions).toContain('Escalate to appropriate team');
        });
        it('should recommend actions for timeout status', () => {
            const timeoutEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.TIMEOUT,
            });
            const actions = timeoutEvent.getRecommendedActions();
            expect(actions).toContain('Investigate timeout cause');
            expect(actions).toContain('Determine if retry is safe');
            expect(actions).toContain('Check system resources');
        });
        it('should recommend actions for partial status', () => {
            const partialEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.PARTIAL,
            });
            const actions = partialEvent.getRecommendedActions();
            expect(actions).toContain('Assess partial completion impact');
            expect(actions).toContain('Determine next steps');
            expect(actions).toContain('Consider manual completion');
        });
        it('should recommend actions for retrying status', () => {
            const retryingEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.FAILED,
                newStatus: action_status_enum_1.ActionStatus.RETRYING,
            });
            const actions = retryingEvent.getRecommendedActions();
            expect(actions).toContain('Monitor retry attempt');
            expect(actions).toContain('Prepare for potential failure');
        });
        it('should recommend actions for paused status', () => {
            const pausedEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.PAUSED,
            });
            const actions = pausedEvent.getRecommendedActions();
            expect(actions).toContain('Document pause reason');
            expect(actions).toContain('Set resume conditions');
        });
        it('should recommend actions for on hold status', () => {
            const holdEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.QUEUED,
                newStatus: action_status_enum_1.ActionStatus.ON_HOLD,
            });
            const actions = holdEvent.getRecommendedActions();
            expect(actions).toContain('Identify blocking dependencies');
            expect(actions).toContain('Set resolution timeline');
        });
        it('should recommend actions for cancelled status', () => {
            const cancelledEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.CANCELLED,
            });
            const actions = cancelledEvent.getRecommendedActions();
            expect(actions).toContain('Clean up partial execution');
            expect(actions).toContain('Document cancellation reason');
        });
        it('should recommend actions for rejected status', () => {
            const rejectedEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.PENDING,
                newStatus: action_status_enum_1.ActionStatus.REJECTED,
            });
            const actions = rejectedEvent.getRecommendedActions();
            expect(actions).toContain('Document rejection reason');
            expect(actions).toContain('Notify requester');
        });
    });
    describe('notification targets', () => {
        it('should identify targets for approval transitions', () => {
            const targets = event.getNotificationTargets();
            expect(targets).toContain('action-requestor');
            expect(targets).toContain('execution-team');
        });
        it('should identify targets for rejection transitions', () => {
            const rejectionEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.PENDING,
                newStatus: action_status_enum_1.ActionStatus.REJECTED,
            });
            const targets = rejectionEvent.getNotificationTargets();
            expect(targets).toContain('action-requestor');
            expect(targets).toContain('security-team');
        });
        it('should identify targets for execution start', () => {
            const executionStartEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.QUEUED,
                newStatus: action_status_enum_1.ActionStatus.EXECUTING,
            });
            const targets = executionStartEvent.getNotificationTargets();
            expect(targets).toContain('monitoring-team');
            expect(targets).toContain('action-requestor');
        });
        it('should identify targets for success transitions', () => {
            const successEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.COMPLETED,
            });
            const targets = successEvent.getNotificationTargets();
            expect(targets).toContain('action-requestor');
            expect(targets).toContain('security-team');
        });
        it('should identify targets for failure transitions', () => {
            const failureEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.FAILED,
            });
            const targets = failureEvent.getNotificationTargets();
            expect(targets).toContain('action-requestor');
            expect(targets).toContain('security-team');
            expect(targets).toContain('incident-response-team');
        });
        it('should identify targets for attention-requiring transitions', () => {
            const interventionEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.MANUAL_INTERVENTION,
            });
            const targets = interventionEvent.getNotificationTargets();
            expect(targets).toContain('on-call-engineers');
            expect(targets).toContain('security-analysts');
        });
    });
    describe('urgency and impact assessment', () => {
        it('should assess critical urgency for manual intervention', () => {
            const interventionEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.MANUAL_INTERVENTION,
            });
            expect(interventionEvent.getUrgencyLevel()).toBe('critical');
        });
        it('should assess high urgency for failure transitions', () => {
            const failureEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.FAILED,
            });
            expect(failureEvent.getUrgencyLevel()).toBe('high');
        });
        it('should assess medium urgency for attention-requiring transitions', () => {
            const partialEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.PARTIAL,
            });
            expect(partialEvent.getUrgencyLevel()).toBe('medium');
        });
        it('should assess low urgency for normal transitions', () => {
            expect(event.getUrgencyLevel()).toBe('low');
        });
        it('should assess high impact for terminal failures', () => {
            const failureEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.FAILED,
            });
            expect(failureEvent.getImpact()).toBe('high');
        });
        it('should assess low impact for terminal successes', () => {
            const successEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.COMPLETED,
            });
            expect(successEvent.getImpact()).toBe('low');
        });
        it('should assess medium impact for attention-requiring transitions', () => {
            const interventionEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.MANUAL_INTERVENTION,
            });
            expect(interventionEvent.getImpact()).toBe('medium');
        });
        it('should assess low impact for progression transitions', () => {
            expect(event.getImpact()).toBe('low');
        });
        it('should assess no impact for no-change transitions', () => {
            const noChangeEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.QUEUED,
                newStatus: action_status_enum_1.ActionStatus.SCHEDULED,
            });
            expect(noChangeEvent.getImpact()).toBe('none');
        });
    });
    describe('metrics generation', () => {
        it('should generate comprehensive metrics', () => {
            const metrics = event.getMetrics();
            expect(metrics.statusTransition).toBe('pending_to_approved');
            expect(metrics.isProgression).toBe(true);
            expect(metrics.isTerminal).toBe(false);
            expect(metrics.requiresAttention).toBe(false);
            expect(metrics.changeCategory).toBe('approval');
            expect(metrics.urgencyLevel).toBe('low');
            expect(metrics.impact).toBe('low');
        });
        it('should generate metrics for failure transitions', () => {
            const failureEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.EXECUTING,
                newStatus: action_status_enum_1.ActionStatus.FAILED,
            });
            const metrics = failureEvent.getMetrics();
            expect(metrics.statusTransition).toBe('executing_to_failed');
            expect(metrics.isProgression).toBe(true);
            expect(metrics.isTerminal).toBe(true);
            expect(metrics.requiresAttention).toBe(true);
            expect(metrics.changeCategory).toBe('execution');
            expect(metrics.urgencyLevel).toBe('high');
            expect(metrics.impact).toBe('high');
        });
    });
    describe('integration event conversion', () => {
        it('should convert to integration event format', () => {
            const integrationEvent = event.toIntegrationEvent();
            expect(integrationEvent.eventType).toBe('ResponseActionStatusChanged');
            expect(integrationEvent.action).toBe('response_action_status_changed');
            expect(integrationEvent.resource).toBe('ResponseAction');
            expect(integrationEvent.resourceId).toBe(aggregateId.toString());
            expect(integrationEvent.data).toBe(eventData);
            expect(integrationEvent.metadata).toEqual({
                statusTransition: 'pending_to_approved',
                changeCategory: 'approval',
                urgencyLevel: 'low',
                impact: 'low',
                isProgression: true,
                isTerminal: false,
                requiresAttention: false,
            });
        });
    });
    describe('edge cases', () => {
        it('should handle events without changedBy', () => {
            const noChangerEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                changedBy: undefined,
            });
            expect(noChangerEvent.changedBy).toBeUndefined();
            expect(noChangerEvent.getNotificationTargets()).toContain('action-requestor');
        });
        it('should handle events without notes', () => {
            const noNotesEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                notes: undefined,
            });
            expect(noNotesEvent.notes).toBeUndefined();
            expect(noNotesEvent.getRecommendedActions()).toContain('Queue action for execution');
        });
        it('should handle same status transitions', () => {
            const sameStatusEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: action_status_enum_1.ActionStatus.PENDING,
                newStatus: action_status_enum_1.ActionStatus.PENDING,
            });
            expect(sameStatusEvent.isProgression()).toBe(false);
            expect(sameStatusEvent.isRegression()).toBe(false);
            expect(sameStatusEvent.getImpact()).toBe('none');
        });
        it('should handle unknown status transitions', () => {
            const unknownEvent = new response_action_status_changed_domain_event_1.ResponseActionStatusChangedDomainEvent(aggregateId, {
                ...eventData,
                oldStatus: 'UNKNOWN_OLD',
                newStatus: 'UNKNOWN_NEW',
            });
            expect(unknownEvent.getChangeCategory()).toBe('other');
            expect(unknownEvent.getUrgencyLevel()).toBe('low');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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