aefebd1444c6169592956394eeae6d07
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const logging_module_1 = require("../logging.module");
const logger_service_1 = require("../logger.service");
const transport_manager_service_1 = require("../transport-manager.service");
const correlation_id_service_1 = require("../correlation-id.service");
const json_formatter_1 = require("../formatters/json.formatter");
const structured_formatter_1 = require("../formatters/structured.formatter");
const security_formatter_1 = require("../formatters/security.formatter");
const file_transport_1 = require("../transports/file.transport");
const logging_config_1 = require("../../config/logging.config");
/**
 * Integration tests for the logging infrastructure
 * Tests formatters, transports, and the complete logging pipeline
 */
describe('Logging Infrastructure Integration', () => {
    let module;
    let loggerService;
    let transportManager;
    let correlationService;
    let configService;
    beforeAll(async () => {
        module = await testing_1.Test.createTestingModule({
            imports: [
                config_1.ConfigModule.forRoot({
                    load: [logging_config_1.loggingConfig],
                    isGlobal: true,
                }),
                logging_module_1.LoggingModule,
            ],
        }).compile();
        loggerService = module.get(logger_service_1.LoggerService);
        transportManager = module.get(transport_manager_service_1.TransportManagerService);
        correlationService = module.get(correlation_id_service_1.CorrelationIdService);
        configService = module.get(config_1.ConfigService);
    });
    afterAll(async () => {
        await module.close();
    });
    describe('LoggerService', () => {
        it('should be defined', () => {
            expect(loggerService).toBeDefined();
        });
        it('should log messages with different levels', () => {
            expect(() => {
                loggerService.log('Test info message', 'TestContext');
                loggerService.error('Test error message', 'TestContext');
                loggerService.warn('Test warning message', 'TestContext');
                loggerService.debug('Test debug message', 'TestContext');
            }).not.toThrow();
        });
        it('should log with correlation ID', () => {
            const correlationId = correlationService.generateCorrelationId();
            correlationService.run(correlationId, () => {
                expect(() => {
                    loggerService.log('Test message with correlation', 'TestContext');
                }).not.toThrow();
            });
        });
    });
    describe('TransportManagerService', () => {
        it('should be defined', () => {
            expect(transportManager).toBeDefined();
        });
        it('should have formatters available', () => {
            const formatters = transportManager.getFormatters();
            expect(formatters.size).toBeGreaterThan(0);
            expect(formatters.has('json')).toBe(true);
            expect(formatters.has('structured')).toBe(true);
            expect(formatters.has('security')).toBe(true);
        });
        it('should provide configuration summary', () => {
            const summary = transportManager.getConfigurationSummary();
            expect(summary).toHaveProperty('transports');
            expect(summary).toHaveProperty('formatters');
            expect(summary).toHaveProperty('healthMonitoring');
            expect(Array.isArray(summary.transports)).toBe(true);
            expect(Array.isArray(summary.formatters)).toBe(true);
        });
    });
    describe('CorrelationIdService', () => {
        it('should be defined', () => {
            expect(correlationService).toBeDefined();
        });
        it('should generate correlation IDs', () => {
            const id1 = correlationService.generateCorrelationId();
            const id2 = correlationService.generateCorrelationId();
            expect(id1).toBeDefined();
            expect(id2).toBeDefined();
            expect(id1).not.toBe(id2);
        });
        it('should maintain correlation context', () => {
            const correlationId = 'test-correlation-id';
            correlationService.run(correlationId, () => {
                expect(correlationService.getCorrelationId()).toBe(correlationId);
                correlationService.setUserId('test-user');
                const context = correlationService.getContext();
                expect(context?.userId).toBe('test-user');
                expect(context?.correlationId).toBe(correlationId);
            });
        });
        it('should create correlation ID from headers', () => {
            const headers = { 'x-correlation-id': 'header-correlation-id' };
            const correlationId = correlationService.fromHeaders(headers);
            expect(correlationId).toBe('header-correlation-id');
        });
    });
    describe('Formatters', () => {
        describe('JsonFormatter', () => {
            it('should format log entries as JSON', () => {
                const formatter = new json_formatter_1.JsonFormatter({
                    pretty: false,
                    includeStack: true,
                    includeMetadata: true,
                });
                const mockInfo = {
                    level: 'info',
                    message: 'Test message',
                    timestamp: new Date().toISOString(),
                    correlationId: 'test-correlation',
                    context: 'TestContext',
                };
                const result = formatter.format(mockInfo);
                expect(result).toBeDefined();
                expect(result.message).toBeDefined();
                // Should be valid JSON
                expect(() => JSON.parse(result.message)).not.toThrow();
            });
        });
        describe('StructuredFormatter', () => {
            it('should format log entries as structured text', () => {
                const formatter = new structured_formatter_1.StructuredFormatter({
                    includeTimestamp: true,
                    includeLevel: true,
                    includeCorrelation: true,
                });
                const mockInfo = {
                    level: 'info',
                    message: 'Test message',
                    timestamp: new Date().toISOString(),
                    correlationId: 'test-correlation',
                    context: 'TestContext',
                };
                const result = formatter.format(mockInfo);
                expect(result).toBeDefined();
                expect(result.message).toBeDefined();
                expect(typeof result.message).toBe('string');
                expect(result.message).toContain('level=INFO');
                expect(result.message).toContain('correlationId=test-correlation');
            });
        });
        describe('SecurityFormatter', () => {
            it('should format security log entries', () => {
                const formatter = new security_formatter_1.SecurityFormatter({
                    complianceFormat: 'json',
                    includeIndicators: true,
                    maskSensitiveData: true,
                });
                const mockInfo = {
                    level: 'warn',
                    message: 'Security event detected',
                    timestamp: new Date().toISOString(),
                    eventType: 'security_violation',
                    severity: 'high',
                    source: 'test-source',
                    correlationId: 'test-correlation',
                };
                const result = formatter.format(mockInfo);
                expect(result).toBeDefined();
                expect(result.message).toBeDefined();
                // Should be valid JSON for security events
                expect(() => JSON.parse(result.message)).not.toThrow();
                const parsed = JSON.parse(result.message);
                expect(parsed.eventType).toBe('security_violation');
                expect(parsed.severity).toBe('high');
            });
        });
    });
    describe('Transports', () => {
        describe('FileTransport', () => {
            it('should create file transport with configuration', () => {
                const config = {
                    name: 'test-file',
                    type: 'file',
                    enabled: true,
                    level: 'info',
                    options: {
                        filename: 'test.log',
                        maxSize: '10m',
                        maxFiles: 5,
                        tailable: true,
                        zippedArchive: true,
                    },
                };
                expect(() => {
                    const transport = new file_transport_1.FileTransport(config);
                    expect(transport).toBeDefined();
                    expect(transport.getName()).toBe('file');
                }).not.toThrow();
            });
        });
    });
    describe('Configuration', () => {
        it('should load logging configuration', () => {
            const config = configService.get('logging');
            expect(config).toBeDefined();
            expect(config).toHaveProperty('level');
            expect(config).toHaveProperty('format');
            expect(config).toHaveProperty('console');
            expect(config).toHaveProperty('file');
            expect(config).toHaveProperty('correlation');
            expect(config).toHaveProperty('masking');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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