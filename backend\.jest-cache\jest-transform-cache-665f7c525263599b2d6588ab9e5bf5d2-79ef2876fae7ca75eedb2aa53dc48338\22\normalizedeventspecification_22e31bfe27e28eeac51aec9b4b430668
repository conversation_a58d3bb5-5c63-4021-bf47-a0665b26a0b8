50da5f1953f967f6e8e34a030514a38f
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NormalizedEventSpecificationBuilder = exports.NormalizationDurationRangeSpecification = exports.PendingReviewSpecification = exports.ReviewedEventSpecification = exports.HighRiskNormalizedEventSpecification = exports.ExceededMaxAttemptsSpecification = exports.OriginalEventSpecification = exports.AppliedRuleSpecification = exports.DataQualityScoreRangeSpecification = exports.SchemaVersionSpecification = exports.NormalizationStatusSpecification = exports.ReadyForNextStageSpecification = exports.RequiresManualReviewSpecification = exports.HasValidationErrorsSpecification = exports.HighDataQualitySpecification = exports.NormalizationInProgressSpecification = exports.NormalizationFailedSpecification = exports.NormalizationCompletedSpecification = exports.NormalizedEventSpecification = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
/**
 * NormalizedEvent Specification Base Class
 *
 * Base class for all normalized event-related specifications.
 * Provides common functionality for normalized event filtering and validation.
 */
class NormalizedEventSpecification extends shared_kernel_1.BaseSpecification {
    /**
     * Helper method to check if normalized event matches any of the provided types
     */
    matchesAnyType(event, types) {
        return types.includes(event.type);
    }
    /**
     * Helper method to check if normalized event matches any of the provided severities
     */
    matchesAnySeverity(event, severities) {
        return severities.includes(event.severity);
    }
    /**
     * Helper method to check if normalized event matches any of the provided statuses
     */
    matchesAnyStatus(event, statuses) {
        return statuses.includes(event.status);
    }
    /**
     * Helper method to check if normalized event matches any of the provided processing statuses
     */
    matchesAnyProcessingStatus(event, statuses) {
        return statuses.includes(event.processingStatus);
    }
    /**
     * Helper method to check if normalized event matches any of the provided normalization statuses
     */
    matchesAnyNormalizationStatus(event, statuses) {
        return statuses.includes(event.normalizationStatus);
    }
    /**
     * Helper method to check if normalized event has any of the provided tags
     */
    hasAnyTag(event, tags) {
        return event.tags.some(tag => tags.includes(tag));
    }
    /**
     * Helper method to check if normalized event has all of the provided tags
     */
    hasAllTags(event, tags) {
        return tags.every(tag => event.tags.includes(tag));
    }
    /**
     * Helper method to check if data quality score is within range
     */
    isDataQualityScoreWithinRange(event, minScore, maxScore) {
        const dataQualityScore = event.dataQualityScore;
        if (dataQualityScore === undefined) {
            return minScore === undefined; // If no min score required, undefined is acceptable
        }
        if (minScore !== undefined && dataQualityScore < minScore) {
            return false;
        }
        if (maxScore !== undefined && dataQualityScore > maxScore) {
            return false;
        }
        return true;
    }
    /**
     * Helper method to check if risk score is within range
     */
    isRiskScoreWithinRange(event, minScore, maxScore) {
        const riskScore = event.riskScore;
        if (riskScore === undefined) {
            return minScore === undefined; // If no min score required, undefined is acceptable
        }
        if (minScore !== undefined && riskScore < minScore) {
            return false;
        }
        if (maxScore !== undefined && riskScore > maxScore) {
            return false;
        }
        return true;
    }
    /**
     * Helper method to check if schema version matches
     */
    matchesSchemaVersion(event, version) {
        return event.schemaVersion === version;
    }
    /**
     * Helper method to check if applied rule exists
     */
    hasAppliedRule(event, ruleId) {
        return event.hasAppliedRule(ruleId);
    }
}
exports.NormalizedEventSpecification = NormalizedEventSpecification;
/**
 * Normalization Completed Specification
 *
 * Specification for normalized events that have completed normalization.
 */
class NormalizationCompletedSpecification extends NormalizedEventSpecification {
    isSatisfiedBy(event) {
        return event.isNormalizationCompleted();
    }
    getDescription() {
        return 'Normalized event has completed normalization';
    }
}
exports.NormalizationCompletedSpecification = NormalizationCompletedSpecification;
/**
 * Normalization Failed Specification
 *
 * Specification for normalized events that have failed normalization.
 */
class NormalizationFailedSpecification extends NormalizedEventSpecification {
    isSatisfiedBy(event) {
        return event.isNormalizationFailed();
    }
    getDescription() {
        return 'Normalized event has failed normalization';
    }
}
exports.NormalizationFailedSpecification = NormalizationFailedSpecification;
/**
 * Normalization In Progress Specification
 *
 * Specification for normalized events that are currently being normalized.
 */
class NormalizationInProgressSpecification extends NormalizedEventSpecification {
    isSatisfiedBy(event) {
        return event.isNormalizationInProgress();
    }
    getDescription() {
        return 'Normalized event is currently being normalized';
    }
}
exports.NormalizationInProgressSpecification = NormalizationInProgressSpecification;
/**
 * High Data Quality Specification
 *
 * Specification for normalized events with high data quality scores.
 */
class HighDataQualitySpecification extends NormalizedEventSpecification {
    isSatisfiedBy(event) {
        return event.hasHighDataQuality();
    }
    getDescription() {
        return 'Normalized event has high data quality (>= 60)';
    }
}
exports.HighDataQualitySpecification = HighDataQualitySpecification;
/**
 * Has Validation Errors Specification
 *
 * Specification for normalized events that have validation errors.
 */
class HasValidationErrorsSpecification extends NormalizedEventSpecification {
    isSatisfiedBy(event) {
        return event.hasValidationErrors();
    }
    getDescription() {
        return 'Normalized event has validation errors';
    }
}
exports.HasValidationErrorsSpecification = HasValidationErrorsSpecification;
/**
 * Requires Manual Review Specification
 *
 * Specification for normalized events that require manual review.
 */
class RequiresManualReviewSpecification extends NormalizedEventSpecification {
    isSatisfiedBy(event) {
        return event.requiresManualReview;
    }
    getDescription() {
        return 'Normalized event requires manual review';
    }
}
exports.RequiresManualReviewSpecification = RequiresManualReviewSpecification;
/**
 * Ready For Next Stage Specification
 *
 * Specification for normalized events that are ready for the next processing stage.
 */
class ReadyForNextStageSpecification extends NormalizedEventSpecification {
    isSatisfiedBy(event) {
        return event.isReadyForNextStage();
    }
    getDescription() {
        return 'Normalized event is ready for next processing stage';
    }
}
exports.ReadyForNextStageSpecification = ReadyForNextStageSpecification;
/**
 * Normalization Status Specification
 *
 * Specification for normalized events with specific normalization statuses.
 */
class NormalizationStatusSpecification extends NormalizedEventSpecification {
    constructor(statuses) {
        super();
        this.statuses = statuses;
    }
    isSatisfiedBy(event) {
        return this.matchesAnyNormalizationStatus(event, this.statuses);
    }
    getDescription() {
        return `Normalized event status is one of: ${this.statuses.join(', ')}`;
    }
}
exports.NormalizationStatusSpecification = NormalizationStatusSpecification;
/**
 * Schema Version Specification
 *
 * Specification for normalized events with specific schema versions.
 */
class SchemaVersionSpecification extends NormalizedEventSpecification {
    constructor(version) {
        super();
        this.version = version;
    }
    isSatisfiedBy(event) {
        return this.matchesSchemaVersion(event, this.version);
    }
    getDescription() {
        return `Normalized event uses schema version: ${this.version}`;
    }
}
exports.SchemaVersionSpecification = SchemaVersionSpecification;
/**
 * Data Quality Score Range Specification
 *
 * Specification for normalized events within a specific data quality score range.
 */
class DataQualityScoreRangeSpecification extends NormalizedEventSpecification {
    constructor(minScore, maxScore) {
        super();
        this.minScore = minScore;
        this.maxScore = maxScore;
    }
    isSatisfiedBy(event) {
        return this.isDataQualityScoreWithinRange(event, this.minScore, this.maxScore);
    }
    getDescription() {
        if (this.minScore !== undefined && this.maxScore !== undefined) {
            return `Normalized event data quality score is between ${this.minScore} and ${this.maxScore}`;
        }
        else if (this.minScore !== undefined) {
            return `Normalized event data quality score is at least ${this.minScore}`;
        }
        else if (this.maxScore !== undefined) {
            return `Normalized event data quality score is at most ${this.maxScore}`;
        }
        return 'Normalized event has any data quality score';
    }
}
exports.DataQualityScoreRangeSpecification = DataQualityScoreRangeSpecification;
/**
 * Applied Rule Specification
 *
 * Specification for normalized events that have specific rules applied.
 */
class AppliedRuleSpecification extends NormalizedEventSpecification {
    constructor(ruleId) {
        super();
        this.ruleId = ruleId;
    }
    isSatisfiedBy(event) {
        return this.hasAppliedRule(event, this.ruleId);
    }
    getDescription() {
        return `Normalized event has applied rule: ${this.ruleId}`;
    }
}
exports.AppliedRuleSpecification = AppliedRuleSpecification;
/**
 * Original Event Specification
 *
 * Specification for normalized events that reference a specific original event.
 */
class OriginalEventSpecification extends NormalizedEventSpecification {
    constructor(originalEventId) {
        super();
        this.originalEventId = originalEventId;
    }
    isSatisfiedBy(event) {
        return event.originalEventId.equals(this.originalEventId);
    }
    getDescription() {
        return `Normalized event references original event: ${this.originalEventId.toString()}`;
    }
}
exports.OriginalEventSpecification = OriginalEventSpecification;
/**
 * Exceeded Max Attempts Specification
 *
 * Specification for normalized events that have exceeded maximum normalization attempts.
 */
class ExceededMaxAttemptsSpecification extends NormalizedEventSpecification {
    isSatisfiedBy(event) {
        return event.hasExceededMaxNormalizationAttempts();
    }
    getDescription() {
        return 'Normalized event has exceeded maximum normalization attempts';
    }
}
exports.ExceededMaxAttemptsSpecification = ExceededMaxAttemptsSpecification;
/**
 * High Risk Normalized Event Specification
 *
 * Specification for normalized events with high risk scores.
 */
class HighRiskNormalizedEventSpecification extends NormalizedEventSpecification {
    isSatisfiedBy(event) {
        return event.isHighRisk();
    }
    getDescription() {
        return 'Normalized event has high risk score (>= 80)';
    }
}
exports.HighRiskNormalizedEventSpecification = HighRiskNormalizedEventSpecification;
/**
 * Reviewed Event Specification
 *
 * Specification for normalized events that have been manually reviewed.
 */
class ReviewedEventSpecification extends NormalizedEventSpecification {
    isSatisfiedBy(event) {
        return event.reviewedAt !== undefined;
    }
    getDescription() {
        return 'Normalized event has been manually reviewed';
    }
}
exports.ReviewedEventSpecification = ReviewedEventSpecification;
/**
 * Pending Review Specification
 *
 * Specification for normalized events that are pending manual review.
 */
class PendingReviewSpecification extends NormalizedEventSpecification {
    isSatisfiedBy(event) {
        return event.requiresManualReview && event.reviewedAt === undefined;
    }
    getDescription() {
        return 'Normalized event is pending manual review';
    }
}
exports.PendingReviewSpecification = PendingReviewSpecification;
/**
 * Normalization Duration Range Specification
 *
 * Specification for normalized events with normalization duration within a specific range.
 */
class NormalizationDurationRangeSpecification extends NormalizedEventSpecification {
    constructor(minDurationMs, maxDurationMs) {
        super();
        this.minDurationMs = minDurationMs;
        this.maxDurationMs = maxDurationMs;
    }
    isSatisfiedBy(event) {
        const duration = event.getNormalizationDuration();
        if (duration === null) {
            return false; // No duration available
        }
        if (this.minDurationMs !== undefined && duration < this.minDurationMs) {
            return false;
        }
        if (this.maxDurationMs !== undefined && duration > this.maxDurationMs) {
            return false;
        }
        return true;
    }
    getDescription() {
        const formatDuration = (ms) => `${ms}ms`;
        if (this.minDurationMs !== undefined && this.maxDurationMs !== undefined) {
            return `Normalized event duration is between ${formatDuration(this.minDurationMs)} and ${formatDuration(this.maxDurationMs)}`;
        }
        else if (this.minDurationMs !== undefined) {
            return `Normalized event duration is at least ${formatDuration(this.minDurationMs)}`;
        }
        else if (this.maxDurationMs !== undefined) {
            return `Normalized event duration is at most ${formatDuration(this.maxDurationMs)}`;
        }
        return 'Normalized event has any duration';
    }
}
exports.NormalizationDurationRangeSpecification = NormalizationDurationRangeSpecification;
/**
 * Composite NormalizedEvent Specification Builder
 *
 * Builder for creating complex normalized event specifications using fluent interface.
 */
class NormalizedEventSpecificationBuilder {
    constructor() {
        this.specifications = [];
    }
    /**
     * Add normalization completed filter
     */
    normalizationCompleted() {
        this.specifications.push(new NormalizationCompletedSpecification());
        return this;
    }
    /**
     * Add normalization failed filter
     */
    normalizationFailed() {
        this.specifications.push(new NormalizationFailedSpecification());
        return this;
    }
    /**
     * Add normalization in progress filter
     */
    normalizationInProgress() {
        this.specifications.push(new NormalizationInProgressSpecification());
        return this;
    }
    /**
     * Add high data quality filter
     */
    highDataQuality() {
        this.specifications.push(new HighDataQualitySpecification());
        return this;
    }
    /**
     * Add has validation errors filter
     */
    hasValidationErrors() {
        this.specifications.push(new HasValidationErrorsSpecification());
        return this;
    }
    /**
     * Add requires manual review filter
     */
    requiresManualReview() {
        this.specifications.push(new RequiresManualReviewSpecification());
        return this;
    }
    /**
     * Add ready for next stage filter
     */
    readyForNextStage() {
        this.specifications.push(new ReadyForNextStageSpecification());
        return this;
    }
    /**
     * Add normalization status filter
     */
    withNormalizationStatus(...statuses) {
        this.specifications.push(new NormalizationStatusSpecification(statuses));
        return this;
    }
    /**
     * Add schema version filter
     */
    withSchemaVersion(version) {
        this.specifications.push(new SchemaVersionSpecification(version));
        return this;
    }
    /**
     * Add data quality score range filter
     */
    dataQualityScoreRange(minScore, maxScore) {
        this.specifications.push(new DataQualityScoreRangeSpecification(minScore, maxScore));
        return this;
    }
    /**
     * Add applied rule filter
     */
    withAppliedRule(ruleId) {
        this.specifications.push(new AppliedRuleSpecification(ruleId));
        return this;
    }
    /**
     * Add original event filter
     */
    fromOriginalEvent(originalEventId) {
        this.specifications.push(new OriginalEventSpecification(originalEventId));
        return this;
    }
    /**
     * Add exceeded max attempts filter
     */
    exceededMaxAttempts() {
        this.specifications.push(new ExceededMaxAttemptsSpecification());
        return this;
    }
    /**
     * Add high risk filter
     */
    highRisk() {
        this.specifications.push(new HighRiskNormalizedEventSpecification());
        return this;
    }
    /**
     * Add reviewed filter
     */
    reviewed() {
        this.specifications.push(new ReviewedEventSpecification());
        return this;
    }
    /**
     * Add pending review filter
     */
    pendingReview() {
        this.specifications.push(new PendingReviewSpecification());
        return this;
    }
    /**
     * Add normalization duration range filter
     */
    normalizationDurationRange(minDurationMs, maxDurationMs) {
        this.specifications.push(new NormalizationDurationRangeSpecification(minDurationMs, maxDurationMs));
        return this;
    }
    /**
     * Build the final specification using AND logic
     */
    build() {
        if (this.specifications.length === 0) {
            throw new Error('At least one specification must be added');
        }
        if (this.specifications.length === 1) {
            return this.specifications[0];
        }
        // Combine all specifications with AND logic
        let combined = this.specifications[0];
        for (let i = 1; i < this.specifications.length; i++) {
            combined = combined.and(this.specifications[i]);
        }
        return combined;
    }
    /**
     * Build the final specification using OR logic
     */
    buildWithOr() {
        if (this.specifications.length === 0) {
            throw new Error('At least one specification must be added');
        }
        if (this.specifications.length === 1) {
            return this.specifications[0];
        }
        // Combine all specifications with OR logic
        let combined = this.specifications[0];
        for (let i = 1; i < this.specifications.length; i++) {
            combined = combined.or(this.specifications[i]);
        }
        return combined;
    }
    /**
     * Create a new builder instance
     */
    static create() {
        return new NormalizedEventSpecificationBuilder();
    }
}
exports.NormalizedEventSpecificationBuilder = NormalizedEventSpecificationBuilder;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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