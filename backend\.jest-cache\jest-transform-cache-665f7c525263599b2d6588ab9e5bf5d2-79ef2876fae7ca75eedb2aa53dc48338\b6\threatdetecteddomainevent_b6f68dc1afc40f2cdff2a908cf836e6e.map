{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\threat-detected.domain-event.ts", "mappings": ";;;AAAA,6DAA4E;AAC5E,wEAA+D;AA+F/D;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,MAAa,yBAA0B,SAAQ,+BAAwC;IACrF,YACE,WAA2B,EAC3B,SAAkC,EAClC,OAOC;QAED,KAAK,CAAC,WAAW,EAAE,SAAS,EAAE;YAC5B,YAAY,EAAE,CAAC;YACf,GAAG,OAAO;YACV,QAAQ,EAAE;gBACR,SAAS,EAAE,gBAAgB;gBAC3B,MAAM,EAAE,UAAU;gBAClB,aAAa,EAAE,QAAQ;gBACvB,eAAe,EAAE,WAAW;gBAC5B,GAAG,OAAO,EAAE,QAAQ;aACrB;SACF,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,QAAQ,KAAK,qCAAc,CAAC,QAAQ,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,CAAC,qCAAc,CAAC,IAAI,EAAE,qCAAc,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,OAAO,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,KAAK,UAAU,CAAC,CAAC;IAC7E,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,2BAA2B;QACzB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,WAAW,KAAK,SAAS,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,OAAO,IAAI,CAAC,WAAW,EAAE,cAAc,KAAK,UAAU,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,OAAO,CAAC,iBAAiB,EAAE,oBAAoB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IACjF,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACH,yBAAyB;QACvB,OAAO,IAAI,CAAC,eAAe,CAAC,cAAc,KAAK,UAAU,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,kBAAkB,EAAE,WAAW,KAAK,SAAS,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,SAAS,EAAE,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,yBAAyB;QACvB,OAAO,IAAI,CAAC,kBAAkB,EAAE;YACzB,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC1D,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7D,CAAC,IAAI,CAAC,sBAAsB,EAAE,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,6BAA6B;QAC3B,OAAO,IAAI,CAAC,kBAAkB,EAAE;YACzB,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,IAAI,CAAC,qBAAqB,EAAE,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC,CAAC;IACxG,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,sBAAsB,EAAE,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,IAAI,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC;YACrC,OAAO,UAAU,CAAC;QACpB,CAAC;QACD,IAAI,IAAI,CAAC,qBAAqB,EAAE,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC;YAC5D,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,IAAI,IAAI,CAAC,QAAQ,KAAK,qCAAc,CAAC,MAAM,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;YAC5E,OAAO,QAAQ,CAAC;QAClB,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAAE,OAAO,EAAE,CAAC,CAAE,aAAa;QAC/D,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAAE,OAAO,EAAE,CAAC,CAAS,aAAa;QAC/D,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAAE,OAAO,GAAG,CAAC,CAAK,UAAU;QAC5D,IAAI,IAAI,CAAC,QAAQ,KAAK,qCAAc,CAAC,MAAM;YAAE,OAAO,GAAG,CAAC,CAAC,UAAU;QACnE,OAAO,IAAI,CAAC,CAAsC,WAAW;IAC/D,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAAE,OAAO,CAAC,CAAC,CAAG,UAAU;QAC5D,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAAE,OAAO,CAAC,CAAC,CAAU,UAAU;QAC5D,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAAE,OAAO,EAAE,CAAC,CAAM,WAAW;QAC7D,IAAI,IAAI,CAAC,QAAQ,KAAK,qCAAc,CAAC,MAAM;YAAE,OAAO,EAAE,CAAC,CAAC,SAAS;QACjE,OAAO,GAAG,CAAC,CAAuC,SAAS;IAC7D,CAAC;IAED;;OAEG;IACH,gCAAgC;QAC9B,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC;YACrC,OAAO,CAAC,IAAI,CAAC,0BAA0B,EAAE,qBAAqB,EAAE,8BAA8B,CAAC,CAAC;QAClG,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,yBAAyB,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;YACjC,OAAO,CAAC,IAAI,CAAC,2BAA2B,EAAE,sBAAsB,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,IAAI,CAAC,sBAAsB,EAAE,EAAE,CAAC;YAClC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,gBAAgB,EAAE,wBAAwB,CAAC,CAAC;QACjF,CAAC;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE,yBAAyB,EAAE,+BAA+B,CAAC,CAAC;QAChG,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,MAAM,OAAO,GAAa,CAAC,4BAA4B,CAAC,CAAC;QAEzD,IAAI,IAAI,CAAC,6BAA6B,EAAE,EAAE,CAAC;YACzC,OAAO,CAAC,IAAI,CAAC,qBAAqB,EAAE,gBAAgB,EAAE,oBAAoB,CAAC,CAAC;QAC9E,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;YACjC,OAAO,CAAC,IAAI,CAAC,wBAAwB,EAAE,iBAAiB,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;YACjC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,uBAAuB,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,IAAI,CAAC,sBAAsB,EAAE,EAAE,CAAC;YAClC,OAAO,CAAC,IAAI,CAAC,0BAA0B,EAAE,mBAAmB,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC;YACrC,OAAO,CAAC,IAAI,CAAC,0BAA0B,EAAE,qBAAqB,CAAC,CAAC;QAClE,CAAC;QAED,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,oBAAoB;IACpD,CAAC;IAED;;OAEG;IACH,yBAAyB;QAqBvB,OAAO;YACL,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,IAAI,EAAE,IAAI,CAAC,UAAU;YACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,IAAI,EAAE,IAAI,CAAC,UAAU;YACrB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,mBAAmB,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM;YAC/C,eAAe,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM;YACvC,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC5C,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,EAAE;YAChD,qBAAqB,EAAE,IAAI,CAAC,wBAAwB,EAAE;YACtD,yBAAyB,EAAE,IAAI,CAAC,yBAAyB,EAAE;YAC3D,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE;YACnD,sBAAsB,EAAE,IAAI,CAAC,sBAAsB,EAAE;YACrD,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,kBAAkB;SAC/D,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,kBAAkB;QAyEhB,MAAM,cAAc,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACtE,MAAM,wBAAwB,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;QAExF,OAAO;YACL,SAAS,EAAE,gBAAgB;YAC3B,OAAO,EAAE,KAAK;YACd,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;YACxC,IAAI,EAAE;gBACJ,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,MAAM,EAAE;oBACN,IAAI,EAAE,IAAI,CAAC,UAAU;oBACrB,WAAW,EAAE,IAAI,CAAC,WAAW;oBAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,IAAI,EAAE,IAAI,CAAC,UAAU;oBACrB,UAAU,EAAE,IAAI,CAAC,UAAU;oBAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;oBACzB,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;oBACzC,eAAe,EAAE,IAAI,CAAC,eAAe;oBACrC,eAAe,EAAE,IAAI,CAAC,eAAe;iBACtC;gBACD,MAAM,EAAE;oBACN,QAAQ,EAAE,IAAI,CAAC,cAAc;oBAC7B,sBAAsB,EAAE,IAAI,CAAC,qBAAqB,EAAE;iBACrD;gBACD,UAAU,EAAE;oBACV,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM;oBAC7B,KAAK,EAAE,cAAc;oBACrB,mBAAmB,EAAE,wBAAwB;iBAC9C;gBACD,MAAM,EAAE;oBACN,OAAO,EAAE,IAAI,CAAC,aAAa;oBAC3B,cAAc,EAAE,IAAI,CAAC,cAAc;oBACnC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;wBAC9B,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI;wBAC3B,cAAc,EAAE,IAAI,CAAC,WAAW,CAAC,cAAc;qBAChD,CAAC,CAAC,CAAC,SAAS;iBACd;gBACD,MAAM,EAAE;oBACN,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,eAAe;oBACrD,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,SAAS;oBACzC,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,YAAY;oBAC/C,cAAc,EAAE,IAAI,CAAC,eAAe,CAAC,cAAc;iBACpD;gBACD,YAAY,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC;oBACtC,SAAS,EAAE,IAAI,CAAC,kBAAkB,CAAC,SAAS;oBAC5C,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI;oBAClC,cAAc,EAAE,IAAI,CAAC,kBAAkB,CAAC,cAAc;oBACtD,WAAW,EAAE,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC,CAAC;wBACjD,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,OAAO;wBACpD,MAAM,EAAE,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,MAAM;qBACnD,CAAC,CAAC,CAAC,SAAS;iBACd,CAAC,CAAC,CAAC,SAAS;gBACb,QAAQ,EAAE;oBACR,QAAQ,EAAE,IAAI,CAAC,mBAAmB,EAAE;oBACpC,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,EAAE;oBAChD,qBAAqB,EAAE,IAAI,CAAC,wBAAwB,EAAE;oBACtD,yBAAyB,EAAE,IAAI,CAAC,yBAAyB,EAAE;oBAC3D,6BAA6B,EAAE,IAAI,CAAC,6BAA6B,EAAE;oBACnE,kBAAkB,EAAE,IAAI,CAAC,gCAAgC,EAAE;oBAC3D,mBAAmB,EAAE,IAAI,CAAC,sBAAsB,EAAE;iBACnD;aACF;YACD,QAAQ,EAAE;gBACR,aAAa,EAAE,IAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,MAAM,EAAE,UAAU;gBAClB,aAAa,EAAE,QAAQ;gBACvB,eAAe,EAAE,WAAW;aAC7B;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,GAAG,KAAK,CAAC,MAAM,EAAE;YACjB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE;gBACR,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,EAAE;gBAC7C,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE;gBACnD,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE;gBAC3C,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EAAE;gBACzC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EAAE;gBACzC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB,EAAE;gBACjD,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE;gBACnD,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE;gBACnD,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE;gBACnD,2BAA2B,EAAE,IAAI,CAAC,2BAA2B,EAAE;gBAC/D,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,EAAE;gBAC/C,sBAAsB,EAAE,IAAI,CAAC,sBAAsB,EAAE;gBACrD,oBAAoB,EAAE,IAAI,CAAC,oBAAoB,EAAE;gBACjD,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE;gBACnD,yBAAyB,EAAE,IAAI,CAAC,yBAAyB,EAAE;gBAC3D,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,EAAE;gBAC7C,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE;gBACnD,yBAAyB,EAAE,IAAI,CAAC,yBAAyB,EAAE;gBAC3D,6BAA6B,EAAE,IAAI,CAAC,6BAA6B,EAAE;gBACnE,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE;gBACnD,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,EAAE;gBAC5C,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,EAAE;gBAChD,qBAAqB,EAAE,IAAI,CAAC,wBAAwB,EAAE;gBACtD,6BAA6B,EAAE,IAAI,CAAC,gCAAgC,EAAE;gBACtE,mBAAmB,EAAE,IAAI,CAAC,sBAAsB,EAAE;gBAClD,sBAAsB,EAAE,IAAI,CAAC,yBAAyB,EAAE;aACzD;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,IAAyB;QACvC,OAAO,IAAI,yBAAyB,CAClC,8BAAc,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,EAC3C,IAAI,CAAC,SAAS,EACd;YACE,OAAO,EAAE,8BAAc,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC;YAChD,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;YACrC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;QACjD,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,qBAAqB,CAAC;QAC5F,MAAM,SAAS,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC,2BAA2B,CAAC,CAAC;YAC7D,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC,aAAa,IAAI,CAAC,cAAc,CAAC,MAAM,SAAS,CAAC,CAAC;gBACjF,wBAAwB,CAAC;QAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,WAAY,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAEpF,OAAO,GAAG,YAAY,qBAAqB,IAAI,CAAC,UAAU,mBAAmB,cAAc,KAAK,SAAS,GAAG,SAAS,EAAE,CAAC;IAC1H,CAAC;IAED;;OAEG;IACH,UAAU;QAeR,OAAO;YACL,SAAS,EAAE,gBAAgB;YAC3B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,IAAI,EAAE,IAAI,CAAC,UAAU;YACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,mBAAmB,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM;YAC/C,yBAAyB,EAAE,IAAI,CAAC,yBAAyB,EAAE;YAC3D,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC5C,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;SACzC,CAAC;IACJ,CAAC;CACF;AAjuBD,8DAiuBC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\threat-detected.domain-event.ts"], "sourcesContent": ["import { BaseDomainEvent, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { ThreatSeverity } from '../enums/threat-severity.enum';\r\nimport { EventTimestamp } from '../value-objects/event-metadata/event-timestamp.value-object';\r\nimport { ThreatSignature } from '../value-objects/threat-indicators/threat-signature.value-object';\r\nimport { CvssScore } from '../value-objects/threat-indicators/cvss-score.value-object';\r\nimport { IpAddress } from '../value-objects/network/ip-address.value-object';\r\n\r\n/**\r\n * Threat Detected Domain Event Data\r\n */\r\nexport interface ThreatDetectedEventData {\r\n  /** ID of the detected threat */\r\n  threatId: string;\r\n  /** Threat name or title */\r\n  name: string;\r\n  /** Threat description */\r\n  description: string;\r\n  /** Threat severity level */\r\n  severity: ThreatSeverity;\r\n  /** Threat category */\r\n  category: string;\r\n  /** Threat type */\r\n  type: string;\r\n  /** Threat signature for identification */\r\n  signature: ThreatSignature;\r\n  /** CVSS score if applicable */\r\n  cvssScore?: CvssScore;\r\n  /** Detection confidence (0-100) */\r\n  confidence: number;\r\n  /** Risk score (0-100) */\r\n  riskScore: number;\r\n  /** When the threat was detected */\r\n  detectedAt: EventTimestamp;\r\n  /** Detection method used */\r\n  detectionMethod: 'signature_based' | 'behavioral_analysis' | 'machine_learning' | 'threat_intelligence' | 'anomaly_detection' | 'manual_analysis';\r\n  /** Detection source/engine */\r\n  detectionSource: string;\r\n  /** Source IP address if applicable */\r\n  sourceIp?: IpAddress;\r\n  /** Target IP address if applicable */\r\n  targetIp?: IpAddress;\r\n  /** Affected assets */\r\n  affectedAssets: Array<{\r\n    id: string;\r\n    name: string;\r\n    type: string;\r\n    criticality: 'low' | 'medium' | 'high' | 'critical';\r\n  }>;\r\n  /** Indicators of compromise */\r\n  indicators: Array<{\r\n    type: 'ip' | 'domain' | 'url' | 'file_hash' | 'email' | 'registry_key' | 'process' | 'network_signature';\r\n    value: string;\r\n    confidence: number;\r\n    source: string;\r\n  }>;\r\n  /** Attack vectors identified */\r\n  attackVectors: string[];\r\n  /** Threat actor information if known */\r\n  threatActor?: {\r\n    name: string;\r\n    group?: string;\r\n    motivation: string;\r\n    sophistication: 'low' | 'medium' | 'high' | 'advanced';\r\n  };\r\n  /** Kill chain stage */\r\n  killChainStage: 'reconnaissance' | 'weaponization' | 'delivery' | 'exploitation' | 'installation' | 'command_control' | 'actions_objectives';\r\n  /** Potential impact assessment */\r\n  potentialImpact: {\r\n    confidentiality: 'none' | 'low' | 'high';\r\n    integrity: 'none' | 'low' | 'high';\r\n    availability: 'none' | 'low' | 'high';\r\n    scope: 'unchanged' | 'changed';\r\n    businessImpact: 'low' | 'medium' | 'high' | 'critical';\r\n  };\r\n  /** Related events or incidents */\r\n  relatedEvents?: string[];\r\n  /** Threat intelligence context */\r\n  threatIntelligence?: {\r\n    campaigns: string[];\r\n    ttps: string[]; // Tactics, Techniques, and Procedures\r\n    mitreAttackIds: string[];\r\n    geolocation?: {\r\n      country: string;\r\n      region: string;\r\n      coordinates?: { lat: number; lon: number };\r\n    };\r\n  };\r\n  /** Processing metadata */\r\n  processingMetadata: {\r\n    processingDuration: number; // milliseconds\r\n    analysisEngine: string;\r\n    engineVersion: string;\r\n    correlationId?: string;\r\n  };\r\n}\r\n\r\n/**\r\n * Threat Detected Domain Event\r\n * \r\n * Raised when a security threat is detected and confirmed by the threat detection\r\n * system. This event represents a high-confidence identification of malicious\r\n * activity that requires immediate attention and response.\r\n * \r\n * Key characteristics:\r\n * - Represents confirmed threat detection\r\n * - Contains comprehensive threat analysis and context\r\n * - Includes impact assessment and response guidance\r\n * - Triggers immediate response and containment workflows\r\n * \r\n * Downstream processes triggered:\r\n * - Incident creation and response\r\n * - Automated containment actions\r\n * - Threat hunting and investigation\r\n * - Alert generation and notification\r\n * - Threat intelligence updates\r\n * - Security metrics and reporting\r\n */\r\nexport class ThreatDetectedDomainEvent extends BaseDomainEvent<ThreatDetectedEventData> {\r\n  constructor(\r\n    aggregateId: UniqueEntityId,\r\n    eventData: ThreatDetectedEventData,\r\n    options?: {\r\n      eventId?: UniqueEntityId;\r\n      occurredOn?: Date;\r\n      eventVersion?: number;\r\n      correlationId?: string;\r\n      causationId?: string;\r\n      metadata?: Record<string, any>;\r\n    }\r\n  ) {\r\n    super(aggregateId, eventData, {\r\n      eventVersion: 1,\r\n      ...options,\r\n      metadata: {\r\n        eventType: 'ThreatDetected',\r\n        domain: 'Security',\r\n        aggregateType: 'Threat',\r\n        processingStage: 'detection',\r\n        ...options?.metadata,\r\n      },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get the threat ID\r\n   */\r\n  get threatId(): string {\r\n    return this.eventData.threatId;\r\n  }\r\n\r\n  /**\r\n   * Get the threat name\r\n   */\r\n  get threatName(): string {\r\n    return this.eventData.name;\r\n  }\r\n\r\n  /**\r\n   * Get the threat description\r\n   */\r\n  get description(): string {\r\n    return this.eventData.description;\r\n  }\r\n\r\n  /**\r\n   * Get the threat severity\r\n   */\r\n  get severity(): ThreatSeverity {\r\n    return this.eventData.severity;\r\n  }\r\n\r\n  /**\r\n   * Get the threat category\r\n   */\r\n  get category(): string {\r\n    return this.eventData.category;\r\n  }\r\n\r\n  /**\r\n   * Get the threat type\r\n   */\r\n  get threatType(): string {\r\n    return this.eventData.type;\r\n  }\r\n\r\n  /**\r\n   * Get the threat signature\r\n   */\r\n  get signature(): ThreatSignature {\r\n    return this.eventData.signature;\r\n  }\r\n\r\n  /**\r\n   * Get the CVSS score\r\n   */\r\n  get cvssScore(): CvssScore | undefined {\r\n    return this.eventData.cvssScore;\r\n  }\r\n\r\n  /**\r\n   * Get the detection confidence\r\n   */\r\n  get confidence(): number {\r\n    return this.eventData.confidence;\r\n  }\r\n\r\n  /**\r\n   * Get the risk score\r\n   */\r\n  get riskScore(): number {\r\n    return this.eventData.riskScore;\r\n  }\r\n\r\n  /**\r\n   * Get when the threat was detected\r\n   */\r\n  get detectedAt(): EventTimestamp {\r\n    return this.eventData.detectedAt;\r\n  }\r\n\r\n  /**\r\n   * Get the detection method\r\n   */\r\n  get detectionMethod(): ThreatDetectedEventData['detectionMethod'] {\r\n    return this.eventData.detectionMethod;\r\n  }\r\n\r\n  /**\r\n   * Get the detection source\r\n   */\r\n  get detectionSource(): string {\r\n    return this.eventData.detectionSource;\r\n  }\r\n\r\n  /**\r\n   * Get the source IP address\r\n   */\r\n  get sourceIp(): IpAddress | undefined {\r\n    return this.eventData.sourceIp;\r\n  }\r\n\r\n  /**\r\n   * Get the target IP address\r\n   */\r\n  get targetIp(): IpAddress | undefined {\r\n    return this.eventData.targetIp;\r\n  }\r\n\r\n  /**\r\n   * Get the affected assets\r\n   */\r\n  get affectedAssets(): ThreatDetectedEventData['affectedAssets'] {\r\n    return this.eventData.affectedAssets;\r\n  }\r\n\r\n  /**\r\n   * Get the indicators of compromise\r\n   */\r\n  get indicators(): ThreatDetectedEventData['indicators'] {\r\n    return this.eventData.indicators;\r\n  }\r\n\r\n  /**\r\n   * Get the attack vectors\r\n   */\r\n  get attackVectors(): string[] {\r\n    return this.eventData.attackVectors;\r\n  }\r\n\r\n  /**\r\n   * Get the threat actor information\r\n   */\r\n  get threatActor(): ThreatDetectedEventData['threatActor'] {\r\n    return this.eventData.threatActor;\r\n  }\r\n\r\n  /**\r\n   * Get the kill chain stage\r\n   */\r\n  get killChainStage(): ThreatDetectedEventData['killChainStage'] {\r\n    return this.eventData.killChainStage;\r\n  }\r\n\r\n  /**\r\n   * Get the potential impact\r\n   */\r\n  get potentialImpact(): ThreatDetectedEventData['potentialImpact'] {\r\n    return this.eventData.potentialImpact;\r\n  }\r\n\r\n  /**\r\n   * Get related events\r\n   */\r\n  get relatedEvents(): string[] | undefined {\r\n    return this.eventData.relatedEvents;\r\n  }\r\n\r\n  /**\r\n   * Get threat intelligence context\r\n   */\r\n  get threatIntelligence(): ThreatDetectedEventData['threatIntelligence'] {\r\n    return this.eventData.threatIntelligence;\r\n  }\r\n\r\n  /**\r\n   * Get processing metadata\r\n   */\r\n  get processingMetadata(): ThreatDetectedEventData['processingMetadata'] {\r\n    return this.eventData.processingMetadata;\r\n  }\r\n\r\n  /**\r\n   * Check if threat is critical severity\r\n   */\r\n  isCriticalSeverity(): boolean {\r\n    return this.severity === ThreatSeverity.CRITICAL;\r\n  }\r\n\r\n  /**\r\n   * Check if threat is high severity or above\r\n   */\r\n  isHighSeverityOrAbove(): boolean {\r\n    return [ThreatSeverity.HIGH, ThreatSeverity.CRITICAL].includes(this.severity);\r\n  }\r\n\r\n  /**\r\n   * Check if threat has high confidence\r\n   */\r\n  hasHighConfidence(): boolean {\r\n    return this.confidence >= 80;\r\n  }\r\n\r\n  /**\r\n   * Check if threat has low confidence\r\n   */\r\n  hasLowConfidence(): boolean {\r\n    return this.confidence < 60;\r\n  }\r\n\r\n  /**\r\n   * Check if threat has high risk score\r\n   */\r\n  hasHighRiskScore(): boolean {\r\n    return this.riskScore >= 70;\r\n  }\r\n\r\n  /**\r\n   * Check if threat has critical risk score\r\n   */\r\n  hasCriticalRiskScore(): boolean {\r\n    return this.riskScore >= 90;\r\n  }\r\n\r\n  /**\r\n   * Check if threat affects critical assets\r\n   */\r\n  affectsCriticalAssets(): boolean {\r\n    return this.affectedAssets.some(asset => asset.criticality === 'critical');\r\n  }\r\n\r\n  /**\r\n   * Check if threat affects multiple assets\r\n   */\r\n  affectsMultipleAssets(): boolean {\r\n    return this.affectedAssets.length > 1;\r\n  }\r\n\r\n  /**\r\n   * Check if threat has multiple indicators\r\n   */\r\n  hasMultipleIndicators(): boolean {\r\n    return this.indicators.length > 1;\r\n  }\r\n\r\n  /**\r\n   * Check if threat has high-confidence indicators\r\n   */\r\n  hasHighConfidenceIndicators(): boolean {\r\n    return this.indicators.some(indicator => indicator.confidence >= 80);\r\n  }\r\n\r\n  /**\r\n   * Check if threat actor is known\r\n   */\r\n  hasKnownThreatActor(): boolean {\r\n    return this.threatActor !== undefined;\r\n  }\r\n\r\n  /**\r\n   * Check if threat actor is advanced\r\n   */\r\n  hasAdvancedThreatActor(): boolean {\r\n    return this.threatActor?.sophistication === 'advanced';\r\n  }\r\n\r\n  /**\r\n   * Check if threat is in late kill chain stage\r\n   */\r\n  isLateKillChainStage(): boolean {\r\n    return ['command_control', 'actions_objectives'].includes(this.killChainStage);\r\n  }\r\n\r\n  /**\r\n   * Check if threat has high business impact\r\n   */\r\n  hasHighBusinessImpact(): boolean {\r\n    return ['high', 'critical'].includes(this.potentialImpact.businessImpact);\r\n  }\r\n\r\n  /**\r\n   * Check if threat has critical business impact\r\n   */\r\n  hasCriticalBusinessImpact(): boolean {\r\n    return this.potentialImpact.businessImpact === 'critical';\r\n  }\r\n\r\n  /**\r\n   * Check if threat has geolocation data\r\n   */\r\n  hasGeolocationData(): boolean {\r\n    return this.threatIntelligence?.geolocation !== undefined;\r\n  }\r\n\r\n  /**\r\n   * Check if threat is part of known campaign\r\n   */\r\n  isPartOfKnownCampaign(): boolean {\r\n    return (this.threatIntelligence?.campaigns?.length || 0) > 0;\r\n  }\r\n\r\n  /**\r\n   * Check if threat requires immediate response\r\n   */\r\n  requiresImmediateResponse(): boolean {\r\n    return this.isCriticalSeverity() || \r\n           (this.isHighSeverityOrAbove() && this.hasHighConfidence()) ||\r\n           (this.hasCriticalRiskScore() && this.affectsCriticalAssets()) ||\r\n           (this.hasAdvancedThreatActor() && this.isLateKillChainStage());\r\n  }\r\n\r\n  /**\r\n   * Check if threat requires executive notification\r\n   */\r\n  requiresExecutiveNotification(): boolean {\r\n    return this.isCriticalSeverity() && \r\n           (this.hasHighConfidence() || this.affectsCriticalAssets() || this.hasCriticalBusinessImpact());\r\n  }\r\n\r\n  /**\r\n   * Check if threat requires SOC escalation\r\n   */\r\n  requiresSOCEscalation(): boolean {\r\n    return this.isHighSeverityOrAbove() || \r\n           this.hasHighRiskScore() ||\r\n           this.affectsCriticalAssets() ||\r\n           this.hasAdvancedThreatActor();\r\n  }\r\n\r\n  /**\r\n   * Get response priority\r\n   */\r\n  getResponsePriority(): 'low' | 'medium' | 'high' | 'critical' {\r\n    if (this.requiresImmediateResponse()) {\r\n      return 'critical';\r\n    }\r\n    if (this.isHighSeverityOrAbove() || this.hasHighRiskScore()) {\r\n      return 'high';\r\n    }\r\n    if (this.severity === ThreatSeverity.MEDIUM || this.affectsMultipleAssets()) {\r\n      return 'medium';\r\n    }\r\n    return 'low';\r\n  }\r\n\r\n  /**\r\n   * Get containment urgency in minutes\r\n   */\r\n  getContainmentUrgency(): number {\r\n    if (this.requiresImmediateResponse()) return 15;  // 15 minutes\r\n    if (this.isCriticalSeverity()) return 30;         // 30 minutes\r\n    if (this.isHighSeverityOrAbove()) return 120;     // 2 hours\r\n    if (this.severity === ThreatSeverity.MEDIUM) return 480; // 8 hours\r\n    return 1440;                                      // 24 hours\r\n  }\r\n\r\n  /**\r\n   * Get investigation timeline in hours\r\n   */\r\n  getInvestigationTimeline(): number {\r\n    if (this.requiresImmediateResponse()) return 2;   // 2 hours\r\n    if (this.isCriticalSeverity()) return 8;          // 8 hours\r\n    if (this.isHighSeverityOrAbove()) return 24;      // 24 hours\r\n    if (this.severity === ThreatSeverity.MEDIUM) return 72; // 3 days\r\n    return 168;                                       // 7 days\r\n  }\r\n\r\n  /**\r\n   * Get recommended containment actions\r\n   */\r\n  getRecommendedContainmentActions(): string[] {\r\n    const actions: string[] = [];\r\n    \r\n    if (this.requiresImmediateResponse()) {\r\n      actions.push('isolate_affected_systems', 'block_malicious_ips', 'disable_compromised_accounts');\r\n    }\r\n    \r\n    if (this.sourceIp) {\r\n      actions.push('block_source_ip', 'analyze_network_traffic');\r\n    }\r\n    \r\n    if (this.affectsCriticalAssets()) {\r\n      actions.push('emergency_asset_isolation', 'backup_critical_data');\r\n    }\r\n    \r\n    if (this.hasAdvancedThreatActor()) {\r\n      actions.push('advanced_forensics', 'threat_hunting', 'intelligence_gathering');\r\n    }\r\n    \r\n    if (this.isLateKillChainStage()) {\r\n      actions.push('damage_assessment', 'data_exfiltration_check', 'system_integrity_verification');\r\n    }\r\n    \r\n    return actions;\r\n  }\r\n\r\n  /**\r\n   * Get notification targets\r\n   */\r\n  getNotificationTargets(): string[] {\r\n    const targets: string[] = ['security_operations_center'];\r\n    \r\n    if (this.requiresExecutiveNotification()) {\r\n      targets.push('security_leadership', 'executive_team', 'board_of_directors');\r\n    }\r\n    \r\n    if (this.requiresSOCEscalation()) {\r\n      targets.push('incident_response_team', 'threat_analysts');\r\n    }\r\n    \r\n    if (this.affectsCriticalAssets()) {\r\n      targets.push('asset_owners', 'business_stakeholders');\r\n    }\r\n    \r\n    if (this.hasAdvancedThreatActor()) {\r\n      targets.push('threat_intelligence_team', 'external_partners');\r\n    }\r\n    \r\n    if (this.hasCriticalBusinessImpact()) {\r\n      targets.push('business_continuity_team', 'communications_team');\r\n    }\r\n    \r\n    return [...new Set(targets)]; // Remove duplicates\r\n  }\r\n\r\n  /**\r\n   * Get threat detection metrics\r\n   */\r\n  getThreatDetectionMetrics(): {\r\n    threatId: string;\r\n    name: string;\r\n    severity: ThreatSeverity;\r\n    category: string;\r\n    type: string;\r\n    confidence: number;\r\n    riskScore: number;\r\n    detectionMethod: string;\r\n    detectionSource: string;\r\n    affectedAssetsCount: number;\r\n    indicatorsCount: number;\r\n    killChainStage: string;\r\n    responsePriority: string;\r\n    containmentUrgency: number;\r\n    investigationTimeline: number;\r\n    requiresImmediateResponse: boolean;\r\n    affectsCriticalAssets: boolean;\r\n    hasAdvancedThreatActor: boolean;\r\n    processingDuration: number;\r\n  } {\r\n    return {\r\n      threatId: this.threatId,\r\n      name: this.threatName,\r\n      severity: this.severity,\r\n      category: this.category,\r\n      type: this.threatType,\r\n      confidence: this.confidence,\r\n      riskScore: this.riskScore,\r\n      detectionMethod: this.detectionMethod,\r\n      detectionSource: this.detectionSource,\r\n      affectedAssetsCount: this.affectedAssets.length,\r\n      indicatorsCount: this.indicators.length,\r\n      killChainStage: this.killChainStage,\r\n      responsePriority: this.getResponsePriority(),\r\n      containmentUrgency: this.getContainmentUrgency(),\r\n      investigationTimeline: this.getInvestigationTimeline(),\r\n      requiresImmediateResponse: this.requiresImmediateResponse(),\r\n      affectsCriticalAssets: this.affectsCriticalAssets(),\r\n      hasAdvancedThreatActor: this.hasAdvancedThreatActor(),\r\n      processingDuration: this.processingMetadata.processingDuration,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert to integration event for external systems\r\n   */\r\n  toIntegrationEvent(): {\r\n    eventType: string;\r\n    version: string;\r\n    timestamp: string;\r\n    data: {\r\n      threatId: string;\r\n      threat: {\r\n        name: string;\r\n        description: string;\r\n        severity: ThreatSeverity;\r\n        category: string;\r\n        type: string;\r\n        confidence: number;\r\n        riskScore: number;\r\n        detectedAt: string;\r\n        detectionMethod: string;\r\n        detectionSource: string;\r\n      };\r\n      assets: {\r\n        affected: Array<{\r\n          id: string;\r\n          name: string;\r\n          type: string;\r\n          criticality: string;\r\n        }>;\r\n        criticalAssetsAffected: boolean;\r\n      };\r\n      indicators: {\r\n        count: number;\r\n        types: string[];\r\n        highConfidenceCount: number;\r\n      };\r\n      attack: {\r\n        vectors: string[];\r\n        killChainStage: string;\r\n        threatActor?: {\r\n          name: string;\r\n          sophistication: string;\r\n        };\r\n      };\r\n      impact: {\r\n        confidentiality: string;\r\n        integrity: string;\r\n        availability: string;\r\n        businessImpact: string;\r\n      };\r\n      intelligence?: {\r\n        campaigns: string[];\r\n        ttps: string[];\r\n        mitreAttackIds: string[];\r\n        geolocation?: {\r\n          country: string;\r\n          region: string;\r\n        };\r\n      };\r\n      response: {\r\n        priority: string;\r\n        containmentUrgency: number;\r\n        investigationTimeline: number;\r\n        requiresImmediateResponse: boolean;\r\n        requiresExecutiveNotification: boolean;\r\n        recommendedActions: string[];\r\n        notificationTargets: string[];\r\n      };\r\n    };\r\n    metadata: {\r\n      correlationId?: string;\r\n      causationId?: string;\r\n      domain: string;\r\n      aggregateType: string;\r\n      processingStage: string;\r\n    };\r\n  } {\r\n    const indicatorTypes = [...new Set(this.indicators.map(i => i.type))];\r\n    const highConfidenceIndicators = this.indicators.filter(i => i.confidence >= 80).length;\r\n    \r\n    return {\r\n      eventType: 'ThreatDetected',\r\n      version: '1.0',\r\n      timestamp: this.occurredOn.toISOString(),\r\n      data: {\r\n        threatId: this.threatId,\r\n        threat: {\r\n          name: this.threatName,\r\n          description: this.description,\r\n          severity: this.severity,\r\n          category: this.category,\r\n          type: this.threatType,\r\n          confidence: this.confidence,\r\n          riskScore: this.riskScore,\r\n          detectedAt: this.detectedAt.toISOString(),\r\n          detectionMethod: this.detectionMethod,\r\n          detectionSource: this.detectionSource,\r\n        },\r\n        assets: {\r\n          affected: this.affectedAssets,\r\n          criticalAssetsAffected: this.affectsCriticalAssets(),\r\n        },\r\n        indicators: {\r\n          count: this.indicators.length,\r\n          types: indicatorTypes,\r\n          highConfidenceCount: highConfidenceIndicators,\r\n        },\r\n        attack: {\r\n          vectors: this.attackVectors,\r\n          killChainStage: this.killChainStage,\r\n          threatActor: this.threatActor ? {\r\n            name: this.threatActor.name,\r\n            sophistication: this.threatActor.sophistication,\r\n          } : undefined,\r\n        },\r\n        impact: {\r\n          confidentiality: this.potentialImpact.confidentiality,\r\n          integrity: this.potentialImpact.integrity,\r\n          availability: this.potentialImpact.availability,\r\n          businessImpact: this.potentialImpact.businessImpact,\r\n        },\r\n        intelligence: this.threatIntelligence ? {\r\n          campaigns: this.threatIntelligence.campaigns,\r\n          ttps: this.threatIntelligence.ttps,\r\n          mitreAttackIds: this.threatIntelligence.mitreAttackIds,\r\n          geolocation: this.threatIntelligence.geolocation ? {\r\n            country: this.threatIntelligence.geolocation.country,\r\n            region: this.threatIntelligence.geolocation.region,\r\n          } : undefined,\r\n        } : undefined,\r\n        response: {\r\n          priority: this.getResponsePriority(),\r\n          containmentUrgency: this.getContainmentUrgency(),\r\n          investigationTimeline: this.getInvestigationTimeline(),\r\n          requiresImmediateResponse: this.requiresImmediateResponse(),\r\n          requiresExecutiveNotification: this.requiresExecutiveNotification(),\r\n          recommendedActions: this.getRecommendedContainmentActions(),\r\n          notificationTargets: this.getNotificationTargets(),\r\n        },\r\n      },\r\n      metadata: {\r\n        correlationId: this.correlationId,\r\n        causationId: this.causationId,\r\n        domain: 'Security',\r\n        aggregateType: 'Threat',\r\n        processingStage: 'detection',\r\n      },\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      ...super.toJSON(),\r\n      eventData: this.eventData,\r\n      analysis: {\r\n        isCriticalSeverity: this.isCriticalSeverity(),\r\n        isHighSeverityOrAbove: this.isHighSeverityOrAbove(),\r\n        hasHighConfidence: this.hasHighConfidence(),\r\n        hasLowConfidence: this.hasLowConfidence(),\r\n        hasHighRiskScore: this.hasHighRiskScore(),\r\n        hasCriticalRiskScore: this.hasCriticalRiskScore(),\r\n        affectsCriticalAssets: this.affectsCriticalAssets(),\r\n        affectsMultipleAssets: this.affectsMultipleAssets(),\r\n        hasMultipleIndicators: this.hasMultipleIndicators(),\r\n        hasHighConfidenceIndicators: this.hasHighConfidenceIndicators(),\r\n        hasKnownThreatActor: this.hasKnownThreatActor(),\r\n        hasAdvancedThreatActor: this.hasAdvancedThreatActor(),\r\n        isLateKillChainStage: this.isLateKillChainStage(),\r\n        hasHighBusinessImpact: this.hasHighBusinessImpact(),\r\n        hasCriticalBusinessImpact: this.hasCriticalBusinessImpact(),\r\n        hasGeolocationData: this.hasGeolocationData(),\r\n        isPartOfKnownCampaign: this.isPartOfKnownCampaign(),\r\n        requiresImmediateResponse: this.requiresImmediateResponse(),\r\n        requiresExecutiveNotification: this.requiresExecutiveNotification(),\r\n        requiresSOCEscalation: this.requiresSOCEscalation(),\r\n        responsePriority: this.getResponsePriority(),\r\n        containmentUrgency: this.getContainmentUrgency(),\r\n        investigationTimeline: this.getInvestigationTimeline(),\r\n        recommendedContainmentActions: this.getRecommendedContainmentActions(),\r\n        notificationTargets: this.getNotificationTargets(),\r\n        threatDetectionMetrics: this.getThreatDetectionMetrics(),\r\n      },\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create from JSON representation\r\n   */\r\n  static fromJSON(json: Record<string, any>): ThreatDetectedDomainEvent {\r\n    return new ThreatDetectedDomainEvent(\r\n      UniqueEntityId.fromString(json.aggregateId),\r\n      json.eventData,\r\n      {\r\n        eventId: UniqueEntityId.fromString(json.eventId),\r\n        occurredOn: new Date(json.occurredOn),\r\n        eventVersion: json.eventVersion,\r\n        correlationId: json.correlationId,\r\n        causationId: json.causationId,\r\n        metadata: json.metadata,\r\n      }\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get human-readable description\r\n   */\r\n  getDescription(): string {\r\n    const severityText = this.severity.toLowerCase();\r\n    const confidenceText = this.hasHighConfidence() ? 'high confidence' : 'moderate confidence';\r\n    const assetText = this.affectsCriticalAssets() ? 'affecting critical assets' : \r\n                     this.affectsMultipleAssets() ? `affecting ${this.affectedAssets.length} assets` : \r\n                     'affecting single asset';\r\n    const actorText = this.hasKnownThreatActor() ? ` by ${this.threatActor!.name}` : '';\r\n    \r\n    return `${severityText} severity threat \"${this.threatName}\" detected with ${confidenceText}, ${assetText}${actorText}`;\r\n  }\r\n\r\n  /**\r\n   * Get event summary for logging\r\n   */\r\n  getSummary(): {\r\n    eventType: string;\r\n    threatId: string;\r\n    name: string;\r\n    severity: string;\r\n    category: string;\r\n    confidence: number;\r\n    riskScore: number;\r\n    detectionMethod: string;\r\n    affectedAssetsCount: number;\r\n    requiresImmediateResponse: boolean;\r\n    responsePriority: string;\r\n    killChainStage: string;\r\n    timestamp: string;\r\n  } {\r\n    return {\r\n      eventType: 'ThreatDetected',\r\n      threatId: this.threatId,\r\n      name: this.threatName,\r\n      severity: this.severity,\r\n      category: this.category,\r\n      confidence: this.confidence,\r\n      riskScore: this.riskScore,\r\n      detectionMethod: this.detectionMethod,\r\n      affectedAssetsCount: this.affectedAssets.length,\r\n      requiresImmediateResponse: this.requiresImmediateResponse(),\r\n      responsePriority: this.getResponsePriority(),\r\n      killChainStage: this.killChainStage,\r\n      timestamp: this.occurredOn.toISOString(),\r\n    };\r\n  }\r\n}"], "version": 3}