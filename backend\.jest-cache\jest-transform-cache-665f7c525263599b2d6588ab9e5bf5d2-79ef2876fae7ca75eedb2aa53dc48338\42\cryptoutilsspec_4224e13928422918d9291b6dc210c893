dd8c461a17a8815a291a4b8feb66a728
"use strict";
/**
 * Crypto Utils Tests
 */
Object.defineProperty(exports, "__esModule", { value: true });
const crypto_utils_1 = require("../../utils/crypto.utils");
const security_types_1 = require("../../types/security.types");
describe('CryptoUtils', () => {
    describe('generateKey', () => {
        it('should generate key of specified size', () => {
            const key = crypto_utils_1.CryptoUtils.generateKey(32);
            expect(key).toBeInstanceOf(Buffer);
            expect(key.length).toBe(32);
        });
        it('should generate different keys', () => {
            const key1 = crypto_utils_1.CryptoUtils.generateKey(16);
            const key2 = crypto_utils_1.CryptoUtils.generateKey(16);
            expect(key1.equals(key2)).toBe(false);
        });
    });
    describe('generateSalt', () => {
        it('should generate salt of specified size', () => {
            const salt = crypto_utils_1.CryptoUtils.generateSalt(16);
            expect(salt).toBeInstanceOf(Buffer);
            expect(salt.length).toBe(16);
        });
        it('should generate different salts', () => {
            const salt1 = crypto_utils_1.CryptoUtils.generateSalt();
            const salt2 = crypto_utils_1.CryptoUtils.generateSalt();
            expect(salt1.equals(salt2)).toBe(false);
        });
    });
    describe('generateIV', () => {
        it('should generate IV of specified size', () => {
            const iv = crypto_utils_1.CryptoUtils.generateIV(12);
            expect(iv).toBeInstanceOf(Buffer);
            expect(iv.length).toBe(12);
        });
        it('should generate different IVs', () => {
            const iv1 = crypto_utils_1.CryptoUtils.generateIV();
            const iv2 = crypto_utils_1.CryptoUtils.generateIV();
            expect(iv1.equals(iv2)).toBe(false);
        });
    });
    describe('deriveKeyPBKDF2', () => {
        it('should derive key from password', async () => {
            const password = 'test-password';
            const salt = crypto_utils_1.CryptoUtils.generateSalt();
            const key = await crypto_utils_1.CryptoUtils.deriveKeyPBKDF2(password, salt, 1000, 32);
            expect(key).toBeInstanceOf(Buffer);
            expect(key.length).toBe(32);
        });
        it('should derive same key for same inputs', async () => {
            const password = 'test-password';
            const salt = Buffer.from('fixed-salt-16-bytes', 'utf8');
            const key1 = await crypto_utils_1.CryptoUtils.deriveKeyPBKDF2(password, salt, 1000, 32);
            const key2 = await crypto_utils_1.CryptoUtils.deriveKeyPBKDF2(password, salt, 1000, 32);
            expect(key1.equals(key2)).toBe(true);
        });
        it('should derive different keys for different passwords', async () => {
            const salt = crypto_utils_1.CryptoUtils.generateSalt();
            const key1 = await crypto_utils_1.CryptoUtils.deriveKeyPBKDF2('password1', salt, 1000, 32);
            const key2 = await crypto_utils_1.CryptoUtils.deriveKeyPBKDF2('password2', salt, 1000, 32);
            expect(key1.equals(key2)).toBe(false);
        });
    });
    describe('AES-256-GCM encryption', () => {
        it('should encrypt and decrypt data', async () => {
            const data = Buffer.from('Hello, World!', 'utf8');
            const key = crypto_utils_1.CryptoUtils.generateKey(32);
            const encrypted = await crypto_utils_1.CryptoUtils.encryptAES256GCM(data, key);
            const decrypted = await crypto_utils_1.CryptoUtils.decryptAES256GCM(encrypted.encrypted, key, encrypted.iv, encrypted.tag);
            expect(decrypted.toString('utf8')).toBe('Hello, World!');
        });
        it('should use provided IV', async () => {
            const data = Buffer.from('test data', 'utf8');
            const key = crypto_utils_1.CryptoUtils.generateKey(32);
            const iv = crypto_utils_1.CryptoUtils.generateIV(12);
            const encrypted = await crypto_utils_1.CryptoUtils.encryptAES256GCM(data, key, iv);
            expect(encrypted.iv.equals(iv)).toBe(true);
        });
        it('should include authentication tag', async () => {
            const data = Buffer.from('test data', 'utf8');
            const key = crypto_utils_1.CryptoUtils.generateKey(32);
            const encrypted = await crypto_utils_1.CryptoUtils.encryptAES256GCM(data, key);
            expect(encrypted.tag).toBeDefined();
            expect(encrypted.tag.length).toBe(16);
        });
        it('should fail decryption with wrong key', async () => {
            const data = Buffer.from('test data', 'utf8');
            const key1 = crypto_utils_1.CryptoUtils.generateKey(32);
            const key2 = crypto_utils_1.CryptoUtils.generateKey(32);
            const encrypted = await crypto_utils_1.CryptoUtils.encryptAES256GCM(data, key1);
            await expect(crypto_utils_1.CryptoUtils.decryptAES256GCM(encrypted.encrypted, key2, encrypted.iv, encrypted.tag)).rejects.toThrow();
        });
    });
    describe('AES-256-CBC encryption', () => {
        it('should encrypt and decrypt data', async () => {
            const data = Buffer.from('Hello, World!', 'utf8');
            const key = crypto_utils_1.CryptoUtils.generateKey(32);
            const encrypted = await crypto_utils_1.CryptoUtils.encryptAES256CBC(data, key);
            const decrypted = await crypto_utils_1.CryptoUtils.decryptAES256CBC(encrypted.encrypted, key, encrypted.iv);
            expect(decrypted.toString('utf8')).toBe('Hello, World!');
        });
        it('should use 16-byte IV', async () => {
            const data = Buffer.from('test data', 'utf8');
            const key = crypto_utils_1.CryptoUtils.generateKey(32);
            const encrypted = await crypto_utils_1.CryptoUtils.encryptAES256CBC(data, key);
            expect(encrypted.iv.length).toBe(16);
        });
    });
    describe('SHA hashing', () => {
        it('should hash data with SHA-256', async () => {
            const data = Buffer.from('test data', 'utf8');
            const hash = await crypto_utils_1.CryptoUtils.hashSHA256(data);
            expect(hash).toBeInstanceOf(Buffer);
            expect(hash.length).toBe(32); // SHA-256 produces 32-byte hash
        });
        it('should hash data with SHA-512', async () => {
            const data = Buffer.from('test data', 'utf8');
            const hash = await crypto_utils_1.CryptoUtils.hashSHA512(data);
            expect(hash).toBeInstanceOf(Buffer);
            expect(hash.length).toBe(64); // SHA-512 produces 64-byte hash
        });
        it('should produce same hash for same data', async () => {
            const data = Buffer.from('test data', 'utf8');
            const hash1 = await crypto_utils_1.CryptoUtils.hashSHA256(data);
            const hash2 = await crypto_utils_1.CryptoUtils.hashSHA256(data);
            expect(hash1.equals(hash2)).toBe(true);
        });
    });
    describe('HMAC', () => {
        it('should create and verify HMAC', async () => {
            const data = Buffer.from('test data', 'utf8');
            const key = crypto_utils_1.CryptoUtils.generateKey(32);
            const hmac = await crypto_utils_1.CryptoUtils.createHMAC(data, key);
            const isValid = await crypto_utils_1.CryptoUtils.verifyHMAC(data, hmac, key);
            expect(isValid).toBe(true);
        });
        it('should fail verification with wrong key', async () => {
            const data = Buffer.from('test data', 'utf8');
            const key1 = crypto_utils_1.CryptoUtils.generateKey(32);
            const key2 = crypto_utils_1.CryptoUtils.generateKey(32);
            const hmac = await crypto_utils_1.CryptoUtils.createHMAC(data, key1);
            const isValid = await crypto_utils_1.CryptoUtils.verifyHMAC(data, hmac, key2);
            expect(isValid).toBe(false);
        });
        it('should fail verification with modified data', async () => {
            const data1 = Buffer.from('test data', 'utf8');
            const data2 = Buffer.from('modified data', 'utf8');
            const key = crypto_utils_1.CryptoUtils.generateKey(32);
            const hmac = await crypto_utils_1.CryptoUtils.createHMAC(data1, key);
            const isValid = await crypto_utils_1.CryptoUtils.verifyHMAC(data2, hmac, key);
            expect(isValid).toBe(false);
        });
    });
    describe('RSA encryption', () => {
        it('should generate RSA key pair', async () => {
            const keyPair = await crypto_utils_1.CryptoUtils.generateRSAKeyPair(2048);
            expect(keyPair.publicKey).toBeInstanceOf(Buffer);
            expect(keyPair.privateKey).toBeInstanceOf(Buffer);
            expect(keyPair.algorithm).toBe('RSA-OAEP');
            expect(keyPair.keySize).toBe(2048);
        });
        it('should encrypt and decrypt with RSA', async () => {
            const data = Buffer.from('Hello, RSA!', 'utf8');
            const keyPair = await crypto_utils_1.CryptoUtils.generateRSAKeyPair(2048);
            const encrypted = await crypto_utils_1.CryptoUtils.encryptRSA(data, keyPair.publicKey);
            const decrypted = await crypto_utils_1.CryptoUtils.decryptRSA(encrypted, keyPair.privateKey);
            expect(decrypted.toString('utf8')).toBe('Hello, RSA!');
        });
    });
    describe('ECDSA signing', () => {
        it('should generate ECDSA key pair', async () => {
            const keyPair = await crypto_utils_1.CryptoUtils.generateECDSAKeyPair('P-256');
            expect(keyPair.publicKey).toBeInstanceOf(Buffer);
            expect(keyPair.privateKey).toBeInstanceOf(Buffer);
            expect(keyPair.algorithm).toBe('ECDSA-P-256');
            expect(keyPair.keySize).toBe(256);
        });
        it('should sign and verify with ECDSA', async () => {
            const data = Buffer.from('test message', 'utf8');
            const keyPair = await crypto_utils_1.CryptoUtils.generateECDSAKeyPair('P-256');
            const signature = await crypto_utils_1.CryptoUtils.signECDSA(data, keyPair.privateKey, 'P-256');
            const isValid = await crypto_utils_1.CryptoUtils.verifyECDSA(data, signature, keyPair.publicKey, 'P-256');
            expect(isValid).toBe(true);
        });
        it('should fail verification with wrong public key', async () => {
            const data = Buffer.from('test message', 'utf8');
            const keyPair1 = await crypto_utils_1.CryptoUtils.generateECDSAKeyPair('P-256');
            const keyPair2 = await crypto_utils_1.CryptoUtils.generateECDSAKeyPair('P-256');
            const signature = await crypto_utils_1.CryptoUtils.signECDSA(data, keyPair1.privateKey, 'P-256');
            const isValid = await crypto_utils_1.CryptoUtils.verifyECDSA(data, signature, keyPair2.publicKey, 'P-256');
            expect(isValid).toBe(false);
        });
    });
    describe('random generation', () => {
        it('should generate random bytes', () => {
            const bytes = crypto_utils_1.CryptoUtils.generateRandomBytes(16);
            expect(bytes).toBeInstanceOf(Buffer);
            expect(bytes.length).toBe(16);
        });
        it('should generate random string', () => {
            const str = crypto_utils_1.CryptoUtils.generateRandomString(20);
            expect(typeof str).toBe('string');
            expect(str.length).toBe(20);
        });
        it('should generate random string with custom charset', () => {
            const str = crypto_utils_1.CryptoUtils.generateRandomString(10, '0123456789');
            expect(str.length).toBe(10);
            expect(/^[0-9]+$/.test(str)).toBe(true);
        });
        it('should generate UUID', () => {
            const uuid = crypto_utils_1.CryptoUtils.generateUUID();
            expect(typeof uuid).toBe('string');
            expect(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(uuid)).toBe(true);
        });
    });
    describe('constant time comparison', () => {
        it('should compare strings in constant time', () => {
            expect(crypto_utils_1.CryptoUtils.constantTimeCompare('hello', 'hello')).toBe(true);
            expect(crypto_utils_1.CryptoUtils.constantTimeCompare('hello', 'world')).toBe(false);
            expect(crypto_utils_1.CryptoUtils.constantTimeCompare('hello', 'hell')).toBe(false);
        });
        it('should compare buffers in constant time', () => {
            const buf1 = Buffer.from('hello', 'utf8');
            const buf2 = Buffer.from('hello', 'utf8');
            const buf3 = Buffer.from('world', 'utf8');
            expect(crypto_utils_1.CryptoUtils.constantTimeCompareBuffers(buf1, buf2)).toBe(true);
            expect(crypto_utils_1.CryptoUtils.constantTimeCompareBuffers(buf1, buf3)).toBe(false);
        });
    });
    describe('encoding', () => {
        it('should encode and decode Base64', () => {
            const data = Buffer.from('Hello, World!', 'utf8');
            const encoded = crypto_utils_1.CryptoUtils.encodeBase64(data);
            const decoded = crypto_utils_1.CryptoUtils.decodeBase64(encoded);
            expect(decoded.toString('utf8')).toBe('Hello, World!');
        });
        it('should encode and decode Base64URL', () => {
            const data = Buffer.from('Hello, World!', 'utf8');
            const encoded = crypto_utils_1.CryptoUtils.encodeBase64URL(data);
            const decoded = crypto_utils_1.CryptoUtils.decodeBase64URL(encoded);
            expect(decoded.toString('utf8')).toBe('Hello, World!');
            expect(encoded).not.toContain('+');
            expect(encoded).not.toContain('/');
            expect(encoded).not.toContain('=');
        });
        it('should encode and decode hex', () => {
            const data = Buffer.from('Hello, World!', 'utf8');
            const encoded = crypto_utils_1.CryptoUtils.encodeHex(data);
            const decoded = crypto_utils_1.CryptoUtils.decodeHex(encoded);
            expect(decoded.toString('utf8')).toBe('Hello, World!');
            expect(/^[0-9a-f]+$/.test(encoded)).toBe(true);
        });
    });
    describe('checksum', () => {
        it('should calculate CRC32 checksum', () => {
            const data = Buffer.from('Hello, World!', 'utf8');
            const checksum = crypto_utils_1.CryptoUtils.calculateChecksum(data);
            expect(typeof checksum).toBe('number');
            expect(checksum).toBeGreaterThan(0);
        });
        it('should produce same checksum for same data', () => {
            const data = Buffer.from('test data', 'utf8');
            const checksum1 = crypto_utils_1.CryptoUtils.calculateChecksum(data);
            const checksum2 = crypto_utils_1.CryptoUtils.calculateChecksum(data);
            expect(checksum1).toBe(checksum2);
        });
    });
    describe('password encryption', () => {
        it('should encrypt and decrypt with password', async () => {
            const data = Buffer.from('sensitive data', 'utf8');
            const password = 'strong-password';
            const encrypted = await crypto_utils_1.CryptoUtils.encryptWithPassword(data, password);
            const decrypted = await crypto_utils_1.CryptoUtils.decryptWithPassword(encrypted, password);
            expect(decrypted.toString('utf8')).toBe('sensitive data');
        });
        it('should fail decryption with wrong password', async () => {
            const data = Buffer.from('sensitive data', 'utf8');
            const password1 = 'password1';
            const password2 = 'password2';
            const encrypted = await crypto_utils_1.CryptoUtils.encryptWithPassword(data, password1);
            await expect(crypto_utils_1.CryptoUtils.decryptWithPassword(encrypted, password2)).rejects.toThrow();
        });
        it('should include salt in result', async () => {
            const data = Buffer.from('test data', 'utf8');
            const password = 'password';
            const encrypted = await crypto_utils_1.CryptoUtils.encryptWithPassword(data, password);
            expect(encrypted.salt).toBeDefined();
            expect(encrypted.salt.length).toBe(16);
        });
    });
    describe('password hashing', () => {
        it('should hash and verify password', async () => {
            const password = 'test-password';
            const hashResult = await crypto_utils_1.CryptoUtils.hashPassword(password);
            const isValid = await crypto_utils_1.CryptoUtils.verifyPassword(password, hashResult);
            expect(isValid).toBe(true);
        });
        it('should fail verification with wrong password', async () => {
            const password1 = 'password1';
            const password2 = 'password2';
            const hashResult = await crypto_utils_1.CryptoUtils.hashPassword(password1);
            const isValid = await crypto_utils_1.CryptoUtils.verifyPassword(password2, hashResult);
            expect(isValid).toBe(false);
        });
        it('should use provided salt', async () => {
            const password = 'test-password';
            const salt = crypto_utils_1.CryptoUtils.generateSalt();
            const hashResult = await crypto_utils_1.CryptoUtils.hashPassword(password, salt);
            expect(hashResult.salt.equals(salt)).toBe(true);
        });
    });
    describe('serialization', () => {
        it('should serialize and deserialize encryption result', () => {
            const encryptionResult = {
                encrypted: Buffer.from('encrypted-data', 'utf8'),
                iv: Buffer.from('initialization-vector', 'utf8'),
                tag: Buffer.from('auth-tag', 'utf8'),
                salt: Buffer.from('salt-value', 'utf8'),
                algorithm: security_types_1.EncryptionAlgorithm.AES_256_GCM,
            };
            const serialized = crypto_utils_1.CryptoUtils.serializeEncryptionResult(encryptionResult);
            const deserialized = crypto_utils_1.CryptoUtils.deserializeEncryptionResult(serialized);
            expect(deserialized.encrypted.equals(encryptionResult.encrypted)).toBe(true);
            expect(deserialized.iv.equals(encryptionResult.iv)).toBe(true);
            expect(deserialized.tag.equals(encryptionResult.tag)).toBe(true);
            expect(deserialized.salt.equals(encryptionResult.salt)).toBe(true);
            expect(deserialized.algorithm).toBe(encryptionResult.algorithm);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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