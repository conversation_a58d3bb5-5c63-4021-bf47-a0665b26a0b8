{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\domain\\entities\\threat-intelligence.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCAUiB;AACjB,qDAA+G;AAE/G,qFAAyE;AACzE,+DAAoD;AACpD,qEAA0D;AAE1D;;GAEG;AACH,IAAY,cAMX;AAND,WAAY,cAAc;IACxB,uCAAqB,CAAA;IACrB,+BAAa,CAAA;IACb,mCAAiB,CAAA;IACjB,6BAAW,CAAA;IACX,+BAAa,CAAA;AACf,CAAC,EANW,cAAc,8BAAd,cAAc,QAMzB;AAED;;GAEG;AACH,IAAY,gBAKX;AALD,WAAY,gBAAgB;IAC1B,iCAAa,CAAA;IACb,qCAAiB,CAAA;IACjB,+BAAW,CAAA;IACX,uCAAmB,CAAA;AACrB,CAAC,EALW,gBAAgB,gCAAhB,gBAAgB,QAK3B;AAED;;GAEG;AACH,IAAY,UAkBX;AAlBD,WAAY,UAAU;IACpB,iCAAmB,CAAA;IACnB,mCAAqB,CAAA;IACrB,+BAAiB,CAAA;IACjB,yBAAW,CAAA;IACX,uCAAyB,CAAA;IACzB,+BAAiB,CAAA;IACjB,mCAAqB,CAAA;IACrB,iCAAmB,CAAA;IACnB,iCAAmB,CAAA;IACnB,+BAAiB,CAAA;IACjB,6CAA+B,CAAA;IAC/B,iCAAmB,CAAA;IACnB,yDAA2C,CAAA;IAC3C,+CAAiC,CAAA;IACjC,2BAAa,CAAA;IACb,+CAAiC,CAAA;IACjC,6BAAe,CAAA;AACjB,CAAC,EAlBW,UAAU,0BAAV,UAAU,QAkBrB;AAED;;GAEG;AACH,IAAY,YAMX;AAND,WAAY,YAAY;IACtB,iCAAiB,CAAA;IACjB,qCAAqB,CAAA;IACrB,mCAAmB,CAAA;IACnB,mCAAmB,CAAA;IACnB,6CAA6B,CAAA;AAC/B,CAAC,EANW,YAAY,4BAAZ,YAAY,QAMvB;AAiCD;;;GAGG;AAMI,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IAkK7B;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,CAAC,MAAM;YACnC,CAAC,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACrF,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,cAAc,EAAE,IAAI,CAAC,UAAU,EAAE,MAAM,IAAI,CAAC;YAC5C,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI;YACnC,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI;YACzC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI;YAChC,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAC;QAC3B,IAAI,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,2BAA2B;QAC3B,MAAM,cAAc,GAAG;YACrB,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,GAAG;YAC9B,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,GAAG;YAC1B,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,GAAG;YAC5B,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,GAAG;YACzB,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,GAAG;SAC3B,CAAC;QACF,KAAK,IAAI,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEvC,sBAAsB;QACtB,MAAM,mBAAmB,GAAG;YAC1B,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,GAAG;YAC5B,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,GAAG;YAC9B,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,GAAG;YAC3B,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE,GAAG;SAChC,CAAC;QACF,KAAK,IAAI,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE9C,8BAA8B;QAC9B,IAAI,IAAI,CAAC,gBAAgB,GAAG,EAAE,EAAE,CAAC;YAC/B,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;aAAM,IAAI,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;YACrC,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;QAED,iBAAiB;QACjB,MAAM,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YAC5E,IAAI,CAAC,SAAS,CAAC;QAEjB,IAAI,iBAAiB,IAAI,CAAC,EAAE,CAAC;YAC3B,KAAK,IAAI,GAAG,CAAC,CAAC,cAAc;QAC9B,CAAC;aAAM,IAAI,iBAAiB,IAAI,EAAE,EAAE,CAAC;YACnC,KAAK,IAAI,GAAG,CAAC,CAAC,SAAS;QACzB,CAAC;QAED,qBAAqB;QACrB,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YAC1C,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;QAED,cAAc;QACd,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACvC,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,QAMP;QACC,IAAI,QAAQ,CAAC,WAAW,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YAC5E,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,QAAQ,CAAC,UAAU,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACxE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,QAAQ,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YAC/B,MAAM,cAAc,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;YAC1E,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpB,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,IAAI,QAAQ,CAAC,OAAO,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAC7C,MAAM,iBAAiB,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CACvD,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,CACtC,CAAC;YACF,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACvB,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,IAAI,QAAQ,CAAC,SAAS,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACjD,MAAM,mBAAmB,GAAG,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAC7D,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAC1C,CAAC;YACF,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACzB,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI;YACnC,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI;YACzC,cAAc,EAAE,IAAI,CAAC,UAAU,EAAE,MAAM,IAAI,CAAC;YAC5C,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,UAAU;QACR,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC,QAAQ,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC;QACnC,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAe;QACpB,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC;QACnC,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,gBAAgB,GAAG;gBACtB,GAAG,IAAI,CAAC,gBAAgB;gBACxB,gBAAgB,EAAE,MAAM;gBACxB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AAjYY,gDAAkB;AAE7B;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;8CACpB;AAIX;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;IACxC,IAAA,0BAAQ,GAAE;;iDACG;AAId;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IACxB,IAAA,0BAAQ,GAAE;;uDACS;AAOpB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,UAAU;KACjB,CAAC;IACD,IAAA,wBAAM,EAAC,UAAU,CAAC;;sDACI;AAQvB;IANC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,cAAc;QACpB,OAAO,EAAE,cAAc,CAAC,MAAM;KAC/B,CAAC;IACD,IAAA,wBAAM,EAAC,cAAc,CAAC;;oDACE;AAQzB;IANC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,gBAAgB;QACtB,OAAO,EAAE,gBAAgB,CAAC,MAAM;KACjC,CAAC;IACD,IAAA,wBAAM,EAAC,gBAAgB,CAAC;;sDACI;AAQ7B;IANC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,YAAY;QAClB,OAAO,EAAE,YAAY,CAAC,MAAM;KAC7B,CAAC;IACD,IAAA,wBAAM,EAAC,YAAY,CAAC;;kDACA;AAIrB;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC;IAC5C,IAAA,wBAAM,GAAE;kDACE,IAAI,oBAAJ,IAAI;qDAAC;AAKhB;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC5D,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,GAAE;kDACE,IAAI,oBAAJ,IAAI;oDAAC;AAKhB;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC5D,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,GAAE;kDACG,IAAI,oBAAJ,IAAI;qDAAC;AAKjB;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACrD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;;gDACM;AAIhB;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;IACzB,IAAA,0BAAQ,GAAE;;sDACkB;AAK7B;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACzC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;uDACsB;AAKjC;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACzC,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;;qDACmB;AAK7B;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACrD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;;2DACiB;AAK3B;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACrD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;;6DACmB;AAK7B;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACrD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;;6DACmB;AAK7B;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACzC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;4DACY;AAKvB;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACzC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;8DACc;AAKzB;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACrD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;;sDACY;AAKtB;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACzC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;oDACI;AAKf;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACnE,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;qDACQ;AAInB;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IACvC,IAAA,0BAAQ,GAAE;;4DACc;AAKzB;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC5D,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,GAAE;kDACM,IAAI,oBAAJ,IAAI;wDAAC;AAIpB;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;IAC3C,IAAA,2BAAS,GAAE;;iDACG;AAIf;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;IAC3C,IAAA,2BAAS,GAAE;;wDACU;AAKtB;IAHC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACzC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;kDACQ,MAAM,oBAAN,MAAM;4DAAc;AAIvC;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,sDAAqB,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,kBAAkB,CAAC;;sDACpC;AAIpC;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,iCAAW,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,kBAAkB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACrF,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,CAAC;kDAC1B,iCAAW,oBAAX,iCAAW;uDAAC;AAG1B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;yDAClB;AAIvB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,uCAAc,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,kBAAkB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC9F,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,CAAC;kDAC1B,uCAAc,oBAAd,uCAAc;0DAAC;AAGhC;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4DACf;AAG1B;IADC,IAAA,0BAAgB,GAAE;kDACR,IAAI,oBAAJ,IAAI;qDAAC;AAGhB;IADC,IAAA,0BAAgB,GAAE;kDACR,IAAI,oBAAJ,IAAI;qDAAC;6BAhKL,kBAAkB;IAL9B,IAAA,gBAAM,EAAC,qBAAqB,CAAC;IAC7B,IAAA,eAAK,EAAC,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;IACjC,IAAA,eAAK,EAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;IAC9B,IAAA,eAAK,EAAC,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;IACjC,IAAA,eAAK,EAAC,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,EAAE,kBAAkB,EAAE,CAAC;GAClC,kBAAkB,CAiY9B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\domain\\entities\\threat-intelligence.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  OneToMany,\r\n  ManyToOne,\r\n  JoinColumn,\r\n} from 'typeorm';\r\nimport { IsEnum, IsOptional, IsString, IsNumber, IsBoolean, IsArray, IsObject, IsDate } from 'class-validator';\r\n\r\nimport { IndicatorOfCompromise } from './indicator-of-compromise.entity';\r\nimport { ThreatActor } from './threat-actor.entity';\r\nimport { ThreatCampaign } from './threat-campaign.entity';\r\n\r\n/**\r\n * Threat intelligence severity levels\r\n */\r\nexport enum ThreatSeverity {\r\n  CRITICAL = 'critical',\r\n  HIGH = 'high',\r\n  MEDIUM = 'medium',\r\n  LOW = 'low',\r\n  INFO = 'info',\r\n}\r\n\r\n/**\r\n * Threat intelligence confidence levels\r\n */\r\nexport enum ThreatConfidence {\r\n  HIGH = 'high',\r\n  MEDIUM = 'medium',\r\n  LOW = 'low',\r\n  UNKNOWN = 'unknown',\r\n}\r\n\r\n/**\r\n * Threat intelligence types\r\n */\r\nexport enum ThreatType {\r\n  MALWARE = 'malware',\r\n  PHISHING = 'phishing',\r\n  BOTNET = 'botnet',\r\n  APT = 'apt',\r\n  RANSOMWARE = 'ransomware',\r\n  TROJAN = 'trojan',\r\n  BACKDOOR = 'backdoor',\r\n  ROOTKIT = 'rootkit',\r\n  SPYWARE = 'spyware',\r\n  ADWARE = 'adware',\r\n  VULNERABILITY = 'vulnerability',\r\n  EXPLOIT = 'exploit',\r\n  SUSPICIOUS_ACTIVITY = 'suspicious_activity',\r\n  ATTACK_PATTERN = 'attack_pattern',\r\n  TOOL = 'tool',\r\n  INFRASTRUCTURE = 'infrastructure',\r\n  OTHER = 'other',\r\n}\r\n\r\n/**\r\n * Threat intelligence status\r\n */\r\nexport enum ThreatStatus {\r\n  ACTIVE = 'active',\r\n  INACTIVE = 'inactive',\r\n  EXPIRED = 'expired',\r\n  REVOKED = 'revoked',\r\n  UNDER_REVIEW = 'under_review',\r\n}\r\n\r\n/**\r\n * Data source information\r\n */\r\nexport interface ThreatDataSource {\r\n  name: string;\r\n  type: 'commercial' | 'open_source' | 'government' | 'internal' | 'community';\r\n  reliability: 'A' | 'B' | 'C' | 'D' | 'E' | 'F'; // Admiralty Code\r\n  url?: string;\r\n  lastUpdated: Date;\r\n  confidence: ThreatConfidence;\r\n}\r\n\r\n/**\r\n * MITRE ATT&CK framework mapping\r\n */\r\nexport interface MitreAttackMapping {\r\n  tactics: string[];\r\n  techniques: string[];\r\n  subTechniques?: string[];\r\n  mitigations?: string[];\r\n}\r\n\r\n/**\r\n * Kill chain phase mapping\r\n */\r\nexport interface KillChainPhase {\r\n  killChainName: string;\r\n  phaseName: string;\r\n  phaseOrder?: number;\r\n}\r\n\r\n/**\r\n * Threat intelligence entity\r\n * Represents comprehensive threat intelligence information including IOCs, attribution, and analysis\r\n */\r\n@Entity('threat_intelligence')\r\n@Index(['threatType', 'severity'])\r\n@Index(['status', 'firstSeen'])\r\n@Index(['confidence', 'severity'])\r\n@Index(['tags'], { where: 'tags IS NOT NULL' })\r\nexport class ThreatIntelligence {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  @Column({ type: 'varchar', length: 255 })\r\n  @IsString()\r\n  title: string;\r\n\r\n  @Column({ type: 'text' })\r\n  @IsString()\r\n  description: string;\r\n\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ThreatType,\r\n  })\r\n  @IsEnum(ThreatType)\r\n  threatType: ThreatType;\r\n\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ThreatSeverity,\r\n    default: ThreatSeverity.MEDIUM,\r\n  })\r\n  @IsEnum(ThreatSeverity)\r\n  severity: ThreatSeverity;\r\n\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ThreatConfidence,\r\n    default: ThreatConfidence.MEDIUM,\r\n  })\r\n  @IsEnum(ThreatConfidence)\r\n  confidence: ThreatConfidence;\r\n\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ThreatStatus,\r\n    default: ThreatStatus.ACTIVE,\r\n  })\r\n  @IsEnum(ThreatStatus)\r\n  status: ThreatStatus;\r\n\r\n  @Column({ type: 'timestamp with time zone' })\r\n  @IsDate()\r\n  firstSeen: Date;\r\n\r\n  @Column({ type: 'timestamp with time zone', nullable: true })\r\n  @IsOptional()\r\n  @IsDate()\r\n  lastSeen?: Date;\r\n\r\n  @Column({ type: 'timestamp with time zone', nullable: true })\r\n  @IsOptional()\r\n  @IsDate()\r\n  expiresAt?: Date;\r\n\r\n  @Column({ type: 'text', array: true, nullable: true })\r\n  @IsOptional()\r\n  @IsArray()\r\n  tags?: string[];\r\n\r\n  @Column({ type: 'jsonb' })\r\n  @IsObject()\r\n  dataSource: ThreatDataSource;\r\n\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  @IsOptional()\r\n  @IsObject()\r\n  mitreAttack?: MitreAttackMapping;\r\n\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  @IsOptional()\r\n  @IsArray()\r\n  killChain?: KillChainPhase[];\r\n\r\n  @Column({ type: 'text', array: true, nullable: true })\r\n  @IsOptional()\r\n  @IsArray()\r\n  targetedSectors?: string[];\r\n\r\n  @Column({ type: 'text', array: true, nullable: true })\r\n  @IsOptional()\r\n  @IsArray()\r\n  targetedCountries?: string[];\r\n\r\n  @Column({ type: 'text', array: true, nullable: true })\r\n  @IsOptional()\r\n  @IsArray()\r\n  affectedPlatforms?: string[];\r\n\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  @IsOptional()\r\n  @IsObject()\r\n  technicalDetails?: any;\r\n\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  @IsOptional()\r\n  @IsObject()\r\n  behavioralAnalysis?: any;\r\n\r\n  @Column({ type: 'text', array: true, nullable: true })\r\n  @IsOptional()\r\n  @IsArray()\r\n  references?: string[];\r\n\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  @IsOptional()\r\n  @IsObject()\r\n  stixData?: any;\r\n\r\n  @Column({ type: 'decimal', precision: 3, scale: 1, nullable: true })\r\n  @IsOptional()\r\n  @IsNumber()\r\n  riskScore?: number;\r\n\r\n  @Column({ type: 'integer', default: 0 })\r\n  @IsNumber()\r\n  observationCount: number;\r\n\r\n  @Column({ type: 'timestamp with time zone', nullable: true })\r\n  @IsOptional()\r\n  @IsDate()\r\n  lastObserved?: Date;\r\n\r\n  @Column({ type: 'boolean', default: false })\r\n  @IsBoolean()\r\n  isIoc: boolean;\r\n\r\n  @Column({ type: 'boolean', default: false })\r\n  @IsBoolean()\r\n  isAttributed: boolean;\r\n\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  @IsOptional()\r\n  @IsObject()\r\n  customAttributes?: Record<string, any>;\r\n\r\n  // Relationships\r\n  @OneToMany(() => IndicatorOfCompromise, (ioc) => ioc.threatIntelligence)\r\n  indicators: IndicatorOfCompromise[];\r\n\r\n  @ManyToOne(() => ThreatActor, (actor) => actor.threatIntelligence, { nullable: true })\r\n  @JoinColumn({ name: 'threat_actor_id' })\r\n  threatActor?: ThreatActor;\r\n\r\n  @Column({ type: 'uuid', nullable: true })\r\n  threatActorId?: string;\r\n\r\n  @ManyToOne(() => ThreatCampaign, (campaign) => campaign.threatIntelligence, { nullable: true })\r\n  @JoinColumn({ name: 'threat_campaign_id' })\r\n  threatCampaign?: ThreatCampaign;\r\n\r\n  @Column({ type: 'uuid', nullable: true })\r\n  threatCampaignId?: string;\r\n\r\n  @CreateDateColumn()\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn()\r\n  updatedAt: Date;\r\n\r\n  /**\r\n   * Check if threat intelligence is currently active\r\n   */\r\n  get isActive(): boolean {\r\n    return this.status === ThreatStatus.ACTIVE && \r\n           (!this.expiresAt || this.expiresAt > new Date());\r\n  }\r\n\r\n  /**\r\n   * Check if threat intelligence is expired\r\n   */\r\n  get isExpired(): boolean {\r\n    return this.expiresAt ? this.expiresAt <= new Date() : false;\r\n  }\r\n\r\n  /**\r\n   * Get age in days\r\n   */\r\n  get ageInDays(): number {\r\n    return Math.floor((Date.now() - this.firstSeen.getTime()) / (1000 * 60 * 60 * 24));\r\n  }\r\n\r\n  /**\r\n   * Get threat intelligence summary\r\n   */\r\n  getSummary(): any {\r\n    return {\r\n      id: this.id,\r\n      title: this.title,\r\n      threatType: this.threatType,\r\n      severity: this.severity,\r\n      confidence: this.confidence,\r\n      status: this.status,\r\n      isActive: this.isActive,\r\n      isExpired: this.isExpired,\r\n      ageInDays: this.ageInDays,\r\n      observationCount: this.observationCount,\r\n      indicatorCount: this.indicators?.length || 0,\r\n      threatActor: this.threatActor?.name,\r\n      threatCampaign: this.threatCampaign?.name,\r\n      dataSource: this.dataSource.name,\r\n      tags: this.tags,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Update observation count and last observed timestamp\r\n   */\r\n  recordObservation(): void {\r\n    this.observationCount += 1;\r\n    this.lastObserved = new Date();\r\n  }\r\n\r\n  /**\r\n   * Calculate risk score based on multiple factors\r\n   */\r\n  calculateRiskScore(): number {\r\n    let score = 0;\r\n\r\n    // Base score from severity\r\n    const severityScores = {\r\n      [ThreatSeverity.CRITICAL]: 9.0,\r\n      [ThreatSeverity.HIGH]: 7.0,\r\n      [ThreatSeverity.MEDIUM]: 5.0,\r\n      [ThreatSeverity.LOW]: 3.0,\r\n      [ThreatSeverity.INFO]: 1.0,\r\n    };\r\n    score += severityScores[this.severity];\r\n\r\n    // Confidence modifier\r\n    const confidenceModifiers = {\r\n      [ThreatConfidence.HIGH]: 1.0,\r\n      [ThreatConfidence.MEDIUM]: 0.8,\r\n      [ThreatConfidence.LOW]: 0.6,\r\n      [ThreatConfidence.UNKNOWN]: 0.4,\r\n    };\r\n    score *= confidenceModifiers[this.confidence];\r\n\r\n    // Observation frequency boost\r\n    if (this.observationCount > 10) {\r\n      score += 1.0;\r\n    } else if (this.observationCount > 5) {\r\n      score += 0.5;\r\n    }\r\n\r\n    // Recency factor\r\n    const daysSinceLastSeen = this.lastSeen ? \r\n      Math.floor((Date.now() - this.lastSeen.getTime()) / (1000 * 60 * 60 * 24)) : \r\n      this.ageInDays;\r\n    \r\n    if (daysSinceLastSeen <= 7) {\r\n      score += 1.0; // Very recent\r\n    } else if (daysSinceLastSeen <= 30) {\r\n      score += 0.5; // Recent\r\n    }\r\n\r\n    // Attribution factor\r\n    if (this.isAttributed && this.threatActor) {\r\n      score += 0.5;\r\n    }\r\n\r\n    // Cap at 10.0\r\n    this.riskScore = Math.min(score, 10.0);\r\n    return this.riskScore;\r\n  }\r\n\r\n  /**\r\n   * Check if threat intelligence matches given criteria\r\n   */\r\n  matches(criteria: {\r\n    threatTypes?: ThreatType[];\r\n    severities?: ThreatSeverity[];\r\n    tags?: string[];\r\n    sectors?: string[];\r\n    platforms?: string[];\r\n  }): boolean {\r\n    if (criteria.threatTypes && !criteria.threatTypes.includes(this.threatType)) {\r\n      return false;\r\n    }\r\n\r\n    if (criteria.severities && !criteria.severities.includes(this.severity)) {\r\n      return false;\r\n    }\r\n\r\n    if (criteria.tags && this.tags) {\r\n      const hasMatchingTag = criteria.tags.some(tag => this.tags.includes(tag));\r\n      if (!hasMatchingTag) {\r\n        return false;\r\n      }\r\n    }\r\n\r\n    if (criteria.sectors && this.targetedSectors) {\r\n      const hasMatchingSector = criteria.sectors.some(sector => \r\n        this.targetedSectors.includes(sector)\r\n      );\r\n      if (!hasMatchingSector) {\r\n        return false;\r\n      }\r\n    }\r\n\r\n    if (criteria.platforms && this.affectedPlatforms) {\r\n      const hasMatchingPlatform = criteria.platforms.some(platform => \r\n        this.affectedPlatforms.includes(platform)\r\n      );\r\n      if (!hasMatchingPlatform) {\r\n        return false;\r\n      }\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Export threat intelligence for reporting\r\n   */\r\n  exportForReporting(): any {\r\n    return {\r\n      id: this.id,\r\n      title: this.title,\r\n      description: this.description,\r\n      threatType: this.threatType,\r\n      severity: this.severity,\r\n      confidence: this.confidence,\r\n      status: this.status,\r\n      firstSeen: this.firstSeen,\r\n      lastSeen: this.lastSeen,\r\n      expiresAt: this.expiresAt,\r\n      tags: this.tags,\r\n      dataSource: this.dataSource,\r\n      mitreAttack: this.mitreAttack,\r\n      killChain: this.killChain,\r\n      targetedSectors: this.targetedSectors,\r\n      targetedCountries: this.targetedCountries,\r\n      affectedPlatforms: this.affectedPlatforms,\r\n      riskScore: this.riskScore,\r\n      observationCount: this.observationCount,\r\n      lastObserved: this.lastObserved,\r\n      isIoc: this.isIoc,\r\n      isAttributed: this.isAttributed,\r\n      threatActor: this.threatActor?.name,\r\n      threatCampaign: this.threatCampaign?.name,\r\n      indicatorCount: this.indicators?.length || 0,\r\n      references: this.references,\r\n      createdAt: this.createdAt,\r\n      updatedAt: this.updatedAt,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Activate threat intelligence\r\n   */\r\n  activate(): void {\r\n    this.status = ThreatStatus.ACTIVE;\r\n  }\r\n\r\n  /**\r\n   * Deactivate threat intelligence\r\n   */\r\n  deactivate(): void {\r\n    this.status = ThreatStatus.INACTIVE;\r\n  }\r\n\r\n  /**\r\n   * Mark as expired\r\n   */\r\n  expire(): void {\r\n    this.status = ThreatStatus.EXPIRED;\r\n    this.expiresAt = new Date();\r\n  }\r\n\r\n  /**\r\n   * Revoke threat intelligence\r\n   */\r\n  revoke(reason?: string): void {\r\n    this.status = ThreatStatus.REVOKED;\r\n    if (reason) {\r\n      this.customAttributes = {\r\n        ...this.customAttributes,\r\n        revocationReason: reason,\r\n        revokedAt: new Date(),\r\n      };\r\n    }\r\n  }\r\n}\r\n"], "version": 3}