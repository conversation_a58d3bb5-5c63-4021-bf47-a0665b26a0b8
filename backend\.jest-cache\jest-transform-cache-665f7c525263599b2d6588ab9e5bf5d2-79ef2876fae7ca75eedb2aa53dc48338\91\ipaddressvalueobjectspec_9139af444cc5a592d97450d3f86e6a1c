4b943aabc6e103ef6ba877b18800aa3c
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const ip_address_value_object_1 = require("./ip-address.value-object");
describe('IPAddress Value Object', () => {
    describe('IPv4 Addresses', () => {
        it('should create valid IPv4 address', () => {
            const ip = ip_address_value_object_1.IPAddress.fromString('***********');
            expect(ip.type).toBe(ip_address_value_object_1.IPAddressType.IPv4);
            expect(ip.toString()).toBe('***********');
            expect(ip.isIPv4()).toBe(true);
            expect(ip.isIPv6()).toBe(false);
        });
        it('should create IPv4 from octets', () => {
            const ip = ip_address_value_object_1.IPAddress.fromIPv4Octets(10, 0, 0, 1);
            expect(ip.toString()).toBe('********');
            expect(ip.getOctets()).toEqual([10, 0, 0, 1]);
        });
        it('should validate IPv4 format', () => {
            expect(() => ip_address_value_object_1.IPAddress.fromString('256.1.1.1')).toThrow('Invalid IP address format');
            expect(() => ip_address_value_object_1.IPAddress.fromString('192.168.1')).toThrow('Invalid IP address format');
            expect(() => ip_address_value_object_1.IPAddress.fromString('***********.1')).toThrow('Invalid IP address format');
        });
        it('should classify private IPv4 addresses', () => {
            const privateIPs = [
                '********',
                '**********',
                '***********',
            ];
            privateIPs.forEach(ipStr => {
                const ip = ip_address_value_object_1.IPAddress.fromString(ipStr);
                expect(ip.classify()).toBe(ip_address_value_object_1.IPAddressClass.PRIVATE);
                expect(ip.isPrivate()).toBe(true);
                expect(ip.isPublic()).toBe(false);
            });
        });
        it('should classify public IPv4 addresses', () => {
            const publicIPs = [
                '*******',
                '*******',
                '**************',
            ];
            publicIPs.forEach(ipStr => {
                const ip = ip_address_value_object_1.IPAddress.fromString(ipStr);
                expect(ip.classify()).toBe(ip_address_value_object_1.IPAddressClass.PUBLIC);
                expect(ip.isPublic()).toBe(true);
                expect(ip.isPrivate()).toBe(false);
            });
        });
        it('should classify loopback IPv4 addresses', () => {
            const loopbackIPs = [
                '127.0.0.1',
                '*********',
                '***************',
            ];
            loopbackIPs.forEach(ipStr => {
                const ip = ip_address_value_object_1.IPAddress.fromString(ipStr);
                expect(ip.classify()).toBe(ip_address_value_object_1.IPAddressClass.LOOPBACK);
                expect(ip.isLoopback()).toBe(true);
            });
        });
        it('should classify multicast IPv4 addresses', () => {
            const multicastIPs = [
                '*********',
                '***************',
            ];
            multicastIPs.forEach(ipStr => {
                const ip = ip_address_value_object_1.IPAddress.fromString(ipStr);
                expect(ip.classify()).toBe(ip_address_value_object_1.IPAddressClass.MULTICAST);
                expect(ip.isMulticast()).toBe(true);
            });
        });
        it('should classify link-local IPv4 addresses', () => {
            const linkLocalIPs = [
                '***********',
                '***************',
            ];
            linkLocalIPs.forEach(ipStr => {
                const ip = ip_address_value_object_1.IPAddress.fromString(ipStr);
                expect(ip.classify()).toBe(ip_address_value_object_1.IPAddressClass.LINK_LOCAL);
            });
        });
        it('should convert IPv4 to integer', () => {
            const ip = ip_address_value_object_1.IPAddress.fromString('***********');
            const expected = (192 << 24) + (168 << 16) + (1 << 8) + 1;
            expect(ip.toIPv4Integer()).toBe(expected);
        });
        it('should check network membership for IPv4', () => {
            const ip = ip_address_value_object_1.IPAddress.fromString('***********00');
            expect(ip.isInNetwork('***********', 24)).toBe(true);
            expect(ip.isInNetwork('***********', 16)).toBe(true);
            expect(ip.isInNetwork('***********', 24)).toBe(false);
        });
        it('should get network address for IPv4', () => {
            const ip = ip_address_value_object_1.IPAddress.fromString('***********00');
            const network = ip.getNetworkAddress(24);
            expect(network.toString()).toBe('***********');
        });
        it('should generate reverse DNS format for IPv4', () => {
            const ip = ip_address_value_object_1.IPAddress.fromString('***********');
            expect(ip.getReverseDNSFormat()).toBe('***********.in-addr.arpa');
        });
    });
    describe('IPv6 Addresses', () => {
        it('should create valid IPv6 address', () => {
            const ip = ip_address_value_object_1.IPAddress.fromString('2001:db8::1');
            expect(ip.type).toBe(ip_address_value_object_1.IPAddressType.IPv6);
            expect(ip.isIPv6()).toBe(true);
            expect(ip.isIPv4()).toBe(false);
        });
        it('should create full IPv6 address', () => {
            const ip = ip_address_value_object_1.IPAddress.fromString('2001:0db8:85a3:0000:0000:8a2e:0370:7334');
            expect(ip.isIPv6()).toBe(true);
            expect(ip.getSegments()).toHaveLength(8);
        });
        it('should validate IPv6 format', () => {
            expect(() => ip_address_value_object_1.IPAddress.fromString('2001:db8::1::2')).toThrow('Invalid IP address format');
            expect(() => ip_address_value_object_1.IPAddress.fromString('2001:db8:gggg::1')).toThrow('Invalid IP address format');
        });
        it('should classify loopback IPv6 address', () => {
            const ip = ip_address_value_object_1.IPAddress.fromString('::1');
            expect(ip.classify()).toBe(ip_address_value_object_1.IPAddressClass.LOOPBACK);
            expect(ip.isLoopback()).toBe(true);
        });
        it('should classify link-local IPv6 addresses', () => {
            const linkLocalIPs = [
                'fe80::1',
                'fe80::1234:5678:9abc:def0',
            ];
            linkLocalIPs.forEach(ipStr => {
                const ip = ip_address_value_object_1.IPAddress.fromString(ipStr);
                expect(ip.classify()).toBe(ip_address_value_object_1.IPAddressClass.LINK_LOCAL);
            });
        });
        it('should classify multicast IPv6 addresses', () => {
            const multicastIPs = [
                'ff00::1',
                'ff02::1',
            ];
            multicastIPs.forEach(ipStr => {
                const ip = ip_address_value_object_1.IPAddress.fromString(ipStr);
                expect(ip.classify()).toBe(ip_address_value_object_1.IPAddressClass.MULTICAST);
                expect(ip.isMulticast()).toBe(true);
            });
        });
        it('should classify private IPv6 addresses', () => {
            const privateIPs = [
                'fc00::1',
                'fd00::1',
            ];
            privateIPs.forEach(ipStr => {
                const ip = ip_address_value_object_1.IPAddress.fromString(ipStr);
                expect(ip.classify()).toBe(ip_address_value_object_1.IPAddressClass.PRIVATE);
                expect(ip.isPrivate()).toBe(true);
            });
        });
        it('should classify public IPv6 addresses', () => {
            const publicIPs = [
                '2001:4860:4860::8888', // Google DNS
                '2606:4700:4700::1111', // Cloudflare DNS
            ];
            publicIPs.forEach(ipStr => {
                const ip = ip_address_value_object_1.IPAddress.fromString(ipStr);
                expect(ip.classify()).toBe(ip_address_value_object_1.IPAddressClass.PUBLIC);
                expect(ip.isPublic()).toBe(true);
            });
        });
    });
    describe('Security Analysis', () => {
        it('should detect suspicious patterns in IPv4', () => {
            const suspiciousIPs = [
                '*******', // All same octets
                '*******', // Sequential octets
            ];
            suspiciousIPs.forEach(ipStr => {
                const ip = ip_address_value_object_1.IPAddress.fromString(ipStr);
                expect(ip.isSuspicious()).toBe(true);
            });
        });
        it('should not flag normal IPs as suspicious', () => {
            const normalIPs = [
                '*******',
                '***********00',
                '*********',
            ];
            normalIPs.forEach(ipStr => {
                const ip = ip_address_value_object_1.IPAddress.fromString(ipStr);
                expect(ip.isSuspicious()).toBe(false);
            });
        });
        it('should provide region hints for public IPs', () => {
            const ip = ip_address_value_object_1.IPAddress.fromString('*******');
            const regionHint = ip.getRegionHint();
            expect(regionHint).toBeTruthy();
            expect(typeof regionHint).toBe('string');
        });
        it('should not provide region hints for private IPs', () => {
            const ip = ip_address_value_object_1.IPAddress.fromString('***********');
            expect(ip.getRegionHint()).toBeNull();
        });
    });
    describe('Utility Methods', () => {
        it('should create localhost addresses', () => {
            const ipv4Localhost = ip_address_value_object_1.IPAddress.localhost();
            const ipv6Localhost = ip_address_value_object_1.IPAddress.localhostIPv6();
            expect(ipv4Localhost.toString()).toBe('127.0.0.1');
            expect(ipv4Localhost.isLoopback()).toBe(true);
            expect(ipv6Localhost.toString()).toBe('::1');
            expect(ipv6Localhost.isLoopback()).toBe(true);
        });
        it('should validate IP address format', () => {
            expect(ip_address_value_object_1.IPAddress.isValid('***********')).toBe(true);
            expect(ip_address_value_object_1.IPAddress.isValid('2001:db8::1')).toBe(true);
            expect(ip_address_value_object_1.IPAddress.isValid('invalid')).toBe(false);
            expect(ip_address_value_object_1.IPAddress.isValid('256.1.1.1')).toBe(false);
        });
        it('should compare IP addresses for equality', () => {
            const ip1 = ip_address_value_object_1.IPAddress.fromString('***********');
            const ip2 = ip_address_value_object_1.IPAddress.fromString('***********');
            const ip3 = ip_address_value_object_1.IPAddress.fromString('***********');
            expect(ip1.equals(ip2)).toBe(true);
            expect(ip1.equals(ip3)).toBe(false);
            expect(ip1.equals(undefined)).toBe(false);
        });
        it('should serialize to JSON', () => {
            const ip = ip_address_value_object_1.IPAddress.fromString('***********');
            const json = ip.toJSON();
            expect(json.address).toBe('***********');
            expect(json.type).toBe(ip_address_value_object_1.IPAddressType.IPv4);
            expect(json.classification).toBe(ip_address_value_object_1.IPAddressClass.PRIVATE);
            expect(json.isPrivate).toBe(true);
            expect(json.isPublic).toBe(false);
        });
        it('should deserialize from JSON', () => {
            const json = { address: '***********' };
            const ip = ip_address_value_object_1.IPAddress.fromJSON(json);
            expect(ip.toString()).toBe('***********');
            expect(ip.isIPv4()).toBe(true);
        });
    });
    describe('Error Handling', () => {
        it('should throw error for empty IP address', () => {
            expect(() => ip_address_value_object_1.IPAddress.fromString('')).toThrow('IP address cannot be empty');
            expect(() => ip_address_value_object_1.IPAddress.fromString('   ')).toThrow('IP address cannot be empty');
        });
        it('should throw error for invalid IPv4 octets', () => {
            expect(() => ip_address_value_object_1.IPAddress.fromIPv4Octets(256, 1, 1, 1)).toThrow('Invalid IP address format');
            expect(() => ip_address_value_object_1.IPAddress.fromIPv4Octets(-1, 1, 1, 1)).toThrow('Invalid IP address format');
        });
        it('should throw error when getting octets from IPv6', () => {
            const ip = ip_address_value_object_1.IPAddress.fromString('2001:db8::1');
            expect(() => ip.getOctets()).toThrow('Cannot get octets for non-IPv4 address');
        });
        it('should throw error when getting segments from IPv4', () => {
            const ip = ip_address_value_object_1.IPAddress.fromString('***********');
            expect(() => ip.getSegments()).toThrow('Cannot get segments for non-IPv6 address');
        });
        it('should throw error when converting IPv6 to integer', () => {
            const ip = ip_address_value_object_1.IPAddress.fromString('2001:db8::1');
            expect(() => ip.toIPv4Integer()).toThrow('Cannot convert non-IPv4 address to integer');
        });
    });
    describe('Edge Cases', () => {
        it('should handle broadcast address', () => {
            const ip = ip_address_value_object_1.IPAddress.fromString('***************');
            expect(ip.classify()).toBe(ip_address_value_object_1.IPAddressClass.BROADCAST);
        });
        it('should handle zero address', () => {
            const ip = ip_address_value_object_1.IPAddress.fromString('0.0.0.0');
            expect(ip.classify()).toBe(ip_address_value_object_1.IPAddressClass.RESERVED);
        });
        it('should handle IPv6 zero address', () => {
            const ip = ip_address_value_object_1.IPAddress.fromString('::');
            expect(ip.classify()).toBe(ip_address_value_object_1.IPAddressClass.RESERVED);
        });
        it('should handle case-insensitive IPv6 comparison', () => {
            const ip1 = ip_address_value_object_1.IPAddress.fromString('2001:DB8::1');
            const ip2 = ip_address_value_object_1.IPAddress.fromString('2001:db8::1');
            expect(ip1.equals(ip2)).toBe(true);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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