2c0d6f311bf4ce1fab38fb6f343d5153
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessingStage = exports.ProcessingAction = void 0;
/**
 * Processing Action Enum
 */
var ProcessingAction;
(function (ProcessingAction) {
    ProcessingAction["SKIP"] = "SKIP";
    ProcessingAction["PRIORITIZE"] = "PRIORITIZE";
    ProcessingAction["DELAY"] = "DELAY";
    ProcessingAction["ROUTE_TO_QUEUE"] = "ROUTE_TO_QUEUE";
    ProcessingAction["APPLY_TAGS"] = "APPLY_TAGS";
    ProcessingAction["SET_SEVERITY"] = "SET_SEVERITY";
    ProcessingAction["TRIGGER_ALERT"] = "TRIGGER_ALERT";
    ProcessingAction["CUSTOM"] = "CUSTOM";
})(ProcessingAction || (exports.ProcessingAction = ProcessingAction = {}));
/**
 * Processing Stage Enum
 */
var ProcessingStage;
(function (ProcessingStage) {
    ProcessingStage["VALIDATION"] = "VALIDATION";
    ProcessingStage["NORMALIZATION"] = "NORMALIZATION";
    ProcessingStage["ENRICHMENT"] = "ENRICHMENT";
    ProcessingStage["CORRELATION"] = "CORRELATION";
    ProcessingStage["THREAT_ANALYSIS"] = "THREAT_ANALYSIS";
    ProcessingStage["RESPONSE_GENERATION"] = "RESPONSE_GENERATION";
})(ProcessingStage || (exports.ProcessingStage = ProcessingStage = {}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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