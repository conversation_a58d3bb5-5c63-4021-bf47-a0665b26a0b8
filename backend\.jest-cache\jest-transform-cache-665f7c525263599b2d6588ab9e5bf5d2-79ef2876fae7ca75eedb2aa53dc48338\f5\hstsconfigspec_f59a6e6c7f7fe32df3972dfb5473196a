af99b3f136487736a9297795108f21c9
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const hsts_config_1 = require("../hsts.config");
describe('HstsConfig', () => {
    let hstsConfig;
    let configService;
    beforeEach(async () => {
        const mockConfigService = {
            get: jest.fn(),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                hsts_config_1.HstsConfig,
                {
                    provide: config_1.ConfigService,
                    useValue: mockConfigService,
                },
            ],
        }).compile();
        hstsConfig = module.get(hsts_config_1.HstsConfig);
        configService = module.get(config_1.ConfigService);
    });
    describe('generateHSTSHeader', () => {
        it('should return production HSTS for production environment', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'production';
                if (key === 'security.hsts.maxAge')
                    return 31536000;
                if (key === 'security.hsts.includeSubDomains')
                    return true;
                if (key === 'security.hsts.preload')
                    return true;
                return defaultValue;
            });
            const hsts = hstsConfig.generateHSTSHeader();
            expect(hsts).toBe('max-age=31536000; includeSubDomains; preload');
        });
        it('should return staging HSTS for staging environment', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'staging';
                if (key === 'security.hsts.maxAge')
                    return 7776000;
                if (key === 'security.hsts.includeSubDomains')
                    return true;
                return defaultValue;
            });
            const hsts = hstsConfig.generateHSTSHeader();
            expect(hsts).toBe('max-age=7776000; includeSubDomains');
            expect(hsts).not.toContain('preload');
        });
        it('should return development HSTS for development environment', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'development';
                if (key === 'security.hsts.maxAge')
                    return 3600;
                return defaultValue;
            });
            const hsts = hstsConfig.generateHSTSHeader();
            expect(hsts).toBe('max-age=3600');
            expect(hsts).not.toContain('includeSubDomains');
            expect(hsts).not.toContain('preload');
        });
        it('should return test HSTS for test environment', () => {
            configService.get.mockReturnValue('test');
            const hsts = hstsConfig.generateHSTSHeader();
            expect(hsts).toBe('max-age=0');
        });
        it('should use default values when config is missing', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'production';
                return defaultValue;
            });
            const hsts = hstsConfig.generateHSTSHeader();
            expect(hsts).toContain('max-age=31536000');
            expect(hsts).toContain('includeSubDomains');
            expect(hsts).toContain('preload');
        });
    });
    describe('isHSTSEnabled', () => {
        it('should be enabled in production', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'production';
                if (key === 'security.hsts.disabled')
                    return false;
                return defaultValue;
            });
            const enabled = hstsConfig.isHSTSEnabled();
            expect(enabled).toBe(true);
        });
        it('should be enabled in staging', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'staging';
                if (key === 'security.hsts.disabled')
                    return false;
                return defaultValue;
            });
            const enabled = hstsConfig.isHSTSEnabled();
            expect(enabled).toBe(true);
        });
        it('should be disabled in development', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'development';
                return defaultValue;
            });
            const enabled = hstsConfig.isHSTSEnabled();
            expect(enabled).toBe(false);
        });
        it('should be disabled when force disabled', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'production';
                if (key === 'security.hsts.disabled')
                    return true;
                return defaultValue;
            });
            const enabled = hstsConfig.isHSTSEnabled();
            expect(enabled).toBe(false);
        });
    });
    describe('getDomainSpecificHSTS', () => {
        beforeEach(() => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'production';
                return defaultValue;
            });
        });
        it('should return API-specific HSTS for API domains', () => {
            const hsts = hstsConfig.getDomainSpecificHSTS('api');
            expect(hsts).toBe('max-age=63072000; includeSubDomains; preload');
        });
        it('should return admin-specific HSTS for admin domains', () => {
            const hsts = hstsConfig.getDomainSpecificHSTS('admin');
            expect(hsts).toBe('max-age=63072000; includeSubDomains; preload');
        });
        it('should return default HSTS for public domains', () => {
            const defaultHsts = hstsConfig.generateHSTSHeader();
            const publicHsts = hstsConfig.getDomainSpecificHSTS('public');
            expect(publicHsts).toBe(defaultHsts);
        });
        it('should return staging API HSTS in staging environment', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'staging';
                return defaultValue;
            });
            const hsts = hstsConfig.getDomainSpecificHSTS('api');
            expect(hsts).toBe('max-age=15552000; includeSubDomains');
            expect(hsts).not.toContain('preload');
        });
        it('should return development API HSTS in development environment', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'development';
                return defaultValue;
            });
            const hsts = hstsConfig.getDomainSpecificHSTS('api');
            expect(hsts).toBe('max-age=3600');
        });
    });
    describe('validateHSTSConfig', () => {
        it('should validate valid HSTS configuration', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'production';
                return defaultValue;
            });
            const isValid = hstsConfig.validateHSTSConfig();
            expect(isValid).toBe(true);
        });
        it('should fail validation for empty HSTS header', () => {
            configService.get.mockReturnValue('production');
            jest.spyOn(hstsConfig, 'getProductionHSTS').mockReturnValue('');
            const isValid = hstsConfig.validateHSTSConfig();
            expect(isValid).toBe(false);
        });
        it('should fail validation for missing max-age', () => {
            configService.get.mockReturnValue('production');
            jest.spyOn(hstsConfig, 'getProductionHSTS').mockReturnValue('includeSubDomains; preload');
            const isValid = hstsConfig.validateHSTSConfig();
            expect(isValid).toBe(false);
        });
        it('should fail validation for negative max-age', () => {
            configService.get.mockReturnValue('production');
            jest.spyOn(hstsConfig, 'getProductionHSTS').mockReturnValue('max-age=-1');
            const isValid = hstsConfig.validateHSTSConfig();
            expect(isValid).toBe(false);
        });
        it('should warn about short max-age in production', () => {
            configService.get.mockReturnValue('production');
            jest.spyOn(hstsConfig, 'getProductionHSTS').mockReturnValue('max-age=3600');
            const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
            const isValid = hstsConfig.validateHSTSConfig();
            expect(isValid).toBe(true);
            expect(consoleSpy).toHaveBeenCalledWith('HSTS: Production max-age should be at least 1 year (31536000 seconds)');
            consoleSpy.mockRestore();
        });
        it('should warn about missing preload in production', () => {
            configService.get.mockReturnValue('production');
            jest.spyOn(hstsConfig, 'getProductionHSTS').mockReturnValue('max-age=31536000; includeSubDomains');
            const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
            const isValid = hstsConfig.validateHSTSConfig();
            expect(isValid).toBe(true);
            expect(consoleSpy).toHaveBeenCalledWith('HSTS: Consider enabling preload for production domains');
            consoleSpy.mockRestore();
        });
        it('should handle validation errors gracefully', () => {
            configService.get.mockImplementation(() => {
                throw new Error('Configuration error');
            });
            const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
            const isValid = hstsConfig.validateHSTSConfig();
            expect(isValid).toBe(false);
            expect(consoleSpy).toHaveBeenCalled();
            consoleSpy.mockRestore();
        });
    });
    describe('getPreloadEligibility', () => {
        it('should return eligible for proper production configuration', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'production';
                return defaultValue;
            });
            const eligibility = hstsConfig.getPreloadEligibility();
            expect(eligibility.eligible).toBe(true);
            expect(eligibility.issues).toHaveLength(0);
            expect(eligibility.requirements).toContain('Serve HSTS header with max-age >= 31536000 (1 year)');
        });
        it('should return not eligible for short max-age', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'production';
                if (key === 'security.hsts.maxAge')
                    return 3600;
                return defaultValue;
            });
            const eligibility = hstsConfig.getPreloadEligibility();
            expect(eligibility.eligible).toBe(false);
            expect(eligibility.issues).toContain('max-age must be at least 31536000 seconds (1 year)');
        });
        it('should return not eligible for missing includeSubDomains', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'production';
                if (key === 'security.hsts.includeSubDomains')
                    return false;
                return defaultValue;
            });
            const eligibility = hstsConfig.getPreloadEligibility();
            expect(eligibility.eligible).toBe(false);
            expect(eligibility.issues).toContain('includeSubDomains directive is required');
        });
        it('should return not eligible for missing preload', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'production';
                if (key === 'security.hsts.preload')
                    return false;
                return defaultValue;
            });
            const eligibility = hstsConfig.getPreloadEligibility();
            expect(eligibility.eligible).toBe(false);
            expect(eligibility.issues).toContain('preload directive is required');
        });
        it('should return not eligible for non-production environment', () => {
            configService.get.mockReturnValue('development');
            const eligibility = hstsConfig.getPreloadEligibility();
            expect(eligibility.eligible).toBe(false);
            expect(eligibility.issues).toContain('Preload is only recommended for production environments');
        });
    });
    describe('getHSTSConfigSummary', () => {
        it('should return configuration summary', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'production';
                return defaultValue;
            });
            const summary = hstsConfig.getHSTSConfigSummary();
            expect(summary.environment).toBe('production');
            expect(summary.enabled).toBe(true);
            expect(summary.maxAge).toBe(31536000);
            expect(summary.maxAgeDays).toBe(365);
            expect(summary.includeSubDomains).toBe(true);
            expect(summary.preload).toBe(true);
            expect(summary.preloadEligible).toBe(true);
            expect(typeof summary.headerLength).toBe('number');
        });
        it('should handle disabled HSTS', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'NODE_ENV')
                    return 'development';
                return defaultValue;
            });
            const summary = hstsConfig.getHSTSConfigSummary();
            expect(summary.enabled).toBe(false);
            expect(summary.maxAge).toBe(3600);
            expect(summary.maxAgeDays).toBe(0);
            expect(summary.includeSubDomains).toBe(false);
            expect(summary.preload).toBe(false);
        });
    });
    describe('generateRemovalHeader', () => {
        it('should generate removal header', () => {
            const removalHeader = hstsConfig.generateRemovalHeader();
            expect(removalHeader).toBe('max-age=0');
        });
    });
    describe('isInPreloadList', () => {
        it('should check if domain is in preload list', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                if (key === 'security.hsts.preloadDomains')
                    return ['example.com', 'api.example.com'];
                return defaultValue;
            });
            expect(hstsConfig.isInPreloadList('example.com')).toBe(true);
            expect(hstsConfig.isInPreloadList('api.example.com')).toBe(true);
            expect(hstsConfig.isInPreloadList('other.com')).toBe(false);
        });
        it('should return false when no preload domains configured', () => {
            configService.get.mockReturnValue([]);
            expect(hstsConfig.isInPreloadList('example.com')).toBe(false);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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