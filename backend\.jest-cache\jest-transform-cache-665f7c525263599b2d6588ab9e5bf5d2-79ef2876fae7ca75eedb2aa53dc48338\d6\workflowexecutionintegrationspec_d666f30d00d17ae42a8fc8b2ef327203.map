{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\testing\\integration\\workflow-execution.integration.spec.ts", "mappings": ";;;;;;;;;;;;AAAA,6CAAsD;AACtD,6CAAgD;AAChD,2CAA8C;AAC9C,yDAA2D;AAC3D,yEAAsG;AACtG,qFAAgF;AAChF,qEAAgE;AAChE,kGAA6F;AAC7F,2HAAqH;AACrH,qHAA+G;AAI/G,0EAAsE;AAEtE;;;;;;;;;;;GAWG;AACH,QAAQ,CAAC,sCAAsC,EAAE,GAAG,EAAE;IACpD,IAAI,SAA2C,CAAC;IAEhD,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,SAAS,GAAG,IAAI,gCAAgC,EAAE,CAAC;QACnD,MAAM,SAAS,CAAC,UAAU,EAAE,CAAC;IAC/B,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,SAAS,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACrE,MAAM,SAAS,CAAC,2BAA2B,EAAE,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;YACpE,MAAM,SAAS,CAAC,6BAA6B,EAAE,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;YACtE,MAAM,SAAS,CAAC,gCAAgC,EAAE,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,SAAS,CAAC,yBAAyB,EAAE,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,SAAS,CAAC,2BAA2B,EAAE,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,SAAS,CAAC,oBAAoB,EAAE,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,SAAS,CAAC,wBAAwB,EAAE,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,MAAM,SAAS,CAAC,sBAAsB,EAAE,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,KAAK,IAAI,EAAE;YAC7E,MAAM,SAAS,CAAC,0BAA0B,EAAE,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,SAAS,CAAC,wBAAwB,EAAE,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,KAAK,IAAI,EAAE;YACxE,MAAM,SAAS,CAAC,+BAA+B,EAAE,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gEAAgE,EAAE,KAAK,IAAI,EAAE;YAC9E,MAAM,SAAS,CAAC,oBAAoB,EAAE,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,SAAS,CAAC,mBAAmB,EAAE,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,MAAM,SAAS,CAAC,sBAAsB,EAAE,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,KAAK,IAAI,EAAE;YACxE,MAAM,SAAS,CAAC,6BAA6B,EAAE,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,KAAK,IAAI,EAAE;YACxE,MAAM,SAAS,CAAC,mBAAmB,EAAE,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,MAAM,SAAS,CAAC,sBAAsB,EAAE,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,MAAM,SAAS,CAAC,yBAAyB,EAAE,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,SAAS,CAAC,iBAAiB,EAAE,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,SAAS,CAAC,2BAA2B,EAAE,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,MAAM,SAAS,CAAC,mBAAmB,EAAE,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,MAAM,gCAAiC,SAAQ,2CAAmB;IAKtD,KAAK,CAAC,mBAAmB;QACjC,OAAO,cAAI,CAAC,mBAAmB,CAAC;YAC9B,OAAO,EAAE;gBACP,qBAAY,CAAC,OAAO,CAAC;oBACnB,QAAQ,EAAE,IAAI;oBACd,WAAW,EAAE,WAAW;iBACzB,CAAC;gBACF,uBAAa,CAAC,YAAY,CAAC;oBACzB,UAAU,EAAE,CAAC,aAAuC,EAAE,EAAE,CACtD,aAAa,CAAC,qBAAqB,EAAE;oBACvC,MAAM,EAAE,CAAC,qDAAwB,CAAC;iBACnC,CAAC;gBACF,kCAAkB,CAAC,OAAO,EAAE;gBAC5B,mDAAuB;gBACvB,oCAAgB;aACjB;YACD,SAAS,EAAE;gBACT,qDAAwB;gBACxB,mCAAe;aAChB;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;IACf,CAAC;IAES,KAAK,CAAC,aAAa;QAC3B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAC3C,kEAA8B,CAC/B,CAAC;QACF,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CACxC,4DAA2B,CAC5B,CAAC;QAEF,0BAA0B;QAC1B,IAAI,CAAC,aAAa,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;IAC/E,CAAC;IAIK,AAAN,KAAK,CAAC,2BAA2B;QAC/B,sBAAsB;QACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC;QAC9E,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QAE/B,kBAAkB;QAClB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,IAAI,CAAC,kBAAkB,CACzD,GAAG,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;YACxC,UAAU,EAAE,QAAQ,CAAC,EAAE;YACvB,aAAa,EAAE,uBAAuB;YACtC,SAAS,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;YAC3B,WAAW,EAAE,WAAW;YACxB,QAAQ,EAAE,QAAQ;SACnB,CAAC,EACF,wBAAwB,CACzB,CAAC;QAEF,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;QAChC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEzC,sBAAsB;QACtB,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE;YAC5B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YACzE,OAAO,OAAO,EAAE,MAAM,KAAK,WAAW,IAAI,OAAO,EAAE,MAAM,KAAK,QAAQ,CAAC;QACzE,CAAC,EAAE,KAAK,CAAC,CAAC;QAEV,qBAAqB;QACrB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QAChF,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAChD,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;QACjD,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAEnD,6BAA6B;QAC7B,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,4BAA4B,CAAC,CAAC;QACtE,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,8BAA8B,CAAC,CAAC;QAC3E,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACnC,MAAM,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC;IAGK,AAAN,KAAK,CAAC,6BAA6B;QACjC,sCAAsC;QACtC,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,8BAA8B,EAAE,CAAC;QAErE,kBAAkB;QAClB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;YAC1D,UAAU,EAAE,gBAAgB,CAAC,EAAE;YAC/B,aAAa,EAAE,yBAAyB;YACxC,SAAS,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;YACrC,WAAW,EAAE,WAAW;YACxB,eAAe,EAAE;gBACf,gBAAgB,EAAE,CAAC;gBACnB,aAAa,EAAE,IAAI;aACpB;SACF,CAAC,CAAC;QAEH,sBAAsB;QACtB,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE;YAC5B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YACzE,OAAO,OAAO,EAAE,MAAM,KAAK,WAAW,CAAC;QACzC,CAAC,EAAE,KAAK,CAAC,CAAC;QAEV,uCAAuC;QACvC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QAChF,MAAM,CAAC,cAAc,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;QAElF,sCAAsC;QACtC,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,UAAU,CAAC;aAC1E,MAAM,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC;QACxD,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC;IAGK,AAAN,KAAK,CAAC,gCAAgC;QACpC,yCAAyC;QACzC,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,iCAAiC,EAAE,CAAC;QAE3E,mBAAmB;QACnB,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;YAC9D,UAAU,EAAE,mBAAmB,CAAC,EAAE;YAClC,aAAa,EAAE,uBAAuB;YACtC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;YAC9B,WAAW,EAAE,WAAW;SACzB,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE;YAC5B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;YAC7E,OAAO,OAAO,EAAE,MAAM,KAAK,WAAW,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,oBAAoB;QACpB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;YAC/D,UAAU,EAAE,mBAAmB,CAAC,EAAE;YAClC,aAAa,EAAE,wBAAwB;YACvC,SAAS,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;YAC/B,WAAW,EAAE,WAAW;SACzB,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE;YAC5B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;YAC9E,OAAO,OAAO,EAAE,MAAM,KAAK,WAAW,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,0CAA0C;QAC1C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;QAChF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;QAElF,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;QACpE,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;QAErE,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAQ,CAAC;QAC1F,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAQ,CAAC;QAE5F,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC/C,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACnD,CAAC;IAGK,AAAN,KAAK,CAAC,yBAAyB;QAC7B,kCAAkC;QAClC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAE7D,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;YAC1D,UAAU,EAAE,YAAY,CAAC,EAAE;YAC3B,aAAa,EAAE,qBAAqB;YACpC,SAAS,EAAE,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE;YACrC,WAAW,EAAE,WAAW;SACzB,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE;YAC5B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YACzE,OAAO,OAAO,EAAE,MAAM,KAAK,WAAW,CAAC;QACzC,CAAC,EAAE,KAAK,CAAC,CAAC;QAEV,wBAAwB;QACxB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QAChF,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;QAE1D,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAQ,CAAC;QAC/E,MAAM,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC9C,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC;IAGK,AAAN,KAAK,CAAC,2BAA2B;QAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QAEvC,kBAAkB;QAClB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;YAC1D,UAAU,EAAE,QAAQ,CAAC,EAAE;YACvB,aAAa,EAAE,uBAAuB;YACtC,SAAS,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;YAC5B,WAAW,EAAE,WAAW;SACzB,CAAC,CAAC;QAEH,oBAAoB;QACpB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QACjF,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;QAEnC,eAAe;QACf,MAAM,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,EAAE;YACvD,WAAW,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;SAChC,CAAC,CAAC;QAEH,sBAAsB;QACtB,IAAI,iBAAiB,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QACjF,MAAM,CAAC,iBAAiB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE1D,yBAAyB;QACzB,MAAM,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,SAAS,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC;QAE1E,kBAAkB;QAClB,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QACrF,MAAM,CAAC,mBAAmB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;IACnE,CAAC;IAGK,AAAN,KAAK,CAAC,oBAAoB;QACxB,oCAAoC;QACpC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,6BAA6B,EAAE,CAAC;QAEnE,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;YAC1D,UAAU,EAAE,eAAe,CAAC,EAAE;YAC9B,aAAa,EAAE,sBAAsB;YACrC,SAAS,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;YAC/B,WAAW,EAAE,WAAW;YACxB,eAAe,EAAE;gBACf,eAAe,EAAE,IAAI;gBACrB,iBAAiB,EAAE,CAAC;aACrB;SACF,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE;YAC5B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YACzE,OAAO,OAAO,EAAE,MAAM,KAAK,QAAQ,IAAI,OAAO,EAAE,MAAM,KAAK,WAAW,CAAC;QACzE,CAAC,EAAE,KAAK,CAAC,CAAC;QAEV,wBAAwB;QACxB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QAChF,MAAM,CAAC,cAAc,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;QAC/D,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;IAC5E,CAAC;IAGK,AAAN,KAAK,CAAC,sBAAsB;QAC1B,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QAEvC,gCAAgC;QAChC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC;aAC/D,IAAI,CAAC;YACJ,UAAU,EAAE,QAAQ,CAAC,EAAE;YACvB,aAAa,EAAE,oBAAoB;YACnC,SAAS,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE;YAC1B,QAAQ,EAAE,MAAM;SACjB,CAAC,CAAC;QAEL,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;QAC7C,MAAM,WAAW,GAAG,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;QAE1C,2BAA2B;QAC3B,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,uBAAuB,WAAW,SAAS,CAAC,CAAC;QACnF,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC;QACzC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;QAEjD,4BAA4B;QAC5B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,uBAAuB,WAAW,oBAAoB,CAAC,CAAC;QAC/F,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC;QAC1C,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;QAEjD,sBAAsB;QACtB,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,uBAAuB,WAAW,QAAQ,CAAC;aAC9E,IAAI,CAAC,EAAE,MAAM,EAAE,6BAA6B,EAAE,CAAC,CAAC;QACnD,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;QAExC,uBAAuB;QACvB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,uBAAuB,WAAW,SAAS,CAAC,CAAC;QACpF,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC;IAC3C,CAAC;IAGK,AAAN,KAAK,CAAC,6BAA6B;QACjC,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QAEvC,2BAA2B;QAC3B,MAAM,oBAAoB,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC;aACxD,IAAI,CAAC,2BAA2B,CAAC;aACjC,IAAI,CAAC;YACJ,UAAU,EAAE,QAAQ,CAAC,EAAE;YACvB,aAAa,EAAE,mBAAmB;YAClC,SAAS,EAAE,EAAE;SACd,CAAC,CAAC;QACL,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,CAAC;QAE9C,gCAAgC;QAChC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE,aAAa,CAAC;aAC/E,IAAI,CAAC;YACJ,UAAU,EAAE,QAAQ,CAAC,EAAE;YACvB,aAAa,EAAE,aAAa;YAC5B,SAAS,EAAE,EAAE;SACd,CAAC,CAAC;QACL,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;QAErC,yBAAyB;QACzB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE,WAAW,CAAC;aAC3E,IAAI,CAAC;YACJ,UAAU,EAAE,QAAQ,CAAC,EAAE;YACvB,aAAa,EAAE,WAAW;YAC1B,SAAS,EAAE,EAAE;SACd,CAAC,CAAC;QACL,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;IAC9C,CAAC;IAGK,AAAN,KAAK,CAAC,sBAAsB;QAC1B,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QAEvC,kBAAkB;QAClB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;YAC1D,UAAU,EAAE,QAAQ,CAAC,EAAE;YACvB,aAAa,EAAE,iBAAiB;YAChC,SAAS,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;YAC5B,WAAW,EAAE,WAAW;YACxB,eAAe,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE;SACzC,CAAC,CAAC;QAEH,mCAAmC;QACnC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAEvB,wBAAwB;QACxB,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,uBAAuB,SAAS,CAAC,EAAE,UAAU,CAAC,CAAC;QACtF,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC;QAC1C,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;QACvD,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;IACjE,CAAC;IAGK,AAAN,KAAK,CAAC,iBAAiB;QACrB,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QAEvC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;YAC1D,UAAU,EAAE,QAAQ,CAAC,EAAE;YACvB,aAAa,EAAE,YAAY;YAC3B,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;YAC3B,WAAW,EAAE,WAAW;SACzB,CAAC,CAAC;QAEH,4BAA4B;QAC5B,MAAM,IAAI,CAAC,YAAY,CAAC,4BAA4B,EAAE,IAAI,CAAC,CAAC;QAE5D,oBAAoB;QACpB,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,4BAA4B,CAAC,CAAC,CAAC,CAAC,CAAC;QACzE,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QAC1D,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,8BAA8B;QAC1C,OAAO,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAClD,IAAI,EAAE,wBAAwB;YAC9B,WAAW,EAAE,8BAA8B;YAC3C,UAAU,EAAE;gBACV,KAAK,EAAE;oBACL;wBACE,EAAE,EAAE,eAAe;wBACnB,IAAI,EAAE,UAAU;wBAChB,MAAM,EAAE;4BACN,KAAK,EAAE;gCACL,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE;gCACrE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE;gCACrE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE;6BACtE;4BACD,cAAc,EAAE,CAAC;yBAClB;qBACF;iBACF;aACF;YACD,SAAS,EAAE,aAAa;YACxB,SAAS,EAAE,aAAa;SACzB,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,iCAAiC;QAC7C,OAAO,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAClD,IAAI,EAAE,2BAA2B;YACjC,WAAW,EAAE,iCAAiC;YAC9C,UAAU,EAAE;gBACV,KAAK,EAAE;oBACL;wBACE,EAAE,EAAE,kBAAkB;wBACtB,IAAI,EAAE,aAAa;wBACnB,MAAM,EAAE;4BACN,SAAS,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE;4BACrF,UAAU,EAAE;gCACV,EAAE,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,aAAa,EAAE,EAAE;6BAC9E;4BACD,WAAW,EAAE;gCACX,EAAE,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,cAAc,EAAE,EAAE;6BAChF;yBACF;qBACF;iBACF;aACF;YACD,SAAS,EAAE,aAAa;YACxB,SAAS,EAAE,aAAa;SACzB,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,0BAA0B;QACtC,OAAO,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAClD,IAAI,EAAE,oBAAoB;YAC1B,WAAW,EAAE,+BAA+B;YAC5C,UAAU,EAAE;gBACV,KAAK,EAAE;oBACL;wBACE,EAAE,EAAE,WAAW;wBACf,IAAI,EAAE,MAAM;wBACZ,MAAM,EAAE;4BACN,IAAI,EAAE,UAAU;4BAChB,gBAAgB,EAAE,OAAO;4BACzB,YAAY,EAAE,MAAM;4BACpB,KAAK,EAAE;gCACL,EAAE,EAAE,EAAE,cAAc,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,iBAAiB,EAAE,EAAE;6BACrF;yBACF;qBACF;iBACF;aACF;YACD,SAAS,EAAE,aAAa;YACxB,SAAS,EAAE,aAAa;SACzB,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,6BAA6B;QACzC,OAAO,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAClD,IAAI,EAAE,uBAAuB;YAC7B,WAAW,EAAE,uCAAuC;YACpD,UAAU,EAAE;gBACV,KAAK,EAAE;oBACL;wBACE,EAAE,EAAE,cAAc;wBAClB,IAAI,EAAE,kBAAkB;wBACxB,MAAM,EAAE;4BACN,OAAO,EAAE,iBAAiB;4BAC1B,QAAQ,EAAE,4BAA4B;4BACtC,MAAM,EAAE,MAAM;4BACd,OAAO,EAAE,IAAI;yBACd;qBACF;iBACF;aACF;YACD,SAAS,EAAE,aAAa;YACxB,SAAS,EAAE,aAAa;SACzB,CAAC,CAAC;IACL,CAAC;IAED,uDAAuD;IACvD,KAAK,CAAC,wBAAwB;QAC5B,mDAAmD;IACrD,CAAC;IAED,KAAK,CAAC,sBAAsB;QAC1B,iDAAiD;IACnD,CAAC;IAED,KAAK,CAAC,0BAA0B;QAC9B,6CAA6C;IAC/C,CAAC;IAED,KAAK,CAAC,wBAAwB;QAC5B,kDAAkD;IACpD,CAAC;IAED,KAAK,CAAC,+BAA+B;QACnC,iDAAiD;IACnD,CAAC;IAED,KAAK,CAAC,oBAAoB;QACxB,+CAA+C;IACjD,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,8CAA8C;IAChD,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,8CAA8C;IAChD,CAAC;IAED,KAAK,CAAC,yBAAyB;QAC7B,oDAAoD;IACtD,CAAC;IAED,KAAK,CAAC,2BAA2B;QAC/B,sDAAsD;IACxD,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,8CAA8C;IAChD,CAAC;CACF;AA/cO;IAFL,IAAA,uCAAe,EAAC,sCAAsC,CAAC;IACvD,IAAA,uCAAe,EAAC,KAAK,CAAC,CAAC,iBAAiB;;;;wDACJ,OAAO,oBAAP,OAAO;mFAqC3C;AAGK;IADL,IAAA,uCAAe,EAAC,mDAAmD,CAAC;;;wDAC9B,OAAO,oBAAP,OAAO;qFA8B7C;AAGK;IADL,IAAA,uCAAe,EAAC,qDAAqD,CAAC;;;wDAC7B,OAAO,oBAAP,OAAO;wFA0ChD;AAGK;IADL,IAAA,uCAAe,EAAC,gDAAgD,CAAC;;;wDAC/B,OAAO,oBAAP,OAAO;iFAuBzC;AAGK;IADL,IAAA,uCAAe,EAAC,2CAA2C,CAAC;;;wDACxB,OAAO,oBAAP,OAAO;mFA8B3C;AAGK;IADL,IAAA,uCAAe,EAAC,+BAA+B,CAAC;;;wDACnB,OAAO,oBAAP,OAAO;4EAwBpC;AAGK;IADL,IAAA,uCAAe,EAAC,wCAAwC,CAAC;;;wDAC1B,OAAO,oBAAP,OAAO;8EAiCtC;AAGK;IADL,IAAA,uCAAe,EAAC,8CAA8C,CAAC;;;wDACzB,OAAO,oBAAP,OAAO;qFA8B7C;AAGK;IADL,IAAA,uCAAe,EAAC,kCAAkC,CAAC;;;wDACpB,OAAO,oBAAP,OAAO;8EAoBtC;AAGK;IADL,IAAA,uCAAe,EAAC,6BAA6B,CAAC;;;wDACpB,OAAO,oBAAP,OAAO;yEAmBjC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\testing\\integration\\workflow-execution.integration.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { TypeOrmModule } from '@nestjs/typeorm';\r\nimport { ConfigModule } from '@nestjs/config';\r\nimport { EventEmitterModule } from '@nestjs/event-emitter';\r\nimport { IntegrationTestBase, IntegrationTest, PerformanceTest } from '../base/integration-test.base';\r\nimport { TestConfigurationService } from '../config/test-configuration.service';\r\nimport { TestDataService } from '../fixtures/test-data.service';\r\nimport { WorkflowExecutionModule } from '../../workflow-execution/workflow-execution.module';\r\nimport { WorkflowExecutionEngineService } from '../../workflow-execution/services/workflow-execution-engine.service';\r\nimport { WorkflowStateManagerService } from '../../workflow-execution/services/workflow-state-manager.service';\r\nimport { WorkflowExecution } from '../../workflow-execution/entities/workflow-execution.entity';\r\nimport { WorkflowExecutionContext } from '../../workflow-execution/entities/workflow-execution-context.entity';\r\nimport { WorkflowTemplate } from '../../templates/entities/workflow-template.entity';\r\nimport { MonitoringModule } from '../../monitoring/monitoring.module';\r\n\r\n/**\r\n * Workflow Execution Integration Tests\r\n * \r\n * Comprehensive integration testing for workflow execution engine including:\r\n * - End-to-end workflow execution scenarios\r\n * - Parallel processing validation\r\n * - Conditional logic testing\r\n * - Error handling and recovery\r\n * - State management and persistence\r\n * - Performance and load testing\r\n * - Cross-module integration validation\r\n */\r\ndescribe('Workflow Execution Integration Tests', () => {\r\n  let testSuite: WorkflowExecutionIntegrationTest;\r\n\r\n  beforeEach(async () => {\r\n    testSuite = new WorkflowExecutionIntegrationTest();\r\n    await testSuite.beforeEach();\r\n  });\r\n\r\n  afterEach(async () => {\r\n    await testSuite.afterEach();\r\n  });\r\n\r\n  describe('Workflow Execution Lifecycle', () => {\r\n    it('should start and complete a simple workflow execution', async () => {\r\n      await testSuite.testSimpleWorkflowExecution();\r\n    });\r\n\r\n    it('should handle workflow execution with parallel steps', async () => {\r\n      await testSuite.testParallelWorkflowExecution();\r\n    });\r\n\r\n    it('should execute conditional workflow branches correctly', async () => {\r\n      await testSuite.testConditionalWorkflowExecution();\r\n    });\r\n\r\n    it('should handle workflow execution with loops', async () => {\r\n      await testSuite.testLoopWorkflowExecution();\r\n    });\r\n\r\n    it('should manage workflow execution state correctly', async () => {\r\n      await testSuite.testWorkflowStateManagement();\r\n    });\r\n  });\r\n\r\n  describe('Error Handling and Recovery', () => {\r\n    it('should handle step failures with retry mechanisms', async () => {\r\n      await testSuite.testStepFailureRetry();\r\n    });\r\n\r\n    it('should execute compensation workflows on failure', async () => {\r\n      await testSuite.testCompensationWorkflow();\r\n    });\r\n\r\n    it('should rollback to checkpoints on critical failures', async () => {\r\n      await testSuite.testCheckpointRollback();\r\n    });\r\n\r\n    it('should handle external service failures with circuit breakers', async () => {\r\n      await testSuite.testCircuitBreakerHandling();\r\n    });\r\n  });\r\n\r\n  describe('Performance and Scalability', () => {\r\n    it('should handle concurrent workflow executions', async () => {\r\n      await testSuite.testConcurrentExecutions();\r\n    });\r\n\r\n    it('should meet performance benchmarks for execution startup', async () => {\r\n      await testSuite.testExecutionStartupPerformance();\r\n    });\r\n\r\n    it('should efficiently manage memory during long-running workflows', async () => {\r\n      await testSuite.testMemoryManagement();\r\n    });\r\n\r\n    it('should scale parallel step execution effectively', async () => {\r\n      await testSuite.testParallelScaling();\r\n    });\r\n  });\r\n\r\n  describe('API Integration', () => {\r\n    it('should provide complete REST API functionality', async () => {\r\n      await testSuite.testRestApiIntegration();\r\n    });\r\n\r\n    it('should handle authentication and authorization correctly', async () => {\r\n      await testSuite.testAuthenticationIntegration();\r\n    });\r\n\r\n    it('should validate input data and return appropriate errors', async () => {\r\n      await testSuite.testInputValidation();\r\n    });\r\n\r\n    it('should provide real-time execution monitoring', async () => {\r\n      await testSuite.testRealTimeMonitoring();\r\n    });\r\n  });\r\n\r\n  describe('Cross-Module Integration', () => {\r\n    it('should integrate with monitoring infrastructure', async () => {\r\n      await testSuite.testMonitoringIntegration();\r\n    });\r\n\r\n    it('should emit events for workflow lifecycle', async () => {\r\n      await testSuite.testEventEmission();\r\n    });\r\n\r\n    it('should integrate with notification systems', async () => {\r\n      await testSuite.testNotificationIntegration();\r\n    });\r\n\r\n    it('should maintain data consistency across modules', async () => {\r\n      await testSuite.testDataConsistency();\r\n    });\r\n  });\r\n});\r\n\r\nclass WorkflowExecutionIntegrationTest extends IntegrationTestBase {\r\n  private executionEngine: WorkflowExecutionEngineService;\r\n  private stateManager: WorkflowStateManagerService;\r\n  private testTemplates: WorkflowTemplate[];\r\n\r\n  protected async createTestingModule(): Promise<TestingModule> {\r\n    return Test.createTestingModule({\r\n      imports: [\r\n        ConfigModule.forRoot({\r\n          isGlobal: true,\r\n          envFilePath: '.env.test',\r\n        }),\r\n        TypeOrmModule.forRootAsync({\r\n          useFactory: (configService: TestConfigurationService) => \r\n            configService.getTestDatabaseConfig(),\r\n          inject: [TestConfigurationService],\r\n        }),\r\n        EventEmitterModule.forRoot(),\r\n        WorkflowExecutionModule,\r\n        MonitoringModule,\r\n      ],\r\n      providers: [\r\n        TestConfigurationService,\r\n        TestDataService,\r\n      ],\r\n    }).compile();\r\n  }\r\n\r\n  protected async setupTestData(): Promise<void> {\r\n    this.executionEngine = this.testingModule.get<WorkflowExecutionEngineService>(\r\n      WorkflowExecutionEngineService\r\n    );\r\n    this.stateManager = this.testingModule.get<WorkflowStateManagerService>(\r\n      WorkflowStateManagerService\r\n    );\r\n\r\n    // Generate test templates\r\n    this.testTemplates = await this.testDataService.generateWorkflowTemplates(5);\r\n  }\r\n\r\n  @IntegrationTest('Simple workflow execution end-to-end')\r\n  @PerformanceTest(10000) // 10 seconds max\r\n  async testSimpleWorkflowExecution(): Promise<void> {\r\n    // Get simple template\r\n    const template = this.testTemplates.find(t => t.definition.steps.length <= 3);\r\n    expect(template).toBeDefined();\r\n\r\n    // Start execution\r\n    const { result: execution } = await this.measurePerformance(\r\n      () => this.executionEngine.startExecution({\r\n        templateId: template.id,\r\n        executionName: 'Test Simple Execution',\r\n        inputData: { test: 'data' },\r\n        triggeredBy: 'test-user',\r\n        priority: 'medium',\r\n      }),\r\n      'simple_execution_start'\r\n    );\r\n\r\n    expect(execution).toBeDefined();\r\n    expect(execution.status).toBe('pending');\r\n\r\n    // Wait for completion\r\n    await this.waitFor(async () => {\r\n      const updated = await this.stateManager.loadExecutionState(execution.id);\r\n      return updated?.status === 'completed' || updated?.status === 'failed';\r\n    }, 15000);\r\n\r\n    // Verify final state\r\n    const finalExecution = await this.stateManager.loadExecutionState(execution.id);\r\n    expect(finalExecution.status).toBe('completed');\r\n    expect(finalExecution.completedAt).toBeDefined();\r\n    expect(finalExecution.duration).toBeGreaterThan(0);\r\n\r\n    // Verify events were emitted\r\n    const startEvent = this.getEventHistory('workflow.execution.started');\r\n    const completeEvent = this.getEventHistory('workflow.execution.completed');\r\n    expect(startEvent).toHaveLength(1);\r\n    expect(completeEvent).toHaveLength(1);\r\n  }\r\n\r\n  @IntegrationTest('Parallel workflow execution with concurrent steps')\r\n  async testParallelWorkflowExecution(): Promise<void> {\r\n    // Create template with parallel steps\r\n    const parallelTemplate = await this.createParallelWorkflowTemplate();\r\n\r\n    // Start execution\r\n    const execution = await this.executionEngine.startExecution({\r\n      templateId: parallelTemplate.id,\r\n      executionName: 'Test Parallel Execution',\r\n      inputData: { items: [1, 2, 3, 4, 5] },\r\n      triggeredBy: 'test-user',\r\n      executionConfig: {\r\n        maxParallelSteps: 3,\r\n        enableMetrics: true,\r\n      },\r\n    });\r\n\r\n    // Wait for completion\r\n    await this.waitFor(async () => {\r\n      const updated = await this.stateManager.loadExecutionState(execution.id);\r\n      return updated?.status === 'completed';\r\n    }, 20000);\r\n\r\n    // Verify parallel execution efficiency\r\n    const finalExecution = await this.stateManager.loadExecutionState(execution.id);\r\n    expect(finalExecution.performanceMetrics?.parallelEfficiency).toBeGreaterThan(50);\r\n\r\n    // Verify all parallel steps completed\r\n    const parallelSteps = Object.values(finalExecution.executionState.stepStates)\r\n      .filter((state: any) => state.status === 'completed');\r\n    expect(parallelSteps.length).toBeGreaterThan(1);\r\n  }\r\n\r\n  @IntegrationTest('Conditional workflow execution with branching logic')\r\n  async testConditionalWorkflowExecution(): Promise<void> {\r\n    // Create template with conditional steps\r\n    const conditionalTemplate = await this.createConditionalWorkflowTemplate();\r\n\r\n    // Test true branch\r\n    const trueExecution = await this.executionEngine.startExecution({\r\n      templateId: conditionalTemplate.id,\r\n      executionName: 'Test Conditional True',\r\n      inputData: { condition: true },\r\n      triggeredBy: 'test-user',\r\n    });\r\n\r\n    await this.waitFor(async () => {\r\n      const updated = await this.stateManager.loadExecutionState(trueExecution.id);\r\n      return updated?.status === 'completed';\r\n    });\r\n\r\n    // Test false branch\r\n    const falseExecution = await this.executionEngine.startExecution({\r\n      templateId: conditionalTemplate.id,\r\n      executionName: 'Test Conditional False',\r\n      inputData: { condition: false },\r\n      triggeredBy: 'test-user',\r\n    });\r\n\r\n    await this.waitFor(async () => {\r\n      const updated = await this.stateManager.loadExecutionState(falseExecution.id);\r\n      return updated?.status === 'completed';\r\n    });\r\n\r\n    // Verify different branches were executed\r\n    const trueResult = await this.stateManager.loadExecutionState(trueExecution.id);\r\n    const falseResult = await this.stateManager.loadExecutionState(falseExecution.id);\r\n\r\n    expect(trueResult.executionState.conditionalBranches).toBeDefined();\r\n    expect(falseResult.executionState.conditionalBranches).toBeDefined();\r\n\r\n    const trueBranch = Object.values(trueResult.executionState.conditionalBranches)[0] as any;\r\n    const falseBranch = Object.values(falseResult.executionState.conditionalBranches)[0] as any;\r\n\r\n    expect(trueBranch.selectedBranch).toBe('true');\r\n    expect(falseBranch.selectedBranch).toBe('false');\r\n  }\r\n\r\n  @IntegrationTest('Loop workflow execution with iteration control')\r\n  async testLoopWorkflowExecution(): Promise<void> {\r\n    // Create template with loop steps\r\n    const loopTemplate = await this.createLoopWorkflowTemplate();\r\n\r\n    const execution = await this.executionEngine.startExecution({\r\n      templateId: loopTemplate.id,\r\n      executionName: 'Test Loop Execution',\r\n      inputData: { items: ['a', 'b', 'c'] },\r\n      triggeredBy: 'test-user',\r\n    });\r\n\r\n    await this.waitFor(async () => {\r\n      const updated = await this.stateManager.loadExecutionState(execution.id);\r\n      return updated?.status === 'completed';\r\n    }, 25000);\r\n\r\n    // Verify loop execution\r\n    const finalExecution = await this.stateManager.loadExecutionState(execution.id);\r\n    expect(finalExecution.executionState.loops).toBeDefined();\r\n\r\n    const loopState = Object.values(finalExecution.executionState.loops)[0] as any;\r\n    expect(loopState.completedIterations).toBe(3);\r\n    expect(loopState.results).toHaveLength(3);\r\n  }\r\n\r\n  @IntegrationTest('Workflow state management and persistence')\r\n  async testWorkflowStateManagement(): Promise<void> {\r\n    const template = this.testTemplates[0];\r\n\r\n    // Start execution\r\n    const execution = await this.executionEngine.startExecution({\r\n      templateId: template.id,\r\n      executionName: 'Test State Management',\r\n      inputData: { test: 'state' },\r\n      triggeredBy: 'test-user',\r\n    });\r\n\r\n    // Create checkpoint\r\n    const checkpointId = await this.stateManager.createStateCheckpoint(execution.id);\r\n    expect(checkpointId).toBeDefined();\r\n\r\n    // Modify state\r\n    await this.stateManager.saveExecutionState(execution.id, {\r\n      contextData: { modified: true },\r\n    });\r\n\r\n    // Verify state change\r\n    let modifiedExecution = await this.stateManager.loadExecutionState(execution.id);\r\n    expect(modifiedExecution.contextData.modified).toBe(true);\r\n\r\n    // Rollback to checkpoint\r\n    await this.stateManager.restoreFromCheckpoint(execution.id, checkpointId);\r\n\r\n    // Verify rollback\r\n    const rolledBackExecution = await this.stateManager.loadExecutionState(execution.id);\r\n    expect(rolledBackExecution.contextData.modified).toBeUndefined();\r\n  }\r\n\r\n  @IntegrationTest('Step failure retry mechanisms')\r\n  async testStepFailureRetry(): Promise<void> {\r\n    // Create template with failing step\r\n    const failingTemplate = await this.createFailingWorkflowTemplate();\r\n\r\n    const execution = await this.executionEngine.startExecution({\r\n      templateId: failingTemplate.id,\r\n      executionName: 'Test Retry Mechanism',\r\n      inputData: { shouldFail: true },\r\n      triggeredBy: 'test-user',\r\n      executionConfig: {\r\n        enableAutoRetry: true,\r\n        defaultMaxRetries: 3,\r\n      },\r\n    });\r\n\r\n    await this.waitFor(async () => {\r\n      const updated = await this.stateManager.loadExecutionState(execution.id);\r\n      return updated?.status === 'failed' || updated?.status === 'completed';\r\n    }, 30000);\r\n\r\n    // Verify retry attempts\r\n    const finalExecution = await this.stateManager.loadExecutionState(execution.id);\r\n    expect(finalExecution.retryConfig?.retryHistory).toBeDefined();\r\n    expect(finalExecution.retryConfig.retryHistory.length).toBeGreaterThan(0);\r\n  }\r\n\r\n  @IntegrationTest('REST API integration and functionality')\r\n  async testRestApiIntegration(): Promise<void> {\r\n    const template = this.testTemplates[0];\r\n\r\n    // Test start execution endpoint\r\n    const startResponse = await this.post('/workflow-execution/start')\r\n      .send({\r\n        templateId: template.id,\r\n        executionName: 'API Test Execution',\r\n        inputData: { api: 'test' },\r\n        priority: 'high',\r\n      });\r\n\r\n    this.expectValidResponse(startResponse, 201);\r\n    const executionId = startResponse.body.id;\r\n\r\n    // Test get status endpoint\r\n    const statusResponse = await this.get(`/workflow-execution/${executionId}/status`);\r\n    this.expectValidResponse(statusResponse);\r\n    expect(statusResponse.body.status).toBeDefined();\r\n\r\n    // Test get details endpoint\r\n    const detailsResponse = await this.get(`/workflow-execution/${executionId}?includeSteps=true`);\r\n    this.expectValidResponse(detailsResponse);\r\n    expect(detailsResponse.body.steps).toBeDefined();\r\n\r\n    // Test pause endpoint\r\n    const pauseResponse = await this.post(`/workflow-execution/${executionId}/pause`)\r\n      .send({ reason: 'Testing pause functionality' });\r\n    this.expectValidResponse(pauseResponse);\r\n\r\n    // Test resume endpoint\r\n    const resumeResponse = await this.post(`/workflow-execution/${executionId}/resume`);\r\n    this.expectValidResponse(resumeResponse);\r\n  }\r\n\r\n  @IntegrationTest('Authentication and authorization integration')\r\n  async testAuthenticationIntegration(): Promise<void> {\r\n    const template = this.testTemplates[0];\r\n\r\n    // Test unauthorized access\r\n    const unauthorizedResponse = await request(this.httpServer)\r\n      .post('/workflow-execution/start')\r\n      .send({\r\n        templateId: template.id,\r\n        executionName: 'Unauthorized Test',\r\n        inputData: {},\r\n      });\r\n    this.expectUnauthorized(unauthorizedResponse);\r\n\r\n    // Test insufficient permissions\r\n    const viewerResponse = await this.post('/workflow-execution/start', 'test-viewer')\r\n      .send({\r\n        templateId: template.id,\r\n        executionName: 'Viewer Test',\r\n        inputData: {},\r\n      });\r\n    this.expectForbidden(viewerResponse);\r\n\r\n    // Test valid permissions\r\n    const userResponse = await this.post('/workflow-execution/start', 'test-user')\r\n      .send({\r\n        templateId: template.id,\r\n        executionName: 'User Test',\r\n        inputData: {},\r\n      });\r\n    this.expectValidResponse(userResponse, 201);\r\n  }\r\n\r\n  @IntegrationTest('Real-time monitoring integration')\r\n  async testRealTimeMonitoring(): Promise<void> {\r\n    const template = this.testTemplates[0];\r\n\r\n    // Start execution\r\n    const execution = await this.executionEngine.startExecution({\r\n      templateId: template.id,\r\n      executionName: 'Monitoring Test',\r\n      inputData: { monitor: true },\r\n      triggeredBy: 'test-user',\r\n      executionConfig: { enableMetrics: true },\r\n    });\r\n\r\n    // Wait for some execution progress\r\n    await this.sleep(2000);\r\n\r\n    // Test metrics endpoint\r\n    const metricsResponse = await this.get(`/workflow-execution/${execution.id}/metrics`);\r\n    this.expectValidResponse(metricsResponse);\r\n    expect(metricsResponse.body.performance).toBeDefined();\r\n    expect(metricsResponse.body.resourceUtilization).toBeDefined();\r\n  }\r\n\r\n  @IntegrationTest('Event emission and handling')\r\n  async testEventEmission(): Promise<void> {\r\n    const template = this.testTemplates[0];\r\n\r\n    this.clearEventHistory();\r\n\r\n    const execution = await this.executionEngine.startExecution({\r\n      templateId: template.id,\r\n      executionName: 'Event Test',\r\n      inputData: { events: true },\r\n      triggeredBy: 'test-user',\r\n    });\r\n\r\n    // Wait for execution events\r\n    await this.waitForEvent('workflow.execution.started', 5000);\r\n\r\n    // Verify event data\r\n    const startEvent = this.getEventHistory('workflow.execution.started')[0];\r\n    expect(startEvent.args[0].executionId).toBe(execution.id);\r\n    expect(startEvent.args[0].templateId).toBe(template.id);\r\n  }\r\n\r\n  /**\r\n   * Helper methods for creating test templates\r\n   */\r\n  private async createParallelWorkflowTemplate(): Promise<WorkflowTemplate> {\r\n    return this.testDataService.templateRepository.save({\r\n      name: 'Parallel Test Template',\r\n      description: 'Template with parallel steps',\r\n      definition: {\r\n        steps: [\r\n          {\r\n            id: 'parallel_step',\r\n            type: 'parallel',\r\n            config: {\r\n              steps: [\r\n                { id: 'p1', type: 'notification', config: { message: 'Parallel 1' } },\r\n                { id: 'p2', type: 'notification', config: { message: 'Parallel 2' } },\r\n                { id: 'p3', type: 'notification', config: { message: 'Parallel 3' } },\r\n              ],\r\n              maxConcurrency: 3,\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      createdBy: 'test-system',\r\n      updatedBy: 'test-system',\r\n    });\r\n  }\r\n\r\n  private async createConditionalWorkflowTemplate(): Promise<WorkflowTemplate> {\r\n    return this.testDataService.templateRepository.save({\r\n      name: 'Conditional Test Template',\r\n      description: 'Template with conditional logic',\r\n      definition: {\r\n        steps: [\r\n          {\r\n            id: 'conditional_step',\r\n            type: 'conditional',\r\n            config: {\r\n              condition: { type: 'comparison', left: 'condition', operator: 'equals', right: true },\r\n              trueBranch: [\r\n                { id: 'true_step', type: 'notification', config: { message: 'True branch' } },\r\n              ],\r\n              falseBranch: [\r\n                { id: 'false_step', type: 'notification', config: { message: 'False branch' } },\r\n              ],\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      createdBy: 'test-system',\r\n      updatedBy: 'test-system',\r\n    });\r\n  }\r\n\r\n  private async createLoopWorkflowTemplate(): Promise<WorkflowTemplate> {\r\n    return this.testDataService.templateRepository.save({\r\n      name: 'Loop Test Template',\r\n      description: 'Template with loop processing',\r\n      definition: {\r\n        steps: [\r\n          {\r\n            id: 'loop_step',\r\n            type: 'loop',\r\n            config: {\r\n              type: 'for_each',\r\n              iterableVariable: 'items',\r\n              itemVariable: 'item',\r\n              steps: [\r\n                { id: 'process_item', type: 'notification', config: { message: 'Processing item' } },\r\n              ],\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      createdBy: 'test-system',\r\n      updatedBy: 'test-system',\r\n    });\r\n  }\r\n\r\n  private async createFailingWorkflowTemplate(): Promise<WorkflowTemplate> {\r\n    return this.testDataService.templateRepository.save({\r\n      name: 'Failing Test Template',\r\n      description: 'Template that fails for retry testing',\r\n      definition: {\r\n        steps: [\r\n          {\r\n            id: 'failing_step',\r\n            type: 'external_service',\r\n            config: {\r\n              service: 'failing_service',\r\n              endpoint: 'http://localhost:9999/fail',\r\n              method: 'POST',\r\n              timeout: 5000,\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      createdBy: 'test-system',\r\n      updatedBy: 'test-system',\r\n    });\r\n  }\r\n\r\n  // Additional test methods would be implemented here...\r\n  async testCompensationWorkflow(): Promise<void> {\r\n    // Implementation for compensation workflow testing\r\n  }\r\n\r\n  async testCheckpointRollback(): Promise<void> {\r\n    // Implementation for checkpoint rollback testing\r\n  }\r\n\r\n  async testCircuitBreakerHandling(): Promise<void> {\r\n    // Implementation for circuit breaker testing\r\n  }\r\n\r\n  async testConcurrentExecutions(): Promise<void> {\r\n    // Implementation for concurrent execution testing\r\n  }\r\n\r\n  async testExecutionStartupPerformance(): Promise<void> {\r\n    // Implementation for startup performance testing\r\n  }\r\n\r\n  async testMemoryManagement(): Promise<void> {\r\n    // Implementation for memory management testing\r\n  }\r\n\r\n  async testParallelScaling(): Promise<void> {\r\n    // Implementation for parallel scaling testing\r\n  }\r\n\r\n  async testInputValidation(): Promise<void> {\r\n    // Implementation for input validation testing\r\n  }\r\n\r\n  async testMonitoringIntegration(): Promise<void> {\r\n    // Implementation for monitoring integration testing\r\n  }\r\n\r\n  async testNotificationIntegration(): Promise<void> {\r\n    // Implementation for notification integration testing\r\n  }\r\n\r\n  async testDataConsistency(): Promise<void> {\r\n    // Implementation for data consistency testing\r\n  }\r\n}\r\n"], "version": 3}