9b630404dc5d1990649e3dfc0f4bc7da
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
const circuit_breaker_1 = require("../../patterns/circuit-breaker");
const service_unavailable_exception_1 = require("../../exceptions/service-unavailable.exception");
describe('Circuit Breaker', () => {
    describe('CircuitBreaker class', () => {
        it('should start in CLOSED state', () => {
            const circuitBreaker = new circuit_breaker_1.CircuitBreaker({
                failureThreshold: 3,
                recoveryTimeout: 1000,
                monitoringPeriod: 5000,
                halfOpenMaxCalls: 1,
            });
            const metrics = circuitBreaker.getMetrics();
            expect(metrics.state).toBe(circuit_breaker_1.CircuitBreakerState.CLOSED);
            expect(metrics.failureCount).toBe(0);
            expect(metrics.successCount).toBe(0);
        });
        it('should remain CLOSED for successful operations', async () => {
            const circuitBreaker = new circuit_breaker_1.CircuitBreaker({
                failureThreshold: 3,
                recoveryTimeout: 1000,
                monitoringPeriod: 5000,
                halfOpenMaxCalls: 1,
            });
            const successfulOperation = jest.fn().mockResolvedValue('success');
            const result1 = await circuitBreaker.execute(successfulOperation);
            const result2 = await circuitBreaker.execute(successfulOperation);
            expect(result1).toBe('success');
            expect(result2).toBe('success');
            expect(successfulOperation).toHaveBeenCalledTimes(2);
            const metrics = circuitBreaker.getMetrics();
            expect(metrics.state).toBe(circuit_breaker_1.CircuitBreakerState.CLOSED);
            expect(metrics.successCount).toBe(2);
            expect(metrics.failureCount).toBe(0);
        });
        it('should transition to OPEN after failure threshold is reached', async () => {
            const circuitBreaker = new circuit_breaker_1.CircuitBreaker({
                failureThreshold: 2,
                recoveryTimeout: 1000,
                monitoringPeriod: 5000,
                halfOpenMaxCalls: 1,
            });
            const failingOperation = jest.fn().mockRejectedValue(new Error('Operation failed'));
            // First failure
            await expect(circuitBreaker.execute(failingOperation)).rejects.toThrow('Operation failed');
            expect(circuitBreaker.getMetrics().state).toBe(circuit_breaker_1.CircuitBreakerState.CLOSED);
            // Second failure - should open circuit
            await expect(circuitBreaker.execute(failingOperation)).rejects.toThrow('Operation failed');
            expect(circuitBreaker.getMetrics().state).toBe(circuit_breaker_1.CircuitBreakerState.OPEN);
            expect(failingOperation).toHaveBeenCalledTimes(2);
        });
        it('should reject calls immediately when OPEN', async () => {
            const circuitBreaker = new circuit_breaker_1.CircuitBreaker({
                failureThreshold: 1,
                recoveryTimeout: 1000,
                monitoringPeriod: 5000,
                halfOpenMaxCalls: 1,
            });
            const failingOperation = jest.fn().mockRejectedValue(new Error('Operation failed'));
            // Trigger circuit to open
            await expect(circuitBreaker.execute(failingOperation)).rejects.toThrow('Operation failed');
            expect(circuitBreaker.getMetrics().state).toBe(circuit_breaker_1.CircuitBreakerState.OPEN);
            // Subsequent calls should be rejected without executing the operation
            await expect(circuitBreaker.execute(failingOperation)).rejects.toThrow(service_unavailable_exception_1.ServiceUnavailableException);
            await expect(circuitBreaker.execute(failingOperation)).rejects.toThrow(service_unavailable_exception_1.ServiceUnavailableException);
            expect(failingOperation).toHaveBeenCalledTimes(1); // Only called once to open circuit
        });
        it('should transition to HALF_OPEN after recovery timeout', async () => {
            const circuitBreaker = new circuit_breaker_1.CircuitBreaker({
                failureThreshold: 1,
                recoveryTimeout: 50, // Short timeout for testing
                monitoringPeriod: 5000,
                halfOpenMaxCalls: 1,
            });
            const failingOperation = jest.fn().mockRejectedValue(new Error('Operation failed'));
            // Open the circuit
            await expect(circuitBreaker.execute(failingOperation)).rejects.toThrow('Operation failed');
            expect(circuitBreaker.getMetrics().state).toBe(circuit_breaker_1.CircuitBreakerState.OPEN);
            // Wait for recovery timeout
            await new Promise(resolve => setTimeout(resolve, 60));
            // Next call should transition to HALF_OPEN
            const successfulOperation = jest.fn().mockResolvedValue('success');
            const result = await circuitBreaker.execute(successfulOperation);
            expect(result).toBe('success');
            expect(circuitBreaker.getMetrics().state).toBe(circuit_breaker_1.CircuitBreakerState.CLOSED);
        });
        it('should transition from HALF_OPEN to CLOSED on success', async () => {
            const circuitBreaker = new circuit_breaker_1.CircuitBreaker({
                failureThreshold: 1,
                recoveryTimeout: 50,
                monitoringPeriod: 5000,
                halfOpenMaxCalls: 1,
            });
            // Open the circuit
            const failingOperation = jest.fn().mockRejectedValue(new Error('Failure'));
            await expect(circuitBreaker.execute(failingOperation)).rejects.toThrow('Failure');
            // Wait for recovery
            await new Promise(resolve => setTimeout(resolve, 60));
            // Successful call should close the circuit
            const successfulOperation = jest.fn().mockResolvedValue('recovery success');
            const result = await circuitBreaker.execute(successfulOperation);
            expect(result).toBe('recovery success');
            expect(circuitBreaker.getMetrics().state).toBe(circuit_breaker_1.CircuitBreakerState.CLOSED);
            expect(circuitBreaker.getMetrics().failureCount).toBe(0);
        });
        it('should transition from HALF_OPEN to OPEN on failure', async () => {
            const circuitBreaker = new circuit_breaker_1.CircuitBreaker({
                failureThreshold: 1,
                recoveryTimeout: 50,
                monitoringPeriod: 5000,
                halfOpenMaxCalls: 1,
            });
            // Open the circuit
            const failingOperation = jest.fn().mockRejectedValue(new Error('Failure'));
            await expect(circuitBreaker.execute(failingOperation)).rejects.toThrow('Failure');
            // Wait for recovery
            await new Promise(resolve => setTimeout(resolve, 60));
            // Failed call should reopen the circuit
            await expect(circuitBreaker.execute(failingOperation)).rejects.toThrow('Failure');
            expect(circuitBreaker.getMetrics().state).toBe(circuit_breaker_1.CircuitBreakerState.OPEN);
        });
        it('should limit calls in HALF_OPEN state', async () => {
            const circuitBreaker = new circuit_breaker_1.CircuitBreaker({
                failureThreshold: 1,
                recoveryTimeout: 50,
                monitoringPeriod: 5000,
                halfOpenMaxCalls: 1,
            });
            // Open the circuit
            const failingOperation = jest.fn().mockRejectedValue(new Error('Failure'));
            await expect(circuitBreaker.execute(failingOperation)).rejects.toThrow('Failure');
            // Wait for recovery
            await new Promise(resolve => setTimeout(resolve, 60));
            // First call should be allowed (transitions to HALF_OPEN)
            const slowOperation = jest.fn().mockImplementation(() => new Promise(resolve => setTimeout(() => resolve('slow success'), 100)));
            // Start first call (should be allowed)
            const firstCallPromise = circuitBreaker.execute(slowOperation);
            // Second call should be rejected due to halfOpenMaxCalls limit
            await expect(circuitBreaker.execute(slowOperation)).rejects.toThrow(service_unavailable_exception_1.ServiceUnavailableException);
            // Wait for first call to complete
            const result = await firstCallPromise;
            expect(result).toBe('slow success');
            expect(circuitBreaker.getMetrics().state).toBe(circuit_breaker_1.CircuitBreakerState.CLOSED);
        });
        it('should call onStateChange callback', async () => {
            const onStateChange = jest.fn();
            const circuitBreaker = new circuit_breaker_1.CircuitBreaker({
                failureThreshold: 1,
                recoveryTimeout: 50,
                monitoringPeriod: 5000,
                halfOpenMaxCalls: 1,
                onStateChange,
            });
            const failingOperation = jest.fn().mockRejectedValue(new Error('Failure'));
            // Trigger state change to OPEN
            await expect(circuitBreaker.execute(failingOperation)).rejects.toThrow('Failure');
            expect(onStateChange).toHaveBeenCalledWith(circuit_breaker_1.CircuitBreakerState.OPEN);
            // Wait for recovery and trigger state change to HALF_OPEN then CLOSED
            await new Promise(resolve => setTimeout(resolve, 60));
            const successfulOperation = jest.fn().mockResolvedValue('success');
            await circuitBreaker.execute(successfulOperation);
            expect(onStateChange).toHaveBeenCalledWith(circuit_breaker_1.CircuitBreakerState.HALF_OPEN);
            expect(onStateChange).toHaveBeenCalledWith(circuit_breaker_1.CircuitBreakerState.CLOSED);
        });
        it('should provide accurate metrics', async () => {
            const circuitBreaker = new circuit_breaker_1.CircuitBreaker({
                failureThreshold: 3,
                recoveryTimeout: 1000,
                monitoringPeriod: 5000,
                halfOpenMaxCalls: 1,
            });
            const successfulOperation = jest.fn().mockResolvedValue('success');
            const failingOperation = jest.fn().mockRejectedValue(new Error('failure'));
            // Execute some operations
            await circuitBreaker.execute(successfulOperation);
            await circuitBreaker.execute(successfulOperation);
            await expect(circuitBreaker.execute(failingOperation)).rejects.toThrow('failure');
            const metrics = circuitBreaker.getMetrics();
            expect(metrics.successCount).toBe(2);
            expect(metrics.failureCount).toBe(1);
            expect(metrics.totalCalls).toBe(3);
            expect(metrics.state).toBe(circuit_breaker_1.CircuitBreakerState.CLOSED);
            expect(metrics.lastSuccessTime).toBeDefined();
            expect(metrics.lastFailureTime).toBeDefined();
        });
        it('should reset circuit breaker state', () => {
            const circuitBreaker = new circuit_breaker_1.CircuitBreaker({
                failureThreshold: 1,
                recoveryTimeout: 1000,
                monitoringPeriod: 5000,
                halfOpenMaxCalls: 1,
            });
            // Manually set some state
            circuitBreaker.forceOpen();
            expect(circuitBreaker.getMetrics().state).toBe(circuit_breaker_1.CircuitBreakerState.OPEN);
            // Reset should return to CLOSED
            circuitBreaker.reset();
            const metrics = circuitBreaker.getMetrics();
            expect(metrics.state).toBe(circuit_breaker_1.CircuitBreakerState.CLOSED);
            expect(metrics.failureCount).toBe(0);
            expect(metrics.successCount).toBe(0);
        });
        it('should force circuit to OPEN state', () => {
            const circuitBreaker = new circuit_breaker_1.CircuitBreaker({
                failureThreshold: 5,
                recoveryTimeout: 1000,
                monitoringPeriod: 5000,
                halfOpenMaxCalls: 1,
            });
            expect(circuitBreaker.getMetrics().state).toBe(circuit_breaker_1.CircuitBreakerState.CLOSED);
            circuitBreaker.forceOpen();
            expect(circuitBreaker.getMetrics().state).toBe(circuit_breaker_1.CircuitBreakerState.OPEN);
        });
    });
    describe('@CircuitBreakerProtection decorator', () => {
        it('should protect method with circuit breaker', async () => {
            var _a;
            let callCount = 0;
            class TestService {
                async protectedMethod(shouldFail) {
                    callCount++;
                    if (shouldFail) {
                        throw new Error('Method failed');
                    }
                    return `success-${callCount}`;
                }
            }
            __decorate([
                (0, circuit_breaker_1.CircuitBreakerProtection)({
                    failureThreshold: 2,
                    recoveryTimeout: 100,
                    monitoringPeriod: 5000,
                    halfOpenMaxCalls: 1,
                }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", [Boolean]),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "protectedMethod", null);
            const service = new TestService();
            // Successful calls should work
            const result1 = await service.protectedMethod(false);
            expect(result1).toBe('success-1');
            // Trigger failures to open circuit
            await expect(service.protectedMethod(true)).rejects.toThrow('Method failed');
            await expect(service.protectedMethod(true)).rejects.toThrow('Method failed');
            // Circuit should now be open
            await expect(service.protectedMethod(false)).rejects.toThrow(service_unavailable_exception_1.ServiceUnavailableException);
            expect(callCount).toBe(3); // Method not called when circuit is open
        });
        it('should attach circuit breaker instance to decorated method', () => {
            var _a;
            class TestService {
                async decoratedMethod() {
                    return 'success';
                }
            }
            __decorate([
                (0, circuit_breaker_1.CircuitBreakerProtection)({
                    failureThreshold: 3,
                    recoveryTimeout: 1000,
                    monitoringPeriod: 5000,
                    halfOpenMaxCalls: 1,
                }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "decoratedMethod", null);
            const service = new TestService();
            const circuitBreaker = service.decoratedMethod.circuitBreaker;
            expect(circuitBreaker).toBeInstanceOf(circuit_breaker_1.CircuitBreaker);
            expect(circuitBreaker.getMetrics().state).toBe(circuit_breaker_1.CircuitBreakerState.CLOSED);
        });
        it('should handle async operations correctly', async () => {
            var _a;
            let callCount = 0;
            class TestService {
                async asyncMethod(delay, shouldFail) {
                    callCount++;
                    await new Promise(resolve => setTimeout(resolve, delay));
                    if (shouldFail) {
                        throw new Error('Async failure');
                    }
                    return `async-success-${callCount}`;
                }
            }
            __decorate([
                (0, circuit_breaker_1.CircuitBreakerProtection)({
                    failureThreshold: 1,
                    recoveryTimeout: 50,
                    monitoringPeriod: 5000,
                    halfOpenMaxCalls: 1,
                }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", [Number, Boolean]),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "asyncMethod", null);
            const service = new TestService();
            // Successful async call
            const result1 = await service.asyncMethod(10, false);
            expect(result1).toBe('async-success-1');
            // Failed async call - should open circuit
            await expect(service.asyncMethod(10, true)).rejects.toThrow('Async failure');
            // Circuit should be open
            await expect(service.asyncMethod(10, false)).rejects.toThrow(service_unavailable_exception_1.ServiceUnavailableException);
            expect(callCount).toBe(2);
        });
        it('should work with multiple instances', async () => {
            var _a;
            class TestService {
                async instanceMethod() {
                    throw new Error('Always fails');
                }
            }
            __decorate([
                (0, circuit_breaker_1.CircuitBreakerProtection)({
                    failureThreshold: 1,
                    recoveryTimeout: 1000,
                    monitoringPeriod: 5000,
                    halfOpenMaxCalls: 1,
                }),
                __metadata("design:type", Function),
                __metadata("design:paramtypes", []),
                __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
            ], TestService.prototype, "instanceMethod", null);
            const service1 = new TestService();
            const service2 = new TestService();
            // The circuit breaker is shared across all instances of the same method
            // First failure should trigger the method to fail normally
            await expect(service1.instanceMethod()).rejects.toThrow('Always fails');
            // Second failure (from any instance) should open the circuit
            // Since the circuit is now open, subsequent calls should throw ServiceUnavailableException
            await expect(service2.instanceMethod()).rejects.toThrow(service_unavailable_exception_1.ServiceUnavailableException);
            await expect(service1.instanceMethod()).rejects.toThrow(service_unavailable_exception_1.ServiceUnavailableException);
        });
    });
    describe('error scenarios', () => {
        it('should handle ServiceUnavailableException with proper details', async () => {
            const circuitBreaker = new circuit_breaker_1.CircuitBreaker({
                failureThreshold: 1,
                recoveryTimeout: 1000,
                monitoringPeriod: 5000,
                halfOpenMaxCalls: 1,
            });
            // Open the circuit
            const failingOperation = jest.fn().mockRejectedValue(new Error('Failure'));
            await expect(circuitBreaker.execute(failingOperation)).rejects.toThrow('Failure');
            // Try to execute when circuit is open
            try {
                await circuitBreaker.execute(failingOperation);
                fail('Should have thrown ServiceUnavailableException');
            }
            catch (error) {
                expect(error).toBeInstanceOf(service_unavailable_exception_1.ServiceUnavailableException);
                const serviceError = error;
                const circuitInfo = serviceError.getCircuitBreakerInfo();
                expect(circuitInfo).toMatchObject({
                    failureCount: 1,
                    failureThreshold: expect.any(Number),
                });
                expect(circuitInfo?.nextRetryTime).toBeDefined();
            }
        });
        it('should handle half-open state limit exceeded', async () => {
            const circuitBreaker = new circuit_breaker_1.CircuitBreaker({
                failureThreshold: 1,
                recoveryTimeout: 50,
                monitoringPeriod: 5000,
                halfOpenMaxCalls: 1,
            });
            // Open the circuit
            await expect(circuitBreaker.execute(() => Promise.reject(new Error('Failure'))))
                .rejects.toThrow('Failure');
            // Wait for recovery
            await new Promise(resolve => setTimeout(resolve, 60));
            // Start a slow operation to keep circuit in HALF_OPEN
            const slowOperation = () => new Promise(resolve => setTimeout(() => resolve('slow'), 100));
            const slowPromise = circuitBreaker.execute(slowOperation);
            // Try another call while in HALF_OPEN - should be rejected
            try {
                await circuitBreaker.execute(() => Promise.resolve('fast'));
                fail('Should have thrown ServiceUnavailableException');
            }
            catch (error) {
                expect(error).toBeInstanceOf(service_unavailable_exception_1.ServiceUnavailableException);
                const serviceError = error;
                expect(serviceError.context).toMatchObject({
                    state: circuit_breaker_1.CircuitBreakerState.HALF_OPEN,
                    maxCalls: 1,
                });
            }
            // Wait for slow operation to complete
            await slowPromise;
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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