b5ddde33c2cf4e1f114b220cbe2f8a1f
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThreatSharingAction = exports.TLPMarking = exports.ThreatDetectionRuleType = exports.ThreatMitigationType = exports.ThreatKillChainPhase = exports.MalwareType = exports.ThreatActorSophistication = exports.ThreatActorMotivation = exports.ThreatActorType = exports.ThreatIndicatorStatus = exports.ThreatIndicatorType = exports.ThreatStatus = exports.ThreatType = exports.ThreatCategory = exports.ThreatSourceReliability = void 0;
/**
 * Threat Source Reliability Levels
 */
var ThreatSourceReliability;
(function (ThreatSourceReliability) {
    /** Completely reliable source */
    ThreatSourceReliability["A"] = "A";
    /** Usually reliable source */
    ThreatSourceReliability["B"] = "B";
    /** Fairly reliable source */
    ThreatSourceReliability["C"] = "C";
    /** Not usually reliable source */
    ThreatSourceReliability["D"] = "D";
    /** Unreliable source */
    ThreatSourceReliability["E"] = "E";
    /** Cannot be judged */
    ThreatSourceReliability["F"] = "F";
})(ThreatSourceReliability || (exports.ThreatSourceReliability = ThreatSourceReliability = {}));
/**
 * Threat Categories
 */
var ThreatCategory;
(function (ThreatCategory) {
    ThreatCategory["MALWARE"] = "malware";
    ThreatCategory["PHISHING"] = "phishing";
    ThreatCategory["RANSOMWARE"] = "ransomware";
    ThreatCategory["APT"] = "apt";
    ThreatCategory["BOTNET"] = "botnet";
    ThreatCategory["EXPLOIT"] = "exploit";
    ThreatCategory["VULNERABILITY"] = "vulnerability";
    ThreatCategory["FRAUD"] = "fraud";
    ThreatCategory["SPAM"] = "spam";
    ThreatCategory["SCAM"] = "scam";
    ThreatCategory["INSIDER_THREAT"] = "insider_threat";
    ThreatCategory["SUPPLY_CHAIN"] = "supply_chain";
    ThreatCategory["SOCIAL_ENGINEERING"] = "social_engineering";
    ThreatCategory["DDOS"] = "ddos";
    ThreatCategory["DATA_BREACH"] = "data_breach";
    ThreatCategory["CYBER_ESPIONAGE"] = "cyber_espionage";
    ThreatCategory["HACKTIVISM"] = "hacktivism";
    ThreatCategory["CYBER_CRIME"] = "cyber_crime";
    ThreatCategory["NATION_STATE"] = "nation_state";
    ThreatCategory["OTHER"] = "other";
})(ThreatCategory || (exports.ThreatCategory = ThreatCategory = {}));
/**
 * Threat Types
 */
var ThreatType;
(function (ThreatType) {
    ThreatType["INDICATOR"] = "indicator";
    ThreatType["CAMPAIGN"] = "campaign";
    ThreatType["ACTOR"] = "actor";
    ThreatType["MALWARE"] = "malware";
    ThreatType["TOOL"] = "tool";
    ThreatType["TECHNIQUE"] = "technique";
    ThreatType["VULNERABILITY"] = "vulnerability";
    ThreatType["INCIDENT"] = "incident";
    ThreatType["REPORT"] = "report";
    ThreatType["OTHER"] = "other";
})(ThreatType || (exports.ThreatType = ThreatType = {}));
/**
 * Threat Status
 */
var ThreatStatus;
(function (ThreatStatus) {
    ThreatStatus["ACTIVE"] = "active";
    ThreatStatus["INACTIVE"] = "inactive";
    ThreatStatus["EXPIRED"] = "expired";
    ThreatStatus["REVOKED"] = "revoked";
    ThreatStatus["UNDER_REVIEW"] = "under_review";
    ThreatStatus["CONFIRMED"] = "confirmed";
    ThreatStatus["SUSPECTED"] = "suspected";
    ThreatStatus["UNKNOWN"] = "unknown";
})(ThreatStatus || (exports.ThreatStatus = ThreatStatus = {}));
/**
 * Threat Indicator Types
 */
var ThreatIndicatorType;
(function (ThreatIndicatorType) {
    ThreatIndicatorType["IP_ADDRESS"] = "ip_address";
    ThreatIndicatorType["DOMAIN"] = "domain";
    ThreatIndicatorType["URL"] = "url";
    ThreatIndicatorType["EMAIL"] = "email";
    ThreatIndicatorType["FILE_HASH"] = "file_hash";
    ThreatIndicatorType["REGISTRY_KEY"] = "registry_key";
    ThreatIndicatorType["MUTEX"] = "mutex";
    ThreatIndicatorType["USER_AGENT"] = "user_agent";
    ThreatIndicatorType["CERTIFICATE"] = "certificate";
    ThreatIndicatorType["ASN"] = "asn";
    ThreatIndicatorType["CVE"] = "cve";
    ThreatIndicatorType["YARA_RULE"] = "yara_rule";
    ThreatIndicatorType["SIGMA_RULE"] = "sigma_rule";
    ThreatIndicatorType["OTHER"] = "other";
})(ThreatIndicatorType || (exports.ThreatIndicatorType = ThreatIndicatorType = {}));
/**
 * Threat Indicator Status
 */
var ThreatIndicatorStatus;
(function (ThreatIndicatorStatus) {
    ThreatIndicatorStatus["ACTIVE"] = "active";
    ThreatIndicatorStatus["INACTIVE"] = "inactive";
    ThreatIndicatorStatus["WHITELISTED"] = "whitelisted";
    ThreatIndicatorStatus["EXPIRED"] = "expired";
    ThreatIndicatorStatus["UNDER_REVIEW"] = "under_review";
})(ThreatIndicatorStatus || (exports.ThreatIndicatorStatus = ThreatIndicatorStatus = {}));
/**
 * Threat Actor Types
 */
var ThreatActorType;
(function (ThreatActorType) {
    ThreatActorType["NATION_STATE"] = "nation_state";
    ThreatActorType["CYBER_CRIMINAL"] = "cyber_criminal";
    ThreatActorType["HACKTIVIST"] = "hacktivist";
    ThreatActorType["INSIDER"] = "insider";
    ThreatActorType["TERRORIST"] = "terrorist";
    ThreatActorType["SCRIPT_KIDDIE"] = "script_kiddie";
    ThreatActorType["UNKNOWN"] = "unknown";
})(ThreatActorType || (exports.ThreatActorType = ThreatActorType = {}));
/**
 * Threat Actor Motivations
 */
var ThreatActorMotivation;
(function (ThreatActorMotivation) {
    ThreatActorMotivation["FINANCIAL"] = "financial";
    ThreatActorMotivation["ESPIONAGE"] = "espionage";
    ThreatActorMotivation["SABOTAGE"] = "sabotage";
    ThreatActorMotivation["IDEOLOGY"] = "ideology";
    ThreatActorMotivation["REVENGE"] = "revenge";
    ThreatActorMotivation["NOTORIETY"] = "notoriety";
    ThreatActorMotivation["UNKNOWN"] = "unknown";
})(ThreatActorMotivation || (exports.ThreatActorMotivation = ThreatActorMotivation = {}));
/**
 * Threat Actor Sophistication Levels
 */
var ThreatActorSophistication;
(function (ThreatActorSophistication) {
    ThreatActorSophistication["MINIMAL"] = "minimal";
    ThreatActorSophistication["INTERMEDIATE"] = "intermediate";
    ThreatActorSophistication["ADVANCED"] = "advanced";
    ThreatActorSophistication["EXPERT"] = "expert";
    ThreatActorSophistication["INNOVATOR"] = "innovator";
    ThreatActorSophistication["STRATEGIC"] = "strategic";
})(ThreatActorSophistication || (exports.ThreatActorSophistication = ThreatActorSophistication = {}));
/**
 * Malware Types
 */
var MalwareType;
(function (MalwareType) {
    MalwareType["TROJAN"] = "trojan";
    MalwareType["VIRUS"] = "virus";
    MalwareType["WORM"] = "worm";
    MalwareType["RANSOMWARE"] = "ransomware";
    MalwareType["SPYWARE"] = "spyware";
    MalwareType["ADWARE"] = "adware";
    MalwareType["ROOTKIT"] = "rootkit";
    MalwareType["BACKDOOR"] = "backdoor";
    MalwareType["KEYLOGGER"] = "keylogger";
    MalwareType["BOTNET"] = "botnet";
    MalwareType["RAT"] = "rat";
    MalwareType["LOADER"] = "loader";
    MalwareType["DROPPER"] = "dropper";
    MalwareType["CRYPTOMINER"] = "cryptominer";
    MalwareType["OTHER"] = "other";
})(MalwareType || (exports.MalwareType = MalwareType = {}));
/**
 * Threat Kill Chain Phases
 */
var ThreatKillChainPhase;
(function (ThreatKillChainPhase) {
    ThreatKillChainPhase["RECONNAISSANCE"] = "reconnaissance";
    ThreatKillChainPhase["WEAPONIZATION"] = "weaponization";
    ThreatKillChainPhase["DELIVERY"] = "delivery";
    ThreatKillChainPhase["EXPLOITATION"] = "exploitation";
    ThreatKillChainPhase["INSTALLATION"] = "installation";
    ThreatKillChainPhase["COMMAND_CONTROL"] = "command_control";
    ThreatKillChainPhase["ACTIONS_OBJECTIVES"] = "actions_objectives";
})(ThreatKillChainPhase || (exports.ThreatKillChainPhase = ThreatKillChainPhase = {}));
/**
 * Threat Mitigation Types
 */
var ThreatMitigationType;
(function (ThreatMitigationType) {
    ThreatMitigationType["PREVENTIVE"] = "preventive";
    ThreatMitigationType["DETECTIVE"] = "detective";
    ThreatMitigationType["CORRECTIVE"] = "corrective";
    ThreatMitigationType["DETERRENT"] = "deterrent";
    ThreatMitigationType["RECOVERY"] = "recovery";
    ThreatMitigationType["COMPENSATING"] = "compensating";
})(ThreatMitigationType || (exports.ThreatMitigationType = ThreatMitigationType = {}));
/**
 * Threat Detection Rule Types
 */
var ThreatDetectionRuleType;
(function (ThreatDetectionRuleType) {
    ThreatDetectionRuleType["YARA"] = "yara";
    ThreatDetectionRuleType["SIGMA"] = "sigma";
    ThreatDetectionRuleType["SNORT"] = "snort";
    ThreatDetectionRuleType["SURICATA"] = "suricata";
    ThreatDetectionRuleType["REGEX"] = "regex";
    ThreatDetectionRuleType["IOC"] = "ioc";
    ThreatDetectionRuleType["CUSTOM"] = "custom";
})(ThreatDetectionRuleType || (exports.ThreatDetectionRuleType = ThreatDetectionRuleType = {}));
/**
 * Traffic Light Protocol Markings
 */
var TLPMarking;
(function (TLPMarking) {
    TLPMarking["RED"] = "red";
    TLPMarking["AMBER"] = "amber";
    TLPMarking["GREEN"] = "green";
    TLPMarking["WHITE"] = "white";
})(TLPMarking || (exports.TLPMarking = TLPMarking = {}));
/**
 * Threat Sharing Actions
 */
var ThreatSharingAction;
(function (ThreatSharingAction) {
    ThreatSharingAction["SHARE"] = "share";
    ThreatSharingAction["ANALYZE"] = "analyze";
    ThreatSharingAction["DETECT"] = "detect";
    ThreatSharingAction["BLOCK"] = "block";
    ThreatSharingAction["ALERT"] = "alert";
    ThreatSharingAction["INVESTIGATE"] = "investigate";
})(ThreatSharingAction || (exports.ThreatSharingAction = ThreatSharingAction = {}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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