{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\vulnerability-status-changed.event.ts", "mappings": ";;;AAAA,6DAA4E;AAC5E,yFAAqF;AAsBrF;;;;;;;;;;;;GAYG;AACH,MAAa,+BAAgC,SAAQ,+BAAoD;IACvG,YACE,WAA2B,EAC3B,SAA8C,EAC9C,OAIC;QAED,KAAK,CAAC,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,MAAM,WAAW,GAAwC;YACvD,CAAC,0CAAmB,CAAC,UAAU,CAAC,EAAE,CAAC;YACnC,CAAC,0CAAmB,CAAC,SAAS,CAAC,EAAE,CAAC;YAClC,CAAC,0CAAmB,CAAC,OAAO,CAAC,EAAE,CAAC;YAChC,CAAC,0CAAmB,CAAC,WAAW,CAAC,EAAE,CAAC;YACpC,CAAC,0CAAmB,CAAC,UAAU,CAAC,EAAE,CAAC;YACnC,CAAC,0CAAmB,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjC,CAAC,0CAAmB,CAAC,MAAM,CAAC,EAAE,CAAC;YAC/B,CAAC,0CAAmB,CAAC,cAAc,CAAC,EAAE,CAAC;YACvC,CAAC,0CAAmB,CAAC,aAAa,CAAC,EAAE,CAAC;SACvC,CAAC;QAEF,OAAO,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IACvF,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,MAAM,WAAW,GAAwC;YACvD,CAAC,0CAAmB,CAAC,UAAU,CAAC,EAAE,CAAC;YACnC,CAAC,0CAAmB,CAAC,SAAS,CAAC,EAAE,CAAC;YAClC,CAAC,0CAAmB,CAAC,OAAO,CAAC,EAAE,CAAC;YAChC,CAAC,0CAAmB,CAAC,WAAW,CAAC,EAAE,CAAC;YACpC,CAAC,0CAAmB,CAAC,UAAU,CAAC,EAAE,CAAC;YACnC,CAAC,0CAAmB,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjC,CAAC,0CAAmB,CAAC,MAAM,CAAC,EAAE,CAAC;YAC/B,CAAC,0CAAmB,CAAC,cAAc,CAAC,EAAE,CAAC;YACvC,CAAC,0CAAmB,CAAC,aAAa,CAAC,EAAE,CAAC;SACvC,CAAC;QAEF,OAAO,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;YAC7E,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,OAAO;YACL,0CAAmB,CAAC,UAAU;YAC9B,0CAAmB,CAAC,QAAQ;YAC5B,0CAAmB,CAAC,MAAM;SAC3B,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,yBAAyB;QACvB,OAAO;YACL,0CAAmB,CAAC,cAAc;YAClC,0CAAmB,CAAC,aAAa;SAClC,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,MAAM,cAAc,GAAG;YACrB,0CAAmB,CAAC,UAAU;YAC9B,0CAAmB,CAAC,QAAQ;YAC5B,0CAAmB,CAAC,MAAM;YAC1B,0CAAmB,CAAC,cAAc;YAClC,0CAAmB,CAAC,aAAa;SAClC,CAAC;QAEF,MAAM,cAAc,GAAG;YACrB,0CAAmB,CAAC,UAAU;YAC9B,0CAAmB,CAAC,SAAS;YAC7B,0CAAmB,CAAC,OAAO;YAC3B,0CAAmB,CAAC,WAAW;SAChC,CAAC;QAEF,OAAO,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;YACjD,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAAE,OAAO,SAAS,CAAC;QACpD,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAAE,OAAO,WAAW,CAAC;QACzD,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAAE,OAAO,WAAW,CAAC;QACxD,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAAE,OAAO,aAAa,CAAC;QACrD,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAAE,OAAO,YAAY,CAAC;QACnD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,uBAAuB;QACrB,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEhD,QAAQ,cAAc,EAAE,CAAC;YACvB,KAAK,SAAS;gBACZ,OAAO,QAAQ,CAAC,CAAC,4BAA4B;YAC/C,KAAK,WAAW;gBACd,OAAO,KAAK,CAAC,CAAC,gBAAgB;YAChC,KAAK,WAAW;gBACd,OAAO,MAAM,CAAC,CAAC,qBAAqB;YACtC,KAAK,YAAY;gBACf,OAAO,MAAM,CAAC,CAAC,mCAAmC;YACpD,KAAK,aAAa;gBAChB,OAAO,KAAK,CAAC,CAAC,8BAA8B;YAC9C;gBACE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,wBAAwB;QAKtB,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAChD,MAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAEhD,MAAM,YAAY,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAC1C,MAAM,cAAc,GAAG,CAAC,oBAAoB,CAAC,CAAC;QAE9C,QAAQ,cAAc,EAAE,CAAC;YACvB,KAAK,SAAS;gBACZ,OAAO;oBACL,QAAQ,EAAE,CAAC,GAAG,YAAY,EAAE,WAAW,CAAC;oBACxC,UAAU,EAAE,CAAC,GAAG,cAAc,EAAE,cAAc,CAAC;oBAC/C,OAAO,EAAE,YAAY;iBACtB,CAAC;YAEJ,KAAK,WAAW;gBACd,OAAO;oBACL,QAAQ,EAAE,YAAY;oBACtB,UAAU,EAAE,cAAc;oBAC1B,OAAO,EAAE,SAAS;iBACnB,CAAC;YAEJ,KAAK,WAAW;gBACd,OAAO;oBACL,QAAQ,EAAE,CAAC,GAAG,YAAY,EAAE,OAAO,EAAE,WAAW,CAAC;oBACjD,UAAU,EAAE,CAAC,GAAG,cAAc,EAAE,eAAe,EAAE,YAAY,CAAC;oBAC9D,OAAO,EAAE,aAAa;iBACvB,CAAC;YAEJ,KAAK,YAAY;gBACf,OAAO;oBACL,QAAQ,EAAE,CAAC,GAAG,YAAY,EAAE,OAAO,CAAC;oBACpC,UAAU,EAAE,CAAC,GAAG,cAAc,EAAE,eAAe,CAAC;oBAChD,OAAO,EAAE,aAAa;iBACvB,CAAC;YAEJ;gBACE,OAAO;oBACL,QAAQ,EAAE,YAAY;oBACtB,UAAU,EAAE,cAAc;oBAC1B,OAAO,EAAE,SAAS;iBACnB,CAAC;QACN,CAAC;IACH,CAAC;IAED;;OAEG;IACH,2BAA2B;QACzB,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEhD,QAAQ,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;YACjC,KAAK,0CAAmB,CAAC,SAAS;gBAChC,OAAO,CAAC,IAAI,CAAC,yBAAyB,EAAE,wBAAwB,CAAC,CAAC;gBAClE,MAAM;YAER,KAAK,0CAAmB,CAAC,OAAO;gBAC9B,OAAO,CAAC,IAAI,CAAC,0BAA0B,EAAE,0BAA0B,CAAC,CAAC;gBACrE,MAAM;YAER,KAAK,0CAAmB,CAAC,WAAW;gBAClC,OAAO,CAAC,IAAI,CAAC,4BAA4B,EAAE,qBAAqB,CAAC,CAAC;gBAClE,MAAM;YAER,KAAK,0CAAmB,CAAC,UAAU;gBACjC,OAAO,CAAC,IAAI,CAAC,uBAAuB,EAAE,wBAAwB,CAAC,CAAC;gBAChE,MAAM;YAER,KAAK,0CAAmB,CAAC,QAAQ;gBAC/B,OAAO,CAAC,IAAI,CAAC,qBAAqB,EAAE,gBAAgB,CAAC,CAAC;gBACtD,MAAM;YAER,KAAK,0CAAmB,CAAC,MAAM;gBAC7B,OAAO,CAAC,IAAI,CAAC,uBAAuB,EAAE,yBAAyB,CAAC,CAAC;gBACjE,MAAM;YAER,KAAK,0CAAmB,CAAC,cAAc;gBACrC,OAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE,yBAAyB,CAAC,CAAC;gBAChE,MAAM;YAER,KAAK,0CAAmB,CAAC,aAAa;gBACpC,OAAO,CAAC,IAAI,CAAC,0BAA0B,EAAE,sBAAsB,CAAC,CAAC;gBACjE,MAAM;QACV,CAAC;QAED,IAAI,cAAc,KAAK,WAAW,EAAE,CAAC;YACnC,OAAO,CAAC,IAAI,CAAC,6BAA6B,EAAE,wBAAwB,CAAC,CAAC;QACxE,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,kBAAkB;QAMhB,MAAM,OAAO,GAKR,EAAE,CAAC;QAER,MAAM,QAAQ,GAAG;YACf,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS;YACpC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS;YACpC,eAAe,EAAE,IAAI,CAAC,iBAAiB,EAAE;SAC1C,CAAC;QAEF,4BAA4B;QAC5B,OAAO,CAAC,IAAI,CAAC;YACX,MAAM,EAAE,kCAAkC;YAC1C,SAAS,EAAE,WAAW;YACtB,IAAI,EAAE,QAAQ;SACf,CAAC,CAAC;QAEH,0BAA0B;QAC1B,IAAI,IAAI,CAAC,sBAAsB,EAAE,EAAE,CAAC;YAClC,OAAO,CAAC,IAAI,CAAC;gBACX,MAAM,EAAE,wBAAwB;gBAChC,SAAS,EAAE,WAAW;gBACtB,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;aAC3C,CAAC,CAAC;YAEH,sDAAsD;YACtD,OAAO,CAAC,IAAI,CAAC;gBACX,MAAM,EAAE,4BAA4B;gBACpC,SAAS,EAAE,QAAQ;gBACnB,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;aAC3C,CAAC,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC;YACrC,OAAO,CAAC,IAAI,CAAC;gBACX,MAAM,EAAE,2BAA2B;gBACnC,SAAS,EAAE,WAAW;gBACtB,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;aAC3C,CAAC,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,wBAAwB,EAAE,EAAE,CAAC;YACpC,OAAO,CAAC,IAAI,CAAC;gBACX,MAAM,EAAE,0BAA0B;gBAClC,SAAS,EAAE,WAAW;gBACtB,IAAI,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;aAChD,CAAC,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,kCAAkC;QAMhC,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEhD,IAAI,IAAI,CAAC,sBAAsB,EAAE,EAAE,CAAC;YAClC,OAAO;gBACL,QAAQ,EAAE,IAAI;gBACd,UAAU,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC;gBAC/C,QAAQ,EAAE,aAAa;gBACvB,UAAU,EAAE,SAAS;aACtB,CAAC;QACJ,CAAC;QAED,IAAI,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC;YACrC,OAAO;gBACL,QAAQ,EAAE,IAAI;gBACd,UAAU,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC;gBAC9B,QAAQ,EAAE,aAAa;gBACvB,UAAU,EAAE,WAAW;aACxB,CAAC;QACJ,CAAC;QAED,IAAI,cAAc,KAAK,WAAW,EAAE,CAAC;YACnC,OAAO;gBACL,QAAQ,EAAE,IAAI;gBACd,UAAU,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC;gBACvC,QAAQ,EAAE,YAAY;gBACtB,UAAU,EAAE,WAAW;aACxB,CAAC;QACJ,CAAC;QAED,OAAO;YACL,QAAQ,EAAE,KAAK;YACf,UAAU,EAAE,EAAE;YACd,QAAQ,EAAE,YAAY;YACtB,UAAU,EAAE,MAAM;SACnB,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,SAAS,EAAE,4BAA4B;YACvC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YAChC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;YACxC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;YACzC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;YACxC,IAAI,EAAE;gBACJ,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe;gBAC/C,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS;gBACnC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS;gBACnC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM;gBAC7B,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS;gBACnC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO;gBAC/B,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS;aACpC;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,IAAI,CAAC,iBAAiB,EAAE;gBACxC,aAAa,EAAE,IAAI,CAAC,mBAAmB,EAAE;gBACzC,YAAY,EAAE,IAAI,CAAC,kBAAkB,EAAE;gBACvC,SAAS,EAAE,IAAI,CAAC,sBAAsB,EAAE;gBACxC,YAAY,EAAE,IAAI,CAAC,yBAAyB,EAAE;gBAC9C,WAAW,EAAE,IAAI,CAAC,wBAAwB,EAAE;gBAC5C,oBAAoB,EAAE,IAAI,CAAC,uBAAuB,EAAE;gBACpD,qBAAqB,EAAE,IAAI,CAAC,wBAAwB,EAAE;gBACtD,gBAAgB,EAAE,IAAI,CAAC,2BAA2B,EAAE;gBACpD,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;gBAC1C,sBAAsB,EAAE,IAAI,CAAC,kCAAkC,EAAE;aAClE;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,IAAyB;QACvC,OAAO,IAAI,+BAA+B,CACxC,8BAAc,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,EAC3C,IAAI,CAAC,IAAI,EACT;YACE,OAAO,EAAE,8BAAc,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC;YAChD,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;YACrC,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAC/D,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAElE,OAAO,sCAAsC,aAAa,SAAS,UAAU,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;IAC7G,CAAC;IAED;;OAEG;IACH,eAAe;QAQb,OAAO;YACL,SAAS,EAAE,4BAA4B;YACvC,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe;YAC/C,gBAAgB,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAC9E,cAAc,EAAE,IAAI,CAAC,iBAAiB,EAAE;YACxC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM;YAC7B,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;SACzC,CAAC;IACJ,CAAC;CACF;AAhdD,0EAgdC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\vulnerability-status-changed.event.ts"], "sourcesContent": ["import { BaseDomainEvent, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { VulnerabilityStatus } from '../entities/vulnerability/vulnerability.entity';\r\n\r\n/**\r\n * Vulnerability Status Changed Event Data\r\n */\r\nexport interface VulnerabilityStatusChangedEventData {\r\n  /** ID of the vulnerability */\r\n  vulnerabilityId: string;\r\n  /** Previous status */\r\n  oldStatus: VulnerabilityStatus;\r\n  /** New status */\r\n  newStatus: VulnerabilityStatus;\r\n  /** Reason for status change */\r\n  reason: string;\r\n  /** Timestamp of the change */\r\n  timestamp: string;\r\n  /** User who made the change (optional) */\r\n  changedBy?: string;\r\n  /** Additional context (optional) */\r\n  context?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Vulnerability Status Changed Domain Event\r\n * \r\n * Published when a vulnerability's status changes (e.g., from discovered to triaged,\r\n * from in_progress to remediated, etc.).\r\n * \r\n * This event enables:\r\n * - Workflow automation based on status transitions\r\n * - Audit trail for vulnerability lifecycle\r\n * - Notification triggers for stakeholders\r\n * - Metrics collection for remediation timelines\r\n * - Integration with external systems (ITSM, etc.)\r\n */\r\nexport class VulnerabilityStatusChangedEvent extends BaseDomainEvent<VulnerabilityStatusChangedEventData> {\r\n  constructor(\r\n    aggregateId: UniqueEntityId,\r\n    eventData: VulnerabilityStatusChangedEventData,\r\n    options?: {\r\n      eventId?: UniqueEntityId;\r\n      occurredOn?: Date;\r\n      version?: number;\r\n    }\r\n  ) {\r\n    super(aggregateId, eventData, options);\r\n  }\r\n\r\n  /**\r\n   * Get the vulnerability ID\r\n   */\r\n  get vulnerabilityId(): string {\r\n    return this.eventData.vulnerabilityId;\r\n  }\r\n\r\n  /**\r\n   * Get the old status\r\n   */\r\n  get oldStatus(): VulnerabilityStatus {\r\n    return this.eventData.oldStatus;\r\n  }\r\n\r\n  /**\r\n   * Get the new status\r\n   */\r\n  get newStatus(): VulnerabilityStatus {\r\n    return this.eventData.newStatus;\r\n  }\r\n\r\n  /**\r\n   * Get the reason for the change\r\n   */\r\n  get reason(): string {\r\n    return this.eventData.reason;\r\n  }\r\n\r\n  /**\r\n   * Get who made the change\r\n   */\r\n  get changedBy(): string | undefined {\r\n    return this.eventData.changedBy;\r\n  }\r\n\r\n  /**\r\n   * Get additional context\r\n   */\r\n  get context(): Record<string, any> | undefined {\r\n    return this.eventData.context;\r\n  }\r\n\r\n  /**\r\n   * Check if this is a status progression (forward movement in lifecycle)\r\n   */\r\n  isStatusProgression(): boolean {\r\n    const statusOrder: Record<VulnerabilityStatus, number> = {\r\n      [VulnerabilityStatus.DISCOVERED]: 1,\r\n      [VulnerabilityStatus.CONFIRMED]: 2,\r\n      [VulnerabilityStatus.TRIAGED]: 3,\r\n      [VulnerabilityStatus.IN_PROGRESS]: 4,\r\n      [VulnerabilityStatus.REMEDIATED]: 5,\r\n      [VulnerabilityStatus.VERIFIED]: 6,\r\n      [VulnerabilityStatus.CLOSED]: 7,\r\n      [VulnerabilityStatus.FALSE_POSITIVE]: 0,\r\n      [VulnerabilityStatus.ACCEPTED_RISK]: 0,\r\n    };\r\n\r\n    return statusOrder[this.eventData.newStatus] > statusOrder[this.eventData.oldStatus];\r\n  }\r\n\r\n  /**\r\n   * Check if this is a status regression (backward movement)\r\n   */\r\n  isStatusRegression(): boolean {\r\n    const statusOrder: Record<VulnerabilityStatus, number> = {\r\n      [VulnerabilityStatus.DISCOVERED]: 1,\r\n      [VulnerabilityStatus.CONFIRMED]: 2,\r\n      [VulnerabilityStatus.TRIAGED]: 3,\r\n      [VulnerabilityStatus.IN_PROGRESS]: 4,\r\n      [VulnerabilityStatus.REMEDIATED]: 5,\r\n      [VulnerabilityStatus.VERIFIED]: 6,\r\n      [VulnerabilityStatus.CLOSED]: 7,\r\n      [VulnerabilityStatus.FALSE_POSITIVE]: 0,\r\n      [VulnerabilityStatus.ACCEPTED_RISK]: 0,\r\n    };\r\n\r\n    return statusOrder[this.eventData.newStatus] < statusOrder[this.eventData.oldStatus] &&\r\n           statusOrder[this.eventData.newStatus] > 0;\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability was closed (remediated, verified, or closed)\r\n   */\r\n  wasVulnerabilityClosed(): boolean {\r\n    return [\r\n      VulnerabilityStatus.REMEDIATED,\r\n      VulnerabilityStatus.VERIFIED,\r\n      VulnerabilityStatus.CLOSED,\r\n    ].includes(this.eventData.newStatus);\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability was dismissed (false positive or accepted risk)\r\n   */\r\n  wasVulnerabilityDismissed(): boolean {\r\n    return [\r\n      VulnerabilityStatus.FALSE_POSITIVE,\r\n      VulnerabilityStatus.ACCEPTED_RISK,\r\n    ].includes(this.eventData.newStatus);\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability was reopened\r\n   */\r\n  wasVulnerabilityReopened(): boolean {\r\n    const closedStatuses = [\r\n      VulnerabilityStatus.REMEDIATED,\r\n      VulnerabilityStatus.VERIFIED,\r\n      VulnerabilityStatus.CLOSED,\r\n      VulnerabilityStatus.FALSE_POSITIVE,\r\n      VulnerabilityStatus.ACCEPTED_RISK,\r\n    ];\r\n\r\n    const activeStatuses = [\r\n      VulnerabilityStatus.DISCOVERED,\r\n      VulnerabilityStatus.CONFIRMED,\r\n      VulnerabilityStatus.TRIAGED,\r\n      VulnerabilityStatus.IN_PROGRESS,\r\n    ];\r\n\r\n    return closedStatuses.includes(this.eventData.oldStatus) &&\r\n           activeStatuses.includes(this.eventData.newStatus);\r\n  }\r\n\r\n  /**\r\n   * Get status transition type\r\n   */\r\n  getTransitionType(): 'progression' | 'regression' | 'closure' | 'dismissal' | 'reopening' | 'lateral' {\r\n    if (this.wasVulnerabilityClosed()) return 'closure';\r\n    if (this.wasVulnerabilityDismissed()) return 'dismissal';\r\n    if (this.wasVulnerabilityReopened()) return 'reopening';\r\n    if (this.isStatusProgression()) return 'progression';\r\n    if (this.isStatusRegression()) return 'regression';\r\n    return 'lateral';\r\n  }\r\n\r\n  /**\r\n   * Get notification priority based on status change\r\n   */\r\n  getNotificationPriority(): 'low' | 'medium' | 'high' | 'critical' {\r\n    const transitionType = this.getTransitionType();\r\n    \r\n    switch (transitionType) {\r\n      case 'closure':\r\n        return 'medium'; // Good news, but not urgent\r\n      case 'dismissal':\r\n        return 'low'; // Informational\r\n      case 'reopening':\r\n        return 'high'; // Requires attention\r\n      case 'regression':\r\n        return 'high'; // Problem that needs investigation\r\n      case 'progression':\r\n        return 'low'; // Normal workflow progression\r\n      default:\r\n        return 'low';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get required notifications based on status change\r\n   */\r\n  getRequiredNotifications(): {\r\n    channels: string[];\r\n    recipients: string[];\r\n    urgency: 'immediate' | 'within_hour' | 'within_day' | 'routine';\r\n  } {\r\n    const transitionType = this.getTransitionType();\r\n    const priority = this.getNotificationPriority();\r\n    \r\n    const baseChannels = ['email', 'webhook'];\r\n    const baseRecipients = ['vulnerability_team'];\r\n    \r\n    switch (transitionType) {\r\n      case 'closure':\r\n        return {\r\n          channels: [...baseChannels, 'dashboard'],\r\n          recipients: [...baseRecipients, 'stakeholders'],\r\n          urgency: 'within_day',\r\n        };\r\n      \r\n      case 'dismissal':\r\n        return {\r\n          channels: baseChannels,\r\n          recipients: baseRecipients,\r\n          urgency: 'routine',\r\n        };\r\n      \r\n      case 'reopening':\r\n        return {\r\n          channels: [...baseChannels, 'slack', 'dashboard'],\r\n          recipients: [...baseRecipients, 'security_team', 'management'],\r\n          urgency: 'within_hour',\r\n        };\r\n      \r\n      case 'regression':\r\n        return {\r\n          channels: [...baseChannels, 'slack'],\r\n          recipients: [...baseRecipients, 'security_team'],\r\n          urgency: 'within_hour',\r\n        };\r\n      \r\n      default:\r\n        return {\r\n          channels: baseChannels,\r\n          recipients: baseRecipients,\r\n          urgency: 'routine',\r\n        };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get workflow actions triggered by this status change\r\n   */\r\n  getTriggeredWorkflowActions(): string[] {\r\n    const actions: string[] = [];\r\n    const transitionType = this.getTransitionType();\r\n    \r\n    switch (this.eventData.newStatus) {\r\n      case VulnerabilityStatus.CONFIRMED:\r\n        actions.push('assign_to_security_team', 'create_risk_assessment');\r\n        break;\r\n      \r\n      case VulnerabilityStatus.TRIAGED:\r\n        actions.push('assign_remediation_owner', 'set_remediation_timeline');\r\n        break;\r\n      \r\n      case VulnerabilityStatus.IN_PROGRESS:\r\n        actions.push('start_remediation_tracking', 'notify_stakeholders');\r\n        break;\r\n      \r\n      case VulnerabilityStatus.REMEDIATED:\r\n        actions.push('schedule_verification', 'update_asset_inventory');\r\n        break;\r\n      \r\n      case VulnerabilityStatus.VERIFIED:\r\n        actions.push('close_vulnerability', 'update_metrics');\r\n        break;\r\n      \r\n      case VulnerabilityStatus.CLOSED:\r\n        actions.push('archive_vulnerability', 'generate_closure_report');\r\n        break;\r\n      \r\n      case VulnerabilityStatus.FALSE_POSITIVE:\r\n        actions.push('update_scanner_rules', 'document_false_positive');\r\n        break;\r\n      \r\n      case VulnerabilityStatus.ACCEPTED_RISK:\r\n        actions.push('document_risk_acceptance', 'schedule_risk_review');\r\n        break;\r\n    }\r\n    \r\n    if (transitionType === 'reopening') {\r\n      actions.push('investigate_reopening_cause', 'reassess_vulnerability');\r\n    }\r\n    \r\n    return actions;\r\n  }\r\n\r\n  /**\r\n   * Get metrics to update based on status change\r\n   */\r\n  getMetricsToUpdate(): Array<{\r\n    metric: string;\r\n    operation: 'increment' | 'decrement' | 'set' | 'timing';\r\n    value?: number;\r\n    tags?: Record<string, string>;\r\n  }> {\r\n    const metrics: Array<{\r\n      metric: string;\r\n      operation: 'increment' | 'decrement' | 'set' | 'timing';\r\n      value?: number;\r\n      tags?: Record<string, string>;\r\n    }> = [];\r\n    \r\n    const baseTags = {\r\n      old_status: this.eventData.oldStatus,\r\n      new_status: this.eventData.newStatus,\r\n      transition_type: this.getTransitionType(),\r\n    };\r\n    \r\n    // Status transition counter\r\n    metrics.push({\r\n      metric: 'vulnerability_status_transitions',\r\n      operation: 'increment',\r\n      tags: baseTags,\r\n    });\r\n    \r\n    // Status-specific metrics\r\n    if (this.wasVulnerabilityClosed()) {\r\n      metrics.push({\r\n        metric: 'vulnerabilities_closed',\r\n        operation: 'increment',\r\n        tags: { status: this.eventData.newStatus },\r\n      });\r\n      \r\n      // Calculate time to closure if we have discovery time\r\n      metrics.push({\r\n        metric: 'vulnerability_closure_time',\r\n        operation: 'timing',\r\n        tags: { status: this.eventData.newStatus },\r\n      });\r\n    }\r\n    \r\n    if (this.wasVulnerabilityDismissed()) {\r\n      metrics.push({\r\n        metric: 'vulnerabilities_dismissed',\r\n        operation: 'increment',\r\n        tags: { reason: this.eventData.newStatus },\r\n      });\r\n    }\r\n    \r\n    if (this.wasVulnerabilityReopened()) {\r\n      metrics.push({\r\n        metric: 'vulnerabilities_reopened',\r\n        operation: 'increment',\r\n        tags: { from_status: this.eventData.oldStatus },\r\n      });\r\n    }\r\n    \r\n    return metrics;\r\n  }\r\n\r\n  /**\r\n   * Get compliance reporting requirements\r\n   */\r\n  getComplianceReportingRequirements(): {\r\n    required: boolean;\r\n    frameworks: string[];\r\n    deadline: 'immediate' | 'within_24h' | 'within_week' | 'next_cycle';\r\n    reportType: 'status_change' | 'closure' | 'exception' | 'none';\r\n  } {\r\n    const transitionType = this.getTransitionType();\r\n    \r\n    if (this.wasVulnerabilityClosed()) {\r\n      return {\r\n        required: true,\r\n        frameworks: ['SOX', 'PCI_DSS', 'HIPAA', 'GDPR'],\r\n        deadline: 'within_week',\r\n        reportType: 'closure',\r\n      };\r\n    }\r\n    \r\n    if (this.wasVulnerabilityDismissed()) {\r\n      return {\r\n        required: true,\r\n        frameworks: ['SOX', 'PCI_DSS'],\r\n        deadline: 'within_week',\r\n        reportType: 'exception',\r\n      };\r\n    }\r\n    \r\n    if (transitionType === 'reopening') {\r\n      return {\r\n        required: true,\r\n        frameworks: ['SOX', 'PCI_DSS', 'HIPAA'],\r\n        deadline: 'within_24h',\r\n        reportType: 'exception',\r\n      };\r\n    }\r\n    \r\n    return {\r\n      required: false,\r\n      frameworks: [],\r\n      deadline: 'next_cycle',\r\n      reportType: 'none',\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      eventType: 'VulnerabilityStatusChanged',\r\n      eventId: this.eventId.toString(),\r\n      aggregateId: this.aggregateId.toString(),\r\n      occurredOn: this.occurredOn.toISOString(),\r\n      version: this.version,\r\n      isDispatched: this.isDispatched,\r\n      timestamp: this.occurredOn.toISOString(),\r\n      data: {\r\n        vulnerabilityId: this.eventData.vulnerabilityId,\r\n        oldStatus: this.eventData.oldStatus,\r\n        newStatus: this.eventData.newStatus,\r\n        reason: this.eventData.reason,\r\n        changedBy: this.eventData.changedBy,\r\n        context: this.eventData.context,\r\n        timestamp: this.eventData.timestamp,\r\n      },\r\n      analysis: {\r\n        transitionType: this.getTransitionType(),\r\n        isProgression: this.isStatusProgression(),\r\n        isRegression: this.isStatusRegression(),\r\n        wasClosed: this.wasVulnerabilityClosed(),\r\n        wasDismissed: this.wasVulnerabilityDismissed(),\r\n        wasReopened: this.wasVulnerabilityReopened(),\r\n        notificationPriority: this.getNotificationPriority(),\r\n        requiredNotifications: this.getRequiredNotifications(),\r\n        triggeredActions: this.getTriggeredWorkflowActions(),\r\n        metricsToUpdate: this.getMetricsToUpdate(),\r\n        complianceRequirements: this.getComplianceReportingRequirements(),\r\n      },\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create from JSON representation\r\n   */\r\n  static fromJSON(json: Record<string, any>): VulnerabilityStatusChangedEvent {\r\n    return new VulnerabilityStatusChangedEvent(\r\n      UniqueEntityId.fromString(json.aggregateId),\r\n      json.data,\r\n      {\r\n        eventId: UniqueEntityId.fromString(json.eventId),\r\n        occurredOn: new Date(json.occurredOn),\r\n        version: json.version,\r\n      }\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get human-readable description\r\n   */\r\n  getDescription(): string {\r\n    const statusText = this.eventData.newStatus.replace(/_/g, ' ');\r\n    const oldStatusText = this.eventData.oldStatus.replace(/_/g, ' ');\r\n    \r\n    return `Vulnerability status changed from \"${oldStatusText}\" to \"${statusText}\": ${this.eventData.reason}`;\r\n  }\r\n\r\n  /**\r\n   * Get event summary for logging\r\n   */\r\n  getEventSummary(): {\r\n    eventType: string;\r\n    vulnerabilityId: string;\r\n    statusTransition: string;\r\n    transitionType: string;\r\n    reason: string;\r\n    timestamp: string;\r\n  } {\r\n    return {\r\n      eventType: 'VulnerabilityStatusChanged',\r\n      vulnerabilityId: this.eventData.vulnerabilityId,\r\n      statusTransition: `${this.eventData.oldStatus} -> ${this.eventData.newStatus}`,\r\n      transitionType: this.getTransitionType(),\r\n      reason: this.eventData.reason,\r\n      timestamp: this.occurredOn.toISOString(),\r\n    };\r\n  }\r\n}"], "version": 3}