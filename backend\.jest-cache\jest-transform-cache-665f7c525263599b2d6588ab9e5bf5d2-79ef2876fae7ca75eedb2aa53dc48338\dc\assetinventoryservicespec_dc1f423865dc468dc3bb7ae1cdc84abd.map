{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\asset-management\\application\\services\\asset-inventory.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,6CAAqD;AAErD,uEAAkE;AAClE,qEAA2D;AAC3D,iFAAsE;AACtE,iGAAsF;AACtF,iGAAsF;AACtF,+FAAoF;AACpF,sFAAkF;AAClF,0FAAsF;AACtF,uGAAmG;AACnG,uEAAkE;AAClE,iFAA4E;AAE5E,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;IACrC,IAAI,OAA8B,CAAC;IACnC,IAAI,eAAkC,CAAC;IACvC,IAAI,oBAA4C,CAAC;IACjD,IAAI,uBAAuD,CAAC;IAC5D,IAAI,uBAAuD,CAAC;IAC5D,IAAI,sBAAqD,CAAC;IAC1D,IAAI,aAA4B,CAAC;IACjC,IAAI,YAA0B,CAAC;IAC/B,IAAI,mBAAwC,CAAC;IAC7C,IAAI,qBAA4C,CAAC;IACjD,IAAI,0BAAsD,CAAC;IAE3D,MAAM,mBAAmB,GAAG;QAC1B,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;KAC9B,CAAC;IAEF,MAAM,wBAAwB,GAAG;QAC/B,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;KAChB,CAAC;IAEF,MAAM,2BAA2B,GAAG;QAClC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;KAChB,CAAC;IAEF,MAAM,2BAA2B,GAAG;QAClC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;KAC9B,CAAC;IAEF,MAAM,0BAA0B,GAAG;QACjC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;KAChB,CAAC;IAEF,MAAM,iBAAiB,GAAG;QACxB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;QACd,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;KACjB,CAAC;IAEF,MAAM,gBAAgB,GAAG;QACvB,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;KACzB,CAAC;IAEF,MAAM,uBAAuB,GAAG;QAC9B,4BAA4B,EAAE,IAAI,CAAC,EAAE,EAAE;KACxC,CAAC;IAEF,MAAM,yBAAyB,GAAG;QAChC,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;KAC9B,CAAC;IAEF,MAAM,8BAA8B,GAAG;QACrC,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;KACzB,CAAC;IAEF,MAAM,gBAAgB,GAAG;QACvB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;QACjC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;QACpC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;QACnC,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;QAC7C,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;QACrC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;QACnC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;QAChC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;QAChC,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE;QAC1B,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;QACnB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;QAClC,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;QACrC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;QACnC,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;QACrB,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;KACrB,CAAC;IAEF,MAAM,SAAS,GAAmB;QAChC,EAAE,EAAE,WAAW;QACf,IAAI,EAAE,aAAa;QACnB,IAAI,EAAE,QAAQ;QACd,MAAM,EAAE,QAAQ;QAChB,WAAW,EAAE,MAAM;QACnB,WAAW,EAAE,YAAY;QACzB,SAAS,EAAE,eAAe;QAC1B,QAAQ,EAAE,gBAAgB;QAC1B,QAAQ,EAAE,eAAe;QACzB,YAAY,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QACpC,QAAQ,EAAE,IAAI,IAAI,EAAE;QACpB,SAAS,EAAE,UAAU;QACrB,IAAI,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;QAClC,QAAQ,EAAE,IAAI;QACd,UAAU,EAAE,IAAI;QAChB,QAAQ,EAAE,IAAI;QACd,SAAS,EAAE,EAAE;QACb,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC;YACpC,EAAE,EAAE,WAAW;YACf,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,QAAQ;YACd,MAAM,EAAE,QAAQ;YAChB,WAAW,EAAE,MAAM;YACnB,QAAQ,EAAE,IAAI;YACd,QAAQ,EAAE,IAAI;SACf,CAAC;QACF,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC;YAC5C,KAAK,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,aAAa,EAAE;YAC/C,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACrC,CAAC;KACH,CAAC;IAEF,MAAM,cAAc,GAAwB;QAC1C,EAAE,EAAE,WAAW;QACf,IAAI,EAAE,oBAAoB;QAC1B,IAAI,EAAE,gBAAgB;QACtB,QAAQ,EAAE,IAAI;QACd,0BAA0B,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;KAC5D,CAAC;IAEF,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,+CAAqB;gBACrB;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,oBAAK,CAAC;oBAClC,QAAQ,EAAE,mBAAmB;iBAC9B;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,+BAAU,CAAC;oBACvC,QAAQ,EAAE,wBAAwB;iBACnC;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,+CAAkB,CAAC;oBAC/C,QAAQ,EAAE,2BAA2B;iBACtC;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,+CAAkB,CAAC;oBAC/C,QAAQ,EAAE,2BAA2B;iBACtC;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,6CAAiB,CAAC;oBAC9C,QAAQ,EAAE,0BAA0B;iBACrC;gBACD;oBACE,OAAO,EAAE,8BAAa;oBACtB,QAAQ,EAAE,iBAAiB;iBAC5B;gBACD;oBACE,OAAO,EAAE,4BAAY;oBACrB,QAAQ,EAAE,gBAAgB;iBAC3B;gBACD;oBACE,OAAO,EAAE,0CAAmB;oBAC5B,QAAQ,EAAE,uBAAuB;iBAClC;gBACD;oBACE,OAAO,EAAE,+CAAqB;oBAC9B,QAAQ,EAAE,yBAAyB;iBACpC;gBACD;oBACE,OAAO,EAAE,yDAA0B;oBACnC,QAAQ,EAAE,8BAA8B;iBACzC;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAAwB,+CAAqB,CAAC,CAAC;QACnE,eAAe,GAAG,MAAM,CAAC,GAAG,CAAoB,IAAA,4BAAkB,EAAC,oBAAK,CAAC,CAAC,CAAC;QAC3E,oBAAoB,GAAG,MAAM,CAAC,GAAG,CAAyB,IAAA,4BAAkB,EAAC,+BAAU,CAAC,CAAC,CAAC;QAC1F,uBAAuB,GAAG,MAAM,CAAC,GAAG,CAAiC,IAAA,4BAAkB,EAAC,+CAAkB,CAAC,CAAC,CAAC;QAC7G,uBAAuB,GAAG,MAAM,CAAC,GAAG,CAAiC,IAAA,4BAAkB,EAAC,+CAAkB,CAAC,CAAC,CAAC;QAC7G,sBAAsB,GAAG,MAAM,CAAC,GAAG,CAAgC,IAAA,4BAAkB,EAAC,6CAAiB,CAAC,CAAC,CAAC;QAC1G,aAAa,GAAG,MAAM,CAAC,GAAG,CAAgB,8BAAa,CAAC,CAAC;QACzD,YAAY,GAAG,MAAM,CAAC,GAAG,CAAe,4BAAY,CAAC,CAAC;QACtD,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAsB,0CAAmB,CAAC,CAAC;QAC3E,qBAAqB,GAAG,MAAM,CAAC,GAAG,CAAwB,+CAAqB,CAAC,CAAC;QACjF,0BAA0B,GAAG,MAAM,CAAC,GAAG,CAA6B,yDAA0B,CAAC,CAAC;QAEhG,4BAA4B;QAC5B,mBAAmB,CAAC,kBAAkB,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;QACzE,2BAA2B,CAAC,kBAAkB,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;IACnF,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,4BAA4B;YAC5B,mBAAmB,CAAC,KAAK,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,cAAc;YAChE,gBAAgB,CAAC,QAAQ;iBACtB,qBAAqB,CAAC,EAAE,CAAC,CAAC,eAAe;iBACzC,qBAAqB,CAAC,EAAE,CAAC,CAAC,mBAAmB;iBAC7C,qBAAqB,CAAC,EAAE,CAAC,CAAC,CAAC,mBAAmB;YAEjD,gBAAgB,CAAC,UAAU;iBACxB,qBAAqB,CAAC;gBACrB,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE;gBAC/B,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE;gBACpC,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,IAAI,EAAE;aACxC,CAAC,CAAC,eAAe;iBACjB,qBAAqB,CAAC;gBACrB,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE;gBACjC,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE;gBACnC,EAAE,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,GAAG,EAAE;aACtC,CAAC,CAAC,iBAAiB;iBACnB,qBAAqB,CAAC;gBACrB,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE;gBACxC,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE;gBACpC,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE;gBACtC,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE;aACpC,CAAC,CAAC,sBAAsB;iBACxB,qBAAqB,CAAC;gBACrB,EAAE,WAAW,EAAE,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE;gBAC1C,EAAE,WAAW,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE;gBACvC,EAAE,WAAW,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE;aAC5C,CAAC,CAAC,CAAC,sBAAsB;YAE5B,mBAAmB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,qBAAqB;YAE9E,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,qBAAqB,EAAE,CAAC;YAErD,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC;gBAC7B,WAAW,EAAE,GAAG;gBAChB,YAAY,EAAE,EAAE;gBAChB,aAAa,EAAE,EAAE;gBACjB,gBAAgB,EAAE,EAAE;gBACpB,gBAAgB,EAAE,EAAE;gBACpB,aAAa,EAAE,EAAE;gBACjB,iBAAiB,EAAE,EAAE;aACtB,CAAC,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACtC,MAAM,EAAE,EAAE;gBACV,WAAW,EAAE,EAAE;gBACf,cAAc,EAAE,EAAE;aACnB,CAAC,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,mBAAmB,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAEzE,MAAM,MAAM,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YAChF,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,oBAAoB,CAClD,8CAA8C,EAC9C,MAAM,CAAC,gBAAgB,CAAC;gBACtB,KAAK,EAAE,gBAAgB;aACxB,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,QAAQ,GAAG;gBACf,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,EAAE;gBACT,KAAK,EAAE,CAAC,QAAQ,CAAC;gBACjB,QAAQ,EAAE,CAAC,QAAQ,CAAC;gBACpB,aAAa,EAAE,CAAC,MAAM,CAAC;gBACvB,YAAY,EAAE,CAAC,YAAY,CAAC;gBAC5B,UAAU,EAAE,MAAM;aACnB,CAAC;YAEF,MAAM,MAAM,GAAG,CAAC,SAAS,CAAC,CAAC;YAC3B,MAAM,KAAK,GAAG,CAAC,CAAC;YAEhB,gBAAgB,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;YAEpE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAEpD,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,MAAM;gBACN,KAAK;gBACL,IAAI,EAAE,CAAC;gBACP,UAAU,EAAE,CAAC;aACd,CAAC,CAAC;YAEH,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,2BAA2B,EAAE,EAAE,KAAK,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAC3G,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,gCAAgC,EAAE,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACnH,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,0CAA0C,EAAE,EAAE,aAAa,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAChI,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,yCAAyC,EAAE,EAAE,YAAY,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YACpI,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CACpD,gJAAgJ,EAChJ,EAAE,UAAU,EAAE,QAAQ,EAAE,CACzB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,QAAQ,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;YACxC,MAAM,MAAM,GAAG,CAAC,SAAS,CAAC,CAAC;YAC3B,MAAM,KAAK,GAAG,EAAE,CAAC;YAEjB,gBAAgB,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;YAEpE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAEpD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;YACvD,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,QAAQ,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;YACpC,gBAAgB,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YAE5D,MAAM,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAErC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CACpD,kCAAkC,EAClC,MAAM,CAAC,gBAAgB,CAAC,EAAE,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAC9D,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,MAAM,QAAQ,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;YACpC,gBAAgB,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YAE5D,MAAM,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAErC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,6CAA6C,CAAC,CAAC;QACxG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,MAAM,SAAS,GAAG;YAChB,IAAI,EAAE,YAAY;YAClB,IAAI,EAAE,QAAQ;YACd,WAAW,EAAE,MAAe;YAC5B,WAAW,EAAE,YAAqB;YAClC,SAAS,EAAE,eAAe;YAC1B,QAAQ,EAAE,eAAe;YACzB,IAAI,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC;SACxB,CAAC;QAEF,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,MAAM,GAAG,UAAU,CAAC;YAC1B,MAAM,UAAU,GAAG,EAAE,GAAG,SAAS,EAAE,EAAE,EAAE,WAAW,EAAE,CAAC;YAErD,mBAAmB,CAAC,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YACtD,mBAAmB,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACvD,8BAA8B,CAAC,aAAa,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAE1E,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,WAAW,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAE5D,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC;gBACtD,GAAG,SAAS;gBACZ,MAAM,EAAE,SAAS;gBACjB,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;gBAC9B,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;gBAC1B,SAAS,EAAE,MAAM;gBACjB,WAAW,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC;gBAClC,IAAI,EAAE,SAAS,CAAC,IAAI;aACrB,CAAC,CAAC;YACH,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;YACjE,MAAM,CAAC,8BAA8B,CAAC,aAAa,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YACzF,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,MAAM,EACN,QAAQ,EACR,OAAO,EACP,UAAU,CAAC,EAAE,EACb,MAAM,CAAC,gBAAgB,CAAC;gBACtB,SAAS,EAAE,SAAS,CAAC,IAAI;gBACzB,SAAS,EAAE,SAAS,CAAC,IAAI;aAC1B,CAAC,CACH,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,kBAAkB,GAAG,EAAE,GAAG,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;YAClE,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,wBAAwB,CAAC,OAAO,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YACnE,mBAAmB,CAAC,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YACtD,mBAAmB,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACtD,8BAA8B,CAAC,aAAa,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAE1E,MAAM,OAAO,CAAC,WAAW,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;YAEtD,MAAM,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBAC5D,KAAK,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE;aAC3C,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,kBAAkB,GAAG,EAAE,GAAG,SAAS,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC;YACrE,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,wBAAwB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAEzD,MAAM,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;iBAC1D,OAAO,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,MAAM,OAAO,GAAG;YACd,IAAI,EAAE,gBAAgB;YACtB,WAAW,EAAE,UAAmB;SACjC,CAAC;QAEF,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,OAAO,GAAG,WAAW,CAAC;YAC5B,MAAM,MAAM,GAAG,UAAU,CAAC;YAC1B,MAAM,KAAK,GAAG,EAAE,GAAG,SAAS,EAAE,CAAC;YAE/B,mBAAmB,CAAC,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YACrD,mBAAmB,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,GAAG,KAAK,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC;YAErE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YAEnE,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBACvD,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE;gBACtB,SAAS,EAAE,CAAC,OAAO,CAAC;aACrB,CAAC,CAAC;YACH,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC;gBACpD,GAAG,KAAK;gBACR,GAAG,OAAO;gBACV,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;YACH,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,MAAM,EACN,QAAQ,EACR,OAAO,EACP,OAAO,EACP,MAAM,CAAC,gBAAgB,CAAC;gBACtB,SAAS,EAAE,KAAK,CAAC,IAAI;gBACrB,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC5B,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,OAAO,GAAG,cAAc,CAAC;YAC/B,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,mBAAmB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAEpD,MAAM,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;iBACxD,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,OAAO,GAAG,WAAW,CAAC;YAC5B,MAAM,MAAM,GAAG,UAAU,CAAC;YAC1B,MAAM,KAAK,GAAG,EAAE,GAAG,SAAS,EAAE,CAAC;YAE/B,mBAAmB,CAAC,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YACrD,mBAAmB,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAEpD,MAAM,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAE3C,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBACvD,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE;aACvB,CAAC,CAAC;YACH,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;YAC/D,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,MAAM,EACN,QAAQ,EACR,OAAO,EACP,OAAO,EACP,MAAM,CAAC,gBAAgB,CAAC;gBACtB,SAAS,EAAE,KAAK,CAAC,IAAI;gBACrB,SAAS,EAAE,KAAK,CAAC,IAAI;aACtB,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,OAAO,GAAG,cAAc,CAAC;YAC/B,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,mBAAmB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAEpD,MAAM,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;iBAC/C,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,OAAO,GAAG,WAAW,CAAC;YAC5B,MAAM,KAAK,GAAG,EAAE,GAAG,SAAS,EAAE,CAAC;YAE/B,mBAAmB,CAAC,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAErD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAEtD,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBACvD,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE;gBACtB,SAAS,EAAE;oBACT,OAAO;oBACP,gBAAgB;oBAChB,iBAAiB;oBACjB,qBAAqB;oBACrB,qBAAqB;oBACrB,cAAc;oBACd,eAAe;iBAChB;aACF,CAAC,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,OAAO,GAAG,cAAc,CAAC;YAE/B,mBAAmB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAEpD,MAAM,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;iBAC3C,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,OAAO,GAAG,WAAW,CAAC;YAE5B,mBAAmB,CAAC,MAAM,CAAC,iBAAiB,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;YAE9D,MAAM,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAEtC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,OAAO,EAAE;gBAC/D,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;aAC3B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,QAAQ,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;YACnD,MAAM,MAAM,GAAG,aAAa,CAAC;YAC7B,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,mBAAmB,CAAC,MAAM,CAAC,iBAAiB,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;YAE9D,MAAM,OAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;YAEzD,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CACrD,EAAE,EAAE,EAAE,MAAM,CAAC,QAAQ,EAAE,EAAE,EACzB,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,CAC9B,CAAC;YACF,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,MAAM,EACN,aAAa,EACb,OAAO,EACP,IAAI,EACJ,MAAM,CAAC,gBAAgB,CAAC;gBACtB,QAAQ;gBACR,MAAM;gBACN,UAAU,EAAE,CAAC;aACd,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,sBAAsB;YACtB,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,oBAAoB,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAC9E,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,0BAA0B,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACpF,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,yBAAyB,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACnF,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,2BAA2B,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAErF,MAAM,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAEjC,MAAM,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAAC,gCAAgC,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,oBAAoB,CAAC,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC;YAElG,MAAM,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAEjC,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,oBAAoB,CAClD,yCAAyC,EACzC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,KAAK,EAAE,kBAAkB;aAC1B,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\asset-management\\application\\services\\asset-inventory.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { getRepositoryToken } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\nimport { AssetInventoryService } from './asset-inventory.service';\r\nimport { Asset } from '../../domain/entities/asset.entity';\r\nimport { AssetGroup } from '../../domain/entities/asset-group.entity';\r\nimport { AssetConfiguration } from '../../domain/entities/asset-configuration.entity';\r\nimport { AssetVulnerability } from '../../domain/entities/asset-vulnerability.entity';\r\nimport { AssetRelationship } from '../../domain/entities/asset-relationship.entity';\r\nimport { LoggerService } from '../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../infrastructure/logging/audit/audit.service';\r\nimport { NotificationService } from '../../../../infrastructure/notification/notification.service';\r\nimport { AssetDiscoveryService } from './asset-discovery.service';\r\nimport { AssetClassificationService } from './asset-classification.service';\r\n\r\ndescribe('AssetInventoryService', () => {\r\n  let service: AssetInventoryService;\r\n  let assetRepository: Repository<Asset>;\r\n  let assetGroupRepository: Repository<AssetGroup>;\r\n  let configurationRepository: Repository<AssetConfiguration>;\r\n  let vulnerabilityRepository: Repository<AssetVulnerability>;\r\n  let relationshipRepository: Repository<AssetRelationship>;\r\n  let loggerService: LoggerService;\r\n  let auditService: AuditService;\r\n  let notificationService: NotificationService;\r\n  let assetDiscoveryService: AssetDiscoveryService;\r\n  let assetClassificationService: AssetClassificationService;\r\n\r\n  const mockAssetRepository = {\r\n    find: jest.fn(),\r\n    findOne: jest.fn(),\r\n    create: jest.fn(),\r\n    save: jest.fn(),\r\n    remove: jest.fn(),\r\n    count: jest.fn(),\r\n    update: jest.fn(),\r\n    createQueryBuilder: jest.fn(),\r\n  };\r\n\r\n  const mockAssetGroupRepository = {\r\n    find: jest.fn(),\r\n    findOne: jest.fn(),\r\n    save: jest.fn(),\r\n  };\r\n\r\n  const mockConfigurationRepository = {\r\n    find: jest.fn(),\r\n    findOne: jest.fn(),\r\n    save: jest.fn(),\r\n  };\r\n\r\n  const mockVulnerabilityRepository = {\r\n    find: jest.fn(),\r\n    count: jest.fn(),\r\n    createQueryBuilder: jest.fn(),\r\n  };\r\n\r\n  const mockRelationshipRepository = {\r\n    find: jest.fn(),\r\n    findOne: jest.fn(),\r\n    save: jest.fn(),\r\n  };\r\n\r\n  const mockLoggerService = {\r\n    debug: jest.fn(),\r\n    log: jest.fn(),\r\n    warn: jest.fn(),\r\n    error: jest.fn(),\r\n  };\r\n\r\n  const mockAuditService = {\r\n    logUserAction: jest.fn(),\r\n  };\r\n\r\n  const mockNotificationService = {\r\n    sendAssetOfflineNotification: jest.fn(),\r\n  };\r\n\r\n  const mockAssetDiscoveryService = {\r\n    scheduledDiscovery: jest.fn(),\r\n  };\r\n\r\n  const mockAssetClassificationService = {\r\n    classifyAsset: jest.fn(),\r\n  };\r\n\r\n  const mockQueryBuilder = {\r\n    where: jest.fn().mockReturnThis(),\r\n    andWhere: jest.fn().mockReturnThis(),\r\n    orWhere: jest.fn().mockReturnThis(),\r\n    leftJoinAndSelect: jest.fn().mockReturnThis(),\r\n    innerJoin: jest.fn().mockReturnThis(),\r\n    orderBy: jest.fn().mockReturnThis(),\r\n    skip: jest.fn().mockReturnThis(),\r\n    take: jest.fn().mockReturnThis(),\r\n    getManyAndCount: jest.fn(),\r\n    getMany: jest.fn(),\r\n    getCount: jest.fn(),\r\n    select: jest.fn().mockReturnThis(),\r\n    addSelect: jest.fn().mockReturnThis(),\r\n    groupBy: jest.fn().mockReturnThis(),\r\n    getRawMany: jest.fn(),\r\n    getRawOne: jest.fn(),\r\n  };\r\n\r\n  const mockAsset: Partial<Asset> = {\r\n    id: 'asset-123',\r\n    name: 'Test Server',\r\n    type: 'server',\r\n    status: 'active',\r\n    criticality: 'high',\r\n    environment: 'production',\r\n    ipAddress: '*************',\r\n    hostname: 'test-server-01',\r\n    location: 'Data Center 1',\r\n    discoveredAt: new Date('2023-01-01'),\r\n    lastSeen: new Date(),\r\n    createdBy: 'user-123',\r\n    tags: ['web-server', 'production'],\r\n    isOnline: true,\r\n    isCritical: true,\r\n    hasAgent: true,\r\n    ageInDays: 30,\r\n    getSummary: jest.fn().mockReturnValue({\r\n      id: 'asset-123',\r\n      name: 'Test Server',\r\n      type: 'server',\r\n      status: 'active',\r\n      criticality: 'high',\r\n      isOnline: true,\r\n      hasAgent: true,\r\n    }),\r\n    exportForReporting: jest.fn().mockReturnValue({\r\n      asset: { id: 'asset-123', name: 'Test Server' },\r\n      exportedAt: new Date().toISOString(),\r\n    }),\r\n  };\r\n\r\n  const mockAssetGroup: Partial<AssetGroup> = {\r\n    id: 'group-123',\r\n    name: 'Production Servers',\r\n    type: 'organizational',\r\n    isActive: true,\r\n    matchesAutoAssignmentRules: jest.fn().mockReturnValue(true),\r\n  };\r\n\r\n  beforeEach(async () => {\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        AssetInventoryService,\r\n        {\r\n          provide: getRepositoryToken(Asset),\r\n          useValue: mockAssetRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(AssetGroup),\r\n          useValue: mockAssetGroupRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(AssetConfiguration),\r\n          useValue: mockConfigurationRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(AssetVulnerability),\r\n          useValue: mockVulnerabilityRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(AssetRelationship),\r\n          useValue: mockRelationshipRepository,\r\n        },\r\n        {\r\n          provide: LoggerService,\r\n          useValue: mockLoggerService,\r\n        },\r\n        {\r\n          provide: AuditService,\r\n          useValue: mockAuditService,\r\n        },\r\n        {\r\n          provide: NotificationService,\r\n          useValue: mockNotificationService,\r\n        },\r\n        {\r\n          provide: AssetDiscoveryService,\r\n          useValue: mockAssetDiscoveryService,\r\n        },\r\n        {\r\n          provide: AssetClassificationService,\r\n          useValue: mockAssetClassificationService,\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    service = module.get<AssetInventoryService>(AssetInventoryService);\r\n    assetRepository = module.get<Repository<Asset>>(getRepositoryToken(Asset));\r\n    assetGroupRepository = module.get<Repository<AssetGroup>>(getRepositoryToken(AssetGroup));\r\n    configurationRepository = module.get<Repository<AssetConfiguration>>(getRepositoryToken(AssetConfiguration));\r\n    vulnerabilityRepository = module.get<Repository<AssetVulnerability>>(getRepositoryToken(AssetVulnerability));\r\n    relationshipRepository = module.get<Repository<AssetRelationship>>(getRepositoryToken(AssetRelationship));\r\n    loggerService = module.get<LoggerService>(LoggerService);\r\n    auditService = module.get<AuditService>(AuditService);\r\n    notificationService = module.get<NotificationService>(NotificationService);\r\n    assetDiscoveryService = module.get<AssetDiscoveryService>(AssetDiscoveryService);\r\n    assetClassificationService = module.get<AssetClassificationService>(AssetClassificationService);\r\n\r\n    // Setup query builder mocks\r\n    mockAssetRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);\r\n    mockVulnerabilityRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);\r\n  });\r\n\r\n  afterEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('getInventoryDashboard', () => {\r\n    it('should return inventory dashboard data', async () => {\r\n      // Mock repository responses\r\n      mockAssetRepository.count.mockResolvedValue(100); // totalAssets\r\n      mockQueryBuilder.getCount\r\n        .mockResolvedValueOnce(80) // onlineAssets\r\n        .mockResolvedValueOnce(60) // assetsWithAgents\r\n        .mockResolvedValueOnce(25); // vulnerableAssets\r\n\r\n      mockQueryBuilder.getRawMany\r\n        .mockResolvedValueOnce([\r\n          { type: 'server', count: '50' },\r\n          { type: 'workstation', count: '30' },\r\n          { type: 'network_device', count: '20' },\r\n        ]) // assetsByType\r\n        .mockResolvedValueOnce([\r\n          { status: 'active', count: '80' },\r\n          { status: 'inactive', count: '15' },\r\n          { status: 'maintenance', count: '5' },\r\n        ]) // assetsByStatus\r\n        .mockResolvedValueOnce([\r\n          { criticality: 'critical', count: '10' },\r\n          { criticality: 'high', count: '30' },\r\n          { criticality: 'medium', count: '40' },\r\n          { criticality: 'low', count: '20' },\r\n        ]) // assetsByCriticality\r\n        .mockResolvedValueOnce([\r\n          { environment: 'production', count: '60' },\r\n          { environment: 'staging', count: '20' },\r\n          { environment: 'development', count: '20' },\r\n        ]); // assetsByEnvironment\r\n\r\n      mockAssetRepository.find.mockResolvedValue([mockAsset]); // recentlyDiscovered\r\n\r\n      const result = await service.getInventoryDashboard();\r\n\r\n      expect(result).toHaveProperty('summary');\r\n      expect(result.summary).toEqual({\r\n        totalAssets: 100,\r\n        onlineAssets: 80,\r\n        offlineAssets: 20,\r\n        assetsWithAgents: 60,\r\n        vulnerableAssets: 25,\r\n        agentCoverage: 60,\r\n        vulnerabilityRate: 25,\r\n      });\r\n      expect(result).toHaveProperty('breakdown');\r\n      expect(result.breakdown.byType).toEqual({\r\n        server: 50,\r\n        workstation: 30,\r\n        network_device: 20,\r\n      });\r\n      expect(result).toHaveProperty('recentActivity');\r\n      expect(result).toHaveProperty('timestamp');\r\n    });\r\n\r\n    it('should handle errors gracefully', async () => {\r\n      mockAssetRepository.count.mockRejectedValue(new Error('Database error'));\r\n\r\n      await expect(service.getInventoryDashboard()).rejects.toThrow('Database error');\r\n      expect(mockLoggerService.error).toHaveBeenCalledWith(\r\n        'Failed to generate asset inventory dashboard',\r\n        expect.objectContaining({\r\n          error: 'Database error',\r\n        }),\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('searchAssets', () => {\r\n    it('should search assets with filters', async () => {\r\n      const criteria = {\r\n        page: 1,\r\n        limit: 50,\r\n        types: ['server'],\r\n        statuses: ['active'],\r\n        criticalities: ['high'],\r\n        environments: ['production'],\r\n        searchText: 'test',\r\n      };\r\n\r\n      const assets = [mockAsset];\r\n      const total = 1;\r\n\r\n      mockQueryBuilder.getManyAndCount.mockResolvedValue([assets, total]);\r\n\r\n      const result = await service.searchAssets(criteria);\r\n\r\n      expect(result).toEqual({\r\n        assets,\r\n        total,\r\n        page: 1,\r\n        totalPages: 1,\r\n      });\r\n\r\n      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('asset.type IN (:...types)', { types: ['server'] });\r\n      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('asset.status IN (:...statuses)', { statuses: ['active'] });\r\n      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('asset.criticality IN (:...criticalities)', { criticalities: ['high'] });\r\n      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('asset.environment IN (:...environments)', { environments: ['production'] });\r\n      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(\r\n        '(asset.name ILIKE :searchText OR asset.hostname ILIKE :searchText OR asset.ipAddress ILIKE :searchText OR asset.description ILIKE :searchText)',\r\n        { searchText: '%test%' },\r\n      );\r\n    });\r\n\r\n    it('should handle pagination correctly', async () => {\r\n      const criteria = { page: 2, limit: 25 };\r\n      const assets = [mockAsset];\r\n      const total = 50;\r\n\r\n      mockQueryBuilder.getManyAndCount.mockResolvedValue([assets, total]);\r\n\r\n      const result = await service.searchAssets(criteria);\r\n\r\n      expect(result.totalPages).toBe(2);\r\n      expect(mockQueryBuilder.skip).toHaveBeenCalledWith(25);\r\n      expect(mockQueryBuilder.take).toHaveBeenCalledWith(25);\r\n    });\r\n\r\n    it('should handle online/offline filtering', async () => {\r\n      const criteria = { isOnline: true };\r\n      mockQueryBuilder.getManyAndCount.mockResolvedValue([[], 0]);\r\n\r\n      await service.searchAssets(criteria);\r\n\r\n      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(\r\n        'asset.lastSeen > :fiveMinutesAgo',\r\n        expect.objectContaining({ fiveMinutesAgo: expect.any(Date) }),\r\n      );\r\n    });\r\n\r\n    it('should handle agent filtering', async () => {\r\n      const criteria = { hasAgent: true };\r\n      mockQueryBuilder.getManyAndCount.mockResolvedValue([[], 0]);\r\n\r\n      await service.searchAssets(criteria);\r\n\r\n      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(\"asset.discovery->>'agentInstalled' = 'true'\");\r\n    });\r\n  });\r\n\r\n  describe('createAsset', () => {\r\n    const assetData = {\r\n      name: 'New Server',\r\n      type: 'server',\r\n      criticality: 'high' as const,\r\n      environment: 'production' as const,\r\n      ipAddress: '*************',\r\n      hostname: 'new-server-01',\r\n      tags: ['new', 'server'],\r\n    };\r\n\r\n    it('should create asset successfully', async () => {\r\n      const userId = 'user-123';\r\n      const savedAsset = { ...mockAsset, id: 'asset-456' };\r\n\r\n      mockAssetRepository.create.mockReturnValue(mockAsset);\r\n      mockAssetRepository.save.mockResolvedValue(savedAsset);\r\n      mockAssetClassificationService.classifyAsset.mockResolvedValue(undefined);\r\n\r\n      const result = await service.createAsset(assetData, userId);\r\n\r\n      expect(mockAssetRepository.create).toHaveBeenCalledWith({\r\n        ...assetData,\r\n        status: 'unknown',\r\n        discoveredAt: expect.any(Date),\r\n        lastSeen: expect.any(Date),\r\n        createdBy: userId,\r\n        ipAddresses: [assetData.ipAddress],\r\n        tags: assetData.tags,\r\n      });\r\n      expect(mockAssetRepository.save).toHaveBeenCalledWith(mockAsset);\r\n      expect(mockAssetClassificationService.classifyAsset).toHaveBeenCalledWith(savedAsset.id);\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        userId,\r\n        'create',\r\n        'asset',\r\n        savedAsset.id,\r\n        expect.objectContaining({\r\n          assetName: assetData.name,\r\n          assetType: assetData.type,\r\n        }),\r\n      );\r\n      expect(result).toEqual(savedAsset);\r\n    });\r\n\r\n    it('should validate group if specified', async () => {\r\n      const assetDataWithGroup = { ...assetData, groupId: 'group-123' };\r\n      const userId = 'user-123';\r\n\r\n      mockAssetGroupRepository.findOne.mockResolvedValue(mockAssetGroup);\r\n      mockAssetRepository.create.mockReturnValue(mockAsset);\r\n      mockAssetRepository.save.mockResolvedValue(mockAsset);\r\n      mockAssetClassificationService.classifyAsset.mockResolvedValue(undefined);\r\n\r\n      await service.createAsset(assetDataWithGroup, userId);\r\n\r\n      expect(mockAssetGroupRepository.findOne).toHaveBeenCalledWith({\r\n        where: { id: 'group-123', isActive: true },\r\n      });\r\n    });\r\n\r\n    it('should throw error when group not found', async () => {\r\n      const assetDataWithGroup = { ...assetData, groupId: 'non-existent' };\r\n      const userId = 'user-123';\r\n\r\n      mockAssetGroupRepository.findOne.mockResolvedValue(null);\r\n\r\n      await expect(service.createAsset(assetDataWithGroup, userId))\r\n        .rejects.toThrow('Asset group not found or inactive');\r\n    });\r\n  });\r\n\r\n  describe('updateAsset', () => {\r\n    const updates = {\r\n      name: 'Updated Server',\r\n      criticality: 'critical' as const,\r\n    };\r\n\r\n    it('should update asset successfully', async () => {\r\n      const assetId = 'asset-123';\r\n      const userId = 'user-123';\r\n      const asset = { ...mockAsset };\r\n\r\n      mockAssetRepository.findOne.mockResolvedValue(asset);\r\n      mockAssetRepository.save.mockResolvedValue({ ...asset, ...updates });\r\n\r\n      const result = await service.updateAsset(assetId, updates, userId);\r\n\r\n      expect(mockAssetRepository.findOne).toHaveBeenCalledWith({\r\n        where: { id: assetId },\r\n        relations: ['group'],\r\n      });\r\n      expect(mockAssetRepository.save).toHaveBeenCalledWith({\r\n        ...asset,\r\n        ...updates,\r\n        updatedBy: userId,\r\n      });\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        userId,\r\n        'update',\r\n        'asset',\r\n        assetId,\r\n        expect.objectContaining({\r\n          assetName: asset.name,\r\n          changes: expect.any(Object),\r\n        }),\r\n      );\r\n    });\r\n\r\n    it('should throw error when asset not found', async () => {\r\n      const assetId = 'non-existent';\r\n      const userId = 'user-123';\r\n\r\n      mockAssetRepository.findOne.mockResolvedValue(null);\r\n\r\n      await expect(service.updateAsset(assetId, updates, userId))\r\n        .rejects.toThrow('Asset not found');\r\n    });\r\n  });\r\n\r\n  describe('deleteAsset', () => {\r\n    it('should delete asset successfully', async () => {\r\n      const assetId = 'asset-123';\r\n      const userId = 'user-123';\r\n      const asset = { ...mockAsset };\r\n\r\n      mockAssetRepository.findOne.mockResolvedValue(asset);\r\n      mockAssetRepository.remove.mockResolvedValue(asset);\r\n\r\n      await service.deleteAsset(assetId, userId);\r\n\r\n      expect(mockAssetRepository.findOne).toHaveBeenCalledWith({\r\n        where: { id: assetId },\r\n      });\r\n      expect(mockAssetRepository.remove).toHaveBeenCalledWith(asset);\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        userId,\r\n        'delete',\r\n        'asset',\r\n        assetId,\r\n        expect.objectContaining({\r\n          assetName: asset.name,\r\n          assetType: asset.type,\r\n        }),\r\n      );\r\n    });\r\n\r\n    it('should throw error when asset not found', async () => {\r\n      const assetId = 'non-existent';\r\n      const userId = 'user-123';\r\n\r\n      mockAssetRepository.findOne.mockResolvedValue(null);\r\n\r\n      await expect(service.deleteAsset(assetId, userId))\r\n        .rejects.toThrow('Asset not found');\r\n    });\r\n  });\r\n\r\n  describe('getAssetDetails', () => {\r\n    it('should return asset with full details', async () => {\r\n      const assetId = 'asset-123';\r\n      const asset = { ...mockAsset };\r\n\r\n      mockAssetRepository.findOne.mockResolvedValue(asset);\r\n\r\n      const result = await service.getAssetDetails(assetId);\r\n\r\n      expect(mockAssetRepository.findOne).toHaveBeenCalledWith({\r\n        where: { id: assetId },\r\n        relations: [\r\n          'group',\r\n          'configurations',\r\n          'vulnerabilities',\r\n          'sourceRelationships',\r\n          'targetRelationships',\r\n          'dependencies',\r\n          'relatedAssets',\r\n        ],\r\n      });\r\n      expect(result).toEqual(asset);\r\n    });\r\n\r\n    it('should throw error when asset not found', async () => {\r\n      const assetId = 'non-existent';\r\n\r\n      mockAssetRepository.findOne.mockResolvedValue(null);\r\n\r\n      await expect(service.getAssetDetails(assetId))\r\n        .rejects.toThrow('Asset not found');\r\n    });\r\n  });\r\n\r\n  describe('updateLastSeen', () => {\r\n    it('should update asset last seen timestamp', async () => {\r\n      const assetId = 'asset-123';\r\n\r\n      mockAssetRepository.update.mockResolvedValue({ affected: 1 });\r\n\r\n      await service.updateLastSeen(assetId);\r\n\r\n      expect(mockAssetRepository.update).toHaveBeenCalledWith(assetId, {\r\n        lastSeen: expect.any(Date),\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('bulkUpdateStatus', () => {\r\n    it('should update multiple asset statuses', async () => {\r\n      const assetIds = ['asset-1', 'asset-2', 'asset-3'];\r\n      const status = 'maintenance';\r\n      const userId = 'user-123';\r\n\r\n      mockAssetRepository.update.mockResolvedValue({ affected: 3 });\r\n\r\n      await service.bulkUpdateStatus(assetIds, status, userId);\r\n\r\n      expect(mockAssetRepository.update).toHaveBeenCalledWith(\r\n        { id: expect.anything() },\r\n        { status, updatedBy: userId },\r\n      );\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        userId,\r\n        'bulk_update',\r\n        'asset',\r\n        null,\r\n        expect.objectContaining({\r\n          assetIds,\r\n          status,\r\n          assetCount: 3,\r\n        }),\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('monitorInventory', () => {\r\n    it('should complete monitoring cycle', async () => {\r\n      // Mock helper methods\r\n      jest.spyOn(service as any, 'checkOfflineAssets').mockResolvedValue(undefined);\r\n      jest.spyOn(service as any, 'checkAssetsWithoutAgents').mockResolvedValue(undefined);\r\n      jest.spyOn(service as any, 'checkUnclassifiedAssets').mockResolvedValue(undefined);\r\n      jest.spyOn(service as any, 'updateInventoryStatistics').mockResolvedValue(undefined);\r\n\r\n      await service.monitorInventory();\r\n\r\n      expect(mockLoggerService.log).toHaveBeenCalledWith('Inventory monitoring completed');\r\n    });\r\n\r\n    it('should handle monitoring errors gracefully', async () => {\r\n      jest.spyOn(service as any, 'checkOfflineAssets').mockRejectedValue(new Error('Monitoring error'));\r\n\r\n      await service.monitorInventory();\r\n\r\n      expect(mockLoggerService.error).toHaveBeenCalledWith(\r\n        'Failed to complete inventory monitoring',\r\n        expect.objectContaining({\r\n          error: 'Monitoring error',\r\n        }),\r\n      );\r\n    });\r\n  });\r\n});\r\n"], "version": 3}