321e579d1efd3865be6c62323b222c97
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const event_emitter_1 = require("@nestjs/event-emitter");
const bull_1 = require("@nestjs/bull");
const typeorm_2 = require("@nestjs/typeorm");
const notification_workflow_service_1 = require("../../services/notification-workflow.service");
const notification_queue_management_service_1 = require("../../services/notification-queue-management.service");
const notification_analytics_service_1 = require("../../services/notification-analytics.service");
const provider_health_monitoring_service_1 = require("../../services/provider-health-monitoring.service");
const email_notification_provider_1 = require("../../providers/email-notification.provider");
const slack_notification_provider_1 = require("../../providers/slack-notification.provider");
const notification_workflow_entity_1 = require("../../entities/notification-workflow.entity");
const workflow_execution_entity_1 = require("../../entities/workflow-execution.entity");
const notification_analytics_event_entity_1 = require("../../entities/notification-analytics-event.entity");
/**
 * Performance and Load Testing Suite
 *
 * Comprehensive performance and load tests for notification infrastructure including:
 * - High-volume workflow execution performance testing
 * - Notification provider throughput and latency testing
 * - Queue management performance under load
 * - Database performance with large datasets
 * - Memory usage and resource optimization testing
 * - Concurrent user and system load testing
 */
describe('Performance and Load Testing Suite', () => {
    let app;
    let workflowService;
    let queueService;
    let analyticsService;
    let healthService;
    let emailProvider;
    let slackProvider;
    let workflowRepository;
    let executionRepository;
    let analyticsRepository;
    const testUser = {
        id: 'perf-test-user',
        email: '<EMAIL>',
        role: 'admin',
        team: 'performance',
    };
    // Performance thresholds
    const PERFORMANCE_THRESHOLDS = {
        WORKFLOW_EXECUTION_TIME: 5000, // 5 seconds max
        NOTIFICATION_DELIVERY_TIME: 3000, // 3 seconds max
        QUEUE_PROCESSING_TIME: 1000, // 1 second max
        DATABASE_QUERY_TIME: 500, // 500ms max
        MEMORY_USAGE_MB: 512, // 512MB max
        CPU_USAGE_PERCENT: 80, // 80% max
        CONCURRENT_WORKFLOWS: 100, // 100 concurrent workflows
        NOTIFICATIONS_PER_SECOND: 50, // 50 notifications/second
    };
    beforeAll(async () => {
        const moduleFixture = await testing_1.Test.createTestingModule({
            imports: [
                config_1.ConfigModule.forRoot({
                    isGlobal: true,
                    envFilePath: '.env.test',
                }),
                typeorm_1.TypeOrmModule.forRoot({
                    type: 'postgres',
                    host: process.env.TEST_DB_HOST || 'localhost',
                    port: parseInt(process.env.TEST_DB_PORT) || 5433,
                    username: process.env.TEST_DB_USERNAME || 'test',
                    password: process.env.TEST_DB_PASSWORD || 'test',
                    database: process.env.TEST_DB_NAME || 'sentinel_perf_test',
                    entities: [
                        notification_workflow_entity_1.NotificationWorkflow,
                        workflow_execution_entity_1.WorkflowExecution,
                        notification_analytics_event_entity_1.NotificationAnalyticsEvent,
                    ],
                    synchronize: true,
                    dropSchema: true,
                    // Performance optimizations for testing
                    extra: {
                        max: 20, // Connection pool size
                        connectionTimeoutMillis: 2000,
                        idleTimeoutMillis: 30000,
                    },
                }),
                typeorm_1.TypeOrmModule.forFeature([
                    notification_workflow_entity_1.NotificationWorkflow,
                    workflow_execution_entity_1.WorkflowExecution,
                    notification_analytics_event_entity_1.NotificationAnalyticsEvent,
                ]),
                event_emitter_1.EventEmitterModule.forRoot({
                    maxListeners: 1000, // Increase for load testing
                }),
                bull_1.BullModule.forRoot({
                    redis: {
                        host: process.env.TEST_REDIS_HOST || 'localhost',
                        port: parseInt(process.env.TEST_REDIS_PORT) || 6380,
                        db: 2, // Separate DB for performance tests
                        maxRetriesPerRequest: 3,
                        retryDelayOnFailover: 100,
                    },
                }),
                bull_1.BullModule.registerQueue({ name: 'notification-high' }, { name: 'notification-medium' }, { name: 'notification-low' }),
            ],
            providers: [
                notification_workflow_service_1.NotificationWorkflowService,
                notification_queue_management_service_1.NotificationQueueManagementService,
                notification_analytics_service_1.NotificationAnalyticsService,
                provider_health_monitoring_service_1.ProviderHealthMonitoringService,
                email_notification_provider_1.EmailNotificationProvider,
                slack_notification_provider_1.SlackNotificationProvider,
                // Mock providers optimized for performance testing
                {
                    provide: 'PerformanceEmailProvider',
                    useValue: {
                        sendNotification: jest.fn().mockImplementation(async () => {
                            await new Promise(resolve => setTimeout(resolve, 100)); // Simulate 100ms latency
                            return { success: true, messageId: `perf-${Date.now()}`, deliveredAt: new Date() };
                        }),
                        validateConfiguration: jest.fn().mockReturnValue(true),
                        getHealthStatus: jest.fn().mockResolvedValue({ healthy: true, responseTime: 100 }),
                    },
                },
            ],
        }).compile();
        app = moduleFixture.createNestApplication();
        // Performance optimizations
        app.enableShutdownHooks();
        await app.init();
        // Get service instances
        workflowService = moduleFixture.get(notification_workflow_service_1.NotificationWorkflowService);
        queueService = moduleFixture.get(notification_queue_management_service_1.NotificationQueueManagementService);
        analyticsService = moduleFixture.get(notification_analytics_service_1.NotificationAnalyticsService);
        healthService = moduleFixture.get(provider_health_monitoring_service_1.ProviderHealthMonitoringService);
        emailProvider = moduleFixture.get(email_notification_provider_1.EmailNotificationProvider);
        slackProvider = moduleFixture.get(slack_notification_provider_1.SlackNotificationProvider);
        // Get repository instances
        workflowRepository = moduleFixture.get((0, typeorm_2.getRepositoryToken)(notification_workflow_entity_1.NotificationWorkflow));
        executionRepository = moduleFixture.get((0, typeorm_2.getRepositoryToken)(workflow_execution_entity_1.WorkflowExecution));
        analyticsRepository = moduleFixture.get((0, typeorm_2.getRepositoryToken)(notification_analytics_event_entity_1.NotificationAnalyticsEvent));
        // Pre-create test workflows for performance testing
        await createTestWorkflows();
    });
    afterAll(async () => {
        await app.close();
    });
    describe('Workflow Execution Performance', () => {
        it('should execute single workflow within performance threshold', async () => {
            const workflow = await workflowRepository.findOne({
                where: { name: 'Performance Test Workflow' },
            });
            const startTime = Date.now();
            const execution = await workflowService.executeWorkflow(workflow.id, {
                input: {
                    alert: { message: 'Performance test alert', severity: 'medium' },
                    user: testUser,
                },
            }, testUser);
            // Wait for completion
            await waitForExecution(execution.executionId);
            const endTime = Date.now();
            const executionTime = endTime - startTime;
            expect(executionTime).toBeLessThan(PERFORMANCE_THRESHOLDS.WORKFLOW_EXECUTION_TIME);
            // Verify execution completed successfully
            const completedExecution = await executionRepository.findOne({
                where: { id: execution.executionId },
            });
            expect(completedExecution.status).toBe('completed');
            expect(completedExecution.duration).toBeLessThan(PERFORMANCE_THRESHOLDS.WORKFLOW_EXECUTION_TIME);
        });
        it('should handle concurrent workflow executions', async () => {
            const workflow = await workflowRepository.findOne({
                where: { name: 'Concurrent Test Workflow' },
            });
            const concurrentCount = PERFORMANCE_THRESHOLDS.CONCURRENT_WORKFLOWS;
            const startTime = Date.now();
            // Execute workflows concurrently
            const executionPromises = Array(concurrentCount).fill(null).map((_, i) => workflowService.executeWorkflow(workflow.id, {
                input: {
                    alert: { id: `alert-${i}`, message: `Concurrent test ${i}`, severity: 'low' },
                    user: testUser,
                },
            }, testUser));
            const executions = await Promise.all(executionPromises);
            expect(executions).toHaveLength(concurrentCount);
            // Wait for all executions to complete
            await Promise.all(executions.map(exec => waitForExecution(exec.executionId)));
            const endTime = Date.now();
            const totalTime = endTime - startTime;
            const avgTimePerWorkflow = totalTime / concurrentCount;
            expect(avgTimePerWorkflow).toBeLessThan(PERFORMANCE_THRESHOLDS.WORKFLOW_EXECUTION_TIME);
            // Verify all executions completed successfully
            const completedExecutions = await executionRepository.find({
                where: { workflowId: workflow.id },
            });
            const successfulExecutions = completedExecutions.filter(exec => exec.status === 'completed');
            expect(successfulExecutions.length).toBeGreaterThanOrEqual(concurrentCount * 0.95); // 95% success rate
        });
        it('should maintain performance with complex workflows', async () => {
            const complexWorkflow = await workflowRepository.findOne({
                where: { name: 'Complex Performance Test Workflow' },
            });
            const iterations = 20;
            const executionTimes = [];
            for (let i = 0; i < iterations; i++) {
                const startTime = Date.now();
                const execution = await workflowService.executeWorkflow(complexWorkflow.id, {
                    input: {
                        alert: { id: `complex-${i}`, message: `Complex test ${i}`, severity: 'high' },
                        user: testUser,
                    },
                }, testUser);
                await waitForExecution(execution.executionId);
                const endTime = Date.now();
                executionTimes.push(endTime - startTime);
            }
            // Calculate performance metrics
            const avgExecutionTime = executionTimes.reduce((sum, time) => sum + time, 0) / iterations;
            const maxExecutionTime = Math.max(...executionTimes);
            const minExecutionTime = Math.min(...executionTimes);
            expect(avgExecutionTime).toBeLessThan(PERFORMANCE_THRESHOLDS.WORKFLOW_EXECUTION_TIME);
            expect(maxExecutionTime).toBeLessThan(PERFORMANCE_THRESHOLDS.WORKFLOW_EXECUTION_TIME * 1.5);
            console.log(`Complex Workflow Performance:
        Average: ${avgExecutionTime}ms
        Min: ${minExecutionTime}ms
        Max: ${maxExecutionTime}ms
        Iterations: ${iterations}`);
        });
    });
    describe('Notification Provider Performance', () => {
        it('should deliver notifications within latency threshold', async () => {
            const notificationData = {
                id: 'perf-notification-123',
                channel: 'email',
                recipients: ['<EMAIL>'],
                message: {
                    subject: 'Performance Test',
                    content: 'Testing notification delivery performance',
                },
                alert: { severity: 'medium', message: 'Performance test alert' },
                user: testUser,
            };
            const startTime = Date.now();
            const result = await emailProvider.sendNotification(notificationData);
            const endTime = Date.now();
            const deliveryTime = endTime - startTime;
            expect(deliveryTime).toBeLessThan(PERFORMANCE_THRESHOLDS.NOTIFICATION_DELIVERY_TIME);
            expect(result.success).toBe(true);
        });
        it('should handle high-volume notification throughput', async () => {
            const notificationCount = PERFORMANCE_THRESHOLDS.NOTIFICATIONS_PER_SECOND * 10; // 10 seconds worth
            const startTime = Date.now();
            // Send notifications in batches to avoid overwhelming the system
            const batchSize = 10;
            const batches = Math.ceil(notificationCount / batchSize);
            for (let batch = 0; batch < batches; batch++) {
                const batchPromises = [];
                for (let i = 0; i < batchSize && (batch * batchSize + i) < notificationCount; i++) {
                    const notificationData = {
                        id: `perf-batch-${batch}-${i}`,
                        channel: 'email',
                        recipients: ['<EMAIL>'],
                        message: { subject: `Batch ${batch} Notification ${i}` },
                        alert: { severity: 'low' },
                        user: testUser,
                    };
                    batchPromises.push(emailProvider.sendNotification(notificationData));
                }
                await Promise.all(batchPromises);
                // Small delay between batches to prevent overwhelming
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            const endTime = Date.now();
            const totalTime = endTime - startTime;
            const throughput = (notificationCount / totalTime) * 1000; // notifications per second
            expect(throughput).toBeGreaterThan(PERFORMANCE_THRESHOLDS.NOTIFICATIONS_PER_SECOND * 0.8); // 80% of target
            console.log(`Notification Throughput:
        Total Notifications: ${notificationCount}
        Total Time: ${totalTime}ms
        Throughput: ${throughput.toFixed(2)} notifications/second`);
        });
        it('should maintain provider health under load', async () => {
            const loadDuration = 30000; // 30 seconds
            const startTime = Date.now();
            let notificationsSent = 0;
            // Continuous load for specified duration
            while (Date.now() - startTime < loadDuration) {
                const batchPromises = Array(5).fill(null).map((_, i) => {
                    const notificationData = {
                        id: `load-test-${Date.now()}-${i}`,
                        channel: 'email',
                        recipients: ['<EMAIL>'],
                        message: { subject: 'Load Test' },
                        alert: { severity: 'low' },
                        user: testUser,
                    };
                    return emailProvider.sendNotification(notificationData);
                });
                await Promise.all(batchPromises);
                notificationsSent += 5;
                // Check provider health periodically
                if (notificationsSent % 50 === 0) {
                    const health = await healthService.getProviderHealth('email');
                    expect(health.healthy).toBe(true);
                    expect(health.responseTime).toBeLessThan(PERFORMANCE_THRESHOLDS.NOTIFICATION_DELIVERY_TIME);
                }
                await new Promise(resolve => setTimeout(resolve, 200)); // 200ms between batches
            }
            const finalHealth = await healthService.getProviderHealth('email');
            expect(finalHealth.healthy).toBe(true);
            console.log(`Load Test Results:
        Duration: ${loadDuration}ms
        Notifications Sent: ${notificationsSent}
        Final Health: ${finalHealth.healthy}
        Final Response Time: ${finalHealth.responseTime}ms`);
        });
    });
    describe('Queue Management Performance', () => {
        it('should process queue items within threshold', async () => {
            const queueItems = 100;
            const startTime = Date.now();
            // Add items to queue
            const queuePromises = Array(queueItems).fill(null).map((_, i) => {
                const notificationData = {
                    id: `queue-perf-${i}`,
                    channel: 'email',
                    recipients: ['<EMAIL>'],
                    message: { subject: `Queue Test ${i}` },
                    alert: { severity: 'medium' },
                    user: testUser,
                };
                return queueService.addNotificationToQueue(notificationData, 'medium');
            });
            await Promise.all(queuePromises);
            const queueTime = Date.now() - startTime;
            expect(queueTime).toBeLessThan(PERFORMANCE_THRESHOLDS.QUEUE_PROCESSING_TIME * queueItems);
            // Wait for queue processing
            await new Promise(resolve => setTimeout(resolve, 5000));
            const queueStats = await queueService.getQueueStatistics();
            expect(queueStats.totalProcessed).toBeGreaterThanOrEqual(queueItems * 0.9); // 90% processed
        });
        it('should handle queue overflow gracefully', async () => {
            const overflowItems = 1000;
            const results = [];
            // Attempt to add many items quickly
            for (let i = 0; i < overflowItems; i++) {
                try {
                    const notificationData = {
                        id: `overflow-${i}`,
                        channel: 'email',
                        recipients: ['<EMAIL>'],
                        message: { subject: `Overflow Test ${i}` },
                        alert: { severity: 'low' },
                        user: testUser,
                    };
                    const result = await queueService.addNotificationToQueue(notificationData, 'low');
                    results.push({ success: true, result });
                }
                catch (error) {
                    results.push({ success: false, error: error.message });
                }
            }
            const successful = results.filter(r => r.success).length;
            const failed = results.filter(r => !r.success).length;
            // Should handle overflow gracefully (some items may be rejected)
            expect(successful).toBeGreaterThan(0);
            expect(successful + failed).toBe(overflowItems);
            console.log(`Queue Overflow Test:
        Total Items: ${overflowItems}
        Successful: ${successful}
        Failed: ${failed}
        Success Rate: ${(successful / overflowItems * 100).toFixed(2)}%`);
        });
    });
    describe('Database Performance', () => {
        it('should handle large dataset queries efficiently', async () => {
            // Create large dataset
            const datasetSize = 10000;
            const analyticsEvents = Array(datasetSize).fill(null).map((_, i) => ({
                notificationId: `perf-${i}`,
                provider: ['email', 'sms', 'slack'][i % 3],
                channel: ['email', 'sms', 'slack'][i % 3],
                status: ['delivered', 'failed', 'pending'][i % 3],
                deliveryTime: Math.random() * 5000,
                timestamp: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
                metadata: { testData: true, index: i },
            }));
            // Insert data in batches
            const batchSize = 1000;
            for (let i = 0; i < datasetSize; i += batchSize) {
                const batch = analyticsEvents.slice(i, i + batchSize);
                await analyticsRepository.save(batch);
            }
            // Test query performance
            const startTime = Date.now();
            const results = await analyticsRepository
                .createQueryBuilder('event')
                .where('event.timestamp > :date', { date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) })
                .groupBy('event.provider')
                .select('event.provider', 'provider')
                .addSelect('COUNT(*)', 'count')
                .addSelect('AVG(event.deliveryTime)', 'avgDeliveryTime')
                .getRawMany();
            const queryTime = Date.now() - startTime;
            expect(queryTime).toBeLessThan(PERFORMANCE_THRESHOLDS.DATABASE_QUERY_TIME);
            expect(results.length).toBeGreaterThan(0);
            console.log(`Database Query Performance:
        Dataset Size: ${datasetSize}
        Query Time: ${queryTime}ms
        Results: ${results.length} rows`);
        });
        it('should maintain performance with concurrent database operations', async () => {
            const concurrentOperations = 50;
            const operationPromises = [];
            for (let i = 0; i < concurrentOperations; i++) {
                // Mix of read and write operations
                if (i % 2 === 0) {
                    // Read operation
                    operationPromises.push(analyticsRepository.find({
                        where: { provider: 'email' },
                        take: 100,
                        order: { timestamp: 'DESC' },
                    }));
                }
                else {
                    // Write operation
                    operationPromises.push(analyticsRepository.save({
                        notificationId: `concurrent-${i}`,
                        provider: 'email',
                        channel: 'email',
                        status: 'delivered',
                        deliveryTime: 1000,
                        timestamp: new Date(),
                        metadata: { concurrent: true },
                    }));
                }
            }
            const startTime = Date.now();
            const results = await Promise.allSettled(operationPromises);
            const endTime = Date.now();
            const totalTime = endTime - startTime;
            const avgTimePerOperation = totalTime / concurrentOperations;
            expect(avgTimePerOperation).toBeLessThan(PERFORMANCE_THRESHOLDS.DATABASE_QUERY_TIME);
            const successful = results.filter(r => r.status === 'fulfilled').length;
            expect(successful).toBeGreaterThanOrEqual(concurrentOperations * 0.95); // 95% success rate
        });
    });
    // Helper functions
    async function waitForExecution(executionId, timeout = 10000) {
        const startTime = Date.now();
        while (Date.now() - startTime < timeout) {
            const execution = await executionRepository.findOne({
                where: { id: executionId },
            });
            if (execution && ['completed', 'failed', 'cancelled'].includes(execution.status)) {
                return;
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        throw new Error(`Execution ${executionId} did not complete within ${timeout}ms`);
    }
    async function createTestWorkflows() {
        // Simple performance test workflow
        await workflowRepository.save({
            name: 'Performance Test Workflow',
            description: 'Simple workflow for performance testing',
            definition: {
                startStep: 'send_notification',
                steps: [
                    {
                        id: 'send_notification',
                        type: 'notification',
                        config: {
                            channel: 'email',
                            message: 'Performance test notification',
                            recipients: ['<EMAIL>'],
                        },
                    },
                ],
            },
            status: 'active',
            createdBy: testUser.id,
            updatedBy: testUser.id,
        });
        // Concurrent test workflow
        await workflowRepository.save({
            name: 'Concurrent Test Workflow',
            description: 'Workflow optimized for concurrent execution',
            definition: {
                startStep: 'quick_notification',
                steps: [
                    {
                        id: 'quick_notification',
                        type: 'notification',
                        config: {
                            channel: 'email',
                            message: 'Concurrent test: {{input.alert.id}}',
                            recipients: ['<EMAIL>'],
                        },
                    },
                ],
            },
            status: 'active',
            createdBy: testUser.id,
            updatedBy: testUser.id,
        });
        // Complex workflow for performance testing
        await workflowRepository.save({
            name: 'Complex Performance Test Workflow',
            description: 'Multi-step workflow for complex performance testing',
            definition: {
                startStep: 'validate_input',
                steps: [
                    {
                        id: 'validate_input',
                        type: 'condition',
                        config: {
                            condition: { field: 'input.alert.severity', operator: 'in', value: ['medium', 'high', 'critical'] },
                        },
                        nextStep: 'set_variables',
                    },
                    {
                        id: 'set_variables',
                        type: 'variable',
                        config: {
                            variables: { processedAt: '{{now}}', alertId: '{{input.alert.id}}' },
                        },
                        nextStep: 'send_notification',
                    },
                    {
                        id: 'send_notification',
                        type: 'notification',
                        config: {
                            channel: 'email',
                            message: 'Complex workflow processed alert {{variables.alertId}} at {{variables.processedAt}}',
                            recipients: ['<EMAIL>'],
                        },
                    },
                ],
            },
            status: 'active',
            createdBy: testUser.id,
            updatedBy: testUser.id,
        });
    }
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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