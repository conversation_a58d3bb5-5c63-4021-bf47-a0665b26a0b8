79486348583df8a7c790b1b6b3acea07
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f;
Object.defineProperty(exports, "__esModule", { value: true });
exports.Asset = void 0;
const typeorm_1 = require("typeorm");
const asset_group_entity_1 = require("./asset-group.entity");
const asset_configuration_entity_1 = require("./asset-configuration.entity");
const asset_vulnerability_entity_1 = require("./asset-vulnerability.entity");
const asset_relationship_entity_1 = require("./asset-relationship.entity");
/**
 * Asset entity
 * Represents physical and logical assets in the organization's infrastructure
 */
let Asset = class Asset {
    /**
     * Check if asset is online/reachable
     */
    get isOnline() {
        if (!this.lastSeen)
            return false;
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
        return this.lastSeen > fiveMinutesAgo;
    }
    /**
     * Check if asset is critical
     */
    get isCritical() {
        return this.criticality === 'critical';
    }
    /**
     * Check if asset is in production
     */
    get isProduction() {
        return this.environment === 'production';
    }
    /**
     * Get asset age in days
     */
    get ageInDays() {
        const now = new Date();
        const diffMs = now.getTime() - this.discoveredAt.getTime();
        return Math.floor(diffMs / (1000 * 60 * 60 * 24));
    }
    /**
     * Get time since last seen in minutes
     */
    get minutesSinceLastSeen() {
        if (!this.lastSeen)
            return Infinity;
        const now = new Date();
        const diffMs = now.getTime() - this.lastSeen.getTime();
        return Math.floor(diffMs / (1000 * 60));
    }
    /**
     * Check if asset has agent installed
     */
    get hasAgent() {
        return this.discovery?.agentInstalled || false;
    }
    /**
     * Get primary operating system
     */
    get primaryOS() {
        if (!this.operatingSystem)
            return 'Unknown';
        return `${this.operatingSystem.name} ${this.operatingSystem.version}`;
    }
    /**
     * Update last seen timestamp
     */
    updateLastSeen() {
        this.lastSeen = new Date();
    }
    /**
     * Add tag to asset
     */
    addTag(tag) {
        if (!this.tags.includes(tag)) {
            this.tags.push(tag);
        }
    }
    /**
     * Remove tag from asset
     */
    removeTag(tag) {
        this.tags = this.tags.filter(t => t !== tag);
    }
    /**
     * Update asset status
     */
    updateStatus(status, updatedBy) {
        this.status = status;
        this.updatedBy = updatedBy;
    }
    /**
     * Set asset criticality
     */
    setCriticality(criticality, updatedBy) {
        this.criticality = criticality;
        this.updatedBy = updatedBy;
    }
    /**
     * Update ownership information
     */
    updateOwnership(ownership, updatedBy) {
        this.ownership = { ...this.ownership, ...ownership };
        this.updatedBy = updatedBy;
    }
    /**
     * Add custom attribute
     */
    setCustomAttribute(key, value) {
        if (!this.customAttributes) {
            this.customAttributes = {};
        }
        this.customAttributes[key] = value;
    }
    /**
     * Get custom attribute
     */
    getCustomAttribute(key) {
        return this.customAttributes?.[key];
    }
    /**
     * Check if asset matches filter criteria
     */
    matchesFilter(filter) {
        // Check type
        if (filter.types && !filter.types.includes(this.type)) {
            return false;
        }
        // Check status
        if (filter.statuses && !filter.statuses.includes(this.status)) {
            return false;
        }
        // Check criticality
        if (filter.criticalities && !filter.criticalities.includes(this.criticality)) {
            return false;
        }
        // Check environment
        if (filter.environments && !filter.environments.includes(this.environment)) {
            return false;
        }
        // Check location
        if (filter.locations && this.location && !filter.locations.includes(this.location)) {
            return false;
        }
        // Check tags
        if (filter.tags && !filter.tags.some(tag => this.tags.includes(tag))) {
            return false;
        }
        // Check agent
        if (filter.hasAgent !== undefined && this.hasAgent !== filter.hasAgent) {
            return false;
        }
        // Check online status
        if (filter.isOnline !== undefined && this.isOnline !== filter.isOnline) {
            return false;
        }
        // Check search text
        if (filter.searchText) {
            const searchText = filter.searchText.toLowerCase();
            const searchableText = `${this.name} ${this.hostname} ${this.ipAddress} ${this.description}`.toLowerCase();
            if (!searchableText.includes(searchText)) {
                return false;
            }
        }
        return true;
    }
    /**
     * Calculate risk score based on various factors
     */
    calculateRiskScore() {
        let score = 0;
        // Base score from criticality
        const criticalityScores = { low: 2, medium: 5, high: 8, critical: 10 };
        score += criticalityScores[this.criticality];
        // Environment factor
        const environmentFactors = { production: 3, staging: 2, development: 1, testing: 1, sandbox: 0.5 };
        score += environmentFactors[this.environment];
        // Online status factor
        if (!this.isOnline)
            score += 2;
        // Agent factor
        if (!this.hasAgent)
            score += 1;
        // Age factor (older assets might be less secure)
        if (this.ageInDays > 365)
            score += 1;
        return Math.min(10, score);
    }
    /**
     * Get asset summary for display
     */
    getSummary() {
        return {
            id: this.id,
            name: this.name,
            type: this.type,
            status: this.status,
            criticality: this.criticality,
            environment: this.environment,
            ipAddress: this.ipAddress,
            hostname: this.hostname,
            location: this.location,
            isOnline: this.isOnline,
            isCritical: this.isCritical,
            hasAgent: this.hasAgent,
            primaryOS: this.primaryOS,
            ageInDays: this.ageInDays,
            riskScore: this.calculateRiskScore(),
            lastSeen: this.lastSeen,
            tags: this.tags,
        };
    }
    /**
     * Export asset for reporting
     */
    exportForReporting() {
        return {
            asset: this.getSummary(),
            operatingSystem: this.operatingSystem,
            hardware: this.hardware,
            software: this.software,
            cloudMetadata: this.cloudMetadata,
            networkInfo: this.networkInfo,
            ownership: this.ownership,
            compliance: this.compliance,
            discovery: this.discovery,
            customAttributes: this.customAttributes,
            exportedAt: new Date().toISOString(),
        };
    }
};
exports.Asset = Asset;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Asset.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], Asset.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Asset.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: [
            'server',
            'workstation',
            'laptop',
            'mobile_device',
            'network_device',
            'router',
            'switch',
            'firewall',
            'load_balancer',
            'storage_device',
            'database',
            'application',
            'web_service',
            'api_service',
            'cloud_instance',
            'container',
            'virtual_machine',
            'iot_device',
            'printer',
            'scanner',
            'camera',
            'other',
        ],
    }),
    __metadata("design:type", String)
], Asset.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'sub_type', nullable: true }),
    __metadata("design:type", String)
], Asset.prototype, "subType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['active', 'inactive', 'maintenance', 'decommissioned', 'unknown'],
        default: 'unknown',
    }),
    __metadata("design:type", String)
], Asset.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['low', 'medium', 'high', 'critical'],
        default: 'medium',
    }),
    __metadata("design:type", String)
], Asset.prototype, "criticality", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['production', 'staging', 'development', 'testing', 'sandbox'],
        default: 'production',
    }),
    __metadata("design:type", String)
], Asset.prototype, "environment", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Asset.prototype, "location", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ip_address', nullable: true }),
    __metadata("design:type", String)
], Asset.prototype, "ipAddress", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ip_addresses', type: 'text', array: true, default: '{}' }),
    __metadata("design:type", Array)
], Asset.prototype, "ipAddresses", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Asset.prototype, "hostname", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'mac_address', nullable: true }),
    __metadata("design:type", String)
], Asset.prototype, "macAddress", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Asset.prototype, "operatingSystem", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Asset.prototype, "hardware", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Asset.prototype, "software", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Asset.prototype, "cloudMetadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Asset.prototype, "networkInfo", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Asset.prototype, "ownership", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Asset.prototype, "compliance", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Asset.prototype, "discovery", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, default: '{}' }),
    __metadata("design:type", Array)
], Asset.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'custom_attributes', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_a = typeof Record !== "undefined" && Record) === "function" ? _a : Object)
], Asset.prototype, "customAttributes", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'discovered_at', type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], Asset.prototype, "discoveredAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_seen', type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], Asset.prototype, "lastSeen", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', type: 'uuid' }),
    __metadata("design:type", String)
], Asset.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], Asset.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], Asset.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_e = typeof Date !== "undefined" && Date) === "function" ? _e : Object)
], Asset.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => asset_group_entity_1.AssetGroup, group => group.assets, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'group_id' }),
    __metadata("design:type", typeof (_f = typeof asset_group_entity_1.AssetGroup !== "undefined" && asset_group_entity_1.AssetGroup) === "function" ? _f : Object)
], Asset.prototype, "group", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'group_id', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], Asset.prototype, "groupId", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => asset_configuration_entity_1.AssetConfiguration, config => config.asset),
    __metadata("design:type", Array)
], Asset.prototype, "configurations", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => asset_vulnerability_entity_1.AssetVulnerability, vulnerability => vulnerability.asset),
    __metadata("design:type", Array)
], Asset.prototype, "vulnerabilities", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => asset_relationship_entity_1.AssetRelationship, relationship => relationship.sourceAsset),
    __metadata("design:type", Array)
], Asset.prototype, "sourceRelationships", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => asset_relationship_entity_1.AssetRelationship, relationship => relationship.targetAsset),
    __metadata("design:type", Array)
], Asset.prototype, "targetRelationships", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => Asset, asset => asset.relatedAssets),
    (0, typeorm_1.JoinTable)({
        name: 'asset_dependencies',
        joinColumn: { name: 'asset_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'dependent_asset_id', referencedColumnName: 'id' },
    }),
    __metadata("design:type", Array)
], Asset.prototype, "dependencies", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => Asset, asset => asset.dependencies),
    __metadata("design:type", Array)
], Asset.prototype, "relatedAssets", void 0);
exports.Asset = Asset = __decorate([
    (0, typeorm_1.Entity)('assets'),
    (0, typeorm_1.Index)(['type']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['criticality']),
    (0, typeorm_1.Index)(['environment']),
    (0, typeorm_1.Index)(['location']),
    (0, typeorm_1.Index)(['ipAddress']),
    (0, typeorm_1.Index)(['hostname']),
    (0, typeorm_1.Index)(['lastSeen']),
    (0, typeorm_1.Index)(['discoveredAt'])
], Asset);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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