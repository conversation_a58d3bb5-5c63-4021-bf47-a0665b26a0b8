{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\testing\\base\\integration-test.base.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2XA,0CAsBC;AAKD,0CAkBC;AAraD,6CAAqD;AAErD,yDAAsD;AACtD,mDAAqC;AACrC,qFAAgF;AAChF,qEAAgE;AAEhE;;;;;;;;;;;GAWG;AACH,MAAsB,mBAAmB;IAAzC;QASE,iBAAiB;QACP,eAAU,GAAwB,IAAI,GAAG,EAAE,CAAC;QAC5C,cAAS,GAAqB,IAAI,GAAG,EAAE,CAAC;QACxC,uBAAkB,GAA0B,IAAI,GAAG,EAAE,CAAC;QACtD,iBAAY,GAAU,EAAE,CAAC;IAmVrC,CAAC;IAjVC;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,wBAAwB;QACxB,IAAI,CAAC,aAAa,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEtD,yBAAyB;QACzB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC;QACtD,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAClC,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QAEtB,eAAe;QACf,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAa,IAAA,4BAAkB,GAAE,CAAC,CAAC;QAC3E,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAA2B,qDAAwB,CAAC,CAAC;QACpG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAkB,mCAAe,CAAC,CAAC;QAChF,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAgB,6BAAa,CAAC,CAAC;QACzE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;QAE3C,yBAAyB;QACzB,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,kBAAkB;QAClB,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;QAE3B,uBAAuB;QACvB,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS;QACb,IAAI,CAAC;YACH,kBAAkB;YAClB,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;YAE7B,sBAAsB;YACtB,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;YACvB,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;YAEhC,oBAAoB;YACpB,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;gBACb,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;YACzB,CAAC;YAED,uBAAuB;YACvB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACvB,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;YACnC,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAOD;;OAEG;IACO,KAAK,CAAC,oBAAoB;QAClC,+CAA+C;QAC/C,mEAAmE;IACrE,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,aAAa;QAC3B,6CAA6C;IAC/C,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,mBAAmB;QACjC,+BAA+B;QAC/B,MAAM,SAAS,GAAG;YAChB,EAAE,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC,GAAG,CAAC,EAAE;YACvD,EAAE,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,eAAe,EAAE,kBAAkB,CAAC,EAAE;YACrF,EAAE,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC,eAAe,CAAC,EAAE;SACtE,CAAC;QAEF,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE,CAAC;YAC7B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;YAClC,2CAA2C;YAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAC3C,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAED;;OAEG;IACO,oBAAoB;QAC5B,iCAAiC;QACjC,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACpE,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC,KAAa,EAAE,GAAG,IAAW,EAAE,EAAE;YACzD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;gBACrB,KAAK;gBACL,IAAI;gBACJ,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YACH,OAAO,YAAY,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;QACtC,CAAC,CAAC;IACJ,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,eAAe;QAC7B,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC;QAC7C,CAAC;IACH,CAAC;IAED;;OAEG;IACO,oBAAoB,CAAC,SAAiB,WAAW;QACzD,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC1C,OAAO,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC,CAAC;IAC1E,CAAC;IAES,GAAG,CAAC,GAAW,EAAE,MAAe;QACxC,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACpD,CAAC;IAES,IAAI,CAAC,GAAW,EAAE,MAAe;QACzC,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACrD,CAAC;IAES,GAAG,CAAC,GAAW,EAAE,MAAe;QACxC,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACpD,CAAC;IAES,MAAM,CAAC,GAAW,EAAE,MAAe;QAC3C,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACvD,CAAC;IAES,KAAK,CAAC,GAAW,EAAE,MAAe;QAC1C,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACO,YAAY,CAAC,SAAiB,EAAE,UAAkB,IAAI;QAC9D,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;gBAChC,MAAM,CAAC,IAAI,KAAK,CAAC,UAAU,SAAS,wBAAwB,OAAO,IAAI,CAAC,CAAC,CAAC;YAC5E,CAAC,EAAE,OAAO,CAAC,CAAC;YAEZ,MAAM,UAAU,GAAG,GAAG,EAAE;gBACtB,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC;gBACjE,IAAI,KAAK,EAAE,CAAC;oBACV,YAAY,CAAC,SAAS,CAAC,CAAC;oBACxB,OAAO,CAAC,KAAK,CAAC,CAAC;gBACjB,CAAC;qBAAM,CAAC;oBACN,UAAU,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;gBAC9B,CAAC;YACH,CAAC,CAAC;YAEF,UAAU,EAAE,CAAC;QACf,CAAC,CAAC,CAAC;IACL,CAAC;IAES,eAAe,CAAC,SAAkB;QAC1C,IAAI,SAAS,EAAE,CAAC;YACd,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC;QAC9D,CAAC;QACD,OAAO,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;IAChC,CAAC;IAES,iBAAiB;QACzB,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,kBAAkB,CAChC,SAA2B,EAC3B,aAAqB;QAErB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;QACjC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAExC,2BAA2B;QAC3B,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC;YAChD,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;QACjD,CAAC;QACD,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,aAAa,CAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE3D,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;IAC9B,CAAC;IAES,mBAAmB,CAAC,aAAqB;QAOjD,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC3D,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,MAAM,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAClD,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;QAC5B,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACtB,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QAC9B,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC;QAC9D,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;QAC1C,MAAM,GAAG,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;QAE7B,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACvC,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,eAAe,CAAI,SAAuC;QACxE,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;QACxD,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;QAC5B,MAAM,WAAW,CAAC,gBAAgB,EAAE,CAAC;QAErC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YACpD,MAAM,WAAW,CAAC,iBAAiB,EAAE,CAAC;YACtC,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,WAAW,CAAC,mBAAmB,EAAE,CAAC;YACxC,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;QAC9B,CAAC;IACH,CAAC;IAES,KAAK,CAAC,YAAY,CAAC,KAAa,EAAE,UAAkB;QAC5D,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;IACxD,CAAC;IAES,KAAK,CAAC,gBAAgB,CAAC,SAAiB;QAChD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,iCAAiC,SAAS,EAAE,CAAC,CAAC;QACrF,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACO,mBAAmB,CAAC,QAA0B,EAAE,iBAAyB,GAAG;QACpF,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC7C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;IACtC,CAAC;IAES,qBAAqB,CAAC,QAA0B,EAAE,KAAc;QACxE,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;QAC5C,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACjD,CAAC;IACH,CAAC;IAES,kBAAkB,CAAC,QAA0B;QACrD,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACpC,CAAC;IAES,eAAe,CAAC,QAA0B;QAClD,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACpC,CAAC;IAES,cAAc,CAAC,QAA0B;QACjD,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,OAAO,CAAC,SAA2C,EAAE,UAAkB,IAAI;QACzF,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,GAAG,OAAO,EAAE,CAAC;YACxC,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;YACjC,IAAI,MAAM,EAAE,CAAC;gBACX,OAAO;YACT,CAAC;YACD,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,4BAA4B,OAAO,IAAI,CAAC,CAAC;IAC3D,CAAC;IAES,KAAK,CAAC,EAAU;QACxB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACO,iBAAiB,CAAC,OAAiB;QAC3C,MAAM,IAAI,GAAQ,EAAE,CAAC;QACrB,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACvB,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;IACd,CAAC;IAES,UAAU,CAAC,GAAG,KAAY;QAClC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACnB,IAAI,IAAI,IAAI,OAAO,IAAI,CAAC,SAAS,KAAK,UAAU,EAAE,CAAC;gBACjD,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,IAAS;QACjC,8CAA8C;QAC9C,MAAM,OAAO,GAAG;YACd,GAAG,EAAE,IAAI,CAAC,EAAE;YACZ,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;YAClC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,IAAI,EAAE,SAAS;SACrD,CAAC;QAEF,8DAA8D;QAC9D,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IACjE,CAAC;CACF;AAhWD,kDAgWC;AAED;;GAEG;AACH,SAAgB,eAAe,CAAC,WAAmB;IACjD,OAAO,UAAU,MAAW,EAAE,YAAoB,EAAE,UAA8B;QAChF,MAAM,cAAc,GAAG,UAAU,CAAC,KAAK,CAAC;QAExC,UAAU,CAAC,KAAK,GAAG,KAAK,WAAW,GAAG,IAAW;YAC/C,OAAO,CAAC,GAAG,CAAC,6BAA6B,WAAW,EAAE,CAAC,CAAC;YACxD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACtD,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBACxC,OAAO,CAAC,GAAG,CAAC,+BAA+B,WAAW,KAAK,QAAQ,KAAK,CAAC,CAAC;gBAC1E,OAAO,MAAM,CAAC;YAChB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBACxC,OAAO,CAAC,KAAK,CAAC,4BAA4B,WAAW,KAAK,QAAQ,KAAK,EAAE,KAAK,CAAC,CAAC;gBAChF,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC,CAAC;QAEF,OAAO,UAAU,CAAC;IACpB,CAAC,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,eAAe,CAAC,WAAmB;IACjD,OAAO,UAAU,MAAW,EAAE,YAAoB,EAAE,UAA8B;QAChF,MAAM,cAAc,GAAG,UAAU,CAAC,KAAK,CAAC;QAExC,UAAU,CAAC,KAAK,GAAG,KAAK,WAAW,GAAG,IAAW;YAC/C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACtD,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAExC,IAAI,QAAQ,GAAG,WAAW,EAAE,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,4BAA4B,YAAY,SAAS,QAAQ,kBAAkB,WAAW,IAAI,CAAC,CAAC;YAC9G,CAAC;YAED,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC;QAEF,OAAO,UAAU,CAAC;IACpB,CAAC,CAAC;AACJ,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\testing\\base\\integration-test.base.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { INestApplication } from '@nestjs/common';\r\nimport { DataSource } from 'typeorm';\r\nimport { getDataSourceToken } from '@nestjs/typeorm';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { EventEmitter2 } from '@nestjs/event-emitter';\r\nimport * as request from 'supertest';\r\nimport { TestConfigurationService } from '../config/test-configuration.service';\r\nimport { TestDataService } from '../fixtures/test-data.service';\r\n\r\n/**\r\n * Integration Test Base Class\r\n * \r\n * Comprehensive base class for integration testing providing:\r\n * - NestJS testing module setup and teardown\r\n * - Database transaction management for test isolation\r\n * - Authentication and authorization testing utilities\r\n * - HTTP request testing with supertest integration\r\n * - Event emission and handling validation\r\n * - Performance measurement and validation\r\n * - Cross-module integration testing support\r\n */\r\nexport abstract class IntegrationTestBase {\r\n  protected app: INestApplication;\r\n  protected testingModule: TestingModule;\r\n  protected dataSource: DataSource;\r\n  protected testConfigService: TestConfigurationService;\r\n  protected testDataService: TestDataService;\r\n  protected eventEmitter: EventEmitter2;\r\n  protected httpServer: any;\r\n\r\n  // Test utilities\r\n  protected authTokens: Map<string, string> = new Map();\r\n  protected testUsers: Map<string, any> = new Map();\r\n  protected performanceMetrics: Map<string, number[]> = new Map();\r\n  protected eventHistory: any[] = [];\r\n\r\n  /**\r\n   * Setup test environment before each test\r\n   */\r\n  async beforeEach(): Promise<void> {\r\n    // Create testing module\r\n    this.testingModule = await this.createTestingModule();\r\n    \r\n    // Initialize application\r\n    this.app = this.testingModule.createNestApplication();\r\n    await this.configureApplication();\r\n    await this.app.init();\r\n\r\n    // Get services\r\n    this.dataSource = this.testingModule.get<DataSource>(getDataSourceToken());\r\n    this.testConfigService = this.testingModule.get<TestConfigurationService>(TestConfigurationService);\r\n    this.testDataService = this.testingModule.get<TestDataService>(TestDataService);\r\n    this.eventEmitter = this.testingModule.get<EventEmitter2>(EventEmitter2);\r\n    this.httpServer = this.app.getHttpServer();\r\n\r\n    // Setup event monitoring\r\n    this.setupEventMonitoring();\r\n\r\n    // Setup test data\r\n    await this.setupTestData();\r\n\r\n    // Setup authentication\r\n    await this.setupAuthentication();\r\n  }\r\n\r\n  /**\r\n   * Cleanup test environment after each test\r\n   */\r\n  async afterEach(): Promise<void> {\r\n    try {\r\n      // Clean test data\r\n      await this.cleanupTestData();\r\n\r\n      // Clear event history\r\n      this.eventHistory = [];\r\n      this.performanceMetrics.clear();\r\n\r\n      // Close application\r\n      if (this.app) {\r\n        await this.app.close();\r\n      }\r\n\r\n      // Close testing module\r\n      if (this.testingModule) {\r\n        await this.testingModule.close();\r\n      }\r\n    } catch (error) {\r\n      console.error('Error during test cleanup:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create testing module - to be implemented by subclasses\r\n   */\r\n  protected abstract createTestingModule(): Promise<TestingModule>;\r\n\r\n  /**\r\n   * Configure application with middleware, pipes, guards, etc.\r\n   */\r\n  protected async configureApplication(): Promise<void> {\r\n    // Configure global pipes, guards, interceptors\r\n    // This can be overridden by subclasses for specific configurations\r\n  }\r\n\r\n  /**\r\n   * Setup test data - can be overridden by subclasses\r\n   */\r\n  protected async setupTestData(): Promise<void> {\r\n    // Default implementation - can be overridden\r\n  }\r\n\r\n  /**\r\n   * Setup authentication tokens for testing\r\n   */\r\n  protected async setupAuthentication(): Promise<void> {\r\n    // Create test users and tokens\r\n    const testUsers = [\r\n      { id: 'test-admin', role: 'admin', permissions: ['*'] },\r\n      { id: 'test-user', role: 'user', permissions: ['workflow.view', 'workflow.execute'] },\r\n      { id: 'test-viewer', role: 'viewer', permissions: ['workflow.view'] },\r\n    ];\r\n\r\n    for (const user of testUsers) {\r\n      this.testUsers.set(user.id, user);\r\n      // Generate JWT token for user (simplified)\r\n      const token = this.generateTestToken(user);\r\n      this.authTokens.set(user.id, token);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Setup event monitoring for testing\r\n   */\r\n  protected setupEventMonitoring(): void {\r\n    // Monitor all events for testing\r\n    const originalEmit = this.eventEmitter.emit.bind(this.eventEmitter);\r\n    this.eventEmitter.emit = (event: string, ...args: any[]) => {\r\n      this.eventHistory.push({\r\n        event,\r\n        args,\r\n        timestamp: new Date(),\r\n      });\r\n      return originalEmit(event, ...args);\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Cleanup test data\r\n   */\r\n  protected async cleanupTestData(): Promise<void> {\r\n    if (this.testDataService) {\r\n      await this.testDataService.cleanTestData();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * HTTP request helpers with authentication\r\n   */\r\n  protected authenticatedRequest(userId: string = 'test-user'): request.Test {\r\n    const token = this.authTokens.get(userId);\r\n    return request(this.httpServer).set('Authorization', `Bearer ${token}`);\r\n  }\r\n\r\n  protected get(url: string, userId?: string): request.Test {\r\n    return this.authenticatedRequest(userId).get(url);\r\n  }\r\n\r\n  protected post(url: string, userId?: string): request.Test {\r\n    return this.authenticatedRequest(userId).post(url);\r\n  }\r\n\r\n  protected put(url: string, userId?: string): request.Test {\r\n    return this.authenticatedRequest(userId).put(url);\r\n  }\r\n\r\n  protected delete(url: string, userId?: string): request.Test {\r\n    return this.authenticatedRequest(userId).delete(url);\r\n  }\r\n\r\n  protected patch(url: string, userId?: string): request.Test {\r\n    return this.authenticatedRequest(userId).patch(url);\r\n  }\r\n\r\n  /**\r\n   * Event testing utilities\r\n   */\r\n  protected waitForEvent(eventName: string, timeout: number = 5000): Promise<any> {\r\n    return new Promise((resolve, reject) => {\r\n      const timeoutId = setTimeout(() => {\r\n        reject(new Error(`Event '${eventName}' not emitted within ${timeout}ms`));\r\n      }, timeout);\r\n\r\n      const checkEvent = () => {\r\n        const event = this.eventHistory.find(e => e.event === eventName);\r\n        if (event) {\r\n          clearTimeout(timeoutId);\r\n          resolve(event);\r\n        } else {\r\n          setTimeout(checkEvent, 100);\r\n        }\r\n      };\r\n\r\n      checkEvent();\r\n    });\r\n  }\r\n\r\n  protected getEventHistory(eventName?: string): any[] {\r\n    if (eventName) {\r\n      return this.eventHistory.filter(e => e.event === eventName);\r\n    }\r\n    return [...this.eventHistory];\r\n  }\r\n\r\n  protected clearEventHistory(): void {\r\n    this.eventHistory = [];\r\n  }\r\n\r\n  /**\r\n   * Performance testing utilities\r\n   */\r\n  protected async measurePerformance<T>(\r\n    operation: () => Promise<T>,\r\n    operationName: string\r\n  ): Promise<{ result: T; duration: number }> {\r\n    const startTime = Date.now();\r\n    const result = await operation();\r\n    const duration = Date.now() - startTime;\r\n\r\n    // Store performance metric\r\n    if (!this.performanceMetrics.has(operationName)) {\r\n      this.performanceMetrics.set(operationName, []);\r\n    }\r\n    this.performanceMetrics.get(operationName)!.push(duration);\r\n\r\n    return { result, duration };\r\n  }\r\n\r\n  protected getPerformanceStats(operationName: string): {\r\n    count: number;\r\n    min: number;\r\n    max: number;\r\n    avg: number;\r\n    p95: number;\r\n  } | null {\r\n    const metrics = this.performanceMetrics.get(operationName);\r\n    if (!metrics || metrics.length === 0) {\r\n      return null;\r\n    }\r\n\r\n    const sorted = [...metrics].sort((a, b) => a - b);\r\n    const count = sorted.length;\r\n    const min = sorted[0];\r\n    const max = sorted[count - 1];\r\n    const avg = sorted.reduce((sum, val) => sum + val, 0) / count;\r\n    const p95Index = Math.floor(count * 0.95);\r\n    const p95 = sorted[p95Index];\r\n\r\n    return { count, min, max, avg, p95 };\r\n  }\r\n\r\n  /**\r\n   * Database testing utilities\r\n   */\r\n  protected async withTransaction<T>(operation: (manager: any) => Promise<T>): Promise<T> {\r\n    const queryRunner = this.dataSource.createQueryRunner();\r\n    await queryRunner.connect();\r\n    await queryRunner.startTransaction();\r\n\r\n    try {\r\n      const result = await operation(queryRunner.manager);\r\n      await queryRunner.commitTransaction();\r\n      return result;\r\n    } catch (error) {\r\n      await queryRunner.rollbackTransaction();\r\n      throw error;\r\n    } finally {\r\n      await queryRunner.release();\r\n    }\r\n  }\r\n\r\n  protected async executeQuery(query: string, parameters?: any[]): Promise<any> {\r\n    return await this.dataSource.query(query, parameters);\r\n  }\r\n\r\n  protected async getTableRowCount(tableName: string): Promise<number> {\r\n    const result = await this.executeQuery(`SELECT COUNT(*) as count FROM ${tableName}`);\r\n    return parseInt(result[0].count, 10);\r\n  }\r\n\r\n  /**\r\n   * Validation utilities\r\n   */\r\n  protected expectValidResponse(response: request.Response, expectedStatus: number = 200): void {\r\n    expect(response.status).toBe(expectedStatus);\r\n    expect(response.body).toBeDefined();\r\n  }\r\n\r\n  protected expectValidationError(response: request.Response, field?: string): void {\r\n    expect(response.status).toBe(400);\r\n    expect(response.body.message).toBeDefined();\r\n    if (field) {\r\n      expect(response.body.message).toContain(field);\r\n    }\r\n  }\r\n\r\n  protected expectUnauthorized(response: request.Response): void {\r\n    expect(response.status).toBe(401);\r\n  }\r\n\r\n  protected expectForbidden(response: request.Response): void {\r\n    expect(response.status).toBe(403);\r\n  }\r\n\r\n  protected expectNotFound(response: request.Response): void {\r\n    expect(response.status).toBe(404);\r\n  }\r\n\r\n  /**\r\n   * Async utilities\r\n   */\r\n  protected async waitFor(condition: () => boolean | Promise<boolean>, timeout: number = 5000): Promise<void> {\r\n    const startTime = Date.now();\r\n    \r\n    while (Date.now() - startTime < timeout) {\r\n      const result = await condition();\r\n      if (result) {\r\n        return;\r\n      }\r\n      await this.sleep(100);\r\n    }\r\n    \r\n    throw new Error(`Condition not met within ${timeout}ms`);\r\n  }\r\n\r\n  protected sleep(ms: number): Promise<void> {\r\n    return new Promise(resolve => setTimeout(resolve, ms));\r\n  }\r\n\r\n  /**\r\n   * Mock utilities\r\n   */\r\n  protected createMockService(methods: string[]): any {\r\n    const mock: any = {};\r\n    methods.forEach(method => {\r\n      mock[method] = jest.fn();\r\n    });\r\n    return mock;\r\n  }\r\n\r\n  protected resetMocks(...mocks: any[]): void {\r\n    mocks.forEach(mock => {\r\n      if (mock && typeof mock.mockReset === 'function') {\r\n        mock.mockReset();\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Private helper methods\r\n   */\r\n  private generateTestToken(user: any): string {\r\n    // Simplified JWT token generation for testing\r\n    const payload = {\r\n      sub: user.id,\r\n      role: user.role,\r\n      permissions: user.permissions,\r\n      iat: Math.floor(Date.now() / 1000),\r\n      exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour\r\n    };\r\n\r\n    // In a real implementation, this would use proper JWT signing\r\n    return Buffer.from(JSON.stringify(payload)).toString('base64');\r\n  }\r\n}\r\n\r\n/**\r\n * Integration Test Decorators\r\n */\r\nexport function IntegrationTest(description: string) {\r\n  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {\r\n    const originalMethod = descriptor.value;\r\n    \r\n    descriptor.value = async function (...args: any[]) {\r\n      console.log(`Running integration test: ${description}`);\r\n      const startTime = Date.now();\r\n      \r\n      try {\r\n        const result = await originalMethod.apply(this, args);\r\n        const duration = Date.now() - startTime;\r\n        console.log(`Integration test completed: ${description} (${duration}ms)`);\r\n        return result;\r\n      } catch (error) {\r\n        const duration = Date.now() - startTime;\r\n        console.error(`Integration test failed: ${description} (${duration}ms)`, error);\r\n        throw error;\r\n      }\r\n    };\r\n    \r\n    return descriptor;\r\n  };\r\n}\r\n\r\n/**\r\n * Performance Test Decorator\r\n */\r\nexport function PerformanceTest(maxDuration: number) {\r\n  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {\r\n    const originalMethod = descriptor.value;\r\n    \r\n    descriptor.value = async function (...args: any[]) {\r\n      const startTime = Date.now();\r\n      const result = await originalMethod.apply(this, args);\r\n      const duration = Date.now() - startTime;\r\n      \r\n      if (duration > maxDuration) {\r\n        throw new Error(`Performance test failed: ${propertyName} took ${duration}ms, expected < ${maxDuration}ms`);\r\n      }\r\n      \r\n      return result;\r\n    };\r\n    \r\n    return descriptor;\r\n  };\r\n}\r\n"], "version": 3}