a050eeb539c5aca8474c38fb995108a3
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RateLimitGuard_1;
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RateLimitUtils = exports.RateLimitGuard = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const config_1 = require("@nestjs/config");
const rate_limit_decorator_1 = require("../decorators/rate-limit.decorator");
/**
 * In-memory rate limit store
 */
class MemoryRateLimitStore {
    constructor() {
        this.store = new Map();
        this.timers = new Map();
    }
    async get(key) {
        return this.store.get(key) || null;
    }
    async set(key, data, ttl) {
        this.store.set(key, data);
        // Clear existing timer
        const existingTimer = this.timers.get(key);
        if (existingTimer) {
            clearTimeout(existingTimer);
        }
        // Set new timer to clean up expired data
        const timer = setTimeout(() => {
            this.store.delete(key);
            this.timers.delete(key);
        }, ttl);
        this.timers.set(key, timer);
    }
    async increment(key, windowMs) {
        const now = Date.now();
        const existing = this.store.get(key);
        if (!existing || now > existing.resetTime) {
            // Create new window
            const data = {
                count: 1,
                resetTime: now + windowMs,
                firstRequest: now,
            };
            await this.set(key, data, windowMs);
            return data;
        }
        else {
            // Increment existing window
            existing.count++;
            await this.set(key, existing, existing.resetTime - now);
            return existing;
        }
    }
    async reset(key) {
        this.store.delete(key);
        const timer = this.timers.get(key);
        if (timer) {
            clearTimeout(timer);
            this.timers.delete(key);
        }
    }
}
/**
 * Rate Limit Guard
 * Implements rate limiting based on decorator configuration
 */
let RateLimitGuard = RateLimitGuard_1 = class RateLimitGuard {
    constructor(reflector, configService) {
        this.reflector = reflector;
        this.configService = configService;
        this.logger = new common_1.Logger(RateLimitGuard_1.name);
        // Initialize store based on configuration
        const storeType = this.configService.get('RATE_LIMIT_STORE', 'memory');
        this.store = this.createStore(storeType);
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const response = context.switchToHttp().getResponse();
        // Get rate limit configuration from decorator
        const rateLimitConfig = this.reflector.getAllAndOverride(rate_limit_decorator_1.RATE_LIMIT_KEY, [context.getHandler(), context.getClass()]);
        if (!rateLimitConfig) {
            return true; // No rate limiting configured
        }
        try {
            // Check if rate limiting should be skipped
            if (await this.shouldSkip(request, rateLimitConfig)) {
                return true;
            }
            // Generate rate limit key
            const key = await this.generateKey(request, rateLimitConfig);
            // Check rate limit
            const result = await this.checkRateLimit(key, rateLimitConfig);
            // Add rate limit headers
            if (rateLimitConfig.headers !== false) {
                this.addRateLimitHeaders(response, result, rateLimitConfig);
            }
            // Check if limit exceeded
            if (result.count > rateLimitConfig.max) {
                await this.handleLimitExceeded(request, rateLimitConfig, result);
                throw new common_1.HttpException({
                    error: 'RATE_LIMIT_EXCEEDED',
                    message: rateLimitConfig.message || 'Too many requests, please try again later.',
                    retryAfter: Math.ceil((result.resetTime - Date.now()) / 1000),
                }, common_1.HttpStatus.TOO_MANY_REQUESTS);
            }
            return true;
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            this.logger.error('Rate limit check failed', {
                error: error instanceof Error ? error.message : String(error),
                stack: error instanceof Error ? error.stack : undefined,
                url: request.url,
                method: request.method,
            });
            // Fail open - allow request if rate limiting fails
            return true;
        }
    }
    createStore(storeType) {
        switch (storeType) {
            case 'memory':
                return new MemoryRateLimitStore();
            case 'redis':
                // TODO: Implement Redis store
                this.logger.warn('Redis store not implemented, falling back to memory store');
                return new MemoryRateLimitStore();
            case 'database':
                // TODO: Implement database store
                this.logger.warn('Database store not implemented, falling back to memory store');
                return new MemoryRateLimitStore();
            default:
                return new MemoryRateLimitStore();
        }
    }
    async shouldSkip(request, config) {
        // Skip based on configuration
        if (config.skipSuccessfulRequests && request.method === 'GET') {
            return false; // We don't know if it's successful yet
        }
        // Skip for certain IPs (e.g., health checks)
        const skipIps = this.configService.get('RATE_LIMIT_SKIP_IPS', []);
        if (skipIps.includes(request.ip)) {
            return true;
        }
        // Skip for certain user agents (e.g., monitoring tools)
        const skipUserAgents = this.configService.get('RATE_LIMIT_SKIP_USER_AGENTS', []);
        const userAgent = request.headers['user-agent'] || '';
        if (skipUserAgents.some(agent => userAgent.includes(agent))) {
            return true;
        }
        return false;
    }
    async generateKey(request, config) {
        const keyGenerator = config.keyGenerator || 'ip';
        let baseKey = '';
        switch (keyGenerator) {
            case 'ip':
                baseKey = request.ip;
                break;
            case 'user':
                baseKey = request.user?.id || request.ip;
                break;
            case 'apiKey':
                baseKey = request.headers['x-api-key'] || request.ip;
                break;
            default:
                baseKey = request.ip;
        }
        // Include endpoint in key for endpoint-specific rate limiting
        const endpoint = `${request.method}:${request.route?.path || request.path}`;
        return `rate_limit:${keyGenerator}:${baseKey}:${endpoint}`;
    }
    async checkRateLimit(key, config) {
        return await this.store.increment(key, config.windowMs);
    }
    addRateLimitHeaders(response, result, config) {
        const remaining = Math.max(0, config.max - result.count);
        const resetTime = Math.ceil(result.resetTime / 1000);
        if (config.standardHeaders !== false) {
            // Standard rate limit headers (draft RFC)
            response.setHeader('RateLimit-Limit', config.max);
            response.setHeader('RateLimit-Remaining', remaining);
            response.setHeader('RateLimit-Reset', resetTime);
            response.setHeader('RateLimit-Window', Math.ceil(config.windowMs / 1000));
        }
        if (config.legacyHeaders !== false) {
            // Legacy headers for backward compatibility
            response.setHeader('X-RateLimit-Limit', config.max);
            response.setHeader('X-RateLimit-Remaining', remaining);
            response.setHeader('X-RateLimit-Reset', resetTime);
        }
        // Add retry-after header if limit exceeded
        if (result.count > config.max) {
            const retryAfter = Math.ceil((result.resetTime - Date.now()) / 1000);
            response.setHeader('Retry-After', retryAfter);
        }
    }
    async handleLimitExceeded(request, config, result) {
        // Log rate limit exceeded
        this.logger.warn('Rate limit exceeded', {
            url: request.url,
            method: request.method,
            ip: request.ip,
            userAgent: request.headers['user-agent'],
            userId: request.user?.id,
            count: result.count,
            limit: config.max,
            windowMs: config.windowMs,
            resetTime: new Date(result.resetTime).toISOString(),
        });
        // Call custom callback if configured
        if (config.onLimitReached) {
            try {
                // TODO: Implement callback mechanism
                this.logger.debug(`Rate limit callback: ${config.onLimitReached}`);
            }
            catch (error) {
                this.logger.error('Rate limit callback failed', {
                    callback: config.onLimitReached,
                    error: error instanceof Error ? error.message : String(error),
                });
            }
        }
    }
};
exports.RateLimitGuard = RateLimitGuard;
exports.RateLimitGuard = RateLimitGuard = RateLimitGuard_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof core_1.Reflector !== "undefined" && core_1.Reflector) === "function" ? _a : Object, typeof (_b = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _b : Object])
], RateLimitGuard);
/**
 * Rate limit utilities
 */
class RateLimitUtils {
    /**
     * Calculate rate limit window reset time
     */
    static calculateResetTime(windowMs) {
        return Date.now() + windowMs;
    }
    /**
     * Check if rate limit window has expired
     */
    static isWindowExpired(resetTime) {
        return Date.now() > resetTime;
    }
    /**
     * Calculate remaining requests
     */
    static calculateRemaining(count, max) {
        return Math.max(0, max - count);
    }
    /**
     * Calculate retry after seconds
     */
    static calculateRetryAfter(resetTime) {
        return Math.ceil((resetTime - Date.now()) / 1000);
    }
    /**
     * Format rate limit error message
     */
    static formatErrorMessage(config, result) {
        const retryAfter = this.calculateRetryAfter(result.resetTime);
        const baseMessage = config.message || 'Too many requests, please try again later.';
        return `${baseMessage} Retry after ${retryAfter} seconds.`;
    }
}
exports.RateLimitUtils = RateLimitUtils;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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