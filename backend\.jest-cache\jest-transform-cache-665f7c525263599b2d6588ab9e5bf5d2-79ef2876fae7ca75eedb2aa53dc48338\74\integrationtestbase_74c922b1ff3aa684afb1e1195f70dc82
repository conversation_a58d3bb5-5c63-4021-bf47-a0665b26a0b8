35e19450c7072d89b3ce4e7faf62d00e
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.IntegrationTestBase = void 0;
exports.IntegrationTest = IntegrationTest;
exports.PerformanceTest = PerformanceTest;
const typeorm_1 = require("@nestjs/typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const request = __importStar(require("supertest"));
const test_configuration_service_1 = require("../config/test-configuration.service");
const test_data_service_1 = require("../fixtures/test-data.service");
/**
 * Integration Test Base Class
 *
 * Comprehensive base class for integration testing providing:
 * - NestJS testing module setup and teardown
 * - Database transaction management for test isolation
 * - Authentication and authorization testing utilities
 * - HTTP request testing with supertest integration
 * - Event emission and handling validation
 * - Performance measurement and validation
 * - Cross-module integration testing support
 */
class IntegrationTestBase {
    constructor() {
        // Test utilities
        this.authTokens = new Map();
        this.testUsers = new Map();
        this.performanceMetrics = new Map();
        this.eventHistory = [];
    }
    /**
     * Setup test environment before each test
     */
    async beforeEach() {
        // Create testing module
        this.testingModule = await this.createTestingModule();
        // Initialize application
        this.app = this.testingModule.createNestApplication();
        await this.configureApplication();
        await this.app.init();
        // Get services
        this.dataSource = this.testingModule.get((0, typeorm_1.getDataSourceToken)());
        this.testConfigService = this.testingModule.get(test_configuration_service_1.TestConfigurationService);
        this.testDataService = this.testingModule.get(test_data_service_1.TestDataService);
        this.eventEmitter = this.testingModule.get(event_emitter_1.EventEmitter2);
        this.httpServer = this.app.getHttpServer();
        // Setup event monitoring
        this.setupEventMonitoring();
        // Setup test data
        await this.setupTestData();
        // Setup authentication
        await this.setupAuthentication();
    }
    /**
     * Cleanup test environment after each test
     */
    async afterEach() {
        try {
            // Clean test data
            await this.cleanupTestData();
            // Clear event history
            this.eventHistory = [];
            this.performanceMetrics.clear();
            // Close application
            if (this.app) {
                await this.app.close();
            }
            // Close testing module
            if (this.testingModule) {
                await this.testingModule.close();
            }
        }
        catch (error) {
            console.error('Error during test cleanup:', error);
        }
    }
    /**
     * Configure application with middleware, pipes, guards, etc.
     */
    async configureApplication() {
        // Configure global pipes, guards, interceptors
        // This can be overridden by subclasses for specific configurations
    }
    /**
     * Setup test data - can be overridden by subclasses
     */
    async setupTestData() {
        // Default implementation - can be overridden
    }
    /**
     * Setup authentication tokens for testing
     */
    async setupAuthentication() {
        // Create test users and tokens
        const testUsers = [
            { id: 'test-admin', role: 'admin', permissions: ['*'] },
            { id: 'test-user', role: 'user', permissions: ['workflow.view', 'workflow.execute'] },
            { id: 'test-viewer', role: 'viewer', permissions: ['workflow.view'] },
        ];
        for (const user of testUsers) {
            this.testUsers.set(user.id, user);
            // Generate JWT token for user (simplified)
            const token = this.generateTestToken(user);
            this.authTokens.set(user.id, token);
        }
    }
    /**
     * Setup event monitoring for testing
     */
    setupEventMonitoring() {
        // Monitor all events for testing
        const originalEmit = this.eventEmitter.emit.bind(this.eventEmitter);
        this.eventEmitter.emit = (event, ...args) => {
            this.eventHistory.push({
                event,
                args,
                timestamp: new Date(),
            });
            return originalEmit(event, ...args);
        };
    }
    /**
     * Cleanup test data
     */
    async cleanupTestData() {
        if (this.testDataService) {
            await this.testDataService.cleanTestData();
        }
    }
    /**
     * HTTP request helpers with authentication
     */
    authenticatedRequest(userId = 'test-user') {
        const token = this.authTokens.get(userId);
        return request(this.httpServer).set('Authorization', `Bearer ${token}`);
    }
    get(url, userId) {
        return this.authenticatedRequest(userId).get(url);
    }
    post(url, userId) {
        return this.authenticatedRequest(userId).post(url);
    }
    put(url, userId) {
        return this.authenticatedRequest(userId).put(url);
    }
    delete(url, userId) {
        return this.authenticatedRequest(userId).delete(url);
    }
    patch(url, userId) {
        return this.authenticatedRequest(userId).patch(url);
    }
    /**
     * Event testing utilities
     */
    waitForEvent(eventName, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new Error(`Event '${eventName}' not emitted within ${timeout}ms`));
            }, timeout);
            const checkEvent = () => {
                const event = this.eventHistory.find(e => e.event === eventName);
                if (event) {
                    clearTimeout(timeoutId);
                    resolve(event);
                }
                else {
                    setTimeout(checkEvent, 100);
                }
            };
            checkEvent();
        });
    }
    getEventHistory(eventName) {
        if (eventName) {
            return this.eventHistory.filter(e => e.event === eventName);
        }
        return [...this.eventHistory];
    }
    clearEventHistory() {
        this.eventHistory = [];
    }
    /**
     * Performance testing utilities
     */
    async measurePerformance(operation, operationName) {
        const startTime = Date.now();
        const result = await operation();
        const duration = Date.now() - startTime;
        // Store performance metric
        if (!this.performanceMetrics.has(operationName)) {
            this.performanceMetrics.set(operationName, []);
        }
        this.performanceMetrics.get(operationName).push(duration);
        return { result, duration };
    }
    getPerformanceStats(operationName) {
        const metrics = this.performanceMetrics.get(operationName);
        if (!metrics || metrics.length === 0) {
            return null;
        }
        const sorted = [...metrics].sort((a, b) => a - b);
        const count = sorted.length;
        const min = sorted[0];
        const max = sorted[count - 1];
        const avg = sorted.reduce((sum, val) => sum + val, 0) / count;
        const p95Index = Math.floor(count * 0.95);
        const p95 = sorted[p95Index];
        return { count, min, max, avg, p95 };
    }
    /**
     * Database testing utilities
     */
    async withTransaction(operation) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const result = await operation(queryRunner.manager);
            await queryRunner.commitTransaction();
            return result;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async executeQuery(query, parameters) {
        return await this.dataSource.query(query, parameters);
    }
    async getTableRowCount(tableName) {
        const result = await this.executeQuery(`SELECT COUNT(*) as count FROM ${tableName}`);
        return parseInt(result[0].count, 10);
    }
    /**
     * Validation utilities
     */
    expectValidResponse(response, expectedStatus = 200) {
        expect(response.status).toBe(expectedStatus);
        expect(response.body).toBeDefined();
    }
    expectValidationError(response, field) {
        expect(response.status).toBe(400);
        expect(response.body.message).toBeDefined();
        if (field) {
            expect(response.body.message).toContain(field);
        }
    }
    expectUnauthorized(response) {
        expect(response.status).toBe(401);
    }
    expectForbidden(response) {
        expect(response.status).toBe(403);
    }
    expectNotFound(response) {
        expect(response.status).toBe(404);
    }
    /**
     * Async utilities
     */
    async waitFor(condition, timeout = 5000) {
        const startTime = Date.now();
        while (Date.now() - startTime < timeout) {
            const result = await condition();
            if (result) {
                return;
            }
            await this.sleep(100);
        }
        throw new Error(`Condition not met within ${timeout}ms`);
    }
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    /**
     * Mock utilities
     */
    createMockService(methods) {
        const mock = {};
        methods.forEach(method => {
            mock[method] = jest.fn();
        });
        return mock;
    }
    resetMocks(...mocks) {
        mocks.forEach(mock => {
            if (mock && typeof mock.mockReset === 'function') {
                mock.mockReset();
            }
        });
    }
    /**
     * Private helper methods
     */
    generateTestToken(user) {
        // Simplified JWT token generation for testing
        const payload = {
            sub: user.id,
            role: user.role,
            permissions: user.permissions,
            iat: Math.floor(Date.now() / 1000),
            exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour
        };
        // In a real implementation, this would use proper JWT signing
        return Buffer.from(JSON.stringify(payload)).toString('base64');
    }
}
exports.IntegrationTestBase = IntegrationTestBase;
/**
 * Integration Test Decorators
 */
function IntegrationTest(description) {
    return function (target, propertyName, descriptor) {
        const originalMethod = descriptor.value;
        descriptor.value = async function (...args) {
            console.log(`Running integration test: ${description}`);
            const startTime = Date.now();
            try {
                const result = await originalMethod.apply(this, args);
                const duration = Date.now() - startTime;
                console.log(`Integration test completed: ${description} (${duration}ms)`);
                return result;
            }
            catch (error) {
                const duration = Date.now() - startTime;
                console.error(`Integration test failed: ${description} (${duration}ms)`, error);
                throw error;
            }
        };
        return descriptor;
    };
}
/**
 * Performance Test Decorator
 */
function PerformanceTest(maxDuration) {
    return function (target, propertyName, descriptor) {
        const originalMethod = descriptor.value;
        descriptor.value = async function (...args) {
            const startTime = Date.now();
            const result = await originalMethod.apply(this, args);
            const duration = Date.now() - startTime;
            if (duration > maxDuration) {
                throw new Error(`Performance test failed: ${propertyName} took ${duration}ms, expected < ${maxDuration}ms`);
            }
            return result;
        };
        return descriptor;
    };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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