2fc467166e4fe8dfc1951659265b71ca
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.configFactory = exports.getDefaultConfig = exports.validateConfig = exports.ConfigValidationService = exports.AppConfig = void 0;
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
// Utility type for boolean transformation
const BooleanTransform = () => (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true);
const NumberTransform = () => (0, class_transformer_1.Transform)(({ value }) => parseInt(value, 10));
// Environment enum
var Environment;
(function (Environment) {
    Environment["DEVELOPMENT"] = "development";
    Environment["STAGING"] = "staging";
    Environment["PRODUCTION"] = "production";
})(Environment || (Environment = {}));
// Log level enum
var LogLevel;
(function (LogLevel) {
    LogLevel["ERROR"] = "error";
    LogLevel["WARN"] = "warn";
    LogLevel["INFO"] = "info";
    LogLevel["DEBUG"] = "debug";
    LogLevel["VERBOSE"] = "verbose";
})(LogLevel || (LogLevel = {}));
class DatabaseConfig {
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DatabaseConfig.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DatabaseConfig.prototype, "host", void 0);
__decorate([
    (0, class_validator_1.IsPort)(),
    NumberTransform(),
    __metadata("design:type", Number)
], DatabaseConfig.prototype, "port", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DatabaseConfig.prototype, "username", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DatabaseConfig.prototype, "password", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DatabaseConfig.prototype, "database", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    BooleanTransform(),
    __metadata("design:type", Boolean)
], DatabaseConfig.prototype, "synchronize", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    BooleanTransform(),
    __metadata("design:type", Boolean)
], DatabaseConfig.prototype, "logging", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DatabaseConfig.prototype, "ssl", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    NumberTransform(),
    __metadata("design:type", Number)
], DatabaseConfig.prototype, "maxConnections", void 0);
class RedisConfig {
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RedisConfig.prototype, "host", void 0);
__decorate([
    (0, class_validator_1.IsPort)(),
    NumberTransform(),
    __metadata("design:type", Number)
], RedisConfig.prototype, "port", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RedisConfig.prototype, "password", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    NumberTransform(),
    __metadata("design:type", Number)
], RedisConfig.prototype, "db", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    NumberTransform(),
    __metadata("design:type", Number)
], RedisConfig.prototype, "ttl", void 0);
class OAuthProviderConfig {
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OAuthProviderConfig.prototype, "clientId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OAuthProviderConfig.prototype, "clientSecret", void 0);
__decorate([
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], OAuthProviderConfig.prototype, "callbackUrl", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OAuthProviderConfig.prototype, "tenant", void 0);
class JwtConfig {
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], JwtConfig.prototype, "secret", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], JwtConfig.prototype, "expiresIn", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], JwtConfig.prototype, "refreshSecret", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], JwtConfig.prototype, "refreshExpiresIn", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], JwtConfig.prototype, "issuer", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], JwtConfig.prototype, "audience", void 0);
class OAuthConfig {
}
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => OAuthProviderConfig),
    __metadata("design:type", OAuthProviderConfig)
], OAuthConfig.prototype, "google", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => OAuthProviderConfig),
    __metadata("design:type", OAuthProviderConfig)
], OAuthConfig.prototype, "microsoft", void 0);
class SamlConfig {
}
__decorate([
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], SamlConfig.prototype, "entryPoint", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SamlConfig.prototype, "issuer", void 0);
__decorate([
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], SamlConfig.prototype, "callbackUrl", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SamlConfig.prototype, "cert", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SamlConfig.prototype, "privateCert", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SamlConfig.prototype, "identifierFormat", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SamlConfig.prototype, "signatureAlgorithm", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SamlConfig.prototype, "digestAlgorithm", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    NumberTransform(),
    __metadata("design:type", Number)
], SamlConfig.prototype, "acceptedClockSkewMs", void 0);
class TokenConfig {
}
__decorate([
    (0, class_validator_1.IsNumber)(),
    NumberTransform(),
    __metadata("design:type", Number)
], TokenConfig.prototype, "tokenExpiryMinutes", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    NumberTransform(),
    __metadata("design:type", Number)
], TokenConfig.prototype, "rateLimitMinutes", void 0);
class EmailVerificationConfig {
}
__decorate([
    (0, class_validator_1.IsNumber)(),
    NumberTransform(),
    __metadata("design:type", Number)
], EmailVerificationConfig.prototype, "tokenExpiryHours", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    NumberTransform(),
    __metadata("design:type", Number)
], EmailVerificationConfig.prototype, "rateLimitMinutes", void 0);
class SessionConfig {
}
__decorate([
    (0, class_validator_1.IsNumber)(),
    NumberTransform(),
    __metadata("design:type", Number)
], SessionConfig.prototype, "maxConcurrentSessions", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    NumberTransform(),
    __metadata("design:type", Number)
], SessionConfig.prototype, "inactivityTimeoutMinutes", void 0);
class AuthConfig {
}
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => JwtConfig),
    __metadata("design:type", JwtConfig)
], AuthConfig.prototype, "jwt", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => OAuthConfig),
    __metadata("design:type", OAuthConfig)
], AuthConfig.prototype, "oauth", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => SamlConfig),
    __metadata("design:type", SamlConfig)
], AuthConfig.prototype, "saml", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => TokenConfig),
    __metadata("design:type", TokenConfig)
], AuthConfig.prototype, "passwordReset", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => EmailVerificationConfig),
    __metadata("design:type", EmailVerificationConfig)
], AuthConfig.prototype, "emailVerification", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => SessionConfig),
    __metadata("design:type", SessionConfig)
], AuthConfig.prototype, "session", void 0);
class CorsConfig {
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", Object)
], CorsConfig.prototype, "origin", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    BooleanTransform(),
    __metadata("design:type", Boolean)
], CorsConfig.prototype, "credentials", void 0);
__decorate([
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CorsConfig.prototype, "methods", void 0);
__decorate([
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CorsConfig.prototype, "allowedHeaders", void 0);
class RateLimitConfig {
}
__decorate([
    (0, class_validator_1.IsBoolean)(),
    BooleanTransform(),
    __metadata("design:type", Boolean)
], RateLimitConfig.prototype, "enabled", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    NumberTransform(),
    __metadata("design:type", Number)
], RateLimitConfig.prototype, "windowMs", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    NumberTransform(),
    __metadata("design:type", Number)
], RateLimitConfig.prototype, "max", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RateLimitConfig.prototype, "message", void 0);
class IpFilterConfig {
}
__decorate([
    (0, class_validator_1.IsBoolean)(),
    BooleanTransform(),
    __metadata("design:type", Boolean)
], IpFilterConfig.prototype, "enabled", void 0);
__decorate([
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], IpFilterConfig.prototype, "whitelist", void 0);
__decorate([
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], IpFilterConfig.prototype, "blacklist", void 0);
class SecurityHeadersConfig {
}
__decorate([
    (0, class_validator_1.IsBoolean)(),
    BooleanTransform(),
    __metadata("design:type", Boolean)
], SecurityHeadersConfig.prototype, "hsts", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    BooleanTransform(),
    __metadata("design:type", Boolean)
], SecurityHeadersConfig.prototype, "noSniff", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    BooleanTransform(),
    __metadata("design:type", Boolean)
], SecurityHeadersConfig.prototype, "xssProtection", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SecurityHeadersConfig.prototype, "referrerPolicy", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SecurityHeadersConfig.prototype, "contentSecurityPolicy", void 0);
class SecurityConfig {
}
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => CorsConfig),
    __metadata("design:type", CorsConfig)
], SecurityConfig.prototype, "cors", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => RateLimitConfig),
    __metadata("design:type", RateLimitConfig)
], SecurityConfig.prototype, "rateLimit", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => RateLimitConfig),
    __metadata("design:type", RateLimitConfig)
], SecurityConfig.prototype, "apiRateLimit", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => IpFilterConfig),
    __metadata("design:type", IpFilterConfig)
], SecurityConfig.prototype, "ipFilter", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => SecurityHeadersConfig),
    __metadata("design:type", SecurityHeadersConfig)
], SecurityConfig.prototype, "headers", void 0);
class AIServiceConfig {
}
__decorate([
    (0, class_validator_1.IsBoolean)(),
    BooleanTransform(),
    __metadata("design:type", Boolean)
], AIServiceConfig.prototype, "enabled", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AIServiceConfig.prototype, "provider", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AIServiceConfig.prototype, "apiKey", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], AIServiceConfig.prototype, "baseUrl", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    NumberTransform(),
    __metadata("design:type", Number)
], AIServiceConfig.prototype, "timeout", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    NumberTransform(),
    __metadata("design:type", Number)
], AIServiceConfig.prototype, "retries", void 0);
class AIFeaturesConfig {
}
__decorate([
    (0, class_validator_1.IsBoolean)(),
    BooleanTransform(),
    __metadata("design:type", Boolean)
], AIFeaturesConfig.prototype, "vulnerabilityAnalysis", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    BooleanTransform(),
    __metadata("design:type", Boolean)
], AIFeaturesConfig.prototype, "threatIntelligence", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    BooleanTransform(),
    __metadata("design:type", Boolean)
], AIFeaturesConfig.prototype, "riskAssessment", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    BooleanTransform(),
    __metadata("design:type", Boolean)
], AIFeaturesConfig.prototype, "reportGeneration", void 0);
class AILimitsConfig {
}
__decorate([
    (0, class_validator_1.IsNumber)(),
    NumberTransform(),
    __metadata("design:type", Number)
], AILimitsConfig.prototype, "requestsPerMinute", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    NumberTransform(),
    __metadata("design:type", Number)
], AILimitsConfig.prototype, "maxTokens", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    NumberTransform(),
    __metadata("design:type", Number)
], AILimitsConfig.prototype, "maxFileSize", void 0);
class AIConfig {
}
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => AIServiceConfig),
    __metadata("design:type", AIServiceConfig)
], AIConfig.prototype, "service", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => AIFeaturesConfig),
    __metadata("design:type", AIFeaturesConfig)
], AIConfig.prototype, "features", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => AILimitsConfig),
    __metadata("design:type", AILimitsConfig)
], AIConfig.prototype, "limits", void 0);
class LogStorageConfig {
}
__decorate([
    (0, class_validator_1.IsBoolean)(),
    BooleanTransform(),
    __metadata("design:type", Boolean)
], LogStorageConfig.prototype, "enabled", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], LogStorageConfig.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    NumberTransform(),
    __metadata("design:type", Number)
], LogStorageConfig.prototype, "retention", void 0);
class AuditConfig {
}
__decorate([
    (0, class_validator_1.IsBoolean)(),
    BooleanTransform(),
    __metadata("design:type", Boolean)
], AuditConfig.prototype, "enabled", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => LogStorageConfig),
    __metadata("design:type", LogStorageConfig)
], AuditConfig.prototype, "storage", void 0);
class LoggingConfig {
}
__decorate([
    (0, class_validator_1.IsEnum)(LogLevel),
    __metadata("design:type", String)
], LoggingConfig.prototype, "level", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    BooleanTransform(),
    __metadata("design:type", Boolean)
], LoggingConfig.prototype, "console", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    BooleanTransform(),
    __metadata("design:type", Boolean)
], LoggingConfig.prototype, "file", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], LoggingConfig.prototype, "filePath", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    BooleanTransform(),
    __metadata("design:type", Boolean)
], LoggingConfig.prototype, "json", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    BooleanTransform(),
    __metadata("design:type", Boolean)
], LoggingConfig.prototype, "timestamp", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => AuditConfig),
    __metadata("design:type", AuditConfig)
], LoggingConfig.prototype, "audit", void 0);
class EmailTemplatesConfig {
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], EmailTemplatesConfig.prototype, "passwordReset", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], EmailTemplatesConfig.prototype, "emailVerification", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], EmailTemplatesConfig.prototype, "welcomeEmail", void 0);
class EmailConfig {
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], EmailConfig.prototype, "host", void 0);
__decorate([
    (0, class_validator_1.IsPort)(),
    NumberTransform(),
    __metadata("design:type", Number)
], EmailConfig.prototype, "port", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    BooleanTransform(),
    __metadata("design:type", Boolean)
], EmailConfig.prototype, "secure", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], EmailConfig.prototype, "username", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], EmailConfig.prototype, "password", void 0);
__decorate([
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], EmailConfig.prototype, "from", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], EmailConfig.prototype, "replyTo", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => EmailTemplatesConfig),
    __metadata("design:type", EmailTemplatesConfig)
], EmailConfig.prototype, "templates", void 0);
class AppConfig {
}
exports.AppConfig = AppConfig;
__decorate([
    (0, class_validator_1.IsEnum)(Environment),
    __metadata("design:type", String)
], AppConfig.prototype, "NODE_ENV", void 0);
__decorate([
    (0, class_validator_1.IsPort)(),
    NumberTransform(),
    __metadata("design:type", Number)
], AppConfig.prototype, "PORT", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AppConfig.prototype, "APP_NAME", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AppConfig.prototype, "APP_VERSION", void 0);
__decorate([
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], AppConfig.prototype, "APP_URL", void 0);
__decorate([
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], AppConfig.prototype, "API_URL", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AppConfig.prototype, "API_PREFIX", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => DatabaseConfig),
    __metadata("design:type", DatabaseConfig)
], AppConfig.prototype, "database", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => RedisConfig),
    __metadata("design:type", RedisConfig)
], AppConfig.prototype, "redis", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => AuthConfig),
    __metadata("design:type", AuthConfig)
], AppConfig.prototype, "auth", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => SecurityConfig),
    __metadata("design:type", SecurityConfig)
], AppConfig.prototype, "security", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => AIConfig),
    __metadata("design:type", AIConfig)
], AppConfig.prototype, "ai", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => LoggingConfig),
    __metadata("design:type", LoggingConfig)
], AppConfig.prototype, "logging", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => EmailConfig),
    __metadata("design:type", EmailConfig)
], AppConfig.prototype, "email", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AppConfig.prototype, "SENTRY_DSN", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    BooleanTransform(),
    __metadata("design:type", Boolean)
], AppConfig.prototype, "ENABLE_SWAGGER", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    BooleanTransform(),
    __metadata("design:type", Boolean)
], AppConfig.prototype, "ENABLE_METRICS", void 0);
let ConfigValidationService = class ConfigValidationService {
    constructor(configService) {
        this.configService = configService;
    }
    validate(config) {
        const validatedConfig = (0, class_transformer_1.plainToClass)(AppConfig, config, {
            enableImplicitConversion: true,
            excludeExtraneousValues: true,
        });
        const errors = (0, class_validator_1.validateSync)(validatedConfig, {
            skipMissingProperties: false,
            whitelist: true,
            forbidNonWhitelisted: true,
        });
        if (errors.length > 0) {
            throw new Error(`Configuration validation failed: ${this.formatErrors(errors)}`);
        }
        return validatedConfig;
    }
    formatErrors(errors) {
        return errors
            .map(error => this.formatError(error))
            .filter(Boolean)
            .join('; ');
    }
    formatError(error, path = '') {
        const currentPath = path ? `${path}.${error.property}` : error.property;
        if (error.constraints) {
            return `${currentPath}: ${Object.values(error.constraints).join(', ')}`;
        }
        if (error.children?.length) {
            return error.children
                .map(child => this.formatError(child, currentPath))
                .filter(Boolean)
                .join('; ');
        }
        return '';
    }
};
exports.ConfigValidationService = ConfigValidationService;
exports.ConfigValidationService = ConfigValidationService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], ConfigValidationService);
const validateConfig = (config) => {
    const validatedConfig = (0, class_transformer_1.plainToClass)(AppConfig, config, {
        enableImplicitConversion: true,
        excludeExtraneousValues: true,
    });
    const errors = (0, class_validator_1.validateSync)(validatedConfig, {
        skipMissingProperties: false,
        whitelist: true,
        forbidNonWhitelisted: true,
    });
    if (errors.length > 0) {
        const errorMessages = errors
            .map(error => {
            const constraints = error.constraints;
            return constraints ? Object.values(constraints).join(', ') : 'Unknown validation error';
        })
            .filter(Boolean);
        throw new Error(`Configuration validation failed: ${errorMessages.join('; ')}`);
    }
    return validatedConfig;
};
exports.validateConfig = validateConfig;
const getDefaultConfig = () => ({
    NODE_ENV: Environment.DEVELOPMENT,
    PORT: 3000,
    APP_NAME: 'Sentinel',
    APP_VERSION: '1.0.0',
    API_PREFIX: 'api/v1',
    ENABLE_SWAGGER: true,
    ENABLE_METRICS: true,
});
exports.getDefaultConfig = getDefaultConfig;
// NestJS Configuration Factory
const configFactory = () => {
    return (0, exports.validateConfig)(process.env);
};
exports.configFactory = configFactory;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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