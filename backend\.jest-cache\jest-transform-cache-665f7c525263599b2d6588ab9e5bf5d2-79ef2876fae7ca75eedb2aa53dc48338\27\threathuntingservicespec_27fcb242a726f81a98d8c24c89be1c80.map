{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\application\\services\\threat-hunting.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,qEAAoF;AACpF,kIAAmH;AACnH,gIAAkH;AAClH,sHAAwG;AACxG,4HAA8G;AAC9G,iGAA4F;AAC5F,oFAA2E;AAC3E,kFAAyE;AAEzE,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;IACpC,IAAI,OAA6B,CAAC;IAClC,IAAI,2BAAiE,CAAC;IACtE,IAAI,2BAAiE,CAAC;IACtE,IAAI,sBAAuD,CAAC;IAC5D,IAAI,yBAA6D,CAAC;IAClE,IAAI,kBAAqD,CAAC;IAE1D,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,2BAA2B,GAAG;YAC5B,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;YACpB,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;YACzB,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;SAClB,CAAC;QAET,2BAA2B,GAAG;YAC5B,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;SAClB,CAAC;QAET,sBAAsB,GAAG;YACvB,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;SAClB,CAAC;QAET,yBAAyB,GAAG;YAC1B,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;SAClB,CAAC;QAET,kBAAkB,GAAG;YACnB,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;SACf,CAAC;QAET,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,6CAAoB;gBACpB;oBACE,OAAO,EAAE,6DAAuB;oBAChC,QAAQ,EAAE,2BAA2B;iBACtC;gBACD;oBACE,OAAO,EAAE,4DAAuB;oBAChC,QAAQ,EAAE,2BAA2B;iBACtC;gBACD;oBACE,OAAO,EAAE,kDAAkB;oBAC3B,QAAQ,EAAE,sBAAsB;iBACjC;gBACD;oBACE,OAAO,EAAE,wDAAqB;oBAC9B,QAAQ,EAAE,yBAAyB;iBACpC;gBACD;oBACE,OAAO,EAAE,6CAAoB;oBAC7B,QAAQ,EAAE,kBAAkB;iBAC7B;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAAuB,6CAAoB,CAAC,CAAC;IACnE,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,KAAK,GAAuB;gBAChC,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,wBAAwB;gBACrC,IAAI,EAAE,YAAY;gBAClB,UAAU,EAAE;oBACV,SAAS,EAAE;wBACT,IAAI,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;wBAC5B,EAAE,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;qBAC3B;oBACD,QAAQ,EAAE,EAAE;oBACZ,IAAI,EAAE;wBACJ;4BACE,IAAI,EAAE,IAAI;4BACV,KAAK,EAAE,eAAe;4BACtB,UAAU,EAAE,uCAAe,CAAC,IAAI;yBACjC;wBACD;4BACE,IAAI,EAAE,QAAQ;4BACd,KAAK,EAAE,uBAAuB;4BAC9B,UAAU,EAAE,uCAAe,CAAC,MAAM;yBACnC;qBACF;iBACF;gBACD,QAAQ,EAAE,MAAM;gBAChB,eAAe,EAAE;oBACf,gBAAgB,EAAE,EAAE;oBACpB,mBAAmB,EAAE,uCAAe,CAAC,MAAM;oBAC3C,iBAAiB,EAAE,GAAG;iBACvB;aACF,CAAC;YAEF,MAAM,UAAU,GAAG;gBACjB,uBAAuB,CAAC;oBACtB,EAAE,EAAE,SAAS;oBACb,QAAQ,EAAE,eAAe;oBACzB,QAAQ,EAAE,qCAAc,CAAC,IAAI;iBAC9B,CAAC;gBACF,uBAAuB,CAAC;oBACtB,EAAE,EAAE,SAAS;oBACb,MAAM,EAAE,uBAAuB;oBAC/B,QAAQ,EAAE,qCAAc,CAAC,MAAM;iBAChC,CAAC;aACH,CAAC;YAEF,2BAA2B,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7E,2BAA2B,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAE7E,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAEhD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,uCAAe,CAAC,IAAI,CAAC,CAAC;YACjE,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACzE,MAAM,CAAC,2BAA2B,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,KAAK,GAAuB;gBAChC,IAAI,EAAE,0BAA0B;gBAChC,WAAW,EAAE,0BAA0B;gBACvC,IAAI,EAAE,qBAAqB;gBAC3B,UAAU,EAAE;oBACV,SAAS,EAAE;wBACT,IAAI,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;wBAC5B,EAAE,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;qBAC3B;oBACD,QAAQ,EAAE,EAAE;oBACZ,QAAQ,EAAE;wBACR;4BACE,OAAO,EAAE,qBAAqB;4BAC9B,SAAS,EAAE,CAAC;4BACZ,UAAU,EAAE,IAAI;yBACjB;qBACF;iBACF;gBACD,QAAQ,EAAE,QAAQ;gBAClB,eAAe,EAAE;oBACf,gBAAgB,EAAE,CAAC;oBACnB,mBAAmB,EAAE,uCAAe,CAAC,MAAM;oBAC3C,iBAAiB,EAAE,GAAG;iBACvB;aACF,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAEhD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,KAAK,GAAuB;gBAChC,IAAI,EAAE,wBAAwB;gBAC9B,WAAW,EAAE,wBAAwB;gBACrC,IAAI,EAAE,mBAAmB;gBACzB,UAAU,EAAE;oBACV,SAAS,EAAE;wBACT,IAAI,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;wBAC5B,EAAE,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;qBAC3B;oBACD,QAAQ,EAAE;wBACR,gBAAgB,EAAE,GAAG;wBACrB,cAAc,EAAE,CAAC;qBAClB;iBACF;gBACD,QAAQ,EAAE,KAAK;gBACf,eAAe,EAAE;oBACf,gBAAgB,EAAE,CAAC;oBACnB,mBAAmB,EAAE,uCAAe,CAAC,GAAG;oBACxC,iBAAiB,EAAE,GAAG;iBACvB;aACF,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAEhD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,MAAM,KAAK,GAAuB;gBAChC,IAAI,EAAE,uBAAuB;gBAC7B,WAAW,EAAE,uBAAuB;gBACpC,IAAI,EAAE,kBAAkB;gBACxB,UAAU,EAAE;oBACV,SAAS,EAAE;wBACT,IAAI,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;wBAC5B,EAAE,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;qBAC3B;oBACD,QAAQ,EAAE;wBACR,QAAQ,EAAE,CAAC,qBAAqB,EAAE,aAAa,CAAC;qBACjD;iBACF;gBACD,QAAQ,EAAE,MAAM;gBAChB,eAAe,EAAE;oBACf,gBAAgB,EAAE,CAAC;oBACnB,mBAAmB,EAAE,uCAAe,CAAC,IAAI;oBACzC,iBAAiB,EAAE,IAAI;iBACxB;aACF,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAEhD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,KAAK,GAAuB;gBAChC,IAAI,EAAE,wBAAwB;gBAC9B,WAAW,EAAE,wBAAwB;gBACrC,IAAI,EAAE,mBAAmB;gBACzB,UAAU,EAAE;oBACV,SAAS,EAAE;wBACT,IAAI,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;wBAC5B,EAAE,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;qBAC3B;oBACD,QAAQ,EAAE;wBACR,gBAAgB,EAAE,CAAC,kBAAkB,EAAE,sBAAsB,CAAC;qBAC/D;iBACF;gBACD,QAAQ,EAAE,UAAU;gBACpB,eAAe,EAAE;oBACf,gBAAgB,EAAE,CAAC;oBACnB,mBAAmB,EAAE,uCAAe,CAAC,IAAI;oBACzC,iBAAiB,EAAE,IAAI;iBACxB;aACF,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAEhD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,MAAM,KAAK,GAAuB;gBAChC,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,qBAAqB;gBAClC,IAAI,EAAE,YAAY;gBAClB,UAAU,EAAE;oBACV,SAAS,EAAE;wBACT,IAAI,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;wBAC5B,EAAE,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;qBAC3B;oBACD,QAAQ,EAAE,EAAE;oBACZ,IAAI,EAAE;wBACJ;4BACE,IAAI,EAAE,IAAI;4BACV,KAAK,EAAE,eAAe;4BACtB,UAAU,EAAE,uCAAe,CAAC,IAAI;yBACjC;qBACF;iBACF;gBACD,QAAQ,EAAE,MAAM;gBAChB,eAAe,EAAE;oBACf,gBAAgB,EAAE,EAAE;oBACpB,mBAAmB,EAAE,uCAAe,CAAC,MAAM;oBAC3C,iBAAiB,EAAE,GAAG;iBACvB;aACF,CAAC;YAEF,2BAA2B,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAErF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAEhD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,MAAM,KAAK,GAAuB;gBAChC,IAAI,EAAE,kBAAkB;gBACxB,WAAW,EAAE,uBAAuB;gBACpC,IAAI,EAAE,kBAAyB;gBAC/B,UAAU,EAAE;oBACV,SAAS,EAAE;wBACT,IAAI,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;wBAC5B,EAAE,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;qBAC3B;oBACD,QAAQ,EAAE,EAAE;iBACb;gBACD,QAAQ,EAAE,KAAK;gBACf,eAAe,EAAE;oBACf,gBAAgB,EAAE,CAAC;oBACnB,mBAAmB,EAAE,uCAAe,CAAC,GAAG;oBACxC,iBAAiB,EAAE,CAAC;iBACrB;aACF,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAEhD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,KAAK,GAAuB;gBAChC,IAAI,EAAE,mBAAmB;gBACzB,WAAW,EAAE,kCAAkC;gBAC/C,IAAI,EAAE,YAAY;gBAClB,UAAU,EAAE;oBACV,SAAS,EAAE;wBACT,IAAI,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;wBAC5B,EAAE,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;qBAC3B;oBACD,QAAQ,EAAE,EAAE;oBACZ,IAAI,EAAE;wBACJ;4BACE,IAAI,EAAE,IAAI;4BACV,KAAK,EAAE,eAAe;4BACtB,UAAU,EAAE,uCAAe,CAAC,IAAI;yBACjC;qBACF;iBACF;gBACD,QAAQ,EAAE,QAAQ;gBAClB,eAAe,EAAE;oBACf,gBAAgB,EAAE,CAAC;oBACnB,mBAAmB,EAAE,uCAAe,CAAC,MAAM;oBAC3C,iBAAiB,EAAE,GAAG;iBACvB;aACF,CAAC;YAEF,MAAM,SAAS,GAAG,uBAAuB,CAAC;gBACxC,EAAE,EAAE,SAAS;gBACb,QAAQ,EAAE,eAAe;gBACzB,QAAQ,EAAE,qCAAc,CAAC,IAAI;aAC9B,CAAC,CAAC;YAEH,2BAA2B,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YAErE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAEhD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,KAAK,GAAuB;gBAChC,IAAI,EAAE,oBAAoB;gBAC1B,WAAW,EAAE,+BAA+B;gBAC5C,IAAI,EAAE,YAAY;gBAClB,UAAU,EAAE;oBACV,SAAS,EAAE;wBACT,IAAI,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;wBAC5B,EAAE,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;qBAC3B;oBACD,QAAQ,EAAE,EAAE;oBACZ,IAAI,EAAE;wBACJ;4BACE,IAAI,EAAE,IAAI;4BACV,KAAK,EAAE,eAAe;4BACtB,UAAU,EAAE,uCAAe,CAAC,IAAI;yBACjC;qBACF;iBACF;gBACD,QAAQ,EAAE,MAAM;gBAChB,eAAe,EAAE;oBACf,gBAAgB,EAAE,CAAC;oBACnB,mBAAmB,EAAE,uCAAe,CAAC,IAAI;oBACzC,iBAAiB,EAAE,IAAI;iBACxB;aACF,CAAC;YAEF,MAAM,SAAS,GAAG,uBAAuB,CAAC;gBACxC,EAAE,EAAE,SAAS;gBACb,QAAQ,EAAE,eAAe;gBACzB,QAAQ,EAAE,qCAAc,CAAC,IAAI;gBAC7B,SAAS,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;aAC5C,CAAC,CAAC;YAEH,2BAA2B,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YAErE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAEhD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACjE,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAChE,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAChE,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAC/D,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;QACnE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,oBAAoB,EAAE,CAAC;YAErD,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAC/D,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAC3D,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,oBAAoB,EAAE,CAAC;YAErD,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACtB,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;gBAC7D,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;gBAC3D,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,YAAY,CAC5D,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,CACxC,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,oBAAoB,EAAE,CAAC;YAErD,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACtB,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;gBAClE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,uCAAe,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC;gBAC5F,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;gBAC1E,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;YACzE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,KAAK,GAAuB;gBAChC,IAAI,EAAE,uBAAuB;gBAC7B,WAAW,EAAE,uBAAuB;gBACpC,IAAI,EAAE,YAAY;gBAClB,UAAU,EAAE;oBACV,SAAS,EAAE;wBACT,IAAI,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;wBAC5B,EAAE,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;qBAC3B;oBACD,QAAQ,EAAE,EAAE;oBACZ,IAAI,EAAE;wBACJ;4BACE,IAAI,EAAE,IAAI;4BACV,KAAK,EAAE,eAAe;4BACtB,UAAU,EAAE,uCAAe,CAAC,MAAM;yBACnC;qBACF;iBACF;gBACD,QAAQ,EAAE,QAAQ;gBAClB,eAAe,EAAE;oBACf,gBAAgB,EAAE,CAAC;oBACnB,mBAAmB,EAAE,uCAAe,CAAC,MAAM;oBAC3C,iBAAiB,EAAE,GAAG;iBACvB;aACF,CAAC;YAEF,2BAA2B,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YAE5D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAExC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAClD,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,mCAAmC;YACxE,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,iDAAiD;IACjD,SAAS,uBAAuB,CAAC,OAMhC;QACC,OAAO;YACL,EAAE,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE;YAClC,MAAM,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE;YAC/B,SAAS,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE,EAAE;YACrD,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,OAAO,EAAE;gBACP,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,MAAM,EAAE,OAAO,CAAC,MAAM;aACvB;YACD,cAAc,EAAE;gBACd;oBACE,OAAO,EAAE,SAAS;oBAClB,SAAS,EAAE,YAAY;iBACxB;aACF;SACF,CAAC;IACJ,CAAC;AACH,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\application\\services\\threat-hunting.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { ThreatHuntingService, ThreatHuntingQuery } from './threat-hunting.service';\r\nimport { SecurityEventRepository } from '../../domain/interfaces/repositories/security-event.repository.interface';\r\nimport { VulnerabilityRepository } from '../../domain/interfaces/repositories/vulnerability.repository.interface';\r\nimport { IncidentRepository } from '../../domain/interfaces/repositories/incident.repository.interface';\r\nimport { CorrelationRepository } from '../../domain/interfaces/repositories/correlation.repository.interface';\r\nimport { DomainEventPublisher } from '../../../shared-kernel/domain/domain-event-publisher';\r\nimport { ConfidenceLevel } from '../../domain/enums/confidence-level.enum';\r\nimport { ThreatSeverity } from '../../domain/enums/threat-severity.enum';\r\n\r\ndescribe('ThreatHuntingService', () => {\r\n  let service: ThreatHuntingService;\r\n  let mockSecurityEventRepository: jest.Mocked<SecurityEventRepository>;\r\n  let mockVulnerabilityRepository: jest.Mocked<VulnerabilityRepository>;\r\n  let mockIncidentRepository: jest.Mocked<IncidentRepository>;\r\n  let mockCorrelationRepository: jest.Mocked<CorrelationRepository>;\r\n  let mockEventPublisher: jest.Mocked<DomainEventPublisher>;\r\n\r\n  beforeEach(async () => {\r\n    mockSecurityEventRepository = {\r\n      findByIOC: jest.fn(),\r\n      findByCriteria: jest.fn(),\r\n      getStatistics: jest.fn(),\r\n    } as any;\r\n\r\n    mockVulnerabilityRepository = {\r\n      getStatistics: jest.fn(),\r\n    } as any;\r\n\r\n    mockIncidentRepository = {\r\n      getStatistics: jest.fn(),\r\n    } as any;\r\n\r\n    mockCorrelationRepository = {\r\n      getStatistics: jest.fn(),\r\n    } as any;\r\n\r\n    mockEventPublisher = {\r\n      publishAll: jest.fn(),\r\n    } as any;\r\n\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        ThreatHuntingService,\r\n        {\r\n          provide: SecurityEventRepository,\r\n          useValue: mockSecurityEventRepository,\r\n        },\r\n        {\r\n          provide: VulnerabilityRepository,\r\n          useValue: mockVulnerabilityRepository,\r\n        },\r\n        {\r\n          provide: IncidentRepository,\r\n          useValue: mockIncidentRepository,\r\n        },\r\n        {\r\n          provide: CorrelationRepository,\r\n          useValue: mockCorrelationRepository,\r\n        },\r\n        {\r\n          provide: DomainEventPublisher,\r\n          useValue: mockEventPublisher,\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    service = module.get<ThreatHuntingService>(ThreatHuntingService);\r\n  });\r\n\r\n  afterEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('executeHunt', () => {\r\n    it('should execute IOC-based hunt successfully', async () => {\r\n      const query: ThreatHuntingQuery = {\r\n        name: 'Test IOC Hunt',\r\n        description: 'Test IOC hunting query',\r\n        type: 'ioc_search',\r\n        parameters: {\r\n          timeRange: {\r\n            from: new Date('2023-01-01'),\r\n            to: new Date('2023-01-02'),\r\n          },\r\n          criteria: {},\r\n          iocs: [\r\n            {\r\n              type: 'ip',\r\n              value: '*************',\r\n              confidence: ConfidenceLevel.HIGH,\r\n            },\r\n            {\r\n              type: 'domain',\r\n              value: 'malicious.example.com',\r\n              confidence: ConfidenceLevel.MEDIUM,\r\n            },\r\n          ],\r\n        },\r\n        priority: 'high',\r\n        expectedResults: {\r\n          estimatedMatches: 10,\r\n          confidenceThreshold: ConfidenceLevel.MEDIUM,\r\n          falsePositiveRate: 0.1,\r\n        },\r\n      };\r\n\r\n      const mockEvents = [\r\n        createMockSecurityEvent({\r\n          id: 'event-1',\r\n          sourceIP: '*************',\r\n          severity: ThreatSeverity.HIGH,\r\n        }),\r\n        createMockSecurityEvent({\r\n          id: 'event-2',\r\n          domain: 'malicious.example.com',\r\n          severity: ThreatSeverity.MEDIUM,\r\n        }),\r\n      ];\r\n\r\n      mockSecurityEventRepository.findByIOC.mockResolvedValueOnce([mockEvents[0]]);\r\n      mockSecurityEventRepository.findByIOC.mockResolvedValueOnce([mockEvents[1]]);\r\n\r\n      const result = await service.executeHunt(query);\r\n\r\n      expect(result.execution.status).toBe('completed');\r\n      expect(result.results.totalMatches).toBe(2);\r\n      expect(result.findings).toHaveLength(2);\r\n      expect(result.findings[0].type).toBe('ioc_match');\r\n      expect(result.findings[0].confidence).toBe(ConfidenceLevel.HIGH);\r\n      expect(result.analysis.threatAssessment.overallThreatLevel).toBe('high');\r\n      expect(mockSecurityEventRepository.findByIOC).toHaveBeenCalledTimes(2);\r\n    });\r\n\r\n    it('should execute behavioral analysis hunt', async () => {\r\n      const query: ThreatHuntingQuery = {\r\n        name: 'Behavioral Analysis Hunt',\r\n        description: 'Test behavioral analysis',\r\n        type: 'behavioral_analysis',\r\n        parameters: {\r\n          timeRange: {\r\n            from: new Date('2023-01-01'),\r\n            to: new Date('2023-01-02'),\r\n          },\r\n          criteria: {},\r\n          patterns: [\r\n            {\r\n              pattern: 'unusual_login_times',\r\n              threshold: 3,\r\n              timeWindow: 3600,\r\n            },\r\n          ],\r\n        },\r\n        priority: 'medium',\r\n        expectedResults: {\r\n          estimatedMatches: 5,\r\n          confidenceThreshold: ConfidenceLevel.MEDIUM,\r\n          falsePositiveRate: 0.2,\r\n        },\r\n      };\r\n\r\n      const result = await service.executeHunt(query);\r\n\r\n      expect(result.execution.status).toBe('completed');\r\n      expect(result.query.type).toBe('behavioral_analysis');\r\n      expect(result.analysis.threatAssessment).toBeDefined();\r\n      expect(result.quality.dataCoverage).toBeGreaterThanOrEqual(0);\r\n    });\r\n\r\n    it('should execute anomaly detection hunt', async () => {\r\n      const query: ThreatHuntingQuery = {\r\n        name: 'Anomaly Detection Hunt',\r\n        description: 'Test anomaly detection',\r\n        type: 'anomaly_detection',\r\n        parameters: {\r\n          timeRange: {\r\n            from: new Date('2023-01-01'),\r\n            to: new Date('2023-01-02'),\r\n          },\r\n          criteria: {\r\n            anomalyThreshold: 2.5,\r\n            baselineWindow: 7,\r\n          },\r\n        },\r\n        priority: 'low',\r\n        expectedResults: {\r\n          estimatedMatches: 3,\r\n          confidenceThreshold: ConfidenceLevel.LOW,\r\n          falsePositiveRate: 0.3,\r\n        },\r\n      };\r\n\r\n      const result = await service.executeHunt(query);\r\n\r\n      expect(result.execution.status).toBe('completed');\r\n      expect(result.query.type).toBe('anomaly_detection');\r\n      expect(result.execution.duration).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should execute pattern matching hunt', async () => {\r\n      const query: ThreatHuntingQuery = {\r\n        name: 'Pattern Matching Hunt',\r\n        description: 'Test pattern matching',\r\n        type: 'pattern_matching',\r\n        parameters: {\r\n          timeRange: {\r\n            from: new Date('2023-01-01'),\r\n            to: new Date('2023-01-02'),\r\n          },\r\n          criteria: {\r\n            patterns: ['powershell.*encoded', 'cmd.*bypass'],\r\n          },\r\n        },\r\n        priority: 'high',\r\n        expectedResults: {\r\n          estimatedMatches: 8,\r\n          confidenceThreshold: ConfidenceLevel.HIGH,\r\n          falsePositiveRate: 0.15,\r\n        },\r\n      };\r\n\r\n      const result = await service.executeHunt(query);\r\n\r\n      expect(result.execution.status).toBe('completed');\r\n      expect(result.query.type).toBe('pattern_matching');\r\n      expect(result.execution.progress).toBe(100);\r\n    });\r\n\r\n    it('should execute timeline analysis hunt', async () => {\r\n      const query: ThreatHuntingQuery = {\r\n        name: 'Timeline Analysis Hunt',\r\n        description: 'Test timeline analysis',\r\n        type: 'timeline_analysis',\r\n        parameters: {\r\n          timeRange: {\r\n            from: new Date('2023-01-01'),\r\n            to: new Date('2023-01-02'),\r\n          },\r\n          criteria: {\r\n            timelinePatterns: ['rapid_succession', 'coordinated_activity'],\r\n          },\r\n        },\r\n        priority: 'critical',\r\n        expectedResults: {\r\n          estimatedMatches: 2,\r\n          confidenceThreshold: ConfidenceLevel.HIGH,\r\n          falsePositiveRate: 0.05,\r\n        },\r\n      };\r\n\r\n      const result = await service.executeHunt(query);\r\n\r\n      expect(result.execution.status).toBe('completed');\r\n      expect(result.query.type).toBe('timeline_analysis');\r\n      expect(result.analysis.attackTimeline).toBeDefined();\r\n    });\r\n\r\n    it('should handle hunt execution errors gracefully', async () => {\r\n      const query: ThreatHuntingQuery = {\r\n        name: 'Failing Hunt',\r\n        description: 'Test error handling',\r\n        type: 'ioc_search',\r\n        parameters: {\r\n          timeRange: {\r\n            from: new Date('2023-01-01'),\r\n            to: new Date('2023-01-02'),\r\n          },\r\n          criteria: {},\r\n          iocs: [\r\n            {\r\n              type: 'ip',\r\n              value: '*************',\r\n              confidence: ConfidenceLevel.HIGH,\r\n            },\r\n          ],\r\n        },\r\n        priority: 'high',\r\n        expectedResults: {\r\n          estimatedMatches: 10,\r\n          confidenceThreshold: ConfidenceLevel.MEDIUM,\r\n          falsePositiveRate: 0.1,\r\n        },\r\n      };\r\n\r\n      mockSecurityEventRepository.findByIOC.mockRejectedValue(new Error('Database error'));\r\n\r\n      const result = await service.executeHunt(query);\r\n\r\n      expect(result.execution.status).toBe('failed');\r\n      expect(result.execution.progress).toBe(0);\r\n      expect(result.execution.duration).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should handle unsupported hunt types', async () => {\r\n      const query: ThreatHuntingQuery = {\r\n        name: 'Unsupported Hunt',\r\n        description: 'Test unsupported type',\r\n        type: 'unsupported_type' as any,\r\n        parameters: {\r\n          timeRange: {\r\n            from: new Date('2023-01-01'),\r\n            to: new Date('2023-01-02'),\r\n          },\r\n          criteria: {},\r\n        },\r\n        priority: 'low',\r\n        expectedResults: {\r\n          estimatedMatches: 0,\r\n          confidenceThreshold: ConfidenceLevel.LOW,\r\n          falsePositiveRate: 0,\r\n        },\r\n      };\r\n\r\n      const result = await service.executeHunt(query);\r\n\r\n      expect(result.execution.status).toBe('failed');\r\n    });\r\n\r\n    it('should calculate quality metrics correctly', async () => {\r\n      const query: ThreatHuntingQuery = {\r\n        name: 'Quality Test Hunt',\r\n        description: 'Test quality metrics calculation',\r\n        type: 'ioc_search',\r\n        parameters: {\r\n          timeRange: {\r\n            from: new Date('2023-01-01'),\r\n            to: new Date('2023-01-02'),\r\n          },\r\n          criteria: {},\r\n          iocs: [\r\n            {\r\n              type: 'ip',\r\n              value: '*************',\r\n              confidence: ConfidenceLevel.HIGH,\r\n            },\r\n          ],\r\n        },\r\n        priority: 'medium',\r\n        expectedResults: {\r\n          estimatedMatches: 5,\r\n          confidenceThreshold: ConfidenceLevel.MEDIUM,\r\n          falsePositiveRate: 0.1,\r\n        },\r\n      };\r\n\r\n      const mockEvent = createMockSecurityEvent({\r\n        id: 'event-1',\r\n        sourceIP: '*************',\r\n        severity: ThreatSeverity.HIGH,\r\n      });\r\n\r\n      mockSecurityEventRepository.findByIOC.mockResolvedValue([mockEvent]);\r\n\r\n      const result = await service.executeHunt(query);\r\n\r\n      expect(result.quality.dataCoverage).toBeGreaterThan(0);\r\n      expect(result.quality.searchCompleteness).toBe(95);\r\n      expect(result.quality.resultConfidence).toBeGreaterThan(0);\r\n      expect(result.quality.falsePositiveLikelihood).toBeGreaterThanOrEqual(0);\r\n    });\r\n\r\n    it('should build attack timeline correctly', async () => {\r\n      const query: ThreatHuntingQuery = {\r\n        name: 'Timeline Test Hunt',\r\n        description: 'Test attack timeline building',\r\n        type: 'ioc_search',\r\n        parameters: {\r\n          timeRange: {\r\n            from: new Date('2023-01-01'),\r\n            to: new Date('2023-01-02'),\r\n          },\r\n          criteria: {},\r\n          iocs: [\r\n            {\r\n              type: 'ip',\r\n              value: '*************',\r\n              confidence: ConfidenceLevel.HIGH,\r\n            },\r\n          ],\r\n        },\r\n        priority: 'high',\r\n        expectedResults: {\r\n          estimatedMatches: 1,\r\n          confidenceThreshold: ConfidenceLevel.HIGH,\r\n          falsePositiveRate: 0.05,\r\n        },\r\n      };\r\n\r\n      const mockEvent = createMockSecurityEvent({\r\n        id: 'event-1',\r\n        sourceIP: '*************',\r\n        severity: ThreatSeverity.HIGH,\r\n        timestamp: new Date('2023-01-01T10:00:00Z'),\r\n      });\r\n\r\n      mockSecurityEventRepository.findByIOC.mockResolvedValue([mockEvent]);\r\n\r\n      const result = await service.executeHunt(query);\r\n\r\n      expect(result.analysis.attackTimeline).toBeDefined();\r\n      expect(result.analysis.attackTimeline.length).toBeGreaterThan(0);\r\n      expect(result.analysis.recommendations.immediate).toBeDefined();\r\n      expect(result.analysis.recommendations.shortTerm).toBeDefined();\r\n      expect(result.analysis.recommendations.longTerm).toBeDefined();\r\n      expect(result.analysis.recommendations.preventive).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('getPredefinedQueries', () => {\r\n    it('should return predefined hunting queries', async () => {\r\n      const queries = await service.getPredefinedQueries();\r\n\r\n      expect(queries).toHaveLength(2);\r\n      expect(queries[0].name).toBe('Suspicious PowerShell Activity');\r\n      expect(queries[0].type).toBe('pattern_matching');\r\n      expect(queries[0].priority).toBe('high');\r\n      expect(queries[1].name).toBe('Lateral Movement Detection');\r\n      expect(queries[1].type).toBe('behavioral_analysis');\r\n      expect(queries[1].priority).toBe('critical');\r\n    });\r\n\r\n    it('should return queries with valid time ranges', async () => {\r\n      const queries = await service.getPredefinedQueries();\r\n\r\n      queries.forEach(query => {\r\n        expect(query.parameters.timeRange.from).toBeInstanceOf(Date);\r\n        expect(query.parameters.timeRange.to).toBeInstanceOf(Date);\r\n        expect(query.parameters.timeRange.from.getTime()).toBeLessThan(\r\n          query.parameters.timeRange.to.getTime()\r\n        );\r\n      });\r\n    });\r\n\r\n    it('should return queries with expected results', async () => {\r\n      const queries = await service.getPredefinedQueries();\r\n\r\n      queries.forEach(query => {\r\n        expect(query.expectedResults.estimatedMatches).toBeGreaterThan(0);\r\n        expect(Object.values(ConfidenceLevel)).toContain(query.expectedResults.confidenceThreshold);\r\n        expect(query.expectedResults.falsePositiveRate).toBeGreaterThanOrEqual(0);\r\n        expect(query.expectedResults.falsePositiveRate).toBeLessThanOrEqual(1);\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('performance', () => {\r\n    it('should complete hunt within reasonable time', async () => {\r\n      const query: ThreatHuntingQuery = {\r\n        name: 'Performance Test Hunt',\r\n        description: 'Test hunt performance',\r\n        type: 'ioc_search',\r\n        parameters: {\r\n          timeRange: {\r\n            from: new Date('2023-01-01'),\r\n            to: new Date('2023-01-02'),\r\n          },\r\n          criteria: {},\r\n          iocs: [\r\n            {\r\n              type: 'ip',\r\n              value: '*************',\r\n              confidence: ConfidenceLevel.MEDIUM,\r\n            },\r\n          ],\r\n        },\r\n        priority: 'medium',\r\n        expectedResults: {\r\n          estimatedMatches: 1,\r\n          confidenceThreshold: ConfidenceLevel.MEDIUM,\r\n          falsePositiveRate: 0.1,\r\n        },\r\n      };\r\n\r\n      mockSecurityEventRepository.findByIOC.mockResolvedValue([]);\r\n\r\n      const startTime = Date.now();\r\n      const result = await service.executeHunt(query);\r\n      const duration = Date.now() - startTime;\r\n\r\n      expect(result.execution.status).toBe('completed');\r\n      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds\r\n      expect(result.execution.duration).toBeLessThan(5000);\r\n    });\r\n  });\r\n\r\n  // Helper function to create mock security events\r\n  function createMockSecurityEvent(options: {\r\n    id: string;\r\n    sourceIP?: string;\r\n    domain?: string;\r\n    severity: ThreatSeverity;\r\n    timestamp?: Date;\r\n  }) {\r\n    return {\r\n      id: { toString: () => options.id },\r\n      source: { name: 'test-source' },\r\n      timestamp: { value: options.timestamp || new Date() },\r\n      severity: options.severity,\r\n      rawData: {\r\n        sourceIP: options.sourceIP,\r\n        domain: options.domain,\r\n      },\r\n      affectedAssets: [\r\n        {\r\n          assetId: 'asset-1',\r\n          assetName: 'Test Asset',\r\n        },\r\n      ],\r\n    };\r\n  }\r\n});\r\n"], "version": 3}