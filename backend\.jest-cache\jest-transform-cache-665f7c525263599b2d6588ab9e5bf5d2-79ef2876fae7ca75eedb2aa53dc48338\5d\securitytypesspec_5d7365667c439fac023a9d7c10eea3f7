70685529253797c4c846b2eeaba3fd11
"use strict";
/**
 * Security Types Tests
 */
Object.defineProperty(exports, "__esModule", { value: true });
const security_types_1 = require("../../types/security.types");
describe('SecurityUtils', () => {
    describe('generateSecureRandom', () => {
        it('should generate random string of specified length', () => {
            const result = security_types_1.SecurityUtils.generateSecureRandom(16);
            expect(result).toHaveLength(16);
            expect(typeof result).toBe('string');
        });
        it('should generate different strings on multiple calls', () => {
            const result1 = security_types_1.SecurityUtils.generateSecureRandom(32);
            const result2 = security_types_1.SecurityUtils.generateSecureRandom(32);
            expect(result1).not.toBe(result2);
        });
        it('should use default length when not specified', () => {
            const result = security_types_1.SecurityUtils.generateSecureRandom();
            expect(result).toHaveLength(32);
        });
    });
    describe('generateToken', () => {
        it('should generate hex token of specified length', () => {
            const result = security_types_1.SecurityUtils.generateToken(16);
            expect(result).toHaveLength(32); // 16 bytes = 32 hex characters
            expect(/^[0-9a-f]+$/.test(result)).toBe(true);
        });
        it('should generate different tokens on multiple calls', () => {
            const token1 = security_types_1.SecurityUtils.generateToken(16);
            const token2 = security_types_1.SecurityUtils.generateToken(16);
            expect(token1).not.toBe(token2);
        });
    });
    describe('isSecurityLevelSufficient', () => {
        it('should return true when actual level meets required level', () => {
            expect(security_types_1.SecurityUtils.isSecurityLevelSufficient(security_types_1.SecurityLevel.CONFIDENTIAL, security_types_1.SecurityLevel.CONFIDENTIAL)).toBe(true);
        });
        it('should return true when actual level exceeds required level', () => {
            expect(security_types_1.SecurityUtils.isSecurityLevelSufficient(security_types_1.SecurityLevel.INTERNAL, security_types_1.SecurityLevel.CONFIDENTIAL)).toBe(true);
        });
        it('should return false when actual level is below required level', () => {
            expect(security_types_1.SecurityUtils.isSecurityLevelSufficient(security_types_1.SecurityLevel.CONFIDENTIAL, security_types_1.SecurityLevel.INTERNAL)).toBe(false);
        });
        it('should handle public level correctly', () => {
            expect(security_types_1.SecurityUtils.isSecurityLevelSufficient(security_types_1.SecurityLevel.PUBLIC, security_types_1.SecurityLevel.PUBLIC)).toBe(true);
            expect(security_types_1.SecurityUtils.isSecurityLevelSufficient(security_types_1.SecurityLevel.INTERNAL, security_types_1.SecurityLevel.PUBLIC)).toBe(false);
        });
        it('should handle top secret level correctly', () => {
            expect(security_types_1.SecurityUtils.isSecurityLevelSufficient(security_types_1.SecurityLevel.TOP_SECRET, security_types_1.SecurityLevel.TOP_SECRET)).toBe(true);
            expect(security_types_1.SecurityUtils.isSecurityLevelSufficient(security_types_1.SecurityLevel.RESTRICTED, security_types_1.SecurityLevel.TOP_SECRET)).toBe(false);
        });
    });
    describe('validatePasswordStrength', () => {
        it('should validate strong password', () => {
            const result = security_types_1.SecurityUtils.validatePasswordStrength('StrongP@ssw0rd123');
            expect(result.isValid).toBe(true);
            expect(result.score).toBeGreaterThan(4);
            expect(result.feedback).toHaveLength(0);
        });
        it('should reject weak password', () => {
            const result = security_types_1.SecurityUtils.validatePasswordStrength('weak');
            expect(result.isValid).toBe(false);
            expect(result.score).toBeLessThan(4);
            expect(result.feedback.length).toBeGreaterThan(0);
        });
        it('should provide feedback for missing requirements', () => {
            const result = security_types_1.SecurityUtils.validatePasswordStrength('password');
            expect(result.feedback).toContain('Password must contain uppercase letters');
            expect(result.feedback).toContain('Password must contain numbers');
            expect(result.feedback).toContain('Password must contain special characters');
        });
        it('should detect common patterns', () => {
            const result = security_types_1.SecurityUtils.validatePasswordStrength('Password123456');
            expect(result.feedback).toContain('Password contains common patterns');
            expect(result.score).toBeLessThan(4);
        });
        it('should give bonus for longer passwords', () => {
            const shortResult = security_types_1.SecurityUtils.validatePasswordStrength('StrongP@ss1');
            const longResult = security_types_1.SecurityUtils.validatePasswordStrength('VeryStrongP@ssw0rd123');
            expect(longResult.score).toBeGreaterThan(shortResult.score);
        });
    });
    describe('sanitizeInput', () => {
        it('should sanitize HTML characters', () => {
            const input = '<script>alert("xss")</script>';
            const result = security_types_1.SecurityUtils.sanitizeInput(input);
            expect(result).toBe('&lt;script&gt;alert(&quot;xss&quot;)&lt;&#x2F;script&gt;');
        });
        it('should sanitize special characters', () => {
            const input = 'Test & "quotes" <tags> /slashes/';
            const result = security_types_1.SecurityUtils.sanitizeInput(input);
            expect(result).toBe('Test &amp; &quot;quotes&quot; &lt;tags&gt; &#x2F;slashes&#x2F;');
        });
        it('should handle empty string', () => {
            const result = security_types_1.SecurityUtils.sanitizeInput('');
            expect(result).toBe('');
        });
    });
    describe('isValidIpAddress', () => {
        it('should validate IPv4 addresses', () => {
            expect(security_types_1.SecurityUtils.isValidIpAddress('***********')).toBe(true);
            expect(security_types_1.SecurityUtils.isValidIpAddress('********')).toBe(true);
            expect(security_types_1.SecurityUtils.isValidIpAddress('***************')).toBe(true);
        });
        it('should reject invalid IPv4 addresses', () => {
            expect(security_types_1.SecurityUtils.isValidIpAddress('256.1.1.1')).toBe(false);
            expect(security_types_1.SecurityUtils.isValidIpAddress('192.168.1')).toBe(false);
            expect(security_types_1.SecurityUtils.isValidIpAddress('***********.1')).toBe(false);
        });
        it('should validate IPv6 addresses', () => {
            expect(security_types_1.SecurityUtils.isValidIpAddress('2001:0db8:85a3:0000:0000:8a2e:0370:7334')).toBe(true);
        });
        it('should reject non-string input', () => {
            expect(security_types_1.SecurityUtils.isValidIpAddress(null)).toBe(false);
            expect(security_types_1.SecurityUtils.isValidIpAddress(123)).toBe(false);
        });
    });
    describe('isPrivateIpAddress', () => {
        it('should identify private IPv4 addresses', () => {
            expect(security_types_1.SecurityUtils.isPrivateIpAddress('***********')).toBe(true);
            expect(security_types_1.SecurityUtils.isPrivateIpAddress('********')).toBe(true);
            expect(security_types_1.SecurityUtils.isPrivateIpAddress('**********')).toBe(true);
            expect(security_types_1.SecurityUtils.isPrivateIpAddress('127.0.0.1')).toBe(true);
        });
        it('should identify public IPv4 addresses', () => {
            expect(security_types_1.SecurityUtils.isPrivateIpAddress('*******')).toBe(false);
            expect(security_types_1.SecurityUtils.isPrivateIpAddress('*******')).toBe(false);
        });
        it('should return false for invalid IP addresses', () => {
            expect(security_types_1.SecurityUtils.isPrivateIpAddress('invalid')).toBe(false);
        });
    });
    describe('generateCsrfToken', () => {
        it('should generate CSRF token', () => {
            const token = security_types_1.SecurityUtils.generateCsrfToken();
            expect(token).toHaveLength(64); // 32 bytes = 64 hex characters
            expect(/^[0-9a-f]+$/.test(token)).toBe(true);
        });
        it('should generate different tokens', () => {
            const token1 = security_types_1.SecurityUtils.generateCsrfToken();
            const token2 = security_types_1.SecurityUtils.generateCsrfToken();
            expect(token1).not.toBe(token2);
        });
    });
    describe('validateCsrfToken', () => {
        it('should validate matching tokens', () => {
            const token = 'abc123def456';
            const result = security_types_1.SecurityUtils.validateCsrfToken(token, token);
            expect(result).toBe(true);
        });
        it('should reject non-matching tokens', () => {
            const result = security_types_1.SecurityUtils.validateCsrfToken('token1', 'token2');
            expect(result).toBe(false);
        });
        it('should reject empty tokens', () => {
            expect(security_types_1.SecurityUtils.validateCsrfToken('', 'token')).toBe(false);
            expect(security_types_1.SecurityUtils.validateCsrfToken('token', '')).toBe(false);
            expect(security_types_1.SecurityUtils.validateCsrfToken(null, 'token')).toBe(false);
        });
        it('should reject tokens of different lengths', () => {
            const result = security_types_1.SecurityUtils.validateCsrfToken('short', 'verylongtoken');
            expect(result).toBe(false);
        });
    });
    describe('createSecurityContext', () => {
        it('should create basic security context', () => {
            const context = security_types_1.SecurityUtils.createSecurityContext('user123', 'session456');
            expect(context.userId).toBe('user123');
            expect(context.sessionId).toBe('session456');
            expect(context.timestamp).toBeInstanceOf(Date);
        });
        it('should create security context with options', () => {
            const options = {
                authMethod: security_types_1.AuthenticationMethod.MFA,
                roles: ['admin'],
                permissions: [security_types_1.PermissionType.READ, security_types_1.PermissionType.WRITE],
                clearanceLevel: security_types_1.SecurityLevel.CONFIDENTIAL,
                ipAddress: '***********',
            };
            const context = security_types_1.SecurityUtils.createSecurityContext('user123', 'session456', options);
            expect(context.authMethod).toBe(security_types_1.AuthenticationMethod.MFA);
            expect(context.roles).toEqual(['admin']);
            expect(context.permissions).toEqual([security_types_1.PermissionType.READ, security_types_1.PermissionType.WRITE]);
            expect(context.clearanceLevel).toBe(security_types_1.SecurityLevel.CONFIDENTIAL);
            expect(context.ipAddress).toBe('***********');
        });
    });
    describe('hasPermission', () => {
        it('should grant access for admin permission', () => {
            const context = {
                userId: 'user123',
                permissions: [security_types_1.PermissionType.ADMIN],
                timestamp: new Date(),
            };
            const result = security_types_1.SecurityUtils.hasPermission(context, security_types_1.PermissionType.DELETE);
            expect(result).toBe(true);
        });
        it('should grant access for owner permission', () => {
            const context = {
                userId: 'user123',
                permissions: [security_types_1.PermissionType.OWNER],
                timestamp: new Date(),
            };
            const resource = { type: 'Document', id: 'doc123' };
            const result = security_types_1.SecurityUtils.hasPermission(context, security_types_1.PermissionType.DELETE, resource);
            expect(result).toBe(true);
        });
        it('should grant access for specific permission', () => {
            const context = {
                userId: 'user123',
                permissions: [security_types_1.PermissionType.READ, security_types_1.PermissionType.WRITE],
                timestamp: new Date(),
            };
            expect(security_types_1.SecurityUtils.hasPermission(context, security_types_1.PermissionType.READ)).toBe(true);
            expect(security_types_1.SecurityUtils.hasPermission(context, security_types_1.PermissionType.WRITE)).toBe(true);
            expect(security_types_1.SecurityUtils.hasPermission(context, security_types_1.PermissionType.DELETE)).toBe(false);
        });
        it('should deny access when no permissions', () => {
            const context = {
                userId: 'user123',
                timestamp: new Date(),
            };
            const result = security_types_1.SecurityUtils.hasPermission(context, security_types_1.PermissionType.READ);
            expect(result).toBe(false);
        });
    });
    describe('evaluateAccessConditions', () => {
        const context = {
            userId: 'user123',
            roles: ['user'],
            ipAddress: '***********',
            timestamp: new Date(),
        };
        it('should return true for empty conditions', () => {
            const result = security_types_1.SecurityUtils.evaluateAccessConditions([], context);
            expect(result).toBe(true);
        });
        it('should evaluate equals condition', () => {
            const conditions = [
                {
                    type: 'attribute',
                    operator: 'equals',
                    field: 'userId',
                    value: 'user123',
                },
            ];
            const result = security_types_1.SecurityUtils.evaluateAccessConditions(conditions, context);
            expect(result).toBe(true);
        });
        it('should evaluate in condition', () => {
            const conditions = [
                {
                    type: 'attribute',
                    operator: 'in',
                    field: 'roles',
                    value: ['user', 'admin'],
                },
            ];
            const result = security_types_1.SecurityUtils.evaluateAccessConditions(conditions, context);
            expect(result).toBe(true);
        });
        it('should evaluate negated condition', () => {
            const conditions = [
                {
                    type: 'attribute',
                    operator: 'equals',
                    field: 'userId',
                    value: 'other-user',
                    negate: true,
                },
            ];
            const result = security_types_1.SecurityUtils.evaluateAccessConditions(conditions, context);
            expect(result).toBe(true);
        });
        it('should return false when condition fails', () => {
            const conditions = [
                {
                    type: 'attribute',
                    operator: 'equals',
                    field: 'userId',
                    value: 'other-user',
                },
            ];
            const result = security_types_1.SecurityUtils.evaluateAccessConditions(conditions, context);
            expect(result).toBe(false);
        });
        it('should require all conditions to pass', () => {
            const conditions = [
                {
                    type: 'attribute',
                    operator: 'equals',
                    field: 'userId',
                    value: 'user123',
                },
                {
                    type: 'attribute',
                    operator: 'equals',
                    field: 'userId',
                    value: 'other-user',
                },
            ];
            const result = security_types_1.SecurityUtils.evaluateAccessConditions(conditions, context);
            expect(result).toBe(false);
        });
    });
    describe('calculateSecurityScore', () => {
        it('should calculate base score', () => {
            const context = {
                userId: 'user123',
                timestamp: new Date(),
            };
            const score = security_types_1.SecurityUtils.calculateSecurityScore(context);
            expect(score).toBe(50); // Base score
        });
        it('should increase score for MFA', () => {
            const context = {
                userId: 'user123',
                authMethod: security_types_1.AuthenticationMethod.MFA,
                timestamp: new Date(),
            };
            const score = security_types_1.SecurityUtils.calculateSecurityScore(context);
            expect(score).toBe(70); // Base + MFA bonus
        });
        it('should decrease score for admin role', () => {
            const context = {
                userId: 'user123',
                roles: ['admin'],
                timestamp: new Date(),
            };
            const score = security_types_1.SecurityUtils.calculateSecurityScore(context);
            expect(score).toBe(40); // Base - admin penalty
        });
        it('should increase score for private IP', () => {
            const context = {
                userId: 'user123',
                ipAddress: '***********',
                timestamp: new Date(),
            };
            const score = security_types_1.SecurityUtils.calculateSecurityScore(context);
            expect(score).toBe(60); // Base + private IP bonus
        });
        it('should decrease score for public IP', () => {
            const context = {
                userId: 'user123',
                ipAddress: '*******',
                timestamp: new Date(),
            };
            const score = security_types_1.SecurityUtils.calculateSecurityScore(context);
            expect(score).toBe(45); // Base - public IP penalty
        });
        it('should cap score at 100', () => {
            const context = {
                userId: 'user123',
                authMethod: security_types_1.AuthenticationMethod.MFA,
                ipAddress: '***********',
                timestamp: new Date(),
            };
            const score = security_types_1.SecurityUtils.calculateSecurityScore(context);
            expect(score).toBeLessThanOrEqual(100);
        });
        it('should not go below 0', () => {
            const context = {
                userId: 'user123',
                roles: ['admin'],
                ipAddress: '*******',
                attributes: {
                    sessionAge: 25 * 60 * 60 * 1000, // 25 hours
                },
                timestamp: new Date(),
            };
            const score = security_types_1.SecurityUtils.calculateSecurityScore(context);
            expect(score).toBeGreaterThanOrEqual(0);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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