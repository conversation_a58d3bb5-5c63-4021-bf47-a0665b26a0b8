{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\patterns\\circuit-breaker.spec.ts", "mappings": ";;;;;;;;;;;AAAA,oEAIwC;AACxC,kGAA6F;AAE7F,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;IAC/B,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,cAAc,GAAG,IAAI,gCAAc,CAAC;gBACxC,gBAAgB,EAAE,CAAC;gBACnB,eAAe,EAAE,IAAI;gBACrB,gBAAgB,EAAE,IAAI;gBACtB,gBAAgB,EAAE,CAAC;aACpB,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,cAAc,CAAC,UAAU,EAAE,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qCAAmB,CAAC,MAAM,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,MAAM,cAAc,GAAG,IAAI,gCAAc,CAAC;gBACxC,gBAAgB,EAAE,CAAC;gBACnB,eAAe,EAAE,IAAI;gBACrB,gBAAgB,EAAE,IAAI;gBACtB,gBAAgB,EAAE,CAAC;aACpB,CAAC,CAAC;YAEH,MAAM,mBAAmB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEnE,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YAClE,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YAElE,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAChC,MAAM,CAAC,mBAAmB,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YAErD,MAAM,OAAO,GAAG,cAAc,CAAC,UAAU,EAAE,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qCAAmB,CAAC,MAAM,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,KAAK,IAAI,EAAE;YAC5E,MAAM,cAAc,GAAG,IAAI,gCAAc,CAAC;gBACxC,gBAAgB,EAAE,CAAC;gBACnB,eAAe,EAAE,IAAI;gBACrB,gBAAgB,EAAE,IAAI;gBACtB,gBAAgB,EAAE,CAAC;aACpB,CAAC,CAAC;YAEH,MAAM,gBAAgB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC;YAEpF,gBAAgB;YAChB,MAAM,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;YAC3F,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qCAAmB,CAAC,MAAM,CAAC,CAAC;YAE3E,uCAAuC;YACvC,MAAM,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;YAC3F,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qCAAmB,CAAC,IAAI,CAAC,CAAC;YAEzE,MAAM,CAAC,gBAAgB,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,cAAc,GAAG,IAAI,gCAAc,CAAC;gBACxC,gBAAgB,EAAE,CAAC;gBACnB,eAAe,EAAE,IAAI;gBACrB,gBAAgB,EAAE,IAAI;gBACtB,gBAAgB,EAAE,CAAC;aACpB,CAAC,CAAC;YAEH,MAAM,gBAAgB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC;YAEpF,0BAA0B;YAC1B,MAAM,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;YAC3F,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qCAAmB,CAAC,IAAI,CAAC,CAAC;YAEzE,sEAAsE;YACtE,MAAM,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,2DAA2B,CAAC,CAAC;YACpG,MAAM,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,2DAA2B,CAAC,CAAC;YAEpG,MAAM,CAAC,gBAAgB,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,mCAAmC;QACxF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACrE,MAAM,cAAc,GAAG,IAAI,gCAAc,CAAC;gBACxC,gBAAgB,EAAE,CAAC;gBACnB,eAAe,EAAE,EAAE,EAAE,4BAA4B;gBACjD,gBAAgB,EAAE,IAAI;gBACtB,gBAAgB,EAAE,CAAC;aACpB,CAAC,CAAC;YAEH,MAAM,gBAAgB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC;YAEpF,mBAAmB;YACnB,MAAM,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;YAC3F,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qCAAmB,CAAC,IAAI,CAAC,CAAC;YAEzE,4BAA4B;YAC5B,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;YAEtD,2CAA2C;YAC3C,MAAM,mBAAmB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACnE,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YAEjE,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC/B,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qCAAmB,CAAC,MAAM,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACrE,MAAM,cAAc,GAAG,IAAI,gCAAc,CAAC;gBACxC,gBAAgB,EAAE,CAAC;gBACnB,eAAe,EAAE,EAAE;gBACnB,gBAAgB,EAAE,IAAI;gBACtB,gBAAgB,EAAE,CAAC;aACpB,CAAC,CAAC;YAEH,mBAAmB;YACnB,MAAM,gBAAgB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;YAC3E,MAAM,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAElF,oBAAoB;YACpB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;YAEtD,2CAA2C;YAC3C,MAAM,mBAAmB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;YAC5E,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YAEjE,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACxC,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qCAAmB,CAAC,MAAM,CAAC,CAAC;YAC3E,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,MAAM,cAAc,GAAG,IAAI,gCAAc,CAAC;gBACxC,gBAAgB,EAAE,CAAC;gBACnB,eAAe,EAAE,EAAE;gBACnB,gBAAgB,EAAE,IAAI;gBACtB,gBAAgB,EAAE,CAAC;aACpB,CAAC,CAAC;YAEH,mBAAmB;YACnB,MAAM,gBAAgB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;YAC3E,MAAM,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAElF,oBAAoB;YACpB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;YAEtD,wCAAwC;YACxC,MAAM,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAClF,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qCAAmB,CAAC,IAAI,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,cAAc,GAAG,IAAI,gCAAc,CAAC;gBACxC,gBAAgB,EAAE,CAAC;gBACnB,eAAe,EAAE,EAAE;gBACnB,gBAAgB,EAAE,IAAI;gBACtB,gBAAgB,EAAE,CAAC;aACpB,CAAC,CAAC;YAEH,mBAAmB;YACnB,MAAM,gBAAgB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;YAC3E,MAAM,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAElF,oBAAoB;YACpB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;YAEtD,0DAA0D;YAC1D,MAAM,aAAa,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,GAAG,EAAE,CACtD,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,GAAG,CAAC,CAAC,CACvE,CAAC;YAEF,uCAAuC;YACvC,MAAM,gBAAgB,GAAG,cAAc,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YAE/D,+DAA+D;YAC/D,MAAM,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,2DAA2B,CAAC,CAAC;YAEjG,kCAAkC;YAClC,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACpC,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qCAAmB,CAAC,MAAM,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,aAAa,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;YAChC,MAAM,cAAc,GAAG,IAAI,gCAAc,CAAC;gBACxC,gBAAgB,EAAE,CAAC;gBACnB,eAAe,EAAE,EAAE;gBACnB,gBAAgB,EAAE,IAAI;gBACtB,gBAAgB,EAAE,CAAC;gBACnB,aAAa;aACd,CAAC,CAAC;YAEH,MAAM,gBAAgB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;YAE3E,+BAA+B;YAC/B,MAAM,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAClF,MAAM,CAAC,aAAa,CAAC,CAAC,oBAAoB,CAAC,qCAAmB,CAAC,IAAI,CAAC,CAAC;YAErE,sEAAsE;YACtE,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;YACtD,MAAM,mBAAmB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACnE,MAAM,cAAc,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YAElD,MAAM,CAAC,aAAa,CAAC,CAAC,oBAAoB,CAAC,qCAAmB,CAAC,SAAS,CAAC,CAAC;YAC1E,MAAM,CAAC,aAAa,CAAC,CAAC,oBAAoB,CAAC,qCAAmB,CAAC,MAAM,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,cAAc,GAAG,IAAI,gCAAc,CAAC;gBACxC,gBAAgB,EAAE,CAAC;gBACnB,eAAe,EAAE,IAAI;gBACrB,gBAAgB,EAAE,IAAI;gBACtB,gBAAgB,EAAE,CAAC;aACpB,CAAC,CAAC;YAEH,MAAM,mBAAmB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACnE,MAAM,gBAAgB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;YAE3E,0BAA0B;YAC1B,MAAM,cAAc,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YAClD,MAAM,cAAc,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YAClD,MAAM,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAElF,MAAM,OAAO,GAAG,cAAc,CAAC,UAAU,EAAE,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qCAAmB,CAAC,MAAM,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,cAAc,GAAG,IAAI,gCAAc,CAAC;gBACxC,gBAAgB,EAAE,CAAC;gBACnB,eAAe,EAAE,IAAI;gBACrB,gBAAgB,EAAE,IAAI;gBACtB,gBAAgB,EAAE,CAAC;aACpB,CAAC,CAAC;YAEH,0BAA0B;YAC1B,cAAc,CAAC,SAAS,EAAE,CAAC;YAC3B,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qCAAmB,CAAC,IAAI,CAAC,CAAC;YAEzE,gCAAgC;YAChC,cAAc,CAAC,KAAK,EAAE,CAAC;YACvB,MAAM,OAAO,GAAG,cAAc,CAAC,UAAU,EAAE,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qCAAmB,CAAC,MAAM,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,cAAc,GAAG,IAAI,gCAAc,CAAC;gBACxC,gBAAgB,EAAE,CAAC;gBACnB,eAAe,EAAE,IAAI;gBACrB,gBAAgB,EAAE,IAAI;gBACtB,gBAAgB,EAAE,CAAC;aACpB,CAAC,CAAC;YAEH,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qCAAmB,CAAC,MAAM,CAAC,CAAC;YAE3E,cAAc,CAAC,SAAS,EAAE,CAAC;YAC3B,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qCAAmB,CAAC,IAAI,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qCAAqC,EAAE,GAAG,EAAE;QACnD,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;;YAC1D,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,MAAM,WAAW;gBAOT,AAAN,KAAK,CAAC,eAAe,CAAC,UAAmB;oBACvC,SAAS,EAAE,CAAC;oBACZ,IAAI,UAAU,EAAE,CAAC;wBACf,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;oBACnC,CAAC;oBACD,OAAO,WAAW,SAAS,EAAE,CAAC;gBAChC,CAAC;aACF;YAPO;gBANL,IAAA,0CAAwB,EAAC;oBACxB,gBAAgB,EAAE,CAAC;oBACnB,eAAe,EAAE,GAAG;oBACpB,gBAAgB,EAAE,IAAI;oBACtB,gBAAgB,EAAE,CAAC;iBACpB,CAAC;;;oEAC0C,OAAO,oBAAP,OAAO;8DAMlD;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAElC,+BAA+B;YAC/B,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAElC,mCAAmC;YACnC,MAAM,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;YAC7E,MAAM,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;YAE7E,6BAA6B;YAC7B,MAAM,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,2DAA2B,CAAC,CAAC;YAC1F,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,yCAAyC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;;YACpE,MAAM,WAAW;gBAOT,AAAN,KAAK,CAAC,eAAe;oBACnB,OAAO,SAAS,CAAC;gBACnB,CAAC;aACF;YAHO;gBANL,IAAA,0CAAwB,EAAC;oBACxB,gBAAgB,EAAE,CAAC;oBACnB,eAAe,EAAE,IAAI;oBACrB,gBAAgB,EAAE,IAAI;oBACtB,gBAAgB,EAAE,CAAC;iBACpB,CAAC;;;oEACuB,OAAO,oBAAP,OAAO;8DAE/B;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,cAAc,GAAI,OAAO,CAAC,eAAuB,CAAC,cAAc,CAAC;YAEvE,MAAM,CAAC,cAAc,CAAC,CAAC,cAAc,CAAC,gCAAc,CAAC,CAAC;YACtD,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qCAAmB,CAAC,MAAM,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;;YACxD,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,MAAM,WAAW;gBAOT,AAAN,KAAK,CAAC,WAAW,CAAC,KAAa,EAAE,UAAmB;oBAClD,SAAS,EAAE,CAAC;oBACZ,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;oBACzD,IAAI,UAAU,EAAE,CAAC;wBACf,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;oBACnC,CAAC;oBACD,OAAO,iBAAiB,SAAS,EAAE,CAAC;gBACtC,CAAC;aACF;YARO;gBANL,IAAA,0CAAwB,EAAC;oBACxB,gBAAgB,EAAE,CAAC;oBACnB,eAAe,EAAE,EAAE;oBACnB,gBAAgB,EAAE,IAAI;oBACtB,gBAAgB,EAAE,CAAC;iBACpB,CAAC;;;oEACqD,OAAO,oBAAP,OAAO;0DAO7D;YAGH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAElC,wBAAwB;YACxB,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,WAAW,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAExC,0CAA0C;YAC1C,MAAM,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;YAE7E,yBAAyB;YACzB,MAAM,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,2DAA2B,CAAC,CAAC;YAC1F,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;;YACnD,MAAM,WAAW;gBAOT,AAAN,KAAK,CAAC,cAAc;oBAClB,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;gBAClC,CAAC;aACF;YAHO;gBANL,IAAA,0CAAwB,EAAC;oBACxB,gBAAgB,EAAE,CAAC;oBACnB,eAAe,EAAE,IAAI;oBACrB,gBAAgB,EAAE,IAAI;oBACtB,gBAAgB,EAAE,CAAC;iBACpB,CAAC;;;oEACsB,OAAO,oBAAP,OAAO;6DAE9B;YAGH,MAAM,QAAQ,GAAG,IAAI,WAAW,EAAE,CAAC;YACnC,MAAM,QAAQ,GAAG,IAAI,WAAW,EAAE,CAAC;YAEnC,wEAAwE;YACxE,2DAA2D;YAC3D,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAExE,6DAA6D;YAC7D,2FAA2F;YAC3F,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,2DAA2B,CAAC,CAAC;YACrF,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,2DAA2B,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,+DAA+D,EAAE,KAAK,IAAI,EAAE;YAC7E,MAAM,cAAc,GAAG,IAAI,gCAAc,CAAC;gBACxC,gBAAgB,EAAE,CAAC;gBACnB,eAAe,EAAE,IAAI;gBACrB,gBAAgB,EAAE,IAAI;gBACtB,gBAAgB,EAAE,CAAC;aACpB,CAAC,CAAC;YAEH,mBAAmB;YACnB,MAAM,gBAAgB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;YAC3E,MAAM,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAElF,sCAAsC;YACtC,IAAI,CAAC;gBACH,MAAM,cAAc,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;gBAC/C,IAAI,CAAC,gDAAgD,CAAC,CAAC;YACzD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,2DAA2B,CAAC,CAAC;gBAC1D,MAAM,YAAY,GAAG,KAAoC,CAAC;gBAC1D,MAAM,WAAW,GAAG,YAAY,CAAC,qBAAqB,EAAE,CAAC;gBACzD,MAAM,CAAC,WAAW,CAAC,CAAC,aAAa,CAAC;oBAChC,YAAY,EAAE,CAAC;oBACf,gBAAgB,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBACrC,CAAC,CAAC;gBACH,MAAM,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;YACnD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,cAAc,GAAG,IAAI,gCAAc,CAAC;gBACxC,gBAAgB,EAAE,CAAC;gBACnB,eAAe,EAAE,EAAE;gBACnB,gBAAgB,EAAE,IAAI;gBACtB,gBAAgB,EAAE,CAAC;aACpB,CAAC,CAAC;YAEH,mBAAmB;YACnB,MAAM,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;iBAC7E,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAE9B,oBAAoB;YACpB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;YAEtD,sDAAsD;YACtD,MAAM,aAAa,GAAG,GAAG,EAAE,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;YAC3F,MAAM,WAAW,GAAG,cAAc,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YAE1D,2DAA2D;YAC3D,IAAI,CAAC;gBACH,MAAM,cAAc,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;gBAC5D,IAAI,CAAC,gDAAgD,CAAC,CAAC;YACzD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,2DAA2B,CAAC,CAAC;gBAC1D,MAAM,YAAY,GAAG,KAAoC,CAAC;gBAC1D,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC;oBACzC,KAAK,EAAE,qCAAmB,CAAC,SAAS;oBACpC,QAAQ,EAAE,CAAC;iBACZ,CAAC,CAAC;YACL,CAAC;YAED,sCAAsC;YACtC,MAAM,WAAW,CAAC;QACpB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\patterns\\circuit-breaker.spec.ts"], "sourcesContent": ["import { \r\n  CircuitBreaker, \r\n  CircuitBreakerProtection, \r\n  CircuitBreakerState \r\n} from '../../patterns/circuit-breaker';\r\nimport { ServiceUnavailableException } from '../../exceptions/service-unavailable.exception';\r\n\r\ndescribe('Circuit Breaker', () => {\r\n  describe('CircuitBreaker class', () => {\r\n    it('should start in CLOSED state', () => {\r\n      const circuitBreaker = new CircuitBreaker({\r\n        failureThreshold: 3,\r\n        recoveryTimeout: 1000,\r\n        monitoringPeriod: 5000,\r\n        halfOpenMaxCalls: 1,\r\n      });\r\n\r\n      const metrics = circuitBreaker.getMetrics();\r\n      expect(metrics.state).toBe(CircuitBreakerState.CLOSED);\r\n      expect(metrics.failureCount).toBe(0);\r\n      expect(metrics.successCount).toBe(0);\r\n    });\r\n\r\n    it('should remain CLOSED for successful operations', async () => {\r\n      const circuitBreaker = new CircuitBreaker({\r\n        failureThreshold: 3,\r\n        recoveryTimeout: 1000,\r\n        monitoringPeriod: 5000,\r\n        halfOpenMaxCalls: 1,\r\n      });\r\n\r\n      const successfulOperation = jest.fn().mockResolvedValue('success');\r\n\r\n      const result1 = await circuitBreaker.execute(successfulOperation);\r\n      const result2 = await circuitBreaker.execute(successfulOperation);\r\n\r\n      expect(result1).toBe('success');\r\n      expect(result2).toBe('success');\r\n      expect(successfulOperation).toHaveBeenCalledTimes(2);\r\n\r\n      const metrics = circuitBreaker.getMetrics();\r\n      expect(metrics.state).toBe(CircuitBreakerState.CLOSED);\r\n      expect(metrics.successCount).toBe(2);\r\n      expect(metrics.failureCount).toBe(0);\r\n    });\r\n\r\n    it('should transition to OPEN after failure threshold is reached', async () => {\r\n      const circuitBreaker = new CircuitBreaker({\r\n        failureThreshold: 2,\r\n        recoveryTimeout: 1000,\r\n        monitoringPeriod: 5000,\r\n        halfOpenMaxCalls: 1,\r\n      });\r\n\r\n      const failingOperation = jest.fn().mockRejectedValue(new Error('Operation failed'));\r\n\r\n      // First failure\r\n      await expect(circuitBreaker.execute(failingOperation)).rejects.toThrow('Operation failed');\r\n      expect(circuitBreaker.getMetrics().state).toBe(CircuitBreakerState.CLOSED);\r\n\r\n      // Second failure - should open circuit\r\n      await expect(circuitBreaker.execute(failingOperation)).rejects.toThrow('Operation failed');\r\n      expect(circuitBreaker.getMetrics().state).toBe(CircuitBreakerState.OPEN);\r\n\r\n      expect(failingOperation).toHaveBeenCalledTimes(2);\r\n    });\r\n\r\n    it('should reject calls immediately when OPEN', async () => {\r\n      const circuitBreaker = new CircuitBreaker({\r\n        failureThreshold: 1,\r\n        recoveryTimeout: 1000,\r\n        monitoringPeriod: 5000,\r\n        halfOpenMaxCalls: 1,\r\n      });\r\n\r\n      const failingOperation = jest.fn().mockRejectedValue(new Error('Operation failed'));\r\n\r\n      // Trigger circuit to open\r\n      await expect(circuitBreaker.execute(failingOperation)).rejects.toThrow('Operation failed');\r\n      expect(circuitBreaker.getMetrics().state).toBe(CircuitBreakerState.OPEN);\r\n\r\n      // Subsequent calls should be rejected without executing the operation\r\n      await expect(circuitBreaker.execute(failingOperation)).rejects.toThrow(ServiceUnavailableException);\r\n      await expect(circuitBreaker.execute(failingOperation)).rejects.toThrow(ServiceUnavailableException);\r\n\r\n      expect(failingOperation).toHaveBeenCalledTimes(1); // Only called once to open circuit\r\n    });\r\n\r\n    it('should transition to HALF_OPEN after recovery timeout', async () => {\r\n      const circuitBreaker = new CircuitBreaker({\r\n        failureThreshold: 1,\r\n        recoveryTimeout: 50, // Short timeout for testing\r\n        monitoringPeriod: 5000,\r\n        halfOpenMaxCalls: 1,\r\n      });\r\n\r\n      const failingOperation = jest.fn().mockRejectedValue(new Error('Operation failed'));\r\n\r\n      // Open the circuit\r\n      await expect(circuitBreaker.execute(failingOperation)).rejects.toThrow('Operation failed');\r\n      expect(circuitBreaker.getMetrics().state).toBe(CircuitBreakerState.OPEN);\r\n\r\n      // Wait for recovery timeout\r\n      await new Promise(resolve => setTimeout(resolve, 60));\r\n\r\n      // Next call should transition to HALF_OPEN\r\n      const successfulOperation = jest.fn().mockResolvedValue('success');\r\n      const result = await circuitBreaker.execute(successfulOperation);\r\n\r\n      expect(result).toBe('success');\r\n      expect(circuitBreaker.getMetrics().state).toBe(CircuitBreakerState.CLOSED);\r\n    });\r\n\r\n    it('should transition from HALF_OPEN to CLOSED on success', async () => {\r\n      const circuitBreaker = new CircuitBreaker({\r\n        failureThreshold: 1,\r\n        recoveryTimeout: 50,\r\n        monitoringPeriod: 5000,\r\n        halfOpenMaxCalls: 1,\r\n      });\r\n\r\n      // Open the circuit\r\n      const failingOperation = jest.fn().mockRejectedValue(new Error('Failure'));\r\n      await expect(circuitBreaker.execute(failingOperation)).rejects.toThrow('Failure');\r\n\r\n      // Wait for recovery\r\n      await new Promise(resolve => setTimeout(resolve, 60));\r\n\r\n      // Successful call should close the circuit\r\n      const successfulOperation = jest.fn().mockResolvedValue('recovery success');\r\n      const result = await circuitBreaker.execute(successfulOperation);\r\n\r\n      expect(result).toBe('recovery success');\r\n      expect(circuitBreaker.getMetrics().state).toBe(CircuitBreakerState.CLOSED);\r\n      expect(circuitBreaker.getMetrics().failureCount).toBe(0);\r\n    });\r\n\r\n    it('should transition from HALF_OPEN to OPEN on failure', async () => {\r\n      const circuitBreaker = new CircuitBreaker({\r\n        failureThreshold: 1,\r\n        recoveryTimeout: 50,\r\n        monitoringPeriod: 5000,\r\n        halfOpenMaxCalls: 1,\r\n      });\r\n\r\n      // Open the circuit\r\n      const failingOperation = jest.fn().mockRejectedValue(new Error('Failure'));\r\n      await expect(circuitBreaker.execute(failingOperation)).rejects.toThrow('Failure');\r\n\r\n      // Wait for recovery\r\n      await new Promise(resolve => setTimeout(resolve, 60));\r\n\r\n      // Failed call should reopen the circuit\r\n      await expect(circuitBreaker.execute(failingOperation)).rejects.toThrow('Failure');\r\n      expect(circuitBreaker.getMetrics().state).toBe(CircuitBreakerState.OPEN);\r\n    });\r\n\r\n    it('should limit calls in HALF_OPEN state', async () => {\r\n      const circuitBreaker = new CircuitBreaker({\r\n        failureThreshold: 1,\r\n        recoveryTimeout: 50,\r\n        monitoringPeriod: 5000,\r\n        halfOpenMaxCalls: 1,\r\n      });\r\n\r\n      // Open the circuit\r\n      const failingOperation = jest.fn().mockRejectedValue(new Error('Failure'));\r\n      await expect(circuitBreaker.execute(failingOperation)).rejects.toThrow('Failure');\r\n\r\n      // Wait for recovery\r\n      await new Promise(resolve => setTimeout(resolve, 60));\r\n\r\n      // First call should be allowed (transitions to HALF_OPEN)\r\n      const slowOperation = jest.fn().mockImplementation(() => \r\n        new Promise(resolve => setTimeout(() => resolve('slow success'), 100))\r\n      );\r\n\r\n      // Start first call (should be allowed)\r\n      const firstCallPromise = circuitBreaker.execute(slowOperation);\r\n\r\n      // Second call should be rejected due to halfOpenMaxCalls limit\r\n      await expect(circuitBreaker.execute(slowOperation)).rejects.toThrow(ServiceUnavailableException);\r\n\r\n      // Wait for first call to complete\r\n      const result = await firstCallPromise;\r\n      expect(result).toBe('slow success');\r\n      expect(circuitBreaker.getMetrics().state).toBe(CircuitBreakerState.CLOSED);\r\n    });\r\n\r\n    it('should call onStateChange callback', async () => {\r\n      const onStateChange = jest.fn();\r\n      const circuitBreaker = new CircuitBreaker({\r\n        failureThreshold: 1,\r\n        recoveryTimeout: 50,\r\n        monitoringPeriod: 5000,\r\n        halfOpenMaxCalls: 1,\r\n        onStateChange,\r\n      });\r\n\r\n      const failingOperation = jest.fn().mockRejectedValue(new Error('Failure'));\r\n\r\n      // Trigger state change to OPEN\r\n      await expect(circuitBreaker.execute(failingOperation)).rejects.toThrow('Failure');\r\n      expect(onStateChange).toHaveBeenCalledWith(CircuitBreakerState.OPEN);\r\n\r\n      // Wait for recovery and trigger state change to HALF_OPEN then CLOSED\r\n      await new Promise(resolve => setTimeout(resolve, 60));\r\n      const successfulOperation = jest.fn().mockResolvedValue('success');\r\n      await circuitBreaker.execute(successfulOperation);\r\n\r\n      expect(onStateChange).toHaveBeenCalledWith(CircuitBreakerState.HALF_OPEN);\r\n      expect(onStateChange).toHaveBeenCalledWith(CircuitBreakerState.CLOSED);\r\n    });\r\n\r\n    it('should provide accurate metrics', async () => {\r\n      const circuitBreaker = new CircuitBreaker({\r\n        failureThreshold: 3,\r\n        recoveryTimeout: 1000,\r\n        monitoringPeriod: 5000,\r\n        halfOpenMaxCalls: 1,\r\n      });\r\n\r\n      const successfulOperation = jest.fn().mockResolvedValue('success');\r\n      const failingOperation = jest.fn().mockRejectedValue(new Error('failure'));\r\n\r\n      // Execute some operations\r\n      await circuitBreaker.execute(successfulOperation);\r\n      await circuitBreaker.execute(successfulOperation);\r\n      await expect(circuitBreaker.execute(failingOperation)).rejects.toThrow('failure');\r\n\r\n      const metrics = circuitBreaker.getMetrics();\r\n      expect(metrics.successCount).toBe(2);\r\n      expect(metrics.failureCount).toBe(1);\r\n      expect(metrics.totalCalls).toBe(3);\r\n      expect(metrics.state).toBe(CircuitBreakerState.CLOSED);\r\n      expect(metrics.lastSuccessTime).toBeDefined();\r\n      expect(metrics.lastFailureTime).toBeDefined();\r\n    });\r\n\r\n    it('should reset circuit breaker state', () => {\r\n      const circuitBreaker = new CircuitBreaker({\r\n        failureThreshold: 1,\r\n        recoveryTimeout: 1000,\r\n        monitoringPeriod: 5000,\r\n        halfOpenMaxCalls: 1,\r\n      });\r\n\r\n      // Manually set some state\r\n      circuitBreaker.forceOpen();\r\n      expect(circuitBreaker.getMetrics().state).toBe(CircuitBreakerState.OPEN);\r\n\r\n      // Reset should return to CLOSED\r\n      circuitBreaker.reset();\r\n      const metrics = circuitBreaker.getMetrics();\r\n      expect(metrics.state).toBe(CircuitBreakerState.CLOSED);\r\n      expect(metrics.failureCount).toBe(0);\r\n      expect(metrics.successCount).toBe(0);\r\n    });\r\n\r\n    it('should force circuit to OPEN state', () => {\r\n      const circuitBreaker = new CircuitBreaker({\r\n        failureThreshold: 5,\r\n        recoveryTimeout: 1000,\r\n        monitoringPeriod: 5000,\r\n        halfOpenMaxCalls: 1,\r\n      });\r\n\r\n      expect(circuitBreaker.getMetrics().state).toBe(CircuitBreakerState.CLOSED);\r\n\r\n      circuitBreaker.forceOpen();\r\n      expect(circuitBreaker.getMetrics().state).toBe(CircuitBreakerState.OPEN);\r\n    });\r\n  });\r\n\r\n  describe('@CircuitBreakerProtection decorator', () => {\r\n    it('should protect method with circuit breaker', async () => {\r\n      let callCount = 0;\r\n\r\n      class TestService {\r\n        @CircuitBreakerProtection({\r\n          failureThreshold: 2,\r\n          recoveryTimeout: 100,\r\n          monitoringPeriod: 5000,\r\n          halfOpenMaxCalls: 1,\r\n        })\r\n        async protectedMethod(shouldFail: boolean): Promise<string> {\r\n          callCount++;\r\n          if (shouldFail) {\r\n            throw new Error('Method failed');\r\n          }\r\n          return `success-${callCount}`;\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n\r\n      // Successful calls should work\r\n      const result1 = await service.protectedMethod(false);\r\n      expect(result1).toBe('success-1');\r\n\r\n      // Trigger failures to open circuit\r\n      await expect(service.protectedMethod(true)).rejects.toThrow('Method failed');\r\n      await expect(service.protectedMethod(true)).rejects.toThrow('Method failed');\r\n\r\n      // Circuit should now be open\r\n      await expect(service.protectedMethod(false)).rejects.toThrow(ServiceUnavailableException);\r\n      expect(callCount).toBe(3); // Method not called when circuit is open\r\n    });\r\n\r\n    it('should attach circuit breaker instance to decorated method', () => {\r\n      class TestService {\r\n        @CircuitBreakerProtection({\r\n          failureThreshold: 3,\r\n          recoveryTimeout: 1000,\r\n          monitoringPeriod: 5000,\r\n          halfOpenMaxCalls: 1,\r\n        })\r\n        async decoratedMethod(): Promise<string> {\r\n          return 'success';\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n      const circuitBreaker = (service.decoratedMethod as any).circuitBreaker;\r\n\r\n      expect(circuitBreaker).toBeInstanceOf(CircuitBreaker);\r\n      expect(circuitBreaker.getMetrics().state).toBe(CircuitBreakerState.CLOSED);\r\n    });\r\n\r\n    it('should handle async operations correctly', async () => {\r\n      let callCount = 0;\r\n\r\n      class TestService {\r\n        @CircuitBreakerProtection({\r\n          failureThreshold: 1,\r\n          recoveryTimeout: 50,\r\n          monitoringPeriod: 5000,\r\n          halfOpenMaxCalls: 1,\r\n        })\r\n        async asyncMethod(delay: number, shouldFail: boolean): Promise<string> {\r\n          callCount++;\r\n          await new Promise(resolve => setTimeout(resolve, delay));\r\n          if (shouldFail) {\r\n            throw new Error('Async failure');\r\n          }\r\n          return `async-success-${callCount}`;\r\n        }\r\n      }\r\n\r\n      const service = new TestService();\r\n\r\n      // Successful async call\r\n      const result1 = await service.asyncMethod(10, false);\r\n      expect(result1).toBe('async-success-1');\r\n\r\n      // Failed async call - should open circuit\r\n      await expect(service.asyncMethod(10, true)).rejects.toThrow('Async failure');\r\n\r\n      // Circuit should be open\r\n      await expect(service.asyncMethod(10, false)).rejects.toThrow(ServiceUnavailableException);\r\n      expect(callCount).toBe(2);\r\n    });\r\n\r\n    it('should work with multiple instances', async () => {\r\n      class TestService {\r\n        @CircuitBreakerProtection({\r\n          failureThreshold: 1,\r\n          recoveryTimeout: 1000,\r\n          monitoringPeriod: 5000,\r\n          halfOpenMaxCalls: 1,\r\n        })\r\n        async instanceMethod(): Promise<string> {\r\n          throw new Error('Always fails');\r\n        }\r\n      }\r\n\r\n      const service1 = new TestService();\r\n      const service2 = new TestService();\r\n\r\n      // The circuit breaker is shared across all instances of the same method\r\n      // First failure should trigger the method to fail normally\r\n      await expect(service1.instanceMethod()).rejects.toThrow('Always fails');\r\n      \r\n      // Second failure (from any instance) should open the circuit\r\n      // Since the circuit is now open, subsequent calls should throw ServiceUnavailableException\r\n      await expect(service2.instanceMethod()).rejects.toThrow(ServiceUnavailableException);\r\n      await expect(service1.instanceMethod()).rejects.toThrow(ServiceUnavailableException);\r\n    });\r\n  });\r\n\r\n  describe('error scenarios', () => {\r\n    it('should handle ServiceUnavailableException with proper details', async () => {\r\n      const circuitBreaker = new CircuitBreaker({\r\n        failureThreshold: 1,\r\n        recoveryTimeout: 1000,\r\n        monitoringPeriod: 5000,\r\n        halfOpenMaxCalls: 1,\r\n      });\r\n\r\n      // Open the circuit\r\n      const failingOperation = jest.fn().mockRejectedValue(new Error('Failure'));\r\n      await expect(circuitBreaker.execute(failingOperation)).rejects.toThrow('Failure');\r\n\r\n      // Try to execute when circuit is open\r\n      try {\r\n        await circuitBreaker.execute(failingOperation);\r\n        fail('Should have thrown ServiceUnavailableException');\r\n      } catch (error) {\r\n        expect(error).toBeInstanceOf(ServiceUnavailableException);\r\n        const serviceError = error as ServiceUnavailableException;\r\n        const circuitInfo = serviceError.getCircuitBreakerInfo();\r\n        expect(circuitInfo).toMatchObject({\r\n          failureCount: 1,\r\n          failureThreshold: expect.any(Number),\r\n        });\r\n        expect(circuitInfo?.nextRetryTime).toBeDefined();\r\n      }\r\n    });\r\n\r\n    it('should handle half-open state limit exceeded', async () => {\r\n      const circuitBreaker = new CircuitBreaker({\r\n        failureThreshold: 1,\r\n        recoveryTimeout: 50,\r\n        monitoringPeriod: 5000,\r\n        halfOpenMaxCalls: 1,\r\n      });\r\n\r\n      // Open the circuit\r\n      await expect(circuitBreaker.execute(() => Promise.reject(new Error('Failure'))))\r\n        .rejects.toThrow('Failure');\r\n\r\n      // Wait for recovery\r\n      await new Promise(resolve => setTimeout(resolve, 60));\r\n\r\n      // Start a slow operation to keep circuit in HALF_OPEN\r\n      const slowOperation = () => new Promise(resolve => setTimeout(() => resolve('slow'), 100));\r\n      const slowPromise = circuitBreaker.execute(slowOperation);\r\n\r\n      // Try another call while in HALF_OPEN - should be rejected\r\n      try {\r\n        await circuitBreaker.execute(() => Promise.resolve('fast'));\r\n        fail('Should have thrown ServiceUnavailableException');\r\n      } catch (error) {\r\n        expect(error).toBeInstanceOf(ServiceUnavailableException);\r\n        const serviceError = error as ServiceUnavailableException;\r\n        expect(serviceError.context).toMatchObject({\r\n          state: CircuitBreakerState.HALF_OPEN,\r\n          maxCalls: 1,\r\n        });\r\n      }\r\n\r\n      // Wait for slow operation to complete\r\n      await slowPromise;\r\n    });\r\n  });\r\n});"], "version": 3}