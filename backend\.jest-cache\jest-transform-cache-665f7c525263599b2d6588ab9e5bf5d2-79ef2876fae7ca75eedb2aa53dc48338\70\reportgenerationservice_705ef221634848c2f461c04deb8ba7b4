a701335b00666dd32ece43b4d4c2e611
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ReportGenerationService_1;
var _a, _b, _c, _d, _e, _f, _g;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReportGenerationService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const report_entity_1 = require("../../domain/entities/report.entity");
const report_execution_entity_1 = require("../../domain/entities/report-execution.entity");
const logger_service_1 = require("../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../infrastructure/logging/audit/audit.service");
const data_aggregation_service_1 = require("./data-aggregation.service");
const report_export_service_1 = require("./report-export.service");
/**
 * Report Generation service
 * Handles report execution, data processing, and output generation
 */
let ReportGenerationService = ReportGenerationService_1 = class ReportGenerationService {
    constructor(reportRepository, executionRepository, dataSource, loggerService, auditService, dataAggregationService, reportExportService) {
        this.reportRepository = reportRepository;
        this.executionRepository = executionRepository;
        this.dataSource = dataSource;
        this.loggerService = loggerService;
        this.auditService = auditService;
        this.dataAggregationService = dataAggregationService;
        this.reportExportService = reportExportService;
        this.logger = new common_1.Logger(ReportGenerationService_1.name);
    }
    /**
     * Execute a report and generate output
     * @param reportId Report ID
     * @param parameters Execution parameters
     * @param userId User executing the report
     * @param triggerType How the execution was triggered
     * @returns Report execution
     */
    async executeReport(reportId, parameters = {}, userId, triggerType = 'manual') {
        try {
            this.logger.debug('Starting report execution', {
                reportId,
                parameters,
                userId,
                triggerType,
            });
            // Get report configuration
            const report = await this.reportRepository.findOne({
                where: { id: reportId },
            });
            if (!report) {
                throw new common_1.NotFoundException('Report not found');
            }
            if (report.status !== 'active') {
                throw new common_1.BadRequestException('Report is not active');
            }
            // Validate report configuration
            const validation = report.validateConfiguration();
            if (!validation.isValid) {
                throw new common_1.BadRequestException(`Report configuration is invalid: ${validation.errors.join(', ')}`);
            }
            // Create execution record
            const execution = this.executionRepository.create({
                reportId,
                status: 'pending',
                triggerType,
                parameters,
                executedBy: userId,
            });
            const savedExecution = await this.executionRepository.save(execution);
            // Start execution asynchronously
            this.processReportExecution(savedExecution, report, userId);
            await this.auditService.logUserAction(userId, 'execute', 'report', reportId, {
                executionId: savedExecution.id,
                triggerType,
                parameters,
            });
            this.logger.log('Report execution started', {
                reportId,
                executionId: savedExecution.id,
                userId,
            });
            return savedExecution;
        }
        catch (error) {
            this.logger.error('Failed to start report execution', {
                reportId,
                error: error.message,
                userId,
            });
            throw error;
        }
    }
    /**
     * Process report execution
     * @param execution Report execution
     * @param report Report configuration
     * @param userId User ID
     */
    async processReportExecution(execution, report, userId) {
        try {
            // Start execution
            execution.start();
            await this.executionRepository.save(execution);
            this.logger.debug('Processing report execution', {
                reportId: report.id,
                executionId: execution.id,
            });
            // Step 1: Data collection
            execution.updateProgress(10, 'Collecting data');
            await this.executionRepository.save(execution);
            const reportData = await this.collectReportData(report, execution.parameters);
            // Step 2: Data processing
            execution.updateProgress(40, 'Processing data');
            await this.executionRepository.save(execution);
            const processedData = await this.processReportData(report, reportData);
            // Step 3: Generate visualizations
            execution.updateProgress(70, 'Generating visualizations');
            await this.executionRepository.save(execution);
            const visualizations = await this.generateVisualizations(report, processedData);
            // Step 4: Export report
            execution.updateProgress(90, 'Exporting report');
            await this.executionRepository.save(execution);
            const exportFormat = execution.parameters?.exportFormat || 'pdf';
            const outputPath = await this.exportReport(report, processedData, visualizations, exportFormat);
            // Complete execution
            const finalReportData = {
                summary: {
                    totalRecords: processedData.totalRecords || 0,
                    dataPoints: processedData.dataPoints || 0,
                    generatedAt: new Date().toISOString(),
                    timeRange: this.getTimeRangeDescription(report, execution.parameters),
                },
                sections: processedData.sections || [],
                charts: visualizations.charts || [],
                tables: processedData.tables || [],
                metrics: processedData.metrics || [],
            };
            execution.complete(outputPath, exportFormat, finalReportData);
            execution.recordsProcessed = processedData.totalRecords || 0;
            execution.outputSizeBytes = await this.getFileSize(outputPath);
            await this.executionRepository.save(execution);
            // Update report statistics
            report.updateExecutionStats(execution.durationMs || 0, Number(execution.outputSizeBytes || 0), 'success');
            await this.reportRepository.save(report);
            this.logger.log('Report execution completed successfully', {
                reportId: report.id,
                executionId: execution.id,
                duration: execution.durationFormatted,
                outputSize: execution.outputSizeFormatted,
            });
        }
        catch (error) {
            this.logger.error('Report execution failed', {
                reportId: report.id,
                executionId: execution.id,
                error: error.message,
            });
            execution.fail(error);
            await this.executionRepository.save(execution);
            // Update report statistics
            report.updateExecutionStats(execution.durationMs || 0, 0, 'failed');
            await this.reportRepository.save(report);
        }
    }
    /**
     * Collect data for report
     * @param report Report configuration
     * @param parameters Execution parameters
     * @returns Raw data
     */
    async collectReportData(report, parameters) {
        const dataSources = report.getDataSources();
        const collectedData = {};
        for (const dataSourceName of dataSources) {
            try {
                const data = await this.dataAggregationService.aggregateData(dataSourceName, {
                    ...report.configuration,
                    ...parameters,
                });
                collectedData[dataSourceName] = data;
            }
            catch (error) {
                this.logger.warn('Failed to collect data from source', {
                    dataSource: dataSourceName,
                    error: error.message,
                });
                collectedData[dataSourceName] = { error: error.message, data: [] };
            }
        }
        return collectedData;
    }
    /**
     * Process collected data
     * @param report Report configuration
     * @param rawData Raw collected data
     * @returns Processed data
     */
    async processReportData(report, rawData) {
        const processedData = {
            totalRecords: 0,
            dataPoints: 0,
            sections: [],
            tables: [],
            metrics: [],
        };
        // Process each data source
        for (const [dataSourceName, sourceData] of Object.entries(rawData)) {
            if (sourceData.error) {
                processedData.sections.push({
                    id: `error-${dataSourceName}`,
                    title: `Error in ${dataSourceName}`,
                    type: 'text',
                    data: { content: `Failed to load data: ${sourceData.error}` },
                });
                continue;
            }
            const data = sourceData.data || sourceData;
            processedData.totalRecords += Array.isArray(data) ? data.length : 1;
            // Apply grouping and aggregations
            if (report.configuration.groupBy) {
                const groupedData = this.groupData(data, report.configuration.groupBy);
                processedData.sections.push({
                    id: `grouped-${dataSourceName}`,
                    title: `${dataSourceName} (Grouped)`,
                    type: 'table',
                    data: groupedData,
                });
            }
            // Apply aggregations
            if (report.configuration.aggregations) {
                const aggregatedData = this.aggregateData(data, report.configuration.aggregations);
                processedData.metrics.push(...aggregatedData);
            }
            // Create tables
            if (Array.isArray(data) && data.length > 0) {
                const headers = Object.keys(data[0]);
                const rows = data.map(item => headers.map(header => item[header]));
                processedData.tables.push({
                    id: `table-${dataSourceName}`,
                    title: dataSourceName,
                    headers,
                    rows,
                    metadata: {
                        totalRows: data.length,
                        dataSource: dataSourceName,
                    },
                });
            }
        }
        return processedData;
    }
    /**
     * Generate visualizations for report
     * @param report Report configuration
     * @param processedData Processed data
     * @returns Visualization data
     */
    async generateVisualizations(report, processedData) {
        const visualizations = {
            charts: [],
        };
        if (report.configuration.visualizations) {
            for (const vizConfig of report.configuration.visualizations) {
                try {
                    const chartData = await this.generateChart(vizConfig, processedData);
                    visualizations.charts.push({
                        id: `chart-${Date.now()}-${Math.random()}`,
                        title: vizConfig.title,
                        type: vizConfig.chartType || 'bar',
                        data: chartData,
                        configuration: vizConfig.configuration,
                    });
                }
                catch (error) {
                    this.logger.warn('Failed to generate visualization', {
                        visualization: vizConfig.title,
                        error: error.message,
                    });
                }
            }
        }
        return visualizations;
    }
    /**
     * Export report to specified format
     * @param report Report configuration
     * @param processedData Processed data
     * @param visualizations Visualization data
     * @param format Export format
     * @returns Output file path
     */
    async exportReport(report, processedData, visualizations, format) {
        return await this.reportExportService.exportReport({
            report,
            data: processedData,
            visualizations,
        }, format);
    }
    /**
     * Get file size in bytes
     * @param filePath File path
     * @returns File size in bytes
     */
    async getFileSize(filePath) {
        try {
            const fs = require('fs').promises;
            const stats = await fs.stat(filePath);
            return stats.size;
        }
        catch (error) {
            this.logger.warn('Failed to get file size', {
                filePath,
                error: error.message,
            });
            return 0;
        }
    }
    /**
     * Get time range description
     * @param report Report configuration
     * @param parameters Execution parameters
     * @returns Time range description
     */
    getTimeRangeDescription(report, parameters) {
        const timeRange = parameters?.timeRange || report.configuration.timeRange;
        if (!timeRange) {
            return 'All time';
        }
        if (timeRange.type === 'relative') {
            return `Last ${timeRange.value}`;
        }
        else {
            return `${timeRange.startDate} to ${timeRange.endDate}`;
        }
    }
    /**
     * Group data by specified fields
     * @param data Data to group
     * @param groupBy Fields to group by
     * @returns Grouped data
     */
    groupData(data, groupBy) {
        if (!Array.isArray(data) || groupBy.length === 0) {
            return data;
        }
        const grouped = data.reduce((acc, item) => {
            const key = groupBy.map(field => item[field]).join('|');
            if (!acc[key]) {
                acc[key] = [];
            }
            acc[key].push(item);
            return acc;
        }, {});
        return Object.entries(grouped).map(([key, items]) => ({
            group: key,
            count: items.length,
            items,
        }));
    }
    /**
     * Apply aggregations to data
     * @param data Data to aggregate
     * @param aggregations Aggregation configurations
     * @returns Aggregated metrics
     */
    aggregateData(data, aggregations) {
        if (!Array.isArray(data) || aggregations.length === 0) {
            return [];
        }
        return aggregations.map(agg => {
            let value;
            const field = agg.field;
            switch (agg.function) {
                case 'count':
                    value = data.length;
                    break;
                case 'sum':
                    value = data.reduce((sum, item) => sum + (Number(item[field]) || 0), 0);
                    break;
                case 'avg':
                    const sum = data.reduce((sum, item) => sum + (Number(item[field]) || 0), 0);
                    value = data.length > 0 ? sum / data.length : 0;
                    break;
                case 'min':
                    value = Math.min(...data.map(item => Number(item[field]) || 0));
                    break;
                case 'max':
                    value = Math.max(...data.map(item => Number(item[field]) || 0));
                    break;
                default:
                    value = 0;
            }
            return {
                id: agg.alias || `${agg.function}_${field}`,
                title: agg.alias || `${agg.function.toUpperCase()} of ${field}`,
                value,
                unit: agg.unit,
            };
        });
    }
    /**
     * Generate chart data
     * @param vizConfig Visualization configuration
     * @param processedData Processed data
     * @returns Chart data
     */
    async generateChart(vizConfig, processedData) {
        // This is a simplified chart generation
        // In a real implementation, this would use a charting library
        const dataSource = processedData.tables?.find(t => t.id.includes(vizConfig.dataSource));
        if (!dataSource) {
            return { labels: [], datasets: [] };
        }
        const labels = dataSource.rows.map((row, index) => row[0] || `Item ${index + 1}`);
        const values = dataSource.rows.map(row => Number(row[1]) || 0);
        return {
            labels,
            datasets: [{
                    label: vizConfig.title,
                    data: values,
                    backgroundColor: vizConfig.configuration?.colors || ['#3498db'],
                }],
        };
    }
    /**
     * Get report execution by ID
     * @param executionId Execution ID
     * @returns Report execution
     */
    async getExecution(executionId) {
        try {
            return await this.executionRepository.findOne({
                where: { id: executionId },
                relations: ['report'],
            });
        }
        catch (error) {
            this.logger.error('Failed to get report execution', {
                executionId,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Cancel running report execution
     * @param executionId Execution ID
     * @param userId User cancelling the execution
     * @returns Updated execution
     */
    async cancelExecution(executionId, userId) {
        try {
            const execution = await this.getExecution(executionId);
            if (!execution) {
                throw new common_1.NotFoundException('Report execution not found');
            }
            if (!execution.isRunning) {
                throw new common_1.BadRequestException('Report execution is not running');
            }
            execution.cancel('Cancelled by user');
            const updatedExecution = await this.executionRepository.save(execution);
            await this.auditService.logUserAction(userId, 'cancel', 'report_execution', executionId, {
                reportId: execution.reportId,
                reason: 'user_request',
            });
            this.logger.log('Report execution cancelled', {
                executionId,
                userId,
            });
            return updatedExecution;
        }
        catch (error) {
            this.logger.error('Failed to cancel report execution', {
                executionId,
                error: error.message,
                userId,
            });
            throw error;
        }
    }
};
exports.ReportGenerationService = ReportGenerationService;
exports.ReportGenerationService = ReportGenerationService = ReportGenerationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(report_entity_1.Report)),
    __param(1, (0, typeorm_1.InjectRepository)(report_execution_entity_1.ReportExecution)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof typeorm_2.DataSource !== "undefined" && typeorm_2.DataSource) === "function" ? _c : Object, typeof (_d = typeof logger_service_1.LoggerService !== "undefined" && logger_service_1.LoggerService) === "function" ? _d : Object, typeof (_e = typeof audit_service_1.AuditService !== "undefined" && audit_service_1.AuditService) === "function" ? _e : Object, typeof (_f = typeof data_aggregation_service_1.DataAggregationService !== "undefined" && data_aggregation_service_1.DataAggregationService) === "function" ? _f : Object, typeof (_g = typeof report_export_service_1.ReportExportService !== "undefined" && report_export_service_1.ReportExportService) === "function" ? _g : Object])
], ReportGenerationService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxtb2R1bGVzXFxyZXBvcnRpbmctYW5hbHl0aWNzXFxhcHBsaWNhdGlvblxcc2VydmljZXNcXHJlcG9ydC1nZW5lcmF0aW9uLnNlcnZpY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSwyQ0FBNEY7QUFDNUYsNkNBQW1EO0FBQ25ELHFDQUFpRDtBQUNqRCx1RUFBNkQ7QUFDN0QsMkZBQWdGO0FBQ2hGLHNGQUFrRjtBQUNsRiwwRkFBc0Y7QUFDdEYseUVBQW9FO0FBQ3BFLG1FQUE4RDtBQUU5RDs7O0dBR0c7QUFFSSxJQUFNLHVCQUF1QiwrQkFBN0IsTUFBTSx1QkFBdUI7SUFHbEMsWUFFRSxnQkFBcUQsRUFFckQsbUJBQWlFLEVBQ2hELFVBQXNCLEVBQ3RCLGFBQTRCLEVBQzVCLFlBQTBCLEVBQzFCLHNCQUE4QyxFQUM5QyxtQkFBd0M7UUFQeEMscUJBQWdCLEdBQWhCLGdCQUFnQixDQUFvQjtRQUVwQyx3QkFBbUIsR0FBbkIsbUJBQW1CLENBQTZCO1FBQ2hELGVBQVUsR0FBVixVQUFVLENBQVk7UUFDdEIsa0JBQWEsR0FBYixhQUFhLENBQWU7UUFDNUIsaUJBQVksR0FBWixZQUFZLENBQWM7UUFDMUIsMkJBQXNCLEdBQXRCLHNCQUFzQixDQUF3QjtRQUM5Qyx3QkFBbUIsR0FBbkIsbUJBQW1CLENBQXFCO1FBWDFDLFdBQU0sR0FBRyxJQUFJLGVBQU0sQ0FBQyx5QkFBdUIsQ0FBQyxJQUFJLENBQUMsQ0FBQztJQVloRSxDQUFDO0lBRUo7Ozs7Ozs7T0FPRztJQUNILEtBQUssQ0FBQyxhQUFhLENBQ2pCLFFBQWdCLEVBQ2hCLGFBVUksRUFBRSxFQUNOLE1BQWMsRUFDZCxjQUEwRCxRQUFRO1FBRWxFLElBQUksQ0FBQztZQUNILElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLDJCQUEyQixFQUFFO2dCQUM3QyxRQUFRO2dCQUNSLFVBQVU7Z0JBQ1YsTUFBTTtnQkFDTixXQUFXO2FBQ1osQ0FBQyxDQUFDO1lBRUgsMkJBQTJCO1lBQzNCLE1BQU0sTUFBTSxHQUFHLE1BQU0sSUFBSSxDQUFDLGdCQUFnQixDQUFDLE9BQU8sQ0FBQztnQkFDakQsS0FBSyxFQUFFLEVBQUUsRUFBRSxFQUFFLFFBQVEsRUFBRTthQUN4QixDQUFDLENBQUM7WUFFSCxJQUFJLENBQUMsTUFBTSxFQUFFLENBQUM7Z0JBQ1osTUFBTSxJQUFJLDBCQUFpQixDQUFDLGtCQUFrQixDQUFDLENBQUM7WUFDbEQsQ0FBQztZQUVELElBQUksTUFBTSxDQUFDLE1BQU0sS0FBSyxRQUFRLEVBQUUsQ0FBQztnQkFDL0IsTUFBTSxJQUFJLDRCQUFtQixDQUFDLHNCQUFzQixDQUFDLENBQUM7WUFDeEQsQ0FBQztZQUVELGdDQUFnQztZQUNoQyxNQUFNLFVBQVUsR0FBRyxNQUFNLENBQUMscUJBQXFCLEVBQUUsQ0FBQztZQUNsRCxJQUFJLENBQUMsVUFBVSxDQUFDLE9BQU8sRUFBRSxDQUFDO2dCQUN4QixNQUFNLElBQUksNEJBQW1CLENBQUMsb0NBQW9DLFVBQVUsQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQztZQUNwRyxDQUFDO1lBRUQsMEJBQTBCO1lBQzFCLE1BQU0sU0FBUyxHQUFHLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxNQUFNLENBQUM7Z0JBQ2hELFFBQVE7Z0JBQ1IsTUFBTSxFQUFFLFNBQVM7Z0JBQ2pCLFdBQVc7Z0JBQ1gsVUFBVTtnQkFDVixVQUFVLEVBQUUsTUFBTTthQUNuQixDQUFDLENBQUM7WUFFSCxNQUFNLGNBQWMsR0FBRyxNQUFNLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUM7WUFFdEUsaUNBQWlDO1lBQ2pDLElBQUksQ0FBQyxzQkFBc0IsQ0FBQyxjQUFjLEVBQUUsTUFBTSxFQUFFLE1BQU0sQ0FBQyxDQUFDO1lBRTVELE1BQU0sSUFBSSxDQUFDLFlBQVksQ0FBQyxhQUFhLENBQ25DLE1BQU0sRUFDTixTQUFTLEVBQ1QsUUFBUSxFQUNSLFFBQVEsRUFDUjtnQkFDRSxXQUFXLEVBQUUsY0FBYyxDQUFDLEVBQUU7Z0JBQzlCLFdBQVc7Z0JBQ1gsVUFBVTthQUNYLENBQ0YsQ0FBQztZQUVGLElBQUksQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLDBCQUEwQixFQUFFO2dCQUMxQyxRQUFRO2dCQUNSLFdBQVcsRUFBRSxjQUFjLENBQUMsRUFBRTtnQkFDOUIsTUFBTTthQUNQLENBQUMsQ0FBQztZQUVILE9BQU8sY0FBYyxDQUFDO1FBQ3hCLENBQUM7UUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1lBQ2YsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsa0NBQWtDLEVBQUU7Z0JBQ3BELFFBQVE7Z0JBQ1IsS0FBSyxFQUFFLEtBQUssQ0FBQyxPQUFPO2dCQUNwQixNQUFNO2FBQ1AsQ0FBQyxDQUFDO1lBQ0gsTUFBTSxLQUFLLENBQUM7UUFDZCxDQUFDO0lBQ0gsQ0FBQztJQUVEOzs7OztPQUtHO0lBQ0ssS0FBSyxDQUFDLHNCQUFzQixDQUNsQyxTQUEwQixFQUMxQixNQUFjLEVBQ2QsTUFBYztRQUVkLElBQUksQ0FBQztZQUNILGtCQUFrQjtZQUNsQixTQUFTLENBQUMsS0FBSyxFQUFFLENBQUM7WUFDbEIsTUFBTSxJQUFJLENBQUMsbUJBQW1CLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDO1lBRS9DLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLDZCQUE2QixFQUFFO2dCQUMvQyxRQUFRLEVBQUUsTUFBTSxDQUFDLEVBQUU7Z0JBQ25CLFdBQVcsRUFBRSxTQUFTLENBQUMsRUFBRTthQUMxQixDQUFDLENBQUM7WUFFSCwwQkFBMEI7WUFDMUIsU0FBUyxDQUFDLGNBQWMsQ0FBQyxFQUFFLEVBQUUsaUJBQWlCLENBQUMsQ0FBQztZQUNoRCxNQUFNLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUM7WUFFL0MsTUFBTSxVQUFVLEdBQUcsTUFBTSxJQUFJLENBQUMsaUJBQWlCLENBQUMsTUFBTSxFQUFFLFNBQVMsQ0FBQyxVQUFVLENBQUMsQ0FBQztZQUU5RSwwQkFBMEI7WUFDMUIsU0FBUyxDQUFDLGNBQWMsQ0FBQyxFQUFFLEVBQUUsaUJBQWlCLENBQUMsQ0FBQztZQUNoRCxNQUFNLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUM7WUFFL0MsTUFBTSxhQUFhLEdBQUcsTUFBTSxJQUFJLENBQUMsaUJBQWlCLENBQUMsTUFBTSxFQUFFLFVBQVUsQ0FBQyxDQUFDO1lBRXZFLGtDQUFrQztZQUNsQyxTQUFTLENBQUMsY0FBYyxDQUFDLEVBQUUsRUFBRSwyQkFBMkIsQ0FBQyxDQUFDO1lBQzFELE1BQU0sSUFBSSxDQUFDLG1CQUFtQixDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQztZQUUvQyxNQUFNLGNBQWMsR0FBRyxNQUFNLElBQUksQ0FBQyxzQkFBc0IsQ0FBQyxNQUFNLEVBQUUsYUFBYSxDQUFDLENBQUM7WUFFaEYsd0JBQXdCO1lBQ3hCLFNBQVMsQ0FBQyxjQUFjLENBQUMsRUFBRSxFQUFFLGtCQUFrQixDQUFDLENBQUM7WUFDakQsTUFBTSxJQUFJLENBQUMsbUJBQW1CLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDO1lBRS9DLE1BQU0sWUFBWSxHQUFHLFNBQVMsQ0FBQyxVQUFVLEVBQUUsWUFBWSxJQUFJLEtBQUssQ0FBQztZQUNqRSxNQUFNLFVBQVUsR0FBRyxNQUFNLElBQUksQ0FBQyxZQUFZLENBQUMsTUFBTSxFQUFFLGFBQWEsRUFBRSxjQUFjLEVBQUUsWUFBWSxDQUFDLENBQUM7WUFFaEcscUJBQXFCO1lBQ3JCLE1BQU0sZUFBZSxHQUFHO2dCQUN0QixPQUFPLEVBQUU7b0JBQ1AsWUFBWSxFQUFFLGFBQWEsQ0FBQyxZQUFZLElBQUksQ0FBQztvQkFDN0MsVUFBVSxFQUFFLGFBQWEsQ0FBQyxVQUFVLElBQUksQ0FBQztvQkFDekMsV0FBVyxFQUFFLElBQUksSUFBSSxFQUFFLENBQUMsV0FBVyxFQUFFO29CQUNyQyxTQUFTLEVBQUUsSUFBSSxDQUFDLHVCQUF1QixDQUFDLE1BQU0sRUFBRSxTQUFTLENBQUMsVUFBVSxDQUFDO2lCQUN0RTtnQkFDRCxRQUFRLEVBQUUsYUFBYSxDQUFDLFFBQVEsSUFBSSxFQUFFO2dCQUN0QyxNQUFNLEVBQUUsY0FBYyxDQUFDLE1BQU0sSUFBSSxFQUFFO2dCQUNuQyxNQUFNLEVBQUUsYUFBYSxDQUFDLE1BQU0sSUFBSSxFQUFFO2dCQUNsQyxPQUFPLEVBQUUsYUFBYSxDQUFDLE9BQU8sSUFBSSxFQUFFO2FBQ3JDLENBQUM7WUFFRixTQUFTLENBQUMsUUFBUSxDQUFDLFVBQVUsRUFBRSxZQUFZLEVBQUUsZUFBZSxDQUFDLENBQUM7WUFDOUQsU0FBUyxDQUFDLGdCQUFnQixHQUFHLGFBQWEsQ0FBQyxZQUFZLElBQUksQ0FBQyxDQUFDO1lBQzdELFNBQVMsQ0FBQyxlQUFlLEdBQUcsTUFBTSxJQUFJLENBQUMsV0FBVyxDQUFDLFVBQVUsQ0FBQyxDQUFDO1lBRS9ELE1BQU0sSUFBSSxDQUFDLG1CQUFtQixDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQztZQUUvQywyQkFBMkI7WUFDM0IsTUFBTSxDQUFDLG9CQUFvQixDQUN6QixTQUFTLENBQUMsVUFBVSxJQUFJLENBQUMsRUFDekIsTUFBTSxDQUFDLFNBQVMsQ0FBQyxlQUFlLElBQUksQ0FBQyxDQUFDLEVBQ3RDLFNBQVMsQ0FDVixDQUFDO1lBQ0YsTUFBTSxJQUFJLENBQUMsZ0JBQWdCLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDO1lBRXpDLElBQUksQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLHlDQUF5QyxFQUFFO2dCQUN6RCxRQUFRLEVBQUUsTUFBTSxDQUFDLEVBQUU7Z0JBQ25CLFdBQVcsRUFBRSxTQUFTLENBQUMsRUFBRTtnQkFDekIsUUFBUSxFQUFFLFNBQVMsQ0FBQyxpQkFBaUI7Z0JBQ3JDLFVBQVUsRUFBRSxTQUFTLENBQUMsbUJBQW1CO2FBQzFDLENBQUMsQ0FBQztRQUNMLENBQUM7UUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1lBQ2YsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMseUJBQXlCLEVBQUU7Z0JBQzNDLFFBQVEsRUFBRSxNQUFNLENBQUMsRUFBRTtnQkFDbkIsV0FBVyxFQUFFLFNBQVMsQ0FBQyxFQUFFO2dCQUN6QixLQUFLLEVBQUUsS0FBSyxDQUFDLE9BQU87YUFDckIsQ0FBQyxDQUFDO1lBRUgsU0FBUyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUN0QixNQUFNLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUM7WUFFL0MsMkJBQTJCO1lBQzNCLE1BQU0sQ0FBQyxvQkFBb0IsQ0FDekIsU0FBUyxDQUFDLFVBQVUsSUFBSSxDQUFDLEVBQ3pCLENBQUMsRUFDRCxRQUFRLENBQ1QsQ0FBQztZQUNGLE1BQU0sSUFBSSxDQUFDLGdCQUFnQixDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQztRQUMzQyxDQUFDO0lBQ0gsQ0FBQztJQUVEOzs7OztPQUtHO0lBQ0ssS0FBSyxDQUFDLGlCQUFpQixDQUM3QixNQUFjLEVBQ2QsVUFBZ0I7UUFFaEIsTUFBTSxXQUFXLEdBQUcsTUFBTSxDQUFDLGNBQWMsRUFBRSxDQUFDO1FBQzVDLE1BQU0sYUFBYSxHQUF3QixFQUFFLENBQUM7UUFFOUMsS0FBSyxNQUFNLGNBQWMsSUFBSSxXQUFXLEVBQUUsQ0FBQztZQUN6QyxJQUFJLENBQUM7Z0JBQ0gsTUFBTSxJQUFJLEdBQUcsTUFBTSxJQUFJLENBQUMsc0JBQXNCLENBQUMsYUFBYSxDQUMxRCxjQUFjLEVBQ2Q7b0JBQ0UsR0FBRyxNQUFNLENBQUMsYUFBYTtvQkFDdkIsR0FBRyxVQUFVO2lCQUNkLENBQ0YsQ0FBQztnQkFDRixhQUFhLENBQUMsY0FBYyxDQUFDLEdBQUcsSUFBSSxDQUFDO1lBQ3ZDLENBQUM7WUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO2dCQUNmLElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLG9DQUFvQyxFQUFFO29CQUNyRCxVQUFVLEVBQUUsY0FBYztvQkFDMUIsS0FBSyxFQUFFLEtBQUssQ0FBQyxPQUFPO2lCQUNyQixDQUFDLENBQUM7Z0JBQ0gsYUFBYSxDQUFDLGNBQWMsQ0FBQyxHQUFHLEVBQUUsS0FBSyxFQUFFLEtBQUssQ0FBQyxPQUFPLEVBQUUsSUFBSSxFQUFFLEVBQUUsRUFBRSxDQUFDO1lBQ3JFLENBQUM7UUFDSCxDQUFDO1FBRUQsT0FBTyxhQUFhLENBQUM7SUFDdkIsQ0FBQztJQUVEOzs7OztPQUtHO0lBQ0ssS0FBSyxDQUFDLGlCQUFpQixDQUM3QixNQUFjLEVBQ2QsT0FBNEI7UUFFNUIsTUFBTSxhQUFhLEdBQVE7WUFDekIsWUFBWSxFQUFFLENBQUM7WUFDZixVQUFVLEVBQUUsQ0FBQztZQUNiLFFBQVEsRUFBRSxFQUFFO1lBQ1osTUFBTSxFQUFFLEVBQUU7WUFDVixPQUFPLEVBQUUsRUFBRTtTQUNaLENBQUM7UUFFRiwyQkFBMkI7UUFDM0IsS0FBSyxNQUFNLENBQUMsY0FBYyxFQUFFLFVBQVUsQ0FBQyxJQUFJLE1BQU0sQ0FBQyxPQUFPLENBQUMsT0FBTyxDQUFDLEVBQUUsQ0FBQztZQUNuRSxJQUFJLFVBQVUsQ0FBQyxLQUFLLEVBQUUsQ0FBQztnQkFDckIsYUFBYSxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUM7b0JBQzFCLEVBQUUsRUFBRSxTQUFTLGNBQWMsRUFBRTtvQkFDN0IsS0FBSyxFQUFFLFlBQVksY0FBYyxFQUFFO29CQUNuQyxJQUFJLEVBQUUsTUFBTTtvQkFDWixJQUFJLEVBQUUsRUFBRSxPQUFPLEVBQUUsd0JBQXdCLFVBQVUsQ0FBQyxLQUFLLEVBQUUsRUFBRTtpQkFDOUQsQ0FBQyxDQUFDO2dCQUNILFNBQVM7WUFDWCxDQUFDO1lBRUQsTUFBTSxJQUFJLEdBQUcsVUFBVSxDQUFDLElBQUksSUFBSSxVQUFVLENBQUM7WUFDM0MsYUFBYSxDQUFDLFlBQVksSUFBSSxLQUFLLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFFcEUsa0NBQWtDO1lBQ2xDLElBQUksTUFBTSxDQUFDLGFBQWEsQ0FBQyxPQUFPLEVBQUUsQ0FBQztnQkFDakMsTUFBTSxXQUFXLEdBQUcsSUFBSSxDQUFDLFNBQVMsQ0FBQyxJQUFJLEVBQUUsTUFBTSxDQUFDLGFBQWEsQ0FBQyxPQUFPLENBQUMsQ0FBQztnQkFDdkUsYUFBYSxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUM7b0JBQzFCLEVBQUUsRUFBRSxXQUFXLGNBQWMsRUFBRTtvQkFDL0IsS0FBSyxFQUFFLEdBQUcsY0FBYyxZQUFZO29CQUNwQyxJQUFJLEVBQUUsT0FBTztvQkFDYixJQUFJLEVBQUUsV0FBVztpQkFDbEIsQ0FBQyxDQUFDO1lBQ0wsQ0FBQztZQUVELHFCQUFxQjtZQUNyQixJQUFJLE1BQU0sQ0FBQyxhQUFhLENBQUMsWUFBWSxFQUFFLENBQUM7Z0JBQ3RDLE1BQU0sY0FBYyxHQUFHLElBQUksQ0FBQyxhQUFhLENBQUMsSUFBSSxFQUFFLE1BQU0sQ0FBQyxhQUFhLENBQUMsWUFBWSxDQUFDLENBQUM7Z0JBQ25GLGFBQWEsQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLEdBQUcsY0FBYyxDQUFDLENBQUM7WUFDaEQsQ0FBQztZQUVELGdCQUFnQjtZQUNoQixJQUFJLEtBQUssQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLElBQUksSUFBSSxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUUsQ0FBQztnQkFDM0MsTUFBTSxPQUFPLEdBQUcsTUFBTSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztnQkFDckMsTUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsTUFBTSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDO2dCQUVuRSxhQUFhLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQztvQkFDeEIsRUFBRSxFQUFFLFNBQVMsY0FBYyxFQUFFO29CQUM3QixLQUFLLEVBQUUsY0FBYztvQkFDckIsT0FBTztvQkFDUCxJQUFJO29CQUNKLFFBQVEsRUFBRTt3QkFDUixTQUFTLEVBQUUsSUFBSSxDQUFDLE1BQU07d0JBQ3RCLFVBQVUsRUFBRSxjQUFjO3FCQUMzQjtpQkFDRixDQUFDLENBQUM7WUFDTCxDQUFDO1FBQ0gsQ0FBQztRQUVELE9BQU8sYUFBYSxDQUFDO0lBQ3ZCLENBQUM7SUFFRDs7Ozs7T0FLRztJQUNLLEtBQUssQ0FBQyxzQkFBc0IsQ0FDbEMsTUFBYyxFQUNkLGFBQWtCO1FBRWxCLE1BQU0sY0FBYyxHQUFRO1lBQzFCLE1BQU0sRUFBRSxFQUFFO1NBQ1gsQ0FBQztRQUVGLElBQUksTUFBTSxDQUFDLGFBQWEsQ0FBQyxjQUFjLEVBQUUsQ0FBQztZQUN4QyxLQUFLLE1BQU0sU0FBUyxJQUFJLE1BQU0sQ0FBQyxhQUFhLENBQUMsY0FBYyxFQUFFLENBQUM7Z0JBQzVELElBQUksQ0FBQztvQkFDSCxNQUFNLFNBQVMsR0FBRyxNQUFNLElBQUksQ0FBQyxhQUFhLENBQUMsU0FBUyxFQUFFLGFBQWEsQ0FBQyxDQUFDO29CQUNyRSxjQUFjLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQzt3QkFDekIsRUFBRSxFQUFFLFNBQVMsSUFBSSxDQUFDLEdBQUcsRUFBRSxJQUFJLElBQUksQ0FBQyxNQUFNLEVBQUUsRUFBRTt3QkFDMUMsS0FBSyxFQUFFLFNBQVMsQ0FBQyxLQUFLO3dCQUN0QixJQUFJLEVBQUUsU0FBUyxDQUFDLFNBQVMsSUFBSSxLQUFLO3dCQUNsQyxJQUFJLEVBQUUsU0FBUzt3QkFDZixhQUFhLEVBQUUsU0FBUyxDQUFDLGFBQWE7cUJBQ3ZDLENBQUMsQ0FBQztnQkFDTCxDQUFDO2dCQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7b0JBQ2YsSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsa0NBQWtDLEVBQUU7d0JBQ25ELGFBQWEsRUFBRSxTQUFTLENBQUMsS0FBSzt3QkFDOUIsS0FBSyxFQUFFLEtBQUssQ0FBQyxPQUFPO3FCQUNyQixDQUFDLENBQUM7Z0JBQ0wsQ0FBQztZQUNILENBQUM7UUFDSCxDQUFDO1FBRUQsT0FBTyxjQUFjLENBQUM7SUFDeEIsQ0FBQztJQUVEOzs7Ozs7O09BT0c7SUFDSyxLQUFLLENBQUMsWUFBWSxDQUN4QixNQUFjLEVBQ2QsYUFBa0IsRUFDbEIsY0FBbUIsRUFDbkIsTUFBYztRQUVkLE9BQU8sTUFBTSxJQUFJLENBQUMsbUJBQW1CLENBQUMsWUFBWSxDQUNoRDtZQUNFLE1BQU07WUFDTixJQUFJLEVBQUUsYUFBYTtZQUNuQixjQUFjO1NBQ2YsRUFDRCxNQUFNLENBQ1AsQ0FBQztJQUNKLENBQUM7SUFFRDs7OztPQUlHO0lBQ0ssS0FBSyxDQUFDLFdBQVcsQ0FBQyxRQUFnQjtRQUN4QyxJQUFJLENBQUM7WUFDSCxNQUFNLEVBQUUsR0FBRyxPQUFPLENBQUMsSUFBSSxDQUFDLENBQUMsUUFBUSxDQUFDO1lBQ2xDLE1BQU0sS0FBSyxHQUFHLE1BQU0sRUFBRSxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQztZQUN0QyxPQUFPLEtBQUssQ0FBQyxJQUFJLENBQUM7UUFDcEIsQ0FBQztRQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7WUFDZixJQUFJLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyx5QkFBeUIsRUFBRTtnQkFDMUMsUUFBUTtnQkFDUixLQUFLLEVBQUUsS0FBSyxDQUFDLE9BQU87YUFDckIsQ0FBQyxDQUFDO1lBQ0gsT0FBTyxDQUFDLENBQUM7UUFDWCxDQUFDO0lBQ0gsQ0FBQztJQUVEOzs7OztPQUtHO0lBQ0ssdUJBQXVCLENBQUMsTUFBYyxFQUFFLFVBQWdCO1FBQzlELE1BQU0sU0FBUyxHQUFHLFVBQVUsRUFBRSxTQUFTLElBQUksTUFBTSxDQUFDLGFBQWEsQ0FBQyxTQUFTLENBQUM7UUFFMUUsSUFBSSxDQUFDLFNBQVMsRUFBRSxDQUFDO1lBQ2YsT0FBTyxVQUFVLENBQUM7UUFDcEIsQ0FBQztRQUVELElBQUksU0FBUyxDQUFDLElBQUksS0FBSyxVQUFVLEVBQUUsQ0FBQztZQUNsQyxPQUFPLFFBQVEsU0FBUyxDQUFDLEtBQUssRUFBRSxDQUFDO1FBQ25DLENBQUM7YUFBTSxDQUFDO1lBQ04sT0FBTyxHQUFHLFNBQVMsQ0FBQyxTQUFTLE9BQU8sU0FBUyxDQUFDLE9BQU8sRUFBRSxDQUFDO1FBQzFELENBQUM7SUFDSCxDQUFDO0lBRUQ7Ozs7O09BS0c7SUFDSyxTQUFTLENBQUMsSUFBVyxFQUFFLE9BQWlCO1FBQzlDLElBQUksQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxJQUFJLE9BQU8sQ0FBQyxNQUFNLEtBQUssQ0FBQyxFQUFFLENBQUM7WUFDakQsT0FBTyxJQUFJLENBQUM7UUFDZCxDQUFDO1FBRUQsTUFBTSxPQUFPLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDLEdBQUcsRUFBRSxJQUFJLEVBQUUsRUFBRTtZQUN4QyxNQUFNLEdBQUcsR0FBRyxPQUFPLENBQUMsR0FBRyxDQUFDLEtBQUssQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDO1lBQ3hELElBQUksQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQztnQkFDZCxHQUFHLENBQUMsR0FBRyxDQUFDLEdBQUcsRUFBRSxDQUFDO1lBQ2hCLENBQUM7WUFDRCxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQ3BCLE9BQU8sR0FBRyxDQUFDO1FBQ2IsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDO1FBRVAsT0FBTyxNQUFNLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsR0FBRyxFQUFFLEtBQUssQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDO1lBQ3BELEtBQUssRUFBRSxHQUFHO1lBQ1YsS0FBSyxFQUFHLEtBQWUsQ0FBQyxNQUFNO1lBQzlCLEtBQUs7U0FDTixDQUFDLENBQUMsQ0FBQztJQUNOLENBQUM7SUFFRDs7Ozs7T0FLRztJQUNLLGFBQWEsQ0FBQyxJQUFXLEVBQUUsWUFBbUI7UUFDcEQsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLElBQUksWUFBWSxDQUFDLE1BQU0sS0FBSyxDQUFDLEVBQUUsQ0FBQztZQUN0RCxPQUFPLEVBQUUsQ0FBQztRQUNaLENBQUM7UUFFRCxPQUFPLFlBQVksQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLEVBQUU7WUFDNUIsSUFBSSxLQUFVLENBQUM7WUFDZixNQUFNLEtBQUssR0FBRyxHQUFHLENBQUMsS0FBSyxDQUFDO1lBRXhCLFFBQVEsR0FBRyxDQUFDLFFBQVEsRUFBRSxDQUFDO2dCQUNyQixLQUFLLE9BQU87b0JBQ1YsS0FBSyxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUM7b0JBQ3BCLE1BQU07Z0JBQ1IsS0FBSyxLQUFLO29CQUNSLEtBQUssR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUMsR0FBRyxFQUFFLElBQUksRUFBRSxFQUFFLENBQUMsR0FBRyxHQUFHLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO29CQUN4RSxNQUFNO2dCQUNSLEtBQUssS0FBSztvQkFDUixNQUFNLEdBQUcsR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUMsR0FBRyxFQUFFLElBQUksRUFBRSxFQUFFLENBQUMsR0FBRyxHQUFHLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO29CQUM1RSxLQUFLLEdBQUcsSUFBSSxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7b0JBQ2hELE1BQU07Z0JBQ1IsS0FBSyxLQUFLO29CQUNSLEtBQUssR0FBRyxJQUFJLENBQUMsR0FBRyxDQUFDLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDO29CQUNoRSxNQUFNO2dCQUNSLEtBQUssS0FBSztvQkFDUixLQUFLLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxHQUFHLElBQUksQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztvQkFDaEUsTUFBTTtnQkFDUjtvQkFDRSxLQUFLLEdBQUcsQ0FBQyxDQUFDO1lBQ2QsQ0FBQztZQUVELE9BQU87Z0JBQ0wsRUFBRSxFQUFFLEdBQUcsQ0FBQyxLQUFLLElBQUksR0FBRyxHQUFHLENBQUMsUUFBUSxJQUFJLEtBQUssRUFBRTtnQkFDM0MsS0FBSyxFQUFFLEdBQUcsQ0FBQyxLQUFLLElBQUksR0FBRyxHQUFHLENBQUMsUUFBUSxDQUFDLFdBQVcsRUFBRSxPQUFPLEtBQUssRUFBRTtnQkFDL0QsS0FBSztnQkFDTCxJQUFJLEVBQUUsR0FBRyxDQUFDLElBQUk7YUFDZixDQUFDO1FBQ0osQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDO0lBRUQ7Ozs7O09BS0c7SUFDSyxLQUFLLENBQUMsYUFBYSxDQUFDLFNBQWMsRUFBRSxhQUFrQjtRQUM1RCx3Q0FBd0M7UUFDeEMsOERBQThEO1FBQzlELE1BQU0sVUFBVSxHQUFHLGFBQWEsQ0FBQyxNQUFNLEVBQUUsSUFBSSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxRQUFRLENBQUMsU0FBUyxDQUFDLFVBQVUsQ0FBQyxDQUFDLENBQUM7UUFFeEYsSUFBSSxDQUFDLFVBQVUsRUFBRSxDQUFDO1lBQ2hCLE9BQU8sRUFBRSxNQUFNLEVBQUUsRUFBRSxFQUFFLFFBQVEsRUFBRSxFQUFFLEVBQUUsQ0FBQztRQUN0QyxDQUFDO1FBRUQsTUFBTSxNQUFNLEdBQUcsVUFBVSxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQyxHQUFHLEVBQUUsS0FBSyxFQUFFLEVBQUUsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLElBQUksUUFBUSxLQUFLLEdBQUcsQ0FBQyxFQUFFLENBQUMsQ0FBQztRQUNsRixNQUFNLE1BQU0sR0FBRyxVQUFVLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQztRQUUvRCxPQUFPO1lBQ0wsTUFBTTtZQUNOLFFBQVEsRUFBRSxDQUFDO29CQUNULEtBQUssRUFBRSxTQUFTLENBQUMsS0FBSztvQkFDdEIsSUFBSSxFQUFFLE1BQU07b0JBQ1osZUFBZSxFQUFFLFNBQVMsQ0FBQyxhQUFhLEVBQUUsTUFBTSxJQUFJLENBQUMsU0FBUyxDQUFDO2lCQUNoRSxDQUFDO1NBQ0gsQ0FBQztJQUNKLENBQUM7SUFFRDs7OztPQUlHO0lBQ0gsS0FBSyxDQUFDLFlBQVksQ0FBQyxXQUFtQjtRQUNwQyxJQUFJLENBQUM7WUFDSCxPQUFPLE1BQU0sSUFBSSxDQUFDLG1CQUFtQixDQUFDLE9BQU8sQ0FBQztnQkFDNUMsS0FBSyxFQUFFLEVBQUUsRUFBRSxFQUFFLFdBQVcsRUFBRTtnQkFDMUIsU0FBUyxFQUFFLENBQUMsUUFBUSxDQUFDO2FBQ3RCLENBQUMsQ0FBQztRQUNMLENBQUM7UUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1lBQ2YsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsZ0NBQWdDLEVBQUU7Z0JBQ2xELFdBQVc7Z0JBQ1gsS0FBSyxFQUFFLEtBQUssQ0FBQyxPQUFPO2FBQ3JCLENBQUMsQ0FBQztZQUNILE1BQU0sS0FBSyxDQUFDO1FBQ2QsQ0FBQztJQUNILENBQUM7SUFFRDs7Ozs7T0FLRztJQUNILEtBQUssQ0FBQyxlQUFlLENBQUMsV0FBbUIsRUFBRSxNQUFjO1FBQ3ZELElBQUksQ0FBQztZQUNILE1BQU0sU0FBUyxHQUFHLE1BQU0sSUFBSSxDQUFDLFlBQVksQ0FBQyxXQUFXLENBQUMsQ0FBQztZQUN2RCxJQUFJLENBQUMsU0FBUyxFQUFFLENBQUM7Z0JBQ2YsTUFBTSxJQUFJLDBCQUFpQixDQUFDLDRCQUE0QixDQUFDLENBQUM7WUFDNUQsQ0FBQztZQUVELElBQUksQ0FBQyxTQUFTLENBQUMsU0FBUyxFQUFFLENBQUM7Z0JBQ3pCLE1BQU0sSUFBSSw0QkFBbUIsQ0FBQyxpQ0FBaUMsQ0FBQyxDQUFDO1lBQ25FLENBQUM7WUFFRCxTQUFTLENBQUMsTUFBTSxDQUFDLG1CQUFtQixDQUFDLENBQUM7WUFDdEMsTUFBTSxnQkFBZ0IsR0FBRyxNQUFNLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUM7WUFFeEUsTUFBTSxJQUFJLENBQUMsWUFBWSxDQUFDLGFBQWEsQ0FDbkMsTUFBTSxFQUNOLFFBQVEsRUFDUixrQkFBa0IsRUFDbEIsV0FBVyxFQUNYO2dCQUNFLFFBQVEsRUFBRSxTQUFTLENBQUMsUUFBUTtnQkFDNUIsTUFBTSxFQUFFLGNBQWM7YUFDdkIsQ0FDRixDQUFDO1lBRUYsSUFBSSxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsNEJBQTRCLEVBQUU7Z0JBQzVDLFdBQVc7Z0JBQ1gsTUFBTTthQUNQLENBQUMsQ0FBQztZQUVILE9BQU8sZ0JBQWdCLENBQUM7UUFDMUIsQ0FBQztRQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7WUFDZixJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxtQ0FBbUMsRUFBRTtnQkFDckQsV0FBVztnQkFDWCxLQUFLLEVBQUUsS0FBSyxDQUFDLE9BQU87Z0JBQ3BCLE1BQU07YUFDUCxDQUFDLENBQUM7WUFDSCxNQUFNLEtBQUssQ0FBQztRQUNkLENBQUM7SUFDSCxDQUFDO0NBQ0YsQ0FBQTtBQXJrQlksMERBQXVCO2tDQUF2Qix1QkFBdUI7SUFEbkMsSUFBQSxtQkFBVSxHQUFFO0lBS1IsV0FBQSxJQUFBLDBCQUFnQixFQUFDLHNCQUFNLENBQUMsQ0FBQTtJQUV4QixXQUFBLElBQUEsMEJBQWdCLEVBQUMseUNBQWUsQ0FBQyxDQUFBO3lEQURDLG9CQUFVLG9CQUFWLG9CQUFVLG9EQUVQLG9CQUFVLG9CQUFWLG9CQUFVLG9EQUNuQixvQkFBVSxvQkFBVixvQkFBVSxvREFDUCw4QkFBYSxvQkFBYiw4QkFBYSxvREFDZCw0QkFBWSxvQkFBWiw0QkFBWSxvREFDRixpREFBc0Isb0JBQXRCLGlEQUFzQixvREFDekIsMkNBQW1CLG9CQUFuQiwyQ0FBbUI7R0FaaEQsdUJBQXVCLENBcWtCbkMiLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxtb2R1bGVzXFxyZXBvcnRpbmctYW5hbHl0aWNzXFxhcHBsaWNhdGlvblxcc2VydmljZXNcXHJlcG9ydC1nZW5lcmF0aW9uLnNlcnZpY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSW5qZWN0YWJsZSwgTG9nZ2VyLCBOb3RGb3VuZEV4Y2VwdGlvbiwgQmFkUmVxdWVzdEV4Y2VwdGlvbiB9IGZyb20gJ0BuZXN0anMvY29tbW9uJztcclxuaW1wb3J0IHsgSW5qZWN0UmVwb3NpdG9yeSB9IGZyb20gJ0BuZXN0anMvdHlwZW9ybSc7XHJcbmltcG9ydCB7IFJlcG9zaXRvcnksIERhdGFTb3VyY2UgfSBmcm9tICd0eXBlb3JtJztcclxuaW1wb3J0IHsgUmVwb3J0IH0gZnJvbSAnLi4vLi4vZG9tYWluL2VudGl0aWVzL3JlcG9ydC5lbnRpdHknO1xyXG5pbXBvcnQgeyBSZXBvcnRFeGVjdXRpb24gfSBmcm9tICcuLi8uLi9kb21haW4vZW50aXRpZXMvcmVwb3J0LWV4ZWN1dGlvbi5lbnRpdHknO1xyXG5pbXBvcnQgeyBMb2dnZXJTZXJ2aWNlIH0gZnJvbSAnLi4vLi4vLi4vLi4vaW5mcmFzdHJ1Y3R1cmUvbG9nZ2luZy9sb2dnZXIuc2VydmljZSc7XHJcbmltcG9ydCB7IEF1ZGl0U2VydmljZSB9IGZyb20gJy4uLy4uLy4uLy4uL2luZnJhc3RydWN0dXJlL2xvZ2dpbmcvYXVkaXQvYXVkaXQuc2VydmljZSc7XHJcbmltcG9ydCB7IERhdGFBZ2dyZWdhdGlvblNlcnZpY2UgfSBmcm9tICcuL2RhdGEtYWdncmVnYXRpb24uc2VydmljZSc7XHJcbmltcG9ydCB7IFJlcG9ydEV4cG9ydFNlcnZpY2UgfSBmcm9tICcuL3JlcG9ydC1leHBvcnQuc2VydmljZSc7XHJcblxyXG4vKipcclxuICogUmVwb3J0IEdlbmVyYXRpb24gc2VydmljZVxyXG4gKiBIYW5kbGVzIHJlcG9ydCBleGVjdXRpb24sIGRhdGEgcHJvY2Vzc2luZywgYW5kIG91dHB1dCBnZW5lcmF0aW9uXHJcbiAqL1xyXG5ASW5qZWN0YWJsZSgpXHJcbmV4cG9ydCBjbGFzcyBSZXBvcnRHZW5lcmF0aW9uU2VydmljZSB7XHJcbiAgcHJpdmF0ZSByZWFkb25seSBsb2dnZXIgPSBuZXcgTG9nZ2VyKFJlcG9ydEdlbmVyYXRpb25TZXJ2aWNlLm5hbWUpO1xyXG5cclxuICBjb25zdHJ1Y3RvcihcclxuICAgIEBJbmplY3RSZXBvc2l0b3J5KFJlcG9ydClcclxuICAgIHByaXZhdGUgcmVhZG9ubHkgcmVwb3J0UmVwb3NpdG9yeTogUmVwb3NpdG9yeTxSZXBvcnQ+LFxyXG4gICAgQEluamVjdFJlcG9zaXRvcnkoUmVwb3J0RXhlY3V0aW9uKVxyXG4gICAgcHJpdmF0ZSByZWFkb25seSBleGVjdXRpb25SZXBvc2l0b3J5OiBSZXBvc2l0b3J5PFJlcG9ydEV4ZWN1dGlvbj4sXHJcbiAgICBwcml2YXRlIHJlYWRvbmx5IGRhdGFTb3VyY2U6IERhdGFTb3VyY2UsXHJcbiAgICBwcml2YXRlIHJlYWRvbmx5IGxvZ2dlclNlcnZpY2U6IExvZ2dlclNlcnZpY2UsXHJcbiAgICBwcml2YXRlIHJlYWRvbmx5IGF1ZGl0U2VydmljZTogQXVkaXRTZXJ2aWNlLFxyXG4gICAgcHJpdmF0ZSByZWFkb25seSBkYXRhQWdncmVnYXRpb25TZXJ2aWNlOiBEYXRhQWdncmVnYXRpb25TZXJ2aWNlLFxyXG4gICAgcHJpdmF0ZSByZWFkb25seSByZXBvcnRFeHBvcnRTZXJ2aWNlOiBSZXBvcnRFeHBvcnRTZXJ2aWNlLFxyXG4gICkge31cclxuXHJcbiAgLyoqXHJcbiAgICogRXhlY3V0ZSBhIHJlcG9ydCBhbmQgZ2VuZXJhdGUgb3V0cHV0XHJcbiAgICogQHBhcmFtIHJlcG9ydElkIFJlcG9ydCBJRFxyXG4gICAqIEBwYXJhbSBwYXJhbWV0ZXJzIEV4ZWN1dGlvbiBwYXJhbWV0ZXJzXHJcbiAgICogQHBhcmFtIHVzZXJJZCBVc2VyIGV4ZWN1dGluZyB0aGUgcmVwb3J0XHJcbiAgICogQHBhcmFtIHRyaWdnZXJUeXBlIEhvdyB0aGUgZXhlY3V0aW9uIHdhcyB0cmlnZ2VyZWRcclxuICAgKiBAcmV0dXJucyBSZXBvcnQgZXhlY3V0aW9uXHJcbiAgICovXHJcbiAgYXN5bmMgZXhlY3V0ZVJlcG9ydChcclxuICAgIHJlcG9ydElkOiBzdHJpbmcsXHJcbiAgICBwYXJhbWV0ZXJzOiB7XHJcbiAgICAgIHRpbWVSYW5nZT86IHtcclxuICAgICAgICB0eXBlOiAncmVsYXRpdmUnIHwgJ2Fic29sdXRlJztcclxuICAgICAgICB2YWx1ZT86IHN0cmluZztcclxuICAgICAgICBzdGFydERhdGU/OiBzdHJpbmc7XHJcbiAgICAgICAgZW5kRGF0ZT86IHN0cmluZztcclxuICAgICAgfTtcclxuICAgICAgZmlsdGVycz86IFJlY29yZDxzdHJpbmcsIGFueT47XHJcbiAgICAgIGV4cG9ydEZvcm1hdD86IHN0cmluZztcclxuICAgICAgY3VzdG9tUGFyYW1ldGVycz86IFJlY29yZDxzdHJpbmcsIGFueT47XHJcbiAgICB9ID0ge30sXHJcbiAgICB1c2VySWQ6IHN0cmluZyxcclxuICAgIHRyaWdnZXJUeXBlOiAnbWFudWFsJyB8ICdzY2hlZHVsZWQnIHwgJ2FwaScgfCAnd2ViaG9vaycgPSAnbWFudWFsJyxcclxuICApOiBQcm9taXNlPFJlcG9ydEV4ZWN1dGlvbj4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgdGhpcy5sb2dnZXIuZGVidWcoJ1N0YXJ0aW5nIHJlcG9ydCBleGVjdXRpb24nLCB7XHJcbiAgICAgICAgcmVwb3J0SWQsXHJcbiAgICAgICAgcGFyYW1ldGVycyxcclxuICAgICAgICB1c2VySWQsXHJcbiAgICAgICAgdHJpZ2dlclR5cGUsXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgLy8gR2V0IHJlcG9ydCBjb25maWd1cmF0aW9uXHJcbiAgICAgIGNvbnN0IHJlcG9ydCA9IGF3YWl0IHRoaXMucmVwb3J0UmVwb3NpdG9yeS5maW5kT25lKHtcclxuICAgICAgICB3aGVyZTogeyBpZDogcmVwb3J0SWQgfSxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpZiAoIXJlcG9ydCkge1xyXG4gICAgICAgIHRocm93IG5ldyBOb3RGb3VuZEV4Y2VwdGlvbignUmVwb3J0IG5vdCBmb3VuZCcpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBpZiAocmVwb3J0LnN0YXR1cyAhPT0gJ2FjdGl2ZScpIHtcclxuICAgICAgICB0aHJvdyBuZXcgQmFkUmVxdWVzdEV4Y2VwdGlvbignUmVwb3J0IGlzIG5vdCBhY3RpdmUnKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gVmFsaWRhdGUgcmVwb3J0IGNvbmZpZ3VyYXRpb25cclxuICAgICAgY29uc3QgdmFsaWRhdGlvbiA9IHJlcG9ydC52YWxpZGF0ZUNvbmZpZ3VyYXRpb24oKTtcclxuICAgICAgaWYgKCF2YWxpZGF0aW9uLmlzVmFsaWQpIHtcclxuICAgICAgICB0aHJvdyBuZXcgQmFkUmVxdWVzdEV4Y2VwdGlvbihgUmVwb3J0IGNvbmZpZ3VyYXRpb24gaXMgaW52YWxpZDogJHt2YWxpZGF0aW9uLmVycm9ycy5qb2luKCcsICcpfWApO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBDcmVhdGUgZXhlY3V0aW9uIHJlY29yZFxyXG4gICAgICBjb25zdCBleGVjdXRpb24gPSB0aGlzLmV4ZWN1dGlvblJlcG9zaXRvcnkuY3JlYXRlKHtcclxuICAgICAgICByZXBvcnRJZCxcclxuICAgICAgICBzdGF0dXM6ICdwZW5kaW5nJyxcclxuICAgICAgICB0cmlnZ2VyVHlwZSxcclxuICAgICAgICBwYXJhbWV0ZXJzLFxyXG4gICAgICAgIGV4ZWN1dGVkQnk6IHVzZXJJZCxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBjb25zdCBzYXZlZEV4ZWN1dGlvbiA9IGF3YWl0IHRoaXMuZXhlY3V0aW9uUmVwb3NpdG9yeS5zYXZlKGV4ZWN1dGlvbik7XHJcblxyXG4gICAgICAvLyBTdGFydCBleGVjdXRpb24gYXN5bmNocm9ub3VzbHlcclxuICAgICAgdGhpcy5wcm9jZXNzUmVwb3J0RXhlY3V0aW9uKHNhdmVkRXhlY3V0aW9uLCByZXBvcnQsIHVzZXJJZCk7XHJcblxyXG4gICAgICBhd2FpdCB0aGlzLmF1ZGl0U2VydmljZS5sb2dVc2VyQWN0aW9uKFxyXG4gICAgICAgIHVzZXJJZCxcclxuICAgICAgICAnZXhlY3V0ZScsXHJcbiAgICAgICAgJ3JlcG9ydCcsXHJcbiAgICAgICAgcmVwb3J0SWQsXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgZXhlY3V0aW9uSWQ6IHNhdmVkRXhlY3V0aW9uLmlkLFxyXG4gICAgICAgICAgdHJpZ2dlclR5cGUsXHJcbiAgICAgICAgICBwYXJhbWV0ZXJzLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICk7XHJcblxyXG4gICAgICB0aGlzLmxvZ2dlci5sb2coJ1JlcG9ydCBleGVjdXRpb24gc3RhcnRlZCcsIHtcclxuICAgICAgICByZXBvcnRJZCxcclxuICAgICAgICBleGVjdXRpb25JZDogc2F2ZWRFeGVjdXRpb24uaWQsXHJcbiAgICAgICAgdXNlcklkLFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIHJldHVybiBzYXZlZEV4ZWN1dGlvbjtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIHRoaXMubG9nZ2VyLmVycm9yKCdGYWlsZWQgdG8gc3RhcnQgcmVwb3J0IGV4ZWN1dGlvbicsIHtcclxuICAgICAgICByZXBvcnRJZCxcclxuICAgICAgICBlcnJvcjogZXJyb3IubWVzc2FnZSxcclxuICAgICAgICB1c2VySWQsXHJcbiAgICAgIH0pO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIFByb2Nlc3MgcmVwb3J0IGV4ZWN1dGlvblxyXG4gICAqIEBwYXJhbSBleGVjdXRpb24gUmVwb3J0IGV4ZWN1dGlvblxyXG4gICAqIEBwYXJhbSByZXBvcnQgUmVwb3J0IGNvbmZpZ3VyYXRpb25cclxuICAgKiBAcGFyYW0gdXNlcklkIFVzZXIgSURcclxuICAgKi9cclxuICBwcml2YXRlIGFzeW5jIHByb2Nlc3NSZXBvcnRFeGVjdXRpb24oXHJcbiAgICBleGVjdXRpb246IFJlcG9ydEV4ZWN1dGlvbixcclxuICAgIHJlcG9ydDogUmVwb3J0LFxyXG4gICAgdXNlcklkOiBzdHJpbmcsXHJcbiAgKTogUHJvbWlzZTx2b2lkPiB7XHJcbiAgICB0cnkge1xyXG4gICAgICAvLyBTdGFydCBleGVjdXRpb25cclxuICAgICAgZXhlY3V0aW9uLnN0YXJ0KCk7XHJcbiAgICAgIGF3YWl0IHRoaXMuZXhlY3V0aW9uUmVwb3NpdG9yeS5zYXZlKGV4ZWN1dGlvbik7XHJcblxyXG4gICAgICB0aGlzLmxvZ2dlci5kZWJ1ZygnUHJvY2Vzc2luZyByZXBvcnQgZXhlY3V0aW9uJywge1xyXG4gICAgICAgIHJlcG9ydElkOiByZXBvcnQuaWQsXHJcbiAgICAgICAgZXhlY3V0aW9uSWQ6IGV4ZWN1dGlvbi5pZCxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICAvLyBTdGVwIDE6IERhdGEgY29sbGVjdGlvblxyXG4gICAgICBleGVjdXRpb24udXBkYXRlUHJvZ3Jlc3MoMTAsICdDb2xsZWN0aW5nIGRhdGEnKTtcclxuICAgICAgYXdhaXQgdGhpcy5leGVjdXRpb25SZXBvc2l0b3J5LnNhdmUoZXhlY3V0aW9uKTtcclxuXHJcbiAgICAgIGNvbnN0IHJlcG9ydERhdGEgPSBhd2FpdCB0aGlzLmNvbGxlY3RSZXBvcnREYXRhKHJlcG9ydCwgZXhlY3V0aW9uLnBhcmFtZXRlcnMpO1xyXG5cclxuICAgICAgLy8gU3RlcCAyOiBEYXRhIHByb2Nlc3NpbmdcclxuICAgICAgZXhlY3V0aW9uLnVwZGF0ZVByb2dyZXNzKDQwLCAnUHJvY2Vzc2luZyBkYXRhJyk7XHJcbiAgICAgIGF3YWl0IHRoaXMuZXhlY3V0aW9uUmVwb3NpdG9yeS5zYXZlKGV4ZWN1dGlvbik7XHJcblxyXG4gICAgICBjb25zdCBwcm9jZXNzZWREYXRhID0gYXdhaXQgdGhpcy5wcm9jZXNzUmVwb3J0RGF0YShyZXBvcnQsIHJlcG9ydERhdGEpO1xyXG5cclxuICAgICAgLy8gU3RlcCAzOiBHZW5lcmF0ZSB2aXN1YWxpemF0aW9uc1xyXG4gICAgICBleGVjdXRpb24udXBkYXRlUHJvZ3Jlc3MoNzAsICdHZW5lcmF0aW5nIHZpc3VhbGl6YXRpb25zJyk7XHJcbiAgICAgIGF3YWl0IHRoaXMuZXhlY3V0aW9uUmVwb3NpdG9yeS5zYXZlKGV4ZWN1dGlvbik7XHJcblxyXG4gICAgICBjb25zdCB2aXN1YWxpemF0aW9ucyA9IGF3YWl0IHRoaXMuZ2VuZXJhdGVWaXN1YWxpemF0aW9ucyhyZXBvcnQsIHByb2Nlc3NlZERhdGEpO1xyXG5cclxuICAgICAgLy8gU3RlcCA0OiBFeHBvcnQgcmVwb3J0XHJcbiAgICAgIGV4ZWN1dGlvbi51cGRhdGVQcm9ncmVzcyg5MCwgJ0V4cG9ydGluZyByZXBvcnQnKTtcclxuICAgICAgYXdhaXQgdGhpcy5leGVjdXRpb25SZXBvc2l0b3J5LnNhdmUoZXhlY3V0aW9uKTtcclxuXHJcbiAgICAgIGNvbnN0IGV4cG9ydEZvcm1hdCA9IGV4ZWN1dGlvbi5wYXJhbWV0ZXJzPy5leHBvcnRGb3JtYXQgfHwgJ3BkZic7XHJcbiAgICAgIGNvbnN0IG91dHB1dFBhdGggPSBhd2FpdCB0aGlzLmV4cG9ydFJlcG9ydChyZXBvcnQsIHByb2Nlc3NlZERhdGEsIHZpc3VhbGl6YXRpb25zLCBleHBvcnRGb3JtYXQpO1xyXG5cclxuICAgICAgLy8gQ29tcGxldGUgZXhlY3V0aW9uXHJcbiAgICAgIGNvbnN0IGZpbmFsUmVwb3J0RGF0YSA9IHtcclxuICAgICAgICBzdW1tYXJ5OiB7XHJcbiAgICAgICAgICB0b3RhbFJlY29yZHM6IHByb2Nlc3NlZERhdGEudG90YWxSZWNvcmRzIHx8IDAsXHJcbiAgICAgICAgICBkYXRhUG9pbnRzOiBwcm9jZXNzZWREYXRhLmRhdGFQb2ludHMgfHwgMCxcclxuICAgICAgICAgIGdlbmVyYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgICAgICB0aW1lUmFuZ2U6IHRoaXMuZ2V0VGltZVJhbmdlRGVzY3JpcHRpb24ocmVwb3J0LCBleGVjdXRpb24ucGFyYW1ldGVycyksXHJcbiAgICAgICAgfSxcclxuICAgICAgICBzZWN0aW9uczogcHJvY2Vzc2VkRGF0YS5zZWN0aW9ucyB8fCBbXSxcclxuICAgICAgICBjaGFydHM6IHZpc3VhbGl6YXRpb25zLmNoYXJ0cyB8fCBbXSxcclxuICAgICAgICB0YWJsZXM6IHByb2Nlc3NlZERhdGEudGFibGVzIHx8IFtdLFxyXG4gICAgICAgIG1ldHJpY3M6IHByb2Nlc3NlZERhdGEubWV0cmljcyB8fCBbXSxcclxuICAgICAgfTtcclxuXHJcbiAgICAgIGV4ZWN1dGlvbi5jb21wbGV0ZShvdXRwdXRQYXRoLCBleHBvcnRGb3JtYXQsIGZpbmFsUmVwb3J0RGF0YSk7XHJcbiAgICAgIGV4ZWN1dGlvbi5yZWNvcmRzUHJvY2Vzc2VkID0gcHJvY2Vzc2VkRGF0YS50b3RhbFJlY29yZHMgfHwgMDtcclxuICAgICAgZXhlY3V0aW9uLm91dHB1dFNpemVCeXRlcyA9IGF3YWl0IHRoaXMuZ2V0RmlsZVNpemUob3V0cHV0UGF0aCk7XHJcblxyXG4gICAgICBhd2FpdCB0aGlzLmV4ZWN1dGlvblJlcG9zaXRvcnkuc2F2ZShleGVjdXRpb24pO1xyXG5cclxuICAgICAgLy8gVXBkYXRlIHJlcG9ydCBzdGF0aXN0aWNzXHJcbiAgICAgIHJlcG9ydC51cGRhdGVFeGVjdXRpb25TdGF0cyhcclxuICAgICAgICBleGVjdXRpb24uZHVyYXRpb25NcyB8fCAwLFxyXG4gICAgICAgIE51bWJlcihleGVjdXRpb24ub3V0cHV0U2l6ZUJ5dGVzIHx8IDApLFxyXG4gICAgICAgICdzdWNjZXNzJyxcclxuICAgICAgKTtcclxuICAgICAgYXdhaXQgdGhpcy5yZXBvcnRSZXBvc2l0b3J5LnNhdmUocmVwb3J0KTtcclxuXHJcbiAgICAgIHRoaXMubG9nZ2VyLmxvZygnUmVwb3J0IGV4ZWN1dGlvbiBjb21wbGV0ZWQgc3VjY2Vzc2Z1bGx5Jywge1xyXG4gICAgICAgIHJlcG9ydElkOiByZXBvcnQuaWQsXHJcbiAgICAgICAgZXhlY3V0aW9uSWQ6IGV4ZWN1dGlvbi5pZCxcclxuICAgICAgICBkdXJhdGlvbjogZXhlY3V0aW9uLmR1cmF0aW9uRm9ybWF0dGVkLFxyXG4gICAgICAgIG91dHB1dFNpemU6IGV4ZWN1dGlvbi5vdXRwdXRTaXplRm9ybWF0dGVkLFxyXG4gICAgICB9KTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIHRoaXMubG9nZ2VyLmVycm9yKCdSZXBvcnQgZXhlY3V0aW9uIGZhaWxlZCcsIHtcclxuICAgICAgICByZXBvcnRJZDogcmVwb3J0LmlkLFxyXG4gICAgICAgIGV4ZWN1dGlvbklkOiBleGVjdXRpb24uaWQsXHJcbiAgICAgICAgZXJyb3I6IGVycm9yLm1lc3NhZ2UsXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgZXhlY3V0aW9uLmZhaWwoZXJyb3IpO1xyXG4gICAgICBhd2FpdCB0aGlzLmV4ZWN1dGlvblJlcG9zaXRvcnkuc2F2ZShleGVjdXRpb24pO1xyXG5cclxuICAgICAgLy8gVXBkYXRlIHJlcG9ydCBzdGF0aXN0aWNzXHJcbiAgICAgIHJlcG9ydC51cGRhdGVFeGVjdXRpb25TdGF0cyhcclxuICAgICAgICBleGVjdXRpb24uZHVyYXRpb25NcyB8fCAwLFxyXG4gICAgICAgIDAsXHJcbiAgICAgICAgJ2ZhaWxlZCcsXHJcbiAgICAgICk7XHJcbiAgICAgIGF3YWl0IHRoaXMucmVwb3J0UmVwb3NpdG9yeS5zYXZlKHJlcG9ydCk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDb2xsZWN0IGRhdGEgZm9yIHJlcG9ydFxyXG4gICAqIEBwYXJhbSByZXBvcnQgUmVwb3J0IGNvbmZpZ3VyYXRpb25cclxuICAgKiBAcGFyYW0gcGFyYW1ldGVycyBFeGVjdXRpb24gcGFyYW1ldGVyc1xyXG4gICAqIEByZXR1cm5zIFJhdyBkYXRhXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBhc3luYyBjb2xsZWN0UmVwb3J0RGF0YShcclxuICAgIHJlcG9ydDogUmVwb3J0LFxyXG4gICAgcGFyYW1ldGVycz86IGFueSxcclxuICApOiBQcm9taXNlPGFueT4ge1xyXG4gICAgY29uc3QgZGF0YVNvdXJjZXMgPSByZXBvcnQuZ2V0RGF0YVNvdXJjZXMoKTtcclxuICAgIGNvbnN0IGNvbGxlY3RlZERhdGE6IFJlY29yZDxzdHJpbmcsIGFueT4gPSB7fTtcclxuXHJcbiAgICBmb3IgKGNvbnN0IGRhdGFTb3VyY2VOYW1lIG9mIGRhdGFTb3VyY2VzKSB7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHRoaXMuZGF0YUFnZ3JlZ2F0aW9uU2VydmljZS5hZ2dyZWdhdGVEYXRhKFxyXG4gICAgICAgICAgZGF0YVNvdXJjZU5hbWUsXHJcbiAgICAgICAgICB7XHJcbiAgICAgICAgICAgIC4uLnJlcG9ydC5jb25maWd1cmF0aW9uLFxyXG4gICAgICAgICAgICAuLi5wYXJhbWV0ZXJzLFxyXG4gICAgICAgICAgfSxcclxuICAgICAgICApO1xyXG4gICAgICAgIGNvbGxlY3RlZERhdGFbZGF0YVNvdXJjZU5hbWVdID0gZGF0YTtcclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICB0aGlzLmxvZ2dlci53YXJuKCdGYWlsZWQgdG8gY29sbGVjdCBkYXRhIGZyb20gc291cmNlJywge1xyXG4gICAgICAgICAgZGF0YVNvdXJjZTogZGF0YVNvdXJjZU5hbWUsXHJcbiAgICAgICAgICBlcnJvcjogZXJyb3IubWVzc2FnZSxcclxuICAgICAgICB9KTtcclxuICAgICAgICBjb2xsZWN0ZWREYXRhW2RhdGFTb3VyY2VOYW1lXSA9IHsgZXJyb3I6IGVycm9yLm1lc3NhZ2UsIGRhdGE6IFtdIH07XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gY29sbGVjdGVkRGF0YTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIFByb2Nlc3MgY29sbGVjdGVkIGRhdGFcclxuICAgKiBAcGFyYW0gcmVwb3J0IFJlcG9ydCBjb25maWd1cmF0aW9uXHJcbiAgICogQHBhcmFtIHJhd0RhdGEgUmF3IGNvbGxlY3RlZCBkYXRhXHJcbiAgICogQHJldHVybnMgUHJvY2Vzc2VkIGRhdGFcclxuICAgKi9cclxuICBwcml2YXRlIGFzeW5jIHByb2Nlc3NSZXBvcnREYXRhKFxyXG4gICAgcmVwb3J0OiBSZXBvcnQsXHJcbiAgICByYXdEYXRhOiBSZWNvcmQ8c3RyaW5nLCBhbnk+LFxyXG4gICk6IFByb21pc2U8YW55PiB7XHJcbiAgICBjb25zdCBwcm9jZXNzZWREYXRhOiBhbnkgPSB7XHJcbiAgICAgIHRvdGFsUmVjb3JkczogMCxcclxuICAgICAgZGF0YVBvaW50czogMCxcclxuICAgICAgc2VjdGlvbnM6IFtdLFxyXG4gICAgICB0YWJsZXM6IFtdLFxyXG4gICAgICBtZXRyaWNzOiBbXSxcclxuICAgIH07XHJcblxyXG4gICAgLy8gUHJvY2VzcyBlYWNoIGRhdGEgc291cmNlXHJcbiAgICBmb3IgKGNvbnN0IFtkYXRhU291cmNlTmFtZSwgc291cmNlRGF0YV0gb2YgT2JqZWN0LmVudHJpZXMocmF3RGF0YSkpIHtcclxuICAgICAgaWYgKHNvdXJjZURhdGEuZXJyb3IpIHtcclxuICAgICAgICBwcm9jZXNzZWREYXRhLnNlY3Rpb25zLnB1c2goe1xyXG4gICAgICAgICAgaWQ6IGBlcnJvci0ke2RhdGFTb3VyY2VOYW1lfWAsXHJcbiAgICAgICAgICB0aXRsZTogYEVycm9yIGluICR7ZGF0YVNvdXJjZU5hbWV9YCxcclxuICAgICAgICAgIHR5cGU6ICd0ZXh0JyxcclxuICAgICAgICAgIGRhdGE6IHsgY29udGVudDogYEZhaWxlZCB0byBsb2FkIGRhdGE6ICR7c291cmNlRGF0YS5lcnJvcn1gIH0sXHJcbiAgICAgICAgfSk7XHJcbiAgICAgICAgY29udGludWU7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IGRhdGEgPSBzb3VyY2VEYXRhLmRhdGEgfHwgc291cmNlRGF0YTtcclxuICAgICAgcHJvY2Vzc2VkRGF0YS50b3RhbFJlY29yZHMgKz0gQXJyYXkuaXNBcnJheShkYXRhKSA/IGRhdGEubGVuZ3RoIDogMTtcclxuXHJcbiAgICAgIC8vIEFwcGx5IGdyb3VwaW5nIGFuZCBhZ2dyZWdhdGlvbnNcclxuICAgICAgaWYgKHJlcG9ydC5jb25maWd1cmF0aW9uLmdyb3VwQnkpIHtcclxuICAgICAgICBjb25zdCBncm91cGVkRGF0YSA9IHRoaXMuZ3JvdXBEYXRhKGRhdGEsIHJlcG9ydC5jb25maWd1cmF0aW9uLmdyb3VwQnkpO1xyXG4gICAgICAgIHByb2Nlc3NlZERhdGEuc2VjdGlvbnMucHVzaCh7XHJcbiAgICAgICAgICBpZDogYGdyb3VwZWQtJHtkYXRhU291cmNlTmFtZX1gLFxyXG4gICAgICAgICAgdGl0bGU6IGAke2RhdGFTb3VyY2VOYW1lfSAoR3JvdXBlZClgLFxyXG4gICAgICAgICAgdHlwZTogJ3RhYmxlJyxcclxuICAgICAgICAgIGRhdGE6IGdyb3VwZWREYXRhLFxyXG4gICAgICAgIH0pO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBBcHBseSBhZ2dyZWdhdGlvbnNcclxuICAgICAgaWYgKHJlcG9ydC5jb25maWd1cmF0aW9uLmFnZ3JlZ2F0aW9ucykge1xyXG4gICAgICAgIGNvbnN0IGFnZ3JlZ2F0ZWREYXRhID0gdGhpcy5hZ2dyZWdhdGVEYXRhKGRhdGEsIHJlcG9ydC5jb25maWd1cmF0aW9uLmFnZ3JlZ2F0aW9ucyk7XHJcbiAgICAgICAgcHJvY2Vzc2VkRGF0YS5tZXRyaWNzLnB1c2goLi4uYWdncmVnYXRlZERhdGEpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBDcmVhdGUgdGFibGVzXHJcbiAgICAgIGlmIChBcnJheS5pc0FycmF5KGRhdGEpICYmIGRhdGEubGVuZ3RoID4gMCkge1xyXG4gICAgICAgIGNvbnN0IGhlYWRlcnMgPSBPYmplY3Qua2V5cyhkYXRhWzBdKTtcclxuICAgICAgICBjb25zdCByb3dzID0gZGF0YS5tYXAoaXRlbSA9PiBoZWFkZXJzLm1hcChoZWFkZXIgPT4gaXRlbVtoZWFkZXJdKSk7XHJcbiAgICAgICAgXHJcbiAgICAgICAgcHJvY2Vzc2VkRGF0YS50YWJsZXMucHVzaCh7XHJcbiAgICAgICAgICBpZDogYHRhYmxlLSR7ZGF0YVNvdXJjZU5hbWV9YCxcclxuICAgICAgICAgIHRpdGxlOiBkYXRhU291cmNlTmFtZSxcclxuICAgICAgICAgIGhlYWRlcnMsXHJcbiAgICAgICAgICByb3dzLFxyXG4gICAgICAgICAgbWV0YWRhdGE6IHtcclxuICAgICAgICAgICAgdG90YWxSb3dzOiBkYXRhLmxlbmd0aCxcclxuICAgICAgICAgICAgZGF0YVNvdXJjZTogZGF0YVNvdXJjZU5hbWUsXHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgIH0pO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIHByb2Nlc3NlZERhdGE7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZW5lcmF0ZSB2aXN1YWxpemF0aW9ucyBmb3IgcmVwb3J0XHJcbiAgICogQHBhcmFtIHJlcG9ydCBSZXBvcnQgY29uZmlndXJhdGlvblxyXG4gICAqIEBwYXJhbSBwcm9jZXNzZWREYXRhIFByb2Nlc3NlZCBkYXRhXHJcbiAgICogQHJldHVybnMgVmlzdWFsaXphdGlvbiBkYXRhXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBhc3luYyBnZW5lcmF0ZVZpc3VhbGl6YXRpb25zKFxyXG4gICAgcmVwb3J0OiBSZXBvcnQsXHJcbiAgICBwcm9jZXNzZWREYXRhOiBhbnksXHJcbiAgKTogUHJvbWlzZTxhbnk+IHtcclxuICAgIGNvbnN0IHZpc3VhbGl6YXRpb25zOiBhbnkgPSB7XHJcbiAgICAgIGNoYXJ0czogW10sXHJcbiAgICB9O1xyXG5cclxuICAgIGlmIChyZXBvcnQuY29uZmlndXJhdGlvbi52aXN1YWxpemF0aW9ucykge1xyXG4gICAgICBmb3IgKGNvbnN0IHZpekNvbmZpZyBvZiByZXBvcnQuY29uZmlndXJhdGlvbi52aXN1YWxpemF0aW9ucykge1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICBjb25zdCBjaGFydERhdGEgPSBhd2FpdCB0aGlzLmdlbmVyYXRlQ2hhcnQodml6Q29uZmlnLCBwcm9jZXNzZWREYXRhKTtcclxuICAgICAgICAgIHZpc3VhbGl6YXRpb25zLmNoYXJ0cy5wdXNoKHtcclxuICAgICAgICAgICAgaWQ6IGBjaGFydC0ke0RhdGUubm93KCl9LSR7TWF0aC5yYW5kb20oKX1gLFxyXG4gICAgICAgICAgICB0aXRsZTogdml6Q29uZmlnLnRpdGxlLFxyXG4gICAgICAgICAgICB0eXBlOiB2aXpDb25maWcuY2hhcnRUeXBlIHx8ICdiYXInLFxyXG4gICAgICAgICAgICBkYXRhOiBjaGFydERhdGEsXHJcbiAgICAgICAgICAgIGNvbmZpZ3VyYXRpb246IHZpekNvbmZpZy5jb25maWd1cmF0aW9uLFxyXG4gICAgICAgICAgfSk7XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgIHRoaXMubG9nZ2VyLndhcm4oJ0ZhaWxlZCB0byBnZW5lcmF0ZSB2aXN1YWxpemF0aW9uJywge1xyXG4gICAgICAgICAgICB2aXN1YWxpemF0aW9uOiB2aXpDb25maWcudGl0bGUsXHJcbiAgICAgICAgICAgIGVycm9yOiBlcnJvci5tZXNzYWdlLFxyXG4gICAgICAgICAgfSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIHZpc3VhbGl6YXRpb25zO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogRXhwb3J0IHJlcG9ydCB0byBzcGVjaWZpZWQgZm9ybWF0XHJcbiAgICogQHBhcmFtIHJlcG9ydCBSZXBvcnQgY29uZmlndXJhdGlvblxyXG4gICAqIEBwYXJhbSBwcm9jZXNzZWREYXRhIFByb2Nlc3NlZCBkYXRhXHJcbiAgICogQHBhcmFtIHZpc3VhbGl6YXRpb25zIFZpc3VhbGl6YXRpb24gZGF0YVxyXG4gICAqIEBwYXJhbSBmb3JtYXQgRXhwb3J0IGZvcm1hdFxyXG4gICAqIEByZXR1cm5zIE91dHB1dCBmaWxlIHBhdGhcclxuICAgKi9cclxuICBwcml2YXRlIGFzeW5jIGV4cG9ydFJlcG9ydChcclxuICAgIHJlcG9ydDogUmVwb3J0LFxyXG4gICAgcHJvY2Vzc2VkRGF0YTogYW55LFxyXG4gICAgdmlzdWFsaXphdGlvbnM6IGFueSxcclxuICAgIGZvcm1hdDogc3RyaW5nLFxyXG4gICk6IFByb21pc2U8c3RyaW5nPiB7XHJcbiAgICByZXR1cm4gYXdhaXQgdGhpcy5yZXBvcnRFeHBvcnRTZXJ2aWNlLmV4cG9ydFJlcG9ydChcclxuICAgICAge1xyXG4gICAgICAgIHJlcG9ydCxcclxuICAgICAgICBkYXRhOiBwcm9jZXNzZWREYXRhLFxyXG4gICAgICAgIHZpc3VhbGl6YXRpb25zLFxyXG4gICAgICB9LFxyXG4gICAgICBmb3JtYXQsXHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IGZpbGUgc2l6ZSBpbiBieXRlc1xyXG4gICAqIEBwYXJhbSBmaWxlUGF0aCBGaWxlIHBhdGhcclxuICAgKiBAcmV0dXJucyBGaWxlIHNpemUgaW4gYnl0ZXNcclxuICAgKi9cclxuICBwcml2YXRlIGFzeW5jIGdldEZpbGVTaXplKGZpbGVQYXRoOiBzdHJpbmcpOiBQcm9taXNlPG51bWJlcj4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgZnMgPSByZXF1aXJlKCdmcycpLnByb21pc2VzO1xyXG4gICAgICBjb25zdCBzdGF0cyA9IGF3YWl0IGZzLnN0YXQoZmlsZVBhdGgpO1xyXG4gICAgICByZXR1cm4gc3RhdHMuc2l6ZTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIHRoaXMubG9nZ2VyLndhcm4oJ0ZhaWxlZCB0byBnZXQgZmlsZSBzaXplJywge1xyXG4gICAgICAgIGZpbGVQYXRoLFxyXG4gICAgICAgIGVycm9yOiBlcnJvci5tZXNzYWdlLFxyXG4gICAgICB9KTtcclxuICAgICAgcmV0dXJuIDA7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgdGltZSByYW5nZSBkZXNjcmlwdGlvblxyXG4gICAqIEBwYXJhbSByZXBvcnQgUmVwb3J0IGNvbmZpZ3VyYXRpb25cclxuICAgKiBAcGFyYW0gcGFyYW1ldGVycyBFeGVjdXRpb24gcGFyYW1ldGVyc1xyXG4gICAqIEByZXR1cm5zIFRpbWUgcmFuZ2UgZGVzY3JpcHRpb25cclxuICAgKi9cclxuICBwcml2YXRlIGdldFRpbWVSYW5nZURlc2NyaXB0aW9uKHJlcG9ydDogUmVwb3J0LCBwYXJhbWV0ZXJzPzogYW55KTogc3RyaW5nIHtcclxuICAgIGNvbnN0IHRpbWVSYW5nZSA9IHBhcmFtZXRlcnM/LnRpbWVSYW5nZSB8fCByZXBvcnQuY29uZmlndXJhdGlvbi50aW1lUmFuZ2U7XHJcbiAgICBcclxuICAgIGlmICghdGltZVJhbmdlKSB7XHJcbiAgICAgIHJldHVybiAnQWxsIHRpbWUnO1xyXG4gICAgfVxyXG5cclxuICAgIGlmICh0aW1lUmFuZ2UudHlwZSA9PT0gJ3JlbGF0aXZlJykge1xyXG4gICAgICByZXR1cm4gYExhc3QgJHt0aW1lUmFuZ2UudmFsdWV9YDtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHJldHVybiBgJHt0aW1lUmFuZ2Uuc3RhcnREYXRlfSB0byAke3RpbWVSYW5nZS5lbmREYXRlfWA7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHcm91cCBkYXRhIGJ5IHNwZWNpZmllZCBmaWVsZHNcclxuICAgKiBAcGFyYW0gZGF0YSBEYXRhIHRvIGdyb3VwXHJcbiAgICogQHBhcmFtIGdyb3VwQnkgRmllbGRzIHRvIGdyb3VwIGJ5XHJcbiAgICogQHJldHVybnMgR3JvdXBlZCBkYXRhXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBncm91cERhdGEoZGF0YTogYW55W10sIGdyb3VwQnk6IHN0cmluZ1tdKTogYW55IHtcclxuICAgIGlmICghQXJyYXkuaXNBcnJheShkYXRhKSB8fCBncm91cEJ5Lmxlbmd0aCA9PT0gMCkge1xyXG4gICAgICByZXR1cm4gZGF0YTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBncm91cGVkID0gZGF0YS5yZWR1Y2UoKGFjYywgaXRlbSkgPT4ge1xyXG4gICAgICBjb25zdCBrZXkgPSBncm91cEJ5Lm1hcChmaWVsZCA9PiBpdGVtW2ZpZWxkXSkuam9pbignfCcpO1xyXG4gICAgICBpZiAoIWFjY1trZXldKSB7XHJcbiAgICAgICAgYWNjW2tleV0gPSBbXTtcclxuICAgICAgfVxyXG4gICAgICBhY2Nba2V5XS5wdXNoKGl0ZW0pO1xyXG4gICAgICByZXR1cm4gYWNjO1xyXG4gICAgfSwge30pO1xyXG5cclxuICAgIHJldHVybiBPYmplY3QuZW50cmllcyhncm91cGVkKS5tYXAoKFtrZXksIGl0ZW1zXSkgPT4gKHtcclxuICAgICAgZ3JvdXA6IGtleSxcclxuICAgICAgY291bnQ6IChpdGVtcyBhcyBhbnlbXSkubGVuZ3RoLFxyXG4gICAgICBpdGVtcyxcclxuICAgIH0pKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEFwcGx5IGFnZ3JlZ2F0aW9ucyB0byBkYXRhXHJcbiAgICogQHBhcmFtIGRhdGEgRGF0YSB0byBhZ2dyZWdhdGVcclxuICAgKiBAcGFyYW0gYWdncmVnYXRpb25zIEFnZ3JlZ2F0aW9uIGNvbmZpZ3VyYXRpb25zXHJcbiAgICogQHJldHVybnMgQWdncmVnYXRlZCBtZXRyaWNzXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBhZ2dyZWdhdGVEYXRhKGRhdGE6IGFueVtdLCBhZ2dyZWdhdGlvbnM6IGFueVtdKTogYW55W10ge1xyXG4gICAgaWYgKCFBcnJheS5pc0FycmF5KGRhdGEpIHx8IGFnZ3JlZ2F0aW9ucy5sZW5ndGggPT09IDApIHtcclxuICAgICAgcmV0dXJuIFtdO1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiBhZ2dyZWdhdGlvbnMubWFwKGFnZyA9PiB7XHJcbiAgICAgIGxldCB2YWx1ZTogYW55O1xyXG4gICAgICBjb25zdCBmaWVsZCA9IGFnZy5maWVsZDtcclxuXHJcbiAgICAgIHN3aXRjaCAoYWdnLmZ1bmN0aW9uKSB7XHJcbiAgICAgICAgY2FzZSAnY291bnQnOlxyXG4gICAgICAgICAgdmFsdWUgPSBkYXRhLmxlbmd0aDtcclxuICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgIGNhc2UgJ3N1bSc6XHJcbiAgICAgICAgICB2YWx1ZSA9IGRhdGEucmVkdWNlKChzdW0sIGl0ZW0pID0+IHN1bSArIChOdW1iZXIoaXRlbVtmaWVsZF0pIHx8IDApLCAwKTtcclxuICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgIGNhc2UgJ2F2Zyc6XHJcbiAgICAgICAgICBjb25zdCBzdW0gPSBkYXRhLnJlZHVjZSgoc3VtLCBpdGVtKSA9PiBzdW0gKyAoTnVtYmVyKGl0ZW1bZmllbGRdKSB8fCAwKSwgMCk7XHJcbiAgICAgICAgICB2YWx1ZSA9IGRhdGEubGVuZ3RoID4gMCA/IHN1bSAvIGRhdGEubGVuZ3RoIDogMDtcclxuICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgIGNhc2UgJ21pbic6XHJcbiAgICAgICAgICB2YWx1ZSA9IE1hdGgubWluKC4uLmRhdGEubWFwKGl0ZW0gPT4gTnVtYmVyKGl0ZW1bZmllbGRdKSB8fCAwKSk7XHJcbiAgICAgICAgICBicmVhaztcclxuICAgICAgICBjYXNlICdtYXgnOlxyXG4gICAgICAgICAgdmFsdWUgPSBNYXRoLm1heCguLi5kYXRhLm1hcChpdGVtID0+IE51bWJlcihpdGVtW2ZpZWxkXSkgfHwgMCkpO1xyXG4gICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgZGVmYXVsdDpcclxuICAgICAgICAgIHZhbHVlID0gMDtcclxuICAgICAgfVxyXG5cclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICBpZDogYWdnLmFsaWFzIHx8IGAke2FnZy5mdW5jdGlvbn1fJHtmaWVsZH1gLFxyXG4gICAgICAgIHRpdGxlOiBhZ2cuYWxpYXMgfHwgYCR7YWdnLmZ1bmN0aW9uLnRvVXBwZXJDYXNlKCl9IG9mICR7ZmllbGR9YCxcclxuICAgICAgICB2YWx1ZSxcclxuICAgICAgICB1bml0OiBhZ2cudW5pdCxcclxuICAgICAgfTtcclxuICAgIH0pO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2VuZXJhdGUgY2hhcnQgZGF0YVxyXG4gICAqIEBwYXJhbSB2aXpDb25maWcgVmlzdWFsaXphdGlvbiBjb25maWd1cmF0aW9uXHJcbiAgICogQHBhcmFtIHByb2Nlc3NlZERhdGEgUHJvY2Vzc2VkIGRhdGFcclxuICAgKiBAcmV0dXJucyBDaGFydCBkYXRhXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBhc3luYyBnZW5lcmF0ZUNoYXJ0KHZpekNvbmZpZzogYW55LCBwcm9jZXNzZWREYXRhOiBhbnkpOiBQcm9taXNlPGFueT4ge1xyXG4gICAgLy8gVGhpcyBpcyBhIHNpbXBsaWZpZWQgY2hhcnQgZ2VuZXJhdGlvblxyXG4gICAgLy8gSW4gYSByZWFsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkIHVzZSBhIGNoYXJ0aW5nIGxpYnJhcnlcclxuICAgIGNvbnN0IGRhdGFTb3VyY2UgPSBwcm9jZXNzZWREYXRhLnRhYmxlcz8uZmluZCh0ID0+IHQuaWQuaW5jbHVkZXModml6Q29uZmlnLmRhdGFTb3VyY2UpKTtcclxuICAgIFxyXG4gICAgaWYgKCFkYXRhU291cmNlKSB7XHJcbiAgICAgIHJldHVybiB7IGxhYmVsczogW10sIGRhdGFzZXRzOiBbXSB9O1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGxhYmVscyA9IGRhdGFTb3VyY2Uucm93cy5tYXAoKHJvdywgaW5kZXgpID0+IHJvd1swXSB8fCBgSXRlbSAke2luZGV4ICsgMX1gKTtcclxuICAgIGNvbnN0IHZhbHVlcyA9IGRhdGFTb3VyY2Uucm93cy5tYXAocm93ID0+IE51bWJlcihyb3dbMV0pIHx8IDApO1xyXG5cclxuICAgIHJldHVybiB7XHJcbiAgICAgIGxhYmVscyxcclxuICAgICAgZGF0YXNldHM6IFt7XHJcbiAgICAgICAgbGFiZWw6IHZpekNvbmZpZy50aXRsZSxcclxuICAgICAgICBkYXRhOiB2YWx1ZXMsXHJcbiAgICAgICAgYmFja2dyb3VuZENvbG9yOiB2aXpDb25maWcuY29uZmlndXJhdGlvbj8uY29sb3JzIHx8IFsnIzM0OThkYiddLFxyXG4gICAgICB9XSxcclxuICAgIH07XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgcmVwb3J0IGV4ZWN1dGlvbiBieSBJRFxyXG4gICAqIEBwYXJhbSBleGVjdXRpb25JZCBFeGVjdXRpb24gSURcclxuICAgKiBAcmV0dXJucyBSZXBvcnQgZXhlY3V0aW9uXHJcbiAgICovXHJcbiAgYXN5bmMgZ2V0RXhlY3V0aW9uKGV4ZWN1dGlvbklkOiBzdHJpbmcpOiBQcm9taXNlPFJlcG9ydEV4ZWN1dGlvbiB8IG51bGw+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIHJldHVybiBhd2FpdCB0aGlzLmV4ZWN1dGlvblJlcG9zaXRvcnkuZmluZE9uZSh7XHJcbiAgICAgICAgd2hlcmU6IHsgaWQ6IGV4ZWN1dGlvbklkIH0sXHJcbiAgICAgICAgcmVsYXRpb25zOiBbJ3JlcG9ydCddLFxyXG4gICAgICB9KTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIHRoaXMubG9nZ2VyLmVycm9yKCdGYWlsZWQgdG8gZ2V0IHJlcG9ydCBleGVjdXRpb24nLCB7XHJcbiAgICAgICAgZXhlY3V0aW9uSWQsXHJcbiAgICAgICAgZXJyb3I6IGVycm9yLm1lc3NhZ2UsXHJcbiAgICAgIH0pO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENhbmNlbCBydW5uaW5nIHJlcG9ydCBleGVjdXRpb25cclxuICAgKiBAcGFyYW0gZXhlY3V0aW9uSWQgRXhlY3V0aW9uIElEXHJcbiAgICogQHBhcmFtIHVzZXJJZCBVc2VyIGNhbmNlbGxpbmcgdGhlIGV4ZWN1dGlvblxyXG4gICAqIEByZXR1cm5zIFVwZGF0ZWQgZXhlY3V0aW9uXHJcbiAgICovXHJcbiAgYXN5bmMgY2FuY2VsRXhlY3V0aW9uKGV4ZWN1dGlvbklkOiBzdHJpbmcsIHVzZXJJZDogc3RyaW5nKTogUHJvbWlzZTxSZXBvcnRFeGVjdXRpb24+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IGV4ZWN1dGlvbiA9IGF3YWl0IHRoaXMuZ2V0RXhlY3V0aW9uKGV4ZWN1dGlvbklkKTtcclxuICAgICAgaWYgKCFleGVjdXRpb24pIHtcclxuICAgICAgICB0aHJvdyBuZXcgTm90Rm91bmRFeGNlcHRpb24oJ1JlcG9ydCBleGVjdXRpb24gbm90IGZvdW5kJyk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGlmICghZXhlY3V0aW9uLmlzUnVubmluZykge1xyXG4gICAgICAgIHRocm93IG5ldyBCYWRSZXF1ZXN0RXhjZXB0aW9uKCdSZXBvcnQgZXhlY3V0aW9uIGlzIG5vdCBydW5uaW5nJyk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGV4ZWN1dGlvbi5jYW5jZWwoJ0NhbmNlbGxlZCBieSB1c2VyJyk7XHJcbiAgICAgIGNvbnN0IHVwZGF0ZWRFeGVjdXRpb24gPSBhd2FpdCB0aGlzLmV4ZWN1dGlvblJlcG9zaXRvcnkuc2F2ZShleGVjdXRpb24pO1xyXG5cclxuICAgICAgYXdhaXQgdGhpcy5hdWRpdFNlcnZpY2UubG9nVXNlckFjdGlvbihcclxuICAgICAgICB1c2VySWQsXHJcbiAgICAgICAgJ2NhbmNlbCcsXHJcbiAgICAgICAgJ3JlcG9ydF9leGVjdXRpb24nLFxyXG4gICAgICAgIGV4ZWN1dGlvbklkLFxyXG4gICAgICAgIHtcclxuICAgICAgICAgIHJlcG9ydElkOiBleGVjdXRpb24ucmVwb3J0SWQsXHJcbiAgICAgICAgICByZWFzb246ICd1c2VyX3JlcXVlc3QnLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICk7XHJcblxyXG4gICAgICB0aGlzLmxvZ2dlci5sb2coJ1JlcG9ydCBleGVjdXRpb24gY2FuY2VsbGVkJywge1xyXG4gICAgICAgIGV4ZWN1dGlvbklkLFxyXG4gICAgICAgIHVzZXJJZCxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICByZXR1cm4gdXBkYXRlZEV4ZWN1dGlvbjtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIHRoaXMubG9nZ2VyLmVycm9yKCdGYWlsZWQgdG8gY2FuY2VsIHJlcG9ydCBleGVjdXRpb24nLCB7XHJcbiAgICAgICAgZXhlY3V0aW9uSWQsXHJcbiAgICAgICAgZXJyb3I6IGVycm9yLm1lc3NhZ2UsXHJcbiAgICAgICAgdXNlcklkLFxyXG4gICAgICB9KTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcbiJdLCJ2ZXJzaW9uIjozfQ==