{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\user-management\\application\\services\\user.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,6CAAqD;AAErD,2CAA2F;AAC3F,iDAA6C;AAC7C,mEAAyD;AACzD,mEAAyD;AACzD,sFAAkF;AAClF,0FAAsF;AACtF,gGAA4F;AAE5F,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;IAC3B,IAAI,OAAoB,CAAC;IACzB,IAAI,cAAgC,CAAC;IACrC,IAAI,cAAgC,CAAC;IACrC,IAAI,aAA4B,CAAC;IACjC,IAAI,YAA0B,CAAC;IAC/B,IAAI,eAAgC,CAAC;IAErC,MAAM,kBAAkB,GAAG;QACzB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;KAC9B,CAAC;IAEF,MAAM,kBAAkB,GAAG;QACzB,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;KACrB,CAAC;IAEF,MAAM,iBAAiB,GAAG;QACxB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;QACd,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;KACjB,CAAC;IAEF,MAAM,gBAAgB,GAAG;QACvB,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;KACzB,CAAC;IAEF,MAAM,mBAAmB,GAAG;QAC1B,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE;KACxB,CAAC;IAEF,MAAM,QAAQ,GAAkB;QAC9B,EAAE,EAAE,sCAAsC;QAC1C,KAAK,EAAE,kBAAkB;QACzB,SAAS,EAAE,MAAM;QACjB,QAAQ,EAAE,KAAK;QACf,MAAM,EAAE,QAAQ;QAChB,aAAa,EAAE,IAAI;QACnB,KAAK,EAAE,EAAE;QACT,SAAS,EAAE,CAAC,MAAM,CAAC;QACnB,WAAW,EAAE,CAAC,YAAY,CAAC;QAC3B,QAAQ,EAAE,UAAU;QACpB,QAAQ,EAAE,IAAI;QACd,QAAQ,EAAE,KAAK;QACf,SAAS,EAAE,IAAI,IAAI,EAAE;QACrB,SAAS,EAAE,IAAI,IAAI,EAAE;KACtB,CAAC;IAEF,MAAM,QAAQ,GAAkB;QAC9B,EAAE,EAAE,UAAU;QACd,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE,MAAM;QACnB,SAAS,EAAE,IAAI;QACf,QAAQ,EAAE,IAAI;KACf,CAAC;IAEF,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,0BAAW;gBACX;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,kBAAI,CAAC;oBACjC,QAAQ,EAAE,kBAAkB;iBAC7B;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,kBAAI,CAAC;oBACjC,QAAQ,EAAE,kBAAkB;iBAC7B;gBACD;oBACE,OAAO,EAAE,8BAAa;oBACtB,QAAQ,EAAE,iBAAiB;iBAC5B;gBACD;oBACE,OAAO,EAAE,4BAAY;oBACrB,QAAQ,EAAE,gBAAgB;iBAC3B;gBACD;oBACE,OAAO,EAAE,kCAAe;oBACxB,QAAQ,EAAE,mBAAmB;iBAC9B;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAAc,0BAAW,CAAC,CAAC;QAC/C,cAAc,GAAG,MAAM,CAAC,GAAG,CAAmB,IAAA,4BAAkB,EAAC,kBAAI,CAAC,CAAC,CAAC;QACxE,cAAc,GAAG,MAAM,CAAC,GAAG,CAAmB,IAAA,4BAAkB,EAAC,kBAAI,CAAC,CAAC,CAAC;QACxE,aAAa,GAAG,MAAM,CAAC,GAAG,CAAgB,8BAAa,CAAC,CAAC;QACzD,YAAY,GAAG,MAAM,CAAC,GAAG,CAAe,4BAAY,CAAC,CAAC;QACtD,eAAe,GAAG,MAAM,CAAC,GAAG,CAAkB,kCAAe,CAAC,CAAC;IACjE,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,UAAU,GAAG;gBACjB,KAAK,EAAE,kBAAkB;gBACzB,SAAS,EAAE,MAAM;gBACjB,QAAQ,EAAE,KAAK;gBACf,YAAY,EAAE,aAAa;aAC5B,CAAC;YACF,MAAM,SAAS,GAAG,WAAW,CAAC;YAE9B,kBAAkB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAmB;YACvE,mBAAmB,CAAC,YAAY,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YACtE,kBAAkB,CAAC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,eAAe;YACvE,kBAAkB,CAAC,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YACpD,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAEpD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YAE3D,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBACtD,KAAK,EAAE,EAAE,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE;aACjD,CAAC,CAAC;YACH,MAAM,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;YACvF,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACrD,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAC/D,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,SAAS,EACT,QAAQ,EACR,MAAM,EACN,QAAQ,CAAC,EAAE,EACX,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mEAAmE,EAAE,KAAK,IAAI,EAAE;YACjF,MAAM,UAAU,GAAG;gBACjB,KAAK,EAAE,kBAAkB;gBACzB,iCAAiC;aAClC,CAAC;YACF,MAAM,SAAS,GAAG,WAAW,CAAC;YAE9B,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,4BAAmB,CAAC,CAAC;QAC3F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,KAAK,IAAI,EAAE;YACvE,MAAM,UAAU,GAAG;gBACjB,KAAK,EAAE,kBAAkB;gBACzB,SAAS,EAAE,MAAM;gBACjB,QAAQ,EAAE,KAAK;gBACf,YAAY,EAAE,aAAa;aAC5B,CAAC;YACF,MAAM,SAAS,GAAG,WAAW,CAAC;YAE9B,kBAAkB,CAAC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,gBAAgB;YAExE,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,0BAAiB,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,MAAM,EAAE,GAAG,sCAAsC,CAAC;YAClD,kBAAkB,CAAC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAEvD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAE1C,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBACtD,KAAK,EAAE,EAAE,EAAE,EAAE;gBACb,SAAS,EAAE,CAAC,OAAO,CAAC;aACrB,CAAC,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,EAAE,GAAG,sCAAsC,CAAC;YAClD,kBAAkB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAEnD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAE1C,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;YAC1B,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,KAAK,GAAG,kBAAkB,CAAC;YACjC,kBAAkB,CAAC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAEvD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAEhD,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBACtD,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,WAAW,EAAE,EAAE;gBACrC,SAAS,EAAE,CAAC,OAAO,CAAC;aACrB,CAAC,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,EAAE,GAAG,sCAAsC,CAAC;YAClD,MAAM,UAAU,GAAG,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC;YACzC,MAAM,SAAS,GAAG,WAAW,CAAC;YAE9B,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,QAAgB,CAAC,CAAC;YACpE,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,GAAG,QAAQ,EAAE,GAAG,UAAU,EAAE,CAAC,CAAC;YAE1E,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;YAE/D,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;YAClD,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACnD,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,SAAS,EACT,QAAQ,EACR,MAAM,EACN,EAAE,EACF,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,MAAM,EAAE,GAAG,sCAAsC,CAAC;YAClD,MAAM,UAAU,GAAG,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC;YACzC,MAAM,SAAS,GAAG,WAAW,CAAC;YAE9B,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAExD,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,0BAAiB,CAAC,CAAC;QAC7F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,KAAK,IAAI,EAAE;YACxE,MAAM,EAAE,GAAG,sCAAsC,CAAC;YAClD,MAAM,UAAU,GAAG,EAAE,KAAK,EAAE,sBAAsB,EAAE,CAAC;YACrD,MAAM,SAAS,GAAG,WAAW,CAAC;YAE9B,MAAM,YAAY,GAAG,EAAE,GAAG,QAAQ,EAAE,EAAE,EAAE,cAAc,EAAE,CAAC;YACzD,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,QAAgB,CAAC,CAAC;YACpE,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC,iBAAiB,CAAC,YAAoB,CAAC,CAAC;YAE3E,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,0BAAiB,CAAC,CAAC;QAC7F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,EAAE,GAAG,sCAAsC,CAAC;YAClD,MAAM,SAAS,GAAG,WAAW,CAAC;YAE9B,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,QAAgB,CAAC,CAAC;YACpE,kBAAkB,CAAC,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAEtD,MAAM,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;YAEpC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;YAClD,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YACjE,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,SAAS,EACT,QAAQ,EACR,MAAM,EACN,EAAE,EACF,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,MAAM,EAAE,GAAG,sCAAsC,CAAC;YAClD,MAAM,SAAS,GAAG,WAAW,CAAC;YAE9B,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAExD,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,0BAAiB,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,MAAM,GAAG,sCAAsC,CAAC;YACtD,MAAM,OAAO,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACrC,MAAM,UAAU,GAAG,WAAW,CAAC;YAE/B,MAAM,KAAK,GAAG;gBACZ,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE;gBAC/B,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;aAClC,CAAC;YAEF,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,QAAgB,CAAC,CAAC;YACpE,kBAAkB,CAAC,SAAS,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YACtD,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,CAAC;gBACxC,GAAG,QAAQ;gBACX,KAAK;gBACL,SAAS,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC;aAChC,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;YAEtE,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;YACtD,MAAM,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YACnE,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACnD,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,UAAU,EACV,cAAc,EACd,MAAM,EACN,MAAM,EACN,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,MAAM,MAAM,GAAG,sCAAsC,CAAC;YACtD,MAAM,OAAO,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC3B,MAAM,UAAU,GAAG,WAAW,CAAC;YAE/B,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAExD,MAAM,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,0BAAiB,CAAC,CAAC;QACpG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;YACpE,MAAM,MAAM,GAAG,sCAAsC,CAAC;YACtD,MAAM,OAAO,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACrC,MAAM,UAAU,GAAG,WAAW,CAAC;YAE/B,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,iBAAiB,CAAC,QAAgB,CAAC,CAAC;YACpE,kBAAkB,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,sBAAsB;YAEzG,MAAM,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,4BAAmB,CAAC,CAAC;QACtG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,kBAAkB,CAAC,KAAK;iBACrB,qBAAqB,CAAC,GAAG,CAAC,CAAC,QAAQ;iBACnC,qBAAqB,CAAC,EAAE,CAAC,CAAE,SAAS;iBACpC,qBAAqB,CAAC,EAAE,CAAC,CAAE,WAAW;iBACtC,qBAAqB,CAAC,CAAC,CAAC,CAAG,YAAY;iBACvC,qBAAqB,CAAC,CAAC,CAAC,CAAG,uBAAuB;iBAClD,qBAAqB,CAAC,EAAE,CAAC,CAAE,iBAAiB;iBAC5C,qBAAqB,CAAC,EAAE,CAAC,CAAC,CAAC,mBAAmB;YAEjD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,aAAa,EAAE,CAAC;YAE7C,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,KAAK,EAAE,GAAG;gBACV,QAAQ,EAAE;oBACR,MAAM,EAAE,EAAE;oBACV,QAAQ,EAAE,EAAE;oBACZ,SAAS,EAAE,CAAC;oBACZ,mBAAmB,EAAE,CAAC;iBACvB;gBACD,mBAAmB,EAAE;oBACnB,QAAQ,EAAE,EAAE;oBACZ,UAAU,EAAE,EAAE;iBACf;gBACD,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC9B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\user-management\\application\\services\\user.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { getRepositoryToken } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\nimport { NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';\r\nimport { UserService } from './user.service';\r\nimport { User } from '../../domain/entities/user.entity';\r\nimport { Role } from '../../domain/entities/role.entity';\r\nimport { LoggerService } from '../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../infrastructure/logging/audit/audit.service';\r\nimport { PasswordService } from '../../../../infrastructure/auth/services/password.service';\r\n\r\ndescribe('UserService', () => {\r\n  let service: UserService;\r\n  let userRepository: Repository<User>;\r\n  let roleRepository: Repository<Role>;\r\n  let loggerService: LoggerService;\r\n  let auditService: AuditService;\r\n  let passwordService: PasswordService;\r\n\r\n  const mockUserRepository = {\r\n    create: jest.fn(),\r\n    save: jest.fn(),\r\n    findOne: jest.fn(),\r\n    find: jest.fn(),\r\n    remove: jest.fn(),\r\n    count: jest.fn(),\r\n    createQueryBuilder: jest.fn(),\r\n  };\r\n\r\n  const mockRoleRepository = {\r\n    findOne: jest.fn(),\r\n    findByIds: jest.fn(),\r\n  };\r\n\r\n  const mockLoggerService = {\r\n    debug: jest.fn(),\r\n    log: jest.fn(),\r\n    warn: jest.fn(),\r\n    error: jest.fn(),\r\n  };\r\n\r\n  const mockAuditService = {\r\n    logUserAction: jest.fn(),\r\n  };\r\n\r\n  const mockPasswordService = {\r\n    hashPassword: jest.fn(),\r\n  };\r\n\r\n  const mockUser: Partial<User> = {\r\n    id: '123e4567-e89b-12d3-a456-426614174000',\r\n    email: '<EMAIL>',\r\n    firstName: 'John',\r\n    lastName: 'Doe',\r\n    status: 'active',\r\n    emailVerified: true,\r\n    roles: [],\r\n    roleNames: ['user'],\r\n    permissions: ['users.read'],\r\n    fullName: 'John Doe',\r\n    canLogin: true,\r\n    isLocked: false,\r\n    createdAt: new Date(),\r\n    updatedAt: new Date(),\r\n  };\r\n\r\n  const mockRole: Partial<Role> = {\r\n    id: 'role-123',\r\n    name: 'user',\r\n    displayName: 'User',\r\n    isDefault: true,\r\n    isActive: true,\r\n  };\r\n\r\n  beforeEach(async () => {\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        UserService,\r\n        {\r\n          provide: getRepositoryToken(User),\r\n          useValue: mockUserRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(Role),\r\n          useValue: mockRoleRepository,\r\n        },\r\n        {\r\n          provide: LoggerService,\r\n          useValue: mockLoggerService,\r\n        },\r\n        {\r\n          provide: AuditService,\r\n          useValue: mockAuditService,\r\n        },\r\n        {\r\n          provide: PasswordService,\r\n          useValue: mockPasswordService,\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    service = module.get<UserService>(UserService);\r\n    userRepository = module.get<Repository<User>>(getRepositoryToken(User));\r\n    roleRepository = module.get<Repository<Role>>(getRepositoryToken(Role));\r\n    loggerService = module.get<LoggerService>(LoggerService);\r\n    auditService = module.get<AuditService>(AuditService);\r\n    passwordService = module.get<PasswordService>(PasswordService);\r\n  });\r\n\r\n  afterEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('create', () => {\r\n    it('should create a user successfully', async () => {\r\n      const createData = {\r\n        email: '<EMAIL>',\r\n        firstName: 'John',\r\n        lastName: 'Doe',\r\n        passwordHash: 'password123',\r\n      };\r\n      const createdBy = 'admin-123';\r\n\r\n      mockUserRepository.findOne.mockResolvedValue(null); // No existing user\r\n      mockPasswordService.hashPassword.mockResolvedValue('hashed-password');\r\n      mockRoleRepository.findOne.mockResolvedValue(mockRole); // Default role\r\n      mockUserRepository.create.mockReturnValue(mockUser);\r\n      mockUserRepository.save.mockResolvedValue(mockUser);\r\n\r\n      const result = await service.create(createData, createdBy);\r\n\r\n      expect(mockUserRepository.findOne).toHaveBeenCalledWith({\r\n        where: { email: createData.email.toLowerCase() },\r\n      });\r\n      expect(mockPasswordService.hashPassword).toHaveBeenCalledWith(createData.passwordHash);\r\n      expect(mockUserRepository.create).toHaveBeenCalled();\r\n      expect(mockUserRepository.save).toHaveBeenCalledWith(mockUser);\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        createdBy,\r\n        'create',\r\n        'user',\r\n        mockUser.id,\r\n        expect.any(Object),\r\n      );\r\n      expect(result).toEqual(mockUser);\r\n    });\r\n\r\n    it('should throw BadRequestException when required fields are missing', async () => {\r\n      const createData = {\r\n        email: '<EMAIL>',\r\n        // Missing firstName and lastName\r\n      };\r\n      const createdBy = 'admin-123';\r\n\r\n      await expect(service.create(createData, createdBy)).rejects.toThrow(BadRequestException);\r\n    });\r\n\r\n    it('should throw ConflictException when user already exists', async () => {\r\n      const createData = {\r\n        email: '<EMAIL>',\r\n        firstName: 'John',\r\n        lastName: 'Doe',\r\n        passwordHash: 'password123',\r\n      };\r\n      const createdBy = 'admin-123';\r\n\r\n      mockUserRepository.findOne.mockResolvedValue(mockUser); // Existing user\r\n\r\n      await expect(service.create(createData, createdBy)).rejects.toThrow(ConflictException);\r\n    });\r\n  });\r\n\r\n  describe('findById', () => {\r\n    it('should return user when found', async () => {\r\n      const id = '123e4567-e89b-12d3-a456-426614174000';\r\n      mockUserRepository.findOne.mockResolvedValue(mockUser);\r\n\r\n      const result = await service.findById(id);\r\n\r\n      expect(mockUserRepository.findOne).toHaveBeenCalledWith({\r\n        where: { id },\r\n        relations: ['roles'],\r\n      });\r\n      expect(result).toEqual(mockUser);\r\n    });\r\n\r\n    it('should return null when user not found', async () => {\r\n      const id = '123e4567-e89b-12d3-a456-426614174000';\r\n      mockUserRepository.findOne.mockResolvedValue(null);\r\n\r\n      const result = await service.findById(id);\r\n\r\n      expect(result).toBeNull();\r\n      expect(mockLoggerService.warn).toHaveBeenCalledWith('User not found', { id });\r\n    });\r\n  });\r\n\r\n  describe('findByEmail', () => {\r\n    it('should return user when found by email', async () => {\r\n      const email = '<EMAIL>';\r\n      mockUserRepository.findOne.mockResolvedValue(mockUser);\r\n\r\n      const result = await service.findByEmail(email);\r\n\r\n      expect(mockUserRepository.findOne).toHaveBeenCalledWith({\r\n        where: { email: email.toLowerCase() },\r\n        relations: ['roles'],\r\n      });\r\n      expect(result).toEqual(mockUser);\r\n    });\r\n  });\r\n\r\n  describe('update', () => {\r\n    it('should update user successfully', async () => {\r\n      const id = '123e4567-e89b-12d3-a456-426614174000';\r\n      const updateData = { firstName: 'Jane' };\r\n      const updatedBy = 'admin-123';\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(mockUser as User);\r\n      mockUserRepository.save.mockResolvedValue({ ...mockUser, ...updateData });\r\n\r\n      const result = await service.update(id, updateData, updatedBy);\r\n\r\n      expect(service.findById).toHaveBeenCalledWith(id);\r\n      expect(mockUserRepository.save).toHaveBeenCalled();\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        updatedBy,\r\n        'update',\r\n        'user',\r\n        id,\r\n        expect.any(Object),\r\n      );\r\n      expect(result).toEqual(expect.objectContaining(updateData));\r\n    });\r\n\r\n    it('should throw NotFoundException when user not found', async () => {\r\n      const id = '123e4567-e89b-12d3-a456-426614174000';\r\n      const updateData = { firstName: 'Jane' };\r\n      const updatedBy = 'admin-123';\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(null);\r\n\r\n      await expect(service.update(id, updateData, updatedBy)).rejects.toThrow(NotFoundException);\r\n    });\r\n\r\n    it('should throw ConflictException when email already exists', async () => {\r\n      const id = '123e4567-e89b-12d3-a456-426614174000';\r\n      const updateData = { email: '<EMAIL>' };\r\n      const updatedBy = 'admin-123';\r\n\r\n      const existingUser = { ...mockUser, id: 'different-id' };\r\n      jest.spyOn(service, 'findById').mockResolvedValue(mockUser as User);\r\n      jest.spyOn(service, 'findByEmail').mockResolvedValue(existingUser as User);\r\n\r\n      await expect(service.update(id, updateData, updatedBy)).rejects.toThrow(ConflictException);\r\n    });\r\n  });\r\n\r\n  describe('delete', () => {\r\n    it('should delete user successfully', async () => {\r\n      const id = '123e4567-e89b-12d3-a456-426614174000';\r\n      const deletedBy = 'admin-123';\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(mockUser as User);\r\n      mockUserRepository.remove.mockResolvedValue(mockUser);\r\n\r\n      await service.delete(id, deletedBy);\r\n\r\n      expect(service.findById).toHaveBeenCalledWith(id);\r\n      expect(mockUserRepository.remove).toHaveBeenCalledWith(mockUser);\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        deletedBy,\r\n        'delete',\r\n        'user',\r\n        id,\r\n        expect.any(Object),\r\n      );\r\n    });\r\n\r\n    it('should throw NotFoundException when user not found', async () => {\r\n      const id = '123e4567-e89b-12d3-a456-426614174000';\r\n      const deletedBy = 'admin-123';\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(null);\r\n\r\n      await expect(service.delete(id, deletedBy)).rejects.toThrow(NotFoundException);\r\n    });\r\n  });\r\n\r\n  describe('assignRoles', () => {\r\n    it('should assign roles to user successfully', async () => {\r\n      const userId = '123e4567-e89b-12d3-a456-426614174000';\r\n      const roleIds = ['role-1', 'role-2'];\r\n      const assignedBy = 'admin-123';\r\n\r\n      const roles = [\r\n        { id: 'role-1', name: 'admin' },\r\n        { id: 'role-2', name: 'analyst' },\r\n      ];\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(mockUser as User);\r\n      mockRoleRepository.findByIds.mockResolvedValue(roles);\r\n      mockUserRepository.save.mockResolvedValue({\r\n        ...mockUser,\r\n        roles,\r\n        roleNames: ['admin', 'analyst'],\r\n      });\r\n\r\n      const result = await service.assignRoles(userId, roleIds, assignedBy);\r\n\r\n      expect(service.findById).toHaveBeenCalledWith(userId);\r\n      expect(mockRoleRepository.findByIds).toHaveBeenCalledWith(roleIds);\r\n      expect(mockUserRepository.save).toHaveBeenCalled();\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        assignedBy,\r\n        'assign_roles',\r\n        'user',\r\n        userId,\r\n        expect.any(Object),\r\n      );\r\n      expect(result.roles).toEqual(roles);\r\n    });\r\n\r\n    it('should throw NotFoundException when user not found', async () => {\r\n      const userId = '123e4567-e89b-12d3-a456-426614174000';\r\n      const roleIds = ['role-1'];\r\n      const assignedBy = 'admin-123';\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(null);\r\n\r\n      await expect(service.assignRoles(userId, roleIds, assignedBy)).rejects.toThrow(NotFoundException);\r\n    });\r\n\r\n    it('should throw BadRequestException when role not found', async () => {\r\n      const userId = '123e4567-e89b-12d3-a456-426614174000';\r\n      const roleIds = ['role-1', 'role-2'];\r\n      const assignedBy = 'admin-123';\r\n\r\n      jest.spyOn(service, 'findById').mockResolvedValue(mockUser as User);\r\n      mockRoleRepository.findByIds.mockResolvedValue([{ id: 'role-1', name: 'admin' }]); // Only one role found\r\n\r\n      await expect(service.assignRoles(userId, roleIds, assignedBy)).rejects.toThrow(BadRequestException);\r\n    });\r\n  });\r\n\r\n  describe('getStatistics', () => {\r\n    it('should return user statistics', async () => {\r\n      mockUserRepository.count\r\n        .mockResolvedValueOnce(100) // total\r\n        .mockResolvedValueOnce(80)  // active\r\n        .mockResolvedValueOnce(10)  // inactive\r\n        .mockResolvedValueOnce(5)   // suspended\r\n        .mockResolvedValueOnce(5)   // pending verification\r\n        .mockResolvedValueOnce(90)  // email verified\r\n        .mockResolvedValueOnce(10); // email unverified\r\n\r\n      const result = await service.getStatistics();\r\n\r\n      expect(result).toEqual({\r\n        total: 100,\r\n        byStatus: {\r\n          active: 80,\r\n          inactive: 10,\r\n          suspended: 5,\r\n          pendingVerification: 5,\r\n        },\r\n        byEmailVerification: {\r\n          verified: 90,\r\n          unverified: 10,\r\n        },\r\n        timestamp: expect.any(String),\r\n      });\r\n    });\r\n  });\r\n});\r\n"], "version": 3}