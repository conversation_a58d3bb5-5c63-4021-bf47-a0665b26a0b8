6c91e76cce55ea905a867bfc043ac66e
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vulnerability_severity_enum_1 = require("../vulnerability-severity.enum");
describe('VulnerabilitySeverity', () => {
    describe('enum values', () => {
        it('should have all expected severity levels', () => {
            expect(vulnerability_severity_enum_1.VulnerabilitySeverity.NONE).toBe('none');
            expect(vulnerability_severity_enum_1.VulnerabilitySeverity.LOW).toBe('low');
            expect(vulnerability_severity_enum_1.VulnerabilitySeverity.MEDIUM).toBe('medium');
            expect(vulnerability_severity_enum_1.VulnerabilitySeverity.HIGH).toBe('high');
            expect(vulnerability_severity_enum_1.VulnerabilitySeverity.CRITICAL).toBe('critical');
            expect(vulnerability_severity_enum_1.VulnerabilitySeverity.UNKNOWN).toBe('unknown');
        });
    });
    describe('VulnerabilitySeverityUtils', () => {
        describe('getAllSeverities', () => {
            it('should return all severity levels', () => {
                const severities = vulnerability_severity_enum_1.VulnerabilitySeverityUtils.getAllSeverities();
                expect(severities).toHaveLength(6);
                expect(severities).toContain(vulnerability_severity_enum_1.VulnerabilitySeverity.NONE);
                expect(severities).toContain(vulnerability_severity_enum_1.VulnerabilitySeverity.LOW);
                expect(severities).toContain(vulnerability_severity_enum_1.VulnerabilitySeverity.MEDIUM);
                expect(severities).toContain(vulnerability_severity_enum_1.VulnerabilitySeverity.HIGH);
                expect(severities).toContain(vulnerability_severity_enum_1.VulnerabilitySeverity.CRITICAL);
                expect(severities).toContain(vulnerability_severity_enum_1.VulnerabilitySeverity.UNKNOWN);
            });
        });
        describe('fromCVSSScore', () => {
            it('should return correct severity for CVSS scores', () => {
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.fromCVSSScore(0.0)).toBe(vulnerability_severity_enum_1.VulnerabilitySeverity.NONE);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.fromCVSSScore(2.5)).toBe(vulnerability_severity_enum_1.VulnerabilitySeverity.LOW);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.fromCVSSScore(5.0)).toBe(vulnerability_severity_enum_1.VulnerabilitySeverity.MEDIUM);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.fromCVSSScore(7.5)).toBe(vulnerability_severity_enum_1.VulnerabilitySeverity.HIGH);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.fromCVSSScore(9.5)).toBe(vulnerability_severity_enum_1.VulnerabilitySeverity.CRITICAL);
            });
            it('should handle edge cases', () => {
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.fromCVSSScore(-1)).toBe(vulnerability_severity_enum_1.VulnerabilitySeverity.UNKNOWN);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.fromCVSSScore(11)).toBe(vulnerability_severity_enum_1.VulnerabilitySeverity.UNKNOWN);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.fromCVSSScore(0.1)).toBe(vulnerability_severity_enum_1.VulnerabilitySeverity.LOW);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.fromCVSSScore(4.0)).toBe(vulnerability_severity_enum_1.VulnerabilitySeverity.MEDIUM);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.fromCVSSScore(7.0)).toBe(vulnerability_severity_enum_1.VulnerabilitySeverity.HIGH);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.fromCVSSScore(9.0)).toBe(vulnerability_severity_enum_1.VulnerabilitySeverity.CRITICAL);
            });
        });
        describe('requiresImmediateRemediation', () => {
            it('should identify severities requiring immediate remediation', () => {
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.requiresImmediateRemediation(vulnerability_severity_enum_1.VulnerabilitySeverity.CRITICAL)).toBe(true);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.requiresImmediateRemediation(vulnerability_severity_enum_1.VulnerabilitySeverity.HIGH)).toBe(true);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.requiresImmediateRemediation(vulnerability_severity_enum_1.VulnerabilitySeverity.MEDIUM)).toBe(false);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.requiresImmediateRemediation(vulnerability_severity_enum_1.VulnerabilitySeverity.LOW)).toBe(false);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.requiresImmediateRemediation(vulnerability_severity_enum_1.VulnerabilitySeverity.NONE)).toBe(false);
            });
        });
        describe('getRemediationSLA', () => {
            it('should return correct SLA days for each severity', () => {
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.getRemediationSLA(vulnerability_severity_enum_1.VulnerabilitySeverity.CRITICAL)).toBe(1);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.getRemediationSLA(vulnerability_severity_enum_1.VulnerabilitySeverity.HIGH)).toBe(7);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.getRemediationSLA(vulnerability_severity_enum_1.VulnerabilitySeverity.MEDIUM)).toBe(30);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.getRemediationSLA(vulnerability_severity_enum_1.VulnerabilitySeverity.LOW)).toBe(90);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.getRemediationSLA(vulnerability_severity_enum_1.VulnerabilitySeverity.NONE)).toBe(365);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.getRemediationSLA(vulnerability_severity_enum_1.VulnerabilitySeverity.UNKNOWN)).toBe(30);
            });
        });
        describe('compare', () => {
            it('should correctly compare severities', () => {
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.compare(vulnerability_severity_enum_1.VulnerabilitySeverity.CRITICAL, vulnerability_severity_enum_1.VulnerabilitySeverity.HIGH)).toBeGreaterThan(0);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.compare(vulnerability_severity_enum_1.VulnerabilitySeverity.HIGH, vulnerability_severity_enum_1.VulnerabilitySeverity.MEDIUM)).toBeGreaterThan(0);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.compare(vulnerability_severity_enum_1.VulnerabilitySeverity.MEDIUM, vulnerability_severity_enum_1.VulnerabilitySeverity.LOW)).toBeGreaterThan(0);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.compare(vulnerability_severity_enum_1.VulnerabilitySeverity.LOW, vulnerability_severity_enum_1.VulnerabilitySeverity.NONE)).toBeGreaterThan(0);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.compare(vulnerability_severity_enum_1.VulnerabilitySeverity.MEDIUM, vulnerability_severity_enum_1.VulnerabilitySeverity.MEDIUM)).toBe(0);
            });
        });
        describe('getHigher', () => {
            it('should return the higher severity', () => {
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.getHigher(vulnerability_severity_enum_1.VulnerabilitySeverity.HIGH, vulnerability_severity_enum_1.VulnerabilitySeverity.MEDIUM)).toBe(vulnerability_severity_enum_1.VulnerabilitySeverity.HIGH);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.getHigher(vulnerability_severity_enum_1.VulnerabilitySeverity.LOW, vulnerability_severity_enum_1.VulnerabilitySeverity.CRITICAL)).toBe(vulnerability_severity_enum_1.VulnerabilitySeverity.CRITICAL);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.getHigher(vulnerability_severity_enum_1.VulnerabilitySeverity.MEDIUM, vulnerability_severity_enum_1.VulnerabilitySeverity.MEDIUM)).toBe(vulnerability_severity_enum_1.VulnerabilitySeverity.MEDIUM);
            });
        });
        describe('isValid', () => {
            it('should validate severity strings', () => {
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.isValid('critical')).toBe(true);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.isValid('high')).toBe(true);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.isValid('invalid')).toBe(false);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.isValid('')).toBe(false);
            });
        });
        describe('fromString', () => {
            it('should convert string to severity', () => {
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.fromString('critical')).toBe(vulnerability_severity_enum_1.VulnerabilitySeverity.CRITICAL);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.fromString('high')).toBe(vulnerability_severity_enum_1.VulnerabilitySeverity.HIGH);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.fromString('MEDIUM')).toBe(vulnerability_severity_enum_1.VulnerabilitySeverity.MEDIUM);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.fromString('invalid')).toBeNull();
            });
        });
        describe('calculateCombinedSeverity', () => {
            it('should calculate combined severity correctly', () => {
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.calculateCombinedSeverity([])).toBe(vulnerability_severity_enum_1.VulnerabilitySeverity.UNKNOWN);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.calculateCombinedSeverity([
                    vulnerability_severity_enum_1.VulnerabilitySeverity.CRITICAL,
                    vulnerability_severity_enum_1.VulnerabilitySeverity.LOW
                ])).toBe(vulnerability_severity_enum_1.VulnerabilitySeverity.CRITICAL);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.calculateCombinedSeverity([
                    vulnerability_severity_enum_1.VulnerabilitySeverity.HIGH,
                    vulnerability_severity_enum_1.VulnerabilitySeverity.HIGH,
                    vulnerability_severity_enum_1.VulnerabilitySeverity.MEDIUM
                ])).toBe(vulnerability_severity_enum_1.VulnerabilitySeverity.HIGH);
                expect(vulnerability_severity_enum_1.VulnerabilitySeverityUtils.calculateCombinedSeverity([
                    vulnerability_severity_enum_1.VulnerabilitySeverity.LOW,
                    vulnerability_severity_enum_1.VulnerabilitySeverity.LOW
                ])).toBe(vulnerability_severity_enum_1.VulnerabilitySeverity.LOW);
            });
        });
        describe('getColorCode', () => {
            it('should return valid color codes', () => {
                const colorCode = vulnerability_severity_enum_1.VulnerabilitySeverityUtils.getColorCode(vulnerability_severity_enum_1.VulnerabilitySeverity.CRITICAL);
                expect(colorCode).toMatch(/^#[0-9A-F]{6}$/i);
            });
        });
        describe('getProcessingPriority', () => {
            it('should return higher priority for more severe vulnerabilities', () => {
                const criticalPriority = vulnerability_severity_enum_1.VulnerabilitySeverityUtils.getProcessingPriority(vulnerability_severity_enum_1.VulnerabilitySeverity.CRITICAL);
                const lowPriority = vulnerability_severity_enum_1.VulnerabilitySeverityUtils.getProcessingPriority(vulnerability_severity_enum_1.VulnerabilitySeverity.LOW);
                expect(criticalPriority).toBeGreaterThan(lowPriority);
            });
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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