4cead5976d5ce93616626788e9c0e8f3
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ConnectionFactory_1;
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConnectionFactory = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("typeorm");
// Helper function to safely extract error information
const getErrorInfo = (error) => ({
    message: error instanceof Error ? error.message : String(error),
    stack: error instanceof Error ? error.stack : undefined,
});
/**
 * Connection factory for creating and managing database connections
 * Supports multiple database types and connection configurations
 */
let ConnectionFactory = ConnectionFactory_1 = class ConnectionFactory {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(ConnectionFactory_1.name);
        this.connections = new Map();
    }
    /**
     * Create a new database connection
     * @param name Connection name
     * @param options Connection options (optional, uses default config if not provided)
     * @returns Promise<DataSource> Database connection
     */
    async createConnection(name = 'default', options) {
        try {
            // Check if connection already exists
            if (this.connections.has(name)) {
                const existingConnection = this.connections.get(name);
                if (existingConnection?.isInitialized) {
                    this.logger.debug(`Reusing existing connection: ${name}`);
                    return existingConnection;
                }
            }
            // Get default configuration
            const defaultConfig = this.configService.get('database');
            // Merge with provided options
            const connectionOptions = {
                ...defaultConfig,
                ...options,
                name,
            };
            this.logger.debug(`Creating new database connection: ${name}`, {
                host: connectionOptions.host,
                database: connectionOptions.database,
                type: connectionOptions.type,
            });
            // Create and initialize connection
            const connection = new typeorm_1.DataSource(connectionOptions);
            await connection.initialize();
            // Store connection for reuse
            this.connections.set(name, connection);
            this.logger.log(`Database connection '${name}' created successfully`);
            return connection;
        }
        catch (error) {
            const errorInfo = getErrorInfo(error);
            this.logger.error(`Failed to create database connection '${name}'`, {
                error: errorInfo.message,
                stack: errorInfo.stack,
            });
            throw error;
        }
    }
    /**
     * Get an existing connection by name
     * @param name Connection name
     * @returns DataSource | undefined
     */
    getConnection(name = 'default') {
        return this.connections.get(name);
    }
    /**
     * Close a specific connection
     * @param name Connection name
     * @returns Promise<void>
     */
    async closeConnection(name = 'default') {
        try {
            const connection = this.connections.get(name);
            if (connection && connection.isInitialized) {
                await connection.destroy();
                this.connections.delete(name);
                this.logger.log(`Database connection '${name}' closed successfully`);
            }
            else {
                this.logger.warn(`Connection '${name}' not found or already closed`);
            }
        }
        catch (error) {
            const errorInfo = getErrorInfo(error);
            this.logger.error(`Failed to close database connection '${name}'`, {
                error: errorInfo.message,
            });
            throw error;
        }
    }
    /**
     * Close all connections
     * @returns Promise<void>
     */
    async closeAllConnections() {
        const connectionNames = Array.from(this.connections.keys());
        this.logger.log(`Closing ${connectionNames.length} database connections...`);
        const closePromises = connectionNames.map(name => this.closeConnection(name).catch(error => {
            this.logger.error(`Failed to close connection '${name}'`, error);
        }));
        await Promise.all(closePromises);
        this.logger.log('All database connections closed');
    }
    /**
     * Create a read-only connection for reporting/analytics
     * @returns Promise<DataSource>
     */
    async createReadOnlyConnection() {
        const readOnlyConfig = {
            extra: {
                ...this.configService.get('database.extra'),
                // Configure for read-only operations
                max: 20, // Smaller pool for read operations
                statement_timeout: 30000, // 30 second timeout
                query_timeout: 30000,
            },
            // Disable migrations and schema sync for read-only
            migrationsRun: false,
            synchronize: false,
            logging: ['error'], // Minimal logging for read operations
        };
        return this.createConnection('readonly', readOnlyConfig);
    }
    /**
     * Create a connection for background jobs/workers
     * @returns Promise<DataSource>
     */
    async createWorkerConnection() {
        const workerConfig = {
            extra: {
                ...this.configService.get('database.extra'),
                // Configure for background processing
                max: 10, // Smaller pool for workers
                min: 2,
                idle: 30000, // Longer idle timeout
                acquire: 60000, // Longer acquire timeout
            },
            // Worker-specific settings
            logging: ['error', 'warn'],
            cache: false, // Disable caching for workers
        };
        return this.createConnection('worker', workerConfig);
    }
    /**
     * Test connection with retry logic
     * @param name Connection name
     * @param maxRetries Maximum number of retry attempts
     * @param retryDelay Delay between retries in milliseconds
     * @returns Promise<boolean>
     */
    async testConnection(name = 'default', maxRetries = 3, retryDelay = 1000) {
        let lastError;
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                this.logger.debug(`Testing database connection '${name}' (attempt ${attempt}/${maxRetries})`);
                const connection = this.getConnection(name);
                if (!connection) {
                    throw new Error(`Connection '${name}' not found`);
                }
                // Test with a simple query
                await connection.query('SELECT 1');
                this.logger.log(`Database connection '${name}' test successful`);
                return true;
            }
            catch (error) {
                lastError = error instanceof Error ? error : new Error(String(error));
                const errorInfo = getErrorInfo(error);
                this.logger.warn(`Database connection test failed (attempt ${attempt}/${maxRetries})`, {
                    error: errorInfo.message,
                    name,
                });
                if (attempt < maxRetries) {
                    this.logger.debug(`Retrying in ${retryDelay}ms...`);
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                }
            }
        }
        this.logger.error(`Database connection test failed after ${maxRetries} attempts`, {
            name,
            error: lastError.message,
        });
        return false;
    }
    /**
     * Get connection health status for all connections
     * @returns Promise<Record<string, any>>
     */
    async getConnectionsHealth() {
        const health = {};
        const entries = Array.from(this.connections.entries());
        for (const [name, connection] of entries) {
            try {
                const isHealthy = await this.testConnection(name, 1, 0);
                health[name] = {
                    status: isHealthy ? 'healthy' : 'unhealthy',
                    isInitialized: connection.isInitialized,
                    database: connection.options.database,
                    host: connection.options.host,
                    type: connection.options.type,
                    entityCount: connection.entityMetadatas.length,
                };
            }
            catch (error) {
                const errorInfo = getErrorInfo(error);
                health[name] = {
                    status: 'error',
                    error: errorInfo.message,
                    isInitialized: connection.isInitialized,
                };
            }
        }
        return health;
    }
    /**
     * Get connection statistics
     * @returns Record<string, any>
     */
    getConnectionStats() {
        const stats = {
            totalConnections: this.connections.size,
            activeConnections: 0,
            connections: {},
        };
        const entries = Array.from(this.connections.entries());
        for (const [name, connection] of entries) {
            const isActive = connection.isInitialized;
            if (isActive) {
                stats.activeConnections++;
            }
            stats.connections[name] = {
                isActive,
                database: connection.options.database,
                type: connection.options.type,
                entityCount: connection.entityMetadatas.length,
                migrationCount: connection.migrations.length,
            };
        }
        return stats;
    }
};
exports.ConnectionFactory = ConnectionFactory;
exports.ConnectionFactory = ConnectionFactory = ConnectionFactory_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], ConnectionFactory);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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