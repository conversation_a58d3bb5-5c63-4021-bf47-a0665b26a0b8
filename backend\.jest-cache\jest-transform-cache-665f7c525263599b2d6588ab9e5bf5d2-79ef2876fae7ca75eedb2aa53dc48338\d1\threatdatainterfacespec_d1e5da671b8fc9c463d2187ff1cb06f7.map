{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\data\\__tests__\\threat-data.interface.spec.ts", "mappings": ";;AAAA,oEA8BkC;AAClC,8EAAqE;AACrE,gFAAuE;AAEvE,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;IACpC,IAAI,cAA0B,CAAC;IAC/B,IAAI,cAAqC,CAAC;IAC1C,IAAI,cAAgC,CAAC;IACrC,IAAI,eAAsC,CAAC;IAC3C,IAAI,iBAA0C,CAAC;IAC/C,IAAI,oBAAiD,CAAC;IACtD,IAAI,YAAgC,CAAC;IACrC,IAAI,eAAuC,CAAC;IAC5C,IAAI,kBAA6C,CAAC;IAClD,IAAI,eAAkC,CAAC;IACvC,IAAI,kBAA4C,CAAC;IAEjD,UAAU,CAAC,GAAG,EAAE;QACd,cAAc,GAAG;YACf;gBACE,IAAI,EAAE,2CAAmB,CAAC,UAAU;gBACpC,KAAK,EAAE,eAAe;gBACtB,WAAW,EAAE,sBAAsB;gBACnC,UAAU,EAAE,uCAAe,CAAC,IAAI;gBAChC,SAAS,EAAE,sBAAsB;gBACjC,QAAQ,EAAE,sBAAsB;gBAChC,MAAM,EAAE,6CAAqB,CAAC,MAAM;gBACpC,IAAI,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC;gBACvB,OAAO,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE;aAChC;YACD;gBACE,IAAI,EAAE,2CAAmB,CAAC,MAAM;gBAChC,KAAK,EAAE,eAAe;gBACtB,WAAW,EAAE,4BAA4B;gBACzC,UAAU,EAAE,uCAAe,CAAC,MAAM;gBAClC,SAAS,EAAE,sBAAsB;gBACjC,QAAQ,EAAE,sBAAsB;gBAChC,MAAM,EAAE,6CAAqB,CAAC,MAAM;gBACpC,IAAI,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC;aACpB;SACF,CAAC;QAEF,cAAc,GAAG;YACf;gBACE,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,GAAG;gBACV,MAAM,EAAE,8CAA8C;gBACtD,QAAQ,EAAE,MAAM;gBAChB,mBAAmB,EAAE,GAAG;gBACxB,WAAW,EAAE,GAAG;aACjB;SACF,CAAC;QAEF,eAAe,GAAG;YAChB,SAAS,EAAE,OAAO;YAClB,OAAO,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;YACnC,SAAS,EAAE,uCAAe,CAAC,YAAY;YACvC,OAAO,EAAE,IAAI;YACb,UAAU,EAAE,CAAC,6CAAqB,CAAC,SAAS,CAAC;YAC7C,cAAc,EAAE,iDAAyB,CAAC,QAAQ;YAClD,UAAU,EAAE,uCAAe,CAAC,IAAI;YAChC,QAAQ,EAAE,CAAC,kCAAkC,CAAC;SAC/C,CAAC;QAEF,iBAAiB,GAAG;YAClB,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,CAAC,IAAI,EAAE,cAAc,CAAC;YAC/B,IAAI,EAAE,mCAAW,CAAC,GAAG;YACrB,SAAS,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC;YAC/B,YAAY,EAAE,CAAC,eAAe,EAAE,kBAAkB,CAAC;YACnD,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;SAC3B,CAAC;QAEF,oBAAoB,GAAG;YACrB;gBACE,WAAW,EAAE,OAAO;gBACpB,IAAI,EAAE,mBAAmB;gBACzB,MAAM,EAAE,iBAAiB;gBACzB,aAAa,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;gBACzC,SAAS,EAAE,CAAC,SAAS,CAAC;gBACtB,WAAW,EAAE,CAAC,oBAAoB,CAAC;gBACnC,WAAW,EAAE,CAAC,OAAO,CAAC;gBACtB,UAAU,EAAE,CAAC,+BAA+B,CAAC;aAC9C;SACF,CAAC;QAEF,YAAY,GAAG;YACb,aAAa,EAAE,sBAAsB;YACrC,WAAW,EAAE,sBAAsB;YACnC,MAAM,EAAE;gBACN;oBACE,SAAS,EAAE,sBAAsB;oBACjC,IAAI,EAAE,oBAAoB;oBAC1B,WAAW,EAAE,6BAA6B;oBAC1C,QAAQ,EAAE,qCAAc,CAAC,IAAI;oBAC7B,UAAU,EAAE,CAAC,eAAe,CAAC;iBAC9B;aACF;YACD,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE;gBACR;oBACE,IAAI,EAAE,UAAU;oBAChB,WAAW,EAAE,sCAAsC;oBACnD,UAAU,EAAE,uCAAe,CAAC,MAAM;oBAClC,IAAI,EAAE,EAAE,UAAU,EAAE,aAAa,EAAE;iBACpC;aACF;SACF,CAAC;QAEF,eAAe,GAAG;YAChB;gBACE,IAAI,EAAE,4CAAoB,CAAC,UAAU;gBACrC,WAAW,EAAE,8BAA8B;gBAC3C,UAAU,EAAE,KAAK;gBACjB,aAAa,EAAE,MAAM;gBACrB,IAAI,EAAE,KAAK;gBACX,YAAY,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC;gBACjC,KAAK,EAAE,CAAC,qBAAqB,EAAE,6BAA6B,CAAC;aAC9D;SACF,CAAC;QAEF,kBAAkB,GAAG;YACnB;gBACE,IAAI,EAAE,+CAAuB,CAAC,IAAI;gBAClC,IAAI,EAAE,sBAAsB;gBAC5B,OAAO,EAAE,kCAAkC;gBAC3C,MAAM,EAAE,MAAM;gBACd,UAAU,EAAE,uCAAe,CAAC,IAAI;gBAChC,iBAAiB,EAAE,KAAK;gBACxB,IAAI,EAAE,CAAC,SAAS,EAAE,eAAe,CAAC;gBAClC,UAAU,EAAE,CAAC,8BAA8B,CAAC;aAC7C;SACF,CAAC;QAEF,eAAe,GAAG;YAChB,GAAG,EAAE,kCAAU,CAAC,KAAK;YACrB,YAAY,EAAE,CAAC,mBAAmB,CAAC;YACnC,gBAAgB,EAAE,CAAC,2CAAmB,CAAC,OAAO,EAAE,2CAAmB,CAAC,MAAM,CAAC;YAC3E,YAAY,EAAE,uBAAuB;YACrC,SAAS,EAAE,0BAA0B;YACrC,OAAO,EAAE,cAAc;SACxB,CAAC;QAEF,kBAAkB,GAAG;YACnB,YAAY,EAAE,EAAE;YAChB,QAAQ,EAAE,EAAE;YACZ,UAAU,EAAE,EAAE;YACd,SAAS,EAAE,EAAE;YACb,YAAY,EAAE,EAAE;YAChB,UAAU,EAAE,sBAAsB;YAClC,MAAM,EAAE,CAAC,6BAA6B,CAAC;SACxC,CAAC;QAEF,cAAc,GAAG;YACf,OAAO,EAAE,OAAO;YAChB,UAAU,EAAE,YAAY;YACxB,QAAQ,EAAE,kBAAkB;YAC5B,kBAAkB,EAAE,mBAAmB;YACvC,iBAAiB,EAAE,+CAAuB,CAAC,CAAC;YAC5C,IAAI,EAAE,qBAAqB;YAC3B,WAAW,EAAE,mEAAmE;YAChF,QAAQ,EAAE,qCAAc,CAAC,IAAI;YAC7B,QAAQ,EAAE,sCAAc,CAAC,GAAG;YAC5B,WAAW,EAAE,sBAAsB;YACnC,IAAI,EAAE,kCAAU,CAAC,QAAQ;YACzB,UAAU,EAAE,uCAAe,CAAC,IAAI;YAChC,MAAM,EAAE,oCAAY,CAAC,MAAM;YAC3B,SAAS,EAAE,sBAAsB;YACjC,QAAQ,EAAE,sBAAsB;YAChC,SAAS,EAAE,sBAAsB;YACjC,UAAU,EAAE,cAAc;YAC1B,UAAU,EAAE,cAAc;YAC1B,WAAW,EAAE,eAAe;YAC5B,aAAa,EAAE,iBAAiB;YAChC,gBAAgB,EAAE,oBAAoB;YACtC,eAAe,EAAE,CAAC,YAAY,EAAE,SAAS,CAAC;YAC1C,eAAe,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;YAC7B,iBAAiB,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC;YACvC,eAAe,EAAE,CAAC,4CAAoB,CAAC,cAAc,EAAE,4CAAoB,CAAC,aAAa,CAAC;YAC1F,QAAQ,EAAE,YAAY;YACtB,WAAW,EAAE,eAAe;YAC5B,cAAc,EAAE,kBAAkB;YAClC,cAAc,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;YAC5C,WAAW,EAAE,eAAe;YAC5B,UAAU,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE;YACnC,IAAI,EAAE,CAAC,KAAK,EAAE,cAAc,EAAE,WAAW,CAAC;YAC1C,cAAc,EAAE,kBAAkB;SACnC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7C,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YAChD,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9C,MAAM,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC,WAAW,EAAE,CAAC;YACxD,MAAM,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC,WAAW,EAAE,CAAC;YACvD,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1C,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YACjD,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9C,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9C,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1C,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YAChD,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAC5C,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAC/C,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9C,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YAChD,MAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE,CAAC;YACtD,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC7C,MAAM,CAAC,OAAO,cAAc,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YAClD,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACjC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAE7C,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YAClD,MAAM,MAAM,GAAe,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAElD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,iBAAiB,GAAG,MAAM,CAAC,MAAM,CAAC,+CAAuB,CAAC,CAAC;YACjE,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACzC,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACzC,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACzC,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACzC,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACzC,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,sCAAc,CAAC,CAAC;YACjD,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,sCAAc,CAAC,OAAO,CAAC,CAAC;YACrD,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,sCAAc,CAAC,QAAQ,CAAC,CAAC;YACtD,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,sCAAc,CAAC,UAAU,CAAC,CAAC;YACxD,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,sCAAc,CAAC,GAAG,CAAC,CAAC;YACjD,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,sCAAc,CAAC,YAAY,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,SAAS,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,2CAAmB,CAAC,UAAU,CAAC,CAAC;YAC5D,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC9C,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,uCAAe,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,6CAAqB,CAAC,MAAM,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,2CAAmB,CAAC,CAAC;YACjD,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,2CAAmB,CAAC,UAAU,CAAC,CAAC;YACxD,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,2CAAmB,CAAC,MAAM,CAAC,CAAC;YACpD,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,2CAAmB,CAAC,SAAS,CAAC,CAAC;YACvD,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,2CAAmB,CAAC,GAAG,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,IAAI,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC7B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,MAAM,GAAG;gBACb,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,GAAG;gBACV,MAAM,EAAE,4BAA4B;gBACpC,QAAQ,EAAE,MAAM;aACjB,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAChD,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,uCAAe,CAAC,YAAY,CAAC,CAAC;YACrE,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,iDAAyB,CAAC,QAAQ,CAAC,CAAC;YAChF,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,uCAAe,CAAC,IAAI,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7D,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,6CAAqB,CAAC,SAAS,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACrD,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,mCAAW,CAAC,GAAG,CAAC,CAAC;YACrD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9D,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,SAAS,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC5C,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACjD,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACjD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;YACjD,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,YAAY,CAAC,QAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,UAAU,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,4CAAoB,CAAC,UAAU,CAAC,CAAC;YAC9D,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,IAAI,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,+CAAuB,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAC/C,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,uCAAe,CAAC,IAAI,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,kCAAU,CAAC,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnE,MAAM,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,2CAAmB,CAAC,OAAO,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,kCAAU,CAAC,CAAC;YAC5C,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,kCAAU,CAAC,GAAG,CAAC,CAAC;YAC5C,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,kCAAU,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,kCAAU,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,kCAAU,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACjD,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC7C,MAAM,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACjD,MAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,aAAa,GAAwB;gBACzC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC;oBAClC,OAAO,EAAE,IAAI;oBACb,MAAM,EAAE,EAAE;oBACV,QAAQ,EAAE,EAAE;oBACZ,aAAa,EAAE,cAAc;oBAC7B,iBAAiB,EAAE,kBAAkB;iBACtC,CAAC;gBACF,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;gBAChD,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,cAAc,CAAC;gBACnD,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC;aAClD,CAAC;YAEF,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7C,MAAM,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;YACpD,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7C,MAAM,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC,WAAW,EAAE,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,cAAc,GAAyB;gBAC3C,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;gBACvC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,cAAc,CAAC;gBACnD,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;gBACvC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,cAAc,CAAC;gBACnD,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACxD,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,cAAc,CAAC;gBACrD,oBAAoB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,CAAC;aAC3D,CAAC;YAEF,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAC5C,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9C,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAC5C,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9C,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9C,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YAChD,MAAM,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC,WAAW,EAAE,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,eAAe,GAA0B;gBAC7C,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,cAAc,CAAC;gBACpD,mBAAmB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC;gBAClD,yBAAyB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBACtE,yBAAyB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;gBACrE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,cAAc,CAAC;aACpD,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAChD,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1D,MAAM,CAAC,eAAe,CAAC,yBAAyB,CAAC,CAAC,WAAW,EAAE,CAAC;YAChE,MAAM,CAAC,eAAe,CAAC,yBAAyB,CAAC,CAAC,WAAW,EAAE,CAAC;YAChE,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,QAAQ,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YAC7C,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACzB,MAAM,aAAa,GAAG,EAAE,GAAG,cAAc,EAAE,OAAO,EAAE,CAAC;gBACrD,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,UAAU,GAAG;gBACjB,GAAG,cAAc;gBACjB,OAAO,EAAE,OAAO;aACjB,CAAC;YAEF,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YACzC,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YAC5C,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,2BAA2B;YAC3B,MAAM,cAAc,GAAG;gBACrB,GAAG,cAAc;gBACjB,UAAU,EAAE;oBACV,GAAG,cAAc,CAAC,UAAU;oBAC5B,OAAO,EAAE,gBAAgB;oBACzB,YAAY,EAAE,KAAK;iBACpB;aACF,CAAC;YAEF,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAC5D,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,YAAY,GAAG;gBACnB,GAAG,cAAc;gBACjB,UAAU,EAAE;oBACV,GAAG,cAAc,CAAC,UAAU;oBAC5B,eAAe,EAAE;wBACf,MAAM,EAAE,QAAQ;wBAChB,OAAO,EAAE,KAAK;wBACd,UAAU,EAAE,CAAC,aAAa,EAAE,UAAU,CAAC;qBACxC;iBACF;aACF,CAAC;YAEF,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,YAAY,GAAG;gBACnB,GAAG,cAAc;gBACjB,UAAU,EAAE;oBACV,GAAG,cAAc,CAAC,UAAU;oBAC5B,kBAAkB,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;oBACrD,oBAAoB,EAAE,sBAAsB;iBAC7C;aACF,CAAC;YAEF,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC;YACrE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,UAAW,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\data\\__tests__\\threat-data.interface.spec.ts"], "sourcesContent": ["import {\r\n  ThreatD<PERSON>,\r\n  ThreatDataValidator,\r\n  ThreatDataSerializer,\r\n  ThreatDataTransformer,\r\n  ThreatSourceReliability,\r\n  ThreatCategory,\r\n  ThreatType,\r\n  ThreatStatus,\r\n  ThreatIndicatorData,\r\n  ThreatIndicatorType,\r\n  ThreatIndicatorStatus,\r\n  ThreatCVSSData,\r\n  ThreatAttributionData,\r\n  ThreatActorType,\r\n  ThreatActorMotivation,\r\n  ThreatActorSophistication,\r\n  ThreatMalwareFamilyData,\r\n  MalwareType,\r\n  ThreatAttackTechniqueData,\r\n  ThreatKillChainPhase,\r\n  ThreatTimelineData,\r\n  ThreatMitigationData,\r\n  ThreatMitigationType,\r\n  ThreatDetectionRuleData,\r\n  ThreatDetectionRuleType,\r\n  ThreatSharingInfo,\r\n  TLPMarking,\r\n  ThreatSharingAction,\r\n  ThreatDataQualityMetrics,\r\n} from '../threat-data.interface';\r\nimport { ThreatSeverity } from '../../../enums/threat-severity.enum';\r\nimport { ConfidenceLevel } from '../../../enums/confidence-level.enum';\r\n\r\ndescribe('ThreatData Interface', () => {\r\n  let mockThreatData: ThreatData;\r\n  let mockIndicators: ThreatIndicatorData[];\r\n  let mockCVSSScores: ThreatCVSSData[];\r\n  let mockAttribution: ThreatAttributionData;\r\n  let mockMalwareFamily: ThreatMalwareFamilyData;\r\n  let mockAttackTechniques: ThreatAttackTechniqueData[];\r\n  let mockTimeline: ThreatTimelineData;\r\n  let mockMitigations: ThreatMitigationData[];\r\n  let mockDetectionRules: ThreatDetectionRuleData[];\r\n  let mockSharingInfo: ThreatSharingInfo;\r\n  let mockQualityMetrics: ThreatDataQualityMetrics;\r\n\r\n  beforeEach(() => {\r\n    mockIndicators = [\r\n      {\r\n        type: ThreatIndicatorType.IP_ADDRESS,\r\n        value: '*************',\r\n        description: 'Malicious IP address',\r\n        confidence: ConfidenceLevel.HIGH,\r\n        firstSeen: '2024-01-01T00:00:00Z',\r\n        lastSeen: '2024-01-02T00:00:00Z',\r\n        status: ThreatIndicatorStatus.ACTIVE,\r\n        tags: ['malware', 'c2'],\r\n        context: { source: 'honeypot' },\r\n      },\r\n      {\r\n        type: ThreatIndicatorType.DOMAIN,\r\n        value: 'malicious.com',\r\n        description: 'Command and control domain',\r\n        confidence: ConfidenceLevel.MEDIUM,\r\n        firstSeen: '2024-01-01T00:00:00Z',\r\n        lastSeen: '2024-01-02T00:00:00Z',\r\n        status: ThreatIndicatorStatus.ACTIVE,\r\n        tags: ['c2', 'apt'],\r\n      },\r\n    ];\r\n\r\n    mockCVSSScores = [\r\n      {\r\n        version: '3.1',\r\n        score: 8.5,\r\n        vector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H',\r\n        severity: 'HIGH',\r\n        exploitabilityScore: 3.9,\r\n        impactScore: 5.9,\r\n      },\r\n    ];\r\n\r\n    mockAttribution = {\r\n      actorName: 'APT29',\r\n      aliases: ['Cozy Bear', 'The Dukes'],\r\n      actorType: ThreatActorType.NATION_STATE,\r\n      country: 'RU',\r\n      motivation: [ThreatActorMotivation.ESPIONAGE],\r\n      sophistication: ThreatActorSophistication.ADVANCED,\r\n      confidence: ConfidenceLevel.HIGH,\r\n      evidence: ['TTPs match known APT29 campaigns'],\r\n    };\r\n\r\n    mockMalwareFamily = {\r\n      name: 'Cobalt Strike',\r\n      aliases: ['CS', 'CobaltStrike'],\r\n      type: MalwareType.RAT,\r\n      platforms: ['Windows', 'Linux'],\r\n      capabilities: ['Remote Access', 'Lateral Movement'],\r\n      variants: ['v4.0', 'v4.1'],\r\n    };\r\n\r\n    mockAttackTechniques = [\r\n      {\r\n        techniqueId: 'T1055',\r\n        name: 'Process Injection',\r\n        tactic: 'Defense Evasion',\r\n        subTechniques: ['T1055.001', 'T1055.002'],\r\n        platforms: ['Windows'],\r\n        dataSources: ['Process monitoring'],\r\n        mitigations: ['M1040'],\r\n        detections: ['Monitor for process injection'],\r\n      },\r\n    ];\r\n\r\n    mockTimeline = {\r\n      campaignStart: '2024-01-01T00:00:00Z',\r\n      campaignEnd: '2024-01-31T23:59:59Z',\r\n      events: [\r\n        {\r\n          timestamp: '2024-01-01T00:00:00Z',\r\n          type: 'initial_compromise',\r\n          description: 'Initial compromise detected',\r\n          severity: ThreatSeverity.HIGH,\r\n          indicators: ['*************'],\r\n        },\r\n      ],\r\n      duration: 31,\r\n      patterns: [\r\n        {\r\n          type: 'temporal',\r\n          description: 'Activity peaks during business hours',\r\n          confidence: ConfidenceLevel.MEDIUM,\r\n          data: { peak_hours: '09:00-17:00' },\r\n        },\r\n      ],\r\n    };\r\n\r\n    mockMitigations = [\r\n      {\r\n        type: ThreatMitigationType.PREVENTIVE,\r\n        description: 'Block malicious IP addresses',\r\n        difficulty: 'low',\r\n        effectiveness: 'high',\r\n        cost: 'low',\r\n        technologies: ['Firewall', 'IPS'],\r\n        steps: ['Add IP to blocklist', 'Monitor for bypass attempts'],\r\n      },\r\n    ];\r\n\r\n    mockDetectionRules = [\r\n      {\r\n        type: ThreatDetectionRuleType.YARA,\r\n        name: 'Cobalt Strike Beacon',\r\n        content: 'rule CobaltStrike_Beacon { ... }',\r\n        format: 'yara',\r\n        confidence: ConfidenceLevel.HIGH,\r\n        falsePositiveRate: 'low',\r\n        tags: ['malware', 'cobalt-strike'],\r\n        references: ['https://example.com/analysis'],\r\n      },\r\n    ];\r\n\r\n    mockSharingInfo = {\r\n      tlp: TLPMarking.AMBER,\r\n      restrictions: ['No public sharing'],\r\n      permittedActions: [ThreatSharingAction.ANALYZE, ThreatSharingAction.DETECT],\r\n      agreementRef: 'sharing-agreement-001',\r\n      copyright: '© 2024 Threat Intel Corp',\r\n      license: 'CC BY-SA 4.0',\r\n    };\r\n\r\n    mockQualityMetrics = {\r\n      completeness: 85,\r\n      accuracy: 90,\r\n      timeliness: 95,\r\n      relevance: 80,\r\n      overallScore: 87,\r\n      assessedAt: '2024-01-01T00:00:00Z',\r\n      issues: ['Missing attribution details'],\r\n    };\r\n\r\n    mockThreatData = {\r\n      version: '1.0.0',\r\n      externalId: 'threat-123',\r\n      sourceId: 'intel-source-001',\r\n      sourceOrganization: 'Threat Intel Corp',\r\n      sourceReliability: ThreatSourceReliability.A,\r\n      name: 'APT29 Campaign 2024',\r\n      description: 'Advanced persistent threat campaign targeting government entities',\r\n      severity: ThreatSeverity.HIGH,\r\n      category: ThreatCategory.APT,\r\n      subcategory: 'Government Targeting',\r\n      type: ThreatType.CAMPAIGN,\r\n      confidence: ConfidenceLevel.HIGH,\r\n      status: ThreatStatus.ACTIVE,\r\n      firstSeen: '2024-01-01T00:00:00Z',\r\n      lastSeen: '2024-01-31T23:59:59Z',\r\n      expiresAt: '2024-12-31T23:59:59Z',\r\n      indicators: mockIndicators,\r\n      cvssScores: mockCVSSScores,\r\n      attribution: mockAttribution,\r\n      malwareFamily: mockMalwareFamily,\r\n      attackTechniques: mockAttackTechniques,\r\n      targetedSectors: ['Government', 'Defense'],\r\n      targetedRegions: ['US', 'EU'],\r\n      affectedPlatforms: ['Windows', 'Linux'],\r\n      killChainPhases: [ThreatKillChainPhase.RECONNAISSANCE, ThreatKillChainPhase.WEAPONIZATION],\r\n      timeline: mockTimeline,\r\n      mitigations: mockMitigations,\r\n      detectionRules: mockDetectionRules,\r\n      relatedThreats: ['threat-456', 'threat-789'],\r\n      sharingInfo: mockSharingInfo,\r\n      attributes: { custom: 'attribute' },\r\n      tags: ['apt', 'nation-state', 'espionage'],\r\n      qualityMetrics: mockQualityMetrics,\r\n    };\r\n  });\r\n\r\n  describe('ThreatData Structure', () => {\r\n    it('should have all required fields', () => {\r\n      expect(mockThreatData.version).toBeDefined();\r\n      expect(mockThreatData.externalId).toBeDefined();\r\n      expect(mockThreatData.sourceId).toBeDefined();\r\n      expect(mockThreatData.sourceOrganization).toBeDefined();\r\n      expect(mockThreatData.sourceReliability).toBeDefined();\r\n      expect(mockThreatData.name).toBeDefined();\r\n      expect(mockThreatData.description).toBeDefined();\r\n      expect(mockThreatData.severity).toBeDefined();\r\n      expect(mockThreatData.category).toBeDefined();\r\n      expect(mockThreatData.type).toBeDefined();\r\n      expect(mockThreatData.confidence).toBeDefined();\r\n      expect(mockThreatData.status).toBeDefined();\r\n      expect(mockThreatData.firstSeen).toBeDefined();\r\n      expect(mockThreatData.lastSeen).toBeDefined();\r\n      expect(mockThreatData.indicators).toBeDefined();\r\n      expect(mockThreatData.attackTechniques).toBeDefined();\r\n      expect(mockThreatData.sharingInfo).toBeDefined();\r\n    });\r\n\r\n    it('should support versioning', () => {\r\n      expect(mockThreatData.version).toBe('1.0.0');\r\n      expect(typeof mockThreatData.version).toBe('string');\r\n    });\r\n\r\n    it('should be serializable to JSON', () => {\r\n      const jsonString = JSON.stringify(mockThreatData);\r\n      expect(jsonString).toBeDefined();\r\n      expect(jsonString.length).toBeGreaterThan(0);\r\n\r\n      const parsed = JSON.parse(jsonString);\r\n      expect(parsed.version).toBe(mockThreatData.version);\r\n      expect(parsed.externalId).toBe(mockThreatData.externalId);\r\n    });\r\n\r\n    it('should maintain data integrity after serialization', () => {\r\n      const jsonString = JSON.stringify(mockThreatData);\r\n      const parsed: ThreatData = JSON.parse(jsonString);\r\n\r\n      expect(parsed.version).toBe(mockThreatData.version);\r\n      expect(parsed.externalId).toBe(mockThreatData.externalId);\r\n      expect(parsed.name).toBe(mockThreatData.name);\r\n      expect(parsed.severity).toBe(mockThreatData.severity);\r\n      expect(parsed.category).toBe(mockThreatData.category);\r\n      expect(parsed.indicators.length).toBe(mockThreatData.indicators.length);\r\n    });\r\n  });\r\n\r\n  describe('ThreatSourceReliability Enum', () => {\r\n    it('should contain all reliability levels', () => {\r\n      const reliabilityLevels = Object.values(ThreatSourceReliability);\r\n      expect(reliabilityLevels).toContain('A');\r\n      expect(reliabilityLevels).toContain('B');\r\n      expect(reliabilityLevels).toContain('C');\r\n      expect(reliabilityLevels).toContain('D');\r\n      expect(reliabilityLevels).toContain('E');\r\n      expect(reliabilityLevels).toContain('F');\r\n    });\r\n  });\r\n\r\n  describe('ThreatCategory Enum', () => {\r\n    it('should contain comprehensive threat categories', () => {\r\n      const categories = Object.values(ThreatCategory);\r\n      expect(categories).toContain(ThreatCategory.MALWARE);\r\n      expect(categories).toContain(ThreatCategory.PHISHING);\r\n      expect(categories).toContain(ThreatCategory.RANSOMWARE);\r\n      expect(categories).toContain(ThreatCategory.APT);\r\n      expect(categories).toContain(ThreatCategory.NATION_STATE);\r\n    });\r\n  });\r\n\r\n  describe('ThreatIndicatorData', () => {\r\n    it('should contain indicator information', () => {\r\n      const indicator = mockIndicators[0];\r\n      expect(indicator.type).toBe(ThreatIndicatorType.IP_ADDRESS);\r\n      expect(indicator.value).toBe('*************');\r\n      expect(indicator.confidence).toBe(ConfidenceLevel.HIGH);\r\n      expect(indicator.status).toBe(ThreatIndicatorStatus.ACTIVE);\r\n    });\r\n\r\n    it('should support multiple indicator types', () => {\r\n      const types = Object.values(ThreatIndicatorType);\r\n      expect(types).toContain(ThreatIndicatorType.IP_ADDRESS);\r\n      expect(types).toContain(ThreatIndicatorType.DOMAIN);\r\n      expect(types).toContain(ThreatIndicatorType.FILE_HASH);\r\n      expect(types).toContain(ThreatIndicatorType.URL);\r\n    });\r\n  });\r\n\r\n  describe('ThreatCVSSData', () => {\r\n    it('should contain CVSS scoring information', () => {\r\n      const cvss = mockCVSSScores[0];\r\n      expect(cvss.version).toBe('3.1');\r\n      expect(cvss.score).toBe(8.5);\r\n      expect(cvss.vector).toContain('CVSS:3.1');\r\n      expect(cvss.severity).toBe('HIGH');\r\n    });\r\n\r\n    it('should support different CVSS versions', () => {\r\n      const cvssV2 = {\r\n        version: '2.0',\r\n        score: 7.5,\r\n        vector: 'AV:N/AC:L/Au:N/C:P/I:P/A:P',\r\n        severity: 'HIGH',\r\n      };\r\n\r\n      expect(cvssV2.version).toBe('2.0');\r\n      expect(cvssV2.score).toBeGreaterThan(0);\r\n    });\r\n  });\r\n\r\n  describe('ThreatAttributionData', () => {\r\n    it('should contain attribution information', () => {\r\n      expect(mockAttribution.actorName).toBe('APT29');\r\n      expect(mockAttribution.actorType).toBe(ThreatActorType.NATION_STATE);\r\n      expect(mockAttribution.sophistication).toBe(ThreatActorSophistication.ADVANCED);\r\n      expect(mockAttribution.confidence).toBe(ConfidenceLevel.HIGH);\r\n    });\r\n\r\n    it('should support multiple motivations', () => {\r\n      expect(Array.isArray(mockAttribution.motivation)).toBe(true);\r\n      expect(mockAttribution.motivation).toContain(ThreatActorMotivation.ESPIONAGE);\r\n    });\r\n  });\r\n\r\n  describe('ThreatMalwareFamilyData', () => {\r\n    it('should contain malware family information', () => {\r\n      expect(mockMalwareFamily.name).toBe('Cobalt Strike');\r\n      expect(mockMalwareFamily.type).toBe(MalwareType.RAT);\r\n      expect(Array.isArray(mockMalwareFamily.platforms)).toBe(true);\r\n      expect(Array.isArray(mockMalwareFamily.capabilities)).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('ThreatAttackTechniqueData', () => {\r\n    it('should contain MITRE ATT&CK information', () => {\r\n      const technique = mockAttackTechniques[0];\r\n      expect(technique.techniqueId).toBe('T1055');\r\n      expect(technique.name).toBe('Process Injection');\r\n      expect(technique.tactic).toBe('Defense Evasion');\r\n      expect(Array.isArray(technique.subTechniques)).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('ThreatTimelineData', () => {\r\n    it('should contain timeline information', () => {\r\n      expect(mockTimeline.campaignStart).toBeDefined();\r\n      expect(mockTimeline.campaignEnd).toBeDefined();\r\n      expect(Array.isArray(mockTimeline.events)).toBe(true);\r\n      expect(mockTimeline.duration).toBe(31);\r\n    });\r\n\r\n    it('should support activity patterns', () => {\r\n      expect(Array.isArray(mockTimeline.patterns)).toBe(true);\r\n      expect(mockTimeline.patterns![0].type).toBe('temporal');\r\n    });\r\n  });\r\n\r\n  describe('ThreatMitigationData', () => {\r\n    it('should contain mitigation information', () => {\r\n      const mitigation = mockMitigations[0];\r\n      expect(mitigation.type).toBe(ThreatMitigationType.PREVENTIVE);\r\n      expect(mitigation.effectiveness).toBe('high');\r\n      expect(Array.isArray(mitigation.technologies)).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('ThreatDetectionRuleData', () => {\r\n    it('should contain detection rule information', () => {\r\n      const rule = mockDetectionRules[0];\r\n      expect(rule.type).toBe(ThreatDetectionRuleType.YARA);\r\n      expect(rule.name).toBe('Cobalt Strike Beacon');\r\n      expect(rule.confidence).toBe(ConfidenceLevel.HIGH);\r\n    });\r\n  });\r\n\r\n  describe('ThreatSharingInfo', () => {\r\n    it('should contain sharing restrictions', () => {\r\n      expect(mockSharingInfo.tlp).toBe(TLPMarking.AMBER);\r\n      expect(Array.isArray(mockSharingInfo.permittedActions)).toBe(true);\r\n      expect(mockSharingInfo.permittedActions).toContain(ThreatSharingAction.ANALYZE);\r\n    });\r\n\r\n    it('should support TLP markings', () => {\r\n      const tlpLevels = Object.values(TLPMarking);\r\n      expect(tlpLevels).toContain(TLPMarking.RED);\r\n      expect(tlpLevels).toContain(TLPMarking.AMBER);\r\n      expect(tlpLevels).toContain(TLPMarking.GREEN);\r\n      expect(tlpLevels).toContain(TLPMarking.WHITE);\r\n    });\r\n  });\r\n\r\n  describe('ThreatDataQualityMetrics', () => {\r\n    it('should contain quality assessment', () => {\r\n      expect(mockQualityMetrics.completeness).toBe(85);\r\n      expect(mockQualityMetrics.accuracy).toBe(90);\r\n      expect(mockQualityMetrics.overallScore).toBe(87);\r\n      expect(mockQualityMetrics.assessedAt).toBeDefined();\r\n    });\r\n\r\n    it('should support quality issues tracking', () => {\r\n      expect(Array.isArray(mockQualityMetrics.issues)).toBe(true);\r\n      expect(mockQualityMetrics.issues).toContain('Missing attribution details');\r\n    });\r\n  });\r\n\r\n  describe('Data Validation Interface', () => {\r\n    it('should define validation methods', () => {\r\n      const mockValidator: ThreatDataValidator = {\r\n        validate: jest.fn().mockReturnValue({\r\n          isValid: true,\r\n          errors: [],\r\n          warnings: [],\r\n          sanitizedData: mockThreatData,\r\n          qualityAssessment: mockQualityMetrics,\r\n        }),\r\n        validateVersion: jest.fn().mockReturnValue(true),\r\n        sanitize: jest.fn().mockReturnValue(mockThreatData),\r\n        validateIndicators: jest.fn().mockReturnValue([]),\r\n      };\r\n\r\n      expect(mockValidator.validate).toBeDefined();\r\n      expect(mockValidator.validateVersion).toBeDefined();\r\n      expect(mockValidator.sanitize).toBeDefined();\r\n      expect(mockValidator.validateIndicators).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('Data Serialization Interface', () => {\r\n    it('should define serialization methods', () => {\r\n      const mockSerializer: ThreatDataSerializer = {\r\n        toJson: jest.fn().mockReturnValue('{}'),\r\n        fromJson: jest.fn().mockReturnValue(mockThreatData),\r\n        toStix: jest.fn().mockReturnValue('{}'),\r\n        fromStix: jest.fn().mockReturnValue(mockThreatData),\r\n        toBinary: jest.fn().mockReturnValue(Buffer.from('test')),\r\n        fromBinary: jest.fn().mockReturnValue(mockThreatData),\r\n        getSupportedVersions: jest.fn().mockReturnValue(['1.0.0']),\r\n      };\r\n\r\n      expect(mockSerializer.toJson).toBeDefined();\r\n      expect(mockSerializer.fromJson).toBeDefined();\r\n      expect(mockSerializer.toStix).toBeDefined();\r\n      expect(mockSerializer.fromStix).toBeDefined();\r\n      expect(mockSerializer.toBinary).toBeDefined();\r\n      expect(mockSerializer.fromBinary).toBeDefined();\r\n      expect(mockSerializer.getSupportedVersions).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('Data Transformation Interface', () => {\r\n    it('should define transformation methods', () => {\r\n      const mockTransformer: ThreatDataTransformer = {\r\n        transform: jest.fn().mockReturnValue(mockThreatData),\r\n        transformToExternal: jest.fn().mockReturnValue({}),\r\n        getSupportedSourceFormats: jest.fn().mockReturnValue(['stix', 'json']),\r\n        getSupportedTargetFormats: jest.fn().mockReturnValue(['json', 'xml']),\r\n        enrich: jest.fn().mockResolvedValue(mockThreatData),\r\n      };\r\n\r\n      expect(mockTransformer.transform).toBeDefined();\r\n      expect(mockTransformer.transformToExternal).toBeDefined();\r\n      expect(mockTransformer.getSupportedSourceFormats).toBeDefined();\r\n      expect(mockTransformer.getSupportedTargetFormats).toBeDefined();\r\n      expect(mockTransformer.enrich).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('Version Compatibility', () => {\r\n    it('should support version checking', () => {\r\n      const versions = ['1.0.0', '1.1.0', '2.0.0'];\r\n      versions.forEach(version => {\r\n        const versionedData = { ...mockThreatData, version };\r\n        expect(versionedData.version).toBe(version);\r\n      });\r\n    });\r\n\r\n    it('should maintain backward compatibility', () => {\r\n      const legacyData = {\r\n        ...mockThreatData,\r\n        version: '0.9.0',\r\n      };\r\n\r\n      expect(legacyData.version).toBeDefined();\r\n      expect(legacyData.externalId).toBeDefined();\r\n      expect(legacyData.name).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('External Integration Support', () => {\r\n    it('should support STIX format compatibility', () => {\r\n      // Mock STIX transformation\r\n      const stixCompatible = {\r\n        ...mockThreatData,\r\n        attributes: {\r\n          ...mockThreatData.attributes,\r\n          stix_id: 'malware--12345',\r\n          stix_version: '2.1',\r\n        },\r\n      };\r\n\r\n      expect(stixCompatible.attributes).toHaveProperty('stix_id');\r\n      expect(stixCompatible.attributes).toHaveProperty('stix_version');\r\n    });\r\n\r\n    it('should support custom threat intelligence formats', () => {\r\n      const customFormat = {\r\n        ...mockThreatData,\r\n        attributes: {\r\n          ...mockThreatData.attributes,\r\n          vendor_specific: {\r\n            format: 'custom',\r\n            version: '1.0',\r\n            extensions: ['attribution', 'timeline'],\r\n          },\r\n        },\r\n      };\r\n\r\n      expect(customFormat.attributes).toHaveProperty('vendor_specific');\r\n    });\r\n\r\n    it('should support enrichment from multiple sources', () => {\r\n      const enrichedData = {\r\n        ...mockThreatData,\r\n        attributes: {\r\n          ...mockThreatData.attributes,\r\n          enrichment_sources: ['source1', 'source2', 'source3'],\r\n          enrichment_timestamp: '2024-01-01T00:00:00Z',\r\n        },\r\n      };\r\n\r\n      expect(enrichedData.attributes).toHaveProperty('enrichment_sources');\r\n      expect(Array.isArray(enrichedData.attributes!.enrichment_sources)).toBe(true);\r\n    });\r\n  });\r\n});"], "version": 3}