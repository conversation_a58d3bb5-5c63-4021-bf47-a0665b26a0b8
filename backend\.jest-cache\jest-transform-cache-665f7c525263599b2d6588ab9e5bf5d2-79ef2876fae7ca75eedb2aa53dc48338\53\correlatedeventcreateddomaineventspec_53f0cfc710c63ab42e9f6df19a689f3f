89cdb3d0a3fffd88c4e233c52e6dd8c8
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const correlated_event_created_domain_event_1 = require("../correlated-event-created.domain-event");
const shared_kernel_1 = require("../../../../../shared-kernel");
const event_type_enum_1 = require("../../enums/event-type.enum");
const event_severity_enum_1 = require("../../enums/event-severity.enum");
const correlation_status_enum_1 = require("../../enums/correlation-status.enum");
const confidence_level_enum_1 = require("../../enums/confidence-level.enum");
describe('CorrelatedEventCreatedDomainEvent', () => {
    let eventData;
    let aggregateId;
    beforeEach(() => {
        aggregateId = shared_kernel_1.UniqueEntityId.create();
        eventData = {
            enrichedEventId: shared_kernel_1.UniqueEntityId.create(),
            eventType: event_type_enum_1.EventType.SECURITY_ALERT,
            severity: event_severity_enum_1.EventSeverity.HIGH,
            correlationStatus: correlation_status_enum_1.CorrelationStatus.COMPLETED,
            correlationQualityScore: 85,
            appliedRulesCount: 3,
            correlationMatchesCount: 5,
            relatedEventsCount: 7,
            confidenceLevel: confidence_level_enum_1.ConfidenceLevel.HIGH,
            hasAttackChain: true,
            requiresManualReview: false
        };
    });
    describe('creation', () => {
        it('should create domain event with required data', () => {
            const domainEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, eventData);
            expect(domainEvent.aggregateId).toEqual(aggregateId);
            expect(domainEvent.eventData).toEqual(eventData);
            expect(domainEvent.eventName).toBe('CorrelatedEventCreatedDomainEvent');
            expect(domainEvent.occurredOn).toBeInstanceOf(Date);
            expect(domainEvent.eventId).toBeDefined();
        });
        it('should create domain event with custom options', () => {
            const customEventId = shared_kernel_1.UniqueEntityId.create();
            const customOccurredOn = new Date('2023-01-01T00:00:00Z');
            const correlationId = 'corr_123';
            const domainEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, eventData, {
                eventId: customEventId,
                occurredOn: customOccurredOn,
                correlationId,
                eventVersion: 2,
                metadata: { custom: 'data' }
            });
            expect(domainEvent.eventId).toEqual(customEventId);
            expect(domainEvent.occurredOn).toEqual(customOccurredOn);
            expect(domainEvent.correlationId).toBe(correlationId);
            expect(domainEvent.eventVersion).toBe(2);
            expect(domainEvent.metadata).toEqual({ custom: 'data' });
        });
    });
    describe('property getters', () => {
        let domainEvent;
        beforeEach(() => {
            domainEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, eventData);
        });
        it('should return enriched event ID', () => {
            expect(domainEvent.enrichedEventId).toEqual(eventData.enrichedEventId);
        });
        it('should return event type', () => {
            expect(domainEvent.eventType).toBe(eventData.eventType);
        });
        it('should return severity', () => {
            expect(domainEvent.severity).toBe(eventData.severity);
        });
        it('should return correlation status', () => {
            expect(domainEvent.correlationStatus).toBe(eventData.correlationStatus);
        });
        it('should return correlation quality score', () => {
            expect(domainEvent.correlationQualityScore).toBe(eventData.correlationQualityScore);
        });
        it('should return applied rules count', () => {
            expect(domainEvent.appliedRulesCount).toBe(eventData.appliedRulesCount);
        });
        it('should return correlation matches count', () => {
            expect(domainEvent.correlationMatchesCount).toBe(eventData.correlationMatchesCount);
        });
        it('should return related events count', () => {
            expect(domainEvent.relatedEventsCount).toBe(eventData.relatedEventsCount);
        });
        it('should return confidence level', () => {
            expect(domainEvent.confidenceLevel).toBe(eventData.confidenceLevel);
        });
        it('should return has attack chain flag', () => {
            expect(domainEvent.hasAttackChain).toBe(eventData.hasAttackChain);
        });
        it('should return requires manual review flag', () => {
            expect(domainEvent.requiresManualReview).toBe(eventData.requiresManualReview);
        });
    });
    describe('business logic methods', () => {
        let domainEvent;
        beforeEach(() => {
            domainEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, eventData);
        });
        it('should identify high severity events', () => {
            const highSeverityEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                severity: event_severity_enum_1.EventSeverity.HIGH
            });
            const criticalSeverityEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                severity: event_severity_enum_1.EventSeverity.CRITICAL
            });
            const mediumSeverityEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                severity: event_severity_enum_1.EventSeverity.MEDIUM
            });
            expect(highSeverityEvent.isHighSeverity()).toBe(true);
            expect(criticalSeverityEvent.isHighSeverity()).toBe(true);
            expect(mediumSeverityEvent.isHighSeverity()).toBe(false);
        });
        it('should identify critical events', () => {
            const criticalEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                severity: event_severity_enum_1.EventSeverity.CRITICAL
            });
            const highEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                severity: event_severity_enum_1.EventSeverity.HIGH
            });
            expect(criticalEvent.isCritical()).toBe(true);
            expect(highEvent.isCritical()).toBe(false);
        });
        it('should identify high correlation quality', () => {
            const highQualityEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                correlationQualityScore: 85
            });
            const lowQualityEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                correlationQualityScore: 50
            });
            const noQualityEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                correlationQualityScore: undefined
            });
            expect(highQualityEvent.hasHighCorrelationQuality()).toBe(true);
            expect(lowQualityEvent.hasHighCorrelationQuality()).toBe(false);
            expect(noQualityEvent.hasHighCorrelationQuality()).toBe(false);
        });
        it('should identify completed correlation', () => {
            const completedEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                correlationStatus: correlation_status_enum_1.CorrelationStatus.COMPLETED
            });
            const pendingEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                correlationStatus: correlation_status_enum_1.CorrelationStatus.PENDING
            });
            expect(completedEvent.isCorrelationCompleted()).toBe(true);
            expect(pendingEvent.isCorrelationCompleted()).toBe(false);
        });
        it('should identify high confidence correlation', () => {
            const highConfidenceEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                confidenceLevel: confidence_level_enum_1.ConfidenceLevel.HIGH
            });
            const veryHighConfidenceEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                confidenceLevel: confidence_level_enum_1.ConfidenceLevel.VERY_HIGH
            });
            const confirmedEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                confidenceLevel: confidence_level_enum_1.ConfidenceLevel.CONFIRMED
            });
            const mediumConfidenceEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                confidenceLevel: confidence_level_enum_1.ConfidenceLevel.MEDIUM
            });
            expect(highConfidenceEvent.isHighConfidenceCorrelation()).toBe(true);
            expect(veryHighConfidenceEvent.isHighConfidenceCorrelation()).toBe(true);
            expect(confirmedEvent.isHighConfidenceCorrelation()).toBe(true);
            expect(mediumConfidenceEvent.isHighConfidenceCorrelation()).toBe(false);
        });
        it('should identify events with multiple related events', () => {
            const multipleRelatedEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                relatedEventsCount: 5
            });
            const singleRelatedEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                relatedEventsCount: 1
            });
            const noRelatedEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                relatedEventsCount: 0
            });
            expect(multipleRelatedEvent.hasMultipleRelatedEvents()).toBe(true);
            expect(singleRelatedEvent.hasMultipleRelatedEvents()).toBe(false);
            expect(noRelatedEvent.hasMultipleRelatedEvents()).toBe(false);
        });
        it('should identify events with correlation matches', () => {
            const withMatchesEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                correlationMatchesCount: 3
            });
            const noMatchesEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                correlationMatchesCount: 0
            });
            expect(withMatchesEvent.hasCorrelationMatches()).toBe(true);
            expect(noMatchesEvent.hasCorrelationMatches()).toBe(false);
        });
        it('should identify potential attack campaigns', () => {
            const attackCampaignEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                hasAttackChain: true,
                relatedEventsCount: 5,
                confidenceLevel: confidence_level_enum_1.ConfidenceLevel.HIGH
            });
            const nonCampaignEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                hasAttackChain: false,
                relatedEventsCount: 1,
                confidenceLevel: confidence_level_enum_1.ConfidenceLevel.LOW
            });
            expect(attackCampaignEvent.isPotentialAttackCampaign()).toBe(true);
            expect(nonCampaignEvent.isPotentialAttackCampaign()).toBe(false);
        });
    });
    describe('priority and alert level calculation', () => {
        it('should calculate critical priority for attack campaigns', () => {
            const criticalEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                severity: event_severity_enum_1.EventSeverity.CRITICAL,
                hasAttackChain: true,
                relatedEventsCount: 5,
                confidenceLevel: confidence_level_enum_1.ConfidenceLevel.HIGH
            });
            expect(criticalEvent.getEventPriority()).toBe('critical');
        });
        it('should calculate high priority for high severity with attack chain', () => {
            const highPriorityEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                severity: event_severity_enum_1.EventSeverity.HIGH,
                hasAttackChain: true,
                relatedEventsCount: 2,
                confidenceLevel: confidence_level_enum_1.ConfidenceLevel.MEDIUM
            });
            expect(highPriorityEvent.getEventPriority()).toBe('high');
        });
        it('should calculate medium priority for quality correlation with matches', () => {
            const mediumPriorityEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                severity: event_severity_enum_1.EventSeverity.MEDIUM,
                hasAttackChain: false,
                correlationQualityScore: 85,
                correlationMatchesCount: 3
            });
            expect(mediumPriorityEvent.getEventPriority()).toBe('medium');
        });
        it('should calculate low priority for basic events', () => {
            const lowPriorityEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                severity: event_severity_enum_1.EventSeverity.LOW,
                hasAttackChain: false,
                correlationQualityScore: 50,
                correlationMatchesCount: 0
            });
            expect(lowPriorityEvent.getEventPriority()).toBe('low');
        });
        it('should calculate alert levels correctly', () => {
            const criticalAlert = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                severity: event_severity_enum_1.EventSeverity.CRITICAL,
                hasAttackChain: true,
                relatedEventsCount: 5,
                confidenceLevel: confidence_level_enum_1.ConfidenceLevel.HIGH
            });
            const alertLevel = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                severity: event_severity_enum_1.EventSeverity.HIGH,
                hasAttackChain: true
            });
            const warningLevel = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                severity: event_severity_enum_1.EventSeverity.MEDIUM,
                hasAttackChain: false,
                confidenceLevel: confidence_level_enum_1.ConfidenceLevel.HIGH
            });
            const infoLevel = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                severity: event_severity_enum_1.EventSeverity.LOW,
                hasAttackChain: false,
                confidenceLevel: confidence_level_enum_1.ConfidenceLevel.LOW
            });
            expect(criticalAlert.getAlertLevel()).toBe('critical');
            expect(alertLevel.getAlertLevel()).toBe('alert');
            expect(warningLevel.getAlertLevel()).toBe('warning');
            expect(infoLevel.getAlertLevel()).toBe('info');
        });
    });
    describe('escalation requirements', () => {
        it('should require management escalation for attack campaigns', () => {
            const campaignEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                severity: event_severity_enum_1.EventSeverity.CRITICAL,
                hasAttackChain: true,
                relatedEventsCount: 5,
                confidenceLevel: confidence_level_enum_1.ConfidenceLevel.HIGH
            });
            const escalation = campaignEvent.getEscalationRequirements();
            expect(escalation.shouldEscalate).toBe(true);
            expect(escalation.escalationLevel).toBe('management');
            expect(escalation.timeoutMinutes).toBe(15);
            expect(escalation.reason).toBe('Potential coordinated attack campaign detected');
        });
        it('should require tier3 escalation for critical events with attack chains', () => {
            const criticalEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                severity: event_severity_enum_1.EventSeverity.CRITICAL,
                hasAttackChain: true,
                relatedEventsCount: 2,
                confidenceLevel: confidence_level_enum_1.ConfidenceLevel.MEDIUM
            });
            const escalation = criticalEvent.getEscalationRequirements();
            expect(escalation.shouldEscalate).toBe(true);
            expect(escalation.escalationLevel).toBe('tier3');
            expect(escalation.timeoutMinutes).toBe(30);
            expect(escalation.reason).toBe('Critical severity event with attack chain identified');
        });
        it('should require tier2 escalation for high severity with high confidence', () => {
            const highSeverityEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                severity: event_severity_enum_1.EventSeverity.HIGH,
                hasAttackChain: false,
                confidenceLevel: confidence_level_enum_1.ConfidenceLevel.HIGH
            });
            const escalation = highSeverityEvent.getEscalationRequirements();
            expect(escalation.shouldEscalate).toBe(true);
            expect(escalation.escalationLevel).toBe('tier2');
            expect(escalation.timeoutMinutes).toBe(60);
            expect(escalation.reason).toBe('High severity event with high confidence correlation');
        });
        it('should require tier1 escalation for quality correlation with matches', () => {
            const qualityEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                severity: event_severity_enum_1.EventSeverity.MEDIUM,
                hasAttackChain: false,
                correlationQualityScore: 85,
                correlationMatchesCount: 3,
                confidenceLevel: confidence_level_enum_1.ConfidenceLevel.MEDIUM
            });
            const escalation = qualityEvent.getEscalationRequirements();
            expect(escalation.shouldEscalate).toBe(true);
            expect(escalation.escalationLevel).toBe('tier1');
            expect(escalation.timeoutMinutes).toBe(120);
            expect(escalation.reason).toBe('Quality correlation with multiple matches found');
        });
        it('should not require escalation for standard events', () => {
            const standardEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                severity: event_severity_enum_1.EventSeverity.LOW,
                hasAttackChain: false,
                correlationQualityScore: 50,
                correlationMatchesCount: 0,
                confidenceLevel: confidence_level_enum_1.ConfidenceLevel.LOW
            });
            const escalation = standardEvent.getEscalationRequirements();
            expect(escalation.shouldEscalate).toBe(false);
            expect(escalation.escalationLevel).toBe('tier1');
            expect(escalation.timeoutMinutes).toBe(240);
            expect(escalation.reason).toBe('Standard correlation processing');
        });
    });
    describe('recommended actions', () => {
        it('should recommend incident response for attack campaigns', () => {
            const campaignEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                severity: event_severity_enum_1.EventSeverity.CRITICAL,
                hasAttackChain: true,
                relatedEventsCount: 5,
                confidenceLevel: confidence_level_enum_1.ConfidenceLevel.HIGH
            });
            const actions = campaignEvent.getRecommendedActions();
            expect(actions).toContain('Initiate incident response procedures');
            expect(actions).toContain('Notify security operations center');
            expect(actions).toContain('Activate threat hunting team');
        });
        it('should recommend attack chain analysis for events with attack chains', () => {
            const attackChainEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                hasAttackChain: true
            });
            const actions = attackChainEvent.getRecommendedActions();
            expect(actions).toContain('Analyze attack chain progression');
            expect(actions).toContain('Identify attack vectors and techniques');
            expect(actions).toContain('Assess potential impact and scope');
        });
        it('should recommend validation for high confidence correlations', () => {
            const highConfidenceEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                confidenceLevel: confidence_level_enum_1.ConfidenceLevel.HIGH
            });
            const actions = highConfidenceEvent.getRecommendedActions();
            expect(actions).toContain('Validate correlation findings');
            expect(actions).toContain('Gather additional evidence');
            expect(actions).toContain('Prepare containment measures');
        });
        it('should recommend manual review scheduling', () => {
            const reviewEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                requiresManualReview: true
            });
            const actions = reviewEvent.getRecommendedActions();
            expect(actions).toContain('Schedule manual review');
            expect(actions).toContain('Assign to security analyst');
            expect(actions).toContain('Prioritize based on severity and confidence');
        });
        it('should recommend related event investigation', () => {
            const multipleRelatedEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, {
                ...eventData,
                relatedEventsCount: 5
            });
            const actions = multipleRelatedEvent.getRecommendedActions();
            expect(actions).toContain('Investigate related events');
            expect(actions).toContain('Look for additional correlations');
            expect(actions).toContain('Map event relationships');
        });
    });
    describe('event summary', () => {
        it('should provide comprehensive event summary', () => {
            const domainEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, eventData);
            const summary = domainEvent.getEventSummary();
            expect(summary.correlatedEventId).toBe(aggregateId.toString());
            expect(summary.enrichedEventId).toBe(eventData.enrichedEventId.toString());
            expect(summary.eventType).toBe(eventData.eventType);
            expect(summary.severity).toBe(eventData.severity);
            expect(summary.correlationStatus).toBe(eventData.correlationStatus);
            expect(summary.correlationQualityScore).toBe(eventData.correlationQualityScore);
            expect(summary.appliedRulesCount).toBe(eventData.appliedRulesCount);
            expect(summary.correlationMatchesCount).toBe(eventData.correlationMatchesCount);
            expect(summary.relatedEventsCount).toBe(eventData.relatedEventsCount);
            expect(summary.confidenceLevel).toBe(eventData.confidenceLevel);
            expect(summary.hasAttackChain).toBe(eventData.hasAttackChain);
            expect(summary.requiresManualReview).toBe(eventData.requiresManualReview);
            expect(summary.isHighSeverity).toBe(true);
            expect(summary.isCritical).toBe(false);
            expect(summary.hasHighCorrelationQuality).toBe(true);
            expect(summary.isCorrelationCompleted).toBe(true);
            expect(summary.isHighConfidenceCorrelation).toBe(true);
            expect(summary.hasMultipleRelatedEvents).toBe(true);
            expect(summary.hasCorrelationMatches).toBe(true);
            expect(summary.isPotentialAttackCampaign).toBe(true);
            expect(summary.eventPriority).toBeDefined();
            expect(summary.alertLevel).toBeDefined();
            expect(summary.recommendedActions).toBeInstanceOf(Array);
            expect(summary.escalationRequirements).toBeDefined();
        });
    });
    describe('JSON serialization', () => {
        it('should serialize to JSON with event summary', () => {
            const domainEvent = new correlated_event_created_domain_event_1.CorrelatedEventCreatedDomainEvent(aggregateId, eventData);
            const json = domainEvent.toJSON();
            expect(json.eventName).toBe('CorrelatedEventCreatedDomainEvent');
            expect(json.aggregateId).toBe(aggregateId.toString());
            expect(json.eventData).toEqual(eventData);
            expect(json.eventSummary).toBeDefined();
            expect(json.occurredOn).toBeInstanceOf(Date);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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