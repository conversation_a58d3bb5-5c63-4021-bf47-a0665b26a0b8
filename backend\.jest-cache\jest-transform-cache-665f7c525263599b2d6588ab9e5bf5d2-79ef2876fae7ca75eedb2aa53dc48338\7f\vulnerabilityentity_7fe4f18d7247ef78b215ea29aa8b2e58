790ec31223f51757b2ebd58512011785
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j;
Object.defineProperty(exports, "__esModule", { value: true });
exports.Vulnerability = void 0;
const typeorm_1 = require("typeorm");
const vulnerability_assessment_entity_1 = require("./vulnerability-assessment.entity");
const vulnerability_exception_entity_1 = require("./vulnerability-exception.entity");
const asset_entity_1 = require("../../../asset-management/domain/entities/asset.entity");
/**
 * Vulnerability entity
 * Represents a security vulnerability with comprehensive metadata and business logic
 */
let Vulnerability = class Vulnerability {
    /**
     * Check if vulnerability is critical
     */
    get isCritical() {
        return this.severity === 'critical';
    }
    /**
     * Check if vulnerability is high risk
     */
    get isHighRisk() {
        return this.severity === 'critical' ||
            (this.severity === 'high' && this.exploitable) ||
            this.hasExploit ||
            this.inTheWild;
    }
    /**
     * Get vulnerability age in days
     */
    get ageInDays() {
        const now = new Date();
        const diffMs = now.getTime() - this.publishedDate.getTime();
        return Math.floor(diffMs / (1000 * 60 * 60 * 24));
    }
    /**
     * Check if vulnerability is recent (published within last 30 days)
     */
    get isRecent() {
        return this.ageInDays <= 30;
    }
    /**
     * Check if vulnerability has been updated recently
     */
    get isRecentlyUpdated() {
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        return this.lastModifiedDate > thirtyDaysAgo;
    }
    /**
     * Get EPSS (Exploit Prediction Scoring System) score if available
     */
    get epssScore() {
        return this.customAttributes?.epss?.score || null;
    }
    /**
     * Get SSVC (Stakeholder-Specific Vulnerability Categorization) decision if available
     */
    get ssvcDecision() {
        return this.customAttributes?.ssvc?.decision || null;
    }
    /**
     * Calculate risk score based on various factors
     */
    calculateRiskScore() {
        let score = 0;
        // Base score from CVSS
        if (this.cvssScore) {
            score = this.cvssScore;
        }
        else {
            // Fallback severity scoring
            const severityScores = { info: 1, low: 3, medium: 5, high: 7, critical: 9 };
            score = severityScores[this.severity];
        }
        // Exploitability factors
        if (this.exploitable)
            score += 1;
        if (this.hasExploit)
            score += 1.5;
        if (this.inTheWild)
            score += 2;
        // Patch availability factor
        if (!this.patchAvailable)
            score += 1;
        // Age factor (newer vulnerabilities might be riskier)
        if (this.isRecent)
            score += 0.5;
        // EPSS score factor
        const epss = this.epssScore;
        if (epss && epss > 0.5)
            score += 1;
        return Math.min(10, score);
    }
    /**
     * Get affected product names
     */
    getAffectedProductNames() {
        return this.affectedProducts.map(p => `${p.vendor} ${p.product}`);
    }
    /**
     * Check if product/version is affected
     */
    isProductAffected(vendor, product, version) {
        const affectedProduct = this.affectedProducts.find(p => p.vendor.toLowerCase() === vendor.toLowerCase() &&
            p.product.toLowerCase() === product.toLowerCase());
        if (!affectedProduct)
            return false;
        if (!version)
            return true;
        // Simple version matching - in production, use proper version comparison
        return affectedProduct.versions.some(v => v.version === version);
    }
    /**
     * Add tag to vulnerability
     */
    addTag(tag) {
        if (!this.tags.includes(tag)) {
            this.tags.push(tag);
        }
    }
    /**
     * Remove tag from vulnerability
     */
    removeTag(tag) {
        this.tags = this.tags.filter(t => t !== tag);
    }
    /**
     * Update exploit status
     */
    updateExploitStatus(hasExploit, inTheWild) {
        this.hasExploit = hasExploit;
        this.inTheWild = inTheWild;
        this.exploitable = hasExploit || inTheWild;
    }
    /**
     * Update patch availability
     */
    updatePatchStatus(available, patchInfo) {
        this.patchAvailable = available;
        if (patchInfo) {
            this.patchInfo = patchInfo;
        }
    }
    /**
     * Get vulnerability summary
     */
    getSummary() {
        return {
            id: this.id,
            identifier: this.identifier,
            title: this.title,
            severity: this.severity,
            cvssScore: this.cvssScore,
            exploitable: this.exploitable,
            hasExploit: this.hasExploit,
            inTheWild: this.inTheWild,
            patchAvailable: this.patchAvailable,
            isCritical: this.isCritical,
            isHighRisk: this.isHighRisk,
            isRecent: this.isRecent,
            ageInDays: this.ageInDays,
            riskScore: this.calculateRiskScore(),
            affectedProductCount: this.affectedProducts.length,
            publishedDate: this.publishedDate,
            lastModifiedDate: this.lastModifiedDate,
            tags: this.tags,
        };
    }
    /**
     * Export vulnerability for reporting
     */
    exportForReporting() {
        return {
            vulnerability: this.getSummary(),
            description: this.description,
            cvssMetrics: this.cvssMetrics,
            affectedProducts: this.affectedProducts,
            references: this.references,
            patchInfo: this.patchInfo,
            exploitInfo: this.exploitInfo,
            threatContext: this.threatContext,
            dataSource: this.dataSource,
            exportedAt: new Date().toISOString(),
        };
    }
};
exports.Vulnerability = Vulnerability;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Vulnerability.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ unique: true }),
    __metadata("design:type", String)
], Vulnerability.prototype, "identifier", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 500 }),
    __metadata("design:type", String)
], Vulnerability.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], Vulnerability.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['info', 'low', 'medium', 'high', 'critical'],
    }),
    __metadata("design:type", String)
], Vulnerability.prototype, "severity", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cvss_score', type: 'decimal', precision: 3, scale: 1, nullable: true }),
    __metadata("design:type", Number)
], Vulnerability.prototype, "cvssScore", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cvss_vector', nullable: true }),
    __metadata("design:type", String)
], Vulnerability.prototype, "cvssVector", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cvss_metrics', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Vulnerability.prototype, "cvssMetrics", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cwe_ids', type: 'text', array: true, default: '{}' }),
    __metadata("design:type", Array)
], Vulnerability.prototype, "cweIds", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'vulnerability_type', nullable: true }),
    __metadata("design:type", String)
], Vulnerability.prototype, "vulnerabilityType", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Vulnerability.prototype, "exploitable", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'has_exploit', default: false }),
    __metadata("design:type", Boolean)
], Vulnerability.prototype, "hasExploit", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'in_the_wild', default: false }),
    __metadata("design:type", Boolean)
], Vulnerability.prototype, "inTheWild", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'patch_available', default: false }),
    __metadata("design:type", Boolean)
], Vulnerability.prototype, "patchAvailable", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'affected_products', type: 'jsonb' }),
    __metadata("design:type", typeof (_a = typeof Array !== "undefined" && Array) === "function" ? _a : Object)
], Vulnerability.prototype, "affectedProducts", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_b = typeof Array !== "undefined" && Array) === "function" ? _b : Object)
], Vulnerability.prototype, "references", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'vendor_advisory', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Vulnerability.prototype, "vendorAdvisory", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'patch_info', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Vulnerability.prototype, "patchInfo", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'exploit_info', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Vulnerability.prototype, "exploitInfo", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'threat_context', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Vulnerability.prototype, "threatContext", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, default: '{}' }),
    __metadata("design:type", Array)
], Vulnerability.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'data_source', type: 'jsonb' }),
    __metadata("design:type", Object)
], Vulnerability.prototype, "dataSource", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'published_date', type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], Vulnerability.prototype, "publishedDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_modified_date', type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], Vulnerability.prototype, "lastModifiedDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'discovered_date', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_e = typeof Date !== "undefined" && Date) === "function" ? _e : Object)
], Vulnerability.prototype, "discoveredDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'disclosure_date', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_f = typeof Date !== "undefined" && Date) === "function" ? _f : Object)
], Vulnerability.prototype, "disclosureDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'custom_attributes', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_g = typeof Record !== "undefined" && Record) === "function" ? _g : Object)
], Vulnerability.prototype, "customAttributes", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_h = typeof Date !== "undefined" && Date) === "function" ? _h : Object)
], Vulnerability.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_j = typeof Date !== "undefined" && Date) === "function" ? _j : Object)
], Vulnerability.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => vulnerability_assessment_entity_1.VulnerabilityAssessment, assessment => assessment.vulnerability),
    __metadata("design:type", Array)
], Vulnerability.prototype, "assessments", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => vulnerability_exception_entity_1.VulnerabilityException, exception => exception.vulnerability),
    __metadata("design:type", Array)
], Vulnerability.prototype, "exceptions", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => asset_entity_1.Asset, asset => asset.vulnerabilities),
    (0, typeorm_1.JoinTable)({
        name: 'asset_vulnerabilities',
        joinColumn: { name: 'vulnerability_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'asset_id', referencedColumnName: 'id' },
    }),
    __metadata("design:type", Array)
], Vulnerability.prototype, "affectedAssets", void 0);
exports.Vulnerability = Vulnerability = __decorate([
    (0, typeorm_1.Entity)('vulnerabilities'),
    (0, typeorm_1.Index)(['identifier']),
    (0, typeorm_1.Index)(['severity']),
    (0, typeorm_1.Index)(['cvssScore']),
    (0, typeorm_1.Index)(['publishedDate']),
    (0, typeorm_1.Index)(['lastModifiedDate']),
    (0, typeorm_1.Index)(['exploitable']),
    (0, typeorm_1.Index)(['hasExploit']),
    (0, typeorm_1.Index)(['inTheWild']),
    (0, typeorm_1.Index)(['patchAvailable'])
], Vulnerability);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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