7ebf4ac5ed7dd5d658899f2b579c862a
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfidenceLevelUtils = exports.ConfidenceLevel = void 0;
/**
 * Confidence Level Enum
 *
 * Represents confidence levels for security assessments, threat intelligence,
 * and automated analysis results. Used to indicate the reliability and
 * trustworthiness of security findings and recommendations.
 *
 * Confidence levels help security analysts prioritize investigations
 * and make informed decisions about response actions.
 */
var ConfidenceLevel;
(function (ConfidenceLevel) {
    /**
     * Very Low Confidence (0-20%)
     * - Preliminary or unverified findings
     * - Automated analysis with high uncertainty
     * - Insufficient data for reliable assessment
     * - Requires significant additional investigation
     */
    ConfidenceLevel["VERY_LOW"] = "very_low";
    /**
     * Low Confidence (21-40%)
     * - Limited supporting evidence
     * - Single-source information
     * - Automated analysis with moderate uncertainty
     * - Requires additional verification
     */
    ConfidenceLevel["LOW"] = "low";
    /**
     * Medium Confidence (41-60%)
     * - Moderate supporting evidence
     * - Multiple sources with some correlation
     * - Automated analysis with reasonable accuracy
     * - Some manual verification completed
     */
    ConfidenceLevel["MEDIUM"] = "medium";
    /**
     * High Confidence (61-80%)
     * - Strong supporting evidence
     * - Multiple corroborating sources
     * - Automated analysis with high accuracy
     * - Significant manual verification
     */
    ConfidenceLevel["HIGH"] = "high";
    /**
     * Very High Confidence (81-95%)
     * - Extensive supporting evidence
     * - Multiple independent sources
     * - Expert analysis and verification
     * - Comprehensive investigation completed
     */
    ConfidenceLevel["VERY_HIGH"] = "very_high";
    /**
     * Confirmed (96-100%)
     * - Definitive evidence
     * - Multiple independent confirmations
     * - Expert validation
     * - No reasonable doubt
     */
    ConfidenceLevel["CONFIRMED"] = "confirmed";
    /**
     * Unknown Confidence
     * - Confidence level cannot be determined
     * - Insufficient metadata
     * - Assessment pending
     */
    ConfidenceLevel["UNKNOWN"] = "unknown";
})(ConfidenceLevel || (exports.ConfidenceLevel = ConfidenceLevel = {}));
/**
 * Confidence Level Utilities
 */
class ConfidenceLevelUtils {
    /**
     * Get all confidence levels
     */
    static getAllConfidenceLevels() {
        return Object.values(ConfidenceLevel);
    }
    /**
     * Get actionable confidence levels (medium and above)
     */
    static getActionableConfidenceLevels() {
        return [
            ConfidenceLevel.MEDIUM,
            ConfidenceLevel.HIGH,
            ConfidenceLevel.VERY_HIGH,
            ConfidenceLevel.CONFIRMED,
        ];
    }
    /**
     * Get high confidence levels (high and above)
     */
    static getHighConfidenceLevels() {
        return [
            ConfidenceLevel.HIGH,
            ConfidenceLevel.VERY_HIGH,
            ConfidenceLevel.CONFIRMED,
        ];
    }
    /**
     * Get low confidence levels (medium and below)
     */
    static getLowConfidenceLevels() {
        return [
            ConfidenceLevel.VERY_LOW,
            ConfidenceLevel.LOW,
            ConfidenceLevel.MEDIUM,
            ConfidenceLevel.UNKNOWN,
        ];
    }
    /**
     * Get numeric value for confidence level (0-100)
     */
    static getNumericValue(confidence) {
        const values = {
            [ConfidenceLevel.VERY_LOW]: 10,
            [ConfidenceLevel.LOW]: 30,
            [ConfidenceLevel.MEDIUM]: 50,
            [ConfidenceLevel.HIGH]: 70,
            [ConfidenceLevel.VERY_HIGH]: 85,
            [ConfidenceLevel.CONFIRMED]: 95,
            [ConfidenceLevel.UNKNOWN]: 0,
        };
        return values[confidence];
    }
    /**
     * Get confidence level from numeric score (0-100)
     */
    static fromNumericValue(score) {
        if (score < 0 || score > 100) {
            return ConfidenceLevel.UNKNOWN;
        }
        if (score >= 96)
            return ConfidenceLevel.CONFIRMED;
        if (score >= 81)
            return ConfidenceLevel.VERY_HIGH;
        if (score >= 61)
            return ConfidenceLevel.HIGH;
        if (score >= 41)
            return ConfidenceLevel.MEDIUM;
        if (score >= 21)
            return ConfidenceLevel.LOW;
        if (score >= 1)
            return ConfidenceLevel.VERY_LOW;
        return ConfidenceLevel.UNKNOWN;
    }
    /**
     * Compare two confidence levels
     */
    static compare(confidence1, confidence2) {
        const value1 = ConfidenceLevelUtils.getNumericValue(confidence1);
        const value2 = ConfidenceLevelUtils.getNumericValue(confidence2);
        return value1 - value2;
    }
    /**
     * Get the higher of two confidence levels
     */
    static getHigher(confidence1, confidence2) {
        return ConfidenceLevelUtils.compare(confidence1, confidence2) >= 0 ? confidence1 : confidence2;
    }
    /**
     * Get the lower of two confidence levels
     */
    static getLower(confidence1, confidence2) {
        return ConfidenceLevelUtils.compare(confidence1, confidence2) <= 0 ? confidence1 : confidence2;
    }
    /**
     * Check if confidence level is actionable
     */
    static isActionable(confidence) {
        return ConfidenceLevelUtils.getActionableConfidenceLevels().includes(confidence);
    }
    /**
     * Check if confidence level is high
     */
    static isHigh(confidence) {
        return ConfidenceLevelUtils.getHighConfidenceLevels().includes(confidence);
    }
    /**
     * Check if confidence level is low
     */
    static isLow(confidence) {
        return ConfidenceLevelUtils.getLowConfidenceLevels().includes(confidence);
    }
    /**
     * Check if confidence level is confirmed
     */
    static isConfirmed(confidence) {
        return confidence === ConfidenceLevel.CONFIRMED;
    }
    /**
     * Get color code for UI display
     */
    static getColorCode(confidence) {
        const colors = {
            [ConfidenceLevel.CONFIRMED]: '#059669', // Green
            [ConfidenceLevel.VERY_HIGH]: '#10B981', // Emerald
            [ConfidenceLevel.HIGH]: '#3B82F6', // Blue
            [ConfidenceLevel.MEDIUM]: '#F59E0B', // Amber
            [ConfidenceLevel.LOW]: '#EF4444', // Red
            [ConfidenceLevel.VERY_LOW]: '#DC2626', // Dark Red
            [ConfidenceLevel.UNKNOWN]: '#6B7280', // Gray
        };
        return colors[confidence];
    }
    /**
     * Get icon name for UI display
     */
    static getIconName(confidence) {
        const icons = {
            [ConfidenceLevel.CONFIRMED]: 'check-circle',
            [ConfidenceLevel.VERY_HIGH]: 'check-circle-outline',
            [ConfidenceLevel.HIGH]: 'thumbs-up',
            [ConfidenceLevel.MEDIUM]: 'help-circle',
            [ConfidenceLevel.LOW]: 'alert-circle',
            [ConfidenceLevel.VERY_LOW]: 'x-circle',
            [ConfidenceLevel.UNKNOWN]: 'question-mark-circle',
        };
        return icons[confidence];
    }
    /**
     * Get human-readable description
     */
    static getDescription(confidence) {
        const descriptions = {
            [ConfidenceLevel.CONFIRMED]: 'Definitive evidence with expert validation and no reasonable doubt',
            [ConfidenceLevel.VERY_HIGH]: 'Extensive supporting evidence from multiple independent sources',
            [ConfidenceLevel.HIGH]: 'Strong supporting evidence with significant manual verification',
            [ConfidenceLevel.MEDIUM]: 'Moderate supporting evidence with some manual verification',
            [ConfidenceLevel.LOW]: 'Limited supporting evidence requiring additional verification',
            [ConfidenceLevel.VERY_LOW]: 'Preliminary findings requiring significant additional investigation',
            [ConfidenceLevel.UNKNOWN]: 'Confidence level cannot be determined due to insufficient data',
        };
        return descriptions[confidence];
    }
    /**
     * Get recommended actions based on confidence level
     */
    static getRecommendedActions(confidence) {
        const actions = {
            [ConfidenceLevel.CONFIRMED]: [
                'Proceed with immediate response actions',
                'Implement containment measures',
                'Notify stakeholders and management',
                'Document findings for compliance',
            ],
            [ConfidenceLevel.VERY_HIGH]: [
                'Proceed with response actions',
                'Consider containment measures',
                'Notify security team',
                'Prepare incident response',
            ],
            [ConfidenceLevel.HIGH]: [
                'Initiate investigation procedures',
                'Gather additional evidence',
                'Monitor for related activity',
                'Prepare response plans',
            ],
            [ConfidenceLevel.MEDIUM]: [
                'Continue investigation',
                'Seek additional verification',
                'Monitor closely',
                'Prepare contingency plans',
            ],
            [ConfidenceLevel.LOW]: [
                'Conduct further analysis',
                'Gather more evidence',
                'Verify with additional sources',
                'Monitor for patterns',
            ],
            [ConfidenceLevel.VERY_LOW]: [
                'Perform comprehensive investigation',
                'Collect additional data',
                'Verify findings with experts',
                'Consider alternative explanations',
            ],
            [ConfidenceLevel.UNKNOWN]: [
                'Assess available information',
                'Determine confidence level',
                'Gather baseline data',
                'Establish monitoring',
            ],
        };
        return actions[confidence];
    }
    /**
     * Get investigation priority based on confidence level
     */
    static getInvestigationPriority(confidence) {
        const priorities = {
            [ConfidenceLevel.CONFIRMED]: 'critical',
            [ConfidenceLevel.VERY_HIGH]: 'high',
            [ConfidenceLevel.HIGH]: 'high',
            [ConfidenceLevel.MEDIUM]: 'medium',
            [ConfidenceLevel.LOW]: 'medium',
            [ConfidenceLevel.VERY_LOW]: 'low',
            [ConfidenceLevel.UNKNOWN]: 'low',
        };
        return priorities[confidence];
    }
    /**
     * Get response urgency based on confidence level
     */
    static getResponseUrgency(confidence) {
        const urgencies = {
            [ConfidenceLevel.CONFIRMED]: 'immediate',
            [ConfidenceLevel.VERY_HIGH]: 'high',
            [ConfidenceLevel.HIGH]: 'high',
            [ConfidenceLevel.MEDIUM]: 'medium',
            [ConfidenceLevel.LOW]: 'medium',
            [ConfidenceLevel.VERY_LOW]: 'low',
            [ConfidenceLevel.UNKNOWN]: 'low',
        };
        return urgencies[confidence];
    }
    /**
     * Get escalation threshold
     */
    static getEscalationThreshold(confidence) {
        // Hours before escalation
        const thresholds = {
            [ConfidenceLevel.CONFIRMED]: 0.5, // 30 minutes
            [ConfidenceLevel.VERY_HIGH]: 1, // 1 hour
            [ConfidenceLevel.HIGH]: 2, // 2 hours
            [ConfidenceLevel.MEDIUM]: 4, // 4 hours
            [ConfidenceLevel.LOW]: 8, // 8 hours
            [ConfidenceLevel.VERY_LOW]: 24, // 24 hours
            [ConfidenceLevel.UNKNOWN]: 48, // 48 hours
        };
        return thresholds[confidence];
    }
    /**
     * Validate confidence level
     */
    static isValid(confidence) {
        return Object.values(ConfidenceLevel).includes(confidence);
    }
    /**
     * Get confidence level from string (case-insensitive)
     */
    static fromString(value) {
        const normalized = value.toLowerCase().trim().replace(/[^a-z]/g, '_');
        const confidenceLevels = Object.values(ConfidenceLevel);
        return confidenceLevels.find(level => level === normalized) || null;
    }
    /**
     * Calculate combined confidence from multiple sources
     */
    static calculateCombinedConfidence(confidences) {
        if (confidences.length === 0) {
            return ConfidenceLevel.UNKNOWN;
        }
        const totalWeight = confidences.reduce((sum, item) => sum + item.weight, 0);
        if (totalWeight === 0) {
            return ConfidenceLevel.UNKNOWN;
        }
        const weightedSum = confidences.reduce((sum, item) => {
            const numericValue = ConfidenceLevelUtils.getNumericValue(item.level);
            return sum + (numericValue * item.weight);
        }, 0);
        const averageScore = weightedSum / totalWeight;
        return ConfidenceLevelUtils.fromNumericValue(averageScore);
    }
    /**
     * Get confidence decay over time
     */
    static getDecayedConfidence(originalConfidence, ageInDays, decayRate = 0.1) {
        const originalValue = ConfidenceLevelUtils.getNumericValue(originalConfidence);
        const decayFactor = Math.exp(-decayRate * ageInDays);
        const decayedValue = originalValue * decayFactor;
        return ConfidenceLevelUtils.fromNumericValue(decayedValue);
    }
    /**
     * Get confidence boost from verification
     */
    static getVerifiedConfidence(originalConfidence, verificationType) {
        const originalValue = ConfidenceLevelUtils.getNumericValue(originalConfidence);
        const boosts = {
            manual: 10,
            automated: 5,
            expert: 20,
            peer: 15,
        };
        const boost = boosts[verificationType] || 0;
        const boostedValue = Math.min(100, originalValue + boost);
        return ConfidenceLevelUtils.fromNumericValue(boostedValue);
    }
}
exports.ConfidenceLevelUtils = ConfidenceLevelUtils;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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