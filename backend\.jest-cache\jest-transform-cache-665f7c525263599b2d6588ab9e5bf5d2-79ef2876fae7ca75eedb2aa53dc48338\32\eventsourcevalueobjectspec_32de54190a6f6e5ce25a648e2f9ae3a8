4a58d3d1e9000d3e2f45ad3bb1c7a2e0
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const event_source_value_object_1 = require("../event-source.value-object");
const event_source_type_enum_1 = require("../../../enums/event-source-type.enum");
describe('EventSource Value Object', () => {
    describe('creation', () => {
        it('should create a valid event source with type and identifier', () => {
            // Act
            const source = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.FIREWALL, 'fw-001');
            // Assert
            expect(source).toBeDefined();
            expect(source.type).toBe(event_source_type_enum_1.EventSourceType.FIREWALL);
            expect(source.identifier).toBe('fw-001');
            expect(source.name).toBe('fw-001'); // Default to identifier
        });
        it('should create a valid event source with optional properties', () => {
            // Act
            const source = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.EDR, 'edr-server-01', {
                name: 'Primary EDR Server',
                version: '1.2.3',
                vendor: 'CrowdStrike',
                location: 'datacenter-east',
                metadata: { zone: 'production' },
            });
            // Assert
            expect(source.type).toBe(event_source_type_enum_1.EventSourceType.EDR);
            expect(source.identifier).toBe('edr-server-01');
            expect(source.name).toBe('Primary EDR Server');
            expect(source.version).toBe('1.2.3');
            expect(source.vendor).toBe('CrowdStrike');
            expect(source.location).toBe('datacenter-east');
            expect(source.getMetadata('zone')).toBe('production');
        });
        it('should create event source from hostname', () => {
            // Act
            const source = event_source_value_object_1.EventSource.fromHostname(event_source_type_enum_1.EventSourceType.WEB_SERVER, 'web01.company.com');
            // Assert
            expect(source.type).toBe(event_source_type_enum_1.EventSourceType.WEB_SERVER);
            expect(source.identifier).toBe('web01.company.com');
            expect(source.name).toBe('web01.company.com');
        });
        it('should create event source from IP address', () => {
            // Act
            const source = event_source_value_object_1.EventSource.fromIPAddress(event_source_type_enum_1.EventSourceType.FIREWALL, '***********');
            // Assert
            expect(source.type).toBe(event_source_type_enum_1.EventSourceType.FIREWALL);
            expect(source.identifier).toBe('***********');
            expect(source.name).toBe('firewall-***********');
        });
        it('should create manual event source', () => {
            // Act
            const source = event_source_value_object_1.EventSource.manual('analyst-001');
            // Assert
            expect(source.type).toBe(event_source_type_enum_1.EventSourceType.MANUAL);
            expect(source.identifier).toBe('analyst-001');
            expect(source.name).toBe('Manual Event Creation');
        });
        it('should create unknown event source', () => {
            // Act
            const source = event_source_value_object_1.EventSource.unknown('unknown-device-123');
            // Assert
            expect(source.type).toBe(event_source_type_enum_1.EventSourceType.UNKNOWN);
            expect(source.identifier).toBe('unknown-device-123');
            expect(source.name).toBe('Unknown Source');
        });
    });
    describe('validation', () => {
        it('should throw error when type is not provided', () => {
            // Act & Assert
            expect(() => {
                new event_source_value_object_1.EventSource({ type: null, identifier: 'test' });
            }).toThrow('Event source must have a type');
        });
        it('should throw error when type is invalid', () => {
            // Act & Assert
            expect(() => {
                new event_source_value_object_1.EventSource({ type: 'invalid_type', identifier: 'test' });
            }).toThrow('Invalid event source type: invalid_type');
        });
        it('should throw error when identifier is not provided', () => {
            // Act & Assert
            expect(() => {
                new event_source_value_object_1.EventSource({ type: event_source_type_enum_1.EventSourceType.FIREWALL, identifier: '' });
            }).toThrow('Event source must have a non-empty identifier');
        });
        it('should throw error when identifier contains invalid characters', () => {
            // Act & Assert
            expect(() => {
                new event_source_value_object_1.EventSource({ type: event_source_type_enum_1.EventSourceType.FIREWALL, identifier: 'fw@001!' });
            }).toThrow('Event source identifier must contain only alphanumeric characters, dots, hyphens, and underscores');
        });
        it('should accept valid identifier formats', () => {
            // Act & Assert
            expect(() => {
                new event_source_value_object_1.EventSource({ type: event_source_type_enum_1.EventSourceType.FIREWALL, identifier: 'fw-001' });
            }).not.toThrow();
            expect(() => {
                new event_source_value_object_1.EventSource({ type: event_source_type_enum_1.EventSourceType.FIREWALL, identifier: 'fw_001' });
            }).not.toThrow();
            expect(() => {
                new event_source_value_object_1.EventSource({ type: event_source_type_enum_1.EventSourceType.FIREWALL, identifier: 'fw.001' });
            }).not.toThrow();
            expect(() => {
                new event_source_value_object_1.EventSource({ type: event_source_type_enum_1.EventSourceType.FIREWALL, identifier: 'fw001' });
            }).not.toThrow();
        });
        it('should throw error when version format is invalid', () => {
            // Act & Assert
            expect(() => {
                new event_source_value_object_1.EventSource({
                    type: event_source_type_enum_1.EventSourceType.FIREWALL,
                    identifier: 'fw-001',
                    version: 'invalid-version',
                });
            }).toThrow('Event source version must be in semantic version format');
        });
        it('should accept valid version formats', () => {
            // Act & Assert
            expect(() => {
                new event_source_value_object_1.EventSource({
                    type: event_source_type_enum_1.EventSourceType.FIREWALL,
                    identifier: 'fw-001',
                    version: '1.0.0',
                });
            }).not.toThrow();
            expect(() => {
                new event_source_value_object_1.EventSource({
                    type: event_source_type_enum_1.EventSourceType.FIREWALL,
                    identifier: 'fw-001',
                    version: '2.1.3-beta',
                });
            }).not.toThrow();
            expect(() => {
                new event_source_value_object_1.EventSource({
                    type: event_source_type_enum_1.EventSourceType.FIREWALL,
                    identifier: 'fw-001',
                    version: '1.0',
                });
            }).not.toThrow();
        });
        it('should throw error when location is too long', () => {
            // Act & Assert
            expect(() => {
                new event_source_value_object_1.EventSource({
                    type: event_source_type_enum_1.EventSourceType.FIREWALL,
                    identifier: 'fw-001',
                    location: 'a'.repeat(101), // 101 characters
                });
            }).toThrow('Event source location must be 100 characters or less');
        });
    });
    describe('categorization', () => {
        it('should identify network sources correctly', () => {
            // Arrange
            const firewall = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.FIREWALL, 'fw-001');
            const idsIps = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.IDS_IPS, 'ids-001');
            const networkDevice = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.NETWORK_DEVICE, 'switch-001');
            // Act & Assert
            expect(firewall.isNetworkSource()).toBe(true);
            expect(idsIps.isNetworkSource()).toBe(true);
            expect(networkDevice.isNetworkSource()).toBe(true);
            expect(firewall.getCategory()).toBe('Network');
        });
        it('should identify endpoint sources correctly', () => {
            // Arrange
            const edr = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.EDR, 'edr-001');
            const antivirus = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.ANTIVIRUS, 'av-001');
            const os = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.OPERATING_SYSTEM, 'win-server-001');
            // Act & Assert
            expect(edr.isEndpointSource()).toBe(true);
            expect(antivirus.isEndpointSource()).toBe(true);
            expect(os.isEndpointSource()).toBe(true);
            expect(edr.getCategory()).toBe('Endpoint');
        });
        it('should identify application sources correctly', () => {
            // Arrange
            const waf = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.WAF, 'waf-001');
            const database = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.DATABASE, 'db-001');
            const webServer = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.WEB_SERVER, 'web-001');
            // Act & Assert
            expect(waf.isApplicationSource()).toBe(true);
            expect(database.isApplicationSource()).toBe(true);
            expect(webServer.isApplicationSource()).toBe(true);
            expect(waf.getCategory()).toBe('Application');
        });
        it('should identify cloud sources correctly', () => {
            // Arrange
            const aws = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.AWS, 'aws-cloudtrail');
            const azure = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.AZURE, 'azure-sentinel');
            const gcp = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.GCP, 'gcp-security-center');
            // Act & Assert
            expect(aws.isCloudSource()).toBe(true);
            expect(azure.isCloudSource()).toBe(true);
            expect(gcp.isCloudSource()).toBe(true);
            expect(aws.getCategory()).toBe('Cloud');
        });
        it('should identify identity sources correctly', () => {
            // Arrange
            const ad = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.DIRECTORY_SERVICE, 'ad-001');
            const sso = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.SSO, 'sso-001');
            const mfa = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.MFA, 'mfa-001');
            // Act & Assert
            expect(ad.isIdentitySource()).toBe(true);
            expect(sso.isIdentitySource()).toBe(true);
            expect(mfa.isIdentitySource()).toBe(true);
            expect(ad.getCategory()).toBe('Identity');
        });
        it('should identify security tools correctly', () => {
            // Arrange
            const siem = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.SIEM, 'splunk-001');
            const soar = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.SOAR, 'phantom-001');
            const vulnScanner = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.VULNERABILITY_SCANNER, 'nessus-001');
            // Act & Assert
            expect(siem.isSecurityTool()).toBe(true);
            expect(soar.isSecurityTool()).toBe(true);
            expect(vulnScanner.isSecurityTool()).toBe(true);
            expect(siem.getCategory()).toBe('Security Tool');
        });
        it('should identify external sources correctly', () => {
            // Arrange
            const externalFeed = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.EXTERNAL_FEED, 'threat-feed-001');
            const thirdParty = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.THIRD_PARTY, 'vendor-001');
            const government = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.GOVERNMENT, 'cisa-feed');
            // Act & Assert
            expect(externalFeed.isExternalSource()).toBe(true);
            expect(thirdParty.isExternalSource()).toBe(true);
            expect(government.isExternalSource()).toBe(true);
            expect(externalFeed.getCategory()).toBe('External');
        });
    });
    describe('trust and reliability', () => {
        it('should identify trusted sources correctly', () => {
            // Arrange
            const siem = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.SIEM, 'splunk-001');
            const edr = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.EDR, 'crowdstrike-001');
            const ad = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.DIRECTORY_SERVICE, 'ad-001');
            const unknown = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.UNKNOWN, 'unknown-001');
            // Act & Assert
            expect(siem.isTrusted()).toBe(true);
            expect(edr.isTrusted()).toBe(true);
            expect(ad.isTrusted()).toBe(true);
            expect(unknown.isTrusted()).toBe(false);
        });
        it('should calculate reliability scores correctly', () => {
            // Arrange
            const siem = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.SIEM, 'splunk-001');
            const edr = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.EDR, 'crowdstrike-001', { vendor: 'CrowdStrike' });
            const unknown = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.UNKNOWN, 'unknown-001');
            const iot = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.IOT_DEVICE, 'sensor-001');
            // Act
            const siemScore = siem.getReliabilityScore();
            const edrScore = edr.getReliabilityScore();
            const unknownScore = unknown.getReliabilityScore();
            const iotScore = iot.getReliabilityScore();
            // Assert
            expect(siemScore).toBeGreaterThan(90);
            expect(edrScore).toBeGreaterThan(90);
            expect(unknownScore).toBeLessThan(40);
            expect(iotScore).toBeLessThan(60);
            expect(edrScore).toBeGreaterThan(siemScore); // EDR with known vendor should score higher
        });
        it('should calculate processing priority correctly', () => {
            // Arrange
            const edr = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.EDR, 'edr-001');
            const firewall = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.FIREWALL, 'fw-001');
            const iot = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.IOT_DEVICE, 'sensor-001');
            const unknown = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.UNKNOWN, 'unknown-001');
            // Act
            const edrPriority = edr.getProcessingPriority();
            const firewallPriority = firewall.getProcessingPriority();
            const iotPriority = iot.getProcessingPriority();
            const unknownPriority = unknown.getProcessingPriority();
            // Assert
            expect(edrPriority).toBe(10); // Highest priority
            expect(firewallPriority).toBe(7);
            expect(iotPriority).toBe(4);
            expect(unknownPriority).toBe(2); // Lowest priority
        });
    });
    describe('volume characteristics', () => {
        it('should identify high-volume sources correctly', () => {
            // Arrange
            const firewall = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.FIREWALL, 'fw-001');
            const webServer = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.WEB_SERVER, 'web-001');
            const edr = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.EDR, 'edr-001');
            const manual = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.MANUAL, 'analyst-001');
            // Act & Assert
            expect(firewall.isHighVolumeSource()).toBe(true);
            expect(webServer.isHighVolumeSource()).toBe(true);
            expect(edr.isHighVolumeSource()).toBe(false);
            expect(manual.isHighVolumeSource()).toBe(false);
        });
    });
    describe('metadata handling', () => {
        it('should handle metadata correctly', () => {
            // Arrange
            const metadata = { zone: 'production', criticality: 'high' };
            const source = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.FIREWALL, 'fw-001', { metadata });
            // Act & Assert
            expect(source.hasMetadata('zone')).toBe(true);
            expect(source.hasMetadata('nonexistent')).toBe(false);
            expect(source.getMetadata('zone')).toBe('production');
            expect(source.getMetadata('criticality')).toBe('high');
            expect(source.getMetadata('nonexistent')).toBeUndefined();
        });
        it('should create new instance with updated metadata', () => {
            // Arrange
            const original = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.FIREWALL, 'fw-001');
            const newMetadata = { zone: 'production' };
            // Act
            const updated = original.withMetadata(newMetadata);
            // Assert
            expect(updated).not.toBe(original);
            expect(updated.hasMetadata('zone')).toBe(true);
            expect(original.hasMetadata('zone')).toBe(false);
        });
        it('should create new instance with updated location', () => {
            // Arrange
            const original = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.FIREWALL, 'fw-001');
            // Act
            const updated = original.withLocation('datacenter-west');
            // Assert
            expect(updated).not.toBe(original);
            expect(updated.location).toBe('datacenter-west');
            expect(original.location).toBeUndefined();
        });
        it('should create new instance with updated version', () => {
            // Arrange
            const original = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.FIREWALL, 'fw-001');
            // Act
            const updated = original.withVersion('2.1.0');
            // Assert
            expect(updated).not.toBe(original);
            expect(updated.version).toBe('2.1.0');
            expect(original.version).toBeUndefined();
        });
    });
    describe('descriptions and summaries', () => {
        it('should provide correct descriptions', () => {
            // Arrange
            const firewall = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.FIREWALL, 'fw-001');
            const edr = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.EDR, 'edr-001');
            const manual = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.MANUAL, 'analyst-001');
            // Act & Assert
            expect(firewall.getDescription()).toContain('firewall');
            expect(edr.getDescription()).toContain('Endpoint Detection');
            expect(manual.getDescription()).toContain('Manual');
        });
        it('should provide comprehensive summary', () => {
            // Arrange
            const source = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.EDR, 'edr-001', {
                name: 'Primary EDR',
                vendor: 'CrowdStrike',
            });
            // Act
            const summary = source.getSummary();
            // Assert
            expect(summary.type).toBe(event_source_type_enum_1.EventSourceType.EDR);
            expect(summary.identifier).toBe('edr-001');
            expect(summary.name).toBe('Primary EDR');
            expect(summary.category).toBe('Endpoint');
            expect(summary.reliability).toBeGreaterThan(90);
            expect(summary.priority).toBe(10);
            expect(summary.isTrusted).toBe(true);
            expect(summary.isHighVolume).toBe(false);
        });
    });
    describe('serialization', () => {
        it('should convert to JSON correctly', () => {
            // Arrange
            const source = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.FIREWALL, 'fw-001', {
                name: 'Main Firewall',
                version: '1.2.3',
                vendor: 'Palo Alto',
                location: 'datacenter-east',
                metadata: { zone: 'production' },
            });
            // Act
            const json = source.toJSON();
            // Assert
            expect(json.type).toBe(event_source_type_enum_1.EventSourceType.FIREWALL);
            expect(json.identifier).toBe('fw-001');
            expect(json.name).toBe('Main Firewall');
            expect(json.version).toBe('1.2.3');
            expect(json.vendor).toBe('Palo Alto');
            expect(json.location).toBe('datacenter-east');
            expect(json.metadata).toEqual({ zone: 'production' });
            expect(json.summary).toBeDefined();
            expect(json.description).toBeDefined();
        });
        it('should create from JSON correctly', () => {
            // Arrange
            const json = {
                type: event_source_type_enum_1.EventSourceType.FIREWALL,
                identifier: 'fw-001',
                name: 'Main Firewall',
                version: '1.2.3',
                vendor: 'Palo Alto',
                location: 'datacenter-east',
                metadata: { zone: 'production' },
            };
            // Act
            const source = event_source_value_object_1.EventSource.fromJSON(json);
            // Assert
            expect(source.type).toBe(event_source_type_enum_1.EventSourceType.FIREWALL);
            expect(source.identifier).toBe('fw-001');
            expect(source.name).toBe('Main Firewall');
            expect(source.version).toBe('1.2.3');
            expect(source.vendor).toBe('Palo Alto');
            expect(source.location).toBe('datacenter-east');
            expect(source.getMetadata('zone')).toBe('production');
        });
    });
    describe('equality and comparison', () => {
        it('should compare sources for equality correctly', () => {
            // Arrange
            const source1 = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.FIREWALL, 'fw-001');
            const source2 = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.FIREWALL, 'fw-001');
            const source3 = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.FIREWALL, 'fw-002');
            const source4 = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.EDR, 'fw-001');
            // Act & Assert
            expect(source1.equals(source2)).toBe(true);
            expect(source1.equals(source3)).toBe(false);
            expect(source1.equals(source4)).toBe(false);
            expect(source1.equals(undefined)).toBe(false);
            expect(source1.equals(source1)).toBe(true);
        });
        it('should convert to string correctly', () => {
            // Arrange
            const source = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.FIREWALL, 'fw-001');
            // Act
            const str = source.toString();
            // Assert
            expect(str).toBe('firewall:fw-001');
        });
    });
    describe('validation utility', () => {
        it('should validate source without creating instance', () => {
            // Act & Assert
            expect(event_source_value_object_1.EventSource.isValid(event_source_type_enum_1.EventSourceType.FIREWALL, 'fw-001')).toBe(true);
            expect(event_source_value_object_1.EventSource.isValid(event_source_type_enum_1.EventSourceType.FIREWALL, '')).toBe(false);
            expect(event_source_value_object_1.EventSource.isValid(event_source_type_enum_1.EventSourceType.FIREWALL, 'fw@001')).toBe(false);
        });
    });
    describe('edge cases', () => {
        it('should handle empty metadata gracefully', () => {
            // Arrange
            const source = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.FIREWALL, 'fw-001');
            // Act & Assert
            expect(source.metadata).toEqual({});
            expect(source.hasMetadata('anything')).toBe(false);
            expect(source.getMetadata('anything')).toBeUndefined();
        });
        it('should handle missing optional properties gracefully', () => {
            // Arrange
            const source = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.FIREWALL, 'fw-001');
            // Act & Assert
            expect(source.name).toBe('fw-001'); // Falls back to identifier
            expect(source.version).toBeUndefined();
            expect(source.vendor).toBeUndefined();
            expect(source.location).toBeUndefined();
        });
        it('should handle unknown source types in utility methods', () => {
            // Arrange
            const source = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.OTHER, 'other-001');
            // Act & Assert
            expect(source.getCategory()).toBe('Other');
            expect(source.getReliabilityScore()).toBe(40); // Default for OTHER
            expect(source.getProcessingPriority()).toBe(5); // Default medium priority
            expect(source.isTrusted()).toBe(false);
            expect(source.isHighVolumeSource()).toBe(false);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************