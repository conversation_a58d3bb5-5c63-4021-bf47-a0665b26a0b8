{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\__tests__\\threat.factory.spec.ts", "mappings": ";;AAAA,sDAAuI;AAEvI,2EAAkE;AAClE,6FAAsF;AACtF,2GAAuG;AACvG,8EAAyE;AAEzE,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;IAC7B,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,OAAO,GAAwB;gBACnC,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,yBAAyB;gBACtC,QAAQ,EAAE,qCAAc,CAAC,IAAI;gBAC7B,QAAQ,EAAE,SAAS;gBACnB,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE,EAAE;aACf,CAAC;YAEF,MAAM,MAAM,GAAG,8BAAa,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE7C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,qCAAc,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,GAAG,GAAG,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,eAAe,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;YACjF,MAAM,SAAS,GAAG,mCAAS,CAAC,UAAU,CAAC,GAAG,EAAE,8CAA8C,CAAC,CAAC;YAE5F,MAAM,OAAO,GAAwB;gBACnC,IAAI,EAAE,iBAAiB;gBACvB,WAAW,EAAE,4BAA4B;gBACzC,QAAQ,EAAE,qCAAc,CAAC,QAAQ;gBACjC,QAAQ,EAAE,KAAK;gBACf,WAAW,EAAE,cAAc;gBAC3B,IAAI,EAAE,4BAA4B;gBAClC,UAAU,EAAE,EAAE;gBACd,UAAU,EAAE,CAAC,GAAG,CAAC;gBACjB,UAAU,EAAE,CAAC,SAAS,CAAC;gBACvB,WAAW,EAAE;oBACX,KAAK,EAAE,OAAO;oBACd,UAAU,EAAE,EAAE;oBACd,OAAO,EAAE,CAAC,WAAW,CAAC;oBACtB,UAAU,EAAE,CAAC,WAAW,CAAC;oBACzB,YAAY,EAAE,CAAC,kBAAkB,CAAC;oBAClC,SAAS,EAAE,CAAC,iBAAiB,CAAC;iBAC/B;gBACD,UAAU,EAAE,CAAC;wBACX,EAAE,EAAE,OAAO;wBACX,IAAI,EAAE,mBAAmB;wBACzB,MAAM,EAAE,iBAAiB;wBACzB,WAAW,EAAE,4CAA4C;wBACzD,UAAU,EAAE,EAAE;wBACd,QAAQ,EAAE,CAAC,4BAA4B,CAAC;qBACzC,CAAC;gBACF,cAAc,EAAE,CAAC,YAAY,EAAE,iBAAiB,CAAC;gBACjD,IAAI,EAAE,CAAC,KAAK,EAAE,UAAU,EAAE,cAAc,CAAC;gBACzC,UAAU,EAAE,EAAE,QAAQ,EAAE,iBAAiB,EAAE;aAC5C,CAAC;YAEF,MAAM,MAAM,GAAG,8BAAa,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE7C,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,OAAO,GAAwB;gBACnC,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,kBAAkB;gBAC/B,QAAQ,EAAE,qCAAc,CAAC,MAAM;gBAC/B,QAAQ,EAAE,MAAM;gBAChB,IAAI,EAAE,WAAW;gBACjB,UAAU,EAAE,EAAE;aACf,CAAC;YAEF,MAAM,MAAM,GAAG,8BAAa,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAC7C,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;YAEzC,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,2CAAmB,CAAC,CAAC;YAE5D,MAAM,aAAa,GAAG,YAAY,CAAC,CAAC,CAAwB,CAAC;YAC7D,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACrD,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,qCAAc,CAAC,MAAM,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,UAAU,GAA2B;gBACzC,SAAS,EAAE,eAAe;gBAC1B,aAAa,EAAE,IAAI;gBACnB,QAAQ,EAAE,MAAM;gBAChB,WAAW,EAAE,sBAAsB;gBACnC,UAAU,EAAE,EAAE;gBACd,MAAM,EAAE,mBAAmB;gBAC3B,WAAW,EAAE;oBACX,KAAK,EAAE,OAAO;oBACd,QAAQ,EAAE,mBAAmB;oBAC7B,aAAa,EAAE,QAAQ;iBACxB;gBACD,QAAQ,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE;aAC/B,CAAC;YAEF,MAAM,MAAM,GAAG,8BAAa,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;YAEhE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;YAC/D,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACxD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,qCAAc,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACzD,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,UAAU,GAA2B;gBACzC,SAAS,EAAE,uBAAuB;gBAClC,aAAa,EAAE,QAAQ;gBACvB,QAAQ,EAAE,QAAQ;gBAClB,WAAW,EAAE,4BAA4B;gBACzC,MAAM,EAAE,mBAAmB;gBAC3B,WAAW,EAAE;oBACX,KAAK,EAAE,eAAe;oBACtB,QAAQ,EAAE,YAAY;iBACvB;aACF,CAAC;YAEF,MAAM,MAAM,GAAG,8BAAa,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;YAEhE,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,UAAU,GAA2B;gBACzC,SAAS,EAAE,qBAAqB;gBAChC,aAAa,EAAE,UAAU;gBACzB,QAAQ,EAAE,KAAK;aAChB,CAAC;YAEF,MAAM,MAAM,GAAG,8BAAa,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;YAEhE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;YACrE,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,qCAAc,CAAC,GAAG,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,aAAa,EAAE,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,aAAa,EAAE,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,WAAW,GAAwB;gBACvC,IAAI,EAAE,kEAAkE;gBACxE,QAAQ,EAAE,QAAQ;gBAClB,MAAM,EAAE,QAAQ;gBAChB,OAAO,EAAE,IAAI;gBACb,UAAU,EAAE,EAAE;gBACd,YAAY,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,CAAC;gBACtD,SAAS,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;gBAC5B,WAAW,EAAE,CAAC,uBAAuB,EAAE,gBAAgB,CAAC;gBACxD,aAAa,EAAE,CAAC,kBAAkB,EAAE,qBAAqB,CAAC;gBAC1D,iBAAiB,EAAE,CAAC,uBAAuB,EAAE,eAAe,CAAC;aAC9D,CAAC;YAEF,MAAM,MAAM,GAAG,8BAAa,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;YAE9D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,4BAA4B;YACjF,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,kBAAkB,EAAE,qBAAqB,CAAC,CAAC,CAAC;YACnF,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,WAAW,GAAwB;gBACvC,IAAI,EAAE,kBAAkB;gBACxB,QAAQ,EAAE,KAAK;gBACf,UAAU,EAAE,EAAE;gBACd,YAAY,EAAE,CAAC,iBAAiB,CAAC;gBACjC,SAAS,EAAE,EAAE;gBACb,WAAW,EAAE,EAAE;aAChB,CAAC;YAEF,MAAM,MAAM,GAAG,8BAAa,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;YAE9D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,aAAa,EAAE,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,qBAAqB,GAAwB;gBACjD,IAAI,EAAE,SAAS;gBACf,QAAQ,EAAE,MAAM;gBAChB,UAAU,EAAE,EAAE;gBACd,YAAY,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,aAAa,EAAE,SAAS,CAAC;gBACrG,SAAS,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC;gBAC3B,WAAW,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC;gBACpC,iBAAiB,EAAE,CAAC,gBAAgB,CAAC;aACtC,CAAC;YAEF,MAAM,MAAM,GAAG,8BAAa,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,CAAC;YAExE,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,qCAAc,CAAC,QAAQ,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,WAAW,GAAsB;gBACrC,IAAI,EAAE,oBAAoB;gBAC1B,WAAW,EAAE,OAAO;gBACpB,MAAM,EAAE,mBAAmB;gBAC3B,WAAW,EAAE,6CAA6C;gBAC1D,UAAU,EAAE,EAAE;gBACd,QAAQ,EAAE,CAAC,uBAAuB,EAAE,oBAAoB,CAAC;gBACzD,cAAc,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;aACpC,CAAC;YAEF,MAAM,MAAM,GAAG,8BAAa,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;YAE5D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;YAC/D,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;YAC/E,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,aAAa,GAAsB;gBACvC,IAAI,EAAE,kBAAkB;gBACxB,MAAM,EAAE,QAAQ;gBAChB,WAAW,EAAE,8BAA8B;gBAC3C,UAAU,EAAE,EAAE;gBACd,QAAQ,EAAE,CAAC,wBAAwB,CAAC;aACrC,CAAC;YAEF,MAAM,MAAM,GAAG,8BAAa,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAC9D,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,qCAAc,CAAC,QAAQ,CAAC,CAAC;YAEtD,MAAM,YAAY,GAAsB;gBACtC,IAAI,EAAE,mBAAmB;gBACzB,MAAM,EAAE,WAAW;gBACnB,WAAW,EAAE,4CAA4C;gBACzD,UAAU,EAAE,EAAE;gBACd,QAAQ,EAAE,CAAC,uBAAuB,CAAC;aACpC,CAAC;YAEF,MAAM,WAAW,GAAG,8BAAa,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAClE,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,qCAAc,CAAC,GAAG,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,MAAM,GAAG,8BAAa,CAAC,uBAAuB,CAClD,gBAAgB,EAChB,iCAAiC,EACjC,gBAAgB,EAChB,UAAU,CACX,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,qCAAc,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,MAAM,GAAG,8BAAa,CAAC,uBAAuB,CAClD,cAAc,EACd,oBAAoB,EACpB,QAAQ,EACR,aAAa,EACb;gBACE,UAAU,EAAE,EAAE;gBACd,IAAI,EAAE,CAAC,YAAY,CAAC;gBACpB,UAAU,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE;aACnC,CACF,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,MAAM,GAAG,8BAAa,CAAC,mBAAmB,CAC9C,4BAA4B,EAC5B,8CAA8C,EAC9C,UAAU,EACV,mBAAmB,CACpB,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,qCAAc,CAAC,QAAQ,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,0BAA0B,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,UAAU,GAAG;gBACjB,EAAE,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE;gBAC7B,EAAE,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,QAAQ,EAAE;gBAC/B,EAAE,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE;aAC9B,CAAC;YAEF,MAAM,MAAM,GAAG,8BAAa,CAAC,oBAAoB,CAC/C,UAAU,EACV,oBAAoB,EACpB,6CAA6C,CAC9C,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC;YAChF,MAAM,MAAM,GAAG,8BAAa,CAAC,oBAAoB,CAC/C,UAAU,EACV,oBAAoB,EACpB,oCAAoC,CACrC,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,qCAAc,CAAC,QAAQ,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,MAAM,GAAG,8BAAa,CAAC,kBAAkB,CAC7C,wBAAwB,EACxB,2CAA2C,EAC3C,qCAAc,CAAC,MAAM,EACrB,iBAAiB,EACjB,iBAAiB,EACjB,EAAE,EACF,qBAAqB,CACtB,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,qCAAc,CAAC,MAAM,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAChE,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,sBAAsB;YACtB,MAAM,cAAc,GAAG,8BAAa,CAAC,sBAAsB,CAAC;gBAC1D,SAAS,EAAE,MAAM;gBACjB,aAAa,EAAE,IAAI;gBACnB,QAAQ,EAAE,UAAU;aACrB,CAAC,CAAC;YACH,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,qCAAc,CAAC,QAAQ,CAAC,CAAC;YAE9D,uBAAuB;YACvB,MAAM,UAAU,GAAG,8BAAa,CAAC,sBAAsB,CAAC;gBACtD,SAAS,EAAE,MAAM;gBACjB,aAAa,EAAE,IAAI;gBACnB,QAAQ,EAAE,CAAC;aACZ,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,qCAAc,CAAC,IAAI,CAAC,CAAC;YAEtD,uBAAuB;YACvB,MAAM,aAAa,GAAG,8BAAa,CAAC,sBAAsB,CAAC;gBACzD,SAAS,EAAE,MAAM;gBACjB,aAAa,EAAE,IAAI;gBACnB,QAAQ,EAAE,SAAS;aACpB,CAAC,CAAC;YACH,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,qCAAc,CAAC,OAAO,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,QAAQ,GAAG,8BAAa,CAAC,sBAAsB,CAAC;gBACpD,SAAS,EAAE,aAAa;gBACxB,aAAa,EAAE,YAAY;gBAC3B,QAAQ,EAAE,QAAQ;aACnB,CAAC,CAAC;YACH,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,0BAAO,CAAC,UAAU,CAAC,CAAC;YAE7D,MAAM,YAAY,GAAG,8BAAa,CAAC,sBAAsB,CAAC;gBACxD,SAAS,EAAE,uBAAuB;gBAClC,aAAa,EAAE,QAAQ;gBACvB,QAAQ,EAAE,QAAQ;aACnB,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,0BAAO,CAAC,MAAM,CAAC,CAAC;YAE7D,MAAM,UAAU,GAAG,8BAAa,CAAC,sBAAsB,CAAC;gBACtD,SAAS,EAAE,kCAAkC;gBAC7C,aAAa,EAAE,KAAK;gBACpB,QAAQ,EAAE,QAAQ;aACnB,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,0BAAO,CAAC,QAAQ,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,eAAe,GAA2B;gBAC9C,SAAS,EAAE,UAAU;gBACrB,aAAa,EAAE,QAAQ;gBACvB,QAAQ,EAAE,MAAM;gBAChB,MAAM,EAAE,cAAc;gBACtB,WAAW,EAAE,gFAAgF;gBAC7F,WAAW,EAAE;oBACX,KAAK,EAAE,OAAO;oBACd,QAAQ,EAAE,mBAAmB;iBAC9B;aACF,CAAC;YAEF,MAAM,MAAM,GAAG,8BAAa,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC;YACrE,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAE9C,MAAM,cAAc,GAA2B;gBAC7C,SAAS,EAAE,WAAW;gBACtB,aAAa,EAAE,QAAQ;gBACvB,QAAQ,EAAE,KAAK;aAChB,CAAC;YAEF,MAAM,gBAAgB,GAAG,8BAAa,CAAC,sBAAsB,CAAC,cAAc,CAAC,CAAC;YAC9E,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,GAAG,EAAE;gBACV,8BAAa,CAAC,MAAM,CAAC;oBACnB,IAAI,EAAE,EAAE;oBACR,WAAW,EAAE,MAAM;oBACnB,QAAQ,EAAE,qCAAc,CAAC,GAAG;oBAC5B,QAAQ,EAAE,MAAM;oBAChB,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,EAAE;iBACf,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;YAEtC,MAAM,CAAC,GAAG,EAAE;gBACV,8BAAa,CAAC,MAAM,CAAC;oBACnB,IAAI,EAAE,MAAM;oBACZ,WAAW,EAAE,EAAE;oBACf,QAAQ,EAAE,qCAAc,CAAC,GAAG;oBAC5B,QAAQ,EAAE,MAAM;oBAChB,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,EAAE;iBACf,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;YAE7C,MAAM,CAAC,GAAG,EAAE;gBACV,8BAAa,CAAC,MAAM,CAAC;oBACnB,IAAI,EAAE,MAAM;oBACZ,WAAW,EAAE,MAAM;oBACnB,QAAQ,EAAE,qCAAc,CAAC,GAAG;oBAC5B,QAAQ,EAAE,MAAM;oBAChB,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,GAAG;iBAChB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,6CAA6C,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\__tests__\\threat.factory.spec.ts"], "sourcesContent": ["import { ThreatFactory, CreateThreatOptions, ThreatIntelligenceData, MalwareAnalysisData, AttackPatternData } from '../threat.factory';\r\nimport { Threat } from '../../entities/threat/threat.entity';\r\nimport { ThreatSeverity } from '../../enums/threat-severity.enum';\r\nimport { IOC, IOCType } from '../../value-objects/threat-indicators/ioc.value-object';\r\nimport { CVSSScore, CVSSVersion } from '../../value-objects/threat-indicators/cvss-score.value-object';\r\nimport { ThreatDetectedEvent } from '../../events/threat-detected.event';\r\n\r\ndescribe('ThreatFactory', () => {\r\n  describe('create', () => {\r\n    it('should create a basic threat with required fields', () => {\r\n      const options: CreateThreatOptions = {\r\n        name: 'Test Threat',\r\n        description: 'Test threat description',\r\n        severity: ThreatSeverity.HIGH,\r\n        category: 'malware',\r\n        type: 'trojan',\r\n        confidence: 85,\r\n      };\r\n\r\n      const threat = ThreatFactory.create(options);\r\n\r\n      expect(threat.name).toBe('Test Threat');\r\n      expect(threat.description).toBe('Test threat description');\r\n      expect(threat.severity).toBe(ThreatSeverity.HIGH);\r\n      expect(threat.category).toBe('malware');\r\n      expect(threat.type).toBe('trojan');\r\n      expect(threat.confidence).toBe(85);\r\n      expect(threat.isActive()).toBe(true);\r\n      expect(threat.riskAssessment.riskScore).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should create threat with optional fields', () => {\r\n      const ioc = IOC.create(IOCType.IP_ADDRESS, '*************', 'high', 'confirmed');\r\n      const cvssScore = CVSSScore.createV3_1(8.5, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H');\r\n\r\n      const options: CreateThreatOptions = {\r\n        name: 'Advanced Threat',\r\n        description: 'Advanced persistent threat',\r\n        severity: ThreatSeverity.CRITICAL,\r\n        category: 'apt',\r\n        subcategory: 'nation-state',\r\n        type: 'advanced-persistent-threat',\r\n        confidence: 95,\r\n        indicators: [ioc],\r\n        cvssScores: [cvssScore],\r\n        attribution: {\r\n          actor: 'APT29',\r\n          confidence: 90,\r\n          aliases: ['Cozy Bear'],\r\n          motivation: ['espionage'],\r\n          capabilities: ['advanced_evasion'],\r\n          campaigns: ['Operation Ghost'],\r\n        },\r\n        techniques: [{\r\n          id: 'T1055',\r\n          name: 'Process Injection',\r\n          tactic: 'Defense Evasion',\r\n          description: 'Adversaries may inject code into processes',\r\n          confidence: 85,\r\n          evidence: ['process_hollowing_detected'],\r\n        }],\r\n        affectedAssets: ['server-001', 'workstation-042'],\r\n        tags: ['apt', 'critical', 'nation-state'],\r\n        attributes: { campaign: 'Operation Ghost' },\r\n      };\r\n\r\n      const threat = ThreatFactory.create(options);\r\n\r\n      expect(threat.subcategory).toBe('nation-state');\r\n      expect(threat.indicators).toHaveLength(1);\r\n      expect(threat.cvssScores).toHaveLength(1);\r\n      expect(threat.attribution?.actor).toBe('APT29');\r\n      expect(threat.techniques).toHaveLength(1);\r\n      expect(threat.affectedAssets).toHaveLength(2);\r\n      expect(threat.tags).toContain('apt');\r\n      expect(threat.attributes.campaign).toBe('Operation Ghost');\r\n    });\r\n\r\n    it('should publish ThreatDetectedEvent on creation', () => {\r\n      const options: CreateThreatOptions = {\r\n        name: 'Test Threat',\r\n        description: 'Test description',\r\n        severity: ThreatSeverity.MEDIUM,\r\n        category: 'test',\r\n        type: 'test_type',\r\n        confidence: 70,\r\n      };\r\n\r\n      const threat = ThreatFactory.create(options);\r\n      const domainEvents = threat.domainEvents;\r\n\r\n      expect(domainEvents).toHaveLength(1);\r\n      expect(domainEvents[0]).toBeInstanceOf(ThreatDetectedEvent);\r\n\r\n      const detectedEvent = domainEvents[0] as ThreatDetectedEvent;\r\n      expect(detectedEvent.threatName).toBe('Test Threat');\r\n      expect(detectedEvent.severity).toBe(ThreatSeverity.MEDIUM);\r\n    });\r\n  });\r\n\r\n  describe('fromThreatIntelligence', () => {\r\n    it('should create threat from threat intelligence data', () => {\r\n      const threatData: ThreatIntelligenceData = {\r\n        indicator: '*************',\r\n        indicatorType: 'ip',\r\n        severity: 'high',\r\n        description: 'Malicious IP address',\r\n        confidence: 85,\r\n        source: 'threat-intel-feed',\r\n        attribution: {\r\n          actor: 'APT28',\r\n          campaign: 'Operation Stealth',\r\n          malwareFamily: 'Sofacy',\r\n        },\r\n        metadata: { feed_id: '12345' },\r\n      };\r\n\r\n      const threat = ThreatFactory.fromThreatIntelligence(threatData);\r\n\r\n      expect(threat.name).toBe('Threat Intelligence: *************');\r\n      expect(threat.description).toBe('Malicious IP address');\r\n      expect(threat.severity).toBe(ThreatSeverity.HIGH);\r\n      expect(threat.category).toBe('threat-intelligence');\r\n      expect(threat.type).toBe('ip');\r\n      expect(threat.confidence).toBe(85);\r\n      expect(threat.indicators).toHaveLength(1);\r\n      expect(threat.indicators[0].value).toBe('*************');\r\n      expect(threat.attribution?.actor).toBe('APT28');\r\n      expect(threat.malwareFamily?.name).toBe('Sofacy');\r\n      expect(threat.tags).toContain('threat-intelligence');\r\n      expect(threat.attributes.source).toBe('threat-intel-feed');\r\n    });\r\n\r\n    it('should infer confidence when not provided', () => {\r\n      const threatData: ThreatIntelligenceData = {\r\n        indicator: 'malicious.example.com',\r\n        indicatorType: 'domain',\r\n        severity: 'medium',\r\n        description: 'Command and control domain',\r\n        source: 'internal-analysis',\r\n        attribution: {\r\n          actor: 'Unknown Actor',\r\n          campaign: 'Campaign X',\r\n        },\r\n      };\r\n\r\n      const threat = ThreatFactory.fromThreatIntelligence(threatData);\r\n\r\n      expect(threat.confidence).toBeGreaterThan(50);\r\n      expect(threat.confidence).toBeLessThanOrEqual(100);\r\n    });\r\n\r\n    it('should handle minimal threat intelligence data', () => {\r\n      const threatData: ThreatIntelligenceData = {\r\n        indicator: 'suspicious-file.exe',\r\n        indicatorType: 'filename',\r\n        severity: 'low',\r\n      };\r\n\r\n      const threat = ThreatFactory.fromThreatIntelligence(threatData);\r\n\r\n      expect(threat.name).toBe('Threat Intelligence: suspicious-file.exe');\r\n      expect(threat.severity).toBe(ThreatSeverity.LOW);\r\n      expect(threat.indicators).toHaveLength(1);\r\n      expect(threat.attribution).toBeUndefined();\r\n      expect(threat.malwareFamily).toBeUndefined();\r\n    });\r\n  });\r\n\r\n  describe('fromMalwareAnalysis', () => {\r\n    it('should create threat from malware analysis data', () => {\r\n      const malwareData: MalwareAnalysisData = {\r\n        hash: 'a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456',\r\n        hashType: 'SHA256',\r\n        family: 'Emotet',\r\n        variant: 'v4',\r\n        confidence: 90,\r\n        capabilities: ['credential_theft', 'lateral_movement'],\r\n        protocols: ['HTTP', 'HTTPS'],\r\n        persistence: ['registry_modification', 'scheduled_task'],\r\n        affectedFiles: ['/tmp/malware.exe', '/var/log/system.log'],\r\n        networkIndicators: ['malicious.example.com', '192.168.1.200'],\r\n      };\r\n\r\n      const threat = ThreatFactory.fromMalwareAnalysis(malwareData);\r\n\r\n      expect(threat.name).toBe('Malware: Emotet');\r\n      expect(threat.category).toBe('malware');\r\n      expect(threat.type).toBe('Emotet');\r\n      expect(threat.confidence).toBe(90);\r\n      expect(threat.indicators.length).toBeGreaterThan(1); // Hash + network indicators\r\n      expect(threat.malwareFamily?.name).toBe('Emotet');\r\n      expect(threat.malwareFamily?.variant).toBe('v4');\r\n      expect(threat.affectedAssets).toEqual(['/tmp/malware.exe', '/var/log/system.log']);\r\n      expect(threat.tags).toContain('malware');\r\n      expect(threat.attributes.hash).toBe(malwareData.hash);\r\n    });\r\n\r\n    it('should handle malware data without family', () => {\r\n      const malwareData: MalwareAnalysisData = {\r\n        hash: 'abcdef1234567890',\r\n        hashType: 'MD5',\r\n        confidence: 75,\r\n        capabilities: ['file_encryption'],\r\n        protocols: [],\r\n        persistence: [],\r\n      };\r\n\r\n      const threat = ThreatFactory.fromMalwareAnalysis(malwareData);\r\n\r\n      expect(threat.name).toBe('Malware: abcdef12');\r\n      expect(threat.type).toBe('unknown-malware');\r\n      expect(threat.malwareFamily).toBeUndefined();\r\n      expect(threat.indicators).toHaveLength(1);\r\n    });\r\n\r\n    it('should infer severity based on capabilities', () => {\r\n      const highCapabilityMalware: MalwareAnalysisData = {\r\n        hash: 'test123',\r\n        hashType: 'SHA1',\r\n        confidence: 80,\r\n        capabilities: ['data_exfiltration', 'credential_theft', 'lateral_movement', 'persistence', 'evasion'],\r\n        protocols: ['HTTPS', 'DNS'],\r\n        persistence: ['registry', 'service'],\r\n        networkIndicators: ['c2.example.com'],\r\n      };\r\n\r\n      const threat = ThreatFactory.fromMalwareAnalysis(highCapabilityMalware);\r\n\r\n      expect(threat.severity).toBe(ThreatSeverity.CRITICAL);\r\n    });\r\n  });\r\n\r\n  describe('fromAttackPattern', () => {\r\n    it('should create threat from attack pattern data', () => {\r\n      const patternData: AttackPatternData = {\r\n        name: 'Credential Dumping',\r\n        techniqueId: 'T1003',\r\n        tactic: 'Credential Access',\r\n        description: 'Adversaries may attempt to dump credentials',\r\n        confidence: 85,\r\n        evidence: ['lsass_access_detected', 'mimikatz_signature'],\r\n        affectedAssets: ['DC-01', 'WS-042'],\r\n      };\r\n\r\n      const threat = ThreatFactory.fromAttackPattern(patternData);\r\n\r\n      expect(threat.name).toBe('Attack Pattern: Credential Dumping');\r\n      expect(threat.description).toBe('Adversaries may attempt to dump credentials');\r\n      expect(threat.category).toBe('attack-pattern');\r\n      expect(threat.type).toBe('credential-access');\r\n      expect(threat.confidence).toBe(85);\r\n      expect(threat.techniques).toHaveLength(1);\r\n      expect(threat.techniques[0].id).toBe('T1003');\r\n      expect(threat.affectedAssets).toEqual(['DC-01', 'WS-042']);\r\n      expect(threat.tags).toContain('attack-pattern');\r\n      expect(threat.attributes.techniqueId).toBe('T1003');\r\n    });\r\n\r\n    it('should infer severity from tactic', () => {\r\n      const impactPattern: AttackPatternData = {\r\n        name: 'Data Destruction',\r\n        tactic: 'Impact',\r\n        description: 'Adversaries may destroy data',\r\n        confidence: 90,\r\n        evidence: ['file_deletion_detected'],\r\n      };\r\n\r\n      const threat = ThreatFactory.fromAttackPattern(impactPattern);\r\n      expect(threat.severity).toBe(ThreatSeverity.CRITICAL);\r\n\r\n      const reconPattern: AttackPatternData = {\r\n        name: 'Network Discovery',\r\n        tactic: 'Discovery',\r\n        description: 'Adversaries may discover network resources',\r\n        confidence: 70,\r\n        evidence: ['network_scan_detected'],\r\n      };\r\n\r\n      const reconThreat = ThreatFactory.fromAttackPattern(reconPattern);\r\n      expect(reconThreat.severity).toBe(ThreatSeverity.LOW);\r\n    });\r\n  });\r\n\r\n  describe('createHighSeverityAlert', () => {\r\n    it('should create high severity alert with defaults', () => {\r\n      const threat = ThreatFactory.createHighSeverityAlert(\r\n        'Security Alert',\r\n        'High severity security incident',\r\n        'security-alert',\r\n        'incident'\r\n      );\r\n\r\n      expect(threat.name).toBe('Security Alert');\r\n      expect(threat.severity).toBe(ThreatSeverity.HIGH);\r\n      expect(threat.confidence).toBe(85);\r\n      expect(threat.tags).toContain('high-severity');\r\n      expect(threat.tags).toContain('alert');\r\n    });\r\n\r\n    it('should allow overriding defaults', () => {\r\n      const threat = ThreatFactory.createHighSeverityAlert(\r\n        'Custom Alert',\r\n        'Custom description',\r\n        'custom',\r\n        'custom-type',\r\n        {\r\n          confidence: 95,\r\n          tags: ['custom-tag'],\r\n          attributes: { priority: 'urgent' },\r\n        }\r\n      );\r\n\r\n      expect(threat.confidence).toBe(95);\r\n      expect(threat.tags).toContain('custom-tag');\r\n      expect(threat.attributes.priority).toBe('urgent');\r\n    });\r\n  });\r\n\r\n  describe('createCriticalAlert', () => {\r\n    it('should create critical alert with appropriate defaults', () => {\r\n      const threat = ThreatFactory.createCriticalAlert(\r\n        'Critical Security Incident',\r\n        'Critical threat requiring immediate response',\r\n        'incident',\r\n        'critical-incident'\r\n      );\r\n\r\n      expect(threat.severity).toBe(ThreatSeverity.CRITICAL);\r\n      expect(threat.confidence).toBe(95);\r\n      expect(threat.tags).toContain('critical');\r\n      expect(threat.tags).toContain('alert');\r\n      expect(threat.tags).toContain('immediate-response');\r\n      expect(threat.requiresImmediateAttention()).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('fromEventCorrelation', () => {\r\n    it('should create threat from correlated events', () => {\r\n      const mockEvents = [\r\n        { id: '1', severity: 'high' },\r\n        { id: '2', severity: 'medium' },\r\n        { id: '3', severity: 'high' },\r\n      ];\r\n\r\n      const threat = ThreatFactory.fromEventCorrelation(\r\n        mockEvents,\r\n        'Multi-stage Attack',\r\n        'Correlated attack involving multiple events'\r\n      );\r\n\r\n      expect(threat.name).toBe('Multi-stage Attack');\r\n      expect(threat.category).toBe('correlated-threat');\r\n      expect(threat.type).toBe('multi-stage-attack');\r\n      expect(threat.tags).toContain('correlated');\r\n      expect(threat.tags).toContain('multi-stage');\r\n      expect(threat.techniques).toHaveLength(1);\r\n      expect(threat.attributes.correlatedEventCount).toBe(3);\r\n    });\r\n\r\n    it('should infer severity from event count', () => {\r\n      const manyEvents = Array.from({ length: 12 }, (_, i) => ({ id: i.toString() }));\r\n      const threat = ThreatFactory.fromEventCorrelation(\r\n        manyEvents,\r\n        'Large Scale Attack',\r\n        'Attack with many correlated events'\r\n      );\r\n\r\n      expect(threat.severity).toBe(ThreatSeverity.CRITICAL);\r\n    });\r\n  });\r\n\r\n  describe('createManualThreat', () => {\r\n    it('should create manual threat with analyst information', () => {\r\n      const threat = ThreatFactory.createManualThreat(\r\n        'Manual Analysis Result',\r\n        'Threat identified through manual analysis',\r\n        ThreatSeverity.MEDIUM,\r\n        'manual-analysis',\r\n        'analyst-finding',\r\n        80,\r\n        '<EMAIL>'\r\n      );\r\n\r\n      expect(threat.name).toBe('Manual Analysis Result');\r\n      expect(threat.severity).toBe(ThreatSeverity.MEDIUM);\r\n      expect(threat.confidence).toBe(80);\r\n      expect(threat.tags).toContain('manual');\r\n      expect(threat.tags).toContain('analyst-created');\r\n      expect(threat.attributes.createdBy).toBe('<EMAIL>');\r\n      expect(threat.attributes.source).toBe('manual-analysis');\r\n    });\r\n  });\r\n\r\n  describe('helper methods', () => {\r\n    it('should map threat severity correctly', () => {\r\n      // Test string mapping\r\n      const criticalThreat = ThreatFactory.fromThreatIntelligence({\r\n        indicator: 'test',\r\n        indicatorType: 'ip',\r\n        severity: 'critical',\r\n      });\r\n      expect(criticalThreat.severity).toBe(ThreatSeverity.CRITICAL);\r\n\r\n      // Test numeric mapping\r\n      const highThreat = ThreatFactory.fromThreatIntelligence({\r\n        indicator: 'test',\r\n        indicatorType: 'ip',\r\n        severity: 8,\r\n      });\r\n      expect(highThreat.severity).toBe(ThreatSeverity.HIGH);\r\n\r\n      // Test unknown mapping\r\n      const unknownThreat = ThreatFactory.fromThreatIntelligence({\r\n        indicator: 'test',\r\n        indicatorType: 'ip',\r\n        severity: 'invalid',\r\n      });\r\n      expect(unknownThreat.severity).toBe(ThreatSeverity.UNKNOWN);\r\n    });\r\n\r\n    it('should infer IOC types correctly', () => {\r\n      const ipThreat = ThreatFactory.fromThreatIntelligence({\r\n        indicator: '***********',\r\n        indicatorType: 'ip_address',\r\n        severity: 'medium',\r\n      });\r\n      expect(ipThreat.indicators[0].type).toBe(IOCType.IP_ADDRESS);\r\n\r\n      const domainThreat = ThreatFactory.fromThreatIntelligence({\r\n        indicator: 'malicious.example.com',\r\n        indicatorType: 'domain',\r\n        severity: 'medium',\r\n      });\r\n      expect(domainThreat.indicators[0].type).toBe(IOCType.DOMAIN);\r\n\r\n      const hashThreat = ThreatFactory.fromThreatIntelligence({\r\n        indicator: 'abcdef1234567890abcdef1234567890',\r\n        indicatorType: 'md5',\r\n        severity: 'medium',\r\n      });\r\n      expect(hashThreat.indicators[0].type).toBe(IOCType.MD5_HASH);\r\n    });\r\n\r\n    it('should calculate confidence correctly', () => {\r\n      const highQualityData: ThreatIntelligenceData = {\r\n        indicator: 'test.com',\r\n        indicatorType: 'domain',\r\n        severity: 'high',\r\n        source: 'premium-feed',\r\n        description: 'Detailed analysis of this malicious domain with comprehensive attribution data',\r\n        attribution: {\r\n          actor: 'APT29',\r\n          campaign: 'Operation Stealth',\r\n        },\r\n      };\r\n\r\n      const threat = ThreatFactory.fromThreatIntelligence(highQualityData);\r\n      expect(threat.confidence).toBeGreaterThan(80);\r\n\r\n      const lowQualityData: ThreatIntelligenceData = {\r\n        indicator: 'test2.com',\r\n        indicatorType: 'domain',\r\n        severity: 'low',\r\n      };\r\n\r\n      const lowQualityThreat = ThreatFactory.fromThreatIntelligence(lowQualityData);\r\n      expect(lowQualityThreat.confidence).toBeLessThan(70);\r\n    });\r\n  });\r\n\r\n  describe('error handling', () => {\r\n    it('should handle invalid data gracefully', () => {\r\n      expect(() => {\r\n        ThreatFactory.create({\r\n          name: '',\r\n          description: 'Test',\r\n          severity: ThreatSeverity.LOW,\r\n          category: 'test',\r\n          type: 'test',\r\n          confidence: 50,\r\n        });\r\n      }).toThrow('Threat must have a name');\r\n\r\n      expect(() => {\r\n        ThreatFactory.create({\r\n          name: 'Test',\r\n          description: '',\r\n          severity: ThreatSeverity.LOW,\r\n          category: 'test',\r\n          type: 'test',\r\n          confidence: 50,\r\n        });\r\n      }).toThrow('Threat must have a description');\r\n\r\n      expect(() => {\r\n        ThreatFactory.create({\r\n          name: 'Test',\r\n          description: 'Test',\r\n          severity: ThreatSeverity.LOW,\r\n          category: 'test',\r\n          type: 'test',\r\n          confidence: 150,\r\n        });\r\n      }).toThrow('Threat confidence must be between 0 and 100');\r\n    });\r\n  });\r\n});"], "version": 3}