{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\repositories\\event.repository.ts", "mappings": "", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\repositories\\event.repository.ts"], "sourcesContent": ["import { AggregateRepository, UniqueEntityId, BaseSpecification } from '../../../../shared-kernel';\r\nimport { Event } from '../entities/event.entity';\r\nimport { EventType } from '../enums/event-type.enum';\r\nimport { EventSeverity } from '../enums/event-severity.enum';\r\nimport { EventStatus } from '../enums/event-status.enum';\r\nimport { EventProcessingStatus } from '../enums/event-processing-status.enum';\r\nimport { EventSource } from '../value-objects/event-metadata/event-source.value-object';\r\nimport { EventTimestamp } from '../value-objects/event-metadata/event-timestamp.value-object';\r\n\r\n/**\r\n * Event Repository Interface\r\n * \r\n * Provides specialized data access methods for Event aggregates.\r\n * Extends the base aggregate repository with event-specific query capabilities.\r\n * \r\n * Key responsibilities:\r\n * - Event persistence and retrieval\r\n * - Time-based event queries\r\n * - Source-based event filtering\r\n * - Severity and status-based queries\r\n * - Event correlation support\r\n * - Performance-optimized queries for real-time processing\r\n */\r\nexport interface EventRepository extends AggregateRepository<Event, UniqueEntityId> {\r\n  /**\r\n   * Find events within a specific time range\r\n   * Optimized for time-series queries with proper indexing\r\n   * \r\n   * @param startTime Start of the time range\r\n   * @param endTime End of the time range\r\n   * @param limit Optional limit for results (default: 1000)\r\n   * @returns Promise resolving to events within the time range\r\n   */\r\n  findByTimeRange(startTime: Date, endTime: Date, limit?: number): Promise<Event[]>;\r\n\r\n  /**\r\n   * Find events from a specific source\r\n   * Useful for source-specific analysis and filtering\r\n   * \r\n   * @param source The event source to filter by\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to events from the specified source\r\n   */\r\n  findBySource(source: EventSource, limit?: number): Promise<Event[]>;\r\n\r\n  /**\r\n   * Find events by severity level\r\n   * Critical for priority-based event processing\r\n   * \r\n   * @param severity The severity level to filter by\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to events with the specified severity\r\n   */\r\n  findBySeverity(severity: EventSeverity, limit?: number): Promise<Event[]>;\r\n\r\n  /**\r\n   * Find events by multiple severity levels\r\n   * Useful for filtering high-priority events (HIGH, CRITICAL)\r\n   * \r\n   * @param severities Array of severity levels to include\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to events with any of the specified severities\r\n   */\r\n  findBySeverities(severities: EventSeverity[], limit?: number): Promise<Event[]>;\r\n\r\n  /**\r\n   * Find events by status\r\n   * Essential for workflow management and event lifecycle tracking\r\n   * \r\n   * @param status The event status to filter by\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to events with the specified status\r\n   */\r\n  findByStatus(status: EventStatus, limit?: number): Promise<Event[]>;\r\n\r\n  /**\r\n   * Find events by processing status\r\n   * Critical for pipeline management and processing workflow\r\n   * \r\n   * @param processingStatus The processing status to filter by\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to events with the specified processing status\r\n   */\r\n  findByProcessingStatus(processingStatus: EventProcessingStatus, limit?: number): Promise<Event[]>;\r\n\r\n  /**\r\n   * Find events by type\r\n   * Useful for type-specific analysis and processing\r\n   * \r\n   * @param eventType The event type to filter by\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to events of the specified type\r\n   */\r\n  findByType(eventType: EventType, limit?: number): Promise<Event[]>;\r\n\r\n  /**\r\n   * Find events by correlation ID\r\n   * Essential for event correlation and relationship tracking\r\n   * \r\n   * @param correlationId The correlation ID to search for\r\n   * @returns Promise resolving to events with the specified correlation ID\r\n   */\r\n  findByCorrelationId(correlationId: string): Promise<Event[]>;\r\n\r\n  /**\r\n   * Find child events of a parent event\r\n   * Supports hierarchical event relationships\r\n   * \r\n   * @param parentEventId The parent event ID\r\n   * @returns Promise resolving to child events\r\n   */\r\n  findChildEvents(parentEventId: UniqueEntityId): Promise<Event[]>;\r\n\r\n  /**\r\n   * Find events with high risk scores\r\n   * Critical for risk-based prioritization\r\n   * \r\n   * @param minRiskScore Minimum risk score threshold (default: 70)\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to high-risk events\r\n   */\r\n  findHighRiskEvents(minRiskScore?: number, limit?: number): Promise<Event[]>;\r\n\r\n  /**\r\n   * Find recent events within a time window\r\n   * Optimized for real-time monitoring and alerting\r\n   * \r\n   * @param withinMs Time window in milliseconds (default: 1 hour)\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to recent events\r\n   */\r\n  findRecentEvents(withinMs?: number, limit?: number): Promise<Event[]>;\r\n\r\n  /**\r\n   * Find stale events that haven't been processed\r\n   * Important for identifying stuck or failed processing\r\n   * \r\n   * @param olderThanMs Age threshold in milliseconds (default: 24 hours)\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to stale events\r\n   */\r\n  findStaleEvents(olderThanMs?: number, limit?: number): Promise<Event[]>;\r\n\r\n  /**\r\n   * Find events that have failed processing\r\n   * Critical for error handling and retry mechanisms\r\n   * \r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to events with failed processing\r\n   */\r\n  findFailedEvents(limit?: number): Promise<Event[]>;\r\n\r\n  /**\r\n   * Find events that have exceeded maximum processing attempts\r\n   * Important for identifying events that need manual intervention\r\n   * \r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to events with exceeded attempts\r\n   */\r\n  findEventsWithExceededAttempts(limit?: number): Promise<Event[]>;\r\n\r\n  /**\r\n   * Find events by tags\r\n   * Supports flexible categorization and filtering\r\n   * \r\n   * @param tags Array of tags to search for\r\n   * @param matchAll Whether to match all tags (AND) or any tag (OR)\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to events with the specified tags\r\n   */\r\n  findByTags(tags: string[], matchAll?: boolean, limit?: number): Promise<Event[]>;\r\n\r\n  /**\r\n   * Find events with specific attributes\r\n   * Supports custom attribute-based queries\r\n   * \r\n   * @param attributes Key-value pairs to match\r\n   * @param limit Optional limit for results\r\n   * @returns Promise resolving to events with matching attributes\r\n   */\r\n  findByAttributes(attributes: Record<string, any>, limit?: number): Promise<Event[]>;\r\n\r\n  /**\r\n   * Count events by status\r\n   * Useful for dashboard metrics and monitoring\r\n   * \r\n   * @returns Promise resolving to status counts\r\n   */\r\n  countByStatus(): Promise<Record<EventStatus, number>>;\r\n\r\n  /**\r\n   * Count events by severity\r\n   * Important for severity distribution analysis\r\n   * \r\n   * @returns Promise resolving to severity counts\r\n   */\r\n  countBySeverity(): Promise<Record<EventSeverity, number>>;\r\n\r\n  /**\r\n   * Count events by processing status\r\n   * Critical for pipeline monitoring\r\n   * \r\n   * @returns Promise resolving to processing status counts\r\n   */\r\n  countByProcessingStatus(): Promise<Record<EventProcessingStatus, number>>;\r\n\r\n  /**\r\n   * Get event statistics for a time period\r\n   * Comprehensive metrics for reporting and analysis\r\n   * \r\n   * @param startTime Start of the time period\r\n   * @param endTime End of the time period\r\n   * @returns Promise resolving to event statistics\r\n   */\r\n  getEventStatistics(startTime: Date, endTime: Date): Promise<{\r\n    totalEvents: number;\r\n    eventsByType: Record<EventType, number>;\r\n    eventsBySeverity: Record<EventSeverity, number>;\r\n    eventsByStatus: Record<EventStatus, number>;\r\n    averageRiskScore: number;\r\n    highRiskEventCount: number;\r\n    processingMetrics: {\r\n      averageProcessingTime: number;\r\n      failedProcessingCount: number;\r\n      successfulProcessingCount: number;\r\n    };\r\n  }>;\r\n\r\n  /**\r\n   * Find events for correlation analysis\r\n   * Optimized query for event correlation algorithms\r\n   * \r\n   * @param timeWindow Time window for correlation in milliseconds\r\n   * @param eventTypes Optional event types to include\r\n   * @param minSeverity Optional minimum severity level\r\n   * @returns Promise resolving to events suitable for correlation\r\n   */\r\n  findEventsForCorrelation(\r\n    timeWindow: number,\r\n    eventTypes?: EventType[],\r\n    minSeverity?: EventSeverity\r\n  ): Promise<Event[]>;\r\n\r\n  /**\r\n   * Archive old events\r\n   * Moves old events to archive storage for performance optimization\r\n   * \r\n   * @param olderThanDays Age threshold in days\r\n   * @param batchSize Number of events to archive per batch\r\n   * @returns Promise resolving to the number of archived events\r\n   */\r\n  archiveOldEvents(olderThanDays: number, batchSize?: number): Promise<number>;\r\n\r\n  /**\r\n   * Bulk update event status\r\n   * Efficient batch operation for status updates\r\n   * \r\n   * @param eventIds Array of event IDs to update\r\n   * @param newStatus New status to set\r\n   * @param updatedBy Who is performing the update\r\n   * @returns Promise resolving to the number of updated events\r\n   */\r\n  bulkUpdateStatus(\r\n    eventIds: UniqueEntityId[],\r\n    newStatus: EventStatus,\r\n    updatedBy?: string\r\n  ): Promise<number>;\r\n\r\n  /**\r\n   * Find events requiring attention\r\n   * Combines multiple criteria to identify events needing immediate attention\r\n   * \r\n   * @param criteria Optional criteria for filtering\r\n   * @returns Promise resolving to events requiring attention\r\n   */\r\n  findEventsRequiringAttention(criteria?: {\r\n    maxAge?: number;\r\n    minSeverity?: EventSeverity;\r\n    minRiskScore?: number;\r\n    excludeStatuses?: EventStatus[];\r\n  }): Promise<Event[]>;\r\n}"], "version": 3}