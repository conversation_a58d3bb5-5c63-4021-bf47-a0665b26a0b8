ab386e7eb6a7831958b3afaf0b94e663
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThreatDetectedDomainEvent = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
const threat_severity_enum_1 = require("../enums/threat-severity.enum");
/**
 * Threat Detected Domain Event
 *
 * Raised when a security threat is detected and confirmed by the threat detection
 * system. This event represents a high-confidence identification of malicious
 * activity that requires immediate attention and response.
 *
 * Key characteristics:
 * - Represents confirmed threat detection
 * - Contains comprehensive threat analysis and context
 * - Includes impact assessment and response guidance
 * - Triggers immediate response and containment workflows
 *
 * Downstream processes triggered:
 * - Incident creation and response
 * - Automated containment actions
 * - Threat hunting and investigation
 * - Alert generation and notification
 * - Threat intelligence updates
 * - Security metrics and reporting
 */
class ThreatDetectedDomainEvent extends shared_kernel_1.BaseDomainEvent {
    constructor(aggregateId, eventData, options) {
        super(aggregateId, eventData, {
            eventVersion: 1,
            ...options,
            metadata: {
                eventType: 'ThreatDetected',
                domain: 'Security',
                aggregateType: 'Threat',
                processingStage: 'detection',
                ...options?.metadata,
            },
        });
    }
    /**
     * Get the threat ID
     */
    get threatId() {
        return this.eventData.threatId;
    }
    /**
     * Get the threat name
     */
    get threatName() {
        return this.eventData.name;
    }
    /**
     * Get the threat description
     */
    get description() {
        return this.eventData.description;
    }
    /**
     * Get the threat severity
     */
    get severity() {
        return this.eventData.severity;
    }
    /**
     * Get the threat category
     */
    get category() {
        return this.eventData.category;
    }
    /**
     * Get the threat type
     */
    get threatType() {
        return this.eventData.type;
    }
    /**
     * Get the threat signature
     */
    get signature() {
        return this.eventData.signature;
    }
    /**
     * Get the CVSS score
     */
    get cvssScore() {
        return this.eventData.cvssScore;
    }
    /**
     * Get the detection confidence
     */
    get confidence() {
        return this.eventData.confidence;
    }
    /**
     * Get the risk score
     */
    get riskScore() {
        return this.eventData.riskScore;
    }
    /**
     * Get when the threat was detected
     */
    get detectedAt() {
        return this.eventData.detectedAt;
    }
    /**
     * Get the detection method
     */
    get detectionMethod() {
        return this.eventData.detectionMethod;
    }
    /**
     * Get the detection source
     */
    get detectionSource() {
        return this.eventData.detectionSource;
    }
    /**
     * Get the source IP address
     */
    get sourceIp() {
        return this.eventData.sourceIp;
    }
    /**
     * Get the target IP address
     */
    get targetIp() {
        return this.eventData.targetIp;
    }
    /**
     * Get the affected assets
     */
    get affectedAssets() {
        return this.eventData.affectedAssets;
    }
    /**
     * Get the indicators of compromise
     */
    get indicators() {
        return this.eventData.indicators;
    }
    /**
     * Get the attack vectors
     */
    get attackVectors() {
        return this.eventData.attackVectors;
    }
    /**
     * Get the threat actor information
     */
    get threatActor() {
        return this.eventData.threatActor;
    }
    /**
     * Get the kill chain stage
     */
    get killChainStage() {
        return this.eventData.killChainStage;
    }
    /**
     * Get the potential impact
     */
    get potentialImpact() {
        return this.eventData.potentialImpact;
    }
    /**
     * Get related events
     */
    get relatedEvents() {
        return this.eventData.relatedEvents;
    }
    /**
     * Get threat intelligence context
     */
    get threatIntelligence() {
        return this.eventData.threatIntelligence;
    }
    /**
     * Get processing metadata
     */
    get processingMetadata() {
        return this.eventData.processingMetadata;
    }
    /**
     * Check if threat is critical severity
     */
    isCriticalSeverity() {
        return this.severity === threat_severity_enum_1.ThreatSeverity.CRITICAL;
    }
    /**
     * Check if threat is high severity or above
     */
    isHighSeverityOrAbove() {
        return [threat_severity_enum_1.ThreatSeverity.HIGH, threat_severity_enum_1.ThreatSeverity.CRITICAL].includes(this.severity);
    }
    /**
     * Check if threat has high confidence
     */
    hasHighConfidence() {
        return this.confidence >= 80;
    }
    /**
     * Check if threat has low confidence
     */
    hasLowConfidence() {
        return this.confidence < 60;
    }
    /**
     * Check if threat has high risk score
     */
    hasHighRiskScore() {
        return this.riskScore >= 70;
    }
    /**
     * Check if threat has critical risk score
     */
    hasCriticalRiskScore() {
        return this.riskScore >= 90;
    }
    /**
     * Check if threat affects critical assets
     */
    affectsCriticalAssets() {
        return this.affectedAssets.some(asset => asset.criticality === 'critical');
    }
    /**
     * Check if threat affects multiple assets
     */
    affectsMultipleAssets() {
        return this.affectedAssets.length > 1;
    }
    /**
     * Check if threat has multiple indicators
     */
    hasMultipleIndicators() {
        return this.indicators.length > 1;
    }
    /**
     * Check if threat has high-confidence indicators
     */
    hasHighConfidenceIndicators() {
        return this.indicators.some(indicator => indicator.confidence >= 80);
    }
    /**
     * Check if threat actor is known
     */
    hasKnownThreatActor() {
        return this.threatActor !== undefined;
    }
    /**
     * Check if threat actor is advanced
     */
    hasAdvancedThreatActor() {
        return this.threatActor?.sophistication === 'advanced';
    }
    /**
     * Check if threat is in late kill chain stage
     */
    isLateKillChainStage() {
        return ['command_control', 'actions_objectives'].includes(this.killChainStage);
    }
    /**
     * Check if threat has high business impact
     */
    hasHighBusinessImpact() {
        return ['high', 'critical'].includes(this.potentialImpact.businessImpact);
    }
    /**
     * Check if threat has critical business impact
     */
    hasCriticalBusinessImpact() {
        return this.potentialImpact.businessImpact === 'critical';
    }
    /**
     * Check if threat has geolocation data
     */
    hasGeolocationData() {
        return this.threatIntelligence?.geolocation !== undefined;
    }
    /**
     * Check if threat is part of known campaign
     */
    isPartOfKnownCampaign() {
        return (this.threatIntelligence?.campaigns?.length || 0) > 0;
    }
    /**
     * Check if threat requires immediate response
     */
    requiresImmediateResponse() {
        return this.isCriticalSeverity() ||
            (this.isHighSeverityOrAbove() && this.hasHighConfidence()) ||
            (this.hasCriticalRiskScore() && this.affectsCriticalAssets()) ||
            (this.hasAdvancedThreatActor() && this.isLateKillChainStage());
    }
    /**
     * Check if threat requires executive notification
     */
    requiresExecutiveNotification() {
        return this.isCriticalSeverity() &&
            (this.hasHighConfidence() || this.affectsCriticalAssets() || this.hasCriticalBusinessImpact());
    }
    /**
     * Check if threat requires SOC escalation
     */
    requiresSOCEscalation() {
        return this.isHighSeverityOrAbove() ||
            this.hasHighRiskScore() ||
            this.affectsCriticalAssets() ||
            this.hasAdvancedThreatActor();
    }
    /**
     * Get response priority
     */
    getResponsePriority() {
        if (this.requiresImmediateResponse()) {
            return 'critical';
        }
        if (this.isHighSeverityOrAbove() || this.hasHighRiskScore()) {
            return 'high';
        }
        if (this.severity === threat_severity_enum_1.ThreatSeverity.MEDIUM || this.affectsMultipleAssets()) {
            return 'medium';
        }
        return 'low';
    }
    /**
     * Get containment urgency in minutes
     */
    getContainmentUrgency() {
        if (this.requiresImmediateResponse())
            return 15; // 15 minutes
        if (this.isCriticalSeverity())
            return 30; // 30 minutes
        if (this.isHighSeverityOrAbove())
            return 120; // 2 hours
        if (this.severity === threat_severity_enum_1.ThreatSeverity.MEDIUM)
            return 480; // 8 hours
        return 1440; // 24 hours
    }
    /**
     * Get investigation timeline in hours
     */
    getInvestigationTimeline() {
        if (this.requiresImmediateResponse())
            return 2; // 2 hours
        if (this.isCriticalSeverity())
            return 8; // 8 hours
        if (this.isHighSeverityOrAbove())
            return 24; // 24 hours
        if (this.severity === threat_severity_enum_1.ThreatSeverity.MEDIUM)
            return 72; // 3 days
        return 168; // 7 days
    }
    /**
     * Get recommended containment actions
     */
    getRecommendedContainmentActions() {
        const actions = [];
        if (this.requiresImmediateResponse()) {
            actions.push('isolate_affected_systems', 'block_malicious_ips', 'disable_compromised_accounts');
        }
        if (this.sourceIp) {
            actions.push('block_source_ip', 'analyze_network_traffic');
        }
        if (this.affectsCriticalAssets()) {
            actions.push('emergency_asset_isolation', 'backup_critical_data');
        }
        if (this.hasAdvancedThreatActor()) {
            actions.push('advanced_forensics', 'threat_hunting', 'intelligence_gathering');
        }
        if (this.isLateKillChainStage()) {
            actions.push('damage_assessment', 'data_exfiltration_check', 'system_integrity_verification');
        }
        return actions;
    }
    /**
     * Get notification targets
     */
    getNotificationTargets() {
        const targets = ['security_operations_center'];
        if (this.requiresExecutiveNotification()) {
            targets.push('security_leadership', 'executive_team', 'board_of_directors');
        }
        if (this.requiresSOCEscalation()) {
            targets.push('incident_response_team', 'threat_analysts');
        }
        if (this.affectsCriticalAssets()) {
            targets.push('asset_owners', 'business_stakeholders');
        }
        if (this.hasAdvancedThreatActor()) {
            targets.push('threat_intelligence_team', 'external_partners');
        }
        if (this.hasCriticalBusinessImpact()) {
            targets.push('business_continuity_team', 'communications_team');
        }
        return [...new Set(targets)]; // Remove duplicates
    }
    /**
     * Get threat detection metrics
     */
    getThreatDetectionMetrics() {
        return {
            threatId: this.threatId,
            name: this.threatName,
            severity: this.severity,
            category: this.category,
            type: this.threatType,
            confidence: this.confidence,
            riskScore: this.riskScore,
            detectionMethod: this.detectionMethod,
            detectionSource: this.detectionSource,
            affectedAssetsCount: this.affectedAssets.length,
            indicatorsCount: this.indicators.length,
            killChainStage: this.killChainStage,
            responsePriority: this.getResponsePriority(),
            containmentUrgency: this.getContainmentUrgency(),
            investigationTimeline: this.getInvestigationTimeline(),
            requiresImmediateResponse: this.requiresImmediateResponse(),
            affectsCriticalAssets: this.affectsCriticalAssets(),
            hasAdvancedThreatActor: this.hasAdvancedThreatActor(),
            processingDuration: this.processingMetadata.processingDuration,
        };
    }
    /**
     * Convert to integration event for external systems
     */
    toIntegrationEvent() {
        const indicatorTypes = [...new Set(this.indicators.map(i => i.type))];
        const highConfidenceIndicators = this.indicators.filter(i => i.confidence >= 80).length;
        return {
            eventType: 'ThreatDetected',
            version: '1.0',
            timestamp: this.occurredOn.toISOString(),
            data: {
                threatId: this.threatId,
                threat: {
                    name: this.threatName,
                    description: this.description,
                    severity: this.severity,
                    category: this.category,
                    type: this.threatType,
                    confidence: this.confidence,
                    riskScore: this.riskScore,
                    detectedAt: this.detectedAt.toISOString(),
                    detectionMethod: this.detectionMethod,
                    detectionSource: this.detectionSource,
                },
                assets: {
                    affected: this.affectedAssets,
                    criticalAssetsAffected: this.affectsCriticalAssets(),
                },
                indicators: {
                    count: this.indicators.length,
                    types: indicatorTypes,
                    highConfidenceCount: highConfidenceIndicators,
                },
                attack: {
                    vectors: this.attackVectors,
                    killChainStage: this.killChainStage,
                    threatActor: this.threatActor ? {
                        name: this.threatActor.name,
                        sophistication: this.threatActor.sophistication,
                    } : undefined,
                },
                impact: {
                    confidentiality: this.potentialImpact.confidentiality,
                    integrity: this.potentialImpact.integrity,
                    availability: this.potentialImpact.availability,
                    businessImpact: this.potentialImpact.businessImpact,
                },
                intelligence: this.threatIntelligence ? {
                    campaigns: this.threatIntelligence.campaigns,
                    ttps: this.threatIntelligence.ttps,
                    mitreAttackIds: this.threatIntelligence.mitreAttackIds,
                    geolocation: this.threatIntelligence.geolocation ? {
                        country: this.threatIntelligence.geolocation.country,
                        region: this.threatIntelligence.geolocation.region,
                    } : undefined,
                } : undefined,
                response: {
                    priority: this.getResponsePriority(),
                    containmentUrgency: this.getContainmentUrgency(),
                    investigationTimeline: this.getInvestigationTimeline(),
                    requiresImmediateResponse: this.requiresImmediateResponse(),
                    requiresExecutiveNotification: this.requiresExecutiveNotification(),
                    recommendedActions: this.getRecommendedContainmentActions(),
                    notificationTargets: this.getNotificationTargets(),
                },
            },
            metadata: {
                correlationId: this.correlationId,
                causationId: this.causationId,
                domain: 'Security',
                aggregateType: 'Threat',
                processingStage: 'detection',
            },
        };
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            ...super.toJSON(),
            eventData: this.eventData,
            analysis: {
                isCriticalSeverity: this.isCriticalSeverity(),
                isHighSeverityOrAbove: this.isHighSeverityOrAbove(),
                hasHighConfidence: this.hasHighConfidence(),
                hasLowConfidence: this.hasLowConfidence(),
                hasHighRiskScore: this.hasHighRiskScore(),
                hasCriticalRiskScore: this.hasCriticalRiskScore(),
                affectsCriticalAssets: this.affectsCriticalAssets(),
                affectsMultipleAssets: this.affectsMultipleAssets(),
                hasMultipleIndicators: this.hasMultipleIndicators(),
                hasHighConfidenceIndicators: this.hasHighConfidenceIndicators(),
                hasKnownThreatActor: this.hasKnownThreatActor(),
                hasAdvancedThreatActor: this.hasAdvancedThreatActor(),
                isLateKillChainStage: this.isLateKillChainStage(),
                hasHighBusinessImpact: this.hasHighBusinessImpact(),
                hasCriticalBusinessImpact: this.hasCriticalBusinessImpact(),
                hasGeolocationData: this.hasGeolocationData(),
                isPartOfKnownCampaign: this.isPartOfKnownCampaign(),
                requiresImmediateResponse: this.requiresImmediateResponse(),
                requiresExecutiveNotification: this.requiresExecutiveNotification(),
                requiresSOCEscalation: this.requiresSOCEscalation(),
                responsePriority: this.getResponsePriority(),
                containmentUrgency: this.getContainmentUrgency(),
                investigationTimeline: this.getInvestigationTimeline(),
                recommendedContainmentActions: this.getRecommendedContainmentActions(),
                notificationTargets: this.getNotificationTargets(),
                threatDetectionMetrics: this.getThreatDetectionMetrics(),
            },
        };
    }
    /**
     * Create from JSON representation
     */
    static fromJSON(json) {
        return new ThreatDetectedDomainEvent(shared_kernel_1.UniqueEntityId.fromString(json.aggregateId), json.eventData, {
            eventId: shared_kernel_1.UniqueEntityId.fromString(json.eventId),
            occurredOn: new Date(json.occurredOn),
            eventVersion: json.eventVersion,
            correlationId: json.correlationId,
            causationId: json.causationId,
            metadata: json.metadata,
        });
    }
    /**
     * Get human-readable description
     */
    getDescription() {
        const severityText = this.severity.toLowerCase();
        const confidenceText = this.hasHighConfidence() ? 'high confidence' : 'moderate confidence';
        const assetText = this.affectsCriticalAssets() ? 'affecting critical assets' :
            this.affectsMultipleAssets() ? `affecting ${this.affectedAssets.length} assets` :
                'affecting single asset';
        const actorText = this.hasKnownThreatActor() ? ` by ${this.threatActor.name}` : '';
        return `${severityText} severity threat "${this.threatName}" detected with ${confidenceText}, ${assetText}${actorText}`;
    }
    /**
     * Get event summary for logging
     */
    getSummary() {
        return {
            eventType: 'ThreatDetected',
            threatId: this.threatId,
            name: this.threatName,
            severity: this.severity,
            category: this.category,
            confidence: this.confidence,
            riskScore: this.riskScore,
            detectionMethod: this.detectionMethod,
            affectedAssetsCount: this.affectedAssets.length,
            requiresImmediateResponse: this.requiresImmediateResponse(),
            responsePriority: this.getResponsePriority(),
            killChainStage: this.killChainStage,
            timestamp: this.occurredOn.toISOString(),
        };
    }
}
exports.ThreatDetectedDomainEvent = ThreatDetectedDomainEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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