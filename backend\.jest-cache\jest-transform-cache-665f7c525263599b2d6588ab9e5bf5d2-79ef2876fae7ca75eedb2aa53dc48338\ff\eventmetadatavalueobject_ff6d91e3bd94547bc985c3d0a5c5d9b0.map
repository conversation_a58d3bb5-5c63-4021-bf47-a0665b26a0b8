{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\event-metadata\\event-metadata.value-object.ts", "mappings": ";;;AAAA,gEAA+D;AAC/D,iFAAgE;AAChE,2EAA0D;AAiC1D;;;;;;;;;;;;GAYG;AACH,MAAa,aAAc,SAAQ,+BAAmC;IACpE,YAAY,KAAyB;QACnC,KAAK,CAAC,KAAK,CAAC,CAAC;IACf,CAAC;IAES,QAAQ;QAChB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,6CAA6C;QAC7C,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC;YACvF,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;QACrF,CAAC;QAED,kCAAkC;QAClC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,KAAK,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;YACrE,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;QAED,uCAAuC;QACvC,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzE,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACjE,CAAC;QAED,sCAAsC;QACtC,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,aAAa,KAAK,SAAS,EAAE,CAAC;YAC7D,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC;YAChE,IAAI,aAAa,GAAG,CAAC,IAAI,aAAa,GAAG,IAAI,EAAE,CAAC,CAAC,eAAe;gBAC9D,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM,CACX,SAAyB,EACzB,MAAmB,EACnB,OAAmE;QAEnE,OAAO,IAAI,aAAa,CAAC;YACvB,SAAS;YACT,MAAM;YACN,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS,CACd,MAAmB,EACnB,OAAmE;QAEnE,OAAO,aAAa,CAAC,MAAM,CAAC,6CAAc,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAU,CACf,UAA2B,EAC3B,gBAAwB,EACxB,SAAgB,EAChB,OAAmE;QAEnE,MAAM,cAAc,GAAG,SAAS,CAAC,CAAC,CAAC,6CAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,6CAAc,CAAC,MAAM,EAAE,CAAC;QAChG,MAAM,WAAW,GAAG,uCAAW,CAAC,MAAM,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;QAErE,OAAO,aAAa,CAAC,MAAM,CAAC,cAAc,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,EAAE,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,GAAW;QACxB,OAAO,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,cAAc,CAAU,GAAW;QACjC,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAM,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,QAAQ,IAAI,QAAQ,CAAC;IAC3D,CAAC;IAED;;OAEG;IACH,uBAAuB;QACrB,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,iBAAiB,IAAI,KAAK,CAAC;IACjE,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,cAAc,IAAI,KAAK,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,eAAe,IAAI,KAAK,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,aAAa,IAAI,GAAG,CAAC,CAAC,iBAAiB;IAC7E,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACpC,OAAO,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,UAAU,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,WAAW,EAAE,KAAK,UAAU,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,WAAmB,MAAM;QAChC,OAAO,IAAI,CAAC,MAAM,EAAE,IAAI,QAAQ,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,cAAsB,QAAQ;QACpC,OAAO,IAAI,CAAC,MAAM,EAAE,GAAG,WAAW,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,YAAiC;QAChD,OAAO,IAAI,aAAa,CAAC;YACvB,GAAG,IAAI,CAAC,MAAM;YACd,YAAY,EAAE,EAAE,GAAG,IAAI,CAAC,YAAY,EAAE,GAAG,YAAY,EAAE;SACxD,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,KAA4C;QAC9D,OAAO,IAAI,aAAa,CAAC;YACvB,GAAG,IAAI,CAAC,MAAM;YACd,eAAe,EAAE,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,GAAG,KAAK,EAAE;SAC9D,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,QAAgD;QAC3D,OAAO,IAAI,CAAC,mBAAmB,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,QAAgB;QAC3B,OAAO,IAAI,aAAa,CAAC;YACvB,GAAG,IAAI,CAAC,MAAM;YACd,QAAQ;SACT,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,SAAiB;QAC7B,OAAO,IAAI,aAAa,CAAC;YACvB,GAAG,IAAI,CAAC,MAAM;YACd,SAAS;SACV,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,cAAsB;QACpC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YAC1B,OAAO,KAAK,CAAC,CAAC,gCAAgC;QAChD,CAAC;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,cAAc,CAAC,WAAW,EAAE,CAAC;IAC7E,CAAC;IAED;;OAEG;IACH,UAAU;QASR,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;YACvC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;YAC9B,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;YAC5B,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE;YAC5B,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE;YAClB,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ;YACnC,gBAAgB,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM;SACxD,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;YAClC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAC5B,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe;YAC5C,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa;YACxC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;YAChC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;YAC9B,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY;YACtC,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,gBAAgB;YAC9C,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe;YAC5C,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE;SAC3B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,IAAyB;QACvC,OAAO,IAAI,aAAa,CAAC;YACvB,SAAS,EAAE,6CAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC;YAClD,MAAM,EAAE,uCAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;YACzC,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,eAAe,EAAE,IAAI,CAAC,eAAe;SACtC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAqB;QACjC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,CACL,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC;YAChC,IAAI,CAAC,MAAM,CAAC,eAAe,KAAK,KAAK,CAAC,MAAM,CAAC,eAAe;YAC5D,IAAI,CAAC,MAAM,CAAC,aAAa,KAAK,KAAK,CAAC,MAAM,CAAC,aAAa;YACxD,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,KAAK,CAAC,MAAM,CAAC,QAAQ,CAC/C,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,OAAO,iBAAiB,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC;IAClF,CAAC;CACF;AAhXD,sCAgXC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\event-metadata\\event-metadata.value-object.ts"], "sourcesContent": ["import { BaseValueObject } from '../../../../../shared-kernel';\r\nimport { EventTimestamp } from './event-timestamp.value-object';\r\nimport { EventSource } from './event-source.value-object';\r\nimport { EventSourceType } from '../../enums/event-source-type.enum';\r\n\r\n/**\r\n * Event Metadata Properties\r\n */\r\nexport interface EventMetadataProps {\r\n  /** When the event occurred */\r\n  timestamp: EventTimestamp;\r\n  /** Source of the event */\r\n  source: EventSource;\r\n  /** Original event ID from the source system */\r\n  originalEventId?: string;\r\n  /** Event format/schema version */\r\n  schemaVersion?: string;\r\n  /** Size of the original event in bytes */\r\n  eventSize?: number;\r\n  /** Checksum/hash of the original event */\r\n  checksum?: string;\r\n  /** Additional custom metadata */\r\n  customFields?: Record<string, any>;\r\n  /** Event collection method */\r\n  collectionMethod?: 'push' | 'pull' | 'streaming' | 'batch';\r\n  /** Processing hints for the event pipeline */\r\n  processingHints?: {\r\n    priority?: 'low' | 'normal' | 'high' | 'critical';\r\n    skipNormalization?: boolean;\r\n    skipEnrichment?: boolean;\r\n    skipCorrelation?: boolean;\r\n    retentionDays?: number;\r\n  };\r\n}\r\n\r\n/**\r\n * Event Metadata Value Object\r\n * \r\n * Encapsulates metadata information about security events.\r\n * Provides context about the event's origin, timing, and processing requirements.\r\n * \r\n * Key features:\r\n * - Immutable metadata container\r\n * - Validation of metadata consistency\r\n * - Processing hints for pipeline optimization\r\n * - Extensible custom fields support\r\n * - Event integrity verification\r\n */\r\nexport class EventMetadata extends BaseValueObject<EventMetadataProps> {\r\n  constructor(props: EventMetadataProps) {\r\n    super(props);\r\n  }\r\n\r\n  protected validate(): void {\r\n    if (!this._value.timestamp) {\r\n      throw new Error('Event metadata must have a timestamp');\r\n    }\r\n\r\n    if (!this._value.source) {\r\n      throw new Error('Event metadata must have a source');\r\n    }\r\n\r\n    // Validate schema version format if provided\r\n    if (this._value.schemaVersion && !/^\\d+\\.\\d+(\\.\\d+)?$/.test(this._value.schemaVersion)) {\r\n      throw new Error('Schema version must be in semantic version format (e.g., 1.0.0)');\r\n    }\r\n\r\n    // Validate event size if provided\r\n    if (this._value.eventSize !== undefined && this._value.eventSize < 0) {\r\n      throw new Error('Event size cannot be negative');\r\n    }\r\n\r\n    // Validate checksum format if provided\r\n    if (this._value.checksum && !/^[a-fA-F0-9]+$/.test(this._value.checksum)) {\r\n      throw new Error('Checksum must be a valid hexadecimal string');\r\n    }\r\n\r\n    // Validate retention days if provided\r\n    if (this._value.processingHints?.retentionDays !== undefined) {\r\n      const retentionDays = this._value.processingHints.retentionDays;\r\n      if (retentionDays < 1 || retentionDays > 3650) { // Max 10 years\r\n        throw new Error('Retention days must be between 1 and 3650');\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create event metadata with minimal required information\r\n   */\r\n  static create(\r\n    timestamp: EventTimestamp,\r\n    source: EventSource,\r\n    options?: Partial<Omit<EventMetadataProps, 'timestamp' | 'source'>>\r\n  ): EventMetadata {\r\n    return new EventMetadata({\r\n      timestamp,\r\n      source,\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create event metadata for current time\r\n   */\r\n  static createNow(\r\n    source: EventSource,\r\n    options?: Partial<Omit<EventMetadataProps, 'timestamp' | 'source'>>\r\n  ): EventMetadata {\r\n    return EventMetadata.create(EventTimestamp.create(), source, options);\r\n  }\r\n\r\n  /**\r\n   * Create event metadata from source system information\r\n   */\r\n  static fromSource(\r\n    sourceType: EventSourceType,\r\n    sourceIdentifier: string,\r\n    timestamp?: Date,\r\n    options?: Partial<Omit<EventMetadataProps, 'timestamp' | 'source'>>\r\n  ): EventMetadata {\r\n    const eventTimestamp = timestamp ? EventTimestamp.fromDate(timestamp) : EventTimestamp.create();\r\n    const eventSource = EventSource.create(sourceType, sourceIdentifier);\r\n    \r\n    return EventMetadata.create(eventTimestamp, eventSource, options);\r\n  }\r\n\r\n  /**\r\n   * Get the event timestamp\r\n   */\r\n  get timestamp(): EventTimestamp {\r\n    return this._value.timestamp;\r\n  }\r\n\r\n  /**\r\n   * Get the event source\r\n   */\r\n  get source(): EventSource {\r\n    return this._value.source;\r\n  }\r\n\r\n  /**\r\n   * Get the original event ID from source system\r\n   */\r\n  get originalEventId(): string | undefined {\r\n    return this._value.originalEventId;\r\n  }\r\n\r\n  /**\r\n   * Get the schema version\r\n   */\r\n  get schemaVersion(): string | undefined {\r\n    return this._value.schemaVersion;\r\n  }\r\n\r\n  /**\r\n   * Get the event size in bytes\r\n   */\r\n  get eventSize(): number | undefined {\r\n    return this._value.eventSize;\r\n  }\r\n\r\n  /**\r\n   * Get the event checksum\r\n   */\r\n  get checksum(): string | undefined {\r\n    return this._value.checksum;\r\n  }\r\n\r\n  /**\r\n   * Get custom fields\r\n   */\r\n  get customFields(): Record<string, any> {\r\n    return this._value.customFields || {};\r\n  }\r\n\r\n  /**\r\n   * Get collection method\r\n   */\r\n  get collectionMethod(): string | undefined {\r\n    return this._value.collectionMethod;\r\n  }\r\n\r\n  /**\r\n   * Get processing hints\r\n   */\r\n  get processingHints(): EventMetadataProps['processingHints'] {\r\n    return this._value.processingHints;\r\n  }\r\n\r\n  /**\r\n   * Check if event has a specific custom field\r\n   */\r\n  hasCustomField(key: string): boolean {\r\n    return key in this.customFields;\r\n  }\r\n\r\n  /**\r\n   * Get a custom field value\r\n   */\r\n  getCustomField<T = any>(key: string): T | undefined {\r\n    return this.customFields[key] as T;\r\n  }\r\n\r\n  /**\r\n   * Get processing priority\r\n   */\r\n  getPriority(): 'low' | 'normal' | 'high' | 'critical' {\r\n    return this._value.processingHints?.priority || 'normal';\r\n  }\r\n\r\n  /**\r\n   * Check if normalization should be skipped\r\n   */\r\n  shouldSkipNormalization(): boolean {\r\n    return this._value.processingHints?.skipNormalization || false;\r\n  }\r\n\r\n  /**\r\n   * Check if enrichment should be skipped\r\n   */\r\n  shouldSkipEnrichment(): boolean {\r\n    return this._value.processingHints?.skipEnrichment || false;\r\n  }\r\n\r\n  /**\r\n   * Check if correlation should be skipped\r\n   */\r\n  shouldSkipCorrelation(): boolean {\r\n    return this._value.processingHints?.skipCorrelation || false;\r\n  }\r\n\r\n  /**\r\n   * Get retention period in days\r\n   */\r\n  getRetentionDays(): number {\r\n    return this._value.processingHints?.retentionDays || 365; // Default 1 year\r\n  }\r\n\r\n  /**\r\n   * Check if event is high priority\r\n   */\r\n  isHighPriority(): boolean {\r\n    const priority = this.getPriority();\r\n    return priority === 'high' || priority === 'critical';\r\n  }\r\n\r\n  /**\r\n   * Check if event is critical priority\r\n   */\r\n  isCriticalPriority(): boolean {\r\n    return this.getPriority() === 'critical';\r\n  }\r\n\r\n  /**\r\n   * Get event age in milliseconds\r\n   */\r\n  getAge(): number {\r\n    return this.timestamp.getAge();\r\n  }\r\n\r\n  /**\r\n   * Check if event is recent (within specified time)\r\n   */\r\n  isRecent(withinMs: number = 300000): boolean { // Default 5 minutes\r\n    return this.getAge() <= withinMs;\r\n  }\r\n\r\n  /**\r\n   * Check if event is stale (older than specified time)\r\n   */\r\n  isStale(olderThanMs: number = 86400000): boolean { // Default 24 hours\r\n    return this.getAge() > olderThanMs;\r\n  }\r\n\r\n  /**\r\n   * Create a new metadata with updated custom fields\r\n   */\r\n  withCustomFields(customFields: Record<string, any>): EventMetadata {\r\n    return new EventMetadata({\r\n      ...this._value,\r\n      customFields: { ...this.customFields, ...customFields },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a new metadata with updated processing hints\r\n   */\r\n  withProcessingHints(hints: EventMetadataProps['processingHints']): EventMetadata {\r\n    return new EventMetadata({\r\n      ...this._value,\r\n      processingHints: { ...this._value.processingHints, ...hints },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a new metadata with updated priority\r\n   */\r\n  withPriority(priority: 'low' | 'normal' | 'high' | 'critical'): EventMetadata {\r\n    return this.withProcessingHints({ priority });\r\n  }\r\n\r\n  /**\r\n   * Create a new metadata with checksum\r\n   */\r\n  withChecksum(checksum: string): EventMetadata {\r\n    return new EventMetadata({\r\n      ...this._value,\r\n      checksum,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a new metadata with event size\r\n   */\r\n  withEventSize(eventSize: number): EventMetadata {\r\n    return new EventMetadata({\r\n      ...this._value,\r\n      eventSize,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Verify event integrity using checksum\r\n   */\r\n  verifyIntegrity(actualChecksum: string): boolean {\r\n    if (!this._value.checksum) {\r\n      return false; // No checksum to verify against\r\n    }\r\n    return this._value.checksum.toLowerCase() === actualChecksum.toLowerCase();\r\n  }\r\n\r\n  /**\r\n   * Get metadata summary for logging\r\n   */\r\n  getSummary(): {\r\n    timestamp: string;\r\n    source: string;\r\n    sourceType: string;\r\n    priority: string;\r\n    age: number;\r\n    hasChecksum: boolean;\r\n    customFieldCount: number;\r\n  } {\r\n    return {\r\n      timestamp: this.timestamp.toISOString(),\r\n      source: this.source.identifier,\r\n      sourceType: this.source.type,\r\n      priority: this.getPriority(),\r\n      age: this.getAge(),\r\n      hasChecksum: !!this._value.checksum,\r\n      customFieldCount: Object.keys(this.customFields).length,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      timestamp: this.timestamp.toJSON(),\r\n      source: this.source.toJSON(),\r\n      originalEventId: this._value.originalEventId,\r\n      schemaVersion: this._value.schemaVersion,\r\n      eventSize: this._value.eventSize,\r\n      checksum: this._value.checksum,\r\n      customFields: this._value.customFields,\r\n      collectionMethod: this._value.collectionMethod,\r\n      processingHints: this._value.processingHints,\r\n      summary: this.getSummary(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create EventMetadata from JSON\r\n   */\r\n  static fromJSON(json: Record<string, any>): EventMetadata {\r\n    return new EventMetadata({\r\n      timestamp: EventTimestamp.fromJSON(json.timestamp),\r\n      source: EventSource.fromJSON(json.source),\r\n      originalEventId: json.originalEventId,\r\n      schemaVersion: json.schemaVersion,\r\n      eventSize: json.eventSize,\r\n      checksum: json.checksum,\r\n      customFields: json.customFields,\r\n      collectionMethod: json.collectionMethod,\r\n      processingHints: json.processingHints,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Compare metadata for equality\r\n   */\r\n  public equals(other?: EventMetadata): boolean {\r\n    if (!other) {\r\n      return false;\r\n    }\r\n\r\n    if (this === other) {\r\n      return true;\r\n    }\r\n\r\n    return (\r\n      this.timestamp.equals(other.timestamp) &&\r\n      this.source.equals(other.source) &&\r\n      this._value.originalEventId === other._value.originalEventId &&\r\n      this._value.schemaVersion === other._value.schemaVersion &&\r\n      this._value.checksum === other._value.checksum\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get string representation\r\n   */\r\n  public toString(): string {\r\n    return `EventMetadata(${this.source.toString()}, ${this.timestamp.toString()})`;\r\n  }\r\n}\r\n"], "version": 3}