7a4cfe1719101bad33185876c6861938
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReportExecution = void 0;
const typeorm_1 = require("typeorm");
const report_definition_entity_1 = require("./report-definition.entity");
/**
 * Report Execution Entity
 *
 * Tracks individual report execution instances including:
 * - Execution status and progress tracking
 * - Performance metrics and timing data
 * - Generated output and export information
 * - Error handling and debugging information
 * - User context and access audit trail
 * - Resource usage and optimization metrics
 * - Cache management and invalidation tracking
 */
let ReportExecution = class ReportExecution {
    // Business Logic Methods
    /**
     * Calculate execution duration
     */
    getExecutionDuration() {
        if (!this.startedAt)
            return 0;
        const endTime = this.completedAt || new Date();
        return endTime.getTime() - this.startedAt.getTime();
    }
    /**
     * Check if execution is still running
     */
    isRunning() {
        return this.status === 'running' || this.status === 'pending';
    }
    /**
     * Check if execution completed successfully
     */
    isSuccessful() {
        return this.status === 'completed';
    }
    /**
     * Check if execution failed
     */
    isFailed() {
        return this.status === 'failed' || this.status === 'timeout' || this.status === 'cancelled';
    }
    /**
     * Check if execution can be retried
     */
    canRetry() {
        if (!this.isFailed())
            return false;
        if (this.errorDetails?.isRetryable === false)
            return false;
        const maxRetries = 3;
        const retryCount = this.errorDetails?.retryCount || 0;
        return retryCount < maxRetries;
    }
    /**
     * Get execution efficiency score
     */
    getEfficiencyScore() {
        if (!this.isSuccessful() || !this.dataMetrics || !this.performanceMetrics) {
            return 0;
        }
        let score = 100;
        // Deduct for high execution time
        const executionTimeScore = Math.max(0, 100 - (this.executionTimeMs / 1000 / 60)); // Deduct 1 point per minute
        score = score * 0.3 + executionTimeScore * 0.3;
        // Add for high cache hit rate
        const cacheScore = this.dataMetrics.cacheHitRate * 100;
        score = score * 0.7 + cacheScore * 0.3;
        // Deduct for high memory usage
        const memoryScore = Math.max(0, 100 - (this.performanceMetrics.memoryUsage.peak / 1024 / 1024 / 100)); // Deduct 1 point per 100MB
        score = score * 0.8 + memoryScore * 0.2;
        return Math.round(Math.max(0, Math.min(100, score)));
    }
    /**
     * Get data quality score
     */
    getDataQualityScore() {
        if (!this.qualityMetrics?.dataQuality)
            return 0;
        const { completeness, accuracy, consistency, timeliness } = this.qualityMetrics.dataQuality;
        return Math.round((completeness + accuracy + consistency + timeliness) / 4);
    }
    /**
     * Update progress
     */
    updateProgress(percentage, step) {
        this.progressPercentage = Math.max(0, Math.min(100, percentage));
        if (step) {
            this.currentStep = step;
        }
        this.updatedAt = new Date();
    }
    /**
     * Mark as completed
     */
    markCompleted(outputMetadata) {
        this.status = 'completed';
        this.completedAt = new Date();
        this.progressPercentage = 100;
        this.currentStep = 'Completed';
        this.executionTimeMs = this.getExecutionDuration();
        if (outputMetadata) {
            this.outputMetadata = outputMetadata;
        }
    }
    /**
     * Mark as failed
     */
    markFailed(error, step) {
        this.status = 'failed';
        this.completedAt = new Date();
        this.executionTimeMs = this.getExecutionDuration();
        this.errorDetails = {
            errorCode: error.name || 'UNKNOWN_ERROR',
            errorMessage: error.message,
            stackTrace: error.stack,
            failedStep: step || this.currentStep,
            retryCount: (this.errorDetails?.retryCount || 0) + 1,
            lastRetryAt: new Date(),
            isRetryable: this.determineIfRetryable(error),
            troubleshootingHints: this.generateTroubleshootingHints(error),
        };
    }
    /**
     * Cancel execution
     */
    cancel(reason) {
        this.status = 'cancelled';
        this.completedAt = new Date();
        this.executionTimeMs = this.getExecutionDuration();
        this.currentStep = `Cancelled: ${reason || 'User requested'}`;
    }
    /**
     * Add security event to audit trail
     */
    addSecurityEvent(event, details) {
        this.auditTrail.securityEvents.push({
            event,
            timestamp: new Date(),
            details,
        });
    }
    /**
     * Update delivery status
     */
    updateDeliveryStatus(status, recipient, error) {
        if (!this.deliveryStatus) {
            this.deliveryStatus = {
                method: 'unknown',
                recipients: [],
                deliveryAttempts: 0,
                status: 'pending',
            };
        }
        this.deliveryStatus.status = status;
        this.deliveryStatus.deliveryAttempts++;
        this.deliveryStatus.lastAttemptAt = new Date();
        if (status === 'delivered') {
            this.deliveryStatus.deliveredAt = new Date();
            if (recipient) {
                this.deliveryStatus.confirmations = this.deliveryStatus.confirmations || [];
                this.deliveryStatus.confirmations.push({
                    recipient,
                    confirmedAt: new Date(),
                    method: this.deliveryStatus.method,
                });
            }
        }
        if (error) {
            this.deliveryStatus.errors = this.deliveryStatus.errors || [];
            this.deliveryStatus.errors.push(error);
        }
    }
    /**
     * Get execution summary
     */
    getSummary() {
        return {
            id: this.id,
            status: this.status,
            duration: this.executionTimeMs || this.getExecutionDuration(),
            recordsProcessed: this.dataMetrics?.recordsProcessed || 0,
            efficiencyScore: this.getEfficiencyScore(),
            dataQualityScore: this.getDataQualityScore(),
            errorMessage: this.errorDetails?.errorMessage,
        };
    }
    // Private helper methods
    determineIfRetryable(error) {
        const nonRetryableErrors = [
            'PERMISSION_DENIED',
            'INVALID_CONFIGURATION',
            'DATA_NOT_FOUND',
            'VALIDATION_ERROR',
        ];
        return !nonRetryableErrors.includes(error.name);
    }
    generateTroubleshootingHints(error) {
        const hints = [];
        switch (error.name) {
            case 'TIMEOUT_ERROR':
                hints.push('Consider reducing the data range or adding filters');
                hints.push('Check if data sources are responding slowly');
                break;
            case 'MEMORY_ERROR':
                hints.push('Reduce the amount of data being processed');
                hints.push('Enable data streaming or pagination');
                break;
            case 'PERMISSION_DENIED':
                hints.push('Verify user has access to all required data sources');
                hints.push('Check role-based permissions');
                break;
            case 'DATA_SOURCE_ERROR':
                hints.push('Verify data source connectivity');
                hints.push('Check if required tables/views exist');
                break;
            default:
                hints.push('Check the error details and stack trace');
                hints.push('Contact system administrator if the issue persists');
        }
        return hints;
    }
};
exports.ReportExecution = ReportExecution;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ReportExecution.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], ReportExecution.prototype, "reportDefinitionId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['pending', 'running', 'completed', 'failed', 'cancelled', 'timeout'],
        default: 'pending',
    }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], ReportExecution.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['manual', 'scheduled', 'api', 'webhook', 'dashboard_refresh'],
    }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], ReportExecution.prototype, "executionType", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], ReportExecution.prototype, "executedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], ReportExecution.prototype, "startedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], ReportExecution.prototype, "completedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'integer', nullable: true }),
    __metadata("design:type", Number)
], ReportExecution.prototype, "executionTimeMs", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'integer', default: 0 }),
    __metadata("design:type", Number)
], ReportExecution.prototype, "progressPercentage", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ReportExecution.prototype, "currentStep", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    __metadata("design:type", Object)
], ReportExecution.prototype, "executionContext", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ReportExecution.prototype, "dataMetrics", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ReportExecution.prototype, "outputMetadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ReportExecution.prototype, "errorDetails", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    __metadata("design:type", Object)
], ReportExecution.prototype, "performanceMetrics", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ReportExecution.prototype, "qualityMetrics", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    __metadata("design:type", Object)
], ReportExecution.prototype, "auditTrail", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ReportExecution.prototype, "deliveryStatus", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    __metadata("design:type", Object)
], ReportExecution.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], ReportExecution.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], ReportExecution.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => report_definition_entity_1.ReportDefinition, definition => definition.executions),
    (0, typeorm_1.JoinColumn)({ name: 'reportDefinitionId' }),
    __metadata("design:type", typeof (_e = typeof report_definition_entity_1.ReportDefinition !== "undefined" && report_definition_entity_1.ReportDefinition) === "function" ? _e : Object)
], ReportExecution.prototype, "reportDefinition", void 0);
exports.ReportExecution = ReportExecution = __decorate([
    (0, typeorm_1.Entity)('report_executions'),
    (0, typeorm_1.Index)(['reportDefinitionId', 'status']),
    (0, typeorm_1.Index)(['executedBy', 'startedAt']),
    (0, typeorm_1.Index)(['status', 'startedAt']),
    (0, typeorm_1.Index)(['executionType', 'createdAt'])
], ReportExecution);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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