{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\auth\\strategies\\jwt.strategy.ts", "mappings": ";;;;;;;;;;;;;;AAAA,2CAA2E;AAC3E,+CAAoD;AACpD,+CAAoD;AACpD,2CAA+C;AAC/C,kDAA8C;AAC9C,wDAAqE;AAErE;;;GAGG;AAEI,IAAM,WAAW,mBAAjB,MAAM,WAAY,SAAQ,IAAA,2BAAgB,EAAC,uBAAQ,EAAE,KAAK,CAAC;IAGhE,YACmB,aAA4B,EAC5B,WAAwB;QAEzC,MAAM,SAAS,GAAG,aAAa,CAAC,GAAG,CAAY,KAAK,CAAC,CAAC;QACtD,MAAM,aAAa,GAAG,4BAAe,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAClE,MAAM,WAAW,GAAG,4BAAe,CAAC,cAAc,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QAErE,KAAK,CAAC;YACJ,cAAc,EAAE,yBAAU,CAAC,2BAA2B,EAAE;YACxD,gBAAgB,EAAE,SAAS,CAAC,gBAAgB;YAC5C,WAAW;YACX,MAAM,EAAE,SAAS,CAAC,MAAM;YACxB,QAAQ,EAAE,SAAS,CAAC,QAAQ;YAC5B,UAAU,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC;YACjC,cAAc,EAAE,SAAS,CAAC,cAAc;SACzC,CAAC,CAAC;QAfc,kBAAa,GAAb,aAAa,CAAe;QAC5B,gBAAW,GAAX,WAAW,CAAa;QAJ1B,WAAM,GAAG,IAAI,eAAM,CAAC,aAAW,CAAC,IAAI,CAAC,CAAC;IAmBvD,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,QAAQ,CAAC,OAAY;QACzB,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;gBAC1C,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,GAAG,EAAE,OAAO,CAAC,GAAG;aACjB,CAAC,CAAC;YAEH,6BAA6B;YAC7B,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;gBACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;gBAC/D,MAAM,IAAI,8BAAqB,CAAC,uBAAuB,CAAC,CAAC;YAC3D,CAAC;YAED,4CAA4C;YAC5C,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YAClD,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,GAAG,WAAW,EAAE,CAAC;gBAC7C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE;oBACxC,GAAG,EAAE,OAAO,CAAC,GAAG;oBAChB,WAAW;oBACX,GAAG,EAAE,OAAO,CAAC,GAAG;iBACjB,CAAC,CAAC;gBACH,MAAM,IAAI,8BAAqB,CAAC,mBAAmB,CAAC,CAAC;YACvD,CAAC;YAED,qCAAqC;YACrC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAEhE,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE;oBACjD,GAAG,EAAE,OAAO,CAAC,GAAG;oBAChB,KAAK,EAAE,OAAO,CAAC,KAAK;iBACrB,CAAC,CAAC;gBACH,MAAM,IAAI,8BAAqB,CAAC,gBAAgB,CAAC,CAAC;YACpD,CAAC;YAED,sDAAsD;YACtD,MAAM,aAAa,GAAG;gBACpB,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,EAAE;gBAC1B,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE;gBACtC,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,aAAa,EAAE,IAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,uBAAuB;gBACvB,aAAa,EAAE,OAAO,CAAC,GAAG;gBAC1B,cAAc,EAAE,OAAO,CAAC,GAAG;aAC5B,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;gBAC7C,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,KAAK,EAAE,aAAa,CAAC,KAAK;gBAC1B,eAAe,EAAE,aAAa,CAAC,WAAW,CAAC,MAAM;aAClD,CAAC,CAAC;YAEH,OAAO,aAAa,CAAC;QAEvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE;gBACzC,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,OAAO,EAAE;oBACP,GAAG,EAAE,OAAO,EAAE,GAAG;oBACjB,KAAK,EAAE,OAAO,EAAE,KAAK;oBACrB,GAAG,EAAE,OAAO,EAAE,GAAG;oBACjB,GAAG,EAAE,OAAO,EAAE,GAAG;iBAClB;aACF,CAAC,CAAC;YAEH,IAAI,KAAK,YAAY,8BAAqB,EAAE,CAAC;gBAC3C,MAAM,KAAK,CAAC;YACd,CAAC;YAED,MAAM,IAAI,8BAAqB,CAAC,yBAAyB,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;CACF,CAAA;AA7GY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;yDAKuB,sBAAa,oBAAb,sBAAa,oDACf,0BAAW,oBAAX,0BAAW;GALhC,WAAW,CA6GvB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\auth\\strategies\\jwt.strategy.ts"], "sourcesContent": ["import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';\r\nimport { PassportStrategy } from '@nestjs/passport';\r\nimport { ExtractJwt, Strategy } from 'passport-jwt';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { AuthService } from '../auth.service';\r\nimport { JwtConfig, JwtConfigHelper } from '../../config/jwt.config';\r\n\r\n/**\r\n * JWT authentication strategy for Passport\r\n * Validates JWT tokens and extracts user information\r\n */\r\n@Injectable()\r\nexport class JwtStrategy extends PassportStrategy(Strategy, 'jwt') {\r\n  private readonly logger = new Logger(JwtStrategy.name);\r\n\r\n  constructor(\r\n    private readonly configService: ConfigService,\r\n    private readonly authService: AuthService,\r\n  ) {\r\n    const jwtConfig = configService.get<JwtConfig>('jwt');\r\n    const verifyOptions = JwtConfigHelper.getVerifyOptions(jwtConfig);\r\n    const secretOrKey = JwtConfigHelper.getSecretOrKey(jwtConfig, false);\r\n    \r\n    super({\r\n      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),\r\n      ignoreExpiration: jwtConfig.ignoreExpiration,\r\n      secretOrKey,\r\n      issuer: jwtConfig.issuer,\r\n      audience: jwtConfig.audience,\r\n      algorithms: [jwtConfig.algorithm],\r\n      clockTolerance: jwtConfig.clockTolerance,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Validate JWT payload and return user object\r\n   * This method is called automatically by Passport when a JWT token is provided\r\n   * \r\n   * @param payload JWT payload containing user information\r\n   * @returns Promise<any> User object with roles and permissions\r\n   */\r\n  async validate(payload: any): Promise<any> {\r\n    try {\r\n      this.logger.debug('Validating JWT payload', {\r\n        sub: payload.sub,\r\n        email: payload.email,\r\n        iat: payload.iat,\r\n        exp: payload.exp,\r\n      });\r\n\r\n      // Validate payload structure\r\n      if (!payload.sub || !payload.email) {\r\n        this.logger.warn('Invalid JWT payload structure', { payload });\r\n        throw new UnauthorizedException('Invalid token payload');\r\n      }\r\n\r\n      // Check token expiration (additional check)\r\n      const currentTime = Math.floor(Date.now() / 1000);\r\n      if (payload.exp && payload.exp < currentTime) {\r\n        this.logger.warn('JWT token has expired', {\r\n          exp: payload.exp,\r\n          currentTime,\r\n          sub: payload.sub,\r\n        });\r\n        throw new UnauthorizedException('Token has expired');\r\n      }\r\n\r\n      // Validate user exists and is active\r\n      const user = await this.authService.validateJwtPayload(payload);\r\n\r\n      if (!user) {\r\n        this.logger.warn('User not found for JWT payload', {\r\n          sub: payload.sub,\r\n          email: payload.email,\r\n        });\r\n        throw new UnauthorizedException('User not found');\r\n      }\r\n\r\n      // Return user object with additional JWT payload data\r\n      const validatedUser = {\r\n        id: user.id,\r\n        email: user.email,\r\n        firstName: user.firstName,\r\n        lastName: user.lastName,\r\n        roles: payload.roles || [],\r\n        permissions: payload.permissions || [],\r\n        isActive: user.isActive,\r\n        emailVerified: user.emailVerified,\r\n        lastLoginAt: user.lastLoginAt,\r\n        // Include JWT metadata\r\n        tokenIssuedAt: payload.iat,\r\n        tokenExpiresAt: payload.exp,\r\n      };\r\n\r\n      this.logger.debug('JWT validation successful', {\r\n        userId: user.id,\r\n        email: user.email,\r\n        roles: validatedUser.roles,\r\n        permissionCount: validatedUser.permissions.length,\r\n      });\r\n\r\n      return validatedUser;\r\n\r\n    } catch (error) {\r\n      this.logger.error('JWT validation failed', {\r\n        error: error.message,\r\n        payload: {\r\n          sub: payload?.sub,\r\n          email: payload?.email,\r\n          iat: payload?.iat,\r\n          exp: payload?.exp,\r\n        },\r\n      });\r\n\r\n      if (error instanceof UnauthorizedException) {\r\n        throw error;\r\n      }\r\n\r\n      throw new UnauthorizedException('Token validation failed');\r\n    }\r\n  }\r\n}\r\n"], "version": 3}