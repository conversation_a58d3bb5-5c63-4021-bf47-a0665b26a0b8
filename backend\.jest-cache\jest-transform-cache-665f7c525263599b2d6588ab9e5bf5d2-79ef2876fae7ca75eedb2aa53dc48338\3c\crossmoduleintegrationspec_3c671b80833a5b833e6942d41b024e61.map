{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\tests\\integration\\cross-module.integration.spec.ts", "mappings": ";;AAAA,6CAAsD;AAEtD,6CAAgD;AAChD,2CAA8C;AAC9C,yDAA2D;AAC3D,uCAA0C;AAG1C,6CAAqD;AACrD,gGAA2F;AAC3F,kGAA6F;AAC7F,gHAA0G;AAC1G,sHAAgH;AAChH,8FAAmF;AACnF,wFAA6E;AAC7E,4GAAgG;AAChG,8FAAmF;AAEnF;;;;;;;;;;GAUG;AACH,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;IAC9C,IAAI,GAAqB,CAAC;IAC1B,IAAI,eAA4C,CAAC;IACjD,IAAI,gBAA8C,CAAC;IACnD,IAAI,YAAgD,CAAC;IACrD,IAAI,eAAsD,CAAC;IAC3D,IAAI,kBAAoD,CAAC;IACzD,IAAI,mBAAkD,CAAC;IACvD,IAAI,mBAA2D,CAAC;IAChE,IAAI,kBAAoD,CAAC;IAEzD,MAAM,QAAQ,GAAG;QACf,EAAE,EAAE,eAAe;QACnB,KAAK,EAAE,kBAAkB;QACzB,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,UAAU;QAChB,WAAW,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,mBAAmB,CAAC;KAChE,CAAC;IAEF,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,aAAa,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAClE,OAAO,EAAE;gBACP,qBAAY,CAAC,OAAO,CAAC;oBACnB,QAAQ,EAAE,IAAI;oBACd,WAAW,EAAE,WAAW;iBACzB,CAAC;gBACF,uBAAa,CAAC,OAAO,CAAC;oBACpB,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,WAAW;oBAC7C,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,IAAI;oBAChD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,MAAM;oBAChD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,MAAM;oBAChD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,eAAe;oBACrD,QAAQ,EAAE;wBACR,mDAAoB;wBACpB,6CAAiB;wBACjB,gEAA0B;wBAC1B,mDAAoB;qBACrB;oBACD,WAAW,EAAE,IAAI;oBACjB,UAAU,EAAE,IAAI;iBACjB,CAAC;gBACF,uBAAa,CAAC,UAAU,CAAC;oBACvB,mDAAoB;oBACpB,6CAAiB;oBACjB,gEAA0B;oBAC1B,mDAAoB;iBACrB,CAAC;gBACF,kCAAkB,CAAC,OAAO,EAAE;gBAC5B,iBAAU,CAAC,OAAO,CAAC;oBACjB,KAAK,EAAE;wBACL,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,WAAW;wBAChD,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,IAAI;wBACnD,EAAE,EAAE,CAAC;qBACN;iBACF,CAAC;gBACF,iBAAU,CAAC,aAAa,CACtB,EAAE,IAAI,EAAE,mBAAmB,EAAE,EAC7B,EAAE,IAAI,EAAE,qBAAqB,EAAE,EAC/B,EAAE,IAAI,EAAE,kBAAkB,EAAE,CAC7B;aACF;YACD,SAAS,EAAE;gBACT,2DAA2B;gBAC3B,6DAA4B;gBAC5B,0EAAkC;gBAClC,gFAAqC;gBACrC,gCAAgC;gBAChC;oBACE,OAAO,EAAE,wBAAwB;oBACjC,QAAQ,EAAE;wBACR,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC;4BACxC,EAAE,EAAE,WAAW;4BACf,IAAI,EAAE,eAAe;4BACrB,IAAI,EAAE,QAAQ;4BACd,WAAW,EAAE,MAAM;4BACnB,KAAK,EAAE,qBAAqB;4BAC5B,WAAW,EAAE,YAAY;4BACzB,QAAQ,EAAE;gCACR,EAAE,EAAE,YAAY;gCAChB,EAAE,EAAE,cAAc;gCAClB,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;6BAC9B;yBACF,CAAC;wBACF,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,CAAC;wBACjD,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC;qBACrD;iBACF;gBACD;oBACE,OAAO,EAAE,2BAA2B;oBACpC,QAAQ,EAAE;wBACR,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC;4BACzC,EAAE,EAAE,YAAY;4BAChB,IAAI,EAAE,eAAe;4BACrB,QAAQ,EAAE,UAAU;4BACpB,IAAI,EAAE,eAAe;4BACrB,WAAW,EAAE,qCAAqC;4BAClD,UAAU,EAAE,CAAC,cAAc,EAAE,mBAAmB,CAAC;4BACjD,cAAc,EAAE,CAAC,WAAW,CAAC;yBAC9B,CAAC;wBACF,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,CAAC;wBACnD,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,CAAC;qBAClD;iBACF;gBACD;oBACE,OAAO,EAAE,uBAAuB;oBAChC,QAAQ,EAAE;wBACR,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,QAAQ,CAAC;wBAClD,uBAAuB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC;wBAC1D,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,CAAC,QAAQ,CAAC,CAAC;qBACxD;iBACF;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,GAAG,GAAG,aAAa,CAAC,qBAAqB,EAAE,CAAC;QAC5C,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC;QAEjB,wBAAwB;QACxB,eAAe,GAAG,aAAa,CAAC,GAAG,CAA8B,2DAA2B,CAAC,CAAC;QAC9F,gBAAgB,GAAG,aAAa,CAAC,GAAG,CAA+B,6DAA4B,CAAC,CAAC;QACjG,YAAY,GAAG,aAAa,CAAC,GAAG,CAAqC,0EAAkC,CAAC,CAAC;QACzG,eAAe,GAAG,aAAa,CAAC,GAAG,CAAwC,gFAAqC,CAAC,CAAC;QAElH,2BAA2B;QAC3B,kBAAkB,GAAG,aAAa,CAAC,GAAG,CACpC,IAAA,4BAAkB,EAAC,mDAAoB,CAAC,CACzC,CAAC;QACF,mBAAmB,GAAG,aAAa,CAAC,GAAG,CACrC,IAAA,4BAAkB,EAAC,6CAAiB,CAAC,CACtC,CAAC;QACF,mBAAmB,GAAG,aAAa,CAAC,GAAG,CACrC,IAAA,4BAAkB,EAAC,gEAA0B,CAAC,CAC/C,CAAC;QACF,kBAAkB,GAAG,aAAa,CAAC,GAAG,CACpC,IAAA,4BAAkB,EAAC,mDAAoB,CAAC,CACzC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,qCAAqC;QACrC,MAAM,mBAAmB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACrC,MAAM,mBAAmB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACrC,MAAM,kBAAkB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACpC,MAAM,kBAAkB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oDAAoD,EAAE,GAAG,EAAE;QAClE,EAAE,CAAC,oEAAoE,EAAE,KAAK,IAAI,EAAE;YAClF,2CAA2C;YAC3C,MAAM,cAAc,GAAG,MAAM,eAAe,CAAC,cAAc,CAAC;gBAC1D,IAAI,EAAE,2BAA2B;gBACjC,WAAW,EAAE,4CAA4C;gBACzD,UAAU,EAAE;oBACV,SAAS,EAAE,eAAe;oBAC1B,KAAK,EAAE;wBACL;4BACE,EAAE,EAAE,eAAe;4BACnB,IAAI,EAAE,WAAW;4BACjB,MAAM,EAAE;gCACN,SAAS,EAAE;oCACT,KAAK,EAAE,uBAAuB;oCAC9B,QAAQ,EAAE,IAAI;oCACd,KAAK,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC;iCAC5B;6BACF;4BACD,SAAS,EAAE;gCACT;oCACE,MAAM,EAAE,sBAAsB;oCAC9B,SAAS,EAAE;wCACT,KAAK,EAAE,mBAAmB;wCAC1B,QAAQ,EAAE,IAAI;wCACd,KAAK,EAAE,IAAI;qCACZ;iCACF;6BACF;yBACF;wBACD;4BACE,EAAE,EAAE,sBAAsB;4BAC1B,IAAI,EAAE,cAAc;4BACpB,MAAM,EAAE;gCACN,OAAO,EAAE,OAAO;gCAChB,OAAO,EAAE,kEAAkE;gCAC3E,UAAU,EAAE,CAAC,sBAAsB,CAAC;gCACpC,QAAQ,EAAE,UAAU;6BACrB;yBACF;qBACF;oBACD,QAAQ,EAAE;wBACR;4BACE,IAAI,EAAE,OAAO;4BACb,SAAS,EAAE,uBAAuB;4BAClC,UAAU,EAAE;gCACV;oCACE,KAAK,EAAE,mBAAmB;oCAC1B,QAAQ,EAAE,IAAI;oCACd,KAAK,EAAE,MAAM;iCACd;6BACF;yBACF;qBACF;iBACF;gBACD,MAAM,EAAE,QAAQ;gBAChB,QAAQ,EAAE,mBAAmB;aAC9B,EAAE,QAAQ,CAAC,CAAC;YAEb,wCAAwC;YACxC,MAAM,WAAW,GAAG;gBAClB,IAAI,EAAE,uBAAuB;gBAC7B,IAAI,EAAE;oBACJ,MAAM,EAAE;wBACN,EAAE,EAAE,YAAY;wBAChB,IAAI,EAAE,eAAe;wBACrB,QAAQ,EAAE,UAAU;wBACpB,IAAI,EAAE,eAAe;qBACtB;oBACD,KAAK,EAAE;wBACL,EAAE,EAAE,WAAW;wBACf,IAAI,EAAE,eAAe;wBACrB,WAAW,EAAE,MAAM;wBACnB,WAAW,EAAE,YAAY;qBAC1B;oBACD,WAAW,EAAE;wBACX,UAAU,EAAE,IAAI;wBAChB,UAAU,EAAE,CAAC,cAAc,EAAE,mBAAmB,CAAC;qBAClD;iBACF;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,oCAAoC;YACpC,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE,EAAE;gBACzE,KAAK,EAAE;oBACL,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,MAAM;oBAC/B,KAAK,EAAE,WAAW,CAAC,IAAI,CAAC,KAAK;oBAC7B,WAAW,EAAE,WAAW,CAAC,IAAI,CAAC,WAAW;oBACzC,IAAI,EAAE,QAAQ;iBACf;gBACD,OAAO,EAAE;oBACP,SAAS,EAAE,uBAAuB;oBAClC,WAAW,EAAE,YAAY;oBACzB,eAAe,EAAE,CAAC;iBACnB;aACF,EAAE,QAAQ,CAAC,CAAC;YAEb,qBAAqB;YACrB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAExD,0CAA0C;YAC1C,MAAM,kBAAkB,GAAG,MAAM,mBAAmB,CAAC,OAAO,CAAC;gBAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,WAAW,EAAE;gBACpC,SAAS,EAAE,CAAC,UAAU,CAAC;aACxB,CAAC,CAAC;YAEH,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACpD,MAAM,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,2BAA2B;YAE9E,iCAAiC;YACjC,MAAM,eAAe,GAAG,MAAM,mBAAmB,CAAC,IAAI,CAAC;gBACrD,KAAK,EAAE,EAAE,cAAc,EAAE,SAAS,CAAC,WAAW,EAAE;aACjD,CAAC,CAAC;YAEH,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAClD,MAAM,KAAK,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;YACjC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;YACtE,8BAA8B;YAC9B,MAAM,mBAAmB,GAAG,MAAM,eAAe,CAAC,cAAc,CAAC;gBAC/D,IAAI,EAAE,6BAA6B;gBACnC,WAAW,EAAE,uDAAuD;gBACpE,UAAU,EAAE;oBACV,SAAS,EAAE,mBAAmB;oBAC9B,KAAK,EAAE;wBACL;4BACE,EAAE,EAAE,mBAAmB;4BACvB,IAAI,EAAE,cAAc;4BACpB,MAAM,EAAE;gCACN,GAAG,EAAE,8CAA8C;gCACnD,MAAM,EAAE,MAAM;gCACd,IAAI,EAAE;oCACJ,OAAO,EAAE,oBAAoB;oCAC7B,UAAU,EAAE,KAAK;iCAClB;6BACF;4BACD,QAAQ,EAAE,aAAa;yBACxB;wBACD;4BACE,EAAE,EAAE,aAAa;4BACjB,IAAI,EAAE,WAAW;4BACjB,MAAM,EAAE;gCACN,SAAS,EAAE;oCACT,KAAK,EAAE,2BAA2B;oCAClC,QAAQ,EAAE,IAAI;oCACd,KAAK,EAAE,EAAE;iCACV;6BACF;4BACD,SAAS,EAAE;gCACT;oCACE,MAAM,EAAE,mBAAmB;oCAC3B,SAAS,EAAE;wCACT,KAAK,EAAE,mBAAmB;wCAC1B,QAAQ,EAAE,IAAI;wCACd,KAAK,EAAE,IAAI;qCACZ;iCACF;6BACF;yBACF;wBACD;4BACE,EAAE,EAAE,mBAAmB;4BACvB,IAAI,EAAE,cAAc;4BACpB,MAAM,EAAE;gCACN,OAAO,EAAE,OAAO;gCAChB,OAAO,EAAE,+DAA+D;gCACxE,UAAU,EAAE,CAAC,qBAAqB,CAAC;gCACnC,QAAQ,EAAE,UAAU;6BACrB;yBACF;qBACF;iBACF;gBACD,MAAM,EAAE,QAAQ;aACjB,EAAE,QAAQ,CAAC,CAAC;YAEb,oCAAoC;YACpC,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,eAAe,CAAC,mBAAmB,CAAC,EAAE,EAAE;gBAC9E,KAAK,EAAE;oBACL,KAAK,EAAE;wBACL,EAAE,EAAE,WAAW;wBACf,IAAI,EAAE,eAAe;wBACrB,WAAW,EAAE,MAAM;qBACpB;oBACD,OAAO,EAAE;wBACP,EAAE,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE;wBACpC,EAAE,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE;wBACxC,EAAE,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE;qBACvC;oBACD,IAAI,EAAE,QAAQ;iBACf;aACF,EAAE,QAAQ,CAAC,CAAC;YAEb,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAExD,MAAM,kBAAkB,GAAG,MAAM,mBAAmB,CAAC,OAAO,CAAC;gBAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,WAAW,EAAE;aACrC,CAAC,CAAC;YAEH,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4CAA4C,EAAE,GAAG,EAAE;QAC1D,EAAE,CAAC,6DAA6D,EAAE,KAAK,IAAI,EAAE;YAC3E,iDAAiD;YACjD,MAAM,kBAAkB,GAAG,MAAM,eAAe,CAAC,cAAc,CAAC;gBAC9D,IAAI,EAAE,8BAA8B;gBACpC,WAAW,EAAE,+BAA+B;gBAC5C,UAAU,EAAE;oBACV,SAAS,EAAE,mBAAmB;oBAC9B,KAAK,EAAE;wBACL;4BACE,EAAE,EAAE,mBAAmB;4BACvB,IAAI,EAAE,WAAW;4BACjB,MAAM,EAAE;gCACN,SAAS,EAAE;oCACT,KAAK,EAAE,wBAAwB;oCAC/B,QAAQ,EAAE,UAAU;oCACpB,KAAK,EAAE,gBAAgB;iCACxB;6BACF;4BACD,SAAS,EAAE;gCACT;oCACE,MAAM,EAAE,qBAAqB;oCAC7B,SAAS,EAAE;wCACT,KAAK,EAAE,mBAAmB;wCAC1B,QAAQ,EAAE,IAAI;wCACd,KAAK,EAAE,IAAI;qCACZ;iCACF;6BACF;yBACF;wBACD;4BACE,EAAE,EAAE,qBAAqB;4BACzB,IAAI,EAAE,cAAc;4BACpB,MAAM,EAAE;gCACN,OAAO,EAAE,OAAO;gCAChB,OAAO,EAAE,mDAAmD;gCAC5D,UAAU,EAAE,CAAC,2BAA2B,CAAC;6BAC1C;yBACF;qBACF;iBACF;gBACD,MAAM,EAAE,QAAQ;gBAChB,aAAa,EAAE;oBACb,WAAW,EAAE;wBACX,OAAO,EAAE,CAAC,gBAAgB,EAAE,OAAO,CAAC;wBACpC,MAAM,EAAE,CAAC,OAAO,CAAC;wBACjB,IAAI,EAAE,CAAC,eAAe,EAAE,gBAAgB,EAAE,OAAO,CAAC;qBACnD;iBACF;aACF,EAAE,QAAQ,CAAC,CAAC;YAEb,4BAA4B;YAC5B,MAAM,cAAc,GAAG;gBACrB,GAAG,QAAQ;gBACX,WAAW,EAAE,CAAC,gBAAgB,EAAE,mBAAmB,CAAC;aACrD,CAAC;YAEF,MAAM,mBAAmB,GAAG,MAAM,eAAe,CAAC,eAAe,CAC/D,kBAAkB,CAAC,EAAE,EACrB;gBACE,KAAK,EAAE;oBACL,IAAI,EAAE,cAAc;oBACpB,KAAK,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,4BAA4B,EAAE;iBACnE;aACF,EACD,cAAc,CACf,CAAC;YAEF,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAExD,MAAM,gBAAgB,GAAG,MAAM,mBAAmB,CAAC,OAAO,CAAC;gBACzD,KAAK,EAAE,EAAE,EAAE,EAAE,mBAAmB,CAAC,WAAW,EAAE;aAC/C,CAAC,CAAC;YAEH,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAElD,8BAA8B;YAC9B,MAAM,gBAAgB,GAAG;gBACvB,GAAG,QAAQ;gBACX,WAAW,EAAE,CAAC,mBAAmB,CAAC,EAAE,yBAAyB;aAC9D,CAAC;YAEF,IAAI,CAAC;gBACH,MAAM,eAAe,CAAC,eAAe,CACnC,kBAAkB,CAAC,EAAE,EACrB;oBACE,KAAK,EAAE;wBACL,IAAI,EAAE,gBAAgB;wBACtB,KAAK,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,4BAA4B,EAAE;qBACnE;iBACF,EACD,gBAAgB,CACjB,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;YAChD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,QAAQ,GAAG,MAAM,eAAe,CAAC,cAAc,CAAC;gBACpD,IAAI,EAAE,+BAA+B;gBACrC,UAAU,EAAE;oBACV,SAAS,EAAE,YAAY;oBACvB,KAAK,EAAE;wBACL;4BACE,EAAE,EAAE,YAAY;4BAChB,IAAI,EAAE,cAAc;4BACpB,MAAM,EAAE;gCACN,OAAO,EAAE,OAAO;gCAChB,OAAO,EAAE,0CAA0C;gCACnD,UAAU,EAAE,CAAC,mBAAmB,CAAC;6BAClC;yBACF;qBACF;iBACF;gBACD,MAAM,EAAE,QAAQ;aACjB,EAAE,QAAQ,CAAC,CAAC;YAEb,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,EAAE;gBACnE,KAAK,EAAE;oBACL,IAAI,EAAE,QAAQ;oBACd,MAAM,EAAE,0BAA0B;oBAClC,QAAQ,EAAE;wBACR,MAAM,EAAE,gBAAgB;wBACxB,QAAQ,EAAE,QAAQ;qBACnB;iBACF;aACF,EAAE,QAAQ,CAAC,CAAC;YAEb,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAExD,4CAA4C;YAC5C,MAAM,eAAe,GAAG,MAAM,mBAAmB,CAAC,IAAI,CAAC;gBACrD,KAAK,EAAE,EAAE,cAAc,EAAE,SAAS,CAAC,WAAW,EAAE;aACjD,CAAC,CAAC;YAEH,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAClD,MAAM,KAAK,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;YACjC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sCAAsC,EAAE,GAAG,EAAE;QACpD,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,kCAAkC;YAClC,MAAM,iBAAiB,GAAG,MAAM,eAAe,CAAC,cAAc,CAAC;gBAC7D,IAAI,EAAE,0BAA0B;gBAChC,WAAW,EAAE,iCAAiC;gBAC9C,UAAU,EAAE;oBACV,SAAS,EAAE,gBAAgB;oBAC3B,KAAK,EAAE;wBACL;4BACE,EAAE,EAAE,gBAAgB;4BACpB,IAAI,EAAE,WAAW;4BACjB,MAAM,EAAE;gCACN,SAAS,EAAE;oCACT,KAAK,EAAE,kBAAkB;oCACzB,QAAQ,EAAE,IAAI;oCACd,KAAK,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,aAAa,CAAC;iCAC7C;6BACF;4BACD,QAAQ,EAAE,kBAAkB;yBAC7B;wBACD;4BACE,EAAE,EAAE,kBAAkB;4BACtB,IAAI,EAAE,UAAU;4BAChB,MAAM,EAAE;gCACN,SAAS,EAAE;oCACT,QAAQ,EAAE,sBAAsB;oCAChC,SAAS,EAAE,QAAQ;iCACpB;6BACF;4BACD,QAAQ,EAAE,cAAc;yBACzB;wBACD;4BACE,EAAE,EAAE,cAAc;4BAClB,IAAI,EAAE,cAAc;4BACpB,MAAM,EAAE;gCACN,OAAO,EAAE,OAAO;gCAChB,OAAO,EAAE,6DAA6D;gCACtE,UAAU,EAAE,CAAC,mBAAmB,CAAC;6BAClC;yBACF;qBACF;oBACD,QAAQ,EAAE;wBACR;4BACE,IAAI,EAAE,OAAO;4BACb,SAAS,EAAE,kBAAkB;yBAC9B;qBACF;iBACF;gBACD,MAAM,EAAE,QAAQ;aACjB,EAAE,QAAQ,CAAC,CAAC;YAEb,iCAAiC;YACjC,MAAM,mBAAmB,GAAG;gBAC1B,IAAI,EAAE,kBAAkB;gBACxB,IAAI,EAAE;oBACJ,KAAK,EAAE;wBACL,EAAE,EAAE,eAAe;wBACnB,IAAI,EAAE,oBAAoB;wBAC1B,IAAI,EAAE,UAAU;wBAChB,EAAE,EAAE,YAAY;wBAChB,YAAY,EAAE,IAAI,IAAI,EAAE;qBACzB;oBACD,OAAO,EAAE,MAAM;oBACf,UAAU,EAAE,IAAI;iBACjB;aACF,CAAC;YAEF,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,eAAe,CAAC,iBAAiB,CAAC,EAAE,EAAE;gBAC5E,KAAK,EAAE;oBACL,KAAK,EAAE,mBAAmB,CAAC,IAAI,CAAC,KAAK;oBACrC,OAAO,EAAE,mBAAmB,CAAC,IAAI,CAAC,OAAO;oBACzC,IAAI,EAAE,QAAQ;iBACf;gBACD,OAAO,EAAE;oBACP,SAAS,EAAE,kBAAkB;oBAC7B,QAAQ,EAAE,IAAI;iBACf;aACF,EAAE,QAAQ,CAAC,CAAC;YAEb,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAExD,MAAM,kBAAkB,GAAG,MAAM,mBAAmB,CAAC,OAAO,CAAC;gBAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,WAAW,EAAE;gBACpC,SAAS,EAAE,CAAC,UAAU,CAAC;aACxB,CAAC,CAAC;YAEH,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACpD,MAAM,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAElD,8BAA8B;YAC9B,MAAM,cAAc,GAAG,kBAAkB,CAAC,QAAQ,CAAC;YACnD,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,yBAAyB;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,MAAM,aAAa,GAAG,MAAM,eAAe,CAAC,cAAc,CAAC;gBACzD,IAAI,EAAE,8BAA8B;gBACpC,UAAU,EAAE;oBACV,SAAS,EAAE,iBAAiB;oBAC5B,KAAK,EAAE;wBACL;4BACE,EAAE,EAAE,iBAAiB;4BACrB,IAAI,EAAE,WAAW;4BACjB,MAAM,EAAE;gCACN,SAAS,EAAE;oCACT,KAAK,EAAE,qBAAqB;oCAC5B,QAAQ,EAAE,KAAK;oCACf,KAAK,EAAE,IAAI;iCACZ;6BACF;4BACD,SAAS,EAAE;gCACT;oCACE,MAAM,EAAE,eAAe;oCACvB,SAAS,EAAE;wCACT,KAAK,EAAE,mBAAmB;wCAC1B,QAAQ,EAAE,IAAI;wCACd,KAAK,EAAE,IAAI;qCACZ;iCACF;6BACF;yBACF;wBACD;4BACE,EAAE,EAAE,eAAe;4BACnB,IAAI,EAAE,cAAc;4BACpB,MAAM,EAAE;gCACN,OAAO,EAAE,OAAO;gCAChB,OAAO,EAAE,6CAA6C;gCACtD,UAAU,EAAE,CAAC,oBAAoB,CAAC;6BACnC;yBACF;qBACF;iBACF;gBACD,MAAM,EAAE,QAAQ;aACjB,EAAE,QAAQ,CAAC,CAAC;YAEb,wBAAwB;YACxB,MAAM,SAAS,GAAG;gBAChB,KAAK,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,gBAAgB,EAAE;gBAClD,IAAI,EAAE,QAAQ;aACf,CAAC;YAEF,MAAM,UAAU,GAAG,MAAM,eAAe,CAAC,eAAe,CAAC,aAAa,CAAC,EAAE,EAAE;gBACzE,KAAK,EAAE,SAAS;gBAChB,OAAO,EAAE,EAAE,WAAW,EAAE,KAAK,EAAE;aAChC,EAAE,QAAQ,CAAC,CAAC;YAEb,MAAM,UAAU,GAAG,MAAM,eAAe,CAAC,eAAe,CAAC,aAAa,CAAC,EAAE,EAAE;gBACzE,KAAK,EAAE,SAAS;gBAChB,OAAO,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,EAAE,YAAY;aAC7C,EAAE,QAAQ,CAAC,CAAC;YAEb,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAExD,MAAM,OAAO,GAAG,MAAM,mBAAmB,CAAC,OAAO,CAAC;gBAChD,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,CAAC,WAAW,EAAE;aACtC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,MAAM,mBAAmB,CAAC,OAAO,CAAC;gBAChD,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,CAAC,WAAW,EAAE;aACtC,CAAC,CAAC;YAEH,iEAAiE;YACjE,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAEvC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACzC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,4BAA4B;QACtE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAChD,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,gDAAgD;YAChD,MAAM,mBAAmB,GAAG,MAAM,eAAe,CAAC,cAAc,CAAC;gBAC/D,IAAI,EAAE,2BAA2B;gBACjC,UAAU,EAAE;oBACV,SAAS,EAAE,cAAc;oBACzB,KAAK,EAAE;wBACL;4BACE,EAAE,EAAE,cAAc;4BAClB,IAAI,EAAE,cAAc;4BACpB,MAAM,EAAE;gCACN,GAAG,EAAE,gDAAgD;gCACrD,MAAM,EAAE,OAAO;gCACf,IAAI,EAAE;oCACJ,MAAM,EAAE,aAAa;oCACrB,YAAY,EAAE,uBAAuB;iCACtC;6BACF;4BACD,QAAQ,EAAE,qBAAqB;yBAChC;wBACD;4BACE,EAAE,EAAE,qBAAqB;4BACzB,IAAI,EAAE,cAAc;4BACpB,MAAM,EAAE;gCACN,GAAG,EAAE,yDAAyD;gCAC9D,MAAM,EAAE,OAAO;gCACf,IAAI,EAAE;oCACJ,cAAc,EAAE,CAAC,oBAAoB,CAAC;oCACtC,MAAM,EAAE,QAAQ;iCACjB;6BACF;4BACD,QAAQ,EAAE,iBAAiB;yBAC5B;wBACD;4BACE,EAAE,EAAE,iBAAiB;4BACrB,IAAI,EAAE,cAAc;4BACpB,MAAM,EAAE;gCACN,OAAO,EAAE,OAAO;gCAChB,OAAO,EAAE,2DAA2D;gCACpE,UAAU,EAAE,CAAC,+BAA+B,CAAC;6BAC9C;yBACF;qBACF;iBACF;gBACD,MAAM,EAAE,QAAQ;aACjB,EAAE,QAAQ,CAAC,CAAC;YAEb,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,eAAe,CAAC,mBAAmB,CAAC,EAAE,EAAE;gBAC9E,KAAK,EAAE;oBACL,KAAK,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE;oBAC1B,MAAM,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE;oBAC5B,QAAQ,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE;oBAChC,IAAI,EAAE,QAAQ;iBACf;aACF,EAAE,QAAQ,CAAC,CAAC;YAEb,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAExD,MAAM,kBAAkB,GAAG,MAAM,mBAAmB,CAAC,OAAO,CAAC;gBAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,WAAW,EAAE;gBACpC,SAAS,EAAE,CAAC,UAAU,CAAC;aACxB,CAAC,CAAC;YAEH,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACpD,MAAM,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAElD,mEAAmE;YACnE,kBAAkB,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBAC5C,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACzC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;YACnC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,MAAM,kBAAkB,GAAG,MAAM,eAAe,CAAC,cAAc,CAAC;gBAC9D,IAAI,EAAE,iCAAiC;gBACvC,UAAU,EAAE;oBACV,SAAS,EAAE,eAAe;oBAC1B,KAAK,EAAE;wBACL;4BACE,EAAE,EAAE,eAAe;4BACnB,IAAI,EAAE,cAAc;4BACpB,MAAM,EAAE;gCACN,OAAO,EAAE,SAAS;gCAClB,OAAO,EAAE,qCAAqC;gCAC9C,UAAU,EAAE,CAAC,0CAA0C,CAAC;6BACzD;yBACF;qBACF;iBACF;gBACD,MAAM,EAAE,QAAQ;aACjB,EAAE,QAAQ,CAAC,CAAC;YAEb,uCAAuC;YACvC,MAAM,SAAS,GAAG,EAAE,CAAC;YACrB,MAAM,UAAU,GAAG,EAAE,CAAC;YAEtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;gBACnC,MAAM,SAAS,GAAG,eAAe,CAAC,eAAe,CAAC,kBAAkB,CAAC,EAAE,EAAE;oBACvE,KAAK,EAAE;wBACL,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE;wBACtC,IAAI,EAAE,QAAQ;qBACf;iBACF,EAAE,QAAQ,CAAC,CAAC;gBAEb,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC7B,CAAC;YAED,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAExC,sCAAsC;YACtC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;YAEzD,+CAA+C;YAC/C,MAAM,mBAAmB,GAAG,MAAM,mBAAmB,CAAC,IAAI,CAAC;gBACzD,KAAK,EAAE,EAAE,UAAU,EAAE,kBAAkB,CAAC,EAAE,EAAE;aAC7C,CAAC,CAAC;YAEH,MAAM,CAAC,mBAAmB,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YACpD,mBAAmB,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBACtC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC;YAEH,oDAAoD;YACpD,MAAM,eAAe,GAAG,MAAM,mBAAmB,CAAC,IAAI,CAAC;gBACrD,KAAK,EAAE,EAAE,UAAU,EAAE,kBAAkB,CAAC,EAAE,EAAE;aAC7C,CAAC,CAAC;YAEH,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\tests\\integration\\cross-module.integration.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { INestApplication } from '@nestjs/common';\r\nimport { TypeOrmModule } from '@nestjs/typeorm';\r\nimport { ConfigModule } from '@nestjs/config';\r\nimport { EventEmitterModule } from '@nestjs/event-emitter';\r\nimport { BullModule } from '@nestjs/bull';\r\nimport * as request from 'supertest';\r\nimport { Repository } from 'typeorm';\r\nimport { getRepositoryToken } from '@nestjs/typeorm';\r\nimport { NotificationWorkflowService } from '../../services/notification-workflow.service';\r\nimport { NotificationAnalyticsService } from '../../services/notification-analytics.service';\r\nimport { NotificationQueueManagementService } from '../../services/notification-queue-management.service';\r\nimport { NotificationTemplateManagementService } from '../../services/notification-template-management.service';\r\nimport { NotificationWorkflow } from '../../entities/notification-workflow.entity';\r\nimport { WorkflowExecution } from '../../entities/workflow-execution.entity';\r\nimport { NotificationAnalyticsEvent } from '../../entities/notification-analytics-event.entity';\r\nimport { NotificationTemplate } from '../../entities/notification-template.entity';\r\n\r\n/**\r\n * Cross-Module Integration Tests\r\n * \r\n * Comprehensive integration tests validating data flow and interactions between modules:\r\n * - Asset Management ↔ Threat Intelligence data flow validation\r\n * - Reporting Module ↔ User Management authentication integration\r\n * - Notification workflows ↔ Asset discovery event processing\r\n * - Analytics data consistency across module boundaries\r\n * - Event-driven architecture validation with real-time processing\r\n * - Performance testing for cross-module data synchronization\r\n */\r\ndescribe('Cross-Module Integration Tests', () => {\r\n  let app: INestApplication;\r\n  let workflowService: NotificationWorkflowService;\r\n  let analyticsService: NotificationAnalyticsService;\r\n  let queueService: NotificationQueueManagementService;\r\n  let templateService: NotificationTemplateManagementService;\r\n  let workflowRepository: Repository<NotificationWorkflow>;\r\n  let executionRepository: Repository<WorkflowExecution>;\r\n  let analyticsRepository: Repository<NotificationAnalyticsEvent>;\r\n  let templateRepository: Repository<NotificationTemplate>;\r\n\r\n  const testUser = {\r\n    id: 'test-user-123',\r\n    email: '<EMAIL>',\r\n    role: 'admin',\r\n    team: 'security',\r\n    permissions: ['asset.read', 'threat.read', 'notification.send'],\r\n  };\r\n\r\n  beforeAll(async () => {\r\n    const moduleFixture: TestingModule = await Test.createTestingModule({\r\n      imports: [\r\n        ConfigModule.forRoot({\r\n          isGlobal: true,\r\n          envFilePath: '.env.test',\r\n        }),\r\n        TypeOrmModule.forRoot({\r\n          type: 'postgres',\r\n          host: process.env.TEST_DB_HOST || 'localhost',\r\n          port: parseInt(process.env.TEST_DB_PORT) || 5433,\r\n          username: process.env.TEST_DB_USERNAME || 'test',\r\n          password: process.env.TEST_DB_PASSWORD || 'test',\r\n          database: process.env.TEST_DB_NAME || 'sentinel_test',\r\n          entities: [\r\n            NotificationWorkflow,\r\n            WorkflowExecution,\r\n            NotificationAnalyticsEvent,\r\n            NotificationTemplate,\r\n          ],\r\n          synchronize: true,\r\n          dropSchema: true,\r\n        }),\r\n        TypeOrmModule.forFeature([\r\n          NotificationWorkflow,\r\n          WorkflowExecution,\r\n          NotificationAnalyticsEvent,\r\n          NotificationTemplate,\r\n        ]),\r\n        EventEmitterModule.forRoot(),\r\n        BullModule.forRoot({\r\n          redis: {\r\n            host: process.env.TEST_REDIS_HOST || 'localhost',\r\n            port: parseInt(process.env.TEST_REDIS_PORT) || 6380,\r\n            db: 1,\r\n          },\r\n        }),\r\n        BullModule.registerQueue(\r\n          { name: 'notification-high' },\r\n          { name: 'notification-medium' },\r\n          { name: 'notification-low' }\r\n        ),\r\n      ],\r\n      providers: [\r\n        NotificationWorkflowService,\r\n        NotificationAnalyticsService,\r\n        NotificationQueueManagementService,\r\n        NotificationTemplateManagementService,\r\n        // Mock external module services\r\n        {\r\n          provide: 'AssetManagementService',\r\n          useValue: {\r\n            getAssetById: jest.fn().mockResolvedValue({\r\n              id: 'asset-123',\r\n              name: 'Web Server 01',\r\n              type: 'server',\r\n              criticality: 'high',\r\n              owner: 'infrastructure-team',\r\n              environment: 'production',\r\n              metadata: {\r\n                ip: '**********',\r\n                os: 'Ubuntu 20.04',\r\n                services: ['nginx', 'nodejs'],\r\n              },\r\n            }),\r\n            getAssetsByQuery: jest.fn().mockResolvedValue([]),\r\n            updateAssetStatus: jest.fn().mockResolvedValue(true),\r\n          },\r\n        },\r\n        {\r\n          provide: 'ThreatIntelligenceService',\r\n          useValue: {\r\n            getThreatById: jest.fn().mockResolvedValue({\r\n              id: 'threat-456',\r\n              name: 'CVE-2024-1234',\r\n              severity: 'critical',\r\n              type: 'vulnerability',\r\n              description: 'Remote code execution vulnerability',\r\n              indicators: ['malicious-ip', 'suspicious-domain'],\r\n              affectedAssets: ['asset-123'],\r\n            }),\r\n            getThreatsForAsset: jest.fn().mockResolvedValue([]),\r\n            correlateThreats: jest.fn().mockResolvedValue([]),\r\n          },\r\n        },\r\n        {\r\n          provide: 'UserManagementService',\r\n          useValue: {\r\n            getUserById: jest.fn().mockResolvedValue(testUser),\r\n            validateUserPermissions: jest.fn().mockResolvedValue(true),\r\n            getUsersByRole: jest.fn().mockResolvedValue([testUser]),\r\n          },\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    app = moduleFixture.createNestApplication();\r\n    await app.init();\r\n\r\n    // Get service instances\r\n    workflowService = moduleFixture.get<NotificationWorkflowService>(NotificationWorkflowService);\r\n    analyticsService = moduleFixture.get<NotificationAnalyticsService>(NotificationAnalyticsService);\r\n    queueService = moduleFixture.get<NotificationQueueManagementService>(NotificationQueueManagementService);\r\n    templateService = moduleFixture.get<NotificationTemplateManagementService>(NotificationTemplateManagementService);\r\n\r\n    // Get repository instances\r\n    workflowRepository = moduleFixture.get<Repository<NotificationWorkflow>>(\r\n      getRepositoryToken(NotificationWorkflow)\r\n    );\r\n    executionRepository = moduleFixture.get<Repository<WorkflowExecution>>(\r\n      getRepositoryToken(WorkflowExecution)\r\n    );\r\n    analyticsRepository = moduleFixture.get<Repository<NotificationAnalyticsEvent>>(\r\n      getRepositoryToken(NotificationAnalyticsEvent)\r\n    );\r\n    templateRepository = moduleFixture.get<Repository<NotificationTemplate>>(\r\n      getRepositoryToken(NotificationTemplate)\r\n    );\r\n  });\r\n\r\n  afterAll(async () => {\r\n    await app.close();\r\n  });\r\n\r\n  beforeEach(async () => {\r\n    // Clean up database before each test\r\n    await analyticsRepository.delete({});\r\n    await executionRepository.delete({});\r\n    await workflowRepository.delete({});\r\n    await templateRepository.delete({});\r\n  });\r\n\r\n  describe('Asset Management ↔ Threat Intelligence Integration', () => {\r\n    it('should trigger notification workflow when new threat affects asset', async () => {\r\n      // Create workflow for threat notifications\r\n      const threatWorkflow = await workflowService.createWorkflow({\r\n        name: 'Threat Detection Workflow',\r\n        description: 'Notify when threats affect critical assets',\r\n        definition: {\r\n          startStep: 'assess_threat',\r\n          steps: [\r\n            {\r\n              id: 'assess_threat',\r\n              type: 'condition',\r\n              config: {\r\n                condition: {\r\n                  field: 'input.threat.severity',\r\n                  operator: 'in',\r\n                  value: ['high', 'critical'],\r\n                },\r\n              },\r\n              nextSteps: [\r\n                {\r\n                  stepId: 'notify_security_team',\r\n                  condition: {\r\n                    field: 'stepResult.result',\r\n                    operator: 'eq',\r\n                    value: true,\r\n                  },\r\n                },\r\n              ],\r\n            },\r\n            {\r\n              id: 'notify_security_team',\r\n              type: 'notification',\r\n              config: {\r\n                channel: 'email',\r\n                message: 'THREAT ALERT: {{input.threat.name}} affects {{input.asset.name}}',\r\n                recipients: ['<EMAIL>'],\r\n                priority: 'critical',\r\n              },\r\n            },\r\n          ],\r\n          triggers: [\r\n            {\r\n              type: 'event',\r\n              eventType: 'threat.asset_affected',\r\n              conditions: [\r\n                {\r\n                  field: 'asset.criticality',\r\n                  operator: 'eq',\r\n                  value: 'high',\r\n                },\r\n              ],\r\n            },\r\n          ],\r\n        },\r\n        status: 'active',\r\n        category: 'incident_response',\r\n      }, testUser);\r\n\r\n      // Simulate threat affecting asset event\r\n      const threatEvent = {\r\n        type: 'threat.asset_affected',\r\n        data: {\r\n          threat: {\r\n            id: 'threat-456',\r\n            name: 'CVE-2024-1234',\r\n            severity: 'critical',\r\n            type: 'vulnerability',\r\n          },\r\n          asset: {\r\n            id: 'asset-123',\r\n            name: 'Web Server 01',\r\n            criticality: 'high',\r\n            environment: 'production',\r\n          },\r\n          correlation: {\r\n            confidence: 0.95,\r\n            indicators: ['malicious-ip', 'suspicious-domain'],\r\n          },\r\n        },\r\n        timestamp: new Date(),\r\n      };\r\n\r\n      // Execute workflow with threat data\r\n      const execution = await workflowService.executeWorkflow(threatWorkflow.id, {\r\n        input: {\r\n          threat: threatEvent.data.threat,\r\n          asset: threatEvent.data.asset,\r\n          correlation: threatEvent.data.correlation,\r\n          user: testUser,\r\n        },\r\n        context: {\r\n          eventType: 'threat.asset_affected',\r\n          environment: 'production',\r\n          escalationLevel: 1,\r\n        },\r\n      }, testUser);\r\n\r\n      // Wait for execution\r\n      await new Promise(resolve => setTimeout(resolve, 3000));\r\n\r\n      // Verify execution completed successfully\r\n      const completedExecution = await executionRepository.findOne({\r\n        where: { id: execution.executionId },\r\n        relations: ['contexts'],\r\n      });\r\n\r\n      expect(completedExecution.status).toBe('completed');\r\n      expect(completedExecution.stepsCompleted).toBe(2); // condition + notification\r\n\r\n      // Verify analytics were recorded\r\n      const analyticsEvents = await analyticsRepository.find({\r\n        where: { notificationId: execution.executionId },\r\n      });\r\n\r\n      expect(analyticsEvents.length).toBeGreaterThan(0);\r\n      const event = analyticsEvents[0];\r\n      expect(event.metadata.threatId).toBe('threat-456');\r\n      expect(event.metadata.assetId).toBe('asset-123');\r\n    });\r\n\r\n    it('should correlate multiple threats affecting same asset', async () => {\r\n      // Create correlation workflow\r\n      const correlationWorkflow = await workflowService.createWorkflow({\r\n        name: 'Threat Correlation Workflow',\r\n        description: 'Correlate multiple threats for comprehensive analysis',\r\n        definition: {\r\n          startStep: 'correlate_threats',\r\n          steps: [\r\n            {\r\n              id: 'correlate_threats',\r\n              type: 'http_request',\r\n              config: {\r\n                url: 'http://threat-intelligence-service/correlate',\r\n                method: 'POST',\r\n                body: {\r\n                  assetId: '{{input.asset.id}}',\r\n                  timeWindow: '24h',\r\n                },\r\n              },\r\n              nextStep: 'assess_risk',\r\n            },\r\n            {\r\n              id: 'assess_risk',\r\n              type: 'condition',\r\n              config: {\r\n                condition: {\r\n                  field: 'stepResult.data.riskScore',\r\n                  operator: 'gt',\r\n                  value: 80,\r\n                },\r\n              },\r\n              nextSteps: [\r\n                {\r\n                  stepId: 'escalate_incident',\r\n                  condition: {\r\n                    field: 'stepResult.result',\r\n                    operator: 'eq',\r\n                    value: true,\r\n                  },\r\n                },\r\n              ],\r\n            },\r\n            {\r\n              id: 'escalate_incident',\r\n              type: 'notification',\r\n              config: {\r\n                channel: 'slack',\r\n                message: 'HIGH RISK: Multiple threats detected for {{input.asset.name}}',\r\n                recipients: ['#security-incidents'],\r\n                priority: 'critical',\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        status: 'active',\r\n      }, testUser);\r\n\r\n      // Execute with multiple threat data\r\n      const execution = await workflowService.executeWorkflow(correlationWorkflow.id, {\r\n        input: {\r\n          asset: {\r\n            id: 'asset-123',\r\n            name: 'Web Server 01',\r\n            criticality: 'high',\r\n          },\r\n          threats: [\r\n            { id: 'threat-1', severity: 'high' },\r\n            { id: 'threat-2', severity: 'critical' },\r\n            { id: 'threat-3', severity: 'medium' },\r\n          ],\r\n          user: testUser,\r\n        },\r\n      }, testUser);\r\n\r\n      await new Promise(resolve => setTimeout(resolve, 3000));\r\n\r\n      const completedExecution = await executionRepository.findOne({\r\n        where: { id: execution.executionId },\r\n      });\r\n\r\n      expect(completedExecution.status).toBe('completed');\r\n    });\r\n  });\r\n\r\n  describe('User Management Authentication Integration', () => {\r\n    it('should validate user permissions for notification workflows', async () => {\r\n      // Create workflow requiring specific permissions\r\n      const restrictedWorkflow = await workflowService.createWorkflow({\r\n        name: 'Restricted Security Workflow',\r\n        description: 'Requires security permissions',\r\n        definition: {\r\n          startStep: 'check_permissions',\r\n          steps: [\r\n            {\r\n              id: 'check_permissions',\r\n              type: 'condition',\r\n              config: {\r\n                condition: {\r\n                  field: 'input.user.permissions',\r\n                  operator: 'contains',\r\n                  value: 'security.admin',\r\n                },\r\n              },\r\n              nextSteps: [\r\n                {\r\n                  stepId: 'send_security_alert',\r\n                  condition: {\r\n                    field: 'stepResult.result',\r\n                    operator: 'eq',\r\n                    value: true,\r\n                  },\r\n                },\r\n              ],\r\n            },\r\n            {\r\n              id: 'send_security_alert',\r\n              type: 'notification',\r\n              config: {\r\n                channel: 'email',\r\n                message: 'Security alert authorized by {{input.user.email}}',\r\n                recipients: ['<EMAIL>'],\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        status: 'active',\r\n        configuration: {\r\n          permissions: {\r\n            execute: ['security.admin', 'admin'],\r\n            modify: ['admin'],\r\n            view: ['security.user', 'security.admin', 'admin'],\r\n          },\r\n        },\r\n      }, testUser);\r\n\r\n      // Test with authorized user\r\n      const authorizedUser = {\r\n        ...testUser,\r\n        permissions: ['security.admin', 'notification.send'],\r\n      };\r\n\r\n      const authorizedExecution = await workflowService.executeWorkflow(\r\n        restrictedWorkflow.id,\r\n        {\r\n          input: {\r\n            user: authorizedUser,\r\n            alert: { severity: 'high', message: 'Security incident detected' },\r\n          },\r\n        },\r\n        authorizedUser\r\n      );\r\n\r\n      await new Promise(resolve => setTimeout(resolve, 2000));\r\n\r\n      const authorizedResult = await executionRepository.findOne({\r\n        where: { id: authorizedExecution.executionId },\r\n      });\r\n\r\n      expect(authorizedResult.status).toBe('completed');\r\n\r\n      // Test with unauthorized user\r\n      const unauthorizedUser = {\r\n        ...testUser,\r\n        permissions: ['notification.send'], // Missing security.admin\r\n      };\r\n\r\n      try {\r\n        await workflowService.executeWorkflow(\r\n          restrictedWorkflow.id,\r\n          {\r\n            input: {\r\n              user: unauthorizedUser,\r\n              alert: { severity: 'high', message: 'Security incident detected' },\r\n            },\r\n          },\r\n          unauthorizedUser\r\n        );\r\n      } catch (error) {\r\n        expect(error.message).toContain('permission');\r\n      }\r\n    });\r\n\r\n    it('should track user actions in analytics', async () => {\r\n      const workflow = await workflowService.createWorkflow({\r\n        name: 'User Action Tracking Workflow',\r\n        definition: {\r\n          startStep: 'log_action',\r\n          steps: [\r\n            {\r\n              id: 'log_action',\r\n              type: 'notification',\r\n              config: {\r\n                channel: 'email',\r\n                message: 'Action performed by {{input.user.email}}',\r\n                recipients: ['<EMAIL>'],\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        status: 'active',\r\n      }, testUser);\r\n\r\n      const execution = await workflowService.executeWorkflow(workflow.id, {\r\n        input: {\r\n          user: testUser,\r\n          action: 'security_alert_triggered',\r\n          metadata: {\r\n            source: 'automated_scan',\r\n            severity: 'medium',\r\n          },\r\n        },\r\n      }, testUser);\r\n\r\n      await new Promise(resolve => setTimeout(resolve, 2000));\r\n\r\n      // Verify analytics include user information\r\n      const analyticsEvents = await analyticsRepository.find({\r\n        where: { notificationId: execution.executionId },\r\n      });\r\n\r\n      expect(analyticsEvents.length).toBeGreaterThan(0);\r\n      const event = analyticsEvents[0];\r\n      expect(event.metadata.userId).toBe(testUser.id);\r\n      expect(event.metadata.userEmail).toBe(testUser.email);\r\n      expect(event.metadata.action).toBe('security_alert_triggered');\r\n    });\r\n  });\r\n\r\n  describe('Event-Driven Architecture Validation', () => {\r\n    it('should process asset discovery events in real-time', async () => {\r\n      // Create asset discovery workflow\r\n      const discoveryWorkflow = await workflowService.createWorkflow({\r\n        name: 'Asset Discovery Workflow',\r\n        description: 'Process newly discovered assets',\r\n        definition: {\r\n          startStep: 'validate_asset',\r\n          steps: [\r\n            {\r\n              id: 'validate_asset',\r\n              type: 'condition',\r\n              config: {\r\n                condition: {\r\n                  field: 'input.asset.type',\r\n                  operator: 'in',\r\n                  value: ['server', 'database', 'application'],\r\n                },\r\n              },\r\n              nextStep: 'categorize_asset',\r\n            },\r\n            {\r\n              id: 'categorize_asset',\r\n              type: 'variable',\r\n              config: {\r\n                variables: {\r\n                  category: '{{input.asset.type}}',\r\n                  riskLevel: 'medium',\r\n                },\r\n              },\r\n              nextStep: 'notify_teams',\r\n            },\r\n            {\r\n              id: 'notify_teams',\r\n              type: 'notification',\r\n              config: {\r\n                channel: 'slack',\r\n                message: 'New {{variables.category}} discovered: {{input.asset.name}}',\r\n                recipients: ['#asset-management'],\r\n              },\r\n            },\r\n          ],\r\n          triggers: [\r\n            {\r\n              type: 'event',\r\n              eventType: 'asset.discovered',\r\n            },\r\n          ],\r\n        },\r\n        status: 'active',\r\n      }, testUser);\r\n\r\n      // Simulate asset discovery event\r\n      const assetDiscoveryEvent = {\r\n        type: 'asset.discovered',\r\n        data: {\r\n          asset: {\r\n            id: 'asset-new-789',\r\n            name: 'Database Server 02',\r\n            type: 'database',\r\n            ip: '**********',\r\n            discoveredAt: new Date(),\r\n          },\r\n          scanner: 'nmap',\r\n          confidence: 0.98,\r\n        },\r\n      };\r\n\r\n      const execution = await workflowService.executeWorkflow(discoveryWorkflow.id, {\r\n        input: {\r\n          asset: assetDiscoveryEvent.data.asset,\r\n          scanner: assetDiscoveryEvent.data.scanner,\r\n          user: testUser,\r\n        },\r\n        context: {\r\n          eventType: 'asset.discovered',\r\n          realTime: true,\r\n        },\r\n      }, testUser);\r\n\r\n      await new Promise(resolve => setTimeout(resolve, 3000));\r\n\r\n      const completedExecution = await executionRepository.findOne({\r\n        where: { id: execution.executionId },\r\n        relations: ['contexts'],\r\n      });\r\n\r\n      expect(completedExecution.status).toBe('completed');\r\n      expect(completedExecution.stepsCompleted).toBe(3);\r\n\r\n      // Verify real-time processing\r\n      const processingTime = completedExecution.duration;\r\n      expect(processingTime).toBeLessThan(5000); // Should process quickly\r\n    });\r\n\r\n    it('should handle event ordering and deduplication', async () => {\r\n      const eventWorkflow = await workflowService.createWorkflow({\r\n        name: 'Event Deduplication Workflow',\r\n        definition: {\r\n          startStep: 'check_duplicate',\r\n          steps: [\r\n            {\r\n              id: 'check_duplicate',\r\n              type: 'condition',\r\n              config: {\r\n                condition: {\r\n                  field: 'context.isDuplicate',\r\n                  operator: 'neq',\r\n                  value: true,\r\n                },\r\n              },\r\n              nextSteps: [\r\n                {\r\n                  stepId: 'process_event',\r\n                  condition: {\r\n                    field: 'stepResult.result',\r\n                    operator: 'eq',\r\n                    value: true,\r\n                  },\r\n                },\r\n              ],\r\n            },\r\n            {\r\n              id: 'process_event',\r\n              type: 'notification',\r\n              config: {\r\n                channel: 'email',\r\n                message: 'Processing unique event: {{input.event.id}}',\r\n                recipients: ['<EMAIL>'],\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        status: 'active',\r\n      }, testUser);\r\n\r\n      // Send duplicate events\r\n      const eventData = {\r\n        event: { id: 'event-123', type: 'security_alert' },\r\n        user: testUser,\r\n      };\r\n\r\n      const execution1 = await workflowService.executeWorkflow(eventWorkflow.id, {\r\n        input: eventData,\r\n        context: { isDuplicate: false },\r\n      }, testUser);\r\n\r\n      const execution2 = await workflowService.executeWorkflow(eventWorkflow.id, {\r\n        input: eventData,\r\n        context: { isDuplicate: true }, // Duplicate\r\n      }, testUser);\r\n\r\n      await new Promise(resolve => setTimeout(resolve, 3000));\r\n\r\n      const result1 = await executionRepository.findOne({\r\n        where: { id: execution1.executionId },\r\n      });\r\n\r\n      const result2 = await executionRepository.findOne({\r\n        where: { id: execution2.executionId },\r\n      });\r\n\r\n      // First execution should complete, second should skip processing\r\n      expect(result1.status).toBe('completed');\r\n      expect(result1.stepsCompleted).toBe(2);\r\n\r\n      expect(result2.status).toBe('completed');\r\n      expect(result2.stepsCompleted).toBe(1); // Only check_duplicate step\r\n    });\r\n  });\r\n\r\n  describe('Performance and Data Consistency', () => {\r\n    it('should maintain data consistency across modules', async () => {\r\n      // Create workflow that updates multiple modules\r\n      const consistencyWorkflow = await workflowService.createWorkflow({\r\n        name: 'Data Consistency Workflow',\r\n        definition: {\r\n          startStep: 'update_asset',\r\n          steps: [\r\n            {\r\n              id: 'update_asset',\r\n              type: 'http_request',\r\n              config: {\r\n                url: 'http://asset-service/assets/{{input.asset.id}}',\r\n                method: 'PATCH',\r\n                body: {\r\n                  status: 'compromised',\r\n                  lastIncident: '{{input.incident.id}}',\r\n                },\r\n              },\r\n              nextStep: 'update_threat_intel',\r\n            },\r\n            {\r\n              id: 'update_threat_intel',\r\n              type: 'http_request',\r\n              config: {\r\n                url: 'http://threat-intel-service/threats/{{input.threat.id}}',\r\n                method: 'PATCH',\r\n                body: {\r\n                  affectedAssets: ['{{input.asset.id}}'],\r\n                  status: 'active',\r\n                },\r\n              },\r\n              nextStep: 'notify_incident',\r\n            },\r\n            {\r\n              id: 'notify_incident',\r\n              type: 'notification',\r\n              config: {\r\n                channel: 'email',\r\n                message: 'Incident {{input.incident.id}} updated across all systems',\r\n                recipients: ['<EMAIL>'],\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        status: 'active',\r\n      }, testUser);\r\n\r\n      const execution = await workflowService.executeWorkflow(consistencyWorkflow.id, {\r\n        input: {\r\n          asset: { id: 'asset-123' },\r\n          threat: { id: 'threat-456' },\r\n          incident: { id: 'incident-789' },\r\n          user: testUser,\r\n        },\r\n      }, testUser);\r\n\r\n      await new Promise(resolve => setTimeout(resolve, 4000));\r\n\r\n      const completedExecution = await executionRepository.findOne({\r\n        where: { id: execution.executionId },\r\n        relations: ['contexts'],\r\n      });\r\n\r\n      expect(completedExecution.status).toBe('completed');\r\n      expect(completedExecution.stepsCompleted).toBe(3);\r\n\r\n      // Verify all steps completed successfully (indicating consistency)\r\n      completedExecution.contexts.forEach(context => {\r\n        expect(context.status).toBe('completed');\r\n        expect(context.error).toBeNull();\r\n      });\r\n    });\r\n\r\n    it('should handle high-volume cross-module events', async () => {\r\n      const highVolumeWorkflow = await workflowService.createWorkflow({\r\n        name: 'High Volume Processing Workflow',\r\n        definition: {\r\n          startStep: 'batch_process',\r\n          steps: [\r\n            {\r\n              id: 'batch_process',\r\n              type: 'notification',\r\n              config: {\r\n                channel: 'webhook',\r\n                message: 'Processing batch {{input.batch.id}}',\r\n                recipients: ['http://analytics-service/batch-processed'],\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        status: 'active',\r\n      }, testUser);\r\n\r\n      // Process multiple events concurrently\r\n      const batchSize = 50;\r\n      const executions = [];\r\n\r\n      for (let i = 0; i < batchSize; i++) {\r\n        const execution = workflowService.executeWorkflow(highVolumeWorkflow.id, {\r\n          input: {\r\n            batch: { id: `batch-${i}`, size: 100 },\r\n            user: testUser,\r\n          },\r\n        }, testUser);\r\n\r\n        executions.push(execution);\r\n      }\r\n\r\n      const results = await Promise.all(executions);\r\n      expect(results).toHaveLength(batchSize);\r\n\r\n      // Wait for all executions to complete\r\n      await new Promise(resolve => setTimeout(resolve, 10000));\r\n\r\n      // Verify all executions completed successfully\r\n      const completedExecutions = await executionRepository.find({\r\n        where: { workflowId: highVolumeWorkflow.id },\r\n      });\r\n\r\n      expect(completedExecutions).toHaveLength(batchSize);\r\n      completedExecutions.forEach(execution => {\r\n        expect(execution.status).toBe('completed');\r\n      });\r\n\r\n      // Verify analytics were recorded for all executions\r\n      const analyticsEvents = await analyticsRepository.find({\r\n        where: { workflowId: highVolumeWorkflow.id },\r\n      });\r\n\r\n      expect(analyticsEvents.length).toBeGreaterThanOrEqual(batchSize);\r\n    });\r\n  });\r\n});\r\n"], "version": 3}