ce98339575cf7fa7644472863d201be3
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const correlation_service_1 = require("./correlation.service");
const correlated_event_entity_1 = require("../../domain/entities/event/correlated-event.entity");
const ioc_value_object_1 = require("../../domain/value-objects/threat-indicators/ioc.value-object");
const domain_event_publisher_1 = require("../../../shared-kernel/domain/domain-event-publisher");
describe('CorrelationService', () => {
    let service;
    let mockEventPublisher;
    beforeEach(async () => {
        mockEventPublisher = {
            publishAll: jest.fn(),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                correlation_service_1.CorrelationService,
                {
                    provide: domain_event_publisher_1.DomainEventPublisher,
                    useValue: mockEventPublisher,
                },
            ],
        }).compile();
        service = module.get(correlation_service_1.CorrelationService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('correlateEvents', () => {
        it('should return empty result when insufficient events', async () => {
            const events = [createMockEnrichedEvent('event1')];
            const result = await service.correlateEvents({ events });
            expect(result.success).toBe(true);
            expect(result.correlatedEvents).toHaveLength(0);
            expect(result.statistics.correlationsFound).toBe(0);
        });
        it('should correlate events with temporal correlation', async () => {
            const now = new Date();
            const events = [
                createMockEnrichedEvent('event1', now),
                createMockEnrichedEvent('event2', new Date(now.getTime() + 30000)), // 30 seconds later
                createMockEnrichedEvent('event3', new Date(now.getTime() + 60000)), // 1 minute later
            ];
            const result = await service.correlateEvents({
                events,
                config: { temporalWindow: 5 } // 5 minutes
            });
            expect(result.success).toBe(true);
            expect(result.correlatedEvents.length).toBeGreaterThan(0);
            expect(result.correlatedEvents[0].correlationType).toBe(correlated_event_entity_1.CorrelationType.TEMPORAL);
            expect(mockEventPublisher.publishAll).toHaveBeenCalled();
        });
        it('should correlate events with indicator correlation', async () => {
            const commonIOC = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '*************', 'high', 'confirmed');
            const events = [
                createMockEnrichedEvent('event1', new Date(), [commonIOC]),
                createMockEnrichedEvent('event2', new Date(), [commonIOC]),
                createMockEnrichedEvent('event3', new Date(), [commonIOC]),
            ];
            const result = await service.correlateEvents({ events });
            expect(result.success).toBe(true);
            expect(result.correlatedEvents.length).toBeGreaterThan(0);
            const indicatorCorrelation = result.correlatedEvents.find(c => c.correlationType === correlated_event_entity_1.CorrelationType.INDICATOR);
            expect(indicatorCorrelation).toBeDefined();
            expect(indicatorCorrelation?.correlationAnalysis.commonIndicators).toHaveLength(1);
        });
        it('should handle correlation errors gracefully', async () => {
            const events = [
                createMockEnrichedEvent('event1'),
                createMockEnrichedEvent('event2'),
            ];
            // Mock an error in event publisher
            mockEventPublisher.publishAll.mockRejectedValue(new Error('Publisher error'));
            const result = await service.correlateEvents({ events });
            expect(result.success).toBe(true); // Should still succeed even if publishing fails
            expect(result.errors.length).toBe(0); // Publisher errors don't affect correlation
        });
        it('should respect correlation configuration', async () => {
            const events = [
                createMockEnrichedEvent('event1'),
                createMockEnrichedEvent('event2'),
            ];
            const result = await service.correlateEvents({
                events,
                config: {
                    minEvents: 5, // Require 5 events minimum
                    enabledTypes: [correlated_event_entity_1.CorrelationType.TEMPORAL],
                }
            });
            expect(result.success).toBe(true);
            expect(result.correlatedEvents).toHaveLength(0); // Not enough events
        });
        it('should focus on specific correlation types when requested', async () => {
            const now = new Date();
            const commonIOC = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '*************', 'high', 'confirmed');
            const events = [
                createMockEnrichedEvent('event1', now, [commonIOC]),
                createMockEnrichedEvent('event2', new Date(now.getTime() + 30000), [commonIOC]),
                createMockEnrichedEvent('event3', new Date(now.getTime() + 60000), [commonIOC]),
            ];
            const result = await service.correlateEvents({
                events,
                focusTypes: [correlated_event_entity_1.CorrelationType.INDICATOR] // Only indicator correlation
            });
            expect(result.success).toBe(true);
            expect(result.correlatedEvents.length).toBeGreaterThan(0);
            expect(result.correlatedEvents.every(c => c.correlationType === correlated_event_entity_1.CorrelationType.INDICATOR)).toBe(true);
        });
        it('should calculate correlation statistics correctly', async () => {
            const now = new Date();
            const events = [
                createMockEnrichedEvent('event1', now),
                createMockEnrichedEvent('event2', new Date(now.getTime() + 30000)),
                createMockEnrichedEvent('event3', new Date(now.getTime() + 60000)),
            ];
            const result = await service.correlateEvents({ events });
            expect(result.statistics.eventsProcessed).toBe(3);
            expect(result.statistics.correlationsFound).toBeGreaterThanOrEqual(0);
            expect(result.statistics.averageConfidence).toBeGreaterThanOrEqual(0);
            expect(result.statistics.typeDistribution).toBeDefined();
        });
        it('should deduplicate similar correlations', async () => {
            const now = new Date();
            const events = [
                createMockEnrichedEvent('event1', now),
                createMockEnrichedEvent('event2', new Date(now.getTime() + 30000)),
                createMockEnrichedEvent('event3', new Date(now.getTime() + 60000)),
            ];
            // Run correlation twice with same events
            const result1 = await service.correlateEvents({ events });
            const result2 = await service.correlateEvents({ events });
            // Should produce same number of correlations (no duplicates)
            expect(result1.correlatedEvents.length).toBe(result2.correlatedEvents.length);
        });
    });
    describe('correlateStreamingEvents', () => {
        it('should correlate new event with recent events', async () => {
            const now = new Date();
            const newEvent = createMockEnrichedEvent('new_event', now);
            const recentEvents = [
                createMockEnrichedEvent('recent1', new Date(now.getTime() - 30000)), // 30 seconds ago
                createMockEnrichedEvent('recent2', new Date(now.getTime() - 60000)), // 1 minute ago
            ];
            const correlations = await service.correlateStreamingEvents(newEvent, recentEvents, { temporalWindow: 5 } // 5 minutes
            );
            expect(correlations).toBeDefined();
            expect(Array.isArray(correlations)).toBe(true);
        });
        it('should filter events outside time window', async () => {
            const now = new Date();
            const newEvent = createMockEnrichedEvent('new_event', now);
            const recentEvents = [
                createMockEnrichedEvent('recent1', new Date(now.getTime() - 30000)), // 30 seconds ago
                createMockEnrichedEvent('old1', new Date(now.getTime() - 7200000)), // 2 hours ago
            ];
            const correlations = await service.correlateStreamingEvents(newEvent, recentEvents, { temporalWindow: 60 } // 60 minutes
            );
            // Should only correlate with recent events, not old ones
            expect(correlations).toBeDefined();
        });
        it('should focus on temporal and indicator correlations for streaming', async () => {
            const now = new Date();
            const commonIOC = ioc_value_object_1.IOC.create(ioc_value_object_1.IOCType.IP_ADDRESS, '*************', 'high', 'confirmed');
            const newEvent = createMockEnrichedEvent('new_event', now, [commonIOC]);
            const recentEvents = [
                createMockEnrichedEvent('recent1', new Date(now.getTime() - 30000), [commonIOC]),
            ];
            const correlations = await service.correlateStreamingEvents(newEvent, recentEvents);
            expect(correlations).toBeDefined();
            // Should focus on temporal and indicator correlations
            if (correlations.length > 0) {
                expect([correlated_event_entity_1.CorrelationType.TEMPORAL, correlated_event_entity_1.CorrelationType.INDICATOR])
                    .toContain(correlations[0].correlationType);
            }
        });
    });
    describe('error handling', () => {
        it('should handle invalid events gracefully', async () => {
            const invalidEvents = [
                null,
                undefined,
                {},
            ].filter(Boolean);
            const result = await service.correlateEvents({ events: invalidEvents });
            expect(result.success).toBe(true);
            expect(result.correlatedEvents).toHaveLength(0);
        });
        it('should continue processing when one correlation type fails', async () => {
            const events = [
                createMockEnrichedEvent('event1'),
                createMockEnrichedEvent('event2'),
            ];
            // This should not throw even if some correlation types fail internally
            const result = await service.correlateEvents({ events });
            expect(result.success).toBe(true);
            expect(result).toBeDefined();
        });
    });
    describe('performance', () => {
        it('should handle large number of events efficiently', async () => {
            const events = Array.from({ length: 50 }, (_, i) => createMockEnrichedEvent(`event${i}`, new Date()));
            const startTime = Date.now();
            const result = await service.correlateEvents({
                events,
                config: { maxEvents: 50 }
            });
            const duration = Date.now() - startTime;
            expect(result.success).toBe(true);
            expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
        });
        it('should respect maxEvents configuration', async () => {
            const events = Array.from({ length: 200 }, (_, i) => createMockEnrichedEvent(`event${i}`, new Date()));
            const result = await service.correlateEvents({
                events,
                config: { maxEvents: 50 }
            });
            expect(result.success).toBe(true);
            expect(result.statistics.eventsProcessed).toBeLessThanOrEqual(50);
        });
    });
    // Helper function to create mock enriched events
    function createMockEnrichedEvent(id, createdAt = new Date(), indicators = []) {
        const mockEvent = {
            id: { toString: () => id },
            createdAt,
            indicators,
            enrichmentScore: 80,
            isReadyForCorrelation: () => true,
            getDomainEvents: () => [],
            clearDomainEvents: () => { },
        };
        return mockEvent;
    }
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************