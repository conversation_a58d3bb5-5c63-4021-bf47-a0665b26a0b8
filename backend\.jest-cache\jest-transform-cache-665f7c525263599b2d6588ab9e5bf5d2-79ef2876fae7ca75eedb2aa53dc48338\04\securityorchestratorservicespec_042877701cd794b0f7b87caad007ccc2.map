{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\application\\services\\__tests__\\security-orchestrator.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,2CAAwC;AACxC,oFAQ0C;AAC1C,oFAAgF;AAChF,sFAAkF;AAClF,oGAAgG;AAChG,wGAAmG;AACnG,6GAA+F;AAC/F,6GAA+F;AAC/F,yHAA2G;AAC3G,iHAAmI;AAEnI,mFAA0E;AAC1E,2EAAkE;AAClE,+EAAsE;AACtE,qFAA4E;AAC5E,mGAA0F;AAC1F,6EAAoE;AACpE,iFAAwE;AACxE,4HAA0G;AAC1G,yCAA+B;AAC/B,yCAAqC;AAmBrC,yCAAsC;AACtC,yCAAuC;AAGvC,IAAA,oBAAQ,EAAC,6BAA6B,EAAE,GAAG,EAAE;IAC3C,IAAI,OAAoC,CAAC;IACzC,IAAI,eAA6C,CAAC;IAClD,IAAI,gBAA+C,CAAC;IACpD,IAAI,uBAA6D,CAAC;IAClE,IAAI,wBAA+D,CAAC;IACpE,IAAI,cAA2C,CAAC;IAChD,IAAI,cAA2C,CAAC;IAChD,IAAI,oBAAuD,CAAC;IAC5D,IAAI,gBAA+C,CAAC;IAEpD,sBAAsB;IACtB,MAAM,eAAe,GAAG,CAAC,YAA0B,EAAE,EAAS,EAAE;QAC9D,MAAM,SAAS,GAAG;YAChB,EAAE,EAAE,8CAAc,CAAC,QAAQ,EAAE;YAC7B,IAAI,EAAE,2BAAS,CAAC,cAAc;YAC9B,QAAQ,EAAE,mCAAa,CAAC,MAAM;YAC9B,MAAM,EAAE,+BAAW,CAAC,GAAG;YACvB,SAAS,EAAE,EAAE;YACb,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,MAAM,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,aAAa,EAAE;YACzC,cAAc,EAAE,GAAG,EAAE,CAAC,KAAK;YAC3B,GAAG,SAAS;SACb,CAAC;QACF,OAAO,SAAkB,CAAC;IAC5B,CAAC,CAAC;IAEF,MAAM,wBAAwB,GAAG,CAAC,cAAsB,CAAC,EAAE,EAAE,CAAC,CAAC;QAC7D,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YACtD,EAAE,EAAE,UAAU,CAAC,EAAE;YACjB,IAAI,EAAE,SAAS;YACf,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,qCAAc,CAAC,IAAI;YAC7B,UAAU,EAAE,EAAE;YACd,IAAI,EAAE,eAAe,CAAC,EAAE;YACxB,WAAW,EAAE,2BAA2B,CAAC,EAAE;YAC3C,UAAU,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC;YAC9B,MAAM,EAAE,mBAAmB;YAC3B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;QACH,UAAU,EAAE,EAAE;QACd,YAAY,EAAE,IAAI,IAAI,EAAE;QACxB,kBAAkB,EAAE,IAAI;QACxB,cAAc,EAAE,kBAAkB;QAClC,SAAS,EAAE,EAAE;QACb,UAAU,EAAE,EAAE;QACd,eAAe,EAAE,CAAC,qBAAqB,CAAC;QACxC,wBAAwB,EAAE,EAAE;KAC7B,CAAC,CAAC;IAEH,MAAM,2BAA2B,GAAG,CAAC,qBAA6B,CAAC,EAAE,EAAE,CAAC,CAAC;QACvE,eAAe,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,kBAAkB,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YACrE,EAAE,EAAE,QAAQ,CAAC,EAAE;YACf,KAAK,EAAE,sBAAsB,CAAC,EAAE;YAChC,QAAQ,EAAE,mDAAqB,CAAC,IAAI;YACpC,SAAS,EAAE,GAAG;YACd,SAAS,EAAE,EAAE;YACb,QAAQ,EAAE,WAAW;YACrB,IAAI,EAAE,qBAAqB;YAC3B,kBAAkB,EAAE,EAAE;YACtB,YAAY,EAAE,IAAI,IAAI,EAAE;YACxB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;QACH,QAAQ,EAAE,IAAI,IAAI,EAAE;QACpB,YAAY,EAAE,IAAI;QAClB,UAAU,EAAE,eAAe;QAC3B,QAAQ,EAAE,EAAE;QACZ,UAAU,EAAE,EAAE;QACd,MAAM,EAAE,EAAE,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,UAAU,EAAE;QACvE,UAAU,EAAE;YACV,oBAAoB,EAAE,kBAAkB;YACxC,yBAAyB,EAAE,EAAE;YAC7B,yBAAyB,EAAE,EAAE;YAC7B,qBAAqB,EAAE,EAAE;YACzB,QAAQ,EAAE,EAAE,YAAY,EAAE,GAAG,EAAE,kBAAkB,EAAE,EAAE,EAAE,kBAAkB,EAAE,CAAC,EAAE;YAC9E,WAAW,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,UAAU,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,EAAE;SAC7D;QACD,eAAe,EAAE,CAAC,wBAAwB,CAAC;QAC3C,MAAM,EAAE,EAAE;QACV,QAAQ,EAAE,EAAE;KACb,CAAC,CAAC;IAEH,MAAM,wBAAwB,GAAG,CAAC,kBAA0B,CAAC,EAAE,EAAE,CAAC,CAAC;QACjE,OAAO,EAAE,IAAI;QACb,WAAW,EAAE,UAAU;QACvB,eAAe;QACf,aAAa,EAAE,CAAC;QAChB,cAAc,EAAE,CAAC;QACjB,aAAa,EAAE,IAAI;QACnB,eAAe,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,eAAe,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YAClE,QAAQ,EAAE,UAAU,CAAC,EAAE;YACvB,UAAU,EAAE,6BAAU,CAAC,QAAQ;YAC/B,UAAU,EAAE,gBAAgB,CAAC,EAAE;YAC/B,MAAM,EAAE,iCAAY,CAAC,SAAS;YAC9B,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,OAAO,EAAE,IAAI,IAAI,EAAE;YACnB,QAAQ,EAAE,IAAI;YACd,UAAU,EAAE,EAAE;YACd,QAAQ,EAAE,GAAG;YACb,MAAM,EAAE;gBACN,cAAc,EAAE,KAAK;gBACrB,aAAa,EAAE,QAAQ;gBACvB,UAAU,EAAE,KAAK;gBACjB,cAAc,EAAE,KAAK;gBACrB,qBAAqB,EAAE,MAAM;aAC9B;SACF,CAAC,CAAC;QACH,kBAAkB,EAAE,GAAG;QACvB,MAAM,EAAE;YACN,gBAAgB,EAAE,aAAa;YAC/B,wBAAwB,EAAE,SAAS;YACnC,kBAAkB,EAAE,MAAM;YAC1B,iBAAiB,EAAE,KAAK;YACxB,mBAAmB,EAAE,MAAM;YAC3B,kBAAkB,EAAE,MAAM;YAC1B,gBAAgB,EAAE,MAAM;SACzB;QACD,MAAM,EAAE,EAAE;QACV,QAAQ,EAAE,EAAE;QACZ,eAAe,EAAE,EAAE;KACpB,CAAC,CAAC;IAEH,IAAA,sBAAU,EAAC,KAAK,IAAI,EAAE;QACpB,eAAe;QACf,MAAM,mBAAmB,GAAG;YAC1B,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;YACf,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;YACnB,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE;YAC1B,wBAAwB,EAAE,IAAI,CAAC,EAAE,EAAE;YACnC,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;SAC9B,CAAC;QAEF,MAAM,oBAAoB,GAAG;YAC3B,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;YACf,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;YACnB,mBAAmB,EAAE,IAAI,CAAC,EAAE,EAAE;SAC/B,CAAC;QAEF,MAAM,2BAA2B,GAAG;YAClC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;YACf,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;YACnB,0BAA0B,EAAE,IAAI,CAAC,EAAE,EAAE;SACtC,CAAC;QAEF,MAAM,4BAA4B,GAAG;YACnC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;YACf,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;SACpB,CAAC;QAEF,MAAM,kBAAkB,GAAG;YACzB,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE;YACvB,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE;SACxB,CAAC;QAEF,MAAM,kBAAkB,GAAG;YACzB,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE;YACvB,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;SACzB,CAAC;QAEF,MAAM,wBAAwB,GAAG;YAC/B,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;YACpB,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;SACvB,CAAC;QAEF,MAAM,oBAAoB,GAAG;YAC3B,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE;YAC1B,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;SACzB,CAAC;QAEF,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,2DAA2B;gBAC3B,EAAE,OAAO,EAAE,kCAAe,EAAE,QAAQ,EAAE,mBAAmB,EAAE;gBAC3D,EAAE,OAAO,EAAE,oCAAgB,EAAE,QAAQ,EAAE,oBAAoB,EAAE;gBAC7D,EAAE,OAAO,EAAE,kDAAuB,EAAE,QAAQ,EAAE,2BAA2B,EAAE;gBAC3E,EAAE,OAAO,EAAE,qDAAwB,EAAE,QAAQ,EAAE,4BAA4B,EAAE;gBAC7E,EAAE,OAAO,EAAE,0CAAc,EAAE,QAAQ,EAAE,kBAAkB,EAAE;gBACzD,EAAE,OAAO,EAAE,0CAAc,EAAE,QAAQ,EAAE,kBAAkB,EAAE;gBACzD,EAAE,OAAO,EAAE,sDAAoB,EAAE,QAAQ,EAAE,wBAAwB,EAAE;gBACrE,EAAE,OAAO,EAAE,8CAAgB,EAAE,QAAQ,EAAE,oBAAoB,EAAE;aAC9D;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAA8B,2DAA2B,CAAC,CAAC;QAC/E,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,kCAAe,CAAC,CAAC;QAC9C,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAC,oCAAgB,CAAC,CAAC;QAChD,uBAAuB,GAAG,MAAM,CAAC,GAAG,CAAC,kDAAuB,CAAC,CAAC;QAC9D,wBAAwB,GAAG,MAAM,CAAC,GAAG,CAAC,qDAAwB,CAAC,CAAC;QAChE,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC,0CAAc,CAAC,CAAC;QAC5C,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC,0CAAc,CAAC,CAAC;QAC5C,oBAAoB,GAAG,MAAM,CAAC,GAAG,CAAC,sDAAoB,CAAC,CAAC;QACxD,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAC,8CAAgB,CAAC,CAAC;QAEhD,qCAAqC;QACrC,IAAI,CAAC,KAAK,CAAC,eAAM,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,kBAAkB,EAAE,CAAC;QACzD,IAAI,CAAC,KAAK,CAAC,eAAM,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,kBAAkB,EAAE,CAAC;QAC3D,IAAI,CAAC,KAAK,CAAC,eAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,kBAAkB,EAAE,CAAC;QAC1D,IAAI,CAAC,KAAK,CAAC,eAAM,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,kBAAkB,EAAE,CAAC;IAC7D,CAAC,CAAC,CAAC;IAEH,IAAA,qBAAS,EAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,IAAA,cAAE,EAAC,mBAAmB,EAAE,GAAG,EAAE;YAC3B,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;YACnD,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,IAAA,cAAE,EAAC,wEAAwE,EAAE,KAAK,IAAI,EAAE;YACtF,UAAU;YACV,MAAM,MAAM,GAAG;gBACb,eAAe,CAAC,EAAE,QAAQ,EAAE,mCAAa,CAAC,IAAI,EAAE,CAAC;gBACjD,eAAe,CAAC,EAAE,QAAQ,EAAE,mCAAa,CAAC,MAAM,EAAE,CAAC;gBACnD,eAAe,CAAC,EAAE,QAAQ,EAAE,mCAAa,CAAC,GAAG,EAAE,CAAC;aACjD,CAAC;YAEF,MAAM,oBAAoB,GAAG;gBAC3B,OAAO,EAAE,IAAI;gBACb,WAAW,EAAE,WAAW;gBACxB,eAAe,EAAE,CAAC,YAAY,EAAE,eAAe,CAAC;gBAChD,YAAY,EAAE,EAAE;gBAChB,oBAAoB,EAAE,IAAI;gBAC1B,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,aAAa,EAAE,EAAE;gBACjB,OAAO,EAAE;oBACP,eAAe,EAAE,CAAC;oBAClB,gBAAgB,EAAE,CAAC;oBACnB,YAAY,EAAE,CAAC;oBACf,aAAa,EAAE,CAAC;oBAChB,mBAAmB,EAAE,IAAI;oBACzB,oBAAoB,EAAE,IAAI;oBAC1B,YAAY,EAAE,EAAE;iBACjB;aACF,CAAC;YAEF,cAAc,CAAC,YAAY,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;YACpE,cAAc,CAAC,YAAY,CAAC,iBAAiB,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3E,oBAAoB,CAAC,SAAS,CAAC,iBAAiB,CAAC,2BAA2B,CAAC,CAAC,CAAC,CAAC,CAAC;YACjF,gBAAgB,CAAC,eAAe,CAAC,iBAAiB,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC;YAChF,eAAe,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YAE/D,MAAM,OAAO,GAA0C;gBACrD,QAAQ,EAAE,8CAAgB,CAAC,IAAI;gBAC/B,IAAI,EAAE,0CAAY,CAAC,SAAS;gBAC5B,YAAY,EAAE,IAAI;aACnB,CAAC;YAEF,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,2BAA2B,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAE1E,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,iCAAiC;YAC/E,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,uCAAuC;YAC1F,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,iCAAiC;YAC/E,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,UAAU;YACV,MAAM,MAAM,GAAG;gBACb,eAAe,CAAC,EAAE,QAAQ,EAAE,mCAAa,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC;gBAChE,eAAe,CAAC,EAAE,QAAQ,EAAE,mCAAa,CAAC,GAAG,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC;aAChE,CAAC;YAEF,MAAM,OAAO,GAA0C;gBACrD,WAAW,EAAE;oBACX;wBACE,EAAE,EAAE,eAAe;wBACnB,IAAI,EAAE,sBAAsB;wBAC5B,WAAW,EAAE,iDAAiD;wBAC9D,SAAS,EAAE,EAAE,kBAAkB,EAAE,EAAE,EAAE;wBACrC,MAAM,EAAE,mDAAmB,CAAC,eAAe;wBAC3C,QAAQ,EAAE,CAAC;wBACX,OAAO,EAAE,IAAI;qBACd;iBACF;aACF,CAAC;YAEF,cAAc,CAAC,YAAY,CAAC,iBAAiB,CAAC;gBAC5C,OAAO,EAAE,IAAI;gBACb,WAAW,EAAE,WAAW;gBACxB,eAAe,EAAE,EAAE;gBACnB,YAAY,EAAE,EAAE;gBAChB,oBAAoB,EAAE,IAAI;gBAC1B,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,aAAa,EAAE,EAAE;gBACjB,OAAO,EAAE;oBACP,eAAe,EAAE,CAAC;oBAClB,gBAAgB,EAAE,CAAC;oBACnB,YAAY,EAAE,CAAC;oBACf,aAAa,EAAE,CAAC;oBAChB,mBAAmB,EAAE,IAAI;oBACzB,oBAAoB,EAAE,IAAI;oBAC1B,YAAY,EAAE,EAAE;iBACjB;aACF,CAAC,CAAC;YACH,cAAc,CAAC,YAAY,CAAC,iBAAiB,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC;YAE3E,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,2BAA2B,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAE1E,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,iCAAiC;YAC/E,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;YACpE,UAAU;YACV,MAAM,MAAM,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC;YACnC,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;YAE7C,cAAc,CAAC,YAAY,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YACrD,cAAc,CAAC,YAAY,CAAC,iBAAiB,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC;YAE3E,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAAC;YAEjE,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,4DAA4D;YAC/F,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YACzE,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,IAAA,cAAE,EAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,UAAU;YACV,MAAM,MAAM,GAAG,CAAC,eAAe,CAAC,EAAE,QAAQ,EAAE,mCAAa,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YAEvE,cAAc,CAAC,YAAY,CAAC,iBAAiB,CAAC;gBAC5C,OAAO,EAAE,IAAI;gBACb,WAAW,EAAE,WAAW;gBACxB,eAAe,EAAE,CAAC,YAAY,EAAE,eAAe,EAAE,YAAY,CAAC;gBAC9D,YAAY,EAAE,EAAE;gBAChB,oBAAoB,EAAE,IAAI;gBAC1B,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,aAAa,EAAE;oBACb,UAAU,EAAE,UAAU;oBACtB,QAAQ,EAAE,YAAY;iBACvB;gBACD,OAAO,EAAE;oBACP,eAAe,EAAE,CAAC;oBAClB,gBAAgB,EAAE,CAAC;oBACnB,YAAY,EAAE,CAAC;oBACf,aAAa,EAAE,CAAC;oBAChB,mBAAmB,EAAE,IAAI;oBACzB,oBAAoB,EAAE,IAAI;oBAC1B,YAAY,EAAE,EAAE;iBACjB;aACF,CAAC,CAAC;YAEH,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;YAE3D,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;YAClC,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,oBAAoB,CACtD,MAAM,CAAC,CAAC,CAAC,EACT,MAAM,CAAC,gBAAgB,CAAC;gBACtB,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,MAAM,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAC9B,mBAAmB,EAAE,IAAI;oBACzB,gBAAgB,EAAE,IAAI;oBACtB,iBAAiB,EAAE,IAAI;oBACvB,oBAAoB,EAAE,IAAI;iBAC3B,CAAC;aACH,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,IAAA,cAAE,EAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACrE,UAAU;YACV,MAAM,QAAQ,GAA0B;gBACtC,SAAS,EAAE;oBACT,KAAK,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;oBAC7B,GAAG,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;iBAC5B;gBACD,UAAU,EAAE,CAAC,2BAAS,CAAC,cAAc,EAAE,2BAAS,CAAC,gBAAgB,CAAC;gBAClE,cAAc,EAAE,CAAC,mCAAa,CAAC,IAAI,EAAE,mCAAa,CAAC,QAAQ,CAAC;gBAC5D,UAAU,EAAE,GAAG;gBACf,kBAAkB,EAAE,IAAI;gBACxB,sBAAsB,EAAE,IAAI;aAC7B,CAAC;YAEF,MAAM,aAAa,GAAG;gBACpB,eAAe,CAAC,EAAE,IAAI,EAAE,2BAAS,CAAC,cAAc,EAAE,QAAQ,EAAE,mCAAa,CAAC,IAAI,EAAE,CAAC;gBACjF,eAAe,CAAC,EAAE,IAAI,EAAE,2BAAS,CAAC,gBAAgB,EAAE,QAAQ,EAAE,mCAAa,CAAC,QAAQ,EAAE,CAAC;aACxF,CAAC;YAEF,eAAe,CAAC,eAAe,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YACjE,cAAc,CAAC,YAAY,CAAC,iBAAiB,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC;YAE3E,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAE5D,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,iCAAiC;YAC5E,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,gBAAgB,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAChE,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,UAAU,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAC1D,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,oBAAoB,CAC1D,QAAQ,CAAC,SAAS,CAAC,KAAK,EACxB,QAAQ,CAAC,SAAS,CAAC,GAAG,EACtB,QAAQ,CAAC,UAAU,CACpB,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,IAAA,cAAE,EAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,UAAU;YACV,MAAM,oBAAoB,GAAyB;gBACjD,UAAU,EAAE,cAAc;gBAC1B,QAAQ,EAAE,mCAAa,CAAC,QAAQ;gBAChC,QAAQ,EAAE,SAAS;gBACnB,OAAO,EAAE;oBACP;wBACE,EAAE,EAAE,UAAU;wBACd,IAAI,EAAE,6BAAU,CAAC,QAAQ;wBACzB,IAAI,EAAE,oBAAoB;wBAC1B,WAAW,EAAE,6BAA6B;wBAC1C,UAAU,EAAE,EAAE,SAAS,EAAE,eAAe,EAAE;wBAC1C,QAAQ,EAAE,EAAE;wBACZ,YAAY,EAAE,EAAE;wBAChB,OAAO,EAAE,KAAK;wBACd,WAAW,EAAE;4BACX,WAAW,EAAE,CAAC;4BACd,YAAY,EAAE,IAAI;4BAClB,iBAAiB,EAAE,CAAC;4BACpB,QAAQ,EAAE,KAAK;4BACf,eAAe,EAAE,CAAC,SAAS,EAAE,eAAe,CAAC;yBAC9C;wBACD,gBAAgB,EAAE,KAAK;wBACvB,iBAAiB,EAAE,IAAI;qBACxB;iBACF;gBACD,eAAe,EAAE,EAAE;gBACnB,QAAQ,EAAE;oBACR,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB;gBACD,YAAY,EAAE,EAAE;gBAChB,iBAAiB,EAAE;oBACjB,QAAQ,EAAE,EAAE;oBACZ,SAAS,EAAE,WAAkB;oBAC7B,SAAS,EAAE,EAAE;oBACb,iBAAiB,EAAE,EAAE;iBACtB;aACF,CAAC;YAEF,gBAAgB,CAAC,aAAa,CAAC,iBAAiB,CAAC;gBAC/C,QAAQ,EAAE,UAAU;gBACpB,UAAU,EAAE,6BAAU,CAAC,QAAQ;gBAC/B,UAAU,EAAE,oBAAoB;gBAChC,MAAM,EAAE,iCAAY,CAAC,SAAS;gBAC9B,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,OAAO,EAAE,IAAI,IAAI,EAAE;gBACnB,QAAQ,EAAE,IAAI;gBACd,UAAU,EAAE,EAAE,SAAS,EAAE,eAAe,EAAE;gBAC1C,QAAQ,EAAE,GAAG;gBACb,MAAM,EAAE;oBACN,cAAc,EAAE,KAAK;oBACrB,aAAa,EAAE,QAAQ;oBACvB,UAAU,EAAE,KAAK;oBACjB,cAAc,EAAE,KAAK;oBACrB,qBAAqB,EAAE,MAAM;iBAC9B;aACF,CAAC,CAAC;YAEH,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,uBAAuB,CAAC,oBAAoB,CAAC,CAAC;YAE3E,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClD,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,6BAAU,CAAC,QAAQ,EACnB,EAAE,SAAS,EAAE,eAAe,EAAE,CAC/B,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,IAAA,cAAE,EAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;YACtE,UAAU;YACV,MAAM,SAAS,GAAG;gBAChB,KAAK,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBAC7B,GAAG,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;aAC5B,CAAC;YAEF,MAAM,cAAc,GAAG;gBACrB,WAAW,EAAE,IAAI;gBACjB,YAAY,EAAE,EAAE,cAAc,EAAE,GAAG,EAAE,gBAAgB,EAAE,GAAG,EAAE,iBAAiB,EAAE,GAAG,EAAE;gBACpF,gBAAgB,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,EAAE;gBACpE,cAAc,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,WAAW,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE;gBAC7D,gBAAgB,EAAE,EAAE;gBACpB,kBAAkB,EAAE,GAAG;gBACvB,iBAAiB,EAAE;oBACjB,qBAAqB,EAAE,IAAI;oBAC3B,qBAAqB,EAAE,EAAE;oBACzB,yBAAyB,EAAE,GAAG;iBAC/B;aACF,CAAC;YAEF,MAAM,eAAe,GAAG;gBACtB,YAAY,EAAE,GAAG;gBACjB,aAAa,EAAE,EAAE;gBACjB,eAAe,EAAE,GAAG;gBACpB,iBAAiB,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE;gBAClE,iBAAiB,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;gBAC1E,aAAa,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;gBACjE,gBAAgB,EAAE,EAAE;gBACpB,mBAAmB,EAAE,EAAE;gBACvB,mBAAmB,EAAE,EAAE;gBACvB,qBAAqB,EAAE,QAAQ,EAAE,UAAU;gBAC3C,aAAa,EAAE,CAAC,EAAE,SAAS,EAAE,cAAc,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;gBACzD,kBAAkB,EAAE,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;gBACvD,oBAAoB,EAAE,EAAE,eAAe,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE;gBAC5D,oBAAoB,EAAE,EAAE,YAAY,EAAE,EAAE,EAAE,aAAa,EAAE,CAAC,EAAE;aAC7D,CAAC;YAEF,MAAM,sBAAsB,GAAG;gBAC7B,oBAAoB,EAAE,GAAG;gBACzB,qBAAqB,EAAE,GAAG;gBAC1B,yBAAyB,EAAE,GAAG;gBAC9B,yBAAyB,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE;gBAC5E,uBAAuB,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE,WAAW,EAAE,EAAE,EAAE,UAAU,EAAE,GAAG,EAAE;gBAC7F,yBAAyB,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,cAAc,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE;gBACtF,gBAAgB,EAAE,EAAE;gBACpB,gBAAgB,EAAE,GAAG;gBACrB,0BAA0B,EAAE,GAAG;gBAC/B,0BAA0B,EAAE,EAAE;gBAC9B,YAAY,EAAE,CAAC;gBACf,sBAAsB,EAAE,CAAC;gBACzB,sBAAsB,EAAE,SAAS,EAAE,SAAS;gBAC5C,kBAAkB,EAAE;oBAClB,eAAe,EAAE,GAAG;oBACpB,wBAAwB,EAAE,SAAS,EAAE,SAAS;oBAC9C,qBAAqB,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE;iBAChD;gBACD,WAAW,EAAE;oBACX,sBAAsB,EAAE,EAAE;oBAC1B,sBAAsB,EAAE,EAAE;oBAC1B,mBAAmB,EAAE,GAAG;iBACzB;aACF,CAAC;YAEF,eAAe,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YACrE,gBAAgB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YACxE,uBAAuB,CAAC,0BAA0B,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,CAAC;YAE7F,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;YAE/D,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,aAAa,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAC7D,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,aAAa,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;YAC5D,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAC7D,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;YAC/D,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,uBAAuB,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;YAC7E,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,WAAW,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,IAAA,cAAE,EAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,UAAU;YACV,MAAM,MAAM,GAAY,EAAE,CAAC;YAE3B,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAAC;YAEjE,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,IAAA,cAAE,EAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,UAAU;YACV,MAAM,MAAM,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC;YACnC,MAAM,eAAe,GAAG,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAEhE,eAAe,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAC5E,cAAc,CAAC,YAAY,CAAC,iBAAiB,CAAC;gBAC5C,OAAO,EAAE,IAAI;gBACb,WAAW,EAAE,WAAW;gBACxB,eAAe,EAAE,EAAE;gBACnB,YAAY,EAAE,EAAE;gBAChB,oBAAoB,EAAE,IAAI;gBAC1B,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,aAAa,EAAE,EAAE;gBACjB,OAAO,EAAE;oBACP,eAAe,EAAE,CAAC;oBAClB,gBAAgB,EAAE,CAAC;oBACnB,YAAY,EAAE,CAAC;oBACf,aAAa,EAAE,CAAC;oBAChB,mBAAmB,EAAE,IAAI;oBACzB,oBAAoB,EAAE,IAAI;oBAC1B,YAAY,EAAE,EAAE;iBACjB;aACF,CAAC,CAAC;YACH,cAAc,CAAC,YAAY,CAAC,iBAAiB,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC;YAE3E,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAAC;YAEjE,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,yDAAyD,CAAC,CAAC;QACrG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,oBAAQ,EAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,IAAA,cAAE,EAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,UAAU;YACV,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,eAAe,EAAE,CAAC,CAAC;YAClE,MAAM,cAAc,GAAG,IAAI,CAAC;YAE5B,cAAc,CAAC,YAAY,CAAC,iBAAiB,CAAC;gBAC5C,OAAO,EAAE,IAAI;gBACb,WAAW,EAAE,WAAW;gBACxB,eAAe,EAAE,EAAE;gBACnB,YAAY,EAAE,EAAE;gBAChB,oBAAoB,EAAE,cAAc;gBACpC,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,aAAa,EAAE,EAAE;gBACjB,OAAO,EAAE;oBACP,eAAe,EAAE,CAAC;oBAClB,gBAAgB,EAAE,CAAC;oBACnB,YAAY,EAAE,CAAC;oBACf,aAAa,EAAE,CAAC;oBAChB,mBAAmB,EAAE,cAAc;oBACnC,oBAAoB,EAAE,cAAc;oBACpC,YAAY,EAAE,EAAE;iBACjB;aACF,CAAC,CAAC;YAEH,cAAc,CAAC,YAAY,CAAC,iBAAiB,CAAC;gBAC5C,GAAG,wBAAwB,CAAC,CAAC,CAAC;gBAC9B,kBAAkB,EAAE,GAAG;aACxB,CAAC,CAAC;YAEH,oBAAoB,CAAC,SAAS,CAAC,iBAAiB,CAAC;gBAC/C,GAAG,2BAA2B,CAAC,CAAC,CAAC;gBACjC,YAAY,EAAE,IAAI;aACnB,CAAC,CAAC;YAEH,gBAAgB,CAAC,eAAe,CAAC,iBAAiB,CAAC;gBACjD,GAAG,wBAAwB,CAAC,CAAC,CAAC;gBAC9B,aAAa,EAAE,IAAI;aACpB,CAAC,CAAC;YAEH,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAAC;YAEjE,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACpE,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC3E,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,wBAAwB;YACrF,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,yBAAyB;YAC5F,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,yBAAyB;YACzF,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,IAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\application\\services\\__tests__\\security-orchestrator.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { Logger } from '@nestjs/common';\r\nimport { \r\n  SecurityOrchestratorService, \r\n  SecurityOrchestrationContext, \r\n  ThreatHuntingCriteria,\r\n  IncidentResponsePlan,\r\n  OrchestrationAction,\r\n  EscalationTrigger,\r\n  EscalationActionType\r\n} from '../security-orchestrator.service';\r\nimport { EventRepository } from '../../../domain/repositories/event.repository';\r\nimport { ThreatRepository } from '../../../domain/repositories/threat.repository';\r\nimport { VulnerabilityRepository } from '../../../domain/repositories/vulnerability.repository';\r\nimport { ResponseActionRepository } from '../../../domain/repositories/response-action.repository';\r\nimport { EventProcessor } from '../../../domain/interfaces/services/event-processor.interface';\r\nimport { ThreatDetector } from '../../../domain/interfaces/services/threat-detector.interface';\r\nimport { VulnerabilityScanner } from '../../../domain/interfaces/services/vulnerability-scanner.interface';\r\nimport { ResponseExecutor, ResponsePriority, ResponseMode } from '../../../domain/interfaces/services/response-executor.interface';\r\nimport { Event } from '../../../domain/entities/event.entity';\r\nimport { EventSeverity } from '../../../domain/enums/event-severity.enum';\r\nimport { EventType } from '../../../domain/enums/event-type.enum';\r\nimport { EventStatus } from '../../../domain/enums/event-status.enum';\r\nimport { ThreatSeverity } from '../../../domain/enums/threat-severity.enum';\r\nimport { VulnerabilitySeverity } from '../../../domain/enums/vulnerability-severity.enum';\r\nimport { ActionType } from '../../../domain/enums/action-type.enum';\r\nimport { ActionStatus } from '../../../domain/enums/action-status.enum';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { it } from 'node:test';\r\nimport { describe } from 'node:test';\r\nimport { afterEach } from 'node:test';\r\nimport { beforeEach } from 'node:test';\r\nimport { describe } from 'node:test';\r\n\r\ndescribe('SecurityOrchestratorService', () => {\r\n  let service: SecurityOrchestratorService;\r\n  let eventRepository: jest.Mocked<EventRepository>;\r\n  let threatRepository: jest.Mocked<ThreatRepository>;\r\n  let vulnerabilityRepository: jest.Mocked<VulnerabilityRepository>;\r\n  let responseActionRepository: jest.Mocked<ResponseActionRepository>;\r\n  let eventProcessor: jest.Mocked<EventProcessor>;\r\n  let threatDetector: jest.Mocked<ThreatDetector>;\r\n  let vulnerabilityScanner: jest.Mocked<VulnerabilityScanner>;\r\n  let responseExecutor: jest.Mocked<ResponseExecutor>;\r\n\r\n  // Test data factories\r\n  const createMockEvent = (overrides: Partial<any> = {}): Event => {\r\n    const mockEvent = {\r\n      id: UniqueEntityId.generate(),\r\n      type: EventType.SECURITY_ALERT,\r\n      severity: EventSeverity.MEDIUM,\r\n      status: EventStatus.NEW,\r\n      riskScore: 50,\r\n      timestamp: new Date(),\r\n      source: { toString: () => 'test-source' },\r\n      isHighSeverity: () => false,\r\n      ...overrides,\r\n    };\r\n    return mockEvent as Event;\r\n  };\r\n\r\n  const createMockThreatAnalysis = (threatCount: number = 1) => ({\r\n    threats: Array.from({ length: threatCount }, (_, i) => ({\r\n      id: `threat-${i}`,\r\n      type: 'MALWARE',\r\n      category: 'NETWORK',\r\n      severity: ThreatSeverity.HIGH,\r\n      confidence: 85,\r\n      name: `Test Threat ${i}`,\r\n      description: `Test threat description ${i}`,\r\n      indicators: [`indicator-${i}`],\r\n      source: 'INTERNAL_ANALYSIS',\r\n      timestamp: new Date(),\r\n    })),\r\n    confidence: 85,\r\n    analysisTime: new Date(),\r\n    analysisDurationMs: 1000,\r\n    analysisMethod: 'MACHINE_LEARNING',\r\n    riskScore: 75,\r\n    indicators: [],\r\n    recommendations: ['Investigate further'],\r\n    falsePositiveProbability: 15,\r\n  });\r\n\r\n  const createMockVulnerabilityScan = (vulnerabilityCount: number = 1) => ({\r\n    vulnerabilities: Array.from({ length: vulnerabilityCount }, (_, i) => ({\r\n      id: `vuln-${i}`,\r\n      title: `Test Vulnerability ${i}`,\r\n      severity: VulnerabilitySeverity.HIGH,\r\n      cvssScore: 7.5,\r\n      riskScore: 80,\r\n      category: 'INJECTION',\r\n      type: 'KNOWN_VULNERABILITY',\r\n      affectedComponents: [],\r\n      discoveredAt: new Date(),\r\n      createdAt: new Date(),\r\n      updatedAt: new Date(),\r\n    })),\r\n    scanTime: new Date(),\r\n    scanDuration: 2000,\r\n    scanMethod: 'AUTHENTICATED',\r\n    coverage: 95,\r\n    confidence: 90,\r\n    target: { id: 'test-target', type: 'APPLICATION', address: 'test-app' },\r\n    statistics: {\r\n      totalVulnerabilities: vulnerabilityCount,\r\n      vulnerabilitiesBySeverity: {},\r\n      vulnerabilitiesByCategory: {},\r\n      vulnerabilitiesByType: {},\r\n      coverage: { portsScanned: 100, servicesIdentified: 10, componentsAnalyzed: 5 },\r\n      performance: { scanRate: 10, throughput: 100, accuracy: 95 },\r\n    },\r\n    recommendations: ['Apply security patches'],\r\n    errors: [],\r\n    warnings: [],\r\n  });\r\n\r\n  const createMockResponseResult = (actionsExecuted: number = 1) => ({\r\n    success: true,\r\n    executionId: 'exec-123',\r\n    actionsExecuted,\r\n    actionsFailed: 0,\r\n    actionsSkipped: 0,\r\n    executionTime: 5000,\r\n    executedActions: Array.from({ length: actionsExecuted }, (_, i) => ({\r\n      actionId: `action-${i}`,\r\n      actionType: ActionType.BLOCK_IP,\r\n      actionName: `Block Action ${i}`,\r\n      status: ActionStatus.COMPLETED,\r\n      startTime: new Date(),\r\n      endTime: new Date(),\r\n      duration: 1000,\r\n      parameters: {},\r\n      progress: 100,\r\n      impact: {\r\n        resourceImpact: 'LOW',\r\n        networkImpact: 'MEDIUM',\r\n        userImpact: 'LOW',\r\n        businessImpact: 'LOW',\r\n        securityEffectiveness: 'HIGH',\r\n      },\r\n    })),\r\n    effectivenessScore: 100,\r\n    impact: {\r\n      threatMitigation: 'SUBSTANTIAL',\r\n      vulnerabilityRemediation: 'PARTIAL',\r\n      systemAvailability: 'NONE',\r\n      performanceImpact: 'LOW',\r\n      securityImprovement: 'HIGH',\r\n      businessContinuity: 'NONE',\r\n      complianceImpact: 'NONE',\r\n    },\r\n    errors: [],\r\n    warnings: [],\r\n    recommendations: [],\r\n  });\r\n\r\n  beforeEach(async () => {\r\n    // Create mocks\r\n    const mockEventRepository = {\r\n      save: jest.fn(),\r\n      findById: jest.fn(),\r\n      findByTimeRange: jest.fn(),\r\n      findEventsForCorrelation: jest.fn(),\r\n      getEventStatistics: jest.fn(),\r\n    };\r\n\r\n    const mockThreatRepository = {\r\n      save: jest.fn(),\r\n      findById: jest.fn(),\r\n      getThreatStatistics: jest.fn(),\r\n    };\r\n\r\n    const mockVulnerabilityRepository = {\r\n      save: jest.fn(),\r\n      findById: jest.fn(),\r\n      getVulnerabilityStatistics: jest.fn(),\r\n    };\r\n\r\n    const mockResponseActionRepository = {\r\n      save: jest.fn(),\r\n      findById: jest.fn(),\r\n    };\r\n\r\n    const mockEventProcessor = {\r\n      processEvent: jest.fn(),\r\n      processBatch: jest.fn(),\r\n    };\r\n\r\n    const mockThreatDetector = {\r\n      analyzeEvent: jest.fn(),\r\n      analyzeEvents: jest.fn(),\r\n    };\r\n\r\n    const mockVulnerabilityScanner = {\r\n      scanEvent: jest.fn(),\r\n      scanTargets: jest.fn(),\r\n    };\r\n\r\n    const mockResponseExecutor = {\r\n      executeResponse: jest.fn(),\r\n      executeAction: jest.fn(),\r\n    };\r\n\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        SecurityOrchestratorService,\r\n        { provide: EventRepository, useValue: mockEventRepository },\r\n        { provide: ThreatRepository, useValue: mockThreatRepository },\r\n        { provide: VulnerabilityRepository, useValue: mockVulnerabilityRepository },\r\n        { provide: ResponseActionRepository, useValue: mockResponseActionRepository },\r\n        { provide: EventProcessor, useValue: mockEventProcessor },\r\n        { provide: ThreatDetector, useValue: mockThreatDetector },\r\n        { provide: VulnerabilityScanner, useValue: mockVulnerabilityScanner },\r\n        { provide: ResponseExecutor, useValue: mockResponseExecutor },\r\n      ],\r\n    }).compile();\r\n\r\n    service = module.get<SecurityOrchestratorService>(SecurityOrchestratorService);\r\n    eventRepository = module.get(EventRepository);\r\n    threatRepository = module.get(ThreatRepository);\r\n    vulnerabilityRepository = module.get(VulnerabilityRepository);\r\n    responseActionRepository = module.get(ResponseActionRepository);\r\n    eventProcessor = module.get(EventProcessor);\r\n    threatDetector = module.get(ThreatDetector);\r\n    vulnerabilityScanner = module.get(VulnerabilityScanner);\r\n    responseExecutor = module.get(ResponseExecutor);\r\n\r\n    // Suppress console logs during tests\r\n    jest.spyOn(Logger.prototype, 'log').mockImplementation();\r\n    jest.spyOn(Logger.prototype, 'error').mockImplementation();\r\n    jest.spyOn(Logger.prototype, 'warn').mockImplementation();\r\n    jest.spyOn(Logger.prototype, 'debug').mockImplementation();\r\n  });\r\n\r\n  afterEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('Service Initialization', () => {\r\n    it('should be defined', () => {\r\n      expect(service).toBeDefined();\r\n    });\r\n\r\n    it('should extend BaseService', () => {\r\n      expect(service).toHaveProperty('executeOperation');\r\n      expect(service).toHaveProperty('createContext');\r\n    });\r\n  });\r\n\r\n  describe('orchestrateSecurityWorkflow', () => {\r\n    it('should successfully orchestrate security workflow with multiple events', async () => {\r\n      // Arrange\r\n      const events = [\r\n        createMockEvent({ severity: EventSeverity.HIGH }),\r\n        createMockEvent({ severity: EventSeverity.MEDIUM }),\r\n        createMockEvent({ severity: EventSeverity.LOW }),\r\n      ];\r\n\r\n      const mockProcessingResult = {\r\n        success: true,\r\n        finalStatus: 'COMPLETED',\r\n        stagesCompleted: ['VALIDATION', 'NORMALIZATION'],\r\n        stagesFailed: [],\r\n        processingDurationMs: 1000,\r\n        errors: [],\r\n        warnings: [],\r\n        eventsCreated: {},\r\n        metrics: {\r\n          eventsProcessed: 1,\r\n          eventsSuccessful: 1,\r\n          eventsFailed: 0,\r\n          eventsSkipped: 0,\r\n          avgProcessingTimeMs: 1000,\r\n          peakProcessingTimeMs: 1000,\r\n          stageMetrics: {},\r\n        },\r\n      };\r\n\r\n      eventProcessor.processEvent.mockResolvedValue(mockProcessingResult);\r\n      threatDetector.analyzeEvent.mockResolvedValue(createMockThreatAnalysis(2));\r\n      vulnerabilityScanner.scanEvent.mockResolvedValue(createMockVulnerabilityScan(1));\r\n      responseExecutor.executeResponse.mockResolvedValue(createMockResponseResult(3));\r\n      eventRepository.findEventsForCorrelation.mockResolvedValue([]);\r\n\r\n      const context: Partial<SecurityOrchestrationContext> = {\r\n        priority: ResponsePriority.HIGH,\r\n        mode: ResponseMode.AUTOMATIC,\r\n        autoResponse: true,\r\n      };\r\n\r\n      // Act\r\n      const result = await service.orchestrateSecurityWorkflow(events, context);\r\n\r\n      // Assert\r\n      expect(result.success).toBe(true);\r\n      expect(result.data).toBeDefined();\r\n      expect(result.data!.eventsProcessed).toBe(3);\r\n      expect(result.data!.threatsDetected).toBe(6); // 2 threats per event * 3 events\r\n      expect(result.data!.vulnerabilitiesFound).toBe(3); // 1 vulnerability per event * 3 events\r\n      expect(result.data!.actionsExecuted).toBe(9); // 3 actions per event * 3 events\r\n      expect(result.data!.success).toBe(true);\r\n      expect(result.data!.recommendations).toBeDefined();\r\n      expect(result.data!.metrics).toBeDefined();\r\n    });\r\n\r\n    it('should handle orchestration with custom rules', async () => {\r\n      // Arrange\r\n      const events = [\r\n        createMockEvent({ severity: EventSeverity.HIGH, riskScore: 90 }),\r\n        createMockEvent({ severity: EventSeverity.LOW, riskScore: 20 }),\r\n      ];\r\n\r\n      const context: Partial<SecurityOrchestrationContext> = {\r\n        customRules: [\r\n          {\r\n            id: 'skip-low-risk',\r\n            name: 'Skip Low Risk Events',\r\n            description: 'Skip processing events with risk score below 50',\r\n            condition: { riskScoreThreshold: 50 },\r\n            action: OrchestrationAction.SKIP_PROCESSING,\r\n            priority: 1,\r\n            enabled: true,\r\n          },\r\n        ],\r\n      };\r\n\r\n      eventProcessor.processEvent.mockResolvedValue({\r\n        success: true,\r\n        finalStatus: 'COMPLETED',\r\n        stagesCompleted: [],\r\n        stagesFailed: [],\r\n        processingDurationMs: 1000,\r\n        errors: [],\r\n        warnings: [],\r\n        eventsCreated: {},\r\n        metrics: {\r\n          eventsProcessed: 1,\r\n          eventsSuccessful: 1,\r\n          eventsFailed: 0,\r\n          eventsSkipped: 0,\r\n          avgProcessingTimeMs: 1000,\r\n          peakProcessingTimeMs: 1000,\r\n          stageMetrics: {},\r\n        },\r\n      });\r\n      threatDetector.analyzeEvent.mockResolvedValue(createMockThreatAnalysis(0));\r\n\r\n      // Act\r\n      const result = await service.orchestrateSecurityWorkflow(events, context);\r\n\r\n      // Assert\r\n      expect(result.success).toBe(true);\r\n      expect(result.data!.eventsProcessed).toBe(1); // Only high-risk event processed\r\n      expect(eventProcessor.processEvent).toHaveBeenCalledTimes(1);\r\n    });\r\n\r\n    it('should handle errors gracefully during orchestration', async () => {\r\n      // Arrange\r\n      const events = [createMockEvent()];\r\n      const error = new Error('Processing failed');\r\n\r\n      eventProcessor.processEvent.mockRejectedValue(error);\r\n      threatDetector.analyzeEvent.mockResolvedValue(createMockThreatAnalysis(0));\r\n\r\n      // Act\r\n      const result = await service.orchestrateSecurityWorkflow(events);\r\n\r\n      // Assert\r\n      expect(result.success).toBe(true); // Service operation succeeds even if individual events fail\r\n      expect(result.data!.errors).toHaveLength(1);\r\n      expect(result.data!.errors[0].errorCode).toBe('EVENT_PROCESSING_FAILED');\r\n      expect(result.data!.errors[0].recoverable).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('processSecurityEvents', () => {\r\n    it('should process events through the complete pipeline', async () => {\r\n      // Arrange\r\n      const events = [createMockEvent({ severity: EventSeverity.CRITICAL })];\r\n\r\n      eventProcessor.processEvent.mockResolvedValue({\r\n        success: true,\r\n        finalStatus: 'COMPLETED',\r\n        stagesCompleted: ['VALIDATION', 'NORMALIZATION', 'ENRICHMENT'],\r\n        stagesFailed: [],\r\n        processingDurationMs: 2000,\r\n        errors: [],\r\n        warnings: [],\r\n        eventsCreated: {\r\n          normalized: 'norm-123',\r\n          enriched: 'enrich-123',\r\n        },\r\n        metrics: {\r\n          eventsProcessed: 1,\r\n          eventsSuccessful: 1,\r\n          eventsFailed: 0,\r\n          eventsSkipped: 0,\r\n          avgProcessingTimeMs: 2000,\r\n          peakProcessingTimeMs: 2000,\r\n          stageMetrics: {},\r\n        },\r\n      });\r\n\r\n      // Act\r\n      const result = await service.processSecurityEvents(events);\r\n\r\n      // Assert\r\n      expect(result.success).toBe(true);\r\n      expect(result.data).toBeDefined();\r\n      expect(eventProcessor.processEvent).toHaveBeenCalledWith(\r\n        events[0],\r\n        expect.objectContaining({\r\n          requestId: expect.any(String),\r\n          config: expect.objectContaining({\r\n            enableNormalization: true,\r\n            enableEnrichment: true,\r\n            enableCorrelation: true,\r\n            enableThreatAnalysis: true,\r\n          }),\r\n        })\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('performThreatHunting', () => {\r\n    it('should execute comprehensive threat hunting operation', async () => {\r\n      // Arrange\r\n      const criteria: ThreatHuntingCriteria = {\r\n        timeRange: {\r\n          start: new Date('2024-01-01'),\r\n          end: new Date('2024-01-02'),\r\n        },\r\n        eventTypes: [EventType.SECURITY_ALERT, EventType.MALWARE_DETECTED],\r\n        severityLevels: [EventSeverity.HIGH, EventSeverity.CRITICAL],\r\n        maxResults: 100,\r\n        includeCorrelation: true,\r\n        includePatternAnalysis: true,\r\n      };\r\n\r\n      const huntingEvents = [\r\n        createMockEvent({ type: EventType.SECURITY_ALERT, severity: EventSeverity.HIGH }),\r\n        createMockEvent({ type: EventType.MALWARE_DETECTED, severity: EventSeverity.CRITICAL }),\r\n      ];\r\n\r\n      eventRepository.findByTimeRange.mockResolvedValue(huntingEvents);\r\n      threatDetector.analyzeEvent.mockResolvedValue(createMockThreatAnalysis(3));\r\n\r\n      // Act\r\n      const result = await service.performThreatHunting(criteria);\r\n\r\n      // Assert\r\n      expect(result.success).toBe(true);\r\n      expect(result.data).toBeDefined();\r\n      expect(result.data!.eventsAnalyzed).toBe(2);\r\n      expect(result.data!.threatsFound).toBe(6); // 3 threats per event * 2 events\r\n      expect(result.data!.patternsDetected).toBeGreaterThanOrEqual(0);\r\n      expect(result.data!.correlations).toBeDefined();\r\n      expect(result.data!.recommendations).toBeDefined();\r\n      expect(result.data!.confidence).toBeGreaterThanOrEqual(0);\r\n      expect(eventRepository.findByTimeRange).toHaveBeenCalledWith(\r\n        criteria.timeRange.start,\r\n        criteria.timeRange.end,\r\n        criteria.maxResults\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('executeIncidentResponse', () => {\r\n    it('should execute incident response plan successfully', async () => {\r\n      // Arrange\r\n      const incidentResponsePlan: IncidentResponsePlan = {\r\n        incidentId: 'incident-123',\r\n        severity: EventSeverity.CRITICAL,\r\n        category: 'MALWARE',\r\n        actions: [\r\n          {\r\n            id: 'action-1',\r\n            type: ActionType.BLOCK_IP,\r\n            name: 'Block Malicious IP',\r\n            description: 'Block the source IP address',\r\n            parameters: { ipAddress: '*************' },\r\n            priority: 10,\r\n            dependencies: [],\r\n            timeout: 30000,\r\n            retryPolicy: {\r\n              maxAttempts: 3,\r\n              initialDelay: 1000,\r\n              backoffMultiplier: 2,\r\n              maxDelay: 10000,\r\n              retryableErrors: ['TIMEOUT', 'NETWORK_ERROR'],\r\n            },\r\n            approvalRequired: false,\r\n            rollbackSupported: true,\r\n          },\r\n        ],\r\n        escalationRules: [],\r\n        timeline: {\r\n          detection: new Date(),\r\n        },\r\n        stakeholders: [],\r\n        communicationPlan: {\r\n          channels: [],\r\n          frequency: 'IMMEDIATE' as any,\r\n          templates: {},\r\n          stakeholderMatrix: {},\r\n        },\r\n      };\r\n\r\n      responseExecutor.executeAction.mockResolvedValue({\r\n        actionId: 'action-1',\r\n        actionType: ActionType.BLOCK_IP,\r\n        actionName: 'Block Malicious IP',\r\n        status: ActionStatus.COMPLETED,\r\n        startTime: new Date(),\r\n        endTime: new Date(),\r\n        duration: 5000,\r\n        parameters: { ipAddress: '*************' },\r\n        progress: 100,\r\n        impact: {\r\n          resourceImpact: 'LOW',\r\n          networkImpact: 'MEDIUM',\r\n          userImpact: 'LOW',\r\n          businessImpact: 'LOW',\r\n          securityEffectiveness: 'HIGH',\r\n        },\r\n      });\r\n\r\n      // Act\r\n      const result = await service.executeIncidentResponse(incidentResponsePlan);\r\n\r\n      // Assert\r\n      expect(result.success).toBe(true);\r\n      expect(result.data).toBeDefined();\r\n      expect(result.data!.success).toBe(true);\r\n      expect(result.data!.actionsExecuted).toBe(1);\r\n      expect(result.data!.actionsFailed).toBe(0);\r\n      expect(result.data!.effectivenessScore).toBe(100);\r\n      expect(responseExecutor.executeAction).toHaveBeenCalledWith(\r\n        ActionType.BLOCK_IP,\r\n        { ipAddress: '*************' }\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('analyzeSecurityPosture', () => {\r\n    it('should perform comprehensive security posture analysis', async () => {\r\n      // Arrange\r\n      const timeRange = {\r\n        start: new Date('2024-01-01'),\r\n        end: new Date('2024-01-31'),\r\n      };\r\n\r\n      const mockEventStats = {\r\n        totalEvents: 1000,\r\n        eventsByType: { SECURITY_ALERT: 500, MALWARE_DETECTED: 300, INTRUSION_ATTEMPT: 200 },\r\n        eventsBySeverity: { LOW: 400, MEDIUM: 400, HIGH: 150, CRITICAL: 50 },\r\n        eventsByStatus: { NEW: 100, IN_PROGRESS: 200, RESOLVED: 700 },\r\n        averageRiskScore: 45,\r\n        highRiskEventCount: 200,\r\n        processingMetrics: {\r\n          averageProcessingTime: 2500,\r\n          failedProcessingCount: 25,\r\n          successfulProcessingCount: 975,\r\n        },\r\n      };\r\n\r\n      const mockThreatStats = {\r\n        totalThreats: 150,\r\n        activeThreats: 25,\r\n        resolvedThreats: 125,\r\n        threatsBySeverity: { LOW: 50, MEDIUM: 60, HIGH: 30, CRITICAL: 10 },\r\n        threatsByCategory: { MALWARE: 60, INTRUSION: 40, PHISHING: 30, OTHER: 20 },\r\n        threatsByType: { APT: 10, RANSOMWARE: 15, TROJAN: 35, OTHER: 90 },\r\n        averageRiskScore: 65,\r\n        highRiskThreatCount: 40,\r\n        criticalThreatCount: 10,\r\n        averageResolutionTime: 14400000, // 4 hours\r\n        topIndicators: [{ indicator: 'malicious-ip', count: 25 }],\r\n        topMitreTechniques: [{ technique: 'T1055', count: 15 }],\r\n        sourceIpDistribution: { '*************': 10, '********': 8 },\r\n        targetIpDistribution: { '**********': 12, '***********': 6 },\r\n      };\r\n\r\n      const mockVulnerabilityStats = {\r\n        totalVulnerabilities: 500,\r\n        activeVulnerabilities: 200,\r\n        remediatedVulnerabilities: 300,\r\n        vulnerabilitiesBySeverity: { LOW: 200, MEDIUM: 200, HIGH: 80, CRITICAL: 20 },\r\n        vulnerabilitiesByStatus: { DISCOVERED: 50, CONFIRMED: 100, IN_PROGRESS: 50, REMEDIATED: 300 },\r\n        vulnerabilitiesByCategory: { INJECTION: 100, XSS: 80, AUTHENTICATION: 60, OTHER: 260 },\r\n        averageRiskScore: 55,\r\n        averageCvssScore: 6.2,\r\n        highRiskVulnerabilityCount: 100,\r\n        criticalVulnerabilityCount: 20,\r\n        zeroDayCount: 5,\r\n        activelyExploitedCount: 8,\r\n        averageRemediationTime: 604800000, // 7 days\r\n        remediationMetrics: {\r\n          totalRemediated: 300,\r\n          averageTimeToRemediation: 518400000, // 6 days\r\n          remediationByPriority: { 1: 20, 2: 80, 3: 200 },\r\n        },\r\n        assetImpact: {\r\n          criticalAssetsAffected: 15,\r\n          externalAssetsAffected: 25,\r\n          totalAssetsAffected: 150,\r\n        },\r\n      };\r\n\r\n      eventRepository.getEventStatistics.mockResolvedValue(mockEventStats);\r\n      threatRepository.getThreatStatistics.mockResolvedValue(mockThreatStats);\r\n      vulnerabilityRepository.getVulnerabilityStatistics.mockResolvedValue(mockVulnerabilityStats);\r\n\r\n      // Act\r\n      const result = await service.analyzeSecurityPosture(timeRange);\r\n\r\n      // Assert\r\n      expect(result.success).toBe(true);\r\n      expect(result.data).toBeDefined();\r\n      expect(result.data!.timeRange).toEqual(timeRange);\r\n      expect(result.data!.securityScore).toBeGreaterThanOrEqual(0);\r\n      expect(result.data!.securityScore).toBeLessThanOrEqual(100);\r\n      expect(result.data!.riskLevel).toBeDefined();\r\n      expect(result.data!.eventStatistics).toEqual(mockEventStats);\r\n      expect(result.data!.threatStatistics).toEqual(mockThreatStats);\r\n      expect(result.data!.vulnerabilityStatistics).toEqual(mockVulnerabilityStats);\r\n      expect(result.data!.recommendations).toBeDefined();\r\n      expect(result.data!.generatedAt).toBeInstanceOf(Date);\r\n    });\r\n  });\r\n\r\n  describe('Error Handling and Edge Cases', () => {\r\n    it('should handle empty event arrays gracefully', async () => {\r\n      // Arrange\r\n      const events: Event[] = [];\r\n\r\n      // Act\r\n      const result = await service.orchestrateSecurityWorkflow(events);\r\n\r\n      // Assert\r\n      expect(result.success).toBe(true);\r\n      expect(result.data!.eventsProcessed).toBe(0);\r\n      expect(result.data!.threatsDetected).toBe(0);\r\n      expect(result.data!.vulnerabilitiesFound).toBe(0);\r\n      expect(result.data!.actionsExecuted).toBe(0);\r\n    });\r\n\r\n    it('should handle repository connection failures', async () => {\r\n      // Arrange\r\n      const events = [createMockEvent()];\r\n      const repositoryError = new Error('Database connection failed');\r\n\r\n      eventRepository.findEventsForCorrelation.mockRejectedValue(repositoryError);\r\n      eventProcessor.processEvent.mockResolvedValue({\r\n        success: true,\r\n        finalStatus: 'COMPLETED',\r\n        stagesCompleted: [],\r\n        stagesFailed: [],\r\n        processingDurationMs: 1000,\r\n        errors: [],\r\n        warnings: [],\r\n        eventsCreated: {},\r\n        metrics: {\r\n          eventsProcessed: 1,\r\n          eventsSuccessful: 1,\r\n          eventsFailed: 0,\r\n          eventsSkipped: 0,\r\n          avgProcessingTimeMs: 1000,\r\n          peakProcessingTimeMs: 1000,\r\n          stageMetrics: {},\r\n        },\r\n      });\r\n      threatDetector.analyzeEvent.mockResolvedValue(createMockThreatAnalysis(0));\r\n\r\n      // Act\r\n      const result = await service.orchestrateSecurityWorkflow(events);\r\n\r\n      // Assert\r\n      expect(result.success).toBe(true);\r\n      expect(result.data!.warnings).toContain('Correlation analysis failed: Database connection failed');\r\n    });\r\n  });\r\n\r\n  describe('Performance and Metrics', () => {\r\n    it('should calculate accurate performance metrics', async () => {\r\n      // Arrange\r\n      const events = Array.from({ length: 5 }, () => createMockEvent());\r\n      const processingTime = 2000;\r\n\r\n      eventProcessor.processEvent.mockResolvedValue({\r\n        success: true,\r\n        finalStatus: 'COMPLETED',\r\n        stagesCompleted: [],\r\n        stagesFailed: [],\r\n        processingDurationMs: processingTime,\r\n        errors: [],\r\n        warnings: [],\r\n        eventsCreated: {},\r\n        metrics: {\r\n          eventsProcessed: 1,\r\n          eventsSuccessful: 1,\r\n          eventsFailed: 0,\r\n          eventsSkipped: 0,\r\n          avgProcessingTimeMs: processingTime,\r\n          peakProcessingTimeMs: processingTime,\r\n          stageMetrics: {},\r\n        },\r\n      });\r\n\r\n      threatDetector.analyzeEvent.mockResolvedValue({\r\n        ...createMockThreatAnalysis(1),\r\n        analysisDurationMs: 500,\r\n      });\r\n\r\n      vulnerabilityScanner.scanEvent.mockResolvedValue({\r\n        ...createMockVulnerabilityScan(1),\r\n        scanDuration: 1000,\r\n      });\r\n\r\n      responseExecutor.executeResponse.mockResolvedValue({\r\n        ...createMockResponseResult(1),\r\n        executionTime: 3000,\r\n      });\r\n\r\n      // Act\r\n      const result = await service.orchestrateSecurityWorkflow(events);\r\n\r\n      // Assert\r\n      expect(result.success).toBe(true);\r\n      expect(result.data!.metrics).toBeDefined();\r\n      expect(result.data!.metrics.totalProcessingTime).toBeGreaterThan(0);\r\n      expect(result.data!.metrics.averageEventProcessingTime).toBeGreaterThan(0);\r\n      expect(result.data!.metrics.threatDetectionTime).toBe(2500); // 5 events * 500ms each\r\n      expect(result.data!.metrics.vulnerabilityScanningTime).toBe(5000); // 5 events * 1000ms each\r\n      expect(result.data!.metrics.responseExecutionTime).toBe(15000); // 5 events * 3000ms each\r\n      expect(result.data!.metrics.throughput).toBeGreaterThan(0);\r\n      expect(result.data!.metrics.successRate).toBe(100);\r\n      expect(result.data!.metrics.errorRate).toBe(0);\r\n    });\r\n  });\r\n});"], "version": 3}