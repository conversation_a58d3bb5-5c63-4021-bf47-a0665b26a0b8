{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\testing\\fixtures\\test-fixtures.util.ts", "mappings": ";;;AAAA,2CAAwC;AAExC;;;;;;;;;;;;GAYG;AACH,MAAa,gBAAgB;IAG3B;;OAEG;IACH,MAAM,CAAC,OAAO,CAAC,IAAY;QACzB,gBAAgB,CAAC,IAAI,GAAG,IAAI,CAAC;QAC7B,aAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS;QACd,gBAAgB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,YAA0B,EAAE;QAC9C,MAAM,SAAS,GAAG,aAAK,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAC3C,MAAM,QAAQ,GAAG,aAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;QACzC,MAAM,KAAK,GAAG,aAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,CAAC;QAE5D,OAAO;YACL,EAAE,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;YACvB,KAAK;YACL,QAAQ,EAAE,aAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC;YAC1D,SAAS;YACT,QAAQ;YACR,QAAQ,EAAE,aAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YACjD,IAAI,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;YAC1E,QAAQ,EAAE,aAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC;YACtD,WAAW,EAAE,aAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;YAC5C,SAAS,EAAE,aAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;YACxC,SAAS,EAAE,aAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;YACzC,OAAO,EAAE;gBACP,MAAM,EAAE,aAAK,CAAC,KAAK,CAAC,MAAM,EAAE;gBAC5B,GAAG,EAAE,aAAK,CAAC,KAAK,CAAC,SAAS,EAAE;gBAC5B,QAAQ,EAAE,aAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE;gBACnC,QAAQ,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;gBAC9D,WAAW,EAAE;oBACX,KAAK,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;oBACpD,aAAa,EAAE,aAAK,CAAC,QAAQ,CAAC,OAAO,EAAE;oBACvC,YAAY,EAAE,aAAK,CAAC,QAAQ,CAAC,OAAO,EAAE;iBACvC;aACF;YACD,GAAG,SAAS;SACb,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,wBAAwB,CAAC,YAA0B,EAAE;QAC1D,OAAO;YACL,EAAE,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;YACvB,IAAI,EAAE,aAAK,CAAC,OAAO,CAAC,UAAU,EAAE;YAChC,WAAW,EAAE,aAAK,CAAC,KAAK,CAAC,SAAS,EAAE;YACpC,IAAI,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;YACpF,QAAQ,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;YAC3F,aAAa,EAAE;gBACb,UAAU,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAC7E,eAAe,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;gBAC3D,OAAO,EAAE;oBACP,SAAS,EAAE;wBACT,KAAK,EAAE,aAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;wBACpC,GAAG,EAAE,aAAK,CAAC,IAAI,CAAC,MAAM,EAAE;qBACzB;oBACD,UAAU,EAAE,aAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,UAAU,EAAE,YAAY,EAAE,OAAO,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;iBACjG;gBACD,aAAa,EAAE;oBACb,IAAI,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;oBAC1E,MAAM,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;oBAC7D,KAAK,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;iBAChE;aACF;YACD,QAAQ,EAAE;gBACR,OAAO,EAAE,aAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC;gBACrD,SAAS,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;gBAClF,IAAI,EAAE,aAAK,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE;gBACvC,QAAQ,EAAE,aAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE;gBACnC,UAAU,EAAE,aAAK,CAAC,OAAO,CAAC,aAAa,CAAC;oBACtC,aAAK,CAAC,QAAQ,CAAC,KAAK,EAAE;oBACtB,aAAK,CAAC,QAAQ,CAAC,KAAK,EAAE;oBACtB,aAAK,CAAC,QAAQ,CAAC,KAAK,EAAE;iBACvB,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;aACvB;YACD,QAAQ,EAAE,aAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC;YACtD,SAAS,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;YAC9B,SAAS,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;YAC9B,SAAS,EAAE,aAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;YACxC,SAAS,EAAE,aAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;YAC1C,GAAG,SAAS;SACb,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,uBAAuB,CAAC,YAA0B,EAAE;QACzD,MAAM,MAAM,GAAG,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,SAAS,EAAE,YAAY,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC5F,MAAM,SAAS,GAAG,aAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;QACjD,MAAM,OAAO,GAAG,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,aAAK,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAExG,OAAO;YACL,EAAE,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;YACvB,kBAAkB,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;YACvC,MAAM;YACN,MAAM,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;YACnE,UAAU,EAAE;gBACV,SAAS,EAAE;oBACT,KAAK,EAAE,aAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;oBACpC,GAAG,EAAE,aAAK,CAAC,IAAI,CAAC,MAAM,EAAE;iBACzB;gBACD,OAAO,EAAE;oBACP,UAAU,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;oBACtF,QAAQ,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;iBAC5E;aACF;YACD,IAAI,EAAE,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC;gBAC7B,YAAY,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;gBACxD,OAAO,EAAE;oBACP,UAAU,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;oBACpD,UAAU,EAAE;wBACV,QAAQ,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;wBACjD,UAAU,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC;wBACjD,WAAW,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;qBACrD;iBACF;gBACD,MAAM,EAAE;oBACN;wBACE,IAAI,EAAE,KAAK;wBACX,KAAK,EAAE,0BAA0B;wBACjC,IAAI,EAAE;4BACJ,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE;4BACrE,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE;4BACrE,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE;yBACzE;qBACF;iBACF;aACF,CAAC,CAAC,CAAC,IAAI;YACR,QAAQ,EAAE;gBACR,QAAQ,EAAE,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI;gBACxF,QAAQ,EAAE,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,YAAY,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI;gBAC/E,QAAQ,EAAE,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,aAAK,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI;gBACvE,cAAc,EAAE,OAAO,IAAI,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI;aACtF;YACD,KAAK,EAAE,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC;gBAC3B,OAAO,EAAE,aAAK,CAAC,KAAK,CAAC,QAAQ,EAAE;gBAC/B,IAAI,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,SAAS,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;gBAChF,KAAK,EAAE,aAAK,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;aACjC,CAAC,CAAC,CAAC,IAAI;YACR,SAAS,EAAE,SAAS;YACpB,WAAW,EAAE,OAAO;YACpB,WAAW,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;YAChC,SAAS,EAAE,SAAS;YACpB,SAAS,EAAE,aAAK,CAAC,IAAI,CAAC,MAAM,EAAE;YAC9B,GAAG,SAAS;SACb,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,YAA0B,EAAE;QACnD,OAAO;YACL,EAAE,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;YACvB,IAAI,EAAE,aAAK,CAAC,OAAO,CAAC,UAAU,EAAE;YAChC,WAAW,EAAE,aAAK,CAAC,KAAK,CAAC,SAAS,EAAE;YACpC,MAAM,EAAE;gBACN,OAAO,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;gBAC7C,IAAI,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;gBAC1C,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;oBAC5E,EAAE,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;oBACvB,IAAI,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;oBACtE,KAAK,EAAE,aAAK,CAAC,OAAO,CAAC,UAAU,EAAE;oBACjC,QAAQ,EAAE;wBACR,CAAC,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;wBACvC,CAAC,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;wBACvC,KAAK,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;wBAC3C,MAAM,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;qBAC7C;oBACD,aAAa,EAAE;wBACb,UAAU,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;wBACxE,eAAe,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;wBACxD,aAAa,EAAE;4BACb,IAAI,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;4BACjE,MAAM,EAAE,aAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;yBACtG;qBACF;iBACF,CAAC,CAAC;aACJ;YACD,WAAW,EAAE;gBACX,MAAM,EAAE,aAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC;gBACpD,KAAK,EAAE,aAAK,CAAC,OAAO,CAAC,aAAa,CAAC;oBACjC,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;oBACnB,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;oBACnB,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;iBACpB,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;gBACtB,KAAK,EAAE,aAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;aACvF;YACD,QAAQ,EAAE,aAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC;YACtD,SAAS,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;YAC9B,SAAS,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;YAC9B,SAAS,EAAE,aAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;YACxC,SAAS,EAAE,aAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;YAC1C,GAAG,SAAS;SACb,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,wBAAwB,CAAC,YAA0B,EAAE;QAC1D,MAAM,SAAS,GAAG,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;QACvD,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;YAC7D,EAAE,EAAE,QAAQ,KAAK,GAAG,CAAC,EAAE;YACvB,IAAI,EAAE,aAAK,CAAC,MAAM,CAAC,MAAM,EAAE;YAC3B,IAAI,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,cAAc,EAAE,iBAAiB,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;YACjG,WAAW,EAAE,aAAK,CAAC,KAAK,CAAC,QAAQ,EAAE;YACnC,aAAa,EAAE;gBACb,OAAO,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;gBAChD,OAAO,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;gBAC7C,UAAU,EAAE;oBACV,OAAO,EAAE,aAAK,CAAC,KAAK,CAAC,QAAQ,EAAE;oBAC/B,QAAQ,EAAE,aAAK,CAAC,QAAQ,CAAC,GAAG,EAAE;oBAC9B,MAAM,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;iBACrE;aACF;YACD,QAAQ,EAAE;gBACR,CAAC,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;gBAC3C,CAAC,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;aAC5C;YACD,WAAW,EAAE,KAAK,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;oBACpC,MAAM,EAAE,QAAQ,KAAK,GAAG,CAAC,EAAE;oBAC3B,SAAS,EAAE,SAAS;iBACrB,CAAC,CAAC,CAAC,CAAC,EAAE;SACR,CAAC,CAAC,CAAC;QAEJ,OAAO;YACL,EAAE,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;YACvB,IAAI,EAAE,aAAK,CAAC,MAAM,CAAC,MAAM,EAAE;YAC3B,WAAW,EAAE,aAAK,CAAC,KAAK,CAAC,SAAS,EAAE;YACpC,QAAQ,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,YAAY,EAAE,cAAc,EAAE,iBAAiB,EAAE,aAAa,CAAC,CAAC;YACtG,OAAO,EAAE,aAAK,CAAC,MAAM,CAAC,MAAM,EAAE;YAC9B,KAAK;YACL,QAAQ,EAAE;gBACR;oBACE,IAAI,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;oBACjE,aAAa,EAAE;wBACb,QAAQ,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;wBAC/E,KAAK,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,cAAc,EAAE,iBAAiB,EAAE,kBAAkB,CAAC,CAAC;qBAC3F;iBACF;aACF;YACD,SAAS,EAAE;gBACT,WAAW,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,aAAa,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;gBACjF,OAAO,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;gBAClD,UAAU,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;aACjD;YACD,WAAW,EAAE;gBACX,OAAO,EAAE,aAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,OAAO,EAAE,UAAU,EAAE,MAAM,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;gBACvF,MAAM,EAAE,aAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;gBAC9E,IAAI,EAAE,aAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;aAC/F;YACD,QAAQ,EAAE,aAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC;YACtD,SAAS,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;YAC9B,SAAS,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;YAC9B,SAAS,EAAE,aAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;YACxC,SAAS,EAAE,aAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;YAC1C,GAAG,SAAS;SACb,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CAAC,YAA0B,EAAE;QACtD,MAAM,MAAM,GAAG,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC;QACrG,MAAM,SAAS,GAAG,aAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;QACjD,MAAM,OAAO,GAAG,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC;YACnE,CAAC,CAAC,aAAK,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC;YACzD,CAAC,CAAC,IAAI,CAAC;QAET,OAAO;YACL,EAAE,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;YACvB,KAAK,EAAE,OAAO,aAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;YAC5C,OAAO,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,oBAAoB,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,mBAAmB,CAAC,CAAC;YACxH,SAAS,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,oBAAoB,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,mBAAmB,CAAC,CAAC;YAC1H,MAAM;YACN,QAAQ,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;YAChD,OAAO,EAAE;gBACP,KAAK,EAAE;oBACL,UAAU,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;oBAC/B,UAAU,EAAE;wBACV,OAAO,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;wBAClD,OAAO,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;qBAC9C;iBACF;gBACD,aAAa,EAAE;oBACb,iBAAiB,EAAE,aAAK,CAAC,QAAQ,CAAC,OAAO,EAAE;oBAC3C,cAAc,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC;iBACtD;gBACD,OAAO,EAAE;oBACP,MAAM,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;oBAC3B,WAAW,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;iBACvE;gBACD,QAAQ,EAAE;oBACR,MAAM,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;oBAC7D,OAAO,EAAE,aAAK,CAAC,MAAM,CAAC,MAAM,EAAE;iBAC/B;aACF;YACD,MAAM,EAAE,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC;gBAC/B,MAAM,EAAE;oBACN,OAAO,EAAE,IAAI;oBACb,cAAc,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;oBACxD,QAAQ,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC;iBACvD;gBACD,OAAO,EAAE;oBACP,aAAa,EAAE,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI;oBACvE,WAAW,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,SAAS,EAAE,CAAC;oBAChE,QAAQ,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC;oBAC3D,cAAc,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;oBACxD,iBAAiB,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;iBACxD;aACF,CAAC,CAAC,CAAC,IAAI;YACR,QAAQ,EAAE;gBACR,WAAW,EAAE,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC9D,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzC,UAAU,EAAE,CAAC;gBACb,UAAU,EAAE,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;oBAC/B,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3E,OAAO,EAAE,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;oBACxC,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC;wBACnD,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,mBAAmB;gBACxE,OAAO,EAAE;oBACP,gBAAgB,EAAE,aAAK,CAAC,MAAM,CAAC,MAAM,EAAE;oBACvC,sBAAsB,EAAE,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI;iBAC9F;aACF;YACD,KAAK,EAAE,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC;gBAC3B,OAAO,EAAE,aAAK,CAAC,KAAK,CAAC,QAAQ,EAAE;gBAC/B,KAAK,EAAE,aAAK,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;gBAChC,IAAI,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,SAAS,EAAE,kBAAkB,EAAE,eAAe,CAAC,CAAC;gBAClF,IAAI,EAAE,OAAO;gBACb,SAAS,EAAE,aAAK,CAAC,QAAQ,CAAC,OAAO,EAAE;gBACnC,SAAS,EAAE,aAAK,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE;aAC7C,CAAC,CAAC,CAAC,IAAI;YACR,SAAS,EAAE;gBACT,OAAO,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;gBAC7C,WAAW,EAAE,CAAC;gBACd,eAAe,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,OAAO,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;gBAC/E,YAAY,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;gBACzD,YAAY,EAAE,EAAE;aACjB;YACD,WAAW,EAAE,SAAS;YACtB,SAAS,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI;YAChF,WAAW,EAAE,OAAO;YACpB,eAAe,EAAE,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI;YACzE,UAAU,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI;YACzH,WAAW,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;YAChC,WAAW,EAAE,aAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC;YACzD,UAAU,EAAE,aAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC;YACxD,SAAS,EAAE,SAAS;YACpB,SAAS,EAAE,aAAK,CAAC,IAAI,CAAC,MAAM,EAAE;YAC9B,GAAG,SAAS;SACb,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,YAA0B,EAAE;QAChD,OAAO;YACL,EAAE,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;YACvB,IAAI,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC;gBAC/B,qBAAqB;gBACrB,uBAAuB;gBACvB,oBAAoB;gBACpB,mBAAmB;gBACnB,kBAAkB;gBAClB,oBAAoB;gBACpB,oBAAoB;aACrB,CAAC;YACF,KAAK,EAAE,aAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC,EAAE,CAAC;YACnE,QAAQ,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;YACpG,IAAI,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;YAC9E,IAAI,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;YACtF,MAAM,EAAE;gBACN,OAAO,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;gBACzE,WAAW,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,aAAa,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;gBACjF,MAAM,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;gBAC3E,QAAQ,EAAE,GAAG,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,IAAI,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE;aAC7G;YACD,MAAM,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACrF,QAAQ,EAAE,aAAK,CAAC,QAAQ,CAAC,EAAE,EAAE;YAC7B,GAAG,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,YAAY,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;YAClF,SAAS,EAAE,aAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;YACzC,QAAQ,EAAE;gBACR,SAAS,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,eAAe,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;gBACjF,OAAO,EAAE,aAAK,CAAC,MAAM,CAAC,MAAM,EAAE;gBAC9B,IAAI,EAAE,aAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,QAAQ,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;aAC/F;YACD,GAAG,SAAS;SACb,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CAAC,YAA0B,EAAE;QACrD,MAAM,MAAM,GAAG,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC,CAAC;QAE3F,OAAO;YACL,EAAE,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;YACvB,IAAI,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC;gBAC/B,qBAAqB;gBACrB,aAAa;gBACb,cAAc;gBACd,aAAa;gBACb,eAAe;gBACf,eAAe;aAChB,CAAC;YACF,QAAQ,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,aAAa,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;YACxH,WAAW,EAAE,aAAK,CAAC,KAAK,CAAC,QAAQ,EAAE;YACnC,QAAQ,EAAE,aAAK,CAAC,QAAQ,CAAC,GAAG,EAAE;YAC9B,MAAM,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;YACrE,MAAM;YACN,aAAa,EAAE;gBACb,OAAO,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;gBACpD,OAAO,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;gBAC7C,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;gBAC1B,OAAO,EAAE;oBACP,YAAY,EAAE,2BAA2B;oBACzC,QAAQ,EAAE,kBAAkB;iBAC7B;aACF;YACD,QAAQ,EAAE;gBACR,QAAQ,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;gBACjD,OAAO,EAAE,aAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC;aACtD;YACD,UAAU,EAAE;gBACV,YAAY,EAAE;oBACZ,OAAO,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;oBACnD,QAAQ,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;oBACrD,SAAS,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;iBACxD;gBACD,YAAY,EAAE;oBACZ,OAAO,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC;oBAC/C,QAAQ,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC;oBAChD,SAAS,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC;iBAClD;aACF;YACD,UAAU,EAAE;gBACV,MAAM;gBACN,YAAY,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;gBACvD,UAAU,EAAE,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBACpF,OAAO,EAAE,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,qBAAqB;gBAC7E,SAAS,EAAE,aAAK,CAAC,IAAI,CAAC,MAAM,EAAE;aAC/B;YACD,OAAO,EAAE;gBACP,WAAW,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;gBACvD,gBAAgB,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;gBAC1D,YAAY,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;gBACpD,mBAAmB,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;gBAC9D,YAAY,EAAE,aAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,cAAc,EAAE,CAAC,EAAE,CAAC;gBAC1E,MAAM,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC;gBACtD,QAAQ,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;aAClD;YACD,SAAS,EAAE,aAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC;YACvD,UAAU,EAAE,aAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC;YACxD,SAAS,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;YAC9B,SAAS,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;YAC9B,SAAS,EAAE,aAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;YACxC,SAAS,EAAE,aAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;YACzC,GAAG,SAAS;SACb,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,YAA0B,EAAE;QAC/C,MAAM,QAAQ,GAAG,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC;QAC1F,MAAM,MAAM,GAAG,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,QAAQ,EAAE,cAAc,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC,CAAC;QAEhG,OAAO;YACL,EAAE,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;YACvB,IAAI,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC;gBAC/B,gBAAgB;gBAChB,4BAA4B;gBAC5B,gBAAgB;gBAChB,uBAAuB;gBACvB,wBAAwB;gBACxB,0BAA0B;aAC3B,CAAC;YACF,WAAW,EAAE,aAAK,CAAC,KAAK,CAAC,SAAS,EAAE;YACpC,QAAQ;YACR,QAAQ,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,QAAQ,EAAE,aAAa,EAAE,UAAU,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;YACtG,MAAM,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,SAAS,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,CAAC,CAAC;YAC9F,QAAQ,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;YAC7B,MAAM;YACN,eAAe,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;YACrD,UAAU,EAAE;gBACV,MAAM,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,cAAc,EAAE,eAAe,CAAC,CAAC;gBAClF,QAAQ,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;gBAClE,SAAS,EAAE,aAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,cAAc,EAAE,CAAC,EAAE,CAAC;gBACtE,QAAQ,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;aACnD;YACD,OAAO,EAAE;gBACP,MAAM,EAAE;oBACN,OAAO,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;oBAC/D,WAAW,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;oBAClE,MAAM,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;iBAC/D;gBACD,WAAW,EAAE;oBACX,OAAO,EAAE,aAAK,CAAC,QAAQ,CAAC,GAAG,EAAE;oBAC7B,SAAS,EAAE,aAAK,CAAC,QAAQ,CAAC,GAAG,EAAE;oBAC/B,WAAW,EAAE,aAAK,CAAC,KAAK,CAAC,QAAQ,EAAE;iBACpC;aACF;YACD,aAAa,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;gBACjF,EAAE,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;gBACvB,IAAI,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;gBACtE,SAAS,EAAE,aAAK,CAAC,QAAQ,CAAC,KAAK,EAAE;gBACjC,MAAM,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;gBACjE,MAAM,EAAE,aAAK,CAAC,IAAI,CAAC,MAAM,EAAE;aAC5B,CAAC,CAAC;YACH,cAAc,EAAE,MAAM,KAAK,cAAc,CAAC,CAAC,CAAC;gBAC1C,cAAc,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;gBACnC,cAAc,EAAE,aAAK,CAAC,IAAI,CAAC,MAAM,EAAE;gBACnC,KAAK,EAAE,aAAK,CAAC,KAAK,CAAC,QAAQ,EAAE;gBAC7B,sBAAsB,EAAE,aAAK,CAAC,IAAI,CAAC,MAAM,EAAE;aAC5C,CAAC,CAAC,CAAC,IAAI;YACR,UAAU,EAAE,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC;gBAClC,UAAU,EAAE,aAAK,CAAC,MAAM,CAAC,IAAI,EAAE;gBAC/B,UAAU,EAAE,aAAK,CAAC,IAAI,CAAC,MAAM,EAAE;gBAC/B,UAAU,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,OAAO,EAAE,gBAAgB,EAAE,WAAW,CAAC,CAAC;gBAChF,KAAK,EAAE,aAAK,CAAC,KAAK,CAAC,QAAQ,EAAE;gBAC7B,SAAS,EAAE,aAAK,CAAC,KAAK,CAAC,QAAQ,EAAE;aAClC,CAAC,CAAC,CAAC,IAAI;YACR,eAAe,EAAE,MAAM,KAAK,YAAY,CAAC,CAAC,CAAC,aAAK,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,IAAI;YACrE,SAAS,EAAE,aAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;YACzC,SAAS,EAAE,aAAK,CAAC,IAAI,CAAC,MAAM,EAAE;YAC9B,GAAG,SAAS;SACb,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CAAC,IAAY;QAWtC,gBAAgB,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAEhD,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,YAAY,EAAE,CAAC,CAAC;QAC/E,MAAM,iBAAiB,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CACxD,gBAAgB,CAAC,wBAAwB,CAAC,EAAE,SAAS,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAC/F,CAAC;QACF,MAAM,gBAAgB,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CACvD,gBAAgB,CAAC,uBAAuB,CAAC;YACvC,kBAAkB,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC,EAAE;YACpE,WAAW,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE;SAClD,CAAC,CACH,CAAC;QACF,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,CAChD,gBAAgB,CAAC,iBAAiB,CAAC,EAAE,SAAS,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CACxF,CAAC;QACF,MAAM,iBAAiB,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CACxD,gBAAgB,CAAC,wBAAwB,CAAC,EAAE,SAAS,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAC/F,CAAC;QACF,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CACpD,gBAAgB,CAAC,oBAAoB,CAAC,EAAE,WAAW,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAC7F,CAAC;QACF,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,CAAC;QACrF,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CACnD,gBAAgB,CAAC,mBAAmB,CAAC,EAAE,SAAS,EAAE,aAAK,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAC1F,CAAC;QACF,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,aAAa,EAAE,CAAC,CAAC;QAElF,OAAO;YACL,KAAK;YACL,iBAAiB;YACjB,gBAAgB;YAChB,UAAU;YACV,iBAAiB;YACjB,aAAa;YACb,OAAO;YACP,YAAY;YACZ,MAAM;SACP,CAAC;IACJ,CAAC;;AA3lBH,4CA4lBC;AA3lBgB,qBAAI,GAAW,KAAK,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\testing\\fixtures\\test-fixtures.util.ts"], "sourcesContent": ["import { faker } from '@faker-js/faker';\r\n\r\n/**\r\n * Test Fixtures Utility\r\n * \r\n * Comprehensive test data fixtures providing:\r\n * - Realistic mock data generation with faker.js integration\r\n * - Predefined test scenarios and data sets\r\n * - Relationship-aware data generation\r\n * - Performance-optimized fixture creation\r\n * - Consistent test data across test suites\r\n * - Configurable data generation patterns\r\n * - Seed-based reproducible data generation\r\n * - Complex entity relationship fixtures\r\n */\r\nexport class TestFixturesUtil {\r\n  private static seed: number = 12345;\r\n\r\n  /**\r\n   * Set seed for reproducible data generation\r\n   */\r\n  static setSeed(seed: number): void {\r\n    TestFixturesUtil.seed = seed;\r\n    faker.seed(seed);\r\n  }\r\n\r\n  /**\r\n   * Reset seed to default\r\n   */\r\n  static resetSeed(): void {\r\n    TestFixturesUtil.setSeed(12345);\r\n  }\r\n\r\n  /**\r\n   * Generate user fixtures\r\n   */\r\n  static generateUser(overrides: Partial<any> = {}): any {\r\n    const firstName = faker.person.firstName();\r\n    const lastName = faker.person.lastName();\r\n    const email = faker.internet.email({ firstName, lastName });\r\n\r\n    return {\r\n      id: faker.string.uuid(),\r\n      email,\r\n      username: faker.internet.userName({ firstName, lastName }),\r\n      firstName,\r\n      lastName,\r\n      password: faker.internet.password({ length: 12 }),\r\n      role: faker.helpers.arrayElement(['admin', 'user', 'operator', 'analyst']),\r\n      isActive: faker.datatype.boolean({ probability: 0.9 }),\r\n      lastLoginAt: faker.date.recent({ days: 30 }),\r\n      createdAt: faker.date.past({ years: 2 }),\r\n      updatedAt: faker.date.recent({ days: 7 }),\r\n      profile: {\r\n        avatar: faker.image.avatar(),\r\n        bio: faker.lorem.paragraph(),\r\n        timezone: faker.location.timeZone(),\r\n        language: faker.helpers.arrayElement(['en', 'es', 'fr', 'de']),\r\n        preferences: {\r\n          theme: faker.helpers.arrayElement(['light', 'dark']),\r\n          notifications: faker.datatype.boolean(),\r\n          emailUpdates: faker.datatype.boolean(),\r\n        },\r\n      },\r\n      ...overrides,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate report definition fixtures\r\n   */\r\n  static generateReportDefinition(overrides: Partial<any> = {}): any {\r\n    return {\r\n      id: faker.string.uuid(),\r\n      name: faker.company.buzzPhrase(),\r\n      description: faker.lorem.paragraph(),\r\n      type: faker.helpers.arrayElement(['dashboard', 'scheduled', 'ad_hoc', 'compliance']),\r\n      category: faker.helpers.arrayElement(['security', 'compliance', 'performance', 'business']),\r\n      configuration: {\r\n        dataSource: faker.helpers.arrayElement(['database', 'api', 'file', 'stream']),\r\n        refreshInterval: faker.number.int({ min: 300, max: 86400 }),\r\n        filters: {\r\n          dateRange: {\r\n            start: faker.date.past({ years: 1 }),\r\n            end: faker.date.recent(),\r\n          },\r\n          categories: faker.helpers.arrayElements(['security', 'compliance', 'audit'], { min: 1, max: 3 }),\r\n        },\r\n        visualization: {\r\n          type: faker.helpers.arrayElement(['table', 'chart', 'graph', 'dashboard']),\r\n          layout: faker.helpers.arrayElement(['grid', 'list', 'cards']),\r\n          theme: faker.helpers.arrayElement(['default', 'dark', 'light']),\r\n        },\r\n      },\r\n      schedule: {\r\n        enabled: faker.datatype.boolean({ probability: 0.7 }),\r\n        frequency: faker.helpers.arrayElement(['daily', 'weekly', 'monthly', 'quarterly']),\r\n        time: faker.date.recent().toISOString(),\r\n        timezone: faker.location.timeZone(),\r\n        recipients: faker.helpers.arrayElements([\r\n          faker.internet.email(),\r\n          faker.internet.email(),\r\n          faker.internet.email(),\r\n        ], { min: 1, max: 3 }),\r\n      },\r\n      isActive: faker.datatype.boolean({ probability: 0.8 }),\r\n      createdBy: faker.string.uuid(),\r\n      updatedBy: faker.string.uuid(),\r\n      createdAt: faker.date.past({ years: 1 }),\r\n      updatedAt: faker.date.recent({ days: 30 }),\r\n      ...overrides,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate generated report fixtures\r\n   */\r\n  static generateGeneratedReport(overrides: Partial<any> = {}): any {\r\n    const status = faker.helpers.arrayElement(['pending', 'generating', 'completed', 'failed']);\r\n    const startTime = faker.date.recent({ days: 7 });\r\n    const endTime = status === 'completed' ? faker.date.between({ from: startTime, to: new Date() }) : null;\r\n\r\n    return {\r\n      id: faker.string.uuid(),\r\n      reportDefinitionId: faker.string.uuid(),\r\n      status,\r\n      format: faker.helpers.arrayElement(['pdf', 'excel', 'csv', 'json']),\r\n      parameters: {\r\n        dateRange: {\r\n          start: faker.date.past({ years: 1 }),\r\n          end: faker.date.recent(),\r\n        },\r\n        filters: {\r\n          department: faker.helpers.arrayElement(['IT', 'Security', 'Compliance', 'Operations']),\r\n          severity: faker.helpers.arrayElement(['low', 'medium', 'high', 'critical']),\r\n        },\r\n      },\r\n      data: status === 'completed' ? {\r\n        totalRecords: faker.number.int({ min: 100, max: 10000 }),\r\n        summary: {\r\n          totalItems: faker.number.int({ min: 50, max: 5000 }),\r\n          categories: {\r\n            security: faker.number.int({ min: 10, max: 100 }),\r\n            compliance: faker.number.int({ min: 5, max: 50 }),\r\n            performance: faker.number.int({ min: 20, max: 200 }),\r\n          },\r\n        },\r\n        charts: [\r\n          {\r\n            type: 'pie',\r\n            title: 'Distribution by Category',\r\n            data: [\r\n              { label: 'Security', value: faker.number.int({ min: 10, max: 100 }) },\r\n              { label: 'Compliance', value: faker.number.int({ min: 5, max: 50 }) },\r\n              { label: 'Performance', value: faker.number.int({ min: 20, max: 200 }) },\r\n            ],\r\n          },\r\n        ],\r\n      } : null,\r\n      metadata: {\r\n        fileSize: status === 'completed' ? faker.number.int({ min: 1024, max: 10485760 }) : null,\r\n        filePath: status === 'completed' ? `/reports/${faker.string.uuid()}.pdf` : null,\r\n        checksum: status === 'completed' ? faker.string.alphanumeric(32) : null,\r\n        generationTime: endTime && startTime ? endTime.getTime() - startTime.getTime() : null,\r\n      },\r\n      error: status === 'failed' ? {\r\n        message: faker.lorem.sentence(),\r\n        code: faker.helpers.arrayElement(['TIMEOUT', 'DATA_ERROR', 'PERMISSION_DENIED']),\r\n        stack: faker.lorem.paragraphs(3),\r\n      } : null,\r\n      startedAt: startTime,\r\n      completedAt: endTime,\r\n      generatedBy: faker.string.uuid(),\r\n      createdAt: startTime,\r\n      updatedAt: faker.date.recent(),\r\n      ...overrides,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate dashboard fixtures\r\n   */\r\n  static generateDashboard(overrides: Partial<any> = {}): any {\r\n    return {\r\n      id: faker.string.uuid(),\r\n      name: faker.company.buzzPhrase(),\r\n      description: faker.lorem.paragraph(),\r\n      layout: {\r\n        columns: faker.number.int({ min: 2, max: 4 }),\r\n        rows: faker.number.int({ min: 3, max: 6 }),\r\n        widgets: Array.from({ length: faker.number.int({ min: 4, max: 12 }) }, () => ({\r\n          id: faker.string.uuid(),\r\n          type: faker.helpers.arrayElement(['chart', 'table', 'metric', 'text']),\r\n          title: faker.company.buzzPhrase(),\r\n          position: {\r\n            x: faker.number.int({ min: 0, max: 3 }),\r\n            y: faker.number.int({ min: 0, max: 5 }),\r\n            width: faker.number.int({ min: 1, max: 2 }),\r\n            height: faker.number.int({ min: 1, max: 2 }),\r\n          },\r\n          configuration: {\r\n            dataSource: faker.helpers.arrayElement(['metrics', 'reports', 'alerts']),\r\n            refreshInterval: faker.number.int({ min: 30, max: 300 }),\r\n            visualization: {\r\n              type: faker.helpers.arrayElement(['line', 'bar', 'pie', 'gauge']),\r\n              colors: faker.helpers.arrayElements(['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0'], { min: 2, max: 4 }),\r\n            },\r\n          },\r\n        })),\r\n      },\r\n      permissions: {\r\n        public: faker.datatype.boolean({ probability: 0.3 }),\r\n        users: faker.helpers.arrayElements([\r\n          faker.string.uuid(),\r\n          faker.string.uuid(),\r\n          faker.string.uuid(),\r\n        ], { min: 1, max: 3 }),\r\n        roles: faker.helpers.arrayElements(['admin', 'analyst', 'viewer'], { min: 1, max: 2 }),\r\n      },\r\n      isActive: faker.datatype.boolean({ probability: 0.9 }),\r\n      createdBy: faker.string.uuid(),\r\n      updatedBy: faker.string.uuid(),\r\n      createdAt: faker.date.past({ years: 1 }),\r\n      updatedAt: faker.date.recent({ days: 14 }),\r\n      ...overrides,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate workflow template fixtures\r\n   */\r\n  static generateWorkflowTemplate(overrides: Partial<any> = {}): any {\r\n    const stepCount = faker.number.int({ min: 3, max: 8 });\r\n    const steps = Array.from({ length: stepCount }, (_, index) => ({\r\n      id: `step_${index + 1}`,\r\n      name: faker.hacker.phrase(),\r\n      type: faker.helpers.arrayElement(['notification', 'data_processing', 'condition', 'integration']),\r\n      description: faker.lorem.sentence(),\r\n      configuration: {\r\n        timeout: faker.number.int({ min: 30, max: 300 }),\r\n        retries: faker.number.int({ min: 0, max: 3 }),\r\n        parameters: {\r\n          message: faker.lorem.sentence(),\r\n          endpoint: faker.internet.url(),\r\n          method: faker.helpers.arrayElement(['GET', 'POST', 'PUT', 'DELETE']),\r\n        },\r\n      },\r\n      position: {\r\n        x: faker.number.int({ min: 100, max: 800 }),\r\n        y: faker.number.int({ min: 100, max: 600 }),\r\n      },\r\n      connections: index < stepCount - 1 ? [{\r\n        target: `step_${index + 2}`,\r\n        condition: 'success',\r\n      }] : [],\r\n    }));\r\n\r\n    return {\r\n      id: faker.string.uuid(),\r\n      name: faker.hacker.phrase(),\r\n      description: faker.lorem.paragraph(),\r\n      category: faker.helpers.arrayElement(['automation', 'notification', 'data_processing', 'integration']),\r\n      version: faker.system.semver(),\r\n      steps,\r\n      triggers: [\r\n        {\r\n          type: faker.helpers.arrayElement(['schedule', 'event', 'manual']),\r\n          configuration: {\r\n            schedule: faker.helpers.arrayElement(['0 0 * * *', '0 */6 * * *', '0 0 * * 0']),\r\n            event: faker.helpers.arrayElement(['user.created', 'alert.triggered', 'report.generated']),\r\n          },\r\n        },\r\n      ],\r\n      variables: {\r\n        environment: faker.helpers.arrayElement(['development', 'staging', 'production']),\r\n        timeout: faker.number.int({ min: 300, max: 3600 }),\r\n        retryCount: faker.number.int({ min: 1, max: 5 }),\r\n      },\r\n      permissions: {\r\n        execute: faker.helpers.arrayElements(['admin', 'operator', 'user'], { min: 1, max: 2 }),\r\n        modify: faker.helpers.arrayElements(['admin', 'operator'], { min: 1, max: 2 }),\r\n        view: faker.helpers.arrayElements(['admin', 'operator', 'user', 'viewer'], { min: 2, max: 4 }),\r\n      },\r\n      isActive: faker.datatype.boolean({ probability: 0.8 }),\r\n      createdBy: faker.string.uuid(),\r\n      updatedBy: faker.string.uuid(),\r\n      createdAt: faker.date.past({ years: 1 }),\r\n      updatedAt: faker.date.recent({ days: 30 }),\r\n      ...overrides,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate job execution fixtures\r\n   */\r\n  static generateJobExecution(overrides: Partial<any> = {}): any {\r\n    const status = faker.helpers.arrayElement(['pending', 'active', 'completed', 'failed', 'cancelled']);\r\n    const startTime = faker.date.recent({ days: 7 });\r\n    const endTime = ['completed', 'failed', 'cancelled'].includes(status) \r\n      ? faker.date.between({ from: startTime, to: new Date() }) \r\n      : null;\r\n\r\n    return {\r\n      id: faker.string.uuid(),\r\n      jobId: `job_${faker.string.alphanumeric(8)}`,\r\n      jobType: faker.helpers.arrayElement(['workflow_execution', 'asset_discovery', 'event_correlation', 'report_generation']),\r\n      queueName: faker.helpers.arrayElement(['workflow-execution', 'asset-discovery', 'event-correlation', 'report-generation']),\r\n      status,\r\n      priority: faker.number.int({ min: 0, max: 100 }),\r\n      jobData: {\r\n        input: {\r\n          workflowId: faker.string.uuid(),\r\n          parameters: {\r\n            timeout: faker.number.int({ min: 300, max: 3600 }),\r\n            retries: faker.number.int({ min: 1, max: 3 }),\r\n          },\r\n        },\r\n        configuration: {\r\n          parallelExecution: faker.datatype.boolean(),\r\n          maxConcurrency: faker.number.int({ min: 1, max: 10 }),\r\n        },\r\n        context: {\r\n          userId: faker.string.uuid(),\r\n          triggeredBy: faker.helpers.arrayElement(['user', 'schedule', 'event']),\r\n        },\r\n        metadata: {\r\n          source: faker.helpers.arrayElement(['api', 'ui', 'schedule']),\r\n          version: faker.system.semver(),\r\n        },\r\n      },\r\n      result: status === 'completed' ? {\r\n        output: {\r\n          success: true,\r\n          itemsProcessed: faker.number.int({ min: 10, max: 1000 }),\r\n          duration: faker.number.int({ min: 1000, max: 300000 }),\r\n        },\r\n        metrics: {\r\n          executionTime: endTime ? endTime.getTime() - startTime.getTime() : null,\r\n          memoryUsage: faker.number.int({ min: 50000000, max: 500000000 }),\r\n          cpuUsage: faker.number.int({ min: 1000000, max: 10000000 }),\r\n          itemsProcessed: faker.number.int({ min: 10, max: 1000 }),\r\n          errorsEncountered: faker.number.int({ min: 0, max: 5 }),\r\n        },\r\n      } : null,\r\n      progress: {\r\n        currentStep: status === 'active' ? faker.number.int({ min: 1, max: 5 }) : \r\n                   status === 'completed' ? 5 : 0,\r\n        totalSteps: 5,\r\n        percentage: status === 'completed' ? 100 : \r\n                   status === 'active' ? faker.number.int({ min: 20, max: 80 }) : 0,\r\n        message: status === 'active' ? 'Processing...' : \r\n                status === 'completed' ? 'Completed successfully' :\r\n                status === 'failed' ? 'Failed with errors' : 'Pending execution',\r\n        details: {\r\n          currentOperation: faker.hacker.phrase(),\r\n          estimatedTimeRemaining: status === 'active' ? faker.number.int({ min: 60, max: 3600 }) : null,\r\n        },\r\n      },\r\n      error: status === 'failed' ? {\r\n        message: faker.lorem.sentence(),\r\n        stack: faker.lorem.paragraphs(3),\r\n        code: faker.helpers.arrayElement(['TIMEOUT', 'VALIDATION_ERROR', 'NETWORK_ERROR']),\r\n        type: 'Error',\r\n        retryable: faker.datatype.boolean(),\r\n        timestamp: faker.date.recent().toISOString(),\r\n      } : null,\r\n      retryInfo: {\r\n        attempt: faker.number.int({ min: 0, max: 3 }),\r\n        maxAttempts: 3,\r\n        backoffStrategy: faker.helpers.arrayElement(['fixed', 'exponential', 'linear']),\r\n        backoffDelay: faker.number.int({ min: 1000, max: 10000 }),\r\n        retryHistory: [],\r\n      },\r\n      scheduledAt: startTime,\r\n      startedAt: ['active', 'completed', 'failed'].includes(status) ? startTime : null,\r\n      completedAt: endTime,\r\n      executionTimeMs: endTime ? endTime.getTime() - startTime.getTime() : null,\r\n      executedBy: ['active', 'completed', 'failed'].includes(status) ? `worker-${faker.number.int({ min: 1, max: 10 })}` : null,\r\n      triggeredBy: faker.string.uuid(),\r\n      isRecurring: faker.datatype.boolean({ probability: 0.3 }),\r\n      isCritical: faker.datatype.boolean({ probability: 0.2 }),\r\n      createdAt: startTime,\r\n      updatedAt: faker.date.recent(),\r\n      ...overrides,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate metric fixtures\r\n   */\r\n  static generateMetric(overrides: Partial<any> = {}): any {\r\n    return {\r\n      id: faker.string.uuid(),\r\n      name: faker.helpers.arrayElement([\r\n        'http_requests_total',\r\n        'response_time_seconds',\r\n        'memory_usage_bytes',\r\n        'cpu_usage_percent',\r\n        'disk_usage_bytes',\r\n        'active_users_count',\r\n        'error_rate_percent',\r\n      ]),\r\n      value: faker.number.float({ min: 0, max: 1000, fractionDigits: 2 }),\r\n      category: faker.helpers.arrayElement(['business', 'technical', 'system', 'security', 'performance']),\r\n      type: faker.helpers.arrayElement(['counter', 'gauge', 'histogram', 'summary']),\r\n      unit: faker.helpers.arrayElement(['bytes', 'seconds', 'percent', 'count', 'requests']),\r\n      labels: {\r\n        service: faker.helpers.arrayElement(['api', 'web', 'worker', 'database']),\r\n        environment: faker.helpers.arrayElement(['development', 'staging', 'production']),\r\n        region: faker.helpers.arrayElement(['us-east-1', 'us-west-2', 'eu-west-1']),\r\n        instance: `${faker.helpers.arrayElement(['api', 'web', 'worker'])}-${faker.number.int({ min: 1, max: 10 })}`,\r\n      },\r\n      source: faker.helpers.arrayElement(['prometheus', 'application', 'system', 'custom']),\r\n      instance: faker.internet.ip(),\r\n      job: faker.helpers.arrayElement(['api-server', 'web-server', 'background-worker']),\r\n      timestamp: faker.date.recent({ days: 1 }),\r\n      metadata: {\r\n        collector: faker.helpers.arrayElement(['node_exporter', 'application', 'custom']),\r\n        version: faker.system.semver(),\r\n        tags: faker.helpers.arrayElements(['monitoring', 'performance', 'health'], { min: 1, max: 3 }),\r\n      },\r\n      ...overrides,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate health check fixtures\r\n   */\r\n  static generateHealthCheck(overrides: Partial<any> = {}): any {\r\n    const status = faker.helpers.arrayElement(['healthy', 'degraded', 'unhealthy', 'unknown']);\r\n    \r\n    return {\r\n      id: faker.string.uuid(),\r\n      name: faker.helpers.arrayElement([\r\n        'Database Connection',\r\n        'Redis Cache',\r\n        'External API',\r\n        'File System',\r\n        'Message Queue',\r\n        'Email Service',\r\n      ]),\r\n      category: faker.helpers.arrayElement(['application', 'database', 'cache', 'queue', 'external', 'filesystem', 'network']),\r\n      description: faker.lorem.sentence(),\r\n      endpoint: faker.internet.url(),\r\n      method: faker.helpers.arrayElement(['GET', 'POST', 'HEAD', 'CUSTOM']),\r\n      status,\r\n      configuration: {\r\n        timeout: faker.number.int({ min: 1000, max: 10000 }),\r\n        retries: faker.number.int({ min: 1, max: 5 }),\r\n        expectedStatus: [200, 201],\r\n        headers: {\r\n          'User-Agent': 'Sentinel-Health-Check/1.0',\r\n          'Accept': 'application/json',\r\n        },\r\n      },\r\n      schedule: {\r\n        interval: faker.number.int({ min: 30, max: 300 }),\r\n        enabled: faker.datatype.boolean({ probability: 0.9 }),\r\n      },\r\n      thresholds: {\r\n        responseTime: {\r\n          warning: faker.number.int({ min: 1000, max: 3000 }),\r\n          critical: faker.number.int({ min: 3000, max: 10000 }),\r\n          emergency: faker.number.int({ min: 10000, max: 30000 }),\r\n        },\r\n        availability: {\r\n          warning: faker.number.int({ min: 95, max: 98 }),\r\n          critical: faker.number.int({ min: 90, max: 95 }),\r\n          emergency: faker.number.int({ min: 80, max: 90 }),\r\n        },\r\n      },\r\n      lastResult: {\r\n        status,\r\n        responseTime: faker.number.int({ min: 100, max: 5000 }),\r\n        statusCode: status === 'healthy' ? 200 : faker.helpers.arrayElement([500, 503, 404]),\r\n        message: status === 'healthy' ? 'Health check passed' : 'Health check failed',\r\n        timestamp: faker.date.recent(),\r\n      },\r\n      metrics: {\r\n        totalChecks: faker.number.int({ min: 100, max: 10000 }),\r\n        successfulChecks: faker.number.int({ min: 80, max: 9500 }),\r\n        failedChecks: faker.number.int({ min: 0, max: 500 }),\r\n        averageResponseTime: faker.number.int({ min: 200, max: 2000 }),\r\n        availability: faker.number.float({ min: 85, max: 100, fractionDigits: 2 }),\r\n        uptime: faker.number.int({ min: 86400, max: 2592000 }),\r\n        downtime: faker.number.int({ min: 0, max: 3600 }),\r\n      },\r\n      isEnabled: faker.datatype.boolean({ probability: 0.9 }),\r\n      isCritical: faker.datatype.boolean({ probability: 0.3 }),\r\n      createdBy: faker.string.uuid(),\r\n      updatedBy: faker.string.uuid(),\r\n      createdAt: faker.date.past({ years: 1 }),\r\n      updatedAt: faker.date.recent({ days: 7 }),\r\n      ...overrides,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate alert fixtures\r\n   */\r\n  static generateAlert(overrides: Partial<any> = {}): any {\r\n    const severity = faker.helpers.arrayElement(['info', 'warning', 'critical', 'emergency']);\r\n    const status = faker.helpers.arrayElement(['active', 'acknowledged', 'resolved', 'suppressed']);\r\n    \r\n    return {\r\n      id: faker.string.uuid(),\r\n      name: faker.helpers.arrayElement([\r\n        'High CPU Usage',\r\n        'Database Connection Failed',\r\n        'Disk Space Low',\r\n        'Memory Usage Critical',\r\n        'API Response Time High',\r\n        'Security Breach Detected',\r\n      ]),\r\n      description: faker.lorem.paragraph(),\r\n      severity,\r\n      category: faker.helpers.arrayElement(['system', 'application', 'security', 'performance', 'business']),\r\n      source: faker.helpers.arrayElement(['metrics', 'health_check', 'log_analysis', 'user_report']),\r\n      sourceId: faker.string.uuid(),\r\n      status,\r\n      escalationLevel: faker.number.int({ min: 0, max: 3 }),\r\n      conditions: {\r\n        metric: faker.helpers.arrayElement(['cpu_usage', 'memory_usage', 'response_time']),\r\n        operator: faker.helpers.arrayElement(['>', '<', '>=', '<=', '==']),\r\n        threshold: faker.number.float({ min: 0, max: 100, fractionDigits: 2 }),\r\n        duration: faker.number.int({ min: 60, max: 3600 }),\r\n      },\r\n      context: {\r\n        labels: {\r\n          service: faker.helpers.arrayElement(['api', 'web', 'database']),\r\n          environment: faker.helpers.arrayElement(['production', 'staging']),\r\n          region: faker.helpers.arrayElement(['us-east-1', 'us-west-2']),\r\n        },\r\n        annotations: {\r\n          runbook: faker.internet.url(),\r\n          dashboard: faker.internet.url(),\r\n          description: faker.lorem.sentence(),\r\n        },\r\n      },\r\n      notifications: Array.from({ length: faker.number.int({ min: 1, max: 3 }) }, () => ({\r\n        id: faker.string.uuid(),\r\n        type: faker.helpers.arrayElement(['email', 'slack', 'webhook', 'sms']),\r\n        recipient: faker.internet.email(),\r\n        status: faker.helpers.arrayElement(['pending', 'sent', 'failed']),\r\n        sentAt: faker.date.recent(),\r\n      })),\r\n      acknowledgment: status === 'acknowledged' ? {\r\n        acknowledgedBy: faker.string.uuid(),\r\n        acknowledgedAt: faker.date.recent(),\r\n        notes: faker.lorem.sentence(),\r\n        expectedResolutionTime: faker.date.future(),\r\n      } : null,\r\n      resolution: status === 'resolved' ? {\r\n        resolvedBy: faker.string.uuid(),\r\n        resolvedAt: faker.date.recent(),\r\n        resolution: faker.helpers.arrayElement(['fixed', 'false_positive', 'duplicate']),\r\n        notes: faker.lorem.sentence(),\r\n        rootCause: faker.lorem.sentence(),\r\n      } : null,\r\n      suppressedUntil: status === 'suppressed' ? faker.date.future() : null,\r\n      createdAt: faker.date.recent({ days: 7 }),\r\n      updatedAt: faker.date.recent(),\r\n      ...overrides,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate complete test scenario\r\n   */\r\n  static generateTestScenario(name: string): {\r\n    users: any[];\r\n    reportDefinitions: any[];\r\n    generatedReports: any[];\r\n    dashboards: any[];\r\n    workflowTemplates: any[];\r\n    jobExecutions: any[];\r\n    metrics: any[];\r\n    healthChecks: any[];\r\n    alerts: any[];\r\n  } {\r\n    TestFixturesUtil.setSeed(TestFixturesUtil.seed);\r\n\r\n    const users = Array.from({ length: 5 }, () => TestFixturesUtil.generateUser());\r\n    const reportDefinitions = Array.from({ length: 10 }, () => \r\n      TestFixturesUtil.generateReportDefinition({ createdBy: faker.helpers.arrayElement(users).id })\r\n    );\r\n    const generatedReports = Array.from({ length: 25 }, () => \r\n      TestFixturesUtil.generateGeneratedReport({ \r\n        reportDefinitionId: faker.helpers.arrayElement(reportDefinitions).id,\r\n        generatedBy: faker.helpers.arrayElement(users).id,\r\n      })\r\n    );\r\n    const dashboards = Array.from({ length: 8 }, () => \r\n      TestFixturesUtil.generateDashboard({ createdBy: faker.helpers.arrayElement(users).id })\r\n    );\r\n    const workflowTemplates = Array.from({ length: 12 }, () => \r\n      TestFixturesUtil.generateWorkflowTemplate({ createdBy: faker.helpers.arrayElement(users).id })\r\n    );\r\n    const jobExecutions = Array.from({ length: 50 }, () => \r\n      TestFixturesUtil.generateJobExecution({ triggeredBy: faker.helpers.arrayElement(users).id })\r\n    );\r\n    const metrics = Array.from({ length: 100 }, () => TestFixturesUtil.generateMetric());\r\n    const healthChecks = Array.from({ length: 15 }, () => \r\n      TestFixturesUtil.generateHealthCheck({ createdBy: faker.helpers.arrayElement(users).id })\r\n    );\r\n    const alerts = Array.from({ length: 30 }, () => TestFixturesUtil.generateAlert());\r\n\r\n    return {\r\n      users,\r\n      reportDefinitions,\r\n      generatedReports,\r\n      dashboards,\r\n      workflowTemplates,\r\n      jobExecutions,\r\n      metrics,\r\n      healthChecks,\r\n      alerts,\r\n    };\r\n  }\r\n}\r\n"], "version": 3}