27d833e3940320efc413c2d0dac7c119
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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