{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\event-source-type.enum.ts", "mappings": ";;;AAAA;;;;;GAKG;AACH,IAAY,eAoOX;AApOD,WAAY,eAAe;IACzB;;OAEG;IAEH,gDAAgD;IAChD,wCAAqB,CAAA;IAErB,6CAA6C;IAC7C,sCAAmB,CAAA;IAEnB,mCAAmC;IACnC,oDAAiC,CAAA;IAEjC,iCAAiC;IACjC,kDAA+B,CAAA;IAE/B,qCAAqC;IACrC,8CAA2B,CAAA;IAE3B,qCAAqC;IACrC,8BAAW,CAAA;IAEX,wCAAwC;IACxC,4CAAyB,CAAA;IAEzB,mBAAmB;IACnB,8CAA2B,CAAA;IAE3B;;OAEG;IAEH,8CAA8C;IAC9C,8BAAW,CAAA;IAEX,2CAA2C;IAC3C,0CAAuB,CAAA;IAEvB,6CAA6C;IAC7C,gCAAa,CAAA;IAEb,oDAAoD;IACpD,wDAAqC,CAAA;IAErC,uCAAuC;IACvC,8BAAW,CAAA;IAEX,uCAAuC;IACvC,8CAA2B,CAAA;IAE3B;;OAEG;IAEH,gCAAgC;IAChC,8BAAW,CAAA;IAEX,yCAAyC;IACzC,8BAAW,CAAA;IAEX,mCAAmC;IACnC,wCAAqB,CAAA;IAErB,uCAAuC;IACvC,4CAAyB,CAAA;IAEzB,0BAA0B;IAC1B,4DAAyC,CAAA;IAEzC,0BAA0B;IAC1B,4DAAyC,CAAA;IAEzC,4CAA4C;IAC5C,8CAA2B,CAAA;IAE3B;;OAEG;IAEH,0BAA0B;IAC1B,8BAAW,CAAA;IAEX,sBAAsB;IACtB,kCAAe,CAAA;IAEf,4BAA4B;IAC5B,8BAAW,CAAA;IAEX,oCAAoC;IACpC,gCAAa,CAAA;IAEb,0CAA0C;IAC1C,gCAAa,CAAA;IAEb,mCAAmC;IACnC,4DAAyC,CAAA;IAEzC,0BAA0B;IAC1B,4CAAyB,CAAA;IAEzB;;OAEG;IAEH,gCAAgC;IAChC,0DAAuC,CAAA;IAEvC,6BAA6B;IAC7B,8BAAW,CAAA;IAEX,0CAA0C;IAC1C,8BAAW,CAAA;IAEX,mCAAmC;IACnC,8BAAW,CAAA;IAEX,6CAA6C;IAC7C,8BAAW,CAAA;IAEX;;OAEG;IAEH,gDAAgD;IAChD,gCAAa,CAAA;IAEb,sDAAsD;IACtD,gCAAa,CAAA;IAEb,6BAA6B;IAC7B,kEAA+C,CAAA;IAE/C,oCAAoC;IACpC,8DAA2C,CAAA;IAE3C,2BAA2B;IAC3B,0CAAuB,CAAA;IAEvB,+BAA+B;IAC/B,sCAAmB,CAAA;IAEnB,8BAA8B;IAC9B,0CAAuB,CAAA;IAEvB;;OAEG;IAEH,8BAA8B;IAC9B,oDAAiC,CAAA;IAEjC,mCAAmC;IACnC,8BAAW,CAAA;IAEX,sCAAsC;IACtC,8CAA2B,CAAA;IAE3B;;OAEG;IAEH,sCAAsC;IACtC,sDAAmC,CAAA;IAEnC,iCAAiC;IACjC,gCAAa,CAAA;IAEb,+BAA+B;IAC/B,kDAA+B,CAAA;IAE/B;;OAEG;IAEH,iCAAiC;IACjC,4CAAyB,CAAA;IAEzB,qCAAqC;IACrC,0CAAuB,CAAA;IAEvB,iCAAiC;IACjC,8BAAW,CAAA;IAEX,oBAAoB;IACpB,kCAAe,CAAA;IAEf;;OAEG;IAEH,4BAA4B;IAC5B,kDAA+B,CAAA;IAE/B,oCAAoC;IACpC,8CAA2B,CAAA;IAE3B,4CAA4C;IAC5C,4CAAyB,CAAA;IAEzB,8BAA8B;IAC9B,wDAAqC,CAAA;IAErC;;OAEG;IAEH,wCAAwC;IACxC,oCAAiB,CAAA;IAEjB,mCAAmC;IACnC,8DAA2C,CAAA;IAE3C,mCAAmC;IACnC,4CAAyB,CAAA;IAEzB,8BAA8B;IAC9B,sDAAmC,CAAA;IAEnC;;OAEG;IAEH,qCAAqC;IACrC,sCAAmB,CAAA;IAEnB,0CAA0C;IAC1C,kCAAe,CAAA;AACjB,CAAC,EApOW,eAAe,+BAAf,eAAe,QAoO1B;AAED;;GAEG;AACH,MAAa,oBAAoB;IAC/B;;OAEG;IACH,MAAM,CAAC,iBAAiB;QACtB,OAAO,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB;QAC1B,OAAO;YACL,eAAe,CAAC,QAAQ;YACxB,eAAe,CAAC,OAAO;YACvB,eAAe,CAAC,cAAc;YAC9B,eAAe,CAAC,aAAa;YAC7B,eAAe,CAAC,WAAW;YAC3B,eAAe,CAAC,GAAG;YACnB,eAAe,CAAC,UAAU;YAC1B,eAAe,CAAC,WAAW;SAC5B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB;QAC3B,OAAO;YACL,eAAe,CAAC,GAAG;YACnB,eAAe,CAAC,SAAS;YACzB,eAAe,CAAC,IAAI;YACpB,eAAe,CAAC,gBAAgB;YAChC,eAAe,CAAC,GAAG;YACnB,eAAe,CAAC,WAAW;SAC5B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,yBAAyB;QAC9B,OAAO;YACL,eAAe,CAAC,GAAG;YACnB,eAAe,CAAC,GAAG;YACnB,eAAe,CAAC,QAAQ;YACxB,eAAe,CAAC,UAAU;YAC1B,eAAe,CAAC,kBAAkB;YAClC,eAAe,CAAC,kBAAkB;YAClC,eAAe,CAAC,WAAW;SAC5B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB;QACxB,OAAO;YACL,eAAe,CAAC,GAAG;YACnB,eAAe,CAAC,KAAK;YACrB,eAAe,CAAC,GAAG;YACnB,eAAe,CAAC,IAAI;YACpB,eAAe,CAAC,IAAI;YACpB,eAAe,CAAC,kBAAkB;YAClC,eAAe,CAAC,UAAU;SAC3B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB;QAC3B,OAAO;YACL,eAAe,CAAC,iBAAiB;YACjC,eAAe,CAAC,GAAG;YACnB,eAAe,CAAC,GAAG;YACnB,eAAe,CAAC,GAAG;YACnB,eAAe,CAAC,GAAG;SACpB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,0BAA0B;QAC/B,OAAO;YACL,eAAe,CAAC,IAAI;YACpB,eAAe,CAAC,IAAI;YACpB,eAAe,CAAC,qBAAqB;YACrC,eAAe,CAAC,mBAAmB;YACnC,eAAe,CAAC,SAAS;YACzB,eAAe,CAAC,OAAO;YACvB,eAAe,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB;QAC3B,OAAO;YACL,eAAe,CAAC,aAAa;YAC7B,eAAe,CAAC,WAAW;YAC3B,eAAe,CAAC,UAAU;YAC1B,eAAe,CAAC,gBAAgB;SACjC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe,CAAC,UAA2B;QAChD,OAAO,oBAAoB,CAAC,qBAAqB,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IAC3E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,UAA2B;QACjD,OAAO,oBAAoB,CAAC,sBAAsB,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,UAA2B;QAC9C,OAAO,oBAAoB,CAAC,mBAAmB,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IACzE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,UAA2B;QACjD,OAAO,oBAAoB,CAAC,sBAAsB,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,UAA2B;QAC/C,OAAO,oBAAoB,CAAC,0BAA0B,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,UAA2B;QAC5C,IAAI,oBAAoB,CAAC,eAAe,CAAC,UAAU,CAAC;YAAE,OAAO,SAAS,CAAC;QACvE,IAAI,oBAAoB,CAAC,gBAAgB,CAAC,UAAU,CAAC;YAAE,OAAO,UAAU,CAAC;QACzE,IAAI,oBAAoB,CAAC,yBAAyB,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;YAAE,OAAO,aAAa,CAAC;QAChG,IAAI,oBAAoB,CAAC,aAAa,CAAC,UAAU,CAAC;YAAE,OAAO,OAAO,CAAC;QACnE,IAAI,oBAAoB,CAAC,sBAAsB,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;YAAE,OAAO,UAAU,CAAC;QAC1F,IAAI,oBAAoB,CAAC,cAAc,CAAC,UAAU,CAAC;YAAE,OAAO,eAAe,CAAC;QAC5E,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE,eAAe,CAAC,GAAG,EAAE,eAAe,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC;YAAE,OAAO,eAAe,CAAC;QACpI,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,IAAI,EAAE,eAAe,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC;YAAE,OAAO,UAAU,CAAC;QACnI,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,eAAe,CAAC,SAAS,EAAE,eAAe,CAAC,GAAG,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC;YAAE,OAAO,QAAQ,CAAC;QAC9I,IAAI,oBAAoB,CAAC,gBAAgB,CAAC,UAAU,CAAC;YAAE,OAAO,UAAU,CAAC;QACzE,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,eAAe,CAAC,mBAAmB,EAAE,eAAe,CAAC,UAAU,EAAE,eAAe,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC;YAAE,OAAO,UAAU,CAAC;QACvK,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,UAA2B;QAC/C,MAAM,YAAY,GAAoC;YACpD,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,yCAAyC;YACrE,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,uCAAuC;YAClE,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE,kDAAkD;YACpF,CAAC,eAAe,CAAC,aAAa,CAAC,EAAE,+BAA+B;YAChE,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE,6BAA6B;YAC5D,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,+BAA+B;YACtD,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE,gCAAgC;YAC9D,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE,aAAa;YAC5C,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,wCAAwC;YAC/D,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,oCAAoC;YACjE,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,uCAAuC;YAC/D,CAAC,eAAe,CAAC,gBAAgB,CAAC,EAAE,kCAAkC;YACtE,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,iCAAiC;YACxD,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE,iCAAiC;YAChE,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,0BAA0B;YACjD,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,oCAAoC;YAC3D,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,8BAA8B;YAC1D,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE,iCAAiC;YAC/D,CAAC,eAAe,CAAC,kBAAkB,CAAC,EAAE,oBAAoB;YAC1D,CAAC,eAAe,CAAC,kBAAkB,CAAC,EAAE,oBAAoB;YAC1D,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE,oCAAoC;YACnE,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,qBAAqB;YAC5C,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,iBAAiB;YAC1C,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,uBAAuB;YAC9C,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,8BAA8B;YACtD,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,oCAAoC;YAC5D,CAAC,eAAe,CAAC,kBAAkB,CAAC,EAAE,6BAA6B;YACnE,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE,qBAAqB;YACnD,CAAC,eAAe,CAAC,iBAAiB,CAAC,EAAE,kCAAkC;YACvE,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,uBAAuB;YAC9C,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,oCAAoC;YAC3D,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,8BAA8B;YACrD,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,wCAAwC;YAC/D,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,2CAA2C;YACnE,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,iDAAiD;YACzE,CAAC,eAAe,CAAC,qBAAqB,CAAC,EAAE,uBAAuB;YAChE,CAAC,eAAe,CAAC,mBAAmB,CAAC,EAAE,8BAA8B;YACrE,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,sBAAsB;YACnD,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,yBAAyB;YACpD,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,wBAAwB;YACrD,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE,wBAAwB;YAC1D,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,6BAA6B;YACpD,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE,iCAAiC;YAChE,CAAC,eAAe,CAAC,eAAe,CAAC,EAAE,gCAAgC;YACnE,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,2BAA2B;YACnD,CAAC,eAAe,CAAC,aAAa,CAAC,EAAE,iCAAiC;YAClE,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE,2BAA2B;YACzD,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,+BAA+B;YAC5D,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,2BAA2B;YAClD,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,cAAc;YACvC,CAAC,eAAe,CAAC,aAAa,CAAC,EAAE,sBAAsB;YACvD,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE,8BAA8B;YAC7D,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE,qCAAqC;YACnE,CAAC,eAAe,CAAC,gBAAgB,CAAC,EAAE,wBAAwB;YAC5D,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,uBAAuB;YACjD,CAAC,eAAe,CAAC,mBAAmB,CAAC,EAAE,4BAA4B;YACnE,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE,6BAA6B;YAC3D,CAAC,eAAe,CAAC,eAAe,CAAC,EAAE,wBAAwB;YAC3D,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,gCAAgC;YAC3D,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,mBAAmB;SAC7C,CAAC;QAEF,OAAO,YAAY,CAAC,UAAU,CAAC,IAAI,qBAAqB,CAAC;IAC3D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CAAC,UAA2B;QACpD,MAAM,MAAM,GAAoC;YAC9C,2BAA2B;YAC3B,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,EAAE;YAC1B,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,EAAE;YACzB,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,EAAE;YAC7B,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,EAAE;YAC9B,CAAC,eAAe,CAAC,gBAAgB,CAAC,EAAE,EAAE;YAEtC,0BAA0B;YAC1B,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,EAAE;YAC/B,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,EAAE;YACzB,CAAC,eAAe,CAAC,qBAAqB,CAAC,EAAE,EAAE;YAC3C,CAAC,eAAe,CAAC,iBAAiB,CAAC,EAAE,EAAE;YAEvC,qBAAqB;YACrB,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE,EAAE;YACpC,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,EAAE;YAC9B,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE,EAAE;YAChC,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,EAAE;YACzB,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,EAAE;YAC3B,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,EAAE;YAEzB,wCAAwC;YACxC,CAAC,eAAe,CAAC,aAAa,CAAC,EAAE,EAAE;YACnC,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE,EAAE;YACjC,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE,EAAE;YAChC,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,EAAE;YAC5B,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,EAAE;YAC7B,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,EAAE;SAC5B,CAAC;QAEF,OAAO,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC,6BAA6B;IAChE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB,CAAC,UAA2B;QACnD,OAAO;YACL,eAAe,CAAC,QAAQ;YACxB,eAAe,CAAC,cAAc;YAC9B,eAAe,CAAC,UAAU;YAC1B,eAAe,CAAC,UAAU;YAC1B,eAAe,CAAC,gBAAgB;YAChC,eAAe,CAAC,QAAQ;SACzB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB,CAAC,UAA2B;QACtD,MAAM,UAAU,GAAoC;YAClD,0BAA0B;YAC1B,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,EAAE;YACzB,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC;YAC5B,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,CAAC;YAC9B,CAAC,eAAe,CAAC,mBAAmB,CAAC,EAAE,CAAC;YAExC,6BAA6B;YAC7B,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7B,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC;YACxB,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,CAAC;YAC9B,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC;YAExB,mBAAmB;YACnB,CAAC,eAAe,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACrC,CAAC,eAAe,CAAC,iBAAiB,CAAC,EAAE,CAAC;YACtC,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE,CAAC;YACnC,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE,CAAC;YAE/B,iBAAiB;YACjB,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE,CAAC;YAC/B,CAAC,eAAe,CAAC,aAAa,CAAC,EAAE,CAAC;YAClC,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3B,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC;SAC7B,CAAC;QAEF,OAAO,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,0BAA0B;IAChE,CAAC;CACF;AA3TD,oDA2TC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\event-source-type.enum.ts"], "sourcesContent": ["/**\r\n * Event Source Type Enum\r\n * \r\n * Represents the various types of sources that can generate security events.\r\n * Used to categorize and route events based on their origin.\r\n */\r\nexport enum EventSourceType {\r\n  /**\r\n   * Network Infrastructure Sources\r\n   */\r\n  \r\n  /** Network firewalls and security appliances */\r\n  FIREWALL = 'firewall',\r\n  \r\n  /** Intrusion Detection/Prevention Systems */\r\n  IDS_IPS = 'ids_ips',\r\n  \r\n  /** Network routers and switches */\r\n  NETWORK_DEVICE = 'network_device',\r\n  \r\n  /** Load balancers and proxies */\r\n  LOAD_BALANCER = 'load_balancer',\r\n  \r\n  /** VPN concentrators and gateways */\r\n  VPN_GATEWAY = 'vpn_gateway',\r\n  \r\n  /** Network access control systems */\r\n  NAC = 'nac',\r\n  \r\n  /** DNS servers and security services */\r\n  DNS_SERVER = 'dns_server',\r\n  \r\n  /** DHCP servers */\r\n  DHCP_SERVER = 'dhcp_server',\r\n\r\n  /**\r\n   * Endpoint Sources\r\n   */\r\n  \r\n  /** Endpoint Detection and Response systems */\r\n  EDR = 'edr',\r\n  \r\n  /** Antivirus and anti-malware solutions */\r\n  ANTIVIRUS = 'antivirus',\r\n  \r\n  /** Host-based Intrusion Detection Systems */\r\n  HIDS = 'hids',\r\n  \r\n  /** Operating system logs (Windows, Linux, macOS) */\r\n  OPERATING_SYSTEM = 'operating_system',\r\n  \r\n  /** Mobile device management systems */\r\n  MDM = 'mdm',\r\n  \r\n  /** USB and removable media controls */\r\n  USB_CONTROL = 'usb_control',\r\n\r\n  /**\r\n   * Application Sources\r\n   */\r\n  \r\n  /** Web application firewalls */\r\n  WAF = 'waf',\r\n  \r\n  /** Application performance monitoring */\r\n  APM = 'apm',\r\n  \r\n  /** Database activity monitoring */\r\n  DATABASE = 'database',\r\n  \r\n  /** Web servers (Apache, Nginx, IIS) */\r\n  WEB_SERVER = 'web_server',\r\n  \r\n  /** Application servers */\r\n  APPLICATION_SERVER = 'application_server',\r\n  \r\n  /** Custom applications */\r\n  CUSTOM_APPLICATION = 'custom_application',\r\n  \r\n  /** API gateways and management platforms */\r\n  API_GATEWAY = 'api_gateway',\r\n\r\n  /**\r\n   * Cloud Sources\r\n   */\r\n  \r\n  /** Amazon Web Services */\r\n  AWS = 'aws',\r\n  \r\n  /** Microsoft Azure */\r\n  AZURE = 'azure',\r\n  \r\n  /** Google Cloud Platform */\r\n  GCP = 'gcp',\r\n  \r\n  /** Cloud Access Security Brokers */\r\n  CASB = 'casb',\r\n  \r\n  /** Cloud workload protection platforms */\r\n  CWPP = 'cwpp',\r\n  \r\n  /** Container security platforms */\r\n  CONTAINER_SECURITY = 'container_security',\r\n  \r\n  /** Kubernetes security */\r\n  KUBERNETES = 'kubernetes',\r\n\r\n  /**\r\n   * Identity and Access Management\r\n   */\r\n  \r\n  /** Active Directory and LDAP */\r\n  DIRECTORY_SERVICE = 'directory_service',\r\n  \r\n  /** Single Sign-On systems */\r\n  SSO = 'sso',\r\n  \r\n  /** Multi-factor authentication systems */\r\n  MFA = 'mfa',\r\n  \r\n  /** Privileged Access Management */\r\n  PAM = 'pam',\r\n  \r\n  /** Identity governance and administration */\r\n  IGA = 'iga',\r\n\r\n  /**\r\n   * Security Tools\r\n   */\r\n  \r\n  /** Security Information and Event Management */\r\n  SIEM = 'siem',\r\n  \r\n  /** Security Orchestration, Automation and Response */\r\n  SOAR = 'soar',\r\n  \r\n  /** Vulnerability scanners */\r\n  VULNERABILITY_SCANNER = 'vulnerability_scanner',\r\n  \r\n  /** Threat intelligence platforms */\r\n  THREAT_INTELLIGENCE = 'threat_intelligence',\r\n  \r\n  /** Deception technology */\r\n  DECEPTION = 'deception',\r\n  \r\n  /** Sandbox analysis systems */\r\n  SANDBOX = 'sandbox',\r\n  \r\n  /** Digital forensics tools */\r\n  FORENSICS = 'forensics',\r\n\r\n  /**\r\n   * Email and Communication\r\n   */\r\n  \r\n  /** Email security gateways */\r\n  EMAIL_SECURITY = 'email_security',\r\n  \r\n  /** Data Loss Prevention systems */\r\n  DLP = 'dlp',\r\n  \r\n  /** Unified communications security */\r\n  UC_SECURITY = 'uc_security',\r\n\r\n  /**\r\n   * Physical Security\r\n   */\r\n  \r\n  /** Physical access control systems */\r\n  PHYSICAL_ACCESS = 'physical_access',\r\n  \r\n  /** Video surveillance systems */\r\n  CCTV = 'cctv',\r\n  \r\n  /** Environmental monitoring */\r\n  ENVIRONMENTAL = 'environmental',\r\n\r\n  /**\r\n   * IoT and OT\r\n   */\r\n  \r\n  /** Internet of Things devices */\r\n  IOT_DEVICE = 'iot_device',\r\n  \r\n  /** Operational Technology systems */\r\n  OT_SYSTEM = 'ot_system',\r\n  \r\n  /** Industrial Control Systems */\r\n  ICS = 'ics',\r\n  \r\n  /** SCADA systems */\r\n  SCADA = 'scada',\r\n\r\n  /**\r\n   * External Sources\r\n   */\r\n  \r\n  /** External threat feeds */\r\n  EXTERNAL_FEED = 'external_feed',\r\n  \r\n  /** Third-party security services */\r\n  THIRD_PARTY = 'third_party',\r\n  \r\n  /** Government and law enforcement alerts */\r\n  GOVERNMENT = 'government',\r\n  \r\n  /** Industry sharing groups */\r\n  INDUSTRY_SHARING = 'industry_sharing',\r\n\r\n  /**\r\n   * Internal Sources\r\n   */\r\n  \r\n  /** Manual event creation by analysts */\r\n  MANUAL = 'manual',\r\n  \r\n  /** Automated internal processes */\r\n  INTERNAL_AUTOMATION = 'internal_automation',\r\n  \r\n  /** Compliance and audit systems */\r\n  COMPLIANCE = 'compliance',\r\n  \r\n  /** Risk management systems */\r\n  RISK_MANAGEMENT = 'risk_management',\r\n\r\n  /**\r\n   * Unknown or Other\r\n   */\r\n  \r\n  /** Unknown or unidentified source */\r\n  UNKNOWN = 'unknown',\r\n  \r\n  /** Other sources not categorized above */\r\n  OTHER = 'other'\r\n}\r\n\r\n/**\r\n * Event Source Type Utilities\r\n */\r\nexport class EventSourceTypeUtils {\r\n  /**\r\n   * Get all source types\r\n   */\r\n  static getAllSourceTypes(): EventSourceType[] {\r\n    return Object.values(EventSourceType);\r\n  }\r\n\r\n  /**\r\n   * Get network infrastructure source types\r\n   */\r\n  static getNetworkSourceTypes(): EventSourceType[] {\r\n    return [\r\n      EventSourceType.FIREWALL,\r\n      EventSourceType.IDS_IPS,\r\n      EventSourceType.NETWORK_DEVICE,\r\n      EventSourceType.LOAD_BALANCER,\r\n      EventSourceType.VPN_GATEWAY,\r\n      EventSourceType.NAC,\r\n      EventSourceType.DNS_SERVER,\r\n      EventSourceType.DHCP_SERVER,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Get endpoint source types\r\n   */\r\n  static getEndpointSourceTypes(): EventSourceType[] {\r\n    return [\r\n      EventSourceType.EDR,\r\n      EventSourceType.ANTIVIRUS,\r\n      EventSourceType.HIDS,\r\n      EventSourceType.OPERATING_SYSTEM,\r\n      EventSourceType.MDM,\r\n      EventSourceType.USB_CONTROL,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Get application source types\r\n   */\r\n  static getApplicationSourceTypes(): EventSourceType[] {\r\n    return [\r\n      EventSourceType.WAF,\r\n      EventSourceType.APM,\r\n      EventSourceType.DATABASE,\r\n      EventSourceType.WEB_SERVER,\r\n      EventSourceType.APPLICATION_SERVER,\r\n      EventSourceType.CUSTOM_APPLICATION,\r\n      EventSourceType.API_GATEWAY,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Get cloud source types\r\n   */\r\n  static getCloudSourceTypes(): EventSourceType[] {\r\n    return [\r\n      EventSourceType.AWS,\r\n      EventSourceType.AZURE,\r\n      EventSourceType.GCP,\r\n      EventSourceType.CASB,\r\n      EventSourceType.CWPP,\r\n      EventSourceType.CONTAINER_SECURITY,\r\n      EventSourceType.KUBERNETES,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Get identity and access management source types\r\n   */\r\n  static getIdentitySourceTypes(): EventSourceType[] {\r\n    return [\r\n      EventSourceType.DIRECTORY_SERVICE,\r\n      EventSourceType.SSO,\r\n      EventSourceType.MFA,\r\n      EventSourceType.PAM,\r\n      EventSourceType.IGA,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Get security tool source types\r\n   */\r\n  static getSecurityToolSourceTypes(): EventSourceType[] {\r\n    return [\r\n      EventSourceType.SIEM,\r\n      EventSourceType.SOAR,\r\n      EventSourceType.VULNERABILITY_SCANNER,\r\n      EventSourceType.THREAT_INTELLIGENCE,\r\n      EventSourceType.DECEPTION,\r\n      EventSourceType.SANDBOX,\r\n      EventSourceType.FORENSICS,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Get external source types\r\n   */\r\n  static getExternalSourceTypes(): EventSourceType[] {\r\n    return [\r\n      EventSourceType.EXTERNAL_FEED,\r\n      EventSourceType.THIRD_PARTY,\r\n      EventSourceType.GOVERNMENT,\r\n      EventSourceType.INDUSTRY_SHARING,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Check if source type is network-related\r\n   */\r\n  static isNetworkSource(sourceType: EventSourceType): boolean {\r\n    return EventSourceTypeUtils.getNetworkSourceTypes().includes(sourceType);\r\n  }\r\n\r\n  /**\r\n   * Check if source type is endpoint-related\r\n   */\r\n  static isEndpointSource(sourceType: EventSourceType): boolean {\r\n    return EventSourceTypeUtils.getEndpointSourceTypes().includes(sourceType);\r\n  }\r\n\r\n  /**\r\n   * Check if source type is cloud-related\r\n   */\r\n  static isCloudSource(sourceType: EventSourceType): boolean {\r\n    return EventSourceTypeUtils.getCloudSourceTypes().includes(sourceType);\r\n  }\r\n\r\n  /**\r\n   * Check if source type is external\r\n   */\r\n  static isExternalSource(sourceType: EventSourceType): boolean {\r\n    return EventSourceTypeUtils.getExternalSourceTypes().includes(sourceType);\r\n  }\r\n\r\n  /**\r\n   * Check if source type is a security tool\r\n   */\r\n  static isSecurityTool(sourceType: EventSourceType): boolean {\r\n    return EventSourceTypeUtils.getSecurityToolSourceTypes().includes(sourceType);\r\n  }\r\n\r\n  /**\r\n   * Get category for a source type\r\n   */\r\n  static getCategory(sourceType: EventSourceType): string {\r\n    if (EventSourceTypeUtils.isNetworkSource(sourceType)) return 'Network';\r\n    if (EventSourceTypeUtils.isEndpointSource(sourceType)) return 'Endpoint';\r\n    if (EventSourceTypeUtils.getApplicationSourceTypes().includes(sourceType)) return 'Application';\r\n    if (EventSourceTypeUtils.isCloudSource(sourceType)) return 'Cloud';\r\n    if (EventSourceTypeUtils.getIdentitySourceTypes().includes(sourceType)) return 'Identity';\r\n    if (EventSourceTypeUtils.isSecurityTool(sourceType)) return 'Security Tool';\r\n    if ([EventSourceType.EMAIL_SECURITY, EventSourceType.DLP, EventSourceType.UC_SECURITY].includes(sourceType)) return 'Communication';\r\n    if ([EventSourceType.PHYSICAL_ACCESS, EventSourceType.CCTV, EventSourceType.ENVIRONMENTAL].includes(sourceType)) return 'Physical';\r\n    if ([EventSourceType.IOT_DEVICE, EventSourceType.OT_SYSTEM, EventSourceType.ICS, EventSourceType.SCADA].includes(sourceType)) return 'IoT/OT';\r\n    if (EventSourceTypeUtils.isExternalSource(sourceType)) return 'External';\r\n    if ([EventSourceType.MANUAL, EventSourceType.INTERNAL_AUTOMATION, EventSourceType.COMPLIANCE, EventSourceType.RISK_MANAGEMENT].includes(sourceType)) return 'Internal';\r\n    return 'Other';\r\n  }\r\n\r\n  /**\r\n   * Get human-readable description\r\n   */\r\n  static getDescription(sourceType: EventSourceType): string {\r\n    const descriptions: Record<EventSourceType, string> = {\r\n      [EventSourceType.FIREWALL]: 'Network firewall and security appliance',\r\n      [EventSourceType.IDS_IPS]: 'Intrusion Detection/Prevention System',\r\n      [EventSourceType.NETWORK_DEVICE]: 'Network router, switch, or infrastructure device',\r\n      [EventSourceType.LOAD_BALANCER]: 'Load balancer or proxy server',\r\n      [EventSourceType.VPN_GATEWAY]: 'VPN concentrator or gateway',\r\n      [EventSourceType.NAC]: 'Network Access Control system',\r\n      [EventSourceType.DNS_SERVER]: 'DNS server or security service',\r\n      [EventSourceType.DHCP_SERVER]: 'DHCP server',\r\n      [EventSourceType.EDR]: 'Endpoint Detection and Response system',\r\n      [EventSourceType.ANTIVIRUS]: 'Antivirus or anti-malware solution',\r\n      [EventSourceType.HIDS]: 'Host-based Intrusion Detection System',\r\n      [EventSourceType.OPERATING_SYSTEM]: 'Operating system logs and events',\r\n      [EventSourceType.MDM]: 'Mobile Device Management system',\r\n      [EventSourceType.USB_CONTROL]: 'USB and removable media control',\r\n      [EventSourceType.WAF]: 'Web Application Firewall',\r\n      [EventSourceType.APM]: 'Application Performance Monitoring',\r\n      [EventSourceType.DATABASE]: 'Database activity monitoring',\r\n      [EventSourceType.WEB_SERVER]: 'Web server (Apache, Nginx, IIS)',\r\n      [EventSourceType.APPLICATION_SERVER]: 'Application server',\r\n      [EventSourceType.CUSTOM_APPLICATION]: 'Custom application',\r\n      [EventSourceType.API_GATEWAY]: 'API gateway or management platform',\r\n      [EventSourceType.AWS]: 'Amazon Web Services',\r\n      [EventSourceType.AZURE]: 'Microsoft Azure',\r\n      [EventSourceType.GCP]: 'Google Cloud Platform',\r\n      [EventSourceType.CASB]: 'Cloud Access Security Broker',\r\n      [EventSourceType.CWPP]: 'Cloud Workload Protection Platform',\r\n      [EventSourceType.CONTAINER_SECURITY]: 'Container security platform',\r\n      [EventSourceType.KUBERNETES]: 'Kubernetes security',\r\n      [EventSourceType.DIRECTORY_SERVICE]: 'Active Directory or LDAP service',\r\n      [EventSourceType.SSO]: 'Single Sign-On system',\r\n      [EventSourceType.MFA]: 'Multi-Factor Authentication system',\r\n      [EventSourceType.PAM]: 'Privileged Access Management',\r\n      [EventSourceType.IGA]: 'Identity Governance and Administration',\r\n      [EventSourceType.SIEM]: 'Security Information and Event Management',\r\n      [EventSourceType.SOAR]: 'Security Orchestration, Automation and Response',\r\n      [EventSourceType.VULNERABILITY_SCANNER]: 'Vulnerability scanner',\r\n      [EventSourceType.THREAT_INTELLIGENCE]: 'Threat intelligence platform',\r\n      [EventSourceType.DECEPTION]: 'Deception technology',\r\n      [EventSourceType.SANDBOX]: 'Sandbox analysis system',\r\n      [EventSourceType.FORENSICS]: 'Digital forensics tool',\r\n      [EventSourceType.EMAIL_SECURITY]: 'Email security gateway',\r\n      [EventSourceType.DLP]: 'Data Loss Prevention system',\r\n      [EventSourceType.UC_SECURITY]: 'Unified Communications security',\r\n      [EventSourceType.PHYSICAL_ACCESS]: 'Physical access control system',\r\n      [EventSourceType.CCTV]: 'Video surveillance system',\r\n      [EventSourceType.ENVIRONMENTAL]: 'Environmental monitoring system',\r\n      [EventSourceType.IOT_DEVICE]: 'Internet of Things device',\r\n      [EventSourceType.OT_SYSTEM]: 'Operational Technology system',\r\n      [EventSourceType.ICS]: 'Industrial Control System',\r\n      [EventSourceType.SCADA]: 'SCADA system',\r\n      [EventSourceType.EXTERNAL_FEED]: 'External threat feed',\r\n      [EventSourceType.THIRD_PARTY]: 'Third-party security service',\r\n      [EventSourceType.GOVERNMENT]: 'Government or law enforcement alert',\r\n      [EventSourceType.INDUSTRY_SHARING]: 'Industry sharing group',\r\n      [EventSourceType.MANUAL]: 'Manual event creation',\r\n      [EventSourceType.INTERNAL_AUTOMATION]: 'Internal automated process',\r\n      [EventSourceType.COMPLIANCE]: 'Compliance and audit system',\r\n      [EventSourceType.RISK_MANAGEMENT]: 'Risk management system',\r\n      [EventSourceType.UNKNOWN]: 'Unknown or unidentified source',\r\n      [EventSourceType.OTHER]: 'Other source type',\r\n    };\r\n\r\n    return descriptions[sourceType] || 'Unknown source type';\r\n  }\r\n\r\n  /**\r\n   * Get reliability score for source type (0-100)\r\n   */\r\n  static getReliabilityScore(sourceType: EventSourceType): number {\r\n    const scores: Record<EventSourceType, number> = {\r\n      // High reliability sources\r\n      [EventSourceType.SIEM]: 95,\r\n      [EventSourceType.EDR]: 90,\r\n      [EventSourceType.IDS_IPS]: 90,\r\n      [EventSourceType.FIREWALL]: 85,\r\n      [EventSourceType.OPERATING_SYSTEM]: 85,\r\n      \r\n      // Medium-high reliability\r\n      [EventSourceType.ANTIVIRUS]: 80,\r\n      [EventSourceType.WAF]: 80,\r\n      [EventSourceType.VULNERABILITY_SCANNER]: 80,\r\n      [EventSourceType.DIRECTORY_SERVICE]: 80,\r\n      \r\n      // Medium reliability\r\n      [EventSourceType.NETWORK_DEVICE]: 75,\r\n      [EventSourceType.DATABASE]: 75,\r\n      [EventSourceType.WEB_SERVER]: 75,\r\n      [EventSourceType.AWS]: 75,\r\n      [EventSourceType.AZURE]: 75,\r\n      [EventSourceType.GCP]: 75,\r\n      \r\n      // Lower reliability or external sources\r\n      [EventSourceType.EXTERNAL_FEED]: 60,\r\n      [EventSourceType.THIRD_PARTY]: 60,\r\n      [EventSourceType.IOT_DEVICE]: 50,\r\n      [EventSourceType.MANUAL]: 70,\r\n      [EventSourceType.UNKNOWN]: 30,\r\n      [EventSourceType.OTHER]: 40,\r\n    };\r\n\r\n    return scores[sourceType] || 50; // Default medium reliability\r\n  }\r\n\r\n  /**\r\n   * Check if source type typically generates high-volume events\r\n   */\r\n  static isHighVolumeSource(sourceType: EventSourceType): boolean {\r\n    return [\r\n      EventSourceType.FIREWALL,\r\n      EventSourceType.NETWORK_DEVICE,\r\n      EventSourceType.WEB_SERVER,\r\n      EventSourceType.DNS_SERVER,\r\n      EventSourceType.OPERATING_SYSTEM,\r\n      EventSourceType.DATABASE,\r\n    ].includes(sourceType);\r\n  }\r\n\r\n  /**\r\n   * Get recommended processing priority (1-10, higher = more priority)\r\n   */\r\n  static getProcessingPriority(sourceType: EventSourceType): number {\r\n    const priorities: Record<EventSourceType, number> = {\r\n      // Critical security tools\r\n      [EventSourceType.EDR]: 10,\r\n      [EventSourceType.IDS_IPS]: 9,\r\n      [EventSourceType.DECEPTION]: 9,\r\n      [EventSourceType.THREAT_INTELLIGENCE]: 8,\r\n      \r\n      // Important security sources\r\n      [EventSourceType.FIREWALL]: 7,\r\n      [EventSourceType.WAF]: 7,\r\n      [EventSourceType.ANTIVIRUS]: 7,\r\n      [EventSourceType.PAM]: 8,\r\n      \r\n      // Standard sources\r\n      [EventSourceType.OPERATING_SYSTEM]: 6,\r\n      [EventSourceType.DIRECTORY_SERVICE]: 6,\r\n      [EventSourceType.NETWORK_DEVICE]: 5,\r\n      [EventSourceType.WEB_SERVER]: 5,\r\n      \r\n      // Lower priority\r\n      [EventSourceType.IOT_DEVICE]: 4,\r\n      [EventSourceType.ENVIRONMENTAL]: 3,\r\n      [EventSourceType.MANUAL]: 6,\r\n      [EventSourceType.UNKNOWN]: 2,\r\n    };\r\n\r\n    return priorities[sourceType] || 5; // Default medium priority\r\n  }\r\n}\r\n"], "version": 3}