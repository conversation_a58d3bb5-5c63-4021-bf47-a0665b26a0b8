{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\response-action-created.domain-event.ts", "mappings": ";;;AAAA,6DAA4E;AAC5E,gEAAuD;AAkCvD;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,MAAa,gCAAiC,SAAQ,+BAA+C;IACnG,YACE,WAA2B,EAC3B,SAAyC,EACzC,OAOC;QAED,KAAK,CAAC,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAI,sBAAsB;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,KAAK,UAAU,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,MAAM,mBAAmB,GAAG;YAC1B,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,QAAQ;YACnB,6BAAU,CAAC,YAAY;YACvB,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,mBAAmB;YAC9B,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,WAAW;YACtB,6BAAU,CAAC,kBAAkB;YAC7B,6BAAU,CAAC,gBAAgB;YAC3B,6BAAU,CAAC,iBAAiB;SAC7B,CAAC;QAEF,OAAO,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,MAAM,kBAAkB,GAAG;YACzB,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,QAAQ;YACnB,6BAAU,CAAC,YAAY;YACvB,6BAAU,CAAC,SAAS;YACpB,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,YAAY;YACvB,6BAAU,CAAC,oBAAoB;YAC/B,6BAAU,CAAC,eAAe;SAC3B,CAAC;QAEF,OAAO,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,MAAM,mBAAmB,GAAG;YAC1B,6BAAU,CAAC,UAAU;YACrB,6BAAU,CAAC,QAAQ;YACnB,6BAAU,CAAC,UAAU;YACrB,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,WAAW;YACtB,6BAAU,CAAC,aAAa;YACxB,6BAAU,CAAC,eAAe;SAC3B,CAAC;QAEF,OAAO,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,UAAkB;QAClC,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,KAAK,UAAU,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,KAAK,SAAS,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,KAAK,SAAS,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,OAAO,IAAI,CAAC,SAAS,CAAC,sBAAsB,KAAK,SAAS,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,yBAAyB;QACvB,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,IAAI,CAAC,SAAS,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;YACnE,OAAO,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;YAC5D,OAAO,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC;YACnE,OAAO,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YACrD,OAAO,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;YAC1B,OAAO,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAC9C,OAAO,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;YACtB,OAAO,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;YACnD,OAAO,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;YAC/B,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAC/C,OAAO,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAC/C,OAAO,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAC3C,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC;YACpC,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;YAC1B,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC9B,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;YACtB,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAClC,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC;YAC5B,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;YAC/B,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAC1C,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAAE,OAAO,aAAa,CAAC;QACrD,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAAE,OAAO,cAAc,CAAC;QAEvD,MAAM,gBAAgB,GAAG;YACvB,6BAAU,CAAC,kBAAkB;YAC7B,6BAAU,CAAC,WAAW;YACtB,6BAAU,CAAC,gBAAgB;YAC3B,6BAAU,CAAC,iBAAiB;YAC5B,6BAAU,CAAC,mBAAmB;YAC9B,6BAAU,CAAC,gBAAgB;YAC3B,6BAAU,CAAC,YAAY;SACxB,CAAC;QAEF,IAAI,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;YAAE,OAAO,WAAW,CAAC;QAE7E,MAAM,kBAAkB,GAAG;YACzB,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,WAAW;YACtB,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,mBAAmB;YAC9B,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,kBAAkB;SAC9B,CAAC;QAEF,IAAI,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;YAAE,OAAO,aAAa,CAAC;QAEjF,MAAM,eAAe,GAAG;YACtB,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,sBAAsB;SAClC,CAAC;QAEF,IAAI,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;YAAE,OAAO,UAAU,CAAC;QAE3E,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,KAAK,UAAU,EAAE,CAAC;YAC3C,OAAO,WAAW,CAAC;QACrB,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,KAAK,MAAM,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;YACrE,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACzC,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,yBAAyB;QACvB,mCAAmC;QACnC,MAAM,aAAa,GAAwC;YACzD,CAAC,6BAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YACxB,CAAC,6BAAU,CAAC,YAAY,CAAC,EAAE,CAAC;YAC5B,CAAC,6BAAU,CAAC,UAAU,CAAC,EAAE,CAAC;YAC1B,CAAC,6BAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YACxB,CAAC,6BAAU,CAAC,eAAe,CAAC,EAAE,CAAC;YAC/B,CAAC,6BAAU,CAAC,eAAe,CAAC,EAAE,CAAC;YAC/B,CAAC,6BAAU,CAAC,cAAc,CAAC,EAAE,EAAE;YAC/B,CAAC,6BAAU,CAAC,kBAAkB,CAAC,EAAE,EAAE;YACnC,CAAC,6BAAU,CAAC,mBAAmB,CAAC,EAAE,EAAE;YACpC,CAAC,6BAAU,CAAC,gBAAgB,CAAC,EAAE,EAAE;YACjC,CAAC,6BAAU,CAAC,iBAAiB,CAAC,EAAE,GAAG;YACnC,CAAC,6BAAU,CAAC,cAAc,CAAC,EAAE,GAAG;SACjC,CAAC;QAEF,OAAO,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,YAAY;QACV,MAAM,eAAe,GAAG;YACtB,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,WAAW;YACtB,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,cAAc;SAC1B,CAAC;QAEF,MAAM,iBAAiB,GAAG;YACxB,6BAAU,CAAC,cAAc;YACzB,6BAAU,CAAC,mBAAmB;YAC9B,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,eAAe;YAC1B,6BAAU,CAAC,eAAe;SAC3B,CAAC;QAEF,IAAI,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,CAAC;YACxD,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,IAAI,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,CAAC;YAC1D,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,2BAA2B;QACzB,MAAM,cAAc,GAAa,EAAE,CAAC;QAEpC,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;YAC/B,cAAc,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;YACrE,cAAc,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,KAAK,6BAAU,CAAC,WAAW,EAAE,CAAC;YACzD,cAAc,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;YACnE,cAAc,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,KAAK,6BAAU,CAAC,eAAe,EAAE,CAAC;YAC7D,cAAc,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;YAChE,cAAc,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,EAAE,KAAK,MAAM,EAAE,CAAC;YACnC,cAAc,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;YAC1E,cAAc,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QACjE,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,kBAAkB;QAgBhB,OAAO;YACL,SAAS,EAAE,uBAAuB;YAClC,MAAM,EAAE,yBAAyB;YACjC,QAAQ,EAAE,gBAAgB;YAC1B,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;YACvC,IAAI,EAAE,IAAI,CAAC,SAAS;YACpB,QAAQ,EAAE;gBACR,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ;gBACjC,QAAQ,EAAE,IAAI,CAAC,iBAAiB,EAAE;gBAClC,OAAO,EAAE,IAAI,CAAC,mBAAmB,EAAE;gBACnC,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE;gBAC9B,sBAAsB,EAAE,IAAI,CAAC,yBAAyB,EAAE;gBACxD,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW;gBACvC,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,gBAAgB;aAClD;SACF,CAAC;IACJ,CAAC;CACF;AAvbD,4EAubC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\events\\response-action-created.domain-event.ts"], "sourcesContent": ["import { BaseDomainEvent, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { ActionType } from '../enums/action-type.enum';\r\nimport { ActionStatus } from '../enums/action-status.enum';\r\n\r\n/**\r\n * Response Action Created Domain Event Data\r\n */\r\nexport interface ResponseActionCreatedEventData {\r\n  /** Type of action that was created */\r\n  actionType: ActionType;\r\n  /** Initial status of the action */\r\n  status: ActionStatus;\r\n  /** Action title */\r\n  title: string;\r\n  /** Action priority */\r\n  priority: 'low' | 'normal' | 'high' | 'critical';\r\n  /** Whether the action is automated */\r\n  isAutomated: boolean;\r\n  /** Whether approval is required */\r\n  approvalRequired: boolean;\r\n  /** Target information */\r\n  target?: {\r\n    type: 'event' | 'threat' | 'vulnerability' | 'system' | 'user' | 'network' | 'custom';\r\n    id: string;\r\n    name?: string;\r\n    metadata?: Record<string, any>;\r\n  };\r\n  /** Related event ID */\r\n  relatedEventId?: string;\r\n  /** Related threat ID */\r\n  relatedThreatId?: string;\r\n  /** Related vulnerability ID */\r\n  relatedVulnerabilityId?: string;\r\n}\r\n\r\n/**\r\n * Response Action Created Domain Event\r\n * \r\n * Raised when a new response action is created in the system.\r\n * This event indicates that a response action has been defined and is ready\r\n * for approval, scheduling, or execution depending on its configuration.\r\n * \r\n * Key information:\r\n * - Action type and configuration\r\n * - Priority and automation settings\r\n * - Approval requirements\r\n * - Related entities (events, threats, vulnerabilities)\r\n * - Target information\r\n * \r\n * Use cases:\r\n * - Trigger approval workflows for manual actions\r\n * - Queue automated actions for execution\r\n * - Notify stakeholders of new response actions\r\n * - Update dashboards and monitoring systems\r\n * - Log action creation for audit trails\r\n */\r\nexport class ResponseActionCreatedDomainEvent extends BaseDomainEvent<ResponseActionCreatedEventData> {\r\n  constructor(\r\n    aggregateId: UniqueEntityId,\r\n    eventData: ResponseActionCreatedEventData,\r\n    options?: {\r\n      eventId?: UniqueEntityId;\r\n      occurredOn?: Date;\r\n      eventVersion?: number;\r\n      correlationId?: string;\r\n      causationId?: string;\r\n      metadata?: Record<string, any>;\r\n    }\r\n  ) {\r\n    super(aggregateId, eventData, options);\r\n  }\r\n\r\n  /**\r\n   * Get the action type\r\n   */\r\n  get actionType(): ActionType {\r\n    return this.eventData.actionType;\r\n  }\r\n\r\n  /**\r\n   * Get the action status\r\n   */\r\n  get status(): ActionStatus {\r\n    return this.eventData.status;\r\n  }\r\n\r\n  /**\r\n   * Get the action title\r\n   */\r\n  get title(): string {\r\n    return this.eventData.title;\r\n  }\r\n\r\n  /**\r\n   * Get the action priority\r\n   */\r\n  get priority(): string {\r\n    return this.eventData.priority;\r\n  }\r\n\r\n  /**\r\n   * Check if the action is automated\r\n   */\r\n  get isAutomated(): boolean {\r\n    return this.eventData.isAutomated;\r\n  }\r\n\r\n  /**\r\n   * Check if approval is required\r\n   */\r\n  get approvalRequired(): boolean {\r\n    return this.eventData.approvalRequired;\r\n  }\r\n\r\n  /**\r\n   * Get the target information\r\n   */\r\n  get target(): ResponseActionCreatedEventData['target'] {\r\n    return this.eventData.target;\r\n  }\r\n\r\n  /**\r\n   * Get related event ID\r\n   */\r\n  get relatedEventId(): string | undefined {\r\n    return this.eventData.relatedEventId;\r\n  }\r\n\r\n  /**\r\n   * Get related threat ID\r\n   */\r\n  get relatedThreatId(): string | undefined {\r\n    return this.eventData.relatedThreatId;\r\n  }\r\n\r\n  /**\r\n   * Get related vulnerability ID\r\n   */\r\n  get relatedVulnerabilityId(): string | undefined {\r\n    return this.eventData.relatedVulnerabilityId;\r\n  }\r\n\r\n  /**\r\n   * Check if this is a high priority action\r\n   */\r\n  isHighPriority(): boolean {\r\n    return ['high', 'critical'].includes(this.eventData.priority);\r\n  }\r\n\r\n  /**\r\n   * Check if this is a critical action\r\n   */\r\n  isCritical(): boolean {\r\n    return this.eventData.priority === 'critical';\r\n  }\r\n\r\n  /**\r\n   * Check if this is a security-related action\r\n   */\r\n  isSecurityAction(): boolean {\r\n    const securityActionTypes = [\r\n      ActionType.ISOLATE_SYSTEM,\r\n      ActionType.BLOCK_IP,\r\n      ActionType.BLOCK_DOMAIN,\r\n      ActionType.QUARANTINE_FILE,\r\n      ActionType.DISABLE_ACCOUNT,\r\n      ActionType.REMOVE_MALWARE,\r\n      ActionType.PATCH_VULNERABILITY,\r\n      ActionType.UPDATE_FIREWALL,\r\n      ActionType.THREAT_HUNT,\r\n      ActionType.VULNERABILITY_SCAN,\r\n      ActionType.MALWARE_ANALYSIS,\r\n      ActionType.FORENSIC_ANALYSIS,\r\n    ];\r\n    \r\n    return securityActionTypes.includes(this.eventData.actionType);\r\n  }\r\n\r\n  /**\r\n   * Check if this is a containment action\r\n   */\r\n  isContainmentAction(): boolean {\r\n    const containmentActions = [\r\n      ActionType.ISOLATE_SYSTEM,\r\n      ActionType.QUARANTINE_FILE,\r\n      ActionType.BLOCK_IP,\r\n      ActionType.BLOCK_DOMAIN,\r\n      ActionType.BLOCK_URL,\r\n      ActionType.DISABLE_ACCOUNT,\r\n      ActionType.REVOKE_TOKEN,\r\n      ActionType.TERMINATE_CONNECTION,\r\n      ActionType.SHUTDOWN_SYSTEM,\r\n    ];\r\n    \r\n    return containmentActions.includes(this.eventData.actionType);\r\n  }\r\n\r\n  /**\r\n   * Check if this is a notification action\r\n   */\r\n  isNotificationAction(): boolean {\r\n    const notificationActions = [\r\n      ActionType.SEND_EMAIL,\r\n      ActionType.SEND_SMS,\r\n      ActionType.SEND_SLACK,\r\n      ActionType.TRIGGER_WEBHOOK,\r\n      ActionType.PAGE_ONCALL,\r\n      ActionType.CREATE_TICKET,\r\n      ActionType.UPDATE_INCIDENT,\r\n    ];\r\n    \r\n    return notificationActions.includes(this.eventData.actionType);\r\n  }\r\n\r\n  /**\r\n   * Check if this action targets a specific entity type\r\n   */\r\n  targetsEntityType(entityType: string): boolean {\r\n    return this.eventData.target?.type === entityType;\r\n  }\r\n\r\n  /**\r\n   * Check if this action is related to an event\r\n   */\r\n  isEventRelated(): boolean {\r\n    return this.eventData.relatedEventId !== undefined;\r\n  }\r\n\r\n  /**\r\n   * Check if this action is related to a threat\r\n   */\r\n  isThreatRelated(): boolean {\r\n    return this.eventData.relatedThreatId !== undefined;\r\n  }\r\n\r\n  /**\r\n   * Check if this action is related to a vulnerability\r\n   */\r\n  isVulnerabilityRelated(): boolean {\r\n    return this.eventData.relatedVulnerabilityId !== undefined;\r\n  }\r\n\r\n  /**\r\n   * Get recommended next actions based on the created action\r\n   */\r\n  getRecommendedNextActions(): string[] {\r\n    const actions: string[] = [];\r\n\r\n    if (this.eventData.approvalRequired && !this.eventData.isAutomated) {\r\n      actions.push('Request approval from appropriate authority');\r\n      actions.push('Notify approval workflow system');\r\n    }\r\n\r\n    if (this.eventData.isAutomated && !this.eventData.approvalRequired) {\r\n      actions.push('Queue action for automated execution');\r\n      actions.push('Validate execution prerequisites');\r\n    }\r\n\r\n    if (this.isHighPriority()) {\r\n      actions.push('Prioritize in execution queue');\r\n      actions.push('Notify high-priority action handlers');\r\n    }\r\n\r\n    if (this.isCritical()) {\r\n      actions.push('Escalate to incident response team');\r\n      actions.push('Activate emergency response procedures');\r\n    }\r\n\r\n    if (this.isContainmentAction()) {\r\n      actions.push('Prepare containment procedures');\r\n      actions.push('Validate containment scope and impact');\r\n    }\r\n\r\n    if (this.isNotificationAction()) {\r\n      actions.push('Prepare notification templates');\r\n      actions.push('Validate recipient lists');\r\n    }\r\n\r\n    return actions;\r\n  }\r\n\r\n  /**\r\n   * Get stakeholders who should be notified\r\n   */\r\n  getNotificationTargets(): string[] {\r\n    const targets: string[] = [];\r\n\r\n    if (this.eventData.approvalRequired) {\r\n      targets.push('approval-managers');\r\n    }\r\n\r\n    if (this.isHighPriority()) {\r\n      targets.push('security-team');\r\n      targets.push('incident-response-team');\r\n    }\r\n\r\n    if (this.isCritical()) {\r\n      targets.push('security-managers');\r\n      targets.push('on-call-engineers');\r\n    }\r\n\r\n    if (this.isSecurityAction()) {\r\n      targets.push('security-analysts');\r\n    }\r\n\r\n    if (this.isContainmentAction()) {\r\n      targets.push('containment-specialists');\r\n    }\r\n\r\n    return targets;\r\n  }\r\n\r\n  /**\r\n   * Get action category for classification\r\n   */\r\n  getActionCategory(): string {\r\n    if (this.isContainmentAction()) return 'Containment';\r\n    if (this.isNotificationAction()) return 'Notification';\r\n    \r\n    const detectionActions = [\r\n      ActionType.VULNERABILITY_SCAN,\r\n      ActionType.THREAT_HUNT,\r\n      ActionType.MALWARE_ANALYSIS,\r\n      ActionType.FORENSIC_ANALYSIS,\r\n      ActionType.BEHAVIORAL_ANALYSIS,\r\n      ActionType.NETWORK_ANALYSIS,\r\n      ActionType.LOG_ANALYSIS,\r\n    ];\r\n    \r\n    if (detectionActions.includes(this.eventData.actionType)) return 'Detection';\r\n    \r\n    const eradicationActions = [\r\n      ActionType.REMOVE_MALWARE,\r\n      ActionType.DELETE_FILE,\r\n      ActionType.CLEAN_REGISTRY,\r\n      ActionType.REMOVE_BACKDOOR,\r\n      ActionType.PATCH_VULNERABILITY,\r\n      ActionType.UPDATE_SOFTWARE,\r\n      ActionType.RECONFIGURE_SYSTEM,\r\n    ];\r\n    \r\n    if (eradicationActions.includes(this.eventData.actionType)) return 'Eradication';\r\n    \r\n    const recoveryActions = [\r\n      ActionType.RESTORE_BACKUP,\r\n      ActionType.REBUILD_SYSTEM,\r\n      ActionType.RESTORE_NETWORK,\r\n      ActionType.ENABLE_SERVICE,\r\n      ActionType.RESET_PASSWORD,\r\n      ActionType.REGENERATE_CERTIFICATE,\r\n    ];\r\n    \r\n    if (recoveryActions.includes(this.eventData.actionType)) return 'Recovery';\r\n    \r\n    return 'Other';\r\n  }\r\n\r\n  /**\r\n   * Get execution urgency level\r\n   */\r\n  getExecutionUrgency(): 'immediate' | 'urgent' | 'normal' | 'low' {\r\n    if (this.eventData.priority === 'critical') {\r\n      return 'immediate';\r\n    }\r\n    \r\n    if (this.eventData.priority === 'high' || this.isContainmentAction()) {\r\n      return 'urgent';\r\n    }\r\n    \r\n    if (this.eventData.priority === 'normal') {\r\n      return 'normal';\r\n    }\r\n    \r\n    return 'low';\r\n  }\r\n\r\n  /**\r\n   * Get estimated execution time based on action type\r\n   */\r\n  getEstimatedExecutionTime(): number {\r\n    // Return estimated time in minutes\r\n    const timeEstimates: Partial<Record<ActionType, number>> = {\r\n      [ActionType.BLOCK_IP]: 1,\r\n      [ActionType.BLOCK_DOMAIN]: 1,\r\n      [ActionType.SEND_EMAIL]: 1,\r\n      [ActionType.SEND_SMS]: 1,\r\n      [ActionType.QUARANTINE_FILE]: 3,\r\n      [ActionType.DISABLE_ACCOUNT]: 2,\r\n      [ActionType.ISOLATE_SYSTEM]: 10,\r\n      [ActionType.VULNERABILITY_SCAN]: 15,\r\n      [ActionType.PATCH_VULNERABILITY]: 20,\r\n      [ActionType.MALWARE_ANALYSIS]: 60,\r\n      [ActionType.FORENSIC_ANALYSIS]: 120,\r\n      [ActionType.REBUILD_SYSTEM]: 240,\r\n    };\r\n    \r\n    return timeEstimates[this.eventData.actionType] || 30;\r\n  }\r\n\r\n  /**\r\n   * Get risk level of executing this action\r\n   */\r\n  getRiskLevel(): 'low' | 'medium' | 'high' | 'critical' {\r\n    const highRiskActions = [\r\n      ActionType.ISOLATE_SYSTEM,\r\n      ActionType.SHUTDOWN_SYSTEM,\r\n      ActionType.DELETE_FILE,\r\n      ActionType.REBUILD_SYSTEM,\r\n      ActionType.EXECUTE_SCRIPT,\r\n    ];\r\n    \r\n    const mediumRiskActions = [\r\n      ActionType.RESTART_SYSTEM,\r\n      ActionType.PATCH_VULNERABILITY,\r\n      ActionType.UPDATE_SOFTWARE,\r\n      ActionType.DISABLE_ACCOUNT,\r\n      ActionType.UPDATE_FIREWALL,\r\n    ];\r\n    \r\n    if (highRiskActions.includes(this.eventData.actionType)) {\r\n      return 'high';\r\n    }\r\n    \r\n    if (mediumRiskActions.includes(this.eventData.actionType)) {\r\n      return 'medium';\r\n    }\r\n    \r\n    return 'low';\r\n  }\r\n\r\n  /**\r\n   * Get compliance considerations\r\n   */\r\n  getComplianceConsiderations(): string[] {\r\n    const considerations: string[] = [];\r\n    \r\n    if (this.isContainmentAction()) {\r\n      considerations.push('Document containment rationale for compliance');\r\n      considerations.push('Ensure containment actions are proportionate');\r\n    }\r\n    \r\n    if (this.eventData.actionType === ActionType.DELETE_FILE) {\r\n      considerations.push('Ensure data retention policies are followed');\r\n      considerations.push('Create forensic copies before deletion');\r\n    }\r\n    \r\n    if (this.eventData.actionType === ActionType.DISABLE_ACCOUNT) {\r\n      considerations.push('Follow user access management procedures');\r\n      considerations.push('Document account disabling rationale');\r\n    }\r\n    \r\n    if (this.getRiskLevel() === 'high') {\r\n      considerations.push('Obtain appropriate approvals for high-risk actions');\r\n      considerations.push('Document risk assessment and mitigation');\r\n    }\r\n    \r\n    return considerations;\r\n  }\r\n\r\n  /**\r\n   * Convert to integration event format\r\n   */\r\n  toIntegrationEvent(): {\r\n    eventType: string;\r\n    action: string;\r\n    resource: string;\r\n    resourceId: string;\r\n    data: ResponseActionCreatedEventData;\r\n    metadata: {\r\n      priority: string;\r\n      category: string;\r\n      urgency: string;\r\n      riskLevel: string;\r\n      estimatedExecutionTime: number;\r\n      isAutomated: boolean;\r\n      approvalRequired: boolean;\r\n    };\r\n  } {\r\n    return {\r\n      eventType: 'ResponseActionCreated',\r\n      action: 'response_action_created',\r\n      resource: 'ResponseAction',\r\n      resourceId: this.aggregateId.toString(),\r\n      data: this.eventData,\r\n      metadata: {\r\n        priority: this.eventData.priority,\r\n        category: this.getActionCategory(),\r\n        urgency: this.getExecutionUrgency(),\r\n        riskLevel: this.getRiskLevel(),\r\n        estimatedExecutionTime: this.getEstimatedExecutionTime(),\r\n        isAutomated: this.eventData.isAutomated,\r\n        approvalRequired: this.eventData.approvalRequired,\r\n      },\r\n    };\r\n  }\r\n}"], "version": 3}