76a56eac4dd8a104b94b67292a61eb1f
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const supertest_1 = __importDefault(require("supertest"));
const app_module_1 = require("../../app.module");
describe('Integration Workflow (e2e)', () => {
    let app;
    let adminToken;
    let userToken;
    let adminUserId;
    let regularUserId;
    beforeAll(async () => {
        const moduleFixture = await testing_1.Test.createTestingModule({
            imports: [
                config_1.ConfigModule.forRoot({
                    isGlobal: true,
                    envFilePath: '.env.test',
                }),
                app_module_1.AppModule,
            ],
        }).compile();
        app = moduleFixture.createNestApplication();
        await app.init();
        // Setup admin user
        const adminResponse = await (0, supertest_1.default)(app.getHttpServer())
            .post('/api/v1/auth/register')
            .send({
            email: '<EMAIL>',
            password: 'AdminPassword123!',
            name: 'Admin User',
            roles: ['admin'],
        });
        adminToken = adminResponse.body.data.tokens.accessToken;
        adminUserId = adminResponse.body.data.user.id;
        // Setup regular user
        const userResponse = await (0, supertest_1.default)(app.getHttpServer())
            .post('/api/v1/auth/register')
            .send({
            email: '<EMAIL>',
            password: 'UserPassword123!',
            name: 'Regular User',
        });
        userToken = userResponse.body.data.tokens.accessToken;
        regularUserId = userResponse.body.data.user.id;
    });
    afterAll(async () => {
        await app.close();
    });
    describe('Complete Security Event Workflow', () => {
        let securityEventId;
        let vulnerabilityId;
        let threatIntelId;
        it('should create a security event', async () => {
            const securityEventData = {
                eventType: 'vulnerability_detected',
                severity: 'high',
                description: 'SQL injection vulnerability detected in web application',
                sourceIp: '*************',
                targetIp: '*********',
                timestamp: new Date().toISOString(),
                metadata: {
                    applicationName: 'WebApp-1',
                    vulnerabilityType: 'SQL Injection',
                    cveId: 'CVE-2024-0001',
                    affectedEndpoint: '/api/users',
                },
            };
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/security-events')
                .set('Authorization', `Bearer ${userToken}`)
                .send(securityEventData)
                .expect(201);
            expect(response.body).toEqual({
                success: true,
                data: expect.objectContaining({
                    id: expect.any(String),
                    ...securityEventData,
                    status: 'open',
                    createdBy: regularUserId,
                    createdAt: expect.any(String),
                    updatedAt: expect.any(String),
                }),
                message: 'Security event created successfully',
                timestamp: expect.any(String),
            });
            securityEventId = response.body.data.id;
        });
        it('should retrieve the created security event', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get(`/api/v1/security-events/${securityEventId}`)
                .set('Authorization', `Bearer ${userToken}`)
                .expect(200);
            expect(response.body.data).toEqual(expect.objectContaining({
                id: securityEventId,
                eventType: 'vulnerability_detected',
                severity: 'high',
                status: 'open',
            }));
        });
        it('should create a vulnerability record linked to the security event', async () => {
            const vulnerabilityData = {
                cveId: 'CVE-2024-0001',
                title: 'SQL Injection in User Authentication',
                description: 'The application is vulnerable to SQL injection attacks in the user authentication module',
                severity: 'high',
                cvssScore: 8.5,
                affectedSystems: ['WebApp-1', 'Database-Server-1'],
                discoveryDate: new Date().toISOString(),
                status: 'open',
                securityEventId: securityEventId,
                remediation: {
                    steps: [
                        'Update input validation',
                        'Implement parameterized queries',
                        'Apply security patches',
                    ],
                    estimatedEffort: '4 hours',
                    priority: 'high',
                },
            };
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/vulnerability-management/vulnerabilities')
                .set('Authorization', `Bearer ${userToken}`)
                .send(vulnerabilityData)
                .expect(201);
            expect(response.body.data).toEqual(expect.objectContaining({
                id: expect.any(String),
                cveId: 'CVE-2024-0001',
                severity: 'high',
                status: 'open',
                securityEventId: securityEventId,
            }));
            vulnerabilityId = response.body.data.id;
        });
        it('should create threat intelligence data', async () => {
            const threatIntelData = {
                threatType: 'malware',
                severity: 'critical',
                title: 'Advanced Persistent Threat Campaign',
                description: 'Sophisticated malware campaign targeting web applications',
                indicators: [
                    {
                        type: 'ip',
                        value: '*************',
                        confidence: 'high',
                    },
                    {
                        type: 'domain',
                        value: 'malicious-domain.com',
                        confidence: 'medium',
                    },
                ],
                ttps: [
                    'T1190 - Exploit Public-Facing Application',
                    'T1505.003 - Web Shell',
                ],
                sources: ['Internal Analysis', 'Threat Feed Alpha'],
                firstSeen: new Date().toISOString(),
                lastSeen: new Date().toISOString(),
                isActive: true,
            };
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/threat-intelligence/threats')
                .set('Authorization', `Bearer ${userToken}`)
                .send(threatIntelData)
                .expect(201);
            expect(response.body.data).toEqual(expect.objectContaining({
                id: expect.any(String),
                threatType: 'malware',
                severity: 'critical',
                isActive: true,
            }));
            threatIntelId = response.body.data.id;
        });
        it('should correlate security event with threat intelligence', async () => {
            const correlationData = {
                securityEventId: securityEventId,
                threatIntelligenceId: threatIntelId,
                correlationType: 'ip_match',
                confidence: 'high',
                matchedIndicators: ['*************'],
                analysisNotes: 'Source IP matches known malicious infrastructure',
            };
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/analytics/correlations')
                .set('Authorization', `Bearer ${userToken}`)
                .send(correlationData)
                .expect(201);
            expect(response.body.data).toEqual(expect.objectContaining({
                id: expect.any(String),
                securityEventId: securityEventId,
                threatIntelligenceId: threatIntelId,
                correlationType: 'ip_match',
                confidence: 'high',
            }));
        });
        it('should generate automated response recommendations', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/automation/generate-response')
                .set('Authorization', `Bearer ${userToken}`)
                .send({
                securityEventId: securityEventId,
                vulnerabilityId: vulnerabilityId,
                threatIntelligenceId: threatIntelId,
            })
                .expect(201);
            expect(response.body.data).toEqual(expect.objectContaining({
                id: expect.any(String),
                securityEventId: securityEventId,
                recommendations: expect.arrayContaining([
                    expect.objectContaining({
                        action: expect.any(String),
                        priority: expect.any(String),
                        description: expect.any(String),
                    }),
                ]),
                automationLevel: expect.any(String),
                estimatedImpact: expect.any(String),
            }));
        });
        it('should update security event status through workflow', async () => {
            const updateData = {
                status: 'investigating',
                assignedTo: adminUserId,
                notes: 'Assigned to security team for investigation',
            };
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .put(`/api/v1/security-events/${securityEventId}`)
                .set('Authorization', `Bearer ${adminToken}`)
                .send(updateData)
                .expect(200);
            expect(response.body.data).toEqual(expect.objectContaining({
                id: securityEventId,
                status: 'investigating',
                assignedTo: adminUserId,
            }));
        });
        it('should track remediation progress', async () => {
            const remediationData = {
                vulnerabilityId: vulnerabilityId,
                status: 'in_progress',
                completedSteps: ['Update input validation'],
                remainingSteps: ['Implement parameterized queries', 'Apply security patches'],
                estimatedCompletion: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
                assignedTo: adminUserId,
            };
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/vulnerability-management/remediation')
                .set('Authorization', `Bearer ${adminToken}`)
                .send(remediationData)
                .expect(201);
            expect(response.body.data).toEqual(expect.objectContaining({
                id: expect.any(String),
                vulnerabilityId: vulnerabilityId,
                status: 'in_progress',
                completedSteps: ['Update input validation'],
            }));
        });
        it('should close the security event after remediation', async () => {
            const closeData = {
                status: 'resolved',
                resolution: 'Vulnerability patched and verified',
                resolvedBy: adminUserId,
                resolvedAt: new Date().toISOString(),
            };
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .put(`/api/v1/security-events/${securityEventId}`)
                .set('Authorization', `Bearer ${adminToken}`)
                .send(closeData)
                .expect(200);
            expect(response.body.data).toEqual(expect.objectContaining({
                id: securityEventId,
                status: 'resolved',
                resolvedBy: adminUserId,
            }));
        });
    });
    describe('Analytics and Reporting Workflow', () => {
        it('should generate security metrics dashboard', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/analytics/dashboard')
                .query({
                timeRange: '7d',
                includeMetrics: 'security_events,vulnerabilities,threats',
            })
                .set('Authorization', `Bearer ${userToken}`)
                .expect(200);
            expect(response.body.data).toEqual({
                timeRange: '7d',
                metrics: {
                    securityEvents: {
                        total: expect.any(Number),
                        byStatus: expect.any(Object),
                        bySeverity: expect.any(Object),
                        trend: expect.any(Array),
                    },
                    vulnerabilities: {
                        total: expect.any(Number),
                        byStatus: expect.any(Object),
                        bySeverity: expect.any(Object),
                        avgResolutionTime: expect.any(Number),
                    },
                    threats: {
                        total: expect.any(Number),
                        active: expect.any(Number),
                        byType: expect.any(Object),
                        topIndicators: expect.any(Array),
                    },
                },
                summary: {
                    riskScore: expect.any(Number),
                    trendDirection: expect.any(String),
                    criticalIssues: expect.any(Number),
                },
            });
        });
        it('should generate vulnerability assessment report', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/analytics/reports/vulnerability-assessment')
                .set('Authorization', `Bearer ${adminToken}`)
                .send({
                scope: 'all_systems',
                includeRemediation: true,
                format: 'json',
            })
                .expect(201);
            expect(response.body.data).toEqual({
                reportId: expect.any(String),
                generatedAt: expect.any(String),
                scope: 'all_systems',
                summary: {
                    totalVulnerabilities: expect.any(Number),
                    criticalCount: expect.any(Number),
                    highCount: expect.any(Number),
                    mediumCount: expect.any(Number),
                    lowCount: expect.any(Number),
                },
                vulnerabilities: expect.any(Array),
                recommendations: expect.any(Array),
                status: 'completed',
            });
        });
        it('should generate threat landscape analysis', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/threat-intelligence/landscape')
                .query({
                timeframe: '30d',
                threatTypes: 'malware,phishing,apt',
            })
                .set('Authorization', `Bearer ${userToken}`)
                .expect(200);
            expect(response.body.data).toEqual({
                timeframe: '30d',
                threatTypes: ['malware', 'phishing', 'apt'],
                analysis: {
                    totalThreats: expect.any(Number),
                    emergingThreats: expect.any(Array),
                    threatTrends: expect.any(Array),
                    geographicDistribution: expect.any(Object),
                    industryTargeting: expect.any(Object),
                },
                recommendations: expect.any(Array),
                riskAssessment: {
                    overallRisk: expect.any(String),
                    keyRisks: expect.any(Array),
                    mitigationStrategies: expect.any(Array),
                },
            });
        });
    });
    describe('User Management and RBAC Workflow', () => {
        let newUserId;
        it('should create a new user with specific roles', async () => {
            const userData = {
                email: '<EMAIL>',
                password: 'AnalystPassword123!',
                name: 'Security Analyst',
                roles: ['analyst'],
                permissions: ['read:security_events', 'write:security_events', 'read:vulnerabilities'],
            };
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/admin/users')
                .set('Authorization', `Bearer ${adminToken}`)
                .send(userData)
                .expect(201);
            expect(response.body.data.user).toEqual(expect.objectContaining({
                id: expect.any(String),
                email: '<EMAIL>',
                name: 'Security Analyst',
                roles: ['analyst'],
                permissions: expect.arrayContaining(['read:security_events']),
            }));
            newUserId = response.body.data.user.id;
        });
        it('should verify role-based access for the new user', async () => {
            // Login as the new analyst user
            const loginResponse = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/auth/login')
                .send({
                email: '<EMAIL>',
                password: 'AnalystPassword123!',
            })
                .expect(200);
            const analystToken = loginResponse.body.data.tokens.accessToken;
            // Should be able to read security events
            await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/security-events')
                .set('Authorization', `Bearer ${analystToken}`)
                .expect(200);
            // Should NOT be able to access admin endpoints
            await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/admin/users')
                .set('Authorization', `Bearer ${analystToken}`)
                .expect(403);
            // Should NOT be able to delete security events (no delete permission)
            await (0, supertest_1.default)(app.getHttpServer())
                .delete('/api/v1/security-events/some-id')
                .set('Authorization', `Bearer ${analystToken}`)
                .expect(403);
        });
        it('should update user permissions', async () => {
            const updateData = {
                permissions: [
                    'read:security_events',
                    'write:security_events',
                    'delete:security_events',
                    'read:vulnerabilities',
                    'write:vulnerabilities',
                ],
            };
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .put(`/api/v1/admin/users/${newUserId}/permissions`)
                .set('Authorization', `Bearer ${adminToken}`)
                .send(updateData)
                .expect(200);
            expect(response.body.data.permissions).toEqual(expect.arrayContaining([
                'read:security_events',
                'write:security_events',
                'delete:security_events',
            ]));
        });
        it('should audit user permission changes', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get(`/api/v1/admin/audit/user-changes/${newUserId}`)
                .set('Authorization', `Bearer ${adminToken}`)
                .expect(200);
            expect(response.body.data.auditLog).toEqual(expect.arrayContaining([
                expect.objectContaining({
                    action: 'user_created',
                    performedBy: adminUserId,
                    timestamp: expect.any(String),
                }),
                expect.objectContaining({
                    action: 'permissions_updated',
                    performedBy: adminUserId,
                    timestamp: expect.any(String),
                }),
            ]));
        });
    });
    describe('System Configuration and Health Workflow', () => {
        it('should update system configuration', async () => {
            const configData = {
                alerting: {
                    emailNotifications: true,
                    webhookUrl: 'https://hooks.example.com/security-alerts',
                    severityThreshold: 'medium',
                },
                scanning: {
                    automaticScanning: true,
                    scanInterval: '24h',
                    scanDepth: 'comprehensive',
                },
                retention: {
                    securityEvents: '90d',
                    auditLogs: '365d',
                    threatIntelligence: '180d',
                },
            };
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .put('/api/v1/admin/system/configuration')
                .set('Authorization', `Bearer ${adminToken}`)
                .send(configData)
                .expect(200);
            expect(response.body.data.configuration).toEqual(expect.objectContaining({
                alerting: expect.objectContaining({
                    emailNotifications: true,
                    severityThreshold: 'medium',
                }),
                scanning: expect.objectContaining({
                    automaticScanning: true,
                    scanInterval: '24h',
                }),
            }));
        });
        it('should perform comprehensive system health check', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/health/comprehensive')
                .set('Authorization', `Bearer ${adminToken}`)
                .expect(200);
            expect(response.body).toEqual({
                status: expect.any(String),
                timestamp: expect.any(String),
                components: {
                    application: expect.any(String),
                    database: expect.any(String),
                    redis: expect.any(String),
                    externalServices: expect.any(String),
                    fileSystem: expect.any(String),
                    memory: expect.any(String),
                    cpu: expect.any(String),
                },
                metrics: {
                    uptime: expect.any(Number),
                    responseTime: expect.any(Number),
                    throughput: expect.any(Number),
                    errorRate: expect.any(Number),
                },
                dependencies: expect.any(Array),
                recommendations: expect.any(Array),
            });
        });
        it('should validate system performance metrics', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/metrics/performance')
                .set('Authorization', `Bearer ${adminToken}`)
                .expect(200);
            expect(response.body).toEqual({
                timestamp: expect.any(String),
                application: {
                    requestsPerSecond: expect.any(Number),
                    averageResponseTime: expect.any(Number),
                    errorRate: expect.any(Number),
                    activeConnections: expect.any(Number),
                },
                system: {
                    cpuUsage: expect.any(Number),
                    memoryUsage: expect.any(Number),
                    diskUsage: expect.any(Number),
                    networkIO: expect.any(Object),
                },
                database: {
                    connectionPoolSize: expect.any(Number),
                    activeQueries: expect.any(Number),
                    slowQueries: expect.any(Number),
                    cacheHitRatio: expect.any(Number),
                },
                business: {
                    securityEventsProcessed: expect.any(Number),
                    vulnerabilitiesDetected: expect.any(Number),
                    threatsAnalyzed: expect.any(Number),
                    alertsGenerated: expect.any(Number),
                },
            });
        });
    });
    describe('Data Export and Backup Workflow', () => {
        it('should export security events data', async () => {
            const exportRequest = {
                dataType: 'security_events',
                format: 'json',
                dateRange: {
                    start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
                    end: new Date().toISOString(),
                },
                filters: {
                    severity: ['high', 'critical'],
                    status: ['open', 'investigating'],
                },
            };
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/admin/export')
                .set('Authorization', `Bearer ${adminToken}`)
                .send(exportRequest)
                .expect(202);
            expect(response.body).toEqual({
                success: true,
                data: {
                    exportId: expect.any(String),
                    status: 'processing',
                    estimatedCompletion: expect.any(String),
                },
                message: 'Export job started successfully',
                timestamp: expect.any(String),
            });
        });
        it('should check export job status', async () => {
            // First create an export job
            const exportResponse = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/admin/export')
                .set('Authorization', `Bearer ${adminToken}`)
                .send({
                dataType: 'vulnerabilities',
                format: 'csv',
                dateRange: {
                    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
                    end: new Date().toISOString(),
                },
            })
                .expect(202);
            const exportId = exportResponse.body.data.exportId;
            // Check the status
            const statusResponse = await (0, supertest_1.default)(app.getHttpServer())
                .get(`/api/v1/admin/export/${exportId}/status`)
                .set('Authorization', `Bearer ${adminToken}`)
                .expect(200);
            expect(statusResponse.body.data).toEqual({
                exportId: exportId,
                status: expect.stringMatching(/^(processing|completed|failed)$/),
                progress: expect.any(Number),
                createdAt: expect.any(String),
                completedAt: expect.any(String),
                downloadUrl: expect.any(String),
                fileSize: expect.any(Number),
            });
        });
        it('should create system backup', async () => {
            const backupRequest = {
                backupType: 'full',
                includeData: true,
                includeConfiguration: true,
                compression: true,
                encryption: true,
            };
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/admin/backup')
                .set('Authorization', `Bearer ${adminToken}`)
                .send(backupRequest)
                .expect(202);
            expect(response.body).toEqual({
                success: true,
                data: {
                    backupId: expect.any(String),
                    status: 'initiated',
                    backupType: 'full',
                    estimatedSize: expect.any(String),
                    estimatedDuration: expect.any(String),
                },
                message: 'Backup process initiated successfully',
                timestamp: expect.any(String),
            });
        });
    });
    describe('End-to-End Integration Validation', () => {
        it('should validate complete data flow integrity', async () => {
            // Create a security event
            const eventResponse = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/security-events')
                .set('Authorization', `Bearer ${userToken}`)
                .send({
                eventType: 'intrusion_attempt',
                severity: 'critical',
                description: 'Unauthorized access attempt detected',
                sourceIp: '***********',
                targetIp: '**********',
                timestamp: new Date().toISOString(),
            })
                .expect(201);
            const eventId = eventResponse.body.data.id;
            // Verify event appears in analytics
            const analyticsResponse = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/analytics/recent-events')
                .query({ limit: 10 })
                .set('Authorization', `Bearer ${userToken}`)
                .expect(200);
            expect(analyticsResponse.body.data.events).toEqual(expect.arrayContaining([
                expect.objectContaining({
                    id: eventId,
                    eventType: 'intrusion_attempt',
                    severity: 'critical',
                }),
            ]));
            // Verify event triggers automated analysis
            const analysisResponse = await (0, supertest_1.default)(app.getHttpServer())
                .get(`/api/v1/automation/analysis/${eventId}`)
                .set('Authorization', `Bearer ${userToken}`)
                .expect(200);
            expect(analysisResponse.body.data).toEqual(expect.objectContaining({
                eventId: eventId,
                analysisStatus: expect.any(String),
                riskScore: expect.any(Number),
                recommendations: expect.any(Array),
            }));
            // Verify audit trail is created
            const auditResponse = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/admin/audit/security-events')
                .query({ eventId: eventId })
                .set('Authorization', `Bearer ${adminToken}`)
                .expect(200);
            expect(auditResponse.body.data.auditEntries).toEqual(expect.arrayContaining([
                expect.objectContaining({
                    action: 'security_event_created',
                    resourceId: eventId,
                    performedBy: regularUserId,
                    timestamp: expect.any(String),
                }),
            ]));
        });
        it('should validate system resilience under load', async () => {
            // Create multiple concurrent operations
            const operations = [
                // Create security events
                ...Array.from({ length: 5 }, (_, i) => (0, supertest_1.default)(app.getHttpServer())
                    .post('/api/v1/security-events')
                    .set('Authorization', `Bearer ${userToken}`)
                    .send({
                    eventType: 'malware_detected',
                    severity: 'high',
                    description: `Malware detection ${i + 1}`,
                    sourceIp: `192.168.1.${100 + i}`,
                    timestamp: new Date().toISOString(),
                })),
                // Create vulnerabilities
                ...Array.from({ length: 3 }, (_, i) => (0, supertest_1.default)(app.getHttpServer())
                    .post('/api/v1/vulnerability-management/vulnerabilities')
                    .set('Authorization', `Bearer ${userToken}`)
                    .send({
                    cveId: `CVE-2024-${1000 + i}`,
                    title: `Test Vulnerability ${i + 1}`,
                    severity: 'medium',
                    cvssScore: 6.5,
                    status: 'open',
                })),
                // Generate reports
                ...Array.from({ length: 2 }, () => (0, supertest_1.default)(app.getHttpServer())
                    .get('/api/v1/analytics/dashboard')
                    .set('Authorization', `Bearer ${userToken}`)),
            ];
            const responses = await Promise.all(operations.map(op => op.then(res => ({ status: res.status, success: true }))
                .catch(err => ({ status: err.response?.status || 500, success: false }))));
            // Most operations should succeed
            const successCount = responses.filter(r => r.success && [200, 201].includes(r.status)).length;
            const totalOperations = operations.length;
            expect(successCount / totalOperations).toBeGreaterThan(0.8); // 80% success rate
        });
        it('should validate data consistency across services', async () => {
            // Create related data across multiple services
            const eventResponse = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/security-events')
                .set('Authorization', `Bearer ${userToken}`)
                .send({
                eventType: 'data_breach',
                severity: 'critical',
                description: 'Potential data breach detected',
                sourceIp: '************',
                timestamp: new Date().toISOString(),
            })
                .expect(201);
            const eventId = eventResponse.body.data.id;
            const vulnResponse = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/vulnerability-management/vulnerabilities')
                .set('Authorization', `Bearer ${userToken}`)
                .send({
                cveId: 'CVE-2024-9999',
                title: 'Data Exposure Vulnerability',
                severity: 'critical',
                securityEventId: eventId,
                status: 'open',
            })
                .expect(201);
            const vulnId = vulnResponse.body.data.id;
            // Verify cross-references are maintained
            const eventDetailResponse = await (0, supertest_1.default)(app.getHttpServer())
                .get(`/api/v1/security-events/${eventId}`)
                .set('Authorization', `Bearer ${userToken}`)
                .expect(200);
            const vulnDetailResponse = await (0, supertest_1.default)(app.getHttpServer())
                .get(`/api/v1/vulnerability-management/vulnerabilities/${vulnId}`)
                .set('Authorization', `Bearer ${userToken}`)
                .expect(200);
            expect(vulnDetailResponse.body.data.securityEventId).toBe(eventId);
            // Verify analytics reflect the relationships
            const correlationResponse = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/analytics/correlations')
                .query({ securityEventId: eventId })
                .set('Authorization', `Bearer ${userToken}`)
                .expect(200);
            expect(correlationResponse.body.data.correlations).toEqual(expect.arrayContaining([
                expect.objectContaining({
                    securityEventId: eventId,
                    relatedVulnerabilities: expect.arrayContaining([vulnId]),
                }),
            ]));
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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