dbeb5e675ccfba9465d6dd1a89a52250
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const response_action_rolled_back_domain_event_1 = require("../response-action-rolled-back.domain-event");
const shared_kernel_1 = require("../../../../../shared-kernel");
const action_type_enum_1 = require("../../enums/action-type.enum");
describe('ResponseActionRolledBackDomainEvent', () => {
    let aggregateId;
    let eventData;
    let event;
    beforeEach(() => {
        aggregateId = shared_kernel_1.UniqueEntityId.generate();
        eventData = {
            actionType: action_type_enum_1.ActionType.BLOCK_IP,
            rolledBackBy: '<EMAIL>',
            rolledBackAt: new Date(),
            rollbackResults: {
                unblocked: true,
                ruleRemoved: 'rule-123',
                trafficRestored: true,
            },
            originalExecutionResults: {
                blocked: true,
                ruleId: 'rule-123',
                affectedConnections: 5,
            },
        };
        event = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, eventData);
    });
    describe('creation', () => {
        it('should create event with required data', () => {
            expect(event).toBeInstanceOf(response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent);
            expect(event.aggregateId).toBe(aggregateId);
            expect(event.eventData).toBe(eventData);
            expect(event.occurredOn).toBeInstanceOf(Date);
        });
        it('should create event with custom options', () => {
            const customOptions = {
                eventId: shared_kernel_1.UniqueEntityId.generate(),
                occurredOn: new Date('2023-01-01'),
                eventVersion: 2,
                correlationId: 'corr-123',
                causationId: 'cause-456',
                metadata: { source: 'test' },
            };
            const customEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, eventData, customOptions);
            expect(customEvent.eventId).toBe(customOptions.eventId);
            expect(customEvent.occurredOn).toBe(customOptions.occurredOn);
            expect(customEvent.eventVersion).toBe(customOptions.eventVersion);
            expect(customEvent.correlationId).toBe(customOptions.correlationId);
            expect(customEvent.causationId).toBe(customOptions.causationId);
            expect(customEvent.metadata).toEqual(customOptions.metadata);
        });
    });
    describe('getters', () => {
        it('should provide access to event data properties', () => {
            expect(event.actionType).toBe(action_type_enum_1.ActionType.BLOCK_IP);
            expect(event.rolledBackBy).toBe('<EMAIL>');
            expect(event.rolledBackAt).toBe(eventData.rolledBackAt);
            expect(event.rollbackResults).toEqual(eventData.rollbackResults);
            expect(event.originalExecutionResults).toEqual(eventData.originalExecutionResults);
        });
    });
    describe('rollback status analysis', () => {
        it('should identify successful rollbacks', () => {
            expect(event.isRollbackSuccessful()).toBe(true);
        });
        it('should identify failed rollbacks', () => {
            const failedRollbackEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                rollbackResults: {
                    error: 'Failed to remove firewall rule',
                    success: false,
                },
            });
            expect(failedRollbackEvent.isRollbackSuccessful()).toBe(false);
        });
        it('should handle rollbacks without results', () => {
            const noResultsEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                rollbackResults: undefined,
            });
            expect(noResultsEvent.isRollbackSuccessful()).toBe(false);
        });
    });
    describe('rollback type analysis', () => {
        it('should identify automated rollbacks', () => {
            const automatedEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                rolledBackBy: 'system@automation',
            });
            expect(automatedEvent.isAutomatedRollback()).toBe(true);
            expect(automatedEvent.isManualRollback()).toBe(false);
            const botEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                rolledBackBy: 'automation-bot',
            });
            expect(botEvent.isAutomatedRollback()).toBe(true);
        });
        it('should identify manual rollbacks', () => {
            expect(event.isAutomatedRollback()).toBe(false);
            expect(event.isManualRollback()).toBe(true);
        });
    });
    describe('action type classification', () => {
        it('should identify security-critical rollbacks', () => {
            expect(event.isSecurityCriticalRollback()).toBe(true);
            const isolateEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.ISOLATE_SYSTEM,
            });
            expect(isolateEvent.isSecurityCriticalRollback()).toBe(true);
            const shutdownEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.SHUTDOWN_SYSTEM,
            });
            expect(shutdownEvent.isSecurityCriticalRollback()).toBe(true);
            const deleteEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.DELETE_FILE,
            });
            expect(deleteEvent.isSecurityCriticalRollback()).toBe(true);
            const patchEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.PATCH_VULNERABILITY,
            });
            expect(patchEvent.isSecurityCriticalRollback()).toBe(true);
            const emailEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.SEND_EMAIL,
            });
            expect(emailEvent.isSecurityCriticalRollback()).toBe(false);
        });
        it('should identify containment rollbacks', () => {
            expect(event.isContainmentRollback()).toBe(true);
            const quarantineEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.QUARANTINE_FILE,
            });
            expect(quarantineEvent.isContainmentRollback()).toBe(true);
            const blockDomainEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.BLOCK_DOMAIN,
            });
            expect(blockDomainEvent.isContainmentRollback()).toBe(true);
            const disableAccountEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.DISABLE_ACCOUNT,
            });
            expect(disableAccountEvent.isContainmentRollback()).toBe(true);
            const terminateEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.TERMINATE_CONNECTION,
            });
            expect(terminateEvent.isContainmentRollback()).toBe(true);
            const emailEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.SEND_EMAIL,
            });
            expect(emailEvent.isContainmentRollback()).toBe(false);
        });
        it('should identify recovery rollbacks', () => {
            const backupEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.RESTORE_BACKUP,
            });
            expect(backupEvent.isRecoveryRollback()).toBe(true);
            const rebuildEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.REBUILD_SYSTEM,
            });
            expect(rebuildEvent.isRecoveryRollback()).toBe(true);
            const enableEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.ENABLE_SERVICE,
            });
            expect(enableEvent.isRecoveryRollback()).toBe(true);
            const resetEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.RESET_PASSWORD,
            });
            expect(resetEvent.isRecoveryRollback()).toBe(true);
            expect(event.isRecoveryRollback()).toBe(false);
        });
        it('should identify configuration rollbacks', () => {
            const firewallEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.UPDATE_FIREWALL,
            });
            expect(firewallEvent.isConfigurationRollback()).toBe(true);
            const reconfigureEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.RECONFIGURE_SYSTEM,
            });
            expect(reconfigureEvent.isConfigurationRollback()).toBe(true);
            const updateEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.UPDATE_SOFTWARE,
            });
            expect(updateEvent.isConfigurationRollback()).toBe(true);
            expect(event.isConfigurationRollback()).toBe(false);
        });
    });
    describe('rollback impact assessment', () => {
        it('should assess critical impact for security-critical containment rollbacks', () => {
            expect(event.getRollbackImpact()).toBe('critical');
        });
        it('should assess high impact for security-critical non-containment rollbacks', () => {
            const patchEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.PATCH_VULNERABILITY,
            });
            expect(patchEvent.getRollbackImpact()).toBe('high');
        });
        it('should assess high impact for containment rollbacks', () => {
            const quarantineEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.QUARANTINE_FILE,
            });
            expect(quarantineEvent.getRollbackImpact()).toBe('critical');
        });
        it('should assess medium impact for recovery rollbacks', () => {
            const recoveryEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.RESTORE_BACKUP,
            });
            expect(recoveryEvent.getRollbackImpact()).toBe('medium');
        });
        it('should assess medium impact for configuration rollbacks', () => {
            const configEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.UPDATE_FIREWALL,
            });
            expect(configEvent.getRollbackImpact()).toBe('medium');
        });
        it('should assess low impact for other rollbacks', () => {
            const emailEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.SEND_EMAIL,
            });
            expect(emailEvent.getRollbackImpact()).toBe('low');
        });
    });
    describe('security implications', () => {
        it('should identify implications for containment rollbacks', () => {
            const implications = event.getSecurityImplications();
            expect(implications).toContain('Containment measures removed - systems may be exposed');
            expect(implications).toContain('Threat may regain access to previously contained resources');
            expect(implications).toContain('Increased risk exposure until alternative measures implemented');
        });
        it('should identify implications for IP blocking rollbacks', () => {
            const implications = event.getSecurityImplications();
            expect(implications).toContain('IP address unblocked - traffic from this IP now allowed');
            expect(implications).toContain('Monitor for suspicious activity from unblocked IP');
        });
        it('should identify implications for account disabling rollbacks', () => {
            const accountEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.DISABLE_ACCOUNT,
            });
            const implications = accountEvent.getSecurityImplications();
            expect(implications).toContain('User account re-enabled - access restored');
            expect(implications).toContain('Monitor account activity for suspicious behavior');
        });
        it('should identify implications for file quarantine rollbacks', () => {
            const quarantineEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.QUARANTINE_FILE,
            });
            const implications = quarantineEvent.getSecurityImplications();
            expect(implications).toContain('File removed from quarantine - file now accessible');
            expect(implications).toContain('Ensure file is safe before allowing access');
        });
        it('should identify implications for firewall rollbacks', () => {
            const firewallEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.UPDATE_FIREWALL,
            });
            const implications = firewallEvent.getSecurityImplications();
            expect(implications).toContain('Firewall rules reverted - network access patterns changed');
            expect(implications).toContain('Review network traffic for unexpected patterns');
        });
        it('should identify implications for vulnerability patch rollbacks', () => {
            const patchEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.PATCH_VULNERABILITY,
            });
            const implications = patchEvent.getSecurityImplications();
            expect(implications).toContain('Vulnerability patch removed - system may be vulnerable again');
            expect(implications).toContain('Implement alternative protection measures');
        });
    });
    describe('post-rollback recommendations', () => {
        it('should recommend general post-rollback actions', () => {
            const actions = event.getRecommendedPostActions();
            expect(actions).toContain('Validate rollback completion');
            expect(actions).toContain('Document rollback rationale');
        });
        it('should recommend actions for security-critical rollbacks', () => {
            const actions = event.getRecommendedPostActions();
            expect(actions).toContain('Assess security posture after rollback');
            expect(actions).toContain('Implement alternative security measures if needed');
            expect(actions).toContain('Monitor for security incidents');
        });
        it('should recommend actions for containment rollbacks', () => {
            const actions = event.getRecommendedPostActions();
            expect(actions).toContain('Implement alternative containment measures');
            expect(actions).toContain('Monitor for threat activity');
            expect(actions).toContain('Assess threat landscape changes');
        });
        it('should recommend actions for recovery rollbacks', () => {
            const recoveryEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.RESTORE_BACKUP,
            });
            const actions = recoveryEvent.getRecommendedPostActions();
            expect(actions).toContain('Verify system functionality after rollback');
            expect(actions).toContain('Check service availability');
            expect(actions).toContain('Consider alternative recovery approaches');
        });
        it('should recommend actions for configuration rollbacks', () => {
            const configEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.UPDATE_FIREWALL,
            });
            const actions = configEvent.getRecommendedPostActions();
            expect(actions).toContain('Validate configuration state');
            expect(actions).toContain('Test system functionality');
            expect(actions).toContain('Update configuration documentation');
        });
        it('should recommend action-specific post-rollback actions for IP blocking', () => {
            const actions = event.getRecommendedPostActions();
            expect(actions).toContain('Monitor traffic from unblocked IP address');
            expect(actions).toContain('Consider alternative blocking methods');
        });
        it('should recommend action-specific post-rollback actions for account disabling', () => {
            const accountEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.DISABLE_ACCOUNT,
            });
            const actions = accountEvent.getRecommendedPostActions();
            expect(actions).toContain('Monitor re-enabled account activity');
            expect(actions).toContain('Review account permissions');
        });
        it('should recommend action-specific post-rollback actions for file quarantine', () => {
            const quarantineEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.QUARANTINE_FILE,
            });
            const actions = quarantineEvent.getRecommendedPostActions();
            expect(actions).toContain('Re-scan file for threats');
            expect(actions).toContain('Monitor file access patterns');
        });
        it('should recommend action-specific post-rollback actions for system isolation', () => {
            const isolateEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.ISOLATE_SYSTEM,
            });
            const actions = isolateEvent.getRecommendedPostActions();
            expect(actions).toContain('Monitor system for suspicious activity');
            expect(actions).toContain('Implement network monitoring');
        });
    });
    describe('notification targets', () => {
        it('should identify basic notification targets', () => {
            const targets = event.getNotificationTargets();
            expect(targets).toContain('rollback-requestor');
            expect(targets).toContain('original-action-requestor');
        });
        it('should identify targets for security-critical rollbacks', () => {
            const targets = event.getNotificationTargets();
            expect(targets).toContain('security-team');
            expect(targets).toContain('incident-response-team');
        });
        it('should identify targets for containment rollbacks', () => {
            const targets = event.getNotificationTargets();
            expect(targets).toContain('security-managers');
            expect(targets).toContain('containment-specialists');
            expect(targets).toContain('threat-analysts');
        });
        it('should identify targets for recovery rollbacks', () => {
            const recoveryEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.RESTORE_BACKUP,
            });
            const targets = recoveryEvent.getNotificationTargets();
            expect(targets).toContain('service-owners');
            expect(targets).toContain('operations-team');
        });
        it('should identify targets for configuration rollbacks', () => {
            const configEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.UPDATE_FIREWALL,
            });
            const targets = configEvent.getNotificationTargets();
            expect(targets).toContain('system-administrators');
            expect(targets).toContain('configuration-managers');
        });
    });
    describe('compliance considerations', () => {
        it('should provide general compliance considerations', () => {
            const considerations = event.getComplianceConsiderations();
            expect(considerations).toContain('Document rollback decision and rationale');
            expect(considerations).toContain('Maintain audit trail of rollback process');
        });
        it('should provide considerations for security-critical rollbacks', () => {
            const considerations = event.getComplianceConsiderations();
            expect(considerations).toContain('Ensure rollback approval was properly obtained');
            expect(considerations).toContain('Document security impact assessment');
        });
        it('should provide considerations for containment rollbacks', () => {
            const considerations = event.getComplianceConsiderations();
            expect(considerations).toContain('Document risk acceptance for containment removal');
            expect(considerations).toContain('Ensure alternative controls are in place');
        });
        it('should provide considerations for file deletion rollbacks', () => {
            const deleteEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.DELETE_FILE,
            });
            const considerations = deleteEvent.getComplianceConsiderations();
            expect(considerations).toContain('Ensure data recovery complies with retention policies');
            expect(considerations).toContain('Document data restoration rationale');
        });
    });
    describe('monitoring requirements', () => {
        it('should provide monitoring requirements for security-critical rollbacks', () => {
            const monitoring = event.getMonitoringRequirements();
            expect(monitoring.duration).toBe('long');
            expect(monitoring.intensity).toBe('intensive');
            expect(monitoring.focus).toContain('security-events');
            expect(monitoring.focus).toContain('threat-indicators');
        });
        it('should provide monitoring requirements for containment rollbacks', () => {
            const monitoring = event.getMonitoringRequirements();
            expect(monitoring.duration).toBe('ongoing');
            expect(monitoring.intensity).toBe('intensive');
            expect(monitoring.focus).toContain('threat-activity');
            expect(monitoring.focus).toContain('network-traffic');
            expect(monitoring.focus).toContain('system-access');
        });
        it('should provide monitoring requirements for recovery rollbacks', () => {
            const recoveryEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.RESTORE_BACKUP,
            });
            const monitoring = recoveryEvent.getMonitoringRequirements();
            expect(monitoring.duration).toBe('medium');
            expect(monitoring.intensity).toBe('enhanced');
            expect(monitoring.focus).toContain('system-performance');
            expect(monitoring.focus).toContain('service-availability');
        });
        it('should provide monitoring requirements for configuration rollbacks', () => {
            const configEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.UPDATE_FIREWALL,
            });
            const monitoring = configEvent.getMonitoringRequirements();
            expect(monitoring.duration).toBe('medium');
            expect(monitoring.intensity).toBe('enhanced');
            expect(monitoring.focus).toContain('configuration-drift');
            expect(monitoring.focus).toContain('system-behavior');
        });
        it('should provide basic monitoring requirements for low-impact rollbacks', () => {
            const emailEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.SEND_EMAIL,
            });
            const monitoring = emailEvent.getMonitoringRequirements();
            expect(monitoring.duration).toBe('short');
            expect(monitoring.intensity).toBe('basic');
        });
    });
    describe('rollback metrics', () => {
        it('should generate comprehensive rollback metrics', () => {
            const metrics = event.getRollbackMetrics();
            expect(metrics.actionType).toBe(action_type_enum_1.ActionType.BLOCK_IP);
            expect(metrics.rollbackImpact).toBe('critical');
            expect(metrics.isSecurityCritical).toBe(true);
            expect(metrics.isAutomated).toBe(false);
            expect(metrics.isSuccessful).toBe(true);
            expect(metrics.securityImplicationsCount).toBeGreaterThan(0);
        });
        it('should generate metrics for failed rollbacks', () => {
            const failedEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                rollbackResults: { error: 'Rollback failed' },
                rolledBackBy: 'system@automation',
            });
            const metrics = failedEvent.getRollbackMetrics();
            expect(metrics.isSuccessful).toBe(false);
            expect(metrics.isAutomated).toBe(true);
        });
    });
    describe('integration event conversion', () => {
        it('should convert to integration event format', () => {
            const integrationEvent = event.toIntegrationEvent();
            expect(integrationEvent.eventType).toBe('ResponseActionRolledBack');
            expect(integrationEvent.action).toBe('response_action_rolled_back');
            expect(integrationEvent.resource).toBe('ResponseAction');
            expect(integrationEvent.resourceId).toBe(aggregateId.toString());
            expect(integrationEvent.data).toBe(eventData);
            expect(integrationEvent.metadata).toEqual({
                rollbackImpact: 'critical',
                isSecurityCritical: true,
                isContainmentRollback: true,
                isSuccessful: true,
                requiresMonitoring: true,
            });
        });
    });
    describe('edge cases', () => {
        it('should handle events without rollback results', () => {
            const noResultsEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                rollbackResults: undefined,
            });
            expect(noResultsEvent.rollbackResults).toBeUndefined();
            expect(noResultsEvent.isRollbackSuccessful()).toBe(false);
        });
        it('should handle events without original execution results', () => {
            const noOriginalEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                originalExecutionResults: undefined,
            });
            expect(noOriginalEvent.originalExecutionResults).toBeUndefined();
            expect(noOriginalEvent.getSecurityImplications()).toContain('Containment measures removed - systems may be exposed');
        });
        it('should handle unknown action types gracefully', () => {
            const unknownEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: 'UNKNOWN_ACTION',
            });
            expect(unknownEvent.isSecurityCriticalRollback()).toBe(false);
            expect(unknownEvent.isContainmentRollback()).toBe(false);
            expect(unknownEvent.isRecoveryRollback()).toBe(false);
            expect(unknownEvent.isConfigurationRollback()).toBe(false);
            expect(unknownEvent.getRollbackImpact()).toBe('low');
        });
        it('should handle rollback results with error property', () => {
            const errorEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                rollbackResults: {
                    error: 'Partial rollback failure',
                    partialSuccess: true,
                },
            });
            expect(errorEvent.isRollbackSuccessful()).toBe(false);
        });
        it('should handle empty security implications gracefully', () => {
            const emailEvent = new response_action_rolled_back_domain_event_1.ResponseActionRolledBackDomainEvent(aggregateId, {
                ...eventData,
                actionType: action_type_enum_1.ActionType.SEND_EMAIL,
            });
            const implications = emailEvent.getSecurityImplications();
            expect(Array.isArray(implications)).toBe(true);
            expect(emailEvent.getRollbackMetrics().securityImplicationsCount).toBe(implications.length);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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