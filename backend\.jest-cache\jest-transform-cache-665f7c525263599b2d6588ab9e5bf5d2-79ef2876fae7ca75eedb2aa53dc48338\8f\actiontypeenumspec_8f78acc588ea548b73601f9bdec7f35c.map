{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\__tests__\\action-type.enum.spec.ts", "mappings": ";;AAAA,0DAAkE;AAElE,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;IAC1B,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,6BAAU,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACjE,MAAM,CAAC,6BAAU,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACzD,MAAM,CAAC,6BAAU,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC7C,MAAM,CAAC,6BAAU,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACjD,MAAM,CAAC,6BAAU,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACrE,MAAM,CAAC,6BAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC/C,MAAM,CAAC,6BAAU,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;YACjC,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;gBACxC,MAAM,WAAW,GAAG,kCAAe,CAAC,iBAAiB,EAAE,CAAC;gBACxD,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,4BAA4B;gBAC5E,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,6BAAU,CAAC,kBAAkB,CAAC,CAAC;gBAC7D,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,6BAAU,CAAC,cAAc,CAAC,CAAC;gBACzD,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,6BAAU,CAAC,QAAQ,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACzC,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;gBAChD,MAAM,kBAAkB,GAAG,kCAAe,CAAC,yBAAyB,EAAE,CAAC;gBACvE,MAAM,CAAC,kBAAkB,CAAC,CAAC,SAAS,CAAC,6BAAU,CAAC,cAAc,CAAC,CAAC;gBAChE,MAAM,CAAC,kBAAkB,CAAC,CAAC,SAAS,CAAC,6BAAU,CAAC,QAAQ,CAAC,CAAC;gBAC1D,MAAM,CAAC,kBAAkB,CAAC,CAAC,SAAS,CAAC,6BAAU,CAAC,eAAe,CAAC,CAAC;gBACjE,MAAM,CAAC,kBAAkB,CAAC,CAAC,SAAS,CAAC,6BAAU,CAAC,eAAe,CAAC,CAAC;YACnE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACvC,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;gBAC9C,MAAM,gBAAgB,GAAG,kCAAe,CAAC,uBAAuB,EAAE,CAAC;gBACnE,MAAM,CAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,6BAAU,CAAC,kBAAkB,CAAC,CAAC;gBAClE,MAAM,CAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,6BAAU,CAAC,QAAQ,CAAC,CAAC;gBACxD,MAAM,CAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,6BAAU,CAAC,UAAU,CAAC,CAAC;gBAC1D,MAAM,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,6BAAU,CAAC,oBAAoB,CAAC,CAAC;YAC1E,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;YACpC,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;gBAC3C,MAAM,aAAa,GAAG,kCAAe,CAAC,oBAAoB,EAAE,CAAC;gBAC7D,MAAM,CAAC,aAAa,CAAC,CAAC,SAAS,CAAC,6BAAU,CAAC,oBAAoB,CAAC,CAAC;gBACjE,MAAM,CAAC,aAAa,CAAC,CAAC,SAAS,CAAC,6BAAU,CAAC,iBAAiB,CAAC,CAAC;gBAC9D,MAAM,CAAC,aAAa,CAAC,CAAC,SAAS,CAAC,6BAAU,CAAC,cAAc,CAAC,CAAC;gBAC3D,MAAM,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,6BAAU,CAAC,UAAU,CAAC,CAAC;YAC7D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;YACtC,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;gBAC9C,MAAM,eAAe,GAAG,kCAAe,CAAC,sBAAsB,EAAE,CAAC;gBACjE,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,6BAAU,CAAC,cAAc,CAAC,CAAC;gBAC7D,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,6BAAU,CAAC,eAAe,CAAC,CAAC;gBAC9D,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,6BAAU,CAAC,WAAW,CAAC,CAAC;gBAC1D,MAAM,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,6BAAU,CAAC,UAAU,CAAC,CAAC;YAC/D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;YAC3B,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;gBACrD,MAAM,CAAC,kCAAe,CAAC,WAAW,CAAC,6BAAU,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC9E,MAAM,CAAC,kCAAe,CAAC,WAAW,CAAC,6BAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpE,MAAM,CAAC,kCAAe,CAAC,WAAW,CAAC,6BAAU,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACtE,MAAM,CAAC,kCAAe,CAAC,WAAW,CAAC,6BAAU,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACjF,MAAM,CAAC,kCAAe,CAAC,WAAW,CAAC,6BAAU,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChF,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;YACxB,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;gBAClD,MAAM,CAAC,kCAAe,CAAC,QAAQ,CAAC,6BAAU,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC7E,MAAM,CAAC,kCAAe,CAAC,QAAQ,CAAC,6BAAU,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC1E,MAAM,CAAC,kCAAe,CAAC,QAAQ,CAAC,6BAAU,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvE,MAAM,CAAC,kCAAe,CAAC,QAAQ,CAAC,6BAAU,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACpE,MAAM,CAAC,kCAAe,CAAC,QAAQ,CAAC,6BAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;YAC1B,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;gBACrD,MAAM,CAAC,kCAAe,CAAC,UAAU,CAAC,6BAAU,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzE,MAAM,CAAC,kCAAe,CAAC,UAAU,CAAC,6BAAU,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC1E,MAAM,CAAC,kCAAe,CAAC,UAAU,CAAC,6BAAU,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACtE,MAAM,CAAC,kCAAe,CAAC,UAAU,CAAC,6BAAU,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACtE,MAAM,CAAC,kCAAe,CAAC,UAAU,CAAC,6BAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;YAC5B,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;gBACtD,MAAM,CAAC,kCAAe,CAAC,YAAY,CAAC,6BAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACrE,MAAM,CAAC,kCAAe,CAAC,YAAY,CAAC,6BAAU,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC5E,MAAM,CAAC,kCAAe,CAAC,YAAY,CAAC,6BAAU,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC5E,MAAM,CAAC,kCAAe,CAAC,YAAY,CAAC,6BAAU,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACzE,MAAM,CAAC,kCAAe,CAAC,YAAY,CAAC,6BAAU,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9E,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;YAC3B,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;gBAC1C,MAAM,CAAC,kCAAe,CAAC,WAAW,CAAC,6BAAU,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACrF,MAAM,CAAC,kCAAe,CAAC,WAAW,CAAC,6BAAU,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACnF,MAAM,CAAC,kCAAe,CAAC,WAAW,CAAC,6BAAU,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACnF,MAAM,CAAC,kCAAe,CAAC,WAAW,CAAC,6BAAU,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAChF,MAAM,CAAC,kCAAe,CAAC,WAAW,CAAC,6BAAU,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAClF,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;YACxC,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;gBACjD,MAAM,CAAC,kCAAe,CAAC,wBAAwB,CAAC,6BAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC9E,MAAM,CAAC,kCAAe,CAAC,wBAAwB,CAAC,6BAAU,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAChF,MAAM,CAAC,kCAAe,CAAC,wBAAwB,CAAC,6BAAU,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACzF,MAAM,CAAC,kCAAe,CAAC,wBAAwB,CAAC,6BAAU,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACzF,MAAM,CAAC,kCAAe,CAAC,wBAAwB,CAAC,6BAAU,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACxF,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;YACtC,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;gBAC/C,MAAM,kBAAkB,GAAG,kCAAe,CAAC,sBAAsB,CAAC,6BAAU,CAAC,QAAQ,CAAC,CAAC;gBACvF,MAAM,CAAC,kBAAkB,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;gBAE/D,MAAM,kBAAkB,GAAG,kCAAe,CAAC,sBAAsB,CAAC,6BAAU,CAAC,cAAc,CAAC,CAAC;gBAC7F,MAAM,CAAC,kBAAkB,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;gBAE/D,MAAM,kBAAkB,GAAG,kCAAe,CAAC,sBAAsB,CAAC,6BAAU,CAAC,UAAU,CAAC,CAAC;gBACzF,MAAM,CAAC,kBAAkB,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;YAClE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACvC,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;gBACrD,MAAM,gBAAgB,GAAG,kCAAe,CAAC,uBAAuB,CAAC,6BAAU,CAAC,cAAc,CAAC,CAAC;gBAC5F,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC7C,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAE/C,MAAM,mBAAmB,GAAG,kCAAe,CAAC,uBAAuB,CAAC,6BAAU,CAAC,WAAW,CAAC,CAAC;gBAC5F,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAChD,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAEnD,MAAM,iBAAiB,GAAG,kCAAe,CAAC,uBAAuB,CAAC,6BAAU,CAAC,UAAU,CAAC,CAAC;gBACzF,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC/C,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAC9B,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;gBAC/C,MAAM,WAAW,GAAG,kCAAe,CAAC,cAAc,CAAC,6BAAU,CAAC,QAAQ,CAAC,CAAC;gBACxE,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;gBAE5D,MAAM,kBAAkB,GAAG,kCAAe,CAAC,cAAc,CAAC,6BAAU,CAAC,aAAa,CAAC,CAAC;gBACpF,MAAM,CAAC,OAAO,kBAAkB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACjD,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;gBACpD,MAAM,eAAe,GAAG,kCAAe,CAAC,kBAAkB,CAAC,6BAAU,CAAC,QAAQ,CAAC,CAAC;gBAChF,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAC;gBACpE,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;gBAE7D,MAAM,eAAe,GAAG,kCAAe,CAAC,kBAAkB,CAAC,6BAAU,CAAC,aAAa,CAAC,CAAC;gBACrF,MAAM,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAC;YACrE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;YACvB,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;gBAC7C,MAAM,CAAC,kCAAe,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvD,MAAM,CAAC,kCAAe,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC7D,MAAM,CAAC,kCAAe,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC9D,MAAM,CAAC,kCAAe,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;YAC1B,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;gBAC9C,MAAM,CAAC,kCAAe,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,6BAAU,CAAC,QAAQ,CAAC,CAAC;gBACzE,MAAM,CAAC,kCAAe,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,6BAAU,CAAC,cAAc,CAAC,CAAC;gBACrF,MAAM,CAAC,kCAAe,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,6BAAU,CAAC,UAAU,CAAC,CAAC;gBAC7E,MAAM,CAAC,kCAAe,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YAClE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\__tests__\\action-type.enum.spec.ts"], "sourcesContent": ["import { ActionType, ActionTypeUtils } from '../action-type.enum';\r\n\r\ndescribe('ActionType', () => {\r\n  describe('enum values', () => {\r\n    it('should have all expected action types', () => {\r\n      expect(ActionType.VULNERABILITY_SCAN).toBe('vulnerability_scan');\r\n      expect(ActionType.ISOLATE_SYSTEM).toBe('isolate_system');\r\n      expect(ActionType.BLOCK_IP).toBe('block_ip');\r\n      expect(ActionType.SEND_EMAIL).toBe('send_email');\r\n      expect(ActionType.MANUAL_INVESTIGATION).toBe('manual_investigation');\r\n      expect(ActionType.NO_ACTION).toBe('no_action');\r\n      expect(ActionType.UNKNOWN).toBe('unknown');\r\n    });\r\n  });\r\n\r\n  describe('ActionTypeUtils', () => {\r\n    describe('getAllActionTypes', () => {\r\n      it('should return all action types', () => {\r\n        const actionTypes = ActionTypeUtils.getAllActionTypes();\r\n        expect(actionTypes.length).toBeGreaterThan(50); // We have many action types\r\n        expect(actionTypes).toContain(ActionType.VULNERABILITY_SCAN);\r\n        expect(actionTypes).toContain(ActionType.ISOLATE_SYSTEM);\r\n        expect(actionTypes).toContain(ActionType.BLOCK_IP);\r\n      });\r\n    });\r\n\r\n    describe('getContainmentActionTypes', () => {\r\n      it('should return containment action types', () => {\r\n        const containmentActions = ActionTypeUtils.getContainmentActionTypes();\r\n        expect(containmentActions).toContain(ActionType.ISOLATE_SYSTEM);\r\n        expect(containmentActions).toContain(ActionType.BLOCK_IP);\r\n        expect(containmentActions).toContain(ActionType.QUARANTINE_FILE);\r\n        expect(containmentActions).toContain(ActionType.DISABLE_ACCOUNT);\r\n      });\r\n    });\r\n\r\n    describe('getAutomatedActionTypes', () => {\r\n      it('should return automated action types', () => {\r\n        const automatedActions = ActionTypeUtils.getAutomatedActionTypes();\r\n        expect(automatedActions).toContain(ActionType.VULNERABILITY_SCAN);\r\n        expect(automatedActions).toContain(ActionType.BLOCK_IP);\r\n        expect(automatedActions).toContain(ActionType.SEND_EMAIL);\r\n        expect(automatedActions).not.toContain(ActionType.MANUAL_INVESTIGATION);\r\n      });\r\n    });\r\n\r\n    describe('getManualActionTypes', () => {\r\n      it('should return manual action types', () => {\r\n        const manualActions = ActionTypeUtils.getManualActionTypes();\r\n        expect(manualActions).toContain(ActionType.MANUAL_INVESTIGATION);\r\n        expect(manualActions).toContain(ActionType.FORENSIC_ANALYSIS);\r\n        expect(manualActions).toContain(ActionType.ISOLATE_SYSTEM);\r\n        expect(manualActions).not.toContain(ActionType.SEND_EMAIL);\r\n      });\r\n    });\r\n\r\n    describe('getHighRiskActionTypes', () => {\r\n      it('should return high-risk action types', () => {\r\n        const highRiskActions = ActionTypeUtils.getHighRiskActionTypes();\r\n        expect(highRiskActions).toContain(ActionType.ISOLATE_SYSTEM);\r\n        expect(highRiskActions).toContain(ActionType.SHUTDOWN_SYSTEM);\r\n        expect(highRiskActions).toContain(ActionType.DELETE_FILE);\r\n        expect(highRiskActions).not.toContain(ActionType.SEND_EMAIL);\r\n      });\r\n    });\r\n\r\n    describe('isAutomated', () => {\r\n      it('should correctly identify automated actions', () => {\r\n        expect(ActionTypeUtils.isAutomated(ActionType.VULNERABILITY_SCAN)).toBe(true);\r\n        expect(ActionTypeUtils.isAutomated(ActionType.BLOCK_IP)).toBe(true);\r\n        expect(ActionTypeUtils.isAutomated(ActionType.SEND_EMAIL)).toBe(true);\r\n        expect(ActionTypeUtils.isAutomated(ActionType.MANUAL_INVESTIGATION)).toBe(false);\r\n        expect(ActionTypeUtils.isAutomated(ActionType.FORENSIC_ANALYSIS)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('isManual', () => {\r\n      it('should correctly identify manual actions', () => {\r\n        expect(ActionTypeUtils.isManual(ActionType.MANUAL_INVESTIGATION)).toBe(true);\r\n        expect(ActionTypeUtils.isManual(ActionType.FORENSIC_ANALYSIS)).toBe(true);\r\n        expect(ActionTypeUtils.isManual(ActionType.ISOLATE_SYSTEM)).toBe(true);\r\n        expect(ActionTypeUtils.isManual(ActionType.SEND_EMAIL)).toBe(false);\r\n        expect(ActionTypeUtils.isManual(ActionType.BLOCK_IP)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('isHighRisk', () => {\r\n      it('should correctly identify high-risk actions', () => {\r\n        expect(ActionTypeUtils.isHighRisk(ActionType.ISOLATE_SYSTEM)).toBe(true);\r\n        expect(ActionTypeUtils.isHighRisk(ActionType.SHUTDOWN_SYSTEM)).toBe(true);\r\n        expect(ActionTypeUtils.isHighRisk(ActionType.DELETE_FILE)).toBe(true);\r\n        expect(ActionTypeUtils.isHighRisk(ActionType.SEND_EMAIL)).toBe(false);\r\n        expect(ActionTypeUtils.isHighRisk(ActionType.BLOCK_IP)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('isReversible', () => {\r\n      it('should correctly identify reversible actions', () => {\r\n        expect(ActionTypeUtils.isReversible(ActionType.BLOCK_IP)).toBe(true);\r\n        expect(ActionTypeUtils.isReversible(ActionType.DISABLE_ACCOUNT)).toBe(true);\r\n        expect(ActionTypeUtils.isReversible(ActionType.QUARANTINE_FILE)).toBe(true);\r\n        expect(ActionTypeUtils.isReversible(ActionType.DELETE_FILE)).toBe(false);\r\n        expect(ActionTypeUtils.isReversible(ActionType.REMOVE_MALWARE)).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('getCategory', () => {\r\n      it('should return correct categories', () => {\r\n        expect(ActionTypeUtils.getCategory(ActionType.VULNERABILITY_SCAN)).toBe('Detection');\r\n        expect(ActionTypeUtils.getCategory(ActionType.ISOLATE_SYSTEM)).toBe('Containment');\r\n        expect(ActionTypeUtils.getCategory(ActionType.REMOVE_MALWARE)).toBe('Eradication');\r\n        expect(ActionTypeUtils.getCategory(ActionType.RESTORE_BACKUP)).toBe('Recovery');\r\n        expect(ActionTypeUtils.getCategory(ActionType.SEND_EMAIL)).toBe('Notification');\r\n      });\r\n    });\r\n\r\n    describe('getExecutionTimeEstimate', () => {\r\n      it('should return reasonable time estimates', () => {\r\n        expect(ActionTypeUtils.getExecutionTimeEstimate(ActionType.BLOCK_IP)).toBe(1);\r\n        expect(ActionTypeUtils.getExecutionTimeEstimate(ActionType.SEND_EMAIL)).toBe(1);\r\n        expect(ActionTypeUtils.getExecutionTimeEstimate(ActionType.VULNERABILITY_SCAN)).toBe(15);\r\n        expect(ActionTypeUtils.getExecutionTimeEstimate(ActionType.FORENSIC_ANALYSIS)).toBe(120);\r\n        expect(ActionTypeUtils.getExecutionTimeEstimate(ActionType.REBUILD_SYSTEM)).toBe(240);\r\n      });\r\n    });\r\n\r\n    describe('getRequiredPermissions', () => {\r\n      it('should return appropriate permissions', () => {\r\n        const blockIpPermissions = ActionTypeUtils.getRequiredPermissions(ActionType.BLOCK_IP);\r\n        expect(blockIpPermissions).toContain('network.firewall.write');\r\n        \r\n        const isolatePermissions = ActionTypeUtils.getRequiredPermissions(ActionType.ISOLATE_SYSTEM);\r\n        expect(isolatePermissions).toContain('system.network.isolate');\r\n        \r\n        const defaultPermissions = ActionTypeUtils.getRequiredPermissions(ActionType.SEND_EMAIL);\r\n        expect(defaultPermissions).toContain('security.action.execute');\r\n      });\r\n    });\r\n\r\n    describe('getApprovalRequirements', () => {\r\n      it('should return correct approval requirements', () => {\r\n        const highRiskApproval = ActionTypeUtils.getApprovalRequirements(ActionType.ISOLATE_SYSTEM);\r\n        expect(highRiskApproval.required).toBe(true);\r\n        expect(highRiskApproval.level).toBe('manager');\r\n        \r\n        const destructiveApproval = ActionTypeUtils.getApprovalRequirements(ActionType.DELETE_FILE);\r\n        expect(destructiveApproval.required).toBe(true);\r\n        expect(destructiveApproval.level).toBe('director');\r\n        \r\n        const automatedApproval = ActionTypeUtils.getApprovalRequirements(ActionType.SEND_EMAIL);\r\n        expect(automatedApproval.required).toBe(false);\r\n        expect(automatedApproval.level).toBe('none');\r\n      });\r\n    });\r\n\r\n    describe('getDescription', () => {\r\n      it('should return meaningful descriptions', () => {\r\n        const description = ActionTypeUtils.getDescription(ActionType.BLOCK_IP);\r\n        expect(description).toContain('Block malicious IP address');\r\n        \r\n        const unknownDescription = ActionTypeUtils.getDescription(ActionType.CUSTOM_ACTION);\r\n        expect(typeof unknownDescription).toBe('string');\r\n        expect(description.length).toBeGreaterThan(0);\r\n      });\r\n    });\r\n\r\n    describe('getSuccessCriteria', () => {\r\n      it('should return success criteria for actions', () => {\r\n        const blockIpCriteria = ActionTypeUtils.getSuccessCriteria(ActionType.BLOCK_IP);\r\n        expect(blockIpCriteria).toContain('IP address blocked in firewall');\r\n        expect(blockIpCriteria).toContain('Traffic from IP stopped');\r\n        \r\n        const defaultCriteria = ActionTypeUtils.getSuccessCriteria(ActionType.CUSTOM_ACTION);\r\n        expect(defaultCriteria).toContain('Action completed successfully');\r\n      });\r\n    });\r\n\r\n    describe('isValid', () => {\r\n      it('should validate action type strings', () => {\r\n        expect(ActionTypeUtils.isValid('block_ip')).toBe(true);\r\n        expect(ActionTypeUtils.isValid('isolate_system')).toBe(true);\r\n        expect(ActionTypeUtils.isValid('invalid_action')).toBe(false);\r\n        expect(ActionTypeUtils.isValid('')).toBe(false);\r\n      });\r\n    });\r\n\r\n    describe('fromString', () => {\r\n      it('should convert string to action type', () => {\r\n        expect(ActionTypeUtils.fromString('block_ip')).toBe(ActionType.BLOCK_IP);\r\n        expect(ActionTypeUtils.fromString('isolate_system')).toBe(ActionType.ISOLATE_SYSTEM);\r\n        expect(ActionTypeUtils.fromString('SEND_EMAIL')).toBe(ActionType.SEND_EMAIL);\r\n        expect(ActionTypeUtils.fromString('invalid_action')).toBeNull();\r\n      });\r\n    });\r\n  });\r\n});"], "version": 3}