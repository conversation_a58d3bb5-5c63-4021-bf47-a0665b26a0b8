937121a135aa7bb8b66c46d5c2321690
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const database_service_1 = require("./database.service");
const connection_factory_1 = require("./connection.factory");
const transaction_manager_1 = require("./transaction.manager");
const seed_runner_service_1 = require("./seeds/seed-runner.service");
/**
 * Database module that provides TypeORM configuration and database services
 * Supports multiple database connections and transaction management
 *
 * Features:
 * - Environment-specific database configuration
 * - Connection pooling and management
 * - Transaction management with rollback support
 * - Database health monitoring
 * - Migration and seeding support
 *
 * @module DatabaseModule
 */
let DatabaseModule = class DatabaseModule {
    constructor(databaseService) {
        this.databaseService = databaseService;
    }
    /**
     * Initialize database module
     * Performs health checks and initial setup
     */
    async onModuleInit() {
        try {
            await this.databaseService.checkConnection();
            console.log('✅ Database connection established successfully');
        }
        catch (error) {
            console.error('❌ Failed to establish database connection:', error instanceof Error ? error.message : String(error));
            throw error;
        }
    }
    /**
     * Cleanup database connections on module destroy
     */
    async onModuleDestroy() {
        try {
            await this.databaseService.closeConnections();
            console.log('✅ Database connections closed successfully');
        }
        catch (error) {
            console.error('❌ Error closing database connections:', error instanceof Error ? error.message : String(error));
        }
    }
};
exports.DatabaseModule = DatabaseModule;
exports.DatabaseModule = DatabaseModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forRootAsync({
                imports: [config_1.ConfigModule],
                inject: [config_1.ConfigService],
                useFactory: async (configService) => {
                    const databaseConfig = configService.get('database');
                    return {
                        ...databaseConfig,
                        // Additional runtime configuration
                        autoLoadEntities: true,
                        retryAttempts: 3,
                        retryDelay: 3000,
                        // Connection pool configuration
                        extra: {
                            ...databaseConfig.extra,
                            // Ensure connection pool is properly configured
                            max: databaseConfig.extra?.max || 100,
                            min: 5,
                            acquire: 30000,
                            idle: 10000,
                            evict: 1000,
                            handleDisconnects: true,
                        },
                        // Logging configuration based on environment
                        logging: process.env.NODE_ENV === 'development' ?
                            ['query', 'error', 'warn', 'info', 'log'] :
                            ['error', 'warn'],
                        // Performance optimizations
                        cache: process.env.NODE_ENV === 'production' ? {
                            type: 'redis',
                            options: {
                                host: process.env.REDIS_HOST || 'localhost',
                                port: parseInt(process.env.REDIS_PORT, 10) || 6379,
                                password: process.env.REDIS_PASSWORD || undefined,
                                db: parseInt(process.env.REDIS_DB, 10) || 1,
                            },
                            duration: 30000, // 30 seconds
                        } : false,
                        // Migration configuration
                        migrationsRun: process.env.NODE_ENV !== 'test',
                        dropSchema: process.env.NODE_ENV === 'test',
                        // Entity configuration
                        entities: [
                            __dirname + '/../../**/*.entity{.ts,.js}',
                            __dirname + '/../../modules/**/domain/entities/**/*.entity{.ts,.js}',
                        ],
                        // Migration configuration
                        migrations: [
                            __dirname + '/migrations/*{.ts,.js}',
                        ],
                        // Subscriber configuration
                        subscribers: [
                            __dirname + '/subscribers/*{.ts,.js}',
                        ],
                    };
                },
            }),
        ],
        providers: [
            database_service_1.DatabaseService,
            connection_factory_1.ConnectionFactory,
            transaction_manager_1.TransactionManager,
            seed_runner_service_1.SeedRunnerService,
        ],
        exports: [
            typeorm_1.TypeOrmModule,
            database_service_1.DatabaseService,
            connection_factory_1.ConnectionFactory,
            transaction_manager_1.TransactionManager,
            seed_runner_service_1.SeedRunnerService,
        ],
    }),
    __metadata("design:paramtypes", [typeof (_a = typeof database_service_1.DatabaseService !== "undefined" && database_service_1.DatabaseService) === "function" ? _a : Object])
], DatabaseModule);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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