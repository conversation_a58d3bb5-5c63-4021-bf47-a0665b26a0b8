b52e0807926d95acd6305924950b5593
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ResponseTransformationInterceptor_1;
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseTransformationInterceptor = void 0;
const common_1 = require("@nestjs/common");
const operators_1 = require("rxjs/operators");
const config_1 = require("@nestjs/config");
/**
 * Response Transformation Interceptor
 * Standardizes API responses across all endpoints
 */
let ResponseTransformationInterceptor = ResponseTransformationInterceptor_1 = class ResponseTransformationInterceptor {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(ResponseTransformationInterceptor_1.name);
    }
    intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        const response = context.switchToHttp().getResponse();
        const startTime = Date.now();
        return next.handle().pipe((0, operators_1.map)(data => this.transformResponse(data, request, response, startTime)), (0, operators_1.tap)(transformedData => this.logResponse(request, transformedData, startTime)));
    }
    transformResponse(data, request, response, startTime) {
        const processingTime = Date.now() - startTime;
        // If data is already in standard format, return as-is
        if (data && typeof data === 'object' && 'success' in data) {
            return this.enhanceStandardResponse(data, request, processingTime);
        }
        // Transform raw data into standard format
        const standardResponse = {
            success: true,
            data: data,
            message: this.generateSuccessMessage(request),
            timestamp: new Date().toISOString(),
            metadata: this.createMetadata(request, processingTime),
        };
        // Add pagination if present
        if (this.hasPaginationData(data)) {
            standardResponse.pagination = this.extractPaginationInfo(data, request);
        }
        // Add links for HATEOAS
        if (this.shouldIncludeLinks(request)) {
            standardResponse.links = this.generateLinks(request, data);
        }
        return standardResponse;
    }
    enhanceStandardResponse(data, request, processingTime) {
        const enhanced = { ...data };
        // Ensure metadata exists and is enhanced
        enhanced.metadata = {
            ...enhanced.metadata,
            ...this.createMetadata(request, processingTime),
        };
        // Add links if not present
        if (!enhanced.links && this.shouldIncludeLinks(request)) {
            enhanced.links = this.generateLinks(request, enhanced.data);
        }
        return enhanced;
    }
    createMetadata(request, processingTime) {
        return {
            version: this.getApiVersion(request),
            timestamp: new Date().toISOString(),
            requestId: this.getRequestId(request),
            processingTime: `${processingTime}ms`,
            endpoint: request.originalUrl,
            method: request.method,
            userAgent: request.headers['user-agent'],
            clientVersion: request.headers['x-client-version'],
            correlationId: request.headers['x-correlation-id'],
            performance: this.categorizePerformance(processingTime),
        };
    }
    generateSuccessMessage(request) {
        const method = request.method.toLowerCase();
        const resource = this.extractResourceName(request.path);
        switch (method) {
            case 'get':
                return `${resource} retrieved successfully`;
            case 'post':
                return `${resource} created successfully`;
            case 'put':
            case 'patch':
                return `${resource} updated successfully`;
            case 'delete':
                return `${resource} deleted successfully`;
            default:
                return 'Operation completed successfully';
        }
    }
    extractResourceName(path) {
        const segments = path.split('/').filter(Boolean);
        const resourceSegment = segments.find(segment => !segment.startsWith('api') &&
            !segment.startsWith('v') &&
            !segment.match(/^[0-9a-f-]{36}$/i) // Not a UUID
        );
        return resourceSegment ?
            resourceSegment.charAt(0).toUpperCase() + resourceSegment.slice(1) :
            'Resource';
    }
    getApiVersion(request) {
        // Extract version from URL path
        const versionMatch = request.path.match(/\/v(\d+)\//);
        if (versionMatch) {
            return versionMatch[1] + '.0';
        }
        // Check version header
        const headerVersion = request.headers['x-api-version'];
        if (headerVersion) {
            return headerVersion;
        }
        return '1.0';
    }
    getRequestId(request) {
        return (request.headers['x-request-id'] ||
            request.headers['x-correlation-id'] ||
            this.generateRequestId());
    }
    generateRequestId() {
        return `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    categorizePerformance(processingTime) {
        if (processingTime < 100)
            return 'excellent';
        if (processingTime < 300)
            return 'good';
        if (processingTime < 1000)
            return 'acceptable';
        if (processingTime < 3000)
            return 'slow';
        return 'very_slow';
    }
    hasPaginationData(data) {
        return (data &&
            typeof data === 'object' &&
            (data.pagination ||
                (data.total !== undefined && data.page !== undefined) ||
                (Array.isArray(data) && data.length > 0)));
    }
    extractPaginationInfo(data, request) {
        if (data.pagination) {
            return data.pagination;
        }
        // Extract from query parameters
        const page = parseInt(request.query.page) || 1;
        const limit = parseInt(request.query.limit) || 10;
        const total = data.total || (Array.isArray(data) ? data.length : 0);
        const totalPages = Math.ceil(total / limit);
        return {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
            offset: (page - 1) * limit,
        };
    }
    shouldIncludeLinks(request) {
        // Include links for API v2 or when explicitly requested
        return (request.path.includes('/v2/') ||
            request.headers['x-include-links'] === 'true' ||
            this.configService.get('API_INCLUDE_HATEOAS', false));
    }
    generateLinks(request, data) {
        const baseUrl = `${request.protocol}://${request.get('host')}`;
        const links = {
            self: `${baseUrl}${request.originalUrl}`,
        };
        // Add resource-specific links
        if (request.path.includes('/analytics')) {
            links.export = `${baseUrl}/api/v1/analytics/export`;
            links.insights = `${baseUrl}/api/v1/analytics/insights`;
        }
        if (request.path.includes('/vulnerabilities')) {
            links.scan = `${baseUrl}/api/v1/vulnerabilities/scan`;
            links.remediation = `${baseUrl}/api/v1/vulnerabilities/remediation`;
        }
        if (request.path.includes('/threats')) {
            links.intelligence = `${baseUrl}/api/v1/threats/intelligence`;
            links.indicators = `${baseUrl}/api/v1/threats/indicators`;
        }
        // Add pagination links if applicable
        if (data && data.pagination) {
            const { page, limit, totalPages } = data.pagination;
            if (page > 1) {
                links.prev = this.buildPaginationUrl(request, page - 1, limit);
                links.first = this.buildPaginationUrl(request, 1, limit);
            }
            if (page < totalPages) {
                links.next = this.buildPaginationUrl(request, page + 1, limit);
                links.last = this.buildPaginationUrl(request, totalPages, limit);
            }
        }
        return links;
    }
    buildPaginationUrl(request, page, limit) {
        const url = new URL(`${request.protocol}://${request.get('host')}${request.originalUrl}`);
        url.searchParams.set('page', page.toString());
        url.searchParams.set('limit', limit.toString());
        return url.toString();
    }
    logResponse(request, data, startTime) {
        const processingTime = Date.now() - startTime;
        this.logger.log('Response transformed', {
            method: request.method,
            url: request.originalUrl,
            statusCode: data.success ? 200 : 400,
            processingTime: `${processingTime}ms`,
            responseSize: JSON.stringify(data).length,
            hasError: !data.success,
            hasPagination: !!data.pagination,
            hasLinks: !!data.links,
            requestId: data.metadata?.requestId,
            correlationId: request.headers['x-correlation-id'],
        });
    }
};
exports.ResponseTransformationInterceptor = ResponseTransformationInterceptor;
exports.ResponseTransformationInterceptor = ResponseTransformationInterceptor = ResponseTransformationInterceptor_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], ResponseTransformationInterceptor);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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