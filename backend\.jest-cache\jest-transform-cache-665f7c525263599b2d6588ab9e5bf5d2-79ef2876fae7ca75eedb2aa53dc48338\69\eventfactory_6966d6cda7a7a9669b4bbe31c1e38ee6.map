{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\event.factory.ts", "mappings": ";;;AAAA,2DAA6D;AAE7D,6GAA4F;AAC5F,+GAA8F;AAC9F,yGAAwF;AACxF,8DAAqD;AACrD,sEAA6D;AAC7D,kEAAyD;AACzD,wFAA8E;AAC9E,4EAAkE;AA0DlE;;;;;;;;;;;;GAYG;AACH,MAAa,YAAY;IACvB;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,OAA2B;QACvC,sBAAsB;QACtB,MAAM,WAAW,GAAG,uCAAW,CAAC,MAAM,CACpC,OAAO,CAAC,UAAU,EAClB,OAAO,CAAC,gBAAgB,EACxB,OAAO,CAAC,cAAc,CACvB,CAAC;QAEF,yBAAyB;QACzB,MAAM,cAAc,GAAG,OAAO,CAAC,SAAS;YACtC,CAAC,CAAC,6CAAc,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC;YAC5C,CAAC,CAAC,6CAAc,CAAC,MAAM,EAAE,CAAC;QAE5B,wBAAwB;QACxB,MAAM,aAAa,GAAG,2CAAa,CAAC,MAAM,CACxC,cAAc,EACd,WAAW,EACX;YACE,eAAe,EAAE,OAAO,CAAC,eAAe;SACzC,CACF,CAAC;QAEF,yBAAyB;QACzB,MAAM,UAAU,GAAe;YAC7B,QAAQ,EAAE,aAAa;YACvB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,+BAAW,CAAC,MAAM;YAC5C,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,IAAI,oDAAqB,CAAC,GAAG;YACvE,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,eAAe,EAAE,OAAO,CAAC,eAAe;YACxC,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,aAAa,EAAE,OAAO,CAAC,aAAa;YACpC,aAAa,EAAE,OAAO,CAAC,aAAa;YACpC,kBAAkB,EAAE,CAAC;SACtB,CAAC;QAEF,OAAO,oBAAK,CAAC,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAU,CACf,OAA4B,EAC5B,UAA2B,EAC3B,gBAAwB,EACxB,OAAqC;QAErC,sCAAsC;QACtC,MAAM,KAAK,GAAG,YAAY,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QACjD,MAAM,WAAW,GAAG,YAAY,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAC7D,MAAM,QAAQ,GAAG,YAAY,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QACrD,MAAM,SAAS,GAAG,YAAY,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,SAAS,GAAG,YAAY,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACzD,MAAM,IAAI,GAAG,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAE/C,OAAO,YAAY,CAAC,MAAM,CAAC;YACzB,IAAI,EAAE,SAAS;YACf,QAAQ;YACR,KAAK;YACL,WAAW;YACX,OAAO,EAAE,OAAO;YAChB,UAAU;YACV,gBAAgB;YAChB,SAAS;YACT,IAAI;YACJ,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CACxB,IAAe,EACf,KAAa,EACb,OAA4B,EAC5B,UAA2B,EAC3B,gBAAwB,EACxB,OAAqC;QAErC,OAAO,YAAY,CAAC,MAAM,CAAC;YACzB,IAAI;YACJ,QAAQ,EAAE,mCAAa,CAAC,IAAI;YAC5B,KAAK;YACL,OAAO;YACP,UAAU;YACV,gBAAgB;YAChB,SAAS,EAAE,EAAE,EAAE,8CAA8C;YAC7D,eAAe,EAAE;gBACf,QAAQ,EAAE,MAAM;aACjB;YACD,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CACxB,IAAe,EACf,KAAa,EACb,OAA4B,EAC5B,UAA2B,EAC3B,gBAAwB,EACxB,OAAqC;QAErC,OAAO,YAAY,CAAC,MAAM,CAAC;YACzB,IAAI;YACJ,QAAQ,EAAE,mCAAa,CAAC,QAAQ;YAChC,KAAK;YACL,OAAO;YACP,UAAU;YACV,gBAAgB;YAChB,SAAS,EAAE,EAAE,EAAE,8BAA8B;YAC7C,eAAe,EAAE;gBACf,QAAQ,EAAE,UAAU;aACrB;YACD,IAAI,EAAE,CAAC,UAAU,EAAE,gBAAgB,CAAC;YACpC,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB,CAC1B,WAAkB,EAClB,IAAe,EACf,KAAa,EACb,OAA4B,EAC5B,OAAqC;QAErC,OAAO,YAAY,CAAC,MAAM,CAAC;YACzB,IAAI;YACJ,QAAQ,EAAE,WAAW,CAAC,QAAQ,EAAE,+BAA+B;YAC/D,KAAK;YACL,OAAO;YACP,UAAU,EAAE,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI;YAC5C,gBAAgB,EAAE,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU;YACxD,aAAa,EAAE,WAAW,CAAC,aAAa,IAAI,WAAW,CAAC,EAAE,CAAC,QAAQ,EAAE;YACrE,aAAa,EAAE,WAAW,CAAC,EAAE;YAC7B,IAAI,EAAE,CAAC,GAAG,WAAW,CAAC,IAAI,EAAE,YAAY,CAAC;YACzC,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAC3B,UAA+B,EAC/B,gBAAwB,EACxB,OAAqC;QAErC,MAAM,KAAK,GAAG,wBAAwB,UAAU,CAAC,SAAS,IAAI,gBAAgB,EAAE,CAAC;QACjF,MAAM,QAAQ,GAAG,YAAY,CAAC,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QACrE,MAAM,SAAS,GAAG,YAAY,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;QAEpE,OAAO,YAAY,CAAC,MAAM,CAAC;YACzB,IAAI,EAAE,2BAAS,CAAC,eAAe;YAC/B,QAAQ;YACR,KAAK;YACL,WAAW,EAAE,UAAU,CAAC,WAAW;YACnC,OAAO,EAAE,UAAU;YACnB,UAAU,EAAE,wCAAe,CAAC,mBAAmB;YAC/C,gBAAgB;YAChB,SAAS;YACT,IAAI,EAAE,CAAC,qBAAqB,EAAE,UAAU,CAAC;YACzC,UAAU,EAAE;gBACV,SAAS,EAAE,UAAU,CAAC,SAAS;gBAC/B,aAAa,EAAE,UAAU,CAAC,aAAa;gBACvC,UAAU,EAAE,UAAU,CAAC,UAAU;gBACjC,MAAM,EAAE,UAAU,CAAC,MAAM;aAC1B;YACD,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB,CAC1B,QAA6B,EAC7B,gBAAwB,EACxB,OAAqC;QAErC,MAAM,KAAK,GAAG,kBAAkB,QAAQ,CAAC,GAAG,IAAI,QAAQ,CAAC,KAAK,IAAI,uBAAuB,EAAE,CAAC;QAC5F,MAAM,QAAQ,GAAG,YAAY,CAAC,wBAAwB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC1E,MAAM,SAAS,GAAG,YAAY,CAAC,+BAA+B,CAAC,QAAQ,CAAC,CAAC;QAEzE,OAAO,YAAY,CAAC,MAAM,CAAC;YACzB,IAAI,EAAE,2BAAS,CAAC,sBAAsB;YACtC,QAAQ;YACR,KAAK;YACL,WAAW,EAAE,QAAQ,CAAC,WAAW;YACjC,OAAO,EAAE,QAAQ;YACjB,UAAU,EAAE,wCAAe,CAAC,qBAAqB;YACjD,gBAAgB;YAChB,SAAS;YACT,IAAI,EAAE,CAAC,eAAe,EAAE,aAAa,CAAC;YACtC,UAAU,EAAE;gBACV,GAAG,EAAE,QAAQ,CAAC,GAAG;gBACjB,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,aAAa,EAAE,QAAQ,CAAC,aAAa;gBACrC,QAAQ,EAAE,QAAQ,CAAC,QAAQ;aAC5B;YACD,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CACtB,IAAe,EACf,QAAuB,EACvB,KAAa,EACb,WAAmB,EACnB,SAAiB,EACjB,OAA6B,EAC7B,OAAqC;QAErC,OAAO,YAAY,CAAC,MAAM,CAAC;YACzB,IAAI;YACJ,QAAQ;YACR,KAAK;YACL,WAAW;YACX,OAAO,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE;YACtE,UAAU,EAAE,wCAAe,CAAC,MAAM;YAClC,gBAAgB,EAAE,SAAS;YAC3B,IAAI,EAAE,CAAC,QAAQ,EAAE,iBAAiB,CAAC;YACnC,UAAU,EAAE;gBACV,SAAS;gBACT,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC;YACD,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED,mDAAmD;IAE3C,MAAM,CAAC,YAAY,CAAC,OAA4B;QACtD,0BAA0B;QAC1B,MAAM,WAAW,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;QAEhF,KAAK,MAAM,KAAK,IAAI,WAAW,EAAE,CAAC;YAChC,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE,CAAC;gBACzD,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,yBAAyB;YACpE,CAAC;QACH,CAAC;QAED,4BAA4B;QAC5B,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAEO,MAAM,CAAC,kBAAkB,CAAC,OAA4B;QAC5D,MAAM,UAAU,GAAG,CAAC,aAAa,EAAE,SAAS,EAAE,cAAc,EAAE,mBAAmB,CAAC,CAAC;QAEnF,KAAK,MAAM,KAAK,IAAI,UAAU,EAAE,CAAC;YAC/B,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE,CAAC;gBACzD,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,yBAAyB;YACrE,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,MAAM,CAAC,gBAAgB,CAAC,OAA4B;QAC1D,MAAM,eAAe,GAAG,CAAC,WAAW,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;QAE/F,KAAK,MAAM,KAAK,IAAI,eAAe,EAAE,CAAC;YACpC,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBACnB,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;gBACtC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;oBAC3B,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,MAAM,CAAC,WAAW,CAAC,OAA4B;QACrD,MAAM,SAAS,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;QAEnD,KAAK,MAAM,KAAK,IAAI,SAAS,EAAE,CAAC;YAC9B,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBAClC,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAC5E,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,MAAM,CAAC,aAAa,CAAC,OAA4B;QACvD,MAAM,cAAc,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;QAExE,KAAK,MAAM,KAAK,IAAI,cAAc,EAAE,CAAC;YACnC,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBACnB,MAAM,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;gBAEtD,IAAI,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;oBAChE,OAAO,mCAAa,CAAC,QAAQ,CAAC;gBAChC,CAAC;gBACD,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;oBAC5D,OAAO,mCAAa,CAAC,IAAI,CAAC;gBAC5B,CAAC;gBACD,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC7D,OAAO,mCAAa,CAAC,MAAM,CAAC;gBAC9B,CAAC;gBACD,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC1D,OAAO,mCAAa,CAAC,GAAG,CAAC;gBAC3B,CAAC;YACH,CAAC;QACH,CAAC;QAED,6BAA6B;QAC7B,OAAO,mCAAa,CAAC,MAAM,CAAC;IAC9B,CAAC;IAEO,MAAM,CAAC,cAAc,CAAC,OAA4B;QACxD,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;QAEtD,wBAAwB;QACxB,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACpE,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9D,OAAO,2BAAS,CAAC,aAAa,CAAC;YACjC,CAAC;YACD,OAAO,2BAAS,CAAC,aAAa,CAAC;QACjC,CAAC;QAED,iBAAiB;QACjB,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAClE,OAAO,2BAAS,CAAC,sBAAsB,CAAC;QAC1C,CAAC;QAED,iBAAiB;QACjB,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC7D,OAAO,2BAAS,CAAC,gBAAgB,CAAC;QACpC,CAAC;QAED,uBAAuB;QACvB,IAAI,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACjE,OAAO,2BAAS,CAAC,sBAAsB,CAAC;QAC1C,CAAC;QAED,gBAAgB;QAChB,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7D,OAAO,2BAAS,CAAC,eAAe,CAAC;QACnC,CAAC;QAED,0BAA0B;QAC1B,OAAO,2BAAS,CAAC,MAAM,CAAC;IAC1B,CAAC;IAEO,MAAM,CAAC,iBAAiB,CAAC,QAAyB;QACxD,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACjC,MAAM,GAAG,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;YACnC,IAAI,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAAE,OAAO,mCAAa,CAAC,QAAQ,CAAC;YACpF,IAAI,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAAE,OAAO,mCAAa,CAAC,MAAM,CAAC;YACxD,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC;gBAAE,OAAO,mCAAa,CAAC,GAAG,CAAC;QACpD,CAAC;QAED,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACjC,IAAI,QAAQ,IAAI,CAAC;gBAAE,OAAO,mCAAa,CAAC,QAAQ,CAAC;YACjD,IAAI,QAAQ,IAAI,CAAC;gBAAE,OAAO,mCAAa,CAAC,IAAI,CAAC;YAC7C,IAAI,QAAQ,IAAI,CAAC;gBAAE,OAAO,mCAAa,CAAC,MAAM,CAAC;YAC/C,OAAO,mCAAa,CAAC,GAAG,CAAC;QAC3B,CAAC;QAED,OAAO,mCAAa,CAAC,MAAM,CAAC;IAC9B,CAAC;IAEO,MAAM,CAAC,wBAAwB,CAAC,QAAyB;QAC/D,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACjC,MAAM,GAAG,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;YACnC,IAAI,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC;gBAAE,OAAO,mCAAa,CAAC,QAAQ,CAAC;YAC5D,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAAE,OAAO,mCAAa,CAAC,IAAI,CAAC;YACpD,IAAI,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAAE,OAAO,mCAAa,CAAC,MAAM,CAAC;YACxD,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC;gBAAE,OAAO,mCAAa,CAAC,GAAG,CAAC;QACpD,CAAC;QAED,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACjC,IAAI,QAAQ,IAAI,CAAC;gBAAE,OAAO,mCAAa,CAAC,QAAQ,CAAC;YACjD,IAAI,QAAQ,IAAI,CAAC;gBAAE,OAAO,mCAAa,CAAC,IAAI,CAAC;YAC7C,IAAI,QAAQ,IAAI,CAAC;gBAAE,OAAO,mCAAa,CAAC,MAAM,CAAC;YAC/C,OAAO,mCAAa,CAAC,GAAG,CAAC;QAC3B,CAAC;QAED,OAAO,mCAAa,CAAC,MAAM,CAAC;IAC9B,CAAC;IAEO,MAAM,CAAC,wBAAwB,CAAC,UAA+B;QACrE,IAAI,KAAK,GAAG,EAAE,CAAC,CAAC,aAAa;QAE7B,6BAA6B;QAC7B,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;YAC1B,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC;QACnD,CAAC;QAED,2BAA2B;QAC3B,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;YACxB,MAAM,QAAQ,GAAG,OAAO,UAAU,CAAC,QAAQ,KAAK,QAAQ;gBACtD,CAAC,CAAC,UAAU,CAAC,QAAQ;gBACrB,CAAC,CAAC,YAAY,CAAC,gBAAgB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YACvD,KAAK,IAAI,QAAQ,GAAG,CAAC,CAAC;QACxB,CAAC;QAED,iCAAiC;QACjC,IAAI,UAAU,CAAC,aAAa,KAAK,IAAI,IAAI,UAAU,CAAC,aAAa,KAAK,QAAQ,EAAE,CAAC;YAC/E,KAAK,IAAI,EAAE,CAAC,CAAC,qCAAqC;QACpD,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IAC3C,CAAC;IAEO,MAAM,CAAC,+BAA+B,CAAC,QAA6B;QAC1E,IAAI,KAAK,GAAG,EAAE,CAAC,CAAC,aAAa;QAE7B,6BAA6B;QAC7B,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC;YACvB,KAAK,IAAI,QAAQ,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,kBAAkB;QACrD,CAAC;QAED,iCAAiC;QACjC,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;YACzB,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,oCAAoC;QACpC,IAAI,QAAQ,CAAC,gBAAgB,KAAK,MAAM,EAAE,CAAC;YACzC,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;aAAM,IAAI,QAAQ,CAAC,gBAAgB,KAAK,QAAQ,EAAE,CAAC;YAClD,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IAC3C,CAAC;IAEO,MAAM,CAAC,gBAAgB,CAAC,QAAgB;QAC9C,MAAM,GAAG,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;QACnC,IAAI,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC;YAAE,OAAO,EAAE,CAAC;QACxC,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,CAAC,CAAC;QACnC,IAAI,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,CAAC,CAAC;QACrC,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAClC,OAAO,CAAC,CAAC;IACX,CAAC;CACF;AAzcD,oCAycC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\event.factory.ts"], "sourcesContent": ["import { Event, EventProps } from '../entities/event.entity';\r\nimport { UniqueEntityId } from '../../../../shared-kernel';\r\nimport { EventMetadata } from '../value-objects/event-metadata/event-metadata.value-object';\r\nimport { EventTimestamp } from '../value-objects/event-metadata/event-timestamp.value-object';\r\nimport { EventSource } from '../value-objects/event-metadata/event-source.value-object';\r\nimport { EventType } from '../enums/event-type.enum';\r\nimport { EventSeverity } from '../enums/event-severity.enum';\r\nimport { EventStatus } from '../enums/event-status.enum';\r\nimport { EventProcessingStatus } from '../enums/event-processing-status.enum';\r\nimport { EventSourceType } from '../enums/event-source-type.enum';\r\n\r\n/**\r\n * Event Creation Options\r\n */\r\nexport interface CreateEventOptions {\r\n  /** Event ID (optional, will be generated if not provided) */\r\n  id?: UniqueEntityId;\r\n  /** Event type */\r\n  type: EventType;\r\n  /** Event severity */\r\n  severity: EventSeverity;\r\n  /** Event title */\r\n  title: string;\r\n  /** Event description */\r\n  description?: string;\r\n  /** Raw event data */\r\n  rawData: Record<string, any>;\r\n  /** Event source type */\r\n  sourceType: EventSourceType;\r\n  /** Event source identifier */\r\n  sourceIdentifier: string;\r\n  /** Event timestamp (optional, will use current time if not provided) */\r\n  timestamp?: Date;\r\n  /** Initial tags */\r\n  tags?: string[];\r\n  /** Initial risk score */\r\n  riskScore?: number;\r\n  /** Initial confidence level */\r\n  confidenceLevel?: number;\r\n  /** Initial attributes */\r\n  attributes?: Record<string, any>;\r\n  /** Correlation ID */\r\n  correlationId?: string;\r\n  /** Parent event ID */\r\n  parentEventId?: UniqueEntityId;\r\n  /** Initial status (optional, defaults to ACTIVE) */\r\n  status?: EventStatus;\r\n  /** Initial processing status (optional, defaults to RAW) */\r\n  processingStatus?: EventProcessingStatus;\r\n  /** Source metadata */\r\n  sourceMetadata?: {\r\n    name?: string;\r\n    version?: string;\r\n    vendor?: string;\r\n    location?: string;\r\n    metadata?: Record<string, any>;\r\n  };\r\n  /** Processing hints */\r\n  processingHints?: {\r\n    priority?: 'low' | 'normal' | 'high' | 'critical';\r\n    skipNormalization?: boolean;\r\n    skipEnrichment?: boolean;\r\n    skipCorrelation?: boolean;\r\n    retentionDays?: number;\r\n  };\r\n}\r\n\r\n/**\r\n * Event Factory\r\n * \r\n * Factory class for creating Event entities with proper validation and defaults.\r\n * Handles complex event creation scenarios and ensures all business rules are applied.\r\n * \r\n * Key responsibilities:\r\n * - Create events from various input formats\r\n * - Apply default values and business rules\r\n * - Validate event data consistency\r\n * - Generate proper metadata and timestamps\r\n * - Handle event relationships and correlations\r\n */\r\nexport class EventFactory {\r\n  /**\r\n   * Create a new Event with the provided options\r\n   */\r\n  static create(options: CreateEventOptions): Event {\r\n    // Create event source\r\n    const eventSource = EventSource.create(\r\n      options.sourceType,\r\n      options.sourceIdentifier,\r\n      options.sourceMetadata\r\n    );\r\n\r\n    // Create event timestamp\r\n    const eventTimestamp = options.timestamp \r\n      ? EventTimestamp.fromDate(options.timestamp)\r\n      : EventTimestamp.create();\r\n\r\n    // Create event metadata\r\n    const eventMetadata = EventMetadata.create(\r\n      eventTimestamp,\r\n      eventSource,\r\n      {\r\n        processingHints: options.processingHints,\r\n      }\r\n    );\r\n\r\n    // Build event properties\r\n    const eventProps: EventProps = {\r\n      metadata: eventMetadata,\r\n      type: options.type,\r\n      severity: options.severity,\r\n      status: options.status || EventStatus.ACTIVE,\r\n      processingStatus: options.processingStatus || EventProcessingStatus.RAW,\r\n      rawData: options.rawData,\r\n      title: options.title,\r\n      description: options.description,\r\n      tags: options.tags,\r\n      riskScore: options.riskScore,\r\n      confidenceLevel: options.confidenceLevel,\r\n      attributes: options.attributes,\r\n      correlationId: options.correlationId,\r\n      parentEventId: options.parentEventId,\r\n      processingAttempts: 0,\r\n    };\r\n\r\n    return Event.create(eventProps, options.id);\r\n  }\r\n\r\n  /**\r\n   * Create an Event from raw log data\r\n   */\r\n  static fromRawLog(\r\n    logData: Record<string, any>,\r\n    sourceType: EventSourceType,\r\n    sourceIdentifier: string,\r\n    options?: Partial<CreateEventOptions>\r\n  ): Event {\r\n    // Extract common fields from log data\r\n    const title = EventFactory.extractTitle(logData);\r\n    const description = EventFactory.extractDescription(logData);\r\n    const severity = EventFactory.inferSeverity(logData);\r\n    const eventType = EventFactory.inferEventType(logData);\r\n    const timestamp = EventFactory.extractTimestamp(logData);\r\n    const tags = EventFactory.extractTags(logData);\r\n\r\n    return EventFactory.create({\r\n      type: eventType,\r\n      severity,\r\n      title,\r\n      description,\r\n      rawData: logData,\r\n      sourceType,\r\n      sourceIdentifier,\r\n      timestamp,\r\n      tags,\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a high-severity security Event\r\n   */\r\n  static createSecurityAlert(\r\n    type: EventType,\r\n    title: string,\r\n    rawData: Record<string, any>,\r\n    sourceType: EventSourceType,\r\n    sourceIdentifier: string,\r\n    options?: Partial<CreateEventOptions>\r\n  ): Event {\r\n    return EventFactory.create({\r\n      type,\r\n      severity: EventSeverity.HIGH,\r\n      title,\r\n      rawData,\r\n      sourceType,\r\n      sourceIdentifier,\r\n      riskScore: 80, // Default high risk score for security alerts\r\n      processingHints: {\r\n        priority: 'high',\r\n      },\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a critical security Event\r\n   */\r\n  static createCriticalAlert(\r\n    type: EventType,\r\n    title: string,\r\n    rawData: Record<string, any>,\r\n    sourceType: EventSourceType,\r\n    sourceIdentifier: string,\r\n    options?: Partial<CreateEventOptions>\r\n  ): Event {\r\n    return EventFactory.create({\r\n      type,\r\n      severity: EventSeverity.CRITICAL,\r\n      title,\r\n      rawData,\r\n      sourceType,\r\n      sourceIdentifier,\r\n      riskScore: 95, // Default critical risk score\r\n      processingHints: {\r\n        priority: 'critical',\r\n      },\r\n      tags: ['critical', 'security-alert'],\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a correlated Event (child of another event)\r\n   */\r\n  static createCorrelatedEvent(\r\n    parentEvent: Event,\r\n    type: EventType,\r\n    title: string,\r\n    rawData: Record<string, any>,\r\n    options?: Partial<CreateEventOptions>\r\n  ): Event {\r\n    return EventFactory.create({\r\n      type,\r\n      severity: parentEvent.severity, // Inherit severity from parent\r\n      title,\r\n      rawData,\r\n      sourceType: parentEvent.metadata.source.type,\r\n      sourceIdentifier: parentEvent.metadata.source.identifier,\r\n      correlationId: parentEvent.correlationId || parentEvent.id.toString(),\r\n      parentEventId: parentEvent.id,\r\n      tags: [...parentEvent.tags, 'correlated'],\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create an Event from external threat intelligence\r\n   */\r\n  static fromThreatIntelligence(\r\n    threatData: Record<string, any>,\r\n    sourceIdentifier: string,\r\n    options?: Partial<CreateEventOptions>\r\n  ): Event {\r\n    const title = `Threat Intelligence: ${threatData.indicator || 'Unknown Threat'}`;\r\n    const severity = EventFactory.mapThreatSeverity(threatData.severity);\r\n    const riskScore = EventFactory.calculateThreatRiskScore(threatData);\r\n\r\n    return EventFactory.create({\r\n      type: EventType.THREAT_DETECTED,\r\n      severity,\r\n      title,\r\n      description: threatData.description,\r\n      rawData: threatData,\r\n      sourceType: EventSourceType.THREAT_INTELLIGENCE,\r\n      sourceIdentifier,\r\n      riskScore,\r\n      tags: ['threat-intelligence', 'external'],\r\n      attributes: {\r\n        indicator: threatData.indicator,\r\n        indicatorType: threatData.indicatorType,\r\n        confidence: threatData.confidence,\r\n        source: threatData.source,\r\n      },\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create an Event from vulnerability scan results\r\n   */\r\n  static fromVulnerabilityScan(\r\n    vulnData: Record<string, any>,\r\n    sourceIdentifier: string,\r\n    options?: Partial<CreateEventOptions>\r\n  ): Event {\r\n    const title = `Vulnerability: ${vulnData.cve || vulnData.title || 'Unknown Vulnerability'}`;\r\n    const severity = EventFactory.mapVulnerabilitySeverity(vulnData.severity);\r\n    const riskScore = EventFactory.calculateVulnerabilityRiskScore(vulnData);\r\n\r\n    return EventFactory.create({\r\n      type: EventType.VULNERABILITY_DETECTED,\r\n      severity,\r\n      title,\r\n      description: vulnData.description,\r\n      rawData: vulnData,\r\n      sourceType: EventSourceType.VULNERABILITY_SCANNER,\r\n      sourceIdentifier,\r\n      riskScore,\r\n      tags: ['vulnerability', 'scan-result'],\r\n      attributes: {\r\n        cve: vulnData.cve,\r\n        cvssScore: vulnData.cvssScore,\r\n        affectedAsset: vulnData.affectedAsset,\r\n        solution: vulnData.solution,\r\n      },\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a manual Event (created by analyst)\r\n   */\r\n  static createManualEvent(\r\n    type: EventType,\r\n    severity: EventSeverity,\r\n    title: string,\r\n    description: string,\r\n    createdBy: string,\r\n    rawData?: Record<string, any>,\r\n    options?: Partial<CreateEventOptions>\r\n  ): Event {\r\n    return EventFactory.create({\r\n      type,\r\n      severity,\r\n      title,\r\n      description,\r\n      rawData: rawData || { createdBy, createdAt: new Date().toISOString() },\r\n      sourceType: EventSourceType.MANUAL,\r\n      sourceIdentifier: createdBy,\r\n      tags: ['manual', 'analyst-created'],\r\n      attributes: {\r\n        createdBy,\r\n        createdAt: new Date().toISOString(),\r\n      },\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  // Helper methods for data extraction and inference\r\n\r\n  private static extractTitle(logData: Record<string, any>): string {\r\n    // Try common title fields\r\n    const titleFields = ['title', 'message', 'summary', 'event_name', 'alert_name'];\r\n    \r\n    for (const field of titleFields) {\r\n      if (logData[field] && typeof logData[field] === 'string') {\r\n        return logData[field].substring(0, 200); // Truncate to max length\r\n      }\r\n    }\r\n\r\n    // Fallback to generic title\r\n    return 'Security Event';\r\n  }\r\n\r\n  private static extractDescription(logData: Record<string, any>): string | undefined {\r\n    const descFields = ['description', 'details', 'full_message', 'event_description'];\r\n    \r\n    for (const field of descFields) {\r\n      if (logData[field] && typeof logData[field] === 'string') {\r\n        return logData[field].substring(0, 2000); // Truncate to max length\r\n      }\r\n    }\r\n\r\n    return undefined;\r\n  }\r\n\r\n  private static extractTimestamp(logData: Record<string, any>): Date | undefined {\r\n    const timestampFields = ['timestamp', '@timestamp', 'event_time', 'created_at', 'occurred_at'];\r\n    \r\n    for (const field of timestampFields) {\r\n      if (logData[field]) {\r\n        const date = new Date(logData[field]);\r\n        if (!isNaN(date.getTime())) {\r\n          return date;\r\n        }\r\n      }\r\n    }\r\n\r\n    return undefined;\r\n  }\r\n\r\n  private static extractTags(logData: Record<string, any>): string[] | undefined {\r\n    const tagFields = ['tags', 'labels', 'categories'];\r\n    \r\n    for (const field of tagFields) {\r\n      if (Array.isArray(logData[field])) {\r\n        return logData[field].filter(tag => typeof tag === 'string').slice(0, 20);\r\n      }\r\n    }\r\n\r\n    return undefined;\r\n  }\r\n\r\n  private static inferSeverity(logData: Record<string, any>): EventSeverity {\r\n    const severityFields = ['severity', 'level', 'priority', 'criticality'];\r\n    \r\n    for (const field of severityFields) {\r\n      if (logData[field]) {\r\n        const severity = String(logData[field]).toLowerCase();\r\n        \r\n        if (severity.includes('critical') || severity.includes('fatal')) {\r\n          return EventSeverity.CRITICAL;\r\n        }\r\n        if (severity.includes('high') || severity.includes('error')) {\r\n          return EventSeverity.HIGH;\r\n        }\r\n        if (severity.includes('medium') || severity.includes('warn')) {\r\n          return EventSeverity.MEDIUM;\r\n        }\r\n        if (severity.includes('low') || severity.includes('info')) {\r\n          return EventSeverity.LOW;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Default to medium severity\r\n    return EventSeverity.MEDIUM;\r\n  }\r\n\r\n  private static inferEventType(logData: Record<string, any>): EventType {\r\n    const message = JSON.stringify(logData).toLowerCase();\r\n    \r\n    // Authentication events\r\n    if (message.includes('login') || message.includes('authentication')) {\r\n      if (message.includes('failed') || message.includes('failure')) {\r\n        return EventType.LOGIN_FAILURE;\r\n      }\r\n      return EventType.LOGIN_SUCCESS;\r\n    }\r\n    \r\n    // Network events\r\n    if (message.includes('connection') || message.includes('network')) {\r\n      return EventType.CONNECTION_ESTABLISHED;\r\n    }\r\n    \r\n    // Malware events\r\n    if (message.includes('malware') || message.includes('virus')) {\r\n      return EventType.MALWARE_DETECTED;\r\n    }\r\n    \r\n    // Vulnerability events\r\n    if (message.includes('vulnerability') || message.includes('cve')) {\r\n      return EventType.VULNERABILITY_DETECTED;\r\n    }\r\n    \r\n    // Threat events\r\n    if (message.includes('threat') || message.includes('attack')) {\r\n      return EventType.THREAT_DETECTED;\r\n    }\r\n    \r\n    // Default to custom event\r\n    return EventType.CUSTOM;\r\n  }\r\n\r\n  private static mapThreatSeverity(severity: string | number): EventSeverity {\r\n    if (typeof severity === 'string') {\r\n      const sev = severity.toLowerCase();\r\n      if (sev.includes('critical') || sev.includes('high')) return EventSeverity.CRITICAL;\r\n      if (sev.includes('medium')) return EventSeverity.MEDIUM;\r\n      if (sev.includes('low')) return EventSeverity.LOW;\r\n    }\r\n    \r\n    if (typeof severity === 'number') {\r\n      if (severity >= 8) return EventSeverity.CRITICAL;\r\n      if (severity >= 6) return EventSeverity.HIGH;\r\n      if (severity >= 4) return EventSeverity.MEDIUM;\r\n      return EventSeverity.LOW;\r\n    }\r\n    \r\n    return EventSeverity.MEDIUM;\r\n  }\r\n\r\n  private static mapVulnerabilitySeverity(severity: string | number): EventSeverity {\r\n    if (typeof severity === 'string') {\r\n      const sev = severity.toLowerCase();\r\n      if (sev.includes('critical')) return EventSeverity.CRITICAL;\r\n      if (sev.includes('high')) return EventSeverity.HIGH;\r\n      if (sev.includes('medium')) return EventSeverity.MEDIUM;\r\n      if (sev.includes('low')) return EventSeverity.LOW;\r\n    }\r\n    \r\n    if (typeof severity === 'number') {\r\n      if (severity >= 9) return EventSeverity.CRITICAL;\r\n      if (severity >= 7) return EventSeverity.HIGH;\r\n      if (severity >= 4) return EventSeverity.MEDIUM;\r\n      return EventSeverity.LOW;\r\n    }\r\n    \r\n    return EventSeverity.MEDIUM;\r\n  }\r\n\r\n  private static calculateThreatRiskScore(threatData: Record<string, any>): number {\r\n    let score = 50; // Base score\r\n    \r\n    // Adjust based on confidence\r\n    if (threatData.confidence) {\r\n      score += Math.floor(threatData.confidence * 0.3);\r\n    }\r\n    \r\n    // Adjust based on severity\r\n    if (threatData.severity) {\r\n      const severity = typeof threatData.severity === 'number' \r\n        ? threatData.severity \r\n        : EventFactory.severityToNumber(threatData.severity);\r\n      score += severity * 5;\r\n    }\r\n    \r\n    // Adjust based on indicator type\r\n    if (threatData.indicatorType === 'ip' || threatData.indicatorType === 'domain') {\r\n      score += 10; // Network indicators are higher risk\r\n    }\r\n    \r\n    return Math.min(100, Math.max(0, score));\r\n  }\r\n\r\n  private static calculateVulnerabilityRiskScore(vulnData: Record<string, any>): number {\r\n    let score = 30; // Base score\r\n    \r\n    // Adjust based on CVSS score\r\n    if (vulnData.cvssScore) {\r\n      score += vulnData.cvssScore * 7; // CVSS 0-10 scale\r\n    }\r\n    \r\n    // Adjust based on exploitability\r\n    if (vulnData.exploitable) {\r\n      score += 20;\r\n    }\r\n    \r\n    // Adjust based on asset criticality\r\n    if (vulnData.assetCriticality === 'high') {\r\n      score += 15;\r\n    } else if (vulnData.assetCriticality === 'medium') {\r\n      score += 10;\r\n    }\r\n    \r\n    return Math.min(100, Math.max(0, score));\r\n  }\r\n\r\n  private static severityToNumber(severity: string): number {\r\n    const sev = severity.toLowerCase();\r\n    if (sev.includes('critical')) return 10;\r\n    if (sev.includes('high')) return 8;\r\n    if (sev.includes('medium')) return 6;\r\n    if (sev.includes('low')) return 4;\r\n    return 5;\r\n  }\r\n}"], "version": 3}