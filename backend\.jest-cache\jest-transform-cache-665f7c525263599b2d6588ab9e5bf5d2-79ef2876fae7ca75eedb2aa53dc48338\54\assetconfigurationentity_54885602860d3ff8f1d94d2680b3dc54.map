{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\asset-management\\domain\\entities\\asset-configuration.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCASiB;AACjB,iDAAuC;AAEvC;;;GAGG;AAOI,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IAwV7B;;OAEG;IACH,IAAI,QAAQ;QACV,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAC7D,OAAO,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,KAAK,UAAU,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,UAAU,EAAE,MAAM,KAAK,WAAW,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,KAAK,EAAE,QAAQ,IAAI,KAAK,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QACzD,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,aAAa;QACX,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACrD,4DAA4D;QAC5D,OAAO,UAAU,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;IAC/E,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,MAAc;QAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,GAAW;QAChB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,GAAW;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,UAAe;QAC9B,IAAI,CAAC,UAAU,GAAG;YAChB,GAAG,IAAI,CAAC,UAAU;YAClB,GAAG,UAAU;YACb,cAAc,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACzC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,QAA4B;QACtC,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,CAAC;YACzC,IAAI,CAAC,KAAK,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,eAAe,EAAE,aAAa,EAAE,EAAE,EAAE,CAAC;YACjG,OAAO;QACT,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QACrD,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAErD,IAAI,CAAC,KAAK,GAAG;YACX,QAAQ,EAAE,UAAU,GAAG,CAAC;YACxB,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC;YAC3C,SAAS,EAAE,eAAe;YAC1B,UAAU,EAAE,QAAQ,CAAC,EAAE;YACvB,UAAU;YACV,aAAa,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YAC7C,eAAe,EAAE,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC;SAC5D,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAC,KAAyB;QAC7C,qEAAqE;QACrE,MAAM,OAAO,GAAG;YACd,KAAK,EAAE,EAAE;YACT,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,EAAE;SACZ,CAAC;QAEF,iDAAiD;QACjD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,OAAY;QACtC,MAAM,WAAW,GAAG,CAAC,CAAC;QACtB,MAAM,cAAc,GAAG,CAAC,CAAC;QACzB,MAAM,aAAa,GAAG,CAAC,CAAC;QAExB,MAAM,KAAK,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC;YACpC,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,cAAc,CAAC;YAC1C,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,aAAa,CAAC,CAAC;QAEvD,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,KAAa;QACpC,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,UAAU,CAAC;QACnC,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,MAAM,CAAC;QAC/B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,QAAQ,CAAC;QACjC,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,OAAY;QACnC,MAAM,KAAK,GAAG,IAAI,GAAG,EAAU,CAAC;QAEhC,8CAA8C;QAC9C,CAAC,GAAG,OAAO,CAAC,KAAK,EAAE,GAAG,OAAO,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC3E,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACzC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACzB,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACK,4BAA4B,CAAC,OAAY;QAC/C,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,eAAe,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;QAClF,CAAC;QAED,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,eAAe,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;QAChF,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,eAAe,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QAC/E,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO;YACL,aAAa,EAAE,IAAI,CAAC,UAAU,EAAE;YAChC,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACrC,CAAC;IACJ,CAAC;CACF,CAAA;AAljBY,gDAAkB;AAE7B;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;8CACpB;AA+BX;IA1BC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,aAAa;QACnB,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE;YACJ,QAAQ;YACR,SAAS;YACT,UAAU;YACV,aAAa;YACb,UAAU;YACV,SAAS;YACT,UAAU;YACV,aAAa;YACb,eAAe;YACf,cAAc;YACd,UAAU;YACV,WAAW;YACX,aAAa;YACb,oBAAoB;YACpB,mBAAmB;YACnB,YAAY;YACZ,cAAc;YACd,cAAc;YACd,kBAAkB;YAClB,QAAQ;SACT;KACF,CAAC;;sDACiB;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;gDACX;AAMb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uDACpB;AAMrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;;sDAkL7C;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;;sDACb;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;sDAC5B;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;0DAC5B;AAMxB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDA0BxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sDAcxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;iDASxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;gDACtC;AAMf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC;kDACtD,IAAI,oBAAJ,IAAI;sDAAC;AAMjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;sDAC3B;AAGnB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;qDAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;qDAAC;AAKhB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,oBAAK,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,cAAc,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;IAC9E,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;kDAC1B,oBAAK,oBAAL,oBAAK;iDAAC;AAGb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;mDAC3B;6BAtVL,kBAAkB;IAN9B,IAAA,gBAAM,EAAC,sBAAsB,CAAC;IAC9B,IAAA,eAAK,EAAC,CAAC,SAAS,CAAC,CAAC;IAClB,IAAA,eAAK,EAAC,CAAC,YAAY,CAAC,CAAC;IACrB,IAAA,eAAK,EAAC,CAAC,YAAY,CAAC,CAAC;IACrB,IAAA,eAAK,EAAC,CAAC,YAAY,CAAC,CAAC;IACrB,IAAA,eAAK,EAAC,CAAC,gBAAgB,CAAC,CAAC;GACb,kBAAkB,CAkjB9B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\asset-management\\domain\\entities\\asset-configuration.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  ManyToOne,\r\n  JoinColumn,\r\n} from 'typeorm';\r\nimport { Asset } from './asset.entity';\r\n\r\n/**\r\n * Asset Configuration entity\r\n * Represents configuration snapshots and changes for assets\r\n */\r\n@Entity('asset_configurations')\r\n@Index(['assetId'])\r\n@Index(['configType'])\r\n@Index(['capturedAt'])\r\n@Index(['isBaseline'])\r\n@Index(['changeDetected'])\r\nexport class AssetConfiguration {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * Configuration type\r\n   */\r\n  @Column({\r\n    name: 'config_type',\r\n    type: 'enum',\r\n    enum: [\r\n      'system',\r\n      'network',\r\n      'security',\r\n      'application',\r\n      'database',\r\n      'service',\r\n      'registry',\r\n      'file_system',\r\n      'user_accounts',\r\n      'group_policy',\r\n      'firewall',\r\n      'antivirus',\r\n      'patch_level',\r\n      'installed_software',\r\n      'running_processes',\r\n      'open_ports',\r\n      'certificates',\r\n      'cloud_config',\r\n      'container_config',\r\n      'custom',\r\n    ],\r\n  })\r\n  configType: string;\r\n\r\n  /**\r\n   * Configuration name/identifier\r\n   */\r\n  @Column({ length: 255 })\r\n  name: string;\r\n\r\n  /**\r\n   * Configuration description\r\n   */\r\n  @Column({ type: 'text', nullable: true })\r\n  description?: string;\r\n\r\n  /**\r\n   * Configuration data\r\n   */\r\n  @Column({ name: 'config_data', type: 'jsonb' })\r\n  configData: {\r\n    // Raw configuration data\r\n    raw?: any;\r\n    \r\n    // Parsed/structured configuration\r\n    structured?: any;\r\n    \r\n    // Configuration metadata\r\n    metadata?: {\r\n      source: string;\r\n      collector: string;\r\n      version: string;\r\n      format: string;\r\n      encoding?: string;\r\n      checksum?: string;\r\n      size?: number;\r\n    };\r\n    \r\n    // System configuration\r\n    system?: {\r\n      hostname?: string;\r\n      domain?: string;\r\n      timezone?: string;\r\n      locale?: string;\r\n      bootTime?: string;\r\n      uptime?: number;\r\n      kernelVersion?: string;\r\n      architecture?: string;\r\n      cpuInfo?: any;\r\n      memoryInfo?: any;\r\n      diskInfo?: any;\r\n    };\r\n    \r\n    // Network configuration\r\n    network?: {\r\n      interfaces?: Array<{\r\n        name: string;\r\n        type: string;\r\n        status: string;\r\n        ipAddresses: string[];\r\n        macAddress?: string;\r\n        mtu?: number;\r\n        speed?: string;\r\n      }>;\r\n      routes?: Array<{\r\n        destination: string;\r\n        gateway: string;\r\n        interface: string;\r\n        metric?: number;\r\n      }>;\r\n      dns?: {\r\n        servers: string[];\r\n        searchDomains: string[];\r\n      };\r\n      firewall?: {\r\n        enabled: boolean;\r\n        rules: Array<{\r\n          direction: 'inbound' | 'outbound';\r\n          action: 'allow' | 'deny';\r\n          protocol: string;\r\n          port?: string;\r\n          source?: string;\r\n          destination?: string;\r\n        }>;\r\n      };\r\n    };\r\n    \r\n    // Security configuration\r\n    security?: {\r\n      passwordPolicy?: {\r\n        minLength?: number;\r\n        complexity?: boolean;\r\n        expiration?: number;\r\n        history?: number;\r\n        lockoutThreshold?: number;\r\n      };\r\n      auditPolicy?: {\r\n        loginEvents?: boolean;\r\n        privilegeUse?: boolean;\r\n        objectAccess?: boolean;\r\n        policyChange?: boolean;\r\n        accountManagement?: boolean;\r\n      };\r\n      userRights?: Array<{\r\n        right: string;\r\n        users: string[];\r\n        groups: string[];\r\n      }>;\r\n      certificates?: Array<{\r\n        subject: string;\r\n        issuer: string;\r\n        serialNumber: string;\r\n        thumbprint: string;\r\n        validFrom: string;\r\n        validTo: string;\r\n        keyUsage: string[];\r\n      }>;\r\n    };\r\n    \r\n    // Application configuration\r\n    application?: {\r\n      installedSoftware?: Array<{\r\n        name: string;\r\n        version: string;\r\n        vendor?: string;\r\n        installDate?: string;\r\n        installPath?: string;\r\n        size?: number;\r\n      }>;\r\n      runningServices?: Array<{\r\n        name: string;\r\n        displayName?: string;\r\n        status: string;\r\n        startType: string;\r\n        account?: string;\r\n        path?: string;\r\n        pid?: number;\r\n      }>;\r\n      scheduledTasks?: Array<{\r\n        name: string;\r\n        path: string;\r\n        status: string;\r\n        lastRun?: string;\r\n        nextRun?: string;\r\n        command: string;\r\n        arguments?: string;\r\n      }>;\r\n    };\r\n    \r\n    // Database configuration\r\n    database?: {\r\n      version?: string;\r\n      edition?: string;\r\n      instances?: Array<{\r\n        name: string;\r\n        status: string;\r\n        port: number;\r\n        authentication: string;\r\n        encryption?: boolean;\r\n      }>;\r\n      databases?: Array<{\r\n        name: string;\r\n        size: number;\r\n        owner: string;\r\n        collation?: string;\r\n        recoveryModel?: string;\r\n      }>;\r\n      users?: Array<{\r\n        name: string;\r\n        type: string;\r\n        roles: string[];\r\n        lastLogin?: string;\r\n      }>;\r\n    };\r\n    \r\n    // Cloud configuration\r\n    cloud?: {\r\n      provider?: string;\r\n      region?: string;\r\n      instanceType?: string;\r\n      securityGroups?: Array<{\r\n        id: string;\r\n        name: string;\r\n        rules: Array<{\r\n          direction: string;\r\n          protocol: string;\r\n          port: string;\r\n          source: string;\r\n        }>;\r\n      }>;\r\n      iamRoles?: Array<{\r\n        name: string;\r\n        policies: string[];\r\n        trustPolicy?: any;\r\n      }>;\r\n      tags?: Record<string, string>;\r\n    };\r\n  };\r\n\r\n  /**\r\n   * Configuration hash for change detection\r\n   */\r\n  @Column({ name: 'config_hash' })\r\n  configHash: string;\r\n\r\n  /**\r\n   * Whether this is a baseline configuration\r\n   */\r\n  @Column({ name: 'is_baseline', default: false })\r\n  isBaseline: boolean;\r\n\r\n  /**\r\n   * Whether a change was detected from previous configuration\r\n   */\r\n  @Column({ name: 'change_detected', default: false })\r\n  changeDetected: boolean;\r\n\r\n  /**\r\n   * Changes from previous configuration\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  changes?: {\r\n    added?: Array<{\r\n      path: string;\r\n      value: any;\r\n      description?: string;\r\n    }>;\r\n    modified?: Array<{\r\n      path: string;\r\n      oldValue: any;\r\n      newValue: any;\r\n      description?: string;\r\n    }>;\r\n    removed?: Array<{\r\n      path: string;\r\n      value: any;\r\n      description?: string;\r\n    }>;\r\n    summary?: {\r\n      totalChanges: number;\r\n      addedCount: number;\r\n      modifiedCount: number;\r\n      removedCount: number;\r\n      changeCategories: string[];\r\n      riskLevel: 'low' | 'medium' | 'high' | 'critical';\r\n    };\r\n  };\r\n\r\n  /**\r\n   * Configuration compliance status\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  compliance?: {\r\n    status: 'compliant' | 'non_compliant' | 'partially_compliant' | 'unknown';\r\n    frameworks: string[]; // Framework IDs\r\n    controls: string[]; // Control IDs\r\n    violations?: Array<{\r\n      framework: string;\r\n      control: string;\r\n      severity: 'low' | 'medium' | 'high' | 'critical';\r\n      description: string;\r\n      recommendation?: string;\r\n    }>;\r\n    lastAssessment?: string;\r\n    score?: number; // 0-100\r\n  };\r\n\r\n  /**\r\n   * Configuration drift information\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  drift?: {\r\n    detected: boolean;\r\n    severity: 'low' | 'medium' | 'high' | 'critical';\r\n    driftType: 'configuration' | 'security' | 'compliance' | 'performance';\r\n    baselineId?: string;\r\n    driftScore?: number; // 0-100\r\n    affectedAreas: string[];\r\n    recommendations?: string[];\r\n  };\r\n\r\n  /**\r\n   * Configuration tags\r\n   */\r\n  @Column({ type: 'text', array: true, default: '{}' })\r\n  tags: string[];\r\n\r\n  /**\r\n   * When configuration was captured\r\n   */\r\n  @Column({ name: 'captured_at', type: 'timestamp with time zone' })\r\n  capturedAt: Date;\r\n\r\n  /**\r\n   * User who captured the configuration\r\n   */\r\n  @Column({ name: 'captured_by', type: 'uuid' })\r\n  capturedBy: string;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  // Relationships\r\n  @ManyToOne(() => Asset, asset => asset.configurations, { onDelete: 'CASCADE' })\r\n  @JoinColumn({ name: 'asset_id' })\r\n  asset: Asset;\r\n\r\n  @Column({ name: 'asset_id', type: 'uuid' })\r\n  assetId: string;\r\n\r\n  /**\r\n   * Check if configuration is recent\r\n   */\r\n  get isRecent(): boolean {\r\n    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);\r\n    return this.capturedAt > oneDayAgo;\r\n  }\r\n\r\n  /**\r\n   * Check if configuration has critical changes\r\n   */\r\n  get hasCriticalChanges(): boolean {\r\n    return this.changes?.summary?.riskLevel === 'critical';\r\n  }\r\n\r\n  /**\r\n   * Check if configuration is compliant\r\n   */\r\n  get isCompliant(): boolean {\r\n    return this.compliance?.status === 'compliant';\r\n  }\r\n\r\n  /**\r\n   * Check if configuration has drift\r\n   */\r\n  get hasDrift(): boolean {\r\n    return this.drift?.detected || false;\r\n  }\r\n\r\n  /**\r\n   * Get configuration age in hours\r\n   */\r\n  get ageInHours(): number {\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - this.capturedAt.getTime();\r\n    return Math.round(diffMs / (1000 * 60 * 60));\r\n  }\r\n\r\n  /**\r\n   * Calculate configuration hash\r\n   */\r\n  calculateHash(): string {\r\n    const configString = JSON.stringify(this.configData);\r\n    // In a real implementation, use a proper cryptographic hash\r\n    return `sha256-${Buffer.from(configString).toString('base64').slice(0, 32)}`;\r\n  }\r\n\r\n  /**\r\n   * Set as baseline configuration\r\n   */\r\n  setAsBaseline(userId: string): void {\r\n    this.isBaseline = true;\r\n    this.capturedBy = userId;\r\n  }\r\n\r\n  /**\r\n   * Add tag to configuration\r\n   */\r\n  addTag(tag: string): void {\r\n    if (!this.tags.includes(tag)) {\r\n      this.tags.push(tag);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove tag from configuration\r\n   */\r\n  removeTag(tag: string): void {\r\n    this.tags = this.tags.filter(t => t !== tag);\r\n  }\r\n\r\n  /**\r\n   * Update compliance status\r\n   */\r\n  updateCompliance(compliance: any): void {\r\n    this.compliance = {\r\n      ...this.compliance,\r\n      ...compliance,\r\n      lastAssessment: new Date().toISOString(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Detect drift from baseline\r\n   */\r\n  detectDrift(baseline: AssetConfiguration): void {\r\n    if (!baseline || baseline.id === this.id) {\r\n      this.drift = { detected: false, severity: 'low', driftType: 'configuration', affectedAreas: [] };\r\n      return;\r\n    }\r\n\r\n    const changes = this.compareConfigurations(baseline);\r\n    const driftScore = this.calculateDriftScore(changes);\r\n    \r\n    this.drift = {\r\n      detected: driftScore > 0,\r\n      severity: this.getDriftSeverity(driftScore),\r\n      driftType: 'configuration',\r\n      baselineId: baseline.id,\r\n      driftScore,\r\n      affectedAreas: this.getAffectedAreas(changes),\r\n      recommendations: this.generateDriftRecommendations(changes),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Compare configurations and detect changes\r\n   */\r\n  compareConfigurations(other: AssetConfiguration): any {\r\n    // Simplified comparison - in production, use a proper diff algorithm\r\n    const changes = {\r\n      added: [],\r\n      modified: [],\r\n      removed: [],\r\n    };\r\n\r\n    // This would contain the actual comparison logic\r\n    return changes;\r\n  }\r\n\r\n  /**\r\n   * Calculate drift score\r\n   */\r\n  private calculateDriftScore(changes: any): number {\r\n    const addedWeight = 1;\r\n    const modifiedWeight = 2;\r\n    const removedWeight = 3;\r\n\r\n    const score = (changes.added.length * addedWeight) +\r\n                  (changes.modified.length * modifiedWeight) +\r\n                  (changes.removed.length * removedWeight);\r\n\r\n    return Math.min(100, score);\r\n  }\r\n\r\n  /**\r\n   * Get drift severity based on score\r\n   */\r\n  private getDriftSeverity(score: number): 'low' | 'medium' | 'high' | 'critical' {\r\n    if (score >= 80) return 'critical';\r\n    if (score >= 60) return 'high';\r\n    if (score >= 30) return 'medium';\r\n    return 'low';\r\n  }\r\n\r\n  /**\r\n   * Get affected areas from changes\r\n   */\r\n  private getAffectedAreas(changes: any): string[] {\r\n    const areas = new Set<string>();\r\n    \r\n    // Analyze changes to determine affected areas\r\n    [...changes.added, ...changes.modified, ...changes.removed].forEach(change => {\r\n      const pathParts = change.path.split('.');\r\n      if (pathParts.length > 0) {\r\n        areas.add(pathParts[0]);\r\n      }\r\n    });\r\n\r\n    return Array.from(areas);\r\n  }\r\n\r\n  /**\r\n   * Generate drift recommendations\r\n   */\r\n  private generateDriftRecommendations(changes: any): string[] {\r\n    const recommendations: string[] = [];\r\n\r\n    if (changes.removed.length > 0) {\r\n      recommendations.push('Review removed configurations for security implications');\r\n    }\r\n\r\n    if (changes.added.length > 0) {\r\n      recommendations.push('Validate new configurations against security policies');\r\n    }\r\n\r\n    if (changes.modified.length > 0) {\r\n      recommendations.push('Assess modified configurations for compliance impact');\r\n    }\r\n\r\n    return recommendations;\r\n  }\r\n\r\n  /**\r\n   * Get configuration summary\r\n   */\r\n  getSummary(): any {\r\n    return {\r\n      id: this.id,\r\n      configType: this.configType,\r\n      name: this.name,\r\n      assetId: this.assetId,\r\n      configHash: this.configHash,\r\n      isBaseline: this.isBaseline,\r\n      changeDetected: this.changeDetected,\r\n      isRecent: this.isRecent,\r\n      hasCriticalChanges: this.hasCriticalChanges,\r\n      isCompliant: this.isCompliant,\r\n      hasDrift: this.hasDrift,\r\n      ageInHours: this.ageInHours,\r\n      capturedAt: this.capturedAt,\r\n      tags: this.tags,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Export configuration for reporting\r\n   */\r\n  exportForReporting(): any {\r\n    return {\r\n      configuration: this.getSummary(),\r\n      configData: this.configData,\r\n      changes: this.changes,\r\n      compliance: this.compliance,\r\n      drift: this.drift,\r\n      exportedAt: new Date().toISOString(),\r\n    };\r\n  }\r\n}\r\n"], "version": 3}