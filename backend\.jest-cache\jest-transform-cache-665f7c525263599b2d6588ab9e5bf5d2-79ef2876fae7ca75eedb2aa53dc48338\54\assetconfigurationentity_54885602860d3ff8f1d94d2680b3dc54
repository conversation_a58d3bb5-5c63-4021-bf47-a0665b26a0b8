45087ce9bb57ac1ca06e8f84e7a3e0d1
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetConfiguration = void 0;
const typeorm_1 = require("typeorm");
const asset_entity_1 = require("./asset.entity");
/**
 * Asset Configuration entity
 * Represents configuration snapshots and changes for assets
 */
let AssetConfiguration = class AssetConfiguration {
    /**
     * Check if configuration is recent
     */
    get isRecent() {
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        return this.capturedAt > oneDayAgo;
    }
    /**
     * Check if configuration has critical changes
     */
    get hasCriticalChanges() {
        return this.changes?.summary?.riskLevel === 'critical';
    }
    /**
     * Check if configuration is compliant
     */
    get isCompliant() {
        return this.compliance?.status === 'compliant';
    }
    /**
     * Check if configuration has drift
     */
    get hasDrift() {
        return this.drift?.detected || false;
    }
    /**
     * Get configuration age in hours
     */
    get ageInHours() {
        const now = new Date();
        const diffMs = now.getTime() - this.capturedAt.getTime();
        return Math.round(diffMs / (1000 * 60 * 60));
    }
    /**
     * Calculate configuration hash
     */
    calculateHash() {
        const configString = JSON.stringify(this.configData);
        // In a real implementation, use a proper cryptographic hash
        return `sha256-${Buffer.from(configString).toString('base64').slice(0, 32)}`;
    }
    /**
     * Set as baseline configuration
     */
    setAsBaseline(userId) {
        this.isBaseline = true;
        this.capturedBy = userId;
    }
    /**
     * Add tag to configuration
     */
    addTag(tag) {
        if (!this.tags.includes(tag)) {
            this.tags.push(tag);
        }
    }
    /**
     * Remove tag from configuration
     */
    removeTag(tag) {
        this.tags = this.tags.filter(t => t !== tag);
    }
    /**
     * Update compliance status
     */
    updateCompliance(compliance) {
        this.compliance = {
            ...this.compliance,
            ...compliance,
            lastAssessment: new Date().toISOString(),
        };
    }
    /**
     * Detect drift from baseline
     */
    detectDrift(baseline) {
        if (!baseline || baseline.id === this.id) {
            this.drift = { detected: false, severity: 'low', driftType: 'configuration', affectedAreas: [] };
            return;
        }
        const changes = this.compareConfigurations(baseline);
        const driftScore = this.calculateDriftScore(changes);
        this.drift = {
            detected: driftScore > 0,
            severity: this.getDriftSeverity(driftScore),
            driftType: 'configuration',
            baselineId: baseline.id,
            driftScore,
            affectedAreas: this.getAffectedAreas(changes),
            recommendations: this.generateDriftRecommendations(changes),
        };
    }
    /**
     * Compare configurations and detect changes
     */
    compareConfigurations(other) {
        // Simplified comparison - in production, use a proper diff algorithm
        const changes = {
            added: [],
            modified: [],
            removed: [],
        };
        // This would contain the actual comparison logic
        return changes;
    }
    /**
     * Calculate drift score
     */
    calculateDriftScore(changes) {
        const addedWeight = 1;
        const modifiedWeight = 2;
        const removedWeight = 3;
        const score = (changes.added.length * addedWeight) +
            (changes.modified.length * modifiedWeight) +
            (changes.removed.length * removedWeight);
        return Math.min(100, score);
    }
    /**
     * Get drift severity based on score
     */
    getDriftSeverity(score) {
        if (score >= 80)
            return 'critical';
        if (score >= 60)
            return 'high';
        if (score >= 30)
            return 'medium';
        return 'low';
    }
    /**
     * Get affected areas from changes
     */
    getAffectedAreas(changes) {
        const areas = new Set();
        // Analyze changes to determine affected areas
        [...changes.added, ...changes.modified, ...changes.removed].forEach(change => {
            const pathParts = change.path.split('.');
            if (pathParts.length > 0) {
                areas.add(pathParts[0]);
            }
        });
        return Array.from(areas);
    }
    /**
     * Generate drift recommendations
     */
    generateDriftRecommendations(changes) {
        const recommendations = [];
        if (changes.removed.length > 0) {
            recommendations.push('Review removed configurations for security implications');
        }
        if (changes.added.length > 0) {
            recommendations.push('Validate new configurations against security policies');
        }
        if (changes.modified.length > 0) {
            recommendations.push('Assess modified configurations for compliance impact');
        }
        return recommendations;
    }
    /**
     * Get configuration summary
     */
    getSummary() {
        return {
            id: this.id,
            configType: this.configType,
            name: this.name,
            assetId: this.assetId,
            configHash: this.configHash,
            isBaseline: this.isBaseline,
            changeDetected: this.changeDetected,
            isRecent: this.isRecent,
            hasCriticalChanges: this.hasCriticalChanges,
            isCompliant: this.isCompliant,
            hasDrift: this.hasDrift,
            ageInHours: this.ageInHours,
            capturedAt: this.capturedAt,
            tags: this.tags,
        };
    }
    /**
     * Export configuration for reporting
     */
    exportForReporting() {
        return {
            configuration: this.getSummary(),
            configData: this.configData,
            changes: this.changes,
            compliance: this.compliance,
            drift: this.drift,
            exportedAt: new Date().toISOString(),
        };
    }
};
exports.AssetConfiguration = AssetConfiguration;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], AssetConfiguration.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'config_type',
        type: 'enum',
        enum: [
            'system',
            'network',
            'security',
            'application',
            'database',
            'service',
            'registry',
            'file_system',
            'user_accounts',
            'group_policy',
            'firewall',
            'antivirus',
            'patch_level',
            'installed_software',
            'running_processes',
            'open_ports',
            'certificates',
            'cloud_config',
            'container_config',
            'custom',
        ],
    }),
    __metadata("design:type", String)
], AssetConfiguration.prototype, "configType", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], AssetConfiguration.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], AssetConfiguration.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'config_data', type: 'jsonb' }),
    __metadata("design:type", Object)
], AssetConfiguration.prototype, "configData", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'config_hash' }),
    __metadata("design:type", String)
], AssetConfiguration.prototype, "configHash", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_baseline', default: false }),
    __metadata("design:type", Boolean)
], AssetConfiguration.prototype, "isBaseline", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'change_detected', default: false }),
    __metadata("design:type", Boolean)
], AssetConfiguration.prototype, "changeDetected", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], AssetConfiguration.prototype, "changes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], AssetConfiguration.prototype, "compliance", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], AssetConfiguration.prototype, "drift", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, default: '{}' }),
    __metadata("design:type", Array)
], AssetConfiguration.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'captured_at', type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], AssetConfiguration.prototype, "capturedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'captured_by', type: 'uuid' }),
    __metadata("design:type", String)
], AssetConfiguration.prototype, "capturedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], AssetConfiguration.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], AssetConfiguration.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => asset_entity_1.Asset, asset => asset.configurations, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'asset_id' }),
    __metadata("design:type", typeof (_d = typeof asset_entity_1.Asset !== "undefined" && asset_entity_1.Asset) === "function" ? _d : Object)
], AssetConfiguration.prototype, "asset", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'asset_id', type: 'uuid' }),
    __metadata("design:type", String)
], AssetConfiguration.prototype, "assetId", void 0);
exports.AssetConfiguration = AssetConfiguration = __decorate([
    (0, typeorm_1.Entity)('asset_configurations'),
    (0, typeorm_1.Index)(['assetId']),
    (0, typeorm_1.Index)(['configType']),
    (0, typeorm_1.Index)(['capturedAt']),
    (0, typeorm_1.Index)(['isBaseline']),
    (0, typeorm_1.Index)(['changeDetected'])
], AssetConfiguration);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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