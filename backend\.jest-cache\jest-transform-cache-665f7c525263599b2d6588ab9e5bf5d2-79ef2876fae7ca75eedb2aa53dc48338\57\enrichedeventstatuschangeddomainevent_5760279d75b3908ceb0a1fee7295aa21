38296212ab91785f28d6855d3f10aaa7
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnrichedEventStatusChangedDomainEvent = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
const enriched_event_entity_1 = require("../entities/enriched-event.entity");
/**
 * Enriched Event Status Changed Domain Event
 *
 * Raised when an enriched security event's enrichment status changes.
 * This event triggers various downstream processes including:
 * - Status-specific workflow routing
 * - Quality monitoring and alerting
 * - Manual review queue updates
 * - Performance metrics collection
 * - Audit trail maintenance
 */
class EnrichedEventStatusChangedDomainEvent extends shared_kernel_1.BaseDomainEvent {
    constructor(aggregateId, eventData, options) {
        super(aggregateId, eventData, options);
    }
    /**
     * Get the previous enrichment status
     */
    get oldStatus() {
        return this.eventData.oldStatus;
    }
    /**
     * Get the new enrichment status
     */
    get newStatus() {
        return this.eventData.newStatus;
    }
    /**
     * Get the enrichment result
     */
    get result() {
        return this.eventData.result;
    }
    /**
     * Get the enrichment quality score
     */
    get enrichmentQualityScore() {
        return this.eventData.enrichmentQualityScore;
    }
    /**
     * Check if the event requires manual review
     */
    get requiresManualReview() {
        return this.eventData.requiresManualReview;
    }
    /**
     * Get the timestamp of the status change
     */
    get timestamp() {
        return this.eventData.timestamp || this.occurredOn;
    }
    /**
     * Get additional context about the status change
     */
    get context() {
        return this.eventData.context || {};
    }
    /**
     * Check if enrichment was completed successfully
     */
    isEnrichmentCompleted() {
        return this.newStatus === enriched_event_entity_1.EnrichmentStatus.COMPLETED;
    }
    /**
     * Check if enrichment failed
     */
    isEnrichmentFailed() {
        return this.newStatus === enriched_event_entity_1.EnrichmentStatus.FAILED;
    }
    /**
     * Check if enrichment is now in progress
     */
    isEnrichmentStarted() {
        return this.oldStatus === enriched_event_entity_1.EnrichmentStatus.PENDING &&
            this.newStatus === enriched_event_entity_1.EnrichmentStatus.IN_PROGRESS;
    }
    /**
     * Check if enrichment was skipped
     */
    isEnrichmentSkipped() {
        return this.newStatus === enriched_event_entity_1.EnrichmentStatus.SKIPPED;
    }
    /**
     * Check if enrichment is partial
     */
    isEnrichmentPartial() {
        return this.newStatus === enriched_event_entity_1.EnrichmentStatus.PARTIAL;
    }
    /**
     * Check if the status change indicates success
     */
    isSuccessfulStatusChange() {
        return [enriched_event_entity_1.EnrichmentStatus.COMPLETED, enriched_event_entity_1.EnrichmentStatus.PARTIAL].includes(this.newStatus);
    }
    /**
     * Check if the status change indicates failure
     */
    isFailureStatusChange() {
        return this.newStatus === enriched_event_entity_1.EnrichmentStatus.FAILED;
    }
    /**
     * Check if enrichment quality is high
     */
    hasHighEnrichmentQuality() {
        return (this.enrichmentQualityScore || 0) >= 70;
    }
    /**
     * Get enrichment success rate from result
     */
    getEnrichmentSuccessRate() {
        if (!this.result)
            return 0;
        const totalRules = this.result.appliedRules.length + this.result.failedRules.length;
        if (totalRules === 0)
            return 0;
        return (this.result.appliedRules.length / totalRules) * 100;
    }
    /**
     * Get processing duration from result
     */
    getProcessingDuration() {
        return this.result?.processingDurationMs || 0;
    }
    /**
     * Get confidence score from result
     */
    getConfidenceScore() {
        return this.result?.confidenceScore || 0;
    }
    /**
     * Get number of sources used from result
     */
    getSourcesUsed() {
        return this.result?.sourcesUsed || 0;
    }
    /**
     * Get data points enriched from result
     */
    getDataPointsEnriched() {
        return this.result?.dataPointsEnriched || 0;
    }
    /**
     * Check if there are enrichment warnings
     */
    hasWarnings() {
        return (this.result?.warnings?.length || 0) > 0;
    }
    /**
     * Check if there are enrichment errors
     */
    hasErrors() {
        return (this.result?.errors?.length || 0) > 0;
    }
    /**
     * Get status transition description
     */
    getStatusTransitionDescription() {
        const transitions = {
            [`${enriched_event_entity_1.EnrichmentStatus.PENDING}->${enriched_event_entity_1.EnrichmentStatus.IN_PROGRESS}`]: 'Enrichment started',
            [`${enriched_event_entity_1.EnrichmentStatus.IN_PROGRESS}->${enriched_event_entity_1.EnrichmentStatus.COMPLETED}`]: 'Enrichment completed successfully',
            [`${enriched_event_entity_1.EnrichmentStatus.IN_PROGRESS}->${enriched_event_entity_1.EnrichmentStatus.PARTIAL}`]: 'Enrichment completed with partial results',
            [`${enriched_event_entity_1.EnrichmentStatus.IN_PROGRESS}->${enriched_event_entity_1.EnrichmentStatus.FAILED}`]: 'Enrichment failed',
            [`${enriched_event_entity_1.EnrichmentStatus.PENDING}->${enriched_event_entity_1.EnrichmentStatus.SKIPPED}`]: 'Enrichment skipped',
            [`${enriched_event_entity_1.EnrichmentStatus.FAILED}->${enriched_event_entity_1.EnrichmentStatus.PENDING}`]: 'Enrichment reset for retry',
            [`${enriched_event_entity_1.EnrichmentStatus.FAILED}->${enriched_event_entity_1.EnrichmentStatus.SKIPPED}`]: 'Failed enrichment skipped',
        };
        const key = `${this.oldStatus}->${this.newStatus}`;
        return transitions[key] || `Status changed from ${this.oldStatus} to ${this.newStatus}`;
    }
    /**
     * Get event summary for handlers
     */
    getEventSummary() {
        return {
            enrichedEventId: this.aggregateId.toString(),
            oldStatus: this.oldStatus,
            newStatus: this.newStatus,
            statusTransition: this.getStatusTransitionDescription(),
            enrichmentQualityScore: this.enrichmentQualityScore,
            requiresManualReview: this.requiresManualReview,
            isEnrichmentCompleted: this.isEnrichmentCompleted(),
            isEnrichmentFailed: this.isEnrichmentFailed(),
            isEnrichmentStarted: this.isEnrichmentStarted(),
            isSuccessfulStatusChange: this.isSuccessfulStatusChange(),
            isFailureStatusChange: this.isFailureStatusChange(),
            hasHighEnrichmentQuality: this.hasHighEnrichmentQuality(),
            enrichmentSuccessRate: this.getEnrichmentSuccessRate(),
            processingDuration: this.getProcessingDuration(),
            confidenceScore: this.getConfidenceScore(),
            sourcesUsed: this.getSourcesUsed(),
            dataPointsEnriched: this.getDataPointsEnriched(),
            hasWarnings: this.hasWarnings(),
            hasErrors: this.hasErrors(),
        };
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            ...super.toJSON(),
            eventSummary: this.getEventSummary(),
        };
    }
}
exports.EnrichedEventStatusChangedDomainEvent = EnrichedEventStatusChangedDomainEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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