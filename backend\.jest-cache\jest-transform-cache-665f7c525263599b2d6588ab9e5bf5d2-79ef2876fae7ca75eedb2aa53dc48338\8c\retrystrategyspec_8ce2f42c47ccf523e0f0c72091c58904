751b8a88d0d4dac7900af66ba7aaa675
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const retry_strategy_1 = require("../../patterns/retry-strategy");
describe('Retry Strategy', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });
    describe('RetryStrategy class', () => {
        it('should succeed on first attempt when no error occurs', async () => {
            const strategy = new retry_strategy_1.RetryStrategy({ maxAttempts: 3 });
            let callCount = 0;
            const successfulOperation = async () => {
                callCount++;
                return `success-${callCount}`;
            };
            const result = await strategy.execute(successfulOperation);
            expect(result).toBe('success-1');
            expect(callCount).toBe(1);
        });
        it('should retry failed operations up to max attempts', async () => {
            const strategy = new retry_strategy_1.RetryStrategy({ maxAttempts: 3, baseDelay: 10 });
            let callCount = 0;
            const flakyOperation = async () => {
                callCount++;
                if (callCount < 3) {
                    throw new Error(`Attempt ${callCount} failed`);
                }
                return 'eventual success';
            };
            const result = await strategy.execute(flakyOperation);
            expect(result).toBe('eventual success');
            expect(callCount).toBe(3);
        });
        it('should throw last error after max attempts exceeded', async () => {
            const strategy = new retry_strategy_1.RetryStrategy({ maxAttempts: 2, baseDelay: 10 });
            let callCount = 0;
            const alwaysFailingOperation = async () => {
                callCount++;
                throw new Error(`Attempt ${callCount} failed`);
            };
            await expect(strategy.execute(alwaysFailingOperation)).rejects.toThrow('Attempt 2 failed');
            expect(callCount).toBe(2);
        });
        it('should apply exponential backoff delays', async () => {
            const delays = [];
            const originalSetTimeout = global.setTimeout;
            // Mock setTimeout to capture delays
            global.setTimeout = jest.fn().mockImplementation((callback, delay) => {
                delays.push(delay);
                return originalSetTimeout(callback, 0); // Execute immediately for test
            });
            const strategy = new retry_strategy_1.RetryStrategy({
                maxAttempts: 3,
                baseDelay: 100,
                backoffMultiplier: 2,
                jitter: false, // Disable jitter for predictable delays
            });
            let callCount = 0;
            const failingOperation = async () => {
                callCount++;
                if (callCount < 3) {
                    throw new Error('Retry needed');
                }
                return 'success';
            };
            await strategy.execute(failingOperation);
            expect(delays).toHaveLength(2); // Two retries
            expect(delays[0]).toBe(100); // First retry: baseDelay
            expect(delays[1]).toBe(200); // Second retry: baseDelay * 2
            // Restore original setTimeout
            global.setTimeout = originalSetTimeout;
        });
        it('should respect maximum delay limit', async () => {
            const delays = [];
            const originalSetTimeout = global.setTimeout;
            global.setTimeout = jest.fn().mockImplementation((callback, delay) => {
                delays.push(delay);
                return originalSetTimeout(callback, 0);
            });
            const strategy = new retry_strategy_1.RetryStrategy({
                maxAttempts: 4,
                baseDelay: 100,
                maxDelay: 150,
                backoffMultiplier: 2,
                jitter: false,
            });
            let callCount = 0;
            const failingOperation = async () => {
                callCount++;
                throw new Error('Always fails');
            };
            await expect(strategy.execute(failingOperation)).rejects.toThrow();
            expect(delays).toHaveLength(3);
            expect(delays[0]).toBe(100); // First retry: baseDelay
            expect(delays[1]).toBe(150); // Second retry: capped at maxDelay
            expect(delays[2]).toBe(150); // Third retry: capped at maxDelay
            global.setTimeout = originalSetTimeout;
        });
        it('should apply jitter to delays', async () => {
            const delays = [];
            const originalSetTimeout = global.setTimeout;
            const originalMathRandom = Math.random;
            // Mock Math.random to return predictable values
            Math.random = jest.fn()
                .mockReturnValueOnce(0.5) // First retry: 0.5 jitter
                .mockReturnValueOnce(0.8); // Second retry: 0.8 jitter
            global.setTimeout = jest.fn().mockImplementation((callback, delay) => {
                delays.push(delay);
                return originalSetTimeout(callback, 0);
            });
            const strategy = new retry_strategy_1.RetryStrategy({
                maxAttempts: 3,
                baseDelay: 100,
                backoffMultiplier: 2,
                jitter: true,
            });
            let callCount = 0;
            const failingOperation = async () => {
                callCount++;
                throw new Error('Always fails');
            };
            await expect(strategy.execute(failingOperation)).rejects.toThrow();
            expect(delays).toHaveLength(2);
            // With jitter, delay = baseDelay * (0.5 + random * 0.5)
            expect(delays[0]).toBe(75); // 100 * (0.5 + 0.5 * 0.5) = 75
            expect(delays[1]).toBe(180); // 200 * (0.5 + 0.8 * 0.5) = 180
            Math.random = originalMathRandom;
            global.setTimeout = originalSetTimeout;
        });
        it('should respect retryOn condition', async () => {
            class NetworkError extends Error {
                constructor(message) {
                    super(message);
                    this.name = 'NetworkError';
                }
            }
            const strategy = new retry_strategy_1.RetryStrategy({
                maxAttempts: 3,
                baseDelay: 10,
                retryOn: (error) => error instanceof NetworkError,
            });
            let callCount = 0;
            // Non-retryable error should fail immediately
            const nonRetryableOperation = async () => {
                callCount++;
                throw new Error('Validation error');
            };
            await expect(strategy.execute(nonRetryableOperation)).rejects.toThrow('Validation error');
            expect(callCount).toBe(1);
            // Reset call count
            callCount = 0;
            // Retryable error should be retried
            const retryableOperation = async () => {
                callCount++;
                throw new NetworkError('Network error');
            };
            await expect(strategy.execute(retryableOperation)).rejects.toThrow('Network error');
            expect(callCount).toBe(3);
        });
        it('should call onRetry callback', async () => {
            const onRetry = jest.fn();
            const strategy = new retry_strategy_1.RetryStrategy({
                maxAttempts: 3,
                baseDelay: 10,
                onRetry,
            });
            let callCount = 0;
            const failingOperation = async () => {
                callCount++;
                throw new Error(`Attempt ${callCount} failed`);
            };
            await expect(strategy.execute(failingOperation)).rejects.toThrow();
            expect(onRetry).toHaveBeenCalledTimes(2); // Called for each retry
            expect(onRetry).toHaveBeenNthCalledWith(1, expect.any(Error), 1);
            expect(onRetry).toHaveBeenNthCalledWith(2, expect.any(Error), 2);
        });
        it('should provide detailed execution results', async () => {
            const strategy = new retry_strategy_1.RetryStrategy({ maxAttempts: 3, baseDelay: 10 });
            let callCount = 0;
            const flakyOperation = async () => {
                callCount++;
                if (callCount < 3) {
                    throw new Error(`Attempt ${callCount} failed`);
                }
                return 'success after retries';
            };
            const result = await strategy.executeWithDetails(flakyOperation);
            expect(result.result).toBe('success after retries');
            expect(result.attempts).toBe(3);
            expect(result.totalDelay).toBeGreaterThan(0);
            expect(result.errors).toHaveLength(2);
            expect(result.errors[0].message).toBe('Attempt 1 failed');
            expect(result.errors[1].message).toBe('Attempt 2 failed');
        });
        it('should handle immediate success with executeWithDetails', async () => {
            const strategy = new retry_strategy_1.RetryStrategy({ maxAttempts: 3 });
            const successfulOperation = async () => 'immediate success';
            const result = await strategy.executeWithDetails(successfulOperation);
            expect(result.result).toBe('immediate success');
            expect(result.attempts).toBe(1);
            expect(result.totalDelay).toBe(0);
            expect(result.errors).toHaveLength(0);
        });
    });
    describe('RetryStrategies factory methods', () => {
        it('should create exponential backoff strategy', async () => {
            const strategy = retry_strategy_1.RetryStrategies.exponentialBackoff(3, 50);
            let callCount = 0;
            const flakyOperation = async () => {
                callCount++;
                if (callCount < 3) {
                    throw new Error('Retry needed');
                }
                return 'exponential success';
            };
            const result = await strategy.execute(flakyOperation);
            expect(result).toBe('exponential success');
            expect(callCount).toBe(3);
        });
        it('should create linear backoff strategy', async () => {
            const delays = [];
            const originalSetTimeout = global.setTimeout;
            global.setTimeout = jest.fn().mockImplementation((callback, delay) => {
                delays.push(delay);
                return originalSetTimeout(callback, 0);
            });
            const strategy = retry_strategy_1.RetryStrategies.linearBackoff(3, 100);
            let callCount = 0;
            const failingOperation = async () => {
                callCount++;
                throw new Error('Always fails');
            };
            await expect(strategy.execute(failingOperation)).rejects.toThrow();
            expect(delays).toHaveLength(2);
            expect(delays[0]).toBe(100); // Linear: same delay each time
            expect(delays[1]).toBe(100);
            global.setTimeout = originalSetTimeout;
        });
        it('should create fixed delay strategy', async () => {
            const delays = [];
            const originalSetTimeout = global.setTimeout;
            global.setTimeout = jest.fn().mockImplementation((callback, delay) => {
                delays.push(delay);
                return originalSetTimeout(callback, 0);
            });
            const strategy = retry_strategy_1.RetryStrategies.fixedDelay(3, 200);
            let callCount = 0;
            const failingOperation = async () => {
                callCount++;
                throw new Error('Always fails');
            };
            await expect(strategy.execute(failingOperation)).rejects.toThrow();
            expect(delays).toHaveLength(2);
            expect(delays[0]).toBe(200);
            expect(delays[1]).toBe(200);
            global.setTimeout = originalSetTimeout;
        });
        it('should create immediate retry strategy', async () => {
            const delays = [];
            const originalSetTimeout = global.setTimeout;
            global.setTimeout = jest.fn().mockImplementation((callback, delay) => {
                delays.push(delay);
                return originalSetTimeout(callback, 0);
            });
            const strategy = retry_strategy_1.RetryStrategies.immediate(3);
            let callCount = 0;
            const failingOperation = async () => {
                callCount++;
                throw new Error('Always fails');
            };
            await expect(strategy.execute(failingOperation)).rejects.toThrow();
            expect(delays).toHaveLength(2);
            expect(delays[0]).toBe(0); // No delay
            expect(delays[1]).toBe(0);
            global.setTimeout = originalSetTimeout;
        });
        it('should create error-specific retry strategy', async () => {
            class NetworkError extends Error {
                constructor(message) {
                    super(message);
                    this.name = 'NetworkError';
                }
            }
            class TimeoutError extends Error {
                constructor(message) {
                    super(message);
                    this.name = 'TimeoutError';
                }
            }
            const strategy = retry_strategy_1.RetryStrategies.onErrorTypes([NetworkError, TimeoutError], {
                maxAttempts: 3,
                baseDelay: 10,
            });
            let callCount = 0;
            // NetworkError should be retried
            const networkFailingOperation = async () => {
                callCount++;
                throw new NetworkError('Network failed');
            };
            await expect(strategy.execute(networkFailingOperation)).rejects.toThrow('Network failed');
            expect(callCount).toBe(3);
            // Reset call count
            callCount = 0;
            // TimeoutError should be retried
            const timeoutFailingOperation = async () => {
                callCount++;
                throw new TimeoutError('Timeout occurred');
            };
            await expect(strategy.execute(timeoutFailingOperation)).rejects.toThrow('Timeout occurred');
            expect(callCount).toBe(3);
            // Reset call count
            callCount = 0;
            // Other errors should not be retried
            const otherFailingOperation = async () => {
                callCount++;
                throw new Error('Other error');
            };
            await expect(strategy.execute(otherFailingOperation)).rejects.toThrow('Other error');
            expect(callCount).toBe(1);
        });
    });
    describe('edge cases and error handling', () => {
        it('should handle zero max attempts', async () => {
            const strategy = new retry_strategy_1.RetryStrategy({ maxAttempts: 0 });
            const operation = async () => 'should not execute';
            // With 0 max attempts, should not execute at all
            // This is an edge case - the implementation should handle it gracefully
            await expect(strategy.execute(operation)).rejects.toThrow();
        });
        it('should handle negative delays gracefully', async () => {
            const strategy = new retry_strategy_1.RetryStrategy({
                maxAttempts: 2,
                baseDelay: -100, // Negative delay
            });
            let callCount = 0;
            const failingOperation = async () => {
                callCount++;
                throw new Error('Always fails');
            };
            await expect(strategy.execute(failingOperation)).rejects.toThrow();
            expect(callCount).toBe(2); // Should still retry despite negative delay
        });
        it('should handle very large delays', async () => {
            const strategy = new retry_strategy_1.RetryStrategy({
                maxAttempts: 2,
                baseDelay: Number.MAX_SAFE_INTEGER,
                maxDelay: 100, // Should cap the delay
                jitter: false, // Disable jitter for predictable testing
            });
            const delays = [];
            const originalSetTimeout = global.setTimeout;
            global.setTimeout = jest.fn().mockImplementation((callback, delay) => {
                delays.push(delay);
                return originalSetTimeout(callback, 0);
            });
            let callCount = 0;
            const failingOperation = async () => {
                callCount++;
                throw new Error('Always fails');
            };
            await expect(strategy.execute(failingOperation)).rejects.toThrow();
            expect(delays[0]).toBe(100); // Should be capped at maxDelay
            global.setTimeout = originalSetTimeout;
        });
        it('should preserve error properties', async () => {
            const strategy = new retry_strategy_1.RetryStrategy({ maxAttempts: 2, baseDelay: 10 });
            const customError = new Error('Custom error');
            customError.name = 'CustomError';
            customError.customProperty = 'custom value';
            const failingOperation = async () => {
                throw customError;
            };
            try {
                await strategy.execute(failingOperation);
                fail('Should have thrown error');
            }
            catch (error) {
                expect(error).toBe(customError); // Should be the same error instance
                expect(error.customProperty).toBe('custom value');
            }
        });
        it('should handle async errors correctly', async () => {
            const strategy = new retry_strategy_1.RetryStrategy({ maxAttempts: 2, baseDelay: 10 });
            const asyncFailingOperation = async () => {
                await new Promise(resolve => setTimeout(resolve, 10));
                throw new Error('Async error');
            };
            let callCount = 0;
            const countingOperation = async () => {
                callCount++;
                return await asyncFailingOperation();
            };
            await expect(strategy.execute(countingOperation)).rejects.toThrow('Async error');
            expect(callCount).toBe(2);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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