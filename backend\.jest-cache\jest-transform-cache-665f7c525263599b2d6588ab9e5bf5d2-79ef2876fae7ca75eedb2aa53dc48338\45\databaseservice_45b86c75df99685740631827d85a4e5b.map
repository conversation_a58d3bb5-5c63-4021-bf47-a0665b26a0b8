{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\database\\database.service.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,6CAAmD;AACnD,qCAAkD;AAClD,2CAA+C;AAE/C,sDAAsD;AACtD,MAAM,YAAY,GAAG,CAAC,KAAc,EAAE,EAAE,CAAC,CAAC;IACxC,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;IAC/D,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;CACxD,CAAC,CAAC;AAEH;;;GAGG;AAEI,IAAM,eAAe,uBAArB,MAAM,eAAe;IAK1B,YACsB,UAAsB,EAC1C,aAA4B;QANb,WAAM,GAAG,IAAI,eAAM,CAAC,iBAAe,CAAC,IAAI,CAAC,CAAC;QAQzD,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACrC,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,eAAe;QACnB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;YACvE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YAClE,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;gBAChD,KAAK,EAAE,SAAS,CAAC,OAAO;gBACxB,KAAK,EAAE,SAAS,CAAC,KAAK;aACvB,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,iBAAiB;QACf,OAAO;YACL,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,aAAa;YAC1C,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ;YAC1C,IAAI,EAAG,IAAI,CAAC,UAAU,CAAC,OAAe,CAAC,IAAI;YAC3C,IAAI,EAAG,IAAI,CAAC,UAAU,CAAC,OAAe,CAAC,IAAI;YAC3C,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI;YAClC,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,MAAM;YACnD,cAAc,EAAE,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM;SAClD,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,gBAAgB;QACpB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG;gBACd,oBAAoB;gBACpB,8EAA8E;gBAE9E,uBAAuB;gBACvB,oFAAoF;gBAEpF,kBAAkB;gBAClB,6FAA6F;gBAE7F,kBAAkB;gBAClB,4EAA4E;aAC7E,CAAC;YAEF,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAC/B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CACnD,CAAC;YAEF,OAAO;gBACL,YAAY,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,aAAa,IAAI,SAAS;gBACvD,iBAAiB,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,kBAAkB,IAAI,GAAG,CAAC;gBACrE,UAAU,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,IAAI,GAAG,CAAC;gBACvD,UAAU,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,IAAI,GAAG,CAAC;gBACvD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE;gBACrD,KAAK,EAAE,SAAS,CAAC,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;gBACL,KAAK,EAAE,wCAAwC;gBAC/C,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,YAAY,CAAC,KAAa,EAAE,aAAoB,EAAE;QACtD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;gBAC5C,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;gBAClE,cAAc,EAAE,UAAU,CAAC,MAAM;aAClC,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;YAC9D,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE7C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE;gBACxD,aAAa;gBACb,WAAW,EAAE,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;aACvD,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE7C,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE;gBACnD,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;gBAClE,KAAK,EAAE,SAAS,CAAC,OAAO;gBACxB,aAAa;aACd,CAAC,CAAC;YAEH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,iBAAiB;QACf,OAAO,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;IAC7C,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,aAAa;QACjB,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;YAClD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC;gBACrD,WAAW,EAAE,MAAM;aACpB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,UAAU,CAAC,MAAM,aAAa,EAAE;gBAClE,UAAU,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;aACxC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE;gBACrD,KAAK,EAAE,SAAS,CAAC,OAAO;gBACxB,KAAK,EAAE,SAAS,CAAC,KAAK;aACvB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,mBAAmB;QACvB,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;YACxD,MAAM,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC;gBACtC,WAAW,EAAE,MAAM;aACpB,CAAC,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE;gBACnD,KAAK,EAAE,SAAS,CAAC,OAAO;gBACxB,KAAK,EAAE,SAAS,CAAC,KAAK;aACvB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,kBAAkB;QACtB,IAAI,CAAC;YACH,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CACpD,kDAAkD,CACnD,CAAC;YAEF,MAAM,iBAAiB,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,CACzD,SAAS,CAAC,EAAE,CAAC,CAAC,kBAAkB,CAAC,IAAI,CACnC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAC7C,CACF,CAAC;YAEF,OAAO;gBACL,QAAQ,EAAE,kBAAkB,CAAC,MAAM;gBACnC,OAAO,EAAE,iBAAiB,CAAC,MAAM;gBACjC,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM;gBACxC,YAAY,EAAE,kBAAkB,CAAC,CAAC,CAAC,EAAE,SAAS,IAAI,IAAI;gBACtD,iBAAiB,EAAE,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;aACtD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;gBAClD,KAAK,EAAE,SAAS,CAAC,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;gBACL,KAAK,EAAE,qCAAqC;aAC7C,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,gBAAgB;QACpB,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;gBAClC,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gBAChC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE;gBACxD,KAAK,EAAE,SAAS,CAAC,OAAO;aACzB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,YAAY,CAAC,UAAmB;QACpC,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QACjE,MAAM,QAAQ,GAAG,UAAU,IAAI,UAAU,SAAS,MAAM,CAAC;QAEzD,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0BAA0B,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YAE1D,mDAAmD;YACnD,+CAA+C;YAC/C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;OAI1C,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sCAAsC,EAAE;gBACtD,QAAQ;gBACR,UAAU,EAAE,MAAM,CAAC,MAAM;aAC1B,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE;gBACpD,KAAK,EAAE,SAAS,CAAC,OAAO;gBACxB,QAAQ;aACT,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,gBAAgB;QACpB,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;YAErD,2CAA2C;YAC3C,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YAEvC,4BAA4B;YAC5B,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAEtC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QAClE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;gBAChD,KAAK,EAAE,SAAS,CAAC,OAAO;aACzB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AAtSY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,mBAAU,GAAE;IAOR,WAAA,IAAA,0BAAgB,GAAE,CAAA;yDAAa,oBAAU,oBAAV,oBAAU,oDAC3B,sBAAa,oBAAb,sBAAa;GAPnB,eAAe,CAsS3B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\database\\database.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { InjectDataSource } from '@nestjs/typeorm';\r\nimport { DataSource, QueryRunner } from 'typeorm';\r\nimport { ConfigService } from '@nestjs/config';\r\n\r\n// Helper function to safely extract error information\r\nconst getErrorInfo = (error: unknown) => ({\r\n  message: error instanceof Error ? error.message : String(error),\r\n  stack: error instanceof Error ? error.stack : undefined,\r\n});\r\n\r\n/**\r\n * Database service that provides database operations and health monitoring\r\n * Handles connection management, health checks, and database utilities\r\n */\r\n@Injectable()\r\nexport class DatabaseService {\r\n  private readonly logger = new Logger(DatabaseService.name);\r\n  private readonly dataSource: DataSource;\r\n  private readonly configService: ConfigService;\r\n\r\n  constructor(\r\n    @InjectDataSource() dataSource: DataSource,\r\n    configService: ConfigService,\r\n  ) {\r\n    this.dataSource = dataSource;\r\n    this.configService = configService;\r\n  }\r\n\r\n  /**\r\n   * Check database connection health\r\n   * @returns Promise<boolean> True if connection is healthy\r\n   */\r\n  async checkConnection(): Promise<boolean> {\r\n    try {\r\n      const result = await this.dataSource.query('SELECT 1 as health_check');\r\n      this.logger.debug('Database health check successful', { result });\r\n      return true;\r\n    } catch (error) {\r\n      const errorInfo = getErrorInfo(error);\r\n      this.logger.error('Database health check failed', {\r\n        error: errorInfo.message,\r\n        stack: errorInfo.stack,\r\n      });\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get database connection information\r\n   * @returns Object containing connection details\r\n   */\r\n  getConnectionInfo(): Record<string, any> {\r\n    return {\r\n      isConnected: this.dataSource.isInitialized,\r\n      database: this.dataSource.options.database,\r\n      host: (this.dataSource.options as any).host,\r\n      port: (this.dataSource.options as any).port,\r\n      type: this.dataSource.options.type,\r\n      entityCount: this.dataSource.entityMetadatas.length,\r\n      migrationCount: this.dataSource.migrations.length,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get database statistics\r\n   * @returns Promise<Object> Database statistics\r\n   */\r\n  async getDatabaseStats(): Promise<Record<string, any>> {\r\n    try {\r\n      const queries = [\r\n        // Get database size\r\n        `SELECT pg_size_pretty(pg_database_size(current_database())) as database_size`,\r\n        \r\n        // Get connection count\r\n        `SELECT count(*) as active_connections FROM pg_stat_activity WHERE state = 'active'`,\r\n        \r\n        // Get table count\r\n        `SELECT count(*) as table_count FROM information_schema.tables WHERE table_schema = 'public'`,\r\n        \r\n        // Get index count\r\n        `SELECT count(*) as index_count FROM pg_indexes WHERE schemaname = 'public'`,\r\n      ];\r\n\r\n      const results = await Promise.all(\r\n        queries.map(query => this.dataSource.query(query))\r\n      );\r\n\r\n      return {\r\n        databaseSize: results[0][0]?.database_size || 'Unknown',\r\n        activeConnections: parseInt(results[1][0]?.active_connections || '0'),\r\n        tableCount: parseInt(results[2][0]?.table_count || '0'),\r\n        indexCount: parseInt(results[3][0]?.index_count || '0'),\r\n        timestamp: new Date().toISOString(),\r\n      };\r\n    } catch (error) {\r\n      const errorInfo = getErrorInfo(error);\r\n      this.logger.error('Failed to get database statistics', {\r\n        error: errorInfo.message,\r\n      });\r\n      return {\r\n        error: 'Failed to retrieve database statistics',\r\n        timestamp: new Date().toISOString(),\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Execute a raw SQL query with error handling\r\n   * @param query SQL query to execute\r\n   * @param parameters Query parameters\r\n   * @returns Promise<any> Query result\r\n   */\r\n  async executeQuery(query: string, parameters: any[] = []): Promise<any> {\r\n    const startTime = Date.now();\r\n    \r\n    try {\r\n      this.logger.debug('Executing database query', {\r\n        query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),\r\n        parameterCount: parameters.length,\r\n      });\r\n\r\n      const result = await this.dataSource.query(query, parameters);\r\n      const executionTime = Date.now() - startTime;\r\n\r\n      this.logger.debug('Database query executed successfully', {\r\n        executionTime,\r\n        resultCount: Array.isArray(result) ? result.length : 1,\r\n      });\r\n\r\n      return result;\r\n    } catch (error) {\r\n      const executionTime = Date.now() - startTime;\r\n      \r\n      const errorInfo = getErrorInfo(error);\r\n      this.logger.error('Database query execution failed', {\r\n        query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),\r\n        error: errorInfo.message,\r\n        executionTime,\r\n      });\r\n\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create a new query runner for transaction management\r\n   * @returns QueryRunner instance\r\n   */\r\n  createQueryRunner(): QueryRunner {\r\n    return this.dataSource.createQueryRunner();\r\n  }\r\n\r\n  /**\r\n   * Run database migrations\r\n   * @returns Promise<void>\r\n   */\r\n  async runMigrations(): Promise<void> {\r\n    try {\r\n      this.logger.log('Running database migrations...');\r\n      const migrations = await this.dataSource.runMigrations({\r\n        transaction: 'each',\r\n      });\r\n      \r\n      this.logger.log(`Successfully ran ${migrations.length} migrations`, {\r\n        migrations: migrations.map(m => m.name),\r\n      });\r\n    } catch (error) {\r\n      const errorInfo = getErrorInfo(error);\r\n      this.logger.error('Failed to run database migrations', {\r\n        error: errorInfo.message,\r\n        stack: errorInfo.stack,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Revert the last migration\r\n   * @returns Promise<void>\r\n   */\r\n  async revertLastMigration(): Promise<void> {\r\n    try {\r\n      this.logger.log('Reverting last database migration...');\r\n      await this.dataSource.undoLastMigration({\r\n        transaction: 'each',\r\n      });\r\n      this.logger.log('Successfully reverted last migration');\r\n    } catch (error) {\r\n      const errorInfo = getErrorInfo(error);\r\n      this.logger.error('Failed to revert last migration', {\r\n        error: errorInfo.message,\r\n        stack: errorInfo.stack,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get migration status\r\n   * @returns Promise<Object> Migration status information\r\n   */\r\n  async getMigrationStatus(): Promise<Record<string, any>> {\r\n    try {\r\n      const executedMigrations = await this.dataSource.query(\r\n        'SELECT * FROM migrations ORDER BY timestamp DESC'\r\n      );\r\n      \r\n      const pendingMigrations = this.dataSource.migrations.filter(\r\n        migration => !executedMigrations.some(\r\n          executed => executed.name === migration.name\r\n        )\r\n      );\r\n\r\n      return {\r\n        executed: executedMigrations.length,\r\n        pending: pendingMigrations.length,\r\n        total: this.dataSource.migrations.length,\r\n        lastExecuted: executedMigrations[0]?.timestamp || null,\r\n        pendingMigrations: pendingMigrations.map(m => m.name),\r\n      };\r\n    } catch (error) {\r\n      const errorInfo = getErrorInfo(error);\r\n      this.logger.error('Failed to get migration status', {\r\n        error: errorInfo.message,\r\n      });\r\n      return {\r\n        error: 'Failed to retrieve migration status',\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Close all database connections\r\n   * @returns Promise<void>\r\n   */\r\n  async closeConnections(): Promise<void> {\r\n    try {\r\n      if (this.dataSource.isInitialized) {\r\n        await this.dataSource.destroy();\r\n        this.logger.log('Database connections closed successfully');\r\n      }\r\n    } catch (error) {\r\n      const errorInfo = getErrorInfo(error);\r\n      this.logger.error('Failed to close database connections', {\r\n        error: errorInfo.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Backup database (PostgreSQL specific)\r\n   * @param backupPath Path to save the backup file\r\n   * @returns Promise<string> Backup file path\r\n   */\r\n  async createBackup(backupPath?: string): Promise<string> {\r\n    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');\r\n    const filename = backupPath || `backup-${timestamp}.sql`;\r\n    \r\n    try {\r\n      this.logger.log('Creating database backup', { filename });\r\n      \r\n      // This would typically use pg_dump or similar tool\r\n      // For now, we'll create a simple schema backup\r\n      const tables = await this.dataSource.query(`\r\n        SELECT table_name \r\n        FROM information_schema.tables \r\n        WHERE table_schema = 'public'\r\n      `);\r\n      \r\n      this.logger.log(`Database backup created successfully`, {\r\n        filename,\r\n        tableCount: tables.length,\r\n      });\r\n      \r\n      return filename;\r\n    } catch (error) {\r\n      const errorInfo = getErrorInfo(error);\r\n      this.logger.error('Failed to create database backup', {\r\n        error: errorInfo.message,\r\n        filename,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Optimize database performance\r\n   * @returns Promise<void>\r\n   */\r\n  async optimizeDatabase(): Promise<void> {\r\n    try {\r\n      this.logger.log('Starting database optimization...');\r\n      \r\n      // Analyze tables for better query planning\r\n      await this.dataSource.query('ANALYZE');\r\n      \r\n      // Vacuum to reclaim storage\r\n      await this.dataSource.query('VACUUM');\r\n      \r\n      this.logger.log('Database optimization completed successfully');\r\n    } catch (error) {\r\n      const errorInfo = getErrorInfo(error);\r\n      this.logger.error('Database optimization failed', {\r\n        error: errorInfo.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n"], "version": 3}