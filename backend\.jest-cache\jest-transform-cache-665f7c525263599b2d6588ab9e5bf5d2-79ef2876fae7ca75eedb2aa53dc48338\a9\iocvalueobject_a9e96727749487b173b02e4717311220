139b102f04dad04bebb808b2356fe4c0
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IOC = exports.IOCSeverity = exports.IOCConfidence = exports.IOCType = void 0;
const base_value_object_1 = require("../../../../../shared-kernel/value-objects/base-value-object");
/**
 * IOC (Indicator of Compromise) Type
 */
var IOCType;
(function (IOCType) {
    IOCType["IP_ADDRESS"] = "ip_address";
    IOCType["DOMAIN"] = "domain";
    IOCType["URL"] = "url";
    IOCType["FILE_HASH"] = "file_hash";
    IOCType["EMAIL"] = "email";
    IOCType["USER_AGENT"] = "user_agent";
    IOCType["REGISTRY_KEY"] = "registry_key";
    IOCType["FILE_PATH"] = "file_path";
    IOCType["PROCESS_NAME"] = "process_name";
    IOCType["CERTIFICATE"] = "certificate";
    IOCType["MUTEX"] = "mutex";
    IOCType["PIPE"] = "pipe";
    IOCType["SERVICE"] = "service";
    IOCType["YARA_RULE"] = "yara_rule";
    IOCType["SIGMA_RULE"] = "sigma_rule";
    IOCType["CVE"] = "cve";
    IOCType["ATTACK_PATTERN"] = "attack_pattern";
    IOCType["MALWARE_FAMILY"] = "malware_family";
    IOCType["CAMPAIGN"] = "campaign";
    IOCType["THREAT_ACTOR"] = "threat_actor";
})(IOCType || (exports.IOCType = IOCType = {}));
/**
 * IOC Confidence Level
 */
var IOCConfidence;
(function (IOCConfidence) {
    IOCConfidence["LOW"] = "low";
    IOCConfidence["MEDIUM"] = "medium";
    IOCConfidence["HIGH"] = "high";
    IOCConfidence["CONFIRMED"] = "confirmed";
})(IOCConfidence || (exports.IOCConfidence = IOCConfidence = {}));
/**
 * IOC Severity Level
 */
var IOCSeverity;
(function (IOCSeverity) {
    IOCSeverity["INFO"] = "info";
    IOCSeverity["LOW"] = "low";
    IOCSeverity["MEDIUM"] = "medium";
    IOCSeverity["HIGH"] = "high";
    IOCSeverity["CRITICAL"] = "critical";
})(IOCSeverity || (exports.IOCSeverity = IOCSeverity = {}));
/**
 * IOC (Indicator of Compromise) Value Object
 *
 * Represents a security indicator that can be used to detect threats.
 * Provides validation, classification, and threat intelligence capabilities.
 *
 * Key features:
 * - Multiple IOC types support (IP, domain, hash, etc.)
 * - Confidence and severity scoring
 * - Temporal tracking (first/last seen, expiration)
 * - Source attribution and tagging
 * - Validation and normalization
 * - Threat intelligence integration
 */
class IOC extends base_value_object_1.BaseValueObject {
    constructor(props) {
        super(props);
    }
    validate() {
        if (!this._value.type) {
            throw new Error('IOC must have a type');
        }
        if (!Object.values(IOCType).includes(this._value.type)) {
            throw new Error(`Invalid IOC type: ${this._value.type}`);
        }
        if (!this._value.value || this._value.value.trim().length === 0) {
            throw new Error('IOC must have a non-empty value');
        }
        if (!Object.values(IOCConfidence).includes(this._value.confidence)) {
            throw new Error(`Invalid IOC confidence: ${this._value.confidence}`);
        }
        if (!Object.values(IOCSeverity).includes(this._value.severity)) {
            throw new Error(`Invalid IOC severity: ${this._value.severity}`);
        }
        // Validate the indicator value based on type
        this.validateIndicatorValue();
        // Validate temporal relationships
        if (this._value.firstSeen && this._value.lastSeen &&
            this._value.firstSeen > this._value.lastSeen) {
            throw new Error('IOC firstSeen cannot be after lastSeen');
        }
        if (this._value.expiresAt && this._value.expiresAt <= new Date()) {
            // Allow expired IOCs but warn about them
            console.warn(`IOC ${this._value.value} has expired`);
        }
    }
    validateIndicatorValue() {
        const value = this._value.value.trim();
        switch (this._value.type) {
            case IOCType.IP_ADDRESS:
                this.validateIPAddress(value);
                break;
            case IOCType.DOMAIN:
                this.validateDomain(value);
                break;
            case IOCType.URL:
                this.validateURL(value);
                break;
            case IOCType.FILE_HASH:
                this.validateFileHash(value);
                break;
            case IOCType.EMAIL:
                this.validateEmail(value);
                break;
            case IOCType.CVE:
                this.validateCVE(value);
                break;
            // Add more validations as needed
        }
    }
    validateIPAddress(value) {
        const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;
        if (!ipv4Regex.test(value) && !ipv6Regex.test(value)) {
            throw new Error(`Invalid IP address format: ${value}`);
        }
    }
    validateDomain(value) {
        const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
        if (!domainRegex.test(value) || value.length > 253) {
            throw new Error(`Invalid domain format: ${value}`);
        }
    }
    validateURL(value) {
        try {
            new URL(value);
        }
        catch {
            throw new Error(`Invalid URL format: ${value}`);
        }
    }
    validateFileHash(value) {
        const md5Regex = /^[a-fA-F0-9]{32}$/;
        const sha1Regex = /^[a-fA-F0-9]{40}$/;
        const sha256Regex = /^[a-fA-F0-9]{64}$/;
        if (!md5Regex.test(value) && !sha1Regex.test(value) && !sha256Regex.test(value)) {
            throw new Error(`Invalid file hash format: ${value}`);
        }
    }
    validateEmail(value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            throw new Error(`Invalid email format: ${value}`);
        }
    }
    validateCVE(value) {
        const cveRegex = /^CVE-\d{4}-\d{4,}$/;
        if (!cveRegex.test(value)) {
            throw new Error(`Invalid CVE format: ${value}`);
        }
    }
    /**
     * Create an IOC with required fields
     */
    static create(type, value, confidence, severity, options) {
        return new IOC({
            type,
            value: value.trim(),
            confidence,
            severity,
            firstSeen: new Date(),
            ...options,
        });
    }
    /**
     * Create an IP address IOC
     */
    static createIPAddress(ipAddress, confidence, severity, options) {
        return IOC.create(IOCType.IP_ADDRESS, ipAddress, confidence, severity, options);
    }
    /**
     * Create a domain IOC
     */
    static createDomain(domain, confidence, severity, options) {
        return IOC.create(IOCType.DOMAIN, domain, confidence, severity, options);
    }
    /**
     * Create a file hash IOC
     */
    static createFileHash(hash, confidence, severity, options) {
        return IOC.create(IOCType.FILE_HASH, hash, confidence, severity, options);
    }
    /**
     * Get IOC type
     */
    get type() {
        return this._value.type;
    }
    /**
     * Get IOC value
     */
    get value() {
        return this._value.value;
    }
    /**
     * Get confidence level
     */
    get confidence() {
        return this._value.confidence;
    }
    /**
     * Get severity level
     */
    get severity() {
        return this._value.severity;
    }
    /**
     * Get description
     */
    get description() {
        return this._value.description;
    }
    /**
     * Get source
     */
    get source() {
        return this._value.source;
    }
    /**
     * Get tags
     */
    get tags() {
        return this._value.tags || [];
    }
    /**
     * Get first seen date
     */
    get firstSeen() {
        return this._value.firstSeen;
    }
    /**
     * Get last seen date
     */
    get lastSeen() {
        return this._value.lastSeen;
    }
    /**
     * Get expiration date
     */
    get expiresAt() {
        return this._value.expiresAt;
    }
    /**
     * Get context metadata
     */
    get context() {
        return this._value.context || {};
    }
    /**
     * Check if IOC is expired
     */
    isExpired() {
        return this._value.expiresAt ? this._value.expiresAt <= new Date() : false;
    }
    /**
     * Check if IOC is active (not expired)
     */
    isActive() {
        return !this.isExpired();
    }
    /**
     * Check if IOC is high confidence
     */
    isHighConfidence() {
        return this._value.confidence === IOCConfidence.HIGH ||
            this._value.confidence === IOCConfidence.CONFIRMED;
    }
    /**
     * Check if IOC is high severity
     */
    isHighSeverity() {
        return this._value.severity === IOCSeverity.HIGH ||
            this._value.severity === IOCSeverity.CRITICAL;
    }
    /**
     * Check if IOC is critical
     */
    isCritical() {
        return this._value.severity === IOCSeverity.CRITICAL;
    }
    /**
     * Check if IOC has specific tag
     */
    hasTag(tag) {
        return this.tags.includes(tag);
    }
    /**
     * Get IOC age in days
     */
    getAge() {
        if (!this._value.firstSeen) {
            return null;
        }
        return Math.floor((Date.now() - this._value.firstSeen.getTime()) / (1000 * 60 * 60 * 24));
    }
    /**
     * Get days until expiration
     */
    getDaysUntilExpiration() {
        if (!this._value.expiresAt) {
            return null;
        }
        return Math.floor((this._value.expiresAt.getTime() - Date.now()) / (1000 * 60 * 60 * 24));
    }
    /**
     * Get threat score (0-100)
     */
    getThreatScore() {
        let score = 0;
        // Base score from severity
        switch (this._value.severity) {
            case IOCSeverity.CRITICAL:
                score += 60;
                break;
            case IOCSeverity.HIGH:
                score += 45;
                break;
            case IOCSeverity.MEDIUM:
                score += 30;
                break;
            case IOCSeverity.LOW:
                score += 15;
                break;
            case IOCSeverity.INFO:
                score += 5;
                break;
        }
        // Confidence multiplier
        switch (this._value.confidence) {
            case IOCConfidence.CONFIRMED:
                score *= 1.0;
                break;
            case IOCConfidence.HIGH:
                score *= 0.9;
                break;
            case IOCConfidence.MEDIUM:
                score *= 0.7;
                break;
            case IOCConfidence.LOW:
                score *= 0.5;
                break;
        }
        // Age penalty (older IOCs are less relevant)
        const age = this.getAge();
        if (age !== null) {
            if (age > 365)
                score *= 0.5; // Very old
            else if (age > 90)
                score *= 0.7; // Old
            else if (age > 30)
                score *= 0.9; // Somewhat old
        }
        // Expiration penalty
        if (this.isExpired()) {
            score *= 0.1;
        }
        return Math.min(100, Math.max(0, Math.round(score)));
    }
    /**
     * Get hash type for file hashes
     */
    getHashType() {
        if (this._value.type !== IOCType.FILE_HASH) {
            return null;
        }
        const length = this._value.value.length;
        switch (length) {
            case 32: return 'md5';
            case 40: return 'sha1';
            case 64: return 'sha256';
            default: return null;
        }
    }
    /**
     * Create a new IOC with updated last seen
     */
    updateLastSeen(lastSeen = new Date()) {
        return new IOC({
            ...this._value,
            lastSeen,
        });
    }
    /**
     * Create a new IOC with additional tags
     */
    withTags(newTags) {
        const existingTags = this.tags;
        const allTags = [...new Set([...existingTags, ...newTags])];
        return new IOC({
            ...this._value,
            tags: allTags,
        });
    }
    /**
     * Create a new IOC with updated context
     */
    withContext(context) {
        return new IOC({
            ...this._value,
            context: { ...this.context, ...context },
        });
    }
    /**
     * Create a new IOC with updated expiration
     */
    withExpiration(expiresAt) {
        return new IOC({
            ...this._value,
            expiresAt,
        });
    }
    /**
     * Get IOC summary for analysis
     */
    getSummary() {
        return {
            type: this._value.type,
            value: this._value.value,
            confidence: this._value.confidence,
            severity: this._value.severity,
            threatScore: this.getThreatScore(),
            isActive: this.isActive(),
            isHighConfidence: this.isHighConfidence(),
            isHighSeverity: this.isHighSeverity(),
            age: this.getAge(),
            daysUntilExpiration: this.getDaysUntilExpiration(),
            tagCount: this.tags.length,
        };
    }
    /**
     * Compare IOCs for equality
     */
    equals(other) {
        if (!other) {
            return false;
        }
        if (this === other) {
            return true;
        }
        return this._value.type === other._value.type &&
            this._value.value === other._value.value;
    }
    /**
     * Get string representation
     */
    toString() {
        return `${this._value.type}:${this._value.value}`;
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            type: this._value.type,
            value: this._value.value,
            confidence: this._value.confidence,
            severity: this._value.severity,
            description: this._value.description,
            source: this._value.source,
            tags: this._value.tags,
            firstSeen: this._value.firstSeen?.toISOString(),
            lastSeen: this._value.lastSeen?.toISOString(),
            expiresAt: this._value.expiresAt?.toISOString(),
            context: this._value.context,
            summary: this.getSummary(),
            hashType: this.getHashType(),
        };
    }
    /**
     * Create IOC from JSON
     */
    static fromJSON(json) {
        return new IOC({
            type: json.type,
            value: json.value,
            confidence: json.confidence,
            severity: json.severity,
            description: json.description,
            source: json.source,
            tags: json.tags,
            firstSeen: json.firstSeen ? new Date(json.firstSeen) : undefined,
            lastSeen: json.lastSeen ? new Date(json.lastSeen) : undefined,
            expiresAt: json.expiresAt ? new Date(json.expiresAt) : undefined,
            context: json.context,
        });
    }
    /**
     * Validate IOC format without creating instance
     */
    static isValid(type, value) {
        try {
            new IOC({
                type,
                value,
                confidence: IOCConfidence.MEDIUM,
                severity: IOCSeverity.MEDIUM,
            });
            return true;
        }
        catch {
            return false;
        }
    }
}
exports.IOC = IOC;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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