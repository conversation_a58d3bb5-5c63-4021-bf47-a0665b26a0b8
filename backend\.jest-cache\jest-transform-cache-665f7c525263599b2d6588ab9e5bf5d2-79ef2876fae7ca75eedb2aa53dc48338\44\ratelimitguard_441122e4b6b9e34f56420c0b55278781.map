{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\common\\guards\\rate-limit.guard.ts", "mappings": ";;;;;;;;;;;;;;AAAA,2CAOwB;AACxB,uCAAyC;AACzC,2CAA+C;AAE/C,6EAAqF;AAgCrF;;GAEG;AACH,MAAM,oBAAoB;IAA1B;QACU,UAAK,GAAG,IAAI,GAAG,EAAyB,CAAC;QACzC,WAAM,GAAG,IAAI,GAAG,EAA0B,CAAC;IAqDrD,CAAC;IAnDC,KAAK,CAAC,GAAG,CAAC,GAAW;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;IACrC,CAAC;IAED,KAAK,CAAC,GAAG,CAAC,GAAW,EAAE,IAAmB,EAAE,GAAW;QACrD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAE1B,uBAAuB;QACvB,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAI,aAAa,EAAE,CAAC;YAClB,YAAY,CAAC,aAAa,CAAC,CAAC;QAC9B,CAAC;QAED,yCAAyC;QACzC,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE;YAC5B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACvB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC1B,CAAC,EAAE,GAAG,CAAC,CAAC;QAER,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,GAAW,EAAE,QAAgB;QAC3C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAErC,IAAI,CAAC,QAAQ,IAAI,GAAG,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC;YAC1C,oBAAoB;YACpB,MAAM,IAAI,GAAkB;gBAC1B,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,GAAG,GAAG,QAAQ;gBACzB,YAAY,EAAE,GAAG;aAClB,CAAC;YACF,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;YACpC,OAAO,IAAI,CAAC;QACd,CAAC;aAAM,CAAC;YACN,4BAA4B;YAC5B,QAAQ,CAAC,KAAK,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,EAAE,QAAQ,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC;YACxD,OAAO,QAAQ,CAAC;QAClB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,GAAW;QACrB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACvB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACnC,IAAI,KAAK,EAAE,CAAC;YACV,YAAY,CAAC,KAAK,CAAC,CAAC;YACpB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;CACF;AAED;;;GAGG;AAEI,IAAM,cAAc,sBAApB,MAAM,cAAc;IAIzB,YACmB,SAAoB,EACpB,aAA4B;QAD5B,cAAS,GAAT,SAAS,CAAW;QACpB,kBAAa,GAAb,aAAa,CAAe;QAL9B,WAAM,GAAG,IAAI,eAAM,CAAC,gBAAc,CAAC,IAAI,CAAC,CAAC;QAOxD,0CAA0C;QAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,kBAAkB,EAAE,QAAQ,CAAC,CAAC;QAC/E,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAAyB;QACzC,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAwB,CAAC;QAC1E,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,WAAW,EAAY,CAAC;QAEhE,8CAA8C;QAC9C,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CACtD,qCAAc,EACd,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAC3C,CAAC;QAEF,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC,CAAC,8BAA8B;QAC7C,CAAC;QAED,IAAI,CAAC;YACH,2CAA2C;YAC3C,IAAI,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,eAAe,CAAC,EAAE,CAAC;gBACpD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,0BAA0B;YAC1B,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;YAE7D,mBAAmB;YACnB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,eAAe,CAAC,CAAC;YAE/D,yBAAyB;YACzB,IAAI,eAAe,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;gBACtC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC;YAC9D,CAAC;YAED,0BAA0B;YAC1B,IAAI,MAAM,CAAC,KAAK,GAAG,eAAe,CAAC,GAAG,EAAE,CAAC;gBACvC,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,eAAe,EAAE,MAAM,CAAC,CAAC;gBACjE,MAAM,IAAI,sBAAa,CACrB;oBACE,KAAK,EAAE,qBAAqB;oBAC5B,OAAO,EAAE,eAAe,CAAC,OAAO,IAAI,4CAA4C;oBAChF,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;iBAC9D,EACD,mBAAU,CAAC,iBAAiB,CAC7B,CAAC;YACJ,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,sBAAa,EAAE,CAAC;gBACnC,MAAM,KAAK,CAAC;YACd,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE;gBAC3C,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC7D,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;gBACvD,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,MAAM,EAAE,OAAO,CAAC,MAAM;aACvB,CAAC,CAAC;YAEH,mDAAmD;YACnD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAEO,WAAW,CAAC,SAAiB;QACnC,QAAQ,SAAS,EAAE,CAAC;YAClB,KAAK,QAAQ;gBACX,OAAO,IAAI,oBAAoB,EAAE,CAAC;YACpC,KAAK,OAAO;gBACV,8BAA8B;gBAC9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAC;gBAC9E,OAAO,IAAI,oBAAoB,EAAE,CAAC;YACpC,KAAK,UAAU;gBACb,iCAAiC;gBACjC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8DAA8D,CAAC,CAAC;gBACjF,OAAO,IAAI,oBAAoB,EAAE,CAAC;YACpC;gBACE,OAAO,IAAI,oBAAoB,EAAE,CAAC;QACtC,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,UAAU,CACtB,OAAgB,EAChB,MAAuB;QAEvB,8BAA8B;QAC9B,IAAI,MAAM,CAAC,sBAAsB,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC;YAC9D,OAAO,KAAK,CAAC,CAAC,uCAAuC;QACvD,CAAC;QAED,6CAA6C;QAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAW,qBAAqB,EAAE,EAAE,CAAC,CAAC;QAC5E,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC;YACjC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,wDAAwD;QACxD,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAW,6BAA6B,EAAE,EAAE,CAAC,CAAC;QAC3F,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;QACtD,IAAI,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAC5D,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,KAAK,CAAC,WAAW,CACvB,OAA6B,EAC7B,MAAuB;QAEvB,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC;QACjD,IAAI,OAAO,GAAG,EAAE,CAAC;QAEjB,QAAQ,YAAY,EAAE,CAAC;YACrB,KAAK,IAAI;gBACP,OAAO,GAAG,OAAO,CAAC,EAAE,CAAC;gBACrB,MAAM;YACR,KAAK,MAAM;gBACT,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,EAAE,IAAI,OAAO,CAAC,EAAE,CAAC;gBACzC,MAAM;YACR,KAAK,QAAQ;gBACX,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,CAAW,IAAI,OAAO,CAAC,EAAE,CAAC;gBAC/D,MAAM;YACR;gBACE,OAAO,GAAG,OAAO,CAAC,EAAE,CAAC;QACzB,CAAC;QAED,8DAA8D;QAC9D,MAAM,QAAQ,GAAG,GAAG,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,KAAK,EAAE,IAAI,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;QAE5E,OAAO,cAAc,YAAY,IAAI,OAAO,IAAI,QAAQ,EAAE,CAAC;IAC7D,CAAC;IAEO,KAAK,CAAC,cAAc,CAC1B,GAAW,EACX,MAAuB;QAEvB,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC1D,CAAC;IAEO,mBAAmB,CACzB,QAAkB,EAClB,MAAqB,EACrB,MAAuB;QAEvB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QACzD,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC;QAErD,IAAI,MAAM,CAAC,eAAe,KAAK,KAAK,EAAE,CAAC;YACrC,0CAA0C;YAC1C,QAAQ,CAAC,SAAS,CAAC,iBAAiB,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;YAClD,QAAQ,CAAC,SAAS,CAAC,qBAAqB,EAAE,SAAS,CAAC,CAAC;YACrD,QAAQ,CAAC,SAAS,CAAC,iBAAiB,EAAE,SAAS,CAAC,CAAC;YACjD,QAAQ,CAAC,SAAS,CAAC,kBAAkB,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC;QAC5E,CAAC;QAED,IAAI,MAAM,CAAC,aAAa,KAAK,KAAK,EAAE,CAAC;YACnC,4CAA4C;YAC5C,QAAQ,CAAC,SAAS,CAAC,mBAAmB,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;YACpD,QAAQ,CAAC,SAAS,CAAC,uBAAuB,EAAE,SAAS,CAAC,CAAC;YACvD,QAAQ,CAAC,SAAS,CAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC;QACrD,CAAC;QAED,2CAA2C;QAC3C,IAAI,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC;YAC9B,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;YACrE,QAAQ,CAAC,SAAS,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAC/B,OAA6B,EAC7B,MAAuB,EACvB,MAAqB;QAErB,0BAA0B;QAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE;YACtC,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC;YACxC,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE;YACxB,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,KAAK,EAAE,MAAM,CAAC,GAAG;YACjB,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,SAAS,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE;SACpD,CAAC,CAAC;QAEH,qCAAqC;QACrC,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;YAC1B,IAAI,CAAC;gBACH,qCAAqC;gBACrC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC;YACrE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;oBAC9C,QAAQ,EAAE,MAAM,CAAC,cAAc;oBAC/B,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;iBAC9D,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;CACF,CAAA;AApNY,wCAAc;yBAAd,cAAc;IAD1B,IAAA,mBAAU,GAAE;yDAMmB,gBAAS,oBAAT,gBAAS,oDACL,sBAAa,oBAAb,sBAAa;GANpC,cAAc,CAoN1B;AAED;;GAEG;AACH,MAAa,cAAc;IACzB;;OAEG;IACH,MAAM,CAAC,kBAAkB,CAAC,QAAgB;QACxC,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe,CAAC,SAAiB;QACtC,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB,CAAC,KAAa,EAAE,GAAW;QAClD,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CAAC,SAAiB;QAC1C,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB,CACvB,MAAuB,EACvB,MAAqB;QAErB,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAC9D,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,IAAI,4CAA4C,CAAC;QAEnF,OAAO,GAAG,WAAW,gBAAgB,UAAU,WAAW,CAAC;IAC7D,CAAC;CACF;AAzCD,wCAyCC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\common\\guards\\rate-limit.guard.ts"], "sourcesContent": ["import {\r\n  Injectable,\r\n  CanActivate,\r\n  Execution<PERSON>ontext,\r\n  HttpException,\r\n  HttpStatus,\r\n  Logger,\r\n} from '@nestjs/common';\r\nimport { Reflector } from '@nestjs/core';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { Request, Response } from 'express';\r\nimport { RATE_LIMIT_KEY, RateLimitConfig } from '../decorators/rate-limit.decorator';\r\n\r\n// Custom request interface with user context\r\ninterface AuthenticatedRequest extends Request {\r\n  user?: {\r\n    id?: string;\r\n    email?: string;\r\n    roles?: string[];\r\n    permissions?: string[];\r\n    organizationId?: string;\r\n  };\r\n}\r\n\r\n/**\r\n * Rate limit store interface\r\n */\r\ninterface RateLimitStore {\r\n  get(key: string): Promise<RateLimitData | null>;\r\n  set(key: string, data: RateLimitData, ttl: number): Promise<void>;\r\n  increment(key: string, windowMs: number): Promise<RateLimitData>;\r\n  reset(key: string): Promise<void>;\r\n}\r\n\r\n/**\r\n * Rate limit data interface\r\n */\r\ninterface RateLimitData {\r\n  count: number;\r\n  resetTime: number;\r\n  firstRequest: number;\r\n}\r\n\r\n/**\r\n * In-memory rate limit store\r\n */\r\nclass MemoryRateLimitStore implements RateLimitStore {\r\n  private store = new Map<string, RateLimitData>();\r\n  private timers = new Map<string, NodeJS.Timeout>();\r\n\r\n  async get(key: string): Promise<RateLimitData | null> {\r\n    return this.store.get(key) || null;\r\n  }\r\n\r\n  async set(key: string, data: RateLimitData, ttl: number): Promise<void> {\r\n    this.store.set(key, data);\r\n    \r\n    // Clear existing timer\r\n    const existingTimer = this.timers.get(key);\r\n    if (existingTimer) {\r\n      clearTimeout(existingTimer);\r\n    }\r\n\r\n    // Set new timer to clean up expired data\r\n    const timer = setTimeout(() => {\r\n      this.store.delete(key);\r\n      this.timers.delete(key);\r\n    }, ttl);\r\n    \r\n    this.timers.set(key, timer);\r\n  }\r\n\r\n  async increment(key: string, windowMs: number): Promise<RateLimitData> {\r\n    const now = Date.now();\r\n    const existing = this.store.get(key);\r\n\r\n    if (!existing || now > existing.resetTime) {\r\n      // Create new window\r\n      const data: RateLimitData = {\r\n        count: 1,\r\n        resetTime: now + windowMs,\r\n        firstRequest: now,\r\n      };\r\n      await this.set(key, data, windowMs);\r\n      return data;\r\n    } else {\r\n      // Increment existing window\r\n      existing.count++;\r\n      await this.set(key, existing, existing.resetTime - now);\r\n      return existing;\r\n    }\r\n  }\r\n\r\n  async reset(key: string): Promise<void> {\r\n    this.store.delete(key);\r\n    const timer = this.timers.get(key);\r\n    if (timer) {\r\n      clearTimeout(timer);\r\n      this.timers.delete(key);\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Rate Limit Guard\r\n * Implements rate limiting based on decorator configuration\r\n */\r\n@Injectable()\r\nexport class RateLimitGuard implements CanActivate {\r\n  private readonly logger = new Logger(RateLimitGuard.name);\r\n  private readonly store: RateLimitStore;\r\n\r\n  constructor(\r\n    private readonly reflector: Reflector,\r\n    private readonly configService: ConfigService,\r\n  ) {\r\n    // Initialize store based on configuration\r\n    const storeType = this.configService.get<string>('RATE_LIMIT_STORE', 'memory');\r\n    this.store = this.createStore(storeType);\r\n  }\r\n\r\n  async canActivate(context: ExecutionContext): Promise<boolean> {\r\n    const request = context.switchToHttp().getRequest<AuthenticatedRequest>();\r\n    const response = context.switchToHttp().getResponse<Response>();\r\n\r\n    // Get rate limit configuration from decorator\r\n    const rateLimitConfig = this.reflector.getAllAndOverride<RateLimitConfig>(\r\n      RATE_LIMIT_KEY,\r\n      [context.getHandler(), context.getClass()],\r\n    );\r\n\r\n    if (!rateLimitConfig) {\r\n      return true; // No rate limiting configured\r\n    }\r\n\r\n    try {\r\n      // Check if rate limiting should be skipped\r\n      if (await this.shouldSkip(request, rateLimitConfig)) {\r\n        return true;\r\n      }\r\n\r\n      // Generate rate limit key\r\n      const key = await this.generateKey(request, rateLimitConfig);\r\n\r\n      // Check rate limit\r\n      const result = await this.checkRateLimit(key, rateLimitConfig);\r\n\r\n      // Add rate limit headers\r\n      if (rateLimitConfig.headers !== false) {\r\n        this.addRateLimitHeaders(response, result, rateLimitConfig);\r\n      }\r\n\r\n      // Check if limit exceeded\r\n      if (result.count > rateLimitConfig.max) {\r\n        await this.handleLimitExceeded(request, rateLimitConfig, result);\r\n        throw new HttpException(\r\n          {\r\n            error: 'RATE_LIMIT_EXCEEDED',\r\n            message: rateLimitConfig.message || 'Too many requests, please try again later.',\r\n            retryAfter: Math.ceil((result.resetTime - Date.now()) / 1000),\r\n          },\r\n          HttpStatus.TOO_MANY_REQUESTS,\r\n        );\r\n      }\r\n\r\n      return true;\r\n    } catch (error) {\r\n      if (error instanceof HttpException) {\r\n        throw error;\r\n      }\r\n\r\n      this.logger.error('Rate limit check failed', {\r\n        error: error instanceof Error ? error.message : String(error),\r\n        stack: error instanceof Error ? error.stack : undefined,\r\n        url: request.url,\r\n        method: request.method,\r\n      });\r\n\r\n      // Fail open - allow request if rate limiting fails\r\n      return true;\r\n    }\r\n  }\r\n\r\n  private createStore(storeType: string): RateLimitStore {\r\n    switch (storeType) {\r\n      case 'memory':\r\n        return new MemoryRateLimitStore();\r\n      case 'redis':\r\n        // TODO: Implement Redis store\r\n        this.logger.warn('Redis store not implemented, falling back to memory store');\r\n        return new MemoryRateLimitStore();\r\n      case 'database':\r\n        // TODO: Implement database store\r\n        this.logger.warn('Database store not implemented, falling back to memory store');\r\n        return new MemoryRateLimitStore();\r\n      default:\r\n        return new MemoryRateLimitStore();\r\n    }\r\n  }\r\n\r\n  private async shouldSkip(\r\n    request: Request,\r\n    config: RateLimitConfig,\r\n  ): Promise<boolean> {\r\n    // Skip based on configuration\r\n    if (config.skipSuccessfulRequests && request.method === 'GET') {\r\n      return false; // We don't know if it's successful yet\r\n    }\r\n\r\n    // Skip for certain IPs (e.g., health checks)\r\n    const skipIps = this.configService.get<string[]>('RATE_LIMIT_SKIP_IPS', []);\r\n    if (skipIps.includes(request.ip)) {\r\n      return true;\r\n    }\r\n\r\n    // Skip for certain user agents (e.g., monitoring tools)\r\n    const skipUserAgents = this.configService.get<string[]>('RATE_LIMIT_SKIP_USER_AGENTS', []);\r\n    const userAgent = request.headers['user-agent'] || '';\r\n    if (skipUserAgents.some(agent => userAgent.includes(agent))) {\r\n      return true;\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  private async generateKey(\r\n    request: AuthenticatedRequest,\r\n    config: RateLimitConfig,\r\n  ): Promise<string> {\r\n    const keyGenerator = config.keyGenerator || 'ip';\r\n    let baseKey = '';\r\n\r\n    switch (keyGenerator) {\r\n      case 'ip':\r\n        baseKey = request.ip;\r\n        break;\r\n      case 'user':\r\n        baseKey = request.user?.id || request.ip;\r\n        break;\r\n      case 'apiKey':\r\n        baseKey = request.headers['x-api-key'] as string || request.ip;\r\n        break;\r\n      default:\r\n        baseKey = request.ip;\r\n    }\r\n\r\n    // Include endpoint in key for endpoint-specific rate limiting\r\n    const endpoint = `${request.method}:${request.route?.path || request.path}`;\r\n    \r\n    return `rate_limit:${keyGenerator}:${baseKey}:${endpoint}`;\r\n  }\r\n\r\n  private async checkRateLimit(\r\n    key: string,\r\n    config: RateLimitConfig,\r\n  ): Promise<RateLimitData> {\r\n    return await this.store.increment(key, config.windowMs);\r\n  }\r\n\r\n  private addRateLimitHeaders(\r\n    response: Response,\r\n    result: RateLimitData,\r\n    config: RateLimitConfig,\r\n  ): void {\r\n    const remaining = Math.max(0, config.max - result.count);\r\n    const resetTime = Math.ceil(result.resetTime / 1000);\r\n\r\n    if (config.standardHeaders !== false) {\r\n      // Standard rate limit headers (draft RFC)\r\n      response.setHeader('RateLimit-Limit', config.max);\r\n      response.setHeader('RateLimit-Remaining', remaining);\r\n      response.setHeader('RateLimit-Reset', resetTime);\r\n      response.setHeader('RateLimit-Window', Math.ceil(config.windowMs / 1000));\r\n    }\r\n\r\n    if (config.legacyHeaders !== false) {\r\n      // Legacy headers for backward compatibility\r\n      response.setHeader('X-RateLimit-Limit', config.max);\r\n      response.setHeader('X-RateLimit-Remaining', remaining);\r\n      response.setHeader('X-RateLimit-Reset', resetTime);\r\n    }\r\n\r\n    // Add retry-after header if limit exceeded\r\n    if (result.count > config.max) {\r\n      const retryAfter = Math.ceil((result.resetTime - Date.now()) / 1000);\r\n      response.setHeader('Retry-After', retryAfter);\r\n    }\r\n  }\r\n\r\n  private async handleLimitExceeded(\r\n    request: AuthenticatedRequest,\r\n    config: RateLimitConfig,\r\n    result: RateLimitData,\r\n  ): Promise<void> {\r\n    // Log rate limit exceeded\r\n    this.logger.warn('Rate limit exceeded', {\r\n      url: request.url,\r\n      method: request.method,\r\n      ip: request.ip,\r\n      userAgent: request.headers['user-agent'],\r\n      userId: request.user?.id,\r\n      count: result.count,\r\n      limit: config.max,\r\n      windowMs: config.windowMs,\r\n      resetTime: new Date(result.resetTime).toISOString(),\r\n    });\r\n\r\n    // Call custom callback if configured\r\n    if (config.onLimitReached) {\r\n      try {\r\n        // TODO: Implement callback mechanism\r\n        this.logger.debug(`Rate limit callback: ${config.onLimitReached}`);\r\n      } catch (error) {\r\n        this.logger.error('Rate limit callback failed', {\r\n          callback: config.onLimitReached,\r\n          error: error instanceof Error ? error.message : String(error),\r\n        });\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Rate limit utilities\r\n */\r\nexport class RateLimitUtils {\r\n  /**\r\n   * Calculate rate limit window reset time\r\n   */\r\n  static calculateResetTime(windowMs: number): number {\r\n    return Date.now() + windowMs;\r\n  }\r\n\r\n  /**\r\n   * Check if rate limit window has expired\r\n   */\r\n  static isWindowExpired(resetTime: number): boolean {\r\n    return Date.now() > resetTime;\r\n  }\r\n\r\n  /**\r\n   * Calculate remaining requests\r\n   */\r\n  static calculateRemaining(count: number, max: number): number {\r\n    return Math.max(0, max - count);\r\n  }\r\n\r\n  /**\r\n   * Calculate retry after seconds\r\n   */\r\n  static calculateRetryAfter(resetTime: number): number {\r\n    return Math.ceil((resetTime - Date.now()) / 1000);\r\n  }\r\n\r\n  /**\r\n   * Format rate limit error message\r\n   */\r\n  static formatErrorMessage(\r\n    config: RateLimitConfig,\r\n    result: RateLimitData,\r\n  ): string {\r\n    const retryAfter = this.calculateRetryAfter(result.resetTime);\r\n    const baseMessage = config.message || 'Too many requests, please try again later.';\r\n    \r\n    return `${baseMessage} Retry after ${retryAfter} seconds.`;\r\n  }\r\n}"], "version": 3}