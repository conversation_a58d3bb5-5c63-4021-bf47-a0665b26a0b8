37f694c69b98a56b5e24a843ef6505fa
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityPolicy = exports.PolicyExecutionStatus = exports.SecurityRuleSeverity = exports.SecurityActionType = void 0;
const base_entity_1 = require("../../../../shared-kernel/domain/base-entity");
const unique_entity_id_value_object_1 = require("../../../../shared-kernel/value-objects/unique-entity-id.value-object");
const timestamp_value_object_1 = require("../../../../shared-kernel/value-objects/timestamp.value-object");
const validation_exception_1 = require("../../../../shared-kernel/exceptions/validation.exception");
var SecurityActionType;
(function (SecurityActionType) {
    SecurityActionType["BLOCK_IP"] = "BLOCK_IP";
    SecurityActionType["QUARANTINE_USER"] = "QUARANTINE_USER";
    SecurityActionType["DISABLE_ACCOUNT"] = "DISABLE_ACCOUNT";
    SecurityActionType["ALERT_ADMIN"] = "ALERT_ADMIN";
    SecurityActionType["LOG_EVENT"] = "LOG_EVENT";
    SecurityActionType["ESCALATE"] = "ESCALATE";
    SecurityActionType["ISOLATE_SYSTEM"] = "ISOLATE_SYSTEM";
})(SecurityActionType || (exports.SecurityActionType = SecurityActionType = {}));
var SecurityRuleSeverity;
(function (SecurityRuleSeverity) {
    SecurityRuleSeverity["LOW"] = "LOW";
    SecurityRuleSeverity["MEDIUM"] = "MEDIUM";
    SecurityRuleSeverity["HIGH"] = "HIGH";
    SecurityRuleSeverity["CRITICAL"] = "CRITICAL";
})(SecurityRuleSeverity || (exports.SecurityRuleSeverity = SecurityRuleSeverity = {}));
var PolicyExecutionStatus;
(function (PolicyExecutionStatus) {
    PolicyExecutionStatus["PENDING"] = "PENDING";
    PolicyExecutionStatus["EXECUTED"] = "EXECUTED";
    PolicyExecutionStatus["FAILED"] = "FAILED";
    PolicyExecutionStatus["SKIPPED"] = "SKIPPED";
    PolicyExecutionStatus["REQUIRES_APPROVAL"] = "REQUIRES_APPROVAL";
})(PolicyExecutionStatus || (exports.PolicyExecutionStatus = PolicyExecutionStatus = {}));
class SecurityPolicy extends base_entity_1.BaseEntity {
    constructor(props) {
        super(props, props.id);
        this.validateProps(props);
        this._name = props.name;
        this._description = props.description;
        this._version = props.version;
        this._tenantId = props.tenantId;
        this._rules = props.rules;
        this._enabled = props.enabled;
        this._createdBy = props.createdBy;
        this._lastModifiedBy = props.lastModifiedBy;
        this._auditTrail = props.auditTrail || [];
    }
    static create(props) {
        return new SecurityPolicy({
            ...props,
            lastModifiedBy: props.createdBy,
            auditTrail: []
        });
    }
    evaluate(context) {
        if (!this._enabled) {
            return [];
        }
        const results = [];
        // Sort rules by priority (higher priority first)
        const sortedRules = [...this._rules]
            .filter(rule => rule.enabled)
            .sort((a, b) => b.priority - a.priority);
        for (const rule of sortedRules) {
            try {
                const matched = this.evaluateRule(rule, context);
                const result = {
                    policyId: this.id,
                    ruleId: rule.id,
                    matched,
                    action: matched ? rule.action : undefined,
                    reason: matched ? `Rule '${rule.name}' matched` : `Rule '${rule.name}' did not match`,
                    confidence: matched ? this.calculateConfidence(rule, context) : 0,
                    evaluatedAt: timestamp_value_object_1.Timestamp.now(),
                    context
                };
                results.push(result);
                // Create audit entry
                this.addAuditEntry(result);
                // If rule matches and has high priority, we might want to stop evaluation
                if (matched && rule.severity === SecurityRuleSeverity.CRITICAL) {
                    break;
                }
            }
            catch (error) {
                // Log evaluation error but continue with other rules
                const errorResult = {
                    policyId: this.id,
                    ruleId: rule.id,
                    matched: false,
                    reason: `Rule evaluation failed: ${error.message}`,
                    confidence: 0,
                    evaluatedAt: timestamp_value_object_1.Timestamp.now(),
                    context
                };
                results.push(errorResult);
            }
        }
        return results;
    }
    addRule(rule, modifiedBy) {
        this.validateRule(rule);
        // Check for duplicate rule IDs
        if (this._rules.some(r => r.id === rule.id)) {
            throw new validation_exception_1.ValidationException(`Rule with ID '${rule.id}' already exists`, []);
        }
        this._rules.push(rule);
        this._lastModifiedBy = modifiedBy;
        this.touch();
    }
    updateRule(ruleId, updates, modifiedBy) {
        const ruleIndex = this._rules.findIndex(r => r.id === ruleId);
        if (ruleIndex === -1) {
            throw new validation_exception_1.ValidationException(`Rule with ID '${ruleId}' not found`, []);
        }
        const updatedRule = { ...this._rules[ruleIndex], ...updates };
        this.validateRule(updatedRule);
        this._rules[ruleIndex] = updatedRule;
        this._lastModifiedBy = modifiedBy;
        this.touch();
    }
    removeRule(ruleId, modifiedBy) {
        const ruleIndex = this._rules.findIndex(r => r.id === ruleId);
        if (ruleIndex === -1) {
            throw new validation_exception_1.ValidationException(`Rule with ID '${ruleId}' not found`, []);
        }
        this._rules.splice(ruleIndex, 1);
        this._lastModifiedBy = modifiedBy;
        this.touch();
    }
    enable(modifiedBy) {
        this._enabled = true;
        this._lastModifiedBy = modifiedBy;
        this.touch();
    }
    disable(modifiedBy) {
        this._enabled = false;
        this._lastModifiedBy = modifiedBy;
        this.touch();
    }
    touch() {
        // Update the last modified timestamp
        // This would typically update an updatedAt field if we had one
    }
    evaluateRule(rule, context) {
        try {
            // Parse the rule condition (simplified JSON-based rule engine)
            const condition = JSON.parse(rule.condition);
            return this.evaluateCondition(condition, context);
        }
        catch (error) {
            throw new Error(`Invalid rule condition: ${error.message}`);
        }
    }
    evaluateCondition(condition, context) {
        if (typeof condition !== 'object' || condition === null) {
            return false;
        }
        // Handle logical operators
        if (condition.and) {
            return condition.and.every((subCondition) => this.evaluateCondition(subCondition, context));
        }
        if (condition.or) {
            return condition.or.some((subCondition) => this.evaluateCondition(subCondition, context));
        }
        if (condition.not) {
            return !this.evaluateCondition(condition.not, context);
        }
        // Handle field comparisons
        if (condition.field && condition.operator && condition.hasOwnProperty('value')) {
            return this.evaluateFieldCondition(condition, context);
        }
        return false;
    }
    evaluateFieldCondition(condition, context) {
        const fieldValue = this.getFieldValue(condition.field, context);
        const expectedValue = condition.value;
        const operator = condition.operator;
        switch (operator) {
            case 'equals':
                return fieldValue === expectedValue;
            case 'not_equals':
                return fieldValue !== expectedValue;
            case 'contains':
                return typeof fieldValue === 'string' && fieldValue.includes(expectedValue);
            case 'starts_with':
                return typeof fieldValue === 'string' && fieldValue.startsWith(expectedValue);
            case 'ends_with':
                return typeof fieldValue === 'string' && fieldValue.endsWith(expectedValue);
            case 'greater_than':
                return Number(fieldValue) > Number(expectedValue);
            case 'less_than':
                return Number(fieldValue) < Number(expectedValue);
            case 'in':
                return Array.isArray(expectedValue) && expectedValue.includes(fieldValue);
            case 'regex':
                return typeof fieldValue === 'string' && new RegExp(expectedValue).test(fieldValue);
            default:
                return false;
        }
    }
    getFieldValue(fieldPath, context) {
        const parts = fieldPath.split('.');
        let current = context;
        for (const part of parts) {
            if (current && typeof current === 'object' && part in current) {
                current = current[part];
            }
            else {
                return undefined;
            }
        }
        return current;
    }
    calculateConfidence(rule, context) {
        // Base confidence based on rule severity
        let confidence = 0.5;
        switch (rule.severity) {
            case SecurityRuleSeverity.CRITICAL:
                confidence = 0.9;
                break;
            case SecurityRuleSeverity.HIGH:
                confidence = 0.8;
                break;
            case SecurityRuleSeverity.MEDIUM:
                confidence = 0.6;
                break;
            case SecurityRuleSeverity.LOW:
                confidence = 0.4;
                break;
        }
        // Adjust based on threat context if available
        if (context.threatContext) {
            confidence = Math.min(1.0, confidence + (context.threatContext.confidence * 0.2));
        }
        return Math.round(confidence * 100) / 100;
    }
    addAuditEntry(evaluationResult) {
        const auditEntry = {
            id: unique_entity_id_value_object_1.UniqueEntityId.generate(),
            policyId: this.id,
            ruleId: evaluationResult.ruleId,
            evaluationResult,
            executionStatus: PolicyExecutionStatus.PENDING,
            executedAt: timestamp_value_object_1.Timestamp.now()
        };
        this._auditTrail.push(auditEntry);
    }
    validateProps(props) {
        if (!props.name || props.name.trim().length === 0) {
            throw new validation_exception_1.ValidationException('Policy name is required', []);
        }
        if (!props.description || props.description.trim().length === 0) {
            throw new validation_exception_1.ValidationException('Policy description is required', []);
        }
        if (!props.version || props.version.trim().length === 0) {
            throw new validation_exception_1.ValidationException('Policy version is required', []);
        }
        if (!props.tenantId) {
            throw new validation_exception_1.ValidationException('Tenant ID is required', []);
        }
        if (!props.createdBy) {
            throw new validation_exception_1.ValidationException('Created by user ID is required', []);
        }
        if (!Array.isArray(props.rules)) {
            throw new validation_exception_1.ValidationException('Rules must be an array', []);
        }
        props.rules.forEach((rule) => this.validateRule(rule));
    }
    validateRule(rule) {
        if (!rule.id || rule.id.trim().length === 0) {
            throw new validation_exception_1.ValidationException('Rule ID is required', []);
        }
        if (!rule.name || rule.name.trim().length === 0) {
            throw new validation_exception_1.ValidationException('Rule name is required', []);
        }
        if (!rule.condition || rule.condition.trim().length === 0) {
            throw new validation_exception_1.ValidationException('Rule condition is required', []);
        }
        if (!rule.action || !rule.action.type) {
            throw new validation_exception_1.ValidationException('Rule action is required', []);
        }
        if (typeof rule.priority !== 'number' || rule.priority < 0) {
            throw new validation_exception_1.ValidationException('Rule priority must be a non-negative number', []);
        }
        // Validate condition is valid JSON
        try {
            JSON.parse(rule.condition);
        }
        catch (error) {
            throw new validation_exception_1.ValidationException('Rule condition must be valid JSON', []);
        }
    }
    // Getters
    get name() { return this._name; }
    get description() { return this._description; }
    get version() { return this._version; }
    get tenantId() { return this._tenantId; }
    get rules() { return [...this._rules]; }
    get enabled() { return this._enabled; }
    get createdBy() { return this._createdBy; }
    get lastModifiedBy() { return this._lastModifiedBy; }
    get auditTrail() { return [...this._auditTrail]; }
}
exports.SecurityPolicy = SecurityPolicy;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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