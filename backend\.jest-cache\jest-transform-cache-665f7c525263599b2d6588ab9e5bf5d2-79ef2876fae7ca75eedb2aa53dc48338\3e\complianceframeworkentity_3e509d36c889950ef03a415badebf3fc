16deac6dc22e02a5ec1ba9eaa4d8eda3
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComplianceFramework = void 0;
const typeorm_1 = require("typeorm");
const compliance_assessment_entity_1 = require("./compliance-assessment.entity");
const policy_definition_entity_1 = require("./policy-definition.entity");
/**
 * Compliance Framework entity
 * Represents regulatory and compliance frameworks (SOC2, ISO27001, GDPR, HIPAA, PCI-DSS, etc.)
 */
let ComplianceFramework = class ComplianceFramework {
    /**
     * Get total number of controls
     */
    get totalControls() {
        return this.configuration.domains.reduce((total, domain) => total + domain.controls.length, 0);
    }
    /**
     * Get controls by priority
     */
    getControlsByPriority(priority) {
        const controls = [];
        this.configuration.domains.forEach(domain => {
            domain.controls.forEach(control => {
                if (control.priority === priority) {
                    controls.push({ ...control, domainId: domain.id, domainName: domain.name });
                }
            });
        });
        return controls;
    }
    /**
     * Get automatable controls
     */
    get automatableControls() {
        const controls = [];
        this.configuration.domains.forEach(domain => {
            domain.controls.forEach(control => {
                if (control.automatable) {
                    controls.push({ ...control, domainId: domain.id, domainName: domain.name });
                }
            });
        });
        return controls;
    }
    /**
     * Get control by ID
     */
    getControlById(controlId) {
        for (const domain of this.configuration.domains) {
            const control = domain.controls.find(c => c.id === controlId);
            if (control) {
                return { ...control, domainId: domain.id, domainName: domain.name };
            }
        }
        return null;
    }
    /**
     * Get domain by ID
     */
    getDomainById(domainId) {
        return this.configuration.domains.find(d => d.id === domainId) || null;
    }
    /**
     * Validate framework configuration
     */
    validateConfiguration() {
        const errors = [];
        // Check domains
        if (!this.configuration.domains || this.configuration.domains.length === 0) {
            errors.push('At least one domain is required');
        }
        else {
            // Check domain IDs are unique
            const domainIds = this.configuration.domains.map(d => d.id);
            const uniqueDomainIds = new Set(domainIds);
            if (domainIds.length !== uniqueDomainIds.size) {
                errors.push('Domain IDs must be unique');
            }
            // Check controls
            this.configuration.domains.forEach((domain, domainIndex) => {
                if (!domain.controls || domain.controls.length === 0) {
                    errors.push(`Domain ${domainIndex + 1} must have at least one control`);
                }
                else {
                    // Check control IDs are unique within domain
                    const controlIds = domain.controls.map(c => c.id);
                    const uniqueControlIds = new Set(controlIds);
                    if (controlIds.length !== uniqueControlIds.size) {
                        errors.push(`Control IDs in domain ${domain.name} must be unique`);
                    }
                    // Check required control fields
                    domain.controls.forEach((control, controlIndex) => {
                        if (!control.name || !control.description) {
                            errors.push(`Control ${controlIndex + 1} in domain ${domain.name} is missing name or description`);
                        }
                    });
                }
            });
        }
        return {
            isValid: errors.length === 0,
            errors,
        };
    }
    /**
     * Calculate framework complexity score
     */
    calculateComplexityScore() {
        const totalControls = this.totalControls;
        const criticalControls = this.getControlsByPriority('critical').length;
        const highControls = this.getControlsByPriority('high').length;
        const automatableControls = this.automatableControls.length;
        const domains = this.configuration.domains.length;
        // Weighted complexity calculation
        const controlComplexity = totalControls * 1;
        const priorityComplexity = (criticalControls * 3) + (highControls * 2);
        const automationReduction = automatableControls * 0.5;
        const domainComplexity = domains * 2;
        return Math.max(1, controlComplexity + priorityComplexity + domainComplexity - automationReduction);
    }
    /**
     * Get implementation timeline estimate
     */
    getImplementationTimeline() {
        const complexity = this.calculateComplexityScore();
        const baseMonths = Math.ceil(complexity / 10);
        const phases = [
            {
                name: 'Planning and Gap Analysis',
                duration: Math.ceil(baseMonths * 0.2),
                activities: ['Framework assessment', 'Gap analysis', 'Implementation planning'],
            },
            {
                name: 'Policy and Procedure Development',
                duration: Math.ceil(baseMonths * 0.3),
                activities: ['Policy creation', 'Procedure documentation', 'Training materials'],
            },
            {
                name: 'Implementation',
                duration: Math.ceil(baseMonths * 0.4),
                activities: ['Control implementation', 'System configuration', 'Process deployment'],
            },
            {
                name: 'Testing and Validation',
                duration: Math.ceil(baseMonths * 0.1),
                activities: ['Control testing', 'Validation', 'Remediation'],
            },
        ];
        return {
            phases,
            totalMonths: phases.reduce((total, phase) => total + phase.duration, 0),
        };
    }
    /**
     * Clone framework for customization
     */
    clone(name, version = '1.0') {
        return {
            name,
            description: `${this.description} (Customized)`,
            type: 'CUSTOM',
            version,
            isActive: false, // New frameworks start inactive
            configuration: JSON.parse(JSON.stringify(this.configuration)),
            metadata: JSON.parse(JSON.stringify(this.metadata)),
            tags: [...this.tags, 'custom', 'cloned'],
        };
    }
    /**
     * Export framework for sharing
     */
    exportFramework() {
        return {
            name: this.name,
            description: this.description,
            type: this.type,
            version: this.version,
            configuration: this.configuration,
            metadata: this.metadata,
            tags: this.tags,
            exportedAt: new Date().toISOString(),
            exportedBy: 'Sentinel Platform',
        };
    }
    /**
     * Get framework summary
     */
    getSummary() {
        return {
            id: this.id,
            name: this.name,
            type: this.type,
            version: this.version,
            isActive: this.isActive,
            totalControls: this.totalControls,
            domains: this.configuration.domains.length,
            complexityScore: this.calculateComplexityScore(),
            automatableControls: this.automatableControls.length,
            criticalControls: this.getControlsByPriority('critical').length,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }
};
exports.ComplianceFramework = ComplianceFramework;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ComplianceFramework.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], ComplianceFramework.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], ComplianceFramework.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: [
            'SOC2',
            'ISO27001',
            'GDPR',
            'HIPAA',
            'PCI_DSS',
            'NIST_CSF',
            'CIS_CONTROLS',
            'COBIT',
            'ITIL',
            'SOX',
            'FISMA',
            'FedRAMP',
            'CUSTOM',
        ],
    }),
    __metadata("design:type", String)
], ComplianceFramework.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: '1.0' }),
    __metadata("design:type", String)
], ComplianceFramework.prototype, "version", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_active', default: true }),
    __metadata("design:type", Boolean)
], ComplianceFramework.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    __metadata("design:type", Object)
], ComplianceFramework.prototype, "configuration", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ComplianceFramework.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, default: '{}' }),
    __metadata("design:type", Array)
], ComplianceFramework.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', type: 'uuid' }),
    __metadata("design:type", String)
], ComplianceFramework.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], ComplianceFramework.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], ComplianceFramework.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], ComplianceFramework.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => compliance_assessment_entity_1.ComplianceAssessment, assessment => assessment.framework),
    __metadata("design:type", Array)
], ComplianceFramework.prototype, "assessments", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => policy_definition_entity_1.PolicyDefinition, policy => policy.framework),
    __metadata("design:type", Array)
], ComplianceFramework.prototype, "policies", void 0);
exports.ComplianceFramework = ComplianceFramework = __decorate([
    (0, typeorm_1.Entity)('compliance_frameworks'),
    (0, typeorm_1.Index)(['type']),
    (0, typeorm_1.Index)(['isActive']),
    (0, typeorm_1.Index)(['version'])
], ComplianceFramework);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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