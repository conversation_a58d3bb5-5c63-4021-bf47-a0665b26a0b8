{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\common\\decorators\\rate-limit.decorator.ts", "mappings": ";;;AAAA,2CAA6C;AAmB7C;;GAEG;AACU,QAAA,cAAc,GAAG,YAAY,CAAC;AAE3C;;;GAGG;AACI,MAAM,SAAS,GAAG,CAAC,MAAuB,EAAE,EAAE,CAAC,IAAA,oBAAW,EAAC,sBAAc,EAAE,MAAM,CAAC,CAAC;AAA7E,QAAA,SAAS,aAAoE;AAE1F;;GAEG;AACU,QAAA,gBAAgB,GAAG;IAC9B;;OAEG;IACH,WAAW,EAAE;QACX,QAAQ,EAAE,EAAE,GAAG,IAAI,EAAE,WAAW;QAChC,GAAG,EAAE,EAAE;QACP,OAAO,EAAE,4CAA4C;QACrD,OAAO,EAAE,IAAI;QACb,eAAe,EAAE,IAAI;KACtB;IAED;;OAEG;IACH,MAAM,EAAE;QACN,QAAQ,EAAE,EAAE,GAAG,IAAI,EAAE,WAAW;QAChC,GAAG,EAAE,EAAE;QACP,OAAO,EAAE,4CAA4C;QACrD,OAAO,EAAE,IAAI;QACb,eAAe,EAAE,IAAI;KACtB;IAED;;OAEG;IACH,QAAQ,EAAE;QACR,QAAQ,EAAE,EAAE,GAAG,IAAI,EAAE,WAAW;QAChC,GAAG,EAAE,GAAG;QACR,OAAO,EAAE,4CAA4C;QACrD,OAAO,EAAE,IAAI;QACb,eAAe,EAAE,IAAI;KACtB;IAED;;OAEG;IACH,OAAO,EAAE;QACP,QAAQ,EAAE,EAAE,GAAG,IAAI,EAAE,WAAW;QAChC,GAAG,EAAE,GAAG;QACR,OAAO,EAAE,4CAA4C;QACrD,OAAO,EAAE,IAAI;QACb,eAAe,EAAE,IAAI;KACtB;IAED;;OAEG;IACH,IAAI,EAAE;QACJ,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;QACvC,GAAG,EAAE,CAAC;QACN,OAAO,EAAE,2DAA2D;QACpE,OAAO,EAAE,IAAI;QACb,eAAe,EAAE,IAAI;QACrB,sBAAsB,EAAE,IAAI;KAC7B;IAED;;OAEG;IACH,cAAc,EAAE;QACd,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,SAAS;QACnC,GAAG,EAAE,CAAC;QACN,OAAO,EAAE,2DAA2D;QACpE,OAAO,EAAE,IAAI;QACb,eAAe,EAAE,IAAI;KACtB;IAED;;OAEG;IACH,WAAW,EAAE;QACX,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,SAAS;QACnC,GAAG,EAAE,EAAE;QACP,OAAO,EAAE,gDAAgD;QACzD,OAAO,EAAE,IAAI;QACb,eAAe,EAAE,IAAI;KACtB;IAED;;OAEG;IACH,MAAM,EAAE;QACN,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,SAAS;QACnC,GAAG,EAAE,CAAC;QACN,OAAO,EAAE,mDAAmD;QAC5D,OAAO,EAAE,IAAI;QACb,eAAe,EAAE,IAAI;KACtB;IAED;;OAEG;IACH,MAAM,EAAE;QACN,QAAQ,EAAE,EAAE,GAAG,IAAI,EAAE,WAAW;QAChC,GAAG,EAAE,GAAG;QACR,OAAO,EAAE,mDAAmD;QAC5D,OAAO,EAAE,IAAI;QACb,eAAe,EAAE,IAAI;KACtB;IAED;;OAEG;IACH,SAAS,EAAE;QACT,QAAQ,EAAE,EAAE,GAAG,IAAI,EAAE,WAAW;QAChC,GAAG,EAAE,EAAE;QACP,OAAO,EAAE,sDAAsD;QAC/D,OAAO,EAAE,IAAI;QACb,eAAe,EAAE,IAAI;KACtB;IAED;;OAEG;IACH,QAAQ,EAAE;QACR,QAAQ,EAAE,EAAE,GAAG,IAAI,EAAE,WAAW;QAChC,GAAG,EAAE,IAAI;QACT,OAAO,EAAE,sDAAsD;QAC/D,OAAO,EAAE,IAAI;QACb,eAAe,EAAE,IAAI;KACtB;CACF,CAAC;AAEF;;GAEG;AACI,MAAM,mBAAmB,GAAG,GAAG,EAAE,CAAC,IAAA,iBAAS,EAAC,wBAAgB,CAAC,WAAW,CAAC,CAAC;AAApE,QAAA,mBAAmB,uBAAiD;AAC1E,MAAM,eAAe,GAAG,GAAG,EAAE,CAAC,IAAA,iBAAS,EAAC,wBAAgB,CAAC,MAAM,CAAC,CAAC;AAA3D,QAAA,eAAe,mBAA4C;AACjE,MAAM,iBAAiB,GAAG,GAAG,EAAE,CAAC,IAAA,iBAAS,EAAC,wBAAgB,CAAC,QAAQ,CAAC,CAAC;AAA/D,QAAA,iBAAiB,qBAA8C;AACrE,MAAM,gBAAgB,GAAG,GAAG,EAAE,CAAC,IAAA,iBAAS,EAAC,wBAAgB,CAAC,OAAO,CAAC,CAAC;AAA7D,QAAA,gBAAgB,oBAA6C;AACnE,MAAM,aAAa,GAAG,GAAG,EAAE,CAAC,IAAA,iBAAS,EAAC,wBAAgB,CAAC,IAAI,CAAC,CAAC;AAAvD,QAAA,aAAa,iBAA0C;AAC7D,MAAM,sBAAsB,GAAG,GAAG,EAAE,CAAC,IAAA,iBAAS,EAAC,wBAAgB,CAAC,cAAc,CAAC,CAAC;AAA1E,QAAA,sBAAsB,0BAAoD;AAChF,MAAM,mBAAmB,GAAG,GAAG,EAAE,CAAC,IAAA,iBAAS,EAAC,wBAAgB,CAAC,WAAW,CAAC,CAAC;AAApE,QAAA,mBAAmB,uBAAiD;AAC1E,MAAM,eAAe,GAAG,GAAG,EAAE,CAAC,IAAA,iBAAS,EAAC,wBAAgB,CAAC,MAAM,CAAC,CAAC;AAA3D,QAAA,eAAe,mBAA4C;AACjE,MAAM,eAAe,GAAG,GAAG,EAAE,CAAC,IAAA,iBAAS,EAAC,wBAAgB,CAAC,MAAM,CAAC,CAAC;AAA3D,QAAA,eAAe,mBAA4C;AACjE,MAAM,kBAAkB,GAAG,GAAG,EAAE,CAAC,IAAA,iBAAS,EAAC,wBAAgB,CAAC,SAAS,CAAC,CAAC;AAAjE,QAAA,kBAAkB,sBAA+C;AACvE,MAAM,iBAAiB,GAAG,GAAG,EAAE,CAAC,IAAA,iBAAS,EAAC,wBAAgB,CAAC,QAAQ,CAAC,CAAC;AAA/D,QAAA,iBAAiB,qBAA8C;AAE5E;;GAEG;AACI,MAAM,kBAAkB,GAAG,CAAC,WAA4C,EAAE,EAAE,CACjF,IAAA,oBAAW,EAAC,sBAAc,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC;AAD/D,QAAA,kBAAkB,sBAC6C;AAE5E;;GAEG;AACI,MAAM,WAAW,GAAG,CAAC,MAAuB,EAAE,EAAE,CACrD,IAAA,oBAAW,EAAC,sBAAc,EAAE,EAAE,GAAG,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;AADpD,QAAA,WAAW,eACyC;AAEjE;;GAEG;AACI,MAAM,aAAa,GAAG,CAAC,MAAuB,EAAE,EAAE,CACvD,IAAA,oBAAW,EAAC,sBAAc,EAAE,EAAE,GAAG,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,CAAC,CAAC;AADtD,QAAA,aAAa,iBACyC;AAEnE;;GAEG;AACI,MAAM,eAAe,GAAG,CAAC,MAAuB,EAAE,EAAE,CACzD,IAAA,oBAAW,EAAC,sBAAc,EAAE,EAAE,GAAG,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,CAAC,CAAC;AADxD,QAAA,eAAe,mBACyC;AAErE;;GAEG;AACI,MAAM,iBAAiB,GAAG,CAAC,OAUjC,EAAE,EAAE,CAAC,IAAA,oBAAW,EAAC,sBAAc,EAAE,OAAO,CAAC,CAAC;AAV9B,QAAA,iBAAiB,qBAUa;AAE3C;;GAEG;AACI,MAAM,cAAc,GAAG,CAAC,OAK9B,EAAE,EAAE,CAAC,IAAA,oBAAW,EAAC,sBAAc,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC;AALpD,QAAA,cAAc,kBAKsC;AAEjE;;GAEG;AACI,MAAM,sBAAsB,GAAG,CAAC,OAItC,EAAE,EAAE,CAAC,IAAA,oBAAW,EAAC,sBAAc,EAAE,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC;AAJ7D,QAAA,sBAAsB,0BAIuC;AAE1E;;GAEG;AACI,MAAM,oBAAoB,GAAG,CAAC,OAIpC,EAAE,EAAE,CAAC,IAAA,oBAAW,EAAC,sBAAc,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC;AAJ3D,QAAA,oBAAoB,wBAIuC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\common\\decorators\\rate-limit.decorator.ts"], "sourcesContent": ["import { SetMetadata } from '@nestjs/common';\r\n\r\n/**\r\n * Rate limit configuration interface\r\n */\r\nexport interface RateLimitConfig {\r\n  windowMs: number; // Time window in milliseconds\r\n  max: number; // Maximum number of requests per window\r\n  skipSuccessfulRequests?: boolean; // Skip successful requests from rate limiting\r\n  skipFailedRequests?: boolean; // Skip failed requests from rate limiting\r\n  keyGenerator?: string; // Custom key generator function name\r\n  message?: string; // Custom rate limit exceeded message\r\n  headers?: boolean; // Include rate limit headers in response\r\n  standardHeaders?: boolean; // Include standard rate limit headers\r\n  legacyHeaders?: boolean; // Include legacy rate limit headers\r\n  store?: string; // Custom store for rate limit data\r\n  onLimitReached?: string; // Callback function name when limit is reached\r\n}\r\n\r\n/**\r\n * Rate limit metadata key\r\n */\r\nexport const RATE_LIMIT_KEY = 'rate_limit';\r\n\r\n/**\r\n * Rate Limit Decorator\r\n * Applies rate limiting to controller methods or entire controllers\r\n */\r\nexport const RateLimit = (config: RateLimitConfig) => SetMetadata(RATE_LIMIT_KEY, config);\r\n\r\n/**\r\n * Predefined rate limit configurations\r\n */\r\nexport const RateLimitPresets = {\r\n  /**\r\n   * Very strict rate limiting - 10 requests per minute\r\n   */\r\n  VERY_STRICT: {\r\n    windowMs: 60 * 1000, // 1 minute\r\n    max: 10,\r\n    message: 'Too many requests, please try again later.',\r\n    headers: true,\r\n    standardHeaders: true,\r\n  },\r\n\r\n  /**\r\n   * Strict rate limiting - 30 requests per minute\r\n   */\r\n  STRICT: {\r\n    windowMs: 60 * 1000, // 1 minute\r\n    max: 30,\r\n    message: 'Too many requests, please try again later.',\r\n    headers: true,\r\n    standardHeaders: true,\r\n  },\r\n\r\n  /**\r\n   * Moderate rate limiting - 100 requests per minute\r\n   */\r\n  MODERATE: {\r\n    windowMs: 60 * 1000, // 1 minute\r\n    max: 100,\r\n    message: 'Too many requests, please try again later.',\r\n    headers: true,\r\n    standardHeaders: true,\r\n  },\r\n\r\n  /**\r\n   * Lenient rate limiting - 300 requests per minute\r\n   */\r\n  LENIENT: {\r\n    windowMs: 60 * 1000, // 1 minute\r\n    max: 300,\r\n    message: 'Too many requests, please try again later.',\r\n    headers: true,\r\n    standardHeaders: true,\r\n  },\r\n\r\n  /**\r\n   * Authentication endpoints - 5 attempts per 15 minutes\r\n   */\r\n  AUTH: {\r\n    windowMs: 15 * 60 * 1000, // 15 minutes\r\n    max: 5,\r\n    message: 'Too many authentication attempts, please try again later.',\r\n    headers: true,\r\n    standardHeaders: true,\r\n    skipSuccessfulRequests: true,\r\n  },\r\n\r\n  /**\r\n   * Password reset - 3 attempts per hour\r\n   */\r\n  PASSWORD_RESET: {\r\n    windowMs: 60 * 60 * 1000, // 1 hour\r\n    max: 3,\r\n    message: 'Too many password reset attempts, please try again later.',\r\n    headers: true,\r\n    standardHeaders: true,\r\n  },\r\n\r\n  /**\r\n   * File upload - 10 uploads per hour\r\n   */\r\n  FILE_UPLOAD: {\r\n    windowMs: 60 * 60 * 1000, // 1 hour\r\n    max: 10,\r\n    message: 'Too many file uploads, please try again later.',\r\n    headers: true,\r\n    standardHeaders: true,\r\n  },\r\n\r\n  /**\r\n   * Export operations - 5 exports per hour\r\n   */\r\n  EXPORT: {\r\n    windowMs: 60 * 60 * 1000, // 1 hour\r\n    max: 5,\r\n    message: 'Too many export requests, please try again later.',\r\n    headers: true,\r\n    standardHeaders: true,\r\n  },\r\n\r\n  /**\r\n   * Search operations - 200 searches per minute\r\n   */\r\n  SEARCH: {\r\n    windowMs: 60 * 1000, // 1 minute\r\n    max: 200,\r\n    message: 'Too many search requests, please try again later.',\r\n    headers: true,\r\n    standardHeaders: true,\r\n  },\r\n\r\n  /**\r\n   * Analytics operations - 50 requests per minute\r\n   */\r\n  ANALYTICS: {\r\n    windowMs: 60 * 1000, // 1 minute\r\n    max: 50,\r\n    message: 'Too many analytics requests, please try again later.',\r\n    headers: true,\r\n    standardHeaders: true,\r\n  },\r\n\r\n  /**\r\n   * Real-time operations - 1000 requests per minute\r\n   */\r\n  REALTIME: {\r\n    windowMs: 60 * 1000, // 1 minute\r\n    max: 1000,\r\n    message: 'Too many real-time requests, please try again later.',\r\n    headers: true,\r\n    standardHeaders: true,\r\n  },\r\n};\r\n\r\n/**\r\n * Convenience decorators for common rate limiting scenarios\r\n */\r\nexport const VeryStrictRateLimit = () => RateLimit(RateLimitPresets.VERY_STRICT);\r\nexport const StrictRateLimit = () => RateLimit(RateLimitPresets.STRICT);\r\nexport const ModerateRateLimit = () => RateLimit(RateLimitPresets.MODERATE);\r\nexport const LenientRateLimit = () => RateLimit(RateLimitPresets.LENIENT);\r\nexport const AuthRateLimit = () => RateLimit(RateLimitPresets.AUTH);\r\nexport const PasswordResetRateLimit = () => RateLimit(RateLimitPresets.PASSWORD_RESET);\r\nexport const FileUploadRateLimit = () => RateLimit(RateLimitPresets.FILE_UPLOAD);\r\nexport const ExportRateLimit = () => RateLimit(RateLimitPresets.EXPORT);\r\nexport const SearchRateLimit = () => RateLimit(RateLimitPresets.SEARCH);\r\nexport const AnalyticsRateLimit = () => RateLimit(RateLimitPresets.ANALYTICS);\r\nexport const RealtimeRateLimit = () => RateLimit(RateLimitPresets.REALTIME);\r\n\r\n/**\r\n * Dynamic rate limit decorator based on user role\r\n */\r\nexport const RoleBasedRateLimit = (roleConfigs: Record<string, RateLimitConfig>) => \r\n  SetMetadata(RATE_LIMIT_KEY, { type: 'role-based', configs: roleConfigs });\r\n\r\n/**\r\n * IP-based rate limiting\r\n */\r\nexport const IpRateLimit = (config: RateLimitConfig) => \r\n  SetMetadata(RATE_LIMIT_KEY, { ...config, keyGenerator: 'ip' });\r\n\r\n/**\r\n * User-based rate limiting\r\n */\r\nexport const UserRateLimit = (config: RateLimitConfig) => \r\n  SetMetadata(RATE_LIMIT_KEY, { ...config, keyGenerator: 'user' });\r\n\r\n/**\r\n * API key-based rate limiting\r\n */\r\nexport const ApiKeyRateLimit = (config: RateLimitConfig) => \r\n  SetMetadata(RATE_LIMIT_KEY, { ...config, keyGenerator: 'apiKey' });\r\n\r\n/**\r\n * Custom rate limit decorator with advanced options\r\n */\r\nexport const AdvancedRateLimit = (options: {\r\n  windowMs: number;\r\n  max: number;\r\n  keyGenerator?: 'ip' | 'user' | 'apiKey' | 'custom';\r\n  customKeyGenerator?: string;\r\n  skipIf?: string; // Function name to determine if rate limiting should be skipped\r\n  onLimitReached?: string; // Callback function name\r\n  store?: 'memory' | 'redis' | 'database';\r\n  message?: string;\r\n  headers?: boolean;\r\n}) => SetMetadata(RATE_LIMIT_KEY, options);\r\n\r\n/**\r\n * Burst rate limiting - allows short bursts but limits sustained usage\r\n */\r\nexport const BurstRateLimit = (options: {\r\n  burstLimit: number; // Maximum requests in burst window\r\n  burstWindowMs: number; // Burst window duration\r\n  sustainedLimit: number; // Maximum requests in sustained window\r\n  sustainedWindowMs: number; // Sustained window duration\r\n}) => SetMetadata(RATE_LIMIT_KEY, { type: 'burst', ...options });\r\n\r\n/**\r\n * Sliding window rate limiting\r\n */\r\nexport const SlidingWindowRateLimit = (options: {\r\n  windowMs: number;\r\n  max: number;\r\n  precision?: number; // Number of sub-windows for precision\r\n}) => SetMetadata(RATE_LIMIT_KEY, { type: 'sliding-window', ...options });\r\n\r\n/**\r\n * Token bucket rate limiting\r\n */\r\nexport const TokenBucketRateLimit = (options: {\r\n  capacity: number; // Bucket capacity\r\n  refillRate: number; // Tokens per second\r\n  tokensPerRequest?: number; // Tokens consumed per request\r\n}) => SetMetadata(RATE_LIMIT_KEY, { type: 'token-bucket', ...options });"], "version": 3}