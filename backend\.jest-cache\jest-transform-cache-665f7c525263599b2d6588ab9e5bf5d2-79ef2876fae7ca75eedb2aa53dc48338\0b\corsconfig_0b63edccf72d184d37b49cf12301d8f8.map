{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\headers\\cors.config.ts", "mappings": ";;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,2CAA+C;AAG/C;;;GAGG;AAEI,IAAM,UAAU,GAAhB,MAAM,UAAU;IACrB,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;IAAG,CAAC;IAE7D;;OAEG;IACH,cAAc;QACZ,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;QAEtE,QAAQ,WAAW,EAAE,CAAC;YACpB,KAAK,YAAY;gBACf,OAAO,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACzC,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC;YACtC,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACnC;gBACE,OAAO,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAC5C,CAAC;IACH,CAAC;IAED;;OAEG;IACK,wBAAwB;QAC9B,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAW,qBAAqB,EAAE,EAAE,CAAC,CAAC;QAEnF,OAAO;YACL,MAAM,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE;gBAC3B,6DAA6D;gBAC7D,IAAI,CAAC,MAAM,EAAE,CAAC;oBACZ,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBAC9B,CAAC;gBAED,0BAA0B;gBAC1B,IAAI,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;oBACpC,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBAC9B,CAAC;gBAED,mCAAmC;gBACnC,OAAO,CAAC,IAAI,CAAC,mDAAmD,MAAM,EAAE,CAAC,CAAC;gBAC1E,OAAO,QAAQ,CAAC,IAAI,KAAK,CAAC,qBAAqB,CAAC,EAAE,KAAK,CAAC,CAAC;YAC3D,CAAC;YACD,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC;YAC7D,cAAc,EAAE;gBACd,QAAQ;gBACR,kBAAkB;gBAClB,cAAc;gBACd,QAAQ;gBACR,eAAe;gBACf,WAAW;gBACX,kBAAkB;gBAClB,cAAc;gBACd,eAAe;aAChB;YACD,cAAc,EAAE;gBACd,eAAe;gBACf,cAAc;gBACd,gBAAgB;gBAChB,YAAY;gBACZ,oBAAoB;gBACpB,wBAAwB;gBACxB,oBAAoB;gBACpB,eAAe;gBACf,cAAc;aACf;YACD,WAAW,EAAE,IAAI;YACjB,MAAM,EAAE,KAAK,EAAE,WAAW;YAC1B,iBAAiB,EAAE,KAAK;YACxB,oBAAoB,EAAE,GAAG;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,qBAAqB;QAC3B,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAW,qBAAqB,EAAE;YAC7E,8BAA8B;YAC9B,oCAAoC;SACrC,CAAC,CAAC;QAEH,OAAO;YACL,MAAM,EAAE,cAAc;YACtB,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,CAAC;YACrE,cAAc,EAAE;gBACd,QAAQ;gBACR,kBAAkB;gBAClB,cAAc;gBACd,QAAQ;gBACR,eAAe;gBACf,WAAW;gBACX,kBAAkB;gBAClB,cAAc;gBACd,eAAe;gBACf,cAAc;aACf;YACD,cAAc,EAAE;gBACd,eAAe;gBACf,cAAc;gBACd,gBAAgB;gBAChB,YAAY;gBACZ,oBAAoB;gBACpB,wBAAwB;gBACxB,oBAAoB;gBACpB,eAAe;gBACf,cAAc;gBACd,cAAc;aACf;YACD,WAAW,EAAE,IAAI;YACjB,MAAM,EAAE,IAAI,EAAE,SAAS;YACvB,iBAAiB,EAAE,KAAK;YACxB,oBAAoB,EAAE,GAAG;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,yBAAyB;QAC/B,OAAO;YACL,MAAM,EAAE,IAAI,EAAE,mCAAmC;YACjD,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,CAAC;YACrE,cAAc,EAAE;gBACd,QAAQ;gBACR,kBAAkB;gBAClB,cAAc;gBACd,QAAQ;gBACR,eAAe;gBACf,WAAW;gBACX,kBAAkB;gBAClB,cAAc;gBACd,eAAe;gBACf,cAAc;gBACd,aAAa;aACd;YACD,cAAc,EAAE;gBACd,eAAe;gBACf,cAAc;gBACd,gBAAgB;gBAChB,YAAY;gBACZ,oBAAoB;gBACpB,wBAAwB;gBACxB,oBAAoB;gBACpB,eAAe;gBACf,cAAc;gBACd,cAAc;gBACd,uBAAuB;aACxB;YACD,WAAW,EAAE,IAAI;YACjB,MAAM,EAAE,GAAG,EAAE,YAAY;YACzB,iBAAiB,EAAE,KAAK;YACxB,oBAAoB,EAAE,GAAG;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,OAAO;YACL,MAAM,EAAE,IAAI;YACZ,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,CAAC;YACrE,cAAc,EAAE,GAAG;YACnB,cAAc,EAAE,GAAG;YACnB,WAAW,EAAE,IAAI;YACjB,MAAM,EAAE,CAAC,EAAE,sBAAsB;YACjC,iBAAiB,EAAE,KAAK;YACxB,oBAAoB,EAAE,GAAG;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YAC1C,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;YAEtE,qCAAqC;YACrC,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;gBACxD,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;YACpE,CAAC;YAED,+BAA+B;YAC/B,IAAI,WAAW,KAAK,YAAY,EAAE,CAAC;gBACjC,IAAI,WAAW,CAAC,MAAM,KAAK,IAAI,EAAE,CAAC;oBAChC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;gBAC9D,CAAC;gBAED,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;oBAC7B,OAAO,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;gBAC9D,CAAC;YACH,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YACtE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAC1C,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;QAEtE,OAAO;YACL,WAAW;YACX,iBAAiB,EAAE,WAAW,CAAC,WAAW;YAC1C,MAAM,EAAE,WAAW,CAAC,MAAM;YAC1B,YAAY,EAAE,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK;YACrF,YAAY,EAAE,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK;YACnG,YAAY,EAAE,OAAO,WAAW,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC;gBACrD,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW;SAChE,CAAC;IACJ,CAAC;CACF,CAAA;AA5NY,gCAAU;qBAAV,UAAU;IADtB,IAAA,mBAAU,GAAE;yDAEiC,sBAAa,oBAAb,sBAAa;GAD9C,UAAU,CA4NtB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\headers\\cors.config.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { CorsOptions } from '@nestjs/common/interfaces/external/cors-options.interface';\r\n\r\n/**\r\n * CORS configuration service for secure cross-origin resource sharing\r\n * Implements environment-specific CORS policies\r\n */\r\n@Injectable()\r\nexport class CorsConfig {\r\n  constructor(private readonly configService: ConfigService) {}\r\n\r\n  /**\r\n   * Generate CORS configuration based on environment\r\n   */\r\n  getCorsOptions(): CorsOptions {\r\n    const environment = this.configService.get('NODE_ENV', 'development');\r\n    \r\n    switch (environment) {\r\n      case 'production':\r\n        return this.getProductionCorsOptions();\r\n      case 'staging':\r\n        return this.getStagingCorsOptions();\r\n      case 'test':\r\n        return this.getTestCorsOptions();\r\n      default:\r\n        return this.getDevelopmentCorsOptions();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Production CORS configuration - Most restrictive\r\n   */\r\n  private getProductionCorsOptions(): CorsOptions {\r\n    const allowedOrigins = this.configService.get<string[]>('cors.allowedOrigins', []);\r\n    \r\n    return {\r\n      origin: (origin, callback) => {\r\n        // Allow requests with no origin (mobile apps, Postman, etc.)\r\n        if (!origin) {\r\n          return callback(null, true);\r\n        }\r\n\r\n        // Check against whitelist\r\n        if (allowedOrigins.includes(origin)) {\r\n          return callback(null, true);\r\n        }\r\n\r\n        // Log unauthorized origin attempts\r\n        console.warn(`CORS: Blocked request from unauthorized origin: ${origin}`);\r\n        return callback(new Error('Not allowed by CORS'), false);\r\n      },\r\n      methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],\r\n      allowedHeaders: [\r\n        'Origin',\r\n        'X-Requested-With',\r\n        'Content-Type',\r\n        'Accept',\r\n        'Authorization',\r\n        'X-API-Key',\r\n        'X-Correlation-ID',\r\n        'X-Request-ID',\r\n        'Cache-Control',\r\n      ],\r\n      exposedHeaders: [\r\n        'X-Total-Count',\r\n        'X-Page-Count',\r\n        'X-Current-Page',\r\n        'X-Per-Page',\r\n        'X-Rate-Limit-Limit',\r\n        'X-Rate-Limit-Remaining',\r\n        'X-Rate-Limit-Reset',\r\n        'X-API-Version',\r\n        'X-Request-ID',\r\n      ],\r\n      credentials: true,\r\n      maxAge: 86400, // 24 hours\r\n      preflightContinue: false,\r\n      optionsSuccessStatus: 204,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Staging CORS configuration - Moderate restrictions\r\n   */\r\n  private getStagingCorsOptions(): CorsOptions {\r\n    const allowedOrigins = this.configService.get<string[]>('cors.allowedOrigins', [\r\n      'https://staging.sentinel.com',\r\n      'https://staging-admin.sentinel.com',\r\n    ]);\r\n\r\n    return {\r\n      origin: allowedOrigins,\r\n      methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS', 'HEAD'],\r\n      allowedHeaders: [\r\n        'Origin',\r\n        'X-Requested-With',\r\n        'Content-Type',\r\n        'Accept',\r\n        'Authorization',\r\n        'X-API-Key',\r\n        'X-Correlation-ID',\r\n        'X-Request-ID',\r\n        'Cache-Control',\r\n        'X-Debug-Mode',\r\n      ],\r\n      exposedHeaders: [\r\n        'X-Total-Count',\r\n        'X-Page-Count',\r\n        'X-Current-Page',\r\n        'X-Per-Page',\r\n        'X-Rate-Limit-Limit',\r\n        'X-Rate-Limit-Remaining',\r\n        'X-Rate-Limit-Reset',\r\n        'X-API-Version',\r\n        'X-Request-ID',\r\n        'X-Debug-Info',\r\n      ],\r\n      credentials: true,\r\n      maxAge: 3600, // 1 hour\r\n      preflightContinue: false,\r\n      optionsSuccessStatus: 204,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Development CORS configuration - Permissive for development\r\n   */\r\n  private getDevelopmentCorsOptions(): CorsOptions {\r\n    return {\r\n      origin: true, // Allow all origins in development\r\n      methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS', 'HEAD'],\r\n      allowedHeaders: [\r\n        'Origin',\r\n        'X-Requested-With',\r\n        'Content-Type',\r\n        'Accept',\r\n        'Authorization',\r\n        'X-API-Key',\r\n        'X-Correlation-ID',\r\n        'X-Request-ID',\r\n        'Cache-Control',\r\n        'X-Debug-Mode',\r\n        'X-Test-Mode',\r\n      ],\r\n      exposedHeaders: [\r\n        'X-Total-Count',\r\n        'X-Page-Count',\r\n        'X-Current-Page',\r\n        'X-Per-Page',\r\n        'X-Rate-Limit-Limit',\r\n        'X-Rate-Limit-Remaining',\r\n        'X-Rate-Limit-Reset',\r\n        'X-API-Version',\r\n        'X-Request-ID',\r\n        'X-Debug-Info',\r\n        'X-Performance-Metrics',\r\n      ],\r\n      credentials: true,\r\n      maxAge: 300, // 5 minutes\r\n      preflightContinue: false,\r\n      optionsSuccessStatus: 200,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Test CORS configuration - Minimal restrictions for testing\r\n   */\r\n  private getTestCorsOptions(): CorsOptions {\r\n    return {\r\n      origin: true,\r\n      methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS', 'HEAD'],\r\n      allowedHeaders: '*',\r\n      exposedHeaders: '*',\r\n      credentials: true,\r\n      maxAge: 0, // No caching in tests\r\n      preflightContinue: false,\r\n      optionsSuccessStatus: 200,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Validate CORS configuration\r\n   */\r\n  validateCorsConfig(): boolean {\r\n    try {\r\n      const corsOptions = this.getCorsOptions();\r\n      const environment = this.configService.get('NODE_ENV', 'development');\r\n\r\n      // Validate required properties exist\r\n      if (!corsOptions.methods || !corsOptions.allowedHeaders) {\r\n        throw new Error('CORS configuration missing required properties');\r\n      }\r\n\r\n      // Validate production security\r\n      if (environment === 'production') {\r\n        if (corsOptions.origin === true) {\r\n          throw new Error('Production CORS cannot allow all origins');\r\n        }\r\n\r\n        if (!corsOptions.credentials) {\r\n          console.warn('CORS: Credentials not enabled in production');\r\n        }\r\n      }\r\n\r\n      return true;\r\n    } catch (error) {\r\n      console.error('CORS configuration validation failed:', error.message);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get CORS configuration summary for logging\r\n   */\r\n  getCorsConfigSummary(): Record<string, any> {\r\n    const corsOptions = this.getCorsOptions();\r\n    const environment = this.configService.get('NODE_ENV', 'development');\r\n\r\n    return {\r\n      environment,\r\n      allowsCredentials: corsOptions.credentials,\r\n      maxAge: corsOptions.maxAge,\r\n      methodsCount: Array.isArray(corsOptions.methods) ? corsOptions.methods.length : 'all',\r\n      headersCount: Array.isArray(corsOptions.allowedHeaders) ? corsOptions.allowedHeaders.length : 'all',\r\n      originPolicy: typeof corsOptions.origin === 'boolean' ? \r\n        (corsOptions.origin ? 'allow-all' : 'deny-all') : 'whitelist',\r\n    };\r\n  }\r\n}"], "version": 3}