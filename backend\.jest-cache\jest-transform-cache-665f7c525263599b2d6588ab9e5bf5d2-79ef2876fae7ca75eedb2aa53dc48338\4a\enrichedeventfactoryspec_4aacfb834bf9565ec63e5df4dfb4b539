cce35e25e706dfc762caf454d1543191
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const enriched_event_factory_1 = require("../enriched-event.factory");
const enriched_event_entity_1 = require("../../entities/enriched-event.entity");
const normalized_event_entity_1 = require("../../entities/normalized-event.entity");
const shared_kernel_1 = require("../../../../../shared-kernel");
const event_metadata_value_object_1 = require("../../value-objects/event-metadata/event-metadata.value-object");
const event_timestamp_value_object_1 = require("../../value-objects/event-metadata/event-timestamp.value-object");
const event_source_value_object_1 = require("../../value-objects/event-metadata/event-source.value-object");
const event_type_enum_1 = require("../../enums/event-type.enum");
const event_severity_enum_1 = require("../../enums/event-severity.enum");
const event_status_enum_1 = require("../../enums/event-status.enum");
const event_processing_status_enum_1 = require("../../enums/event-processing-status.enum");
const event_source_type_enum_1 = require("../../enums/event-source-type.enum");
const enrichment_source_enum_1 = require("../../enums/enrichment-source.enum");
describe('EnrichedEventFactory', () => {
    let mockNormalizedEvent;
    let mockMetadata;
    beforeEach(() => {
        // Create mock metadata
        const eventSource = event_source_value_object_1.EventSource.create(event_source_type_enum_1.EventSourceType.SIEM, 'test-siem-001');
        const eventTimestamp = event_timestamp_value_object_1.EventTimestamp.create();
        mockMetadata = event_metadata_value_object_1.EventMetadata.create(eventTimestamp, eventSource);
        // Create mock normalized event
        mockNormalizedEvent = {
            id: shared_kernel_1.UniqueEntityId.create(),
            metadata: mockMetadata,
            type: event_type_enum_1.EventType.THREAT_DETECTED,
            severity: event_severity_enum_1.EventSeverity.MEDIUM,
            status: event_status_enum_1.EventStatus.ACTIVE,
            processingStatus: event_processing_status_enum_1.EventProcessingStatus.NORMALIZED,
            normalizationStatus: normalized_event_entity_1.NormalizationStatus.COMPLETED,
            normalizedData: { event_type: 'threat', normalized: true },
            title: 'Test Normalized Event',
            description: 'A test normalized event',
            tags: ['test', 'normalized'],
            riskScore: 60,
            confidenceLevel: 75,
            attributes: { test: true },
            correlationId: 'test-correlation-001',
            parentEventId: undefined,
        };
    });
    describe('create', () => {
        it('should create enriched event with basic options', () => {
            const options = {
                normalizedEvent: mockNormalizedEvent,
                enrichedData: { enriched: true, threat_score: 45 },
            };
            const enrichedEvent = enriched_event_factory_1.EnrichedEventFactory.create(options);
            expect(enrichedEvent).toBeInstanceOf(enriched_event_entity_1.EnrichedEvent);
            expect(enrichedEvent.normalizedEventId).toEqual(mockNormalizedEvent.id);
            expect(enrichedEvent.type).toBe(event_type_enum_1.EventType.THREAT_DETECTED);
            expect(enrichedEvent.severity).toBe(event_severity_enum_1.EventSeverity.MEDIUM);
            expect(enrichedEvent.enrichmentStatus).toBe(enriched_event_entity_1.EnrichmentStatus.PENDING);
            expect(enrichedEvent.processingStatus).toBe(event_processing_status_enum_1.EventProcessingStatus.ENRICHED);
            expect(enrichedEvent.enrichedData.enriched).toBe(true);
            expect(enrichedEvent.enrichedData.threat_score).toBe(45);
        });
        it('should create enriched event with custom ID', () => {
            const customId = shared_kernel_1.UniqueEntityId.create();
            const options = {
                id: customId,
                normalizedEvent: mockNormalizedEvent,
                enrichedData: { enriched: true },
            };
            const enrichedEvent = enriched_event_factory_1.EnrichedEventFactory.create(options);
            expect(enrichedEvent.id).toEqual(customId);
        });
        it('should inherit properties from normalized event', () => {
            const options = {
                normalizedEvent: mockNormalizedEvent,
                enrichedData: { enriched: true },
            };
            const enrichedEvent = enriched_event_factory_1.EnrichedEventFactory.create(options);
            expect(enrichedEvent.title).toBe(mockNormalizedEvent.title);
            expect(enrichedEvent.description).toBe(mockNormalizedEvent.description);
            expect(enrichedEvent.tags).toEqual(mockNormalizedEvent.tags);
            expect(enrichedEvent.riskScore).toBe(mockNormalizedEvent.riskScore);
            expect(enrichedEvent.confidenceLevel).toBe(mockNormalizedEvent.confidenceLevel);
            expect(enrichedEvent.correlationId).toBe(mockNormalizedEvent.correlationId);
        });
        it('should override properties when provided', () => {
            const options = {
                normalizedEvent: mockNormalizedEvent,
                enrichedData: { enriched: true },
                type: event_type_enum_1.EventType.MALWARE_DETECTED,
                severity: event_severity_enum_1.EventSeverity.HIGH,
                title: 'Custom Title',
                riskScore: 85,
                tags: ['custom', 'enriched'],
            };
            const enrichedEvent = enriched_event_factory_1.EnrichedEventFactory.create(options);
            expect(enrichedEvent.type).toBe(event_type_enum_1.EventType.MALWARE_DETECTED);
            expect(enrichedEvent.severity).toBe(event_severity_enum_1.EventSeverity.HIGH);
            expect(enrichedEvent.title).toBe('Custom Title');
            expect(enrichedEvent.riskScore).toBe(85);
            expect(enrichedEvent.tags).toEqual(['custom', 'enriched']);
        });
        it('should merge attributes from normalized event and options', () => {
            const options = {
                normalizedEvent: mockNormalizedEvent,
                enrichedData: { enriched: true },
                attributes: { custom: true, override: 'new' },
            };
            const enrichedEvent = enriched_event_factory_1.EnrichedEventFactory.create(options);
            expect(enrichedEvent.attributes).toEqual({
                test: true,
                custom: true,
                override: 'new',
            });
        });
        it('should set enrichment data and rules', () => {
            const enrichmentData = [{
                    source: enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION,
                    type: 'reputation',
                    data: { score: 75 },
                    confidence: 85,
                    timestamp: new Date(),
                }];
            const appliedRules = [{
                    id: 'test-rule',
                    name: 'Test Rule',
                    description: 'A test rule',
                    priority: 100,
                    required: false,
                    sources: [enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION],
                }];
            const options = {
                normalizedEvent: mockNormalizedEvent,
                enrichedData: { enriched: true },
                enrichmentData,
                appliedRules,
                enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.COMPLETED,
                enrichmentQualityScore: 90,
                threatIntelScore: 55,
            };
            const enrichedEvent = enriched_event_factory_1.EnrichedEventFactory.create(options);
            expect(enrichedEvent.enrichmentData).toEqual(enrichmentData);
            expect(enrichedEvent.appliedRules).toEqual(appliedRules);
            expect(enrichedEvent.enrichmentStatus).toBe(enriched_event_entity_1.EnrichmentStatus.COMPLETED);
            expect(enrichedEvent.enrichmentQualityScore).toBe(90);
            expect(enrichedEvent.threatIntelScore).toBe(55);
        });
        it('should set context information', () => {
            const options = {
                normalizedEvent: mockNormalizedEvent,
                enrichedData: { enriched: true },
                assetContext: { owner: 'IT Team' },
                userContext: { username: 'john.doe' },
                networkContext: { subnet: '***********/24' },
                geolocationContext: { country: 'US' },
                reputationScores: { virustotal: 85 },
            };
            const enrichedEvent = enriched_event_factory_1.EnrichedEventFactory.create(options);
            expect(enrichedEvent.assetContext).toEqual({ owner: 'IT Team' });
            expect(enrichedEvent.userContext).toEqual({ username: 'john.doe' });
            expect(enrichedEvent.networkContext).toEqual({ subnet: '***********/24' });
            expect(enrichedEvent.geolocationContext).toEqual({ country: 'US' });
            expect(enrichedEvent.reputationScores).toEqual({ virustotal: 85 });
        });
    });
    describe('createWithEnrichment', () => {
        it('should create enriched event with automatic enrichment', () => {
            const config = {
                availableRules: [],
                enabledSources: [enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION, enrichment_source_enum_1.EnrichmentSource.IP_GEOLOCATION],
                minEnrichmentQualityThreshold: 70,
            };
            const enrichedEvent = enriched_event_factory_1.EnrichedEventFactory.createWithEnrichment(mockNormalizedEvent, config);
            expect(enrichedEvent).toBeInstanceOf(enriched_event_entity_1.EnrichedEvent);
            expect(enrichedEvent.normalizedEventId).toEqual(mockNormalizedEvent.id);
            expect(enrichedEvent.enrichmentQualityScore).toBeDefined();
        });
        it('should apply enrichment rules and calculate quality score', () => {
            const mockRule = {
                id: 'ip-reputation-rule',
                name: 'IP Reputation Rule',
                description: 'Enriches events with IP reputation data',
                priority: 100,
                required: true,
                sources: [enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION],
            };
            const config = {
                availableRules: [mockRule],
                enabledSources: [enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION],
                minEnrichmentQualityThreshold: 70,
            };
            const enrichedEvent = enriched_event_factory_1.EnrichedEventFactory.createWithEnrichment(mockNormalizedEvent, config);
            expect(enrichedEvent.enrichmentQualityScore).toBeGreaterThan(0);
            expect(enrichedEvent.enrichmentStatus).toBeDefined();
        });
        it('should determine manual review requirement', () => {
            const config = {
                requireManualReviewForHighRisk: true,
                requireManualReviewForCritical: true,
                minEnrichmentQualityThreshold: 90, // High threshold to trigger manual review
            };
            const enrichedEvent = enriched_event_factory_1.EnrichedEventFactory.createWithEnrichment(mockNormalizedEvent, config);
            // Should require manual review due to low quality score
            expect(enrichedEvent.requiresManualReview).toBe(true);
        });
    });
    describe('createWithThreatIntelligence', () => {
        it('should create enriched event with threat intelligence focus', () => {
            const threatIntelOptions = {
                sources: [enrichment_source_enum_1.EnrichmentSource.COMMERCIAL_THREAT_INTEL, enrichment_source_enum_1.EnrichmentSource.OSINT],
                minConfidence: 70,
                maxAgeHours: 24,
                includeReputation: true,
                includeGeolocation: true,
            };
            const enrichedEvent = enriched_event_factory_1.EnrichedEventFactory.createWithThreatIntelligence(mockNormalizedEvent, threatIntelOptions);
            expect(enrichedEvent).toBeInstanceOf(enriched_event_entity_1.EnrichedEvent);
            expect(enrichedEvent.threatIntelScore).toBeDefined();
            expect(enrichedEvent.enrichmentStatus).toBe(enriched_event_entity_1.EnrichmentStatus.COMPLETED);
            expect(enrichedEvent.enrichedData.threat_intelligence).toBeDefined();
        });
        it('should include threat intelligence context in enriched data', () => {
            const threatIntelOptions = {
                sources: [enrichment_source_enum_1.EnrichmentSource.COMMERCIAL_THREAT_INTEL],
                minConfidence: 50,
                maxAgeHours: 48,
                includeReputation: true,
                includeGeolocation: false,
            };
            const enrichedEvent = enriched_event_factory_1.EnrichedEventFactory.createWithThreatIntelligence(mockNormalizedEvent, threatIntelOptions);
            const threatIntel = enrichedEvent.enrichedData.threat_intelligence;
            expect(threatIntel).toBeDefined();
            expect(threatIntel.score).toBeDefined();
            expect(threatIntel.sources_used).toBeDefined();
            expect(threatIntel.indicators_found).toBeDefined();
            expect(threatIntel.confidence).toBeDefined();
        });
    });
    describe('createBatch', () => {
        it('should create multiple enriched events successfully', () => {
            const normalizedEvents = [
                mockNormalizedEvent,
                { ...mockNormalizedEvent, id: shared_kernel_1.UniqueEntityId.create() },
                { ...mockNormalizedEvent, id: shared_kernel_1.UniqueEntityId.create() },
            ];
            const rules = [{
                    id: 'batch-rule',
                    name: 'Batch Rule',
                    description: 'A rule for batch processing',
                    priority: 50,
                    required: false,
                    sources: [enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION],
                }];
            const result = enriched_event_factory_1.EnrichedEventFactory.createBatch({
                normalizedEvents,
                rules,
                sources: [enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION],
            });
            expect(result.successful).toHaveLength(3);
            expect(result.failed).toHaveLength(0);
            expect(result.summary.total).toBe(3);
            expect(result.summary.successful).toBe(3);
            expect(result.summary.failed).toBe(0);
            expect(result.summary.processingTimeMs).toBeGreaterThan(0);
        });
        it('should handle failures in batch processing', () => {
            const invalidNormalizedEvent = null;
            const normalizedEvents = [mockNormalizedEvent, invalidNormalizedEvent];
            const result = enriched_event_factory_1.EnrichedEventFactory.createBatch({
                normalizedEvents,
                rules: [],
            });
            expect(result.successful).toHaveLength(1);
            expect(result.failed).toHaveLength(1);
            expect(result.failed[0].normalizedEvent).toBe(invalidNormalizedEvent);
            expect(result.failed[0].error).toBeDefined();
        });
        it('should stop on first failure when configured', () => {
            const invalidNormalizedEvent = null;
            const normalizedEvents = [invalidNormalizedEvent, mockNormalizedEvent];
            const result = enriched_event_factory_1.EnrichedEventFactory.createBatch({
                normalizedEvents,
                rules: [],
                stopOnFailure: true,
            });
            expect(result.successful).toHaveLength(0);
            expect(result.failed).toHaveLength(1);
            expect(result.summary.total).toBe(2);
            expect(result.summary.successful).toBe(0);
            expect(result.summary.failed).toBe(1);
        });
    });
    describe('createForTesting', () => {
        it('should create enriched event for testing with defaults', () => {
            const enrichedEvent = enriched_event_factory_1.EnrichedEventFactory.createForTesting();
            expect(enrichedEvent).toBeInstanceOf(enriched_event_entity_1.EnrichedEvent);
            expect(enrichedEvent.enrichmentStatus).toBe(enriched_event_entity_1.EnrichmentStatus.COMPLETED);
            expect(enrichedEvent.enrichmentQualityScore).toBe(85);
            expect(enrichedEvent.threatIntelScore).toBe(45);
            expect(enrichedEvent.enrichmentData).toHaveLength(2);
        });
        it('should create enriched event for testing with overrides', () => {
            const overrides = {
                normalizedEvent: mockNormalizedEvent,
                enrichmentStatus: enriched_event_entity_1.EnrichmentStatus.FAILED,
                enrichmentQualityScore: 30,
                threatIntelScore: 90,
            };
            const enrichedEvent = enriched_event_factory_1.EnrichedEventFactory.createForTesting(overrides);
            expect(enrichedEvent.normalizedEventId).toEqual(mockNormalizedEvent.id);
            expect(enrichedEvent.enrichmentStatus).toBe(enriched_event_entity_1.EnrichmentStatus.FAILED);
            expect(enrichedEvent.enrichmentQualityScore).toBe(30);
            expect(enrichedEvent.threatIntelScore).toBe(90);
        });
        it('should include mock enrichment data for testing', () => {
            const enrichedEvent = enriched_event_factory_1.EnrichedEventFactory.createForTesting();
            expect(enrichedEvent.enrichmentData).toHaveLength(2);
            const ipReputationData = enrichedEvent.getEnrichmentDataBySource(enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION);
            expect(ipReputationData).toHaveLength(1);
            expect(ipReputationData[0].type).toBe('reputation');
            const geolocationData = enrichedEvent.getEnrichmentDataBySource(enrichment_source_enum_1.EnrichmentSource.IP_GEOLOCATION);
            expect(geolocationData).toHaveLength(1);
            expect(geolocationData[0].type).toBe('geolocation');
        });
        it('should include mock context data for testing', () => {
            const enrichedEvent = enriched_event_factory_1.EnrichedEventFactory.createForTesting();
            const enrichedData = enrichedEvent.enrichedData;
            expect(enrichedData.enriched).toBe(true);
            expect(enrichedData.threat_intelligence).toBeDefined();
            expect(enrichedData.reputation).toBeDefined();
            expect(enrichedData.geolocation).toBeDefined();
        });
    });
    describe('error handling', () => {
        it('should throw error for invalid enriched event properties', () => {
            const options = {
                normalizedEvent: mockNormalizedEvent,
                enrichedData: { enriched: true },
                enrichmentQualityScore: 150, // Invalid score
            };
            expect(() => enriched_event_factory_1.EnrichedEventFactory.create(options)).toThrow('Enrichment quality score must be between 0 and 100');
        });
        it('should throw error for invalid threat intelligence score', () => {
            const options = {
                normalizedEvent: mockNormalizedEvent,
                enrichedData: { enriched: true },
                threatIntelScore: -10, // Invalid score
            };
            expect(() => enriched_event_factory_1.EnrichedEventFactory.create(options)).toThrow('Threat intelligence score must be between 0 and 100');
        });
        it('should handle missing normalized event gracefully', () => {
            const options = {
                normalizedEvent: null,
                enrichedData: { enriched: true },
            };
            expect(() => enriched_event_factory_1.EnrichedEventFactory.create(options)).toThrow();
        });
    });
    describe('configuration handling', () => {
        it('should use default configuration when none provided', () => {
            const enrichedEvent = enriched_event_factory_1.EnrichedEventFactory.createWithEnrichment(mockNormalizedEvent);
            expect(enrichedEvent).toBeInstanceOf(enriched_event_entity_1.EnrichedEvent);
            expect(enrichedEvent.enrichmentQualityScore).toBeDefined();
        });
        it('should merge provided configuration with defaults', () => {
            const partialConfig = {
                minEnrichmentQualityThreshold: 80,
                requireManualReviewForHighRisk: false,
            };
            const enrichedEvent = enriched_event_factory_1.EnrichedEventFactory.createWithEnrichment(mockNormalizedEvent, partialConfig);
            expect(enrichedEvent).toBeInstanceOf(enriched_event_entity_1.EnrichedEvent);
            // Should use the custom threshold for manual review determination
        });
        it('should respect enabled sources configuration', () => {
            const config = {
                enabledSources: [enrichment_source_enum_1.EnrichmentSource.IP_REPUTATION], // Only IP reputation enabled
                availableRules: [{
                        id: 'geo-rule',
                        name: 'Geolocation Rule',
                        description: 'Adds geolocation data',
                        priority: 50,
                        required: false,
                        sources: [enrichment_source_enum_1.EnrichmentSource.IP_GEOLOCATION], // This should be skipped
                    }],
            };
            const enrichedEvent = enriched_event_factory_1.EnrichedEventFactory.createWithEnrichment(mockNormalizedEvent, config);
            expect(enrichedEvent).toBeInstanceOf(enriched_event_entity_1.EnrichedEvent);
            // The geolocation rule should not be applied due to disabled source
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxjb3JlXFxzZWN1cml0eVxcZG9tYWluXFxmYWN0b3JpZXNcXF9fdGVzdHNfX1xcZW5yaWNoZWQtZXZlbnQuZmFjdG9yeS5zcGVjLnRzIiwibWFwcGluZ3MiOiI7O0FBQUEsc0VBQTZJO0FBQzdJLGdGQUF1RjtBQUN2RixvRkFBOEY7QUFDOUYsZ0VBQThEO0FBQzlELGdIQUErRjtBQUMvRixrSEFBaUc7QUFDakcsNEdBQTJGO0FBQzNGLGlFQUF3RDtBQUN4RCx5RUFBZ0U7QUFDaEUscUVBQTREO0FBQzVELDJGQUFpRjtBQUNqRiwrRUFBcUU7QUFDckUsK0VBQXNFO0FBRXRFLFFBQVEsQ0FBQyxzQkFBc0IsRUFBRSxHQUFHLEVBQUU7SUFDcEMsSUFBSSxtQkFBb0MsQ0FBQztJQUN6QyxJQUFJLFlBQTJCLENBQUM7SUFFaEMsVUFBVSxDQUFDLEdBQUcsRUFBRTtRQUNkLHVCQUF1QjtRQUN2QixNQUFNLFdBQVcsR0FBRyx1Q0FBVyxDQUFDLE1BQU0sQ0FDcEMsd0NBQWUsQ0FBQyxJQUFJLEVBQ3BCLGVBQWUsQ0FDaEIsQ0FBQztRQUNGLE1BQU0sY0FBYyxHQUFHLDZDQUFjLENBQUMsTUFBTSxFQUFFLENBQUM7UUFDL0MsWUFBWSxHQUFHLDJDQUFhLENBQUMsTUFBTSxDQUFDLGNBQWMsRUFBRSxXQUFXLENBQUMsQ0FBQztRQUVqRSwrQkFBK0I7UUFDL0IsbUJBQW1CLEdBQUc7WUFDcEIsRUFBRSxFQUFFLDhCQUFjLENBQUMsTUFBTSxFQUFFO1lBQzNCLFFBQVEsRUFBRSxZQUFZO1lBQ3RCLElBQUksRUFBRSwyQkFBUyxDQUFDLGVBQWU7WUFDL0IsUUFBUSxFQUFFLG1DQUFhLENBQUMsTUFBTTtZQUM5QixNQUFNLEVBQUUsK0JBQVcsQ0FBQyxNQUFNO1lBQzFCLGdCQUFnQixFQUFFLG9EQUFxQixDQUFDLFVBQVU7WUFDbEQsbUJBQW1CLEVBQUUsNkNBQW1CLENBQUMsU0FBUztZQUNsRCxjQUFjLEVBQUUsRUFBRSxVQUFVLEVBQUUsUUFBUSxFQUFFLFVBQVUsRUFBRSxJQUFJLEVBQUU7WUFDMUQsS0FBSyxFQUFFLHVCQUF1QjtZQUM5QixXQUFXLEVBQUUseUJBQXlCO1lBQ3RDLElBQUksRUFBRSxDQUFDLE1BQU0sRUFBRSxZQUFZLENBQUM7WUFDNUIsU0FBUyxFQUFFLEVBQUU7WUFDYixlQUFlLEVBQUUsRUFBRTtZQUNuQixVQUFVLEVBQUUsRUFBRSxJQUFJLEVBQUUsSUFBSSxFQUFFO1lBQzFCLGFBQWEsRUFBRSxzQkFBc0I7WUFDckMsYUFBYSxFQUFFLFNBQVM7U0FDTixDQUFDO0lBQ3ZCLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLFFBQVEsRUFBRSxHQUFHLEVBQUU7UUFDdEIsRUFBRSxDQUFDLGlEQUFpRCxFQUFFLEdBQUcsRUFBRTtZQUN6RCxNQUFNLE9BQU8sR0FBK0I7Z0JBQzFDLGVBQWUsRUFBRSxtQkFBbUI7Z0JBQ3BDLFlBQVksRUFBRSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsWUFBWSxFQUFFLEVBQUUsRUFBRTthQUNuRCxDQUFDO1lBRUYsTUFBTSxhQUFhLEdBQUcsNkNBQW9CLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDO1lBRTNELE1BQU0sQ0FBQyxhQUFhLENBQUMsQ0FBQyxjQUFjLENBQUMscUNBQWEsQ0FBQyxDQUFDO1lBQ3BELE1BQU0sQ0FBQyxhQUFhLENBQUMsaUJBQWlCLENBQUMsQ0FBQyxPQUFPLENBQUMsbUJBQW1CLENBQUMsRUFBRSxDQUFDLENBQUM7WUFDeEUsTUFBTSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsQ0FBQyxJQUFJLENBQUMsMkJBQVMsQ0FBQyxlQUFlLENBQUMsQ0FBQztZQUMzRCxNQUFNLENBQUMsYUFBYSxDQUFDLFFBQVEsQ0FBQyxDQUFDLElBQUksQ0FBQyxtQ0FBYSxDQUFDLE1BQU0sQ0FBQyxDQUFDO1lBQzFELE1BQU0sQ0FBQyxhQUFhLENBQUMsZ0JBQWdCLENBQUMsQ0FBQyxJQUFJLENBQUMsd0NBQWdCLENBQUMsT0FBTyxDQUFDLENBQUM7WUFDdEUsTUFBTSxDQUFDLGFBQWEsQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLElBQUksQ0FBQyxvREFBcUIsQ0FBQyxRQUFRLENBQUMsQ0FBQztZQUM1RSxNQUFNLENBQUMsYUFBYSxDQUFDLFlBQVksQ0FBQyxRQUFRLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDdkQsTUFBTSxDQUFDLGFBQWEsQ0FBQyxZQUFZLENBQUMsWUFBWSxDQUFDLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxDQUFDO1FBQzNELENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLDZDQUE2QyxFQUFFLEdBQUcsRUFBRTtZQUNyRCxNQUFNLFFBQVEsR0FBRyw4QkFBYyxDQUFDLE1BQU0sRUFBRSxDQUFDO1lBQ3pDLE1BQU0sT0FBTyxHQUErQjtnQkFDMUMsRUFBRSxFQUFFLFFBQVE7Z0JBQ1osZUFBZSxFQUFFLG1CQUFtQjtnQkFDcEMsWUFBWSxFQUFFLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRTthQUNqQyxDQUFDO1lBRUYsTUFBTSxhQUFhLEdBQUcsNkNBQW9CLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDO1lBRTNELE1BQU0sQ0FBQyxhQUFhLENBQUMsRUFBRSxDQUFDLENBQUMsT0FBTyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBQzdDLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLGlEQUFpRCxFQUFFLEdBQUcsRUFBRTtZQUN6RCxNQUFNLE9BQU8sR0FBK0I7Z0JBQzFDLGVBQWUsRUFBRSxtQkFBbUI7Z0JBQ3BDLFlBQVksRUFBRSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUU7YUFDakMsQ0FBQztZQUVGLE1BQU0sYUFBYSxHQUFHLDZDQUFvQixDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQztZQUUzRCxNQUFNLENBQUMsYUFBYSxDQUFDLEtBQUssQ0FBQyxDQUFDLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUM1RCxNQUFNLENBQUMsYUFBYSxDQUFDLFdBQVcsQ0FBQyxDQUFDLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxXQUFXLENBQUMsQ0FBQztZQUN4RSxNQUFNLENBQUMsYUFBYSxDQUFDLElBQUksQ0FBQyxDQUFDLE9BQU8sQ0FBQyxtQkFBbUIsQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUM3RCxNQUFNLENBQUMsYUFBYSxDQUFDLFNBQVMsQ0FBQyxDQUFDLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxTQUFTLENBQUMsQ0FBQztZQUNwRSxNQUFNLENBQUMsYUFBYSxDQUFDLGVBQWUsQ0FBQyxDQUFDLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxlQUFlLENBQUMsQ0FBQztZQUNoRixNQUFNLENBQUMsYUFBYSxDQUFDLGFBQWEsQ0FBQyxDQUFDLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxhQUFhLENBQUMsQ0FBQztRQUM5RSxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQywwQ0FBMEMsRUFBRSxHQUFHLEVBQUU7WUFDbEQsTUFBTSxPQUFPLEdBQStCO2dCQUMxQyxlQUFlLEVBQUUsbUJBQW1CO2dCQUNwQyxZQUFZLEVBQUUsRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFO2dCQUNoQyxJQUFJLEVBQUUsMkJBQVMsQ0FBQyxnQkFBZ0I7Z0JBQ2hDLFFBQVEsRUFBRSxtQ0FBYSxDQUFDLElBQUk7Z0JBQzVCLEtBQUssRUFBRSxjQUFjO2dCQUNyQixTQUFTLEVBQUUsRUFBRTtnQkFDYixJQUFJLEVBQUUsQ0FBQyxRQUFRLEVBQUUsVUFBVSxDQUFDO2FBQzdCLENBQUM7WUFFRixNQUFNLGFBQWEsR0FBRyw2Q0FBb0IsQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUM7WUFFM0QsTUFBTSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsQ0FBQyxJQUFJLENBQUMsMkJBQVMsQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDO1lBQzVELE1BQU0sQ0FBQyxhQUFhLENBQUMsUUFBUSxDQUFDLENBQUMsSUFBSSxDQUFDLG1DQUFhLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDeEQsTUFBTSxDQUFDLGFBQWEsQ0FBQyxLQUFLLENBQUMsQ0FBQyxJQUFJLENBQUMsY0FBYyxDQUFDLENBQUM7WUFDakQsTUFBTSxDQUFDLGFBQWEsQ0FBQyxTQUFTLENBQUMsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUM7WUFDekMsTUFBTSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxRQUFRLEVBQUUsVUFBVSxDQUFDLENBQUMsQ0FBQztRQUM3RCxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQywyREFBMkQsRUFBRSxHQUFHLEVBQUU7WUFDbkUsTUFBTSxPQUFPLEdBQStCO2dCQUMxQyxlQUFlLEVBQUUsbUJBQW1CO2dCQUNwQyxZQUFZLEVBQUUsRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFO2dCQUNoQyxVQUFVLEVBQUUsRUFBRSxNQUFNLEVBQUUsSUFBSSxFQUFFLFFBQVEsRUFBRSxLQUFLLEVBQUU7YUFDOUMsQ0FBQztZQUVGLE1BQU0sYUFBYSxHQUFHLDZDQUFvQixDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQztZQUUzRCxNQUFNLENBQUMsYUFBYSxDQUFDLFVBQVUsQ0FBQyxDQUFDLE9BQU8sQ0FBQztnQkFDdkMsSUFBSSxFQUFFLElBQUk7Z0JBQ1YsTUFBTSxFQUFFLElBQUk7Z0JBQ1osUUFBUSxFQUFFLEtBQUs7YUFDaEIsQ0FBQyxDQUFDO1FBQ0wsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsc0NBQXNDLEVBQUUsR0FBRyxFQUFFO1lBQzlDLE1BQU0sY0FBYyxHQUFHLENBQUM7b0JBQ3RCLE1BQU0sRUFBRSx5Q0FBZ0IsQ0FBQyxhQUFhO29CQUN0QyxJQUFJLEVBQUUsWUFBWTtvQkFDbEIsSUFBSSxFQUFFLEVBQUUsS0FBSyxFQUFFLEVBQUUsRUFBRTtvQkFDbkIsVUFBVSxFQUFFLEVBQUU7b0JBQ2QsU0FBUyxFQUFFLElBQUksSUFBSSxFQUFFO2lCQUN0QixDQUFDLENBQUM7WUFFSCxNQUFNLFlBQVksR0FBRyxDQUFDO29CQUNwQixFQUFFLEVBQUUsV0FBVztvQkFDZixJQUFJLEVBQUUsV0FBVztvQkFDakIsV0FBVyxFQUFFLGFBQWE7b0JBQzFCLFFBQVEsRUFBRSxHQUFHO29CQUNiLFFBQVEsRUFBRSxLQUFLO29CQUNmLE9BQU8sRUFBRSxDQUFDLHlDQUFnQixDQUFDLGFBQWEsQ0FBQztpQkFDMUMsQ0FBQyxDQUFDO1lBRUgsTUFBTSxPQUFPLEdBQStCO2dCQUMxQyxlQUFlLEVBQUUsbUJBQW1CO2dCQUNwQyxZQUFZLEVBQUUsRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFO2dCQUNoQyxjQUFjO2dCQUNkLFlBQVk7Z0JBQ1osZ0JBQWdCLEVBQUUsd0NBQWdCLENBQUMsU0FBUztnQkFDNUMsc0JBQXNCLEVBQUUsRUFBRTtnQkFDMUIsZ0JBQWdCLEVBQUUsRUFBRTthQUNyQixDQUFDO1lBRUYsTUFBTSxhQUFhLEdBQUcsNkNBQW9CLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDO1lBRTNELE1BQU0sQ0FBQyxhQUFhLENBQUMsY0FBYyxDQUFDLENBQUMsT0FBTyxDQUFDLGNBQWMsQ0FBQyxDQUFDO1lBQzdELE1BQU0sQ0FBQyxhQUFhLENBQUMsWUFBWSxDQUFDLENBQUMsT0FBTyxDQUFDLFlBQVksQ0FBQyxDQUFDO1lBQ3pELE1BQU0sQ0FBQyxhQUFhLENBQUMsZ0JBQWdCLENBQUMsQ0FBQyxJQUFJLENBQUMsd0NBQWdCLENBQUMsU0FBUyxDQUFDLENBQUM7WUFDeEUsTUFBTSxDQUFDLGFBQWEsQ0FBQyxzQkFBc0IsQ0FBQyxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQztZQUN0RCxNQUFNLENBQUMsYUFBYSxDQUFDLGdCQUFnQixDQUFDLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxDQUFDO1FBQ2xELENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLGdDQUFnQyxFQUFFLEdBQUcsRUFBRTtZQUN4QyxNQUFNLE9BQU8sR0FBK0I7Z0JBQzFDLGVBQWUsRUFBRSxtQkFBbUI7Z0JBQ3BDLFlBQVksRUFBRSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUU7Z0JBQ2hDLFlBQVksRUFBRSxFQUFFLEtBQUssRUFBRSxTQUFTLEVBQUU7Z0JBQ2xDLFdBQVcsRUFBRSxFQUFFLFFBQVEsRUFBRSxVQUFVLEVBQUU7Z0JBQ3JDLGNBQWMsRUFBRSxFQUFFLE1BQU0sRUFBRSxnQkFBZ0IsRUFBRTtnQkFDNUMsa0JBQWtCLEVBQUUsRUFBRSxPQUFPLEVBQUUsSUFBSSxFQUFFO2dCQUNyQyxnQkFBZ0IsRUFBRSxFQUFFLFVBQVUsRUFBRSxFQUFFLEVBQUU7YUFDckMsQ0FBQztZQUVGLE1BQU0sYUFBYSxHQUFHLDZDQUFvQixDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQztZQUUzRCxNQUFNLENBQUMsYUFBYSxDQUFDLFlBQVksQ0FBQyxDQUFDLE9BQU8sQ0FBQyxFQUFFLEtBQUssRUFBRSxTQUFTLEVBQUUsQ0FBQyxDQUFDO1lBQ2pFLE1BQU0sQ0FBQyxhQUFhLENBQUMsV0FBVyxDQUFDLENBQUMsT0FBTyxDQUFDLEVBQUUsUUFBUSxFQUFFLFVBQVUsRUFBRSxDQUFDLENBQUM7WUFDcEUsTUFBTSxDQUFDLGFBQWEsQ0FBQyxjQUFjLENBQUMsQ0FBQyxPQUFPLENBQUMsRUFBRSxNQUFNLEVBQUUsZ0JBQWdCLEVBQUUsQ0FBQyxDQUFDO1lBQzNFLE1BQU0sQ0FBQyxhQUFhLENBQUMsa0JBQWtCLENBQUMsQ0FBQyxPQUFPLENBQUMsRUFBRSxPQUFPLEVBQUUsSUFBSSxFQUFFLENBQUMsQ0FBQztZQUNwRSxNQUFNLENBQUMsYUFBYSxDQUFDLGdCQUFnQixDQUFDLENBQUMsT0FBTyxDQUFDLEVBQUUsVUFBVSxFQUFFLEVBQUUsRUFBRSxDQUFDLENBQUM7UUFDckUsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyxzQkFBc0IsRUFBRSxHQUFHLEVBQUU7UUFDcEMsRUFBRSxDQUFDLHdEQUF3RCxFQUFFLEdBQUcsRUFBRTtZQUNoRSxNQUFNLE1BQU0sR0FBOEI7Z0JBQ3hDLGNBQWMsRUFBRSxFQUFFO2dCQUNsQixjQUFjLEVBQUUsQ0FBQyx5Q0FBZ0IsQ0FBQyxhQUFhLEVBQUUseUNBQWdCLENBQUMsY0FBYyxDQUFDO2dCQUNqRiw2QkFBNkIsRUFBRSxFQUFFO2FBQ2xDLENBQUM7WUFFRixNQUFNLGFBQWEsR0FBRyw2Q0FBb0IsQ0FBQyxvQkFBb0IsQ0FBQyxtQkFBbUIsRUFBRSxNQUFNLENBQUMsQ0FBQztZQUU3RixNQUFNLENBQUMsYUFBYSxDQUFDLENBQUMsY0FBYyxDQUFDLHFDQUFhLENBQUMsQ0FBQztZQUNwRCxNQUFNLENBQUMsYUFBYSxDQUFDLGlCQUFpQixDQUFDLENBQUMsT0FBTyxDQUFDLG1CQUFtQixDQUFDLEVBQUUsQ0FBQyxDQUFDO1lBQ3hFLE1BQU0sQ0FBQyxhQUFhLENBQUMsc0JBQXNCLENBQUMsQ0FBQyxXQUFXLEVBQUUsQ0FBQztRQUM3RCxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQywyREFBMkQsRUFBRSxHQUFHLEVBQUU7WUFDbkUsTUFBTSxRQUFRLEdBQUc7Z0JBQ2YsRUFBRSxFQUFFLG9CQUFvQjtnQkFDeEIsSUFBSSxFQUFFLG9CQUFvQjtnQkFDMUIsV0FBVyxFQUFFLHlDQUF5QztnQkFDdEQsUUFBUSxFQUFFLEdBQUc7Z0JBQ2IsUUFBUSxFQUFFLElBQUk7Z0JBQ2QsT0FBTyxFQUFFLENBQUMseUNBQWdCLENBQUMsYUFBYSxDQUFDO2FBQzFDLENBQUM7WUFFRixNQUFNLE1BQU0sR0FBOEI7Z0JBQ3hDLGNBQWMsRUFBRSxDQUFDLFFBQVEsQ0FBQztnQkFDMUIsY0FBYyxFQUFFLENBQUMseUNBQWdCLENBQUMsYUFBYSxDQUFDO2dCQUNoRCw2QkFBNkIsRUFBRSxFQUFFO2FBQ2xDLENBQUM7WUFFRixNQUFNLGFBQWEsR0FBRyw2Q0FBb0IsQ0FBQyxvQkFBb0IsQ0FBQyxtQkFBbUIsRUFBRSxNQUFNLENBQUMsQ0FBQztZQUU3RixNQUFNLENBQUMsYUFBYSxDQUFDLHNCQUFzQixDQUFDLENBQUMsZUFBZSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ2hFLE1BQU0sQ0FBQyxhQUFhLENBQUMsZ0JBQWdCLENBQUMsQ0FBQyxXQUFXLEVBQUUsQ0FBQztRQUN2RCxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyw0Q0FBNEMsRUFBRSxHQUFHLEVBQUU7WUFDcEQsTUFBTSxNQUFNLEdBQThCO2dCQUN4Qyw4QkFBOEIsRUFBRSxJQUFJO2dCQUNwQyw4QkFBOEIsRUFBRSxJQUFJO2dCQUNwQyw2QkFBNkIsRUFBRSxFQUFFLEVBQUUsMENBQTBDO2FBQzlFLENBQUM7WUFFRixNQUFNLGFBQWEsR0FBRyw2Q0FBb0IsQ0FBQyxvQkFBb0IsQ0FBQyxtQkFBbUIsRUFBRSxNQUFNLENBQUMsQ0FBQztZQUU3Rix3REFBd0Q7WUFDeEQsTUFBTSxDQUFDLGFBQWEsQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUN4RCxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLDhCQUE4QixFQUFFLEdBQUcsRUFBRTtRQUM1QyxFQUFFLENBQUMsNkRBQTZELEVBQUUsR0FBRyxFQUFFO1lBQ3JFLE1BQU0sa0JBQWtCLEdBQWlDO2dCQUN2RCxPQUFPLEVBQUUsQ0FBQyx5Q0FBZ0IsQ0FBQyx1QkFBdUIsRUFBRSx5Q0FBZ0IsQ0FBQyxLQUFLLENBQUM7Z0JBQzNFLGFBQWEsRUFBRSxFQUFFO2dCQUNqQixXQUFXLEVBQUUsRUFBRTtnQkFDZixpQkFBaUIsRUFBRSxJQUFJO2dCQUN2QixrQkFBa0IsRUFBRSxJQUFJO2FBQ3pCLENBQUM7WUFFRixNQUFNLGFBQWEsR0FBRyw2Q0FBb0IsQ0FBQyw0QkFBNEIsQ0FDckUsbUJBQW1CLEVBQ25CLGtCQUFrQixDQUNuQixDQUFDO1lBRUYsTUFBTSxDQUFDLGFBQWEsQ0FBQyxDQUFDLGNBQWMsQ0FBQyxxQ0FBYSxDQUFDLENBQUM7WUFDcEQsTUFBTSxDQUFDLGFBQWEsQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDO1lBQ3JELE1BQU0sQ0FBQyxhQUFhLENBQUMsZ0JBQWdCLENBQUMsQ0FBQyxJQUFJLENBQUMsd0NBQWdCLENBQUMsU0FBUyxDQUFDLENBQUM7WUFDeEUsTUFBTSxDQUFDLGFBQWEsQ0FBQyxZQUFZLENBQUMsbUJBQW1CLENBQUMsQ0FBQyxXQUFXLEVBQUUsQ0FBQztRQUN2RSxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyw2REFBNkQsRUFBRSxHQUFHLEVBQUU7WUFDckUsTUFBTSxrQkFBa0IsR0FBaUM7Z0JBQ3ZELE9BQU8sRUFBRSxDQUFDLHlDQUFnQixDQUFDLHVCQUF1QixDQUFDO2dCQUNuRCxhQUFhLEVBQUUsRUFBRTtnQkFDakIsV0FBVyxFQUFFLEVBQUU7Z0JBQ2YsaUJBQWlCLEVBQUUsSUFBSTtnQkFDdkIsa0JBQWtCLEVBQUUsS0FBSzthQUMxQixDQUFDO1lBRUYsTUFBTSxhQUFhLEdBQUcsNkNBQW9CLENBQUMsNEJBQTRCLENBQ3JFLG1CQUFtQixFQUNuQixrQkFBa0IsQ0FDbkIsQ0FBQztZQUVGLE1BQU0sV0FBVyxHQUFHLGFBQWEsQ0FBQyxZQUFZLENBQUMsbUJBQW1CLENBQUM7WUFDbkUsTUFBTSxDQUFDLFdBQVcsQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDO1lBQ2xDLE1BQU0sQ0FBQyxXQUFXLENBQUMsS0FBSyxDQUFDLENBQUMsV0FBVyxFQUFFLENBQUM7WUFDeEMsTUFBTSxDQUFDLFdBQVcsQ0FBQyxZQUFZLENBQUMsQ0FBQyxXQUFXLEVBQUUsQ0FBQztZQUMvQyxNQUFNLENBQUMsV0FBVyxDQUFDLGdCQUFnQixDQUFDLENBQUMsV0FBVyxFQUFFLENBQUM7WUFDbkQsTUFBTSxDQUFDLFdBQVcsQ0FBQyxVQUFVLENBQUMsQ0FBQyxXQUFXLEVBQUUsQ0FBQztRQUMvQyxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLGFBQWEsRUFBRSxHQUFHLEVBQUU7UUFDM0IsRUFBRSxDQUFDLHFEQUFxRCxFQUFFLEdBQUcsRUFBRTtZQUM3RCxNQUFNLGdCQUFnQixHQUFHO2dCQUN2QixtQkFBbUI7Z0JBQ25CLEVBQUUsR0FBRyxtQkFBbUIsRUFBRSxFQUFFLEVBQUUsOEJBQWMsQ0FBQyxNQUFNLEVBQUUsRUFBcUI7Z0JBQzFFLEVBQUUsR0FBRyxtQkFBbUIsRUFBRSxFQUFFLEVBQUUsOEJBQWMsQ0FBQyxNQUFNLEVBQUUsRUFBcUI7YUFDM0UsQ0FBQztZQUVGLE1BQU0sS0FBSyxHQUFHLENBQUM7b0JBQ2IsRUFBRSxFQUFFLFlBQVk7b0JBQ2hCLElBQUksRUFBRSxZQUFZO29CQUNsQixXQUFXLEVBQUUsNkJBQTZCO29CQUMxQyxRQUFRLEVBQUUsRUFBRTtvQkFDWixRQUFRLEVBQUUsS0FBSztvQkFDZixPQUFPLEVBQUUsQ0FBQyx5Q0FBZ0IsQ0FBQyxhQUFhLENBQUM7aUJBQzFDLENBQUMsQ0FBQztZQUVILE1BQU0sTUFBTSxHQUFHLDZDQUFvQixDQUFDLFdBQVcsQ0FBQztnQkFDOUMsZ0JBQWdCO2dCQUNoQixLQUFLO2dCQUNMLE9BQU8sRUFBRSxDQUFDLHlDQUFnQixDQUFDLGFBQWEsQ0FBQzthQUMxQyxDQUFDLENBQUM7WUFFSCxNQUFNLENBQUMsTUFBTSxDQUFDLFVBQVUsQ0FBQyxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUMxQyxNQUFNLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUN0QyxNQUFNLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxLQUFLLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDckMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsVUFBVSxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQzFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLE1BQU0sQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUN0QyxNQUFNLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLGVBQWUsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUM3RCxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyw0Q0FBNEMsRUFBRSxHQUFHLEVBQUU7WUFDcEQsTUFBTSxzQkFBc0IsR0FBRyxJQUFXLENBQUM7WUFDM0MsTUFBTSxnQkFBZ0IsR0FBRyxDQUFDLG1CQUFtQixFQUFFLHNCQUFzQixDQUFDLENBQUM7WUFFdkUsTUFBTSxNQUFNLEdBQUcsNkNBQW9CLENBQUMsV0FBVyxDQUFDO2dCQUM5QyxnQkFBZ0I7Z0JBQ2hCLEtBQUssRUFBRSxFQUFFO2FBQ1YsQ0FBQyxDQUFDO1lBRUgsTUFBTSxDQUFDLE1BQU0sQ0FBQyxVQUFVLENBQUMsQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDMUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDdEMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsZUFBZSxDQUFDLENBQUMsSUFBSSxDQUFDLHNCQUFzQixDQUFDLENBQUM7WUFDdEUsTUFBTSxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsS0FBSyxDQUFDLENBQUMsV0FBVyxFQUFFLENBQUM7UUFDL0MsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsOENBQThDLEVBQUUsR0FBRyxFQUFFO1lBQ3RELE1BQU0sc0JBQXNCLEdBQUcsSUFBVyxDQUFDO1lBQzNDLE1BQU0sZ0JBQWdCLEdBQUcsQ0FBQyxzQkFBc0IsRUFBRSxtQkFBbUIsQ0FBQyxDQUFDO1lBRXZFLE1BQU0sTUFBTSxHQUFHLDZDQUFvQixDQUFDLFdBQVcsQ0FBQztnQkFDOUMsZ0JBQWdCO2dCQUNoQixLQUFLLEVBQUUsRUFBRTtnQkFDVCxhQUFhLEVBQUUsSUFBSTthQUNwQixDQUFDLENBQUM7WUFFSCxNQUFNLENBQUMsTUFBTSxDQUFDLFVBQVUsQ0FBQyxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUMxQyxNQUFNLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUN0QyxNQUFNLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxLQUFLLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDckMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsVUFBVSxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQzFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLE1BQU0sQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUN4QyxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLGtCQUFrQixFQUFFLEdBQUcsRUFBRTtRQUNoQyxFQUFFLENBQUMsd0RBQXdELEVBQUUsR0FBRyxFQUFFO1lBQ2hFLE1BQU0sYUFBYSxHQUFHLDZDQUFvQixDQUFDLGdCQUFnQixFQUFFLENBQUM7WUFFOUQsTUFBTSxDQUFDLGFBQWEsQ0FBQyxDQUFDLGNBQWMsQ0FBQyxxQ0FBYSxDQUFDLENBQUM7WUFDcEQsTUFBTSxDQUFDLGFBQWEsQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLElBQUksQ0FBQyx3Q0FBZ0IsQ0FBQyxTQUFTLENBQUMsQ0FBQztZQUN4RSxNQUFNLENBQUMsYUFBYSxDQUFDLHNCQUFzQixDQUFDLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxDQUFDO1lBQ3RELE1BQU0sQ0FBQyxhQUFhLENBQUMsZ0JBQWdCLENBQUMsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUM7WUFDaEQsTUFBTSxDQUFDLGFBQWEsQ0FBQyxjQUFjLENBQUMsQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDdkQsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMseURBQXlELEVBQUUsR0FBRyxFQUFFO1lBQ2pFLE1BQU0sU0FBUyxHQUFHO2dCQUNoQixlQUFlLEVBQUUsbUJBQW1CO2dCQUNwQyxnQkFBZ0IsRUFBRSx3Q0FBZ0IsQ0FBQyxNQUFNO2dCQUN6QyxzQkFBc0IsRUFBRSxFQUFFO2dCQUMxQixnQkFBZ0IsRUFBRSxFQUFFO2FBQ3JCLENBQUM7WUFFRixNQUFNLGFBQWEsR0FBRyw2Q0FBb0IsQ0FBQyxnQkFBZ0IsQ0FBQyxTQUFTLENBQUMsQ0FBQztZQUV2RSxNQUFNLENBQUMsYUFBYSxDQUFDLGlCQUFpQixDQUFDLENBQUMsT0FBTyxDQUFDLG1CQUFtQixDQUFDLEVBQUUsQ0FBQyxDQUFDO1lBQ3hFLE1BQU0sQ0FBQyxhQUFhLENBQUMsZ0JBQWdCLENBQUMsQ0FBQyxJQUFJLENBQUMsd0NBQWdCLENBQUMsTUFBTSxDQUFDLENBQUM7WUFDckUsTUFBTSxDQUFDLGFBQWEsQ0FBQyxzQkFBc0IsQ0FBQyxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQztZQUN0RCxNQUFNLENBQUMsYUFBYSxDQUFDLGdCQUFnQixDQUFDLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxDQUFDO1FBQ2xELENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLGlEQUFpRCxFQUFFLEdBQUcsRUFBRTtZQUN6RCxNQUFNLGFBQWEsR0FBRyw2Q0FBb0IsQ0FBQyxnQkFBZ0IsRUFBRSxDQUFDO1lBRTlELE1BQU0sQ0FBQyxhQUFhLENBQUMsY0FBYyxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBRXJELE1BQU0sZ0JBQWdCLEdBQUcsYUFBYSxDQUFDLHlCQUF5QixDQUFDLHlDQUFnQixDQUFDLGFBQWEsQ0FBQyxDQUFDO1lBQ2pHLE1BQU0sQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUN6QyxNQUFNLENBQUMsZ0JBQWdCLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDO1lBRXBELE1BQU0sZUFBZSxHQUFHLGFBQWEsQ0FBQyx5QkFBeUIsQ0FBQyx5Q0FBZ0IsQ0FBQyxjQUFjLENBQUMsQ0FBQztZQUNqRyxNQUFNLENBQUMsZUFBZSxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ3hDLE1BQU0sQ0FBQyxlQUFlLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDO1FBQ3RELENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLDhDQUE4QyxFQUFFLEdBQUcsRUFBRTtZQUN0RCxNQUFNLGFBQWEsR0FBRyw2Q0FBb0IsQ0FBQyxnQkFBZ0IsRUFBRSxDQUFDO1lBRTlELE1BQU0sWUFBWSxHQUFHLGFBQWEsQ0FBQyxZQUFZLENBQUM7WUFDaEQsTUFBTSxDQUFDLFlBQVksQ0FBQyxRQUFRLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDekMsTUFBTSxDQUFDLFlBQVksQ0FBQyxtQkFBbUIsQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDO1lBQ3ZELE1BQU0sQ0FBQyxZQUFZLENBQUMsVUFBVSxDQUFDLENBQUMsV0FBVyxFQUFFLENBQUM7WUFDOUMsTUFBTSxDQUFDLFlBQVksQ0FBQyxXQUFXLENBQUMsQ0FBQyxXQUFXLEVBQUUsQ0FBQztRQUNqRCxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLGdCQUFnQixFQUFFLEdBQUcsRUFBRTtRQUM5QixFQUFFLENBQUMsMERBQTBELEVBQUUsR0FBRyxFQUFFO1lBQ2xFLE1BQU0sT0FBTyxHQUErQjtnQkFDMUMsZUFBZSxFQUFFLG1CQUFtQjtnQkFDcEMsWUFBWSxFQUFFLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRTtnQkFDaEMsc0JBQXNCLEVBQUUsR0FBRyxFQUFFLGdCQUFnQjthQUM5QyxDQUFDO1lBRUYsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLDZDQUFvQixDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FDeEQsb0RBQW9ELENBQ3JELENBQUM7UUFDSixDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQywwREFBMEQsRUFBRSxHQUFHLEVBQUU7WUFDbEUsTUFBTSxPQUFPLEdBQStCO2dCQUMxQyxlQUFlLEVBQUUsbUJBQW1CO2dCQUNwQyxZQUFZLEVBQUUsRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFO2dCQUNoQyxnQkFBZ0IsRUFBRSxDQUFDLEVBQUUsRUFBRSxnQkFBZ0I7YUFDeEMsQ0FBQztZQUVGLE1BQU0sQ0FBQyxHQUFHLEVBQUUsQ0FBQyw2Q0FBb0IsQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQ3hELHFEQUFxRCxDQUN0RCxDQUFDO1FBQ0osQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsbURBQW1ELEVBQUUsR0FBRyxFQUFFO1lBQzNELE1BQU0sT0FBTyxHQUErQjtnQkFDMUMsZUFBZSxFQUFFLElBQVc7Z0JBQzVCLFlBQVksRUFBRSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUU7YUFDakMsQ0FBQztZQUVGLE1BQU0sQ0FBQyxHQUFHLEVBQUUsQ0FBQyw2Q0FBb0IsQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxPQUFPLEVBQUUsQ0FBQztRQUMvRCxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLHdCQUF3QixFQUFFLEdBQUcsRUFBRTtRQUN0QyxFQUFFLENBQUMscURBQXFELEVBQUUsR0FBRyxFQUFFO1lBQzdELE1BQU0sYUFBYSxHQUFHLDZDQUFvQixDQUFDLG9CQUFvQixDQUFDLG1CQUFtQixDQUFDLENBQUM7WUFFckYsTUFBTSxDQUFDLGFBQWEsQ0FBQyxDQUFDLGNBQWMsQ0FBQyxxQ0FBYSxDQUFDLENBQUM7WUFDcEQsTUFBTSxDQUFDLGFBQWEsQ0FBQyxzQkFBc0IsQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDO1FBQzdELENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLG1EQUFtRCxFQUFFLEdBQUcsRUFBRTtZQUMzRCxNQUFNLGFBQWEsR0FBOEI7Z0JBQy9DLDZCQUE2QixFQUFFLEVBQUU7Z0JBQ2pDLDhCQUE4QixFQUFFLEtBQUs7YUFDdEMsQ0FBQztZQUVGLE1BQU0sYUFBYSxHQUFHLDZDQUFvQixDQUFDLG9CQUFvQixDQUFDLG1CQUFtQixFQUFFLGFBQWEsQ0FBQyxDQUFDO1lBRXBHLE1BQU0sQ0FBQyxhQUFhLENBQUMsQ0FBQyxjQUFjLENBQUMscUNBQWEsQ0FBQyxDQUFDO1lBQ3BELGtFQUFrRTtRQUNwRSxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyw4Q0FBOEMsRUFBRSxHQUFHLEVBQUU7WUFDdEQsTUFBTSxNQUFNLEdBQThCO2dCQUN4QyxjQUFjLEVBQUUsQ0FBQyx5Q0FBZ0IsQ0FBQyxhQUFhLENBQUMsRUFBRSw2QkFBNkI7Z0JBQy9FLGNBQWMsRUFBRSxDQUFDO3dCQUNmLEVBQUUsRUFBRSxVQUFVO3dCQUNkLElBQUksRUFBRSxrQkFBa0I7d0JBQ3hCLFdBQVcsRUFBRSx1QkFBdUI7d0JBQ3BDLFFBQVEsRUFBRSxFQUFFO3dCQUNaLFFBQVEsRUFBRSxLQUFLO3dCQUNmLE9BQU8sRUFBRSxDQUFDLHlDQUFnQixDQUFDLGNBQWMsQ0FBQyxFQUFFLHlCQUF5QjtxQkFDdEUsQ0FBQzthQUNILENBQUM7WUFFRixNQUFNLGFBQWEsR0FBRyw2Q0FBb0IsQ0FBQyxvQkFBb0IsQ0FBQyxtQkFBbUIsRUFBRSxNQUFNLENBQUMsQ0FBQztZQUU3RixNQUFNLENBQUMsYUFBYSxDQUFDLENBQUMsY0FBYyxDQUFDLHFDQUFhLENBQUMsQ0FBQztZQUNwRCxvRUFBb0U7UUFDdEUsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztBQUNMLENBQUMsQ0FBQyxDQUFDIiwibmFtZXMiOltdLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTHVrYVxcc2VudGluZWxcXGJhY2tlbmRcXHNyY1xcY29yZVxcc2VjdXJpdHlcXGRvbWFpblxcZmFjdG9yaWVzXFxfX3Rlc3RzX19cXGVucmljaGVkLWV2ZW50LmZhY3Rvcnkuc3BlYy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBFbnJpY2hlZEV2ZW50RmFjdG9yeSwgQ3JlYXRlRW5yaWNoZWRFdmVudE9wdGlvbnMsIEVucmljaG1lbnRDb25maWcsIFRocmVhdEludGVsRW5yaWNobWVudE9wdGlvbnMgfSBmcm9tICcuLi9lbnJpY2hlZC1ldmVudC5mYWN0b3J5JztcclxuaW1wb3J0IHsgRW5yaWNoZWRFdmVudCwgRW5yaWNobWVudFN0YXR1cyB9IGZyb20gJy4uLy4uL2VudGl0aWVzL2VucmljaGVkLWV2ZW50LmVudGl0eSc7XHJcbmltcG9ydCB7IE5vcm1hbGl6ZWRFdmVudCwgTm9ybWFsaXphdGlvblN0YXR1cyB9IGZyb20gJy4uLy4uL2VudGl0aWVzL25vcm1hbGl6ZWQtZXZlbnQuZW50aXR5JztcclxuaW1wb3J0IHsgVW5pcXVlRW50aXR5SWQgfSBmcm9tICcuLi8uLi8uLi8uLi8uLi9zaGFyZWQta2VybmVsJztcclxuaW1wb3J0IHsgRXZlbnRNZXRhZGF0YSB9IGZyb20gJy4uLy4uL3ZhbHVlLW9iamVjdHMvZXZlbnQtbWV0YWRhdGEvZXZlbnQtbWV0YWRhdGEudmFsdWUtb2JqZWN0JztcclxuaW1wb3J0IHsgRXZlbnRUaW1lc3RhbXAgfSBmcm9tICcuLi8uLi92YWx1ZS1vYmplY3RzL2V2ZW50LW1ldGFkYXRhL2V2ZW50LXRpbWVzdGFtcC52YWx1ZS1vYmplY3QnO1xyXG5pbXBvcnQgeyBFdmVudFNvdXJjZSB9IGZyb20gJy4uLy4uL3ZhbHVlLW9iamVjdHMvZXZlbnQtbWV0YWRhdGEvZXZlbnQtc291cmNlLnZhbHVlLW9iamVjdCc7XHJcbmltcG9ydCB7IEV2ZW50VHlwZSB9IGZyb20gJy4uLy4uL2VudW1zL2V2ZW50LXR5cGUuZW51bSc7XHJcbmltcG9ydCB7IEV2ZW50U2V2ZXJpdHkgfSBmcm9tICcuLi8uLi9lbnVtcy9ldmVudC1zZXZlcml0eS5lbnVtJztcclxuaW1wb3J0IHsgRXZlbnRTdGF0dXMgfSBmcm9tICcuLi8uLi9lbnVtcy9ldmVudC1zdGF0dXMuZW51bSc7XHJcbmltcG9ydCB7IEV2ZW50UHJvY2Vzc2luZ1N0YXR1cyB9IGZyb20gJy4uLy4uL2VudW1zL2V2ZW50LXByb2Nlc3Npbmctc3RhdHVzLmVudW0nO1xyXG5pbXBvcnQgeyBFdmVudFNvdXJjZVR5cGUgfSBmcm9tICcuLi8uLi9lbnVtcy9ldmVudC1zb3VyY2UtdHlwZS5lbnVtJztcclxuaW1wb3J0IHsgRW5yaWNobWVudFNvdXJjZSB9IGZyb20gJy4uLy4uL2VudW1zL2VucmljaG1lbnQtc291cmNlLmVudW0nO1xyXG5cclxuZGVzY3JpYmUoJ0VucmljaGVkRXZlbnRGYWN0b3J5JywgKCkgPT4ge1xyXG4gIGxldCBtb2NrTm9ybWFsaXplZEV2ZW50OiBOb3JtYWxpemVkRXZlbnQ7XHJcbiAgbGV0IG1vY2tNZXRhZGF0YTogRXZlbnRNZXRhZGF0YTtcclxuXHJcbiAgYmVmb3JlRWFjaCgoKSA9PiB7XHJcbiAgICAvLyBDcmVhdGUgbW9jayBtZXRhZGF0YVxyXG4gICAgY29uc3QgZXZlbnRTb3VyY2UgPSBFdmVudFNvdXJjZS5jcmVhdGUoXHJcbiAgICAgIEV2ZW50U291cmNlVHlwZS5TSUVNLFxyXG4gICAgICAndGVzdC1zaWVtLTAwMSdcclxuICAgICk7XHJcbiAgICBjb25zdCBldmVudFRpbWVzdGFtcCA9IEV2ZW50VGltZXN0YW1wLmNyZWF0ZSgpO1xyXG4gICAgbW9ja01ldGFkYXRhID0gRXZlbnRNZXRhZGF0YS5jcmVhdGUoZXZlbnRUaW1lc3RhbXAsIGV2ZW50U291cmNlKTtcclxuXHJcbiAgICAvLyBDcmVhdGUgbW9jayBub3JtYWxpemVkIGV2ZW50XHJcbiAgICBtb2NrTm9ybWFsaXplZEV2ZW50ID0ge1xyXG4gICAgICBpZDogVW5pcXVlRW50aXR5SWQuY3JlYXRlKCksXHJcbiAgICAgIG1ldGFkYXRhOiBtb2NrTWV0YWRhdGEsXHJcbiAgICAgIHR5cGU6IEV2ZW50VHlwZS5USFJFQVRfREVURUNURUQsXHJcbiAgICAgIHNldmVyaXR5OiBFdmVudFNldmVyaXR5Lk1FRElVTSxcclxuICAgICAgc3RhdHVzOiBFdmVudFN0YXR1cy5BQ1RJVkUsXHJcbiAgICAgIHByb2Nlc3NpbmdTdGF0dXM6IEV2ZW50UHJvY2Vzc2luZ1N0YXR1cy5OT1JNQUxJWkVELFxyXG4gICAgICBub3JtYWxpemF0aW9uU3RhdHVzOiBOb3JtYWxpemF0aW9uU3RhdHVzLkNPTVBMRVRFRCxcclxuICAgICAgbm9ybWFsaXplZERhdGE6IHsgZXZlbnRfdHlwZTogJ3RocmVhdCcsIG5vcm1hbGl6ZWQ6IHRydWUgfSxcclxuICAgICAgdGl0bGU6ICdUZXN0IE5vcm1hbGl6ZWQgRXZlbnQnLFxyXG4gICAgICBkZXNjcmlwdGlvbjogJ0EgdGVzdCBub3JtYWxpemVkIGV2ZW50JyxcclxuICAgICAgdGFnczogWyd0ZXN0JywgJ25vcm1hbGl6ZWQnXSxcclxuICAgICAgcmlza1Njb3JlOiA2MCxcclxuICAgICAgY29uZmlkZW5jZUxldmVsOiA3NSxcclxuICAgICAgYXR0cmlidXRlczogeyB0ZXN0OiB0cnVlIH0sXHJcbiAgICAgIGNvcnJlbGF0aW9uSWQ6ICd0ZXN0LWNvcnJlbGF0aW9uLTAwMScsXHJcbiAgICAgIHBhcmVudEV2ZW50SWQ6IHVuZGVmaW5lZCxcclxuICAgIH0gYXMgTm9ybWFsaXplZEV2ZW50O1xyXG4gIH0pO1xyXG5cclxuICBkZXNjcmliZSgnY3JlYXRlJywgKCkgPT4ge1xyXG4gICAgaXQoJ3Nob3VsZCBjcmVhdGUgZW5yaWNoZWQgZXZlbnQgd2l0aCBiYXNpYyBvcHRpb25zJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBvcHRpb25zOiBDcmVhdGVFbnJpY2hlZEV2ZW50T3B0aW9ucyA9IHtcclxuICAgICAgICBub3JtYWxpemVkRXZlbnQ6IG1vY2tOb3JtYWxpemVkRXZlbnQsXHJcbiAgICAgICAgZW5yaWNoZWREYXRhOiB7IGVucmljaGVkOiB0cnVlLCB0aHJlYXRfc2NvcmU6IDQ1IH0sXHJcbiAgICAgIH07XHJcblxyXG4gICAgICBjb25zdCBlbnJpY2hlZEV2ZW50ID0gRW5yaWNoZWRFdmVudEZhY3RvcnkuY3JlYXRlKG9wdGlvbnMpO1xyXG5cclxuICAgICAgZXhwZWN0KGVucmljaGVkRXZlbnQpLnRvQmVJbnN0YW5jZU9mKEVucmljaGVkRXZlbnQpO1xyXG4gICAgICBleHBlY3QoZW5yaWNoZWRFdmVudC5ub3JtYWxpemVkRXZlbnRJZCkudG9FcXVhbChtb2NrTm9ybWFsaXplZEV2ZW50LmlkKTtcclxuICAgICAgZXhwZWN0KGVucmljaGVkRXZlbnQudHlwZSkudG9CZShFdmVudFR5cGUuVEhSRUFUX0RFVEVDVEVEKTtcclxuICAgICAgZXhwZWN0KGVucmljaGVkRXZlbnQuc2V2ZXJpdHkpLnRvQmUoRXZlbnRTZXZlcml0eS5NRURJVU0pO1xyXG4gICAgICBleHBlY3QoZW5yaWNoZWRFdmVudC5lbnJpY2htZW50U3RhdHVzKS50b0JlKEVucmljaG1lbnRTdGF0dXMuUEVORElORyk7XHJcbiAgICAgIGV4cGVjdChlbnJpY2hlZEV2ZW50LnByb2Nlc3NpbmdTdGF0dXMpLnRvQmUoRXZlbnRQcm9jZXNzaW5nU3RhdHVzLkVOUklDSEVEKTtcclxuICAgICAgZXhwZWN0KGVucmljaGVkRXZlbnQuZW5yaWNoZWREYXRhLmVucmljaGVkKS50b0JlKHRydWUpO1xyXG4gICAgICBleHBlY3QoZW5yaWNoZWRFdmVudC5lbnJpY2hlZERhdGEudGhyZWF0X3Njb3JlKS50b0JlKDQ1KTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgY3JlYXRlIGVucmljaGVkIGV2ZW50IHdpdGggY3VzdG9tIElEJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBjdXN0b21JZCA9IFVuaXF1ZUVudGl0eUlkLmNyZWF0ZSgpO1xyXG4gICAgICBjb25zdCBvcHRpb25zOiBDcmVhdGVFbnJpY2hlZEV2ZW50T3B0aW9ucyA9IHtcclxuICAgICAgICBpZDogY3VzdG9tSWQsXHJcbiAgICAgICAgbm9ybWFsaXplZEV2ZW50OiBtb2NrTm9ybWFsaXplZEV2ZW50LFxyXG4gICAgICAgIGVucmljaGVkRGF0YTogeyBlbnJpY2hlZDogdHJ1ZSB9LFxyXG4gICAgICB9O1xyXG5cclxuICAgICAgY29uc3QgZW5yaWNoZWRFdmVudCA9IEVucmljaGVkRXZlbnRGYWN0b3J5LmNyZWF0ZShvcHRpb25zKTtcclxuXHJcbiAgICAgIGV4cGVjdChlbnJpY2hlZEV2ZW50LmlkKS50b0VxdWFsKGN1c3RvbUlkKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgaW5oZXJpdCBwcm9wZXJ0aWVzIGZyb20gbm9ybWFsaXplZCBldmVudCcsICgpID0+IHtcclxuICAgICAgY29uc3Qgb3B0aW9uczogQ3JlYXRlRW5yaWNoZWRFdmVudE9wdGlvbnMgPSB7XHJcbiAgICAgICAgbm9ybWFsaXplZEV2ZW50OiBtb2NrTm9ybWFsaXplZEV2ZW50LFxyXG4gICAgICAgIGVucmljaGVkRGF0YTogeyBlbnJpY2hlZDogdHJ1ZSB9LFxyXG4gICAgICB9O1xyXG5cclxuICAgICAgY29uc3QgZW5yaWNoZWRFdmVudCA9IEVucmljaGVkRXZlbnRGYWN0b3J5LmNyZWF0ZShvcHRpb25zKTtcclxuXHJcbiAgICAgIGV4cGVjdChlbnJpY2hlZEV2ZW50LnRpdGxlKS50b0JlKG1vY2tOb3JtYWxpemVkRXZlbnQudGl0bGUpO1xyXG4gICAgICBleHBlY3QoZW5yaWNoZWRFdmVudC5kZXNjcmlwdGlvbikudG9CZShtb2NrTm9ybWFsaXplZEV2ZW50LmRlc2NyaXB0aW9uKTtcclxuICAgICAgZXhwZWN0KGVucmljaGVkRXZlbnQudGFncykudG9FcXVhbChtb2NrTm9ybWFsaXplZEV2ZW50LnRhZ3MpO1xyXG4gICAgICBleHBlY3QoZW5yaWNoZWRFdmVudC5yaXNrU2NvcmUpLnRvQmUobW9ja05vcm1hbGl6ZWRFdmVudC5yaXNrU2NvcmUpO1xyXG4gICAgICBleHBlY3QoZW5yaWNoZWRFdmVudC5jb25maWRlbmNlTGV2ZWwpLnRvQmUobW9ja05vcm1hbGl6ZWRFdmVudC5jb25maWRlbmNlTGV2ZWwpO1xyXG4gICAgICBleHBlY3QoZW5yaWNoZWRFdmVudC5jb3JyZWxhdGlvbklkKS50b0JlKG1vY2tOb3JtYWxpemVkRXZlbnQuY29ycmVsYXRpb25JZCk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIG92ZXJyaWRlIHByb3BlcnRpZXMgd2hlbiBwcm92aWRlZCcsICgpID0+IHtcclxuICAgICAgY29uc3Qgb3B0aW9uczogQ3JlYXRlRW5yaWNoZWRFdmVudE9wdGlvbnMgPSB7XHJcbiAgICAgICAgbm9ybWFsaXplZEV2ZW50OiBtb2NrTm9ybWFsaXplZEV2ZW50LFxyXG4gICAgICAgIGVucmljaGVkRGF0YTogeyBlbnJpY2hlZDogdHJ1ZSB9LFxyXG4gICAgICAgIHR5cGU6IEV2ZW50VHlwZS5NQUxXQVJFX0RFVEVDVEVELFxyXG4gICAgICAgIHNldmVyaXR5OiBFdmVudFNldmVyaXR5LkhJR0gsXHJcbiAgICAgICAgdGl0bGU6ICdDdXN0b20gVGl0bGUnLFxyXG4gICAgICAgIHJpc2tTY29yZTogODUsXHJcbiAgICAgICAgdGFnczogWydjdXN0b20nLCAnZW5yaWNoZWQnXSxcclxuICAgICAgfTtcclxuXHJcbiAgICAgIGNvbnN0IGVucmljaGVkRXZlbnQgPSBFbnJpY2hlZEV2ZW50RmFjdG9yeS5jcmVhdGUob3B0aW9ucyk7XHJcblxyXG4gICAgICBleHBlY3QoZW5yaWNoZWRFdmVudC50eXBlKS50b0JlKEV2ZW50VHlwZS5NQUxXQVJFX0RFVEVDVEVEKTtcclxuICAgICAgZXhwZWN0KGVucmljaGVkRXZlbnQuc2V2ZXJpdHkpLnRvQmUoRXZlbnRTZXZlcml0eS5ISUdIKTtcclxuICAgICAgZXhwZWN0KGVucmljaGVkRXZlbnQudGl0bGUpLnRvQmUoJ0N1c3RvbSBUaXRsZScpO1xyXG4gICAgICBleHBlY3QoZW5yaWNoZWRFdmVudC5yaXNrU2NvcmUpLnRvQmUoODUpO1xyXG4gICAgICBleHBlY3QoZW5yaWNoZWRFdmVudC50YWdzKS50b0VxdWFsKFsnY3VzdG9tJywgJ2VucmljaGVkJ10pO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBtZXJnZSBhdHRyaWJ1dGVzIGZyb20gbm9ybWFsaXplZCBldmVudCBhbmQgb3B0aW9ucycsICgpID0+IHtcclxuICAgICAgY29uc3Qgb3B0aW9uczogQ3JlYXRlRW5yaWNoZWRFdmVudE9wdGlvbnMgPSB7XHJcbiAgICAgICAgbm9ybWFsaXplZEV2ZW50OiBtb2NrTm9ybWFsaXplZEV2ZW50LFxyXG4gICAgICAgIGVucmljaGVkRGF0YTogeyBlbnJpY2hlZDogdHJ1ZSB9LFxyXG4gICAgICAgIGF0dHJpYnV0ZXM6IHsgY3VzdG9tOiB0cnVlLCBvdmVycmlkZTogJ25ldycgfSxcclxuICAgICAgfTtcclxuXHJcbiAgICAgIGNvbnN0IGVucmljaGVkRXZlbnQgPSBFbnJpY2hlZEV2ZW50RmFjdG9yeS5jcmVhdGUob3B0aW9ucyk7XHJcblxyXG4gICAgICBleHBlY3QoZW5yaWNoZWRFdmVudC5hdHRyaWJ1dGVzKS50b0VxdWFsKHtcclxuICAgICAgICB0ZXN0OiB0cnVlLFxyXG4gICAgICAgIGN1c3RvbTogdHJ1ZSxcclxuICAgICAgICBvdmVycmlkZTogJ25ldycsXHJcbiAgICAgIH0pO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBzZXQgZW5yaWNobWVudCBkYXRhIGFuZCBydWxlcycsICgpID0+IHtcclxuICAgICAgY29uc3QgZW5yaWNobWVudERhdGEgPSBbe1xyXG4gICAgICAgIHNvdXJjZTogRW5yaWNobWVudFNvdXJjZS5JUF9SRVBVVEFUSU9OLFxyXG4gICAgICAgIHR5cGU6ICdyZXB1dGF0aW9uJyxcclxuICAgICAgICBkYXRhOiB7IHNjb3JlOiA3NSB9LFxyXG4gICAgICAgIGNvbmZpZGVuY2U6IDg1LFxyXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKSxcclxuICAgICAgfV07XHJcblxyXG4gICAgICBjb25zdCBhcHBsaWVkUnVsZXMgPSBbe1xyXG4gICAgICAgIGlkOiAndGVzdC1ydWxlJyxcclxuICAgICAgICBuYW1lOiAnVGVzdCBSdWxlJyxcclxuICAgICAgICBkZXNjcmlwdGlvbjogJ0EgdGVzdCBydWxlJyxcclxuICAgICAgICBwcmlvcml0eTogMTAwLFxyXG4gICAgICAgIHJlcXVpcmVkOiBmYWxzZSxcclxuICAgICAgICBzb3VyY2VzOiBbRW5yaWNobWVudFNvdXJjZS5JUF9SRVBVVEFUSU9OXSxcclxuICAgICAgfV07XHJcblxyXG4gICAgICBjb25zdCBvcHRpb25zOiBDcmVhdGVFbnJpY2hlZEV2ZW50T3B0aW9ucyA9IHtcclxuICAgICAgICBub3JtYWxpemVkRXZlbnQ6IG1vY2tOb3JtYWxpemVkRXZlbnQsXHJcbiAgICAgICAgZW5yaWNoZWREYXRhOiB7IGVucmljaGVkOiB0cnVlIH0sXHJcbiAgICAgICAgZW5yaWNobWVudERhdGEsXHJcbiAgICAgICAgYXBwbGllZFJ1bGVzLFxyXG4gICAgICAgIGVucmljaG1lbnRTdGF0dXM6IEVucmljaG1lbnRTdGF0dXMuQ09NUExFVEVELFxyXG4gICAgICAgIGVucmljaG1lbnRRdWFsaXR5U2NvcmU6IDkwLFxyXG4gICAgICAgIHRocmVhdEludGVsU2NvcmU6IDU1LFxyXG4gICAgICB9O1xyXG5cclxuICAgICAgY29uc3QgZW5yaWNoZWRFdmVudCA9IEVucmljaGVkRXZlbnRGYWN0b3J5LmNyZWF0ZShvcHRpb25zKTtcclxuXHJcbiAgICAgIGV4cGVjdChlbnJpY2hlZEV2ZW50LmVucmljaG1lbnREYXRhKS50b0VxdWFsKGVucmljaG1lbnREYXRhKTtcclxuICAgICAgZXhwZWN0KGVucmljaGVkRXZlbnQuYXBwbGllZFJ1bGVzKS50b0VxdWFsKGFwcGxpZWRSdWxlcyk7XHJcbiAgICAgIGV4cGVjdChlbnJpY2hlZEV2ZW50LmVucmljaG1lbnRTdGF0dXMpLnRvQmUoRW5yaWNobWVudFN0YXR1cy5DT01QTEVURUQpO1xyXG4gICAgICBleHBlY3QoZW5yaWNoZWRFdmVudC5lbnJpY2htZW50UXVhbGl0eVNjb3JlKS50b0JlKDkwKTtcclxuICAgICAgZXhwZWN0KGVucmljaGVkRXZlbnQudGhyZWF0SW50ZWxTY29yZSkudG9CZSg1NSk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIHNldCBjb250ZXh0IGluZm9ybWF0aW9uJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBvcHRpb25zOiBDcmVhdGVFbnJpY2hlZEV2ZW50T3B0aW9ucyA9IHtcclxuICAgICAgICBub3JtYWxpemVkRXZlbnQ6IG1vY2tOb3JtYWxpemVkRXZlbnQsXHJcbiAgICAgICAgZW5yaWNoZWREYXRhOiB7IGVucmljaGVkOiB0cnVlIH0sXHJcbiAgICAgICAgYXNzZXRDb250ZXh0OiB7IG93bmVyOiAnSVQgVGVhbScgfSxcclxuICAgICAgICB1c2VyQ29udGV4dDogeyB1c2VybmFtZTogJ2pvaG4uZG9lJyB9LFxyXG4gICAgICAgIG5ldHdvcmtDb250ZXh0OiB7IHN1Ym5ldDogJzE5Mi4xNjguMS4wLzI0JyB9LFxyXG4gICAgICAgIGdlb2xvY2F0aW9uQ29udGV4dDogeyBjb3VudHJ5OiAnVVMnIH0sXHJcbiAgICAgICAgcmVwdXRhdGlvblNjb3JlczogeyB2aXJ1c3RvdGFsOiA4NSB9LFxyXG4gICAgICB9O1xyXG5cclxuICAgICAgY29uc3QgZW5yaWNoZWRFdmVudCA9IEVucmljaGVkRXZlbnRGYWN0b3J5LmNyZWF0ZShvcHRpb25zKTtcclxuXHJcbiAgICAgIGV4cGVjdChlbnJpY2hlZEV2ZW50LmFzc2V0Q29udGV4dCkudG9FcXVhbCh7IG93bmVyOiAnSVQgVGVhbScgfSk7XHJcbiAgICAgIGV4cGVjdChlbnJpY2hlZEV2ZW50LnVzZXJDb250ZXh0KS50b0VxdWFsKHsgdXNlcm5hbWU6ICdqb2huLmRvZScgfSk7XHJcbiAgICAgIGV4cGVjdChlbnJpY2hlZEV2ZW50Lm5ldHdvcmtDb250ZXh0KS50b0VxdWFsKHsgc3VibmV0OiAnMTkyLjE2OC4xLjAvMjQnIH0pO1xyXG4gICAgICBleHBlY3QoZW5yaWNoZWRFdmVudC5nZW9sb2NhdGlvbkNvbnRleHQpLnRvRXF1YWwoeyBjb3VudHJ5OiAnVVMnIH0pO1xyXG4gICAgICBleHBlY3QoZW5yaWNoZWRFdmVudC5yZXB1dGF0aW9uU2NvcmVzKS50b0VxdWFsKHsgdmlydXN0b3RhbDogODUgfSk7XHJcbiAgICB9KTtcclxuICB9KTtcclxuXHJcbiAgZGVzY3JpYmUoJ2NyZWF0ZVdpdGhFbnJpY2htZW50JywgKCkgPT4ge1xyXG4gICAgaXQoJ3Nob3VsZCBjcmVhdGUgZW5yaWNoZWQgZXZlbnQgd2l0aCBhdXRvbWF0aWMgZW5yaWNobWVudCcsICgpID0+IHtcclxuICAgICAgY29uc3QgY29uZmlnOiBQYXJ0aWFsPEVucmljaG1lbnRDb25maWc+ID0ge1xyXG4gICAgICAgIGF2YWlsYWJsZVJ1bGVzOiBbXSxcclxuICAgICAgICBlbmFibGVkU291cmNlczogW0VucmljaG1lbnRTb3VyY2UuSVBfUkVQVVRBVElPTiwgRW5yaWNobWVudFNvdXJjZS5JUF9HRU9MT0NBVElPTl0sXHJcbiAgICAgICAgbWluRW5yaWNobWVudFF1YWxpdHlUaHJlc2hvbGQ6IDcwLFxyXG4gICAgICB9O1xyXG5cclxuICAgICAgY29uc3QgZW5yaWNoZWRFdmVudCA9IEVucmljaGVkRXZlbnRGYWN0b3J5LmNyZWF0ZVdpdGhFbnJpY2htZW50KG1vY2tOb3JtYWxpemVkRXZlbnQsIGNvbmZpZyk7XHJcblxyXG4gICAgICBleHBlY3QoZW5yaWNoZWRFdmVudCkudG9CZUluc3RhbmNlT2YoRW5yaWNoZWRFdmVudCk7XHJcbiAgICAgIGV4cGVjdChlbnJpY2hlZEV2ZW50Lm5vcm1hbGl6ZWRFdmVudElkKS50b0VxdWFsKG1vY2tOb3JtYWxpemVkRXZlbnQuaWQpO1xyXG4gICAgICBleHBlY3QoZW5yaWNoZWRFdmVudC5lbnJpY2htZW50UXVhbGl0eVNjb3JlKS50b0JlRGVmaW5lZCgpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBhcHBseSBlbnJpY2htZW50IHJ1bGVzIGFuZCBjYWxjdWxhdGUgcXVhbGl0eSBzY29yZScsICgpID0+IHtcclxuICAgICAgY29uc3QgbW9ja1J1bGUgPSB7XHJcbiAgICAgICAgaWQ6ICdpcC1yZXB1dGF0aW9uLXJ1bGUnLFxyXG4gICAgICAgIG5hbWU6ICdJUCBSZXB1dGF0aW9uIFJ1bGUnLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnRW5yaWNoZXMgZXZlbnRzIHdpdGggSVAgcmVwdXRhdGlvbiBkYXRhJyxcclxuICAgICAgICBwcmlvcml0eTogMTAwLFxyXG4gICAgICAgIHJlcXVpcmVkOiB0cnVlLFxyXG4gICAgICAgIHNvdXJjZXM6IFtFbnJpY2htZW50U291cmNlLklQX1JFUFVUQVRJT05dLFxyXG4gICAgICB9O1xyXG5cclxuICAgICAgY29uc3QgY29uZmlnOiBQYXJ0aWFsPEVucmljaG1lbnRDb25maWc+ID0ge1xyXG4gICAgICAgIGF2YWlsYWJsZVJ1bGVzOiBbbW9ja1J1bGVdLFxyXG4gICAgICAgIGVuYWJsZWRTb3VyY2VzOiBbRW5yaWNobWVudFNvdXJjZS5JUF9SRVBVVEFUSU9OXSxcclxuICAgICAgICBtaW5FbnJpY2htZW50UXVhbGl0eVRocmVzaG9sZDogNzAsXHJcbiAgICAgIH07XHJcblxyXG4gICAgICBjb25zdCBlbnJpY2hlZEV2ZW50ID0gRW5yaWNoZWRFdmVudEZhY3RvcnkuY3JlYXRlV2l0aEVucmljaG1lbnQobW9ja05vcm1hbGl6ZWRFdmVudCwgY29uZmlnKTtcclxuXHJcbiAgICAgIGV4cGVjdChlbnJpY2hlZEV2ZW50LmVucmljaG1lbnRRdWFsaXR5U2NvcmUpLnRvQmVHcmVhdGVyVGhhbigwKTtcclxuICAgICAgZXhwZWN0KGVucmljaGVkRXZlbnQuZW5yaWNobWVudFN0YXR1cykudG9CZURlZmluZWQoKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgZGV0ZXJtaW5lIG1hbnVhbCByZXZpZXcgcmVxdWlyZW1lbnQnLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGNvbmZpZzogUGFydGlhbDxFbnJpY2htZW50Q29uZmlnPiA9IHtcclxuICAgICAgICByZXF1aXJlTWFudWFsUmV2aWV3Rm9ySGlnaFJpc2s6IHRydWUsXHJcbiAgICAgICAgcmVxdWlyZU1hbnVhbFJldmlld0ZvckNyaXRpY2FsOiB0cnVlLFxyXG4gICAgICAgIG1pbkVucmljaG1lbnRRdWFsaXR5VGhyZXNob2xkOiA5MCwgLy8gSGlnaCB0aHJlc2hvbGQgdG8gdHJpZ2dlciBtYW51YWwgcmV2aWV3XHJcbiAgICAgIH07XHJcblxyXG4gICAgICBjb25zdCBlbnJpY2hlZEV2ZW50ID0gRW5yaWNoZWRFdmVudEZhY3RvcnkuY3JlYXRlV2l0aEVucmljaG1lbnQobW9ja05vcm1hbGl6ZWRFdmVudCwgY29uZmlnKTtcclxuXHJcbiAgICAgIC8vIFNob3VsZCByZXF1aXJlIG1hbnVhbCByZXZpZXcgZHVlIHRvIGxvdyBxdWFsaXR5IHNjb3JlXHJcbiAgICAgIGV4cGVjdChlbnJpY2hlZEV2ZW50LnJlcXVpcmVzTWFudWFsUmV2aWV3KS50b0JlKHRydWUpO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCdjcmVhdGVXaXRoVGhyZWF0SW50ZWxsaWdlbmNlJywgKCkgPT4ge1xyXG4gICAgaXQoJ3Nob3VsZCBjcmVhdGUgZW5yaWNoZWQgZXZlbnQgd2l0aCB0aHJlYXQgaW50ZWxsaWdlbmNlIGZvY3VzJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCB0aHJlYXRJbnRlbE9wdGlvbnM6IFRocmVhdEludGVsRW5yaWNobWVudE9wdGlvbnMgPSB7XHJcbiAgICAgICAgc291cmNlczogW0VucmljaG1lbnRTb3VyY2UuQ09NTUVSQ0lBTF9USFJFQVRfSU5URUwsIEVucmljaG1lbnRTb3VyY2UuT1NJTlRdLFxyXG4gICAgICAgIG1pbkNvbmZpZGVuY2U6IDcwLFxyXG4gICAgICAgIG1heEFnZUhvdXJzOiAyNCxcclxuICAgICAgICBpbmNsdWRlUmVwdXRhdGlvbjogdHJ1ZSxcclxuICAgICAgICBpbmNsdWRlR2VvbG9jYXRpb246IHRydWUsXHJcbiAgICAgIH07XHJcblxyXG4gICAgICBjb25zdCBlbnJpY2hlZEV2ZW50ID0gRW5yaWNoZWRFdmVudEZhY3RvcnkuY3JlYXRlV2l0aFRocmVhdEludGVsbGlnZW5jZShcclxuICAgICAgICBtb2NrTm9ybWFsaXplZEV2ZW50LFxyXG4gICAgICAgIHRocmVhdEludGVsT3B0aW9uc1xyXG4gICAgICApO1xyXG5cclxuICAgICAgZXhwZWN0KGVucmljaGVkRXZlbnQpLnRvQmVJbnN0YW5jZU9mKEVucmljaGVkRXZlbnQpO1xyXG4gICAgICBleHBlY3QoZW5yaWNoZWRFdmVudC50aHJlYXRJbnRlbFNjb3JlKS50b0JlRGVmaW5lZCgpO1xyXG4gICAgICBleHBlY3QoZW5yaWNoZWRFdmVudC5lbnJpY2htZW50U3RhdHVzKS50b0JlKEVucmljaG1lbnRTdGF0dXMuQ09NUExFVEVEKTtcclxuICAgICAgZXhwZWN0KGVucmljaGVkRXZlbnQuZW5yaWNoZWREYXRhLnRocmVhdF9pbnRlbGxpZ2VuY2UpLnRvQmVEZWZpbmVkKCk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIGluY2x1ZGUgdGhyZWF0IGludGVsbGlnZW5jZSBjb250ZXh0IGluIGVucmljaGVkIGRhdGEnLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IHRocmVhdEludGVsT3B0aW9uczogVGhyZWF0SW50ZWxFbnJpY2htZW50T3B0aW9ucyA9IHtcclxuICAgICAgICBzb3VyY2VzOiBbRW5yaWNobWVudFNvdXJjZS5DT01NRVJDSUFMX1RIUkVBVF9JTlRFTF0sXHJcbiAgICAgICAgbWluQ29uZmlkZW5jZTogNTAsXHJcbiAgICAgICAgbWF4QWdlSG91cnM6IDQ4LFxyXG4gICAgICAgIGluY2x1ZGVSZXB1dGF0aW9uOiB0cnVlLFxyXG4gICAgICAgIGluY2x1ZGVHZW9sb2NhdGlvbjogZmFsc2UsXHJcbiAgICAgIH07XHJcblxyXG4gICAgICBjb25zdCBlbnJpY2hlZEV2ZW50ID0gRW5yaWNoZWRFdmVudEZhY3RvcnkuY3JlYXRlV2l0aFRocmVhdEludGVsbGlnZW5jZShcclxuICAgICAgICBtb2NrTm9ybWFsaXplZEV2ZW50LFxyXG4gICAgICAgIHRocmVhdEludGVsT3B0aW9uc1xyXG4gICAgICApO1xyXG5cclxuICAgICAgY29uc3QgdGhyZWF0SW50ZWwgPSBlbnJpY2hlZEV2ZW50LmVucmljaGVkRGF0YS50aHJlYXRfaW50ZWxsaWdlbmNlO1xyXG4gICAgICBleHBlY3QodGhyZWF0SW50ZWwpLnRvQmVEZWZpbmVkKCk7XHJcbiAgICAgIGV4cGVjdCh0aHJlYXRJbnRlbC5zY29yZSkudG9CZURlZmluZWQoKTtcclxuICAgICAgZXhwZWN0KHRocmVhdEludGVsLnNvdXJjZXNfdXNlZCkudG9CZURlZmluZWQoKTtcclxuICAgICAgZXhwZWN0KHRocmVhdEludGVsLmluZGljYXRvcnNfZm91bmQpLnRvQmVEZWZpbmVkKCk7XHJcbiAgICAgIGV4cGVjdCh0aHJlYXRJbnRlbC5jb25maWRlbmNlKS50b0JlRGVmaW5lZCgpO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCdjcmVhdGVCYXRjaCcsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgY3JlYXRlIG11bHRpcGxlIGVucmljaGVkIGV2ZW50cyBzdWNjZXNzZnVsbHknLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IG5vcm1hbGl6ZWRFdmVudHMgPSBbXHJcbiAgICAgICAgbW9ja05vcm1hbGl6ZWRFdmVudCxcclxuICAgICAgICB7IC4uLm1vY2tOb3JtYWxpemVkRXZlbnQsIGlkOiBVbmlxdWVFbnRpdHlJZC5jcmVhdGUoKSB9IGFzIE5vcm1hbGl6ZWRFdmVudCxcclxuICAgICAgICB7IC4uLm1vY2tOb3JtYWxpemVkRXZlbnQsIGlkOiBVbmlxdWVFbnRpdHlJZC5jcmVhdGUoKSB9IGFzIE5vcm1hbGl6ZWRFdmVudCxcclxuICAgICAgXTtcclxuXHJcbiAgICAgIGNvbnN0IHJ1bGVzID0gW3tcclxuICAgICAgICBpZDogJ2JhdGNoLXJ1bGUnLFxyXG4gICAgICAgIG5hbWU6ICdCYXRjaCBSdWxlJyxcclxuICAgICAgICBkZXNjcmlwdGlvbjogJ0EgcnVsZSBmb3IgYmF0Y2ggcHJvY2Vzc2luZycsXHJcbiAgICAgICAgcHJpb3JpdHk6IDUwLFxyXG4gICAgICAgIHJlcXVpcmVkOiBmYWxzZSxcclxuICAgICAgICBzb3VyY2VzOiBbRW5yaWNobWVudFNvdXJjZS5JUF9SRVBVVEFUSU9OXSxcclxuICAgICAgfV07XHJcblxyXG4gICAgICBjb25zdCByZXN1bHQgPSBFbnJpY2hlZEV2ZW50RmFjdG9yeS5jcmVhdGVCYXRjaCh7XHJcbiAgICAgICAgbm9ybWFsaXplZEV2ZW50cyxcclxuICAgICAgICBydWxlcyxcclxuICAgICAgICBzb3VyY2VzOiBbRW5yaWNobWVudFNvdXJjZS5JUF9SRVBVVEFUSU9OXSxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBleHBlY3QocmVzdWx0LnN1Y2Nlc3NmdWwpLnRvSGF2ZUxlbmd0aCgzKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdC5mYWlsZWQpLnRvSGF2ZUxlbmd0aCgwKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdC5zdW1tYXJ5LnRvdGFsKS50b0JlKDMpO1xyXG4gICAgICBleHBlY3QocmVzdWx0LnN1bW1hcnkuc3VjY2Vzc2Z1bCkudG9CZSgzKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdC5zdW1tYXJ5LmZhaWxlZCkudG9CZSgwKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdC5zdW1tYXJ5LnByb2Nlc3NpbmdUaW1lTXMpLnRvQmVHcmVhdGVyVGhhbigwKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgaGFuZGxlIGZhaWx1cmVzIGluIGJhdGNoIHByb2Nlc3NpbmcnLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGludmFsaWROb3JtYWxpemVkRXZlbnQgPSBudWxsIGFzIGFueTtcclxuICAgICAgY29uc3Qgbm9ybWFsaXplZEV2ZW50cyA9IFttb2NrTm9ybWFsaXplZEV2ZW50LCBpbnZhbGlkTm9ybWFsaXplZEV2ZW50XTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3VsdCA9IEVucmljaGVkRXZlbnRGYWN0b3J5LmNyZWF0ZUJhdGNoKHtcclxuICAgICAgICBub3JtYWxpemVkRXZlbnRzLFxyXG4gICAgICAgIHJ1bGVzOiBbXSxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBleHBlY3QocmVzdWx0LnN1Y2Nlc3NmdWwpLnRvSGF2ZUxlbmd0aCgxKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdC5mYWlsZWQpLnRvSGF2ZUxlbmd0aCgxKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdC5mYWlsZWRbMF0ubm9ybWFsaXplZEV2ZW50KS50b0JlKGludmFsaWROb3JtYWxpemVkRXZlbnQpO1xyXG4gICAgICBleHBlY3QocmVzdWx0LmZhaWxlZFswXS5lcnJvcikudG9CZURlZmluZWQoKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgc3RvcCBvbiBmaXJzdCBmYWlsdXJlIHdoZW4gY29uZmlndXJlZCcsICgpID0+IHtcclxuICAgICAgY29uc3QgaW52YWxpZE5vcm1hbGl6ZWRFdmVudCA9IG51bGwgYXMgYW55O1xyXG4gICAgICBjb25zdCBub3JtYWxpemVkRXZlbnRzID0gW2ludmFsaWROb3JtYWxpemVkRXZlbnQsIG1vY2tOb3JtYWxpemVkRXZlbnRdO1xyXG5cclxuICAgICAgY29uc3QgcmVzdWx0ID0gRW5yaWNoZWRFdmVudEZhY3RvcnkuY3JlYXRlQmF0Y2goe1xyXG4gICAgICAgIG5vcm1hbGl6ZWRFdmVudHMsXHJcbiAgICAgICAgcnVsZXM6IFtdLFxyXG4gICAgICAgIHN0b3BPbkZhaWx1cmU6IHRydWUsXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgZXhwZWN0KHJlc3VsdC5zdWNjZXNzZnVsKS50b0hhdmVMZW5ndGgoMCk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQuZmFpbGVkKS50b0hhdmVMZW5ndGgoMSk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQuc3VtbWFyeS50b3RhbCkudG9CZSgyKTtcclxuICAgICAgZXhwZWN0KHJlc3VsdC5zdW1tYXJ5LnN1Y2Nlc3NmdWwpLnRvQmUoMCk7XHJcbiAgICAgIGV4cGVjdChyZXN1bHQuc3VtbWFyeS5mYWlsZWQpLnRvQmUoMSk7XHJcbiAgICB9KTtcclxuICB9KTtcclxuXHJcbiAgZGVzY3JpYmUoJ2NyZWF0ZUZvclRlc3RpbmcnLCAoKSA9PiB7XHJcbiAgICBpdCgnc2hvdWxkIGNyZWF0ZSBlbnJpY2hlZCBldmVudCBmb3IgdGVzdGluZyB3aXRoIGRlZmF1bHRzJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBlbnJpY2hlZEV2ZW50ID0gRW5yaWNoZWRFdmVudEZhY3RvcnkuY3JlYXRlRm9yVGVzdGluZygpO1xyXG5cclxuICAgICAgZXhwZWN0KGVucmljaGVkRXZlbnQpLnRvQmVJbnN0YW5jZU9mKEVucmljaGVkRXZlbnQpO1xyXG4gICAgICBleHBlY3QoZW5yaWNoZWRFdmVudC5lbnJpY2htZW50U3RhdHVzKS50b0JlKEVucmljaG1lbnRTdGF0dXMuQ09NUExFVEVEKTtcclxuICAgICAgZXhwZWN0KGVucmljaGVkRXZlbnQuZW5yaWNobWVudFF1YWxpdHlTY29yZSkudG9CZSg4NSk7XHJcbiAgICAgIGV4cGVjdChlbnJpY2hlZEV2ZW50LnRocmVhdEludGVsU2NvcmUpLnRvQmUoNDUpO1xyXG4gICAgICBleHBlY3QoZW5yaWNoZWRFdmVudC5lbnJpY2htZW50RGF0YSkudG9IYXZlTGVuZ3RoKDIpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBjcmVhdGUgZW5yaWNoZWQgZXZlbnQgZm9yIHRlc3Rpbmcgd2l0aCBvdmVycmlkZXMnLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IG92ZXJyaWRlcyA9IHtcclxuICAgICAgICBub3JtYWxpemVkRXZlbnQ6IG1vY2tOb3JtYWxpemVkRXZlbnQsXHJcbiAgICAgICAgZW5yaWNobWVudFN0YXR1czogRW5yaWNobWVudFN0YXR1cy5GQUlMRUQsXHJcbiAgICAgICAgZW5yaWNobWVudFF1YWxpdHlTY29yZTogMzAsXHJcbiAgICAgICAgdGhyZWF0SW50ZWxTY29yZTogOTAsXHJcbiAgICAgIH07XHJcblxyXG4gICAgICBjb25zdCBlbnJpY2hlZEV2ZW50ID0gRW5yaWNoZWRFdmVudEZhY3RvcnkuY3JlYXRlRm9yVGVzdGluZyhvdmVycmlkZXMpO1xyXG5cclxuICAgICAgZXhwZWN0KGVucmljaGVkRXZlbnQubm9ybWFsaXplZEV2ZW50SWQpLnRvRXF1YWwobW9ja05vcm1hbGl6ZWRFdmVudC5pZCk7XHJcbiAgICAgIGV4cGVjdChlbnJpY2hlZEV2ZW50LmVucmljaG1lbnRTdGF0dXMpLnRvQmUoRW5yaWNobWVudFN0YXR1cy5GQUlMRUQpO1xyXG4gICAgICBleHBlY3QoZW5yaWNoZWRFdmVudC5lbnJpY2htZW50UXVhbGl0eVNjb3JlKS50b0JlKDMwKTtcclxuICAgICAgZXhwZWN0KGVucmljaGVkRXZlbnQudGhyZWF0SW50ZWxTY29yZSkudG9CZSg5MCk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIGluY2x1ZGUgbW9jayBlbnJpY2htZW50IGRhdGEgZm9yIHRlc3RpbmcnLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGVucmljaGVkRXZlbnQgPSBFbnJpY2hlZEV2ZW50RmFjdG9yeS5jcmVhdGVGb3JUZXN0aW5nKCk7XHJcblxyXG4gICAgICBleHBlY3QoZW5yaWNoZWRFdmVudC5lbnJpY2htZW50RGF0YSkudG9IYXZlTGVuZ3RoKDIpO1xyXG4gICAgICBcclxuICAgICAgY29uc3QgaXBSZXB1dGF0aW9uRGF0YSA9IGVucmljaGVkRXZlbnQuZ2V0RW5yaWNobWVudERhdGFCeVNvdXJjZShFbnJpY2htZW50U291cmNlLklQX1JFUFVUQVRJT04pO1xyXG4gICAgICBleHBlY3QoaXBSZXB1dGF0aW9uRGF0YSkudG9IYXZlTGVuZ3RoKDEpO1xyXG4gICAgICBleHBlY3QoaXBSZXB1dGF0aW9uRGF0YVswXS50eXBlKS50b0JlKCdyZXB1dGF0aW9uJyk7XHJcbiAgICAgIFxyXG4gICAgICBjb25zdCBnZW9sb2NhdGlvbkRhdGEgPSBlbnJpY2hlZEV2ZW50LmdldEVucmljaG1lbnREYXRhQnlTb3VyY2UoRW5yaWNobWVudFNvdXJjZS5JUF9HRU9MT0NBVElPTik7XHJcbiAgICAgIGV4cGVjdChnZW9sb2NhdGlvbkRhdGEpLnRvSGF2ZUxlbmd0aCgxKTtcclxuICAgICAgZXhwZWN0KGdlb2xvY2F0aW9uRGF0YVswXS50eXBlKS50b0JlKCdnZW9sb2NhdGlvbicpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBpbmNsdWRlIG1vY2sgY29udGV4dCBkYXRhIGZvciB0ZXN0aW5nJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBlbnJpY2hlZEV2ZW50ID0gRW5yaWNoZWRFdmVudEZhY3RvcnkuY3JlYXRlRm9yVGVzdGluZygpO1xyXG5cclxuICAgICAgY29uc3QgZW5yaWNoZWREYXRhID0gZW5yaWNoZWRFdmVudC5lbnJpY2hlZERhdGE7XHJcbiAgICAgIGV4cGVjdChlbnJpY2hlZERhdGEuZW5yaWNoZWQpLnRvQmUodHJ1ZSk7XHJcbiAgICAgIGV4cGVjdChlbnJpY2hlZERhdGEudGhyZWF0X2ludGVsbGlnZW5jZSkudG9CZURlZmluZWQoKTtcclxuICAgICAgZXhwZWN0KGVucmljaGVkRGF0YS5yZXB1dGF0aW9uKS50b0JlRGVmaW5lZCgpO1xyXG4gICAgICBleHBlY3QoZW5yaWNoZWREYXRhLmdlb2xvY2F0aW9uKS50b0JlRGVmaW5lZCgpO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCdlcnJvciBoYW5kbGluZycsICgpID0+IHtcclxuICAgIGl0KCdzaG91bGQgdGhyb3cgZXJyb3IgZm9yIGludmFsaWQgZW5yaWNoZWQgZXZlbnQgcHJvcGVydGllcycsICgpID0+IHtcclxuICAgICAgY29uc3Qgb3B0aW9uczogQ3JlYXRlRW5yaWNoZWRFdmVudE9wdGlvbnMgPSB7XHJcbiAgICAgICAgbm9ybWFsaXplZEV2ZW50OiBtb2NrTm9ybWFsaXplZEV2ZW50LFxyXG4gICAgICAgIGVucmljaGVkRGF0YTogeyBlbnJpY2hlZDogdHJ1ZSB9LFxyXG4gICAgICAgIGVucmljaG1lbnRRdWFsaXR5U2NvcmU6IDE1MCwgLy8gSW52YWxpZCBzY29yZVxyXG4gICAgICB9O1xyXG5cclxuICAgICAgZXhwZWN0KCgpID0+IEVucmljaGVkRXZlbnRGYWN0b3J5LmNyZWF0ZShvcHRpb25zKSkudG9UaHJvdyhcclxuICAgICAgICAnRW5yaWNobWVudCBxdWFsaXR5IHNjb3JlIG11c3QgYmUgYmV0d2VlbiAwIGFuZCAxMDAnXHJcbiAgICAgICk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBpdCgnc2hvdWxkIHRocm93IGVycm9yIGZvciBpbnZhbGlkIHRocmVhdCBpbnRlbGxpZ2VuY2Ugc2NvcmUnLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IG9wdGlvbnM6IENyZWF0ZUVucmljaGVkRXZlbnRPcHRpb25zID0ge1xyXG4gICAgICAgIG5vcm1hbGl6ZWRFdmVudDogbW9ja05vcm1hbGl6ZWRFdmVudCxcclxuICAgICAgICBlbnJpY2hlZERhdGE6IHsgZW5yaWNoZWQ6IHRydWUgfSxcclxuICAgICAgICB0aHJlYXRJbnRlbFNjb3JlOiAtMTAsIC8vIEludmFsaWQgc2NvcmVcclxuICAgICAgfTtcclxuXHJcbiAgICAgIGV4cGVjdCgoKSA9PiBFbnJpY2hlZEV2ZW50RmFjdG9yeS5jcmVhdGUob3B0aW9ucykpLnRvVGhyb3coXHJcbiAgICAgICAgJ1RocmVhdCBpbnRlbGxpZ2VuY2Ugc2NvcmUgbXVzdCBiZSBiZXR3ZWVuIDAgYW5kIDEwMCdcclxuICAgICAgKTtcclxuICAgIH0pO1xyXG5cclxuICAgIGl0KCdzaG91bGQgaGFuZGxlIG1pc3Npbmcgbm9ybWFsaXplZCBldmVudCBncmFjZWZ1bGx5JywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBvcHRpb25zOiBDcmVhdGVFbnJpY2hlZEV2ZW50T3B0aW9ucyA9IHtcclxuICAgICAgICBub3JtYWxpemVkRXZlbnQ6IG51bGwgYXMgYW55LFxyXG4gICAgICAgIGVucmljaGVkRGF0YTogeyBlbnJpY2hlZDogdHJ1ZSB9LFxyXG4gICAgICB9O1xyXG5cclxuICAgICAgZXhwZWN0KCgpID0+IEVucmljaGVkRXZlbnRGYWN0b3J5LmNyZWF0ZShvcHRpb25zKSkudG9UaHJvdygpO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcblxyXG4gIGRlc2NyaWJlKCdjb25maWd1cmF0aW9uIGhhbmRsaW5nJywgKCkgPT4ge1xyXG4gICAgaXQoJ3Nob3VsZCB1c2UgZGVmYXVsdCBjb25maWd1cmF0aW9uIHdoZW4gbm9uZSBwcm92aWRlZCcsICgpID0+IHtcclxuICAgICAgY29uc3QgZW5yaWNoZWRFdmVudCA9IEVucmljaGVkRXZlbnRGYWN0b3J5LmNyZWF0ZVdpdGhFbnJpY2htZW50KG1vY2tOb3JtYWxpemVkRXZlbnQpO1xyXG5cclxuICAgICAgZXhwZWN0KGVucmljaGVkRXZlbnQpLnRvQmVJbnN0YW5jZU9mKEVucmljaGVkRXZlbnQpO1xyXG4gICAgICBleHBlY3QoZW5yaWNoZWRFdmVudC5lbnJpY2htZW50UXVhbGl0eVNjb3JlKS50b0JlRGVmaW5lZCgpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCBtZXJnZSBwcm92aWRlZCBjb25maWd1cmF0aW9uIHdpdGggZGVmYXVsdHMnLCAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IHBhcnRpYWxDb25maWc6IFBhcnRpYWw8RW5yaWNobWVudENvbmZpZz4gPSB7XHJcbiAgICAgICAgbWluRW5yaWNobWVudFF1YWxpdHlUaHJlc2hvbGQ6IDgwLFxyXG4gICAgICAgIHJlcXVpcmVNYW51YWxSZXZpZXdGb3JIaWdoUmlzazogZmFsc2UsXHJcbiAgICAgIH07XHJcblxyXG4gICAgICBjb25zdCBlbnJpY2hlZEV2ZW50ID0gRW5yaWNoZWRFdmVudEZhY3RvcnkuY3JlYXRlV2l0aEVucmljaG1lbnQobW9ja05vcm1hbGl6ZWRFdmVudCwgcGFydGlhbENvbmZpZyk7XHJcblxyXG4gICAgICBleHBlY3QoZW5yaWNoZWRFdmVudCkudG9CZUluc3RhbmNlT2YoRW5yaWNoZWRFdmVudCk7XHJcbiAgICAgIC8vIFNob3VsZCB1c2UgdGhlIGN1c3RvbSB0aHJlc2hvbGQgZm9yIG1hbnVhbCByZXZpZXcgZGV0ZXJtaW5hdGlvblxyXG4gICAgfSk7XHJcblxyXG4gICAgaXQoJ3Nob3VsZCByZXNwZWN0IGVuYWJsZWQgc291cmNlcyBjb25maWd1cmF0aW9uJywgKCkgPT4ge1xyXG4gICAgICBjb25zdCBjb25maWc6IFBhcnRpYWw8RW5yaWNobWVudENvbmZpZz4gPSB7XHJcbiAgICAgICAgZW5hYmxlZFNvdXJjZXM6IFtFbnJpY2htZW50U291cmNlLklQX1JFUFVUQVRJT05dLCAvLyBPbmx5IElQIHJlcHV0YXRpb24gZW5hYmxlZFxyXG4gICAgICAgIGF2YWlsYWJsZVJ1bGVzOiBbe1xyXG4gICAgICAgICAgaWQ6ICdnZW8tcnVsZScsXHJcbiAgICAgICAgICBuYW1lOiAnR2VvbG9jYXRpb24gUnVsZScsXHJcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJ0FkZHMgZ2VvbG9jYXRpb24gZGF0YScsXHJcbiAgICAgICAgICBwcmlvcml0eTogNTAsXHJcbiAgICAgICAgICByZXF1aXJlZDogZmFsc2UsXHJcbiAgICAgICAgICBzb3VyY2VzOiBbRW5yaWNobWVudFNvdXJjZS5JUF9HRU9MT0NBVElPTl0sIC8vIFRoaXMgc2hvdWxkIGJlIHNraXBwZWRcclxuICAgICAgICB9XSxcclxuICAgICAgfTtcclxuXHJcbiAgICAgIGNvbnN0IGVucmljaGVkRXZlbnQgPSBFbnJpY2hlZEV2ZW50RmFjdG9yeS5jcmVhdGVXaXRoRW5yaWNobWVudChtb2NrTm9ybWFsaXplZEV2ZW50LCBjb25maWcpO1xyXG5cclxuICAgICAgZXhwZWN0KGVucmljaGVkRXZlbnQpLnRvQmVJbnN0YW5jZU9mKEVucmljaGVkRXZlbnQpO1xyXG4gICAgICAvLyBUaGUgZ2VvbG9jYXRpb24gcnVsZSBzaG91bGQgbm90IGJlIGFwcGxpZWQgZHVlIHRvIGRpc2FibGVkIHNvdXJjZVxyXG4gICAgfSk7XHJcbiAgfSk7XHJcbn0pOyJdLCJ2ZXJzaW9uIjozfQ==