{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\__tests__\\threat.specification.spec.ts", "mappings": ";;AAAA,kEAuBiC;AACjC,uEAA6D;AAC7D,2EAAkE;AAClE,6FAAsF;AAEtF,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;IACnC,IAAI,MAAc,CAAC;IACnB,IAAI,cAAsB,CAAC;IAC3B,IAAI,iBAAyB,CAAC;IAC9B,IAAI,cAAsB,CAAC;IAE3B,UAAU,CAAC,GAAG,EAAE;QACd,sBAAsB;QACtB,MAAM,GAAG,sBAAM,CAAC,MAAM,CACpB,aAAa,EACb,yBAAyB,EACzB,qCAAc,CAAC,IAAI,EACnB,SAAS,EACT,QAAQ,EACR,EAAE,EACF;YACE,IAAI,EAAE,CAAC,SAAS,EAAE,eAAe,CAAC;YAClC,cAAc,EAAE,CAAC,YAAY,EAAE,iBAAiB,CAAC;YACjD,UAAU,EAAE,CAAC,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,eAAe,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;YAClF,WAAW,EAAE;gBACX,KAAK,EAAE,OAAO;gBACd,UAAU,EAAE,EAAE;gBACd,OAAO,EAAE,CAAC,WAAW,CAAC;gBACtB,UAAU,EAAE,CAAC,WAAW,CAAC;gBACzB,YAAY,EAAE,CAAC,kBAAkB,CAAC;gBAClC,SAAS,EAAE,CAAC,iBAAiB,CAAC;aAC/B;YACD,aAAa,EAAE;gBACb,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE,EAAE;gBACd,YAAY,EAAE,CAAC,kBAAkB,CAAC;gBAClC,SAAS,EAAE,CAAC,MAAM,CAAC;gBACnB,WAAW,EAAE,CAAC,UAAU,CAAC;aAC1B;SACF,CACF,CAAC;QAEF,cAAc,GAAG,sBAAM,CAAC,MAAM,CAC5B,iBAAiB,EACjB,+CAA+C,EAC/C,qCAAc,CAAC,QAAQ,EACvB,KAAK,EACL,4BAA4B,EAC5B,EAAE,EACF;YACE,IAAI,EAAE,CAAC,UAAU,EAAE,KAAK,EAAE,cAAc,CAAC;YACzC,cAAc,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,YAAY,CAAC;YACtD,UAAU,EAAE;gBACV,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,WAAW,CAAC;gBAC/D,sBAAG,CAAC,MAAM,CAAC,0BAAO,CAAC,MAAM,EAAE,uBAAuB,EAAE,MAAM,EAAE,WAAW,CAAC;aACzE;SACF,CACF,CAAC;QAEF,iBAAiB,GAAG,sBAAM,CAAC,MAAM,CAC/B,qBAAqB,EACrB,qBAAqB,EACrB,qCAAc,CAAC,GAAG,EAClB,qBAAqB,EACrB,SAAS,EACT,EAAE,EACF;YACE,IAAI,EAAE,CAAC,cAAc,EAAE,SAAS,CAAC;YACjC,cAAc,EAAE,CAAC,iBAAiB,CAAC;SACpC,CACF,CAAC;QAEF,cAAc,GAAG,sBAAM,CAAC,MAAM,CAC5B,iBAAiB,EACjB,iDAAiD,EACjD,qCAAc,CAAC,MAAM,EACrB,SAAS,EACT,OAAO,EACP,EAAE,CACH,CAAC;QACF,cAAc,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC;IACrD,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC/C,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,IAAI,GAAG,IAAI,sDAA+B,EAAE,CAAC;YAEnD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB;YAC/D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,oBAAoB;YAC3E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,eAAe;YAC1E,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,IAAI,GAAG,IAAI,kDAA2B,EAAE,CAAC;YAE/C,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,gBAAgB;YAChE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,oBAAoB;YAC3E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,eAAe;YAC1E,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,IAAI,GAAG,IAAI,gDAAyB,EAAE,CAAC;YAE7C,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,IAAI,GAAG,IAAI,kDAA2B,EAAE,CAAC;YAE/C,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,IAAI,GAAG,IAAI,kDAA2B,EAAE,CAAC;YAE/C,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,8BAA8B;YAC7E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,mCAAmC;YAC1F,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,6BAA6B;YACxF,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,IAAI,GAAG,IAAI,kDAA2B,CAAC,EAAE,CAAC,CAAC;YAEjD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAiB;YACxE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,qBAAqB;YACrE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mCAAmC,EAAE,GAAG,EAAE;QACjD,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;YACxE,MAAM,IAAI,GAAG,IAAI,wDAAiC,EAAE,CAAC;YAErD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAiB;YAChE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAiB;YACxE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAiB;YAC5E,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,IAAI,GAAG,IAAI,wDAAiC,CAAC,EAAE,CAAC,CAAC;YAEvD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAiB;YACxE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAiB;YACjE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,IAAI,GAAG,IAAI,gDAAyB,CAAC,EAAE,CAAC,CAAC,CAAC,kBAAkB;YAElE,mDAAmD;YACnD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,IAAI,GAAG,IAAI,gDAAyB,EAAE,CAAC;YAE7C,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,IAAI,GAAG,IAAI,+CAAwB,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB;YAElE,4DAA4D;YAC5D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,IAAI,GAAG,IAAI,kDAA2B,CAAC,CAAC,qCAAc,CAAC,IAAI,EAAE,qCAAc,CAAC,QAAQ,CAAC,CAAC,CAAC;YAE7F,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO;YACtD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW;YAClE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM;YACjE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,IAAI,GAAG,IAAI,kDAA2B,CAAC,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;YAEjE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU;YACzD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM;YAC7D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,sBAAsB;YACjF,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,IAAI,GAAG,IAAI,8CAAuB,CAAC,CAAC,QAAQ,EAAE,4BAA4B,CAAC,CAAC,CAAC;YAEnF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;YACxD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,6BAA6B;YACpF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU;YACrE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAC;QAClG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,IAAI,GAAG,IAAI,6CAAsB,CAAC,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC;YAEjE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,oBAAoB;YACnE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,qBAAqB;YAC5E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,kBAAkB;YAC7E,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sEAAsE,EAAE,GAAG,EAAE;YAC9E,MAAM,IAAI,GAAG,IAAI,6CAAsB,CAAC,CAAC,SAAS,EAAE,eAAe,CAAC,EAAE,IAAI,CAAC,CAAC;YAE5E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB;YAC/D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,6BAA6B;YACrF,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QAC7F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mCAAmC,EAAE,GAAG,EAAE;QACjD,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,IAAI,GAAG,IAAI,wDAAiC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAE3D,mEAAmE;YACnE,mDAAmD;YACnD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,IAAI,GAAG,IAAI,wDAAiC,CAAC,EAAE,CAAC,CAAC;YAEvD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,IAAI,GAAG,IAAI,wDAAiC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YAElE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE;QAClD,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,IAAI,GAAG,IAAI,yDAAkC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAE5D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAiB;YAChE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,+BAA+B;YACvF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,+BAA+B;YAC1F,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,IAAI,GAAG,IAAI,kDAA2B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,gBAAgB;YAEpE,0DAA0D;YAC1D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;QAC9C,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,IAAI,GAAG,IAAI,qDAA8B,EAAE,CAAC;YAElD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,kBAAkB;YACjE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAiB;YAC5E,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,IAAI,GAAG,IAAI,qDAA8B,CAAC,OAAO,CAAC,CAAC;YAEzD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,sBAAsB;YACrE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAiB;YACzE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,IAAI,GAAG,IAAI,qDAA8B,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC;YAE9E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,0BAA0B;YACzE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAiB;YACzE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uEAAuE,EAAE,GAAG,EAAE;YAC/E,MAAM,IAAI,GAAG,IAAI,qDAA8B,CAAC,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;YAE7E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,kBAAkB;YAClE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAiB;YAC3E,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAChD,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,IAAI,GAAG,IAAI,uDAAgC,EAAE,CAAC;YAEpD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,qBAAqB;YACpE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,oBAAoB;YAC5E,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,IAAI,GAAG,IAAI,uDAAgC,CAAC,QAAQ,CAAC,CAAC;YAE5D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB;YAC/D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,oBAAoB;YAC5E,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4EAA4E,EAAE,GAAG,EAAE;YACpF,MAAM,IAAI,GAAG,IAAI,uDAAgC,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YAEpE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,qBAAqB;YACrE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,oBAAoB;YAC3E,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yCAAyC,EAAE,GAAG,EAAE;QACvD,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,IAAI,GAAG,IAAI,8DAAuC,EAAE,CAAC;YAE3D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,qCAAqC;YACpF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,oBAAoB;YAC3E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,mCAAmC;YAC9F,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,+FAA+F,CAAC,CAAC;QACtI,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,IAAI,GAAG,IAAI,kDAA2B,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;YAE7D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,qBAAqB;YACpE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,4BAA4B;YACpF,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,IAAI,GAAG,IAAI,kDAA2B,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAE9D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAmB;YAClE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAmB;YAC1E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,kBAAkB;YAC7E,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,IAAI,GAAG,IAAI,kDAA2B,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;YAE3D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAmB;YAClE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAmB;YAC1E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,kBAAkB;YAC7E,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,IAAI,GAAG,IAAI,mDAA4B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAEpD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,kBAAkB;YACjE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAmB;YAC1E,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,IAAI,GAAG,IAAI,mDAA4B,CAAC,CAAC,CAAC,CAAC;YAEjD,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,kBAAkB;YAClE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAmB;YAC1E,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,IAAI,GAAG,iDAA0B,CAAC,MAAM,EAAE;iBAC7C,YAAY,EAAE;iBACd,KAAK,EAAE,CAAC;YAEX,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,IAAI,GAAG,iDAA0B,CAAC,MAAM,EAAE;iBAC7C,YAAY,EAAE;iBACd,MAAM,EAAE;iBACR,cAAc,CAAC,EAAE,CAAC;iBAClB,cAAc,CAAC,SAAS,EAAE,KAAK,CAAC;iBAChC,KAAK,EAAE,CAAC;YAEX,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,qBAAqB;YACpE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,wBAAwB;YACnF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,aAAa;QACvE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,IAAI,GAAG,iDAA0B,CAAC,MAAM,EAAE;iBAC7C,QAAQ,EAAE;iBACV,0BAA0B,EAAE;iBAC5B,WAAW,EAAE,CAAC;YAEjB,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc;YACrE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,+BAA+B;YAC9E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,gBAAgB;QAC7E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,IAAI,GAAG,iDAA0B,CAAC,MAAM,EAAE;iBAC7C,QAAQ,CAAC,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;iBACjC,KAAK,EAAE,CAAC;YAEX,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,kBAAkB;YACjE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAmB;YAC1E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,kBAAkB;QAC/E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,IAAI,GAAG,iDAA0B,CAAC,MAAM,EAAE;iBAC7C,cAAc,CAAC,EAAE,EAAE,EAAE,CAAC;iBACtB,eAAe,CAAC,EAAE,EAAE,EAAE,CAAC;iBACvB,KAAK,EAAE,CAAC;YAEX,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,4BAA4B;YAC3E,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAiB;QAC9E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;YACxE,MAAM,IAAI,GAAG,iDAA0B,CAAC,MAAM,EAAE;iBAC7C,eAAe,CAAC,OAAO,CAAC;iBACxB,iBAAiB,CAAC,QAAQ,CAAC;iBAC3B,KAAK,EAAE,CAAC;YAEX,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW;YAC1D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,cAAc;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iEAAiE,EAAE,GAAG,EAAE;YACzE,MAAM,IAAI,GAAG,iDAA0B,CAAC,MAAM,EAAE;iBAC7C,eAAe,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,oBAAoB;iBAClD,kBAAkB,CAAC,CAAC,CAAC,CAAC,uBAAuB;iBAC7C,KAAK,EAAE,CAAC;YAEX,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,wBAAwB;YACvE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,yBAAyB;YAChF,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU;QACvE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,CAAC,GAAG,EAAE;gBACV,iDAA0B,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC;YAC9C,CAAC,CAAC,CAAC,OAAO,CAAC,0CAA0C,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,IAAI,GAAG,iDAA0B,CAAC,MAAM,EAAE;iBAC7C,QAAQ,EAAE;iBACV,KAAK,EAAE,CAAC;YAEX,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,kDAA2B,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,gBAAgB,GAAG,IAAI,sDAA+B,EAAE,CAAC;YAC/D,MAAM,UAAU,GAAG,IAAI,gDAAyB,EAAE,CAAC;YACnD,MAAM,YAAY,GAAG,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAEtD,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,2BAA2B;YAClF,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,aAAa;YAC7E,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,oBAAoB;QACzF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,YAAY,GAAG,IAAI,kDAA2B,EAAE,CAAC;YACvD,MAAM,YAAY,GAAG,IAAI,kDAA2B,EAAE,CAAC;YACvD,MAAM,YAAY,GAAG,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;YAEnD,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc;YAC7E,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc;YAC7E,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,gCAAgC;QAC1F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,UAAU,GAAG,IAAI,gDAAyB,EAAE,CAAC;YACnD,MAAM,aAAa,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC;YAEvC,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY;YACrE,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa;QAC/E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,MAAM,KAAK,GAAG;gBACZ,IAAI,sDAA+B,EAAE;gBACrC,IAAI,kDAA2B,EAAE;gBACjC,IAAI,gDAAyB,EAAE;gBAC/B,IAAI,kDAA2B,EAAE;gBACjC,IAAI,kDAA2B,EAAE;gBACjC,IAAI,wDAAiC,EAAE;gBACvC,IAAI,gDAAyB,EAAE;gBAC/B,IAAI,+CAAwB,EAAE;gBAC9B,IAAI,kDAA2B,CAAC,CAAC,qCAAc,CAAC,IAAI,CAAC,CAAC;gBACtD,IAAI,kDAA2B,CAAC,CAAC,SAAS,CAAC,CAAC;gBAC5C,IAAI,8CAAuB,CAAC,CAAC,QAAQ,CAAC,CAAC;gBACvC,IAAI,6CAAsB,CAAC,CAAC,MAAM,CAAC,CAAC;gBACpC,IAAI,wDAAiC,CAAC,EAAE,EAAE,EAAE,CAAC;gBAC7C,IAAI,yDAAkC,CAAC,EAAE,EAAE,EAAE,CAAC;gBAC9C,IAAI,kDAA2B,CAAC,CAAC,EAAE,EAAE,CAAC;gBACtC,IAAI,qDAA8B,EAAE;gBACpC,IAAI,uDAAgC,EAAE;gBACtC,IAAI,8DAAuC,EAAE;gBAC7C,IAAI,kDAA2B,EAAE;gBACjC,IAAI,mDAA4B,EAAE;aACnC,CAAC;YAEF,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACnB,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC;gBAC3C,MAAM,CAAC,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACpD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC1D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\__tests__\\threat.specification.spec.ts"], "sourcesContent": ["import {\r\n  ThreatSpecification,\r\n  HighSeverityThreatSpecification,\r\n  CriticalThreatSpecification,\r\n  ActiveThreatSpecification,\r\n  ResolvedThreatSpecification,\r\n  HighRiskThreatSpecification,\r\n  HighConfidenceThreatSpecification,\r\n  RecentThreatSpecification,\r\n  StaleThreatSpecification,\r\n  ThreatSeveritySpecification,\r\n  ThreatCategorySpecification,\r\n  ThreatTypeSpecification,\r\n  ThreatTagSpecification,\r\n  ThreatRiskScoreRangeSpecification,\r\n  ThreatConfidenceRangeSpecification,\r\n  ThreatAgeRangeSpecification,\r\n  ThreatAttributionSpecification,\r\n  ThreatMalwareFamilySpecification,\r\n  RequiresImmediateAttentionSpecification,\r\n  AffectedAssetsSpecification,\r\n  IndicatorsCountSpecification,\r\n  ThreatSpecificationBuilder,\r\n} from '../threat.specification';\r\nimport { Threat } from '../../entities/threat/threat.entity';\r\nimport { ThreatSeverity } from '../../enums/threat-severity.enum';\r\nimport { IOC, IOCType } from '../../value-objects/threat-indicators/ioc.value-object';\r\n\r\ndescribe('ThreatSpecification', () => {\r\n  let threat: Threat;\r\n  let criticalThreat: Threat;\r\n  let lowSeverityThreat: Threat;\r\n  let resolvedThreat: Threat;\r\n\r\n  beforeEach(() => {\r\n    // Create test threats\r\n    threat = Threat.create(\r\n      'Test Threat',\r\n      'Test threat description',\r\n      ThreatSeverity.HIGH,\r\n      'malware',\r\n      'trojan',\r\n      85,\r\n      {\r\n        tags: ['malware', 'high-priority'],\r\n        affectedAssets: ['server-001', 'workstation-042'],\r\n        indicators: [IOC.create(IOCType.IP_ADDRESS, '*************', 'high', 'confirmed')],\r\n        attribution: {\r\n          actor: 'APT29',\r\n          confidence: 90,\r\n          aliases: ['Cozy Bear'],\r\n          motivation: ['espionage'],\r\n          capabilities: ['advanced_evasion'],\r\n          campaigns: ['Operation Ghost'],\r\n        },\r\n        malwareFamily: {\r\n          name: 'Emotet',\r\n          confidence: 85,\r\n          capabilities: ['credential_theft'],\r\n          protocols: ['HTTP'],\r\n          persistence: ['registry'],\r\n        },\r\n      }\r\n    );\r\n\r\n    criticalThreat = Threat.create(\r\n      'Critical Threat',\r\n      'Critical threat requiring immediate attention',\r\n      ThreatSeverity.CRITICAL,\r\n      'apt',\r\n      'advanced-persistent-threat',\r\n      95,\r\n      {\r\n        tags: ['critical', 'apt', 'nation-state'],\r\n        affectedAssets: ['dc-01', 'file-server', 'web-server'],\r\n        indicators: [\r\n          IOC.create(IOCType.IP_ADDRESS, '********', 'high', 'confirmed'),\r\n          IOC.create(IOCType.DOMAIN, 'malicious.example.com', 'high', 'confirmed'),\r\n        ],\r\n      }\r\n    );\r\n\r\n    lowSeverityThreat = Threat.create(\r\n      'Low Severity Threat',\r\n      'Low severity threat',\r\n      ThreatSeverity.LOW,\r\n      'suspicious-activity',\r\n      'anomaly',\r\n      40,\r\n      {\r\n        tags: ['low-priority', 'anomaly'],\r\n        affectedAssets: ['workstation-001'],\r\n      }\r\n    );\r\n\r\n    resolvedThreat = Threat.create(\r\n      'Resolved Threat',\r\n      'Previously active threat that has been resolved',\r\n      ThreatSeverity.MEDIUM,\r\n      'malware',\r\n      'virus',\r\n      75\r\n    );\r\n    resolvedThreat.updateMitigationStatus('recovered');\r\n  });\r\n\r\n  describe('HighSeverityThreatSpecification', () => {\r\n    it('should identify high severity threats', () => {\r\n      const spec = new HighSeverityThreatSpecification();\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true); // HIGH severity\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // CRITICAL severity\r\n      expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // LOW severity\r\n      expect(spec.getDescription()).toBe('Threat has high or critical severity');\r\n    });\r\n  });\r\n\r\n  describe('CriticalThreatSpecification', () => {\r\n    it('should identify only critical threats', () => {\r\n      const spec = new CriticalThreatSpecification();\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(false); // HIGH severity\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // CRITICAL severity\r\n      expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // LOW severity\r\n      expect(spec.getDescription()).toBe('Threat has critical severity');\r\n    });\r\n  });\r\n\r\n  describe('ActiveThreatSpecification', () => {\r\n    it('should identify active threats', () => {\r\n      const spec = new ActiveThreatSpecification();\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true);\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(true);\r\n      expect(spec.isSatisfiedBy(resolvedThreat)).toBe(false);\r\n      expect(spec.getDescription()).toBe('Threat is active (not resolved)');\r\n    });\r\n  });\r\n\r\n  describe('ResolvedThreatSpecification', () => {\r\n    it('should identify resolved threats', () => {\r\n      const spec = new ResolvedThreatSpecification();\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(false);\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(false);\r\n      expect(spec.isSatisfiedBy(resolvedThreat)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Threat has been resolved');\r\n    });\r\n  });\r\n\r\n  describe('HighRiskThreatSpecification', () => {\r\n    it('should identify high risk threats with default threshold', () => {\r\n      const spec = new HighRiskThreatSpecification();\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true); // Should have high risk score\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // Should have very high risk score\r\n      expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // Should have low risk score\r\n      expect(spec.getDescription()).toBe('Threat has high risk score (>= 70)');\r\n    });\r\n\r\n    it('should use custom risk threshold', () => {\r\n      const spec = new HighRiskThreatSpecification(90);\r\n\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // Very high risk\r\n      expect(spec.isSatisfiedBy(threat)).toBe(false); // High but not >= 90\r\n      expect(spec.getDescription()).toBe('Threat has high risk score (>= 90)');\r\n    });\r\n  });\r\n\r\n  describe('HighConfidenceThreatSpecification', () => {\r\n    it('should identify high confidence threats with default threshold', () => {\r\n      const spec = new HighConfidenceThreatSpecification();\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true); // 85% confidence\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // 95% confidence\r\n      expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // 40% confidence\r\n      expect(spec.getDescription()).toBe('Threat has high confidence (>= 80)');\r\n    });\r\n\r\n    it('should use custom confidence threshold', () => {\r\n      const spec = new HighConfidenceThreatSpecification(90);\r\n\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // 95% confidence\r\n      expect(spec.isSatisfiedBy(threat)).toBe(false); // 85% confidence\r\n      expect(spec.getDescription()).toBe('Threat has high confidence (>= 90)');\r\n    });\r\n  });\r\n\r\n  describe('RecentThreatSpecification', () => {\r\n    it('should identify recent threats', () => {\r\n      const spec = new RecentThreatSpecification(24); // Within 24 hours\r\n\r\n      // All test threats should be recent (just created)\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true);\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Threat was detected within 24 hours');\r\n    });\r\n\r\n    it('should use default time window', () => {\r\n      const spec = new RecentThreatSpecification();\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Threat was detected within 24 hours');\r\n    });\r\n  });\r\n\r\n  describe('StaleThreatSpecification', () => {\r\n    it('should identify stale threats', () => {\r\n      const spec = new StaleThreatSpecification(1); // Older than 1 hour\r\n\r\n      // Test threats are just created, so they shouldn't be stale\r\n      expect(spec.isSatisfiedBy(threat)).toBe(false);\r\n      expect(spec.getDescription()).toBe('Threat is older than 1 hours');\r\n    });\r\n  });\r\n\r\n  describe('ThreatSeveritySpecification', () => {\r\n    it('should match threats with specified severities', () => {\r\n      const spec = new ThreatSeveritySpecification([ThreatSeverity.HIGH, ThreatSeverity.CRITICAL]);\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true); // HIGH\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // CRITICAL\r\n      expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // LOW\r\n      expect(spec.getDescription()).toBe('Threat severity is one of: HIGH, CRITICAL');\r\n    });\r\n  });\r\n\r\n  describe('ThreatCategorySpecification', () => {\r\n    it('should match threats with specified categories', () => {\r\n      const spec = new ThreatCategorySpecification(['malware', 'apt']);\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true); // malware\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // apt\r\n      expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // suspicious-activity\r\n      expect(spec.getDescription()).toBe('Threat category is one of: malware, apt');\r\n    });\r\n  });\r\n\r\n  describe('ThreatTypeSpecification', () => {\r\n    it('should match threats with specified types', () => {\r\n      const spec = new ThreatTypeSpecification(['trojan', 'advanced-persistent-threat']);\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true); // trojan\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // advanced-persistent-threat\r\n      expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // anomaly\r\n      expect(spec.getDescription()).toBe('Threat type is one of: trojan, advanced-persistent-threat');\r\n    });\r\n  });\r\n\r\n  describe('ThreatTagSpecification', () => {\r\n    it('should match threats with any of the specified tags', () => {\r\n      const spec = new ThreatTagSpecification(['malware', 'critical']);\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true); // has 'malware' tag\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // has 'critical' tag\r\n      expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // has neither tag\r\n      expect(spec.getDescription()).toBe('Threat has any of these tags: malware, critical');\r\n    });\r\n\r\n    it('should match threats with all specified tags when requireAll is true', () => {\r\n      const spec = new ThreatTagSpecification(['malware', 'high-priority'], true);\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true); // has both tags\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(false); // doesn't have 'malware' tag\r\n      expect(spec.getDescription()).toBe('Threat has all of these tags: malware, high-priority');\r\n    });\r\n  });\r\n\r\n  describe('ThreatRiskScoreRangeSpecification', () => {\r\n    it('should match threats within risk score range', () => {\r\n      const spec = new ThreatRiskScoreRangeSpecification(60, 80);\r\n\r\n      // This depends on the actual risk scores calculated by the threats\r\n      // We'll test the logic rather than specific values\r\n      expect(spec.getDescription()).toBe('Threat risk score is between 60 and 80');\r\n    });\r\n\r\n    it('should match threats with minimum risk score', () => {\r\n      const spec = new ThreatRiskScoreRangeSpecification(50);\r\n\r\n      expect(spec.getDescription()).toBe('Threat risk score is at least 50');\r\n    });\r\n\r\n    it('should match threats with maximum risk score', () => {\r\n      const spec = new ThreatRiskScoreRangeSpecification(undefined, 90);\r\n\r\n      expect(spec.getDescription()).toBe('Threat risk score is at most 90');\r\n    });\r\n  });\r\n\r\n  describe('ThreatConfidenceRangeSpecification', () => {\r\n    it('should match threats within confidence range', () => {\r\n      const spec = new ThreatConfidenceRangeSpecification(80, 90);\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true); // 85% confidence\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(false); // 95% confidence (above range)\r\n      expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // 40% confidence (below range)\r\n      expect(spec.getDescription()).toBe('Threat confidence is between 80 and 90');\r\n    });\r\n  });\r\n\r\n  describe('ThreatAgeRangeSpecification', () => {\r\n    it('should match threats within age range', () => {\r\n      const spec = new ThreatAgeRangeSpecification(0, 1); // 0-1 hours old\r\n\r\n      // All test threats are just created, so they should match\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true);\r\n      expect(spec.getDescription()).toBe('Threat age is between 0 and 1 hours');\r\n    });\r\n  });\r\n\r\n  describe('ThreatAttributionSpecification', () => {\r\n    it('should match threats with attribution', () => {\r\n      const spec = new ThreatAttributionSpecification();\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true); // has attribution\r\n      expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // no attribution\r\n      expect(spec.getDescription()).toBe('Threat has attribution information');\r\n    });\r\n\r\n    it('should match threats with specific actor', () => {\r\n      const spec = new ThreatAttributionSpecification('APT29');\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true); // attributed to APT29\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(false); // no attribution\r\n      expect(spec.getDescription()).toBe('Threat is attributed to actor \"APT29\"');\r\n    });\r\n\r\n    it('should match threats with specific campaign', () => {\r\n      const spec = new ThreatAttributionSpecification(undefined, 'Operation Ghost');\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true); // part of Operation Ghost\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(false); // no attribution\r\n      expect(spec.getDescription()).toBe('Threat is part of campaign \"Operation Ghost\"');\r\n    });\r\n\r\n    it('should match threats without attribution when hasAttribution is false', () => {\r\n      const spec = new ThreatAttributionSpecification(undefined, undefined, false);\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(false); // has attribution\r\n      expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(true); // no attribution\r\n      expect(spec.getDescription()).toBe('Threat has no attribution information');\r\n    });\r\n  });\r\n\r\n  describe('ThreatMalwareFamilySpecification', () => {\r\n    it('should match threats with malware family', () => {\r\n      const spec = new ThreatMalwareFamilySpecification();\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true); // has malware family\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(false); // no malware family\r\n      expect(spec.getDescription()).toBe('Threat has malware family information');\r\n    });\r\n\r\n    it('should match threats with specific malware family', () => {\r\n      const spec = new ThreatMalwareFamilySpecification('Emotet');\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true); // Emotet family\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(false); // no malware family\r\n      expect(spec.getDescription()).toBe('Threat is associated with malware family \"Emotet\"');\r\n    });\r\n\r\n    it('should match threats without malware family when hasMalwareFamily is false', () => {\r\n      const spec = new ThreatMalwareFamilySpecification(undefined, false);\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(false); // has malware family\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // no malware family\r\n      expect(spec.getDescription()).toBe('Threat has no malware family information');\r\n    });\r\n  });\r\n\r\n  describe('RequiresImmediateAttentionSpecification', () => {\r\n    it('should identify threats requiring immediate attention', () => {\r\n      const spec = new RequiresImmediateAttentionSpecification();\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true); // HIGH severity with high confidence\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // CRITICAL severity\r\n      expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // LOW severity with low confidence\r\n      expect(spec.getDescription()).toBe('Threat requires immediate attention (critical severity or high severity with high confidence)');\r\n    });\r\n  });\r\n\r\n  describe('AffectedAssetsSpecification', () => {\r\n    it('should match threats affecting specific assets', () => {\r\n      const spec = new AffectedAssetsSpecification(['server-001']);\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true); // affects server-001\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(false); // doesn't affect server-001\r\n      expect(spec.getDescription()).toBe('Threat affects assets: server-001');\r\n    });\r\n\r\n    it('should match threats with asset count in range', () => {\r\n      const spec = new AffectedAssetsSpecification(undefined, 2, 5);\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true); // affects 2 assets\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // affects 3 assets\r\n      expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // affects 1 asset\r\n      expect(spec.getDescription()).toBe('Threat affects 2-5 assets');\r\n    });\r\n\r\n    it('should match threats with minimum asset count', () => {\r\n      const spec = new AffectedAssetsSpecification(undefined, 2);\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true); // affects 2 assets\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // affects 3 assets\r\n      expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // affects 1 asset\r\n      expect(spec.getDescription()).toBe('Threat affects at least 2 assets');\r\n    });\r\n  });\r\n\r\n  describe('IndicatorsCountSpecification', () => {\r\n    it('should match threats with indicator count in range', () => {\r\n      const spec = new IndicatorsCountSpecification(1, 2);\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true); // has 1 indicator\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // has 2 indicators\r\n      expect(spec.getDescription()).toBe('Threat has 1-2 indicators');\r\n    });\r\n\r\n    it('should match threats with minimum indicator count', () => {\r\n      const spec = new IndicatorsCountSpecification(2);\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(false); // has 1 indicator\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // has 2 indicators\r\n      expect(spec.getDescription()).toBe('Threat has at least 2 indicators');\r\n    });\r\n  });\r\n\r\n  describe('ThreatSpecificationBuilder', () => {\r\n    it('should build simple specification', () => {\r\n      const spec = ThreatSpecificationBuilder.create()\r\n        .highSeverity()\r\n        .build();\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true);\r\n      expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false);\r\n    });\r\n\r\n    it('should build complex AND specification', () => {\r\n      const spec = ThreatSpecificationBuilder.create()\r\n        .highSeverity()\r\n        .active()\r\n        .highConfidence(80)\r\n        .withCategories('malware', 'apt')\r\n        .build();\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true); // meets all criteria\r\n      expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // doesn't meet severity\r\n      expect(spec.isSatisfiedBy(resolvedThreat)).toBe(false); // not active\r\n    });\r\n\r\n    it('should build complex OR specification', () => {\r\n      const spec = ThreatSpecificationBuilder.create()\r\n        .critical()\r\n        .requiresImmediateAttention()\r\n        .buildWithOr();\r\n\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // is critical\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true); // requires immediate attention\r\n      expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // meets neither\r\n    });\r\n\r\n    it('should build specification with tags', () => {\r\n      const spec = ThreatSpecificationBuilder.create()\r\n        .withTags(['malware', 'critical'])\r\n        .build();\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true); // has malware tag\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // has critical tag\r\n      expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // has neither tag\r\n    });\r\n\r\n    it('should build specification with risk and confidence ranges', () => {\r\n      const spec = ThreatSpecificationBuilder.create()\r\n        .riskScoreRange(50, 90)\r\n        .confidenceRange(70, 95)\r\n        .build();\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true); // should meet both criteria\r\n      expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // low confidence\r\n    });\r\n\r\n    it('should build specification with attribution and malware family', () => {\r\n      const spec = ThreatSpecificationBuilder.create()\r\n        .withAttribution('APT29')\r\n        .withMalwareFamily('Emotet')\r\n        .build();\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true); // has both\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(false); // has neither\r\n    });\r\n\r\n    it('should build specification with asset and indicator constraints', () => {\r\n      const spec = ThreatSpecificationBuilder.create()\r\n        .affectingAssets(undefined, 2) // at least 2 assets\r\n        .withIndicatorCount(1) // at least 1 indicator\r\n        .build();\r\n\r\n      expect(spec.isSatisfiedBy(threat)).toBe(true); // 2 assets, 1 indicator\r\n      expect(spec.isSatisfiedBy(criticalThreat)).toBe(true); // 3 assets, 2 indicators\r\n      expect(spec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // 1 asset\r\n    });\r\n\r\n    it('should throw error when building empty specification', () => {\r\n      expect(() => {\r\n        ThreatSpecificationBuilder.create().build();\r\n      }).toThrow('At least one specification must be added');\r\n    });\r\n\r\n    it('should return single specification when only one is added', () => {\r\n      const spec = ThreatSpecificationBuilder.create()\r\n        .critical()\r\n        .build();\r\n\r\n      expect(spec).toBeInstanceOf(CriticalThreatSpecification);\r\n    });\r\n  });\r\n\r\n  describe('specification combination', () => {\r\n    it('should combine specifications with AND logic', () => {\r\n      const highSeveritySpec = new HighSeverityThreatSpecification();\r\n      const activeSpec = new ActiveThreatSpecification();\r\n      const combinedSpec = highSeveritySpec.and(activeSpec);\r\n\r\n      expect(combinedSpec.isSatisfiedBy(threat)).toBe(true); // high severity and active\r\n      expect(combinedSpec.isSatisfiedBy(resolvedThreat)).toBe(false); // not active\r\n      expect(combinedSpec.isSatisfiedBy(lowSeverityThreat)).toBe(false); // not high severity\r\n    });\r\n\r\n    it('should combine specifications with OR logic', () => {\r\n      const criticalSpec = new CriticalThreatSpecification();\r\n      const resolvedSpec = new ResolvedThreatSpecification();\r\n      const combinedSpec = criticalSpec.or(resolvedSpec);\r\n\r\n      expect(combinedSpec.isSatisfiedBy(criticalThreat)).toBe(true); // is critical\r\n      expect(combinedSpec.isSatisfiedBy(resolvedThreat)).toBe(true); // is resolved\r\n      expect(combinedSpec.isSatisfiedBy(threat)).toBe(false); // neither critical nor resolved\r\n    });\r\n\r\n    it('should negate specifications', () => {\r\n      const activeSpec = new ActiveThreatSpecification();\r\n      const notActiveSpec = activeSpec.not();\r\n\r\n      expect(notActiveSpec.isSatisfiedBy(threat)).toBe(false); // is active\r\n      expect(notActiveSpec.isSatisfiedBy(resolvedThreat)).toBe(true); // not active\r\n    });\r\n  });\r\n\r\n  describe('specification descriptions', () => {\r\n    it('should provide meaningful descriptions for all specifications', () => {\r\n      const specs = [\r\n        new HighSeverityThreatSpecification(),\r\n        new CriticalThreatSpecification(),\r\n        new ActiveThreatSpecification(),\r\n        new ResolvedThreatSpecification(),\r\n        new HighRiskThreatSpecification(),\r\n        new HighConfidenceThreatSpecification(),\r\n        new RecentThreatSpecification(),\r\n        new StaleThreatSpecification(),\r\n        new ThreatSeveritySpecification([ThreatSeverity.HIGH]),\r\n        new ThreatCategorySpecification(['malware']),\r\n        new ThreatTypeSpecification(['trojan']),\r\n        new ThreatTagSpecification(['test']),\r\n        new ThreatRiskScoreRangeSpecification(50, 90),\r\n        new ThreatConfidenceRangeSpecification(70, 95),\r\n        new ThreatAgeRangeSpecification(0, 24),\r\n        new ThreatAttributionSpecification(),\r\n        new ThreatMalwareFamilySpecification(),\r\n        new RequiresImmediateAttentionSpecification(),\r\n        new AffectedAssetsSpecification(),\r\n        new IndicatorsCountSpecification(),\r\n      ];\r\n\r\n      specs.forEach(spec => {\r\n        expect(spec.getDescription()).toBeTruthy();\r\n        expect(typeof spec.getDescription()).toBe('string');\r\n        expect(spec.getDescription().length).toBeGreaterThan(0);\r\n      });\r\n    });\r\n  });\r\n});"], "version": 3}