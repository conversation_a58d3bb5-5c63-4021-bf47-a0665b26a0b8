85df4248e354531b1a70ca0a8f02ea28
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductionSeeder = void 0;
const common_1 = require("@nestjs/common");
const bcrypt = __importStar(require("bcrypt"));
/**
 * Production environment database seeder
 * Creates essential system data for production deployment
 */
class ProductionSeeder {
    constructor(dataSource) {
        this.dataSource = dataSource;
        this.logger = new common_1.Logger(ProductionSeeder.name);
    }
    /**
     * Execute production seeds
     */
    async seed() {
        this.logger.log('Starting production database seeding...');
        try {
            await this.seedSystemRoles();
            await this.seedDefaultAdmin();
            await this.seedSecurityEventTags();
            await this.seedSystemConfiguration();
            this.logger.log('Production database seeding completed successfully');
        }
        catch (error) {
            this.logger.error('Production database seeding failed', {
                error: error.message,
                stack: error.stack,
            });
            throw error;
        }
    }
    /**
     * Seed essential system roles
     */
    async seedSystemRoles() {
        this.logger.debug('Seeding system roles...');
        const roles = [
            {
                name: 'super_admin',
                description: 'Super administrator with full system access',
                permissions: [
                    'system:admin',
                    'users:*',
                    'roles:*',
                    'security-events:*',
                    'vulnerabilities:*',
                    'assets:*',
                    'scans:*',
                    'reports:*',
                    'audit:*',
                    'configuration:*'
                ],
                is_system_role: true,
            },
            {
                name: 'admin',
                description: 'System administrator',
                permissions: [
                    'users:read', 'users:write',
                    'roles:read',
                    'security-events:*',
                    'vulnerabilities:*',
                    'assets:*',
                    'scans:*',
                    'reports:*',
                    'audit:read'
                ],
                is_system_role: true,
            },
            {
                name: 'security_analyst',
                description: 'Security analyst with operational access',
                permissions: [
                    'security-events:read', 'security-events:write',
                    'vulnerabilities:read', 'vulnerabilities:write',
                    'assets:read', 'assets:write',
                    'scans:read', 'scans:write',
                    'reports:read', 'reports:write'
                ],
                is_system_role: true,
            },
            {
                name: 'incident_responder',
                description: 'Incident response team member',
                permissions: [
                    'security-events:read', 'security-events:write',
                    'vulnerabilities:read',
                    'assets:read',
                    'reports:read'
                ],
                is_system_role: true,
            },
            {
                name: 'auditor',
                description: 'Security auditor with read-only access',
                permissions: [
                    'security-events:read',
                    'vulnerabilities:read',
                    'assets:read',
                    'scans:read',
                    'reports:read',
                    'audit:read'
                ],
                is_system_role: true,
            },
            {
                name: 'viewer',
                description: 'Read-only dashboard access',
                permissions: [
                    'security-events:read',
                    'vulnerabilities:read',
                    'assets:read',
                    'reports:read'
                ],
                is_system_role: true,
            },
        ];
        for (const role of roles) {
            await this.dataSource.query(`
        INSERT INTO roles (name, description, permissions, is_system_role)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (name) DO UPDATE SET
          description = EXCLUDED.description,
          permissions = EXCLUDED.permissions,
          is_system_role = EXCLUDED.is_system_role,
          updated_at = CURRENT_TIMESTAMP
      `, [role.name, role.description, JSON.stringify(role.permissions), role.is_system_role]);
        }
        this.logger.debug(`Seeded ${roles.length} system roles`);
    }
    /**
     * Seed default admin user (if not exists)
     */
    async seedDefaultAdmin() {
        this.logger.debug('Checking for default admin user...');
        // Check if any admin users exist
        const existingAdmins = await this.dataSource.query(`
      SELECT COUNT(*) as count FROM users WHERE role IN ('super_admin', 'admin')
    `);
        if (parseInt(existingAdmins[0].count) > 0) {
            this.logger.debug('Admin users already exist, skipping default admin creation');
            return;
        }
        // Create default admin only if no admin users exist
        const defaultPassword = process.env.DEFAULT_ADMIN_PASSWORD || 'ChangeMe123!';
        const passwordHash = await bcrypt.hash(defaultPassword, 12);
        const defaultAdmin = {
            email: process.env.DEFAULT_ADMIN_EMAIL || '<EMAIL>',
            password_hash: passwordHash,
            first_name: 'System',
            last_name: 'Administrator',
            role: 'super_admin',
            is_active: true,
            email_verified: true,
        };
        await this.dataSource.query(`
      INSERT INTO users (email, password_hash, first_name, last_name, role, is_active, email_verified)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
    `, [
            defaultAdmin.email, defaultAdmin.password_hash, defaultAdmin.first_name,
            defaultAdmin.last_name, defaultAdmin.role, defaultAdmin.is_active,
            defaultAdmin.email_verified
        ]);
        this.logger.warn('Default admin user created', {
            email: defaultAdmin.email,
            message: 'Please change the default password immediately after first login'
        });
    }
    /**
     * Seed essential security event tags
     */
    async seedSecurityEventTags() {
        this.logger.debug('Seeding security event tags...');
        const tags = [
            { name: 'critical', description: 'Critical security events requiring immediate attention', color: '#dc3545' },
            { name: 'high-priority', description: 'High priority security events', color: '#fd7e14' },
            { name: 'malware', description: 'Malware detection events', color: '#6f42c1' },
            { name: 'intrusion', description: 'Intrusion attempts and breaches', color: '#e83e8c' },
            { name: 'brute-force', description: 'Brute force attack attempts', color: '#dc3545' },
            { name: 'ddos', description: 'Distributed denial of service attacks', color: '#fd7e14' },
            { name: 'data-exfiltration', description: 'Potential data exfiltration events', color: '#6f42c1' },
            { name: 'privilege-escalation', description: 'Privilege escalation attempts', color: '#e83e8c' },
            { name: 'lateral-movement', description: 'Lateral movement within network', color: '#fd7e14' },
            { name: 'persistence', description: 'Persistence mechanism establishment', color: '#6f42c1' },
            { name: 'reconnaissance', description: 'Network reconnaissance activities', color: '#6c757d' },
            { name: 'false-positive', description: 'Confirmed false positive events', color: '#28a745' },
            { name: 'resolved', description: 'Resolved security events', color: '#17a2b8' },
            { name: 'under-investigation', description: 'Events currently under investigation', color: '#ffc107' },
            { name: 'automated', description: 'Automated attack patterns', color: '#6c757d' },
            { name: 'manual', description: 'Manual attack activities', color: '#dc3545' },
            { name: 'external', description: 'External threat sources', color: '#fd7e14' },
            { name: 'internal', description: 'Internal threat sources', color: '#e83e8c' },
            { name: 'compliance', description: 'Compliance-related security events', color: '#17a2b8' },
            { name: 'policy-violation', description: 'Security policy violations', color: '#ffc107' },
        ];
        for (const tag of tags) {
            await this.dataSource.query(`
        INSERT INTO security_event_tags (name, description, color)
        VALUES ($1, $2, $3)
        ON CONFLICT (name) DO UPDATE SET
          description = EXCLUDED.description,
          color = EXCLUDED.color
      `, [tag.name, tag.description, tag.color]);
        }
        this.logger.debug(`Seeded ${tags.length} security event tags`);
    }
    /**
     * Seed system configuration
     */
    async seedSystemConfiguration() {
        this.logger.debug('Seeding system configuration...');
        // Create system configuration table if it doesn't exist
        await this.dataSource.query(`
      CREATE TABLE IF NOT EXISTS system_configuration (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        key VARCHAR(255) UNIQUE NOT NULL,
        value JSONB NOT NULL,
        description TEXT,
        category VARCHAR(100) NOT NULL DEFAULT 'general',
        is_sensitive BOOLEAN NOT NULL DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);
        // Add updated_at trigger
        await this.dataSource.query(`
      CREATE TRIGGER update_system_configuration_updated_at 
      BEFORE UPDATE ON system_configuration 
      FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    `);
        const configurations = [
            {
                key: 'system.version',
                value: { version: '1.0.0', build: 'production' },
                description: 'System version information',
                category: 'system',
                is_sensitive: false
            },
            {
                key: 'security.session_timeout',
                value: { timeout_minutes: 30 },
                description: 'User session timeout in minutes',
                category: 'security',
                is_sensitive: false
            },
            {
                key: 'security.password_policy',
                value: {
                    min_length: 12,
                    require_uppercase: true,
                    require_lowercase: true,
                    require_numbers: true,
                    require_special_chars: true,
                    max_age_days: 90
                },
                description: 'Password policy requirements',
                category: 'security',
                is_sensitive: false
            },
            {
                key: 'alerts.email_notifications',
                value: { enabled: true, smtp_configured: false },
                description: 'Email notification settings',
                category: 'alerts',
                is_sensitive: false
            },
            {
                key: 'scanning.default_schedule',
                value: {
                    vulnerability_scan_interval_hours: 24,
                    network_scan_interval_hours: 6,
                    compliance_scan_interval_hours: 168
                },
                description: 'Default scanning schedules',
                category: 'scanning',
                is_sensitive: false
            },
            {
                key: 'retention.log_retention_days',
                value: {
                    security_events: 365,
                    audit_logs: 2555, // 7 years
                    vulnerability_scans: 90,
                    system_logs: 30
                },
                description: 'Data retention policies',
                category: 'retention',
                is_sensitive: false
            },
        ];
        for (const config of configurations) {
            await this.dataSource.query(`
        INSERT INTO system_configuration (key, value, description, category, is_sensitive)
        VALUES ($1, $2, $3, $4, $5)
        ON CONFLICT (key) DO UPDATE SET
          value = EXCLUDED.value,
          description = EXCLUDED.description,
          category = EXCLUDED.category,
          is_sensitive = EXCLUDED.is_sensitive,
          updated_at = CURRENT_TIMESTAMP
      `, [config.key, JSON.stringify(config.value), config.description, config.category, config.is_sensitive]);
        }
        this.logger.debug(`Seeded ${configurations.length} system configurations`);
    }
    /**
     * Verify production seed integrity
     */
    async verify() {
        this.logger.log('Verifying production seed integrity...');
        try {
            // Verify essential roles exist
            const roleCount = await this.dataSource.query(`
        SELECT COUNT(*) as count FROM roles WHERE is_system_role = true
      `);
            if (parseInt(roleCount[0].count) < 6) {
                this.logger.error('Missing essential system roles');
                return false;
            }
            // Verify admin user exists
            const adminCount = await this.dataSource.query(`
        SELECT COUNT(*) as count FROM users WHERE role IN ('super_admin', 'admin')
      `);
            if (parseInt(adminCount[0].count) === 0) {
                this.logger.error('No admin users found');
                return false;
            }
            // Verify system configuration
            const configCount = await this.dataSource.query(`
        SELECT COUNT(*) as count FROM system_configuration
      `);
            if (parseInt(configCount[0].count) === 0) {
                this.logger.error('No system configuration found');
                return false;
            }
            this.logger.log('Production seed integrity verification passed');
            return true;
        }
        catch (error) {
            this.logger.error('Production seed integrity verification failed', {
                error: error.message,
            });
            return false;
        }
    }
}
exports.ProductionSeeder = ProductionSeeder;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************