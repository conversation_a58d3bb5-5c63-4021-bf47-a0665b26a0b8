f0ef44bcac6dcd276e089fd3047d9f1b
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VulnerabilityFactory = void 0;
const vulnerability_entity_1 = require("../entities/vulnerability/vulnerability.entity");
const cvss_score_value_object_1 = require("../value-objects/threat-indicators/cvss-score.value-object");
const threat_severity_enum_1 = require("../enums/threat-severity.enum");
const confidence_level_enum_1 = require("../enums/confidence-level.enum");
/**
 * Vulnerability Factory
 *
 * Factory class for creating Vulnerability entities with proper validation and defaults.
 * Handles complex vulnerability creation scenarios from various data sources.
 *
 * Key responsibilities:
 * - Create vulnerabilities from scan results
 * - Integrate CVE database information
 * - Apply threat intelligence data
 * - Generate proper risk assessments
 * - Handle vulnerability relationships and correlations
 */
class VulnerabilityFactory {
    /**
     * Create a new Vulnerability with the provided options
     */
    static create(options) {
        // If a specific ID was provided, we need to create the vulnerability directly with that ID
        if (options.id) {
            return VulnerabilityFactory.createWithId(options, options.id);
        }
        return vulnerability_entity_1.Vulnerability.create(options.title, options.description, options.severity, options.category, options.type, options.affectedAssets, options.discovery, {
            cveId: options.cveId,
            cvssScores: options.cvssScores,
            confidence: options.confidence,
            exploitation: options.exploitation,
            tags: options.tags,
            attributes: options.attributes,
        });
    }
    /**
     * Create vulnerability with specific ID (internal helper method)
     */
    static createWithId(options, id) {
        // We need to replicate the logic from Vulnerability.create but with a specific ID
        const riskAssessment = {
            riskScore: 50, // Default risk score, will be calculated properly by the entity
            exploitabilityScore: 50,
            impactScore: 50,
            environmentalFactors: {
                assetCriticality: 50,
                networkExposure: 50,
                dataClassification: 50,
                businessImpact: 50,
            },
            riskFactors: [],
            riskLevel: 'medium',
            businessRisk: {
                financialImpact: 0,
                reputationalImpact: 'medium',
                operationalImpact: 'medium',
                complianceImpact: 'medium',
            },
        };
        const props = {
            cveId: options.cveId,
            title: options.title.trim(),
            description: options.description.trim(),
            severity: options.severity,
            cvssScores: options.cvssScores || [],
            category: options.category.trim(),
            type: options.type.trim(),
            confidence: options.confidence || confidence_level_enum_1.ConfidenceLevel.MEDIUM,
            affectedAssets: options.affectedAssets,
            status: vulnerability_entity_1.VulnerabilityStatus.DISCOVERED,
            discovery: options.discovery,
            exploitation: options.exploitation,
            remediation: {
                status: 'not_started',
                priority: 'medium',
                actions: [],
                workarounds: [],
                timeline: {},
            },
            riskAssessment,
            complianceImpact: [],
            tags: options.tags || [],
            attributes: options.attributes || {},
        };
        return new vulnerability_entity_1.Vulnerability(props, id);
    }
    /**
     * Create vulnerability from CVE database entry
     */
    static fromCVEDatabase(cveEntry, affectedAssets, options) {
        const cvssScores = [];
        // Add CVSS v3.1 score if available
        if (cveEntry.cvssV3) {
            cvssScores.push(cvss_score_value_object_1.CVSSScore.createV3_1(cveEntry.cvssV3.baseScore, cveEntry.cvssV3.vectorString, {
                exploitabilityScore: cveEntry.cvssV3.exploitabilityScore,
                impactScore: cveEntry.cvssV3.impactScore,
                source: 'NVD',
                calculatedAt: cveEntry.lastModifiedDate,
            }));
        }
        // Add CVSS v2 score if available
        if (cveEntry.cvssV2) {
            cvssScores.push(cvss_score_value_object_1.CVSSScore.create(cveEntry.cvssV2.baseScore, cvss_score_value_object_1.CVSSVersion.V2, cveEntry.cvssV2.vectorString, {
                source: 'NVD',
                calculatedAt: cveEntry.lastModifiedDate,
            }));
        }
        // Determine severity from CVSS scores
        const severity = VulnerabilityFactory.mapCVSSToThreatSeverity(cvssScores);
        // Build category from CWE mappings
        const category = VulnerabilityFactory.inferCategoryFromCWE(cveEntry.cweIds);
        // Build type from affected products
        const type = VulnerabilityFactory.inferTypeFromProducts(cveEntry.affectedProducts);
        // Create discovery information
        const discovery = {
            method: 'threat_intelligence',
            source: 'CVE Database',
            discoveredAt: cveEntry.publishedDate,
            discoveredBy: 'NVD',
            details: `CVE ${cveEntry.cveId} from National Vulnerability Database`,
        };
        return VulnerabilityFactory.create({
            cveId: cveEntry.cveId,
            title: cveEntry.title,
            description: cveEntry.description,
            severity,
            category,
            type,
            confidence: confidence_level_enum_1.ConfidenceLevel.HIGH, // CVE entries are generally high confidence
            cvssScores,
            affectedAssets,
            discovery,
            tags: ['cve', 'nvd', ...cveEntry.cweIds.map(cwe => `cwe-${cwe}`)],
            attributes: {
                cveId: cveEntry.cveId,
                publishedDate: cveEntry.publishedDate.toISOString(),
                lastModifiedDate: cveEntry.lastModifiedDate.toISOString(),
                references: cveEntry.references,
                cweIds: cveEntry.cweIds,
                affectedProducts: cveEntry.affectedProducts,
                configurations: cveEntry.configurations,
            },
            ...options,
        });
    }
    /**
     * Create vulnerability from scan result
     */
    static fromScanResult(scanResult, options) {
        // Map scan result to vulnerability asset
        const affectedAsset = {
            assetId: scanResult.asset.id,
            assetName: scanResult.asset.name,
            assetType: scanResult.asset.type,
            criticality: 'medium', // Default, should be determined by asset management
            affectedComponents: scanResult.service ? [{
                    name: scanResult.service.service,
                    version: scanResult.service.version || 'unknown',
                    type: 'software',
                }] : [],
            exposure: 'internal', // Default, should be determined by network topology
            businessImpact: [],
        };
        // Create CVSS score if available
        const cvssScores = [];
        if (scanResult.cvssScore && scanResult.cvssVector) {
            try {
                cvssScores.push(cvss_score_value_object_1.CVSSScore.fromVectorString(scanResult.cvssVector, scanResult.scanner.name));
            }
            catch (error) {
                // If vector parsing fails, create basic score
                cvssScores.push(cvss_score_value_object_1.CVSSScore.createV3_1(scanResult.cvssScore, 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:N', // Placeholder
                { source: scanResult.scanner.name }));
            }
        }
        // Map scanner severity to threat severity
        const severity = VulnerabilityFactory.mapScannerSeverity(scanResult.severity);
        // Infer category and type from scanner data
        const category = VulnerabilityFactory.inferCategoryFromScanResult(scanResult);
        const type = VulnerabilityFactory.inferTypeFromScanResult(scanResult);
        // Create discovery information
        const discovery = {
            method: 'automated_scan',
            source: scanResult.scanner.name,
            discoveredAt: scanResult.scanTimestamp,
            discoveredBy: scanResult.scanner.name,
            details: `Discovered by ${scanResult.scanner.name} v${scanResult.scanner.version}`,
            scannerInfo: {
                name: scanResult.scanner.name,
                version: scanResult.scanner.version,
                ruleId: scanResult.scanner.ruleId,
                confidence: scanResult.scanner.confidence,
            },
        };
        // Map scanner confidence to domain confidence
        const confidence = VulnerabilityFactory.mapScannerConfidence(scanResult.scanner.confidence);
        return VulnerabilityFactory.create({
            cveId: scanResult.cveId,
            title: scanResult.name,
            description: scanResult.description,
            severity,
            category,
            type,
            confidence,
            cvssScores,
            affectedAssets: [affectedAsset],
            discovery,
            tags: ['scan-result', scanResult.scanner.name.toLowerCase()],
            attributes: {
                scannerId: scanResult.scannerId,
                scanTimestamp: scanResult.scanTimestamp.toISOString(),
                scanner: scanResult.scanner,
                service: scanResult.service,
                metadata: scanResult.metadata,
            },
            ...options,
        });
    }
    /**
     * Create vulnerability with threat intelligence data
     */
    static withThreatIntelligence(baseVulnerability, threatIntel) {
        // Create exploitation information from threat intelligence
        const exploitation = {
            status: threatIntel.exploitationStatus,
            difficulty: VulnerabilityFactory.inferExploitationDifficulty(threatIntel),
            availableExploits: threatIntel.exploits.map(exploit => ({
                name: exploit.name,
                type: exploit.type,
                source: exploit.source,
                reliability: exploit.reliability,
                discovered: exploit.publishedDate,
            })),
            timeline: VulnerabilityFactory.buildExploitationTimeline(threatIntel),
            attackVectors: threatIntel.attackPatterns?.map(pattern => pattern.technique) || [],
            prerequisites: [],
        };
        // Update tags with threat intelligence information
        const updatedTags = [
            ...baseVulnerability.tags,
            'threat-intelligence',
            threatIntel.source.toLowerCase(),
        ];
        if (threatIntel.exploitationStatus === 'active_exploitation') {
            updatedTags.push('actively-exploited');
        }
        if (threatIntel.threatActorActivity && threatIntel.threatActorActivity.length > 0) {
            updatedTags.push('threat-actor-activity');
        }
        // Update attributes with threat intelligence data
        const updatedAttributes = {
            ...baseVulnerability.attributes,
            threatIntelligence: {
                source: threatIntel.source,
                exploitationStatus: threatIntel.exploitationStatus,
                exploits: threatIntel.exploits,
                threatActorActivity: threatIntel.threatActorActivity,
                attackPatterns: threatIntel.attackPatterns,
                timestamp: threatIntel.timestamp.toISOString(),
            },
        };
        // Create new vulnerability with updated information
        return VulnerabilityFactory.create({
            id: baseVulnerability.id,
            cveId: baseVulnerability.cveId,
            title: baseVulnerability.title,
            description: baseVulnerability.description,
            severity: baseVulnerability.severity,
            category: baseVulnerability.category,
            type: baseVulnerability.type,
            confidence: baseVulnerability.confidence,
            cvssScores: baseVulnerability.cvssScores,
            affectedAssets: baseVulnerability.affectedAssets,
            discovery: baseVulnerability.discovery,
            exploitation,
            tags: updatedTags,
            attributes: updatedAttributes,
        });
    }
    /**
     * Create critical vulnerability alert
     */
    static createCriticalAlert(title, description, cveId, affectedAssets, options) {
        const discovery = {
            method: 'external_report',
            source: 'Security Alert',
            discoveredAt: new Date(),
            discoveredBy: 'Security Team',
            details: 'Critical vulnerability alert requiring immediate attention',
        };
        return VulnerabilityFactory.create({
            cveId,
            title,
            description,
            severity: threat_severity_enum_1.ThreatSeverity.CRITICAL,
            category: 'critical-alert',
            type: 'security-alert',
            confidence: confidence_level_enum_1.ConfidenceLevel.HIGH,
            affectedAssets,
            discovery,
            tags: ['critical', 'alert', 'immediate-response'],
            attributes: {
                alertType: 'critical',
                requiresImmediateAttention: true,
            },
            ...options,
        });
    }
    /**
     * Create zero-day vulnerability
     */
    static createZeroDay(title, description, affectedAssets, discoveryMethod, options) {
        const discovery = {
            method: discoveryMethod,
            source: 'Zero-day Discovery',
            discoveredAt: new Date(),
            discoveredBy: 'Security Research Team',
            details: 'Zero-day vulnerability with no known CVE identifier',
        };
        const exploitation = {
            status: 'active_exploitation',
            difficulty: 'medium',
            availableExploits: [],
            attackVectors: [],
            prerequisites: [],
        };
        return VulnerabilityFactory.create({
            title,
            description,
            severity: threat_severity_enum_1.ThreatSeverity.CRITICAL,
            category: 'zero-day',
            type: 'unknown-vulnerability',
            confidence: confidence_level_enum_1.ConfidenceLevel.MEDIUM,
            affectedAssets,
            discovery,
            exploitation,
            tags: ['zero-day', 'no-cve', 'active-exploitation'],
            attributes: {
                zeroDay: true,
                noCVE: true,
                discoveryMethod,
            },
            ...options,
        });
    }
    // Helper methods for data mapping and inference
    static mapCVSSToThreatSeverity(cvssScores) {
        if (cvssScores.length === 0) {
            return threat_severity_enum_1.ThreatSeverity.UNKNOWN;
        }
        const maxScore = Math.max(...cvssScores.map(score => score.baseScore));
        if (maxScore >= 9.0)
            return threat_severity_enum_1.ThreatSeverity.CRITICAL;
        if (maxScore >= 7.0)
            return threat_severity_enum_1.ThreatSeverity.HIGH;
        if (maxScore >= 4.0)
            return threat_severity_enum_1.ThreatSeverity.MEDIUM;
        if (maxScore >= 0.1)
            return threat_severity_enum_1.ThreatSeverity.LOW;
        return threat_severity_enum_1.ThreatSeverity.UNKNOWN;
    }
    static mapScannerSeverity(severity) {
        const sev = severity.toLowerCase();
        if (sev.includes('critical') || sev.includes('fatal'))
            return threat_severity_enum_1.ThreatSeverity.CRITICAL;
        if (sev.includes('high') || sev.includes('severe'))
            return threat_severity_enum_1.ThreatSeverity.HIGH;
        if (sev.includes('medium') || sev.includes('moderate'))
            return threat_severity_enum_1.ThreatSeverity.MEDIUM;
        if (sev.includes('low') || sev.includes('minor'))
            return threat_severity_enum_1.ThreatSeverity.LOW;
        return threat_severity_enum_1.ThreatSeverity.UNKNOWN;
    }
    static mapScannerConfidence(confidence) {
        if (confidence >= 95)
            return confidence_level_enum_1.ConfidenceLevel.CONFIRMED;
        if (confidence >= 85)
            return confidence_level_enum_1.ConfidenceLevel.VERY_HIGH;
        if (confidence >= 70)
            return confidence_level_enum_1.ConfidenceLevel.HIGH;
        if (confidence >= 50)
            return confidence_level_enum_1.ConfidenceLevel.MEDIUM;
        if (confidence >= 30)
            return confidence_level_enum_1.ConfidenceLevel.LOW;
        return confidence_level_enum_1.ConfidenceLevel.VERY_LOW;
    }
    static inferCategoryFromCWE(cweIds) {
        // Map common CWE categories
        const categoryMap = {
            'CWE-79': 'injection',
            'CWE-89': 'injection',
            'CWE-78': 'injection',
            'CWE-94': 'injection',
            'CWE-287': 'authentication',
            'CWE-285': 'authorization',
            'CWE-284': 'access_control',
            'CWE-200': 'information_disclosure',
            'CWE-119': 'memory_corruption',
            'CWE-120': 'memory_corruption',
            'CWE-787': 'memory_corruption',
            'CWE-22': 'path_traversal',
            'CWE-352': 'csrf',
            'CWE-434': 'file_upload',
        };
        for (const cweId of cweIds) {
            if (categoryMap[cweId]) {
                return categoryMap[cweId];
            }
        }
        return 'general';
    }
    static inferTypeFromProducts(products) {
        if (products.length === 0)
            return 'unknown';
        const product = products[0];
        const productName = product.product.toLowerCase();
        if (productName.includes('windows') || productName.includes('linux') || productName.includes('macos')) {
            return 'operating_system';
        }
        if (productName.includes('apache') || productName.includes('nginx') || productName.includes('iis')) {
            return 'web_server';
        }
        if (productName.includes('mysql') || productName.includes('postgresql') || productName.includes('oracle')) {
            return 'database';
        }
        if (productName.includes('chrome') || productName.includes('firefox') || productName.includes('safari')) {
            return 'browser';
        }
        return 'application';
    }
    static inferCategoryFromScanResult(scanResult) {
        const name = scanResult.name.toLowerCase();
        const description = scanResult.description.toLowerCase();
        if (name.includes('sql') || description.includes('sql injection'))
            return 'injection';
        if (name.includes('xss') || description.includes('cross-site scripting'))
            return 'injection';
        if (name.includes('rce') || description.includes('remote code execution'))
            return 'remote_code_execution';
        if (name.includes('auth') || description.includes('authentication'))
            return 'authentication';
        if (name.includes('privilege') || description.includes('escalation'))
            return 'privilege_escalation';
        if (name.includes('buffer') || description.includes('overflow'))
            return 'memory_corruption';
        if (name.includes('path') || description.includes('traversal'))
            return 'path_traversal';
        if (name.includes('csrf') || description.includes('cross-site request'))
            return 'csrf';
        return 'general';
    }
    static inferTypeFromScanResult(scanResult) {
        if (scanResult.service) {
            return `${scanResult.service.service}_vulnerability`;
        }
        const assetType = scanResult.asset.type.toLowerCase();
        return `${assetType}_vulnerability`;
    }
    static inferExploitationDifficulty(threatIntel) {
        const publicExploits = threatIntel.exploits.filter(e => e.type === 'public').length;
        const highReliabilityExploits = threatIntel.exploits.filter(e => e.reliability >= 80).length;
        if (publicExploits > 0 && highReliabilityExploits > 0)
            return 'low';
        if (publicExploits > 0 || highReliabilityExploits > 0)
            return 'medium';
        return 'high';
    }
    static buildExploitationTimeline(threatIntel) {
        const exploitDates = threatIntel.exploits.map(e => e.publishedDate);
        const actorDates = threatIntel.threatActorActivity?.flatMap(a => [a.firstObserved, a.lastObserved]) || [];
        return {
            firstExploitPublished: exploitDates.length > 0 ? new Date(Math.min(...exploitDates.map(d => d.getTime()))) : undefined,
            firstExploitationObserved: actorDates.length > 0 ? new Date(Math.min(...actorDates.map(d => d.getTime()))) : undefined,
            lastExploitationObserved: actorDates.length > 0 ? new Date(Math.max(...actorDates.map(d => d.getTime()))) : undefined,
        };
    }
}
exports.VulnerabilityFactory = VulnerabilityFactory;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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