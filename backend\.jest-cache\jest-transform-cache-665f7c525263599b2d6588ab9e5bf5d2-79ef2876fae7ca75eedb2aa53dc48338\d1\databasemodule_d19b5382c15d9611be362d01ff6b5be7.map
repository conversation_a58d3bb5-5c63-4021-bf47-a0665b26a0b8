{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\database\\database.module.ts", "mappings": ";;;;;;;;;;;;;AAAA,2CAAwC;AACxC,6CAAgD;AAChD,2CAA6D;AAC7D,yDAAqD;AACrD,6DAAyD;AACzD,+DAA2D;AAC3D,qEAAgE;AAEhE;;;;;;;;;;;;GAYG;AAkFI,IAAM,cAAc,GAApB,MAAM,cAAc;IACzB,YAA6B,eAAgC;QAAhC,oBAAe,GAAf,eAAe,CAAiB;IAAG,CAAC;IAEjE;;;OAGG;IACH,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE,CAAC;YAC7C,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;QAChE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YACpH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe;QACnB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,CAAC;YAC9C,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;QAC5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QACjH,CAAC;IACH,CAAC;CACF,CAAA;AA5BY,wCAAc;yBAAd,cAAc;IAjF1B,IAAA,eAAM,EAAC;QACN,OAAO,EAAE;YACP,uBAAa,CAAC,YAAY,CAAC;gBACzB,OAAO,EAAE,CAAC,qBAAY,CAAC;gBACvB,MAAM,EAAE,CAAC,sBAAa,CAAC;gBACvB,UAAU,EAAE,KAAK,EAAE,aAA4B,EAAE,EAAE;oBACjD,MAAM,cAAc,GAAG,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;oBAErD,OAAO;wBACL,GAAG,cAAc;wBACjB,mCAAmC;wBACnC,gBAAgB,EAAE,IAAI;wBACtB,aAAa,EAAE,CAAC;wBAChB,UAAU,EAAE,IAAI;wBAEhB,gCAAgC;wBAChC,KAAK,EAAE;4BACL,GAAG,cAAc,CAAC,KAAK;4BACvB,gDAAgD;4BAChD,GAAG,EAAE,cAAc,CAAC,KAAK,EAAE,GAAG,IAAI,GAAG;4BACrC,GAAG,EAAE,CAAC;4BACN,OAAO,EAAE,KAAK;4BACd,IAAI,EAAE,KAAK;4BACX,KAAK,EAAE,IAAI;4BACX,iBAAiB,EAAE,IAAI;yBACxB;wBAED,6CAA6C;wBAC7C,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC;4BAC/C,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;4BAC3C,CAAC,OAAO,EAAE,MAAM,CAAC;wBAEnB,4BAA4B;wBAC5B,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,CAAC,CAAC,CAAC;4BAC7C,IAAI,EAAE,OAAO;4BACb,OAAO,EAAE;gCACP,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,WAAW;gCAC3C,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,IAAI;gCAClD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,SAAS;gCACjD,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC;6BAC5C;4BACD,QAAQ,EAAE,KAAK,EAAE,aAAa;yBAC/B,CAAC,CAAC,CAAC,KAAK;wBAET,0BAA0B;wBAC1B,aAAa,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM;wBAC9C,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM;wBAE3C,uBAAuB;wBACvB,QAAQ,EAAE;4BACR,SAAS,GAAG,6BAA6B;4BACzC,SAAS,GAAG,wDAAwD;yBACrE;wBAED,0BAA0B;wBAC1B,UAAU,EAAE;4BACV,SAAS,GAAG,wBAAwB;yBACrC;wBAED,2BAA2B;wBAC3B,WAAW,EAAE;4BACX,SAAS,GAAG,yBAAyB;yBACtC;qBACF,CAAC;gBACJ,CAAC;aACF,CAAC;SACH;QACD,SAAS,EAAE;YACT,kCAAe;YACf,sCAAiB;YACjB,wCAAkB;YAClB,uCAAiB;SAClB;QACD,OAAO,EAAE;YACP,uBAAa;YACb,kCAAe;YACf,sCAAiB;YACjB,wCAAkB;YAClB,uCAAiB;SAClB;KACF,CAAC;yDAE8C,kCAAe,oBAAf,kCAAe;GADlD,cAAc,CA4B1B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\database\\database.module.ts"], "sourcesContent": ["import { Module } from '@nestjs/common';\r\nimport { TypeOrmModule } from '@nestjs/typeorm';\r\nimport { ConfigModule, ConfigService } from '@nestjs/config';\r\nimport { DatabaseService } from './database.service';\r\nimport { ConnectionFactory } from './connection.factory';\r\nimport { TransactionManager } from './transaction.manager';\r\nimport { SeedRunnerService } from './seeds/seed-runner.service';\r\n\r\n/**\r\n * Database module that provides TypeORM configuration and database services\r\n * Supports multiple database connections and transaction management\r\n * \r\n * Features:\r\n * - Environment-specific database configuration\r\n * - Connection pooling and management\r\n * - Transaction management with rollback support\r\n * - Database health monitoring\r\n * - Migration and seeding support\r\n * \r\n * @module DatabaseModule\r\n */\r\n@Module({\r\n  imports: [\r\n    TypeOrmModule.forRootAsync({\r\n      imports: [ConfigModule],\r\n      inject: [ConfigService],\r\n      useFactory: async (configService: ConfigService) => {\r\n        const databaseConfig = configService.get('database');\r\n        \r\n        return {\r\n          ...databaseConfig,\r\n          // Additional runtime configuration\r\n          autoLoadEntities: true,\r\n          retryAttempts: 3,\r\n          retryDelay: 3000,\r\n          \r\n          // Connection pool configuration\r\n          extra: {\r\n            ...databaseConfig.extra,\r\n            // Ensure connection pool is properly configured\r\n            max: databaseConfig.extra?.max || 100,\r\n            min: 5,\r\n            acquire: 30000,\r\n            idle: 10000,\r\n            evict: 1000,\r\n            handleDisconnects: true,\r\n          },\r\n          \r\n          // Logging configuration based on environment\r\n          logging: process.env.NODE_ENV === 'development' ? \r\n            ['query', 'error', 'warn', 'info', 'log'] : \r\n            ['error', 'warn'],\r\n          \r\n          // Performance optimizations\r\n          cache: process.env.NODE_ENV === 'production' ? {\r\n            type: 'redis',\r\n            options: {\r\n              host: process.env.REDIS_HOST || 'localhost',\r\n              port: parseInt(process.env.REDIS_PORT, 10) || 6379,\r\n              password: process.env.REDIS_PASSWORD || undefined,\r\n              db: parseInt(process.env.REDIS_DB, 10) || 1,\r\n            },\r\n            duration: 30000, // 30 seconds\r\n          } : false,\r\n          \r\n          // Migration configuration\r\n          migrationsRun: process.env.NODE_ENV !== 'test',\r\n          dropSchema: process.env.NODE_ENV === 'test',\r\n          \r\n          // Entity configuration\r\n          entities: [\r\n            __dirname + '/../../**/*.entity{.ts,.js}',\r\n            __dirname + '/../../modules/**/domain/entities/**/*.entity{.ts,.js}',\r\n          ],\r\n          \r\n          // Migration configuration\r\n          migrations: [\r\n            __dirname + '/migrations/*{.ts,.js}',\r\n          ],\r\n          \r\n          // Subscriber configuration\r\n          subscribers: [\r\n            __dirname + '/subscribers/*{.ts,.js}',\r\n          ],\r\n        };\r\n      },\r\n    }),\r\n  ],\r\n  providers: [\r\n    DatabaseService,\r\n    ConnectionFactory,\r\n    TransactionManager,\r\n    SeedRunnerService,\r\n  ],\r\n  exports: [\r\n    TypeOrmModule,\r\n    DatabaseService,\r\n    ConnectionFactory,\r\n    TransactionManager,\r\n    SeedRunnerService,\r\n  ],\r\n})\r\nexport class DatabaseModule {\r\n  constructor(private readonly databaseService: DatabaseService) {}\r\n\r\n  /**\r\n   * Initialize database module\r\n   * Performs health checks and initial setup\r\n   */\r\n  async onModuleInit(): Promise<void> {\r\n    try {\r\n      await this.databaseService.checkConnection();\r\n      console.log('✅ Database connection established successfully');\r\n    } catch (error) {\r\n      console.error('❌ Failed to establish database connection:', error instanceof Error ? error.message : String(error));\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Cleanup database connections on module destroy\r\n   */\r\n  async onModuleDestroy(): Promise<void> {\r\n    try {\r\n      await this.databaseService.closeConnections();\r\n      console.log('✅ Database connections closed successfully');\r\n    } catch (error) {\r\n      console.error('❌ Error closing database connections:', error instanceof Error ? error.message : String(error));\r\n    }\r\n  }\r\n}\r\n"], "version": 3}