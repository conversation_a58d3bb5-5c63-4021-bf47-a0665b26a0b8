{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\event.specification.ts", "mappings": ";;;AAAA,6DAA8E;AAE9E,8DAAqD;AAErD,kEAAyD;AAIzD;;;;;GAKG;AACH,MAAsB,kBAAmB,SAAQ,iCAAwB;IACvE;;OAEG;IACO,cAAc,CAAC,KAAY,EAAE,KAAkB;QACvD,OAAO,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACO,kBAAkB,CAAC,KAAY,EAAE,UAA2B;QACpE,OAAO,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACO,gBAAgB,CAAC,KAAY,EAAE,QAAuB;QAC9D,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACO,0BAA0B,CAAC,KAAY,EAAE,QAAiC;QAClF,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACO,oBAAoB,CAAC,KAAY,EAAE,WAA8B;QACzE,OAAO,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACO,SAAS,CAAC,KAAY,EAAE,IAAc;QAC9C,OAAO,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACO,UAAU,CAAC,KAAY,EAAE,IAAc;QAC/C,OAAO,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACO,gBAAgB,CAAC,KAAY,EAAE,QAAiB,EAAE,QAAiB;QAC3E,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;QAE3B,IAAI,QAAQ,KAAK,SAAS,IAAI,GAAG,GAAG,QAAQ,EAAE,CAAC;YAC7C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,QAAQ,KAAK,SAAS,IAAI,GAAG,GAAG,QAAQ,EAAE,CAAC;YAC7C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACO,sBAAsB,CAAC,KAAY,EAAE,QAAiB,EAAE,QAAiB;QACjF,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;QAElC,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;YAC5B,OAAO,QAAQ,KAAK,SAAS,CAAC,CAAC,oDAAoD;QACrF,CAAC;QAED,IAAI,QAAQ,KAAK,SAAS,IAAI,SAAS,GAAG,QAAQ,EAAE,CAAC;YACnD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,QAAQ,KAAK,SAAS,IAAI,SAAS,GAAG,QAAQ,EAAE,CAAC;YACnD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAvFD,gDAuFC;AAED;;;;GAIG;AACH,MAAa,8BAA+B,SAAQ,kBAAkB;IACpE,aAAa,CAAC,KAAY;QACxB,OAAO,KAAK,CAAC,cAAc,EAAE,CAAC;IAChC,CAAC;IAED,cAAc;QACZ,OAAO,qCAAqC,CAAC;IAC/C,CAAC;CACF;AARD,wEAQC;AAED;;;;GAIG;AACH,MAAa,0BAA2B,SAAQ,kBAAkB;IAChE,aAAa,CAAC,KAAY;QACxB,OAAO,KAAK,CAAC,UAAU,EAAE,CAAC;IAC5B,CAAC;IAED,cAAc;QACZ,OAAO,6BAA6B,CAAC;IACvC,CAAC;CACF;AARD,gEAQC;AAED;;;;GAIG;AACH,MAAa,wBAAyB,SAAQ,kBAAkB;IAC9D,aAAa,CAAC,KAAY;QACxB,OAAO,KAAK,CAAC,QAAQ,EAAE,CAAC;IAC1B,CAAC;IAED,cAAc;QACZ,OAAO,0CAA0C,CAAC;IACpD,CAAC;CACF;AARD,4DAQC;AAED;;;;GAIG;AACH,MAAa,0BAA2B,SAAQ,kBAAkB;IAChE,aAAa,CAAC,KAAY;QACxB,OAAO,KAAK,CAAC,UAAU,EAAE,CAAC;IAC5B,CAAC;IAED,cAAc;QACZ,OAAO,mCAAmC,CAAC;IAC7C,CAAC;CACF;AARD,gEAQC;AAED;;;;GAIG;AACH,MAAa,wBAAyB,SAAQ,kBAAkB;IAC9D,YAA6B,WAAmB,OAAO;QACrD,KAAK,EAAE,CAAC;QADmB,aAAQ,GAAR,QAAQ,CAAkB;IAEvD,CAAC;IAED,aAAa,CAAC,KAAY;QACxB,OAAO,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACvC,CAAC;IAED,cAAc;QACZ,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAClD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,GAAG,KAAK,CAAC,CAAC;QAC9D,OAAO,yBAAyB,KAAK,KAAK,OAAO,GAAG,CAAC;IACvD,CAAC;CACF;AAdD,4DAcC;AAED;;;;GAIG;AACH,MAAa,uBAAwB,SAAQ,kBAAkB;IAC7D,YAA6B,cAAsB,QAAQ;QACzD,KAAK,EAAE,CAAC;QADmB,gBAAW,GAAX,WAAW,CAAmB;IAE3D,CAAC;IAED,aAAa,CAAC,KAAY;QACxB,OAAO,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACzC,CAAC;IAED,cAAc;QACZ,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,CAAC;QACrD,OAAO,uBAAuB,KAAK,QAAQ,CAAC;IAC9C,CAAC;CACF;AAbD,0DAaC;AAED;;;;GAIG;AACH,MAAa,sBAAuB,SAAQ,kBAAkB;IAC5D,YAA6B,KAAkB;QAC7C,KAAK,EAAE,CAAC;QADmB,UAAK,GAAL,KAAK,CAAa;IAE/C,CAAC;IAED,aAAa,CAAC,KAAY;QACxB,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IAChD,CAAC;IAED,cAAc;QACZ,OAAO,yBAAyB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAC1D,CAAC;CACF;AAZD,wDAYC;AAED;;;;GAIG;AACH,MAAa,0BAA2B,SAAQ,kBAAkB;IAChE,YAA6B,UAA2B;QACtD,KAAK,EAAE,CAAC;QADmB,eAAU,GAAV,UAAU,CAAiB;IAExD,CAAC;IAED,aAAa,CAAC,KAAY;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IACzD,CAAC;IAED,cAAc;QACZ,OAAO,6BAA6B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IACnE,CAAC;CACF;AAZD,gEAYC;AAED;;;;GAIG;AACH,MAAa,wBAAyB,SAAQ,kBAAkB;IAC9D,YAA6B,QAAuB;QAClD,KAAK,EAAE,CAAC;QADmB,aAAQ,GAAR,QAAQ,CAAe;IAEpD,CAAC;IAED,aAAa,CAAC,KAAY;QACxB,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACrD,CAAC;IAED,cAAc;QACZ,OAAO,2BAA2B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAC/D,CAAC;CACF;AAZD,4DAYC;AAED;;;;GAIG;AACH,MAAa,kCAAmC,SAAQ,kBAAkB;IACxE,YAA6B,QAAiC;QAC5D,KAAK,EAAE,CAAC;QADmB,aAAQ,GAAR,QAAQ,CAAyB;IAE9D,CAAC;IAED,aAAa,CAAC,KAAY;QACxB,OAAO,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC/D,CAAC;IAED,cAAc;QACZ,OAAO,sCAAsC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAC1E,CAAC;CACF;AAZD,gFAYC;AAED;;;;GAIG;AACH,MAAa,4BAA6B,SAAQ,kBAAkB;IAClE,YAA6B,WAA8B;QACzD,KAAK,EAAE,CAAC;QADmB,gBAAW,GAAX,WAAW,CAAmB;IAE3D,CAAC;IAED,aAAa,CAAC,KAAY;QACxB,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAC5D,CAAC;IAED,cAAc;QACZ,OAAO,gCAAgC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IACvE,CAAC;CACF;AAZD,oEAYC;AAED;;;;GAIG;AACH,MAAa,qBAAsB,SAAQ,kBAAkB;IAC3D,YACmB,IAAc,EACd,aAAsB,KAAK;QAE5C,KAAK,EAAE,CAAC;QAHS,SAAI,GAAJ,IAAI,CAAU;QACd,eAAU,GAAV,UAAU,CAAiB;IAG9C,CAAC;IAED,aAAa,CAAC,KAAY;QACxB,OAAO,IAAI,CAAC,UAAU;YACpB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC;YACnC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAED,cAAc;QACZ,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;QACjD,OAAO,aAAa,QAAQ,mBAAmB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IACxE,CAAC;CACF;AAlBD,sDAkBC;AAED;;;;GAIG;AACH,MAAa,gCAAiC,SAAQ,kBAAkB;IACtE,YACmB,QAAiB,EACjB,QAAiB;QAElC,KAAK,EAAE,CAAC;QAHS,aAAQ,GAAR,QAAQ,CAAS;QACjB,aAAQ,GAAR,QAAQ,CAAS;IAGpC,CAAC;IAED,aAAa,CAAC,KAAY;QACxB,OAAO,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC1E,CAAC;IAED,cAAc;QACZ,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC/D,OAAO,+BAA+B,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC7E,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,gCAAgC,IAAI,CAAC,QAAQ,EAAE,CAAC;QACzD,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,+BAA+B,IAAI,CAAC,QAAQ,EAAE,CAAC;QACxD,CAAC;QACD,OAAO,0BAA0B,CAAC;IACpC,CAAC;CACF;AAtBD,4EAsBC;AAED;;;;GAIG;AACH,MAAa,0BAA2B,SAAQ,kBAAkB;IAChE,YACmB,QAAiB,EACjB,QAAiB;QAElC,KAAK,EAAE,CAAC;QAHS,aAAQ,GAAR,QAAQ,CAAS;QACjB,aAAQ,GAAR,QAAQ,CAAS;IAGpC,CAAC;IAED,aAAa,CAAC,KAAY;QACxB,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACpE,CAAC;IAED,cAAc;QACZ,MAAM,UAAU,GAAG,CAAC,EAAU,EAAE,EAAE;YAChC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC,CAAC;YACvC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,KAAK,CAAC,CAAC;YACnD,OAAO,GAAG,KAAK,KAAK,OAAO,GAAG,CAAC;QACjC,CAAC,CAAC;QAEF,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC/D,OAAO,wBAAwB,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC9F,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,qBAAqB,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QAC9D,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,oBAAoB,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QAC7D,CAAC;QACD,OAAO,mBAAmB,CAAC;IAC7B,CAAC;CACF;AA5BD,gEA4BC;AAED;;;;GAIG;AACH,MAAa,6BAA8B,SAAQ,kBAAkB;IACnE,YACmB,aAAsB,EACtB,aAA8B,EAC9B,iBAA0B,IAAI;QAE/C,KAAK,EAAE,CAAC;QAJS,kBAAa,GAAb,aAAa,CAAS;QACtB,kBAAa,GAAb,aAAa,CAAiB;QAC9B,mBAAc,GAAd,cAAc,CAAgB;IAGjD,CAAC;IAED,aAAa,CAAC,KAAY;QACxB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,OAAO,KAAK,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC;QACpD,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,OAAO,KAAK,CAAC,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC;QAClE,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,OAAO,KAAK,CAAC,aAAa,KAAK,SAAS,IAAI,KAAK,CAAC,aAAa,KAAK,SAAS,CAAC;QAChF,CAAC;aAAM,CAAC;YACN,OAAO,KAAK,CAAC,aAAa,KAAK,SAAS,IAAI,KAAK,CAAC,aAAa,KAAK,SAAS,CAAC;QAChF,CAAC;IACH,CAAC;IAED,cAAc;QACZ,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,OAAO,6BAA6B,IAAI,CAAC,aAAa,EAAE,CAAC;QAC3D,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,OAAO,wBAAwB,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,EAAE,CAAC;QACjE,CAAC;QAED,OAAO,IAAI,CAAC,cAAc;YACxB,CAAC,CAAC,mCAAmC;YACrC,CAAC,CAAC,sCAAsC,CAAC;IAC7C,CAAC;CACF;AAtCD,sEAsCC;AAED;;;;GAIG;AACH,MAAa,kCAAmC,SAAQ,kBAAkB;IACxE,aAAa,CAAC,KAAY;QACxB,OAAO,KAAK,CAAC,mBAAmB,EAAE,CAAC;IACrC,CAAC;IAED,cAAc;QACZ,OAAO,6BAA6B,CAAC;IACvC,CAAC;CACF;AARD,gFAQC;AAED;;;;GAIG;AACH,MAAa,gCAAiC,SAAQ,kBAAkB;IACtE,aAAa,CAAC,KAAY;QACxB,OAAO,KAAK,CAAC,sBAAsB,EAAE,CAAC;IACxC,CAAC;IAED,cAAc;QACZ,OAAO,gDAAgD,CAAC;IAC1D,CAAC;CACF;AARD,4EAQC;AAED;;;;GAIG;AACH,MAAa,8BAA+B,SAAQ,kBAAkB;IACpE,aAAa,CAAC,KAAY;QACxB,OAAO,KAAK,CAAC,cAAc,EAAE;YACtB,KAAK,CAAC,UAAU,EAAE;YAClB,KAAK,CAAC,mBAAmB,EAAE;YAC3B,KAAK,CAAC,sBAAsB,EAAE;YAC9B,KAAK,CAAC,MAAM,KAAK,+BAAW,CAAC,aAAa,CAAC;IACpD,CAAC;IAED,cAAc;QACZ,OAAO,sGAAsG,CAAC;IAChH,CAAC;CACF;AAZD,wEAYC;AAED;;;;GAIG;AACH,MAAa,0BAA2B,SAAQ,kBAAkB;IAYhE,aAAa,CAAC,KAAY;QACxB,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,0BAA0B,CAAC,oBAAoB,CAAC;YAC3E,KAAK,CAAC,cAAc,EAAE;YACtB,KAAK,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;IACxC,CAAC;IAED,cAAc;QACZ,OAAO,8EAA8E,CAAC;IACxF,CAAC;;AApBH,gEAqBC;AApByB,+CAAoB,GAAG;IAC7C,2BAAS,CAAC,eAAe;IACzB,2BAAS,CAAC,gBAAgB;IAC1B,2BAAS,CAAC,sBAAsB;IAChC,2BAAS,CAAC,qBAAqB;IAC/B,2BAAS,CAAC,kBAAkB;IAC5B,2BAAS,CAAC,WAAW;IACrB,2BAAS,CAAC,WAAW;IACrB,2BAAS,CAAC,YAAY;CACvB,CAAC;AAaJ;;;;GAIG;AACH,MAAa,yBAAyB;IAAtC;QACU,mBAAc,GAAyB,EAAE,CAAC;IAkJpD,CAAC;IAhJC;;OAEG;IACH,YAAY;QACV,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,8BAA8B,EAAE,CAAC,CAAC;QAC/D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,0BAA0B,EAAE,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,wBAAwB,EAAE,CAAC,CAAC;QACzD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,0BAA0B,EAAE,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAmB,OAAO;QAC/B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,wBAAwB,CAAC,QAAQ,CAAC,CAAC,CAAC;QACjE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,GAAG,KAAkB;QAC3B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,sBAAsB,CAAC,KAAK,CAAC,CAAC,CAAC;QAC5D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,GAAG,UAA2B;QAC3C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,0BAA0B,CAAC,UAAU,CAAC,CAAC,CAAC;QACrE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,GAAG,WAA8B;QAC3C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,4BAA4B,CAAC,WAAW,CAAC,CAAC,CAAC;QACxE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,IAAc,EAAE,aAAsB,KAAK;QAClD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,qBAAqB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;QACtE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,QAAiB,EAAE,QAAiB;QACjD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,gCAAgC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;QACnF,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,8BAA8B,EAAE,CAAC,CAAC;QAC/D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,0BAA0B,EAAE,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC;QAED,4CAA4C;QAC5C,IAAI,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpD,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAuB,CAAC;QACxE,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,WAAW;QACT,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC;QAED,2CAA2C;QAC3C,IAAI,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpD,QAAQ,GAAG,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAuB,CAAC;QACvE,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM;QACX,OAAO,IAAI,yBAAyB,EAAE,CAAC;IACzC,CAAC;CACF;AAnJD,8DAmJC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\specifications\\event.specification.ts"], "sourcesContent": ["import { BaseSpecification, UniqueEntityId } from '../../../../shared-kernel';\r\nimport { Event } from '../entities/event.entity';\r\nimport { EventType } from '../enums/event-type.enum';\r\nimport { EventSeverity } from '../enums/event-severity.enum';\r\nimport { EventStatus } from '../enums/event-status.enum';\r\nimport { EventProcessingStatus } from '../enums/event-processing-status.enum';\r\nimport { EventSourceType } from '../enums/event-source-type.enum';\r\n\r\n/**\r\n * Event Specification Base Class\r\n * \r\n * Base class for all event-related specifications.\r\n * Provides common functionality for event filtering and validation.\r\n */\r\nexport abstract class EventSpecification extends BaseSpecification<Event> {\r\n  /**\r\n   * Helper method to check if event matches any of the provided types\r\n   */\r\n  protected matchesAnyType(event: Event, types: EventType[]): boolean {\r\n    return types.includes(event.type);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if event matches any of the provided severities\r\n   */\r\n  protected matchesAnySeverity(event: Event, severities: EventSeverity[]): boolean {\r\n    return severities.includes(event.severity);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if event matches any of the provided statuses\r\n   */\r\n  protected matchesAnyStatus(event: Event, statuses: EventStatus[]): boolean {\r\n    return statuses.includes(event.status);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if event matches any of the provided processing statuses\r\n   */\r\n  protected matchesAnyProcessingStatus(event: Event, statuses: EventProcessingStatus[]): boolean {\r\n    return statuses.includes(event.processingStatus);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if event source matches any of the provided types\r\n   */\r\n  protected matchesAnySourceType(event: Event, sourceTypes: EventSourceType[]): boolean {\r\n    return sourceTypes.includes(event.metadata.source.type);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if event has any of the provided tags\r\n   */\r\n  protected hasAnyTag(event: Event, tags: string[]): boolean {\r\n    return event.hasAnyTag(tags);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if event has all of the provided tags\r\n   */\r\n  protected hasAllTags(event: Event, tags: string[]): boolean {\r\n    return event.hasAllTags(tags);\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if event age is within range\r\n   */\r\n  protected isAgeWithinRange(event: Event, minAgeMs?: number, maxAgeMs?: number): boolean {\r\n    const age = event.getAge();\r\n    \r\n    if (minAgeMs !== undefined && age < minAgeMs) {\r\n      return false;\r\n    }\r\n    \r\n    if (maxAgeMs !== undefined && age > maxAgeMs) {\r\n      return false;\r\n    }\r\n    \r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Helper method to check if risk score is within range\r\n   */\r\n  protected isRiskScoreWithinRange(event: Event, minScore?: number, maxScore?: number): boolean {\r\n    const riskScore = event.riskScore;\r\n    \r\n    if (riskScore === undefined) {\r\n      return minScore === undefined; // If no min score required, undefined is acceptable\r\n    }\r\n    \r\n    if (minScore !== undefined && riskScore < minScore) {\r\n      return false;\r\n    }\r\n    \r\n    if (maxScore !== undefined && riskScore > maxScore) {\r\n      return false;\r\n    }\r\n    \r\n    return true;\r\n  }\r\n}\r\n\r\n/**\r\n * High Severity Event Specification\r\n * \r\n * Specification for events with high or critical severity.\r\n */\r\nexport class HighSeverityEventSpecification extends EventSpecification {\r\n  isSatisfiedBy(event: Event): boolean {\r\n    return event.isHighSeverity();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Event has high or critical severity';\r\n  }\r\n}\r\n\r\n/**\r\n * Critical Event Specification\r\n * \r\n * Specification for events with critical severity only.\r\n */\r\nexport class CriticalEventSpecification extends EventSpecification {\r\n  isSatisfiedBy(event: Event): boolean {\r\n    return event.isCritical();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Event has critical severity';\r\n  }\r\n}\r\n\r\n/**\r\n * Active Event Specification\r\n * \r\n * Specification for events that are currently active (not resolved or closed).\r\n */\r\nexport class ActiveEventSpecification extends EventSpecification {\r\n  isSatisfiedBy(event: Event): boolean {\r\n    return event.isActive();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Event is active (not resolved or closed)';\r\n  }\r\n}\r\n\r\n/**\r\n * High Risk Event Specification\r\n * \r\n * Specification for events with high risk scores.\r\n */\r\nexport class HighRiskEventSpecification extends EventSpecification {\r\n  isSatisfiedBy(event: Event): boolean {\r\n    return event.isHighRisk();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Event has high risk score (>= 70)';\r\n  }\r\n}\r\n\r\n/**\r\n * Recent Event Specification\r\n * \r\n * Specification for events that occurred recently.\r\n */\r\nexport class RecentEventSpecification extends EventSpecification {\r\n  constructor(private readonly withinMs: number = 3600000) { // Default 1 hour\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: Event): boolean {\r\n    return event.isRecent(this.withinMs);\r\n  }\r\n\r\n  getDescription(): string {\r\n    const hours = Math.floor(this.withinMs / 3600000);\r\n    const minutes = Math.floor((this.withinMs % 3600000) / 60000);\r\n    return `Event occurred within ${hours}h ${minutes}m`;\r\n  }\r\n}\r\n\r\n/**\r\n * Stale Event Specification\r\n * \r\n * Specification for events that are considered stale.\r\n */\r\nexport class StaleEventSpecification extends EventSpecification {\r\n  constructor(private readonly olderThanMs: number = 86400000) { // Default 24 hours\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: Event): boolean {\r\n    return event.isStale(this.olderThanMs);\r\n  }\r\n\r\n  getDescription(): string {\r\n    const hours = Math.floor(this.olderThanMs / 3600000);\r\n    return `Event is older than ${hours} hours`;\r\n  }\r\n}\r\n\r\n/**\r\n * Event Type Specification\r\n * \r\n * Specification for events of specific types.\r\n */\r\nexport class EventTypeSpecification extends EventSpecification {\r\n  constructor(private readonly types: EventType[]) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: Event): boolean {\r\n    return this.matchesAnyType(event, this.types);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Event type is one of: ${this.types.join(', ')}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Event Severity Specification\r\n * \r\n * Specification for events of specific severities.\r\n */\r\nexport class EventSeveritySpecification extends EventSpecification {\r\n  constructor(private readonly severities: EventSeverity[]) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: Event): boolean {\r\n    return this.matchesAnySeverity(event, this.severities);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Event severity is one of: ${this.severities.join(', ')}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Event Status Specification\r\n * \r\n * Specification for events with specific statuses.\r\n */\r\nexport class EventStatusSpecification extends EventSpecification {\r\n  constructor(private readonly statuses: EventStatus[]) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: Event): boolean {\r\n    return this.matchesAnyStatus(event, this.statuses);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Event status is one of: ${this.statuses.join(', ')}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Event Processing Status Specification\r\n * \r\n * Specification for events with specific processing statuses.\r\n */\r\nexport class EventProcessingStatusSpecification extends EventSpecification {\r\n  constructor(private readonly statuses: EventProcessingStatus[]) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: Event): boolean {\r\n    return this.matchesAnyProcessingStatus(event, this.statuses);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Event processing status is one of: ${this.statuses.join(', ')}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Event Source Type Specification\r\n * \r\n * Specification for events from specific source types.\r\n */\r\nexport class EventSourceTypeSpecification extends EventSpecification {\r\n  constructor(private readonly sourceTypes: EventSourceType[]) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: Event): boolean {\r\n    return this.matchesAnySourceType(event, this.sourceTypes);\r\n  }\r\n\r\n  getDescription(): string {\r\n    return `Event source type is one of: ${this.sourceTypes.join(', ')}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Event Tag Specification\r\n * \r\n * Specification for events with specific tags.\r\n */\r\nexport class EventTagSpecification extends EventSpecification {\r\n  constructor(\r\n    private readonly tags: string[],\r\n    private readonly requireAll: boolean = false\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: Event): boolean {\r\n    return this.requireAll \r\n      ? this.hasAllTags(event, this.tags)\r\n      : this.hasAnyTag(event, this.tags);\r\n  }\r\n\r\n  getDescription(): string {\r\n    const operator = this.requireAll ? 'all' : 'any';\r\n    return `Event has ${operator} of these tags: ${this.tags.join(', ')}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Event Risk Score Range Specification\r\n * \r\n * Specification for events within a specific risk score range.\r\n */\r\nexport class EventRiskScoreRangeSpecification extends EventSpecification {\r\n  constructor(\r\n    private readonly minScore?: number,\r\n    private readonly maxScore?: number\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: Event): boolean {\r\n    return this.isRiskScoreWithinRange(event, this.minScore, this.maxScore);\r\n  }\r\n\r\n  getDescription(): string {\r\n    if (this.minScore !== undefined && this.maxScore !== undefined) {\r\n      return `Event risk score is between ${this.minScore} and ${this.maxScore}`;\r\n    } else if (this.minScore !== undefined) {\r\n      return `Event risk score is at least ${this.minScore}`;\r\n    } else if (this.maxScore !== undefined) {\r\n      return `Event risk score is at most ${this.maxScore}`;\r\n    }\r\n    return 'Event has any risk score';\r\n  }\r\n}\r\n\r\n/**\r\n * Event Age Range Specification\r\n * \r\n * Specification for events within a specific age range.\r\n */\r\nexport class EventAgeRangeSpecification extends EventSpecification {\r\n  constructor(\r\n    private readonly minAgeMs?: number,\r\n    private readonly maxAgeMs?: number\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: Event): boolean {\r\n    return this.isAgeWithinRange(event, this.minAgeMs, this.maxAgeMs);\r\n  }\r\n\r\n  getDescription(): string {\r\n    const formatTime = (ms: number) => {\r\n      const hours = Math.floor(ms / 3600000);\r\n      const minutes = Math.floor((ms % 3600000) / 60000);\r\n      return `${hours}h ${minutes}m`;\r\n    };\r\n\r\n    if (this.minAgeMs !== undefined && this.maxAgeMs !== undefined) {\r\n      return `Event age is between ${formatTime(this.minAgeMs)} and ${formatTime(this.maxAgeMs)}`;\r\n    } else if (this.minAgeMs !== undefined) {\r\n      return `Event is at least ${formatTime(this.minAgeMs)} old`;\r\n    } else if (this.maxAgeMs !== undefined) {\r\n      return `Event is at most ${formatTime(this.maxAgeMs)} old`;\r\n    }\r\n    return 'Event has any age';\r\n  }\r\n}\r\n\r\n/**\r\n * Event Correlation Specification\r\n * \r\n * Specification for events with specific correlation properties.\r\n */\r\nexport class EventCorrelationSpecification extends EventSpecification {\r\n  constructor(\r\n    private readonly correlationId?: string,\r\n    private readonly parentEventId?: UniqueEntityId,\r\n    private readonly hasCorrelation: boolean = true\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  isSatisfiedBy(event: Event): boolean {\r\n    if (this.correlationId) {\r\n      return event.correlationId === this.correlationId;\r\n    }\r\n    \r\n    if (this.parentEventId) {\r\n      return event.parentEventId?.equals(this.parentEventId) || false;\r\n    }\r\n    \r\n    if (this.hasCorrelation) {\r\n      return event.correlationId !== undefined || event.parentEventId !== undefined;\r\n    } else {\r\n      return event.correlationId === undefined && event.parentEventId === undefined;\r\n    }\r\n  }\r\n\r\n  getDescription(): string {\r\n    if (this.correlationId) {\r\n      return `Event has correlation ID: ${this.correlationId}`;\r\n    }\r\n    \r\n    if (this.parentEventId) {\r\n      return `Event has parent ID: ${this.parentEventId.toString()}`;\r\n    }\r\n    \r\n    return this.hasCorrelation \r\n      ? 'Event has correlation information'\r\n      : 'Event has no correlation information';\r\n  }\r\n}\r\n\r\n/**\r\n * Failed Processing Event Specification\r\n * \r\n * Specification for events that have failed processing.\r\n */\r\nexport class FailedProcessingEventSpecification extends EventSpecification {\r\n  isSatisfiedBy(event: Event): boolean {\r\n    return event.hasProcessingFailed();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Event processing has failed';\r\n  }\r\n}\r\n\r\n/**\r\n * Max Attempts Exceeded Specification\r\n * \r\n * Specification for events that have exceeded maximum processing attempts.\r\n */\r\nexport class MaxAttemptsExceededSpecification extends EventSpecification {\r\n  isSatisfiedBy(event: Event): boolean {\r\n    return event.hasExceededMaxAttempts();\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Event has exceeded maximum processing attempts';\r\n  }\r\n}\r\n\r\n/**\r\n * Requires Attention Specification\r\n * \r\n * Specification for events that require human attention.\r\n */\r\nexport class RequiresAttentionSpecification extends EventSpecification {\r\n  isSatisfiedBy(event: Event): boolean {\r\n    return event.isHighSeverity() || \r\n           event.isHighRisk() || \r\n           event.hasProcessingFailed() ||\r\n           event.hasExceededMaxAttempts() ||\r\n           event.status === EventStatus.INVESTIGATING;\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Event requires human attention (high severity, high risk, failed processing, or under investigation)';\r\n  }\r\n}\r\n\r\n/**\r\n * Security Alert Specification\r\n * \r\n * Specification for events that are security alerts.\r\n */\r\nexport class SecurityAlertSpecification extends EventSpecification {\r\n  private static readonly SECURITY_EVENT_TYPES = [\r\n    EventType.THREAT_DETECTED,\r\n    EventType.MALWARE_DETECTED,\r\n    EventType.VULNERABILITY_DETECTED,\r\n    EventType.SUSPICIOUS_CONNECTION,\r\n    EventType.PORT_SCAN_DETECTED,\r\n    EventType.DDOS_ATTACK,\r\n    EventType.DATA_BREACH,\r\n    EventType.IOC_DETECTED,\r\n  ];\r\n\r\n  isSatisfiedBy(event: Event): boolean {\r\n    return this.matchesAnyType(event, SecurityAlertSpecification.SECURITY_EVENT_TYPES) ||\r\n           event.isHighSeverity() ||\r\n           event.hasTag('security-alert');\r\n  }\r\n\r\n  getDescription(): string {\r\n    return 'Event is a security alert (threat, malware, vulnerability, or high severity)';\r\n  }\r\n}\r\n\r\n/**\r\n * Composite Event Specification Builder\r\n * \r\n * Builder for creating complex event specifications using fluent interface.\r\n */\r\nexport class EventSpecificationBuilder {\r\n  private specifications: EventSpecification[] = [];\r\n\r\n  /**\r\n   * Add high severity filter\r\n   */\r\n  highSeverity(): EventSpecificationBuilder {\r\n    this.specifications.push(new HighSeverityEventSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add critical severity filter\r\n   */\r\n  critical(): EventSpecificationBuilder {\r\n    this.specifications.push(new CriticalEventSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add active status filter\r\n   */\r\n  active(): EventSpecificationBuilder {\r\n    this.specifications.push(new ActiveEventSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add high risk filter\r\n   */\r\n  highRisk(): EventSpecificationBuilder {\r\n    this.specifications.push(new HighRiskEventSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add recent events filter\r\n   */\r\n  recent(withinMs: number = 3600000): EventSpecificationBuilder {\r\n    this.specifications.push(new RecentEventSpecification(withinMs));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add event type filter\r\n   */\r\n  ofTypes(...types: EventType[]): EventSpecificationBuilder {\r\n    this.specifications.push(new EventTypeSpecification(types));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add severity filter\r\n   */\r\n  withSeverities(...severities: EventSeverity[]): EventSpecificationBuilder {\r\n    this.specifications.push(new EventSeveritySpecification(severities));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add source type filter\r\n   */\r\n  fromSources(...sourceTypes: EventSourceType[]): EventSpecificationBuilder {\r\n    this.specifications.push(new EventSourceTypeSpecification(sourceTypes));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add tag filter\r\n   */\r\n  withTags(tags: string[], requireAll: boolean = false): EventSpecificationBuilder {\r\n    this.specifications.push(new EventTagSpecification(tags, requireAll));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add risk score range filter\r\n   */\r\n  riskScoreRange(minScore?: number, maxScore?: number): EventSpecificationBuilder {\r\n    this.specifications.push(new EventRiskScoreRangeSpecification(minScore, maxScore));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add requires attention filter\r\n   */\r\n  requiresAttention(): EventSpecificationBuilder {\r\n    this.specifications.push(new RequiresAttentionSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Add security alert filter\r\n   */\r\n  securityAlerts(): EventSpecificationBuilder {\r\n    this.specifications.push(new SecurityAlertSpecification());\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Build the final specification using AND logic\r\n   */\r\n  build(): EventSpecification {\r\n    if (this.specifications.length === 0) {\r\n      throw new Error('At least one specification must be added');\r\n    }\r\n\r\n    if (this.specifications.length === 1) {\r\n      return this.specifications[0];\r\n    }\r\n\r\n    // Combine all specifications with AND logic\r\n    let combined = this.specifications[0];\r\n    for (let i = 1; i < this.specifications.length; i++) {\r\n      combined = combined.and(this.specifications[i]) as EventSpecification;\r\n    }\r\n\r\n    return combined;\r\n  }\r\n\r\n  /**\r\n   * Build the final specification using OR logic\r\n   */\r\n  buildWithOr(): EventSpecification {\r\n    if (this.specifications.length === 0) {\r\n      throw new Error('At least one specification must be added');\r\n    }\r\n\r\n    if (this.specifications.length === 1) {\r\n      return this.specifications[0];\r\n    }\r\n\r\n    // Combine all specifications with OR logic\r\n    let combined = this.specifications[0];\r\n    for (let i = 1; i < this.specifications.length; i++) {\r\n      combined = combined.or(this.specifications[i]) as EventSpecification;\r\n    }\r\n\r\n    return combined;\r\n  }\r\n\r\n  /**\r\n   * Create a new builder instance\r\n   */\r\n  static create(): EventSpecificationBuilder {\r\n    return new EventSpecificationBuilder();\r\n  }\r\n}"], "version": 3}