{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\vulnerability.factory.ts", "mappings": ";;;AAAA,yFAOwD;AAExD,wGAAoG;AACpG,wEAA+D;AAE/D,0EAAiE;AAyKjE;;;;;;;;;;;;GAYG;AACH,MAAa,oBAAoB;IAC/B;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,OAAmC;QAC/C,2FAA2F;QAC3F,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC;YACf,OAAO,oBAAoB,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;QAChE,CAAC;QAED,OAAO,oCAAa,CAAC,MAAM,CACzB,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,WAAW,EACnB,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,IAAI,EACZ,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,SAAS,EACjB;YACE,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,UAAU,EAAE,OAAO,CAAC,UAAU;SAC/B,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,YAAY,CAAC,OAAmC,EAAE,EAAkB;QACjF,kFAAkF;QAClF,MAAM,cAAc,GAAG;YACrB,SAAS,EAAE,EAAE,EAAE,gEAAgE;YAC/E,mBAAmB,EAAE,EAAE;YACvB,WAAW,EAAE,EAAE;YACf,oBAAoB,EAAE;gBACpB,gBAAgB,EAAE,EAAE;gBACpB,eAAe,EAAE,EAAE;gBACnB,kBAAkB,EAAE,EAAE;gBACtB,cAAc,EAAE,EAAE;aACnB;YACD,WAAW,EAAE,EAAE;YACf,SAAS,EAAE,QAAiB;YAC5B,YAAY,EAAE;gBACZ,eAAe,EAAE,CAAC;gBAClB,kBAAkB,EAAE,QAAiB;gBACrC,iBAAiB,EAAE,QAAiB;gBACpC,gBAAgB,EAAE,QAAiB;aACpC;SACF,CAAC;QAEF,MAAM,KAAK,GAAG;YACZ,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE;YAC3B,WAAW,EAAE,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE;YACvC,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,EAAE;YACpC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE;YACjC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE;YACzB,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,uCAAe,CAAC,MAAM;YACxD,cAAc,EAAE,OAAO,CAAC,cAAc;YACtC,MAAM,EAAE,0CAAmB,CAAC,UAAU;YACtC,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,WAAW,EAAE;gBACX,MAAM,EAAE,aAAsB;gBAC9B,QAAQ,EAAE,QAAiB;gBAC3B,OAAO,EAAE,EAAE;gBACX,WAAW,EAAE,EAAE;gBACf,QAAQ,EAAE,EAAE;aACb;YACD,cAAc;YACd,gBAAgB,EAAE,EAAE;YACpB,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,EAAE;YACxB,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,EAAE;SACrC,CAAC;QAEF,OAAO,IAAI,oCAAa,CAAC,KAAY,EAAE,EAAE,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe,CACpB,QAA0B,EAC1B,cAAoC,EACpC,OAA6C;QAE7C,MAAM,UAAU,GAAgB,EAAE,CAAC;QAEnC,mCAAmC;QACnC,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;YACpB,UAAU,CAAC,IAAI,CAAC,mCAAS,CAAC,UAAU,CAClC,QAAQ,CAAC,MAAM,CAAC,SAAS,EACzB,QAAQ,CAAC,MAAM,CAAC,YAAY,EAC5B;gBACE,mBAAmB,EAAE,QAAQ,CAAC,MAAM,CAAC,mBAAmB;gBACxD,WAAW,EAAE,QAAQ,CAAC,MAAM,CAAC,WAAW;gBACxC,MAAM,EAAE,KAAK;gBACb,YAAY,EAAE,QAAQ,CAAC,gBAAgB;aACxC,CACF,CAAC,CAAC;QACL,CAAC;QAED,iCAAiC;QACjC,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;YACpB,UAAU,CAAC,IAAI,CAAC,mCAAS,CAAC,MAAM,CAC9B,QAAQ,CAAC,MAAM,CAAC,SAAS,EACzB,qCAAW,CAAC,EAAE,EACd,QAAQ,CAAC,MAAM,CAAC,YAAY,EAC5B;gBACE,MAAM,EAAE,KAAK;gBACb,YAAY,EAAE,QAAQ,CAAC,gBAAgB;aACxC,CACF,CAAC,CAAC;QACL,CAAC;QAED,sCAAsC;QACtC,MAAM,QAAQ,GAAG,oBAAoB,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;QAE1E,mCAAmC;QACnC,MAAM,QAAQ,GAAG,oBAAoB,CAAC,oBAAoB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAE5E,oCAAoC;QACpC,MAAM,IAAI,GAAG,oBAAoB,CAAC,qBAAqB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;QAEnF,+BAA+B;QAC/B,MAAM,SAAS,GAA2B;YACxC,MAAM,EAAE,qBAAqB;YAC7B,MAAM,EAAE,cAAc;YACtB,YAAY,EAAE,QAAQ,CAAC,aAAa;YACpC,YAAY,EAAE,KAAK;YACnB,OAAO,EAAE,OAAO,QAAQ,CAAC,KAAK,uCAAuC;SACtE,CAAC;QAEF,OAAO,oBAAoB,CAAC,MAAM,CAAC;YACjC,KAAK,EAAE,QAAQ,CAAC,KAAK;YACrB,KAAK,EAAE,QAAQ,CAAC,KAAK;YACrB,WAAW,EAAE,QAAQ,CAAC,WAAW;YACjC,QAAQ;YACR,QAAQ;YACR,IAAI;YACJ,UAAU,EAAE,uCAAe,CAAC,IAAI,EAAE,4CAA4C;YAC9E,UAAU;YACV,cAAc;YACd,SAAS;YACT,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC;YACjE,UAAU,EAAE;gBACV,KAAK,EAAE,QAAQ,CAAC,KAAK;gBACrB,aAAa,EAAE,QAAQ,CAAC,aAAa,CAAC,WAAW,EAAE;gBACnD,gBAAgB,EAAE,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE;gBACzD,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,gBAAgB,EAAE,QAAQ,CAAC,gBAAgB;gBAC3C,cAAc,EAAE,QAAQ,CAAC,cAAc;aACxC;YACD,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CACnB,UAAmC,EACnC,OAA6C;QAE7C,yCAAyC;QACzC,MAAM,aAAa,GAAuB;YACxC,OAAO,EAAE,UAAU,CAAC,KAAK,CAAC,EAAE;YAC5B,SAAS,EAAE,UAAU,CAAC,KAAK,CAAC,IAAI;YAChC,SAAS,EAAE,UAAU,CAAC,KAAK,CAAC,IAAI;YAChC,WAAW,EAAE,QAAQ,EAAE,oDAAoD;YAC3E,kBAAkB,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;oBACxC,IAAI,EAAE,UAAU,CAAC,OAAO,CAAC,OAAO;oBAChC,OAAO,EAAE,UAAU,CAAC,OAAO,CAAC,OAAO,IAAI,SAAS;oBAChD,IAAI,EAAE,UAAU;iBACjB,CAAC,CAAC,CAAC,CAAC,EAAE;YACP,QAAQ,EAAE,UAAU,EAAE,oDAAoD;YAC1E,cAAc,EAAE,EAAE;SACnB,CAAC;QAEF,iCAAiC;QACjC,MAAM,UAAU,GAAgB,EAAE,CAAC;QACnC,IAAI,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;YAClD,IAAI,CAAC;gBACH,UAAU,CAAC,IAAI,CAAC,mCAAS,CAAC,gBAAgB,CACxC,UAAU,CAAC,UAAU,EACrB,UAAU,CAAC,OAAO,CAAC,IAAI,CACxB,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,8CAA8C;gBAC9C,UAAU,CAAC,IAAI,CAAC,mCAAS,CAAC,UAAU,CAClC,UAAU,CAAC,SAAS,EACpB,8CAA8C,EAAE,cAAc;gBAC9D,EAAE,MAAM,EAAE,UAAU,CAAC,OAAO,CAAC,IAAI,EAAE,CACpC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,0CAA0C;QAC1C,MAAM,QAAQ,GAAG,oBAAoB,CAAC,kBAAkB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAE9E,4CAA4C;QAC5C,MAAM,QAAQ,GAAG,oBAAoB,CAAC,2BAA2B,CAAC,UAAU,CAAC,CAAC;QAC9E,MAAM,IAAI,GAAG,oBAAoB,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;QAEtE,+BAA+B;QAC/B,MAAM,SAAS,GAA2B;YACxC,MAAM,EAAE,gBAAgB;YACxB,MAAM,EAAE,UAAU,CAAC,OAAO,CAAC,IAAI;YAC/B,YAAY,EAAE,UAAU,CAAC,aAAa;YACtC,YAAY,EAAE,UAAU,CAAC,OAAO,CAAC,IAAI;YACrC,OAAO,EAAE,iBAAiB,UAAU,CAAC,OAAO,CAAC,IAAI,KAAK,UAAU,CAAC,OAAO,CAAC,OAAO,EAAE;YAClF,WAAW,EAAE;gBACX,IAAI,EAAE,UAAU,CAAC,OAAO,CAAC,IAAI;gBAC7B,OAAO,EAAE,UAAU,CAAC,OAAO,CAAC,OAAO;gBACnC,MAAM,EAAE,UAAU,CAAC,OAAO,CAAC,MAAM;gBACjC,UAAU,EAAE,UAAU,CAAC,OAAO,CAAC,UAAU;aAC1C;SACF,CAAC;QAEF,8CAA8C;QAC9C,MAAM,UAAU,GAAG,oBAAoB,CAAC,oBAAoB,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAE5F,OAAO,oBAAoB,CAAC,MAAM,CAAC;YACjC,KAAK,EAAE,UAAU,CAAC,KAAK;YACvB,KAAK,EAAE,UAAU,CAAC,IAAI;YACtB,WAAW,EAAE,UAAU,CAAC,WAAW;YACnC,QAAQ;YACR,QAAQ;YACR,IAAI;YACJ,UAAU;YACV,UAAU;YACV,cAAc,EAAE,CAAC,aAAa,CAAC;YAC/B,SAAS;YACT,IAAI,EAAE,CAAC,aAAa,EAAE,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YAC5D,UAAU,EAAE;gBACV,SAAS,EAAE,UAAU,CAAC,SAAS;gBAC/B,aAAa,EAAE,UAAU,CAAC,aAAa,CAAC,WAAW,EAAE;gBACrD,OAAO,EAAE,UAAU,CAAC,OAAO;gBAC3B,OAAO,EAAE,UAAU,CAAC,OAAO;gBAC3B,QAAQ,EAAE,UAAU,CAAC,QAAQ;aAC9B;YACD,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAC3B,iBAAgC,EAChC,WAAyC;QAEzC,2DAA2D;QAC3D,MAAM,YAAY,GAA8B;YAC9C,MAAM,EAAE,WAAW,CAAC,kBAAkB;YACtC,UAAU,EAAE,oBAAoB,CAAC,2BAA2B,CAAC,WAAW,CAAC;YACzE,iBAAiB,EAAE,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBACtD,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,UAAU,EAAE,OAAO,CAAC,aAAa;aAClC,CAAC,CAAC;YACH,QAAQ,EAAE,oBAAoB,CAAC,yBAAyB,CAAC,WAAW,CAAC;YACrE,aAAa,EAAE,WAAW,CAAC,cAAc,EAAE,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE;YAClF,aAAa,EAAE,EAAE;SAClB,CAAC;QAEF,mDAAmD;QACnD,MAAM,WAAW,GAAG;YAClB,GAAG,iBAAiB,CAAC,IAAI;YACzB,qBAAqB;YACrB,WAAW,CAAC,MAAM,CAAC,WAAW,EAAE;SACjC,CAAC;QAEF,IAAI,WAAW,CAAC,kBAAkB,KAAK,qBAAqB,EAAE,CAAC;YAC7D,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,WAAW,CAAC,mBAAmB,IAAI,WAAW,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClF,WAAW,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAC5C,CAAC;QAED,kDAAkD;QAClD,MAAM,iBAAiB,GAAG;YACxB,GAAG,iBAAiB,CAAC,UAAU;YAC/B,kBAAkB,EAAE;gBAClB,MAAM,EAAE,WAAW,CAAC,MAAM;gBAC1B,kBAAkB,EAAE,WAAW,CAAC,kBAAkB;gBAClD,QAAQ,EAAE,WAAW,CAAC,QAAQ;gBAC9B,mBAAmB,EAAE,WAAW,CAAC,mBAAmB;gBACpD,cAAc,EAAE,WAAW,CAAC,cAAc;gBAC1C,SAAS,EAAE,WAAW,CAAC,SAAS,CAAC,WAAW,EAAE;aAC/C;SACF,CAAC;QAEF,oDAAoD;QACpD,OAAO,oBAAoB,CAAC,MAAM,CAAC;YACjC,EAAE,EAAE,iBAAiB,CAAC,EAAE;YACxB,KAAK,EAAE,iBAAiB,CAAC,KAAK;YAC9B,KAAK,EAAE,iBAAiB,CAAC,KAAK;YAC9B,WAAW,EAAE,iBAAiB,CAAC,WAAW;YAC1C,QAAQ,EAAE,iBAAiB,CAAC,QAAQ;YACpC,QAAQ,EAAE,iBAAiB,CAAC,QAAQ;YACpC,IAAI,EAAE,iBAAiB,CAAC,IAAI;YAC5B,UAAU,EAAE,iBAAiB,CAAC,UAAU;YACxC,UAAU,EAAE,iBAAiB,CAAC,UAAU;YACxC,cAAc,EAAE,iBAAiB,CAAC,cAAc;YAChD,SAAS,EAAE,iBAAiB,CAAC,SAAS;YACtC,YAAY;YACZ,IAAI,EAAE,WAAW;YACjB,UAAU,EAAE,iBAAiB;SAC9B,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CACxB,KAAa,EACb,WAAmB,EACnB,KAAa,EACb,cAAoC,EACpC,OAA6C;QAE7C,MAAM,SAAS,GAA2B;YACxC,MAAM,EAAE,iBAAiB;YACzB,MAAM,EAAE,gBAAgB;YACxB,YAAY,EAAE,IAAI,IAAI,EAAE;YACxB,YAAY,EAAE,eAAe;YAC7B,OAAO,EAAE,4DAA4D;SACtE,CAAC;QAEF,OAAO,oBAAoB,CAAC,MAAM,CAAC;YACjC,KAAK;YACL,KAAK;YACL,WAAW;YACX,QAAQ,EAAE,qCAAc,CAAC,QAAQ;YACjC,QAAQ,EAAE,gBAAgB;YAC1B,IAAI,EAAE,gBAAgB;YACtB,UAAU,EAAE,uCAAe,CAAC,IAAI;YAChC,cAAc;YACd,SAAS;YACT,IAAI,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,oBAAoB,CAAC;YACjD,UAAU,EAAE;gBACV,SAAS,EAAE,UAAU;gBACrB,0BAA0B,EAAE,IAAI;aACjC;YACD,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAClB,KAAa,EACb,WAAmB,EACnB,cAAoC,EACpC,eAA+E,EAC/E,OAA6C;QAE7C,MAAM,SAAS,GAA2B;YACxC,MAAM,EAAE,eAAe;YACvB,MAAM,EAAE,oBAAoB;YAC5B,YAAY,EAAE,IAAI,IAAI,EAAE;YACxB,YAAY,EAAE,wBAAwB;YACtC,OAAO,EAAE,qDAAqD;SAC/D,CAAC;QAEF,MAAM,YAAY,GAA8B;YAC9C,MAAM,EAAE,qBAAqB;YAC7B,UAAU,EAAE,QAAQ;YACpB,iBAAiB,EAAE,EAAE;YACrB,aAAa,EAAE,EAAE;YACjB,aAAa,EAAE,EAAE;SAClB,CAAC;QAEF,OAAO,oBAAoB,CAAC,MAAM,CAAC;YACjC,KAAK;YACL,WAAW;YACX,QAAQ,EAAE,qCAAc,CAAC,QAAQ;YACjC,QAAQ,EAAE,UAAU;YACpB,IAAI,EAAE,uBAAuB;YAC7B,UAAU,EAAE,uCAAe,CAAC,MAAM;YAClC,cAAc;YACd,SAAS;YACT,YAAY;YACZ,IAAI,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,qBAAqB,CAAC;YACnD,UAAU,EAAE;gBACV,OAAO,EAAE,IAAI;gBACb,KAAK,EAAE,IAAI;gBACX,eAAe;aAChB;YACD,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED,gDAAgD;IAExC,MAAM,CAAC,uBAAuB,CAAC,UAAuB;QAC5D,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAO,qCAAc,CAAC,OAAO,CAAC;QAChC,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;QAEvE,IAAI,QAAQ,IAAI,GAAG;YAAE,OAAO,qCAAc,CAAC,QAAQ,CAAC;QACpD,IAAI,QAAQ,IAAI,GAAG;YAAE,OAAO,qCAAc,CAAC,IAAI,CAAC;QAChD,IAAI,QAAQ,IAAI,GAAG;YAAE,OAAO,qCAAc,CAAC,MAAM,CAAC;QAClD,IAAI,QAAQ,IAAI,GAAG;YAAE,OAAO,qCAAc,CAAC,GAAG,CAAC;QAC/C,OAAO,qCAAc,CAAC,OAAO,CAAC;IAChC,CAAC;IAEO,MAAM,CAAC,kBAAkB,CAAC,QAAgB;QAChD,MAAM,GAAG,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;QAEnC,IAAI,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,qCAAc,CAAC,QAAQ,CAAC;QACtF,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,qCAAc,CAAC,IAAI,CAAC;QAC/E,IAAI,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC;YAAE,OAAO,qCAAc,CAAC,MAAM,CAAC;QACrF,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,qCAAc,CAAC,GAAG,CAAC;QAE5E,OAAO,qCAAc,CAAC,OAAO,CAAC;IAChC,CAAC;IAEO,MAAM,CAAC,oBAAoB,CAAC,UAAkB;QACpD,IAAI,UAAU,IAAI,EAAE;YAAE,OAAO,uCAAe,CAAC,SAAS,CAAC;QACvD,IAAI,UAAU,IAAI,EAAE;YAAE,OAAO,uCAAe,CAAC,SAAS,CAAC;QACvD,IAAI,UAAU,IAAI,EAAE;YAAE,OAAO,uCAAe,CAAC,IAAI,CAAC;QAClD,IAAI,UAAU,IAAI,EAAE;YAAE,OAAO,uCAAe,CAAC,MAAM,CAAC;QACpD,IAAI,UAAU,IAAI,EAAE;YAAE,OAAO,uCAAe,CAAC,GAAG,CAAC;QACjD,OAAO,uCAAe,CAAC,QAAQ,CAAC;IAClC,CAAC;IAEO,MAAM,CAAC,oBAAoB,CAAC,MAAgB;QAClD,4BAA4B;QAC5B,MAAM,WAAW,GAA2B;YAC1C,QAAQ,EAAE,WAAW;YACrB,QAAQ,EAAE,WAAW;YACrB,QAAQ,EAAE,WAAW;YACrB,QAAQ,EAAE,WAAW;YACrB,SAAS,EAAE,gBAAgB;YAC3B,SAAS,EAAE,eAAe;YAC1B,SAAS,EAAE,gBAAgB;YAC3B,SAAS,EAAE,wBAAwB;YACnC,SAAS,EAAE,mBAAmB;YAC9B,SAAS,EAAE,mBAAmB;YAC9B,SAAS,EAAE,mBAAmB;YAC9B,QAAQ,EAAE,gBAAgB;YAC1B,SAAS,EAAE,MAAM;YACjB,SAAS,EAAE,aAAa;SACzB,CAAC;QAEF,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;gBACvB,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,MAAM,CAAC,qBAAqB,CAAC,QAAwE;QAC3G,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,SAAS,CAAC;QAE5C,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC5B,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QAElD,IAAI,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACtG,OAAO,kBAAkB,CAAC;QAC5B,CAAC;QACD,IAAI,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACnG,OAAO,YAAY,CAAC;QACtB,CAAC;QACD,IAAI,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC1G,OAAO,UAAU,CAAC;QACpB,CAAC;QACD,IAAI,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACxG,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAEO,MAAM,CAAC,2BAA2B,CAAC,UAAmC;QAC5E,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;QAC3C,MAAM,WAAW,GAAG,UAAU,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;QAEzD,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,eAAe,CAAC;YAAE,OAAO,WAAW,CAAC;QACtF,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,sBAAsB,CAAC;YAAE,OAAO,WAAW,CAAC;QAC7F,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,uBAAuB,CAAC;YAAE,OAAO,uBAAuB,CAAC;QAC1G,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,gBAAgB,CAAC;YAAE,OAAO,gBAAgB,CAAC;QAC7F,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC;YAAE,OAAO,sBAAsB,CAAC;QACpG,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC;YAAE,OAAO,mBAAmB,CAAC;QAC5F,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC;YAAE,OAAO,gBAAgB,CAAC;QACxF,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,oBAAoB,CAAC;YAAE,OAAO,MAAM,CAAC;QAEvF,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,MAAM,CAAC,uBAAuB,CAAC,UAAmC;QACxE,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;YACvB,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC,OAAO,gBAAgB,CAAC;QACvD,CAAC;QAED,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;QACtD,OAAO,GAAG,SAAS,gBAAgB,CAAC;IACtC,CAAC;IAEO,MAAM,CAAC,2BAA2B,CAAC,WAAyC;QAClF,MAAM,cAAc,GAAG,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,MAAM,CAAC;QACpF,MAAM,uBAAuB,GAAG,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;QAE7F,IAAI,cAAc,GAAG,CAAC,IAAI,uBAAuB,GAAG,CAAC;YAAE,OAAO,KAAK,CAAC;QACpE,IAAI,cAAc,GAAG,CAAC,IAAI,uBAAuB,GAAG,CAAC;YAAE,OAAO,QAAQ,CAAC;QACvE,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,MAAM,CAAC,yBAAyB,CAAC,WAAyC;QAChF,MAAM,YAAY,GAAG,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;QACpE,MAAM,UAAU,GAAG,WAAW,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,IAAI,EAAE,CAAC;QAE1G,OAAO;YACL,qBAAqB,EAAE,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACtH,yBAAyB,EAAE,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACtH,wBAAwB,EAAE,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;SACtH,CAAC;IACJ,CAAC;CACF;AAthBD,oDAshBC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\factories\\vulnerability.factory.ts"], "sourcesContent": ["import { \r\n  Vulnerability, \r\n  VulnerabilityProps, \r\n  VulnerabilityAsset, \r\n  VulnerabilityDiscovery, \r\n  VulnerabilityExploitation,\r\n  VulnerabilityStatus \r\n} from '../entities/vulnerability/vulnerability.entity';\r\nimport { UniqueEntityId } from '../../../../shared-kernel';\r\nimport { CVSSScore, CVSSVersion } from '../value-objects/threat-indicators/cvss-score.value-object';\r\nimport { ThreatSeverity } from '../enums/threat-severity.enum';\r\nimport { VulnerabilitySeverity, VulnerabilitySeverityUtils } from '../enums/vulnerability-severity.enum';\r\nimport { ConfidenceLevel } from '../enums/confidence-level.enum';\r\n\r\n/**\r\n * Vulnerability Creation Options\r\n */\r\nexport interface CreateVulnerabilityOptions {\r\n  /** Vulnerability ID (optional, will be generated if not provided) */\r\n  id?: UniqueEntityId;\r\n  /** CVE identifier */\r\n  cveId?: string;\r\n  /** Vulnerability title */\r\n  title: string;\r\n  /** Vulnerability description */\r\n  description: string;\r\n  /** Vulnerability severity */\r\n  severity: ThreatSeverity;\r\n  /** Vulnerability category */\r\n  category: string;\r\n  /** Vulnerability type */\r\n  type: string;\r\n  /** Discovery confidence */\r\n  confidence?: ConfidenceLevel;\r\n  /** CVSS scores */\r\n  cvssScores?: CVSSScore[];\r\n  /** Affected assets */\r\n  affectedAssets: VulnerabilityAsset[];\r\n  /** Discovery information */\r\n  discovery: VulnerabilityDiscovery;\r\n  /** Exploitation information */\r\n  exploitation?: VulnerabilityExploitation;\r\n  /** Vulnerability tags */\r\n  tags?: string[];\r\n  /** Custom attributes */\r\n  attributes?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * CVE Database Entry\r\n */\r\nexport interface CVEDatabaseEntry {\r\n  /** CVE identifier */\r\n  cveId: string;\r\n  /** CVE title/summary */\r\n  title: string;\r\n  /** CVE description */\r\n  description: string;\r\n  /** CVSS v3.1 score */\r\n  cvssV3?: {\r\n    baseScore: number;\r\n    vectorString: string;\r\n    exploitabilityScore?: number;\r\n    impactScore?: number;\r\n  };\r\n  /** CVSS v2 score */\r\n  cvssV2?: {\r\n    baseScore: number;\r\n    vectorString: string;\r\n  };\r\n  /** Publication date */\r\n  publishedDate: Date;\r\n  /** Last modified date */\r\n  lastModifiedDate: Date;\r\n  /** Affected products */\r\n  affectedProducts: Array<{\r\n    vendor: string;\r\n    product: string;\r\n    versions: string[];\r\n  }>;\r\n  /** References */\r\n  references: Array<{\r\n    url: string;\r\n    source: string;\r\n    tags: string[];\r\n  }>;\r\n  /** CWE mappings */\r\n  cweIds: string[];\r\n  /** Vulnerability configuration */\r\n  configurations?: Array<{\r\n    operator: 'AND' | 'OR';\r\n    criteria: Array<{\r\n      vulnerable: boolean;\r\n      criteria: string;\r\n      matchCriteriaId: string;\r\n    }>;\r\n  }>;\r\n}\r\n\r\n/**\r\n * Vulnerability Scan Result\r\n */\r\nexport interface VulnerabilityScanResult {\r\n  /** Scanner-specific vulnerability ID */\r\n  scannerId: string;\r\n  /** CVE ID if available */\r\n  cveId?: string;\r\n  /** Vulnerability name/title */\r\n  name: string;\r\n  /** Vulnerability description */\r\n  description: string;\r\n  /** Scanner-reported severity */\r\n  severity: string;\r\n  /** CVSS score if available */\r\n  cvssScore?: number;\r\n  /** CVSS vector if available */\r\n  cvssVector?: string;\r\n  /** Affected asset information */\r\n  asset: {\r\n    id: string;\r\n    name: string;\r\n    type: string;\r\n    ipAddress?: string;\r\n    hostname?: string;\r\n  };\r\n  /** Affected service/port */\r\n  service?: {\r\n    port: number;\r\n    protocol: string;\r\n    service: string;\r\n    version?: string;\r\n  };\r\n  /** Scanner information */\r\n  scanner: {\r\n    name: string;\r\n    version: string;\r\n    ruleId: string;\r\n    confidence: number;\r\n  };\r\n  /** Scan timestamp */\r\n  scanTimestamp: Date;\r\n  /** Additional metadata */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Threat Intelligence Vulnerability Data\r\n */\r\nexport interface ThreatIntelVulnerabilityData {\r\n  /** CVE identifier */\r\n  cveId: string;\r\n  /** Threat intelligence source */\r\n  source: string;\r\n  /** Exploitation status */\r\n  exploitationStatus: 'not_exploited' | 'proof_of_concept' | 'active_exploitation' | 'weaponized';\r\n  /** Available exploits */\r\n  exploits: Array<{\r\n    name: string;\r\n    type: 'public' | 'private' | 'commercial';\r\n    source: string;\r\n    reliability: number;\r\n    publishedDate: Date;\r\n  }>;\r\n  /** Threat actor activity */\r\n  threatActorActivity?: Array<{\r\n    actor: string;\r\n    campaign?: string;\r\n    firstObserved: Date;\r\n    lastObserved: Date;\r\n    confidence: number;\r\n  }>;\r\n  /** Attack patterns */\r\n  attackPatterns?: Array<{\r\n    techniqueId: string;\r\n    technique: string;\r\n    tactic: string;\r\n  }>;\r\n  /** Intelligence timestamp */\r\n  timestamp: Date;\r\n}\r\n\r\n/**\r\n * Vulnerability Factory\r\n * \r\n * Factory class for creating Vulnerability entities with proper validation and defaults.\r\n * Handles complex vulnerability creation scenarios from various data sources.\r\n * \r\n * Key responsibilities:\r\n * - Create vulnerabilities from scan results\r\n * - Integrate CVE database information\r\n * - Apply threat intelligence data\r\n * - Generate proper risk assessments\r\n * - Handle vulnerability relationships and correlations\r\n */\r\nexport class VulnerabilityFactory {\r\n  /**\r\n   * Create a new Vulnerability with the provided options\r\n   */\r\n  static create(options: CreateVulnerabilityOptions): Vulnerability {\r\n    // If a specific ID was provided, we need to create the vulnerability directly with that ID\r\n    if (options.id) {\r\n      return VulnerabilityFactory.createWithId(options, options.id);\r\n    }\r\n\r\n    return Vulnerability.create(\r\n      options.title,\r\n      options.description,\r\n      options.severity,\r\n      options.category,\r\n      options.type,\r\n      options.affectedAssets,\r\n      options.discovery,\r\n      {\r\n        cveId: options.cveId,\r\n        cvssScores: options.cvssScores,\r\n        confidence: options.confidence,\r\n        exploitation: options.exploitation,\r\n        tags: options.tags,\r\n        attributes: options.attributes,\r\n      }\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Create vulnerability with specific ID (internal helper method)\r\n   */\r\n  private static createWithId(options: CreateVulnerabilityOptions, id: UniqueEntityId): Vulnerability {\r\n    // We need to replicate the logic from Vulnerability.create but with a specific ID\r\n    const riskAssessment = {\r\n      riskScore: 50, // Default risk score, will be calculated properly by the entity\r\n      exploitabilityScore: 50,\r\n      impactScore: 50,\r\n      environmentalFactors: {\r\n        assetCriticality: 50,\r\n        networkExposure: 50,\r\n        dataClassification: 50,\r\n        businessImpact: 50,\r\n      },\r\n      riskFactors: [],\r\n      riskLevel: 'medium' as const,\r\n      businessRisk: {\r\n        financialImpact: 0,\r\n        reputationalImpact: 'medium' as const,\r\n        operationalImpact: 'medium' as const,\r\n        complianceImpact: 'medium' as const,\r\n      },\r\n    };\r\n\r\n    const props = {\r\n      cveId: options.cveId,\r\n      title: options.title.trim(),\r\n      description: options.description.trim(),\r\n      severity: options.severity,\r\n      cvssScores: options.cvssScores || [],\r\n      category: options.category.trim(),\r\n      type: options.type.trim(),\r\n      confidence: options.confidence || ConfidenceLevel.MEDIUM,\r\n      affectedAssets: options.affectedAssets,\r\n      status: VulnerabilityStatus.DISCOVERED,\r\n      discovery: options.discovery,\r\n      exploitation: options.exploitation,\r\n      remediation: {\r\n        status: 'not_started' as const,\r\n        priority: 'medium' as const,\r\n        actions: [],\r\n        workarounds: [],\r\n        timeline: {},\r\n      },\r\n      riskAssessment,\r\n      complianceImpact: [],\r\n      tags: options.tags || [],\r\n      attributes: options.attributes || {},\r\n    };\r\n\r\n    return new Vulnerability(props as any, id);\r\n  }\r\n\r\n  /**\r\n   * Create vulnerability from CVE database entry\r\n   */\r\n  static fromCVEDatabase(\r\n    cveEntry: CVEDatabaseEntry,\r\n    affectedAssets: VulnerabilityAsset[],\r\n    options?: Partial<CreateVulnerabilityOptions>\r\n  ): Vulnerability {\r\n    const cvssScores: CVSSScore[] = [];\r\n    \r\n    // Add CVSS v3.1 score if available\r\n    if (cveEntry.cvssV3) {\r\n      cvssScores.push(CVSSScore.createV3_1(\r\n        cveEntry.cvssV3.baseScore,\r\n        cveEntry.cvssV3.vectorString,\r\n        {\r\n          exploitabilityScore: cveEntry.cvssV3.exploitabilityScore,\r\n          impactScore: cveEntry.cvssV3.impactScore,\r\n          source: 'NVD',\r\n          calculatedAt: cveEntry.lastModifiedDate,\r\n        }\r\n      ));\r\n    }\r\n\r\n    // Add CVSS v2 score if available\r\n    if (cveEntry.cvssV2) {\r\n      cvssScores.push(CVSSScore.create(\r\n        cveEntry.cvssV2.baseScore,\r\n        CVSSVersion.V2,\r\n        cveEntry.cvssV2.vectorString,\r\n        {\r\n          source: 'NVD',\r\n          calculatedAt: cveEntry.lastModifiedDate,\r\n        }\r\n      ));\r\n    }\r\n\r\n    // Determine severity from CVSS scores\r\n    const severity = VulnerabilityFactory.mapCVSSToThreatSeverity(cvssScores);\r\n    \r\n    // Build category from CWE mappings\r\n    const category = VulnerabilityFactory.inferCategoryFromCWE(cveEntry.cweIds);\r\n    \r\n    // Build type from affected products\r\n    const type = VulnerabilityFactory.inferTypeFromProducts(cveEntry.affectedProducts);\r\n\r\n    // Create discovery information\r\n    const discovery: VulnerabilityDiscovery = {\r\n      method: 'threat_intelligence',\r\n      source: 'CVE Database',\r\n      discoveredAt: cveEntry.publishedDate,\r\n      discoveredBy: 'NVD',\r\n      details: `CVE ${cveEntry.cveId} from National Vulnerability Database`,\r\n    };\r\n\r\n    return VulnerabilityFactory.create({\r\n      cveId: cveEntry.cveId,\r\n      title: cveEntry.title,\r\n      description: cveEntry.description,\r\n      severity,\r\n      category,\r\n      type,\r\n      confidence: ConfidenceLevel.HIGH, // CVE entries are generally high confidence\r\n      cvssScores,\r\n      affectedAssets,\r\n      discovery,\r\n      tags: ['cve', 'nvd', ...cveEntry.cweIds.map(cwe => `cwe-${cwe}`)],\r\n      attributes: {\r\n        cveId: cveEntry.cveId,\r\n        publishedDate: cveEntry.publishedDate.toISOString(),\r\n        lastModifiedDate: cveEntry.lastModifiedDate.toISOString(),\r\n        references: cveEntry.references,\r\n        cweIds: cveEntry.cweIds,\r\n        affectedProducts: cveEntry.affectedProducts,\r\n        configurations: cveEntry.configurations,\r\n      },\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create vulnerability from scan result\r\n   */\r\n  static fromScanResult(\r\n    scanResult: VulnerabilityScanResult,\r\n    options?: Partial<CreateVulnerabilityOptions>\r\n  ): Vulnerability {\r\n    // Map scan result to vulnerability asset\r\n    const affectedAsset: VulnerabilityAsset = {\r\n      assetId: scanResult.asset.id,\r\n      assetName: scanResult.asset.name,\r\n      assetType: scanResult.asset.type,\r\n      criticality: 'medium', // Default, should be determined by asset management\r\n      affectedComponents: scanResult.service ? [{\r\n        name: scanResult.service.service,\r\n        version: scanResult.service.version || 'unknown',\r\n        type: 'software',\r\n      }] : [],\r\n      exposure: 'internal', // Default, should be determined by network topology\r\n      businessImpact: [],\r\n    };\r\n\r\n    // Create CVSS score if available\r\n    const cvssScores: CVSSScore[] = [];\r\n    if (scanResult.cvssScore && scanResult.cvssVector) {\r\n      try {\r\n        cvssScores.push(CVSSScore.fromVectorString(\r\n          scanResult.cvssVector,\r\n          scanResult.scanner.name\r\n        ));\r\n      } catch (error) {\r\n        // If vector parsing fails, create basic score\r\n        cvssScores.push(CVSSScore.createV3_1(\r\n          scanResult.cvssScore,\r\n          'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:N', // Placeholder\r\n          { source: scanResult.scanner.name }\r\n        ));\r\n      }\r\n    }\r\n\r\n    // Map scanner severity to threat severity\r\n    const severity = VulnerabilityFactory.mapScannerSeverity(scanResult.severity);\r\n    \r\n    // Infer category and type from scanner data\r\n    const category = VulnerabilityFactory.inferCategoryFromScanResult(scanResult);\r\n    const type = VulnerabilityFactory.inferTypeFromScanResult(scanResult);\r\n\r\n    // Create discovery information\r\n    const discovery: VulnerabilityDiscovery = {\r\n      method: 'automated_scan',\r\n      source: scanResult.scanner.name,\r\n      discoveredAt: scanResult.scanTimestamp,\r\n      discoveredBy: scanResult.scanner.name,\r\n      details: `Discovered by ${scanResult.scanner.name} v${scanResult.scanner.version}`,\r\n      scannerInfo: {\r\n        name: scanResult.scanner.name,\r\n        version: scanResult.scanner.version,\r\n        ruleId: scanResult.scanner.ruleId,\r\n        confidence: scanResult.scanner.confidence,\r\n      },\r\n    };\r\n\r\n    // Map scanner confidence to domain confidence\r\n    const confidence = VulnerabilityFactory.mapScannerConfidence(scanResult.scanner.confidence);\r\n\r\n    return VulnerabilityFactory.create({\r\n      cveId: scanResult.cveId,\r\n      title: scanResult.name,\r\n      description: scanResult.description,\r\n      severity,\r\n      category,\r\n      type,\r\n      confidence,\r\n      cvssScores,\r\n      affectedAssets: [affectedAsset],\r\n      discovery,\r\n      tags: ['scan-result', scanResult.scanner.name.toLowerCase()],\r\n      attributes: {\r\n        scannerId: scanResult.scannerId,\r\n        scanTimestamp: scanResult.scanTimestamp.toISOString(),\r\n        scanner: scanResult.scanner,\r\n        service: scanResult.service,\r\n        metadata: scanResult.metadata,\r\n      },\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create vulnerability with threat intelligence data\r\n   */\r\n  static withThreatIntelligence(\r\n    baseVulnerability: Vulnerability,\r\n    threatIntel: ThreatIntelVulnerabilityData\r\n  ): Vulnerability {\r\n    // Create exploitation information from threat intelligence\r\n    const exploitation: VulnerabilityExploitation = {\r\n      status: threatIntel.exploitationStatus,\r\n      difficulty: VulnerabilityFactory.inferExploitationDifficulty(threatIntel),\r\n      availableExploits: threatIntel.exploits.map(exploit => ({\r\n        name: exploit.name,\r\n        type: exploit.type,\r\n        source: exploit.source,\r\n        reliability: exploit.reliability,\r\n        discovered: exploit.publishedDate,\r\n      })),\r\n      timeline: VulnerabilityFactory.buildExploitationTimeline(threatIntel),\r\n      attackVectors: threatIntel.attackPatterns?.map(pattern => pattern.technique) || [],\r\n      prerequisites: [],\r\n    };\r\n\r\n    // Update tags with threat intelligence information\r\n    const updatedTags = [\r\n      ...baseVulnerability.tags,\r\n      'threat-intelligence',\r\n      threatIntel.source.toLowerCase(),\r\n    ];\r\n\r\n    if (threatIntel.exploitationStatus === 'active_exploitation') {\r\n      updatedTags.push('actively-exploited');\r\n    }\r\n\r\n    if (threatIntel.threatActorActivity && threatIntel.threatActorActivity.length > 0) {\r\n      updatedTags.push('threat-actor-activity');\r\n    }\r\n\r\n    // Update attributes with threat intelligence data\r\n    const updatedAttributes = {\r\n      ...baseVulnerability.attributes,\r\n      threatIntelligence: {\r\n        source: threatIntel.source,\r\n        exploitationStatus: threatIntel.exploitationStatus,\r\n        exploits: threatIntel.exploits,\r\n        threatActorActivity: threatIntel.threatActorActivity,\r\n        attackPatterns: threatIntel.attackPatterns,\r\n        timestamp: threatIntel.timestamp.toISOString(),\r\n      },\r\n    };\r\n\r\n    // Create new vulnerability with updated information\r\n    return VulnerabilityFactory.create({\r\n      id: baseVulnerability.id,\r\n      cveId: baseVulnerability.cveId,\r\n      title: baseVulnerability.title,\r\n      description: baseVulnerability.description,\r\n      severity: baseVulnerability.severity,\r\n      category: baseVulnerability.category,\r\n      type: baseVulnerability.type,\r\n      confidence: baseVulnerability.confidence,\r\n      cvssScores: baseVulnerability.cvssScores,\r\n      affectedAssets: baseVulnerability.affectedAssets,\r\n      discovery: baseVulnerability.discovery,\r\n      exploitation,\r\n      tags: updatedTags,\r\n      attributes: updatedAttributes,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create critical vulnerability alert\r\n   */\r\n  static createCriticalAlert(\r\n    title: string,\r\n    description: string,\r\n    cveId: string,\r\n    affectedAssets: VulnerabilityAsset[],\r\n    options?: Partial<CreateVulnerabilityOptions>\r\n  ): Vulnerability {\r\n    const discovery: VulnerabilityDiscovery = {\r\n      method: 'external_report',\r\n      source: 'Security Alert',\r\n      discoveredAt: new Date(),\r\n      discoveredBy: 'Security Team',\r\n      details: 'Critical vulnerability alert requiring immediate attention',\r\n    };\r\n\r\n    return VulnerabilityFactory.create({\r\n      cveId,\r\n      title,\r\n      description,\r\n      severity: ThreatSeverity.CRITICAL,\r\n      category: 'critical-alert',\r\n      type: 'security-alert',\r\n      confidence: ConfidenceLevel.HIGH,\r\n      affectedAssets,\r\n      discovery,\r\n      tags: ['critical', 'alert', 'immediate-response'],\r\n      attributes: {\r\n        alertType: 'critical',\r\n        requiresImmediateAttention: true,\r\n      },\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create zero-day vulnerability\r\n   */\r\n  static createZeroDay(\r\n    title: string,\r\n    description: string,\r\n    affectedAssets: VulnerabilityAsset[],\r\n    discoveryMethod: 'incident_response' | 'manual_testing' | 'threat_intelligence',\r\n    options?: Partial<CreateVulnerabilityOptions>\r\n  ): Vulnerability {\r\n    const discovery: VulnerabilityDiscovery = {\r\n      method: discoveryMethod,\r\n      source: 'Zero-day Discovery',\r\n      discoveredAt: new Date(),\r\n      discoveredBy: 'Security Research Team',\r\n      details: 'Zero-day vulnerability with no known CVE identifier',\r\n    };\r\n\r\n    const exploitation: VulnerabilityExploitation = {\r\n      status: 'active_exploitation',\r\n      difficulty: 'medium',\r\n      availableExploits: [],\r\n      attackVectors: [],\r\n      prerequisites: [],\r\n    };\r\n\r\n    return VulnerabilityFactory.create({\r\n      title,\r\n      description,\r\n      severity: ThreatSeverity.CRITICAL,\r\n      category: 'zero-day',\r\n      type: 'unknown-vulnerability',\r\n      confidence: ConfidenceLevel.MEDIUM,\r\n      affectedAssets,\r\n      discovery,\r\n      exploitation,\r\n      tags: ['zero-day', 'no-cve', 'active-exploitation'],\r\n      attributes: {\r\n        zeroDay: true,\r\n        noCVE: true,\r\n        discoveryMethod,\r\n      },\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  // Helper methods for data mapping and inference\r\n\r\n  private static mapCVSSToThreatSeverity(cvssScores: CVSSScore[]): ThreatSeverity {\r\n    if (cvssScores.length === 0) {\r\n      return ThreatSeverity.UNKNOWN;\r\n    }\r\n\r\n    const maxScore = Math.max(...cvssScores.map(score => score.baseScore));\r\n    \r\n    if (maxScore >= 9.0) return ThreatSeverity.CRITICAL;\r\n    if (maxScore >= 7.0) return ThreatSeverity.HIGH;\r\n    if (maxScore >= 4.0) return ThreatSeverity.MEDIUM;\r\n    if (maxScore >= 0.1) return ThreatSeverity.LOW;\r\n    return ThreatSeverity.UNKNOWN;\r\n  }\r\n\r\n  private static mapScannerSeverity(severity: string): ThreatSeverity {\r\n    const sev = severity.toLowerCase();\r\n    \r\n    if (sev.includes('critical') || sev.includes('fatal')) return ThreatSeverity.CRITICAL;\r\n    if (sev.includes('high') || sev.includes('severe')) return ThreatSeverity.HIGH;\r\n    if (sev.includes('medium') || sev.includes('moderate')) return ThreatSeverity.MEDIUM;\r\n    if (sev.includes('low') || sev.includes('minor')) return ThreatSeverity.LOW;\r\n    \r\n    return ThreatSeverity.UNKNOWN;\r\n  }\r\n\r\n  private static mapScannerConfidence(confidence: number): ConfidenceLevel {\r\n    if (confidence >= 95) return ConfidenceLevel.CONFIRMED;\r\n    if (confidence >= 85) return ConfidenceLevel.VERY_HIGH;\r\n    if (confidence >= 70) return ConfidenceLevel.HIGH;\r\n    if (confidence >= 50) return ConfidenceLevel.MEDIUM;\r\n    if (confidence >= 30) return ConfidenceLevel.LOW;\r\n    return ConfidenceLevel.VERY_LOW;\r\n  }\r\n\r\n  private static inferCategoryFromCWE(cweIds: string[]): string {\r\n    // Map common CWE categories\r\n    const categoryMap: Record<string, string> = {\r\n      'CWE-79': 'injection',\r\n      'CWE-89': 'injection',\r\n      'CWE-78': 'injection',\r\n      'CWE-94': 'injection',\r\n      'CWE-287': 'authentication',\r\n      'CWE-285': 'authorization',\r\n      'CWE-284': 'access_control',\r\n      'CWE-200': 'information_disclosure',\r\n      'CWE-119': 'memory_corruption',\r\n      'CWE-120': 'memory_corruption',\r\n      'CWE-787': 'memory_corruption',\r\n      'CWE-22': 'path_traversal',\r\n      'CWE-352': 'csrf',\r\n      'CWE-434': 'file_upload',\r\n    };\r\n\r\n    for (const cweId of cweIds) {\r\n      if (categoryMap[cweId]) {\r\n        return categoryMap[cweId];\r\n      }\r\n    }\r\n\r\n    return 'general';\r\n  }\r\n\r\n  private static inferTypeFromProducts(products: Array<{ vendor: string; product: string; versions: string[] }>): string {\r\n    if (products.length === 0) return 'unknown';\r\n    \r\n    const product = products[0];\r\n    const productName = product.product.toLowerCase();\r\n    \r\n    if (productName.includes('windows') || productName.includes('linux') || productName.includes('macos')) {\r\n      return 'operating_system';\r\n    }\r\n    if (productName.includes('apache') || productName.includes('nginx') || productName.includes('iis')) {\r\n      return 'web_server';\r\n    }\r\n    if (productName.includes('mysql') || productName.includes('postgresql') || productName.includes('oracle')) {\r\n      return 'database';\r\n    }\r\n    if (productName.includes('chrome') || productName.includes('firefox') || productName.includes('safari')) {\r\n      return 'browser';\r\n    }\r\n    \r\n    return 'application';\r\n  }\r\n\r\n  private static inferCategoryFromScanResult(scanResult: VulnerabilityScanResult): string {\r\n    const name = scanResult.name.toLowerCase();\r\n    const description = scanResult.description.toLowerCase();\r\n    \r\n    if (name.includes('sql') || description.includes('sql injection')) return 'injection';\r\n    if (name.includes('xss') || description.includes('cross-site scripting')) return 'injection';\r\n    if (name.includes('rce') || description.includes('remote code execution')) return 'remote_code_execution';\r\n    if (name.includes('auth') || description.includes('authentication')) return 'authentication';\r\n    if (name.includes('privilege') || description.includes('escalation')) return 'privilege_escalation';\r\n    if (name.includes('buffer') || description.includes('overflow')) return 'memory_corruption';\r\n    if (name.includes('path') || description.includes('traversal')) return 'path_traversal';\r\n    if (name.includes('csrf') || description.includes('cross-site request')) return 'csrf';\r\n    \r\n    return 'general';\r\n  }\r\n\r\n  private static inferTypeFromScanResult(scanResult: VulnerabilityScanResult): string {\r\n    if (scanResult.service) {\r\n      return `${scanResult.service.service}_vulnerability`;\r\n    }\r\n    \r\n    const assetType = scanResult.asset.type.toLowerCase();\r\n    return `${assetType}_vulnerability`;\r\n  }\r\n\r\n  private static inferExploitationDifficulty(threatIntel: ThreatIntelVulnerabilityData): 'low' | 'medium' | 'high' {\r\n    const publicExploits = threatIntel.exploits.filter(e => e.type === 'public').length;\r\n    const highReliabilityExploits = threatIntel.exploits.filter(e => e.reliability >= 80).length;\r\n    \r\n    if (publicExploits > 0 && highReliabilityExploits > 0) return 'low';\r\n    if (publicExploits > 0 || highReliabilityExploits > 0) return 'medium';\r\n    return 'high';\r\n  }\r\n\r\n  private static buildExploitationTimeline(threatIntel: ThreatIntelVulnerabilityData) {\r\n    const exploitDates = threatIntel.exploits.map(e => e.publishedDate);\r\n    const actorDates = threatIntel.threatActorActivity?.flatMap(a => [a.firstObserved, a.lastObserved]) || [];\r\n    \r\n    return {\r\n      firstExploitPublished: exploitDates.length > 0 ? new Date(Math.min(...exploitDates.map(d => d.getTime()))) : undefined,\r\n      firstExploitationObserved: actorDates.length > 0 ? new Date(Math.min(...actorDates.map(d => d.getTime()))) : undefined,\r\n      lastExploitationObserved: actorDates.length > 0 ? new Date(Math.max(...actorDates.map(d => d.getTime()))) : undefined,\r\n    };\r\n  }\r\n}"], "version": 3}