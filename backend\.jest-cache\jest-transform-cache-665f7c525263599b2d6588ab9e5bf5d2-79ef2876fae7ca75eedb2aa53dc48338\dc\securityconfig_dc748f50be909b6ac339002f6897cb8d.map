{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\security.config.ts", "mappings": ";;;AAAA,2CAA4C;AAmI5C,qCAAqC;AACrC,MAAM,eAAe,GAAG,CAAC,KAAyB,EAAE,YAAY,GAAG,KAAK,EAAW,EAAE,CACnF,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC;AAE1C,MAAM,WAAW,GAAG,CAAC,KAAyB,EAAE,YAAoB,EAAU,EAAE,CAC9E,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC;AAE7D,MAAM,aAAa,GAAG,CAAC,KAAyB,EAAE,YAAsB,EAAY,EAAE,CACpF,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;AAEnE,MAAM,YAAY,GAAG,GAAY,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,YAAY,CAAC;AAE7E,8BAA8B;AAC9B,MAAM,oBAAoB,GAAG,CAAC,uBAAuB,CAAC,CAAC;AACvD,MAAM,oBAAoB,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;AAC1F,MAAM,oBAAoB,GAAG,CAAC,cAAc,EAAE,eAAe,EAAE,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;AACvG,MAAM,0BAA0B,GAAG,CAAC,kBAAkB,EAAE,UAAU,EAAE,iBAAiB,CAAC,CAAC;AACvF,MAAM,sBAAsB,GAA6B;IACvD,UAAU,EAAE,CAAC,QAAQ,CAAC;IACtB,QAAQ,EAAE,CAAC,QAAQ,EAAE,iBAAiB,CAAC;IACvC,SAAS,EAAE,CAAC,QAAQ,CAAC;IACrB,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;IACrC,UAAU,EAAE,CAAC,QAAQ,CAAC;IACtB,OAAO,EAAE,CAAC,QAAQ,CAAC;IACnB,SAAS,EAAE,CAAC,QAAQ,CAAC;IACrB,QAAQ,EAAE,CAAC,QAAQ,CAAC;IACpB,QAAQ,EAAE,CAAC,QAAQ,CAAC;CACrB,CAAC;AAEF,MAAM,0BAA0B,GAA6B;IAC3D,MAAM,EAAE,CAAC,MAAM,CAAC;IAChB,UAAU,EAAE,CAAC,MAAM,CAAC;IACpB,WAAW,EAAE,CAAC,MAAM,CAAC;IACrB,OAAO,EAAE,CAAC,MAAM,CAAC;IACjB,GAAG,EAAE,CAAC,MAAM,CAAC;CACd,CAAC;AAEF;;;GAGG;AACU,QAAA,cAAc,GAAG,IAAA,mBAAU,EAAC,UAAU,EAAE,GAAmB,EAAE,CAAC,CAAC;IAC1E,IAAI,EAAE;QACJ,OAAO,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,IAAI,CAAC;QAC3D,MAAM,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,oBAAoB,CAAC;QACvE,OAAO,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,oBAAoB,CAAC;QACzE,cAAc,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,EAAE,oBAAoB,CAAC;QACxF,WAAW,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAC7D,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,KAAK,CAAC,EAAE,WAAW;QACpE,iBAAiB,EAAE,KAAK;QACxB,oBAAoB,EAAE,GAAG;KAC1B;IAED,OAAO,EAAE;QACP,GAAG,EAAE;YACH,OAAO,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;YAC7D,UAAU,EAAE,sBAAsB;YAClC,UAAU,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;YACpE,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC;SAClD;QACD,IAAI,EAAE;YACJ,OAAO,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;YAC9D,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,EAAE,QAAQ,CAAC,EAAE,SAAS;YAC9E,iBAAiB,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;YACnF,OAAO,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;SAC/D;QACD,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,IAAI,MAAM;QAC7D,kBAAkB,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,EAAE,IAAI,CAAC;QACvF,SAAS,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,EAAE,IAAI,CAAC;QACpE,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,IAAI,iCAAiC;QAC5F,iBAAiB,EAAE,0BAA0B;KAC9C;IAED,SAAS,EAAE;QACT,OAAO,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,EAAE,IAAI,CAAC;QACjE,QAAQ,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI;QAC/D,GAAG,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,EAAE,GAAG,CAAC;QACtD,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,IAAI,gCAAgC;QAC9E,eAAe,EAAE,IAAI;QACrB,aAAa,EAAE,KAAK;QACpB,sBAAsB,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;QAC3F,kBAAkB,EAAE,KAAK;QACzB,YAAY,EAAE,CAAC,GAAQ,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE;KACnC;IAED,YAAY,EAAE;QACZ,OAAO,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,EAAE,IAAI,CAAC;QACrE,QAAQ,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,EAAE,KAAK,CAAC;QAClE,GAAG,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,EAAE,IAAI,CAAC;QACzD,OAAO,EAAE,yBAAyB;QAClC,eAAe,EAAE,IAAI;QACrB,aAAa,EAAE,KAAK;KACrB;IAED,UAAU,EAAE;QACV,SAAS,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QAC/D,oBAAoB,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QACvF,qBAAqB,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QACzF,SAAS,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QAC/D,oBAAoB,EAAE,YAAY,EAAE;QACpC,SAAS,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,EAAE,OAAO,CAAC,EAAE,MAAM;KAC9E;IAED,MAAM,EAAE;QACN,WAAW,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,EAAE,QAAQ,CAAC,EAAE,OAAO;QAChF,gBAAgB,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,EAAE,0BAA0B,CAAC;QAChG,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,IAAI,WAAW;QAC7D,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,IAAI,QAAQ;QACnE,gBAAgB,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QAC3E,iBAAiB,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;KAC7E;IAED,IAAI,EAAE;QACJ,OAAO,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QACrD,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,OAAO;QACtD,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,cAAc;QAC7D,aAAa,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC;QACzC,MAAM,EAAE,YAAY,EAAE;QACtB,QAAQ,EAAE,QAAQ;KACnB;IAED,QAAQ,EAAE;QACR,OAAO,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QAC1D,SAAS,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,EAAE,CAAC;QACzD,SAAS,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,EAAE,CAAC;QACzD,UAAU,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;KACxD;IAED,MAAM,EAAE;QACN,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,KAAK;QACxC,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,KAAK;QACpD,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,KAAK;QACxC,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,KAAK;KACvC;IAED,UAAU,EAAE;QACV,OAAO,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QACpE,iBAAiB,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,EAAE,IAAI,CAAC;QAC5E,yBAAyB,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QACpF,kBAAkB,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;QACjF,2BAA2B,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,EAAE,EAAE,CAAC;KAC3F;IAED,MAAM,EAAE;QACN,OAAO,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QACjE,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,WAAW;QACxD,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,IAAI,QAAQ;QAC1D,eAAe,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;QACpE,eAAe,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,EAAE,IAAI,CAAC;QACrE,QAAQ,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;KAC5D;CACF,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\security.config.ts"], "sourcesContent": ["import { registerAs } from '@nestjs/config';\r\n\r\n// Type definitions for better type safety\r\ninterface CorsConfig {\r\n  enabled: boolean;\r\n  origin: string[];\r\n  methods: string[];\r\n  allowedHeaders: string[];\r\n  credentials: boolean;\r\n  maxAge: number;\r\n  preflightContinue: boolean;\r\n  optionsSuccessStatus: number;\r\n}\r\n\r\ninterface CspConfig {\r\n  enabled: boolean;\r\n  directives: Record<string, string[]>;\r\n  reportOnly: boolean;\r\n  reportUri?: string | undefined;\r\n}\r\n\r\ninterface HstsConfig {\r\n  enabled: boolean;\r\n  maxAge: number;\r\n  includeSubDomains: boolean;\r\n  preload: boolean;\r\n}\r\n\r\ninterface HeadersConfig {\r\n  csp: CspConfig;\r\n  hsts: HstsConfig;\r\n  frameOptions: string;\r\n  contentTypeOptions: boolean;\r\n  xssFilter: boolean;\r\n  referrerPolicy: string;\r\n  permissionsPolicy: Record<string, string[]>;\r\n}\r\n\r\ninterface RateLimitConfig {\r\n  enabled: boolean;\r\n  windowMs: number;\r\n  max: number;\r\n  message: string;\r\n  standardHeaders: boolean;\r\n  legacyHeaders: boolean;\r\n  skipSuccessfulRequests?: boolean;\r\n  skipFailedRequests: boolean;\r\n  keyGenerator: (req: any) => string;\r\n}\r\n\r\ninterface ApiRateLimitConfig {\r\n  enabled: boolean;\r\n  windowMs: number;\r\n  max: number;\r\n  message: string;\r\n  standardHeaders: boolean;\r\n  legacyHeaders: boolean;\r\n}\r\n\r\ninterface ValidationConfig {\r\n  whitelist: boolean;\r\n  forbidNonWhitelisted: boolean;\r\n  skipMissingProperties: boolean;\r\n  transform: boolean;\r\n  disableErrorMessages: boolean;\r\n  maxLength: number;\r\n}\r\n\r\ninterface UploadConfig {\r\n  maxFileSize: number;\r\n  allowedMimeTypes: string[];\r\n  destination: string;\r\n  tempDestination: string;\r\n  virusScanEnabled: boolean;\r\n  quarantineEnabled: boolean;\r\n}\r\n\r\ninterface CsrfConfig {\r\n  enabled: boolean;\r\n  cookieName: string;\r\n  headerName: string;\r\n  ignoreMethods: string[];\r\n  secure: boolean;\r\n  sameSite: 'strict' | 'lax' | 'none';\r\n}\r\n\r\ninterface IpFilterConfig {\r\n  enabled: boolean;\r\n  whitelist: string[];\r\n  blacklist: string[];\r\n  trustProxy: boolean;\r\n}\r\n\r\ninterface LimitsConfig {\r\n  json: string;\r\n  urlencoded: string;\r\n  text: string;\r\n  raw: string;\r\n}\r\n\r\ninterface MonitoringConfig {\r\n  enabled: boolean;\r\n  logSecurityEvents: boolean;\r\n  alertOnSuspiciousActivity: boolean;\r\n  blockSuspiciousIPs: boolean;\r\n  suspiciousActivityThreshold: number;\r\n}\r\n\r\ninterface ApiKeyConfig {\r\n  enabled: boolean;\r\n  headerName: string;\r\n  queryParam: string;\r\n  allowQueryParam: boolean;\r\n  rateLimitPerKey: number;\r\n  logUsage: boolean;\r\n}\r\n\r\nexport interface SecurityConfig {\r\n  cors: CorsConfig;\r\n  headers: HeadersConfig;\r\n  rateLimit: RateLimitConfig;\r\n  apiRateLimit: ApiRateLimitConfig;\r\n  validation: ValidationConfig;\r\n  upload: UploadConfig;\r\n  csrf: CsrfConfig;\r\n  ipFilter: IpFilterConfig;\r\n  limits: LimitsConfig;\r\n  monitoring: MonitoringConfig;\r\n  apiKey: ApiKeyConfig;\r\n}\r\n\r\n// Utility functions for cleaner code\r\nconst parseEnvBoolean = (value: string | undefined, defaultValue = false): boolean => \r\n  value ? value === 'true' : defaultValue;\r\n\r\nconst parseEnvInt = (value: string | undefined, defaultValue: number): number =>\r\n  value ? parseInt(value, 10) || defaultValue : defaultValue;\r\n\r\nconst parseEnvArray = (value: string | undefined, defaultArray: string[]): string[] =>\r\n  value ? value.split(',').map(item => item.trim()) : defaultArray;\r\n\r\nconst isProduction = (): boolean => process.env['NODE_ENV'] === 'production';\r\n\r\n// Default values as constants\r\nconst DEFAULT_CORS_ORIGINS = ['http://localhost:3001'];\r\nconst DEFAULT_CORS_METHODS = ['GET', 'HEAD', 'PUT', 'PATCH', 'POST', 'DELETE', 'OPTIONS'];\r\nconst DEFAULT_CORS_HEADERS = ['Content-Type', 'Authorization', 'X-Requested-With', 'X-Correlation-ID'];\r\nconst DEFAULT_ALLOWED_MIME_TYPES = ['application/json', 'text/csv', 'application/xml'];\r\nconst DEFAULT_CSP_DIRECTIVES: Record<string, string[]> = {\r\n  defaultSrc: [\"'self'\"],\r\n  styleSrc: [\"'self'\", \"'unsafe-inline'\"],\r\n  scriptSrc: [\"'self'\"],\r\n  imgSrc: [\"'self'\", \"data:\", \"https:\"],\r\n  connectSrc: [\"'self'\"],\r\n  fontSrc: [\"'self'\"],\r\n  objectSrc: [\"'none'\"],\r\n  mediaSrc: [\"'self'\"],\r\n  frameSrc: [\"'none'\"],\r\n};\r\n\r\nconst DEFAULT_PERMISSIONS_POLICY: Record<string, string[]> = {\r\n  camera: ['none'],\r\n  microphone: ['none'],\r\n  geolocation: ['none'],\r\n  payment: ['none'],\r\n  usb: ['none'],\r\n};\r\n\r\n/**\r\n * Security configuration for headers, CORS, and protection mechanisms\r\n * Provides comprehensive security settings for the application\r\n */\r\nexport const securityConfig = registerAs('security', (): SecurityConfig => ({\r\n  cors: {\r\n    enabled: parseEnvBoolean(process.env['CORS_ENABLED'], true),\r\n    origin: parseEnvArray(process.env['CORS_ORIGIN'], DEFAULT_CORS_ORIGINS),\r\n    methods: parseEnvArray(process.env['CORS_METHODS'], DEFAULT_CORS_METHODS),\r\n    allowedHeaders: parseEnvArray(process.env['CORS_ALLOWED_HEADERS'], DEFAULT_CORS_HEADERS),\r\n    credentials: parseEnvBoolean(process.env['CORS_CREDENTIALS']),\r\n    maxAge: parseEnvInt(process.env['CORS_MAX_AGE'], 86400), // 24 hours\r\n    preflightContinue: false,\r\n    optionsSuccessStatus: 204,\r\n  },\r\n\r\n  headers: {\r\n    csp: {\r\n      enabled: parseEnvBoolean(process.env['SECURITY_CSP_ENABLED']),\r\n      directives: DEFAULT_CSP_DIRECTIVES,\r\n      reportOnly: parseEnvBoolean(process.env['SECURITY_CSP_REPORT_ONLY']),\r\n      reportUri: process.env['SECURITY_CSP_REPORT_URI'],\r\n    },\r\n    hsts: {\r\n      enabled: parseEnvBoolean(process.env['SECURITY_HSTS_ENABLED']),\r\n      maxAge: parseEnvInt(process.env['SECURITY_HSTS_MAX_AGE'], 31536000), // 1 year\r\n      includeSubDomains: parseEnvBoolean(process.env['SECURITY_HSTS_INCLUDE_SUBDOMAINS']),\r\n      preload: parseEnvBoolean(process.env['SECURITY_HSTS_PRELOAD']),\r\n    },\r\n    frameOptions: process.env['SECURITY_FRAME_OPTIONS'] || 'DENY',\r\n    contentTypeOptions: parseEnvBoolean(process.env['SECURITY_CONTENT_TYPE_OPTIONS'], true),\r\n    xssFilter: parseEnvBoolean(process.env['SECURITY_XSS_FILTER'], true),\r\n    referrerPolicy: process.env['SECURITY_REFERRER_POLICY'] || 'strict-origin-when-cross-origin',\r\n    permissionsPolicy: DEFAULT_PERMISSIONS_POLICY,\r\n  },\r\n\r\n  rateLimit: {\r\n    enabled: parseEnvBoolean(process.env['RATE_LIMIT_ENABLED'], true),\r\n    windowMs: parseEnvInt(process.env['RATE_LIMIT_TTL'], 60) * 1000,\r\n    max: parseEnvInt(process.env['RATE_LIMIT_LIMIT'], 100),\r\n    message: process.env['RATE_LIMIT_MESSAGE'] || 'Too many requests from this IP',\r\n    standardHeaders: true,\r\n    legacyHeaders: false,\r\n    skipSuccessfulRequests: parseEnvBoolean(process.env['RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS']),\r\n    skipFailedRequests: false,\r\n    keyGenerator: (req: any) => req.ip,\r\n  },\r\n\r\n  apiRateLimit: {\r\n    enabled: parseEnvBoolean(process.env['API_RATE_LIMIT_ENABLED'], true),\r\n    windowMs: parseEnvInt(process.env['API_RATE_LIMIT_WINDOW'], 60000),\r\n    max: parseEnvInt(process.env['API_RATE_LIMIT_MAX'], 1000),\r\n    message: 'API rate limit exceeded',\r\n    standardHeaders: true,\r\n    legacyHeaders: false,\r\n  },\r\n\r\n  validation: {\r\n    whitelist: parseEnvBoolean(process.env['VALIDATION_WHITELIST']),\r\n    forbidNonWhitelisted: parseEnvBoolean(process.env['VALIDATION_FORBID_NON_WHITELISTED']),\r\n    skipMissingProperties: parseEnvBoolean(process.env['VALIDATION_SKIP_MISSING_PROPERTIES']),\r\n    transform: parseEnvBoolean(process.env['VALIDATION_TRANSFORM']),\r\n    disableErrorMessages: isProduction(),\r\n    maxLength: parseEnvInt(process.env['VALIDATION_MAX_LENGTH'], 1000000), // 1MB\r\n  },\r\n\r\n  upload: {\r\n    maxFileSize: parseEnvInt(process.env['UPLOAD_MAX_FILE_SIZE'], 10485760), // 10MB\r\n    allowedMimeTypes: parseEnvArray(process.env['UPLOAD_ALLOWED_TYPES'], DEFAULT_ALLOWED_MIME_TYPES),\r\n    destination: process.env['UPLOAD_DESTINATION'] || './uploads',\r\n    tempDestination: process.env['UPLOAD_TEMP_DESTINATION'] || './temp',\r\n    virusScanEnabled: parseEnvBoolean(process.env['UPLOAD_VIRUS_SCAN_ENABLED']),\r\n    quarantineEnabled: parseEnvBoolean(process.env['UPLOAD_QUARANTINE_ENABLED']),\r\n  },\r\n\r\n  csrf: {\r\n    enabled: parseEnvBoolean(process.env['CSRF_ENABLED']),\r\n    cookieName: process.env['CSRF_COOKIE_NAME'] || '_csrf',\r\n    headerName: process.env['CSRF_HEADER_NAME'] || 'x-csrf-token',\r\n    ignoreMethods: ['GET', 'HEAD', 'OPTIONS'],\r\n    secure: isProduction(),\r\n    sameSite: 'strict',\r\n  },\r\n\r\n  ipFilter: {\r\n    enabled: parseEnvBoolean(process.env['IP_FILTER_ENABLED']),\r\n    whitelist: parseEnvArray(process.env['IP_WHITELIST'], []),\r\n    blacklist: parseEnvArray(process.env['IP_BLACKLIST'], []),\r\n    trustProxy: parseEnvBoolean(process.env['TRUST_PROXY']),\r\n  },\r\n\r\n  limits: {\r\n    json: process.env['LIMIT_JSON'] || '1mb',\r\n    urlencoded: process.env['LIMIT_URLENCODED'] || '1mb',\r\n    text: process.env['LIMIT_TEXT'] || '1mb',\r\n    raw: process.env['LIMIT_RAW'] || '1mb',\r\n  },\r\n\r\n  monitoring: {\r\n    enabled: parseEnvBoolean(process.env['SECURITY_MONITORING_ENABLED']),\r\n    logSecurityEvents: parseEnvBoolean(process.env['SECURITY_LOG_EVENTS'], true),\r\n    alertOnSuspiciousActivity: parseEnvBoolean(process.env['SECURITY_ALERT_SUSPICIOUS']),\r\n    blockSuspiciousIPs: parseEnvBoolean(process.env['SECURITY_BLOCK_SUSPICIOUS_IPS']),\r\n    suspiciousActivityThreshold: parseEnvInt(process.env['SECURITY_SUSPICIOUS_THRESHOLD'], 10),\r\n  },\r\n\r\n  apiKey: {\r\n    enabled: parseEnvBoolean(process.env['API_KEY_SECURITY_ENABLED']),\r\n    headerName: process.env['API_KEY_HEADER'] || 'X-API-Key',\r\n    queryParam: process.env['API_KEY_QUERY_PARAM'] || 'apikey',\r\n    allowQueryParam: parseEnvBoolean(process.env['API_KEY_ALLOW_QUERY']),\r\n    rateLimitPerKey: parseEnvInt(process.env['API_KEY_RATE_LIMIT'], 1000),\r\n    logUsage: parseEnvBoolean(process.env['API_KEY_LOG_USAGE']),\r\n  },\r\n}));"], "version": 3}