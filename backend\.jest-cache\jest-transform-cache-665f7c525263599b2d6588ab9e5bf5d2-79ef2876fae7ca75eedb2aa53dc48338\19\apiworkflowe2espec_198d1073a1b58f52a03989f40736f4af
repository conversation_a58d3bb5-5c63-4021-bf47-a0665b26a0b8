42d7238981e2d43d13e5eba0e4914fed
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const supertest_1 = __importDefault(require("supertest"));
const app_module_1 = require("../../app.module");
describe('API Workflow (e2e)', () => {
    let app;
    let authToken;
    let userId;
    beforeAll(async () => {
        const moduleFixture = await testing_1.Test.createTestingModule({
            imports: [
                config_1.ConfigModule.forRoot({
                    isGlobal: true,
                    envFilePath: '.env.test',
                }),
                app_module_1.AppModule,
            ],
        }).compile();
        app = moduleFixture.createNestApplication();
        await app.init();
        // Setup authentication for protected endpoints
        const authResponse = await (0, supertest_1.default)(app.getHttpServer())
            .post('/api/v1/auth/register')
            .send({
            email: '<EMAIL>',
            password: 'SecurePassword123!',
            name: 'API Test User',
        });
        authToken = authResponse.body.data.tokens.accessToken;
        userId = authResponse.body.data.user.id;
    });
    afterAll(async () => {
        await app.close();
    });
    describe('API Versioning', () => {
        it('should handle v1 API requests', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/health')
                .expect(200);
            expect(response.body).toEqual({
                status: 'ok',
                timestamp: expect.any(String),
                uptime: expect.any(Number),
                version: '1.0.0',
                environment: 'test',
            });
        });
        it('should handle v2 API requests', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v2/health')
                .expect(200);
            expect(response.body).toEqual({
                status: 'healthy',
                timestamp: expect.any(String),
                uptime: expect.any(Number),
                version: '2.0.0',
                environment: 'test',
                services: expect.any(Object),
            });
        });
        it('should include version headers', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/health')
                .set('Accept', 'application/vnd.api+json;version=1.0')
                .expect(200);
            expect(response.headers['x-api-version']).toBe('1.0');
        });
        it('should handle version negotiation', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/health')
                .set('X-API-Version', '1.0')
                .expect(200);
            expect(response.body.version).toBe('1.0.0');
        });
        it('should reject unsupported versions', async () => {
            await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v999/health')
                .expect(404);
        });
    });
    describe('Request/Response Flow', () => {
        it('should handle GET requests with query parameters', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/health')
                .query({
                detailed: 'true',
                format: 'json',
            })
                .expect(200);
            expect(response.body.status).toBe('ok');
        });
        it('should handle POST requests with JSON body', async () => {
            const testData = {
                name: 'Test Item',
                description: 'Test Description',
                category: 'test',
            };
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/items')
                .set('Authorization', `Bearer ${authToken}`)
                .send(testData)
                .expect(201);
            expect(response.body).toEqual({
                success: true,
                data: expect.objectContaining({
                    id: expect.any(String),
                    ...testData,
                    createdAt: expect.any(String),
                    updatedAt: expect.any(String),
                }),
                message: 'Item created successfully',
                timestamp: expect.any(String),
            });
        });
        it('should handle PUT requests for updates', async () => {
            // First create an item
            const createResponse = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/items')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                name: 'Original Item',
                description: 'Original Description',
                category: 'test',
            })
                .expect(201);
            const itemId = createResponse.body.data.id;
            // Then update it
            const updateData = {
                name: 'Updated Item',
                description: 'Updated Description',
                category: 'updated',
            };
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .put(`/api/v1/test/items/${itemId}`)
                .set('Authorization', `Bearer ${authToken}`)
                .send(updateData)
                .expect(200);
            expect(response.body.data).toEqual(expect.objectContaining({
                id: itemId,
                ...updateData,
                updatedAt: expect.any(String),
            }));
        });
        it('should handle DELETE requests', async () => {
            // First create an item
            const createResponse = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/items')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                name: 'Item to Delete',
                description: 'Will be deleted',
                category: 'test',
            })
                .expect(201);
            const itemId = createResponse.body.data.id;
            // Then delete it
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .delete(`/api/v1/test/items/${itemId}`)
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(response.body.message).toBe('Item deleted successfully');
            // Verify it's deleted
            await (0, supertest_1.default)(app.getHttpServer())
                .get(`/api/v1/test/items/${itemId}`)
                .set('Authorization', `Bearer ${authToken}`)
                .expect(404);
        });
    });
    describe('Pagination Workflow', () => {
        beforeAll(async () => {
            // Create multiple items for pagination testing
            const items = Array.from({ length: 25 }, (_, i) => ({
                name: `Test Item ${i + 1}`,
                description: `Description for item ${i + 1}`,
                category: 'pagination-test',
            }));
            for (const item of items) {
                await (0, supertest_1.default)(app.getHttpServer())
                    .post('/api/v1/test/items')
                    .set('Authorization', `Bearer ${authToken}`)
                    .send(item);
            }
        });
        it('should handle default pagination', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/test/items')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(response.body).toEqual({
                success: true,
                data: expect.any(Array),
                message: 'Items retrieved successfully',
                timestamp: expect.any(String),
                pagination: {
                    page: 1,
                    limit: 10,
                    total: expect.any(Number),
                    totalPages: expect.any(Number),
                    hasNext: expect.any(Boolean),
                    hasPrev: false,
                    offset: 0,
                },
                metadata: expect.any(Object),
            });
            expect(response.body.data.length).toBeLessThanOrEqual(10);
        });
        it('should handle custom pagination parameters', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/test/items')
                .query({
                page: 2,
                limit: 5,
            })
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(response.body.pagination).toEqual({
                page: 2,
                limit: 5,
                total: expect.any(Number),
                totalPages: expect.any(Number),
                hasNext: expect.any(Boolean),
                hasPrev: true,
                offset: 5,
            });
            expect(response.body.data.length).toBeLessThanOrEqual(5);
        });
        it('should handle sorting parameters', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/test/items')
                .query({
                sortBy: 'name',
                sortOrder: 'asc',
                limit: 5,
            })
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            const items = response.body.data;
            expect(items.length).toBeGreaterThan(0);
            // Verify sorting
            for (let i = 1; i < items.length; i++) {
                expect(items[i].name >= items[i - 1].name).toBe(true);
            }
        });
        it('should handle filtering parameters', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/test/items')
                .query({
                filter_category: 'pagination-test',
                filter_name: 'Test Item 1',
            })
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(response.body.data.length).toBeGreaterThan(0);
            response.body.data.forEach(item => {
                expect(item.category).toBe('pagination-test');
                expect(item.name).toContain('Test Item 1');
            });
        });
    });
    describe('Validation Workflow', () => {
        it('should validate required fields', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/items')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                description: 'Missing name field',
            })
                .expect(400);
            expect(response.body.message).toContain('name');
        });
        it('should validate field types', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/items')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                name: 123, // Should be string
                description: 'Type validation test',
                category: 'test',
            })
                .expect(400);
            expect(response.body.message).toContain('name must be a string');
        });
        it('should validate field lengths', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/items')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                name: 'a'.repeat(256), // Too long
                description: 'Length validation test',
                category: 'test',
            })
                .expect(400);
            expect(response.body.message).toContain('name must be shorter than');
        });
        it('should sanitize input data', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/items')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                name: '<script>alert("xss")</script>Clean Name',
                description: 'XSS test description',
                category: 'test',
            })
                .expect(201);
            expect(response.body.data.name).toBe('Clean Name');
            expect(response.body.data.name).not.toContain('<script>');
        });
    });
    describe('Error Handling Workflow', () => {
        it('should handle 404 errors for non-existent resources', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/test/items/non-existent-id')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(404);
            expect(response.body).toEqual({
                statusCode: 404,
                message: 'Item not found',
                error: 'Not Found',
                timestamp: expect.any(String),
                path: '/api/v1/test/items/non-existent-id',
                method: 'GET',
                requestId: expect.any(String),
            });
        });
        it('should handle authorization errors', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/test/items')
                .expect(401);
            expect(response.body.statusCode).toBe(401);
            expect(response.body.message).toContain('Unauthorized');
        });
        it('should handle permission errors', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/admin/users')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(403);
            expect(response.body.statusCode).toBe(403);
            expect(response.body.message).toContain('Forbidden');
        });
        it('should handle rate limiting', async () => {
            // Make multiple rapid requests to trigger rate limiting
            const requests = Array.from({ length: 20 }, () => (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/health'));
            const responses = await Promise.all(requests.map(req => req.then(res => ({ status: res.status, headers: res.headers }))
                .catch(err => ({ status: err.response?.status || 500, headers: {} }))));
            // Some requests should be rate limited
            const rateLimitedResponses = responses.filter(r => r.status === 429);
            if (rateLimitedResponses.length > 0) {
                expect(rateLimitedResponses[0].headers['x-ratelimit-limit']).toBeDefined();
                expect(rateLimitedResponses[0].headers['x-ratelimit-remaining']).toBeDefined();
            }
        });
    });
    describe('Content Negotiation', () => {
        it('should handle JSON content type', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/health')
                .set('Accept', 'application/json')
                .expect(200);
            expect(response.headers['content-type']).toMatch(/application\/json/);
        });
        it('should handle different API versions in Accept header', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/health')
                .set('Accept', 'application/vnd.api+json;version=1.0')
                .expect(200);
            expect(response.body.version).toBe('1.0.0');
        });
        it('should handle compression', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/health')
                .set('Accept-Encoding', 'gzip, deflate')
                .expect(200);
            // Response should be successful regardless of compression
            expect(response.body.status).toBe('ok');
        });
    });
    describe('Security Headers', () => {
        it('should include security headers in responses', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/health')
                .expect(200);
            // Check for common security headers
            expect(response.headers).toBeDefined();
            // In a real implementation, you would check for specific headers like:
            // expect(response.headers['x-frame-options']).toBe('DENY');
            // expect(response.headers['x-content-type-options']).toBe('nosniff');
        });
        it('should handle CORS preflight requests', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .options('/api/v1/health')
                .set('Origin', 'http://localhost:3000')
                .set('Access-Control-Request-Method', 'GET')
                .expect(200);
            expect(response.headers['access-control-allow-origin']).toBeDefined();
        });
    });
    describe('Monitoring and Observability', () => {
        it('should include correlation IDs in responses', async () => {
            const correlationId = 'test-correlation-123';
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/health')
                .set('X-Correlation-ID', correlationId)
                .expect(200);
            // In a real implementation, the correlation ID would be included in response headers
            expect(response.headers).toBeDefined();
        });
        it('should track request metrics', async () => {
            const startTime = Date.now();
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/health')
                .expect(200);
            const endTime = Date.now();
            const duration = endTime - startTime;
            expect(duration).toBeLessThan(1000); // Should respond quickly
            expect(response.body.uptime).toBeGreaterThan(0);
        });
        it('should provide health check information', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/health')
                .expect(200);
            expect(response.body).toEqual({
                status: 'ok',
                timestamp: expect.any(String),
                uptime: expect.any(Number),
                version: '1.0.0',
                environment: 'test',
            });
        });
        it('should provide detailed health information', async () => {
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get('/api/v1/health/detailed')
                .expect(200);
            expect(response.body).toEqual({
                status: 'ok',
                timestamp: expect.any(String),
                uptime: expect.any(Number),
                version: '1.0.0',
                environment: 'test',
                services: {
                    database: expect.any(String),
                    redis: expect.any(String),
                    external_apis: expect.any(String),
                },
                memory: expect.any(Object),
                cpu: expect.any(Object),
            });
        });
    });
    describe('Performance', () => {
        it('should handle concurrent requests efficiently', async () => {
            const startTime = Date.now();
            const requests = Array.from({ length: 10 }, () => (0, supertest_1.default)(app.getHttpServer()).get('/api/v1/health'));
            const responses = await Promise.all(requests);
            const endTime = Date.now();
            const duration = endTime - startTime;
            // All requests should succeed
            responses.forEach(response => {
                expect(response.status).toBe(200);
            });
            // Should handle concurrent requests efficiently
            expect(duration).toBeLessThan(2000);
        });
        it('should handle large payloads efficiently', async () => {
            const largePayload = {
                name: 'Large Item',
                description: 'A'.repeat(1000), // Large description
                category: 'performance-test',
                metadata: Array.from({ length: 100 }, (_, i) => ({
                    key: `key${i}`,
                    value: `value${i}`,
                })),
            };
            const startTime = Date.now();
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/items')
                .set('Authorization', `Bearer ${authToken}`)
                .send(largePayload)
                .expect(201);
            const endTime = Date.now();
            const duration = endTime - startTime;
            expect(response.body.data.name).toBe(largePayload.name);
            expect(duration).toBeLessThan(2000); // Should handle large payloads efficiently
        });
    });
    describe('Data Consistency', () => {
        it('should maintain data consistency across operations', async () => {
            // Create an item
            const createResponse = await (0, supertest_1.default)(app.getHttpServer())
                .post('/api/v1/test/items')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                name: 'Consistency Test Item',
                description: 'Testing data consistency',
                category: 'consistency',
            })
                .expect(201);
            const itemId = createResponse.body.data.id;
            // Retrieve the item
            const getResponse = await (0, supertest_1.default)(app.getHttpServer())
                .get(`/api/v1/test/items/${itemId}`)
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(getResponse.body.data).toEqual(createResponse.body.data);
            // Update the item
            const updateResponse = await (0, supertest_1.default)(app.getHttpServer())
                .put(`/api/v1/test/items/${itemId}`)
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                name: 'Updated Consistency Test Item',
                description: 'Updated description',
                category: 'updated-consistency',
            })
                .expect(200);
            // Verify the update
            const getUpdatedResponse = await (0, supertest_1.default)(app.getHttpServer())
                .get(`/api/v1/test/items/${itemId}`)
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(getUpdatedResponse.body.data.name).toBe('Updated Consistency Test Item');
            expect(getUpdatedResponse.body.data.updatedAt).not.toBe(createResponse.body.data.updatedAt);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************