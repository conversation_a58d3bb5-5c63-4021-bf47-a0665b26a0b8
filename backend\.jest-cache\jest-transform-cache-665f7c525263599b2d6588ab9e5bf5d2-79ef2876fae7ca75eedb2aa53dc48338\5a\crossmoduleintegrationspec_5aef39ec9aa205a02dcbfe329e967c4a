987f54b20d3a7a8749da39ea3bbb9f23
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g;
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const event_emitter_1 = require("@nestjs/event-emitter");
const integration_test_base_1 = require("../base/integration-test.base");
const test_configuration_service_1 = require("../config/test-configuration.service");
const test_data_service_1 = require("../fixtures/test-data.service");
// Import all modules for cross-module testing
const workflow_execution_module_1 = require("../../workflow-execution/workflow-execution.module");
const monitoring_module_1 = require("../../monitoring/monitoring.module");
const workflow_templates_module_1 = require("../../templates/workflow-templates.module");
// Import services for integration testing
const workflow_execution_engine_service_1 = require("../../workflow-execution/services/workflow-execution-engine.service");
const metrics_collector_service_1 = require("../../monitoring/metrics-collector.service");
const health_check_service_1 = require("../../monitoring/health-check.service");
const intelligent_alerting_service_1 = require("../../monitoring/intelligent-alerting.service");
const performance_monitoring_service_1 = require("../../monitoring/performance-monitoring.service");
// Import entities for data validation
const workflow_execution_entity_1 = require("../../workflow-execution/entities/workflow-execution.entity");
const metric_snapshot_entity_1 = require("../../monitoring/entities/metric-snapshot.entity");
const performance_metric_entity_1 = require("../../monitoring/entities/performance-metric.entity");
const health_check_result_entity_1 = require("../../monitoring/entities/health-check-result.entity");
const alert_incident_entity_1 = require("../../monitoring/entities/alert-incident.entity");
/**
 * Cross-Module Integration Tests
 *
 * Comprehensive cross-module integration testing including:
 * - Workflow Execution ↔ Monitoring integration
 * - Workflow Execution ↔ Templates integration
 * - Monitoring ↔ Alerting integration
 * - Data flow validation across module boundaries
 * - Event propagation and handling across modules
 * - Performance impact of cross-module operations
 * - Error handling and recovery across modules
 */
describe('Cross-Module Integration Tests', () => {
    let testSuite;
    beforeEach(async () => {
        testSuite = new CrossModuleIntegrationTest();
        await testSuite.beforeEach();
    });
    afterEach(async () => {
        await testSuite.afterEach();
    });
    describe('Workflow Execution ↔ Monitoring Integration', () => {
        it('should collect metrics during workflow execution', async () => {
            await testSuite.testWorkflowExecutionMetricsCollection();
        });
        it('should trigger health checks during workflow execution', async () => {
            await testSuite.testWorkflowExecutionHealthChecks();
        });
        it('should generate alerts for workflow execution failures', async () => {
            await testSuite.testWorkflowExecutionAlerting();
        });
        it('should track performance metrics for workflow steps', async () => {
            await testSuite.testWorkflowStepPerformanceTracking();
        });
    });
    describe('Workflow Execution ↔ Templates Integration', () => {
        it('should validate template compatibility with execution engine', async () => {
            await testSuite.testTemplateExecutionCompatibility();
        });
        it('should handle template updates during active executions', async () => {
            await testSuite.testTemplateUpdateHandling();
        });
        it('should validate template input/output schemas', async () => {
            await testSuite.testTemplateSchemaValidation();
        });
    });
    describe('Data Flow Validation', () => {
        it('should maintain data consistency across module boundaries', async () => {
            await testSuite.testCrossModuleDataConsistency();
        });
        it('should handle concurrent operations across modules', async () => {
            await testSuite.testConcurrentCrossModuleOperations();
        });
        it('should validate transaction boundaries across modules', async () => {
            await testSuite.testCrossModuleTransactions();
        });
    });
    describe('Event Propagation', () => {
        it('should propagate events correctly across modules', async () => {
            await testSuite.testCrossModuleEventPropagation();
        });
        it('should handle event ordering and dependencies', async () => {
            await testSuite.testEventOrderingAndDependencies();
        });
        it('should recover from event handling failures', async () => {
            await testSuite.testEventHandlingFailureRecovery();
        });
    });
    describe('Performance Impact', () => {
        it('should measure cross-module operation performance', async () => {
            await testSuite.testCrossModulePerformance();
        });
        it('should validate resource usage during cross-module operations', async () => {
            await testSuite.testCrossModuleResourceUsage();
        });
        it('should handle load balancing across modules', async () => {
            await testSuite.testCrossModuleLoadBalancing();
        });
    });
    describe('Error Handling and Recovery', () => {
        it('should handle cascading failures across modules', async () => {
            await testSuite.testCascadingFailureHandling();
        });
        it('should implement circuit breakers for cross-module calls', async () => {
            await testSuite.testCrossModuleCircuitBreakers();
        });
        it('should provide graceful degradation across modules', async () => {
            await testSuite.testCrossModuleGracefulDegradation();
        });
    });
});
class CrossModuleIntegrationTest extends integration_test_base_1.IntegrationTestBase {
    async createTestingModule() {
        return testing_1.Test.createTestingModule({
            imports: [
                config_1.ConfigModule.forRoot({
                    isGlobal: true,
                    envFilePath: '.env.test',
                }),
                typeorm_1.TypeOrmModule.forRootAsync({
                    useFactory: (configService) => configService.getTestDatabaseConfig(),
                    inject: [test_configuration_service_1.TestConfigurationService],
                }),
                event_emitter_1.EventEmitterModule.forRoot(),
                workflow_execution_module_1.WorkflowExecutionModule,
                monitoring_module_1.MonitoringModule,
                workflow_templates_module_1.WorkflowTemplatesModule,
            ],
            providers: [
                test_configuration_service_1.TestConfigurationService,
                test_data_service_1.TestDataService,
            ],
        }).compile();
    }
    async setupTestData() {
        // Get services from all modules
        this.executionEngine = this.testingModule.get(workflow_execution_engine_service_1.WorkflowExecutionEngineService);
        this.metricsCollector = this.testingModule.get(metrics_collector_service_1.MetricsCollectorService);
        this.healthCheckService = this.testingModule.get(health_check_service_1.HealthCheckService);
        this.alertingService = this.testingModule.get(intelligent_alerting_service_1.IntelligentAlertingService);
        this.performanceMonitoring = this.testingModule.get(performance_monitoring_service_1.PerformanceMonitoringService);
        // Seed test data across all modules
        await this.testDataService.seedTestData({
            templates: 5,
            executions: 10,
            contexts: 2,
            metrics: 50,
            healthChecks: 20,
            alertRules: 10,
            incidents: 5,
        });
    }
    async testWorkflowExecutionMetricsCollection() {
        // Get test template
        const templates = await this.testDataService.generateWorkflowTemplates(1);
        const template = templates[0];
        // Clear existing metrics
        this.clearEventHistory();
        // Start workflow execution
        const execution = await this.executionEngine.startExecution({
            templateId: template.id,
            executionName: 'Metrics Collection Test',
            inputData: { test: 'metrics' },
            triggeredBy: 'test-user',
            executionConfig: { enableMetrics: true },
        });
        // Wait for execution to progress
        await this.sleep(2000);
        // Verify metrics were collected
        const metricsSnapshot = await this.dataSource
            .getRepository(metric_snapshot_entity_1.MetricSnapshot)
            .find({
            where: {
                metricName: 'workflow_executions_started',
            },
            order: { timestamp: 'DESC' },
            take: 1,
        });
        expect(metricsSnapshot).toHaveLength(1);
        expect(metricsSnapshot[0].labels).toContain(template.id);
        // Verify performance metrics were recorded
        const performanceMetrics = await this.dataSource
            .getRepository(performance_metric_entity_1.PerformanceMetric)
            .find({
            where: {
                component: 'workflow_engine',
            },
            order: { timestamp: 'DESC' },
            take: 5,
        });
        expect(performanceMetrics.length).toBeGreaterThan(0);
        // Verify events were emitted for metrics collection
        const metricsEvents = this.getEventHistory('metrics.recorded');
        expect(metricsEvents.length).toBeGreaterThan(0);
    }
    async testWorkflowExecutionHealthChecks() {
        // Register workflow-specific health check
        this.healthCheckService.registerHealthCheck('workflow_execution_engine', {
            execute: async () => ({
                status: 'healthy',
                message: 'Workflow execution engine operational',
                details: {
                    activeExecutions: 0,
                    queueSize: 0,
                },
            }),
            timeout: 5000,
            critical: true,
            tags: ['workflow', 'execution'],
        });
        // Execute health check
        const healthResult = await this.healthCheckService.executeHealthCheck('workflow_execution_engine');
        expect(healthResult.status).toBe('healthy');
        expect(healthResult.details).toBeDefined();
        // Verify health check result was persisted
        const persistedResults = await this.dataSource
            .getRepository(health_check_result_entity_1.HealthCheckResult)
            .find({
            where: {
                checkName: 'workflow_execution_engine',
            },
            order: { timestamp: 'DESC' },
            take: 1,
        });
        expect(persistedResults).toHaveLength(1);
        expect(persistedResults[0].status).toBe('healthy');
    }
    async testWorkflowExecutionAlerting() {
        // Create alert rule for workflow failures
        const alertRule = await this.dataSource
            .getRepository('AlertRule')
            .save({
            name: 'Workflow Execution Failure Alert',
            description: 'Alert when workflow execution fails',
            metricName: 'workflow_executions_failed',
            condition: {
                type: 'threshold',
                operator: 'greater_than',
                value: 0,
                window: '5m',
            },
            severity: 'critical',
            enabled: true,
            escalation: {
                levels: [
                    { delay: 0, channels: ['email'], recipients: ['<EMAIL>'] },
                ],
            },
        });
        // Simulate workflow failure by triggering metric
        this.metricsCollector.incrementCounter('workflow_executions_failed', {
            template_id: 'test-template',
            error_type: 'TestError',
        });
        // Wait for alert processing
        await this.sleep(1000);
        // Verify alert incident was created
        const incidents = await this.dataSource
            .getRepository(alert_incident_entity_1.AlertIncident)
            .find({
            where: {
                ruleId: alertRule.id,
            },
            order: { startTime: 'DESC' },
            take: 1,
        });
        expect(incidents).toHaveLength(1);
        expect(incidents[0].status).toBe('active');
        expect(incidents[0].severity).toBe('critical');
    }
    async testWorkflowStepPerformanceTracking() {
        // Create template with multiple steps
        const template = await this.testDataService.templateRepository.save({
            name: 'Performance Tracking Test',
            description: 'Template for performance tracking',
            definition: {
                steps: [
                    { id: 'step1', type: 'notification', config: { message: 'Step 1' } },
                    { id: 'step2', type: 'validation', config: { rules: [] } },
                    { id: 'step3', type: 'data_transformation', config: { type: 'map' } },
                ],
            },
            createdBy: 'test-system',
            updatedBy: 'test-system',
        });
        // Start execution with performance monitoring
        const execution = await this.executionEngine.startExecution({
            templateId: template.id,
            executionName: 'Performance Tracking Test',
            inputData: { performance: 'test' },
            triggeredBy: 'test-user',
            executionConfig: {
                enableMetrics: true,
                enableTracing: true,
            },
        });
        // Wait for execution to complete
        await this.waitFor(async () => {
            const updated = await this.dataSource
                .getRepository(workflow_execution_entity_1.WorkflowExecution)
                .findOne({ where: { id: execution.id } });
            return updated?.status === 'completed' || updated?.status === 'failed';
        }, 15000);
        // Verify performance metrics were recorded for each step
        const stepMetrics = await this.dataSource
            .getRepository(performance_metric_entity_1.PerformanceMetric)
            .find({
            where: {
                component: 'workflow_engine',
                operation: 'step_execution',
            },
            order: { timestamp: 'DESC' },
        });
        expect(stepMetrics.length).toBeGreaterThanOrEqual(3); // One for each step
        // Verify step-specific performance data
        const finalExecution = await this.dataSource
            .getRepository(workflow_execution_entity_1.WorkflowExecution)
            .findOne({ where: { id: execution.id } });
        expect(finalExecution.performanceMetrics).toBeDefined();
        expect(finalExecution.performanceMetrics.totalSteps).toBe(3);
        expect(finalExecution.performanceMetrics.avgStepDuration).toBeGreaterThan(0);
    }
    async testCrossModuleDataConsistency() {
        // Start workflow execution
        const templates = await this.testDataService.generateWorkflowTemplates(1);
        const execution = await this.executionEngine.startExecution({
            templateId: templates[0].id,
            executionName: 'Data Consistency Test',
            inputData: { consistency: 'test' },
            triggeredBy: 'test-user',
        });
        // Wait for some execution progress
        await this.sleep(1000);
        // Verify data consistency across modules
        await this.withTransaction(async (manager) => {
            // Check workflow execution exists
            const workflowExecution = await manager.findOne(workflow_execution_entity_1.WorkflowExecution, {
                where: { id: execution.id },
            });
            expect(workflowExecution).toBeDefined();
            // Check corresponding metrics exist
            const relatedMetrics = await manager.find(metric_snapshot_entity_1.MetricSnapshot, {
                where: {
                    metricName: 'workflow_executions_started',
                },
            });
            expect(relatedMetrics.length).toBeGreaterThan(0);
            // Check performance metrics exist
            const performanceMetrics = await manager.find(performance_metric_entity_1.PerformanceMetric, {
                where: {
                    component: 'workflow_engine',
                },
            });
            expect(performanceMetrics.length).toBeGreaterThan(0);
            // Verify referential integrity
            const executionCount = await manager.count(workflow_execution_entity_1.WorkflowExecution);
            const contextCount = await manager.count('WorkflowExecutionContext');
            // Each execution should have at least one context
            expect(contextCount).toBeGreaterThanOrEqual(executionCount);
        });
    }
    async testCrossModuleEventPropagation() {
        this.clearEventHistory();
        // Start workflow execution to trigger cross-module events
        const templates = await this.testDataService.generateWorkflowTemplates(1);
        const execution = await this.executionEngine.startExecution({
            templateId: templates[0].id,
            executionName: 'Event Propagation Test',
            inputData: { events: 'test' },
            triggeredBy: 'test-user',
            executionConfig: { enableMetrics: true },
        });
        // Wait for events to propagate
        await this.sleep(2000);
        // Verify workflow events were emitted
        const workflowEvents = this.getEventHistory('workflow.execution.started');
        expect(workflowEvents).toHaveLength(1);
        expect(workflowEvents[0].args[0].executionId).toBe(execution.id);
        // Verify metrics events were emitted
        const metricsEvents = this.getEventHistory('metrics.recorded');
        expect(metricsEvents.length).toBeGreaterThan(0);
        // Verify monitoring events were emitted
        const monitoringEvents = this.getEventHistory().filter(e => e.event.startsWith('monitoring.') || e.event.startsWith('health.'));
        expect(monitoringEvents.length).toBeGreaterThan(0);
        // Verify event ordering (workflow events should come before metrics events)
        const allEvents = this.getEventHistory().sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
        const workflowEventIndex = allEvents.findIndex(e => e.event === 'workflow.execution.started');
        const metricsEventIndex = allEvents.findIndex(e => e.event === 'metrics.recorded');
        if (workflowEventIndex !== -1 && metricsEventIndex !== -1) {
            expect(workflowEventIndex).toBeLessThan(metricsEventIndex);
        }
    }
    async testCrossModulePerformance() {
        const templates = await this.testDataService.generateWorkflowTemplates(1);
        // Measure performance of cross-module operation
        const { result: execution, duration } = await this.measurePerformance(() => this.executionEngine.startExecution({
            templateId: templates[0].id,
            executionName: 'Performance Test',
            inputData: { performance: 'test' },
            triggeredBy: 'test-user',
            executionConfig: {
                enableMetrics: true,
                enableTracing: true,
            },
        }), 'cross_module_execution_start');
        // Verify performance is within acceptable limits
        expect(duration).toBeLessThan(5000); // 5 seconds max for startup
        // Measure metrics collection performance
        const { duration: metricsCollectionDuration } = await this.measurePerformance(() => this.metricsCollector.recordBusinessMetric({
            name: 'test_metric',
            value: 1,
            type: 'counter',
            category: 'test',
            labels: { test: 'true' },
        }), 'metrics_collection');
        expect(metricsCollectionDuration).toBeLessThan(100); // 100ms max for metrics
        // Get performance statistics
        const executionStats = this.getPerformanceStats('cross_module_execution_start');
        const metricsStats = this.getPerformanceStats('metrics_collection');
        expect(executionStats).toBeDefined();
        expect(metricsStats).toBeDefined();
        // Verify performance consistency
        if (executionStats && executionStats.count > 1) {
            const variance = executionStats.max - executionStats.min;
            expect(variance).toBeLessThan(executionStats.avg * 2); // Variance should be reasonable
        }
    }
    // Additional test method implementations...
    async testTemplateExecutionCompatibility() {
        // Implementation for template compatibility testing
    }
    async testTemplateUpdateHandling() {
        // Implementation for template update handling testing
    }
    async testTemplateSchemaValidation() {
        // Implementation for template schema validation testing
    }
    async testConcurrentCrossModuleOperations() {
        // Implementation for concurrent operations testing
    }
    async testCrossModuleTransactions() {
        // Implementation for transaction boundary testing
    }
    async testEventOrderingAndDependencies() {
        // Implementation for event ordering testing
    }
    async testEventHandlingFailureRecovery() {
        // Implementation for event failure recovery testing
    }
    async testCrossModuleResourceUsage() {
        // Implementation for resource usage testing
    }
    async testCrossModuleLoadBalancing() {
        // Implementation for load balancing testing
    }
    async testCascadingFailureHandling() {
        // Implementation for cascading failure testing
    }
    async testCrossModuleCircuitBreakers() {
        // Implementation for circuit breaker testing
    }
    async testCrossModuleGracefulDegradation() {
        // Implementation for graceful degradation testing
    }
}
__decorate([
    (0, integration_test_base_1.IntegrationTest)('Workflow execution metrics collection integration'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
], CrossModuleIntegrationTest.prototype, "testWorkflowExecutionMetricsCollection", null);
__decorate([
    (0, integration_test_base_1.IntegrationTest)('Workflow execution health checks integration'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_b = typeof Promise !== "undefined" && Promise) === "function" ? _b : Object)
], CrossModuleIntegrationTest.prototype, "testWorkflowExecutionHealthChecks", null);
__decorate([
    (0, integration_test_base_1.IntegrationTest)('Workflow execution alerting integration'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_c = typeof Promise !== "undefined" && Promise) === "function" ? _c : Object)
], CrossModuleIntegrationTest.prototype, "testWorkflowExecutionAlerting", null);
__decorate([
    (0, integration_test_base_1.IntegrationTest)('Workflow step performance tracking integration'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_d = typeof Promise !== "undefined" && Promise) === "function" ? _d : Object)
], CrossModuleIntegrationTest.prototype, "testWorkflowStepPerformanceTracking", null);
__decorate([
    (0, integration_test_base_1.IntegrationTest)('Cross-module data consistency validation'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_e = typeof Promise !== "undefined" && Promise) === "function" ? _e : Object)
], CrossModuleIntegrationTest.prototype, "testCrossModuleDataConsistency", null);
__decorate([
    (0, integration_test_base_1.IntegrationTest)('Cross-module event propagation validation'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_f = typeof Promise !== "undefined" && Promise) === "function" ? _f : Object)
], CrossModuleIntegrationTest.prototype, "testCrossModuleEventPropagation", null);
__decorate([
    (0, integration_test_base_1.IntegrationTest)('Cross-module performance impact measurement'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_g = typeof Promise !== "undefined" && Promise) === "function" ? _g : Object)
], CrossModuleIntegrationTest.prototype, "testCrossModulePerformance", null);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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