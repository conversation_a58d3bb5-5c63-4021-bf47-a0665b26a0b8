c22accbf6c9d659c46c2004c4dfc856a
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const policy_configuration_1 = require("../policy-configuration");
const tenant_id_value_object_1 = require("../../../../../shared-kernel/value-objects/tenant-id.value-object");
const user_id_value_object_1 = require("../../../../../shared-kernel/value-objects/user-id.value-object");
const validation_exception_1 = require("../../../../../shared-kernel/exceptions/validation.exception");
describe('PolicyConfiguration', () => {
    let tenantId;
    let userId;
    let validRuleTemplate;
    beforeEach(() => {
        tenantId = tenant_id_value_object_1.TenantId.create('tenant-123');
        userId = user_id_value_object_1.UserId.create('user-123');
        validRuleTemplate = {
            id: 'template-1',
            name: 'Failed Login Template',
            description: 'Template for detecting failed login attempts',
            category: policy_configuration_1.RuleCategory.AUTHENTICATION,
            template: '{"field":"eventData.attempts","operator":"greater_than","value":{{maxAttempts}}}',
            parameters: [
                {
                    name: 'maxAttempts',
                    type: policy_configuration_1.RuleParameterType.NUMBER,
                    description: 'Maximum number of failed attempts',
                    required: true,
                    defaultValue: 5
                }
            ],
            defaultPriority: 80,
            defaultSeverity: 'HIGH',
            tags: ['authentication', 'brute-force']
        };
    });
    describe('creation', () => {
        it('should create a policy configuration with defaults', () => {
            const config = policy_configuration_1.PolicyConfiguration.create({
                tenantId,
                createdBy: userId
            });
            expect(config).toBeDefined();
            expect(config.tenantId).toBe(tenantId);
            expect(config.createdBy).toBe(userId);
            expect(config.settings).toBeDefined();
            expect(config.ruleTemplates).toBeDefined();
            expect(config.version).toBe('1.0.0');
        });
        it('should have default settings configured', () => {
            const config = policy_configuration_1.PolicyConfiguration.create({
                tenantId,
                createdBy: userId
            });
            const settings = config.settings;
            expect(settings.enableAutomaticExecution).toBe(false);
            expect(settings.requireApprovalForCriticalActions).toBe(true);
            expect(settings.maxRulesPerPolicy).toBe(100);
            expect(settings.auditRetentionDays).toBe(365);
            expect(settings.notificationSettings).toBeDefined();
        });
        it('should include default rule templates', () => {
            const config = policy_configuration_1.PolicyConfiguration.create({
                tenantId,
                createdBy: userId
            });
            expect(config.ruleTemplates.length).toBeGreaterThan(0);
            expect(config.ruleTemplates.some(t => t.category === policy_configuration_1.RuleCategory.AUTHENTICATION)).toBe(true);
            expect(config.ruleTemplates.some(t => t.category === policy_configuration_1.RuleCategory.NETWORK_SECURITY)).toBe(true);
        });
    });
    describe('settings management', () => {
        let config;
        beforeEach(() => {
            config = policy_configuration_1.PolicyConfiguration.create({
                tenantId,
                createdBy: userId
            });
        });
        it('should update settings', () => {
            const updates = {
                enableAutomaticExecution: true,
                maxRulesPerPolicy: 200
            };
            config.updateSettings(updates, userId);
            expect(config.settings.enableAutomaticExecution).toBe(true);
            expect(config.settings.maxRulesPerPolicy).toBe(200);
            expect(config.lastModifiedBy).toBe(userId);
        });
        it('should validate settings updates', () => {
            const invalidUpdates = {
                maxRulesPerPolicy: -1
            };
            expect(() => {
                config.updateSettings(invalidUpdates, userId);
            }).toThrow(validation_exception_1.ValidationException);
        });
        it('should preserve existing settings when updating', () => {
            const originalRetentionDays = config.settings.auditRetentionDays;
            config.updateSettings({ enableAutomaticExecution: true }, userId);
            expect(config.settings.auditRetentionDays).toBe(originalRetentionDays);
            expect(config.settings.enableAutomaticExecution).toBe(true);
        });
    });
    describe('rule template management', () => {
        let config;
        beforeEach(() => {
            config = policy_configuration_1.PolicyConfiguration.create({
                tenantId,
                createdBy: userId
            });
        });
        it('should add a new rule template', () => {
            const initialCount = config.ruleTemplates.length;
            config.addRuleTemplate(validRuleTemplate, userId);
            expect(config.ruleTemplates).toHaveLength(initialCount + 1);
            expect(config.ruleTemplates.find(t => t.id === 'template-1')).toBeDefined();
        });
        it('should throw error when adding duplicate template ID', () => {
            config.addRuleTemplate(validRuleTemplate, userId);
            expect(() => {
                config.addRuleTemplate(validRuleTemplate, userId);
            }).toThrow(validation_exception_1.ValidationException);
        });
        it('should update an existing rule template', () => {
            config.addRuleTemplate(validRuleTemplate, userId);
            config.updateRuleTemplate('template-1', { name: 'Updated Template' }, userId);
            const updatedTemplate = config.ruleTemplates.find(t => t.id === 'template-1');
            expect(updatedTemplate?.name).toBe('Updated Template');
        });
        it('should throw error when updating non-existent template', () => {
            expect(() => {
                config.updateRuleTemplate('non-existent', { name: 'Updated' }, userId);
            }).toThrow(validation_exception_1.ValidationException);
        });
        it('should remove a rule template', () => {
            config.addRuleTemplate(validRuleTemplate, userId);
            const initialCount = config.ruleTemplates.length;
            config.removeRuleTemplate('template-1', userId);
            expect(config.ruleTemplates).toHaveLength(initialCount - 1);
            expect(config.ruleTemplates.find(t => t.id === 'template-1')).toBeUndefined();
        });
        it('should throw error when removing non-existent template', () => {
            expect(() => {
                config.removeRuleTemplate('non-existent', userId);
            }).toThrow(validation_exception_1.ValidationException);
        });
    });
    describe('rule template filtering', () => {
        let config;
        beforeEach(() => {
            config = policy_configuration_1.PolicyConfiguration.create({
                tenantId,
                createdBy: userId
            });
            const authTemplate = { ...validRuleTemplate, category: policy_configuration_1.RuleCategory.AUTHENTICATION };
            const networkTemplate = {
                ...validRuleTemplate,
                id: 'template-2',
                category: policy_configuration_1.RuleCategory.NETWORK_SECURITY
            };
            config.addRuleTemplate(authTemplate, userId);
            config.addRuleTemplate(networkTemplate, userId);
        });
        it('should filter templates by category', () => {
            const authTemplates = config.getRuleTemplatesByCategory(policy_configuration_1.RuleCategory.AUTHENTICATION);
            const networkTemplates = config.getRuleTemplatesByCategory(policy_configuration_1.RuleCategory.NETWORK_SECURITY);
            expect(authTemplates.length).toBeGreaterThan(0);
            expect(networkTemplates.length).toBeGreaterThan(0);
            expect(authTemplates.every(t => t.category === policy_configuration_1.RuleCategory.AUTHENTICATION)).toBe(true);
            expect(networkTemplates.every(t => t.category === policy_configuration_1.RuleCategory.NETWORK_SECURITY)).toBe(true);
        });
        it('should return empty array for category with no templates', () => {
            const customTemplates = config.getRuleTemplatesByCategory(policy_configuration_1.RuleCategory.CUSTOM);
            expect(customTemplates).toHaveLength(0);
        });
    });
    describe('rule generation from templates', () => {
        let config;
        beforeEach(() => {
            config = policy_configuration_1.PolicyConfiguration.create({
                tenantId,
                createdBy: userId
            });
            config.addRuleTemplate(validRuleTemplate, userId);
        });
        it('should generate rule from template with valid parameters', () => {
            const parameters = { maxAttempts: 10 };
            const ruleCondition = config.generateRuleFromTemplate('template-1', parameters);
            expect(ruleCondition).toBeDefined();
            expect(ruleCondition).toContain('10');
            expect(JSON.parse(ruleCondition)).toEqual({
                field: 'eventData.attempts',
                operator: 'greater_than',
                value: 10
            });
        });
        it('should throw error for missing required parameters', () => {
            expect(() => {
                config.generateRuleFromTemplate('template-1', {});
            }).toThrow(validation_exception_1.ValidationException);
        });
        it('should throw error for non-existent template', () => {
            expect(() => {
                config.generateRuleFromTemplate('non-existent', { maxAttempts: 5 });
            }).toThrow(validation_exception_1.ValidationException);
        });
        it('should validate parameter types', () => {
            expect(() => {
                config.generateRuleFromTemplate('template-1', { maxAttempts: 'invalid' });
            }).toThrow(validation_exception_1.ValidationException);
        });
        it('should handle multiple parameters', () => {
            const multiParamTemplate = {
                ...validRuleTemplate,
                id: 'multi-param',
                template: '{"and":[{"field":"eventData.attempts","operator":"greater_than","value":{{maxAttempts}}},{"field":"eventData.sourceIp","operator":"equals","value":{{sourceIp}}}]}',
                parameters: [
                    {
                        name: 'maxAttempts',
                        type: policy_configuration_1.RuleParameterType.NUMBER,
                        description: 'Max attempts',
                        required: true
                    },
                    {
                        name: 'sourceIp',
                        type: policy_configuration_1.RuleParameterType.IP_ADDRESS,
                        description: 'Source IP',
                        required: true
                    }
                ]
            };
            config.addRuleTemplate(multiParamTemplate, userId);
            const parameters = { maxAttempts: 5, sourceIp: '***********' };
            const ruleCondition = config.generateRuleFromTemplate('multi-param', parameters);
            const parsed = JSON.parse(ruleCondition);
            expect(parsed.and).toHaveLength(2);
            expect(parsed.and[0].value).toBe(5);
            expect(parsed.and[1].value).toBe('***********');
        });
    });
    describe('parameter validation', () => {
        let config;
        beforeEach(() => {
            config = policy_configuration_1.PolicyConfiguration.create({
                tenantId,
                createdBy: userId
            });
        });
        it('should validate string parameters', () => {
            const stringTemplate = {
                ...validRuleTemplate,
                id: 'string-template',
                parameters: [{
                        name: 'stringParam',
                        type: policy_configuration_1.RuleParameterType.STRING,
                        description: 'String parameter',
                        required: true
                    }]
            };
            config.addRuleTemplate(stringTemplate, userId);
            expect(() => {
                config.generateRuleFromTemplate('string-template', { stringParam: 123 });
            }).toThrow(validation_exception_1.ValidationException);
        });
        it('should validate IP address parameters', () => {
            const ipTemplate = {
                ...validRuleTemplate,
                id: 'ip-template',
                parameters: [{
                        name: 'ipParam',
                        type: policy_configuration_1.RuleParameterType.IP_ADDRESS,
                        description: 'IP parameter',
                        required: true
                    }]
            };
            config.addRuleTemplate(ipTemplate, userId);
            expect(() => {
                config.generateRuleFromTemplate('ip-template', { ipParam: 'invalid-ip' });
            }).toThrow(validation_exception_1.ValidationException);
            // Valid IP should work
            expect(() => {
                config.generateRuleFromTemplate('ip-template', { ipParam: '***********' });
            }).not.toThrow();
        });
        it('should validate email parameters', () => {
            const emailTemplate = {
                ...validRuleTemplate,
                id: 'email-template',
                parameters: [{
                        name: 'emailParam',
                        type: policy_configuration_1.RuleParameterType.EMAIL,
                        description: 'Email parameter',
                        required: true
                    }]
            };
            config.addRuleTemplate(emailTemplate, userId);
            expect(() => {
                config.generateRuleFromTemplate('email-template', { emailParam: 'invalid-email' });
            }).toThrow(validation_exception_1.ValidationException);
            // Valid email should work
            expect(() => {
                config.generateRuleFromTemplate('email-template', { emailParam: '<EMAIL>' });
            }).not.toThrow();
        });
        it('should validate enum parameters', () => {
            const enumTemplate = {
                ...validRuleTemplate,
                id: 'enum-template',
                parameters: [{
                        name: 'enumParam',
                        type: policy_configuration_1.RuleParameterType.ENUM,
                        description: 'Enum parameter',
                        required: true,
                        options: ['option1', 'option2', 'option3']
                    }]
            };
            config.addRuleTemplate(enumTemplate, userId);
            expect(() => {
                config.generateRuleFromTemplate('enum-template', { enumParam: 'invalid-option' });
            }).toThrow(validation_exception_1.ValidationException);
            // Valid option should work
            expect(() => {
                config.generateRuleFromTemplate('enum-template', { enumParam: 'option1' });
            }).not.toThrow();
        });
    });
    describe('rule condition validation', () => {
        let config;
        beforeEach(() => {
            config = policy_configuration_1.PolicyConfiguration.create({
                tenantId,
                createdBy: userId
            });
        });
        it('should validate valid rule conditions', () => {
            const validConditions = [
                '{"field":"test","operator":"equals","value":"test"}',
                '{"and":[{"field":"a","operator":"equals","value":"1"},{"field":"b","operator":"equals","value":"2"}]}',
                '{"or":[{"field":"a","operator":"equals","value":"1"},{"field":"b","operator":"equals","value":"2"}]}',
                '{"not":{"field":"test","operator":"equals","value":"test"}}'
            ];
            validConditions.forEach(condition => {
                expect(config.validateRuleCondition(condition)).toBe(true);
            });
        });
        it('should reject invalid rule conditions', () => {
            const invalidConditions = [
                'invalid json',
                '{}',
                '{"field":"test"}',
                '{"operator":"equals"}',
                '{"value":"test"}',
                '{"field":"test","operator":"invalid","value":"test"}',
                '{"and":"not an array"}'
            ];
            invalidConditions.forEach(condition => {
                expect(config.validateRuleCondition(condition)).toBe(false);
            });
        });
    });
    describe('template validation', () => {
        let config;
        beforeEach(() => {
            config = policy_configuration_1.PolicyConfiguration.create({
                tenantId,
                createdBy: userId
            });
        });
        it('should validate template properties', () => {
            const invalidTemplate = { ...validRuleTemplate, id: '' };
            expect(() => {
                config.addRuleTemplate(invalidTemplate, userId);
            }).toThrow(validation_exception_1.ValidationException);
        });
        it('should validate template parameters', () => {
            const templateWithInvalidParam = {
                ...validRuleTemplate,
                parameters: [{
                        name: '',
                        type: policy_configuration_1.RuleParameterType.STRING,
                        description: 'Invalid param',
                        required: true
                    }]
            };
            expect(() => {
                config.addRuleTemplate(templateWithInvalidParam, userId);
            }).toThrow(validation_exception_1.ValidationException);
        });
        it('should validate template condition JSON', () => {
            const templateWithInvalidCondition = {
                ...validRuleTemplate,
                template: 'invalid json'
            };
            expect(() => {
                config.addRuleTemplate(templateWithInvalidCondition, userId);
            }).toThrow(validation_exception_1.ValidationException);
        });
    });
    describe('metrics', () => {
        let config;
        beforeEach(() => {
            config = policy_configuration_1.PolicyConfiguration.create({
                tenantId,
                createdBy: userId
            });
        });
        it('should return policy metrics', () => {
            const metrics = config.getMetrics();
            expect(metrics).toBeDefined();
            expect(typeof metrics.totalPolicies).toBe('number');
            expect(typeof metrics.activePolicies).toBe('number');
            expect(typeof metrics.totalRules).toBe('number');
            expect(typeof metrics.activeRules).toBe('number');
            expect(Array.isArray(metrics.topViolatedRules)).toBe(true);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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