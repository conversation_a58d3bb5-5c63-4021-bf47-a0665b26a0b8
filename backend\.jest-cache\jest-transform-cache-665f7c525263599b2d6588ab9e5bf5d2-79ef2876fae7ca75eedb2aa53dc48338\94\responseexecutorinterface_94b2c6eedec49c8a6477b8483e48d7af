d76db512b74f42302f28275a257f6a69
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ParameterType = exports.ActionCategory = exports.RollbackComplexity = exports.RollbackStepType = exports.ResponseErrorCategory = exports.ImpactLevel = exports.MitigationLevel = exports.ResponseMode = exports.ResponsePriority = void 0;
/**
 * Response Priority Enum
 */
var ResponsePriority;
(function (ResponsePriority) {
    ResponsePriority["LOW"] = "LOW";
    ResponsePriority["MEDIUM"] = "MEDIUM";
    ResponsePriority["HIGH"] = "HIGH";
    ResponsePriority["CRITICAL"] = "CRITICAL";
    ResponsePriority["EMERGENCY"] = "EMERGENCY";
})(ResponsePriority || (exports.ResponsePriority = ResponsePriority = {}));
/**
 * Response Mode Enum
 */
var ResponseMode;
(function (ResponseMode) {
    ResponseMode["AUTOMATIC"] = "AUTOMATIC";
    ResponseMode["SEMI_AUTOMATIC"] = "SEMI_AUTOMATIC";
    ResponseMode["MANUAL"] = "MANUAL";
    ResponseMode["SIMULATION"] = "SIMULATION";
    ResponseMode["DRY_RUN"] = "DRY_RUN";
})(ResponseMode || (exports.ResponseMode = ResponseMode = {}));
/**
 * Mitigation Level Enum
 */
var MitigationLevel;
(function (MitigationLevel) {
    MitigationLevel["NONE"] = "NONE";
    MitigationLevel["MINIMAL"] = "MINIMAL";
    MitigationLevel["PARTIAL"] = "PARTIAL";
    MitigationLevel["SUBSTANTIAL"] = "SUBSTANTIAL";
    MitigationLevel["COMPLETE"] = "COMPLETE";
})(MitigationLevel || (exports.MitigationLevel = MitigationLevel = {}));
/**
 * Impact Level Enum
 */
var ImpactLevel;
(function (ImpactLevel) {
    ImpactLevel["NONE"] = "NONE";
    ImpactLevel["LOW"] = "LOW";
    ImpactLevel["MEDIUM"] = "MEDIUM";
    ImpactLevel["HIGH"] = "HIGH";
    ImpactLevel["CRITICAL"] = "CRITICAL";
})(ImpactLevel || (exports.ImpactLevel = ImpactLevel = {}));
/**
 * Response Error Category Enum
 */
var ResponseErrorCategory;
(function (ResponseErrorCategory) {
    ResponseErrorCategory["AUTHENTICATION"] = "AUTHENTICATION";
    ResponseErrorCategory["AUTHORIZATION"] = "AUTHORIZATION";
    ResponseErrorCategory["CONFIGURATION"] = "CONFIGURATION";
    ResponseErrorCategory["NETWORK"] = "NETWORK";
    ResponseErrorCategory["RESOURCE"] = "RESOURCE";
    ResponseErrorCategory["TIMEOUT"] = "TIMEOUT";
    ResponseErrorCategory["VALIDATION"] = "VALIDATION";
    ResponseErrorCategory["EXECUTION"] = "EXECUTION";
    ResponseErrorCategory["ROLLBACK"] = "ROLLBACK";
    ResponseErrorCategory["SYSTEM"] = "SYSTEM";
})(ResponseErrorCategory || (exports.ResponseErrorCategory = ResponseErrorCategory = {}));
/**
 * Rollback Step Type Enum
 */
var RollbackStepType;
(function (RollbackStepType) {
    RollbackStepType["CONFIGURATION_RESTORE"] = "CONFIGURATION_RESTORE";
    RollbackStepType["SERVICE_RESTART"] = "SERVICE_RESTART";
    RollbackStepType["RULE_REMOVAL"] = "RULE_REMOVAL";
    RollbackStepType["POLICY_REVERT"] = "POLICY_REVERT";
    RollbackStepType["NETWORK_RESTORE"] = "NETWORK_RESTORE";
    RollbackStepType["DATA_RESTORE"] = "DATA_RESTORE";
    RollbackStepType["PERMISSION_RESTORE"] = "PERMISSION_RESTORE";
    RollbackStepType["CUSTOM"] = "CUSTOM";
})(RollbackStepType || (exports.RollbackStepType = RollbackStepType = {}));
/**
 * Rollback Complexity Enum
 */
var RollbackComplexity;
(function (RollbackComplexity) {
    RollbackComplexity["SIMPLE"] = "SIMPLE";
    RollbackComplexity["MODERATE"] = "MODERATE";
    RollbackComplexity["COMPLEX"] = "COMPLEX";
    RollbackComplexity["VERY_COMPLEX"] = "VERY_COMPLEX";
    RollbackComplexity["IRREVERSIBLE"] = "IRREVERSIBLE";
})(RollbackComplexity || (exports.RollbackComplexity = RollbackComplexity = {}));
/**
 * Action Category Enum
 */
var ActionCategory;
(function (ActionCategory) {
    ActionCategory["BLOCKING"] = "BLOCKING";
    ActionCategory["ISOLATION"] = "ISOLATION";
    ActionCategory["REMEDIATION"] = "REMEDIATION";
    ActionCategory["NOTIFICATION"] = "NOTIFICATION";
    ActionCategory["INVESTIGATION"] = "INVESTIGATION";
    ActionCategory["MONITORING"] = "MONITORING";
    ActionCategory["RECOVERY"] = "RECOVERY";
    ActionCategory["PREVENTION"] = "PREVENTION";
})(ActionCategory || (exports.ActionCategory = ActionCategory = {}));
/**
 * Parameter Type Enum
 */
var ParameterType;
(function (ParameterType) {
    ParameterType["STRING"] = "STRING";
    ParameterType["NUMBER"] = "NUMBER";
    ParameterType["BOOLEAN"] = "BOOLEAN";
    ParameterType["ARRAY"] = "ARRAY";
    ParameterType["OBJECT"] = "OBJECT";
    ParameterType["IP_ADDRESS"] = "IP_ADDRESS";
    ParameterType["URL"] = "URL";
    ParameterType["EMAIL"] = "EMAIL";
    ParameterType["DURATION"] = "DURATION";
    ParameterType["ENUM"] = "ENUM";
})(ParameterType || (exports.ParameterType = ParameterType = {}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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