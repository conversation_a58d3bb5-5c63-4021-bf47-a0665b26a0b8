{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\app.service.ts", "mappings": ";;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,2CAA+C;AAE/C;;;GAGG;AAEI,IAAM,UAAU,GAAhB,MAAM,UAAU;IACrB,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;IAAG,CAAC;IAE7D;;OAEG;IACH,kBAAkB;QAChB,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc;YAC1C,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YAChD,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;QAEf,OAAO;YACL,IAAI,EAAE,4CAA4C;YAClD,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC;YACvD,WAAW,EAAE,6LAA6L;YAC1M,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC;YAC9D,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE;YACxB,SAAS,EAAE,SAAS,CAAC,WAAW,EAAE;YAClC,QAAQ,EAAE;gBACR,0BAA0B;gBAC1B,qBAAqB;gBACrB,0BAA0B;gBAC1B,sBAAsB;gBACtB,oBAAoB;gBACpB,sBAAsB;aACvB;YACD,GAAG,EAAE;gBACH,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC;gBACpD,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,CAAC;gBACnD,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,YAAY,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI;aACxF;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,SAAS;QACP,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAC1C,MAAM,WAAW,GAAG,WAAW,CAAC,SAAS,GAAG,WAAW,CAAC,QAAQ,CAAC;QACjE,MAAM,UAAU,GAAG,WAAW,CAAC,QAAQ,CAAC;QAExC,OAAO;YACL,MAAM,EAAE,SAAS;YACjB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE;YACxB,MAAM,EAAE;gBACN,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,EAAE,KAAK;gBAC7D,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,EAAE,KAAK;gBAC/D,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,UAAU,GAAG,WAAW,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG;gBACpE,IAAI,EAAE;oBACJ,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG;oBAChE,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG;iBACnE;gBACD,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG;gBACpE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG;aAC3D;YACD,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC;YACvD,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC;YAC9D,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,WAAW,EAAE,OAAO,CAAC,OAAO;SAC7B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC;YACvD,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;YACzE,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,EAAE,SAAS,CAAC;YAC1D,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,EAAE,SAAS,CAAC;YAC1D,WAAW,EAAE,OAAO,CAAC,OAAO;YAC5B,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,YAAY,EAAE,OAAO,CAAC,IAAI;YAC1B,YAAY,EAAE;gBACZ,MAAM,EAAE,IAAI,CAAC,gBAAgB,EAAE;gBAC/B,UAAU,EAAE,IAAI,CAAC,oBAAoB,EAAE;gBACvC,IAAI,EAAE,OAAO,CAAC,OAAO;aACtB;YACD,SAAS,EAAE;gBACT,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;gBAChE,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,EAAE,aAAa,CAAC;gBAC/D,EAAE,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC;aACxC;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,gBAAgB;QACtB,IAAI,CAAC;YACH,6DAA6D;YAC7D,OAAO,QAAQ,CAAC,CAAC,cAAc;QACjC,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,oBAAoB;QAC1B,IAAI,CAAC;YACH,6DAA6D;YAC7D,OAAO,OAAO,CAAC,CAAC,cAAc;QAChC,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;CACF,CAAA;AAlHY,gCAAU;qBAAV,UAAU;IADtB,IAAA,mBAAU,GAAE;yDAEiC,sBAAa,oBAAb,sBAAa;GAD9C,UAAU,CAkHtB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\app.service.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\n\r\n/**\r\n * Root application service\r\n * Provides application information, status, and version details\r\n */\r\n@Injectable()\r\nexport class AppService {\r\n  constructor(private readonly configService: ConfigService) {}\r\n\r\n  /**\r\n   * Get application information\r\n   */\r\n  getApplicationInfo() {\r\n    const startTime = process.env.APP_START_TIME \r\n      ? new Date(parseInt(process.env.APP_START_TIME))\r\n      : new Date();\r\n\r\n    return {\r\n      name: 'Sentinel Vulnerability Assessment Platform',\r\n      version: this.configService.get('APP_VERSION', '1.0.0'),\r\n      description: 'Enterprise-grade vulnerability assessment and threat intelligence platform providing comprehensive security analysis, predictive risk assessment, and automated mitigation recommendations.',\r\n      environment: this.configService.get('NODE_ENV', 'development'),\r\n      timestamp: new Date().toISOString(),\r\n      uptime: process.uptime(),\r\n      startTime: startTime.toISOString(),\r\n      features: [\r\n        'Vulnerability Assessment',\r\n        'Threat Intelligence',\r\n        'Predictive Risk Analysis',\r\n        'Automated Mitigation',\r\n        'Security Analytics',\r\n        'Real-time Monitoring'\r\n      ],\r\n      api: {\r\n        version: this.configService.get('API_VERSION', 'v1'),\r\n        prefix: this.configService.get('API_PREFIX', 'api'),\r\n        documentation: this.configService.get('NODE_ENV') !== 'production' ? '/api/docs' : null\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get application status\r\n   */\r\n  getStatus() {\r\n    const memoryUsage = process.memoryUsage();\r\n    const totalMemory = memoryUsage.heapTotal + memoryUsage.external;\r\n    const usedMemory = memoryUsage.heapUsed;\r\n\r\n    return {\r\n      status: 'healthy',\r\n      timestamp: new Date().toISOString(),\r\n      uptime: process.uptime(),\r\n      memory: {\r\n        used: Math.round(usedMemory / 1024 / 1024 * 100) / 100, // MB\r\n        total: Math.round(totalMemory / 1024 / 1024 * 100) / 100, // MB\r\n        percentage: Math.round((usedMemory / totalMemory) * 100 * 100) / 100,\r\n        heap: {\r\n          used: Math.round(memoryUsage.heapUsed / 1024 / 1024 * 100) / 100,\r\n          total: Math.round(memoryUsage.heapTotal / 1024 / 1024 * 100) / 100\r\n        },\r\n        external: Math.round(memoryUsage.external / 1024 / 1024 * 100) / 100,\r\n        rss: Math.round(memoryUsage.rss / 1024 / 1024 * 100) / 100\r\n      },\r\n      version: this.configService.get('APP_VERSION', '1.0.0'),\r\n      environment: this.configService.get('NODE_ENV', 'development'),\r\n      pid: process.pid,\r\n      platform: process.platform,\r\n      nodeVersion: process.version\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get version information\r\n   */\r\n  getVersion() {\r\n    return {\r\n      version: this.configService.get('APP_VERSION', '1.0.0'),\r\n      buildDate: this.configService.get('BUILD_DATE', new Date().toISOString()),\r\n      gitCommit: this.configService.get('GIT_COMMIT', 'unknown'),\r\n      gitBranch: this.configService.get('GIT_BRANCH', 'unknown'),\r\n      nodeVersion: process.version,\r\n      platform: process.platform,\r\n      architecture: process.arch,\r\n      dependencies: {\r\n        nestjs: this.getNestJSVersion(),\r\n        typescript: this.getTypeScriptVersion(),\r\n        node: process.version\r\n      },\r\n      buildInfo: {\r\n        timestamp: this.configService.get('BUILD_TIMESTAMP', Date.now()),\r\n        environment: this.configService.get('BUILD_ENV', 'development'),\r\n        ci: this.configService.get('CI', false)\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get NestJS version from package.json\r\n   */\r\n  private getNestJSVersion(): string {\r\n    try {\r\n      // In a real implementation, you might read from package.json\r\n      return '10.0.0'; // Placeholder\r\n    } catch {\r\n      return 'unknown';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get TypeScript version\r\n   */\r\n  private getTypeScriptVersion(): string {\r\n    try {\r\n      // In a real implementation, you might read from package.json\r\n      return '5.1.3'; // Placeholder\r\n    } catch {\r\n      return 'unknown';\r\n    }\r\n  }\r\n}\r\n"], "version": 3}