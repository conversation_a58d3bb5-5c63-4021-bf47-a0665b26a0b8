b271fea8930469b2295bc4e6d212aafe
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReportDefinition = void 0;
const typeorm_1 = require("typeorm");
const report_execution_entity_1 = require("./report-execution.entity");
/**
 * Report Definition Entity
 *
 * Defines the structure and configuration for reports including:
 * - Report metadata and categorization
 * - Data source configuration and query definitions
 * - Visualization settings and chart configurations
 * - Export formats and delivery options
 * - Access control and sharing permissions
 * - Scheduling and automation settings
 * - Performance optimization parameters
 */
let ReportDefinition = class ReportDefinition {
    // Business Logic Methods
    /**
     * Check if user has permission to access this report
     */
    hasUserAccess(userId, userRoles, permission) {
        // Check visibility
        if (this.accessControl.visibility === 'private' && this.ownerId !== userId) {
            return false;
        }
        // Check user-specific permissions
        if (this.accessControl.allowedUsers.includes(userId)) {
            return this.accessControl.permissions[permission] || false;
        }
        // Check role-based permissions
        const hasRole = userRoles.some(role => this.accessControl.allowedRoles.includes(role));
        if (hasRole) {
            return this.accessControl.permissions[permission] || false;
        }
        // Check organization-wide access
        if (this.accessControl.visibility === 'organization' || this.accessControl.visibility === 'public') {
            return this.accessControl.permissions[permission] || false;
        }
        return false;
    }
    /**
     * Get data filters for user-specific data access
     */
    getUserDataFilters(userId, userRoles, userAttributes) {
        const filters = {};
        if (!this.accessControl.dataFiltering) {
            return filters;
        }
        // User-based filtering
        if (this.accessControl.dataFiltering.userBased) {
            filters.userId = userId;
        }
        // Role-based filtering
        if (this.accessControl.dataFiltering.roleBased) {
            filters.userRoles = userRoles;
        }
        // Attribute-based filtering
        if (this.accessControl.dataFiltering.attributeBased) {
            Object.assign(filters, this.accessControl.dataFiltering.filters);
            // Replace attribute placeholders
            for (const [key, value] of Object.entries(filters)) {
                if (typeof value === 'string' && value.startsWith('${') && value.endsWith('}')) {
                    const attributeName = value.slice(2, -1);
                    filters[key] = userAttributes[attributeName];
                }
            }
        }
        return filters;
    }
    /**
     * Check if report needs refresh based on cache configuration
     */
    needsRefresh() {
        if (!this.dataSourceConfig.cacheConfig?.enabled) {
            return true;
        }
        const lastExecution = this.metadata.usage.lastExecuted;
        if (!lastExecution) {
            return true;
        }
        const cacheAge = Date.now() - new Date(lastExecution).getTime();
        const cacheTtl = this.dataSourceConfig.cacheConfig.ttl * 1000; // Convert to milliseconds
        return cacheAge > cacheTtl;
    }
    /**
     * Get estimated execution time based on historical data
     */
    getEstimatedExecutionTime() {
        return this.metadata.usage.averageExecutionTime || 30000; // Default 30 seconds
    }
    /**
     * Check if report execution should be throttled
     */
    shouldThrottle() {
        const recentExecutions = this.executions?.filter(execution => {
            const executionTime = new Date(execution.startedAt).getTime();
            const oneHourAgo = Date.now() - 3600000; // 1 hour
            return executionTime > oneHourAgo;
        }).length || 0;
        // Throttle if more than 10 executions in the last hour
        return recentExecutions > 10;
    }
    /**
     * Update usage statistics
     */
    updateUsageStats(executionTime) {
        this.metadata.usage.executionCount++;
        this.metadata.usage.lastExecuted = new Date();
        // Update average execution time
        const currentAvg = this.metadata.usage.averageExecutionTime || 0;
        const count = this.metadata.usage.executionCount;
        this.metadata.usage.averageExecutionTime =
            (currentAvg * (count - 1) + executionTime) / count;
        // Update popularity score (simple algorithm based on recent usage)
        const daysSinceLastExecution = (Date.now() - new Date(this.metadata.usage.lastExecuted).getTime()) / (1000 * 60 * 60 * 24);
        this.metadata.usage.popularityScore = Math.max(0, 100 - daysSinceLastExecution * 2);
    }
    /**
     * Validate report configuration
     */
    validateConfiguration() {
        const errors = [];
        // Validate data source configuration
        if (!this.dataSourceConfig.sources || this.dataSourceConfig.sources.length === 0) {
            errors.push('At least one data source must be configured');
        }
        // Validate visualization configuration
        if (!this.visualizationConfig.chartType) {
            errors.push('Chart type must be specified');
        }
        if (!this.visualizationConfig.series || this.visualizationConfig.series.length === 0) {
            errors.push('At least one data series must be configured');
        }
        // Validate export configuration
        if (!this.exportConfig.formats || this.exportConfig.formats.length === 0) {
            errors.push('At least one export format must be specified');
        }
        // Validate access control
        if (this.accessControl.visibility === 'shared' &&
            this.accessControl.allowedUsers.length === 0 &&
            this.accessControl.allowedRoles.length === 0) {
            errors.push('Shared reports must specify allowed users or roles');
        }
        return {
            isValid: errors.length === 0,
            errors,
        };
    }
    /**
     * Clone report definition for templating
     */
    clone(newName, newOwnerId) {
        return {
            name: newName,
            description: this.description,
            reportType: this.reportType,
            category: this.category,
            priority: this.priority,
            dataSourceConfig: JSON.parse(JSON.stringify(this.dataSourceConfig)),
            visualizationConfig: JSON.parse(JSON.stringify(this.visualizationConfig)),
            exportConfig: JSON.parse(JSON.stringify(this.exportConfig)),
            accessControl: {
                ...JSON.parse(JSON.stringify(this.accessControl)),
                visibility: 'private',
                allowedUsers: [],
                allowedRoles: [],
            },
            schedulingConfig: null,
            performanceConfig: JSON.parse(JSON.stringify(this.performanceConfig)),
            isActive: true,
            isTemplate: false,
            templateId: this.id,
            ownerId: newOwnerId,
            metadata: {
                tags: [...this.metadata.tags],
                version: '1.0',
                lastModifiedBy: newOwnerId,
                changeLog: [],
                usage: {
                    executionCount: 0,
                    lastExecuted: null,
                    averageExecutionTime: 0,
                    popularityScore: 0,
                },
                compliance: JSON.parse(JSON.stringify(this.metadata.compliance)),
            },
        };
    }
};
exports.ReportDefinition = ReportDefinition;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ReportDefinition.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], ReportDefinition.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ReportDefinition.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: [
            'compliance_dashboard',
            'assessment_report',
            'audit_summary',
            'risk_analysis',
            'evidence_report',
            'framework_status',
            'control_effectiveness',
            'violation_analysis',
            'trend_analysis',
            'executive_summary',
            'regulatory_report',
            'custom_report',
        ],
    }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], ReportDefinition.prototype, "reportType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: [
            'compliance',
            'security',
            'risk',
            'audit',
            'operational',
            'financial',
            'executive',
            'regulatory',
        ],
    }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], ReportDefinition.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['low', 'medium', 'high', 'critical'],
        default: 'medium',
    }),
    __metadata("design:type", String)
], ReportDefinition.prototype, "priority", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    __metadata("design:type", Object)
], ReportDefinition.prototype, "dataSourceConfig", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    __metadata("design:type", Object)
], ReportDefinition.prototype, "visualizationConfig", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    __metadata("design:type", Object)
], ReportDefinition.prototype, "exportConfig", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    __metadata("design:type", Object)
], ReportDefinition.prototype, "accessControl", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ReportDefinition.prototype, "schedulingConfig", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    __metadata("design:type", Object)
], ReportDefinition.prototype, "performanceConfig", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", Boolean)
], ReportDefinition.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], ReportDefinition.prototype, "isTemplate", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ReportDefinition.prototype, "templateId", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], ReportDefinition.prototype, "ownerId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    __metadata("design:type", Object)
], ReportDefinition.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], ReportDefinition.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], ReportDefinition.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => report_execution_entity_1.ReportExecution, execution => execution.reportDefinition),
    __metadata("design:type", Array)
], ReportDefinition.prototype, "executions", void 0);
exports.ReportDefinition = ReportDefinition = __decorate([
    (0, typeorm_1.Entity)('report_definitions'),
    (0, typeorm_1.Index)(['category', 'isActive']),
    (0, typeorm_1.Index)(['reportType', 'createdAt']),
    (0, typeorm_1.Index)(['ownerId', 'isActive'])
], ReportDefinition);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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