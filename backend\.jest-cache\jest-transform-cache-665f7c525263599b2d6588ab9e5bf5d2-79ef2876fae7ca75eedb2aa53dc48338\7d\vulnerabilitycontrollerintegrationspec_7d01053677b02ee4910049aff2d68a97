d70f044a83a136eaf5e932739d2d3fb8
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const request = __importStar(require("supertest"));
const typeorm_2 = require("@nestjs/typeorm");
const vulnerability_controller_1 = require("../vulnerability.controller");
const vulnerability_service_1 = require("../../../application/services/vulnerability.service");
const vulnerability_entity_1 = require("../../../domain/entities/vulnerability.entity");
const vulnerability_assessment_entity_1 = require("../../../domain/entities/vulnerability-assessment.entity");
const vulnerability_exception_entity_1 = require("../../../domain/entities/vulnerability-exception.entity");
const asset_entity_1 = require("../../../../asset-management/domain/entities/asset.entity");
const logger_service_1 = require("../../../../../infrastructure/logging/logger.service");
const audit_service_1 = require("../../../../../infrastructure/logging/audit/audit.service");
const notification_service_1 = require("../../../../../infrastructure/notification/notification.service");
const jwt_auth_guard_1 = require("../../../../../infrastructure/auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../../../../../infrastructure/auth/guards/roles.guard");
describe('VulnerabilityController (Integration)', () => {
    let app;
    let vulnerabilityRepository;
    let assessmentRepository;
    let exceptionRepository;
    let assetRepository;
    const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        roles: ['security_analyst'],
    };
    const mockVulnerability = {
        identifier: 'CVE-2023-1234',
        title: 'Test SQL Injection Vulnerability',
        description: 'A SQL injection vulnerability in the user authentication module',
        severity: 'high',
        cvssScore: 7.5,
        affectedProducts: [
            {
                vendor: 'Test Corp',
                product: 'Web Application',
                version: '1.0.0',
            },
        ],
        publishedDate: '2023-01-01T00:00:00Z',
        dataSource: {
            name: 'Manual Entry',
            type: 'internal',
            confidence: 'high',
            lastUpdated: '2023-01-01T00:00:00Z',
        },
    };
    beforeAll(async () => {
        const moduleFixture = await testing_1.Test.createTestingModule({
            imports: [
                config_1.ConfigModule.forRoot({
                    isGlobal: true,
                    envFilePath: '.env.test',
                }),
                typeorm_1.TypeOrmModule.forRoot({
                    type: 'sqlite',
                    database: ':memory:',
                    entities: [vulnerability_entity_1.Vulnerability, vulnerability_assessment_entity_1.VulnerabilityAssessment, vulnerability_exception_entity_1.VulnerabilityException, asset_entity_1.Asset],
                    synchronize: true,
                    logging: false,
                }),
                typeorm_1.TypeOrmModule.forFeature([
                    vulnerability_entity_1.Vulnerability,
                    vulnerability_assessment_entity_1.VulnerabilityAssessment,
                    vulnerability_exception_entity_1.VulnerabilityException,
                    asset_entity_1.Asset,
                ]),
            ],
            controllers: [vulnerability_controller_1.VulnerabilityController],
            providers: [
                vulnerability_service_1.VulnerabilityService,
                {
                    provide: logger_service_1.LoggerService,
                    useValue: {
                        debug: jest.fn(),
                        log: jest.fn(),
                        warn: jest.fn(),
                        error: jest.fn(),
                    },
                },
                {
                    provide: audit_service_1.AuditService,
                    useValue: {
                        logUserAction: jest.fn(),
                    },
                },
                {
                    provide: notification_service_1.NotificationService,
                    useValue: {
                        sendNewCriticalVulnerabilitiesAlert: jest.fn(),
                    },
                },
            ],
        })
            .overrideGuard(jwt_auth_guard_1.JwtAuthGuard)
            .useValue({
            canActivate: (context) => {
                const request = context.switchToHttp().getRequest();
                request.user = mockUser;
                return true;
            },
        })
            .overrideGuard(roles_guard_1.RolesGuard)
            .useValue({
            canActivate: () => true,
        })
            .compile();
        app = moduleFixture.createNestApplication();
        await app.init();
        vulnerabilityRepository = moduleFixture.get((0, typeorm_2.getRepositoryToken)(vulnerability_entity_1.Vulnerability));
        assessmentRepository = moduleFixture.get((0, typeorm_2.getRepositoryToken)(vulnerability_assessment_entity_1.VulnerabilityAssessment));
        exceptionRepository = moduleFixture.get((0, typeorm_2.getRepositoryToken)(vulnerability_exception_entity_1.VulnerabilityException));
        assetRepository = moduleFixture.get((0, typeorm_2.getRepositoryToken)(asset_entity_1.Asset));
    });
    afterAll(async () => {
        await app.close();
    });
    beforeEach(async () => {
        // Clean up database before each test
        await vulnerabilityRepository.clear();
        await assessmentRepository.clear();
        await exceptionRepository.clear();
        await assetRepository.clear();
    });
    describe('GET /api/vulnerabilities/dashboard', () => {
        it('should return vulnerability dashboard data', async () => {
            // Create test vulnerabilities
            const vuln1 = vulnerabilityRepository.create({
                ...mockVulnerability,
                identifier: 'CVE-2023-0001',
                severity: 'critical',
            });
            const vuln2 = vulnerabilityRepository.create({
                ...mockVulnerability,
                identifier: 'CVE-2023-0002',
                severity: 'high',
            });
            await vulnerabilityRepository.save([vuln1, vuln2]);
            const response = await request(app.getHttpServer())
                .get('/api/vulnerabilities/dashboard')
                .expect(200);
            expect(response.body).toHaveProperty('summary');
            expect(response.body).toHaveProperty('breakdown');
            expect(response.body).toHaveProperty('timestamp');
            expect(response.body.summary.total).toBe(2);
            expect(response.body.summary.critical).toBe(1);
            expect(response.body.summary.high).toBe(1);
        });
    });
    describe('GET /api/vulnerabilities', () => {
        it('should return paginated vulnerabilities', async () => {
            // Create test vulnerabilities
            const vulnerabilities = [];
            for (let i = 1; i <= 15; i++) {
                vulnerabilities.push(vulnerabilityRepository.create({
                    ...mockVulnerability,
                    identifier: `CVE-2023-${i.toString().padStart(4, '0')}`,
                    title: `Test Vulnerability ${i}`,
                }));
            }
            await vulnerabilityRepository.save(vulnerabilities);
            const response = await request(app.getHttpServer())
                .get('/api/vulnerabilities')
                .query({ page: 1, limit: 10 })
                .expect(200);
            expect(response.body).toHaveProperty('vulnerabilities');
            expect(response.body).toHaveProperty('total');
            expect(response.body).toHaveProperty('page');
            expect(response.body).toHaveProperty('totalPages');
            expect(response.body.vulnerabilities).toHaveLength(10);
            expect(response.body.total).toBe(15);
            expect(response.body.totalPages).toBe(2);
        });
        it('should filter vulnerabilities by severity', async () => {
            const vuln1 = vulnerabilityRepository.create({
                ...mockVulnerability,
                identifier: 'CVE-2023-0001',
                severity: 'critical',
            });
            const vuln2 = vulnerabilityRepository.create({
                ...mockVulnerability,
                identifier: 'CVE-2023-0002',
                severity: 'medium',
            });
            await vulnerabilityRepository.save([vuln1, vuln2]);
            const response = await request(app.getHttpServer())
                .get('/api/vulnerabilities')
                .query({ severities: 'critical' })
                .expect(200);
            expect(response.body.vulnerabilities).toHaveLength(1);
            expect(response.body.vulnerabilities[0].severity).toBe('critical');
        });
        it('should search vulnerabilities by text', async () => {
            const vuln1 = vulnerabilityRepository.create({
                ...mockVulnerability,
                identifier: 'CVE-2023-0001',
                title: 'SQL Injection in Login Form',
            });
            const vuln2 = vulnerabilityRepository.create({
                ...mockVulnerability,
                identifier: 'CVE-2023-0002',
                title: 'Cross-Site Scripting in Comments',
            });
            await vulnerabilityRepository.save([vuln1, vuln2]);
            const response = await request(app.getHttpServer())
                .get('/api/vulnerabilities')
                .query({ searchText: 'SQL' })
                .expect(200);
            expect(response.body.vulnerabilities).toHaveLength(1);
            expect(response.body.vulnerabilities[0].title).toContain('SQL');
        });
    });
    describe('GET /api/vulnerabilities/:id', () => {
        it('should return vulnerability details', async () => {
            const vulnerability = vulnerabilityRepository.create(mockVulnerability);
            const savedVuln = await vulnerabilityRepository.save(vulnerability);
            const response = await request(app.getHttpServer())
                .get(`/api/vulnerabilities/${savedVuln.id}`)
                .expect(200);
            expect(response.body).toHaveProperty('id');
            expect(response.body.identifier).toBe(mockVulnerability.identifier);
            expect(response.body.title).toBe(mockVulnerability.title);
        });
        it('should return 404 for non-existent vulnerability', async () => {
            const response = await request(app.getHttpServer())
                .get('/api/vulnerabilities/123e4567-e89b-12d3-a456-426614174000')
                .expect(404);
            expect(response.body.message).toContain('Vulnerability not found');
        });
    });
    describe('POST /api/vulnerabilities', () => {
        it('should create a new vulnerability', async () => {
            const response = await request(app.getHttpServer())
                .post('/api/vulnerabilities')
                .send(mockVulnerability)
                .expect(201);
            expect(response.body).toHaveProperty('id');
            expect(response.body.identifier).toBe(mockVulnerability.identifier);
            expect(response.body.title).toBe(mockVulnerability.title);
            // Verify it was saved to database
            const savedVuln = await vulnerabilityRepository.findOne({
                where: { identifier: mockVulnerability.identifier },
            });
            expect(savedVuln).toBeDefined();
            expect(savedVuln.title).toBe(mockVulnerability.title);
        });
        it('should return 400 for invalid vulnerability data', async () => {
            const invalidVulnerability = {
                ...mockVulnerability,
                identifier: '', // Invalid: empty identifier
                severity: 'invalid', // Invalid: not in enum
            };
            const response = await request(app.getHttpServer())
                .post('/api/vulnerabilities')
                .send(invalidVulnerability)
                .expect(400);
            expect(response.body.message).toContain('validation failed');
        });
        it('should return 409 for duplicate vulnerability identifier', async () => {
            // Create first vulnerability
            const vulnerability = vulnerabilityRepository.create(mockVulnerability);
            await vulnerabilityRepository.save(vulnerability);
            // Try to create duplicate
            const response = await request(app.getHttpServer())
                .post('/api/vulnerabilities')
                .send(mockVulnerability)
                .expect(409);
            expect(response.body.message).toContain('already exists');
        });
    });
    describe('PUT /api/vulnerabilities/:id', () => {
        it('should update an existing vulnerability', async () => {
            const vulnerability = vulnerabilityRepository.create(mockVulnerability);
            const savedVuln = await vulnerabilityRepository.save(vulnerability);
            const updates = {
                title: 'Updated Vulnerability Title',
                severity: 'critical',
                cvssScore: 9.0,
            };
            const response = await request(app.getHttpServer())
                .put(`/api/vulnerabilities/${savedVuln.id}`)
                .send(updates)
                .expect(200);
            expect(response.body.title).toBe(updates.title);
            expect(response.body.severity).toBe(updates.severity);
            expect(response.body.cvssScore).toBe(updates.cvssScore);
            // Verify database was updated
            const updatedVuln = await vulnerabilityRepository.findOne({
                where: { id: savedVuln.id },
            });
            expect(updatedVuln.title).toBe(updates.title);
        });
        it('should return 404 for non-existent vulnerability', async () => {
            const response = await request(app.getHttpServer())
                .put('/api/vulnerabilities/123e4567-e89b-12d3-a456-426614174000')
                .send({ title: 'Updated Title' })
                .expect(404);
            expect(response.body.message).toContain('Vulnerability not found');
        });
    });
    describe('DELETE /api/vulnerabilities/:id', () => {
        it('should delete an existing vulnerability', async () => {
            const vulnerability = vulnerabilityRepository.create(mockVulnerability);
            const savedVuln = await vulnerabilityRepository.save(vulnerability);
            await request(app.getHttpServer())
                .delete(`/api/vulnerabilities/${savedVuln.id}`)
                .expect(204);
            // Verify it was deleted from database
            const deletedVuln = await vulnerabilityRepository.findOne({
                where: { id: savedVuln.id },
            });
            expect(deletedVuln).toBeNull();
        });
        it('should return 404 for non-existent vulnerability', async () => {
            const response = await request(app.getHttpServer())
                .delete('/api/vulnerabilities/123e4567-e89b-12d3-a456-426614174000')
                .expect(404);
            expect(response.body.message).toContain('Vulnerability not found');
        });
    });
    describe('POST /api/vulnerabilities/:id/assess', () => {
        it('should create a vulnerability assessment', async () => {
            const vulnerability = vulnerabilityRepository.create(mockVulnerability);
            const savedVuln = await vulnerabilityRepository.save(vulnerability);
            const assessmentData = {
                assessmentType: 'manual',
                findings: 'Confirmed vulnerability through manual testing',
                assessedSeverity: 'high',
                confidenceLevel: 90,
            };
            const response = await request(app.getHttpServer())
                .post(`/api/vulnerabilities/${savedVuln.id}/assess`)
                .send(assessmentData)
                .expect(201);
            expect(response.body).toHaveProperty('id');
            expect(response.body.vulnerabilityId).toBe(savedVuln.id);
            expect(response.body.findings).toBe(assessmentData.findings);
            // Verify assessment was saved
            const assessment = await assessmentRepository.findOne({
                where: { vulnerabilityId: savedVuln.id },
            });
            expect(assessment).toBeDefined();
            expect(assessment.findings).toBe(assessmentData.findings);
        });
    });
    describe('POST /api/vulnerabilities/:id/exceptions', () => {
        it('should create a vulnerability exception', async () => {
            const vulnerability = vulnerabilityRepository.create(mockVulnerability);
            const savedVuln = await vulnerabilityRepository.save(vulnerability);
            const exceptionData = {
                exceptionType: 'false_positive',
                justification: 'This is a false positive due to configuration differences',
                businessJustification: 'No business impact in our environment',
            };
            const response = await request(app.getHttpServer())
                .post(`/api/vulnerabilities/${savedVuln.id}/exceptions`)
                .send(exceptionData)
                .expect(201);
            expect(response.body).toHaveProperty('id');
            expect(response.body.vulnerabilityId).toBe(savedVuln.id);
            expect(response.body.exceptionType).toBe(exceptionData.exceptionType);
            // Verify exception was saved
            const exception = await exceptionRepository.findOne({
                where: { vulnerabilityId: savedVuln.id },
            });
            expect(exception).toBeDefined();
            expect(exception.justification).toBe(exceptionData.justification);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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