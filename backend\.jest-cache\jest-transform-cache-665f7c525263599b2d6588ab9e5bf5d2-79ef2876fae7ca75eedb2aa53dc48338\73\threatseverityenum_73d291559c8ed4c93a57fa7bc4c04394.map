{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\threat-severity.enum.ts", "mappings": ";;;AAAA;;;;;;;;;GASG;AACH,IAAY,cA4CX;AA5CD,WAAY,cAAc;IACxB;;;;;OAKG;IACH,qCAAmB,CAAA;IAEnB;;;;;;OAMG;IACH,6BAAW,CAAA;IAEX;;;;;;OAMG;IACH,mCAAiB,CAAA;IAEjB;;;;;;OAMG;IACH,+BAAa,CAAA;IAEb;;;;;;OAMG;IACH,uCAAqB,CAAA;AACvB,CAAC,EA5CW,cAAc,8BAAd,cAAc,QA4CzB;AAED;;GAEG;AACH,MAAa,mBAAmB;IAC9B;;OAEG;IACH,MAAM,CAAC,gBAAgB;QACrB,OAAO,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,4BAA4B;QACjC,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,+BAA+B;QACpC,OAAO,CAAC,cAAc,CAAC,MAAM,EAAE,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC;IAC/E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB;QACzB,OAAO,CAAC,cAAc,CAAC,GAAG,EAAE,cAAc,CAAC,OAAO,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,uBAAuB,CAAC,QAAwB;QACrD,OAAO,mBAAmB,CAAC,4BAA4B,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC/E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,QAAwB;QACxC,OAAO,QAAQ,KAAK,cAAc,CAAC,QAAQ,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,QAAwB;QAC9C,OAAO,QAAQ,KAAK,cAAc,CAAC,IAAI,IAAI,QAAQ,KAAK,cAAc,CAAC,QAAQ,CAAC;IAClF,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,0BAA0B,CAAC,QAAwB;QACxD,OAAO,mBAAmB,CAAC,+BAA+B,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAClF,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe,CAAC,QAAwB;QAC7C,MAAM,MAAM,GAAmC;YAC7C,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;YAC3B,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;YACvB,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1B,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;YACxB,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC;SAC7B,CAAC;QACF,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,QAAwB;QACtC,MAAM,MAAM,GAAmC;YAC7C,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;YAC3B,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,EAAE;YACxB,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,EAAE;YAC3B,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE;YACzB,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,GAAG;SAC/B,CAAC;QACF,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,CAAC,SAAyB,EAAE,SAAyB;QACjE,OAAO,mBAAmB,CAAC,eAAe,CAAC,SAAS,CAAC,GAAG,mBAAmB,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;IACzG,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS,CAAC,SAAyB,EAAE,SAAyB;QACnE,OAAO,mBAAmB,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC;IACxF,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,SAAyB,EAAE,SAAyB;QAClE,OAAO,mBAAmB,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC;IACxF,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB,CAAC,QAAwB;QAChD,MAAM,UAAU,GAAmC;YACjD,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAO,YAAY;YAC/C,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,EAAU,aAAa;YAChD,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,GAAG,EAAO,UAAU;YAC7C,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,GAAG,EAAU,UAAU;YAC7C,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,IAAI,EAAK,WAAW;SAC/C,CAAC;QACF,OAAO,UAAU,CAAC,QAAQ,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB,CAAC,QAAwB;QACnD,MAAM,QAAQ,GAAmC;YAC/C,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAO,SAAS;YAC5C,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,EAAW,UAAU;YAC7C,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,EAAE,EAAQ,WAAW;YAC9C,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,EAAE,EAAW,SAAS;YAC5C,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,GAAG,EAAM,SAAS;SAC7C,CAAC;QACF,OAAO,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB,CAAC,QAAwB;QACnD,MAAM,QAAQ,GAAmC;YAC/C,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAO,UAAU;YAC7C,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,EAAU,WAAW;YAC9C,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,EAAE,EAAQ,SAAS;YAC5C,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,GAAG,EAAU,SAAS;YAC5C,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,GAAG,EAAM,UAAU;SAC9C,CAAC;QACF,OAAO,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,QAAwB;QAC/C,MAAM,iBAAiB,GAAmC;YACxD,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAM,aAAa;YAChD,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,EAAU,SAAS;YAC5C,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,GAAG,EAAO,UAAU;YAC7C,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,GAAG,EAAU,WAAW;YAC9C,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,IAAI,EAAK,WAAW;SAC/C,CAAC;QACF,OAAO,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe,CAAC,QAAwB;QAC7C,MAAM,KAAK,GAAqC;YAC9C,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC,oBAAoB,EAAE,gBAAgB,EAAE,eAAe,EAAE,WAAW,EAAE,OAAO,EAAE,gBAAgB,CAAC;YAC5H,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,gBAAgB,EAAE,eAAe,EAAE,WAAW,CAAC;YACvE,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,eAAe,CAAC;YACrD,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC;YACjC,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC;SACtC,CAAC;QACF,OAAO,KAAK,CAAC,QAAQ,CAAC,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,uBAAuB,CAAC,QAAwB;QACrD,MAAM,QAAQ,GAAqC;YACjD,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC;YACjF,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,CAAC;YAC3D,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,CAAC;YACtD,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC;YAC1C,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC;SACtC,CAAC;QACF,OAAO,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAAC,QAAwB;QAIpD,MAAM,WAAW,GAAmE;YAClF,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,EAAK,0BAA0B;YACvF,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,EAAS,6BAA6B;YAC1F,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,EAAM,6BAA6B;YAC1F,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,EAAU,yBAAyB;YACtF,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC,EAAE,EAAK,4BAA4B;SAC1F,CAAC;QACF,OAAO,WAAW,CAAC,QAAQ,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,QAAwB;QAC1C,MAAM,MAAM,GAAmC;YAC7C,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,SAAS,EAAE,MAAM;YAC5C,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,SAAS,EAAM,SAAS;YAC/C,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,SAAS,EAAI,QAAQ;YAC9C,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,SAAS,EAAO,QAAQ;YAC9C,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,SAAS,EAAG,OAAO;SAC9C,CAAC;QACF,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,QAAwB;QACzC,MAAM,KAAK,GAAmC;YAC5C,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,cAAc;YACzC,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,oBAAoB;YAC3C,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,cAAc;YACvC,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,QAAQ;YAC9B,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,iBAAiB;SAC5C,CAAC;QACF,OAAO,KAAK,CAAC,QAAQ,CAAC,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,QAAwB;QAC5C,MAAM,YAAY,GAAmC;YACnD,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,wDAAwD;YACnF,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,kEAAkE;YACzF,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,2EAA2E;YACpG,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,oEAAoE;YAC1F,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,iEAAiE;SAC5F,CAAC;QACF,OAAO,YAAY,CAAC,QAAQ,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS,CAAC,KAAa;QAC5B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,cAAc,CAAC,QAAQ,CAAC;QAChD,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,cAAc,CAAC,IAAI,CAAC;QAC5C,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,cAAc,CAAC,MAAM,CAAC;QAC9C,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,cAAc,CAAC,GAAG,CAAC;QAC3C,OAAO,cAAc,CAAC,OAAO,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,SAAiB;QACpC,IAAI,SAAS,IAAI,GAAG;YAAE,OAAO,cAAc,CAAC,QAAQ,CAAC;QACrD,IAAI,SAAS,IAAI,GAAG;YAAE,OAAO,cAAc,CAAC,IAAI,CAAC;QACjD,IAAI,SAAS,IAAI,GAAG;YAAE,OAAO,cAAc,CAAC,MAAM,CAAC;QACnD,IAAI,SAAS,IAAI,GAAG;YAAE,OAAO,cAAc,CAAC,GAAG,CAAC;QAChD,OAAO,cAAc,CAAC,OAAO,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,KAAa;QAC7B,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;QAC9C,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QACjD,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,UAAU,CAAC,IAAI,IAAI,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,CAAC,QAAgB;QAC7B,OAAO,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC,QAA0B,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB,CAAC,QAAwB;QACnD,MAAM,UAAU,GAAmC;YACjD,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,EAAE;YAC7B,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;YACxB,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1B,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;YACvB,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;SAC5B,CAAC;QACF,OAAO,UAAU,CAAC,QAAQ,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,0BAA0B,CAAC,QAAwB;QAMxD,OAAO;YACL,UAAU,EAAE,QAAQ,KAAK,cAAc,CAAC,OAAO;YAC/C,WAAW,EAAE,mBAAmB,CAAC,gBAAgB,CAAC,QAAQ,CAAC;YAC3D,aAAa,EAAE,mBAAmB,CAAC,0BAA0B,CAAC,QAAQ,CAAC;YACvE,OAAO,EAAE,mBAAmB,CAAC,uBAAuB,CAAC,QAAQ,CAAC;SAC/D,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAAC,QAAwB;QAMpD,OAAO;YACL,SAAS,EAAE,mBAAmB,CAAC,0BAA0B,CAAC,QAAQ,CAAC;YACnE,UAAU,EAAE,mBAAmB,CAAC,gBAAgB,CAAC,QAAQ,CAAC;YAC1D,mBAAmB,EAAE,mBAAmB,CAAC,uBAAuB,CAAC,QAAQ,CAAC;YAC1E,cAAc,EAAE,QAAQ,KAAK,cAAc,CAAC,QAAQ;SACrD,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,4BAA4B,CAAC,QAAwB;QAO1D,OAAO;YACL,oBAAoB,EAAE,QAAQ,KAAK,cAAc,CAAC,OAAO;YACzD,qBAAqB,EAAE,mBAAmB,CAAC,gBAAgB,CAAC,QAAQ,CAAC;YACrE,oBAAoB,EAAE,QAAQ,KAAK,cAAc,CAAC,QAAQ;YAC1D,sBAAsB,EAAE,QAAQ,KAAK,cAAc,CAAC,QAAQ;YAC5D,kBAAkB,EAAE,KAAK,EAAE,wCAAwC;SACpE,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,yBAAyB,CAAC,UAA4B;QAC3D,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAO,cAAc,CAAC,OAAO,CAAC;QAChC,CAAC;QAED,2DAA2D;QAC3D,IAAI,UAAU,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjD,OAAO,cAAc,CAAC,QAAQ,CAAC;QACjC,CAAC;QAED,6BAA6B;QAC7B,MAAM,aAAa,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;QAClF,MAAM,OAAO,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,CAAC;QAExF,2BAA2B;QAC3B,IAAI,OAAO,IAAI,GAAG;YAAE,OAAO,cAAc,CAAC,QAAQ,CAAC;QACnD,IAAI,OAAO,IAAI,GAAG;YAAE,OAAO,cAAc,CAAC,IAAI,CAAC;QAC/C,IAAI,OAAO,IAAI,GAAG;YAAE,OAAO,cAAc,CAAC,MAAM,CAAC;QACjD,IAAI,OAAO,IAAI,GAAG;YAAE,OAAO,cAAc,CAAC,GAAG,CAAC;QAC9C,OAAO,cAAc,CAAC,OAAO,CAAC;IAChC,CAAC;CACF;AA1XD,kDA0XC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\threat-severity.enum.ts"], "sourcesContent": ["/**\r\n * Threat Severity Enum\r\n * \r\n * Represents the severity level of identified threats based on potential impact,\r\n * likelihood, and risk assessment. Used for threat prioritization, response planning,\r\n * and resource allocation in security operations.\r\n * \r\n * Severity Levels (ascending order):\r\n * UNKNOWN -> LOW -> MEDIUM -> HIGH -> CRITICAL\r\n */\r\nexport enum ThreatSeverity {\r\n  /**\r\n   * Unknown threat severity\r\n   * - Insufficient information for assessment\r\n   * - Requires further analysis\r\n   * - Default state for new threats\r\n   */\r\n  UNKNOWN = 'unknown',\r\n\r\n  /**\r\n   * Low severity threats\r\n   * - Minimal potential impact\r\n   * - Low likelihood of exploitation\r\n   * - Routine monitoring sufficient\r\n   * - No immediate action required\r\n   */\r\n  LOW = 'low',\r\n\r\n  /**\r\n   * Medium severity threats\r\n   * - Moderate potential impact\r\n   * - Possible exploitation scenarios\r\n   * - Enhanced monitoring recommended\r\n   * - Mitigation planning advised\r\n   */\r\n  MEDIUM = 'medium',\r\n\r\n  /**\r\n   * High severity threats\r\n   * - Significant potential impact\r\n   * - High likelihood of exploitation\r\n   * - Active monitoring required\r\n   * - Immediate mitigation needed\r\n   */\r\n  HIGH = 'high',\r\n\r\n  /**\r\n   * Critical severity threats\r\n   * - Severe potential impact\r\n   * - Imminent or active exploitation\r\n   * - Continuous monitoring required\r\n   * - Emergency response activated\r\n   */\r\n  CRITICAL = 'critical',\r\n}\r\n\r\n/**\r\n * Threat Severity Utilities\r\n */\r\nexport class ThreatSeverityUtils {\r\n  /**\r\n   * Get all threat severity levels\r\n   */\r\n  static getAllSeverities(): ThreatSeverity[] {\r\n    return Object.values(ThreatSeverity);\r\n  }\r\n\r\n  /**\r\n   * Get severity levels requiring immediate action\r\n   */\r\n  static getImmediateActionSeverities(): ThreatSeverity[] {\r\n    return [ThreatSeverity.HIGH, ThreatSeverity.CRITICAL];\r\n  }\r\n\r\n  /**\r\n   * Get severity levels requiring enhanced monitoring\r\n   */\r\n  static getEnhancedMonitoringSeverities(): ThreatSeverity[] {\r\n    return [ThreatSeverity.MEDIUM, ThreatSeverity.HIGH, ThreatSeverity.CRITICAL];\r\n  }\r\n\r\n  /**\r\n   * Get severity levels for routine processing\r\n   */\r\n  static getRoutineSeverities(): ThreatSeverity[] {\r\n    return [ThreatSeverity.LOW, ThreatSeverity.UNKNOWN];\r\n  }\r\n\r\n  /**\r\n   * Check if severity requires immediate action\r\n   */\r\n  static requiresImmediateAction(severity: ThreatSeverity): boolean {\r\n    return ThreatSeverityUtils.getImmediateActionSeverities().includes(severity);\r\n  }\r\n\r\n  /**\r\n   * Check if severity is critical\r\n   */\r\n  static isCritical(severity: ThreatSeverity): boolean {\r\n    return severity === ThreatSeverity.CRITICAL;\r\n  }\r\n\r\n  /**\r\n   * Check if severity is high or critical\r\n   */\r\n  static isHighOrCritical(severity: ThreatSeverity): boolean {\r\n    return severity === ThreatSeverity.HIGH || severity === ThreatSeverity.CRITICAL;\r\n  }\r\n\r\n  /**\r\n   * Check if severity requires enhanced monitoring\r\n   */\r\n  static requiresEnhancedMonitoring(severity: ThreatSeverity): boolean {\r\n    return ThreatSeverityUtils.getEnhancedMonitoringSeverities().includes(severity);\r\n  }\r\n\r\n  /**\r\n   * Get numeric value for severity (for comparison and scoring)\r\n   */\r\n  static getNumericValue(severity: ThreatSeverity): number {\r\n    const values: Record<ThreatSeverity, number> = {\r\n      [ThreatSeverity.UNKNOWN]: 0,\r\n      [ThreatSeverity.LOW]: 1,\r\n      [ThreatSeverity.MEDIUM]: 2,\r\n      [ThreatSeverity.HIGH]: 3,\r\n      [ThreatSeverity.CRITICAL]: 4,\r\n    };\r\n    return values[severity];\r\n  }\r\n\r\n  /**\r\n   * Get severity score (0-100) for risk calculations\r\n   */\r\n  static getScore(severity: ThreatSeverity): number {\r\n    const scores: Record<ThreatSeverity, number> = {\r\n      [ThreatSeverity.UNKNOWN]: 0,\r\n      [ThreatSeverity.LOW]: 25,\r\n      [ThreatSeverity.MEDIUM]: 50,\r\n      [ThreatSeverity.HIGH]: 75,\r\n      [ThreatSeverity.CRITICAL]: 100,\r\n    };\r\n    return scores[severity];\r\n  }\r\n\r\n  /**\r\n   * Compare two threat severities\r\n   */\r\n  static compare(severity1: ThreatSeverity, severity2: ThreatSeverity): number {\r\n    return ThreatSeverityUtils.getNumericValue(severity1) - ThreatSeverityUtils.getNumericValue(severity2);\r\n  }\r\n\r\n  /**\r\n   * Get the higher of two severities\r\n   */\r\n  static getHigher(severity1: ThreatSeverity, severity2: ThreatSeverity): ThreatSeverity {\r\n    return ThreatSeverityUtils.compare(severity1, severity2) >= 0 ? severity1 : severity2;\r\n  }\r\n\r\n  /**\r\n   * Get the lower of two severities\r\n   */\r\n  static getLower(severity1: ThreatSeverity, severity2: ThreatSeverity): ThreatSeverity {\r\n    return ThreatSeverityUtils.compare(severity1, severity2) <= 0 ? severity1 : severity2;\r\n  }\r\n\r\n  /**\r\n   * Get response time SLA in minutes based on threat severity\r\n   */\r\n  static getResponseTimeSLA(severity: ThreatSeverity): number {\r\n    const slaMinutes: Record<ThreatSeverity, number> = {\r\n      [ThreatSeverity.CRITICAL]: 5,      // 5 minutes\r\n      [ThreatSeverity.HIGH]: 30,         // 30 minutes\r\n      [ThreatSeverity.MEDIUM]: 120,      // 2 hours\r\n      [ThreatSeverity.LOW]: 480,         // 8 hours\r\n      [ThreatSeverity.UNKNOWN]: 1440,    // 24 hours\r\n    };\r\n    return slaMinutes[severity];\r\n  }\r\n\r\n  /**\r\n   * Get containment time SLA in hours based on threat severity\r\n   */\r\n  static getContainmentTimeSLA(severity: ThreatSeverity): number {\r\n    const slaHours: Record<ThreatSeverity, number> = {\r\n      [ThreatSeverity.CRITICAL]: 1,      // 1 hour\r\n      [ThreatSeverity.HIGH]: 4,          // 4 hours\r\n      [ThreatSeverity.MEDIUM]: 24,       // 24 hours\r\n      [ThreatSeverity.LOW]: 72,          // 3 days\r\n      [ThreatSeverity.UNKNOWN]: 168,     // 7 days\r\n    };\r\n    return slaHours[severity];\r\n  }\r\n\r\n  /**\r\n   * Get eradication time SLA in hours based on threat severity\r\n   */\r\n  static getEradicationTimeSLA(severity: ThreatSeverity): number {\r\n    const slaHours: Record<ThreatSeverity, number> = {\r\n      [ThreatSeverity.CRITICAL]: 4,      // 4 hours\r\n      [ThreatSeverity.HIGH]: 24,         // 24 hours\r\n      [ThreatSeverity.MEDIUM]: 72,       // 3 days\r\n      [ThreatSeverity.LOW]: 168,         // 7 days\r\n      [ThreatSeverity.UNKNOWN]: 336,     // 14 days\r\n    };\r\n    return slaHours[severity];\r\n  }\r\n\r\n  /**\r\n   * Get escalation time in minutes based on threat severity\r\n   */\r\n  static getEscalationTime(severity: ThreatSeverity): number {\r\n    const escalationMinutes: Record<ThreatSeverity, number> = {\r\n      [ThreatSeverity.CRITICAL]: 15,     // 15 minutes\r\n      [ThreatSeverity.HIGH]: 60,         // 1 hour\r\n      [ThreatSeverity.MEDIUM]: 240,      // 4 hours\r\n      [ThreatSeverity.LOW]: 960,         // 16 hours\r\n      [ThreatSeverity.UNKNOWN]: 2880,    // 48 hours\r\n    };\r\n    return escalationMinutes[severity];\r\n  }\r\n\r\n  /**\r\n   * Get required response team based on threat severity\r\n   */\r\n  static getResponseTeam(severity: ThreatSeverity): string[] {\r\n    const teams: Record<ThreatSeverity, string[]> = {\r\n      [ThreatSeverity.CRITICAL]: ['incident_commander', 'senior_analyst', 'threat_hunter', 'forensics', 'legal', 'communications'],\r\n      [ThreatSeverity.HIGH]: ['senior_analyst', 'threat_hunter', 'forensics'],\r\n      [ThreatSeverity.MEDIUM]: ['analyst', 'threat_hunter'],\r\n      [ThreatSeverity.LOW]: ['analyst'],\r\n      [ThreatSeverity.UNKNOWN]: ['analyst'],\r\n    };\r\n    return teams[severity];\r\n  }\r\n\r\n  /**\r\n   * Get notification channels based on threat severity\r\n   */\r\n  static getNotificationChannels(severity: ThreatSeverity): string[] {\r\n    const channels: Record<ThreatSeverity, string[]> = {\r\n      [ThreatSeverity.CRITICAL]: ['email', 'sms', 'slack', 'webhook', 'pager', 'phone'],\r\n      [ThreatSeverity.HIGH]: ['email', 'sms', 'slack', 'webhook'],\r\n      [ThreatSeverity.MEDIUM]: ['email', 'slack', 'webhook'],\r\n      [ThreatSeverity.LOW]: ['email', 'webhook'],\r\n      [ThreatSeverity.UNKNOWN]: ['webhook'],\r\n    };\r\n    return channels[severity];\r\n  }\r\n\r\n  /**\r\n   * Get monitoring frequency based on threat severity\r\n   */\r\n  static getMonitoringFrequency(severity: ThreatSeverity): {\r\n    interval: number; // in minutes\r\n    duration: number; // in hours\r\n  } {\r\n    const frequencies: Record<ThreatSeverity, { interval: number; duration: number }> = {\r\n      [ThreatSeverity.CRITICAL]: { interval: 1, duration: 72 },    // Every minute for 3 days\r\n      [ThreatSeverity.HIGH]: { interval: 5, duration: 48 },        // Every 5 minutes for 2 days\r\n      [ThreatSeverity.MEDIUM]: { interval: 15, duration: 24 },     // Every 15 minutes for 1 day\r\n      [ThreatSeverity.LOW]: { interval: 60, duration: 8 },         // Every hour for 8 hours\r\n      [ThreatSeverity.UNKNOWN]: { interval: 240, duration: 4 },    // Every 4 hours for 4 hours\r\n    };\r\n    return frequencies[severity];\r\n  }\r\n\r\n  /**\r\n   * Get color code for UI display\r\n   */\r\n  static getColorCode(severity: ThreatSeverity): string {\r\n    const colors: Record<ThreatSeverity, string> = {\r\n      [ThreatSeverity.CRITICAL]: '#DC2626', // Red\r\n      [ThreatSeverity.HIGH]: '#EA580C',     // Orange\r\n      [ThreatSeverity.MEDIUM]: '#D97706',   // Amber\r\n      [ThreatSeverity.LOW]: '#16A34A',      // Green\r\n      [ThreatSeverity.UNKNOWN]: '#6B7280',  // Gray\r\n    };\r\n    return colors[severity];\r\n  }\r\n\r\n  /**\r\n   * Get icon name for UI display\r\n   */\r\n  static getIconName(severity: ThreatSeverity): string {\r\n    const icons: Record<ThreatSeverity, string> = {\r\n      [ThreatSeverity.CRITICAL]: 'shield-alert',\r\n      [ThreatSeverity.HIGH]: 'shield-exclamation',\r\n      [ThreatSeverity.MEDIUM]: 'shield-check',\r\n      [ThreatSeverity.LOW]: 'shield',\r\n      [ThreatSeverity.UNKNOWN]: 'shield-question',\r\n    };\r\n    return icons[severity];\r\n  }\r\n\r\n  /**\r\n   * Get human-readable description\r\n   */\r\n  static getDescription(severity: ThreatSeverity): string {\r\n    const descriptions: Record<ThreatSeverity, string> = {\r\n      [ThreatSeverity.CRITICAL]: 'Critical threat requiring immediate emergency response',\r\n      [ThreatSeverity.HIGH]: 'High-severity threat requiring urgent containment and mitigation',\r\n      [ThreatSeverity.MEDIUM]: 'Medium-severity threat requiring enhanced monitoring and planned response',\r\n      [ThreatSeverity.LOW]: 'Low-severity threat requiring routine monitoring and documentation',\r\n      [ThreatSeverity.UNKNOWN]: 'Unknown threat severity requiring assessment and classification',\r\n    };\r\n    return descriptions[severity];\r\n  }\r\n\r\n  /**\r\n   * Get severity from numeric score (0-100)\r\n   */\r\n  static fromScore(score: number): ThreatSeverity {\r\n    if (score >= 90) return ThreatSeverity.CRITICAL;\r\n    if (score >= 70) return ThreatSeverity.HIGH;\r\n    if (score >= 40) return ThreatSeverity.MEDIUM;\r\n    if (score >= 10) return ThreatSeverity.LOW;\r\n    return ThreatSeverity.UNKNOWN;\r\n  }\r\n\r\n  /**\r\n   * Get severity from CVSS score\r\n   */\r\n  static fromCVSSScore(cvssScore: number): ThreatSeverity {\r\n    if (cvssScore >= 9.0) return ThreatSeverity.CRITICAL;\r\n    if (cvssScore >= 7.0) return ThreatSeverity.HIGH;\r\n    if (cvssScore >= 4.0) return ThreatSeverity.MEDIUM;\r\n    if (cvssScore >= 0.1) return ThreatSeverity.LOW;\r\n    return ThreatSeverity.UNKNOWN;\r\n  }\r\n\r\n  /**\r\n   * Get severity from string (case-insensitive)\r\n   */\r\n  static fromString(value: string): ThreatSeverity | null {\r\n    const normalized = value.toLowerCase().trim();\r\n    const severities = Object.values(ThreatSeverity);\r\n    return severities.find(s => s === normalized) || null;\r\n  }\r\n\r\n  /**\r\n   * Validate severity value\r\n   */\r\n  static isValid(severity: string): boolean {\r\n    return Object.values(ThreatSeverity).includes(severity as ThreatSeverity);\r\n  }\r\n\r\n  /**\r\n   * Get processing priority (1-10) based on threat severity\r\n   */\r\n  static getProcessingPriority(severity: ThreatSeverity): number {\r\n    const priorities: Record<ThreatSeverity, number> = {\r\n      [ThreatSeverity.CRITICAL]: 10,\r\n      [ThreatSeverity.HIGH]: 8,\r\n      [ThreatSeverity.MEDIUM]: 6,\r\n      [ThreatSeverity.LOW]: 4,\r\n      [ThreatSeverity.UNKNOWN]: 2,\r\n    };\r\n    return priorities[severity];\r\n  }\r\n\r\n  /**\r\n   * Get threat intelligence requirements\r\n   */\r\n  static getThreatIntelRequirements(severity: ThreatSeverity): {\r\n    enrichment: boolean;\r\n    attribution: boolean;\r\n    iocGeneration: boolean;\r\n    sharing: boolean;\r\n  } {\r\n    return {\r\n      enrichment: severity !== ThreatSeverity.UNKNOWN,\r\n      attribution: ThreatSeverityUtils.isHighOrCritical(severity),\r\n      iocGeneration: ThreatSeverityUtils.requiresEnhancedMonitoring(severity),\r\n      sharing: ThreatSeverityUtils.requiresImmediateAction(severity),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get containment strategy based on threat severity\r\n   */\r\n  static getContainmentStrategy(severity: ThreatSeverity): {\r\n    isolation: boolean;\r\n    quarantine: boolean;\r\n    networkSegmentation: boolean;\r\n    systemShutdown: boolean;\r\n  } {\r\n    return {\r\n      isolation: ThreatSeverityUtils.requiresEnhancedMonitoring(severity),\r\n      quarantine: ThreatSeverityUtils.isHighOrCritical(severity),\r\n      networkSegmentation: ThreatSeverityUtils.requiresImmediateAction(severity),\r\n      systemShutdown: severity === ThreatSeverity.CRITICAL,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get communication requirements based on threat severity\r\n   */\r\n  static getCommunicationRequirements(severity: ThreatSeverity): {\r\n    internalNotification: boolean;\r\n    executiveNotification: boolean;\r\n    customerNotification: boolean;\r\n    regulatoryNotification: boolean;\r\n    publicNotification: boolean;\r\n  } {\r\n    return {\r\n      internalNotification: severity !== ThreatSeverity.UNKNOWN,\r\n      executiveNotification: ThreatSeverityUtils.isHighOrCritical(severity),\r\n      customerNotification: severity === ThreatSeverity.CRITICAL,\r\n      regulatoryNotification: severity === ThreatSeverity.CRITICAL,\r\n      publicNotification: false, // Determined by specific threat context\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate combined severity from multiple threats\r\n   */\r\n  static calculateCombinedSeverity(severities: ThreatSeverity[]): ThreatSeverity {\r\n    if (severities.length === 0) {\r\n      return ThreatSeverity.UNKNOWN;\r\n    }\r\n\r\n    // If any threat is critical, combined severity is critical\r\n    if (severities.includes(ThreatSeverity.CRITICAL)) {\r\n      return ThreatSeverity.CRITICAL;\r\n    }\r\n\r\n    // Calculate weighted average\r\n    const numericValues = severities.map(s => ThreatSeverityUtils.getNumericValue(s));\r\n    const average = numericValues.reduce((sum, val) => sum + val, 0) / numericValues.length;\r\n\r\n    // Convert back to severity\r\n    if (average >= 3.5) return ThreatSeverity.CRITICAL;\r\n    if (average >= 2.5) return ThreatSeverity.HIGH;\r\n    if (average >= 1.5) return ThreatSeverity.MEDIUM;\r\n    if (average >= 0.5) return ThreatSeverity.LOW;\r\n    return ThreatSeverity.UNKNOWN;\r\n  }\r\n}\r\n"], "version": 3}