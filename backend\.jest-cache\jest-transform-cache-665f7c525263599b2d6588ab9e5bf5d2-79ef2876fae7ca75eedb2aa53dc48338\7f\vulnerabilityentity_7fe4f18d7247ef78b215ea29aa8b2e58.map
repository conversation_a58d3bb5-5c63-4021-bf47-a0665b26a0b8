{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\domain\\entities\\vulnerability.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCAUiB;AACjB,uFAA4E;AAC5E,qFAA0E;AAC1E,yFAA+E;AAE/E;;;GAGG;AAWI,IAAM,aAAa,GAAnB,MAAM,aAAa;IA8SxB;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,QAAQ,KAAK,UAAU,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,QAAQ,KAAK,UAAU;YAC5B,CAAC,IAAI,CAAC,QAAQ,KAAK,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC;YAC9C,IAAI,CAAC,UAAU;YACf,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC5D,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAI,iBAAiB;QACnB,MAAM,aAAa,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QACtE,OAAO,IAAI,CAAC,gBAAgB,GAAG,aAAa,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,gBAAgB,EAAE,IAAI,EAAE,KAAK,IAAI,IAAI,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,gBAAgB,EAAE,IAAI,EAAE,QAAQ,IAAI,IAAI,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,uBAAuB;QACvB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,4BAA4B;YAC5B,MAAM,cAAc,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;YAC5E,KAAK,GAAG,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACxC,CAAC;QAED,yBAAyB;QACzB,IAAI,IAAI,CAAC,WAAW;YAAE,KAAK,IAAI,CAAC,CAAC;QACjC,IAAI,IAAI,CAAC,UAAU;YAAE,KAAK,IAAI,GAAG,CAAC;QAClC,IAAI,IAAI,CAAC,SAAS;YAAE,KAAK,IAAI,CAAC,CAAC;QAE/B,4BAA4B;QAC5B,IAAI,CAAC,IAAI,CAAC,cAAc;YAAE,KAAK,IAAI,CAAC,CAAC;QAErC,sDAAsD;QACtD,IAAI,IAAI,CAAC,QAAQ;YAAE,KAAK,IAAI,GAAG,CAAC;QAEhC,oBAAoB;QACpB,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC;QAC5B,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG;YAAE,KAAK,IAAI,CAAC,CAAC;QAEnC,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,uBAAuB;QACrB,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;IACpE,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,MAAc,EAAE,OAAe,EAAE,OAAgB;QACjE,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAChD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC,WAAW,EAAE;YAC/C,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,OAAO,CAAC,WAAW,EAAE,CACvD,CAAC;QAEF,IAAI,CAAC,eAAe;YAAE,OAAO,KAAK,CAAC;QACnC,IAAI,CAAC,OAAO;YAAE,OAAO,IAAI,CAAC;QAE1B,yEAAyE;QACzE,OAAO,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,GAAW;QAChB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,GAAW;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,UAAmB,EAAE,SAAkB;QACzD,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,WAAW,GAAG,UAAU,IAAI,SAAS,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,SAAkB,EAAE,SAAe;QACnD,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;QAChC,IAAI,SAAS,EAAE,CAAC;YACd,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC7B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,kBAAkB,EAAE;YACpC,oBAAoB,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM;YAClD,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO;YACL,aAAa,EAAE,IAAI,CAAC,UAAU,EAAE;YAChC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACrC,CAAC;IACJ,CAAC;CACF,CAAA;AA3eY,sCAAa;AAExB;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;yCACpB;AAMX;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;;iDACN;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;4CACV;AAMd;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;kDACL;AASpB;IAJC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC;KACpD,CAAC;;+CACwD;AAM1D;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;gDACrE;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;iDAC5B;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kDAa9D;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;6CACrD;AAMjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;wDAC5B;AAM3B;IADC,IAAA,gBAAM,EAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;kDACN;AAMrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;iDAC5B;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;gDAC7B;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;qDAC5B;AAMxB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;kDACnC,KAAK,oBAAL,KAAK;uDAYpB;AAMH;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAC7B,KAAK,oBAAL,KAAK;iDAKf;AAMH;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDAQjE;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;gDA2B5D;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kDA0B9D;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;oDAyBhE;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;2CACtC;AAMf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;;iDAQ7C;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC;kDACtD,IAAI,oBAAJ,IAAI;oDAAC;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC;kDACvD,IAAI,oBAAJ,IAAI;uDAAC;AAMvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACrE,IAAI,oBAAJ,IAAI;qDAAC;AAMtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACrE,IAAI,oBAAJ,IAAI;qDAAC;AAMtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAClD,MAAM,oBAAN,MAAM;uDAAc;AAGvC;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;gDAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;gDAAC;AAIhB;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,yDAAuB,EAAE,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC;;kDAC1C;AAGvC;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,uDAAsB,EAAE,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,aAAa,CAAC;;iDACzC;AAQrC;IANC,IAAA,oBAAU,EAAC,GAAG,EAAE,CAAC,oBAAK,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC;IACvD,IAAA,mBAAS,EAAC;QACT,IAAI,EAAE,uBAAuB;QAC7B,UAAU,EAAE,EAAE,IAAI,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,IAAI,EAAE;QACpE,iBAAiB,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,oBAAoB,EAAE,IAAI,EAAE;KACpE,CAAC;;qDACsB;wBA5Sb,aAAa;IAVzB,IAAA,gBAAM,EAAC,iBAAiB,CAAC;IACzB,IAAA,eAAK,EAAC,CAAC,YAAY,CAAC,CAAC;IACrB,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,WAAW,CAAC,CAAC;IACpB,IAAA,eAAK,EAAC,CAAC,eAAe,CAAC,CAAC;IACxB,IAAA,eAAK,EAAC,CAAC,kBAAkB,CAAC,CAAC;IAC3B,IAAA,eAAK,EAAC,CAAC,aAAa,CAAC,CAAC;IACtB,IAAA,eAAK,EAAC,CAAC,YAAY,CAAC,CAAC;IACrB,IAAA,eAAK,EAAC,CAAC,WAAW,CAAC,CAAC;IACpB,IAAA,eAAK,EAAC,CAAC,gBAAgB,CAAC,CAAC;GACb,aAAa,CA2ezB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\domain\\entities\\vulnerability.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  OneToMany,\r\n  ManyToMany,\r\n  JoinTable,\r\n} from 'typeorm';\r\nimport { VulnerabilityAssessment } from './vulnerability-assessment.entity';\r\nimport { VulnerabilityException } from './vulnerability-exception.entity';\r\nimport { Asset } from '../../../asset-management/domain/entities/asset.entity';\r\n\r\n/**\r\n * Vulnerability entity\r\n * Represents a security vulnerability with comprehensive metadata and business logic\r\n */\r\n@Entity('vulnerabilities')\r\n@Index(['identifier'])\r\n@Index(['severity'])\r\n@Index(['cvssScore'])\r\n@Index(['publishedDate'])\r\n@Index(['lastModifiedDate'])\r\n@Index(['exploitable'])\r\n@Index(['hasExploit'])\r\n@Index(['inTheWild'])\r\n@Index(['patchAvailable'])\r\nexport class Vulnerability {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * Vulnerability identifier (CVE, vendor-specific, etc.)\r\n   */\r\n  @Column({ unique: true })\r\n  identifier: string;\r\n\r\n  /**\r\n   * Vulnerability title/name\r\n   */\r\n  @Column({ length: 500 })\r\n  title: string;\r\n\r\n  /**\r\n   * Detailed vulnerability description\r\n   */\r\n  @Column({ type: 'text' })\r\n  description: string;\r\n\r\n  /**\r\n   * Vulnerability severity level\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['info', 'low', 'medium', 'high', 'critical'],\r\n  })\r\n  severity: 'info' | 'low' | 'medium' | 'high' | 'critical';\r\n\r\n  /**\r\n   * CVSS v3.1 base score\r\n   */\r\n  @Column({ name: 'cvss_score', type: 'decimal', precision: 3, scale: 1, nullable: true })\r\n  cvssScore?: number;\r\n\r\n  /**\r\n   * CVSS v3.1 vector string\r\n   */\r\n  @Column({ name: 'cvss_vector', nullable: true })\r\n  cvssVector?: string;\r\n\r\n  /**\r\n   * CVSS v3.1 metrics breakdown\r\n   */\r\n  @Column({ name: 'cvss_metrics', type: 'jsonb', nullable: true })\r\n  cvssMetrics?: {\r\n    attackVector: 'network' | 'adjacent' | 'local' | 'physical';\r\n    attackComplexity: 'low' | 'high';\r\n    privilegesRequired: 'none' | 'low' | 'high';\r\n    userInteraction: 'none' | 'required';\r\n    scope: 'unchanged' | 'changed';\r\n    confidentialityImpact: 'none' | 'low' | 'high';\r\n    integrityImpact: 'none' | 'low' | 'high';\r\n    availabilityImpact: 'none' | 'low' | 'high';\r\n    exploitCodeMaturity?: 'not_defined' | 'unproven' | 'proof_of_concept' | 'functional' | 'high';\r\n    remediationLevel?: 'not_defined' | 'official_fix' | 'temporary_fix' | 'workaround' | 'unavailable';\r\n    reportConfidence?: 'not_defined' | 'unknown' | 'reasonable' | 'confirmed';\r\n  };\r\n\r\n  /**\r\n   * CWE (Common Weakness Enumeration) identifiers\r\n   */\r\n  @Column({ name: 'cwe_ids', type: 'text', array: true, default: '{}' })\r\n  cweIds: string[];\r\n\r\n  /**\r\n   * Vulnerability type/category\r\n   */\r\n  @Column({ name: 'vulnerability_type', nullable: true })\r\n  vulnerabilityType?: string;\r\n\r\n  /**\r\n   * Whether vulnerability is exploitable\r\n   */\r\n  @Column({ default: false })\r\n  exploitable: boolean;\r\n\r\n  /**\r\n   * Whether exploit code is available\r\n   */\r\n  @Column({ name: 'has_exploit', default: false })\r\n  hasExploit: boolean;\r\n\r\n  /**\r\n   * Whether vulnerability is being exploited in the wild\r\n   */\r\n  @Column({ name: 'in_the_wild', default: false })\r\n  inTheWild: boolean;\r\n\r\n  /**\r\n   * Whether patch is available\r\n   */\r\n  @Column({ name: 'patch_available', default: false })\r\n  patchAvailable: boolean;\r\n\r\n  /**\r\n   * Affected products and versions\r\n   */\r\n  @Column({ name: 'affected_products', type: 'jsonb' })\r\n  affectedProducts: Array<{\r\n    vendor: string;\r\n    product: string;\r\n    versions: Array<{\r\n      version: string;\r\n      versionType?: string;\r\n      lessThan?: string;\r\n      lessThanOrEqual?: string;\r\n      greaterThan?: string;\r\n      greaterThanOrEqual?: string;\r\n    }>;\r\n    platforms?: string[];\r\n  }>;\r\n\r\n  /**\r\n   * Vulnerability references\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  references?: Array<{\r\n    url: string;\r\n    name?: string;\r\n    source: string;\r\n    tags?: string[];\r\n  }>;\r\n\r\n  /**\r\n   * Vendor advisory information\r\n   */\r\n  @Column({ name: 'vendor_advisory', type: 'jsonb', nullable: true })\r\n  vendorAdvisory?: {\r\n    id?: string;\r\n    url?: string;\r\n    title?: string;\r\n    summary?: string;\r\n    publishedDate?: string;\r\n    lastUpdatedDate?: string;\r\n  };\r\n\r\n  /**\r\n   * Patch information\r\n   */\r\n  @Column({ name: 'patch_info', type: 'jsonb', nullable: true })\r\n  patchInfo?: {\r\n    patches: Array<{\r\n      id: string;\r\n      title: string;\r\n      vendor: string;\r\n      product: string;\r\n      version: string;\r\n      releaseDate: string;\r\n      downloadUrl?: string;\r\n      description?: string;\r\n      prerequisites?: string[];\r\n      rebootRequired?: boolean;\r\n      supersedes?: string[];\r\n    }>;\r\n    workarounds?: Array<{\r\n      description: string;\r\n      effectiveness: 'low' | 'medium' | 'high';\r\n      complexity: 'low' | 'medium' | 'high';\r\n      sideEffects?: string[];\r\n    }>;\r\n    mitigations?: Array<{\r\n      type: 'configuration' | 'network' | 'access_control' | 'monitoring';\r\n      description: string;\r\n      effectiveness: 'low' | 'medium' | 'high';\r\n      implementation: string;\r\n    }>;\r\n  };\r\n\r\n  /**\r\n   * Exploit information\r\n   */\r\n  @Column({ name: 'exploit_info', type: 'jsonb', nullable: true })\r\n  exploitInfo?: {\r\n    exploits: Array<{\r\n      id?: string;\r\n      name: string;\r\n      type: 'remote' | 'local' | 'web' | 'client_side';\r\n      complexity: 'low' | 'medium' | 'high';\r\n      reliability: 'unreliable' | 'average' | 'excellent';\r\n      source: string;\r\n      url?: string;\r\n      publishedDate?: string;\r\n      description?: string;\r\n    }>;\r\n    exploitKits?: Array<{\r\n      name: string;\r\n      family: string;\r\n      firstSeen: string;\r\n      lastSeen?: string;\r\n      prevalence: 'low' | 'medium' | 'high';\r\n    }>;\r\n    malwareUsage?: Array<{\r\n      malwareFamily: string;\r\n      firstSeen: string;\r\n      lastSeen?: string;\r\n      campaigns?: string[];\r\n    }>;\r\n  };\r\n\r\n  /**\r\n   * Threat intelligence context\r\n   */\r\n  @Column({ name: 'threat_context', type: 'jsonb', nullable: true })\r\n  threatContext?: {\r\n    threatActors?: Array<{\r\n      name: string;\r\n      aliases?: string[];\r\n      motivation: string;\r\n      sophistication: 'low' | 'medium' | 'high';\r\n      firstSeen?: string;\r\n      lastSeen?: string;\r\n    }>;\r\n    campaigns?: Array<{\r\n      name: string;\r\n      description: string;\r\n      startDate?: string;\r\n      endDate?: string;\r\n      targets?: string[];\r\n    }>;\r\n    iocs?: Array<{\r\n      type: 'ip' | 'domain' | 'url' | 'hash' | 'email' | 'file';\r\n      value: string;\r\n      confidence: 'low' | 'medium' | 'high';\r\n      firstSeen: string;\r\n      lastSeen?: string;\r\n      source: string;\r\n    }>;\r\n  };\r\n\r\n  /**\r\n   * Vulnerability tags for categorization\r\n   */\r\n  @Column({ type: 'text', array: true, default: '{}' })\r\n  tags: string[];\r\n\r\n  /**\r\n   * Data source information\r\n   */\r\n  @Column({ name: 'data_source', type: 'jsonb' })\r\n  dataSource: {\r\n    name: string;\r\n    type: 'nvd' | 'vendor' | 'researcher' | 'commercial' | 'internal';\r\n    url?: string;\r\n    lastUpdated: string;\r\n    confidence: 'low' | 'medium' | 'high';\r\n    version?: string;\r\n  };\r\n\r\n  /**\r\n   * When vulnerability was first published\r\n   */\r\n  @Column({ name: 'published_date', type: 'timestamp with time zone' })\r\n  publishedDate: Date;\r\n\r\n  /**\r\n   * When vulnerability was last modified\r\n   */\r\n  @Column({ name: 'last_modified_date', type: 'timestamp with time zone' })\r\n  lastModifiedDate: Date;\r\n\r\n  /**\r\n   * When vulnerability was discovered\r\n   */\r\n  @Column({ name: 'discovered_date', type: 'timestamp with time zone', nullable: true })\r\n  discoveredDate?: Date;\r\n\r\n  /**\r\n   * When vulnerability was disclosed\r\n   */\r\n  @Column({ name: 'disclosure_date', type: 'timestamp with time zone', nullable: true })\r\n  disclosureDate?: Date;\r\n\r\n  /**\r\n   * Custom attributes for organization-specific data\r\n   */\r\n  @Column({ name: 'custom_attributes', type: 'jsonb', nullable: true })\r\n  customAttributes?: Record<string, any>;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  // Relationships\r\n  @OneToMany(() => VulnerabilityAssessment, assessment => assessment.vulnerability)\r\n  assessments: VulnerabilityAssessment[];\r\n\r\n  @OneToMany(() => VulnerabilityException, exception => exception.vulnerability)\r\n  exceptions: VulnerabilityException[];\r\n\r\n  @ManyToMany(() => Asset, asset => asset.vulnerabilities)\r\n  @JoinTable({\r\n    name: 'asset_vulnerabilities',\r\n    joinColumn: { name: 'vulnerability_id', referencedColumnName: 'id' },\r\n    inverseJoinColumn: { name: 'asset_id', referencedColumnName: 'id' },\r\n  })\r\n  affectedAssets: Asset[];\r\n\r\n  /**\r\n   * Check if vulnerability is critical\r\n   */\r\n  get isCritical(): boolean {\r\n    return this.severity === 'critical';\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability is high risk\r\n   */\r\n  get isHighRisk(): boolean {\r\n    return this.severity === 'critical' ||\r\n           (this.severity === 'high' && this.exploitable) ||\r\n           this.hasExploit ||\r\n           this.inTheWild;\r\n  }\r\n\r\n  /**\r\n   * Get vulnerability age in days\r\n   */\r\n  get ageInDays(): number {\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - this.publishedDate.getTime();\r\n    return Math.floor(diffMs / (1000 * 60 * 60 * 24));\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability is recent (published within last 30 days)\r\n   */\r\n  get isRecent(): boolean {\r\n    return this.ageInDays <= 30;\r\n  }\r\n\r\n  /**\r\n   * Check if vulnerability has been updated recently\r\n   */\r\n  get isRecentlyUpdated(): boolean {\r\n    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);\r\n    return this.lastModifiedDate > thirtyDaysAgo;\r\n  }\r\n\r\n  /**\r\n   * Get EPSS (Exploit Prediction Scoring System) score if available\r\n   */\r\n  get epssScore(): number | null {\r\n    return this.customAttributes?.epss?.score || null;\r\n  }\r\n\r\n  /**\r\n   * Get SSVC (Stakeholder-Specific Vulnerability Categorization) decision if available\r\n   */\r\n  get ssvcDecision(): string | null {\r\n    return this.customAttributes?.ssvc?.decision || null;\r\n  }\r\n\r\n  /**\r\n   * Calculate risk score based on various factors\r\n   */\r\n  calculateRiskScore(): number {\r\n    let score = 0;\r\n\r\n    // Base score from CVSS\r\n    if (this.cvssScore) {\r\n      score = this.cvssScore;\r\n    } else {\r\n      // Fallback severity scoring\r\n      const severityScores = { info: 1, low: 3, medium: 5, high: 7, critical: 9 };\r\n      score = severityScores[this.severity];\r\n    }\r\n\r\n    // Exploitability factors\r\n    if (this.exploitable) score += 1;\r\n    if (this.hasExploit) score += 1.5;\r\n    if (this.inTheWild) score += 2;\r\n\r\n    // Patch availability factor\r\n    if (!this.patchAvailable) score += 1;\r\n\r\n    // Age factor (newer vulnerabilities might be riskier)\r\n    if (this.isRecent) score += 0.5;\r\n\r\n    // EPSS score factor\r\n    const epss = this.epssScore;\r\n    if (epss && epss > 0.5) score += 1;\r\n\r\n    return Math.min(10, score);\r\n  }\r\n\r\n  /**\r\n   * Get affected product names\r\n   */\r\n  getAffectedProductNames(): string[] {\r\n    return this.affectedProducts.map(p => `${p.vendor} ${p.product}`);\r\n  }\r\n\r\n  /**\r\n   * Check if product/version is affected\r\n   */\r\n  isProductAffected(vendor: string, product: string, version?: string): boolean {\r\n    const affectedProduct = this.affectedProducts.find(\r\n      p => p.vendor.toLowerCase() === vendor.toLowerCase() &&\r\n           p.product.toLowerCase() === product.toLowerCase()\r\n    );\r\n\r\n    if (!affectedProduct) return false;\r\n    if (!version) return true;\r\n\r\n    // Simple version matching - in production, use proper version comparison\r\n    return affectedProduct.versions.some(v => v.version === version);\r\n  }\r\n\r\n  /**\r\n   * Add tag to vulnerability\r\n   */\r\n  addTag(tag: string): void {\r\n    if (!this.tags.includes(tag)) {\r\n      this.tags.push(tag);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove tag from vulnerability\r\n   */\r\n  removeTag(tag: string): void {\r\n    this.tags = this.tags.filter(t => t !== tag);\r\n  }\r\n\r\n  /**\r\n   * Update exploit status\r\n   */\r\n  updateExploitStatus(hasExploit: boolean, inTheWild: boolean): void {\r\n    this.hasExploit = hasExploit;\r\n    this.inTheWild = inTheWild;\r\n    this.exploitable = hasExploit || inTheWild;\r\n  }\r\n\r\n  /**\r\n   * Update patch availability\r\n   */\r\n  updatePatchStatus(available: boolean, patchInfo?: any): void {\r\n    this.patchAvailable = available;\r\n    if (patchInfo) {\r\n      this.patchInfo = patchInfo;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get vulnerability summary\r\n   */\r\n  getSummary(): any {\r\n    return {\r\n      id: this.id,\r\n      identifier: this.identifier,\r\n      title: this.title,\r\n      severity: this.severity,\r\n      cvssScore: this.cvssScore,\r\n      exploitable: this.exploitable,\r\n      hasExploit: this.hasExploit,\r\n      inTheWild: this.inTheWild,\r\n      patchAvailable: this.patchAvailable,\r\n      isCritical: this.isCritical,\r\n      isHighRisk: this.isHighRisk,\r\n      isRecent: this.isRecent,\r\n      ageInDays: this.ageInDays,\r\n      riskScore: this.calculateRiskScore(),\r\n      affectedProductCount: this.affectedProducts.length,\r\n      publishedDate: this.publishedDate,\r\n      lastModifiedDate: this.lastModifiedDate,\r\n      tags: this.tags,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Export vulnerability for reporting\r\n   */\r\n  exportForReporting(): any {\r\n    return {\r\n      vulnerability: this.getSummary(),\r\n      description: this.description,\r\n      cvssMetrics: this.cvssMetrics,\r\n      affectedProducts: this.affectedProducts,\r\n      references: this.references,\r\n      patchInfo: this.patchInfo,\r\n      exploitInfo: this.exploitInfo,\r\n      threatContext: this.threatContext,\r\n      dataSource: this.dataSource,\r\n      exportedAt: new Date().toISOString(),\r\n    };\r\n  }\r\n}\r\n"], "version": 3}