f1702868fd20c3104733ec3143d1a72f
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityEvent = void 0;
const base_aggregate_root_1 = require("../../../../shared-kernel/domain/base-aggregate-root");
const event_processing_status_enum_1 = require("../../enums/event-processing-status.enum");
const security_event_created_event_1 = require("../../events/security-event-created.event");
const security_event_status_changed_event_1 = require("../../events/security-event-status-changed.event");
/**
 * Security Event Entity
 *
 * Represents a security event in the system with full lifecycle tracking.
 * Serves as the aggregate root for event processing workflows.
 *
 * Key responsibilities:
 * - Event lifecycle management
 * - Status transitions and validation
 * - Processing attempt tracking
 * - Domain event publishing
 * - Business rule enforcement
 *
 * Business Rules:
 * - Events can only transition to valid next statuses
 * - Failed events can be retried with limits
 * - Terminal statuses cannot be changed
 * - Processing attempts are tracked and limited
 */
class SecurityEvent extends base_aggregate_root_1.BaseAggregateRoot {
    constructor(props, id) {
        super(props, id);
    }
    validate() {
        if (!this.props.metadata) {
            throw new Error('Security event must have metadata');
        }
        if (!this.props.status) {
            throw new Error('Security event must have a status');
        }
        if (!Object.values(event_processing_status_enum_1.EventProcessingStatus).includes(this.props.status)) {
            throw new Error(`Invalid event processing status: ${this.props.status}`);
        }
        if (!this.props.rawData || Object.keys(this.props.rawData).length === 0) {
            throw new Error('Security event must have raw data');
        }
        if (!this.props.title || this.props.title.trim().length === 0) {
            throw new Error('Security event must have a title');
        }
        if (this.props.title.length > SecurityEvent.MAX_TITLE_LENGTH) {
            throw new Error(`Event title cannot exceed ${SecurityEvent.MAX_TITLE_LENGTH} characters`);
        }
        if (this.props.description && this.props.description.length > SecurityEvent.MAX_DESCRIPTION_LENGTH) {
            throw new Error(`Event description cannot exceed ${SecurityEvent.MAX_DESCRIPTION_LENGTH} characters`);
        }
        if (this.props.processingAttempts < 0) {
            throw new Error('Processing attempts cannot be negative');
        }
        if (this.props.processingAttempts > SecurityEvent.MAX_PROCESSING_ATTEMPTS) {
            throw new Error(`Processing attempts cannot exceed ${SecurityEvent.MAX_PROCESSING_ATTEMPTS}`);
        }
        if (this.props.processingDuration !== undefined && this.props.processingDuration < 0) {
            throw new Error('Processing duration cannot be negative');
        }
    }
    /**
     * Create a new security event
     */
    static create(metadata, rawData, title, options) {
        const props = {
            metadata,
            status: event_processing_status_enum_1.EventProcessingStatus.RAW,
            rawData,
            title: title.trim(),
            description: options?.description?.trim(),
            category: options?.category?.trim(),
            subcategory: options?.subcategory?.trim(),
            processingAttempts: 0,
            tags: options?.tags || [],
            attributes: options?.attributes || {},
        };
        const event = new SecurityEvent(props);
        // Publish domain event
        event.addDomainEvent(new security_event_created_event_1.SecurityEventCreatedEvent(event.id, {
            eventId: event.id.toString(),
            title: event.props.title,
            category: event.props.category,
            sourceType: event.props.metadata.source.type,
            sourceIdentifier: event.props.metadata.source.identifier,
            timestamp: event.props.metadata.timestamp.toISOString(),
        }));
        return event;
    }
    /**
     * Get event metadata
     */
    get metadata() {
        return this.props.metadata;
    }
    /**
     * Get current status
     */
    get status() {
        return this.props.status;
    }
    /**
     * Get raw event data
     */
    get rawData() {
        return { ...this.props.rawData };
    }
    /**
     * Get event title
     */
    get title() {
        return this.props.title;
    }
    /**
     * Get event description
     */
    get description() {
        return this.props.description;
    }
    /**
     * Get event category
     */
    get category() {
        return this.props.category;
    }
    /**
     * Get event subcategory
     */
    get subcategory() {
        return this.props.subcategory;
    }
    /**
     * Get error message
     */
    get errorMessage() {
        return this.props.errorMessage;
    }
    /**
     * Get processing attempts count
     */
    get processingAttempts() {
        return this.props.processingAttempts;
    }
    /**
     * Get last processed timestamp
     */
    get lastProcessedAt() {
        return this.props.lastProcessedAt;
    }
    /**
     * Get processing duration
     */
    get processingDuration() {
        return this.props.processingDuration;
    }
    /**
     * Get event tags
     */
    get tags() {
        return [...this.props.tags];
    }
    /**
     * Get event attributes
     */
    get attributes() {
        return { ...this.props.attributes };
    }
    /**
     * Check if event is in terminal status
     */
    isTerminal() {
        const terminalStatuses = [
            event_processing_status_enum_1.EventProcessingStatus.RESOLVED,
            event_processing_status_enum_1.EventProcessingStatus.ARCHIVED,
            event_processing_status_enum_1.EventProcessingStatus.FAILED,
            event_processing_status_enum_1.EventProcessingStatus.DISCARDED,
            event_processing_status_enum_1.EventProcessingStatus.SKIPPED,
            event_processing_status_enum_1.EventProcessingStatus.TIMEOUT,
        ];
        return terminalStatuses.includes(this.props.status);
    }
    /**
     * Check if event is in progress
     */
    isInProgress() {
        const inProgressStatuses = [
            event_processing_status_enum_1.EventProcessingStatus.NORMALIZING,
            event_processing_status_enum_1.EventProcessingStatus.ENRICHING,
            event_processing_status_enum_1.EventProcessingStatus.CORRELATING,
            event_processing_status_enum_1.EventProcessingStatus.ANALYZING,
            event_processing_status_enum_1.EventProcessingStatus.INVESTIGATING,
            event_processing_status_enum_1.EventProcessingStatus.REPROCESSING,
        ];
        return inProgressStatuses.includes(this.props.status);
    }
    /**
     * Check if event requires attention
     */
    requiresAttention() {
        const attentionStatuses = [
            event_processing_status_enum_1.EventProcessingStatus.PENDING_REVIEW,
            event_processing_status_enum_1.EventProcessingStatus.INVESTIGATING,
            event_processing_status_enum_1.EventProcessingStatus.FAILED,
            event_processing_status_enum_1.EventProcessingStatus.ON_HOLD,
            event_processing_status_enum_1.EventProcessingStatus.TIMEOUT,
        ];
        return attentionStatuses.includes(this.props.status);
    }
    /**
     * Check if event can be retried
     */
    canRetry() {
        return this.props.status === event_processing_status_enum_1.EventProcessingStatus.FAILED &&
            this.props.processingAttempts < SecurityEvent.MAX_PROCESSING_ATTEMPTS;
    }
    /**
     * Check if event has specific tag
     */
    hasTag(tag) {
        return this.props.tags.includes(tag);
    }
    /**
     * Get attribute value
     */
    getAttribute(key) {
        return this.props.attributes[key];
    }
    /**
     * Change event status
     */
    changeStatus(newStatus, options) {
        if (this.isTerminal()) {
            throw new Error(`Cannot change status from terminal state: ${this.props.status}`);
        }
        if (!this.isValidStatusTransition(newStatus)) {
            throw new Error(`Invalid status transition from ${this.props.status} to ${newStatus}`);
        }
        const oldStatus = this.props.status;
        this.props.status = newStatus;
        this.props.lastProcessedAt = new Date();
        if (options?.errorMessage) {
            this.props.errorMessage = options.errorMessage;
        }
        if (options?.processingDuration !== undefined) {
            this.props.processingDuration = options.processingDuration;
        }
        // Increment processing attempts for certain statuses
        if (this.shouldIncrementAttempts(newStatus)) {
            this.props.processingAttempts++;
        }
        // Publish status change event
        this.addDomainEvent(new security_event_status_changed_event_1.SecurityEventStatusChangedEvent(this.id, {
            eventId: this.id.toString(),
            oldStatus,
            newStatus,
            processingAttempts: this.props.processingAttempts,
            errorMessage: options?.errorMessage,
            timestamp: new Date().toISOString(),
        }));
    }
    isValidStatusTransition(newStatus) {
        const validTransitions = {
            [event_processing_status_enum_1.EventProcessingStatus.RAW]: [
                event_processing_status_enum_1.EventProcessingStatus.NORMALIZING,
                event_processing_status_enum_1.EventProcessingStatus.SKIPPED,
                event_processing_status_enum_1.EventProcessingStatus.FAILED,
            ],
            [event_processing_status_enum_1.EventProcessingStatus.NORMALIZING]: [
                event_processing_status_enum_1.EventProcessingStatus.NORMALIZED,
                event_processing_status_enum_1.EventProcessingStatus.FAILED,
                event_processing_status_enum_1.EventProcessingStatus.TIMEOUT,
            ],
            [event_processing_status_enum_1.EventProcessingStatus.NORMALIZED]: [
                event_processing_status_enum_1.EventProcessingStatus.ENRICHING,
                event_processing_status_enum_1.EventProcessingStatus.SKIPPED,
                event_processing_status_enum_1.EventProcessingStatus.FAILED,
            ],
            [event_processing_status_enum_1.EventProcessingStatus.ENRICHING]: [
                event_processing_status_enum_1.EventProcessingStatus.ENRICHED,
                event_processing_status_enum_1.EventProcessingStatus.FAILED,
                event_processing_status_enum_1.EventProcessingStatus.TIMEOUT,
            ],
            [event_processing_status_enum_1.EventProcessingStatus.ENRICHED]: [
                event_processing_status_enum_1.EventProcessingStatus.CORRELATING,
                event_processing_status_enum_1.EventProcessingStatus.ANALYZING,
                event_processing_status_enum_1.EventProcessingStatus.FAILED,
            ],
            [event_processing_status_enum_1.EventProcessingStatus.CORRELATING]: [
                event_processing_status_enum_1.EventProcessingStatus.CORRELATED,
                event_processing_status_enum_1.EventProcessingStatus.FAILED,
                event_processing_status_enum_1.EventProcessingStatus.TIMEOUT,
            ],
            [event_processing_status_enum_1.EventProcessingStatus.CORRELATED]: [
                event_processing_status_enum_1.EventProcessingStatus.ANALYZING,
                event_processing_status_enum_1.EventProcessingStatus.FAILED,
            ],
            [event_processing_status_enum_1.EventProcessingStatus.ANALYZING]: [
                event_processing_status_enum_1.EventProcessingStatus.ANALYZED,
                event_processing_status_enum_1.EventProcessingStatus.FAILED,
                event_processing_status_enum_1.EventProcessingStatus.TIMEOUT,
            ],
            [event_processing_status_enum_1.EventProcessingStatus.ANALYZED]: [
                event_processing_status_enum_1.EventProcessingStatus.PENDING_REVIEW,
                event_processing_status_enum_1.EventProcessingStatus.RESOLVED,
                event_processing_status_enum_1.EventProcessingStatus.DISCARDED,
            ],
            [event_processing_status_enum_1.EventProcessingStatus.PENDING_REVIEW]: [
                event_processing_status_enum_1.EventProcessingStatus.INVESTIGATING,
                event_processing_status_enum_1.EventProcessingStatus.DISCARDED,
                event_processing_status_enum_1.EventProcessingStatus.ON_HOLD,
            ],
            [event_processing_status_enum_1.EventProcessingStatus.INVESTIGATING]: [
                event_processing_status_enum_1.EventProcessingStatus.RESOLVED,
                event_processing_status_enum_1.EventProcessingStatus.ON_HOLD,
                event_processing_status_enum_1.EventProcessingStatus.PENDING_REVIEW,
            ],
            [event_processing_status_enum_1.EventProcessingStatus.ON_HOLD]: [
                event_processing_status_enum_1.EventProcessingStatus.INVESTIGATING,
                event_processing_status_enum_1.EventProcessingStatus.PENDING_REVIEW,
                event_processing_status_enum_1.EventProcessingStatus.DISCARDED,
            ],
            [event_processing_status_enum_1.EventProcessingStatus.FAILED]: [
                event_processing_status_enum_1.EventProcessingStatus.REPROCESSING,
                event_processing_status_enum_1.EventProcessingStatus.DISCARDED,
            ],
            [event_processing_status_enum_1.EventProcessingStatus.TIMEOUT]: [
                event_processing_status_enum_1.EventProcessingStatus.REPROCESSING,
                event_processing_status_enum_1.EventProcessingStatus.DISCARDED,
            ],
            [event_processing_status_enum_1.EventProcessingStatus.REPROCESSING]: [
                event_processing_status_enum_1.EventProcessingStatus.NORMALIZED,
                event_processing_status_enum_1.EventProcessingStatus.FAILED,
            ],
            [event_processing_status_enum_1.EventProcessingStatus.RESOLVED]: [
                event_processing_status_enum_1.EventProcessingStatus.ARCHIVED,
                event_processing_status_enum_1.EventProcessingStatus.REPROCESSING,
            ],
            // Terminal statuses have no valid transitions
            [event_processing_status_enum_1.EventProcessingStatus.ARCHIVED]: [],
            [event_processing_status_enum_1.EventProcessingStatus.DISCARDED]: [],
            [event_processing_status_enum_1.EventProcessingStatus.SKIPPED]: [],
        };
        return validTransitions[this.props.status]?.includes(newStatus) || false;
    }
    shouldIncrementAttempts(status) {
        return [
            event_processing_status_enum_1.EventProcessingStatus.NORMALIZING,
            event_processing_status_enum_1.EventProcessingStatus.ENRICHING,
            event_processing_status_enum_1.EventProcessingStatus.CORRELATING,
            event_processing_status_enum_1.EventProcessingStatus.ANALYZING,
            event_processing_status_enum_1.EventProcessingStatus.REPROCESSING,
        ].includes(status);
    }
    /**
     * Add tags to the event
     */
    addTags(tags) {
        const newTags = tags.filter(tag => !this.props.tags.includes(tag));
        this.props.tags.push(...newTags);
    }
    /**
     * Remove tags from the event
     */
    removeTags(tags) {
        this.props.tags = this.props.tags.filter(tag => !tags.includes(tag));
    }
    /**
     * Set attribute value
     */
    setAttribute(key, value) {
        this.props.attributes[key] = value;
    }
    /**
     * Remove attribute
     */
    removeAttribute(key) {
        delete this.props.attributes[key];
    }
    /**
     * Update event description
     */
    updateDescription(description) {
        if (description.length > SecurityEvent.MAX_DESCRIPTION_LENGTH) {
            throw new Error(`Description cannot exceed ${SecurityEvent.MAX_DESCRIPTION_LENGTH} characters`);
        }
        this.props.description = description.trim();
    }
    /**
     * Mark event as failed
     */
    markAsFailed(errorMessage, processingDuration) {
        this.changeStatus(event_processing_status_enum_1.EventProcessingStatus.FAILED, {
            errorMessage,
            processingDuration,
        });
    }
    /**
     * Mark event as resolved
     */
    markAsResolved(processingDuration) {
        this.changeStatus(event_processing_status_enum_1.EventProcessingStatus.RESOLVED, {
            processingDuration,
        });
    }
    /**
     * Get event age in milliseconds
     */
    getAge() {
        return this.props.metadata.timestamp.getAge();
    }
    /**
     * Get processing summary
     */
    getProcessingSummary() {
        return {
            status: this.props.status,
            attempts: this.props.processingAttempts,
            maxAttempts: SecurityEvent.MAX_PROCESSING_ATTEMPTS,
            canRetry: this.canRetry(),
            isTerminal: this.isTerminal(),
            isInProgress: this.isInProgress(),
            requiresAttention: this.requiresAttention(),
            lastProcessedAt: this.props.lastProcessedAt?.toISOString(),
            processingDuration: this.props.processingDuration,
            errorMessage: this.props.errorMessage,
        };
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            id: this.id.toString(),
            metadata: this.props.metadata.toJSON(),
            status: this.props.status,
            title: this.props.title,
            description: this.props.description,
            category: this.props.category,
            subcategory: this.props.subcategory,
            tags: this.props.tags,
            attributes: this.props.attributes,
            processingSummary: this.getProcessingSummary(),
            age: this.getAge(),
            createdAt: this.createdAt?.toISOString(),
            updatedAt: this.updatedAt?.toISOString(),
        };
    }
}
exports.SecurityEvent = SecurityEvent;
SecurityEvent.MAX_PROCESSING_ATTEMPTS = 3;
SecurityEvent.MAX_TITLE_LENGTH = 255;
SecurityEvent.MAX_DESCRIPTION_LENGTH = 2000;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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