71a34d13860f4dc727aeae33652ed963
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetVulnerability = void 0;
const typeorm_1 = require("typeorm");
const asset_entity_1 = require("./asset.entity");
/**
 * Asset Vulnerability entity
 * Represents vulnerabilities associated with specific assets
 */
let AssetVulnerability = class AssetVulnerability {
    /**
     * Check if vulnerability is open
     */
    get isOpen() {
        return ['open', 'confirmed', 'investigating'].includes(this.status);
    }
    /**
     * Check if vulnerability is critical
     */
    get isCritical() {
        return this.severity === 'critical';
    }
    /**
     * Check if vulnerability is high risk
     */
    get isHighRisk() {
        return this.severity === 'critical' ||
            (this.severity === 'high' && this.exploitable) ||
            this.hasExploit ||
            this.inTheWild;
    }
    /**
     * Get vulnerability age in days
     */
    get ageInDays() {
        const now = new Date();
        const diffMs = now.getTime() - this.discoveredAt.getTime();
        return Math.floor(diffMs / (1000 * 60 * 60 * 24));
    }
    /**
     * Get time to resolution in days
     */
    get timeToResolutionDays() {
        if (!this.resolvedAt)
            return null;
        const diffMs = this.resolvedAt.getTime() - this.discoveredAt.getTime();
        return Math.floor(diffMs / (1000 * 60 * 60 * 24));
    }
    /**
     * Check if vulnerability is overdue for patching
     */
    get isOverdue() {
        if (!this.isOpen)
            return false;
        // Define SLA based on severity
        const slaMatrix = {
            critical: 7, // 7 days
            high: 30, // 30 days
            medium: 90, // 90 days
            low: 180, // 180 days
            info: 365, // 365 days
        };
        const slaDays = slaMatrix[this.severity];
        return this.ageInDays > slaDays;
    }
    /**
     * Calculate risk score for this asset
     */
    calculateRiskScore() {
        let score = 0;
        // Base score from CVSS
        if (this.cvssScore) {
            score = this.cvssScore;
        }
        else {
            // Fallback severity scoring
            const severityScores = { info: 1, low: 3, medium: 5, high: 7, critical: 9 };
            score = severityScores[this.severity];
        }
        // Exploitability factors
        if (this.exploitable)
            score += 1;
        if (this.hasExploit)
            score += 1;
        if (this.inTheWild)
            score += 2;
        // Asset-specific factors
        const assetCriticality = this.assetSpecificDetails.businessImpact?.assetCriticality;
        if (assetCriticality === 'critical')
            score += 2;
        else if (assetCriticality === 'high')
            score += 1;
        // Risk factors
        if (this.riskFactors) {
            if (this.riskFactors.assetExposure === 'external')
                score += 1;
            if (this.riskFactors.accessControls === 'weak' || this.riskFactors.accessControls === 'none')
                score += 1;
            if (this.riskFactors.patchingCadence === 'delayed' || this.riskFactors.patchingCadence === 'none')
                score += 1;
            if (this.riskFactors.incidentHistory)
                score += 0.5;
        }
        return Math.min(10, score);
    }
    /**
     * Update vulnerability status
     */
    updateStatus(status, updatedBy, reason) {
        this.status = status;
        this.updatedBy = updatedBy;
        if (['patched', 'mitigated', 'false_positive', 'accepted_risk'].includes(status)) {
            this.resolvedAt = new Date();
        }
        if (!this.assetSpecificDetails.remediation) {
            this.assetSpecificDetails.remediation = {};
        }
        if (reason) {
            this.assetSpecificDetails.remediation.statusChangeReason = reason;
        }
    }
    /**
     * Assign vulnerability to user
     */
    assign(userId, assignedBy) {
        this.assignedTo = userId;
        this.updatedBy = assignedBy;
    }
    /**
     * Add tag to vulnerability
     */
    addTag(tag) {
        if (!this.tags.includes(tag)) {
            this.tags.push(tag);
        }
    }
    /**
     * Remove tag from vulnerability
     */
    removeTag(tag) {
        this.tags = this.tags.filter(t => t !== tag);
    }
    /**
     * Update risk factors
     */
    updateRiskFactors(riskFactors) {
        this.riskFactors = { ...this.riskFactors, ...riskFactors };
        this.riskScore = this.calculateRiskScore();
    }
    /**
     * Add remediation recommendation
     */
    addRemediation(remediation) {
        if (!this.assetSpecificDetails.remediation) {
            this.assetSpecificDetails.remediation = {};
        }
        if (!this.assetSpecificDetails.remediation.recommendations) {
            this.assetSpecificDetails.remediation.recommendations = [];
        }
        this.assetSpecificDetails.remediation.recommendations.push(remediation);
    }
    /**
     * Mark mitigation as implemented
     */
    implementMitigation(mitigationIndex, implementedBy) {
        if (this.assetSpecificDetails.remediation?.mitigations?.[mitigationIndex]) {
            this.assetSpecificDetails.remediation.mitigations[mitigationIndex].implemented = true;
            this.assetSpecificDetails.remediation.mitigations[mitigationIndex].implementedDate = new Date().toISOString();
            this.updatedBy = implementedBy;
        }
    }
    /**
     * Get vulnerability summary
     */
    getSummary() {
        return {
            id: this.id,
            vulnerabilityId: this.vulnerabilityId,
            vulnerabilityIdentifier: this.vulnerabilityIdentifier,
            title: this.title,
            severity: this.severity,
            cvssScore: this.cvssScore,
            status: this.status,
            assetId: this.assetId,
            exploitable: this.exploitable,
            hasExploit: this.hasExploit,
            inTheWild: this.inTheWild,
            isOpen: this.isOpen,
            isCritical: this.isCritical,
            isHighRisk: this.isHighRisk,
            isOverdue: this.isOverdue,
            ageInDays: this.ageInDays,
            timeToResolutionDays: this.timeToResolutionDays,
            riskScore: this.riskScore || this.calculateRiskScore(),
            discoveredAt: this.discoveredAt,
            lastVerifiedAt: this.lastVerifiedAt,
            resolvedAt: this.resolvedAt,
            assignedTo: this.assignedTo,
            tags: this.tags,
        };
    }
    /**
     * Export vulnerability for reporting
     */
    exportForReporting() {
        return {
            vulnerability: this.getSummary(),
            assetSpecificDetails: this.assetSpecificDetails,
            riskFactors: this.riskFactors,
            exportedAt: new Date().toISOString(),
        };
    }
};
exports.AssetVulnerability = AssetVulnerability;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], AssetVulnerability.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'vulnerability_id', type: 'uuid' }),
    __metadata("design:type", String)
], AssetVulnerability.prototype, "vulnerabilityId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'vulnerability_identifier' }),
    __metadata("design:type", String)
], AssetVulnerability.prototype, "vulnerabilityIdentifier", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 500 }),
    __metadata("design:type", String)
], AssetVulnerability.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], AssetVulnerability.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['info', 'low', 'medium', 'high', 'critical'],
    }),
    __metadata("design:type", String)
], AssetVulnerability.prototype, "severity", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cvss_score', type: 'decimal', precision: 3, scale: 1, nullable: true }),
    __metadata("design:type", Number)
], AssetVulnerability.prototype, "cvssScore", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cvss_vector', nullable: true }),
    __metadata("design:type", String)
], AssetVulnerability.prototype, "cvssVector", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['open', 'confirmed', 'false_positive', 'mitigated', 'patched', 'accepted_risk', 'investigating'],
        default: 'open',
    }),
    __metadata("design:type", String)
], AssetVulnerability.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], AssetVulnerability.prototype, "exploitable", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'has_exploit', default: false }),
    __metadata("design:type", Boolean)
], AssetVulnerability.prototype, "hasExploit", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'in_the_wild', default: false }),
    __metadata("design:type", Boolean)
], AssetVulnerability.prototype, "inTheWild", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    __metadata("design:type", Object)
], AssetVulnerability.prototype, "assetSpecificDetails", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'risk_score', type: 'decimal', precision: 4, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], AssetVulnerability.prototype, "riskScore", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'risk_factors', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], AssetVulnerability.prototype, "riskFactors", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, default: '{}' }),
    __metadata("design:type", Array)
], AssetVulnerability.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'discovered_at', type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], AssetVulnerability.prototype, "discoveredAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_verified_at', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], AssetVulnerability.prototype, "lastVerifiedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'resolved_at', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], AssetVulnerability.prototype, "resolvedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'discovered_by', type: 'uuid' }),
    __metadata("design:type", String)
], AssetVulnerability.prototype, "discoveredBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'assigned_to', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], AssetVulnerability.prototype, "assignedTo", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], AssetVulnerability.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], AssetVulnerability.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_e = typeof Date !== "undefined" && Date) === "function" ? _e : Object)
], AssetVulnerability.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => asset_entity_1.Asset, asset => asset.vulnerabilities, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'asset_id' }),
    __metadata("design:type", typeof (_f = typeof asset_entity_1.Asset !== "undefined" && asset_entity_1.Asset) === "function" ? _f : Object)
], AssetVulnerability.prototype, "asset", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'asset_id', type: 'uuid' }),
    __metadata("design:type", String)
], AssetVulnerability.prototype, "assetId", void 0);
exports.AssetVulnerability = AssetVulnerability = __decorate([
    (0, typeorm_1.Entity)('asset_vulnerabilities'),
    (0, typeorm_1.Index)(['assetId']),
    (0, typeorm_1.Index)(['vulnerabilityId']),
    (0, typeorm_1.Index)(['severity']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['discoveredAt']),
    (0, typeorm_1.Index)(['exploitable']),
    (0, typeorm_1.Index)(['hasExploit'])
], AssetVulnerability);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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