{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\value-objects\\version.spec.ts", "mappings": ";;AAAA,mFAAmE;AAEnE,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;IACvB,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,OAAO,GAAG,8BAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACpC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,OAAO,GAAG,8BAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACpC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,OAAO,GAAG,8BAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;YACnD,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,OAAO,GAAG,8BAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;YAChE,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,OAAO,GAAG,8BAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;YAC/D,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC1C,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,OAAO,GAAG,8BAAO,CAAC,OAAO,EAAE,CAAC;YAClC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACpC,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,OAAO,GAAG,8BAAO,CAAC,WAAW,EAAE,CAAC;YACtC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACpC,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,8BAAO,CAAC,IAAW,CAAC,CAAC,CAAC,OAAO,CAAC,qCAAqC,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,8BAAO,CAAC,SAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,qCAAqC,CAAC,CAAC;QAC7F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,8BAAO,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,8BAAO,CAAC,GAAU,CAAC,CAAC,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,8BAAO,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;YAC5E,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,8BAAO,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;YAChF,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,8BAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;YAC/E,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,8BAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;YAC/E,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,8BAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,CAAC,GAAG,EAAE,CAAC,8BAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,kDAAkD,CAAC,CAAC;YACnG,MAAM,CAAC,GAAG,EAAE,CAAC,8BAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,kDAAkD,CAAC,CAAC;YACnG,MAAM,CAAC,GAAG,EAAE,CAAC,8BAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,kDAAkD,CAAC,CAAC;QACrG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,CAAC,GAAG,EAAE,CAAC,8BAAO,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,qCAAqC,CAAC,CAAC;YACvF,MAAM,CAAC,GAAG,EAAE,CAAC,8BAAO,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,qCAAqC,CAAC,CAAC;YACvF,MAAM,CAAC,GAAG,EAAE,CAAC,8BAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,qCAAqC,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,CAAC,8BAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5C,MAAM,CAAC,8BAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5C,MAAM,CAAC,8BAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,8BAAO,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,8BAAO,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,8BAAO,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,8BAAO,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,CAAC,8BAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3C,MAAM,CAAC,8BAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,8BAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,8BAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,8BAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,8BAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,MAAM,CAAC,8BAAO,CAAC,OAAO,CAAC,IAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjD,MAAM,CAAC,8BAAO,CAAC,OAAO,CAAC,SAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,MAAM,GAAG,8BAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;YAC9B,MAAM,CAAC,MAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,MAAM,GAAG,8BAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACjC,MAAM,OAAO,GAAG,IAAI,8BAAO,CAAC,uBAAuB,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,MAAM,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;YACpC,MAAM,UAAU,GAAG,IAAI,8BAAO,CAAC,eAAe,CAAC,CAAC;YAEhD,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,OAAO,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;YACrC,MAAM,UAAU,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;YACxC,MAAM,iBAAiB,GAAG,IAAI,8BAAO,CAAC,aAAa,CAAC,CAAC;YAErD,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3C,MAAM,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,IAAI,WAAoB,CAAC;QAEzB,UAAU,CAAC,GAAG,EAAE;YACd,WAAW,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,WAAW,GAAG,WAAW,CAAC,cAAc,EAAE,CAAC;YACjD,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACxC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,WAAW,GAAG,WAAW,CAAC,cAAc,EAAE,CAAC;YACjD,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACxC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,WAAW,GAAG,WAAW,CAAC,cAAc,EAAE,CAAC;YACjD,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACxC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,WAAW,GAAG,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAChD,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,WAAW,GAAG,WAAW,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;YACvE,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAClD,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,WAAW,GAAG,WAAW,CAAC,cAAc,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;YACtE,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YACzD,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC9C,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC/C,IAAI,WAAoB,CAAC;QAEzB,UAAU,CAAC,GAAG,EAAE;YACd,WAAW,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uBAAuB,EAAE,GAAG,EAAE;YAC/B,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAC7D,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACnD,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,uCAAuC,CAAC,CAAC;YAC9F,MAAM,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,uCAAuC,CAAC,CAAC;QACnG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,SAAS,GAAG,WAAW,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;YAC7D,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAChD,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;YAC1F,MAAM,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;QAC/F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,UAAU,GAAG,IAAI,8BAAO,CAAC,yBAAyB,CAAC,CAAC;YAC1D,MAAM,MAAM,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,EAAE,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;YAChC,MAAM,EAAE,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;YAEhC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACjC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,EAAE,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;YAChC,MAAM,EAAE,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;YAEhC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,EAAE,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;YAChC,MAAM,EAAE,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;YAEhC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,MAAM,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;YACpC,MAAM,UAAU,GAAG,IAAI,8BAAO,CAAC,aAAa,CAAC,CAAC;YAE9C,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,KAAK,GAAG,IAAI,8BAAO,CAAC,aAAa,CAAC,CAAC;YACzC,MAAM,IAAI,GAAG,IAAI,8BAAO,CAAC,YAAY,CAAC,CAAC;YAEvC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,EAAE,GAAG,IAAI,8BAAO,CAAC,eAAe,CAAC,CAAC;YACxC,MAAM,EAAE,GAAG,IAAI,8BAAO,CAAC,eAAe,CAAC,CAAC;YAExC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,OAAO,GAAG,IAAI,8BAAO,CAAC,eAAe,CAAC,CAAC;YAC7C,MAAM,IAAI,GAAG,IAAI,8BAAO,CAAC,kBAAkB,CAAC,CAAC;YAE7C,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,IAAI,EAAW,CAAC;QAChB,IAAI,EAAW,CAAC;QAChB,IAAI,EAAW,CAAC;QAEhB,UAAU,CAAC,GAAG,EAAE;YACd,EAAE,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;YAC1B,EAAE,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;YAC1B,EAAE,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtC,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,CAAC,EAAE,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5C,MAAM,CAAC,EAAE,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5C,MAAM,CAAC,EAAE,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,CAAC,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzC,MAAM,CAAC,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,EAAE,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,EAAE,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,EAAE,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,IAAI,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;YAClC,MAAM,IAAI,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;YAClC,MAAM,IAAI,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;YAClC,MAAM,IAAI,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;YAElC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,oCAAoC;QACvF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,IAAI,OAAgB,CAAC;QAErB,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEjD,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEjD,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEhD,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEhD,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,OAAO,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;YACrC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,QAAQ,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;YACtC,MAAM,QAAQ,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;YACtC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,QAAQ,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;YACtC,MAAM,QAAQ,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;YACtC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,QAAQ,GAAG,IAAI,8BAAO,CAAC,eAAe,CAAC,CAAC;YAC9C,MAAM,QAAQ,GAAG,IAAI,8BAAO,CAAC,eAAe,CAAC,CAAC;YAC9C,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,MAAM,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;YACpC,MAAM,UAAU,GAAG,IAAI,8BAAO,CAAC,aAAa,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,OAAO,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;YACrC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,SAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,OAAO,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;YACrC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,OAAO,GAAG,IAAI,8BAAO,CAAC,yBAAyB,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wBAAwB,EAAE,GAAG,EAAE;YAChC,MAAM,OAAO,GAAG,IAAI,8BAAO,CAAC,yBAAyB,CAAC,CAAC;YACvD,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;YAE9B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YACnD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3B,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACvC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACjC,MAAM,IAAI,GAAG,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC;YAClD,MAAM,OAAO,GAAG,8BAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEvC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YACtD,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,OAAO,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;YACrC,MAAM,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC;YAEpC,sDAAsD;YACtD,MAAM,CAAC,GAAG,EAAE;gBACT,OAAe,CAAC,MAAM,GAAG,UAAU,CAAC;YACvC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,sDAAsD;YAExE,gCAAgC;YAChC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kBAAkB,EAAE,GAAG,EAAE;YAC1B,MAAM,OAAO,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,QAAQ,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;YACtC,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,EAAE,CAAC;YAE9C,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACrC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,OAAO,GAAG,IAAI,8BAAO,CAAC,sBAAsB,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACnC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACnC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,OAAO,GAAG,IAAI,8BAAO,CAAC,8BAA8B,CAAC,CAAC;YAC5D,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,OAAO,GAAG,IAAI,8BAAO,CAAC,8BAA8B,CAAC,CAAC;YAC5D,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,EAAE,GAAG,IAAI,8BAAO,CAAC,aAAa,CAAC,CAAC;YACtC,MAAM,EAAE,GAAG,IAAI,8BAAO,CAAC,eAAe,CAAC,CAAC;YAExC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,OAAO,GAAG,IAAI,8BAAO,CAAC,OAAO,CAAC,CAAC;YACrC,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\shared-kernel\\__tests__\\value-objects\\version.spec.ts"], "sourcesContent": ["import { Version } from '../../value-objects/version.value-object';\r\n\r\ndescribe('Version', () => {\r\n  describe('creation', () => {\r\n    it('should create version from string', () => {\r\n      const version = Version.fromString('1.2.3');\r\n      expect(version.value).toBe('1.2.3');\r\n      expect(version.major).toBe(1);\r\n      expect(version.minor).toBe(2);\r\n      expect(version.patch).toBe(3);\r\n    });\r\n\r\n    it('should create version from components', () => {\r\n      const version = Version.create(1, 2, 3);\r\n      expect(version.value).toBe('1.2.3');\r\n      expect(version.major).toBe(1);\r\n      expect(version.minor).toBe(2);\r\n      expect(version.patch).toBe(3);\r\n    });\r\n\r\n    it('should create version with prerelease', () => {\r\n      const version = Version.create(1, 2, 3, 'alpha.1');\r\n      expect(version.value).toBe('1.2.3-alpha.1');\r\n      expect(version.prerelease).toBe('alpha.1');\r\n      expect(version.isPrerelease()).toBe(true);\r\n    });\r\n\r\n    it('should create version with build metadata', () => {\r\n      const version = Version.create(1, 2, 3, undefined, 'build.123');\r\n      expect(version.value).toBe('1.2.3+build.123');\r\n      expect(version.buildMetadata).toBe('build.123');\r\n    });\r\n\r\n    it('should create version with prerelease and build metadata', () => {\r\n      const version = Version.create(1, 2, 3, 'beta.2', 'build.456');\r\n      expect(version.value).toBe('1.2.3-beta.2+build.456');\r\n      expect(version.prerelease).toBe('beta.2');\r\n      expect(version.buildMetadata).toBe('build.456');\r\n    });\r\n\r\n    it('should create initial version', () => {\r\n      const version = Version.initial();\r\n      expect(version.value).toBe('0.0.0');\r\n      expect(version.isInitial()).toBe(true);\r\n    });\r\n\r\n    it('should create first stable version', () => {\r\n      const version = Version.firstStable();\r\n      expect(version.value).toBe('1.0.0');\r\n      expect(version.isStable()).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('validation', () => {\r\n    it('should throw error for null value', () => {\r\n      expect(() => new Version(null as any)).toThrow('Version cannot be null or undefined');\r\n    });\r\n\r\n    it('should throw error for undefined value', () => {\r\n      expect(() => new Version(undefined as any)).toThrow('Version cannot be null or undefined');\r\n    });\r\n\r\n    it('should throw error for empty string', () => {\r\n      expect(() => new Version('')).toThrow('Version cannot be empty');\r\n    });\r\n\r\n    it('should throw error for non-string value', () => {\r\n      expect(() => new Version(123 as any)).toThrow('Version must be a string');\r\n    });\r\n\r\n    it('should throw error for invalid semantic version format', () => {\r\n      expect(() => new Version('1.2')).toThrow('Invalid semantic version format');\r\n      expect(() => new Version('1.2.3.4')).toThrow('Invalid semantic version format');\r\n      expect(() => new Version('v1.2.3')).toThrow('Invalid semantic version format');\r\n      expect(() => new Version('1.2.3-')).toThrow('Invalid semantic version format');\r\n      expect(() => new Version('1.2.3+')).toThrow('Invalid semantic version format');\r\n    });\r\n\r\n    it('should throw error for negative version components', () => {\r\n      expect(() => Version.create(-1, 0, 0)).toThrow('Version components must be non-negative integers');\r\n      expect(() => Version.create(0, -1, 0)).toThrow('Version components must be non-negative integers');\r\n      expect(() => Version.create(0, 0, -1)).toThrow('Version components must be non-negative integers');\r\n    });\r\n\r\n    it('should throw error for non-integer version components', () => {\r\n      expect(() => Version.create(1.5, 0, 0)).toThrow('Version components must be integers');\r\n      expect(() => Version.create(0, 2.5, 0)).toThrow('Version components must be integers');\r\n      expect(() => Version.create(0, 0, 3.5)).toThrow('Version components must be integers');\r\n    });\r\n  });\r\n\r\n  describe('static validation methods', () => {\r\n    it('should validate correct semantic version strings', () => {\r\n      expect(Version.isValid('1.2.3')).toBe(true);\r\n      expect(Version.isValid('0.0.0')).toBe(true);\r\n      expect(Version.isValid('10.20.30')).toBe(true);\r\n      expect(Version.isValid('1.2.3-alpha')).toBe(true);\r\n      expect(Version.isValid('1.2.3-alpha.1')).toBe(true);\r\n      expect(Version.isValid('1.2.3+build.1')).toBe(true);\r\n      expect(Version.isValid('1.2.3-alpha.1+build.1')).toBe(true);\r\n    });\r\n\r\n    it('should reject invalid semantic version strings', () => {\r\n      expect(Version.isValid('1.2')).toBe(false);\r\n      expect(Version.isValid('1.2.3.4')).toBe(false);\r\n      expect(Version.isValid('v1.2.3')).toBe(false);\r\n      expect(Version.isValid('1.2.3-')).toBe(false);\r\n      expect(Version.isValid('1.2.3+')).toBe(false);\r\n      expect(Version.isValid('')).toBe(false);\r\n      expect(Version.isValid(null as any)).toBe(false);\r\n      expect(Version.isValid(undefined as any)).toBe(false);\r\n    });\r\n\r\n    it('should try parse valid version', () => {\r\n      const result = Version.tryParse('1.2.3');\r\n      expect(result).not.toBeNull();\r\n      expect(result!.value).toBe('1.2.3');\r\n    });\r\n\r\n    it('should return null for invalid version in tryParse', () => {\r\n      const result = Version.tryParse('invalid-version');\r\n      expect(result).toBeNull();\r\n    });\r\n  });\r\n\r\n  describe('version properties', () => {\r\n    it('should get core version', () => {\r\n      const version = new Version('1.2.3-alpha.1+build.1');\r\n      expect(version.getCoreVersion()).toBe('1.2.3');\r\n    });\r\n\r\n    it('should identify prerelease versions', () => {\r\n      const stable = new Version('1.2.3');\r\n      const prerelease = new Version('1.2.3-alpha.1');\r\n\r\n      expect(stable.isPrerelease()).toBe(false);\r\n      expect(stable.isStable()).toBe(true);\r\n      expect(prerelease.isPrerelease()).toBe(true);\r\n      expect(prerelease.isStable()).toBe(false);\r\n    });\r\n\r\n    it('should identify initial version', () => {\r\n      const initial = new Version('0.0.0');\r\n      const notInitial = new Version('0.0.1');\r\n      const prereleaseInitial = new Version('0.0.0-alpha');\r\n\r\n      expect(initial.isInitial()).toBe(true);\r\n      expect(notInitial.isInitial()).toBe(false);\r\n      expect(prereleaseInitial.isInitial()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('version increment operations', () => {\r\n    let baseVersion: Version;\r\n\r\n    beforeEach(() => {\r\n      baseVersion = new Version('1.2.3');\r\n    });\r\n\r\n    it('should increment major version', () => {\r\n      const incremented = baseVersion.incrementMajor();\r\n      expect(incremented.value).toBe('2.0.0');\r\n      expect(incremented.major).toBe(2);\r\n      expect(incremented.minor).toBe(0);\r\n      expect(incremented.patch).toBe(0);\r\n    });\r\n\r\n    it('should increment minor version', () => {\r\n      const incremented = baseVersion.incrementMinor();\r\n      expect(incremented.value).toBe('1.3.0');\r\n      expect(incremented.major).toBe(1);\r\n      expect(incremented.minor).toBe(3);\r\n      expect(incremented.patch).toBe(0);\r\n    });\r\n\r\n    it('should increment patch version', () => {\r\n      const incremented = baseVersion.incrementPatch();\r\n      expect(incremented.value).toBe('1.2.4');\r\n      expect(incremented.major).toBe(1);\r\n      expect(incremented.minor).toBe(2);\r\n      expect(incremented.patch).toBe(4);\r\n    });\r\n\r\n    it('should increment with prerelease', () => {\r\n      const incremented = baseVersion.incrementMajor('alpha.1');\r\n      expect(incremented.value).toBe('2.0.0-alpha.1');\r\n      expect(incremented.prerelease).toBe('alpha.1');\r\n    });\r\n\r\n    it('should increment with build metadata', () => {\r\n      const incremented = baseVersion.incrementMinor(undefined, 'build.123');\r\n      expect(incremented.value).toBe('1.3.0+build.123');\r\n      expect(incremented.buildMetadata).toBe('build.123');\r\n    });\r\n\r\n    it('should increment with prerelease and build metadata', () => {\r\n      const incremented = baseVersion.incrementPatch('beta.2', 'build.456');\r\n      expect(incremented.value).toBe('1.2.4-beta.2+build.456');\r\n      expect(incremented.prerelease).toBe('beta.2');\r\n      expect(incremented.buildMetadata).toBe('build.456');\r\n    });\r\n  });\r\n\r\n  describe('version modification operations', () => {\r\n    let baseVersion: Version;\r\n\r\n    beforeEach(() => {\r\n      baseVersion = new Version('1.2.3');\r\n    });\r\n\r\n    it('should add prerelease', () => {\r\n      const withPrerelease = baseVersion.withPrerelease('alpha.1');\r\n      expect(withPrerelease.value).toBe('1.2.3-alpha.1');\r\n      expect(withPrerelease.prerelease).toBe('alpha.1');\r\n    });\r\n\r\n    it('should throw error for empty prerelease', () => {\r\n      expect(() => baseVersion.withPrerelease('')).toThrow('Prerelease identifier cannot be empty');\r\n      expect(() => baseVersion.withPrerelease('   ')).toThrow('Prerelease identifier cannot be empty');\r\n    });\r\n\r\n    it('should add build metadata', () => {\r\n      const withBuild = baseVersion.withBuildMetadata('build.123');\r\n      expect(withBuild.value).toBe('1.2.3+build.123');\r\n      expect(withBuild.buildMetadata).toBe('build.123');\r\n    });\r\n\r\n    it('should throw error for empty build metadata', () => {\r\n      expect(() => baseVersion.withBuildMetadata('')).toThrow('Build metadata cannot be empty');\r\n      expect(() => baseVersion.withBuildMetadata('   ')).toThrow('Build metadata cannot be empty');\r\n    });\r\n\r\n    it('should convert to stable version', () => {\r\n      const prerelease = new Version('1.2.3-alpha.1+build.123');\r\n      const stable = prerelease.toStable();\r\n      expect(stable.value).toBe('1.2.3');\r\n      expect(stable.prerelease).toBeNull();\r\n      expect(stable.buildMetadata).toBeNull();\r\n    });\r\n  });\r\n\r\n  describe('version comparison', () => {\r\n    it('should compare major versions', () => {\r\n      const v1 = new Version('1.0.0');\r\n      const v2 = new Version('2.0.0');\r\n\r\n      expect(v1.compareTo(v2)).toBe(-1);\r\n      expect(v2.compareTo(v1)).toBe(1);\r\n      expect(v1.compareTo(v1)).toBe(0);\r\n    });\r\n\r\n    it('should compare minor versions', () => {\r\n      const v1 = new Version('1.1.0');\r\n      const v2 = new Version('1.2.0');\r\n\r\n      expect(v1.compareTo(v2)).toBe(-1);\r\n      expect(v2.compareTo(v1)).toBe(1);\r\n    });\r\n\r\n    it('should compare patch versions', () => {\r\n      const v1 = new Version('1.0.1');\r\n      const v2 = new Version('1.0.2');\r\n\r\n      expect(v1.compareTo(v2)).toBe(-1);\r\n      expect(v2.compareTo(v1)).toBe(1);\r\n    });\r\n\r\n    it('should compare prerelease versions', () => {\r\n      const stable = new Version('1.0.0');\r\n      const prerelease = new Version('1.0.0-alpha');\r\n\r\n      expect(prerelease.compareTo(stable)).toBe(-1);\r\n      expect(stable.compareTo(prerelease)).toBe(1);\r\n    });\r\n\r\n    it('should compare different prerelease versions', () => {\r\n      const alpha = new Version('1.0.0-alpha');\r\n      const beta = new Version('1.0.0-beta');\r\n\r\n      expect(alpha.compareTo(beta)).toBe(-1);\r\n      expect(beta.compareTo(alpha)).toBe(1);\r\n    });\r\n\r\n    it('should compare numeric prerelease identifiers', () => {\r\n      const v1 = new Version('1.0.0-alpha.1');\r\n      const v2 = new Version('1.0.0-alpha.2');\r\n\r\n      expect(v1.compareTo(v2)).toBe(-1);\r\n      expect(v2.compareTo(v1)).toBe(1);\r\n    });\r\n\r\n    it('should compare mixed prerelease identifiers', () => {\r\n      const numeric = new Version('1.0.0-alpha.1');\r\n      const text = new Version('1.0.0-alpha.beta');\r\n\r\n      expect(numeric.compareTo(text)).toBe(-1);\r\n      expect(text.compareTo(numeric)).toBe(1);\r\n    });\r\n  });\r\n\r\n  describe('version comparison methods', () => {\r\n    let v1: Version;\r\n    let v2: Version;\r\n    let v3: Version;\r\n\r\n    beforeEach(() => {\r\n      v1 = new Version('1.0.0');\r\n      v2 = new Version('2.0.0');\r\n      v3 = new Version('1.0.0');\r\n    });\r\n\r\n    it('should check if less than', () => {\r\n      expect(v1.isLessThan(v2)).toBe(true);\r\n      expect(v2.isLessThan(v1)).toBe(false);\r\n      expect(v1.isLessThan(v3)).toBe(false);\r\n    });\r\n\r\n    it('should check if less than or equal', () => {\r\n      expect(v1.isLessThanOrEqual(v2)).toBe(true);\r\n      expect(v1.isLessThanOrEqual(v3)).toBe(true);\r\n      expect(v2.isLessThanOrEqual(v1)).toBe(false);\r\n    });\r\n\r\n    it('should check if greater than', () => {\r\n      expect(v2.isGreaterThan(v1)).toBe(true);\r\n      expect(v1.isGreaterThan(v2)).toBe(false);\r\n      expect(v1.isGreaterThan(v3)).toBe(false);\r\n    });\r\n\r\n    it('should check if greater than or equal', () => {\r\n      expect(v2.isGreaterThanOrEqual(v1)).toBe(true);\r\n      expect(v1.isGreaterThanOrEqual(v3)).toBe(true);\r\n      expect(v1.isGreaterThanOrEqual(v2)).toBe(false);\r\n    });\r\n\r\n    it('should check compatibility (same major version)', () => {\r\n      const v1_1 = new Version('1.1.0');\r\n      const v1_2 = new Version('1.2.0');\r\n      const v2_0 = new Version('2.0.0');\r\n      const v0_1 = new Version('0.1.0');\r\n\r\n      expect(v1_1.isCompatibleWith(v1_2)).toBe(true);\r\n      expect(v1_1.isCompatibleWith(v2_0)).toBe(false);\r\n      expect(v1_1.isCompatibleWith(v0_1)).toBe(false); // 0.x.x versions are not compatible\r\n    });\r\n  });\r\n\r\n  describe('version range satisfaction', () => {\r\n    let version: Version;\r\n\r\n    beforeEach(() => {\r\n      version = new Version('1.2.3');\r\n    });\r\n\r\n    it('should satisfy exact match', () => {\r\n      expect(version.satisfies('1.2.3')).toBe(true);\r\n      expect(version.satisfies('1.2.4')).toBe(false);\r\n    });\r\n\r\n    it('should satisfy caret range (compatible within major)', () => {\r\n      expect(version.satisfies('^1.2.0')).toBe(true);\r\n      expect(version.satisfies('^1.0.0')).toBe(true);\r\n      expect(version.satisfies('^1.3.0')).toBe(false);\r\n      expect(version.satisfies('^2.0.0')).toBe(false);\r\n    });\r\n\r\n    it('should satisfy tilde range (compatible within minor)', () => {\r\n      expect(version.satisfies('~1.2.0')).toBe(true);\r\n      expect(version.satisfies('~1.2.3')).toBe(true);\r\n      expect(version.satisfies('~1.1.0')).toBe(false);\r\n      expect(version.satisfies('~1.3.0')).toBe(false);\r\n    });\r\n\r\n    it('should satisfy comparison operators', () => {\r\n      expect(version.satisfies('>=1.2.0')).toBe(true);\r\n      expect(version.satisfies('>=1.2.3')).toBe(true);\r\n      expect(version.satisfies('>=1.2.4')).toBe(false);\r\n      \r\n      expect(version.satisfies('<=1.2.3')).toBe(true);\r\n      expect(version.satisfies('<=1.2.4')).toBe(true);\r\n      expect(version.satisfies('<=1.2.2')).toBe(false);\r\n      \r\n      expect(version.satisfies('>1.2.2')).toBe(true);\r\n      expect(version.satisfies('>1.2.3')).toBe(false);\r\n      \r\n      expect(version.satisfies('<1.2.4')).toBe(true);\r\n      expect(version.satisfies('<1.2.3')).toBe(false);\r\n      \r\n      expect(version.satisfies('=1.2.3')).toBe(true);\r\n      expect(version.satisfies('=1.2.4')).toBe(false);\r\n    });\r\n\r\n    it('should not satisfy invalid ranges', () => {\r\n      expect(version.satisfies('invalid-range')).toBe(false);\r\n      expect(version.satisfies('^invalid')).toBe(false);\r\n      expect(version.satisfies('>=invalid')).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('equality and comparison', () => {\r\n    it('should be equal to itself', () => {\r\n      const version = new Version('1.2.3');\r\n      expect(version.equals(version)).toBe(true);\r\n    });\r\n\r\n    it('should be equal to version with same value', () => {\r\n      const version1 = new Version('1.2.3');\r\n      const version2 = new Version('1.2.3');\r\n      expect(version1.equals(version2)).toBe(true);\r\n    });\r\n\r\n    it('should not be equal to version with different value', () => {\r\n      const version1 = new Version('1.2.3');\r\n      const version2 = new Version('1.2.4');\r\n      expect(version1.equals(version2)).toBe(false);\r\n    });\r\n\r\n    it('should ignore build metadata in equality', () => {\r\n      const version1 = new Version('1.2.3+build.1');\r\n      const version2 = new Version('1.2.3+build.2');\r\n      expect(version1.equals(version2)).toBe(true);\r\n    });\r\n\r\n    it('should consider prerelease in equality', () => {\r\n      const stable = new Version('1.2.3');\r\n      const prerelease = new Version('1.2.3-alpha');\r\n      expect(stable.equals(prerelease)).toBe(false);\r\n    });\r\n\r\n    it('should not be equal to null or undefined', () => {\r\n      const version = new Version('1.2.3');\r\n      expect(version.equals(null as any)).toBe(false);\r\n      expect(version.equals(undefined as any)).toBe(false);\r\n    });\r\n\r\n    it('should not be equal to non-Version object', () => {\r\n      const version = new Version('1.2.3');\r\n      expect(version.equals({} as any)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('serialization', () => {\r\n    it('should convert to string', () => {\r\n      const version = new Version('1.2.3-alpha.1+build.123');\r\n      expect(version.toString()).toBe('1.2.3-alpha.1+build.123');\r\n    });\r\n\r\n    it('should convert to JSON', () => {\r\n      const version = new Version('1.2.3-alpha.1+build.123');\r\n      const json = version.toJSON();\r\n\r\n      expect(json.value).toBe('1.2.3-alpha.1+build.123');\r\n      expect(json.major).toBe(1);\r\n      expect(json.minor).toBe(2);\r\n      expect(json.patch).toBe(3);\r\n      expect(json.prerelease).toBe('alpha.1');\r\n      expect(json.buildMetadata).toBe('build.123');\r\n      expect(json.coreVersion).toBe('1.2.3');\r\n      expect(json.isPrerelease).toBe(true);\r\n      expect(json.isStable).toBe(false);\r\n      expect(json.type).toBe('Version');\r\n    });\r\n\r\n    it('should create from JSON', () => {\r\n      const json = { value: '1.2.3-alpha.1+build.123' };\r\n      const version = Version.fromJSON(json);\r\n\r\n      expect(version.value).toBe('1.2.3-alpha.1+build.123');\r\n      expect(version.major).toBe(1);\r\n      expect(version.minor).toBe(2);\r\n      expect(version.patch).toBe(3);\r\n    });\r\n  });\r\n\r\n  describe('immutability', () => {\r\n    it('should be immutable after creation', () => {\r\n      const version = new Version('1.2.3');\r\n      const originalValue = version.value;\r\n\r\n      // Attempt to modify (should not work due to readonly)\r\n      expect(() => {\r\n        (version as any)._value = 'modified';\r\n      }).not.toThrow(); // TypeScript prevents this, but runtime doesn't throw\r\n\r\n      // Value should remain unchanged\r\n      expect(version.value).toBe(originalValue);\r\n    });\r\n\r\n    it('should be frozen', () => {\r\n      const version = new Version('1.2.3');\r\n      expect(Object.isFrozen(version)).toBe(true);\r\n    });\r\n\r\n    it('should return new instances for modification operations', () => {\r\n      const original = new Version('1.2.3');\r\n      const incremented = original.incrementMajor();\r\n\r\n      expect(incremented).not.toBe(original);\r\n      expect(original.value).toBe('1.2.3');\r\n      expect(incremented.value).toBe('2.0.0');\r\n    });\r\n  });\r\n\r\n  describe('edge cases', () => {\r\n    it('should handle large version numbers', () => {\r\n      const version = new Version('999999.999999.999999');\r\n      expect(version.major).toBe(999999);\r\n      expect(version.minor).toBe(999999);\r\n      expect(version.patch).toBe(999999);\r\n    });\r\n\r\n    it('should handle complex prerelease identifiers', () => {\r\n      const version = new Version('1.0.0-alpha.1.2.3.beta.4.5.6');\r\n      expect(version.prerelease).toBe('alpha.1.2.3.beta.4.5.6');\r\n    });\r\n\r\n    it('should handle complex build metadata', () => {\r\n      const version = new Version('1.0.0+build.1.2.3.sha.abc123');\r\n      expect(version.buildMetadata).toBe('build.1.2.3.sha.abc123');\r\n    });\r\n\r\n    it('should compare prerelease versions with different lengths', () => {\r\n      const v1 = new Version('1.0.0-alpha');\r\n      const v2 = new Version('1.0.0-alpha.1');\r\n\r\n      expect(v1.compareTo(v2)).toBe(-1);\r\n      expect(v2.compareTo(v1)).toBe(1);\r\n    });\r\n\r\n    it('should handle zero versions', () => {\r\n      const version = new Version('0.0.0');\r\n      expect(version.isInitial()).toBe(true);\r\n      expect(version.isStable()).toBe(true);\r\n    });\r\n  });\r\n});"], "version": 3}