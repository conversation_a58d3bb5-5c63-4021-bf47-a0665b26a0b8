{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\domain\\entities\\asset.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCAQiB;AACjB,iEAAuD;AACvD,6DAAkD;AAElD;;;GAGG;AASI,IAAM,KAAK,GAAX,MAAM,KAAK;IA0ShB;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,WAAW,KAAK,UAAU,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,WAAW;YAAE,OAAO,IAAI,CAAC;QAEzD,MAAM,YAAY,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACjD,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC;QAEpE,OAAO,IAAI,IAAI,EAAE,IAAI,YAAY,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,IAAI,CAAC,IAAI,CAAC,eAAe;YAAE,OAAO,CAAC,CAAC;QACpC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;QAC9D,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,IAAI,iBAAiB;QACnB,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,OAAO,IAAI,CAAC;QACpC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;QAC3D,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,IAAI,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,yBAAyB,CAAC,MAAoC;QAC5D,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC;QAClC,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC9B,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;YACnB,OAAO;QACT,CAAC;QAED,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAEjE,kCAAkC;QAClC,IAAI,SAAS,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QAEvE,6DAA6D;QAC7D,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;QAEzC,+BAA+B;QAC/B,MAAM,sBAAsB,GAAG;YAC7B,GAAG,EAAE,GAAG;YACR,MAAM,EAAE,GAAG;YACX,IAAI,EAAE,GAAG;YACT,QAAQ,EAAE,GAAG;SACd,CAAC;QAEF,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,sBAAsB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,CAAC,CAAC;QACpF,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,qBAAqB;IAC9E,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,QAMX;QACC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC5B,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAC9B,CAAC;QAED,mCAAmC;QACnC,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CACpD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,OAAO,KAAK,QAAQ,CAAC,OAAO,CAChE,CAAC;QAEF,IAAI,aAAa,IAAI,CAAC,EAAE,CAAC;YACvB,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC;QACnD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,OAMV;QACC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACrB,CAAC;QAED,kCAAkC;QAClC,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAC3C,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC,QAAQ,KAAK,OAAO,CAAC,QAAQ,CAC3F,CAAC;QAEF,IAAI,aAAa,IAAI,CAAC,EAAE,CAAC;YACvB,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,GAAG,OAAO,CAAC;QACzC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,GAAW;QAChB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,GAAW;QACnB,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,GAAW;QAChB,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IACrD,CAAC;CACF,CAAA;AAndY,sBAAK;AAEhB;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;iCACpB;AAMX;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;mCACX;AAsBb;IAjBC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE;YACJ,QAAQ;YACR,aAAa;YACb,gBAAgB;YAChB,eAAe;YACf,YAAY;YACZ,WAAW;YACX,iBAAiB;YACjB,gBAAgB;YAChB,UAAU;YACV,iBAAiB;YACjB,KAAK;YACL,SAAS;SACV;KACF,CAAC;;mCACW;AAMb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;0CACpB;AAUrB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,gBAAgB,EAAE,aAAa,EAAE,SAAS,CAAC;QACxE,OAAO,EAAE,QAAQ;KAClB,CAAC;;qCAC2E;AAU7E;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC;QAC3C,OAAO,EAAE,QAAQ;KAClB,CAAC;;0CACkD;AAUpD;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,aAAa,EAAE,SAAS,EAAE,SAAS,CAAC;QACpE,OAAO,EAAE,YAAY;KACtB,CAAC;;0CAC4E;AAM9E;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;wCAC5B;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;0CACzC;AAMvB;IADC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uCACT;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;yCAC5B;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;8CAOlE;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;0CAO9D;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;2CAQ/D;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAClD,KAAK,oBAAL,KAAK;gDAMtB;AAMH;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAC/B,KAAK,oBAAL,KAAK;uCAMb;AAMH;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;oCAMxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uCASxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,yBAAyB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDACzC;AAMlC;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAClD,KAAK,oBAAL,KAAK;+CAKrB;AAMH;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mCAC1B;AAMhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;8CAC5B;AAMzB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACrE,IAAI,oBAAJ,IAAI;8CAAC;AAMvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACrE,IAAI,oBAAJ,IAAI;uCAAC;AAMhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACtE,IAAI,oBAAJ,IAAI;2CAAC;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,oBAAoB;;;4CACjE;AAMtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;0CAC3B;AAMrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;yCAM7D;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;wCAClE;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,sBAAsB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kDAQtE;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAC/B,MAAM,oBAAN,MAAM;uCAAc;AAM/B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;wCAC3B;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;wCAC1C;AAGnB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;wCAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;wCAAC;AAIhB;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,oCAAa,EAAE,aAAa,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC;;8CACpC;AAGjC;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,+BAAU,EAAE,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC;;0CAClC;gBAxSf,KAAK;IARjB,IAAA,gBAAM,EAAC,QAAQ,CAAC;IAChB,IAAA,eAAK,EAAC,CAAC,MAAM,CAAC,CAAC;IACf,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,aAAa,CAAC,CAAC;IACtB,IAAA,eAAK,EAAC,CAAC,aAAa,CAAC,CAAC;IACtB,IAAA,eAAK,EAAC,CAAC,WAAW,CAAC,CAAC;IACpB,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,cAAc,CAAC,CAAC;GACX,KAAK,CAmdjB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\domain\\entities\\asset.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  OneToMany,\r\n} from 'typeorm';\r\nimport { Vulnerability } from './vulnerability.entity';\r\nimport { ScanResult } from './scan-result.entity';\r\n\r\n/**\r\n * Asset entity\r\n * Represents IT assets that can be scanned for vulnerabilities\r\n */\r\n@Entity('assets')\r\n@Index(['type'])\r\n@Index(['status'])\r\n@Index(['criticality'])\r\n@Index(['environment'])\r\n@Index(['ipAddress'])\r\n@Index(['hostname'])\r\n@Index(['lastScanDate'])\r\nexport class Asset {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * Asset name/identifier\r\n   */\r\n  @Column({ length: 255 })\r\n  name: string;\r\n\r\n  /**\r\n   * Asset type\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: [\r\n      'server',\r\n      'workstation',\r\n      'network_device',\r\n      'mobile_device',\r\n      'iot_device',\r\n      'container',\r\n      'virtual_machine',\r\n      'cloud_instance',\r\n      'database',\r\n      'web_application',\r\n      'api',\r\n      'service',\r\n    ],\r\n  })\r\n  type: string;\r\n\r\n  /**\r\n   * Asset description\r\n   */\r\n  @Column({ type: 'text', nullable: true })\r\n  description?: string;\r\n\r\n  /**\r\n   * Asset status\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['active', 'inactive', 'decommissioned', 'maintenance', 'unknown'],\r\n    default: 'active',\r\n  })\r\n  status: 'active' | 'inactive' | 'decommissioned' | 'maintenance' | 'unknown';\r\n\r\n  /**\r\n   * Business criticality level\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['low', 'medium', 'high', 'critical'],\r\n    default: 'medium',\r\n  })\r\n  criticality: 'low' | 'medium' | 'high' | 'critical';\r\n\r\n  /**\r\n   * Environment classification\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['production', 'staging', 'development', 'testing', 'sandbox'],\r\n    default: 'production',\r\n  })\r\n  environment: 'production' | 'staging' | 'development' | 'testing' | 'sandbox';\r\n\r\n  /**\r\n   * Primary IP address\r\n   */\r\n  @Column({ name: 'ip_address', nullable: true })\r\n  ipAddress?: string;\r\n\r\n  /**\r\n   * Additional IP addresses\r\n   */\r\n  @Column({ name: 'ip_addresses', type: 'jsonb', nullable: true })\r\n  ipAddresses?: string[];\r\n\r\n  /**\r\n   * Hostname/FQDN\r\n   */\r\n  @Column({ nullable: true })\r\n  hostname?: string;\r\n\r\n  /**\r\n   * MAC address\r\n   */\r\n  @Column({ name: 'mac_address', nullable: true })\r\n  macAddress?: string;\r\n\r\n  /**\r\n   * Operating system information\r\n   */\r\n  @Column({ name: 'operating_system', type: 'jsonb', nullable: true })\r\n  operatingSystem?: {\r\n    name?: string;\r\n    version?: string;\r\n    architecture?: string;\r\n    kernel?: string;\r\n    distribution?: string;\r\n  };\r\n\r\n  /**\r\n   * Network information\r\n   */\r\n  @Column({ name: 'network_info', type: 'jsonb', nullable: true })\r\n  networkInfo?: {\r\n    subnet?: string;\r\n    vlan?: string;\r\n    gateway?: string;\r\n    dns?: string[];\r\n    domain?: string;\r\n  };\r\n\r\n  /**\r\n   * Hardware information\r\n   */\r\n  @Column({ name: 'hardware_info', type: 'jsonb', nullable: true })\r\n  hardwareInfo?: {\r\n    manufacturer?: string;\r\n    model?: string;\r\n    serialNumber?: string;\r\n    cpu?: string;\r\n    memory?: string;\r\n    storage?: string;\r\n  };\r\n\r\n  /**\r\n   * Software inventory\r\n   */\r\n  @Column({ name: 'software_inventory', type: 'jsonb', nullable: true })\r\n  softwareInventory?: Array<{\r\n    name: string;\r\n    version?: string;\r\n    vendor?: string;\r\n    installDate?: string;\r\n    license?: string;\r\n  }>;\r\n\r\n  /**\r\n   * Services running on the asset\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  services?: Array<{\r\n    name: string;\r\n    port: number;\r\n    protocol: string;\r\n    version?: string;\r\n    state: string;\r\n  }>;\r\n\r\n  /**\r\n   * Asset owner information\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  owner?: {\r\n    name?: string;\r\n    email?: string;\r\n    department?: string;\r\n    businessUnit?: string;\r\n  };\r\n\r\n  /**\r\n   * Location information\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  location?: {\r\n    building?: string;\r\n    floor?: string;\r\n    room?: string;\r\n    rack?: string;\r\n    datacenter?: string;\r\n    region?: string;\r\n    country?: string;\r\n  };\r\n\r\n  /**\r\n   * Compliance requirements\r\n   */\r\n  @Column({ name: 'compliance_requirements', type: 'jsonb', nullable: true })\r\n  complianceRequirements?: string[];\r\n\r\n  /**\r\n   * Security controls applied\r\n   */\r\n  @Column({ name: 'security_controls', type: 'jsonb', nullable: true })\r\n  securityControls?: Array<{\r\n    type: string;\r\n    name: string;\r\n    status: string;\r\n    lastVerified?: string;\r\n  }>;\r\n\r\n  /**\r\n   * Asset tags for categorization\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  tags?: string[];\r\n\r\n  /**\r\n   * Discovery method\r\n   */\r\n  @Column({ name: 'discovery_method', nullable: true })\r\n  discoveryMethod?: string;\r\n\r\n  /**\r\n   * First discovered date\r\n   */\r\n  @Column({ name: 'first_discovered', type: 'timestamp with time zone', nullable: true })\r\n  firstDiscovered?: Date;\r\n\r\n  /**\r\n   * Last seen date\r\n   */\r\n  @Column({ name: 'last_seen', type: 'timestamp with time zone', nullable: true })\r\n  lastSeen?: Date;\r\n\r\n  /**\r\n   * Last scan date\r\n   */\r\n  @Column({ name: 'last_scan_date', type: 'timestamp with time zone', nullable: true })\r\n  lastScanDate?: Date;\r\n\r\n  /**\r\n   * Scan frequency in hours\r\n   */\r\n  @Column({ name: 'scan_frequency', type: 'integer', default: 168 }) // Weekly by default\r\n  scanFrequency: number;\r\n\r\n  /**\r\n   * Whether asset is scannable\r\n   */\r\n  @Column({ name: 'is_scannable', default: true })\r\n  isScannable: boolean;\r\n\r\n  /**\r\n   * Scan configuration\r\n   */\r\n  @Column({ name: 'scan_config', type: 'jsonb', nullable: true })\r\n  scanConfig?: {\r\n    scanTypes?: string[];\r\n    credentials?: string;\r\n    excludedPorts?: number[];\r\n    customSettings?: Record<string, any>;\r\n  };\r\n\r\n  /**\r\n   * Risk score (0-10)\r\n   */\r\n  @Column({ name: 'risk_score', type: 'decimal', precision: 3, scale: 1, default: 0 })\r\n  riskScore: number;\r\n\r\n  /**\r\n   * Vulnerability counts\r\n   */\r\n  @Column({ name: 'vulnerability_counts', type: 'jsonb', nullable: true })\r\n  vulnerabilityCounts?: {\r\n    critical: number;\r\n    high: number;\r\n    medium: number;\r\n    low: number;\r\n    info: number;\r\n    total: number;\r\n  };\r\n\r\n  /**\r\n   * Additional metadata\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  metadata?: Record<string, any>;\r\n\r\n  /**\r\n   * User who created the asset\r\n   */\r\n  @Column({ name: 'created_by', type: 'uuid' })\r\n  createdBy: string;\r\n\r\n  /**\r\n   * User who last updated the asset\r\n   */\r\n  @Column({ name: 'updated_by', type: 'uuid', nullable: true })\r\n  updatedBy?: string;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  // Relationships\r\n  @OneToMany(() => Vulnerability, vulnerability => vulnerability.asset)\r\n  vulnerabilities: Vulnerability[];\r\n\r\n  @OneToMany(() => ScanResult, scanResult => scanResult.asset)\r\n  scanResults: ScanResult[];\r\n\r\n  /**\r\n   * Check if asset is active\r\n   */\r\n  get isActive(): boolean {\r\n    return this.status === 'active';\r\n  }\r\n\r\n  /**\r\n   * Check if asset is critical\r\n   */\r\n  get isCritical(): boolean {\r\n    return this.criticality === 'critical';\r\n  }\r\n\r\n  /**\r\n   * Check if asset is due for scan\r\n   */\r\n  get isDueForScan(): boolean {\r\n    if (!this.lastScanDate || !this.isScannable) return true;\r\n    \r\n    const nextScanDate = new Date(this.lastScanDate);\r\n    nextScanDate.setHours(nextScanDate.getHours() + this.scanFrequency);\r\n    \r\n    return new Date() >= nextScanDate;\r\n  }\r\n\r\n  /**\r\n   * Get asset age in days\r\n   */\r\n  get ageInDays(): number {\r\n    if (!this.firstDiscovered) return 0;\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - this.firstDiscovered.getTime();\r\n    return Math.floor(diffMs / (1000 * 60 * 60 * 24));\r\n  }\r\n\r\n  /**\r\n   * Get days since last scan\r\n   */\r\n  get daysSinceLastScan(): number | null {\r\n    if (!this.lastScanDate) return null;\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - this.lastScanDate.getTime();\r\n    return Math.floor(diffMs / (1000 * 60 * 60 * 24));\r\n  }\r\n\r\n  /**\r\n   * Update last seen timestamp\r\n   */\r\n  updateLastSeen(): void {\r\n    this.lastSeen = new Date();\r\n  }\r\n\r\n  /**\r\n   * Update vulnerability counts\r\n   */\r\n  updateVulnerabilityCounts(counts: Asset['vulnerabilityCounts']): void {\r\n    this.vulnerabilityCounts = counts;\r\n    this.calculateRiskScore();\r\n  }\r\n\r\n  /**\r\n   * Calculate risk score based on vulnerabilities and criticality\r\n   */\r\n  calculateRiskScore(): void {\r\n    if (!this.vulnerabilityCounts) {\r\n      this.riskScore = 0;\r\n      return;\r\n    }\r\n\r\n    const { critical, high, medium, low } = this.vulnerabilityCounts;\r\n    \r\n    // Base score from vulnerabilities\r\n    let baseScore = (critical * 4) + (high * 3) + (medium * 2) + (low * 1);\r\n    \r\n    // Normalize to 0-10 scale (assuming max 100 vulnerabilities)\r\n    baseScore = Math.min(baseScore / 10, 10);\r\n    \r\n    // Apply criticality multiplier\r\n    const criticalityMultipliers = {\r\n      low: 0.5,\r\n      medium: 1.0,\r\n      high: 1.5,\r\n      critical: 2.0,\r\n    };\r\n    \r\n    this.riskScore = Math.min(baseScore * criticalityMultipliers[this.criticality], 10);\r\n    this.riskScore = Math.round(this.riskScore * 10) / 10; // Round to 1 decimal\r\n  }\r\n\r\n  /**\r\n   * Add software to inventory\r\n   */\r\n  addSoftware(software: {\r\n    name: string;\r\n    version?: string;\r\n    vendor?: string;\r\n    installDate?: string;\r\n    license?: string;\r\n  }): void {\r\n    if (!this.softwareInventory) {\r\n      this.softwareInventory = [];\r\n    }\r\n    \r\n    // Check if software already exists\r\n    const existingIndex = this.softwareInventory.findIndex(\r\n      s => s.name === software.name && s.version === software.version\r\n    );\r\n    \r\n    if (existingIndex >= 0) {\r\n      this.softwareInventory[existingIndex] = software;\r\n    } else {\r\n      this.softwareInventory.push(software);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Add service to asset\r\n   */\r\n  addService(service: {\r\n    name: string;\r\n    port: number;\r\n    protocol: string;\r\n    version?: string;\r\n    state: string;\r\n  }): void {\r\n    if (!this.services) {\r\n      this.services = [];\r\n    }\r\n    \r\n    // Check if service already exists\r\n    const existingIndex = this.services.findIndex(\r\n      s => s.name === service.name && s.port === service.port && s.protocol === service.protocol\r\n    );\r\n    \r\n    if (existingIndex >= 0) {\r\n      this.services[existingIndex] = service;\r\n    } else {\r\n      this.services.push(service);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Add tag to asset\r\n   */\r\n  addTag(tag: string): void {\r\n    if (!this.tags) {\r\n      this.tags = [];\r\n    }\r\n    if (!this.tags.includes(tag)) {\r\n      this.tags.push(tag);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove tag from asset\r\n   */\r\n  removeTag(tag: string): void {\r\n    if (this.tags) {\r\n      this.tags = this.tags.filter(t => t !== tag);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if asset has specific tag\r\n   */\r\n  hasTag(tag: string): boolean {\r\n    return this.tags ? this.tags.includes(tag) : false;\r\n  }\r\n}\r\n"], "version": 3}