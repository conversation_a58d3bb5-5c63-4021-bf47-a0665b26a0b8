{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\compliance-audit\\application\\services\\compliance-monitoring.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,6CAAqD;AAErD,mFAA8E;AAC9E,mGAAwF;AACxF,qGAA0F;AAC1F,6FAAkF;AAClF,2FAAgF;AAChF,6EAAkE;AAClE,sFAAkF;AAClF,0FAAsF;AACtF,uGAAmG;AACnG,6EAAwE;AACxE,+EAA0E;AAE1E,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;IAC3C,IAAI,OAAoC,CAAC;IACzC,IAAI,mBAAoD,CAAC;IACzD,IAAI,oBAAsD,CAAC;IAC3D,IAAI,gBAA8C,CAAC;IACnD,IAAI,mBAAgD,CAAC;IACrD,IAAI,kBAAwC,CAAC;IAC7C,IAAI,aAA4B,CAAC;IACjC,IAAI,YAA0B,CAAC;IAC/B,IAAI,mBAAwC,CAAC;IAC7C,IAAI,wBAAkD,CAAC;IACvD,IAAI,yBAAoD,CAAC;IAEzD,MAAM,uBAAuB,GAAG;QAC9B,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;KAC9B,CAAC;IAEF,MAAM,wBAAwB,GAAG;QAC/B,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;KAC9B,CAAC;IAEF,MAAM,oBAAoB,GAAG;QAC3B,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;KACjB,CAAC;IAEF,MAAM,uBAAuB,GAAG;QAC9B,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;KAC9B,CAAC;IAEF,MAAM,sBAAsB,GAAG;QAC7B,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;KACjB,CAAC;IAEF,MAAM,iBAAiB,GAAG;QACxB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;QACd,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;KACjB,CAAC;IAEF,MAAM,gBAAgB,GAAG;QACvB,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;KACzB,CAAC;IAEF,MAAM,uBAAuB,GAAG;QAC9B,0BAA0B,EAAE,IAAI,CAAC,EAAE,EAAE;QACrC,iCAAiC,EAAE,IAAI,CAAC,EAAE,EAAE;QAC5C,+BAA+B,EAAE,IAAI,CAAC,EAAE,EAAE;KAC3C,CAAC;IAEF,MAAM,4BAA4B,GAAG;QACnC,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE;QAC1B,wBAAwB,EAAE,IAAI,CAAC,EAAE,EAAE;KACpC,CAAC;IAEF,MAAM,6BAA6B,GAAG;QACpC,qBAAqB,EAAE,IAAI,CAAC,EAAE,EAAE;QAChC,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC5B,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE;KAC3B,CAAC;IAEF,MAAM,gBAAgB,GAAG;QACvB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;QACjC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;QACpC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;QACnC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;QAChC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;QAChC,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE;QAC1B,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;QACnB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;QAClC,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;QACrC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;QACnC,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;QACrB,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;KACrB,CAAC;IAEF,MAAM,aAAa,GAAiC;QAClD,EAAE,EAAE,eAAe;QACnB,IAAI,EAAE,eAAe;QACrB,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE,IAAI;QACd,aAAa,EAAE;YACb,OAAO,EAAE;gBACP;oBACE,EAAE,EAAE,UAAU;oBACd,IAAI,EAAE,UAAU;oBAChB,WAAW,EAAE,mBAAmB;oBAChC,QAAQ,EAAE;wBACR;4BACE,EAAE,EAAE,OAAO;4BACX,IAAI,EAAE,yBAAyB;4BAC/B,WAAW,EAAE,iCAAiC;4BAC9C,IAAI,EAAE,YAAY;4BAClB,QAAQ,EAAE,MAAM;4BAChB,SAAS,EAAE,YAAY;4BACvB,WAAW,EAAE,IAAI;4BACjB,oBAAoB,EAAE,CAAC,aAAa,EAAE,eAAe,CAAC;yBACvD;qBACF;iBACF;aACF;SACF;QACD,aAAa,EAAE,CAAC;QAChB,mBAAmB,EAAE;YACnB;gBACE,EAAE,EAAE,OAAO;gBACX,IAAI,EAAE,yBAAyB;gBAC/B,QAAQ,EAAE,UAAU;gBACpB,UAAU,EAAE,UAAU;aACvB;SACF;QACD,qBAAqB,EAAE,IAAI,CAAC,EAAE,EAAE;QAChC,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;QACxB,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;QACzB,qBAAqB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;QAC/E,wBAAwB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC;KACxD,CAAC;IAEF,MAAM,cAAc,GAAkC;QACpD,EAAE,EAAE,gBAAgB;QACpB,IAAI,EAAE,0BAA0B;QAChC,WAAW,EAAE,eAAe;QAC5B,cAAc,EAAE,gBAAgB;QAChC,MAAM,EAAE,SAAS;QACjB,cAAc,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QACtC,kBAAkB,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QAC1C,KAAK,EAAE;YACL,WAAW,EAAE,gCAAgC;SAC9C;QACD,SAAS,EAAE,KAAK;QAChB,WAAW,EAAE,KAAK;QAClB,oBAAoB,EAAE,CAAC;QACvB,qBAAqB,EAAE,CAAC;QACxB,iBAAiB,EAAE,CAAC;QACpB,SAAS,EAAE,EAAE;QACb,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;QACnB,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC;YACzC,EAAE,EAAE,gBAAgB;YACpB,IAAI,EAAE,0BAA0B;YAChC,MAAM,EAAE,SAAS;SAClB,CAAC;KACH,CAAC;IAEF,MAAM,aAAa,GAA6B;QAC9C,EAAE,EAAE,eAAe;QACnB,KAAK,EAAE,0BAA0B;QACjC,QAAQ,EAAE,MAAM;QAChB,MAAM,EAAE,MAAM;QACd,UAAU,EAAE,IAAI,IAAI,EAAE;QACtB,OAAO,EAAE;YACP,gBAAgB,EAAE,CAAC,OAAO,CAAC;YAC3B,UAAU,EAAE;gBACV,UAAU,EAAE,CAAC,eAAe,CAAC;gBAC7B,QAAQ,EAAE,CAAC,OAAO,CAAC;aACpB;SACF;QACD,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC;YACzC,EAAE,EAAE,eAAe;YACnB,KAAK,EAAE,0BAA0B;YACjC,QAAQ,EAAE,MAAM;YAChB,MAAM,EAAE,MAAM;SACf,CAAC;KACH,CAAC;IAEF,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,2DAA2B;gBAC3B;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,iDAAmB,CAAC;oBAChD,QAAQ,EAAE,uBAAuB;iBAClC;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,mDAAoB,CAAC;oBACjD,QAAQ,EAAE,wBAAwB;iBACnC;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,2CAAgB,CAAC;oBAC7C,QAAQ,EAAE,oBAAoB;iBAC/B;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,yCAAe,CAAC;oBAC5C,QAAQ,EAAE,uBAAuB;iBAClC;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,2BAAQ,CAAC;oBACrC,QAAQ,EAAE,sBAAsB;iBACjC;gBACD;oBACE,OAAO,EAAE,8BAAa;oBACtB,QAAQ,EAAE,iBAAiB;iBAC5B;gBACD;oBACE,OAAO,EAAE,4BAAY;oBACrB,QAAQ,EAAE,gBAAgB;iBAC3B;gBACD;oBACE,OAAO,EAAE,0CAAmB;oBAC5B,QAAQ,EAAE,uBAAuB;iBAClC;gBACD;oBACE,OAAO,EAAE,qDAAwB;oBACjC,QAAQ,EAAE,4BAA4B;iBACvC;gBACD;oBACE,OAAO,EAAE,uDAAyB;oBAClC,QAAQ,EAAE,6BAA6B;iBACxC;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAA8B,2DAA2B,CAAC,CAAC;QAC/E,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAkC,IAAA,4BAAkB,EAAC,iDAAmB,CAAC,CAAC,CAAC;QAC3G,oBAAoB,GAAG,MAAM,CAAC,GAAG,CAAmC,IAAA,4BAAkB,EAAC,mDAAoB,CAAC,CAAC,CAAC;QAC9G,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAA+B,IAAA,4BAAkB,EAAC,2CAAgB,CAAC,CAAC,CAAC;QAClG,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAA8B,IAAA,4BAAkB,EAAC,yCAAe,CAAC,CAAC,CAAC;QACnG,kBAAkB,GAAG,MAAM,CAAC,GAAG,CAAuB,IAAA,4BAAkB,EAAC,2BAAQ,CAAC,CAAC,CAAC;QACpF,aAAa,GAAG,MAAM,CAAC,GAAG,CAAgB,8BAAa,CAAC,CAAC;QACzD,YAAY,GAAG,MAAM,CAAC,GAAG,CAAe,4BAAY,CAAC,CAAC;QACtD,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAsB,0CAAmB,CAAC,CAAC;QAC3E,wBAAwB,GAAG,MAAM,CAAC,GAAG,CAA2B,qDAAwB,CAAC,CAAC;QAC1F,yBAAyB,GAAG,MAAM,CAAC,GAAG,CAA4B,uDAAyB,CAAC,CAAC;QAE7F,4BAA4B;QAC5B,uBAAuB,CAAC,kBAAkB,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;QAC7E,wBAAwB,CAAC,kBAAkB,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;QAC9E,uBAAuB,CAAC,kBAAkB,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;IAC/E,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,4BAA4B;YAC5B,uBAAuB,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB;YACvE,wBAAwB,CAAC,KAAK,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC,mBAAmB;YACzE,uBAAuB,CAAC,KAAK;iBAC1B,qBAAqB,CAAC,EAAE,CAAC,CAAC,iBAAiB;iBAC3C,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB;YAElD,uBAAuB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC;YAChE,wBAAwB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC;YAClE,uBAAuB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC;YAEhE,sBAAsB;YACtB,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,qBAAqB,CAAC,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YACpF,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,mCAAmC,CAAC,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YAEtF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,sBAAsB,EAAE,CAAC;YAEtD,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC;gBAC7B,gBAAgB,EAAE,CAAC;gBACnB,gBAAgB,EAAE,EAAE;gBACpB,cAAc,EAAE,EAAE;gBAClB,kBAAkB,EAAE,CAAC;gBACrB,sBAAsB,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC3C,CAAC,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,uBAAuB,CAAC,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,uBAAuB,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAE7E,MAAM,MAAM,CAAC,OAAO,CAAC,sBAAsB,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YACjF,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,oBAAoB,CAClD,yCAAyC,EACzC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,KAAK,EAAE,gBAAgB;aACxB,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,MAAM,cAAc,GAAG;YACrB,IAAI,EAAE,iBAAiB;YACvB,WAAW,EAAE,6BAA6B;YAC1C,cAAc,EAAE,gBAAyB;YACzC,cAAc,EAAE,IAAI,IAAI,EAAE;YAC1B,KAAK,EAAE;gBACL,WAAW,EAAE,YAAY;aAC1B;SACF,CAAC;QAEF,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,MAAM,GAAG,UAAU,CAAC;YAC1B,MAAM,eAAe,GAAG,EAAE,GAAG,cAAc,EAAE,EAAE,EAAE,gBAAgB,EAAE,CAAC;YAEpE,uBAAuB,CAAC,OAAO,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YACjE,wBAAwB,CAAC,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;YAChE,wBAAwB,CAAC,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAEjE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;YAEvF,MAAM,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,eAAe,EAAE;aAC/B,CAAC,CAAC;YACH,MAAM,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC;gBAC3D,GAAG,cAAc;gBACjB,WAAW,EAAE,eAAe;gBAC5B,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;YACH,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;YAC3E,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,MAAM,EACN,QAAQ,EACR,uBAAuB,EACvB,eAAe,CAAC,EAAE,EAClB,MAAM,CAAC,gBAAgB,CAAC;gBACtB,WAAW,EAAE,eAAe;gBAC5B,cAAc,EAAE,cAAc,CAAC,cAAc;gBAC7C,cAAc,EAAE,cAAc,CAAC,IAAI;aACpC,CAAC,CACH,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,MAAM,GAAG,UAAU,CAAC;YAC1B,uBAAuB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAExD,MAAM,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,cAAc,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;iBAC3E,OAAO,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,MAAM,YAAY,GAAG,gBAAgB,CAAC;YACtC,MAAM,MAAM,GAAG,UAAU,CAAC;YAC1B,MAAM,UAAU,GAAG,EAAE,GAAG,cAAc,EAAE,SAAS,EAAE,aAAa,EAAE,CAAC;YAEnE,wBAAwB,CAAC,OAAO,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAC/D,wBAAwB,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAE5D,qBAAqB;YACrB,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,8BAA8B,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAExF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YAEnE,MAAM,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBAC5D,KAAK,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE;gBAC3B,SAAS,EAAE,CAAC,WAAW,CAAC;aACzB,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;YACtD,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;YACvE,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,MAAM,EACN,OAAO,EACP,uBAAuB,EACvB,YAAY,EACZ,MAAM,CAAC,gBAAgB,CAAC;gBACtB,WAAW,EAAE,UAAU,CAAC,WAAW;gBACnC,cAAc,EAAE,UAAU,CAAC,cAAc;aAC1C,CAAC,CACH,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,YAAY,GAAG,cAAc,CAAC;YACpC,MAAM,MAAM,GAAG,UAAU,CAAC;YAE1B,wBAAwB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAEzD,MAAM,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;iBACxD,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,YAAY,GAAG,gBAAgB,CAAC;YACtC,MAAM,MAAM,GAAG,UAAU,CAAC;YAC1B,MAAM,OAAO,GAAG;gBACd,YAAY,EAAE,EAAE;gBAChB,aAAa,EAAE,WAAW;gBAC1B,aAAa,EAAE,EAAE;aAClB,CAAC;YACF,MAAM,UAAU,GAAG,EAAE,GAAG,cAAc,EAAE,SAAS,EAAE,aAAa,EAAE,CAAC;YAEnE,wBAAwB,CAAC,OAAO,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAC/D,wBAAwB,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAE5D,sBAAsB;YACtB,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,kCAAkC,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAE5F,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,kBAAkB,CAAC,YAAY,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YAE/E,MAAM,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBAC5D,KAAK,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE;gBAC3B,SAAS,EAAE,CAAC,WAAW,CAAC;aACzB,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAClE,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;YACvE,MAAM,CAAC,uBAAuB,CAAC,0BAA0B,CAAC,CAAC,oBAAoB,CAC7E,UAAU,EACV,sBAAsB,CACvB,CAAC;YACF,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACzD,MAAM,EACN,UAAU,EACV,uBAAuB,EACvB,YAAY,EACZ,MAAM,CAAC,gBAAgB,CAAC;gBACtB,WAAW,EAAE,UAAU,CAAC,WAAW;gBACnC,eAAe,EAAE,OAAO,CAAC,YAAY;gBACrC,gBAAgB,EAAE,OAAO,CAAC,aAAa;aACxC,CAAC,CACH,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,WAAW,GAAG,eAAe,CAAC;YAEpC,uBAAuB,CAAC,OAAO,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAEjE,sBAAsB;YACtB,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,qBAAqB,CAAC,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YACpF,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,wBAAwB,CAAC,CAAC,iBAAiB,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC;YACxF,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,sBAAsB,CAAC,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YACzE,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,mCAAmC,CAAC,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YACtF,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,6BAA6B,CAAC,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAEtF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,4BAA4B,CAAC,WAAW,CAAC,CAAC;YAEvE,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,WAAW,GAAG,cAAc,CAAC;YACnC,uBAAuB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAExD,MAAM,MAAM,CAAC,OAAO,CAAC,4BAA4B,CAAC,WAAW,CAAC,CAAC;iBAC5D,OAAO,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,IAAI,GAAG,EAAE,CAAC;YAChB,MAAM,WAAW,GAAG,CAAC,cAAc,CAAC,CAAC;YACrC,MAAM,UAAU,GAAG,CAAC,aAAa,CAAC,CAAC;YAEnC,wBAAwB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;YAC7D,uBAAuB,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAE3D,sBAAsB;YACtB,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC;gBAC9D;oBACE,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,OAAO,EAAE,IAAI,IAAI,EAAE;oBACnB,WAAW,EAAE,CAAC;oBACd,UAAU,EAAE,CAAC;oBACb,sBAAsB,EAAE,EAAE;iBAC3B;aACF,CAAC,CAAC;YACH,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,iCAAiC,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAClF,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,yBAAyB,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAEnF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAEvD,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,MAAM,gBAAgB,GAAG,CAAC,aAAa,CAAC,CAAC;YACzC,uBAAuB,CAAC,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;YAEjE,sBAAsB;YACtB,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,4BAA4B,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAC/E,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,yBAAyB,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACnF,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,uBAAuB,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEjF,MAAM,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAElC,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC;gBACxD,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;aAC1B,CAAC,CAAC;YACH,MAAM,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC,oBAAoB,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;YAClF,MAAM,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAChD,uCAAuC,EACvC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,mBAAmB,EAAE,CAAC;aACvB,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;YACpE,MAAM,gBAAgB,GAAG,CAAC,aAAa,CAAC,CAAC;YACzC,uBAAuB,CAAC,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;YAEjE,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,4BAA4B,CAAC,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC;YACnG,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,yBAAyB,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACnF,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,uBAAuB,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEjF,MAAM,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAElC,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,oBAAoB,CAClD,wCAAwC,EACxC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,WAAW,EAAE,aAAa,CAAC,EAAE;gBAC7B,aAAa,EAAE,aAAa,CAAC,IAAI;gBACjC,KAAK,EAAE,kBAAkB;aAC1B,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,WAAW,GAAG,eAAe,CAAC;YACpC,MAAM,SAAS,GAAG,EAAE,GAAG,aAAa,EAAE,WAAW,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;YAEtE,uBAAuB,CAAC,OAAO,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAE7D,sBAAsB;YACtB,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,qBAAqB,CAAC,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YACpF,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,iBAAiB,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YACrE,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,2BAA2B,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACrF,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,uBAAuB,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACjF,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,yBAAyB,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEnF,MAAM,OAAO,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAC;YAEtD,MAAM,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE;gBAC1B,SAAS,EAAE,CAAC,aAAa,EAAE,UAAU,CAAC;aACvC,CAAC,CAAC;YACH,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,oBAAoB,CAClD,2CAA2C,EAC3C,MAAM,CAAC,gBAAgB,CAAC;gBACtB,WAAW;aACZ,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,WAAW,GAAG,cAAc,CAAC;YACnC,uBAAuB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAExD,MAAM,MAAM,CAAC,OAAO,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAC;iBAC1D,OAAO,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\compliance-audit\\application\\services\\compliance-monitoring.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { getRepositoryToken } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\nimport { ComplianceMonitoringService } from './compliance-monitoring.service';\r\nimport { ComplianceFramework } from '../../domain/entities/compliance-framework.entity';\r\nimport { ComplianceAssessment } from '../../domain/entities/compliance-assessment.entity';\r\nimport { PolicyDefinition } from '../../domain/entities/policy-definition.entity';\r\nimport { PolicyViolation } from '../../domain/entities/policy-violation.entity';\r\nimport { AuditLog } from '../../domain/entities/audit-log.entity';\r\nimport { LoggerService } from '../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../infrastructure/logging/audit/audit.service';\r\nimport { NotificationService } from '../../../../infrastructure/notification/notification.service';\r\nimport { PolicyEnforcementService } from './policy-enforcement.service';\r\nimport { ViolationDetectionService } from './violation-detection.service';\r\n\r\ndescribe('ComplianceMonitoringService', () => {\r\n  let service: ComplianceMonitoringService;\r\n  let frameworkRepository: Repository<ComplianceFramework>;\r\n  let assessmentRepository: Repository<ComplianceAssessment>;\r\n  let policyRepository: Repository<PolicyDefinition>;\r\n  let violationRepository: Repository<PolicyViolation>;\r\n  let auditLogRepository: Repository<AuditLog>;\r\n  let loggerService: LoggerService;\r\n  let auditService: AuditService;\r\n  let notificationService: NotificationService;\r\n  let policyEnforcementService: PolicyEnforcementService;\r\n  let violationDetectionService: ViolationDetectionService;\r\n\r\n  const mockFrameworkRepository = {\r\n    find: jest.fn(),\r\n    findOne: jest.fn(),\r\n    create: jest.fn(),\r\n    save: jest.fn(),\r\n    count: jest.fn(),\r\n    createQueryBuilder: jest.fn(),\r\n  };\r\n\r\n  const mockAssessmentRepository = {\r\n    find: jest.fn(),\r\n    findOne: jest.fn(),\r\n    create: jest.fn(),\r\n    save: jest.fn(),\r\n    count: jest.fn(),\r\n    createQueryBuilder: jest.fn(),\r\n  };\r\n\r\n  const mockPolicyRepository = {\r\n    find: jest.fn(),\r\n    findOne: jest.fn(),\r\n    create: jest.fn(),\r\n    save: jest.fn(),\r\n    count: jest.fn(),\r\n  };\r\n\r\n  const mockViolationRepository = {\r\n    find: jest.fn(),\r\n    findOne: jest.fn(),\r\n    create: jest.fn(),\r\n    save: jest.fn(),\r\n    count: jest.fn(),\r\n    createQueryBuilder: jest.fn(),\r\n  };\r\n\r\n  const mockAuditLogRepository = {\r\n    find: jest.fn(),\r\n    findOne: jest.fn(),\r\n    create: jest.fn(),\r\n    save: jest.fn(),\r\n    count: jest.fn(),\r\n  };\r\n\r\n  const mockLoggerService = {\r\n    debug: jest.fn(),\r\n    log: jest.fn(),\r\n    warn: jest.fn(),\r\n    error: jest.fn(),\r\n  };\r\n\r\n  const mockAuditService = {\r\n    logUserAction: jest.fn(),\r\n  };\r\n\r\n  const mockNotificationService = {\r\n    sendComplianceNotification: jest.fn(),\r\n    sendAssessmentOverdueNotification: jest.fn(),\r\n    sendPolicyViolationNotification: jest.fn(),\r\n  };\r\n\r\n  const mockPolicyEnforcementService = {\r\n    evaluateControl: jest.fn(),\r\n    evaluatePolicyCompliance: jest.fn(),\r\n  };\r\n\r\n  const mockViolationDetectionService = {\r\n    checkPolicyCompliance: jest.fn(),\r\n    scanForViolations: jest.fn(),\r\n    createViolation: jest.fn(),\r\n  };\r\n\r\n  const mockQueryBuilder = {\r\n    where: jest.fn().mockReturnThis(),\r\n    andWhere: jest.fn().mockReturnThis(),\r\n    orderBy: jest.fn().mockReturnThis(),\r\n    skip: jest.fn().mockReturnThis(),\r\n    take: jest.fn().mockReturnThis(),\r\n    getManyAndCount: jest.fn(),\r\n    getMany: jest.fn(),\r\n    getCount: jest.fn(),\r\n    select: jest.fn().mockReturnThis(),\r\n    addSelect: jest.fn().mockReturnThis(),\r\n    groupBy: jest.fn().mockReturnThis(),\r\n    getRawMany: jest.fn(),\r\n    getRawOne: jest.fn(),\r\n  };\r\n\r\n  const mockFramework: Partial<ComplianceFramework> = {\r\n    id: 'framework-123',\r\n    name: 'SOC 2 Type II',\r\n    type: 'SOC2',\r\n    version: '2017',\r\n    isActive: true,\r\n    configuration: {\r\n      domains: [\r\n        {\r\n          id: 'security',\r\n          name: 'Security',\r\n          description: 'Security controls',\r\n          controls: [\r\n            {\r\n              id: 'CC6.1',\r\n              name: 'Logical Access Controls',\r\n              description: 'Access controls are implemented',\r\n              type: 'preventive',\r\n              priority: 'high',\r\n              frequency: 'continuous',\r\n              automatable: true,\r\n              evidenceRequirements: ['access_logs', 'configuration'],\r\n            },\r\n          ],\r\n        },\r\n      ],\r\n    },\r\n    totalControls: 1,\r\n    automatableControls: [\r\n      {\r\n        id: 'CC6.1',\r\n        name: 'Logical Access Controls',\r\n        domainId: 'security',\r\n        domainName: 'Security',\r\n      },\r\n    ],\r\n    getControlsByPriority: jest.fn(),\r\n    getDomainById: jest.fn(),\r\n    getControlById: jest.fn(),\r\n    validateConfiguration: jest.fn().mockReturnValue({ isValid: true, errors: [] }),\r\n    calculateComplexityScore: jest.fn().mockReturnValue(10),\r\n  };\r\n\r\n  const mockAssessment: Partial<ComplianceAssessment> = {\r\n    id: 'assessment-123',\r\n    name: 'Q4 2023 SOC 2 Assessment',\r\n    frameworkId: 'framework-123',\r\n    assessmentType: 'internal_audit',\r\n    status: 'planned',\r\n    assessmentDate: new Date('2023-12-15'),\r\n    nextAssessmentDate: new Date('2024-03-15'),\r\n    scope: {\r\n      description: 'Full organizational assessment',\r\n    },\r\n    isOverdue: false,\r\n    isCompliant: false,\r\n    compliancePercentage: 0,\r\n    criticalFindingsCount: 0,\r\n    highFindingsCount: 0,\r\n    ageInDays: 30,\r\n    start: jest.fn(),\r\n    complete: jest.fn(),\r\n    generateSummary: jest.fn().mockReturnValue({\r\n      id: 'assessment-123',\r\n      name: 'Q4 2023 SOC 2 Assessment',\r\n      status: 'planned',\r\n    }),\r\n  };\r\n\r\n  const mockViolation: Partial<PolicyViolation> = {\r\n    id: 'violation-123',\r\n    title: 'Access Control Violation',\r\n    severity: 'high',\r\n    status: 'open',\r\n    detectedAt: new Date(),\r\n    details: {\r\n      violatedControls: ['CC6.1'],\r\n      compliance: {\r\n        frameworks: ['framework-123'],\r\n        controls: ['CC6.1'],\r\n      },\r\n    },\r\n    generateSummary: jest.fn().mockReturnValue({\r\n      id: 'violation-123',\r\n      title: 'Access Control Violation',\r\n      severity: 'high',\r\n      status: 'open',\r\n    }),\r\n  };\r\n\r\n  beforeEach(async () => {\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        ComplianceMonitoringService,\r\n        {\r\n          provide: getRepositoryToken(ComplianceFramework),\r\n          useValue: mockFrameworkRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(ComplianceAssessment),\r\n          useValue: mockAssessmentRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(PolicyDefinition),\r\n          useValue: mockPolicyRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(PolicyViolation),\r\n          useValue: mockViolationRepository,\r\n        },\r\n        {\r\n          provide: getRepositoryToken(AuditLog),\r\n          useValue: mockAuditLogRepository,\r\n        },\r\n        {\r\n          provide: LoggerService,\r\n          useValue: mockLoggerService,\r\n        },\r\n        {\r\n          provide: AuditService,\r\n          useValue: mockAuditService,\r\n        },\r\n        {\r\n          provide: NotificationService,\r\n          useValue: mockNotificationService,\r\n        },\r\n        {\r\n          provide: PolicyEnforcementService,\r\n          useValue: mockPolicyEnforcementService,\r\n        },\r\n        {\r\n          provide: ViolationDetectionService,\r\n          useValue: mockViolationDetectionService,\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    service = module.get<ComplianceMonitoringService>(ComplianceMonitoringService);\r\n    frameworkRepository = module.get<Repository<ComplianceFramework>>(getRepositoryToken(ComplianceFramework));\r\n    assessmentRepository = module.get<Repository<ComplianceAssessment>>(getRepositoryToken(ComplianceAssessment));\r\n    policyRepository = module.get<Repository<PolicyDefinition>>(getRepositoryToken(PolicyDefinition));\r\n    violationRepository = module.get<Repository<PolicyViolation>>(getRepositoryToken(PolicyViolation));\r\n    auditLogRepository = module.get<Repository<AuditLog>>(getRepositoryToken(AuditLog));\r\n    loggerService = module.get<LoggerService>(LoggerService);\r\n    auditService = module.get<AuditService>(AuditService);\r\n    notificationService = module.get<NotificationService>(NotificationService);\r\n    policyEnforcementService = module.get<PolicyEnforcementService>(PolicyEnforcementService);\r\n    violationDetectionService = module.get<ViolationDetectionService>(ViolationDetectionService);\r\n\r\n    // Setup query builder mocks\r\n    mockFrameworkRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);\r\n    mockAssessmentRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);\r\n    mockViolationRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);\r\n  });\r\n\r\n  afterEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('getComplianceDashboard', () => {\r\n    it('should return compliance dashboard data', async () => {\r\n      // Mock repository responses\r\n      mockFrameworkRepository.count.mockResolvedValue(5); // activeFrameworks\r\n      mockAssessmentRepository.count.mockResolvedValue(20); // totalAssessments\r\n      mockViolationRepository.count\r\n        .mockResolvedValueOnce(15) // openViolations\r\n        .mockResolvedValueOnce(3); // criticalViolations\r\n\r\n      mockFrameworkRepository.find.mockResolvedValue([mockFramework]);\r\n      mockAssessmentRepository.find.mockResolvedValue([mockAssessment]);\r\n      mockViolationRepository.find.mockResolvedValue([mockViolation]);\r\n\r\n      // Mock helper methods\r\n      jest.spyOn(service as any, 'getLatestAssessment').mockResolvedValue(mockAssessment);\r\n      jest.spyOn(service as any, 'calculateFrameworkComplianceScore').mockResolvedValue(85);\r\n\r\n      const result = await service.getComplianceDashboard();\r\n\r\n      expect(result).toHaveProperty('summary');\r\n      expect(result.summary).toEqual({\r\n        activeFrameworks: 5,\r\n        totalAssessments: 20,\r\n        openViolations: 15,\r\n        criticalViolations: 3,\r\n        overallComplianceScore: expect.any(Number),\r\n      });\r\n      expect(result).toHaveProperty('complianceByFramework');\r\n      expect(result).toHaveProperty('trends');\r\n      expect(result).toHaveProperty('riskMetrics');\r\n      expect(result).toHaveProperty('timestamp');\r\n    });\r\n\r\n    it('should handle errors gracefully', async () => {\r\n      mockFrameworkRepository.count.mockRejectedValue(new Error('Database error'));\r\n\r\n      await expect(service.getComplianceDashboard()).rejects.toThrow('Database error');\r\n      expect(mockLoggerService.error).toHaveBeenCalledWith(\r\n        'Failed to generate compliance dashboard',\r\n        expect.objectContaining({\r\n          error: 'Database error',\r\n        }),\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('createAssessment', () => {\r\n    const assessmentData = {\r\n      name: 'Test Assessment',\r\n      description: 'Test assessment description',\r\n      assessmentType: 'internal_audit' as const,\r\n      assessmentDate: new Date(),\r\n      scope: {\r\n        description: 'Test scope',\r\n      },\r\n    };\r\n\r\n    it('should create assessment successfully', async () => {\r\n      const userId = 'user-123';\r\n      const savedAssessment = { ...mockAssessment, id: 'assessment-456' };\r\n\r\n      mockFrameworkRepository.findOne.mockResolvedValue(mockFramework);\r\n      mockAssessmentRepository.create.mockReturnValue(mockAssessment);\r\n      mockAssessmentRepository.save.mockResolvedValue(savedAssessment);\r\n\r\n      const result = await service.createAssessment('framework-123', assessmentData, userId);\r\n\r\n      expect(mockFrameworkRepository.findOne).toHaveBeenCalledWith({\r\n        where: { id: 'framework-123' },\r\n      });\r\n      expect(mockAssessmentRepository.create).toHaveBeenCalledWith({\r\n        ...assessmentData,\r\n        frameworkId: 'framework-123',\r\n        status: 'planned',\r\n        createdBy: userId,\r\n      });\r\n      expect(mockAssessmentRepository.save).toHaveBeenCalledWith(mockAssessment);\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        userId,\r\n        'create',\r\n        'compliance_assessment',\r\n        savedAssessment.id,\r\n        expect.objectContaining({\r\n          frameworkId: 'framework-123',\r\n          assessmentType: assessmentData.assessmentType,\r\n          assessmentName: assessmentData.name,\r\n        }),\r\n      );\r\n      expect(result).toEqual(savedAssessment);\r\n    });\r\n\r\n    it('should throw error when framework not found', async () => {\r\n      const userId = 'user-123';\r\n      mockFrameworkRepository.findOne.mockResolvedValue(null);\r\n\r\n      await expect(service.createAssessment('non-existent', assessmentData, userId))\r\n        .rejects.toThrow('Framework not found');\r\n    });\r\n  });\r\n\r\n  describe('startAssessment', () => {\r\n    it('should start assessment successfully', async () => {\r\n      const assessmentId = 'assessment-123';\r\n      const userId = 'user-123';\r\n      const assessment = { ...mockAssessment, framework: mockFramework };\r\n\r\n      mockAssessmentRepository.findOne.mockResolvedValue(assessment);\r\n      mockAssessmentRepository.save.mockResolvedValue(assessment);\r\n\r\n      // Mock helper method\r\n      jest.spyOn(service as any, 'initializeAssessmentControls').mockResolvedValue(undefined);\r\n\r\n      const result = await service.startAssessment(assessmentId, userId);\r\n\r\n      expect(mockAssessmentRepository.findOne).toHaveBeenCalledWith({\r\n        where: { id: assessmentId },\r\n        relations: ['framework'],\r\n      });\r\n      expect(assessment.start).toHaveBeenCalledWith(userId);\r\n      expect(mockAssessmentRepository.save).toHaveBeenCalledWith(assessment);\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        userId,\r\n        'start',\r\n        'compliance_assessment',\r\n        assessmentId,\r\n        expect.objectContaining({\r\n          frameworkId: assessment.frameworkId,\r\n          assessmentType: assessment.assessmentType,\r\n        }),\r\n      );\r\n      expect(result).toEqual(assessment);\r\n    });\r\n\r\n    it('should throw error when assessment not found', async () => {\r\n      const assessmentId = 'non-existent';\r\n      const userId = 'user-123';\r\n\r\n      mockAssessmentRepository.findOne.mockResolvedValue(null);\r\n\r\n      await expect(service.startAssessment(assessmentId, userId))\r\n        .rejects.toThrow('Assessment not found');\r\n    });\r\n  });\r\n\r\n  describe('completeAssessment', () => {\r\n    it('should complete assessment successfully', async () => {\r\n      const assessmentId = 'assessment-123';\r\n      const userId = 'user-123';\r\n      const results = {\r\n        overallScore: 85,\r\n        overallStatus: 'compliant',\r\n        domainResults: [],\r\n      };\r\n      const assessment = { ...mockAssessment, framework: mockFramework };\r\n\r\n      mockAssessmentRepository.findOne.mockResolvedValue(assessment);\r\n      mockAssessmentRepository.save.mockResolvedValue(assessment);\r\n\r\n      // Mock helper methods\r\n      jest.spyOn(service as any, 'generateViolationsFromAssessment').mockResolvedValue(undefined);\r\n\r\n      const result = await service.completeAssessment(assessmentId, results, userId);\r\n\r\n      expect(mockAssessmentRepository.findOne).toHaveBeenCalledWith({\r\n        where: { id: assessmentId },\r\n        relations: ['framework'],\r\n      });\r\n      expect(assessment.complete).toHaveBeenCalledWith(userId, results);\r\n      expect(mockAssessmentRepository.save).toHaveBeenCalledWith(assessment);\r\n      expect(mockNotificationService.sendComplianceNotification).toHaveBeenCalledWith(\r\n        assessment,\r\n        'assessment_completed',\r\n      );\r\n      expect(mockAuditService.logUserAction).toHaveBeenCalledWith(\r\n        userId,\r\n        'complete',\r\n        'compliance_assessment',\r\n        assessmentId,\r\n        expect.objectContaining({\r\n          frameworkId: assessment.frameworkId,\r\n          complianceScore: results.overallScore,\r\n          complianceStatus: results.overallStatus,\r\n        }),\r\n      );\r\n      expect(result).toEqual(assessment);\r\n    });\r\n  });\r\n\r\n  describe('getFrameworkComplianceStatus', () => {\r\n    it('should return framework compliance status', async () => {\r\n      const frameworkId = 'framework-123';\r\n\r\n      mockFrameworkRepository.findOne.mockResolvedValue(mockFramework);\r\n\r\n      // Mock helper methods\r\n      jest.spyOn(service as any, 'getLatestAssessment').mockResolvedValue(mockAssessment);\r\n      jest.spyOn(service as any, 'getFrameworkViolations').mockResolvedValue([mockViolation]);\r\n      jest.spyOn(service as any, 'getFrameworkPolicies').mockResolvedValue([]);\r\n      jest.spyOn(service as any, 'calculateFrameworkComplianceScore').mockResolvedValue(85);\r\n      jest.spyOn(service as any, 'calculateFrameworkRiskLevel').mockResolvedValue('medium');\r\n\r\n      const result = await service.getFrameworkComplianceStatus(frameworkId);\r\n\r\n      expect(result).toHaveProperty('framework');\r\n      expect(result).toHaveProperty('latestAssessment');\r\n      expect(result).toHaveProperty('violations');\r\n      expect(result).toHaveProperty('policies');\r\n      expect(result).toHaveProperty('complianceScore');\r\n      expect(result).toHaveProperty('riskLevel');\r\n      expect(result.framework.id).toBe(frameworkId);\r\n      expect(result.complianceScore).toBe(85);\r\n      expect(result.riskLevel).toBe('medium');\r\n    });\r\n\r\n    it('should throw error when framework not found', async () => {\r\n      const frameworkId = 'non-existent';\r\n      mockFrameworkRepository.findOne.mockResolvedValue(null);\r\n\r\n      await expect(service.getFrameworkComplianceStatus(frameworkId))\r\n        .rejects.toThrow('Framework not found');\r\n    });\r\n  });\r\n\r\n  describe('getComplianceTrends', () => {\r\n    it('should return compliance trends', async () => {\r\n      const days = 90;\r\n      const assessments = [mockAssessment];\r\n      const violations = [mockViolation];\r\n\r\n      mockAssessmentRepository.find.mockResolvedValue(assessments);\r\n      mockViolationRepository.find.mockResolvedValue(violations);\r\n\r\n      // Mock helper methods\r\n      jest.spyOn(service as any, 'groupTrendsByWeek').mockReturnValue([\r\n        {\r\n          weekStart: new Date(),\r\n          weekEnd: new Date(),\r\n          assessments: 1,\r\n          violations: 1,\r\n          averageComplianceScore: 85,\r\n        },\r\n      ]);\r\n      jest.spyOn(service as any, 'calculateAverageComplianceScore').mockReturnValue(85);\r\n      jest.spyOn(service as any, 'calculateTrendDirection').mockReturnValue('improving');\r\n\r\n      const result = await service.getComplianceTrends(days);\r\n\r\n      expect(result).toHaveProperty('period');\r\n      expect(result).toHaveProperty('trends');\r\n      expect(result).toHaveProperty('summary');\r\n      expect(result.period.days).toBe(days);\r\n      expect(result.summary.totalAssessments).toBe(1);\r\n      expect(result.summary.totalViolations).toBe(1);\r\n      expect(result.summary.averageComplianceScore).toBe(85);\r\n      expect(result.summary.trendDirection).toBe('improving');\r\n    });\r\n  });\r\n\r\n  describe('monitorCompliance', () => {\r\n    it('should monitor compliance for all active frameworks', async () => {\r\n      const activeFrameworks = [mockFramework];\r\n      mockFrameworkRepository.find.mockResolvedValue(activeFrameworks);\r\n\r\n      // Mock helper methods\r\n      jest.spyOn(service, 'monitorFrameworkCompliance').mockResolvedValue(undefined);\r\n      jest.spyOn(service as any, 'checkOverdueAssessments').mockResolvedValue(undefined);\r\n      jest.spyOn(service as any, 'checkPolicyViolations').mockResolvedValue(undefined);\r\n\r\n      await service.monitorCompliance();\r\n\r\n      expect(mockFrameworkRepository.find).toHaveBeenCalledWith({\r\n        where: { isActive: true },\r\n      });\r\n      expect(service.monitorFrameworkCompliance).toHaveBeenCalledWith(mockFramework.id);\r\n      expect(mockLoggerService.log).toHaveBeenCalledWith(\r\n        'Compliance monitoring cycle completed',\r\n        expect.objectContaining({\r\n          frameworksMonitored: 1,\r\n        }),\r\n      );\r\n    });\r\n\r\n    it('should handle framework monitoring errors gracefully', async () => {\r\n      const activeFrameworks = [mockFramework];\r\n      mockFrameworkRepository.find.mockResolvedValue(activeFrameworks);\r\n\r\n      jest.spyOn(service, 'monitorFrameworkCompliance').mockRejectedValue(new Error('Monitoring error'));\r\n      jest.spyOn(service as any, 'checkOverdueAssessments').mockResolvedValue(undefined);\r\n      jest.spyOn(service as any, 'checkPolicyViolations').mockResolvedValue(undefined);\r\n\r\n      await service.monitorCompliance();\r\n\r\n      expect(mockLoggerService.error).toHaveBeenCalledWith(\r\n        'Failed to monitor framework compliance',\r\n        expect.objectContaining({\r\n          frameworkId: mockFramework.id,\r\n          frameworkName: mockFramework.name,\r\n          error: 'Monitoring error',\r\n        }),\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('monitorFrameworkCompliance', () => {\r\n    it('should monitor framework compliance successfully', async () => {\r\n      const frameworkId = 'framework-123';\r\n      const framework = { ...mockFramework, assessments: [], policies: [] };\r\n\r\n      mockFrameworkRepository.findOne.mockResolvedValue(framework);\r\n\r\n      // Mock helper methods\r\n      jest.spyOn(service as any, 'getLatestAssessment').mockResolvedValue(mockAssessment);\r\n      jest.spyOn(service as any, 'isAssessmentDue').mockReturnValue(false);\r\n      jest.spyOn(service as any, 'monitorContinuousControls').mockResolvedValue(undefined);\r\n      jest.spyOn(service as any, 'checkPolicyCompliance').mockResolvedValue(undefined);\r\n      jest.spyOn(service as any, 'updateComplianceMetrics').mockResolvedValue(undefined);\r\n\r\n      await service.monitorFrameworkCompliance(frameworkId);\r\n\r\n      expect(mockFrameworkRepository.findOne).toHaveBeenCalledWith({\r\n        where: { id: frameworkId },\r\n        relations: ['assessments', 'policies'],\r\n      });\r\n      expect(mockLoggerService.debug).toHaveBeenCalledWith(\r\n        'Framework compliance monitoring completed',\r\n        expect.objectContaining({\r\n          frameworkId,\r\n        }),\r\n      );\r\n    });\r\n\r\n    it('should throw error when framework not found', async () => {\r\n      const frameworkId = 'non-existent';\r\n      mockFrameworkRepository.findOne.mockResolvedValue(null);\r\n\r\n      await expect(service.monitorFrameworkCompliance(frameworkId))\r\n        .rejects.toThrow('Framework not found');\r\n    });\r\n  });\r\n});\r\n"], "version": 3}