2af10e30d3d97dbd179e46fc4af47c1f
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const base_service_1 = require("../../domain/base-service");
const base_specification_1 = require("../../domain/base-specification");
const base_aggregate_root_1 = require("../../domain/base-aggregate-root");
const base_domain_event_1 = require("../../domain/base-domain-event");
class TestService extends base_service_1.BaseService {
    constructor() {
        super('TestService');
    }
    // Expose protected methods for testing
    testCreateContext(options = {}) {
        return this.createContext(options);
    }
    async testExecuteOperation(operation, context, operationName) {
        return this.executeOperation(operation, context, operationName);
    }
    testValidateBusinessRules(entity, specifications) {
        return this.validateBusinessRules(entity, specifications);
    }
    testCollectDomainEvents(aggregates) {
        return this.collectDomainEvents(aggregates);
    }
    testClearDomainEvents(aggregates) {
        return this.clearDomainEvents(aggregates);
    }
    testSuccess(data, metadata) {
        return this.success(data, metadata);
    }
    testFailure(error, errors, metadata) {
        return this.failure(error, errors, metadata);
    }
    async testRetryOperation(operation, maxRetries = 3, baseDelay = 1000) {
        return this.retryOperation(operation, maxRetries, baseDelay);
    }
    async testExecuteParallel(operations, failFast = false) {
        return this.executeParallel(operations, failFast);
    }
    testGenerateCorrelationId() {
        return this.generateCorrelationId();
    }
    testLogMetric(metricName, value, tags) {
        return this.logMetric(metricName, value, tags);
    }
    // Test business operation
    async performBusinessOperation(input) {
        const context = this.createContext({ customField: 'test' });
        return this.executeOperation(async () => {
            if (input === 'error') {
                throw new Error('Test error');
            }
            return `Processed: ${input}`;
        }, context, 'performBusinessOperation').then(result => {
            if (result.success) {
                return result.data;
            }
            throw new Error(result.error);
        });
    }
}
class TestEntitySpecification extends base_specification_1.BaseSpecification {
    constructor(shouldPass) {
        super();
        this.shouldPass = shouldPass;
    }
    isSatisfiedBy(entity) {
        return this.shouldPass && entity.isValid;
    }
    getDescription() {
        return `Entity should ${this.shouldPass ? 'pass' : 'fail'} validation`;
    }
}
class TestAggregate extends base_aggregate_root_1.BaseAggregateRoot {
    constructor(props, id) {
        super(props, id);
    }
    addTestEvent(message) {
        const event = new TestDomainEvent(this.id, { message });
        this.addDomainEvent(event);
    }
}
class TestDomainEvent extends base_domain_event_1.BaseDomainEvent {
    constructor(aggregateId, data) {
        super(aggregateId, data);
    }
}
describe('BaseService', () => {
    let service;
    beforeEach(() => {
        service = new TestService();
    });
    describe('context creation', () => {
        it('should create context with default timestamp', () => {
            const context = service.testCreateContext();
            expect(context.timestamp).toBeInstanceOf(Date);
            expect(context.timestamp.getTime()).toBeLessThanOrEqual(Date.now());
        });
        it('should create context with provided options', () => {
            const options = {
                userId: 'user-123',
                tenantId: 'tenant-456',
                correlationId: 'correlation-789',
                customField: 'custom-value'
            };
            const context = service.testCreateContext(options);
            expect(context.userId).toBe('user-123');
            expect(context.tenantId).toBe('tenant-456');
            expect(context.correlationId).toBe('correlation-789');
            expect(context.customField).toBe('custom-value');
            expect(context.timestamp).toBeInstanceOf(Date);
        });
        it('should merge options with defaults', () => {
            const context = service.testCreateContext({ userId: 'user-123' });
            expect(context.userId).toBe('user-123');
            expect(context.timestamp).toBeInstanceOf(Date);
            expect(context.tenantId).toBeUndefined();
        });
    });
    describe('operation execution', () => {
        it('should execute successful operation', async () => {
            const context = service.testCreateContext();
            const operation = jest.fn().mockResolvedValue('success');
            const result = await service.testExecuteOperation(operation, context, 'testOp');
            expect(result.success).toBe(true);
            expect(result.data).toBe('success');
            expect(result.error).toBeUndefined();
            expect(result.metadata).toHaveProperty('duration');
            expect(result.metadata).toHaveProperty('operationName', 'testOp');
            expect(operation).toHaveBeenCalledTimes(1);
        });
        it('should handle operation failure', async () => {
            const context = service.testCreateContext();
            const error = new Error('Test error');
            const operation = jest.fn().mockRejectedValue(error);
            const result = await service.testExecuteOperation(operation, context, 'testOp');
            expect(result.success).toBe(false);
            expect(result.data).toBeUndefined();
            expect(result.error).toBe('Test error');
            expect(result.metadata).toHaveProperty('duration');
            expect(result.metadata).toHaveProperty('errorType', 'Error');
        });
        it('should measure execution time', async () => {
            const context = service.testCreateContext();
            const operation = jest.fn().mockImplementation(() => new Promise(resolve => setTimeout(() => resolve('done'), 10)));
            const result = await service.testExecuteOperation(operation, context, 'testOp');
            expect(result.success).toBe(true);
            expect(result.metadata.duration).toBeGreaterThan(0);
        });
        it('should include correlation ID in logs', async () => {
            const context = service.testCreateContext({ correlationId: 'test-correlation' });
            const operation = jest.fn().mockResolvedValue('success');
            const result = await service.testExecuteOperation(operation, context, 'testOp');
            expect(result.success).toBe(true);
            // Correlation ID should be used in logging (tested through behavior)
        });
    });
    describe('business rule validation', () => {
        it('should pass validation when all specifications are satisfied', () => {
            const entity = { id: '1', name: 'Test', isValid: true };
            const specifications = [
                new TestEntitySpecification(true),
                new TestEntitySpecification(true)
            ];
            const result = service.testValidateBusinessRules(entity, specifications);
            expect(result.success).toBe(true);
            expect(result.errors).toBeUndefined();
        });
        it('should fail validation when specifications are not satisfied', () => {
            const entity = { id: '1', name: 'Test', isValid: false };
            const specifications = [
                new TestEntitySpecification(true),
                new TestEntitySpecification(false)
            ];
            const result = service.testValidateBusinessRules(entity, specifications);
            expect(result.success).toBe(false);
            expect(result.errors).toHaveLength(2);
            expect(result.errors).toContain('Entity should pass validation');
            expect(result.errors).toContain('Entity should fail validation');
        });
        it('should handle empty specifications array', () => {
            const entity = { id: '1', name: 'Test', isValid: true };
            const specifications = [];
            const result = service.testValidateBusinessRules(entity, specifications);
            expect(result.success).toBe(true);
        });
    });
    describe('domain event management', () => {
        it('should collect domain events from aggregates', () => {
            const aggregate1 = new TestAggregate({ name: 'Aggregate 1' });
            const aggregate2 = new TestAggregate({ name: 'Aggregate 2' });
            aggregate1.addTestEvent('Event 1');
            aggregate2.addTestEvent('Event 2');
            aggregate2.addTestEvent('Event 3');
            const events = service.testCollectDomainEvents([aggregate1, aggregate2]);
            expect(events).toHaveLength(3);
            expect(events[0]).toBeInstanceOf(TestDomainEvent);
            expect(events[1]).toBeInstanceOf(TestDomainEvent);
            expect(events[2]).toBeInstanceOf(TestDomainEvent);
        });
        it('should handle aggregates with no events', () => {
            const aggregate = new TestAggregate({ name: 'Aggregate' });
            const events = service.testCollectDomainEvents([aggregate]);
            expect(events).toHaveLength(0);
        });
        it('should clear domain events from aggregates', () => {
            const aggregate1 = new TestAggregate({ name: 'Aggregate 1' });
            const aggregate2 = new TestAggregate({ name: 'Aggregate 2' });
            aggregate1.addTestEvent('Event 1');
            aggregate2.addTestEvent('Event 2');
            expect(aggregate1.domainEvents).toHaveLength(1);
            expect(aggregate2.domainEvents).toHaveLength(1);
            service.testClearDomainEvents([aggregate1, aggregate2]);
            expect(aggregate1.domainEvents).toHaveLength(0);
            expect(aggregate2.domainEvents).toHaveLength(0);
        });
    });
    describe('result creation helpers', () => {
        it('should create success result', () => {
            const result = service.testSuccess('data', { extra: 'metadata' });
            expect(result.success).toBe(true);
            expect(result.data).toBe('data');
            expect(result.error).toBeUndefined();
            expect(result.metadata).toEqual({ extra: 'metadata' });
        });
        it('should create success result without data', () => {
            const result = service.testSuccess();
            expect(result.success).toBe(true);
            expect(result.data).toBeUndefined();
        });
        it('should create failure result with single error', () => {
            const result = service.testFailure('Single error', undefined, { context: 'test' });
            expect(result.success).toBe(false);
            expect(result.error).toBe('Single error');
            expect(result.errors).toBeUndefined();
            expect(result.metadata).toEqual({ context: 'test' });
        });
        it('should create failure result with multiple errors', () => {
            const errors = ['Error 1', 'Error 2'];
            const result = service.testFailure(undefined, errors);
            expect(result.success).toBe(false);
            expect(result.error).toBeUndefined();
            expect(result.errors).toEqual(errors);
        });
    });
    describe('retry mechanism', () => {
        it('should succeed on first attempt', async () => {
            const operation = jest.fn().mockResolvedValue('success');
            const result = await service.testRetryOperation(operation, 3, 100);
            expect(result).toBe('success');
            expect(operation).toHaveBeenCalledTimes(1);
        });
        it('should retry on failure and eventually succeed', async () => {
            const operation = jest.fn()
                .mockRejectedValueOnce(new Error('Attempt 1'))
                .mockRejectedValueOnce(new Error('Attempt 2'))
                .mockResolvedValue('success');
            const result = await service.testRetryOperation(operation, 3, 10);
            expect(result).toBe('success');
            expect(operation).toHaveBeenCalledTimes(3);
        });
        it('should fail after max retries', async () => {
            const error = new Error('Persistent error');
            const operation = jest.fn().mockRejectedValue(error);
            await expect(service.testRetryOperation(operation, 2, 10)).rejects.toThrow('Persistent error');
            expect(operation).toHaveBeenCalledTimes(3); // Initial + 2 retries
        });
        it('should handle zero retries', async () => {
            const error = new Error('Immediate error');
            const operation = jest.fn().mockRejectedValue(error);
            await expect(service.testRetryOperation(operation, 0, 10)).rejects.toThrow('Immediate error');
            expect(operation).toHaveBeenCalledTimes(1);
        });
    });
    describe('parallel execution', () => {
        it('should execute operations in parallel with fail fast', async () => {
            const operations = [
                jest.fn().mockResolvedValue('result1'),
                jest.fn().mockResolvedValue('result2'),
                jest.fn().mockResolvedValue('result3')
            ];
            const results = await service.testExecuteParallel(operations, true);
            expect(results).toHaveLength(3);
            expect(results.every(r => r.success)).toBe(true);
            expect(results[0].data).toBe('result1');
            expect(results[1].data).toBe('result2');
            expect(results[2].data).toBe('result3');
        });
        it('should fail fast on first error', async () => {
            const operations = [
                jest.fn().mockResolvedValue('result1'),
                jest.fn().mockRejectedValue(new Error('Error 2')),
                jest.fn().mockResolvedValue('result3')
            ];
            const results = await service.testExecuteParallel(operations, true);
            expect(results).toHaveLength(1);
            expect(results[0].success).toBe(false);
            expect(results[0].error).toBe('Error 2');
        });
        it('should execute all operations without fail fast', async () => {
            const operations = [
                jest.fn().mockResolvedValue('result1'),
                jest.fn().mockRejectedValue(new Error('Error 2')),
                jest.fn().mockResolvedValue('result3')
            ];
            const results = await service.testExecuteParallel(operations, false);
            expect(results).toHaveLength(3);
            expect(results[0].success).toBe(true);
            expect(results[0].data).toBe('result1');
            expect(results[1].success).toBe(false);
            expect(results[1].error).toBe('Error 2');
            expect(results[2].success).toBe(true);
            expect(results[2].data).toBe('result3');
        });
        it('should handle empty operations array', async () => {
            const results = await service.testExecuteParallel([], false);
            expect(results).toHaveLength(0);
        });
    });
    describe('utility methods', () => {
        it('should generate correlation ID', () => {
            const correlationId = service.testGenerateCorrelationId();
            expect(correlationId).toBeDefined();
            expect(typeof correlationId).toBe('string');
            expect(correlationId.length).toBeGreaterThan(0);
        });
        it('should generate unique correlation IDs', () => {
            const id1 = service.testGenerateCorrelationId();
            const id2 = service.testGenerateCorrelationId();
            expect(id1).not.toBe(id2);
        });
        it('should log metrics', () => {
            // This test verifies the method doesn't throw
            expect(() => {
                service.testLogMetric('test.metric', 42, { tag1: 'value1' });
            }).not.toThrow();
        });
    });
    describe('integration scenarios', () => {
        it('should handle complete business operation successfully', async () => {
            const result = await service.performBusinessOperation('test input');
            expect(result).toBe('Processed: test input');
        });
        it('should handle business operation failure', async () => {
            await expect(service.performBusinessOperation('error')).rejects.toThrow('Test error');
        });
    });
    describe('inheritance', () => {
        class ExtendedService extends base_service_1.BaseService {
            constructor() {
                super('ExtendedService');
            }
            async extendedOperation() {
                const context = this.createContext({ userId: 'extended-user' });
                const result = await this.executeOperation(async () => 'extended result', context, 'extendedOperation');
                return result.success ? result.data : 'failed';
            }
        }
        it('should support inheritance', async () => {
            const extendedService = new ExtendedService();
            const result = await extendedService.extendedOperation();
            expect(result).toBe('extended result');
        });
    });
    describe('error handling edge cases', () => {
        it('should handle non-Error objects thrown', async () => {
            const context = service.testCreateContext();
            const operation = jest.fn().mockRejectedValue('string error');
            const result = await service.testExecuteOperation(operation, context, 'testOp');
            expect(result.success).toBe(false);
            expect(result.error).toBe('string error');
        });
        it('should handle null/undefined errors', async () => {
            const context = service.testCreateContext();
            const operation = jest.fn().mockRejectedValue(null);
            const result = await service.testExecuteOperation(operation, context, 'testOp');
            expect(result.success).toBe(false);
            expect(result.error).toBeDefined();
        });
    });
    describe('performance considerations', () => {
        it('should handle large number of parallel operations', async () => {
            const operations = Array.from({ length: 100 }, (_, i) => jest.fn().mockResolvedValue(`result${i}`));
            const startTime = Date.now();
            const results = await service.testExecuteParallel(operations, false);
            const endTime = Date.now();
            expect(results).toHaveLength(100);
            expect(results.every(r => r.success)).toBe(true);
            expect(endTime - startTime).toBeLessThan(1000); // Should be fast due to parallelization
        });
        it('should handle operations with varying execution times', async () => {
            const operations = [
                () => new Promise(resolve => setTimeout(() => resolve('fast'), 10)),
                () => new Promise(resolve => setTimeout(() => resolve('medium'), 50)),
                () => new Promise(resolve => setTimeout(() => resolve('slow'), 100))
            ];
            const results = await service.testExecuteParallel(operations, false);
            expect(results).toHaveLength(3);
            expect(results.every(r => r.success)).toBe(true);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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