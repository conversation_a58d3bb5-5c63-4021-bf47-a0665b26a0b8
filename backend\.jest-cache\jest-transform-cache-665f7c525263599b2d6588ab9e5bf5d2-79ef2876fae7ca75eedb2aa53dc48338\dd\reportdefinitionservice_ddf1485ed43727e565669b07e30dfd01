da72b045ab695905e35839ae2d748cbb
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReportDefinitionService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_emitter_1 = require("@nestjs/event-emitter");
const logger_service_1 = require("../../../infrastructure/logging/logger.service");
const distributed_cache_service_1 = require("../../../infrastructure/cache/distributed-cache.service");
const report_definition_entity_1 = require("../entities/report-definition.entity");
const audit_log_service_1 = require("../../compliance-audit/services/audit-log.service");
/**
 * Report Definition Service
 *
 * Provides comprehensive report definition management including:
 * - CRUD operations with validation and access control
 * - Template management and cloning capabilities
 * - Configuration validation and optimization recommendations
 * - Usage statistics tracking and popularity scoring
 * - Integration with compliance and audit systems
 * - Performance optimization with caching strategies
 * - User permission validation and data filtering
 */
let ReportDefinitionService = class ReportDefinitionService {
    constructor(reportDefinitionRepository, eventEmitter, cacheService, auditLogService) {
        this.reportDefinitionRepository = reportDefinitionRepository;
        this.eventEmitter = eventEmitter;
        this.cacheService = cacheService;
        this.auditLogService = auditLogService;
        this.logger = new logger_service_1.LoggerService('ReportDefinitionService');
    }
    /**
     * Create a new report definition
     */
    async createReportDefinition(createDto, userId) {
        try {
            this.logger.log('Creating new report definition', {
                name: createDto.name,
                reportType: createDto.reportType,
                userId,
            });
            // Validate report name uniqueness for the user
            await this.validateReportNameUniqueness(createDto.name, userId);
            // Validate configuration
            const validationResult = this.validateReportConfiguration(createDto);
            if (!validationResult.isValid) {
                throw new common_1.BadRequestException(`Configuration validation failed: ${validationResult.errors.join(', ')}`);
            }
            // Create report definition entity
            const reportDefinition = this.reportDefinitionRepository.create({
                ...createDto,
                ownerId: userId,
                isActive: true,
                metadata: {
                    tags: createDto.tags || [],
                    version: '1.0',
                    lastModifiedBy: userId,
                    changeLog: [{
                            version: '1.0',
                            changes: ['Initial creation'],
                            modifiedBy: userId,
                            modifiedAt: new Date(),
                        }],
                    usage: {
                        executionCount: 0,
                        lastExecuted: null,
                        averageExecutionTime: 0,
                        popularityScore: 0,
                    },
                    compliance: {
                        frameworks: createDto.complianceFrameworks || [],
                        dataClassification: createDto.dataClassification || 'internal',
                        retentionPeriod: createDto.retentionPeriod || 365,
                        auditRequired: createDto.auditRequired || false,
                    },
                },
            });
            // Save to database
            const savedReport = await this.reportDefinitionRepository.save(reportDefinition);
            // Cache the report definition
            await this.cacheReportDefinition(savedReport);
            // Log audit trail
            await this.auditLogService.createAuditLog({
                tableName: 'report_definitions',
                recordId: savedReport.id,
                action: 'REPORT_DEFINITION_CREATED',
                userId,
                newValues: {
                    name: savedReport.name,
                    reportType: savedReport.reportType,
                    category: savedReport.category,
                    isTemplate: savedReport.isTemplate,
                },
                success: true,
                riskLevel: 'low',
                complianceFrameworks: savedReport.metadata.compliance.frameworks,
                businessJustification: 'Report definition creation',
            });
            // Emit creation event
            this.eventEmitter.emit('reporting.definition.created', {
                reportId: savedReport.id,
                name: savedReport.name,
                reportType: savedReport.reportType,
                userId,
                timestamp: new Date(),
            });
            this.logger.log('Report definition created successfully', {
                reportId: savedReport.id,
                name: savedReport.name,
                userId,
            });
            return savedReport;
        }
        catch (error) {
            this.logger.error('Failed to create report definition', {
                error: error.message,
                name: createDto.name,
                userId,
            });
            throw error;
        }
    }
    /**
     * Get report definition by ID with access control
     */
    async getReportDefinitionById(id, userId, userRoles = []) {
        try {
            this.logger.debug('Retrieving report definition', { reportId: id, userId });
            // Try cache first
            const cachedReport = await this.getCachedReportDefinition(id);
            if (cachedReport) {
                // Validate access
                if (!cachedReport.hasUserAccess(userId, userRoles, 'view')) {
                    throw new common_1.ForbiddenException('Insufficient permissions to view this report');
                }
                return cachedReport;
            }
            // Fetch from database
            const reportDefinition = await this.reportDefinitionRepository.findOne({
                where: { id, isActive: true },
                relations: ['executions'],
            });
            if (!reportDefinition) {
                throw new common_1.NotFoundException(`Report definition with ID ${id} not found`);
            }
            // Validate access
            if (!reportDefinition.hasUserAccess(userId, userRoles, 'view')) {
                throw new common_1.ForbiddenException('Insufficient permissions to view this report');
            }
            // Cache the report
            await this.cacheReportDefinition(reportDefinition);
            return reportDefinition;
        }
        catch (error) {
            this.logger.error('Failed to retrieve report definition', {
                error: error.message,
                reportId: id,
                userId,
            });
            throw error;
        }
    }
    /**
     * Get report definitions with filtering and pagination
     */
    async getReportDefinitions(filters, userId, userRoles = []) {
        try {
            this.logger.debug('Retrieving report definitions with filters', {
                filters,
                userId,
            });
            const page = filters.page || 1;
            const limit = Math.min(filters.limit || 20, 100); // Max 100 per page
            const offset = (page - 1) * limit;
            // Build query
            const queryBuilder = this.reportDefinitionRepository.createQueryBuilder('report')
                .where('report.isActive = :isActive', { isActive: true });
            // Apply filters
            if (filters.reportType) {
                queryBuilder.andWhere('report.reportType = :reportType', { reportType: filters.reportType });
            }
            if (filters.category) {
                queryBuilder.andWhere('report.category = :category', { category: filters.category });
            }
            if (filters.ownerId) {
                queryBuilder.andWhere('report.ownerId = :ownerId', { ownerId: filters.ownerId });
            }
            if (filters.isTemplate !== undefined) {
                queryBuilder.andWhere('report.isTemplate = :isTemplate', { isTemplate: filters.isTemplate });
            }
            if (filters.tags && filters.tags.length > 0) {
                queryBuilder.andWhere('report.metadata @> :tags', {
                    tags: JSON.stringify({ tags: filters.tags }),
                });
            }
            if (filters.search) {
                queryBuilder.andWhere('(report.name ILIKE :search OR report.description ILIKE :search)', { search: `%${filters.search}%` });
            }
            // Apply access control - only show reports user can view
            queryBuilder.andWhere('(report.ownerId = :userId OR ' +
                'report.accessControl @> :publicAccess OR ' +
                'report.accessControl @> :userAccess OR ' +
                'report.accessControl @> :roleAccess)', {
                userId,
                publicAccess: JSON.stringify({ visibility: 'public' }),
                userAccess: JSON.stringify({ allowedUsers: [userId] }),
                roleAccess: JSON.stringify({ allowedRoles: userRoles }),
            });
            // Order by popularity and creation date
            queryBuilder.orderBy('report.metadata->>\'usage\'->>\'popularityScore\'', 'DESC')
                .addOrderBy('report.createdAt', 'DESC');
            // Get total count
            const total = await queryBuilder.getCount();
            // Apply pagination
            queryBuilder.limit(limit).offset(offset);
            // Execute query
            const reports = await queryBuilder.getMany();
            // Filter out reports user doesn't have access to (additional security layer)
            const accessibleReports = reports.filter(report => report.hasUserAccess(userId, userRoles, 'view'));
            this.logger.debug('Retrieved report definitions', {
                total,
                returned: accessibleReports.length,
                page,
                userId,
            });
            return {
                reports: accessibleReports,
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit),
            };
        }
        catch (error) {
            this.logger.error('Failed to retrieve report definitions', {
                error: error.message,
                filters,
                userId,
            });
            throw error;
        }
    }
    /**
     * Update report definition
     */
    async updateReportDefinition(id, updateDto, userId, userRoles = []) {
        try {
            this.logger.log('Updating report definition', { reportId: id, userId });
            // Get existing report
            const existingReport = await this.getReportDefinitionById(id, userId, userRoles);
            // Check edit permission
            if (!existingReport.hasUserAccess(userId, userRoles, 'edit')) {
                throw new common_1.ForbiddenException('Insufficient permissions to edit this report');
            }
            // Validate name uniqueness if name is being changed
            if (updateDto.name && updateDto.name !== existingReport.name) {
                await this.validateReportNameUniqueness(updateDto.name, userId, id);
            }
            // Validate configuration if provided
            if (updateDto.dataSourceConfig || updateDto.visualizationConfig || updateDto.exportConfig) {
                const configToValidate = {
                    ...existingReport,
                    ...updateDto,
                };
                const validationResult = this.validateReportConfiguration(configToValidate);
                if (!validationResult.isValid) {
                    throw new common_1.BadRequestException(`Configuration validation failed: ${validationResult.errors.join(', ')}`);
                }
            }
            // Update metadata
            const updatedMetadata = {
                ...existingReport.metadata,
                lastModifiedBy: userId,
                changeLog: [
                    ...existingReport.metadata.changeLog,
                    {
                        version: this.incrementVersion(existingReport.metadata.version),
                        changes: this.generateChangeDescription(existingReport, updateDto),
                        modifiedBy: userId,
                        modifiedAt: new Date(),
                    },
                ],
            };
            // Apply updates
            const updatedReport = await this.reportDefinitionRepository.save({
                ...existingReport,
                ...updateDto,
                metadata: updatedMetadata,
                updatedAt: new Date(),
            });
            // Update cache
            await this.cacheReportDefinition(updatedReport);
            // Log audit trail
            await this.auditLogService.createAuditLog({
                tableName: 'report_definitions',
                recordId: id,
                action: 'REPORT_DEFINITION_UPDATED',
                userId,
                oldValues: this.extractAuditableFields(existingReport),
                newValues: this.extractAuditableFields(updatedReport),
                success: true,
                riskLevel: 'low',
                complianceFrameworks: updatedReport.metadata.compliance.frameworks,
                businessJustification: 'Report definition update',
            });
            // Emit update event
            this.eventEmitter.emit('reporting.definition.updated', {
                reportId: id,
                name: updatedReport.name,
                changes: updatedMetadata.changeLog[updatedMetadata.changeLog.length - 1].changes,
                userId,
                timestamp: new Date(),
            });
            this.logger.log('Report definition updated successfully', {
                reportId: id,
                userId,
            });
            return updatedReport;
        }
        catch (error) {
            this.logger.error('Failed to update report definition', {
                error: error.message,
                reportId: id,
                userId,
            });
            throw error;
        }
    }
    /**
     * Delete report definition (soft delete)
     */
    async deleteReportDefinition(id, userId, userRoles = []) {
        try {
            this.logger.log('Deleting report definition', { reportId: id, userId });
            // Get existing report
            const existingReport = await this.getReportDefinitionById(id, userId, userRoles);
            // Check delete permission
            if (!existingReport.hasUserAccess(userId, userRoles, 'delete')) {
                throw new common_1.ForbiddenException('Insufficient permissions to delete this report');
            }
            // Check if report has active executions
            const activeExecutions = existingReport.executions?.filter(execution => execution.status === 'running' || execution.status === 'pending');
            if (activeExecutions && activeExecutions.length > 0) {
                throw new common_1.BadRequestException('Cannot delete report with active executions');
            }
            // Soft delete
            await this.reportDefinitionRepository.update(id, {
                isActive: false,
                updatedAt: new Date(),
            });
            // Remove from cache
            await this.removeCachedReportDefinition(id);
            // Log audit trail
            await this.auditLogService.createAuditLog({
                tableName: 'report_definitions',
                recordId: id,
                action: 'REPORT_DEFINITION_DELETED',
                userId,
                oldValues: this.extractAuditableFields(existingReport),
                success: true,
                riskLevel: 'medium',
                complianceFrameworks: existingReport.metadata.compliance.frameworks,
                businessJustification: 'Report definition deletion',
            });
            // Emit deletion event
            this.eventEmitter.emit('reporting.definition.deleted', {
                reportId: id,
                name: existingReport.name,
                userId,
                timestamp: new Date(),
            });
            this.logger.log('Report definition deleted successfully', {
                reportId: id,
                userId,
            });
        }
        catch (error) {
            this.logger.error('Failed to delete report definition', {
                error: error.message,
                reportId: id,
                userId,
            });
            throw error;
        }
    }
    /**
     * Clone report definition from template
     */
    async cloneReportDefinition(templateId, newName, userId, userRoles = []) {
        try {
            this.logger.log('Cloning report definition from template', {
                templateId,
                newName,
                userId,
            });
            // Get template
            const template = await this.getReportDefinitionById(templateId, userId, userRoles);
            if (!template.isTemplate) {
                throw new common_1.BadRequestException('Source report is not a template');
            }
            // Validate new name uniqueness
            await this.validateReportNameUniqueness(newName, userId);
            // Clone the template
            const clonedData = template.clone(newName, userId);
            // Create new report
            const clonedReport = await this.reportDefinitionRepository.save(clonedData);
            // Cache the new report
            await this.cacheReportDefinition(clonedReport);
            // Log audit trail
            await this.auditLogService.createAuditLog({
                tableName: 'report_definitions',
                recordId: clonedReport.id,
                action: 'REPORT_DEFINITION_CLONED',
                userId,
                newValues: {
                    name: clonedReport.name,
                    templateId,
                    clonedFrom: template.name,
                },
                success: true,
                riskLevel: 'low',
                complianceFrameworks: clonedReport.metadata.compliance.frameworks,
                businessJustification: 'Report definition cloning from template',
            });
            // Emit cloning event
            this.eventEmitter.emit('reporting.definition.cloned', {
                reportId: clonedReport.id,
                templateId,
                name: clonedReport.name,
                userId,
                timestamp: new Date(),
            });
            this.logger.log('Report definition cloned successfully', {
                reportId: clonedReport.id,
                templateId,
                userId,
            });
            return clonedReport;
        }
        catch (error) {
            this.logger.error('Failed to clone report definition', {
                error: error.message,
                templateId,
                newName,
                userId,
            });
            throw error;
        }
    }
    /**
     * Get optimization recommendations for a report
     */
    async getOptimizationRecommendations(id, userId, userRoles = []) {
        try {
            const report = await this.getReportDefinitionById(id, userId, userRoles);
            const recommendations = {
                performance: [],
                security: [],
                usability: [],
                compliance: [],
            };
            // Performance recommendations
            if (!report.performanceConfig.caching.enabled) {
                recommendations.performance.push('Enable caching to improve report generation performance');
            }
            if (report.dataSourceConfig.sources.length > 3) {
                recommendations.performance.push('Consider reducing the number of data sources for better performance');
            }
            if (report.performanceConfig.optimization.dataLimit > 10000) {
                recommendations.performance.push('Consider reducing data limit or implementing pagination');
            }
            // Security recommendations
            if (report.accessControl.visibility === 'public' &&
                report.metadata.compliance.dataClassification !== 'public') {
                recommendations.security.push('Review public visibility for non-public data classification');
            }
            if (!report.metadata.compliance.auditRequired &&
                report.metadata.compliance.frameworks.length > 0) {
                recommendations.security.push('Consider enabling audit requirements for compliance frameworks');
            }
            // Usability recommendations
            if (!report.visualizationConfig.interactivity.export) {
                recommendations.usability.push('Enable export functionality for better user experience');
            }
            if (report.visualizationConfig.series.length > 5) {
                recommendations.usability.push('Consider reducing the number of data series for clarity');
            }
            // Compliance recommendations
            if (report.metadata.compliance.frameworks.length > 0 &&
                report.metadata.compliance.retentionPeriod < 2555) {
                recommendations.compliance.push('Consider extending retention period for compliance frameworks');
            }
            return recommendations;
        }
        catch (error) {
            this.logger.error('Failed to get optimization recommendations', {
                error: error.message,
                reportId: id,
                userId,
            });
            throw error;
        }
    }
    // Private helper methods
    async validateReportNameUniqueness(name, userId, excludeId) {
        const existingReport = await this.reportDefinitionRepository.findOne({
            where: {
                name,
                ownerId: userId,
                isActive: true,
                ...(excludeId && { id: Not(excludeId) }),
            },
        });
        if (existingReport) {
            throw new common_1.BadRequestException(`Report with name "${name}" already exists`);
        }
    }
    validateReportConfiguration(config) {
        const errors = [];
        // Validate data sources
        if (!config.dataSourceConfig?.sources || config.dataSourceConfig.sources.length === 0) {
            errors.push('At least one data source must be configured');
        }
        // Validate visualization
        if (!config.visualizationConfig?.chartType) {
            errors.push('Chart type must be specified');
        }
        if (!config.visualizationConfig?.series || config.visualizationConfig.series.length === 0) {
            errors.push('At least one data series must be configured');
        }
        // Validate export configuration
        if (!config.exportConfig?.formats || config.exportConfig.formats.length === 0) {
            errors.push('At least one export format must be specified');
        }
        // Validate access control
        if (config.accessControl?.visibility === 'shared' &&
            (!config.accessControl.allowedUsers?.length && !config.accessControl.allowedRoles?.length)) {
            errors.push('Shared reports must specify allowed users or roles');
        }
        return {
            isValid: errors.length === 0,
            errors,
        };
    }
    async cacheReportDefinition(report) {
        const cacheKey = `report_definition:${report.id}`;
        await this.cacheService.set(cacheKey, report, { ttl: 3600 }); // 1 hour
    }
    async getCachedReportDefinition(id) {
        const cacheKey = `report_definition:${id}`;
        return this.cacheService.get(cacheKey);
    }
    async removeCachedReportDefinition(id) {
        const cacheKey = `report_definition:${id}`;
        await this.cacheService.del(cacheKey);
    }
    incrementVersion(currentVersion) {
        const parts = currentVersion.split('.');
        const patch = parseInt(parts[2] || '0') + 1;
        return `${parts[0]}.${parts[1]}.${patch}`;
    }
    generateChangeDescription(oldReport, updateDto) {
        const changes = [];
        if (updateDto.name && updateDto.name !== oldReport.name) {
            changes.push(`Name changed from "${oldReport.name}" to "${updateDto.name}"`);
        }
        if (updateDto.description !== undefined && updateDto.description !== oldReport.description) {
            changes.push('Description updated');
        }
        if (updateDto.dataSourceConfig) {
            changes.push('Data source configuration updated');
        }
        if (updateDto.visualizationConfig) {
            changes.push('Visualization configuration updated');
        }
        if (updateDto.exportConfig) {
            changes.push('Export configuration updated');
        }
        if (updateDto.accessControl) {
            changes.push('Access control updated');
        }
        if (updateDto.schedulingConfig) {
            changes.push('Scheduling configuration updated');
        }
        if (updateDto.performanceConfig) {
            changes.push('Performance configuration updated');
        }
        return changes.length > 0 ? changes : ['Minor updates'];
    }
    extractAuditableFields(report) {
        return {
            name: report.name,
            reportType: report.reportType,
            category: report.category,
            priority: report.priority,
            isActive: report.isActive,
            isTemplate: report.isTemplate,
            visibility: report.accessControl.visibility,
            dataClassification: report.metadata.compliance.dataClassification,
        };
    }
};
exports.ReportDefinitionService = ReportDefinitionService;
exports.ReportDefinitionService = ReportDefinitionService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(report_definition_entity_1.ReportDefinition)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof event_emitter_1.EventEmitter2 !== "undefined" && event_emitter_1.EventEmitter2) === "function" ? _b : Object, typeof (_c = typeof distributed_cache_service_1.DistributedCacheService !== "undefined" && distributed_cache_service_1.DistributedCacheService) === "function" ? _c : Object, typeof (_d = typeof audit_log_service_1.AuditLogService !== "undefined" && audit_log_service_1.AuditLogService) === "function" ? _d : Object])
], ReportDefinitionService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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