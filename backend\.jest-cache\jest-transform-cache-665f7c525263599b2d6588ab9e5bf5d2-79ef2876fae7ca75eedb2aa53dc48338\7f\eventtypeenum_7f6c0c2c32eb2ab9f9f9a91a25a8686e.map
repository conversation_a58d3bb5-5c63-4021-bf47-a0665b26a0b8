{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\event-type.enum.ts", "mappings": ";;;AAAA,IAAY,SAiEX;AAjED,WAAY,SAAS;IACnB,wBAAwB;IACxB,4CAA+B,CAAA;IAC/B,4CAA+B,CAAA;IAC/B,8BAAiB,CAAA;IACjB,gDAAmC,CAAA;IACnC,wCAA2B,CAAA;IAC3B,0CAA6B,CAAA;IAC7B,8CAAiC,CAAA;IACjC,kDAAqC,CAAA;IAErC,uBAAuB;IACvB,sDAAyC,CAAA;IACzC,oDAAuC,CAAA;IACvC,4CAA+B,CAAA;IAC/B,0CAA6B,CAAA;IAC7B,4CAA+B,CAAA;IAE/B,iBAAiB;IACjB,8DAAiD,CAAA;IACjD,oDAAuC,CAAA;IACvC,4DAA+C,CAAA;IAC/C,sDAAyC,CAAA;IACzC,wCAA2B,CAAA;IAE3B,uBAAuB;IACvB,8DAAiD,CAAA;IACjD,wDAA2C,CAAA;IAC3C,gEAAmD,CAAA;IACnD,4CAA+B,CAAA;IAC/B,0CAA6B,CAAA;IAE7B,iBAAiB;IACjB,kDAAqC,CAAA;IACrC,wDAA2C,CAAA;IAC3C,gDAAmC,CAAA;IACnC,gDAAmC,CAAA;IAEnC,cAAc;IACd,wCAA2B,CAAA;IAC3B,oDAAuC,CAAA;IACvC,4CAA+B,CAAA;IAC/B,wCAA2B,CAAA;IAC3B,wCAA2B,CAAA;IAE3B,gBAAgB;IAChB,8CAAiC,CAAA;IACjC,gDAAmC,CAAA;IACnC,gDAAmC,CAAA;IACnC,gDAAmC,CAAA;IACnC,0DAA6C,CAAA;IAE7C,gBAAgB;IAChB,gDAAmC,CAAA;IACnC,8CAAiC,CAAA;IACjC,kDAAqC,CAAA;IACrC,0CAA6B,CAAA;IAE7B,oBAAoB;IACpB,0DAA6C,CAAA;IAC7C,kDAAqC,CAAA;IACrC,oCAAuB,CAAA;IAEvB,gBAAgB;IAChB,8BAAiB,CAAA;AACnB,CAAC,EAjEW,SAAS,yBAAT,SAAS,QAiEpB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\enums\\event-type.enum.ts"], "sourcesContent": ["export enum EventType {\r\n  // Authentication events\r\n  LOGIN_SUCCESS = 'LOGIN_SUCCESS',\r\n  LOGIN_FAILURE = 'LOGIN_FAILURE',\r\n  LOGOUT = 'LOGOUT',\r\n  PASSWORD_CHANGE = 'PASSWORD_CHANGE',\r\n  MFA_ENABLED = 'MFA_ENABLED',\r\n  MFA_DISABLED = 'MFA_DISABLED',\r\n  ACCOUNT_LOCKED = 'ACCOUNT_LOCKED',\r\n  ACCOUNT_UNLOCKED = 'ACCOUNT_UNLOCKED',\r\n\r\n  // Authorization events\r\n  PERMISSION_GRANTED = 'PERMISSION_GRANTED',\r\n  PERMISSION_DENIED = 'PERMISSION_DENIED',\r\n  ROLE_ASSIGNED = 'ROLE_ASSIGNED',\r\n  ROLE_REMOVED = 'ROLE_REMOVED',\r\n  ACCESS_DENIED = 'ACCESS_DENIED',\r\n\r\n  // Network events\r\n  CONNECTION_ESTABLISHED = 'CONNECTION_ESTABLISHED',\r\n  CONNECTION_CLOSED = 'CONNECTION_CLOSED',\r\n  SUSPICIOUS_CONNECTION = 'SUSPICIOUS_CONNECTION',\r\n  PORT_SCAN_DETECTED = 'PORT_SCAN_DETECTED',\r\n  DDoS_ATTACK = 'DDOS_ATTACK',\r\n\r\n  // Vulnerability events\r\n  VULNERABILITY_DETECTED = 'VULNERABILITY_DETECTED',\r\n  VULNERABILITY_FIXED = 'VULNERABILITY_FIXED',\r\n  VULNERABILITY_EXPLOITED = 'VULNERABILITY_EXPLOITED',\r\n  PATCH_APPLIED = 'PATCH_APPLIED',\r\n  PATCH_FAILED = 'PATCH_FAILED',\r\n\r\n  // Malware events\r\n  MALWARE_DETECTED = 'MALWARE_DETECTED',\r\n  MALWARE_QUARANTINED = 'MALWARE_QUARANTINED',\r\n  MALWARE_REMOVED = 'MALWARE_REMOVED',\r\n  SUSPICIOUS_FILE = 'SUSPICIOUS_FILE',\r\n\r\n  // Data events\r\n  DATA_ACCESS = 'DATA_ACCESS',\r\n  DATA_MODIFICATION = 'DATA_MODIFICATION',\r\n  DATA_DELETION = 'DATA_DELETION',\r\n  DATA_EXPORT = 'DATA_EXPORT',\r\n  DATA_BREACH = 'DATA_BREACH',\r\n\r\n  // System events\r\n  SYSTEM_STARTUP = 'SYSTEM_STARTUP',\r\n  SYSTEM_SHUTDOWN = 'SYSTEM_SHUTDOWN',\r\n  SERVICE_STARTED = 'SERVICE_STARTED',\r\n  SERVICE_STOPPED = 'SERVICE_STOPPED',\r\n  CONFIGURATION_CHANGE = 'CONFIGURATION_CHANGE',\r\n\r\n  // Threat events\r\n  THREAT_DETECTED = 'THREAT_DETECTED',\r\n  THREAT_BLOCKED = 'THREAT_BLOCKED',\r\n  THREAT_MITIGATED = 'THREAT_MITIGATED',\r\n  IOC_DETECTED = 'IOC_DETECTED',\r\n\r\n  // Compliance events\r\n  COMPLIANCE_VIOLATION = 'COMPLIANCE_VIOLATION',\r\n  COMPLIANCE_CHECK = 'COMPLIANCE_CHECK',\r\n  AUDIT_LOG = 'AUDIT_LOG',\r\n\r\n  // Custom events\r\n  CUSTOM = 'CUSTOM',\r\n} "], "version": 3}