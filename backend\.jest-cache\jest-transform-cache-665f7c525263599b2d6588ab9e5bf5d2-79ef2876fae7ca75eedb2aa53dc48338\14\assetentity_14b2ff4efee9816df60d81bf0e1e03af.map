{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\asset-management\\domain\\entities\\asset.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCAYiB;AACjB,6DAAkD;AAClD,6EAAkE;AAClE,6EAAkE;AAClE,2EAAgE;AAEhE;;;GAGG;AAWI,IAAM,KAAK,GAAX,MAAM,KAAK;IAoWhB;;OAEG;IACH,IAAI,QAAQ;QACV,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO,KAAK,CAAC;QACjC,MAAM,cAAc,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAC5D,OAAO,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,WAAW,KAAK,UAAU,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,WAAW,KAAK,YAAY,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;QAC3D,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,IAAI,oBAAoB;QACtB,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO,QAAQ,CAAC;QACpC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;QACvD,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,EAAE,cAAc,IAAI,KAAK,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,IAAI,CAAC,IAAI,CAAC,eAAe;YAAE,OAAO,SAAS,CAAC;QAC5C,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,IAAI,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,GAAW;QAChB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,GAAW;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,MAA4E,EAAE,SAAiB;QAC1G,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,WAAmD,EAAE,SAAiB;QACnF,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,SAAc,EAAE,SAAiB;QAC/C,IAAI,CAAC,SAAS,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,GAAG,SAAS,EAAE,CAAC;QACrD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,GAAW,EAAE,KAAU;QACxC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC7B,CAAC;QACD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,GAAW;QAC5B,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC,GAAG,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,MAUb;QACC,aAAa;QACb,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACtD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,eAAe;QACf,IAAI,MAAM,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YAC9D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,oBAAoB;QACpB,IAAI,MAAM,CAAC,aAAa,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;YAC7E,OAAO,KAAK,CAAC;QACf,CAAC;QAED,oBAAoB;QACpB,IAAI,MAAM,CAAC,YAAY,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;YAC3E,OAAO,KAAK,CAAC;QACf,CAAC;QAED,iBAAiB;QACjB,IAAI,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnF,OAAO,KAAK,CAAC;QACf,CAAC;QAED,aAAa;QACb,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;YACrE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,cAAc;QACd,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC;YACvE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,sBAAsB;QACtB,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC;YACvE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,oBAAoB;QACpB,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACtB,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;YACnD,MAAM,cAAc,GAAG,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,WAAW,EAAE,CAAC;YAC3G,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;gBACzC,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,8BAA8B;QAC9B,MAAM,iBAAiB,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;QACvE,KAAK,IAAI,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAE7C,qBAAqB;QACrB,MAAM,kBAAkB,GAAG,EAAE,UAAU,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;QACnG,KAAK,IAAI,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAE9C,uBAAuB;QACvB,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,KAAK,IAAI,CAAC,CAAC;QAE/B,eAAe;QACf,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,KAAK,IAAI,CAAC,CAAC;QAE/B,iDAAiD;QACjD,IAAI,IAAI,CAAC,SAAS,GAAG,GAAG;YAAE,KAAK,IAAI,CAAC,CAAC;QAErC,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,kBAAkB,EAAE;YACpC,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE;YACxB,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACrC,CAAC;IACJ,CAAC;CACF,CAAA;AApmBY,sBAAK;AAEhB;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;iCACpB;AAMX;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;mCACX;AAMb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;0CACpB;AAgCrB;IA3BC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE;YACJ,QAAQ;YACR,aAAa;YACb,QAAQ;YACR,eAAe;YACf,gBAAgB;YAChB,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,eAAe;YACf,gBAAgB;YAChB,UAAU;YACV,aAAa;YACb,aAAa;YACb,aAAa;YACb,gBAAgB;YAChB,WAAW;YACX,iBAAiB;YACjB,YAAY;YACZ,SAAS;YACT,SAAS;YACT,QAAQ;YACR,OAAO;SACR;KACF,CAAC;;mCACW;AAMb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sCAC5B;AAUjB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,aAAa,EAAE,gBAAgB,EAAE,SAAS,CAAC;QACxE,OAAO,EAAE,SAAS;KACnB,CAAC;;qCAC2E;AAU7E;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC;QAC3C,OAAO,EAAE,QAAQ;KAClB,CAAC;;0CACkD;AAUpD;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,aAAa,EAAE,SAAS,EAAE,SAAS,CAAC;QACpE,OAAO,EAAE,YAAY;KACtB,CAAC;;0CAC4E;AAM9E;IADC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uCACT;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;wCAC5B;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;0CACrD;AAMtB;IADC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uCACT;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;yCAC5B;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;8CAWxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uCA6BxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uCAsBxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4CAgBxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;0CAiBxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;wCAUxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;yCAcxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;wCAUxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;mCACtC;AAMf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAClD,MAAM,oBAAN,MAAM;+CAAc;AAMvC;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC;kDACtD,IAAI,oBAAJ,IAAI;2CAAC;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC;kDACtD,IAAI,oBAAJ,IAAI;uCAAC;AAMf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;wCAC3B;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;wCAC1C;AAGnB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;wCAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;wCAAC;AAKhB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,+BAAU,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACtE,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;kDACzB,+BAAU,oBAAV,+BAAU;oCAAC;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sCAC1C;AAGjB;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,+CAAkB,EAAE,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC;;6CACvB;AAGrC;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,+CAAkB,EAAE,aAAa,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC;;8CACpC;AAGtC;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,6CAAiB,EAAE,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,WAAW,CAAC;;kDACpC;AAGzC;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,6CAAiB,EAAE,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,WAAW,CAAC;;kDACpC;AAQzC;IANC,IAAA,oBAAU,EAAC,GAAG,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC;IACrD,IAAA,mBAAS,EAAC;QACT,IAAI,EAAE,oBAAoB;QAC1B,UAAU,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,oBAAoB,EAAE,IAAI,EAAE;QAC5D,iBAAiB,EAAE,EAAE,IAAI,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,IAAI,EAAE;KAC9E,CAAC;;2CACoB;AAGtB;IADC,IAAA,oBAAU,EAAC,GAAG,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC;;4CAC9B;gBAlWZ,KAAK;IAVjB,IAAA,gBAAM,EAAC,QAAQ,CAAC;IAChB,IAAA,eAAK,EAAC,CAAC,MAAM,CAAC,CAAC;IACf,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,aAAa,CAAC,CAAC;IACtB,IAAA,eAAK,EAAC,CAAC,aAAa,CAAC,CAAC;IACtB,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,WAAW,CAAC,CAAC;IACpB,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,cAAc,CAAC,CAAC;GACX,KAAK,CAomBjB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\asset-management\\domain\\entities\\asset.entity.ts"], "sourcesContent": ["import {\r\n  <PERSON>tity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  OneToMany,\r\n  ManyToOne,\r\n  JoinColumn,\r\n  ManyToMany,\r\n  JoinTable,\r\n} from 'typeorm';\r\nimport { AssetGroup } from './asset-group.entity';\r\nimport { AssetConfiguration } from './asset-configuration.entity';\r\nimport { AssetVulnerability } from './asset-vulnerability.entity';\r\nimport { AssetRelationship } from './asset-relationship.entity';\r\n\r\n/**\r\n * Asset entity\r\n * Represents physical and logical assets in the organization's infrastructure\r\n */\r\n@Entity('assets')\r\n@Index(['type'])\r\n@Index(['status'])\r\n@Index(['criticality'])\r\n@Index(['environment'])\r\n@Index(['location'])\r\n@Index(['ipAddress'])\r\n@Index(['hostname'])\r\n@Index(['lastSeen'])\r\n@Index(['discoveredAt'])\r\nexport class Asset {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * Asset name\r\n   */\r\n  @Column({ length: 255 })\r\n  name: string;\r\n\r\n  /**\r\n   * Asset description\r\n   */\r\n  @Column({ type: 'text', nullable: true })\r\n  description?: string;\r\n\r\n  /**\r\n   * Asset type\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: [\r\n      'server',\r\n      'workstation',\r\n      'laptop',\r\n      'mobile_device',\r\n      'network_device',\r\n      'router',\r\n      'switch',\r\n      'firewall',\r\n      'load_balancer',\r\n      'storage_device',\r\n      'database',\r\n      'application',\r\n      'web_service',\r\n      'api_service',\r\n      'cloud_instance',\r\n      'container',\r\n      'virtual_machine',\r\n      'iot_device',\r\n      'printer',\r\n      'scanner',\r\n      'camera',\r\n      'other',\r\n    ],\r\n  })\r\n  type: string;\r\n\r\n  /**\r\n   * Asset subtype for more specific classification\r\n   */\r\n  @Column({ name: 'sub_type', nullable: true })\r\n  subType?: string;\r\n\r\n  /**\r\n   * Asset status\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['active', 'inactive', 'maintenance', 'decommissioned', 'unknown'],\r\n    default: 'unknown',\r\n  })\r\n  status: 'active' | 'inactive' | 'maintenance' | 'decommissioned' | 'unknown';\r\n\r\n  /**\r\n   * Asset criticality level\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['low', 'medium', 'high', 'critical'],\r\n    default: 'medium',\r\n  })\r\n  criticality: 'low' | 'medium' | 'high' | 'critical';\r\n\r\n  /**\r\n   * Environment where asset is deployed\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['production', 'staging', 'development', 'testing', 'sandbox'],\r\n    default: 'production',\r\n  })\r\n  environment: 'production' | 'staging' | 'development' | 'testing' | 'sandbox';\r\n\r\n  /**\r\n   * Physical or logical location\r\n   */\r\n  @Column({ nullable: true })\r\n  location?: string;\r\n\r\n  /**\r\n   * Primary IP address\r\n   */\r\n  @Column({ name: 'ip_address', nullable: true })\r\n  ipAddress?: string;\r\n\r\n  /**\r\n   * Additional IP addresses\r\n   */\r\n  @Column({ name: 'ip_addresses', type: 'text', array: true, default: '{}' })\r\n  ipAddresses: string[];\r\n\r\n  /**\r\n   * Hostname or FQDN\r\n   */\r\n  @Column({ nullable: true })\r\n  hostname?: string;\r\n\r\n  /**\r\n   * MAC address\r\n   */\r\n  @Column({ name: 'mac_address', nullable: true })\r\n  macAddress?: string;\r\n\r\n  /**\r\n   * Operating system information\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  operatingSystem?: {\r\n    name: string;\r\n    version: string;\r\n    architecture: string;\r\n    kernel?: string;\r\n    distribution?: string;\r\n    servicePackLevel?: string;\r\n    buildNumber?: string;\r\n    installDate?: string;\r\n    lastBootTime?: string;\r\n  };\r\n\r\n  /**\r\n   * Hardware information\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  hardware?: {\r\n    manufacturer?: string;\r\n    model?: string;\r\n    serialNumber?: string;\r\n    assetTag?: string;\r\n    cpu?: {\r\n      model: string;\r\n      cores: number;\r\n      threads: number;\r\n      speed: string;\r\n    };\r\n    memory?: {\r\n      total: number; // in GB\r\n      available: number;\r\n      type: string;\r\n    };\r\n    storage?: Array<{\r\n      type: 'hdd' | 'ssd' | 'nvme';\r\n      size: number; // in GB\r\n      model?: string;\r\n      serialNumber?: string;\r\n    }>;\r\n    network?: Array<{\r\n      interface: string;\r\n      type: string;\r\n      speed: string;\r\n      macAddress?: string;\r\n    }>;\r\n  };\r\n\r\n  /**\r\n   * Software and services information\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  software?: {\r\n    installedSoftware?: Array<{\r\n      name: string;\r\n      version: string;\r\n      vendor?: string;\r\n      installDate?: string;\r\n      licenseKey?: string;\r\n    }>;\r\n    runningServices?: Array<{\r\n      name: string;\r\n      status: 'running' | 'stopped' | 'disabled';\r\n      port?: number;\r\n      protocol?: string;\r\n      version?: string;\r\n    }>;\r\n    openPorts?: Array<{\r\n      port: number;\r\n      protocol: 'tcp' | 'udp';\r\n      service?: string;\r\n      state: 'open' | 'closed' | 'filtered';\r\n    }>;\r\n  };\r\n\r\n  /**\r\n   * Cloud-specific information\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  cloudMetadata?: {\r\n    provider?: 'aws' | 'azure' | 'gcp' | 'other';\r\n    region?: string;\r\n    availabilityZone?: string;\r\n    instanceId?: string;\r\n    instanceType?: string;\r\n    imageId?: string;\r\n    vpcId?: string;\r\n    subnetId?: string;\r\n    securityGroups?: string[];\r\n    tags?: Record<string, string>;\r\n    publicIp?: string;\r\n    privateIp?: string;\r\n    iamRole?: string;\r\n    launchTime?: string;\r\n  };\r\n\r\n  /**\r\n   * Network information\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  networkInfo?: {\r\n    domain?: string;\r\n    workgroup?: string;\r\n    dnsServers?: string[];\r\n    defaultGateway?: string;\r\n    subnetMask?: string;\r\n    vlan?: number;\r\n    networkSegment?: string;\r\n    firewallRules?: Array<{\r\n      direction: 'inbound' | 'outbound';\r\n      protocol: string;\r\n      port: string;\r\n      source: string;\r\n      destination: string;\r\n      action: 'allow' | 'deny';\r\n    }>;\r\n  };\r\n\r\n  /**\r\n   * Asset ownership and responsibility\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  ownership?: {\r\n    owner?: string; // User ID\r\n    technicalContact?: string; // User ID\r\n    businessContact?: string; // User ID\r\n    department?: string;\r\n    costCenter?: string;\r\n    project?: string;\r\n    businessFunction?: string;\r\n    dataClassification?: 'public' | 'internal' | 'confidential' | 'restricted';\r\n  };\r\n\r\n  /**\r\n   * Compliance and security information\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  compliance?: {\r\n    frameworks?: string[]; // Compliance framework IDs\r\n    controls?: string[]; // Control IDs\r\n    complianceStatus?: 'compliant' | 'non_compliant' | 'partially_compliant' | 'unknown';\r\n    lastAssessment?: string; // ISO date\r\n    nextAssessment?: string; // ISO date\r\n    exemptions?: Array<{\r\n      framework: string;\r\n      control: string;\r\n      reason: string;\r\n      approvedBy: string;\r\n      expiryDate: string;\r\n    }>;\r\n  };\r\n\r\n  /**\r\n   * Discovery and monitoring information\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  discovery?: {\r\n    discoveryMethod?: 'network_scan' | 'agent' | 'cloud_api' | 'manual' | 'import';\r\n    discoverySource?: string;\r\n    lastScanDate?: string;\r\n    scanFrequency?: 'continuous' | 'daily' | 'weekly' | 'monthly';\r\n    monitoringEnabled?: boolean;\r\n    agentInstalled?: boolean;\r\n    agentVersion?: string;\r\n    agentLastContact?: string;\r\n  };\r\n\r\n  /**\r\n   * Asset tags for categorization\r\n   */\r\n  @Column({ type: 'text', array: true, default: '{}' })\r\n  tags: string[];\r\n\r\n  /**\r\n   * Custom attributes\r\n   */\r\n  @Column({ name: 'custom_attributes', type: 'jsonb', nullable: true })\r\n  customAttributes?: Record<string, any>;\r\n\r\n  /**\r\n   * When asset was first discovered\r\n   */\r\n  @Column({ name: 'discovered_at', type: 'timestamp with time zone' })\r\n  discoveredAt: Date;\r\n\r\n  /**\r\n   * When asset was last seen/detected\r\n   */\r\n  @Column({ name: 'last_seen', type: 'timestamp with time zone' })\r\n  lastSeen: Date;\r\n\r\n  /**\r\n   * User who created the asset record\r\n   */\r\n  @Column({ name: 'created_by', type: 'uuid' })\r\n  createdBy: string;\r\n\r\n  /**\r\n   * User who last updated the asset\r\n   */\r\n  @Column({ name: 'updated_by', type: 'uuid', nullable: true })\r\n  updatedBy?: string;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  // Relationships\r\n  @ManyToOne(() => AssetGroup, group => group.assets, { nullable: true })\r\n  @JoinColumn({ name: 'group_id' })\r\n  group?: AssetGroup;\r\n\r\n  @Column({ name: 'group_id', type: 'uuid', nullable: true })\r\n  groupId?: string;\r\n\r\n  @OneToMany(() => AssetConfiguration, config => config.asset)\r\n  configurations: AssetConfiguration[];\r\n\r\n  @OneToMany(() => AssetVulnerability, vulnerability => vulnerability.asset)\r\n  vulnerabilities: AssetVulnerability[];\r\n\r\n  @OneToMany(() => AssetRelationship, relationship => relationship.sourceAsset)\r\n  sourceRelationships: AssetRelationship[];\r\n\r\n  @OneToMany(() => AssetRelationship, relationship => relationship.targetAsset)\r\n  targetRelationships: AssetRelationship[];\r\n\r\n  @ManyToMany(() => Asset, asset => asset.relatedAssets)\r\n  @JoinTable({\r\n    name: 'asset_dependencies',\r\n    joinColumn: { name: 'asset_id', referencedColumnName: 'id' },\r\n    inverseJoinColumn: { name: 'dependent_asset_id', referencedColumnName: 'id' },\r\n  })\r\n  dependencies: Asset[];\r\n\r\n  @ManyToMany(() => Asset, asset => asset.dependencies)\r\n  relatedAssets: Asset[];\r\n\r\n  /**\r\n   * Check if asset is online/reachable\r\n   */\r\n  get isOnline(): boolean {\r\n    if (!this.lastSeen) return false;\r\n    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);\r\n    return this.lastSeen > fiveMinutesAgo;\r\n  }\r\n\r\n  /**\r\n   * Check if asset is critical\r\n   */\r\n  get isCritical(): boolean {\r\n    return this.criticality === 'critical';\r\n  }\r\n\r\n  /**\r\n   * Check if asset is in production\r\n   */\r\n  get isProduction(): boolean {\r\n    return this.environment === 'production';\r\n  }\r\n\r\n  /**\r\n   * Get asset age in days\r\n   */\r\n  get ageInDays(): number {\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - this.discoveredAt.getTime();\r\n    return Math.floor(diffMs / (1000 * 60 * 60 * 24));\r\n  }\r\n\r\n  /**\r\n   * Get time since last seen in minutes\r\n   */\r\n  get minutesSinceLastSeen(): number {\r\n    if (!this.lastSeen) return Infinity;\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - this.lastSeen.getTime();\r\n    return Math.floor(diffMs / (1000 * 60));\r\n  }\r\n\r\n  /**\r\n   * Check if asset has agent installed\r\n   */\r\n  get hasAgent(): boolean {\r\n    return this.discovery?.agentInstalled || false;\r\n  }\r\n\r\n  /**\r\n   * Get primary operating system\r\n   */\r\n  get primaryOS(): string {\r\n    if (!this.operatingSystem) return 'Unknown';\r\n    return `${this.operatingSystem.name} ${this.operatingSystem.version}`;\r\n  }\r\n\r\n  /**\r\n   * Update last seen timestamp\r\n   */\r\n  updateLastSeen(): void {\r\n    this.lastSeen = new Date();\r\n  }\r\n\r\n  /**\r\n   * Add tag to asset\r\n   */\r\n  addTag(tag: string): void {\r\n    if (!this.tags.includes(tag)) {\r\n      this.tags.push(tag);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove tag from asset\r\n   */\r\n  removeTag(tag: string): void {\r\n    this.tags = this.tags.filter(t => t !== tag);\r\n  }\r\n\r\n  /**\r\n   * Update asset status\r\n   */\r\n  updateStatus(status: 'active' | 'inactive' | 'maintenance' | 'decommissioned' | 'unknown', updatedBy: string): void {\r\n    this.status = status;\r\n    this.updatedBy = updatedBy;\r\n  }\r\n\r\n  /**\r\n   * Set asset criticality\r\n   */\r\n  setCriticality(criticality: 'low' | 'medium' | 'high' | 'critical', updatedBy: string): void {\r\n    this.criticality = criticality;\r\n    this.updatedBy = updatedBy;\r\n  }\r\n\r\n  /**\r\n   * Update ownership information\r\n   */\r\n  updateOwnership(ownership: any, updatedBy: string): void {\r\n    this.ownership = { ...this.ownership, ...ownership };\r\n    this.updatedBy = updatedBy;\r\n  }\r\n\r\n  /**\r\n   * Add custom attribute\r\n   */\r\n  setCustomAttribute(key: string, value: any): void {\r\n    if (!this.customAttributes) {\r\n      this.customAttributes = {};\r\n    }\r\n    this.customAttributes[key] = value;\r\n  }\r\n\r\n  /**\r\n   * Get custom attribute\r\n   */\r\n  getCustomAttribute(key: string): any {\r\n    return this.customAttributes?.[key];\r\n  }\r\n\r\n  /**\r\n   * Check if asset matches filter criteria\r\n   */\r\n  matchesFilter(filter: {\r\n    types?: string[];\r\n    statuses?: string[];\r\n    criticalities?: string[];\r\n    environments?: string[];\r\n    locations?: string[];\r\n    tags?: string[];\r\n    hasAgent?: boolean;\r\n    isOnline?: boolean;\r\n    searchText?: string;\r\n  }): boolean {\r\n    // Check type\r\n    if (filter.types && !filter.types.includes(this.type)) {\r\n      return false;\r\n    }\r\n\r\n    // Check status\r\n    if (filter.statuses && !filter.statuses.includes(this.status)) {\r\n      return false;\r\n    }\r\n\r\n    // Check criticality\r\n    if (filter.criticalities && !filter.criticalities.includes(this.criticality)) {\r\n      return false;\r\n    }\r\n\r\n    // Check environment\r\n    if (filter.environments && !filter.environments.includes(this.environment)) {\r\n      return false;\r\n    }\r\n\r\n    // Check location\r\n    if (filter.locations && this.location && !filter.locations.includes(this.location)) {\r\n      return false;\r\n    }\r\n\r\n    // Check tags\r\n    if (filter.tags && !filter.tags.some(tag => this.tags.includes(tag))) {\r\n      return false;\r\n    }\r\n\r\n    // Check agent\r\n    if (filter.hasAgent !== undefined && this.hasAgent !== filter.hasAgent) {\r\n      return false;\r\n    }\r\n\r\n    // Check online status\r\n    if (filter.isOnline !== undefined && this.isOnline !== filter.isOnline) {\r\n      return false;\r\n    }\r\n\r\n    // Check search text\r\n    if (filter.searchText) {\r\n      const searchText = filter.searchText.toLowerCase();\r\n      const searchableText = `${this.name} ${this.hostname} ${this.ipAddress} ${this.description}`.toLowerCase();\r\n      if (!searchableText.includes(searchText)) {\r\n        return false;\r\n      }\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Calculate risk score based on various factors\r\n   */\r\n  calculateRiskScore(): number {\r\n    let score = 0;\r\n\r\n    // Base score from criticality\r\n    const criticalityScores = { low: 2, medium: 5, high: 8, critical: 10 };\r\n    score += criticalityScores[this.criticality];\r\n\r\n    // Environment factor\r\n    const environmentFactors = { production: 3, staging: 2, development: 1, testing: 1, sandbox: 0.5 };\r\n    score += environmentFactors[this.environment];\r\n\r\n    // Online status factor\r\n    if (!this.isOnline) score += 2;\r\n\r\n    // Agent factor\r\n    if (!this.hasAgent) score += 1;\r\n\r\n    // Age factor (older assets might be less secure)\r\n    if (this.ageInDays > 365) score += 1;\r\n\r\n    return Math.min(10, score);\r\n  }\r\n\r\n  /**\r\n   * Get asset summary for display\r\n   */\r\n  getSummary(): any {\r\n    return {\r\n      id: this.id,\r\n      name: this.name,\r\n      type: this.type,\r\n      status: this.status,\r\n      criticality: this.criticality,\r\n      environment: this.environment,\r\n      ipAddress: this.ipAddress,\r\n      hostname: this.hostname,\r\n      location: this.location,\r\n      isOnline: this.isOnline,\r\n      isCritical: this.isCritical,\r\n      hasAgent: this.hasAgent,\r\n      primaryOS: this.primaryOS,\r\n      ageInDays: this.ageInDays,\r\n      riskScore: this.calculateRiskScore(),\r\n      lastSeen: this.lastSeen,\r\n      tags: this.tags,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Export asset for reporting\r\n   */\r\n  exportForReporting(): any {\r\n    return {\r\n      asset: this.getSummary(),\r\n      operatingSystem: this.operatingSystem,\r\n      hardware: this.hardware,\r\n      software: this.software,\r\n      cloudMetadata: this.cloudMetadata,\r\n      networkInfo: this.networkInfo,\r\n      ownership: this.ownership,\r\n      compliance: this.compliance,\r\n      discovery: this.discovery,\r\n      customAttributes: this.customAttributes,\r\n      exportedAt: new Date().toISOString(),\r\n    };\r\n  }\r\n}\r\n"], "version": 3}