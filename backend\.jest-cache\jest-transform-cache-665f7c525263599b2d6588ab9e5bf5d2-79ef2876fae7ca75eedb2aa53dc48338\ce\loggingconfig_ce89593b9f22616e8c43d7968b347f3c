bd6e0a4cef9398289ada69e281e39491
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createWinstonFormat = exports.loggingConfig = exports.AggregationType = exports.HttpLogFormat = exports.LogFormat = exports.LogLevel = void 0;
const config_1 = require("@nestjs/config");
const winston = __importStar(require("winston"));
// Enums for better type safety and configuration
var LogLevel;
(function (LogLevel) {
    LogLevel["ERROR"] = "error";
    LogLevel["WARN"] = "warn";
    LogLevel["INFO"] = "info";
    LogLevel["DEBUG"] = "debug";
})(LogLevel || (exports.LogLevel = LogLevel = {}));
var LogFormat;
(function (LogFormat) {
    LogFormat["JSON"] = "json";
    LogFormat["SIMPLE"] = "simple";
    LogFormat["COMBINED"] = "combined";
})(LogFormat || (exports.LogFormat = LogFormat = {}));
var HttpLogFormat;
(function (HttpLogFormat) {
    HttpLogFormat["COMBINED"] = "combined";
    HttpLogFormat["COMMON"] = "common";
    HttpLogFormat["DEV"] = "dev";
    HttpLogFormat["SHORT"] = "short";
    HttpLogFormat["TINY"] = "tiny";
})(HttpLogFormat || (exports.HttpLogFormat = HttpLogFormat = {}));
var AggregationType;
(function (AggregationType) {
    AggregationType["ELASTICSEARCH"] = "elasticsearch";
    AggregationType["SPLUNK"] = "splunk";
    AggregationType["DATADOG"] = "datadog";
})(AggregationType || (exports.AggregationType = AggregationType = {}));
// Utility functions for cleaner configuration parsing
const parseBoolean = (value, defaultValue) => value !== undefined ? value === 'true' : defaultValue;
const parseInteger = (value, defaultValue) => value ? parseInt(value, 10) || defaultValue : defaultValue;
const parseEnum = (value, enumObject, defaultValue) => {
    if (!value)
        return defaultValue;
    const enumValues = Object.values(enumObject);
    return enumValues.includes(value) ? value : defaultValue;
};
const parseStringArray = (value, defaultValue) => value?.split(',').map(item => item.trim()) ?? defaultValue;
// Environment variable helpers
const getEnvVar = (key) => process.env[key];
const isProduction = () => getEnvVar('NODE_ENV') === 'production';
// Skip function for HTTP logging - fixed type issue
const createHttpSkipFunction = (skipSuccessful) => {
    if (!skipSuccessful)
        return undefined;
    return (_req, res) => res.statusCode < 400;
};
// Default values constants
const DEFAULT_LOG_MASKING_FIELDS = ['password', 'token', 'secret', 'key', 'authorization'];
const DEFAULT_FILE_MAX_SIZE = '10m';
const DEFAULT_FILE_MAX_FILES = 5;
const DEFAULT_AUDIT_MAX_SIZE = '50m';
const DEFAULT_AUDIT_MAX_FILES = 10;
const DEFAULT_PERFORMANCE_MAX_SIZE = '20m';
const DEFAULT_PERFORMANCE_MAX_FILES = 3;
const DEFAULT_PERFORMANCE_THRESHOLD = 1000;
const DEFAULT_SLOW_QUERY_THRESHOLD = 1000;
const DEFAULT_CORRELATION_HEADER = 'x-correlation-id';
const DEFAULT_AGGREGATION_INDEX = 'sentinel-logs';
const DEFAULT_AGGREGATION_BATCH_SIZE = 100;
const DEFAULT_AGGREGATION_FLUSH_INTERVAL = 5000;
const DEFAULT_MASKING_REPLACEMENT = '[REDACTED]';
const DEFAULT_RETENTION_DAYS = 30;
const DEFAULT_ARCHIVE_LOCATION = 'logs/archive';
/**
 * Optimized logging configuration with comprehensive type safety
 * Provides structured logging setup for development and production environments
 */
exports.loggingConfig = (0, config_1.registerAs)('logging', () => {
    const isProd = isProduction();
    const defaultLevel = parseEnum(getEnvVar('LOG_LEVEL'), LogLevel, LogLevel.INFO);
    return {
        // Core settings
        level: defaultLevel,
        format: parseEnum(getEnvVar('LOG_FORMAT'), LogFormat, LogFormat.JSON),
        silent: parseBoolean(getEnvVar('LOG_SILENT'), false),
        // Console logging
        console: {
            enabled: parseBoolean(getEnvVar('LOG_CONSOLE_ENABLED'), true),
            level: parseEnum(getEnvVar('LOG_CONSOLE_LEVEL'), LogLevel, defaultLevel),
            colorize: !isProd,
            timestamp: true,
            prettyPrint: !isProd,
        },
        // File logging
        file: {
            enabled: parseBoolean(getEnvVar('LOG_FILE_ENABLED'), false),
            level: parseEnum(getEnvVar('LOG_FILE_LEVEL'), LogLevel, LogLevel.INFO),
            filename: getEnvVar('LOG_FILE_PATH') || 'logs/app.log',
            maxSize: getEnvVar('LOG_FILE_MAX_SIZE') || DEFAULT_FILE_MAX_SIZE,
            maxFiles: parseInteger(getEnvVar('LOG_FILE_MAX_FILES'), DEFAULT_FILE_MAX_FILES),
            tailable: true,
            zippedArchive: true,
        },
        // Error file logging
        errorFile: {
            enabled: parseBoolean(getEnvVar('LOG_ERROR_FILE_ENABLED'), false),
            level: LogLevel.ERROR,
            filename: getEnvVar('LOG_ERROR_FILE_PATH') || 'logs/error.log',
            maxSize: getEnvVar('LOG_ERROR_FILE_MAX_SIZE') || DEFAULT_FILE_MAX_SIZE,
            maxFiles: parseInteger(getEnvVar('LOG_ERROR_FILE_MAX_FILES'), DEFAULT_FILE_MAX_FILES),
            tailable: true,
            zippedArchive: true,
        },
        // Audit logging
        audit: {
            enabled: parseBoolean(getEnvVar('LOG_AUDIT_ENABLED'), false),
            level: LogLevel.INFO,
            filename: getEnvVar('LOG_AUDIT_PATH') || 'logs/audit.log',
            maxSize: getEnvVar('LOG_AUDIT_MAX_SIZE') || DEFAULT_AUDIT_MAX_SIZE,
            maxFiles: parseInteger(getEnvVar('LOG_AUDIT_MAX_FILES'), DEFAULT_AUDIT_MAX_FILES),
            tailable: true,
            zippedArchive: true,
            format: LogFormat.JSON,
        },
        // Performance logging
        performance: {
            enabled: parseBoolean(getEnvVar('LOG_PERFORMANCE_ENABLED'), false),
            level: LogLevel.DEBUG,
            filename: getEnvVar('LOG_PERFORMANCE_PATH') || 'logs/performance.log',
            maxSize: getEnvVar('LOG_PERFORMANCE_MAX_SIZE') || DEFAULT_PERFORMANCE_MAX_SIZE,
            maxFiles: parseInteger(getEnvVar('LOG_PERFORMANCE_MAX_FILES'), DEFAULT_PERFORMANCE_MAX_FILES),
            threshold: parseInteger(getEnvVar('LOG_PERFORMANCE_THRESHOLD'), DEFAULT_PERFORMANCE_THRESHOLD),
        },
        // HTTP request logging - Fixed the type issue
        http: {
            enabled: parseBoolean(getEnvVar('LOG_HTTP_ENABLED'), false),
            level: parseEnum(getEnvVar('LOG_HTTP_LEVEL'), LogLevel, LogLevel.INFO),
            format: parseEnum(getEnvVar('LOG_HTTP_FORMAT'), HttpLogFormat, HttpLogFormat.COMBINED),
            ...(parseBoolean(getEnvVar('LOG_HTTP_SKIP_SUCCESSFUL'), false) && {
                skip: createHttpSkipFunction(true)
            }),
        },
        // Database query logging
        database: {
            enabled: parseBoolean(getEnvVar('LOG_DATABASE_ENABLED'), false),
            level: LogLevel.DEBUG,
            logQueries: parseBoolean(getEnvVar('LOG_DATABASE_QUERIES'), false),
            logErrors: parseBoolean(getEnvVar('LOG_DATABASE_ERRORS'), true),
            logSlowQueries: parseBoolean(getEnvVar('LOG_DATABASE_SLOW_QUERIES'), false),
            slowQueryThreshold: parseInteger(getEnvVar('LOG_DATABASE_SLOW_THRESHOLD'), DEFAULT_SLOW_QUERY_THRESHOLD),
        },
        // External service logging
        external: {
            enabled: parseBoolean(getEnvVar('LOG_EXTERNAL_ENABLED'), false),
            level: LogLevel.INFO,
            logRequests: parseBoolean(getEnvVar('LOG_EXTERNAL_REQUESTS'), false),
            logResponses: parseBoolean(getEnvVar('LOG_EXTERNAL_RESPONSES'), false),
            logErrors: parseBoolean(getEnvVar('LOG_EXTERNAL_ERRORS'), true),
            maskSensitiveData: parseBoolean(getEnvVar('LOG_MASK_SENSITIVE'), true),
        },
        // Correlation ID settings
        correlation: {
            enabled: parseBoolean(getEnvVar('LOG_CORRELATION_ENABLED'), true),
            headerName: getEnvVar('LOG_CORRELATION_HEADER') || DEFAULT_CORRELATION_HEADER,
            generateIfMissing: parseBoolean(getEnvVar('LOG_CORRELATION_GENERATE'), true),
        },
        // Log aggregation
        aggregation: {
            enabled: parseBoolean(getEnvVar('LOG_AGGREGATION_ENABLED'), false),
            type: parseEnum(getEnvVar('LOG_AGGREGATION_TYPE'), AggregationType, AggregationType.ELASTICSEARCH),
            ...(getEnvVar('LOG_AGGREGATION_ENDPOINT') && { endpoint: getEnvVar('LOG_AGGREGATION_ENDPOINT') }),
            ...(getEnvVar('LOG_AGGREGATION_API_KEY') && { apiKey: getEnvVar('LOG_AGGREGATION_API_KEY') }),
            index: getEnvVar('LOG_AGGREGATION_INDEX') || DEFAULT_AGGREGATION_INDEX,
            batchSize: parseInteger(getEnvVar('LOG_AGGREGATION_BATCH_SIZE'), DEFAULT_AGGREGATION_BATCH_SIZE),
            flushInterval: parseInteger(getEnvVar('LOG_AGGREGATION_FLUSH_INTERVAL'), DEFAULT_AGGREGATION_FLUSH_INTERVAL),
        },
        // Sensitive data masking
        masking: {
            enabled: parseBoolean(getEnvVar('LOG_MASKING_ENABLED'), true),
            fields: parseStringArray(getEnvVar('LOG_MASKING_FIELDS'), DEFAULT_LOG_MASKING_FIELDS),
            replacement: getEnvVar('LOG_MASKING_REPLACEMENT') || DEFAULT_MASKING_REPLACEMENT,
            preserveLength: parseBoolean(getEnvVar('LOG_MASKING_PRESERVE_LENGTH'), false),
        },
        // Log retention
        retention: {
            enabled: parseBoolean(getEnvVar('LOG_RETENTION_ENABLED'), false),
            days: parseInteger(getEnvVar('LOG_RETENTION_DAYS'), DEFAULT_RETENTION_DAYS),
            compressionEnabled: parseBoolean(getEnvVar('LOG_RETENTION_COMPRESSION'), true),
            archiveLocation: getEnvVar('LOG_RETENTION_ARCHIVE_LOCATION') || DEFAULT_ARCHIVE_LOCATION,
        },
    };
});
/**
 * Creates Winston format configuration with type safety
 */
const createWinstonFormat = (config) => {
    const formats = [
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
    ];
    // Add correlation ID format
    if (config.correlation.enabled) {
        formats.push(winston.format((info) => {
            const correlationId = info['correlationId'];
            return correlationId ? { ...info, correlationId } : info;
        })());
    }
    // Add data masking format
    if (config.masking.enabled) {
        formats.push(winston.format((info) => {
            const masked = { ...info };
            config.masking.fields.forEach((field) => {
                if (masked[field]) {
                    masked[field] = config.masking.replacement;
                }
            });
            return masked;
        })());
    }
    // Add final output format
    formats.push(config.format === LogFormat.JSON
        ? winston.format.json()
        : winston.format.simple());
    return winston.format.combine(...formats);
};
exports.createWinstonFormat = createWinstonFormat;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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