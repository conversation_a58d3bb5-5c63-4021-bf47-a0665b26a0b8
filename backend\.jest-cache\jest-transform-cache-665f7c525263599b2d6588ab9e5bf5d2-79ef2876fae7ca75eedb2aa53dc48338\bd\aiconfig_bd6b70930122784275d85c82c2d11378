ae1e9b16f8ff66d780c96e39b396fa2d
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AI_ENV_KEYS = exports.AI_CONFIG_DEFAULTS = exports.AIConfigDto = exports.AIMonitoringConfigDto = exports.AIHealthCheckConfigDto = exports.AIQueueConfigDto = exports.AIModelsConfigDto = exports.AIModelConfigDto = exports.AIRateLimitConfigDto = exports.AICacheConfigDto = exports.AICircuitBreakerConfigDto = exports.AIRetryConfigDto = exports.AIServiceConfigDto = exports.aiConfig = void 0;
const config_1 = require("@nestjs/config");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
// Optimized environment parsing utilities with enhanced type safety
const parseEnv = {
    boolean: (value, fallback = false) => value?.toLowerCase() === 'true' || fallback,
    number: (value, fallback, radix = 10) => {
        if (!value)
            return fallback;
        const parsed = parseInt(value, radix);
        return Number.isNaN(parsed) ? fallback : parsed;
    },
    float: (value, fallback) => {
        if (!value)
            return fallback;
        const parsed = Number.parseFloat(value);
        return Number.isNaN(parsed) ? fallback : parsed;
    },
    string: (value, fallback) => value ?? fallback,
};
// Configuration factory with optimized environment parsing
const createAIConfig = () => {
    const env = process.env;
    const service = {
        enabled: parseEnv.boolean(env['AI_SERVICE_ENABLED']),
        url: parseEnv.string(env['AI_SERVICE_URL'], 'http://localhost:8000'),
        apiKey: parseEnv.string(env['AI_SERVICE_API_KEY'], 'your-ai-service-api-key'),
        timeout: parseEnv.number(env['AI_SERVICE_TIMEOUT'], 30000),
        version: parseEnv.string(env['AI_SERVICE_VERSION'], 'v1'),
    };
    const retry = {
        attempts: parseEnv.number(env['AI_SERVICE_RETRY_ATTEMPTS'], 3),
        delay: parseEnv.number(env['AI_SERVICE_RETRY_DELAY'], 1000),
        backoffMultiplier: parseEnv.float(env['AI_SERVICE_BACKOFF_MULTIPLIER'], 2),
        maxDelay: parseEnv.number(env['AI_SERVICE_MAX_DELAY'], 10000),
    };
    const circuitBreaker = {
        enabled: parseEnv.boolean(env['AI_SERVICE_CIRCUIT_BREAKER_ENABLED']),
        threshold: parseEnv.number(env['AI_SERVICE_CIRCUIT_BREAKER_THRESHOLD'], 5),
        timeout: parseEnv.number(env['AI_SERVICE_CIRCUIT_BREAKER_TIMEOUT'], 60000),
        resetTimeout: parseEnv.number(env['AI_SERVICE_CIRCUIT_BREAKER_RESET_TIMEOUT'], 30000),
        monitoringPeriod: parseEnv.number(env['AI_SERVICE_MONITORING_PERIOD'], 10000),
    };
    const cache = {
        enabled: parseEnv.boolean(env['AI_CACHE_ENABLED']),
        ttl: parseEnv.number(env['AI_CACHE_TTL'], 3600),
        maxItems: parseEnv.number(env['AI_CACHE_MAX_ITEMS'], 1000),
        keyPrefix: parseEnv.string(env['AI_CACHE_KEY_PREFIX'], 'ai:'),
    };
    const rateLimit = {
        enabled: parseEnv.boolean(env['AI_RATE_LIMIT_ENABLED']),
        requestsPerMinute: parseEnv.number(env['AI_REQUESTS_PER_MINUTE'], 60),
        burstLimit: parseEnv.number(env['AI_BURST_LIMIT'], 10),
    };
    const models = {
        vulnerabilityAnalysis: {
            name: parseEnv.string(env['AI_VULNERABILITY_MODEL'], 'vulnerability-analyzer-v1'),
            timeout: parseEnv.number(env['AI_VULNERABILITY_TIMEOUT'], 15000),
            maxTokens: parseEnv.number(env['AI_VULNERABILITY_MAX_TOKENS'], 2048),
        },
        threatDetection: {
            name: parseEnv.string(env['AI_THREAT_MODEL'], 'threat-detector-v1'),
            timeout: parseEnv.number(env['AI_THREAT_TIMEOUT'], 10000),
            maxTokens: parseEnv.number(env['AI_THREAT_MAX_TOKENS'], 1024),
        },
        anomalyDetection: {
            name: parseEnv.string(env['AI_ANOMALY_MODEL'], 'anomaly-detector-v1'),
            timeout: parseEnv.number(env['AI_ANOMALY_TIMEOUT'], 20000),
            batchSize: parseEnv.number(env['AI_ANOMALY_BATCH_SIZE'], 100),
        },
    };
    const queue = {
        enabled: parseEnv.boolean(env['AI_QUEUE_ENABLED']),
        name: parseEnv.string(env['AI_QUEUE_NAME'], 'ai-processing'),
        concurrency: parseEnv.number(env['AI_QUEUE_CONCURRENCY'], 5),
        maxRetries: parseEnv.number(env['AI_QUEUE_MAX_RETRIES'], 3),
        retryDelay: parseEnv.number(env['AI_QUEUE_RETRY_DELAY'], 5000),
    };
    const healthCheck = {
        enabled: parseEnv.boolean(env['AI_HEALTH_CHECK_ENABLED']),
        interval: parseEnv.number(env['AI_HEALTH_CHECK_INTERVAL'], 30000),
        timeout: parseEnv.number(env['AI_HEALTH_CHECK_TIMEOUT'], 5000),
        endpoint: parseEnv.string(env['AI_HEALTH_CHECK_ENDPOINT'], '/health'),
    };
    const monitoring = {
        enabled: parseEnv.boolean(env['AI_MONITORING_ENABLED']),
        metricsInterval: parseEnv.number(env['AI_METRICS_INTERVAL'], 60000),
        logRequests: parseEnv.boolean(env['AI_LOG_REQUESTS']),
        logResponses: parseEnv.boolean(env['AI_LOG_RESPONSES']),
        trackPerformance: parseEnv.boolean(env['AI_TRACK_PERFORMANCE']),
    };
    return {
        service,
        retry,
        circuitBreaker,
        cache,
        rateLimit,
        models,
        queue,
        healthCheck,
        monitoring,
    };
};
/**
 * AI Service Configuration with optimized environment parsing and validation.
 * Provides enterprise-grade settings for AI service integration with enhanced type safety.
 */
exports.aiConfig = (0, config_1.registerAs)('ai', createAIConfig);
// Validation DTOs with enhanced decorators and better organization
class AIServiceConfigDto {
}
exports.AIServiceConfigDto = AIServiceConfigDto;
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Transform)(({ value }) => parseEnv.boolean(value)),
    __metadata("design:type", Boolean)
], AIServiceConfigDto.prototype, "enabled", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AIServiceConfigDto.prototype, "url", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AIServiceConfigDto.prototype, "apiKey", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1000),
    (0, class_validator_1.Max)(300000),
    __metadata("design:type", Number)
], AIServiceConfigDto.prototype, "timeout", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AIServiceConfigDto.prototype, "version", void 0);
class AIRetryConfigDto {
}
exports.AIRetryConfigDto = AIRetryConfigDto;
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(10),
    __metadata("design:type", Number)
], AIRetryConfigDto.prototype, "attempts", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(100),
    __metadata("design:type", Number)
], AIRetryConfigDto.prototype, "delay", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], AIRetryConfigDto.prototype, "backoffMultiplier", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1000),
    __metadata("design:type", Number)
], AIRetryConfigDto.prototype, "maxDelay", void 0);
class AICircuitBreakerConfigDto {
}
exports.AICircuitBreakerConfigDto = AICircuitBreakerConfigDto;
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Transform)(({ value }) => parseEnv.boolean(value)),
    __metadata("design:type", Boolean)
], AICircuitBreakerConfigDto.prototype, "enabled", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], AICircuitBreakerConfigDto.prototype, "threshold", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1000),
    __metadata("design:type", Number)
], AICircuitBreakerConfigDto.prototype, "timeout", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1000),
    __metadata("design:type", Number)
], AICircuitBreakerConfigDto.prototype, "resetTimeout", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1000),
    __metadata("design:type", Number)
], AICircuitBreakerConfigDto.prototype, "monitoringPeriod", void 0);
class AICacheConfigDto {
}
exports.AICacheConfigDto = AICacheConfigDto;
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Transform)(({ value }) => parseEnv.boolean(value)),
    __metadata("design:type", Boolean)
], AICacheConfigDto.prototype, "enabled", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(60),
    __metadata("design:type", Number)
], AICacheConfigDto.prototype, "ttl", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(100),
    __metadata("design:type", Number)
], AICacheConfigDto.prototype, "maxItems", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AICacheConfigDto.prototype, "keyPrefix", void 0);
class AIRateLimitConfigDto {
}
exports.AIRateLimitConfigDto = AIRateLimitConfigDto;
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Transform)(({ value }) => parseEnv.boolean(value)),
    __metadata("design:type", Boolean)
], AIRateLimitConfigDto.prototype, "enabled", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], AIRateLimitConfigDto.prototype, "requestsPerMinute", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], AIRateLimitConfigDto.prototype, "burstLimit", void 0);
class AIModelConfigDto {
}
exports.AIModelConfigDto = AIModelConfigDto;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AIModelConfigDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1000),
    __metadata("design:type", Number)
], AIModelConfigDto.prototype, "timeout", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(256),
    __metadata("design:type", Number)
], AIModelConfigDto.prototype, "maxTokens", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], AIModelConfigDto.prototype, "batchSize", void 0);
class AIModelsConfigDto {
}
exports.AIModelsConfigDto = AIModelsConfigDto;
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => AIModelConfigDto),
    __metadata("design:type", AIModelConfigDto)
], AIModelsConfigDto.prototype, "vulnerabilityAnalysis", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => AIModelConfigDto),
    __metadata("design:type", AIModelConfigDto)
], AIModelsConfigDto.prototype, "threatDetection", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => AIModelConfigDto),
    __metadata("design:type", AIModelConfigDto)
], AIModelsConfigDto.prototype, "anomalyDetection", void 0);
class AIQueueConfigDto {
}
exports.AIQueueConfigDto = AIQueueConfigDto;
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Transform)(({ value }) => parseEnv.boolean(value)),
    __metadata("design:type", Boolean)
], AIQueueConfigDto.prototype, "enabled", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AIQueueConfigDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], AIQueueConfigDto.prototype, "concurrency", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], AIQueueConfigDto.prototype, "maxRetries", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1000),
    __metadata("design:type", Number)
], AIQueueConfigDto.prototype, "retryDelay", void 0);
class AIHealthCheckConfigDto {
}
exports.AIHealthCheckConfigDto = AIHealthCheckConfigDto;
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Transform)(({ value }) => parseEnv.boolean(value)),
    __metadata("design:type", Boolean)
], AIHealthCheckConfigDto.prototype, "enabled", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(5000),
    __metadata("design:type", Number)
], AIHealthCheckConfigDto.prototype, "interval", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1000),
    __metadata("design:type", Number)
], AIHealthCheckConfigDto.prototype, "timeout", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AIHealthCheckConfigDto.prototype, "endpoint", void 0);
class AIMonitoringConfigDto {
}
exports.AIMonitoringConfigDto = AIMonitoringConfigDto;
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Transform)(({ value }) => parseEnv.boolean(value)),
    __metadata("design:type", Boolean)
], AIMonitoringConfigDto.prototype, "enabled", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(10000),
    __metadata("design:type", Number)
], AIMonitoringConfigDto.prototype, "metricsInterval", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Transform)(({ value }) => parseEnv.boolean(value)),
    __metadata("design:type", Boolean)
], AIMonitoringConfigDto.prototype, "logRequests", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Transform)(({ value }) => parseEnv.boolean(value)),
    __metadata("design:type", Boolean)
], AIMonitoringConfigDto.prototype, "logResponses", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Transform)(({ value }) => parseEnv.boolean(value)),
    __metadata("design:type", Boolean)
], AIMonitoringConfigDto.prototype, "trackPerformance", void 0);
/**
 * Complete AI Configuration DTO with nested validation
 */
class AIConfigDto {
}
exports.AIConfigDto = AIConfigDto;
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => AIServiceConfigDto),
    __metadata("design:type", AIServiceConfigDto)
], AIConfigDto.prototype, "service", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => AIRetryConfigDto),
    __metadata("design:type", AIRetryConfigDto)
], AIConfigDto.prototype, "retry", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => AICircuitBreakerConfigDto),
    __metadata("design:type", AICircuitBreakerConfigDto)
], AIConfigDto.prototype, "circuitBreaker", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => AICacheConfigDto),
    __metadata("design:type", AICacheConfigDto)
], AIConfigDto.prototype, "cache", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => AIRateLimitConfigDto),
    __metadata("design:type", AIRateLimitConfigDto)
], AIConfigDto.prototype, "rateLimit", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => AIModelsConfigDto),
    __metadata("design:type", AIModelsConfigDto)
], AIConfigDto.prototype, "models", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => AIQueueConfigDto),
    __metadata("design:type", AIQueueConfigDto)
], AIConfigDto.prototype, "queue", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => AIHealthCheckConfigDto),
    __metadata("design:type", AIHealthCheckConfigDto)
], AIConfigDto.prototype, "healthCheck", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => AIMonitoringConfigDto),
    __metadata("design:type", AIMonitoringConfigDto)
], AIConfigDto.prototype, "monitoring", void 0);
// Configuration constants for easy reference
exports.AI_CONFIG_DEFAULTS = {
    SERVICE_TIMEOUT: 30000,
    RETRY_ATTEMPTS: 3,
    CACHE_TTL: 3600,
    QUEUE_CONCURRENCY: 5,
    HEALTH_CHECK_INTERVAL: 30000,
    MONITORING_METRICS_INTERVAL: 60000,
};
// Environment variable keys for documentation
exports.AI_ENV_KEYS = {
    SERVICE: {
        ENABLED: 'AI_SERVICE_ENABLED',
        URL: 'AI_SERVICE_URL',
        API_KEY: 'AI_SERVICE_API_KEY',
        TIMEOUT: 'AI_SERVICE_TIMEOUT',
        VERSION: 'AI_SERVICE_VERSION',
    },
    RETRY: {
        ATTEMPTS: 'AI_SERVICE_RETRY_ATTEMPTS',
        DELAY: 'AI_SERVICE_RETRY_DELAY',
        BACKOFF_MULTIPLIER: 'AI_SERVICE_BACKOFF_MULTIPLIER',
        MAX_DELAY: 'AI_SERVICE_MAX_DELAY',
    },
    CIRCUIT_BREAKER: {
        ENABLED: 'AI_SERVICE_CIRCUIT_BREAKER_ENABLED',
        THRESHOLD: 'AI_SERVICE_CIRCUIT_BREAKER_THRESHOLD',
        TIMEOUT: 'AI_SERVICE_CIRCUIT_BREAKER_TIMEOUT',
        RESET_TIMEOUT: 'AI_SERVICE_CIRCUIT_BREAKER_RESET_TIMEOUT',
        MONITORING_PERIOD: 'AI_SERVICE_MONITORING_PERIOD',
    },
    CACHE: {
        ENABLED: 'AI_CACHE_ENABLED',
        TTL: 'AI_CACHE_TTL',
        MAX_ITEMS: 'AI_CACHE_MAX_ITEMS',
        KEY_PREFIX: 'AI_CACHE_KEY_PREFIX',
    },
    RATE_LIMIT: {
        ENABLED: 'AI_RATE_LIMIT_ENABLED',
        REQUESTS_PER_MINUTE: 'AI_REQUESTS_PER_MINUTE',
        BURST_LIMIT: 'AI_BURST_LIMIT',
    },
    MODELS: {
        VULNERABILITY_MODEL: 'AI_VULNERABILITY_MODEL',
        VULNERABILITY_TIMEOUT: 'AI_VULNERABILITY_TIMEOUT',
        VULNERABILITY_MAX_TOKENS: 'AI_VULNERABILITY_MAX_TOKENS',
        THREAT_MODEL: 'AI_THREAT_MODEL',
        THREAT_TIMEOUT: 'AI_THREAT_TIMEOUT',
        THREAT_MAX_TOKENS: 'AI_THREAT_MAX_TOKENS',
        ANOMALY_MODEL: 'AI_ANOMALY_MODEL',
        ANOMALY_TIMEOUT: 'AI_ANOMALY_TIMEOUT',
        ANOMALY_BATCH_SIZE: 'AI_ANOMALY_BATCH_SIZE',
    },
    QUEUE: {
        ENABLED: 'AI_QUEUE_ENABLED',
        NAME: 'AI_QUEUE_NAME',
        CONCURRENCY: 'AI_QUEUE_CONCURRENCY',
        MAX_RETRIES: 'AI_QUEUE_MAX_RETRIES',
        RETRY_DELAY: 'AI_QUEUE_RETRY_DELAY',
    },
    HEALTH_CHECK: {
        ENABLED: 'AI_HEALTH_CHECK_ENABLED',
        INTERVAL: 'AI_HEALTH_CHECK_INTERVAL',
        TIMEOUT: 'AI_HEALTH_CHECK_TIMEOUT',
        ENDPOINT: 'AI_HEALTH_CHECK_ENDPOINT',
    },
    MONITORING: {
        ENABLED: 'AI_MONITORING_ENABLED',
        METRICS_INTERVAL: 'AI_METRICS_INTERVAL',
        LOG_REQUESTS: 'AI_LOG_REQUESTS',
        LOG_RESPONSES: 'AI_LOG_RESPONSES',
        TRACK_PERFORMANCE: 'AI_TRACK_PERFORMANCE',
    },
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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