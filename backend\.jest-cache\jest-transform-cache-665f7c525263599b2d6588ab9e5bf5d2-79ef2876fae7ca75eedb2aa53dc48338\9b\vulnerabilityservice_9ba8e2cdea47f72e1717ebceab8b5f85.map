{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\vulnerability.service.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,2CAAuE;AACvE,6CAAmD;AACnD,qCAAqE;AACrE,+CAAwD;AACxD,qFAA2E;AAC3E,2GAAgG;AAChG,yGAA8F;AAC9F,yFAA+E;AAC/E,sFAAkF;AAClF,0FAAsF;AACtF,uGAAmG;AAEnG;;;GAGG;AAEI,IAAM,oBAAoB,4BAA1B,MAAM,oBAAoB;IAG/B,YAEE,uBAAmE,EAEnE,oBAA0E,EAE1E,mBAAwE,EAExE,eAAmD,EAClC,aAA4B,EAC5B,YAA0B,EAC1B,mBAAwC;QATxC,4BAAuB,GAAvB,uBAAuB,CAA2B;QAElD,yBAAoB,GAApB,oBAAoB,CAAqC;QAEzD,wBAAmB,GAAnB,mBAAmB,CAAoC;QAEvD,oBAAe,GAAf,eAAe,CAAmB;QAClC,kBAAa,GAAb,aAAa,CAAe;QAC5B,iBAAY,GAAZ,YAAY,CAAc;QAC1B,wBAAmB,GAAnB,mBAAmB,CAAqB;QAb1C,WAAM,GAAG,IAAI,eAAM,CAAC,sBAAoB,CAAC,IAAI,CAAC,CAAC;IAc7D,CAAC;IAEJ;;OAEG;IACH,KAAK,CAAC,yBAAyB;QAC7B,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAC;YAExD,uCAAuC;YACvC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,uBAAuB;iBACtD,kBAAkB,CAAC,MAAM,CAAC;iBAC1B,MAAM,CAAC,eAAe,EAAE,UAAU,CAAC;iBACnC,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC;iBAC9B,OAAO,CAAC,eAAe,CAAC;iBACxB,UAAU,EAAE,CAAC;YAEhB,kCAAkC;YAClC,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC;gBAChE,KAAK,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE;aAC7B,CAAC,CAAC;YAEH,oCAAoC;YACpC,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC;gBACjE,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;aAC5B,CAAC,CAAC;YAEH,kCAAkC;YAClC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC;gBAC9D,KAAK,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;aAC3B,CAAC,CAAC;YAEH,4CAA4C;YAC5C,MAAM,aAAa,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YACtE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC;gBAC3D,KAAK,EAAE,EAAE,aAAa,EAAE,IAAA,iBAAO,EAAC,aAAa,EAAE,IAAI,IAAI,EAAE,CAAC,EAAE;aAC7D,CAAC,CAAC;YAEH,mCAAmC;YACnC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC;gBAC5D,KAAK,EAAE,EAAE,cAAc,EAAE,IAAI,EAAE;aAChC,CAAC,CAAC;YAEH,4BAA4B;YAC5B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,uBAAuB;iBACnD,kBAAkB,CAAC,MAAM,CAAC;iBAC1B,MAAM,CAAC,4HAA4H,EAAE,SAAS,CAAC;iBAC/I,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC;iBAC9B,OAAO,CAAC,SAAS,CAAC;iBAClB,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC;iBACxB,KAAK,CAAC,EAAE,CAAC;iBACT,UAAU,EAAE,CAAC;YAEhB,4CAA4C;YAC5C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAEnD,wBAAwB;YACxB,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAE1D,uCAAuC;YACvC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC;gBAC7D,KAAK,EAAE;oBACL,EAAE,QAAQ,EAAE,UAAU,EAAE;oBACxB,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE;iBACxC;gBACD,KAAK,EAAE,EAAE,aAAa,EAAE,MAAM,EAAE;gBAChC,IAAI,EAAE,EAAE;aACT,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG;gBACd,KAAK,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBAC1E,QAAQ,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,UAAU,CAAC,EAAE,KAAK,IAAI,CAAC;gBACzE,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,EAAE,KAAK,IAAI,CAAC;gBACjE,MAAM,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,EAAE,KAAK,IAAI,CAAC;gBACrE,GAAG,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,KAAK,CAAC,EAAE,KAAK,IAAI,CAAC;gBAC/D,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,EAAE,KAAK,IAAI,CAAC;gBACjE,WAAW,EAAE,gBAAgB;gBAC7B,YAAY,EAAE,iBAAiB;gBAC/B,SAAS,EAAE,cAAc;gBACzB,MAAM,EAAE,WAAW;gBACnB,OAAO,EAAE,YAAY;gBACrB,SAAS,EAAE,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;aACtI,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gDAAgD,EAAE;gBAChE,oBAAoB,EAAE,OAAO,CAAC,KAAK;gBACnC,aAAa,EAAE,OAAO,CAAC,QAAQ;gBAC/B,gBAAgB,EAAE,OAAO,CAAC,WAAW;aACtC,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO;gBACP,SAAS,EAAE;oBACT,UAAU,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;wBAC9C,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBAC1C,OAAO,GAAG,CAAC;oBACb,CAAC,EAAE,EAAE,CAAC;oBACN,gBAAgB,EAAE;wBAChB,WAAW,EAAE,gBAAgB;wBAC7B,UAAU,EAAE,iBAAiB;wBAC7B,SAAS,EAAE,cAAc;qBAC1B;oBACD,aAAa,EAAE;wBACb,OAAO,EAAE,YAAY;wBACrB,SAAS,EAAE,OAAO,CAAC,KAAK,GAAG,YAAY;qBACxC;iBACF;gBACD,WAAW;gBACX,MAAM;gBACN,gBAAgB;gBAChB,cAAc,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;gBACvD,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4CAA4C,EAAE;gBAC9D,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CAAC,QAiB3B;QAMC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC;YAChC,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC;YACnD,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAElC,MAAM,YAAY,GAAG,IAAI,CAAC,uBAAuB;iBAC9C,kBAAkB,CAAC,MAAM,CAAC;iBAC1B,iBAAiB,CAAC,kBAAkB,EAAE,aAAa,CAAC;iBACpD,iBAAiB,CAAC,iBAAiB,EAAE,YAAY,CAAC;iBAClD,iBAAiB,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC;YAEtD,gBAAgB;YAChB,IAAI,QAAQ,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC;gBAChC,YAAY,CAAC,QAAQ,CAAC,mCAAmC,EAAE,EAAE,UAAU,EAAE,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;YAClG,CAAC;YAED,IAAI,QAAQ,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;gBACvC,YAAY,CAAC,QAAQ,CAAC,iCAAiC,EAAE,EAAE,WAAW,EAAE,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;YAClG,CAAC;YAED,IAAI,QAAQ,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;gBACtC,YAAY,CAAC,QAAQ,CAAC,+BAA+B,EAAE,EAAE,UAAU,EAAE,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;YAC9F,CAAC;YAED,IAAI,QAAQ,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;gBACrC,YAAY,CAAC,QAAQ,CAAC,6BAA6B,EAAE,EAAE,SAAS,EAAE,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC;YAC1F,CAAC;YAED,IAAI,QAAQ,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;gBAC1C,YAAY,CAAC,QAAQ,CAAC,uCAAuC,EAAE,EAAE,cAAc,EAAE,QAAQ,CAAC,cAAc,EAAE,CAAC,CAAC;YAC9G,CAAC;YAED,IAAI,QAAQ,CAAC,cAAc,EAAE,CAAC;gBAC5B,YAAY,CAAC,QAAQ,CAAC,uCAAuC,EAAE,EAAE,cAAc,EAAE,QAAQ,CAAC,cAAc,EAAE,CAAC,CAAC;YAC9G,CAAC;YAED,IAAI,QAAQ,CAAC,eAAe,EAAE,CAAC;gBAC7B,YAAY,CAAC,QAAQ,CAAC,wCAAwC,EAAE,EAAE,eAAe,EAAE,QAAQ,CAAC,eAAe,EAAE,CAAC,CAAC;YACjH,CAAC;YAED,IAAI,QAAQ,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;gBACxC,YAAY,CAAC,QAAQ,CAAC,iCAAiC,EAAE,EAAE,YAAY,EAAE,QAAQ,CAAC,YAAY,EAAE,CAAC,CAAC;YACpG,CAAC;YAED,IAAI,QAAQ,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;gBACxC,YAAY,CAAC,QAAQ,CAAC,iCAAiC,EAAE,EAAE,YAAY,EAAE,QAAQ,CAAC,YAAY,EAAE,CAAC,CAAC;YACpG,CAAC;YAED,IAAI,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;gBAC1B,YAAY,CAAC,QAAQ,CAAC,oBAAoB,EAAE,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;YACvE,CAAC;YAED,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;gBACxB,YAAY,CAAC,QAAQ,CACnB,2GAA2G,EAC3G,EAAE,UAAU,EAAE,IAAI,QAAQ,CAAC,UAAU,GAAG,EAAE,CAC3C,CAAC;YACJ,CAAC;YAED,IAAI,QAAQ,CAAC,gBAAgB,EAAE,MAAM,EAAE,CAAC;gBACtC,MAAM,iBAAiB,GAAG,QAAQ,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CACzE,2CAA2C,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAC9G,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACf,YAAY,CAAC,QAAQ,CAAC,IAAI,iBAAiB,GAAG,CAAC,CAAC;YAClD,CAAC;YAED,gBAAgB;YAChB,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,IAAI,eAAe,CAAC;YAClD,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,IAAI,MAAM,CAAC;YAC/C,YAAY,CAAC,OAAO,CAAC,QAAQ,MAAM,EAAE,EAAE,SAAS,CAAC,CAAC;YAElD,mBAAmB;YACnB,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEtC,MAAM,CAAC,eAAe,EAAE,KAAK,CAAC,GAAG,MAAM,YAAY,CAAC,eAAe,EAAE,CAAC;YAEtE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;gBAClD,KAAK;gBACL,IAAI;gBACJ,KAAK;gBACL,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM;aAC5C,CAAC,CAAC;YAEH,OAAO;gBACL,eAAe;gBACf,KAAK;gBACL,IAAI;gBACJ,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;aACrC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE;gBACpD,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,QAAQ;aACT,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAAC,EAAU;QACtC,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC;gBAC/D,KAAK,EAAE,EAAE,EAAE,EAAE;gBACb,SAAS,EAAE;oBACT,aAAa;oBACb,YAAY;oBACZ,gBAAgB;oBAChB,sBAAsB;iBACvB;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,MAAM,IAAI,0BAAiB,CAAC,yBAAyB,CAAC,CAAC;YACzD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE;gBACnD,eAAe,EAAE,EAAE;gBACnB,UAAU,EAAE,aAAa,CAAC,UAAU;gBACpC,QAAQ,EAAE,aAAa,CAAC,QAAQ;aACjC,CAAC,CAAC;YAEH,OAAO,aAAa,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE;gBACvD,eAAe,EAAE,EAAE;gBACnB,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,iBAAsB,EAAE,MAAc;QAC9D,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;gBAC1C,UAAU,EAAE,iBAAiB,CAAC,UAAU;gBACxC,QAAQ,EAAE,iBAAiB,CAAC,QAAQ;gBACpC,MAAM;aACP,CAAC,CAAC;YAEH,wCAAwC;YACxC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC;gBAC1D,KAAK,EAAE,EAAE,UAAU,EAAE,iBAAiB,CAAC,UAAU,EAAE;aACpD,CAAC,CAAC;YAEH,IAAI,QAAQ,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;YACvE,CAAC;YAED,MAAM,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC;gBACxD,GAAG,iBAAiB;gBACpB,aAAa,EAAE,iBAAiB,CAAC,aAAa,IAAI,IAAI,IAAI,EAAE;gBAC5D,gBAAgB,EAAE,iBAAiB,CAAC,gBAAgB,IAAI,IAAI,IAAI,EAAE;gBAClE,UAAU,EAAE,iBAAiB,CAAC,UAAU,IAAI;oBAC1C,IAAI,EAAE,cAAc;oBACpB,IAAI,EAAE,UAAU;oBAChB,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACrC,UAAU,EAAE,MAAM;iBACnB;aACF,CAAC,CAAC;YAEH,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAElF,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,eAAe,EACf,kBAAkB,CAAC,EAAE,EACrB;gBACE,UAAU,EAAE,iBAAiB,CAAC,UAAU;gBACxC,QAAQ,EAAE,iBAAiB,CAAC,QAAQ;gBACpC,KAAK,EAAE,iBAAiB,CAAC,KAAK;aAC/B,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oCAAoC,EAAE;gBACpD,eAAe,EAAE,kBAAkB,CAAC,EAAE;gBACtC,UAAU,EAAE,iBAAiB,CAAC,UAAU;gBACxC,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,kBAAkB,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;gBAClD,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,iBAAiB;gBACjB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,EAAU,EAAE,OAAY,EAAE,MAAc;QAChE,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC;gBAC/D,KAAK,EAAE,EAAE,EAAE,EAAE;aACd,CAAC,CAAC;YAEH,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,MAAM,IAAI,0BAAiB,CAAC,yBAAyB,CAAC,CAAC;YACzD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;gBAC1C,eAAe,EAAE,EAAE;gBACnB,UAAU,EAAE,aAAa,CAAC,UAAU;gBACpC,MAAM;aACP,CAAC,CAAC;YAEH,0BAA0B;YAC1B,MAAM,OAAO,GAAG,EAAE,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBACjC,IAAI,aAAa,CAAC,GAAG,CAAC,KAAK,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;oBACxC,OAAO,CAAC,GAAG,CAAC,GAAG;wBACb,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC;wBACxB,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC;qBACjB,CAAC;gBACJ,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;YACtC,aAAa,CAAC,gBAAgB,GAAG,IAAI,IAAI,EAAE,CAAC;YAE5C,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAElF,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,eAAe,EACf,EAAE,EACF;gBACE,UAAU,EAAE,aAAa,CAAC,UAAU;gBACpC,OAAO;aACR,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oCAAoC,EAAE;gBACpD,eAAe,EAAE,EAAE;gBACnB,UAAU,EAAE,aAAa,CAAC,UAAU;gBACpC,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM;gBACzC,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,kBAAkB,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;gBAClD,eAAe,EAAE,EAAE;gBACnB,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,EAAU,EAAE,MAAc;QAClD,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC;gBAC/D,KAAK,EAAE,EAAE,EAAE,EAAE;aACd,CAAC,CAAC;YAEH,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,MAAM,IAAI,0BAAiB,CAAC,yBAAyB,CAAC,CAAC;YACzD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;gBAC1C,eAAe,EAAE,EAAE;gBACnB,UAAU,EAAE,aAAa,CAAC,UAAU;gBACpC,MAAM;aACP,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAEzD,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACnC,MAAM,EACN,QAAQ,EACR,eAAe,EACf,EAAE,EACF;gBACE,UAAU,EAAE,aAAa,CAAC,UAAU;gBACpC,QAAQ,EAAE,aAAa,CAAC,QAAQ;gBAChC,KAAK,EAAE,aAAa,CAAC,KAAK;aAC3B,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oCAAoC,EAAE;gBACpD,eAAe,EAAE,EAAE;gBACnB,UAAU,EAAE,aAAa,CAAC,UAAU;gBACpC,MAAM;aACP,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;gBAClD,eAAe,EAAE,EAAE;gBACnB,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IAEG,AAAN,KAAK,CAAC,sBAAsB;QAC1B,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;YAEvD,yCAAyC;YACzC,MAAM,IAAI,CAAC,+BAA+B,EAAE,CAAC;YAE7C,4BAA4B;YAC5B,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAEjC,0BAA0B;YAC1B,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAE/B,wCAAwC;YACxC,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAEnC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6CAA6C,EAAE;gBAC/D,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,yBAAyB;IAEjB,KAAK,CAAC,sBAAsB;QAClC,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QAEvB,KAAK,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7B,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;YACrE,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;YAEvE,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC;gBACrD,KAAK,EAAE,EAAE,aAAa,EAAE,IAAA,iBAAO,EAAC,SAAS,EAAE,OAAO,CAAC,EAAE;aACtD,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC;gBACV,KAAK,EAAE,SAAS,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;gBAC9C,KAAK;aACN,CAAC,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC/B,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,uBAAuB;aACpD,kBAAkB,CAAC,MAAM,CAAC;aAC1B,MAAM,CAAC,qJAAqJ,EAAE,MAAM,CAAC;aACrK,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC;aAC9B,KAAK,CAAC,6BAA6B,CAAC;aACpC,OAAO,CAAC,MAAM,CAAC;aACf,UAAU,EAAE,CAAC;QAEhB,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YACvC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtC,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAEO,KAAK,CAAC,+BAA+B;QAC3C,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAE7D,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC;YAC1D,KAAK,EAAE;gBACL,QAAQ,EAAE,UAAU;gBACpB,aAAa,EAAE,IAAA,iBAAO,EAAC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC;aAC9C;SACF,CAAC,CAAC;QAEH,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,mBAAmB,CAAC,mCAAmC,CAAC,WAAW,CAAC,CAAC;QAClF,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC/B,gFAAgF;QAChF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpD,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,+DAA+D;QAC/D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClD,CAAC;IAEO,KAAK,CAAC,qBAAqB;QACjC,8EAA8E;QAC9E,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;IACxD,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,aAAa;QACjB,IAAI,CAAC;YACH,MAAM,CACJ,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,GAAG,EACH,IAAI,EACJ,UAAU,EACV,QAAQ,EACT,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACpB,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE;gBACpC,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE,EAAE,CAAC;gBACvE,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,CAAC;gBACnE,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,CAAC;gBACrE,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,CAAC;gBAClE,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,CAAC;gBACjE,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,aAAa,EAAE,EAAE,CAAC;gBACxE,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,EAAE,CAAC;aACtE,CAAC,CAAC;YAEH,OAAO;gBACL,KAAK;gBACL,UAAU,EAAE;oBACV,QAAQ;oBACR,IAAI;oBACJ,MAAM;oBACN,GAAG;iBACJ;gBACD,QAAQ,EAAE;oBACR,IAAI;oBACJ,UAAU;oBACV,QAAQ;iBACT;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE;gBAC1D,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACK,kBAAkB,CAAC,aAAqC;QAC9D,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,uBAAuB;QACvB,IAAI,aAAa,CAAC,SAAS,EAAE,CAAC;YAC5B,KAAK,GAAG,aAAa,CAAC,SAAS,CAAC;QAClC,CAAC;aAAM,CAAC;YACN,qCAAqC;YACrC,MAAM,cAAc,GAAG;gBACrB,QAAQ,EAAE,GAAG;gBACb,IAAI,EAAE,GAAG;gBACT,MAAM,EAAE,GAAG;gBACX,GAAG,EAAE,GAAG;gBACR,IAAI,EAAE,GAAG;aACV,CAAC;YACF,KAAK,GAAG,cAAc,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC;QACxD,CAAC;QAED,4BAA4B;QAC5B,IAAI,aAAa,CAAC,aAAa,EAAE,CAAC;YAChC,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;QAED,IAAI,aAAa,CAAC,gBAAgB,EAAE,CAAC;YACnC,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;QAED,IAAI,aAAa,CAAC,mBAAmB,EAAE,CAAC;YACtC,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;QAED,qCAAqC;QACrC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;IAC9C,CAAC;IAED;;;;OAIG;IACK,yBAAyB,CAAC,IAAS;QACzC,MAAM,SAAS,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;QAE9B,iCAAiC;QACjC,OAAO,SAAS,CAAC,QAAQ,CAAC;QAE1B,OAAO,SAAS,CAAC;IACnB,CAAC;CACF,CAAA;AA5pBY,oDAAoB;AA0dzB;IADL,IAAA,eAAI,EAAC,yBAAc,CAAC,UAAU,CAAC;;;wDACA,OAAO,oBAAP,OAAO;kEAsBtC;+BAhfU,oBAAoB;IADhC,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,oCAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,yDAAuB,CAAC,CAAA;IAEzC,WAAA,IAAA,0BAAgB,EAAC,uDAAsB,CAAC,CAAA;IAExC,WAAA,IAAA,0BAAgB,EAAC,oBAAK,CAAC,CAAA;yDALkB,oBAAU,oBAAV,oBAAU,oDAEb,oBAAU,oBAAV,oBAAU,oDAEX,oBAAU,oBAAV,oBAAU,oDAEd,oBAAU,oBAAV,oBAAU,oDACZ,8BAAa,oBAAb,8BAAa,oDACd,4BAAY,oBAAZ,4BAAY,oDACL,0CAAmB,oBAAnB,0CAAmB;GAdhD,oBAAoB,CA4pBhC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\vulnerability-management\\application\\services\\vulnerability.service.ts"], "sourcesContent": ["import { Injectable, Logger, NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository, Between, In, Like, IsNull, Not } from 'typeorm';\r\nimport { <PERSON>ron, CronExpression } from '@nestjs/schedule';\r\nimport { Vulnerability } from '../../domain/entities/vulnerability.entity';\r\nimport { VulnerabilityAssessment } from '../../domain/entities/vulnerability-assessment.entity';\r\nimport { VulnerabilityException } from '../../domain/entities/vulnerability-exception.entity';\r\nimport { Asset } from '../../../asset-management/domain/entities/asset.entity';\r\nimport { LoggerService } from '../../../../infrastructure/logging/logger.service';\r\nimport { AuditService } from '../../../../infrastructure/logging/audit/audit.service';\r\nimport { NotificationService } from '../../../../infrastructure/notification/notification.service';\r\n\r\n/**\r\n * Vulnerability service\r\n * Handles core vulnerability management operations\r\n */\r\n@Injectable()\r\nexport class VulnerabilityService {\r\n  private readonly logger = new Logger(VulnerabilityService.name);\r\n\r\n  constructor(\r\n    @InjectRepository(Vulnerability)\r\n    private readonly vulnerabilityRepository: Repository<Vulnerability>,\r\n    @InjectRepository(VulnerabilityAssessment)\r\n    private readonly assessmentRepository: Repository<VulnerabilityAssessment>,\r\n    @InjectRepository(VulnerabilityException)\r\n    private readonly exceptionRepository: Repository<VulnerabilityException>,\r\n    @InjectRepository(Asset)\r\n    private readonly assetRepository: Repository<Asset>,\r\n    private readonly loggerService: LoggerService,\r\n    private readonly auditService: AuditService,\r\n    private readonly notificationService: NotificationService,\r\n  ) {}\r\n\r\n  /**\r\n   * Get vulnerability dashboard data\r\n   */\r\n  async getVulnerabilityDashboard(): Promise<any> {\r\n    try {\r\n      this.logger.debug('Generating vulnerability dashboard');\r\n\r\n      // Get vulnerability counts by severity\r\n      const severityCounts = await this.vulnerabilityRepository\r\n        .createQueryBuilder('vuln')\r\n        .select('vuln.severity', 'severity')\r\n        .addSelect('COUNT(*)', 'count')\r\n        .groupBy('vuln.severity')\r\n        .getRawMany();\r\n\r\n      // Get exploitable vulnerabilities\r\n      const exploitableCount = await this.vulnerabilityRepository.count({\r\n        where: { exploitable: true },\r\n      });\r\n\r\n      // Get vulnerabilities with exploits\r\n      const withExploitsCount = await this.vulnerabilityRepository.count({\r\n        where: { hasExploit: true },\r\n      });\r\n\r\n      // Get vulnerabilities in the wild\r\n      const inTheWildCount = await this.vulnerabilityRepository.count({\r\n        where: { inTheWild: true },\r\n      });\r\n\r\n      // Get recent vulnerabilities (last 30 days)\r\n      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);\r\n      const recentCount = await this.vulnerabilityRepository.count({\r\n        where: { publishedDate: Between(thirtyDaysAgo, new Date()) },\r\n      });\r\n\r\n      // Get vulnerabilities with patches\r\n      const patchedCount = await this.vulnerabilityRepository.count({\r\n        where: { patchAvailable: true },\r\n      });\r\n\r\n      // Get top affected products\r\n      const topProducts = await this.vulnerabilityRepository\r\n        .createQueryBuilder('vuln')\r\n        .select(\"jsonb_array_elements(vuln.affected_products)->>'vendor' || ' ' || jsonb_array_elements(vuln.affected_products)->>'product'\", 'product')\r\n        .addSelect('COUNT(*)', 'count')\r\n        .groupBy('product')\r\n        .orderBy('count', 'DESC')\r\n        .limit(10)\r\n        .getRawMany();\r\n\r\n      // Get vulnerability trends (last 12 months)\r\n      const trends = await this.getVulnerabilityTrends();\r\n\r\n      // Get risk distribution\r\n      const riskDistribution = await this.getRiskDistribution();\r\n\r\n      // Get recent high-risk vulnerabilities\r\n      const recentHighRisk = await this.vulnerabilityRepository.find({\r\n        where: [\r\n          { severity: 'critical' },\r\n          { severity: 'high', exploitable: true },\r\n        ],\r\n        order: { publishedDate: 'DESC' },\r\n        take: 10,\r\n      });\r\n\r\n      const summary = {\r\n        total: severityCounts.reduce((sum, item) => sum + parseInt(item.count), 0),\r\n        critical: severityCounts.find(s => s.severity === 'critical')?.count || 0,\r\n        high: severityCounts.find(s => s.severity === 'high')?.count || 0,\r\n        medium: severityCounts.find(s => s.severity === 'medium')?.count || 0,\r\n        low: severityCounts.find(s => s.severity === 'low')?.count || 0,\r\n        info: severityCounts.find(s => s.severity === 'info')?.count || 0,\r\n        exploitable: exploitableCount,\r\n        withExploits: withExploitsCount,\r\n        inTheWild: inTheWildCount,\r\n        recent: recentCount,\r\n        patched: patchedCount,\r\n        patchRate: severityCounts.length > 0 ? (patchedCount / severityCounts.reduce((sum, item) => sum + parseInt(item.count), 0)) * 100 : 0,\r\n      };\r\n\r\n      this.logger.log('Vulnerability dashboard generated successfully', {\r\n        totalVulnerabilities: summary.total,\r\n        criticalCount: summary.critical,\r\n        exploitableCount: summary.exploitable,\r\n      });\r\n\r\n      return {\r\n        summary,\r\n        breakdown: {\r\n          bySeverity: severityCounts.reduce((acc, item) => {\r\n            acc[item.severity] = parseInt(item.count);\r\n            return acc;\r\n          }, {}),\r\n          byExploitability: {\r\n            exploitable: exploitableCount,\r\n            hasExploit: withExploitsCount,\r\n            inTheWild: inTheWildCount,\r\n          },\r\n          byPatchStatus: {\r\n            patched: patchedCount,\r\n            unpatched: summary.total - patchedCount,\r\n          },\r\n        },\r\n        topProducts,\r\n        trends,\r\n        riskDistribution,\r\n        recentHighRisk: recentHighRisk.map(v => v.getSummary()),\r\n        timestamp: new Date(),\r\n      };\r\n    } catch (error) {\r\n      this.logger.error('Failed to generate vulnerability dashboard', {\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Search vulnerabilities with advanced filtering\r\n   */\r\n  async searchVulnerabilities(criteria: {\r\n    page?: number;\r\n    limit?: number;\r\n    severities?: string[];\r\n    exploitable?: boolean;\r\n    hasExploit?: boolean;\r\n    inTheWild?: boolean;\r\n    patchAvailable?: boolean;\r\n    publishedAfter?: Date;\r\n    publishedBefore?: Date;\r\n    cvssScoreMin?: number;\r\n    cvssScoreMax?: number;\r\n    affectedProducts?: string[];\r\n    tags?: string[];\r\n    searchText?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n  }): Promise<{\r\n    vulnerabilities: Vulnerability[];\r\n    total: number;\r\n    page: number;\r\n    totalPages: number;\r\n  }> {\r\n    try {\r\n      const page = criteria.page || 1;\r\n      const limit = Math.min(criteria.limit || 50, 1000);\r\n      const offset = (page - 1) * limit;\r\n\r\n      const queryBuilder = this.vulnerabilityRepository\r\n        .createQueryBuilder('vuln')\r\n        .leftJoinAndSelect('vuln.assessments', 'assessments')\r\n        .leftJoinAndSelect('vuln.exceptions', 'exceptions')\r\n        .leftJoinAndSelect('vuln.affectedAssets', 'assets');\r\n\r\n      // Apply filters\r\n      if (criteria.severities?.length) {\r\n        queryBuilder.andWhere('vuln.severity IN (:...severities)', { severities: criteria.severities });\r\n      }\r\n\r\n      if (criteria.exploitable !== undefined) {\r\n        queryBuilder.andWhere('vuln.exploitable = :exploitable', { exploitable: criteria.exploitable });\r\n      }\r\n\r\n      if (criteria.hasExploit !== undefined) {\r\n        queryBuilder.andWhere('vuln.hasExploit = :hasExploit', { hasExploit: criteria.hasExploit });\r\n      }\r\n\r\n      if (criteria.inTheWild !== undefined) {\r\n        queryBuilder.andWhere('vuln.inTheWild = :inTheWild', { inTheWild: criteria.inTheWild });\r\n      }\r\n\r\n      if (criteria.patchAvailable !== undefined) {\r\n        queryBuilder.andWhere('vuln.patchAvailable = :patchAvailable', { patchAvailable: criteria.patchAvailable });\r\n      }\r\n\r\n      if (criteria.publishedAfter) {\r\n        queryBuilder.andWhere('vuln.publishedDate >= :publishedAfter', { publishedAfter: criteria.publishedAfter });\r\n      }\r\n\r\n      if (criteria.publishedBefore) {\r\n        queryBuilder.andWhere('vuln.publishedDate <= :publishedBefore', { publishedBefore: criteria.publishedBefore });\r\n      }\r\n\r\n      if (criteria.cvssScoreMin !== undefined) {\r\n        queryBuilder.andWhere('vuln.cvssScore >= :cvssScoreMin', { cvssScoreMin: criteria.cvssScoreMin });\r\n      }\r\n\r\n      if (criteria.cvssScoreMax !== undefined) {\r\n        queryBuilder.andWhere('vuln.cvssScore <= :cvssScoreMax', { cvssScoreMax: criteria.cvssScoreMax });\r\n      }\r\n\r\n      if (criteria.tags?.length) {\r\n        queryBuilder.andWhere('vuln.tags && :tags', { tags: criteria.tags });\r\n      }\r\n\r\n      if (criteria.searchText) {\r\n        queryBuilder.andWhere(\r\n          '(vuln.identifier ILIKE :searchText OR vuln.title ILIKE :searchText OR vuln.description ILIKE :searchText)',\r\n          { searchText: `%${criteria.searchText}%` }\r\n        );\r\n      }\r\n\r\n      if (criteria.affectedProducts?.length) {\r\n        const productConditions = criteria.affectedProducts.map((product, index) =>\r\n          `vuln.affected_products @> '[{\"vendor\": \"${product.split(' ')[0]}\", \"product\": \"${product.split(' ')[1]}\"}]'`\r\n        ).join(' OR ');\r\n        queryBuilder.andWhere(`(${productConditions})`);\r\n      }\r\n\r\n      // Apply sorting\r\n      const sortBy = criteria.sortBy || 'publishedDate';\r\n      const sortOrder = criteria.sortOrder || 'DESC';\r\n      queryBuilder.orderBy(`vuln.${sortBy}`, sortOrder);\r\n\r\n      // Apply pagination\r\n      queryBuilder.skip(offset).take(limit);\r\n\r\n      const [vulnerabilities, total] = await queryBuilder.getManyAndCount();\r\n\r\n      this.logger.debug('Vulnerability search completed', {\r\n        total,\r\n        page,\r\n        limit,\r\n        criteriaCount: Object.keys(criteria).length,\r\n      });\r\n\r\n      return {\r\n        vulnerabilities,\r\n        total,\r\n        page,\r\n        totalPages: Math.ceil(total / limit),\r\n      };\r\n    } catch (error) {\r\n      this.logger.error('Failed to search vulnerabilities', {\r\n        error: error.message,\r\n        criteria,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get vulnerability details\r\n   */\r\n  async getVulnerabilityDetails(id: string): Promise<Vulnerability> {\r\n    try {\r\n      const vulnerability = await this.vulnerabilityRepository.findOne({\r\n        where: { id },\r\n        relations: [\r\n          'assessments',\r\n          'exceptions',\r\n          'affectedAssets',\r\n          'affectedAssets.group',\r\n        ],\r\n      });\r\n\r\n      if (!vulnerability) {\r\n        throw new NotFoundException('Vulnerability not found');\r\n      }\r\n\r\n      this.logger.debug('Vulnerability details retrieved', {\r\n        vulnerabilityId: id,\r\n        identifier: vulnerability.identifier,\r\n        severity: vulnerability.severity,\r\n      });\r\n\r\n      return vulnerability;\r\n    } catch (error) {\r\n      this.logger.error('Failed to get vulnerability details', {\r\n        vulnerabilityId: id,\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create vulnerability\r\n   */\r\n  async createVulnerability(vulnerabilityData: any, userId: string): Promise<Vulnerability> {\r\n    try {\r\n      this.logger.debug('Creating vulnerability', {\r\n        identifier: vulnerabilityData.identifier,\r\n        severity: vulnerabilityData.severity,\r\n        userId,\r\n      });\r\n\r\n      // Check if vulnerability already exists\r\n      const existing = await this.vulnerabilityRepository.findOne({\r\n        where: { identifier: vulnerabilityData.identifier },\r\n      });\r\n\r\n      if (existing) {\r\n        throw new Error('Vulnerability with this identifier already exists');\r\n      }\r\n\r\n      const vulnerability = this.vulnerabilityRepository.create({\r\n        ...vulnerabilityData,\r\n        publishedDate: vulnerabilityData.publishedDate || new Date(),\r\n        lastModifiedDate: vulnerabilityData.lastModifiedDate || new Date(),\r\n        dataSource: vulnerabilityData.dataSource || {\r\n          name: 'Manual Entry',\r\n          type: 'internal',\r\n          lastUpdated: new Date().toISOString(),\r\n          confidence: 'high',\r\n        },\r\n      });\r\n\r\n      const savedVulnerability = await this.vulnerabilityRepository.save(vulnerability);\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'create',\r\n        'vulnerability',\r\n        savedVulnerability.id,\r\n        {\r\n          identifier: vulnerabilityData.identifier,\r\n          severity: vulnerabilityData.severity,\r\n          title: vulnerabilityData.title,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Vulnerability created successfully', {\r\n        vulnerabilityId: savedVulnerability.id,\r\n        identifier: vulnerabilityData.identifier,\r\n        userId,\r\n      });\r\n\r\n      return savedVulnerability;\r\n    } catch (error) {\r\n      this.logger.error('Failed to create vulnerability', {\r\n        error: error.message,\r\n        vulnerabilityData,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update vulnerability\r\n   */\r\n  async updateVulnerability(id: string, updates: any, userId: string): Promise<Vulnerability> {\r\n    try {\r\n      const vulnerability = await this.vulnerabilityRepository.findOne({\r\n        where: { id },\r\n      });\r\n\r\n      if (!vulnerability) {\r\n        throw new NotFoundException('Vulnerability not found');\r\n      }\r\n\r\n      this.logger.debug('Updating vulnerability', {\r\n        vulnerabilityId: id,\r\n        identifier: vulnerability.identifier,\r\n        userId,\r\n      });\r\n\r\n      // Track changes for audit\r\n      const changes = {};\r\n      Object.keys(updates).forEach(key => {\r\n        if (vulnerability[key] !== updates[key]) {\r\n          changes[key] = {\r\n            from: vulnerability[key],\r\n            to: updates[key],\r\n          };\r\n        }\r\n      });\r\n\r\n      Object.assign(vulnerability, updates);\r\n      vulnerability.lastModifiedDate = new Date();\r\n\r\n      const savedVulnerability = await this.vulnerabilityRepository.save(vulnerability);\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'update',\r\n        'vulnerability',\r\n        id,\r\n        {\r\n          identifier: vulnerability.identifier,\r\n          changes,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Vulnerability updated successfully', {\r\n        vulnerabilityId: id,\r\n        identifier: vulnerability.identifier,\r\n        changesCount: Object.keys(changes).length,\r\n        userId,\r\n      });\r\n\r\n      return savedVulnerability;\r\n    } catch (error) {\r\n      this.logger.error('Failed to update vulnerability', {\r\n        vulnerabilityId: id,\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Delete vulnerability\r\n   */\r\n  async deleteVulnerability(id: string, userId: string): Promise<void> {\r\n    try {\r\n      const vulnerability = await this.vulnerabilityRepository.findOne({\r\n        where: { id },\r\n      });\r\n\r\n      if (!vulnerability) {\r\n        throw new NotFoundException('Vulnerability not found');\r\n      }\r\n\r\n      this.logger.debug('Deleting vulnerability', {\r\n        vulnerabilityId: id,\r\n        identifier: vulnerability.identifier,\r\n        userId,\r\n      });\r\n\r\n      await this.vulnerabilityRepository.remove(vulnerability);\r\n\r\n      await this.auditService.logUserAction(\r\n        userId,\r\n        'delete',\r\n        'vulnerability',\r\n        id,\r\n        {\r\n          identifier: vulnerability.identifier,\r\n          severity: vulnerability.severity,\r\n          title: vulnerability.title,\r\n        },\r\n      );\r\n\r\n      this.logger.log('Vulnerability deleted successfully', {\r\n        vulnerabilityId: id,\r\n        identifier: vulnerability.identifier,\r\n        userId,\r\n      });\r\n    } catch (error) {\r\n      this.logger.error('Failed to delete vulnerability', {\r\n        vulnerabilityId: id,\r\n        error: error.message,\r\n        userId,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Monitor vulnerabilities for critical updates\r\n   */\r\n  @Cron(CronExpression.EVERY_HOUR)\r\n  async monitorVulnerabilities(): Promise<void> {\r\n    try {\r\n      this.logger.debug('Starting vulnerability monitoring');\r\n\r\n      // Check for new critical vulnerabilities\r\n      await this.checkNewCriticalVulnerabilities();\r\n\r\n      // Check for exploit updates\r\n      await this.checkExploitUpdates();\r\n\r\n      // Check for patch updates\r\n      await this.checkPatchUpdates();\r\n\r\n      // Check for vulnerabilities in the wild\r\n      await this.checkInTheWildUpdates();\r\n\r\n      this.logger.log('Vulnerability monitoring completed');\r\n    } catch (error) {\r\n      this.logger.error('Failed to complete vulnerability monitoring', {\r\n        error: error.message,\r\n      });\r\n    }\r\n  }\r\n\r\n  // Private helper methods\r\n\r\n  private async getVulnerabilityTrends(): Promise<any[]> {\r\n    const trends = [];\r\n    const now = new Date();\r\n\r\n    for (let i = 11; i >= 0; i--) {\r\n      const startDate = new Date(now.getFullYear(), now.getMonth() - i, 1);\r\n      const endDate = new Date(now.getFullYear(), now.getMonth() - i + 1, 0);\r\n\r\n      const count = await this.vulnerabilityRepository.count({\r\n        where: { publishedDate: Between(startDate, endDate) },\r\n      });\r\n\r\n      trends.push({\r\n        month: startDate.toISOString().substring(0, 7),\r\n        count,\r\n      });\r\n    }\r\n\r\n    return trends;\r\n  }\r\n\r\n  private async getRiskDistribution(): Promise<any> {\r\n    const distribution = await this.vulnerabilityRepository\r\n      .createQueryBuilder('vuln')\r\n      .select('CASE WHEN vuln.cvss_score >= 9 THEN \\'critical\\' WHEN vuln.cvss_score >= 7 THEN \\'high\\' WHEN vuln.cvss_score >= 4 THEN \\'medium\\' ELSE \\'low\\' END', 'risk')\r\n      .addSelect('COUNT(*)', 'count')\r\n      .where('vuln.cvss_score IS NOT NULL')\r\n      .groupBy('risk')\r\n      .getRawMany();\r\n\r\n    return distribution.reduce((acc, item) => {\r\n      acc[item.risk] = parseInt(item.count);\r\n      return acc;\r\n    }, {});\r\n  }\r\n\r\n  private async checkNewCriticalVulnerabilities(): Promise<void> {\r\n    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);\r\n\r\n    const newCritical = await this.vulnerabilityRepository.find({\r\n      where: {\r\n        severity: 'critical',\r\n        publishedDate: Between(oneDayAgo, new Date()),\r\n      },\r\n    });\r\n\r\n    if (newCritical.length > 0) {\r\n      await this.notificationService.sendNewCriticalVulnerabilitiesAlert(newCritical);\r\n    }\r\n  }\r\n\r\n  private async checkExploitUpdates(): Promise<void> {\r\n    // Implementation would check for exploit updates from threat intelligence feeds\r\n    this.logger.debug('Checking for exploit updates');\r\n  }\r\n\r\n  private async checkPatchUpdates(): Promise<void> {\r\n    // Implementation would check for new patches from vendor feeds\r\n    this.logger.debug('Checking for patch updates');\r\n  }\r\n\r\n  private async checkInTheWildUpdates(): Promise<void> {\r\n    // Implementation would check threat intelligence for in-the-wild exploitation\r\n    this.logger.debug('Checking for in-the-wild updates');\r\n  }\r\n\r\n  /**\r\n   * Get vulnerability statistics\r\n   * @returns Vulnerability statistics\r\n   */\r\n  async getStatistics(): Promise<any> {\r\n    try {\r\n      const [\r\n        total,\r\n        critical,\r\n        high,\r\n        medium,\r\n        low,\r\n        open,\r\n        inProgress,\r\n        resolved,\r\n      ] = await Promise.all([\r\n        this.vulnerabilityRepository.count(),\r\n        this.vulnerabilityRepository.count({ where: { severity: 'critical' } }),\r\n        this.vulnerabilityRepository.count({ where: { severity: 'high' } }),\r\n        this.vulnerabilityRepository.count({ where: { severity: 'medium' } }),\r\n        this.vulnerabilityRepository.count({ where: { severity: 'low' } }),\r\n        this.vulnerabilityRepository.count({ where: { status: 'open' } }),\r\n        this.vulnerabilityRepository.count({ where: { status: 'in_progress' } }),\r\n        this.vulnerabilityRepository.count({ where: { status: 'resolved' } }),\r\n      ]);\r\n\r\n      return {\r\n        total,\r\n        bySeverity: {\r\n          critical,\r\n          high,\r\n          medium,\r\n          low,\r\n        },\r\n        byStatus: {\r\n          open,\r\n          inProgress,\r\n          resolved,\r\n        },\r\n        timestamp: new Date().toISOString(),\r\n      };\r\n    } catch (error) {\r\n      this.logger.error('Failed to get vulnerability statistics', {\r\n        error: error.message,\r\n      });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate risk score for a vulnerability\r\n   * @param vulnerability Vulnerability data\r\n   * @returns Calculated risk score\r\n   */\r\n  private calculateRiskScore(vulnerability: Partial<Vulnerability>): number {\r\n    let score = 0;\r\n\r\n    // Base score from CVSS\r\n    if (vulnerability.cvssScore) {\r\n      score = vulnerability.cvssScore;\r\n    } else {\r\n      // Fallback to severity-based scoring\r\n      const severityScores = {\r\n        critical: 9.0,\r\n        high: 7.0,\r\n        medium: 5.0,\r\n        low: 3.0,\r\n        info: 1.0,\r\n      };\r\n      score = severityScores[vulnerability.severity] || 5.0;\r\n    }\r\n\r\n    // Adjust for exploitability\r\n    if (vulnerability.isExploitable) {\r\n      score += 1.0;\r\n    }\r\n\r\n    if (vulnerability.hasPublicExploit) {\r\n      score += 1.5;\r\n    }\r\n\r\n    if (vulnerability.isActivelyExploited) {\r\n      score += 2.0;\r\n    }\r\n\r\n    // Ensure score is within valid range\r\n    return Math.min(10.0, Math.max(0.0, score));\r\n  }\r\n\r\n  /**\r\n   * Sanitize vulnerability data for logging\r\n   * @param data Vulnerability data\r\n   * @returns Sanitized data\r\n   */\r\n  private sanitizeVulnerabilityData(data: any): any {\r\n    const sanitized = { ...data };\r\n    \r\n    // Remove sensitive fields if any\r\n    delete sanitized.metadata;\r\n    \r\n    return sanitized;\r\n  }\r\n}\r\n"], "version": 3}