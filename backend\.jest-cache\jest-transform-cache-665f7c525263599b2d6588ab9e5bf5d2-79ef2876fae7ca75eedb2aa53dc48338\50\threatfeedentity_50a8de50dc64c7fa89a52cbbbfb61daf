60adc82f4b0fd7c5e6e28c0ce51dc5ec
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThreatFeed = void 0;
const typeorm_1 = require("typeorm");
const ioc_entity_1 = require("./ioc.entity");
/**
 * Threat Feed entity
 * Represents external threat intelligence feeds and their configuration
 */
let ThreatFeed = class ThreatFeed {
    /**
     * Check if feed is healthy
     */
    get isHealthy() {
        if (this.status !== 'active')
            return false;
        if (!this.lastSync)
            return false;
        const maxAge = this.syncFrequency * 2; // Allow 2x sync frequency
        const ageMinutes = (Date.now() - this.lastSync.getTime()) / (1000 * 60);
        return ageMinutes <= maxAge;
    }
    /**
     * Check if feed is due for sync
     */
    get isDueForSync() {
        if (!this.autoSync || this.status !== 'active')
            return false;
        if (!this.lastSync)
            return true;
        const ageMinutes = (Date.now() - this.lastSync.getTime()) / (1000 * 60);
        return ageMinutes >= this.syncFrequency;
    }
    /**
     * Get sync success rate
     */
    get syncSuccessRate() {
        if (!this.syncStats)
            return 0;
        const total = (this.syncStats.successfulSyncs || 0) + (this.syncStats.failedSyncs || 0);
        if (total === 0)
            return 0;
        return Math.round(((this.syncStats.successfulSyncs || 0) / total) * 100);
    }
    /**
     * Get average IOCs per sync
     */
    get averageIocsPerSync() {
        if (!this.syncStats || !this.syncStats.successfulSyncs)
            return 0;
        return Math.round(this.totalIocs / this.syncStats.successfulSyncs);
    }
    /**
     * Update sync statistics
     */
    updateSyncStats(success, duration, newIocs = 0, updatedIocs = 0, expiredIocs = 0, error) {
        if (!this.syncStats) {
            this.syncStats = {
                successfulSyncs: 0,
                failedSyncs: 0,
                newIocsLastSync: 0,
                updatedIocsLastSync: 0,
                expiredIocsLastSync: 0,
            };
        }
        this.lastSyncAttempt = new Date();
        if (success) {
            this.lastSync = new Date();
            this.lastSyncStatus = 'success';
            this.lastSyncError = null;
            this.syncStats.successfulSyncs = (this.syncStats.successfulSyncs || 0) + 1;
            this.syncStats.newIocsLastSync = newIocs;
            this.syncStats.updatedIocsLastSync = updatedIocs;
            this.syncStats.expiredIocsLastSync = expiredIocs;
        }
        else {
            this.lastSyncStatus = 'error';
            this.lastSyncError = error || 'Unknown error';
            this.syncStats.failedSyncs = (this.syncStats.failedSyncs || 0) + 1;
        }
        this.syncStats.lastSyncDuration = duration;
        // Calculate average duration
        const totalSyncs = (this.syncStats.successfulSyncs || 0) + (this.syncStats.failedSyncs || 0);
        if (totalSyncs > 1) {
            const currentAvg = this.syncStats.averageSyncDuration || duration;
            this.syncStats.averageSyncDuration = Math.round((currentAvg * (totalSyncs - 1) + duration) / totalSyncs);
        }
        else {
            this.syncStats.averageSyncDuration = duration;
        }
    }
    /**
     * Mark feed as healthy
     */
    markAsHealthy() {
        this.status = 'active';
    }
    /**
     * Mark feed as unhealthy
     */
    markAsUnhealthy(error) {
        this.status = 'error';
        this.lastSyncError = error;
    }
    /**
     * Get next sync time
     */
    get nextSyncTime() {
        if (!this.autoSync || !this.lastSync)
            return null;
        const nextSync = new Date(this.lastSync);
        nextSync.setMinutes(nextSync.getMinutes() + this.syncFrequency);
        return nextSync;
    }
};
exports.ThreatFeed = ThreatFeed;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ThreatFeed.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ unique: true, length: 255 }),
    __metadata("design:type", String)
], ThreatFeed.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'display_name', length: 255 }),
    __metadata("design:type", String)
], ThreatFeed.prototype, "displayName", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ThreatFeed.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['commercial', 'open_source', 'government', 'community', 'internal'],
        default: 'open_source',
    }),
    __metadata("design:type", String)
], ThreatFeed.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['stix', 'taxii', 'json', 'csv', 'xml', 'txt', 'api'],
        default: 'json',
    }),
    __metadata("design:type", String)
], ThreatFeed.prototype, "format", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ThreatFeed.prototype, "url", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['active', 'inactive', 'error', 'maintenance'],
        default: 'active',
    }),
    __metadata("design:type", String)
], ThreatFeed.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'reliability_score', type: 'integer', default: 50 }),
    __metadata("design:type", Number)
], ThreatFeed.prototype, "reliabilityScore", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'confidence_level', type: 'integer', default: 50 }),
    __metadata("design:type", Number)
], ThreatFeed.prototype, "confidenceLevel", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['white', 'green', 'amber', 'red'],
        default: 'white',
    }),
    __metadata("design:type", String)
], ThreatFeed.prototype, "tlp", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'sync_frequency', type: 'integer', default: 60 }),
    __metadata("design:type", Number)
], ThreatFeed.prototype, "syncFrequency", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'auto_sync', default: true }),
    __metadata("design:type", Boolean)
], ThreatFeed.prototype, "autoSync", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'auth_config', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ThreatFeed.prototype, "authConfig", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'parsing_config', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ThreatFeed.prototype, "parsingConfig", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'supported_ioc_types', type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], ThreatFeed.prototype, "supportedIocTypes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], ThreatFeed.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_sync', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], ThreatFeed.prototype, "lastSync", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_sync_attempt', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], ThreatFeed.prototype, "lastSyncAttempt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_sync_status', nullable: true }),
    __metadata("design:type", String)
], ThreatFeed.prototype, "lastSyncStatus", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_sync_error', type: 'text', nullable: true }),
    __metadata("design:type", String)
], ThreatFeed.prototype, "lastSyncError", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'total_iocs', type: 'integer', default: 0 }),
    __metadata("design:type", Number)
], ThreatFeed.prototype, "totalIocs", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'active_iocs', type: 'integer', default: 0 }),
    __metadata("design:type", Number)
], ThreatFeed.prototype, "activeIocs", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'sync_stats', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ThreatFeed.prototype, "syncStats", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'provider_info', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ThreatFeed.prototype, "providerInfo", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'rate_limit', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ThreatFeed.prototype, "rateLimit", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'retention_policy', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ThreatFeed.prototype, "retentionPolicy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_c = typeof Record !== "undefined" && Record) === "function" ? _c : Object)
], ThreatFeed.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], ThreatFeed.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], ThreatFeed.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], ThreatFeed.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_e = typeof Date !== "undefined" && Date) === "function" ? _e : Object)
], ThreatFeed.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => ioc_entity_1.IOC, ioc => ioc.threatFeed),
    __metadata("design:type", Array)
], ThreatFeed.prototype, "iocs", void 0);
exports.ThreatFeed = ThreatFeed = __decorate([
    (0, typeorm_1.Entity)('threat_feeds'),
    (0, typeorm_1.Index)(['name'], { unique: true }),
    (0, typeorm_1.Index)(['type']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['lastSync'])
], ThreatFeed);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxtb2R1bGVzXFx0aHJlYXQtaW50ZWxsaWdlbmNlXFxkb21haW5cXGVudGl0aWVzXFx0aHJlYXQtZmVlZC5lbnRpdHkudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUFBLHFDQVFpQjtBQUNqQiw2Q0FBbUM7QUFFbkM7OztHQUdHO0FBTUksSUFBTSxVQUFVLEdBQWhCLE1BQU0sVUFBVTtJQWlQckI7O09BRUc7SUFDSCxJQUFJLFNBQVM7UUFDWCxJQUFJLElBQUksQ0FBQyxNQUFNLEtBQUssUUFBUTtZQUFFLE9BQU8sS0FBSyxDQUFDO1FBRTNDLElBQUksQ0FBQyxJQUFJLENBQUMsUUFBUTtZQUFFLE9BQU8sS0FBSyxDQUFDO1FBRWpDLE1BQU0sTUFBTSxHQUFHLElBQUksQ0FBQyxhQUFhLEdBQUcsQ0FBQyxDQUFDLENBQUMsMEJBQTBCO1FBQ2pFLE1BQU0sVUFBVSxHQUFHLENBQUMsSUFBSSxDQUFDLEdBQUcsRUFBRSxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsT0FBTyxFQUFFLENBQUMsR0FBRyxDQUFDLElBQUksR0FBRyxFQUFFLENBQUMsQ0FBQztRQUV4RSxPQUFPLFVBQVUsSUFBSSxNQUFNLENBQUM7SUFDOUIsQ0FBQztJQUVEOztPQUVHO0lBQ0gsSUFBSSxZQUFZO1FBQ2QsSUFBSSxDQUFDLElBQUksQ0FBQyxRQUFRLElBQUksSUFBSSxDQUFDLE1BQU0sS0FBSyxRQUFRO1lBQUUsT0FBTyxLQUFLLENBQUM7UUFFN0QsSUFBSSxDQUFDLElBQUksQ0FBQyxRQUFRO1lBQUUsT0FBTyxJQUFJLENBQUM7UUFFaEMsTUFBTSxVQUFVLEdBQUcsQ0FBQyxJQUFJLENBQUMsR0FBRyxFQUFFLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxHQUFHLENBQUMsSUFBSSxHQUFHLEVBQUUsQ0FBQyxDQUFDO1FBQ3hFLE9BQU8sVUFBVSxJQUFJLElBQUksQ0FBQyxhQUFhLENBQUM7SUFDMUMsQ0FBQztJQUVEOztPQUVHO0lBQ0gsSUFBSSxlQUFlO1FBQ2pCLElBQUksQ0FBQyxJQUFJLENBQUMsU0FBUztZQUFFLE9BQU8sQ0FBQyxDQUFDO1FBRTlCLE1BQU0sS0FBSyxHQUFHLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxlQUFlLElBQUksQ0FBQyxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLFdBQVcsSUFBSSxDQUFDLENBQUMsQ0FBQztRQUN4RixJQUFJLEtBQUssS0FBSyxDQUFDO1lBQUUsT0FBTyxDQUFDLENBQUM7UUFFMUIsT0FBTyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGVBQWUsSUFBSSxDQUFDLENBQUMsR0FBRyxLQUFLLENBQUMsR0FBRyxHQUFHLENBQUMsQ0FBQztJQUMzRSxDQUFDO0lBRUQ7O09BRUc7SUFDSCxJQUFJLGtCQUFrQjtRQUNwQixJQUFJLENBQUMsSUFBSSxDQUFDLFNBQVMsSUFBSSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsZUFBZTtZQUFFLE9BQU8sQ0FBQyxDQUFDO1FBRWpFLE9BQU8sSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsU0FBUyxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsZUFBZSxDQUFDLENBQUM7SUFDckUsQ0FBQztJQUVEOztPQUVHO0lBQ0gsZUFBZSxDQUNiLE9BQWdCLEVBQ2hCLFFBQWdCLEVBQ2hCLFVBQWtCLENBQUMsRUFDbkIsY0FBc0IsQ0FBQyxFQUN2QixjQUFzQixDQUFDLEVBQ3ZCLEtBQWM7UUFFZCxJQUFJLENBQUMsSUFBSSxDQUFDLFNBQVMsRUFBRSxDQUFDO1lBQ3BCLElBQUksQ0FBQyxTQUFTLEdBQUc7Z0JBQ2YsZUFBZSxFQUFFLENBQUM7Z0JBQ2xCLFdBQVcsRUFBRSxDQUFDO2dCQUNkLGVBQWUsRUFBRSxDQUFDO2dCQUNsQixtQkFBbUIsRUFBRSxDQUFDO2dCQUN0QixtQkFBbUIsRUFBRSxDQUFDO2FBQ3ZCLENBQUM7UUFDSixDQUFDO1FBRUQsSUFBSSxDQUFDLGVBQWUsR0FBRyxJQUFJLElBQUksRUFBRSxDQUFDO1FBRWxDLElBQUksT0FBTyxFQUFFLENBQUM7WUFDWixJQUFJLENBQUMsUUFBUSxHQUFHLElBQUksSUFBSSxFQUFFLENBQUM7WUFDM0IsSUFBSSxDQUFDLGNBQWMsR0FBRyxTQUFTLENBQUM7WUFDaEMsSUFBSSxDQUFDLGFBQWEsR0FBRyxJQUFJLENBQUM7WUFDMUIsSUFBSSxDQUFDLFNBQVMsQ0FBQyxlQUFlLEdBQUcsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGVBQWUsSUFBSSxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUM7WUFDM0UsSUFBSSxDQUFDLFNBQVMsQ0FBQyxlQUFlLEdBQUcsT0FBTyxDQUFDO1lBQ3pDLElBQUksQ0FBQyxTQUFTLENBQUMsbUJBQW1CLEdBQUcsV0FBVyxDQUFDO1lBQ2pELElBQUksQ0FBQyxTQUFTLENBQUMsbUJBQW1CLEdBQUcsV0FBVyxDQUFDO1FBQ25ELENBQUM7YUFBTSxDQUFDO1lBQ04sSUFBSSxDQUFDLGNBQWMsR0FBRyxPQUFPLENBQUM7WUFDOUIsSUFBSSxDQUFDLGFBQWEsR0FBRyxLQUFLLElBQUksZUFBZSxDQUFDO1lBQzlDLElBQUksQ0FBQyxTQUFTLENBQUMsV0FBVyxHQUFHLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxXQUFXLElBQUksQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDO1FBQ3JFLENBQUM7UUFFRCxJQUFJLENBQUMsU0FBUyxDQUFDLGdCQUFnQixHQUFHLFFBQVEsQ0FBQztRQUUzQyw2QkFBNkI7UUFDN0IsTUFBTSxVQUFVLEdBQUcsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGVBQWUsSUFBSSxDQUFDLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsV0FBVyxJQUFJLENBQUMsQ0FBQyxDQUFDO1FBQzdGLElBQUksVUFBVSxHQUFHLENBQUMsRUFBRSxDQUFDO1lBQ25CLE1BQU0sVUFBVSxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsbUJBQW1CLElBQUksUUFBUSxDQUFDO1lBQ2xFLElBQUksQ0FBQyxTQUFTLENBQUMsbUJBQW1CLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FDN0MsQ0FBQyxVQUFVLEdBQUcsQ0FBQyxVQUFVLEdBQUcsQ0FBQyxDQUFDLEdBQUcsUUFBUSxDQUFDLEdBQUcsVUFBVSxDQUN4RCxDQUFDO1FBQ0osQ0FBQzthQUFNLENBQUM7WUFDTixJQUFJLENBQUMsU0FBUyxDQUFDLG1CQUFtQixHQUFHLFFBQVEsQ0FBQztRQUNoRCxDQUFDO0lBQ0gsQ0FBQztJQUVEOztPQUVHO0lBQ0gsYUFBYTtRQUNYLElBQUksQ0FBQyxNQUFNLEdBQUcsUUFBUSxDQUFDO0lBQ3pCLENBQUM7SUFFRDs7T0FFRztJQUNILGVBQWUsQ0FBQyxLQUFjO1FBQzVCLElBQUksQ0FBQyxNQUFNLEdBQUcsT0FBTyxDQUFDO1FBQ3RCLElBQUksQ0FBQyxhQUFhLEdBQUcsS0FBSyxDQUFDO0lBQzdCLENBQUM7SUFFRDs7T0FFRztJQUNILElBQUksWUFBWTtRQUNkLElBQUksQ0FBQyxJQUFJLENBQUMsUUFBUSxJQUFJLENBQUMsSUFBSSxDQUFDLFFBQVE7WUFBRSxPQUFPLElBQUksQ0FBQztRQUVsRCxNQUFNLFFBQVEsR0FBRyxJQUFJLElBQUksQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUM7UUFDekMsUUFBUSxDQUFDLFVBQVUsQ0FBQyxRQUFRLENBQUMsVUFBVSxFQUFFLEdBQUcsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDO1FBRWhFLE9BQU8sUUFBUSxDQUFDO0lBQ2xCLENBQUM7Q0FDRixDQUFBO0FBN1dZLGdDQUFVO0FBRXJCO0lBREMsSUFBQSxnQ0FBc0IsRUFBQyxNQUFNLENBQUM7O3NDQUNwQjtBQU1YO0lBREMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsTUFBTSxFQUFFLElBQUksRUFBRSxNQUFNLEVBQUUsR0FBRyxFQUFFLENBQUM7O3dDQUN6QjtBQU1iO0lBREMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLGNBQWMsRUFBRSxNQUFNLEVBQUUsR0FBRyxFQUFFLENBQUM7OytDQUMxQjtBQU1wQjtJQURDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxNQUFNLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDOzsrQ0FDcEI7QUFVckI7SUFMQyxJQUFBLGdCQUFNLEVBQUM7UUFDTixJQUFJLEVBQUUsTUFBTTtRQUNaLElBQUksRUFBRSxDQUFDLFlBQVksRUFBRSxhQUFhLEVBQUUsWUFBWSxFQUFFLFdBQVcsRUFBRSxVQUFVLENBQUM7UUFDMUUsT0FBTyxFQUFFLGFBQWE7S0FDdkIsQ0FBQzs7d0NBQzJFO0FBVTdFO0lBTEMsSUFBQSxnQkFBTSxFQUFDO1FBQ04sSUFBSSxFQUFFLE1BQU07UUFDWixJQUFJLEVBQUUsQ0FBQyxNQUFNLEVBQUUsT0FBTyxFQUFFLE1BQU0sRUFBRSxLQUFLLEVBQUUsS0FBSyxFQUFFLEtBQUssRUFBRSxLQUFLLENBQUM7UUFDM0QsT0FBTyxFQUFFLE1BQU07S0FDaEIsQ0FBQzs7MENBQ2dFO0FBTWxFO0lBREMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDOzt1Q0FDZDtBQVViO0lBTEMsSUFBQSxnQkFBTSxFQUFDO1FBQ04sSUFBSSxFQUFFLE1BQU07UUFDWixJQUFJLEVBQUUsQ0FBQyxRQUFRLEVBQUUsVUFBVSxFQUFFLE9BQU8sRUFBRSxhQUFhLENBQUM7UUFDcEQsT0FBTyxFQUFFLFFBQVE7S0FDbEIsQ0FBQzs7MENBQ3NEO0FBTXhEO0lBREMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLG1CQUFtQixFQUFFLElBQUksRUFBRSxTQUFTLEVBQUUsT0FBTyxFQUFFLEVBQUUsRUFBRSxDQUFDOztvREFDM0M7QUFNekI7SUFEQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUsa0JBQWtCLEVBQUUsSUFBSSxFQUFFLFNBQVMsRUFBRSxPQUFPLEVBQUUsRUFBRSxFQUFFLENBQUM7O21EQUMzQztBQVV4QjtJQUxDLElBQUEsZ0JBQU0sRUFBQztRQUNOLElBQUksRUFBRSxNQUFNO1FBQ1osSUFBSSxFQUFFLENBQUMsT0FBTyxFQUFFLE9BQU8sRUFBRSxPQUFPLEVBQUUsS0FBSyxDQUFDO1FBQ3hDLE9BQU8sRUFBRSxPQUFPO0tBQ2pCLENBQUM7O3VDQUN1QztBQU16QztJQURDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxnQkFBZ0IsRUFBRSxJQUFJLEVBQUUsU0FBUyxFQUFFLE9BQU8sRUFBRSxFQUFFLEVBQUUsQ0FBQzs7aURBQzNDO0FBTXRCO0lBREMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLFdBQVcsRUFBRSxPQUFPLEVBQUUsSUFBSSxFQUFFLENBQUM7OzRDQUMzQjtBQU1sQjtJQURDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxhQUFhLEVBQUUsSUFBSSxFQUFFLE9BQU8sRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7OzhDQVE3RDtBQU1GO0lBREMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLGdCQUFnQixFQUFFLElBQUksRUFBRSxPQUFPLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDOztpREFRaEU7QUFNRjtJQURDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxxQkFBcUIsRUFBRSxJQUFJLEVBQUUsT0FBTyxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQzs7cURBQzFDO0FBTTdCO0lBREMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLE9BQU8sRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7O3dDQUMxQjtBQU1oQjtJQURDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxXQUFXLEVBQUUsSUFBSSxFQUFFLDBCQUEwQixFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQztrREFDckUsSUFBSSxvQkFBSixJQUFJOzRDQUFDO0FBTWhCO0lBREMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLG1CQUFtQixFQUFFLElBQUksRUFBRSwwQkFBMEIsRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7a0RBQ3RFLElBQUksb0JBQUosSUFBSTttREFBQztBQU12QjtJQURDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxrQkFBa0IsRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7O2tEQUM3QjtBQU14QjtJQURDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxpQkFBaUIsRUFBRSxJQUFJLEVBQUUsTUFBTSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQzs7aURBQzNDO0FBTXZCO0lBREMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLFlBQVksRUFBRSxJQUFJLEVBQUUsU0FBUyxFQUFFLE9BQU8sRUFBRSxDQUFDLEVBQUUsQ0FBQzs7NkNBQzFDO0FBTWxCO0lBREMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLGFBQWEsRUFBRSxJQUFJLEVBQUUsU0FBUyxFQUFFLE9BQU8sRUFBRSxDQUFDLEVBQUUsQ0FBQzs7OENBQzFDO0FBTW5CO0lBREMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLFlBQVksRUFBRSxJQUFJLEVBQUUsT0FBTyxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQzs7NkNBUzVEO0FBTUY7SUFEQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUsZUFBZSxFQUFFLElBQUksRUFBRSxPQUFPLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDOztnREFPL0Q7QUFNRjtJQURDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxZQUFZLEVBQUUsSUFBSSxFQUFFLE9BQU8sRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7OzZDQU01RDtBQU1GO0lBREMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLGtCQUFrQixFQUFFLElBQUksRUFBRSxPQUFPLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDOzttREFLbEU7QUFNRjtJQURDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxPQUFPLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDO2tEQUMvQixNQUFNLG9CQUFOLE1BQU07NENBQWM7QUFNL0I7SUFEQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUsWUFBWSxFQUFFLElBQUksRUFBRSxNQUFNLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDOzs2Q0FDMUM7QUFNbkI7SUFEQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUsWUFBWSxFQUFFLElBQUksRUFBRSxNQUFNLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDOzs2Q0FDMUM7QUFHbkI7SUFEQyxJQUFBLDBCQUFnQixFQUFDLEVBQUUsSUFBSSxFQUFFLFlBQVksRUFBRSxDQUFDO2tEQUM5QixJQUFJLG9CQUFKLElBQUk7NkNBQUM7QUFHaEI7SUFEQyxJQUFBLDBCQUFnQixFQUFDLEVBQUUsSUFBSSxFQUFFLFlBQVksRUFBRSxDQUFDO2tEQUM5QixJQUFJLG9CQUFKLElBQUk7NkNBQUM7QUFJaEI7SUFEQyxJQUFBLG1CQUFTLEVBQUMsR0FBRyxFQUFFLENBQUMsZ0JBQUcsRUFBRSxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsQ0FBQyxVQUFVLENBQUM7O3dDQUNoQztxQkEvT0QsVUFBVTtJQUx0QixJQUFBLGdCQUFNLEVBQUMsY0FBYyxDQUFDO0lBQ3RCLElBQUEsZUFBSyxFQUFDLENBQUMsTUFBTSxDQUFDLEVBQUUsRUFBRSxNQUFNLEVBQUUsSUFBSSxFQUFFLENBQUM7SUFDakMsSUFBQSxlQUFLLEVBQUMsQ0FBQyxNQUFNLENBQUMsQ0FBQztJQUNmLElBQUEsZUFBSyxFQUFDLENBQUMsUUFBUSxDQUFDLENBQUM7SUFDakIsSUFBQSxlQUFLLEVBQUMsQ0FBQyxVQUFVLENBQUMsQ0FBQztHQUNQLFVBQVUsQ0E2V3RCIiwibmFtZXMiOltdLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTHVrYVxcc2VudGluZWxcXGJhY2tlbmRcXHNyY1xcbW9kdWxlc1xcdGhyZWF0LWludGVsbGlnZW5jZVxcZG9tYWluXFxlbnRpdGllc1xcdGhyZWF0LWZlZWQuZW50aXR5LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7XHJcbiAgRW50aXR5LFxyXG4gIFByaW1hcnlHZW5lcmF0ZWRDb2x1bW4sXHJcbiAgQ29sdW1uLFxyXG4gIENyZWF0ZURhdGVDb2x1bW4sXHJcbiAgVXBkYXRlRGF0ZUNvbHVtbixcclxuICBJbmRleCxcclxuICBPbmVUb01hbnksXHJcbn0gZnJvbSAndHlwZW9ybSc7XHJcbmltcG9ydCB7IElPQyB9IGZyb20gJy4vaW9jLmVudGl0eSc7XHJcblxyXG4vKipcclxuICogVGhyZWF0IEZlZWQgZW50aXR5XHJcbiAqIFJlcHJlc2VudHMgZXh0ZXJuYWwgdGhyZWF0IGludGVsbGlnZW5jZSBmZWVkcyBhbmQgdGhlaXIgY29uZmlndXJhdGlvblxyXG4gKi9cclxuQEVudGl0eSgndGhyZWF0X2ZlZWRzJylcclxuQEluZGV4KFsnbmFtZSddLCB7IHVuaXF1ZTogdHJ1ZSB9KVxyXG5ASW5kZXgoWyd0eXBlJ10pXHJcbkBJbmRleChbJ3N0YXR1cyddKVxyXG5ASW5kZXgoWydsYXN0U3luYyddKVxyXG5leHBvcnQgY2xhc3MgVGhyZWF0RmVlZCB7XHJcbiAgQFByaW1hcnlHZW5lcmF0ZWRDb2x1bW4oJ3V1aWQnKVxyXG4gIGlkOiBzdHJpbmc7XHJcblxyXG4gIC8qKlxyXG4gICAqIEZlZWQgbmFtZVxyXG4gICAqL1xyXG4gIEBDb2x1bW4oeyB1bmlxdWU6IHRydWUsIGxlbmd0aDogMjU1IH0pXHJcbiAgbmFtZTogc3RyaW5nO1xyXG5cclxuICAvKipcclxuICAgKiBGZWVkIGRpc3BsYXkgbmFtZVxyXG4gICAqL1xyXG4gIEBDb2x1bW4oeyBuYW1lOiAnZGlzcGxheV9uYW1lJywgbGVuZ3RoOiAyNTUgfSlcclxuICBkaXNwbGF5TmFtZTogc3RyaW5nO1xyXG5cclxuICAvKipcclxuICAgKiBGZWVkIGRlc2NyaXB0aW9uXHJcbiAgICovXHJcbiAgQENvbHVtbih7IHR5cGU6ICd0ZXh0JywgbnVsbGFibGU6IHRydWUgfSlcclxuICBkZXNjcmlwdGlvbj86IHN0cmluZztcclxuXHJcbiAgLyoqXHJcbiAgICogRmVlZCB0eXBlXHJcbiAgICovXHJcbiAgQENvbHVtbih7XHJcbiAgICB0eXBlOiAnZW51bScsXHJcbiAgICBlbnVtOiBbJ2NvbW1lcmNpYWwnLCAnb3Blbl9zb3VyY2UnLCAnZ292ZXJubWVudCcsICdjb21tdW5pdHknLCAnaW50ZXJuYWwnXSxcclxuICAgIGRlZmF1bHQ6ICdvcGVuX3NvdXJjZScsXHJcbiAgfSlcclxuICB0eXBlOiAnY29tbWVyY2lhbCcgfCAnb3Blbl9zb3VyY2UnIHwgJ2dvdmVybm1lbnQnIHwgJ2NvbW11bml0eScgfCAnaW50ZXJuYWwnO1xyXG5cclxuICAvKipcclxuICAgKiBGZWVkIGZvcm1hdFxyXG4gICAqL1xyXG4gIEBDb2x1bW4oe1xyXG4gICAgdHlwZTogJ2VudW0nLFxyXG4gICAgZW51bTogWydzdGl4JywgJ3RheGlpJywgJ2pzb24nLCAnY3N2JywgJ3htbCcsICd0eHQnLCAnYXBpJ10sXHJcbiAgICBkZWZhdWx0OiAnanNvbicsXHJcbiAgfSlcclxuICBmb3JtYXQ6ICdzdGl4JyB8ICd0YXhpaScgfCAnanNvbicgfCAnY3N2JyB8ICd4bWwnIHwgJ3R4dCcgfCAnYXBpJztcclxuXHJcbiAgLyoqXHJcbiAgICogRmVlZCBVUkwgb3IgZW5kcG9pbnRcclxuICAgKi9cclxuICBAQ29sdW1uKHsgbnVsbGFibGU6IHRydWUgfSlcclxuICB1cmw/OiBzdHJpbmc7XHJcblxyXG4gIC8qKlxyXG4gICAqIEZlZWQgc3RhdHVzXHJcbiAgICovXHJcbiAgQENvbHVtbih7XHJcbiAgICB0eXBlOiAnZW51bScsXHJcbiAgICBlbnVtOiBbJ2FjdGl2ZScsICdpbmFjdGl2ZScsICdlcnJvcicsICdtYWludGVuYW5jZSddLFxyXG4gICAgZGVmYXVsdDogJ2FjdGl2ZScsXHJcbiAgfSlcclxuICBzdGF0dXM6ICdhY3RpdmUnIHwgJ2luYWN0aXZlJyB8ICdlcnJvcicgfCAnbWFpbnRlbmFuY2UnO1xyXG5cclxuICAvKipcclxuICAgKiBGZWVkIHJlbGlhYmlsaXR5IHNjb3JlICgwLTEwMClcclxuICAgKi9cclxuICBAQ29sdW1uKHsgbmFtZTogJ3JlbGlhYmlsaXR5X3Njb3JlJywgdHlwZTogJ2ludGVnZXInLCBkZWZhdWx0OiA1MCB9KVxyXG4gIHJlbGlhYmlsaXR5U2NvcmU6IG51bWJlcjtcclxuXHJcbiAgLyoqXHJcbiAgICogRmVlZCBjb25maWRlbmNlIGxldmVsICgwLTEwMClcclxuICAgKi9cclxuICBAQ29sdW1uKHsgbmFtZTogJ2NvbmZpZGVuY2VfbGV2ZWwnLCB0eXBlOiAnaW50ZWdlcicsIGRlZmF1bHQ6IDUwIH0pXHJcbiAgY29uZmlkZW5jZUxldmVsOiBudW1iZXI7XHJcblxyXG4gIC8qKlxyXG4gICAqIFRMUCAoVHJhZmZpYyBMaWdodCBQcm90b2NvbCkgY2xhc3NpZmljYXRpb25cclxuICAgKi9cclxuICBAQ29sdW1uKHtcclxuICAgIHR5cGU6ICdlbnVtJyxcclxuICAgIGVudW06IFsnd2hpdGUnLCAnZ3JlZW4nLCAnYW1iZXInLCAncmVkJ10sXHJcbiAgICBkZWZhdWx0OiAnd2hpdGUnLFxyXG4gIH0pXHJcbiAgdGxwOiAnd2hpdGUnIHwgJ2dyZWVuJyB8ICdhbWJlcicgfCAncmVkJztcclxuXHJcbiAgLyoqXHJcbiAgICogU3luYyBmcmVxdWVuY3kgaW4gbWludXRlc1xyXG4gICAqL1xyXG4gIEBDb2x1bW4oeyBuYW1lOiAnc3luY19mcmVxdWVuY3knLCB0eXBlOiAnaW50ZWdlcicsIGRlZmF1bHQ6IDYwIH0pXHJcbiAgc3luY0ZyZXF1ZW5jeTogbnVtYmVyO1xyXG5cclxuICAvKipcclxuICAgKiBXaGV0aGVyIGF1dG8tc3luYyBpcyBlbmFibGVkXHJcbiAgICovXHJcbiAgQENvbHVtbih7IG5hbWU6ICdhdXRvX3N5bmMnLCBkZWZhdWx0OiB0cnVlIH0pXHJcbiAgYXV0b1N5bmM6IGJvb2xlYW47XHJcblxyXG4gIC8qKlxyXG4gICAqIEF1dGhlbnRpY2F0aW9uIGNvbmZpZ3VyYXRpb25cclxuICAgKi9cclxuICBAQ29sdW1uKHsgbmFtZTogJ2F1dGhfY29uZmlnJywgdHlwZTogJ2pzb25iJywgbnVsbGFibGU6IHRydWUgfSlcclxuICBhdXRoQ29uZmlnPzoge1xyXG4gICAgdHlwZTogJ25vbmUnIHwgJ2Jhc2ljJyB8ICdiZWFyZXInIHwgJ2FwaV9rZXknIHwgJ29hdXRoJztcclxuICAgIHVzZXJuYW1lPzogc3RyaW5nO1xyXG4gICAgcGFzc3dvcmQ/OiBzdHJpbmc7XHJcbiAgICB0b2tlbj86IHN0cmluZztcclxuICAgIGFwaUtleT86IHN0cmluZztcclxuICAgIGhlYWRlcnM/OiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+O1xyXG4gIH07XHJcblxyXG4gIC8qKlxyXG4gICAqIEZlZWQgcGFyc2luZyBjb25maWd1cmF0aW9uXHJcbiAgICovXHJcbiAgQENvbHVtbih7IG5hbWU6ICdwYXJzaW5nX2NvbmZpZycsIHR5cGU6ICdqc29uYicsIG51bGxhYmxlOiB0cnVlIH0pXHJcbiAgcGFyc2luZ0NvbmZpZz86IHtcclxuICAgIGRlbGltaXRlcj86IHN0cmluZztcclxuICAgIHNraXBMaW5lcz86IG51bWJlcjtcclxuICAgIGNvbHVtbnM/OiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+O1xyXG4gICAganNvblBhdGg/OiBzdHJpbmc7XHJcbiAgICB4bWxQYXRoPzogc3RyaW5nO1xyXG4gICAgZmlsdGVycz86IFJlY29yZDxzdHJpbmcsIGFueT47XHJcbiAgfTtcclxuXHJcbiAgLyoqXHJcbiAgICogSU9DIHR5cGVzIHN1cHBvcnRlZCBieSB0aGlzIGZlZWRcclxuICAgKi9cclxuICBAQ29sdW1uKHsgbmFtZTogJ3N1cHBvcnRlZF9pb2NfdHlwZXMnLCB0eXBlOiAnanNvbmInLCBudWxsYWJsZTogdHJ1ZSB9KVxyXG4gIHN1cHBvcnRlZElvY1R5cGVzPzogc3RyaW5nW107XHJcblxyXG4gIC8qKlxyXG4gICAqIEZlZWQgdGFnc1xyXG4gICAqL1xyXG4gIEBDb2x1bW4oeyB0eXBlOiAnanNvbmInLCBudWxsYWJsZTogdHJ1ZSB9KVxyXG4gIHRhZ3M/OiBzdHJpbmdbXTtcclxuXHJcbiAgLyoqXHJcbiAgICogTGFzdCBzdWNjZXNzZnVsIHN5bmMgdGltZXN0YW1wXHJcbiAgICovXHJcbiAgQENvbHVtbih7IG5hbWU6ICdsYXN0X3N5bmMnLCB0eXBlOiAndGltZXN0YW1wIHdpdGggdGltZSB6b25lJywgbnVsbGFibGU6IHRydWUgfSlcclxuICBsYXN0U3luYz86IERhdGU7XHJcblxyXG4gIC8qKlxyXG4gICAqIExhc3Qgc3luYyBhdHRlbXB0IHRpbWVzdGFtcFxyXG4gICAqL1xyXG4gIEBDb2x1bW4oeyBuYW1lOiAnbGFzdF9zeW5jX2F0dGVtcHQnLCB0eXBlOiAndGltZXN0YW1wIHdpdGggdGltZSB6b25lJywgbnVsbGFibGU6IHRydWUgfSlcclxuICBsYXN0U3luY0F0dGVtcHQ/OiBEYXRlO1xyXG5cclxuICAvKipcclxuICAgKiBMYXN0IHN5bmMgc3RhdHVzXHJcbiAgICovXHJcbiAgQENvbHVtbih7IG5hbWU6ICdsYXN0X3N5bmNfc3RhdHVzJywgbnVsbGFibGU6IHRydWUgfSlcclxuICBsYXN0U3luY1N0YXR1cz86IHN0cmluZztcclxuXHJcbiAgLyoqXHJcbiAgICogTGFzdCBzeW5jIGVycm9yIG1lc3NhZ2VcclxuICAgKi9cclxuICBAQ29sdW1uKHsgbmFtZTogJ2xhc3Rfc3luY19lcnJvcicsIHR5cGU6ICd0ZXh0JywgbnVsbGFibGU6IHRydWUgfSlcclxuICBsYXN0U3luY0Vycm9yPzogc3RyaW5nO1xyXG5cclxuICAvKipcclxuICAgKiBUb3RhbCBJT0NzIGltcG9ydGVkIGZyb20gdGhpcyBmZWVkXHJcbiAgICovXHJcbiAgQENvbHVtbih7IG5hbWU6ICd0b3RhbF9pb2NzJywgdHlwZTogJ2ludGVnZXInLCBkZWZhdWx0OiAwIH0pXHJcbiAgdG90YWxJb2NzOiBudW1iZXI7XHJcblxyXG4gIC8qKlxyXG4gICAqIEFjdGl2ZSBJT0NzIGZyb20gdGhpcyBmZWVkXHJcbiAgICovXHJcbiAgQENvbHVtbih7IG5hbWU6ICdhY3RpdmVfaW9jcycsIHR5cGU6ICdpbnRlZ2VyJywgZGVmYXVsdDogMCB9KVxyXG4gIGFjdGl2ZUlvY3M6IG51bWJlcjtcclxuXHJcbiAgLyoqXHJcbiAgICogU3luYyBzdGF0aXN0aWNzXHJcbiAgICovXHJcbiAgQENvbHVtbih7IG5hbWU6ICdzeW5jX3N0YXRzJywgdHlwZTogJ2pzb25iJywgbnVsbGFibGU6IHRydWUgfSlcclxuICBzeW5jU3RhdHM/OiB7XHJcbiAgICBsYXN0U3luY0R1cmF0aW9uPzogbnVtYmVyO1xyXG4gICAgYXZlcmFnZVN5bmNEdXJhdGlvbj86IG51bWJlcjtcclxuICAgIHN1Y2Nlc3NmdWxTeW5jcz86IG51bWJlcjtcclxuICAgIGZhaWxlZFN5bmNzPzogbnVtYmVyO1xyXG4gICAgbmV3SW9jc0xhc3RTeW5jPzogbnVtYmVyO1xyXG4gICAgdXBkYXRlZElvY3NMYXN0U3luYz86IG51bWJlcjtcclxuICAgIGV4cGlyZWRJb2NzTGFzdFN5bmM/OiBudW1iZXI7XHJcbiAgfTtcclxuXHJcbiAgLyoqXHJcbiAgICogRmVlZCBwcm92aWRlciBpbmZvcm1hdGlvblxyXG4gICAqL1xyXG4gIEBDb2x1bW4oeyBuYW1lOiAncHJvdmlkZXJfaW5mbycsIHR5cGU6ICdqc29uYicsIG51bGxhYmxlOiB0cnVlIH0pXHJcbiAgcHJvdmlkZXJJbmZvPzoge1xyXG4gICAgbmFtZT86IHN0cmluZztcclxuICAgIHdlYnNpdGU/OiBzdHJpbmc7XHJcbiAgICBjb250YWN0Pzogc3RyaW5nO1xyXG4gICAgbGljZW5zZT86IHN0cmluZztcclxuICAgIHRlcm1zPzogc3RyaW5nO1xyXG4gIH07XHJcblxyXG4gIC8qKlxyXG4gICAqIFJhdGUgbGltaXRpbmcgY29uZmlndXJhdGlvblxyXG4gICAqL1xyXG4gIEBDb2x1bW4oeyBuYW1lOiAncmF0ZV9saW1pdCcsIHR5cGU6ICdqc29uYicsIG51bGxhYmxlOiB0cnVlIH0pXHJcbiAgcmF0ZUxpbWl0Pzoge1xyXG4gICAgcmVxdWVzdHNQZXJNaW51dGU/OiBudW1iZXI7XHJcbiAgICByZXF1ZXN0c1BlckhvdXI/OiBudW1iZXI7XHJcbiAgICByZXF1ZXN0c1BlckRheT86IG51bWJlcjtcclxuICAgIGJhY2tvZmZTdHJhdGVneT86ICdsaW5lYXInIHwgJ2V4cG9uZW50aWFsJztcclxuICB9O1xyXG5cclxuICAvKipcclxuICAgKiBEYXRhIHJldGVudGlvbiBwb2xpY3lcclxuICAgKi9cclxuICBAQ29sdW1uKHsgbmFtZTogJ3JldGVudGlvbl9wb2xpY3knLCB0eXBlOiAnanNvbmInLCBudWxsYWJsZTogdHJ1ZSB9KVxyXG4gIHJldGVudGlvblBvbGljeT86IHtcclxuICAgIG1heEFnZT86IG51bWJlcjsgLy8gZGF5c1xyXG4gICAgbWF4UmVjb3Jkcz86IG51bWJlcjtcclxuICAgIGF1dG9DbGVhbnVwPzogYm9vbGVhbjtcclxuICB9O1xyXG5cclxuICAvKipcclxuICAgKiBBZGRpdGlvbmFsIG1ldGFkYXRhXHJcbiAgICovXHJcbiAgQENvbHVtbih7IHR5cGU6ICdqc29uYicsIG51bGxhYmxlOiB0cnVlIH0pXHJcbiAgbWV0YWRhdGE/OiBSZWNvcmQ8c3RyaW5nLCBhbnk+O1xyXG5cclxuICAvKipcclxuICAgKiBVc2VyIHdobyBjcmVhdGVkIHRoaXMgZmVlZFxyXG4gICAqL1xyXG4gIEBDb2x1bW4oeyBuYW1lOiAnY3JlYXRlZF9ieScsIHR5cGU6ICd1dWlkJywgbnVsbGFibGU6IHRydWUgfSlcclxuICBjcmVhdGVkQnk/OiBzdHJpbmc7XHJcblxyXG4gIC8qKlxyXG4gICAqIFVzZXIgd2hvIGxhc3QgdXBkYXRlZCB0aGlzIGZlZWRcclxuICAgKi9cclxuICBAQ29sdW1uKHsgbmFtZTogJ3VwZGF0ZWRfYnknLCB0eXBlOiAndXVpZCcsIG51bGxhYmxlOiB0cnVlIH0pXHJcbiAgdXBkYXRlZEJ5Pzogc3RyaW5nO1xyXG5cclxuICBAQ3JlYXRlRGF0ZUNvbHVtbih7IG5hbWU6ICdjcmVhdGVkX2F0JyB9KVxyXG4gIGNyZWF0ZWRBdDogRGF0ZTtcclxuXHJcbiAgQFVwZGF0ZURhdGVDb2x1bW4oeyBuYW1lOiAndXBkYXRlZF9hdCcgfSlcclxuICB1cGRhdGVkQXQ6IERhdGU7XHJcblxyXG4gIC8vIFJlbGF0aW9uc2hpcHNcclxuICBAT25lVG9NYW55KCgpID0+IElPQywgaW9jID0+IGlvYy50aHJlYXRGZWVkKVxyXG4gIGlvY3M6IElPQ1tdO1xyXG5cclxuICAvKipcclxuICAgKiBDaGVjayBpZiBmZWVkIGlzIGhlYWx0aHlcclxuICAgKi9cclxuICBnZXQgaXNIZWFsdGh5KCk6IGJvb2xlYW4ge1xyXG4gICAgaWYgKHRoaXMuc3RhdHVzICE9PSAnYWN0aXZlJykgcmV0dXJuIGZhbHNlO1xyXG4gICAgXHJcbiAgICBpZiAoIXRoaXMubGFzdFN5bmMpIHJldHVybiBmYWxzZTtcclxuICAgIFxyXG4gICAgY29uc3QgbWF4QWdlID0gdGhpcy5zeW5jRnJlcXVlbmN5ICogMjsgLy8gQWxsb3cgMnggc3luYyBmcmVxdWVuY3lcclxuICAgIGNvbnN0IGFnZU1pbnV0ZXMgPSAoRGF0ZS5ub3coKSAtIHRoaXMubGFzdFN5bmMuZ2V0VGltZSgpKSAvICgxMDAwICogNjApO1xyXG4gICAgXHJcbiAgICByZXR1cm4gYWdlTWludXRlcyA8PSBtYXhBZ2U7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDaGVjayBpZiBmZWVkIGlzIGR1ZSBmb3Igc3luY1xyXG4gICAqL1xyXG4gIGdldCBpc0R1ZUZvclN5bmMoKTogYm9vbGVhbiB7XHJcbiAgICBpZiAoIXRoaXMuYXV0b1N5bmMgfHwgdGhpcy5zdGF0dXMgIT09ICdhY3RpdmUnKSByZXR1cm4gZmFsc2U7XHJcbiAgICBcclxuICAgIGlmICghdGhpcy5sYXN0U3luYykgcmV0dXJuIHRydWU7XHJcbiAgICBcclxuICAgIGNvbnN0IGFnZU1pbnV0ZXMgPSAoRGF0ZS5ub3coKSAtIHRoaXMubGFzdFN5bmMuZ2V0VGltZSgpKSAvICgxMDAwICogNjApO1xyXG4gICAgcmV0dXJuIGFnZU1pbnV0ZXMgPj0gdGhpcy5zeW5jRnJlcXVlbmN5O1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IHN5bmMgc3VjY2VzcyByYXRlXHJcbiAgICovXHJcbiAgZ2V0IHN5bmNTdWNjZXNzUmF0ZSgpOiBudW1iZXIge1xyXG4gICAgaWYgKCF0aGlzLnN5bmNTdGF0cykgcmV0dXJuIDA7XHJcbiAgICBcclxuICAgIGNvbnN0IHRvdGFsID0gKHRoaXMuc3luY1N0YXRzLnN1Y2Nlc3NmdWxTeW5jcyB8fCAwKSArICh0aGlzLnN5bmNTdGF0cy5mYWlsZWRTeW5jcyB8fCAwKTtcclxuICAgIGlmICh0b3RhbCA9PT0gMCkgcmV0dXJuIDA7XHJcbiAgICBcclxuICAgIHJldHVybiBNYXRoLnJvdW5kKCgodGhpcy5zeW5jU3RhdHMuc3VjY2Vzc2Z1bFN5bmNzIHx8IDApIC8gdG90YWwpICogMTAwKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCBhdmVyYWdlIElPQ3MgcGVyIHN5bmNcclxuICAgKi9cclxuICBnZXQgYXZlcmFnZUlvY3NQZXJTeW5jKCk6IG51bWJlciB7XHJcbiAgICBpZiAoIXRoaXMuc3luY1N0YXRzIHx8ICF0aGlzLnN5bmNTdGF0cy5zdWNjZXNzZnVsU3luY3MpIHJldHVybiAwO1xyXG4gICAgXHJcbiAgICByZXR1cm4gTWF0aC5yb3VuZCh0aGlzLnRvdGFsSW9jcyAvIHRoaXMuc3luY1N0YXRzLnN1Y2Nlc3NmdWxTeW5jcyk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBVcGRhdGUgc3luYyBzdGF0aXN0aWNzXHJcbiAgICovXHJcbiAgdXBkYXRlU3luY1N0YXRzKFxyXG4gICAgc3VjY2VzczogYm9vbGVhbixcclxuICAgIGR1cmF0aW9uOiBudW1iZXIsXHJcbiAgICBuZXdJb2NzOiBudW1iZXIgPSAwLFxyXG4gICAgdXBkYXRlZElvY3M6IG51bWJlciA9IDAsXHJcbiAgICBleHBpcmVkSW9jczogbnVtYmVyID0gMCxcclxuICAgIGVycm9yPzogc3RyaW5nLFxyXG4gICk6IHZvaWQge1xyXG4gICAgaWYgKCF0aGlzLnN5bmNTdGF0cykge1xyXG4gICAgICB0aGlzLnN5bmNTdGF0cyA9IHtcclxuICAgICAgICBzdWNjZXNzZnVsU3luY3M6IDAsXHJcbiAgICAgICAgZmFpbGVkU3luY3M6IDAsXHJcbiAgICAgICAgbmV3SW9jc0xhc3RTeW5jOiAwLFxyXG4gICAgICAgIHVwZGF0ZWRJb2NzTGFzdFN5bmM6IDAsXHJcbiAgICAgICAgZXhwaXJlZElvY3NMYXN0U3luYzogMCxcclxuICAgICAgfTtcclxuICAgIH1cclxuXHJcbiAgICB0aGlzLmxhc3RTeW5jQXR0ZW1wdCA9IG5ldyBEYXRlKCk7XHJcblxyXG4gICAgaWYgKHN1Y2Nlc3MpIHtcclxuICAgICAgdGhpcy5sYXN0U3luYyA9IG5ldyBEYXRlKCk7XHJcbiAgICAgIHRoaXMubGFzdFN5bmNTdGF0dXMgPSAnc3VjY2Vzcyc7XHJcbiAgICAgIHRoaXMubGFzdFN5bmNFcnJvciA9IG51bGw7XHJcbiAgICAgIHRoaXMuc3luY1N0YXRzLnN1Y2Nlc3NmdWxTeW5jcyA9ICh0aGlzLnN5bmNTdGF0cy5zdWNjZXNzZnVsU3luY3MgfHwgMCkgKyAxO1xyXG4gICAgICB0aGlzLnN5bmNTdGF0cy5uZXdJb2NzTGFzdFN5bmMgPSBuZXdJb2NzO1xyXG4gICAgICB0aGlzLnN5bmNTdGF0cy51cGRhdGVkSW9jc0xhc3RTeW5jID0gdXBkYXRlZElvY3M7XHJcbiAgICAgIHRoaXMuc3luY1N0YXRzLmV4cGlyZWRJb2NzTGFzdFN5bmMgPSBleHBpcmVkSW9jcztcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHRoaXMubGFzdFN5bmNTdGF0dXMgPSAnZXJyb3InO1xyXG4gICAgICB0aGlzLmxhc3RTeW5jRXJyb3IgPSBlcnJvciB8fCAnVW5rbm93biBlcnJvcic7XHJcbiAgICAgIHRoaXMuc3luY1N0YXRzLmZhaWxlZFN5bmNzID0gKHRoaXMuc3luY1N0YXRzLmZhaWxlZFN5bmNzIHx8IDApICsgMTtcclxuICAgIH1cclxuXHJcbiAgICB0aGlzLnN5bmNTdGF0cy5sYXN0U3luY0R1cmF0aW9uID0gZHVyYXRpb247XHJcbiAgICBcclxuICAgIC8vIENhbGN1bGF0ZSBhdmVyYWdlIGR1cmF0aW9uXHJcbiAgICBjb25zdCB0b3RhbFN5bmNzID0gKHRoaXMuc3luY1N0YXRzLnN1Y2Nlc3NmdWxTeW5jcyB8fCAwKSArICh0aGlzLnN5bmNTdGF0cy5mYWlsZWRTeW5jcyB8fCAwKTtcclxuICAgIGlmICh0b3RhbFN5bmNzID4gMSkge1xyXG4gICAgICBjb25zdCBjdXJyZW50QXZnID0gdGhpcy5zeW5jU3RhdHMuYXZlcmFnZVN5bmNEdXJhdGlvbiB8fCBkdXJhdGlvbjtcclxuICAgICAgdGhpcy5zeW5jU3RhdHMuYXZlcmFnZVN5bmNEdXJhdGlvbiA9IE1hdGgucm91bmQoXHJcbiAgICAgICAgKGN1cnJlbnRBdmcgKiAodG90YWxTeW5jcyAtIDEpICsgZHVyYXRpb24pIC8gdG90YWxTeW5jc1xyXG4gICAgICApO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgdGhpcy5zeW5jU3RhdHMuYXZlcmFnZVN5bmNEdXJhdGlvbiA9IGR1cmF0aW9uO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogTWFyayBmZWVkIGFzIGhlYWx0aHlcclxuICAgKi9cclxuICBtYXJrQXNIZWFsdGh5KCk6IHZvaWQge1xyXG4gICAgdGhpcy5zdGF0dXMgPSAnYWN0aXZlJztcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIE1hcmsgZmVlZCBhcyB1bmhlYWx0aHlcclxuICAgKi9cclxuICBtYXJrQXNVbmhlYWx0aHkoZXJyb3I/OiBzdHJpbmcpOiB2b2lkIHtcclxuICAgIHRoaXMuc3RhdHVzID0gJ2Vycm9yJztcclxuICAgIHRoaXMubGFzdFN5bmNFcnJvciA9IGVycm9yO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IG5leHQgc3luYyB0aW1lXHJcbiAgICovXHJcbiAgZ2V0IG5leHRTeW5jVGltZSgpOiBEYXRlIHwgbnVsbCB7XHJcbiAgICBpZiAoIXRoaXMuYXV0b1N5bmMgfHwgIXRoaXMubGFzdFN5bmMpIHJldHVybiBudWxsO1xyXG4gICAgXHJcbiAgICBjb25zdCBuZXh0U3luYyA9IG5ldyBEYXRlKHRoaXMubGFzdFN5bmMpO1xyXG4gICAgbmV4dFN5bmMuc2V0TWludXRlcyhuZXh0U3luYy5nZXRNaW51dGVzKCkgKyB0aGlzLnN5bmNGcmVxdWVuY3kpO1xyXG4gICAgXHJcbiAgICByZXR1cm4gbmV4dFN5bmM7XHJcbiAgfVxyXG59XHJcbiJdLCJ2ZXJzaW9uIjozfQ==