b6a3c2864ba5d961c636973f8ddec281
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const user_id_value_object_1 = require("../../value-objects/user-id.value-object");
const tenant_id_value_object_1 = require("../../value-objects/tenant-id.value-object");
const unique_entity_id_value_object_1 = require("../../value-objects/unique-entity-id.value-object");
describe('UserId', () => {
    describe('creation', () => {
        it('should create a valid user ID from UUID string', () => {
            const uuid = '123e4567-e89b-42d3-a456-************';
            const userId = user_id_value_object_1.UserId.fromString(uuid);
            expect(userId.value).toBe(uuid);
            expect(userId.isValid()).toBe(true);
        });
        it('should generate a new user ID', () => {
            const userId = user_id_value_object_1.UserId.generate();
            expect(userId.value).toBeDefined();
            expect(userId.isValid()).toBe(true);
            expect(user_id_value_object_1.UserId.isValid(userId.value)).toBe(true);
        });
        it('should create user ID from UniqueEntityId', () => {
            const uniqueId = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const userId = user_id_value_object_1.UserId.fromUniqueEntityId(uniqueId);
            expect(userId.value).toBe(uniqueId.value);
            expect(userId.uniqueId.equals(uniqueId)).toBe(true);
        });
        it('should create user ID from existing ID', () => {
            const original = user_id_value_object_1.UserId.generate();
            const copy = user_id_value_object_1.UserId.fromId(original);
            expect(copy.value).toBe(original.value);
            expect(copy.equals(original)).toBe(true);
            expect(copy).not.toBe(original); // Different instances
        });
    });
    describe('validation', () => {
        it('should throw error for null value', () => {
            expect(() => new user_id_value_object_1.UserId(null)).toThrow('UserId cannot be null or undefined');
        });
        it('should throw error for undefined value', () => {
            expect(() => new user_id_value_object_1.UserId(undefined)).toThrow('UserId cannot be null or undefined');
        });
        it('should throw error for empty string', () => {
            expect(() => new user_id_value_object_1.UserId('')).toThrow('UserId cannot be empty');
        });
        it('should throw error for whitespace string', () => {
            expect(() => new user_id_value_object_1.UserId('   ')).toThrow('UserId cannot be empty');
        });
        it('should throw error for non-string value', () => {
            expect(() => new user_id_value_object_1.UserId(123)).toThrow('UserId must be a string');
        });
        it('should throw error for invalid UUID format', () => {
            expect(() => new user_id_value_object_1.UserId('invalid-uuid')).toThrow('Invalid UserId format');
        });
        it('should throw error for non-UUID v4 format', () => {
            expect(() => new user_id_value_object_1.UserId('123e4567-e89b-22d3-a456-************')).toThrow('Invalid UserId format');
        });
    });
    describe('static validation methods', () => {
        it('should validate correct UUID strings', () => {
            const validUuid = '123e4567-e89b-42d3-a456-************';
            expect(user_id_value_object_1.UserId.isValid(validUuid)).toBe(true);
        });
        it('should reject invalid UUID strings', () => {
            expect(user_id_value_object_1.UserId.isValid('invalid-uuid')).toBe(false);
            expect(user_id_value_object_1.UserId.isValid('')).toBe(false);
            expect(user_id_value_object_1.UserId.isValid(null)).toBe(false);
            expect(user_id_value_object_1.UserId.isValid(undefined)).toBe(false);
        });
        it('should try parse valid UUID', () => {
            const validUuid = '123e4567-e89b-42d3-a456-************';
            const result = user_id_value_object_1.UserId.tryParse(validUuid);
            expect(result).not.toBeNull();
            expect(result.value).toBe(validUuid);
        });
        it('should return null for invalid UUID in tryParse', () => {
            const result = user_id_value_object_1.UserId.tryParse('invalid-uuid');
            expect(result).toBeNull();
        });
    });
    describe('utility methods', () => {
        let userId;
        beforeEach(() => {
            userId = user_id_value_object_1.UserId.fromString('123e4567-e89b-42d3-a456-************');
        });
        it('should get version', () => {
            expect(userId.getVersion()).toBe(1);
        });
        it('should get variant', () => {
            expect(userId.getVariant()).toBe('RFC4122');
        });
        it('should get short string representation', () => {
            const shortString = userId.toShortString();
            expect(shortString).toBe('123e4567');
            expect(userId.toShortString(4)).toBe('123e');
        });
        it('should get compact string representation', () => {
            const compactString = userId.toCompactString();
            expect(compactString).toBe('123e4567e89b42d3a456************');
        });
        it('should convert to uppercase', () => {
            const upperCase = userId.toUpperCase();
            expect(upperCase).toBe('123e4567-e89b-42d3-a456-************');
        });
        it('should convert to lowercase', () => {
            const lowerCase = userId.toLowerCase();
            expect(lowerCase).toBe('123e4567-e89b-42d3-a456-************');
        });
    });
    describe('equality and comparison', () => {
        it('should be equal to itself', () => {
            const userId = user_id_value_object_1.UserId.generate();
            expect(userId.equals(userId)).toBe(true);
            expect(userId.matches(userId)).toBe(true);
        });
        it('should be equal to user ID with same value', () => {
            const uuid = '123e4567-e89b-42d3-a456-************';
            const userId1 = user_id_value_object_1.UserId.fromString(uuid);
            const userId2 = user_id_value_object_1.UserId.fromString(uuid);
            expect(userId1.equals(userId2)).toBe(true);
            expect(userId1.matches(userId2)).toBe(true);
        });
        it('should not be equal to user ID with different value', () => {
            const userId1 = user_id_value_object_1.UserId.generate();
            const userId2 = user_id_value_object_1.UserId.generate();
            expect(userId1.equals(userId2)).toBe(false);
            expect(userId1.matches(userId2)).toBe(false);
        });
        it('should not be equal to null or undefined', () => {
            const userId = user_id_value_object_1.UserId.generate();
            expect(userId.equals(null)).toBe(false);
            expect(userId.equals(undefined)).toBe(false);
        });
        it('should not be equal to non-UserId object', () => {
            const userId = user_id_value_object_1.UserId.generate();
            expect(userId.equals({})).toBe(false);
        });
    });
    describe('special user IDs', () => {
        it('should create system user ID', () => {
            const systemUser = user_id_value_object_1.UserId.system();
            expect(systemUser.isSystem()).toBe(true);
            expect(systemUser.isAnonymous()).toBe(false);
        });
        it('should create anonymous user ID', () => {
            const anonymousUser = user_id_value_object_1.UserId.anonymous();
            expect(anonymousUser.isAnonymous()).toBe(true);
            expect(anonymousUser.isSystem()).toBe(false);
        });
        it('should create service user ID', () => {
            const serviceUser = user_id_value_object_1.UserId.service('auth-service');
            expect(serviceUser.isService()).toBe(true);
            expect(serviceUser.isSystem()).toBe(false);
            expect(serviceUser.isAnonymous()).toBe(false);
        });
        it('should throw error for empty service name', () => {
            expect(() => user_id_value_object_1.UserId.service('')).toThrow('Service name cannot be empty');
            expect(() => user_id_value_object_1.UserId.service('   ')).toThrow('Service name cannot be empty');
        });
        it('should create deterministic user ID from seed', () => {
            const user1 = user_id_value_object_1.UserId.fromSeed('test');
            const user2 = user_id_value_object_1.UserId.fromSeed('test');
            expect(user1.equals(user2)).toBe(true);
        });
        it('should create different user IDs from different seeds', () => {
            const user1 = user_id_value_object_1.UserId.fromSeed('test1');
            const user2 = user_id_value_object_1.UserId.fromSeed('test2');
            expect(user1.equals(user2)).toBe(false);
        });
    });
    describe('tenant scoping', () => {
        it('should scope user ID to tenant', () => {
            const userId = user_id_value_object_1.UserId.generate();
            const tenantId = tenant_id_value_object_1.TenantId.generate();
            const scopedId = userId.scopeToTenant(tenantId);
            expect(scopedId).toBe(`${tenantId.value}:${userId.value}`);
        });
        it('should parse scoped user ID', () => {
            const userId = user_id_value_object_1.UserId.generate();
            const tenantId = tenant_id_value_object_1.TenantId.generate();
            const scopedId = `${tenantId.value}:${userId.value}`;
            const parsed = user_id_value_object_1.UserId.parseScopedId(scopedId);
            expect(parsed).not.toBeNull();
            expect(parsed.userId.equals(userId)).toBe(true);
            expect(parsed.tenantId.equals(tenantId)).toBe(true);
        });
        it('should return null for invalid scoped ID format', () => {
            expect(user_id_value_object_1.UserId.parseScopedId('invalid')).toBeNull();
            expect(user_id_value_object_1.UserId.parseScopedId('')).toBeNull();
            expect(user_id_value_object_1.UserId.parseScopedId(null)).toBeNull();
            expect(user_id_value_object_1.UserId.parseScopedId('too:many:colons')).toBeNull();
        });
        it('should return null for invalid UUID parts in scoped ID', () => {
            const result = user_id_value_object_1.UserId.parseScopedId('invalid-tenant:invalid-user');
            expect(result).toBeNull();
        });
    });
    describe('bulk operations', () => {
        it('should generate multiple user IDs', () => {
            const userIds = user_id_value_object_1.UserId.generateMany(5);
            expect(userIds).toHaveLength(5);
            expect(userIds.every(id => id.isValid())).toBe(true);
            // All should be unique
            const uniqueValues = new Set(userIds.map(id => id.value));
            expect(uniqueValues.size).toBe(5);
        });
        it('should handle zero count for generateMany', () => {
            const userIds = user_id_value_object_1.UserId.generateMany(0);
            expect(userIds).toHaveLength(0);
        });
        it('should throw error for negative count', () => {
            expect(() => user_id_value_object_1.UserId.generateMany(-1)).toThrow('Count must be non-negative');
        });
    });
    describe('serialization', () => {
        it('should convert to string', () => {
            const uuid = '123e4567-e89b-42d3-a456-************';
            const userId = user_id_value_object_1.UserId.fromString(uuid);
            expect(userId.toString()).toBe(uuid);
        });
        it('should convert to JSON', () => {
            const uuid = '123e4567-e89b-42d3-a456-************';
            const userId = user_id_value_object_1.UserId.fromString(uuid);
            const json = userId.toJSON();
            expect(json.value).toBe(uuid);
            expect(json.type).toBe('UserId');
            expect(json.version).toBe(1);
            expect(json.variant).toBe('RFC4122');
            expect(json.shortString).toBe('123e4567');
            expect(json.compactString).toBe('123e4567e89b42d3a456************');
        });
        it('should create from JSON', () => {
            const uuid = '123e4567-e89b-42d3-a456-************';
            const json = { value: uuid };
            const userId = user_id_value_object_1.UserId.fromJSON(json);
            expect(userId.value).toBe(uuid);
        });
    });
    describe('immutability', () => {
        it('should be immutable after creation', () => {
            const userId = user_id_value_object_1.UserId.generate();
            const originalValue = userId.value;
            // Attempt to modify (should not work due to readonly)
            expect(() => {
                userId._value = 'modified';
            }).not.toThrow(); // TypeScript prevents this, but runtime doesn't throw
            // Value should remain unchanged
            expect(userId.value).toBe(originalValue);
        });
        it('should be frozen', () => {
            const userId = user_id_value_object_1.UserId.generate();
            expect(Object.isFrozen(userId)).toBe(true);
        });
    });
    describe('edge cases', () => {
        it('should handle case-insensitive UUID comparison', () => {
            const uuid1 = '123e4567-e89b-42d3-a456-************';
            const uuid2 = '123e4567-e89b-42d3-a456-************';
            const userId1 = user_id_value_object_1.UserId.fromString(uuid1);
            const userId2 = user_id_value_object_1.UserId.fromString(uuid2);
            expect(userId1.equals(userId2)).toBe(true);
        });
        it('should maintain original case in value', () => {
            const uuid = '123e4567-e89b-42d3-a456-************';
            const userId = user_id_value_object_1.UserId.fromString(uuid);
            expect(userId.value).toBe(uuid);
        });
        it('should create consistent service user IDs', () => {
            const service1 = user_id_value_object_1.UserId.service('auth-service');
            const service2 = user_id_value_object_1.UserId.service('auth-service');
            expect(service1.equals(service2)).toBe(true);
        });
        it('should create different service user IDs for different services', () => {
            const authService = user_id_value_object_1.UserId.service('auth-service');
            const dataService = user_id_value_object_1.UserId.service('data-service');
            expect(authService.equals(dataService)).toBe(false);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************