{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\headers\\__tests__\\csp.config.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,2CAA+C;AAC/C,8CAA0C;AAE1C,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;IACzB,IAAI,SAAoB,CAAC;IACzB,IAAI,aAAyC,CAAC;IAE9C,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,iBAAiB,GAAG;YACxB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;SACf,CAAC;QAEF,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,sBAAS;gBACT;oBACE,OAAO,EAAE,sBAAa;oBACtB,QAAQ,EAAE,iBAAiB;iBAC5B;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,SAAS,GAAG,MAAM,CAAC,GAAG,CAAY,sBAAS,CAAC,CAAC;QAC7C,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,sBAAa,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;YAEhD,MAAM,GAAG,GAAG,SAAS,CAAC,iBAAiB,EAAE,CAAC;YAE1C,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;YAC5C,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;YAC3C,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;YAChD,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;YACnD,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YAE7C,MAAM,GAAG,GAAG,SAAS,CAAC,iBAAiB,EAAE,CAAC;YAE1C,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;YAC5C,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,iDAAiD,CAAC,CAAC;YACzE,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,wCAAwC,CAAC,CAAC;YAChE,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;YAEjD,MAAM,GAAG,GAAG,SAAS,CAAC,iBAAiB,EAAE,CAAC;YAE1C,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,sFAAsF,CAAC,CAAC;YAC9G,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YACrC,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YACrC,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,wCAAwC,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAE1C,MAAM,GAAG,GAAG,SAAS,CAAC,iBAAiB,EAAE,CAAC;YAE1C,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,kDAAkD,CAAC,CAAC;YAC1E,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,iDAAiD,CAAC,CAAC;YACzE,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YAE7C,MAAM,GAAG,GAAG,SAAS,CAAC,iBAAiB,EAAE,CAAC;YAE1C,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YACrC,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,UAAU,CAAC,GAAG,EAAE;YACd,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,GAAG,GAAG,SAAS,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;YAEpD,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;YAC5C,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;YAC5C,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;YAC3C,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;YACxC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,GAAG,GAAG,SAAS,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;YAExD,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;YAC5C,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;YAC5C,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,GAAG,GAAG,SAAS,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;YAEvD,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;YAC5C,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YACzC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,UAAU,GAAG,SAAS,CAAC,iBAAiB,EAAE,CAAC;YACjD,MAAM,WAAW,GAAG,SAAS,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;YAEhE,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;YAEjD,MAAM,OAAO,GAAG,SAAS,CAAC,iBAAiB,EAAE,CAAC;YAE9C,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;YAChD,IAAI,CAAC,KAAK,CAAC,SAAgB,EAAE,kBAAkB,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAErE,MAAM,OAAO,GAAG,SAAS,CAAC,iBAAiB,EAAE,CAAC;YAE9C,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;YAChD,IAAI,CAAC,KAAK,CAAC,SAAgB,EAAE,kBAAkB,CAAC,CAAC,eAAe,CAAC,8CAA8C,CAAC,CAAC;YACjH,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,kBAAkB,EAAE,CAAC;YAEpE,MAAM,OAAO,GAAG,SAAS,CAAC,iBAAiB,EAAE,CAAC;YAE9C,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3B,MAAM,CAAC,UAAU,CAAC,CAAC,oBAAoB,CAAC,6CAA6C,CAAC,CAAC;YAEvF,UAAU,CAAC,WAAW,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;YACxE,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;YAChD,IAAI,CAAC,KAAK,CAAC,SAAgB,EAAE,kBAAkB,CAAC,CAAC,eAAe,CAAC,kCAAkC,CAAC,CAAC;YACrG,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,kBAAkB,EAAE,CAAC;YAEpE,MAAM,OAAO,GAAG,SAAS,CAAC,iBAAiB,EAAE,CAAC;YAE9C,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3B,MAAM,CAAC,UAAU,CAAC,CAAC,oBAAoB,CAAC,0DAA0D,CAAC,CAAC;YAEpG,UAAU,CAAC,WAAW,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,GAAG,EAAE;gBACxC,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,kBAAkB,EAAE,CAAC;YAErE,MAAM,OAAO,GAAG,SAAS,CAAC,iBAAiB,EAAE,CAAC;YAE9C,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5B,MAAM,CAAC,UAAU,CAAC,CAAC,gBAAgB,EAAE,CAAC;YAEtC,UAAU,CAAC,WAAW,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;YAEhD,MAAM,OAAO,GAAG,SAAS,CAAC,mBAAmB,EAAE,CAAC;YAEhD,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,OAAO,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACpD,MAAM,CAAC,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;YAEjD,MAAM,OAAO,GAAG,SAAS,CAAC,mBAAmB,EAAE,CAAC;YAEhD,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;YAEjD,MAAM,OAAO,GAAG,SAAS,CAAC,mBAAmB,EAAE,CAAC;YAEhD,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YAE7C,MAAM,OAAO,GAAG,SAAS,CAAC,mBAAmB,EAAE,CAAC;YAEhD,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,UAAU,GAAG;gBACjB,aAAa,EAAE,CAAC,QAAQ,CAAC;gBACzB,YAAY,EAAE,CAAC,QAAQ,EAAE,iBAAiB,CAAC;gBAC3C,2BAA2B,EAAE,EAAE;aAChC,CAAC;YAEF,MAAM,SAAS,GAAI,SAAiB,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAEhE,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,kFAAkF,CAAC,CAAC;QAC7G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,UAAU,GAAG;gBACjB,yBAAyB,EAAE,EAAE;gBAC7B,2BAA2B,EAAE,EAAE;aAChC,CAAC;YAEF,MAAM,SAAS,GAAI,SAAiB,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAEhE,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,UAAU,GAAG;gBACjB,aAAa,EAAE,CAAC,QAAQ,CAAC;aAC1B,CAAC;YAEF,MAAM,SAAS,GAAI,SAAiB,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAEhE,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qCAAqC,EAAE,GAAG,EAAE;QACnD,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;YAEhD,MAAM,GAAG,GAAG,SAAS,CAAC,iBAAiB,EAAE,CAAC;YAE1C,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAC3C,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;YACnD,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;YACjD,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YAE7C,MAAM,GAAG,GAAG,SAAS,CAAC,iBAAiB,EAAE,CAAC;YAE1C,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YACvC,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;YAClD,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;YAEjD,MAAM,GAAG,GAAG,SAAS,CAAC,iBAAiB,EAAE,CAAC;YAE1C,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YACrC,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YACrC,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YACtC,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAE1C,MAAM,GAAG,GAAG,SAAS,CAAC,iBAAiB,EAAE,CAAC;YAE1C,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;YACzC,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YACvC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YACzC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\security\\headers\\__tests__\\csp.config.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { CspConfig } from '../csp.config';\r\n\r\ndescribe('CspConfig', () => {\r\n  let cspConfig: CspConfig;\r\n  let configService: jest.Mocked<ConfigService>;\r\n\r\n  beforeEach(async () => {\r\n    const mockConfigService = {\r\n      get: jest.fn(),\r\n    };\r\n\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        CspConfig,\r\n        {\r\n          provide: ConfigService,\r\n          useValue: mockConfigService,\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    cspConfig = module.get<CspConfig>(CspConfig);\r\n    configService = module.get(ConfigService);\r\n  });\r\n\r\n  describe('generateCSPHeader', () => {\r\n    it('should return production CSP for production environment', () => {\r\n      configService.get.mockReturnValue('production');\r\n\r\n      const csp = cspConfig.generateCSPHeader();\r\n\r\n      expect(csp).toContain(\"default-src 'self'\");\r\n      expect(csp).toContain(\"object-src 'none'\");\r\n      expect(csp).toContain(\"frame-ancestors 'none'\");\r\n      expect(csp).toContain('upgrade-insecure-requests');\r\n      expect(csp).toContain('block-all-mixed-content');\r\n    });\r\n\r\n    it('should return staging CSP for staging environment', () => {\r\n      configService.get.mockReturnValue('staging');\r\n\r\n      const csp = cspConfig.generateCSPHeader();\r\n\r\n      expect(csp).toContain(\"default-src 'self'\");\r\n      expect(csp).toContain(\"script-src 'self' 'unsafe-inline' 'unsafe-eval'\");\r\n      expect(csp).toContain('report-uri /api/v1/security/csp-report');\r\n      expect(csp).toContain('https://staging-api.sentinel.com');\r\n    });\r\n\r\n    it('should return development CSP for development environment', () => {\r\n      configService.get.mockReturnValue('development');\r\n\r\n      const csp = cspConfig.generateCSPHeader();\r\n\r\n      expect(csp).toContain(\"script-src 'self' 'unsafe-inline' 'unsafe-eval' https: http: localhost:* 127.0.0.1:*\");\r\n      expect(csp).toContain('localhost:*');\r\n      expect(csp).toContain('127.0.0.1:*');\r\n      expect(csp).toContain('report-uri /api/v1/security/csp-report');\r\n    });\r\n\r\n    it('should return test CSP for test environment', () => {\r\n      configService.get.mockReturnValue('test');\r\n\r\n      const csp = cspConfig.generateCSPHeader();\r\n\r\n      expect(csp).toContain(\"default-src 'self' 'unsafe-inline' 'unsafe-eval'\");\r\n      expect(csp).toContain(\"script-src 'self' 'unsafe-inline' 'unsafe-eval'\");\r\n      expect(csp).not.toContain('report-uri');\r\n    });\r\n\r\n    it('should default to development CSP for unknown environment', () => {\r\n      configService.get.mockReturnValue('unknown');\r\n\r\n      const csp = cspConfig.generateCSPHeader();\r\n\r\n      expect(csp).toContain('localhost:*');\r\n      expect(csp).toContain(\"'unsafe-eval'\");\r\n    });\r\n  });\r\n\r\n  describe('getEndpointSpecificCSP', () => {\r\n    beforeEach(() => {\r\n      configService.get.mockReturnValue('production');\r\n    });\r\n\r\n    it('should return API-only CSP for API endpoints', () => {\r\n      const csp = cspConfig.getEndpointSpecificCSP('api');\r\n\r\n      expect(csp).toContain(\"default-src 'none'\");\r\n      expect(csp).toContain(\"connect-src 'self'\");\r\n      expect(csp).toContain(\"object-src 'none'\");\r\n      expect(csp).not.toContain('script-src');\r\n      expect(csp).not.toContain('style-src');\r\n    });\r\n\r\n    it('should return webhook CSP for webhook endpoints', () => {\r\n      const csp = cspConfig.getEndpointSpecificCSP('webhook');\r\n\r\n      expect(csp).toContain(\"default-src 'none'\");\r\n      expect(csp).toContain(\"connect-src 'self'\");\r\n      expect(csp).toContain(\"form-action 'self'\");\r\n    });\r\n\r\n    it('should return health check CSP for health endpoints', () => {\r\n      const csp = cspConfig.getEndpointSpecificCSP('health');\r\n\r\n      expect(csp).toContain(\"default-src 'none'\");\r\n      expect(csp).not.toContain('connect-src');\r\n      expect(csp).not.toContain('script-src');\r\n    });\r\n\r\n    it('should return default CSP for unknown endpoint types', () => {\r\n      const defaultCsp = cspConfig.generateCSPHeader();\r\n      const endpointCsp = cspConfig.getEndpointSpecificCSP('unknown');\r\n\r\n      expect(endpointCsp).toBe(defaultCsp);\r\n    });\r\n  });\r\n\r\n  describe('validateCSPConfig', () => {\r\n    it('should validate valid CSP configuration', () => {\r\n      configService.get.mockReturnValue('development');\r\n\r\n      const isValid = cspConfig.validateCSPConfig();\r\n\r\n      expect(isValid).toBe(true);\r\n    });\r\n\r\n    it('should fail validation for empty CSP', () => {\r\n      configService.get.mockReturnValue('production');\r\n      jest.spyOn(cspConfig as any, 'getProductionCSP').mockReturnValue('');\r\n\r\n      const isValid = cspConfig.validateCSPConfig();\r\n\r\n      expect(isValid).toBe(false);\r\n    });\r\n\r\n    it('should warn about unsafe-eval in production', () => {\r\n      configService.get.mockReturnValue('production');\r\n      jest.spyOn(cspConfig as any, 'getProductionCSP').mockReturnValue(\"default-src 'self'; script-src 'unsafe-eval'\");\r\n      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();\r\n\r\n      const isValid = cspConfig.validateCSPConfig();\r\n\r\n      expect(isValid).toBe(true);\r\n      expect(consoleSpy).toHaveBeenCalledWith('CSP: unsafe-eval detected in production CSP');\r\n      \r\n      consoleSpy.mockRestore();\r\n    });\r\n\r\n    it('should warn about wildcard sources without HTTPS in production', () => {\r\n      configService.get.mockReturnValue('production');\r\n      jest.spyOn(cspConfig as any, 'getProductionCSP').mockReturnValue(\"default-src 'self'; script-src *\");\r\n      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();\r\n\r\n      const isValid = cspConfig.validateCSPConfig();\r\n\r\n      expect(isValid).toBe(true);\r\n      expect(consoleSpy).toHaveBeenCalledWith('CSP: Wildcard sources detected without HTTPS restriction');\r\n      \r\n      consoleSpy.mockRestore();\r\n    });\r\n\r\n    it('should handle validation errors gracefully', () => {\r\n      configService.get.mockImplementation(() => {\r\n        throw new Error('Configuration error');\r\n      });\r\n      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();\r\n\r\n      const isValid = cspConfig.validateCSPConfig();\r\n\r\n      expect(isValid).toBe(false);\r\n      expect(consoleSpy).toHaveBeenCalled();\r\n      \r\n      consoleSpy.mockRestore();\r\n    });\r\n  });\r\n\r\n  describe('getCSPConfigSummary', () => {\r\n    it('should return configuration summary', () => {\r\n      configService.get.mockReturnValue('production');\r\n\r\n      const summary = cspConfig.getCSPConfigSummary();\r\n\r\n      expect(summary.environment).toBe('production');\r\n      expect(typeof summary.directiveCount).toBe('number');\r\n      expect(typeof summary.hasUnsafeInline).toBe('boolean');\r\n      expect(typeof summary.hasUnsafeEval).toBe('boolean');\r\n      expect(typeof summary.hasReportUri).toBe('boolean');\r\n      expect(typeof summary.length).toBe('number');\r\n    });\r\n\r\n    it('should detect unsafe-inline in CSP', () => {\r\n      configService.get.mockReturnValue('development');\r\n\r\n      const summary = cspConfig.getCSPConfigSummary();\r\n\r\n      expect(summary.hasUnsafeInline).toBe(true);\r\n    });\r\n\r\n    it('should detect unsafe-eval in CSP', () => {\r\n      configService.get.mockReturnValue('development');\r\n\r\n      const summary = cspConfig.getCSPConfigSummary();\r\n\r\n      expect(summary.hasUnsafeEval).toBe(true);\r\n    });\r\n\r\n    it('should detect report-uri in CSP', () => {\r\n      configService.get.mockReturnValue('staging');\r\n\r\n      const summary = cspConfig.getCSPConfigSummary();\r\n\r\n      expect(summary.hasReportUri).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('buildCSPString', () => {\r\n    it('should build CSP string from directives object', () => {\r\n      const directives = {\r\n        'default-src': [\"'self'\"],\r\n        'script-src': [\"'self'\", \"'unsafe-inline'\"],\r\n        'upgrade-insecure-requests': [],\r\n      };\r\n\r\n      const cspString = (cspConfig as any).buildCSPString(directives);\r\n\r\n      expect(cspString).toBe(\"default-src 'self'; script-src 'self' 'unsafe-inline'; upgrade-insecure-requests\");\r\n    });\r\n\r\n    it('should handle empty sources array', () => {\r\n      const directives = {\r\n        'block-all-mixed-content': [],\r\n        'upgrade-insecure-requests': [],\r\n      };\r\n\r\n      const cspString = (cspConfig as any).buildCSPString(directives);\r\n\r\n      expect(cspString).toBe('block-all-mixed-content; upgrade-insecure-requests');\r\n    });\r\n\r\n    it('should handle single source', () => {\r\n      const directives = {\r\n        'default-src': [\"'none'\"],\r\n      };\r\n\r\n      const cspString = (cspConfig as any).buildCSPString(directives);\r\n\r\n      expect(cspString).toBe(\"default-src 'none'\");\r\n    });\r\n  });\r\n\r\n  describe('environment-specific configurations', () => {\r\n    it('should have strict production configuration', () => {\r\n      configService.get.mockReturnValue('production');\r\n\r\n      const csp = cspConfig.generateCSPHeader();\r\n\r\n      expect(csp).not.toContain(\"'unsafe-eval'\");\r\n      expect(csp).toContain('upgrade-insecure-requests');\r\n      expect(csp).toContain('block-all-mixed-content');\r\n      expect(csp).toContain(\"frame-ancestors 'none'\");\r\n    });\r\n\r\n    it('should allow debugging in staging', () => {\r\n      configService.get.mockReturnValue('staging');\r\n\r\n      const csp = cspConfig.generateCSPHeader();\r\n\r\n      expect(csp).toContain(\"'unsafe-eval'\");\r\n      expect(csp).toContain('staging-api.sentinel.com');\r\n      expect(csp).toContain('report-uri');\r\n    });\r\n\r\n    it('should be permissive in development', () => {\r\n      configService.get.mockReturnValue('development');\r\n\r\n      const csp = cspConfig.generateCSPHeader();\r\n\r\n      expect(csp).toContain('localhost:*');\r\n      expect(csp).toContain('127.0.0.1:*');\r\n      expect(csp).toContain('https: http:');\r\n      expect(csp).toContain(\"'unsafe-eval'\");\r\n    });\r\n\r\n    it('should be minimal in test environment', () => {\r\n      configService.get.mockReturnValue('test');\r\n\r\n      const csp = cspConfig.generateCSPHeader();\r\n\r\n      expect(csp).toContain(\"'unsafe-inline'\");\r\n      expect(csp).toContain(\"'unsafe-eval'\");\r\n      expect(csp).not.toContain('localhost:*');\r\n      expect(csp).not.toContain('report-uri');\r\n    });\r\n  });\r\n});"], "version": 3}