16f24173b9244da53fc71459c79c321c
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const event_factory_1 = require("../event.factory");
const event_entity_1 = require("../../entities/event.entity");
const event_type_enum_1 = require("../../enums/event-type.enum");
const event_severity_enum_1 = require("../../enums/event-severity.enum");
const event_status_enum_1 = require("../../enums/event-status.enum");
const event_processing_status_enum_1 = require("../../enums/event-processing-status.enum");
const event_source_type_enum_1 = require("../../enums/event-source-type.enum");
const shared_kernel_1 = require("../../../../shared-kernel");
describe('EventFactory', () => {
    describe('create', () => {
        it('should create event with required options', () => {
            const options = {
                type: event_type_enum_1.EventType.THREAT_DETECTED,
                severity: event_severity_enum_1.EventSeverity.HIGH,
                title: 'Test Threat',
                rawData: { threat: 'malicious-ip' },
                sourceType: event_source_type_enum_1.EventSourceType.FIREWALL,
                sourceIdentifier: 'fw-01',
            };
            const event = event_factory_1.EventFactory.create(options);
            expect(event).toBeInstanceOf(event_entity_1.Event);
            expect(event.type).toBe(event_type_enum_1.EventType.THREAT_DETECTED);
            expect(event.severity).toBe(event_severity_enum_1.EventSeverity.HIGH);
            expect(event.title).toBe('Test Threat');
            expect(event.rawData).toEqual({ threat: 'malicious-ip' });
            expect(event.metadata.source.type).toBe(event_source_type_enum_1.EventSourceType.FIREWALL);
            expect(event.metadata.source.identifier).toBe('fw-01');
            expect(event.status).toBe(event_status_enum_1.EventStatus.ACTIVE); // Default
            expect(event.processingStatus).toBe(event_processing_status_enum_1.EventProcessingStatus.RAW); // Default
        });
        it('should create event with custom ID', () => {
            const customId = shared_kernel_1.UniqueEntityId.generate();
            const options = {
                id: customId,
                type: event_type_enum_1.EventType.MALWARE_DETECTED,
                severity: event_severity_enum_1.EventSeverity.CRITICAL,
                title: 'Malware Alert',
                rawData: { file: 'malware.exe' },
                sourceType: event_source_type_enum_1.EventSourceType.ANTIVIRUS,
                sourceIdentifier: 'av-01',
            };
            const event = event_factory_1.EventFactory.create(options);
            expect(event.id.equals(customId)).toBe(true);
        });
        it('should create event with all optional properties', () => {
            const parentId = shared_kernel_1.UniqueEntityId.generate();
            const timestamp = new Date('2023-01-01T10:00:00Z');
            const options = {
                type: event_type_enum_1.EventType.VULNERABILITY_DETECTED,
                severity: event_severity_enum_1.EventSeverity.MEDIUM,
                title: 'Vulnerability Found',
                description: 'Critical vulnerability detected',
                rawData: { cve: 'CVE-2023-1234' },
                sourceType: event_source_type_enum_1.EventSourceType.VULNERABILITY_SCANNER,
                sourceIdentifier: 'vuln-scanner-01',
                timestamp,
                tags: ['vulnerability', 'critical'],
                riskScore: 80,
                confidenceLevel: 90,
                attributes: { cve: 'CVE-2023-1234' },
                correlationId: 'corr-123',
                parentEventId: parentId,
                status: event_status_enum_1.EventStatus.INVESTIGATING,
                processingStatus: event_processing_status_enum_1.EventProcessingStatus.ANALYZED,
                sourceMetadata: {
                    name: 'Vulnerability Scanner',
                    version: '2.1.0',
                    vendor: 'Security Corp',
                    location: 'datacenter-1',
                },
                processingHints: {
                    priority: 'high',
                    skipNormalization: false,
                    retentionDays: 90,
                },
            };
            const event = event_factory_1.EventFactory.create(options);
            expect(event.type).toBe(event_type_enum_1.EventType.VULNERABILITY_DETECTED);
            expect(event.severity).toBe(event_severity_enum_1.EventSeverity.MEDIUM);
            expect(event.title).toBe('Vulnerability Found');
            expect(event.description).toBe('Critical vulnerability detected');
            expect(event.tags).toEqual(['vulnerability', 'critical']);
            expect(event.riskScore).toBe(80);
            expect(event.confidenceLevel).toBe(90);
            expect(event.attributes).toEqual({ cve: 'CVE-2023-1234' });
            expect(event.correlationId).toBe('corr-123');
            expect(event.parentEventId?.equals(parentId)).toBe(true);
            expect(event.status).toBe(event_status_enum_1.EventStatus.INVESTIGATING);
            expect(event.processingStatus).toBe(event_processing_status_enum_1.EventProcessingStatus.ANALYZED);
            expect(event.metadata.timestamp.occurredAt).toEqual(timestamp);
            expect(event.metadata.source.name).toBe('Vulnerability Scanner');
            expect(event.metadata.source.version).toBe('2.1.0');
            expect(event.metadata.source.vendor).toBe('Security Corp');
            expect(event.metadata.source.location).toBe('datacenter-1');
            expect(event.metadata.processingHints?.priority).toBe('high');
            expect(event.metadata.processingHints?.retentionDays).toBe(90);
        });
    });
    describe('fromRawLog', () => {
        it('should create event from raw log data', () => {
            const logData = {
                message: 'Suspicious login attempt',
                severity: 'high',
                timestamp: '2023-01-01T10:00:00Z',
                user: 'admin',
                source_ip: '*************',
                tags: ['authentication', 'suspicious'],
            };
            const event = event_factory_1.EventFactory.fromRawLog(logData, event_source_type_enum_1.EventSourceType.DIRECTORY_SERVICE, 'ad-server-01');
            expect(event).toBeInstanceOf(event_entity_1.Event);
            expect(event.title).toBe('Suspicious login attempt');
            expect(event.severity).toBe(event_severity_enum_1.EventSeverity.HIGH);
            expect(event.rawData).toEqual(logData);
            expect(event.metadata.source.type).toBe(event_source_type_enum_1.EventSourceType.DIRECTORY_SERVICE);
            expect(event.metadata.source.identifier).toBe('ad-server-01');
            expect(event.tags).toEqual(['authentication', 'suspicious']);
        });
        it('should infer event type from log content', () => {
            const loginFailureLog = {
                message: 'Login failed for user admin',
                level: 'error',
            };
            const event = event_factory_1.EventFactory.fromRawLog(loginFailureLog, event_source_type_enum_1.EventSourceType.DIRECTORY_SERVICE, 'ad-01');
            expect(event.type).toBe(event_type_enum_1.EventType.LOGIN_FAILURE);
        });
        it('should handle missing fields gracefully', () => {
            const minimalLog = {
                data: 'some event data',
            };
            const event = event_factory_1.EventFactory.fromRawLog(minimalLog, event_source_type_enum_1.EventSourceType.CUSTOM_APPLICATION, 'app-01');
            expect(event.title).toBe('Security Event'); // Default title
            expect(event.severity).toBe(event_severity_enum_1.EventSeverity.MEDIUM); // Default severity
            expect(event.type).toBe(event_type_enum_1.EventType.CUSTOM); // Default type
        });
    });
    describe('createSecurityAlert', () => {
        it('should create high-severity security alert', () => {
            const rawData = { alert: 'Intrusion detected' };
            const event = event_factory_1.EventFactory.createSecurityAlert(event_type_enum_1.EventType.THREAT_DETECTED, 'Intrusion Alert', rawData, event_source_type_enum_1.EventSourceType.IDS_IPS, 'ids-01');
            expect(event.type).toBe(event_type_enum_1.EventType.THREAT_DETECTED);
            expect(event.severity).toBe(event_severity_enum_1.EventSeverity.HIGH);
            expect(event.title).toBe('Intrusion Alert');
            expect(event.riskScore).toBe(80);
            expect(event.metadata.processingHints?.priority).toBe('high');
        });
        it('should allow overriding default properties', () => {
            const event = event_factory_1.EventFactory.createSecurityAlert(event_type_enum_1.EventType.MALWARE_DETECTED, 'Malware Alert', { file: 'virus.exe' }, event_source_type_enum_1.EventSourceType.ANTIVIRUS, 'av-01', {
                severity: event_severity_enum_1.EventSeverity.CRITICAL,
                riskScore: 95,
            });
            expect(event.severity).toBe(event_severity_enum_1.EventSeverity.CRITICAL);
            expect(event.riskScore).toBe(95);
        });
    });
    describe('createCriticalAlert', () => {
        it('should create critical security alert', () => {
            const event = event_factory_1.EventFactory.createCriticalAlert(event_type_enum_1.EventType.DATA_BREACH, 'Data Breach Detected', { records: 10000 }, event_source_type_enum_1.EventSourceType.DLP, 'dlp-01');
            expect(event.severity).toBe(event_severity_enum_1.EventSeverity.CRITICAL);
            expect(event.riskScore).toBe(95);
            expect(event.metadata.processingHints?.priority).toBe('critical');
            expect(event.tags).toContain('critical');
            expect(event.tags).toContain('security-alert');
        });
    });
    describe('createCorrelatedEvent', () => {
        it('should create correlated event from parent', () => {
            const parentEvent = event_factory_1.EventFactory.create({
                type: event_type_enum_1.EventType.THREAT_DETECTED,
                severity: event_severity_enum_1.EventSeverity.HIGH,
                title: 'Parent Threat',
                rawData: { ip: '*************' },
                sourceType: event_source_type_enum_1.EventSourceType.FIREWALL,
                sourceIdentifier: 'fw-01',
                tags: ['network', 'threat'],
            });
            const correlatedEvent = event_factory_1.EventFactory.createCorrelatedEvent(parentEvent, event_type_enum_1.EventType.CONNECTION_ESTABLISHED, 'Related Connection', { connection: 'tcp:443' });
            expect(correlatedEvent.severity).toBe(event_severity_enum_1.EventSeverity.HIGH); // Inherited
            expect(correlatedEvent.metadata.source.type).toBe(event_source_type_enum_1.EventSourceType.FIREWALL); // Inherited
            expect(correlatedEvent.metadata.source.identifier).toBe('fw-01'); // Inherited
            expect(correlatedEvent.correlationId).toBe(parentEvent.id.toString());
            expect(correlatedEvent.parentEventId?.equals(parentEvent.id)).toBe(true);
            expect(correlatedEvent.tags).toContain('network'); // Inherited
            expect(correlatedEvent.tags).toContain('threat'); // Inherited
            expect(correlatedEvent.tags).toContain('correlated'); // Added
        });
        it('should use existing correlation ID from parent', () => {
            const parentEvent = event_factory_1.EventFactory.create({
                type: event_type_enum_1.EventType.THREAT_DETECTED,
                severity: event_severity_enum_1.EventSeverity.HIGH,
                title: 'Parent Threat',
                rawData: { ip: '*************' },
                sourceType: event_source_type_enum_1.EventSourceType.FIREWALL,
                sourceIdentifier: 'fw-01',
                correlationId: 'existing-correlation-123',
            });
            const correlatedEvent = event_factory_1.EventFactory.createCorrelatedEvent(parentEvent, event_type_enum_1.EventType.CONNECTION_ESTABLISHED, 'Related Connection', { connection: 'tcp:443' });
            expect(correlatedEvent.correlationId).toBe('existing-correlation-123');
        });
    });
    describe('fromThreatIntelligence', () => {
        it('should create event from threat intelligence data', () => {
            const threatData = {
                indicator: '*************',
                indicatorType: 'ip',
                severity: 'high',
                confidence: 85,
                description: 'Known malicious IP',
                source: 'ThreatFeed-1',
            };
            const event = event_factory_1.EventFactory.fromThreatIntelligence(threatData, 'threat-intel-feed-01');
            expect(event.type).toBe(event_type_enum_1.EventType.THREAT_DETECTED);
            expect(event.title).toBe('Threat Intelligence: *************');
            expect(event.description).toBe('Known malicious IP');
            expect(event.metadata.source.type).toBe(event_source_type_enum_1.EventSourceType.THREAT_INTELLIGENCE);
            expect(event.tags).toContain('threat-intelligence');
            expect(event.tags).toContain('external');
            expect(event.attributes.indicator).toBe('*************');
            expect(event.attributes.indicatorType).toBe('ip');
            expect(event.attributes.confidence).toBe(85);
            expect(event.attributes.source).toBe('ThreatFeed-1');
        });
        it('should calculate risk score from threat data', () => {
            const highConfidenceThreat = {
                indicator: 'malware.exe',
                severity: 9,
                confidence: 95,
                indicatorType: 'file',
            };
            const event = event_factory_1.EventFactory.fromThreatIntelligence(highConfidenceThreat, 'threat-feed-01');
            expect(event.riskScore).toBeGreaterThan(80); // Should be high due to high confidence and severity
        });
    });
    describe('fromVulnerabilityScan', () => {
        it('should create event from vulnerability scan data', () => {
            const vulnData = {
                cve: 'CVE-2023-1234',
                title: 'Remote Code Execution',
                severity: 'critical',
                cvssScore: 9.8,
                description: 'Critical RCE vulnerability',
                affectedAsset: 'web-server-01',
                solution: 'Apply security patch',
            };
            const event = event_factory_1.EventFactory.fromVulnerabilityScan(vulnData, 'vuln-scanner-01');
            expect(event.type).toBe(event_type_enum_1.EventType.VULNERABILITY_DETECTED);
            expect(event.title).toBe('Vulnerability: CVE-2023-1234');
            expect(event.severity).toBe(event_severity_enum_1.EventSeverity.CRITICAL);
            expect(event.description).toBe('Critical RCE vulnerability');
            expect(event.metadata.source.type).toBe(event_source_type_enum_1.EventSourceType.VULNERABILITY_SCANNER);
            expect(event.tags).toContain('vulnerability');
            expect(event.tags).toContain('scan-result');
            expect(event.attributes.cve).toBe('CVE-2023-1234');
            expect(event.attributes.cvssScore).toBe(9.8);
            expect(event.attributes.affectedAsset).toBe('web-server-01');
            expect(event.attributes.solution).toBe('Apply security patch');
        });
        it('should calculate risk score from vulnerability data', () => {
            const criticalVuln = {
                cve: 'CVE-2023-5678',
                cvssScore: 9.5,
                exploitable: true,
                assetCriticality: 'high',
            };
            const event = event_factory_1.EventFactory.fromVulnerabilityScan(criticalVuln, 'vuln-scanner-01');
            expect(event.riskScore).toBeGreaterThan(90); // Should be very high
        });
    });
    describe('createManualEvent', () => {
        it('should create manual event', () => {
            const event = event_factory_1.EventFactory.createManualEvent(event_type_enum_1.EventType.COMPLIANCE_VIOLATION, event_severity_enum_1.EventSeverity.MEDIUM, 'Manual Compliance Check', 'Found policy violation during manual review', '<EMAIL>');
            expect(event.type).toBe(event_type_enum_1.EventType.COMPLIANCE_VIOLATION);
            expect(event.severity).toBe(event_severity_enum_1.EventSeverity.MEDIUM);
            expect(event.title).toBe('Manual Compliance Check');
            expect(event.description).toBe('Found policy violation during manual review');
            expect(event.metadata.source.type).toBe(event_source_type_enum_1.EventSourceType.MANUAL);
            expect(event.metadata.source.identifier).toBe('<EMAIL>');
            expect(event.tags).toContain('manual');
            expect(event.tags).toContain('analyst-created');
            expect(event.attributes.createdBy).toBe('<EMAIL>');
            expect(event.attributes.createdAt).toBeDefined();
        });
        it('should use provided raw data', () => {
            const customRawData = { policy: 'PCI-DSS', violation: 'unencrypted-data' };
            const event = event_factory_1.EventFactory.createManualEvent(event_type_enum_1.EventType.COMPLIANCE_VIOLATION, event_severity_enum_1.EventSeverity.HIGH, 'PCI Violation', 'Unencrypted cardholder data found', '<EMAIL>', customRawData);
            expect(event.rawData).toEqual(customRawData);
        });
    });
    describe('Data Extraction Helpers', () => {
        it('should extract title from various fields', () => {
            const logWithTitle = { title: 'Event Title' };
            const logWithMessage = { message: 'Event Message' };
            const logWithSummary = { summary: 'Event Summary' };
            const logWithEventName = { event_name: 'Event Name' };
            const logWithAlertName = { alert_name: 'Alert Name' };
            expect(event_factory_1.EventFactory.fromRawLog(logWithTitle, event_source_type_enum_1.EventSourceType.SIEM, 'siem-01').title)
                .toBe('Event Title');
            expect(event_factory_1.EventFactory.fromRawLog(logWithMessage, event_source_type_enum_1.EventSourceType.SIEM, 'siem-01').title)
                .toBe('Event Message');
            expect(event_factory_1.EventFactory.fromRawLog(logWithSummary, event_source_type_enum_1.EventSourceType.SIEM, 'siem-01').title)
                .toBe('Event Summary');
            expect(event_factory_1.EventFactory.fromRawLog(logWithEventName, event_source_type_enum_1.EventSourceType.SIEM, 'siem-01').title)
                .toBe('Event Name');
            expect(event_factory_1.EventFactory.fromRawLog(logWithAlertName, event_source_type_enum_1.EventSourceType.SIEM, 'siem-01').title)
                .toBe('Alert Name');
        });
        it('should extract timestamp from various fields', () => {
            const timestamp = '2023-01-01T10:00:00Z';
            const logWithTimestamp = { timestamp };
            const logWithAtTimestamp = { '@timestamp': timestamp };
            const logWithEventTime = { event_time: timestamp };
            const event1 = event_factory_1.EventFactory.fromRawLog(logWithTimestamp, event_source_type_enum_1.EventSourceType.SIEM, 'siem-01');
            const event2 = event_factory_1.EventFactory.fromRawLog(logWithAtTimestamp, event_source_type_enum_1.EventSourceType.SIEM, 'siem-01');
            const event3 = event_factory_1.EventFactory.fromRawLog(logWithEventTime, event_source_type_enum_1.EventSourceType.SIEM, 'siem-01');
            expect(event1.metadata.timestamp.occurredAt).toEqual(new Date(timestamp));
            expect(event2.metadata.timestamp.occurredAt).toEqual(new Date(timestamp));
            expect(event3.metadata.timestamp.occurredAt).toEqual(new Date(timestamp));
        });
        it('should infer severity from various indicators', () => {
            const criticalLog = { severity: 'critical' };
            const highLog = { level: 'error' };
            const mediumLog = { priority: 'warning' };
            const lowLog = { severity: 'info' };
            expect(event_factory_1.EventFactory.fromRawLog(criticalLog, event_source_type_enum_1.EventSourceType.SIEM, 'siem-01').severity)
                .toBe(event_severity_enum_1.EventSeverity.CRITICAL);
            expect(event_factory_1.EventFactory.fromRawLog(highLog, event_source_type_enum_1.EventSourceType.SIEM, 'siem-01').severity)
                .toBe(event_severity_enum_1.EventSeverity.HIGH);
            expect(event_factory_1.EventFactory.fromRawLog(mediumLog, event_source_type_enum_1.EventSourceType.SIEM, 'siem-01').severity)
                .toBe(event_severity_enum_1.EventSeverity.MEDIUM);
            expect(event_factory_1.EventFactory.fromRawLog(lowLog, event_source_type_enum_1.EventSourceType.SIEM, 'siem-01').severity)
                .toBe(event_severity_enum_1.EventSeverity.LOW);
        });
        it('should infer event type from content', () => {
            const loginFailure = { message: 'Authentication failed for user admin' };
            const loginSuccess = { message: 'User login successful' };
            const networkEvent = { message: 'Network connection established' };
            const malwareEvent = { message: 'Malware detected in file.exe' };
            const vulnEvent = { message: 'Vulnerability CVE-2023-1234 found' };
            const threatEvent = { message: 'Threat attack detected' };
            expect(event_factory_1.EventFactory.fromRawLog(loginFailure, event_source_type_enum_1.EventSourceType.SIEM, 'siem-01').type)
                .toBe(event_type_enum_1.EventType.LOGIN_FAILURE);
            expect(event_factory_1.EventFactory.fromRawLog(loginSuccess, event_source_type_enum_1.EventSourceType.SIEM, 'siem-01').type)
                .toBe(event_type_enum_1.EventType.LOGIN_SUCCESS);
            expect(event_factory_1.EventFactory.fromRawLog(networkEvent, event_source_type_enum_1.EventSourceType.SIEM, 'siem-01').type)
                .toBe(event_type_enum_1.EventType.CONNECTION_ESTABLISHED);
            expect(event_factory_1.EventFactory.fromRawLog(malwareEvent, event_source_type_enum_1.EventSourceType.SIEM, 'siem-01').type)
                .toBe(event_type_enum_1.EventType.MALWARE_DETECTED);
            expect(event_factory_1.EventFactory.fromRawLog(vulnEvent, event_source_type_enum_1.EventSourceType.SIEM, 'siem-01').type)
                .toBe(event_type_enum_1.EventType.VULNERABILITY_DETECTED);
            expect(event_factory_1.EventFactory.fromRawLog(threatEvent, event_source_type_enum_1.EventSourceType.SIEM, 'siem-01').type)
                .toBe(event_type_enum_1.EventType.THREAT_DETECTED);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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