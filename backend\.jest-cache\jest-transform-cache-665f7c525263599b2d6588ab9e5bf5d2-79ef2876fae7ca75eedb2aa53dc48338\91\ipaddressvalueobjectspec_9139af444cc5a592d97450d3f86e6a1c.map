{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\network\\ip-address.value-object.spec.ts", "mappings": ";;AAAA,uEAAqF;AAErF,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;IACtC,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;YAE/C,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,uCAAa,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC1C,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,EAAE,GAAG,mCAAS,CAAC,cAAc,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAEjD,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACvC,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;YACrF,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;YACrF,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;QAC3F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,UAAU,GAAG;gBACjB,UAAU;gBACV,YAAY;gBACZ,aAAa;aACd,CAAC;YAEF,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACzB,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,wCAAc,CAAC,OAAO,CAAC,CAAC;gBACnD,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAClC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,SAAS,GAAG;gBAChB,SAAS;gBACT,SAAS;gBACT,gBAAgB;aACjB,CAAC;YAEF,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACxB,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,wCAAc,CAAC,MAAM,CAAC,CAAC;gBAClD,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACjC,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,WAAW,GAAG;gBAClB,WAAW;gBACX,WAAW;gBACX,iBAAiB;aAClB,CAAC;YAEF,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAC1B,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,wCAAc,CAAC,QAAQ,CAAC,CAAC;gBACpD,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,YAAY,GAAG;gBACnB,WAAW;gBACX,iBAAiB;aAClB,CAAC;YAEF,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAC3B,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,wCAAc,CAAC,SAAS,CAAC,CAAC;gBACrD,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,YAAY,GAAG;gBACnB,aAAa;gBACb,iBAAiB;aAClB,CAAC;YAEF,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAC3B,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,wCAAc,CAAC,UAAU,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;YAC/C,MAAM,QAAQ,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAE1D,MAAM,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;YAEjD,MAAM,CAAC,EAAE,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,EAAE,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,EAAE,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;YACjD,MAAM,OAAO,GAAG,EAAE,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YAEzC,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;YAE/C,MAAM,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;YAE/C,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,uCAAa,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,yCAAyC,CAAC,CAAC;YAE3E,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;YAC1F,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;QAC9F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAEvC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,wCAAc,CAAC,QAAQ,CAAC,CAAC;YACpD,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,YAAY,GAAG;gBACnB,SAAS;gBACT,2BAA2B;aAC5B,CAAC;YAEF,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAC3B,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,wCAAc,CAAC,UAAU,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,YAAY,GAAG;gBACnB,SAAS;gBACT,SAAS;aACV,CAAC;YAEF,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAC3B,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,wCAAc,CAAC,SAAS,CAAC,CAAC;gBACrD,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,UAAU,GAAG;gBACjB,SAAS;gBACT,SAAS;aACV,CAAC;YAEF,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACzB,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,wCAAc,CAAC,OAAO,CAAC,CAAC;gBACnD,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,SAAS,GAAG;gBAChB,sBAAsB,EAAE,aAAa;gBACrC,sBAAsB,EAAE,iBAAiB;aAC1C,CAAC;YAEF,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACxB,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,wCAAc,CAAC,MAAM,CAAC,CAAC;gBAClD,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,aAAa,GAAG;gBACpB,SAAS,EAAO,kBAAkB;gBAClC,SAAS,EAAO,oBAAoB;aACrC,CAAC;YAEF,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAC5B,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvC,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,SAAS,GAAG;gBAChB,SAAS;gBACT,eAAe;gBACf,WAAW;aACZ,CAAC;YAEF,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACxB,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvC,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YAC3C,MAAM,UAAU,GAAG,EAAE,CAAC,aAAa,EAAE,CAAC;YAEtC,MAAM,CAAC,UAAU,CAAC,CAAC,UAAU,EAAE,CAAC;YAChC,MAAM,CAAC,OAAO,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;YAE/C,MAAM,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,aAAa,GAAG,mCAAS,CAAC,SAAS,EAAE,CAAC;YAC5C,MAAM,aAAa,GAAG,mCAAS,CAAC,aAAa,EAAE,CAAC;YAEhD,MAAM,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACnD,MAAM,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE9C,MAAM,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7C,MAAM,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,CAAC,mCAAS,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,mCAAS,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,mCAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjD,MAAM,CAAC,mCAAS,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,GAAG,GAAG,mCAAS,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;YAChD,MAAM,GAAG,GAAG,mCAAS,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;YAChD,MAAM,GAAG,GAAG,mCAAS,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;YAEhD,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;YAC/C,MAAM,IAAI,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC;YAEzB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACzC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,uCAAa,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,wCAAc,CAAC,OAAO,CAAC,CAAC;YACzD,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,IAAI,GAAG,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC;YACxC,MAAM,EAAE,GAAG,mCAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEpC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC1C,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAC;YAC7E,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;YAC1F,MAAM,CAAC,GAAG,EAAE,CAAC,mCAAS,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;QAC3F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;YAE/C,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC,OAAO,CAAC,wCAAwC,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;YAE/C,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC,OAAO,CAAC,0CAA0C,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;YAE/C,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;YAEnD,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,wCAAc,CAAC,SAAS,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YAE3C,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,wCAAc,CAAC,QAAQ,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,EAAE,GAAG,mCAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAEtC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,wCAAc,CAAC,QAAQ,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,GAAG,GAAG,mCAAS,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;YAChD,MAAM,GAAG,GAAG,mCAAS,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;YAEhD,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\network\\ip-address.value-object.spec.ts"], "sourcesContent": ["import { IPAddress, IPAddressType, IPAddressClass } from './ip-address.value-object';\r\n\r\ndescribe('IPAddress Value Object', () => {\r\n  describe('IPv4 Addresses', () => {\r\n    it('should create valid IPv4 address', () => {\r\n      const ip = IPAddress.fromString('***********');\r\n      \r\n      expect(ip.type).toBe(IPAddressType.IPv4);\r\n      expect(ip.toString()).toBe('***********');\r\n      expect(ip.isIPv4()).toBe(true);\r\n      expect(ip.isIPv6()).toBe(false);\r\n    });\r\n\r\n    it('should create IPv4 from octets', () => {\r\n      const ip = IPAddress.fromIPv4Octets(10, 0, 0, 1);\r\n      \r\n      expect(ip.toString()).toBe('********');\r\n      expect(ip.getOctets()).toEqual([10, 0, 0, 1]);\r\n    });\r\n\r\n    it('should validate IPv4 format', () => {\r\n      expect(() => IPAddress.fromString('256.1.1.1')).toThrow('Invalid IP address format');\r\n      expect(() => IPAddress.fromString('192.168.1')).toThrow('Invalid IP address format');\r\n      expect(() => IPAddress.fromString('***********.1')).toThrow('Invalid IP address format');\r\n    });\r\n\r\n    it('should classify private IPv4 addresses', () => {\r\n      const privateIPs = [\r\n        '********',\r\n        '**********',\r\n        '***********',\r\n      ];\r\n\r\n      privateIPs.forEach(ipStr => {\r\n        const ip = IPAddress.fromString(ipStr);\r\n        expect(ip.classify()).toBe(IPAddressClass.PRIVATE);\r\n        expect(ip.isPrivate()).toBe(true);\r\n        expect(ip.isPublic()).toBe(false);\r\n      });\r\n    });\r\n\r\n    it('should classify public IPv4 addresses', () => {\r\n      const publicIPs = [\r\n        '*******',\r\n        '*******',\r\n        '**************',\r\n      ];\r\n\r\n      publicIPs.forEach(ipStr => {\r\n        const ip = IPAddress.fromString(ipStr);\r\n        expect(ip.classify()).toBe(IPAddressClass.PUBLIC);\r\n        expect(ip.isPublic()).toBe(true);\r\n        expect(ip.isPrivate()).toBe(false);\r\n      });\r\n    });\r\n\r\n    it('should classify loopback IPv4 addresses', () => {\r\n      const loopbackIPs = [\r\n        '127.0.0.1',\r\n        '*********',\r\n        '***************',\r\n      ];\r\n\r\n      loopbackIPs.forEach(ipStr => {\r\n        const ip = IPAddress.fromString(ipStr);\r\n        expect(ip.classify()).toBe(IPAddressClass.LOOPBACK);\r\n        expect(ip.isLoopback()).toBe(true);\r\n      });\r\n    });\r\n\r\n    it('should classify multicast IPv4 addresses', () => {\r\n      const multicastIPs = [\r\n        '*********',\r\n        '***************',\r\n      ];\r\n\r\n      multicastIPs.forEach(ipStr => {\r\n        const ip = IPAddress.fromString(ipStr);\r\n        expect(ip.classify()).toBe(IPAddressClass.MULTICAST);\r\n        expect(ip.isMulticast()).toBe(true);\r\n      });\r\n    });\r\n\r\n    it('should classify link-local IPv4 addresses', () => {\r\n      const linkLocalIPs = [\r\n        '***********',\r\n        '***************',\r\n      ];\r\n\r\n      linkLocalIPs.forEach(ipStr => {\r\n        const ip = IPAddress.fromString(ipStr);\r\n        expect(ip.classify()).toBe(IPAddressClass.LINK_LOCAL);\r\n      });\r\n    });\r\n\r\n    it('should convert IPv4 to integer', () => {\r\n      const ip = IPAddress.fromString('***********');\r\n      const expected = (192 << 24) + (168 << 16) + (1 << 8) + 1;\r\n      \r\n      expect(ip.toIPv4Integer()).toBe(expected);\r\n    });\r\n\r\n    it('should check network membership for IPv4', () => {\r\n      const ip = IPAddress.fromString('***********00');\r\n      \r\n      expect(ip.isInNetwork('***********', 24)).toBe(true);\r\n      expect(ip.isInNetwork('***********', 16)).toBe(true);\r\n      expect(ip.isInNetwork('***********', 24)).toBe(false);\r\n    });\r\n\r\n    it('should get network address for IPv4', () => {\r\n      const ip = IPAddress.fromString('***********00');\r\n      const network = ip.getNetworkAddress(24);\r\n      \r\n      expect(network.toString()).toBe('***********');\r\n    });\r\n\r\n    it('should generate reverse DNS format for IPv4', () => {\r\n      const ip = IPAddress.fromString('***********');\r\n      \r\n      expect(ip.getReverseDNSFormat()).toBe('***********.in-addr.arpa');\r\n    });\r\n  });\r\n\r\n  describe('IPv6 Addresses', () => {\r\n    it('should create valid IPv6 address', () => {\r\n      const ip = IPAddress.fromString('2001:db8::1');\r\n      \r\n      expect(ip.type).toBe(IPAddressType.IPv6);\r\n      expect(ip.isIPv6()).toBe(true);\r\n      expect(ip.isIPv4()).toBe(false);\r\n    });\r\n\r\n    it('should create full IPv6 address', () => {\r\n      const ip = IPAddress.fromString('2001:0db8:85a3:0000:0000:8a2e:0370:7334');\r\n      \r\n      expect(ip.isIPv6()).toBe(true);\r\n      expect(ip.getSegments()).toHaveLength(8);\r\n    });\r\n\r\n    it('should validate IPv6 format', () => {\r\n      expect(() => IPAddress.fromString('2001:db8::1::2')).toThrow('Invalid IP address format');\r\n      expect(() => IPAddress.fromString('2001:db8:gggg::1')).toThrow('Invalid IP address format');\r\n    });\r\n\r\n    it('should classify loopback IPv6 address', () => {\r\n      const ip = IPAddress.fromString('::1');\r\n      \r\n      expect(ip.classify()).toBe(IPAddressClass.LOOPBACK);\r\n      expect(ip.isLoopback()).toBe(true);\r\n    });\r\n\r\n    it('should classify link-local IPv6 addresses', () => {\r\n      const linkLocalIPs = [\r\n        'fe80::1',\r\n        'fe80::1234:5678:9abc:def0',\r\n      ];\r\n\r\n      linkLocalIPs.forEach(ipStr => {\r\n        const ip = IPAddress.fromString(ipStr);\r\n        expect(ip.classify()).toBe(IPAddressClass.LINK_LOCAL);\r\n      });\r\n    });\r\n\r\n    it('should classify multicast IPv6 addresses', () => {\r\n      const multicastIPs = [\r\n        'ff00::1',\r\n        'ff02::1',\r\n      ];\r\n\r\n      multicastIPs.forEach(ipStr => {\r\n        const ip = IPAddress.fromString(ipStr);\r\n        expect(ip.classify()).toBe(IPAddressClass.MULTICAST);\r\n        expect(ip.isMulticast()).toBe(true);\r\n      });\r\n    });\r\n\r\n    it('should classify private IPv6 addresses', () => {\r\n      const privateIPs = [\r\n        'fc00::1',\r\n        'fd00::1',\r\n      ];\r\n\r\n      privateIPs.forEach(ipStr => {\r\n        const ip = IPAddress.fromString(ipStr);\r\n        expect(ip.classify()).toBe(IPAddressClass.PRIVATE);\r\n        expect(ip.isPrivate()).toBe(true);\r\n      });\r\n    });\r\n\r\n    it('should classify public IPv6 addresses', () => {\r\n      const publicIPs = [\r\n        '2001:4860:4860::8888', // Google DNS\r\n        '2606:4700:4700::1111', // Cloudflare DNS\r\n      ];\r\n\r\n      publicIPs.forEach(ipStr => {\r\n        const ip = IPAddress.fromString(ipStr);\r\n        expect(ip.classify()).toBe(IPAddressClass.PUBLIC);\r\n        expect(ip.isPublic()).toBe(true);\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Security Analysis', () => {\r\n    it('should detect suspicious patterns in IPv4', () => {\r\n      const suspiciousIPs = [\r\n        '*******',      // All same octets\r\n        '*******',      // Sequential octets\r\n      ];\r\n\r\n      suspiciousIPs.forEach(ipStr => {\r\n        const ip = IPAddress.fromString(ipStr);\r\n        expect(ip.isSuspicious()).toBe(true);\r\n      });\r\n    });\r\n\r\n    it('should not flag normal IPs as suspicious', () => {\r\n      const normalIPs = [\r\n        '*******',\r\n        '***********00',\r\n        '*********',\r\n      ];\r\n\r\n      normalIPs.forEach(ipStr => {\r\n        const ip = IPAddress.fromString(ipStr);\r\n        expect(ip.isSuspicious()).toBe(false);\r\n      });\r\n    });\r\n\r\n    it('should provide region hints for public IPs', () => {\r\n      const ip = IPAddress.fromString('*******');\r\n      const regionHint = ip.getRegionHint();\r\n      \r\n      expect(regionHint).toBeTruthy();\r\n      expect(typeof regionHint).toBe('string');\r\n    });\r\n\r\n    it('should not provide region hints for private IPs', () => {\r\n      const ip = IPAddress.fromString('***********');\r\n      \r\n      expect(ip.getRegionHint()).toBeNull();\r\n    });\r\n  });\r\n\r\n  describe('Utility Methods', () => {\r\n    it('should create localhost addresses', () => {\r\n      const ipv4Localhost = IPAddress.localhost();\r\n      const ipv6Localhost = IPAddress.localhostIPv6();\r\n      \r\n      expect(ipv4Localhost.toString()).toBe('127.0.0.1');\r\n      expect(ipv4Localhost.isLoopback()).toBe(true);\r\n      \r\n      expect(ipv6Localhost.toString()).toBe('::1');\r\n      expect(ipv6Localhost.isLoopback()).toBe(true);\r\n    });\r\n\r\n    it('should validate IP address format', () => {\r\n      expect(IPAddress.isValid('***********')).toBe(true);\r\n      expect(IPAddress.isValid('2001:db8::1')).toBe(true);\r\n      expect(IPAddress.isValid('invalid')).toBe(false);\r\n      expect(IPAddress.isValid('256.1.1.1')).toBe(false);\r\n    });\r\n\r\n    it('should compare IP addresses for equality', () => {\r\n      const ip1 = IPAddress.fromString('***********');\r\n      const ip2 = IPAddress.fromString('***********');\r\n      const ip3 = IPAddress.fromString('***********');\r\n      \r\n      expect(ip1.equals(ip2)).toBe(true);\r\n      expect(ip1.equals(ip3)).toBe(false);\r\n      expect(ip1.equals(undefined)).toBe(false);\r\n    });\r\n\r\n    it('should serialize to JSON', () => {\r\n      const ip = IPAddress.fromString('***********');\r\n      const json = ip.toJSON();\r\n      \r\n      expect(json.address).toBe('***********');\r\n      expect(json.type).toBe(IPAddressType.IPv4);\r\n      expect(json.classification).toBe(IPAddressClass.PRIVATE);\r\n      expect(json.isPrivate).toBe(true);\r\n      expect(json.isPublic).toBe(false);\r\n    });\r\n\r\n    it('should deserialize from JSON', () => {\r\n      const json = { address: '***********' };\r\n      const ip = IPAddress.fromJSON(json);\r\n      \r\n      expect(ip.toString()).toBe('***********');\r\n      expect(ip.isIPv4()).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('Error Handling', () => {\r\n    it('should throw error for empty IP address', () => {\r\n      expect(() => IPAddress.fromString('')).toThrow('IP address cannot be empty');\r\n      expect(() => IPAddress.fromString('   ')).toThrow('IP address cannot be empty');\r\n    });\r\n\r\n    it('should throw error for invalid IPv4 octets', () => {\r\n      expect(() => IPAddress.fromIPv4Octets(256, 1, 1, 1)).toThrow('Invalid IP address format');\r\n      expect(() => IPAddress.fromIPv4Octets(-1, 1, 1, 1)).toThrow('Invalid IP address format');\r\n    });\r\n\r\n    it('should throw error when getting octets from IPv6', () => {\r\n      const ip = IPAddress.fromString('2001:db8::1');\r\n      \r\n      expect(() => ip.getOctets()).toThrow('Cannot get octets for non-IPv4 address');\r\n    });\r\n\r\n    it('should throw error when getting segments from IPv4', () => {\r\n      const ip = IPAddress.fromString('***********');\r\n      \r\n      expect(() => ip.getSegments()).toThrow('Cannot get segments for non-IPv6 address');\r\n    });\r\n\r\n    it('should throw error when converting IPv6 to integer', () => {\r\n      const ip = IPAddress.fromString('2001:db8::1');\r\n      \r\n      expect(() => ip.toIPv4Integer()).toThrow('Cannot convert non-IPv4 address to integer');\r\n    });\r\n  });\r\n\r\n  describe('Edge Cases', () => {\r\n    it('should handle broadcast address', () => {\r\n      const ip = IPAddress.fromString('***************');\r\n      \r\n      expect(ip.classify()).toBe(IPAddressClass.BROADCAST);\r\n    });\r\n\r\n    it('should handle zero address', () => {\r\n      const ip = IPAddress.fromString('0.0.0.0');\r\n      \r\n      expect(ip.classify()).toBe(IPAddressClass.RESERVED);\r\n    });\r\n\r\n    it('should handle IPv6 zero address', () => {\r\n      const ip = IPAddress.fromString('::');\r\n      \r\n      expect(ip.classify()).toBe(IPAddressClass.RESERVED);\r\n    });\r\n\r\n    it('should handle case-insensitive IPv6 comparison', () => {\r\n      const ip1 = IPAddress.fromString('2001:DB8::1');\r\n      const ip2 = IPAddress.fromString('2001:db8::1');\r\n      \r\n      expect(ip1.equals(ip2)).toBe(true);\r\n    });\r\n  });\r\n});\r\n"], "version": 3}