16f354bf9c3be15eb62360174da6c60d
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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