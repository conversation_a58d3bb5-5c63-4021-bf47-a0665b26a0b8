{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\event-metadata\\event-source.value-object.ts", "mappings": ";;;AAAA,iGAA4F;AAC5F,+EAAqE;AAsBrE;;;;;;;;;;;;;GAaG;AACH,MAAa,WAAY,SAAQ,mCAAiC;IAChE,YAAY,KAAuB;QACjC,KAAK,CAAC,KAAK,CAAC,CAAC;IACf,CAAC;IAES,QAAQ;QAChB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,wCAAe,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/D,MAAM,IAAI,KAAK,CAAC,8BAA8B,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1E,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACnE,CAAC;QAED,wEAAwE;QACxE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC;YACtD,MAAM,IAAI,KAAK,CAAC,mGAAmG,CAAC,CAAC;QACvH,CAAC;QAED,sCAAsC;QACtC,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,+BAA+B,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;YACtF,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;QAC7E,CAAC;QAED,uCAAuC;QACvC,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YAC9D,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM,CACX,IAAqB,EACrB,UAAkB,EAClB,OAAgE;QAEhE,OAAO,IAAI,WAAW,CAAC;YACrB,IAAI;YACJ,UAAU;YACV,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CACjB,IAAqB,EACrB,QAAgB,EAChB,OAAgE;QAEhE,OAAO,WAAW,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,EAAE;YACxC,IAAI,EAAE,QAAQ;YACd,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAClB,IAAqB,EACrB,SAAiB,EACjB,OAAgE;QAEhE,OAAO,WAAW,CAAC,MAAM,CAAC,IAAI,EAAE,SAAS,EAAE;YACzC,IAAI,EAAE,GAAG,IAAI,IAAI,SAAS,EAAE;YAC5B,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM,CACX,UAAkB,EAClB,OAAgE;QAEhE,OAAO,WAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,MAAM,EAAE,UAAU,EAAE;YAC5D,IAAI,EAAE,uBAAuB;YAC7B,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,CACZ,UAAkB,EAClB,OAAgE;QAEhE,OAAO,WAAW,CAAC,MAAM,CAAC,wCAAe,CAAC,OAAO,EAAE,UAAU,EAAE;YAC7D,IAAI,EAAE,gBAAgB;YACtB,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,GAAW;QACrB,OAAO,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,WAAW,CAAU,GAAW;QAC9B,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAM,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,WAAW;QACT,IAAI,IAAI,CAAC,eAAe,EAAE;YAAE,OAAO,SAAS,CAAC;QAC7C,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAAE,OAAO,UAAU,CAAC;QAC/C,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAAE,OAAO,aAAa,CAAC;QACrD,IAAI,IAAI,CAAC,aAAa,EAAE;YAAE,OAAO,OAAO,CAAC;QACzC,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAAE,OAAO,UAAU,CAAC;QAC/C,IAAI,IAAI,CAAC,cAAc,EAAE;YAAE,OAAO,eAAe,CAAC;QAClD,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAAE,OAAO,UAAU,CAAC;QAC/C,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,eAAe;QACb,MAAM,YAAY,GAAG;YACnB,wCAAe,CAAC,QAAQ;YACxB,wCAAe,CAAC,OAAO;YACvB,wCAAe,CAAC,cAAc;YAC9B,wCAAe,CAAC,aAAa;YAC7B,wCAAe,CAAC,WAAW;YAC3B,wCAAe,CAAC,GAAG;YACnB,wCAAe,CAAC,UAAU;YAC1B,wCAAe,CAAC,WAAW;SAC5B,CAAC;QACF,OAAO,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,MAAM,aAAa,GAAG;YACpB,wCAAe,CAAC,GAAG;YACnB,wCAAe,CAAC,SAAS;YACzB,wCAAe,CAAC,IAAI;YACpB,wCAAe,CAAC,gBAAgB;YAChC,wCAAe,CAAC,GAAG;YACnB,wCAAe,CAAC,WAAW;SAC5B,CAAC;QACF,OAAO,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,MAAM,QAAQ,GAAG;YACf,wCAAe,CAAC,GAAG;YACnB,wCAAe,CAAC,GAAG;YACnB,wCAAe,CAAC,QAAQ;YACxB,wCAAe,CAAC,UAAU;YAC1B,wCAAe,CAAC,kBAAkB;YAClC,wCAAe,CAAC,kBAAkB;YAClC,wCAAe,CAAC,WAAW;SAC5B,CAAC;QACF,OAAO,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,aAAa;QACX,MAAM,UAAU,GAAG;YACjB,wCAAe,CAAC,GAAG;YACnB,wCAAe,CAAC,KAAK;YACrB,wCAAe,CAAC,GAAG;YACnB,wCAAe,CAAC,IAAI;YACpB,wCAAe,CAAC,IAAI;YACpB,wCAAe,CAAC,kBAAkB;YAClC,wCAAe,CAAC,UAAU;SAC3B,CAAC;QACF,OAAO,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,MAAM,aAAa,GAAG;YACpB,wCAAe,CAAC,iBAAiB;YACjC,wCAAe,CAAC,GAAG;YACnB,wCAAe,CAAC,GAAG;YACnB,wCAAe,CAAC,GAAG;YACnB,wCAAe,CAAC,GAAG;SACpB,CAAC;QACF,OAAO,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,MAAM,aAAa,GAAG;YACpB,wCAAe,CAAC,IAAI;YACpB,wCAAe,CAAC,IAAI;YACpB,wCAAe,CAAC,qBAAqB;YACrC,wCAAe,CAAC,mBAAmB;YACnC,wCAAe,CAAC,SAAS;YACzB,wCAAe,CAAC,OAAO;YACvB,wCAAe,CAAC,SAAS;SAC1B,CAAC;QACF,OAAO,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,MAAM,aAAa,GAAG;YACpB,wCAAe,CAAC,aAAa;YAC7B,wCAAe,CAAC,WAAW;YAC3B,wCAAe,CAAC,UAAU;YAC1B,wCAAe,CAAC,gBAAgB;SACjC,CAAC;QACF,OAAO,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,SAAS;QACP,kEAAkE;QAClE,OAAO,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,wCAAe,CAAC,mBAAmB,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,MAAM,SAAS,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAEjD,2BAA2B;QAC3B,IAAI,aAAa,GAAG,SAAS,CAAC;QAE9B,gCAAgC;QAChC,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;YAC1B,aAAa,IAAI,CAAC,CAAC;QACrB,CAAC;QAED,oCAAoC;QACpC,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACxB,aAAa,IAAI,CAAC,CAAC;QACrB,CAAC;QAED,6CAA6C;QAC7C,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YACzB,aAAa,IAAI,CAAC,CAAC;QACrB,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;IACtC,CAAC;IAEO,uBAAuB;QAC7B,MAAM,MAAM,GAA6C;YACvD,CAAC,wCAAe,CAAC,IAAI,CAAC,EAAE,EAAE;YAC1B,CAAC,wCAAe,CAAC,GAAG,CAAC,EAAE,EAAE;YACzB,CAAC,wCAAe,CAAC,OAAO,CAAC,EAAE,EAAE;YAC7B,CAAC,wCAAe,CAAC,QAAQ,CAAC,EAAE,EAAE;YAC9B,CAAC,wCAAe,CAAC,gBAAgB,CAAC,EAAE,EAAE;YACtC,CAAC,wCAAe,CAAC,SAAS,CAAC,EAAE,EAAE;YAC/B,CAAC,wCAAe,CAAC,GAAG,CAAC,EAAE,EAAE;YACzB,CAAC,wCAAe,CAAC,qBAAqB,CAAC,EAAE,EAAE;YAC3C,CAAC,wCAAe,CAAC,iBAAiB,CAAC,EAAE,EAAE;YACvC,CAAC,wCAAe,CAAC,aAAa,CAAC,EAAE,EAAE;YACnC,CAAC,wCAAe,CAAC,WAAW,CAAC,EAAE,EAAE;YACjC,CAAC,wCAAe,CAAC,UAAU,CAAC,EAAE,EAAE;YAChC,CAAC,wCAAe,CAAC,MAAM,CAAC,EAAE,EAAE;YAC5B,CAAC,wCAAe,CAAC,OAAO,CAAC,EAAE,EAAE;SAC9B,CAAC;QAEF,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IACxC,CAAC;IAEO,cAAc;QACpB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;YAAE,OAAO,KAAK,CAAC;QAEtC,MAAM,YAAY,GAAG;YACnB,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,YAAY;YAC3D,QAAQ,EAAE,SAAS,EAAE,aAAa,EAAE,aAAa,EAAE,UAAU;YAC7D,QAAQ,EAAE,aAAa,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS;YACrD,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ;SAChD,CAAC;QAEF,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QAChD,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,MAAM,UAAU,GAA6C;YAC3D,CAAC,wCAAe,CAAC,GAAG,CAAC,EAAE,EAAE;YACzB,CAAC,wCAAe,CAAC,OAAO,CAAC,EAAE,CAAC;YAC5B,CAAC,wCAAe,CAAC,SAAS,CAAC,EAAE,CAAC;YAC9B,CAAC,wCAAe,CAAC,mBAAmB,CAAC,EAAE,CAAC;YACxC,CAAC,wCAAe,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7B,CAAC,wCAAe,CAAC,GAAG,CAAC,EAAE,CAAC;YACxB,CAAC,wCAAe,CAAC,SAAS,CAAC,EAAE,CAAC;YAC9B,CAAC,wCAAe,CAAC,GAAG,CAAC,EAAE,CAAC;YACxB,CAAC,wCAAe,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACrC,CAAC,wCAAe,CAAC,iBAAiB,CAAC,EAAE,CAAC;YACtC,CAAC,wCAAe,CAAC,cAAc,CAAC,EAAE,CAAC;YACnC,CAAC,wCAAe,CAAC,UAAU,CAAC,EAAE,CAAC;YAC/B,CAAC,wCAAe,CAAC,UAAU,CAAC,EAAE,CAAC;YAC/B,CAAC,wCAAe,CAAC,aAAa,CAAC,EAAE,CAAC;YAClC,CAAC,wCAAe,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3B,CAAC,wCAAe,CAAC,OAAO,CAAC,EAAE,CAAC;SAC7B,CAAC;QAEF,OAAO,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,MAAM,eAAe,GAAG;YACtB,wCAAe,CAAC,QAAQ;YACxB,wCAAe,CAAC,cAAc;YAC9B,wCAAe,CAAC,UAAU;YAC1B,wCAAe,CAAC,UAAU;YAC1B,wCAAe,CAAC,gBAAgB;YAChC,wCAAe,CAAC,QAAQ;SACzB,CAAC;QACF,OAAO,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,MAAM,YAAY,GAA6C;YAC7D,CAAC,wCAAe,CAAC,QAAQ,CAAC,EAAE,yCAAyC;YACrE,CAAC,wCAAe,CAAC,OAAO,CAAC,EAAE,uCAAuC;YAClE,CAAC,wCAAe,CAAC,GAAG,CAAC,EAAE,wCAAwC;YAC/D,CAAC,wCAAe,CAAC,IAAI,CAAC,EAAE,2CAA2C;YACnE,CAAC,wCAAe,CAAC,MAAM,CAAC,EAAE,2CAA2C;YACrE,CAAC,wCAAe,CAAC,OAAO,CAAC,EAAE,gCAAgC;SAC5D,CAAC;QAEF,OAAO,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,SAAS,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,QAA6B;QACxC,OAAO,IAAI,WAAW,CAAC;YACrB,GAAG,IAAI,CAAC,MAAM;YACd,QAAQ,EAAE,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,QAAQ,EAAE;SAC5C,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,QAAgB;QAC3B,OAAO,IAAI,WAAW,CAAC;YACrB,GAAG,IAAI,CAAC,MAAM;YACd,QAAQ;SACT,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,OAAe;QACzB,OAAO,IAAI,WAAW,CAAC;YACrB,GAAG,IAAI,CAAC,MAAM;YACd,OAAO;SACR,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,UAAU;QAUR,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;YACtB,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;YAClC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE;YAC5B,WAAW,EAAE,IAAI,CAAC,mBAAmB,EAAE;YACvC,QAAQ,EAAE,IAAI,CAAC,qBAAqB,EAAE;YACtC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE;YAC3B,YAAY,EAAE,IAAI,CAAC,kBAAkB,EAAE;SACxC,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAmB;QAC/B,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,CAAC,MAAM,CAAC,IAAI;YACtC,IAAI,CAAC,MAAM,CAAC,UAAU,KAAK,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC;IAC5D,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;IACzD,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;YACtB,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;YAClC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;YACtB,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;YAC5B,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;YAC1B,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;YAC9B,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;YAC9B,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE;YAC1B,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE;SACnC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,IAAyB;QACvC,OAAO,IAAI,WAAW,CAAC;YACrB,IAAI,EAAE,IAAI,CAAC,IAAuB;YAClC,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,CAAC,IAAqB,EAAE,UAAkB;QACtD,IAAI,CAAC;YACH,IAAI,WAAW,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;YACtC,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF;AAzhBD,kCAyhBC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\value-objects\\event-metadata\\event-source.value-object.ts"], "sourcesContent": ["import { BaseValueObject } from '../../../../shared-kernel/value-objects/base-value-object';\r\nimport { EventSourceType } from '../../enums/event-source-type.enum';\r\n\r\n/**\r\n * Event Source Properties\r\n */\r\nexport interface EventSourceProps {\r\n  /** Type of the event source */\r\n  type: EventSourceType;\r\n  /** Unique identifier for the source instance */\r\n  identifier: string;\r\n  /** Human-readable name for the source */\r\n  name?: string;\r\n  /** Version of the source system */\r\n  version?: string;\r\n  /** Vendor/manufacturer of the source */\r\n  vendor?: string;\r\n  /** Location or zone of the source */\r\n  location?: string;\r\n  /** Additional metadata about the source */\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Event Source Value Object\r\n * \r\n * Represents the source system that generated a security event.\r\n * Provides identification, classification, and metadata about event origins.\r\n * \r\n * Key features:\r\n * - Source type classification and validation\r\n * - Unique source identification\r\n * - Vendor and version tracking\r\n * - Location and zone information\r\n * - Extensible metadata support\r\n * - Reliability and trust scoring\r\n */\r\nexport class EventSource extends BaseValueObject<EventSourceProps> {\r\n  constructor(props: EventSourceProps) {\r\n    super(props);\r\n  }\r\n\r\n  protected validate(): void {\r\n    if (!this._value.type) {\r\n      throw new Error('Event source must have a type');\r\n    }\r\n\r\n    if (!Object.values(EventSourceType).includes(this._value.type)) {\r\n      throw new Error(`Invalid event source type: ${this._value.type}`);\r\n    }\r\n\r\n    if (!this._value.identifier || this._value.identifier.trim().length === 0) {\r\n      throw new Error('Event source must have a non-empty identifier');\r\n    }\r\n\r\n    // Validate identifier format (alphanumeric, hyphens, underscores, dots)\r\n    if (!/^[a-zA-Z0-9._-]+$/.test(this._value.identifier)) {\r\n      throw new Error('Event source identifier must contain only alphanumeric characters, dots, hyphens, and underscores');\r\n    }\r\n\r\n    // Validate version format if provided\r\n    if (this._value.version && !/^\\d+\\.\\d+(\\.\\d+)?(-[\\w.-]+)?$/.test(this._value.version)) {\r\n      throw new Error('Event source version must be in semantic version format');\r\n    }\r\n\r\n    // Validate location format if provided\r\n    if (this._value.location && this._value.location.length > 100) {\r\n      throw new Error('Event source location must be 100 characters or less');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create an event source with type and identifier\r\n   */\r\n  static create(\r\n    type: EventSourceType,\r\n    identifier: string,\r\n    options?: Partial<Omit<EventSourceProps, 'type' | 'identifier'>>\r\n  ): EventSource {\r\n    return new EventSource({\r\n      type,\r\n      identifier,\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create an event source from hostname\r\n   */\r\n  static fromHostname(\r\n    type: EventSourceType,\r\n    hostname: string,\r\n    options?: Partial<Omit<EventSourceProps, 'type' | 'identifier'>>\r\n  ): EventSource {\r\n    return EventSource.create(type, hostname, {\r\n      name: hostname,\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create an event source from IP address\r\n   */\r\n  static fromIPAddress(\r\n    type: EventSourceType,\r\n    ipAddress: string,\r\n    options?: Partial<Omit<EventSourceProps, 'type' | 'identifier'>>\r\n  ): EventSource {\r\n    return EventSource.create(type, ipAddress, {\r\n      name: `${type}-${ipAddress}`,\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a manual event source\r\n   */\r\n  static manual(\r\n    identifier: string,\r\n    options?: Partial<Omit<EventSourceProps, 'type' | 'identifier'>>\r\n  ): EventSource {\r\n    return EventSource.create(EventSourceType.MANUAL, identifier, {\r\n      name: 'Manual Event Creation',\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create an unknown event source\r\n   */\r\n  static unknown(\r\n    identifier: string,\r\n    options?: Partial<Omit<EventSourceProps, 'type' | 'identifier'>>\r\n  ): EventSource {\r\n    return EventSource.create(EventSourceType.UNKNOWN, identifier, {\r\n      name: 'Unknown Source',\r\n      ...options,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get the source type\r\n   */\r\n  get type(): EventSourceType {\r\n    return this._value.type;\r\n  }\r\n\r\n  /**\r\n   * Get the source identifier\r\n   */\r\n  get identifier(): string {\r\n    return this._value.identifier;\r\n  }\r\n\r\n  /**\r\n   * Get the source name\r\n   */\r\n  get name(): string {\r\n    return this._value.name || this._value.identifier;\r\n  }\r\n\r\n  /**\r\n   * Get the source version\r\n   */\r\n  get version(): string | undefined {\r\n    return this._value.version;\r\n  }\r\n\r\n  /**\r\n   * Get the source vendor\r\n   */\r\n  get vendor(): string | undefined {\r\n    return this._value.vendor;\r\n  }\r\n\r\n  /**\r\n   * Get the source location\r\n   */\r\n  get location(): string | undefined {\r\n    return this._value.location;\r\n  }\r\n\r\n  /**\r\n   * Get source metadata\r\n   */\r\n  get metadata(): Record<string, any> {\r\n    return this._value.metadata || {};\r\n  }\r\n\r\n  /**\r\n   * Check if source has specific metadata\r\n   */\r\n  hasMetadata(key: string): boolean {\r\n    return key in this.metadata;\r\n  }\r\n\r\n  /**\r\n   * Get metadata value\r\n   */\r\n  getMetadata<T = any>(key: string): T | undefined {\r\n    return this.metadata[key] as T;\r\n  }\r\n\r\n  /**\r\n   * Get source category\r\n   */\r\n  getCategory(): string {\r\n    if (this.isNetworkSource()) return 'Network';\r\n    if (this.isEndpointSource()) return 'Endpoint';\r\n    if (this.isApplicationSource()) return 'Application';\r\n    if (this.isCloudSource()) return 'Cloud';\r\n    if (this.isIdentitySource()) return 'Identity';\r\n    if (this.isSecurityTool()) return 'Security Tool';\r\n    if (this.isExternalSource()) return 'External';\r\n    return 'Other';\r\n  }\r\n\r\n  /**\r\n   * Check if this is a network source\r\n   */\r\n  isNetworkSource(): boolean {\r\n    const networkTypes = [\r\n      EventSourceType.FIREWALL,\r\n      EventSourceType.IDS_IPS,\r\n      EventSourceType.NETWORK_DEVICE,\r\n      EventSourceType.LOAD_BALANCER,\r\n      EventSourceType.VPN_GATEWAY,\r\n      EventSourceType.NAC,\r\n      EventSourceType.DNS_SERVER,\r\n      EventSourceType.DHCP_SERVER,\r\n    ];\r\n    return networkTypes.includes(this._value.type);\r\n  }\r\n\r\n  /**\r\n   * Check if this is an endpoint source\r\n   */\r\n  isEndpointSource(): boolean {\r\n    const endpointTypes = [\r\n      EventSourceType.EDR,\r\n      EventSourceType.ANTIVIRUS,\r\n      EventSourceType.HIDS,\r\n      EventSourceType.OPERATING_SYSTEM,\r\n      EventSourceType.MDM,\r\n      EventSourceType.USB_CONTROL,\r\n    ];\r\n    return endpointTypes.includes(this._value.type);\r\n  }\r\n\r\n  /**\r\n   * Check if this is an application source\r\n   */\r\n  isApplicationSource(): boolean {\r\n    const appTypes = [\r\n      EventSourceType.WAF,\r\n      EventSourceType.APM,\r\n      EventSourceType.DATABASE,\r\n      EventSourceType.WEB_SERVER,\r\n      EventSourceType.APPLICATION_SERVER,\r\n      EventSourceType.CUSTOM_APPLICATION,\r\n      EventSourceType.API_GATEWAY,\r\n    ];\r\n    return appTypes.includes(this._value.type);\r\n  }\r\n\r\n  /**\r\n   * Check if this is a cloud source\r\n   */\r\n  isCloudSource(): boolean {\r\n    const cloudTypes = [\r\n      EventSourceType.AWS,\r\n      EventSourceType.AZURE,\r\n      EventSourceType.GCP,\r\n      EventSourceType.CASB,\r\n      EventSourceType.CWPP,\r\n      EventSourceType.CONTAINER_SECURITY,\r\n      EventSourceType.KUBERNETES,\r\n    ];\r\n    return cloudTypes.includes(this._value.type);\r\n  }\r\n\r\n  /**\r\n   * Check if this is an identity source\r\n   */\r\n  isIdentitySource(): boolean {\r\n    const identityTypes = [\r\n      EventSourceType.DIRECTORY_SERVICE,\r\n      EventSourceType.SSO,\r\n      EventSourceType.MFA,\r\n      EventSourceType.PAM,\r\n      EventSourceType.IGA,\r\n    ];\r\n    return identityTypes.includes(this._value.type);\r\n  }\r\n\r\n  /**\r\n   * Check if this is a security tool\r\n   */\r\n  isSecurityTool(): boolean {\r\n    const securityTypes = [\r\n      EventSourceType.SIEM,\r\n      EventSourceType.SOAR,\r\n      EventSourceType.VULNERABILITY_SCANNER,\r\n      EventSourceType.THREAT_INTELLIGENCE,\r\n      EventSourceType.DECEPTION,\r\n      EventSourceType.SANDBOX,\r\n      EventSourceType.FORENSICS,\r\n    ];\r\n    return securityTypes.includes(this._value.type);\r\n  }\r\n\r\n  /**\r\n   * Check if this is an external source\r\n   */\r\n  isExternalSource(): boolean {\r\n    const externalTypes = [\r\n      EventSourceType.EXTERNAL_FEED,\r\n      EventSourceType.THIRD_PARTY,\r\n      EventSourceType.GOVERNMENT,\r\n      EventSourceType.INDUSTRY_SHARING,\r\n    ];\r\n    return externalTypes.includes(this._value.type);\r\n  }\r\n\r\n  /**\r\n   * Check if this is a trusted source\r\n   */\r\n  isTrusted(): boolean {\r\n    // Internal security tools and known systems are generally trusted\r\n    return this.isSecurityTool() || \r\n           this.isIdentitySource() || \r\n           this._value.type === EventSourceType.INTERNAL_AUTOMATION;\r\n  }\r\n\r\n  /**\r\n   * Get reliability score (0-100)\r\n   */\r\n  getReliabilityScore(): number {\r\n    const baseScore = this.getBaseReliabilityScore();\r\n    \r\n    // Adjust based on metadata\r\n    let adjustedScore = baseScore;\r\n    \r\n    // Boost score for known vendors\r\n    if (this.hasKnownVendor()) {\r\n      adjustedScore += 5;\r\n    }\r\n    \r\n    // Boost score for versioned sources\r\n    if (this._value.version) {\r\n      adjustedScore += 3;\r\n    }\r\n    \r\n    // Boost score for sources with location info\r\n    if (this._value.location) {\r\n      adjustedScore += 2;\r\n    }\r\n    \r\n    return Math.min(100, adjustedScore);\r\n  }\r\n\r\n  private getBaseReliabilityScore(): number {\r\n    const scores: Partial<Record<EventSourceType, number>> = {\r\n      [EventSourceType.SIEM]: 95,\r\n      [EventSourceType.EDR]: 90,\r\n      [EventSourceType.IDS_IPS]: 90,\r\n      [EventSourceType.FIREWALL]: 85,\r\n      [EventSourceType.OPERATING_SYSTEM]: 85,\r\n      [EventSourceType.ANTIVIRUS]: 80,\r\n      [EventSourceType.WAF]: 80,\r\n      [EventSourceType.VULNERABILITY_SCANNER]: 80,\r\n      [EventSourceType.DIRECTORY_SERVICE]: 80,\r\n      [EventSourceType.EXTERNAL_FEED]: 60,\r\n      [EventSourceType.THIRD_PARTY]: 60,\r\n      [EventSourceType.IOT_DEVICE]: 50,\r\n      [EventSourceType.MANUAL]: 70,\r\n      [EventSourceType.UNKNOWN]: 30,\r\n    };\r\n    \r\n    return scores[this._value.type] || 50;\r\n  }\r\n\r\n  private hasKnownVendor(): boolean {\r\n    if (!this._value.vendor) return false;\r\n    \r\n    const knownVendors = [\r\n      'microsoft', 'cisco', 'palo alto', 'fortinet', 'checkpoint',\r\n      'splunk', 'elastic', 'crowdstrike', 'sentinelone', 'symantec',\r\n      'mcafee', 'trend micro', 'kaspersky', 'f5', 'juniper',\r\n      'amazon', 'google', 'azure', 'vmware', 'citrix'\r\n    ];\r\n    \r\n    const vendor = this._value.vendor.toLowerCase();\r\n    return knownVendors.some(known => vendor.includes(known));\r\n  }\r\n\r\n  /**\r\n   * Get processing priority (1-10, higher = more priority)\r\n   */\r\n  getProcessingPriority(): number {\r\n    const priorities: Partial<Record<EventSourceType, number>> = {\r\n      [EventSourceType.EDR]: 10,\r\n      [EventSourceType.IDS_IPS]: 9,\r\n      [EventSourceType.DECEPTION]: 9,\r\n      [EventSourceType.THREAT_INTELLIGENCE]: 8,\r\n      [EventSourceType.FIREWALL]: 7,\r\n      [EventSourceType.WAF]: 7,\r\n      [EventSourceType.ANTIVIRUS]: 7,\r\n      [EventSourceType.PAM]: 8,\r\n      [EventSourceType.OPERATING_SYSTEM]: 6,\r\n      [EventSourceType.DIRECTORY_SERVICE]: 6,\r\n      [EventSourceType.NETWORK_DEVICE]: 5,\r\n      [EventSourceType.WEB_SERVER]: 5,\r\n      [EventSourceType.IOT_DEVICE]: 4,\r\n      [EventSourceType.ENVIRONMENTAL]: 3,\r\n      [EventSourceType.MANUAL]: 6,\r\n      [EventSourceType.UNKNOWN]: 2,\r\n    };\r\n    \r\n    return priorities[this._value.type] || 5;\r\n  }\r\n\r\n  /**\r\n   * Check if source typically generates high volume\r\n   */\r\n  isHighVolumeSource(): boolean {\r\n    const highVolumeTypes = [\r\n      EventSourceType.FIREWALL,\r\n      EventSourceType.NETWORK_DEVICE,\r\n      EventSourceType.WEB_SERVER,\r\n      EventSourceType.DNS_SERVER,\r\n      EventSourceType.OPERATING_SYSTEM,\r\n      EventSourceType.DATABASE,\r\n    ];\r\n    return highVolumeTypes.includes(this._value.type);\r\n  }\r\n\r\n  /**\r\n   * Get source description\r\n   */\r\n  getDescription(): string {\r\n    const descriptions: Partial<Record<EventSourceType, string>> = {\r\n      [EventSourceType.FIREWALL]: 'Network firewall and security appliance',\r\n      [EventSourceType.IDS_IPS]: 'Intrusion Detection/Prevention System',\r\n      [EventSourceType.EDR]: 'Endpoint Detection and Response system',\r\n      [EventSourceType.SIEM]: 'Security Information and Event Management',\r\n      [EventSourceType.MANUAL]: 'Manual event creation by security analyst',\r\n      [EventSourceType.UNKNOWN]: 'Unknown or unidentified source',\r\n    };\r\n    \r\n    return descriptions[this._value.type] || `${this._value.type} source`;\r\n  }\r\n\r\n  /**\r\n   * Create a new source with updated metadata\r\n   */\r\n  withMetadata(metadata: Record<string, any>): EventSource {\r\n    return new EventSource({\r\n      ...this._value,\r\n      metadata: { ...this.metadata, ...metadata },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a new source with updated location\r\n   */\r\n  withLocation(location: string): EventSource {\r\n    return new EventSource({\r\n      ...this._value,\r\n      location,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a new source with updated version\r\n   */\r\n  withVersion(version: string): EventSource {\r\n    return new EventSource({\r\n      ...this._value,\r\n      version,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get source summary for logging\r\n   */\r\n  getSummary(): {\r\n    type: string;\r\n    identifier: string;\r\n    name: string;\r\n    category: string;\r\n    reliability: number;\r\n    priority: number;\r\n    isTrusted: boolean;\r\n    isHighVolume: boolean;\r\n  } {\r\n    return {\r\n      type: this._value.type,\r\n      identifier: this._value.identifier,\r\n      name: this.name,\r\n      category: this.getCategory(),\r\n      reliability: this.getReliabilityScore(),\r\n      priority: this.getProcessingPriority(),\r\n      isTrusted: this.isTrusted(),\r\n      isHighVolume: this.isHighVolumeSource(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Compare sources for equality\r\n   */\r\n  public equals(other?: EventSource): boolean {\r\n    if (!other) {\r\n      return false;\r\n    }\r\n\r\n    if (this === other) {\r\n      return true;\r\n    }\r\n\r\n    return this._value.type === other._value.type && \r\n           this._value.identifier === other._value.identifier;\r\n  }\r\n\r\n  /**\r\n   * Get string representation\r\n   */\r\n  public toString(): string {\r\n    return `${this._value.type}:${this._value.identifier}`;\r\n  }\r\n\r\n  /**\r\n   * Convert to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      type: this._value.type,\r\n      identifier: this._value.identifier,\r\n      name: this._value.name,\r\n      version: this._value.version,\r\n      vendor: this._value.vendor,\r\n      location: this._value.location,\r\n      metadata: this._value.metadata,\r\n      summary: this.getSummary(),\r\n      description: this.getDescription(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create EventSource from JSON\r\n   */\r\n  static fromJSON(json: Record<string, any>): EventSource {\r\n    return new EventSource({\r\n      type: json.type as EventSourceType,\r\n      identifier: json.identifier,\r\n      name: json.name,\r\n      version: json.version,\r\n      vendor: json.vendor,\r\n      location: json.location,\r\n      metadata: json.metadata,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Validate source format without creating instance\r\n   */\r\n  static isValid(type: EventSourceType, identifier: string): boolean {\r\n    try {\r\n      new EventSource({ type, identifier });\r\n      return true;\r\n    } catch {\r\n      return false;\r\n    }\r\n  }\r\n}\r\n"], "version": 3}