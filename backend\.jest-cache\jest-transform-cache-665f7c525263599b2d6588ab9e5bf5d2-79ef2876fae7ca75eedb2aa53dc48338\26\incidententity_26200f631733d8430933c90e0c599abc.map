{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\incident-response\\domain\\entities\\incident.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCAUiB;AACjB,iEAAsD;AACtD,yEAA8D;AAC9D,mFAAwE;AACxE,yEAA8D;AAC9D,iEAAsD;AAEtD;;;GAGG;AASI,IAAM,QAAQ,GAAd,MAAM,QAAQ;IAgRnB;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,CAAC,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,QAAQ,KAAK,UAAU,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ,CAAC;IACpE,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE,OAAO,KAAK,CAAC;QAE/B,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QACxC,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,UAAU,GAAG,KAAK,CAAC,CAAC;QAE1E,OAAO,GAAG,GAAG,QAAQ,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QACzD,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,IAAI,qBAAqB;QACvB,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAO,IAAI,CAAC;QAClC,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QACrE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,MAAc;QACxB,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC;YAC1B,IAAI,CAAC,MAAM,GAAG,eAAe,CAAC;YAC9B,IAAI,CAAC,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC;YACjC,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;YAEzB,gCAAgC;YAChC,IAAI,CAAC,IAAI,CAAC,OAAO;gBAAE,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;YACrC,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YACzE,IAAI,CAAC,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,MAAc,EAAE,WAAmB;QAC1C,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAErC,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,IAAI,CAAC,CAAC;QACvD,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,YAAY,GAAG,CAAC,CAAC;QAEhD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;YACpC,IAAI,CAAC,OAAO,CAAC,iBAAiB,GAAG,EAAE,CAAC;QACtC,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC;YAClC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,MAAM;YACN,WAAW;SACZ,CAAC,CAAC;QAEH,uCAAuC;QACvC,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK;YAAE,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;aACjD,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ;YAAE,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC;aACvD,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM;YAAE,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,MAAc,EAAE,UAAmB;QACzC,IAAI,CAAC,MAAM,GAAG,eAAe,CAAC;QAC9B,IAAI,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QAExB,+BAA+B;QAC/B,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QACrC,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QACrE,IAAI,CAAC,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;QAEjE,uBAAuB;QACvB,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QACxC,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,UAAU,CAAC;QAExE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;YAC/B,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG;gBACvB,IAAI,EAAE,YAAY;gBAClB,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,GAAG,UAAU;aACvD,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAc;QAClB,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,MAAc,EAAE,MAAe;QACjD,IAAI,CAAC,MAAM,GAAG,gBAAgB,CAAC;QAC/B,IAAI,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QAExB,IAAI,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAC3B,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAAG,MAAM,CAAC;QAC5C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,MAAc,EAAE,IAAY,EAAE,cAAwB,EAAE;QACpE,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvB,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACzB,CAAC;QAED,2BAA2B;QAC3B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;QAEjF,iBAAiB;QACjB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;YACrB,MAAM;YACN,IAAI;YACJ,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACpC,WAAW;SACZ,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,MAAc;QAC7B,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;QACnF,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,GAAW;QAChB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,GAAW;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,MAAc,EAAE,UAAkB;QAC9C,IAAI,IAAI,CAAC,UAAU,KAAK,MAAM;YAAE,OAAO,IAAI,CAAC;QAE5C,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;QAC/E,OAAO,UAAU,EAAE,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC;IAC/D,CAAC;IAED;;OAEG;IACK,aAAa;QACnB,4CAA4C;QAC5C,MAAM,SAAS,GAAG;YAChB,QAAQ,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;YACxD,IAAI,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;YACrD,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;YACxD,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;SACvD,CAAC;QAEF,OAAO,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,MAAM,cAAc,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;QACnE,MAAM,cAAc,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;QAEjE,MAAM,aAAa,GAAG,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACpD,MAAM,aAAa,GAAG,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEpD,qBAAqB;QACrB,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,IAAI,CAAC,OAAO,EAAE,cAAc,EAAE,eAAe;YAAE,UAAU,IAAI,GAAG,CAAC;QACrE,IAAI,IAAI,CAAC,OAAO,EAAE,cAAc,EAAE,iBAAiB;YAAE,UAAU,IAAI,GAAG,CAAC;QACvE,IAAI,IAAI,CAAC,OAAO,EAAE,eAAe,EAAE,MAAM,GAAG,CAAC;YAAE,UAAU,IAAI,GAAG,CAAC;QAEjE,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,aAAa,GAAG,aAAa,CAAC,GAAG,UAAU,CAAC,CAAC;IACpE,CAAC;IAED;;OAEG;IACH,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,OAAO,EAAE,eAAe,EAAE,MAAM,IAAI,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,IAAI,CAAC,CAAC;IACzC,CAAC;CACF,CAAA;AA5fY,4BAAQ;AAEnB;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;oCACpB;AAMX;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;uCACV;AAMd;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;6CACL;AAmBpB;IAdC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE;YACJ,KAAK;YACL,eAAe;YACf,aAAa;YACb,aAAa;YACb,UAAU;YACV,eAAe;YACf,QAAQ;YACR,gBAAgB;SACjB;QACD,OAAO,EAAE,KAAK;KACf,CAAC;;wCAC2H;AAS7H;IAJC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC;KAC5C,CAAC;;0CAC+C;AAUjD;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;QACzC,OAAO,EAAE,QAAQ;KAClB,CAAC;;0CAC6C;AAqB/C;IAhBC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE;YACJ,SAAS;YACT,UAAU;YACV,aAAa;YACb,qBAAqB;YACrB,mBAAmB;YACnB,gBAAgB;YAChB,4BAA4B;YAC5B,oBAAoB;YACpB,mBAAmB;YACnB,sBAAsB;YACtB,OAAO;SACR;KACF,CAAC;;0CACe;AAUjB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,qBAAqB,EAAE,aAAa,EAAE,iBAAiB,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,QAAQ,CAAC;QACtH,OAAO,EAAE,QAAQ;KAClB,CAAC;;wCACa;AAMf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC;kDACtD,IAAI,oBAAJ,IAAI;4CAAC;AAMjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACrE,IAAI,oBAAJ,IAAI;4CAAC;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACrE,IAAI,oBAAJ,IAAI;gDAAC;AAMtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACrE,IAAI,oBAAJ,IAAI;4CAAC;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACrE,IAAI,oBAAJ,IAAI;0CAAC;AAMhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;;yCAyCxB;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;yCA0BxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;gDAC3C;AAMxB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;+CAC5B;AAMvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4CAC1C;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAClD,KAAK,oBAAL,KAAK;8CAKjB;AAMH;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;sCACtC;AAUf;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,cAAc,EAAE,YAAY,CAAC;QAC1D,OAAO,EAAE,UAAU;KACpB,CAAC;;iDACqE;AAMvE;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;2CAC3B;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;2CAC1C;AAGnB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;2CAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;2CAAC;AAIhB;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,mCAAY,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;uCAClD;AAGtB;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,2CAAgB,EAAE,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;0CACvD;AAG7B;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,qDAAqB,EAAE,aAAa,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;gDAC3D;AAGxC;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,2CAAgB,EAAE,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;0CACvD;AAI7B;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,mCAAY,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACjD,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,CAAC;kDAC1B,mCAAY,oBAAZ,mCAAY;8CAAC;mBA9QjB,QAAQ;IARpB,IAAA,gBAAM,EAAC,WAAW,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,YAAY,CAAC,CAAC;IACrB,IAAA,eAAK,EAAC,CAAC,WAAW,CAAC,CAAC;IACpB,IAAA,eAAK,EAAC,CAAC,YAAY,CAAC,CAAC;GACT,QAAQ,CA4fpB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\incident-response\\domain\\entities\\incident.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  OneToMany,\r\n  ManyToOne,\r\n  JoinColumn,\r\n} from 'typeorm';\r\nimport { IncidentTask } from './incident-task.entity';\r\nimport { IncidentEvidence } from './incident-evidence.entity';\r\nimport { IncidentCommunication } from './incident-communication.entity';\r\nimport { IncidentTimeline } from './incident-timeline.entity';\r\nimport { ResponsePlan } from './response-plan.entity';\r\n\r\n/**\r\n * Incident entity\r\n * Represents security incidents with complete lifecycle management\r\n */\r\n@Entity('incidents')\r\n@Index(['status'])\r\n@Index(['severity'])\r\n@Index(['priority'])\r\n@Index(['category'])\r\n@Index(['assignedTo'])\r\n@Index(['createdAt'])\r\n@Index(['detectedAt'])\r\nexport class Incident {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * Incident title\r\n   */\r\n  @Column({ length: 255 })\r\n  title: string;\r\n\r\n  /**\r\n   * Incident description\r\n   */\r\n  @Column({ type: 'text' })\r\n  description: string;\r\n\r\n  /**\r\n   * Incident status\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: [\r\n      'new',\r\n      'investigating',\r\n      'containment',\r\n      'eradication',\r\n      'recovery',\r\n      'post_incident',\r\n      'closed',\r\n      'false_positive',\r\n    ],\r\n    default: 'new',\r\n  })\r\n  status: 'new' | 'investigating' | 'containment' | 'eradication' | 'recovery' | 'post_incident' | 'closed' | 'false_positive';\r\n\r\n  /**\r\n   * Incident severity\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['low', 'medium', 'high', 'critical'],\r\n  })\r\n  severity: 'low' | 'medium' | 'high' | 'critical';\r\n\r\n  /**\r\n   * Incident priority\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['low', 'medium', 'high', 'urgent'],\r\n    default: 'medium',\r\n  })\r\n  priority: 'low' | 'medium' | 'high' | 'urgent';\r\n\r\n  /**\r\n   * Incident category\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: [\r\n      'malware',\r\n      'phishing',\r\n      'data_breach',\r\n      'unauthorized_access',\r\n      'denial_of_service',\r\n      'insider_threat',\r\n      'vulnerability_exploitation',\r\n      'social_engineering',\r\n      'physical_security',\r\n      'compliance_violation',\r\n      'other',\r\n    ],\r\n  })\r\n  category: string;\r\n\r\n  /**\r\n   * Incident source\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['automated_detection', 'user_report', 'external_report', 'threat_intelligence', 'vulnerability_scan', 'manual'],\r\n    default: 'manual',\r\n  })\r\n  source: string;\r\n\r\n  /**\r\n   * When the incident was first detected\r\n   */\r\n  @Column({ name: 'detected_at', type: 'timestamp with time zone' })\r\n  detectedAt: Date;\r\n\r\n  /**\r\n   * When the incident actually occurred (if different from detection)\r\n   */\r\n  @Column({ name: 'occurred_at', type: 'timestamp with time zone', nullable: true })\r\n  occurredAt?: Date;\r\n\r\n  /**\r\n   * When the incident was acknowledged\r\n   */\r\n  @Column({ name: 'acknowledged_at', type: 'timestamp with time zone', nullable: true })\r\n  acknowledgedAt?: Date;\r\n\r\n  /**\r\n   * When the incident was resolved\r\n   */\r\n  @Column({ name: 'resolved_at', type: 'timestamp with time zone', nullable: true })\r\n  resolvedAt?: Date;\r\n\r\n  /**\r\n   * When the incident was closed\r\n   */\r\n  @Column({ name: 'closed_at', type: 'timestamp with time zone', nullable: true })\r\n  closedAt?: Date;\r\n\r\n  /**\r\n   * Incident details and metadata\r\n   */\r\n  @Column({ type: 'jsonb' })\r\n  details: {\r\n    // Affected systems and assets\r\n    affectedSystems?: Array<{\r\n      assetId?: string;\r\n      hostname?: string;\r\n      ipAddress?: string;\r\n      type: string;\r\n      impact: 'none' | 'low' | 'medium' | 'high';\r\n    }>;\r\n    \r\n    // Attack vectors and techniques\r\n    attackVectors?: string[];\r\n    techniques?: string[]; // MITRE ATT&CK techniques\r\n    \r\n    // Indicators of compromise\r\n    iocs?: Array<{\r\n      type: 'ip' | 'domain' | 'url' | 'hash' | 'email' | 'file';\r\n      value: string;\r\n      confidence: 'low' | 'medium' | 'high';\r\n      source?: string;\r\n    }>;\r\n    \r\n    // Business impact\r\n    businessImpact?: {\r\n      description: string;\r\n      estimatedCost?: number;\r\n      affectedUsers?: number;\r\n      dataCompromised?: boolean;\r\n      serviceDisruption?: boolean;\r\n    };\r\n    \r\n    // Detection details\r\n    detectionMethod?: string;\r\n    detectionSource?: string;\r\n    alertIds?: string[];\r\n    \r\n    // External references\r\n    externalTicketId?: string;\r\n    relatedIncidents?: string[];\r\n    threatIntelligenceIds?: string[];\r\n  };\r\n\r\n  /**\r\n   * Incident metrics and SLA tracking\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  metrics?: {\r\n    // Time to acknowledge (minutes)\r\n    timeToAcknowledge?: number;\r\n    \r\n    // Time to containment (minutes)\r\n    timeToContainment?: number;\r\n    \r\n    // Time to resolution (minutes)\r\n    timeToResolution?: number;\r\n    \r\n    // SLA compliance\r\n    slaCompliant?: boolean;\r\n    slaBreach?: {\r\n      type: 'acknowledgment' | 'containment' | 'resolution';\r\n      breachTime: number; // minutes over SLA\r\n    };\r\n    \r\n    // Escalation tracking\r\n    escalationLevel?: number;\r\n    escalationHistory?: Array<{\r\n      level: number;\r\n      timestamp: string;\r\n      reason: string;\r\n      escalatedBy: string;\r\n    }>;\r\n  };\r\n\r\n  /**\r\n   * Response plan being executed\r\n   */\r\n  @Column({ name: 'response_plan_id', type: 'uuid', nullable: true })\r\n  responsePlanId?: string;\r\n\r\n  /**\r\n   * Current response phase\r\n   */\r\n  @Column({ name: 'response_phase', nullable: true })\r\n  responsePhase?: string;\r\n\r\n  /**\r\n   * Incident commander/lead\r\n   */\r\n  @Column({ name: 'assigned_to', type: 'uuid', nullable: true })\r\n  assignedTo?: string;\r\n\r\n  /**\r\n   * Response team members\r\n   */\r\n  @Column({ name: 'response_team', type: 'jsonb', nullable: true })\r\n  responseTeam?: Array<{\r\n    userId: string;\r\n    role: string;\r\n    assignedAt: string;\r\n    permissions: string[];\r\n  }>;\r\n\r\n  /**\r\n   * Incident tags for categorization\r\n   */\r\n  @Column({ type: 'text', array: true, default: '{}' })\r\n  tags: string[];\r\n\r\n  /**\r\n   * Confidentiality level\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['public', 'internal', 'confidential', 'restricted'],\r\n    default: 'internal',\r\n  })\r\n  confidentiality: 'public' | 'internal' | 'confidential' | 'restricted';\r\n\r\n  /**\r\n   * User who created the incident\r\n   */\r\n  @Column({ name: 'created_by', type: 'uuid' })\r\n  createdBy: string;\r\n\r\n  /**\r\n   * User who last updated the incident\r\n   */\r\n  @Column({ name: 'updated_by', type: 'uuid', nullable: true })\r\n  updatedBy?: string;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  // Relationships\r\n  @OneToMany(() => IncidentTask, task => task.incident, { cascade: true })\r\n  tasks: IncidentTask[];\r\n\r\n  @OneToMany(() => IncidentEvidence, evidence => evidence.incident, { cascade: true })\r\n  evidence: IncidentEvidence[];\r\n\r\n  @OneToMany(() => IncidentCommunication, communication => communication.incident, { cascade: true })\r\n  communications: IncidentCommunication[];\r\n\r\n  @OneToMany(() => IncidentTimeline, timeline => timeline.incident, { cascade: true })\r\n  timeline: IncidentTimeline[];\r\n\r\n  @ManyToOne(() => ResponsePlan, { nullable: true })\r\n  @JoinColumn({ name: 'response_plan_id' })\r\n  responsePlan?: ResponsePlan;\r\n\r\n  /**\r\n   * Check if incident is open\r\n   */\r\n  get isOpen(): boolean {\r\n    return !['closed', 'false_positive'].includes(this.status);\r\n  }\r\n\r\n  /**\r\n   * Check if incident is critical\r\n   */\r\n  get isCritical(): boolean {\r\n    return this.severity === 'critical' || this.priority === 'urgent';\r\n  }\r\n\r\n  /**\r\n   * Check if incident is overdue\r\n   */\r\n  get isOverdue(): boolean {\r\n    if (!this.isOpen) return false;\r\n    \r\n    const now = new Date();\r\n    const slaMinutes = this.getSlaMinutes();\r\n    const deadline = new Date(this.detectedAt.getTime() + slaMinutes * 60000);\r\n    \r\n    return now > deadline;\r\n  }\r\n\r\n  /**\r\n   * Get incident age in hours\r\n   */\r\n  get ageInHours(): number {\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - this.detectedAt.getTime();\r\n    return Math.round(diffMs / (1000 * 60 * 60));\r\n  }\r\n\r\n  /**\r\n   * Get time to resolution in hours\r\n   */\r\n  get timeToResolutionHours(): number | null {\r\n    if (!this.resolvedAt) return null;\r\n    const diffMs = this.resolvedAt.getTime() - this.detectedAt.getTime();\r\n    return Math.round(diffMs / (1000 * 60 * 60));\r\n  }\r\n\r\n  /**\r\n   * Acknowledge incident\r\n   */\r\n  acknowledge(userId: string): void {\r\n    if (this.status === 'new') {\r\n      this.status = 'investigating';\r\n      this.acknowledgedAt = new Date();\r\n      this.assignedTo = userId;\r\n      \r\n      // Calculate time to acknowledge\r\n      if (!this.metrics) this.metrics = {};\r\n      const diffMs = this.acknowledgedAt.getTime() - this.detectedAt.getTime();\r\n      this.metrics.timeToAcknowledge = Math.round(diffMs / (1000 * 60));\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Escalate incident\r\n   */\r\n  escalate(reason: string, escalatedBy: string): void {\r\n    if (!this.metrics) this.metrics = {};\r\n    \r\n    const currentLevel = this.metrics.escalationLevel || 0;\r\n    this.metrics.escalationLevel = currentLevel + 1;\r\n    \r\n    if (!this.metrics.escalationHistory) {\r\n      this.metrics.escalationHistory = [];\r\n    }\r\n    \r\n    this.metrics.escalationHistory.push({\r\n      level: this.metrics.escalationLevel,\r\n      timestamp: new Date().toISOString(),\r\n      reason,\r\n      escalatedBy,\r\n    });\r\n    \r\n    // Auto-increase priority on escalation\r\n    if (this.priority === 'low') this.priority = 'medium';\r\n    else if (this.priority === 'medium') this.priority = 'high';\r\n    else if (this.priority === 'high') this.priority = 'urgent';\r\n  }\r\n\r\n  /**\r\n   * Resolve incident\r\n   */\r\n  resolve(userId: string, resolution?: string): void {\r\n    this.status = 'post_incident';\r\n    this.resolvedAt = new Date();\r\n    this.updatedBy = userId;\r\n    \r\n    // Calculate time to resolution\r\n    if (!this.metrics) this.metrics = {};\r\n    const diffMs = this.resolvedAt.getTime() - this.detectedAt.getTime();\r\n    this.metrics.timeToResolution = Math.round(diffMs / (1000 * 60));\r\n    \r\n    // Check SLA compliance\r\n    const slaMinutes = this.getSlaMinutes();\r\n    this.metrics.slaCompliant = this.metrics.timeToResolution <= slaMinutes;\r\n    \r\n    if (!this.metrics.slaCompliant) {\r\n      this.metrics.slaBreach = {\r\n        type: 'resolution',\r\n        breachTime: this.metrics.timeToResolution - slaMinutes,\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Close incident\r\n   */\r\n  close(userId: string): void {\r\n    this.status = 'closed';\r\n    this.closedAt = new Date();\r\n    this.updatedBy = userId;\r\n  }\r\n\r\n  /**\r\n   * Mark as false positive\r\n   */\r\n  markAsFalsePositive(userId: string, reason?: string): void {\r\n    this.status = 'false_positive';\r\n    this.closedAt = new Date();\r\n    this.updatedBy = userId;\r\n    \r\n    if (reason && this.details) {\r\n      this.details.falsePositiveReason = reason;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Add team member to response team\r\n   */\r\n  addTeamMember(userId: string, role: string, permissions: string[] = []): void {\r\n    if (!this.responseTeam) {\r\n      this.responseTeam = [];\r\n    }\r\n    \r\n    // Remove if already exists\r\n    this.responseTeam = this.responseTeam.filter(member => member.userId !== userId);\r\n    \r\n    // Add new member\r\n    this.responseTeam.push({\r\n      userId,\r\n      role,\r\n      assignedAt: new Date().toISOString(),\r\n      permissions,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Remove team member from response team\r\n   */\r\n  removeTeamMember(userId: string): void {\r\n    if (this.responseTeam) {\r\n      this.responseTeam = this.responseTeam.filter(member => member.userId !== userId);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Add tag to incident\r\n   */\r\n  addTag(tag: string): void {\r\n    if (!this.tags.includes(tag)) {\r\n      this.tags.push(tag);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove tag from incident\r\n   */\r\n  removeTag(tag: string): void {\r\n    this.tags = this.tags.filter(t => t !== tag);\r\n  }\r\n\r\n  /**\r\n   * Check if user has permission\r\n   */\r\n  hasPermission(userId: string, permission: string): boolean {\r\n    if (this.assignedTo === userId) return true;\r\n    \r\n    const teamMember = this.responseTeam?.find(member => member.userId === userId);\r\n    return teamMember?.permissions.includes(permission) || false;\r\n  }\r\n\r\n  /**\r\n   * Get SLA minutes based on severity and priority\r\n   */\r\n  private getSlaMinutes(): number {\r\n    // SLA matrix based on severity and priority\r\n    const slaMatrix = {\r\n      critical: { urgent: 15, high: 30, medium: 60, low: 120 },\r\n      high: { urgent: 30, high: 60, medium: 120, low: 240 },\r\n      medium: { urgent: 60, high: 120, medium: 240, low: 480 },\r\n      low: { urgent: 120, high: 240, medium: 480, low: 960 },\r\n    };\r\n    \r\n    return slaMatrix[this.severity][this.priority];\r\n  }\r\n\r\n  /**\r\n   * Calculate risk score\r\n   */\r\n  calculateRiskScore(): number {\r\n    const severityScores = { low: 1, medium: 2, high: 3, critical: 4 };\r\n    const priorityScores = { low: 1, medium: 2, high: 3, urgent: 4 };\r\n    \r\n    const severityScore = severityScores[this.severity];\r\n    const priorityScore = priorityScores[this.priority];\r\n    \r\n    // Additional factors\r\n    let multiplier = 1;\r\n    if (this.details?.businessImpact?.dataCompromised) multiplier += 0.5;\r\n    if (this.details?.businessImpact?.serviceDisruption) multiplier += 0.3;\r\n    if (this.details?.affectedSystems?.length > 5) multiplier += 0.2;\r\n    \r\n    return Math.min(10, (severityScore + priorityScore) * multiplier);\r\n  }\r\n\r\n  /**\r\n   * Get affected asset count\r\n   */\r\n  get affectedAssetCount(): number {\r\n    return this.details?.affectedSystems?.length || 0;\r\n  }\r\n\r\n  /**\r\n   * Get IOC count\r\n   */\r\n  get iocCount(): number {\r\n    return this.details?.iocs?.length || 0;\r\n  }\r\n}\r\n"], "version": 3}