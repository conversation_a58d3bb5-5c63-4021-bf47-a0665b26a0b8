784f0dd7cc2ffa95b6453ba489a75cfd
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const request = __importStar(require("supertest"));
const common_2 = require("@nestjs/common");
const response_transformation_interceptor_1 = require("../common/interceptors/response-transformation.interceptor");
const global_exception_filter_1 = require("../common/filters/global-exception.filter");
// Test controller that throws various types of errors
let ErrorTestController = class ErrorTestController {
    getSuccess() {
        return { message: 'Success response' };
    }
    getBadRequest() {
        throw new common_1.BadRequestException('Invalid request parameters');
    }
    getBadRequestDetailed() {
        throw new common_1.BadRequestException({
            message: 'Validation failed',
            errors: [
                { field: 'email', message: 'Invalid email format' },
                { field: 'age', message: 'Age must be between 18 and 120' },
            ],
        });
    }
    getUnauthorized() {
        throw new common_1.UnauthorizedException('Authentication required');
    }
    getForbidden() {
        throw new common_1.ForbiddenException('Insufficient permissions');
    }
    getNotFound() {
        throw new common_1.NotFoundException('Resource not found');
    }
    getInternalError() {
        throw new common_1.InternalServerErrorException('Internal server error');
    }
    getCustomError() {
        throw new common_1.HttpException({
            statusCode: 422,
            message: 'Unprocessable Entity',
            error: 'Business logic validation failed',
            details: {
                code: 'BUSINESS_RULE_VIOLATION',
                field: 'amount',
                constraint: 'Amount cannot exceed daily limit',
            },
        }, common_1.HttpStatus.UNPROCESSABLE_ENTITY);
    }
    getJavaScriptError() {
        // Simulate a JavaScript runtime error
        const obj = null;
        return obj.someProperty; // This will throw TypeError
    }
    async getAsyncError() {
        // Simulate an async error
        await new Promise((resolve, reject) => {
            setTimeout(() => reject(new Error('Async operation failed')), 10);
        });
    }
    postValidationError(body) {
        if (!body.name) {
            throw new common_1.BadRequestException([
                'name should not be empty',
                'name must be a string',
            ]);
        }
        return { message: 'Validation passed' };
    }
    async getTimeoutError() {
        // Simulate a timeout scenario
        await new Promise(resolve => setTimeout(resolve, 5000));
        return { message: 'This should timeout' };
    }
    getDatabaseError() {
        // Simulate a database connection error
        const error = new Error('Connection to database failed');
        error.name = 'DatabaseConnectionError';
        error.code = 'ECONNREFUSED';
        throw error;
    }
    getRateLimitError() {
        throw new common_1.HttpException({
            statusCode: 429,
            message: 'Too Many Requests',
            error: 'Rate limit exceeded',
            retryAfter: 60,
        }, common_1.HttpStatus.TOO_MANY_REQUESTS);
    }
    getUser(id) {
        if (id === 'invalid') {
            throw new common_1.BadRequestException('Invalid user ID format');
        }
        if (id === 'notfound') {
            throw new common_1.NotFoundException(`User with ID ${id} not found`);
        }
        return { id, name: 'John Doe' };
    }
};
__decorate([
    (0, common_2.Get)('success'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ErrorTestController.prototype, "getSuccess", null);
__decorate([
    (0, common_2.Get)('bad-request'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ErrorTestController.prototype, "getBadRequest", null);
__decorate([
    (0, common_2.Get)('bad-request-detailed'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ErrorTestController.prototype, "getBadRequestDetailed", null);
__decorate([
    (0, common_2.Get)('unauthorized'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ErrorTestController.prototype, "getUnauthorized", null);
__decorate([
    (0, common_2.Get)('forbidden'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ErrorTestController.prototype, "getForbidden", null);
__decorate([
    (0, common_2.Get)('not-found'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ErrorTestController.prototype, "getNotFound", null);
__decorate([
    (0, common_2.Get)('internal-error'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ErrorTestController.prototype, "getInternalError", null);
__decorate([
    (0, common_2.Get)('custom-error'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ErrorTestController.prototype, "getCustomError", null);
__decorate([
    (0, common_2.Get)('javascript-error'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ErrorTestController.prototype, "getJavaScriptError", null);
__decorate([
    (0, common_2.Get)('async-error'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ErrorTestController.prototype, "getAsyncError", null);
__decorate([
    (0, common_2.Post)('validation-error'),
    __param(0, (0, common_2.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], ErrorTestController.prototype, "postValidationError", null);
__decorate([
    (0, common_2.Get)('timeout-error'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ErrorTestController.prototype, "getTimeoutError", null);
__decorate([
    (0, common_2.Get)('database-error'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ErrorTestController.prototype, "getDatabaseError", null);
__decorate([
    (0, common_2.Get)('rate-limit-error'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ErrorTestController.prototype, "getRateLimitError", null);
__decorate([
    (0, common_2.Get)('user/:id'),
    __param(0, (0, common_2.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ErrorTestController.prototype, "getUser", null);
ErrorTestController = __decorate([
    (0, common_2.Controller)('error-test'),
    (0, common_2.UseInterceptors)(response_transformation_interceptor_1.ResponseTransformInterceptor),
    (0, common_2.UseFilters)(global_exception_filter_1.GlobalExceptionFilter)
], ErrorTestController);
// Mock Global Exception Filter
class MockGlobalExceptionFilter {
    catch(exception, host) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const request = ctx.getRequest();
        let status = common_1.HttpStatus.INTERNAL_SERVER_ERROR;
        let errorResponse = {
            statusCode: status,
            timestamp: new Date().toISOString(),
            path: request.url,
            method: request.method,
            message: 'Internal server error',
            error: 'Internal Server Error',
        };
        if (exception instanceof common_1.HttpException) {
            status = exception.getStatus();
            const exceptionResponse = exception.getResponse();
            if (typeof exceptionResponse === 'string') {
                errorResponse = {
                    statusCode: status,
                    timestamp: new Date().toISOString(),
                    path: request.url,
                    method: request.method,
                    message: exceptionResponse,
                    error: exception.name,
                };
            }
            else {
                errorResponse = {
                    statusCode: status,
                    timestamp: new Date().toISOString(),
                    path: request.url,
                    method: request.method,
                    ...exceptionResponse,
                };
            }
        }
        else if (exception instanceof Error) {
            // Handle JavaScript errors
            errorResponse = {
                statusCode: common_1.HttpStatus.INTERNAL_SERVER_ERROR,
                timestamp: new Date().toISOString(),
                path: request.url,
                method: request.method,
                message: exception.message,
                error: exception.name,
            };
            // Handle specific error types
            if (exception.name === 'DatabaseConnectionError') {
                errorResponse.statusCode = common_1.HttpStatus.SERVICE_UNAVAILABLE;
                errorResponse.error = 'Service Unavailable';
                errorResponse.message = 'Database service is temporarily unavailable';
            }
        }
        // Add correlation ID if present
        if (request.headers['x-correlation-id']) {
            errorResponse.correlationId = request.headers['x-correlation-id'];
        }
        // Add request ID for tracking
        errorResponse.requestId = request.headers['x-request-id'] || 'unknown';
        response.status(status).json(errorResponse);
    }
}
// Mock Response Transform Interceptor
class MockResponseTransformInterceptor {
    intercept(context, next) {
        return next.handle().pipe(
        // In a real implementation, this would transform the response
        );
    }
}
describe('Error Handling Integration Tests', () => {
    let app;
    beforeAll(async () => {
        const moduleFixture = await testing_1.Test.createTestingModule({
            imports: [
                config_1.ConfigModule.forRoot({
                    isGlobal: true,
                }),
            ],
            controllers: [ErrorTestController],
            providers: [
                {
                    provide: global_exception_filter_1.GlobalExceptionFilter,
                    useClass: MockGlobalExceptionFilter,
                },
                {
                    provide: response_transformation_interceptor_1.ResponseTransformInterceptor,
                    useClass: MockResponseTransformInterceptor,
                },
            ],
        }).compile();
        app = moduleFixture.createNestApplication();
        // Apply global exception filter
        app.useGlobalFilters(new MockGlobalExceptionFilter());
        await app.init();
    });
    afterAll(async () => {
        await app.close();
    });
    describe('Success Responses', () => {
        it('should return successful response', async () => {
            const response = await request(app.getHttpServer())
                .get('/error-test/success')
                .expect(200);
            expect(response.body).toEqual({
                message: 'Success response',
            });
        });
    });
    describe('HTTP Exception Handling', () => {
        it('should handle BadRequestException', async () => {
            const response = await request(app.getHttpServer())
                .get('/error-test/bad-request')
                .expect(400);
            expect(response.body).toEqual({
                statusCode: 400,
                timestamp: expect.any(String),
                path: '/error-test/bad-request',
                method: 'GET',
                message: 'Invalid request parameters',
                error: 'BadRequestException',
                requestId: 'unknown',
            });
        });
        it('should handle detailed BadRequestException', async () => {
            const response = await request(app.getHttpServer())
                .get('/error-test/bad-request-detailed')
                .expect(400);
            expect(response.body).toEqual({
                statusCode: 400,
                timestamp: expect.any(String),
                path: '/error-test/bad-request-detailed',
                method: 'GET',
                message: 'Validation failed',
                errors: [
                    { field: 'email', message: 'Invalid email format' },
                    { field: 'age', message: 'Age must be between 18 and 120' },
                ],
                requestId: 'unknown',
            });
        });
        it('should handle UnauthorizedException', async () => {
            const response = await request(app.getHttpServer())
                .get('/error-test/unauthorized')
                .expect(401);
            expect(response.body).toEqual({
                statusCode: 401,
                timestamp: expect.any(String),
                path: '/error-test/unauthorized',
                method: 'GET',
                message: 'Authentication required',
                error: 'UnauthorizedException',
                requestId: 'unknown',
            });
        });
        it('should handle ForbiddenException', async () => {
            const response = await request(app.getHttpServer())
                .get('/error-test/forbidden')
                .expect(403);
            expect(response.body).toEqual({
                statusCode: 403,
                timestamp: expect.any(String),
                path: '/error-test/forbidden',
                method: 'GET',
                message: 'Insufficient permissions',
                error: 'ForbiddenException',
                requestId: 'unknown',
            });
        });
        it('should handle NotFoundException', async () => {
            const response = await request(app.getHttpServer())
                .get('/error-test/not-found')
                .expect(404);
            expect(response.body).toEqual({
                statusCode: 404,
                timestamp: expect.any(String),
                path: '/error-test/not-found',
                method: 'GET',
                message: 'Resource not found',
                error: 'NotFoundException',
                requestId: 'unknown',
            });
        });
        it('should handle InternalServerErrorException', async () => {
            const response = await request(app.getHttpServer())
                .get('/error-test/internal-error')
                .expect(500);
            expect(response.body).toEqual({
                statusCode: 500,
                timestamp: expect.any(String),
                path: '/error-test/internal-error',
                method: 'GET',
                message: 'Internal server error',
                error: 'InternalServerErrorException',
                requestId: 'unknown',
            });
        });
        it('should handle custom HTTP exceptions', async () => {
            const response = await request(app.getHttpServer())
                .get('/error-test/custom-error')
                .expect(422);
            expect(response.body).toEqual({
                statusCode: 422,
                timestamp: expect.any(String),
                path: '/error-test/custom-error',
                method: 'GET',
                message: 'Unprocessable Entity',
                error: 'Business logic validation failed',
                details: {
                    code: 'BUSINESS_RULE_VIOLATION',
                    field: 'amount',
                    constraint: 'Amount cannot exceed daily limit',
                },
                requestId: 'unknown',
            });
        });
    });
    describe('JavaScript Error Handling', () => {
        it('should handle JavaScript runtime errors', async () => {
            const response = await request(app.getHttpServer())
                .get('/error-test/javascript-error')
                .expect(500);
            expect(response.body).toEqual({
                statusCode: 500,
                timestamp: expect.any(String),
                path: '/error-test/javascript-error',
                method: 'GET',
                message: expect.stringContaining('Cannot read'),
                error: 'TypeError',
                requestId: 'unknown',
            });
        });
        it('should handle async errors', async () => {
            const response = await request(app.getHttpServer())
                .get('/error-test/async-error')
                .expect(500);
            expect(response.body).toEqual({
                statusCode: 500,
                timestamp: expect.any(String),
                path: '/error-test/async-error',
                method: 'GET',
                message: 'Async operation failed',
                error: 'Error',
                requestId: 'unknown',
            });
        });
    });
    describe('Validation Error Handling', () => {
        it('should handle validation errors with array messages', async () => {
            const response = await request(app.getHttpServer())
                .post('/error-test/validation-error')
                .send({})
                .expect(400);
            expect(response.body).toEqual({
                statusCode: 400,
                timestamp: expect.any(String),
                path: '/error-test/validation-error',
                method: 'POST',
                message: [
                    'name should not be empty',
                    'name must be a string',
                ],
                error: 'BadRequestException',
                requestId: 'unknown',
            });
        });
        it('should pass validation with valid data', async () => {
            const response = await request(app.getHttpServer())
                .post('/error-test/validation-error')
                .send({ name: 'John Doe' })
                .expect(201);
            expect(response.body).toEqual({
                message: 'Validation passed',
            });
        });
    });
    describe('Specific Error Types', () => {
        it('should handle database connection errors', async () => {
            const response = await request(app.getHttpServer())
                .get('/error-test/database-error')
                .expect(503);
            expect(response.body).toEqual({
                statusCode: 503,
                timestamp: expect.any(String),
                path: '/error-test/database-error',
                method: 'GET',
                message: 'Database service is temporarily unavailable',
                error: 'Service Unavailable',
                requestId: 'unknown',
            });
        });
        it('should handle rate limit errors', async () => {
            const response = await request(app.getHttpServer())
                .get('/error-test/rate-limit-error')
                .expect(429);
            expect(response.body).toEqual({
                statusCode: 429,
                timestamp: expect.any(String),
                path: '/error-test/rate-limit-error',
                method: 'GET',
                message: 'Too Many Requests',
                error: 'Rate limit exceeded',
                retryAfter: 60,
                requestId: 'unknown',
            });
        });
    });
    describe('Parameterized Route Error Handling', () => {
        it('should handle valid parameter', async () => {
            const response = await request(app.getHttpServer())
                .get('/error-test/user/123')
                .expect(200);
            expect(response.body).toEqual({
                id: '123',
                name: 'John Doe',
            });
        });
        it('should handle invalid parameter', async () => {
            const response = await request(app.getHttpServer())
                .get('/error-test/user/invalid')
                .expect(400);
            expect(response.body.message).toBe('Invalid user ID format');
        });
        it('should handle not found parameter', async () => {
            const response = await request(app.getHttpServer())
                .get('/error-test/user/notfound')
                .expect(404);
            expect(response.body.message).toBe('User with ID notfound not found');
        });
    });
    describe('Error Response Format', () => {
        it('should include timestamp in all error responses', async () => {
            const response = await request(app.getHttpServer())
                .get('/error-test/bad-request')
                .expect(400);
            expect(response.body.timestamp).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/);
        });
        it('should include request path and method', async () => {
            const response = await request(app.getHttpServer())
                .post('/error-test/validation-error')
                .send({})
                .expect(400);
            expect(response.body.path).toBe('/error-test/validation-error');
            expect(response.body.method).toBe('POST');
        });
        it('should include correlation ID when provided', async () => {
            const correlationId = 'test-correlation-123';
            const response = await request(app.getHttpServer())
                .get('/error-test/bad-request')
                .set('X-Correlation-ID', correlationId)
                .expect(400);
            expect(response.body.correlationId).toBe(correlationId);
        });
        it('should include request ID for tracking', async () => {
            const requestId = 'test-request-456';
            const response = await request(app.getHttpServer())
                .get('/error-test/bad-request')
                .set('X-Request-ID', requestId)
                .expect(400);
            expect(response.body.requestId).toBe(requestId);
        });
    });
    describe('Error Consistency', () => {
        it('should maintain consistent error format across different error types', async () => {
            const endpoints = [
                '/error-test/bad-request',
                '/error-test/unauthorized',
                '/error-test/forbidden',
                '/error-test/not-found',
                '/error-test/internal-error',
            ];
            for (const endpoint of endpoints) {
                const response = await request(app.getHttpServer()).get(endpoint);
                expect(response.body).toHaveProperty('statusCode');
                expect(response.body).toHaveProperty('timestamp');
                expect(response.body).toHaveProperty('path');
                expect(response.body).toHaveProperty('method');
                expect(response.body).toHaveProperty('message');
                expect(response.body).toHaveProperty('error');
                expect(response.body).toHaveProperty('requestId');
            }
        });
        it('should not expose sensitive information in error responses', async () => {
            const response = await request(app.getHttpServer())
                .get('/error-test/javascript-error')
                .expect(500);
            // Should not contain stack traces or internal paths
            expect(JSON.stringify(response.body)).not.toContain('node_modules');
            expect(JSON.stringify(response.body)).not.toContain('at Object.');
        });
    });
    describe('HTTP Status Code Accuracy', () => {
        const statusTests = [
            { endpoint: '/error-test/bad-request', expectedStatus: 400 },
            { endpoint: '/error-test/unauthorized', expectedStatus: 401 },
            { endpoint: '/error-test/forbidden', expectedStatus: 403 },
            { endpoint: '/error-test/not-found', expectedStatus: 404 },
            { endpoint: '/error-test/custom-error', expectedStatus: 422 },
            { endpoint: '/error-test/rate-limit-error', expectedStatus: 429 },
            { endpoint: '/error-test/internal-error', expectedStatus: 500 },
            { endpoint: '/error-test/database-error', expectedStatus: 503 },
        ];
        statusTests.forEach(({ endpoint, expectedStatus }) => {
            it(`should return correct status code ${expectedStatus} for ${endpoint}`, async () => {
                const response = await request(app.getHttpServer()).get(endpoint);
                expect(response.status).toBe(expectedStatus);
                expect(response.body.statusCode).toBe(expectedStatus);
            });
        });
    });
    describe('Content-Type Headers', () => {
        it('should return JSON content type for error responses', async () => {
            const response = await request(app.getHttpServer())
                .get('/error-test/bad-request')
                .expect(400);
            expect(response.headers['content-type']).toMatch(/application\/json/);
        });
    });
    describe('Error Logging Integration', () => {
        it('should handle errors without breaking the application', async () => {
            // Make multiple error requests to ensure stability
            const errorEndpoints = [
                '/error-test/bad-request',
                '/error-test/javascript-error',
                '/error-test/async-error',
                '/error-test/database-error',
            ];
            for (const endpoint of errorEndpoints) {
                await request(app.getHttpServer()).get(endpoint);
            }
            // Application should still respond to valid requests
            await request(app.getHttpServer())
                .get('/error-test/success')
                .expect(200);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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