9d01be8585bd26f683ea15f0475408fd
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NormalizedEventValidationFailedDomainEvent = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
/**
 * Normalized Event Validation Failed Domain Event
 *
 * Raised when a normalized event fails validation during the normalization process.
 * This event triggers various downstream processes including:
 * - Error handling and retry logic
 * - Alert generation for failed normalizations
 * - Dead letter queue management
 * - Performance monitoring and analysis
 * - Manual intervention workflows
 */
class NormalizedEventValidationFailedDomainEvent extends shared_kernel_1.BaseDomainEvent {
    constructor(aggregateId, eventData, options) {
        super(aggregateId, eventData, options);
    }
    /**
     * Get the original event ID
     */
    get originalEventId() {
        return this.eventData.originalEventId;
    }
    /**
     * Get the error message
     */
    get error() {
        return this.eventData.error;
    }
    /**
     * Get the current attempt number
     */
    get attempt() {
        return this.eventData.attempt;
    }
    /**
     * Check if maximum attempts have been exceeded
     */
    get maxAttemptsExceeded() {
        return this.eventData.maxAttemptsExceeded;
    }
    /**
     * Get validation errors
     */
    get validationErrors() {
        return this.eventData.validationErrors || [];
    }
    /**
     * Get failed rules
     */
    get failedRules() {
        return this.eventData.failedRules || [];
    }
    /**
     * Get processing duration before failure
     */
    get processingDurationMs() {
        return this.eventData.processingDurationMs || 0;
    }
    /**
     * Get the timestamp when failure occurred
     */
    get timestamp() {
        return this.eventData.timestamp || this.occurredOn;
    }
    /**
     * Check if this is the first attempt
     */
    isFirstAttempt() {
        return this.attempt === 1;
    }
    /**
     * Check if retry is possible
     */
    canRetry() {
        return !this.maxAttemptsExceeded;
    }
    /**
     * Check if manual intervention is required
     */
    requiresManualIntervention() {
        return this.maxAttemptsExceeded || this.validationErrors.length > 5;
    }
    /**
     * Check if this is a critical failure
     */
    isCriticalFailure() {
        return this.maxAttemptsExceeded ||
            this.error.toLowerCase().includes('critical') ||
            this.error.toLowerCase().includes('fatal');
    }
    /**
     * Get the number of validation errors
     */
    getValidationErrorsCount() {
        return this.validationErrors.length;
    }
    /**
     * Get the number of failed rules
     */
    getFailedRulesCount() {
        return this.failedRules.length;
    }
    /**
     * Check if failure is due to schema validation
     */
    isSchemaValidationFailure() {
        return this.error.toLowerCase().includes('schema') ||
            this.validationErrors.some(error => error.toLowerCase().includes('schema'));
    }
    /**
     * Check if failure is due to data quality issues
     */
    isDataQualityFailure() {
        return this.error.toLowerCase().includes('quality') ||
            this.error.toLowerCase().includes('invalid') ||
            this.validationErrors.some(error => error.toLowerCase().includes('quality') ||
                error.toLowerCase().includes('invalid'));
    }
    /**
     * Check if failure is due to missing required fields
     */
    isMissingFieldsFailure() {
        return this.error.toLowerCase().includes('required') ||
            this.error.toLowerCase().includes('missing') ||
            this.validationErrors.some(error => error.toLowerCase().includes('required') ||
                error.toLowerCase().includes('missing'));
    }
    /**
     * Get failure category
     */
    getFailureCategory() {
        if (this.isSchemaValidationFailure()) {
            return 'schema';
        }
        if (this.isDataQualityFailure()) {
            return 'data_quality';
        }
        if (this.isMissingFieldsFailure()) {
            return 'missing_fields';
        }
        if (this.getFailedRulesCount() > 0) {
            return 'rule_failure';
        }
        return 'unknown';
    }
    /**
     * Get retry delay suggestion in milliseconds
     */
    getSuggestedRetryDelayMs() {
        // Exponential backoff based on attempt number
        const baseDelay = 1000; // 1 second
        const maxDelay = 300000; // 5 minutes
        const delay = baseDelay * Math.pow(2, this.attempt - 1);
        return Math.min(delay, maxDelay);
    }
    /**
     * Get event summary for handlers
     */
    getEventSummary() {
        return {
            normalizedEventId: this.aggregateId.toString(),
            originalEventId: this.originalEventId.toString(),
            error: this.error,
            attempt: this.attempt,
            maxAttemptsExceeded: this.maxAttemptsExceeded,
            validationErrorsCount: this.getValidationErrorsCount(),
            failedRulesCount: this.getFailedRulesCount(),
            processingDurationMs: this.processingDurationMs,
            isFirstAttempt: this.isFirstAttempt(),
            canRetry: this.canRetry(),
            requiresManualIntervention: this.requiresManualIntervention(),
            isCriticalFailure: this.isCriticalFailure(),
            failureCategory: this.getFailureCategory(),
            suggestedRetryDelayMs: this.getSuggestedRetryDelayMs(),
        };
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            ...super.toJSON(),
            eventSummary: this.getEventSummary(),
        };
    }
}
exports.NormalizedEventValidationFailedDomainEvent = NormalizedEventValidationFailedDomainEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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