040bc474a4af09946e3ef8313294f292
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JwtConfigHelper = exports.jwtConfig = exports.JwtConfigValidation = void 0;
const config_1 = require("@nestjs/config");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
// Validation class for JWT configuration
class JwtConfigValidation {
}
exports.JwtConfigValidation = JwtConfigValidation;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_transformer_1.Transform)(({ value }) => value || 'your-super-secret-jwt-key-change-this-in-production'),
    __metadata("design:type", String)
], JwtConfigValidation.prototype, "secret", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], JwtConfigValidation.prototype, "publicKey", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], JwtConfigValidation.prototype, "privateKey", void 0);
__decorate([
    (0, class_validator_1.IsIn)(['HS256', 'HS384', 'HS512', 'RS256', 'RS384', 'RS512']),
    (0, class_transformer_1.Transform)(({ value }) => value || 'HS256'),
    __metadata("design:type", String)
], JwtConfigValidation.prototype, "algorithm", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_transformer_1.Transform)(({ value }) => value || '15m'),
    __metadata("design:type", String)
], JwtConfigValidation.prototype, "expiresIn", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_transformer_1.Transform)(({ value }) => value || 'your-super-secret-refresh-key-change-this-in-production'),
    __metadata("design:type", String)
], JwtConfigValidation.prototype, "refreshSecret", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_transformer_1.Transform)(({ value }) => value || '7d'),
    __metadata("design:type", String)
], JwtConfigValidation.prototype, "refreshExpiresIn", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_transformer_1.Transform)(({ value }) => value || 'sentinel-platform'),
    __metadata("design:type", String)
], JwtConfigValidation.prototype, "issuer", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_transformer_1.Transform)(({ value }) => value || 'sentinel-users'),
    __metadata("design:type", String)
], JwtConfigValidation.prototype, "audience", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(300),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_transformer_1.Transform)(({ value }) => Number(value) || 0),
    __metadata("design:type", Number)
], JwtConfigValidation.prototype, "clockTolerance", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], JwtConfigValidation.prototype, "subject", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], JwtConfigValidation.prototype, "jwtid", void 0);
// Utility functions
const parseBoolean = (value, fallback = false) => value?.toLowerCase() === 'true' || fallback;
const env = {
    getString: (key, fallback = '') => process.env[key] ?? fallback,
    getNumber: (key, fallback = 0) => {
        const value = process.env[key];
        if (!value)
            return fallback;
        const parsed = Number(value);
        return Number.isNaN(parsed) ? fallback : parsed;
    },
    getBoolean: (key, fallback = false) => parseBoolean(process.env[key], fallback),
    isProduction: () => process.env['NODE_ENV'] === 'production',
};
// Configuration defaults
const DEFAULTS = {
    JWT_SECRET: 'your-super-secret-jwt-key-change-this-in-production',
    JWT_REFRESH_SECRET: 'your-super-secret-refresh-key-change-this-in-production',
    JWT_ALGORITHM: 'HS256',
    JWT_EXPIRES_IN: '15m',
    JWT_REFRESH_EXPIRES_IN: '7d',
    JWT_ISSUER: 'sentinel-platform',
    JWT_AUDIENCE: 'sentinel-users',
    JWT_CLOCK_TOLERANCE: 0,
    JWT_MAX_AGE: '1h',
};
/**
 * JWT configuration factory
 * Creates JWT configuration with environment variable support and validation
 */
exports.jwtConfig = (0, config_1.registerAs)('jwt', () => {
    const algorithm = env.getString('JWT_ALGORITHM', DEFAULTS.JWT_ALGORITHM);
    const isRSA = algorithm.startsWith('RS');
    // For production, warn if using default secrets
    if (env.isProduction()) {
        const secret = env.getString('JWT_SECRET');
        const refreshSecret = env.getString('JWT_REFRESH_SECRET');
        if (secret === DEFAULTS.JWT_SECRET || refreshSecret === DEFAULTS.JWT_REFRESH_SECRET) {
            console.warn('⚠️  WARNING: Using default JWT secrets in production! Please set JWT_SECRET and JWT_REFRESH_SECRET environment variables.');
        }
    }
    // Build config object with conditional properties
    const baseConfig = {
        secret: env.getString('JWT_SECRET', DEFAULTS.JWT_SECRET),
        algorithm,
        expiresIn: env.getString('JWT_EXPIRES_IN', DEFAULTS.JWT_EXPIRES_IN),
        refreshSecret: env.getString('JWT_REFRESH_SECRET', DEFAULTS.JWT_REFRESH_SECRET),
        refreshExpiresIn: env.getString('JWT_REFRESH_EXPIRES_IN', DEFAULTS.JWT_REFRESH_EXPIRES_IN),
        issuer: env.getString('JWT_ISSUER', DEFAULTS.JWT_ISSUER),
        audience: env.getString('JWT_AUDIENCE', DEFAULTS.JWT_AUDIENCE),
        clockTolerance: env.getNumber('JWT_CLOCK_TOLERANCE', DEFAULTS.JWT_CLOCK_TOLERANCE),
        ignoreExpiration: env.getBoolean('JWT_IGNORE_EXPIRATION', false),
        ignoreNotBefore: env.getBoolean('JWT_IGNORE_NOT_BEFORE', false),
        maxAge: env.getString('JWT_MAX_AGE', DEFAULTS.JWT_MAX_AGE),
    };
    // Build complete config object
    const publicKey = isRSA ? env.getString('JWT_PUBLIC_KEY') : undefined;
    const privateKey = isRSA ? env.getString('JWT_PRIVATE_KEY') : undefined;
    const subject = env.getString('JWT_SUBJECT');
    const jwtid = env.getString('JWT_ID');
    // Warn about missing RSA keys in production
    if (isRSA && env.isProduction() && (!publicKey || !privateKey)) {
        console.warn('⚠️  WARNING: RSA algorithm selected but RSA keys not provided! Please set JWT_PUBLIC_KEY and JWT_PRIVATE_KEY environment variables.');
    }
    const config = {
        ...baseConfig,
        ...(publicKey && { publicKey }),
        ...(privateKey && { privateKey }),
        ...(subject && { subject }),
        ...(jwtid && { jwtid }),
    };
    return config;
});
/**
 * JWT configuration helper functions
 */
exports.JwtConfigHelper = {
    /**
     * Get JWT sign options for the JWT service
     */
    getSignOptions: (config) => ({
        algorithm: config.algorithm,
        expiresIn: config.expiresIn,
        issuer: config.issuer,
        audience: config.audience,
        subject: config.subject,
        jwtid: config.jwtid,
    }),
    /**
     * Get JWT verify options for the JWT service
     */
    getVerifyOptions: (config) => ({
        algorithms: [config.algorithm],
        issuer: config.issuer,
        audience: config.audience,
        clockTolerance: config.clockTolerance,
        ignoreExpiration: config.ignoreExpiration,
        ignoreNotBefore: config.ignoreNotBefore,
        maxAge: config.maxAge,
    }),
    /**
     * Get secret or key for signing/verifying
     */
    getSecretOrKey: (config, isPrivate = false) => {
        if (config.algorithm.startsWith('RS')) {
            return isPrivate ? (config.privateKey || config.secret) : (config.publicKey || config.secret);
        }
        return config.secret;
    },
    /**
     * Validate JWT configuration
     */
    validate: (config) => {
        const errors = [];
        if (!config.secret) {
            errors.push('JWT secret is required');
        }
        if (config.algorithm.startsWith('RS')) {
            if (!config.publicKey) {
                errors.push('Public key is required for RSA algorithms');
            }
            if (!config.privateKey) {
                errors.push('Private key is required for RSA algorithms');
            }
        }
        if (!config.issuer) {
            errors.push('JWT issuer is required');
        }
        if (!config.audience) {
            errors.push('JWT audience is required');
        }
        if (config.clockTolerance < 0 || config.clockTolerance > 300) {
            errors.push('Clock tolerance must be between 0 and 300 seconds');
        }
        return errors;
    },
    /**
     * Check if configuration is secure for production
     */
    isSecure: (config) => {
        const isDefaultSecret = config.secret === DEFAULTS.JWT_SECRET;
        const isDefaultRefreshSecret = config.refreshSecret === DEFAULTS.JWT_REFRESH_SECRET;
        const isShortExpiration = config.expiresIn === '15m' || config.expiresIn === '1h';
        const hasStrongAlgorithm = ['HS256', 'RS256', 'RS384', 'RS512'].includes(config.algorithm);
        return !isDefaultSecret && !isDefaultRefreshSecret && isShortExpiration && hasStrongAlgorithm;
    },
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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