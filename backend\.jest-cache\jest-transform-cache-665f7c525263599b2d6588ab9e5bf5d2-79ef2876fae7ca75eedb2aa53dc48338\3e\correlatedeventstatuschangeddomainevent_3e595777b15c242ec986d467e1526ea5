ff9c14c54bae19657c814ed816cfb9d9
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CorrelatedEventStatusChangedDomainEvent = void 0;
const shared_kernel_1 = require("../../../../shared-kernel");
const correlation_status_enum_1 = require("../enums/correlation-status.enum");
/**
 * Correlated Event Status Changed Domain Event
 *
 * Raised when a correlated event's correlation status changes.
 * This event triggers various downstream processes including:
 * - Workflow state transitions
 * - Notification systems
 * - Metrics and monitoring updates
 * - Audit trail maintenance
 * - Queue management for different processing stages
 * - Alert escalation or de-escalation
 */
class CorrelatedEventStatusChangedDomainEvent extends shared_kernel_1.BaseDomainEvent {
    constructor(aggregateId, eventData, options) {
        super(aggregateId, eventData, options);
    }
    /**
     * Get the previous correlation status
     */
    get oldStatus() {
        return this.eventData.oldStatus;
    }
    /**
     * Get the new correlation status
     */
    get newStatus() {
        return this.eventData.newStatus;
    }
    /**
     * Get the correlation result
     */
    get result() {
        return this.eventData.result;
    }
    /**
     * Get the correlation quality score
     */
    get correlationQualityScore() {
        return this.eventData.correlationQualityScore;
    }
    /**
     * Check if the event requires manual review
     */
    get requiresManualReview() {
        return this.eventData.requiresManualReview;
    }
    /**
     * Get the status change timestamp
     */
    get changedAt() {
        return this.eventData.changedAt || this.occurredOn;
    }
    /**
     * Get the reason for status change
     */
    get reason() {
        return this.eventData.reason;
    }
    /**
     * Get additional change metadata
     */
    get changeMetadata() {
        return this.eventData.changeMetadata || {};
    }
    /**
     * Check if status changed to completed
     */
    isCompletedTransition() {
        return this.newStatus === correlation_status_enum_1.CorrelationStatus.COMPLETED;
    }
    /**
     * Check if status changed to failed
     */
    isFailedTransition() {
        return this.newStatus === correlation_status_enum_1.CorrelationStatus.FAILED;
    }
    /**
     * Check if status changed to in progress
     */
    isInProgressTransition() {
        return this.newStatus === correlation_status_enum_1.CorrelationStatus.IN_PROGRESS;
    }
    /**
     * Check if status changed to partial
     */
    isPartialTransition() {
        return this.newStatus === correlation_status_enum_1.CorrelationStatus.PARTIAL;
    }
    /**
     * Check if status changed to skipped
     */
    isSkippedTransition() {
        return this.newStatus === correlation_status_enum_1.CorrelationStatus.SKIPPED;
    }
    /**
     * Check if status changed to timeout
     */
    isTimeoutTransition() {
        return this.newStatus === correlation_status_enum_1.CorrelationStatus.TIMEOUT;
    }
    /**
     * Check if this is a successful completion
     */
    isSuccessfulCompletion() {
        return this.isCompletedTransition() && (this.result?.success === true);
    }
    /**
     * Check if this is a failure transition
     */
    isFailureTransition() {
        return this.isFailedTransition() || this.isTimeoutTransition() ||
            (this.isCompletedTransition() && this.result?.success === false);
    }
    /**
     * Check if correlation quality improved
     */
    hasQualityImprovement() {
        // This would need previous quality score to compare
        // For now, we'll consider any quality score above 70 as good
        return (this.correlationQualityScore || 0) >= 70;
    }
    /**
     * Check if status represents progress
     */
    isProgressTransition() {
        const progressOrder = {
            [correlation_status_enum_1.CorrelationStatus.PENDING]: 1,
            [correlation_status_enum_1.CorrelationStatus.IN_PROGRESS]: 2,
            [correlation_status_enum_1.CorrelationStatus.PARTIAL]: 3,
            [correlation_status_enum_1.CorrelationStatus.COMPLETED]: 4,
            [correlation_status_enum_1.CorrelationStatus.FAILED]: 0,
            [correlation_status_enum_1.CorrelationStatus.SKIPPED]: 0,
            [correlation_status_enum_1.CorrelationStatus.TIMEOUT]: 0,
        };
        return progressOrder[this.newStatus] > progressOrder[this.oldStatus];
    }
    /**
     * Check if status represents regression
     */
    isRegressionTransition() {
        const progressOrder = {
            [correlation_status_enum_1.CorrelationStatus.PENDING]: 1,
            [correlation_status_enum_1.CorrelationStatus.IN_PROGRESS]: 2,
            [correlation_status_enum_1.CorrelationStatus.PARTIAL]: 3,
            [correlation_status_enum_1.CorrelationStatus.COMPLETED]: 4,
            [correlation_status_enum_1.CorrelationStatus.FAILED]: 0,
            [correlation_status_enum_1.CorrelationStatus.SKIPPED]: 0,
            [correlation_status_enum_1.CorrelationStatus.TIMEOUT]: 0,
        };
        return progressOrder[this.newStatus] < progressOrder[this.oldStatus] &&
            progressOrder[this.oldStatus] > 0;
    }
    /**
     * Get transition type
     */
    getTransitionType() {
        if (this.isSuccessfulCompletion()) {
            return 'completion';
        }
        if (this.isFailureTransition()) {
            return 'failure';
        }
        if (this.isProgressTransition()) {
            return 'progress';
        }
        if (this.isRegressionTransition()) {
            return 'regression';
        }
        return 'neutral';
    }
    /**
     * Get notification priority based on transition
     */
    getNotificationPriority() {
        if (this.isSuccessfulCompletion() && this.hasQualityImprovement()) {
            return 'high';
        }
        if (this.isFailureTransition()) {
            return 'high';
        }
        if (this.requiresManualReview) {
            return 'medium';
        }
        if (this.isProgressTransition()) {
            return 'low';
        }
        return 'low';
    }
    /**
     * Get recommended actions based on status change
     */
    getRecommendedActions() {
        const actions = [];
        switch (this.newStatus) {
            case correlation_status_enum_1.CorrelationStatus.COMPLETED:
                if (this.result?.success) {
                    actions.push('Review correlation results');
                    actions.push('Validate identified patterns');
                    actions.push('Proceed to next processing stage');
                    if (this.requiresManualReview) {
                        actions.push('Schedule manual review');
                    }
                }
                else {
                    actions.push('Investigate correlation issues');
                    actions.push('Review failed rules and errors');
                }
                break;
            case correlation_status_enum_1.CorrelationStatus.FAILED:
                actions.push('Investigate correlation failure');
                actions.push('Review error logs and diagnostics');
                actions.push('Consider retry with adjusted parameters');
                actions.push('Escalate to correlation engine team');
                break;
            case correlation_status_enum_1.CorrelationStatus.PARTIAL:
                actions.push('Review partial correlation results');
                actions.push('Identify missing correlation data');
                actions.push('Consider additional correlation rules');
                actions.push('Evaluate if results are sufficient');
                break;
            case correlation_status_enum_1.CorrelationStatus.TIMEOUT:
                actions.push('Investigate correlation performance');
                actions.push('Review correlation rule complexity');
                actions.push('Consider increasing timeout limits');
                actions.push('Retry with optimized parameters');
                break;
            case correlation_status_enum_1.CorrelationStatus.IN_PROGRESS:
                actions.push('Monitor correlation progress');
                actions.push('Track processing metrics');
                break;
            case correlation_status_enum_1.CorrelationStatus.SKIPPED:
                actions.push('Review skip reason');
                actions.push('Validate skip criteria');
                break;
        }
        return actions;
    }
    /**
     * Get metrics to update based on status change
     */
    getMetricsToUpdate() {
        const metrics = [];
        // Status transition counter
        metrics.push({
            metric: 'correlation_status_transitions',
            value: 1,
            tags: {
                from_status: this.oldStatus,
                to_status: this.newStatus,
                transition_type: this.getTransitionType(),
            },
        });
        // Quality score metric
        if (this.correlationQualityScore !== undefined) {
            metrics.push({
                metric: 'correlation_quality_score',
                value: this.correlationQualityScore,
                tags: {
                    status: this.newStatus,
                },
            });
        }
        // Success/failure counters
        if (this.isSuccessfulCompletion()) {
            metrics.push({
                metric: 'correlation_completions_successful',
                value: 1,
            });
        }
        else if (this.isFailureTransition()) {
            metrics.push({
                metric: 'correlation_completions_failed',
                value: 1,
                tags: {
                    failure_type: this.newStatus,
                },
            });
        }
        // Processing duration if available
        if (this.result?.processingDurationMs) {
            metrics.push({
                metric: 'correlation_processing_duration_ms',
                value: this.result.processingDurationMs,
                tags: {
                    status: this.newStatus,
                },
            });
        }
        // Rules and matches metrics
        if (this.result) {
            metrics.push({
                metric: 'correlation_rules_applied',
                value: this.result.appliedRules.length,
            });
            metrics.push({
                metric: 'correlation_matches_found',
                value: this.result.matchesFound,
            });
            metrics.push({
                metric: 'correlation_patterns_identified',
                value: this.result.patternsIdentified.length,
            });
        }
        return metrics;
    }
    /**
     * Get event summary for handlers
     */
    getEventSummary() {
        return {
            correlatedEventId: this.aggregateId.toString(),
            oldStatus: this.oldStatus,
            newStatus: this.newStatus,
            correlationQualityScore: this.correlationQualityScore,
            requiresManualReview: this.requiresManualReview,
            changedAt: this.changedAt,
            reason: this.reason,
            isCompletedTransition: this.isCompletedTransition(),
            isFailedTransition: this.isFailedTransition(),
            isSuccessfulCompletion: this.isSuccessfulCompletion(),
            isFailureTransition: this.isFailureTransition(),
            hasQualityImprovement: this.hasQualityImprovement(),
            isProgressTransition: this.isProgressTransition(),
            isRegressionTransition: this.isRegressionTransition(),
            transitionType: this.getTransitionType(),
            notificationPriority: this.getNotificationPriority(),
            recommendedActions: this.getRecommendedActions(),
            metricsToUpdate: this.getMetricsToUpdate(),
            result: this.result,
        };
    }
    /**
     * Convert to JSON representation
     */
    toJSON() {
        return {
            ...super.toJSON(),
            eventSummary: this.getEventSummary(),
        };
    }
}
exports.CorrelatedEventStatusChangedDomainEvent = CorrelatedEventStatusChangedDomainEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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