{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\validation\\config.schema.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,yCAA2B;AAS3B,gDAAgD;AAChD,MAAM,cAAc,GAAG,GAAG,EAAE,CAAC,CAAC;IAC5B,MAAM,EAAE,CAAC,SAAS,GAAG,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,QAAQ,EAAE;IAClE,IAAI,EAAE,CAAC,YAAY,GAAG,IAAI,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,YAAY,CAAC;IACxE,QAAQ,EAAE,CAAC,YAAqB,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,YAAY,CAAC;I<PERSON>lF,WAAW,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,gBAAgB,EAAE,YAAqB,EAAE,EAAE,CAC7E,GAAG,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC;IAChE,OAAO,EAAE,CAAC,YAAqB,EAAE,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,YAAY,CAAC;IACvE,IAAI,EAAE,CAA8B,MAAS,EAAE,YAAwB,EAAE,EAAE,CACzE,YAAY,KAAK,SAAS;QACxB,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC;QACrD,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;IACnC,GAAG,EAAE,CAAC,QAAQ,GAAG,KAAK,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE;IACxF,iBAAiB,EAAE,CAAC,YAAoB,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,YAAY,CAAC;IAC/E,cAAc,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CACpC,CAAA,CAAC;AAEZ,MAAM,QAAQ,GAAG,cAAc,EAAE,CAAC;AAElC,0CAA0C;AAC1C,MAAM,wBAAwB,GAAG,GAAG,EAAE,CAAC,CAAC;IACtC,UAAU,EAAE,GAAG,CAAC,MAAM,CAAC;QACrB,QAAQ,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,QAAQ,EAAE;QACrD,UAAU,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QAC/B,kBAAkB,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QACvC,cAAc,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QACnC,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE;QAC3D,SAAS,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,CAAU,CAAC,CAAC,QAAQ,EAAE;QACvE,qBAAqB,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE;QACpE,oBAAoB,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE;KACpE,CAAC;IACF,WAAW,EAAE,GAAG,CAAC,MAAM,CAAC;QACtB,QAAQ,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,QAAQ,EAAE;QACtD,qBAAqB,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;QAC7C,wBAAwB,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;QAChD,wBAAwB,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;KACjD,CAAC;IACF,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC;QACf,QAAQ,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE;QAC/C,aAAa,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE;QACxD,SAAS,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,MAAM,CAAU,EAAE,OAAO,CAAC;KAC9D,CAAC;IACF,OAAO,EAAE,GAAG,CAAC,MAAM,CAAC;QAClB,QAAQ,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,QAAQ,EAAE;KACnD,CAAC;CACO,CAAA,CAAC;AAEZ,MAAM,UAAU,GAAG,wBAAwB,EAAE,CAAC;AAE9C,qDAAqD;AACrD,MAAM,kBAAkB,GAAG,GAAG,EAAE;IAC9B,MAAM,UAAU,GAAG;QACjB,mBAAmB;QACnB,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,YAAY,EAAE,MAAM,EAAE,SAAS,CAAU,EAAE,aAAa,CAAC;QACjG,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;QACzB,QAAQ,EAAE,QAAQ,CAAC,iBAAiB,CAAC,kBAAkB,CAAC;QACxD,WAAW,EAAE,QAAQ,CAAC,iBAAiB,CAAC,OAAO,CAAC;QAChD,UAAU,EAAE,QAAQ,CAAC,iBAAiB,CAAC,KAAK,CAAC;QAC7C,WAAW,EAAE,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC;KAC9C,CAAC;IAEF,MAAM,cAAc,GAAG;QACrB,yBAAyB;QACzB,aAAa,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,OAAO,EAAE,SAAS,CAAU,EAAE,UAAU,CAAC;QACnF,aAAa,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;QACjD,aAAa,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;QAClC,iBAAiB,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QAC1C,iBAAiB,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QAC1C,aAAa,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QACtC,eAAe,EAAE,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC;QACrD,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC;QACrC,gBAAgB,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;QACxC,oBAAoB,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC;QAC7C,uBAAuB,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;QAC/C,wBAAwB,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC;QAC5D,2BAA2B,EAAE,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC;KAC1E,CAAC;IAEF,MAAM,WAAW,GAAG;QAClB,sBAAsB;QACtB,UAAU,EAAE,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC;QAC1C,UAAU,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;QAC/B,cAAc,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;QACjD,QAAQ,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QACxC,SAAS,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC;QACnD,iBAAiB,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC;QACxD,iBAAiB,EAAE,QAAQ,CAAC,WAAW,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,CAAC;KAC9D,CAAC;IAEF,MAAM,UAAU,GAAG;QACjB,iBAAiB;QACjB,UAAU,EAAE,QAAQ,CAAC,MAAM,EAAE;QAC7B,cAAc,EAAE,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC;QAChD,kBAAkB,EAAE,QAAQ,CAAC,MAAM,EAAE;QACrC,sBAAsB,EAAE,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC;QACxD,aAAa,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;QAC9C,cAAc,EAAE,QAAQ,CAAC,MAAM,EAAE;KAClC,CAAC;IAEF,MAAM,cAAc,GAAG;QACrB,gBAAgB;QAChB,cAAc,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,SAAS,EAAE,EAAE,CAAC;QACtD,gBAAgB,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,SAAS,EAAE,GAAG,CAAC;QACzD,mCAAmC,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC;QAE5D,OAAO;QACP,WAAW,EAAE,QAAQ,CAAC,iBAAiB,CAAC,uBAAuB,CAAC;QAChE,YAAY,EAAE,QAAQ,CAAC,iBAAiB,CAAC,wCAAwC,CAAC;QAClF,gBAAgB,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;QAExC,mBAAmB;QACnB,qBAAqB,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC;QAC9C,qBAAqB,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,SAAS,EAAE,QAAQ,CAAC;QACnE,oBAAoB,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC;QAC7C,sBAAsB,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,YAAY,EAAE,YAAY,CAAU,EAAE,MAAM,CAAC;QAC5F,6BAA6B,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;KACtD,CAAC;IAEF,MAAM,aAAa,GAAG;QACpB,wBAAwB;QACxB,SAAS,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,CAAU,EAAE,MAAM,CAAC;QACzG,UAAU,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,CAAU,EAAE,MAAM,CAAC;QAC1E,gBAAgB,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC;QACzC,aAAa,EAAE,QAAQ,CAAC,iBAAiB,CAAC,cAAc,CAAC;QACzD,iBAAiB,EAAE,QAAQ,CAAC,iBAAiB,CAAC,KAAK,CAAC;QACpD,kBAAkB,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC;QACzD,mBAAmB,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;QAC3C,iBAAiB,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC;QAC1C,cAAc,EAAE,QAAQ,CAAC,iBAAiB,CAAC,gBAAgB,CAAC;KAC7D,CAAC;IAEF,MAAM,YAAY,GAAG;QACnB,eAAe;QACf,oBAAoB,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;QAC5C,6BAA6B,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;QACrD,0BAA0B,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;QAClD,kCAAkC,EAAE,QAAQ,CAAC,WAAW,CAAC,EAAE,EAAE,SAAS,EAAE,GAAG,CAAC;QAC5E,iCAAiC,EAAE,QAAQ,CAAC,WAAW,CAAC,EAAE,EAAE,SAAS,EAAE,GAAG,CAAC;KAC5E,CAAC;IAEF,MAAM,eAAe,GAAG;QACtB,2BAA2B;QAC3B,kBAAkB,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC;QAC3C,cAAc,EAAE,QAAQ,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,oBAAoB,EAAE;YACxD,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,GAAG,CAAC,QAAQ,EAAE;SAC1D,CAAC;QACF,kBAAkB,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC1D,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,GAAG,CAAC,QAAQ,EAAE;SAC1D,CAAC;QACF,kBAAkB,EAAE,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC;QAChE,yBAAyB,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QACzD,sBAAsB,EAAE,QAAQ,CAAC,WAAW,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,CAAC;QAClE,kCAAkC,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC;QAC3D,oCAAoC,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC;QAC3E,kCAAkC,EAAE,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC;KACjF,CAAC;IAEF,MAAM,sBAAsB,GAAG;QAC7B,oBAAoB;QACpB,oBAAoB,EAAE,QAAQ,CAAC,cAAc,EAAE;QAC/C,6BAA6B,EAAE,QAAQ,CAAC,cAAc,EAAE;QACxD,wBAAwB,EAAE,QAAQ,CAAC,GAAG,EAAE;QACxC,4BAA4B,EAAE,QAAQ,CAAC,cAAc,EAAE;KACxD,CAAC;IAEF,MAAM,gBAAgB,GAAG;QACvB,uBAAuB;QACvB,eAAe,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC;QACxC,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;QACjC,YAAY,EAAE,QAAQ,CAAC,iBAAiB,CAAC,UAAU,CAAC;QACpD,eAAe,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC;QACxC,oBAAoB,EAAE,QAAQ,CAAC,iBAAiB,CAAC,kBAAkB,CAAC;QACpE,uBAAuB,EAAE,QAAQ,CAAC,GAAG,EAAE;KACxC,CAAC;IAEF,MAAM,gBAAgB,GAAG;QACvB,4BAA4B;QAC5B,oBAAoB,EAAE,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,QAAQ,CAAC;QACrE,oBAAoB,EAAE,QAAQ,CAAC,iBAAiB,CAAC,2CAA2C,CAAC;QAC7F,kBAAkB,EAAE,QAAQ,CAAC,iBAAiB,CAAC,WAAW,CAAC;QAC3D,uBAAuB,EAAE,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC;KAC9D,CAAC;IAEF,MAAM,WAAW,GAAG;QAClB,sBAAsB;QACtB,iBAAiB,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,SAAS,EAAE,GAAG,CAAC;QAC1D,yBAAyB,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC;QACnE,iBAAiB,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,SAAS,EAAE,GAAG,CAAC;QAC1D,oBAAoB,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC;QAC9D,eAAe,EAAE,QAAQ,CAAC,WAAW,CAAC,GAAG,EAAE,SAAS,EAAE,KAAK,CAAC;KAC7D,CAAC;IAEF,MAAM,gBAAgB,GAAG;QACvB,aAAa;QACb,wBAAwB,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC;QAC1D,oBAAoB,EAAE,QAAQ,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC;KAC1D,CAAC;IAEF,MAAM,gBAAgB,GAAG;QACvB,2BAA2B;QAC3B,oBAAoB,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;QAC5C,iCAAiC,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;QACzD,kCAAkC,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC;QAC3D,oBAAoB,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;KAC7C,CAAC;IAEF,MAAM,iBAAiB,GAAG;QACxB,4BAA4B;QAC5B,qBAAqB,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC;QAC9C,wBAAwB,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC;QACjD,wBAAwB,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC;QACjD,0BAA0B,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC;KACpD,CAAC;IAEF,OAAO,GAAG,CAAC,MAAM,CAAC;QAChB,GAAG,UAAU;QACb,GAAG,cAAc;QACjB,GAAG,WAAW;QACd,GAAG,UAAU;QACb,GAAG,cAAc;QACjB,GAAG,aAAa;QAChB,GAAG,YAAY;QACf,GAAG,eAAe;QAClB,GAAG,sBAAsB;QACzB,GAAG,gBAAgB;QACnB,GAAG,gBAAgB;QACnB,GAAG,WAAW;QACd,GAAG,gBAAgB;QACnB,GAAG,gBAAgB;QACnB,GAAG,iBAAiB;KACrB,CAAC,CAAC;AACL,CAAC,CAAC;AAEW,QAAA,sBAAsB,GAAG,kBAAkB,EAAE,CAAC;AAE3D,gCAAgC;AACzB,MAAM,cAAc,GAAG,CAAC,MAA+B,EAAE,EAAE;IAChE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,8BAAsB,CAAC,QAAQ,CAAC,MAAM,EAAE;QAC/D,YAAY,EAAE,IAAI;QAClB,UAAU,EAAE,KAAK;KAClB,CAAC,CAAC;IAEH,IAAI,KAAK,EAAE,CAAC;QACV,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAClE,MAAM,IAAI,KAAK,CAAC,6BAA6B,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC3E,CAAC;IAED,yDAAyD;IACzD,MAAM,WAAW,GAAG,KAAK,CAAC,QAAuB,CAAC;IAClD,MAAM,SAAS,GAAG,UAAU,CAAC,WAAW,CAAC,CAAC;IAE1C,IAAI,SAAS,EAAE,CAAC;QACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACtD,IAAI,QAAQ,EAAE,CAAC;YACb,MAAM,gBAAgB,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACxE,MAAM,IAAI,KAAK,CAAC,8CAA8C,WAAW,KAAK,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC/G,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAxBW,QAAA,cAAc,kBAwBzB;AAqGF,gDAAgD;AAChD,kBAAe,IAAA,mBAAU,EAAC,KAAK,EAAE,GAAc,EAAE,CAAC,IAAA,sBAAc,EAAC,OAAO,CAAC,GAAG,CAAc,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\config\\validation\\config.schema.ts"], "sourcesContent": ["import { registerAs } from '@nestjs/config';\r\nimport * as Jo<PERSON> from 'joi';\r\n\r\n// Type definitions for better type safety\r\nexport type Environment = 'development' | 'production' | 'test' | 'staging';\r\nexport type DatabaseType = 'postgres' | 'mysql' | 'mariadb';\r\nexport type LogLevel = 'error' | 'warn' | 'info' | 'http' | 'verbose' | 'debug' | 'silly';\r\nexport type LogFormat = 'json' | 'simple' | 'combined';\r\nexport type FrameOptions = 'DENY' | 'SAMEORIGIN' | 'ALLOW-FROM';\r\n\r\n// Validation pattern factory with strong typing\r\nconst createPatterns = () => ({\r\n  secret: (minLength = 32) => Joi.string().min(minLength).required(),\r\n  port: (defaultValue = 3000) => Joi.number().port().default(defaultValue),\r\n  hostname: (defaultValue?: string) => Joi.string().hostname().default(defaultValue),\r\n  positiveInt: (min = 1, max = Number.MAX_SAFE_INTEGER, defaultValue?: number) => \r\n    Joi.number().integer().min(min).max(max).default(defaultValue),\r\n  boolean: (defaultValue: boolean) => Joi.boolean().default(defaultValue),\r\n  enum: <T extends readonly string[]>(values: T, defaultValue?: T[number]) => \r\n    defaultValue !== undefined \r\n      ? Joi.string().valid(...values).default(defaultValue)\r\n      : Joi.string().valid(...values),\r\n  uri: (required = false) => required ? Joi.string().uri().required() : Joi.string().uri(),\r\n  stringWithDefault: (defaultValue: string) => Joi.string().default(defaultValue),\r\n  optionalString: () => Joi.string().optional(),\r\n} as const);\r\n\r\nconst patterns = createPatterns();\r\n\r\n// Environment-specific validation schemas\r\nconst createEnvironmentSchemas = () => ({\r\n  production: Joi.object({\r\n    NODE_ENV: Joi.string().valid('production').required(),\r\n    JWT_SECRET: patterns.secret(64),\r\n    JWT_REFRESH_SECRET: patterns.secret(64),\r\n    SESSION_SECRET: patterns.secret(64),\r\n    DATABASE_SSL: patterns.boolean(true).valid(true).required(),\r\n    LOG_LEVEL: patterns.enum(['error', 'warn', 'info'] as const).required(),\r\n    SECURITY_HSTS_ENABLED: patterns.boolean(true).valid(true).required(),\r\n    SECURITY_CSP_ENABLED: patterns.boolean(true).valid(true).required(),\r\n  }),\r\n  development: Joi.object({\r\n    NODE_ENV: Joi.string().valid('development').required(),\r\n    DEV_ENABLE_PLAYGROUND: patterns.boolean(true),\r\n    DEV_ENABLE_INTROSPECTION: patterns.boolean(true),\r\n    DEV_ENABLE_DEBUG_LOGGING: patterns.boolean(true),\r\n  }),\r\n  test: Joi.object({\r\n    NODE_ENV: Joi.string().valid('test').required(),\r\n    DATABASE_NAME: Joi.string().pattern(/_test$/).required(),\r\n    LOG_LEVEL: patterns.enum(['error', 'warn'] as const, 'error'),\r\n  }),\r\n  staging: Joi.object({\r\n    NODE_ENV: Joi.string().valid('staging').required(),\r\n  }),\r\n} as const);\r\n\r\nconst envSchemas = createEnvironmentSchemas();\r\n\r\n// Configuration schema factory with grouped sections\r\nconst createConfigSchema = () => {\r\n  const coreSchema = {\r\n    // Core Application\r\n    NODE_ENV: patterns.enum(['development', 'production', 'test', 'staging'] as const, 'development'),\r\n    PORT: patterns.port(3000),\r\n    APP_NAME: patterns.stringWithDefault('Sentinel Backend'),\r\n    APP_VERSION: patterns.stringWithDefault('1.0.0'),\r\n    API_PREFIX: patterns.stringWithDefault('api'),\r\n    API_VERSION: patterns.stringWithDefault('v1'),\r\n  };\r\n\r\n  const databaseSchema = {\r\n    // Database Configuration\r\n    DATABASE_TYPE: patterns.enum(['postgres', 'mysql', 'mariadb'] as const, 'postgres'),\r\n    DATABASE_HOST: Joi.string().hostname().required(),\r\n    DATABASE_PORT: patterns.port(5432),\r\n    DATABASE_USERNAME: Joi.string().required(),\r\n    DATABASE_PASSWORD: Joi.string().required(),\r\n    DATABASE_NAME: Joi.string().required(),\r\n    DATABASE_SCHEMA: patterns.stringWithDefault('public'),\r\n    DATABASE_SSL: patterns.boolean(false),\r\n    DATABASE_LOGGING: patterns.boolean(true),\r\n    DATABASE_SYNCHRONIZE: patterns.boolean(false),\r\n    DATABASE_MIGRATIONS_RUN: patterns.boolean(true),\r\n    DATABASE_MAX_CONNECTIONS: patterns.positiveInt(1, 1000, 100),\r\n    DATABASE_CONNECTION_TIMEOUT: patterns.positiveInt(1000, undefined, 60000),\r\n  };\r\n\r\n  const redisSchema = {\r\n    // Redis Configuration\r\n    REDIS_HOST: patterns.hostname('localhost'),\r\n    REDIS_PORT: patterns.port(6379),\r\n    REDIS_PASSWORD: Joi.string().allow('').optional(),\r\n    REDIS_DB: patterns.positiveInt(0, 15, 0),\r\n    REDIS_TTL: patterns.positiveInt(1, undefined, 3600),\r\n    REDIS_MAX_RETRIES: patterns.positiveInt(0, undefined, 3),\r\n    REDIS_RETRY_DELAY: patterns.positiveInt(100, undefined, 1000),\r\n  };\r\n\r\n  const authSchema = {\r\n    // Authentication\r\n    JWT_SECRET: patterns.secret(),\r\n    JWT_EXPIRES_IN: patterns.stringWithDefault('1h'),\r\n    JWT_REFRESH_SECRET: patterns.secret(),\r\n    JWT_REFRESH_EXPIRES_IN: patterns.stringWithDefault('7d'),\r\n    BCRYPT_ROUNDS: patterns.positiveInt(8, 15, 12),\r\n    SESSION_SECRET: patterns.secret(),\r\n  };\r\n\r\n  const securitySchema = {\r\n    // Rate Limiting\r\n    RATE_LIMIT_TTL: patterns.positiveInt(1, undefined, 60),\r\n    RATE_LIMIT_LIMIT: patterns.positiveInt(1, undefined, 100),\r\n    RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS: patterns.boolean(false),\r\n\r\n    // CORS\r\n    CORS_ORIGIN: patterns.stringWithDefault('http://localhost:3001'),\r\n    CORS_METHODS: patterns.stringWithDefault('GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS'),\r\n    CORS_CREDENTIALS: patterns.boolean(true),\r\n\r\n    // Security Headers\r\n    SECURITY_HSTS_ENABLED: patterns.boolean(false),\r\n    SECURITY_HSTS_MAX_AGE: patterns.positiveInt(0, undefined, 31536000),\r\n    SECURITY_CSP_ENABLED: patterns.boolean(false),\r\n    SECURITY_FRAME_OPTIONS: patterns.enum(['DENY', 'SAMEORIGIN', 'ALLOW-FROM'] as const, 'DENY'),\r\n    SECURITY_CONTENT_TYPE_OPTIONS: patterns.boolean(true),\r\n  };\r\n\r\n  const loggingSchema = {\r\n    // Logging Configuration\r\n    LOG_LEVEL: patterns.enum(['error', 'warn', 'info', 'http', 'verbose', 'debug', 'silly'] as const, 'info'),\r\n    LOG_FORMAT: patterns.enum(['json', 'simple', 'combined'] as const, 'json'),\r\n    LOG_FILE_ENABLED: patterns.boolean(false),\r\n    LOG_FILE_PATH: patterns.stringWithDefault('logs/app.log'),\r\n    LOG_FILE_MAX_SIZE: patterns.stringWithDefault('10m'),\r\n    LOG_FILE_MAX_FILES: patterns.positiveInt(1, undefined, 5),\r\n    LOG_CONSOLE_ENABLED: patterns.boolean(true),\r\n    LOG_AUDIT_ENABLED: patterns.boolean(false),\r\n    LOG_AUDIT_PATH: patterns.stringWithDefault('logs/audit.log'),\r\n  };\r\n\r\n  const healthSchema = {\r\n    // Health Check\r\n    HEALTH_CHECK_ENABLED: patterns.boolean(true),\r\n    HEALTH_CHECK_DATABASE_ENABLED: patterns.boolean(true),\r\n    HEALTH_CHECK_REDIS_ENABLED: patterns.boolean(true),\r\n    HEALTH_CHECK_MEMORY_HEAP_THRESHOLD: patterns.positiveInt(50, undefined, 150),\r\n    HEALTH_CHECK_MEMORY_RSS_THRESHOLD: patterns.positiveInt(50, undefined, 150),\r\n  };\r\n\r\n  const aiServiceSchema = {\r\n    // AI Service Configuration\r\n    AI_SERVICE_ENABLED: patterns.boolean(false),\r\n    AI_SERVICE_URL: patterns.uri().when('AI_SERVICE_ENABLED', {\r\n      is: true, then: Joi.required(), otherwise: Joi.optional(),\r\n    }),\r\n    AI_SERVICE_API_KEY: Joi.string().when('AI_SERVICE_ENABLED', {\r\n      is: true, then: Joi.required(), otherwise: Joi.optional(),\r\n    }),\r\n    AI_SERVICE_TIMEOUT: patterns.positiveInt(1000, undefined, 30000),\r\n    AI_SERVICE_RETRY_ATTEMPTS: patterns.positiveInt(0, 10, 3),\r\n    AI_SERVICE_RETRY_DELAY: patterns.positiveInt(100, undefined, 1000),\r\n    AI_SERVICE_CIRCUIT_BREAKER_ENABLED: patterns.boolean(false),\r\n    AI_SERVICE_CIRCUIT_BREAKER_THRESHOLD: patterns.positiveInt(1, undefined, 5),\r\n    AI_SERVICE_CIRCUIT_BREAKER_TIMEOUT: patterns.positiveInt(1000, undefined, 60000),\r\n  };\r\n\r\n  const externalServicesSchema = {\r\n    // External Services\r\n    THREAT_INTEL_API_KEY: patterns.optionalString(),\r\n    VULNERABILITY_SCANNER_API_KEY: patterns.optionalString(),\r\n    NOTIFICATION_SERVICE_URL: patterns.uri(),\r\n    NOTIFICATION_SERVICE_API_KEY: patterns.optionalString(),\r\n  };\r\n\r\n  const monitoringSchema = {\r\n    // Monitoring & Metrics\r\n    METRICS_ENABLED: patterns.boolean(false),\r\n    METRICS_PORT: patterns.port(9090),\r\n    METRICS_PATH: patterns.stringWithDefault('/metrics'),\r\n    TRACING_ENABLED: patterns.boolean(false),\r\n    TRACING_SERVICE_NAME: patterns.stringWithDefault('sentinel-backend'),\r\n    TRACING_JAEGER_ENDPOINT: patterns.uri(),\r\n  };\r\n\r\n  const fileUploadSchema = {\r\n    // File Upload Configuration\r\n    UPLOAD_MAX_FILE_SIZE: patterns.positiveInt(1024, undefined, 10485760),\r\n    UPLOAD_ALLOWED_TYPES: patterns.stringWithDefault('application/json,text/csv,application/xml'),\r\n    UPLOAD_DESTINATION: patterns.stringWithDefault('./uploads'),\r\n    UPLOAD_TEMP_DESTINATION: patterns.stringWithDefault('./temp'),\r\n  };\r\n\r\n  const cacheSchema = {\r\n    // Cache Configuration\r\n    CACHE_TTL_DEFAULT: patterns.positiveInt(1, undefined, 300),\r\n    CACHE_TTL_VULNERABILITIES: patterns.positiveInt(1, undefined, 1800),\r\n    CACHE_TTL_THREATS: patterns.positiveInt(1, undefined, 900),\r\n    CACHE_TTL_AI_RESULTS: patterns.positiveInt(1, undefined, 3600),\r\n    CACHE_MAX_ITEMS: patterns.positiveInt(100, undefined, 10000),\r\n  };\r\n\r\n  const paginationSchema = {\r\n    // Pagination\r\n    PAGINATION_DEFAULT_LIMIT: patterns.positiveInt(1, 100, 20),\r\n    PAGINATION_MAX_LIMIT: patterns.positiveInt(10, 1000, 100),\r\n  };\r\n\r\n  const validationSchema = {\r\n    // Validation Configuration\r\n    VALIDATION_WHITELIST: patterns.boolean(true),\r\n    VALIDATION_FORBID_NON_WHITELISTED: patterns.boolean(true),\r\n    VALIDATION_SKIP_MISSING_PROPERTIES: patterns.boolean(false),\r\n    VALIDATION_TRANSFORM: patterns.boolean(true),\r\n  };\r\n\r\n  const developmentSchema = {\r\n    // Development Configuration\r\n    DEV_ENABLE_PLAYGROUND: patterns.boolean(false),\r\n    DEV_ENABLE_INTROSPECTION: patterns.boolean(false),\r\n    DEV_ENABLE_DEBUG_LOGGING: patterns.boolean(false),\r\n    DEV_MOCK_EXTERNAL_SERVICES: patterns.boolean(false),\r\n  };\r\n\r\n  return Joi.object({\r\n    ...coreSchema,\r\n    ...databaseSchema,\r\n    ...redisSchema,\r\n    ...authSchema,\r\n    ...securitySchema,\r\n    ...loggingSchema,\r\n    ...healthSchema,\r\n    ...aiServiceSchema,\r\n    ...externalServicesSchema,\r\n    ...monitoringSchema,\r\n    ...fileUploadSchema,\r\n    ...cacheSchema,\r\n    ...paginationSchema,\r\n    ...validationSchema,\r\n    ...developmentSchema,\r\n  });\r\n};\r\n\r\nexport const configValidationSchema = createConfigSchema();\r\n\r\n// Type-safe validation function\r\nexport const validateConfig = (config: Record<string, unknown>) => {\r\n  const { error, value } = configValidationSchema.validate(config, {\r\n    allowUnknown: true,\r\n    abortEarly: false,\r\n  });\r\n\r\n  if (error) {\r\n    const errorMessages = error.details.map(detail => detail.message);\r\n    throw new Error(`Config validation failed: ${errorMessages.join(', ')}`);\r\n  }\r\n\r\n  // Apply environment-specific validation with type safety\r\n  const environment = value.NODE_ENV as Environment;\r\n  const envSchema = envSchemas[environment];\r\n  \r\n  if (envSchema) {\r\n    const { error: envError } = envSchema.validate(value);\r\n    if (envError) {\r\n      const envErrorMessages = envError.details.map(detail => detail.message);\r\n      throw new Error(`Environment-specific validation failed for ${environment}: ${envErrorMessages.join(', ')}`);\r\n    }\r\n  }\r\n\r\n  return value;\r\n};\r\n\r\n// Type-safe configuration interface\r\nexport interface AppConfig {\r\n  NODE_ENV: Environment;\r\n  PORT: number;\r\n  APP_NAME: string;\r\n  APP_VERSION: string;\r\n  API_PREFIX: string;\r\n  API_VERSION: string;\r\n  DATABASE_TYPE: DatabaseType;\r\n  DATABASE_HOST: string;\r\n  DATABASE_PORT: number;\r\n  DATABASE_USERNAME: string;\r\n  DATABASE_PASSWORD: string;\r\n  DATABASE_NAME: string;\r\n  DATABASE_SCHEMA: string;\r\n  DATABASE_SSL: boolean;\r\n  DATABASE_LOGGING: boolean;\r\n  DATABASE_SYNCHRONIZE: boolean;\r\n  DATABASE_MIGRATIONS_RUN: boolean;\r\n  DATABASE_MAX_CONNECTIONS: number;\r\n  DATABASE_CONNECTION_TIMEOUT: number;\r\n  REDIS_HOST: string;\r\n  REDIS_PORT: number;\r\n  REDIS_PASSWORD?: string;\r\n  REDIS_DB: number;\r\n  REDIS_TTL: number;\r\n  REDIS_MAX_RETRIES: number;\r\n  REDIS_RETRY_DELAY: number;\r\n  JWT_SECRET: string;\r\n  JWT_EXPIRES_IN: string;\r\n  JWT_REFRESH_SECRET: string;\r\n  JWT_REFRESH_EXPIRES_IN: string;\r\n  BCRYPT_ROUNDS: number;\r\n  SESSION_SECRET: string;\r\n  RATE_LIMIT_TTL: number;\r\n  RATE_LIMIT_LIMIT: number;\r\n  RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS: boolean;\r\n  CORS_ORIGIN: string;\r\n  CORS_METHODS: string;\r\n  CORS_CREDENTIALS: boolean;\r\n  LOG_LEVEL: LogLevel;\r\n  LOG_FORMAT: LogFormat;\r\n  LOG_FILE_ENABLED: boolean;\r\n  LOG_FILE_PATH: string;\r\n  LOG_FILE_MAX_SIZE: string;\r\n  LOG_FILE_MAX_FILES: number;\r\n  LOG_CONSOLE_ENABLED: boolean;\r\n  LOG_AUDIT_ENABLED: boolean;\r\n  LOG_AUDIT_PATH: string;\r\n  SECURITY_HSTS_ENABLED: boolean;\r\n  SECURITY_HSTS_MAX_AGE: number;\r\n  SECURITY_CSP_ENABLED: boolean;\r\n  SECURITY_FRAME_OPTIONS: FrameOptions;\r\n  SECURITY_CONTENT_TYPE_OPTIONS: boolean;\r\n  HEALTH_CHECK_ENABLED: boolean;\r\n  HEALTH_CHECK_DATABASE_ENABLED: boolean;\r\n  HEALTH_CHECK_REDIS_ENABLED: boolean;\r\n  HEALTH_CHECK_MEMORY_HEAP_THRESHOLD: number;\r\n  HEALTH_CHECK_MEMORY_RSS_THRESHOLD: number;\r\n  AI_SERVICE_ENABLED: boolean;\r\n  AI_SERVICE_URL?: string;\r\n  AI_SERVICE_API_KEY?: string;\r\n  AI_SERVICE_TIMEOUT: number;\r\n  AI_SERVICE_RETRY_ATTEMPTS: number;\r\n  AI_SERVICE_RETRY_DELAY: number;\r\n  AI_SERVICE_CIRCUIT_BREAKER_ENABLED: boolean;\r\n  AI_SERVICE_CIRCUIT_BREAKER_THRESHOLD: number;\r\n  AI_SERVICE_CIRCUIT_BREAKER_TIMEOUT: number;\r\n  THREAT_INTEL_API_KEY?: string;\r\n  VULNERABILITY_SCANNER_API_KEY?: string;\r\n  NOTIFICATION_SERVICE_URL?: string;\r\n  NOTIFICATION_SERVICE_API_KEY?: string;\r\n  METRICS_ENABLED: boolean;\r\n  METRICS_PORT: number;\r\n  METRICS_PATH: string;\r\n  TRACING_ENABLED: boolean;\r\n  TRACING_SERVICE_NAME: string;\r\n  TRACING_JAEGER_ENDPOINT?: string;\r\n  UPLOAD_MAX_FILE_SIZE: number;\r\n  UPLOAD_ALLOWED_TYPES: string;\r\n  UPLOAD_DESTINATION: string;\r\n  UPLOAD_TEMP_DESTINATION: string;\r\n  CACHE_TTL_DEFAULT: number;\r\n  CACHE_TTL_VULNERABILITIES: number;\r\n  CACHE_TTL_THREATS: number;\r\n  CACHE_TTL_AI_RESULTS: number;\r\n  CACHE_MAX_ITEMS: number;\r\n  PAGINATION_DEFAULT_LIMIT: number;\r\n  PAGINATION_MAX_LIMIT: number;\r\n  VALIDATION_WHITELIST: boolean;\r\n  VALIDATION_FORBID_NON_WHITELISTED: boolean;\r\n  VALIDATION_SKIP_MISSING_PROPERTIES: boolean;\r\n  VALIDATION_TRANSFORM: boolean;\r\n  DEV_ENABLE_PLAYGROUND: boolean;\r\n  DEV_ENABLE_INTROSPECTION: boolean;\r\n  DEV_ENABLE_DEBUG_LOGGING: boolean;\r\n  DEV_MOCK_EXTERNAL_SERVICES: boolean;\r\n}\r\n\r\n// NestJS configuration factory with type safety\r\nexport default registerAs('app', (): AppConfig => validateConfig(process.env) as AppConfig);"], "version": 3}