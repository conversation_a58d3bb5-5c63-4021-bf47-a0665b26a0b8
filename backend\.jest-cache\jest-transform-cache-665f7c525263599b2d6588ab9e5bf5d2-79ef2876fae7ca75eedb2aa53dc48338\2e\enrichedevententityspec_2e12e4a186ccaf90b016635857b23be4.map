{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\__tests__\\enriched-event.entity.spec.ts", "mappings": ";;AAAA,oEAAiJ;AACjJ,gEAA8D;AAC9D,gHAA+F;AAC/F,kHAAiG;AACjG,4GAA2F;AAC3F,iEAAwD;AACxD,yEAAgE;AAChE,qEAA4D;AAC5D,2FAAiF;AACjF,+EAAqE;AACrE,+EAAsE;AAEtE,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;IACpC,IAAI,UAA8B,CAAC;IACnC,IAAI,YAA2B,CAAC;IAChC,IAAI,kBAAkC,CAAC;IACvC,IAAI,kBAAkC,CAAC;IAEvC,UAAU,CAAC,GAAG,EAAE;QACd,uBAAuB;QACvB,MAAM,WAAW,GAAG,uCAAW,CAAC,MAAM,CACpC,wCAAe,CAAC,IAAI,EACpB,eAAe,CAChB,CAAC;QACF,MAAM,cAAc,GAAG,6CAAc,CAAC,MAAM,EAAE,CAAC;QAC/C,YAAY,GAAG,2CAAa,CAAC,MAAM,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAEjE,8BAA8B;QAC9B,kBAAkB,GAAG;YACnB,EAAE,EAAE,eAAe;YACnB,IAAI,EAAE,sBAAsB;YAC5B,WAAW,EAAE,wBAAwB;YACrC,QAAQ,EAAE,GAAG;YACb,QAAQ,EAAE,KAAK;YACf,OAAO,EAAE,CAAC,yCAAgB,CAAC,aAAa,CAAC;YACzC,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;SAC1B,CAAC;QAEF,8BAA8B;QAC9B,kBAAkB,GAAG;YACnB,MAAM,EAAE,yCAAgB,CAAC,aAAa;YACtC,IAAI,EAAE,YAAY;YAClB,IAAI,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE;YACtC,UAAU,EAAE,EAAE;YACd,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,qBAAqB;QACrB,UAAU,GAAG;YACX,iBAAiB,EAAE,8BAAc,CAAC,MAAM,EAAE;YAC1C,QAAQ,EAAE,YAAY;YACtB,IAAI,EAAE,2BAAS,CAAC,eAAe;YAC/B,QAAQ,EAAE,mCAAa,CAAC,MAAM;YAC9B,MAAM,EAAE,+BAAW,CAAC,MAAM;YAC1B,gBAAgB,EAAE,oDAAqB,CAAC,QAAQ;YAChD,gBAAgB,EAAE,wCAAgB,CAAC,SAAS;YAC5C,cAAc,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,IAAI,EAAE;YAC1D,YAAY,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,EAAE,EAAE;YACxE,KAAK,EAAE,qBAAqB;YAC5B,WAAW,EAAE,gCAAgC;YAC7C,IAAI,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC;YAC1B,SAAS,EAAE,EAAE;YACb,eAAe,EAAE,EAAE;YACnB,UAAU,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;YAC1B,YAAY,EAAE,CAAC,kBAAkB,CAAC;YAClC,cAAc,EAAE,CAAC,kBAAkB,CAAC;YACpC,sBAAsB,EAAE,EAAE;YAC1B,gBAAgB,EAAE,EAAE;SACrB,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,MAAM,aAAa,GAAG,qCAAa,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAEvD,MAAM,CAAC,aAAa,CAAC,CAAC,cAAc,CAAC,qCAAa,CAAC,CAAC;YACpD,MAAM,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;YAC9E,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,eAAe,CAAC,CAAC;YAC3D,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,mCAAa,CAAC,MAAM,CAAC,CAAC;YAC1D,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,wCAAgB,CAAC,SAAS,CAAC,CAAC;YACxE,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACxD,MAAM,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACtD,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,aAAa,GAAG,qCAAa,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACvD,MAAM,YAAY,GAAG,aAAa,CAAC,oBAAoB,EAAE,CAAC;YAE1D,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,aAAa,GAAG,qCAAa,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACvD,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;YACvC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,8BAAc,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,QAAQ,GAAG,8BAAc,CAAC,MAAM,EAAE,CAAC;YACzC,MAAM,aAAa,GAAG,qCAAa,CAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YACjE,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,CAAC;YACvC,OAAQ,YAAoB,CAAC,iBAAiB,CAAC;YAE/C,MAAM,CAAC,GAAG,EAAE,CAAC,qCAAa,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CACtD,iDAAiD,CAClD,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,CAAC;YACvC,OAAQ,YAAoB,CAAC,QAAQ,CAAC;YAEtC,MAAM,CAAC,GAAG,EAAE,CAAC,qCAAa,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CACtD,kCAAkC,CACnC,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,CAAC;YACvC,OAAQ,YAAoB,CAAC,gBAAgB,CAAC;YAE9C,MAAM,CAAC,GAAG,EAAE,CAAC,qCAAa,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CACtD,8CAA8C,CAC/C,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,CAAC;YACvC,OAAQ,YAAoB,CAAC,cAAc,CAAC;YAE5C,MAAM,CAAC,GAAG,EAAE,CAAC,qCAAa,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CACtD,yCAAyC,CAC1C,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,CAAC;YACvC,OAAQ,YAAoB,CAAC,YAAY,CAAC;YAE1C,MAAM,CAAC,GAAG,EAAE,CAAC,qCAAa,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CACtD,uCAAuC,CACxC,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;YAElD,MAAM,CAAC,GAAG,EAAE,CAAC,qCAAa,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CACtD,2CAA2C,CAC5C,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;YACxE,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,sBAAsB,EAAE,GAAG,EAAE,CAAC;YAEpE,MAAM,CAAC,GAAG,EAAE,CAAC,qCAAa,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CACtD,oDAAoD,CACrD,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iEAAiE,EAAE,GAAG,EAAE;YACzE,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE,EAAE,CAAC;YAE9D,MAAM,CAAC,GAAG,EAAE,CAAC,qCAAa,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CACtD,qDAAqD,CACtD,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,kBAAkB,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;gBAC/D,GAAG,kBAAkB;gBACrB,MAAM,EAAE,yCAAgB,CAAC,aAAa;gBACtC,IAAI,EAAE,QAAQ,CAAC,EAAE;aAClB,CAAC,CAAC,CAAC;YAEJ,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,cAAc,EAAE,kBAAkB,EAAE,CAAC;YAE3E,MAAM,CAAC,GAAG,EAAE,CAAC,qCAAa,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CACtD,gEAAgE,CACjE,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,IAAI,aAA4B,CAAC;QAEjC,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,gBAAgB,EAAE,wCAAgB,CAAC,OAAO,EAAE,CAAC;YACnF,aAAa,GAAG,qCAAa,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YACnD,aAAa,CAAC,qBAAqB,EAAE,CAAC,CAAC,uBAAuB;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,aAAa,CAAC,eAAe,EAAE,CAAC;YAEhC,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,wCAAgB,CAAC,WAAW,CAAC,CAAC;YAC1E,MAAM,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;YACxD,MAAM,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,aAAa,CAAC,eAAe,EAAE,CAAC;YAEhC,MAAM,MAAM,GAAqB;gBAC/B,OAAO,EAAE,IAAI;gBACb,YAAY,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;gBAChC,WAAW,EAAE,EAAE;gBACf,QAAQ,EAAE,EAAE;gBACZ,MAAM,EAAE,EAAE;gBACV,oBAAoB,EAAE,IAAI;gBAC1B,eAAe,EAAE,EAAE;gBACnB,WAAW,EAAE,CAAC;gBACd,kBAAkB,EAAE,EAAE;aACvB,CAAC;YAEF,aAAa,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;YAEzC,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,wCAAgB,CAAC,SAAS,CAAC,CAAC;YACxE,MAAM,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1D,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACvD,MAAM,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,aAAa,EAAE,CAAC;YAE1D,MAAM,YAAY,GAAG,aAAa,CAAC,oBAAoB,EAAE,CAAC;YAC1D,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,aAAa,CAAC,eAAe,EAAE,CAAC;YAEhC,MAAM,KAAK,GAAG,mCAAmC,CAAC;YAClD,aAAa,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAEpC,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,wCAAgB,CAAC,MAAM,CAAC,CAAC;YACrE,MAAM,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEtD,MAAM,YAAY,GAAG,aAAa,CAAC,oBAAoB,EAAE,CAAC;YAC1D,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QAC5F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,MAAM,GAAG,6BAA6B,CAAC;YAC7C,aAAa,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAErC,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,wCAAgB,CAAC,OAAO,CAAC,CAAC;YACtE,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC/C,MAAM,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,aAAa,EAAE,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,aAAa,CAAC,eAAe,EAAE,CAAC;YAChC,aAAa,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAE3C,aAAa,CAAC,eAAe,EAAE,CAAC;YAEhC,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,wCAAgB,CAAC,OAAO,CAAC,CAAC;YACtE,MAAM,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,aAAa,EAAE,CAAC;YAC1D,MAAM,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,aAAa,EAAE,CAAC;YAC5D,MAAM,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,aAAa,EAAE,CAAC;YAC1D,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,aAAa,EAAE,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,aAAa,CAAC,eAAe,EAAE,CAAC;YAEhC,MAAM,CAAC,GAAG,EAAE,CAAC,aAAa,CAAC,eAAe,EAAE,CAAC,CAAC,OAAO,CACnD,8CAA8C,CAC/C,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kEAAkE,EAAE,GAAG,EAAE;YAC1E,MAAM,MAAM,GAAqB;gBAC/B,OAAO,EAAE,IAAI;gBACb,YAAY,EAAE,EAAE;gBAChB,WAAW,EAAE,EAAE;gBACf,QAAQ,EAAE,EAAE;gBACZ,MAAM,EAAE,EAAE;gBACV,oBAAoB,EAAE,IAAI;gBAC1B,eAAe,EAAE,EAAE;gBACnB,WAAW,EAAE,CAAC;gBACd,kBAAkB,EAAE,CAAC;aACtB,CAAC;YAEF,MAAM,CAAC,GAAG,EAAE,CAAC,aAAa,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAC5D,qDAAqD,CACtD,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,IAAI,aAA4B,CAAC;QAEjC,UAAU,CAAC,GAAG,EAAE;YACd,aAAa,GAAG,qCAAa,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,iBAAiB,GAAmB;gBACxC,MAAM,EAAE,yCAAgB,CAAC,cAAc;gBACvC,IAAI,EAAE,aAAa;gBACnB,IAAI,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE;gBACzC,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,aAAa,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YAEnD,MAAM,cAAc,GAAG,aAAa,CAAC,cAAc,CAAC;YACpD,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kEAAkE,EAAE,GAAG,EAAE;YAC1E,MAAM,WAAW,GAAmB;gBAClC,MAAM,EAAE,yCAAgB,CAAC,aAAa;gBACtC,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE;gBACxC,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,aAAa,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;YAE7C,MAAM,cAAc,GAAG,aAAa,CAAC,cAAc,CAAC;YACpD,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC9C,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,cAAc,GAAG,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,SAAS,EAAE,CAAC;YAC5E,aAAa,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;YAEjD,MAAM,YAAY,GAAG,aAAa,CAAC,YAAY,CAAC;YAChD,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACjD,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,OAAO,GAAmB;gBAC9B,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,UAAU;gBAChB,WAAW,EAAE,uBAAuB;gBACpC,QAAQ,EAAE,EAAE;gBACZ,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,CAAC,yCAAgB,CAAC,mBAAmB,CAAC;aAChD,CAAC;YAEF,aAAa,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAEtC,MAAM,YAAY,GAAG,aAAa,CAAC,YAAY,CAAC;YAChD,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,aAAa,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;YAEjD,MAAM,YAAY,GAAG,aAAa,CAAC,YAAY,CAAC;YAChD,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,IAAI,aAA4B,CAAC;QAEjC,UAAU,CAAC,GAAG,EAAE;YACd,aAAa,GAAG,qCAAa,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,YAAY,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;YAC/D,aAAa,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;YAE/C,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,WAAW,GAAG,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,aAAa,EAAE,CAAC;YACxE,aAAa,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;YAE7C,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,cAAc,GAAG,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;YACjE,aAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;YAEnD,MAAM,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,UAAU,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC;YAC5D,aAAa,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;YAEnD,MAAM,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,aAAa,CAAC,kBAAkB,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;YACnD,aAAa,CAAC,kBAAkB,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;YAE/C,MAAM,gBAAgB,GAAG,aAAa,CAAC,gBAAgB,CAAC;YACxD,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC7C,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,CAAC,GAAG,EAAE,CAAC,aAAa,CAAC,kBAAkB,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,CACjE,4CAA4C,CAC7C,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,IAAI,aAA4B,CAAC;QAEjC,UAAU,CAAC,GAAG,EAAE;YACd,aAAa,GAAG,qCAAa,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,aAAa,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC;YACzC,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,CAAC,GAAG,EAAE,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAC5D,qDAAqD,CACtD,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,aAAa,CAAC,4BAA4B,CAAC,EAAE,CAAC,CAAC;YAC/C,MAAM,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,CAAC,GAAG,EAAE,CAAC,aAAa,CAAC,4BAA4B,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CACnE,oDAAoD,CACrD,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,IAAI,aAA4B,CAAC;QAEjC,UAAU,CAAC,GAAG,EAAE;YACd,aAAa,GAAG,qCAAa,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,MAAM,GAAG,yCAAyC,CAAC;YACzD,aAAa,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAE1C,MAAM,CAAC,aAAa,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,aAAa,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;YAEjD,MAAM,QAAQ,GAAG,qBAAqB,CAAC;YACvC,MAAM,KAAK,GAAG,uBAAuB,CAAC;YACtC,aAAa,CAAC,oBAAoB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAEpD,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAChD,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YAC/C,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oEAAoE,EAAE,GAAG,EAAE;YAC5E,MAAM,CAAC,GAAG,EAAE,CAAC,aAAa,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,CAAC,CAAC,OAAO,CAC7E,uCAAuC,CACxC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,IAAI,aAA4B,CAAC;QAEjC,UAAU,CAAC,GAAG,EAAE;YACd,aAAa,GAAG,qCAAa,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;YACtC,aAAa,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAE1C,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACvD,MAAM,CAAC,aAAa,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,aAAa,CAAC,mBAAmB,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YAC/C,aAAa,CAAC,qBAAqB,EAAE,CAAC;YAEtC,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACnD,MAAM,CAAC,aAAa,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAE1E,MAAM,CAAC,GAAG,EAAE,CAAC,aAAa,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CACjE,4CAA4C,CAC7C,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,IAAI,aAA4B,CAAC;QAEjC,UAAU,CAAC,GAAG,EAAE;YACd,aAAa,GAAG,qCAAa,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzD,MAAM,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvD,MAAM,CAAC,aAAa,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,CAAC,aAAa,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxD,MAAM,CAAC,aAAa,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,CAAC,aAAa,CAAC,wBAAwB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,aAAa,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAErD,aAAa,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC;YACzC,MAAM,CAAC,aAAa,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1D,MAAM,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEvD,aAAa,CAAC,kBAAkB,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;YAC7C,MAAM,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,aAAa,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEvD,aAAa,CAAC,mBAAmB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;YAClD,MAAM,CAAC,aAAa,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,eAAe,GAAG;gBACtB,GAAG,UAAU;gBACb,mBAAmB,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;gBAChD,qBAAqB,EAAE,IAAI,IAAI,EAAE;aAClC,CAAC;YAEF,MAAM,UAAU,GAAG,qCAAa,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YACzD,MAAM,QAAQ,GAAG,UAAU,CAAC,qBAAqB,EAAE,CAAC;YAEpD,MAAM,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,SAAS,GAAG,aAAa,CAAC,mBAAmB,EAAE,CAAC;YACtD,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,CAAC,aAAa,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjE,MAAM,CAAC,aAAa,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,gBAAgB,GAAG,aAAa,CAAC,yBAAyB,CAAC,yCAAgB,CAAC,aAAa,CAAC,CAAC;YACjG,MAAM,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAEpD,MAAM,OAAO,GAAG,aAAa,CAAC,yBAAyB,CAAC,yCAAgB,CAAC,cAAc,CAAC,CAAC;YACzF,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,cAAc,GAAG,aAAa,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;YAC3E,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,yCAAgB,CAAC,aAAa,CAAC,CAAC;YAEtE,MAAM,OAAO,GAAG,aAAa,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC;YACrE,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,aAAa,CAAC,yBAAyB,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;YAE7D,aAAa,CAAC,kBAAkB,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YAChD,aAAa,CAAC,kBAAkB,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YAEhD,MAAM,CAAC,aAAa,CAAC,yBAAyB,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,CAAC,aAAa,CAAC,gCAAgC,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAErE,oCAAoC;YACpC,MAAM,iBAAiB,GAAG,EAAE,GAAG,UAAU,EAAE,kBAAkB,EAAE,CAAC,EAAE,CAAC;YACnE,MAAM,iBAAiB,GAAG,qCAAa,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YAElE,MAAM,CAAC,iBAAiB,CAAC,gCAAgC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,IAAI,aAA4B,CAAC;QAEjC,UAAU,CAAC,GAAG,EAAE;YACd,aAAa,GAAG,qCAAa,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,OAAO,GAAG,aAAa,CAAC,UAAU,EAAE,CAAC;YAE3C,MAAM,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC;gBAC5B,EAAE,EAAE,aAAa,CAAC,EAAE,CAAC,QAAQ,EAAE;gBAC/B,iBAAiB,EAAE,UAAU,CAAC,iBAAiB,CAAC,QAAQ,EAAE;gBAC1D,KAAK,EAAE,qBAAqB;gBAC5B,IAAI,EAAE,2BAAS,CAAC,eAAe;gBAC/B,QAAQ,EAAE,mCAAa,CAAC,MAAM;gBAC9B,MAAM,EAAE,+BAAW,CAAC,MAAM;gBAC1B,gBAAgB,EAAE,wCAAgB,CAAC,SAAS;gBAC5C,sBAAsB,EAAE,EAAE;gBAC1B,gBAAgB,EAAE,EAAE;gBACpB,iBAAiB,EAAE,CAAC;gBACpB,mBAAmB,EAAE,CAAC;gBACtB,mBAAmB,EAAE,KAAK;gBAC1B,oBAAoB,EAAE,KAAK;gBAC3B,mBAAmB,EAAE,IAAI;gBACzB,qBAAqB,EAAE,KAAK;gBAC5B,iBAAiB,EAAE,KAAK;aACzB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,IAAI,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC;YAEpC,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC;YACjD,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAC5C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAC5C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YACvC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,2BAAS,CAAC,eAAe,CAAC,CAAC;YAClD,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,wCAAgB,CAAC,SAAS,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,IAAI,aAA4B,CAAC;QAEjC,UAAU,CAAC,GAAG,EAAE;YACd,aAAa,GAAG,qCAAa,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,cAAc,GAAG,aAAa,CAAC,cAAc,CAAC;YACpD,MAAM,YAAY,GAAG,aAAa,CAAC,YAAY,CAAC;YAChD,MAAM,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC;YAChC,MAAM,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC;YAC5C,MAAM,YAAY,GAAG,aAAa,CAAC,YAAY,CAAC;YAChD,MAAM,cAAc,GAAG,aAAa,CAAC,cAAc,CAAC;YAEpD,0BAA0B;YAC1B,cAAc,CAAC,QAAQ,GAAG,IAAI,CAAC;YAC/B,YAAY,CAAC,QAAQ,GAAG,IAAI,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACtB,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;YAC3B,YAAY,CAAC,IAAI,CAAC,EAAS,CAAC,CAAC;YAC7B,cAAc,CAAC,IAAI,CAAC,EAAS,CAAC,CAAC;YAE/B,+BAA+B;YAC/B,MAAM,CAAC,aAAa,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;YAC9D,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;YAC5D,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YACrD,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;YAC1D,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACnD,MAAM,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\entities\\__tests__\\enriched-event.entity.spec.ts"], "sourcesContent": ["import { EnrichedEvent, EnrichedEventProps, EnrichmentStatus, EnrichmentRule, EnrichmentData, EnrichmentResult } from '../enriched-event.entity';\r\nimport { UniqueEntityId } from '../../../../../shared-kernel';\r\nimport { EventMetadata } from '../../value-objects/event-metadata/event-metadata.value-object';\r\nimport { EventTimestamp } from '../../value-objects/event-metadata/event-timestamp.value-object';\r\nimport { EventSource } from '../../value-objects/event-metadata/event-source.value-object';\r\nimport { EventType } from '../../enums/event-type.enum';\r\nimport { EventSeverity } from '../../enums/event-severity.enum';\r\nimport { EventStatus } from '../../enums/event-status.enum';\r\nimport { EventProcessingStatus } from '../../enums/event-processing-status.enum';\r\nimport { EventSourceType } from '../../enums/event-source-type.enum';\r\nimport { EnrichmentSource } from '../../enums/enrichment-source.enum';\r\n\r\ndescribe('EnrichedEvent Entity', () => {\r\n  let validProps: EnrichedEventProps;\r\n  let mockMetadata: EventMetadata;\r\n  let mockEnrichmentRule: EnrichmentRule;\r\n  let mockEnrichmentData: EnrichmentData;\r\n\r\n  beforeEach(() => {\r\n    // Create mock metadata\r\n    const eventSource = EventSource.create(\r\n      EventSourceType.SIEM,\r\n      'test-siem-001'\r\n    );\r\n    const eventTimestamp = EventTimestamp.create();\r\n    mockMetadata = EventMetadata.create(eventTimestamp, eventSource);\r\n\r\n    // Create mock enrichment rule\r\n    mockEnrichmentRule = {\r\n      id: 'test-rule-001',\r\n      name: 'Test Enrichment Rule',\r\n      description: 'A test enrichment rule',\r\n      priority: 100,\r\n      required: false,\r\n      sources: [EnrichmentSource.IP_REPUTATION],\r\n      config: { timeout: 5000 },\r\n    };\r\n\r\n    // Create mock enrichment data\r\n    mockEnrichmentData = {\r\n      source: EnrichmentSource.IP_REPUTATION,\r\n      type: 'reputation',\r\n      data: { score: 75, category: 'clean' },\r\n      confidence: 85,\r\n      timestamp: new Date(),\r\n    };\r\n\r\n    // Create valid props\r\n    validProps = {\r\n      normalizedEventId: UniqueEntityId.create(),\r\n      metadata: mockMetadata,\r\n      type: EventType.THREAT_DETECTED,\r\n      severity: EventSeverity.MEDIUM,\r\n      status: EventStatus.ACTIVE,\r\n      processingStatus: EventProcessingStatus.ENRICHED,\r\n      enrichmentStatus: EnrichmentStatus.COMPLETED,\r\n      normalizedData: { event_type: 'threat', normalized: true },\r\n      enrichedData: { event_type: 'threat', enriched: true, threat_score: 45 },\r\n      title: 'Test Enriched Event',\r\n      description: 'A test enriched security event',\r\n      tags: ['test', 'enriched'],\r\n      riskScore: 65,\r\n      confidenceLevel: 80,\r\n      attributes: { test: true },\r\n      appliedRules: [mockEnrichmentRule],\r\n      enrichmentData: [mockEnrichmentData],\r\n      enrichmentQualityScore: 85,\r\n      threatIntelScore: 45,\r\n    };\r\n  });\r\n\r\n  describe('creation', () => {\r\n    it('should create a valid enriched event with required properties', () => {\r\n      const enrichedEvent = EnrichedEvent.create(validProps);\r\n\r\n      expect(enrichedEvent).toBeInstanceOf(EnrichedEvent);\r\n      expect(enrichedEvent.normalizedEventId).toEqual(validProps.normalizedEventId);\r\n      expect(enrichedEvent.type).toBe(EventType.THREAT_DETECTED);\r\n      expect(enrichedEvent.severity).toBe(EventSeverity.MEDIUM);\r\n      expect(enrichedEvent.enrichmentStatus).toBe(EnrichmentStatus.COMPLETED);\r\n      expect(enrichedEvent.title).toBe('Test Enriched Event');\r\n      expect(enrichedEvent.enrichmentQualityScore).toBe(85);\r\n      expect(enrichedEvent.threatIntelScore).toBe(45);\r\n    });\r\n\r\n    it('should generate domain event on creation', () => {\r\n      const enrichedEvent = EnrichedEvent.create(validProps);\r\n      const domainEvents = enrichedEvent.getUncommittedEvents();\r\n\r\n      expect(domainEvents).toHaveLength(1);\r\n      expect(domainEvents[0].constructor.name).toBe('EnrichedEventCreatedDomainEvent');\r\n    });\r\n\r\n    it('should assign unique ID if not provided', () => {\r\n      const enrichedEvent = EnrichedEvent.create(validProps);\r\n      expect(enrichedEvent.id).toBeDefined();\r\n      expect(enrichedEvent.id).toBeInstanceOf(UniqueEntityId);\r\n    });\r\n\r\n    it('should use provided ID if given', () => {\r\n      const customId = UniqueEntityId.create();\r\n      const enrichedEvent = EnrichedEvent.create(validProps, customId);\r\n      expect(enrichedEvent.id).toEqual(customId);\r\n    });\r\n  });\r\n\r\n  describe('validation', () => {\r\n    it('should throw error if normalized event ID is missing', () => {\r\n      const invalidProps = { ...validProps };\r\n      delete (invalidProps as any).normalizedEventId;\r\n\r\n      expect(() => EnrichedEvent.create(invalidProps)).toThrow(\r\n        'EnrichedEvent must reference a normalized event'\r\n      );\r\n    });\r\n\r\n    it('should throw error if metadata is missing', () => {\r\n      const invalidProps = { ...validProps };\r\n      delete (invalidProps as any).metadata;\r\n\r\n      expect(() => EnrichedEvent.create(invalidProps)).toThrow(\r\n        'EnrichedEvent must have metadata'\r\n      );\r\n    });\r\n\r\n    it('should throw error if enrichment status is missing', () => {\r\n      const invalidProps = { ...validProps };\r\n      delete (invalidProps as any).enrichmentStatus;\r\n\r\n      expect(() => EnrichedEvent.create(invalidProps)).toThrow(\r\n        'EnrichedEvent must have an enrichment status'\r\n      );\r\n    });\r\n\r\n    it('should throw error if normalized data is missing', () => {\r\n      const invalidProps = { ...validProps };\r\n      delete (invalidProps as any).normalizedData;\r\n\r\n      expect(() => EnrichedEvent.create(invalidProps)).toThrow(\r\n        'EnrichedEvent must have normalized data'\r\n      );\r\n    });\r\n\r\n    it('should throw error if enriched data is missing', () => {\r\n      const invalidProps = { ...validProps };\r\n      delete (invalidProps as any).enrichedData;\r\n\r\n      expect(() => EnrichedEvent.create(invalidProps)).toThrow(\r\n        'EnrichedEvent must have enriched data'\r\n      );\r\n    });\r\n\r\n    it('should throw error if title is empty', () => {\r\n      const invalidProps = { ...validProps, title: '' };\r\n\r\n      expect(() => EnrichedEvent.create(invalidProps)).toThrow(\r\n        'EnrichedEvent must have a non-empty title'\r\n      );\r\n    });\r\n\r\n    it('should throw error if enrichment quality score is out of range', () => {\r\n      const invalidProps = { ...validProps, enrichmentQualityScore: 150 };\r\n\r\n      expect(() => EnrichedEvent.create(invalidProps)).toThrow(\r\n        'Enrichment quality score must be between 0 and 100'\r\n      );\r\n    });\r\n\r\n    it('should throw error if threat intelligence score is out of range', () => {\r\n      const invalidProps = { ...validProps, threatIntelScore: -10 };\r\n\r\n      expect(() => EnrichedEvent.create(invalidProps)).toThrow(\r\n        'Threat intelligence score must be between 0 and 100'\r\n      );\r\n    });\r\n\r\n    it('should throw error if too many enrichment data sources', () => {\r\n      const manyEnrichmentData = Array.from({ length: 51 }, (_, i) => ({\r\n        ...mockEnrichmentData,\r\n        source: EnrichmentSource.IP_REPUTATION,\r\n        type: `type-${i}`,\r\n      }));\r\n\r\n      const invalidProps = { ...validProps, enrichmentData: manyEnrichmentData };\r\n\r\n      expect(() => EnrichedEvent.create(invalidProps)).toThrow(\r\n        'EnrichedEvent cannot have more than 50 enrichment data sources'\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('enrichment lifecycle', () => {\r\n    let enrichedEvent: EnrichedEvent;\r\n\r\n    beforeEach(() => {\r\n      const pendingProps = { ...validProps, enrichmentStatus: EnrichmentStatus.PENDING };\r\n      enrichedEvent = EnrichedEvent.create(pendingProps);\r\n      enrichedEvent.markEventsAsCommitted(); // Clear creation event\r\n    });\r\n\r\n    it('should start enrichment process', () => {\r\n      enrichedEvent.startEnrichment();\r\n\r\n      expect(enrichedEvent.enrichmentStatus).toBe(EnrichmentStatus.IN_PROGRESS);\r\n      expect(enrichedEvent.enrichmentStartedAt).toBeDefined();\r\n      expect(enrichedEvent.enrichmentAttempts).toBe(1);\r\n    });\r\n\r\n    it('should complete enrichment process', () => {\r\n      enrichedEvent.startEnrichment();\r\n      \r\n      const result: EnrichmentResult = {\r\n        success: true,\r\n        appliedRules: ['rule1', 'rule2'],\r\n        failedRules: [],\r\n        warnings: [],\r\n        errors: [],\r\n        processingDurationMs: 1500,\r\n        confidenceScore: 85,\r\n        sourcesUsed: 3,\r\n        dataPointsEnriched: 10,\r\n      };\r\n\r\n      enrichedEvent.completeEnrichment(result);\r\n\r\n      expect(enrichedEvent.enrichmentStatus).toBe(EnrichmentStatus.COMPLETED);\r\n      expect(enrichedEvent.enrichmentCompletedAt).toBeDefined();\r\n      expect(enrichedEvent.enrichmentResult).toEqual(result);\r\n      expect(enrichedEvent.lastEnrichmentError).toBeUndefined();\r\n\r\n      const domainEvents = enrichedEvent.getUncommittedEvents();\r\n      expect(domainEvents).toHaveLength(1);\r\n      expect(domainEvents[0].constructor.name).toBe('EnrichedEventStatusChangedDomainEvent');\r\n    });\r\n\r\n    it('should fail enrichment process', () => {\r\n      enrichedEvent.startEnrichment();\r\n      \r\n      const error = 'Network timeout during enrichment';\r\n      enrichedEvent.failEnrichment(error);\r\n\r\n      expect(enrichedEvent.enrichmentStatus).toBe(EnrichmentStatus.FAILED);\r\n      expect(enrichedEvent.lastEnrichmentError).toBe(error);\r\n\r\n      const domainEvents = enrichedEvent.getUncommittedEvents();\r\n      expect(domainEvents).toHaveLength(1);\r\n      expect(domainEvents[0].constructor.name).toBe('EnrichedEventEnrichmentFailedDomainEvent');\r\n    });\r\n\r\n    it('should skip enrichment process', () => {\r\n      const reason = 'Skipped due to low priority';\r\n      enrichedEvent.skipEnrichment(reason);\r\n\r\n      expect(enrichedEvent.enrichmentStatus).toBe(EnrichmentStatus.SKIPPED);\r\n      expect(enrichedEvent.reviewNotes).toBe(reason);\r\n      expect(enrichedEvent.lastEnrichmentError).toBeUndefined();\r\n    });\r\n\r\n    it('should reset enrichment for retry', () => {\r\n      enrichedEvent.startEnrichment();\r\n      enrichedEvent.failEnrichment('Test error');\r\n      \r\n      enrichedEvent.resetEnrichment();\r\n\r\n      expect(enrichedEvent.enrichmentStatus).toBe(EnrichmentStatus.PENDING);\r\n      expect(enrichedEvent.enrichmentStartedAt).toBeUndefined();\r\n      expect(enrichedEvent.enrichmentCompletedAt).toBeUndefined();\r\n      expect(enrichedEvent.lastEnrichmentError).toBeUndefined();\r\n      expect(enrichedEvent.enrichmentResult).toBeUndefined();\r\n    });\r\n\r\n    it('should throw error when starting enrichment if not pending', () => {\r\n      enrichedEvent.startEnrichment();\r\n\r\n      expect(() => enrichedEvent.startEnrichment()).toThrow(\r\n        'Can only start enrichment for pending events'\r\n      );\r\n    });\r\n\r\n    it('should throw error when completing enrichment if not in progress', () => {\r\n      const result: EnrichmentResult = {\r\n        success: true,\r\n        appliedRules: [],\r\n        failedRules: [],\r\n        warnings: [],\r\n        errors: [],\r\n        processingDurationMs: 1000,\r\n        confidenceScore: 80,\r\n        sourcesUsed: 1,\r\n        dataPointsEnriched: 5,\r\n      };\r\n\r\n      expect(() => enrichedEvent.completeEnrichment(result)).toThrow(\r\n        'Can only complete enrichment for in-progress events'\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('enrichment data management', () => {\r\n    let enrichedEvent: EnrichedEvent;\r\n\r\n    beforeEach(() => {\r\n      enrichedEvent = EnrichedEvent.create(validProps);\r\n    });\r\n\r\n    it('should add enrichment data', () => {\r\n      const newEnrichmentData: EnrichmentData = {\r\n        source: EnrichmentSource.IP_GEOLOCATION,\r\n        type: 'geolocation',\r\n        data: { country: 'US', city: 'New York' },\r\n        confidence: 95,\r\n        timestamp: new Date(),\r\n      };\r\n\r\n      enrichedEvent.addEnrichmentData(newEnrichmentData);\r\n\r\n      const enrichmentData = enrichedEvent.enrichmentData;\r\n      expect(enrichmentData).toHaveLength(2);\r\n      expect(enrichmentData[1]).toEqual(newEnrichmentData);\r\n    });\r\n\r\n    it('should update existing enrichment data with same source and type', () => {\r\n      const updatedData: EnrichmentData = {\r\n        source: EnrichmentSource.IP_REPUTATION,\r\n        type: 'reputation',\r\n        data: { score: 90, category: 'trusted' },\r\n        confidence: 95,\r\n        timestamp: new Date(),\r\n      };\r\n\r\n      enrichedEvent.addEnrichmentData(updatedData);\r\n\r\n      const enrichmentData = enrichedEvent.enrichmentData;\r\n      expect(enrichmentData).toHaveLength(1);\r\n      expect(enrichmentData[0].data.score).toBe(90);\r\n      expect(enrichmentData[0].data.category).toBe('trusted');\r\n    });\r\n\r\n    it('should update enriched data', () => {\r\n      const additionalData = { new_field: 'new_value', updated_field: 'updated' };\r\n      enrichedEvent.updateEnrichedData(additionalData);\r\n\r\n      const enrichedData = enrichedEvent.enrichedData;\r\n      expect(enrichedData.new_field).toBe('new_value');\r\n      expect(enrichedData.updated_field).toBe('updated');\r\n    });\r\n\r\n    it('should add applied enrichment rule', () => {\r\n      const newRule: EnrichmentRule = {\r\n        id: 'new-rule-001',\r\n        name: 'New Rule',\r\n        description: 'A new enrichment rule',\r\n        priority: 50,\r\n        required: true,\r\n        sources: [EnrichmentSource.THREAT_INTELLIGENCE],\r\n      };\r\n\r\n      enrichedEvent.addAppliedRule(newRule);\r\n\r\n      const appliedRules = enrichedEvent.appliedRules;\r\n      expect(appliedRules).toHaveLength(2);\r\n      expect(appliedRules[1]).toEqual(newRule);\r\n    });\r\n\r\n    it('should not add duplicate applied rule', () => {\r\n      enrichedEvent.addAppliedRule(mockEnrichmentRule);\r\n\r\n      const appliedRules = enrichedEvent.appliedRules;\r\n      expect(appliedRules).toHaveLength(1);\r\n    });\r\n  });\r\n\r\n  describe('context management', () => {\r\n    let enrichedEvent: EnrichedEvent;\r\n\r\n    beforeEach(() => {\r\n      enrichedEvent = EnrichedEvent.create(validProps);\r\n    });\r\n\r\n    it('should update asset context', () => {\r\n      const assetContext = { owner: 'IT Team', criticality: 'high' };\r\n      enrichedEvent.updateAssetContext(assetContext);\r\n\r\n      expect(enrichedEvent.assetContext).toEqual(assetContext);\r\n    });\r\n\r\n    it('should update user context', () => {\r\n      const userContext = { username: 'john.doe', department: 'Engineering' };\r\n      enrichedEvent.updateUserContext(userContext);\r\n\r\n      expect(enrichedEvent.userContext).toEqual(userContext);\r\n    });\r\n\r\n    it('should update network context', () => {\r\n      const networkContext = { subnet: '192.168.1.0/24', vlan: 'DMZ' };\r\n      enrichedEvent.updateNetworkContext(networkContext);\r\n\r\n      expect(enrichedEvent.networkContext).toEqual(networkContext);\r\n    });\r\n\r\n    it('should update geolocation context', () => {\r\n      const geoContext = { country: 'US', city: 'San Francisco' };\r\n      enrichedEvent.updateGeolocationContext(geoContext);\r\n\r\n      expect(enrichedEvent.geolocationContext).toEqual(geoContext);\r\n    });\r\n\r\n    it('should add reputation score', () => {\r\n      enrichedEvent.addReputationScore('virustotal', 85);\r\n      enrichedEvent.addReputationScore('shodan', 70);\r\n\r\n      const reputationScores = enrichedEvent.reputationScores;\r\n      expect(reputationScores.virustotal).toBe(85);\r\n      expect(reputationScores.shodan).toBe(70);\r\n    });\r\n\r\n    it('should throw error for invalid reputation score', () => {\r\n      expect(() => enrichedEvent.addReputationScore('test', 150)).toThrow(\r\n        'Reputation score must be between 0 and 100'\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('scoring and quality', () => {\r\n    let enrichedEvent: EnrichedEvent;\r\n\r\n    beforeEach(() => {\r\n      enrichedEvent = EnrichedEvent.create(validProps);\r\n    });\r\n\r\n    it('should update threat intelligence score', () => {\r\n      enrichedEvent.updateThreatIntelScore(75);\r\n      expect(enrichedEvent.threatIntelScore).toBe(75);\r\n    });\r\n\r\n    it('should throw error for invalid threat intelligence score', () => {\r\n      expect(() => enrichedEvent.updateThreatIntelScore(-5)).toThrow(\r\n        'Threat intelligence score must be between 0 and 100'\r\n      );\r\n    });\r\n\r\n    it('should update enrichment quality score', () => {\r\n      enrichedEvent.updateEnrichmentQualityScore(90);\r\n      expect(enrichedEvent.enrichmentQualityScore).toBe(90);\r\n    });\r\n\r\n    it('should throw error for invalid enrichment quality score', () => {\r\n      expect(() => enrichedEvent.updateEnrichmentQualityScore(105)).toThrow(\r\n        'Enrichment quality score must be between 0 and 100'\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('manual review', () => {\r\n    let enrichedEvent: EnrichedEvent;\r\n\r\n    beforeEach(() => {\r\n      enrichedEvent = EnrichedEvent.create(validProps);\r\n    });\r\n\r\n    it('should mark for manual review', () => {\r\n      const reason = 'High threat intelligence score detected';\r\n      enrichedEvent.markForManualReview(reason);\r\n\r\n      expect(enrichedEvent.requiresManualReview).toBe(true);\r\n      expect(enrichedEvent.reviewNotes).toBe(reason);\r\n    });\r\n\r\n    it('should complete manual review', () => {\r\n      enrichedEvent.markForManualReview('Test reason');\r\n      \r\n      const reviewer = '<EMAIL>';\r\n      const notes = 'Reviewed and approved';\r\n      enrichedEvent.completeManualReview(reviewer, notes);\r\n\r\n      expect(enrichedEvent.reviewedBy).toBe(reviewer);\r\n      expect(enrichedEvent.reviewedAt).toBeDefined();\r\n      expect(enrichedEvent.reviewNotes).toBe(notes);\r\n    });\r\n\r\n    it('should throw error when completing review if not marked for review', () => {\r\n      expect(() => enrichedEvent.completeManualReview('<EMAIL>')).toThrow(\r\n        'Event is not marked for manual review'\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('validation errors', () => {\r\n    let enrichedEvent: EnrichedEvent;\r\n\r\n    beforeEach(() => {\r\n      enrichedEvent = EnrichedEvent.create(validProps);\r\n    });\r\n\r\n    it('should add validation errors', () => {\r\n      const errors = ['Error 1', 'Error 2'];\r\n      enrichedEvent.addValidationErrors(errors);\r\n\r\n      expect(enrichedEvent.validationErrors).toEqual(errors);\r\n      expect(enrichedEvent.hasValidationErrors()).toBe(true);\r\n    });\r\n\r\n    it('should clear validation errors', () => {\r\n      enrichedEvent.addValidationErrors(['Error 1']);\r\n      enrichedEvent.clearValidationErrors();\r\n\r\n      expect(enrichedEvent.validationErrors).toEqual([]);\r\n      expect(enrichedEvent.hasValidationErrors()).toBe(false);\r\n    });\r\n\r\n    it('should throw error for too many validation errors', () => {\r\n      const manyErrors = Array.from({ length: 11 }, (_, i) => `Error ${i + 1}`);\r\n\r\n      expect(() => enrichedEvent.addValidationErrors(manyErrors)).toThrow(\r\n        'Cannot have more than 10 validation errors'\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('query methods', () => {\r\n    let enrichedEvent: EnrichedEvent;\r\n\r\n    beforeEach(() => {\r\n      enrichedEvent = EnrichedEvent.create(validProps);\r\n    });\r\n\r\n    it('should check enrichment completion status', () => {\r\n      expect(enrichedEvent.isEnrichmentCompleted()).toBe(true);\r\n      expect(enrichedEvent.isEnrichmentFailed()).toBe(false);\r\n      expect(enrichedEvent.isEnrichmentInProgress()).toBe(false);\r\n      expect(enrichedEvent.isEnrichmentPartial()).toBe(false);\r\n      expect(enrichedEvent.isEnrichmentSkipped()).toBe(false);\r\n    });\r\n\r\n    it('should check enrichment quality', () => {\r\n      expect(enrichedEvent.hasHighEnrichmentQuality()).toBe(true);\r\n    });\r\n\r\n    it('should check threat intelligence risk', () => {\r\n      expect(enrichedEvent.isHighThreatRisk()).toBe(false);\r\n      \r\n      enrichedEvent.updateThreatIntelScore(90);\r\n      expect(enrichedEvent.isHighThreatRisk()).toBe(true);\r\n    });\r\n\r\n    it('should check data availability', () => {\r\n      expect(enrichedEvent.hasThreatIntelligence()).toBe(false);\r\n      expect(enrichedEvent.hasReputationData()).toBe(false);\r\n      expect(enrichedEvent.hasGeolocationData()).toBe(false);\r\n\r\n      enrichedEvent.addReputationScore('test', 75);\r\n      expect(enrichedEvent.hasReputationData()).toBe(true);\r\n    });\r\n\r\n    it('should check readiness for next stage', () => {\r\n      expect(enrichedEvent.isReadyForNextStage()).toBe(true);\r\n\r\n      enrichedEvent.addValidationErrors(['Test error']);\r\n      expect(enrichedEvent.isReadyForNextStage()).toBe(false);\r\n    });\r\n\r\n    it('should get enrichment duration', () => {\r\n      const propsWithTiming = {\r\n        ...validProps,\r\n        enrichmentStartedAt: new Date(Date.now() - 5000),\r\n        enrichmentCompletedAt: new Date(),\r\n      };\r\n      \r\n      const timedEvent = EnrichedEvent.create(propsWithTiming);\r\n      const duration = timedEvent.getEnrichmentDuration();\r\n      \r\n      expect(duration).toBeGreaterThan(4000);\r\n      expect(duration).toBeLessThan(6000);\r\n    });\r\n\r\n    it('should get applied rule names', () => {\r\n      const ruleNames = enrichedEvent.getAppliedRuleNames();\r\n      expect(ruleNames).toEqual(['Test Enrichment Rule']);\r\n    });\r\n\r\n    it('should check if specific rule was applied', () => {\r\n      expect(enrichedEvent.hasAppliedRule('test-rule-001')).toBe(true);\r\n      expect(enrichedEvent.hasAppliedRule('non-existent-rule')).toBe(false);\r\n    });\r\n\r\n    it('should get enrichment data by source', () => {\r\n      const ipReputationData = enrichedEvent.getEnrichmentDataBySource(EnrichmentSource.IP_REPUTATION);\r\n      expect(ipReputationData).toHaveLength(1);\r\n      expect(ipReputationData[0].type).toBe('reputation');\r\n\r\n      const geoData = enrichedEvent.getEnrichmentDataBySource(EnrichmentSource.IP_GEOLOCATION);\r\n      expect(geoData).toHaveLength(0);\r\n    });\r\n\r\n    it('should get enrichment data by type', () => {\r\n      const reputationData = enrichedEvent.getEnrichmentDataByType('reputation');\r\n      expect(reputationData).toHaveLength(1);\r\n      expect(reputationData[0].source).toBe(EnrichmentSource.IP_REPUTATION);\r\n\r\n      const geoData = enrichedEvent.getEnrichmentDataByType('geolocation');\r\n      expect(geoData).toHaveLength(0);\r\n    });\r\n\r\n    it('should get average reputation score', () => {\r\n      expect(enrichedEvent.getAverageReputationScore()).toBeNull();\r\n\r\n      enrichedEvent.addReputationScore('source1', 80);\r\n      enrichedEvent.addReputationScore('source2', 60);\r\n      \r\n      expect(enrichedEvent.getAverageReputationScore()).toBe(70);\r\n    });\r\n\r\n    it('should check max attempts exceeded', () => {\r\n      expect(enrichedEvent.hasExceededMaxEnrichmentAttempts()).toBe(false);\r\n\r\n      // Simulate multiple failed attempts\r\n      const propsWithAttempts = { ...validProps, enrichmentAttempts: 3 };\r\n      const eventWithAttempts = EnrichedEvent.create(propsWithAttempts);\r\n      \r\n      expect(eventWithAttempts.hasExceededMaxEnrichmentAttempts()).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('summary and serialization', () => {\r\n    let enrichedEvent: EnrichedEvent;\r\n\r\n    beforeEach(() => {\r\n      enrichedEvent = EnrichedEvent.create(validProps);\r\n    });\r\n\r\n    it('should generate event summary', () => {\r\n      const summary = enrichedEvent.getSummary();\r\n\r\n      expect(summary).toMatchObject({\r\n        id: enrichedEvent.id.toString(),\r\n        normalizedEventId: validProps.normalizedEventId.toString(),\r\n        title: 'Test Enriched Event',\r\n        type: EventType.THREAT_DETECTED,\r\n        severity: EventSeverity.MEDIUM,\r\n        status: EventStatus.ACTIVE,\r\n        enrichmentStatus: EnrichmentStatus.COMPLETED,\r\n        enrichmentQualityScore: 85,\r\n        threatIntelScore: 45,\r\n        appliedRulesCount: 1,\r\n        enrichmentDataCount: 1,\r\n        hasValidationErrors: false,\r\n        requiresManualReview: false,\r\n        isReadyForNextStage: true,\r\n        hasThreatIntelligence: false,\r\n        hasReputationData: false,\r\n      });\r\n    });\r\n\r\n    it('should serialize to JSON', () => {\r\n      const json = enrichedEvent.toJSON();\r\n\r\n      expect(json).toHaveProperty('id');\r\n      expect(json).toHaveProperty('normalizedEventId');\r\n      expect(json).toHaveProperty('metadata');\r\n      expect(json).toHaveProperty('enrichedData');\r\n      expect(json).toHaveProperty('appliedRules');\r\n      expect(json).toHaveProperty('enrichmentData');\r\n      expect(json).toHaveProperty('summary');\r\n      expect(json.type).toBe(EventType.THREAT_DETECTED);\r\n      expect(json.enrichmentStatus).toBe(EnrichmentStatus.COMPLETED);\r\n    });\r\n  });\r\n\r\n  describe('immutability', () => {\r\n    let enrichedEvent: EnrichedEvent;\r\n\r\n    beforeEach(() => {\r\n      enrichedEvent = EnrichedEvent.create(validProps);\r\n    });\r\n\r\n    it('should return copies of mutable properties', () => {\r\n      const normalizedData = enrichedEvent.normalizedData;\r\n      const enrichedData = enrichedEvent.enrichedData;\r\n      const tags = enrichedEvent.tags;\r\n      const attributes = enrichedEvent.attributes;\r\n      const appliedRules = enrichedEvent.appliedRules;\r\n      const enrichmentData = enrichedEvent.enrichmentData;\r\n\r\n      // Modify returned objects\r\n      normalizedData.modified = true;\r\n      enrichedData.modified = true;\r\n      tags.push('modified');\r\n      attributes.modified = true;\r\n      appliedRules.push({} as any);\r\n      enrichmentData.push({} as any);\r\n\r\n      // Original should be unchanged\r\n      expect(enrichedEvent.normalizedData.modified).toBeUndefined();\r\n      expect(enrichedEvent.enrichedData.modified).toBeUndefined();\r\n      expect(enrichedEvent.tags).not.toContain('modified');\r\n      expect(enrichedEvent.attributes.modified).toBeUndefined();\r\n      expect(enrichedEvent.appliedRules).toHaveLength(1);\r\n      expect(enrichedEvent.enrichmentData).toHaveLength(1);\r\n    });\r\n  });\r\n});"], "version": 3}