{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\repositories\\security-event.repository.interface.ts", "mappings": "", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\core\\security\\domain\\interfaces\\repositories\\security-event.repository.interface.ts"], "sourcesContent": ["import { AggregateRepository } from '../../../../shared-kernel/domain/base-repository.interface';\r\nimport { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { PaginatedResult, QueryOptions } from '../../../../shared-kernel/types/pagination.types';\r\nimport { SecurityEvent } from '../../entities/event/security-event.entity';\r\nimport { EventProcessingStatus } from '../../enums/event-processing-status.enum';\r\nimport { EventSourceType } from '../../enums/event-source-type.enum';\r\n\r\n/**\r\n * Security Event Query Filters\r\n */\r\nexport interface SecurityEventFilters {\r\n  /** Filter by processing status */\r\n  status?: EventProcessingStatus | EventProcessingStatus[];\r\n  /** Filter by source type */\r\n  sourceType?: EventSourceType | EventSourceType[];\r\n  /** Filter by source identifier */\r\n  sourceIdentifier?: string;\r\n  /** Filter by event category */\r\n  category?: string | string[];\r\n  /** Filter by event subcategory */\r\n  subcategory?: string | string[];\r\n  /** Filter by tags */\r\n  tags?: string | string[];\r\n  /** Filter by date range */\r\n  dateRange?: {\r\n    from?: Date;\r\n    to?: Date;\r\n  };\r\n  /** Filter by processing attempts */\r\n  processingAttempts?: {\r\n    min?: number;\r\n    max?: number;\r\n  };\r\n  /** Filter by processing duration */\r\n  processingDuration?: {\r\n    min?: number;\r\n    max?: number;\r\n  };\r\n  /** Filter by age in milliseconds */\r\n  age?: {\r\n    min?: number;\r\n    max?: number;\r\n  };\r\n  /** Filter events that require attention */\r\n  requiresAttention?: boolean;\r\n  /** Filter events that are in progress */\r\n  inProgress?: boolean;\r\n  /** Filter events that are terminal */\r\n  terminal?: boolean;\r\n  /** Filter events that can be retried */\r\n  canRetry?: boolean;\r\n  /** Full-text search in title and description */\r\n  search?: string;\r\n}\r\n\r\n/**\r\n * Security Event Statistics\r\n */\r\nexport interface SecurityEventStatistics {\r\n  /** Total number of events */\r\n  total: number;\r\n  /** Events by status */\r\n  byStatus: Record<EventProcessingStatus, number>;\r\n  /** Events by source type */\r\n  bySourceType: Record<EventSourceType, number>;\r\n  /** Events by category */\r\n  byCategory: Record<string, number>;\r\n  /** Events requiring attention */\r\n  requiresAttention: number;\r\n  /** Events in progress */\r\n  inProgress: number;\r\n  /** Events that failed */\r\n  failed: number;\r\n  /** Events that can be retried */\r\n  canRetry: number;\r\n  /** Average processing time */\r\n  averageProcessingTime: number;\r\n  /** Events created in last 24 hours */\r\n  createdLast24Hours: number;\r\n  /** Events processed in last hour */\r\n  processedLastHour: number;\r\n}\r\n\r\n/**\r\n * Security Event Processing Metrics\r\n */\r\nexport interface SecurityEventProcessingMetrics {\r\n  /** Processing throughput (events per minute) */\r\n  throughput: number;\r\n  /** Average processing duration by status */\r\n  averageDurationByStatus: Record<EventProcessingStatus, number>;\r\n  /** Success rate percentage */\r\n  successRate: number;\r\n  /** Failure rate percentage */\r\n  failureRate: number;\r\n  /** Retry rate percentage */\r\n  retryRate: number;\r\n  /** SLA compliance percentage */\r\n  slaCompliance: number;\r\n  /** Top error messages */\r\n  topErrors: Array<{ message: string; count: number }>;\r\n  /** Processing bottlenecks */\r\n  bottlenecks: Array<{ status: EventProcessingStatus; avgDuration: number; count: number }>;\r\n}\r\n\r\n/**\r\n * Security Event Repository Interface\r\n * \r\n * Provides data access operations for security events with specialized queries\r\n * for security operations, monitoring, and analytics.\r\n * \r\n * Key capabilities:\r\n * - Event lifecycle management\r\n * - Advanced filtering and search\r\n * - Performance metrics and statistics\r\n * - Bulk operations for processing\r\n * - Real-time monitoring support\r\n * - SLA and compliance tracking\r\n */\r\nexport interface SecurityEventRepository extends AggregateRepository<SecurityEvent, UniqueEntityId> {\r\n  /**\r\n   * Find events by filters with pagination\r\n   */\r\n  findByFilters(\r\n    filters: SecurityEventFilters,\r\n    options?: QueryOptions\r\n  ): Promise<PaginatedResult<SecurityEvent>>;\r\n\r\n  /**\r\n   * Find events by status\r\n   */\r\n  findByStatus(\r\n    status: EventProcessingStatus | EventProcessingStatus[],\r\n    options?: QueryOptions\r\n  ): Promise<PaginatedResult<SecurityEvent>>;\r\n\r\n  /**\r\n   * Find events by source type\r\n   */\r\n  findBySourceType(\r\n    sourceType: EventSourceType | EventSourceType[],\r\n    options?: QueryOptions\r\n  ): Promise<PaginatedResult<SecurityEvent>>;\r\n\r\n  /**\r\n   * Find events by source identifier\r\n   */\r\n  findBySourceIdentifier(\r\n    sourceIdentifier: string,\r\n    options?: QueryOptions\r\n  ): Promise<PaginatedResult<SecurityEvent>>;\r\n\r\n  /**\r\n   * Find events that require attention\r\n   */\r\n  findRequiringAttention(options?: QueryOptions): Promise<PaginatedResult<SecurityEvent>>;\r\n\r\n  /**\r\n   * Find events that are in progress\r\n   */\r\n  findInProgress(options?: QueryOptions): Promise<PaginatedResult<SecurityEvent>>;\r\n\r\n  /**\r\n   * Find events that can be retried\r\n   */\r\n  findRetryable(options?: QueryOptions): Promise<PaginatedResult<SecurityEvent>>;\r\n\r\n  /**\r\n   * Find events by date range\r\n   */\r\n  findByDateRange(\r\n    from: Date,\r\n    to: Date,\r\n    options?: QueryOptions\r\n  ): Promise<PaginatedResult<SecurityEvent>>;\r\n\r\n  /**\r\n   * Find events by tags\r\n   */\r\n  findByTags(\r\n    tags: string | string[],\r\n    matchAll?: boolean,\r\n    options?: QueryOptions\r\n  ): Promise<PaginatedResult<SecurityEvent>>;\r\n\r\n  /**\r\n   * Search events by text\r\n   */\r\n  searchEvents(\r\n    query: string,\r\n    options?: QueryOptions\r\n  ): Promise<PaginatedResult<SecurityEvent>>;\r\n\r\n  /**\r\n   * Find stale events (older than specified time)\r\n   */\r\n  findStaleEvents(\r\n    olderThanMs: number,\r\n    options?: QueryOptions\r\n  ): Promise<PaginatedResult<SecurityEvent>>;\r\n\r\n  /**\r\n   * Find events with processing errors\r\n   */\r\n  findWithErrors(options?: QueryOptions): Promise<PaginatedResult<SecurityEvent>>;\r\n\r\n  /**\r\n   * Get event statistics\r\n   */\r\n  getStatistics(filters?: SecurityEventFilters): Promise<SecurityEventStatistics>;\r\n\r\n  /**\r\n   * Get processing metrics\r\n   */\r\n  getProcessingMetrics(\r\n    timeRange?: { from: Date; to: Date }\r\n  ): Promise<SecurityEventProcessingMetrics>;\r\n\r\n  /**\r\n   * Get events count by status\r\n   */\r\n  countByStatus(): Promise<Record<EventProcessingStatus, number>>;\r\n\r\n  /**\r\n   * Get events count by source type\r\n   */\r\n  countBySourceType(): Promise<Record<EventSourceType, number>>;\r\n\r\n  /**\r\n   * Get events count by category\r\n   */\r\n  countByCategory(): Promise<Record<string, number>>;\r\n\r\n  /**\r\n   * Get recent events (last N events)\r\n   */\r\n  getRecentEvents(limit: number): Promise<SecurityEvent[]>;\r\n\r\n  /**\r\n   * Get oldest unprocessed events\r\n   */\r\n  getOldestUnprocessed(limit: number): Promise<SecurityEvent[]>;\r\n\r\n  /**\r\n   * Get events for processing (by priority and age)\r\n   */\r\n  getEventsForProcessing(\r\n    status: EventProcessingStatus,\r\n    limit: number\r\n  ): Promise<SecurityEvent[]>;\r\n\r\n  /**\r\n   * Bulk update event status\r\n   */\r\n  bulkUpdateStatus(\r\n    eventIds: UniqueEntityId[],\r\n    newStatus: EventProcessingStatus,\r\n    errorMessage?: string\r\n  ): Promise<number>;\r\n\r\n  /**\r\n   * Bulk add tags to events\r\n   */\r\n  bulkAddTags(eventIds: UniqueEntityId[], tags: string[]): Promise<number>;\r\n\r\n  /**\r\n   * Bulk remove tags from events\r\n   */\r\n  bulkRemoveTags(eventIds: UniqueEntityId[], tags: string[]): Promise<number>;\r\n\r\n  /**\r\n   * Archive old events\r\n   */\r\n  archiveOldEvents(olderThanDays: number): Promise<number>;\r\n\r\n  /**\r\n   * Delete archived events\r\n   */\r\n  deleteArchivedEvents(olderThanDays: number): Promise<number>;\r\n\r\n  /**\r\n   * Get processing queue depth by status\r\n   */\r\n  getQueueDepth(): Promise<Record<EventProcessingStatus, number>>;\r\n\r\n  /**\r\n   * Get average processing time by status\r\n   */\r\n  getAverageProcessingTime(): Promise<Record<EventProcessingStatus, number>>;\r\n\r\n  /**\r\n   * Get SLA compliance metrics\r\n   */\r\n  getSLACompliance(\r\n    timeRange?: { from: Date; to: Date }\r\n  ): Promise<{\r\n    overall: number;\r\n    byStatus: Record<EventProcessingStatus, number>;\r\n    bySourceType: Record<EventSourceType, number>;\r\n    breaches: Array<{\r\n      eventId: string;\r\n      status: EventProcessingStatus;\r\n      duration: number;\r\n      threshold: number;\r\n    }>;\r\n  }>;\r\n\r\n  /**\r\n   * Get top error messages\r\n   */\r\n  getTopErrors(\r\n    limit?: number,\r\n    timeRange?: { from: Date; to: Date }\r\n  ): Promise<Array<{ message: string; count: number; lastOccurred: Date }>>;\r\n\r\n  /**\r\n   * Get processing bottlenecks\r\n   */\r\n  getProcessingBottlenecks(): Promise<Array<{\r\n    status: EventProcessingStatus;\r\n    averageDuration: number;\r\n    eventCount: number;\r\n    percentile95Duration: number;\r\n  }>>;\r\n\r\n  /**\r\n   * Get events timeline data\r\n   */\r\n  getEventsTimeline(\r\n    timeRange: { from: Date; to: Date },\r\n    granularity: 'minute' | 'hour' | 'day'\r\n  ): Promise<Array<{\r\n    timestamp: Date;\r\n    created: number;\r\n    processed: number;\r\n    failed: number;\r\n    resolved: number;\r\n  }>>;\r\n\r\n  /**\r\n   * Get correlation candidates\r\n   */\r\n  getCorrelationCandidates(\r\n    event: SecurityEvent,\r\n    timeWindow: number,\r\n    limit?: number\r\n  ): Promise<SecurityEvent[]>;\r\n\r\n  /**\r\n   * Find similar events\r\n   */\r\n  findSimilarEvents(\r\n    event: SecurityEvent,\r\n    similarity: {\r\n      sourceType?: boolean;\r\n      category?: boolean;\r\n      tags?: boolean;\r\n      timeWindow?: number;\r\n    },\r\n    limit?: number\r\n  ): Promise<SecurityEvent[]>;\r\n\r\n  /**\r\n   * Get event processing history\r\n   */\r\n  getProcessingHistory(\r\n    eventId: UniqueEntityId\r\n  ): Promise<Array<{\r\n    status: EventProcessingStatus;\r\n    timestamp: Date;\r\n    duration?: number;\r\n    errorMessage?: string;\r\n  }>>;\r\n\r\n  /**\r\n   * Check if event exists by external ID\r\n   */\r\n  existsByExternalId(externalId: string, sourceIdentifier: string): Promise<boolean>;\r\n\r\n  /**\r\n   * Find event by external ID\r\n   */\r\n  findByExternalId(externalId: string, sourceIdentifier: string): Promise<SecurityEvent | null>;\r\n\r\n  /**\r\n   * Get duplicate events\r\n   */\r\n  findDuplicates(\r\n    event: SecurityEvent,\r\n    timeWindow: number\r\n  ): Promise<SecurityEvent[]>;\r\n\r\n  /**\r\n   * Get events requiring cleanup\r\n   */\r\n  getEventsRequiringCleanup(): Promise<{\r\n    stale: SecurityEvent[];\r\n    failed: SecurityEvent[];\r\n    orphaned: SecurityEvent[];\r\n  }>;\r\n}\r\n"], "version": 3}