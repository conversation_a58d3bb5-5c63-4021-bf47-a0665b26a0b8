{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\__tests__\\e2e\\health-monitoring-workflow.e2e-spec.ts", "mappings": ";;;;;AAAA,6CAAsD;AAEtD,2CAA8C;AAC9C,0DAAgC;AAChC,iDAA6C;AAE7C,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;IAChD,IAAI,GAAqB,CAAC;IAE1B,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,aAAa,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAClE,OAAO,EAAE;gBACP,qBAAY,CAAC,OAAO,CAAC;oBACnB,QAAQ,EAAE,IAAI;oBACd,WAAW,EAAE,WAAW;iBACzB,CAAC;gBACF,sBAAS;aACV;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,GAAG,GAAG,aAAa,CAAC,qBAAqB,EAAE,CAAC;QAC5C,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC;IACnB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,SAAS,CAAC;iBACd,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,MAAM,EAAE,IAAI;gBACZ,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC1B,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC3B,WAAW,EAAE,MAAM;aACpB,CAAC,CAAC;YAEH,4BAA4B;YAC5B,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAEtF,8BAA8B;YAC9B,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,SAAS,CAAC;iBACd,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YACtE,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,SAAS,CAAC;iBACd,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC3B,MAAM,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAC;YAErC,+BAA+B;YAC/B,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,kBAAkB,CAAC;iBACvB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,MAAM,EAAE,IAAI;gBACZ,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC1B,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC3B,WAAW,EAAE,MAAM;gBACnB,QAAQ,EAAE;oBACR,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC5B,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACzB,aAAa,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAClC;gBACD,MAAM,EAAE;oBACN,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACxB,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACzB,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC/B;gBACD,GAAG,EAAE;oBACH,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACzB,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;iBAC/B;gBACD,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACxB,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACzB,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC/B;aACF,CAAC,CAAC;YAEH,4BAA4B;YAC5B,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACxF,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACrF,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;YAE7F,0BAA0B;YAC1B,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACrD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC9E,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAClE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;YAEjE,uBAAuB;YACvB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAC1D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;YACzD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC1B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,YAAY,EAAE;oBACZ,QAAQ,EAAE;wBACR,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC1B,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAChC,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC/B,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;qBAC5B;oBACD,KAAK,EAAE;wBACL,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC1B,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAChC,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC/B,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;qBAC5B;oBACD,UAAU,EAAE;wBACV,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC1B,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAChC,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC/B,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;qBAC5B;oBACD,qBAAqB,EAAE;wBACrB,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC1B,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAChC,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC/B,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;qBAC5B;iBACF;aACF,CAAC,CAAC;YAEH,+BAA+B;YAC/B,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,GAAQ,EAAE,EAAE;gBAC7D,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBACnE,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;gBACnD,MAAM,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YACxE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,eAAe,CAAC;iBACpB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,MAAM,EAAE,OAAO;gBACf,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,MAAM,EAAE;oBACN,mBAAmB,EAAE,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC;oBACxC,gBAAgB,EAAE,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC;oBACrC,oBAAoB,EAAE,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC;oBACzC,kBAAkB,EAAE,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC;iBACxC;aACF,CAAC,CAAC;YAEH,uDAAuD;YACvD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,mDAAmD;YACnD,+DAA+D;YAC/D,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,eAAe,CAAC,CAAC;YAExB,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAE9C,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;gBAC5B,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAC/C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7C,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,cAAc,CAAC;iBACnB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,MAAM,EAAE,OAAO;gBACf,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC1B,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBACvB,MAAM,EAAE;oBACN,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC5B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC7B,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC5B,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBACxB;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAChD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACzD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,cAAc,CAAC;iBACnB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC3B,MAAM,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAC;YAErC,sCAAsC;YACtC,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,UAAU,CAAC;iBACf,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,WAAW,EAAE;oBACX,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC1B,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC3B,WAAW,EAAE,MAAM;oBACnB,QAAQ,EAAE;wBACR,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBACzB,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBACxB,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC1B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;qBAC9B;iBACF;gBACD,MAAM,EAAE;oBACN,MAAM,EAAE;wBACN,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBACxB,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBACzB,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;qBAC/B;oBACD,GAAG,EAAE;wBACH,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBACzB,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;qBAC/B;oBACD,IAAI,EAAE;wBACJ,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBACxB,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBACzB,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;qBAC/B;iBACF;gBACD,QAAQ,EAAE;oBACR,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAChC,wBAAwB,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC5C,gBAAgB,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACpC,eAAe,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBACpC;aACF,CAAC,CAAC;YAEH,0BAA0B;YAC1B,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAC3E,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAC1E,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAC5E,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAC/E,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,qBAAqB,CAAC;iBAC1B,GAAG,CAAC,QAAQ,EAAE,YAAY,CAAC;iBAC3B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YAChE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAC1C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAC1C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;YACvD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;YAC7D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;YAC3C,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,YAAY,EAAE;oBACZ,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACvB,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACvB,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACvB,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC3B,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACvB,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBACxB;gBACD,UAAU,EAAE;oBACV,iBAAiB,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACrC,iBAAiB,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBACtC;gBACD,MAAM,EAAE;oBACN,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACxB,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACzB,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC1B;aACF,CAAC,CAAC;YAEH,+BAA+B;YAC/B,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC1D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,sBAAsB,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;YAC9F,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,sBAAsB,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;YAC9F,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,iBAAiB,CAAC;iBACtB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,OAAO,EAAE;oBACP,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC5B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC7B,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC5B,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBACxB;gBACD,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;gBAC1B,MAAM,EAAE;oBACN,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAClC,kBAAkB,EAAE,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC;iBACxC;aACF,CAAC,CAAC;YAEH,0BAA0B;YAC1B,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC1D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACxF,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,kBAAkB,CAAC;iBACvB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC1B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,QAAQ,EAAE;oBACR,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC9B,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAChC,iBAAiB,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACrC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAClC,OAAO,EAAE;wBACP,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBACzB,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBACxB,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;qBAC3B;iBACF;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC7E,MAAM,CAAC,CAAC,WAAW,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YACjG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC/D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAC3E,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,KAAK,IAAI,EAAE;YACzC,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,eAAe,CAAC;iBACpB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC1B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,KAAK,EAAE;oBACL,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC9B,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAChC,MAAM,EAAE;wBACN,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBACxB,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;qBACzB;oBACD,QAAQ,EAAE;wBACR,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBACxB,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;qBAC5B;iBACF;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC7E,MAAM,CAAC,CAAC,WAAW,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAChG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,uBAAuB,CAAC;iBAC5B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC1B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,IAAI,EAAE;oBACJ,mBAAmB,EAAE;wBACnB,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC1B,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAChC,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC/B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;qBAC9B;oBACD,qBAAqB,EAAE;wBACrB,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC1B,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAChC,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC/B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;qBAC9B;oBACD,UAAU,EAAE;wBACV,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC1B,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAChC,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC/B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;qBAC9B;iBACF;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,GAAQ,EAAE,EAAE;gBACrD,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBACnE,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;gBACnD,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;gBAChD,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,mBAAmB,CAAC;iBACxB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAC5B,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC1B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,OAAO,EAAE;oBACP,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC3B,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC5B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC7B,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC1B;gBACD,UAAU,EAAE;oBACV,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC/B,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC5B,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACzB,aAAa,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACjC,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC3B;gBACD,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC7E,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CACtC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO;gBAC7B,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ;gBAC9B,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAChC,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,mBAAmB,CAAC;iBACxB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,6DAA6D;YAC7D,gFAAgF;YAChF,2DAA2D;YAE3D,MAAM,iBAAiB,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAClE,MAAM,YAAY,GAAG,iBAAiB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YAC7D,MAAM,WAAW,GAAG,iBAAiB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAE3D,IAAI,YAAY,EAAE,CAAC;gBACjB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACjD,CAAC;iBAAM,IAAI,WAAW,EAAE,CAAC;gBACvB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAChD,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,iCAAiC;YACjC,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAClC,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC,GAAG,CAAC,kBAAkB,CAAC;gBACpD,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC,GAAG,CAAC,kBAAkB,CAAC;gBACpD,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC,GAAG,CAAC,kBAAkB,CAAC;aACrD,CAAC,CAAC;YAEH,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAC3B,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;YAEH,uDAAuD;YACvD,MAAM,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;YAC5E,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC;YAClE,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,kBAAkB;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,+CAA+C;YAC/C,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,OAAO,CAAC,IAAI,CAAC,CAAC;YAEjB,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,qBAAqB,CAAC;iBAC1B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAC/C,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAC5C,CAAC;YAEF,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAE9C,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAC3B,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAC/C,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC,GAAG,CAAC,kBAAkB,CAAC,CACrD,CAAC;YAEF,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAE5B,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC3B,MAAM,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAC;YAErC,gDAAgD;YAChD,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\__tests__\\e2e\\health-monitoring-workflow.e2e-spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { INestApplication } from '@nestjs/common';\r\nimport { ConfigModule } from '@nestjs/config';\r\nimport request from 'supertest';\r\nimport { AppModule } from '../../app.module';\r\n\r\ndescribe('Health Monitoring Workflow (e2e)', () => {\r\n  let app: INestApplication;\r\n\r\n  beforeAll(async () => {\r\n    const moduleFixture: TestingModule = await Test.createTestingModule({\r\n      imports: [\r\n        ConfigModule.forRoot({\r\n          isGlobal: true,\r\n          envFilePath: '.env.test',\r\n        }),\r\n        AppModule,\r\n      ],\r\n    }).compile();\r\n\r\n    app = moduleFixture.createNestApplication();\r\n    await app.init();\r\n  });\r\n\r\n  afterAll(async () => {\r\n    await app.close();\r\n  });\r\n\r\n  describe('Basic Health Checks', () => {\r\n    it('should return basic health status', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/health')\r\n        .expect(200);\r\n\r\n      expect(response.body).toEqual({\r\n        status: 'ok',\r\n        timestamp: expect.any(String),\r\n        uptime: expect.any(Number),\r\n        version: expect.any(String),\r\n        environment: 'test',\r\n      });\r\n\r\n      // Validate timestamp format\r\n      expect(new Date(response.body.timestamp).toISOString()).toBe(response.body.timestamp);\r\n      \r\n      // Validate uptime is positive\r\n      expect(response.body.uptime).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should return health status with proper headers', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/health')\r\n        .expect(200);\r\n\r\n      expect(response.headers['content-type']).toMatch(/application\\/json/);\r\n      expect(response.headers['cache-control']).toBe('no-cache, no-store, must-revalidate');\r\n    });\r\n\r\n    it('should handle health check requests quickly', async () => {\r\n      const startTime = Date.now();\r\n      \r\n      await request(app.getHttpServer())\r\n        .get('/health')\r\n        .expect(200);\r\n      \r\n      const endTime = Date.now();\r\n      const duration = endTime - startTime;\r\n\r\n      // Health checks should be fast\r\n      expect(duration).toBeLessThan(500);\r\n    });\r\n  });\r\n\r\n  describe('Detailed Health Checks', () => {\r\n    it('should return detailed health information', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/health/detailed')\r\n        .expect(200);\r\n\r\n      expect(response.body).toEqual({\r\n        status: 'ok',\r\n        timestamp: expect.any(String),\r\n        uptime: expect.any(Number),\r\n        version: expect.any(String),\r\n        environment: 'test',\r\n        services: {\r\n          database: expect.any(String),\r\n          redis: expect.any(String),\r\n          external_apis: expect.any(String),\r\n        },\r\n        memory: {\r\n          used: expect.any(Number),\r\n          total: expect.any(Number),\r\n          percentage: expect.any(Number),\r\n        },\r\n        cpu: {\r\n          usage: expect.any(Number),\r\n          loadAverage: expect.any(Array),\r\n        },\r\n        disk: {\r\n          used: expect.any(Number),\r\n          total: expect.any(Number),\r\n          percentage: expect.any(Number),\r\n        },\r\n      });\r\n\r\n      // Validate service statuses\r\n      expect(['healthy', 'degraded', 'unhealthy']).toContain(response.body.services.database);\r\n      expect(['healthy', 'degraded', 'unhealthy']).toContain(response.body.services.redis);\r\n      expect(['healthy', 'degraded', 'unhealthy']).toContain(response.body.services.external_apis);\r\n\r\n      // Validate memory metrics\r\n      expect(response.body.memory.used).toBeGreaterThan(0);\r\n      expect(response.body.memory.total).toBeGreaterThan(response.body.memory.used);\r\n      expect(response.body.memory.percentage).toBeGreaterThanOrEqual(0);\r\n      expect(response.body.memory.percentage).toBeLessThanOrEqual(100);\r\n\r\n      // Validate CPU metrics\r\n      expect(response.body.cpu.usage).toBeGreaterThanOrEqual(0);\r\n      expect(response.body.cpu.usage).toBeLessThanOrEqual(100);\r\n      expect(Array.isArray(response.body.cpu.loadAverage)).toBe(true);\r\n    });\r\n\r\n    it('should include dependency health checks', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/health/dependencies')\r\n        .expect(200);\r\n\r\n      expect(response.body).toEqual({\r\n        status: expect.any(String),\r\n        timestamp: expect.any(String),\r\n        dependencies: {\r\n          database: {\r\n            status: expect.any(String),\r\n            responseTime: expect.any(Number),\r\n            lastChecked: expect.any(String),\r\n            details: expect.any(Object),\r\n          },\r\n          redis: {\r\n            status: expect.any(String),\r\n            responseTime: expect.any(Number),\r\n            lastChecked: expect.any(String),\r\n            details: expect.any(Object),\r\n          },\r\n          ai_service: {\r\n            status: expect.any(String),\r\n            responseTime: expect.any(Number),\r\n            lastChecked: expect.any(String),\r\n            details: expect.any(Object),\r\n          },\r\n          external_threat_intel: {\r\n            status: expect.any(String),\r\n            responseTime: expect.any(Number),\r\n            lastChecked: expect.any(String),\r\n            details: expect.any(Object),\r\n          },\r\n        },\r\n      });\r\n\r\n      // Validate dependency statuses\r\n      Object.values(response.body.dependencies).forEach((dep: any) => {\r\n        expect(['healthy', 'degraded', 'unhealthy']).toContain(dep.status);\r\n        expect(dep.responseTime).toBeGreaterThanOrEqual(0);\r\n        expect(new Date(dep.lastChecked).toISOString()).toBe(dep.lastChecked);\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Readiness Checks', () => {\r\n    it('should return readiness status', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/health/ready')\r\n        .expect(200);\r\n\r\n      expect(response.body).toEqual({\r\n        status: 'ready',\r\n        timestamp: expect.any(String),\r\n        checks: {\r\n          database_connection: expect.any(Boolean),\r\n          redis_connection: expect.any(Boolean),\r\n          configuration_loaded: expect.any(Boolean),\r\n          migrations_applied: expect.any(Boolean),\r\n        },\r\n      });\r\n\r\n      // All readiness checks should pass in test environment\r\n      expect(response.body.checks.database_connection).toBe(true);\r\n      expect(response.body.checks.redis_connection).toBe(true);\r\n      expect(response.body.checks.configuration_loaded).toBe(true);\r\n      expect(response.body.checks.migrations_applied).toBe(true);\r\n    });\r\n\r\n    it('should handle readiness check failures gracefully', async () => {\r\n      // This test would require mocking service failures\r\n      // For now, we'll test that the endpoint responds appropriately\r\n      const response = await request(app.getHttpServer())\r\n        .get('/health/ready');\r\n\r\n      expect([200, 503]).toContain(response.status);\r\n      \r\n      if (response.status === 503) {\r\n        expect(response.body.status).toBe('not_ready');\r\n        expect(response.body.checks).toBeDefined();\r\n      }\r\n    });\r\n  });\r\n\r\n  describe('Liveness Checks', () => {\r\n    it('should return liveness status', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/health/live')\r\n        .expect(200);\r\n\r\n      expect(response.body).toEqual({\r\n        status: 'alive',\r\n        timestamp: expect.any(String),\r\n        uptime: expect.any(Number),\r\n        pid: expect.any(Number),\r\n        memory: {\r\n          heapUsed: expect.any(Number),\r\n          heapTotal: expect.any(Number),\r\n          external: expect.any(Number),\r\n          rss: expect.any(Number),\r\n        },\r\n      });\r\n\r\n      expect(response.body.uptime).toBeGreaterThan(0);\r\n      expect(response.body.pid).toBeGreaterThan(0);\r\n      expect(response.body.memory.heapUsed).toBeGreaterThan(0);\r\n      expect(response.body.memory.heapTotal).toBeGreaterThan(response.body.memory.heapUsed);\r\n    });\r\n\r\n    it('should respond quickly to liveness checks', async () => {\r\n      const startTime = Date.now();\r\n      \r\n      await request(app.getHttpServer())\r\n        .get('/health/live')\r\n        .expect(200);\r\n      \r\n      const endTime = Date.now();\r\n      const duration = endTime - startTime;\r\n\r\n      // Liveness checks should be very fast\r\n      expect(duration).toBeLessThan(100);\r\n    });\r\n  });\r\n\r\n  describe('Metrics Endpoint', () => {\r\n    it('should return application metrics', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/metrics')\r\n        .expect(200);\r\n\r\n      expect(response.body).toEqual({\r\n        timestamp: expect.any(String),\r\n        application: {\r\n          uptime: expect.any(Number),\r\n          version: expect.any(String),\r\n          environment: 'test',\r\n          requests: {\r\n            total: expect.any(Number),\r\n            rate: expect.any(Number),\r\n            errors: expect.any(Number),\r\n            errorRate: expect.any(Number),\r\n          },\r\n        },\r\n        system: {\r\n          memory: {\r\n            used: expect.any(Number),\r\n            total: expect.any(Number),\r\n            percentage: expect.any(Number),\r\n          },\r\n          cpu: {\r\n            usage: expect.any(Number),\r\n            loadAverage: expect.any(Array),\r\n          },\r\n          disk: {\r\n            used: expect.any(Number),\r\n            total: expect.any(Number),\r\n            percentage: expect.any(Number),\r\n          },\r\n        },\r\n        business: {\r\n          active_users: expect.any(Number),\r\n          vulnerabilities_detected: expect.any(Number),\r\n          threats_analyzed: expect.any(Number),\r\n          security_events: expect.any(Number),\r\n        },\r\n      });\r\n\r\n      // Validate metrics ranges\r\n      expect(response.body.application.requests.total).toBeGreaterThanOrEqual(0);\r\n      expect(response.body.application.requests.rate).toBeGreaterThanOrEqual(0);\r\n      expect(response.body.application.requests.errors).toBeGreaterThanOrEqual(0);\r\n      expect(response.body.application.requests.errorRate).toBeGreaterThanOrEqual(0);\r\n      expect(response.body.application.requests.errorRate).toBeLessThanOrEqual(100);\r\n    });\r\n\r\n    it('should return Prometheus-formatted metrics', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/metrics/prometheus')\r\n        .set('Accept', 'text/plain')\r\n        .expect(200);\r\n\r\n      expect(response.headers['content-type']).toMatch(/text\\/plain/);\r\n      expect(response.text).toContain('# HELP');\r\n      expect(response.text).toContain('# TYPE');\r\n      expect(response.text).toContain('http_requests_total');\r\n      expect(response.text).toContain('process_cpu_seconds_total');\r\n      expect(response.text).toContain('nodejs_heap_size_used_bytes');\r\n    });\r\n  });\r\n\r\n  describe('Performance Monitoring', () => {\r\n    it('should track response times', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/metrics/performance')\r\n        .expect(200);\r\n\r\n      expect(response.body).toEqual({\r\n        timestamp: expect.any(String),\r\n        responseTime: {\r\n          p50: expect.any(Number),\r\n          p95: expect.any(Number),\r\n          p99: expect.any(Number),\r\n          average: expect.any(Number),\r\n          min: expect.any(Number),\r\n          max: expect.any(Number),\r\n        },\r\n        throughput: {\r\n          requestsPerSecond: expect.any(Number),\r\n          requestsPerMinute: expect.any(Number),\r\n        },\r\n        errors: {\r\n          rate: expect.any(Number),\r\n          count: expect.any(Number),\r\n          types: expect.any(Object),\r\n        },\r\n      });\r\n\r\n      // Validate performance metrics\r\n      expect(response.body.responseTime.p50).toBeGreaterThan(0);\r\n      expect(response.body.responseTime.p95).toBeGreaterThanOrEqual(response.body.responseTime.p50);\r\n      expect(response.body.responseTime.p99).toBeGreaterThanOrEqual(response.body.responseTime.p95);\r\n      expect(response.body.throughput.requestsPerSecond).toBeGreaterThanOrEqual(0);\r\n    });\r\n\r\n    it('should monitor memory usage over time', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/metrics/memory')\r\n        .expect(200);\r\n\r\n      expect(response.body).toEqual({\r\n        timestamp: expect.any(String),\r\n        current: {\r\n          heapUsed: expect.any(Number),\r\n          heapTotal: expect.any(Number),\r\n          external: expect.any(Number),\r\n          rss: expect.any(Number),\r\n        },\r\n        history: expect.any(Array),\r\n        trends: {\r\n          heapGrowthRate: expect.any(Number),\r\n          memoryLeakDetected: expect.any(Boolean),\r\n        },\r\n      });\r\n\r\n      // Validate memory metrics\r\n      expect(response.body.current.heapUsed).toBeGreaterThan(0);\r\n      expect(response.body.current.heapTotal).toBeGreaterThan(response.body.current.heapUsed);\r\n      expect(response.body.history.length).toBeGreaterThanOrEqual(0);\r\n    });\r\n  });\r\n\r\n  describe('Custom Health Indicators', () => {\r\n    it('should check database health', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/health/database')\r\n        .expect(200);\r\n\r\n      expect(response.body).toEqual({\r\n        status: expect.any(String),\r\n        timestamp: expect.any(String),\r\n        database: {\r\n          connection: expect.any(String),\r\n          responseTime: expect.any(Number),\r\n          activeConnections: expect.any(Number),\r\n          maxConnections: expect.any(Number),\r\n          queries: {\r\n            total: expect.any(Number),\r\n            slow: expect.any(Number),\r\n            failed: expect.any(Number),\r\n          },\r\n        },\r\n      });\r\n\r\n      expect(['healthy', 'degraded', 'unhealthy']).toContain(response.body.status);\r\n      expect(['connected', 'disconnected', 'connecting']).toContain(response.body.database.connection);\r\n      expect(response.body.database.responseTime).toBeGreaterThan(0);\r\n      expect(response.body.database.activeConnections).toBeGreaterThanOrEqual(0);\r\n      expect(response.body.database.maxConnections).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should check Redis health', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/health/redis')\r\n        .expect(200);\r\n\r\n      expect(response.body).toEqual({\r\n        status: expect.any(String),\r\n        timestamp: expect.any(String),\r\n        redis: {\r\n          connection: expect.any(String),\r\n          responseTime: expect.any(Number),\r\n          memory: {\r\n            used: expect.any(Number),\r\n            peak: expect.any(Number),\r\n          },\r\n          keyspace: {\r\n            keys: expect.any(Number),\r\n            expires: expect.any(Number),\r\n          },\r\n        },\r\n      });\r\n\r\n      expect(['healthy', 'degraded', 'unhealthy']).toContain(response.body.status);\r\n      expect(['connected', 'disconnected', 'connecting']).toContain(response.body.redis.connection);\r\n    });\r\n\r\n    it('should check external API health', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/health/external-apis')\r\n        .expect(200);\r\n\r\n      expect(response.body).toEqual({\r\n        status: expect.any(String),\r\n        timestamp: expect.any(String),\r\n        apis: {\r\n          threat_intelligence: {\r\n            status: expect.any(String),\r\n            responseTime: expect.any(Number),\r\n            lastChecked: expect.any(String),\r\n            errorRate: expect.any(Number),\r\n          },\r\n          vulnerability_scanner: {\r\n            status: expect.any(String),\r\n            responseTime: expect.any(Number),\r\n            lastChecked: expect.any(String),\r\n            errorRate: expect.any(Number),\r\n          },\r\n          ai_service: {\r\n            status: expect.any(String),\r\n            responseTime: expect.any(Number),\r\n            lastChecked: expect.any(String),\r\n            errorRate: expect.any(Number),\r\n          },\r\n        },\r\n      });\r\n\r\n      Object.values(response.body.apis).forEach((api: any) => {\r\n        expect(['healthy', 'degraded', 'unhealthy']).toContain(api.status);\r\n        expect(api.responseTime).toBeGreaterThanOrEqual(0);\r\n        expect(api.errorRate).toBeGreaterThanOrEqual(0);\r\n        expect(api.errorRate).toBeLessThanOrEqual(100);\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Health Check Aggregation', () => {\r\n    it('should aggregate all health checks', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/health/aggregate')\r\n        .expect(200);\r\n\r\n      expect(response.body).toEqual({\r\n        status: expect.any(String),\r\n        timestamp: expect.any(String),\r\n        overall: {\r\n          healthy: expect.any(Number),\r\n          degraded: expect.any(Number),\r\n          unhealthy: expect.any(Number),\r\n          total: expect.any(Number),\r\n        },\r\n        components: {\r\n          application: expect.any(String),\r\n          database: expect.any(String),\r\n          redis: expect.any(String),\r\n          external_apis: expect.any(String),\r\n          system: expect.any(String),\r\n        },\r\n        summary: expect.any(String),\r\n      });\r\n\r\n      expect(['healthy', 'degraded', 'unhealthy']).toContain(response.body.status);\r\n      expect(response.body.overall.total).toBe(\r\n        response.body.overall.healthy + \r\n        response.body.overall.degraded + \r\n        response.body.overall.unhealthy\r\n      );\r\n    });\r\n\r\n    it('should determine overall status correctly', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/health/aggregate')\r\n        .expect(200);\r\n\r\n      // If any component is unhealthy, overall should be unhealthy\r\n      // If any component is degraded (and none unhealthy), overall should be degraded\r\n      // If all components are healthy, overall should be healthy\r\n      \r\n      const componentStatuses = Object.values(response.body.components);\r\n      const hasUnhealthy = componentStatuses.includes('unhealthy');\r\n      const hasDegraded = componentStatuses.includes('degraded');\r\n\r\n      if (hasUnhealthy) {\r\n        expect(response.body.status).toBe('unhealthy');\r\n      } else if (hasDegraded) {\r\n        expect(response.body.status).toBe('degraded');\r\n      } else {\r\n        expect(response.body.status).toBe('healthy');\r\n      }\r\n    });\r\n  });\r\n\r\n  describe('Health Check Caching', () => {\r\n    it('should cache health check results appropriately', async () => {\r\n      // Make multiple requests quickly\r\n      const responses = await Promise.all([\r\n        request(app.getHttpServer()).get('/health/detailed'),\r\n        request(app.getHttpServer()).get('/health/detailed'),\r\n        request(app.getHttpServer()).get('/health/detailed'),\r\n      ]);\r\n\r\n      responses.forEach(response => {\r\n        expect(response.status).toBe(200);\r\n      });\r\n\r\n      // Timestamps should be very close (indicating caching)\r\n      const timestamps = responses.map(r => new Date(r.body.timestamp).getTime());\r\n      const maxDiff = Math.max(...timestamps) - Math.min(...timestamps);\r\n      expect(maxDiff).toBeLessThan(1000); // Within 1 second\r\n    });\r\n  });\r\n\r\n  describe('Error Scenarios', () => {\r\n    it('should handle health check timeouts gracefully', async () => {\r\n      // This would require mocking slow dependencies\r\n      const response = await request(app.getHttpServer())\r\n        .get('/health/dependencies')\r\n        .timeout(5000);\r\n\r\n      expect([200, 503]).toContain(response.status);\r\n    });\r\n\r\n    it('should provide meaningful error messages', async () => {\r\n      const response = await request(app.getHttpServer())\r\n        .get('/health/nonexistent')\r\n        .expect(404);\r\n\r\n      expect(response.body.message).toContain('not found');\r\n    });\r\n  });\r\n\r\n  describe('Concurrent Health Checks', () => {\r\n    it('should handle concurrent health check requests', async () => {\r\n      const requests = Array.from({ length: 10 }, () =>\r\n        request(app.getHttpServer()).get('/health')\r\n      );\r\n\r\n      const responses = await Promise.all(requests);\r\n\r\n      responses.forEach(response => {\r\n        expect(response.status).toBe(200);\r\n        expect(response.body.status).toBe('ok');\r\n      });\r\n    });\r\n\r\n    it('should maintain performance under load', async () => {\r\n      const startTime = Date.now();\r\n      \r\n      const requests = Array.from({ length: 20 }, () =>\r\n        request(app.getHttpServer()).get('/health/detailed')\r\n      );\r\n\r\n      await Promise.all(requests);\r\n      \r\n      const endTime = Date.now();\r\n      const duration = endTime - startTime;\r\n\r\n      // Should handle concurrent requests efficiently\r\n      expect(duration).toBeLessThan(3000);\r\n    });\r\n  });\r\n});"], "version": 3}